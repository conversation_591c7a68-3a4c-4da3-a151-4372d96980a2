module solver_data

  use grid_types,            only : grid_type, mass_type!, raw_grid_data_type
  use solution_types,        only : soln_type
  use relax_types,           only : relax_type
  use comprow_types,         only : crow_flow

  implicit none

  private

  public :: grid, hiro_grid, mass
  public :: soln
  public :: crow
  public :: relaxation

  type(relax_type)                                    :: relaxation
  !type(design_type)                                   :: design
  !type(raw_grid_data_type)                            :: raw_grid_data
  type(mass_type),        dimension(:,:), pointer     :: mass
  type(grid_type),        dimension(:),   allocatable :: grid
  type(grid_type),        dimension(:),   pointer     :: hiro_grid
  type(soln_type),        dimension(:),   allocatable :: soln
  type(crow_flow),        dimension(:),   allocatable :: crow

end module solver_data

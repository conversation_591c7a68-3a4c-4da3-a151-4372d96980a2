module turb_3eqn

  use lmpi,           only : lmpi_conditional_stop
  use kinddefs,       only : dp

  use turb_asbm,      only : source_resid_asbm, source_jacob_asbm, bc_wall_asbm
  use turb_kw_lag,    only : source_resid_kw_lag, source_jacob_kw_lag,         &
                             bc_wall_kw_lag

  implicit none

  private

  public :: turb_resid_3eqn
  public :: turb_jacob_3eqn
  public :: bc_3eqn_set_walls

contains

!============================== TURB_RESID_3EQN ==============================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for 3-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid_3eqn ( eqn_set, n_turb, n_tot, n_grd, nnodes0,         &
                               nnodes01, nnodes0_2d, node_pairs_2d, qnode,     &
                               turb, gradx, grady, gradz, x, y, z, slen,       &
                               iflagslen, vol, rhotauij, res )

    use kinddefs,        only : dp
    use info_depr,       only : xmach, re, twod
    use lmpi,            only : lmpi_conditional_stop
    use solution_types,  only : compressible, incompressible
    use turbulence_info, only : turbulence_model_int, asbm_sst, kw_lag

    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    integer,                              intent(in)    :: n_grd
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nnodes0_2d
    integer,  dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d

    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: slen
    integer,  dimension(nnodes01),        intent(in)    :: iflagslen
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(6,nnodes01),      intent(out)   :: rhotauij
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    integer  :: node_src_eval
    integer  :: ierr
    real(dp) :: xmr
    real(dp) :: my_xmach

    real(dp), parameter :: zero = 0.0_dp

  continue

    ierr = 0

    my_xmach = zero

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1.0_dp
    case default
      call lmpi_conditional_stop(1,'turb_resid: only for in/compress pg')
    end select

    xmr   = my_xmach / re

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    select case ( turbulence_model_int )

    case ( asbm_sst )

      call source_resid_asbm( nnodes01, nnodes0_2d, node_src_eval,             &
                              node_pairs_2d, n_tot, n_turb, n_grd, vol, qnode, &
                              turb, gradx, grady, gradz, xmr, rhotauij, res)

    case ( kw_lag )

      call source_resid_kw_lag( eqn_set, nnodes0, nnodes01,                    &
                               turb, qnode, res, slen, gradx, grady,           &
                               gradz, vol, x, y, z,                            &
                               nnodes0_2d, node_pairs_2d, iflagslen, n_turb,   &
                               n_tot, n_grd, rhotauij )

!    case ( 'some other 3 eqn model' )

!      call source_resid_other_3eqn_model

!    case ( 'some other 3 eqn model' )

!      call source_resid_other_3eqn_model

    case default

      ierr = 1

    end select

    call lmpi_conditional_stop(ierr,                                           &
         'turbulence_model invalid:turb_resid_3eqn')

  end subroutine turb_resid_3eqn

!============================== TURB_JACOB_3EQN ==============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for 4-eqn models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob_3eqn ( eqn_set, n_turb, n_tot, n_grd, nnodes0,         &
                               nnodes01, nnodes0_2d, node_pairs_2d, g2m,       &
                               qnode, turb, gradx, grady, gradz, vol,          &
                               iflagslen, rhotauij, a_diag )

    use kinddefs,      only : dp
    use info_depr,     only : xmach, re, twod
    use solution_types,only : compressible, incompressible
    use turbulence_info, only : turbulence_model_int, asbm_sst, kw_lag

    integer,                           intent(in) :: eqn_set
    integer,                           intent(in) :: n_turb
    integer,                           intent(in) :: n_tot
    integer,                           intent(in) :: n_grd
    integer,                           intent(in) :: nnodes0
    integer,                           intent(in) :: nnodes01
    integer,                           intent(in) :: nnodes0_2d
    integer,  dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d
    integer, dimension(:),             intent(in) :: g2m

    real(dp), dimension(n_tot,nnodes01),         intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01),        intent(inout) :: turb
    real(dp), dimension(n_grd,nnodes01),         intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),         intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),         intent(in)    :: gradz
    real(dp), dimension(nnodes01),               intent(in)    :: vol
    integer,  dimension(nnodes01),               intent(in)    :: iflagslen
    real(dp), dimension(6,nnodes01),             intent(in)    :: rhotauij
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag

    integer  :: node_src_eval
    integer  :: ierr
    real(dp) :: xmr
    real(dp) :: my_xmach

  continue

    ierr = 0

    my_xmach = 0.0_dp
    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1.0_dp
    case default
      call lmpi_conditional_stop(1,'turb_jacob_3eqn: only for in/compress pg')
    end select
    xmr   = my_xmach / re

    node_src_eval  = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    select case ( turbulence_model_int )

    case ( asbm_sst )

      call source_jacob_asbm( nnodes01, nnodes0, nnodes0_2d, node_src_eval,    &
                              node_pairs_2d, n_tot, n_turb, n_grd, g2m, vol,   &
                              qnode, turb, gradx, grady, gradz, xmr, rhotauij, &
                              a_diag)

    case ( kw_lag )

      call source_jacob_kw_lag( nnodes01, nnodes0, nnodes0_2d, node_src_eval,  &
                                node_pairs_2d, n_tot, n_turb, g2m, vol,        &
                                qnode, turb, iflagslen, a_diag )

!      call source_jacob_other_3eqn_model

!    case ( 'some other 3 eqn model' )

!      call source_jacob_other_3eqn_model

!    case ( 'some other 3 eqn model' )

!      call source_jacob_other_3eqn_model

    case default

      ierr = 1

    end select

    call lmpi_conditional_stop(ierr,                                           &
         'turbulence_model invalid:turb_jacob_3eqn')

  end subroutine turb_jacob_3eqn

!============================ BC_3EQN_SET_WALLS ==============================80
!
!  Sets quantities on viscous walls for 3 equation models
!
!=============================================================================80

  subroutine bc_3eqn_set_walls( eqn_set, nnodes0, nnodes01, nbnode, ibnode,    &
                                slen_wall, turb, qnode, n_turb, n_tot,         &
                                ibc, k_wf, omega_wf, mu_t_wf )

    use solution_types,  only : compressible
    use turbulence_info, only : turbulence_model_int, asbm_sst, kw_lag

    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nbnode
    integer,  dimension(nbnode),          intent(in)    :: ibnode
    real(dp), dimension(nbnode),          intent(in)    :: slen_wall
    integer,                              intent(in)    :: ibc
    real(dp), dimension(nbnode),          intent(in)    :: k_wf
    real(dp), dimension(nbnode),          intent(in)    :: omega_wf
    real(dp), dimension(nbnode),          intent(in)    :: mu_t_wf
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode

    integer :: ierr

    continue

    ierr = 0

    if ( eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'bc_3eqn_set_walls: only comprss pg')
    end if

    select case (turbulence_model_int)

    case ( asbm_sst )

      call bc_wall_asbm( nnodes0, nnodes01, nbnode, ibnode, slen_wall, turb,   &
                          qnode, n_turb, n_tot )

    case ( kw_lag )

      call bc_wall_kw_lag( eqn_set, nnodes0, nnodes01, nbnode, ibnode,        &
                           slen_wall, turb, qnode,                            &
                           n_turb, n_tot, ibc, k_wf, omega_wf, mu_t_wf )

!     call bc_wall_other_3eqn_model()

!   case ( 'some other 3 eqn model')

!     call bc_wall_other_3eqn_model()

!   case ( 'some other 3 eqn model')

!     call bc_wall_other_3eqn_model()

    case default

      ierr = 1

    end select

    call lmpi_conditional_stop(ierr,                                           &
         'turbulence_model invalid:bc_3eqn_set_walls')

  end subroutine bc_3eqn_set_walls

end module turb_3eqn

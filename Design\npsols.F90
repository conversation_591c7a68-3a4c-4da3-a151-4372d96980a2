module npsols

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: npsol_driver, feasibility_tolerance

  character(len=80) :: feasibility_tolerance = '0.005' ! Max absolute
                                                       ! constraint
                                                       ! violation
                                                       ! allowed

#ifdef HAVE_NPSOL
  character(len=80), parameter :: optimality_tolerance = '1.e-4' ! Basically
                                                                 ! the number
                                                                 ! of digits
                                                                 ! agreement
                                                                 ! between
                                                                 ! final and
                                                                 ! optimal objs
#endif

contains

!============================== NPSOL_DRIVER =================================80
!
!  Performs a multipoint optimization using NPSOL
!
!=============================================================================80
  subroutine npsol_driver(max_design_cycles,what_to_do,restart_optimization,   &
                          io,ammo_directory,lss_flag)

    use allocations,       only : my_alloc_ptr
    use kinddefs,          only : dp
    use design_types,      only : opt_data_type
    use designs,           only : load_optimization_data,                      &
                                  free_optimization_data
    use npsol_data,        only : npsol_restart_flow, npsol_restart_dual
    use lmpi,              only : lmpi_die
    use system_extensions, only : se_chdir
    use nml_design,        only : n_design_pts, model_variables

#ifdef HAVE_NPSOL
    use string_utils,       only : sprintf
    use designs,            only : unload_optimization_data
    use string_utils,       only : int_to_s
#endif

    integer, intent(in) :: max_design_cycles, what_to_do, io

    logical, intent(in) :: lss_flag
    logical, intent(in) :: restart_optimization

    character(len=*), intent(in) :: ammo_directory

    integer :: i,j               ! Loop indices
    integer :: n                 ! Number of design variables
    integer :: nclin             ! Number of linear constraints
    integer :: ncnln             ! Number of nonlinear constraints
    integer :: nctotl            ! Sum of ndv, nclin, ncnln
    integer :: lda               ! row dimension of the array a
    integer :: ldj               ! row dimension of the array cjac
    integer :: ldr               ! row dimension of the array r
    integer :: leniw             ! Integer workspace size for NPSOL
    integer :: lenw              ! Real workspace size for NPSOL
    integer :: inform            ! Output flag from NPSOL
    integer :: counter           ! Indexing variable

    integer, parameter :: print_file = 57
    integer, parameter :: sum_file   = 58

    integer, dimension(:), pointer :: istate, iw

    real(dp), dimension(:),   pointer :: bl, bu, clamda, c, g, x, w
    real(dp), dimension(:,:), pointer :: a, cjac, r

    type(opt_data_type), dimension(n_design_pts) :: opt_data

#ifdef HAVE_NPSOL
    integer :: iter              ! Number of NPSOL iterations performed
    real(dp)    :: f
    character(len=80) :: line
    external :: funcon, funobj
#endif

  continue

! Open the NPSOL output files

    open(print_file,file='npsol.printfile')
    open(sum_file,file='npsol.summaryfile')

! Load the optimization data for each model

    do i = 1, n_design_pts
      call se_chdir(model_variables(i)%model_directory)
      opt_data(i)%allocated = .false.
      call load_optimization_data(opt_data(i),io,'NPSOL: Location 1')
      opt_data(i)%scale = 1.0_dp
    end do

! Load the design variable info into the NPSOL-style array
! using the values from the first model.  We assume the
! parameterization, bounds, active DV's, etc are the same
! across all models

    n      = opt_data(1)%ndv
    nclin  = 0

    ncnln = 0
    do i = 1, n_design_pts
      ncnln = ncnln + opt_data(i)%nconstraints
    end do

    lda    = max(1,nclin)
    ldj    = max(1,ncnln)
    ldr    = n
    nctotl = n + nclin + ncnln
    leniw  = 3*n + nclin + 2*ncnln
    lenw   = 2*n*n + n*nclin + 2*n*ncnln + 20*n + 11*nclin + 21*ncnln

    call my_alloc_ptr(a,      lda,1)
    call my_alloc_ptr(bl,     nctotl)
    call my_alloc_ptr(bu,     nctotl)
    call my_alloc_ptr(istate, nctotl)
    call my_alloc_ptr(cjac,   ldj, n)
    call my_alloc_ptr(clamda, nctotl)
    call my_alloc_ptr(r,      ldr, n)
    call my_alloc_ptr(x,      n)
    call my_alloc_ptr(iw,     leniw)
    call my_alloc_ptr(w,      lenw)
    call my_alloc_ptr(c,      max(1,ncnln))
    call my_alloc_ptr(g,      n)

! Set the design variables

    x = opt_data(1)%design_variables

! Set the upper and lower bounds on the design variables

    do i = 1, n
      bl(i) = opt_data(1)%lower_bounds(i)
      bu(i) = opt_data(1)%upper_bounds(i)
    end do

! Set the upper and lower bounds on the constraints

    counter = 0
    do i = 1, n_design_pts
      if ( opt_data(i)%nconstraints > 0 ) then
        do j = 1, opt_data(i)%nconstraints
          counter = counter + 1
          bl(n+counter) = opt_data(i)%constraint_lower_bound(j)
          bu(n+counter) = opt_data(i)%constraint_upper_bound(j)
        end do
      endif
    end do

! Free memory

    do i = 1, n_design_pts
      call free_optimization_data(opt_data(i))
    end do

! Initialize and set some data in the npsol data module since we can't
! pass it in through argument lists

    call initialize_npsol_data(n,what_to_do,restart_optimization,io,           &
                               ammo_directory,lss_flag)

    call establish_scaling(n,bl,bu,x)

! Set some defaults for the NPSOL package

#ifdef HAVE_NPSOL
    call npoptn( 'Derivative level = 3' )

    line = sprintf('Major iteration limit = %i0',max_design_cycles)
    call npoptn( line )
    write(io,*) line

    line = 'Feasibility tolerance = ' // trim(feasibility_tolerance)
    call npoptn( line ) ! FIXME: use sprintf %*
    write(io,*) line

    line = 'Optimality tolerance = ' // trim(optimality_tolerance)
    call npoptn( line ) ! FIXME: use sprintf %*
    write(io,*) line

    line = sprintf('Major print level = %i0',20)
    call npoptn( line )
    write(io,*) line

    line = sprintf('Minor print level = %i0',20)
    call npoptn( line )
    write(io,*) line

    line = sprintf('Print file = %i0',print_file)
    call npoptn( line )
    write(io,*) line

    line = sprintf('Summary file = %i0',sum_file)
    call npoptn( line )
    write(io,*) line

    call npoptn( 'Verify No' )

    write(io,*) 'heading into npsol: bl, bu...'
    do i = 1, size(bl,1)
      write(io,*) bl(i), bu(i)
    end do

    call npsol(n, nclin, ncnln, lda, ldj, ldr, a, bl, bu, funcon, funobj,      &
               inform, iter, istate, c, cjac, clamda, f, g, r, x, iw, leniw, w,&
               lenw)
#else
    inform = max_design_cycles ! Try to quiet the g95 compiler
    write(*,*) 'You do not have NPSOL installed.'
    call lmpi_die
#endif

! Set some data from the npsol data module that NPSOL may have set

    do i = 1, n_design_pts
      model_variables(i)%restart_flow = npsol_restart_flow(i)
      model_variables(i)%restart_dual = npsol_restart_dual(i)
    end do

    call interpret_npsol_output(inform)

    deallocate(a,bl,bu,istate,cjac,clamda,r,iw,w,c,g,x)

! Close the NPSOL output files

    close(print_file)
    close(sum_file)

  end subroutine npsol_driver


!============================== INTERPRET_NPSOL_OUTPUT =======================80
!
!  Interprets the flag returned from NPSOL after an optimization
!
!=============================================================================80

  subroutine interpret_npsol_output(npsol_flag)

    use system_extensions, only : se_flush

    integer, intent(in) :: npsol_flag

  continue

    write(*,*) 'NPSOL has returned the following flag: ',npsol_flag

    select case ( npsol_flag )
    case (:-1)
      write(*,*) 'Either funcon or funobj has set mode to this negative value'
      write(*,*) 'See Section 4.'
    case (0)
      write(*,*) 'The iteratives have converged to a point x that satisfies'
      write(*,*) 'the optimality conditions to the accuracy requested by the'
      write(*,*) 'Linear feasibility tolerance, the Nonlinear feasibility'
      write(*,*) 'tolerance, and the Optimality tolerance. That is, the active'
      write(*,*) 'constraint residuals and the reduced gradient are negligible'
      write(*,*) 'at x.'
    case (1)
      write(*,*) 'The final iterate x satisfies the optimality conditions to'
      write(*,*) 'the accuracy requested, but the sequence of iterates has not'
      write(*,*) 'yet converged. NPSOL was terminated because no further'
      write(*,*) 'improvement could be made in the merit function.'
    case (2)
      write(*,*) 'The linear constraints and bounds could not be satisfied.'
      write(*,*) 'The problem has no feasible solution.'
    case (3)
      write(*,*) 'The nonlinear constraints could not be satisfied. The'
      write(*,*) 'problem may have no feasible solution.'
    case (4)
      write(*,*) 'The Major iteration limit was reached.'
    case (6)
      write(*,*) 'x does not satisfy the first-order optimality conditions'
      write(*,*) 'to the required accuracy, and no improved point for the'
      write(*,*) 'merit function could be found during the final linesearch.'
    case (7)
      write(*,*) 'The function derivatives returned by funcon or funobj'
      write(*,*) 'appear to be incorrect.'
    case (9)
      write(*,*) 'An input parameter was invalid.'
    case default
      write(*,*) 'Nothing seems to be known about this NPSOL flag...'
    end select

    call se_flush(6)

  end subroutine interpret_npsol_output


!============================== INITIALIZE_NPSOL_DATA ========================80
!
!  Initializes some data in the NPSOL data module
!
!=============================================================================80
  subroutine initialize_npsol_data(n,what_to_do,restart_optimization,io,       &
                                   ammo_directory,lss_flag)

    use npsol_data,   only : npsol_restart_flow, npsol_restart_dual,           &
                             last_flow_dvs, last_adjoint_dvs, npsol_lss_flag,  &
                             npsol_what_to_do, npsol_io, npsol_ammo_directory, &
                             npsol_restart_optimization, npsol_desc_directory
    use allocations,  only : my_alloc_ptr
    use kinddefs,     only : dp
    use nml_design,   only : model_variables, n_design_pts
    use design_types, only : max_string_length

    integer, intent(in) :: n, what_to_do, io

    logical, intent(in) :: lss_flag, restart_optimization

    character(len=*), intent(in) :: ammo_directory

    integer :: i

  continue

    call my_alloc_ptr(npsol_restart_flow, n_design_pts)
    call my_alloc_ptr(npsol_restart_dual, n_design_pts)
    call my_alloc_ptr(npsol_desc_directory, max_string_length, n_design_pts)

    do i = 1, n_design_pts
      npsol_restart_flow(i)   = model_variables(i)%restart_flow
      npsol_restart_dual(i)   = model_variables(i)%restart_dual
      npsol_desc_directory(i) = model_variables(i)%desc_directory
    end do

    npsol_ammo_directory       = ammo_directory
    npsol_what_to_do           = what_to_do
    npsol_restart_optimization = restart_optimization
    npsol_io                   = io
    npsol_lss_flag             = lss_flag

    call my_alloc_ptr(last_flow_dvs,    n_design_pts, n)
    call my_alloc_ptr(last_adjoint_dvs, n_design_pts, n)

! Allocate routine sets things to zero, so we ought
! to set it to something crazy, since this could
! very well be our starting design point

    last_flow_dvs    = huge(1.0_dp)
    last_adjoint_dvs = huge(1.0_dp)

  end subroutine initialize_npsol_data

!============================== ESTABLISH_SCALING ============================80
!
!  Establishes scaling coefficients for NPSOL and also scales the dvs and bounds
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine establish_scaling(n,bl,bu,x)

    use allocations, only : my_alloc_ptr
    use npsol_data,  only : c, d
    use kinddefs,    only : dp

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x
    real(dp), dimension(:), intent(inout) :: bl, bu

  continue

    call my_alloc_ptr(c,n)
    call my_alloc_ptr(d,n)

    c(1:n) = 0.5_dp*(bl(1:n)+bu(1:n))
    d(1:n) = 0.5_dp*(bu(1:n)-bl(1:n))

! scale the bounds

    bl(1:n) = (bl(1:n)-c(1:n)) / d(1:n)
    bu(1:n) = (bu(1:n)-c(1:n)) / d(1:n)

! scale the dvs

    x(1:n) = (x(1:n)-c(1:n)) / d(1:n)

  end subroutine establish_scaling


end module npsols


!============================== FUNOBJ =======================================80
!
!  Provides the external interface to the function and gradients for NPSOL
!
!  Be very careful to see if we have already been to this design point
!  with the flow solver and/or the adjoint solver
!
!  Before doing an adjoint, make sure the flow solution is at the correct
!  design point
!
!=============================================================================80
subroutine funobj( mode, n, x, f, g, nstate )

  use kinddefs,          only : dp
  use npsol_data,        only : last_flow_dvs, last_adjoint_dvs,               &
                                npsol_restart_flow, npsol_io,                  &
                                npsol_restart_dual, npsol_lss_flag,            &
                                npsol_what_to_do, npsol_restart_optimization,  &
                                npsol_desc_directory, npsol_ammo_directory
  use design_types,      only : opt_data_type
  use designs,           only : load_optimization_data, free_optimization_data,&
                                unload_optimization_data, been_there
  use analysis,          only : perform_analysis
  use sensitivity,       only : perform_sensitivity_analysis
  use system_extensions, only : se_chdir
  use utilities,         only : check_for_stop
  use file_utils,        only : rm
  use nml_design,        only : n_design_pts, model_variables

  integer, intent(inout) :: mode
  integer, intent(in)    :: n, nstate

  real(dp), intent(out) :: f

  real(dp), dimension(n), intent(inout)  :: x
  real(dp), dimension(n), intent(inout)  :: g

  integer :: i, istop

  type(opt_data_type), dimension(n_design_pts) :: opt_data

continue

  write(*,*) "npsol:funobj Unused arguments mode nstate" !FIXME unused
  if (.false.) mode = nstate !FIXME unused

! Stop if user requested us to

  call se_chdir(npsol_ammo_directory)
  call check_for_stop(istop)
  if ( istop > 0 ) then
    call rm('stop.dat')
    write(*,*) 'User requested premature stop...'
    stop
  endif

! Unscale the problem

  call unscale_obj(n,x,g)

! Update rubber.data info

  do i = 1, n_design_pts
    call se_chdir(model_variables(i)%model_directory)
    opt_data(i)%allocated = .false.
    call load_optimization_data(opt_data(i),npsol_io,'NPSOL: Location 2')
    opt_data(i)%design_variables = x
    call unload_optimization_data(opt_data(i),npsol_io,'Top of FUNOBJ')
  end do

! Compute objective function if we haven't already done it at this x

  find_obj1 : do i = 1, n_design_pts
    if ( opt_data(i)%nobjectives > 0 ) then
      if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
        call se_chdir(model_variables(i)%model_directory)
        call perform_analysis(npsol_restart_flow(i),                           &
                              i, n_design_pts, npsol_what_to_do,               &
                              npsol_restart_optimization,                      &
                              npsol_desc_directory(i))
        call load_optimization_data(opt_data(i),npsol_io,'NPSOL: Location 3')
        last_flow_dvs(i,:) = x
      endif
      exit find_obj1
    endif
  end do find_obj1

! Set the objective function value heading back into NPSOL

  find_obj2 : do i = 1, n_design_pts
    if ( opt_data(i)%nobjectives > 0 ) then
      f = opt_data(i)%objectives(1)
      exit find_obj2
    endif
  end do find_obj2

! Compute gradient of objective function

  find_obj3 : do i = 1, n_design_pts

    if ( opt_data(i)%nobjectives > 0 ) then

      if ( .not. been_there(n,x,last_adjoint_dvs(i,:),.false.) ) then

        call se_chdir(model_variables(i)%model_directory)

        if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
          call perform_analysis(npsol_restart_flow(i),                         &
                                i, n_design_pts, npsol_what_to_do,             &
                                npsol_restart_optimization,                    &
                                npsol_desc_directory(i))
          last_flow_dvs(i,:) = x
        endif

        call perform_sensitivity_analysis(npsol_restart_dual(i),               &
                                          i,n_design_pts,npsol_what_to_do,     &
                                          npsol_lss_flag)

        call load_optimization_data(opt_data(i),npsol_io,'NPSOL: Location 4')
        last_adjoint_dvs(i,:) = x

        exit find_obj3

      endif

    endif

  end do find_obj3


! Set the gradient values heading back into NPSOL

  find_obj4 : do i = 1, n_design_pts
    if ( opt_data(i)%nobjectives > 0 ) then
      g = opt_data(i)%gradients(1,:)
      exit find_obj4
    endif
  end do find_obj4

! Free memory

  do i = 1, n_design_pts
    call free_optimization_data(opt_data(i))
  end do

! Scale the problem

  call scale_obj(n,x,g)

end subroutine funobj


!============================== FUNCON =======================================80
!
!  Provides the external interface to the constraints and gradients for NPSOL
!
!  Be very careful to see if we have already been to this design point
!  with the flow solver and/or the adjoint solver
!
!  Before doing an adjoint, make sure the flow solution is at the correct
!  design point
!
!=============================================================================80
subroutine funcon( mode, ncnln, n, ldj, needc, x, c, cjac, nstate )

  use kinddefs,           only : dp
  use npsol_data,         only : last_flow_dvs, last_adjoint_dvs,              &
                                 npsol_restart_flow, npsol_restart_dual,       &
                                 npsol_io, npsol_ammo_directory,               &
                                 npsol_what_to_do, npsol_restart_optimization, &
                                 npsol_desc_directory, npsol_lss_flag
  use design_types,       only : opt_data_type
  use designs,            only : load_optimization_data, been_there,           &
                                 unload_optimization_data,                     &
                                 free_optimization_data
  use analysis,           only : perform_analysis
  use sensitivity,        only : perform_sensitivity_analysis
  use system_extensions,  only : se_chdir
  use utilities,          only : check_for_stop
  use file_utils,         only : rm
  use nml_design,         only : n_design_pts, model_variables

  integer,                    intent(in)    :: ncnln, n, ldj, nstate
  integer,                    intent(inout) :: mode
  integer,  dimension(:),     intent(in)    :: needc
  real(dp), dimension(n),     intent(inout) :: x
  real(dp), dimension(ncnln), intent(out)   :: c
  real(dp), dimension(ldj,*), intent(out)   :: cjac

  integer :: i, j, k, counter, istop

  type(opt_data_type), dimension(n_design_pts) :: opt_data

continue

  write(*,*) "npsol:funcon Unused arguments ncnln nstate needc mode"
  if (.false.) write(*,*) mode, nstate, ncnln, needc ! avoid compiler warnings

! Stop if user requested us to

  call se_chdir(npsol_ammo_directory)
  call check_for_stop(istop)
  if ( istop > 0 ) then
    call rm('stop.dat')
    write(*,*) 'User requested premature stop...'
    stop
  endif

! Unscale the problem

  call unscale_con(n,ncnln,ldj,x,cjac)

! Update rubber.data info

  do i = 1, n_design_pts
    call se_chdir(model_variables(i)%model_directory)
    opt_data(i)%allocated = .false.
    call load_optimization_data(opt_data(i),npsol_io,'NPSOL: Location 5')
    opt_data(i)%design_variables = x
    call unload_optimization_data(opt_data(i),npsol_io,'Top of FUNCON')
  end do

! Compute constraint if we haven't already done it at this x

  find_con1 : do i = 1, n_design_pts
    if ( opt_data(i)%nconstraints > 0 ) then
      if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
        call se_chdir(model_variables(i)%model_directory)
        call perform_analysis(npsol_restart_flow(i),                           &
                              i, n_design_pts, npsol_what_to_do,               &
                              npsol_restart_optimization,                      &
                              npsol_desc_directory(i))
        call load_optimization_data(opt_data(i),npsol_io,'NPSOL: Location 6')
        last_flow_dvs(i,:) = x
      endif
    endif
  end do find_con1

! Set the constraint value heading back into NPSOL

  counter = 0
  find_con2 : do i = 1, n_design_pts
    if ( opt_data(i)%nconstraints > 0 ) then
      do j = 1, opt_data(i)%nconstraints
        counter = counter + 1
        c(counter) = opt_data(i)%constraints(j)
      end do
    endif
  end do find_con2

! Compute gradient of constraint if we haven't already done it at this x

  find_con3 : do i = 1, n_design_pts

    if ( opt_data(i)%nconstraints > 0 ) then

      if ( .not. been_there(n,x,last_adjoint_dvs(i,:),.false.) ) then

        call se_chdir(model_variables(i)%model_directory)

        if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
          call perform_analysis(npsol_restart_flow(i),                         &
                                i, n_design_pts, npsol_what_to_do,             &
                                npsol_restart_optimization,                    &
                                npsol_desc_directory(i))
          last_flow_dvs(i,:) = x
        endif

        call perform_sensitivity_analysis(npsol_restart_dual(i),               &
                                          i,n_design_pts,npsol_what_to_do,     &
                                          npsol_lss_flag)

        call load_optimization_data(opt_data(i),npsol_io,'NPSOL: Location 7')
        last_adjoint_dvs(i,:) = x

      endif

    endif

  end do find_con3

! Set the gradient values heading back into NPSOL

  counter = 0
  find_con4 : do i = 1, n_design_pts
    if ( opt_data(i)%nconstraints > 0 ) then
      do j = 1, opt_data(i)%nconstraints
        counter = counter + 1
        do k = 1, n
          cjac(counter,k) = opt_data(i)%constraint_gradients(j,k)
        end do
      end do
    endif
  end do find_con4

! Free memory

  do i = 1, n_design_pts
    call free_optimization_data(opt_data(i))
  end do

! Scale the problem

  call scale_con(n,ncnln,ldj,x,cjac)

end subroutine funcon

!============================== UNSCALE_OBJ ==================================80
!
!  Unscales objective stuff for NPSOL
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_obj(n,x,g)

    use npsol_data, only : c, d
    use kinddefs,   only : dp

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x, g

  continue

! unscale the DV's

    x(1:n) = d(1:n)*x(1:n) + c(1:n)

! unscale the gradient

    g(1:n) = g(1:n)/d(1:n)

  end subroutine unscale_obj


!============================== SCALE_OBJ ====================================80
!
!  Scales objective stuff for NPSOL
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_obj(n,x,g)

    use npsol_data, only : c, d
    use kinddefs,   only : dp

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x, g

  continue

! scale the DV's

    x(1:n) = (x(1:n)-c(1:n)) / d(1:n)

! scale the gradient

    g(1:n) = d(1:n)*g(1:n)

  end subroutine scale_obj


!============================== UNSCALE_CON ==================================80
!
!  Unscales constraint stuff for NPSOL
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_con(n,ncnln,ldj,x,cjac)

    use npsol_data, only : c, d
    use kinddefs,   only : dp

    integer, intent(in) :: n, ncnln, ldj

    real(dp), dimension(n),     intent(inout) :: x
    real(dp), dimension(ldj,*), intent(inout) :: cjac

    integer :: i

  continue

! unscale the DV's

    x(1:n) = d(1:n)*x(1:n) + c(1:n)

! unscale the constraint gradients

    do i = 1, ncnln
      cjac(i,1:n) = cjac(i,1:n)/d(1:n)
    end do

  end subroutine unscale_con


!============================== SCALE_CON ====================================80
!
!  Scales constraint stuff for NPSOL
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_con(n,ncnln,ldj,x,cjac)

    use npsol_data, only : c, d
    use kinddefs,   only : dp

    integer, intent(in) :: n, ncnln, ldj

    real(dp), dimension(n),     intent(inout) :: x
    real(dp), dimension(ldj,*), intent(inout) :: cjac

    integer :: i

  continue

! scale the DV's

    x(1:n) = (x(1:n)-c(1:n)) / d(1:n)

! scale the constraint gradients

    do i = 1, ncnln
      cjac(i,1:n) = d(1:n)*cjac(i,1:n)
    end do

  end subroutine scale_con

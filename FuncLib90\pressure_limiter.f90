!=============================== PRESSURE_LIMITER ============================80
!
! Pressure based limiter function with a correction for cell Reynolds number
!
!=============================================================================80

  pure function pressure_limiter(x1, y1, z1, x2, y2, z2,                       &
                                 xn, yn, zn, ra, vol1, vol2, q1, q2,           &
                                 gradx1, grady1, gradz1,                       &
                                 gradx2, grady2, gradz2,                       &
                                 ndim,  eqn_set,                               &
                                 mu, gm1, viscous_terms, powerv,               &
                                 shft, gradcc, laplcc, power, ivisc_option,    &
                                 c_gen)

    use kinddefs,        only : dp
    use generic_gas_map, only : n_momx, n_momy, n_momz, n_species, n_etot
    use solution_types,  only : generic_gas

    real(dp)                              :: pressure_limiter

    integer,                   intent(in) :: powerv, ivisc_option
    integer,                   intent(in) :: ndim, eqn_set
    real(dp),                  intent(in) :: x1, y1, z1, x2, y2, z2
    real(dp),                  intent(in) :: xn, yn, zn, ra
    real(dp),                  intent(in) :: vol1, vol2
    real(dp),                  intent(in) :: c_gen
    real(dp),                  intent(in) :: gradx1, grady1, gradz1
    real(dp),                  intent(in) :: gradx2, grady2, gradz2
    real(dp),                  intent(in) :: shft, gradcc, laplcc, power
    real(dp),                  intent(in) :: gm1, mu
    real(dp), dimension(ndim), intent(in) :: q1, q2
    character(len=20),         intent(in) :: viscous_terms

    real(dp) :: rho1, u1, v1, w1, press1, q21, enrgy1, H1, ubar1
    real(dp) :: rho2, u2, v2, w2, press2, q22, enrgy2, H2, ubar2
    real(dp) :: rho, wat, u, v, w, qsqd, h, c, vcoef, ubar

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

!   Compute the normalized undivided pressure ratio and
!   pressure Laplacian based limiter

    pressure_limiter = pswitch(x1, y1, z1, x2, y2, z2, q1(n_etot), q2(n_etot), &
                               gradx1, grady1, gradz1, gradx2, grady2 ,gradz2, &
                               shft, gradcc, laplcc, power, 'flux')

!   Compute the cell face reynolds number to make the pressure switch coeff.
!   one on low Reynolds number cells faces

    if (viscous_terms /= 'inviscid' ) then

!     Get left and right cell primitive variables

      rho1   = sum(q1(1:n_species))
      u1     = q1(n_momx)
      v1     = q1(n_momy)
      w1     = q1(n_momz)
      ubar1  = xn*u1 + yn*v1 + zn*w1
      q21    = u1*u1 + v1*v1 + w1*w1
      press1 = q1(n_etot) ! use n_etot when q contains primitive variables

      rho2   = sum(q2(1:n_species))
      u2     = q2(n_momx)
      v2     = q2(n_momy)
      w2     = q2(n_momz)
      ubar2  = xn*u2 + yn*v2 + zn*w2
      q22    = u2*u2 + v2*v2 + w2*w2
      press2 = q2(n_etot)

      rho  = sqrt(rho1*rho2)
      wat  = rho/(rho + rho2)
      u    = u1*wat + u2*(my_1 - wat)
      v    = v1*wat + v2*(my_1 - wat)
      w    = w1*wat + w2*(my_1 - wat)
      ubar = xn*u + yn*v + zn*w

      if ( eqn_set == generic_gas ) then

!       Assume switching function does not require Roe averaged sound speed
        c     = c_gen
        if (ivisc_option == 0) then
          vcoef = vswch_coef_orig(rho1, rho2, q21, q22, c, vol1, vol2, ra,     &
                                  powerv, mu)
        else
          vcoef = vswch_coef(wat, rho, q21, ubar1, q22, ubar2, ubar, c,        &
                             vol1, vol2, ra, powerv, mu)
        end if

      else

        enrgy1 = press1/gm1 + my_half*rho1*q21
        H1     = (enrgy1 + press1)/rho1

        enrgy2 = press2/gm1 + my_half*rho2*q22
        H2     = (enrgy2 + press2)/rho2

        qsqd = u*u + v*v + w*w
        h    = H1*wat + H2*(my_1 - wat)
        c    = sqrt(gm1*(h - my_half*qsqd))

        if (ivisc_option == 0) then
          vcoef = vswch_coef_orig(rho1, rho2, q21, q22, c, vol1, vol2, ra,     &
                                  powerv, mu)
        else
          vcoef = vswch_coef(wat, rho, q21, ubar1, q22, ubar2, ubar, c,        &
                             vol1, vol2, ra, powerv, mu)
        end if
      end if

      pressure_limiter = max(pressure_limiter, vcoef)

    end if

  end function pressure_limiter

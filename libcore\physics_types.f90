module physics_types

  use kinddefs, only : dp

  implicit none

  private

  public :: spec_prop, max_ranges, kin_3, kin_2, kin_1
  public :: fun3d_and_char, thermal_response_type, fun3d_char_restart
  public :: fun3d_to_char_max, start_coupling_iter
  public :: fun3d_to_char_freq
  public :: is_surface_ablated
  public :: ablation_recession, ablation_recession_freq, start_recession

  integer, parameter :: max_ranges = 5 ! maximum number of temperature ranges

  integer       :: fun3d_to_char_freq
  integer       :: fun3d_to_char_max, start_coupling_iter
  logical       :: fun3d_and_char = .false.
  logical       :: fun3d_char_restart = .false.
  logical       :: is_surface_ablated = .false.
  character(80) :: thermal_response_type !conduction or ablation

  ! The following three flags only apply to
  ! the internal coupled ablation,
  ! and do not affect Fun3D-CHAR.
  logical       :: ablation_recession = .false.
  integer       :: ablation_recession_freq = 3000
  integer       :: start_recession = 0

  type spec_prop

    real(dp) :: mol_wt         ! molecular weight
    real(dp) :: cprt0          ! non-dimensional rot.-tranl. heat capacity
    real(dp) :: heat_for       ! heat of formation
    real(dp) :: elec_impct_ion ! electron impact ionization energy
    real(dp) :: alantel        ! landau-teller coef for vibr. relaxation
    real(dp) :: disoc_ener     ! dissociation energy
    real(dp) :: gamma          ! this is applicable for perfect gas

    real(dp), dimension(3)             :: siga ! energy exchange curve fit
    real(dp), dimension(10,max_ranges) :: coef
    real(dp), dimension(max_ranges)    :: t_lower
    real(dp), dimension(max_ranges)    :: t_upper

    character(2), dimension(5) :: elements ! assume max 5 elemnts in any species
    integer,      dimension(5) :: number
    integer                    :: n_elements ! number of elements in species

    integer :: charge
    integer :: neutral_map  ! index of neutral species corresponding to ion
    integer :: ion_map      ! index of charged species corresponding to neutral
    integer :: molecule_map ! index of diatom corresponding to atom
    integer :: number_thermo_ranges ! for curve fit coef

    character(24) :: species ! active species

    logical :: molecule
    logical :: ion
    logical :: electron

    real(dp) :: ake, bke, cke, dke, eke ! equil constants
    logical  :: replace ! true if not a basis species

    real(dp), dimension(:), pointer :: factor ! if replace == .true. then
                                              !  moles of reac (-) or prod (+)
                                              ! else
                                              !  fractional contribution to elem
                                              !  conservation

  end type spec_prop

  type kin_3

    real(dp), dimension(:), pointer :: m_r ! real stoichiometric coefficient
    real(dp), dimension(:), pointer :: n2m ! real stoichiometric coefficient
                                           ! times molecular weight of
                                           ! participant species to convert
                                           ! number to mass

    integer :: j_max      ! number of species in reaction
    integer :: t_eff_indx ! index for effective temperature

    integer, dimension(:), pointer :: j   ! index of species in reaction
    integer, dimension(:), pointer :: m_i ! integer stoichiometric coefficient

    logical :: engaged ! true if reaction is engaged

    logical, dimension(:), pointer :: int_exp ! true if integer coefficient

  end type kin_3

  type kin_2

    real(dp) :: reac1 ! real stoichiometric coefficient of reactant
    real(dp) :: prod1 ! real stoichiometric coefficient of product

    integer  :: jr    ! reactant  index
    integer  :: jp    ! product  index
    integer  :: reac0 ! integer stoichiometric coefficient of reactant
    integer  :: prod0 ! integer stoichiometric coefficient of product

    logical  :: rexp0 ! true if integer coefficient for reactant
    logical  :: pexp0 ! true if integer coefficient for product

  end type kin_2

  type kin_1

    real(dp) :: a          !\
    real(dp) :: b          ! > k_f = a*t_eff**b*exp(-c/t_eff)
    real(dp) :: c          !/
    real(dp) :: ake        !\
    real(dp) :: bke        ! \  K_c = exp(ake/tz+bke+log(tz)*cke+dke*tz
    real(dp) :: cke        !  >     + eke*tz**2)
    real(dp) :: dke        ! /  where tz = 10000./t_eff
    real(dp) :: eke        !/
    real(dp) :: exp1       ! t_eff = T**exp1*T_V**exp2
    real(dp) :: exp2       ! if indx_forward/backward_teff = 2
    real(dp) :: t_eff_min1 ! minimum allowed value t_eff
    real(dp) :: t_eff_max1 ! maximum allowed value t_eff
    real(dp) :: t_eff_min2 ! minimum allowed value t_eff
    real(dp) :: t_eff_max2 ! maximum allowed value t_eff
    real(dp) :: rf_max     ! maximum allowed forward reaction rate
    real(dp) :: rf_min     ! minimum allowed forward reaction rate
    real(dp) :: rb_max     ! maximum allowed backward reaction rate
    real(dp) :: rb_min     ! minimum allowed backward reaction rate

    integer  :: j_reac             ! number of reactants
    integer  :: j_prod             ! number of products
    integer  :: indx_forward_teff  !/1(t_eff=T), 2(t_eff=T**exp1*T_V**exp2),
    integer  :: indx_backward_teff !\3(t_eff=T_V) for forward/backward resp.
    integer  :: elec_impact_target ! index of target for electron impact ion.

    logical  :: forward            ! true if forward rate is engaged
    logical  :: backward           ! true if backward rate is engaged
    logical  :: gen_col_ptnr       ! true if generic partner M is present
    logical  :: electron_impact    ! true if electron impact ioniz. reaction

  end type kin_1

end module physics_types


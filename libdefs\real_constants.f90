!
! conversions used at various points throughout the performance modules
!
! A good reference for conversions can be found in the NIST Guide to SI Units,
! Appendix B.8 Factors for Units Listed Alphabetically
!
! http://physics.nist.gov/Pubs/SP811/contents.full.html (accessed 17/8/2006)

module real_constants

  use kinddefs, only : dp

  implicit none

  private

    public :: convl, convl2
    public :: conva1, conva2
    public :: convf, convm, convp, convt, convs, convr

    public :: airmol
    public :: pref_atmos
    public :: tref_atmos
    public :: tref_visc
    public :: aviscr
    public :: rm, rj
    public :: g

!* to convert [in]  to [m]
    real(dp), parameter :: convl     = 0.0254_dp
!* to convert [ft] to [m]
    real(dp), parameter :: convl2    = 12.0_dp*convl
!* to convert [sq.in.] to [sq.cm.]
    real(dp), parameter :: conva1    = 10000._dp*convl*convl
!* to convert [ft^2] to [m^2]
    real(dp), parameter :: conva2    = (12.0_dp**2)*(convl*convl)
!* to convert [ft^3] to [m^3]
    real(dp), parameter :: convv     = (12.0_dp**3)*(convl*convl*convl)
!* to convert [lbf] to [N]
    real(dp), parameter :: convf     = 4.44822161526_dp
!* to convert [lbm] to [kg]
    real(dp), parameter :: convm     = 0.45359237_dp
!* to convert [psi] to [Pa] 100000./14.7
    real(dp), parameter :: convp     = convf/(convl*convl)
!* to convert [R]   to [K]
    real(dp), parameter :: convt     = 5.0_dp/9.0_dp
!* to convert [ft/s] to [m/s]
    real(dp), parameter :: convs     = 12.0_dp*convl
!* to convert [lbm/ft^3] to [kg/m^3]
    real(dp), parameter :: convr     = convm/convv

! [lbm]
    real(dp), parameter :: airmol    = 28.96_dp
! [Pa] reference pressure atmosphere
    real(dp), parameter :: pref_atmos = 101325.0_dp
! [K] reference temperature atmosphere
    real(dp), parameter :: tref_atmos = 288.15_dp
! [K] reference temperature viscosity
    real(dp), parameter :: tref_visc = 288.15_dp
! [kg/m-sec] - reference viscosity
    real(dp), parameter :: aviscr    = 1.7894e-5_dp
! [J/kg-K]  1962 Std. Atmos.
    real(dp), parameter :: rm        = 287.053_dp
! [J/K-mol]/[kg/mol]
!    real(dp), parameter :: rstar    = 8.31432/29.9644
! [ft/R] --> [ft-lbf/lbm-R]  1962 Std. Atmos.
    real(dp), parameter :: rj        = 53.352_dp
! [ft/sec**2]
    real(dp), parameter :: g         = 32.174_dp

end module real_constants

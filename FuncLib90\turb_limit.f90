!================================= TURB_LIMIT ================================80
!
! Limit turbulence update.
!
!=============================================================================80
  pure function turb_limit( turb, dturb, minimum_allowable_turb )

    use kinddefs,        only : dp
    use turb_parameters, only : f_allow_decrease, f_allow_increase,            &
                                limit_dq_threshold_t
    use info_depr,       only : use_full_turb_update

    real(dp), intent(in) :: turb, dturb, minimum_allowable_turb
    real(dp)             :: turb_eps, turb_limit

  continue

    turb_limit = turb + dturb

    if ( use_full_turb_update) return

    if ( real(turb,dp) >= 0._dp ) then               !A:turbulence non-negative.

      turb_eps = max( turb, limit_dq_threshold_t )

      if ( dturb > limit_dq_threshold_t ) then       !A.1:increasing turbulence.

        turb_limit = min( turb_limit, turb + f_allow_increase*turb_eps )

      elseif ( dturb < -limit_dq_threshold_t ) then  !A.2:decreasing turbulence.

        turb_limit = max( turb_limit, turb - f_allow_decrease*turb_eps )

      endif
                                                     !A.3:delta below threshold.

    else                                             !B:turbulence negative.

      turb_eps = min( turb, -limit_dq_threshold_t )

      if ( dturb < -limit_dq_threshold_t ) then      !B.1:decreasing turbulence.

        turb_limit = max( turb_limit, turb + f_allow_decrease*turb_eps )


      endif
                                                     !B.2:increasing turbulence.
                                                     !B.3:delta below threshold.
    endif

    turb_limit = max( turb_limit, minimum_allowable_turb )

  end function turb_limit

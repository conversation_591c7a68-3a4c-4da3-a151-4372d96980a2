module parallel_edge_swap

  use kinddefs,        only : dp

  implicit none

  private

  public :: report_max_dot, embed_edge_swap

  public :: gridregisterallcells
  public :: gridpack

  type swap_type
    integer :: nswap, nremove, nadd
    integer :: swapstride, cellstride
    integer,     dimension(:),   pointer :: swapremove, swapadd
    integer,     dimension(:,:), pointer :: removecell, addcell
    real(dp), dimension(:,:), pointer    :: addxyz
  end type swap_type

contains

!================================== parallel_edge_swap =======================80
!
!=============================================================================80

  subroutine embed_edge_swap( grid, dotfloor, orig_nnodesg )

    use kinddefs,        only : dp
    use grid_types,      only : grid_type
    use element_defs,    only : local_e2n_tet
    use lmpi,            only : lmpi_id, lmpi_nproc
    use allocations,     only : my_alloc_ptr

    type(grid_type), intent(inout) :: grid
    real(dp),        intent(in)    :: dotfloor
    integer,         intent(in)    :: orig_nnodesg

    integer     :: cell
    real(dp)    :: dot
    integer     :: edge, node1, node2
    integer     :: ncellfound
    integer, dimension(4)   :: celllist
    integer, dimension(4,4) :: shell
    integer, dimension(6)   :: equator

    integer :: processor
    logical :: localswap, sharededge

    type(swap_type) :: swap

    continue

    swap%swapstride = 250
    call my_alloc_ptr(swap%swapremove,swap%swapstride)
    call my_alloc_ptr(swap%swapadd,swap%swapstride)
    swap%cellstride = 1000
    call my_alloc_ptr(swap%removecell,4,swap%cellstride)
    call my_alloc_ptr(swap%addcell,4,swap%cellstride)
    call my_alloc_ptr(swap%addxyz,12,swap%cellstride)

    proc_loop :do processor = -1, lmpi_nproc-1

      swap%nswap   = 0
      swap%nremove = 0
      swap%nadd    = 0

      i_am_swapping : if (processor < 0 .or. processor == lmpi_id) then
        check_all_cells : do cell = 1, grid%elem(1)%ncell

          if (grid%elem(1)%c2n(1,cell) <= 0) cycle check_all_cells

          dot = gridtetdot(grid,                                               &
            grid%elem(1)%c2n(1,cell),                                          &
            grid%elem(1)%c2n(2,cell),                                          &
            grid%elem(1)%c2n(3,cell),                                          &
            grid%elem(1)%c2n(4,cell)  )

          bad_dot : if (dot > dotfloor ) then
            search_each_edge : do edge = 1, 6
              node1 = local_e2n_tet(edge,1)
              node2 = local_e2n_tet(edge,2)
              node1 = grid%elem(1)%c2n(node1,cell)
              node2 = grid%elem(1)%c2n(node2,cell)

              ! if the edge connects to an old node skip it
              if (   grid%l2g(node1) <= orig_nnodesg                           &
                .or. grid%l2g(node2) <= orig_nnodesg ) then
                cycle search_each_edge
              end if

              call gridformgem(grid,node1,node2,4,celllist,ncellfound)
              if (ncellfound /= 4) cycle search_each_edge
              call gridorientcelllistnodes(grid,node1,node2,4,celllist,shell)
              if ( .not. gridfillequator(shell,equator) ) cycle search_each_edge

              localswap = (                                                    &
                equator(1) <= grid%nnodes0 .and.                               &
                equator(2) <= grid%nnodes0 .and.                               &
                equator(3) <= grid%nnodes0 .and.                               &
                equator(4) <= grid%nnodes0 .and.                               &
                equator(5) <= grid%nnodes0 .and.                               &
                equator(6) <= grid%nnodes0 )

              if ( localswap ) then
                if ( edgeswap4(grid,celllist,equator,dotfloor,-1,swap) )       &
                  exit search_each_edge
              else
                sharededge = (                                                 &
                  equator(1) <= grid%nnodes0 .or.                              &
                  equator(2) <= grid%nnodes0 )
                if ( processor > -1 .and. sharededge ) then
                  if ( edgeswap4(grid,celllist,equator,dotfloor,               &
                                 processor,swap ) ) exit search_each_edge
                end if
              end if

            end do search_each_edge
          end if bad_dot

        end do check_all_cells
      end if i_am_swapping

      if ( processor > -1 ) call syncswap(grid,processor,swap)

    end do proc_loop

    deallocate(swap%addxyz)
    deallocate(swap%addcell)
    deallocate(swap%removecell)
    deallocate(swap%swapadd)
    deallocate(swap%swapremove)

  end subroutine embed_edge_swap

!==================================== EDGESWAP4 ==============================80
!
! do an edge swap on a four cell mesh
!
!=============================================================================80

  function edgeswap4( grid, celllist, equator, dotfloor, processor, swap)

    use kinddefs,        only : dp
    use grid_types,      only : grid_type

    logical                                          :: edgeswap4
    type(grid_type),                   intent(inout) :: grid
    integer,     dimension(4),         intent(in)    :: celllist
    integer,     dimension(6),         intent(in)    :: equator
    real(dp),                          intent(in)    :: dotfloor
    integer,                           intent(in)    :: processor
    type(swap_type),                   intent(inout) :: swap

    integer, dimension(6) :: equator1, equator2, equator3
    integer, dimension(4,4) :: shell1, shell2, shell3, bestshell
    real(dp)    :: dot1, dot2, dot3, bestdot

    integer :: icell
    integer :: in4

    continue

    equator1 = equator

    do icell = 1, 4
      shell1(1,icell) = equator1(1)
      shell1(2,icell) = equator1(2)
      shell1(3,icell) = equator1(2+icell)
      in4 = 3+icell
      if (in4 > 6) in4 = 3
      shell1(4,icell) = equator1(in4)
    end do

    ! rotate equator about 4-6 axis

    equator2(1) = equator1(3)
    equator2(3) = equator1(2)
    equator2(2) = equator1(5)
    equator2(5) = equator1(1)
    equator2(4) = equator1(4)
    equator2(6) = equator1(6)

    do icell = 1, 4
      shell2(1,icell) = equator2(1)
      shell2(2,icell) = equator2(2)
      shell2(3,icell) = equator2(2+icell)
      in4 = 3+icell
      if (in4 > 6) in4 = 3
      shell2(4,icell) = equator2(in4)
    end do

    ! rotate equator about 3-5 axis

    equator3(4) = equator1(1)
    equator3(2) = equator1(4)
    equator3(6) = equator1(2)
    equator3(1) = equator1(6)
    equator3(3) = equator1(3)
    equator3(5) = equator1(5)

    do icell = 1, 4
      shell3(1,icell) = equator3(1)
      shell3(2,icell) = equator3(2)
      shell3(3,icell) = equator3(2+icell)
      in4 = 3+icell
      if (in4 > 6) in4 = 3
      shell3(4,icell) = equator3(in4)
    end do

    dot1 = shell_max_dot( grid, shell1 )
    dot2 = shell_max_dot( grid, shell2 )
    dot3 = shell_max_dot( grid, shell3 )

    get_best_combo : if (dot3 < dot2 ) then
      bestdot = dot3
      bestshell = shell3
    else
      bestdot = dot2
      bestshell = shell2
    end if get_best_combo

    beter_combo : if  (   bestdot < dot1 .and. bestdot < dotfloor ) then
      call replace_cells_with_shells ( grid, celllist, bestshell,              &
                                       processor, swap )
      edgeswap4 = .true.
    else
      edgeswap4 = .false.
    end if beter_combo

  end function edgeswap4

!============================= REPLACE_CELLS_WITH_SHELLS =====================80
!
! replace four cells with a new shell
!
!=============================================================================80

  subroutine replace_cells_with_shells (grid, celllist, shell, processor, swap )

    use grid_types,      only : grid_type

    type(grid_type),                   intent(inout) :: grid
    integer,     dimension(4),         intent(in)    :: celllist
    integer,     dimension(4,4),       intent(in)    :: shell
    integer,                           intent(in)    :: processor
    type(swap_type),                   intent(inout) :: swap

    integer :: icell, inode, node
    logical :: local, spans_parts

    continue

    if ( processor > -1 ) call startnewswap(swap)

    remove_cells : do icell = 1, 4
      spans_parts = (                                                          &
        grid%elem(1)%c2n(1,celllist(icell)) > grid%nnodes0 .or.                &
        grid%elem(1)%c2n(2,celllist(icell)) > grid%nnodes0 .or.                &
        grid%elem(1)%c2n(3,celllist(icell)) > grid%nnodes0 .or.                &
        grid%elem(1)%c2n(4,celllist(icell)) > grid%nnodes0 )
      if ( (processor > -1) .and. spans_parts )                                &
        call queuecellforremoval(grid,swap,celllist(icell))
      call gridremovecell(grid,celllist(icell))
    end do remove_cells

    ! only add local cells

    add_shells : do icell = 1, 4
      local = (                                                                &
        shell(1,icell) <= grid%nnodes0 .or.                                    &
        shell(2,icell) <= grid%nnodes0 .or.                                    &
        shell(3,icell) <= grid%nnodes0 .or.                                    &
        shell(4,icell) <= grid%nnodes0  )
      spans_parts = (                                                          &
        shell(1,icell) > grid%nnodes0 .or.                                     &
        shell(2,icell) > grid%nnodes0 .or.                                     &
        shell(3,icell) > grid%nnodes0 .or.                                     &
        shell(4,icell) > grid%nnodes0  )
      if ( (processor > -1) .and. spans_parts )                                &
        call queuecellforaddition(grid,swap,shell(:,icell))
      if (local) call gridaddcell(grid,shell(:,icell))
    end do add_shells

    ! remove unused nodes
    do icell = 1,4
      do inode = 1,4
        node = shell(inode,icell)
        if (gridnodedeg(grid,node) == 0) call gridremovenode(grid,node)
      end do
    end do

  end subroutine replace_cells_with_shells


!=============================================================================80
!=============================================================================80
  subroutine startnewswap(swap)
    use allocations,     only : my_realloc_ptr

    type(swap_type),       intent(inout) :: swap

    integer :: newsize

    continue

    swap%nswap = swap%nswap + 1
    if ( swap%nswap > size(swap%swapremove,1) ) then
      newsize = size(swap%swapremove,1) + swap%swapstride
      call my_realloc_ptr(swap%swapremove,newsize)
      call my_realloc_ptr(swap%swapadd,newsize)
    end if
    swap%swapremove(swap%nswap) = 0
    swap%swapadd(swap%nswap) = 0

  end subroutine startnewswap

!=============================================================================80
!=============================================================================80
  subroutine queuecellforremoval(grid,swap,cell)
    use grid_types,      only : grid_type
    use allocations,     only : my_realloc_ptr

    type(grid_type),       intent(in)    :: grid
    type(swap_type),       intent(inout) :: swap
    integer,               intent(in)    :: cell

    integer :: i, newsize

    continue

    swap%nremove = swap%nremove + 1
    if ( swap%nremove > size(swap%removecell,2) ) then
      newsize = size(swap%removecell,2) + swap%cellstride
      call my_realloc_ptr(swap%removecell,4,newsize)
    end if

    swap%swapremove(swap%nswap) = swap%swapremove(swap%nswap) + 1
    do i = 1,4
      swap%removecell(i,swap%nremove) = grid%l2g(grid%elem(1)%c2n(i,cell))
    end do

  end subroutine queuecellforremoval

!=============================================================================80
!=============================================================================80
  subroutine queuecellforaddition(grid,swap,nodes)
    use grid_types,      only : grid_type
    use allocations,     only : my_realloc_ptr

    type(grid_type),       intent(in)    :: grid
    type(swap_type),       intent(inout) :: swap
    integer, dimension(4), intent(in)    :: nodes

    integer :: i, newsize

    continue

    swap%nadd = swap%nadd + 1
    if ( swap%nadd > size(swap%addcell,2) ) then
      newsize = size(swap%addcell,2) + swap%cellstride
      call my_realloc_ptr(swap%addcell,4,newsize)
      call my_realloc_ptr(swap%addxyz,12,newsize)
    end if

    swap%swapadd(swap%nswap) = swap%swapadd(swap%nswap) + 1
    do i = 1,4
      swap%addcell(i,       swap%nadd) = grid%l2g(nodes(i))
      swap%addxyz(1+3*(i-1),swap%nadd) = grid%x(nodes(i))
      swap%addxyz(2+3*(i-1),swap%nadd) = grid%y(nodes(i))
      swap%addxyz(3+3*(i-1),swap%nadd) = grid%z(nodes(i))
    end do

  end subroutine queuecellforaddition

!============================= syncswap ======================================80
!=============================================================================80

  subroutine syncswap ( grid, processor, swap )

    use grid_types,      only : grid_type
    use grid_helper,     only : lookupg2l, uniqueg2l
    use lmpi,            only : lmpi_bcast, lmpi_id
    use allocations,     only : my_realloc_ptr

    type(grid_type),                   intent(inout) :: grid
    integer,                           intent(in)    :: processor
    type(swap_type),                   intent(inout) :: swap

    integer :: myremove, maxremove
    integer :: myadd, maxadd
    integer :: myswap, maxswap
    integer :: remove, add

    integer :: iswap, icell, inode, node, localcellid
    logical :: local
    integer, dimension(4) :: localcell


    continue

    myremove = size(swap%removecell,2)
    myadd    = size(swap%addcell,2)
    myswap   = size(swap%swapremove,1)
    if (processor==lmpi_id) then
      maxremove = myremove
      maxadd    = myadd
      maxswap   = myswap
    end if
    call lmpi_bcast(maxremove, processor)
    call lmpi_bcast(maxadd,    processor)
    call lmpi_bcast(maxswap,   processor)
    if (myremove /= maxremove) then
      call my_realloc_ptr(swap%removecell,4,maxremove)
    end if
    if (myadd /= maxadd) then
      call my_realloc_ptr(swap%addcell,4,maxadd)
      call my_realloc_ptr(swap%addxyz,12,maxadd)
    end if
    if (myswap /= maxswap) then
      call my_realloc_ptr(swap%swapremove,maxswap)
      call my_realloc_ptr(swap%swapadd,maxswap)
    end if

    call lmpi_bcast(swap%nswap,      processor)
    call lmpi_bcast(swap%nremove,    processor)
    call lmpi_bcast(swap%nadd,       processor)
    call lmpi_bcast(swap%swapremove, processor)
    call lmpi_bcast(swap%swapadd,    processor)
    call lmpi_bcast(swap%removecell, processor)
    call lmpi_bcast(swap%addcell,    processor)
    call lmpi_bcast(swap%addxyz,     processor)

    not_local_processor : if (processor /= lmpi_id ) then

      remove = 0
      add    = 0
      process_swap_transaction : do iswap = 1, swap%nswap

        remove_cells : do icell = 1, swap%swapremove(iswap)
          remove = remove + 1
          do inode = 1, 4
            swap%removecell(inode,remove) =                                    &
              lookupg2l(swap%removecell(inode,remove),grid)
          end do
          localcellid = gridfindcell(grid, swap%removecell(1:4,remove))
          if (localcellid>0) call gridremovecell(grid,localcellid)
        end do remove_cells

        add_cells : do icell = 1, swap%swapadd(iswap)
          add = add + 1
          do inode = 1, 4
            localcell(inode) = lookupg2l(swap%addcell(inode,add),grid)
          end do
          local = (                                                            &
            ( localcell(1) > 0 .and. localcell(1) <= grid%nnodes0 ).or.        &
            ( localcell(2) > 0 .and. localcell(2) <= grid%nnodes0 ).or.        &
            ( localcell(3) > 0 .and. localcell(3) <= grid%nnodes0 ).or.        &
            ( localcell(4) > 0 .and. localcell(4) <= grid%nnodes0 ) )
          if (local) then
            do inode = 1, 4
              localcell(inode) = uniqueg2l(grid,swap%addcell(inode,add))
            end do
            call gridaddcell(grid,localcell)
            do inode = 1, 4
              grid%x(localcell(inode)) = swap%addxyz(1+3*(inode-1),add)
              grid%y(localcell(inode)) = swap%addxyz(2+3*(inode-1),add)
              grid%z(localcell(inode)) = swap%addxyz(3+3*(inode-1),add)
            end do
          end if
        end do add_cells
      end do process_swap_transaction

      remove_used_nodes : do icell = 1,swap%nremove
        do inode = 1,4
          node = swap%removecell(inode,icell)
          if (node>0) then
            if (gridnodedeg(grid,node) == 0) call gridremovenode(grid,node)
          end if
        end do
      end do remove_used_nodes

    end if not_local_processor

  end subroutine syncswap

!==================================== SHELL_MAX_DOT ==========================80
!
! get the max dot for a 4 cell shell
!
!=============================================================================80

  function shell_max_dot( grid, shell )

    use kinddefs,        only : dp
    use grid_types,      only : grid_type
    use grid_helper,     only : gridpositivevolumetet

    real(dp)                                        :: shell_max_dot
    type(grid_type),                  intent(in)    :: grid
    integer,     dimension(4, 4),     intent(in)    :: shell

    real(dp)    :: maxdot, dot
    integer     :: cell

    real(dp), parameter    :: my_2 = 2.0_dp

    continue

    maxdot = -my_2

    do cell = 1, 4
      dot = gridtetdot( grid,                                                  &
        shell(1,cell), shell(2,cell), shell(3,cell), shell(4,cell) )
      maxdot = max(maxdot, dot )

      if ( .not. gridpositivevolumetet ( grid,                                 &
           shell(1,cell), shell(2,cell), shell(3,cell), shell(4,cell) ) )      &
             maxdot = my_2
    end do

    shell_max_dot = maxdot

  end function shell_max_dot

!==================================== REPORT_MAX_DOT =========================80
!
! report the maximum face dot prod and angle
!
!=============================================================================80

  subroutine report_max_dot( grid, filename )

    use kinddefs,        only : dp, i8
    use grid_types,      only : grid_type
    use lmpi,            only : lmpi_master, lmpi_reduce, lmpi_max
    use system_extensions, only : se_open

    type(grid_type),             intent(in) :: grid
    character(len=*),  optional, intent(in) :: filename

    real(dp)    :: dot, maxdot, maxangle, anglelimit, dotlimit, globaldot
    integer     :: cell, node
    integer(i8) :: nbad, globalbad

    integer,     parameter :: itec   = 43
    real(dp), parameter    :: my_1   = 1.0_dp
    real(dp), parameter    :: my_2   = 2.0_dp
    real(dp), parameter    :: my_180 = 180.0_dp

    continue

    open_tecplot : if ( present(filename) ) then
      call se_open(itec,file=filename)
      write(itec,*) "title='bad cells'"
      write(itec,*) "variables='X','Y','Z'"
    end if open_tecplot

    anglelimit = my_2

    dotlimit = cos(anglelimit/my_180*acos(-my_1))

    maxdot = -huge(1.0_dp)

    nbad = 0

    cell_loop : do cell = 1, grid%elem(1)%ncell
      if  (  grid%elem(1)%c2n(1,cell) <= 0                                     &
        .or. grid%elem(1)%c2n(1,cell) > grid%nnodes0 ) cycle cell_loop
      dot = gridtetdot(grid,                                                   &
        grid%elem(1)%c2n(1,cell), grid%elem(1)%c2n(2,cell),                    &
        grid%elem(1)%c2n(3,cell), grid%elem(1)%c2n(4,cell) )
      maxdot = max(maxdot, dot)
      bad_cell : if (dot > dotlimit) then
        nbad = nbad + 1
        dump_bad_cell : if ( present(filename) ) then
          write(itec,*)"zone n=4, e=1, f=fepoint, et=tetrahedron"
          do node =1,4
            write(itec,'(3(e23.15))')                                          &
                 grid%x(grid%elem(1)%c2n(node,cell)),                          &
                 grid%y(grid%elem(1)%c2n(node,cell)),                          &
                 grid%y(grid%elem(1)%c2n(node,cell))
          end do
          write(itec,*) "1 2 3 4"
        end if dump_bad_cell
      end if bad_cell
    end do cell_loop

    call lmpi_max(maxdot,globaldot)
    call lmpi_reduce(nbad,globalbad)

    if (lmpi_master) then

      maxangle = my_180 - acos(globaldot)/acos(-my_1)*my_180

      write(*,'(a,f15.12,a,f15.10)') '  edge_swap_c2n : max dot ', globaldot,  &
                                     ' max angle ', maxangle

      write(*,'(2(a,i12),a,f6.2,a,f15.12,a)')                                  &
        '   cells ',globalbad,' of ', grid%elem(1)%ncellg,                     &
        ' greater than ', 180.0_dp - anglelimit, ' deg (', dotlimit,' )'

    end if

    close_tecplot : if ( present(filename) ) then
      close(unit=itec)
    end if close_tecplot

  end subroutine report_max_dot

!=================================== gridtetdot ==============================80
!
!=============================================================================80

  function gridtetdot(grid, node1, node2, node3, node4)

    use kinddefs,        only : dp
    use grid_types,      only : grid_type

    real(dp)                                        :: gridtetdot
    type(grid_type),                  intent(in)    :: grid
    integer,                          intent(in)    :: node1,node2,node3,node4

    real(dp)    :: x1, x2, x3, x4
    real(dp)    :: y1, y2, y3, y4
    real(dp)    :: z1, z2, z3, z4

    real(dp)    :: nx1, nx2, nx3, nx4
    real(dp)    :: ny1, ny2, ny3, ny4
    real(dp)    :: nz1, nz2, nz3, nz4

    real(dp)    :: rlen1, xnorm1, ynorm1, znorm1
    real(dp)    :: rlen2, xnorm2, ynorm2, znorm2
    real(dp)    :: rlen3, xnorm3, ynorm3, znorm3
    real(dp)    :: rlen4, xnorm4, ynorm4, znorm4

    real(dp)    :: dot, maxdot

    real(dp), parameter    :: my_haf = 0.5_dp

    continue

    x1 = grid%x(node1)
    x2 = grid%x(node2)
    x3 = grid%x(node3)
    x4 = grid%x(node4)

    y1 = grid%y(node1)
    y2 = grid%y(node2)
    y3 = grid%y(node3)
    y4 = grid%y(node4)

    z1 = grid%z(node1)
    z2 = grid%z(node2)
    z3 = grid%z(node3)
    z4 = grid%z(node4)

! Lets get outward normals (nx_i is for the face opposite node i)

    nx1 = my_haf*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
    ny1 = my_haf*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
    nz1 = my_haf*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

    nx2 = my_haf*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
    ny2 = my_haf*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
    nz2 = my_haf*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

    nx3 = my_haf*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
    ny3 = my_haf*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
    nz3 = my_haf*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

    nx4 = -nx1 -nx2 -nx3
    ny4 = -ny1 -ny2 -ny3
    nz4 = -nz1 -nz2 -nz3

    rlen1 = sqrt(nx1*nx1 + ny1*ny1 + nz1*nz1)
    rlen2 = sqrt(nx2*nx2 + ny2*ny2 + nz2*nz2)
    rlen3 = sqrt(nx3*nx3 + ny3*ny3 + nz3*nz3)
    rlen4 = sqrt(nx4*nx4 + ny4*ny4 + nz4*nz4)

    prevent_divide_by_zero : if ( rlen1 < tiny(1.0_dp) .or.    &
                                  rlen2 < tiny(1.0_dp) .or.    &
                                  rlen3 < tiny(1.0_dp) .or.    &
                                  rlen4 < tiny(1.0_dp) ) then
      gridtetdot = huge(1.0_dp)
    else
      xnorm1 = nx1/rlen1
      ynorm1 = ny1/rlen1
      znorm1 = nz1/rlen1

      xnorm2 = nx2/rlen2
      ynorm2 = ny2/rlen2
      znorm2 = nz2/rlen2

      xnorm3 = nx3/rlen3
      ynorm3 = ny3/rlen3
      znorm3 = nz3/rlen3

      xnorm4 = nx4/rlen4
      ynorm4 = ny4/rlen4
      znorm4 = nz4/rlen4

      dot = xnorm1*xnorm2 + ynorm1*ynorm2 + znorm1*znorm2
      maxdot = dot
      dot = xnorm1*xnorm3 + ynorm1*ynorm3 + znorm1*znorm3
      maxdot = max(dot,maxdot)
      dot = xnorm1*xnorm4 + ynorm1*ynorm4 + znorm1*znorm4
      maxdot = max(dot,maxdot)
      dot = xnorm2*xnorm3 + ynorm2*ynorm3 + znorm2*znorm3
      maxdot = max(dot,maxdot)
      dot = xnorm2*xnorm4 + ynorm2*ynorm4 + znorm2*znorm4
      maxdot = max(dot,maxdot)
      dot = xnorm3*xnorm4 + ynorm3*ynorm4 + znorm3*znorm4
      maxdot = max(dot,maxdot)

      gridtetdot = maxdot
    end if prevent_divide_by_zero
  end function gridtetdot

!============================== gridformgem ==================================80
!
!=============================================================================80

  subroutine gridformgem(grid,node1,node2,maxsize,celllist,ncellfound)
    use grid_types,      only : grid_type

    type(grid_type),             intent(in)    :: grid
    integer,                     intent(in)    :: node1, node2
    integer,                     intent(in)    :: maxsize
    integer, dimension(maxsize), intent(out)   :: celllist
    integer,                     intent(out)   :: ncellfound

    integer :: n2cindex, cell, inode

    continue

    ncellfound = 0

    n2cindex = grid%firstn2c(node1)
    search_link_list : do while (n2cindex > 0)
      cell = grid%n2c(1,n2cindex)
      look_for_node2 : do inode = 1, 4
        found_node2 : if (grid%elem(1)%c2n(inode,cell) == node2 ) then
          ncellfound = ncellfound + 1
          if (ncellfound > maxsize ) then
            ncellfound = -1
            return
          end if
          celllist(ncellfound) = cell
        end if found_node2
      end do look_for_node2
      n2cindex = grid%n2c(2,n2cindex)
    end do search_link_list

  end subroutine gridformgem

!============================== gridorientcelllistnodes ======================80
!
!=============================================================================80

  subroutine gridorientcelllistnodes(grid,node1,node2,cellsize,celllist,shell)
    use grid_types,      only : grid_type

    type(grid_type),                intent(in)  :: grid
    integer,                        intent(in)  :: node1, node2
    integer,                        intent(in)  :: cellsize
    integer, dimension(cellsize),   intent(in)  :: celllist
    integer, dimension(4,cellsize), intent(out) :: shell

    integer :: icell, cell, inode, temp

    continue

    ! remember if you swap two nodes you must swap twice to keep right hand rule

    orient_nodes_of_cells : do icell = 1, 4
      cell = celllist(icell)
      load_nodes : do inode = 1, 4
        shell(inode, icell) = grid%elem(1)%c2n(inode,cell)
      end do load_nodes
      orient_node1 : do inode = 2, 4
        if ( shell(inode, icell) == node1 ) then
          shell(inode, icell) = shell(1, icell)
          shell(1, icell) = node1
          temp = shell(3, icell)
          shell(3, icell) = shell(4, icell)
          shell(4, icell) = temp
          exit orient_node1
        end if
      end do orient_node1
      orient_node2 : do inode = 3, 4 ! node1 is in place
        if ( shell(inode, icell) == node2 ) then
          shell(inode, icell) = shell(2, icell)
          shell(2, icell) = node2
          temp = shell(3, icell)
          shell(3, icell) = shell(4, icell)
          shell(4, icell) = temp
          exit orient_node2
        end if
      end do orient_node2
      if (shell(1, icell) /= node1 .or. shell(2, icell) /= node2 )             &
           write(*,*)'ERROR: grid_helper: orient_nodes_of_cells'
    end do orient_nodes_of_cells

  end subroutine gridorientcelllistnodes

!============================== gridfillequator ==============================80
!
!=============================================================================80

  function gridfillequator(shell,equator)

    logical                                     :: gridfillequator
    integer, dimension(4,4),        intent(in)  :: shell
    integer, dimension(6),          intent(out) :: equator

    integer :: filled_node, icell
    logical :: bc_induced_equ_gap
    continue

    equator = 0
    equator(1) = shell(1,1)
    equator(2) = shell(2,1)
    equator(3) = shell(3,1)
    equator(4) = shell(4,1)

    bc_induced_equ_gap = .false.

    ! start the equator with the first cell the correct way and
    ! the rest will fall in line

    fill_equator : do filled_node = 5, 6
      from_cell : do icell = 2, 4
        got_fill_node : if ( equator(filled_node-1) == shell(3,icell) ) then
          equator(filled_node) = shell(4,icell)
          cycle fill_equator
        end if got_fill_node
      end do from_cell
      bc_induced_equ_gap = .true.
      exit fill_equator
    end do fill_equator

    if ( bc_induced_equ_gap ) then
      gridfillequator = .false.
      return
    end if

    check_cells : do icell = 2, 4
      if  (   shell(3, icell) == equator(6)                                    &
        .and. shell(4, icell) == equator(3) ) then
        gridfillequator = .true.
        return
      end if
    end do check_cells

    gridfillequator = .false.

  end function gridfillequator

!============================== gridremovenode ===============================80
!
!=============================================================================80

  subroutine gridremovenode(grid,node)
    use grid_types,      only : grid_type
    use grid_helper,     only : findglobalentry

    type(grid_type),       intent(inout) :: grid
    integer,               intent(in)    :: node

    integer :: indx, removepoint

    continue

    if (node < 1) return
    if (node > grid%nnodes01) return
    if (grid%l2g(node) < 1) return

    removepoint = findglobalentry(grid,grid%l2g(node))

    grid%l2g(node) = -grid%firstemptynode
    grid%firstemptynode = node

    grid%nsorted = grid%nsorted - 1
    slide_global_down : do indx = removepoint, grid%nsorted
      grid%sortedglobal(indx) = grid%sortedglobal(indx+1)
    end do slide_global_down
    slide_local_down : do indx = removepoint, grid%nsorted
      grid%sortedlocal(indx)  = grid%sortedlocal(indx+1)
    end do slide_local_down

  end subroutine gridremovenode

!============================== gridregisterallcells =========================80
!
!=============================================================================80

  subroutine gridregisterallcells(grid)
    use grid_types,      only : grid_type
    use allocations,     only : my_alloc_ptr

    type(grid_type),       intent(inout) :: grid

    integer :: cell
    integer :: i, maxn2c
    continue

    maxn2c = grid%elem(1)%ncell*4
    if ( associated(grid%n2c) ) deallocate(grid%n2c)
    call my_alloc_ptr(grid%n2c, 2, maxn2c)
    grid%firstemptyn2c = 1
    do i = 1, maxn2c
      grid%n2c(1,i) = 0
      grid%n2c(2,i) = i+1
    end do
    grid%n2c(2,maxn2c) = 0

    do cell = 1, grid%elem(1)%ncell
      call gridregistercell(grid,cell)
    end do

  end subroutine gridregisterallcells

!============================== gridregistercell =============================80
!
!=============================================================================80

  subroutine gridregistercell(grid,cell)
    use grid_types,      only : grid_type
    use allocations,     only : my_realloc_ptr

    type(grid_type),       intent(inout) :: grid
    integer,               intent(in)    :: cell

    integer :: i, j, node, nextempty
    integer :: sizen2c, maxn2c

    continue

    do i = 1, 4
      if (grid%firstemptyn2c == 0) then
        sizen2c = size(grid%n2c,2)
        maxn2c = sizen2c + 5000
        call my_realloc_ptr(grid%n2c,2,maxn2c)
        do j = 1+sizen2c, maxn2c
          grid%n2c(1,j) = 0
          grid%n2c(2,j) = j+1
        end do
        grid%n2c(2,maxn2c) = 0
        grid%firstemptyn2c = sizen2c + 1
      end if
      node = grid%elem(1)%c2n(i,cell)
      nextempty = grid%n2c(2,grid%firstemptyn2c)
      grid%n2c(1,grid%firstemptyn2c) = cell
      grid%n2c(2,grid%firstemptyn2c) = grid%firstn2c(node)
      grid%firstn2c(node) = grid%firstemptyn2c
      grid%firstemptyn2c = nextempty
    end do

  end subroutine gridregistercell

!============================== gridaddcell ==================================80
!
!=============================================================================80

  subroutine gridaddcell(grid,nodes)
    use grid_types,      only : grid_type
    use allocations,     only : my_realloc_ptr

    type(grid_type),       intent(inout) :: grid
    integer, dimension(4), intent(in)    :: nodes

    integer :: i, maxcell, newsize, newcell

    continue

    reuse_removed_cell : if (grid%firstemptyc2n > 0 ) then
      newcell = grid%firstemptyc2n
      grid%firstemptyc2n = -grid%elem(1)%c2n(1,newcell)
    else
      maxcell = size(grid%elem(1)%c2n,2)

      if (grid%elem(1)%ncell >= maxcell) then
        newsize = maxcell + 5000
        call my_realloc_ptr(grid%elem(1)%c2n,4,newsize)
      end if

      grid%elem(1)%ncell = grid%elem(1)%ncell + 1
      newcell = grid%elem(1)%ncell
    end if reuse_removed_cell

    do i = 1, 4
      grid%elem(1)%c2n(i,newcell) = nodes(i)
    end do
    call gridregistercell(grid,newcell)

  end subroutine gridaddcell

!============================== gridremovecell ===============================80
!
!=============================================================================80

  subroutine gridremovecell(grid,cell)
    use grid_types,      only : grid_type

    type(grid_type),       intent(inout) :: grid
    integer,               intent(in)    :: cell

    integer :: inode, node
    integer :: n2cindex, removen2c, previousn2c

    continue

    do inode = 1, 4
      node = grid%elem(1)%c2n(inode,cell)
      removen2c = 0
      previousn2c = 0
      n2cindex = grid%firstn2c(node)
      search_orbit : do while ( n2cindex > 0 )
        if (cell == grid%n2c(1,n2cindex)) then
          removen2c = n2cindex
          exit search_orbit
        end if
        previousn2c = n2cindex
        n2cindex = grid%n2c(2,n2cindex)
      end do search_orbit
      if (removen2c == 0 ) write(*,*)'grid_helper: gridremovecell error'

      if (previousn2c == 0) then
        grid%firstn2c(node) = grid%n2c(2,removen2c)
      else
        grid%n2c(2,previousn2c) = grid%n2c(2,removen2c)
      end if

      grid%n2c(2,removen2c) = grid%firstemptyn2c
      grid%firstemptyn2c = removen2c
    end do

    grid%elem(1)%c2n(1,cell) = -grid%firstemptyc2n
    grid%firstemptyc2n = cell

  end subroutine gridremovecell

!============================== gridfindcell =================================80
!
!=============================================================================80

  function gridfindcell(grid, nodes)
    use grid_types,      only : grid_type

    integer                                  :: gridfindcell
    type(grid_type),           intent(inout) :: grid
    integer,     dimension(4), intent(in)    :: nodes

    integer :: n2cindex, cell, cellnode, inputnode, node, nfound

    continue

    gridfindcell = 0

    if (nodes(1) <= 0) return

    n2cindex = grid%firstn2c(nodes(1))
    search_orbit :do while ( n2cindex > 0 )
      cell = grid%n2c(1,n2cindex)
      nfound = 0
      do cellnode = 1, 4
        node = grid%elem(1)%c2n(cellnode,cell)
        do inputnode = 1, 4
          if (node == nodes(inputnode)) nfound = nfound + 1
        end do
      end do
      if (nfound == 4) then
        gridfindcell = cell
        return
      end if
      n2cindex = grid%n2c(2,n2cindex)
    end do search_orbit

  end function gridfindcell

!============================== gridnodedeg ==================================80
!
!=============================================================================80

  function gridnodedeg(grid,node)
    use grid_types,      only : grid_type

    integer                     :: gridnodedeg
    type(grid_type), intent(in) :: grid
    integer,         intent(in) :: node

    integer :: degree, n2cindex

    continue

    if (node < 1) then
      gridnodedeg = 0
      return
    end if

    degree = 0
    n2cindex = grid%firstn2c(node)
    do while ( n2cindex > 0 )
      degree = degree + 1
      n2cindex = grid%n2c(2,n2cindex)
    end do

    gridnodedeg = degree

  end function gridnodedeg

!============================== gridpack =====================================80
!
!=============================================================================80

  subroutine gridpack(grid)
    use grid_types,      only : grid_type

    type(grid_type), intent(inout) :: grid

    integer               :: orignode, packnode
    integer               :: origcell, packcell, inode

    integer, dimension(grid%nnodes01) :: o2p ! original to packed node index map

    continue

    ! erase all n2c entries
    deallocate(grid%firstn2c) ; grid%firstn2c => null()
    deallocate(grid%n2c)      ; grid%n2c => null()
    grid%firstemptyn2c = 0

    ! pack nodes
    packnode = 0
    do orignode = 1, grid%nnodes01
      valid_node : if (grid%l2g(orignode) > 0) then
        packnode = packnode + 1
        o2p(orignode) = packnode
        slide_node_up : if (orignode /= packnode) then
          grid%l2g(packnode) = grid%l2g(orignode)
          grid%x(packnode)   = grid%x(orignode)
          grid%y(packnode)   = grid%y(orignode)
          grid%z(packnode)   = grid%z(orignode)
        end if slide_node_up
      end if valid_node
    end do
    grid%firstemptynode = 0
    grid%nnodes01 = packnode

    ! pack and renumber cells
    packcell = 0
    do origcell = 1, grid%elem(1)%ncell
      valid_cell : if (grid%elem(1)%c2n(1,origcell) > 0) then
        packcell = packcell + 1
        do inode = 1, 4
          grid%elem(1)%c2n(inode,packcell) =                                   &
            o2p(grid%elem(1)%c2n(inode,origcell))
        end do
      end if valid_cell
    end do
    grid%firstemptyc2n  = 0
    grid%elem(1)%ncell = packcell

  end subroutine gridpack

end module parallel_edge_swap

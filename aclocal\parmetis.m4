# -*- Autoconf -*-
#
# Assigned Shell Variables:
#   $with_parmetis      Build with ParMETIS parallel partitioning support
#
# Assigned Output Variables:
#   @parmetis_include@  Stuff to include ParMETIS header files 
#   @parmetis_ldadd@    Stuff to link ParMETIS library
#
# Assigned AC_DEFINES:
#   HAVE_PARMETIS
#
# Assigned AM_CONDITIONALS:
#   BUILD_PARMETIS_SUPPORT
#
AC_DEFUN([AX_PARMETIS],[

AC_ARG_WITH(parmetis,
	[  --with-parmetis[=ARG]     use ParMETIS partitioner [ARG=no]],
	[with_parmetis=$withval],    [with_parmetis="no"])

if test "$with_parmetis" != 'no'
then
  AC_CHECK_FILE([$with_parmetis/include/parmetis.h],
                [parmetis_h_path=$with_parmetis/include],
                [parmetis_h_path='no'])
  AC_CHECK_FILE([$with_parmetis/include/metis.h],
                [metis_h_path=$with_parmetis/include],
                [metis_h_path='no'])
  AC_CHECK_FILE([$with_parmetis/lib/libparmetis.a],
                [libparmetis_a_path=$with_parmetis/lib],
		[libparmetis_a_path='no'])
  AC_CHECK_FILE([$with_parmetis/lib/libmetis.a],
                [libmetis_a_path=$with_parmetis/lib],
		[libmetis_a_path='no'])

  if test "$parmetis_h_path" == 'no'
  then
    AC_MSG_ERROR([parmetis.h not found in $with_parmetis/include])
  fi

  if test "$metis_h_path" == 'no'
  then
    AC_MSG_WARN([metis.h not found in $with_parmetis/include,
                    this is not an issue for ParMETIS 3.*, 
                    but compiling will fail for ParMETIS 4.*])
  fi

  if test "$libparmetis_a_path" == 'no'
  then
    AC_MSG_ERROR([libparmetis.a not found in $with_parmetis/lib])
  fi

  if test "$libmetis_a_path" == 'no'
  then
    AC_MSG_ERROR([libmetis.a not found in $with_parmetis/lib])
  fi

  parmetis_include="-I$parmetis_h_path"
  parmetis_ldadd="-L$libparmetis_a_path -lparmetis -lmetis"

  AC_SUBST([parmetis_include])
  AC_SUBST([parmetis_ldadd])
  AC_DEFINE([HAVE_PARMETIS],[1],[ParMETIS is available])
  AM_CONDITIONAL(BUILD_PARMETIS_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_PARMETIS_SUPPORT,false)
fi

])


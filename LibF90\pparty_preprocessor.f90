module pparty_preprocessor

  use system_extensions, only : se_wall_time, se_open

  implicit none

  private

  public :: pparty_preprocess
  public :: pparty_setup_stuff

  logical, parameter :: dbtime = .false.

! partition line variables

  integer :: n_pts   = 0
  integer, dimension(:),   allocatable :: endline
  integer, dimension(:,:), allocatable :: line

contains

!=============================== pparty_preprocess ===========================80
!
! pparty preprocess
!
!=============================================================================80

  subroutine pparty_preprocess(flow_dir,raw_grid_data,grid)

    use nml_global,           only : nsequence
    use bc_names,             only : need_distance_function
    use bc_types,             only : deallocate_bc
    use grid_types,           only : grid_type, raw_grid_data_type
    use element_defs,         only : type_tet
    use metis_defs,           only : partition_lines,                          &
                                     partitioning_out, partitioning_in
    use info_depr,            only : pp_cmd_outformat, pp_cmd_color, mixed,    &
                                     mixed_present, twod, force_stream_file,   &
                                     pp_cmd_stats, complex_mode,               &
                                     mirror_x, mirror_y,                       &
                                     mirror_z, scale_grid,                     &
                                     scale_grid_factor_x, scale_grid_factor_y, &
                                     scale_grid_factor_z, make_tets, cc_primal
    use implicit_lines,       only : read_lines_fmt_par,                       &
                                     read_lines_fmt_write_lines
    use party_lmpi,           only : party_lmpi_setup_mpi_sm,                  &
                                     party_lmpi_setup_mpi_cc_sm,               &
                                     pparty_write_stats
    use pparty,               only : node_cell_chopper, bc_chopper,            &
                                     writegrid_sm, pparty_convert_g2l,         &
                                     node_cell_chopper_cc
    use pparty_mixed_element, only : edge_pointer_driver, test_edges,          &
                                     edge_pointer_driver_cc, sort_edges_2d,    &
                                     compute_cc_specific
    use pparty_computes,      only : pparty_color, pparty_color_cc
    use pparty_metis,         only : compute_adj_list, migrate_driver,         &
                                     my_metis, my_metis_in, my_metis_out,      &
                                     partition_out_global,                     &
                                     partition_in_sequence,                    &
                                     pparty_write_data_stats, my_metis_out_cc, &
                                     my_metis_cc, compute_adj_list2,           &
                                     compute_c2c_cc_driver,                    &
                                     convert_to_nc_partitioning
    use pparty_metis_lines_revised, only : partition_dof_lines,                &
                                           check_points_2d, find_points_2d
    use pparty_puns3d,        only : puns3d_bc, pparty_puns3d_bc_sm,           &
                                     euler_number, raw_grid_checker,           &
                                     pparty_puns3d_bc_cc_sm
    use puns3d_io_c2n,        only : process_grid_bc, read_mapbc,              &
                                     process_grid_bc_mirror_fill,              &
                                     mirror_grid_par_driver,                   &
                                     puns3d_expand_xyz_level1, delete_faceptr, &
                                     lump_boundaries
    use split_element,        only : split_elements, gather_split_bc
    use local_grid,           only : pp_nhead, pp_ntail, pp_nsize, pp_chead,   &
                                     pp_ctail, pp_csize !, cc_csize
    use lmpi,                 only : lmpi_id, lmpi_nproc, lmpi_die,            &
                                     lmpi_master, lmpi_finalize, lmpi_bcast,   &
                                     lmpi_conditional_stop, lmpi_reduce
    use kinddefs,             only : dp, system_i1
    use debug_defs,           only : implicit_line_sets
    use periodics,            only : periodic
    use multiblocks,          only : multiblock
    use sort,                 only : heap_sort
    use nml_governing_equations, only : ssdc_flag

    character(*), intent(in) :: flow_dir

    type(raw_grid_data_type), intent(inout) :: raw_grid_data
    type(grid_type),          intent(inout) :: grid

    integer :: ielem, sz, i,j, node, ncell1, i2d, ib
    integer :: n_lines, ierr, lmax, overlap, my_s, my_e, line_set

    integer, dimension(:),   pointer :: t_endline
    integer, dimension(:,:), pointer :: t_line
    integer(system_i1), dimension(:), allocatable :: tag

    real(dp), dimension(:), allocatable :: temp

    integer,  dimension(:),   allocatable :: temp_cl2g, temp1
    integer,  dimension(:,:), allocatable :: temp2

    logical :: euler_number_bad, do_mirror

    character(len=80), save :: projectp
    integer, save :: dofgp

    integer                        :: nl2g_on ! number of non-line points
    integer, dimension(:), pointer :: l2g_on  ! local  index to non-line points
    integer, dimension(:), pointer :: l2g_off ! global index to non-line points

  continue

! If we have forced ourselves to read stream files, impose it here

    if ( force_stream_file ) then
      raw_grid_data%grid_format = 'aflr3'
      raw_grid_data%data_format = 'stream'
    endif

! perform consistency checks spanning all input with raw grid-related input

    call raw_grid_consistency_checks(raw_grid_data)

! Process grid

    if ( allocated(pp_nhead) ) deallocate(pp_nhead)
    if ( allocated(pp_ntail) ) deallocate(pp_ntail)
    if ( allocated(pp_nsize) ) deallocate(pp_nsize)
    if ( allocated(pp_chead) ) deallocate(pp_chead)
    if ( allocated(pp_ctail) ) deallocate(pp_ctail)
    if ( allocated(pp_csize) ) deallocate(pp_csize)

    allocate(pp_nhead(0:lmpi_nproc-1)); pp_nhead = 0
    allocate(pp_ntail(0:lmpi_nproc-1)); pp_ntail = 0
    allocate(pp_nsize(0:lmpi_nproc-1)); pp_nsize = 0
    allocate(pp_chead(0:lmpi_nproc-1)); pp_chead = 0
    allocate(pp_ctail(0:lmpi_nproc-1)); pp_ctail = 0
    allocate(pp_csize(0:lmpi_nproc-1)); pp_csize = 0

    if (pp_cmd_color == 0) then
      if (lmpi_master) write(*,*)'Only coloring supported in preprocessor.'
      call lmpi_die
    end if

    if (lmpi_master.and.dbtime)                                               &
       call se_wall_time('    ... Time at pp_preprocessor start ')

! Read grid

    call pparty_read_grid(raw_grid_data,grid,flow_dir)
    call hanging_node_check(pp_nhead(lmpi_id),pp_ntail(lmpi_id),grid)

    if (lmpi_master.and.dbtime)                                               &
       call se_wall_time('    ... Time after pp_preprocessor read_grid')

! Initialize cc variables (really should be done when grid is allocated).

    if (grid%cc) then
       cc_primal = .true.
       grid%ncell_augmentors = 0
    end if

! If complex, load the perturbation data and perform the perturbations

    if ( complex_mode ) call read_complex_input(grid)

! Read partition_lines

    if ( partition_lines ) then
      ierr = 0
      if ( lmpi_master ) then
         write(*,"(1x,a,a,a,i0)") 'Partitioning to preserve lines...project=',&
         trim(grid%project),' grid=',grid%igrid
         call read_lines_fmt_par( ierr, flow_dir, grid%project,               &
                                  n_pts, n_lines, t_endline, t_line )
      end if
      call lmpi_conditional_stop(ierr,"partition_lines:pparty_preprocess")
      if (lmpi_master) lmax = size(t_line,2)
      call lmpi_bcast(n_lines)
      call lmpi_bcast(n_pts)
      call lmpi_bcast(lmax)

      allocate(endline(n_lines));    endline = 0
      allocate(line(n_lines, lmax)); line    = 0
      if (lmpi_master) then
         endline(1:n_lines)       = t_endline(1:n_lines)
         line(1:n_lines,1:lmax) = t_line(1:n_lines,1:lmax)
         deallocate(t_endline); nullify(t_endline)
         deallocate(t_line);    nullify(t_line)
      end if
      call lmpi_bcast(endline)
      call lmpi_bcast(line)

! Check for overlapping lines

      !write(*,*)"MY_S ",lmpi_id,pp_nhead(lmpi_id),pp_ntail(lmpi_id)
       my_s = pp_nhead(lmpi_id)
       my_e = pp_ntail(lmpi_id)
       allocate(tag(my_s:my_e)); tag = 0
       overlap = 0
       outer: do i = 1,n_lines
          do j = 1,endline(i)
             node = line(i,j)
             if ((node >= my_s).and.(node <= my_e)) then
                if (tag(node) == 1) then
                   overlap = 1
                   exit outer
                end if
             end if
          end do
       end do outer

       call lmpi_reduce(overlap,i)
       call lmpi_bcast(i)
       if (i /= 0) then
          if(lmpi_master)write(*,*)'Number of partition lines overlapping ',i
          call lmpi_conditional_stop(i)
       end if

    endif

    do line_set=1,size(implicit_line_sets,1)
      if ( .not. implicit_line_sets(line_set) ) cycle
      call read_lines_fmt_write_lines( line_set, flow_dir, grid%project, ierr )
      call lmpi_conditional_stop(ierr,"fmt_to_unfmt lines:pparty_preprocess")
    enddo

! Set the mixed element flag based on the element types we found in the raw grid

    search_for_non_tets: do ielem = 1, size(grid%elem)
      if ( grid%elem(ielem)%type_cell /= type_tet ) then
        mixed = .true.
        exit search_for_non_tets
      endif
    end do search_for_non_tets

    if (lmpi_master.and.dbtime)                                                &
       call se_wall_time('   ... Time after read grid ')

! Scale or rotate grid coordinates if requested

    if (scale_grid) then
      if (lmpi_master) write(*,*)'Scaling x,y,z of grid by ',                  &
        scale_grid_factor_x, scale_grid_factor_y, scale_grid_factor_z
      grid%x(:)=grid%x(:)*scale_grid_factor_x
      grid%y(:)=grid%y(:)*scale_grid_factor_y
      grid%z(:)=grid%z(:)*scale_grid_factor_z
    end if

    if ( raw_grid_data%swap_yz_axes ) then
      if ( lmpi_master ) then
         write(*,*) 'Swapping y- and z-points and preserving right-handedness.'
         write(*,*) '...y(new) = -z(input)'
         write(*,*) '...z(new) = +y(input)'
      endif
      sz = size(grid%y,1); allocate(temp(sz))
      temp = grid%y; grid%y = -grid%z; grid%z = temp
      deallocate(temp)
    endif

! Expand our node-based arrays to include level-1 data

    if (.not.grid%cc) call puns3d_expand_xyz_level1(grid)

    if (lmpi_master.and.dbtime)                                                &
       call se_wall_time('    ... Time after pp_preprocessor expand_xyz')

! Check for valid primal cells and right-handedness

    if (.not.grid%cc) call raw_grid_checker(grid)

! Read mapbc (set info:periodic if periodicity is present)

    call read_mapbc(raw_grid_data,grid,flow_dir)

! Mirror grid if requested

    do_mirror = (mirror_x).or.(mirror_y).or.(mirror_z)
    if (do_mirror) then
      call mirror_grid_par_driver(flow_dir,grid,raw_grid_data)
      if (lmpi_master.and.dbtime)                                              &
         call se_wall_time('    ... Time after mirror grid ')
    end if

! For cell-centered, compute unique adj_ct and adjncy list

   if (grid%cc .or. ssdc_flag) call compute_adj_list2(grid)

! If make_tets or partition_lines, read bc.

    if (make_tets.or.partition_lines.or.periodic.or.multiblock) then
      call pparty_read_bc(flow_dir,raw_grid_data,grid)
      if ((make_tets.or.partition_lines).and.(.not.do_mirror)) then
         call lump_boundaries(raw_grid_data,grid)
      end if

      call process_grid_bc(grid, raw_grid_data%patch_lumping)
      call test_periodic(grid) ! info: periodic,periodic1,periodic2
      if (make_tets) mixed = mixed_present
      grid%idistfcn = 0
      if (need_distance_function(grid%bc)) grid%idistfcn = 1
      if (make_tets) then
         twod = .false.
         call split_elements(grid)
         if (lmpi_master) then
            write(*,*)
            write(*,'(a)')                                                     &
              ' Mixed-Element Mesh Converted Into Pure Tetrahedral Mesh'
         end if
! FIXME  mixed = .false. ! avoid interaction with solver's mixed variable
! that controls mixed-element edge-based discretization flavors.
! This results in preprocessing inefficiencies to be repaired at a later date.
      end if
    end if

! Partition grid

    if (.not.grid%cc .and. .not. ssdc_flag) call compute_adj_list(grid,flow_dir)

    if (lmpi_master.and.dbtime)                                                &
      call se_wall_time('    ... Time after dist grid and build adj ')

    if ( partitioning_in ) then

      if ( lmpi_master ) write(*,*) ' Reading partitioning from project.metisin'

      if (.not.grid%cc) then
         call my_metis_in(grid%project,grid%nnodesg,pp_nsize)
      else
         if (.not.partition_lines) then
            ! adj, adjncy is needed (not just metis data)
            call compute_c2c_cc_driver(grid)
            call my_metis_cc(grid)
           !call my_metis_in(grid%project,grid%ncellg,cc_csize)
         else
            if (lmpi_master) write(*,*)"cc partition lines not implemented."
            call lmpi_conditional_stop(1)
         end if
      end if
    elseif ( grid%igrid /= 1 .and. grid%igrid <= nsequence ) then
      if (.not.grid%cc) then
        call partition_in_sequence( projectp, dofgp, grid%project,         &
                                    grid%l2g, lmpi_nproc, pp_nsize )
      else
        call lmpi_conditional_stop(1,'cc nsequence:pparty_preprocessor')
      endif
    elseif ( .not.partitioning_in ) then
      if (.not.grid%cc .and. .not. ssdc_flag) then
         if (.not.partition_lines) then
           call my_metis(grid)
           call migrate_driver(grid)
         else
           call partition_dof_lines( grid, n_pts, n_lines, line, endline )
         end if
      else
         if (.not.partition_lines) then
            call compute_c2c_cc_driver(grid)
            call my_metis_cc(grid)
         else
            if (lmpi_master) write(*,*)"cc partition lines not implemented."
            call lmpi_conditional_stop(1)
         end if
         if ( ssdc_flag ) call convert_to_nc_partitioning(grid)
      end if
    end if

    !  check 2d points have the same partition vector
    if ((.false.).and.twod.and.partition_lines) then ! .false. for Hiro to test
    !if (twod.and.partition_lines) then
       call find_points_2d (grid, nl2g_on, l2g_on, l2g_off)
       call check_points_2d(0, grid, nl2g_on, l2g_on, l2g_off, i2d) !check/set
       if (i2d > 0)                                                            &
       call check_points_2d(1, grid, nl2g_on, l2g_on, l2g_off, i2d) !check/stop
    end if

    ! Output partitioning to file, if needed.

    if ( partitioning_in ) then

      if ( partitioning_out .and. lmpi_master ) write(*,*) &
      ' Skipping write of partitioning to project.metisout'

    else

    if ( grid%igrid <= nsequence - 1 ) then
      projectp = grid%project
      if (.not.grid%cc) then
        dofgp = grid%nnodesg
        call my_metis_out(grid%nnodesg,grid%project)
        call partition_out_global( grid%nnodesg, grid%project, &
                                   grid%l2g, lmpi_nproc, pp_nsize )
      else
        dofgp = grid%ncellg
        call my_metis_out_cc(grid%project)
        call partition_out_global( grid%ncellg, grid%project, &
                                   grid%cl2g, lmpi_nproc, pp_nsize )
      end if
    elseif ( partitioning_out ) then
      if (.not.grid%cc) then
        call my_metis_out(grid%nnodesg,grid%project)
      else
        call my_metis_out_cc(grid%project)
      end if
    end if

    endif

    if (lmpi_master.and.dbtime)                                                &
       call se_wall_time('    ... Time after ParMetis ')

!-------------------------------------------------------------------------------
!
! partition the cells, nodes, edges, and boundaries.
!
!-------------------------------------------------------------------------------

! partition the nodes and cells

    grid%partid = lmpi_id+1

    if (.not.grid%cc)then
       call node_cell_chopper(grid)
    else
       call node_cell_chopper_cc(grid)
    end if

    if (lmpi_master.and.dbtime)                                                &
      call se_wall_time('    ... Time after Node/Cell Partitioning ')

! Edge partitioning

    if (lmpi_master) write(*,*) '    ... Edge Partitioning ....'

    if (.not.grid%cc) then
       call edge_pointer_driver(grid)
    else
       call edge_pointer_driver_cc(grid)
    end if
    call test_edges(grid)

    if (lmpi_master.and.dbtime)                                                &
       call se_wall_time('    ... Time after Edge Partitioning ')

! Boundary partitioning

    if (lmpi_master) write(*,*) '    ... Boundary partitioning....'

    if (.not.(make_tets.or.partition_lines)) then
       call puns3d_bc(flow_dir,raw_grid_data,grid)
    end if

    if (lmpi_master.and.dbtime)                                                &
       call se_wall_time('    ... Time after Boundary Partitioning ')

! fill the bc arrays (f2ntb, f2nqb). In puns3d_io_c2n.f90; deallocate faceptr.

    if (.not.(mirror_x.or.mirror_y.or.mirror_z)) then
      if (.not.make_tets) then
        prevent_part_lines_memeory_leak : if (partition_lines) then
          do ib = 1, grid%nbound
            call deallocate_bc(grid%bc(ib))
          end do
          deallocate(grid%bc)
        end if prevent_part_lines_memeory_leak
        call process_grid_bc(grid, raw_grid_data%patch_lumping)
      else
         call gather_split_bc(grid)
      end if
    else
      call process_grid_bc_mirror_fill(grid)
    end if

    if (need_distance_function(grid%bc)) then
      grid%idistfcn = 1
    else
      grid%idistfcn = 0
    end if

    call delete_faceptr()

! Constructing list of nodes on boundary.   In pparty_puns3d.f90.
!  - Find element that surround each node
!  - Check volume-boundary connectivity
!  - Calculating cell volumes and metrics
!  - Calculating geometric information from the grid

    if (.not.grid%cc) then
       call pparty_puns3d_bc_sm(grid)
    else
       call pparty_puns3d_bc_cc_sm(grid)
    end if

! Set face bits, and statistics

    call bc_chopper(grid)

    if (lmpi_master.and.dbtime) call se_wall_time('    ... Time after Metrics ')

! Convert global numbering to local.  Up until now all node
! related numbering has been kept global to avoid the overhead of mapping.

    call pparty_convert_g2l(grid)

! Check the Euler numbers

    if (.not.grid%cc) then ! DANA CC
       call euler_number(grid,euler_number_bad) ! TBD CC
       if ( ( .not. raw_grid_data%ignore_euler_number ) .and.                  &
         euler_number_bad ) call lmpi_conditional_stop(1,'bad euler number')
    end if

! Color grid (Cuthill-McKee)

    if (lmpi_id == 0) write(*,*) '    ... Reordering for cache efficiency....'

    if (.not.grid%cc) then
       call pparty_color(grid)
    else
       call pparty_color_cc(grid)
       ! reorder ncell0 cells to be in order (CC NOTE)
       if (grid%nelem == 1) then
          j = size(grid%elem(1)%c2n,1)
          allocate(temp1(grid%ncell0))
          temp1 = grid%cl2g(1:grid%ncell0)
          allocate(temp2(j,grid%ncell0))
          temp2 = grid%elem(1)%c2n(1:j,1:grid%ncell0)
          allocate(temp_cl2g(grid%ncell0)); temp_cl2g = 0
          call heap_sort(grid%ncell0,grid%cl2g(1:grid%ncell0),temp_cl2g)
          do i = 1,grid%ncell0
             grid%elem(1)%c2n(1:j,i) = temp2(1:j,temp_cl2g(i))
             grid%cl2g(i) = temp1(temp_cl2g(i))
          end do
          ierr = 0
          do i = 1,grid%ncell0-1
             if (grid%cl2g(i) > grid%cl2g(i+1)) then
               write(*,*)"Error cl2g L0 order",i,i+1,grid%cl2g(i),grid%cl2g(i+1)
                ierr = 1
                exit
             end if
          end do
          deallocate(temp1)
          deallocate(temp2)
          deallocate(temp_cl2g)

          ncell1 = grid%ncell01 - grid%ncell0
          allocate(temp1(ncell1)); temp1 = grid%cl2g(grid%ncell0+1:grid%ncell01)
          allocate(temp2(j,ncell1))
          temp2 = grid%elem(1)%c2n(1:j,grid%ncell0+1:grid%ncell01)
          allocate(temp_cl2g(ncell1)); temp_cl2g = 0
          call heap_sort(ncell1,grid%cl2g(grid%ncell0+1:grid%ncell01),temp_cl2g)
          do i = 1,ncell1
             grid%elem(1)%c2n(1:j,grid%ncell0+i) = temp2(1:j,temp_cl2g(i))
          end do
          do i = grid%ncell0+1,grid%ncell01-1
             if (grid%cl2g(i) > grid%cl2g(i+1)) then
               write(*,*)"Error cl2g L1 order",i,i+1,grid%cl2g(i),grid%cl2g(i+1)
                ierr = 1
                exit
             end if
          end do
          deallocate(temp2)
          deallocate(temp_cl2g)
          call lmpi_conditional_stop(ierr,'Error cl2g')
       end if
    end if

    if (lmpi_master.and.dbtime) call se_wall_time('    ... Time after CM ')

! Sort the edges in 2D cases

    if ( twod .and. .not. grid%cc ) then
      call sort_edges_2d(grid)
    else
      grid%nedgeloc_2d = 0
    endif

! Construct MPI xfer arrays

    if (lmpi_master.and.dbtime)                                                &
       write(*,*) '    ... Constructing MPI passing arrays....'

    if (.not.grid%cc) then
       call party_lmpi_setup_mpi_sm(grid)
    else
       call party_lmpi_setup_mpi_cc_sm(grid)
    end if

!   output some info about the global grid to the part info file

    call write_part_info(grid)

! Determine load balance

    if ((pp_cmd_stats == 1).or.(pp_cmd_stats == 3)) then
       call pparty_write_stats(grid)
       call pparty_write_data_stats(grid)
    end if

! If CC, then populate the CC-specific variables.

    if (grid%cc) call compute_cc_specific(grid)

    if (lmpi_master) call se_wall_time("    ... Time after preprocess ")

! If requested, write partition files

    if (pp_cmd_outformat.or.raw_grid_data%write_part_files) then

      if (lmpi_master) then
        if (pp_cmd_outformat) then
          write(*,*)' Writing grid partition to disk... (ASCII) ',grid%cc
        else
          write(*,*)' Writing grid partition to disk... ',grid%cc
        end if
      end if

      call writegrid_sm(grid, pp_cmd_outformat)

      if (dbtime.and.(lmpi_id == lmpi_nproc-1))                                &
         call se_wall_time('    ... Time after write files ')

      if (raw_grid_data%write_part_files) then
        if (lmpi_master) write(*,*)"Part files written ... stopping."
        call lmpi_finalize
        stop
      end if

    end if

    if ( partition_lines ) then
      deallocate(endline)
      deallocate(   line)
    endif

    if (lmpi_master.and.dbtime)                                                &
       call se_wall_time("    ... Time at end of pp_preprocessor")

  end subroutine pparty_preprocess

!============================= HANGING_NODE_CHECK ============================80
!
! Looks for any nodes in the raw grid that are not connected to anything
!
!=============================================================================80
  subroutine hanging_node_check(nhead,ntail,grid)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_conditional_stop
    use kinddefs,   only : system_i1

    integer,         intent(in) :: nhead, ntail
    type(grid_type), intent(in) :: grid

    integer :: i, ielem, icell, inode

    integer(system_i1), dimension(:), allocatable :: tag

  continue

    allocate(tag(nhead:ntail)); tag = 0

    do ielem = 1, grid%nelem
      do icell = 1, grid%elem(ielem)%ncell
        do inode = 1, grid%elem(ielem)%node_per_cell
           i = grid%elem(ielem)%c2n(inode,icell)
           if ((i >= nhead).and.(i <= ntail)) tag(i) = 1
        end do
      end do
    end do

    do i = nhead,ntail
      if (tag(i) == 0) then
        write(*,*) 'Hanging node ', i, &
          ' at (',grid%x(i),',',grid%y(i),',',grid%z(i),')'
        call lmpi_conditional_stop(1,'Stopping due to hanging node.')
      endif
    end do
    call lmpi_conditional_stop(0,'Stopping due to hanging node.')
    deallocate(tag)

  end subroutine hanging_node_check

!=============================== RAW_GRID_CONSISTENCY_CHECKS =================80
!
! error checking for inputs related to the raw grid after all inputs
! (CLO, namelists, etc.) are read.
!
!=============================================================================80

  subroutine raw_grid_consistency_checks(raw_grid_data)

    use twod_util,  only : nplanes, yspan, y_coplanar_tol
    use lmpi,       only : lmpi_conditional_stop, lmpi_master
    use grid_types, only : raw_grid_data_type
    use kinddefs,   only : dp

    type(raw_grid_data_type), intent(in) :: raw_grid_data

    integer :: ierr

  continue

    ierr = 0
    if (lmpi_master) then

! 2D stuff

       if ( trim(raw_grid_data%grid_format) /= 'fun2d' ) then
         if (nplanes /= 2) then
           write(*,*) 'Specified nplanes can only be used with FUN2D grids.'
           ierr = 1
         end if
         if (abs(yspan - 1.0_dp) > y_coplanar_tol) then
           write(*,*) 'Specified yspan can only be used with FUN2D grids.'
           ierr = 1
         end if
       endif

    end if
    call lmpi_conditional_stop(ierr)

  end subroutine raw_grid_consistency_checks

!=============================== PPARTY_READ_GRID ============================80
!
! pparty read_grid
!
! Note: raw_grid_error_checks in LibF90/read_flow_input.f90 precludes
!       invalid data/grid combinations; so minimal error checking here.
!
!=============================================================================80

  subroutine pparty_read_grid(raw_grid_data,grid,flow_dir)

    use grid_types,    only : grid_type, raw_grid_data_type
    use puns3d_io_c2n, only : puns3d_read_fast_c2n,                            &
                              puns3d_read_vgrid_c2n,                           &
                              puns3d_read_vgrid_c2n_sio,                       &
                              puns3d_read_fun2d_c2n,                           &
                              puns3d_read_fieldview_c2n,                       &
                              puns3d_read_felisa_c2n,                          &
                              puns3d_read_aflr3_c2n,                           &
                              puns3d_read_aflr3_c2n_sio
    use lmpi,          only : lmpi_conditional_stop, lmpi_master

    character(*), intent(in) :: flow_dir

    type(raw_grid_data_type), intent(in)    :: raw_grid_data
    type(grid_type),          intent(inout) :: grid

  continue

    select case(trim(raw_grid_data%grid_format))
    case('fast')

      call puns3d_read_fast_c2n(flow_dir,grid,raw_grid_data%data_format)

    case('vgrid')

      if ( trim(raw_grid_data%data_format) == 'unformatted' ) then
        call puns3d_read_vgrid_c2n(flow_dir,grid)
      else if (trim(raw_grid_data%data_format) == 'stream' ) then
        call puns3d_read_vgrid_c2n_sio(flow_dir,grid)
      endif

    case('aflr3')

      if ((trim(raw_grid_data%data_format) == 'stream' ).or.                   &
          (trim(raw_grid_data%data_format) == 'stream64' )) then
         call puns3d_read_aflr3_c2n_sio(flow_dir,grid,                         &
                raw_grid_data%twod_mode, raw_grid_data%data_format)
      else
         call puns3d_read_aflr3_c2n(flow_dir,grid,raw_grid_data%data_format,   &
                                    raw_grid_data%twod_mode)
      end if

    case('fieldview')

      call puns3d_read_fieldview_c2n(1,flow_dir,grid,raw_grid_data)

    case('fun2d')

        call puns3d_read_fun2d_c2n(flow_dir,grid)

    case('felisa')

        call puns3d_read_felisa_c2n(flow_dir, grid)

    case default

      if ( lmpi_master ) write(*,*) 'Unsupported grid_format.'
      call lmpi_conditional_stop(1)

    end select
    call lmpi_conditional_stop(0)

  end subroutine pparty_read_grid


!=============================== PPARTY_READ_BC ==============================80
!
! pparty read_bc
!
!=============================================================================80

  subroutine pparty_read_bc(flow_dir,raw_grid_data,grid)

    use grid_types,    only : grid_type, raw_grid_data_type
    use puns3d_io_c2n, only : puns3d_read_fast_bc,                            &
                              puns3d_read_vgrid_bc,                           &
                              puns3d_read_felisa_bc,                          &
                              puns3d_read_aflr3_bc,                           &
                              puns3d_read_fieldview_c2n
    use lmpi,          only : lmpi_conditional_stop

    character(*),             intent(in)    :: flow_dir
    type(raw_grid_data_type), intent(in)    :: raw_grid_data
    type(grid_type),          intent(inout) :: grid

    continue

       if (trim(raw_grid_data%grid_format) == 'fast') then ! FAST

          call puns3d_read_fast_bc(flow_dir,grid,raw_grid_data%data_format)

       else if (trim(raw_grid_data%grid_format) == 'vgrid') then ! VGRID

          call puns3d_read_vgrid_bc(flow_dir,grid)

       else if (trim(raw_grid_data%grid_format) == 'felisa') then ! FELISA

          call puns3d_read_felisa_bc(flow_dir,grid)

       else if (trim(raw_grid_data%grid_format) == 'fieldview') then ! FIELDVR

           call puns3d_read_fieldview_c2n(2,flow_dir,grid,raw_grid_data)

       else if (trim(raw_grid_data%grid_format) == 'aflr3') then ! AFLR3

          call puns3d_read_aflr3_bc(                                           &
               flow_dir,raw_grid_data%data_format,grid)

       else if (trim(raw_grid_data%grid_format) == 'fun2d') then

          ! already done in puns3d_read_fun2d_c2n

       else

          write (*,*) 'Error: format not supported in pparty_read_bc.'
          call lmpi_conditional_stop(1)

       end if

   end subroutine pparty_read_bc

!=============================== TEST_PERIODIC ===============================80
!
! Determine if this is a periodic case
! if so, set flag and store plane numbers (info: periodic, periodic1,periodic2
!
!=============================================================================80

  subroutine test_periodic(grid)

    use grid_types, only : grid_type
    use periodics,  only : periodic, periodic1a, periodic1b, periodic2a,       &
                           periodic2b, periodic3a, periodic3b
    use bc_names,   only : periodicity1, periodicity2, periodicity3
    use lmpi,       only : lmpi_conditional_stop, lmpi_master

    type(grid_type), intent(in) :: grid

    integer :: ib

  continue

! periodicity1 instances

    do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc == periodicity1 ) then
        periodic = .true.
        if ( periodic1a > 0 ) then
          if ( periodic1b > 0 ) call lmpi_conditional_stop(1,                  &
               "Error: More than 2 periodicity1 planes found in grid...")
          periodic1b = ib
        else
          periodic1a = ib
        endif
      endif
    end do

    if ( periodic1a > 0 .and. periodic1b == 0 ) then
      call lmpi_conditional_stop(1,"Error finding pair(s) of periodic planes.")
    endif

! periodicity2 instances

    do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc == periodicity2 ) then
        periodic = .true.
        if ( periodic2a > 0 ) then
          if ( periodic2b > 0 ) call lmpi_conditional_stop(1,                  &
               "Error: More than 2 periodicity2 planes found in grid...")
          periodic2b = ib
        else
          periodic2a = ib
        endif
      endif
    end do

    if ( periodic2a > 0 .and. periodic2b == 0 ) then
      call lmpi_conditional_stop(1,"Error finding pair(s) of periodic planes.")
    endif

! periodicity3 instances

    do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc == periodicity3 ) then
        periodic = .true.
        if ( periodic3a > 0 ) then
          if ( periodic3b > 0 ) call lmpi_conditional_stop(1,                  &
               "Error: More than 2 periodicity3 planes found in grid...")
          periodic3b = ib
        else
          periodic3a = ib
        endif
      endif
    end do

    if ( periodic3a > 0 .and. periodic3b == 0 ) then
      call lmpi_conditional_stop(1,"Error finding pair(s) of periodic planes.")
    endif

    if (lmpi_master.and.periodic) write(*,*)"    ... Periodicity detected.."

   end subroutine test_periodic


!=============================== PPARTY_SETUP_STUFF ==========================80
!
!  set up stuff that used to be done in readme
!
!=============================================================================80
  subroutine pparty_setup_stuff(grid)

    use nml_grid_motion,      only : grid_motion_attribute
    use nml_grid_transform,   only : static_grid_transform
    use lmpi,                 only : lmpi_master
    use element_defs,         only : max_node_per_cell, max_face_per_cell,     &
                                     max_edge_per_cell
    use info_depr,            only : cc_primal
    use grid_motion_helpers,  only : need_grid_velocity
    use bc_names,             only : backwards_compatible_bc
    use allocations,          only : my_alloc_ptr
    use grid_types,           only : grid_type
    use io,                   only : set_bc_dirichlet
    use io_helpers,           only : set_kappa_umuscl
    use exact_defs,           only : dirichlet_bc_override
    use nml_mdo_surface_data, only : aero_loads_use_initial_coords,            &
                                     massoud_use_initial_coords

    type(grid_type), intent(inout) :: grid

    integer :: ielem, k, node1, node2, nodea, nodeb, i
    integer :: level = 1

    logical, save :: print_grid_motion = .true. ! prevent multiple party writes

  continue

    if (grid%cc) then
       call my_alloc_ptr(grid%slen,      grid%ncell01)
       call my_alloc_ptr(grid%iflagslen, grid%ncell01)
       call my_alloc_ptr(grid%des_slen,  grid%ncell01)
       call my_alloc_ptr(grid%vol,       grid%ncell01)
       call my_alloc_ptr(grid%xn,        grid%nedgeloc)
       call my_alloc_ptr(grid%yn,        grid%nedgeloc)
       call my_alloc_ptr(grid%zn,        grid%nedgeloc)
       grid%grid_motion = grid_motion_attribute
       ! if (lmpi_master) write(*,*)"TBD pparty_setup_stuff INTEGRATE"
    end if

! Check the edges and see if any of them need to be reversed

   do k = 1, grid%nedgeloc
      node1 = grid%eptr(1,k)
      node2 = grid%eptr(2,k)

      if(node1 > node2)then

        nodea = node1
        nodeb = node2

        grid%eptr(1,k) = nodeb
        grid%eptr(2,k) = nodea

        if (associated(grid%xn)) then
           grid%xn(k) = -grid%xn(k)
           grid%yn(k) = -grid%yn(k)
           grid%zn(k) = -grid%zn(k)
        end if

      end if
    enddo

    if (grid%cc) return ! look at rest of routine and integrate properly

    grid%grid_motion = grid_motion_attribute

    if (lmpi_master .and. trim(grid%grid_motion) /= 'static') then
      if (print_grid_motion) then
        write(*,'(a)') ' Grid Motion Type '
        write(*,'(3x,a)') trim(grid%grid_motion)
        write(*,*)
      end if
    end if
    print_grid_motion = .false.

!   allocate space for various arrays in the grid type

    if (.not.grid%cc) then
       call pparty_size_grid(.false., grid, ghost_level_arg = level)
       do ielem = 1,grid%nelem
         call pparty_size_elem(grid%elem(ielem),.false.,ghost_level_arg=level)
       end do
    else
       call pparty_size_grid(.true., grid, ghost_level_arg = 3)
       do ielem = 1,grid%nelem
          call pparty_size_elem(grid%elem(ielem),.true., ghost_level_arg=3)
       end do
    end if

!   get some max dimensions over all element types in the mesh

    max_node_per_cell = 0
    max_face_per_cell = 0
    max_edge_per_cell = 0

    do ielem=1,grid%nelem
      max_node_per_cell = max(grid%elem(ielem)%node_per_cell, max_node_per_cell)
      max_face_per_cell = max(grid%elem(ielem)%face_per_cell, max_face_per_cell)
      max_edge_per_cell = max(grid%elem(ielem)%edge_per_cell, max_edge_per_cell)
    end do

! boundary stuff

    boundary_loop : do i = 1, grid%nbound

      call backwards_compatible_bc( grid%bc(i)%ibc )

      call my_alloc_ptr( grid%bc(i)%bxn,       max0(grid%bc(i)%nbnode,1) )
      call my_alloc_ptr( grid%bc(i)%byn,       max0(grid%bc(i)%nbnode,1) )
      call my_alloc_ptr( grid%bc(i)%bzn,       max0(grid%bc(i)%nbnode,1) )

      if (need_grid_velocity) then
        call my_alloc_ptr( grid%bc(i)%bdxdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdydt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, max0(grid%bc(i)%nbnode,1) )
      else
        call my_alloc_ptr( grid%bc(i)%bdxdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdydt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, 1 )
      end if

      if ( cc_primal ) then
        call my_alloc_ptr(grid%bc(i)%qcell_ptr_t, max0(grid%bc(i)%nbfacet,1))
        call my_alloc_ptr(grid%bc(i)%qcell_ptr_q, max0(grid%bc(i)%nbfaceq,1))
      else
        call my_alloc_ptr(grid%bc(i)%qcell_ptr_t, 1)
        call my_alloc_ptr(grid%bc(i)%qcell_ptr_q, 1)
      endif

      if ( cc_primal ) then
        call my_alloc_ptr(grid%bc(i)%slen_wall,   1)
        call my_alloc_ptr(grid%bc(i)%node_nearest,1)
      else
        call my_alloc_ptr(grid%bc(i)%slen_wall,    max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr(grid%bc(i)%node_nearest, max0(grid%bc(i)%nbnode,1) )
      endif

    end do boundary_loop

    if ( dirichlet_bc_override )                                               &
    call set_bc_dirichlet( grid%nbound, grid%bc(:)%ibc )

! Store off grid in inertial (t=0) reference frame
! FIXME: also in io::readme

    if (trim(grid%grid_motion) /= 'static' .or. static_grid_transform .or.     &
        aero_loads_use_initial_coords .or. massoud_use_initial_coords) then
      grid%xat0(:) = grid%x(:)
      grid%yat0(:) = grid%y(:)
      grid%zat0(:) = grid%z(:)
    end if

    call set_kappa_umuscl(grid%elem)

  end subroutine pparty_setup_stuff

!=============================== PPARTY_SIZE_GRID ============================80
!
! Allocates memory for the arrays in the first layer of the grid derived type
! Note: There are exceptions - see node_pairs_2d for instance.
!
!=============================================================================80
  subroutine pparty_size_grid(cc, grid, ghost_level_arg)

    use info_depr,            only : mixed, wls_inv_terms, twod, ebv_tets
    use nml_nonlinear_solves, only : itime
    use grid_motion_helpers,  only : need_grid_velocity
    use nml_grid_transform,   only : static_grid_transform
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use code_status,          only : code_id, adjoint_code_id, rad_code_id
    use nml_overset_data,     only : overset_flag
    use pundit,               only : pundit_flag
    use lsq_defs,             only : nc_mapped_lsq, type_fit
    use grid_types,           only : grid_type
    use string_utils,         only : sub_string
    use nml_mdo_surface_data, only : aero_loads_use_initial_coords,            &
                                     massoud_use_initial_coords

    logical,                   intent(in)    :: cc
    type(grid_type),           intent(inout) :: grid
    integer,         optional, intent(in)    :: ghost_level_arg

    integer :: level

  continue

    if (present(ghost_level_arg)) then
      level = ghost_level_arg
    else
      level = 1
    endif

    read_depth_level : select case (level)
    case (1) read_depth_level

      call my_realloc_ptr(grid%x,grid%nnodes01)
      call my_realloc_ptr(grid%y,grid%nnodes01)
      call my_realloc_ptr(grid%z,grid%nnodes01)
      call my_alloc_ptr(grid%symmetry,  grid%nnodes01)
      call my_alloc_ptr(grid%jag,       grid%nnodes01)
      call my_alloc_ptr(grid%vol,       grid%nnodes01)

      if ( ebv_tets ) then
        call my_alloc_ptr(grid%weight,10,grid%nedgeloc)
      else
        call my_alloc_ptr(grid%weight,1,1)
      endif

      if ( cc ) then
        call my_alloc_ptr(grid%slen,      grid%ncell01)
        call my_alloc_ptr(grid%iflagslen, grid%ncell01)
        call my_alloc_ptr(grid%des_slen,  grid%ncell01)
      else
        call my_alloc_ptr(grid%slen,      grid%nnodes01)
        call my_alloc_ptr(grid%iflagslen, grid%nnodes01)
        call my_alloc_ptr(grid%des_slen,  grid%nnodes01)
      endif

      call my_realloc_ptr(grid%eptr,2,grid%nedge)
      call my_alloc_ptr(grid%xn,        grid%nedgeloc)
      call my_alloc_ptr(grid%yn,        grid%nedgeloc)
      call my_alloc_ptr(grid%zn,        grid%nedgeloc)
      call my_alloc_ptr(grid%ra,        grid%nedgeloc)

      need_timedata_1 : if (itime /= 0) then

!       if grid is static, i.e. it is not moving, we don't need
!       any extra mesh data; the exceptions are if the static mesh is in
!       a noninertial reference frame, or we are simulating gusts - then
!       we need to store the equivalent mesh point velocities and dual face
!       speeds; also need to store original grid data if using static transforms

        static_or_moving_1 : if (trim(grid%grid_motion) == 'static') then

          if (need_grid_velocity) then
            call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
            call my_alloc_ptr(grid%dydt,      grid%nnodes01)
            call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
            call my_alloc_ptr(grid%facespeed, grid%nedgeloc)
            call my_alloc_ptr(grid%res_gcl,1, grid%nnodes01)
          else
            call my_alloc_ptr(grid%dxdt,      1)
            call my_alloc_ptr(grid%dydt,      1)
            call my_alloc_ptr(grid%dzdt,      1)
            call my_alloc_ptr(grid%facespeed, 1)
            call my_alloc_ptr(grid%res_gcl,1, 1)
          end if

          if (static_grid_transform .or. aero_loads_use_initial_coords .or.    &
                                     massoud_use_initial_coords) then
            call my_alloc_ptr(grid%xat0,      grid%nnodes01)
            call my_alloc_ptr(grid%yat0,      grid%nnodes01)
            call my_alloc_ptr(grid%zat0,      grid%nnodes01)
          else
            call my_alloc_ptr(grid%xat0,      1)
            call my_alloc_ptr(grid%yat0,      1)
            call my_alloc_ptr(grid%zat0,      1)
          end if

          call my_alloc_ptr(grid%xatn,      1)
          call my_alloc_ptr(grid%yatn,      1)
          call my_alloc_ptr(grid%zatn,      1)
          call my_alloc_ptr(grid%xatn1,     1)
          call my_alloc_ptr(grid%yatn1,     1)
          call my_alloc_ptr(grid%zatn1,     1)
          call my_alloc_ptr(grid%xatn2,     1)
          call my_alloc_ptr(grid%yatn2,     1)
          call my_alloc_ptr(grid%zatn2,     1)
          call my_alloc_ptr(grid%xatn3,     1)
          call my_alloc_ptr(grid%yatn3,     1)
          call my_alloc_ptr(grid%zatn3,     1)
          call my_alloc_ptr(grid%xatn4,     1)
          call my_alloc_ptr(grid%yatn4,     1)
          call my_alloc_ptr(grid%zatn4,     1)
          call my_alloc_ptr(grid%volatn,    1)
          call my_alloc_ptr(grid%volatn1,   1)
          call my_alloc_ptr(grid%volatn2,   1)
          call my_alloc_ptr(grid%volatn3,   1)
          call my_alloc_ptr(grid%volatn4,   1)
          call my_alloc_ptr(grid%volatnp1,  1)
          call my_alloc_ptr(grid%volatnp2,  1)

        else static_or_moving_1

!         grid is moving - need to store grid point velocities,
!         dual face speeds, and x,y,z locations at previous
!         time steps, regardless of whether grid motion is
!         rigid or deforming

          call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
          call my_alloc_ptr(grid%dydt,      grid%nnodes01)
          call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
          call my_alloc_ptr(grid%facespeed, grid%nedgeloc)
          call my_alloc_ptr(grid%xat0,      grid%nnodes01)
          call my_alloc_ptr(grid%yat0,      grid%nnodes01)
          call my_alloc_ptr(grid%zat0,      grid%nnodes01)
          call my_alloc_ptr(grid%xatn,      grid%nnodes01)
          call my_alloc_ptr(grid%yatn,      grid%nnodes01)
          call my_alloc_ptr(grid%zatn,      grid%nnodes01)
          call my_alloc_ptr(grid%xatn1,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn1,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn1,     grid%nnodes01)
          call my_alloc_ptr(grid%xatn2,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn2,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn2,     grid%nnodes01)
          call my_alloc_ptr(grid%xatn3,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn3,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn3,     grid%nnodes01)
          call my_alloc_ptr(grid%xatn4,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn4,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn4,     grid%nnodes01)
          call my_alloc_ptr(grid%res_gcl,1, grid%nnodes01)
          call my_alloc_ptr(grid%volatn,    grid%nnodes01)
          call my_alloc_ptr(grid%volatn1,   grid%nnodes01)
          call my_alloc_ptr(grid%volatn2,   grid%nnodes01)
          call my_alloc_ptr(grid%volatn3,   grid%nnodes01)
          call my_alloc_ptr(grid%volatn4,   grid%nnodes01)
          if ( code_id == adjoint_code_id ) then
            call my_alloc_ptr(grid%volatnp1,  grid%nnodes01)
            call my_alloc_ptr(grid%volatnp2,  grid%nnodes01)
            call my_alloc_ptr(grid%dxdtatn1,  grid%nnodes01)
            call my_alloc_ptr(grid%dydtatn1,  grid%nnodes01)
            call my_alloc_ptr(grid%dzdtatn1,  grid%nnodes01)
            call my_alloc_ptr(grid%dxdtatn2,  grid%nnodes01)
            call my_alloc_ptr(grid%dydtatn2,  grid%nnodes01)
            call my_alloc_ptr(grid%dzdtatn2,  grid%nnodes01)
          else
            call my_alloc_ptr(grid%volatnp1,  1)
            call my_alloc_ptr(grid%volatnp2,  1)
            call my_alloc_ptr(grid%dxdtatn1,  1)
            call my_alloc_ptr(grid%dydtatn1,  1)
            call my_alloc_ptr(grid%dzdtatn1,  1)
            call my_alloc_ptr(grid%dxdtatn2,  1)
            call my_alloc_ptr(grid%dydtatn2,  1)
            call my_alloc_ptr(grid%dzdtatn2,  1)
          endif

        end if static_or_moving_1

      else need_timedata_1

!       not time accurate - don't need extra mesh data; the exceptions: 1) if
!       the mesh is in a noninertial reference frame - then we need to store the
!       equivalent mesh point velocities and dual face speeds; 2) if a static
!       aeroelastic case or static grid transform case we need to store the
!       intial mesh

        if (need_grid_velocity) then
          call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
          call my_alloc_ptr(grid%dydt,      grid%nnodes01)
          call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
          call my_alloc_ptr(grid%facespeed, grid%nedgeloc)
          call my_alloc_ptr(grid%res_gcl,1, grid%nnodes01)
        else
          call my_alloc_ptr(grid%dxdt,      1)
          call my_alloc_ptr(grid%dydt,      1)
          call my_alloc_ptr(grid%dzdt,      1)
          call my_alloc_ptr(grid%facespeed, 1)
          call my_alloc_ptr(grid%res_gcl,1, 1)
        end if

        call my_alloc_ptr(grid%volatn,    1)
        call my_alloc_ptr(grid%volatn1,   1)
        call my_alloc_ptr(grid%volatn2,   1)
        call my_alloc_ptr(grid%volatn3,   1)
        call my_alloc_ptr(grid%volatn4,   1)
        call my_alloc_ptr(grid%volatnp1,  1)
        call my_alloc_ptr(grid%volatnp2,  1)

        if ( sub_string(grid%grid_motion,'deform') .or.                        &
             static_grid_transform                 .or.                        &
             aero_loads_use_initial_coords         .or.                        &
             massoud_use_initial_coords )          then
          call my_alloc_ptr(grid%xat0,      grid%nnodes01)
          call my_alloc_ptr(grid%yat0,      grid%nnodes01)
          call my_alloc_ptr(grid%zat0,      grid%nnodes01)
        else
          call my_alloc_ptr(grid%xat0,      1)
          call my_alloc_ptr(grid%yat0,      1)
          call my_alloc_ptr(grid%zat0,      1)
        end if

        call my_alloc_ptr(grid%xatn,      1)
        call my_alloc_ptr(grid%yatn,      1)
        call my_alloc_ptr(grid%zatn,      1)
        call my_alloc_ptr(grid%xatn1,     1)
        call my_alloc_ptr(grid%yatn1,     1)
        call my_alloc_ptr(grid%zatn1,     1)
        call my_alloc_ptr(grid%xatn2,     1)
        call my_alloc_ptr(grid%yatn2,     1)
        call my_alloc_ptr(grid%zatn2,     1)
        call my_alloc_ptr(grid%xatn3,     1)
        call my_alloc_ptr(grid%yatn3,     1)
        call my_alloc_ptr(grid%zatn3,     1)
        call my_alloc_ptr(grid%xatn4,     1)
        call my_alloc_ptr(grid%yatn4,     1)
        call my_alloc_ptr(grid%zatn4,     1)

      end if need_timedata_1

      if  ( code_id == rad_code_id ) then

        call my_alloc_ptr(grid%r11,       grid%nnodes01)
        call my_alloc_ptr(grid%r12,       grid%nnodes01)
        call my_alloc_ptr(grid%r13,       grid%nnodes01)
        call my_alloc_ptr(grid%r22,       grid%nnodes01)
        call my_alloc_ptr(grid%r23,       grid%nnodes01)
        call my_alloc_ptr(grid%r33,       grid%nnodes01)
        call my_alloc_ptr(grid%rlsq, 1,1,1)
        call my_alloc_ptr(grid%rlsq_ia,  1)
        call my_alloc_ptr(grid%rlsq_ja,  1)

        if(mixed) then
          call my_alloc_ptr(grid%wr11,      grid%nnodes01)
          call my_alloc_ptr(grid%wr12,      grid%nnodes01)
          call my_alloc_ptr(grid%wr13,      grid%nnodes01)
          call my_alloc_ptr(grid%wr22,      grid%nnodes01)
          call my_alloc_ptr(grid%wr23,      grid%nnodes01)
          call my_alloc_ptr(grid%wr33,      grid%nnodes01)
        else
          call my_alloc_ptr(grid%wr11,      1)
          call my_alloc_ptr(grid%wr12,      1)
          call my_alloc_ptr(grid%wr13,      1)
          call my_alloc_ptr(grid%wr22,      1)
          call my_alloc_ptr(grid%wr23,      1)
          call my_alloc_ptr(grid%wr33,      1)
        endif

      else

        if ( cc ) then
          call my_alloc_ptr(grid%r11,       1)
          call my_alloc_ptr(grid%r12,       1)
          call my_alloc_ptr(grid%r13,       1)
          call my_alloc_ptr(grid%r22,       1)
          call my_alloc_ptr(grid%r23,       1)
          call my_alloc_ptr(grid%r33,       1)
          call my_alloc_ptr(grid%rlsq,  3,3,grid%ncell0)
          call my_alloc_ptr(grid%rlsq_ia, grid%ncell01+1)
          call my_alloc_ptr(grid%rlsq_ja, grid%ncell_augmentors)
        else
          call my_alloc_ptr(grid%r11,       grid%nnodes0)
          call my_alloc_ptr(grid%r12,       grid%nnodes0)
          call my_alloc_ptr(grid%r13,       grid%nnodes0)
          call my_alloc_ptr(grid%r22,       grid%nnodes0)
          call my_alloc_ptr(grid%r23,       grid%nnodes0)
          call my_alloc_ptr(grid%r33,       grid%nnodes0)
          call my_alloc_ptr(grid%rlsq,      1,1,1)
          call my_alloc_ptr(grid%rlsq_ia,   1)
          call my_alloc_ptr(grid%rlsq_ja,   1)
        endif

        if(mixed .or. wls_inv_terms) then
          call my_alloc_ptr(grid%wr11,      grid%nnodes0)
          call my_alloc_ptr(grid%wr12,      grid%nnodes0)
          call my_alloc_ptr(grid%wr13,      grid%nnodes0)
          call my_alloc_ptr(grid%wr22,      grid%nnodes0)
          call my_alloc_ptr(grid%wr23,      grid%nnodes0)
          call my_alloc_ptr(grid%wr33,      grid%nnodes0)
        else
          call my_alloc_ptr(grid%wr11,      1)
          call my_alloc_ptr(grid%wr12,      1)
          call my_alloc_ptr(grid%wr13,      1)
          call my_alloc_ptr(grid%wr22,      1)
          call my_alloc_ptr(grid%wr23,      1)
          call my_alloc_ptr(grid%wr33,      1)
        endif

      endif

      if ( cc ) then
        call my_alloc_ptr(grid%fptr,      6,grid%nface)
        call my_alloc_ptr(grid%cell_vol,  grid%ncell01)
        call my_alloc_ptr(grid%area_face, grid%nface)
        call my_alloc_ptr(grid%xn_face,   grid%nface)
        call my_alloc_ptr(grid%yn_face,   grid%nface)
        call my_alloc_ptr(grid%zn_face,   grid%nface)
        call my_alloc_ptr(grid%x_face,    grid%nface)
        call my_alloc_ptr(grid%y_face,    grid%nface)
        call my_alloc_ptr(grid%z_face,    grid%nface)
        call my_alloc_ptr(grid%fl2g,      grid%nface)
        call my_alloc_ptr(grid%xc,        grid%ncell01)
        call my_alloc_ptr(grid%yc,        grid%ncell01)
        call my_alloc_ptr(grid%zc,        grid%ncell01)
        call my_alloc_ptr(grid%flsq_ia,   grid%nface+1)
        call my_alloc_ptr(grid%flsq_ja,   grid%nface_augmentors)
        call my_alloc_ptr(grid%flsq_lu, 4,4, grid%nface)
        call my_alloc_ptr(grid%flsq_flag_rhs,  grid%nface)
        call my_alloc_ptr(grid%flsq_flag_lhs,  grid%nface)
      else
        call my_alloc_ptr(grid%fptr,    6,1)
        call my_alloc_ptr(grid%cell_vol,  1)
        call my_alloc_ptr(grid%area_face, 1)
        call my_alloc_ptr(grid%xn_face,   1)
        call my_alloc_ptr(grid%yn_face,   1)
        call my_alloc_ptr(grid%zn_face,   1)
        call my_alloc_ptr(grid%x_face,    1)
        call my_alloc_ptr(grid%y_face,    1)
        call my_alloc_ptr(grid%z_face,    1)
        call my_alloc_ptr(grid%fl2g,      1)
        call my_alloc_ptr(grid%xc,        1)
        call my_alloc_ptr(grid%yc,        1)
        call my_alloc_ptr(grid%zc,        1)
        call my_alloc_ptr(grid%flsq_ia,   1)
        call my_alloc_ptr(grid%flsq_ja,   1)
        call my_alloc_ptr(grid%flsq_lu,   4, 4, 1)
        call my_alloc_ptr(grid%flsq_flag_rhs, 1)
        call my_alloc_ptr(grid%flsq_flag_lhs, 1)
      endif

      if (overset_flag .or. pundit_flag) then
        call my_alloc_ptr(grid%iblank, grid%nnodes01)
        call my_alloc_ptr(grid%imesh,  grid%nnodes01)
      else
        call my_alloc_ptr(grid%iblank, 1)
        call my_alloc_ptr(grid%imesh,  1)
      end if

    case (3) read_depth_level

      call my_alloc_ptr(grid%x,         grid%nnodes01)
      call my_alloc_ptr(grid%y,         grid%nnodes01)
      call my_alloc_ptr(grid%z,         grid%nnodes01)
      call my_alloc_ptr(grid%symmetry,  grid%nnodes01)
      call my_alloc_ptr(grid%jag,       grid%nnodes01)
      call my_alloc_ptr(grid%vol,       grid%nnodes01)
      call my_alloc_ptr(grid%l2g,       grid%nnodes01)

      if ( ebv_tets ) then
        call my_alloc_ptr(grid%weight,10,grid%nedgeloc)
      else
        call my_alloc_ptr(grid%weight,1,1)
      endif

      if ( cc ) then
        call my_alloc_ptr(grid%slen,      grid%ncell01)
        call my_alloc_ptr(grid%iflagslen, grid%ncell01)
        call my_alloc_ptr(grid%des_slen,  grid%ncell01)
      else
        call my_alloc_ptr(grid%slen,      grid%nnodes01)
        call my_alloc_ptr(grid%iflagslen, grid%nnodes01)
        call my_alloc_ptr(grid%des_slen,  grid%nnodes01)
      endif

      call my_alloc_ptr(grid%eptr,      2,grid%nedge)

      call my_alloc_ptr(grid%xn,        grid%nedge)
      call my_alloc_ptr(grid%yn,        grid%nedge)
      call my_alloc_ptr(grid%zn,        grid%nedge)
      call my_alloc_ptr(grid%ra,        grid%nedge)

      need_timedata_3 : if (itime /= 0) then

!       if grid is static, i.e. it is not moving, we don't need
!       any extra mesh data; the exceptions are if the static mesh is in
!       a noninertial reference frame, or we are doing gust simulations - then
!       we need to store the equivalent mesh point velocities and dual face
!       speeds; also need to store original grid data if using static transforms

        static_or_moving_3 : if (trim(grid%grid_motion) == 'static') then

          if (need_grid_velocity) then
            call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
            call my_alloc_ptr(grid%dydt,      grid%nnodes01)
            call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
            call my_alloc_ptr(grid%facespeed, grid%nedgeloc)
            call my_alloc_ptr(grid%res_gcl,1, grid%nnodes01)
          else
            call my_alloc_ptr(grid%dxdt,      1)
            call my_alloc_ptr(grid%dydt,      1)
            call my_alloc_ptr(grid%dzdt,      1)
            call my_alloc_ptr(grid%facespeed, 1)
            call my_alloc_ptr(grid%res_gcl,1, 1)
          end if

          if (static_grid_transform) then
            call my_alloc_ptr(grid%xat0,      grid%nnodes01)
            call my_alloc_ptr(grid%yat0,      grid%nnodes01)
            call my_alloc_ptr(grid%zat0,      grid%nnodes01)
          else
            call my_alloc_ptr(grid%xat0,      1)
            call my_alloc_ptr(grid%yat0,      1)
            call my_alloc_ptr(grid%zat0,      1)
          end if

          call my_alloc_ptr(grid%xatn,      1)
          call my_alloc_ptr(grid%yatn,      1)
          call my_alloc_ptr(grid%zatn,      1)
          call my_alloc_ptr(grid%xatn1,     1)
          call my_alloc_ptr(grid%yatn1,     1)
          call my_alloc_ptr(grid%zatn1,     1)
          call my_alloc_ptr(grid%xatn2,     1)
          call my_alloc_ptr(grid%yatn2,     1)
          call my_alloc_ptr(grid%zatn2,     1)
          call my_alloc_ptr(grid%xatn3,     1)
          call my_alloc_ptr(grid%yatn3,     1)
          call my_alloc_ptr(grid%zatn3,     1)
          call my_alloc_ptr(grid%xatn4,     1)
          call my_alloc_ptr(grid%yatn4,     1)
          call my_alloc_ptr(grid%zatn4,     1)
          call my_alloc_ptr(grid%volatn,    1)
          call my_alloc_ptr(grid%volatn1,   1)
          call my_alloc_ptr(grid%volatn2,   1)
          call my_alloc_ptr(grid%volatn3,   1)
          call my_alloc_ptr(grid%volatn4,   1)
          call my_alloc_ptr(grid%volatnp1,  1)
          call my_alloc_ptr(grid%volatnp2,  1)

        else static_or_moving_3

!         grid is moving - need to store grid point velocities,
!         dual face speeds, and x,y,z locations at previous
!         time steps, regardless of whether grid motion is
!         rigid or deforming

          call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
          call my_alloc_ptr(grid%dydt,      grid%nnodes01)
          call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
          call my_alloc_ptr(grid%facespeed, grid%nedgeloc)
          call my_alloc_ptr(grid%xat0,      grid%nnodes01)
          call my_alloc_ptr(grid%yat0,      grid%nnodes01)
          call my_alloc_ptr(grid%zat0,      grid%nnodes01)
          call my_alloc_ptr(grid%xatn,      grid%nnodes01)
          call my_alloc_ptr(grid%yatn,      grid%nnodes01)
          call my_alloc_ptr(grid%zatn,      grid%nnodes01)
          call my_alloc_ptr(grid%xatn1,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn1,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn1,     grid%nnodes01)
          call my_alloc_ptr(grid%xatn2,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn2,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn2,     grid%nnodes01)
          call my_alloc_ptr(grid%xatn3,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn3,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn3,     grid%nnodes01)
          call my_alloc_ptr(grid%xatn4,     grid%nnodes01)
          call my_alloc_ptr(grid%yatn4,     grid%nnodes01)
          call my_alloc_ptr(grid%zatn4,     grid%nnodes01)
          call my_alloc_ptr(grid%res_gcl,1, grid%nnodes01)
          call my_alloc_ptr(grid%volatn,    grid%nnodes01)
          call my_alloc_ptr(grid%volatn1,   grid%nnodes01)
          call my_alloc_ptr(grid%volatn2,   grid%nnodes01)
          call my_alloc_ptr(grid%volatn3,   grid%nnodes01)
          call my_alloc_ptr(grid%volatn4,   grid%nnodes01)
          call my_alloc_ptr(grid%volatnp1,  1)
          call my_alloc_ptr(grid%volatnp2,  1)

        end if static_or_moving_3

      else need_timedata_3

!       not time accurate - don't need extra mesh data; the exceptions: 1) if
!       the mesh is in a noninertial reference frame - then we need to store the
!       equivalent mesh point velocities and dual face speeds; 2) if a static
!       aeroelastic case or static grid transform case we need to store the
!       intial mesh

        if (need_grid_velocity) then
          call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
          call my_alloc_ptr(grid%dydt,      grid%nnodes01)
          call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
          call my_alloc_ptr(grid%facespeed, grid%nedge)
          call my_alloc_ptr(grid%res_gcl,1, grid%nnodes01)
        else
          call my_alloc_ptr(grid%dxdt,      1)
          call my_alloc_ptr(grid%dydt,      1)
          call my_alloc_ptr(grid%dzdt,      1)
          call my_alloc_ptr(grid%facespeed, 1)
          call my_alloc_ptr(grid%res_gcl,1, 1)
        end if

        call my_alloc_ptr(grid%volatn,    1)
        call my_alloc_ptr(grid%volatn1,   1)
        call my_alloc_ptr(grid%volatn2,   1)
        call my_alloc_ptr(grid%volatn3,   1)
        call my_alloc_ptr(grid%volatn4,   1)
        call my_alloc_ptr(grid%volatnp1,  1)
        call my_alloc_ptr(grid%volatnp2,  1)

        if ( sub_string(grid%grid_motion,'deform') .or.                        &
             static_grid_transform )               then
          call my_alloc_ptr(grid%xat0,      grid%nnodes01)
          call my_alloc_ptr(grid%yat0,      grid%nnodes01)
          call my_alloc_ptr(grid%zat0,      grid%nnodes01)
        else
          call my_alloc_ptr(grid%xat0,      1)
          call my_alloc_ptr(grid%yat0,      1)
          call my_alloc_ptr(grid%zat0,      1)
        end if

        call my_alloc_ptr(grid%xatn,      1)
        call my_alloc_ptr(grid%yatn,      1)
        call my_alloc_ptr(grid%zatn,      1)
        call my_alloc_ptr(grid%xatn1,     1)
        call my_alloc_ptr(grid%yatn1,     1)
        call my_alloc_ptr(grid%zatn1,     1)
        call my_alloc_ptr(grid%xatn2,     1)
        call my_alloc_ptr(grid%yatn2,     1)
        call my_alloc_ptr(grid%zatn2,     1)
        call my_alloc_ptr(grid%xatn3,     1)
        call my_alloc_ptr(grid%yatn3,     1)
        call my_alloc_ptr(grid%zatn3,     1)
        call my_alloc_ptr(grid%xatn4,     1)
        call my_alloc_ptr(grid%yatn4,     1)
        call my_alloc_ptr(grid%zatn4,     1)

      end if need_timedata_3

      if ( cc ) then
        call my_alloc_ptr(grid%r11,       1)
        call my_alloc_ptr(grid%r12,       1)
        call my_alloc_ptr(grid%r13,       1)
        call my_alloc_ptr(grid%r22,       1)
        call my_alloc_ptr(grid%r23,       1)
        call my_alloc_ptr(grid%r33,       1)
        call my_alloc_ptr(grid%rlsq,  3,3,grid%ncell0)
        call my_alloc_ptr(grid%rlsq_ia, grid%ncell01+1)
        call my_alloc_ptr(grid%rlsq_ja, grid%ncell_augmentors)
      else
        call my_alloc_ptr(grid%r11,       grid%nnodes01)
        call my_alloc_ptr(grid%r12,       grid%nnodes01)
        call my_alloc_ptr(grid%r13,       grid%nnodes01)
        call my_alloc_ptr(grid%r22,       grid%nnodes01)
        call my_alloc_ptr(grid%r23,       grid%nnodes01)
        call my_alloc_ptr(grid%r33,       grid%nnodes01)
        call my_alloc_ptr(grid%rlsq,  1,1,1)
        call my_alloc_ptr(grid%rlsq_ia, 1)
        call my_alloc_ptr(grid%rlsq_ja, 1)
      endif

      if(mixed) then
        call my_alloc_ptr(grid%wr11,      grid%nnodes01)
        call my_alloc_ptr(grid%wr12,      grid%nnodes01)
        call my_alloc_ptr(grid%wr13,      grid%nnodes01)
        call my_alloc_ptr(grid%wr22,      grid%nnodes01)
        call my_alloc_ptr(grid%wr23,      grid%nnodes01)
        call my_alloc_ptr(grid%wr33,      grid%nnodes01)
      else
        call my_alloc_ptr(grid%wr11,      1)
        call my_alloc_ptr(grid%wr12,      1)
        call my_alloc_ptr(grid%wr13,      1)
        call my_alloc_ptr(grid%wr22,      1)
        call my_alloc_ptr(grid%wr23,      1)
        call my_alloc_ptr(grid%wr33,      1)
      endif

      if ( cc ) then
        call my_alloc_ptr(grid%fptr,      6,grid%nface)
        call my_alloc_ptr(grid%cell_vol,  grid%ncell01)
        call my_alloc_ptr(grid%area_face, grid%nface)
        call my_alloc_ptr(grid%xn_face,   grid%nface)
        call my_alloc_ptr(grid%yn_face,   grid%nface)
        call my_alloc_ptr(grid%zn_face,   grid%nface)
        call my_alloc_ptr(grid%x_face,    grid%nface)
        call my_alloc_ptr(grid%y_face,    grid%nface)
        call my_alloc_ptr(grid%z_face,    grid%nface)
        call my_alloc_ptr(grid%fl2g,      grid%nface)
        call my_alloc_ptr(grid%xc,        grid%ncell01)
        call my_alloc_ptr(grid%yc,        grid%ncell01)
        call my_alloc_ptr(grid%zc,        grid%ncell01)
        call my_alloc_ptr(grid%cl2g,      grid%ncell01)
        call my_alloc_ptr(grid%flsq_ia,   grid%nface+1)
        call my_alloc_ptr(grid%flsq_ja,   grid%nface_augmentors)
        call my_alloc_ptr(grid%flsq_lu,   4, 4, grid%nface)
        call my_alloc_ptr(grid%flsq_flag_rhs, grid%nface)
        call my_alloc_ptr(grid%flsq_flag_lhs, grid%nface)
      else
        call my_alloc_ptr(grid%fptr,    6,1)
        call my_alloc_ptr(grid%cell_vol,  1)
        call my_alloc_ptr(grid%area_face, 1)
        call my_alloc_ptr(grid%xn_face,   1)
        call my_alloc_ptr(grid%yn_face,   1)
        call my_alloc_ptr(grid%zn_face,   1)
        call my_alloc_ptr(grid%x_face,    1)
        call my_alloc_ptr(grid%y_face,    1)
        call my_alloc_ptr(grid%z_face,    1)
        call my_alloc_ptr(grid%fl2g,      1)
        call my_alloc_ptr(grid%xc,        1)
        call my_alloc_ptr(grid%yc,        1)
        call my_alloc_ptr(grid%zc,        1)
        call my_alloc_ptr(grid%cl2g,      1)
        call my_alloc_ptr(grid%flsq_ia,   1)
        call my_alloc_ptr(grid%flsq_ja,   1)
        call my_alloc_ptr(grid%flsq_lu,   4, 4, 1)
        call my_alloc_ptr(grid%flsq_flag_rhs, 1)
        call my_alloc_ptr(grid%flsq_flag_lhs, 1)
      endif

      if (overset_flag .or. pundit_flag) then
        call my_alloc_ptr(grid%iblank, grid%nnodes01)
        call my_alloc_ptr(grid%imesh,  grid%nnodes01)
      else
        call my_alloc_ptr(grid%iblank, 1)
        call my_alloc_ptr(grid%imesh,  1)
      end if

    case default read_depth_level

      write(*,*)"ERROR: Invalid read_depth_level..stopping"
      stop ! FIXME: should be lmpi_die or se_exit(1)?

    end select read_depth_level

!   initially allocate grid position arrays to size 1; if we are running the
!   flow solver then we will reallocate to them to store as functions of time

    call my_alloc_ptr(grid%thetax, 1)
    call my_alloc_ptr(grid%thetay, 1)
    call my_alloc_ptr(grid%thetaz, 1)
    call my_alloc_ptr(grid%xorig,  1)
    call my_alloc_ptr(grid%yorig,  1)
    call my_alloc_ptr(grid%zorig,  1)

    grid%skip_q_allocated = .false.

    if ( cc ) then
      call my_alloc_ptr(grid%cell_skewness, grid%ncell01)
      call my_alloc_ptr(grid%skip_q,        grid%ncell01)
      call my_alloc_ptr(grid%boundary_flag, grid%ncell01)
      call my_alloc_ptr(grid%cgamma,        grid%ncell01)
      call my_alloc_ptr(grid%slenxn,        grid%ncell01)
      call my_alloc_ptr(grid%slenyn,        grid%ncell01)
      call my_alloc_ptr(grid%slenzn,        grid%ncell01)
      call my_alloc_ptr(grid%nlsq, 1,1,1)
    else
      call my_alloc_ptr(grid%cell_skewness, grid%nnodes01)
      call my_alloc_ptr(grid%skip_q,        grid%nnodes01)
      call my_alloc_ptr(grid%boundary_flag,             1)
      if ( nc_mapped_lsq ) then
        call my_alloc_ptr(grid%cgamma,        grid%nnodes01)
        call my_alloc_ptr(grid%slenxn,        grid%nnodes01)
        call my_alloc_ptr(grid%slenyn,        grid%nnodes01)
        call my_alloc_ptr(grid%slenzn,        grid%nnodes01)
        !FIXME - JLT
        !FIXME - JLT With choleski, nlsq storage could be halved.
        !FIXME - JLT
        if ( type_fit == 'linear' ) then
          call my_alloc_ptr(grid%nlsq,  3,3,grid%nnodes0)
        else
          call my_alloc_ptr(grid%nlsq,  9,9,grid%nnodes0)
        endif
      else
        call my_alloc_ptr(grid%cgamma, 1)
        call my_alloc_ptr(grid%slenxn, 1)
        call my_alloc_ptr(grid%slenyn, 1)
        call my_alloc_ptr(grid%slenzn, 1)
        call my_alloc_ptr(grid%nlsq,   1,1,1)
      endif
    endif

                     !FIXME
    if ( cc ) then   !FIXME SP Note: This is a not-DRY clone LibF90/grids.f90
                     !FIXME
      grid%xq    => grid%xc
      grid%yq    => grid%yc
      grid%zq    => grid%zc
      grid%volq  => grid%cell_vol
      grid%cc    = .true.
      grid%dof0  = grid%ncell0
    else
      grid%xq    => grid%x
      grid%yq    => grid%y
      grid%zq    => grid%z
      grid%volq  => grid%vol
      grid%cc    = .false.
      grid%dof0  = grid%nnodes0
      if (twod) grid%dof0 = grid%nnodes0/2
    endif

    grid%origin = 1 !default to non-agglomerated grid.

  end subroutine pparty_size_grid


!=============================== PPARTY_SIZE_ELEM ============================80
!
! Allocates memory for the element derived type
! (2nd layer of the grid derived type)
!
!=============================================================================80

  subroutine pparty_size_elem(elem, cc_primal, ghost_level_arg)

    use allocations,      only : my_alloc_ptr, my_realloc_ptr
    use element_types,    only : elem_type

    type(elem_type),           intent(inout) :: elem
    logical,                   intent(in)    :: cc_primal
    integer,         optional, intent(in)    :: ghost_level_arg

    integer :: level

    continue

    level = 1
    if (present(ghost_level_arg)) level = ghost_level_arg

    if (level > 0) then
      select case (level)
      case (1)
         call my_realloc_ptr(elem%c2n,  elem%node_per_cell, max(elem%ncell,1))
         call my_realloc_ptr(elem%c2e,  elem%edge_per_cell, max(elem%ncell,1))
         call my_realloc_ptr(elem%cl2g, max(elem%ncell,1))
        if ( cc_primal ) then
          call my_alloc_ptr(elem%cell_map, max(elem%ncell,1))
          call my_alloc_ptr(elem%big_angle, 1 )
        else
          call my_alloc_ptr(elem%cell_map, 1)
          call my_alloc_ptr(elem%big_angle, max(elem%ncell,1) )
        endif
      case (3)
        if ( cc_primal ) then
          call my_alloc_ptr(elem%cell_map, max(elem%ncell,1))
        else
          call my_alloc_ptr(elem%cell_map, 1)
        endif
      case default
        write(*,*)"ERROR: allocate_elem only valid for level 1 or 3",          &
                  " not ",level," (complete) stopping..."
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end select
    end if

  end subroutine pparty_size_elem

!=============================== WRITE_PART_INFO =============================80
!
! Write g2l array to disk for temporary overset solution until
! Ralph can receive this directly through a call
!
!=============================================================================80

  subroutine write_part_info(grid)

    use bc_names,          only : bc_name_index
    use grid_types,        only : grid_type
    use info_depr,         only : twod
    use lmpi,              only : lmpi_master, lmpi_send, lmpi_recv, lmpi_id,  &
                                  lmpi_nproc
    type(grid_type), intent(in) :: grid

    integer            :: i, j, ib, ibc, ierr, melem, mbound, temp_int(8), ipe
    character(len=80)  :: filename, bc_name

    integer, dimension(:), allocatable :: temp_ncell, temp_boundary

    integer, parameter :: unit_info = 28

  continue

    if (lmpi_master) then
       filename = trim(grid%project)//'.grid_info'
       write(*,*)'    ... Write global grid information to '//trim(filename)

       call se_open(unit_info,file=filename)

       write(unit_info,'(a)') 'Global Grid Info'
       write(unit_info,'(a)') '----------------'
       write(unit_info,*)

       do i=1,grid%nelem
         write(unit_info,'(a,a3,a,i0)')                                        &
               '   number of ',grid%elem(i)%type_cell,                         &
               ' cells : ',    grid%elem(i)%ncellg
         write(unit_info,*)
       end do

       write(unit_info,'(a,i0)')'   number of nodes     : ',grid%nnodesg
       write(unit_info,*)
       write(unit_info,'(a,i0)')'   number of edges     : ',grid%nedgeg
       write(unit_info,*)

       write(unit_info,'(a,i0)')'   number of boundaries: ',grid%nbound
       write(unit_info,*)

       do i = 1,grid%nbound
         ibc = grid%bc(i)%ibc
         call bc_name_index(ibc,bc_name,.true.)
         write(unit_info,'(a,i0)') '   boundary info for boundary',i
         write(unit_info,'(3a,i0,a)') '     boundary condition         : ',    &
               trim(bc_name), ' (',ibc,')'
         write(unit_info,'(a,i0)') '     number of tria faces       : ',       &
               grid%bc(i)%nbfacetg
         write(unit_info,'(a,i0)') '     number of quad faces       : ',       &
               grid%bc(i)%nbfaceqg
         write(unit_info,'(a,i0)') '     number of nodes            : ',       &
               grid%bc(i)%nbnodeg
         write(unit_info,*)
       end do
       write(unit_info,*)

    end if

    temp_int(1) = grid%nelem
    temp_int(2) = grid%nnodes0
    temp_int(3) = grid%nnodes01-grid%nnodes0
    temp_int(4) = grid%nedgeloc
    temp_int(5) = grid%nedge
    temp_int(6) = grid%nedgeloc_2D
    temp_int(7) = grid%nbound
    temp_int(8) = 0
    if (twod) temp_int(8) = 1

    allocate(temp_ncell(grid%nelem*2))
    j = 1
    do i = 1,grid%nelem
       select case (trim(grid%elem(i)%type_cell))
          case ('tet')
            temp_ncell(j) = 1
          case ('pyr')
            temp_ncell(j) = 2
          case ('prz')
            temp_ncell(j) = 3
          case ('hex')
            temp_ncell(j) = 4
          case default
            write(*,*)"Impossible case ",trim(grid%elem(i)%type_cell)
       end select
       temp_ncell(j+1) = grid%elem(i)%ncell
       j = j + 2
    end do

    if (grid%nbound > 0) then
       allocate(temp_boundary(grid%nbound*4))
       i = 1
       do ib = 1,grid%nbound
          temp_boundary(i)   = grid%bc(ib)%ibc
          temp_boundary(i+1) = grid%bc(ib)%nbfacet
          temp_boundary(i+2) = grid%bc(ib)%nbfaceq
          temp_boundary(i+3) = grid%bc(ib)%nbnode
          i = i + 4
       end do
    end if

    if (lmpi_master) then
       call write_part_info_par(unit_info,lmpi_id,temp_int,                    &
            temp_ncell,temp_boundary)
       deallocate(temp_ncell)
       if (grid%nbound > 0) deallocate(temp_boundary)

       do ipe = 1,lmpi_nproc-1
          call lmpi_recv(temp_int,8,ipe,ipe*100,ierr)
          melem = temp_int(1)
          allocate(temp_ncell(melem*2))
          call lmpi_recv(temp_ncell,melem*2,ipe,ipe*100,ierr)

          mbound = temp_int(7)
          if (mbound > 0) then
             allocate(temp_boundary(mbound*4))
             call lmpi_recv(temp_boundary,mbound*4,ipe,ipe*100,ierr)
          end if
          call write_part_info_par(unit_info,ipe,temp_int,                     &
               temp_ncell,temp_boundary)
          deallocate(temp_ncell)
          if (mbound > 0) deallocate(temp_boundary)
       end do
    else
       call lmpi_send(temp_int,8,             0,lmpi_id*100,ierr)
       call lmpi_send(temp_ncell,grid%nelem*2,0,lmpi_id*100,ierr)
       if (grid%nbound > 0) then
          call lmpi_send(temp_boundary,grid%nbound*4,0,lmpi_id*100,ierr)
          deallocate(temp_boundary)
       end if
       deallocate(temp_ncell)
    end if
    if (.false.) write(*,*)ierr

  end subroutine write_part_info

!=============================== WRITE_PART_INFO_PAR =========================80
!
! Write distributed part info to a single file.
!
!=============================================================================80

  subroutine write_part_info_par(unit,ipe,temp_int,temp_ncell,temp_bound)

    use bc_names,          only : bc_name_index

    integer,               intent(in) :: unit, ipe
    integer, dimension(8), intent(in) :: temp_int
    integer, dimension(:), intent(in) :: temp_ncell, temp_bound

    integer            :: j, k, ielem, ib, melem, mbound
    character(len=80)  :: bc_name

    character(len=3), dimension(4), parameter :: temp_type_cell =              &
        (/'tet','pyr','prz','hex'/)

  continue

    melem  = temp_int(1)
    mbound = temp_int(7)
    write(unit,'(a,i0)') 'Info for Grid Partition ',ipe+1
    write(unit,'(a,i0)') '---------------------------'
    write(unit,*)

    j = 1
    do ielem=1,melem
      write(unit,'(a,a3,a,i0)')                                                &
            '   number of ',temp_type_cell(temp_ncell(j)),                     &
            ' level 1  cells : ', temp_ncell(j+1)
      write(unit,*)
      j = j + 2
    end do

!   output part grid info for nodes

    write(unit,'(a,i0)') '   number of     level 0  nodes : ',temp_int(2)
    write(unit,'(a,i0)') '   number of     level 1  nodes : ',temp_int(3)

!   output part grid info for edges

    write(unit,*)
    write(unit,'(a,i0)') '   number of     level 0  edges : ',temp_int(4)
    write(unit,'(a,i0)') '   number of     level 01 edges : ',temp_int(5)
    ! if twod
    if (temp_int(8)==1)                                                        &
      write(unit,'(a,i0)')'   number of  2D level 0  edges : ',temp_int(6)
    write(unit,*)

!   output part grid info for boundaries

    j = 1
    do ib = 1,mbound
      k = temp_bound(j)
      call bc_name_index(k,bc_name,.true.)
      write(unit,'(a,i0)') '   boundary info for boundary ',ib
      write(unit,'(3a,i0,a)') '     boundary condition         : ',            &
            trim(bc_name),' (',k,')'
      write(unit,'(a,i0)') '     number of tria faces       : ', temp_bound(j+1)
      write(unit,'(a,i0)') '     number of quad faces       : ', temp_bound(j+2)
      write(unit,'(a,i0)') '     number of nodes            : ', temp_bound(j+3)
      write(unit,*)
      j = j + 4
    end do

  end subroutine write_part_info_par

!================================ READ_COMPLEX_INPUT =========================80
!
!  Reads complex input parameters
!
!=============================================================================80
  subroutine read_complex_input(grid)

    use lmpi,         only : lmpi_bcast, lmpi_conditional_stop, lmpi_die,      &
                             lmpi_master
    use info_depr,    only : xmach, alpha, complex_epsilon, yaw,               &
                             complex_to_perturb, complex_grid_point
    use nml_noninertial_reference_frame, only : noninertial
    use noninertials, only : xrotrate_ni, yrotrate_ni, zrotrate_ni
    use debug_defs,   only : skip_perturb_input, composite_jacobian_lhs,       &
                             ntt_for_jacobian_check
    use kinddefs,     only : dp
    use grid_types,   only : grid_type
    use moves,        only : snap_grid

    type(grid_type), intent(inout) :: grid

    integer :: punit, iostat, i, global_node

  continue

!   if debugging jacobians, we don't perturb anything here

    if (  skip_perturb_input     .or. &
          composite_jacobian_lhs .or. &
          ntt_for_jacobian_check > 0 ) then
      complex_to_perturb = 0
      complex_epsilon = epsilon(real(complex_epsilon,dp))
      call lmpi_bcast(complex_to_perturb)
      call lmpi_bcast(complex_epsilon)
      return
    end if

    iostat = 0
    punit = 8

    master_open_input_file : if ( lmpi_master ) then

      call se_open(punit, file='perturb.input',                                &
            form='formatted', status='old', iostat=iostat)

      if (iostat /= 0) then
        write (*,*) 'error: the file perturb.input not found, stopping...'
        iostat = 1
      endif

    endif master_open_input_file

    call lmpi_conditional_stop(iostat)

    if ( lmpi_master ) rewind(punit)
    if ( lmpi_master ) read(punit,*)
    if ( lmpi_master ) read(punit,*)complex_to_perturb,complex_epsilon,        &
                                    complex_grid_point
    if ( lmpi_master ) close(punit)

    call lmpi_bcast(complex_to_perturb)
    call lmpi_bcast(complex_epsilon)
    call lmpi_bcast(complex_grid_point)

    if(complex_to_perturb == 1) then

      if ( lmpi_master ) xmach = xmach                                         &
                                 + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      if ( lmpi_master ) write(*,*) 'Perturbing Mach number...xmach = ',xmach

    else if(complex_to_perturb == 2) then

      if ( lmpi_master ) alpha = alpha                                         &
                                 + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      if ( lmpi_master ) write(*,*) 'Perturbing alpha...alpha = ',alpha

    else if(complex_to_perturb == 3) then

      if (lmpi_master) write(*,*)'Shape perturbation requested; must snap grid.'
      if ( .not. snap_grid ) then
        if (lmpi_master) write(*,*) 'MUST --snap_grid with shape perturbations.'
        call lmpi_die
      endif

    else if(complex_to_perturb == 4) then

      if ( lmpi_master ) xrotrate_ni = xrotrate_ni                             &
                                    + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      if ( lmpi_master )                                                       &
           write(*,*) 'Perturbing x-rotation rate...xrotrate_ni = ',xrotrate_ni
      if ( lmpi_master ) noninertial = .true.

    else if(complex_to_perturb == 5) then

      if ( lmpi_master ) yrotrate_ni = yrotrate_ni                             &
                                    + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      if ( lmpi_master )                                                       &
           write(*,*) 'Perturbing y-rotation rate...yrotrate_ni = ',yrotrate_ni
      if ( lmpi_master ) noninertial = .true.

    else if(complex_to_perturb == 6) then

      if ( lmpi_master ) zrotrate_ni = zrotrate_ni                             &
                                    + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      if ( lmpi_master )                                                       &
           write(*,*) 'Perturbing z-rotation rate...zrotrate_ni = ',zrotrate_ni
      if ( lmpi_master ) noninertial = .true.

    else if(complex_to_perturb == 7) then

      search_nodes1 : do i = 1, grid%nnodes0
        global_node = grid%l2g(i)
        if ( global_node == complex_grid_point ) then
          grid%x(i) = grid%x(i) + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
          exit search_nodes1
        endif
      end do search_nodes1

      if (lmpi_master) write(*,*)'Shape perturbation requested; must snap grid.'
      if ( .not. snap_grid ) then
        if (lmpi_master) write(*,*) 'MUST --snap_grid with shape perturbations.'
        call lmpi_die
      endif
      if (lmpi_master) write(*,*)'Perturbing global x-coord ',complex_grid_point

    else if(complex_to_perturb == 8) then

      search_nodes2 : do i = 1, grid%nnodes0
        global_node = grid%l2g(i)
        if ( global_node == complex_grid_point ) then
          grid%y(i) = grid%y(i) + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
          exit search_nodes2
        endif
      end do search_nodes2

      if (lmpi_master) write(*,*)'Shape perturbation requested; must snap grid.'
      if ( .not. snap_grid ) then
        if (lmpi_master) write(*,*) 'MUST --snap_grid with shape perturbations.'
        call lmpi_die
      endif
      if (lmpi_master) write(*,*)'Perturbing global y-coord ',complex_grid_point

    else if(complex_to_perturb == 9) then

      search_nodes3 : do i = 1, grid%nnodes0
        global_node = grid%l2g(i)
        if ( global_node == complex_grid_point ) then
          grid%z(i) = grid%z(i) + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
          exit search_nodes3
        endif
      end do search_nodes3

      if (lmpi_master) write(*,*)'Shape perturbation requested; must snap grid.'
      if ( .not. snap_grid ) then
        if (lmpi_master) write(*,*) 'MUST --snap_grid with shape perturbations.'
        call lmpi_die
      endif
      if (lmpi_master) write(*,*)'Perturbing global z-coord ',complex_grid_point

    else if(complex_to_perturb == 10) then

      if ( lmpi_master ) yaw = yaw + cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      if ( lmpi_master ) write(*,*) 'Perturbing yaw...yaw = ',yaw

    endif

    call lmpi_bcast(xmach)
    call lmpi_bcast(alpha)
    call lmpi_bcast(yaw)
    call lmpi_bcast(noninertial)
    call lmpi_bcast(xrotrate_ni)
    call lmpi_bcast(yrotrate_ni)
    call lmpi_bcast(zrotrate_ni)

  end subroutine read_complex_input

end module pparty_preprocessor

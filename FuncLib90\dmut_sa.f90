!================================ DMUT_SA ====================================80
!
! Jacobian of mut = f( turb, rho, mu/rho )for S-A model.
!
!=============================================================================80

  pure function dmut_sa( eqn_set, turb, rho_in, nu_in )

    use solution_types, only : compressible
    use turb_sa_const,  only : cv1

    integer,    intent(in) :: eqn_set
    real(dp),   intent(in) :: turb, rho_in, nu_in

    real(dp), dimension(3) :: dmut_sa

    real(dp) :: rho, nu, mut, psi, fv1, dfv1, dpsi

  continue

    rho = 1._dp
    nu  = 1._dp
    if ( eqn_set == compressible ) then
      rho  = rho_in
      nu   = nu_in
    end if

    psi = turb / nu

    fv1 = psi**3 / (psi**3 + cv1**3)
    mut = rho * turb * fv1

    dmut_sa(:) = 0._dp

    if ( mut > 0._dp ) then

      dpsi = dsa1_psi( turb, nu )

      dfv1 = ((psi**3 + cv1**3)*3._dp*psi**2*dpsi  -           &
             psi**3*3._dp*psi**2*dpsi ) / (psi**3 + cv1**3) ** 2

      dmut_sa(1) = rho * ( fv1 + turb * dfv1 )

      dmut_sa(2) = turb * fv1

      dpsi  = dsa3_psi( turb, nu )

      dfv1 = ((psi**3 + cv1**3)*3._dp*psi**2*dpsi  -           &
             psi**3*3._dp*psi**2*dpsi ) / (psi**3 + cv1**3) ** 2

      dmut_sa(3) = rho * turb * dfv1

    endif

  end function dmut_sa

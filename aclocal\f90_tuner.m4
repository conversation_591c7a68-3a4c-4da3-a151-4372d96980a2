# autoconf macros for "tuning" the f90 compilers by setting appropriate
# options and #defines
#
# NOTE: This macro will augment $FCFLAGS and potentially $LDFLAGS!
#
# Assigned Shell Variables:
#   enable_fastloose "Fast and Loose" dependency tracking.
#   fcompiler  compiler name for use with `f2py`
#
# Assigned Output Variables:
#   @F90_EXT_LIB@    F90 extensions library
#
# Assigned AM_CONDITIONALS:
# XLF_CONFIG_H
#

AC_DEFUN([FUN3D_TUNE_F90],[

AC_ARG_ENABLE(ftune,
        [[  --enable-ftune          tailor Fortran compiler options for FUN3D [yes]]],
        [enable_ftune=$enableval],  [enable_ftune="yes"])

F90_EXT_LIB=$F90_EXT_LIB

compiler_base=`basename $FC`
AC_MSG_RESULT([  Tuning $compiler_base compiler for FUN3D])

if test -n "${MPIF90}" -a -n "`echo ${FC}x | grep ${MPIF90}x`"
then
  compiler_base=`$FC -show | grep -v "^ln" | grep -v "^rm" | grep -v "compchk.sh"`
  compiler_base=`echo "$compiler_base" | sed 's/ .*//'`
  compiler_base=`basename "$compiler_base"`
  compiler_base=`echo $compiler_base | sed 's/ .*//'`
else
  compiler_base=`echo $compiler_base | sed 's/ .*//'`
fi
AC_DEFINE_UNQUOTED([FORTRAN_COMPILER],["$compiler_base"],[Base Fortran Compiler])

AM_CONDITIONAL(XLF_CONFIG_H,false)

MOD_DEP_COMPILER="UNKNOWN"
fcompiler="none"

case $compiler_base in
  ifort)

    fcompiler="intelem"

    if (uname -s | grep Darwin) > /dev/null 2>&1
    then
      MOD_DEP_COMPILER="INTEL-ifort12-on-DARWIN"
    else
      MOD_DEP_COMPILER="INTEL-ifort12-on-LINUX"
    fi

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-O2 -ip -align -fno-alias -g -traceback $FCFLAGS"
      if (uname -s | grep Darwin) > /dev/null 2>&1
      then
        FCFLAGS="-fno-common $FCFLAGS"
      fi
    fi

    F90_EXT_LIB="-lm $F90_EXT_LIB"

    if test "$with_mpif90" == 'no'
    then
      F90_EXT_LIB="$F90_EXT_LIB -lmpi"
    fi
    ;;

  g95)

    fcompiler="g95"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-i4 $FCFLAGS"
    fi
    ;;

  gfortran*)

    MOD_DEP_COMPILER="GCC-f95-on-LINUX"
    fcompiler="gnu95"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-march=native -ffast-math -funroll-loops -O3 -g $FCFLAGS"
    fi
    ;;

  lf95)

    MOD_DEP_COMPILER="LAHEY-lf95-on-LINUX"
    fcompiler="lahey"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-O --ap --tpp $FCFLAGS"
    fi
    ;;

  nagfor)

    fcompiler="nag"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-O4 -ieee=full -Bstatic $FCFLAGS"
    fi
    ;;

  pgf95 | pgfortran)

    MOD_DEP_COMPILER="PGI-pgf95-on-LINUX"
    fcompiler="pg"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-Bstatic_pgi -fastsse -Munroll=n:4 -Mipa=fast,inline $FCFLAGS"
    fi
    ;;

  af95)

    if test "`uname`" = "Linux"
    then
      MOD_DEP_COMPILER="ABSOFT-f95-on-LINUX"
    elif test "`uname`" = "Darwin"
    then
      MOD_DEP_COMPILER="ABSOFT-f95-on-DARWIN"
    fi
    fcompiler="absoft"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-Ofast -speed_math=9 -WOPT:if_conv=off -LNO:fu=9:full_unroll_size=7000 -TARG:sse3=on $FCFLAGS"
    fi

    F90_EXT_LIB="-lU77 $F90_EXT_LIB"
    ;;

  *xlf*)
    AC_DEFINE([FORTRAN_FLUSH_UNDERSCORE],[1],[IBM xlf compiler flush requires underscore])

    MOD_DEP_COMPILER="IBM-xlf90-on-AIX"
    fcompiler="ibm"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-O2 -qhalt=e -WF,-C!,-DHAVE_CONFIG_H $FCFLAGS"
    fi
    ;;

  f95)
    case `uname` in
      SunOS)

        MOD_DEP_COMPILER="WORKSHOP-f95-on-SUNOS"
        fcompiler="sun"

        if test "$enable_ftune" != 'no'
        then
          FCFLAGS="-fast $FCFLAGS"
          LDFLAGS="$LDFLAGS"
        fi
        ;;
    esac
    ;;

  fort)

    MOD_DEP_COMPILER="COMPAQ-f95-on-LINUX"
    fcompiler="compaq"

    if test "$enable_ftune" != 'no'
    then
      FCFLAGS="-O0 $FCFLAGS"
    fi
    ;;

  ftn)

    # Remove C-style comments from config.h using the preprocessor 
    AC_CONFIG_COMMANDS([ftn],
                       [if test `echo ${ax_compiler_base} | grep "ftn"`
                        then
                          ${ax_cpp} -fpreprocessed -dD config.h > $$.h
                          touch -r config.h $$.h
                          /bin/mv $$.h config.h
                        fi
                       ],
                       [ax_cpp="$CPP $CPPFLAGS" ax_compiler_base="$compiler_base"
                       ])

    if test "$enable_ftune" != 'no'
    then
      # Big endian I/O
      FCFLAGS="-h byteswapio -O 3 $FCFLAGS"
    fi

    mpi_ldadd=
    AC_SUBST([mpi_ldadd])
    ;;

esac

AC_SUBST([F90_EXT_LIB])
AC_SUBST([MOD_DEP_COMPILER])
AC_SUBST([fcompiler])

])

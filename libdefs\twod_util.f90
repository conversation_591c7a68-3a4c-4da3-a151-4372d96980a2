module twod_util

  use kinddefs, only : dp, odp, dqp

  implicit none

  private

  public :: nplanes
  public :: yspan
  public :: yplane_2d
  public :: y_coplanar_tol
  public :: setup_2d
  public :: check_on_plane
  public :: copy_dq_2d, copy_array_2d, copy_twod
  public :: q_2d, check_skip_sidewalls
  public :: set_res_twod, set_jac_twod

  integer                :: nplanes = 2           ! no. of planes for 2D grid
! beginNeverComplex
  real(dp)            :: yspan = 1.0_dp     ! span of grid in y
  real(dp), parameter :: yplane_2d = 0.0_dp ! 2D plane to solve on
  real(dp), parameter :: y_coplanar_tol = epsilon(1._dp) ! tolerance for
                                                         ! points taken
                                                         ! to be coplanar
! endNeverComplex

  logical :: q_2d = .false.  !General indicator for two-dimensional flow.
                             !Note twod is strictly for node-centered path.

  interface copy_twod
    module procedure copy_twod_r4
    module procedure copy_twod_r8
  end interface

contains

!================================== CHECK_ON_PLANE ===========================80
!
!   Checks whether solve points are first points in grid array.
!
!=============================================================================80

  subroutine check_on_plane(grid)

    use lmpi,       only : lmpi_conditional_stop, lmpi_id
    use grid_types, only : grid_type

    type(grid_type), intent(in) :: grid

    integer :: node, ierr

    continue

    ierr = 0
    do node=1,grid%nnodes0_2d
      if ( real(abs(grid%y(node) - yplane_2d)) <= y_coplanar_tol ) cycle
      write(*,"(1x,a,5(a,i0),a,e12.5)")                        &
      ' Solve points are not first nodes!',                    &
      ' nnodes0_2d=',grid%nnodes0_2d,' nnodes0=',grid%nnodes0, &
      ' nnodes01=',grid%nnodes01,' lmpi_id=',lmpi_id,          &
      ' node=',node,' y=',grid%y(node)
      ierr = 1
      exit
    end do
    call lmpi_conditional_stop(ierr,'check_on_plane:twod_util')

  end subroutine check_on_plane

!================================== SETUP_2D =================================80
!
!   Checks whether this mesh is appropriate for 2D and sets up a few utility
!   arrays: node pairings on opposite planes for 2D and edge-to-node pointers
!   for the 2D viscous fluxes and jacobains
!
!   Allocates small default arrays if 3D
!
!=============================================================================80

  subroutine setup_2d(grid)

    use allocations, only : my_alloc_ptr
    use lmpi,        only : lmpi_conditional_stop, lmpi_id, lmpi_master
    use grid_types,  only : grid_type
    use info_depr,   only : twod

    type(grid_type),                         intent(inout) :: grid

    integer :: edge, cnt, ielem, nedgeloc, nedgeloc_2d
    integer :: node1, node2, face_2d, ierr

    continue

    ierr = 0

    if ( twod ) q_2d = .true.

!   If this is a dual agglomeration, return.

    if(grid%origin == 3) return

!   if this is not a 2D case, just set some default array sizes and return

    if (.not. twod) then

      call my_alloc_ptr(grid%node_pairs_2d, 1, 1)

      return

    end if

!   set some local variables

    nedgeloc    = grid%nedgeloc
    nedgeloc_2d = grid%nedgeloc_2d

!   note that for 2D cases, the edges have been ordered so that the first
!   nedgeloc_2d edges lie on one y plane, the next nedgeloc_2d edges lie on
!   the other plane, and edges 2*nedgeloc_2d+1 to  nedgeloc edges span the
!   two planes - therefore the last set of edges is all we need look at.

    if ( lmpi_master ) then
      write(*,*)
      write(*,*) 'Using 2D Mode (Node-Centered)'
      write(*,*)
    end if

    if (nedgeloc_2d <= 0) then

!     something fundametally wrong...

      write(*,'(a,i3,a)') 'processor ',lmpi_id,                              &
           ' error: nedgeloc_2d <= 0...cannot run as 2D'
      call lmpi_conditional_stop(1,'1:twod_util:setup_2d')
    end if

!   only hexes and prisms are compatable with 2D...make sure we have no
!   other cell types in the mesh

    do ielem = 1,grid%nelem

      if (grid%elem(ielem)%type_cell /= 'hex'  .and.                           &
          grid%elem(ielem)%type_cell /= 'prz') then
          ierr = 1
          write(*,'(a,i3,3a)') 'processor ',lmpi_id,                         &
          ' error: cell type ',grid%elem(ielem)%type_cell,' cannot be run as 2D'
      end if

    end do
    call lmpi_conditional_stop(ierr,'2:twod_util:setup_2d')

!   Set face_2d (and check consistency for each element type)

    do ielem = 1,grid%nelem
      call set_face_2d( grid, ielem )
    end do

!   note that the number of nodes on one 2D plane is the same as  the
!   number of edges that span the two planes, i.e nedgeloc - 2*nedgeloc_2d

    grid%nnodes0_2d = nedgeloc - 2*nedgeloc_2d

    call my_alloc_ptr(grid%node_pairs_2d, 2, grid%nnodes0_2d)

    cnt = 0

    do edge = 2*nedgeloc_2d+1,nedgeloc

      node1 = grid%eptr(1,edge)
      node2 = grid%eptr(2,edge)

!     if y coordinates differ, they lie on opposite planes and and so form a
!     pair (this is just performed as a check)

      if ( real(abs(grid%y(node1) - grid%y(node2))) > y_coplanar_tol ) then

        cnt = cnt + 1

!       put nodes on the 1st y plane in the 1st location in node_pairs_2d
!       put nodes on the 2nd y plane in the 2nd location

        if ( real(abs(grid%y(node1) - yplane_2d)) <= y_coplanar_tol ) then
          grid%node_pairs_2d(1,cnt) = node1
          grid%node_pairs_2d(2,cnt) = node2
        else if ( real(abs(grid%y(node2) - yplane_2d)) <= y_coplanar_tol ) then
          grid%node_pairs_2d(1,cnt) = node2
          grid%node_pairs_2d(2,cnt) = node1
        end if

      end if

    end do

!   check that count and nnodes0_2d are the same

    ierr = cnt - grid%nnodes0_2d
    if ( ierr /= 0 )                                                  &
    write(*,'(a,i3,a,a,i0,a,i0)') 'processor ',lmpi_id,               &
    ' error3: cnt /= nnodes0_2d in subroutine setup_2d',              &
    ' cnt=',cnt,' grid%nnodes0_2d=',grid%nnodes0_2d
    call lmpi_conditional_stop(ierr,'3:twod_util:setup_2d')

!   set up the 2D edge-to-node mappings for the viscous fluxes and jacobians

    do ielem = 1,grid%nelem

      face_2d = grid%elem(ielem)%face_2d

      select case (grid%elem(ielem)%type_cell)

        case ('hex')

          select case (face_2d)

            case (1)

              grid%elem(ielem)%e2n_2d(1,1) = 2
              grid%elem(ielem)%e2n_2d(1,2) = 1

              grid%elem(ielem)%e2n_2d(2,1) = 1
              grid%elem(ielem)%e2n_2d(2,2) = 3

              grid%elem(ielem)%e2n_2d(3,1) = 3
              grid%elem(ielem)%e2n_2d(3,2) = 4

              grid%elem(ielem)%e2n_2d(4,1) = 4
              grid%elem(ielem)%e2n_2d(4,2) = 2

            case (2)

              grid%elem(ielem)%e2n_2d(1,1) = 5
              grid%elem(ielem)%e2n_2d(1,2) = 6

              grid%elem(ielem)%e2n_2d(2,1) = 6
              grid%elem(ielem)%e2n_2d(2,2) = 8

              grid%elem(ielem)%e2n_2d(3,1) = 8
              grid%elem(ielem)%e2n_2d(3,2) = 7

              grid%elem(ielem)%e2n_2d(4,1) = 7
              grid%elem(ielem)%e2n_2d(4,2) = 5

            case (3)

              grid%elem(ielem)%e2n_2d(1,1) = 5
              grid%elem(ielem)%e2n_2d(1,2) = 7

              grid%elem(ielem)%e2n_2d(2,1) = 7
              grid%elem(ielem)%e2n_2d(2,2) = 3

              grid%elem(ielem)%e2n_2d(3,1) = 3
              grid%elem(ielem)%e2n_2d(3,2) = 1

              grid%elem(ielem)%e2n_2d(4,1) = 1
              grid%elem(ielem)%e2n_2d(4,2) = 5

            case (4)

              grid%elem(ielem)%e2n_2d(1,1) = 6
              grid%elem(ielem)%e2n_2d(1,2) = 2

              grid%elem(ielem)%e2n_2d(2,1) = 2
              grid%elem(ielem)%e2n_2d(2,2) = 4

              grid%elem(ielem)%e2n_2d(3,1) = 4
              grid%elem(ielem)%e2n_2d(3,2) = 8

              grid%elem(ielem)%e2n_2d(4,1) = 8
              grid%elem(ielem)%e2n_2d(4,2) = 6

            case (5)

              grid%elem(ielem)%e2n_2d(1,1) = 5
              grid%elem(ielem)%e2n_2d(1,2) = 1

              grid%elem(ielem)%e2n_2d(2,1) = 1
              grid%elem(ielem)%e2n_2d(2,2) = 2

              grid%elem(ielem)%e2n_2d(3,1) = 2
              grid%elem(ielem)%e2n_2d(3,2) = 6

              grid%elem(ielem)%e2n_2d(4,1) = 6
              grid%elem(ielem)%e2n_2d(4,2) = 5

            case (6)

              grid%elem(ielem)%e2n_2d(1,1) = 7
              grid%elem(ielem)%e2n_2d(1,2) = 8

              grid%elem(ielem)%e2n_2d(2,1) = 8
              grid%elem(ielem)%e2n_2d(2,2) = 4

              grid%elem(ielem)%e2n_2d(3,1) = 4
              grid%elem(ielem)%e2n_2d(3,2) = 3

              grid%elem(ielem)%e2n_2d(4,1) = 3
              grid%elem(ielem)%e2n_2d(4,2) = 7

            end select

        case ('prz')

          select case (face_2d)

            case (1)

              grid%elem(ielem)%e2n_2d(1,1) = 2
              grid%elem(ielem)%e2n_2d(1,2) = 3

              grid%elem(ielem)%e2n_2d(2,1) = 3
              grid%elem(ielem)%e2n_2d(2,2) = 5

              grid%elem(ielem)%e2n_2d(3,1) = 5
              grid%elem(ielem)%e2n_2d(3,2) = 2

            case (2)

              grid%elem(ielem)%e2n_2d(1,1) = 1
              grid%elem(ielem)%e2n_2d(1,2) = 6

              grid%elem(ielem)%e2n_2d(2,1) = 6
              grid%elem(ielem)%e2n_2d(2,2) = 4

              grid%elem(ielem)%e2n_2d(3,1) = 4
              grid%elem(ielem)%e2n_2d(3,2) = 1

            end select

        case default

      end select

    end do

    call lmpi_conditional_stop(0,'4:twod_util:setup_2d')

  end subroutine setup_2d

!=============================== SET_FACE2D ==================================80
!
! To set face2d variable for 2D simulations
! Note: nearly a clone of check_cell_2d in party
!
!=============================================================================80

  subroutine set_face_2d(grid, element_set)

    use grid_types, only : grid_type
    use info_depr,  only : skeleton

    integer,         intent(in)    :: element_set
    type(grid_type), intent(inout) :: grid

    integer :: cell, not_on_yplane_2d, iface, total_faces
    integer :: nn1, nn2, nn3, nn4, n1, n2 ,n3, n4, nface

    real(dp) :: y1_chk, y2_chk, y3_chk, y4_chk

    integer, save :: candidate_face = 0, calls = 0

    logical :: solve_plane_found

  continue

! can have ncell=0 with  multiple processors
    if (grid%elem(element_set)%ncell == 0) return

    solve_plane_found = .false.

    if (trim(grid%elem(element_set)%type_cell) == 'hex') then
      nface = 6
    else if (trim(grid%elem(element_set)%type_cell) == 'prz') then
      nface = 2
    else
      nface = -1
    end if

    if ( nface == -1 ) then
      write(*,*) 'error in set_face_2d...cell type not hex or prz'
      stop ! FIXME: should be lmpi_die or se_exit(1)?
    endif

    candidate_face_loop : do iface=1,nface

!     yplane_2d and face_2d together identify the primary 2D plane/face

      not_on_yplane_2d = 0
      total_faces      = 0

      do cell = 1,grid%elem(element_set)%ncell

        nn1 = grid%elem(element_set)%local_f2n(iface,1)
        nn2 = grid%elem(element_set)%local_f2n(iface,2)
        nn3 = grid%elem(element_set)%local_f2n(iface,3)
        nn4 = grid%elem(element_set)%local_f2n(iface,4)

        n1 = grid%elem(element_set)%c2n(nn1,cell)
        n2 = grid%elem(element_set)%c2n(nn2,cell)
        n3 = grid%elem(element_set)%c2n(nn3,cell)

        total_faces = total_faces + 1

        y1_chk = abs(grid%y(n1) - yplane_2d)
        y2_chk = abs(grid%y(n2) - yplane_2d)
        y3_chk = abs(grid%y(n3) - yplane_2d)

        if (nn4 == nn1) then

          if (y1_chk > y_coplanar_tol .or. y2_chk > y_coplanar_tol  .or.       &
              y3_chk > y_coplanar_tol                             ) then

            not_on_yplane_2d = not_on_yplane_2d + 1

          end if

        else

          n4 = grid%elem(element_set)%c2n(nn4,cell)

          y4_chk = abs(grid%y(n4) - yplane_2d)

          if (y1_chk > y_coplanar_tol .or. y2_chk > y_coplanar_tol  .or.       &
              y3_chk > y_coplanar_tol .or. y4_chk > y_coplanar_tol) then

            not_on_yplane_2d = not_on_yplane_2d + 1

          end if

        end if

      end do

      if((not_on_yplane_2d == 0) .and. .not.solve_plane_found) then
        candidate_face = iface
        solve_plane_found = .true.
      elseif(not_on_yplane_2d == 0) then
        write(*,*)
        write(*,*) ' Fatal error in set_face_2d...stopping.'
        write(*,*) ' Considering the element type : ',&
          grid%elem(element_set)%type_cell
        write(*,*) ' And the topological face : ',iface
        write(*,*) ' This topological face is on the 2d plane.'
        write(*,'(1x,a,e11.4,a)') ' (within a tolerance of ',y_coplanar_tol,')'
        write(*,*) ' But another face on the 2d plane was already found!'
        write(*,*) ' Faces considered on this topological face =',total_faces
        write(*,*) ' Total cells on this processor=',&
          grid%elem(element_set)%ncell
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      elseif(not_on_yplane_2d /= total_faces ) then
        write(*,*)
        write(*,*) ' Fatal error in set_face_2d...stopping.'
        write(*,*) ' Considering the element type : ',&
          grid%elem(element_set)%type_cell
        write(*,*) ' And the topological face : ',iface
        write(*,*) ' Some points on this topological face are on the 2d plane.'
        write(*,'(1x,a,e11.4,a)') ' (within a tolerance of ',y_coplanar_tol,')'
        write(*,*) ' All should be or none should be.'
        write(*,*) ' Faces with points on the 2D plane =',                     &
                   total_faces-not_on_yplane_2d
        write(*,*) ' Faces considered on this topological face =',total_faces
        write(*,*) ' Total cells on this processore=',&
          grid%elem(element_set)%ncell
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end if
    enddo candidate_face_loop

!   Check that a valid solve plane has been found

    if (.not.solve_plane_found) then
      write(*,*)
      write(*,*) ' Fatal error in set_face_2d...stopping.'
      write(*,'(a,i8,a,i8,3a,f7.3,a)') ' STOPPING: ',not_on_yplane_2d,' of ',  &
                                     total_faces,' ',                          &
                                     grid%elem(element_set)%type_cell,         &
                                     ' faces are not on the y = ',yplane_2d,   &
                                     ' plane'
      write(*,'(a,e11.4,a)') ' (within a tolerance of ',y_coplanar_tol,')'
      write(*,'(2a)') ' YOU WILL NOT BE ABLE TO RUN THIS MESH IN THE 2D MODE', &
                      ' EITHER FOR VISCOUS CASES',                             &
                      ' OR MIXED ELEMENT DUAL FACE INTEGRATIONS'
      stop ! FIXME: should be lmpi_die or se_exit(1)?
    end if

    grid%elem(element_set)%face_2d = candidate_face

    if(skeleton > 0 ) write(*,*) ' Element_type=',        &
      grid%elem(element_set)%type_cell,'  face_2d=',      &
                                 grid%elem(element_set)%face_2d
    calls = calls + 1

  end subroutine set_face_2d

!================================== COPY_DQ_2D ===============================80
!
! Copy solution increment dq from one yplane to another for 2D cases
!
!=============================================================================80

  subroutine copy_dq_2d( ndq, dq, nnodes0_2d, node_pairs_2d)

    integer,                            intent(in)    :: ndq, nnodes0_2d
    integer,   dimension(2,nnodes0_2d), intent(in)    :: node_pairs_2d
    real(dqp), dimension(:,:),          intent(inout) :: dq

    integer :: n, node1, node2, eqn

    continue

    do n=1,nnodes0_2d

      node1 = node_pairs_2d(1,n)
      node2 = node_pairs_2d(2,n)

      do eqn = 1,ndq
        dq(eqn,node2) = dq(eqn,node1)
      end do

    end do

  end subroutine copy_dq_2d

!================================== COPY_ARRAY_2D ============================80
!
! Copy array from one yplane to another for 2D cases
!
!=============================================================================80

  subroutine copy_array_2d(nnodes01, nnodes0_2d, node_pairs_2d,                &
                           array, lead_dim )

    use kinddefs, only : dp

    integer, intent(in) :: nnodes01, nnodes0_2d, lead_dim

    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(dp), dimension(lead_dim, nnodes01), intent(inout) :: array

    integer :: n, node1, node2

    continue

    do n=1,nnodes0_2d

      node1 = node_pairs_2d(1,n)
      node2 = node_pairs_2d(2,n)

      array(:,node2) = array(:,node1)

    end do

  end subroutine copy_array_2d

!================================== COPY_TWOD_R4 =============================80
!
! Copy array from one yplane to another for 2D (node-centered data).
!
!=============================================================================80

  subroutine copy_twod_r4( nnodes0_2d, node_pairs_2d, array )

    use kinddefs, only : r4

    integer, intent(in) :: nnodes0_2d

    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(r4), dimension(:,:), intent(inout) :: array

    integer :: n, node1, node2

    continue

    do n=1,nnodes0_2d

      node1 = node_pairs_2d(1,n)
      node2 = node_pairs_2d(2,n)

      array(:,node2) = array(:,node1)

    end do

  end subroutine copy_twod_r4

!================================== COPY_TWOD_R8 =============================80
!
! Copy array from one yplane to another for 2D (node-centered data).
!
!=============================================================================80

  subroutine copy_twod_r8( nnodes0_2d, node_pairs_2d, array )

    use kinddefs, only : r8

    integer, intent(in) :: nnodes0_2d

    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(r8), dimension(:,:), intent(inout) :: array

    integer :: n, node1, node2

    continue

    do n=1,nnodes0_2d

      node1 = node_pairs_2d(1,n)
      node2 = node_pairs_2d(2,n)

      array(:,node2) = array(:,node1)

    end do

  end subroutine copy_twod_r8

!============================== CHECK_SKIP_SIDEWALLS =========================80
!
! Ensure we have pairs of y-symmetry boundary conditions.
!
!=============================================================================80
  subroutine check_skip_sidewalls( grid )

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_master, lmpi_die
    use bc_names,   only : symmetry_y
    use info_depr,  only : skeleton, cc_primal

    type(grid_type), intent(in) :: grid

    integer :: ib, sidewalls
    logical :: skip_sidewalls

  continue

    sidewalls = 0
    skip_sidewalls = .false.
    do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc /= symmetry_y ) cycle
      sidewalls = sidewalls + 1
    enddo

    if ( sidewalls > 0 .and. ( sidewalls == (sidewalls/2)*2 ) ) then
      skip_sidewalls = .true.
    endif

    if ( lmpi_master .and. skeleton > 0 ) then
      write(*,*) ' 2D Cell-Centered...sidewalls=',sidewalls
    endif

    if( skip_sidewalls ) return

    if ( lmpi_master  ) then
      write(*,*) ' 2D Cell-Centered Inconsistency...stopping.'
      write(*,*) ' ...sidewalls=',sidewalls
      write(*,*) ' ...grid%cc=',grid%cc
      write(*,*) ' ...cc_primal=',cc_primal
      write(*,*) ' ...need pairs of y-symmetry bcs.'
      write(*,*) ' ...stopping in check_skip_sidewalls'
      do ib = 1, grid%nbound
        write(*,*) ' .ib,grid%bc(ib)=',ib,grid%bc(ib)%ibc
      enddo
    endif
    call lmpi_die

  end subroutine check_skip_sidewalls


!================================== SET_RES_TWOD =============================80
!
! Set component of 3D residual for 2D node-centered path.
! ..usually the y component of the momentum residual (for flow solves).
! ..but for grid moves, it is the 2nd component.
!
!=============================================================================80

  subroutine set_res_twod( eqn_set, res, qnode )

    use generic_gas_map, only : n_momy
    use solution_types,  only : elasticity

    integer,                  intent(in)    :: eqn_set
    real(dp), dimension(:,:), intent(inout) :: qnode
    real(dp), dimension(:,:), intent(inout) :: res

    integer :: eqn_2d, node

    real(dp), parameter :: desired_value = 0._dp

  continue

    eqn_2d = n_momy
    if ( eqn_set == elasticity ) eqn_2d = 2

    do node=1,size(res,2)
      res(eqn_2d,node) = qnode(eqn_2d,node) - desired_value
    enddo

  end subroutine set_res_twod

!=================================== SET_JAC_TWOD ===========================80
!
! Account for 2D cases by setting components of the 3D jacobians:
! ..usually the y component of the momentum residual (for flow solves).
! ..but for grid moves, it is the y component of the residual.
!
!=============================================================================80

  subroutine set_jac_twod( eqn_set, a_diag, a_off )

    use generic_gas_map, only : n_momy
    use solution_types,  only : elasticity

    integer,  intent(in) :: eqn_set

    real(dp),  dimension(:,:,:), intent(inout) :: a_diag
    real(odp), dimension(:,:,:), intent(inout) :: a_off

    integer :: eqn_2d, node, entry

  continue

    eqn_2d = n_momy
    if ( eqn_set == elasticity ) eqn_2d = 2

    do node=1,size(a_diag,3)
      a_diag(eqn_2d,     :,node) = 0._dp
      a_diag(     :,eqn_2d,node) = 0._dp
      a_diag(eqn_2d,eqn_2d,node) = 1._dp
    enddo

    do entry=1,size(a_off,3)
      a_off(eqn_2d,     :,entry) = 0._odp
      a_off(     :,eqn_2d,entry) = 0._odp
    enddo

  end subroutine set_jac_twod

end module twod_util

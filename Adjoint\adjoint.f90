program adjoint

  use grid_types,            only : grid_type, raw_grid_data_type
  use solution_types,        only : soln_type, elasticity, compressible
  use eqn_groups,            only : process_eqn_groups
  use kinddefs,              only : dp, system_r8
  use allocations,           only : my_alloc_ptr
  use nml_code_run_control,  only : absolute_stopping_tolerance, iterwrt,      &
                                    duration_limit_in_minutes,                 &
                                    jacobian_eval_freq, smart_jupdate,         &
                                    no_restart
  use info_depr,             only : ntt, twod, ivisc, ngrid, ncyc, mixed,      &
                                    skeleton, simulation_time,                 &
                                    time_timestep_loop, physical_timestep,     &
                                    subiteration, cc_primal, adapt
  use nml_nonlinear_solves,  only : subiters, itime, dt
  use nml_noninertial_reference_frame, only : noninertial
  use sampling_headers,      only : number_of_geometries, sampling_frequency
  use nml_global,            only : moving_grid, volume_animation_freq,        &
                                    boundary_animation_freq, irest
  use cut_types,             only : cut_cell_activated, cut_cell_visualize, cut
  use cut_utils,             only : cut_cell_preprocess
  use cut_visualizations,    only : cut_cell_adj_vis, cut_surface_adj_tec
  use cfl_defs,              only : cfl1, cfl2, cflturb1, cflturb2
  use grids,                 only : nullify_grid
  use grid_motion_helpers,   only : initialize_a_moving_body,                  &
                                    initialize_all_moving_bodies,              &
                                    set_up_moving_body,                        &
                                    set_specified_rigid_motion_data,           &
                                    check_if_grid_velocity_needed
  use nml_grid_motion,       only : six_dof
  use io,                    only : relax_schedule_gridmove, prior_iters,      &
                                    use_prior, write_timestep_timing, readme,  &
                                    project_rootname
  use main_nml,              only : main_nml_read, read_fun3d_nml,             &
                                    write_namelist_docs
  use bc_names,              only : bc_allowed_for_adjoint,                    &
                                    bc_explicitly_initialized,                 &
                                    bc_allowed_for_getgrad
  use design_types,          only : design_type, cpstar_data
  use design_io_helpers,     only : set_up_design
  use designs,               only : gettag, write_design, slice_for_cpstar,    &
                                    moving_design_setup
  use timedep_sensitivities, only : unsteady_getgrad, set_up_grid_at_timelevel,&
                                    update_temporal_adj_backplanes,            &
                                    update_geometric_backplanes
  use solution_writes,       only : write_vol_tec, solution_output,            &
                                    output_global_surface_data,                &
                                    end_native_volume_data_write
  use solution,              only : nullify_soln, set_global_scalars,          &
                                    load_qnode_from_timelevel, set_up_neq,     &
                                    set_viscous_method, load_overset_metadata
  use solution_adj,          only : sadj_type, set_up_dual_solution, rresta,   &
                                    wresta
  use lmpi,                  only : lmpi_id, lmpi_start, lmpi_conditional_stop,&
                                    lmpi_master, lmpi_bcast, lmpi_synchronize, &
                                    lmpi_finalize, lmpi_io => lmpi_mpiio_type, &
                                    lmpi_duration
  use lmpi_app,              only : lmpi_xferedge, lmpi_xfer
  use comprow_types,         only : crow_type, crow_flow
  use comprow,               only : set_up_comprow, set_up_comprow_flow
  use flow_initialization,   only : init_flowfield, init_freestream,           &
                                    initialize_backplanes
  use init_adjs,             only : init_adj
  use thermo,                only : etop, ptoe
  use timings,               only : wall_cr_inv, checkpoint, count_max,        &
                                    nrollovers
  use gradient_driver,       only : sumgs_variable
  use bcs,                   only : bc_init_nc
  use bc_gen,                only : bc_init_gen
  use relax_adjoint,         only : relax, new_gcr, read_lambda_chaos,         &
                                    krylov_initialize, krylov_relax,           &
                                    build_lhs_driver_jacobians, decompose_adj, &
                                    zero_out_lambda, entropy_transform,        &
                                    build_lhs_driver_jacobians_turb,           &
                                    adjoint_history, setup_bp
  use fill_jacobians,        only : transpose_driver_jacobians
  use force_driver,          only : compute_forces
  use adjoint_switches,      only : always_recompute, outer_loop_krylov,       &
                                    rad, locally_optimal, getgrad, rn, np,     &
                                    debug_linearizations, store_full_stencil,  &
                                    new_krylov, get_dldx, use_bp_model,        &
                                    read_for_chaos, write_for_chaos
  use residual_drdq,         only : store_aa, write_drdqt
  use command_line_parser,   only : usage, read_command_line
  use input_sanity,          only : check_input_sanity, check_compatibility
  use turb_util,             only : edget
  use code_status,           only : code_id, adjoint_code_id, rad_code_id
  use twod_util,             only : setup_2d
  use radp,                  only : embed_rad, single_rad, inria_rad, &
                                    inria_rad_time_adapt
  use adaptation_parameter,  only : inria_opt_goal_metric_time
  use system_extensions,     only : se_flush, se_sleep, se_shell
  use file_utils,            only : rm
  use relax_types,           only : relax_type
  use moving_body_types,     only : moving_body, observer,                     &
                                    specified_rigid_motion
  use nml_grid_motion,       only : n_moving_bodies
  use grid_metrics,          only : compute_dual_metrics
  use rotors,                only : init_rotor_source, source2node_assoc,      &
                                    write_source_grid, rotor_flag
  use timeacc,               only : new_backwards_timestep, timestep_scalars
  use timeacc_coeffs,        only : set_time_coeffn, set_time_coeff2, dt_vec
  use residual,              only : evaluate_terminal_condition
  use sixdof,                only : cleanup_sixdof
  use overset,               only : blank_lhs_equation, blank_lhs_turb_equation
  use nml_overset_data,      only : overset_flag
  use dirtlib,               only : init_overset, init_dirtlib_communication
  use utilities,             only : check_for_stop, my_clock
  use noninertials,          only : setup_nonin, setup_nonin_observer
  use distance_function,     only : compute_distance_function
  use split_comm,            only : split_communicator
  use custom_transforms,     only : update_thrust_angle, tilt_rotor
  use pparty_preprocessor,   only : pparty_preprocess, pparty_setup_stuff
  use getgrad_driver,        only : steady_getgrad,                            &
                                    echo_sensitivity_at_perturbation
  use refine_adaptation_input, only : adapt_error_estimation
  use nml_relax_schedule,    only : relax_schedule_linear
  use sampling_main,         only : survey
  use tecplot_io_helpers,    only : bypass_solver, set_strand_usage
  use solution_globals,      only : set_up_global_bndry_data,                  &
                                    deallocate_global_bndry_data
  use forces,                only : entropy_transform_id
  use global_image,          only : global_image_export_to
  use nml_volume_output,     only : read_nml_volume_output
  use nml_boundary_output,   only : read_nml_boundary_output
  use nml_sampling_output,   only : read_nml_sampling_output
  use suggar,                only : init_libsuggar

  implicit none

  type(sadj_type)          :: sadj       ! Adj Solution
  type(crow_type)          :: crow       ! comp row (adj)
  type(design_type)        :: design     ! Design info
  type(relax_type)         :: relaxation ! Relaxation info
  type(raw_grid_data_type) :: raw_grid_data

  type(grid_type),        dimension(:),   allocatable :: grid
  type(soln_type),        dimension(:),   allocatable :: soln
  type(crow_flow),        dimension(:),   allocatable :: crowf

  integer :: i,ib,ibc,istop,istep,output_target,final_k
  integer :: igeom,sampling_sum
  integer :: starting_time,delta_clicks,subiterations,nphysical_time_steps

  real(dp) :: time_increment
  real(dp) :: start_of_timestep_loop ! Time timestep loop started
  real(dp) :: end_of_timestep_loop   ! Time timestep loop finished

  real(system_r8) :: duration_in_sec

  real(dp), dimension(:,:,:), pointer :: dldx

  logical, parameter :: mpi_debug_sleep    = .false.

  logical :: use_inviscid_bndry, skip_min_wall_spacing

  character(len=80) :: command

  character(80) :: flow_dir, nml_path

  continue

  code_id = adjoint_code_id

  flow_dir = '../Flow/'
  nml_path = '../Flow/fun3d.nml'

  call lmpi_start
  call usage()

  if (mpi_debug_sleep) then
    write(*,*) 'proc', lmpi_id, 'Sleeping...'
    call se_sleep(60)
    write(*,*) 'proc', lmpi_id,'Awake...'
  endif

  call checkpoint(starting_time)

  ngrid = 1

  allocate(grid(ngrid))
  allocate(soln(ngrid))
  allocate(crowf(ngrid))

  call nullify_grid(grid(1))
  call nullify_soln(soln(1))

  call read_fun3d_nml( soln(1)%eqn_set, raw_grid_data, nml_path)

!...Read namelists.

  call main_nml_read(soln(1)%eqn_set,flow_dir,nml_path)

! Read command line arguments

  call read_command_line()

  grid(1)%project = trim(project_rootname)
  call lmpi_bcast( grid(1)%project )

!...Set flag for grid-speed terms

  call check_if_grid_velocity_needed()

!...Echo namelists.

  call write_namelist_docs(6)

  call split_communicator()

  call init_libsuggar()

  call init_dirtlib_communication()

  bypass_solver = .false.

! Check validity of inputs

  call check_input_sanity(flow_dir,soln(1)%eqn_set)

! Debug linearizations or not

  if ( debug_linearizations ) then
    write(*,*) 'Enter residual node'
    read(*,*) rn
    write(*,*) 'Enter node to perturb'
    read(*,*) np
  else
    rn = 0
    np = 0
  endif

! Set skeleton printing flag to be nonzero only on 1 processor

  if(.not.lmpi_master) skeleton = 0

! Read linear nonlinear relaxation schedules.

   if ( soln(1)%eqn_set == elasticity ) then
     call relax_schedule_gridmove(relaxation,flow_dir)
   else
     allocate(relaxation%schedule(6))
     call relax_schedule_linear( relaxation )
   endif

  allocate(moving_body(max(1,n_moving_bodies)))
  call initialize_all_moving_bodies(moving_body)
  call initialize_a_moving_body(observer)
  allocate(specified_rigid_motion(n_moving_bodies))
  call set_specified_rigid_motion_data()

  if ( cut_cell_activated ) then
    always_recompute = .true.
    store_full_stencil = .false.
    if ( lmpi_master ) write(*,*) 'always_recompute forced true for cut cells'
  endif

  if ( adapt ) then
    rad = .true.
    if ( lmpi_master ) write(*,*) 'adapt implies rad for the adjoint solver'
  end if

  if ( rad ) then
    code_id = rad_code_id
    always_recompute = .true.
    store_full_stencil = .false.
    if ( lmpi_master ) write(*,*) 'always_recompute forced true for rad'
  end if

  cant_store_kw_yet : if ( ivisc == 8 ) then
    always_recompute = .true.
    store_full_stencil = .false.
    if ( lmpi_master ) write(*,*) 'always_recompute forced true for 2 equ turb'
  end if cant_store_kw_yet

  if(lmpi_master) write(*,*) 'Code_id=',code_id

! CFL Number Setting Strategy:

! It is assumed that the user ran the flow
! solver far enough to reach CFLMAX during the ramping.  So here in the
! adjoint, we will set CFL1=CFL2, which will guarantee that the diagonal
! piece on the LHS is constructed with the correct CFL number.

  cfl1     = cfl2
  cflturb1 = cflturb2

  jacobian_eval_freq = 1                ! required for curr dual implementation
  smart_jupdate = .false.               ! required for curr dual implementation

! Set global solution scalars

  call set_global_scalars(soln(1))
  call init_freestream( soln(1)%eqn_set, soln(1)%n_tot )
  grid(1)%origin = 1
  grid(1)%igrid  = 1
  grid(1)%cc     = cc_primal
  call set_viscous_method( grid(1), soln(1) )

  if ( lmpi_master .and. (.not.no_restart) .and. (lmpi_io /= 0) ) then
     command = 'touch ' // trim(grid(1)%project) // '.adjoint'
     call se_shell(command)
  endif

! Set some timestep data

  call timestep_scalars()
  dt_vec(:) = dt

  if (.not.raw_grid_data%read_part_files) then
    call pparty_preprocess(flow_dir,raw_grid_data,grid(1))
    call pparty_setup_stuff(grid(1))
  else
    call readme(soln(1)%eqn_set, grid(1),flow_dir)
  endif

  if ( cpstar_data ) call slice_for_cpstar(grid(1))

! Set up the design derived type

  call set_up_design(soln(1)%eqn_set, itime, design, grid(1)%nbound)

! Set up dual metrics

  call compute_dual_metrics( grid(1), moving_grid )

! Perform cut cell operations if requested

  if (cut_cell_activated) call cut_cell_preprocess(grid(1))

! Size number of soln equations

  call set_up_neq( grid(1), soln(1) )

  if ( twod .and. lmpi_master ) write(*,*) 'Using 2D Mode'

! Perform some more sanity checks

  call check_compatibility( soln(1)%eqn_set, 1, grid(1), soln(1) )

! Accomodate best practice model if needed

  if ( use_bp_model ) call setup_bp(grid(1))

! Set up 2D data structures (dummy structures if 3D)

  call setup_2d(grid(1))

! Check to see if our BC's are allowed
! Note that getgrad will run automatically if we are
! running time-dependent

  check_boundary_types : do ib = 1,grid(1)%nbound

    ibc = grid(1)%bc(ib)%ibc

    if ( .not. bc_allowed_for_adjoint( soln(1)%eqn_set, ibc ) .and.            &
          ( grid(1)%bc(ib)%nbfacet > 0 .or. grid(1)%bc(ib)%nbfaceq > 0 ) ) then
      write(*,*)"ERROR: Adjoint can not handle bc type ", ibc
      call lmpi_conditional_stop(1,"Adjoint can not handle bc type")
    endif

    if ( getgrad .or. itime /= 0 ) then
      if ( .not. bc_allowed_for_getgrad(ibc, design%alpha_active, moving_grid, &
           noninertial) .and.                                                  &
         ( grid(1)%bc(ib)%nbfacet > 0 .or. grid(1)%bc(ib)%nbfaceq > 0) ) then
        write(*,*)"ERROR: Getgrad can not handle bc type ", ibc
        call lmpi_conditional_stop(1,"Getgrad can not handle bc type")
      endif
    endif

  end do check_boundary_types
  call lmpi_conditional_stop(0,"Adjoint can not handle bc type")


! Compute the distance function

  if ( .not. cut_cell_activated ) then
    use_inviscid_bndry    = .false.
    skip_min_wall_spacing = .false.
    if ( ivisc == 0 ) then
      use_inviscid_bndry    = .true.
      skip_min_wall_spacing = .true.
    end if
    call compute_distance_function(grid(1), .false.,                           &
                      skip_min_wall_spacing_arg=skip_min_wall_spacing,         &
                      use_inviscid_boundaries_arg = use_inviscid_bndry)
  end if

! Set up soln/sadj derived types

  call set_up_dual_solution(grid(1),soln(1),sadj,design)

! Set up compressed row storage for LHS matrix (diag/off-diag broken out)

  call set_up_comprow_flow(soln(1)%viscous_method, grid(1),crowf(1))

! Set up compressed row storage for stored adjoint matrix

  call set_up_comprow(soln(1)%viscous_method, grid(1),crow)

  soln(1)%max_nnz   = crowf(1)%nnz01

! Get least squares weights for reconstruction

  call sumgs_variable(grid(1),crowf(1))

! Initialize noninertial rotation speeds

  if ( noninertial ) then
    call setup_nonin(grid(1))
    call setup_nonin_observer()
  end if

! Initialize any rotor sources

  if ( rotor_flag ) then
    call init_rotor_source(soln(1)%eqn_set, flow_dir)
    call source2node_assoc(grid(1)%nnodes0,                                    &
                           grid(1)%nnodes01,grid(1)%x,grid(1)%y,grid(1)%z,     &
                           grid(1)%bc,grid(1)%nbound, grid(1)%l2g)
    call write_source_grid(1, 0)
  end if

! Initialize the field (we don't really need qnode to be set to freestream
! since we're just going to load it in from disk, but the freestream scalars
! are also set in here, which we will need)

  call init_flowfield(grid(1), soln(1),nml_path)

  boundary_init : if ( soln(1)%eqn_set < 2 ) then
    do ib = 1,grid(1)%nbound
      if( bc_explicitly_initialized(grid(1)%bc(ib)%ibc, itime) ) then
        call bc_init_nc(grid(1), soln(1), ib, grid(1)%bc(ib))
      endif
    end do
  else
    do ib = 1,grid(1)%nbound
      if ( bc_explicitly_initialized(grid(1)%bc(ib)%ibc, itime) ) then
        call bc_init_gen(grid(1), soln(1), grid(1)%bc(ib), ib)
      endif
    end do
  endif boundary_init

! Ensure any changes in bc solution data are transfered to ghost nodes

  call lmpi_xfer(soln(1)%q_dof)

! Push Q initializations into backplanes

  if ( itime /= 0 ) then
    call initialize_backplanes(soln(1)%n_tot, soln(1)%n_turb,                  &
                               size(soln(1)%q_dof,2), soln(1)%q_dof,           &
                               soln(1)%qatn, soln(1)%qatn1, soln(1)%qatn2,     &
                               soln(1)%qatn3, soln(1)%qatn4, soln(1)%turb,     &
                               soln(1)%turbatn, soln(1)%turbatn1,              &
                               soln(1)%turbatn2, soln(1)%turbatn3,             &
                               soln(1)%turbatn4, grid(1)%x, grid(1)%z,         &
                               soln(1)%eqn_set)
  endif

! Set up equation groups for linear solve

  call process_eqn_groups(grid(1), crowf(1), soln(1), relaxation, 1,&
    flow_dir,nml_path)

  call read_nml_volume_output(soln(1)%n_turb, soln(1)%eqn_set,                 &
                              grid(1)%idistfcn, .true., soln(1)%adim,          &
                              nml_path)
  call read_nml_boundary_output(soln(1)%n_turb, soln(1)%eqn_set,               &
                                grid(1)%idistfcn, .true., soln(1)%adim,        &
                                nml_path)
  call read_nml_sampling_output(soln(1)%n_turb, soln(1)%eqn_set,               &
                                grid(1)%idistfcn, .true., soln(1)%adim,        &
                                nml_path)

  call init_adj(grid(1)%nnodes01, design%nfunctions, sadj%rlam, soln(1)%adim,  &
                soln(1)%eqn_set, soln(1)%n_turb, grid(1)%x, grid(1)%y,         &
                grid(1)%z)

  prior_iters = 0

  if ( rad .and. itime == 0 ) then
    irest = 1
    if(lmpi_master) write(*,*) 'RAD forcing irest to',irest
    use_prior = 1
    if(lmpi_master) write(*,*) 'RAD forcing use_prior to',use_prior
  end if

! Set up LHS jacobian memory

  call my_alloc_ptr(soln(1)%a_off, soln(1)%njac, soln(1)%njac, crowf(1)%nnz01)

  call my_alloc_ptr(soln(1)%a_turb_off, soln(1)%n_turb, soln(1)%n_turb,        &
                    crowf(1)%nnz01)

! Set up stored adjoint jacobian memory

  if ( store_full_stencil ) then
    call my_alloc_ptr(sadj%aa,soln(1)%adim,soln(1)%adim,crow%nnz01)
  else if ( always_recompute .or. ivisc == 0 ) then
    call my_alloc_ptr(sadj%aa,1,1,1)
  else
    call my_alloc_ptr(sadj%aa,soln(1)%adim,soln(1)%adim,crow%nnz0)
  end if

! Load any existing adjoint solution

  if (irest == 1) call rresta(grid(1),design,soln(1),sadj,'')

  do i = 1, design%nfunctions
    call lmpi_xfer(sadj%rlam(:,:,i))
  end do

! Set up moving_body data

  call set_up_moving_body(grid(1), soln(1))

! If dynamic grid case, ensure that the moving bodies are
! consistent with the design bodies

  if ( moving_grid ) call moving_design_setup(design)

! Initialize the FUN3D-DiRTlib interface

  if (overset_flag) then
    call init_overset(grid(1), soln(1), crowf(1),.true.,                       &
                      raw_grid_data, flow_dir, .false.)
    if ( moving_grid ) call load_overset_metadata(grid(1)%project)
  endif

! Set up column tag array

  call gettag(grid(1),soln(1)%adim,sadj%coltag)

! Set up of boundary/volume data output for animation, etc

  if ( boundary_animation_freq(1) /= 0 ) then
    call set_up_global_bndry_data(grid(1), soln(1)%global_bndry_data,          &
                                  soln(1)%n_tot, soln(1)%n_turb,               &
                                  soln(1)%n_grd, soln(1)%njac, soln(1)%adim)
    call output_global_surface_data(grid(1),soln(1),0,.true.)
  end if

  if (volume_animation_freq(1) /= 0 ) then
    call write_vol_tec(grid(1),soln(1),0,sadj)
  endif

  sampling_sum = 0
  sampling_sum = sum(abs(sampling_frequency(1:number_of_geometries)))
  if ( sampling_sum /= 0 ) then
    call survey( grid(1),soln(1),0,nml_path,sadj)
  end if

  call set_strand_usage(bypass_solver, report_usage=.false.)

! Output Tecplot files of the initial solution on selected surfaces or
! throughout entire volume

  call solution_output(grid(1), soln(1), 0, nml_path, sadj)

! If using outer Krylov wrapper, the following frequencies will later
! be based on search direction indices once they are established

  if ( .not. new_krylov ) then
    output_target = ncyc
    if ( itime /= 0 ) output_target = ncyc + 1   ! +1 for freestream BC

    if (boundary_animation_freq(1) < 0) boundary_animation_freq(1)=output_target
    if (volume_animation_freq(1) < 0)     volume_animation_freq(1)=output_target
    do igeom = 1,number_of_geometries
      if (sampling_frequency(igeom) < 0) sampling_frequency(igeom)=output_target
    end do
  endif

! If evaluating the sensitivities of the cost function to the coords of every
! grid point, allocate an array to store them

  if ( get_dldx ) then
    call my_alloc_ptr(dldx,3,grid(1)%nnodes0,design%nfunctions)
  else
    call my_alloc_ptr(dldx,1,1,1)
  endif

  if ( time_timestep_loop ) call my_clock(start_of_timestep_loop)

! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+
! Physical time-stepping loop for the adjoint solver:
! 1 pass through for steady-state computations
! ncyc+1 passes through for time-accurate computations (including freestream BC)
! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+

  nphysical_time_steps = 1
  if ( itime /= 0 ) nphysical_time_steps = ncyc + 1  ! +1 for freestream BC

  physical_time_stepping : do istep = 1, nphysical_time_steps

    physical_timestep = ncyc-istep+1

    if ( itime /= 0 ) simulation_time = physical_timestep*dt

    if ( itime /= 0 .and. lmpi_master ) then
      write(*,*)
      write(*,'(1x,a34,i8)') '------ START OF PHYSICAL TIMESTEP ',             &
      physical_timestep
      write(*,*)
    endif

! Compute time step, shuffle dt's, compute coefficients needed for
! specified temporal accuracy, store backplanes

    call new_backwards_timestep()
    call set_time_coeffn()
    call set_time_coeff2()
    call update_temporal_adj_backplanes(sadj)
    if ( moving_grid ) call update_geometric_backplanes(grid(1))
    if ( tilt_rotor ) call update_thrust_angle(simulation_time)

! If we have marched back to the initial state, evaluate the terminal
! condition and kick out of physical time-stepping loop

    terminal_condition : if ( physical_timestep == 0 ) then
      call load_qnode_from_timelevel(grid(1),soln(1),sadj,1,0)
      if ( moving_grid ) then
        call set_up_grid_at_timelevel(grid(1),.true.,crowf(1))
      endif
      call evaluate_terminal_condition(grid(1),soln(1),design,sadj)
      call solution_output(grid(1),soln(1),istep,nml_path,sadj)
      call unsteady_getgrad(design,grid,sadj,soln(1),crowf,flow_dir,nml_path,  &
                            istep,dldx)
      exit physical_time_stepping
    endif terminal_condition

! Load up appropriate Q's to get the LHS Jacobians

    call load_qnode_from_timelevel(grid(1),soln(1),sadj,physical_timestep,     &
                                   subiters)

! Position grid, get speeds, metrics, etc for this physical timestep

    if ( moving_grid ) then
      call set_up_grid_at_timelevel(grid(1),.false.,crowf(1))
    endif

! set the turbulent edge weights for tet cases

    if ( ivisc == 6 .and. .not. mixed ) then
      if ( soln(1)%eqn_set == compressible ) then
        call etop(grid(1)%nnodes01,soln(1)%q_dof,soln(1)%n_tot,soln(1)%eqn_set)
      endif
      call edget(soln(1)%eqn_set, grid(1)%nnodes01, grid(1)%elem(1)%ncell,     &
                 grid(1)%nedgeloc, grid(1)%elem(1)%c2n, grid(1)%elem(1)%c2e,   &
                 grid(1)%x, grid(1)%y,grid(1)%z, soln(1)%q_dof, soln(1)%turb,  &
                 soln(1)%dft1, soln(1)%dft2, soln(1)%n_turb, soln(1)%n_tot, 0)
      if ( soln(1)%eqn_set == compressible ) then
        call ptoe(grid(1)%nnodes01,soln(1)%q_dof,soln(1)%n_tot,soln(1)%eqn_set)
      endif
      call lmpi_xferedge(soln(1)%dft1)
      call lmpi_xferedge(soln(1)%dft2)
    endif

! Form LHS meanflow driver jacobians

    call build_lhs_driver_jacobians(grid(1),soln(1),crowf(1))

! If we are running turbulent, we will need those jacobians also

    turb_jacobians : if ( ivisc >= 6 ) then
      call build_lhs_driver_jacobians_turb(grid(1),soln(1),crowf(1))
    endif turb_jacobians

! Transpose the LHS driver jacobians

    call transpose_driver_jacobians(grid(1),soln(1),crowf(1))

! Blanking for overset

    if ( overset_flag ) then
      call blank_lhs_equation(grid(1)%nnodes0, soln(1)%neq01, soln(1)%njac,    &
                              soln(1)%max_nnz, grid(1)%iblank, crowf(1)%iam,   &
                              soln(1)%a_diag, soln(1)%a_off, crowf(1)%g2m)
      if ( ivisc >= 6 ) then
        call blank_lhs_turb_equation(grid(1)%nnodes0, soln(1)%neq01,           &
                                     soln(1)%n_turb, soln(1)%max_nnz,          &
                                     grid(1)%iblank, crowf(1)%iam,             &
                                     soln(1)%a_turb_diag, soln(1)%a_turb_off,  &
                                     crowf(1)%g2m)
      endif
    end if

! Decompose the jacobians

    call decompose_adj( grid(1), soln(1), crowf(1) )

! If steady run, load up the latest Q's to form the adjoint residual at the
! current physical timestep (unsteady simply uses the same Q's for everything)

! Entropy transform if desired

    if ( trim(design%function_data(1)%component_data(1)%name) ==               &
         trim(entropy_transform_id) ) then
      call entropy_transform(grid(1),soln(1),sadj,design,nml_path)
      call lmpi_finalize
      if (lmpi_master) write(*,*) 'entropy transform done.'
      stop
    endif

! Get the current forces

    call compute_forces(grid(1),soln(1),1)
    if ( lmpi_master ) then
      write (6,'("       Lift",e23.15,"         Drag",e23.15)')                &
      soln(1)%totforce(1)%cl, soln(1)%totforce(1)%cd
      call se_flush()
    endif

! If we have used Qiqi's process to determine the adjoint solution, simply
! load it in and skip the solve below

    if ( read_for_chaos ) then
      call read_lambda_chaos(grid(1)%nnodes01, grid(1)%nnodesg, grid(1)%l2g,   &
                             sadj%rlam)
    endif

! store the linearizations explicitly if requested

    if ( .not. read_for_chaos ) then
      if(store_full_stencil.or.(.not.always_recompute.and.(.not.ivisc==0))) then
        call store_aa(grid(1),soln(1),sadj,crow,design)
      endif
    endif

! Write out dR/dQ transpose for testing

    if ( write_for_chaos ) then
      call write_drdqt(grid(1)%nnodes01,grid(1)%nnodesg,grid(1)%l2g,sadj%aa,   &
                       crow%ia,crow%ja)
    endif

! error estimation if requested

    if ( rad .and. itime == 0 ) then
      select case( adapt_error_estimation )
      case('single')
        call single_rad(grid(1),soln(1),sadj,crow,crowf(1),design,raw_grid_data)
      case('embed')
        call embed_rad(grid(1),soln(1),sadj,design,raw_grid_data,nml_path)
      case('opt-goal')
        call inria_rad(grid(1),soln(1),sadj,design)
      case default
        if (lmpi_master) write(*,*) "unknown, adapt_error_estimation of ",&
          trim(adapt_error_estimation)
      end select
      call lmpi_finalize()
      stop
    end if

! Now solve the adjoint problem

    gcr : if ( new_krylov ) then

      call new_gcr(grid(1),soln(1),sadj,crow,crowf(1),design,relaxation,       &
                   nml_path,starting_time,istep,final_k)

    else gcr

! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+
! Subiterative loop for the adjoint solver:
! ncyc passes through for steady-state computations
! subiters passes through for time-accurate computations
! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+

      subiterations = ncyc

      if ( itime /= 0 )     subiterations = subiters
      if ( read_for_chaos ) subiterations = 0

      reverse_time_step_loop : do ntt = 1, subiterations

        subiteration = ntt

        if ( ntt == 1 ) then
          soln(1)%time1 = starting_time
        else
          soln(1)%time1 = soln(1)%time2
        endif

        if ( itime /= 0 .or. ntt == 1 ) then
          outer_loop_initialize : if ( outer_loop_krylov ) then
            call krylov_initialize(grid(1),soln(1),sadj,crow,crowf(1),design,  &
                                   nml_path)
          end if outer_loop_initialize
        endif

! Relax the adjoint system

        outer_loop_relax : if ( outer_loop_krylov ) then
          call krylov_relax(grid(1),soln(1),sadj,crow,crowf(1),design,         &
                            relaxation,ntt,istep,nml_path)
        else
          call relax( grid(1), soln(1), sadj, crow, crowf(1), design,          &
                      relaxation, ntt, istep, nml_path, .true. )
        end if outer_loop_relax

        call checkpoint(soln(1)%time2)

        delta_clicks = soln(1)%time2 - soln(1)%time1
        if ( delta_clicks < 0 ) then
          nrollovers   = nrollovers + 1
          delta_clicks = count_max + delta_clicks
        endif
        time_increment = wall_cr_inv*real(delta_clicks,dp)

        if ( itime == 0 ) then
          if ( ntt == 1 ) then
            soln(1)%walltime(ntt) = time_increment
          else
            soln(1)%walltime(ntt) = soln(1)%walltime(ntt-1) + time_increment
          endif
        else
          if ( istep == 1 ) then
            soln(1)%walltime(istep) = time_increment
          else
            soln(1)%walltime(istep) = soln(1)%walltime(istep-1) + time_increment
          endif
        endif

        if ( itime == 0 ) then
          dump_sadj1: if (ntt/iterwrt*iterwrt == ntt .and. ntt /= ncyc) then
            call wresta(grid(1),soln(1),sadj,design)
            use_prior = 0 ! prior history is for flow
            if(lmpi_master) call adjoint_history(grid(1),soln(1),soln(1)%adim, &
                                                 ntt)
          endif dump_sadj1
          call solution_output(grid(1), soln(1), ntt,nml_path,sadj)
          if (ntt >= 4 .and.                                                &
              soln(1)%rmshist(1,ntt,1) <= absolute_stopping_tolerance) then
            exit reverse_time_step_loop
          endif
        end if

!.....Check to see we are out of time

        if (lmpi_master .and. ( duration_limit_in_minutes > 0.0_dp) ) then
          call lmpi_duration(duration_in_sec)
          if ( duration_in_sec/60.0_dp >= duration_limit_in_minutes) then
            write(*,*) "run time ", duration_in_sec/60.0_dp, " exceeds",       &
              " duration_limit_in_minutes of ",duration_limit_in_minutes
            exit reverse_time_step_loop
          end if
        end if

! Check to see if user wants to stop the execution

        call check_for_stop(istop)

        if ((ntt >= istop) .and. istop > 0) then
          exit reverse_time_step_loop
        end if

!   Reset printing flag back to zero

        skeleton = 0
        call se_flush()

        if ( write_for_chaos ) exit reverse_time_step_loop

      end do reverse_time_step_loop

! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+
! End of subiterative loop for the adjoint solver
! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+

    endif gcr

! If time-dependent, dump out current solution and go call getgrad

    if ( itime /= 0 ) then

      dump_sadj2: if (istep/iterwrt*iterwrt == istep .and. istep /= ncyc) then
        use_prior = 0 ! prior history is for flow
        if(lmpi_master) call adjoint_history(grid(1),soln(1),soln(1)%adim,istep)
      endif dump_sadj2

      call solution_output(grid(1),soln(1),istep,nml_path,sadj)

      call unsteady_getgrad(design,grid,sadj,soln(1),crowf,flow_dir,nml_path,  &
                            istep,dldx)
      if ( rad ) then
        call inria_opt_goal_metric_time(grid(1),soln(1),sadj)
      end if

    endif

! If steady-state, call getgrad pieces if desired

    if ( itime == 0 .and. getgrad ) then
      call steady_getgrad(grid,soln(1),sadj,design,nml_path,dldx)
    endif

! Write dldx so that rad can use it
    if ( get_dldx ) then
      if (design%nfunctions == 1 ) then
        call global_image_export_to( "raw_ascii",                              &
          trim(grid(1)%project)//".grid_sensitivities", grid(1), dldx(:,:,1) )
      else
        if ( lmpi_master ) write(*,*) 'Set nfunctions = 1 to export dldx'
      end if
    endif

! If running locally optimal scheme, perhaps knock out the current lambdas

    if ( locally_optimal ) call zero_out_lambda(sadj)

  end do physical_time_stepping

! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+
! End of Physical time-stepping loop for the adjoint solver
! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+

  if ( time_timestep_loop ) then
    call my_clock(end_of_timestep_loop)
    call write_timestep_timing(start_of_timestep_loop,end_of_timestep_loop)
  endif

  ntt = min(ntt,ncyc)

  if ( new_krylov ) then
    if ( lmpi_master ) call adjoint_history(grid(1),soln(1),1,final_k)
  else
    if ( lmpi_master ) call adjoint_history(grid(1),soln(1),soln(1)%adim,ntt)
  endif

  ntt = ncyc

  if ( new_krylov ) then
    if ( itime == 0 )call solution_output(grid(1),soln(1),final_k,nml_path,sadj)
  else
    prior_iters = 0
    if ( itime == 0 )call solution_output(grid(1),soln(1),ntt,nml_path,sadj)
  endif

  if ( itime == 0 ) call wresta(grid(1),soln(1),sadj,design)
  use_prior = 0 ! prior history is for flow
  if ( cut_cell_activated .or. cut_cell_visualize ) then
    call cut_cell_adj_vis(grid(1),soln(1),design,sadj%rlam)
    call echo_sensitivity_at_perturbation(grid,soln(1),sadj,design,nml_path)
  end if
  if ( cut_cell_activated )                                 &
    call cut_surface_adj_tec(grid(1), cut, soln(1), design, &
      soln(1)%totforce(1), sadj%rlam)

! Deallocate any memory reserved for global-data collection

  if (associated(soln(1)%global_bndry_data)) then
    do ib = 1, size(soln(1)%global_bndry_data)
      call deallocate_global_bndry_data(soln(1)%global_bndry_data(ib))
    end do
    deallocate(soln(1)%global_bndry_data)
  end if

  if ( rad .and. itime /= 0) then
    call inria_rad_time_adapt(grid(1),soln(1),sadj,design)
  end if

  if ( lmpi_master ) then
    if ( itime > 0 .or. ( itime == 0 .and. getgrad ) ) call write_design(design)
!   Remove an existing stop file so it doesn't kill the next run unintentionally
    call rm('stop.dat')
  end if

! Shutdown 6DOF library and free up associated memory

  if (six_dof) then
    call cleanup_sixdof()
  end if

! Shut down any asynchronous native volume data I/O

  call end_native_volume_data_write()

  call lmpi_synchronize()
  if (lmpi_master) write(*,*) 'Done.'

  call lmpi_finalize

end program adjoint

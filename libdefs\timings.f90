module timings

  use kinddefs,        only : dp

  implicit none

  private

  public :: timing, checkpoint, time_moving_grid
  public :: timer, wtimer, last_wdiff, wall_cr_inv, count_max
  public :: nrollovers

  logical :: timing
  logical :: time_moving_grid

  real(dp) :: last_time  = 0.0_dp

  ! wall clock time. system_clock is a standard fortran90 routine.
  integer     :: wall_cr     = 0          ! wall count rate
  integer     :: last_wtime  = 0          ! last wall clock time
  integer     :: count_max   = 0          ! max value of the clock
  integer     :: nrollovers  = 0          ! Number of times the clock has reset
                                          ! during current run
  real(dp) :: wall_cr_inv = 0.0_dp  ! inverse of wall count rate
  real(dp) :: last_wdiff  = 0.0_dp  ! last wall clock difference

contains

!=============================== WTIMER ======================================80
!
! Gets the current wall-clock time and finds the delta from the last time
! this routine was called.
!
! system_clock is a standard fortran90 routine.
!
! call wtimer('reset')
! call sleep(5)
! call wtimer('main-sleep')
!
!    delta_time =           5.0090 main-sleep
!
!=============================================================================80

  subroutine wtimer(location)

!   use lmpi, only : lmpi_id

    character(len=*), intent(in) :: location

    integer :: current_wtime

    continue

    call system_clock(current_wtime)

    if (wall_cr == 0) then
       call system_clock(count_rate=wall_cr)
       wall_cr_inv = 1.0_dp/wall_cr
    end if

    last_wdiff = (current_wtime-last_wtime)*wall_cr_inv
    last_wtime = current_wtime

    if (.false.) write(*,*) trim(location)

!   if (location /= 'reset')                                                   &
!   write(*,'("delta_time = ",1x,i10,1x,f16.4,1x,a)') lmpi_id,last_wdiff,      &
!                                                        trim(adjustl(location))

  end subroutine wtimer


!=============================== CHECKPOINT ==================================80
!
!  Gets the current wall-clock time
!
!=============================================================================80
  subroutine checkpoint(current_wtime)

    integer, intent(out) :: current_wtime

  continue

    call system_clock(current_wtime)

    if (wall_cr == 0) then
       call system_clock(count_rate=wall_cr,count_max=count_max)
       wall_cr_inv = 1.0_dp/wall_cr
    end if

  end subroutine checkpoint


!================================ TIMER ======================================80
!
! Gets the current CPU time and finds the delta from the last time
! this routine was called.
!
!=============================================================================80

  subroutine timer(location)

    use utilities, only : my_clock

    character(len=*), intent(in) :: location

    real(dp) :: current_time

    continue

    call my_clock(current_time)

    if (location /= 'reset')                                                   &
    write(*,'(a,f16.8,1x,a)') ' delta_time = ', current_time-last_time, location

    last_time = current_time

  end subroutine timer

end module timings

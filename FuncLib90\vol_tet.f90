!================================== VOL_TET ==================================80
!
! Function to find the volume of a tetrahedral
! defined by the coordinates of the pts a, b, c and d
!
!=============================================================================80

  pure function vol_tet( ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz )

    use kinddefs, only : dp

    real(dp), intent(in) :: ax, ay, az, bx, by, bz, cx, cy, cz, dx, dy, dz
    real(dp)             :: vol_tet

    real(dp), parameter :: sixth = 1.0_dp/6.0_dp

  continue

!   Compute the determinant to determine the volume using the edge vectors

    vol_tet = -sixth*det_3x3( ax-dx, ay-dy, az-dz, &
                              bx-dx, by-dy, bz-dz, &
                              cx-dx, cy-dy, cz-dz )

  end function vol_tet


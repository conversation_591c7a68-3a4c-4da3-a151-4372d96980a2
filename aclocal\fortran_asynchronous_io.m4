AC_DEFUN([AX_FORTRAN_ASYNCHRONOUS_IO],
[AC_CACHE_CHECK([fortran io can be asynchronous],
 ax_cv_fortran_asynchronous_io,
 [AC_LANG_PUSH(Fortran)
  AC_COMPILE_IFELSE(
  [
       program main
       integer, asynchronous :: data
       data = 1
       open(10,file='async.dat',asynchronous='yes',form='unformatted')
       write(10,asynchronous='yes') data
       wait(10)
       close(10)
       end
  ],
  [ax_cv_fortran_asynchronous_io=yes],
  [ax_cv_fortran_asynchronous_io=no] 
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_asynchronous_io" != 'no'
then
 AC_DEFINE([HAVE_FORTRAN_ASYNCHRONOUS_IO],[1],[fortran has asynchronous io])
fi
])


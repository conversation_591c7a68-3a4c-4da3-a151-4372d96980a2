module relax_adjoint

  use kinddefs,       only : dp, dqp
  use eqn_groups,     only : solve_name
  use info_depr,      only : skeleton, twod
  use lmpi,           only : lmpi_master
  use grid_types,     only : grid_type
  use solution_types, only : soln_type, linear_projection_type
  use relax_types,    only : relax_type

  implicit none

  private

  public :: relax, new_gcr, adjoint_history, setup_bp
  public :: krylov_initialize, read_lambda_chaos
  public :: krylov_relax
  public :: build_lhs_driver_jacobians, build_lhs_driver_jacobians_turb
  public :: decompose_adj, zero_out_lambda
  public :: entropy_transform

  logical, parameter :: solve_backwards = .true.
  logical :: overset_terms_setup = .false.

  real(dp), dimension(:,:,:), allocatable :: overset_terms, dlambda, lhs_terms

  type(linear_projection_type), dimension(:), allocatable :: projection_outer

contains

!================================== RELAX ====================================80
!
! Solves the adjoint problem in reverse
! If turbulent, this does it loosely coupled
!
!=============================================================================80

  subroutine relax( grid, soln, sadj, crow, crowf, design, relaxation,         &
                    subiteration, istep, nml_path, monitor_norms )

    use info_depr,        only : ivisc, ntt
    use solution_adj,     only : sadj_type
    use lmpi_app,         only : lmpi_xfer
    use comprow_types,    only : crow_type, crow_flow
    use design_types,     only : design_type
    use residual,         only : residual_adj, evaluate_stored_matrix_turb
    use adjoint_switches, only : always_recompute, write_for_chaos
    use nml_overset_data, only : overset_flag
    use exact_defs,       only : ic_y_symmetry
    use check_symmetry,   only : y_adj_res_check

    integer, intent(in) :: subiteration, istep

    character(len=*), intent(in) :: nml_path

    logical, intent(in) :: monitor_norms

    type(grid_type),   intent(in)    :: grid
    type(crow_type),   intent(in)    :: crow
    type(crow_flow),   intent(in)    :: crowf
    type(relax_type),  intent(in)    :: relaxation
    type(design_type), intent(in)    :: design
    type(sadj_type),   intent(inout) :: sadj
    type(soln_type),   intent(inout) :: soln

    integer :: i, j, k, t

    logical, parameter :: update_all_points = .false.
    logical, parameter :: verbose = .false.

  continue

    if(skeleton > 0) write(*,*) ' Top of relax...relax_adjoint.f90'

    compute_turb_model: if (ivisc > 2) then

! Get the adjoint residual for the turbulence equation

      if (always_recompute) then
        if(skeleton > 0) write(*,*) ' ...via getres in residual.'
        call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1),   &
                          update_all_points, verbose)
      else
        if(skeleton > 0) write(*,*) ' ...via evaluate_stored_matrix_turb.'
        call evaluate_stored_matrix_turb(grid,soln,sadj,crow,design,           &
                                         soln%totforce(1),crowf,               &
                                         update_all_points)
      end if

      if ( overset_flag ) call relax_overset_eqns(grid,soln,sadj,crowf,design, &
                                                  soln%adim,soln%adim)


      sadj%res = -sadj%res

      call solve_adj_turb(grid,soln,sadj,crowf,design,relaxation,nml_path)

      if ( monitor_norms ) then
        call adjoint_res_norm_turb(grid,soln,sadj,design,subiteration,istep)
      endif

      if ( overset_flag ) then

        do j = 1, design%nfunctions
          do i = 1, grid%nnodes0
            if ( grid%iblank(i) > 0 ) then
              do t = soln%adim-soln%n_turb+1, soln%adim
                sadj%rlam(t,i,j) = sadj%rlam(t,i,j) + sadj%dq(t,i,j)
              end do
            endif
          end do
        end do

      else

        do j = 1, design%nfunctions
          do i = 1, grid%nnodes0
            do t = soln%adim-soln%n_turb+1, soln%adim
              sadj%rlam(t,i,j) = sadj%rlam(t,i,j) + sadj%dq(t,i,j)
            end do
          end do
        end do

      endif

      do i = 1, design%nfunctions
        call lmpi_xfer(sadj%rlam(:,:,i))
      end do

    end if compute_turb_model

! Get the adjoint residual for the flow equations

    if(skeleton > 0) write(*,*) ' ...via getres in residual.'
    call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1),       &
                      update_all_points, verbose)

! If we were merely running to get the jacobian matrices out to disk for
! testing, there is no need to pursue an actual solve

    if ( write_for_chaos ) return

    if ( overset_flag ) call relax_overset_eqns(grid,soln,sadj,crowf,design,1, &
                                                soln%ndim)

! Flip sign on residual

    sadj%res = -sadj%res

!   Checking residuals on first iteration

    if(ic_y_symmetry .and. (ntt == 1)) then
      call y_adj_res_check(grid, sadj, soln%adim)
    endif

! Now we have res = -df/dQ + dR/dQ*lambda + timeterm

    call solve_adj( grid, soln, sadj, crowf, design, relaxation, nml_path )

    if ( monitor_norms ) then
      call adjoint_res_norm_flow(grid,soln,sadj,design,subiteration,istep)
    endif

! Update the solution

    if ( overset_flag ) then

      do j = 1, design%nfunctions
        do i = 1, grid%nnodes0
          if ( grid%iblank(i) > 0 ) then
            do k = 1, soln%ndim
              sadj%rlam(k,i,j) = sadj%rlam(k,i,j) + sadj%dq(k,i,j)
            end do
          endif
        end do
      end do

    else

      do j = 1, design%nfunctions
        do i = 1, grid%nnodes0
          do k = 1, soln%ndim
            sadj%rlam(k,i,j) = sadj%rlam(k,i,j) + sadj%dq(k,i,j)
          end do
        end do
      end do

    endif

    do i = 1, design%nfunctions
      call lmpi_xfer(sadj%rlam(:,:,i))
    end do

  end subroutine relax


!============================== adjoint_res_norm_turb ========================80
!
! calculates the turbulence equation residual norms
!
!=============================================================================80

  subroutine adjoint_res_norm_turb(grid,soln,sadj,design,subiteration,istep)

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use design_types,         only : design_type
    use solution_adj,         only : sadj_type
    use kinddefs,             only : dp
    use info_depr,            only : ntt, testing, ncyc
    use nml_nonlinear_solves, only : subiters, itime
    use lmpi,                 only : lmpi_master, lmpi_nproc, lmpi_die
    use system_extensions,    only : se_flush, se_open
    use lmpi_app,             only : lmpi_collect_res
    use string_utils,         only : int_to_s
    use file_utils,           only : available_unit

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    integer,           intent(in)    :: subiteration, istep

    real(dp),dimension(soln%n_turb) :: rmshist_loc, rmaxhist_loc
    real(dp),dimension(soln%n_turb) :: xlochist_loc, ylochist_loc, zlochist_loc
    real(dp) :: time_value, value

    logical, save :: file_opened = .false.
    logical, save :: subhist_file_opened = .false.

    integer :: i, eqn

    integer, save :: subhist_unit

    character(len=80) :: filename

  continue

! file header

    if (ntt == 1 .and. lmpi_master .and. (.not.file_opened) .and. testing) then
      call se_open(89,file='nonlinturbadjoint.tec')
      write(89,*) 'TITLE="Adjoint Turbulence convergence"'
      write(89,*) 'VARIABLES="Iteration"'
      do i=1,design%nfunctions
        write(89,*)  '"', "Res_f" // trim(int_to_s(i)), '"'
      end do
      write(89,*) 'ZONE'
      file_opened = .true.
    end if

    if ( itime /= 0 .and. .not.subhist_file_opened .and. lmpi_master ) then
      subhist_unit = available_unit()
      filename = trim(grid%project) // '_turbsubhist.dat'
      open(subhist_unit,file=trim(filename),form='formatted')
      write(subhist_unit,*) 'TITLE="Adjoint subiterative convergence"'
      select case(soln%njac)
      case(4)
        write(subhist_unit,*) 'VARIABLES="Fractional_Time_Step" "R_5"'
      case(5)
        write(subhist_unit,*) 'VARIABLES="Fractional_Time_Step" "R_6"'
      case default
        write(*,*) 'turbsubhist.dat file not coded for soln%njac=', soln%njac
        call lmpi_die
        stop
      end select
      subhist_file_opened = .true.
    endif

! compute norms

    fcn_loop : do i = 1, design%nfunctions

      call l2norm_turb(sadj%res(:,:,i), grid%nnodes0, grid%nnodes01, grid%x,   &
                       grid%y, grid%z, rmshist_loc, rmaxhist_loc, xlochist_loc,&
                       ylochist_loc,zlochist_loc, soln%adim, soln%n_turb)

      if ( itime == 0 ) then
        time_value = soln%walltime(ntt)
      else
        time_value = soln%walltime(istep)
      endif

      eqn_num : do eqn = 1, soln%n_turb
        call lmpi_collect_res(rmshist_loc(eqn), time_value, rmaxhist_loc(eqn), &
                              xlochist_loc(eqn), ylochist_loc(eqn),            &
                              zlochist_loc(eqn))

        rmshist_loc(eqn) = sqrt(rmshist_loc(eqn)/real(grid%nnodesG,dp))

        if ( eqn == 1 ) then
          if ( itime == 0 ) then
            soln%walltime(ntt) = time_value / real(lmpi_nproc, dp)
          else
            soln%walltime(istep) = time_value / real(lmpi_nproc, dp)
          endif
        end if
      end do eqn_num

      turbulent_screen_info : if (lmpi_master) then
        do eqn = 1, soln%n_turb
          write (6,'(a4, i0, e23.15, e13.5, 3e13.5)')                          &
             "Turb",eqn,                                                       &
             real(rmshist_loc(eqn),dp),                                        &
             real(rmaxhist_loc(eqn),dp),real(xlochist_loc(eqn),dp),            &
             real(ylochist_loc(eqn),dp),real(zlochist_loc(eqn),dp)
          call se_flush()
        end do
      end if turbulent_screen_info

      file_info : if ( lmpi_master .and. itime /= 0 .and. i == 1 ) then
        value = real(istep,dp) + (real(ntt,dp)-1.0_dp)/real(subiters,dp)
        write(subhist_unit,'(e25.15,1x,e25.15)') value, rmshist_loc(1)
      end if file_info

      if ( itime == 0 ) then
        soln%rmshist(soln%adim-soln%n_turb+1:soln%adim,ntt,i)  = rmshist_loc
        soln%rmaxhist(soln%adim-soln%n_turb+1:soln%adim,ntt,i) = rmaxhist_loc
        soln%xlochist(soln%adim-soln%n_turb+1:soln%adim,ntt,i) = xlochist_loc
        soln%ylochist(soln%adim-soln%n_turb+1:soln%adim,ntt,i) = ylochist_loc
        soln%zlochist(soln%adim-soln%n_turb+1:soln%adim,ntt,i) = zlochist_loc
      else if ( subiteration == subiters ) then
        soln%rmshist(soln%adim-soln%n_turb+1:soln%adim,istep,i)  = rmshist_loc
        soln%rmaxhist(soln%adim-soln%n_turb+1:soln%adim,istep,i) = rmaxhist_loc
        soln%xlochist(soln%adim-soln%n_turb+1:soln%adim,istep,i) = xlochist_loc
        soln%ylochist(soln%adim-soln%n_turb+1:soln%adim,istep,i) = ylochist_loc
        soln%zlochist(soln%adim-soln%n_turb+1:soln%adim,istep,i) = zlochist_loc
      endif

    end do fcn_loop

    if ( itime == 0 ) then
      if (lmpi_master .and. testing) then
        write(89,'(1x,i8,2x,20e22.13)') ntt, (soln%rmshist(soln%adim,ntt,i),   &
                                              i=1,design%nfunctions)
        call se_flush(89)
      end if
    else
      if (lmpi_master .and. subiteration == subiters .and. testing) then
       write(89,'(1x,i8,2x,20e22.13)') istep, (soln%rmshist(soln%adim,istep,i),&
                                               i=1,design%nfunctions)
        call se_flush(89)
      end if
    endif

    if ( testing ) then
      if ( itime == 0 ) then
        if(ntt == ncyc .and. lmpi_master) close(89)
      else if ( istep == ncyc .and. subiteration == subiters ) then
        close(89)
      endif
    end if

  end subroutine adjoint_res_norm_turb

!============================== adjoint_res_norm_flow ====================80
!
! calculates the flow equation residual norms
!
!=============================================================================80

  subroutine adjoint_res_norm_flow(grid,soln,sadj,design,subiteration,istep)

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use design_types,         only : design_type
    use solution_adj,         only : sadj_type
    use kinddefs,             only : dp
    use info_depr,            only : ntt, testing, ncyc
    use nml_nonlinear_solves, only : subiters, itime
    use lmpi,                 only : lmpi_master, lmpi_nproc, lmpi_die
    use system_extensions,    only : se_flush, se_open
    use lmpi_app,             only : lmpi_collect_res
    use string_utils,         only : int_to_s
    use file_utils,           only : available_unit

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    integer,           intent(in)    :: subiteration, istep

    real(dp), dimension(soln%njac) :: rmshist_loc
    real(dp), dimension(soln%njac) :: rmaxhist_loc
    real(dp), dimension(soln%njac) :: xlochist_loc
    real(dp), dimension(soln%njac) :: ylochist_loc
    real(dp), dimension(soln%njac) :: zlochist_loc

    real(dp) :: time_value, value

    logical, save :: file_opened = .false.
    logical, save :: subhist_file_opened = .false.

    integer :: i, eqn

    integer, save :: subhist_unit

    character(len=80) :: filename

  continue

! file header

    if (ntt == 1 .and. lmpi_master .and. (.not.file_opened) .and. testing) then
      call se_open(88,file='nonlinadjoint.tec')
      write(88,*) 'TITLE="Adjoint convergence"'
      write(88,*) 'VARIABLES="Iteration"'
      do i=1,design%nfunctions
        write(88,*)  '"', "Res_f" // trim(int_to_s(i)), '"'
      end do
      write(88,*) 'ZONE'
      file_opened = .true.
    end if

    if ( itime /= 0 .and. .not.subhist_file_opened .and. lmpi_master ) then
      subhist_unit = available_unit()
      filename = trim(grid%project) // '_subhist.dat'
      open(subhist_unit,file=trim(filename),form='formatted')
      write(subhist_unit,*) 'TITLE="Adjoint subiterative convergence"'
      select case(soln%njac)
      case(4)
        write(subhist_unit,*)                                                  &
          'VARIABLES="Fractional_Time_Step" "R_1" "R_2" "R_3" "R_4"'
      case(5)
        write(subhist_unit,*)                                                  &
          'VARIABLES="Fractional_Time_Step" "R_1" "R_2" "R_3" "R_4" "R_5"'
      case(6)
        write(subhist_unit,*)                                                  &
          'VARIABLES="Fractional_Time_Step" "R_1" "R_2" "R_3" "R_4" "R_5" "R_6"'
      case default
        write(*,*) 'subhist.dat file not coded for soln%njac=', soln%njac
        call lmpi_die
        stop
      end select
      subhist_file_opened = .true.
    endif

! compute norms

    fcn_loop : do i = 1, design%nfunctions

      call l2norm(sadj%res(:,:,i), grid%nnodes0, grid%nnodes01, grid%x, grid%y,&
                  grid%z, grid%vol, rmshist_loc, rmaxhist_loc, xlochist_loc,   &
                  ylochist_loc,zlochist_loc, soln%adim,                        &
                  soln%njac)

      if ( itime == 0 ) then
        time_value = soln%walltime(ntt)
      else
        time_value = soln%walltime(istep)
      endif

      eqn_num : do eqn = 1, soln%njac
        call lmpi_collect_res(rmshist_loc(eqn), time_value, rmaxhist_loc(eqn), &
                              xlochist_loc(eqn), ylochist_loc(eqn),            &
                              zlochist_loc(eqn))

        rmshist_loc(eqn) = sqrt(rmshist_loc(eqn)/real(grid%nnodesG,dp))

        if ( eqn == 1 ) then
          if ( itime == 0 ) then
            soln%walltime(ntt) = time_value / real(lmpi_nproc, dp)
          else
            soln%walltime(istep) = time_value / real(lmpi_nproc, dp)
          endif
        endif

      end do eqn_num

      screen_info : if (lmpi_master) then
        if ( (itime==0 .and. ntt==1) .or.                                      &
             (itime/=0 .and. istep==1 .and. subiteration==1) )                 &
          write(6,'(a5,a23,4a13)')                                             &
               " Iter",                                                        &
               "            adjoint RMS",                                      &
               " adjoint MAX",                                                 &
               "  X location",                                                 &
               "  Y location",                                                 &
               "  Z location"
          write (6,'(i5, e23.15, e13.5, 3e13.5)') ntt, real(rmshist_loc(1),dp),&
                real(rmaxhist_loc(1),dp), real(xlochist_loc(1),dp),            &
                real(ylochist_loc(1),dp), real(zlochist_loc(1),dp)
        call se_flush()
      end if screen_info

      file_info : if ( lmpi_master .and. itime /= 0 .and. i == 1 ) then
        value = real(istep,dp) + (real(ntt,dp)-1.0_dp)/real(subiters,dp)
        write(subhist_unit,'(e25.15,6(1x,e25.15))') value,                     &
                                                    rmshist_loc(1:soln%njac)
      end if file_info

      if ( itime == 0 ) then
        soln%rmshist(1:soln%njac,ntt,i)  = rmshist_loc(:)
        soln%rmaxhist(1:soln%njac,ntt,i) = rmaxhist_loc(:)
        soln%xlochist(1:soln%njac,ntt,i) = xlochist_loc(:)
        soln%ylochist(1:soln%njac,ntt,i) = ylochist_loc(:)
        soln%zlochist(1:soln%njac,ntt,i) = zlochist_loc(:)
      else if ( subiteration == subiters ) then
        soln%rmshist(1:soln%njac,istep,i)  = rmshist_loc(:)
        soln%rmaxhist(1:soln%njac,istep,i) = rmaxhist_loc(:)
        soln%xlochist(1:soln%njac,istep,i) = xlochist_loc(:)
        soln%ylochist(1:soln%njac,istep,i) = ylochist_loc(:)
        soln%zlochist(1:soln%njac,istep,i) = zlochist_loc(:)
      endif

    end do fcn_loop

    if ( itime == 0 ) then
      if (lmpi_master .and. testing) then
        write(88,'(1x,i8,2x,20e22.13)') ntt, (soln%rmshist(1,ntt,i),           &
                                            i=1,design%nfunctions)
        call se_flush(88)
      endif
    else
      if ( lmpi_master .and. subiteration == subiters .and. testing ) then
        write(88,'(1x,i8,2x,20e22.13)') istep, (soln%rmshist(1,istep,i),       &
                                            i=1,design%nfunctions)
        call se_flush(88)
      endif
    endif

    if ( testing ) then
      if ( itime == 0 ) then
        if ( ntt == ncyc .and. lmpi_master ) close(88)
      else if ( istep == ncyc .and. subiteration == subiters ) then
        close(88)
      endif
    end if

  end subroutine adjoint_res_norm_flow

!=================================== L2NORM ==================================80
!
!  calculates the L2 norm of the residual
!
!=============================================================================80

  subroutine l2norm(res, nnodes0, nnodes01, x, y, z, vol, rms,                 &
                    rmax, xloc, yloc, zloc, adim, ldim )

    use lmpi, only : lmpi_id, lmpi_conditional_stop

    integer,                             intent(in)    :: adim
    integer,                             intent(in)    :: ldim
    integer,                             intent(in)    :: nnodes0
    integer,                             intent(in)    :: nnodes01
    real(dp), dimension(adim,nnodes01),  intent(in)    :: res
    real(dp), dimension(nnodes01),       intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),       intent(in)    :: vol
    real(dp), dimension(ldim),           intent(inout) :: rms, rmax
    real(dp), dimension(ldim),           intent(inout) :: xloc, yloc, zloc

    real(dp), parameter :: my_0 = 0.0_dp

    real(dp) :: acc,test

    integer :: iflag,i,eqn

  continue

! Set the flag to monitor which type of residual you want
! iflag=0 "normal" residual
! iflag=1 residual/volume

    iflag = 0

    eqn_num : do eqn = 1, ldim

      acc  = my_0
      rmax(eqn) = -huge(rmax(eqn))

! iflag=0

      if (iflag == 0)then
        do i = 1,nnodes0
          acc = acc + res(eqn,i)*res(eqn,i)
          test = abs(res(eqn,i))
          if (test >= rmax(eqn))then
            rmax(eqn) = test
            xloc(eqn) = x(i)
            yloc(eqn) = y(i)
            zloc(eqn) = z(i)
          end if
        end do
      end if

! iflag=1

      if (iflag == 1)then
        do i = 1,nnodes0
          acc = acc + res(eqn,i)*res(eqn,i)/vol(i)/vol(i)
          test = abs(res(eqn,i)/vol(i))
          if (test >= rmax(eqn))then
            rmax(eqn) = test
            xloc(eqn) = x(i)
            yloc(eqn) = y(i)
            zloc(eqn) = z(i)
          end if
        end do
      end if

      rms(eqn) = acc

! NaN-checker

      if((rms(eqn)+rms(eqn) == rms(eqn) .and. abs(rms(eqn)) > 1.d-100) .or.    &
          .not.(rms(eqn) < abs(rms(eqn)) .or. rms(eqn) >= 0.d0)) then
        write(*,*) 'flow l2norm NaN detected, stopping from processor',lmpi_id+1
        call lmpi_conditional_stop(1)
      else
        call lmpi_conditional_stop(0)
      endif

    end do eqn_num

  end subroutine l2norm

!=================================== L2NORM_TURB =============================80
!
! Calculates the L2 norm of the residual for the turbulence model
!
!=============================================================================80

  subroutine l2norm_turb( res, nnodes0, nnodes01, x, y, z, rms,                &
                          rmax, xloc, yloc, zloc, adim, n_turb )

    use lmpi, only : lmpi_id, lmpi_conditional_stop

    integer,                             intent(in)  :: adim,n_turb
    integer,                             intent(in)  :: nnodes0
    integer,                             intent(in)  :: nnodes01
    real(dp), dimension(adim,nnodes01),  intent(in)  :: res
    real(dp), dimension(nnodes01),       intent(in)  :: x, y, z
    real(dp), dimension(n_turb),         intent(out) :: rms, rmax
    real(dp), dimension(n_turb),         intent(out) :: xloc, yloc, zloc

    real(dp), parameter :: my_0 = 0.0_dp

    real(dp) :: acc, test

    integer :: i, eqn, turb

    continue

    eqn_num : do turb = 1, n_turb

      eqn = adim - n_turb + turb

      acc  = my_0
      rmax(turb) = -huge(rmax(turb))

      do i = 1,nnodes0
        acc = acc + res(eqn,i)*res(eqn,i)
        test = abs(res(eqn,i))
        if (test >= rmax(turb))then
          rmax(turb) = test
          xloc(turb) = x(i)
          yloc(turb) = y(i)
          zloc(turb) = z(i)
        end if
      end do

      rms(turb) = acc

! NaN-checker

      if((rms(turb)+rms(turb) == rms(turb) .and. abs(rms(turb)) > 1.d-100) .or.&
          .not.(rms(turb) < abs(rms(turb)) .or. rms(turb) >= 0.d0)) then
        write(*,*)'turb l2norm NaN detected, stopping from processor', lmpi_id+1
        call lmpi_conditional_stop(1)
      else
        call lmpi_conditional_stop(0)
      endif

    end do eqn_num

  end subroutine l2norm_turb

!================================ SOLVE_ADJ ==================================80
!
! Solves for the dependent variable adjoint update
!
!=============================================================================80

  subroutine solve_adj( grid, soln, sadj, crow, design, relaxation, nml_path )

    use grid_types,         only : grid_type
    use solution_types,     only : soln_type, linear_projection_type
    use solution_adj,       only : sadj_type
    use comprow_types,      only : crow_flow
    use point_solver,       only : point_solve
    use line_solver_ddq,    only : line_solve_ddq
    use design_types,       only : design_type
    use lmpi,               only : lmpi_die
    use lmpi_app,           only : lmpi_xfer
    use info_depr,          only : twod
    use linear_systems,     only : linear_projection
    use linear_projections, only : setup_linear_projection,                    &
                                   initialize_linear_projection,               &
                                   krylov_update
    use linear_spectral,    only : set_field_points
    use dual_numbering,     only : grid_to_matrix, matrix_to_grid

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(crow_flow),   intent(in)    :: crow
    type(design_type), intent(in)    :: design
    type(relax_type),  intent(in)    :: relaxation

    character(len=*),  intent(in)    :: nml_path

    integer :: i, j, k, sweeps_to_do, step_type, jj, gcr_in, gcr_out
    integer :: solve_sweep, sweeps

    character(len=22) :: solve_type

    type(linear_projection_type), save :: projection_meanflow

    real(dp) :: omega
    logical  :: ddq

  continue

! Unpack res from grid numbering to matrix numbering

    do j = 1, design%nfunctions
      call grid_to_matrix(soln%adim,soln%neq01,sadj%res(:,:,j),   &
                          crow%g2m)
    end do

    if(twod) then
      call set_field_points(grid%nnodes0_2d)
    else
      call set_field_points(grid%nnodes0)
    endif

! Read input and allocate memory if performing linear projection

    if ( linear_projection .and. (.not.projection_meanflow%setup) ) then
      projection_meanflow%list_name = 'linear_projection'
      call setup_linear_projection(soln%njac,grid%nnodes0,projection_meanflow, &
                                   nml_path)
      projection_meanflow%setup = .true.
    endif

    solve_name = 'mean_flow_adjoint'

! Zero out the dqs

    do k = 1, design%nfunctions
      do i = 1, grid%nnodes01
        do j = 1, soln%njac
          sadj%dq(j,i,k) = 0.0_dqp
        end do
      end do
    end do

! Although dq for the meanflow lambdas are all at zero and it wouldn't matter
! if we were in grid or matrix numbering, there might be some dq's for the
! turbulence model sitting in dq(:,i,j), which we want to preserve.  So we
! need to move dq into matrix numbering, since we will later on be transforming
! the whole thing back into grid numbering

    do j = 1, design%nfunctions
      call grid_to_matrix(soln%adim,grid%nnodes01,sadj%dq(:,:,j),      &
                          crow%g2m)
    end do

! Solve the systems in reverse

    fcn_loop : do j = 1, design%nfunctions

! Zero Krylov vectors, and form the initial linear residual (assume x_0 = 0)

      if ( linear_projection ) then
        projection_meanflow%hit_tolerance = .false.
        call initialize_linear_projection(soln%njac,                           &
                                          grid%nnodes0,grid%nnodesg,           &
                                          projection_meanflow,                 &
                                          sadj%res(:,:,j), soln%adim)
      endif

      restart_krylov : do gcr_out = 1, projection_meanflow%projection_restarts
        inner_krylov : do gcr_in = 1, projection_meanflow%krylov_dimension

          schedules : do i = 3, 1, -1                    ! Pre, global, post
            schedule_iters : do jj=relaxation%schedule(i)%n_meanflow_iters,1,-1
              steps : do k = relaxation%schedule(i)%n_steps, 1, -1

                step_type    = relaxation%schedule(i)%type(k)
                sweeps_to_do = relaxation%schedule(i)%sweeps(k)
                solve_type   = soln%eqn_groups(step_type)%solve_type
                solve_sweep  = relaxation%schedule(i)%direction(k)
                if ( solve_sweep == 2 ) then
                  solve_sweep = -1
                else
                  solve_sweep = -2
                endif

                sel_solve_type : select case (solve_type)
                case('line-multicolor' , 'line-downstream')

                  if((skeleton > 0) .and.                                      &
                     (jj == relaxation%schedule(i)%n_meanflow_iters) .and.     &
                     (k == relaxation%schedule(i)%n_steps))                    &
                     write(*,*)'Line_solve...nb,dq_dim,res_dim=',              &
                                             soln%njac,soln%adim,soln%adim

                  omega = 1._dp ; sweeps = -1
                  call line_solve_ddq(                                         &
                       solve_sweep, soln%njac,                                 &
                       soln%adim,           soln%adim,       soln%njac,        &
                       size(soln%dq,2), size(soln%a_diag_lu,3),                &
                       size(crow%ia,1), size(crow%ja,1),                       &
                       crow%ia,             crow%ja,                           &
                       soln%eqn_groups(step_type)%n_lines,                     &
                       soln%eqn_groups(step_type)%n_line_dofs,                 &
                       soln%eqn_groups(step_type)%first_dof,                   &
                       soln%eqn_groups(step_type)%line_dofs,                   &
                       sadj%res(:,:,j), sadj%dq(:,:,j),                        &
                       soln%eqn_groups(step_type)%a,                           &
                       soln%eqn_groups(step_type)%b,                           &
                       soln%eqn_groups(step_type)%c,                           &
                       soln%a_diag, soln%a_off,                                &
                       sweeps_to_do, sweeps, omega, soln%cell_re,              &
                       soln%eqn_groups(step_type)%colored_sweeps,              &
                       soln%eqn_groups(step_type)%color_indices,               &
                       soln%eqn_groups(step_type)%max_colored_sweeps,          &
                       soln%eqn_groups(step_type)%lines,                       &
                       soln%eqn_groups(step_type)%lu_offset,                   &
                       soln%eqn_groups(step_type)%sr, crow%g2m, crow%nzg2m)
                case('point-multicolor')

                  if((skeleton > 0) .and.                                      &
                     (jj == relaxation%schedule(i)%n_meanflow_iters) .and.     &
                     (k == relaxation%schedule(i)%n_steps))                    &
                     write(*,*)'Point_solve...nb,dq_dim,res_dim=',             &
                                             soln%njac,soln%adim,soln%adim
                  omega = 1._dp ; ddq = .false.
                  call point_solve(                                            &
                       soln%eqn_groups(step_type)%colored_sweeps,              &
                       soln%eqn_groups(step_type)%color_indices,               &
                       soln%eqn_groups(step_type)%max_colored_sweeps,          &
                       ddq, solve_sweep, omega, soln%njac,                     &
                       soln%adim,           soln%adim,       soln%njac,        &
                       grid%nnodes01,  size(soln%a_diag_lu,3),                 &
                       size(crow%iam,1), size(crow%jam,1),                     &
                       crow%iam,            crow%jam,                          &
                       soln%eqn_groups(step_type)%n_eqns,                      &
                       sweeps_to_do,                                           &
                       sadj%res(:,:,j), sadj%dq(:,:,j),                        &
                       soln%a_diag_lu, soln%a_off,                             &
                       soln%eqn_groups(step_type)%sr,                          &
                       soln%eqn_groups(step_type)%color_periodic_end,          &
                       soln%eqn_groups(step_type)%color_boundary_end,          &
                       crow%nnz01,soln%eqn_groups(step_type)%color_xfer_indices)
                case default
                  write(*,*) 'Unknown solve_type in solve_adj, stopping...'
                  call lmpi_die
                end select sel_solve_type

              end do steps
            end do schedule_iters
          end do schedules

          if ( linear_projection ) then
            call krylov_update(sadj%dq(:,:,j),soln%adim,soln%neq01,            &
                        gcr_in, gcr_out, projection_meanflow,                  &
                        soln%njac, soln%neq0,                                  &
                        size(crow%iam,1), size(crow%jam,1),                    &
                        soln%dofg,size(soln%a_diag,1),                         &
                        crow%iam, crow%jam,                                    &
                        soln%diag_has_been_decomposed, soln%a_diag,            &
                        soln%a_diag_lu, soln%pivot_lu, soln%a_off,             &
                        solve_backwards, crow%g2m)
            if( projection_meanflow%hit_tolerance ) exit restart_krylov
          endif

        enddo inner_krylov
      enddo restart_krylov

      if ( linear_projection ) then
        do i = 1, grid%nnodes0
          do k = 1, soln%njac
            sadj%dq(k,i,j) = projection_meanflow%x_vec_linear(k,i)
          end do
        end do
        call lmpi_xfer(sadj%dq(:,:,j))
      endif

    end do fcn_loop

    solve_name = 'unassigned'

! Unpack dq and res from matrix numbering to grid numbering

    do j = 1, design%nfunctions
      call matrix_to_grid(soln%adim,grid%nnodes01,sadj%dq(:,:,j),      &
                          crow%m2g)
      call matrix_to_grid(soln%adim,soln%neq01,sadj%res(:,:,j),   &
                          crow%m2g)

    end do

  end subroutine solve_adj

!================================ DECOMPOSE_ADJ ==============================80
!
! Solves for the dependent variable adjoint update
!
!=============================================================================80

  subroutine decompose_adj( grid, soln, crow )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow
    use point_lu_ddq,   only : lu_decomposition
    use line_lu_ddq,    only : setup_tridiagonals_ddq, block_tridiag_info
    use info_depr,      only : ivisc

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: i, nb, nr, field_nodes

  continue

    nb          = soln%njac
    field_nodes = grid%nnodes0
    if(twod) then
     field_nodes = grid%nnodes0_2d
    endif
    nr           = soln%njac

    if(skeleton > 0) then
      write(6,*) ' Array sizes in decompose_adj.'
      write(*,*) ' soln%adim =',soln%adim, ' soln%njac=',soln%njac
    endif

!   First copy elements of coefficient matrix into local a,b,c arrays for block
!   tridiagonal systems if doing line solves.  We need to do this before we
!   overwrite the diagonal elements of any entries of A with their LU
!   decomposition

    setup_tridiags : do i = 1, soln%n_eqn_groups
      if ( soln%eqn_groups(i)%solve_type == 'line-multicolor' .or. &
           soln%eqn_groups(i)%solve_type == 'line-downstream') then

        call block_tridiag_info(soln%eqn_groups(i)%n_lines,                    &
                                soln%eqn_groups(i)%first_dof,                  &
                                field_nodes, nb, nr)

        call setup_tridiagonals_ddq(solve_backwards,                           &
                                      grid%nnodes0, crow%nnz0,                 &
                                      soln%eqn_groups(i)%n_lines,              &
                                      soln%eqn_groups(i)%n_line_dofs,          &
                                      soln%eqn_groups(i)%first_dof,            &
                                      soln%eqn_groups(i)%line_dofs,            &
                                      soln%a_diag,  soln%a_off,                &
                                      soln%eqn_groups(i)%a,                    &
                                      soln%eqn_groups(i)%b,                    &
                                      soln%eqn_groups(i)%c,                    &
                                      soln%eqn_groups(i)%nonzero_below,        &
                                      soln%eqn_groups(i)%nonzero_above,        &
                                      nb, nr,                                  &
                                      soln%eqn_groups(i)%lines,                &
                                      soln%eqn_groups(i)%lu_offset,            &
                                      soln%eqn_groups(i)%colored_sweeps,       &
                                      soln%eqn_groups(i)%color_indices,        &
                                      crow%g2m, crow%ia, crow%nzg2m)
      endif
    end do setup_tridiags

!   LU decomposition - points

    do i = 1, grid%nnodes0
      soln%diag_has_been_decomposed(i) = .false.
    end do

    grps : do i = 1, soln%n_eqn_groups
      point_group : if ( soln%eqn_groups(i)%solve_type == &
                                  'point-multicolor' ) then
        call lu_decomposition(size(soln%a_diag,3),                             &
                              soln%a_diag, soln%a_diag_lu, soln%pivot_lu,      &
                              soln%eqn_groups(i)%n_eqns,                       &
                              soln%diag_has_been_decomposed,                   &
                              soln%njac, soln%njac)
      endif point_group
    end do grps

! Deal with turbulence if needed

    if ( ivisc >= 6 ) call decompose_adj_turb(grid,soln,crow)

  end subroutine decompose_adj

!============================== SOLVE_ADJ_TURB ===============================80
!
! Solves for the dependent variable adjoint update (turbulence variable)
!
!=============================================================================80

  subroutine solve_adj_turb(grid,soln,sadj,crow,design,relaxation,nml_path)

    use grid_types,         only : grid_type
    use solution_types,     only : soln_type, linear_projection_type
    use solution_adj,       only : sadj_type
    use comprow_types,      only : crow_flow
    use point_solver,       only : point_solve
    use line_solver_ddq,    only : line_solve_ddq
    use design_types,       only : design_type
    use lmpi,               only : lmpi_die
    use lmpi_app,           only : lmpi_xfer
    use linear_systems,     only : linear_projection
    use info_depr,          only : twod
    use linear_projections, only : setup_linear_projection,                    &
                                   initialize_linear_projection,               &
                                   krylov_update
    use linear_spectral,    only : set_field_points
    use dual_numbering,     only : grid_to_matrix, matrix_to_grid

    type(grid_type),   intent(in)    :: grid
    type(crow_flow),   intent(in)    :: crow
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    type(relax_type),  intent(in)    :: relaxation

    character(len=*),  intent(in)    :: nml_path

    integer :: i, j, jj, k, step_type, sweeps_to_do, t, gcr_out, gcr_in
    integer :: solve_sweep, sweeps

    character(len=22) :: solve_type

    type(linear_projection_type), save :: projection_turb

    real(dp), dimension(soln%n_turb, grid%nnodes0)  :: res_scratch
    real(dqp),dimension(soln%n_turb, grid%nnodes01) :: dq_scratch

    real(dp) :: omega
    logical  :: ddq

  continue

! Unpack res from grid numbering to matrix numbering

    do j = 1, design%nfunctions
      call grid_to_matrix(size(sadj%res,1),size(sadj%res,2),sadj%res(:,:,j),   &
                          crow%g2m)
    end do

    if(twod) then
      call set_field_points(grid%nnodes0_2d)
    else
      call set_field_points(grid%nnodes0)
    endif

! Read input and allocate memory if performing linear projection

    if ( linear_projection .and. (.not.projection_turb%setup) ) then
      projection_turb%list_name = 'linear_projection_turb'
      call setup_linear_projection(soln%n_turb,grid%nnodes0,projection_turb,&
                                   nml_path)
      projection_turb%setup = .true.
    endif

    solve_name = 'turbulence_adjoint'

! Solve the system

    fcn_loop : do j = 1, design%nfunctions

! Zero the turbulent adjoint dq and b arrays (scratch)

      dq_scratch = 0._dqp

! Set res scratch from sadj%res array

      do t = 1, soln%n_turb
        res_scratch( t, 1:grid%nnodes0 ) =                                     &
                    sadj%res( soln%adim - soln%n_turb + t, 1:grid%nnodes0, j )
      enddo

! Zero Krylov vectors, and form the initial linear residual (assume x_0 = 0)

      if ( linear_projection ) then
        projection_turb%hit_tolerance = .false.
        call initialize_linear_projection(soln%n_turb,                         &
                                          grid%nnodes0,grid%nnodesg,           &
                                          projection_turb,                     &
                                          res_scratch,soln%n_turb)
      endif

      restart_krylov : do gcr_out = 1, projection_turb%projection_restarts
        inner_krylov : do gcr_in = 1, projection_turb%krylov_dimension

          schedules : do i = 3, 1, -1                        ! Pre, global, post
            schedule_iters : do jj = relaxation%schedule(i)%n_turb_iters, 1, -1
              steps : do k = relaxation%schedule(i)%n_steps, 1, -1

                step_type    = relaxation%schedule(i)%type(k)
                sweeps_to_do = relaxation%schedule(i)%turb_sweeps(k)
                solve_type   = soln%eqn_groups(step_type)%solve_type
                solve_sweep  = relaxation%schedule(i)%direction(k)
                if ( solve_sweep == 2 ) then
                  solve_sweep = -1
                else
                  solve_sweep = -2
                endif

                sel_solve_type : select case (solve_type)
                case('line-multicolor' , 'line-downstream')

                  if((skeleton > 0) .and.                                      &
                     (jj == relaxation%schedule(i)%n_turb_iters) .and.         &
                     (k == relaxation%schedule(i)%n_steps))                    &
                     write(*,*)'Line_solve...nb,dq_dim,res_dim=',              &
                                             soln%n_turb,soln%n_turb,soln%n_turb

                  omega = 1._dp ; ddq = .false. ; sweeps = -1
                  call line_solve_ddq(                                         &
                       solve_sweep, soln%n_turb,                               &
                       soln%n_turb,         soln%n_turb,     soln%n_turb,      &
                       size(dq_scratch,2), size(soln%a_turb_diag_lu,3),        &
                       size(crow%ia,1), size(crow%ja,1),                       &
                       crow%ia,             crow%ja,                           &
                       soln%eqn_groups(step_type)%n_lines,                     &
                       soln%eqn_groups(step_type)%n_line_dofs,                 &
                       soln%eqn_groups(step_type)%first_dof,                   &
                       soln%eqn_groups(step_type)%line_dofs,                   &
                       res_scratch, dq_scratch,                                &
                       soln%eqn_groups(step_type)%turb_a,                      &
                       soln%eqn_groups(step_type)%turb_b,                      &
                       soln%eqn_groups(step_type)%turb_c,                      &
                       soln%a_turb_diag,                                       &
                       soln%a_turb_off,  sweeps_to_do, sweeps,                 &
                       omega, soln%cell_re,                                    &
                       soln%eqn_groups(step_type)%colored_sweeps,              &
                       soln%eqn_groups(step_type)%color_indices,               &
                       soln%eqn_groups(step_type)%max_colored_sweeps,          &
                       soln%eqn_groups(step_type)%lines,                       &
                       soln%eqn_groups(step_type)%lu_offset,                   &
                       soln%eqn_groups(step_type)%sr, crow%g2m, crow%nzg2m)

                case('point-multicolor')

                  if((skeleton > 0) .and.                                      &
                     (jj == relaxation%schedule(i)%n_turb_iters) .and.         &
                     (k == relaxation%schedule(i)%n_steps))                    &
                      write(*,*)'Point_solve...nb,dq_dim,res_dim=',            &
                                             soln%n_turb,soln%n_turb,soln%n_turb

                  omega = 1._dp ; ddq = .false.
                  call point_solve(                                            &
                       soln%eqn_groups(step_type)%colored_sweeps,              &
                       soln%eqn_groups(step_type)%color_indices,               &
                       soln%eqn_groups(step_type)%max_colored_sweeps,          &
                       ddq, solve_sweep, omega, soln%n_turb,                   &
                       soln%n_turb,         soln%n_turb,         soln%n_turb,  &
                       grid%nnodes01,  size(soln%a_diag_lu,3),                 &
                       size(crow%iam,1), size(crow%jam,1),                     &
                       crow%iam,            crow%jam,                          &
                       soln%eqn_groups(step_type)%n_eqns,                      &
                       sweeps_to_do,                                           &
                       res_scratch, dq_scratch,                                &
                       soln%a_turb_diag_lu, soln%a_turb_off,                   &
                       soln%eqn_groups(step_type)%sr,                          &
                       soln%eqn_groups(step_type)%color_periodic_end,          &
                       soln%eqn_groups(step_type)%color_boundary_end,          &
                       crow%nnz01,soln%eqn_groups(step_type)%color_xfer_indices)

                case default
                  write(*,*) 'Unknown solve_type in solve_adj_turb, stopping...'
                  call lmpi_die
                end select sel_solve_type

              end do steps
            end do schedule_iters
          end do schedules

          if ( linear_projection ) then
            call krylov_update(dq_scratch, size(dq_scratch,1), grid%nnodes01,  &
                               gcr_in, gcr_out, projection_turb,               &
                               soln%n_turb,grid%nnodes0,                       &
                               grid%nnodes0+1,crow%nnz0,                       &
                               grid%nnodesg, size(soln%a_turb_diag,1),         &
                               crow%iam,crow%jam,                              &
                               soln%diag_has_been_decomposed,                  &
                               soln%a_turb_diag,                               &
                               soln%a_turb_diag_lu, soln%turb_pivot_lu,        &
                               soln%a_turb_off, solve_backwards, crow%g2m)
            if( projection_turb%hit_tolerance ) exit restart_krylov
          endif

        enddo inner_krylov
      enddo restart_krylov

      if ( linear_projection ) then
        do i = 1, grid%nnodes0
          do k = 1, soln%n_turb
            dq_scratch(k,i) = projection_turb%x_vec_linear(k,i)
          end do
        end do
        call lmpi_xfer(dq_scratch)
      endif

! Install turbulent adjoint dq into upper locations of sadj%dq

      do t = 1, soln%n_turb
        sadj%dq(soln%adim - soln%n_turb + t,1:grid%nnodes01,j) =              &
                               dq_scratch(t,1:grid%nnodes01)
      enddo

    end do fcn_loop

    solve_name = 'unassigned'

! Unpack dq and res from matrix numbering to grid numbering

    do j = 1, design%nfunctions
      call matrix_to_grid(size(sadj%dq,1),size(sadj%dq,2),sadj%dq(:,:,j),      &
                          crow%m2g)
      call matrix_to_grid(size(sadj%res,1),size(sadj%res,2),sadj%res(:,:,j),   &
                          crow%m2g)
    end do

  end subroutine solve_adj_turb

!============================== DECOMPOSE_ADJ_TURB ===========================80
!
! Decompose the adjoint linear solves (turbulence variable)
!
!=============================================================================80

  subroutine decompose_adj_turb(grid,soln,crow)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow
    use point_lu_ddq,   only : lu_decomposition
    use line_lu_ddq,    only : setup_tridiagonals_ddq, block_tridiag_info
    use lmpi,           only : lmpi_die

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: i, check, nb, nr, field_nodes

  continue

    nb          = soln%n_turb
    field_nodes = grid%nnodes0
    if(twod) then
     field_nodes = grid%nnodes0_2d
    endif
    nr           = soln%n_turb

    if(skeleton > 0) then
      write(6,*) ' Array sizes in decompose_adj_turb.'
      write(*,*) ' soln%n_turb =',soln%n_turb,                                 &
                 ' soln%adim=',soln%adim,                                      &
                 ' soln%njac=',soln%njac
    endif

! First copy elements of coefficient matrix into local a,b,c arrays for block
! tridiagonal systems if doing line solves.  We need to do this before we
! overwrite the diagonal elements of any entries of A with their LU
! decomposition

    setup_tridiags : do i = 1, soln%n_eqn_groups
      if ( soln%eqn_groups(i)%solve_type == 'line-multicolor' .or. &
           soln%eqn_groups(i)%solve_type == 'line-downstream') then

! Check leading dimensions

         check =  +abs(soln%n_turb - size(soln%eqn_groups(i)%turb_a,1))        &
                  +abs(soln%n_turb - size(soln%eqn_groups(i)%turb_a,2))        &
                  +abs(soln%n_turb - size(soln%eqn_groups(i)%turb_b,1))        &
                  +abs(soln%n_turb - size(soln%eqn_groups(i)%turb_b,2))        &
                  +abs(soln%n_turb - size(soln%eqn_groups(i)%turb_c,1))        &
                  +abs(soln%n_turb - size(soln%eqn_groups(i)%turb_c,2))

        if(check > 0) then
          if(lmpi_master) write(*,*) &
            ' Stopping...setup_block_triadiagonals...mismatch:solve_adj_turb.'
          call lmpi_die
        endif

        call block_tridiag_info(soln%eqn_groups(i)%n_lines,                    &
                                soln%eqn_groups(i)%first_dof,                  &
                                field_nodes, nb, nr)

        call setup_tridiagonals_ddq(solve_backwards,                           &
                                      grid%nnodes0, crow%nnz0,                 &
                                      soln%eqn_groups(i)%n_lines,              &
                                      soln%eqn_groups(i)%n_line_dofs,          &
                                      soln%eqn_groups(i)%first_dof,            &
                                      soln%eqn_groups(i)%line_dofs,            &
                                      soln%a_turb_diag, soln%a_turb_off,       &
                                      soln%eqn_groups(i)%turb_a,               &
                                      soln%eqn_groups(i)%turb_b,               &
                                      soln%eqn_groups(i)%turb_c,               &
                                      soln%eqn_groups(i)%nonzero_below,        &
                                      soln%eqn_groups(i)%nonzero_above,        &
                                      nb, nr,                                  &
                                      soln%eqn_groups(i)%lines,                &
                                      soln%eqn_groups(i)%lu_offset,            &
                                      soln%eqn_groups(i)%colored_sweeps,       &
                                      soln%eqn_groups(i)%color_indices,        &
                                      crow%g2m, crow%ia, crow%nzg2m )
      endif
    end do setup_tridiags

!   LU decomposition - points

    do i = 1, grid%nnodes0
      soln%diag_has_been_decomposed(i) = .false.
    end do

    grps : do i = 1, soln%n_eqn_groups
      point_group : if ( soln%eqn_groups(i)%solve_type == &
                                  'point-multicolor' ) then
        call lu_decomposition(size(soln%a_turb_diag,3),                        &
                              soln%a_turb_diag, soln%a_turb_diag_lu,           &
                              soln%turb_pivot_lu,                              &
                              soln%eqn_groups(i)%n_eqns,                       &
                              soln%diag_has_been_decomposed,                   &
                              soln%n_turb, soln%n_turb)
      endif point_group
    end do grps

  end subroutine decompose_adj_turb

!================================ krylov_initialize ==========================80
!
! fire up krylov
!
!=============================================================================80
  subroutine krylov_initialize(grid,soln,sadj,crow,crowf,design, nml_path)

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use comprow_types,        only : crow_type, crow_flow
    use design_types,         only : design_type
    use linear_projections,   only : ddot_i_x_j, setup_linear_projection
    use residual,             only : residual_adj
    use nml_nonlinear_solves, only : subiters, itime

    type(grid_type),   intent(in)    :: grid       ! Grid info
    type(soln_type),   intent(inout) :: soln       ! Solution info
    type(crow_flow),   intent(in)    :: crowf      ! comp row (flow)
    type(sadj_type),   intent(inout) :: sadj       ! Adj Solution
    type(crow_type),   intent(in)    :: crow       ! comp row (adj)
    type(design_type), intent(in)    :: design     ! Design info
    character(len=*),  intent(in)    :: nml_path

    integer :: i, j, k

    logical, save :: setup_completed = .false.

    logical, parameter :: update_all_points = .false.
    logical, parameter :: verbose = .false.

  continue

    if ( .not. setup_completed ) then
      allocate(projection_outer(design%nfunctions))
      do k = 1, design%nfunctions
        projection_outer(k)%list_name = 'linear_projection_outer'
        if ( itime /= 0 ) then
          call setup_linear_projection(soln%adim,soln%neq0,projection_outer(k),&
                                       nml_path,kry_dim_arg=subiters/2)
        else
          call setup_linear_projection(soln%adim,soln%neq0,projection_outer(k),&
                                       nml_path)
        endif
      end do
      setup_completed = .true.
    endif

    if ( 'rpm' == trim(projection_outer(1)%kry_meth) ) then
      return
    end if

! Get the adjoint residual

    call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1),       &
                      update_all_points, verbose)

! Flip sign on residual

    sadj%res = -sadj%res

    do k = 1, design%nfunctions
      do j = 1, soln%neq0
        do i = 1, soln%adim
          projection_outer(k)%x_vec_linear(i,j) = sadj%rlam(i,j,k)
          projection_outer(k)%r_vec_linear(i,j) = sadj%res(i,j,k)
        end do
      end do
      call ddot_i_x_j(projection_outer(k)%r_vec_linear,                        &
                      projection_outer(k)%r_vec_linear,                        &
                      soln%adim, soln%neq0, projection_outer(k)%resid_init)
      projection_outer(k)%resid_init = sqrt(projection_outer(k)%resid_init)
      projection_outer(k)%active_vector = 0
    end do

  end subroutine krylov_initialize

!=============================================================================80
!=============================================================================80
  subroutine debug_octave_dump(ldim,nnode,nvect,data,name)
    use kinddefs, only :dp
    use file_utils, only : available_unit
    use lmpi, only : lmpi_nproc

    integer, intent(in) :: ldim,nnode,nvect
    real(dp),dimension(:,:,:), intent(in) :: data
    character(len=*), intent(in) :: name
    integer :: f
    integer :: i,j
    continue

!to speed up
    return

!not parallel, use global_image
    if ( 1 /= lmpi_nproc ) return

    f = available_unit()
    open(f,file='load_'//trim(name)//'.m')
    write(f,*)trim(name)//' = ['
    do j = 1,nnode
      do i = 1,ldim
        write(f,'(10e25.15)') data(i,j,1:nvect)
      end do
    end do
    write(f,*)'];'
    close(f)
  end subroutine debug_octave_dump

!================================ krylov_relax ===============================80
!
! relax using GCR
!
!=============================================================================80
  subroutine krylov_relax(grid,soln,sadj,crow,crowf,design,relax,              &
                          subiteration,istep,nml_path)

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use comprow_types,        only : crow_type, crow_flow
    use design_types,         only : design_type
    use linear_projections,   only : ddot_i_x_j,dorth_gcr
    use residual_drdq,        only : adjoint_drdq
    use lmpi_app,             only : lmpi_xfer
    use info_depr,            only : ivisc
    use nml_nonlinear_solves, only : itime
    use residual,             only : residual_adj, evaluate_stored_matrix_turb
    use adjoint_switches,     only : always_recompute
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use linear_algebra,       only : singleLU, backsolveLU
    use lmpi,                 only : lmpi_bcast,lmpi_reduce,lmpi_master

    type(grid_type),   intent(in)    :: grid     ! Grid info
    type(soln_type),   intent(inout) :: soln     ! Solution info
    type(crow_flow),   intent(in)    :: crowf    ! comp row (flow)
    type(sadj_type),   intent(inout) :: sadj     ! Adj Solution
    type(crow_type),   intent(in)    :: crow     ! comp row (adj)
    type(design_type), intent(in)    :: design   ! Design info
    type(relax_type),  intent(in)    :: relax    ! Relaxation info
    integer,           intent(in)    :: subiteration, istep

    character(len=*),  intent(in)    :: nml_path

    integer :: i, j, k, a, b

    real(dp) :: dottot

    real(dp) :: dot, gdot

    logical :: debug_projection

    logical, parameter :: update_all_points = .false.
    logical, parameter :: verbose = .false.

    integer :: arnoldi_iterations = 0
    integer :: new_directions = 1
    real(dp), dimension(:), allocatable :: z,z0,z1
    real(dp), dimension(:,:), allocatable :: r
    real(dp), dimension(:,:,:), allocatable :: q

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: one = 1.0_dp

    logical :: roundoff_encountered

    continue

    rpm : if ( 'rpm' == trim(projection_outer(1)%kry_meth) ) then

      j = 1
      store_old_rlam : do i = 1, grid%nnodes0
        do k = 1, soln%adim
          projection_outer(j)%x_vec_linear(k,i) = sadj%rlam(k,i,j)
        end do
      end do store_old_rlam

      rpm_compute_turb_model: if (ivisc > 2) then

! Get the adjoint residual for the turbulence equation

        if (always_recompute) then
          call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1), &
                            update_all_points, verbose)
        else
          call evaluate_stored_matrix_turb(grid,soln,sadj,crow,design,         &
                                          soln%totforce(1),crowf,              &
                                          update_all_points)
        end if

        sadj%res = -sadj%res

        call solve_adj_turb(grid,soln,sadj,crowf,design,relax,nml_path)

        call adjoint_res_norm_turb(grid,soln,sadj,design,subiteration,istep)

        do j = 1, design%nfunctions
          do i = 1, grid%nnodes0
            sadj%rlam(soln%adim,i,j) = sadj%rlam(soln%adim,i,j)                &
                                     + sadj%dq(soln%adim,i,j)
          end do
        end do

        do i = 1, design%nfunctions
          call lmpi_xfer(sadj%rlam(:,:,i))
        end do

      end if rpm_compute_turb_model

! Get the adjoint residual for the flow equations

      call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1),     &
                        update_all_points, verbose)

      sadj%res = -sadj%res

! Now we have res = -df/dQ + dR/dQ*lambda + timeterm

      call solve_adj( grid, soln, sadj, crowf, design, relax, nml_path )

      call adjoint_res_norm_flow(grid,soln,sadj,design,subiteration,istep)

! Update the solution

      do j = 1, design%nfunctions
        do i = 1, grid%nnodes0
          do k = 1, soln%ndim
            sadj%rlam(k,i,j) = sadj%rlam(k,i,j) + sadj%dq(k,i,j)
          end do
        end do
      end do

      do i = 1, design%nfunctions
        call lmpi_xfer(sadj%rlam(:,:,i))
      end do

      newtons_method : if ( projection_outer(1)%krylov_dimension > 0 ) then
         j = 1 ! make loop of func
        allocate(z( projection_outer(j)%krylov_dimension ))
        allocate(z0(projection_outer(j)%krylov_dimension ))
        allocate(z1(projection_outer(j)%krylov_dimension ))

        do a = 1, projection_outer(1)%krylov_dimension
          call ddot_i_x_j(projection_outer(j)%s_vec_linear(:,:,a),&
                          projection_outer(j)%x_vec_linear,       &
                          soln%adim,grid%nnodes0,z0(a))
          call ddot_i_x_j(projection_outer(j)%s_vec_linear(:,:,a),&
                          sadj%rlam(:,:,j),                       &
                          soln%adim,grid%nnodes0,z1(a))
        end do
        call backsolveLU(projection_outer(j)%hes,z,(z1-z0))

        z = z + z0 - z1
!        if (lmpi_master) write(*,'(a,5e12.3)') 'z   ',z
!        if (lmpi_master) write(*,'(a,5e12.3)') '  z1',z1
!        if (lmpi_master) write(*,'(a,5e12.3)') 'z-z1',z-z1

! can this vector addition be done faster?
        do i = 1, grid%nnodes0
          do k = 1, soln%adim
            do a = 1, projection_outer(1)%krylov_dimension
              sadj%rlam(k,i,j) = sadj%rlam(k,i,j) &
                + z(a)*projection_outer(j)%s_vec_linear(k,i,a)
            end do
          end do
        end do
        call lmpi_xfer(sadj%rlam(:,:,j))

        deallocate(z, z0, z1)
      end if newtons_method

!shift old lambda differences back in history
      do j = 1, design%nfunctions
        do a = size(projection_outer(j)%ritz_vecs,3), 2, -1
          do i = 1, grid%nnodes0
            do k = 1, soln%ndim
              projection_outer(j)%ritz_vecs(k,i,a) = &
              projection_outer(j)%ritz_vecs(k,i,a-1)
            end do
          end do
        end do
      end do

!save current lambda differences
      a = 1
      do j = 1, design%nfunctions
        do i = 1, grid%nnodes0
          do k = 1, soln%ndim
            projection_outer(j)%ritz_vecs(k,i,a) = sadj%rlam(k,i,j) - &
              projection_outer(j)%x_vec_linear(k,i)
          end do
        end do
      end do

      new_directions = 2

      debug_projection = .true.

      debug_qr_old_diffs : if ( debug_projection ) then
        k = 1
        allocate(q( soln%adim, soln%neq0, &
                    size(projection_outer(k)%ritz_vecs,3) ))
        allocate(r(size(q,3),size(q,3)))
        copy_q : do a = 1, size(q,3)
          do j = 1, soln%neq0
            do i = 1, soln%adim
              q(i,j,a) = projection_outer(k)%ritz_vecs(i,j,a)
            end do
          end do
        end do copy_q

        do a = 1, size(q,3)
          !remove comp
          do b = 1, a-1
            dot = 0.0_dp
            do j = 1, soln%neq0
              do i = 1, soln%adim
                dot = dot + q(i,j,a) * q(i,j,b)
              end do
            end do
            call lmpi_reduce(dot,gdot)
            call lmpi_bcast(gdot)
            r(a,b) = gdot
            do j = 1, soln%neq0
              do i = 1, soln%adim
                q(i,j,a) = q(i,j,a) - r(a,b)*q(i,j,b)
              end do
            end do
          end do
          ! normalize
          dot = 0.0_dp
          do j = 1, soln%neq0
            do i = 1, soln%adim
              dot = dot + q(i,j,a) * q(i,j,a)
            end do
          end do
          call lmpi_reduce(dot,gdot)
          call lmpi_bcast(gdot)
          r(a,a) = sqrt(gdot)
          not_zero : if ( r(a,a) > 1.0e-15_dp) then
            do j = 1, soln%neq0
              do i = 1, soln%adim
                q(i,j,a) = q(i,j,a) / r(a,a)
              end do
            end do
          end if not_zero
        end do

        new_directions = 0
        each_eigenvalue : do a = 1, size(q,3)
          if (abs(r(a,a)) > 25.0_dp * abs(r(a+1,a+1)) ) then
            new_directions = new_directions + 1
          else
            exit each_eigenvalue
          end if
        end do each_eigenvalue

        if (lmpi_master) then
          write(*,*) new_directions, 'suggested'
          do a = 1, size(q,3)
 !           if ( r(a,a)/r(1,1) > 0.001_dp ) write(*,*) r(a,a)/r(1,1)
          end do
        end if

        dump_to_octave : if ( soln%neq0 < 1000 ) then
          call debug_octave_dump(soln%adim, soln%neq0, size(q,3), &
            projection_outer(k)%ritz_vecs, 'dq' )
          call debug_octave_dump(soln%adim, soln%neq0, size(q,3), &
            q, 'q' )
        end if dump_to_octave

        deallocate(q,r)

      end if debug_qr_old_diffs

      arnoldi_iterations = arnoldi_iterations + 1
      add_new_vectors : if (arnoldi_iterations>10 .and. new_directions>0) then
        arnoldi_iterations = 0
        j = 1 ! make loop of func
        if ( projection_outer(j)%krylov_dimension == 0 ) then
          call my_alloc_ptr( projection_outer(j)%s_vec_linear,&
            soln%adim,grid%nnodes0,new_directions)
          call my_alloc_ptr( projection_outer(j)%v_vec_linear,&
            soln%adim,grid%nnodes0,new_directions)
        else
          call my_realloc_ptr( projection_outer(j)%s_vec_linear,&
            soln%adim,grid%nnodes0,                             &
            projection_outer(j)%krylov_dimension+new_directions)
          call my_realloc_ptr( projection_outer(j)%v_vec_linear,&
            soln%adim,grid%nnodes0,                             &
            projection_outer(j)%krylov_dimension+new_directions)
        end if
        differences_in_past_soln : do a = 1, new_directions
          do i = 1, grid%nnodes0
            do k = 1, soln%ndim
              projection_outer(j)%s_vec_linear(i,k,      &
                projection_outer(j)%krylov_dimension+a) =&
                projection_outer(j)%ritz_vecs(k,i,a)
            end do
          end do
        end do differences_in_past_soln
        ortho_normalize : do a = projection_outer(j)%krylov_dimension+1, &
                          projection_outer(j)%krylov_dimension+new_directions
          ortho : do b = 1, a-1
            call ddot_i_x_j(projection_outer(j)%s_vec_linear(:,:,b),&
                            projection_outer(j)%s_vec_linear(:,:,a),&
                            soln%adim,grid%nnodes0,dottot)
            do i = 1, grid%nnodes0
              do k = 1, soln%adim
                projection_outer(j)%s_vec_linear(k,i,a) = &
                  projection_outer(j)%s_vec_linear(k,i,a) &
                  - projection_outer(j)%s_vec_linear(k,i,b) * dottot
              end do
            end do
          end do ortho
          call ddot_i_x_j(projection_outer(j)%s_vec_linear(:,:,a),&
                          projection_outer(j)%s_vec_linear(:,:,a),&
                          soln%adim,grid%nnodes0,dottot)
          dottot = 1.0_dp/sqrt(dottot)
          do i = 1, grid%nnodes0
            do k = 1, soln%adim
              projection_outer(j)%s_vec_linear(k,i,a) = &
              projection_outer(j)%s_vec_linear(k,i,a) *dottot
            end do
          end do
        end do ortho_normalize

        save_res : do i = 1, grid%nnodes0
          do k = 1, soln%adim
            projection_outer(j)%x_vec_linear(k,i) = sadj%rlam(k,i,j)
            projection_outer(j)%r_vec_linear(k,i) = sadj%res(k,i,j)
          end do
        end do save_res
        M_times_search_dir : do a = projection_outer(j)%krylov_dimension+1, &
                          projection_outer(j)%krylov_dimension+new_directions
          zero_res : do i = 1, grid%nnodes0
            do k = 1, soln%adim
              sadj%res(k,i,j) = zero
! to accout for the sign on the adjoint drdq piece
              sadj%rlam(k,i,j) = -projection_outer(j)%s_vec_linear(k,i,a)
            end do
          end do zero_res

          call lmpi_xfer(sadj%res(:,:,j)) ! zeros
          call lmpi_xfer(sadj%rlam(:,:,j))
          call adjoint_drdq(grid,soln,sadj,crow,crowf,design)

          call solve_adj( grid, soln, sadj, crowf, design, relax, nml_path )
          do i = 1, grid%nnodes0
            do k = 1, soln%adim
              projection_outer(j)%v_vec_linear(k,i,a) =  &
                 projection_outer(j)%s_vec_linear(k,i,a) &
               + sadj%dq(k,i,j)
            end do
          end do

        end do M_times_search_dir
        restore_res : do i = 1, grid%nnodes0
          do k = 1, soln%adim
            sadj%rlam(k,i,j) = projection_outer(j)%x_vec_linear(k,i)
            sadj%res(k,i,j) = projection_outer(j)%r_vec_linear(k,i)
          end do
        end do restore_res
        call lmpi_xfer(sadj%rlam(:,:,j))
        call lmpi_xfer(sadj%res(:,:,j))

        projection_outer(j)%krylov_dimension = &
          projection_outer(j)%krylov_dimension+new_directions

        if (lmpi_master) then
          write(*,*)'rpm: vectors ',projection_outer(j)%krylov_dimension
        end if

        if (associated(projection_outer(j)%hes)) &
          deallocate(projection_outer(j)%hes)
        call my_alloc_ptr(projection_outer(j)%hes, &
          projection_outer(j)%krylov_dimension,    &
          projection_outer(j)%krylov_dimension)
        do i = 1, projection_outer(j)%krylov_dimension
          do k = 1, projection_outer(j)%krylov_dimension
            call ddot_i_x_j(projection_outer(j)%s_vec_linear(:,:,k),&
                            projection_outer(j)%v_vec_linear(:,:,i),&
                            soln%adim,grid%nnodes0,                 &
                            projection_outer(j)%hes(k,i) )
            projection_outer(j)%hes(k,i) = -projection_outer(j)%hes(k,i)
            if (i==k) projection_outer(j)%hes(k,i) = &
              projection_outer(j)%hes(k,i) + one
          end do
        end do
!        do i = 1, projection_outer(j)%krylov_dimension
!          print *, 'H',projection_outer(j)%hes(i,:)
!        end do
        call singleLU(projection_outer(j)%hes)

        debug_projection = .false.

        debug_verify_ortho_norm : if ( debug_projection ) then
          k = 1
          do a = 1, projection_outer(k)%krylov_dimension
            do b = a, projection_outer(k)%krylov_dimension
              dot = 0.0_dp
              do j = 1, soln%neq0
                do i = 1, soln%adim
                  dot = dot + projection_outer(k)%s_vec_linear(i,j,a) * &
                    projection_outer(k)%s_vec_linear(i,j,b)
                end do
              end do
              call lmpi_reduce(dot,gdot)
              call lmpi_bcast(gdot)
              if (lmpi_master) &
                write(*,*) a, b, gdot
            end do
          end do
        end if debug_verify_ortho_norm

      end if add_new_vectors

      return
    end if rpm

    debug_projection = .false.

! Turbulence model

    compute_turb_model: if (ivisc > 2) then

      call solve_adj_turb(grid,soln,sadj,crowf,design,relax, nml_path)

      call adjoint_res_norm_turb(grid,soln,sadj,design,subiteration,istep)

! Should lambda and res be updated before meanflow?

    end if compute_turb_model

! Meanflow

    call solve_adj( grid, soln, sadj, crowf, design, relax, nml_path)

    call adjoint_res_norm_flow(grid,soln,sadj,design,subiteration,istep)

! Now do Krylov

    do k = 1, design%nfunctions
      projection_outer(k)%active_vector = projection_outer(k)%active_vector + 1
      if (   projection_outer(k)%active_vector                                 &
           > projection_outer(k)%krylov_dimension )                            &
        projection_outer(k)%active_vector = 1
    end do

! save search direction

    do k = 1, design%nfunctions
      do j = 1, soln%neq0
        do i = 1, soln%adim
          projection_outer(k)%s_vec_linear(i,j,                                &
            projection_outer(k)%active_vector) = sadj%dq(i,j,k)
        end do
      end do
    end do

! compute A*search_direction

    do k = 1, design%nfunctions
      do j = 1, soln%neq0
        do i = 1, soln%adim
          sadj%rlam(i,j,k) = projection_outer(k)%s_vec_linear(i,j,             &
            projection_outer(k)%active_vector)
        end do
      end do
      call lmpi_xfer(sadj%rlam(:,:,k))
      do j = 1, soln%neq01
        do i = 1, soln%adim
          sadj%res(i,j,k) = 0.0_dp
        end do
      end do
    end do

    call adjoint_drdq(grid,soln,sadj,crow,crowf,design)

! Add in the time term if unsteady

    if ( itime /= 0 ) then
      call add_time_piece(grid,soln,sadj,design)
! No need to add any GCL terms since these only involve lambdas at
! other time levels
    endif

    do k = 1, design%nfunctions
      do j = 1, soln%neq0
        do i = 1, soln%adim
          projection_outer(k)%v_vec_linear(i,j,                                &
            projection_outer(k)%active_vector) = sadj%res(i,j,k)
        end do
      end do
    end do

! orthogonalize

    do k = 1, design%nfunctions
      call dorth_gcr(projection_outer(k)%s_vec_linear,                         &
        projection_outer(k)%v_vec_linear,                                      &
        projection_outer(k)%active_vector, soln%neq0, soln%adim,               &
        roundoff_encountered )
        if( roundoff_encountered .and. lmpi_master )                           &
        write(*,*)'reorthogonalizing in dorth_gcr...too much roundoff.'
    end do


    debug_verify_orthogonality : if ( debug_projection ) then
      do k = 1, design%nfunctions
        do a = 1, projection_outer(k)%active_vector
          dot = 0.0_dp
          do j = 1, soln%neq0
            do i = 1, soln%adim
              dot = dot + projection_outer(k)%v_vec_linear(i,j,a) * &
              projection_outer(k)%v_vec_linear(i,j,                 &
                         projection_outer(k)%active_vector)
            end do
          end do
          call lmpi_reduce(dot,gdot)
          call lmpi_bcast(gdot)
          if (lmpi_master) &
            write(*,*) k, a, projection_outer(k)%active_vector, gdot
        end do
      end do
    end if debug_verify_orthogonality

! update

    do k = 1, design%nfunctions

      call ddot_i_x_j(projection_outer(k)%r_vec_linear,                        &
       projection_outer(k)%v_vec_linear(:,:,projection_outer(k)%active_vector),&
       soln%adim,soln%neq0, dottot)

      do j = 1, soln%neq0
        do i = 1, soln%adim

          projection_outer(k)%x_vec_linear(i,j) =                              &
            projection_outer(k)%x_vec_linear(i,j)                              &
            + dottot                                                           &
            * projection_outer(k)%s_vec_linear(i,j,                            &
                 projection_outer(k)%active_vector)

          sadj%rlam(i,j,k) = projection_outer(k)%x_vec_linear(i,j)

          projection_outer(k)%r_vec_linear(i,j) =                              &
            projection_outer(k)%r_vec_linear(i,j)                              &
            - dottot                                                           &
            * projection_outer(k)%v_vec_linear(i,j,                            &
            projection_outer(k)%active_vector)

          sadj%res(i,j,k) = projection_outer(k)%r_vec_linear(i,j)

        end do
      end do

      call lmpi_xfer(sadj%rlam(:,:,k))
      call lmpi_xfer(sadj%res(:,:,k))

    end do

    debug_verify_residual : if ( debug_projection ) then
      do k = 1, design%nfunctions
        dot = 0.0_dp
        do j = 1, soln%neq0
          do i = 1, soln%adim
            dot = dot + sadj%res(i,j,k)*sadj%res(i,j,k)
          end do
        end do
        call lmpi_reduce(dot,gdot)
        call lmpi_bcast(gdot)
        if (lmpi_master) &
          write(*,*) 'l2 res of r ', k, sqrt(gdot)
      end do

! Get the adjoint residual

      call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1),     &
                        update_all_points, verbose)

      sadj%res = -sadj%res

      do k = 1, design%nfunctions
        dot = 0.0_dp
        do j = 1, soln%neq0
          do i = 1, soln%adim
            dot = dot + sadj%res(i,j,k)*sadj%res(i,j,k)
          end do
!          if ( sum(abs(sadj%res(:,j,k) -                                      &
!                       projection_outer(k)%r_vec_linear(:,j))) > 1.0e-15 )    &
!          write(200+lmpi_id,*)j,sadj%res(:,j,k) -                             &
!                                projection_outer(k)%r_vec_linear(:,j)
        end do
        call lmpi_reduce(dot,gdot)
        call lmpi_bcast(gdot)
        if (lmpi_master) &
          write(*,*) 'l2 res of Ax', k, sqrt(gdot)
      end do

      do k = 1, design%nfunctions
        do j = 1, soln%neq0
          do i = 1, soln%adim
            if (abs( projection_outer(k)%r_vec_linear(i,j) &
                   - sadj%res(i,j,k) ) > 1.0e-12 ) then
              write(*,'(3i5,3e16.8)')k,j,i,                              &
                projection_outer(k)%r_vec_linear(i,j), sadj%res(i,j,k),  &
                projection_outer(k)%v_vec_linear(i,j,                    &
                                        projection_outer(k)%active_vector)
            end if
          end do
        end do
      end do

    end if debug_verify_residual

  end subroutine krylov_relax


!================================ NEW_GCR ====================================80
!
! solve using GCR in the following manner:
!
!    r1 = A*x1 - b
!    for k = 1, kmax
!      solve preconditioner for x
!      sk = x - xk
!      vk = A*sk
!      for i = 1, k-1
!        orthogonalize vk:
!        vk = vk - (vk,vi)*vi
!        sk = sk - (vk,vi)*si
!      end
!      if ( norm(vk) < epsilon ) save xk and exit
!      normalize:
!      vk = vk / norm(vk)
!      sk = sk / norm(vk)
!      xk = xk - (rk,vk)*sk
!      rk = rk - (rk,vk)*vk
!      x  = xk
!    end
!
!=============================================================================80
  subroutine new_gcr(grid,soln,sadj,crow,crowf,design,relaxation,nml_path,     &
                     starting_time,istep,final_k)

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type, wresta
    use comprow_types,        only : crow_type, crow_flow
    use design_types,         only : design_type
    use linear_projections,   only : ddot_i_x_j, setup_linear_projection
    use residual_drdq,        only : adjoint_drdq
    use lmpi_app,             only : lmpi_xfer
    use info_depr,            only : ncyc, skeleton, subiteration
    use nml_nonlinear_solves, only : subiters, itime
    use sampling_headers,     only : number_of_geometries, sampling_frequency
    use residual,             only : residual_adj, all_times_lambda
    use nml_overset_data,     only : overset_flag
    use allocations,          only : my_realloc_ptr
    use lmpi,                 only : lmpi_master
    use timings,              only : checkpoint, nrollovers, count_max,        &
                                     wall_cr_inv
    use nml_code_run_control, only : absolute_stopping_tolerance, iterwrt
    use io,                   only : use_prior
    use utilities,            only : check_for_stop
    use system_extensions,    only : se_flush
    use solution_writes,      only : solution_output
    use nml_global,           only : boundary_animation_freq,                  &
                                     volume_animation_freq
    use file_utils,           only : available_unit

    type(grid_type),   intent(inout) :: grid     ! Grid info
    type(soln_type),   intent(inout) :: soln     ! Solution info
    type(crow_flow),   intent(in)    :: crowf    ! comp row (flow)
    type(sadj_type),   intent(inout) :: sadj     ! Adj Solution
    type(crow_type),   intent(in)    :: crow     ! comp row (adj)
    type(design_type), intent(in)    :: design   ! Design info
    type(relax_type),  intent(in)    :: relaxation    ! Relaxation info

    integer, intent(in)  :: starting_time, istep
    integer, intent(out) :: final_k

    character(len=*), intent(in) :: nml_path

    integer :: i, j, k, pseudo_steps, output_target, l, igeom
    integer :: ifcn, delta_clicks, dim1, dim2, dim3, istop

    integer, save :: subhist_unit

    real(dp) :: vk_vk, norm_vk, r_vk, time_increment, resnorm, value
    real(dp) :: vk_vl, rk_rk, norm_r, norm_res, res_res

    logical :: continue_flag_found

    logical, save :: setup_completed = .false.

    logical, parameter :: update_all_points = .true.
    logical, parameter :: verbose = .false.

    logical, dimension(design%nfunctions) :: continue_gcr

    character(len=80) :: filename

  continue

! initially perform GCR on all cost functions

    continue_gcr(:) = .true.

    perform_initialization : if ( .not. setup_completed ) then

! set up the projection data structures

      allocate(projection_outer(design%nfunctions))
      do k = 1, design%nfunctions
        projection_outer(k)%list_name = 'linear_projection_outer'
        call setup_linear_projection(soln%adim,soln%neq0,projection_outer(k),  &
                                     nml_path)
      end do

! reallocate history-type arrays

      dim1 = size(soln%rmshist,1)
      dim2 = projection_outer(1)%krylov_dimension
      dim3 = size(soln%rmshist,3)

      call my_realloc_ptr(soln%rmshist,  dim1, dim2, dim3)
      call my_realloc_ptr(soln%rmaxhist, dim1, dim2, dim3)
      call my_realloc_ptr(soln%xlochist, dim1, dim2, dim3)
      call my_realloc_ptr(soln%ylochist, dim1, dim2, dim3)
      call my_realloc_ptr(soln%zlochist, dim1, dim2, dim3)
      call my_realloc_ptr(soln%walltime,       dim2)
      call my_realloc_ptr(soln%simtime,        dim2)

! alter output frequencies for solution vis as needed

      output_target = projection_outer(1)%krylov_dimension
      if ( itime /= 0 ) output_target = ncyc + 1   ! +1 for freestream BC

      if(boundary_animation_freq(1) < 0)boundary_animation_freq(1)=output_target
      if(volume_animation_freq(1) < 0)    volume_animation_freq(1)=output_target
      do igeom = 1,number_of_geometries
        if (sampling_frequency(igeom) < 0)                                     &
            sampling_frequency(igeom)           = output_target
      end do


! Open subhistory tracking file for time-dependent cases

      if ( itime /= 0 .and. lmpi_master ) then
        subhist_unit = available_unit()
        filename = trim(grid%project) // '_subhist.dat'
        open(subhist_unit,file=trim(filename),form='formatted')
        write(subhist_unit,*) 'TITLE="Adjoint subiterative convergence"'
        write(subhist_unit,*) 'VARIABLES="Fractional_Time_Step" "Res"'
      endif

      setup_completed = .true.

    endif perform_initialization

! lambda_gcr is stored in x
! lambda     is sadj%rlam
! residual   is stored in r

! Evaluate baseline residual and place in r

    subiteration = 1
    call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1),       &
                      update_all_points, verbose)

! Initialize lambda_gcr to be lambda

    fcn_loop1 : do ifcn = 1, design%nfunctions
      do j = 1, soln%neq0
        do i = 1, soln%adim
          projection_outer(ifcn)%x_vec_linear(i,j) = sadj%rlam(i,j,ifcn)
          projection_outer(ifcn)%r_vec_linear(i,j) = sadj%res(i,j,ifcn)
        end do
      end do
    end do fcn_loop1

! Now start building up a solution with GCR

    final_k = 1 ! initialize

    search_dirs : do k = 1, projection_outer(1)%krylov_dimension

! this module variable 'subiteration' controls things in other places,
! such as history writing, things that should happen at the beginning of a
! physical time step, vis output, etc

      subiteration = k

      if ( k == 1 ) then
        soln%time1 = starting_time
      else
        soln%time1 = soln%time2
      endif

! preconditioner: calling relax will update adjoint residual, solve Ax=b, then
! update values of sadj%rlam

      pseudo_steps = ncyc
      if ( itime /= 0 ) pseudo_steps = subiters

      do i = 1, pseudo_steps
        call relax(grid,soln,sadj,crow,crowf,design,relaxation,0,0,nml_path,   &
                   .false.)
      end do

! set s(k) and lambda to be lambda - lambda_gcr
! zero out residual

      fcn_loop2 : do ifcn = 1, design%nfunctions
        if ( .not. continue_gcr(ifcn) ) cycle fcn_loop2
        do j = 1, soln%neq0
          do i = 1, soln%adim
            projection_outer(ifcn)%s_vec_linear(i,j,k) = sadj%rlam(i,j,ifcn)   &
                                      - projection_outer(ifcn)%x_vec_linear(i,j)
            sadj%rlam(i,j,ifcn) = projection_outer(ifcn)%s_vec_linear(i,j,k)
          end do
        end do
        call lmpi_xfer(sadj%rlam(:,:,ifcn))
        sadj%res(:,:,ifcn) = 0.0_dp
      end do fcn_loop2

! compute A*s(k)

      call adjoint_drdq(grid,soln,sadj,crow,crowf,design)

! If overset, add in A*search_direction pieces for interpolation equations

      if ( overset_flag ) then
        if ( .not. overset_terms_setup ) then
          allocate(overset_terms(soln%adim,grid%nnodes01,design%nfunctions))
          overset_terms_setup = .true.
        endif
        call all_times_lambda(overset_terms,sadj%rlam,soln%adim,               &
                              design%nfunctions,grid%nnodes01,grid%iblank,     &
                              grid%nnodes0,crowf%ia,crowf%ja,1,soln%adim)
        fcn_loop3 : do ifcn = 1, design%nfunctions
          if ( .not. continue_gcr(ifcn) ) cycle fcn_loop3
          do j = 1, soln%neq0
            do i = 1, soln%adim
              sadj%res(i,j,ifcn) = sadj%res(i,j,ifcn) - overset_terms(i,j,ifcn)
            end do
          end do
        end do fcn_loop3
      endif

! Add in the time term corresponding to lambda at current time level if unsteady
! No need to add any GCL terms since these only involve lambdas at other time
! levels

      if ( itime /= 0 ) call add_time_piece(grid,soln,sadj,design)

      fcn_loop4 : do ifcn = 1, design%nfunctions

        if ( .not. continue_gcr(ifcn) ) cycle fcn_loop4

! set v(k) = A*s(k)

        do j = 1, soln%neq0
          do i = 1, soln%adim
            projection_outer(ifcn)%v_vec_linear(i,j,k) = sadj%res(i,j,ifcn)
          end do
        end do

! orthogonalize versus previous vectors

        orthogonalize : do l = 1, k-1
          call ddot_i_x_j(projection_outer(ifcn)%v_vec_linear(:,:,k),          &
                          projection_outer(ifcn)%v_vec_linear(:,:,l),          &
                          soln%adim, soln%neq0, vk_vl)
          do j = 1, soln%neq0
            do i = 1, soln%adim
              projection_outer(ifcn)%v_vec_linear(i,j,k) =                     &
              projection_outer(ifcn)%v_vec_linear(i,j,k) -                     &
              vk_vl*projection_outer(ifcn)%v_vec_linear(i,j,l)
              projection_outer(ifcn)%s_vec_linear(i,j,k) =                     &
              projection_outer(ifcn)%s_vec_linear(i,j,k) -                     &
              vk_vl*projection_outer(ifcn)%s_vec_linear(i,j,l)
            end do
          end do
        end do orthogonalize

! compute norm of v(k)

        call ddot_i_x_j(projection_outer(ifcn)%v_vec_linear(:,:,k),            &
                        projection_outer(ifcn)%v_vec_linear(:,:,k),            &
                        soln%adim, soln%neq0, vk_vk)
        norm_vk = sqrt(vk_vk)

! If norm of vk is zero, stick current solution X into lambda and quit

        zero_norm : if ( norm_vk <= 100.0_dp*epsilon(norm_vk) ) then

          if ( lmpi_master ) write(*,*) 'GCR: Found zero search direction...'

! take norm of residual before preconditioning

          call ddot_i_x_j(projection_outer(ifcn)%r_vec_linear(:,:),            &
                          projection_outer(ifcn)%r_vec_linear(:,:),            &
                          soln%adim, soln%neq0, rk_rk)
          norm_r = sqrt(rk_rk)

! take norm of residual after preconditioning

          do j = 1, soln%neq0
            do i = 1, soln%adim
              sadj%rlam(i,j,ifcn) = projection_outer(ifcn)%x_vec_linear(i,j)   &
                                  + projection_outer(ifcn)%s_vec_linear(i,j,k)
            end do
          end do

          call lmpi_xfer(sadj%rlam(:,:,ifcn))

          call residual_adj(grid,soln,sadj,crow,crowf,design,soln%totforce(1), &
                            update_all_points, verbose)

! use r_vec_linear as scratch space

          do j = 1, soln%neq0
            do i = 1, soln%adim
              projection_outer(ifcn)%r_vec_linear(i,j) = sadj%res(i,j,ifcn)
            end do
          end do

          call ddot_i_x_j(projection_outer(ifcn)%r_vec_linear(:,:),            &
                          projection_outer(ifcn)%r_vec_linear(:,:),            &
                          soln%adim, soln%neq0, res_res)
          norm_res = sqrt(res_res)

          if ( norm_r > norm_res ) then
            do j = 1, soln%neq0
              do i = 1, soln%adim
                sadj%rlam(i,j,ifcn) = projection_outer(ifcn)%x_vec_linear(i,j) &
                                    + projection_outer(ifcn)%s_vec_linear(i,j,k)
              end do
            end do
            resnorm = norm_res
          else
            do j = 1, soln%neq0
              do i = 1, soln%adim
                sadj%rlam(i,j,ifcn) = projection_outer(ifcn)%x_vec_linear(i,j)
              end do
            end do
            resnorm = norm_r
          endif

          continue_gcr(ifcn) = .false.

        else zero_norm

! normalize s(k) and v(k)

          do j = 1, soln%neq0
            do i = 1, soln%adim
              projection_outer(ifcn)%v_vec_linear(i,j,k) =                     &
              projection_outer(ifcn)%v_vec_linear(i,j,k) / norm_vk
              projection_outer(ifcn)%s_vec_linear(i,j,k) =                     &
              projection_outer(ifcn)%s_vec_linear(i,j,k) / norm_vk
            end do
          end do

! compute r_vk

          call ddot_i_x_j(projection_outer(ifcn)%r_vec_linear(:,:),            &
                          projection_outer(ifcn)%v_vec_linear(:,:,k),          &
                          soln%adim, soln%neq0, r_vk)

! compute new solution vector X
! and stick new r into residual vector for monitoring

          do j = 1, soln%neq0
            do i = 1, soln%adim
              projection_outer(ifcn)%x_vec_linear(i,j) =                       &
              projection_outer(ifcn)%x_vec_linear(i,j)                         &
              - r_vk * projection_outer(ifcn)%s_vec_linear(i,j,k)

              sadj%rlam(i,j,ifcn) = projection_outer(ifcn)%x_vec_linear(i,j)

              projection_outer(ifcn)%r_vec_linear(i,j) =                       &
              projection_outer(ifcn)%r_vec_linear(i,j)                         &
              - r_vk * projection_outer(ifcn)%v_vec_linear(i,j,k)
            end do
          end do

! compute residuals over all eqns and store/monitor

          call ddot_i_x_j(projection_outer(ifcn)%r_vec_linear(:,:),            &
                          projection_outer(ifcn)%r_vec_linear(:,:),            &
                          soln%adim, soln%neq0, resnorm)
          resnorm = sqrt(resnorm)

        endif zero_norm

        soln%rmshist(1,k,ifcn) = resnorm

        if ( lmpi_master .and. k == 1 .and. ifcn == 1 ) then
          write(*,*) '     Search Dir      Fcn   Residual Norm'
        endif

        if ( lmpi_master ) write(*,*) k, ifcn, resnorm

! If unsteady, also send subiteration history to a file

        if ( lmpi_master .and. itime /= 0 .and. ifcn == 1 ) then
          value = real(istep,dp) + (real(k,dp)-1.0_dp) /                       &
                                   real(projection_outer(1)%krylov_dimension,dp)
          write(subhist_unit,'(e25.15,1x,e25.15)') value, resnorm
        end if

        call lmpi_xfer(sadj%rlam(:,:,ifcn))

      end do fcn_loop4

      call checkpoint(soln%time2)

      delta_clicks = soln%time2 - soln%time1
      if ( delta_clicks < 0 ) then
        nrollovers   = nrollovers + 1
        delta_clicks = count_max + delta_clicks
      endif
      time_increment = wall_cr_inv*real(delta_clicks,dp)

      if ( k == 1 ) then
        soln%walltime(k) = time_increment
      else
        soln%walltime(k) = soln%walltime(k-1) + time_increment
      endif

! Periodically dump steady solution and/or vis files as desired
! Quit if tolerance reached

      final_k = k

      if ( itime == 0 ) then
        if (k / iterwrt * iterwrt == k .and.                                   &
            k /= projection_outer(1)%krylov_dimension) then
          call wresta(grid,soln,sadj,design)
          use_prior = 0 ! prior history is for flow
          if ( lmpi_master ) call adjoint_history(grid,soln,1,k)
        endif
        call solution_output(grid, soln, k, nml_path, sadj)
        if (k >= 4 .and.                                        &
            soln%rmshist(1,k,1) <= absolute_stopping_tolerance) &
          exit search_dirs
      end if

! Check to see if user wants to stop the execution

      call check_for_stop(istop)

      if ((k >= istop) .and. istop > 0) exit search_dirs

! If none of the functions require further GCR stuff, exit this routine

      continue_flag_found = .false.
      fcn_loop5 : do ifcn = 1, design%nfunctions
        if ( continue_gcr(ifcn) ) continue_flag_found = .true.
      end do fcn_loop5

      if ( .not. continue_flag_found ) exit search_dirs

    end do search_dirs

!   Reset printing flag back to zero

    skeleton = 0
    call se_flush()

  end subroutine new_gcr


!============================== ADJOINT_HISTORY ==============================80
!
! Writes adjoint history for plotting in tecplot format
!
!=============================================================================80
  subroutine adjoint_history(grid,soln,ub,iteration)

    use string_utils,      only : sprintf, int_to_s
    use grid_types,        only : grid_type
    use system_extensions, only : se_open
    use file_utils,        only : available_unit

    integer, intent(in) :: ub, iteration

    type(soln_type), intent(in) :: soln
    type(grid_type), intent(in) :: grid

    integer :: n, eqn, u

    character(len=80)  :: temp_string, filename, format
    character(len=800) :: res_string

  continue

    filename = trim(grid%project) // '_hist.dat'

    u = available_unit()
    call se_open(u, file=filename)
    rewind(u)

    res_string = ' "R_1"'
    do eqn = 2, ub
      temp_string = trim(sprintf(' "R_%i0',eqn))//'"'
      res_string  = trim(res_string) // trim(temp_string)
    end do

    write(u,'(a)')'TITLE="Adjoint Convergence History"'
    write(u,'(7a)')'VARIABLES="Iteration"',trim(res_string),' "Simulation_Time"'
    write(u,'("ZONE I=",i0," F=POINT")') iteration

    format = '(i0,'//int_to_s(ub+1)//'(1x,e17.10))'

    do n = 1, iteration
      write(u,format) n, (soln%rmshist(eqn,n,1),eqn=1,ub),soln%walltime(n)
    end do

    close(u)

  end subroutine adjoint_history


!========================== BUILD_LHS_DRIVER_JACOBIANS =======================80
!
!  Forms jacobians to drive adjoint equations
!
!=============================================================================80

  subroutine build_lhs_driver_jacobians(grid,soln,crowf)

    use info_depr,          only : ivisc, ntt, ncyc
    use cut_types,          only : cut_cell_activated
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type, generic_gas,                     &
                                   compressible, incompressible
    use comprow_types,      only : crow_flow
    use thermo,             only : etop, ptoe
    use gradient_driver,    only : grad_variable
    use lmpi,               only : lmpi_die
    use lmpi_app,           only : lmpi_xfer
    use reconstruction,     only : no_lim
    use cut_cell,           only : cut_cell_time_step
    use timestep,           only : deltat2, deltat2i
    use timestep_gen,       only : deltat2_gen
    use fill_jacobians,     only : fill_jacobian
    use fill_jacobians_gen, only : fill_jacobian_gen
    use generic_gas_map,    only : perfect
    use inviscid_flux,      only : gen_use_perf_jac

    type(grid_type), intent(in)    :: grid
    type(crow_flow), intent(in)    :: crowf
    type(soln_type), intent(inout) :: soln

    integer :: ntt_save

    character(len=80) :: variable, gradient

  continue

! Get gradients for cut-cells since jacobians use these

    cut_cell_gradients : if (cut_cell_activated) then
      variable = 'inviscid'
      if (ivisc > 2) variable = 'viscous'
      gradient = 'least-squares'
      !CHECK
      if ( soln%eqn_set == compressible) then
        call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      else if ( soln%eqn_set == generic_gas ) then
        call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      endif
      call grad_variable(grid, soln, variable, gradient)
      !CHECK
      if ( soln%eqn_set == compressible ) then
        call ptoe( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      else if ( soln%eqn_set == generic_gas ) then
        call ptoe( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      end if
      call lmpi_xfer(soln%gradx)
      call lmpi_xfer(soln%grady)
      call lmpi_xfer(soln%gradz)
      call no_lim(grid%nnodes01, soln%phi, soln%n_grd)
      call lmpi_xfer(soln%phi)
    end if cut_cell_gradients

! First we need to form the jacobians at the current Q

    ntt_save = ntt
    ntt = ncyc   ! Sets the CFL number in the LHS Jacobian to follow here
                 ! In the event we are doing time-dependent, the CFL will
                 ! be based solely on the pseudo_sub/iramp values

    select case (soln%eqn_set)
    case (compressible)
      cut_or_std : if (cut_cell_activated) then
        call cut_cell_time_step(grid,soln)
      else
        call deltat2(grid%nnodes0, grid%nnodes01, grid%nedgeloc,               &
                     soln%q_dof,   soln%cdt,      grid%vol,                    &
                     grid%xn,      grid%yn,       grid%zn,       grid%ra,      &
                     grid%eptr,    grid%nbound,   grid%bc,                     &
                     grid%nedgeloc_2d,            grid%nnodes0_2d,             &
                     grid%node_pairs_2d,          grid%facespeed, soln%n_tot)
      end if cut_or_std
    case (incompressible)
      call deltat2i(grid%nnodes0, grid%nnodes01, grid%nedgeloc,                &
                    soln%q_dof,   soln%cdt,      grid%vol,                     &
                    grid%xn,      grid%yn,       grid%zn,      grid%ra,        &
                    grid%eptr,    grid%nbound,   grid%bc,                      &
                    grid%nedgeloc_2d,            grid%nnodes0_2d,              &
                    grid%node_pairs_2d,          grid%facespeed, soln%n_tot)
    case (generic_gas)
      if ( perfect .and. gen_use_perf_jac ) then
        if ( skeleton > 0 ) &
        write(*,*) "CHECK: using perfect gas path timestepping routine"
        call deltat2(grid%nnodes0, grid%nnodes01, grid%nedgeloc,               &
                     soln%q_dof,   soln%cdt,      grid%vol,                    &
                     grid%xn,      grid%yn,       grid%zn,       grid%ra,      &
                     grid%eptr,    grid%nbound,   grid%bc,                     &
                     grid%nedgeloc_2d,            grid%nnodes0_2d,             &
                     grid%node_pairs_2d,          grid%facespeed, soln%n_tot)
      else
        call deltat2_gen(grid%nnodes0, grid%nnodes01, grid%nedgeloc,           &
                       soln%q_dof,   soln%cdt,      grid%vol,                  &
                       grid%xn,      grid%yn,       grid%zn,   grid%ra,        &
                       grid%eptr,    grid%nbound,   grid%bc,                   &
                       grid%nedgeloc_2d,            grid%nnodes0_2d,           &
                       grid%node_pairs_2d, soln%n_tot)
      end if
    case default
      write(*,*)"eqn_set = ", soln%eqn_set, " not valid in dual solve main"
      call lmpi_die
    end select

    if(skeleton > 0) write(*,*) 'Setting up mean_flow jacobians.'

    select case(soln%eqn_set)
    case(compressible,incompressible)
      call fill_jacobian(grid,soln,crowf)
    case(generic_gas)
      if ( perfect .and. gen_use_perf_jac ) then
        if ( skeleton > 0 ) &
        write(*,*) 'CHECK: eqn_set = generic_gas, using perfect gas jacobian'
        call fill_jacobian(grid,soln,crowf)
      else
        call fill_jacobian_gen(grid,soln,crowf)
      end if
    case default
      write(*,*)"ONLY eqn_set = 0,1,2 allowed, eqn_set = ",soln%eqn_set
      call lmpi_die
    end select

    ntt = ntt_save

  end subroutine build_lhs_driver_jacobians

!========================== BUILD_LHS_DRIVER_JACOBIANS_TURB ==================80
!
!  Forms jacobians to drive turbulent adjoint equations
!
!=============================================================================80

  subroutine build_lhs_driver_jacobians_turb(grid,soln,crowf)

    use info_depr,        only : ntt, ncyc
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use comprow_types,    only : crow_flow
    use flux_turb,        only : turbulent_jacobian, turbulent_gradients
    use thermo,           only : etop, ptoe
    use solution_types,   only : compressible
    use lmpi_app,         only : lmpi_xfer
    use turb_sa_const,    only : note1
    use adjoint_switches, only : use_bp_model
    use turbulence_info,  only : turbulence_model
    use turb_sa_2012,     only : s_terms
    use allocations,      only : my_alloc_ptr

    type(grid_type), intent(inout) :: grid
    type(crow_flow), intent(in)    :: crowf
    type(soln_type), intent(inout) :: soln

    integer :: ntt_save

    character(len=1)  :: note1_save
    character(len=40) :: turbulence_model_save

    logical, save :: init = .true.

  continue

! Temporarily save off the state of a few global control variables

    ntt_save = ntt
    ntt = ncyc   ! Sets the CFL number in the LHS Jacobian to follow here
                 ! In the event we are doing time-dependent, the CFL will
                 ! be based solely on the pseudo_sub/iramp values

    if ( use_bp_model ) then
      note1_save            = note1
      turbulence_model_save = turbulence_model
      note1                 = 'c'
      turbulence_model      = 'sa-neg'
    endif

    if ( soln%eqn_set == compressible ) then
      call etop( soln%neq01, soln%q_dof, soln%n_tot, soln%eqn_set )
    endif

    call turbulent_gradients(grid, soln)

    call lmpi_xfer(soln%gradx)
    call lmpi_xfer(soln%grady)
    call lmpi_xfer(soln%gradz)

    if ( soln%eqn_set == compressible ) then
      call ptoe( soln%neq01, soln%q_dof, soln%n_tot, soln%eqn_set )
    endif

    if ( init .and. use_bp_model ) then
      allocate(s_terms(1))
      call my_alloc_ptr( s_terms(1)%dsdvt,  1 )
      call my_alloc_ptr( s_terms(1)%s_diag, grid%nnodes0 )
    endif

    call turbulent_jacobian(grid, soln, crowf)

! Restore the state of a few global control variables

    ntt = ntt_save

    if ( use_bp_model ) then
      note1            = note1_save
      turbulence_model = turbulence_model_save
    endif

    init = .false.

  end subroutine build_lhs_driver_jacobians_turb

!================================= ZERO_OUT_LAMBDA ===========================80
!
!  Possibly zeroes out adjoint variables for locally optimal scheme
!
!=============================================================================80

  subroutine zero_out_lambda(sadj)

    use solution_adj,     only : sadj_type
    use info_depr,        only : physical_timestep
    use adjoint_switches, only : locally_optimal_freq
    use lmpi,             only : lmpi_master

    type(sadj_type), intent(inout) :: sadj

  continue

    if ( physical_timestep/locally_optimal_freq*locally_optimal_freq ==        &
                                                   physical_timestep ) then
      if ( lmpi_master ) write(*,*) 'Locally optimal: zeroing lambda...'
      sadj%rlam = 0.0_dp
    endif

  end subroutine zero_out_lambda


!================================= ADD_TIME_PIECE ============================80
!
! Adds the time term contribution (from the current time-level only!) to
! the adjoint residual for GCR
!
!=============================================================================80
  subroutine add_time_piece(grid,soln,sadj,design)

    use kinddefs,             only : dp
    use info_depr,            only : physical_timestep, ncyc
    use nml_nonlinear_solves, only : ibdf2opt, itime, dt
    use lmpi,                 only : lmpi_die
    use solution_adj,         only : sadj_type
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type, incompressible
    use fun3d_constants,      only : my_1, my_1p5, my_6, my_11
    use design_types,         only : design_type

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(in)    :: soln
    type(design_type), intent(in)    :: design
    type(sadj_type),   intent(inout) :: sadj

    integer :: i, j, k

    real(dp) :: a

  continue

   select case (itime)
   case (1)    ! BDF1

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                             + sadj%coltag(j,i)/dt*grid%vol(i)*sadj%rlam(j,i,k)
           end do
         end do
       end do

   case (2)    ! BDF2 with BDF1 assumed at first step

     if ( physical_timestep == ncyc ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                       + sadj%coltag(j,i)/dt*grid%vol(i)*my_1p5*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else if ( physical_timestep == ncyc-1 ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                       + sadj%coltag(j,i)/dt*grid%vol(i)*my_1p5*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else if ( physical_timestep == 1 ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                         + sadj%coltag(j,i)/dt*grid%vol(i)*my_1*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                       + sadj%coltag(j,i)/dt*grid%vol(i)*my_1p5*sadj%rlam(j,i,k)
           end do
         end do
       end do

     endif

   case (3)    ! BDF2opt/BDF3 with BDF1 assumed @ 1st step, BDF2 assumed @ 2nd

     if ( ibdf2opt == 0 ) then         ! BDF3
       a =  my_11/my_6
     else if ( ibdf2opt == 1 ) then    ! BDF2opt
       a =  0.48_dp*my_11/my_6 + 0.52_dp*my_1p5
     else
       a = 0.0_dp
       write(*,*) 'Unknown value for ibdf2opt: ', ibdf2opt
       call lmpi_die
     endif

     if ( physical_timestep == ncyc ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                            + sadj%coltag(j,i)/dt*grid%vol(i)*a*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else if ( physical_timestep == ncyc-1 ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                            + sadj%coltag(j,i)/dt*grid%vol(i)*a*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else if ( physical_timestep == ncyc-2 ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                            + sadj%coltag(j,i)/dt*grid%vol(i)*a*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else if ( physical_timestep == 2 ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                       + sadj%coltag(j,i)/dt*grid%vol(i)*my_1p5*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else if ( physical_timestep == 1 ) then

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                         + sadj%coltag(j,i)/dt*grid%vol(i)*my_1*sadj%rlam(j,i,k)
           end do
         end do
       end do

     else

       do i = 1, grid%nnodes0
         do k = 1, design%nfunctions
           do j = 1, soln%adim
             if ( soln%eqn_set == incompressible .and. j == 1 ) cycle
             sadj%res(j,i,k) = sadj%res(j,i,k)                                 &
                            + sadj%coltag(j,i)/dt*grid%vol(i)*a*sadj%rlam(j,i,k)
           end do
         end do
       end do

     endif

   case default
     write(*,*) 'Invalid itime in add_time_piece'
     call lmpi_die
   end select

  end subroutine add_time_piece


!=============================== ENTROPY_TRANSFORM ===========================80
!
!  Performs entropy transform
!
!=============================================================================80
  subroutine entropy_transform(grid,soln,sadj,design,nml_path)

    use fluid,              only : gm1, gamma
    use io,                 only : use_prior
    use solution_writes,    only : solution_output
    use solution_adj,       only : wresta
    use cut_types,          only : cut_cell_activated, cut_cell_visualize, cut
    use cut_visualizations, only : cut_cell_adj_vis, cut_surface_adj_tec
    use design_types,       only : design_type
    use solution_adj,       only : sadj_type
    use info_depr,          only : ncyc

    type(design_type), intent(inout) :: design
    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj

    character(80), intent(in) :: nml_path

    integer :: i

    real(dp) :: state(5), s

    real(dp), parameter :: half = 0.5_dp

  continue

    do i = 1, grid%nnodes01
      state = in_primitive_variables(soln%q_dof(:,i))
      s = log(state(5)/state(1)**gamma)
      sadj%rlam(1,i,1) = (gamma-s)/gm1 &
        - half * state(1) *(state(2)**2 +state(3)**2 +state(4)**2 )/state(5)
      sadj%rlam(2,i,1) = state(1)*state(2)/state(5)
      sadj%rlam(3,i,1) = state(1)*state(3)/state(5)
      sadj%rlam(4,i,1) = state(1)*state(4)/state(5)
      sadj%rlam(5,i,1) = - state(1)/state(5)
    end do

    use_prior = 0 ! prior history is for flow

    call solution_output(grid,soln,ncyc,nml_path,sadj)
    call wresta(grid,soln,sadj,design)
    if ( cut_cell_activated .or. cut_cell_visualize )                          &
      call cut_cell_adj_vis(grid,soln,design,sadj%rlam)
    if ( cut_cell_activated )                                                  &
      call cut_surface_adj_tec(grid, cut, soln, design,                        &
                               soln%totforce(1), sadj%rlam)

  end subroutine entropy_transform


!============================== RELAX_OVERSET_EQNS ===========================80
!
!  Sweep the interpolated points to solve adjoint equations, then move
!  these now-known terms to residuals for solve points and blank the
!  residuals at the interpolated points
!
!=============================================================================80
  subroutine relax_overset_eqns(grid,soln,sadj,crowf,design,lb,ub)

    use solution_adj,   only : sadj_type
    use comprow_types,  only : crow_flow
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use design_types,   only : design_type
    use overset,        only : blank_rhs_equation_adj
    use residual,       only : overset_update_adjoint_res

    integer, intent(in) :: lb, ub

    type(grid_type),   intent(in)    :: grid
    type(crow_flow),   intent(in)    :: crowf
    type(design_type), intent(in)    :: design
    type(soln_type),   intent(in)    :: soln
    type(sadj_type),   intent(inout) :: sadj

  continue

    call relax_interp_pts(grid%nnodes0, grid%nnodes01, grid%iblank, sadj%rlam, &
                          crowf%ia, crowf%ja, design%nfunctions, soln%adim,    &
                          sadj%res, lb, ub)
    call overset_update_adjoint_res(grid%nnodes0, grid%nnodes01, grid%iblank,  &
                                    sadj%res, crowf%ia, crowf%ja, sadj%rlam,   &
                                    soln%adim,design%nfunctions, lb, ub)
    call blank_rhs_equation_adj(grid%iblank,sadj%res)

!  Now we have res = df/dQ + dR/dQ*lambda + Vol/dt*(lambda_n-lambda_n+1) + Rgcl
!                   + [I_i*A*I_f]^T*lambda_i
!  at the solve points and hardwired zero residuals at the interpolated points

  end subroutine relax_overset_eqns


!=============================== RELAX_INTERP_PTS ============================80
!
! Relaxes the adjoint solution at interpolated points through simple sweeping
!
!=============================================================================80
  subroutine relax_interp_pts(nnodes0,nnodes01,iblank,rlam,ia,ja,nfunctions,   &
                              adim,res,lb,ub)

    use lmpi,     only : lmpi_reduce, lmpi_bcast, lmpi_master
    use lmpi_app, only : lmpi_xfer
    use residual, only : aii_times_lambda

    integer, intent(in) :: nnodes0, nnodes01, nfunctions, adim, lb, ub

    integer, dimension(nnodes01), intent(in) :: iblank
    integer, dimension(:),        intent(in) :: ia, ja

    real(dp), dimension(:,:,:), intent(in)    :: res
    real(dp), dimension(:,:,:), intent(inout) :: rlam

    integer :: ifcn, i, sweep, total_local_nodes, total_global_nodes

    real(dp), dimension(adim) :: resid, residg

    integer, parameter :: max_nsweeps = 1  ! 1000

    logical, parameter :: monitor_residual = .true.

    logical :: good

    real(dp), parameter :: tolerance = 1.e-17_dp

    logical, save :: init = .true.

  continue

! set up some memory the first time through

    if ( init ) then
      allocate(dlambda(adim,nnodes0,nfunctions))
      allocate(lhs_terms(adim,nnodes01,nfunctions))
      init = .false.
    endif

! Now that we have the RHS, sweep through the system

! First initialize dlambda

    dlambda = 0.0_dp

    sweeping : do sweep = 1, max_nsweeps

      call aii_times_lambda(lhs_terms,dlambda,adim,nfunctions,nnodes01,iblank, &
                            nnodes0,ia,ja,lb,ub)

      compute_residual : if ( monitor_residual ) then

        resid(lb:ub) = 0.0_dp
        residg       = 0.0_dp
        total_local_nodes = 0

        do i = 1, nnodes0
          if ( iblank(i) <= 0 ) then
            total_local_nodes = total_local_nodes + 1
            resid(lb:ub) = resid(lb:ub)                                        &
               + ((dlambda(lb:ub,i,1)-lhs_terms(lb:ub,i,1)) + res(lb:ub,i,1))**2
          endif
        end do

        call lmpi_reduce(resid,residg)
        call lmpi_bcast(residg)

        call lmpi_reduce(total_local_nodes,total_global_nodes)
        call lmpi_bcast(total_global_nodes)

        residg(lb:ub) = sqrt(residg(lb:ub)) / total_global_nodes

        if ( lmpi_master ) then
         write(*,'(1x,a,1x,i0,1x,6e20.10)')'interp points: ',sweep,residg(lb:ub)
        endif

        good = .true.
        do i = lb, ub
          if ( residg(i) > tolerance ) good = .false.
        end do

! Exit sweeping if convergence tolerance satisfied

        if ( good ) then
          if ( lmpi_master ) then
            write(*,'(a,i5,a)') 'Exiting overset averaging after ',sweep,      &
                                ' sweeps.'
          endif
          exit sweeping
        endif

      endif compute_residual

      do i = 1, nnodes0
        if ( iblank(i) <= 0 ) then
          dlambda(lb:ub,i,:) = -res(lb:ub,i,:) + lhs_terms(lb:ub,i,:)
        endif
      end do

    end do sweeping

! Now update the adjoint solution at the interpolated nodes

    do i = 1, nnodes0
      if (iblank(i) <= 0) rlam(lb:ub,i,:) = rlam(lb:ub,i,:) + dlambda(lb:ub,i,:)
    end do

! Exchange ghost data

    do ifcn = 1, nfunctions
      call lmpi_xfer(rlam(:,:,ifcn))
    end do

  end subroutine relax_interp_pts


!========================== SETUP_BP =========================================80
!
! Loads best-practices-related stuff as a hack to accommodate Jim's version
! of SA model in adjoint solver
!
!=============================================================================80
  subroutine setup_bp(grid)

    use lmpi,          only : lmpi_master
    use debug_defs,    only : gradient_construction_rhs,                       &
                              gradient_construction_lhs
    use turb_sa_const, only : turbinf
    use kinddefs,      only : dp
    use grid_types,    only : grid_type
    use bc_names,      only : farfield_riem

    type(grid_type), intent(in) :: grid

    integer :: ib

  continue

    if ( lmpi_master ) then
      write(*,*)
      write(*,*) 'Using best practice form of SA model...'
      write(*,*)
    endif

    gradient_construction_rhs = 1 ! to ensure residual pieces are correct
    gradient_construction_lhs = 1 ! to ensure elasticity operates correctly
    turbinf = 3.0_dp

! Watch out for farfield BC switch that best-practice triggers in flow solver

    boundary_search : do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc == farfield_riem ) then
        if ( lmpi_master ) then
          write(*,*) 'Be careful: farfield_riem boundary condition is set.'
          write(*,*) 'Normally using best-practice, flow solver would have'
          write(*,*) 'set this to farfield_roe if it originated as a BC index'
          write(*,*) 'of 3.  This would yield an inconsistent linearization'
          write(*,*) 'in the adjoint solver.'
        endif
        exit boundary_search
      endif
    end do boundary_search

  end subroutine setup_bp


!========================== READ_LAMBDA_CHAOS ================================80
!
! Overloads adjoint solution with that computed by external chaos solver
!
!=============================================================================80
  subroutine read_lambda_chaos(nnodes01,nnodesg,l2g,rlam)

    use info_depr,         only : physical_timestep
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use string_utils,      only : int_to_s
    use lmpi,              only : lmpi_master, lmpi_bcast

    integer, intent(in) :: nnodes01, nnodesg

    integer, dimension(:), intent(in) :: l2g

    real(dp), dimension(:,:,:), intent(out) :: rlam

    integer :: iu, i, gnode

    real(dp), dimension(5,nnodesg) :: temp

    character(len=1000) :: filename

  continue

    if ( lmpi_master ) then
      iu = available_unit()
      filename = 'adjoint_solution.' // trim(int_to_s(physical_timestep))
      call se_open(iu,file=trim(filename),form='unformatted',status='unknown', &
                   access='stream')
      read(iu) temp(:,:)
      close(iu)
    endif

    call lmpi_bcast(temp)

    do i = 1, nnodes01
      gnode = l2g(i)
      rlam(:,i,1) = temp(:,gnode)
    end do

  end subroutine read_lambda_chaos

  include 'in_primitive_variables.f90'

end module relax_adjoint

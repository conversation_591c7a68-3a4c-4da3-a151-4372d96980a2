module structured_lines

  use kinddefs,         only : dp
  use line_types,       only : line_type
  use grid_types,       only : grid_type
  use comprow_types,    only : crow_flow
  use lmpi,             only : lmpi_conditional_stop, lmpi_master
  use inviscid_flux,    only : tecplot_mapped_lines
  use complex_functions,only : o
  use fun3d_maximums,   only : ngrid_max

  implicit none

  private

  public :: mapped_lines
  public :: set_mapped_lines, mapped_grad
  public :: line_seek

  type(line_type), dimension(:), allocatable :: mapped_lines

  logical :: first_time_through = .true.

  integer, parameter :: n_seek_max=3

  integer, dimension(ngrid_max,n_seek_max) :: line_seek

contains

!=============================== SET_MAPPED_LINES ============================80
!
!  Set structured lines data.
!
!=============================================================================80
  subroutine set_mapped_lines( grid, crow )

    use info_depr,         only : ngrid
    use implicit_lines,    only : set_dof_lines, recast_lines

    type(grid_type),      intent(in   ) :: grid
    type(crow_flow),      intent(in   ) :: crow

    integer :: n_lines, fl

    integer, dimension(:),   pointer :: endline
    integer, dimension(:,:), pointer :: line

  continue

    if ( first_time_through ) then
      allocate(mapped_lines(ngrid))
      first_time_through = .false.
    endif

    call set_dof_lines( 1, '', grid%project,                       &
                        line, endline, n_lines,                    &
                        grid%dof0, grid%l2g,                       &
                        crow%ia, crow%ja, crow%ia_ns,              &
                        grid%x,  grid%y,  grid%z, .true.  )

    fl = grid%igrid

    call recast_lines( n_lines, line, endline, mapped_lines(fl), grid%y )

    deallocate(line)
    deallocate(endline)

    call set_gptr( grid, mapped_lines(fl))

    if ( tecplot_mapped_lines ) call find_lines( grid, mapped_lines(fl))

  end subroutine set_mapped_lines

!=============================== SET_GPTR ===================================80
!
! Set gptr.
!
!============================================================================80
  subroutine set_gptr( grid, mapped_lines )

    use lmpi,           only : lmpi_bcast, lmpi_reduce
    use info_depr,      only : twod

    type(grid_type),  intent(in)    :: grid

    type(line_type), intent(inout) :: mapped_lines

    integer :: n_lines, i, ii, node, ierr, nedges
    integer :: edge, nedge_lines, node1, node2, line1, line2
    integer :: entry1, entry2, node3, node4, nedge_flux_eval

    integer, dimension(:), allocatable :: node_to_line

    logical :: swapped

  continue

    nedge_flux_eval = grid%nedgeloc
    if ( twod ) nedge_flux_eval = grid%nedgeloc_2d

    allocate( node_to_line(grid%nnodes01) )

    allocate( mapped_lines%gptr( 3, size(grid%eptr,2) ) )

    n_lines = mapped_lines%n_lines

    ierr = 0

    node_to_line(:) = 0
    do i = 1, n_lines
      do ii=mapped_lines%first_entry(i),mapped_lines%first_entry(i+1)-1

        node               = mapped_lines%line_to_dof_index(ii)
        node_to_line(node) = i

      enddo
    enddo

    ! Count the edges.

    nedge_lines = 0

    count_edges: do edge = 1, nedge_flux_eval

      node1 = grid%eptr(1,edge)
      node2 = grid%eptr(2,edge)

      line1 = node_to_line(node1)
      line2 = node_to_line(node2)

      if ( line1 /= line2 ) cycle count_edges
      if ( line1 == 0     ) cycle count_edges

      nedge_lines = nedge_lines + 1

    enddo count_edges

    i = nedge_lines ; call lmpi_reduce( i, nedge_lines )
    call lmpi_bcast( nedge_lines )
    i = nedge_flux_eval ; call lmpi_reduce( i, nedges )
    call lmpi_bcast( nedges )
    if ( lmpi_master ) then
      write(*,*) ' Fraction edges within lines=',real(nedge_lines,dp)/&
                                                 real(     nedges,dp)
    endif

    mapped_lines%gptr(1:3,:) = 0

    edge_loop: do edge = 1, nedge_flux_eval

      node1 = grid%eptr(1,edge)
      node2 = grid%eptr(2,edge)

      line1 = node_to_line(node1)
      line2 = node_to_line(node2)

      if ( line1 /= line2 ) cycle edge_loop
      if ( line1 == 0     ) cycle edge_loop

      entry1 = 0
      entry2 = 0
      do ii=mapped_lines%first_entry(line1),mapped_lines%first_entry(line1+1)-1
        node = mapped_lines%line_to_dof_index(ii)
        if ( node1 == node ) then
          entry1 = ii
        endif
        if ( node2 == node ) then
          entry2 = ii
        endif
        if ( entry1 > 0 .and. entry2 > 0 ) exit
      enddo

      if ( entry1 == 0 .or. entry2 == 0 ) then
        write(*,*) ' Failure line=',line1,&
        ' node1=',node1,' node2=',node2,  &
        ' entry1=',entry1,' entry2=',entry2
        ierr = 1 ; exit
      endif

      swapped = .false.
      if ( entry2 < entry1 ) then
        swapped = .true.
      endif

      node3 = 0
      node4 = 0
      if ( .not.swapped ) then

        if ( entry1 /= mapped_lines%first_entry(line1) ) then
          node3 = mapped_lines%line_to_dof_index(entry1-1)
        endif

        if ( entry2 /= mapped_lines%first_entry(line1+1) - 1 )then
          node4 = mapped_lines%line_to_dof_index(entry2+1)
        endif

      else

        if ( entry2 /= mapped_lines%first_entry(line1) ) then
          node4 = mapped_lines%line_to_dof_index(entry2-1)
        endif

        if ( entry1 /= mapped_lines%first_entry(line1+1) - 1 )then
          node3 = mapped_lines%line_to_dof_index(entry1+1)
        endif

      endif

      mapped_lines%gptr(1,edge) = node3
      mapped_lines%gptr(2,edge) = node4
      mapped_lines%gptr(3,edge) = line1

    enddo edge_loop

    call lmpi_conditional_stop(ierr,'set_gptr')

    deallocate( node_to_line )

  end subroutine set_gptr

!=================================== MAPPED_GRAD =============================80
!
! Mapped gradients.
!
!=============================================================================80
  subroutine mapped_grad( node1, node2, node3, node4, ndim, x, y, z, q,        &
                          gradx1, grady1, gradz1, gradx2, grady2, gradz2 )

    use kinddefs,        only : dp

    integer,                  intent(in)    :: node1, node2, node3, node4, ndim
    real(dp), dimension(:),   intent(in)    :: x, y, z
    real(dp), dimension(:),   intent(inout) :: gradx1, grady1, gradz1
    real(dp), dimension(:),   intent(inout) :: gradx2, grady2, gradz2
    real(dp), dimension(:,:), intent(in)    :: q

    real(dp) :: x1, y1, z1, x2, y2, z2, x3, y3, z3, x4, y4, z4
    real(dp) :: dx, dy, dz, ds, ds1, ds2, ex, ey, ez

    real(dp), dimension(ndim) :: dqds1, dqds2

    logical, parameter :: mapped = .true.

  continue

    x1 = x(node1)
    y1 = y(node1)
    z1 = z(node1)

    x2 = x(node2)
    y2 = y(node2)
    z2 = z(node2)

    dx = x2 - x1
    dy = y2 - y1
    dz = z2 - z1

    ds = sqrt( dx**2 + dy**2 + dz**2 )

    ex = dx/ds
    ey = dy/ds
    ez = dz/ds

    dqds1(1:ndim) = ( q(1:ndim,node2) - q(1:ndim,node1) )/ds

    dqds2(1:ndim) = dqds1(1:ndim)

    if ( node3 /= 0 .and. mapped ) then

      dqds1(1:ndim) = 0.5_dp*( q(1:ndim,node2) - q(1:ndim,node3) )/ds

    elseif ( node3 /= 0 ) then

      x3 = x(node3)
      y3 = y(node3)
      z3 = z(node3)

      ds1 = sqrt( (x2 - x3)**2 &
                + (y2 - y3)**2 &
                + (z2 - z3)**2 )

      dqds1(1:ndim) = ( q(1:ndim,node2) - q(1:ndim,node3) )/ds1

    endif

    gradx1(1:ndim) = ex*dqds1(1:ndim)
    grady1(1:ndim) = ey*dqds1(1:ndim)
    gradz1(1:ndim) = ez*dqds1(1:ndim)

    if ( node4 /= 0 .and. mapped ) then

      dqds2(1:ndim) = 0.5_dp*( q(1:ndim,node4) - q(1:ndim,node1) )/ds

    elseif ( node4 /=0 ) then

      x4 = x(node4)
      y4 = y(node4)
      z4 = z(node4)

      ds2 = sqrt( (x4 - x1)**2 &
                + (y4 - y1)**2 &
                + (z4 - z1)**2 )

      dqds2(1:ndim) = ( q(1:ndim,node4) - q(1:ndim,node1) )/ds2

    endif

    gradx2(1:ndim) = ex*dqds2(1:ndim)
    grady2(1:ndim) = ey*dqds2(1:ndim)
    gradz2(1:ndim) = ez*dqds2(1:ndim)

end subroutine mapped_grad

!=============================== FIND_LINES =================================80
!
! Find lines corresponding to specified x, y, z.
!
!============================================================================80
  subroutine find_lines( grid, mapped_lines )

    use lmpi,           only : lmpi_id, lmpi_max_and_maxid, lmpi_bcast
    use info_depr,      only : twod
    use inviscid_flux,  only : x_line, y_line, z_line

    type(grid_type), intent(in) :: grid
    type(line_type), intent(in) :: mapped_lines

    integer :: i, node, edge, node1, node2
    integer :: n, line, nedge_flux_eval, proc_id, n_seek, ploc

    real(dp) :: dx, dy, dz, s, xloc, yloc, zloc, sloc

    real(dp), dimension(n_seek_max) :: x_found, y_found, z_found, s_found

    logical :: namelist_input

    logical, save :: first_grid = .true.

  continue

    namelist_input = .true.

    line_seek(grid%igrid,:) = 0

    s_found(:) = +huge(1._dp)

    if ( .not.namelist_input ) then

      do i=1,n_seek_max
      if ( i == 1 ) then        !L1.4 grid
        x_line(i) = 1225.130_dp
        y_line(i) =  118.954_dp
        z_line(i) =  177.839_dp
      elseif ( i == 2 ) then
        x_line(i) = 1368.000_dp
        y_line(i) =  427.128_dp
        z_line(i) =  193.787_dp
      elseif ( i == 3 ) then
        x_line(i) = 1606.140_dp
        y_line(i) =  798.347_dp
        z_line(i) =  221.975_dp
      endif
      if ( i == 1 ) then        !L4.4 grid
        x_line(i) = 1298.000_dp
        y_line(i) =  121.459_dp
        z_line(i) =  167.000_dp
      elseif ( i == 2 ) then
        x_line(i) = 1338.000_dp
        y_line(i) =  232.000_dp
        z_line(i) =  167.000_dp
      elseif ( i == 3 ) then
        x_line(i) = 1654.000_dp
        y_line(i) =  840.704_dp
        z_line(i) =  240.800_dp
      elseif ( i == 4 ) then
        x_line(i) = 0._dp
        y_line(i) = 0._dp
        z_line(i) = 0._dp
      endif
    enddo
    endif

    n_seek = 0
    do i=1,n_seek_max
      if ( abs(x_line(i)) < huge(1._dp) .and. &
           abs(y_line(i)) < huge(1._dp) .and. &
           abs(z_line(i)) < huge(1._dp) ) then
        n_seek = n_seek + 1
      else
        exit
      endif
    enddo

    if ( lmpi_master ) then
      write(*,*)
      write(*,*) ' At level=',grid%igrid
      write(*,*) ' Seeking lines via find_lines...n_seek=',n_seek
    endif
    do i=1,n_seek
      if ( lmpi_master ) then
        write(*,*)
        write(*,*) ' Closest line to x/y/z=',&
        o(x_line(i)),o(y_line(i)),o(z_line(i))
      endif
    enddo

    nedge_flux_eval = grid%nedgeloc
    if ( twod ) then
      nedge_flux_eval = grid%nedgeloc_2d
    end if


    edge_loop: do n = 1, nedge_flux_eval

!     Get location of the nodes on opposite sides of the dual interface

      node1 = grid%eptr(1,n)
      node2 = grid%eptr(2,n)

      line  = mapped_lines%gptr(3,n)

      if ( line == 0 ) cycle

      if ( .not.twod                     .and. &
           grid%slen(node1) > 1.0e-10_dp .and. &
           grid%slen(node2) > 1.0e-10_dp       ) cycle

      line  = mapped_lines%gptr(3,n)

      do edge=1,2

        node = node1
        if ( edge == 2 ) node = node2

        if ( .not.twod .and. grid%slen(node) > 1.0e-10_dp ) cycle

        do i=1,n_seek

          dx = grid%x(node) - x_line(i)
          dy = grid%y(node) - y_line(i)
          dz = grid%z(node) - z_line(i)

          if ( dx**2 + dy**2 + dz**2 < s_found(i) ) then

            s_found(i) = dx**2 + dy**2 + dz**2
            x_found(i) = grid%x(node)
            y_found(i) = grid%y(node)
            z_found(i) = grid%z(node)
            line_seek(grid%igrid,i) = line

          endif

        enddo

      enddo

    enddo edge_loop

    xloc = -huge(1._dp)
    yloc = -huge(1._dp)
    zloc = -huge(1._dp)
    do i=1,n_seek

      s = -s_found(i)

      call lmpi_max_and_maxid( real(s,dp), proc_id )
      ploc = proc_id

      if ( proc_id /= lmpi_id ) then
        line_seek(grid%igrid,i) = 0
      else
        xloc = x_found(i)
        yloc = y_found(i)
        zloc = z_found(i)
        sloc = s_found(i)
      endif

      call lmpi_bcast(ploc,proc_id)
      call lmpi_bcast(xloc,proc_id)
      call lmpi_bcast(yloc,proc_id)
      call lmpi_bcast(zloc,proc_id)
      call lmpi_bcast(sloc,proc_id)

      if ( lmpi_master ) then
        write(*,*)
        write(*,"(1x,2(a,i5),11x,a,3f20.10)")        &
        ' grid=',grid%igrid,' i=',i,                 &
        ' x/y/z(request)=',                          &
        o(x_line(i)), o(y_line(i)), o(z_line(i))
        write(*,"(1x,3(a,i5),a,3f20.10,a,e20.10)")   &
        ' grid=',grid%igrid,' i=',i,' ploc=',ploc,   &
        ' x/y/z(  found)=',                          &
        o(xloc), o(yloc), o(zloc), ' ds=',o(sqrt(sloc))
      endif

      if ( first_grid ) then
        if ( lmpi_master ) then
          write(*,"(1x,a,i5)") &
          ' Overwriting x/y/z_line inputs based on grid=',grid%igrid
        endif
        x_line(i) = xloc
        y_line(i) = yloc
        z_line(i) = zloc
      endif

    enddo

    first_grid = .false.

  end subroutine find_lines

end module structured_lines

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite turb_2eqn_routines


  integer, parameter  ::        dp = selected_real_kind(15,307)
  integer, parameter  :: system_i8 = selected_int_kind(10)
  integer, parameter  :: system_i1 = selected_int_kind(2)

  real(dp) :: xmr, tke, omega, nu, betastar, dist
  real(dp), parameter :: tol = 1.0e-8_dp

  integer, parameter :: bsl                 = 201  
  integer, parameter :: menter_sst          = 202  
  integer, parameter :: kw_sst              = 203  
  integer, parameter :: kw_sst2003          = 204  
  integer, parameter :: sst_v               = 205  
  integer, parameter :: sst                 = 206  
  integer, parameter :: sst_2003            = 207  
  integer, parameter :: wilcox1988          = 208  
  integer, parameter :: wilcox1988_v        = 209  
  integer, parameter :: wilcox_kw88         = 210  
  integer, parameter :: wilcox_kw88p        = 211  
  integer, parameter :: wilcox_kw98         = 212  
  integer, parameter :: wilcox_kw06         = 213  
  integer, parameter :: wilcox_kw06p        = 214  
  integer, parameter :: wilcox2006          = 215  
  integer, parameter :: wilcox2006_v        = 216  

  integer, parameter :: wilcox_asm          = 230  
  integer, parameter :: asbm_sst            = 231  
  integer, parameter :: easm_ddes           = 232  
  integer, parameter :: EASMko2003_S        = 233  
  integer, parameter :: hrles               = 240  
  integer, parameter :: kw_des              = 241  
  integer, parameter :: wilcox_les          = 242  

  integer, parameter :: abid_linear         = 250  
  integer, parameter :: chien               = 251  
  integer, parameter :: jones_launder       = 252  
  integer, parameter :: k_kL_MEAH2013       = 253  

  integer, parameter :: kw_lag              = 300  


test test_turb_source_kkl


    use ddt,      only : ddt7, assignment(=), operator(*), operator(+)         &
                       , operator(/), ddt_min, operator(-), operator(**)

    use fluid,           only : gamma, sutherland_constant
    use fluid,           only : setup_fluid_gamma, setup_sutherland_constant
    use info_depr,       only : tref 
    use nml_two_d_trans, only : turb_transition
    use debug_defs,      only : test_freestream
    use solution_types,  only : compressible, incompressible
    use turb_kw_const,   only : kappa, slen_kdes, cmu_0
    use turb_functions,  only : get_prodk_ddt
    use turb_parameters, only : t_prod, t_dest
    use turb_util,       only : kloc, wloc

    integer            :: i
    integer            :: eqn_set 
    integer, parameter :: nnodes01 = 1
    integer, parameter :: n_tot    = 5
    integer, parameter :: n_turb   = 2
    integer, parameter :: n_grd    = 7
             
    real(dp), dimension(nnodes01)        :: vol
    real(dp), dimension(n_tot,nnodes01)  :: qnode
    real(dp), dimension(n_turb,nnodes01) :: turb 
    real(dp), dimension(n_grd,nnodes01)  :: gradx
    real(dp), dimension(n_grd,nnodes01)  :: grady
    real(dp), dimension(n_grd,nnodes01)  :: gradz
    integer,  dimension(nnodes01)        :: iflagslen
    real(dp), dimension(nnodes01)        :: slen 
    real(dp)                             :: xmr 
    real(dp), dimension(nnodes01)        :: u_double_prime
    real(dp), dimension(6,nnodes01)      :: rhotauij

    type(ddt7), dimension(n_tot+n_turb) :: actual

    call setup_fluid_gamma
    call setup_sutherland_constant

    kloc     = n_grd - n_turb + 1
    wloc     = n_grd - n_turb + 2

    i         = 1
    eqn_set   = 0
    vol(i)            = 1.0_dp
    qnode(1,i)        = 1.0_dp
    qnode(2,i)        = 0.0_dp
    qnode(3,i)        = 0.0_dp
    qnode(4,i)        = 0.0_dp
    qnode(5,i)        = 0.714_dp
    turb(1,i)         = 1.0_dp
    turb(2,i)         = 1.0_dp

    gradx(1:n_tot,i)  = 1.0_dp
    gradx(6,i)        = 0.0_dp
    gradx(7,i)        = 0.0_dp

    grady(1:n_tot,i)  = 1.0_dp
    grady(6,i)        = 0.0_dp
    grady(7,i)        = 0.0_dp

    gradz(1:n_tot,i)  = 1.0_dp
    gradz(6,i)        = 0.0_dp
    gradz(7,i)        = 0.0_dp

    iflagslen(i)      = 0
    slen(i)           = 1.0_dp
    xmr               = 1.0_dp
    u_double_prime(i) = 1.0_dp
    rhotauij(1,i) = 0.0_dp
    rhotauij(2,i) = 0.0_dp
    rhotauij(3,i) = 0.0_dp
    rhotauij(4,i) = 1.0_dp
    rhotauij(5,i) = 0.0_dp
    rhotauij(6,i) = 0.0_dp

    actual = turb_source_kkl  ( i, eqn_set, nnodes01, n_tot, n_turb, n_grd  &
                    , vol, qnode, turb, gradx, grady, gradz                 &
                    , iflagslen, slen, xmr, u_double_prime, rhotauij )

    write(6,'(a,10(1x,es15.8))') 'actual%f:.....', actual%f
    write(6,'(a,10(1x,es15.8))') 'actual%d(6):..', actual%d(6)
    write(6,'(a,10(1x,es15.8))') 'actual%d(7):..', actual%d(7)


   write(6,'(a,f20.10,a)') '! actual'
   write(6,'(a,f20.10,a)') 'assert_equal_within(',actual(6)%f,'_dp, actual(6)%f, tol      ) '
   write(6,'(a,f20.10,a)') 'assert_equal_within(',actual(7)%f,'_dp, actual(7)%f, tol      ) '
   write(6,'(a,f20.10,a)') 'assert_equal_within(',actual(6)%d(6),'_dp, actual(6)%d(6), tol ) '
   write(6,'(a,f20.10,a)') 'assert_equal_within(',actual(6)%d(7),'_dp, actual(6)%d(7), tol ) '
   write(6,'(a,f20.10,a)') 'assert_equal_within(',actual(7)%d(6),'_dp, actual(7)%d(6), tol ) '
   write(6,'(a,f20.10,a)') 'assert_equal_within(',actual(7)%d(7),'_dp, actual(7)%d(7), tol ) '

! actual

assert_equal_within(        2.6148326870_dp, actual(6)%f, tol      ) 
assert_equal_within(       -9.7008709254_dp, actual(7)%f, tol      ) 
assert_equal_within(       -0.9179778089_dp, actual(6)%d(6), tol ) 
assert_equal_within(        0.1643167673_dp, actual(6)%d(7), tol ) 
assert_equal_within(        6.6968683092_dp, actual(7)%d(6), tol ) 
assert_equal_within(       -9.5708709254_dp, actual(7)%d(7), tol ) 

end test

test test_approximate_jacobians

   use turb_kw_const,  only : betastar_w88       &
                            , betastar_0_w98     &
                            , betastar_w06       &
                            , betastar_0_asm     &
                            , betastar_sst       &
                            , beta_w88           &
                            , beta_0_w98         &
                            , beta_0_w06         &
                            , beta_0_asm

  integer                  :: turbulence_model_int
  integer                  :: n_turb
  real(dp)                 :: betastar, beta, ctg
  real(dp)                 :: f4, crossd, blend, rho, vol, phi
  real(dp)                 :: lambda
  real(dp), dimension(2,2) :: actual

  n_turb   = 2
  ctg      = 1.0_dp
  f4       = 1.0_dp
  rho      = 0.5_dp
  tke      = 1.0e-4_dp
  omega    = 1.0e-5_dp
  phi      = 1.0_dp
  vol      = 1.0e-6_dp
  xmr      = 1.0e-6_dp
  lambda   = 0.0_dp

!-------------------------------------------------------------
  turbulence_model_int = wilcox_kw88p
  crossd           = 0.0_dp
  blend            = 0.0_dp
  betastar         = betastar_w88
  beta             = beta_w88

  write(6,'(a,f20.10,a)') 'test wilcox_kw88p'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

  assert_real_equal(  0.9_dp, actual(1,1) )
  assert_real_equal(  9.0_dp, actual(1,2) )
  assert_real_equal(  0.0_dp, actual(2,1) )
  assert_real_equal(  1.5_dp, actual(2,2) )

!-------------------------------------------------------------
  turbulence_model_int = wilcox_kw98
  crossd           = 1.0e-3_dp
  betastar         = betastar_0_w98
  beta             = beta_0_w98

  write(6,'(a,f20.10,a)') 'test wilcox_kw98'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

  assert_real_equal(   0.45_dp, actual(1,1) )
  assert_real_equal(   4.5_dp,  actual(1,2) )
  assert_real_equal(   0.0_dp,  actual(2,1) )
  assert_real_equal(  50.72_dp, actual(2,2) )

!-------------------------------------------------------------
  turbulence_model_int = wilcox_kw06
  crossd           = 1.0e-3_dp
  betastar         = betastar_w06
  beta             = beta_0_w06

  write(6,'(a,f20.10,a)') 'test wilcox_kw06'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

  assert_real_equal(   0.45_dp,  actual(1,1) )
  assert_real_equal(   4.5_dp,   actual(1,2) )
  assert_real_equal(   0.0_dp,   actual(2,1) )
  assert_real_equal(   0.708_dp, actual(2,2) )

!-------------------------------------------------------------
  turbulence_model_int = kw_sst
  crossd           = 1.0e-3_dp
  betastar         = betastar_sst
  beta             = 0.0_dp

  write(6,'(a,f20.10,a)') 'test kw_sst'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

  assert_real_equal(   0.90_dp,  actual(1,1) )
  assert_real_equal(   9.0_dp,   actual(1,2) )
  assert_real_equal(   0.0_dp,   actual(2,1) )
  assert_real_equal(   1.656_dp, actual(2,2) )

!-------------------------------------------------------------
  turbulence_model_int = wilcox_les
  crossd           = 1.0e-3_dp
  betastar         = betastar_w06
  beta             = beta_0_w06

  write(6,'(a,f20.10,a)') 'test wilcox_les'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

  assert_real_equal(   0.45_dp,  actual(1,1) )
  assert_real_equal(   4.5_dp,   actual(1,2) )
  assert_real_equal(   0.0_dp,   actual(2,1) )
  assert_real_equal(  50.708_dp, actual(2,2) )

!-------------------------------------------------------------
  turbulence_model_int = kw_des
  crossd           = 1.0e-3_dp
  vol              = 1.0e-6_dp
  blend            = 1.0_dp
  betastar         = betastar_w06
  beta             = beta_0_w06

  write(6,'(a,f20.10,a)') 'test kw_des'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

  assert_real_equal(   0.45_dp,  actual(1,1) )
  assert_real_equal(   0.0_dp,   actual(1,2) )
  assert_real_equal(   0.0_dp,   actual(2,1) )
  assert_real_equal(   0.708_dp, actual(2,2) )
!-------------------------------------------------------------
  turbulence_model_int = kw_des
  crossd           = 1.0e-3_dp
  vol              = 1.0e-0_dp
  blend            = 0.0_dp
  betastar         = betastar_w06
  beta             = beta_0_w06

  write(6,'(a,f20.10,a)') 'test kw_des-2'
  actual = approximate_jacobians_2eqn ( n_turb, turbulence_model_int    &
                        , betastar, beta, ctg, f4, crossd, blend    &
                        , rho, tke, omega, phi, vol, xmr, lambda  )

! write(6,'(a)') 'kw-des'
! write(6,'(a,2(1x,f20.10))') 'd(1,1:2)',actual(1,1), actual(1,2)
! write(6,'(a,2(1x,f20.10))') 'd(2,1:2)',actual(2,1), actual(2,2)
  assert_equal_within(   0.45, actual(1,1), 1.0e-5_dp )
  assert_real_equal(   0.0_dp,  actual(1,2) )
  assert_real_equal(   0.0_dp,  actual(2,1) )
  assert_real_equal(   0.708_dp, actual(2,2) )

end test


end test_suite

! Routines to check validity of unstructured mesh data for FUN3D

module puns3d_checker

  implicit none

  private

  public :: chkgrd, chkelem, chkbnd, common_element

contains

!================================ CHKGRD =====================================80
!
! Check to make sure tetrahedrons are right-handed; if not, swap edges to fix
! the the offending tetrahedra
!
!=============================================================================80

  subroutine chkgrd( act_size, ncell, nnodesg, c2n, x, y, z )

    use kinddefs, only : dp
    use lmpi,     only : lmpi_reduce, lmpi_master

    integer,                         intent(in)    :: act_size,ncell,nnodesg
    integer,  dimension(4,act_size), intent(inout) :: c2n
    real(dp), dimension(nnodesg),    intent(in)    :: x,y,z

    integer  :: i,n1,n2,n3,n4
    integer  :: swap, value

    real(dp) :: ax,ay,az,bx,by,bz,cx,cy,cz,crdot

    real(dp), parameter :: my_0 = 0._dp

  continue

!   check tetrahedral volumes

    swap = 0

    do i = 1, ncell

      n1 = c2n(1,i)
      n2 = c2n(2,i)
      n3 = c2n(3,i)
      n4 = c2n(4,i)

      ax = x(n2) - x(n1)
      ay = y(n2) - y(n1)
      az = z(n2) - z(n1)

      bx = x(n3) - x(n1)
      by = y(n3) - y(n1)
      bz = z(n3) - z(n1)

      cx = x(n4) - x(n1)
      cy = y(n4) - y(n1)
      cz = z(n4) - z(n1)

      crdot = (ay*bz - by*az)*cx - (ax*bz - bx*az)*cy + (ax*by - bx*ay)*cz

      if (crdot < my_0) then ! volume is left-handed, renumber
        c2n(2,i) = n3
        c2n(3,i) = n2
        swap = swap + 1
      end if

    end do

    call lmpi_reduce(swap, value); swap = value

    if (lmpi_master .and. swap > 0) then
      write (*,'(a,i0,a,a)') '     chkgrd: ', swap,' left-handed tets were ',  &
                        'edge-swapped to make volumes positive'
    end if

  end subroutine chkgrd

!================================ CHKELEM ====================================80
!
! Check to make sure elements of a given type are right-handed; if not, swap
! edges to fix the the offending elements
!
!=============================================================================80

  subroutine chkelem( ielem, grid )

    use kinddefs,        only : dp
    use lmpi,            only : lmpi_reduce, lmpi_master, lmpi_die
    use grid_types,      only : grid_type
    use element_defs,    only : element_metric

    integer,                              intent(in)    :: ielem
    type(grid_type),                      intent(inout) :: grid

    real(dp)                                            :: volume
    integer                                             :: i, swap, value

    real(dp), parameter                                 :: my_0 = 0._dp

  continue

!   check cell volumes

    swap = 0

    do i = 1, grid%elem(ielem)%ncell

      call element_metric( i, ielem, grid, volume )

      if (volume < my_0) then ! assume volume is left-handed, renumber
        select case( grid%elem(ielem)%type_cell )
          case('tet')   ! Swap nodes 2 and 3
            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(3,i))
            grid%elem(ielem)%c2n(3,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(3,i))
            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(3,i))
          case('pyr')   ! Swap nodes 2 and 4
            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(4,i))
            grid%elem(ielem)%c2n(4,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(4,i))
            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(4,i))
          case('prz')   ! Swap faces 1 and 2
            grid%elem(ielem)%c2n(1,i) = ieor(grid%elem(ielem)%c2n(1,i),        &
                                             grid%elem(ielem)%c2n(2,i))
            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(1,i),        &
                                             grid%elem(ielem)%c2n(2,i))
            grid%elem(ielem)%c2n(1,i) = ieor(grid%elem(ielem)%c2n(1,i),        &
                                             grid%elem(ielem)%c2n(2,i))

            grid%elem(ielem)%c2n(4,i) = ieor(grid%elem(ielem)%c2n(4,i),        &
                                             grid%elem(ielem)%c2n(3,i))
            grid%elem(ielem)%c2n(3,i) = ieor(grid%elem(ielem)%c2n(4,i),        &
                                             grid%elem(ielem)%c2n(3,i))
            grid%elem(ielem)%c2n(4,i) = ieor(grid%elem(ielem)%c2n(4,i),        &
                                             grid%elem(ielem)%c2n(3,i))

            grid%elem(ielem)%c2n(6,i) = ieor(grid%elem(ielem)%c2n(6,i),        &
                                             grid%elem(ielem)%c2n(5,i))
            grid%elem(ielem)%c2n(5,i) = ieor(grid%elem(ielem)%c2n(6,i),        &
                                             grid%elem(ielem)%c2n(5,i))
            grid%elem(ielem)%c2n(6,i) = ieor(grid%elem(ielem)%c2n(6,i),        &
                                             grid%elem(ielem)%c2n(5,i))
          case('hex')   ! Swap faces 1 and 2
            grid%elem(ielem)%c2n(1,i) = ieor(grid%elem(ielem)%c2n(1,i),        &
                                             grid%elem(ielem)%c2n(5,i))
            grid%elem(ielem)%c2n(5,i) = ieor(grid%elem(ielem)%c2n(1,i),        &
                                             grid%elem(ielem)%c2n(5,i))
            grid%elem(ielem)%c2n(1,i) = ieor(grid%elem(ielem)%c2n(1,i),        &
                                             grid%elem(ielem)%c2n(5,i))

            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(6,i))
            grid%elem(ielem)%c2n(6,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(6,i))
            grid%elem(ielem)%c2n(2,i) = ieor(grid%elem(ielem)%c2n(2,i),        &
                                             grid%elem(ielem)%c2n(6,i))

            grid%elem(ielem)%c2n(3,i) = ieor(grid%elem(ielem)%c2n(3,i),        &
                                             grid%elem(ielem)%c2n(7,i))
            grid%elem(ielem)%c2n(7,i) = ieor(grid%elem(ielem)%c2n(3,i),        &
                                             grid%elem(ielem)%c2n(7,i))
            grid%elem(ielem)%c2n(3,i) = ieor(grid%elem(ielem)%c2n(3,i),        &
                                             grid%elem(ielem)%c2n(7,i))

            grid%elem(ielem)%c2n(4,i) = ieor(grid%elem(ielem)%c2n(4,i),        &
                                             grid%elem(ielem)%c2n(8,i))
            grid%elem(ielem)%c2n(8,i) = ieor(grid%elem(ielem)%c2n(4,i),        &
                                             grid%elem(ielem)%c2n(8,i))
            grid%elem(ielem)%c2n(4,i) = ieor(grid%elem(ielem)%c2n(4,i),        &
                                             grid%elem(ielem)%c2n(8,i))
          case default
            if (lmpi_master) write (*,*) 'chkelem: unknown type_cell'
            call lmpi_die
        end select
        swap = swap + 1
      end if

    end do

    call lmpi_reduce(swap, value); swap = value

    if (lmpi_master .and. swap > 0) then
      write (*,'(a,i0,a,a,a)') '  chkelem: ', swap,' left-handed ',            &
                               grid%elem(ielem)%type_cell,                     &
                               ' cells were modified to make positive volume'
    end if

  end subroutine chkelem

!==================================== CHKBND =================================80
!
! Check to make sure surface elements are right handed, with normal pointing
! into grid; if not, swap nodes to fix offending surface elements (can only
! done if surface element is a triangle)
!
!=============================================================================80

  subroutine chkbnd( ibound, nnodesg, nbound, nloc, x, y, z, locs, locvc,      &
                     locvc_type, nelem, elem, nbface, f2nb,                    &
                     type_face, node_per_face, ibc, verbose, ierr )

    use kinddefs,      only : dp
    use element_types, only : elem_type

    logical,                               intent(in)   :: verbose
    character(len=4),                      intent(in)   :: type_face
    integer,                               intent(in)   :: ibc
    integer,                               intent(in)   :: nnodesg, nbface
    integer,                               intent(in)   :: node_per_face
    integer,                               intent(in)   :: ibound, nbound
    integer,                               intent(in)   :: nelem
    integer,                               intent(in)   :: nloc
    integer,         dimension(nnodesg+1), intent(in)   :: locs
    integer,         dimension(nloc),      intent(in)   :: locvc
    integer,         dimension(nloc),      intent(in)   :: locvc_type
    real(dp),        dimension(nnodesg),   intent(in)   :: x, y, z
    type(elem_type), dimension(nelem),     intent(in)   :: elem
    integer,         dimension(:,:),       intent(inout):: f2nb
    integer,                               intent(out)  :: ierr

    logical :: found
    integer :: swap, fatal_bc_error, iface, ielem, node
    integer :: missingcell,nodepercell, max_nodepercell

    integer, dimension(node_per_face) :: nd ! to store face nodes

    continue

!   check to make sure nodes on boundary faces are numbered
!   so that resultant normal points into domain, also make
!   sure that the boundary face is a consistent with a volume
!   cell face

!   initialize some flags

    ierr = 0
    fatal_bc_error = 0
    swap        = 0
    missingcell = 0

    max_nodepercell = 0
    do ielem = 1, nelem
      nodepercell = elem(ielem)%node_per_cell
      if (nodepercell > max_nodepercell) max_nodepercell = nodepercell
    end do

!   loop over boundary faces

    boundary_face_i: do iface=1,nbface

      do node=1,node_per_face
        nd(node) = f2nb(iface,node)
      end do

!     search over volume elements adjacent to each surface node to find a
!     common element that contains all the face nodes in the nd array

      found = .false.

      call common_element(nnodesg, iface, nloc, locs, locvc,                   &
                          locvc_type, nelem, elem, x, y, z,                    &
                          nbface, f2nb, type_face, node_per_face,              &
                          nd, ibound, found, swap, verbose,                    &
                          max_nodepercell, ierr )
      if (ierr == 1) return

      if (.not. found) then
        missingcell = missingcell + 1
      end if

    end do boundary_face_i

    if (swap > 0) then
      write(*,'(a,i7,a,a4,a,a,i3)')                                            &
             '  chkbnd: warning ',swap,' left-handed ',type_face,' bc faces',  &
             ' detected, bc = ',ibound
        write(*,'(a)') '    these faces were fixed by node-swapping'
    end if

    if (missingcell > 0) then

      fatal_bc_error = fatal_bc_error + 1

      write(*,*) '  chkbnd: error bc faces without a volume cell,',            &
                 ' bc = ',ibound,', ibc = ', ibc

    end if

    if (fatal_bc_error > 0) then
      write(*,'(a,a4,a)') '  chkbnd: error, fatal bc error - ',type_face,      &
                        ' boundary faces'
      write(*,'(a,i4,a,i4,a)') '  chkbnd: error ',fatal_bc_error ,             &
                               ' boundaries of ',nbound ,' affected'
      write(*,'(a)') '    This means that the boundary faces do not correspond'
      write(*,'(a)') '    to the volume cells. This error is often caused on'
      write(*,'(a)') '    the symmetry plane by vgrid, try runing vgrid_bcfix'
      write(*,'(a)') '  chkbnd: Error, stopping... '

      ierr = 1

    end if

  end subroutine chkbnd

!================================ COMMON_ELEMENT =============================80
!
! Find the volume element (cell) that contains the boundary nodes stored in the
! nd array, corresponding to boundary face iface. Also make sure the boundary
! normal points into this cell; if not, swap nodes (renumber f2nb) to make the
! normal point inwards
!
!=============================================================================80

  subroutine common_element( nnodesg, iface, nloc, locs,                       &
                             locvc, locvc_type, nelem, elem,                   &
                             x, y, z,  nbface, f2nb, type_face,                &
                             node_per_face, nd, ibound, found,                 &
                             swap, verbose, max_nodepercell, ierr )

    use kinddefs,      only : dp
    use element_types, only : elem_type

    character(len=4),                          intent(in)    :: type_face
    logical,                                   intent(in)    :: verbose
    logical,                                   intent(inout) :: found
    integer,                                   intent(inout) :: swap
    integer,                                   intent(in)    :: iface
    integer,                                   intent(in)    :: nbface
    integer,                                   intent(in)    :: ibound
    integer,                                   intent(in)    :: node_per_face
    integer,                                   intent(in)    :: nnodesg
    integer,                                   intent(in)    :: nelem
    integer,                                   intent(in)    :: nloc
    integer,                                   intent(in)    :: max_nodepercell
    integer,         dimension(nnodesg+1),     intent(in)    :: locs
    integer,         dimension(node_per_face), intent(inout) :: nd
    integer,         dimension(nbface,node_per_face+2), &
                                               intent(inout) :: f2nb
    integer,         dimension(nloc),          intent(in)    :: locvc
    integer,         dimension(nloc),          intent(in)    :: locvc_type
    real(dp),        dimension(nnodesg),       intent(in)    :: x, y, z
    type(elem_type), dimension(nelem),         intent(in)    :: elem
    integer,                                   intent(out)   :: ierr

    integer :: i, j, k, nnd_int, cnt, icell, node, flag, ielem, nde

    integer, dimension(max_nodepercell) :: nd_int
    integer, dimension(node_per_face)   :: el, el_type

    real(dp) :: crdot
    real(dp) :: xave_on, yave_on, zave_on
    real(dp) :: xave_off, yave_off, zave_off
    real(dp) :: xnorm, ynorm, znorm
    real(dp) :: dx, dy, dz
    real(dp) :: x1, y1, z1, x2, y2, z2
    real(dp) :: x3, y3, z3, x4, y4, z4

    continue

! start at local face node 1 and look over all cells containing face node nd(1)

    ierr = 0
    outer_loop : do i = locs(nd(1))+1,locs(nd(1)+1)

      el(1)      = locvc(i)       ! an element containing node nd(1)
      el_type(1) = locvc_type(i)  ! pointer to element type

!     loop over the remaining face nodes, looking for a match of both element
!     number el and element type el_type

      flag = 0

      middle_loop : do node = 2,node_per_face

        flag = 0

        inner_loop : do j = locs(nd(node))+1,locs(nd(node)+1)

          el(node)      = locvc(j)       ! an element containing node nd(node)
          el_type(node) = locvc_type(j)  ! pointer to element type

          if (el(node) == el(1) .and. el_type(node) == el_type(1)) then

!           found a match for this node; now check the rest

            flag = 1

            exit inner_loop

          end if

        end do inner_loop

        if (flag == 0) cycle outer_loop ! no match; pick another candidate el(1)

      end do middle_loop

      if (flag == 1) then

        found = .true.

        exit outer_loop

      end if

    end do outer_loop

    if (.not. found) then

!     search failed

      write(*,*)
      write(*,'(2(a,i7))')'stopping...unable to find common element for face ',&
                          iface,' of boundary ',ibound

      write(*,*) 'if this is a VGrid grid, consider running'
      write(*,*) ' utils/repair_vgrid_mesh'
      write(*,*) '<NAME_EMAIL>'

!      write(*,'(a,10i7)') 'boundary nd array ',(nd(j),j=1,node_per_face)

!      do node=1,node_per_face
!        write(*,*)
!        write(*,'(a,40i6)')'node,locvc      ',nd(node),                       &
!        (locvc(j),j=locs(nd(node))+1,locs(nd(node)+1))
!        write(*,'(a,i6,40a6)')'node,locvc_type ',nd(node),                    &
!        (elem(locvc_type(j))%type_cell,j=locs(nd(node))+1,locs(nd(node)+1))

!      end do

      ierr = 1; return

    end if

!   search successful

    icell = el(1)                    ! common element (cell) found

    ielem = el_type(1)               ! pointer to common cell type

!   store the common cell number in the 1st extra location in f2n
!   store the pointer to the common cell type in the 2nd

    f2nb(iface,node_per_face+1) = icell
    f2nb(iface,node_per_face+2) = ielem

!   now check that face normal points into the domain

!   first search over nodes of cell "icell", of element type number ielem,
!   looking for interior nodes (ones that do not match the boundary nodes
!   of the current face)

    nnd_int = 0      ! number of interior nodes found in cell
    nd_int  = 0      ! array with interior nodes in cell

    do k=1,elem(ielem)%node_per_cell
      nde = elem(ielem)%c2n(k,icell)
      cnt = 0
      do j=1,node_per_face
        if (nde /= nd(j)) then
          cnt = cnt + 1
        end if
      end do
      if (cnt == node_per_face) then
!       nde differs from all nd(j)...it is an interior node, not on the boundary
        nnd_int         = nnd_int + 1
        nd_int(nnd_int) = nde
      end if
    end do

!   check to make sure that we have consistency between number of interior
!   nodes and number of boundary nodes in the cell

    if (nnd_int /= (elem(ielem)%node_per_cell - node_per_face)) then
      write(*,'(3(a,i7))') ' common_element error : cell ',icell,              &
                           ' found for face ',iface,' on boundary ',ibound
      write(*,'(2(a,i2))') '   interior nodes + face nodes = ',nnd_int,' + ',  &
                           node_per_face
      write(*,'(1(a,i2))') '   node_per_cell               = ',                &
                           elem(ielem)%node_per_cell
     ierr = 1; return
    end if

!   get average x,y,z of nodes on the surface, and those off the surface

    xave_on = 0._dp
    yave_on = 0._dp
    zave_on = 0._dp

    do j=1,node_per_face
      xave_on =  xave_on + x(nd(j))
      yave_on =  yave_on + y(nd(j))
      zave_on =  zave_on + z(nd(j))
    end do

    xave_on =  xave_on/real(node_per_face)
    yave_on =  yave_on/real(node_per_face)
    zave_on =  zave_on/real(node_per_face)

    xave_off = 0._dp
    yave_off = 0._dp
    zave_off = 0._dp

    do j=1,nnd_int
      xave_off = xave_off + x(nd_int(j))
      yave_off = yave_off + y(nd_int(j))
      zave_off = zave_off + z(nd_int(j))
    end do

    xave_off =  xave_off/real(nnd_int)
    yave_off =  yave_off/real(nnd_int)
    zave_off =  zave_off/real(nnd_int)

!   get the normal to the boundary face

    if (type_face == 'tria') then

      x1 = x(nd(1))
      y1 = y(nd(1))
      z1 = z(nd(1))

      x2 = x(nd(2))
      y2 = y(nd(2))
      z2 = z(nd(2))

      x3 = x(nd(3))
      y3 = y(nd(3))
      z3 = z(nd(3))

      xnorm =  0.5_dp*((y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1))
      ynorm = -0.5_dp*((x2 - x1)*(z3 - z1) - (z2 - z1)*(x3 - x1))
      znorm =  0.5_dp*((x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1))

    else if (type_face == 'quad') then

      x1 = x(nd(1))
      y1 = y(nd(1))
      z1 = z(nd(1))

      x2 = x(nd(2))
      y2 = y(nd(2))
      z2 = z(nd(2))

      x3 = x(nd(3))
      y3 = y(nd(3))
      z3 = z(nd(3))

      x4 = x(nd(4))
      y4 = y(nd(4))
      z4 = z(nd(4))

      xnorm = ((y2-y1)*(z3-z1) - (z2-z1)*(y3-y1)                               &
            +  (y3-y1)*(z4-z1) - (z3-z1)*(y4-y1)                               &
            +  (y2-y1)*(z4-z1) - (z2-z1)*(y4-y1)                               &
            +  (y3-y2)*(z4-z2) - (z3-z2)*(y4-y2)) * 0.25_dp

      ynorm = ((z2-z1)*(x3-x1) - (x2-x1)*(z3-z1)                               &
            +  (z3-z1)*(x4-x1) - (x3-x1)*(z4-z1)                               &
            +  (z2-z1)*(x4-x1) - (x2-x1)*(z4-z1)                               &
            +  (z3-z2)*(x4-x2) - (x3-x2)*(z4-z2)) * 0.25_dp

      znorm = ((x2-x1)*(y3-y1) - (y2-y1)*(x3-x1)                               &
            +  (x3-x1)*(y4-y1) - (y3-y1)*(x4-x1)                               &
            +  (x2-x1)*(y4-y1) - (y2-y1)*(x4-x1)                               &
            +  (x3-x2)*(y4-y2) - (y3-y2)*(x4-x2)) * 0.25_dp

    else

      xnorm = 0.0_dp
      ynorm = 0.0_dp
      znorm = 0.0_dp

      write(*,*) ' common_element: error - unknown face type: ',type_face
      ierr = 1; return

    end if

!   vector (pointing toward interior) between off-surface avg point and
!   on-surface avg point

    dx = xave_off - xave_on
    dy = yave_off - yave_on
    dz = zave_off - zave_on

    crdot = dx*xnorm + dy*ynorm + dz*znorm

!   test for left-handedness

    if (crdot < 0._dp) then

      if (verbose) then
        write(8,'(a,a4,a,i7,e13.5)')                                           &
             ' chkbnd: left-handed ',type_face,' face, area x height = ',      &
             iface,crdot/2._dp
        write(8,'(a,i2,a,12i7)') ' chkbnd: node( 1 - ',node_per_face,          &
              ') = ', (nd(j),j=1,node_per_face)
        do j = 1,node_per_face
         write(8,'(a,i2,a,3e13.5)')                                            &
              '         node',j,': x, y, z = ',x(nd(j)),y(nd(j)),z(nd(j))
        end do
      end if

!     reverse node listing so normal points into grid

      do j = 1 ,node_per_face-1
        f2nb(iface,j) = nd(node_per_face-j)
      end do

!     recheck to make sure face is now right-handed!

      do j = 1,node_per_face
       nd(j) = f2nb(iface,j)
      end do

!     get the normal to the boundary face

      if (type_face == 'tria') then

        x1 = x(nd(1))
        y1 = y(nd(1))
        z1 = z(nd(1))

        x2 = x(nd(2))
        y2 = y(nd(2))
        z2 = z(nd(2))

        x3 = x(nd(3))
        y3 = y(nd(3))
        z3 = z(nd(3))

        xnorm =  0.5_dp*((y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1))
        ynorm = -0.5_dp*((x2 - x1)*(z3 - z1) - (z2 - z1)*(x3 - x1))
        znorm =  0.5_dp*((x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1))

      else if (type_face == 'quad') then

        x1 = x(nd(1))
        y1 = y(nd(1))
        z1 = z(nd(1))

        x2 = x(nd(2))
        y2 = y(nd(2))
        z2 = z(nd(2))

        x3 = x(nd(3))
        y3 = y(nd(3))
        z3 = z(nd(3))

        x4 = x(nd(4))
        y4 = y(nd(4))
        z4 = z(nd(4))

        xnorm = ((y2-y1)*(z3-z1) - (z2-z1)*(y3-y1)                             &
              +  (y3-y1)*(z4-z1) - (z3-z1)*(y4-y1)                             &
              +  (y2-y1)*(z4-z1) - (z2-z1)*(y4-y1)                             &
              +  (y3-y2)*(z4-z2) - (z3-z2)*(y4-y2)) * 0.25_dp

        ynorm = ((z2-z1)*(x3-x1) - (x2-x1)*(z3-z1)                             &
              +  (z3-z1)*(x4-x1) - (x3-x1)*(z4-z1)                             &
              +  (z2-z1)*(x4-x1) - (x2-x1)*(z4-z1)                             &
              +  (z3-z2)*(x4-x2) - (x3-x2)*(z4-z2)) * 0.25_dp

        znorm = ((x2-x1)*(y3-y1) - (y2-y1)*(x3-x1)                             &
              +  (x3-x1)*(y4-y1) - (y3-y1)*(x4-x1)                             &
              +  (x2-x1)*(y4-y1) - (y2-y1)*(x4-x1)                             &
              +  (x3-x2)*(y4-y2) - (y3-y2)*(x4-x2)) * 0.25_dp

      end if

!     note: dx, dy, dz unchanged by reordering nodes

      crdot = dx*xnorm + dy*ynorm + dz*znorm

      if (crdot > 0._dp) then

        swap = swap + 1

      else

         write(6,'(2a,a4,a)')'  stopping...node swapping failed to ',          &
                 'correct left-handed ',type_face,' boundary face'
         ierr = 1; return

      end if

    end if

  end subroutine common_element

end module puns3d_checker

module scalar_diffusion

  use kinddefs,          only : dp
  use lmpi,              only : lmpi_conditional_stop, lmpi_id
  use complex_functions, only : o

  implicit none

  private

  public :: check_diagonal_sign

contains

!================================= CHECK_DIAGONAL_SIGN =======================80
!
! Check diagonal entries of jacobians.
!
!=============================================================================80
  subroutine check_diagonal_sign( igrid, n_sta, dof0, a_diag, g2m,             &
                                  x, y, z, slen, dtau, turb, warnings, site )

    use info_depr,         only : ntt, testing
    use cfl_defs,          only : cfl_t_jacobian
    use debug_defs,        only : fraction_allowable_source

    character(len=*), intent(in) :: site

    integer, intent(in) :: igrid, n_sta, dof0

    integer,   dimension(:),     intent(in) :: g2m
    real(dp),  dimension(:),     intent(in) :: x, y, z, slen, dtau
    real(dp),  dimension(:,:),   intent(in) :: turb
    real(dp),  dimension(:,:,:), intent(in) :: a_diag

    integer, intent(out) :: warnings

    integer :: i, row, min_loc, max_loc, ierr, show

    real(dp) :: min_val, max_val, diagonal

  continue

    show =0 ; if ( testing ) show = 1

    warnings = 0

    min_loc = 0           ; max_loc = 0
    min_val = huge(1._dp) ; max_val =-huge(1._dp)
    do i = 1, dof0

      if ( slen(i) < epsilon(1._dp) ) cycle !skip wall

      row      = g2m(i)
      diagonal =             a_diag(n_sta,n_sta,row) &
               + fraction_allowable_source*dtau(row)/cfl_t_jacobian(igrid)


      if ( diagonal < min_val ) then
        min_val = diagonal ; min_loc = i
      endif
      if ( diagonal > max_val ) then
        max_val = diagonal ; max_loc = i
      endif

      if ( diagonal < 0._dp ) then

        warnings = warnings + 1

        if ( show > 0 ) then
          write(6000+lmpi_id,"(1x,2a,i5,a,i2,a,i10,a,e12.4,a,i10)") &
          trim(site),' ntt=',ntt,' grid=',igrid,                    &
          ' diag < 0 warnings=',warnings,                           &
          ' Turb:J-diag=',o(diagonal),' at i=',i
          write(6000+lmpi_id,"(1x,a,3e12.4,2(a,e12.4))")            &
          '                   at x/y/z=',o(x(i)),o(y(i)),o(z(i)),   &
          ' slen=',o(slen(i)),' turb=',o(turb(1,i))
        endif

      endif

    end do

    ierr = 0
    if ( max_loc == 0 .or. min_loc == 0 ) ierr = 1
    call lmpi_conditional_stop(ierr,'Turb:J:NaN:check_diagonal_sign')

    if ( min_val < 1.0e-10_dp ) ierr = 1

    if ( show > 1 .and. ierr > 0 ) then
      write(6000+lmpi_id,"(1x,2a,i5,a,i2,a,i10,a,e12.4,a,i10)") &
      trim(site),' ntt=',ntt,' grid=',igrid,                    &
      ' diag < 0 warnings=',warnings,                           &
      ' Turb:J-diag-min=',min_val,' at min_loc=',min_loc
      write(6000+lmpi_id,"(1x,a,3e12.4,2(a,e12.4))")          &
      ' at x/y/z=',o(x(min_loc)),o(y(min_loc)),o(z(min_loc)), &
      ' slen=',o(slen(min_loc)),' turb=',o(turb(1,min_loc))
    elseif ( show > 10 ) then
      write(7000+lmpi_id,"(1x,2(a,4e12.4))")                &
      ' Turb:J-diag-min/x/y/z=',                            &
      o(min_val),o(x(min_loc)),o(y(min_loc)),o(z(min_loc)), &
      ' Turb:J-diag-max/x/y/z=',                            &
      o(max_val),o(x(max_loc)),o(y(max_loc)),o(z(max_loc))
    endif

  end subroutine check_diagonal_sign

end module scalar_diffusion

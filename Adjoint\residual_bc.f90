module residual_bc

  implicit none

  private

  public :: atlam_bc
  public :: enforce_lam_strong_bc, enforce_turb_strong_bc

contains

!================================ atlam_bc ===================================80
!
! This does A-transpose times lambda at time-level n
! for part of the residual (dI/dQ is the other part)
!
! Puts the result in res
!
! Expects qnode in PRIMITIVE
! Returns qnode in CONSERVED
!
!=============================================================================80
  subroutine atlam_bc(nnodes0,nnodes01,x,y,z,qnode,nbound,bc,nelem,elem,adim,  &
                      n_tot,dxdt,dydt,dzdt,ad,nfunctions,res,rlam,coltag,aa,ia,&
                      ja,iau,eqn_set)

    use kinddefs,            only : dp
    use grid_motion_helpers, only : need_grid_velocity
    use fluid,               only : gamma, gm1
    use thermo,              only : ptoe
    use ivals,               only : u0, v0, w0, c0, s0
    use bc_types,            only : bcgrid_type
    use bc_names,            only : pback, farfield_pbck,                      &
                                    bc_has_pressure_closure,                   &
                                    farfield_riem, farfield_extr
    use grid_metrics,        only : dual_area_quad, dual_area_tria
    use element_types,       only : elem_type
    use adjoint_switches,    only : rn, np
    use design_types,        only : max_functions
    use lmpi,                only : lmpi_master, lmpi_die
    use nml_noninertial_reference_frame, only : noninertial

    integer, intent(in) :: nnodes0
    integer, intent(in) :: adim, n_tot
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nbound
    integer, intent(in) :: nelem
    integer, intent(in), optional :: nfunctions
    integer, intent(in), optional :: eqn_set

    integer, dimension(:), intent(in), optional :: ia, ja, iau

    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),        intent(in)    :: dxdt
    real(dp), dimension(nnodes01),        intent(in)    :: dydt
    real(dp), dimension(nnodes01),        intent(in)    :: dzdt
    type(bcgrid_type), dimension(nbound), intent(in)    :: bc
    type(elem_type),   dimension(nelem),  intent(in)    :: elem
    real(dp), dimension(n_tot,nnodes01),  intent(inout) :: qnode
    real(dp), dimension(adim,nnodes01),   intent(in)   , optional :: coltag
    real(dp), dimension(:,:,:),           intent(in)   , optional :: rlam
    real(dp), dimension(:,:,:),           intent(inout), optional :: res
    real(dp), dimension(5,5),             intent(inout), optional :: ad
    real(dp), dimension(:,:,:),           intent(inout), optional :: aa

    integer :: ib, n, i, j, ielem, inode, idiag4, idiag
    integer :: node1, node2, node3, node4, ioff32
    integer :: idiag1, idiag2, ioff12, ioff21, jstart, jend, neighbor
    integer :: idiag3, ioff13, ioff23, ioff31

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q, face_speed_q
    real(dp), dimension(3) :: xnorm_t, ynorm_t, znorm_t, face_speed_t

    real(dp) :: face_speed

    real(dp) :: x1, y1, z1
    real(dp) :: x2, y2, z2
    real(dp) :: x3, y3, z3
    real(dp) :: p1q1, p1q2, p1q3, p1q4, p1q5
    real(dp) :: p2q1, p2q2, p2q3, p2q4, p2q5
    real(dp) :: p3q1, p3q2, p3q3, p3q4, p3q5
    real(dp) :: p4q1, p4q2, p4q3, p4q4, p4q5
    real(dp) :: ax, ay, az, bx, by, bz
    real(dp) :: xnorm, ynorm, znorm
    real(dp) :: paq11, paq12, paq13, paq14, paq15
    real(dp) :: paq21, paq22, paq23, paq24, paq25
    real(dp) :: paq31, paq32, paq33, paq34, paq35

    real(dp) :: pbq11, pbq12, pbq13, pbq14, pbq15
    real(dp) :: pbq21, pbq22, pbq23, pbq24, pbq25
    real(dp) :: pbq31, pbq32, pbq33, pbq34, pbq35
    real(dp) :: pcq11, pcq12, pcq13, pcq14, pcq15
    real(dp) :: pcq21, pcq22, pcq23, pcq24, pcq25
    real(dp) :: pcq31, pcq32, pcq33, pcq34, pcq35

    real(dp) :: p1q1C, p1q2C, p1q3C, p1q4C, p1q5C
    real(dp) :: p2q1C, p2q2C, p2q3C, p2q4C, p2q5C
    real(dp) :: p3q1C, p3q2C, p3q3C, p3q4C, p3q5C
    real(dp) :: p4q1C, p4q2C, p4q3C, p4q4C, p4q5C

    real(dp) :: q1Q1, q2Q1, q3Q1, q4Q1, q5Q1
    real(dp) :: q1Q2, q2Q2, q3Q2, q4Q2, q5Q2
    real(dp) :: q1Q3, q2Q3, q3Q3, q4Q3, q5Q3
    real(dp) :: q1Q4, q2Q4, q3Q4, q4Q4, q5Q4
    real(dp) :: q1Q5, q2Q5, q3Q5, q4Q5, q5Q5

    real(dp) :: paq11C, paq12C, paq13C, paq14C, paq15C
    real(dp) :: pbq11C, pbq12C, pbq13C, pbq14C, pbq15C
    real(dp) :: pcq11C, pcq12C, pcq13C, pcq14C, pcq15C

    real(dp) :: paq21C, paq22C, paq23C, paq24C, paq25C
    real(dp) :: pbq21C, pbq22C, pbq23C, pbq24C, pbq25C
    real(dp) :: pcq21C, pcq22C, pcq23C, pcq24C, pcq25C

    real(dp) :: paq31C, paq32C, paq33C, paq34C, paq35C
    real(dp) :: pbq31C, pbq32C, pbq33C, pbq34C, pbq35C
    real(dp) :: pcq31C, pcq32C, pcq33C, pcq34C, pcq35C

    real(dp),dimension(max_functions)   ::        rlam12, rlam13, rlam14, rlam15
    real(dp),dimension(max_functions)   ::        rlam22, rlam23, rlam24, rlam25
    real(dp),dimension(max_functions)   ::        rlam32, rlam33, rlam34, rlam35
    real(dp),dimension(max_functions)   ::rlam42, rlam43, rlam44, rlam45

    real(dp) :: xgm1
    real(dp) :: area
    real(dp) :: uout, vout, wout, cout, sout
    real(dp) :: rhoi,rhoiq1,rhoiq2,rhoiq3,rhoiq4,rhoiq5
    real(dp) :: ui,uiq1,uiq2,uiq3,uiq4,uiq5
    real(dp) :: vi,viq1,viq2,viq3,viq4,viq5
    real(dp) :: wi,wiq1,wiq2,wiq3,wiq4,wiq5
    real(dp) :: ei,eiq1,eiq2,eiq3,eiq4,eiq5
    real(dp) :: pi,piq1,piq2,piq3,piq4,piq5
    real(dp) :: a2,a2q1,a2q2,a2q3,a2q4,a2q5
    real(dp) :: a,aq1,aq2,aq3,aq4,aq5
    real(dp) :: ai,aiq1,aiq2,aiq3,aiq4,aiq5
    real(dp) :: rplus,rplusq1,rplusq2,rplusq3,rplusq4,rplusq5
    real(dp) :: rminus,rminusq1,rminusq2,rminusq3,rminusq4,rminusq5
    real(dp) :: unormi,unormiq1,unormiq2,unormiq3,unormiq4,unormiq5
    real(dp) :: unorm,unormq1,unormq2,unormq3,unormq4,unormq5
    real(dp) :: s,sq1,sq2,sq3,sq4,sq5
    real(dp) :: unormo

    real(dp) :: c68, c18

    real(dp) :: rho,rhoq1,rhoq2,rhoq3,rhoq4,rhoq5
    real(dp) :: u,uq1,uq2,uq3,uq4,uq5
    real(dp) :: v,vq1,vq2,vq3,vq4,vq5
    real(dp) :: w,wq1,wq2,wq3,wq4,wq5
    real(dp) :: p,pq1,pq2,pq3,pq4,pq5
    real(dp) :: e,eq1,eq2,eq3,eq4,eq5
    real(dp) :: ubar,ubarq1,ubarq2,ubarq3,ubarq4,ubarq5
    real(dp) :: res1q1,res1q2,res1q3,res1q4,res1q5
    real(dp) :: res2q1,res2q2,res2q3,res2q4,res2q5
    real(dp) :: res3q1,res3q2,res3q3,res3q4,res3q5
    real(dp) :: res4q1,res4q2,res4q3,res4q4,res4q5
    real(dp) :: res5q1,res5q2,res5q3,res5q4,res5q5

    real(dp), dimension(max_functions) :: rlam1, rlam2, rlam3, rlam4, rlam5

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_4th = 0.25_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
    real(dp), parameter :: my_8   = 8.0_dp

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) .or.                   &
           .not.present(ad)     .or. .not.present(nfunctions) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_bc, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(aa) ) then
      if ( .not.present(iau) .or. .not.present(ia) .or. .not.present(ja) ) then
        if ( lmpi_master ) then
          write(*,*) 'a requested in atlam_bc, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    close_pressure : do ib = 1, nbound

      conditional_01: if ( bc_has_pressure_closure(bc(ib)%ibc) ) then

        loop_tris_01: do n = 1,bc(ib)%nbfacet

          ielem = bc(ib)%f2ntb(n,5)  ! index to type of element
                                     ! attached to this face

          c68 = my_1
          c18 = my_0

          if (elem(ielem)%type_cell == 'tet') then
!           for linear function preservation during flux closure - tets only
            c68 = my_6/my_8
            c18 = my_1/my_8
          end if

          node1 = bc(ib)%ibnode(bc(ib)%f2ntb(n,1))
          node2 = bc(ib)%ibnode(bc(ib)%f2ntb(n,2))
          node3 = bc(ib)%ibnode(bc(ib)%f2ntb(n,3))

!          p1 = qnode(5,node1)
          p1q1 = my_0
          p1q2 = my_0
          p1q3 = my_0
          p1q4 = my_0
          p1q5 = my_1

!          p2 = qnode(5,node2)
          p2q1 = my_0
          p2q2 = my_0
          p2q3 = my_0
          p2q4 = my_0
          p2q5 = my_1

!          p3 = qnode(5,node3)
          p3q1 = my_0
          p3q2 = my_0
          p3q3 = my_0
          p3q4 = my_0
          p3q5 = my_1

!         norm points away from grid interior
!         norm magnitude is 1/3 area of surface triangle
!         and is the same at all 3 nodes

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)

          ax = x2 - x1
          ay = y2 - y1
          az = z2 - z1

          bx = x3 - x1
          by = y3 - y1
          bz = z3 - z1

          xnorm = -my_haf*(ay*bz-az*by)/my_3
          ynorm =  my_haf*(ax*bz-az*bx)/my_3
          znorm = -my_haf*(ax*by-ay*bx)/my_3

!       for moving grid cases get face speed contributions

        face_speed_t(:) = my_0

        if (need_grid_velocity) then
          call dual_area_tria(nnodes01,x,y,z,node1,node2,node3,noninertial,    &
                              xnorm_t,ynorm_t,znorm_t,dxdt,dydt,dzdt,          &
                              face_speed_t)
        end if

!         pa = c68*p1 + c18*(p2+p3)
          paq11 = c68*p1q1
          paq12 = c68*p1q2
          paq13 = c68*p1q3
          paq14 = c68*p1q4
          paq15 = c68*p1q5

          paq21 = c18*p2q1
          paq22 = c18*p2q2
          paq23 = c18*p2q3
          paq24 = c18*p2q4
          paq25 = c18*p2q5

          paq31 = c18*p3q1
          paq32 = c18*p3q2
          paq33 = c18*p3q3
          paq34 = c18*p3q4
          paq35 = c18*p3q5

!         pb = c68*p2 + c18*(p1+p3)
          pbq11 = c18*p1q1
          pbq12 = c18*p1q2
          pbq13 = c18*p1q3
          pbq14 = c18*p1q4
          pbq15 = c18*p1q5

          pbq21 = c68*p2q1
          pbq22 = c68*p2q2
          pbq23 = c68*p2q3
          pbq24 = c68*p2q4
          pbq25 = c68*p2q5

          pbq31 = c18*p3q1
          pbq32 = c18*p3q2
          pbq33 = c18*p3q3
          pbq34 = c18*p3q4
          pbq35 = c18*p3q5

!         pc = c68*p3 + c18*(p1+p2)
          pcq11 = c18*p1q1
          pcq12 = c18*p1q2
          pcq13 = c18*p1q3
          pcq14 = c18*p1q4
          pcq15 = c18*p1q5

          pcq21 = c18*p2q1
          pcq22 = c18*p2q2
          pcq23 = c18*p2q3
          pcq24 = c18*p2q4
          pcq25 = c18*p2q5

          pcq31 = c68*p3q1
          pcq32 = c68*p3q2
          pcq33 = c68*p3q3
          pcq34 = c68*p3q4
          pcq35 = c68*p3q5

! Now convert derivatives wrt primitive to be wrt conservative

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node1) / qnode(1,node1)
          q2Q2 = my_1 / qnode(1,node1)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node1) / qnode(1,node1)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node1)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node1) / qnode(1,node1)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node1)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node1)*qnode(2,node1)                   &
            + qnode(3,node1)*qnode(3,node1)                                    &
            + qnode(4,node1)*qnode(4,node1))
          q5Q2 = - gm1 * qnode(2,node1)
          q5Q3 = - gm1 * qnode(3,node1)
          q5Q4 = - gm1 * qnode(4,node1)
          q5Q5 = gm1

          paq11C = paq11*q1Q1+paq12*q2Q1+paq13*q3Q1+paq14*q4Q1+paq15*q5Q1
          paq12C = paq11*q1Q2+paq12*q2Q2+paq13*q3Q2+paq14*q4Q2+paq15*q5Q2
          paq13C = paq11*q1Q3+paq12*q2Q3+paq13*q3Q3+paq14*q4Q3+paq15*q5Q3
          paq14C = paq11*q1Q4+paq12*q2Q4+paq13*q3Q4+paq14*q4Q4+paq15*q5Q4
          paq15C = paq11*q1Q5+paq12*q2Q5+paq13*q3Q5+paq14*q4Q5+paq15*q5Q5

          pbq11C = pbq11*q1Q1+pbq12*q2Q1+pbq13*q3Q1+pbq14*q4Q1+pbq15*q5Q1
          pbq12C = pbq11*q1Q2+pbq12*q2Q2+pbq13*q3Q2+pbq14*q4Q2+pbq15*q5Q2
          pbq13C = pbq11*q1Q3+pbq12*q2Q3+pbq13*q3Q3+pbq14*q4Q3+pbq15*q5Q3
          pbq14C = pbq11*q1Q4+pbq12*q2Q4+pbq13*q3Q4+pbq14*q4Q4+pbq15*q5Q4
          pbq15C = pbq11*q1Q5+pbq12*q2Q5+pbq13*q3Q5+pbq14*q4Q5+pbq15*q5Q5

          pcq11C = pcq11*q1Q1+pcq12*q2Q1+pcq13*q3Q1+pcq14*q4Q1+pcq15*q5Q1
          pcq12C = pcq11*q1Q2+pcq12*q2Q2+pcq13*q3Q2+pcq14*q4Q2+pcq15*q5Q2
          pcq13C = pcq11*q1Q3+pcq12*q2Q3+pcq13*q3Q3+pcq14*q4Q3+pcq15*q5Q3
          pcq14C = pcq11*q1Q4+pcq12*q2Q4+pcq13*q3Q4+pcq14*q4Q4+pcq15*q5Q4
          pcq15C = pcq11*q1Q5+pcq12*q2Q5+pcq13*q3Q5+pcq14*q4Q5+pcq15*q5Q5

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node2) / qnode(1,node2)
          q2Q2 = my_1 / qnode(1,node2)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node2) / qnode(1,node2)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node2)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node2) / qnode(1,node2)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node2)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node2)*qnode(2,node2)                   &
            + qnode(3,node2)*qnode(3,node2)                                    &
            + qnode(4,node2)*qnode(4,node2))
          q5Q2 = - gm1 * qnode(2,node2)
          q5Q3 = - gm1 * qnode(3,node2)
          q5Q4 = - gm1 * qnode(4,node2)
          q5Q5 = gm1

          paq21C = paq21*q1Q1+paq22*q2Q1+paq23*q3Q1+paq24*q4Q1+paq25*q5Q1
          paq22C = paq21*q1Q2+paq22*q2Q2+paq23*q3Q2+paq24*q4Q2+paq25*q5Q2
          paq23C = paq21*q1Q3+paq22*q2Q3+paq23*q3Q3+paq24*q4Q3+paq25*q5Q3
          paq24C = paq21*q1Q4+paq22*q2Q4+paq23*q3Q4+paq24*q4Q4+paq25*q5Q4
          paq25C = paq21*q1Q5+paq22*q2Q5+paq23*q3Q5+paq24*q4Q5+paq25*q5Q5

          pbq21C = pbq21*q1Q1+pbq22*q2Q1+pbq23*q3Q1+pbq24*q4Q1+pbq25*q5Q1
          pbq22C = pbq21*q1Q2+pbq22*q2Q2+pbq23*q3Q2+pbq24*q4Q2+pbq25*q5Q2
          pbq23C = pbq21*q1Q3+pbq22*q2Q3+pbq23*q3Q3+pbq24*q4Q3+pbq25*q5Q3
          pbq24C = pbq21*q1Q4+pbq22*q2Q4+pbq23*q3Q4+pbq24*q4Q4+pbq25*q5Q4
          pbq25C = pbq21*q1Q5+pbq22*q2Q5+pbq23*q3Q5+pbq24*q4Q5+pbq25*q5Q5

          pcq21C = pcq21*q1Q1+pcq22*q2Q1+pcq23*q3Q1+pcq24*q4Q1+pcq25*q5Q1
          pcq22C = pcq21*q1Q2+pcq22*q2Q2+pcq23*q3Q2+pcq24*q4Q2+pcq25*q5Q2
          pcq23C = pcq21*q1Q3+pcq22*q2Q3+pcq23*q3Q3+pcq24*q4Q3+pcq25*q5Q3
          pcq24C = pcq21*q1Q4+pcq22*q2Q4+pcq23*q3Q4+pcq24*q4Q4+pcq25*q5Q4
          pcq25C = pcq21*q1Q5+pcq22*q2Q5+pcq23*q3Q5+pcq24*q4Q5+pcq25*q5Q5

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node3) / qnode(1,node3)
          q2Q2 = my_1 / qnode(1,node3)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node3) / qnode(1,node3)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node3)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node3) / qnode(1,node3)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node3)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node3)*qnode(2,node3)                   &
            + qnode(3,node3)*qnode(3,node3)                                    &
            + qnode(4,node3)*qnode(4,node3))
          q5Q2 = - gm1 * qnode(2,node3)
          q5Q3 = - gm1 * qnode(3,node3)
          q5Q4 = - gm1 * qnode(4,node3)
          q5Q5 = gm1

          paq31C = paq31*q1Q1+paq32*q2Q1+paq33*q3Q1+paq34*q4Q1+paq35*q5Q1
          paq32C = paq31*q1Q2+paq32*q2Q2+paq33*q3Q2+paq34*q4Q2+paq35*q5Q2
          paq33C = paq31*q1Q3+paq32*q2Q3+paq33*q3Q3+paq34*q4Q3+paq35*q5Q3
          paq34C = paq31*q1Q4+paq32*q2Q4+paq33*q3Q4+paq34*q4Q4+paq35*q5Q4
          paq35C = paq31*q1Q5+paq32*q2Q5+paq33*q3Q5+paq34*q4Q5+paq35*q5Q5

          pbq31C = pbq31*q1Q1+pbq32*q2Q1+pbq33*q3Q1+pbq34*q4Q1+pbq35*q5Q1
          pbq32C = pbq31*q1Q2+pbq32*q2Q2+pbq33*q3Q2+pbq34*q4Q2+pbq35*q5Q2
          pbq33C = pbq31*q1Q3+pbq32*q2Q3+pbq33*q3Q3+pbq34*q4Q3+pbq35*q5Q3
          pbq34C = pbq31*q1Q4+pbq32*q2Q4+pbq33*q3Q4+pbq34*q4Q4+pbq35*q5Q4
          pbq35C = pbq31*q1Q5+pbq32*q2Q5+pbq33*q3Q5+pbq34*q4Q5+pbq35*q5Q5

          pcq31C = pcq31*q1Q1+pcq32*q2Q1+pcq33*q3Q1+pcq34*q4Q1+pcq35*q5Q1
          pcq32C = pcq31*q1Q2+pcq32*q2Q2+pcq33*q3Q2+pcq34*q4Q2+pcq35*q5Q2
          pcq33C = pcq31*q1Q3+pcq32*q2Q3+pcq33*q3Q3+pcq34*q4Q3+pcq35*q5Q3
          pcq34C = pcq31*q1Q4+pcq32*q2Q4+pcq33*q3Q4+pcq34*q4Q4+pcq35*q5Q4
          pcq35C = pcq31*q1Q5+pcq32*q2Q5+pcq33*q3Q5+pcq34*q4Q5+pcq35*q5Q5

        res_contribs1 : if ( fill_res ) then
          do i = 1, nfunctions
            rlam12(i) = coltag(2,node1)*rlam(2,node1,i)
            rlam13(i) = coltag(3,node1)*rlam(3,node1,i)
            rlam14(i) = coltag(4,node1)*rlam(4,node1,i)
            rlam15(i) = coltag(5,node1)*rlam(5,node1,i)

            rlam22(i) = coltag(2,node2)*rlam(2,node2,i)
            rlam23(i) = coltag(3,node2)*rlam(3,node2,i)
            rlam24(i) = coltag(4,node2)*rlam(4,node2,i)
            rlam25(i) = coltag(5,node2)*rlam(5,node2,i)

            rlam32(i) = coltag(2,node3)*rlam(2,node3,i)
            rlam33(i) = coltag(3,node3)*rlam(3,node3,i)
            rlam34(i) = coltag(4,node3)*rlam(4,node3,i)
            rlam35(i) = coltag(5,node3)*rlam(5,node3,i)
          end do

          if ( rn == node1 .and. np == node1 ) then
            ad(2,1) = ad(2,1) + coltag(2,node1)*xnorm*paq11C
            ad(2,2) = ad(2,2) + coltag(2,node1)*xnorm*paq12C
            ad(2,3) = ad(2,3) + coltag(2,node1)*xnorm*paq13C
            ad(2,4) = ad(2,4) + coltag(2,node1)*xnorm*paq14C
            ad(2,5) = ad(2,5) + coltag(2,node1)*xnorm*paq15C

            ad(3,1) = ad(3,1) + coltag(3,node1)*ynorm*paq11C
            ad(3,2) = ad(3,2) + coltag(3,node1)*ynorm*paq12C
            ad(3,3) = ad(3,3) + coltag(3,node1)*ynorm*paq13C
            ad(3,4) = ad(3,4) + coltag(3,node1)*ynorm*paq14C
            ad(3,5) = ad(3,5) + coltag(3,node1)*ynorm*paq15C

            ad(4,1) = ad(4,1) + coltag(4,node1)*znorm*paq11C
            ad(4,2) = ad(4,2) + coltag(4,node1)*znorm*paq12C
            ad(4,3) = ad(4,3) + coltag(4,node1)*znorm*paq13C
            ad(4,4) = ad(4,4) + coltag(4,node1)*znorm*paq14C
            ad(4,5) = ad(4,5) + coltag(4,node1)*znorm*paq15C

            ad(5,1) = ad(5,1) + coltag(5,node1)*face_speed_t(1)*paq11C
            ad(5,2) = ad(5,2) + coltag(5,node1)*face_speed_t(1)*paq12C
            ad(5,3) = ad(5,3) + coltag(5,node1)*face_speed_t(1)*paq13C
            ad(5,4) = ad(5,4) + coltag(5,node1)*face_speed_t(1)*paq14C
            ad(5,5) = ad(5,5) + coltag(5,node1)*face_speed_t(1)*paq15C
          endif

          if ( rn == node1 .and. np == node2 ) then
            ad(2,1) = ad(2,1) + coltag(2,node1)*xnorm*paq21C
            ad(2,2) = ad(2,2) + coltag(2,node1)*xnorm*paq22C
            ad(2,3) = ad(2,3) + coltag(2,node1)*xnorm*paq23C
            ad(2,4) = ad(2,4) + coltag(2,node1)*xnorm*paq24C
            ad(2,5) = ad(2,5) + coltag(2,node1)*xnorm*paq25C

            ad(3,1) = ad(3,1) + coltag(3,node1)*ynorm*paq21C
            ad(3,2) = ad(3,2) + coltag(3,node1)*ynorm*paq22C
            ad(3,3) = ad(3,3) + coltag(3,node1)*ynorm*paq23C
            ad(3,4) = ad(3,4) + coltag(3,node1)*ynorm*paq24C
            ad(3,5) = ad(3,5) + coltag(3,node1)*ynorm*paq25C

            ad(4,1) = ad(4,1) + coltag(4,node1)*znorm*paq21C
            ad(4,2) = ad(4,2) + coltag(4,node1)*znorm*paq22C
            ad(4,3) = ad(4,3) + coltag(4,node1)*znorm*paq23C
            ad(4,4) = ad(4,4) + coltag(4,node1)*znorm*paq24C
            ad(4,5) = ad(4,5) + coltag(4,node1)*znorm*paq25C

            ad(5,1) = ad(5,1) + coltag(5,node1)*face_speed_t(1)*paq21C
            ad(5,2) = ad(5,2) + coltag(5,node1)*face_speed_t(1)*paq22C
            ad(5,3) = ad(5,3) + coltag(5,node1)*face_speed_t(1)*paq23C
            ad(5,4) = ad(5,4) + coltag(5,node1)*face_speed_t(1)*paq24C
            ad(5,5) = ad(5,5) + coltag(5,node1)*face_speed_t(1)*paq25C
          endif

          if ( rn == node1 .and. np == node3 ) then
            ad(2,1) = ad(2,1) + coltag(2,node1)*xnorm*paq31C
            ad(2,2) = ad(2,2) + coltag(2,node1)*xnorm*paq32C
            ad(2,3) = ad(2,3) + coltag(2,node1)*xnorm*paq33C
            ad(2,4) = ad(2,4) + coltag(2,node1)*xnorm*paq34C
            ad(2,5) = ad(2,5) + coltag(2,node1)*xnorm*paq35C

            ad(3,1) = ad(3,1) + coltag(3,node1)*ynorm*paq31C
            ad(3,2) = ad(3,2) + coltag(3,node1)*ynorm*paq32C
            ad(3,3) = ad(3,3) + coltag(3,node1)*ynorm*paq33C
            ad(3,4) = ad(3,4) + coltag(3,node1)*ynorm*paq34C
            ad(3,5) = ad(3,5) + coltag(3,node1)*ynorm*paq35C

            ad(4,1) = ad(4,1) + coltag(4,node1)*znorm*paq31C
            ad(4,2) = ad(4,2) + coltag(4,node1)*znorm*paq32C
            ad(4,3) = ad(4,3) + coltag(4,node1)*znorm*paq33C
            ad(4,4) = ad(4,4) + coltag(4,node1)*znorm*paq34C
            ad(4,5) = ad(4,5) + coltag(4,node1)*znorm*paq35C

            ad(5,1) = ad(5,1) + coltag(5,node1)*face_speed_t(1)*paq31C
            ad(5,2) = ad(5,2) + coltag(5,node1)*face_speed_t(1)*paq32C
            ad(5,3) = ad(5,3) + coltag(5,node1)*face_speed_t(1)*paq33C
            ad(5,4) = ad(5,4) + coltag(5,node1)*face_speed_t(1)*paq34C
            ad(5,5) = ad(5,5) + coltag(5,node1)*face_speed_t(1)*paq35C
          endif

          if ( rn == node2 .and. np == node1 ) then
            ad(2,1) = ad(2,1) + coltag(2,node2)*xnorm*pbq11C
            ad(2,2) = ad(2,2) + coltag(2,node2)*xnorm*pbq12C
            ad(2,3) = ad(2,3) + coltag(2,node2)*xnorm*pbq13C
            ad(2,4) = ad(2,4) + coltag(2,node2)*xnorm*pbq14C
            ad(2,5) = ad(2,5) + coltag(2,node2)*xnorm*pbq15C

            ad(3,1) = ad(3,1) + coltag(3,node2)*ynorm*pbq11C
            ad(3,2) = ad(3,2) + coltag(3,node2)*ynorm*pbq12C
            ad(3,3) = ad(3,3) + coltag(3,node2)*ynorm*pbq13C
            ad(3,4) = ad(3,4) + coltag(3,node2)*ynorm*pbq14C
            ad(3,5) = ad(3,5) + coltag(3,node2)*ynorm*pbq15C

            ad(4,1) = ad(4,1) + coltag(4,node2)*znorm*pbq11C
            ad(4,2) = ad(4,2) + coltag(4,node2)*znorm*pbq12C
            ad(4,3) = ad(4,3) + coltag(4,node2)*znorm*pbq13C
            ad(4,4) = ad(4,4) + coltag(4,node2)*znorm*pbq14C
            ad(4,5) = ad(4,5) + coltag(4,node2)*znorm*pbq15C

            ad(5,1) = ad(5,1) + coltag(5,node2)*face_speed_t(2)*pbq11C
            ad(5,2) = ad(5,2) + coltag(5,node2)*face_speed_t(2)*pbq12C
            ad(5,3) = ad(5,3) + coltag(5,node2)*face_speed_t(2)*pbq13C
            ad(5,4) = ad(5,4) + coltag(5,node2)*face_speed_t(2)*pbq14C
            ad(5,5) = ad(5,5) + coltag(5,node2)*face_speed_t(2)*pbq15C
          endif

          if ( rn == node2 .and. np == node2 ) then
            ad(2,1) = ad(2,1) + coltag(2,node2)*xnorm*pbq21C
            ad(2,2) = ad(2,2) + coltag(2,node2)*xnorm*pbq22C
            ad(2,3) = ad(2,3) + coltag(2,node2)*xnorm*pbq23C
            ad(2,4) = ad(2,4) + coltag(2,node2)*xnorm*pbq24C
            ad(2,5) = ad(2,5) + coltag(2,node2)*xnorm*pbq25C

            ad(3,1) = ad(3,1) + coltag(3,node2)*ynorm*pbq21C
            ad(3,2) = ad(3,2) + coltag(3,node2)*ynorm*pbq22C
            ad(3,3) = ad(3,3) + coltag(3,node2)*ynorm*pbq23C
            ad(3,4) = ad(3,4) + coltag(3,node2)*ynorm*pbq24C
            ad(3,5) = ad(3,5) + coltag(3,node2)*ynorm*pbq25C

            ad(4,1) = ad(4,1) + coltag(4,node2)*znorm*pbq21C
            ad(4,2) = ad(4,2) + coltag(4,node2)*znorm*pbq22C
            ad(4,3) = ad(4,3) + coltag(4,node2)*znorm*pbq23C
            ad(4,4) = ad(4,4) + coltag(4,node2)*znorm*pbq24C
            ad(4,5) = ad(4,5) + coltag(4,node2)*znorm*pbq25C

            ad(5,1) = ad(5,1) + coltag(5,node2)*face_speed_t(2)*pbq21C
            ad(5,2) = ad(5,2) + coltag(5,node2)*face_speed_t(2)*pbq22C
            ad(5,3) = ad(5,3) + coltag(5,node2)*face_speed_t(2)*pbq23C
            ad(5,4) = ad(5,4) + coltag(5,node2)*face_speed_t(2)*pbq24C
            ad(5,5) = ad(5,5) + coltag(5,node2)*face_speed_t(2)*pbq25C
          endif

          if ( rn == node2 .and. np == node3 ) then
            ad(2,1) = ad(2,1) + coltag(2,node2)*xnorm*pbq31C
            ad(2,2) = ad(2,2) + coltag(2,node2)*xnorm*pbq32C
            ad(2,3) = ad(2,3) + coltag(2,node2)*xnorm*pbq33C
            ad(2,4) = ad(2,4) + coltag(2,node2)*xnorm*pbq34C
            ad(2,5) = ad(2,5) + coltag(2,node2)*xnorm*pbq35C

            ad(3,1) = ad(3,1) + coltag(3,node2)*ynorm*pbq31C
            ad(3,2) = ad(3,2) + coltag(3,node2)*ynorm*pbq32C
            ad(3,3) = ad(3,3) + coltag(3,node2)*ynorm*pbq33C
            ad(3,4) = ad(3,4) + coltag(3,node2)*ynorm*pbq34C
            ad(3,5) = ad(3,5) + coltag(3,node2)*ynorm*pbq35C

            ad(4,1) = ad(4,1) + coltag(4,node2)*znorm*pbq31C
            ad(4,2) = ad(4,2) + coltag(4,node2)*znorm*pbq32C
            ad(4,3) = ad(4,3) + coltag(4,node2)*znorm*pbq33C
            ad(4,4) = ad(4,4) + coltag(4,node2)*znorm*pbq34C
            ad(4,5) = ad(4,5) + coltag(4,node2)*znorm*pbq35C

            ad(5,1) = ad(5,1) + coltag(5,node2)*face_speed_t(2)*pbq31C
            ad(5,2) = ad(5,2) + coltag(5,node2)*face_speed_t(2)*pbq32C
            ad(5,3) = ad(5,3) + coltag(5,node2)*face_speed_t(2)*pbq33C
            ad(5,4) = ad(5,4) + coltag(5,node2)*face_speed_t(2)*pbq34C
            ad(5,5) = ad(5,5) + coltag(5,node2)*face_speed_t(2)*pbq35C
          endif

          if ( rn == node3 .and. np == node1 ) then
            ad(2,1) = ad(2,1) + coltag(2,node3)*xnorm*pcq11C
            ad(2,2) = ad(2,2) + coltag(2,node3)*xnorm*pcq12C
            ad(2,3) = ad(2,3) + coltag(2,node3)*xnorm*pcq13C
            ad(2,4) = ad(2,4) + coltag(2,node3)*xnorm*pcq14C
            ad(2,5) = ad(2,5) + coltag(2,node3)*xnorm*pcq15C

            ad(3,1) = ad(3,1) + coltag(3,node3)*ynorm*pcq11C
            ad(3,2) = ad(3,2) + coltag(3,node3)*ynorm*pcq12C
            ad(3,3) = ad(3,3) + coltag(3,node3)*ynorm*pcq13C
            ad(3,4) = ad(3,4) + coltag(3,node3)*ynorm*pcq14C
            ad(3,5) = ad(3,5) + coltag(3,node3)*ynorm*pcq15C

            ad(4,1) = ad(4,1) + coltag(4,node3)*znorm*pcq11C
            ad(4,2) = ad(4,2) + coltag(4,node3)*znorm*pcq12C
            ad(4,3) = ad(4,3) + coltag(4,node3)*znorm*pcq13C
            ad(4,4) = ad(4,4) + coltag(4,node3)*znorm*pcq14C
            ad(4,5) = ad(4,5) + coltag(4,node3)*znorm*pcq15C

            ad(5,1) = ad(5,1) + coltag(5,node3)*face_speed_t(3)*pcq11C
            ad(5,2) = ad(5,2) + coltag(5,node3)*face_speed_t(3)*pcq12C
            ad(5,3) = ad(5,3) + coltag(5,node3)*face_speed_t(3)*pcq13C
            ad(5,4) = ad(5,4) + coltag(5,node3)*face_speed_t(3)*pcq14C
            ad(5,5) = ad(5,5) + coltag(5,node3)*face_speed_t(3)*pcq15C
          endif

          if ( rn == node3 .and. np == node2 ) then
            ad(2,1) = ad(2,1) + coltag(2,node3)*xnorm*pcq21C
            ad(2,2) = ad(2,2) + coltag(2,node3)*xnorm*pcq22C
            ad(2,3) = ad(2,3) + coltag(2,node3)*xnorm*pcq23C
            ad(2,4) = ad(2,4) + coltag(2,node3)*xnorm*pcq24C
            ad(2,5) = ad(2,5) + coltag(2,node3)*xnorm*pcq25C

            ad(3,1) = ad(3,1) + coltag(3,node3)*ynorm*pcq21C
            ad(3,2) = ad(3,2) + coltag(3,node3)*ynorm*pcq22C
            ad(3,3) = ad(3,3) + coltag(3,node3)*ynorm*pcq23C
            ad(3,4) = ad(3,4) + coltag(3,node3)*ynorm*pcq24C
            ad(3,5) = ad(3,5) + coltag(3,node3)*ynorm*pcq25C

            ad(4,1) = ad(4,1) + coltag(4,node3)*znorm*pcq21C
            ad(4,2) = ad(4,2) + coltag(4,node3)*znorm*pcq22C
            ad(4,3) = ad(4,3) + coltag(4,node3)*znorm*pcq23C
            ad(4,4) = ad(4,4) + coltag(4,node3)*znorm*pcq24C
            ad(4,5) = ad(4,5) + coltag(4,node3)*znorm*pcq25C

            ad(5,1) = ad(5,1) + coltag(5,node3)*face_speed_t(3)*pcq21C
            ad(5,2) = ad(5,2) + coltag(5,node3)*face_speed_t(3)*pcq22C
            ad(5,3) = ad(5,3) + coltag(5,node3)*face_speed_t(3)*pcq23C
            ad(5,4) = ad(5,4) + coltag(5,node3)*face_speed_t(3)*pcq24C
            ad(5,5) = ad(5,5) + coltag(5,node3)*face_speed_t(3)*pcq25C
          endif

          if ( rn == node3 .and. np == node3 ) then
            ad(2,1) = ad(2,1) + coltag(2,node3)*xnorm*pcq31C
            ad(2,2) = ad(2,2) + coltag(2,node3)*xnorm*pcq32C
            ad(2,3) = ad(2,3) + coltag(2,node3)*xnorm*pcq33C
            ad(2,4) = ad(2,4) + coltag(2,node3)*xnorm*pcq34C
            ad(2,5) = ad(2,5) + coltag(2,node3)*xnorm*pcq35C

            ad(3,1) = ad(3,1) + coltag(3,node3)*ynorm*pcq31C
            ad(3,2) = ad(3,2) + coltag(3,node3)*ynorm*pcq32C
            ad(3,3) = ad(3,3) + coltag(3,node3)*ynorm*pcq33C
            ad(3,4) = ad(3,4) + coltag(3,node3)*ynorm*pcq34C
            ad(3,5) = ad(3,5) + coltag(3,node3)*ynorm*pcq35C

            ad(4,1) = ad(4,1) + coltag(4,node3)*znorm*pcq31C
            ad(4,2) = ad(4,2) + coltag(4,node3)*znorm*pcq32C
            ad(4,3) = ad(4,3) + coltag(4,node3)*znorm*pcq33C
            ad(4,4) = ad(4,4) + coltag(4,node3)*znorm*pcq34C
            ad(4,5) = ad(4,5) + coltag(4,node3)*znorm*pcq35C

            ad(5,1) = ad(5,1) + coltag(5,node3)*face_speed_t(3)*pcq31C
            ad(5,2) = ad(5,2) + coltag(5,node3)*face_speed_t(3)*pcq32C
            ad(5,3) = ad(5,3) + coltag(5,node3)*face_speed_t(3)*pcq33C
            ad(5,4) = ad(5,4) + coltag(5,node3)*face_speed_t(3)*pcq34C
            ad(5,5) = ad(5,5) + coltag(5,node3)*face_speed_t(3)*pcq35C
          endif

          if (node1 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node1,i) = res(1,node1,i)                                  &
                + xnorm*paq11C*rlam12(i)                                       &
                + ynorm*paq11C*rlam13(i)                                       &
                + znorm*paq11C*rlam14(i)                                       &
                + face_speed_t(1)*paq11C*rlam15(i)                             &
                + xnorm*pbq11C*rlam22(i)                                       &
                + ynorm*pbq11C*rlam23(i)                                       &
                + znorm*pbq11C*rlam24(i)                                       &
                + face_speed_t(2)*pbq11C*rlam25(i)                             &
                + xnorm*pcq11C*rlam32(i)                                       &
                + ynorm*pcq11C*rlam33(i)                                       &
                + znorm*pcq11C*rlam34(i)                                       &
                + face_speed_t(3)*pcq11C*rlam35(i)

              res(2,node1,i) = res(2,node1,i)                                  &
                + xnorm*paq12C*rlam12(i)                                       &
                + ynorm*paq12C*rlam13(i)                                       &
                + znorm*paq12C*rlam14(i)                                       &
                + face_speed_t(1)*paq12C*rlam15(i)                             &
                + xnorm*pbq12C*rlam22(i)                                       &
                + ynorm*pbq12C*rlam23(i)                                       &
                + znorm*pbq12C*rlam24(i)                                       &
                + face_speed_t(2)*pbq12C*rlam25(i)                             &
                + xnorm*pcq12C*rlam32(i)                                       &
                + ynorm*pcq12C*rlam33(i)                                       &
                + znorm*pcq12C*rlam34(i)                                       &
                + face_speed_t(3)*pcq12C*rlam35(i)

              res(3,node1,i) = res(3,node1,i)                                  &
                + xnorm*paq13C*rlam12(i)                                       &
                + ynorm*paq13C*rlam13(i)                                       &
                + znorm*paq13C*rlam14(i)                                       &
                + face_speed_t(1)*paq13C*rlam15(i)                             &
                + xnorm*pbq13C*rlam22(i)                                       &
                + ynorm*pbq13C*rlam23(i)                                       &
                + znorm*pbq13C*rlam24(i)                                       &
                + face_speed_t(2)*pbq13C*rlam25(i)                             &
                + xnorm*pcq13C*rlam32(i)                                       &
                + ynorm*pcq13C*rlam33(i)                                       &
                + znorm*pcq13C*rlam34(i)                                       &
                + face_speed_t(3)*pcq13C*rlam35(i)

              res(4,node1,i) = res(4,node1,i)                                  &
                + xnorm*paq14C*rlam12(i)                                       &
                + ynorm*paq14C*rlam13(i)                                       &
                + znorm*paq14C*rlam14(i)                                       &
                + face_speed_t(1)*paq14C*rlam15(i)                             &
                + xnorm*pbq14C*rlam22(i)                                       &
                + ynorm*pbq14C*rlam23(i)                                       &
                + znorm*pbq14C*rlam24(i)                                       &
                + face_speed_t(2)*pbq14C*rlam25(i)                             &
                + xnorm*pcq14C*rlam32(i)                                       &
                + ynorm*pcq14C*rlam33(i)                                       &
                + znorm*pcq14C*rlam34(i)                                       &
                + face_speed_t(3)*pcq14C*rlam35(i)

              res(5,node1,i) = res(5,node1,i)                                  &
                + xnorm*paq15C*rlam12(i)                                       &
                + ynorm*paq15C*rlam13(i)                                       &
                + znorm*paq15C*rlam14(i)                                       &
                + face_speed_t(1)*paq15C*rlam15(i)                             &
                + xnorm*pbq15C*rlam22(i)                                       &
                + ynorm*pbq15C*rlam23(i)                                       &
                + znorm*pbq15C*rlam24(i)                                       &
                + face_speed_t(2)*pbq15C*rlam25(i)                             &
                + xnorm*pcq15C*rlam32(i)                                       &
                + ynorm*pcq15C*rlam33(i)                                       &
                + znorm*pcq15C*rlam34(i)                                       &
                + face_speed_t(3)*pcq15C*rlam35(i)
            end do
          end if

          if (node2 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node2,i) = res(1,node2,i)                                  &
                + xnorm*paq21C*rlam12(i)                                       &
                + ynorm*paq21C*rlam13(i)                                       &
                + znorm*paq21C*rlam14(i)                                       &
                + face_speed_t(1)*paq21C*rlam15(i)                             &
                + xnorm*pbq21C*rlam22(i)                                       &
                + ynorm*pbq21C*rlam23(i)                                       &
                + znorm*pbq21C*rlam24(i)                                       &
                + face_speed_t(2)*pbq21C*rlam25(i)                             &
                + xnorm*pcq21C*rlam32(i)                                       &
                + ynorm*pcq21C*rlam33(i)                                       &
                + znorm*pcq21C*rlam34(i)                                       &
                + face_speed_t(3)*pcq21C*rlam35(i)

              res(2,node2,i) = res(2,node2,i)                                  &
                + xnorm*paq22C*rlam12(i)                                       &
                + ynorm*paq22C*rlam13(i)                                       &
                + znorm*paq22C*rlam14(i)                                       &
                + face_speed_t(1)*paq22C*rlam15(i)                             &
                + xnorm*pbq22C*rlam22(i)                                       &
                + ynorm*pbq22C*rlam23(i)                                       &
                + znorm*pbq22C*rlam24(i)                                       &
                + face_speed_t(2)*pbq22C*rlam25(i)                             &
                + xnorm*pcq22C*rlam32(i)                                       &
                + ynorm*pcq22C*rlam33(i)                                       &
                + znorm*pcq22C*rlam34(i)                                       &
                + face_speed_t(3)*pcq22C*rlam35(i)

              res(3,node2,i) = res(3,node2,i)                                  &
                + xnorm*paq23C*rlam12(i)                                       &
                + ynorm*paq23C*rlam13(i)                                       &
                + znorm*paq23C*rlam14(i)                                       &
                + face_speed_t(1)*paq23C*rlam15(i)                             &
                + xnorm*pbq23C*rlam22(i)                                       &
                + ynorm*pbq23C*rlam23(i)                                       &
                + znorm*pbq23C*rlam24(i)                                       &
                + face_speed_t(2)*pbq23C*rlam25(i)                             &
                + xnorm*pcq23C*rlam32(i)                                       &
                + ynorm*pcq23C*rlam33(i)                                       &
                + znorm*pcq23C*rlam34(i)                                       &
                + face_speed_t(3)*pcq23C*rlam35(i)

              res(4,node2,i) = res(4,node2,i)                                  &
                + xnorm*paq24C*rlam12(i)                                       &
                + ynorm*paq24C*rlam13(i)                                       &
                + znorm*paq24C*rlam14(i)                                       &
                + face_speed_t(1)*paq24C*rlam15(i)                             &
                + xnorm*pbq24C*rlam22(i)                                       &
                + ynorm*pbq24C*rlam23(i)                                       &
                + znorm*pbq24C*rlam24(i)                                       &
                + face_speed_t(2)*pbq24C*rlam25(i)                             &
                + xnorm*pcq24C*rlam32(i)                                       &
                + ynorm*pcq24C*rlam33(i)                                       &
                + znorm*pcq24C*rlam34(i)                                       &
                + face_speed_t(3)*pcq24C*rlam35(i)

              res(5,node2,i) = res(5,node2,i)                                  &
                + xnorm*paq25C*rlam12(i)                                       &
                + ynorm*paq25C*rlam13(i)                                       &
                + znorm*paq25C*rlam14(i)                                       &
                + face_speed_t(1)*paq25C*rlam15(i)                             &
                + xnorm*pbq25C*rlam22(i)                                       &
                + ynorm*pbq25C*rlam23(i)                                       &
                + znorm*pbq25C*rlam24(i)                                       &
                + face_speed_t(2)*pbq25C*rlam25(i)                             &
                + xnorm*pcq25C*rlam32(i)                                       &
                + ynorm*pcq25C*rlam33(i)                                       &
                + znorm*pcq25C*rlam34(i)                                       &
                + face_speed_t(3)*pcq25C*rlam35(i)
            end do
          end if

          if (node3 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node3,i) = res(1,node3,i)                                  &
                + xnorm*paq31C*rlam12(i)                                       &
                + ynorm*paq31C*rlam13(i)                                       &
                + znorm*paq31C*rlam14(i)                                       &
                + face_speed_t(1)*paq31C*rlam15(i)                             &
                + xnorm*pbq31C*rlam22(i)                                       &
                + ynorm*pbq31C*rlam23(i)                                       &
                + znorm*pbq31C*rlam24(i)                                       &
                + face_speed_t(2)*pbq31C*rlam25(i)                             &
                + xnorm*pcq31C*rlam32(i)                                       &
                + ynorm*pcq31C*rlam33(i)                                       &
                + znorm*pcq31C*rlam34(i)                                       &
                + face_speed_t(3)*pcq31C*rlam35(i)

              res(2,node3,i) = res(2,node3,i)                                  &
                + xnorm*paq32C*rlam12(i)                                       &
                + ynorm*paq32C*rlam13(i)                                       &
                + znorm*paq32C*rlam14(i)                                       &
                + face_speed_t(1)*paq32C*rlam15(i)                             &
                + xnorm*pbq32C*rlam22(i)                                       &
                + ynorm*pbq32C*rlam23(i)                                       &
                + znorm*pbq32C*rlam24(i)                                       &
                + face_speed_t(2)*pbq32C*rlam25(i)                             &
                + xnorm*pcq32C*rlam32(i)                                       &
                + ynorm*pcq32C*rlam33(i)                                       &
                + znorm*pcq32C*rlam34(i)                                       &
                + face_speed_t(3)*pcq32C*rlam35(i)

              res(3,node3,i) = res(3,node3,i)                                  &
                + xnorm*paq33C*rlam12(i)                                       &
                + ynorm*paq33C*rlam13(i)                                       &
                + znorm*paq33C*rlam14(i)                                       &
                + face_speed_t(1)*paq33C*rlam15(i)                             &
                + xnorm*pbq33C*rlam22(i)                                       &
                + ynorm*pbq33C*rlam23(i)                                       &
                + znorm*pbq33C*rlam24(i)                                       &
                + face_speed_t(2)*pbq33C*rlam25(i)                             &
                + xnorm*pcq33C*rlam32(i)                                       &
                + ynorm*pcq33C*rlam33(i)                                       &
                + znorm*pcq33C*rlam34(i)                                       &
                + face_speed_t(3)*pcq33C*rlam35(i)

              res(4,node3,i) = res(4,node3,i)                                  &
                + xnorm*paq34C*rlam12(i)                                       &
                + ynorm*paq34C*rlam13(i)                                       &
                + znorm*paq34C*rlam14(i)                                       &
                + face_speed_t(1)*paq34C*rlam15(i)                             &
                + xnorm*pbq34C*rlam22(i)                                       &
                + ynorm*pbq34C*rlam23(i)                                       &
                + znorm*pbq34C*rlam24(i)                                       &
                + face_speed_t(2)*pbq34C*rlam25(i)                             &
                + xnorm*pcq34C*rlam32(i)                                       &
                + ynorm*pcq34C*rlam33(i)                                       &
                + znorm*pcq34C*rlam34(i)                                       &
                + face_speed_t(3)*pcq34C*rlam35(i)

              res(5,node3,i) = res(5,node3,i)                                  &
                + xnorm*paq35C*rlam12(i)                                       &
                + ynorm*paq35C*rlam13(i)                                       &
                + znorm*paq35C*rlam14(i)                                       &
                + face_speed_t(1)*paq35C*rlam15(i)                             &
                + xnorm*pbq35C*rlam22(i)                                       &
                + ynorm*pbq35C*rlam23(i)                                       &
                + znorm*pbq35C*rlam24(i)                                       &
                + face_speed_t(2)*pbq35C*rlam25(i)                             &
                + xnorm*pcq35C*rlam32(i)                                       &
                + ynorm*pcq35C*rlam33(i)                                       &
                + znorm*pcq35C*rlam34(i)                                       &
                + face_speed_t(3)*pcq35C*rlam35(i)
            end do
          end if

         endif res_contribs1

         a_contribs1 : if ( fill_a ) then
          idiag1 = iau(node1)
          idiag2 = iau(node2)
          idiag3 = iau(node3)

          ioff12 = 0
          jstart = ia(node2)
          jend   = ia(node2+1)-1
          search1 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node1 ) then
              ioff12 = j
              exit search1
            endif
          end do search1

          if ( ioff12 == 0 .and. node2 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 12'
            call lmpi_die
          endif

          ioff13 = 0
          jstart = ia(node3)
          jend   = ia(node3+1)-1
          search2 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node1 ) then
              ioff13 = j
              exit search2
            endif
          end do search2

          if ( ioff13 == 0 .and. node3 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 13'
            call lmpi_die
          endif

          ioff21 = 0
          jstart = ia(node1)
          jend   = ia(node1+1)-1
          search3 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node2 ) then
              ioff21 = j
              exit search3
            endif
          end do search3

          if ( ioff21 == 0 .and. node1 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 21'
            call lmpi_die
          endif

          ioff23 = 0
          jstart = ia(node3)
          jend   = ia(node3+1)-1
          search4 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node2 ) then
              ioff23 = j
              exit search4
            endif
          end do search4

          if ( ioff23 == 0 .and. node3 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 23'
            call lmpi_die
          endif

          ioff31 = 0
          jstart = ia(node1)
          jend   = ia(node1+1)-1
          search5 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node3 ) then
              ioff31 = j
              exit search5
            endif
          end do search5

          if ( ioff31 == 0 .and. node1 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 31'
            call lmpi_die
          endif

          ioff32 = 0
          jstart = ia(node2)
          jend   = ia(node2+1)-1
          search6 : do j = jstart, jend
            neighbor = abs(ja(j))
            if ( neighbor == node3 ) then
              ioff32 = j
              exit search6
            endif
          end do search6

          if ( ioff32 == 0 .and. node2 <= nnodes0 ) then
            write(*,*) 'Error finding off-diag 32'
            call lmpi_die
          endif

! Now assemble the contributions

!  dR1/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,idiag1) = aa(2,1,idiag1) + xnorm*paq11C
            aa(2,2,idiag1) = aa(2,2,idiag1) + xnorm*paq12C
            aa(2,3,idiag1) = aa(2,3,idiag1) + xnorm*paq13C
            aa(2,4,idiag1) = aa(2,4,idiag1) + xnorm*paq14C
            aa(2,5,idiag1) = aa(2,5,idiag1) + xnorm*paq15C

            aa(3,1,idiag1) = aa(3,1,idiag1) + ynorm*paq11C
            aa(3,2,idiag1) = aa(3,2,idiag1) + ynorm*paq12C
            aa(3,3,idiag1) = aa(3,3,idiag1) + ynorm*paq13C
            aa(3,4,idiag1) = aa(3,4,idiag1) + ynorm*paq14C
            aa(3,5,idiag1) = aa(3,5,idiag1) + ynorm*paq15C

            aa(4,1,idiag1) = aa(4,1,idiag1) + znorm*paq11C
            aa(4,2,idiag1) = aa(4,2,idiag1) + znorm*paq12C
            aa(4,3,idiag1) = aa(4,3,idiag1) + znorm*paq13C
            aa(4,4,idiag1) = aa(4,4,idiag1) + znorm*paq14C
            aa(4,5,idiag1) = aa(4,5,idiag1) + znorm*paq15C

            aa(5,1,idiag1) = aa(5,1,idiag1) + face_speed_t(1)*paq11C
            aa(5,2,idiag1) = aa(5,2,idiag1) + face_speed_t(1)*paq12C
            aa(5,3,idiag1) = aa(5,3,idiag1) + face_speed_t(1)*paq13C
            aa(5,4,idiag1) = aa(5,4,idiag1) + face_speed_t(1)*paq14C
            aa(5,5,idiag1) = aa(5,5,idiag1) + face_speed_t(1)*paq15C
          endif

!  dR1/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,ioff12) = aa(2,1,ioff12) + xnorm*paq21C
            aa(2,2,ioff12) = aa(2,2,ioff12) + xnorm*paq22C
            aa(2,3,ioff12) = aa(2,3,ioff12) + xnorm*paq23C
            aa(2,4,ioff12) = aa(2,4,ioff12) + xnorm*paq24C
            aa(2,5,ioff12) = aa(2,5,ioff12) + xnorm*paq25C

            aa(3,1,ioff12) = aa(3,1,ioff12) + ynorm*paq21C
            aa(3,2,ioff12) = aa(3,2,ioff12) + ynorm*paq22C
            aa(3,3,ioff12) = aa(3,3,ioff12) + ynorm*paq23C
            aa(3,4,ioff12) = aa(3,4,ioff12) + ynorm*paq24C
            aa(3,5,ioff12) = aa(3,5,ioff12) + ynorm*paq25C

            aa(4,1,ioff12) = aa(4,1,ioff12) + znorm*paq21C
            aa(4,2,ioff12) = aa(4,2,ioff12) + znorm*paq22C
            aa(4,3,ioff12) = aa(4,3,ioff12) + znorm*paq23C
            aa(4,4,ioff12) = aa(4,4,ioff12) + znorm*paq24C
            aa(4,5,ioff12) = aa(4,5,ioff12) + znorm*paq25C

            aa(5,1,ioff12) = aa(5,1,ioff12) + face_speed_t(1)*paq21C
            aa(5,2,ioff12) = aa(5,2,ioff12) + face_speed_t(1)*paq22C
            aa(5,3,ioff12) = aa(5,3,ioff12) + face_speed_t(1)*paq23C
            aa(5,4,ioff12) = aa(5,4,ioff12) + face_speed_t(1)*paq24C
            aa(5,5,ioff12) = aa(5,5,ioff12) + face_speed_t(1)*paq25C
          endif

!  dR1/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,ioff13) = aa(2,1,ioff13) + xnorm*paq31C
            aa(2,2,ioff13) = aa(2,2,ioff13) + xnorm*paq32C
            aa(2,3,ioff13) = aa(2,3,ioff13) + xnorm*paq33C
            aa(2,4,ioff13) = aa(2,4,ioff13) + xnorm*paq34C
            aa(2,5,ioff13) = aa(2,5,ioff13) + xnorm*paq35C

            aa(3,1,ioff13) = aa(3,1,ioff13) + ynorm*paq31C
            aa(3,2,ioff13) = aa(3,2,ioff13) + ynorm*paq32C
            aa(3,3,ioff13) = aa(3,3,ioff13) + ynorm*paq33C
            aa(3,4,ioff13) = aa(3,4,ioff13) + ynorm*paq34C
            aa(3,5,ioff13) = aa(3,5,ioff13) + ynorm*paq35C

            aa(4,1,ioff13) = aa(4,1,ioff13) + znorm*paq31C
            aa(4,2,ioff13) = aa(4,2,ioff13) + znorm*paq32C
            aa(4,3,ioff13) = aa(4,3,ioff13) + znorm*paq33C
            aa(4,4,ioff13) = aa(4,4,ioff13) + znorm*paq34C
            aa(4,5,ioff13) = aa(4,5,ioff13) + znorm*paq35C

            aa(5,1,ioff13) = aa(5,1,ioff13) + face_speed_t(1)*paq31C
            aa(5,2,ioff13) = aa(5,2,ioff13) + face_speed_t(1)*paq32C
            aa(5,3,ioff13) = aa(5,3,ioff13) + face_speed_t(1)*paq33C
            aa(5,4,ioff13) = aa(5,4,ioff13) + face_speed_t(1)*paq34C
            aa(5,5,ioff13) = aa(5,5,ioff13) + face_speed_t(1)*paq35C
          endif

!  dR2/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,ioff21) = aa(2,1,ioff21) + xnorm*pbq11C
            aa(2,2,ioff21) = aa(2,2,ioff21) + xnorm*pbq12C
            aa(2,3,ioff21) = aa(2,3,ioff21) + xnorm*pbq13C
            aa(2,4,ioff21) = aa(2,4,ioff21) + xnorm*pbq14C
            aa(2,5,ioff21) = aa(2,5,ioff21) + xnorm*pbq15C

            aa(3,1,ioff21) = aa(3,1,ioff21) + ynorm*pbq11C
            aa(3,2,ioff21) = aa(3,2,ioff21) + ynorm*pbq12C
            aa(3,3,ioff21) = aa(3,3,ioff21) + ynorm*pbq13C
            aa(3,4,ioff21) = aa(3,4,ioff21) + ynorm*pbq14C
            aa(3,5,ioff21) = aa(3,5,ioff21) + ynorm*pbq15C

            aa(4,1,ioff21) = aa(4,1,ioff21) + znorm*pbq11C
            aa(4,2,ioff21) = aa(4,2,ioff21) + znorm*pbq12C
            aa(4,3,ioff21) = aa(4,3,ioff21) + znorm*pbq13C
            aa(4,4,ioff21) = aa(4,4,ioff21) + znorm*pbq14C
            aa(4,5,ioff21) = aa(4,5,ioff21) + znorm*pbq15C

            aa(5,1,ioff21) = aa(5,1,ioff21) + face_speed_t(2)*pbq11C
            aa(5,2,ioff21) = aa(5,2,ioff21) + face_speed_t(2)*pbq12C
            aa(5,3,ioff21) = aa(5,3,ioff21) + face_speed_t(2)*pbq13C
            aa(5,4,ioff21) = aa(5,4,ioff21) + face_speed_t(2)*pbq14C
            aa(5,5,ioff21) = aa(5,5,ioff21) + face_speed_t(2)*pbq15C
          endif

!  dR2/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,idiag2) = aa(2,1,idiag2) + xnorm*pbq21C
            aa(2,2,idiag2) = aa(2,2,idiag2) + xnorm*pbq22C
            aa(2,3,idiag2) = aa(2,3,idiag2) + xnorm*pbq23C
            aa(2,4,idiag2) = aa(2,4,idiag2) + xnorm*pbq24C
            aa(2,5,idiag2) = aa(2,5,idiag2) + xnorm*pbq25C

            aa(3,1,idiag2) = aa(3,1,idiag2) + ynorm*pbq21C
            aa(3,2,idiag2) = aa(3,2,idiag2) + ynorm*pbq22C
            aa(3,3,idiag2) = aa(3,3,idiag2) + ynorm*pbq23C
            aa(3,4,idiag2) = aa(3,4,idiag2) + ynorm*pbq24C
            aa(3,5,idiag2) = aa(3,5,idiag2) + ynorm*pbq25C

            aa(4,1,idiag2) = aa(4,1,idiag2) + znorm*pbq21C
            aa(4,2,idiag2) = aa(4,2,idiag2) + znorm*pbq22C
            aa(4,3,idiag2) = aa(4,3,idiag2) + znorm*pbq23C
            aa(4,4,idiag2) = aa(4,4,idiag2) + znorm*pbq24C
            aa(4,5,idiag2) = aa(4,5,idiag2) + znorm*pbq25C

            aa(5,1,idiag2) = aa(5,1,idiag2) + face_speed_t(2)*pbq21C
            aa(5,2,idiag2) = aa(5,2,idiag2) + face_speed_t(2)*pbq22C
            aa(5,3,idiag2) = aa(5,3,idiag2) + face_speed_t(2)*pbq23C
            aa(5,4,idiag2) = aa(5,4,idiag2) + face_speed_t(2)*pbq24C
            aa(5,5,idiag2) = aa(5,5,idiag2) + face_speed_t(2)*pbq25C
          endif

!  dR2/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,ioff23) = aa(2,1,ioff23) + xnorm*pbq31C
            aa(2,2,ioff23) = aa(2,2,ioff23) + xnorm*pbq32C
            aa(2,3,ioff23) = aa(2,3,ioff23) + xnorm*pbq33C
            aa(2,4,ioff23) = aa(2,4,ioff23) + xnorm*pbq34C
            aa(2,5,ioff23) = aa(2,5,ioff23) + xnorm*pbq35C

            aa(3,1,ioff23) = aa(3,1,ioff23) + ynorm*pbq31C
            aa(3,2,ioff23) = aa(3,2,ioff23) + ynorm*pbq32C
            aa(3,3,ioff23) = aa(3,3,ioff23) + ynorm*pbq33C
            aa(3,4,ioff23) = aa(3,4,ioff23) + ynorm*pbq34C
            aa(3,5,ioff23) = aa(3,5,ioff23) + ynorm*pbq35C

            aa(4,1,ioff23) = aa(4,1,ioff23) + znorm*pbq31C
            aa(4,2,ioff23) = aa(4,2,ioff23) + znorm*pbq32C
            aa(4,3,ioff23) = aa(4,3,ioff23) + znorm*pbq33C
            aa(4,4,ioff23) = aa(4,4,ioff23) + znorm*pbq34C
            aa(4,5,ioff23) = aa(4,5,ioff23) + znorm*pbq35C

            aa(5,1,ioff23) = aa(5,1,ioff23) + face_speed_t(2)*pbq31C
            aa(5,2,ioff23) = aa(5,2,ioff23) + face_speed_t(2)*pbq32C
            aa(5,3,ioff23) = aa(5,3,ioff23) + face_speed_t(2)*pbq33C
            aa(5,4,ioff23) = aa(5,4,ioff23) + face_speed_t(2)*pbq34C
            aa(5,5,ioff23) = aa(5,5,ioff23) + face_speed_t(2)*pbq35C
          endif

!  dR3/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,ioff31) = aa(2,1,ioff31) + xnorm*pcq11C
            aa(2,2,ioff31) = aa(2,2,ioff31) + xnorm*pcq12C
            aa(2,3,ioff31) = aa(2,3,ioff31) + xnorm*pcq13C
            aa(2,4,ioff31) = aa(2,4,ioff31) + xnorm*pcq14C
            aa(2,5,ioff31) = aa(2,5,ioff31) + xnorm*pcq15C

            aa(3,1,ioff31) = aa(3,1,ioff31) + ynorm*pcq11C
            aa(3,2,ioff31) = aa(3,2,ioff31) + ynorm*pcq12C
            aa(3,3,ioff31) = aa(3,3,ioff31) + ynorm*pcq13C
            aa(3,4,ioff31) = aa(3,4,ioff31) + ynorm*pcq14C
            aa(3,5,ioff31) = aa(3,5,ioff31) + ynorm*pcq15C

            aa(4,1,ioff31) = aa(4,1,ioff31) + znorm*pcq11C
            aa(4,2,ioff31) = aa(4,2,ioff31) + znorm*pcq12C
            aa(4,3,ioff31) = aa(4,3,ioff31) + znorm*pcq13C
            aa(4,4,ioff31) = aa(4,4,ioff31) + znorm*pcq14C
            aa(4,5,ioff31) = aa(4,5,ioff31) + znorm*pcq15C

            aa(5,1,ioff31) = aa(5,1,ioff31) + face_speed_t(3)*pcq11C
            aa(5,2,ioff31) = aa(5,2,ioff31) + face_speed_t(3)*pcq12C
            aa(5,3,ioff31) = aa(5,3,ioff31) + face_speed_t(3)*pcq13C
            aa(5,4,ioff31) = aa(5,4,ioff31) + face_speed_t(3)*pcq14C
            aa(5,5,ioff31) = aa(5,5,ioff31) + face_speed_t(3)*pcq15C
          endif

!  dR3/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,ioff32) = aa(2,1,ioff32) + xnorm*pcq21C
            aa(2,2,ioff32) = aa(2,2,ioff32) + xnorm*pcq22C
            aa(2,3,ioff32) = aa(2,3,ioff32) + xnorm*pcq23C
            aa(2,4,ioff32) = aa(2,4,ioff32) + xnorm*pcq24C
            aa(2,5,ioff32) = aa(2,5,ioff32) + xnorm*pcq25C

            aa(3,1,ioff32) = aa(3,1,ioff32) + ynorm*pcq21C
            aa(3,2,ioff32) = aa(3,2,ioff32) + ynorm*pcq22C
            aa(3,3,ioff32) = aa(3,3,ioff32) + ynorm*pcq23C
            aa(3,4,ioff32) = aa(3,4,ioff32) + ynorm*pcq24C
            aa(3,5,ioff32) = aa(3,5,ioff32) + ynorm*pcq25C

            aa(4,1,ioff32) = aa(4,1,ioff32) + znorm*pcq21C
            aa(4,2,ioff32) = aa(4,2,ioff32) + znorm*pcq22C
            aa(4,3,ioff32) = aa(4,3,ioff32) + znorm*pcq23C
            aa(4,4,ioff32) = aa(4,4,ioff32) + znorm*pcq24C
            aa(4,5,ioff32) = aa(4,5,ioff32) + znorm*pcq25C

            aa(5,1,ioff32) = aa(5,1,ioff32) + face_speed_t(3)*pcq21C
            aa(5,2,ioff32) = aa(5,2,ioff32) + face_speed_t(3)*pcq22C
            aa(5,3,ioff32) = aa(5,3,ioff32) + face_speed_t(3)*pcq23C
            aa(5,4,ioff32) = aa(5,4,ioff32) + face_speed_t(3)*pcq24C
            aa(5,5,ioff32) = aa(5,5,ioff32) + face_speed_t(3)*pcq25C
          endif

!  dR3/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,idiag3) = aa(2,1,idiag3) + xnorm*pcq31C
            aa(2,2,idiag3) = aa(2,2,idiag3) + xnorm*pcq32C
            aa(2,3,idiag3) = aa(2,3,idiag3) + xnorm*pcq33C
            aa(2,4,idiag3) = aa(2,4,idiag3) + xnorm*pcq34C
            aa(2,5,idiag3) = aa(2,5,idiag3) + xnorm*pcq35C

            aa(3,1,idiag3) = aa(3,1,idiag3) + ynorm*pcq31C
            aa(3,2,idiag3) = aa(3,2,idiag3) + ynorm*pcq32C
            aa(3,3,idiag3) = aa(3,3,idiag3) + ynorm*pcq33C
            aa(3,4,idiag3) = aa(3,4,idiag3) + ynorm*pcq34C
            aa(3,5,idiag3) = aa(3,5,idiag3) + ynorm*pcq35C

            aa(4,1,idiag3) = aa(4,1,idiag3) + znorm*pcq31C
            aa(4,2,idiag3) = aa(4,2,idiag3) + znorm*pcq32C
            aa(4,3,idiag3) = aa(4,3,idiag3) + znorm*pcq33C
            aa(4,4,idiag3) = aa(4,4,idiag3) + znorm*pcq34C
            aa(4,5,idiag3) = aa(4,5,idiag3) + znorm*pcq35C

            aa(5,1,idiag3) = aa(5,1,idiag3) + face_speed_t(3)*pcq31C
            aa(5,2,idiag3) = aa(5,2,idiag3) + face_speed_t(3)*pcq32C
            aa(5,3,idiag3) = aa(5,3,idiag3) + face_speed_t(3)*pcq33C
            aa(5,4,idiag3) = aa(5,4,idiag3) + face_speed_t(3)*pcq34C
            aa(5,5,idiag3) = aa(5,5,idiag3) + face_speed_t(3)*pcq35C
          endif
         endif a_contribs1

        end do loop_tris_01


        loop_quads_01: do n = 1,bc(ib)%nbfaceq

          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(n,1))
          node2 = bc(ib)%ibnode(bc(ib)%f2nqb(n,2))
          node3 = bc(ib)%ibnode(bc(ib)%f2nqb(n,3))
          node4 = bc(ib)%ibnode(bc(ib)%f2nqb(n,4))

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)
!          p1 = qnode(5,node1)
          p1q1 = my_0
          p1q2 = my_0
          p1q3 = my_0
          p1q4 = my_0
          p1q5 = my_1

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)
!          p2 = qnode(5,node2)
          p2q1 = my_0
          p2q2 = my_0
          p2q3 = my_0
          p2q4 = my_0
          p2q5 = my_1

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)
!          p3 = qnode(5,node3)
          p3q1 = my_0
          p3q2 = my_0
          p3q3 = my_0
          p3q4 = my_0
          p3q5 = my_1

!         x4 = x(node4)
!         y4 = y(node4)
!         z4 = z(node4)
!         p4 = qnode(5,node4)
          p4q1 = my_0
          p4q2 = my_0
          p4q3 = my_0
          p4q4 = my_0
          p4q5 = my_1

!         get dual norm contributions at each node of the quad face

          face_speed_q(:) = my_0

          if ( need_grid_velocity ) then
            call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,        &
                                noninertial,xnorm_q,ynorm_q,znorm_q,dxdt=dxdt, &
                                dydt=dydt,dzdt=dzdt,facespeed=face_speed_q)
          else
            call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,        &
                                noninertial,xnorm_q,ynorm_q,znorm_q)
          endif

! Now convert derivatives wrt primitive to be wrt conservative

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node1) / qnode(1,node1)
          q2Q2 = my_1 / qnode(1,node1)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node1) / qnode(1,node1)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node1)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node1) / qnode(1,node1)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node1)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node1)*qnode(2,node1)                   &
            + qnode(3,node1)*qnode(3,node1)                                    &
            + qnode(4,node1)*qnode(4,node1))
          q5Q2 = - gm1 * qnode(2,node1)
          q5Q3 = - gm1 * qnode(3,node1)
          q5Q4 = - gm1 * qnode(4,node1)
          q5Q5 = gm1

          p1q1C = p1q1*q1Q1 + p1q2*q2Q1 + p1q3*q3Q1 + p1q4*q4Q1 + p1q5*q5Q1
          p1q2C = p1q1*q1Q2 + p1q2*q2Q2 + p1q3*q3Q2 + p1q4*q4Q2 + p1q5*q5Q2
          p1q3C = p1q1*q1Q3 + p1q2*q2Q3 + p1q3*q3Q3 + p1q4*q4Q3 + p1q5*q5Q3
          p1q4C = p1q1*q1Q4 + p1q2*q2Q4 + p1q3*q3Q4 + p1q4*q4Q4 + p1q5*q5Q4
          p1q5C = p1q1*q1Q5 + p1q2*q2Q5 + p1q3*q3Q5 + p1q4*q4Q5 + p1q5*q5Q5

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node2) / qnode(1,node2)
          q2Q2 = my_1 / qnode(1,node2)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node2) / qnode(1,node2)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node2)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node2) / qnode(1,node2)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node2)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node2)*qnode(2,node2)                   &
            + qnode(3,node2)*qnode(3,node2)                                    &
            + qnode(4,node2)*qnode(4,node2))
          q5Q2 = - gm1 * qnode(2,node2)
          q5Q3 = - gm1 * qnode(3,node2)
          q5Q4 = - gm1 * qnode(4,node2)
          q5Q5 = gm1

          p2q1C = p2q1*q1Q1 + p2q2*q2Q1 + p2q3*q3Q1 + p2q4*q4Q1 + p2q5*q5Q1
          p2q2C = p2q1*q1Q2 + p2q2*q2Q2 + p2q3*q3Q2 + p2q4*q4Q2 + p2q5*q5Q2
          p2q3C = p2q1*q1Q3 + p2q2*q2Q3 + p2q3*q3Q3 + p2q4*q4Q3 + p2q5*q5Q3
          p2q4C = p2q1*q1Q4 + p2q2*q2Q4 + p2q3*q3Q4 + p2q4*q4Q4 + p2q5*q5Q4
          p2q5C = p2q1*q1Q5 + p2q2*q2Q5 + p2q3*q3Q5 + p2q4*q4Q5 + p2q5*q5Q5

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node3) / qnode(1,node3)
          q2Q2 = my_1 / qnode(1,node3)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node3) / qnode(1,node3)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node3)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node3) / qnode(1,node3)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node3)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node3)*qnode(2,node3)                   &
            + qnode(3,node3)*qnode(3,node3)                                    &
            + qnode(4,node3)*qnode(4,node3))
          q5Q2 = - gm1 * qnode(2,node3)
          q5Q3 = - gm1 * qnode(3,node3)
          q5Q4 = - gm1 * qnode(4,node3)
          q5Q5 = gm1

          p3q1C = p3q1*q1Q1 + p3q2*q2Q1 + p3q3*q3Q1 + p3q4*q4Q1 + p3q5*q5Q1
          p3q2C = p3q1*q1Q2 + p3q2*q2Q2 + p3q3*q3Q2 + p3q4*q4Q2 + p3q5*q5Q2
          p3q3C = p3q1*q1Q3 + p3q2*q2Q3 + p3q3*q3Q3 + p3q4*q4Q3 + p3q5*q5Q3
          p3q4C = p3q1*q1Q4 + p3q2*q2Q4 + p3q3*q3Q4 + p3q4*q4Q4 + p3q5*q5Q4
          p3q5C = p3q1*q1Q5 + p3q2*q2Q5 + p3q3*q3Q5 + p3q4*q4Q5 + p3q5*q5Q5

          q1Q1 = my_1
          q1Q2 = my_0
          q1Q3 = my_0
          q1Q4 = my_0
          q1Q5 = my_0

          q2Q1 = - qnode(2,node4) / qnode(1,node4)
          q2Q2 = my_1 / qnode(1,node4)
          q2Q3 = my_0
          q2Q4 = my_0
          q2Q5 = my_0

          q3Q1 = - qnode(3,node4) / qnode(1,node4)
          q3Q2 = my_0
          q3Q3 = my_1 / qnode(1,node4)
          q3Q4 = my_0
          q3Q5 = my_0

          q4Q1 = - qnode(4,node4) / qnode(1,node4)
          q4Q2 = my_0
          q4Q3 = my_0
          q4Q4 = my_1 / qnode(1,node4)
          q4Q5 = my_0

          q5Q1 = gm1 / my_2 * (qnode(2,node4)*qnode(2,node4)                   &
            + qnode(3,node4)*qnode(3,node4)                                    &
            + qnode(4,node4)*qnode(4,node4))
          q5Q2 = - gm1 * qnode(2,node4)
          q5Q3 = - gm1 * qnode(3,node4)
          q5Q4 = - gm1 * qnode(4,node4)
          q5Q5 = gm1

          p4q1C = p4q1*q1Q1 + p4q2*q2Q1 + p4q3*q3Q1 + p4q4*q4Q1 + p4q5*q5Q1
          p4q2C = p4q1*q1Q2 + p4q2*q2Q2 + p4q3*q3Q2 + p4q4*q4Q2 + p4q5*q5Q2
          p4q3C = p4q1*q1Q3 + p4q2*q2Q3 + p4q3*q3Q3 + p4q4*q4Q3 + p4q5*q5Q3
          p4q4C = p4q1*q1Q4 + p4q2*q2Q4 + p4q3*q3Q4 + p4q4*q4Q4 + p4q5*q5Q4
          p4q5C = p4q1*q1Q5 + p4q2*q2Q5 + p4q3*q3Q5 + p4q4*q4Q5 + p4q5*q5Q5

        res_contribs2 : if ( fill_res ) then
          do i = 1, nfunctions
            rlam12(i) = coltag(2,node1)*rlam(2,node1,i)
            rlam13(i) = coltag(3,node1)*rlam(3,node1,i)
            rlam14(i) = coltag(4,node1)*rlam(4,node1,i)
            rlam15(i) = coltag(5,node1)*rlam(5,node1,i)

            rlam22(i) = coltag(2,node2)*rlam(2,node2,i)
            rlam23(i) = coltag(3,node2)*rlam(3,node2,i)
            rlam24(i) = coltag(4,node2)*rlam(4,node2,i)
            rlam25(i) = coltag(5,node2)*rlam(5,node2,i)

            rlam32(i) = coltag(2,node3)*rlam(2,node3,i)
            rlam33(i) = coltag(3,node3)*rlam(3,node3,i)
            rlam34(i) = coltag(4,node3)*rlam(4,node3,i)
            rlam35(i) = coltag(5,node3)*rlam(5,node3,i)

            rlam42(i) = coltag(2,node4)*rlam(2,node4,i)
            rlam43(i) = coltag(3,node4)*rlam(3,node4,i)
            rlam44(i) = coltag(4,node4)*rlam(4,node4,i)
            rlam45(i) = coltag(5,node4)*rlam(5,node4,i)
          end do

!  Check this, are these the right lambdas for quads?!

          xnorm = xnorm_q(1)
          ynorm = ynorm_q(1)
          znorm = znorm_q(1)

          face_speed = face_speed_q(1)

          if (node1 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node1,i) = res(1,node1,i)                                  &
                + xnorm*p1q1C*rlam12(i)                                        &
                + ynorm*p1q1C*rlam13(i)                                        &
                + znorm*p1q1C*rlam14(i)                                        &
                + face_speed*p1q1C*rlam15(i)

              res(2,node1,i) = res(2,node1,i)                                  &
                + xnorm*p1q2C*rlam12(i)                                        &
                + ynorm*p1q2C*rlam13(i)                                        &
                + znorm*p1q2C*rlam14(i)                                        &
                + face_speed*p1q2C*rlam15(i)

              res(3,node1,i) = res(3,node1,i)                                  &
                + xnorm*p1q3C*rlam12(i)                                        &
                + ynorm*p1q3C*rlam13(i)                                        &
                + znorm*p1q3C*rlam14(i)                                        &
                + face_speed*p1q3C*rlam15(i)

              res(4,node1,i) = res(4,node1,i)                                  &
                + xnorm*p1q4C*rlam12(i)                                        &
                + ynorm*p1q4C*rlam13(i)                                        &
                + znorm*p1q4C*rlam14(i)                                        &
                + face_speed*p1q4C*rlam15(i)

              res(5,node1,i) = res(5,node1,i)                                  &
                + xnorm*p1q5C*rlam12(i)                                        &
                + ynorm*p1q5C*rlam13(i)                                        &
                + znorm*p1q5C*rlam14(i)                                        &
                + face_speed*p1q5C*rlam15(i)
            end do
          end if

          xnorm = xnorm_q(2)
          ynorm = ynorm_q(2)
          znorm = znorm_q(2)

          face_speed = face_speed_q(2)

          if (node2 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node2,i) = res(1,node2,i)                                  &
                + xnorm*p2q1C*rlam22(i)                                        &
                + ynorm*p2q1C*rlam23(i)                                        &
                + znorm*p2q1C*rlam24(i)                                        &
                + face_speed*p2q1C*rlam25(i)

              res(2,node2,i) = res(2,node2,i)                                  &
                + xnorm*p2q2C*rlam22(i)                                        &
                + ynorm*p2q2C*rlam23(i)                                        &
                + znorm*p2q2C*rlam24(i)                                        &
                + face_speed*p2q2C*rlam25(i)

              res(3,node2,i) = res(3,node2,i)                                  &
                + xnorm*p2q3C*rlam22(i)                                        &
                + ynorm*p2q3C*rlam23(i)                                        &
                + znorm*p2q3C*rlam24(i)                                        &
                + face_speed*p2q3C*rlam25(i)

              res(4,node2,i) = res(4,node2,i)                                  &
                + xnorm*p2q4C*rlam22(i)                                        &
                + ynorm*p2q4C*rlam23(i)                                        &
                + znorm*p2q4C*rlam24(i)                                        &
                + face_speed*p2q4C*rlam25(i)

              res(5,node2,i) = res(5,node2,i)                                  &
                + xnorm*p2q5C*rlam22(i)                                        &
                + ynorm*p2q5C*rlam23(i)                                        &
                + znorm*p2q5C*rlam24(i)                                        &
                + face_speed*p2q5C*rlam25(i)
            end do
          end if

          xnorm = xnorm_q(3)
          ynorm = ynorm_q(3)
          znorm = znorm_q(3)

          face_speed = face_speed_q(3)

          if (node3 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node3,i) = res(1,node3,i)                                  &
                + xnorm*p3q1C*rlam32(i)                                        &
                + ynorm*p3q1C*rlam33(i)                                        &
                + znorm*p3q1C*rlam34(i)                                        &
                + face_speed*p3q1C*rlam35(i)

              res(2,node3,i) = res(2,node3,i)                                  &
                + xnorm*p3q2C*rlam32(i)                                        &
                + ynorm*p3q2C*rlam33(i)                                        &
                + znorm*p3q2C*rlam34(i)                                        &
                + face_speed*p3q2C*rlam35(i)

              res(3,node3,i) = res(3,node3,i)                                  &
                + xnorm*p3q3C*rlam32(i)                                        &
                + ynorm*p3q3C*rlam33(i)                                        &
                + znorm*p3q3C*rlam34(i)                                        &
                + face_speed*p3q3C*rlam35(i)

              res(4,node3,i) = res(4,node3,i)                                  &
                + xnorm*p3q4C*rlam32(i)                                        &
                + ynorm*p3q4C*rlam33(i)                                        &
                + znorm*p3q4C*rlam34(i)                                        &
                + face_speed*p3q4C*rlam35(i)

              res(5,node3,i) = res(5,node3,i)                                  &
                + xnorm*p3q5C*rlam32(i)                                        &
                + ynorm*p3q5C*rlam33(i)                                        &
                + znorm*p3q5C*rlam34(i)                                        &
                + face_speed*p3q5C*rlam35(i)
            end do
          end if

          xnorm = xnorm_q(4)
          ynorm = ynorm_q(4)
          znorm = znorm_q(4)

          face_speed = face_speed_q(4)

          if (node4 <= nnodes0) then
            do i = 1, nfunctions
              res(1,node4,i) = res(1,node4,i)                                  &
                + xnorm*p4q1C*rlam42(i)                                        &
                + ynorm*p4q1C*rlam43(i)                                        &
                + znorm*p4q1C*rlam44(i)                                        &
                + face_speed*p4q1C*rlam45(i)

              res(2,node4,i) = res(2,node4,i)                                  &
                + xnorm*p4q2C*rlam42(i)                                        &
                + ynorm*p4q2C*rlam43(i)                                        &
                + znorm*p4q2C*rlam44(i)                                        &
                + face_speed*p4q2C*rlam45(i)

              res(3,node4,i) = res(3,node4,i)                                  &
                + xnorm*p4q3C*rlam42(i)                                        &
                + ynorm*p4q3C*rlam43(i)                                        &
                + znorm*p4q3C*rlam44(i)                                        &
                + face_speed*p4q3C*rlam45(i)

              res(4,node4,i) = res(4,node4,i)                                  &
                + xnorm*p4q4C*rlam42(i)                                        &
                + ynorm*p4q4C*rlam43(i)                                        &
                + znorm*p4q4C*rlam44(i)                                        &
                + face_speed*p4q4C*rlam45(i)

              res(5,node4,i) = res(5,node4,i)                                  &
                + xnorm*p4q5C*rlam42(i)                                        &
                + ynorm*p4q5C*rlam43(i)                                        &
                + znorm*p4q5C*rlam44(i)                                        &
                + face_speed*p4q5C*rlam45(i)
            end do
          end if
        endif res_contribs2

        a_contribs2 : if ( fill_a ) then
          idiag1 = iau(node1)
          idiag2 = iau(node2)
          idiag3 = iau(node3)
          idiag4 = iau(node4)

! Now assemble the contributions

!  dR1/dQ1

          if ( node1 <= nnodes0 ) then
            aa(2,1,idiag1) = aa(2,1,idiag1) + xnorm_q(1)*p1q1C
            aa(2,2,idiag1) = aa(2,2,idiag1) + xnorm_q(1)*p1q2C
            aa(2,3,idiag1) = aa(2,3,idiag1) + xnorm_q(1)*p1q3C
            aa(2,4,idiag1) = aa(2,4,idiag1) + xnorm_q(1)*p1q4C
            aa(2,5,idiag1) = aa(2,5,idiag1) + xnorm_q(1)*p1q5C

            aa(3,1,idiag1) = aa(3,1,idiag1) + ynorm_q(1)*p1q1C
            aa(3,2,idiag1) = aa(3,2,idiag1) + ynorm_q(1)*p1q2C
            aa(3,3,idiag1) = aa(3,3,idiag1) + ynorm_q(1)*p1q3C
            aa(3,4,idiag1) = aa(3,4,idiag1) + ynorm_q(1)*p1q4C
            aa(3,5,idiag1) = aa(3,5,idiag1) + ynorm_q(1)*p1q5C

            aa(4,1,idiag1) = aa(4,1,idiag1) + znorm_q(1)*p1q1C
            aa(4,2,idiag1) = aa(4,2,idiag1) + znorm_q(1)*p1q2C
            aa(4,3,idiag1) = aa(4,3,idiag1) + znorm_q(1)*p1q3C
            aa(4,4,idiag1) = aa(4,4,idiag1) + znorm_q(1)*p1q4C
            aa(4,5,idiag1) = aa(4,5,idiag1) + znorm_q(1)*p1q5C

            aa(5,1,idiag1) = aa(5,1,idiag1) + face_speed_q(1)*p1q1C
            aa(5,2,idiag1) = aa(5,2,idiag1) + face_speed_q(1)*p1q2C
            aa(5,3,idiag1) = aa(5,3,idiag1) + face_speed_q(1)*p1q3C
            aa(5,4,idiag1) = aa(5,4,idiag1) + face_speed_q(1)*p1q4C
            aa(5,5,idiag1) = aa(5,5,idiag1) + face_speed_q(1)*p1q5C
          endif

!  dR2/dQ2

          if ( node2 <= nnodes0 ) then
            aa(2,1,idiag2) = aa(2,1,idiag2) + xnorm_q(2)*p2q1C
            aa(2,2,idiag2) = aa(2,2,idiag2) + xnorm_q(2)*p2q2C
            aa(2,3,idiag2) = aa(2,3,idiag2) + xnorm_q(2)*p2q3C
            aa(2,4,idiag2) = aa(2,4,idiag2) + xnorm_q(2)*p2q4C
            aa(2,5,idiag2) = aa(2,5,idiag2) + xnorm_q(2)*p2q5C

            aa(3,1,idiag2) = aa(3,1,idiag2) + ynorm_q(2)*p2q1C
            aa(3,2,idiag2) = aa(3,2,idiag2) + ynorm_q(2)*p2q2C
            aa(3,3,idiag2) = aa(3,3,idiag2) + ynorm_q(2)*p2q3C
            aa(3,4,idiag2) = aa(3,4,idiag2) + ynorm_q(2)*p2q4C
            aa(3,5,idiag2) = aa(3,5,idiag2) + ynorm_q(2)*p2q5C

            aa(4,1,idiag2) = aa(4,1,idiag2) + znorm_q(2)*p2q1C
            aa(4,2,idiag2) = aa(4,2,idiag2) + znorm_q(2)*p2q2C
            aa(4,3,idiag2) = aa(4,3,idiag2) + znorm_q(2)*p2q3C
            aa(4,4,idiag2) = aa(4,4,idiag2) + znorm_q(2)*p2q4C
            aa(4,5,idiag2) = aa(4,5,idiag2) + znorm_q(2)*p2q5C

            aa(5,1,idiag2) = aa(5,1,idiag2) + face_speed_q(2)*p2q1C
            aa(5,2,idiag2) = aa(5,2,idiag2) + face_speed_q(2)*p2q2C
            aa(5,3,idiag2) = aa(5,3,idiag2) + face_speed_q(2)*p2q3C
            aa(5,4,idiag2) = aa(5,4,idiag2) + face_speed_q(2)*p2q4C
            aa(5,5,idiag2) = aa(5,5,idiag2) + face_speed_q(2)*p2q5C
          endif

!  dR3/dQ3

          if ( node3 <= nnodes0 ) then
            aa(2,1,idiag3) = aa(2,1,idiag3) + xnorm_q(3)*p3q1C
            aa(2,2,idiag3) = aa(2,2,idiag3) + xnorm_q(3)*p3q2C
            aa(2,3,idiag3) = aa(2,3,idiag3) + xnorm_q(3)*p3q3C
            aa(2,4,idiag3) = aa(2,4,idiag3) + xnorm_q(3)*p3q4C
            aa(2,5,idiag3) = aa(2,5,idiag3) + xnorm_q(3)*p3q5C

            aa(3,1,idiag3) = aa(3,1,idiag3) + ynorm_q(3)*p3q1C
            aa(3,2,idiag3) = aa(3,2,idiag3) + ynorm_q(3)*p3q2C
            aa(3,3,idiag3) = aa(3,3,idiag3) + ynorm_q(3)*p3q3C
            aa(3,4,idiag3) = aa(3,4,idiag3) + ynorm_q(3)*p3q4C
            aa(3,5,idiag3) = aa(3,5,idiag3) + ynorm_q(3)*p3q5C

            aa(4,1,idiag3) = aa(4,1,idiag3) + znorm_q(3)*p3q1C
            aa(4,2,idiag3) = aa(4,2,idiag3) + znorm_q(3)*p3q2C
            aa(4,3,idiag3) = aa(4,3,idiag3) + znorm_q(3)*p3q3C
            aa(4,4,idiag3) = aa(4,4,idiag3) + znorm_q(3)*p3q4C
            aa(4,5,idiag3) = aa(4,5,idiag3) + znorm_q(3)*p3q5C

            aa(5,1,idiag3) = aa(5,1,idiag3) + face_speed_q(3)*p3q1C
            aa(5,2,idiag3) = aa(5,2,idiag3) + face_speed_q(3)*p3q2C
            aa(5,3,idiag3) = aa(5,3,idiag3) + face_speed_q(3)*p3q3C
            aa(5,4,idiag3) = aa(5,4,idiag3) + face_speed_q(3)*p3q4C
            aa(5,5,idiag3) = aa(5,5,idiag3) + face_speed_q(3)*p3q5C
          endif

!  dR4/dQ4

          if ( node4 <= nnodes0 ) then
            aa(2,1,idiag4) = aa(2,1,idiag4) + xnorm_q(4)*p4q1C
            aa(2,2,idiag4) = aa(2,2,idiag4) + xnorm_q(4)*p4q2C
            aa(2,3,idiag4) = aa(2,3,idiag4) + xnorm_q(4)*p4q3C
            aa(2,4,idiag4) = aa(2,4,idiag4) + xnorm_q(4)*p4q4C
            aa(2,5,idiag4) = aa(2,5,idiag4) + xnorm_q(4)*p4q5C

            aa(3,1,idiag4) = aa(3,1,idiag4) + ynorm_q(4)*p4q1C
            aa(3,2,idiag4) = aa(3,2,idiag4) + ynorm_q(4)*p4q2C
            aa(3,3,idiag4) = aa(3,3,idiag4) + ynorm_q(4)*p4q3C
            aa(3,4,idiag4) = aa(3,4,idiag4) + ynorm_q(4)*p4q4C
            aa(3,5,idiag4) = aa(3,5,idiag4) + ynorm_q(4)*p4q5C

            aa(4,1,idiag4) = aa(4,1,idiag4) + znorm_q(4)*p4q1C
            aa(4,2,idiag4) = aa(4,2,idiag4) + znorm_q(4)*p4q2C
            aa(4,3,idiag4) = aa(4,3,idiag4) + znorm_q(4)*p4q3C
            aa(4,4,idiag4) = aa(4,4,idiag4) + znorm_q(4)*p4q4C
            aa(4,5,idiag4) = aa(4,5,idiag4) + znorm_q(4)*p4q5C

            aa(5,1,idiag4) = aa(5,1,idiag4) + face_speed_q(4)*p4q1C
            aa(5,2,idiag4) = aa(5,2,idiag4) + face_speed_q(4)*p4q2C
            aa(5,3,idiag4) = aa(5,3,idiag4) + face_speed_q(4)*p4q3C
            aa(5,4,idiag4) = aa(5,4,idiag4) + face_speed_q(4)*p4q4C
            aa(5,5,idiag4) = aa(5,5,idiag4) + face_speed_q(4)*p4q5C
          endif
        endif a_contribs2

        end do loop_quads_01

      end if conditional_01
    end do close_pressure

!  Convert back to conserved variables

    !CHECK
    if (.not.present(eqn_set)) then
      call ptoe(nnodes01,qnode,n_tot,0)
    else
      call ptoe(nnodes01,qnode,n_tot,2)
    end if

! Now do the farfield_riem

    xgm1 = my_1/gm1
    uout = u0
    vout = v0
    wout = w0
    cout = c0
    sout = s0

    do ib = 1, nbound
      if(bc(ib)%ibc == farfield_riem) then
        do i = 1,bc(ib)%nbnode
          inode  = bc(ib)%ibnode(i)
          if(inode <= nnodes0) then

            xnorm = bc(ib)%bxn(i)
            ynorm = bc(ib)%byn(i)
            znorm = bc(ib)%bzn(i)
            area  = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)
            xnorm = xnorm/area
            ynorm = ynorm/area
            znorm = znorm/area

            face_speed = my_0
            if (need_grid_velocity) then
              face_speed = bc(ib)%bfacespeed(i)
            end if

            unormo = uout*xnorm + vout*ynorm + wout*znorm - face_speed

! identify internal conditions

            rhoi   = qnode(1,inode)
              rhoiq1 = my_1
              rhoiq2 = my_0
              rhoiq3 = my_0
              rhoiq4 = my_0
              rhoiq5 = my_0

            ui   = qnode(2,inode)/rhoi
              uiq1 = -qnode(2,inode)*rhoiq1/rhoi/rhoi
              uiq2 = (rhoi-qnode(2,inode)*rhoiq2)/rhoi/rhoi
              uiq3 = -qnode(2,inode)*rhoiq3/rhoi/rhoi
              uiq4 = -qnode(2,inode)*rhoiq4/rhoi/rhoi
              uiq5 = -qnode(2,inode)*rhoiq5/rhoi/rhoi

            vi   = qnode(3,inode)/rhoi
              viq1 = -qnode(3,inode)*rhoiq1/rhoi/rhoi
              viq2 = -qnode(3,inode)*rhoiq2/rhoi/rhoi
              viq3 = (rhoi-qnode(3,inode)*rhoiq3)/rhoi/rhoi
              viq4 = -qnode(3,inode)*rhoiq4/rhoi/rhoi
              viq5 = -qnode(3,inode)*rhoiq5/rhoi/rhoi

            wi   = qnode(4,inode)/rhoi
              wiq1 = -qnode(4,inode)*rhoiq1/rhoi/rhoi
              wiq2 = -qnode(4,inode)*rhoiq2/rhoi/rhoi
              wiq3 = -qnode(4,inode)*rhoiq3/rhoi/rhoi
              wiq4 = (rhoi-qnode(4,inode)*rhoiq4)/rhoi/rhoi
              wiq5 = -qnode(4,inode)*rhoiq5/rhoi/rhoi

            ei   = qnode(5,inode)
              eiq1 = my_0
              eiq2 = my_0
              eiq3 = my_0
              eiq4 = my_0
              eiq5 = my_1

            pi   = gm1*(ei - my_haf*rhoi*(ui*ui + vi*vi + wi*wi))
              piq1 = gm1*(eiq1 - my_haf*(rhoi*(my_2*ui*uiq1 + my_2*vi*viq1     &
                   + my_2*wi*wiq1) + rhoiq1*(ui*ui + vi*vi + wi*wi)))
              piq2 = gm1*(eiq2 - my_haf*(rhoi*(my_2*ui*uiq2 + my_2*vi*viq2     &
                   + my_2*wi*wiq2) + rhoiq2*(ui*ui + vi*vi + wi*wi)))
              piq3 = gm1*(eiq3 - my_haf*(rhoi*(my_2*ui*uiq3 + my_2*vi*viq3     &
                   + my_2*wi*wiq3) + rhoiq3*(ui*ui + vi*vi + wi*wi)))
              piq4 = gm1*(eiq4 - my_haf*(rhoi*(my_2*ui*uiq4 + my_2*vi*viq4     &
                   + my_2*wi*wiq4) + rhoiq4*(ui*ui + vi*vi + wi*wi)))
              piq5 = gm1*(eiq5 - my_haf*(rhoi*(my_2*ui*uiq5 + my_2*vi*viq5     &
                   + my_2*wi*wiq5) + rhoiq5*(ui*ui + vi*vi + wi*wi)))

            unormi = ui*xnorm + vi*ynorm + wi*znorm - face_speed
              unormiq1 = uiq1*xnorm + viq1*ynorm + wiq1*znorm
              unormiq2 = uiq2*xnorm + viq2*ynorm + wiq2*znorm
              unormiq3 = uiq3*xnorm + viq3*ynorm + wiq3*znorm
              unormiq4 = uiq4*xnorm + viq4*ynorm + wiq4*znorm
              unormiq5 = uiq5*xnorm + viq5*ynorm + wiq5*znorm

            a2 = gamma * pi / rhoi
              a2q1 = gamma * (rhoi*piq1 - pi*rhoiq1) / rhoi / rhoi
              a2q2 = gamma * (rhoi*piq2 - pi*rhoiq2) / rhoi / rhoi
              a2q3 = gamma * (rhoi*piq3 - pi*rhoiq3) / rhoi / rhoi
              a2q4 = gamma * (rhoi*piq4 - pi*rhoiq4) / rhoi / rhoi
              a2q5 = gamma * (rhoi*piq5 - pi*rhoiq5) / rhoi / rhoi

            a = sqrt(a2)
              aq1 = my_haf/sqrt(a2)*a2q1
              aq2 = my_haf/sqrt(a2)*a2q2
              aq3 = my_haf/sqrt(a2)*a2q3
              aq4 = my_haf/sqrt(a2)*a2q4
              aq5 = my_haf/sqrt(a2)*a2q5

            ai = a
              aiq1 = aq1
              aiq2 = aq2
              aiq3 = aq3
              aiq4 = aq4
              aiq5 = aq5

!         Calculate R+ and R-
!         Then get the normal velocity and the
!         speed of sound on the boundary

            rplus  = unormi + my_2*a/gm1
              rplusq1 = unormiq1 + my_2/gm1*aq1
              rplusq2 = unormiq2 + my_2/gm1*aq2
              rplusq3 = unormiq3 + my_2/gm1*aq3
              rplusq4 = unormiq4 + my_2/gm1*aq4
              rplusq5 = unormiq5 + my_2/gm1*aq5

            rminus = unormo - my_2*cout/gm1
              rminusq1 = my_0
              rminusq2 = my_0
              rminusq3 = my_0
              rminusq4 = my_0
              rminusq5 = my_0

            if (unormi > c0) then
              rminus = unormi - my_2*a/gm1
                rminusq1 = unormiq1 - my_2/gm1*aq1
                rminusq2 = unormiq2 - my_2/gm1*aq2
                rminusq3 = unormiq3 - my_2/gm1*aq3
                rminusq4 = unormiq4 - my_2/gm1*aq4
                rminusq5 = unormiq5 - my_2/gm1*aq5
            end if

            if (unormi < -c0) then
              rplus = unormo + my_2*cout/gm1
                rplusq1 = my_0
                rplusq2 = my_0
                rplusq3 = my_0
                rplusq4 = my_0
                rplusq5 = my_0
            end if

            unorm = my_haf * (rplus+rminus)
              unormq1 = my_haf * (rplusq1 + rminusq1)
              unormq2 = my_haf * (rplusq2 + rminusq2)
              unormq3 = my_haf * (rplusq3 + rminusq3)
              unormq4 = my_haf * (rplusq4 + rminusq4)
              unormq5 = my_haf * (rplusq5 + rminusq5)

            a = my_4th * gm1 * (rplus-rminus)
              aq1 = my_4th*gm1 * (rplusq1 - rminusq1)
              aq2 = my_4th*gm1 * (rplusq2 - rminusq2)
              aq3 = my_4th*gm1 * (rplusq3 - rminusq3)
              aq4 = my_4th*gm1 * (rplusq4 - rminusq4)
              aq5 = my_4th*gm1 * (rplusq5 - rminusq5)

!         If unorm > 0 this is outflow: take variables from inside
!         If unorm < 0 this is inflow:  take variables from outside

          if (unorm > my_0) then
            u = qnode(2,inode)/qnode(1,inode) + xnorm*(unorm-unormi)
              uq1 = -qnode(2,inode)/qnode(1,inode)/qnode(1,inode)              &
                  + xnorm*(unormq1-unormiq1)
              uq2 = my_1/qnode(1,inode) + xnorm*(unormq2-unormiq2)
              uq3 = xnorm*(unormq3-unormiq3)
              uq4 = xnorm*(unormq4-unormiq4)
              uq5 = xnorm*(unormq5-unormiq5)

            v = qnode(3,inode)/qnode(1,inode) + ynorm*(unorm-unormi)
              vq1 = -qnode(3,inode)/qnode(1,inode)/qnode(1,inode)              &
                  + ynorm*(unormq1-unormiq1)
              vq2 = ynorm*(unormq2-unormiq2)
              vq3 = my_1/qnode(1,inode) + ynorm*(unormq3-unormiq3)
              vq4 = ynorm*(unormq4-unormiq4)
              vq5 = ynorm*(unormq5-unormiq5)

            w = qnode(4,inode)/qnode(1,inode) + znorm*(unorm-unormi)
              wq1 = -qnode(4,inode)/qnode(1,inode)/qnode(1,inode)              &
                  + znorm*(unormq1-unormiq1)
              wq2 = znorm*(unormq2-unormiq2)
              wq3 = znorm*(unormq3-unormiq3)
              wq4 = my_1/qnode(1,inode) + znorm*(unormq4-unormiq4)
              wq5 = znorm*(unormq5-unormiq5)

            s = ai * ai / (gamma*rhoi**gm1)
              sq1 = (gamma*rhoi**gm1*(my_2*ai*aiq1)                            &
                  - ai*ai*(gamma*gm1*rhoi**(gamma-my_2)*rhoiq1))               &
                  / (gamma*rhoi**gm1) / (gamma*rhoi**gm1)
              sq2 = (gamma*rhoi**gm1*(my_2*ai*aiq2)                            &
                  - ai*ai*(gamma*gm1*rhoi**(gamma-my_2)*rhoiq2))               &
                  / (gamma*rhoi**gm1) / (gamma*rhoi**gm1)
              sq3 = (gamma*rhoi**gm1*(my_2*ai*aiq3)                            &
                  - ai*ai*(gamma*gm1*rhoi**(gamma-my_2)*rhoiq3))               &
                  / (gamma*rhoi**gm1) / (gamma*rhoi**gm1)
              sq4 = (gamma*rhoi**gm1*(my_2*ai*aiq4)                            &
                  - ai*ai*(gamma*gm1*rhoi**(gamma-my_2)*rhoiq4))               &
                  / (gamma*rhoi**gm1) / (gamma*rhoi**gm1)
              sq5 = (gamma*rhoi**gm1*(my_2*ai*aiq5)                            &
                  - ai*ai*(gamma*gm1*rhoi**(gamma-my_2)*rhoiq5))               &
                  / (gamma*rhoi**gm1) / (gamma*rhoi**gm1)
          else
            u = uout + xnorm*(unorm-unormo)
              uq1 = xnorm*unormq1
              uq2 = xnorm*unormq2
              uq3 = xnorm*unormq3
              uq4 = xnorm*unormq4
              uq5 = xnorm*unormq5

            v = vout + ynorm*(unorm-unormo)
              vq1 = ynorm*unormq1
              vq2 = ynorm*unormq2
              vq3 = ynorm*unormq3
              vq4 = ynorm*unormq4
              vq5 = ynorm*unormq5

            w = wout + znorm*(unorm-unormo)
              wq1 = znorm*unormq1
              wq2 = znorm*unormq2
              wq3 = znorm*unormq3
              wq4 = znorm*unormq4
              wq5 = znorm*unormq5

            s = sout
              sq1 = my_0
              sq2 = my_0
              sq3 = my_0
              sq4 = my_0
              sq5 = my_0
          end if

          rho  = (a*a/(gamma*s))**xgm1
            rhoq1 = xgm1*(a*a/(gamma*s))**(xgm1-my_1) * (gamma*s*my_2*a*aq1    &
                  - a*a*gamma*sq1)/(gamma*s)/(gamma*s)
            rhoq2 = xgm1*(a*a/(gamma*s))**(xgm1-my_1) * (gamma*s*my_2*a*aq2    &
                  - a*a*gamma*sq2)/(gamma*s)/(gamma*s)
            rhoq3 = xgm1*(a*a/(gamma*s))**(xgm1-my_1) * (gamma*s*my_2*a*aq3    &
                  - a*a*gamma*sq3)/(gamma*s)/(gamma*s)
            rhoq4 = xgm1*(a*a/(gamma*s))**(xgm1-my_1) * (gamma*s*my_2*a*aq4    &
                  - a*a*gamma*sq4)/(gamma*s)/(gamma*s)
            rhoq5 = xgm1*(a*a/(gamma*s))**(xgm1-my_1) * (gamma*s*my_2*a*aq5    &
                  - a*a*gamma*sq5)/(gamma*s)/(gamma*s)

          p = rho * a * a / gamma
            pq1 = my_1/gamma * (rho*my_2*a*aq1 + a*a*rhoq1)
            pq2 = my_1/gamma * (rho*my_2*a*aq2 + a*a*rhoq2)
            pq3 = my_1/gamma * (rho*my_2*a*aq3 + a*a*rhoq3)
            pq4 = my_1/gamma * (rho*my_2*a*aq4 + a*a*rhoq4)
            pq5 = my_1/gamma * (rho*my_2*a*aq5 + a*a*rhoq5)

          e = p/gm1 + my_haf*rho*(u*u + v*v + w*w)
            eq1 = pq1/gm1 + my_haf*(rho*(my_2*u*uq1 + my_2*v*vq1 + my_2*w*wq1) &
                + rhoq1*(u*u + v*v + w*w))
            eq2 = pq2/gm1 + my_haf*(rho*(my_2*u*uq2 + my_2*v*vq2 + my_2*w*wq2) &
                + rhoq2*(u*u + v*v + w*w))
            eq3 = pq3/gm1 + my_haf*(rho*(my_2*u*uq3 + my_2*v*vq3 + my_2*w*wq3) &
                + rhoq3*(u*u + v*v + w*w))
            eq4 = pq4/gm1 + my_haf*(rho*(my_2*u*uq4 + my_2*v*vq4 + my_2*w*wq4) &
                + rhoq4*(u*u + v*v + w*w))
            eq5 = pq5/gm1 + my_haf*(rho*(my_2*u*uq5 + my_2*v*vq5 + my_2*w*wq5) &
                + rhoq5*(u*u + v*v + w*w))

          ubar = xnorm*u + ynorm*v + znorm*w - face_speed
            ubarq1 = xnorm*uq1 + ynorm*vq1 + znorm*wq1
            ubarq2 = xnorm*uq2 + ynorm*vq2 + znorm*wq2
            ubarq3 = xnorm*uq3 + ynorm*vq3 + znorm*wq3
            ubarq4 = xnorm*uq4 + ynorm*vq4 + znorm*wq4
            ubarq5 = xnorm*uq5 + ynorm*vq5 + znorm*wq5

!         res1 = area*rho*ubar
            res1q1 = area*(rho*ubarq1 + ubar*rhoq1)
            res1q2 = area*(rho*ubarq2 + ubar*rhoq2)
            res1q3 = area*(rho*ubarq3 + ubar*rhoq3)
            res1q4 = area*(rho*ubarq4 + ubar*rhoq4)
            res1q5 = area*(rho*ubarq5 + ubar*rhoq5)

!         res2 = area*(rho*u*ubar + xnorm*p)
            res2q1 = area*(rho*(u*ubarq1 + ubar*uq1) + u*ubar*rhoq1 + xnorm*pq1)
            res2q2 = area*(rho*(u*ubarq2 + ubar*uq2) + u*ubar*rhoq2 + xnorm*pq2)
            res2q3 = area*(rho*(u*ubarq3 + ubar*uq3) + u*ubar*rhoq3 + xnorm*pq3)
            res2q4 = area*(rho*(u*ubarq4 + ubar*uq4) + u*ubar*rhoq4 + xnorm*pq4)
            res2q5 = area*(rho*(u*ubarq5 + ubar*uq5) + u*ubar*rhoq5 + xnorm*pq5)

!         res3 = area*(rho*v*ubar + ynorm*p)
            res3q1 = area*(rho*(v*ubarq1 + ubar*vq1) + v*ubar*rhoq1 + ynorm*pq1)
            res3q2 = area*(rho*(v*ubarq2 + ubar*vq2) + v*ubar*rhoq2 + ynorm*pq2)
            res3q3 = area*(rho*(v*ubarq3 + ubar*vq3) + v*ubar*rhoq3 + ynorm*pq3)
            res3q4 = area*(rho*(v*ubarq4 + ubar*vq4) + v*ubar*rhoq4 + ynorm*pq4)
            res3q5 = area*(rho*(v*ubarq5 + ubar*vq5) + v*ubar*rhoq5 + ynorm*pq5)

!         res4 = area*(rho*w*ubar + znorm*p)
            res4q1 = area*(rho*(w*ubarq1 + ubar*wq1) + w*ubar*rhoq1 + znorm*pq1)
            res4q2 = area*(rho*(w*ubarq2 + ubar*wq2) + w*ubar*rhoq2 + znorm*pq2)
            res4q3 = area*(rho*(w*ubarq3 + ubar*wq3) + w*ubar*rhoq3 + znorm*pq3)
            res4q4 = area*(rho*(w*ubarq4 + ubar*wq4) + w*ubar*rhoq4 + znorm*pq4)
            res4q5 = area*(rho*(w*ubarq5 + ubar*wq5) + w*ubar*rhoq5 + znorm*pq5)

!         res5 = area*(e + p)*ubar + area*face_speed*p
            res5q1 = area*((e+p)*ubarq1 + ubar*(eq1+pq1)) + area*face_speed*pq1
            res5q2 = area*((e+p)*ubarq2 + ubar*(eq2+pq2)) + area*face_speed*pq2
            res5q3 = area*((e+p)*ubarq3 + ubar*(eq3+pq3)) + area*face_speed*pq3
            res5q4 = area*((e+p)*ubarq4 + ubar*(eq4+pq4)) + area*face_speed*pq4
            res5q5 = area*((e+p)*ubarq5 + ubar*(eq5+pq5)) + area*face_speed*pq5

          res_contribs3 : if ( fill_res ) then
          if ( rn == inode .and. np == inode ) then
            ad(1,1) = ad(1,1) + coltag(1,inode)*res1q1
            ad(1,2) = ad(1,2) + coltag(1,inode)*res1q2
            ad(1,3) = ad(1,3) + coltag(1,inode)*res1q3
            ad(1,4) = ad(1,4) + coltag(1,inode)*res1q4
            ad(1,5) = ad(1,5) + coltag(1,inode)*res1q5

            ad(2,1) = ad(2,1) + coltag(2,inode)*res2q1
            ad(2,2) = ad(2,2) + coltag(2,inode)*res2q2
            ad(2,3) = ad(2,3) + coltag(2,inode)*res2q3
            ad(2,4) = ad(2,4) + coltag(2,inode)*res2q4
            ad(2,5) = ad(2,5) + coltag(2,inode)*res2q5

            ad(3,1) = ad(3,1) + coltag(3,inode)*res3q1
            ad(3,2) = ad(3,2) + coltag(3,inode)*res3q2
            ad(3,3) = ad(3,3) + coltag(3,inode)*res3q3
            ad(3,4) = ad(3,4) + coltag(3,inode)*res3q4
            ad(3,5) = ad(3,5) + coltag(3,inode)*res3q5

            ad(4,1) = ad(4,1) + coltag(4,inode)*res4q1
            ad(4,2) = ad(4,2) + coltag(4,inode)*res4q2
            ad(4,3) = ad(4,3) + coltag(4,inode)*res4q3
            ad(4,4) = ad(4,4) + coltag(4,inode)*res4q4
            ad(4,5) = ad(4,5) + coltag(4,inode)*res4q5

            ad(5,1) = ad(5,1) + coltag(5,inode)*res5q1
            ad(5,2) = ad(5,2) + coltag(5,inode)*res5q2
            ad(5,3) = ad(5,3) + coltag(5,inode)*res5q3
            ad(5,4) = ad(5,4) + coltag(5,inode)*res5q4
            ad(5,5) = ad(5,5) + coltag(5,inode)*res5q5
          endif

! Now add contribution to lhs

            do j = 1, nfunctions
              rlam1(j) = coltag(1,inode)*rlam(1,inode,j)
              rlam2(j) = coltag(2,inode)*rlam(2,inode,j)
              rlam3(j) = coltag(3,inode)*rlam(3,inode,j)
              rlam4(j) = coltag(4,inode)*rlam(4,inode,j)
              rlam5(j) = coltag(5,inode)*rlam(5,inode,j)
            end do

            do j = 1, nfunctions
             res(1,inode,j)= res(1,inode,j) + res1q1*rlam1(j) + res2q1*rlam2(j)&
                          + res3q1*rlam3(j) + res4q1*rlam4(j) + res5q1*rlam5(j)
             res(2,inode,j)= res(2,inode,j) + res1q2*rlam1(j) + res2q2*rlam2(j)&
                          + res3q2*rlam3(j) + res4q2*rlam4(j) + res5q2*rlam5(j)
             res(3,inode,j)= res(3,inode,j) + res1q3*rlam1(j) + res2q3*rlam2(j)&
                          + res3q3*rlam3(j) + res4q3*rlam4(j) + res5q3*rlam5(j)
             res(4,inode,j)= res(4,inode,j) + res1q4*rlam1(j) + res2q4*rlam2(j)&
                          + res3q4*rlam3(j) + res4q4*rlam4(j) + res5q4*rlam5(j)
             res(5,inode,j)= res(5,inode,j) + res1q5*rlam1(j) + res2q5*rlam2(j)&
                          + res3q5*rlam3(j) + res4q5*rlam4(j) + res5q5*rlam5(j)
            end do
          endif res_contribs3

          a_contribs3 : if ( fill_a ) then
            idiag = iau(inode)

            aa(1,1,idiag) = aa(1,1,idiag) + res1q1
            aa(1,2,idiag) = aa(1,2,idiag) + res1q2
            aa(1,3,idiag) = aa(1,3,idiag) + res1q3
            aa(1,4,idiag) = aa(1,4,idiag) + res1q4
            aa(1,5,idiag) = aa(1,5,idiag) + res1q5

            aa(2,1,idiag) = aa(2,1,idiag) + res2q1
            aa(2,2,idiag) = aa(2,2,idiag) + res2q2
            aa(2,3,idiag) = aa(2,3,idiag) + res2q3
            aa(2,4,idiag) = aa(2,4,idiag) + res2q4
            aa(2,5,idiag) = aa(2,5,idiag) + res2q5

            aa(3,1,idiag) = aa(3,1,idiag) + res3q1
            aa(3,2,idiag) = aa(3,2,idiag) + res3q2
            aa(3,3,idiag) = aa(3,3,idiag) + res3q3
            aa(3,4,idiag) = aa(3,4,idiag) + res3q4
            aa(3,5,idiag) = aa(3,5,idiag) + res3q5

            aa(4,1,idiag) = aa(4,1,idiag) + res4q1
            aa(4,2,idiag) = aa(4,2,idiag) + res4q2
            aa(4,3,idiag) = aa(4,3,idiag) + res4q3
            aa(4,4,idiag) = aa(4,4,idiag) + res4q4
            aa(4,5,idiag) = aa(4,5,idiag) + res4q5

            aa(5,1,idiag) = aa(5,1,idiag) + res5q1
            aa(5,2,idiag) = aa(5,2,idiag) + res5q2
            aa(5,3,idiag) = aa(5,3,idiag) + res5q3
            aa(5,4,idiag) = aa(5,4,idiag) + res5q4
            aa(5,5,idiag) = aa(5,5,idiag) + res5q5
          endif a_contribs3

          endif
        end do
      endif
    end do


! Get any pieces from farfield_extr or farfield_pbck boundaries

    conditional_ext : do ib = 1, nbound

      if(bc(ib)%ibc == farfield_extr .or. bc(ib)%ibc == farfield_pbck) then

        node_loop : do i = 1, bc(ib)%nbnode

          inode  = bc(ib)%ibnode(i)

          local_node : if(inode <= nnodes0) then

            xnorm = bc(ib)%bxn(i)
            ynorm = bc(ib)%byn(i)
            znorm = bc(ib)%bzn(i)
            area  = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)
            xnorm = xnorm/area
            ynorm = ynorm/area
            znorm = znorm/area

            face_speed = my_0

            if ( need_grid_velocity ) then
              face_speed = bc(ib)%bfacespeed(i)
            end if

            rho   = qnode(1,inode)
              rhoq1 = 1.0_dp
              rhoq2 = 0.0_dp
              rhoq3 = 0.0_dp
              rhoq4 = 0.0_dp
              rhoq5 = 0.0_dp

            u     = qnode(2,inode)/rho
              uq1 = -qnode(2,inode)/rho/rho
              uq2 = 1.0_dp/rho
              uq3 = 0.0_dp
              uq4 = 0.0_dp
              uq5 = 0.0_dp

            v     = qnode(3,inode)/rho
              vq1 = -qnode(3,inode)/rho/rho
              vq2 = 0.0_dp
              vq3 = 1.0_dp/rho
              vq4 = 0.0_dp
              vq5 = 0.0_dp

            w     = qnode(4,inode)/rho
              wq1 = -qnode(4,inode)/rho/rho
              wq2 = 0.0_dp
              wq3 = 0.0_dp
              wq4 = 1.0_dp/rho
              wq5 = 0.0_dp

            ubar = xnorm*u + ynorm*v + znorm*w - face_speed
              ubarq1 = xnorm*uq1 + ynorm*vq1 + znorm*wq1
              ubarq2 = xnorm*uq2 + ynorm*vq2 + znorm*wq2
              ubarq3 = xnorm*uq3 + ynorm*vq3 + znorm*wq3
              ubarq4 = xnorm*uq4 + ynorm*vq4 + znorm*wq4
              ubarq5 = xnorm*uq5 + ynorm*vq5 + znorm*wq5

            e  = qnode(5,inode)
              eq1 = 0.0_dp
              eq2 = 0.0_dp
              eq3 = 0.0_dp
              eq4 = 0.0_dp
              eq5 = 1.0_dp

            p = gm1*(e - my_haf*rho*(u*u+v*v+w*w))
            a2 = gamma * p / rho
            a  = sqrt(a2)

            if (bc(ib)%ibc == farfield_pbck) then

              if ( ubar/a < my_1 ) then
                p = pback
                  pq1 = 0.0_dp
                  pq2 = 0.0_dp
                  pq3 = 0.0_dp
                  pq4 = 0.0_dp
                  pq5 = 0.0_dp
                e   = p/gm1 + my_haf*rho*(u*u+v*v+w*w)
                  eq1 = pq1/gm1 + my_haf*(rho*(2.0_dp*u*uq1 + 2.0_dp*v*vq1     &
                                         + 2.0_dp*w*wq1) + (u*u+v*v+w*w)*rhoq1)
                  eq2 = pq2/gm1 + my_haf*(rho*(2.0_dp*u*uq2 + 2.0_dp*v*vq2     &
                                         + 2.0_dp*w*wq2) + (u*u+v*v+w*w)*rhoq2)
                  eq3 = pq3/gm1 + my_haf*(rho*(2.0_dp*u*uq3 + 2.0_dp*v*vq3     &
                                         + 2.0_dp*w*wq3) + (u*u+v*v+w*w)*rhoq3)
                  eq4 = pq4/gm1 + my_haf*(rho*(2.0_dp*u*uq4 + 2.0_dp*v*vq4     &
                                         + 2.0_dp*w*wq4) + (u*u+v*v+w*w)*rhoq4)
                  eq5 = pq5/gm1 + my_haf*(rho*(2.0_dp*u*uq5 + 2.0_dp*v*vq5     &
                                         + 2.0_dp*w*wq5) + (u*u+v*v+w*w)*rhoq5)
              else
                p = gm1*(e - my_haf*rho*(u*u+v*v+w*w))
                  pq1 = gm1*(eq1 - my_haf*(rho*(2.0_dp*u*uq1                   &
                      + 2.0_dp*v*vq1 + 2.0_dp*w*wq1) + (u*u+v*v+w*w)*rhoq1))
                  pq2 = gm1*(eq2 - my_haf*(rho*(2.0_dp*u*uq2                   &
                      + 2.0_dp*v*vq2 + 2.0_dp*w*wq2) + (u*u+v*v+w*w)*rhoq2))
                  pq3 = gm1*(eq3 - my_haf*(rho*(2.0_dp*u*uq3                   &
                      + 2.0_dp*v*vq3 + 2.0_dp*w*wq3) + (u*u+v*v+w*w)*rhoq3))
                  pq4 = gm1*(eq4 - my_haf*(rho*(2.0_dp*u*uq4                   &
                      + 2.0_dp*v*vq4 + 2.0_dp*w*wq4) + (u*u+v*v+w*w)*rhoq4))
                  pq5 = gm1*(eq5 - my_haf*(rho*(2.0_dp*u*uq5                   &
                      + 2.0_dp*v*vq5 + 2.0_dp*w*wq5) + (u*u+v*v+w*w)*rhoq5))
              endif

            else

              p   = gm1*(e - my_haf*rho*(u*u+v*v+w*w))
                pq1 = gm1*(eq1 - my_haf*(rho*(2.0_dp*u*uq1                     &
                    + 2.0_dp*v*vq1 + 2.0_dp*w*wq1) + (u*u+v*v+w*w)*rhoq1))
                pq2 = gm1*(eq2 - my_haf*(rho*(2.0_dp*u*uq2                     &
                    + 2.0_dp*v*vq2 + 2.0_dp*w*wq2) + (u*u+v*v+w*w)*rhoq2))
                pq3 = gm1*(eq3 - my_haf*(rho*(2.0_dp*u*uq3                     &
                    + 2.0_dp*v*vq3 + 2.0_dp*w*wq3) + (u*u+v*v+w*w)*rhoq3))
                pq4 = gm1*(eq4 - my_haf*(rho*(2.0_dp*u*uq4                     &
                    + 2.0_dp*v*vq4 + 2.0_dp*w*wq4) + (u*u+v*v+w*w)*rhoq4))
                pq5 = gm1*(eq5 - my_haf*(rho*(2.0_dp*u*uq5                     &
                    + 2.0_dp*v*vq5 + 2.0_dp*w*wq5) + (u*u+v*v+w*w)*rhoq5))

            end if

!           res1 = area*rho*ubar
              res1q1 = area*(rho*ubarq1 + ubar*rhoq1)
              res1q2 = area*(rho*ubarq2 + ubar*rhoq2)
              res1q3 = area*(rho*ubarq3 + ubar*rhoq3)
              res1q4 = area*(rho*ubarq4 + ubar*rhoq4)
              res1q5 = area*(rho*ubarq5 + ubar*rhoq5)

!           res2 = area*(rho*u*ubar + xnorm*p)
              res2q1 = area*( rho*(u*ubarq1+ubar*uq1)+u*ubar*rhoq1 + xnorm*pq1 )
              res2q2 = area*( rho*(u*ubarq2+ubar*uq2)+u*ubar*rhoq2 + xnorm*pq2 )
              res2q3 = area*( rho*(u*ubarq3+ubar*uq3)+u*ubar*rhoq3 + xnorm*pq3 )
              res2q4 = area*( rho*(u*ubarq4+ubar*uq4)+u*ubar*rhoq4 + xnorm*pq4 )
              res2q5 = area*( rho*(u*ubarq5+ubar*uq5)+u*ubar*rhoq5 + xnorm*pq5 )

!           res3 = area*(rho*v*ubar + ynorm*p)
              res3q1 = area*( rho*(v*ubarq1+ubar*vq1)+v*ubar*rhoq1 + ynorm*pq1 )
              res3q2 = area*( rho*(v*ubarq2+ubar*vq2)+v*ubar*rhoq2 + ynorm*pq2 )
              res3q3 = area*( rho*(v*ubarq3+ubar*vq3)+v*ubar*rhoq3 + ynorm*pq3 )
              res3q4 = area*( rho*(v*ubarq4+ubar*vq4)+v*ubar*rhoq4 + ynorm*pq4 )
              res3q5 = area*( rho*(v*ubarq5+ubar*vq5)+v*ubar*rhoq5 + ynorm*pq5 )

!           res4 = area*(rho*w*ubar + znorm*p)
              res4q1 = area*( rho*(w*ubarq1+ubar*wq1)+w*ubar*rhoq1 + znorm*pq1 )
              res4q2 = area*( rho*(w*ubarq2+ubar*wq2)+w*ubar*rhoq2 + znorm*pq2 )
              res4q3 = area*( rho*(w*ubarq3+ubar*wq3)+w*ubar*rhoq3 + znorm*pq3 )
              res4q4 = area*( rho*(w*ubarq4+ubar*wq4)+w*ubar*rhoq4 + znorm*pq4 )
              res4q5 = area*( rho*(w*ubarq5+ubar*wq5)+w*ubar*rhoq5 + znorm*pq5 )

!           res5 = area*(e + p)*ubar + area*face_speed*p
              res5q1= area*((e+p)*ubarq1 + ubar*(eq1+pq1)) + area*face_speed*pq1
              res5q2= area*((e+p)*ubarq2 + ubar*(eq2+pq2)) + area*face_speed*pq2
              res5q3= area*((e+p)*ubarq3 + ubar*(eq3+pq3)) + area*face_speed*pq3
              res5q4= area*((e+p)*ubarq4 + ubar*(eq4+pq4)) + area*face_speed*pq4
              res5q5= area*((e+p)*ubarq5 + ubar*(eq5+pq5)) + area*face_speed*pq5

        res_contribs4 : if ( fill_res ) then
          if ( rn == inode .and. np == inode ) then
            ad(1,1) = ad(1,1) + coltag(1,inode)*res1q1
            ad(1,2) = ad(1,2) + coltag(1,inode)*res1q2
            ad(1,3) = ad(1,3) + coltag(1,inode)*res1q3
            ad(1,4) = ad(1,4) + coltag(1,inode)*res1q4
            ad(1,5) = ad(1,5) + coltag(1,inode)*res1q5

            ad(2,1) = ad(2,1) + coltag(2,inode)*res2q1
            ad(2,2) = ad(2,2) + coltag(2,inode)*res2q2
            ad(2,3) = ad(2,3) + coltag(2,inode)*res2q3
            ad(2,4) = ad(2,4) + coltag(2,inode)*res2q4
            ad(2,5) = ad(2,5) + coltag(2,inode)*res2q5

            ad(3,1) = ad(3,1) + coltag(3,inode)*res3q1
            ad(3,2) = ad(3,2) + coltag(3,inode)*res3q2
            ad(3,3) = ad(3,3) + coltag(3,inode)*res3q3
            ad(3,4) = ad(3,4) + coltag(3,inode)*res3q4
            ad(3,5) = ad(3,5) + coltag(3,inode)*res3q5

            ad(4,1) = ad(4,1) + coltag(4,inode)*res4q1
            ad(4,2) = ad(4,2) + coltag(4,inode)*res4q2
            ad(4,3) = ad(4,3) + coltag(4,inode)*res4q3
            ad(4,4) = ad(4,4) + coltag(4,inode)*res4q4
            ad(4,5) = ad(4,5) + coltag(4,inode)*res4q5

            ad(5,1) = ad(5,1) + coltag(5,inode)*res5q1
            ad(5,2) = ad(5,2) + coltag(5,inode)*res5q2
            ad(5,3) = ad(5,3) + coltag(5,inode)*res5q3
            ad(5,4) = ad(5,4) + coltag(5,inode)*res5q4
            ad(5,5) = ad(5,5) + coltag(5,inode)*res5q5
          endif

            do j = 1, nfunctions
              rlam1(j) = coltag(1,inode)*rlam(1,inode,j)
              rlam2(j) = coltag(2,inode)*rlam(2,inode,j)
              rlam3(j) = coltag(3,inode)*rlam(3,inode,j)
              rlam4(j) = coltag(4,inode)*rlam(4,inode,j)
              rlam5(j) = coltag(5,inode)*rlam(5,inode,j)
            end do

            fcn_loop : do j = 1, nfunctions
              res(1,inode,j) = res(1,inode,j) + res1q1*rlam1(j)                &
                                              + res2q1*rlam2(j)                &
                                              + res3q1*rlam3(j)                &
                                              + res4q1*rlam4(j)                &
                                              + res5q1*rlam5(j)

              res(2,inode,j) = res(2,inode,j) + res1q2*rlam1(j)                &
                                              + res2q2*rlam2(j)                &
                                              + res3q2*rlam3(j)                &
                                              + res4q2*rlam4(j)                &
                                              + res5q2*rlam5(j)

              res(3,inode,j) = res(3,inode,j) + res1q3*rlam1(j)                &
                                              + res2q3*rlam2(j)                &
                                              + res3q3*rlam3(j)                &
                                              + res4q3*rlam4(j)                &
                                              + res5q3*rlam5(j)

              res(4,inode,j) = res(4,inode,j) + res1q4*rlam1(j)                &
                                              + res2q4*rlam2(j)                &
                                              + res3q4*rlam3(j)                &
                                              + res4q4*rlam4(j)                &
                                              + res5q4*rlam5(j)

              res(5,inode,j) = res(5,inode,j) + res1q5*rlam1(j)                &
                                              + res2q5*rlam2(j)                &
                                              + res3q5*rlam3(j)                &
                                              + res4q5*rlam4(j)                &
                                              + res5q5*rlam5(j)
            end do fcn_loop
        endif res_contribs4

        a_contribs4 : if ( fill_a ) then
            idiag = iau(inode)

            aa(1,1,idiag) = aa(1,1,idiag) + res1q1
            aa(1,2,idiag) = aa(1,2,idiag) + res1q2
            aa(1,3,idiag) = aa(1,3,idiag) + res1q3
            aa(1,4,idiag) = aa(1,4,idiag) + res1q4
            aa(1,5,idiag) = aa(1,5,idiag) + res1q5

            aa(2,1,idiag) = aa(2,1,idiag) + res2q1
            aa(2,2,idiag) = aa(2,2,idiag) + res2q2
            aa(2,3,idiag) = aa(2,3,idiag) + res2q3
            aa(2,4,idiag) = aa(2,4,idiag) + res2q4
            aa(2,5,idiag) = aa(2,5,idiag) + res2q5

            aa(3,1,idiag) = aa(3,1,idiag) + res3q1
            aa(3,2,idiag) = aa(3,2,idiag) + res3q2
            aa(3,3,idiag) = aa(3,3,idiag) + res3q3
            aa(3,4,idiag) = aa(3,4,idiag) + res3q4
            aa(3,5,idiag) = aa(3,5,idiag) + res3q5

            aa(4,1,idiag) = aa(4,1,idiag) + res4q1
            aa(4,2,idiag) = aa(4,2,idiag) + res4q2
            aa(4,3,idiag) = aa(4,3,idiag) + res4q3
            aa(4,4,idiag) = aa(4,4,idiag) + res4q4
            aa(4,5,idiag) = aa(4,5,idiag) + res4q5

            aa(5,1,idiag) = aa(5,1,idiag) + res5q1
            aa(5,2,idiag) = aa(5,2,idiag) + res5q2
            aa(5,3,idiag) = aa(5,3,idiag) + res5q3
            aa(5,4,idiag) = aa(5,4,idiag) + res5q4
            aa(5,5,idiag) = aa(5,5,idiag) + res5q5
        endif a_contribs4

          endif local_node

        end do node_loop

      endif

    end do conditional_ext

  end subroutine atlam_bc


!================================ ENFORCE_LAM_STRONG_BC ======================80
!
! Set diag for strong laminar bc.
!
! Note: The block jacobian matrices are not yet transposed.
!
! Expects qnode in PRIMITIVE variables
! Returns qnode in PRIMITIVE variables
!
!=============================================================================80

  subroutine enforce_lam_strong_bc(nnodes01,nnodes0,nnz,adim,A,iau2,nbound,bc, &
                                   symmetry)

    use kinddefs,            only : dp
    use fluid,               only : ggm1
    use bc_types,            only : bcgrid_type
    use bc_names,            only : bc_strong_viscous_adjoint, twall
    use grid_motion_helpers, only : need_grid_velocity
    use flux_symmetry,       only : has_x_symmetry, has_y_symmetry,            &
                                    has_z_symmetry

    integer,                              intent(in)    :: nnodes01, nnodes0
    integer,                              intent(in)    :: nnz, adim, nbound
    real(dp), dimension(adim,adim,nnz),   intent(inout) :: A
    integer,  dimension(nnodes01),        intent(in)    :: iau2, symmetry
    type(bcgrid_type), dimension(nbound), intent(in)    :: bc

    integer :: i, icolumn
    integer :: ib, node

    real(dp) :: res2q1,res2q2,res2q3,res2q4,res2q5
    real(dp) :: res3q1,res3q2,res3q3,res3q4,res3q5
    real(dp) :: res4q1,res4q2,res4q3,res4q4,res4q5
    real(dp) :: res5q1,res5q2,res5q3,res5q4,res5q5
    real(dp) :: uwall,vwall,wwall

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp

  continue

! Account for strong viscous BC but be careful to remember that symmetry takes
! precedence.  The symmetry BC's for non-viscous walls are taken care of later.

    do ib = 1,nbound

      strong_boundary : if( bc_strong_viscous_adjoint( bc(ib)%ibc ) ) then

        do i = 1,bc(ib)%nbnode
          node = bc(ib)%ibnode(i)
          if(node <= nnodes0) then

            if ( need_grid_velocity ) then
              uwall = bc(ib)%bdxdt(i)
              vwall = bc(ib)%bdydt(i)
              wwall = bc(ib)%bdzdt(i)
            else
              uwall = my_0
              vwall = my_0
              wwall = my_0
            endif

            if ( has_x_symmetry(symmetry(node)) ) then
              res2q1 = my_0
              res2q2 = my_1
              res2q3 = my_0
              res2q4 = my_0
              res2q5 = my_0
            else
              res2q1 = -uwall
              res2q2 = my_1
              res2q3 = my_0
              res2q4 = my_0
              res2q5 = my_0
            endif

            if ( has_y_symmetry(symmetry(node)) ) then
              res3q1 = my_0
              res3q2 = my_0
              res3q3 = my_1
              res3q4 = my_0
              res3q5 = my_0
            else
              res3q1 = -vwall
              res3q2 = my_0
              res3q3 = my_1
              res3q4 = my_0
              res3q5 = my_0
            endif

            if ( has_z_symmetry(symmetry(node)) ) then
              res4q1 = my_0
              res4q2 = my_0
              res4q3 = my_0
              res4q4 = my_1
              res4q5 = my_0
            else
              res4q1 = -wwall
              res4q2 = my_0
              res4q3 = my_0
              res4q4 = my_1
              res4q5 = my_0
            endif

            res5q1 = -twall/ggm1-my_haf*(uwall*uwall+vwall*vwall+wwall*wwall)
            res5q2 = my_0
            res5q3 = my_0
            res5q4 = my_0
            res5q5 = my_1

            icolumn = iau2(node)

            A(2,1,icolumn) = res2q1
            A(2,2,icolumn) = res2q2
            A(2,3,icolumn) = res2q3
            A(2,4,icolumn) = res2q4
            A(2,5,icolumn) = res2q5

            A(3,1,icolumn) = res3q1
            A(3,2,icolumn) = res3q2
            A(3,3,icolumn) = res3q3
            A(3,4,icolumn) = res3q4
            A(3,5,icolumn) = res3q5

            A(4,1,icolumn) = res4q1
            A(4,2,icolumn) = res4q2
            A(4,3,icolumn) = res4q3
            A(4,4,icolumn) = res4q4
            A(4,5,icolumn) = res4q5

            A(5,1,icolumn) = res5q1
            A(5,2,icolumn) = res5q2
            A(5,3,icolumn) = res5q3
            A(5,4,icolumn) = res5q4
            A(5,5,icolumn) = res5q5
          endif
        end do

      endif strong_boundary

    end do

  end subroutine enforce_lam_strong_bc

!================================ ENFORCE_TURB_STRONG_BC =====================80
!
! Set diag for strong turbulent bc.
!
! Note: The block jacobian matrices are not yet transposed.
!
! Expects qnode in PRIMITIVE variables
! Returns qnode in PRIMITIVE variables
!
!=============================================================================80

  subroutine enforce_turb_strong_bc(nnodes01,nnodes0,nnz,adim,A,iau2,nbound,bc,&
                                    symmetry)

    use kinddefs,            only : dp
    use fluid,               only : ggm1
    use bc_types,            only : bcgrid_type
    use bc_names,            only : bc_strong_viscous_adjoint, twall
    use grid_motion_helpers, only : need_grid_velocity
    use flux_symmetry,       only : has_x_symmetry, has_y_symmetry,            &
                                    has_z_symmetry

    integer,                              intent(in)    :: nnodes01, nnodes0
    integer,                              intent(in)    :: nnz, adim, nbound
    real(dp), dimension(adim,adim,nnz),   intent(inout) :: A
    integer,  dimension(nnodes01),        intent(in)    :: iau2, symmetry
    type(bcgrid_type), dimension(nbound), intent(in)    :: bc

    integer :: i, icolumn
    integer :: ib, node

    real(dp) :: res2q1,res2q2,res2q3,res2q4,res2q5
    real(dp) :: res3q1,res3q2,res3q3,res3q4,res3q5
    real(dp) :: res4q1,res4q2,res4q3,res4q4,res4q5
    real(dp) :: res5q1,res5q2,res5q3,res5q4,res5q5
    real(dp) :: uwall,vwall,wwall

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp

  continue

! Account for strong viscous BC but be careful to remember that symmetry takes
! precedence.  The symmetry BC's for non-viscous walls are taken care of later.

    set_strong_bc_diag : do ib = 1,nbound

      strong_boundary : if(bc_strong_viscous_adjoint(bc(ib)%ibc)) then

        do i = 1,bc(ib)%nbnode
          node = bc(ib)%ibnode(i)
          if(node <= nnodes0) then

            if ( need_grid_velocity ) then   ! Moving solid viscous wall
              uwall = bc(ib)%bdxdt(i)
              vwall = bc(ib)%bdydt(i)
              wwall = bc(ib)%bdzdt(i)
            else
              uwall = my_0
              vwall = my_0
              wwall = my_0
            endif

            if ( has_x_symmetry(symmetry(node)) ) then
              res2q1 = my_0
              res2q2 = my_1
              res2q3 = my_0
              res2q4 = my_0
              res2q5 = my_0
            else
              res2q1 = -uwall
              res2q2 = my_1
              res2q3 = my_0
              res2q4 = my_0
              res2q5 = my_0
            endif

            if ( has_y_symmetry(symmetry(node)) ) then
              res3q1 = my_0
              res3q2 = my_0
              res3q3 = my_1
              res3q4 = my_0
              res3q5 = my_0
            else
              res3q1 = -vwall
              res3q2 = my_0
              res3q3 = my_1
              res3q4 = my_0
              res3q5 = my_0
            endif

            if ( has_z_symmetry(symmetry(node)) ) then
              res4q1 = my_0
              res4q2 = my_0
              res4q3 = my_0
              res4q4 = my_1
              res4q5 = my_0
            else
              res4q1 = -wwall
              res4q2 = my_0
              res4q3 = my_0
              res4q4 = my_1
              res4q5 = my_0
            endif

            res5q1 = -twall/ggm1-my_haf*(uwall*uwall+vwall*vwall+wwall*wwall)
            res5q2 = my_0
            res5q3 = my_0
            res5q4 = my_0
            res5q5 = my_1

            icolumn = iau2(node)

            A(2,1,icolumn) = res2q1
            A(2,2,icolumn) = res2q2
            A(2,3,icolumn) = res2q3
            A(2,4,icolumn) = res2q4
            A(2,5,icolumn) = res2q5
            A(2,6,icolumn) = my_0

            A(3,1,icolumn) = res3q1
            A(3,2,icolumn) = res3q2
            A(3,3,icolumn) = res3q3
            A(3,4,icolumn) = res3q4
            A(3,5,icolumn) = res3q5
            A(3,6,icolumn) = my_0

            A(4,1,icolumn) = res4q1
            A(4,2,icolumn) = res4q2
            A(4,3,icolumn) = res4q3
            A(4,4,icolumn) = res4q4
            A(4,5,icolumn) = res4q5
            A(4,6,icolumn) = my_0

            A(5,1,icolumn) = res5q1
            A(5,2,icolumn) = res5q2
            A(5,3,icolumn) = res5q3
            A(5,4,icolumn) = res5q4
            A(5,5,icolumn) = res5q5
            A(5,6,icolumn) = my_0

            A(6,1,icolumn) = my_0
            A(6,2,icolumn) = my_0
            A(6,3,icolumn) = my_0
            A(6,4,icolumn) = my_0
            A(6,5,icolumn) = my_0
            A(6,6,icolumn) = my_1
          endif
        end do

      endif strong_boundary

    end do set_strong_bc_diag

  end subroutine enforce_turb_strong_bc

end module residual_bc

!================================= LSQ_LC_MAX ================================80
!
! Maximum of local weighted coordinate.
!
!=============================================================================80

  pure function lsq_lc_max( n, lc, face_wsq )

    use twod_util,     only : q_2d
    use lsq_constants, only : wflsq1, wflsq2

    integer,                  intent(in) :: n
    real(dp), dimension(4,n), intent(in) :: lc
    real(dp), dimension(n),   intent(in), optional :: face_wsq

    integer                :: i
    real(dp)               :: wsq, xie, eta, zie, inv_distancesq, d2i_max
    real(dp), dimension(4) :: lsq_lc_max

  continue

    lsq_lc_max(:) = 0._dp

    d2i_max = 0._dp
    do i=1,n
      !...inverse distance weighting
      inv_distancesq = lc(4,i)
      d2i_max = max( d2i_max , inv_distancesq )
    enddo
    lsq_lc_max(4) = d2i_max

    do i=1,n
      xie            = lc(1,i)
      eta            = lc(2,i)
      zie            = lc(3,i)
      inv_distancesq = lc(4,i)
      wsq = wflsq1*1._dp + wflsq2*inv_distancesq/d2i_max
      if ( present( face_wsq ) ) then
        wsq = face_wsq(i)
      endif
      lsq_lc_max(1) = max( lsq_lc_max(1) , wsq*( xie**2 ) )
      lsq_lc_max(2) = max( lsq_lc_max(2) , wsq*( eta**2 ) )
      lsq_lc_max(3) = max( lsq_lc_max(3) , wsq*( zie**2 ) )
    enddo
    lsq_lc_max(1) = sqrt( lsq_lc_max(1) )
    lsq_lc_max(2) = sqrt( lsq_lc_max(2) )
    lsq_lc_max(3) = sqrt( lsq_lc_max(3) )

    if ( q_2d ) lsq_lc_max(3) = 1._dp

  end function lsq_lc_max

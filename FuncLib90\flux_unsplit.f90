!================================= FLUX_UNSPLIT ==============================80
!
! Unsplit flux...ubar_null enforces tangency explicity.
! Note this function uses primitive variables
!
!=============================================================================80

  pure function flux_unsplit( xnorm, ynorm, znorm, area, face_speed, ql,       &
                              enrgyl, n_species, ndim,                         &
                              n_momx, n_momy, n_momz, n_etot,                  &
                              n_energy, n_energy_last, n_turb_g, n_turb_ke,    &
                              n_dis_nutl, betat, pel, i_electron, couple,      &
                              ubar_null )

    use kinddefs,        only : dp

    integer,                     intent(in) :: ndim, n_momx, n_momy,           &
                                               n_momz, n_etot, n_species,      &
                                               n_energy, n_energy_last,        &
                                               n_turb_g, n_turb_ke, n_dis_nutl,&
                                               i_electron
    real(dp),                    intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                    intent(in) :: face_speed
    real(dp),                    intent(in) :: enrgyl
    real(dp),                    intent(in) :: pel
    real(dp),                    intent(in) :: betat
    real(dp), dimension(ndim),   intent(in) :: ql
    logical,                     intent(in) :: couple, ubar_null

    real(dp), dimension(ndim)             :: flux_unsplit

    real(dp) :: rhol, pressl, pressl_tot, ul, vl, wl, ubarl

    real(dp), dimension(ndim) :: fluxl

    integer :: i

  continue

    if(n_species == 1)then
      rhol   = ql(1)
    else
      rhol   = sum(ql(1:n_species))
    end if

    ul     = ql(n_momx)
    vl     = ql(n_momy)
    wl     = ql(n_momz)
    pressl = ql(n_etot)
    pressl_tot = pressl

    if ( .not.ubar_null ) then
      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed
    else
      ubarl = 0._dp
    endif

    !   vib-elec energy flux terms if present

    if(n_energy > 1)then
      fluxl(n_energy_last) = ubarl*rhol*ql(n_energy_last)
      if(i_electron > 0)then
        fluxl(n_energy_last) = fluxl(n_energy_last) &
                             + ubarl*pel + face_speed*pel
      end if
    end if

    if( couple .and. n_turb_g > 0 )then
      fluxl(n_dis_nutl) = ubarl*rhol*ql(n_dis_nutl)
      if(n_turb_g > 1)then
        fluxl(n_turb_ke) = ubarl*rhol*ql(n_turb_ke)
        if(betat > 0._dp)then
          pressl_tot = pressl + betat*rhol*ql(n_turb_ke)
        end if
      end if
    end if

    fluxl(n_momx) = ubarl*rhol*ul + xnorm*pressl_tot
    fluxl(n_momy) = ubarl*rhol*vl + ynorm*pressl_tot
    fluxl(n_momz) = ubarl*rhol*wl + znorm*pressl_tot
    fluxl(n_etot) = ubarl*(enrgyl+pressl_tot) + face_speed*pressl_tot

!   Compute the contribution to the flux balance

    if(n_species == 1)then
      fluxl(1) = ubarl*rhol
      flux_unsplit(1:ndim) = area*fluxl(1:ndim)
    else
      do i = 1,n_species
        flux_unsplit(i) = area*ubarl*ql(i)
      end do
      flux_unsplit(n_momx:ndim) = area*fluxl(n_momx:ndim)
    end if

  end function flux_unsplit

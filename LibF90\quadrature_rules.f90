! to define quadrature rules for integrating functions
module quadrature_rules

  use kinddefs, only : dp

  implicit none

  private

  public :: triangle01_nq, triangle01_weight
  public :: triangle01_bary1, triangle01_bary2, triangle01_bary3

  real(dp), parameter    :: third   = 1.0_dp/3.0_dp
  real(dp), parameter    :: my_1    = 1.0_dp

! Order 1
  integer, parameter :: triangle01_nq = 1
  real(dp), parameter, dimension(triangle01_nq)    :: triangle01_bary1 = &
    (/ third /)
  real(dp), parameter, dimension(triangle01_nq)    :: triangle01_bary2 = &
    (/ third /)
  real(dp), parameter, dimension(triangle01_nq)    :: triangle01_bary3 = &
    (/ third /)
  real(dp), parameter, dimension(triangle01_nq)    :: triangle01_weight = &
    (/ my_1 /)

end module quadrature_rules

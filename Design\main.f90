program main

  use analysis,           only : perform_analysis, analysis_counter
  use sensitivity,        only : sensitivity_counter
  use lmpi,               only : lmpi_finalize
  use ammo,               only : optimization_driver, design_sanity
  use design_types,       only : max_string_length, sleep_delay, setup_design
  use designs,            only : set_up_model_files, load_optimization_data,   &
                                 setup_design_structure, get_input_data,       &
                                 load_body_grouping_data
  use system_extensions,  only : se_chdir, se_flush
  use command_line_parser,only : usage, read_command_line
  use code_status,        only : code_id, design_code_id
  use allocations,        only : my_alloc_ptr
  use nml_design,         only : read_nml_design, base_directory, what_to_do,  &
                                 ammo_directory, restart_optimization,         &
                                 n_design_pts, body_grouping, model_variables

  implicit none

  character(len=80)                :: project
  character(len=20)                :: grid_format, data_format
  character(len=max_string_length) :: output_file

  integer :: imodel                  ! Working model index
  integer :: io = 43                 ! Output file
  integer :: eqn_set
  integer :: ivisc, itime, ncyc, nslices

continue

  code_id = design_code_id

! Get any command line options

  call usage()

  call read_command_line()

  if ( setup_design ) then
    call setup_design_structure()
    call lmpi_finalize
    stop
  endif

  write(*,*) 'Sleep delay (in seconds) between code executions is set to ',    &
              sleep_delay
  call se_flush(6)

! Initialize design-related stuff

  call read_nml_design()

! Open the output file

  output_file = trim(ammo_directory) // 'ammo.output'
  open(unit=io,file=output_file)

! Allocate counters

  call my_alloc_ptr(analysis_counter, n_design_pts);    analysis_counter = 0
  call my_alloc_ptr(sensitivity_counter, n_design_pts); sensitivity_counter = 0

! Set up the model files in each model directory
! Load in the optimization data for that model

  baseline_stuff : do imodel = 1, n_design_pts

    call se_chdir(model_variables(imodel)%model_directory)

    if ( .not.restart_optimization ) then
      call set_up_model_files(model_variables(imodel))
    endif

    if ( imodel == 1 ) then
      call se_chdir('Flow')
      call get_input_data(project,eqn_set,ivisc,itime,ncyc,grid_format,        &
                          data_format,nslices)
      call se_chdir('..')
    endif

    if ( .not.restart_optimization .or. itime /= 0 ) then
      model_variables(imodel)%restart_flow = 0
      model_variables(imodel)%restart_dual = 0
    else
      model_variables(imodel)%restart_flow = 1
      model_variables(imodel)%restart_dual = 1
    endif

    if ( body_grouping .and. imodel == 1 ) call load_body_grouping_data()

    model_variables(imodel)%opt_data%allocated = .false.
    call load_optimization_data(model_variables(imodel)%opt_data,io,           &
                                'MAIN: Location 1')
    call se_chdir(trim(base_directory))

  end do baseline_stuff

! Perform some sanity checking

  call design_sanity()

! Now start the optimization

  call optimization_driver(io)

  do imodel = 1, n_design_pts
    call se_chdir(model_variables(imodel)%model_directory)
    call load_optimization_data(model_variables(imodel)%opt_data,io,           &
                                'MAIN: Location 2')
  end do
  call se_chdir(trim(base_directory))

! Just in case the optimizer backtracked as its last step, let's
! perform one last analysis at the final DV values to make sure
! we've got aero answers at the final DV's

  write(*,*) 'Performing final analysis at optimal point...'
  call se_flush(6)

  do imodel = 1, n_design_pts
    call se_chdir(model_variables(imodel)%model_directory)
    call perform_analysis(model_variables(imodel)%restart_flow,                &
                          imodel,n_design_pts,what_to_do,restart_optimization, &
                          model_variables(imodel)%desc_directory)
  end do

  close(io)

end program main

module sampling_main

  use kinddefs, only : dp

  implicit none

  private

  public :: survey
  public :: read_nml_sampling_parameters
  public :: initialize_type

contains

!==================================== SURVEY =================================80
!
! Extract flowfield data onto user-specified surfaces - sphere, box, plane, etc
!
!=============================================================================80

  subroutine survey( grid, soln, time_step, nml_path, sadj )

    use lmpi,                only : lmpi_master
    use grid_types,          only : grid_type
    use solution_types,      only : soln_type
    use solution_adj,        only : sadj_type
    use nml_sampling_output, only : read_nml_sampling_output,                  &
                                    need_turb_variables_m=>need_turb_variables,&
                                    need_gradients_m=>need_gradients,          &
                                    need_slen_m=>need_slen,                    &
                                    n_output_variables_m=>n_output_variables,  &
                                    output_variables_m=>output_variables
    use sampling_templates,  only : survey_template
    use sampling_output,     only : survey_write
    use sampling_headers,    only : verbose, init                              &
                                  , n_output_variables, output_variables       &
                                  , need_gradients, need_slen, project_name    &
                                  , need_turb_variables

    type(grid_type),           intent(in)    :: grid
    type(soln_type),           intent(inout) :: soln
    integer,                   intent(in)    :: time_step
    character(*),              intent(in)    :: nml_path
    type(sadj_type), optional, intent(in)    :: sadj

    integer :: xyz_dim

    logical :: adjoint_mode

  continue

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    if ( .not.lmpi_master ) verbose = .false.

    initial_setup : if (init) then

      call read_nml_sampling_output(soln%n_turb, soln%eqn_set,                 &
                                    grid%idistfcn,                             &
                                    adjoint_mode, soln%adim, nml_path )

      n_output_variables  = n_output_variables_m
      output_variables(:) = output_variables_m(:)
      need_turb_variables = need_turb_variables_m
      need_gradients      = need_gradients_m
      need_slen           = need_slen_m

      init = .false.

      return

    end if initial_setup

!   set aliases

    project_name = grid%project

    xyz_dim = size(grid%x)
    if ( verbose  ) write(6,'(a,1x,i8)') 'xyz_dim=',xyz_dim

    call survey_template( grid, soln, xyz_dim, sadj )

    call survey_write( grid, soln, time_step, sadj )

  end subroutine survey

!======================== READ_NML_SAMPLING_PARAMETERS =======================80
!
! Read namelist for reading in solution sampling parameters.
!
!=============================================================================80

  subroutine read_nml_sampling_parameters(nml_path)

    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use allocations,       only : my_alloc_ptr
    use namelist_util,     only : nml_error
    use string_utils,      only : list_to_array
    use geometry_utils,    only : get_area_normal
    use sampling_funclib,  only : rotate, is_quad_flat
    use sampling_headers,  only : sample, sampling_parameters_read             &
            , crinkle, nodal, plot                                             &
            , number_of_geometries, patch_list                                 &
            , type_of_geometry                                                 &
            , type_of_data, move_with_body                                     &
            , boundary_list                                                    &
            , plane_center, plane_normal                                       &
            , box_lower_corner, box_upper_corner                               &
            , sphere_center, sphere_radius                                     &
            , circle_center, circle_normal, circle_radius                      &
            , cylinder_face1, cylinder_face2, cylinder_radius                  &
            , cone_face1, cone_face2                                           &
            , cone_radius1, cone_radius2                                       &
            , corner1, corner2, corner3, corner4                               &
            , points, number_of_points, number_of_lines, number_of_rows        &
            , number_of_columns, schlieren_aspect                              &
            , window_height, window_width, window_center, window_normal        &
            , model_center, plot_lines                                         &
            , isosurf_variable, isosurf_value, isosurf_dist_threshold          &
            , isosurf_box                                                      &
            , x_range_upper, x_range_lower, y_range_upper, y_range_lower       &
            , z_range_upper, z_range_lower                                     &
            , blanking_list, blanking_list_count, sampling_frequency           &
            , variable_list, snap_output_xyz, dist_tolerance                   &
            , p1_line, p2_line, patch_list_count, label, fwh_formatted         &
            , verbose, default_boundary, append_timestep                       &
            , init_patch_list, sampling_final_write                            &
            , append_history, make_shadow, boundary_point, boundary            &
            , need_wall_data, reference_length, asynchronous_fwh

    use sampling_headers, only : sampling_strands
    use lmpi,             only : lmpi_master, lmpi_bcast,                      &
                                 lmpi_conditional_stop
    use info_depr,        only : print_conditional, skeleton

    character(*), intent(in) :: nml_path

    integer :: sunit, iostat1, iostat2

    integer :: ivol
    integer :: ipts
    integer :: istop
    integer :: nn

    logical :: show = .true.
    logical :: flat

    real(dp)                 :: pi
    real(dp)                 :: dist
    real(dp)                 :: angle, cossq
    real(dp), dimension(3)   :: n
    real(dp), dimension(3)   :: p1, p2, p3
    real(dp), dimension(3,3) :: a_rot
    real(dp), dimension(3)   :: pc
    real(dp), dimension(3)   :: pc_rot
    real(dp), dimension(3)   :: tparm
    real(dp), dimension(3)   :: temp_lower_corner
    real(dp), dimension(3)   :: temp_upper_corner
    real(dp)                 :: x_p, y_p, z_p
    real(dp)                 :: ifactor, jfactor
    real(dp)                 :: radius, phi, theta
    real(dp)                 :: x_frac, y_frac
    real(dp), parameter      :: small = 1.0e-8_dp
    real(dp), parameter      :: zero = 0.0_dp
    integer                  :: i, ii, j, line_index
    integer, dimension(:), pointer :: patch_found

    namelist /sampling_parameters/                                             &
              crinkle, nodal, plot                                             &
            , number_of_geometries, patch_list                                 &
            , type_of_geometry                                                 &
            , type_of_data, move_with_body                                     &
            , boundary_list                                                    &
            , plane_center, plane_normal                                       &
            , box_lower_corner, box_upper_corner                               &
            , sphere_center, sphere_radius                                     &
            , circle_center, circle_normal, circle_radius                      &
            , cylinder_face1, cylinder_face2, cylinder_radius                  &
            , cone_face1, cone_face2                                           &
            , cone_radius1, cone_radius2                                       &
            , corner1, corner2, corner3, corner4                               &
            , points, number_of_points, number_of_rows                         &
            , number_of_columns, schlieren_aspect                              &
            , window_height, window_width, window_center, window_normal        &
            , model_center, plot_lines                                         &
            , isosurf_variable, isosurf_value, isosurf_dist_threshold          &
            , isosurf_box                                                      &
            , x_range_upper, x_range_lower, y_range_upper, y_range_lower       &
            , z_range_upper, z_range_lower                                     &
            , blanking_list, blanking_list_count, sampling_frequency           &
            , variable_list, snap_output_xyz, dist_tolerance                   &
            , p1_line, p2_line, patch_list_count, label, fwh_formatted         &
            , verbose, append_history, make_shadow, boundary_point             &
            , boundary, need_wall_data, reference_length, asynchronous_fwh

  continue

!begin namelist &sampling_parameters
!
! This namelist specifies the types and frequency
! of sampling data to be exported for visualization.
! The output variables themselves are specified in
! the \cmd{&sampling_output_variables} namelist.
! The last dimension of each array references the geometry index,
! which is one to \cmd{number_of_geometries}.

    number_of_geometries = 0

!   This is the total number of sampling geometries.

    sampling_frequency(:) = 0

!   This specifies
!   the iteration interval at which sampling is performed.
!   The special value of \cmd{-1} means to only perform sampling at
!   the end of a successful run.

    label(:) = ''

!   This customizes the filename of sampling output.
!   When it is blank, the file will be
!   \var{[project_rootname]_tec_sampling_geomN.(dat,plt)}
!   where \var{N} is the sampling geometry number,
!   \var{.dat} is ASCII format, and \var{.plt} is binary format.

    type_of_geometry(:) = 'none'

!   This is the type of sampling geometry,

    verbose = .false.

!   [hidden] Produce more output diagnostics when sampling.

    crinkle = .false.

!   This snaps the sampling surface to nearest grid faces
!   instead of using linear interpolation.

    nodal = .false.

!   This uses the nearest nodal values instead of interpolating.

    plot(:) = 'tecplot'

!   This is the format of sampling output,

    patch_list_count(:) = 0

!   This is the number of patches in \var{patch_list}.

    patch_list(:) = ''

!   A string list of patch face IDs to limit boundary survey to
!   a subset of the boundary faces.
!   Commas and dashes can be used to specify ranges, i.e., \cmd{'1,2,5-7'}.

    type_of_data(:) = 'volume'

!   The source of data for extracting the requested sampling variables
!   for each \cmd{type_of_geometry}.

    move_with_body(:) = ''

!   Move the sampling geometry with the body if body is
!   in motion. Use the fixed inertial reference frame when blank.

    boundary_list = ''

!   List of patches to include when sampling boundaries;
!   Commas and dashes can be used to specify ranges, i.e., \cmd{'1,2,5-7'}.

    default_boundary = .true.

!   Use FUN3D default solid-wall-only boundary patches
!   when sampling boundary points, i.e., ignore symmetry,
!   slip, and flow-through boundaries.

    plane_center(1:3,:) = 0.0_dp

!   This is a point on a requested sampling \cmd{'plane'};
!   it fixes the location.

    plane_normal(1:3,:) = 0.0_dp

!   This is a normal vector of sampling \cmd{'plane'}; it fixes the orientation.

    box_lower_corner(1:3,:) = 0.0_dp

!   This is the coordinate of the lower corner of a \cmd{'box'}.

    box_upper_corner(1:3,:) = 0.0_dp

!   This is the coordinate of the upper corner of a \cmd{'box'}.

    sphere_center(1:3,:) = 0.0_dp

!   This is the coordinate of \cmd{'sphere'} center; it fixes the location.

    sphere_radius(:) = 0.0_dp

!   This is the radius for \cmd{'sphere'}; it fixes the size.

    circle_center(1:3,:) = 0.0_dp

!   This is the coordinate of center of a \cmd{'circle'}; it fixes the location.

    circle_normal(1:3,:) = 0.0_dp

!   This is the normal vector for a \cmd{'circle'}; it fixes the orientation.

    circle_radius(:) = 0.0_dp

!   This is the radius for a \cmd{'circle'}; it fixes the size.

    cylinder_face1(1:3,:) = 0.0_dp

!   This is the coordinate for the center of
!   the first face of a \cmd{'cylinder'}.

    cylinder_face2(1:3,:) = 0.0_dp

!   This is the coordinate for center of
!   the second face of a \cmd{'cylinder'}.

    cylinder_radius(:) = 0.0_dp

!   This is the radius of a \cmd{'cylinder'}.

    cone_face1(1:3,:) = 0.0_dp

!   This is the coordinate for center of the first face of a \cmd{'cylinder'}.

    cone_face2(1:3,:) = 0.0_dp

!   This is the coordinate for center of the second face of a \cmd{'cylinder'}.

    cone_radius1(:) = 0.0_dp

!   This is the radius of the first face of a \cmd{'cone'}.

    cone_radius2(:) = 0.0_dp

!   This is the radius of the second face of a \cmd{'cone'}.

    corner1(1:3,:) = 0.0_dp

!   This is the coordinate of the first corner of a \cmd{'quad'};
!   the corners proceed clockwise.

    corner2(1:3,:) = 0.0_dp

!   The coordinate of the second corner of a \cmd{'quad'}.

    corner3(1:3,:) = 0.0_dp

!   The coordinate of the third corner of a \cmd{'quad'}.

    corner4(1:3,:) = 0.0_dp

!   The coordinate of the fourth corner of a \cmd{'quad'}.

    number_of_points(:) = 0

!   This is the number of points to be sampled by \cmd{'boundary_point'}
!   or \cmd{'volume_point'}.

    points(1:3,:,:) = 0.0_dp

!   These are the coordinates of \var{boundary_point} and \var{volume_point}
!   sampling.
!   The first index is the Cartesian direction,
!   the second index is the geometry,
!   and the last index is the point in this geometry.

    p1_line(1:3,:) = 0.0_dp

!   This is the first end point of a line in \var{line} sampling.

    p2_line(1:3,:) = 0.0_dp

!   This is the second end point of a line in \var{line} sampling.

    schlieren_aspect = ''

!   This is the Cartesian direction for \cmd{'schlieren'} view,

    window_normal(1:3,:) = 0.0_dp

!   [hidden] This is the window normal vector for \cmd{'schlieren'};
!   it fixes orientation.

    window_height(:) = 0.0_dp

!   This is the window height for \cmd{'schlieren'}.

    window_width(:) = 0.0_dp

!   This is the window width for \cmd{'schlieren'}.

    window_center(1:3,:) = 0.0_dp

!   This is the window center for \cmd{'schlieren'}.

    number_of_rows(:) = 0

!   This is the vertical number of pixels in the \cmd{'schlieren'} window.

    number_of_columns(:) = 0

!   This is the horizontal number of pixels in the \cmd{'schlieren'} window.

    model_center(1:3,:) = 0.0_dp

!   This is the model center for \cmd{'schlieren'}.

    plot_lines(:) = .false.

!   This plots lines for \cmd{'schlieren'}.

    make_shadow = .false.

!   The boundary will cast a shadow in \var{schlieren} output.

    blanking_list_count(:) = 0

!   This is the number of boundaries to search for \cmd{'schlieren'}
!   boundary shadow.

    blanking_list(:) = ''

!   This is a list of boundaries to search for \cmd{'schlieren'} shadow.
!   Commas and dashes can be used to specify ranges, i.e., \cmd{'1,2,5-7'}.

    isosurf_variable(:) = 'p'

!   This is the variable used to define the geometry of an
!   \cmd{'isosurface'} and \var{isocrinkle}.

    isosurf_value(:) = 0.0_dp

!   This is the value of \cmd{isosurf_variable(:)} to create
!   the \cmd{'isosurface'}
!   and \var{isocrinkle} geometry.

    isosurf_box(:) = .false.

!   This clips the sampling geometry to be inside a box sized by \cmd{*_range_*}
!   within \cmd{isosurf_dist_threshold}.

    x_range_lower(:) = -1.0_dp

!   This limits \var{isosurface} or \var{isocrinkle}
!   when \cmd{isosurf_box(:) = .true.}

    x_range_upper(:) =  1.0_dp

!   This limits \var{isosurface} or \var{isocrinkle}
!   when \cmd{isosurf_box(:) = .true.}

    y_range_lower(:) = -1.0_dp

!   This limits \var{isosurface} or \var{isocrinkle}
!   when \cmd{isosurf_box(:) = .true.}

    y_range_upper(:) =  1.0_dp

!   This limits \var{isosurface} or \var{isocrinkle}
!   when \cmd{isosurf_box(:) = .true.}

    z_range_lower(:) = -1.0_dp

!   This limits \var{isosurface} or \var{isocrinkle}
!   when \cmd{isosurf_box(:) = .true.}

    z_range_upper(:) =  1.0_dp

!   This limits \var{isosurface} or \var{isocrinkle}
!   when \cmd{isosurf_box(:) = .true.}

    isosurf_dist_threshold(:) = 0.0_dp

!   This trims portions of an \var{isosurface} or \var{isocrinkle}
!   that have a distance to the surface less then this threshold.
!   It requires \cmd{isosurf_box(:) = .true.}

    variable_list(:) = ''

!   These variables augment \cmd{&sampling_output_variables}
!   for this sampling object.

    snap_output_xyz = .true.

!   This snaps the requested points to the nearest surface.

    dist_tolerance = 1.0e-3_dp

!   This is the tolerance used when \var{snap_output_xyz} is engaged.

    fwh_formatted = .false.

!   Write Ffowcs Williams-Hawkings in Fortran unformatted format.
!   The default is Fortran stream (C-binary).

    append_history(:) = .false.

!   This option removes the step number from the filename and opens
!   it with append.

    asynchronous_fwh = .false.

!   This uses asynchronous I/O for permeable FWH output.

    boundary_point(1:3,:) = 0.0_dp

!   This defines the location on a boundary where the friction velocity is to
!   be calculated for use in plotting data in wall units.  Additional input
!   that is required is \var{boundary(ib)}.

    boundary(:) = 0

!   This defines the boundary on which \var{boundary_point(1:3,ib)}
!   will be found.

    need_wall_data(:) = .false.

!   This option activates the calculation of the friction velocity at a point
!   on a wall for plotting of data in wall units.  Other required input would
!   be \var{boundary(ib)} and \var{boundary_point(:,ib)}.
!   A sample asking for a boundary layer profile in wall units on boundary
!   number 2 at the point (4., 0.05,0.) could have the following form:
!   \begin{Verbatim}
!   &sampling_parameters
!     number_of_geometries = 1
!     type_of_geometry(1)  = 'line'
!     p1_line(:,1)         = 4.0,0.05,-10.
!     p2_line(:,1)         = 4.0,0.05, 10.
!     boundary(1)          = 2
!     boundary_point(:,1)  = 4.0,0.05,0.
!     need_wall_data(1)    = T
!     variable_list(1)='uplus,yplus,kplus,wplus'
!     sampling_frequency(1) = -1
!   /
!   \end{Verbatim}

    reference_length = 0.0_dp

!   This is the reference length for
!   $Re_{\tau}$ used in \cmd{&sampling_output_variables}.

!end namelist

    iostat1 = 0
    iostat2 = 0
    master_read_nml : if ( lmpi_master ) then
      sunit = available_unit()
      call se_open(sunit, file=nml_path, status='old', iostat=iostat1)
      if (iostat1 == 0) then
        show = print_conditional
        read(sunit,iostat=iostat2,nml=sampling_parameters)
        if ( show ) write(*,nml=sampling_parameters)
      else
        write(*,*) 'unable to open ', trim(nml_path)
      endif
      close(sunit)
    endif master_read_nml

    call nml_error( iostat1, iostat2, nml_path, 'sampling_parameters')

    sampling_parameters_read = .true.

    call lmpi_bcast( sampling_parameters_read )
    call lmpi_bcast( number_of_geometries )
    call lmpi_bcast( asynchronous_fwh )

    if ( number_of_geometries < 1 ) return

! Allocate and read input data

    call lmpi_bcast( verbose )
    call lmpi_bcast( crinkle )
    call lmpi_bcast( nodal )
    call lmpi_bcast( boundary_list )
    call lmpi_bcast( default_boundary )
    call lmpi_bcast( reference_length )

!-----------------figure up possible boundaries to be sampled-------

    allocate(sample(number_of_geometries))

    read_geo_data : do ivol = 1, number_of_geometries

      master_check_options : if ( lmpi_master ) then
        select case (plot(ivol))
        case ('tecplot')
!         \Tecplot format.
        case ('fwh')
!         format for Ffowcs Williams-Hawkings analysis.
        case ('serial_history')
!         custom low-overhead point sampling format where all locations
!         listed once at the top and then just the requested values
!         per \var{sampling_frequency}.
        case default
          write(*,*) 'ERROR: plot format for volume', ivol, 'is ', &
                     trim(plot(ivol))
          call lmpi_conditional_stop(1,'invalid &sampling_parameters option')
        end select

        select case (type_of_data(ivol))
        case ('volume')
!         extract data from the computational volume.
        case ('boundary')
!         extract data from a boundary.
        case ('volume_statistics')
!         [hidden] examine volume statistics.
        case ('mesh_statistics')
!         [hidden] examine mesh statistics.
        case ('integrated')
!         extract data from the computational volume and integrate over
!         defined geometry.
        case default
          write(*,*) 'ERROR: type_of_data for volume', ivol, 'is ', &
                     trim(plot(ivol))
          call lmpi_conditional_stop(1,'invalid &sampling_parameters option')
        end select

        select case ( schlieren_aspect(ivol) )
        case ( 'y' )
!         Schlieren viewing along $y$ axis.
        case ( 'z' )
!         Schlieren viewing along $z$ axis.
        case ( 'y1' )
!         Schlieren viewing along $y$ axis.
        case ( 'z1' )
!         Schlieren viewing along $z$ axis.
        case ('')
!         Schlieren viewing along \var{window_normal}.
        case default
          write(*,*) 'ERROR: invalid schlieren_aspect: ', &
                     trim(schlieren_aspect(ivol))
          call lmpi_conditional_stop(1,'invalid &sampling_parameters option')
        end select

        select case ( isosurf_variable(ivol) )
        case ( 'p' )
!         Pressure.
        case ( 'rho' )
!         Density.
        case ( 'u' )
!         $X$-component of velocity.
        case ( 'v' )
!         $Y$-component of velocity.
        case ( 'w' )
!         $Z$-component of velocity.
        case ( 'vort_x' )
!         $X$-component of vorticity.
        case ( 'vort_y' )
!         $Y$-component of vorticity.
        case ( 'vort_z' )
!         $Z$-component of vorticity.
        case ( 'vort_mag' )
!         Total magnitude of vorticity vector.
        case ( 'vort_mag_avg' )
!         Average total magnitude of vorticity vector.
        case ( 'vort_mag_rms' )
!         RMS total magnitude of vorticity vector.
        case ( 'q_criterion' )
!         Q-criterion.
        case ( 'mach' )
!         Mach number.
        case ( 'temperature' )
!         Temperature.
        case ( 'p_tavg' )
!         Time average pressure.
        case ( 'rho_tavg' )
!         Time average density.
        case ( 'u_tavg' )
!         Time average $x$-component of velocity.
        case ( 'v_tavg' )
!         Time average $y$-component of velocity.
        case ( 'w_tavg' )
!         Time average $z$-component of velocity.
        case ( 'p_trms' )
!         RMS of pressure.
        case ( 'rho_trms' )
!         RMS of density.
        case ( 'u_trms' )
!         RMS of the $x$-component of velocity.
        case ( 'v_trms' )
!         RMS of the $y$-component of velocity.
        case ( 'w_trms' )
!         RMS of the $z$-component of velocity.
        case ( 'critical_d' )
!         critical d
        case ('s1a')
!         other option
        case ('s1b')
!         other option
        case ('s1')
!         other option
        case ('s2')
!         other option
        case ('lambda1')
!         Adjoint variable for the 1st governing equation.
        case ('lambda2')
!         Adjoint variable for the 2nd governing equation.
        case ('lambda3')
!         Adjoint variable for the 3rd governing equation.
        case ('lambda4')
!         Adjoint variable for the 4th governing equation.
        case ('lambda5')
!         Adjoint variable for the 5th governing equation.
        case ('lambda6')
!         Adjoint variable for the 6th governing equation.
        case ('lambda7')
!         Adjoint variable for the 7th governing equation.
        case ('processor_id')
!         The assigned processor ID.
        case ('bird_breakdown')
!         Bird breakdown factor.
        case default
          write(*,*) 'ERROR: invalid isosurf_variable: ', &
                     trim(isosurf_variable(ivol))
          call lmpi_conditional_stop(1,'invalid &sampling_parameters option')
        end select

      end if master_check_options

      call lmpi_conditional_stop(0,'invalid &sampling_parameters option')

      call initialize_type(ivol)

      call lmpi_bcast( plot(ivol) )
      call lmpi_bcast( label(ivol) )
      call lmpi_bcast( patch_list(ivol) )
      call lmpi_bcast( variable_list(ivol) )
      call lmpi_bcast( type_of_geometry(ivol) )
      call lmpi_bcast( type_of_data(ivol) )
      call lmpi_bcast( sampling_frequency(ivol) )
      call lmpi_bcast( move_with_body(ivol) )
      call lmpi_bcast( patch_list_count(ivol) )
      call lmpi_bcast( blanking_list(ivol) )
      call lmpi_bcast( blanking_list_count(ivol) )
      call lmpi_bcast( fwh_formatted )
      call lmpi_bcast( append_history )

      if ( sampling_frequency(ivol) == 0 ) then

!       don't output this sampling geometry

      else if ( sampling_frequency(ivol) <  0 ) then

!       output only at end of run...but we don't yet know when that is

        sampling_frequency(ivol)      = -1
        sampling_final_write(ivol)    = .true.
        sampling_strands(ivol)        = .false.
        append_timestep(ivol)         = .false.

      else if ( sampling_frequency(ivol) >  0 ) then

!       user has explicitly set the frequency for output

        sampling_final_write(ivol)    = .false.
        sampling_strands(ivol)        = .true.
        append_timestep(ivol)         = .true.

      endif

      call lmpi_bcast( append_timestep )
      call lmpi_bcast( sampling_frequency(ivol) )
      call lmpi_bcast( sampling_final_write(ivol) )
      call lmpi_bcast( sampling_strands(ivol) )
      call lmpi_bcast( need_wall_data(ivol) )

      istop = 0

      init_master: if ( init_patch_list(ivol) ) then

        if ( lmpi_master ) then
          if ( init_patch_list(ivol) ) then
            nullify ( sample(ivol)%patch_list )
            init_patch_list(ivol) = .false.
          endif

          make_list: if ( patch_list_count(ivol) == 0 ) then

            if ( .not.associated( sample(ivol)%patch_list ) )                  &
               call my_alloc_ptr( sample(ivol)%patch_list, 1 )
            sample(ivol)%patch_list = 0

          else

            if ( patch_list(ivol) /= '' ) then
              call list_to_array( patch_list(ivol), patch_found )
              if ( size(patch_found) /= patch_list_count(ivol) ) then
                write(*,'(3a,i0)') 'Character string patch_list too short ',   &
                     'in namelist file read for requested number of ',         &
                      'geometries to group, geometry= ', ivol
                istop = 1
              end if

              if ( .not.associated( sample(ivol)%patch_list ) ) then
              call my_alloc_ptr( sample(ivol)%patch_list,                      &
                                                patch_list_count(ivol) )
            endif

            do nn = 1, patch_list_count(ivol)
              sample(ivol)%patch_list(nn) = patch_found(nn)
            enddo
            deallocate(patch_found)
          endif

          endif make_list

        else

          call my_alloc_ptr( sample(ivol)%patch_list,                          &
                       max( 1, patch_list_count(ivol) ) )

        endif

      endif init_master

      call lmpi_conditional_stop( istop, "sampling_main::patch_list failure")
      call lmpi_bcast( sample(ivol)%patch_list )

      if ( verbose ) then
        write(*,'(a,50i0)') 'Patch list = ', sample(ivol)%patch_list
      endif

      pi = acos(-1.0_dp)

      x_p = 0.0_dp
      y_p = 0.0_dp
      z_p = 0.0_dp

      sample(ivol)%geo_type_name = type_of_geometry(ivol)
      select case ( type_of_geometry(ivol) )

      case ('streamsurface')
! is a stream surface, requires \var{number_of_points} and \var{points}.

        call lmpi_bcast( number_of_points(ivol) )
        call lmpi_bcast( points )

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Streamsurface ',ivol,  &
                     ' requesting ',number_of_points(ivol),' points.'
        endif
        do ipts = 1, number_of_points(ivol)
          sample(ivol)%point_list(:,ipts) = points(:,ivol,ipts)
        enddo

        call lmpi_bcast( sample(ivol)%point_list )

      case ('boundary_points')
! for boundary point sampling, requires \var{number_of_points} and \var{points},
! modified by \var{snap_output_xyz} and \var{dist_tolerance}.

        call lmpi_bcast( number_of_points(ivol) )
        call lmpi_bcast( points )
        call lmpi_bcast( snap_output_xyz )
        call lmpi_bcast( dist_tolerance )

        if ( lmpi_master ) write(*,*)                                      &
          'Found user-defined sampling type:',                             &
          trim(sample(ivol)%geo_type_name),ivol,                           &
          ' requesting ',number_of_points(ivol),' points.'

        do ipts = 1, number_of_points(ivol)
          sample(ivol)%point_list(:,ipts) = points(:,ivol,ipts)
          sample(ivol)%print_list(:,ipts) = points(:,ivol,ipts)
        enddo

        call lmpi_bcast( sample(ivol)%point_list )
        call lmpi_bcast( sample(ivol)%print_list )

      case ('volume_points')
! for point sampling in the domain,
! requires \var{number_of_points} and \var{points}.

        call lmpi_bcast( number_of_points(ivol) )
        call lmpi_bcast( points )
        call lmpi_bcast( snap_output_xyz )
        call lmpi_bcast( dist_tolerance )

        if ( lmpi_master ) write(*,*)                                      &
          'Found user-defined sampling type:',                             &
          trim(sample(ivol)%geo_type_name),ivol,                           &
          ' requesting ',number_of_points(ivol),' points.'

        do ipts = 1, number_of_points(ivol)
          sample(ivol)%point_list(:,ipts) = points(:,ivol,ipts)
          sample(ivol)%print_list(:,ipts) = points(:,ivol,ipts)
        enddo

        call lmpi_bcast( sample(ivol)%point_list )
        call lmpi_bcast( sample(ivol)%print_list )

       case ('schlieren')
! is a schlieren image via an integral of the refractive index field, requires
! \var{number_of_rows}, \var{number_of_columns},
! \var{window_height}, \var{window_width}, \var{window_center},
! and \var{schlieren_aspect}.
! It is controlled by \var{make_shadow} and \var{plot_lines}.

        call lmpi_bcast( number_of_rows )
        call lmpi_bcast( number_of_columns )
        call lmpi_bcast( window_height )
        call lmpi_bcast( window_width )
        call lmpi_bcast( window_center )
        call lmpi_bcast( model_center )
        call lmpi_bcast( schlieren_aspect(ivol) )
        call lmpi_bcast( plot_lines(ivol) )
        call lmpi_bcast( make_shadow )

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
        if ( schlieren_aspect(ivol) == 'z' ) then
          window_normal(:,ivol) = (/0.0_dp,0.0_dp,1.0_dp/)
          if ( lmpi_master )                                                   &
           write(*,'(3x,a,a,a,i0)') 'Using schlieren_aspect of ',              &
             schlieren_aspect(ivol),' for geometry ', ivol
        else if ( schlieren_aspect(ivol) == 'y' ) then
          window_normal(:,ivol) = (/0.0_dp,1.0_dp,0.0_dp/)
          if ( lmpi_master )                                                   &
           write(*,'(3x,a,a,a,i0)') 'Using schlieren_aspect of ',              &
             schlieren_aspect(ivol),' for geometry ', ivol
        else

        window_normal(:,ivol) = window_center(:,ivol) - model_center(:,ivol)
        radius                = sqrt(                                          &
                        window_normal(1,ivol)* window_normal(1,ivol) +         &
                        window_normal(2,ivol)* window_normal(2,ivol) +         &
                        window_normal(3,ivol)* window_normal(3,ivol))
        if ( radius /= zero ) then
          window_normal(:,ivol) = window_normal(:,ivol)/radius
        endif
          if ( lmpi_master )  then
            write(6,'(3x,a,i0)') 'Using window look angle for geometry ', ivol
          end if
        endif

        if ( ( abs(window_normal(1,ivol)) < small ) .and.                      &
             ( abs(window_normal(2,ivol)) < small ) ) then
          theta = pi/2.0_dp
!         theta = 0.0_dp
            phi = 0.0_dp
        else if ( ( abs(window_normal(1,ivol)) < small ) .and.                 &
                  ( abs(window_normal(2,ivol)) > small ) ) then
          theta = pi/2.0_dp
          phi   = acos( window_normal(3,ivol) )
        else
          theta = atan( window_normal(2,ivol) / window_normal(1,ivol) )
          phi   = acos( window_normal(3,ivol) )
        endif

        sample(ivol)%theta_hat =                                               &
                     (/-sin(theta),cos(theta),zero/)                       ! 'x'
        sample(ivol)%phi_hat   =                                               &
                     (/cos(theta)*cos(phi),sin(theta)*cos(phi),-sin(phi)/) ! 'y'
        sample(ivol)%r_hat     =                                               &
                     (/cos(theta)*sin(phi),sin(theta)*sin(phi),cos(phi)/)  ! 'z'

        a_rot     = reshape((/                                                 &
                              sample(ivol)%phi_hat,                            &
                              sample(ivol)%theta_hat,                          &
                              sample(ivol)%r_hat/),(/3,3/))
        if ( schlieren_aspect(ivol) == 'y' ) then
          a_rot = reshape((/                                                   &
           1.0_dp, 0.0_dp, 0.0_dp                                              &
          ,0.0_dp, 0.0_dp, 1.0_dp                                              &
          ,0.0_dp, 1.0_dp, 0.0_dp                                              &
                           /),(/3, 3/))
        else if ( schlieren_aspect(ivol) == 'z' ) then
          a_rot = reshape((/                                                   &
           1.0_dp, 0.0_dp, 0.0_dp                                              &
          ,0.0_dp, 1.0_dp, 0.0_dp                                              &
          ,0.0_dp, 0.0_dp, 1.0_dp                                              &
                           /),(/3, 3/))
        endif
        if ( skeleton > 2 ) then
          write(6,'(a)') 'Schlieren rotation matrix'
          write(6,'(3(1x,f10.3))') a_rot
        endif
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80


        call lmpi_bcast( window_normal )

        sample(ivol)%delta_x =  window_width(ivol)                             &
                             / float(number_of_columns(ivol)-1)
        sample(ivol)%delta_y = window_height(ivol)                             &
                             / float(number_of_rows(ivol)-1)
        number_of_lines(ivol) = number_of_rows(ivol)                           &
                              * number_of_columns(ivol)
        call lmpi_bcast( number_of_lines )

!       allocate( lines ( 6, number_of_lines(ivol) ) )
        call my_alloc_ptr(                                                     &
             sample(ivol)%line_list , 3, max(1,number_of_lines(ivol)) )

        do i = 1, number_of_columns(ivol)
          do j = 1, number_of_rows(ivol)
            line_index = (i-1)*number_of_rows(ivol) + j

            if ( number_of_columns(ivol) == 1 ) then
              x_p = window_center(1,ivol)
              y_p = window_center(2,ivol)
              z_p = window_center(3,ivol)
            else
              ifactor = 2.0_dp*real(i-1,dp)/real(number_of_columns(ivol)-1,dp)
              jfactor = 2.0_dp*real(j-1,dp)/real(number_of_rows(ivol)-1,dp)
              pc(1) = - (1.0_dp-ifactor)*(window_width(ivol)/2.0_dp) !x,theta
              pc(2) = - (1.0_dp-jfactor)*(window_height(ivol)/2.0_dp) !y,phi
              pc(3) = 0.0_dp
              pc_rot  = rotate( pc, a_rot )
              pc_rot  = pc_rot + window_center(:,ivol)
              if ( schlieren_aspect(ivol) == 'y' ) then

                x_frac = real(i-1,dp)/real(number_of_columns(ivol)-1,dp)       &
                         - 0.5_dp
                y_frac = real(j-1,dp)/real(number_of_rows(ivol)-1,dp)          &
                         - 0.5_dp
                x_p    = x_frac*window_width(ivol)  + window_center(1,ivol)
                y_p    =                              window_center(2,ivol)
                z_p    = y_frac*window_height(ivol) + window_center(3,ivol)
!               x_p     = pc(1) + window_center(1,ivol)
!               y_p     = pc(2) + window_center(2,ivol)
!               z_p     = pc(3) + window_center(3,ivol)
              else if ( schlieren_aspect(ivol) == 'z' ) then

                x_frac = real(i-1,dp)/real(number_of_columns(ivol)-1,dp)       &
                         - 0.5_dp
                y_frac = real(j-1,dp)/real(number_of_rows(ivol)-1,dp)          &
                         - 0.5_dp
                x_p    = x_frac*window_width(ivol)  + window_center(1,ivol)
                y_p    = y_frac*window_height(ivol) + window_center(2,ivol)
                z_p    =                              window_center(3,ivol)
!               x_p     = pc(1) + window_center(1,ivol)
!               y_p     = pc(2) + window_center(2,ivol)
!               z_p     = pc(3) + window_center(3,ivol)
              else
                x_p     = pc_rot(1) !- 100.0_dp*window_normal(1,ivol)
                y_p     = pc_rot(2) !- 100.0_dp*window_normal(2,ivol)
                z_p     = pc_rot(3) !- 100.0_dp*window_normal(3,ivol)
              endif
            endif

!           if ( number_of_columns(ivol) == 1 ) then
!             x_p = window_center(1,ivol)
!           else
!             x_frac = float(i-1)/float(number_of_columns(ivol)-1) - 0.5_dp
!             x_p    = x_frac*window_width(ivol) + window_center(1,ivol)
!           endif

!           if ( number_of_rows(ivol) == 1 ) then
!             y_p = window_center(2,ivol)
!             z_p = window_center(3,ivol)
!           else
!             y_frac = float(j-1)/float(number_of_rows(ivol)-1) - 0.5_dp
!             y_p    = y_frac*window_height(ivol) + window_center(2,ivol)
!             z_p    = y_frac*window_height(ivol) + window_center(3,ivol)
!           endif

            sample(ivol)%line_list(1,line_index) = x_p
            sample(ivol)%line_list(2,line_index) = y_p
            sample(ivol)%line_list(3,line_index) = z_p
!           if ( schlieren_aspect(ivol) == 'z' ) then
!             sample(ivol)%line_list(2,line_index) = y_p
!             sample(ivol)%line_list(3,line_index) = window_center(3,ivol)
!           else if ( schlieren_aspect(ivol) == 'y' ) then
!             sample(ivol)%line_list(2,line_index) = window_center(2,ivol)
!             sample(ivol)%line_list(3,line_index) = z_p
!           endif
          enddo
        enddo

!       call lmpi_bcast( lines )

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Schlieren ',ivol,      &
                     ' requesting ',number_of_lines(ivol),' lines.'
        endif

        sample(ivol)%number_of_rows     = number_of_rows(ivol)
        sample(ivol)%number_of_columns  = number_of_columns(ivol)
        sample(ivol)%number_of_lines    = number_of_lines(ivol)
        sample(ivol)%window_height      = window_height(ivol)
        sample(ivol)%window_width       = window_width(ivol)
        sample(ivol)%window_center(1:3) = window_center(1:3,ivol)
        sample(ivol)%window_normal(1:3) = window_normal(1:3,ivol)
        sample(ivol)%schlieren_aspect   = schlieren_aspect(ivol)
        sample(ivol)%plot_lines         = plot_lines(ivol)
        call lmpi_bcast( sample(ivol)%number_of_rows )
        call lmpi_bcast( sample(ivol)%number_of_columns )
        call lmpi_bcast( sample(ivol)%number_of_lines )
        call lmpi_bcast( sample(ivol)%window_height )
        call lmpi_bcast( sample(ivol)%window_width )
        call lmpi_bcast( sample(ivol)%window_center )
        call lmpi_bcast( sample(ivol)%window_normal )
        call lmpi_bcast( sample(ivol)%phi_hat )
        call lmpi_bcast( sample(ivol)%theta_hat )
        call lmpi_bcast( sample(ivol)%r_hat )
        call lmpi_bcast( sample(ivol)%schlieren_aspect )
        call lmpi_bcast( sample(ivol)%plot_lines )
! p1
        pc(1)   = -(window_width(ivol)/2.0_dp) !x,theta
        pc(2)   = -(window_height(ivol)/2.0_dp) !y,phi
        pc(3)   = 0.0_dp
        pc_rot  = rotate( pc, a_rot )
        pc_rot  = pc_rot + window_center(:,ivol)
        if ( schlieren_aspect(ivol) == 'y' ) then
          x_p     = window_center(1,ivol)-(window_width(ivol)/2.0_dp)
          y_p     = -1.0e+5
          z_p     = window_center(3,ivol)-(window_height(ivol)/2.0_dp)
        else if ( schlieren_aspect(ivol) == 'z' ) then
          x_p     = window_center(1,ivol)-(window_width(ivol)/2.0_dp)
          y_p     = window_center(2,ivol)-(window_height(ivol)/2.0_dp)
          z_p     = -1.0e+5
        else if ( schlieren_aspect(ivol) == '' ) then
          x_p     = pc_rot(1) - 100.0_dp*window_normal(1,ivol)
          y_p     = pc_rot(2) - 100.0_dp*window_normal(2,ivol)
          z_p     = pc_rot(3) - 100.0_dp*window_normal(3,ivol)
        else
          if (lmpi_master)                                                     &
            write(*,*) 'invalid schlieren_aspect: ',trim(schlieren_aspect(ivol))
        end if
        sample(ivol)%window_box(1,1) = x_p
        sample(ivol)%window_box(2,1) = y_p
        sample(ivol)%window_box(3,1) = z_p

        sample(ivol)%window_box(1,2) = x_p
        sample(ivol)%window_box(1,5) = x_p
        sample(ivol)%window_box(1,6) = x_p
        pc(1)   = (window_width(ivol)/2.0_dp) !x,theta
        pc(2)   = (window_height(ivol)/2.0_dp) !y,phi
        pc(3)   = 0.0_dp
        pc_rot  = rotate( pc, a_rot )
        pc_rot  = pc_rot + window_center(:,ivol)
        if ( schlieren_aspect(ivol) == 'y' ) then
          x_p     = window_center(1,ivol)+(window_width(ivol)/2.0_dp)
          y_p     = 1.0e+5
          z_p     = window_center(3,ivol)+(window_height(ivol)/2.0_dp)
        else if ( schlieren_aspect(ivol) == 'z' ) then
          x_p     = window_center(1,ivol)+(window_width(ivol)/2.0_dp)
          y_p     = window_center(2,ivol)+(window_height(ivol)/2.0_dp)
          z_p     = 1.0e+5
        else if ( schlieren_aspect(ivol) == '' ) then
          x_p     = pc_rot(1) + 100.0_dp*window_normal(1,ivol)
          y_p     = pc_rot(2) + 100.0_dp*window_normal(2,ivol)
          z_p     = pc_rot(3) + 100.0_dp*window_normal(3,ivol)
        else
          if (lmpi_master)                                                     &
          write(*,*) 'invalid schlieren_aspect: ',trim(schlieren_aspect(ivol))
        end if
        sample(ivol)%window_box(1,3) = x_p
        sample(ivol)%window_box(1,4) = x_p
        sample(ivol)%window_box(1,7) = x_p
        sample(ivol)%window_box(1,8) = x_p
        sample(ivol)%window_box(2,8) = y_p
        sample(ivol)%window_box(3,8) = z_p
        if ( schlieren_aspect(ivol) == 'z1' ) then
          sample(ivol)%window_box(2,1) =                                       &
                    window_center(2,ivol) - 0.5_dp*window_height(ivol)
          sample(ivol)%window_box(2,2) = sample(ivol)%window_box(2,1)
          sample(ivol)%window_box(2,3) = sample(ivol)%window_box(2,1)
          sample(ivol)%window_box(2,4) = sample(ivol)%window_box(2,1)

          sample(ivol)%window_box(2,5) =                                       &
                    window_center(2,ivol) + 0.5_dp*window_height(ivol)
          sample(ivol)%window_box(2,6) = sample(ivol)%window_box(2,5)
          sample(ivol)%window_box(2,7) = sample(ivol)%window_box(2,5)
          sample(ivol)%window_box(2,8) = sample(ivol)%window_box(2,5)

          sample(ivol)%window_box(3,1) = -1.0e+12
          sample(ivol)%window_box(3,3) = -1.0e+12
          sample(ivol)%window_box(3,5) = -1.0e+12
          sample(ivol)%window_box(3,7) = -1.0e+12
          sample(ivol)%window_box(3,2) =  1.0e+12
          sample(ivol)%window_box(3,4) =  1.0e+12
          sample(ivol)%window_box(3,6) =  1.0e+12
          sample(ivol)%window_box(3,8) =  1.0e+12

        else if ( schlieren_aspect(ivol) == 'y1' ) then
          sample(ivol)%window_box(3,1) =                                       &
                    window_center(3,ivol) - 0.5_dp*window_height(ivol)
          sample(ivol)%window_box(3,3) = sample(ivol)%window_box(3,1)
          sample(ivol)%window_box(3,5) = sample(ivol)%window_box(3,1)
          sample(ivol)%window_box(3,7) = sample(ivol)%window_box(3,1)

          sample(ivol)%window_box(3,2) =                                       &
                    window_center(3,ivol) + 0.5_dp*window_height(ivol)
          sample(ivol)%window_box(3,4) = sample(ivol)%window_box(3,2)
          sample(ivol)%window_box(3,6) = sample(ivol)%window_box(3,2)
          sample(ivol)%window_box(3,8) = sample(ivol)%window_box(3,2)

          sample(ivol)%window_box(2,1) = -1.0e+12
          sample(ivol)%window_box(2,2) = -1.0e+12
          sample(ivol)%window_box(2,3) = -1.0e+12
          sample(ivol)%window_box(2,4) = -1.0e+12
          sample(ivol)%window_box(2,5) =  1.0e+12
          sample(ivol)%window_box(2,6) =  1.0e+12
          sample(ivol)%window_box(2,7) =  1.0e+12
          sample(ivol)%window_box(2,8) =  1.0e+12
        end if
        call lmpi_bcast( sample(ivol)%window_box )
        call lmpi_bcast( sample(ivol)%line_list )
        if ( lmpi_master ) then
          write(*,'(a,3(1x,f12.4))')'Window box-corner 1',                     &
                        sample(ivol)%window_box(1:3,1)
          write(*,'(a,3(1x,f12.4))')'Window box-corner 8',                     &
                        sample(ivol)%window_box(1:3,8)
        endif

      case ('isosurface','isocrinkle')
! is an isosurface that
! requires \var{isosurf_variable} and \var{isosurf_value}.
! It is controlled by \var{*_range_lower} and \var{*_range_upper}.

        if ( lmpi_master ) write(*,*) 'Found user-defined sampling type:',&
          trim(sample(ivol)%geo_type_name), ivol

        call lmpi_bcast( isosurf_variable(ivol) )
        call lmpi_bcast( isosurf_value )
        sample(ivol)%iso_variable = isosurf_variable(ivol)
        sample(ivol)%iso_val      = isosurf_value(ivol)

        call lmpi_bcast( isosurf_dist_threshold(ivol) )
        sample(ivol)%slen_min = isosurf_dist_threshold(ivol)

        call lmpi_bcast( isosurf_box(ivol)   )
        sample(ivol)%iso_box = isosurf_box(ivol)

        call lmpi_bcast( x_range_lower(ivol) )
        call lmpi_bcast( x_range_upper(ivol) )
        call lmpi_bcast( y_range_lower(ivol) )
        call lmpi_bcast( y_range_upper(ivol) )
        call lmpi_bcast( z_range_lower(ivol) )
        call lmpi_bcast( z_range_upper(ivol) )

        sample(ivol)%x_lower = x_range_lower(ivol)
        sample(ivol)%x_upper = x_range_upper(ivol)
        sample(ivol)%y_lower = y_range_lower(ivol)
        sample(ivol)%y_upper = y_range_upper(ivol)
        sample(ivol)%z_lower = z_range_lower(ivol)
        sample(ivol)%z_upper = z_range_upper(ivol)

      case ('box','filledbox')
! samples a the surface of a box.
! It requires \var{box_lower_corner} and \var{box_upper_corner}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Box ',ivol
        endif

!       make sure box corner xyz data is ordered correctly

        temp_lower_corner(:) = box_lower_corner(:,ivol)
        temp_upper_corner(:) = box_upper_corner(:,ivol)

        box_lower_corner(1,ivol) = min(temp_lower_corner(1),                   &
                                       temp_upper_corner(1))
        box_lower_corner(2,ivol) = min(temp_lower_corner(2),                   &
                                       temp_upper_corner(2))
        box_lower_corner(3,ivol) = min(temp_lower_corner(3),                   &
                                       temp_upper_corner(3))

        box_upper_corner(1,ivol) = max(temp_lower_corner(1),                   &
                                       temp_upper_corner(1))
        box_upper_corner(2,ivol) = max(temp_lower_corner(2),                   &
                                       temp_upper_corner(2))
        box_upper_corner(3,ivol) = max(temp_lower_corner(3),                   &
                                       temp_upper_corner(3))

        if (lmpi_master) then
          if ( (box_lower_corner(1,ivol) /= temp_lower_corner(1))  .or.        &
               (box_lower_corner(2,ivol) /= temp_lower_corner(2))  .or.        &
               (box_lower_corner(3,ivol) /= temp_lower_corner(3))  .or.        &
               (box_upper_corner(1,ivol) /= temp_upper_corner(1))  .or.        &
               (box_upper_corner(2,ivol) /= temp_upper_corner(2))  .or.        &
               (box_upper_corner(3,ivol) /= temp_upper_corner(3))) then
            write(*,*)
            write(*,'(2a)')' Warning: box upper/lower corner data altered',    &
                       ' to conform to order requirements'
            write(*,'(a,3(1x,e15.7))') '   Original  lower corner:',           &
                           (temp_lower_corner(ii), ii=1,3)
            write(*,'(a,3(1x,e15.7))') '   Corrected lower corner:',           &
                           (box_lower_corner(ii,ivol), ii=1,3)
            write(*,'(a,3(1x,e15.7))') '   Original  upper corner:',           &
                           (temp_upper_corner(ii), ii=1,3)
            write(*,'(a,3(1x,e15.7))') '   Corrected upper corner:',           &
                           (box_upper_corner(ii,ivol), ii=1,3)
            write(*,*)
          end if
        end if

        call lmpi_bcast( box_lower_corner )
        call lmpi_bcast( box_upper_corner )

        sample(ivol)%lower_corner(:) = box_lower_corner(:,ivol)
        sample(ivol)%upper_corner(:) = box_upper_corner(:,ivol)

        call lmpi_bcast( sample(ivol)%lower_corner )
        call lmpi_bcast( sample(ivol)%upper_corner )

        sample(ivol)%p1(1) = sample(ivol)%lower_corner(1)
        sample(ivol)%p1(2) = sample(ivol)%lower_corner(2)
        sample(ivol)%p1(3) = sample(ivol)%lower_corner(3)

        sample(ivol)%p2(1) = sample(ivol)%lower_corner(1)
        sample(ivol)%p2(2) = sample(ivol)%lower_corner(2)
        sample(ivol)%p2(3) = sample(ivol)%upper_corner(3)

        sample(ivol)%p3(1) = sample(ivol)%upper_corner(1)
        sample(ivol)%p3(2) = sample(ivol)%lower_corner(2)
        sample(ivol)%p3(3) = sample(ivol)%lower_corner(3)

        sample(ivol)%p4(1) = sample(ivol)%upper_corner(1)
        sample(ivol)%p4(2) = sample(ivol)%lower_corner(2)
        sample(ivol)%p4(3) = sample(ivol)%upper_corner(3)

        sample(ivol)%p5(1) = sample(ivol)%lower_corner(1)
        sample(ivol)%p5(2) = sample(ivol)%upper_corner(2)
        sample(ivol)%p5(3) = sample(ivol)%lower_corner(3)

        sample(ivol)%p6(1) = sample(ivol)%lower_corner(1)
        sample(ivol)%p6(2) = sample(ivol)%upper_corner(2)
        sample(ivol)%p6(3) = sample(ivol)%upper_corner(3)

        sample(ivol)%p7(1) = sample(ivol)%upper_corner(1)
        sample(ivol)%p7(2) = sample(ivol)%upper_corner(2)
        sample(ivol)%p7(3) = sample(ivol)%lower_corner(3)

        sample(ivol)%p8(1) = sample(ivol)%upper_corner(1)
        sample(ivol)%p8(2) = sample(ivol)%upper_corner(2)
        sample(ivol)%p8(3) = sample(ivol)%upper_corner(3)

        call lmpi_bcast( sample(ivol)%p1 )
        call lmpi_bcast( sample(ivol)%p2 )
        call lmpi_bcast( sample(ivol)%p3 )
        call lmpi_bcast( sample(ivol)%p4 )
        call lmpi_bcast( sample(ivol)%p5 )
        call lmpi_bcast( sample(ivol)%p6 )
        call lmpi_bcast( sample(ivol)%p7 )
        call lmpi_bcast( sample(ivol)%p8 )

      case ('sphere')
! samples a spherical surface.
! It requires \var{sphere_center} and \var{sphere_radius}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Sphere ',ivol
        endif

        call lmpi_bcast( sphere_center )
        call lmpi_bcast( sphere_radius )

        sample(ivol)%center(:) = sphere_center(:,ivol)
        sample(ivol)%radius    = sphere_radius(ivol)

        call lmpi_bcast( sample(ivol)%center )
        call lmpi_bcast( sample(ivol)%radius )

        if ( sample(ivol)%radius <= zero ) then
          if (lmpi_master)                                                     &
            write(*,*) 'User defined sphere must have positive radius...'
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

      case ('cylinder')
! samples a cylindrical surface.
! It requires \var{cylinder_face1}, \var{cylinder_face2},
! and \var{cylinder_radius}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Cylinder ',ivol
        endif

        call lmpi_bcast( cylinder_face1  )
        call lmpi_bcast( cylinder_face2  )
        call lmpi_bcast( cylinder_radius )

        sample(ivol)%p1_cylinder(:) = cylinder_face1(:,ivol)
        sample(ivol)%p2_cylinder(:) = cylinder_face2(:,ivol)
        sample(ivol)%r_cylinder     = cylinder_radius(ivol)

        if ( sample(ivol)%r_cylinder <= zero ) then
          if (lmpi_master)                                                     &
            write(*,*) 'User defined cylinder must have positive radius...'
          if (lmpi_master)                                                     &
            write(*,*) 'sample index ',ivol, ',  r=', sample(ivol)%r_cylinder
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

        call lmpi_bcast( sample(ivol)%p1_cylinder )
        call lmpi_bcast( sample(ivol)%p2_cylinder )
        call lmpi_bcast( sample(ivol)%r_cylinder  )

      case ('cone')
! samples a conic surface.
! It requires \var{cone_face1}, \var{cone_face2},
! \var{cone_radius1}, and \var{cone_radius2}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Cone ',ivol
        endif

        angle = 0.0_dp
        cossq = 0.0_dp

        call lmpi_bcast( cone_face1   )
        call lmpi_bcast( cone_face2   )
        call lmpi_bcast( cone_radius1 )
        call lmpi_bcast( cone_radius2 )

        sample(ivol)%r1_cone    = cone_radius1(ivol)
        sample(ivol)%r2_cone    = cone_radius2(ivol)
        sample(ivol)%p1_cone(:) = cone_face1(:,ivol)
        sample(ivol)%p2_cone(:) = cone_face2(:,ivol)

        if ( sample(ivol)%r1_cone < zero .or.                                  &
             sample(ivol)%r2_cone < zero ) then
          if (lmpi_master)                                                     &
            write(*,*) 'User defined cone must have positive radii...'
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

        if ( sample(ivol)%r1_cone == zero .and.                                &
             sample(ivol)%r2_cone == zero ) then
          if (lmpi_master)                                                     &
            write(*,*) 'cone must have at least one positive radius'
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

        dist = sqrt(                                                           &
               (cone_face2(1,ivol)-cone_face1(1,ivol))**2                      &
             + (cone_face2(2,ivol)-cone_face1(2,ivol))**2                      &
             + (cone_face2(3,ivol)-cone_face1(3,ivol))**2 )

        if ( dist > tiny(0.0_dp) ) then
!         cone vertex angle
          angle = atan((cone_radius2(ivol)-cone_radius1(ivol))/dist)
          cossq = cos(angle)*cos(angle)
!         cone axis direction
          n     = (cone_face2(:,ivol) - cone_face1(:,ivol))/dist
        else
          if (lmpi_master) then
            write(*,*) &
              'Cone can not have an angle equal or greater than 90 degrees'
            write(*,*) 'Distance = ', dist
            write(*,*) 'Angle = ', angle*180.0/3.1416, '[deg]'
          end if
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

        if ( abs(cone_face2(1,ivol)-cone_face1(1,ivol)) > tiny(0.0_dp) ) then
          tparm(1)=(cone_radius2(ivol)-cone_radius1(ivol))                     &
                  /(cone_face2(1,ivol)-cone_face1(1,ivol))
        else
          tparm(1)= huge(1.0_dp)
        endif
        if ( abs(cone_face2(2,ivol)-cone_face1(2,ivol)) > tiny(0.0_dp) ) then
          tparm(2)=(cone_radius2(ivol)-cone_radius1(ivol))                     &
                  /(cone_face2(2,ivol)-cone_face1(2,ivol))
        else
          tparm(2)= huge(1.0_dp)
        endif
        if ( abs(cone_face2(3,ivol)-cone_face1(3,ivol)) > tiny(0.0_dp) ) then
          tparm(3)=(cone_radius2(ivol)-cone_radius1(ivol))                     &
                  /(cone_face2(3,ivol)-cone_face1(3,ivol))
        else
          tparm(3)= huge(1.0_dp)
        endif

        if (        (tparm(1)  < huge(1.0_dp))                                 &
          .and. (abs(tparm(1)) > tiny(0.0_dp)) ) then
          sample(ivol)%p0_cone(1) = cone_face1(1,ivol)                         &
                                     - cone_radius1(ivol)/tparm(1)
        else if ( tparm(1) > huge(1.0_dp) ) then
          sample(ivol)%p0_cone(1) = cone_face1(1,ivol)
        else if ( abs(tparm(1)) < tiny(0.0_dp) ) then
          sample(ivol)%p0_cone(1) = huge(1.0_dp)
        endif

        if (        (tparm(2)  < huge(1.0_dp))                                 &
          .and. (abs(tparm(2)) > tiny(0.0_dp)) ) then
          sample(ivol)%p0_cone(2) = cone_face1(2,ivol)                         &
                                     - cone_radius1(ivol)/tparm(2)
        else if ( tparm(2) > huge(1.0_dp) ) then
          sample(ivol)%p0_cone(2) = cone_face1(2,ivol)
        else if ( abs(tparm(2)) < tiny(0.0_dp) ) then
          sample(ivol)%p0_cone(2) = huge(1.0_dp)
        endif

        if (        (tparm(3)  < huge(1.0_dp))                                 &
          .and. (abs(tparm(3)) > tiny(0.0_dp)) ) then
          sample(ivol)%p0_cone(3) = cone_face1(1,ivol)                         &
                                     - cone_radius1(ivol)/tparm(3)
        else if ( tparm(3) > huge(1.0_dp) ) then
          sample(ivol)%p0_cone(3) = cone_face1(3,ivol)
        else if ( abs(tparm(3)) < tiny(0.0_dp) ) then
          sample(ivol)%p0_cone(3) = huge(1.0_dp)
        endif

        sample(ivol)%cossq  = cossq
        sample(ivol)%n      = n

        call lmpi_bcast( sample(ivol)%p0_cone )
        sample(ivol)%vertex = sample(ivol)%p0_cone

        call lmpi_bcast( sample(ivol)%vertex  )
        call lmpi_bcast( sample(ivol)%cossq   )
        call lmpi_bcast( sample(ivol)%n       )

      case ('plane')
! samples a plane.
! It requires \var{plane_center} and \var{plane_normal}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Plane ',ivol
        endif

        call lmpi_bcast( plane_center )
        call lmpi_bcast( plane_normal )

        sample(ivol)%po(:) = plane_center(:,ivol)
        sample(ivol)%n(:)  = plane_normal(:,ivol)

        call lmpi_bcast( sample(ivol)%po )
        call lmpi_bcast( sample(ivol)%n )

        if ( sample(ivol)%n(1) == zero .and.                                   &
             sample(ivol)%n(2) == zero .and.                                   &
             sample(ivol)%n(3) == zero ) then
          if (lmpi_master)                                                     &
            write(*,*) 'User defined plane normal must be non-zero..........'
          if (lmpi_master)                                                     &
            write(*,*) 'sample index ',ivol, ',  n=', sample(ivol)%n
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

      case ('quad')
! samples a quadrilateral.
! It requires \var{corner1}, \var{corner2}, \var{corner3}, \var{corner4},
! and \var{window_normal}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Quad ',ivol
        endif

        call lmpi_bcast( corner1 )
        call lmpi_bcast( corner2 )
        call lmpi_bcast( corner3 )
        call lmpi_bcast( corner4 )

        sample(ivol)%p1(:) = corner1(:,ivol)
        sample(ivol)%p2(:) = corner2(:,ivol)
        sample(ivol)%p3(:) = corner3(:,ivol)
        sample(ivol)%p4(:) = corner4(:,ivol)
        call lmpi_bcast( sample(ivol)%p1 )
        call lmpi_bcast( sample(ivol)%p2 )
        call lmpi_bcast( sample(ivol)%p3 )
        call lmpi_bcast( sample(ivol)%p4 )
        p1 = sample(ivol)%p1(:)
        p2 = sample(ivol)%p2(:)
        p3 = sample(ivol)%p3(:)
        sample(ivol)%window_normal = get_area_normal ( p1, p2, p3 )
        sample(ivol)%window_normal = sample(ivol)%window_normal                &
          / sqrt( dot_product( sample(ivol)%window_normal,                     &
                               sample(ivol)%window_normal ) )
        call lmpi_bcast( sample(ivol)%window_normal )
        if ( lmpi_master ) write(*,'(a,3(1x,f12.5))')                          &
          ' Quad window normal = ',sample(ivol)%window_normal

        call is_quad_flat(sample(ivol),flat)

        if ( .not.flat )  then
          if ( lmpi_master ) then
            write(*,*)                                                         &
            'User defined quad must have non-zero area and/or be co-planar'
            write(*,*) 'sample index ',ivol
          endif
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

      case ('circle')
! samples a circle.
! It requires \var{circle_center}, \var{circle_normal}, and \var{circle_radius}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Circle ',ivol
        endif

        call lmpi_bcast( circle_center )
        call lmpi_bcast( circle_normal )
        call lmpi_bcast( circle_radius )


        sample(ivol)%po(:) = circle_center(:,ivol)
        sample(ivol)%n(:)  = circle_normal(:,ivol)
        sample(ivol)%r0    = circle_radius(ivol)

        if ( sample(ivol)%r0 <= 0.0_dp )  then
          if (lmpi_master)                                                     &
            write(*,*) 'User defined circle must have a positive radius'
          if (lmpi_master)                                                     &
            write(*,*) 'sample index ',ivol, ',  r=', sample(ivol)%r0
          call lmpi_conditional_stop(1,'&sampling_parameters namelist error')
        endif

        call lmpi_bcast( sample(ivol)%po )
        call lmpi_bcast( sample(ivol)%n  )
        call lmpi_bcast( sample(ivol)%r0 )

      case ('line')  ! line
! is line sampling, which requires \var{p1_line} and \var{p2_line}.

        if ( lmpi_master ) then
          write(*,*) 'Found user-defined sampling type: Line ',ivol
        endif

        call lmpi_bcast( p1_line )
        call lmpi_bcast( p2_line )
        call lmpi_bcast( boundary_point )
        call lmpi_bcast( boundary )

        sample(ivol)%p1_line(:)      = p1_line(:,ivol)
        sample(ivol)%p2_line(:)      = p2_line(:,ivol)

        call lmpi_bcast( sample(ivol)%p1_line )
        call lmpi_bcast( sample(ivol)%p2_line )

      case default

        if (lmpi_master) then
          write(*,*) '&sampling_parameters: Unknown type_of_geometry: '        &
                     //trim(sample(ivol)%geo_type_name)
        endif

        call lmpi_conditional_stop(1,'&sampling_parameters namelist error')

      end select

      call lmpi_conditional_stop(0,'&sampling_parameters namelist error')

    end do read_geo_data

  end subroutine read_nml_sampling_parameters

!=========================== INITIALIZE_TYPE =================================80
!
!  Initialize variables
!
!=============================================================================80

  subroutine initialize_type(i)

    use sampling_headers, only : sample

    integer, intent(in) :: i

  continue

    sample(i)%geo_type = 0

    sample(i)%point_list(:,:) = 0.0_dp
    sample(i)%print_list(:,:) = 0.0_dp
    sample(i)%lower_corner(:) = 0.0_dp
    sample(i)%upper_corner(:) = 0.0_dp
    sample(i)%po(:) = 0.0_dp
    sample(i)%p1(:) = 0.0_dp
    sample(i)%p2(:) = 0.0_dp
    sample(i)%p3(:) = 0.0_dp
    sample(i)%p4(:) = 0.0_dp
    sample(i)%p5(:) = 0.0_dp
    sample(i)%p6(:) = 0.0_dp
    sample(i)%p7(:) = 0.0_dp
    sample(i)%p8(:) = 0.0_dp
    sample(i)%n(:)  = 0.0_dp
    sample(i)%r0    = 0.0_dp
    sample(i)%r1    = 0.0_dp

    sample(i)%x0_sphere = 0.0_dp
    sample(i)%y0_sphere = 0.0_dp
    sample(i)%z0_sphere = 0.0_dp
    sample(i)%r0_sphere = 0.0_dp

    sample(i)%p0_cone(:) = 0.0_dp
    sample(i)%p1_cone(:) = 0.0_dp
    sample(i)%p2_cone(:) = 0.0_dp
    sample(i)%r1_cone    = 0.0_dp
    sample(i)%r2_cone    = 0.0_dp
    sample(i)%vertex(:)  = 0.0_dp

    sample(i)%x0_plane  = 0.0_dp
    sample(i)%y0_plane  = 0.0_dp
    sample(i)%z0_plane  = 0.0_dp
    sample(i)%nx_plane  = 0.0_dp
    sample(i)%ny_plane  = 0.0_dp
    sample(i)%nz_plane  = 0.0_dp

    sample(i)%schlieren_aspect    = ''
    sample(i)%number_of_rows      = 0
    sample(i)%number_of_columns   = 0
    sample(i)%window_height       = 0.0_dp
    sample(i)%window_width        = 0.0_dp
    sample(i)%window_center       = 0.0_dp
    sample(i)%model_center        = 0.0_dp
    sample(i)%window_normal       = 0.0_dp
    sample(i)%window_box          = 0.0_dp
    sample(i)%delta_x             = 0.0_dp
    sample(i)%delta_y             = 0.0_dp
    sample(i)%l2c_local_image     => null()
    sample(i)%blanking_list       => null()
    sample(i)%frequency           = 0
    sample(i)%final_write         = .false.
    sample(i)%strands             = .true.
    sample(i)%patch_list          => null()

  end subroutine initialize_type

end module sampling_main

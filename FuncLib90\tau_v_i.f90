!================================= TAU_V_I ===================================80
!
! Full viscous fluxes for incompressible.
!
! Incoming variables and gradients are primitive variables (var1,u,v,w).
!
!=============================================================================80
  pure function tau_v_i( xnorm, ynorm, znorm, area,                            &
                         qtgrad, amut )

    use flux_constants,        only : xmr, c43, c23

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, amut

    real(dp), dimension(3,4), intent(in) :: qtgrad

    real(dp), dimension(4) :: tau_v_i

    real(dp) :: mu, muta, t_xx, t_yy, t_zz, t_xy, t_xz, t_yz

  continue

!   xmr   = xmach/re
!   c43   = xmr*4._dp/3._dp
!   c23   = xmr*2._dp/3._dp

    mu     = 1._dp
    muta   = (     mu +      amut )*area

!   t_xx =   c43*ux - c23*vy - c23*wz
!   t_yy = - c23*ux + c43*vy - c23*wz
!   t_zz = - c23*ux - c23*vy + c43*wz

!   t_xy = xmr*(uy + vx)
!   t_xz = xmr*(uz + wx)

!   t_yz = xmr*(vz + wy)

    t_xx =   c43*qtgrad(1,2) - c23*qtgrad(2,3) - c23*qtgrad(3,4)
    t_yy = - c23*qtgrad(1,2) + c43*qtgrad(2,3) - c23*qtgrad(3,4)
    t_zz = - c23*qtgrad(1,2) - c23*qtgrad(2,3) + c43*qtgrad(3,4)

    t_xy = xmr*(qtgrad(2,2) + qtgrad(1,3))
    t_xz = xmr*(qtgrad(3,2) + qtgrad(1,4))

    t_yz = xmr*(qtgrad(3,3) + qtgrad(2,4))

    tau_v_i(1) = 0._dp
    tau_v_i(2) = -muta*(xnorm*t_xx + ynorm*t_xy + znorm*t_xz)
    tau_v_i(3) = -muta*(xnorm*t_xy + ynorm*t_yy + znorm*t_yz)
    tau_v_i(4) = -muta*(xnorm*t_xz + ynorm*t_yz + znorm*t_zz)

  end function tau_v_i

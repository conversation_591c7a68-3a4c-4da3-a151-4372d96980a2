! Provides generic interface for allocating, reallocating, and deallocating
! memory
!
! Note: using min0 specific-named function rather than generic form 'min'
!       to facilitate automatic conversion of this routine to complex
!       variables version.

module allocations

  implicit none

  private

  public :: my_alloc_ptr
  public :: my_realloc_ptr
  public :: inc_alloc ! increment allocation by 1

  interface my_alloc_ptr

    module procedure logical_my_alloc_ptr1
    module procedure logical_my_alloc_ptr2
    module procedure logical_my_alloc_ptr3
    module procedure logical_my_alloc_ptr4

    module procedure integer_1_my_alloc_ptr1
    module procedure integer_1_my_alloc_ptr2

    module procedure integer_2_my_alloc_ptr1

    module procedure integer_4_my_alloc_ptr1
    module procedure integer_4_my_alloc_ptr2
    module procedure integer_4_my_alloc_ptr3
    module procedure integer_4_my_alloc_ptr4
    module procedure integer_4_my_alloc_ptr5

    module procedure integer_8_my_alloc_ptr1
    module procedure integer_8_my_alloc_ptr2
    module procedure integer_8_my_alloc_ptr3

    module procedure single_my_alloc_ptr1
    module procedure single_my_alloc_ptr2
    module procedure single_my_alloc_ptr3
    module procedure single_my_alloc_ptr4
    module procedure single_my_alloc_ptr5

    module procedure double_my_alloc_ptr1
    module procedure double_my_alloc_ptr2
    module procedure double_my_alloc_ptr3
    module procedure double_my_alloc_ptr4
    module procedure double_my_alloc_ptr5

    module procedure single_complx_my_alloc_ptr1
    module procedure single_complx_my_alloc_ptr2
    module procedure single_complx_my_alloc_ptr3
    module procedure single_complx_my_alloc_ptr4
    module procedure single_complx_my_alloc_ptr5

    module procedure double_complx_my_alloc_ptr1
    module procedure double_complx_my_alloc_ptr2
    module procedure double_complx_my_alloc_ptr3
    module procedure double_complx_my_alloc_ptr4
    module procedure double_complx_my_alloc_ptr5

    module procedure char_my_alloc_ptrn

  end interface

  interface inc_alloc

    module procedure log1_inc_alloc

    module procedure int1_inc_alloc
    module procedure int2_inc_alloc

    module procedure real1_inc_alloc
    module procedure dbl1_inc_alloc
    module procedure dbl2_inc_alloc

    module procedure complxr41_inc_alloc
    module procedure complxr81_inc_alloc
    module procedure complxr42_inc_alloc
    module procedure complxr82_inc_alloc

    module procedure charn_inc_alloc

  end interface

  interface my_realloc_ptr

    module procedure logical_my_realloc_ptr1
    module procedure logical_my_realloc_ptr2

    module procedure integer_1_my_realloc_ptr1
    module procedure integer_1_my_realloc_ptr2

    module procedure integer_2_my_realloc_ptr1

    module procedure integer_my_realloc_ptr1
    module procedure integer_my_realloc_ptr2
    module procedure integer_my_realloc_ptr3

    module procedure single_my_realloc_ptr1
    module procedure single_my_realloc_ptr2
    module procedure single_my_realloc_ptr3

    module procedure double_my_realloc_ptr1
    module procedure double_my_realloc_ptr2
    module procedure double_my_realloc_ptr3

    module procedure complx_r4_my_realloc_ptr1
    module procedure complx_r4_my_realloc_ptr2
    module procedure complx_r4_my_realloc_ptr3

    module procedure complx_r8_my_realloc_ptr1
    module procedure complx_r8_my_realloc_ptr2
    module procedure complx_r8_my_realloc_ptr3

    module procedure char_my_realloc_ptrn
    module procedure char_my_realloc_ptrn_2

  end interface

contains


!=========================== LOGICAL_MY_REALLOC_PTR1 =========================80
!
! Reallocate space for rank 1 logical arrays
!
!=============================================================================80

  subroutine logical_my_realloc_ptr1(x,dim1)

    logical, dimension(:), pointer    :: x
    integer,               intent(in) :: dim1

    logical, dimension(:), pointer :: y

    integer :: old_dim1, ierr, i1, n1

  continue

!   allocate the final array with the new dimension

    allocate(y(max(1,dim1)),stat=ierr)
    if(ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = .FALSE.
    old_dim1 = size(x,1)
    n1 = min0(dim1,old_dim1)
    do i1 = 1, n1
      y(i1) = x(i1)
    end do

!   having allocated the required memory for y, now release the memory used
!   by the array x.

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine logical_my_realloc_ptr1

!=========================== LOGICAL_MY_REALLOC_PTR2 =========================80
!
! Reallocate space for rank 2 logical arrays
!
!=============================================================================80

  subroutine logical_my_realloc_ptr2(x,dim1,dim2)

    logical, dimension(:,:), pointer     :: x
    integer,                 intent (in) :: dim1, dim2

    logical, dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i1, n1, i2, n2

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = .false.
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    do i2 = 1, n2
      do i1 = 1, n1
        y(i1,i2) = x(i1,i2)
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine logical_my_realloc_ptr2


!=========================== INTEGER_1_MY_REALLOC_PTR1 =======================80
!
! Reallocate space for rank 1 integer arrays
!
!=============================================================================80

  subroutine integer_1_my_realloc_ptr1(x,dim1)

    use kinddefs, only : system_i1

    integer(system_i1), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer(system_i1), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i1, n1

  continue

!   allocate the final array with the new dimension

    allocate(y(max(1,dim1)),stat=ierr)
    if(ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    n1 = min0(dim1,old_dim1)
    do i1 = 1, n1
      y(i1) = x(i1)
    end do

!   having allocated the required memory for y, now release the memory used
!   by the array x.

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine integer_1_my_realloc_ptr1


!=========================== INTEGER_1_MY_REALLOC_PTR2 =======================80
!
! Reallocate space for rank 2 integer arrays
!
!=============================================================================80

  subroutine integer_1_my_realloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_i1

    integer(system_i1), dimension(:,:), pointer     :: x
    integer,                            intent (in) :: dim1, dim2

    integer(system_i1), dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i1, n1, i2, n2

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0_system_i1
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    do i2 = 1, n2
      do i1 = 1, n1
        y(i1,i2) = x(i1,i2)
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine integer_1_my_realloc_ptr2


!=========================== INTEGER_2_MY_REALLOC_PTR1 =======================80
!
! Reallocate space for rank 1 integer arrays
!
!=============================================================================80

  subroutine integer_2_my_realloc_ptr1(x,dim1)

    use kinddefs, only : system_i2

    integer(system_i2), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer(system_i2), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i1, n1

  continue

!   allocate the final array with the new dimension

    allocate(y(max(1,dim1)),stat=ierr)
    if(ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    n1 = min0(dim1,old_dim1)
    do i1 = 1, n1
      y(i1) = x(i1)
    end do

!   having allocated the required memory for y, now release the memory used
!   by the array x.

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine integer_2_my_realloc_ptr1


!=========================== INTEGER_MY_REALLOC_PTR1 =========================80
!
! Reallocate space for rank 1 integer arrays
!
!=============================================================================80

  subroutine integer_my_realloc_ptr1(x,dim1)

    integer, dimension(:), pointer    :: x
    integer,               intent(in) :: dim1

    integer, dimension(:), pointer :: y

    integer :: old_dim1, ierr, i1, n1

  continue

!   allocate the final array with the new dimension

    allocate(y(max(1,dim1)),stat=ierr)
    if(ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    n1 = min0(dim1,old_dim1)
    do i1 = 1, n1
      y(i1) = x(i1)
    end do

!   having allocated the required memory for y, now release the memory used
!   by the array x.

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine integer_my_realloc_ptr1


!=========================== INTEGER_MY_REALLOC_PTR2 =========================80
!
! Reallocate space for rank 2 integer arrays
!
!=============================================================================80

  subroutine integer_my_realloc_ptr2(x,dim1,dim2)

    integer, dimension(:,:), pointer     :: x
    integer,                 intent (in) :: dim1, dim2

    integer, dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i1, n1, i2, n2

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    do i2 = 1, n2
      do i1 = 1, n1
        y(i1,i2) = x(i1,i2)
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine integer_my_realloc_ptr2


!=========================== INTEGER_MY_REALLOC_PTR3 =========================80
!
! Reallocate space for rank 3 integer arrays
!
!=============================================================================80

  subroutine integer_my_realloc_ptr3(x,dim1,dim2,dim3)

    integer, dimension(:,:,:), pointer     :: x
    integer,                   intent (in) :: dim1, dim2, dim3

    integer, dimension(:,:,:), pointer :: y

    integer :: old_dim1, old_dim2, old_dim3, ierr, i1, n1, i2, n2, i3, n3

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    old_dim3 = size(x,3)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    n3 = min0(dim3,old_dim3)
    do i3 = 1, n3
      do i2 = 1, n2
        do i1 = 1, n1
          y(i1,i2,i3) = x(i1,i2,i3)
        end do
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine integer_my_realloc_ptr3


!=========================== SINGLE_MY_REALLOC_PTR1 ==========================80
!
! Reallocate space for rank 1 single precision arrays
!
!=============================================================================80

  subroutine single_my_realloc_ptr1(x,dim1)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:), pointer    :: x
    integer,                       intent(in) :: dim1

    real(system_r4), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i1, n1

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0.0_system_r4
    old_dim1 = size(x,1)
    n1 = min0(dim1,old_dim1)
    do i1 = 1, n1
      y(i1) = x(i1)
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine single_my_realloc_ptr1


!=========================== SINGLE_MY_REALLOC_PTR2 ==========================80
!
! Reallocate space for rank 2 single arrays
!
!=============================================================================80

  subroutine single_my_realloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:,:), pointer    :: x
    integer,                         intent(in) :: dim1, dim2

    real(system_r4), dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i1, n1, i2, n2

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    y = 0.0_system_r4
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    do i2 = 1, n2
      do i1 = 1, n1
        y(i1,i2) = x(i1,i2)
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine single_my_realloc_ptr2


!=========================== SINGLE_MY_REALLOC_PTR3 ==========================80
!
! Reallocate space for rank 3 single arrays
!
!=============================================================================80

  subroutine single_my_realloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:,:,:), pointer    :: x
    integer,                           intent(in) :: dim1, dim2, dim3

    real(system_r4), dimension(:,:,:), pointer :: y

    integer :: old_dim1, old_dim2, old_dim3, ierr, i1, n1, i2, n2, i3, n3

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    y = 0.0_system_r4
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    old_dim3 = size(x,3)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    n3 = min0(dim3,old_dim3)
    do i3 = 1, n3
      do i2 = 1, n2
        do i1 = 1, n1
          y(i1,i2,i3) = x(i1,i2,i3)
        end do
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine single_my_realloc_ptr3


!============================ CHAR_MY_REALLOC_PTRN ===========================80
!
! Reallocate space for n-character arrays
!
!=============================================================================80

  subroutine char_my_realloc_ptrn(x,n,dim1)

    integer,                    intent(in) :: dim1, n
    character(n), dimension(:), pointer    :: x

    character(n), dimension(:), pointer :: y

    integer :: status, old_dim1, i

  continue

!   allocate the final array with the new dimensions

    allocate (y(max(1,dim1)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    old_dim1 = size(x,1)
    i = min0(dim1,old_dim1)
    y = " "
    y(1:i) = x(1:i)

    x => y ! reassign the x pointer

  end subroutine char_my_realloc_ptrn


!============================ CHAR_MY_ALLOC_PTRN_2 ===========================80
!
! Allocate space for two-dimensional n-character arrays
!
!=============================================================================80

  subroutine char_my_realloc_ptrn_2(x,n,dim1,dim2)

    integer,                      intent(in) :: dim1, dim2, n
    character(n), dimension(:,:), pointer    :: x

    character(n), dimension(:,:), pointer :: y

    integer :: status, old_dim1, old_dim2, i, j

  continue

!   allocate the final array with the new dimensions

    allocate (y(max(1,dim1),max(1,dim2)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    i = min0(dim1,old_dim1)
    j = min0(dim2,old_dim2)
    y = " "
    y(1:i,1:j) = x(1:i,1:j)

    x => y ! reassign the x pointer

  end subroutine char_my_realloc_ptrn_2


!=========================== DOUBLE_MY_REALLOC_PTR1 ==========================80
!
! Reallocate space for rank 1 double precision arrays
!
!=============================================================================80

  subroutine double_my_realloc_ptr1(x,dim1)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:), pointer    :: x
    integer,                       intent(in) :: dim1

    real(system_r8), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i1, n1

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0.0_system_r8
    old_dim1 = size(x,1)
    n1 = min0(dim1,old_dim1)
    do i1 = 1, n1
      y(i1) = x(i1)
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine double_my_realloc_ptr1


!=========================== DOUBLE_MY_REALLOC_PTR2 ==========================80
!
! Reallocate space for rank 2 double arrays
!
!=============================================================================80

  subroutine double_my_realloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:), pointer    :: x
    integer,                         intent(in) :: dim1, dim2

    real(system_r8), dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i1, n1, i2, n2

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0.0_system_r8
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    do i2 = 1, n2
      do i1 = 1, n1
        y(i1,i2) = x(i1,i2)
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine double_my_realloc_ptr2


!=========================== DOUBLE_MY_REALLOC_PTR3 ==========================80
!
! Reallocate space for rank 3 double arrays
!
!=============================================================================80

  subroutine double_my_realloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:,:), pointer    :: x
    integer,                           intent(in) :: dim1, dim2, dim3

    real(system_r8), dimension(:,:,:), pointer :: y

    integer :: old_dim1, old_dim2, old_dim3, ierr, i1, n1, i2, n2, i3, n3

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0.0_system_r8
    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    old_dim3 = size(x,3)
    n1 = min0(dim1,old_dim1)
    n2 = min0(dim2,old_dim2)
    n3 = min0(dim3,old_dim3)
    do i3 = 1, n3
      do i2 = 1, n2
        do i1 = 1, n1
          y(i1,i2,i3) = x(i1,i2,i3)
        end do
      end do
    end do

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: deallocate failed in realloc"

    x => y ! reassign the x pointer

  end subroutine double_my_realloc_ptr3


!========================== INTEGER_1_MY_ALLOC_PTR1 ==========================80
!
! Allocate space for rank 1 integer kind 1 arrays
!
!=============================================================================80

  subroutine integer_1_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_i1

    integer(system_i1), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_1_my_alloc_ptr1

!========================== INTEGER_2_MY_ALLOC_PTR1 ==========================80
!
! Allocate space for rank 1 integer kind 1 arrays
!
!=============================================================================80

  subroutine integer_2_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_i2

    integer(system_i2), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_2_my_alloc_ptr1

!========================== INTEGER_8_MY_ALLOC_PTR1 ==========================80
!
! Allocate space for rank 1 integer kind 1 arrays
!
!=============================================================================80

  subroutine integer_8_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_i8

    integer(system_i8), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_8_my_alloc_ptr1

!============================ INTEGER_1_MY_ALLOC_PTR2 ========================80
!
! Allocate space for rank 2 integer kind 1 arrays
!
!=============================================================================80

  subroutine integer_1_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_i1

    integer(system_i1), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_1_my_alloc_ptr2

!============================ INTEGER_8_MY_ALLOC_PTR2 ========================80
!
! Allocate space for rank 2 integer kind 1 arrays
!
!=============================================================================80

  subroutine integer_8_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_i8

    integer(system_i8), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_8_my_alloc_ptr2

!============================ INTEGER_8_MY_ALLOC_PTR2 ========================80
!
! Allocate space for rank 2 integer kind 1 arrays
!
!=============================================================================80

  subroutine integer_8_my_alloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_i8

    integer(system_i8), dimension(:,:,:), pointer    :: x
    integer,                              intent(in) :: dim1, dim2, dim3

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_8_my_alloc_ptr3

!============================ INTEGER_4_MY_ALLOC_PTR1 ========================80
!
! Allocate space for rank 1 integer arrays
!
!=============================================================================80

  subroutine integer_4_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_i4

    integer(system_i4), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_4_my_alloc_ptr1


!============================ INTEGER_4_MY_ALLOC_PTR2 ========================80
!
! Allocate space for rank 2 integer arrays
!
!=============================================================================80

  subroutine integer_4_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_i4

    integer(system_i4), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_4_my_alloc_ptr2


!============================ INTEGER_4_MY_ALLOC_PTR3 ========================80
!
! Allocate space for rank 3 integer arrays
!
!=============================================================================80

  subroutine integer_4_my_alloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_i4

    integer(system_i4), dimension(:,:,:), pointer    :: x
    integer,                              intent(in) :: dim1, dim2, dim3

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_4_my_alloc_ptr3


!============================ INTEGER_4_MY_ALLOC_PTR4 ========================80
!
! Allocate space for rank 4 integer arrays
!
!=============================================================================80

  subroutine integer_4_my_alloc_ptr4(x,dim1,dim2,dim3,dim4)

    use kinddefs, only : system_i4

    integer(system_i4), dimension(:,:,:,:), pointer    :: x
    integer,                                intent(in) :: dim1, dim2, dim3, dim4

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_4_my_alloc_ptr4


!============================ INTEGER_4_MY_ALLOC_PTR5 ========================80
!
! Allocate space for rank 5 integer arrays
!
!=============================================================================80

  subroutine integer_4_my_alloc_ptr5(x,dim1,dim2,dim3,dim4,dim5)

    use kinddefs, only : system_i4

    integer(system_i4), dimension(:,:,:,:,:), pointer    :: x
    integer,                                  intent(in) :: dim1, dim2, dim3
    integer,                                  intent(in) :: dim4, dim5

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4),                &
               max(1,dim5)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine integer_4_my_alloc_ptr5


!============================ SINGLE_MY_ALLOC_PTR1 ===========================80
!
! Allocate space for rank 1 single precision real arrays
!
!=============================================================================80

  subroutine single_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:), pointer    :: x
    integer,                       intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r4

  end subroutine single_my_alloc_ptr1


!============================ SINGLE_MY_ALLOC_PTR2 ===========================80
!
! Allocate space for rank 2 single precision real arrays
!
!=============================================================================80

  subroutine single_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:,:), pointer    :: x
    integer,                         intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r4

  end subroutine single_my_alloc_ptr2


!============================ SINGLE_MY_ALLOC_PTR3 ===========================80
!
! Allocate space for rank 3 single precision real arrays
!
!=============================================================================80

  subroutine single_my_alloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:,:,:), pointer    :: x
    integer,                           intent(in) :: dim1, dim2, dim3

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r4

  end subroutine single_my_alloc_ptr3


!============================ SINGLE_MY_ALLOC_PTR4 ===========================80
!
! Allocate space for rank 4 single precision real arrays
!
!=============================================================================80

  subroutine single_my_alloc_ptr4(x,dim1,dim2,dim3,dim4)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:,:,:,:), pointer    :: x
    integer,                             intent(in) :: dim1, dim2
    integer,                             intent(in) :: dim3,dim4

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r4

  end subroutine single_my_alloc_ptr4


!============================ SINGLE_MY_ALLOC_PTR5 ===========================80
!
! Allocate space for rank 5 single precision real arrays
!
!=============================================================================80

  subroutine single_my_alloc_ptr5(x,dim1,dim2,dim3,dim4,dim5)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:,:,:,:,:), pointer    :: x
    integer,                               intent(in) :: dim1, dim2
    integer,                               intent(in) :: dim3, dim4
    integer,                               intent(in) :: dim5

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4),                &
               max(1,dim5)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r4

  end subroutine single_my_alloc_ptr5


!============================ DOUBLE_MY_ALLOC_PTR1 ===========================80
!
! Allocate space for rank 1 double precision real arrays
!
!=============================================================================80

  subroutine double_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:), pointer    :: x
    integer,                       intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r8

  end subroutine double_my_alloc_ptr1


!============================ DOUBLE_MY_ALLOC_PTR2 ===========================80
!
! Allocate space for rank 2 double precision real arrays
!
!=============================================================================80

  subroutine double_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:), pointer    :: x
    integer,                         intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r8

  end subroutine double_my_alloc_ptr2


!============================ DOUBLE_MY_ALLOC_PTR3 ===========================80
!
! Allocate space for rank 3 double precision real arrays
!
!=============================================================================80

  subroutine double_my_alloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:,:), pointer    :: x
    integer,                           intent(in) :: dim1, dim2, dim3

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r8

  end subroutine double_my_alloc_ptr3


!============================ DOUBLE_MY_ALLOC_PTR4 ===========================80
!
! Allocate space for rank 4 double precision real arrays
!
!=============================================================================80

  subroutine double_my_alloc_ptr4(x,dim1,dim2,dim3,dim4)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:,:,:), pointer    :: x
    integer,                             intent(in) :: dim1, dim2
    integer,                             intent(in) :: dim3,dim4

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r8

  end subroutine double_my_alloc_ptr4


!============================ DOUBLE_MY_ALLOC_PTR5 ===========================80
!
! Allocate space for rank 5 double precision real arrays
!
!=============================================================================80

  subroutine double_my_alloc_ptr5(x,dim1,dim2,dim3,dim4,dim5)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:,:,:,:), pointer    :: x
    integer,                               intent(in) :: dim1,dim2
    integer,                               intent(in) :: dim3,dim4
    integer,                               intent(in) :: dim5

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4),                &
               max(1,dim5)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0.0_system_r8

  end subroutine double_my_alloc_ptr5


!============================ CHAR_MY_ALLOC_PTRN =============================80
!
! Allocate space for n-character arrays
!
!=============================================================================80

  subroutine char_my_alloc_ptrn(x,n,dim1)

    integer,                    intent(in) :: dim1, n
    character(n), dimension(:), pointer    :: x

    integer :: status

  continue

    allocate (x(max(1,dim1)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"

    x = "                        "

  end subroutine char_my_alloc_ptrn


!=========================== LOGICAL_MY_ALLOC_PTR1 ===========================80
!
! Allocate space for rank 1 logical arrays
!
!=============================================================================80

  subroutine logical_my_alloc_ptr1(x,dim1)

    logical, dimension(:), pointer    :: x
    integer,               intent(in) :: dim1

    integer :: status

  continue

    allocate (x(max(1,dim1)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"
    x = .false.

  end subroutine logical_my_alloc_ptr1

!========================= LOGICAL_MY_ALLOC_PTR2 =============================80
!
! Allocate space for rank 2 logical arrays
!
!=============================================================================80

  subroutine logical_my_alloc_ptr2(x,dim1,dim2)

    logical, dimension(:,:), pointer    :: x
    integer,                 intent(in) :: dim1, dim2

    integer :: status

  continue

    allocate (x(max(1,dim1),max(1,dim2)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"
    x = .false.

  end subroutine logical_my_alloc_ptr2

!========================= LOGICAL_MY_ALLOC_PTR3 =============================80
!
! Allocate space for rank 3 logical arrays
!
!=============================================================================80

  subroutine logical_my_alloc_ptr3(x,dim1,dim2,dim3)

    logical, dimension(:,:,:), pointer    :: x
    integer,                   intent(in) :: dim1, dim2, dim3

    integer :: status

  continue

    allocate (x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"
    x = .false.

  end subroutine logical_my_alloc_ptr3

!========================= LOGICAL_MY_ALLOC_PTR4 =============================80
!
! Allocate space for rank 4 logical arrays
!
!=============================================================================80

  subroutine logical_my_alloc_ptr4(x,dim1,dim2,dim3,dim4)

    logical, dimension(:,:,:,:), pointer    :: x
    integer,                     intent(in) :: dim1, dim2, dim3, dim4

    integer :: status

  continue

    allocate (x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4)),stat=status)
    if (status /= 0) stop "fun3d: insufficient memory available"
    x = .false.

  end subroutine logical_my_alloc_ptr4


!============================== INT1_INC_ALLOC ===============================80
!
! Increment allocation by 1 for rank 1 integer arrays
!
!=============================================================================80

  subroutine int1_inc_alloc(integer1,length)

    integer, dimension(:), pointer    :: integer1
    integer,               intent(in) :: length

  continue

    call my_realloc_ptr(integer1,length+1)

  end subroutine int1_inc_alloc


!============================ LOG1_INC_ALLOC =================================80
!
! Increment allocation by 1 for rank 1 logical arrays
!
!=============================================================================80

  subroutine log1_inc_alloc(x,length)

    logical, dimension(:), pointer    :: x
    integer,               intent(in) :: length

  continue

    call my_realloc_ptr(x,length+1)

  end subroutine log1_inc_alloc


!============================== DBL1_INC_ALLOC ===============================80
!
! Increment allocation by 1 for rank 1 double arrays
!
!=============================================================================80

  subroutine dbl1_inc_alloc(real1,length)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:), pointer    :: real1
    integer,                       intent(in) :: length

  continue

    call my_realloc_ptr(real1,length+1)

  end subroutine dbl1_inc_alloc


!============================== REAL1_INC_ALLOC ==============================80
!
! Increment allocation by 1 for rank 1 real arrays
!
!=============================================================================80

  subroutine real1_inc_alloc(real1,length)

    use kinddefs, only : system_r4

    real(system_r4), dimension(:), pointer    :: real1
    integer,                       intent(in) :: length

  continue

    call my_realloc_ptr(real1,length+1)

  end subroutine real1_inc_alloc


!============================= COMPLXR81_INC_ALLOC ===========================80
!
! Increment allocation by 1 for rank 1 double complex arrays
!
!=============================================================================80

  subroutine complxr81_inc_alloc(real1,length)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:), pointer    :: real1
    integer,                          intent(in) :: length

  continue

    call my_realloc_ptr(real1,length+1)

  end subroutine complxr81_inc_alloc


!============================ COMPLXR41_INC_ALLOC ============================80
!
! Increment allocation by 1 for rank 1 real complex arrays
!
!=============================================================================80

  subroutine complxr41_inc_alloc(real1,length)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:), pointer    :: real1
    integer,                          intent(in) :: length

  continue

    call my_realloc_ptr(real1,length+1)

  end subroutine complxr41_inc_alloc


!============================= CHARN_INC_ALLOC ===============================80
!
! Increment allocation by 1 for rank 1 character(n) arrays
!
!=============================================================================80

  subroutine charn_inc_alloc(char1,n,length)

    integer,                    intent(in) :: n, length
    character(n), dimension(:), pointer    :: char1

  continue

    call my_realloc_ptr(char1,n,length+1)

  end subroutine charn_inc_alloc


!=============================== INT2_INC_ALLOC ==============================80
!
! Increment allocation by 1 for rank 2 type integer arrays
!
!=============================================================================80

  subroutine int2_inc_alloc(integer2,dim1,dim2,extent)

    integer, dimension(:,:), pointer    :: integer2
    integer,                 intent(in) :: dim1   ! first extent
    integer,                 intent(in) :: dim2   ! second extent
    integer,                 intent(in) :: extent ! incremented extent

  continue

    if (extent == 1) then
      call my_realloc_ptr(integer2,dim1+1,dim2)
    elseif (extent == 2) then
      call my_realloc_ptr(integer2,dim1,dim2+1)
    else
      write (6,*) "Illegal value for extent in call to inc_alloc"
      write (6,*) "extent = ", extent, "but value must be 1 or 2"
      stop
    end if

  end subroutine int2_inc_alloc


!=============================== DBL2_INC_ALLOC ==============================80
!
! Increment allocation by 1 for rank 2 double arrays
!
!=============================================================================80

  subroutine dbl2_inc_alloc(real2,dim1,dim2,extent)

    use kinddefs, only : system_r8

    real(system_r8), dimension(:,:), pointer    :: real2
    integer,                         intent(in) :: dim1   ! 1st extent
    integer,                         intent(in) :: dim2   ! 2nd extent
    integer,                         intent(in) :: extent ! inc extent

  continue

    if (extent == 1) then
      call my_realloc_ptr(real2,dim1+1,dim2)
    elseif (extent == 2) then
      call my_realloc_ptr(real2,dim1,dim2+1)
    else
      write (6,*) "Illegal value for extent in call to inc_alloc"
      write (6,*) "extent = ", extent, "but value must be 1 or 2"
      stop
    end if

  end subroutine dbl2_inc_alloc


!============================= COMPLXR82_INC_ALLOC ===========================80
!
! Increment allocation by 1 for rank 2 double complex arrays
!
!=============================================================================80

  subroutine complxr82_inc_alloc(real2,dim1,dim2,extent)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:), pointer    :: real2
    integer,                            intent(in) :: dim1
    integer,                            intent(in) :: dim2
    integer,                            intent(in) :: extent

  continue

    if (extent == 1) then
      call my_realloc_ptr(real2,dim1+1,dim2)
    elseif (extent == 2) then
      call my_realloc_ptr(real2,dim1,dim2+1)
    else
      write (6,*) "Illegal value for extent in call to inc_alloc"
      write (6,*) "extent = ", extent, "but value must be 1 or 2"
      stop
    end if

  end subroutine complxr82_inc_alloc


!============================= COMPLXR42_INC_ALLOC ===========================80
!
! Increment allocation by 1 for rank 2 double complex arrays
!
!=============================================================================80

  subroutine complxr42_inc_alloc(real2,dim1,dim2,extent)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:), pointer    :: real2
    integer,                            intent(in) :: dim1
    integer,                            intent(in) :: dim2
    integer,                            intent(in) :: extent

  continue

    if (extent == 1) then
      call my_realloc_ptr(real2,dim1+1,dim2)
    elseif (extent == 2) then
      call my_realloc_ptr(real2,dim1,dim2+1)
    else
      write (6,*) "Illegal value for extent in call to inc_alloc"
      write (6,*) "extent = ", extent, "but value must be 1 or 2"
      stop
    end if

  end subroutine complxr42_inc_alloc


!============================ COMPLX_MY_ALLOC_PTR1 ===========================80
!
! Allocate space for rank 1 complx precision complex arrays
!
!=============================================================================80

  subroutine single_complx_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine single_complx_my_alloc_ptr1


!============================ COMPLX_MY_ALLOC_PTR2 ===========================80
!
! Allocate space for rank 2 complx precision complex arrays
!
!=============================================================================80

  subroutine single_complx_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine single_complx_my_alloc_ptr2


!============================ COMPLX_MY_ALLOC_PTR3 ===========================80
!
! Allocate space for rank 3 complx precision complex arrays
!
!=============================================================================80

  subroutine single_complx_my_alloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:,:), pointer    :: x
    integer,                              intent(in) :: dim1,dim2,dim3

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine single_complx_my_alloc_ptr3


!============================ COMPLX_MY_ALLOC_PTR4 ===========================80
!
! Allocate space for rank 4 complx precision complex arrays
!
!=============================================================================80

  subroutine single_complx_my_alloc_ptr4(x,dim1,dim2,dim3,dim4)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:,:,:), pointer    :: x
    integer,                                intent(in) :: dim1, dim2
    integer,                                intent(in) :: dim3,dim4

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine single_complx_my_alloc_ptr4


!============================ COMPLX_MY_ALLOC_PTR5 ===========================80
!
! Allocate space for rank 5 complx precision complex arrays
!
!=============================================================================80

  subroutine single_complx_my_alloc_ptr5(x,dim1,dim2,dim3,dim4,dim5)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:,:,:,:), pointer    :: x
    integer,                                  intent(in) :: dim1,dim2
    integer,                                  intent(in) :: dim3,dim4
    integer,                                  intent(in) :: dim5

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4),                &
               max(1,dim5)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine single_complx_my_alloc_ptr5


!============================ COMPLX_MY_ALLOC_PTR1 ===========================80
!
! Allocate space for rank 1 complx precision complex arrays
!
!=============================================================================80

  subroutine double_complx_my_alloc_ptr1(x,dim1)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    integer :: ierr

  continue

    allocate(x(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine double_complx_my_alloc_ptr1


!============================ COMPLX_MY_ALLOC_PTR2 ===========================80
!
! Allocate space for rank 2 complx precision complex arrays
!
!=============================================================================80

  subroutine double_complx_my_alloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine double_complx_my_alloc_ptr2


!============================ COMPLX_MY_ALLOC_PTR3 ===========================80
!
! Allocate space for rank 3 complx precision complex arrays
!
!=============================================================================80

  subroutine double_complx_my_alloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:,:), pointer    :: x
    integer,                              intent(in) :: dim1,dim2,dim3

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine double_complx_my_alloc_ptr3


!============================ COMPLX_MY_ALLOC_PTR4 ===========================80
!
! Allocate space for rank 4 complx precision complex arrays
!
!=============================================================================80

  subroutine double_complx_my_alloc_ptr4(x,dim1,dim2,dim3,dim4)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:,:,:), pointer    :: x
    integer,                                intent(in) :: dim1, dim2
    integer,                                intent(in) :: dim3, dim4

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine double_complx_my_alloc_ptr4


!============================ COMPLX_MY_ALLOC_PTR5 ===========================80
!
! Allocate space for rank 5 complx precision complex arrays
!
!=============================================================================80

  subroutine double_complx_my_alloc_ptr5(x,dim1,dim2,dim3,dim4,dim5)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:,:,:,:), pointer    :: x
    integer,                                  intent(in) :: dim1, dim2
    integer,                                  intent(in) :: dim3, dim4
    integer,                                  intent(in) :: dim5

    integer :: ierr

  continue

    allocate(x(max(1,dim1),max(1,dim2),max(1,dim3),max(1,dim4),max(1,dim5)),   &
      stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

    x = 0

  end subroutine double_complx_my_alloc_ptr5


!=========================== COMPLX_R4_MY_REALLOC_PTR1 =======================80
!
! Reallocate space for rank 1 complx_r4 precision arrays
!
!=============================================================================80

  subroutine complx_r4_my_realloc_ptr1(x,dim1)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    complex(system_r4), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1)),stat=ierr)

    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    i = min0(dim1,old_dim1)
    y(1:i)=x(1:i)

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

    x => y ! reassign the x pointer

  end subroutine complx_r4_my_realloc_ptr1


!=========================== COMPLX_R4_MY_REALLOC_PTR2 =======================80
!
! Reallocate space for rank 2 complx_r4 arrays
!
!=============================================================================80

  subroutine complx_r4_my_realloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    complex(system_r4), dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i,j

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    i = min0(dim1,old_dim1)
    j = min0(dim2,old_dim2)
    y = 0
    y(1:i,1:j)=x(1:i,1:j)

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: dealloc failed in realloc"

    x => y ! reassign the x pointer

  end subroutine complx_r4_my_realloc_ptr2


!=========================== COMPLX_R4_MY_REALLOC_PTR3 =======================80
!
! Reallocate space for rank 3 complx_r4 arrays
!
!=============================================================================80

  subroutine complx_r4_my_realloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r4

    complex(system_r4), dimension(:,:,:), pointer    :: x
    integer,                              intent(in) :: dim1, dim2
    integer,                              intent(in) :: dim3

    complex(system_r4), dimension(:,:,:), pointer :: y

    integer :: old_dim1, old_dim2, old_dim3, ierr, i, j, k

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    old_dim3 = size(x,3)
    i = min0(dim1,old_dim1)
    j = min0(dim2,old_dim2)
    k = min0(dim3,old_dim3)
    y = 0
    y(1:i,1:j,1:k)=x(1:i,1:j,1:k)

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: dealloc failed in realloc"

    x => y ! reassign the x pointer

  end subroutine complx_r4_my_realloc_ptr3


!=========================== COMPLX_R8_MY_REALLOC_PTR1 =======================80
!
! Reallocate space for rank 1 complx_r8 precision arrays
!
!=============================================================================80

  subroutine complx_r8_my_realloc_ptr1(x,dim1)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:), pointer    :: x
    integer,                          intent(in) :: dim1

    complex(system_r8), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

!   Initialize y, then copy the old values

    y = 0
    old_dim1 = size(x,1)
    i = min0(dim1,old_dim1)
    y(1:i)=x(1:i)

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available for realloc"

    x => y ! reassign the x pointer

  end subroutine complx_r8_my_realloc_ptr1


!=========================== COMPLX_R8_MY_REALLOC_PTR2 =======================80
!
! Reallocate space for rank 2 complx_r8 arrays
!
!=============================================================================80

  subroutine complx_r8_my_realloc_ptr2(x,dim1,dim2)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:), pointer    :: x
    integer,                            intent(in) :: dim1, dim2

    complex(system_r8), dimension(:,:), pointer :: y

    integer :: old_dim1, old_dim2, ierr, i, j

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    i = min0(dim1,old_dim1)
    j = min0(dim2,old_dim2)
    y = 0
    y(1:i,1:j)=x(1:i,1:j)

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if (ierr /= 0) stop "fun3d: dealloc failed in realloc"

    x => y ! reassign the x pointer

  end subroutine complx_r8_my_realloc_ptr2


!=========================== COMPLX_R8_MY_REALLOC_PTR3 =======================80
!
! Reallocate space for rank 3 complx_r8 arrays
!
!=============================================================================80

  subroutine complx_r8_my_realloc_ptr3(x,dim1,dim2,dim3)

    use kinddefs, only : system_r8

    complex(system_r8), dimension(:,:,:), pointer    :: x
    integer,                              intent(in) :: dim1, dim2
    integer,                              intent(in) :: dim3

    complex(system_r8), dimension(:,:,:), pointer :: y

    integer :: old_dim1, old_dim2, old_dim3, ierr, i, j, k

  continue

!   allocate the final array with the new dimensions

    allocate(y(max(1,dim1),max(1,dim2),max(1,dim3)),stat=ierr)
    if (ierr /= 0) stop "fun3d: insufficient memory available"

!   Initialize y, then copy the old values

    old_dim1 = size(x,1)
    old_dim2 = size(x,2)
    old_dim3 = size(x,3)
    i = min0(dim1,old_dim1)
    j = min0(dim2,old_dim2)
    k = min0(dim3,old_dim3)
    y = 0
    y(1:i,1:j,1:k)=x(1:i,1:j,1:k)

!   having allocated the required memory for y, release the memory used by x

    deallocate(x,stat=ierr)
    if(ierr /= 0) stop "fun3d: dealloc failed in realloc"

    x => y ! reassign the x pointer

  end subroutine complx_r8_my_realloc_ptr3

end module allocations

module ksopt

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs, only : dp

  implicit none

  private

  public :: ksopt_driver

  real(dp), dimension(:,:), pointer :: last_flow_dvs

#ifdef HAVE_KSOPT
  real(dp), dimension(:), pointer :: c, d ! Scaling coefficients
#endif

contains

!================================ KSOPT_DRIVER ===============================80
!
!  Performs a multipoint optimization using KSOPT
!
!  Each point may in turn have multiple objectives/constraints
!
!  KSOPT does not distinguish between the design points from which the
!  various objectives/constraints originate
!
!=============================================================================80
  subroutine ksopt_driver(max_design_cycles,what_to_do,restart_optimization,   &
                          io,ammo_directory,lss_flag)

    use allocations,       only : my_alloc_ptr
    use kinddefs,          only : dp
    use lmpi,              only : lmpi_die
    use analysis,          only : perform_analysis
    use sensitivity,       only : perform_sensitivity_analysis
    use design_types,      only : opt_data_type
    use designs,           only : load_optimization_data, been_there,          &
                                  unload_optimization_data,                    &
                                  free_optimization_data
    use system_extensions, only : se_flush, se_chdir
    use file_utils,        only : rm
    use utilities,         only : check_for_stop
    use nml_design,        only : n_design_pts, model_variables

    integer, intent(in) :: max_design_cycles, what_to_do, io

    logical, intent(in) :: lss_flag
    logical, intent(in) :: restart_optimization

    character(len=*), intent(in) :: ammo_directory

    integer :: ksopt_munit = 68  ! KSOPT output file number
    integer :: isel, obj_index, con_index, i, j
    integer :: ksopt_mdv, istop
#ifdef HAVE_KSOPT
    integer :: ksopt_mobj, ksopt_mcon, ksopt_mside, ksopt_mscale
    integer :: ksopt_jprnt, ksopt_jtmax, ksopt_jgrad, ksopt_jsdrst
    integer :: ksopt_nomax, ksopt_ngmax

    real(dp) :: ksopt_rdfun, ksopt_adfun, ksopt_fdelt, ksopt_fdmin, ksopt_rhomn
    real(dp) :: ksopt_rhomx, ksopt_rhodl
#endif

    real(dp), dimension(:),   pointer :: ksopt_x
    real(dp), dimension(:),   pointer :: ksopt_xlb
    real(dp), dimension(:),   pointer :: ksopt_xub
    real(dp), dimension(:),   pointer :: ksopt_scale
    real(dp), dimension(:),   pointer :: ksopt_obj
    real(dp), dimension(:),   pointer :: ksopt_g
    real(dp), dimension(:,:), pointer :: ksopt_df
    real(dp), dimension(:,:), pointer :: ksopt_dg

    type(opt_data_type), dimension(n_design_pts) :: opt_data

  continue

! Load the optimization data for each point

    do i = 1, n_design_pts
      call se_chdir(model_variables(i)%model_directory)
      opt_data(i)%allocated = .false.
      call load_optimization_data(opt_data(i),io,'KSOPT: Location 1')
      opt_data(i)%scale = 1.0_dp
    end do

! Open the KSOPT output file

    open(ksopt_munit,file='ksopt.output')

! Allocate and set some initial KSOPT inputs
! Take them from the first model - this assumes that these inputs
! are constant across each design point

    call my_alloc_ptr(ksopt_x,     opt_data(1)%ndv)
    call my_alloc_ptr(ksopt_xlb,   opt_data(1)%ndv)
    call my_alloc_ptr(ksopt_xub,   opt_data(1)%ndv)
    call my_alloc_ptr(ksopt_scale, opt_data(1)%ndv)

    ksopt_x      = opt_data(1)%design_variables
    ksopt_xlb    = opt_data(1)%lower_bounds
    ksopt_xub    = opt_data(1)%upper_bounds
    ksopt_scale  = opt_data(1)%scale
    ksopt_mdv    = opt_data(1)%ndv
#ifdef HAVE_KSOPT
    ksopt_mobj = 0
    ksopt_mcon = 0
    do i = 1, n_design_pts ! Sum number of f and g's across all design points
      ksopt_mobj = ksopt_mobj + opt_data(i)%nobjectives
      ksopt_mcon = ksopt_mcon + opt_data(i)%nconstraints
    end do
    ksopt_mside  = 1
    ksopt_mscale = 0
    ksopt_jprnt  = 214
    ksopt_jtmax  = max_design_cycles
    ksopt_jgrad  = 1
    ksopt_jsdrst = 0
    ksopt_rdfun  = 0.0_dp
    ksopt_adfun  = 0.0_dp
    ksopt_fdelt  = 0.0_dp
    ksopt_fdmin  = 0.0_dp
    ksopt_rhomn  = 0.0_dp
    ksopt_rhomx  = 0.0_dp
    ksopt_rhodl  = 0.0_dp

! Initialize the arrays to store the previous set of DV's for each model

    call my_alloc_ptr(last_flow_dvs, n_design_pts, ksopt_mdv)

! Allocate routine sets things to zero, so we ought
! to set it to something crazy, since this could
! very well be our starting design point

    last_flow_dvs = huge(1.0_dp)

#endif

#ifdef HAVE_KSOPT
    call scale_initial_problem(ksopt_mdv,ksopt_xlb,ksopt_xub,ksopt_x)
    call ksinit90(ksopt_x,ksopt_xlb,ksopt_xub,ksopt_scale,ksopt_mdv,ksopt_mcon,&
                  ksopt_mobj,ksopt_mside,ksopt_mscale,ksopt_jprnt,ksopt_jtmax, &
                  ksopt_jgrad,ksopt_jsdrst,ksopt_rdfun,ksopt_adfun,ksopt_fdelt,&
                  ksopt_fdmin,ksopt_rhomn,ksopt_rhomx,ksopt_rhodl,ksopt_munit)
    call unscale_initial_problem(ksopt_mdv,ksopt_xlb,ksopt_xub,ksopt_x)
#else
    write(*,*) 'You do not have KSOPT installed.'
    call lmpi_die
#endif

#ifdef HAVE_KSOPT
    call my_alloc_ptr(ksopt_obj, ksopt_mobj)
    call my_alloc_ptr(ksopt_g,   ksopt_mcon)
    call my_alloc_ptr(ksopt_df,  ksopt_mobj, ksopt_mdv)
    call my_alloc_ptr(ksopt_dg,  ksopt_mcon, ksopt_mdv)
#else
    call my_alloc_ptr(ksopt_obj, 1)
    call my_alloc_ptr(ksopt_g,   1)
    call my_alloc_ptr(ksopt_df,  1, 1)
    call my_alloc_ptr(ksopt_dg,  1, 1)
#endif

#ifdef HAVE_KSOPT
    ksopt_nomax = ksopt_mobj
    ksopt_ngmax = ksopt_mcon
#endif

! Gather up the objectives, constraints, and their gradients

    obj_index = 0
    con_index = 0
    do i = 1, n_design_pts
      do j = 1, opt_data(i)%nobjectives
        obj_index = obj_index + 1
        ksopt_obj(obj_index)  = opt_data(i)%weights(j)*                        &
                       (opt_data(i)%objectives(j)-opt_data(i)%targets(j))**    &
                        opt_data(i)%powers(j)
        ksopt_df(obj_index,:) = opt_data(i)%weights(j)*opt_data(i)%powers(j)*  &
                       (opt_data(i)%objectives(j)-opt_data(i)%targets(j))**    &
                       (opt_data(i)%powers(j)-1.0_dp)*opt_data(i)%gradients(j,:)
      end do
      do j = 1, opt_data(i)%nconstraints
        con_index = con_index + 1
        ksopt_g(con_index)    = opt_data(i)%constraints(j)
        ksopt_dg(con_index,:) = opt_data(i)%constraint_gradients(j,:)
      end do
    end do

! Call KSOPT

100 continue

    call se_chdir(ammo_directory)
    call check_for_stop(istop)
    if ( istop > 0 ) then
      call rm('stop.dat')
      write(*,*) 'User requested premature stop...'
      stop
    endif

#ifdef HAVE_KSOPT
    call scale_problem(ksopt_mdv,ksopt_nomax,ksopt_ngmax,ksopt_x,ksopt_df,     &
                       ksopt_dg)
    call ksopt90(ksopt_nomax,ksopt_ngmax,isel,ksopt_x,ksopt_obj,ksopt_g,       &
                 ksopt_df,ksopt_dg)
    call unscale_problem(ksopt_mdv,ksopt_nomax,ksopt_ngmax,ksopt_x,ksopt_df,   &
                         ksopt_dg)
#else
    write(*,*) 'You do not have KSOPT installed.'
    call lmpi_die
#endif

! Flush the KSOPT output

    call se_flush(ksopt_munit)

! Write latest optimization data - this assumes that the only
! outbound variable from the KSOPT call is the design variable
! vector

    do i = 1, n_design_pts
      opt_data(i)%design_variables = ksopt_x
      call se_chdir(model_variables(i)%model_directory)
      call unload_optimization_data(opt_data(i),io,'After returning from KSOPT')
    end do

! Based on the info returned by KSOPT, either get function, gradient, or exit

#ifndef HAVE_KSOPT
    isel = max_design_cycles  ! Silence compilers
#endif

    select case ( isel )
    case (1)

      obj_index = 0
      con_index = 0

      do i = 1, n_design_pts
        if(.not.been_there(ksopt_mdv,ksopt_x,last_flow_dvs(i,:),.false.)) then
          call se_chdir(model_variables(i)%model_directory)
          call perform_analysis(model_variables(i)%restart_flow,               &
                                i,n_design_pts,what_to_do,restart_optimization,&
                                model_variables(i)%desc_directory)
          call load_optimization_data(opt_data(i),io,'KSOPT: Location 2')
          do j = 1, opt_data(i)%nobjectives
            obj_index = obj_index + 1
            ksopt_obj(obj_index)  = opt_data(i)%weights(j)*                    &
                       (opt_data(i)%objectives(j)-opt_data(i)%targets(j))**    &
                        opt_data(i)%powers(j)
          end do
          do j = 1, opt_data(i)%nconstraints
            con_index = con_index + 1
            ksopt_g(con_index) = opt_data(i)%constraints(j)
          end do
          last_flow_dvs(i,:) = ksopt_x
        endif
      end do

    case (2)

      obj_index = 0
      con_index = 0

      do i = 1, n_design_pts

        call se_chdir(model_variables(i)%model_directory)

        if(.not.been_there(ksopt_mdv,ksopt_x,last_flow_dvs(i,:),.true.)) then
          write(*,*) 'WARNING: Sensitivity analysis requested at design point'
          write(*,*) 'different from previous function analysis!'
          call perform_analysis(model_variables(i)%restart_flow,               &
                                i,n_design_pts,what_to_do,restart_optimization,&
                                model_variables(i)%desc_directory)
          call load_optimization_data(opt_data(i),io,'KSOPT: Location 3')
          do j = 1, opt_data(i)%nobjectives
            obj_index = obj_index + 1
            ksopt_obj(obj_index)  = opt_data(i)%weights(j)*                    &
                       (opt_data(i)%objectives(j)-opt_data(i)%targets(j))**    &
                        opt_data(i)%powers(j)
          end do
          do j = 1, opt_data(i)%nconstraints
            con_index = con_index + 1
            ksopt_g(con_index) = opt_data(i)%constraints(j)
          end do
          last_flow_dvs(i,:) = ksopt_x
        endif

        call perform_sensitivity_analysis(model_variables(i)%restart_dual,     &
                                          i,n_design_pts,what_to_do,lss_flag)
        call load_optimization_data(opt_data(i),io,'KSOPT: Location 4')
        do j = 1, opt_data(i)%nobjectives
          obj_index = obj_index + 1
          ksopt_df(obj_index,:) = opt_data(i)%weights(j)*opt_data(i)%powers(j)*&
                       (opt_data(i)%objectives(j)-opt_data(i)%targets(j))**    &
                       (opt_data(i)%powers(j)-1.0_dp)*opt_data(i)%gradients(j,:)
        end do
        do j = 1, opt_data(i)%nconstraints
          con_index = con_index + 1
          ksopt_dg(con_index,:) = opt_data(i)%constraint_gradients(j,:)
        end do
      end do

    case default

      write(*,*) 'KSOPT has completed.'
      goto 200

    end select

! Return to KSOPT

    goto 100

200 continue

! Close the KSOPT output file and deallocate its memory

    close(ksopt_munit)

    deallocate(ksopt_x,ksopt_xlb,ksopt_xub,ksopt_scale)
    deallocate(ksopt_obj,ksopt_g,ksopt_df,ksopt_dg,last_flow_dvs)

! Free the memory used by the optimization data derived type

    do i = 1, n_design_pts
      call free_optimization_data(opt_data(i))
    end do

  end subroutine ksopt_driver

#ifdef HAVE_KSOPT
!============================== SCALE_INITIAL_PROBLEM ========================80
!
!  Scales initial problem for KSOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_initial_problem(n,lb,ub,x)

    use allocations, only : my_alloc_ptr

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: lb, ub, x

    logical, save :: first_time = .true.

  continue

    if ( first_time ) then

! determine the scaling coefficients

      call my_alloc_ptr(c,n)
      call my_alloc_ptr(d,n)

      c(:) = 0.5_dp*(lb(:)+ub(:))
      d(:) = 0.5_dp*(ub(:)-lb(:))

      first_time = .false.
    endif

! scale the DV's

    x(:) = (x(:)-c(:)) / d(:)

! scale the bounds

    lb(:) = (lb(:)-c(:)) / d(:)
    ub(:) = (ub(:)-c(:)) / d(:)

  end subroutine scale_initial_problem


!============================== UNSCALE_INITIAL_PROBLEM ======================80
!
!  Unscales initial problem for KSOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_initial_problem(n,lb,ub,x)

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: lb, ub, x

  continue

! unscale the DV's

    x(:) = d(:)*x(:) + c(:)

! unscale the bounds

    lb(:) = d(:)*lb(:) + c(:)
    ub(:) = d(:)*ub(:) + c(:)

  end subroutine unscale_initial_problem


!============================== SCALE_PROBLEM ================================80
!
!  Scales problem for KSOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_problem(n,no,ng,x,df,dg)

    integer, intent(in) :: n, no, ng

    real(dp), dimension(n),    intent(inout) :: x
    real(dp), dimension(no,n), intent(inout) :: df
    real(dp), dimension(ng,n), intent(inout) :: dg

    integer :: i

  continue

! scale the DV's

    x(:) = (x(:)-c(:)) / d(:)

! scale the objective gradients

    do i = 1, no
      df(i,:) = d(:)*df(i,:)
    end do

! scale the constraint gradients

    do i = 1, ng
      dg(i,:) = d(:)*dg(i,:)
    end do

  end subroutine scale_problem


!============================== UNSCALE_PROBLEM ==============================80
!
!  Unscales problem for KSOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_problem(n,no,ng,x,df,dg)

    integer, intent(in) :: n, no, ng

    real(dp), dimension(n),    intent(inout) :: x
    real(dp), dimension(no,n), intent(inout) :: df
    real(dp), dimension(ng,n), intent(inout) :: dg

    integer :: i

  continue

! unscale the DV's

    x(:) = d(:)*x(:) + c(:)

! unscale the objective gradients

    do i = 1, no
      df(i,:) = df(i,:)/d(:)
    end do

! unscale the constraint gradients

    do i = 1, ng
      dg(i,:) = dg(i,:)/d(:)
    end do

  end subroutine unscale_problem
#endif

end module ksopt

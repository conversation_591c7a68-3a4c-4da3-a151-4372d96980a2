!============================ VISCOSITY_LAW_DDT5 =============================80
!
! This routine computes the viscosity for a perfect gas,
! given t (temperature) and cstar (sutherland_constant/tref)
!
!=============================================================================80

  pure function viscosity_law_ddt5( cstar, t )

    use kinddefs, only : dp
    use ddt,      only : ddt5, assignment(=), operator(+), operator(*)         &
                       , operator(/), ddt_sqrt

    real(dp),   intent(in) :: cstar
    type(ddt5), intent(in) :: t

    type(ddt5)             :: viscosity_law_ddt5
    real(dp), parameter    :: one = 1.0_dp

  continue

    viscosity_law_ddt5 = (one+cstar)/(t+cstar)*t*ddt_sqrt(t)

  end function viscosity_law_ddt5

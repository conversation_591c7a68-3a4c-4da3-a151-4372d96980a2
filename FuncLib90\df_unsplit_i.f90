!================================== DF_UNSPLIT_I =============================80
!
! Unsplit flux jacobians...ubar_null enforces tangency explicity.
!
! Newer version that handles moving grid and noninertial rotating reference
! frame cases as well as stationary grid/inertial frame cases.
!
! Reference: Neel, R. E., Godfrey, A. G., and McGrory, W. D.:"Low-Speed,
!            Time-Accurate Validation of GASP Version 4"; AIAA 2005-686
!            43rd AIAA Aerospace Sciences Meeting, Jan. 2005.
!
!=============================================================================80

  pure function df_unsplit_i( xnorm, ynorm, znorm, area, face_speed, beta,     &
                              ql, ubar_null )

    use kinddefs, only : dp

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: face_speed, beta
    real(dp), dimension(4), intent(in) :: ql
    logical,                intent(in) :: ubar_null

    real(dp), dimension(4,4)           :: df_unsplit_i

    real(dp) :: flux1ul,flux1vl,flux1wl,flux1pl
    real(dp) :: flux2ul,flux2vl,flux2wl,flux2pl
    real(dp) :: flux3ul,flux3vl,flux3wl,flux3pl
    real(dp) :: flux4ul,flux4vl,flux4wl,flux4pl

    real(dp) :: fluxp1ul,fluxp1vl,fluxp1wl,fluxp1pl
    real(dp) :: fluxp2ul,fluxp2vl,fluxp2wl,fluxp2pl
    real(dp) :: fluxp3ul,fluxp3vl,fluxp3wl,fluxp3pl
    real(dp) :: fluxp4ul,fluxp4vl,fluxp4wl,fluxp4pl

    real(dp) :: ubarl
    real(dp) :: ubarlul,ubarlvl,ubarlwl,ubarlpl

    real(dp) :: ul
    real(dp) :: ulul,ulvl,ulwl,ulpl

    real(dp) :: vl
    real(dp) :: vlul,vlvl,vlwl,vlpl

    real(dp) :: wl
    real(dp) :: wlul,wlvl,wlwl,wlpl

   !real(dp) :: pl
    real(dp) :: plul,plvl,plwl,plpl

  continue

! Get variables on "left" side of face

     !pl     = ql(1)

        plpl = 1.0_dp
        plul = 0.0_dp
        plvl = 0.0_dp
        plwl = 0.0_dp

      ul     = ql(2)

        ulpl = 0.0_dp
        ulul = 1.0_dp
        ulvl = 0.0_dp
        ulwl = 0.0_dp

      vl     = ql(3)

        vlpl = 0.0_dp
        vlul = 0.0_dp
        vlvl = 1.0_dp
        vlwl = 0.0_dp

      wl     = ql(4)

        wlpl = 0.0_dp
        wlul = 0.0_dp
        wlvl = 0.0_dp
        wlwl = 1.0_dp

      if ( ubar_null ) then
        ubarl   = 0._dp
        ubarlpl = 0._dp
        ubarlul = 0._dp
        ubarlvl = 0._dp
        ubarlwl = 0._dp
      else
        ubarl   = xnorm*ul + ynorm*vl + znorm*wl
        ubarlpl = xnorm*ulpl + ynorm*vlpl + znorm*wlpl
        ubarlul = xnorm*ulul + ynorm*vlul + znorm*wlul
        ubarlvl = xnorm*ulvl + ynorm*vlvl + znorm*wlvl
        ubarlwl = xnorm*ulwl + ynorm*vlwl + znorm*wlwl
      endif


! Calculate the flux vector on the left side
! Note: area term deferrred until the end

!     fluxp1 = area*beta*(ubarl-face_speed)

        fluxp1pl = beta*ubarlpl
        fluxp1ul = beta*ubarlul
        fluxp1vl = beta*ubarlvl
        fluxp1wl = beta*ubarlwl

!     fluxp2 = area*(ul*(ubarl-face_speed) + xnorm*pl)

        fluxp2pl = ulpl*(ubarl-face_speed) + ul*ubarlpl + xnorm*plpl
        fluxp2ul = ulul*(ubarl-face_speed) + ul*ubarlul + xnorm*plul
        fluxp2vl = ulvl*(ubarl-face_speed) + ul*ubarlvl + xnorm*plvl
        fluxp2wl = ulwl*(ubarl-face_speed) + ul*ubarlwl + xnorm*plwl

!     fluxp3 = area*(vl*(ubarl-face_speed) + ynorm*pl)

        fluxp3pl = vlpl*(ubarl-face_speed) + vl*ubarlpl + ynorm*plpl
        fluxp3ul = vlul*(ubarl-face_speed) + vl*ubarlul + ynorm*plul
        fluxp3vl = vlvl*(ubarl-face_speed) + vl*ubarlvl + ynorm*plvl
        fluxp3wl = vlwl*(ubarl-face_speed) + vl*ubarlwl + ynorm*plwl

!     fluxp4 = area*(wl*(ubarl-face_speed) + znorm*pl)

        fluxp4pl = wlpl*(ubarl-face_speed) + wl*ubarlpl + znorm*plpl
        fluxp4ul = wlul*(ubarl-face_speed) + wl*ubarlul + znorm*plul
        fluxp4vl = wlvl*(ubarl-face_speed) + wl*ubarlvl + znorm*plvl
        fluxp4wl = wlwl*(ubarl-face_speed) + wl*ubarlwl + znorm*plwl

! Finally, form the numerical flux
! Area terms now added back in

!     res1      = fluxp1

        flux1pl = area*fluxp1pl
        flux1ul = area*fluxp1ul
        flux1vl = area*fluxp1vl
        flux1wl = area*fluxp1wl

        df_unsplit_i(1,1) = flux1pl
        df_unsplit_i(1,2) = flux1ul
        df_unsplit_i(1,3) = flux1vl
        df_unsplit_i(1,4) = flux1wl

!     res2      = fluxp2

        flux2pl = area*fluxp2pl
        flux2ul = area*fluxp2ul
        flux2vl = area*fluxp2vl
        flux2wl = area*fluxp2wl

        df_unsplit_i(2,1) = flux2pl
        df_unsplit_i(2,2) = flux2ul
        df_unsplit_i(2,3) = flux2vl
        df_unsplit_i(2,4) = flux2wl

!     res3      = fluxp3

        flux3pl = area*fluxp3pl
        flux3ul = area*fluxp3ul
        flux3vl = area*fluxp3vl
        flux3wl = area*fluxp3wl

        df_unsplit_i(3,1) = flux3pl
        df_unsplit_i(3,2) = flux3ul
        df_unsplit_i(3,3) = flux3vl
        df_unsplit_i(3,4) = flux3wl

!     res4   = fluxp4

        flux4pl = area*fluxp4pl
        flux4ul = area*fluxp4ul
        flux4vl = area*fluxp4vl
        flux4wl = area*fluxp4wl

        df_unsplit_i(4,1) = flux4pl
        df_unsplit_i(4,2) = flux4ul
        df_unsplit_i(4,3) = flux4vl
        df_unsplit_i(4,4) = flux4wl

  end function df_unsplit_i

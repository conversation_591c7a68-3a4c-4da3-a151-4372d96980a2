!============================= FLUX_UPWIND_HNS17 =============================80
!
! Full viscous flux for the hyperbolic viscous system (HNS17).
!
! Incoming primitive variables (rho, u, v, w, p, G, Q), where
!
!  G = [gxx,gxy,gxz; gyx,gyy,gyz; gzx,gzy,gzz] = xmr*(mu+amut)*grad(velocity)
!  Q = [qx,qy,qz] = heat fluxes
!
!=============================================================================80
  pure function flux_upwind_hvisc17(xnorm,ynorm,znorm,area,ql,qr,amutl,amutr)

    use fluid,           only :           prandtl
    use turb_parameters, only : turbulent_prandtl
    use flux_constants,  only : xmr, gamma, cstar

    real(dp),                intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                intent(in) :: amutl, amutr
    real(dp), dimension(17), intent(in) :: ql, qr
    real(dp), dimension(17)             :: flux_upwind_hvisc17

    real(dp), parameter ::         zero = 0.0_dp
    real(dp), parameter ::         half = 0.5_dp
    real(dp), parameter ::          one = 1.0_dp
    real(dp), parameter ::          two = 2.0_dp
    real(dp), parameter :: four_third   = 4.0_dp/3.0_dp
    real(dp), parameter :: three_fourth = 3.0_dp/4.0_dp

    real(dp) :: taunxL, taunxR, taunyL
    real(dp) :: taunyR, taunzL, taunzR
    real(dp) :: taunx , tauny , taunz , taunn
    real(dp) :: dtaunx, dtauny, dtaunz, dtaunn
    real(dp) :: muv,muh, anv,amv,ah, t, max_wave_speed
    real(dp) :: temp, temp2, temp3

  continue

! Averaged flux

        t = gamma*ql(5)/ql(1)
    temp  = three_fourth*(ql( 7)+ql( 9))
    temp2 = three_fourth*(ql(12)+ql( 8))
    temp3 = three_fourth*(ql(11)+ql(13))
   taunxL = (ql( 6)-half*(ql(10)+ql(14)))*xnorm+temp*ynorm+temp2*znorm
   taunyL = temp*xnorm+(ql(10)-half*(ql( 6)+ql(14)))*ynorm+temp3*znorm
   taunzL = temp2*xnorm+temp3*ynorm+(ql(14)-half*(ql( 6)+ql(10)))*znorm

        t = t + gamma*qr(5)/qr(1)
    temp  = three_fourth*(qr( 7)+qr( 9))
    temp2 = three_fourth*(qr(12)+qr( 8))
    temp3 = three_fourth*(qr(11)+qr(13))
   taunxR = (qr( 6)-half*(qr(10)+qr(14)))*xnorm + temp*ynorm + temp2*znorm
   taunyR = temp*xnorm+(qr(10)-half*(qr( 6)+qr(14)))*ynorm+temp3*znorm
   taunzR = temp2*xnorm+temp3*ynorm+(qr(14)-half*(qr( 6)+qr(10)))*znorm

         temp2 = one/gamma/(gamma-one)
  flux_upwind_hvisc17( 1) =  zero
  flux_upwind_hvisc17( 2) = -taunxL-taunxR
  flux_upwind_hvisc17( 3) = -taunyL-taunyR
  flux_upwind_hvisc17( 4) = -taunzL-taunzR
  flux_upwind_hvisc17( 5) = -(taunxL*ql(2)+taunyL*ql(3)+taunzL*ql(4)) &
               + (ql(15)*xnorm+ql(16)*ynorm+ql(17)*znorm)             &
                 -(taunxR*qr(2)+taunyR*qr(3)+taunzR*qr(4))            &
               + (qr(15)*xnorm+qr(16)*ynorm+qr(17)*znorm)
  temp = ql(2)+qr(2)
  flux_upwind_hvisc17( 6) = -temp*xnorm
  flux_upwind_hvisc17( 7) = -temp*ynorm
  flux_upwind_hvisc17( 8) = -temp*znorm
  temp = ql(3)+qr(3)
  flux_upwind_hvisc17( 9) = -temp*xnorm
  flux_upwind_hvisc17(10) = -temp*ynorm
  flux_upwind_hvisc17(11) = -temp*znorm
  temp = ql(4)+qr(4)
  flux_upwind_hvisc17(12) = -temp*xnorm
  flux_upwind_hvisc17(13) = -temp*ynorm
  flux_upwind_hvisc17(14) = -temp*znorm
  temp         =  t*temp2
  flux_upwind_hvisc17(15) =  temp*xnorm
  flux_upwind_hvisc17(16) =  temp*ynorm
  flux_upwind_hvisc17(17) =  temp*znorm

! Add dissipation

    taunx = half*(taunxR+taunxL)
    tauny = half*(taunyR+taunyL)
    taunz = half*(taunzR+taunzL)
   dtaunx = taunxR-taunxL
   dtauny = taunyR-taunyL
   dtaunz = taunzR-taunzL

    taunn = taunx*xnorm+tauny*ynorm+taunz*znorm
   taunxL = (qr(2)-ql(2))*xnorm+(qr(3)-ql(3))*ynorm+(qr(4)-ql(4))*znorm
   dtaunn = dtaunx*xnorm+dtauny*ynorm+dtaunz*znorm
   taunyL = half*((ql(2)+qr(2))*xnorm+(ql(3)+qr(3))*ynorm+(ql(4)+qr(4))*znorm)
   taunzL = one/( half*(ql(1)+qr(1)) )

! -> It should be half/3.141592653589793238_dp/aspect_ratio.
    temp2 = half/3.141592653589793238_dp
    temp2 = temp2*temp2
    temp3 = viscosity_law( cstar, half*t )
    max_wave_speed = half*(amutl+amutr)
     temp = xmr * ( temp3 + max_wave_speed )
      muv = temp2 / (temp*four_third*taunzL)
      muv = (temp*four_third*taunzL)/muv
     temp = xmr * gamma*( temp3/prandtl + max_wave_speed/turbulent_prandtl )
      muh = temp2 / (temp*taunzL)
      muh = (temp*taunzL)/muh
      anv = sqrt(muv)
      amv = sqrt(three_fourth*muv)
       ah = sqrt(muh)

   taunxR = qr(2)-ql(2)
   taunyR = qr(3)-ql(3)
   taunzR = qr(4)-ql(4)
     temp = one/ah
        t = one/(anv*temp+one)
  max_wave_speed = one/(amv*temp+one)

  temp  = (anv-amv)*taunxL
  temp2 = anv * 0.25_dp
  temp3 = one/(taunzL*ah)

  flux_upwind_hvisc17(2) = flux_upwind_hvisc17(2)                         &
                         - (taunzL*(temp*xnorm+amv*taunxR)+temp2*taunxR)
  flux_upwind_hvisc17(3) = flux_upwind_hvisc17(3)                         &
                         - (taunzL*(temp*ynorm+amv*taunyR)+temp2*taunyR)
  flux_upwind_hvisc17(4) = flux_upwind_hvisc17(4)                         &
                         - (taunzL*(temp*znorm+amv*taunzR)+temp2*taunzR)
  flux_upwind_hvisc17(5) = flux_upwind_hvisc17(5)                         &
                   - (taunzL*(temp*taunyL+half*amv*((ql(2)+qr(2))*taunxR  &
             +  (ql(3)+qr(3))*taunyR+(ql(4)+qr(4))*taunzR-taunyL*taunxL)) &
+ah*((qr(5)-ql(5))-(half*(ql(5)+qr(5))/taunzL)*(qr(1)-ql(1)))/(gamma-one) &
          + taunn*dtaunn*temp3*t                                          &
          + (taunx*dtaunx + tauny*dtauny + taunz*dtaunz                   &
          - taunn*dtaunn)*temp3*max_wave_speed )

  temp3 = one/muv
    amv = four_third*amv
   temp = temp3*( temp2*((qr(6)-ql(6))*xnorm+(qr(7)-ql(7))*ynorm            &
        +(qr(8)-ql(8))*znorm)+(anv*xnorm)*dtaunn+amv*(dtaunx-dtaunn*xnorm) )
  flux_upwind_hvisc17( 6) = flux_upwind_hvisc17(6)-temp*xnorm
  flux_upwind_hvisc17( 7) = flux_upwind_hvisc17(7)-temp*ynorm
  flux_upwind_hvisc17( 8) = flux_upwind_hvisc17(8)-temp*znorm

  temp = temp3*( temp2*((qr(9)-ql(9))*xnorm           &
       + (qr(10)-ql(10))*ynorm+(qr(11)-ql(11))*znorm) &
       + (anv*ynorm)*dtaunn+amv*(dtauny-dtaunn*ynorm) )
  flux_upwind_hvisc17( 9) = flux_upwind_hvisc17(9) -temp*xnorm
  flux_upwind_hvisc17(10) = flux_upwind_hvisc17(10)-temp*ynorm
  flux_upwind_hvisc17(11) = flux_upwind_hvisc17(11)-temp*znorm

  temp = temp3*( temp2*((qr(12)-ql(12))*xnorm         &
       + (qr(13)-ql(13))*ynorm+(qr(14)-ql(14))*znorm) &
       + (anv*znorm)*dtaunn+amv*(dtaunz-dtaunn*znorm) )
  flux_upwind_hvisc17(12) = flux_upwind_hvisc17(12)-temp*xnorm
  flux_upwind_hvisc17(13) = flux_upwind_hvisc17(13)-temp*ynorm
  flux_upwind_hvisc17(14) = flux_upwind_hvisc17(14)-temp*znorm

  temp  = (one/muh)*( ah*(((qr(15)-ql(15))*xnorm+(qr(16)-ql(16))*ynorm       &
        + (qr(17)-ql(17))*znorm)-taunn*taunxL*t                              &
        - (taunx*taunxR+tauny*taunyR+taunz*taunzR-taunn*taunxL)*max_wave_speed))

  flux_upwind_hvisc17(15) = flux_upwind_hvisc17(15)-temp*xnorm
  flux_upwind_hvisc17(16) = flux_upwind_hvisc17(16)-temp*ynorm
  flux_upwind_hvisc17(17) = flux_upwind_hvisc17(17)-temp*znorm

! Upwind flux

  flux_upwind_hvisc17 = half*flux_upwind_hvisc17*area

 end function flux_upwind_hvisc17

! Needed to avoid circular dependencies between the python interface and the
! tight coupling routines

  module pydata

  implicit none

     integer  :: py_nblades, py_nspan
     logical  :: py_bfomo
     logical  :: py_allocated

     ! f2py does not easily support 'dp'
     ! real*8 was giving compiler warnings with the NAG compiler
     ! will it handle this local definition of dp?
     ! we'll find out when we try to use this next...

     integer, parameter :: dp=selected_real_kind(15, 307)

     real(dp) :: py_radius
     real(dp) :: py_rho, py_ainf
     real(dp), dimension(:), pointer :: py_rtmp, py_drtmp, py_qctmp
     real(dp), dimension(:), pointer :: py_forces, py_moments
     real(dp), dimension(:), pointer :: py_deflections, py_rotations

  end module pydata

!================================= FV_NODE_AVG_I =============================80
!
! Full viscous compressible meanflow interior fluxes.
!
!=============================================================================80
  pure function fv_node_avg_i( xnorm, ynorm, znorm, area,                      &
                               amutl, amutr, qtgrad )

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, amutl, amutr

    real(dp), dimension(3,4), intent(in) :: qtgrad   !T variables

    real(dp), dimension(4) :: fv_node_avg_i

    real(dp) :: amutf

  continue

    amutf = 0.5_dp*( amutl + amutr )

    fv_node_avg_i = tau_v_i( xnorm, ynorm, znorm, area, &
                             qtgrad, amutf )

  end function fv_node_avg_i

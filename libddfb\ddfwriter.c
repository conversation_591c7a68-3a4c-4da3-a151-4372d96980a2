/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/
#include <stdio.h>
#include <string.h>
#include <time.h>
#include "ioswap.h"
#include "ddfwriter.h"

#define DATETIMESIZE 80

static FILE* private_fp=NULL;	/* File Pointer to DDF file */
static long private_nzone=0;	/* Zone tracker */
static long private_npts;	/* Number of Points tracker */
static long private_nfunc;	/* Number of Function values tracker */
static long private_nelem;	/* Number of Elements tracker */

/*
 * Function pointer used to potentially allow setting to ioswap functions
 * if needed.
 */
static size_t (*private_fwrite)(const void *ptr, size_t size, size_t nmem, FILE *fp)=fwrite;


/*----------------------------------------------------------------------------*/
size_t writeBinaryDDFHeader(const char* name, const char* title, long nzone, double solutionTime)
{
  int    magic=IOSWAP_REF;
  char   myTitle[80];
  char   version[20];
  time_t rawtime;
  struct tm *info;
  char   dateTime[DATETIMESIZE];
  double stime;

  if( private_fp != NULL ) {
    fprintf(stderr,"Active DDF file (complete writing it first)\n");
    return 1;
  }

  if( (private_fp=fopen(name,"wb")) == NULL ) {
    fprintf(stderr,"Failed to open \"%s\" for writing\n",name);
    goto Write_Header_Error;
  }

  /* Magic number to identify byte-swap order to reader */
  if( private_fwrite(&magic,sizeof(int),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write magic number\n");
    goto Write_Header_Error;
  }

  /* DDF data */

  if( strncpy(myTitle,title,80) == NULL ) {
    fprintf(stderr,"Bad title\n");
    goto Write_Header_Error;
  }

  if( private_fwrite(myTitle,sizeof(char),80,private_fp) != 80 ) {
    fprintf(stderr,"Failed to write title\n");
    goto Write_Header_Error;
  }

  if( strcpy(memset(version,'\0',20),"2.0") == NULL ) {
    fprintf(stderr,"Bad version\n");
    goto Write_Header_Error;
  }

  if( private_fwrite(version,sizeof(char),20,private_fp) != 20 ) {
    fprintf(stderr,"Failed to write title\n");
    goto Write_Header_Error;
  }

  time( &rawtime );
  info = localtime( &rawtime );
  strftime(memset(dateTime,'\0',DATETIMESIZE),DATETIMESIZE,"%Y-%m-%d %H:%M:%S %Z", info);

  if( private_fwrite(dateTime,sizeof(char),DATETIMESIZE,private_fp) != DATETIMESIZE ) {
    fprintf(stderr,"Failed to write date and time\n");
    goto Write_Header_Error;
  }

  stime = solutionTime;

  if( private_fwrite(&stime,sizeof(double),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write solution time\n");
    goto Write_Header_Error;
  }

  private_nzone = nzone;

  if( private_fwrite(&private_nzone,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write the number of zones\n");
    goto Write_Header_Error;
  }

  private_npts = private_nelem = 0;

  return 0;

Write_Header_Error:
  private_nzone = 0;
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t writeBinaryDDFZoneHeader(const char* title, long type, long npts, long nfunc, long nelem)
{
  long i,ii;
  char myTitle[80];

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with writeBinaryDDFHeader)\n");
    goto Write_Zone_Error;
  }

  if( private_npts != 0 || private_nelem != 0 ) {
    fprintf(stderr,"Previous zone did not complete\n");
    goto Write_Zone_Error;
  }

  /* Zone Header */
  if( strncpy(myTitle,title,80) == NULL ) {
    fprintf(stderr,"Bad zone title\n");
    goto Write_Zone_Error;
  }

  if( private_fwrite(myTitle,sizeof(char),80,private_fp) != 80 ) {
    fprintf(stderr,"Failed to write zone title\n");
    goto Write_Zone_Error;
  }

  if( private_fwrite(&npts,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write the number of points\n");
    goto Write_Zone_Error;
  }
  private_npts = npts;

  if( private_fwrite(&nelem,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write the number of elements\n");
    goto Write_Zone_Error;
  }
  private_nelem = nelem;

  if( private_fwrite(&nfunc,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write the number of functions\n");
    goto Write_Zone_Error;
  }
  private_nfunc = nfunc;

  if( private_fwrite(&type,sizeof(long),1,private_fp) != 1 ) {
    fprintf(stderr,"Failed to write the data type\n");
    goto Write_Zone_Error;
  }

  return 0;

Write_Zone_Error:
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t writeBinaryDDFPoint(double xval, double yval, double zval, long pid, long setid, const double* func)
{
  double point[3] = {xval, yval, zval};
  long   pids[2]  = {pid, setid};

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with writeBinaryDDFHeader)\n");
    goto Write_Point_Error;
  }

  if( private_npts <= 0 ) {
    fprintf(stderr,"Exceeded point count for zone!\n");
    goto Write_Point_Error;
  }

  /* Zone point */
  if( private_fwrite(point,sizeof(double),3,private_fp) != 3 ) {
    fprintf(stderr,"Failed to write the point\n");
    goto Write_Point_Error;
  }

  /* Ids */
  if( private_fwrite(pids,sizeof(long),2,private_fp) != 2 ) {
    fprintf(stderr,"Failed to write the point ids\n");
    goto Write_Point_Error;
  }

  /* Function */
  if( private_nfunc > 0 ) {
    if( private_fwrite(func,sizeof(double),private_nfunc,private_fp) != private_nfunc) {
      fprintf(stderr,"Failed to write the function data for the point\n");
      goto Write_Point_Error;
    }
  }

  if( --private_npts == 0 && private_nelem == 0 && --private_nzone == 0 ) {
    fclose(private_fp);
    private_fp = NULL;
  }

  return 0;

Write_Point_Error:
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}


/*----------------------------------------------------------------------------*/
size_t writeBinaryDDFElement(long npe, long id, long pid, long setid, const long* elem)
{
  long eids[4] = {npe, id, pid, setid};

  if( private_fp == NULL ) {
    fprintf(stderr,"No active DDF file (activate with writeBinaryDDFHeader)\n");
    goto Write_Element_Error;
  }

  if( private_npts != 0 ) {
    fprintf(stderr,"Did not finish writing all points for zone!\n");
    goto Write_Element_Error;
  }

  if( private_nelem <= 0 ) {
    fprintf(stderr,"Exceeded element count for zone!\n");
    goto Write_Element_Error;
  }

  /* elements */
  if( private_fwrite(eids,sizeof(long),4,private_fp) != 4) {
    fprintf(stderr,"Failed to write the element ids\n");
    goto Write_Element_Error;
  }

  if( private_fwrite(elem,sizeof(long),npe,private_fp) != npe) {
    fprintf(stderr,"Failed to write the element\n");
    goto Write_Element_Error;
  }

  if( --private_nelem == 0 && --private_nzone == 0 ) {
    fclose(private_fp);
    private_fp = NULL;
  }

  return 0;

Write_Element_Error:
  if( private_fp ) {
    fclose(private_fp);
    private_fp = NULL;
  }
  return 1;
}

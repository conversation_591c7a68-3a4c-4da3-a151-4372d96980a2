!================================== FLUX_ROE_I ===============================80
!
! Calculates the fluxes on the face and performs the flux balance
!
! Newer version that handles moving grid and noninertial rotating reference
! frame cases as well as stationary grid/inertial frame cases.
!
! Reference: <PERSON><PERSON>, R<PERSON> E<PERSON>, <PERSON>, A. G., and McGrory, W. D.:"Low-Speed,
!            Time-Accurate Validation of GASP Version 4"; AIAA 2005-686
!            43rd AIAA Aerospace Sciences Meeting, Jan. 2005.
!
!=============================================================================80

  pure function flux_roe_i(xnorm, ynorm, znorm, area, face_speed, beta, ql, qr)

    use kinddefs,        only : dp

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, face_speed, beta

    real(dp), dimension(4), intent(in) :: ql, qr

    real(dp), dimension(4)             :: flux_roe_i

    real(dp) :: c,c2,delp,delu,delv,delw
    real(dp) :: eig1,eig2,eig3,eig4
    real(dp) :: fluxm1,fluxm2,fluxm3,fluxm4,fluxp1,fluxp2,fluxp3,fluxp4
    real(dp) :: pl,pr
    real(dp) :: phi_p,phi_m
    real(dp) :: f11,f12,f13,f14,f21,f22,f23,f24
    real(dp) :: f31,f32,f33,f34,f41,f42,f43,f44
    real(dp) :: t1,t2,t3,t4
    real(dp) :: u,ul,ur,v,vl,vr,w,wl,wr
    real(dp) :: ubar,ubarl,ubarr, delubar
    real(dp) :: fspd_half,fspd2_4th
    real(dp) :: term, term1, term2, term3, term4

    real(dp), parameter ::    my_0 = 0.00_dp
    real(dp), parameter ::  my_haf = 0.50_dp
    real(dp), parameter ::  my_4th = 0.25_dp

  continue

    fspd_half = my_haf*face_speed
    fspd2_4th = my_4th*face_speed*face_speed

    pl = ql(1)
    ul = ql(2)
    vl = ql(3)
    wl = ql(4)

    ubarl  = xnorm*ul + ynorm*vl + znorm*wl

    pr = qr(1)
    ur = qr(2)
    vr = qr(3)
    wr = qr(4)

    ubarr  = xnorm*ur + ynorm*vr + znorm*wr

!   Compute averages

    u     = my_haf*(ul + ur)
    v     = my_haf*(vl + vr)
    w     = my_haf*(wl + wr)
    ubar  = my_haf*(ubarl + ubarr)

    c2    = (ubar-fspd_half)*(ubar-fspd_half) + beta
    c     = sqrt(c2)
    phi_p = c/(c + fspd_half)
    phi_m = c/(c - fspd_half)

!   Now compute eigenvalues, eigenvectors, and strengths

    eig1 = ubar
    eig2 = ubar
    eig3 = ubar - fspd_half - c
    eig4 = ubar - fspd_half + c

    eig1 = abs(eig1)
    eig2 = abs(eig2)
    eig3 = abs(eig3)
    eig4 = abs(eig4)

!   Jumps

    delp    = pr - pl
    delu    = ur - ul
    delv    = vr - vl
    delw    = wr - wl
    delubar = ubarr - ubarl

    term1 =  eig1
    f11   =  my_0
    f12   =  term1*(delu - xnorm*delubar)
    f13   =  term1*(delv - ynorm*delubar)
    f14   =  term1*(delw - znorm*delubar)

    term2 = -eig2*((ubar - face_speed)*delubar + delp)/(c2 + fspd2_4th)
    f21   =  my_0
    f22   =  term2*(u - xnorm*ubar)
    f23   =  term2*(v - ynorm*ubar)
    f24   =  term2*(w - znorm*ubar)

    term3 =  eig3*my_haf*((ubar - c - fspd_half)*delubar + delp)/c2
    term  =  ubar+c-fspd_half
    f31   =  term3*c*term
    f32   =  term3*phi_m*(u - xnorm*term)
    f33   =  term3*phi_m*(v - ynorm*term)
    f34   =  term3*phi_m*(w - znorm*term)

    term4 =  eig4*my_haf*((ubar + c - fspd_half)*delubar + delp)/c2
    term  =  ubar-c-fspd_half
    f41   = -term4*c*term
    f42   =  term4*phi_p*(u - xnorm*term)
    f43   =  term4*phi_p*(v - ynorm*term)
    f44   =  term4*phi_p*(w - znorm*term)

    t1 = f11 + f21 + f31 + f41
    t2 = f12 + f22 + f32 + f42
    t3 = f13 + f23 + f33 + f43
    t4 = f14 + f24 + f34 + f44

!   Calculate the flux vector on the left side

    fluxp1 = beta*(ubarl-face_speed)
    fluxp2 = ul*(ubarl-face_speed) + xnorm*pl
    fluxp3 = vl*(ubarl-face_speed) + ynorm*pl
    fluxp4 = wl*(ubarl-face_speed) + znorm*pl

!   Now the right side

    fluxm1 = beta*(ubarr-face_speed)
    fluxm2 = ur*(ubarr-face_speed) + xnorm*pr
    fluxm3 = vr*(ubarr-face_speed) + ynorm*pr
    fluxm4 = wr*(ubarr-face_speed) + znorm*pr

!   Finally, form the numerical flux

    flux_roe_i(1)  = my_haf*area*(fluxp1 + fluxm1 - t1)
    flux_roe_i(2)  = my_haf*area*(fluxp2 + fluxm2 - t2)
    flux_roe_i(3)  = my_haf*area*(fluxp3 + fluxm3 - t3)
    flux_roe_i(4)  = my_haf*area*(fluxp4 + fluxm4 - t4)

  end function flux_roe_i

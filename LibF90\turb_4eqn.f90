module turb_4eqn

  use lmpi,          only : lmpi_id
  use lmpi,          only : lmpi_conditional_stop
  use kinddefs,      only : dp, odp
  use turb_kw_const, only : verbose
  use turb_gammaretsst_const, only : transition_4eqn_on

  implicit none

  private

  public :: bc_gammaretsst_set_walls, turb_resid_4eqn, turb_jacob_4eqn
  public :: wall_turbulence_gammaretsst

  real(dp), parameter :: zero    = 0.0_dp
  real(dp), parameter :: one     = 1.0_dp
  real(dp), parameter :: two     = 2.0_dp
  real(dp), parameter :: half    = 0.5_dp

contains

!============================== TURB_RESID_4EQN ==============================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for 4-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid_4eqn (eqn_set, nnodes0, nnodes01,                      &
                              turb, qnode, res, slen, gradx, grady,            &
                              gradz, vol, nnodes0_2d, node_pairs_2d, n_turb,   &
                              n_tot, n_grd, amut, sst_f1, crossd )

    use kinddefs,       only : dp
    use info_depr,      only : xmach, re, twod
    use lmpi,           only : lmpi_conditional_stop
    use solution_types, only : compressible, incompressible
    use turbulence_info,only : use_approximate_jacobians

    use ddt,            only : ddt4, assignment(=), operator(*), operator(/),  &
                               operator(>),   operator(**),                    &
                               operator(+), operator(-)

    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    integer,                              intent(in)    :: n_grd
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nnodes0_2d

    integer,  dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d

    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1
    real(dp), dimension(nnodes0),         intent(inout) :: crossd


    integer :: node_src_eval
    integer :: i, ii

    real(dp) :: xmr
    real(dp) :: my_xmach

    real(dp), dimension(n_turb,2)       :: sourcegammaretsst
    real(dp)                            :: omega

    type(ddt4), dimension(n_turb)       :: sourcegammaretsst_ddt

  continue

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time
    my_xmach = zero

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1,'turb_resid: only for in/compress pg')
    end select

    xmr   = my_xmach / re

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    crossd   = zero

!-----------------------------------------------------------------------------80
!                    source terms
!-----------------------------------------------------------------------------80
      do ii = 1, node_src_eval
        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if
        omega = turb(2,i)
        crossd(i) = cross_diffusion_gammaretsst( omega, xmr                    &
      , gradx(1:n_grd,i), grady(1:n_grd,i), gradz(1:n_grd,i), n_grd )
      end do

      do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if
        source_term_jac_fidelity: if ( use_approximate_jacobians ) then
          sourcegammaretsst = source_gammaretsst ( i, eqn_set                  &
                               , nnodes01, nnodes0, n_tot, n_turb, n_grd       &
                               , vol, qnode, turb, amut, gradx, grady, gradz   &
                               , slen, xmr, sst_f1, crossd )

          res(1,i) = res(1,i)-( sourcegammaretsst(1,1)                         &
                               -sourcegammaretsst(1,2) )
          res(2,i) = res(2,i)-( sourcegammaretsst(2,1)                         &
                               -sourcegammaretsst(2,2) )
          if (transition_4eqn_on) then
            res(3,i) = res(3,i)-( sourcegammaretsst(3,1)                       &
                                 -sourcegammaretsst(3,2) )
            res(4,i) = res(4,i)-( sourcegammaretsst(4,1)                       &
                                 -sourcegammaretsst(4,2) )
          end if

        else
          sourcegammaretsst_ddt = source_gammaretsst_ddt ( i, eqn_set          &
                               , nnodes01, nnodes0, n_tot, n_turb, n_grd       &
                               , vol, qnode, turb, amut, gradx, grady, gradz   &
                               , slen, xmr, sst_f1, crossd )
          res(1,i) = res(1,i) - sourcegammaretsst_ddt(1)%f
          res(2,i) = res(2,i) - sourcegammaretsst_ddt(2)%f
          if (transition_4eqn_on) then
            res(3,i) = res(3,i) - sourcegammaretsst_ddt(3)%f
            res(4,i) = res(4,i) - sourcegammaretsst_ddt(4)%f
          end if

        end if source_term_jac_fidelity

      end do

  end subroutine turb_resid_4eqn

!============================== TURB_JACOB_4EQN ==============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for 4-eqn models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob_4eqn ( eqn_set, nnodes0, nnodes01,                     &
                               turb, qnode, slen, gradx, grady, gradz, vol,    &
                               a_diag, nnodes0_2d, node_pairs_2d,              &
                               n_turb, n_tot,                                  &
                               n_grd, g2m, mut, sst_f1, crossd )

    use kinddefs,      only : dp
    use info_depr,     only : xmach, re, twod
    use turbulence_info, only : use_approximate_jacobians

    use solution_types,only : compressible, incompressible

    use ddt,            only : ddt4, assignment(=), operator(*), operator(/),  &
                               operator(>),   operator(**),                    &
                               operator(+), operator(-)

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot, n_grd
    integer, intent(in) :: n_turb
    integer, intent(in) :: nnodes0_2d
    integer, intent(in) :: nnodes0, nnodes01

    integer, dimension(2,nnodes0_2d),        intent(in) :: node_pairs_2d
    integer, dimension(:),                   intent(in) :: g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: vol
    real(dp),  dimension(nnodes01),              intent(in)    :: mut
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1
    real(dp),  dimension(nnodes0),               intent(in)    :: crossd

    integer :: node_src_eval
    integer :: i, ii
    integer :: mm, nn

    real(dp),  dimension(n_turb, n_turb) :: d_source_gammaretsst
    integer                              :: row

    real(dp) :: xmr
    real(dp) :: my_xmach

    type(ddt4),  dimension(n_turb)       :: sourcegammaretsst_ddt

!   real(dp), dimension(n_turb) :: diff_const
!   real(dp), dimension(n_turb) :: diff_const2
!   real(dp), dimension(1) :: nu
!   integer :: n_sta

  continue

    my_xmach = zero
    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1,'turb_jacob_4eqn: only for in/compress pg')
    end select
    xmr   = my_xmach / re

!   When using the edge-based terms, we only need to visit this routine
!   one time

    node_src_eval  = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

!                             source terms
!-----------------------------------------------------------------------------80
!   Next compute the source (production/destruction) terms

        source2a: do ii = 1, node_src_eval
        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if
        source_term_jac_fidelity: if ( use_approximate_jacobians ) then
          d_source_gammaretsst = jacob_source_gammaretsst( i, eqn_set, nnodes01&
                            , nnodes0                                          &
                            ,  turb, qnode, gradx, grady, gradz                &
                            , n_turb, n_tot, n_grd, slen, sst_f1, crossd )

          row             = g2m(i)
!         Dkdk
          a_diag(1,1,row) = a_diag(1,1,row) + vol(i)*d_source_gammaretsst(1,1)
!         Dkdw
          a_diag(1,2,row) = a_diag(1,2,row) + vol(i)*d_source_gammaretsst(1,2)
!         Dwdk
          a_diag(2,1,row) = a_diag(2,1,row) + vol(i)*d_source_gammaretsst(2,1)
!         Dwdw
          a_diag(2,2,row) = a_diag(2,2,row) + vol(i)*d_source_gammaretsst(2,2)
!         D(gamma)d(gamma)
          a_diag(3,3,row) = a_diag(3,3,row) + vol(i)*d_source_gammaretsst(3,3)
!         D(rethetat)d(rethetat)
          a_diag(4,4,row) = a_diag(4,4,row) + vol(i)*d_source_gammaretsst(4,4)
        else
          xmr   = my_xmach / re
          sourcegammaretsst_ddt = source_gammaretsst_ddt ( i, eqn_set          &
                               , nnodes01, nnodes0, n_tot, n_turb, n_grd       &
                               , vol, qnode, turb, mut, gradx, grady, gradz    &
                               , slen, xmr, sst_f1, crossd )
          row = g2m(i)
          do nn=1,4
          do mm=1,4
            a_diag(nn,mm,row) = a_diag(nn,mm,row)                              &
                          - real(sourcegammaretsst_ddt(nn)%d(mm),odp)
          enddo
          enddo
        end if source_term_jac_fidelity
        end do source2a

  end subroutine turb_jacob_4eqn

!========================= JACOB_SOURCE_GAMMARETSST ==========================80
!
!
!=============================================================================80
  function jacob_source_gammaretsst( i, eqn_set, nnodes01, nnodes0             &
                          , turb, qnode, gradx, grady, gradz                   &
                          , n_turb, n_tot, n_grd, slen, sst_f1, crossd )       &
    result ( d_source_kw )

    use fluid,          only : gamma, sutherland_constant
    use kinddefs,       only : dp
    use info_depr,      only : tref, xmach, re
    use debug_defs,     only : test_freestream
    use solution_types, only : compressible, incompressible
    use turb_kw_const,  only : cmu_0                                           &
                             , beta1, beta2

    integer, intent(in) :: i
    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: n_turb
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nnodes0

    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1
    real(dp),  dimension(nnodes0),               intent(in)    :: crossd

    real(dp), dimension(n_turb, n_turb)                        :: d_source_kw

    real(dp) :: beta, vort, vortpluseps
    real(dp) :: tke
    real(dp) :: omega
    real(dp) :: xmr
    real(dp) :: xmrinv

    real(dp) :: my_xmach, rho, rhoinv, velu, velv, velw, press
    real(dp) :: w1, w2
    real(dp) :: pkdk
    real(dp) :: dkdk
    real(dp) :: dkdw
    real(dp) :: dwdw
    real(dp) :: temp, mu, cstar, fmuinv, f_turb, re_t, uuu
    real(dp) :: gammasst, f_thetat, rethetat
    real(dp) :: dist2, dist_delta, f_thetat1, f_thetat2, re_omega
    real(dp) :: ce2inv

    real(dp), parameter :: ca2      = 0.06_dp
    real(dp), parameter :: ce2      = 50.0_dp
    real(dp), parameter :: cthetat  = 0.03_dp

    real(dp), dimension(3,3)          :: gradv
!  CLR
!   real(dp)                          :: sijsij
!   real(dp), dimension(3,3)          :: sij

  continue

    ce2inv = one/ce2
    d_source_kw = zero

!-------------------- to satisfy the nag -------------------------------------80
    beta        = zero
    dkdw        = zero
    dkdk        = zero
    pkdk        = zero
    dwdw        = zero
    mu          = zero

    w1     = sst_f1(i)
    w2     = one - sst_f1(i)

    rho         = qnode(1,i)
    rhoinv      = one / rho
    velu        = qnode(2,i)
    velv        = qnode(3,i)
    velw        = qnode(4,i)
    press       = qnode(5,i)
    tke         = turb(1,i)
    omega       = turb(2,i)
    gammasst    = turb(3,i)
    rethetat    = turb(4,i)

    beta      = w1*beta1 + w2*beta2
    cstar    = sutherland_constant / tref

!   When using the edge-based terms, we only need to visit this routine
!   one time

    my_xmach = zero

!---------------------------------  equation set -----------------------------80
    select case ( eqn_set )
    case ( compressible )
      my_xmach    = xmach
      temp    = gamma * press * rhoinv
      mu      = viscosity_law( cstar, temp )
    case ( incompressible )
      my_xmach    = one
      temp   = one
      mu     = one
    case default
      call lmpi_conditional_stop(1,'turb_jacob: only for in/compress pg')
    end select

    xmr    = my_xmach / re
    xmrinv = one /xmr

!   Next compute the source (production/destruction) terms
!   This assumes that the blending and cross terms have been previously computed
!   for the residual computation!

!     Add linearizations from just the destruction term since they will
!     add a positive contribution to the diagonal
      pkdk = zero
      dkdk = cmu_0 * omega * xmrinv
      dkdw = cmu_0 * tke * xmrinv
      dwdw = two * beta * omega * xmrinv + abs(crossd(i))/omega
      d_source_kw(1,1) = -pkdk + dkdk
      d_source_kw(1,2) = dkdw
      d_source_kw(2,2) = dwdw

    if(transition_4eqn_on) then
      gradv = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                    &
                       , gradx(3,i), grady(3,i), gradz(3,i)                    &
                       , gradx(4,i), grady(4,i), gradz(4,i) )
!  CLR
!     sij     = get_sij( gradv )
!     sijsij  = get_sijsij( sij )
!  CLR
      vort    = get_vort( gradv )
      vortpluseps = vort + 1.0e-20_dp
!     vort = sqrt((grady(4,i)-gradz(3,i))**2+(gradz(2,i)-gradx(4,i))**2        &
!                +(gradx(3,i)-grady(2,i))**2)
      dist2=slen(i)**2
      fmuinv=one/mu
      re_t=rho*tke*fmuinv/omega
      re_omega=rho*dist2*omega*xmrinv*xmrinv*fmuinv
      f_turb=exp(-((0.25_dp*re_t)**4))

!  CLR
      d_source_kw(3,3) = ca2*vort*f_turb*abs(two*ce2*gammasst - one)
!     d_source_kw(3,3) = ca2*vort*f_turb*(two*ce2*gammasst - one)              &
!       + 1.5_dp*f_length*ca1*ce1*sqrt(two*sijsij*f_onset*gammasst)

      uuu=sqrt(velu**2+velv**2+velw**2)
      uuu=max(uuu,1.e-20_dp)
      dist_delta=rho*xmrinv*uuu**2*fmuinv/(375._dp*vortpluseps*rethetat)
      f_thetat1=exp(-((1.e-5_dp*re_omega)**2))*exp(-(dist_delta**4))
      f_thetat2=one-(((gammasst-ce2inv)/(one-ce2inv))**2)
      f_thetat = max(f_thetat1,f_thetat2)
      f_thetat = min(f_thetat,one)
      d_source_kw(4,4) = cthetat*rho*uuu**2*0.002_dp*fmuinv*                   &
                         (one-f_thetat)*xmrinv
    end if

      if (test_freestream) then
        d_source_kw = zero
      end if

  end function jacob_source_gammaretsst

!=========================== BC_GAMMARETSST_SET_WALLS ========================80
!
!  Sets quantities on viscous walls for gamma-ret-sst
!
!=============================================================================80

  subroutine bc_gammaretsst_set_walls(eqn_set,                                 &
                                 nnodes0, nnodes01, nbnode, ibnode,            &
                                 slen_wall, turb, qnode, n_turb, n_tot,ibc)

    use solution_types, only : compressible, incompressible

    integer, intent(in) :: nbnode, n_turb, n_tot, eqn_set
    integer, intent(in) :: nnodes0, nnodes01, ibc

    integer,     dimension(nbnode),         intent(in)    :: ibnode
    real(dp),    dimension(nbnode),         intent(in)    :: slen_wall

    real(dp), dimension(n_turb,nnodes01),intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode

    integer :: i,inode

    real(dp), dimension(n_tot)  :: qlocal
    real(dp), dimension(n_turb) :: turb_wall

    continue

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'bc_gammaretsst_set_walls: only in/compr pg')
    end if

    do i = 1,nbnode
      inode = ibnode(i)
      if (inode <= nnodes0) then

        qlocal = qnode(1:n_tot,inode)

        call wall_turbulence_gammaretsst( eqn_set, ibc, slen_wall(i), qlocal,  &
                                     turb_wall )
!  CLR - do not set wall values for turb quantities 3 and 4
!       turb(:,inode) = turb_wall(:)
        turb(1,inode) = turb_wall(1)
        turb(2,inode) = turb_wall(2)

      end if
    end do

  end subroutine bc_gammaretsst_set_walls

!======================== WALL_TURBULENCE_GAMMARETSST ========================80
!
!  Sets quantities on viscous walls for gamma-ret-sst
!
!=============================================================================80

  subroutine wall_turbulence_gammaretsst( eqn_set, ibc, slen_wall, qnode,      &
                                     turb_wall )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref, xmach, Re
    use bc_names,       only : viscous_wall_rough
    use solution_types, only : compressible
    use turb_kw_const,  only : beta1

    integer, intent(in) :: eqn_set, ibc

    real(dp),               intent(in)  :: slen_wall
    real(dp), dimension(:), intent(in)  :: qnode
    real(dp), dimension(:), intent(out) :: turb_wall

    real(dp) :: dymin,rho,p
    real(dp) :: temp,rnu,cstar
    real(dp) :: my_xmach, xmr

    continue

    cstar = sutherland_constant / tref
    my_xmach = zero

    my_xmach = one
    if (eqn_set == compressible ) my_xmach = xmach

    xmr = my_xmach/Re

    dymin = slen_wall

    if (eqn_set == compressible) then
      rho  = qnode(1)
      p    = qnode(5)
      temp = gamma * p / rho
      rnu  = viscosity_law( cstar, temp ) / rho
    else
      rnu  = one
    end if
    turb_wall(1) = zero

    turb_wall(2) = 60.0_dp*rnu/beta1*(xmr/dymin)**2
!  rough wall
    if ( ibc==viscous_wall_rough ) turb_wall(2) = turb_wall(2)/100.0_dp

!  Wall values for 3rd and 4th turbulence variables are not set;
!  the BC is for these variables is zero flux
    turb_wall(3) = -huge(1.0_dp)
    turb_wall(4) = -huge(1.0_dp)

    if ( verbose )then
    write(6,'(i5,a,15(1x,es20.10))') lmpi_id,' wallwall',rho,p,turb_wall(2)
    endif

  end subroutine wall_turbulence_gammaretsst

!======================= CROSS_DIFFUSION_GAMMARETSST =========================80
!
! Blending function for gamma-ret-sst (same as for Menter SST)
!
!=============================================================================80
  pure function cross_diffusion_gammaretsst ( omega, xmr, gradx, grady, gradz  &
                                     , n_grd )  result ( crossd )

    use kinddefs,       only : dp
    use turb_kw_const,  only : sig_w2
    use turb_util,      only : kloc, wloc

    real(dp) :: crossd

    integer,                              intent(in) :: n_grd

    real(dp), dimension(n_grd),           intent(in) :: gradx
    real(dp), dimension(n_grd),           intent(in) :: grady
    real(dp), dimension(n_grd),           intent(in) :: gradz
    real(dp),                             intent(in) :: omega
    real(dp),                             intent(in) :: xmr

    real(dp) :: rkx, rky, rkz, rwx, rwy, rwz
    real(dp) :: term
    real(dp) :: sigma_w_asm2_inv

  continue

    sigma_w_asm2_inv = one / sig_w2
    crossd = zero
    rkx    = gradx(kloc)
    rky    = grady(kloc)
    rkz    = gradz(kloc)
    rwx    = gradx(wloc)
    rwy    = grady(wloc)
    rwz    = gradz(wloc)
    term   = rkx*rwx + rky*rwy + rkz*rwz
    crossd = two * xmr * sigma_w_asm2_inv * term / omega

  end function cross_diffusion_gammaretsst

!============================== SOURCE_GAMMARETSST ===========================80
!
! Source terms for gamma-ret-sst, both compress and incompress perfect gas
!
!=============================================================================80
    function source_gammaretsst ( i, eqn_set                                   &
                       , nnodes01, nnodes0, n_tot, n_turb, n_grd               &
                       , vol, qnode, turb, mut , gradx, grady, gradz           &
                       , slen, xmr, sst_f1, crossd )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref
    use debug_defs,     only : test_freestream
    use solution_types, only : compressible
    use turb_kw_const,  only : kappa, k_inf, w_inf                             &
                             , keepambient, strain_production                  &
                             , beta1, beta2, sig_w1, sig_w2
    use turb_kw_const,  only : betastar_sst

    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    real(dp),   dimension(n_turb,2)                     :: source_gammaretsst

    integer,                              intent(in)    :: i
    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: n_grd

    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(nnodes01),        intent(in)    :: mut
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp),                             intent(in)    :: xmr
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1
    real(dp), dimension(nnodes0) ,        intent(in)    :: crossd

    real(dp)                 :: xmrinv
    real(dp)                 :: cstar
    real(dp)                 :: rho, rhoinv, temp
    real(dp)                 :: velu, velv, velw, press
    real(dp)                 :: vort, vortpluseps
    real(dp)                 :: rmut

    real(dp)                 :: gamma_w
    real(dp)                 :: sijsij
    real(dp), dimension(3,3) :: sij
    real(dp), dimension(3,3) :: gradv

    real(dp), parameter      :: c20      = 20.0_dp
    real(dp), parameter      :: s1       = 2.0_dp
    real(dp), parameter      :: ca1      = 2.0_dp
    real(dp), parameter      :: ca2      = 0.06_dp
    real(dp), parameter      :: ce1      = 1.0_dp
    real(dp), parameter      :: ce2      = 50.0_dp
    real(dp), parameter      :: cthetat  = 0.03_dp

    real(dp)                   :: prod_k
    real(dp)                   :: prod_w
    real(dp)                   :: production_keqn
    real(dp)                   :: production_eqn2
    real(dp)                   :: production_eqn3
    real(dp)                   :: production_eqn4
    real(dp)                   :: destruction_keqn
    real(dp)                   :: destruction_eqn2
    real(dp)                   :: destruction_eqn3
    real(dp)                   :: destruction_eqn4
    real(dp)                   :: beta
    real(dp)                   :: betastar
    real(dp)                   :: tke
    real(dp)                   :: omega
    real(dp)                   :: alpha_inner, alpha_outer
    real(dp)                   :: f1
    real(dp)                   :: mu

    real(dp)                   :: dist2, fmuinv, re_v, re_t, re_omega
    real(dp)                   :: f_turb, f_sublayer, gammasst, rethetat
    real(dp)                   :: f_length, re_thetac, f_reattach, uuu
    real(dp)                   :: f_onset1, f_onset2, f_onset3, f_onset
    real(dp)                   :: dist_delta, f_thetat1, f_thetat2, f_thetat
    real(dp)                   :: gamma_sep, gamma_eff
    real(dp)                   :: du_dx, du_dy, du_dz, du_ds
    real(dp)                   :: tu_percent_ltd, rey, reth_part, thetat
    real(dp)                   :: xlam, eff_lambda, eff_deriv
    real(dp)                   :: resr, dresr, dthetat, dkfactor, wrks
    real(dp)                   :: dlam,re_thetat, ce2inv

    integer                    :: nmo

  continue

    ce2inv = one/ce2
!-------------------- to satisfy the nag -------------------------------------80
    betastar         = zero
    gamma_w          = zero
    beta             = zero
    rmut             = zero
    prod_k           = zero
    prod_w           = zero
    production_keqn  = zero
    production_eqn2  = zero
    destruction_keqn = zero
    destruction_eqn2 = zero

!---------------------------------  constants --------------------------------80
    rho         = qnode(1,i)
    rhoinv      = one / rho
    velu        = qnode(2,i)
    velv        = qnode(3,i)
    velw        = qnode(4,i)
    press       = qnode(5,i)

    xmrinv   = one / xmr
    cstar    = sutherland_constant / tref

    tke         = turb(1,i)
    omega       = turb(2,i)
    gammasst    = turb(3,i)
    rethetat    = turb(4,i)

!---------------------------------  equation set -----------------------------80
    if ( eqn_set == compressible ) then
      temp    = gamma * press * rhoinv
      mu      = viscosity_law( cstar, temp )
    else
      temp   = one
      mu     = one
    end if


    betastar    = betastar_sst
    alpha_inner = beta1 / betastar - kappa*kappa/(sqrt(betastar)*sig_w1)
    alpha_outer = beta2 / betastar - kappa*kappa/(sqrt(betastar)*sig_w2)
    f1          = sst_f1(i)
    beta        = f1*beta1       + (one-f1)*beta2
    gamma_w     = f1*alpha_inner + (one-f1)*alpha_outer
!--------------------------------- gradients ---------------------------------80
    gradv = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                      &
                     , gradx(3,i), grady(3,i), gradz(3,i)                      &
                     , gradx(4,i), grady(4,i), gradz(4,i) )
    sij     = get_sij( gradv )
    sijsij  = get_sijsij( sij )
    vort    = get_vort( gradv )
    vortpluseps = vort + 1.0e-20_dp

!----------- Here is the bulk of the gamma-ret-sst source terms --------------80
    dist2=slen(i)**2
    fmuinv=one/mu
!   note that xmrinv is Re/Mach
    re_v=rho*dist2*sqrt(2.*sijsij)*xmrinv*fmuinv
    re_t=rho*tke*fmuinv/omega
    re_omega=rho*dist2*omega*xmrinv*xmrinv*fmuinv
    f_turb=exp(-((0.25_dp*re_t)**4))
    f_sublayer=exp(-((0.005_dp*re_omega)**2))
    if (rethetat < 400._dp) then
       f_length=39.8189_dp-(119.27e-4_dp)*rethetat-(132.567e-6_dp)*rethetat**2
    else if (rethetat >= 400._dp .and. rethetat < 596._dp) then
       f_length=263.404_dp-(123.939e-2_dp)*rethetat+                           &
                           (194.548e-5_dp)*rethetat**2-                        &
                           (101.695e-8_dp)*rethetat**3
    else if (rethetat >= 596._dp .and. rethetat < 1200._dp) then
       f_length=half-((rethetat-596._dp)*3.e-4_dp)
    else
       f_length=0.3188_dp
    end if
    f_length=f_length*(one-f_sublayer)+40._dp*f_sublayer
    if (rethetat <= 1870._dp) then
       re_thetac=rethetat-(396.035e-2_dp -                                     &
                          (120.656e-4_dp)*rethetat+                            &
                          (868.23e-6_dp)*rethetat**2-                          &
                          (696.506e-9_dp)*rethetat**3+                         &
                          (174.105e-12_dp)*rethetat**4)
    else
       re_thetac=rethetat-(593.11_dp+(rethetat-1870._dp)*0.482_dp)
    end if
    f_onset1=re_v/(2.193_dp*re_thetac)
    f_onset2=max(f_onset1,f_onset1**4)
    f_onset2=min(f_onset2,two)
    f_onset3=max((one-(0.4_dp*re_t)**3), zero)
    f_onset =max((f_onset2-f_onset3),zero)
    f_reattach=exp(-((0.05_dp*re_t)**4))
!
    uuu=sqrt(velu**2+velv**2+velw**2)
    uuu=max(uuu,1.e-20_dp)
    dist_delta=rho*xmrinv*uuu**2*fmuinv/(375._dp*vortpluseps*rethetat)
    f_thetat1=exp(-((1.e-5_dp*re_omega)**2))*exp(-(dist_delta**4))
    f_thetat2=one-(((gammasst-ce2inv)/(one-ce2inv))**2)
    f_thetat = max(f_thetat1,f_thetat2)
    f_thetat = min(f_thetat,one)
!
    gamma_sep=max((re_v/(3.235_dp*re_thetac)-one),zero)
    gamma_sep=min((gamma_sep*f_reattach*s1),two)
    gamma_sep=gamma_sep*f_thetat
    gamma_eff=max(gammasst,gamma_sep)
!
    du_dx=(velu*gradx(2,i)+velv*gradx(3,i)+velw*gradx(4,i))/uuu
    du_dy=(velu*grady(2,i)+velv*grady(3,i)+velw*grady(4,i))/uuu
    du_dz=(velu*gradz(2,i)+velv*gradz(3,i)+velw*gradz(4,i))/uuu
    du_ds=velu/uuu*du_dx+velv/uuu*du_dy+velw/uuu*du_dz
!
    tu_percent_ltd=100._dp*sqrt(two*tke/3._dp)/uuu
    tu_percent_ltd=max(tu_percent_ltd,0.027_dp)
    rey=rho*uuu*xmrinv*fmuinv
    if (tu_percent_ltd > 1.3_dp) then
       reth_part=331.5_dp*(tu_percent_ltd-0.5658_dp)**(-0.671_dp)
    else
       reth_part=(1173.51_dp-589.428_dp*tu_percent_ltd+0.2196_dp               &
                 /(tu_percent_ltd**2))
    end if
    thetat=reth_part/rey
!   iterate to get re_thetat
    do nmo=1,10
       xlam=rho*thetat**2*fmuinv*du_ds*xmrinv
       dlam=two*rho*thetat*fmuinv*du_ds*xmrinv
       if (xlam <= -0.1_dp) then
          xlam=-0.1_dp
          dlam=zero
       else if (xlam >= 0.1_dp) then
          xlam=0.1_dp
          dlam=zero
       end if
       if (xlam <= zero) then
          eff_lambda=one-((-12.986_dp*xlam-                                    &
                             123.66_dp*xlam**2-                                &
                             405.689_dp*xlam**3)*                              &
                             exp(-((tu_percent_ltd/1.5_dp)**1.5)))
          eff_deriv =(12.986_dp+two*123.66_dp*xlam+                            &
                             3._dp*405.689_dp*xlam**2)*                        &
                             dlam*exp(-((tu_percent_ltd/1.5_dp)**1.5))
       else
          eff_lambda=one+(0.275_dp*(one-exp(-35._dp*xlam))*                    &
                             exp(-two*tu_percent_ltd))
          eff_deriv =35._dp*0.275_dp*exp(-35._dp*xlam)*                        &
                             dlam*exp(-two*tu_percent_ltd)
       end if
       resr=reth_part*eff_lambda-rey*thetat
       dresr=reth_part*eff_deriv-rey
       dthetat=-resr/dresr
       dthetat=min(max(dthetat,-0.9_dp*thetat),0.9_dp*thetat)
       thetat=thetat+dthetat
    enddo
    re_thetat=rey*thetat
    re_thetat=max(re_thetat,c20)

!--------------- turbulent eddy viscosity ------------------------------------80
    rmut = mut(i) + 1.0e-6_dp
    if ( strain_production ) then
      prod_k = two * rmut * sijsij
    else
      prod_k     = rmut * vort * vort
    end if
    prod_w  = gamma_w * prod_k / rmut

!-----------------------------------------------------------------------------80
!------------------------------ source terms ---------------------------------80
!-----------------------------------------------------------------------------80
    production_keqn  = abs( xmr * rhoinv * prod_k )
    destruction_keqn = betastar * tke * omega * xmrinv

    production_eqn2  = prod_w * xmr
    destruction_eqn2 = beta * omega * omega * xmrinv                           &
                       - ( one-sst_f1(i) ) * crossd(i)

    if (transition_4eqn_on) then
       dkfactor=max(gamma_eff,0.1_dp)
       dkfactor=min(dkfactor,one)
       destruction_keqn=destruction_keqn*dkfactor
       production_keqn=production_keqn*gamma_eff
    end if

    production_eqn3  = f_length*ca1*sqrt(two*sijsij)*                          &
               sqrt(gammasst*f_onset)*(one-(ce1*gammasst))
    destruction_eqn3 = ca2*vort*gammasst*f_turb*(ce2*gammasst-one)

    wrks = (cthetat*rho*uuu**2*0.002_dp*fmuinv*(one-f_thetat)*xmrinv)
    production_eqn4  = wrks*re_thetat
    destruction_eqn4 = wrks*rethetat

!----------------------------- correction models -----------------------------80
!       Limit on k-production term; try limiting eqn 3 and 4 also
    production_keqn = min( production_keqn, c20*destruction_keqn )
    production_eqn3 = min( production_eqn3, c20*destruction_eqn3 )
    production_eqn4 = min( production_eqn4, c20*destruction_eqn4 )
!----------------------------- mods to source term s--------------------------80
!       Ambient terms - Spalart and Rumsey, AIAA J. Vol.45 No.10
    if (keepambient) then
      destruction_keqn = destruction_keqn - betastar*k_inf*w_inf*xmrinv
      destruction_eqn2 = destruction_eqn2 - beta*w_inf*w_inf*xmrinv
    end if

    source_gammaretsst(1,1) = vol(i)*production_keqn
    source_gammaretsst(1,2) = vol(i)*destruction_keqn
    source_gammaretsst(2,1) = vol(i)*production_eqn2
    source_gammaretsst(2,2) = vol(i)*destruction_eqn2
    source_gammaretsst(3,1) = vol(i)*production_eqn3
    source_gammaretsst(3,2) = vol(i)*destruction_eqn3
    source_gammaretsst(4,1) = vol(i)*production_eqn4
    source_gammaretsst(4,2) = vol(i)*destruction_eqn4

    if (test_freestream) then
      source_gammaretsst = zero
    end if

  end function source_gammaretsst

!========================== SOURCE_GAMMARETSST_DDT ===========================80
!
! Source terms for gamma-ret-sst, both compress and incompress perfect gas
! Uses DDT methodology for both RHS and LHS
!
!=============================================================================80
    function source_gammaretsst_ddt ( i, eqn_set                               &
                       , nnodes01, nnodes0, n_tot, n_turb, n_grd               &
                       , vol, qnode, turb, mut , gradx, grady, gradz           &
                       , slen, xmr, sst_f1, crossd )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref
    use debug_defs,     only : test_freestream
    use solution_types, only : compressible
    use turb_kw_const,  only : kappa, k_inf, w_inf                             &
                             , keepambient, strain_production                  &
                             , beta1, beta2, sig_w1, sig_w2

    use turb_kw_const,  only : betastar_sst

    use ddt,            only : ddt4, assignment(=), operator(*), operator(/),  &
                               ddt_sqrt, operator(>), operator(**),            &
                               operator(+), operator(-),                       &
                               ddt_min, ddt_max, ddt_exp

    integer,                              intent(in)  :: n_turb
    integer,                              intent(in)  :: n_tot
    type(ddt4), dimension(n_turb)                     :: source_gammaretsst_ddt

    integer,                              intent(in)  :: i
    integer,                              intent(in)  :: eqn_set
    integer,                              intent(in)  :: nnodes01
    integer,                              intent(in)  :: nnodes0
    integer,                              intent(in)  :: n_grd

    real(dp), dimension(nnodes01),        intent(in)  :: vol
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)  :: turb
    real(dp), dimension(nnodes01),        intent(in)  :: mut
    real(dp), dimension(n_grd,nnodes01),  intent(in)  :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)  :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)  :: gradz
    real(dp), dimension(nnodes01),        intent(in)  :: slen
    real(dp),                             intent(in)  :: xmr
    real(dp), dimension(nnodes01),        intent(in)  :: sst_f1
    real(dp), dimension(nnodes0),         intent(in)  :: crossd

    real(dp)                 :: xmrinv
    real(dp)                 :: cstar
    real(dp)                 :: rho, rhoinv, temp
    real(dp)                 :: velu, velv, velw, press
    real(dp)                 :: vort, vortpluseps
    real(dp)                 :: rmut

    real(dp)                 :: gamma_w
    real(dp)                 :: sijsij
    real(dp), dimension(3,3) :: sij
    real(dp), dimension(3,3) :: gradv

    real(dp), parameter      :: c20      = 20.0_dp
    real(dp), parameter      :: s1       = 2.0_dp
    real(dp), parameter      :: ca1      = 2.0_dp
    real(dp), parameter      :: ca2      = 0.06_dp
    real(dp), parameter      :: ce1      = 1.0_dp
    real(dp), parameter      :: ce2      = 50.0_dp
    real(dp), parameter      :: cthetat  = 0.03_dp

    real(dp), parameter      :: cp25     = 0.25_dp
    real(dp), parameter      :: cp005    = 0.005_dp
    real(dp), parameter      :: c39p8189 = 39.8189_dp
    real(dp), parameter      :: c119p27em4  = 119.27e-4_dp
    real(dp), parameter      :: c132p567em6 = 132.567e-6_dp
    real(dp), parameter      :: c263p404    = 263.404_dp
    real(dp), parameter      :: c123p939em2 = 123.939e-2_dp
    real(dp), parameter      :: c194p548em5 = 194.548e-5_dp
    real(dp), parameter      :: c101p695em8 = 101.695e-8_dp
    real(dp), parameter      :: c400        = 400._dp
    real(dp), parameter      :: c596        = 596._dp
    real(dp), parameter      :: c1200       = 1200._dp
    real(dp), parameter      :: c3em4       = 3.e-4_dp
    real(dp), parameter      :: cp3188      = 0.3188_dp
    real(dp), parameter      :: c40         = 40._dp
    real(dp), parameter      :: c1870       = 1870._dp
    real(dp), parameter      :: c396p035em2 = 396.035e-2_dp
    real(dp), parameter      :: c120p656em4 = 120.656e-4_dp
    real(dp), parameter      :: c868p23em6  = 868.23e-6_dp
    real(dp), parameter      :: c696p506em9 = 696.506e-9_dp
    real(dp), parameter      :: c174p105em12= 174.105e-12_dp
    real(dp), parameter      :: c593p11     = 593.11_dp
    real(dp), parameter      :: cp482       = 0.482_dp
    real(dp), parameter      :: c2p193      = 2.193_dp
    real(dp), parameter      :: cp4         = 0.4_dp
    real(dp), parameter      :: cp05        = 0.05_dp
    real(dp), parameter      :: c1pem20     = 1.e-20_dp
    real(dp), parameter      :: c375        = 375._dp
    real(dp), parameter      :: c1pem5      = 1.e-5_dp
    real(dp), parameter      :: c3p235      = 3.235_dp
    real(dp), parameter      :: c100        = 100._dp
    real(dp), parameter      :: c3          = 3._dp
    real(dp), parameter      :: cp027       = 0.027_dp
    real(dp), parameter      :: c1p3        = 1.3_dp
    real(dp), parameter      :: c331p5      = 331.5_dp
    real(dp), parameter      :: cp5658      = 0.5658_dp
    real(dp), parameter      :: cp671       = 0.671_dp
    real(dp), parameter      :: c1173p51    = 1173.51_dp
    real(dp), parameter      :: c589p428    = 589.428_dp
    real(dp), parameter      :: cp2196      = 0.2196_dp
    real(dp), parameter      :: cp1         = 0.1_dp
    real(dp), parameter      :: c1p5        = 1.5_dp
    real(dp), parameter      :: c12p986     = 12.986_dp
    real(dp), parameter      :: c123p66     = 123.66_dp
    real(dp), parameter      :: c405p689    = 405.689_dp
    real(dp), parameter      :: cp275       = 0.275_dp
    real(dp), parameter      :: c35         = 35._dp
    real(dp), parameter      :: cp9         = 0.9_dp
    real(dp), parameter      :: cp002       = 0.002_dp
    real(dp), parameter      :: c1pem6      = 1.0e-6_dp

    real(dp)                   :: prod_k
    real(dp)                   :: prod_w
    real(dp)                   :: production_eqn2
    real(dp)                   :: beta
    real(dp)                   :: betastar
    real(dp)                   :: alpha_inner, alpha_outer
    real(dp)                   :: f1
    real(dp)                   :: mu

    real(dp)                   :: dist2, fmuinv, re_v
    real(dp)                   :: uuu
    real(dp)                   :: du_dx, du_dy, du_dz, du_ds
    real(dp)                   :: rey
    real(dp)                   :: ce2inv

    type(ddt4)                 :: production_keqn
    type(ddt4)                 :: production_eqn3
    type(ddt4)                 :: production_eqn4
    type(ddt4)                 :: destruction_keqn
    type(ddt4)                 :: destruction_eqn2
    type(ddt4)                 :: destruction_eqn3
    type(ddt4)                 :: destruction_eqn4
    type(ddt4)                 :: tke, re_t, re_omega, f_turb, f_sublayer
    type(ddt4)                 :: omega, f_reattach, tu_percent_ltd
    type(ddt4)                 :: gammasst, rethetat, reth_part, thetat
    type(ddt4)                 :: f_length, re_thetac, dist_delta
    type(ddt4)                 :: f_onset1, f_onset2, f_onset3, f_onset
    type(ddt4)                 :: f_thetat1, f_thetat2, f_thetat
    type(ddt4)                 :: gamma_sep, gamma_eff, dkfactor, wrks
    type(ddt4)                 :: xlam, eff_lambda, eff_deriv
    type(ddt4)                 :: dlam,re_thetat, temp0, temp1
    type(ddt4)                 :: resr, dresr, dthetat

    integer                    :: nmo

  continue

    source_gammaretsst_ddt = zero
    ce2inv = one/ce2
!-------------------- to satisfy the nag -------------------------------------80
    betastar         = zero
    gamma_w          = zero
    beta             = zero
    rmut             = zero
    prod_k           = zero
    prod_w           = zero
    production_keqn  = zero
    production_eqn2  = zero
    destruction_keqn = zero
    destruction_eqn2 = zero

!---------------------------------  constants --------------------------------80
    rho         = qnode(1,i)
    rhoinv      = one / rho
    velu        = qnode(2,i)
    velv        = qnode(3,i)
    velw        = qnode(4,i)
    press       = qnode(5,i)

    xmrinv   = one / xmr
    cstar    = sutherland_constant / tref

!   The 4 turb variables are (in order) tke, omega, gammasst, and rethetat
!   Need to also set their derivatives (wrt themselves)
    tke           = turb(1,i)
    tke%d(1)      = one
    omega         = turb(2,i)
    omega%d(2)    = one
    gammasst      = turb(3,i)
    gammasst%d(3) = one
    rethetat      = turb(4,i)
    rethetat%d(4) = one

!---------------------------------  equation set -----------------------------80
    if ( eqn_set == compressible ) then
      temp    = gamma * press * rhoinv
      mu      = viscosity_law( cstar, temp )
    else
      temp   = one
      mu     = one
    end if


    betastar    = betastar_sst
    alpha_inner = beta1 / betastar - kappa*kappa/(sqrt(betastar)*sig_w1)
    alpha_outer = beta2 / betastar - kappa*kappa/(sqrt(betastar)*sig_w2)
    f1          = sst_f1(i)
    beta        = f1*beta1       + (one-f1)*beta2
    gamma_w     = f1*alpha_inner + (one-f1)*alpha_outer
!--------------------------------- gradients ---------------------------------80
    gradv = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                      &
                     , gradx(3,i), grady(3,i), gradz(3,i)                      &
                     , gradx(4,i), grady(4,i), gradz(4,i) )
    sij     = get_sij( gradv )
    sijsij  = get_sijsij( sij )
    vort    = get_vort( gradv )
    vortpluseps = vort + 1.0e-20_dp

!----------- Here is the bulk of the gamma-ret-sst source terms --------------80
    dist2=slen(i)**2
    fmuinv=one/mu
!   note that xmrinv is Re/Mach
    re_v=rho*dist2*sqrt(2.*sijsij)*xmrinv*fmuinv
    re_t=rho*tke*fmuinv/omega
    re_omega=rho*dist2*omega*xmrinv*xmrinv*fmuinv
    f_turb=ddt_exp(-((cp25*re_t)**4))
    f_sublayer=ddt_exp(-((cp005*re_omega)**2))
    if (rethetat%f < c400) then
       f_length=c39p8189 - c119p27em4*rethetat - c132p567em6*rethetat**2
    else if (rethetat%f >= c400 .and. rethetat%f < c596) then
       f_length=c263p404 - c123p939em2*rethetat+                               &
                           c194p548em5*rethetat**2-                            &
                           c101p695em8*rethetat**3
    else if (rethetat%f >= c596 .and. rethetat%f < c1200) then
       f_length=half-((rethetat-c596)*c3em4)
    else
       f_length=cp3188
    end if
    f_length=f_length*(one-f_sublayer)+c40*f_sublayer
    if (rethetat%f <= c1870) then
       re_thetac=rethetat-(c396p035em2 -                                       &
                           c120p656em4*rethetat+                               &
                           c868p23em6*rethetat**2-                             &
                           c696p506em9*rethetat**3+                            &
                           c174p105em12*rethetat**4)
    else
       re_thetac=rethetat-(c593p11+(rethetat-c1870)*cp482)
    end if
    f_onset1=re_v/(c2p193*re_thetac)
    f_onset2=ddt_max(f_onset1,f_onset1**4)
    f_onset2=ddt_min(f_onset2,two)
    f_onset3=ddt_max((one-(cp4*re_t)**3), zero)
    f_onset =ddt_max((f_onset2-f_onset3),zero)
    f_reattach=ddt_exp(-((cp05*re_t)**4))
!
    uuu=sqrt(velu**2+velv**2+velw**2)
    uuu=max(uuu,c1pem20)
    dist_delta=rho*xmrinv*uuu**2*fmuinv/(c375*vortpluseps*rethetat)
    f_thetat1=ddt_exp(-((c1pem5*re_omega)**2))*ddt_exp(-(dist_delta**4))
    f_thetat2=one-(((gammasst-ce2inv)/(one-ce2inv))**2)
    f_thetat = ddt_max(f_thetat1,f_thetat2)
    f_thetat = ddt_min(f_thetat,one)
!
    gamma_sep=ddt_max((re_v/(c3p235*re_thetac)-one),zero)
    gamma_sep=ddt_min((gamma_sep*f_reattach*s1),two)
    gamma_sep=gamma_sep*f_thetat
    gamma_eff=ddt_max(gammasst,gamma_sep)
!
    du_dx=(velu*gradx(2,i)+velv*gradx(3,i)+velw*gradx(4,i))/uuu
    du_dy=(velu*grady(2,i)+velv*grady(3,i)+velw*grady(4,i))/uuu
    du_dz=(velu*gradz(2,i)+velv*gradz(3,i)+velw*gradz(4,i))/uuu
    du_ds=velu/uuu*du_dx+velv/uuu*du_dy+velw/uuu*du_dz
!
    tu_percent_ltd=c100*ddt_sqrt(two*tke/c3)/uuu
    tu_percent_ltd=ddt_max(tu_percent_ltd,cp027)
    rey=rho*uuu*xmrinv*fmuinv
    if (tu_percent_ltd%f > c1p3) then
       reth_part=c331p5*(tu_percent_ltd-cp5658)**(-cp671)
    else
       reth_part=(c1173p51-c589p428*tu_percent_ltd+cp2196                      &
                 /(tu_percent_ltd*tu_percent_ltd))
    end if
    thetat=reth_part/rey
!   iterate to get re_thetat
    do nmo=1,10
       xlam=rho*thetat*thetat*fmuinv*du_ds*xmrinv
       dlam=two*rho*thetat*fmuinv*du_ds*xmrinv
       if (xlam%f <= -cp1) then
          xlam=-cp1
          dlam=zero
       else if (xlam%f >= cp1) then
          xlam=cp1
          dlam=zero
       end if
       if (xlam%f <= zero) then
          temp0=tu_percent_ltd/c1p5
          temp1=-(temp0*ddt_sqrt(temp0))
          eff_lambda=one-((-c12p986*xlam-                                      &
                            c123p66*xlam**2-                                   &
                            c405p689*xlam**3)*                                 &
                            ddt_exp(temp1))
          eff_deriv =(c12p986+two*c123p66*xlam+                                &
                             c3*c405p689*xlam**2)*                             &
                             dlam*ddt_exp(temp1)
       else
          eff_lambda=one+(cp275*(one-ddt_exp(-c35*xlam))*                      &
                             ddt_exp(-two*tu_percent_ltd))
          eff_deriv =c35*cp275*ddt_exp(-c35*xlam)*                             &
                             dlam*ddt_exp(-two*tu_percent_ltd)
       end if
       resr=reth_part*eff_lambda-rey*thetat
       dresr=reth_part*eff_deriv-rey
       dthetat=-resr/dresr
       dthetat=ddt_min(ddt_max(dthetat,-cp9*thetat),cp9*thetat)
       thetat=thetat+dthetat
    enddo
    re_thetat=rey*thetat
    re_thetat=ddt_max(re_thetat,c20)

!--------------- turbulent eddy viscosity ------------------------------------80
    rmut = mut(i) + c1pem6
    if ( strain_production ) then
      prod_k = two * rmut * sijsij
    else
      prod_k     = rmut * vort * vort
    end if
    prod_w  = gamma_w * prod_k / rmut

!-----------------------------------------------------------------------------80
!------------------------------ source terms ---------------------------------80
!-----------------------------------------------------------------------------80
    production_keqn  = abs( xmr * rhoinv * prod_k )
    destruction_keqn = betastar * tke * omega * xmrinv

    production_eqn2  = prod_w * xmr
    destruction_eqn2 = beta * omega * omega * xmrinv                           &
                       - ( one-sst_f1(i) ) * crossd(i)

    if (transition_4eqn_on) then
       dkfactor=ddt_max(gamma_eff,cp1)
       dkfactor=ddt_min(dkfactor,one)
       destruction_keqn=destruction_keqn*dkfactor
       production_keqn=production_keqn*gamma_eff
    end if

    production_eqn3  = f_length*ca1*sqrt(two*sijsij)*                          &
               ddt_sqrt(gammasst*f_onset)*(one-(ce1*gammasst))
    destruction_eqn3 = ca2*vort*gammasst*f_turb*(ce2*gammasst-one)

    wrks = (cthetat*rho*uuu*uuu*cp002*fmuinv*(one-f_thetat)*xmrinv)
    production_eqn4  = wrks*re_thetat
    destruction_eqn4 = wrks*rethetat

!----------------------------- correction models -----------------------------80
!       Limit on k-production term; try limiting eqn 3 and 4 also
    production_keqn = ddt_min( production_keqn, c20*destruction_keqn )
    production_eqn3 = ddt_min( production_eqn3, c20*destruction_eqn3 )
    production_eqn4 = ddt_min( production_eqn4, c20*destruction_eqn4 )
!----------------------------- mods to source term s--------------------------80
!       Ambient terms - Spalart and Rumsey, AIAA J. Vol.45 No.10
    if (keepambient) then
      destruction_keqn = destruction_keqn - betastar*k_inf*w_inf*xmrinv
      destruction_eqn2 = destruction_eqn2 - beta*w_inf*w_inf*xmrinv
    end if

    source_gammaretsst_ddt(1) = vol(i)*(production_keqn - destruction_keqn)
    source_gammaretsst_ddt(2) = vol(i)*(production_eqn2 - destruction_eqn2)
    if (transition_4eqn_on) then
      source_gammaretsst_ddt(3) = vol(i)*(production_eqn3 - destruction_eqn3)
      source_gammaretsst_ddt(4) = vol(i)*(production_eqn4 - destruction_eqn4)
    end if

    if (test_freestream) then
      source_gammaretsst_ddt = zero
    end if

  end function source_gammaretsst_ddt
  include 'viscosity_law.f90'
  include 'get_sij.f90'
  include 'get_sijsij.f90'
  include 'get_vort.f90'
  include 'set_gradv.f90'

end module turb_4eqn

module reference_q

  use kinddefs,          only : dp
  use info_depr,         only : twod
  use lmpi,              only : lmpi_master, lmpi_id, lmpi_nproc,              &
                                lmpi_conditional_stop, lmpi_bcast, lmpi_reduce,&
                                lmpi_max_and_maxid
  use lmpi_app,          only : lmpi_xfer
  use grid_types,        only : grid_type
  use solution_types,    only : soln_type

  implicit none

  private

  public :: write_reference_q,    read_reference_q
  public :: write_reference_q_2d, read_reference_q_2d

contains

!================================= WRITE_REFERENCE_Q_2D ======================80
!
! Write reference solution from 2D solution.
!
!=============================================================================80

  subroutine write_reference_q_2d( grid, soln )

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln

    integer :: i, iu, iostat1, n_q, n_turb, n_mf, dof0, pid
    integer :: dofg, ierr, proc_id

    character(len=80) :: filename

    real(dp), dimension(:,:), allocatable :: q_dof
    real(dp), dimension(:,:), allocatable :: r_dof

    logical, parameter :: debug = .false.

    real(dp) :: t_min, t_max, m_min, m_max

  continue

    m_min = huge(1._dp)
    m_max =-huge(1._dp)
    t_min = huge(1._dp)
    t_max =-huge(1._dp)

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = soln%n_q - soln%n_turb

    dofg = 0
    iu   = 716

    if ( grid%cc .or. n_turb == 0 ) then
      if ( lmpi_master ) then
        write(*,*) ' Failure within write_reference_q_2d...stopping.'
        write(*,*) ' ...only nc with turbulence supported.'
        write(*,*) ' ......grid%cc=',grid%cc
        write(*,*) ' ...soln%nturb=',soln%n_turb
      endif
      call lmpi_conditional_stop(1,'disallowed:write_reference_q_2d')
    endif

    iostat1 = 0
    if ( lmpi_master ) then

      filename = trim(grid%project) // '.refq_2d'

      open(unit=iu,file=filename,status='new',&
           form='unformatted',iostat=iostat1)

      if ( iostat1 /= 0 ) then
        write(*,"(1x,3a,i0)")                                     &
        ' Problem opening file via write_reference_q_2d...file= ',&
        trim(filename),                                           &
        ' with error code iostat1=',iostat1
      elseif ( lmpi_master ) then
        write(*,"(1x,3a)")                                       &
        'BOX: Writing unformatted file via write_reference_q_2d',&
        '...file= ',trim(filename)
        write(*,"(1x,a,i0,a,i0)") &
        'BOX: lmpi_id= ',lmpi_id,' nprocessors=',lmpi_nproc
      endif

    endif
    call lmpi_conditional_stop(iostat1,'open error:write_reference_q_2d')

    if ( lmpi_master ) write(iu) soln%dofg

    do pid = 0, lmpi_nproc-1

      dof0 = 0
      if ( pid == lmpi_id ) then
         dof0 = soln%dof0
      endif

      call lmpi_bcast( dof0, pid )

      allocate( q_dof(n_q,dof0) )
      allocate( r_dof(  3,dof0) )

      if ( pid == lmpi_id ) then
        do i=1,dof0
          q_dof(     1:n_mf,i) = soln%q_dof(1:n_mf,  i)
          q_dof(n_mf+1:n_q, i) = soln%turb( 1:n_turb,i)
          r_dof(          1,i) = grid%x(             i)
          r_dof(          2,i) = grid%y(             i)
          r_dof(          3,i) = grid%z(             i)
          m_min = min( m_min, soln%q_dof(2,i) )
          m_max = max( m_max, soln%q_dof(2,i) )
          t_min = min( t_min, soln%turb( 1,i) )
          t_max = max( t_max, soln%turb( 1,i) )
        enddo
        if ( debug ) write(*,*) &
        ' debug1 setting data...pid=',pid,' dof0=',dof0
      endif

      call lmpi_bcast( q_dof, pid )
      call lmpi_bcast( r_dof, pid )

      if ( lmpi_master ) then
        do i=1,dof0
          write(iu) q_dof(1:n_q,i), r_dof(1:3,i)
        enddo
        dofg = dofg + dof0
        if ( debug ) write(*,*) &
        ' debug2 writing data...pid=',pid,' dof0=',dofg
      endif

      deallocate( q_dof, r_dof )

    enddo

    ierr = 0
    if ( lmpi_master ) then
      ierr = dofg - soln%dofg
    endif
    call lmpi_conditional_stop(ierr,'incorrect entries:write_reference_q_2d')

    call lmpi_max_and_maxid(-real(m_min,dp), proc_id)
    call lmpi_bcast(              m_min,     proc_id)
    call lmpi_max_and_maxid(+real(m_max,dp), proc_id)
    call lmpi_bcast(              m_max,     proc_id)

    call lmpi_max_and_maxid(-real(t_min,dp), proc_id)
    call lmpi_bcast(              t_min,     proc_id)
    call lmpi_max_and_maxid(+real(t_max,dp), proc_id)
    call lmpi_bcast(              t_max,     proc_id)

    if ( lmpi_master ) then
      close(iu)
      write(*,"(1x,2a)")                                       &
      'BOX: Completed write via write_reference_q_2d...file= ',&
      trim(filename)
      write(*,"(1x,2(a,e20.10))")                              &
      'BOX: ...m_min=',m_min,' m_max=',m_max
      write(*,"(1x,2(a,e20.10))")                              &
      'BOX: ...t_min=',t_min,' t_max=',t_max
    endif

  end subroutine write_reference_q_2d

!================================= READ_REFERENCE_Q_2D =======================80
!
! Read reference 2D solution and set n_planes of 3D soln% arrays
! for both meanflow and turbulence.
!
!=============================================================================80

  subroutine read_reference_q_2d( grid, soln )

    use solve_box,   only : reference_meanflow, reference_turbulence
    use lmpi_app,    only : lmpi_set_grid_level

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: i, ii, iu, iostat1, n_q, n_turb, n_mf
    integer :: dofg, ierr, dofg_file

    real(dp), dimension(:,:), allocatable :: q_dof
    real(dp), dimension(:,:), allocatable :: r_dof

    logical, parameter :: debug = .false.

    character(len=80) :: filename

    integer :: plane, n_planes, proc_id

    real(dp) :: x, z, x_min, x_max, z_min, z_max

    real(dp) :: m_min, m_max, t_min, t_max

  continue

    m_min = huge(1._dp)
    m_max =-huge(1._dp)
    t_min = huge(1._dp)
    t_max =-huge(1._dp)

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = soln%n_q - soln%n_turb

    dofg = 0
    iu   = 716

    if ( grid%cc .or. n_turb == 0 ) then
      if ( lmpi_master ) then
        write(*,*) ' Failure within write_reference_q_2d...stopping.'
        write(*,*) ' ...only nc with turbulence supported.'
        write(*,*) ' ......grid%cc=',grid%cc
        write(*,*) ' ...soln%nturb=',soln%n_turb
      endif
      call lmpi_conditional_stop(2,'read_reference_q_2d')
    endif

    iostat1 = 0
    if ( lmpi_master ) then

      filename = trim(grid%project) // '.refq_2d'

      open(unit=iu,file=filename,status='old',&
           form='unformatted',iostat=iostat1)
    endif

    if ( iostat1 /= 0 ) then
      write(*,"(1x,2a)")                                       &
      ' Problem opening file via read_reference_q_2d...file= ',&
      trim(filename)
    elseif ( lmpi_master ) then
      write(*,"(1x,3a,i5)")                                               &
      'BOX: Reading file via read_reference_q_2d...file= ',trim(filename),&
      ' n_q=',n_q
      write(*,"(1x,a,L1)") &
      'BOX: .....initializing both meanflow and turbulence.'
      write(*,"(1x,a,L1)") &
      'BOX: .....reference_meanflow= ',reference_meanflow
      write(*,"(1x,a,L1)") &
      'BOX: ...reference_turbulence= ',reference_turbulence
    endif
    call lmpi_conditional_stop(iostat1,'open error:read_reference_q_2d')

    ierr = 0
    if ( lmpi_master ) then
      read(iu) dofg_file
      n_planes = soln%dofg/dofg_file
      ierr = dofg_file - soln%dofg/n_planes
      write(*,"(1x,3(a,i0))") ' dofg_file=',dofg_file,&
      ' soln%dofg=',soln%dofg,                        &
      ' n_planes=',n_planes
    endif
    call lmpi_conditional_stop(ierr,'dofg inconsistent:read_reference_q_2d')

    call lmpi_bcast( dofg_file )
    call lmpi_bcast( n_planes)

    allocate( q_dof(n_q,dofg_file) )
    allocate( r_dof(  3,dofg_file) )

    if ( lmpi_master ) then
      do i=1,dofg_file
        read(iu) q_dof(1:n_q,i), r_dof(1:3,i)
      enddo
      if ( debug ) write(*,*) &
      ' debug3 reading data dofg_file=',dofg_file,' dofg=',dofg
    endif

    call lmpi_bcast( q_dof )
    call lmpi_bcast( r_dof )

    x_min = huge(1._dp)
    x_max =-huge(1._dp)
    z_min = huge(1._dp)
    z_max =-huge(1._dp)
    do i=1,soln%dof0
      x_min = min( x_min, grid%x(i) )
      x_max = max( x_max, grid%x(i) )
      z_min = min( z_min, grid%z(i) )
      z_max = max( z_max, grid%z(i) )
    enddo

    if ( debug ) write(*,"(1x,a,i5,4(a,f20.10))") &
    ' debug4 lmpi_id=',lmpi_id,                   &
    ' x_min=',x_min,' x_max=',x_max,              &
    ' z_min=',z_min,' z_max=',z_max

    do ii=1,dofg_file

      x = r_dof(1,ii)
      z = r_dof(3,ii)

      if ( x >= x_min-1.0e-12_dp .and. &
           x <= x_max+1.0e-12_dp .and. &
           z >= z_min-1.0e-12_dp .and. &
           z <= z_max+1.0e-12_dp ) then

        plane = 0
        do i=1,soln%dof0
          if ( abs( grid%x(i) - x ) > 1.0e-12_dp ) cycle
          if ( abs( grid%z(i) - z ) > 1.0e-12_dp ) cycle
          soln%q_dof(1:n_mf,  i) = q_dof(     1:n_mf,ii)
          soln%turb( 1:n_turb,i) = q_dof(n_mf+1:n_q, ii)
          m_min = min( m_min, soln%q_dof(2,i) )
          m_max = max( m_max, soln%q_dof(2,i) )
          t_min = min( t_min, soln%turb( 1,i) )
          t_max = max( t_max, soln%turb( 1,i) )
          dofg = dofg + 1
          plane = plane + 1
          if ( plane == 3 ) exit
        enddo

      endif

    enddo

    call lmpi_max_and_maxid(-real(m_min,dp), proc_id)
    call lmpi_bcast(              m_min,     proc_id)
    call lmpi_max_and_maxid(+real(m_max,dp), proc_id)
    call lmpi_bcast(              m_max,     proc_id)

    call lmpi_max_and_maxid(-real(t_min,dp), proc_id)
    call lmpi_bcast(              t_min,     proc_id)
    call lmpi_max_and_maxid(+real(t_max,dp), proc_id)
    call lmpi_bcast(              t_max,     proc_id)

    ii = dofg
    call lmpi_reduce( ii,dofg)
    call lmpi_bcast(dofg)

    deallocate( q_dof, r_dof )

    ierr = dofg - soln%dofg
    if ( ierr /= 0 .and. lmpi_master ) then
      write(*,*) ' Failure within read_reference_q_2d...stopping.'
      write(*,*) ' ...dofg=',dofg,' soln%dofg=',soln%dofg
    endif
    call lmpi_conditional_stop(ierr,'inconsistent dofg:read_reference_q_2d')

    call lmpi_set_grid_level(grid%igrid)
    call lmpi_xfer( soln%q_dof )
    call lmpi_xfer( soln%turb  )

    if ( lmpi_master ) then
      close(iu)
      write(*,"(1x,2a)")                                     &
      'BOX: Completed read via read_reference_q_2d...file= ',&
      trim(filename)
      write(*,"(1x,2(a,e20.10))")                              &
      'BOX: ...m_min=',m_min,' m_max=',m_max
      write(*,"(1x,2(a,e20.10))")                              &
      'BOX: ...t_min=',t_min,' t_max=',t_max
    endif

  end subroutine read_reference_q_2d

!================================= WRITE_REFERENCE_Q =========================80
!
! Write reference solution.
!
!=============================================================================80

  subroutine write_reference_q( grid, soln )

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln

    integer :: i, iu, iostat1, n_q, n_turb, n_mf, dof0, pid
    integer :: dofg, ierr

    character(len=80) :: filename

    real(dp), dimension(:,:), allocatable :: q_dof
    integer,  dimension(:),   allocatable :: l2g

    logical, parameter :: debug = .false.

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = soln%n_q - soln%n_turb


    dofg = 0
    iu   = 716

    if ( grid%cc .or. n_turb /= 1 ) then
      if ( lmpi_master ) then
        write(*,*) ' Failure within write_reference_q...stopping.'
        write(*,*) ' ...only nc and 1-eq turbulence supported.'
        write(*,*) ' ......grid%cc=',grid%cc
        write(*,*) ' ...soln%nturb=',soln%n_turb
      endif
      call lmpi_conditional_stop(1,'disallowed:write_reference_q')
    endif

    iostat1 = 0
    if ( lmpi_master ) then

      filename = trim(grid%project) // '.refq'

      open(unit=iu,file=filename,status='new',&
           form='unformatted',iostat=iostat1)

      if ( iostat1 /= 0 ) then
        write(*,"(1x,3a,i0)")                                                 &
        ' Problem opening file via write_reference_q...file= ',trim(filename),&
        ' with error code iostat1=',iostat1
      elseif ( lmpi_master ) then
        write(*,"(1x,2a)")                                             &
        'BOX: Writing unformatted file via write_reference_q...file= ',&
        trim(filename)
        write(*,"(1x,a,i0,a,i0)") &
        'BOX: lmpi_id= ',lmpi_id,' nprocessors=',lmpi_nproc
      endif

    endif
    call lmpi_conditional_stop(iostat1,'open error:write_reference_q')

    if ( lmpi_master ) write(iu) soln%dofg

    do pid = 0, lmpi_nproc-1

      dof0 = 0
      if ( pid == lmpi_id ) then
         dof0 = soln%dof0
      endif

      call lmpi_bcast( dof0, pid )

      allocate( q_dof(n_q,dof0) )
      allocate(   l2g(    dof0) )

      if ( pid == lmpi_id ) then
        do i=1,dof0
          q_dof(     1:n_mf,i) = soln%q_dof(1:n_mf,  i)
          q_dof(n_mf+1:n_q, i) = soln%turb( 1:n_turb,i)
          l2g(              i) = grid%l2g(           i)
        enddo
        if ( debug ) write(*,*) &
        ' debug1 setting data...pid=',pid,' dof0=',dof0
      endif

      call lmpi_bcast( q_dof, pid )
      call lmpi_bcast( l2g,   pid )

      if ( lmpi_master ) then
        do i=1,dof0
          write(iu) l2g(i),q_dof(1:n_q,i)
        enddo
        dofg = dofg + dof0
        if ( debug ) write(*,*) &
        ' debug2 writing data...pid=',pid,' dof0=',dofg
      endif

      deallocate( q_dof, l2g )

    enddo

    ierr = 0
    if ( lmpi_master ) then
      ierr = dofg - soln%dofg
    endif
    call lmpi_conditional_stop(ierr,'incorrect entries:write_reference_q')

    if ( lmpi_master ) then
      close(iu)
      write(*,"(1x,2a)")                                    &
      'BOX: Completed write via write_reference_q...file= ',&
      trim(filename)
    endif

  end subroutine write_reference_q

!================================= READ_REFERENCE_Q ==========================80
!
! Read reference solution.
!
!=============================================================================80

  subroutine read_reference_q( grid, soln )

    use twod_util,   only : copy_array_2d
    use solve_box,   only : reference_meanflow, reference_turbulence
    use allocations, only : my_realloc_ptr
    use lmpi_app,    only : lmpi_set_grid_level

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: i, iu, iostat1, n_q, n_turb, n_mf, dof0, pid
    integer :: dofg, ierr, dofg_file

    real(dp), dimension(:,:), allocatable :: q_dof
    integer,  dimension(:),   allocatable :: l2g

    logical, parameter :: debug = .false.

    character(len=80) :: filename

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = soln%n_q - soln%n_turb

    dofg = 0
    iu   = 716

    if ( grid%cc .or. soln%n_turb /= 1 ) then
      if ( lmpi_master ) then
        write(*,*) ' Failure within write_reference_q...stopping.'
        write(*,*) ' ...only nc and 1-eq turbulence supported.'
        write(*,*) ' ......grid%cc=',grid%cc
        write(*,*) ' ...soln%nturb=',soln%n_turb
      endif
      call lmpi_conditional_stop(2,'read_reference_q')
    endif

    iostat1 = 0
    if ( lmpi_master ) then

      filename = trim(grid%project) // '.refq'

      open(unit=iu,file=filename,status='old',&
           form='unformatted',iostat=iostat1)
    endif

    if ( iostat1 /= 0 ) then
      write(*,"(1x,2a)") ' Problem opening file via read_reference_q...file= ',&
                         trim(filename)
    elseif ( lmpi_master ) then
      write(*,"(1x,3a,i5)")                                            &
      'BOX: Reading file via read_reference_q...file= ',trim(filename),&
      ' n_q=',n_q
      write(*,"(1x,a,L1)") &
      'BOX: .....initializing both meanflow and turbulence.'
      write(*,"(1x,a,L1)") &
      'BOX: .....reference_meanflow= ',reference_meanflow
      write(*,"(1x,a,L1)") &
      'BOX: ...reference_turbulence= ',reference_turbulence
    endif
    call lmpi_conditional_stop(iostat1,'open error:read_reference_q')

    ierr = 0
    if ( lmpi_master ) then
      read(iu) dofg_file
      ierr = dofg_file - soln%dofg
      write(*,"(1x,2(a,i0))") ' dofg_file=',dofg_file,' soln%dofg=',soln%dofg
    endif
    call lmpi_conditional_stop(ierr,'dofg inconsistent:read_reference_q')

    if ( size( soln%q_dof_res0, 1 ) == 1 .and. &
         size( soln%q_dof_res0, 2 ) == 1 ) then
      call my_realloc_ptr(soln%q_dof_res0,soln%n_q,grid%nnodes01)
    endif

    ierr =      soln%n_q - size( soln%q_dof_res0, 1 )
    call lmpi_conditional_stop(ierr,'q_dof_res0:size1:read_reference_q')
    ierr = grid%nnodes01 - size( soln%q_dof_res0, 2 )
    call lmpi_conditional_stop(ierr,'q_dof_res0:size2:read_reference_q')

    do pid = 0, lmpi_nproc-1

      dof0 = 0
      if ( pid == lmpi_id ) then
         dof0 = soln%dof0
      endif

      call lmpi_bcast( dof0, pid )

      allocate( q_dof(n_q,dof0) )
      allocate(   l2g(    dof0) )

      if ( lmpi_master ) then
        do i=1,dof0
          read(iu) l2g(i),q_dof(1:n_q,i)
        enddo
        if ( debug ) write(*,*) &
        ' debug3 reading data pid=',pid,' dof0=',dof0,' dofg=',dofg
      endif

      call lmpi_bcast( q_dof, 0 )
      call lmpi_bcast( l2g,   0 )

      ! Check for perfect correspondence of l2g data
      ! (same partitioning vector for write and read)

      ierr = 0
      if ( pid == lmpi_id ) then
        do i=1,dof0
          if ( l2g(i) == grid%l2g(i) ) cycle
          ierr = 1
          write(*,*) ' pid,i,l2g=',pid,i,l2g(i),grid%l2g(i)
          exit
        enddo
        if ( debug .or. ierr > 0 ) write(*,*) &
        ' debug4 pid=',pid,' dof0=',dof0,' partitition_check:ierr=',ierr
      endif

      call lmpi_bcast( ierr, pid )

      if ( ierr == 0 ) then

        ! Perfect correspondence - partitioning between
        ! file contents and current data are identical.

        if ( pid == lmpi_id ) then
          do i=1,dof0
            soln%q_dof_res0(1:n_q,i) = q_dof(1:n_q,i)
          enddo
          dofg = dofg + dof0

          if ( debug ) write(*,*) &
          ' debug5 setting data pid=',pid,'dof0=',dof0,' dofg=',dofg
        endif

      else

         ! Not coded - more extensive search needed.

        call lmpi_conditional_stop(ierr,&
        'different processors not programmed:read_reference_q')

      endif

      call lmpi_bcast(dofg,pid)

      deallocate( q_dof, l2g )

      if ( debug .and. lmpi_master ) write(*,*) &
      ' debug6 Completed pid=',pid,' dofg=',dofg

    enddo

    ierr = dofg - soln%dofg
    if ( ierr /= 0 .and. lmpi_master ) then
      write(*,*) ' Failure within read_reference_q...stopping.'
      write(*,*) ' ...dofg=',dofg,' soln%dofg=',soln%dofg
    endif
    call lmpi_conditional_stop(ierr,'inconsistent dofg:read_reference_q')

    if ( twod ) then
      call copy_array_2d( grid%nnodes01, grid%nnodes0_2d, grid%node_pairs_2d, &
                          soln%q_dof_res0, size(soln%q_dof_res0,1) )
    endif

    call lmpi_set_grid_level(grid%igrid)
    call lmpi_xfer( soln%q_dof_res0 )

    ! Install into soln% arrays for both meanflow and turbulence.

    do i=1,grid%nnodes01
      soln%q_dof( 1:n_mf,  i) = soln%q_dof_res0(     1:n_mf,i)
      soln%turb(  1:n_turb,i) = soln%q_dof_res0(n_mf+1:n_q, i)
    enddo

    if ( lmpi_master ) then
      close(iu)
      write(*,"(1x,2a)")                                  &
      'BOX: Completed read via read_reference_q...file= ',&
      trim(filename)
    endif

  end subroutine read_reference_q

end module reference_q

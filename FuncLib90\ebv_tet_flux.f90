!=============================== EBV_TET_FLUX ================================80
!
! Edge-based viscous fluxes for tetrahedra
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function ebv_tet_flux(r1,u1,v1,w1,p1,r2,u2,v2,w2,p2,cstar,cgp,cgpt,     &
                             amut1,amut2,weight,xmr)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_1p5
    use fluid,           only : gamma

    real(dp), intent(in) :: r1,u1,v1,w1,p1,r2,u2,v2,w2,p2,cstar,cgp,cgpt
    real(dp), intent(in) :: amut1,amut2,xmr

    real(dp), dimension(10), intent(in) :: weight
    real(dp), dimension(5)              :: ebv_tet_flux

    real(dp) :: t1,t2,du,dv,dw,da,mu1,mu2,mucgp,mu,umu,vmu,wmu

  continue

    t1 = gamma*p1/r1
    t2 = gamma*p2/r2

    du = u2-u1
    dv = v2-v1
    dw = w2-w1
    da = t2-t1

    mu1 = (my_1 + cstar)/(t1 + cstar)*t1**my_1p5
    mu2 = (my_1 + cstar)/(t2 + cstar)*t2**my_1p5

    mucgp = my_half*(cgp*(mu1 + mu2) + cgpt*(amut1 + amut2))

    mu1 = mu1 + amut1
    mu2 = mu2 + amut2

    mu = my_half*(mu1 + mu2)

    umu = my_half*(u2+u1)*mu
    vmu = my_half*(v2+v1)*mu
    wmu = my_half*(w2+w1)*mu

    ebv_tet_flux(1) =  my_0
    ebv_tet_flux(2) = -mu*xmr*(weight(1)*du + weight(2)*dv + weight(3)*dw)
    ebv_tet_flux(3) = -mu*xmr*(weight(4)*du + weight(5)*dv + weight(6)*dw)
    ebv_tet_flux(4) = -mu*xmr*(weight(7)*du + weight(8)*dv + weight(9)*dw)
    ebv_tet_flux(5) = -xmr*(du*(umu*weight(1) + vmu*weight(4) + wmu*weight(7)) &
                          + dv*(umu*weight(2) + vmu*weight(5) + wmu*weight(8)) &
                          + dw*(umu*weight(3) + vmu*weight(6) + wmu*weight(9)) &
                          + mucgp*da*weight(10))

  end function ebv_tet_flux

module dshape_cost

  implicit none

  private

  public :: pressurexyz, pressurexyzi, skinfricxyz, skinfricxyzi, dfdx_cpstar

contains

!============================== PRESSUREXYZ ==================================80
!
!  This routine computes the derivatives of the pressure forces wrt shape
!
!=============================================================================80

  subroutine pressurexyz( nnodes01, x, y, z, qnode,                            &
                          nbnode, nbfacet, nbfaceq, face_bit, face_bitq,       &
                          ibnode, f2ntb, f2nqb, design,force,ia,ja,dfdx,       &
                          small_stencil_nnz01, ib, nbound, bcforce, ndim )

    use kinddefs,          only : dp
    use info_depr,         only : xmach,alpha,yaw
    use fluid,             only : gamma
    use ivals,             only : p0
    use design_types,      only : design_type
    use force_types,       only : force_type
    use forces,            only : cl_id, cd_id, clp_id, cdp_id, clcd_id,       &
                                  cmx_id, cmy_id, cmz_id, cmxp_id, cmyp_id,    &
                                  cmzp_id, rotor_thrust_id, cx_id, cy_id,      &
                                  cz_id, cxp_id, cyp_id, czp_id, fom_id,       &
                                  propeff_id, powerx_id, powery_id, powerz_id
    use refgeom,           only : sref,xmc,ymc,zmc,bref,cref
    use fun3d_constants,   only : my_1p5, my_1
    use custom_transforms, only : thrust_angle

    integer,                                intent(in)    :: nnodes01, nbnode
    integer,                                intent(in)    :: nbfacet, ib, ndim
    integer,                                intent(in)    :: nbfaceq
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,                                intent(in)    :: nbound
    integer,  dimension(nbfacet),           intent(in)    :: face_bit
    integer,  dimension(nbfaceq),           intent(in)    :: face_bitq
    integer,  dimension(nbnode),            intent(in)    :: ibnode
    integer,  dimension(nbfacet,5),         intent(in)    :: f2ntb
    integer,  dimension(nbfaceq,6),         intent(in)    :: f2nqb
    integer,  dimension(:),                 intent(in)    :: ia, ja
    real(dp), dimension(nnodes01),          intent(in)    :: x, y, z
    real(dp), dimension(ndim,nnodes01),     intent(in)    :: qnode
    type(design_type),                      intent(in)    :: design
    type(force_type),                       intent(in)    :: force
    type(force_type), dimension(nbound),    intent(in)    :: bcforce
    real(dp), dimension(3,small_stencil_nnz01,design%nfunctions),    &
                                            intent(inout) :: dfdx

    integer :: n,node1,node2,node3,node4
    integer :: i,j,k,ioff1,ioff2,ioff3,ioff4,icol

    real(dp) :: conv,csa,csy,pi,sna,sny

    real(dp) :: x1,y1,z1,x2,y2,z2,x3,y3,z3,x4,y4,z4

    real(dp) :: xnorm
    real(dp) :: xnormx1,xnormx2,xnormx3,xnormx4
    real(dp) :: xnormy1,xnormy2,xnormy3,xnormy4
    real(dp) :: xnormz1,xnormz2,xnormz3,xnormz4

    real(dp) :: ynorm
    real(dp) :: ynormx1,ynormx2,ynormx3,ynormx4
    real(dp) :: ynormy1,ynormy2,ynormy3,ynormy4
    real(dp) :: ynormz1,ynormz2,ynormz3,ynormz4

    real(dp) :: znorm
    real(dp) :: znormx1,znormx2,znormx3,znormx4
    real(dp) :: znormy1,znormy2,znormy3,znormy4
    real(dp) :: znormz1,znormz2,znormz3,znormz4

    real(dp) :: ax,axx1,axx2,axx3,axy1,axy2,axy3,axz1,axz2,axz3
    real(dp) :: ay,ayx1,ayx2,ayx3,ayy1,ayy2,ayy3,ayz1,ayz2,ayz3
    real(dp) :: az,azx1,azx2,azx3,azy1,azy2,azy3,azz1,azz2,azz3

    real(dp) :: bx,bxx1,bxx2,bxx3,bxy1,bxy2,bxy3,bxz1,bxz2,bxz3
    real(dp) :: by,byx1,byx2,byx3,byy1,byy2,byy3,byz1,byz2,byz3
    real(dp) :: bz,bzx1,bzx2,bzx3,bzy1,bzy2,bzy3,bzz1,bzz2,bzz3

    real(dp) :: cp,cpx1,cpx2,cpx3,cpy1,cpy2,cpy3,cpz1,cpz2,cpz3
    real(dp) :: cpx4,cpy4,cpz4

    real(dp) :: csax1,csax2,csax3,csay1,csay2,csay3,csaz1,csaz2,csaz3
    real(dp) :: csyx1,csyx2,csyx3,csyy1,csyy2,csyy3,csyz1,csyz2,csyz3
    real(dp) :: csyx4,csyy4,csyz4,csax4,csay4,csaz4

    real(dp) :: snax1,snax2,snax3,snay1,snay2,snay3,snaz1,snaz2,snaz3
    real(dp) :: snyx1,snyx2,snyx3,snyy1,snyy2,snyy3,snyz1,snyz2,snyz3
    real(dp) :: snyx4,snyy4,snyz4,snax4,snay4,snaz4

    real(dp) :: clwx1,clwx2,clwx3,clwy1,clwy2,clwy3,clwz1,clwz2,clwz3
    real(dp) :: cdwx1,cdwx2,cdwx3,cdwy1,cdwy2,cdwy3,cdwz1,cdwz2,cdwz3
    real(dp) :: clwx4,clwy4,clwz4,cdwx4,cdwy4,cdwz4

    real(dp) :: p1
    real(dp) :: p1x1,p1x2,p1x3,p1y1,p1y2,p1y3,p1z1,p1z2,p1z3,p1x4,p1y4,p1z4

    real(dp) :: p2
    real(dp) :: p2x1,p2x2,p2x3,p2y1,p2y2,p2y3,p2z1,p2z2,p2z3,p2x4,p2y4,p2z4

    real(dp) :: p3
    real(dp) :: p3x1,p3x2,p3x3,p3y1,p3y2,p3y3,p3z1,p3z2,p3z3,p3x4,p3y4,p3z4

    real(dp) :: p4
    real(dp) :: p4x1,p4x2,p4x3,p4y1,p4y2,p4y3,p4z1,p4z2,p4z3,p4x4,p4y4,p4z4

    real(dp) :: press
    real(dp) :: pressx1,pressx2,pressx3,pressx4
    real(dp) :: pressy1,pressy2,pressy3,pressy4
    real(dp) :: pressz1,pressz2,pressz3,pressz4

    real(dp) :: dcx
    real(dp) :: dcxx1,dcxx2,dcxx3,dcxy1,dcxy2,dcxy3,dcxz1,dcxz2,dcxz3
    real(dp) :: dcxx4,dcxy4,dcxz4

    real(dp) :: dcy
    real(dp) :: dcyx1,dcyx2,dcyx3,dcyy1,dcyy2,dcyy3,dcyz1,dcyz2,dcyz3
    real(dp) :: dcyx4,dcyy4,dcyz4

    real(dp) :: dcz
    real(dp) :: dczx1,dczx2,dczx3,dczy1,dczy2,dczy3,dczz1,dczz2,dczz3
    real(dp) :: dczx4,dczy4,dczz4

    real(dp) :: cd,cdp,cl,clp,clcd,weight,target,power,fom,rotor_thrust,propeff
    real(dp) :: clwx1_final,clwx2_final,clwx3_final,clwx4_final
    real(dp) :: clwy1_final,clwy2_final,clwy3_final,clwy4_final
    real(dp) :: clwz1_final,clwz2_final,clwz3_final,clwz4_final
    real(dp) :: cx,cy,cz,cxp,cyp,czp
    real(dp) :: cxx1,cxx2,cxx3,cxx4
    real(dp) :: cxy1,cxy2,cxy3,cxy4
    real(dp) :: cxz1,cxz2,cxz3,cxz4
    real(dp) :: cyx1,cyx2,cyx3,cyx4
    real(dp) :: cyy1,cyy2,cyy3,cyy4
    real(dp) :: cyz1,cyz2,cyz3,cyz4
    real(dp) :: czx1,czx2,czx3,czx4
    real(dp) :: czy1,czy2,czy3,czy4
    real(dp) :: czz1,czz2,czz3,czz4
    real(dp) :: cxx1_final,cxx2_final,cxx3_final,cxx4_final
    real(dp) :: cxy1_final,cxy2_final,cxy3_final,cxy4_final
    real(dp) :: cxz1_final,cxz2_final,cxz3_final,cxz4_final
    real(dp) :: cyx1_final,cyx2_final,cyx3_final,cyx4_final
    real(dp) :: cyy1_final,cyy2_final,cyy3_final,cyy4_final
    real(dp) :: cyz1_final,cyz2_final,cyz3_final,cyz4_final
    real(dp) :: czx1_final,czx2_final,czx3_final,czx4_final
    real(dp) :: czy1_final,czy2_final,czy3_final,czy4_final
    real(dp) :: czz1_final,czz2_final,czz3_final,czz4_final
    real(dp) :: cdwx1_final,cdwx2_final,cdwx3_final,cdwx4_final
    real(dp) :: cdwy1_final,cdwy2_final,cdwy3_final,cdwy4_final
    real(dp) :: cdwz1_final,cdwz2_final,cdwz3_final,cdwz4_final
    real(dp) :: cmxx1_final,cmxx2_final,cmxx3_final,cmxx4_final
    real(dp) :: cmxy1_final,cmxy2_final,cmxy3_final,cmxy4_final
    real(dp) :: cmxz1_final,cmxz2_final,cmxz3_final,cmxz4_final
    real(dp) :: cmyx1_final,cmyx2_final,cmyx3_final,cmyx4_final
    real(dp) :: cmyy1_final,cmyy2_final,cmyy3_final,cmyy4_final
    real(dp) :: cmyz1_final,cmyz2_final,cmyz3_final,cmyz4_final
    real(dp) :: cmzx1_final,cmzx2_final,cmzx3_final,cmzx4_final
    real(dp) :: cmzy1_final,cmzy2_final,cmzy3_final,cmzy4_final
    real(dp) :: cmzz1_final,cmzz2_final,cmzz3_final,cmzz4_final

    real(dp) :: cmx,cmy,cmz,cmxp,cmyp,cmzp,sint,cost
    real(dp) :: xmid,xmidx1,xmidx2,xmidx3,xmidx4
    real(dp) :: ymid,ymidy1,ymidy2,ymidy3,ymidy4
    real(dp) :: zmid,zmidz1,zmidz2,zmidz3,zmidz4
    real(dp) :: cmxx1,cmxx2,cmxx3,cmxy1,cmxy2,cmxy3,cmxz1,cmxz2,cmxz3
    real(dp) :: cmyx1,cmyx2,cmyx3,cmyy1,cmyy2,cmyy3,cmyz1,cmyz2,cmyz3
    real(dp) :: cmzx1,cmzx2,cmzx3,cmzy1,cmzy2,cmzy3,cmzz1,cmzz2,cmzz3
    real(dp) :: cmxx4,cmxy4,cmxz4
    real(dp) :: cmyx4,cmyy4,cmyz4
    real(dp) :: cmzx4,cmzy4,cmzz4
    real(dp) :: width, base, factor, average, const
    real(dp) :: u,v,w,rpowerx,rpowery,rpowerz
    real(dp) :: powerxx1,powerxx2,powerxx3,powerxx4
    real(dp) :: powerxy1,powerxy2,powerxy3,powerxy4
    real(dp) :: powerxz1,powerxz2,powerxz3,powerxz4
    real(dp) :: poweryx1,poweryx2,poweryx3,poweryx4
    real(dp) :: poweryy1,poweryy2,poweryy3,poweryy4
    real(dp) :: poweryz1,poweryz2,poweryz3,poweryz4
    real(dp) :: powerzx1,powerzx2,powerzx3,powerzx4
    real(dp) :: powerzy1,powerzy2,powerzy3,powerzy4
    real(dp) :: powerzz1,powerzz2,powerzz3,powerzz4
    real(dp) :: powerxx1_final,powerxx2_final,powerxx3_final,powerxx4_final
    real(dp) :: powerxy1_final,powerxy2_final,powerxy3_final,powerxy4_final
    real(dp) :: powerxz1_final,powerxz2_final,powerxz3_final,powerxz4_final
    real(dp) :: poweryx1_final,poweryx2_final,poweryx3_final,poweryx4_final
    real(dp) :: poweryy1_final,poweryy2_final,poweryy3_final,poweryy4_final
    real(dp) :: poweryz1_final,poweryz2_final,poweryz3_final,poweryz4_final
    real(dp) :: powerzx1_final,powerzx2_final,powerzx3_final,powerzx4_final
    real(dp) :: powerzy1_final,powerzy2_final,powerzy3_final,powerzy4_final
    real(dp) :: powerzz1_final,powerzz2_final,powerzz3_final,powerzz4_final

    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_4   = 4.0_dp

    continue

    const  = 0.0_dp
    rotor_thrust = 0.0_dp
    fom    = 0.0_dp
    propeff= 0.0_dp
    clcd   = 0.0_dp

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi
    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)
    sny=sin(yaw/conv)

    fcn_loop : do j = 1, design%nfunctions

            tri_loop : do n = 1, nbfacet
              if(face_bit(n)==1) then
                node1 = ibnode(f2ntb(n,1))
                node2 = ibnode(f2ntb(n,2))
                node3 = ibnode(f2ntb(n,3))

                x1    = x(node1)
                y1    = y(node1)
                z1    = z(node1)

                x2    = x(node2)
                y2    = y(node2)
                z2    = z(node2)

                x3 = x(node3)
                y3 = y(node3)
                z3 = z(node3)

                ax = x2 - x1
                  axx1 = -1.0_dp
                  axx2 = 1.0_dp
                  axx3 = 0.0_dp
                  axy1 = 0.0_dp
                  axy2 = 0.0_dp
                  axy3 = 0.0_dp
                  axz1 = 0.0_dp
                  axz2 = 0.0_dp
                  axz3 = 0.0_dp
                ay = y2 - y1
                  ayx1 = 0.0_dp
                  ayx2 = 0.0_dp
                  ayx3 = 0.0_dp
                  ayy1 = -1.0_dp
                  ayy2 = 1.0_dp
                  ayy3 = 0.0_dp
                  ayz1 = 0.0_dp
                  ayz2 = 0.0_dp
                  ayz3 = 0.0_dp
                az = z2 - z1
                  azx1 = 0.0_dp
                  azx2 = 0.0_dp
                  azx3 = 0.0_dp
                  azy1 = 0.0_dp
                  azy2 = 0.0_dp
                  azy3 = 0.0_dp
                  azz1 = -1.0_dp
                  azz2 = 1.0_dp
                  azz3 = 0.0_dp
                bx = x3 - x1
                  bxx1 = -1.0_dp
                  bxx2 = 0.0_dp
                  bxx3 = 1.0_dp
                  bxy1 = 0.0_dp
                  bxy2 = 0.0_dp
                  bxy3 = 0.0_dp
                  bxz1 = 0.0_dp
                  bxz2 = 0.0_dp
                  bxz3 = 0.0_dp
                by = y3 - y1
                  byx1 = 0.0_dp
                  byx2 = 0.0_dp
                  byx3 = 0.0_dp
                  byy1 = -1.0_dp
                  byy2 = 0.0_dp
                  byy3 = 1.0_dp
                  byz1 = 0.0_dp
                  byz2 = 0.0_dp
                  byz3 = 0.0_dp
                bz = z3 - z1
                  bzx1 = 0.0_dp
                  bzx2 = 0.0_dp
                  bzx3 = 0.0_dp
                  bzy1 = 0.0_dp
                  bzy2 = 0.0_dp
                  bzy3 = 0.0_dp
                  bzz1 = -1.0_dp
                  bzz2 = 0.0_dp
                  bzz3 = 1.0_dp

!  norm points outward, away from grid interior.
!  norm magnitude is area of surface triangle.

                xnorm =-0.5_dp*(ay*bz - az*by)
                 xnormx1 = -0.5_dp*(ay*bzx1 + bz*ayx1 - az*byx1 - by*azx1)
                 xnormx2 = -0.5_dp*(ay*bzx2 + bz*ayx2 - az*byx2 - by*azx2)
                 xnormx3 = -0.5_dp*(ay*bzx3 + bz*ayx3 - az*byx3 - by*azx3)

                 xnormy1 = -0.5_dp*(ay*bzy1 + bz*ayy1 - az*byy1 - by*azy1)
                 xnormy2 = -0.5_dp*(ay*bzy2 + bz*ayy2 - az*byy2 - by*azy2)
                 xnormy3 = -0.5_dp*(ay*bzy3 + bz*ayy3 - az*byy3 - by*azy3)

                 xnormz1 = -0.5_dp*(ay*bzz1 + bz*ayz1 - az*byz1 - by*azz1)
                 xnormz2 = -0.5_dp*(ay*bzz2 + bz*ayz2 - az*byz2 - by*azz2)
                 xnormz3 = -0.5_dp*(ay*bzz3 + bz*ayz3 - az*byz3 - by*azz3)

                ynorm = 0.5_dp*(ax*bz - az*bx)
                 ynormx1 = 0.5_dp*(ax*bzx1 + bz*axx1 - az*bxx1 - bx*azx1)
                 ynormx2 = 0.5_dp*(ax*bzx2 + bz*axx2 - az*bxx2 - bx*azx2)
                 ynormx3 = 0.5_dp*(ax*bzx3 + bz*axx3 - az*bxx3 - bx*azx3)

                 ynormy1 = 0.5_dp*(ax*bzy1 + bz*axy1 - az*bxy1 - bx*azy1)
                 ynormy2 = 0.5_dp*(ax*bzy2 + bz*axy2 - az*bxy2 - bx*azy2)
                 ynormy3 = 0.5_dp*(ax*bzy3 + bz*axy3 - az*bxy3 - bx*azy3)

                 ynormz1 = 0.5_dp*(ax*bzz1 + bz*axz1 - az*bxz1 - bx*azz1)
                 ynormz2 = 0.5_dp*(ax*bzz2 + bz*axz2 - az*bxz2 - bx*azz2)
                 ynormz3 = 0.5_dp*(ax*bzz3 + bz*axz3 - az*bxz3 - bx*azz3)

                znorm =-0.5_dp*(ax*by - ay*bx)
                 znormx1 = -0.5_dp*(ax*byx1 + by*axx1 - ay*bxx1 - bx*ayx1)
                 znormx2 = -0.5_dp*(ax*byx2 + by*axx2 - ay*bxx2 - bx*ayx2)
                 znormx3 = -0.5_dp*(ax*byx3 + by*axx3 - ay*bxx3 - bx*ayx3)

                 znormy1 = -0.5_dp*(ax*byy1 + by*axy1 - ay*bxy1 - bx*ayy1)
                 znormy2 = -0.5_dp*(ax*byy2 + by*axy2 - ay*bxy2 - bx*ayy2)
                 znormy3 = -0.5_dp*(ax*byy3 + by*axy3 - ay*bxy3 - bx*ayy3)

                 znormz1 = -0.5_dp*(ax*byz1 + by*axz1 - ay*bxz1 - bx*ayz1)
                 znormz2 = -0.5_dp*(ax*byz2 + by*axz2 - ay*bxz2 - bx*ayz2)
                 znormz3 = -0.5_dp*(ax*byz3 + by*axz3 - ay*bxz3 - bx*ayz3)

                p1    = qnode(5,node1)
                  p1x1 = 0.0_dp
                  p1x2 = 0.0_dp
                  p1x3 = 0.0_dp

                  p1y1 = 0.0_dp
                  p1y2 = 0.0_dp
                  p1y3 = 0.0_dp

                  p1z1 = 0.0_dp
                  p1z2 = 0.0_dp
                  p1z3 = 0.0_dp

                p2    = qnode(5,node2)
                  p2x1 = 0.0_dp
                  p2x2 = 0.0_dp
                  p2x3 = 0.0_dp

                  p2y1 = 0.0_dp
                  p2y2 = 0.0_dp
                  p2y3 = 0.0_dp

                  p2z1 = 0.0_dp
                  p2z2 = 0.0_dp
                  p2z3 = 0.0_dp

                p3    = qnode(5,node3)
                  p3x1 = 0.0_dp
                  p3x2 = 0.0_dp
                  p3x3 = 0.0_dp

                  p3y1 = 0.0_dp
                  p3y2 = 0.0_dp
                  p3y3 = 0.0_dp

                  p3z1 = 0.0_dp
                  p3z2 = 0.0_dp
                  p3z3 = 0.0_dp

                press = (p1 + p2 + p3)/3.0_dp
                  pressx1 = (p1x1 + p2x1 + p3x1) / 3.0_dp
                  pressx2 = (p1x2 + p2x2 + p3x2) / 3.0_dp
                  pressx3 = (p1x3 + p2x3 + p3x3) / 3.0_dp

                  pressy1 = (p1y1 + p2y1 + p3y1) / 3.0_dp
                  pressy2 = (p1y2 + p2y2 + p3y2) / 3.0_dp
                  pressy3 = (p1y3 + p2y3 + p3y3) / 3.0_dp

                  pressz1 = (p1z1 + p2z1 + p3z1) / 3.0_dp
                  pressz2 = (p1z2 + p2z2 + p3z2) / 3.0_dp
                  pressz3 = (p1z3 + p2z3 + p3z3) / 3.0_dp

                cp    = 2.0_dp*(press/p0-1.0_dp)/(gamma*xmach*xmach)
                  cpx1 = 2.0_dp*(pressx1/p0) / (gamma*xmach*xmach)
                  cpx2 = 2.0_dp*(pressx2/p0) / (gamma*xmach*xmach)
                  cpx3 = 2.0_dp*(pressx3/p0) / (gamma*xmach*xmach)

                  cpy1 = 2.0_dp*(pressy1/p0) / (gamma*xmach*xmach)
                  cpy2 = 2.0_dp*(pressy2/p0) / (gamma*xmach*xmach)
                  cpy3 = 2.0_dp*(pressy3/p0) / (gamma*xmach*xmach)

                  cpz1 = 2.0_dp*(pressz1/p0) / (gamma*xmach*xmach)
                  cpz2 = 2.0_dp*(pressz2/p0) / (gamma*xmach*xmach)
                  cpz3 = 2.0_dp*(pressz3/p0) / (gamma*xmach*xmach)

                u = (qnode(2,node1) + qnode(2,node2) + qnode(2,node3)) / my_3
                v = (qnode(3,node1) + qnode(3,node2) + qnode(3,node3)) / my_3
                w = (qnode(4,node1) + qnode(4,node2) + qnode(4,node3)) / my_3

                dcx = cp*xnorm
                  dcxx1 = cp*xnormx1 + xnorm*cpx1
                  dcxx2 = cp*xnormx2 + xnorm*cpx2
                  dcxx3 = cp*xnormx3 + xnorm*cpx3

                  dcxy1 = cp*xnormy1 + xnorm*cpy1
                  dcxy2 = cp*xnormy2 + xnorm*cpy2
                  dcxy3 = cp*xnormy3 + xnorm*cpy3

                  dcxz1 = cp*xnormz1 + xnorm*cpz1
                  dcxz2 = cp*xnormz2 + xnorm*cpz2
                  dcxz3 = cp*xnormz3 + xnorm*cpz3

                dcy = cp*ynorm
                  dcyx1 = cp*ynormx1 + ynorm*cpx1
                  dcyx2 = cp*ynormx2 + ynorm*cpx2
                  dcyx3 = cp*ynormx3 + ynorm*cpx3

                  dcyy1 = cp*ynormy1 + ynorm*cpy1
                  dcyy2 = cp*ynormy2 + ynorm*cpy2
                  dcyy3 = cp*ynormy3 + ynorm*cpy3

                  dcyz1 = cp*ynormz1 + ynorm*cpz1
                  dcyz2 = cp*ynormz2 + ynorm*cpz2
                  dcyz3 = cp*ynormz3 + ynorm*cpz3

                dcz = cp*znorm
                  dczx1 = cp*znormx1 + znorm*cpx1
                  dczx2 = cp*znormx2 + znorm*cpx2
                  dczx3 = cp*znormx3 + znorm*cpx3

                  dczy1 = cp*znormy1 + znorm*cpy1
                  dczy2 = cp*znormy2 + znorm*cpy2
                  dczy3 = cp*znormy3 + znorm*cpy3

                  dczz1 = cp*znormz1 + znorm*cpz1
                  dczz2 = cp*znormz2 + znorm*cpz2
                  dczz3 = cp*znormz3 + znorm*cpz3

!               rpowerx = - dcx*u
                  powerxx1 = -dcxx1*u
                  powerxx2 = -dcxx2*u
                  powerxx3 = -dcxx3*u

                  powerxy1 = -dcxy1*u
                  powerxy2 = -dcxy2*u
                  powerxy3 = -dcxy3*u

                  powerxz1 = -dcxz1*u
                  powerxz2 = -dcxz2*u
                  powerxz3 = -dcxz3*u

!               rpowery = - dcy*v
                  poweryx1 = - dcyx1*v
                  poweryx2 = - dcyx2*v
                  poweryx3 = - dcyx3*v

                  poweryy1 = - dcyy1*v
                  poweryy2 = - dcyy2*v
                  poweryy3 = - dcyy3*v

                  poweryz1 = - dcyz1*v
                  poweryz2 = - dcyz2*v
                  poweryz3 = - dcyz3*v

!               rpowerz = - dcz*w
                  powerzx1 = - dczx1*w
                  powerzx2 = - dczx2*w
                  powerzx3 = - dczx3*w

                  powerzy1 = - dczy1*w
                  powerzy2 = - dczy2*w
                  powerzy3 = - dczy3*w

                  powerzz1 = - dczz1*w
                  powerzz2 = - dczz2*w
                  powerzz3 = - dczz3*w

                xmid = (x1 + x2 + x3)/3.0_dp
                  xmidx1 = 1.0_dp / 3.0_dp
                  xmidx2 = 1.0_dp / 3.0_dp
                  xmidx3 = 1.0_dp / 3.0_dp

                ymid = (y1 + y2 + y3)/3.0_dp
                  ymidy1 = 1.0_dp / 3.0_dp
                  ymidy2 = 1.0_dp / 3.0_dp
                  ymidy3 = 1.0_dp / 3.0_dp

                zmid = (z1 + z2 + z3)/3.0_dp
                  zmidz1 = 1.0_dp / 3.0_dp
                  zmidz2 = 1.0_dp / 3.0_dp
                  zmidz3 = 1.0_dp / 3.0_dp

!  Insert some AOA and yaw derivatives we'll need

                  snax1 = 0.0_dp
                  snax2 = 0.0_dp
                  snax3 = 0.0_dp

                  snay1 = 0.0_dp
                  snay2 = 0.0_dp
                  snay3 = 0.0_dp

                  snaz1 = 0.0_dp
                  snaz2 = 0.0_dp
                  snaz3 = 0.0_dp

                  snyx1 = 0.0_dp
                  snyx2 = 0.0_dp
                  snyx3 = 0.0_dp

                  snyy1 = 0.0_dp
                  snyy2 = 0.0_dp
                  snyy3 = 0.0_dp

                  snyz1 = 0.0_dp
                  snyz2 = 0.0_dp
                  snyz3 = 0.0_dp

                  csax1 = 0.0_dp
                  csax2 = 0.0_dp
                  csax3 = 0.0_dp

                  csay1 = 0.0_dp
                  csay2 = 0.0_dp
                  csay3 = 0.0_dp

                  csaz1 = 0.0_dp
                  csaz2 = 0.0_dp
                  csaz3 = 0.0_dp

                  csyx1 = 0.0_dp
                  csyx2 = 0.0_dp
                  csyx3 = 0.0_dp

                  csyy1 = 0.0_dp
                  csyy2 = 0.0_dp
                  csyy3 = 0.0_dp

                  csyz1 = 0.0_dp
                  csyz2 = 0.0_dp
                  csyz3 = 0.0_dp

!               clw(ntt) = clw(ntt) - dcx*sna     + dcz*csa
!               cdw(ntt) = cdw(ntt) + dcx*csa*csy - dcy*sny
!                                   + dcz*sna*csy

                clwx1 = -dcx*snax1 - sna*dcxx1 + dcz*csax1 + csa*dczx1
                clwx2 = -dcx*snax2 - sna*dcxx2 + dcz*csax2 + csa*dczx2
                clwx3 = -dcx*snax3 - sna*dcxx3 + dcz*csax3 + csa*dczx3

                clwy1 = -dcx*snay1 - sna*dcxy1 + dcz*csay1 + csa*dczy1
                clwy2 = -dcx*snay2 - sna*dcxy2 + dcz*csay2 + csa*dczy2
                clwy3 = -dcx*snay3 - sna*dcxy3 + dcz*csay3 + csa*dczy3

                clwz1 = -dcx*snaz1 - sna*dcxz1 + dcz*csaz1 + csa*dczz1
                clwz2 = -dcx*snaz2 - sna*dcxz2 + dcz*csaz2 + csa*dczz2
                clwz3 = -dcx*snaz3 - sna*dcxz3 + dcz*csaz3 + csa*dczz3

                cdwx1 = dcx*(csa*csyx1 + csy*csax1) + csa*csy*dcxx1            &
                      - dcy*snyx1 - sny*dcyx1                                  &
                      + dcz*(sna*csyx1 + csy*snax1) + sna*csy*dczx1
                cdwx2 = dcx*(csa*csyx2 + csy*csax2) + csa*csy*dcxx2            &
                      - dcy*snyx2 - sny*dcyx2                                  &
                      + dcz*(sna*csyx2 + csy*snax2) + sna*csy*dczx2
                cdwx3 = dcx*(csa*csyx3 + csy*csax3) + csa*csy*dcxx3            &
                      - dcy*snyx3 - sny*dcyx3                                  &
                      + dcz*(sna*csyx3 + csy*snax3) + sna*csy*dczx3

                cdwy1 = dcx*(csa*csyy1 + csy*csay1) + csa*csy*dcxy1            &
                      - dcy*snyy1 - sny*dcyy1                                  &
                      + dcz*(sna*csyy1 + csy*snay1) + sna*csy*dczy1
                cdwy2 = dcx*(csa*csyy2 + csy*csay2) + csa*csy*dcxy2            &
                      - dcy*snyy2 - sny*dcyy2                                  &
                      + dcz*(sna*csyy2 + csy*snay2) + sna*csy*dczy2
                cdwy3 = dcx*(csa*csyy3 + csy*csay3) + csa*csy*dcxy3            &
                      - dcy*snyy3 - sny*dcyy3                                  &
                      + dcz*(sna*csyy3 + csy*snay3) + sna*csy*dczy3

                cdwz1 = dcx*(csa*csyz1 + csy*csaz1) + csa*csy*dcxz1            &
                      - dcy*snyz1 - sny*dcyz1                                  &
                      + dcz*(sna*csyz1 + csy*snaz1) + sna*csy*dczz1
                cdwz2 = dcx*(csa*csyz2 + csy*csaz2) + csa*csy*dcxz2            &
                      - dcy*snyz2 - sny*dcyz2                                  &
                      + dcz*(sna*csyz2 + csy*snaz2) + sna*csy*dczz2
                cdwz3 = dcx*(csa*csyz3 + csy*csaz3) + csa*csy*dcxz3            &
                      - dcy*snyz3 - sny*dcyz3                                  &
                      + dcz*(sna*csyz3 + csy*snaz3) + sna*csy*dczz3

!       force%cxp  = force%cxp + dcx
!       force%cyp  = force%cyp + dcy
!       force%czp  = force%czp + dcz

                cxx1 = dcxx1
                cxx2 = dcxx2
                cxx3 = dcxx3
                cxy1 = dcxy1
                cxy2 = dcxy2
                cxy3 = dcxy3
                cxz1 = dcxz1
                cxz2 = dcxz2
                cxz3 = dcxz3

                cyx1 = dcyx1
                cyx2 = dcyx2
                cyx3 = dcyx3
                cyy1 = dcyy1
                cyy2 = dcyy2
                cyy3 = dcyy3
                cyz1 = dcyz1
                cyz2 = dcyz2
                cyz3 = dcyz3

                czx1 = dczx1
                czx2 = dczx2
                czx3 = dczx3
                czy1 = dczy1
                czy2 = dczy2
                czy3 = dczy3
                czz1 = dczz1
                czz2 = dczz2
                czz3 = dczz3

!       force%cmxp = force%cmxp + dcz*(ymid-ymc) - dcy*(zmid-zmc)
!       force%cmyp = force%cmyp - dcz*(xmid-xmc) + dcx*(zmid-zmc)
!       force%cmzp = force%cmzp + dcy*(xmid-xmc) - dcx*(ymid-ymc)

                cmxx1 = dczx1*(ymid-ymc) - dcyx1*(zmid-zmc)
                cmxx2 = dczx2*(ymid-ymc) - dcyx2*(zmid-zmc)
                cmxx3 = dczx3*(ymid-ymc) - dcyx3*(zmid-zmc)
                cmxy1 = dcz*ymidy1 + dczy1*(ymid-ymc) - dcyy1*(zmid-zmc)
                cmxy2 = dcz*ymidy2 + dczy2*(ymid-ymc) - dcyy2*(zmid-zmc)
                cmxy3 = dcz*ymidy3 + dczy3*(ymid-ymc) - dcyy3*(zmid-zmc)
                cmxz1 = dczz1*(ymid-ymc) - dcy*zmidz1 - dcyz1*(zmid-zmc)
                cmxz2 = dczz2*(ymid-ymc) - dcy*zmidz2 - dcyz2*(zmid-zmc)
                cmxz3 = dczz3*(ymid-ymc) - dcy*zmidz3 - dcyz3*(zmid-zmc)

                cmyx1 = -dcz*xmidx1 - dczx1*(xmid-xmc) + dcxx1*(zmid-zmc)
                cmyx2 = -dcz*xmidx2 - dczx2*(xmid-xmc) + dcxx2*(zmid-zmc)
                cmyx3 = -dcz*xmidx3 - dczx3*(xmid-xmc) + dcxx3*(zmid-zmc)
                cmyy1 = -dczy1*(xmid-xmc) + dcxy1*(zmid-zmc)
                cmyy2 = -dczy2*(xmid-xmc) + dcxy2*(zmid-zmc)
                cmyy3 = -dczy3*(xmid-xmc) + dcxy3*(zmid-zmc)
                cmyz1 = -dczz1*(xmid-xmc) + dcx*zmidz1 + dcxz1*(zmid-zmc)
                cmyz2 = -dczz2*(xmid-xmc) + dcx*zmidz2 + dcxz2*(zmid-zmc)
                cmyz3 = -dczz3*(xmid-xmc) + dcx*zmidz3 + dcxz3*(zmid-zmc)

                cmzx1 = dcy*xmidx1 + dcyx1*(xmid-xmc) - dcxx1*(ymid-ymc)
                cmzx2 = dcy*xmidx2 + dcyx2*(xmid-xmc) - dcxx2*(ymid-ymc)
                cmzx3 = dcy*xmidx3 + dcyx3*(xmid-xmc) - dcxx3*(ymid-ymc)
                cmzy1 = dcyy1*(xmid-xmc) - dcx*ymidy1 - dcxy1*(ymid-ymc)
                cmzy2 = dcyy2*(xmid-xmc) - dcx*ymidy2 - dcxy2*(ymid-ymc)
                cmzy3 = dcyy3*(xmid-xmc) - dcx*ymidy3 - dcxy3*(ymid-ymc)
                cmzz1 = dcyz1*(xmid-xmc) - dcxz1*(ymid-ymc)
                cmzz2 = dcyz2*(xmid-xmc) - dcxz2*(ymid-ymc)
                cmzz3 = dcyz3*(xmid-xmc) - dcxz3*(ymid-ymc)

                component_loop : do k = 1, design%function_data(j)%ncomponents

                  if ( design%function_data(j)%component_data(k)%boundary_id &
                       == 0 ) then
                    if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
                    cd   = force%cd
                    cdp  = force%cdp
                    cl   = force%cl
                    clp  = force%clp
                    cx   = force%cx
                    cy   = force%cy
                    cz   = force%cz
                    cxp  = force%cxp
                    cyp  = force%cyp
                    czp  = force%czp
                    cmx  = force%cmx
                    cmxp = force%cmxp
                    cmy  = force%cmy
                    cmyp = force%cmyp
                    cmz  = force%cmz
                    cmzp = force%cmzp
                    clcd = force%clcd
                    fom  = force%fom
                    propeff= force%propeff
                    rotor_thrust = force%rotor_thrust
                    rpowerx = force%powerx
                    rpowery = force%powery
                    rpowerz = force%powerz
                  else if (                                               &
                    design%function_data(j)%component_data(k)%boundary_id &
                    == ib ) then
                    cd   = bcforce(ib)%cd
                    cdp  = bcforce(ib)%cdp
                    cl   = bcforce(ib)%cl
                    clp  = bcforce(ib)%clp
                    cx   = bcforce(ib)%cx
                    cy   = bcforce(ib)%cy
                    cz   = bcforce(ib)%cz
                    cxp  = bcforce(ib)%cxp
                    cyp  = bcforce(ib)%cyp
                    czp  = bcforce(ib)%czp
                    cmx  = bcforce(ib)%cmx
                    cmxp = bcforce(ib)%cmxp
                    cmy  = bcforce(ib)%cmy
                    cmyp = bcforce(ib)%cmyp
                    cmz  = bcforce(ib)%cmz
                    cmzp = bcforce(ib)%cmzp
                    rpowerx = bcforce(ib)%powerx
                    rpowery = bcforce(ib)%powery
                    rpowerz = bcforce(ib)%powerz
                  else
                    cycle component_loop
                  endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(j)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(j)%averaging) base = clp-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(j)%averaging) base = cmxp-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyp_id )
                if (.not.design%function_data(j)%averaging) base = cmyp-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzp_id )
                if (.not.design%function_data(j)%averaging) base = cmzp-target
                const = my_1/sref/bref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(j)%averaging) base = cxp-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(j)%averaging) base = cyp-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(j)%averaging) base = czp-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging) base =rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging) base =rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging) base =rpowerz-target
                const = my_1/sref
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                  ioff1 = 0; ioff2 = 0; ioff3 = 0

                  clwx1_final = clwx1 * factor
                  clwy1_final = clwy1 * factor
                  clwz1_final = clwz1 * factor
                  clwx2_final = clwx2 * factor
                  clwy2_final = clwy2 * factor
                  clwz2_final = clwz2 * factor
                  clwx3_final = clwx3 * factor
                  clwy3_final = clwy3 * factor
                  clwz3_final = clwz3 * factor

                  cdwx1_final = cdwx1 * factor
                  cdwy1_final = cdwy1 * factor
                  cdwz1_final = cdwz1 * factor
                  cdwx2_final = cdwx2 * factor
                  cdwy2_final = cdwy2 * factor
                  cdwz2_final = cdwz2 * factor
                  cdwx3_final = cdwx3 * factor
                  cdwy3_final = cdwy3 * factor
                  cdwz3_final = cdwz3 * factor

                  powerxx1_final = powerxx1*factor
                  powerxx2_final = powerxx2*factor
                  powerxx3_final = powerxx3*factor

                  powerxy1_final = powerxy1*factor
                  powerxy2_final = powerxy2*factor
                  powerxy3_final = powerxy3*factor

                  powerxz1_final = powerxz1*factor
                  powerxz2_final = powerxz2*factor
                  powerxz3_final = powerxz3*factor

                  poweryx1_final = poweryx1*factor
                  poweryx2_final = poweryx2*factor
                  poweryx3_final = poweryx3*factor

                  poweryy1_final = poweryy1*factor
                  poweryy2_final = poweryy2*factor
                  poweryy3_final = poweryy3*factor

                  poweryz1_final = poweryz1*factor
                  poweryz2_final = poweryz2*factor
                  poweryz3_final = poweryz3*factor

                  powerzx1_final = powerzx1*factor
                  powerzx2_final = powerzx2*factor
                  powerzx3_final = powerzx3*factor

                  powerzy1_final = powerzy1*factor
                  powerzy2_final = powerzy2*factor
                  powerzy3_final = powerzy3*factor

                  powerzz1_final = powerzz1*factor
                  powerzz2_final = powerzz2*factor
                  powerzz3_final = powerzz3*factor

                  cxx1_final = cxx1 * factor
                  cxx2_final = cxx2 * factor
                  cxx3_final = cxx3 * factor
                  cxy1_final = cxy1 * factor
                  cxy2_final = cxy2 * factor
                  cxy3_final = cxy3 * factor
                  cxz1_final = cxz1 * factor
                  cxz2_final = cxz2 * factor
                  cxz3_final = cxz3 * factor

                  cyx1_final = cyx1 * factor
                  cyx2_final = cyx2 * factor
                  cyx3_final = cyx3 * factor
                  cyy1_final = cyy1 * factor
                  cyy2_final = cyy2 * factor
                  cyy3_final = cyy3 * factor
                  cyz1_final = cyz1 * factor
                  cyz2_final = cyz2 * factor
                  cyz3_final = cyz3 * factor

                  czx1_final = czx1 * factor
                  czx2_final = czx2 * factor
                  czx3_final = czx3 * factor
                  czy1_final = czy1 * factor
                  czy2_final = czy2 * factor
                  czy3_final = czy3 * factor
                  czz1_final = czz1 * factor
                  czz2_final = czz2 * factor
                  czz3_final = czz3 * factor

                  cmxx1_final = cmxx1 * factor
                  cmxx2_final = cmxx2 * factor
                  cmxx3_final = cmxx3 * factor
                  cmxy1_final = cmxy1 * factor
                  cmxy2_final = cmxy2 * factor
                  cmxy3_final = cmxy3 * factor
                  cmxz1_final = cmxz1 * factor
                  cmxz2_final = cmxz2 * factor
                  cmxz3_final = cmxz3 * factor

                  cmyx1_final = cmyx1 * factor
                  cmyx2_final = cmyx2 * factor
                  cmyx3_final = cmyx3 * factor
                  cmyy1_final = cmyy1 * factor
                  cmyy2_final = cmyy2 * factor
                  cmyy3_final = cmyy3 * factor
                  cmyz1_final = cmyz1 * factor
                  cmyz2_final = cmyz2 * factor
                  cmyz3_final = cmyz3 * factor

                  cmzx1_final = cmzx1 * factor
                  cmzx2_final = cmzx2 * factor
                  cmzx3_final = cmzx3 * factor
                  cmzy1_final = cmzy1 * factor
                  cmzy2_final = cmzy2 * factor
                  cmzy3_final = cmzy3 * factor
                  cmzz1_final = cmzz1 * factor
                  cmzz2_final = cmzz2 * factor
                  cmzz3_final = cmzz3 * factor

                    do i = ia(node1), ia(node1+1)-1
                      icol = ja(i)
                      if ( icol == node1 ) ioff1 = i
                    end do
                    do i = ia(node2), ia(node2+1)-1
                      icol = ja(i)
                      if ( icol == node2 ) ioff2 = i
                    end do
                    do i = ia(node3), ia(node3+1)-1
                      icol = ja(i)
                      if ( icol == node3 ) ioff3 = i
                    end do

                    select case (design%function_data(j)%component_data(k)%name)
                    case ( cl_id, clp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + clwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + clwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + clwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + clwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + clwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + clwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + clwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + clwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + clwz3_final
                    case ( cd_id, cdp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cdwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cdwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cdwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cdwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cdwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cdwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cdwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cdwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cdwz3_final
                    case ( powerx_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerxz3_final
                    case ( powery_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + poweryx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + poweryy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + poweryz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + poweryx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + poweryy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + poweryz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + poweryx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + poweryy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + poweryz3_final
                    case ( powerz_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerzz3_final
                    case ( cx_id, cxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cxz3_final
                    case ( cy_id, cyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cyz3_final
                    case ( cz_id, czp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + czx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + czy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + czz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + czx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + czy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + czz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + czx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + czy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + czz3_final
                    case ( cmx_id, cmxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmxz3_final
                    case ( cmy_id, cmyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmyz3_final
                    case ( cmz_id, cmzp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmzz3_final
                    case ( clcd_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwx1 - cl/cd/cd*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwy1 - cl/cd/cd*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwz1 - cl/cd/cd*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwx2 - cl/cd/cd*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwy2 - cl/cd/cd*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwz2 - cl/cd/cd*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwx3 - cl/cd/cd*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwy3 - cl/cd/cd*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwz3 - cl/cd/cd*cdwz3)
                    case ( fom_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz3/sref/bref)
                    case ( propeff_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) - factor               &
                                         *(my_1/cmz*czx1/sref                  &
                                      - cz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) - factor               &
                                         *(my_1/cmz*czy1/sref                  &
                                      - cz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) - factor               &
                                         *(my_1/cmz*czz1/sref                  &
                                      - cz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) - factor               &
                                         *(my_1/cmz*czx2/sref                  &
                                      - cz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) - factor               &
                                         *(my_1/cmz*czy2/sref                  &
                                      - cz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) - factor               &
                                         *(my_1/cmz*czz2/sref                  &
                                      - cz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) - factor               &
                                         *(my_1/cmz*czx3/sref                  &
                                      - cz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) - factor               &
                                         *(my_1/cmz*czy3/sref                  &
                                      - cz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) - factor               &
                                         *(my_1/cmz*czz3/sref                  &
                                      - cz/cmz/cmz*cmzz3/sref/bref)
                    case ( rotor_thrust_id )
                      sint = sin(thrust_angle)
                      cost = cos(thrust_angle)
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(cost*clwx1 - sint*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(cost*clwy1 - sint*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(cost*clwz1 - sint*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(cost*clwx2 - sint*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(cost*clwy2 - sint*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(cost*clwz2 - sint*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(cost*clwx3 - sint*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(cost*clwy3 - sint*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(cost*clwz3 - sint*cdwz3)
                    case default
                    end select

                end do component_loop

              endif

            end do tri_loop

            quad_loop : do n = 1, nbfaceq
              if(face_bitq(n)==1) then
                node1 = ibnode(f2nqb(n,1))
                node2 = ibnode(f2nqb(n,2))
                node3 = ibnode(f2nqb(n,3))
                node4 = ibnode(f2nqb(n,4))

                x1 = x(node1)
                y1 = y(node1)
                z1 = z(node1)

                x2 = x(node2)
                y2 = y(node2)
                z2 = z(node2)

                x3 = x(node3)
                y3 = y(node3)
                z3 = z(node3)

                x4 = x(node4)
                y4 = y(node4)
                z4 = z(node4)

!       quad normal computed as 1/2 the  cross product of the 2 diagonals
!       change sign to point away from interior

                xnorm = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
                  xnormx1 = 0.0_dp
                  xnormx2 = 0.0_dp
                  xnormx3 = 0.0_dp
                  xnormx4 = 0.0_dp

                  xnormy1 = -my_haf*(-(z4-z2))
                  xnormy2 = -my_haf*( (z3-z1))
                  xnormy3 = -my_haf*( (z4-z2))
                  xnormy4 = -my_haf*(-(z3-z1))

                  xnormz1 = -my_haf*( (y4-y2))
                  xnormz2 = -my_haf*(-(y3-y1))
                  xnormz3 = -my_haf*(-(y4-y2))
                  xnormz4 = -my_haf*( (y3-y1))

                ynorm = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
                  ynormx1 = -my_haf*( (z4-z2))
                  ynormx2 = -my_haf*(-(z3-z1))
                  ynormx3 = -my_haf*(-(z4-z2))
                  ynormx4 = -my_haf*( (z3-z1))

                  ynormy1 = 0.0_dp
                  ynormy2 = 0.0_dp
                  ynormy3 = 0.0_dp
                  ynormy4 = 0.0_dp

                  ynormz1 = -my_haf*(-(x4-x2))
                  ynormz2 = -my_haf*( (x3-x1))
                  ynormz3 = -my_haf*( (x4-x2))
                  ynormz4 = -my_haf*(-(x3-x1))

                znorm = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )
                  znormx1 = -my_haf*(-(y4-y2))
                  znormx2 = -my_haf*( (y3-y1))
                  znormx3 = -my_haf*( (y4-y2))
                  znormx4 = -my_haf*(-(y3-y1))

                  znormy1 = -my_haf*( (x4-x2))
                  znormy2 = -my_haf*(-(x3-x1))
                  znormy3 = -my_haf*(-(x4-x2))
                  znormy4 = -my_haf*( (x3-x1))

                  znormz1 = 0.0_dp
                  znormz2 = 0.0_dp
                  znormz3 = 0.0_dp
                  znormz4 = 0.0_dp

                p1    = qnode(5,node1)
                  p1x1 = 0.0_dp
                  p1x2 = 0.0_dp
                  p1x3 = 0.0_dp
                  p1x4 = 0.0_dp

                  p1y1 = 0.0_dp
                  p1y2 = 0.0_dp
                  p1y3 = 0.0_dp
                  p1y4 = 0.0_dp

                  p1z1 = 0.0_dp
                  p1z2 = 0.0_dp
                  p1z3 = 0.0_dp
                  p1z4 = 0.0_dp

                p2    = qnode(5,node2)
                  p2x1 = 0.0_dp
                  p2x2 = 0.0_dp
                  p2x3 = 0.0_dp
                  p2x4 = 0.0_dp

                  p2y1 = 0.0_dp
                  p2y2 = 0.0_dp
                  p2y3 = 0.0_dp
                  p2y4 = 0.0_dp

                  p2z1 = 0.0_dp
                  p2z2 = 0.0_dp
                  p2z3 = 0.0_dp
                  p2z4 = 0.0_dp

                p3    = qnode(5,node3)
                  p3x1 = 0.0_dp
                  p3x2 = 0.0_dp
                  p3x3 = 0.0_dp
                  p3x4 = 0.0_dp

                  p3y1 = 0.0_dp
                  p3y2 = 0.0_dp
                  p3y3 = 0.0_dp
                  p3y4 = 0.0_dp

                  p3z1 = 0.0_dp
                  p3z2 = 0.0_dp
                  p3z3 = 0.0_dp
                  p3z4 = 0.0_dp

                p4    = qnode(5,node4)
                  p4x1 = 0.0_dp
                  p4x2 = 0.0_dp
                  p4x3 = 0.0_dp
                  p4x4 = 0.0_dp

                  p4y1 = 0.0_dp
                  p4y2 = 0.0_dp
                  p4y3 = 0.0_dp
                  p4y4 = 0.0_dp

                  p4z1 = 0.0_dp
                  p4z2 = 0.0_dp
                  p4z3 = 0.0_dp
                  p4z4 = 0.0_dp

                press = (p1 + p2 + p3 + p4)/4.0_dp
                  pressx1 = (p1x1 + p2x1 + p3x1 + p4x1) / 4.0_dp
                  pressx2 = (p1x2 + p2x2 + p3x2 + p4x2) / 4.0_dp
                  pressx3 = (p1x3 + p2x3 + p3x3 + p4x3) / 4.0_dp
                  pressx4 = (p1x4 + p2x4 + p3x4 + p4x4) / 4.0_dp

                  pressy1 = (p1y1 + p2y1 + p3y1 + p4y1) / 4.0_dp
                  pressy2 = (p1y2 + p2y2 + p3y2 + p4y2) / 4.0_dp
                  pressy3 = (p1y3 + p2y3 + p3y3 + p4y3) / 4.0_dp
                  pressy4 = (p1y4 + p2y4 + p3y4 + p4y4) / 4.0_dp

                  pressz1 = (p1z1 + p2z1 + p3z1 + p4z1) / 4.0_dp
                  pressz2 = (p1z2 + p2z2 + p3z2 + p4z2) / 4.0_dp
                  pressz3 = (p1z3 + p2z3 + p3z3 + p4z3) / 4.0_dp
                  pressz4 = (p1z4 + p2z4 + p3z4 + p4z4) / 4.0_dp

                cp    = 2.0_dp*(press/p0-1.0_dp)/(gamma*xmach*xmach)
                  cpx1 = 2.0_dp*(pressx1/p0) / (gamma*xmach*xmach)
                  cpx2 = 2.0_dp*(pressx2/p0) / (gamma*xmach*xmach)
                  cpx3 = 2.0_dp*(pressx3/p0) / (gamma*xmach*xmach)
                  cpx4 = 2.0_dp*(pressx4/p0) / (gamma*xmach*xmach)

                  cpy1 = 2.0_dp*(pressy1/p0) / (gamma*xmach*xmach)
                  cpy2 = 2.0_dp*(pressy2/p0) / (gamma*xmach*xmach)
                  cpy3 = 2.0_dp*(pressy3/p0) / (gamma*xmach*xmach)
                  cpy4 = 2.0_dp*(pressy4/p0) / (gamma*xmach*xmach)

                  cpz1 = 2.0_dp*(pressz1/p0) / (gamma*xmach*xmach)
                  cpz2 = 2.0_dp*(pressz2/p0) / (gamma*xmach*xmach)
                  cpz3 = 2.0_dp*(pressz3/p0) / (gamma*xmach*xmach)
                  cpz4 = 2.0_dp*(pressz4/p0) / (gamma*xmach*xmach)

                u = (qnode(2,node1) + qnode(2,node2) + qnode(2,node3)          &
                   + qnode(2,node4)) / my_4
                v = (qnode(3,node1) + qnode(3,node2) + qnode(3,node3)          &
                   + qnode(3,node4)) / my_4
                w = (qnode(4,node1) + qnode(4,node2) + qnode(4,node3)          &
                   + qnode(4,node4)) / my_4

                dcx = cp*xnorm
                  dcxx1 = cp*xnormx1 + xnorm*cpx1
                  dcxx2 = cp*xnormx2 + xnorm*cpx2
                  dcxx3 = cp*xnormx3 + xnorm*cpx3
                  dcxx4 = cp*xnormx4 + xnorm*cpx4

                  dcxy1 = cp*xnormy1 + xnorm*cpy1
                  dcxy2 = cp*xnormy2 + xnorm*cpy2
                  dcxy3 = cp*xnormy3 + xnorm*cpy3
                  dcxy4 = cp*xnormy4 + xnorm*cpy4

                  dcxz1 = cp*xnormz1 + xnorm*cpz1
                  dcxz2 = cp*xnormz2 + xnorm*cpz2
                  dcxz3 = cp*xnormz3 + xnorm*cpz3
                  dcxz4 = cp*xnormz4 + xnorm*cpz4

                dcy = cp*ynorm
                  dcyx1 = cp*ynormx1 + ynorm*cpx1
                  dcyx2 = cp*ynormx2 + ynorm*cpx2
                  dcyx3 = cp*ynormx3 + ynorm*cpx3
                  dcyx4 = cp*ynormx4 + ynorm*cpx4

                  dcyy1 = cp*ynormy1 + ynorm*cpy1
                  dcyy2 = cp*ynormy2 + ynorm*cpy2
                  dcyy3 = cp*ynormy3 + ynorm*cpy3
                  dcyy4 = cp*ynormy4 + ynorm*cpy4

                  dcyz1 = cp*ynormz1 + ynorm*cpz1
                  dcyz2 = cp*ynormz2 + ynorm*cpz2
                  dcyz3 = cp*ynormz3 + ynorm*cpz3
                  dcyz4 = cp*ynormz4 + ynorm*cpz4

                dcz = cp*znorm
                  dczx1 = cp*znormx1 + znorm*cpx1
                  dczx2 = cp*znormx2 + znorm*cpx2
                  dczx3 = cp*znormx3 + znorm*cpx3
                  dczx4 = cp*znormx4 + znorm*cpx4

                  dczy1 = cp*znormy1 + znorm*cpy1
                  dczy2 = cp*znormy2 + znorm*cpy2
                  dczy3 = cp*znormy3 + znorm*cpy3
                  dczy4 = cp*znormy4 + znorm*cpy4

                  dczz1 = cp*znormz1 + znorm*cpz1
                  dczz2 = cp*znormz2 + znorm*cpz2
                  dczz3 = cp*znormz3 + znorm*cpz3
                  dczz4 = cp*znormz4 + znorm*cpz4

!               rpowerx = - dcx*u
                  powerxx1 = -dcxx1*u
                  powerxx2 = -dcxx2*u
                  powerxx3 = -dcxx3*u
                  powerxx4 = -dcxx4*u

                  powerxy1 = -dcxy1*u
                  powerxy2 = -dcxy2*u
                  powerxy3 = -dcxy3*u
                  powerxy4 = -dcxy4*u

                  powerxz1 = -dcxz1*u
                  powerxz2 = -dcxz2*u
                  powerxz3 = -dcxz3*u
                  powerxz4 = -dcxz4*u

!               rpowery = - dcy*v
                  poweryx1 = - dcyx1*v
                  poweryx2 = - dcyx2*v
                  poweryx3 = - dcyx3*v
                  poweryx4 = - dcyx4*v

                  poweryy1 = - dcyy1*v
                  poweryy2 = - dcyy2*v
                  poweryy3 = - dcyy3*v
                  poweryy4 = - dcyy4*v

                  poweryz1 = - dcyz1*v
                  poweryz2 = - dcyz2*v
                  poweryz3 = - dcyz3*v
                  poweryz4 = - dcyz4*v

!               rpowerz = - dcz*w
                  powerzx1 = - dczx1*w
                  powerzx2 = - dczx2*w
                  powerzx3 = - dczx3*w
                  powerzx4 = - dczx4*w

                  powerzy1 = - dczy1*w
                  powerzy2 = - dczy2*w
                  powerzy3 = - dczy3*w
                  powerzy4 = - dczy4*w

                  powerzz1 = - dczz1*w
                  powerzz2 = - dczz2*w
                  powerzz3 = - dczz3*w
                  powerzz4 = - dczz4*w

                xmid = (x1 + x2 + x3 + x4)/4.0_dp
                  xmidx1 = 1.0_dp / 4.0_dp
                  xmidx2 = 1.0_dp / 4.0_dp
                  xmidx3 = 1.0_dp / 4.0_dp
                  xmidx4 = 1.0_dp / 4.0_dp

                ymid = (y1 + y2 + y3 + y4)/4.0_dp
                  ymidy1 = 1.0_dp / 4.0_dp
                  ymidy2 = 1.0_dp / 4.0_dp
                  ymidy3 = 1.0_dp / 4.0_dp
                  ymidy4 = 1.0_dp / 4.0_dp

                zmid = (z1 + z2 + z3 + z4)/4.0_dp
                  zmidz1 = 1.0_dp / 4.0_dp
                  zmidz2 = 1.0_dp / 4.0_dp
                  zmidz3 = 1.0_dp / 4.0_dp
                  zmidz4 = 1.0_dp / 4.0_dp

!  Insert some AOA and yaw derivatives we'll need

                  snax1 = 0.0_dp
                  snax2 = 0.0_dp
                  snax3 = 0.0_dp
                  snax4 = 0.0_dp

                  snay1 = 0.0_dp
                  snay2 = 0.0_dp
                  snay3 = 0.0_dp
                  snay4 = 0.0_dp

                  snaz1 = 0.0_dp
                  snaz2 = 0.0_dp
                  snaz3 = 0.0_dp
                  snaz4 = 0.0_dp

                  snyx1 = 0.0_dp
                  snyx2 = 0.0_dp
                  snyx3 = 0.0_dp
                  snyx4 = 0.0_dp

                  snyy1 = 0.0_dp
                  snyy2 = 0.0_dp
                  snyy3 = 0.0_dp
                  snyy4 = 0.0_dp

                  snyz1 = 0.0_dp
                  snyz2 = 0.0_dp
                  snyz3 = 0.0_dp
                  snyz4 = 0.0_dp

                  csax1 = 0.0_dp
                  csax2 = 0.0_dp
                  csax3 = 0.0_dp
                  csax4 = 0.0_dp

                  csay1 = 0.0_dp
                  csay2 = 0.0_dp
                  csay3 = 0.0_dp
                  csay4 = 0.0_dp

                  csaz1 = 0.0_dp
                  csaz2 = 0.0_dp
                  csaz3 = 0.0_dp
                  csaz4 = 0.0_dp

                  csyx1 = 0.0_dp
                  csyx2 = 0.0_dp
                  csyx3 = 0.0_dp
                  csyx4 = 0.0_dp

                  csyy1 = 0.0_dp
                  csyy2 = 0.0_dp
                  csyy3 = 0.0_dp
                  csyy4 = 0.0_dp

                  csyz1 = 0.0_dp
                  csyz2 = 0.0_dp
                  csyz3 = 0.0_dp
                  csyz4 = 0.0_dp

!               clw(ntt) = clw(ntt) - dcx*sna     + dcz*csa
!               cdw(ntt) = cdw(ntt) + dcx*csa*csy - dcy*sny
!                                   + dcz*sna*csy

                clwx1 = -dcx*snax1 - sna*dcxx1 + dcz*csax1 + csa*dczx1
                clwx2 = -dcx*snax2 - sna*dcxx2 + dcz*csax2 + csa*dczx2
                clwx3 = -dcx*snax3 - sna*dcxx3 + dcz*csax3 + csa*dczx3
                clwx4 = -dcx*snax4 - sna*dcxx4 + dcz*csax4 + csa*dczx4

                clwy1 = -dcx*snay1 - sna*dcxy1 + dcz*csay1 + csa*dczy1
                clwy2 = -dcx*snay2 - sna*dcxy2 + dcz*csay2 + csa*dczy2
                clwy3 = -dcx*snay3 - sna*dcxy3 + dcz*csay3 + csa*dczy3
                clwy4 = -dcx*snay4 - sna*dcxy4 + dcz*csay4 + csa*dczy4

                clwz1 = -dcx*snaz1 - sna*dcxz1 + dcz*csaz1 + csa*dczz1
                clwz2 = -dcx*snaz2 - sna*dcxz2 + dcz*csaz2 + csa*dczz2
                clwz3 = -dcx*snaz3 - sna*dcxz3 + dcz*csaz3 + csa*dczz3
                clwz4 = -dcx*snaz4 - sna*dcxz4 + dcz*csaz4 + csa*dczz4

                cdwx1 = dcx*(csa*csyx1 + csy*csax1) + csa*csy*dcxx1            &
                      - dcy*snyx1 - sny*dcyx1                                  &
                      + dcz*(sna*csyx1 + csy*snax1) + sna*csy*dczx1
                cdwx2 = dcx*(csa*csyx2 + csy*csax2) + csa*csy*dcxx2            &
                      - dcy*snyx2 - sny*dcyx2                                  &
                      + dcz*(sna*csyx2 + csy*snax2) + sna*csy*dczx2
                cdwx3 = dcx*(csa*csyx3 + csy*csax3) + csa*csy*dcxx3            &
                      - dcy*snyx3 - sny*dcyx3                                  &
                      + dcz*(sna*csyx3 + csy*snax3) + sna*csy*dczx3
                cdwx4 = dcx*(csa*csyx4 + csy*csax4) + csa*csy*dcxx4            &
                      - dcy*snyx4 - sny*dcyx4                                  &
                      + dcz*(sna*csyx4 + csy*snax4) + sna*csy*dczx4

                cdwy1 = dcx*(csa*csyy1 + csy*csay1) + csa*csy*dcxy1            &
                      - dcy*snyy1 - sny*dcyy1                                  &
                      + dcz*(sna*csyy1 + csy*snay1) + sna*csy*dczy1
                cdwy2 = dcx*(csa*csyy2 + csy*csay2) + csa*csy*dcxy2            &
                      - dcy*snyy2 - sny*dcyy2                                  &
                      + dcz*(sna*csyy2 + csy*snay2) + sna*csy*dczy2
                cdwy3 = dcx*(csa*csyy3 + csy*csay3) + csa*csy*dcxy3            &
                      - dcy*snyy3 - sny*dcyy3                                  &
                      + dcz*(sna*csyy3 + csy*snay3) + sna*csy*dczy3
                cdwy4 = dcx*(csa*csyy4 + csy*csay4) + csa*csy*dcxy4            &
                      - dcy*snyy4 - sny*dcyy4                                  &
                      + dcz*(sna*csyy4 + csy*snay4) + sna*csy*dczy4

                cdwz1 = dcx*(csa*csyz1 + csy*csaz1) + csa*csy*dcxz1            &
                      - dcy*snyz1 - sny*dcyz1                                  &
                      + dcz*(sna*csyz1 + csy*snaz1) + sna*csy*dczz1
                cdwz2 = dcx*(csa*csyz2 + csy*csaz2) + csa*csy*dcxz2            &
                      - dcy*snyz2 - sny*dcyz2                                  &
                      + dcz*(sna*csyz2 + csy*snaz2) + sna*csy*dczz2
                cdwz3 = dcx*(csa*csyz3 + csy*csaz3) + csa*csy*dcxz3            &
                      - dcy*snyz3 - sny*dcyz3                                  &
                      + dcz*(sna*csyz3 + csy*snaz3) + sna*csy*dczz3
                cdwz4 = dcx*(csa*csyz4 + csy*csaz4) + csa*csy*dcxz4            &
                      - dcy*snyz4 - sny*dcyz4                                  &
                      + dcz*(sna*csyz4 + csy*snaz4) + sna*csy*dczz4

!       force%cxp  = force%cxp + dcx
!       force%cyp  = force%cyp + dcy
!       force%czp  = force%czp + dcz

                cxx1 = dcxx1
                cxx2 = dcxx2
                cxx3 = dcxx3
                cxx4 = dcxx4
                cxy1 = dcxy1
                cxy2 = dcxy2
                cxy3 = dcxy3
                cxy4 = dcxy4
                cxz1 = dcxz1
                cxz2 = dcxz2
                cxz3 = dcxz3
                cxz4 = dcxz4

                cyx1 = dcyx1
                cyx2 = dcyx2
                cyx3 = dcyx3
                cyx4 = dcyx4
                cyy1 = dcyy1
                cyy2 = dcyy2
                cyy3 = dcyy3
                cyy4 = dcyy4
                cyz1 = dcyz1
                cyz2 = dcyz2
                cyz3 = dcyz3
                cyz4 = dcyz4

                czx1 = dczx1
                czx2 = dczx2
                czx3 = dczx3
                czx4 = dczx4
                czy1 = dczy1
                czy2 = dczy2
                czy3 = dczy3
                czy4 = dczy4
                czz1 = dczz1
                czz2 = dczz2
                czz3 = dczz3
                czz4 = dczz4

!       force%cmxp = force%cmxp + dcz*(ymid-ymc) - dcy*(zmid-zmc)
!       force%cmyp = force%cmyp - dcz*(xmid-xmc) + dcx*(zmid-zmc)
!       force%cmzp = force%cmzp + dcy*(xmid-xmc) - dcx*(ymid-ymc)

                cmxx1 = dczx1*(ymid-ymc) - dcyx1*(zmid-zmc)
                cmxx2 = dczx2*(ymid-ymc) - dcyx2*(zmid-zmc)
                cmxx3 = dczx3*(ymid-ymc) - dcyx3*(zmid-zmc)
                cmxx4 = dczx4*(ymid-ymc) - dcyx4*(zmid-zmc)
                cmxy1 = dcz*ymidy1 + dczy1*(ymid-ymc) - dcyy1*(zmid-zmc)
                cmxy2 = dcz*ymidy2 + dczy2*(ymid-ymc) - dcyy2*(zmid-zmc)
                cmxy3 = dcz*ymidy3 + dczy3*(ymid-ymc) - dcyy3*(zmid-zmc)
                cmxy4 = dcz*ymidy4 + dczy4*(ymid-ymc) - dcyy4*(zmid-zmc)
                cmxz1 = dczz1*(ymid-ymc) - dcy*zmidz1 - dcyz1*(zmid-zmc)
                cmxz2 = dczz2*(ymid-ymc) - dcy*zmidz2 - dcyz2*(zmid-zmc)
                cmxz3 = dczz3*(ymid-ymc) - dcy*zmidz3 - dcyz3*(zmid-zmc)
                cmxz4 = dczz4*(ymid-ymc) - dcy*zmidz4 - dcyz4*(zmid-zmc)

                cmyx1 = -dcz*xmidx1 - dczx1*(xmid-xmc) + dcxx1*(zmid-zmc)
                cmyx2 = -dcz*xmidx2 - dczx2*(xmid-xmc) + dcxx2*(zmid-zmc)
                cmyx3 = -dcz*xmidx3 - dczx3*(xmid-xmc) + dcxx3*(zmid-zmc)
                cmyx4 = -dcz*xmidx4 - dczx4*(xmid-xmc) + dcxx4*(zmid-zmc)
                cmyy1 = -dczy1*(xmid-xmc) + dcxy1*(zmid-zmc)
                cmyy2 = -dczy2*(xmid-xmc) + dcxy2*(zmid-zmc)
                cmyy3 = -dczy3*(xmid-xmc) + dcxy3*(zmid-zmc)
                cmyy4 = -dczy4*(xmid-xmc) + dcxy4*(zmid-zmc)
                cmyz1 = -dczz1*(xmid-xmc) + dcx*zmidz1 + dcxz1*(zmid-zmc)
                cmyz2 = -dczz2*(xmid-xmc) + dcx*zmidz2 + dcxz2*(zmid-zmc)
                cmyz3 = -dczz3*(xmid-xmc) + dcx*zmidz3 + dcxz3*(zmid-zmc)
                cmyz4 = -dczz4*(xmid-xmc) + dcx*zmidz4 + dcxz4*(zmid-zmc)

                cmzx1 = dcy*xmidx1 + dcyx1*(xmid-xmc) - dcxx1*(ymid-ymc)
                cmzx2 = dcy*xmidx2 + dcyx2*(xmid-xmc) - dcxx2*(ymid-ymc)
                cmzx3 = dcy*xmidx3 + dcyx3*(xmid-xmc) - dcxx3*(ymid-ymc)
                cmzx4 = dcy*xmidx4 + dcyx4*(xmid-xmc) - dcxx4*(ymid-ymc)
                cmzy1 = dcyy1*(xmid-xmc) - dcx*ymidy1 - dcxy1*(ymid-ymc)
                cmzy2 = dcyy2*(xmid-xmc) - dcx*ymidy2 - dcxy2*(ymid-ymc)
                cmzy3 = dcyy3*(xmid-xmc) - dcx*ymidy3 - dcxy3*(ymid-ymc)
                cmzy4 = dcyy4*(xmid-xmc) - dcx*ymidy4 - dcxy4*(ymid-ymc)
                cmzz1 = dcyz1*(xmid-xmc) - dcxz1*(ymid-ymc)
                cmzz2 = dcyz2*(xmid-xmc) - dcxz2*(ymid-ymc)
                cmzz3 = dcyz3*(xmid-xmc) - dcxz3*(ymid-ymc)
                cmzz4 = dcyz4*(xmid-xmc) - dcxz4*(ymid-ymc)

                component_loop2 : do k = 1, design%function_data(j)%ncomponents

                  if ( design%function_data(j)%component_data(k)%boundary_id &
                       == 0 ) then
                    if ( .not. bcforce(ib)%add_to_total ) cycle component_loop2
                    cd   = force%cd
                    cdp  = force%cdp
                    cl   = force%cl
                    clp  = force%clp
                    cx   = force%cx
                    cy   = force%cy
                    cz   = force%cz
                    cxp  = force%cxp
                    cyp  = force%cyp
                    czp  = force%czp
                    cmx  = force%cmx
                    cmxp = force%cmxp
                    cmy  = force%cmy
                    cmyp = force%cmyp
                    cmz  = force%cmz
                    cmzp = force%cmzp
                    clcd = force%clcd
                    fom  = force%fom
                    propeff= force%propeff
                    rotor_thrust = force%rotor_thrust
                    rpowerx = force%powerx
                    rpowery = force%powery
                    rpowerz = force%powerz
                  else if (                                               &
                    design%function_data(j)%component_data(k)%boundary_id &
                    == ib ) then
                    cd   = bcforce(ib)%cd
                    cdp  = bcforce(ib)%cdp
                    cl   = bcforce(ib)%cl
                    clp  = bcforce(ib)%clp
                    cx   = bcforce(ib)%cx
                    cy   = bcforce(ib)%cy
                    cz   = bcforce(ib)%cz
                    cxp  = bcforce(ib)%cxp
                    cyp  = bcforce(ib)%cyp
                    czp  = bcforce(ib)%czp
                    cmx  = bcforce(ib)%cmx
                    cmxp = bcforce(ib)%cmxp
                    cmy  = bcforce(ib)%cmy
                    cmyp = bcforce(ib)%cmyp
                    cmz  = bcforce(ib)%cmz
                    cmzp = bcforce(ib)%cmzp
                    rpowerx = bcforce(ib)%powerx
                    rpowery = bcforce(ib)%powery
                    rpowerz = bcforce(ib)%powerz
                  else
                    cycle component_loop2
                  endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(j)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(j)%averaging) base = clp-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(j)%averaging) base = cmxp-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyp_id )
                if (.not.design%function_data(j)%averaging) base = cmyp-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzp_id )
                if (.not.design%function_data(j)%averaging) base = cmzp-target
                const = my_1/sref/bref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(j)%averaging) base = cxp-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(j)%averaging) base = cyp-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(j)%averaging) base = czp-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case default
                cycle component_loop2
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                  ioff1 = 0; ioff2 = 0; ioff3 = 0; ioff4 = 0

                  clwx1_final = clwx1 * factor
                  clwy1_final = clwy1 * factor
                  clwz1_final = clwz1 * factor
                  clwx2_final = clwx2 * factor
                  clwy2_final = clwy2 * factor
                  clwz2_final = clwz2 * factor
                  clwx3_final = clwx3 * factor
                  clwy3_final = clwy3 * factor
                  clwz3_final = clwz3 * factor
                  clwx4_final = clwx4 * factor
                  clwy4_final = clwy4 * factor
                  clwz4_final = clwz4 * factor

                  cdwx1_final = cdwx1 * factor
                  cdwy1_final = cdwy1 * factor
                  cdwz1_final = cdwz1 * factor
                  cdwx2_final = cdwx2 * factor
                  cdwy2_final = cdwy2 * factor
                  cdwz2_final = cdwz2 * factor
                  cdwx3_final = cdwx3 * factor
                  cdwy3_final = cdwy3 * factor
                  cdwz3_final = cdwz3 * factor
                  cdwx4_final = cdwx4 * factor
                  cdwy4_final = cdwy4 * factor
                  cdwz4_final = cdwz4 * factor

                  powerxx1_final = powerxx1*factor
                  powerxx2_final = powerxx2*factor
                  powerxx3_final = powerxx3*factor
                  powerxx4_final = powerxx4*factor

                  powerxy1_final = powerxy1*factor
                  powerxy2_final = powerxy2*factor
                  powerxy3_final = powerxy3*factor
                  powerxy4_final = powerxy4*factor

                  powerxz1_final = powerxz1*factor
                  powerxz2_final = powerxz2*factor
                  powerxz3_final = powerxz3*factor
                  powerxz4_final = powerxz4*factor

                  poweryx1_final = poweryx1*factor
                  poweryx2_final = poweryx2*factor
                  poweryx3_final = poweryx3*factor
                  poweryx4_final = poweryx4*factor

                  poweryy1_final = poweryy1*factor
                  poweryy2_final = poweryy2*factor
                  poweryy3_final = poweryy3*factor
                  poweryy4_final = poweryy4*factor

                  poweryz1_final = poweryz1*factor
                  poweryz2_final = poweryz2*factor
                  poweryz3_final = poweryz3*factor
                  poweryz4_final = poweryz4*factor

                  powerzx1_final = powerzx1*factor
                  powerzx2_final = powerzx2*factor
                  powerzx3_final = powerzx3*factor
                  powerzx4_final = powerzx4*factor

                  powerzy1_final = powerzy1*factor
                  powerzy2_final = powerzy2*factor
                  powerzy3_final = powerzy3*factor
                  powerzy4_final = powerzy4*factor

                  powerzz1_final = powerzz1*factor
                  powerzz2_final = powerzz2*factor
                  powerzz3_final = powerzz3*factor
                  powerzz4_final = powerzz4*factor

                  cxx1_final = cxx1 * factor
                  cxx2_final = cxx2 * factor
                  cxx3_final = cxx3 * factor
                  cxx4_final = cxx4 * factor
                  cxy1_final = cxy1 * factor
                  cxy2_final = cxy2 * factor
                  cxy3_final = cxy3 * factor
                  cxy4_final = cxy4 * factor
                  cxz1_final = cxz1 * factor
                  cxz2_final = cxz2 * factor
                  cxz3_final = cxz3 * factor
                  cxz4_final = cxz4 * factor

                  cyx1_final = cyx1 * factor
                  cyx2_final = cyx2 * factor
                  cyx3_final = cyx3 * factor
                  cyx4_final = cyx4 * factor
                  cyy1_final = cyy1 * factor
                  cyy2_final = cyy2 * factor
                  cyy3_final = cyy3 * factor
                  cyy4_final = cyy4 * factor
                  cyz1_final = cyz1 * factor
                  cyz2_final = cyz2 * factor
                  cyz3_final = cyz3 * factor
                  cyz4_final = cyz4 * factor

                  czx1_final = czx1 * factor
                  czx2_final = czx2 * factor
                  czx3_final = czx3 * factor
                  czx4_final = czx4 * factor
                  czy1_final = czy1 * factor
                  czy2_final = czy2 * factor
                  czy3_final = czy3 * factor
                  czy4_final = czy4 * factor
                  czz1_final = czz1 * factor
                  czz2_final = czz2 * factor
                  czz3_final = czz3 * factor
                  czz4_final = czz4 * factor

                  cmxx1_final = cmxx1 * factor
                  cmxx2_final = cmxx2 * factor
                  cmxx3_final = cmxx3 * factor
                  cmxx4_final = cmxx4 * factor
                  cmxy1_final = cmxy1 * factor
                  cmxy2_final = cmxy2 * factor
                  cmxy3_final = cmxy3 * factor
                  cmxy4_final = cmxy4 * factor
                  cmxz1_final = cmxz1 * factor
                  cmxz2_final = cmxz2 * factor
                  cmxz3_final = cmxz3 * factor
                  cmxz4_final = cmxz4 * factor

                  cmyx1_final = cmyx1 * factor
                  cmyx2_final = cmyx2 * factor
                  cmyx3_final = cmyx3 * factor
                  cmyx4_final = cmyx4 * factor
                  cmyy1_final = cmyy1 * factor
                  cmyy2_final = cmyy2 * factor
                  cmyy3_final = cmyy3 * factor
                  cmyy4_final = cmyy4 * factor
                  cmyz1_final = cmyz1 * factor
                  cmyz2_final = cmyz2 * factor
                  cmyz3_final = cmyz3 * factor
                  cmyz4_final = cmyz4 * factor

                  cmzx1_final = cmzx1 * factor
                  cmzx2_final = cmzx2 * factor
                  cmzx3_final = cmzx3 * factor
                  cmzx4_final = cmzx4 * factor
                  cmzy1_final = cmzy1 * factor
                  cmzy2_final = cmzy2 * factor
                  cmzy3_final = cmzy3 * factor
                  cmzy4_final = cmzy4 * factor
                  cmzz1_final = cmzz1 * factor
                  cmzz2_final = cmzz2 * factor
                  cmzz3_final = cmzz3 * factor
                  cmzz4_final = cmzz4 * factor

                    do i = ia(node1), ia(node1+1)-1
                      icol = ja(i)
                      if ( icol == node1 ) ioff1 = i
                    end do
                    do i = ia(node2), ia(node2+1)-1
                      icol = ja(i)
                      if ( icol == node2 ) ioff2 = i
                    end do
                    do i = ia(node3), ia(node3+1)-1
                      icol = ja(i)
                      if ( icol == node3 ) ioff3 = i
                    end do
                    do i = ia(node4), ia(node4+1)-1
                      icol = ja(i)
                      if ( icol == node4 ) ioff4 = i
                    end do

                    select case (design%function_data(j)%component_data(k)%name)
                    case ( cl_id, clp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + clwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + clwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + clwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + clwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + clwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + clwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + clwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + clwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + clwz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + clwx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + clwy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + clwz4_final
                    case ( cd_id, cdp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cdwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cdwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cdwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cdwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cdwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cdwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cdwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cdwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cdwz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cdwx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cdwy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cdwz4_final
                    case ( powerx_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerxz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + powerxx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + powerxy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + powerxz4_final
                    case ( powery_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + poweryx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + poweryy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + poweryz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + poweryx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + poweryy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + poweryz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + poweryx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + poweryy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + poweryz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + poweryx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + poweryy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + poweryz4_final
                    case ( powerz_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerzz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + powerzx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + powerzy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + powerzz4_final
                    case ( cx_id, cxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cxz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cxx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cxy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cxz4_final
                    case ( cy_id, cyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cyz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cyx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cyy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cyz4_final
                    case ( cz_id, czp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + czx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + czy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + czz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + czx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + czy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + czz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + czx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + czy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + czz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + czx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + czy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + czz4_final
                    case ( cmx_id, cmxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmxz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cmxx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cmxy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cmxz4_final
                    case ( cmy_id, cmyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmyz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cmyx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cmyy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cmyz4_final
                    case ( cmz_id, cmzp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmzz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cmzx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cmzy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cmzz4_final
                    case ( clcd_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwx1 - cl/cd/cd*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwy1 - cl/cd/cd*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwz1 - cl/cd/cd*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwx2 - cl/cd/cd*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwy2 - cl/cd/cd*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwz2 - cl/cd/cd*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwx3 - cl/cd/cd*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwy3 - cl/cd/cd*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwz3 - cl/cd/cd*cdwz3)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j)                        &
                        + factor*(1.0_dp/cd*clwx4 - cl/cd/cd*cdwx4)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j)                        &
                        + factor*(1.0_dp/cd*clwy4 - cl/cd/cd*cdwy4)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j)                        &
                        + factor*(1.0_dp/cd*clwz4 - cl/cd/cd*cdwz4)
                    case ( fom_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz3/sref/bref)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx4/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx4/sref/bref)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy4/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy4/sref/bref)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz4/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz4/sref/bref)
                    case ( propeff_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) - factor               &
                                         *(my_1/cmz*czx1/sref                  &
                                      - cz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) - factor               &
                                         *(my_1/cmz*czy1/sref                  &
                                      - cz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) - factor               &
                                         *(my_1/cmz*czz1/sref                  &
                                      - cz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) - factor               &
                                         *(my_1/cmz*czx2/sref                  &
                                      - cz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) - factor               &
                                         *(my_1/cmz*czy2/sref                  &
                                      - cz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) - factor               &
                                         *(my_1/cmz*czz2/sref                  &
                                      - cz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) - factor               &
                                         *(my_1/cmz*czx3/sref                  &
                                      - cz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) - factor               &
                                         *(my_1/cmz*czy3/sref                  &
                                      - cz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) - factor               &
                                         *(my_1/cmz*czz3/sref                  &
                                      - cz/cmz/cmz*cmzz3/sref/bref)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) - factor               &
                                         *(my_1/cmz*czx4/sref                  &
                                      - cz/cmz/cmz*cmzx4/sref/bref)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) - factor               &
                                         *(my_1/cmz*czy4/sref                  &
                                      - cz/cmz/cmz*cmzy4/sref/bref)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) - factor               &
                                         *(my_1/cmz*czz4/sref                  &
                                      - cz/cmz/cmz*cmzz4/sref/bref)
                    case ( rotor_thrust_id )
                      sint = sin(thrust_angle)
                      cost = cos(thrust_angle)
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(cost*clwx1 - sint*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(cost*clwy1 - sint*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(cost*clwz1 - sint*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(cost*clwx2 - sint*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(cost*clwy2 - sint*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(cost*clwz2 - sint*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(cost*clwx3 - sint*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(cost*clwy3 - sint*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(cost*clwz3 - sint*cdwz3)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j)                        &
                        + factor*(cost*clwx4 - sint*cdwx4)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j)                        &
                        + factor*(cost*clwy4 - sint*cdwy4)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j)                        &
                        + factor*(cost*clwz4 - sint*cdwz4)
                    case default
                    end select

                end do component_loop2

              endif

            end do quad_loop

      end do fcn_loop

  end subroutine pressurexyz

!============================== PRESSUREXYZI =================================80
!
!  This routine computes the derivatives of the pressure forces wrt shape
!
!  Incompressible version
!
!=============================================================================80

  subroutine pressurexyzi(nnodes01, x, y, z, qnode, nbnode, nbfacet, face_bit, &
                          ibnode, f2ntb, design,force,ia,ja,dfdx,              &
                          small_stencil_nnz01, ib,nbound,bcforce, ndim,        &
                          nbfaceq, face_bitq, f2nqb)

    use kinddefs,          only : dp
    use info_depr,         only : alpha,yaw
    use design_types,      only : design_type
    use force_types,       only : force_type
    use forces,            only : cl_id, cd_id, clp_id, cdp_id, cx_id, cy_id,  &
                                  cz_id, cxp_id, cyp_id, czp_id, powerx_id,    &
                                  powery_id, powerz_id, cmx_id, cmy_id, cmz_id,&
                                  cmxp_id, cmyp_id, cmzp_id, clcd_id, fom_id,  &
                                  propeff_id, rotor_thrust_id
    use refgeom,           only : sref, xmc, ymc, zmc, bref, cref
    use custom_transforms, only : thrust_angle

    integer,                                intent(in)    :: nnodes01, nbnode
    integer,                                intent(in)    :: nbfacet, ib, ndim
    integer,                                intent(in)    :: nbfaceq
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,                                intent(in)    :: nbound
    integer,     dimension(nbfacet),        intent(in)    :: face_bit
    integer,     dimension(nbfaceq),        intent(in)    :: face_bitq
    integer,     dimension(nbnode),         intent(in)    :: ibnode
    integer,     dimension(nbfacet,5),      intent(in)    :: f2ntb
    integer,     dimension(nbfaceq,6),      intent(in)    :: f2nqb
    integer,     dimension(:),              intent(in)    :: ia,ja
    real(dp), dimension(nnodes01),          intent(in)    :: x,y,z
    real(dp), dimension(ndim,nnodes01),     intent(in)    :: qnode
    type(design_type),                      intent(in)    :: design
    type(force_type),                       intent(in)    :: force
    type(force_type), dimension(nbound),    intent(in)    :: bcforce
    real(dp), dimension(3,small_stencil_nnz01,design%nfunctions),    &
                                            intent(inout) :: dfdx

    integer :: n, node1, node2, node3, node4, ioff4
    integer :: i, j, k, ioff1, ioff2, ioff3, icol

    real(dp) :: pi,conv,csa,sna,csy,sny,x1,y1,z1,x2,y2,z2,x3,y3,z3,const
    real(dp) :: ax,x4,y4,z4
    real(dp) :: axx1,axx2,axx3,axy1,axy2,axy3,axz1,axz2,axz3
    real(dp) :: ay
    real(dp) :: ayx1,ayx2,ayx3,ayy1,ayy2,ayy3,ayz1,ayz2,ayz3
    real(dp) :: az
    real(dp) :: azx1,azx2,azx3,azy1,azy2,azy3,azz1,azz2,azz3
    real(dp) :: bx
    real(dp) :: bxx1,bxx2,bxx3,bxy1,bxy2,bxy3,bxz1,bxz2,bxz3
    real(dp) :: by
    real(dp) :: byx1,byx2,byx3,byy1,byy2,byy3,byz1,byz2,byz3
    real(dp) :: bz
    real(dp) :: bzx1,bzx2,bzx3,bzy1,bzy2,bzy3,bzz1,bzz2,bzz3
    real(dp) :: xnormx1,xnormx2,xnormx3,xnormx4
    real(dp) :: xnormy1,xnormy2,xnormy3,xnormy4
    real(dp) :: xnormz1,xnormz2,xnormz3,xnormz4
    real(dp) :: ynormx1,ynormx2,ynormx3,ynormx4
    real(dp) :: ynormy1,ynormy2,ynormy3,ynormy4
    real(dp) :: ynormz1,ynormz2,ynormz3,ynormz4
    real(dp) :: znormx1,znormx2,znormx3,znormx4
    real(dp) :: znormy1,znormy2,znormy3,znormy4
    real(dp) :: znormz1,znormz2,znormz3,znormz4
    real(dp) :: p1,p2,p3,p4
    real(dp) :: press
    real(dp) :: cp,u,v,w
    real(dp) :: dcxx1,dcxx2,dcxx3,dcxx4
    real(dp) :: dcxy1,dcxy2,dcxy3,dcxy4
    real(dp) :: dcxz1,dcxz2,dcxz3,dcxz4
    real(dp) :: dcyx1,dcyx2,dcyx3,dcyx4
    real(dp) :: dcyy1,dcyy2,dcyy3,dcyy4
    real(dp) :: dcyz1,dcyz2,dcyz3,dcyz4
    real(dp) :: dczx1,dczx2,dczx3,dczx4
    real(dp) :: dczy1,dczy2,dczy3,dczy4
    real(dp) :: dczz1,dczz2,dczz3,dczz4
    real(dp) :: clwx1,clwx2,clwx3,clwx4
    real(dp) :: clwy1,clwy2,clwy3,clwy4
    real(dp) :: clwz1,clwz2,clwz3,clwz4
    real(dp) :: cdwx1,cdwx2,cdwx3,cdwx4
    real(dp) :: cdwy1,cdwy2,cdwy3,cdwy4
    real(dp) :: cdwz1,cdwz2,cdwz3,cdwz4
    real(dp) :: cd,cdp,cl,clp,weight,target,power
    real(dp) :: clwx1_final,clwx2_final,clwx3_final,clwx4_final
    real(dp) :: clwy1_final,clwy2_final,clwy3_final,clwy4_final
    real(dp) :: clwz1_final,clwz2_final,clwz3_final,clwz4_final
    real(dp) :: cdwx1_final,cdwx2_final,cdwx3_final,cdwx4_final
    real(dp) :: cdwy1_final,cdwy2_final,cdwy3_final,cdwy4_final
    real(dp) :: cdwz1_final,cdwz2_final,cdwz3_final,cdwz4_final
    real(dp) :: cx,cy,cz,cxp,cyp,czp
    real(dp) :: cxx1,cxx2,cxx3,cxx4
    real(dp) :: cxy1,cxy2,cxy3,cxy4
    real(dp) :: cxz1,cxz2,cxz3,cxz4
    real(dp) :: cyx1,cyx2,cyx3,cyx4
    real(dp) :: cyy1,cyy2,cyy3,cyy4
    real(dp) :: cyz1,cyz2,cyz3,cyz4
    real(dp) :: czx1,czx2,czx3,czx4
    real(dp) :: czy1,czy2,czy3,czy4
    real(dp) :: czz1,czz2,czz3,czz4
    real(dp) :: cxx1_final,cxx2_final,cxx3_final,cxx4_final
    real(dp) :: cxy1_final,cxy2_final,cxy3_final,cxy4_final
    real(dp) :: cxz1_final,cxz2_final,cxz3_final,cxz4_final
    real(dp) :: cyx1_final,cyx2_final,cyx3_final,cyx4_final
    real(dp) :: cyy1_final,cyy2_final,cyy3_final,cyy4_final
    real(dp) :: cyz1_final,cyz2_final,cyz3_final,cyz4_final
    real(dp) :: czx1_final,czx2_final,czx3_final,czx4_final
    real(dp) :: czy1_final,czy2_final,czy3_final,czy4_final
    real(dp) :: czz1_final,czz2_final,czz3_final,czz4_final
    real(dp) :: width, base, factor, average, rpowerx, rpowery, rpowerz
    real(dp) :: powerxx1,powerxx2,powerxx3,powerxx4
    real(dp) :: powerxy1,powerxy2,powerxy3,powerxy4
    real(dp) :: powerxz1,powerxz2,powerxz3,powerxz4
    real(dp) :: poweryx1,poweryx2,poweryx3,poweryx4
    real(dp) :: poweryy1,poweryy2,poweryy3,poweryy4
    real(dp) :: poweryz1,poweryz2,poweryz3,poweryz4
    real(dp) :: powerzx1,powerzx2,powerzx3,powerzx4
    real(dp) :: powerzy1,powerzy2,powerzy3,powerzy4
    real(dp) :: powerzz1,powerzz2,powerzz3,powerzz4
    real(dp) :: powerxx1_final,powerxx2_final,powerxx3_final,powerxx4_final
    real(dp) :: powerxy1_final,powerxy2_final,powerxy3_final,powerxy4_final
    real(dp) :: powerxz1_final,powerxz2_final,powerxz3_final,powerxz4_final
    real(dp) :: poweryx1_final,poweryx2_final,poweryx3_final,poweryx4_final
    real(dp) :: poweryy1_final,poweryy2_final,poweryy3_final,poweryy4_final
    real(dp) :: poweryz1_final,poweryz2_final,poweryz3_final,poweryz4_final
    real(dp) :: powerzx1_final,powerzx2_final,powerzx3_final,powerzx4_final
    real(dp) :: powerzy1_final,powerzy2_final,powerzy3_final,powerzy4_final
    real(dp) :: powerzz1_final,powerzz2_final,powerzz3_final,powerzz4_final
    real(dp) :: cmxx1,cmxx2,cmxx3,cmxy1,cmxy2,cmxy3,cmxz1,cmxz2,cmxz3,cmxz4
    real(dp) :: cmyx1,cmyx2,cmyx3,cmyy1,cmyy2,cmyy3,cmyz1,cmyz2,cmyz3,cmyz4
    real(dp) :: cmzx1,cmzx2,cmzx3,cmzy1,cmzy2,cmzy3,cmzz1,cmzz2,cmzz3,cmzz4
    real(dp) :: cmxx4,cmxy4
    real(dp) :: cmyx4,cmyy4
    real(dp) :: cmzx4,cmzy4
    real(dp) :: xmid,xmidx1,xmidx2,xmidx3,xmidx4
    real(dp) :: ymid,ymidy1,ymidy2,ymidy3,ymidy4
    real(dp) :: zmid,zmidz1,zmidz2,zmidz3,zmidz4
    real(dp) :: dcx,dcy,dcz,xnorm,ynorm,znorm,sint,cost
    real(dp) :: cmx,cmy,cmz,cmxp,cmyp,cmzp,clcd,fom,propeff,rotor_thrust
    real(dp) :: cmxx1_final,cmxx2_final,cmxx3_final,cmxx4_final
    real(dp) :: cmxy1_final,cmxy2_final,cmxy3_final,cmxy4_final
    real(dp) :: cmxz1_final,cmxz2_final,cmxz3_final,cmxz4_final
    real(dp) :: cmyx1_final,cmyx2_final,cmyx3_final,cmyx4_final
    real(dp) :: cmyy1_final,cmyy2_final,cmyy3_final,cmyy4_final
    real(dp) :: cmyz1_final,cmyz2_final,cmyz3_final,cmyz4_final
    real(dp) :: cmzx1_final,cmzx2_final,cmzx3_final,cmzx4_final
    real(dp) :: cmzy1_final,cmzy2_final,cmzy3_final,cmzy4_final
    real(dp) :: cmzz1_final,cmzz2_final,cmzz3_final,cmzz4_final

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_1p5 = 1.5_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_4   = 4.0_dp
    real(dp), parameter :: my_haf = 0.5_dp

  continue

    const = 0.0_dp

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi
    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)
    sny=sin(yaw/conv)

    fcn_loop : do j = 1, design%nfunctions

            tri_loop : do n = 1, nbfacet
              if(face_bit(n)==1) then
                node1 = ibnode(f2ntb(n,1))
                node2 = ibnode(f2ntb(n,2))
                node3 = ibnode(f2ntb(n,3))

                x1    = x(node1)
                y1    = y(node1)
                z1    = z(node1)

                x2    = x(node2)
                y2    = y(node2)
                z2    = z(node2)

                x3 = x(node3)
                y3 = y(node3)
                z3 = z(node3)

                ax = x2 - x1
                  axx1 = -1.0_dp
                  axx2 = 1.0_dp
                  axx3 = 0.0_dp
                  axy1 = 0.0_dp
                  axy2 = 0.0_dp
                  axy3 = 0.0_dp
                  axz1 = 0.0_dp
                  axz2 = 0.0_dp
                  axz3 = 0.0_dp
                ay = y2 - y1
                  ayx1 = 0.0_dp
                  ayx2 = 0.0_dp
                  ayx3 = 0.0_dp
                  ayy1 = -1.0_dp
                  ayy2 = 1.0_dp
                  ayy3 = 0.0_dp
                  ayz1 = 0.0_dp
                  ayz2 = 0.0_dp
                  ayz3 = 0.0_dp
                az = z2 - z1
                  azx1 = 0.0_dp
                  azx2 = 0.0_dp
                  azx3 = 0.0_dp
                  azy1 = 0.0_dp
                  azy2 = 0.0_dp
                  azy3 = 0.0_dp
                  azz1 = -1.0_dp
                  azz2 = 1.0_dp
                  azz3 = 0.0_dp
                bx = x3 - x1
                  bxx1 = -1.0_dp
                  bxx2 = 0.0_dp
                  bxx3 = 1.0_dp
                  bxy1 = 0.0_dp
                  bxy2 = 0.0_dp
                  bxy3 = 0.0_dp
                  bxz1 = 0.0_dp
                  bxz2 = 0.0_dp
                  bxz3 = 0.0_dp
                by = y3 - y1
                  byx1 = 0.0_dp
                  byx2 = 0.0_dp
                  byx3 = 0.0_dp
                  byy1 = -1.0_dp
                  byy2 = 0.0_dp
                  byy3 = 1.0_dp
                  byz1 = 0.0_dp
                  byz2 = 0.0_dp
                  byz3 = 0.0_dp
                bz = z3 - z1
                  bzx1 = 0.0_dp
                  bzx2 = 0.0_dp
                  bzx3 = 0.0_dp
                  bzy1 = 0.0_dp
                  bzy2 = 0.0_dp
                  bzy3 = 0.0_dp
                  bzz1 = -1.0_dp
                  bzz2 = 0.0_dp
                  bzz3 = 1.0_dp

!  norm points outward, away from grid interior.
!  norm magnitude is area of surface triangle.

                xnorm =-0.5_dp*(ay*bz - az*by)
                 xnormx1 = -0.5_dp*(ay*bzx1 + bz*ayx1 - az*byx1 - by*azx1)
                 xnormx2 = -0.5_dp*(ay*bzx2 + bz*ayx2 - az*byx2 - by*azx2)
                 xnormx3 = -0.5_dp*(ay*bzx3 + bz*ayx3 - az*byx3 - by*azx3)

                 xnormy1 = -0.5_dp*(ay*bzy1 + bz*ayy1 - az*byy1 - by*azy1)
                 xnormy2 = -0.5_dp*(ay*bzy2 + bz*ayy2 - az*byy2 - by*azy2)
                 xnormy3 = -0.5_dp*(ay*bzy3 + bz*ayy3 - az*byy3 - by*azy3)

                 xnormz1 = -0.5_dp*(ay*bzz1 + bz*ayz1 - az*byz1 - by*azz1)
                 xnormz2 = -0.5_dp*(ay*bzz2 + bz*ayz2 - az*byz2 - by*azz2)
                 xnormz3 = -0.5_dp*(ay*bzz3 + bz*ayz3 - az*byz3 - by*azz3)

                ynorm = 0.5_dp*(ax*bz - az*bx)
                 ynormx1 = 0.5_dp*(ax*bzx1 + bz*axx1 - az*bxx1 - bx*azx1)
                 ynormx2 = 0.5_dp*(ax*bzx2 + bz*axx2 - az*bxx2 - bx*azx2)
                 ynormx3 = 0.5_dp*(ax*bzx3 + bz*axx3 - az*bxx3 - bx*azx3)

                 ynormy1 = 0.5_dp*(ax*bzy1 + bz*axy1 - az*bxy1 - bx*azy1)
                 ynormy2 = 0.5_dp*(ax*bzy2 + bz*axy2 - az*bxy2 - bx*azy2)
                 ynormy3 = 0.5_dp*(ax*bzy3 + bz*axy3 - az*bxy3 - bx*azy3)

                 ynormz1 = 0.5_dp*(ax*bzz1 + bz*axz1 - az*bxz1 - bx*azz1)
                 ynormz2 = 0.5_dp*(ax*bzz2 + bz*axz2 - az*bxz2 - bx*azz2)
                 ynormz3 = 0.5_dp*(ax*bzz3 + bz*axz3 - az*bxz3 - bx*azz3)

                znorm =-0.5_dp*(ax*by - ay*bx)
                 znormx1 = -0.5_dp*(ax*byx1 + by*axx1 - ay*bxx1 - bx*ayx1)
                 znormx2 = -0.5_dp*(ax*byx2 + by*axx2 - ay*bxx2 - bx*ayx2)
                 znormx3 = -0.5_dp*(ax*byx3 + by*axx3 - ay*bxx3 - bx*ayx3)

                 znormy1 = -0.5_dp*(ax*byy1 + by*axy1 - ay*bxy1 - bx*ayy1)
                 znormy2 = -0.5_dp*(ax*byy2 + by*axy2 - ay*bxy2 - bx*ayy2)
                 znormy3 = -0.5_dp*(ax*byy3 + by*axy3 - ay*bxy3 - bx*ayy3)

                 znormz1 = -0.5_dp*(ax*byz1 + by*axz1 - ay*bxz1 - bx*ayz1)
                 znormz2 = -0.5_dp*(ax*byz2 + by*axz2 - ay*bxz2 - bx*ayz2)
                 znormz3 = -0.5_dp*(ax*byz3 + by*axz3 - ay*bxz3 - bx*ayz3)

                p1 = qnode(1,node1)
                p2 = qnode(1,node2)
                p3 = qnode(1,node3)

                press = (p1 + p2 + p3)/3.0_dp

                cp    = 2.0_dp*(press-1._dp)

                u = (qnode(2,node1) + qnode(2,node2) + qnode(2,node3)) / my_3
                v = (qnode(3,node1) + qnode(3,node2) + qnode(3,node3)) / my_3
                w = (qnode(4,node1) + qnode(4,node2) + qnode(4,node3)) / my_3

                dcx = cp*xnorm
                  dcxx1 = cp*xnormx1
                  dcxx2 = cp*xnormx2
                  dcxx3 = cp*xnormx3

                  dcxy1 = cp*xnormy1
                  dcxy2 = cp*xnormy2
                  dcxy3 = cp*xnormy3

                  dcxz1 = cp*xnormz1
                  dcxz2 = cp*xnormz2
                  dcxz3 = cp*xnormz3

                dcy = cp*ynorm
                  dcyx1 = cp*ynormx1
                  dcyx2 = cp*ynormx2
                  dcyx3 = cp*ynormx3

                  dcyy1 = cp*ynormy1
                  dcyy2 = cp*ynormy2
                  dcyy3 = cp*ynormy3

                  dcyz1 = cp*ynormz1
                  dcyz2 = cp*ynormz2
                  dcyz3 = cp*ynormz3

                dcz = cp*znorm
                  dczx1 = cp*znormx1
                  dczx2 = cp*znormx2
                  dczx3 = cp*znormx3

                  dczy1 = cp*znormy1
                  dczy2 = cp*znormy2
                  dczy3 = cp*znormy3

                  dczz1 = cp*znormz1
                  dczz2 = cp*znormz2
                  dczz3 = cp*znormz3

!               rpowerx = - dcx*u
                  powerxx1 = -dcxx1*u
                  powerxx2 = -dcxx2*u
                  powerxx3 = -dcxx3*u

                  powerxy1 = -dcxy1*u
                  powerxy2 = -dcxy2*u
                  powerxy3 = -dcxy3*u

                  powerxz1 = -dcxz1*u
                  powerxz2 = -dcxz2*u
                  powerxz3 = -dcxz3*u

!               rpowery = - dcy*v
                  poweryx1 = - dcyx1*v
                  poweryx2 = - dcyx2*v
                  poweryx3 = - dcyx3*v

                  poweryy1 = - dcyy1*v
                  poweryy2 = - dcyy2*v
                  poweryy3 = - dcyy3*v

                  poweryz1 = - dcyz1*v
                  poweryz2 = - dcyz2*v
                  poweryz3 = - dcyz3*v

!               rpowerz = - dcz*w
                  powerzx1 = - dczx1*w
                  powerzx2 = - dczx2*w
                  powerzx3 = - dczx3*w

                  powerzy1 = - dczy1*w
                  powerzy2 = - dczy2*w
                  powerzy3 = - dczy3*w

                  powerzz1 = - dczz1*w
                  powerzz2 = - dczz2*w
                  powerzz3 = - dczz3*w

                xmid = (x1 + x2 + x3)/3.0_dp
                  xmidx1 = 1.0_dp / 3.0_dp
                  xmidx2 = 1.0_dp / 3.0_dp
                  xmidx3 = 1.0_dp / 3.0_dp

                ymid = (y1 + y2 + y3)/3.0_dp
                  ymidy1 = 1.0_dp / 3.0_dp
                  ymidy2 = 1.0_dp / 3.0_dp
                  ymidy3 = 1.0_dp / 3.0_dp

                zmid = (z1 + z2 + z3)/3.0_dp
                  zmidz1 = 1.0_dp / 3.0_dp
                  zmidz2 = 1.0_dp / 3.0_dp
                  zmidz3 = 1.0_dp / 3.0_dp

!               clw(ntt) = clw(ntt) - dcx*sna     + dcz*csa
!               cdw(ntt) = cdw(ntt) + dcx*csa*csy - dcy*sny
!                                   + dcz*sna*csy

                clwx1 = - sna*dcxx1 + csa*dczx1
                clwx2 = - sna*dcxx2 + csa*dczx2
                clwx3 = - sna*dcxx3 + csa*dczx3

                clwy1 = - sna*dcxy1 + csa*dczy1
                clwy2 = - sna*dcxy2 + csa*dczy2
                clwy3 = - sna*dcxy3 + csa*dczy3

                clwz1 = - sna*dcxz1 + csa*dczz1
                clwz2 = - sna*dcxz2 + csa*dczz2
                clwz3 = - sna*dcxz3 + csa*dczz3

                cdwx1 = csa*csy*dcxx1 - sny*dcyx1 + sna*csy*dczx1
                cdwx2 = csa*csy*dcxx2 - sny*dcyx2 + sna*csy*dczx2
                cdwx3 = csa*csy*dcxx3 - sny*dcyx3 + sna*csy*dczx3

                cdwy1 = csa*csy*dcxy1 - sny*dcyy1 + sna*csy*dczy1
                cdwy2 = csa*csy*dcxy2 - sny*dcyy2 + sna*csy*dczy2
                cdwy3 = csa*csy*dcxy3 - sny*dcyy3 + sna*csy*dczy3

                cdwz1 = csa*csy*dcxz1 - sny*dcyz1 + sna*csy*dczz1
                cdwz2 = csa*csy*dcxz2 - sny*dcyz2 + sna*csy*dczz2
                cdwz3 = csa*csy*dcxz3 - sny*dcyz3 + sna*csy*dczz3

!       force%cxp  = force%cxp + dcx
!       force%cyp  = force%cyp + dcy
!       force%czp  = force%czp + dcz

                cxx1 = dcxx1
                cxx2 = dcxx2
                cxx3 = dcxx3
                cxy1 = dcxy1
                cxy2 = dcxy2
                cxy3 = dcxy3
                cxz1 = dcxz1
                cxz2 = dcxz2
                cxz3 = dcxz3

                cyx1 = dcyx1
                cyx2 = dcyx2
                cyx3 = dcyx3
                cyy1 = dcyy1
                cyy2 = dcyy2
                cyy3 = dcyy3
                cyz1 = dcyz1
                cyz2 = dcyz2
                cyz3 = dcyz3

                czx1 = dczx1
                czx2 = dczx2
                czx3 = dczx3
                czy1 = dczy1
                czy2 = dczy2
                czy3 = dczy3
                czz1 = dczz1
                czz2 = dczz2
                czz3 = dczz3

!       force%cmxp = force%cmxp + dcz*(ymid-ymc) - dcy*(zmid-zmc)
!       force%cmyp = force%cmyp - dcz*(xmid-xmc) + dcx*(zmid-zmc)
!       force%cmzp = force%cmzp + dcy*(xmid-xmc) - dcx*(ymid-ymc)

                cmxx1 = dczx1*(ymid-ymc) - dcyx1*(zmid-zmc)
                cmxx2 = dczx2*(ymid-ymc) - dcyx2*(zmid-zmc)
                cmxx3 = dczx3*(ymid-ymc) - dcyx3*(zmid-zmc)
                cmxy1 = dcz*ymidy1 + dczy1*(ymid-ymc) - dcyy1*(zmid-zmc)
                cmxy2 = dcz*ymidy2 + dczy2*(ymid-ymc) - dcyy2*(zmid-zmc)
                cmxy3 = dcz*ymidy3 + dczy3*(ymid-ymc) - dcyy3*(zmid-zmc)
                cmxz1 = dczz1*(ymid-ymc) - dcy*zmidz1 - dcyz1*(zmid-zmc)
                cmxz2 = dczz2*(ymid-ymc) - dcy*zmidz2 - dcyz2*(zmid-zmc)
                cmxz3 = dczz3*(ymid-ymc) - dcy*zmidz3 - dcyz3*(zmid-zmc)

                cmyx1 = -dcz*xmidx1 - dczx1*(xmid-xmc) + dcxx1*(zmid-zmc)
                cmyx2 = -dcz*xmidx2 - dczx2*(xmid-xmc) + dcxx2*(zmid-zmc)
                cmyx3 = -dcz*xmidx3 - dczx3*(xmid-xmc) + dcxx3*(zmid-zmc)
                cmyy1 = -dczy1*(xmid-xmc) + dcxy1*(zmid-zmc)
                cmyy2 = -dczy2*(xmid-xmc) + dcxy2*(zmid-zmc)
                cmyy3 = -dczy3*(xmid-xmc) + dcxy3*(zmid-zmc)
                cmyz1 = -dczz1*(xmid-xmc) + dcx*zmidz1 + dcxz1*(zmid-zmc)
                cmyz2 = -dczz2*(xmid-xmc) + dcx*zmidz2 + dcxz2*(zmid-zmc)
                cmyz3 = -dczz3*(xmid-xmc) + dcx*zmidz3 + dcxz3*(zmid-zmc)

                cmzx1 = dcy*xmidx1 + dcyx1*(xmid-xmc) - dcxx1*(ymid-ymc)
                cmzx2 = dcy*xmidx2 + dcyx2*(xmid-xmc) - dcxx2*(ymid-ymc)
                cmzx3 = dcy*xmidx3 + dcyx3*(xmid-xmc) - dcxx3*(ymid-ymc)
                cmzy1 = dcyy1*(xmid-xmc) - dcx*ymidy1 - dcxy1*(ymid-ymc)
                cmzy2 = dcyy2*(xmid-xmc) - dcx*ymidy2 - dcxy2*(ymid-ymc)
                cmzy3 = dcyy3*(xmid-xmc) - dcx*ymidy3 - dcxy3*(ymid-ymc)
                cmzz1 = dcyz1*(xmid-xmc) - dcxz1*(ymid-ymc)
                cmzz2 = dcyz2*(xmid-xmc) - dcxz2*(ymid-ymc)
                cmzz3 = dcyz3*(xmid-xmc) - dcxz3*(ymid-ymc)

                component_loop : do k = 1, design%function_data(j)%ncomponents

                  if ( design%function_data(j)%component_data(k)%boundary_id &
                       == 0 ) then
                    if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
                    cd   = force%cd
                    cdp  = force%cdp
                    cl   = force%cl
                    clp  = force%clp
                    cx   = force%cx
                    cy   = force%cy
                    cz   = force%cz
                    cxp  = force%cxp
                    cyp  = force%cyp
                    czp  = force%czp
                    cmx  = force%cmx
                    cmxp = force%cmxp
                    cmy  = force%cmy
                    cmyp = force%cmyp
                    cmz  = force%cmz
                    cmzp = force%cmzp
                    clcd = force%clcd
                    fom  = force%fom
                    propeff= force%propeff
                    rotor_thrust = force%rotor_thrust
                    rpowerx  = force%powerx
                    rpowery  = force%powery
                    rpowerz  = force%powerz
                  else if (                                               &
                    design%function_data(j)%component_data(k)%boundary_id &
                    == ib ) then
                    cd   = bcforce(ib)%cd
                    cdp  = bcforce(ib)%cdp
                    cl   = bcforce(ib)%cl
                    clp  = bcforce(ib)%clp
                    cx   = bcforce(ib)%cx
                    cy   = bcforce(ib)%cy
                    cz   = bcforce(ib)%cz
                    cxp  = bcforce(ib)%cxp
                    cyp  = bcforce(ib)%cyp
                    czp  = bcforce(ib)%czp
                    cmx  = bcforce(ib)%cmx
                    cmxp = bcforce(ib)%cmxp
                    cmy  = bcforce(ib)%cmy
                    cmyp = bcforce(ib)%cmyp
                    cmz  = bcforce(ib)%cmz
                    cmzp = bcforce(ib)%cmzp
                    rpowerx  = bcforce(ib)%powerx
                    rpowery  = bcforce(ib)%powery
                    rpowerz  = bcforce(ib)%powerz
                  else
                    cycle component_loop
                  endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(j)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(j)%averaging) base = clp-target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(j)%averaging) base = cxp-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(j)%averaging) base = cyp-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(j)%averaging) base = czp-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(j)%averaging) base = cmxp-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyp_id )
                if (.not.design%function_data(j)%averaging) base = cmyp-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzp_id )
                if (.not.design%function_data(j)%averaging) base = cmzp-target
                const = my_1/sref/bref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                  ioff1 = 0; ioff2 = 0; ioff3 = 0

                  clwx1_final = clwx1 * factor
                  clwy1_final = clwy1 * factor
                  clwz1_final = clwz1 * factor
                  clwx2_final = clwx2 * factor
                  clwy2_final = clwy2 * factor
                  clwz2_final = clwz2 * factor
                  clwx3_final = clwx3 * factor
                  clwy3_final = clwy3 * factor
                  clwz3_final = clwz3 * factor

                  cdwx1_final = cdwx1 * factor
                  cdwy1_final = cdwy1 * factor
                  cdwz1_final = cdwz1 * factor
                  cdwx2_final = cdwx2 * factor
                  cdwy2_final = cdwy2 * factor
                  cdwz2_final = cdwz2 * factor
                  cdwx3_final = cdwx3 * factor
                  cdwy3_final = cdwy3 * factor
                  cdwz3_final = cdwz3 * factor

                  powerxx1_final = powerxx1*factor
                  powerxx2_final = powerxx2*factor
                  powerxx3_final = powerxx3*factor

                  powerxy1_final = powerxy1*factor
                  powerxy2_final = powerxy2*factor
                  powerxy3_final = powerxy3*factor

                  powerxz1_final = powerxz1*factor
                  powerxz2_final = powerxz2*factor
                  powerxz3_final = powerxz3*factor

                  poweryx1_final = poweryx1*factor
                  poweryx2_final = poweryx2*factor
                  poweryx3_final = poweryx3*factor

                  poweryy1_final = poweryy1*factor
                  poweryy2_final = poweryy2*factor
                  poweryy3_final = poweryy3*factor

                  poweryz1_final = poweryz1*factor
                  poweryz2_final = poweryz2*factor
                  poweryz3_final = poweryz3*factor

                  powerzx1_final = powerzx1*factor
                  powerzx2_final = powerzx2*factor
                  powerzx3_final = powerzx3*factor

                  powerzy1_final = powerzy1*factor
                  powerzy2_final = powerzy2*factor
                  powerzy3_final = powerzy3*factor

                  powerzz1_final = powerzz1*factor
                  powerzz2_final = powerzz2*factor
                  powerzz3_final = powerzz3*factor

                  cxx1_final = cxx1 * factor
                  cxx2_final = cxx2 * factor
                  cxx3_final = cxx3 * factor
                  cxy1_final = cxy1 * factor
                  cxy2_final = cxy2 * factor
                  cxy3_final = cxy3 * factor
                  cxz1_final = cxz1 * factor
                  cxz2_final = cxz2 * factor
                  cxz3_final = cxz3 * factor

                  cyx1_final = cyx1 * factor
                  cyx2_final = cyx2 * factor
                  cyx3_final = cyx3 * factor
                  cyy1_final = cyy1 * factor
                  cyy2_final = cyy2 * factor
                  cyy3_final = cyy3 * factor
                  cyz1_final = cyz1 * factor
                  cyz2_final = cyz2 * factor
                  cyz3_final = cyz3 * factor

                  czx1_final = czx1 * factor
                  czx2_final = czx2 * factor
                  czx3_final = czx3 * factor
                  czy1_final = czy1 * factor
                  czy2_final = czy2 * factor
                  czy3_final = czy3 * factor
                  czz1_final = czz1 * factor
                  czz2_final = czz2 * factor
                  czz3_final = czz3 * factor

                  cmxx1_final = cmxx1 * factor
                  cmxx2_final = cmxx2 * factor
                  cmxx3_final = cmxx3 * factor
                  cmxy1_final = cmxy1 * factor
                  cmxy2_final = cmxy2 * factor
                  cmxy3_final = cmxy3 * factor
                  cmxz1_final = cmxz1 * factor
                  cmxz2_final = cmxz2 * factor
                  cmxz3_final = cmxz3 * factor

                  cmyx1_final = cmyx1 * factor
                  cmyx2_final = cmyx2 * factor
                  cmyx3_final = cmyx3 * factor
                  cmyy1_final = cmyy1 * factor
                  cmyy2_final = cmyy2 * factor
                  cmyy3_final = cmyy3 * factor
                  cmyz1_final = cmyz1 * factor
                  cmyz2_final = cmyz2 * factor
                  cmyz3_final = cmyz3 * factor

                  cmzx1_final = cmzx1 * factor
                  cmzx2_final = cmzx2 * factor
                  cmzx3_final = cmzx3 * factor
                  cmzy1_final = cmzy1 * factor
                  cmzy2_final = cmzy2 * factor
                  cmzy3_final = cmzy3 * factor
                  cmzz1_final = cmzz1 * factor
                  cmzz2_final = cmzz2 * factor
                  cmzz3_final = cmzz3 * factor

                    do i = ia(node1), ia(node1+1)-1
                      icol = ja(i)
                      if ( icol == node1 ) ioff1 = i
                    end do
                    do i = ia(node2), ia(node2+1)-1
                      icol = ja(i)
                      if ( icol == node2 ) ioff2 = i
                    end do
                    do i = ia(node3), ia(node3+1)-1
                      icol = ja(i)
                      if ( icol == node3 ) ioff3 = i
                    end do

                    select case (design%function_data(j)%component_data(k)%name)
                    case ( cl_id, clp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + clwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + clwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + clwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + clwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + clwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + clwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + clwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + clwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + clwz3_final
                    case ( cd_id, cdp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cdwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cdwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cdwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cdwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cdwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cdwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cdwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cdwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cdwz3_final
                    case ( powerx_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerxz3_final
                    case ( powery_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + poweryx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + poweryy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + poweryz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + poweryx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + poweryy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + poweryz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + poweryx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + poweryy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + poweryz3_final
                    case ( powerz_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerzz3_final
                    case ( cx_id, cxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cxz3_final
                    case ( cy_id, cyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cyz3_final
                    case ( cz_id, czp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + czx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + czy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + czz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + czx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + czy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + czz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + czx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + czy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + czz3_final
                    case ( cmx_id, cmxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmxz3_final
                    case ( cmy_id, cmyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmyz3_final
                    case ( cmz_id, cmzp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmzz3_final
                    case ( clcd_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwx1 - cl/cd/cd*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwy1 - cl/cd/cd*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwz1 - cl/cd/cd*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwx2 - cl/cd/cd*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwy2 - cl/cd/cd*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwz2 - cl/cd/cd*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwx3 - cl/cd/cd*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwy3 - cl/cd/cd*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwz3 - cl/cd/cd*cdwz3)
                    case ( fom_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz3/sref/bref)
                    case ( propeff_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) - factor               &
                                         *(my_1/cmz*czx1/sref                  &
                                      - cz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) - factor               &
                                         *(my_1/cmz*czy1/sref                  &
                                      - cz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) - factor               &
                                         *(my_1/cmz*czz1/sref                  &
                                      - cz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) - factor               &
                                         *(my_1/cmz*czx2/sref                  &
                                      - cz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) - factor               &
                                         *(my_1/cmz*czy2/sref                  &
                                      - cz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) - factor               &
                                         *(my_1/cmz*czz2/sref                  &
                                      - cz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) - factor               &
                                         *(my_1/cmz*czx3/sref                  &
                                      - cz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) - factor               &
                                         *(my_1/cmz*czy3/sref                  &
                                      - cz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) - factor               &
                                         *(my_1/cmz*czz3/sref                  &
                                      - cz/cmz/cmz*cmzz3/sref/bref)
                    case ( rotor_thrust_id )
                      sint = sin(thrust_angle)
                      cost = cos(thrust_angle)
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(cost*clwx1 - sint*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(cost*clwy1 - sint*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(cost*clwz1 - sint*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(cost*clwx2 - sint*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(cost*clwy2 - sint*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(cost*clwz2 - sint*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(cost*clwx3 - sint*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(cost*clwy3 - sint*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(cost*clwz3 - sint*cdwz3)
                    case default
                    end select

                end do component_loop

              endif

            end do tri_loop

            quad_loop : do n = 1, nbfaceq
              if(face_bitq(n)==1) then
                node1 = ibnode(f2nqb(n,1))
                node2 = ibnode(f2nqb(n,2))
                node3 = ibnode(f2nqb(n,3))
                node4 = ibnode(f2nqb(n,4))

                x1 = x(node1)
                y1 = y(node1)
                z1 = z(node1)

                x2 = x(node2)
                y2 = y(node2)
                z2 = z(node2)

                x3 = x(node3)
                y3 = y(node3)
                z3 = z(node3)

                x4 = x(node4)
                y4 = y(node4)
                z4 = z(node4)

!         quad normal computed as 1/2 the  cross product of the 2 diagonals
!         change sign to point away from interior

                xnorm = -my_haf*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
                  xnormx1 = my_0
                  xnormx2 = my_0
                  xnormx3 = my_0
                  xnormx4 = my_0

                  xnormy1 =  my_haf*(z4 - z2)
                  xnormy2 = -my_haf*(z3 - z1)
                  xnormy3 = -my_haf*(z4 - z2)
                  xnormy4 =  my_haf*(z3 - z1)

                  xnormz1 = -my_haf*(y4 - y2)
                  xnormz2 =  my_haf*(y3 - y1)
                  xnormz3 =  my_haf*(y4 - y2)
                  xnormz4 = -my_haf*(y3 - y1)

                ynorm = -my_haf*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
                  ynormx1 = -my_haf*(z4 - z2)
                  ynormx2 =  my_haf*(z3 - z1)
                  ynormx3 =  my_haf*(z4 - z2)
                  ynormx4 = -my_haf*(z3 - z1)

                  ynormy1 = my_0
                  ynormy2 = my_0
                  ynormy3 = my_0
                  ynormy4 = my_0

                  ynormz1 =  my_haf*(x4 - x2)
                  ynormz2 = -my_haf*(x3 - x1)
                  ynormz3 = -my_haf*(x4 - x2)
                  ynormz4 =  my_haf*(x3 - x1)

                znorm = -my_haf*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )
                  znormx1 =  my_haf*(y4 - y2)
                  znormx2 = -my_haf*(y3 - y1)
                  znormx3 = -my_haf*(y4 - y2)
                  znormx4 =  my_haf*(y3 - y1)

                  znormy1 = -my_haf*(x4 - x2)
                  znormy2 =  my_haf*(x3 - x1)
                  znormy3 =  my_haf*(x4 - x2)
                  znormy4 = -my_haf*(x3 - x1)

                  znormz1 = my_0
                  znormz2 = my_0
                  znormz3 = my_0
                  znormz4 = my_0

                p1 = qnode(1,node1)
                p2 = qnode(1,node2)
                p3 = qnode(1,node3)
                p4 = qnode(1,node4)

                press = (p1 + p2 + p3 + p4)/4.0_dp

                cp    = 2.0_dp*(press-1._dp)

                u = (qnode(2,node1) + qnode(2,node2) + qnode(2,node3)          &
                   + qnode(2,node4)) / my_4
                v = (qnode(3,node1) + qnode(3,node2) + qnode(3,node3)          &
                   + qnode(3,node4)) / my_4
                w = (qnode(4,node1) + qnode(4,node2) + qnode(4,node3)          &
                   + qnode(4,node4)) / my_4

                dcx = cp*xnorm
                  dcxx1 = cp*xnormx1
                  dcxx2 = cp*xnormx2
                  dcxx3 = cp*xnormx3
                  dcxx4 = cp*xnormx4

                  dcxy1 = cp*xnormy1
                  dcxy2 = cp*xnormy2
                  dcxy3 = cp*xnormy3
                  dcxy4 = cp*xnormy4

                  dcxz1 = cp*xnormz1
                  dcxz2 = cp*xnormz2
                  dcxz3 = cp*xnormz3
                  dcxz4 = cp*xnormz4

                dcy = cp*ynorm
                  dcyx1 = cp*ynormx1
                  dcyx2 = cp*ynormx2
                  dcyx3 = cp*ynormx3
                  dcyx4 = cp*ynormx4

                  dcyy1 = cp*ynormy1
                  dcyy2 = cp*ynormy2
                  dcyy3 = cp*ynormy3
                  dcyy4 = cp*ynormy4

                  dcyz1 = cp*ynormz1
                  dcyz2 = cp*ynormz2
                  dcyz3 = cp*ynormz3
                  dcyz4 = cp*ynormz4

                dcz = cp*znorm
                  dczx1 = cp*znormx1
                  dczx2 = cp*znormx2
                  dczx3 = cp*znormx3
                  dczx4 = cp*znormx4

                  dczy1 = cp*znormy1
                  dczy2 = cp*znormy2
                  dczy3 = cp*znormy3
                  dczy4 = cp*znormy4

                  dczz1 = cp*znormz1
                  dczz2 = cp*znormz2
                  dczz3 = cp*znormz3
                  dczz4 = cp*znormz4

!               rpowerx = - dcx*u
                  powerxx1 = -dcxx1*u
                  powerxx2 = -dcxx2*u
                  powerxx3 = -dcxx3*u
                  powerxx4 = -dcxx4*u

                  powerxy1 = -dcxy1*u
                  powerxy2 = -dcxy2*u
                  powerxy3 = -dcxy3*u
                  powerxy4 = -dcxy4*u

                  powerxz1 = -dcxz1*u
                  powerxz2 = -dcxz2*u
                  powerxz3 = -dcxz3*u
                  powerxz4 = -dcxz4*u

!               rpowery = - dcy*v
                  poweryx1 = - dcyx1*v
                  poweryx2 = - dcyx2*v
                  poweryx3 = - dcyx3*v
                  poweryx4 = - dcyx4*v

                  poweryy1 = - dcyy1*v
                  poweryy2 = - dcyy2*v
                  poweryy3 = - dcyy3*v
                  poweryy4 = - dcyy4*v

                  poweryz1 = - dcyz1*v
                  poweryz2 = - dcyz2*v
                  poweryz3 = - dcyz3*v
                  poweryz4 = - dcyz4*v

!               rpowerz = - dcz*w
                  powerzx1 = - dczx1*w
                  powerzx2 = - dczx2*w
                  powerzx3 = - dczx3*w
                  powerzx4 = - dczx4*w

                  powerzy1 = - dczy1*w
                  powerzy2 = - dczy2*w
                  powerzy3 = - dczy3*w
                  powerzy4 = - dczy4*w

                  powerzz1 = - dczz1*w
                  powerzz2 = - dczz2*w
                  powerzz3 = - dczz3*w
                  powerzz4 = - dczz4*w

                xmid = (x1 + x2 + x3 + x4)/4.0_dp
                  xmidx1 = 1.0_dp / 4.0_dp
                  xmidx2 = 1.0_dp / 4.0_dp
                  xmidx3 = 1.0_dp / 4.0_dp
                  xmidx4 = 1.0_dp / 4.0_dp

                ymid = (y1 + y2 + y3 + y4)/4.0_dp
                  ymidy1 = 1.0_dp / 4.0_dp
                  ymidy2 = 1.0_dp / 4.0_dp
                  ymidy3 = 1.0_dp / 4.0_dp
                  ymidy4 = 1.0_dp / 4.0_dp

                zmid = (z1 + z2 + z3 + z4)/4.0_dp
                  zmidz1 = 1.0_dp / 4.0_dp
                  zmidz2 = 1.0_dp / 4.0_dp
                  zmidz3 = 1.0_dp / 4.0_dp
                  zmidz4 = 1.0_dp / 4.0_dp

!               clw(ntt) = clw(ntt) - dcx*sna     + dcz*csa
!               cdw(ntt) = cdw(ntt) + dcx*csa*csy - dcy*sny
!                                   + dcz*sna*csy

                clwx1 = - sna*dcxx1 + csa*dczx1
                clwx2 = - sna*dcxx2 + csa*dczx2
                clwx3 = - sna*dcxx3 + csa*dczx3
                clwx4 = - sna*dcxx4 + csa*dczx4

                clwy1 = - sna*dcxy1 + csa*dczy1
                clwy2 = - sna*dcxy2 + csa*dczy2
                clwy3 = - sna*dcxy3 + csa*dczy3
                clwy4 = - sna*dcxy4 + csa*dczy4

                clwz1 = - sna*dcxz1 + csa*dczz1
                clwz2 = - sna*dcxz2 + csa*dczz2
                clwz3 = - sna*dcxz3 + csa*dczz3
                clwz4 = - sna*dcxz4 + csa*dczz4

                cdwx1 = csa*csy*dcxx1 - sny*dcyx1 + sna*csy*dczx1
                cdwx2 = csa*csy*dcxx2 - sny*dcyx2 + sna*csy*dczx2
                cdwx3 = csa*csy*dcxx3 - sny*dcyx3 + sna*csy*dczx3
                cdwx4 = csa*csy*dcxx4 - sny*dcyx4 + sna*csy*dczx4

                cdwy1 = csa*csy*dcxy1 - sny*dcyy1 + sna*csy*dczy1
                cdwy2 = csa*csy*dcxy2 - sny*dcyy2 + sna*csy*dczy2
                cdwy3 = csa*csy*dcxy3 - sny*dcyy3 + sna*csy*dczy3
                cdwy4 = csa*csy*dcxy4 - sny*dcyy4 + sna*csy*dczy4

                cdwz1 = csa*csy*dcxz1 - sny*dcyz1 + sna*csy*dczz1
                cdwz2 = csa*csy*dcxz2 - sny*dcyz2 + sna*csy*dczz2
                cdwz3 = csa*csy*dcxz3 - sny*dcyz3 + sna*csy*dczz3
                cdwz4 = csa*csy*dcxz4 - sny*dcyz4 + sna*csy*dczz4

!       force%cxp  = force%cxp + dcx
!       force%cyp  = force%cyp + dcy
!       force%czp  = force%czp + dcz

                cxx1 = dcxx1
                cxx2 = dcxx2
                cxx3 = dcxx3
                cxx4 = dcxx4
                cxy1 = dcxy1
                cxy2 = dcxy2
                cxy3 = dcxy3
                cxy4 = dcxy4
                cxz1 = dcxz1
                cxz2 = dcxz2
                cxz3 = dcxz3
                cxz4 = dcxz4

                cyx1 = dcyx1
                cyx2 = dcyx2
                cyx3 = dcyx3
                cyx4 = dcyx4
                cyy1 = dcyy1
                cyy2 = dcyy2
                cyy3 = dcyy3
                cyy4 = dcyy4
                cyz1 = dcyz1
                cyz2 = dcyz2
                cyz3 = dcyz3
                cyz4 = dcyz4

                czx1 = dczx1
                czx2 = dczx2
                czx3 = dczx3
                czx4 = dczx4
                czy1 = dczy1
                czy2 = dczy2
                czy3 = dczy3
                czy4 = dczy4
                czz1 = dczz1
                czz2 = dczz2
                czz3 = dczz3
                czz4 = dczz4

!       force%cmxp = force%cmxp + dcz*(ymid-ymc) - dcy*(zmid-zmc)
!       force%cmyp = force%cmyp - dcz*(xmid-xmc) + dcx*(zmid-zmc)
!       force%cmzp = force%cmzp + dcy*(xmid-xmc) - dcx*(ymid-ymc)

                cmxx1 = dczx1*(ymid-ymc) - dcyx1*(zmid-zmc)
                cmxx2 = dczx2*(ymid-ymc) - dcyx2*(zmid-zmc)
                cmxx3 = dczx3*(ymid-ymc) - dcyx3*(zmid-zmc)
                cmxx4 = dczx4*(ymid-ymc) - dcyx4*(zmid-zmc)
                cmxy1 = dcz*ymidy1 + dczy1*(ymid-ymc) - dcyy1*(zmid-zmc)
                cmxy2 = dcz*ymidy2 + dczy2*(ymid-ymc) - dcyy2*(zmid-zmc)
                cmxy3 = dcz*ymidy3 + dczy3*(ymid-ymc) - dcyy3*(zmid-zmc)
                cmxy4 = dcz*ymidy4 + dczy4*(ymid-ymc) - dcyy4*(zmid-zmc)
                cmxz1 = dczz1*(ymid-ymc) - dcy*zmidz1 - dcyz1*(zmid-zmc)
                cmxz2 = dczz2*(ymid-ymc) - dcy*zmidz2 - dcyz2*(zmid-zmc)
                cmxz3 = dczz3*(ymid-ymc) - dcy*zmidz3 - dcyz3*(zmid-zmc)
                cmxz4 = dczz4*(ymid-ymc) - dcy*zmidz4 - dcyz4*(zmid-zmc)

                cmyx1 = -dcz*xmidx1 - dczx1*(xmid-xmc) + dcxx1*(zmid-zmc)
                cmyx2 = -dcz*xmidx2 - dczx2*(xmid-xmc) + dcxx2*(zmid-zmc)
                cmyx3 = -dcz*xmidx3 - dczx3*(xmid-xmc) + dcxx3*(zmid-zmc)
                cmyx4 = -dcz*xmidx4 - dczx4*(xmid-xmc) + dcxx4*(zmid-zmc)
                cmyy1 = -dczy1*(xmid-xmc) + dcxy1*(zmid-zmc)
                cmyy2 = -dczy2*(xmid-xmc) + dcxy2*(zmid-zmc)
                cmyy3 = -dczy3*(xmid-xmc) + dcxy3*(zmid-zmc)
                cmyy4 = -dczy4*(xmid-xmc) + dcxy4*(zmid-zmc)
                cmyz1 = -dczz1*(xmid-xmc) + dcx*zmidz1 + dcxz1*(zmid-zmc)
                cmyz2 = -dczz2*(xmid-xmc) + dcx*zmidz2 + dcxz2*(zmid-zmc)
                cmyz3 = -dczz3*(xmid-xmc) + dcx*zmidz3 + dcxz3*(zmid-zmc)
                cmyz4 = -dczz4*(xmid-xmc) + dcx*zmidz4 + dcxz4*(zmid-zmc)

                cmzx1 = dcy*xmidx1 + dcyx1*(xmid-xmc) - dcxx1*(ymid-ymc)
                cmzx2 = dcy*xmidx2 + dcyx2*(xmid-xmc) - dcxx2*(ymid-ymc)
                cmzx3 = dcy*xmidx3 + dcyx3*(xmid-xmc) - dcxx3*(ymid-ymc)
                cmzx4 = dcy*xmidx4 + dcyx4*(xmid-xmc) - dcxx4*(ymid-ymc)
                cmzy1 = dcyy1*(xmid-xmc) - dcx*ymidy1 - dcxy1*(ymid-ymc)
                cmzy2 = dcyy2*(xmid-xmc) - dcx*ymidy2 - dcxy2*(ymid-ymc)
                cmzy3 = dcyy3*(xmid-xmc) - dcx*ymidy3 - dcxy3*(ymid-ymc)
                cmzy4 = dcyy4*(xmid-xmc) - dcx*ymidy4 - dcxy4*(ymid-ymc)
                cmzz1 = dcyz1*(xmid-xmc) - dcxz1*(ymid-ymc)
                cmzz2 = dcyz2*(xmid-xmc) - dcxz2*(ymid-ymc)
                cmzz3 = dcyz3*(xmid-xmc) - dcxz3*(ymid-ymc)
                cmzz4 = dcyz4*(xmid-xmc) - dcxz4*(ymid-ymc)

                component_loop2 : do k = 1, design%function_data(j)%ncomponents

                  if ( design%function_data(j)%component_data(k)%boundary_id &
                       == 0 ) then
                    if ( .not. bcforce(ib)%add_to_total ) cycle component_loop2
                    cd   = force%cd
                    cdp  = force%cdp
                    cl   = force%cl
                    clp  = force%clp
                    cx   = force%cx
                    cy   = force%cy
                    cz   = force%cz
                    cxp  = force%cxp
                    cyp  = force%cyp
                    czp  = force%czp
                    cmx  = force%cmx
                    cmxp = force%cmxp
                    cmy  = force%cmy
                    cmyp = force%cmyp
                    cmz  = force%cmz
                    cmzp = force%cmzp
                    clcd = force%clcd
                    fom  = force%fom
                    propeff= force%propeff
                    rotor_thrust = force%rotor_thrust
                    rpowerx  = force%powerx
                    rpowery  = force%powery
                    rpowerz  = force%powerz
                  else if (                                               &
                    design%function_data(j)%component_data(k)%boundary_id &
                    == ib ) then
                    cd   = bcforce(ib)%cd
                    cdp  = bcforce(ib)%cdp
                    cl   = bcforce(ib)%cl
                    clp  = bcforce(ib)%clp
                    cx   = bcforce(ib)%cx
                    cy   = bcforce(ib)%cy
                    cz   = bcforce(ib)%cz
                    cxp  = bcforce(ib)%cxp
                    cyp  = bcforce(ib)%cyp
                    czp  = bcforce(ib)%czp
                    cmx  = bcforce(ib)%cmx
                    cmxp = bcforce(ib)%cmxp
                    cmy  = bcforce(ib)%cmy
                    cmyp = bcforce(ib)%cmyp
                    cmz  = bcforce(ib)%cmz
                    cmzp = bcforce(ib)%cmzp
                    rpowerx= bcforce(ib)%powerx
                    rpowery= bcforce(ib)%powery
                    rpowerz= bcforce(ib)%powerz
                  else
                    cycle component_loop2
                  endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdp_id )
                if (.not.design%function_data(j)%averaging) base = cdp-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clp_id )
                if (.not.design%function_data(j)%averaging) base = clp-target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxp_id )
                if (.not.design%function_data(j)%averaging) base = cxp-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyp_id )
                if (.not.design%function_data(j)%averaging) base = cyp-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czp_id )
                if (.not.design%function_data(j)%averaging) base = czp-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxp_id )
                if (.not.design%function_data(j)%averaging) base = cmxp-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyp_id )
                if (.not.design%function_data(j)%averaging) base = cmyp-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzp_id )
                if (.not.design%function_data(j)%averaging) base = cmzp-target
                const = my_1/sref/bref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case default
                cycle component_loop2
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                  ioff1 = 0; ioff2 = 0; ioff3 = 0; ioff4 = 0

                  clwx1_final = clwx1 * factor
                  clwy1_final = clwy1 * factor
                  clwz1_final = clwz1 * factor
                  clwx2_final = clwx2 * factor
                  clwy2_final = clwy2 * factor
                  clwz2_final = clwz2 * factor
                  clwx3_final = clwx3 * factor
                  clwy3_final = clwy3 * factor
                  clwz3_final = clwz3 * factor
                  clwx4_final = clwx4 * factor
                  clwy4_final = clwy4 * factor
                  clwz4_final = clwz4 * factor

                  cdwx1_final = cdwx1 * factor
                  cdwy1_final = cdwy1 * factor
                  cdwz1_final = cdwz1 * factor
                  cdwx2_final = cdwx2 * factor
                  cdwy2_final = cdwy2 * factor
                  cdwz2_final = cdwz2 * factor
                  cdwx3_final = cdwx3 * factor
                  cdwy3_final = cdwy3 * factor
                  cdwz3_final = cdwz3 * factor
                  cdwx4_final = cdwx4 * factor
                  cdwy4_final = cdwy4 * factor
                  cdwz4_final = cdwz4 * factor

                  powerxx1_final = powerxx1*factor
                  powerxx2_final = powerxx2*factor
                  powerxx3_final = powerxx3*factor
                  powerxx4_final = powerxx4*factor

                  powerxy1_final = powerxy1*factor
                  powerxy2_final = powerxy2*factor
                  powerxy3_final = powerxy3*factor
                  powerxy4_final = powerxy4*factor

                  powerxz1_final = powerxz1*factor
                  powerxz2_final = powerxz2*factor
                  powerxz3_final = powerxz3*factor
                  powerxz4_final = powerxz4*factor

                  poweryx1_final = poweryx1*factor
                  poweryx2_final = poweryx2*factor
                  poweryx3_final = poweryx3*factor
                  poweryx4_final = poweryx4*factor

                  poweryy1_final = poweryy1*factor
                  poweryy2_final = poweryy2*factor
                  poweryy3_final = poweryy3*factor
                  poweryy4_final = poweryy4*factor

                  poweryz1_final = poweryz1*factor
                  poweryz2_final = poweryz2*factor
                  poweryz3_final = poweryz3*factor
                  poweryz4_final = poweryz4*factor

                  powerzx1_final = powerzx1*factor
                  powerzx2_final = powerzx2*factor
                  powerzx3_final = powerzx3*factor
                  powerzx4_final = powerzx4*factor

                  powerzy1_final = powerzy1*factor
                  powerzy2_final = powerzy2*factor
                  powerzy3_final = powerzy3*factor
                  powerzy4_final = powerzy4*factor

                  powerzz1_final = powerzz1*factor
                  powerzz2_final = powerzz2*factor
                  powerzz3_final = powerzz3*factor
                  powerzz4_final = powerzz4*factor

                  cxx1_final = cxx1 * factor
                  cxx2_final = cxx2 * factor
                  cxx3_final = cxx3 * factor
                  cxx4_final = cxx4 * factor
                  cxy1_final = cxy1 * factor
                  cxy2_final = cxy2 * factor
                  cxy3_final = cxy3 * factor
                  cxy4_final = cxy4 * factor
                  cxz1_final = cxz1 * factor
                  cxz2_final = cxz2 * factor
                  cxz3_final = cxz3 * factor
                  cxz4_final = cxz4 * factor

                  cyx1_final = cyx1 * factor
                  cyx2_final = cyx2 * factor
                  cyx3_final = cyx3 * factor
                  cyx4_final = cyx4 * factor
                  cyy1_final = cyy1 * factor
                  cyy2_final = cyy2 * factor
                  cyy3_final = cyy3 * factor
                  cyy4_final = cyy4 * factor
                  cyz1_final = cyz1 * factor
                  cyz2_final = cyz2 * factor
                  cyz3_final = cyz3 * factor
                  cyz4_final = cyz4 * factor

                  czx1_final = czx1 * factor
                  czx2_final = czx2 * factor
                  czx3_final = czx3 * factor
                  czx4_final = czx4 * factor
                  czy1_final = czy1 * factor
                  czy2_final = czy2 * factor
                  czy3_final = czy3 * factor
                  czy4_final = czy4 * factor
                  czz1_final = czz1 * factor
                  czz2_final = czz2 * factor
                  czz3_final = czz3 * factor
                  czz4_final = czz4 * factor

                  cmxx1_final = cmxx1 * factor
                  cmxx2_final = cmxx2 * factor
                  cmxx3_final = cmxx3 * factor
                  cmxx4_final = cmxx4 * factor
                  cmxy1_final = cmxy1 * factor
                  cmxy2_final = cmxy2 * factor
                  cmxy3_final = cmxy3 * factor
                  cmxy4_final = cmxy4 * factor
                  cmxz1_final = cmxz1 * factor
                  cmxz2_final = cmxz2 * factor
                  cmxz3_final = cmxz3 * factor
                  cmxz4_final = cmxz4 * factor

                  cmyx1_final = cmyx1 * factor
                  cmyx2_final = cmyx2 * factor
                  cmyx3_final = cmyx3 * factor
                  cmyx4_final = cmyx4 * factor
                  cmyy1_final = cmyy1 * factor
                  cmyy2_final = cmyy2 * factor
                  cmyy3_final = cmyy3 * factor
                  cmyy4_final = cmyy4 * factor
                  cmyz1_final = cmyz1 * factor
                  cmyz2_final = cmyz2 * factor
                  cmyz3_final = cmyz3 * factor
                  cmyz4_final = cmyz4 * factor

                  cmzx1_final = cmzx1 * factor
                  cmzx2_final = cmzx2 * factor
                  cmzx3_final = cmzx3 * factor
                  cmzx4_final = cmzx4 * factor
                  cmzy1_final = cmzy1 * factor
                  cmzy2_final = cmzy2 * factor
                  cmzy3_final = cmzy3 * factor
                  cmzy4_final = cmzy4 * factor
                  cmzz1_final = cmzz1 * factor
                  cmzz2_final = cmzz2 * factor
                  cmzz3_final = cmzz3 * factor
                  cmzz4_final = cmzz4 * factor

                    do i = ia(node1), ia(node1+1)-1
                      icol = ja(i)
                      if ( icol == node1 ) ioff1 = i
                    end do
                    do i = ia(node2), ia(node2+1)-1
                      icol = ja(i)
                      if ( icol == node2 ) ioff2 = i
                    end do
                    do i = ia(node3), ia(node3+1)-1
                      icol = ja(i)
                      if ( icol == node3 ) ioff3 = i
                    end do
                    do i = ia(node4), ia(node4+1)-1
                      icol = ja(i)
                      if ( icol == node4 ) ioff4 = i
                    end do

                    select case (design%function_data(j)%component_data(k)%name)
                    case ( cl_id, clp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + clwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + clwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + clwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + clwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + clwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + clwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + clwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + clwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + clwz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + clwx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + clwy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + clwz4_final
                    case ( cd_id, cdp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cdwx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cdwy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cdwz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cdwx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cdwy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cdwz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cdwx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cdwy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cdwz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cdwx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cdwy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cdwz4_final
                    case ( powerx_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerxz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + powerxx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + powerxy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + powerxz4_final
                    case ( powery_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + poweryx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + poweryy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + poweryz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + poweryx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + poweryy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + poweryz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + poweryx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + poweryy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + poweryz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + poweryx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + poweryy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + poweryz4_final
                    case ( powerz_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + powerzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + powerzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + powerzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + powerzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + powerzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + powerzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + powerzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + powerzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + powerzz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + powerzx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + powerzy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + powerzz4_final
                    case ( cx_id, cxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cxz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cxx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cxy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cxz4_final
                    case ( cy_id, cyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cyz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cyx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cyy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cyz4_final
                    case ( cz_id, czp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + czx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + czy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + czz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + czx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + czy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + czz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + czx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + czy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + czz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + czx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + czy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + czz4_final
                    case ( cmx_id, cmxp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmxx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmxy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmxz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmxx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmxy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmxz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmxx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmxy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmxz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cmxx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cmxy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cmxz4_final
                    case ( cmy_id, cmyp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmyx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmyy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmyz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmyx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmyy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmyz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmyx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmyy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmyz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cmyx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cmyy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cmyz4_final
                    case ( cmz_id, cmzp_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + cmzx1_final
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + cmzy1_final
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + cmzz1_final
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + cmzx2_final
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + cmzy2_final
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + cmzz2_final
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + cmzx3_final
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + cmzy3_final
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + cmzz3_final
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + cmzx4_final
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + cmzy4_final
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + cmzz4_final
                    case ( clcd_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwx1 - cl/cd/cd*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwy1 - cl/cd/cd*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(1.0_dp/cd*clwz1 - cl/cd/cd*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwx2 - cl/cd/cd*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwy2 - cl/cd/cd*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(1.0_dp/cd*clwz2 - cl/cd/cd*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwx3 - cl/cd/cd*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwy3 - cl/cd/cd*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(1.0_dp/cd*clwz3 - cl/cd/cd*cdwz3)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j)                        &
                        + factor*(1.0_dp/cd*clwx4 - cl/cd/cd*cdwx4)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j)                        &
                        + factor*(1.0_dp/cd*clwy4 - cl/cd/cd*cdwy4)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j)                        &
                        + factor*(1.0_dp/cd*clwz4 - cl/cd/cd*cdwz4)
                    case ( fom_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz1/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz2/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz3/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz3/sref/bref)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwx4/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzx4/sref/bref)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwy4/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzy4/sref/bref)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) + factor               &
                                         *(my_1p5*cl*cl/cmz/cmz*clwz4/sref     &
                                    - cl*cl*cl/cmz/cmz/cmz*cmzz4/sref/bref)
                    case ( propeff_id )
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j) - factor               &
                                         *(my_1/cmz*czx1/sref                  &
                                      - cz/cmz/cmz*cmzx1/sref/bref)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j) - factor               &
                                         *(my_1/cmz*czy1/sref                  &
                                      - cz/cmz/cmz*cmzy1/sref/bref)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j) - factor               &
                                         *(my_1/cmz*czz1/sref                  &
                                      - cz/cmz/cmz*cmzz1/sref/bref)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j) - factor               &
                                         *(my_1/cmz*czx2/sref                  &
                                      - cz/cmz/cmz*cmzx2/sref/bref)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j) - factor               &
                                         *(my_1/cmz*czy2/sref                  &
                                      - cz/cmz/cmz*cmzy2/sref/bref)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j) - factor               &
                                         *(my_1/cmz*czz2/sref                  &
                                      - cz/cmz/cmz*cmzz2/sref/bref)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j) - factor               &
                                         *(my_1/cmz*czx3/sref                  &
                                      - cz/cmz/cmz*cmzx3/sref/bref)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j) - factor               &
                                         *(my_1/cmz*czy3/sref                  &
                                      - cz/cmz/cmz*cmzy3/sref/bref)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j) - factor               &
                                         *(my_1/cmz*czz3/sref                  &
                                      - cz/cmz/cmz*cmzz3/sref/bref)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j) - factor               &
                                         *(my_1/cmz*czx4/sref                  &
                                      - cz/cmz/cmz*cmzx4/sref/bref)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j) - factor               &
                                         *(my_1/cmz*czy4/sref                  &
                                      - cz/cmz/cmz*cmzy4/sref/bref)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j) - factor               &
                                         *(my_1/cmz*czz4/sref                  &
                                      - cz/cmz/cmz*cmzz4/sref/bref)
                    case ( rotor_thrust_id )
                      sint = sin(thrust_angle)
                      cost = cos(thrust_angle)
                      dfdx(1,ioff1,j) = dfdx(1,ioff1,j)                        &
                        + factor*(cost*clwx1 - sint*cdwx1)
                      dfdx(2,ioff1,j) = dfdx(2,ioff1,j)                        &
                        + factor*(cost*clwy1 - sint*cdwy1)
                      dfdx(3,ioff1,j) = dfdx(3,ioff1,j)                        &
                        + factor*(cost*clwz1 - sint*cdwz1)
                      dfdx(1,ioff2,j) = dfdx(1,ioff2,j)                        &
                        + factor*(cost*clwx2 - sint*cdwx2)
                      dfdx(2,ioff2,j) = dfdx(2,ioff2,j)                        &
                        + factor*(cost*clwy2 - sint*cdwy2)
                      dfdx(3,ioff2,j) = dfdx(3,ioff2,j)                        &
                        + factor*(cost*clwz2 - sint*cdwz2)
                      dfdx(1,ioff3,j) = dfdx(1,ioff3,j)                        &
                        + factor*(cost*clwx3 - sint*cdwx3)
                      dfdx(2,ioff3,j) = dfdx(2,ioff3,j)                        &
                        + factor*(cost*clwy3 - sint*cdwy3)
                      dfdx(3,ioff3,j) = dfdx(3,ioff3,j)                        &
                        + factor*(cost*clwz3 - sint*cdwz3)
                      dfdx(1,ioff4,j) = dfdx(1,ioff4,j)                        &
                        + factor*(cost*clwx4 - sint*cdwx4)
                      dfdx(2,ioff4,j) = dfdx(2,ioff4,j)                        &
                        + factor*(cost*clwy4 - sint*cdwy4)
                      dfdx(3,ioff4,j) = dfdx(3,ioff4,j)                        &
                        + factor*(cost*clwz4 - sint*cdwz4)

                    case default
                    end select

                end do component_loop2

              endif

            end do quad_loop

      end do fcn_loop

  end subroutine pressurexyzi

!================================ SKINFRICXYZ ================================80
!
!  This gets the derivatives of skin friction wrt the coordinates
!  of the design points
!
!=============================================================================80

  subroutine skinfricxyz(nnodes01,x,y,z,qnode,amut,nbnode,nbfacet,             &
                         face_bit,ibnode,f2ntb, design, force, ia, ja,         &
                         dfdx, small_stencil_nnz01, ib, nbound, bcforce, ndim, &
                         nelem, elem, nbfaceq, face_bitq, f2nqb)

    use info_depr,         only : xmach, Re, Tref, alpha, yaw, twod
    use fluid,             only : gamma, sutherland_constant
    use kinddefs,          only : dp
    use design_types,      only : design_type
    use force_types,       only : force_type
    use forces,            only : cl_id, cd_id, clv_id, cdv_id, clcd_id,       &
                                  fom_id, cmx_id, cmy_id, cmz_id, cmxv_id,     &
                                  cmyv_id, cmzv_id, rotor_thrust_id, cx_id,    &
                                  cy_id, cz_id, cxv_id, cyv_id, czv_id,        &
                                  propeff_id, powerx_id, powery_id, powerz_id
    use refgeom,           only : sref, xmc, ymc, zmc, bref, cref
    use fun3d_constants,   only : my_0, my_1, my_1p5, my_3, my_4, my_2, my_6th
    use element_defs,      only : max_node_per_cell
    use element_types,     only : elem_type
    use custom_transforms, only : thrust_angle

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: nelem, nbfaceq
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,                                intent(in)    :: ib, nbound, ndim
    integer,  dimension(:),                 intent(in)    :: ia, ja
    integer,                                intent(in)    :: nbnode
    integer,                                intent(in)    :: nbfacet
    integer,  dimension(nbfacet),           intent(in)    :: face_bit
    integer,  dimension(nbfaceq),           intent(in)    :: face_bitq
    integer,  dimension(nbnode),            intent(in)    :: ibnode
    integer,  dimension(nbfacet,5),         intent(in)    :: f2ntb
    integer,  dimension(nbfaceq,6),         intent(in)    :: f2nqb
    real(dp), dimension(ndim,nnodes01),     intent(in)    :: qnode
    real(dp), dimension(nnodes01),          intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),          intent(in)    :: amut
    type(design_type),                      intent(in)    :: design
    type(force_type),                       intent(in)    :: force
    type(force_type), dimension(nbound),    intent(in)    :: bcforce
    real(dp), dimension(3,small_stencil_nnz01,design%nfunctions),              &
                                            intent(inout) :: dfdx

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: n, icell, j, ielem, i_local, iface, eqn, i, icol, k
    integer :: bnode1, bnode2, bnode3, bnode4, face_2d
    integer :: nodes_local, node, nn1, nn2, nn3, nn4, m

    integer, dimension(max_node_per_cell) :: c2n_cell, node_map
    integer, dimension(max_node_per_cell) :: ioff

    real(dp) :: sint, cost, width, average, base
    real(dp) :: nx1,nx2,const,factor
    real(dp) :: ny1,ny2
    real(dp) :: nz1,nz2
    real(dp) :: nxd, nyd, nzd
    real(dp) :: nxl, nyl, nzl
    real(dp) :: c43,c23,xmr,pi,conv,cstar
    real(dp) :: x1,y1,z1
    real(dp) :: x2,y2,z2
    real(dp) :: x3,y3,z3
    real(dp) :: x4,y4,z4
    real(dp) :: rmu,u,v,w,rpowerx,rpowery,rpowerz
    real(dp) :: ux,uy,uz,vx,vy,vz,wx,wy,wz
    real(dp) :: xnorm,ynorm,znorm
    real(dp) :: termx,termy,termz
    real(dp) :: term1,term2
    real(dp) :: cd,cdv,cl,clv,clcd,weight,target,power,fom,rotor_thrust,propeff
    real(dp) :: cmx,cmy,cmz,cmxv,cmyv,cmzv
    real(dp) :: cx,cy,cz,cxv,cyv,czv
    real(dp) :: xmid
    real(dp) :: ymid
    real(dp) :: zmid
    real(dp) :: cell_vol, nx, ny, nz
    real(dp) :: xavg, yavg, zavg, qavg, qavg1, qavg2, cell_vol_inv
    real(dp) :: xavg1, yavg1, zavg1
    real(dp) :: xavg2, yavg2, zavg2
    real(dp) :: termx1, termy1, termz1
    real(dp) :: termx2, termy2, termz2

    real(dp), dimension(max_node_per_cell)   :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)   :: t_node, p_node, mu_node
    real(dp), dimension(max_node_per_cell)   :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)   :: x1dx,x2dx,x3dx,x4dx
    real(dp), dimension(max_node_per_cell)   :: y1dy,y2dy,y3dy,y4dy
    real(dp), dimension(max_node_per_cell)   :: z1dz,z2dz,z3dz,z4dz
    real(dp), dimension(max_node_per_cell)   :: xnormdx,xnormdy,xnormdz
    real(dp), dimension(max_node_per_cell)   :: ynormdx,ynormdy,ynormdz
    real(dp), dimension(max_node_per_cell)   :: znormdx,znormdy,znormdz
    real(dp), dimension(max_node_per_cell)   :: cell_voldx,cell_voldy,cell_voldz
    real(dp), dimension(max_node_per_cell)   :: nxdx,nxdy,nxdz
    real(dp), dimension(max_node_per_cell)   :: nydx,nydy,nydz
    real(dp), dimension(max_node_per_cell)   :: nzdx,nzdy,nzdz
    real(dp), dimension(max_node_per_cell)   :: nx1dx,nx1dy,nx1dz
    real(dp), dimension(max_node_per_cell)   :: ny1dx,ny1dy,ny1dz
    real(dp), dimension(max_node_per_cell)   :: nz1dx,nz1dy,nz1dz
    real(dp), dimension(max_node_per_cell)   :: nx2dx,nx2dy,nx2dz
    real(dp), dimension(max_node_per_cell)   :: ny2dx,ny2dy,ny2dz
    real(dp), dimension(max_node_per_cell)   :: nz2dx,nz2dy,nz2dz
    real(dp), dimension(max_node_per_cell)   :: xavgdx,yavgdy,zavgdz
    real(dp), dimension(max_node_per_cell)   :: xavg1dx,yavg1dy,zavg1dz
    real(dp), dimension(max_node_per_cell)   :: xavg2dx,yavg2dy,zavg2dz
    real(dp), dimension(max_node_per_cell)   :: termxdx,termxdy,termxdz
    real(dp), dimension(max_node_per_cell)   :: termydx,termydy,termydz
    real(dp), dimension(max_node_per_cell)   :: termzdx,termzdy,termzdz
    real(dp), dimension(max_node_per_cell)   :: term1dx,term1dy,term1dz
    real(dp), dimension(max_node_per_cell)   :: term2dx,term2dy,term2dz
    real(dp), dimension(max_node_per_cell)   :: termx1dx,termx1dy,termx1dz
    real(dp), dimension(max_node_per_cell)   :: termy1dx,termy1dy,termy1dz
    real(dp), dimension(max_node_per_cell)   :: termz1dx,termz1dy,termz1dz
    real(dp), dimension(max_node_per_cell)   :: termx2dx,termx2dy,termx2dz
    real(dp), dimension(max_node_per_cell)   :: termy2dx,termy2dy,termy2dz
    real(dp), dimension(max_node_per_cell)   :: termz2dx,termz2dy,termz2dz
    real(dp), dimension(max_node_per_cell)   :: cell_vol_invdx
    real(dp), dimension(max_node_per_cell)   :: cell_vol_invdy
    real(dp), dimension(max_node_per_cell)   :: cell_vol_invdz
    real(dp), dimension(max_node_per_cell)   :: uxdx,uxdy,uxdz
    real(dp), dimension(max_node_per_cell)   :: uydx,uydy,uydz
    real(dp), dimension(max_node_per_cell)   :: uzdx,uzdy,uzdz
    real(dp), dimension(max_node_per_cell)   :: vxdx,vxdy,vxdz
    real(dp), dimension(max_node_per_cell)   :: vydx,vydy,vydz
    real(dp), dimension(max_node_per_cell)   :: vzdx,vzdy,vzdz
    real(dp), dimension(max_node_per_cell)   :: wxdx,wxdy,wxdz
    real(dp), dimension(max_node_per_cell)   :: wydx,wydy,wydz
    real(dp), dimension(max_node_per_cell)   :: wzdx,wzdy,wzdz
    real(dp), dimension(max_node_per_cell)   :: forceddx,forceddy,forceddz
    real(dp), dimension(max_node_per_cell)   :: forceldx,forceldy,forceldz
    real(dp), dimension(max_node_per_cell)   :: xmiddx
    real(dp), dimension(max_node_per_cell)   :: ymiddy
    real(dp), dimension(max_node_per_cell)   :: zmiddz
    real(dp), dimension(max_node_per_cell)   :: cxdx,cxdy,cxdz
    real(dp), dimension(max_node_per_cell)   :: cydx,cydy,cydz
    real(dp), dimension(max_node_per_cell)   :: czdx,czdy,czdz
    real(dp), dimension(max_node_per_cell)   :: cmxdx,cmxdy,cmxdz
    real(dp), dimension(max_node_per_cell)   :: cmydx,cmydy,cmydz
    real(dp), dimension(max_node_per_cell)   :: cmzdx,cmzdy,cmzdz
    real(dp), dimension(max_node_per_cell)   :: forced_finaldx
    real(dp), dimension(max_node_per_cell)   :: forced_finaldy
    real(dp), dimension(max_node_per_cell)   :: forced_finaldz
    real(dp), dimension(max_node_per_cell)   :: forcel_finaldx
    real(dp), dimension(max_node_per_cell)   :: forcel_finaldy
    real(dp), dimension(max_node_per_cell)   :: forcel_finaldz
    real(dp), dimension(max_node_per_cell)   :: cx_finaldx
    real(dp), dimension(max_node_per_cell)   :: cx_finaldy
    real(dp), dimension(max_node_per_cell)   :: cx_finaldz
    real(dp), dimension(max_node_per_cell)   :: cy_finaldx
    real(dp), dimension(max_node_per_cell)   :: cy_finaldy
    real(dp), dimension(max_node_per_cell)   :: cy_finaldz
    real(dp), dimension(max_node_per_cell)   :: cz_finaldx
    real(dp), dimension(max_node_per_cell)   :: cz_finaldy
    real(dp), dimension(max_node_per_cell)   :: cz_finaldz
    real(dp), dimension(max_node_per_cell)   :: cmx_finaldx
    real(dp), dimension(max_node_per_cell)   :: cmx_finaldy
    real(dp), dimension(max_node_per_cell)   :: cmx_finaldz
    real(dp), dimension(max_node_per_cell)   :: cmy_finaldx
    real(dp), dimension(max_node_per_cell)   :: cmy_finaldy
    real(dp), dimension(max_node_per_cell)   :: cmy_finaldz
    real(dp), dimension(max_node_per_cell)   :: cmz_finaldx
    real(dp), dimension(max_node_per_cell)   :: cmz_finaldy
    real(dp), dimension(max_node_per_cell)   :: cmz_finaldz
    real(dp), dimension(max_node_per_cell)   :: powerxdx
    real(dp), dimension(max_node_per_cell)   :: powerxdy
    real(dp), dimension(max_node_per_cell)   :: powerxdz
    real(dp), dimension(max_node_per_cell)   :: powerydx
    real(dp), dimension(max_node_per_cell)   :: powerydy
    real(dp), dimension(max_node_per_cell)   :: powerydz
    real(dp), dimension(max_node_per_cell)   :: powerzdx
    real(dp), dimension(max_node_per_cell)   :: powerzdy
    real(dp), dimension(max_node_per_cell)   :: powerzdz
    real(dp), dimension(max_node_per_cell)   :: powerx_finaldx
    real(dp), dimension(max_node_per_cell)   :: powerx_finaldy
    real(dp), dimension(max_node_per_cell)   :: powerx_finaldz
    real(dp), dimension(max_node_per_cell)   :: powery_finaldx
    real(dp), dimension(max_node_per_cell)   :: powery_finaldy
    real(dp), dimension(max_node_per_cell)   :: powery_finaldz
    real(dp), dimension(max_node_per_cell)   :: powerz_finaldx
    real(dp), dimension(max_node_per_cell)   :: powerz_finaldy
    real(dp), dimension(max_node_per_cell)   :: powerz_finaldz
    real(dp), dimension(4,max_node_per_cell) :: q_node
    real(dp), dimension(ndim)                :: gradx_cell, grady_cell
    real(dp), dimension(ndim)                :: gradz_cell
    real(dp), dimension(ndim)                :: gradx_cell_new, grady_cell_new
    real(dp), dimension(ndim)                :: gradz_cell_new
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdz

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

  continue

    const  = 0.0_dp
    rotor_thrust = 0.0_dp
    fom    = 0.0_dp
    propeff= 0.0_dp
    clcd   = 0.0_dp

    !  Some constants

    c43 = 4.0_dp/3.0_dp
    c23 = 2.0_dp/3.0_dp
    xmr = 1.0_dp/ xmach / Re
    pi = acos(-1.0_dp)
    conv = 180.0_dp / pi
    cstar = sutherland_constant/Tref

      fcn_loop : do j = 1, design%nfunctions

          tria_faces : do n = 1, nbfacet

            local_tria : if (face_bit(n) == 1) then

              bnode1 = ibnode(f2ntb(n,1))
              bnode2 = ibnode(f2ntb(n,2))
              bnode3 = ibnode(f2ntb(n,3))

              icell = f2ntb(n,4)
              ielem = f2ntb(n,5)

! set some loop indicies and local mapping arrays depending on whether
! we are doing a 2D case or a 3D case

              node_map(:) = 0

              if (twod) then

                face_2d = elem(ielem)%face_2d

                nodes_local = 3
                if (elem(ielem)%local_f2n(face_2d,1) /=                        &
                    elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

                do i=1,nodes_local
                  node_map(i) = elem(ielem)%local_f2n(face_2d,i)
                end do

              else

                nodes_local = elem(ielem)%node_per_cell

                do i=1,nodes_local
                  node_map(i) = i
                end do

              end if

! copy c2n and local_f2n arrays from the derived type so we  minimize
! references to derived types inside loops as much as possible

              do node = 1, elem(ielem)%node_per_cell
                c2n_cell(node) = elem(ielem)%c2n(node,icell)
              end do

! compute cell averaged viscosity by looping over the nodes in the
! element and gathering their contributions, then average at the end

              rmu = 0.0_dp

              x_node(:)   = 0.0_dp
              y_node(:)   = 0.0_dp
              z_node(:)   = 0.0_dp
              u_node(:)   = 0.0_dp
              v_node(:)   = 0.0_dp
              w_node(:)   = 0.0_dp
              p_node(:)   = 0.0_dp
              t_node(:)   = 0.0_dp
              mu_node(:)  = 0.0_dp
              q_node(:,:) = 0.0_dp

              node_loop1 : do i_local = 1, nodes_local

                i = node_map(i_local)

                node = c2n_cell(i)

                x_node(i) = x(node)
                y_node(i) = y(node)
                z_node(i) = z(node)

                u_node(i) = qnode(2,node)
                v_node(i) = qnode(3,node)
                w_node(i) = qnode(4,node)
                p_node(i) = qnode(5,node)
                t_node(i) = gamma*p_node(i)/qnode(1,node)

                mu_node(i) = viscosity_law( cstar, t_node(i) ) + amut(node)

                rmu = rmu + mu_node(i)

              end do node_loop1

! now compute cell average by dividing by the number of nodes
! that contributed

              rmu = rmu / real(nodes_local, dp)

              u = (qnode(2,bnode1) + qnode(2,bnode2) + qnode(2,bnode3)) / 3.0_dp
              v = (qnode(3,bnode1) + qnode(3,bnode2) + qnode(3,bnode3)) / 3.0_dp
              w = (qnode(4,bnode1) + qnode(4,bnode2) + qnode(4,bnode3)) / 3.0_dp

! now we get this boundary face's normal

              x1dx(:) = 0.0_dp
              x2dx(:) = 0.0_dp
              x3dx(:) = 0.0_dp
              y1dy(:) = 0.0_dp
              y2dy(:) = 0.0_dp
              y3dy(:) = 0.0_dp
              z1dz(:) = 0.0_dp
              z2dz(:) = 0.0_dp
              z3dz(:) = 0.0_dp

              x1 = x(bnode1)
              y1 = y(bnode1)
              z1 = z(bnode1)

              x2 = x(bnode2)
              y2 = y(bnode2)
              z2 = z(bnode2)

              x3 = x(bnode3)
              y3 = y(bnode3)
              z3 = z(bnode3)

              do node = 1, elem(ielem)%node_per_cell
                if ( bnode1 == c2n_cell(node) ) then
                  x1dx(node) = 1.0_dp
                  y1dy(node) = 1.0_dp
                  z1dz(node) = 1.0_dp
                endif
                if ( bnode2 == c2n_cell(node) ) then
                  x2dx(node) = 1.0_dp
                  y2dy(node) = 1.0_dp
                  z2dz(node) = 1.0_dp
                endif
                if ( bnode3 == c2n_cell(node) ) then
                  x3dx(node) = 1.0_dp
                  y3dy(node) = 1.0_dp
                  z3dz(node) = 1.0_dp
                endif
              end do

! - sign for outward facing normal

              xnorm = -0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
                xnormdx(:) =  0.0_dp
                xnormdy(:) = -0.5_dp*( (y2dy(:)-y1dy(:))*(z3-z1)               &
                                   - (z2-z1)*(y3dy(:)-y1dy(:)) )
                xnormdz(:) = -0.5_dp*( (y2-y1)*(z3dz(:)-z1dz(:))               &
                                   - (z2dz(:)-z1dz(:))*(y3-y1) )

              ynorm = -0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
                ynormdx(:) = -0.5_dp*( (z2-z1)*(x3dx(:)-x1dx(:))               &
                                   - (x2dx(:)-x1dx(:))*(z3-z1) )
                ynormdy(:) =  0.0_dp
                ynormdz(:) = -0.5_dp*( (z2dz(:)-z1dz(:))*(x3-x1)               &
                                   - (x2-x1)*(z3dz(:)-z1dz(:)) )

              znorm = -0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )
                znormdx(:) = -0.5_dp*( (x2dx(:)-x1dx(:))*(y3-y1)               &
                                   - (y2-y1)*(x3dx(:)-x1dx(:)) )
                znormdy(:) = -0.5_dp*( (x2-x1)*(y3dy(:)-y1dy(:))               &
                                   - (y2dy(:)-y1dy(:))*(x3-x1) )
                znormdz(:) =  0.0_dp

              q_node(2,:) = u_node(:)
              q_node(3,:) = v_node(:)
              q_node(4,:) = w_node(:)

              cell_vol = 0.0_dp
                cell_voldx(:) = 0.0_dp
                cell_voldy(:) = 0.0_dp
                cell_voldz(:) = 0.0_dp

              gradx_cell(:) = my_0
                gradx_celldx(:,:) = my_0
                gradx_celldy(:,:) = my_0
                gradx_celldz(:,:) = my_0
              grady_cell(:) = my_0
                grady_celldx(:,:) = my_0
                grady_celldy(:,:) = my_0
                grady_celldz(:,:) = my_0
              gradz_cell(:) = my_0
                gradz_celldx(:,:) = my_0
                gradz_celldy(:,:) = my_0
                gradz_celldz(:,:) = my_0

              threed_faces : do iface = 1, elem(ielem)%face_per_cell

                nn1 = elem(ielem)%local_f2n(iface,1)
                nn2 = elem(ielem)%local_f2n(iface,2)
                nn3 = elem(ielem)%local_f2n(iface,3)
                nn4 = elem(ielem)%local_f2n(iface,4)

                nxdx = 0.0_dp
                nxdy = 0.0_dp
                nxdz = 0.0_dp
                nx1dx = 0.0_dp
                nx1dy = 0.0_dp
                nx1dz = 0.0_dp
                nx2dx = 0.0_dp
                nx2dy = 0.0_dp
                nx2dz = 0.0_dp

                nydx = 0.0_dp
                nydy = 0.0_dp
                nydz = 0.0_dp
                ny1dx = 0.0_dp
                ny1dy = 0.0_dp
                ny1dz = 0.0_dp
                ny2dx = 0.0_dp
                ny2dy = 0.0_dp
                ny2dz = 0.0_dp

                nzdx = 0.0_dp
                nzdy = 0.0_dp
                nzdz = 0.0_dp
                nz1dx = 0.0_dp
                nz1dy = 0.0_dp
                nz1dz = 0.0_dp
                nz2dx = 0.0_dp
                nz2dy = 0.0_dp
                nz2dz = 0.0_dp

                if (nn4 == nn1) then

! triangular faces of the cell

! face normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1)) &
                     - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nxdy(nn1) = -(z_node(nn3) - z_node(nn1))                   &
                               + (z_node(nn2) - z_node(nn1))
                    nxdy(nn2) =   z_node(nn3) - z_node(nn1)
                    nxdy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nxdz(nn1) = -(y_node(nn2) - y_node(nn1))                   &
                               + (y_node(nn3) - y_node(nn1))
                    nxdz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nxdz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1)) &
                     - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    nydx(nn1) = -(z_node(nn2) - z_node(nn1))                   &
                               + (z_node(nn3) - z_node(nn1))
                    nydx(nn2) = -(z_node(nn3) - z_node(nn1))
                    nydx(nn3) =  (z_node(nn2) - z_node(nn1))

                    nydz(nn1) = -(x_node(nn3) - x_node(nn1))                   &
                               + (x_node(nn2) - x_node(nn1))
                    nydz(nn2) =  (x_node(nn3) - x_node(nn1))
                    nydz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1)) &
                     - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nzdx(nn1) = -(y_node(nn3) - y_node(nn1))                   &
                               + (y_node(nn2) - y_node(nn1))
                    nzdx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nzdx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nzdy(nn1) = -(x_node(nn2) - x_node(nn1))                   &
                               + (x_node(nn3) - x_node(nn1))
                    nzdy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nzdy(nn3) =  (x_node(nn2) - x_node(nn1))

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavgdx = 0.0_dp
                  yavgdy = 0.0_dp
                  zavgdz = 0.0_dp

                  xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavgdx(nn1) = 1.0_dp
                    xavgdx(nn2) = 1.0_dp
                    xavgdx(nn3) = 1.0_dp

                  yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavgdy(nn1) = 1.0_dp
                    yavgdy(nn2) = 1.0_dp
                    yavgdy(nn3) = 1.0_dp

                  zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavgdz(nn1) = 1.0_dp
                    zavgdz(nn2) = 1.0_dp
                    zavgdz(nn3) = 1.0_dp

! cell volume contributions

                  cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:)              &
                                  + yavg*nydx(:) + zavg*nzdx(:) + xavgdx(:)*nx &
                                                                     )*my_18th
                    cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:)              &
                                  + yavg*nydy(:) + zavg*nzdy(:) + yavgdy(:)*ny &
                                                                     )*my_18th
                    cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:)              &
                                  + yavg*nydz(:) + zavg*nzdz(:) + zavgdz(:)*nz &
                                                                     )*my_18th

                  termx = nx*my_6th
                    termxdx(:) = nxdx(:)*my_6th
                    termxdy(:) = nxdy(:)*my_6th
                    termxdz(:) = nxdz(:)*my_6th
                  termy = ny*my_6th
                    termydx(:) = nydx(:)*my_6th
                    termydy(:) = nydy(:)*my_6th
                    termydz(:) = nydz(:)*my_6th
                  termz = nz*my_6th
                    termzdx(:) = nzdx(:)*my_6th
                    termzdy(:) = nzdy(:)*my_6th
                    termzdz(:) = nzdz(:)*my_6th

! gradient contributions

                  do eqn = 2, 4
                    qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
                      gradx_celldx(eqn,:)= gradx_celldx(eqn,:) + termxdx(:)*qavg
                      gradx_celldy(eqn,:)= gradx_celldy(eqn,:) + termxdy(:)*qavg
                      gradx_celldz(eqn,:)= gradx_celldz(eqn,:) + termxdz(:)*qavg
                    grady_cell(eqn) = grady_cell(eqn) + termy*qavg
                      grady_celldx(eqn,:)= grady_celldx(eqn,:) + termydx(:)*qavg
                      grady_celldy(eqn,:)= grady_celldy(eqn,:) + termydy(:)*qavg
                      grady_celldz(eqn,:)= grady_celldz(eqn,:) + termydz(:)*qavg
                    gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
                      gradz_celldx(eqn,:)= gradz_celldx(eqn,:) + termzdx(:)*qavg
                      gradz_celldy(eqn,:)= gradz_celldy(eqn,:) + termzdy(:)*qavg
                      gradz_celldz(eqn,:)= gradz_celldz(eqn,:) + termzdz(:)*qavg
                  end do

                else

! quadrilateral faces of the cell

! break face up into triangles 1-2-3 and 1-3-4 and add together

! triangle 1: 1-2-3

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg1dx = 0.0_dp
                  yavg1dy = 0.0_dp
                  zavg1dz = 0.0_dp

                  xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavg1dx(nn1) = 1.0_dp
                    xavg1dx(nn2) = 1.0_dp
                    xavg1dx(nn3) = 1.0_dp
                  yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavg1dy(nn1) = 1.0_dp
                    yavg1dy(nn2) = 1.0_dp
                    yavg1dy(nn3) = 1.0_dp
                  zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavg1dz(nn1) = 1.0_dp
                    zavg1dz(nn2) = 1.0_dp
                    zavg1dz(nn3) = 1.0_dp

! triangle 1 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))&
                      - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nx1dy(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn2) - z_node(nn1))
                    nx1dy(nn2) =   z_node(nn3) - z_node(nn1)
                    nx1dy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nx1dz(nn1) = -(y_node(nn2) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nx1dz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nx1dz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))&
                      - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    ny1dx(nn1) = -(z_node(nn2) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    ny1dx(nn2) = -(z_node(nn3) - z_node(nn1))
                    ny1dx(nn3) =  (z_node(nn2) - z_node(nn1))

                    ny1dz(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn2) - x_node(nn1))
                    ny1dz(nn2) =  (x_node(nn3) - x_node(nn1))
                    ny1dz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))&
                      - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nz1dx(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn2) - y_node(nn1))
                    nz1dx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nz1dx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nz1dy(nn1) = -(x_node(nn2) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    nz1dy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nz1dy(nn3) =  (x_node(nn2) - x_node(nn1))

                  term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
                    term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:)               &
                               + zavg1*nz1dx(:) + xavg1dx(:)*nx1
                    term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:)               &
                               + zavg1*nz1dy(:) + yavg1dy(:)*ny1
                    term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:)               &
                               + zavg1*nz1dz(:) + zavg1dz(:)*nz1

! triangle 2: 1-3-4

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg2dx = 0.0_dp
                  yavg2dy = 0.0_dp
                  zavg2dz = 0.0_dp

                  xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
                    xavg2dx(nn1) = 1.0_dp
                    xavg2dx(nn3) = 1.0_dp
                    xavg2dx(nn4) = 1.0_dp
                  yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
                    yavg2dy(nn1) = 1.0_dp
                    yavg2dy(nn3) = 1.0_dp
                    yavg2dy(nn4) = 1.0_dp
                  zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
                    zavg2dz(nn1) = 1.0_dp
                    zavg2dz(nn3) = 1.0_dp
                    zavg2dz(nn4) = 1.0_dp

! triangle 2 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))&
                      - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

                    nx2dy(nn1) = -(z_node(nn4) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    nx2dy(nn3) =   z_node(nn4) - z_node(nn1)
                    nx2dy(nn4) = -(z_node(nn3) - z_node(nn1))

                    nx2dz(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn4) - y_node(nn1))
                    nx2dz(nn3) = -(y_node(nn4) - y_node(nn1))
                    nx2dz(nn4) =  (y_node(nn3) - y_node(nn1))

                  ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))&
                      - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

                    ny2dx(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn4) - z_node(nn1))
                    ny2dx(nn3) = -(z_node(nn4) - z_node(nn1))
                    ny2dx(nn4) =  (z_node(nn3) - z_node(nn1))

                    ny2dz(nn1) = -(x_node(nn4) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    ny2dz(nn3) =  (x_node(nn4) - x_node(nn1))
                    ny2dz(nn4) = -(x_node(nn3) - x_node(nn1))

                  nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))&
                      - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

                    nz2dx(nn1) = -(y_node(nn4) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nz2dx(nn3) =  (y_node(nn4) - y_node(nn1))
                    nz2dx(nn4) = -(y_node(nn3) - y_node(nn1))

                    nz2dy(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn4) - x_node(nn1))
                    nz2dy(nn3) = -(x_node(nn4) - x_node(nn1))
                    nz2dy(nn4) =  (x_node(nn3) - x_node(nn1))

                  term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
                    term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:)               &
                               + zavg2*nz2dx(:) + xavg2dx(:)*nx2
                    term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:)               &
                               + zavg2*nz2dy(:) + yavg2dy(:)*ny2
                    term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:)               &
                               + zavg2*nz2dz(:) + zavg2dz(:)*nz2

! cell volume contributions

                  cell_vol = cell_vol + (term1 + term2)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))  &
                                                                        *my_18th
                    cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))  &
                                                                        *my_18th
                    cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))  &
                                                                        *my_18th

! gradient contributions

                  termx1 = nx1*my_6th
                    termx1dx(:) = nx1dx(:)*my_6th
                    termx1dy(:) = nx1dy(:)*my_6th
                    termx1dz(:) = nx1dz(:)*my_6th
                  termy1 = ny1*my_6th
                    termy1dx(:) = ny1dx(:)*my_6th
                    termy1dy(:) = ny1dy(:)*my_6th
                    termy1dz(:) = ny1dz(:)*my_6th
                  termz1 = nz1*my_6th
                    termz1dx(:) = nz1dx(:)*my_6th
                    termz1dy(:) = nz1dy(:)*my_6th
                    termz1dz(:) = nz1dz(:)*my_6th

                  termx2 = nx2*my_6th
                    termx2dx(:) = nx2dx(:)*my_6th
                    termx2dy(:) = nx2dy(:)*my_6th
                    termx2dz(:) = nx2dz(:)*my_6th
                  termy2 = ny2*my_6th
                    termy2dx(:) = ny2dx(:)*my_6th
                    termy2dy(:) = ny2dy(:)*my_6th
                    termy2dz(:) = ny2dz(:)*my_6th
                  termz2 = nz2*my_6th
                    termz2dx(:) = nz2dx(:)*my_6th
                    termz2dy(:) = nz2dy(:)*my_6th
                    termz2dz(:) = nz2dz(:)*my_6th

                  do eqn = 2, 4
                    qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)
                    gradx_cell(eqn)=gradx_cell(eqn)+ termx1*qavg1 + termx2*qavg2
                      gradx_celldx(eqn,:) = gradx_celldx(eqn,:)                &
                                         + termx1dx(:)*qavg1 + termx2dx(:)*qavg2
                      gradx_celldy(eqn,:) = gradx_celldy(eqn,:)                &
                                         + termx1dy(:)*qavg1 + termx2dy(:)*qavg2
                      gradx_celldz(eqn,:) = gradx_celldz(eqn,:)                &
                                         + termx1dz(:)*qavg1 + termx2dz(:)*qavg2
                    grady_cell(eqn)=grady_cell(eqn)+ termy1*qavg1 + termy2*qavg2
                      grady_celldx(eqn,:) = grady_celldx(eqn,:)                &
                                         + termy1dx(:)*qavg1 + termy2dx(:)*qavg2
                      grady_celldy(eqn,:) = grady_celldy(eqn,:)                &
                                         + termy1dy(:)*qavg1 + termy2dy(:)*qavg2
                      grady_celldz(eqn,:) = grady_celldz(eqn,:)                &
                                         + termy1dz(:)*qavg1 + termy2dz(:)*qavg2
                    gradz_cell(eqn)=gradz_cell(eqn)+ termz1*qavg1 + termz2*qavg2
                      gradz_celldx(eqn,:) = gradz_celldx(eqn,:)                &
                                         + termz1dx(:)*qavg1 + termz2dx(:)*qavg2
                      gradz_celldy(eqn,:) = gradz_celldy(eqn,:)                &
                                         + termz1dy(:)*qavg1 + termz2dy(:)*qavg2
                      gradz_celldz(eqn,:) = gradz_celldz(eqn,:)                &
                                         + termz1dz(:)*qavg1 + termz2dz(:)*qavg2
                  end do

                end if

              end do threed_faces

! need to divide the gradient sums by the grid cell volume to give the
! cell-average Green-Gauss gradients

              cell_vol_inv = my_1/cell_vol
                cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
                cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
                cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

              gradx_cell_new(2:4) = gradx_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)      &
                                                + cell_vol_inv*gradx_celldx(k,:)
                  gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)      &
                                                + cell_vol_inv*gradx_celldy(k,:)
                  gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)      &
                                                + cell_vol_inv*gradx_celldz(k,:)
                end do
              grady_cell_new(2:4) = grady_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)      &
                                                + cell_vol_inv*grady_celldx(k,:)
                  grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)      &
                                                + cell_vol_inv*grady_celldy(k,:)
                  grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)      &
                                                + cell_vol_inv*grady_celldz(k,:)
                end do
              gradz_cell_new(2:4) = gradz_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)      &
                                                + cell_vol_inv*gradz_celldx(k,:)
                  gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)      &
                                                + cell_vol_inv*gradz_celldy(k,:)
                  gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)      &
                                                + cell_vol_inv*gradz_celldz(k,:)
                end do

              ux = gradx_cell_new(2)
                uxdx(:) = gradx_cell_newdx(2,:)
                uxdy(:) = gradx_cell_newdy(2,:)
                uxdz(:) = gradx_cell_newdz(2,:)
              vx = gradx_cell_new(3)
                vxdx(:) = gradx_cell_newdx(3,:)
                vxdy(:) = gradx_cell_newdy(3,:)
                vxdz(:) = gradx_cell_newdz(3,:)
              wx = gradx_cell_new(4)
                wxdx(:) = gradx_cell_newdx(4,:)
                wxdy(:) = gradx_cell_newdy(4,:)
                wxdz(:) = gradx_cell_newdz(4,:)

              uy = grady_cell_new(2)
                uydx(:) = grady_cell_newdx(2,:)
                uydy(:) = grady_cell_newdy(2,:)
                uydz(:) = grady_cell_newdz(2,:)
              vy = grady_cell_new(3)
                vydx(:) = grady_cell_newdx(3,:)
                vydy(:) = grady_cell_newdy(3,:)
                vydz(:) = grady_cell_newdz(3,:)
              wy = grady_cell_new(4)
                wydx(:) = grady_cell_newdx(4,:)
                wydy(:) = grady_cell_newdy(4,:)
                wydz(:) = grady_cell_newdz(4,:)

              uz = gradz_cell_new(2)
                uzdx(:) = gradz_cell_newdx(2,:)
                uzdy(:) = gradz_cell_newdy(2,:)
                uzdz(:) = gradz_cell_newdz(2,:)
              vz = gradz_cell_new(3)
                vzdx(:) = gradz_cell_newdx(3,:)
                vzdy(:) = gradz_cell_newdy(3,:)
                vzdz(:) = gradz_cell_newdz(3,:)
              wz = gradz_cell_new(4)
                wzdx(:) = gradz_cell_newdx(4,:)
                wzdy(:) = gradz_cell_newdy(4,:)
                wzdz(:) = gradz_cell_newdz(4,:)

! now compute components of stress vector acting on the face

              termx = my_2*xmr*rmu*(xnorm*(c43*ux - c23*(vy + wz)) +           &
                                    ynorm*(uy + vx)                +           &
                                    znorm*(uz + wx))
                termxdx(:) = my_2*xmr*rmu*((xnorm*(c43*uxdx(:) - c23*(vydx(:) +&
                             wzdx(:))) + ynorm*(uydx(:) + vxdx(:)) +           &
                             znorm*(uzdx(:) + wxdx(:))) + (xnormdx(:)*(c43*ux -&
                             c23*(vy + wz)) + ynormdx(:)*(uy + vx) +           &
                             znormdx(:)*(uz + wx)  ) )
                termxdy(:) = my_2*xmr*rmu*((xnorm*(c43*uxdy(:) - c23*(vydy(:) +&
                             wzdy(:))) + ynorm*(uydy(:) + vxdy(:)) +           &
                             znorm*(uzdy(:) + wxdy(:))) + (xnormdy(:)*(c43*ux -&
                             c23*(vy + wz)) + ynormdy(:)*(uy + vx) +           &
                             znormdy(:)*(uz + wx)  ) )
                termxdz(:) = my_2*xmr*rmu*((xnorm*(c43*uxdz(:) - c23*(vydz(:) +&
                             wzdz(:))) + ynorm*(uydz(:) + vxdz(:)) +           &
                             znorm*(uzdz(:) + wxdz(:))) + (xnormdz(:)*(c43*ux -&
                             c23*(vy + wz)) + ynormdz(:)*(uy + vx) +           &
                             znormdz(:)*(uz + wx)  ) )

              termy = my_2*xmr*rmu*(xnorm*(uy + vx)                +           &
                                    ynorm*(c43*vy - c23*(ux + wz)) +           &
                                    znorm*(vz + wy))
                termydx(:) = my_2*xmr*rmu*( (xnorm*(uydx(:) + vxdx(:)) +       &
                             ynorm*(c43*vydx(:) - c23*(uxdx(:) + wzdx(:))) +   &
                             znorm*(vzdx(:) + wydx(:))) + xnormdx(:)*(uy + vx) &
                             + ynormdx(:)*(c43*vy - c23*(ux + wz))             &
                             + znormdx(:)*(vz + wy) )
                termydy(:) = my_2*xmr*rmu*( (xnorm*(uydy(:) + vxdy(:)) +       &
                             ynorm*(c43*vydy(:) - c23*(uxdy(:) + wzdy(:))) +   &
                             znorm*(vzdy(:) + wydy(:))) + xnormdy(:)*(uy + vx) &
                             + ynormdy(:)*(c43*vy - c23*(ux + wz))             &
                             + znormdy(:)*(vz + wy) )
                termydz(:) = my_2*xmr*rmu*( (xnorm*(uydz(:) + vxdz(:)) +       &
                             ynorm*(c43*vydz(:) - c23*(uxdz(:) + wzdz(:))) +   &
                             znorm*(vzdz(:) + wydz(:))) + xnormdz(:)*(uy + vx) &
                             + ynormdz(:)*(c43*vy - c23*(ux + wz))             &
                             + znormdz(:)*(vz + wy) )

              termz = my_2*xmr*rmu*(xnorm*(uz + wx)                +           &
                                    ynorm*(vz + wy)                +           &
                                    znorm*(c43*wz - c23*(ux + vy)))
                termzdx(:) = my_2*xmr*rmu*( (xnorm*(uzdx(:) + wxdx(:))         &
                             + ynorm*(vzdx(:) + wydx(:)) + znorm*(c43*wzdx(:)  &
                             - c23*(uxdx(:) + vydx(:)))) + xnormdx(:)*(uz + wx)&
                             + ynormdx(:)*(vz + wy) + znormdx(:)*(c43*wz       &
                             - c23*(ux + vy)) )
                termzdy(:) = my_2*xmr*rmu*( (xnorm*(uzdy(:) + wxdy(:))         &
                             + ynorm*(vzdy(:) + wydy(:)) + znorm*(c43*wzdy(:)  &
                             - c23*(uxdy(:) + vydy(:)))) + xnormdy(:)*(uz + wx)&
                             + ynormdy(:)*(vz + wy) + znormdy(:)*(c43*wz       &
                             - c23*(ux + vy)) )
                termzdz(:) = my_2*xmr*rmu*( (xnorm*(uzdz(:) + wxdz(:))         &
                             + ynorm*(vzdz(:) + wydz(:)) + znorm*(c43*wzdz(:)  &
                             - c23*(uxdz(:) + vydz(:)))) + xnormdz(:)*(uz + wx)&
                             + ynormdz(:)*(vz + wy) + znormdz(:)*(c43*wz       &
                             - c23*(ux + vy)) )

! now dot the stress vector acting on the surface face with
! a unit vector in the drag (lift) direction.  This is the
! magnitude of the friction force acting on the face in the
! drag (lift) direction

! find unit vectors in drag and lift directions

              nxd =   cos(alpha/conv) * cos(yaw/conv)
              nyd = - sin(yaw/conv)
              nzd =   sin(alpha/conv) * cos(yaw/conv)

              nxl = - sin(alpha/conv)
              nyl =   my_0
              nzl =   cos(alpha/conv)

! now do the dot product to get the force in the drag (lift) direction

!             forced = - (termx*nxd + termy*nyd + termz*nzd)
                forceddx(:) =-(termxdx(:)*nxd + termydx(:)*nyd + termzdx(:)*nzd)
                forceddy(:) =-(termxdy(:)*nxd + termydy(:)*nyd + termzdy(:)*nzd)
                forceddz(:) =-(termxdz(:)*nxd + termydz(:)*nyd + termzdz(:)*nzd)

!             forcel = - (termx*nxl + termy*nyl + termz*nzl)
                forceldx(:) =-(termxdx(:)*nxl + termydx(:)*nyl + termzdx(:)*nzl)
                forceldy(:) =-(termxdy(:)*nxl + termydy(:)*nyl + termzdy(:)*nzl)
                forceldz(:) =-(termxdz(:)*nxl + termydz(:)*nyl + termzdz(:)*nzl)

              xmid = (x1 + x2 + x3)/my_3
                xmiddx(:) = (x1dx(:) + x2dx(:) + x3dx(:))/my_3
!               xmiddy(:) = 0.0_dp
!               xmiddz(:) = 0.0_dp

              ymid = (y1 + y2 + y3)/my_3
!               ymiddx(:) = 0.0_dp
                ymiddy(:) = (y1dy(:) + y2dy(:) + y3dy(:))/my_3
!               ymiddz(:) = 0.0_dp

              zmid = (z1 + z2 + z3)/my_3
!               zmiddx(:) = 0.0_dp
!               zmiddy(:) = 0.0_dp
                zmiddz(:) = (z1dz(:) + z2dz(:) + z3dz(:))/my_3

              cxdx(:) = -termxdx(:)
              cxdy(:) = -termxdy(:)
              cxdz(:) = -termxdz(:)
              cydx(:) = -termydx(:)
              cydy(:) = -termydy(:)
              cydz(:) = -termydz(:)
              czdx(:) = -termzdx(:)
              czdy(:) = -termzdy(:)
              czdz(:) = -termzdz(:)

              powerxdx(:) = termxdx(:)*u
              powerxdy(:) = termxdy(:)*u
              powerxdz(:) = termxdz(:)*u

              powerydx(:) = termydx(:)*v
              powerydy(:) = termydy(:)*v
              powerydz(:) = termydz(:)*v

              powerzdx(:) = termzdx(:)*w
              powerzdy(:) = termzdy(:)*w
              powerzdz(:) = termzdz(:)*w

              cmxdx(:) = - termzdx(:)*(ymid-ymc) + termydx(:)*(zmid-zmc)
              cmxdy(:) = - termzdy(:)*(ymid-ymc) + termydy(:)*(zmid-zmc)       &
                         - termz*ymiddy(:)
              cmxdz(:) = - termzdz(:)*(ymid-ymc) + termydz(:)*(zmid-zmc)       &
                         + termy*zmiddz(:)

              cmydx(:) =   termzdx(:)*(xmid-xmc) - termxdx(:)*(zmid-zmc)       &
                         + termz*xmiddx(:)
              cmydy(:) =   termzdy(:)*(xmid-xmc) - termxdy(:)*(zmid-zmc)
              cmydz(:) =   termzdz(:)*(xmid-xmc) - termxdz(:)*(zmid-zmc)       &
                         - termx*zmiddz(:)

              cmzdx(:) = - termydx(:)*(xmid-xmc) + termxdx(:)*(ymid-ymc)       &
                         - termy*xmiddx(:)
              cmzdy(:) = - termydy(:)*(xmid-xmc) + termxdy(:)*(ymid-ymc)       &
                         + termx*ymiddy(:)
              cmzdz(:) = - termydz(:)*(xmid-xmc) + termxdz(:)*(ymid-ymc)

!             force%cdv = force%cdv + forced
!             force%clv = force%clv + forcel

!             force%cxv = force%cxv - termx
!             force%cyv = force%cyv - termy
!             force%czv = force%czv - termz

!             force%cmxv = force%cmxv - termz*(ymid-ymc) + termy*(zmid-zmc)
!             force%cmyv = force%cmyv + termz*(xmid-xmc) - termx*(zmid-zmc)
!             force%cmzv = force%cmzv - termy*(xmid-xmc) + termx*(ymid-ymc)

              component_loop : do k = 1, design%function_data(j)%ncomponents

                if ( design%function_data(j)%component_data(k)%boundary_id     &
                     == 0 ) then
                  if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
                  cd   = force%cd
                  cdv  = force%cdv
                  cl   = force%cl
                  clv  = force%clv
                  cx   = force%cx
                  cy   = force%cy
                  cz   = force%cz
                  cxv  = force%cxv
                  cyv  = force%cyv
                  czv  = force%czv
                  cmx  = force%cmx
                  cmxv = force%cmxv
                  cmy  = force%cmy
                  cmyv = force%cmyv
                  cmz  = force%cmz
                  cmzv = force%cmzv
                  clcd = force%clcd
                  fom  = force%fom
                  propeff= force%propeff
                  rotor_thrust = force%rotor_thrust
                  rpowerx = force%powerx
                  rpowery = force%powery
                  rpowerz = force%powerz
                else if (design%function_data(j)%component_data(k)%boundary_id &
                         == ib ) then
                  cd   = bcforce(ib)%cd
                  cdv  = bcforce(ib)%cdv
                  cl   = bcforce(ib)%cl
                  clv  = bcforce(ib)%clv
                  cx   = bcforce(ib)%cx
                  cy   = bcforce(ib)%cy
                  cz   = bcforce(ib)%cz
                  cxv  = bcforce(ib)%cxv
                  cyv  = bcforce(ib)%cyv
                  czv  = bcforce(ib)%czv
                  cmx  = bcforce(ib)%cmx
                  cmxv = bcforce(ib)%cmxv
                  cmy  = bcforce(ib)%cmy
                  cmyv = bcforce(ib)%cmyv
                  cmz  = bcforce(ib)%cmz
                  cmzv = bcforce(ib)%cmzv
                  rpowerx = bcforce(ib)%powerx
                  rpowery = bcforce(ib)%powery
                  rpowerz = bcforce(ib)%powerz
                else
                  cycle component_loop
                endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxv_id )
                if (.not.design%function_data(j)%averaging) base = cmxv-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyv_id )
                if (.not.design%function_data(j)%averaging) base = cmyv-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzv_id )
                if (.not.design%function_data(j)%averaging) base = cmzv-target
                const = my_1/sref/bref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxv_id )
                if (.not.design%function_data(j)%averaging) base = cxv-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyv_id )
                if (.not.design%function_data(j)%averaging) base = cyv-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czv_id )
                if (.not.design%function_data(j)%averaging) base = czv-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                forcel_finaldx(:) = forceldx(:)*factor
                forcel_finaldy(:) = forceldy(:)*factor
                forcel_finaldz(:) = forceldz(:)*factor

                forced_finaldx(:) = forceddx(:)*factor
                forced_finaldy(:) = forceddy(:)*factor
                forced_finaldz(:) = forceddz(:)*factor

                cx_finaldx(:) = cxdx(:)*factor
                cx_finaldy(:) = cxdy(:)*factor
                cx_finaldz(:) = cxdz(:)*factor

                cy_finaldx(:) = cydx(:)*factor
                cy_finaldy(:) = cydy(:)*factor
                cy_finaldz(:) = cydz(:)*factor

                cz_finaldx(:) = czdx(:)*factor
                cz_finaldy(:) = czdy(:)*factor
                cz_finaldz(:) = czdz(:)*factor

                powerx_finaldx(:) = powerxdx(:)*factor
                powerx_finaldy(:) = powerxdy(:)*factor
                powerx_finaldz(:) = powerxdz(:)*factor

                powery_finaldx(:) = powerydx(:)*factor
                powery_finaldy(:) = powerydy(:)*factor
                powery_finaldz(:) = powerydz(:)*factor

                powerz_finaldx(:) = powerzdx(:)*factor
                powerz_finaldy(:) = powerzdy(:)*factor
                powerz_finaldz(:) = powerzdz(:)*factor

                cmx_finaldx(:) = cmxdx(:)*factor
                cmx_finaldy(:) = cmxdy(:)*factor
                cmx_finaldz(:) = cmxdz(:)*factor

                cmy_finaldx(:) = cmydx(:)*factor
                cmy_finaldy(:) = cmydy(:)*factor
                cmy_finaldz(:) = cmydz(:)*factor

                cmz_finaldx(:) = cmzdx(:)*factor
                cmz_finaldy(:) = cmzdy(:)*factor
                cmz_finaldz(:) = cmzdz(:)*factor

                ioff(:) = 0

                do m = 1, elem(ielem)%node_per_cell
                  node = c2n_cell(m)
                  do i = ia(node), ia(node+1)-1
                    icol = ja(i)
                    if ( icol == node ) ioff(m) = i
                  end do
                end do

                select case (design%function_data(j)%component_data(k)%name)
                case ( cl_id, clv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forcel_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forcel_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forcel_finaldz(m)
                  end do
                case ( cd_id, cdv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forced_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forced_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forced_finaldz(m)
                  end do
                case ( powerx_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerx_finaldz(m)
                  end do
                case ( powery_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powery_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powery_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powery_finaldz(m)
                  end do
                case ( powerz_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerz_finaldz(m)
                  end do
                case ( cx_id, cxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cx_finaldz(m)
                  end do
                case ( cy_id, cyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cy_finaldz(m)
                  end do
                case ( cz_id, czv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cz_finaldz(m)
                  end do
                case ( cmx_id, cmxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmx_finaldz(m)
                  end do
                case ( cmy_id, cmyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmy_finaldz(m)
                  end do
                case ( cmz_id, cmzv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmz_finaldz(m)
                  end do
                case ( clcd_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldx(m) - cl/cd/cd*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldy(m) - cl/cd/cd*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldz(m) - cl/cd/cd*forceddz(m))
                  end do
                case ( fom_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldx(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldy(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldz(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( propeff_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdx(m)/sref        &
                                             - cz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdy(m)/sref        &
                                             - cz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdz(m)/sref        &
                                             - cz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( rotor_thrust_id )
                  sint = sin(thrust_angle)
                  cost = cos(thrust_angle)
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                           (cost*forceldx(m) - sint*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                           (cost*forceldy(m) - sint*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                           (cost*forceldz(m) - sint*forceddz(m))
                  end do
                case default
                end select

              end do component_loop

            endif local_tria

          end do tria_faces


          quad_faces : do n = 1, nbfaceq

            local_quad : if (face_bitq(n) == 1) then

              bnode1 = ibnode(f2nqb(n,1))
              bnode2 = ibnode(f2nqb(n,2))
              bnode3 = ibnode(f2nqb(n,3))
              bnode4 = ibnode(f2nqb(n,4))

              icell = f2nqb(n,5)
              ielem = f2nqb(n,6)

! set some loop indicies and local mapping arrays depending on whether
! we are doing a 2D case or a 3D case

              node_map(:) = 0

              if (twod) then

                face_2d = elem(ielem)%face_2d

                nodes_local = 3
                if (elem(ielem)%local_f2n(face_2d,1) /=                        &
                    elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

                do i=1,nodes_local
                  node_map(i) = elem(ielem)%local_f2n(face_2d,i)
                end do

              else

                nodes_local = elem(ielem)%node_per_cell

                do i=1,nodes_local
                  node_map(i) = i
                end do

              end if

! copy c2n and local_f2n arrays from the derived type so we  minimize
! references to derived types inside loops as much as possible

              do node = 1, elem(ielem)%node_per_cell
                c2n_cell(node) = elem(ielem)%c2n(node,icell)
              end do

! compute cell averaged viscosity by looping over the nodes in the
! element and gathering their contributions, then average at the end

              rmu = 0.0_dp

              x_node(:)   = 0.0_dp
              y_node(:)   = 0.0_dp
              z_node(:)   = 0.0_dp
              u_node(:)   = 0.0_dp
              v_node(:)   = 0.0_dp
              w_node(:)   = 0.0_dp
              p_node(:)   = 0.0_dp
              t_node(:)   = 0.0_dp
              mu_node(:)  = 0.0_dp
              q_node(:,:) = 0.0_dp

              node_loop2 : do i_local = 1, nodes_local

                i = node_map(i_local)

                node = c2n_cell(i)

                x_node(i) = x(node)
                y_node(i) = y(node)
                z_node(i) = z(node)

                u_node(i) = qnode(2,node)
                v_node(i) = qnode(3,node)
                w_node(i) = qnode(4,node)
                p_node(i) = qnode(5,node)
                t_node(i) = gamma*p_node(i)/qnode(1,node)

                mu_node(i) = viscosity_law( cstar, t_node(i) ) + amut(node)

                rmu = rmu + mu_node(i)

              end do node_loop2

! now compute cell average by dividing by the number of nodes
! that contributed

              rmu = rmu / real(nodes_local, dp)

              u = (qnode(2,bnode1) + qnode(2,bnode2) + qnode(2,bnode3)         &
                 + qnode(2,bnode4)) / 4.0_dp
              v = (qnode(3,bnode1) + qnode(3,bnode2) + qnode(3,bnode3)         &
                 + qnode(3,bnode4)) / 4.0_dp
              w = (qnode(4,bnode1) + qnode(4,bnode2) + qnode(4,bnode3)         &
                 + qnode(4,bnode4)) / 4.0_dp

! now we get this boundary face's normal

              x1dx(:) = 0.0_dp
              x2dx(:) = 0.0_dp
              x3dx(:) = 0.0_dp
              x4dx(:) = 0.0_dp
              y1dy(:) = 0.0_dp
              y2dy(:) = 0.0_dp
              y3dy(:) = 0.0_dp
              y4dy(:) = 0.0_dp
              z1dz(:) = 0.0_dp
              z2dz(:) = 0.0_dp
              z3dz(:) = 0.0_dp
              z4dz(:) = 0.0_dp

              x1 = x(bnode1)
              y1 = y(bnode1)
              z1 = z(bnode1)

              x2 = x(bnode2)
              y2 = y(bnode2)
              z2 = z(bnode2)

              x3 = x(bnode3)
              y3 = y(bnode3)
              z3 = z(bnode3)

              x4 = x(bnode4)
              y4 = y(bnode4)
              z4 = z(bnode4)

              do node = 1, elem(ielem)%node_per_cell
                if ( bnode1 == c2n_cell(node) ) then
                  x1dx(node) = 1.0_dp
                  y1dy(node) = 1.0_dp
                  z1dz(node) = 1.0_dp
                endif
                if ( bnode2 == c2n_cell(node) ) then
                  x2dx(node) = 1.0_dp
                  y2dy(node) = 1.0_dp
                  z2dz(node) = 1.0_dp
                endif
                if ( bnode3 == c2n_cell(node) ) then
                  x3dx(node) = 1.0_dp
                  y3dy(node) = 1.0_dp
                  z3dz(node) = 1.0_dp
                endif
                if ( bnode4 == c2n_cell(node) ) then
                  x4dx(node) = 1.0_dp
                  y4dy(node) = 1.0_dp
                  z4dz(node) = 1.0_dp
                endif
              end do

! - sign for outward facing normal

              xnorm = -0.5_dp*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
                xnormdx(:) =  0.0_dp
                xnormdy(:) = -0.5_dp*( (y3dy(:) - y1dy(:))*(z4 - z2)           &
                                   - (z3 - z1)*(y4dy(:) - y2dy(:)) )
                xnormdz(:) = -0.5_dp*( (y3 - y1)*(z4dz(:) - z2dz(:))           &
                                   - (z3dz(:) - z1dz(:))*(y4 - y2) )

              ynorm = -0.5_dp*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
                ynormdx(:) = -0.5_dp*( (z3 - z1)*(x4dx(:) - x2dx(:))           &
                                   - (x3dx(:) - x1dx(:))*(z4 - z2) )
                ynormdy(:) =  0.0_dp
                ynormdz(:) = -0.5_dp*( (z3dz(:) - z1dz(:))*(x4 - x2)           &
                                   - (x3 - x1)*(z4dz(:) - z2dz(:)) )

              znorm = -0.5_dp*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )
                znormdx(:) = -0.5_dp*( (x3dx(:) - x1dx(:))*(y4 - y2)           &
                                   - (y3 - y1)*(x4dx(:) - x2dx(:)) )
                znormdy(:) = -0.5_dp*( (x3 - x1)*(y4dy(:) - y2dy(:))           &
                                   - (y3dy(:) - y1dy(:))*(x4 - x2) )
                znormdz(:) =  0.0_dp

              q_node(2,:) = u_node(:)
              q_node(3,:) = v_node(:)
              q_node(4,:) = w_node(:)

              cell_vol = 0.0_dp
                cell_voldx(:) = 0.0_dp
                cell_voldy(:) = 0.0_dp
                cell_voldz(:) = 0.0_dp

              gradx_cell(:) = my_0
                gradx_celldx(:,:) = my_0
                gradx_celldy(:,:) = my_0
                gradx_celldz(:,:) = my_0
              grady_cell(:) = my_0
                grady_celldx(:,:) = my_0
                grady_celldy(:,:) = my_0
                grady_celldz(:,:) = my_0
              gradz_cell(:) = my_0
                gradz_celldx(:,:) = my_0
                gradz_celldy(:,:) = my_0
                gradz_celldz(:,:) = my_0

              threed_faces2 : do iface = 1, elem(ielem)%face_per_cell

                nn1 = elem(ielem)%local_f2n(iface,1)
                nn2 = elem(ielem)%local_f2n(iface,2)
                nn3 = elem(ielem)%local_f2n(iface,3)
                nn4 = elem(ielem)%local_f2n(iface,4)

                nxdx = 0.0_dp
                nxdy = 0.0_dp
                nxdz = 0.0_dp
                nx1dx = 0.0_dp
                nx1dy = 0.0_dp
                nx1dz = 0.0_dp
                nx2dx = 0.0_dp
                nx2dy = 0.0_dp
                nx2dz = 0.0_dp

                nydx = 0.0_dp
                nydy = 0.0_dp
                nydz = 0.0_dp
                ny1dx = 0.0_dp
                ny1dy = 0.0_dp
                ny1dz = 0.0_dp
                ny2dx = 0.0_dp
                ny2dy = 0.0_dp
                ny2dz = 0.0_dp

                nzdx = 0.0_dp
                nzdy = 0.0_dp
                nzdz = 0.0_dp
                nz1dx = 0.0_dp
                nz1dy = 0.0_dp
                nz1dz = 0.0_dp
                nz2dx = 0.0_dp
                nz2dy = 0.0_dp
                nz2dz = 0.0_dp

                if (nn4 == nn1) then

! triangular faces of the cell

! face normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1)) &
                     - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nxdy(nn1) = -(z_node(nn3) - z_node(nn1))                   &
                               + (z_node(nn2) - z_node(nn1))
                    nxdy(nn2) =   z_node(nn3) - z_node(nn1)
                    nxdy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nxdz(nn1) = -(y_node(nn2) - y_node(nn1))                   &
                               + (y_node(nn3) - y_node(nn1))
                    nxdz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nxdz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1)) &
                     - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    nydx(nn1) = -(z_node(nn2) - z_node(nn1))                   &
                               + (z_node(nn3) - z_node(nn1))
                    nydx(nn2) = -(z_node(nn3) - z_node(nn1))
                    nydx(nn3) =  (z_node(nn2) - z_node(nn1))

                    nydz(nn1) = -(x_node(nn3) - x_node(nn1))                   &
                               + (x_node(nn2) - x_node(nn1))
                    nydz(nn2) =  (x_node(nn3) - x_node(nn1))
                    nydz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1)) &
                     - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nzdx(nn1) = -(y_node(nn3) - y_node(nn1))                   &
                               + (y_node(nn2) - y_node(nn1))
                    nzdx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nzdx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nzdy(nn1) = -(x_node(nn2) - x_node(nn1))                   &
                               + (x_node(nn3) - x_node(nn1))
                    nzdy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nzdy(nn3) =  (x_node(nn2) - x_node(nn1))

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavgdx = 0.0_dp
                  yavgdy = 0.0_dp
                  zavgdz = 0.0_dp

                  xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavgdx(nn1) = 1.0_dp
                    xavgdx(nn2) = 1.0_dp
                    xavgdx(nn3) = 1.0_dp

                  yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavgdy(nn1) = 1.0_dp
                    yavgdy(nn2) = 1.0_dp
                    yavgdy(nn3) = 1.0_dp

                  zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavgdz(nn1) = 1.0_dp
                    zavgdz(nn2) = 1.0_dp
                    zavgdz(nn3) = 1.0_dp

! cell volume contributions

                  cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:)              &
                                  + yavg*nydx(:) + zavg*nzdx(:) + xavgdx(:)*nx &
                                                                       )*my_18th
                    cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:)              &
                                  + yavg*nydy(:) + zavg*nzdy(:) + yavgdy(:)*ny &
                                                                       )*my_18th
                    cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:)              &
                                  + yavg*nydz(:) + zavg*nzdz(:) + zavgdz(:)*nz &
                                                                       )*my_18th

                  termx = nx*my_6th
                    termxdx(:) = nxdx(:)*my_6th
                    termxdy(:) = nxdy(:)*my_6th
                    termxdz(:) = nxdz(:)*my_6th
                  termy = ny*my_6th
                    termydx(:) = nydx(:)*my_6th
                    termydy(:) = nydy(:)*my_6th
                    termydz(:) = nydz(:)*my_6th
                  termz = nz*my_6th
                    termzdx(:) = nzdx(:)*my_6th
                    termzdy(:) = nzdy(:)*my_6th
                    termzdz(:) = nzdz(:)*my_6th

! gradient contributions

                  do eqn = 2, 4
                    qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
                      gradx_celldx(eqn,:)= gradx_celldx(eqn,:) + termxdx(:)*qavg
                      gradx_celldy(eqn,:)= gradx_celldy(eqn,:) + termxdy(:)*qavg
                      gradx_celldz(eqn,:)= gradx_celldz(eqn,:) + termxdz(:)*qavg
                    grady_cell(eqn) = grady_cell(eqn) + termy*qavg
                      grady_celldx(eqn,:)= grady_celldx(eqn,:) + termydx(:)*qavg
                      grady_celldy(eqn,:)= grady_celldy(eqn,:) + termydy(:)*qavg
                      grady_celldz(eqn,:)= grady_celldz(eqn,:) + termydz(:)*qavg
                    gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
                      gradz_celldx(eqn,:)= gradz_celldx(eqn,:) + termzdx(:)*qavg
                      gradz_celldy(eqn,:)= gradz_celldy(eqn,:) + termzdy(:)*qavg
                      gradz_celldz(eqn,:)= gradz_celldz(eqn,:) + termzdz(:)*qavg
                  end do

                else

! quadrilateral faces of the cell

! break face up into triangles 1-2-3 and 1-3-4 and add together

! triangle 1: 1-2-3

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg1dx = 0.0_dp
                  yavg1dy = 0.0_dp
                  zavg1dz = 0.0_dp

                  xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavg1dx(nn1) = 1.0_dp
                    xavg1dx(nn2) = 1.0_dp
                    xavg1dx(nn3) = 1.0_dp
                  yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavg1dy(nn1) = 1.0_dp
                    yavg1dy(nn2) = 1.0_dp
                    yavg1dy(nn3) = 1.0_dp
                  zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavg1dz(nn1) = 1.0_dp
                    zavg1dz(nn2) = 1.0_dp
                    zavg1dz(nn3) = 1.0_dp

! triangle 1 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))&
                      - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nx1dy(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn2) - z_node(nn1))
                    nx1dy(nn2) =   z_node(nn3) - z_node(nn1)
                    nx1dy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nx1dz(nn1) = -(y_node(nn2) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nx1dz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nx1dz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))&
                      - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    ny1dx(nn1) = -(z_node(nn2) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    ny1dx(nn2) = -(z_node(nn3) - z_node(nn1))
                    ny1dx(nn3) =  (z_node(nn2) - z_node(nn1))

                    ny1dz(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn2) - x_node(nn1))
                    ny1dz(nn2) =  (x_node(nn3) - x_node(nn1))
                    ny1dz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))&
                      - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nz1dx(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn2) - y_node(nn1))
                    nz1dx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nz1dx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nz1dy(nn1) = -(x_node(nn2) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    nz1dy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nz1dy(nn3) =  (x_node(nn2) - x_node(nn1))

                  term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
                    term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:)               &
                               + zavg1*nz1dx(:) + xavg1dx(:)*nx1
                    term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:)               &
                               + zavg1*nz1dy(:) + yavg1dy(:)*ny1
                    term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:)               &
                               + zavg1*nz1dz(:) + zavg1dz(:)*nz1

! triangle 2: 1-3-4

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg2dx = 0.0_dp
                  yavg2dy = 0.0_dp
                  zavg2dz = 0.0_dp

                  xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
                    xavg2dx(nn1) = 1.0_dp
                    xavg2dx(nn3) = 1.0_dp
                    xavg2dx(nn4) = 1.0_dp
                  yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
                    yavg2dy(nn1) = 1.0_dp
                    yavg2dy(nn3) = 1.0_dp
                    yavg2dy(nn4) = 1.0_dp
                  zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
                    zavg2dz(nn1) = 1.0_dp
                    zavg2dz(nn3) = 1.0_dp
                    zavg2dz(nn4) = 1.0_dp

! triangle 2 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))&
                      - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

                    nx2dy(nn1) = -(z_node(nn4) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    nx2dy(nn3) =   z_node(nn4) - z_node(nn1)
                    nx2dy(nn4) = -(z_node(nn3) - z_node(nn1))

                    nx2dz(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn4) - y_node(nn1))
                    nx2dz(nn3) = -(y_node(nn4) - y_node(nn1))
                    nx2dz(nn4) =  (y_node(nn3) - y_node(nn1))

                  ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))&
                      - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

                    ny2dx(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn4) - z_node(nn1))
                    ny2dx(nn3) = -(z_node(nn4) - z_node(nn1))
                    ny2dx(nn4) =  (z_node(nn3) - z_node(nn1))

                    ny2dz(nn1) = -(x_node(nn4) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    ny2dz(nn3) =  (x_node(nn4) - x_node(nn1))
                    ny2dz(nn4) = -(x_node(nn3) - x_node(nn1))

                  nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))&
                      - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

                    nz2dx(nn1) = -(y_node(nn4) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nz2dx(nn3) =  (y_node(nn4) - y_node(nn1))
                    nz2dx(nn4) = -(y_node(nn3) - y_node(nn1))

                    nz2dy(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn4) - x_node(nn1))
                    nz2dy(nn3) = -(x_node(nn4) - x_node(nn1))
                    nz2dy(nn4) =  (x_node(nn3) - x_node(nn1))

                  term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
                    term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:)               &
                               + zavg2*nz2dx(:) + xavg2dx(:)*nx2
                    term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:)               &
                               + zavg2*nz2dy(:) + yavg2dy(:)*ny2
                    term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:)               &
                               + zavg2*nz2dz(:) + zavg2dz(:)*nz2

! cell volume contributions

                  cell_vol = cell_vol + (term1 + term2)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))  &
                                                                        *my_18th
                    cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))  &
                                                                        *my_18th
                    cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))  &
                                                                        *my_18th

! gradient contributions

                  termx1 = nx1*my_6th
                    termx1dx(:) = nx1dx(:)*my_6th
                    termx1dy(:) = nx1dy(:)*my_6th
                    termx1dz(:) = nx1dz(:)*my_6th
                  termy1 = ny1*my_6th
                    termy1dx(:) = ny1dx(:)*my_6th
                    termy1dy(:) = ny1dy(:)*my_6th
                    termy1dz(:) = ny1dz(:)*my_6th
                  termz1 = nz1*my_6th
                    termz1dx(:) = nz1dx(:)*my_6th
                    termz1dy(:) = nz1dy(:)*my_6th
                    termz1dz(:) = nz1dz(:)*my_6th

                  termx2 = nx2*my_6th
                    termx2dx(:) = nx2dx(:)*my_6th
                    termx2dy(:) = nx2dy(:)*my_6th
                    termx2dz(:) = nx2dz(:)*my_6th
                  termy2 = ny2*my_6th
                    termy2dx(:) = ny2dx(:)*my_6th
                    termy2dy(:) = ny2dy(:)*my_6th
                    termy2dz(:) = ny2dz(:)*my_6th
                  termz2 = nz2*my_6th
                    termz2dx(:) = nz2dx(:)*my_6th
                    termz2dy(:) = nz2dy(:)*my_6th
                    termz2dz(:) = nz2dz(:)*my_6th

                  do eqn = 2, 4
                    qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)
                    gradx_cell(eqn)=gradx_cell(eqn)+ termx1*qavg1 + termx2*qavg2
                      gradx_celldx(eqn,:) = gradx_celldx(eqn,:)                &
                                         + termx1dx(:)*qavg1 + termx2dx(:)*qavg2
                      gradx_celldy(eqn,:) = gradx_celldy(eqn,:)                &
                                         + termx1dy(:)*qavg1 + termx2dy(:)*qavg2
                      gradx_celldz(eqn,:) = gradx_celldz(eqn,:)                &
                                         + termx1dz(:)*qavg1 + termx2dz(:)*qavg2
                    grady_cell(eqn)=grady_cell(eqn)+ termy1*qavg1 + termy2*qavg2
                      grady_celldx(eqn,:) = grady_celldx(eqn,:)                &
                                         + termy1dx(:)*qavg1 + termy2dx(:)*qavg2
                      grady_celldy(eqn,:) = grady_celldy(eqn,:)                &
                                         + termy1dy(:)*qavg1 + termy2dy(:)*qavg2
                      grady_celldz(eqn,:) = grady_celldz(eqn,:)                &
                                         + termy1dz(:)*qavg1 + termy2dz(:)*qavg2
                    gradz_cell(eqn)=gradz_cell(eqn)+ termz1*qavg1 + termz2*qavg2
                      gradz_celldx(eqn,:) = gradz_celldx(eqn,:)                &
                                         + termz1dx(:)*qavg1 + termz2dx(:)*qavg2
                      gradz_celldy(eqn,:) = gradz_celldy(eqn,:)                &
                                         + termz1dy(:)*qavg1 + termz2dy(:)*qavg2
                      gradz_celldz(eqn,:) = gradz_celldz(eqn,:)                &
                                         + termz1dz(:)*qavg1 + termz2dz(:)*qavg2
                  end do

                end if

              end do threed_faces2

! need to divide the gradient sums by the grid cell volume to give the
! cell-average Green-Gauss gradients

              cell_vol_inv = my_1/cell_vol
                cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
                cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
                cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

              gradx_cell_new(2:4) = gradx_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)      &
                                        + cell_vol_inv*gradx_celldx(k,:)
                  gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)      &
                                        + cell_vol_inv*gradx_celldy(k,:)
                  gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)      &
                                        + cell_vol_inv*gradx_celldz(k,:)
                end do
              grady_cell_new(2:4) = grady_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)      &
                                        + cell_vol_inv*grady_celldx(k,:)
                  grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)      &
                                        + cell_vol_inv*grady_celldy(k,:)
                  grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)      &
                                        + cell_vol_inv*grady_celldz(k,:)
                end do
              gradz_cell_new(2:4) = gradz_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)      &
                                        + cell_vol_inv*gradz_celldx(k,:)
                  gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)      &
                                        + cell_vol_inv*gradz_celldy(k,:)
                  gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)      &
                                        + cell_vol_inv*gradz_celldz(k,:)
                end do

              ux = gradx_cell_new(2)
                uxdx(:) = gradx_cell_newdx(2,:)
                uxdy(:) = gradx_cell_newdy(2,:)
                uxdz(:) = gradx_cell_newdz(2,:)
              vx = gradx_cell_new(3)
                vxdx(:) = gradx_cell_newdx(3,:)
                vxdy(:) = gradx_cell_newdy(3,:)
                vxdz(:) = gradx_cell_newdz(3,:)
              wx = gradx_cell_new(4)
                wxdx(:) = gradx_cell_newdx(4,:)
                wxdy(:) = gradx_cell_newdy(4,:)
                wxdz(:) = gradx_cell_newdz(4,:)

              uy = grady_cell_new(2)
                uydx(:) = grady_cell_newdx(2,:)
                uydy(:) = grady_cell_newdy(2,:)
                uydz(:) = grady_cell_newdz(2,:)
              vy = grady_cell_new(3)
                vydx(:) = grady_cell_newdx(3,:)
                vydy(:) = grady_cell_newdy(3,:)
                vydz(:) = grady_cell_newdz(3,:)
              wy = grady_cell_new(4)
                wydx(:) = grady_cell_newdx(4,:)
                wydy(:) = grady_cell_newdy(4,:)
                wydz(:) = grady_cell_newdz(4,:)

              uz = gradz_cell_new(2)
                uzdx(:) = gradz_cell_newdx(2,:)
                uzdy(:) = gradz_cell_newdy(2,:)
                uzdz(:) = gradz_cell_newdz(2,:)
              vz = gradz_cell_new(3)
                vzdx(:) = gradz_cell_newdx(3,:)
                vzdy(:) = gradz_cell_newdy(3,:)
                vzdz(:) = gradz_cell_newdz(3,:)
              wz = gradz_cell_new(4)
                wzdx(:) = gradz_cell_newdx(4,:)
                wzdy(:) = gradz_cell_newdy(4,:)
                wzdz(:) = gradz_cell_newdz(4,:)

! now compute components of stress vector acting on the face

              termx = my_2*xmr*rmu*(xnorm*(c43*ux - c23*(vy + wz)) +           &
                                    ynorm*(uy + vx)                +           &
                                    znorm*(uz + wx))
                termxdx(:) = my_2*xmr*rmu*((xnorm*(c43*uxdx(:) - c23*(vydx(:) +&
                             wzdx(:))) + ynorm*(uydx(:) + vxdx(:)) +           &
                             znorm*(uzdx(:) + wxdx(:))) + (xnormdx(:)*(c43*ux -&
                             c23*(vy + wz)) + ynormdx(:)*(uy + vx) +           &
                             znormdx(:)*(uz + wx)  ) )
                termxdy(:) = my_2*xmr*rmu*((xnorm*(c43*uxdy(:) - c23*(vydy(:) +&
                             wzdy(:))) + ynorm*(uydy(:) + vxdy(:)) +           &
                             znorm*(uzdy(:) + wxdy(:))) + (xnormdy(:)*(c43*ux -&
                             c23*(vy + wz)) + ynormdy(:)*(uy + vx) +           &
                             znormdy(:)*(uz + wx)  ) )
                termxdz(:) = my_2*xmr*rmu*((xnorm*(c43*uxdz(:) - c23*(vydz(:) +&
                             wzdz(:))) + ynorm*(uydz(:) + vxdz(:)) +           &
                             znorm*(uzdz(:) + wxdz(:))) + (xnormdz(:)*(c43*ux -&
                             c23*(vy + wz)) + ynormdz(:)*(uy + vx) +           &
                             znormdz(:)*(uz + wx)  ) )

              termy = my_2*xmr*rmu*(xnorm*(uy + vx)                +           &
                                    ynorm*(c43*vy - c23*(ux + wz)) +           &
                                    znorm*(vz + wy))
                termydx(:) = my_2*xmr*rmu*( (xnorm*(uydx(:) + vxdx(:)) +       &
                             ynorm*(c43*vydx(:) - c23*(uxdx(:) + wzdx(:))) +   &
                             znorm*(vzdx(:) + wydx(:))) + xnormdx(:)*(uy + vx) &
                             + ynormdx(:)*(c43*vy - c23*(ux + wz))             &
                             + znormdx(:)*(vz + wy) )
                termydy(:) = my_2*xmr*rmu*( (xnorm*(uydy(:) + vxdy(:)) +       &
                             ynorm*(c43*vydy(:) - c23*(uxdy(:) + wzdy(:))) +   &
                             znorm*(vzdy(:) + wydy(:))) + xnormdy(:)*(uy + vx) &
                             + ynormdy(:)*(c43*vy - c23*(ux + wz))             &
                             + znormdy(:)*(vz + wy) )
                termydz(:) = my_2*xmr*rmu*( (xnorm*(uydz(:) + vxdz(:)) +       &
                             ynorm*(c43*vydz(:) - c23*(uxdz(:) + wzdz(:))) +   &
                             znorm*(vzdz(:) + wydz(:))) + xnormdz(:)*(uy + vx) &
                             + ynormdz(:)*(c43*vy - c23*(ux + wz))             &
                             + znormdz(:)*(vz + wy) )

              termz = my_2*xmr*rmu*(xnorm*(uz + wx)                +           &
                                    ynorm*(vz + wy)                +           &
                                    znorm*(c43*wz - c23*(ux + vy)))
                termzdx(:) = my_2*xmr*rmu*( (xnorm*(uzdx(:) + wxdx(:))         &
                             + ynorm*(vzdx(:) + wydx(:)) + znorm*(c43*wzdx(:)  &
                             - c23*(uxdx(:) + vydx(:)))) + xnormdx(:)*(uz + wx)&
                             + ynormdx(:)*(vz + wy) + znormdx(:)*(c43*wz       &
                             - c23*(ux + vy)) )
                termzdy(:) = my_2*xmr*rmu*( (xnorm*(uzdy(:) + wxdy(:))         &
                             + ynorm*(vzdy(:) + wydy(:)) + znorm*(c43*wzdy(:)  &
                             - c23*(uxdy(:) + vydy(:)))) + xnormdy(:)*(uz + wx)&
                             + ynormdy(:)*(vz + wy) + znormdy(:)*(c43*wz       &
                             - c23*(ux + vy)) )
                termzdz(:) = my_2*xmr*rmu*( (xnorm*(uzdz(:) + wxdz(:))         &
                             + ynorm*(vzdz(:) + wydz(:)) + znorm*(c43*wzdz(:)  &
                             - c23*(uxdz(:) + vydz(:)))) + xnormdz(:)*(uz + wx)&
                             + ynormdz(:)*(vz + wy) + znormdz(:)*(c43*wz       &
                             - c23*(ux + vy)) )

! now dot the stress vector acting on the surface face with
! a unit vector in the drag (lift) direction.  This is the
! magnitude of the friction force acting on the face in the
! drag (lift) direction

! find unit vectors in drag and lift directions

              nxd =   cos(alpha/conv) * cos(yaw/conv)
              nyd = - sin(yaw/conv)
              nzd =   sin(alpha/conv) * cos(yaw/conv)

              nxl = - sin(alpha/conv)
              nyl =   my_0
              nzl =   cos(alpha/conv)

! now do the dot product to get the force in the drag (lift) direction

!             forced = - (termx*nxd + termy*nyd + termz*nzd)
                forceddx(:) =-(termxdx(:)*nxd + termydx(:)*nyd + termzdx(:)*nzd)
                forceddy(:) =-(termxdy(:)*nxd + termydy(:)*nyd + termzdy(:)*nzd)
                forceddz(:) =-(termxdz(:)*nxd + termydz(:)*nyd + termzdz(:)*nzd)

!             forcel = - (termx*nxl + termy*nyl + termz*nzl)
                forceldx(:) =-(termxdx(:)*nxl + termydx(:)*nyl + termzdx(:)*nzl)
                forceldy(:) =-(termxdy(:)*nxl + termydy(:)*nyl + termzdy(:)*nzl)
                forceldz(:) =-(termxdz(:)*nxl + termydz(:)*nyl + termzdz(:)*nzl)

              xmid = (x1 + x2 + x3 + x4)/my_4
                xmiddx(:) = (x1dx(:) + x2dx(:) + x3dx(:) + x4dx(:))/my_4
!               xmiddy(:) = 0.0_dp
!               xmiddz(:) = 0.0_dp

              ymid = (y1 + y2 + y3 + y4)/my_4
!               ymiddx(:) = 0.0_dp
                ymiddy(:) = (y1dy(:) + y2dy(:) + y3dy(:) + y4dy(:))/my_4
!               ymiddz(:) = 0.0_dp

              zmid = (z1 + z2 + z3 + z4)/my_4
!               zmiddx(:) = 0.0_dp
!               zmiddy(:) = 0.0_dp
                zmiddz(:) = (z1dz(:) + z2dz(:) + z3dz(:) + z4dz(:))/my_4

              cxdx(:) = -termxdx(:)
              cxdy(:) = -termxdy(:)
              cxdz(:) = -termxdz(:)
              cydx(:) = -termydx(:)
              cydy(:) = -termydy(:)
              cydz(:) = -termydz(:)
              czdx(:) = -termzdx(:)
              czdy(:) = -termzdy(:)
              czdz(:) = -termzdz(:)

              powerxdx(:) = termxdx(:)*u
              powerxdy(:) = termxdy(:)*u
              powerxdz(:) = termxdz(:)*u

              powerydx(:) = termydx(:)*v
              powerydy(:) = termydy(:)*v
              powerydz(:) = termydz(:)*v

              powerzdx(:) = termzdx(:)*w
              powerzdy(:) = termzdy(:)*w
              powerzdz(:) = termzdz(:)*w

              cmxdx(:) = - termzdx(:)*(ymid-ymc) + termydx(:)*(zmid-zmc)
              cmxdy(:) = - termzdy(:)*(ymid-ymc) + termydy(:)*(zmid-zmc)       &
                         - termz*ymiddy(:)
              cmxdz(:) = - termzdz(:)*(ymid-ymc) + termydz(:)*(zmid-zmc)       &
                         + termy*zmiddz(:)

              cmydx(:) =   termzdx(:)*(xmid-xmc) - termxdx(:)*(zmid-zmc)       &
                         + termz*xmiddx(:)
              cmydy(:) =   termzdy(:)*(xmid-xmc) - termxdy(:)*(zmid-zmc)
              cmydz(:) =   termzdz(:)*(xmid-xmc) - termxdz(:)*(zmid-zmc)       &
                         - termx*zmiddz(:)

              cmzdx(:) = - termydx(:)*(xmid-xmc) + termxdx(:)*(ymid-ymc)       &
                         - termy*xmiddx(:)
              cmzdy(:) = - termydy(:)*(xmid-xmc) + termxdy(:)*(ymid-ymc)       &
                         + termx*ymiddy(:)
              cmzdz(:) = - termydz(:)*(xmid-xmc) + termxdz(:)*(ymid-ymc)

!             force%cdv = force%cdv + forced
!             force%clv = force%clv + forcel

!             force%cxv = force%cxv - termx
!             force%cyv = force%cyv - termy
!             force%czv = force%czv - termz

!             force%cmxv = force%cmxv - termz*(ymid-ymc) + termy*(zmid-zmc)
!             force%cmyv = force%cmyv + termz*(xmid-xmc) - termx*(zmid-zmc)
!             force%cmzv = force%cmzv - termy*(xmid-xmc) + termx*(ymid-ymc)

              component_loop2 : do k = 1, design%function_data(j)%ncomponents

                if ( design%function_data(j)%component_data(k)%boundary_id     &
                     == 0 ) then
                  if ( .not. bcforce(ib)%add_to_total ) cycle component_loop2
                  cd   = force%cd
                  cdv  = force%cdv
                  cl   = force%cl
                  clv  = force%clv
                  cx   = force%cx
                  cy   = force%cy
                  cz   = force%cz
                  cxv  = force%cxv
                  cyv  = force%cyv
                  czv  = force%czv
                  cmx  = force%cmx
                  cmxv = force%cmxv
                  cmy  = force%cmy
                  cmyv = force%cmyv
                  cmz  = force%cmz
                  cmzv = force%cmzv
                  clcd = force%clcd
                  fom  = force%fom
                  propeff= force%propeff
                  rotor_thrust = force%rotor_thrust
                  rpowerx = force%powerx
                  rpowery = force%powery
                  rpowerz = force%powerz
                else if (design%function_data(j)%component_data(k)%boundary_id &
                         == ib ) then
                  cd   = bcforce(ib)%cd
                  cdv  = bcforce(ib)%cdv
                  cl   = bcforce(ib)%cl
                  clv  = bcforce(ib)%clv
                  cx   = bcforce(ib)%cx
                  cy   = bcforce(ib)%cy
                  cz   = bcforce(ib)%cz
                  cxv  = bcforce(ib)%cxv
                  cyv  = bcforce(ib)%cyv
                  czv  = bcforce(ib)%czv
                  cmx  = bcforce(ib)%cmx
                  cmxv = bcforce(ib)%cmxv
                  cmy  = bcforce(ib)%cmy
                  cmyv = bcforce(ib)%cmyv
                  cmz  = bcforce(ib)%cmz
                  cmzv = bcforce(ib)%cmzv
                  rpowerx = bcforce(ib)%powerx
                  rpowery = bcforce(ib)%powery
                  rpowerz = bcforce(ib)%powerz
                else
                  cycle component_loop2
                endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxv_id )
                if (.not.design%function_data(j)%averaging) base = cmxv-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyv_id )
                if (.not.design%function_data(j)%averaging) base = cmyv-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzv_id )
                if (.not.design%function_data(j)%averaging) base = cmzv-target
                const = my_1/sref/bref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxv_id )
                if (.not.design%function_data(j)%averaging) base = cxv-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyv_id )
                if (.not.design%function_data(j)%averaging) base = cyv-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czv_id )
                if (.not.design%function_data(j)%averaging) base = czv-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case default
                cycle component_loop2
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                forcel_finaldx(:) = forceldx(:)*factor
                forcel_finaldy(:) = forceldy(:)*factor
                forcel_finaldz(:) = forceldz(:)*factor

                forced_finaldx(:) = forceddx(:)*factor
                forced_finaldy(:) = forceddy(:)*factor
                forced_finaldz(:) = forceddz(:)*factor

                cx_finaldx(:) = cxdx(:)*factor
                cx_finaldy(:) = cxdy(:)*factor
                cx_finaldz(:) = cxdz(:)*factor

                cy_finaldx(:) = cydx(:)*factor
                cy_finaldy(:) = cydy(:)*factor
                cy_finaldz(:) = cydz(:)*factor

                cz_finaldx(:) = czdx(:)*factor
                cz_finaldy(:) = czdy(:)*factor
                cz_finaldz(:) = czdz(:)*factor

                powerx_finaldx(:) = powerxdx(:)*factor
                powerx_finaldy(:) = powerxdy(:)*factor
                powerx_finaldz(:) = powerxdz(:)*factor

                powery_finaldx(:) = powerydx(:)*factor
                powery_finaldy(:) = powerydy(:)*factor
                powery_finaldz(:) = powerydz(:)*factor

                powerz_finaldx(:) = powerzdx(:)*factor
                powerz_finaldy(:) = powerzdy(:)*factor
                powerz_finaldz(:) = powerzdz(:)*factor

                cmx_finaldx(:) = cmxdx(:)*factor
                cmx_finaldy(:) = cmxdy(:)*factor
                cmx_finaldz(:) = cmxdz(:)*factor

                cmy_finaldx(:) = cmydx(:)*factor
                cmy_finaldy(:) = cmydy(:)*factor
                cmy_finaldz(:) = cmydz(:)*factor

                cmz_finaldx(:) = cmzdx(:)*factor
                cmz_finaldy(:) = cmzdy(:)*factor
                cmz_finaldz(:) = cmzdz(:)*factor

                ioff(:) = 0

                do m = 1, elem(ielem)%node_per_cell
                  node = c2n_cell(m)
                  do i = ia(node), ia(node+1)-1
                    icol = ja(i)
                    if ( icol == node ) ioff(m) = i
                  end do
                end do

                select case (design%function_data(j)%component_data(k)%name)
                case ( cl_id, clv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forcel_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forcel_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forcel_finaldz(m)
                  end do
                case ( cd_id, cdv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forced_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forced_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forced_finaldz(m)
                  end do
                case ( powerx_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerx_finaldz(m)
                  end do
                case ( powery_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powery_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powery_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powery_finaldz(m)
                  end do
                case ( powerz_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerz_finaldz(m)
                  end do
                case ( cx_id, cxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cx_finaldz(m)
                  end do
                case ( cy_id, cyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cy_finaldz(m)
                  end do
                case ( cz_id, czv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cz_finaldz(m)
                  end do
                case ( cmx_id, cmxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmx_finaldz(m)
                  end do
                case ( cmy_id, cmyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmy_finaldz(m)
                  end do
                case ( cmz_id, cmzv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmz_finaldz(m)
                  end do
                case ( clcd_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldx(m) - cl/cd/cd*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldy(m) - cl/cd/cd*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldz(m) - cl/cd/cd*forceddz(m))
                  end do
                case ( fom_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldx(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldy(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldz(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( propeff_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdx(m)/sref        &
                                             - cz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdy(m)/sref        &
                                             - cz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdz(m)/sref        &
                                             - cz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( rotor_thrust_id )
                  sint = sin(thrust_angle)
                  cost = cos(thrust_angle)
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                           (cost*forceldx(m) - sint*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                           (cost*forceldy(m) - sint*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                           (cost*forceldz(m) - sint*forceddz(m))
                  end do
                case default
                end select

              end do component_loop2

            endif local_quad

          end do quad_faces

        end do fcn_loop

  end subroutine skinfricxyz


!=================================== DFDX_CPSTAR =============================80
!
!  Computes cost function linearizations for target pressure functions
!
!=============================================================================80
  subroutine dfdx_cpstar(eqn_set,nnodes0,nnodes01,x,z,l2g,design,qnode,n_tot,  &
                         ia,ja,dfdx,small_stencil_nnz01)

    use design_types,   only : design_type, cpstar_id, cpstar_slices
    use designs,        only : cpstar_loaded, setup_cpstar_data,               &
                               n_cpstar_nodes, cpstar_nodes,                   &
                               cpstar, cpstar_weight, cpstar_deriv
    use kinddefs,       only : dp
    use info_depr,      only : xmach
    use fluid,          only : gamma
    use ivals,          only : p0
    use lmpi,           only : lmpi_die
    use solution_types, only : compressible, incompressible

    integer, intent(in) :: eqn_set, small_stencil_nnz01
    integer, intent(in) :: nnodes0, nnodes01, n_tot

    integer, dimension(nnodes01), intent(in) :: l2g
    integer, dimension(:),        intent(in) :: ia, ja

    type(design_type), intent(in) :: design

    real(dp), dimension(nnodes01),       intent(in) :: x, z
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(3,small_stencil_nnz01,design%nfunctions),              &
                                                           intent(inout) :: dfdx

    integer :: i, j, k, islice, node, ioff, ii, icol

    real(dp) :: weight, target, tag, p, cp
    real(dp) :: deriv, contribx

  continue

    ioff = 0      ! compiler warning
    cp   = 0.0_dp ! compiler warning

! Get the Cpstar info if we haven't already

    if ( .not. cpstar_loaded ) then
      call setup_cpstar_data(nnodes0,nnodes01,x,z,l2g)
      cpstar_loaded = .true.
    endif

! Cycle through the cost functions/constraints and do the p-pstar ones

    fcn_loop : do i = 1, design%nfunctions

      component_loop : do j = 1, design%function_data(i)%ncomponents

        if ( design%function_data(i)%component_data(j)%name == cpstar_id ) then

          weight = design%function_data(i)%component_data(j)%weight

          slice_loop : do islice = 1, cpstar_slices

            node_loop : do k = 1, n_cpstar_nodes(islice)

              node   = cpstar_nodes(islice,k)
              target = cpstar(islice,k)
              tag    = cpstar_weight(islice,k)
              deriv  = cpstar_deriv(islice,k)

              select case ( eqn_set )
              case (compressible)
                p   = qnode(5,node)
                cp  = 2.0_dp*(p/p0-1.0_dp)/(gamma*xmach*xmach)
              case (incompressible)
                p = qnode(1,node)
                cp = 2.0_dp*(p-1.0_dp)
              case default
                write(*,*) 'eqn_set not recognized:',eqn_set
                call lmpi_die
              end select

!    contrib = tag*(cp - target)**2

              contribx = 2.0_dp*weight*tag*(cp-target)*deriv

! Don't need an if (node < nnodes0) statement since we already know
! node is local

              do ii = ia(node), ia(node+1)-1
                icol = ja(ii)
                if ( icol == node ) ioff = ii
              end do

              dfdx(1,ioff,i) = dfdx(1,ioff,i) + contribx

            end do node_loop

          end do slice_loop

        endif

      end do component_loop

    end do fcn_loop

  end subroutine dfdx_cpstar


!================================ SKINFRICXYZI ===============================80
!
!  This gets the derivatives of skin friction wrt the coordinates
!  of the design points
!
!=============================================================================80
  subroutine skinfricxyzi(nnodes01,x,y,z,qnode,amut,nbnode,nbfacet,            &
                          face_bit,ibnode,f2ntb, design, force, ia, ja,        &
                          dfdx, small_stencil_nnz01, ib, nbound, bcforce, ndim,&
                          nelem, elem, nbfaceq, face_bitq, f2nqb)

    use info_depr,         only : Re, alpha, yaw, twod
    use kinddefs,          only : dp
    use design_types,      only : design_type
    use force_types,       only : force_type
    use forces,            only : cl_id, cd_id, clv_id, cdv_id, cmx_id, cmy_id,&
                                  cmz_id, cmxv_id, cmyv_id, cmzv_id, cx_id,    &
                                  cy_id, cz_id, cxv_id, cyv_id, czv_id,        &
                                  powerx_id, powery_id, powerz_id, fom_id,     &
                                  propeff_id, rotor_thrust_id, clcd_id
    use refgeom,           only : sref, xmc, ymc, zmc, bref, cref
    use fun3d_constants,   only : my_0, my_1, my_3, my_4, my_2, my_6th
    use element_defs,      only : max_node_per_cell
    use element_types,     only : elem_type
    use custom_transforms, only : thrust_angle

    integer,                                intent(in)    :: nnodes01
    integer,                                intent(in)    :: nelem, nbfaceq
    integer,                                intent(in)    :: small_stencil_nnz01
    integer,                                intent(in)    :: ib, nbound, ndim
    integer,  dimension(:),                 intent(in)    :: ia, ja
    integer,                                intent(in)    :: nbnode
    integer,                                intent(in)    :: nbfacet
    integer,  dimension(nbfacet),           intent(in)    :: face_bit
    integer,  dimension(nbfaceq),           intent(in)    :: face_bitq
    integer,  dimension(nbnode),            intent(in)    :: ibnode
    integer,  dimension(nbfacet,5),         intent(in)    :: f2ntb
    integer,  dimension(nbfaceq,6),         intent(in)    :: f2nqb
    real(dp), dimension(ndim,nnodes01),     intent(in)    :: qnode
    real(dp), dimension(nnodes01),          intent(in)    :: x, y, z
    real(dp), dimension(nnodes01),          intent(in)    :: amut
    type(design_type),                      intent(in)    :: design
    type(force_type),                       intent(in)    :: force
    type(force_type), dimension(nbound),    intent(in)    :: bcforce
    real(dp), dimension(3,small_stencil_nnz01,design%nfunctions),              &
                                            intent(inout) :: dfdx

    type(elem_type), dimension(nelem), intent(in) :: elem

    integer :: n, icell, j, ielem, i_local, iface, eqn, i, icol, k
    integer :: bnode1, bnode2, bnode3, bnode4, face_2d
    integer :: nodes_local, node, nn1, nn2, nn3, nn4, m

    integer, dimension(max_node_per_cell) :: c2n_cell, node_map
    integer, dimension(max_node_per_cell) :: ioff

    real(dp) :: width, average, base
    real(dp) :: nx1,nx2,const,factor
    real(dp) :: ny1,ny2
    real(dp) :: nz1,nz2
    real(dp) :: nxd, nyd, nzd
    real(dp) :: nxl, nyl, nzl
    real(dp) :: rei,pi,conv
    real(dp) :: x1,y1,z1
    real(dp) :: x2,y2,z2
    real(dp) :: x3,y3,z3
    real(dp) :: x4,y4,z4
    real(dp) :: rmu,u,v,w,rpowerx,rpowery,rpowerz
    real(dp) :: ux,uy,uz,vx,vy,vz,wx,wy,wz
    real(dp) :: xnorm,ynorm,znorm
    real(dp) :: termx,termy,termz
    real(dp) :: term1,term2
    real(dp) :: cd,cdv,cl,clv,weight,target,power
    real(dp) :: cmx,cmy,cmz,cmxv,cmyv,cmzv
    real(dp) :: cx,cy,cz,cxv,cyv,czv
    real(dp) :: xmid
    real(dp) :: ymid
    real(dp) :: zmid
    real(dp) :: cell_vol, nx, ny, nz
    real(dp) :: xavg, yavg, zavg, qavg, qavg1, qavg2, cell_vol_inv
    real(dp) :: xavg1, yavg1, zavg1
    real(dp) :: xavg2, yavg2, zavg2
    real(dp) :: termx1, termy1, termz1
    real(dp) :: termx2, termy2, termz2, mu_node
    real(dp) :: clcd, fom, propeff, rotor_thrust, sint, cost

    real(dp), dimension(max_node_per_cell)   :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)   :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)   :: x1dx,x2dx,x3dx,x4dx
    real(dp), dimension(max_node_per_cell)   :: y1dy,y2dy,y3dy,y4dy
    real(dp), dimension(max_node_per_cell)   :: z1dz,z2dz,z3dz,z4dz
    real(dp), dimension(max_node_per_cell)   :: xnormdx,xnormdy,xnormdz
    real(dp), dimension(max_node_per_cell)   :: ynormdx,ynormdy,ynormdz
    real(dp), dimension(max_node_per_cell)   :: znormdx,znormdy,znormdz
    real(dp), dimension(max_node_per_cell)   :: cell_voldx,cell_voldy,cell_voldz
    real(dp), dimension(max_node_per_cell)   :: nxdx,nxdy,nxdz
    real(dp), dimension(max_node_per_cell)   :: nydx,nydy,nydz
    real(dp), dimension(max_node_per_cell)   :: nzdx,nzdy,nzdz
    real(dp), dimension(max_node_per_cell)   :: nx1dx,nx1dy,nx1dz
    real(dp), dimension(max_node_per_cell)   :: ny1dx,ny1dy,ny1dz
    real(dp), dimension(max_node_per_cell)   :: nz1dx,nz1dy,nz1dz
    real(dp), dimension(max_node_per_cell)   :: nx2dx,nx2dy,nx2dz
    real(dp), dimension(max_node_per_cell)   :: ny2dx,ny2dy,ny2dz
    real(dp), dimension(max_node_per_cell)   :: nz2dx,nz2dy,nz2dz
    real(dp), dimension(max_node_per_cell)   :: xavgdx,yavgdy,zavgdz
    real(dp), dimension(max_node_per_cell)   :: xavg1dx,yavg1dy,zavg1dz
    real(dp), dimension(max_node_per_cell)   :: xavg2dx,yavg2dy,zavg2dz
    real(dp), dimension(max_node_per_cell)   :: termxdx,termxdy,termxdz
    real(dp), dimension(max_node_per_cell)   :: termydx,termydy,termydz
    real(dp), dimension(max_node_per_cell)   :: termzdx,termzdy,termzdz
    real(dp), dimension(max_node_per_cell)   :: term1dx,term1dy,term1dz
    real(dp), dimension(max_node_per_cell)   :: term2dx,term2dy,term2dz
    real(dp), dimension(max_node_per_cell)   :: termx1dx,termx1dy,termx1dz
    real(dp), dimension(max_node_per_cell)   :: termy1dx,termy1dy,termy1dz
    real(dp), dimension(max_node_per_cell)   :: termz1dx,termz1dy,termz1dz
    real(dp), dimension(max_node_per_cell)   :: termx2dx,termx2dy,termx2dz
    real(dp), dimension(max_node_per_cell)   :: termy2dx,termy2dy,termy2dz
    real(dp), dimension(max_node_per_cell)   :: termz2dx,termz2dy,termz2dz
    real(dp), dimension(max_node_per_cell)   :: cell_vol_invdx
    real(dp), dimension(max_node_per_cell)   :: cell_vol_invdy
    real(dp), dimension(max_node_per_cell)   :: cell_vol_invdz
    real(dp), dimension(max_node_per_cell)   :: uxdx,uxdy,uxdz
    real(dp), dimension(max_node_per_cell)   :: uydx,uydy,uydz
    real(dp), dimension(max_node_per_cell)   :: uzdx,uzdy,uzdz
    real(dp), dimension(max_node_per_cell)   :: vxdx,vxdy,vxdz
    real(dp), dimension(max_node_per_cell)   :: vydx,vydy,vydz
    real(dp), dimension(max_node_per_cell)   :: vzdx,vzdy,vzdz
    real(dp), dimension(max_node_per_cell)   :: wxdx,wxdy,wxdz
    real(dp), dimension(max_node_per_cell)   :: wydx,wydy,wydz
    real(dp), dimension(max_node_per_cell)   :: wzdx,wzdy,wzdz
    real(dp), dimension(max_node_per_cell)   :: forceddx,forceddy,forceddz
    real(dp), dimension(max_node_per_cell)   :: forceldx,forceldy,forceldz
    real(dp), dimension(max_node_per_cell)   :: xmiddx
    real(dp), dimension(max_node_per_cell)   :: ymiddy
    real(dp), dimension(max_node_per_cell)   :: zmiddz
    real(dp), dimension(max_node_per_cell)   :: cxdx,cxdy,cxdz
    real(dp), dimension(max_node_per_cell)   :: cydx,cydy,cydz
    real(dp), dimension(max_node_per_cell)   :: czdx,czdy,czdz
    real(dp), dimension(max_node_per_cell)   :: cmxdx,cmxdy,cmxdz
    real(dp), dimension(max_node_per_cell)   :: cmydx,cmydy,cmydz
    real(dp), dimension(max_node_per_cell)   :: cmzdx,cmzdy,cmzdz
    real(dp), dimension(max_node_per_cell)   :: forced_finaldx
    real(dp), dimension(max_node_per_cell)   :: forced_finaldy
    real(dp), dimension(max_node_per_cell)   :: forced_finaldz
    real(dp), dimension(max_node_per_cell)   :: forcel_finaldx
    real(dp), dimension(max_node_per_cell)   :: forcel_finaldy
    real(dp), dimension(max_node_per_cell)   :: forcel_finaldz
    real(dp), dimension(max_node_per_cell)   :: cx_finaldx
    real(dp), dimension(max_node_per_cell)   :: cx_finaldy
    real(dp), dimension(max_node_per_cell)   :: cx_finaldz
    real(dp), dimension(max_node_per_cell)   :: cy_finaldx
    real(dp), dimension(max_node_per_cell)   :: cy_finaldy
    real(dp), dimension(max_node_per_cell)   :: cy_finaldz
    real(dp), dimension(max_node_per_cell)   :: cz_finaldx
    real(dp), dimension(max_node_per_cell)   :: cz_finaldy
    real(dp), dimension(max_node_per_cell)   :: cz_finaldz
    real(dp), dimension(max_node_per_cell)   :: cmx_finaldx
    real(dp), dimension(max_node_per_cell)   :: cmx_finaldy
    real(dp), dimension(max_node_per_cell)   :: cmx_finaldz
    real(dp), dimension(max_node_per_cell)   :: cmy_finaldx
    real(dp), dimension(max_node_per_cell)   :: cmy_finaldy
    real(dp), dimension(max_node_per_cell)   :: cmy_finaldz
    real(dp), dimension(max_node_per_cell)   :: cmz_finaldx
    real(dp), dimension(max_node_per_cell)   :: cmz_finaldy
    real(dp), dimension(max_node_per_cell)   :: cmz_finaldz
    real(dp), dimension(max_node_per_cell)   :: powerxdx
    real(dp), dimension(max_node_per_cell)   :: powerxdy
    real(dp), dimension(max_node_per_cell)   :: powerxdz
    real(dp), dimension(max_node_per_cell)   :: powerydx
    real(dp), dimension(max_node_per_cell)   :: powerydy
    real(dp), dimension(max_node_per_cell)   :: powerydz
    real(dp), dimension(max_node_per_cell)   :: powerzdx
    real(dp), dimension(max_node_per_cell)   :: powerzdy
    real(dp), dimension(max_node_per_cell)   :: powerzdz
    real(dp), dimension(max_node_per_cell)   :: powerx_finaldx
    real(dp), dimension(max_node_per_cell)   :: powerx_finaldy
    real(dp), dimension(max_node_per_cell)   :: powerx_finaldz
    real(dp), dimension(max_node_per_cell)   :: powery_finaldx
    real(dp), dimension(max_node_per_cell)   :: powery_finaldy
    real(dp), dimension(max_node_per_cell)   :: powery_finaldz
    real(dp), dimension(max_node_per_cell)   :: powerz_finaldx
    real(dp), dimension(max_node_per_cell)   :: powerz_finaldy
    real(dp), dimension(max_node_per_cell)   :: powerz_finaldz
    real(dp), dimension(4,max_node_per_cell) :: q_node
    real(dp), dimension(ndim)                :: gradx_cell, grady_cell
    real(dp), dimension(ndim)                :: gradz_cell
    real(dp), dimension(ndim)                :: gradx_cell_new, grady_cell_new
    real(dp), dimension(ndim)                :: gradz_cell_new
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_celldz
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradx_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: grady_cell_newdz
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdx
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdy
    real(dp), dimension(ndim,max_node_per_cell) :: gradz_cell_newdz

    real(dp), parameter :: my_1p5  = 1.5_dp
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp

  continue

    const  = 0.0_dp

    rei = 1.0_dp/Re
    pi = acos(-1.0_dp)
    conv = 180.0_dp / pi

      fcn_loop : do j = 1, design%nfunctions

          tria_faces : do n = 1, nbfacet

            local_tria : if (face_bit(n) == 1) then

              bnode1 = ibnode(f2ntb(n,1))
              bnode2 = ibnode(f2ntb(n,2))
              bnode3 = ibnode(f2ntb(n,3))

              icell = f2ntb(n,4)
              ielem = f2ntb(n,5)

! set some loop indicies and local mapping arrays depending on whether
! we are doing a 2D case or a 3D case

              node_map(:) = 0

              if (twod) then

                face_2d = elem(ielem)%face_2d

                nodes_local = 3
                if (elem(ielem)%local_f2n(face_2d,1) /=                        &
                    elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

                do i=1,nodes_local
                  node_map(i) = elem(ielem)%local_f2n(face_2d,i)
                end do

              else

                nodes_local = elem(ielem)%node_per_cell

                do i=1,nodes_local
                  node_map(i) = i
                end do

              end if

! copy c2n and local_f2n arrays from the derived type so we  minimize
! references to derived types inside loops as much as possible

              do node = 1, elem(ielem)%node_per_cell
                c2n_cell(node) = elem(ielem)%c2n(node,icell)
              end do

! compute cell averaged viscosity by looping over the nodes in the
! element and gathering their contributions, then average at the end

              rmu = 0.0_dp

              x_node(:)   = 0.0_dp
              y_node(:)   = 0.0_dp
              z_node(:)   = 0.0_dp
              u_node(:)   = 0.0_dp
              v_node(:)   = 0.0_dp
              w_node(:)   = 0.0_dp
              q_node(:,:) = 0.0_dp

              node_loop1 : do i_local = 1, nodes_local

                i = node_map(i_local)

                node = c2n_cell(i)

                x_node(i) = x(node)
                y_node(i) = y(node)
                z_node(i) = z(node)

                u_node(i) = qnode(2,node)
                v_node(i) = qnode(3,node)
                w_node(i) = qnode(4,node)

                mu_node = my_1 + amut(node)

                rmu = rmu + mu_node

              end do node_loop1

! now compute cell average by dividing by the number of nodes
! that contributed

              rmu = rmu / real(nodes_local, dp)

              u = (qnode(2,bnode1) + qnode(2,bnode2) + qnode(2,bnode3)) / 3.0_dp
              v = (qnode(3,bnode1) + qnode(3,bnode2) + qnode(3,bnode3)) / 3.0_dp
              w = (qnode(4,bnode1) + qnode(4,bnode2) + qnode(4,bnode3)) / 3.0_dp

! now we get this boundary face's normal

              x1dx(:) = 0.0_dp
              x2dx(:) = 0.0_dp
              x3dx(:) = 0.0_dp
              y1dy(:) = 0.0_dp
              y2dy(:) = 0.0_dp
              y3dy(:) = 0.0_dp
              z1dz(:) = 0.0_dp
              z2dz(:) = 0.0_dp
              z3dz(:) = 0.0_dp

              x1 = x(bnode1)
              y1 = y(bnode1)
              z1 = z(bnode1)

              x2 = x(bnode2)
              y2 = y(bnode2)
              z2 = z(bnode2)

              x3 = x(bnode3)
              y3 = y(bnode3)
              z3 = z(bnode3)

              do node = 1, elem(ielem)%node_per_cell
                if ( bnode1 == c2n_cell(node) ) then
                  x1dx(node) = 1.0_dp
                  y1dy(node) = 1.0_dp
                  z1dz(node) = 1.0_dp
                endif
                if ( bnode2 == c2n_cell(node) ) then
                  x2dx(node) = 1.0_dp
                  y2dy(node) = 1.0_dp
                  z2dz(node) = 1.0_dp
                endif
                if ( bnode3 == c2n_cell(node) ) then
                  x3dx(node) = 1.0_dp
                  y3dy(node) = 1.0_dp
                  z3dz(node) = 1.0_dp
                endif
              end do

! - sign for outward facing normal

              xnorm = -0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
                xnormdx(:) =  0.0_dp
                xnormdy(:) = -0.5_dp*( (y2dy(:)-y1dy(:))*(z3-z1)               &
                                   - (z2-z1)*(y3dy(:)-y1dy(:)) )
                xnormdz(:) = -0.5_dp*( (y2-y1)*(z3dz(:)-z1dz(:))               &
                                   - (z2dz(:)-z1dz(:))*(y3-y1) )

              ynorm = -0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
                ynormdx(:) = -0.5_dp*( (z2-z1)*(x3dx(:)-x1dx(:))               &
                                   - (x2dx(:)-x1dx(:))*(z3-z1) )
                ynormdy(:) =  0.0_dp
                ynormdz(:) = -0.5_dp*( (z2dz(:)-z1dz(:))*(x3-x1)               &
                                   - (x2-x1)*(z3dz(:)-z1dz(:)) )

              znorm = -0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )
                znormdx(:) = -0.5_dp*( (x2dx(:)-x1dx(:))*(y3-y1)               &
                                   - (y2-y1)*(x3dx(:)-x1dx(:)) )
                znormdy(:) = -0.5_dp*( (x2-x1)*(y3dy(:)-y1dy(:))               &
                                   - (y2dy(:)-y1dy(:))*(x3-x1) )
                znormdz(:) =  0.0_dp

              q_node(2,:) = u_node(:)
              q_node(3,:) = v_node(:)
              q_node(4,:) = w_node(:)

              cell_vol = 0.0_dp
                cell_voldx(:) = 0.0_dp
                cell_voldy(:) = 0.0_dp
                cell_voldz(:) = 0.0_dp

              gradx_cell(:) = my_0
                gradx_celldx(:,:) = my_0
                gradx_celldy(:,:) = my_0
                gradx_celldz(:,:) = my_0
              grady_cell(:) = my_0
                grady_celldx(:,:) = my_0
                grady_celldy(:,:) = my_0
                grady_celldz(:,:) = my_0
              gradz_cell(:) = my_0
                gradz_celldx(:,:) = my_0
                gradz_celldy(:,:) = my_0
                gradz_celldz(:,:) = my_0

              threed_faces : do iface = 1, elem(ielem)%face_per_cell

                nn1 = elem(ielem)%local_f2n(iface,1)
                nn2 = elem(ielem)%local_f2n(iface,2)
                nn3 = elem(ielem)%local_f2n(iface,3)
                nn4 = elem(ielem)%local_f2n(iface,4)

                nxdx = 0.0_dp
                nxdy = 0.0_dp
                nxdz = 0.0_dp
                nx1dx = 0.0_dp
                nx1dy = 0.0_dp
                nx1dz = 0.0_dp
                nx2dx = 0.0_dp
                nx2dy = 0.0_dp
                nx2dz = 0.0_dp

                nydx = 0.0_dp
                nydy = 0.0_dp
                nydz = 0.0_dp
                ny1dx = 0.0_dp
                ny1dy = 0.0_dp
                ny1dz = 0.0_dp
                ny2dx = 0.0_dp
                ny2dy = 0.0_dp
                ny2dz = 0.0_dp

                nzdx = 0.0_dp
                nzdy = 0.0_dp
                nzdz = 0.0_dp
                nz1dx = 0.0_dp
                nz1dy = 0.0_dp
                nz1dz = 0.0_dp
                nz2dx = 0.0_dp
                nz2dy = 0.0_dp
                nz2dz = 0.0_dp

                if (nn4 == nn1) then

! triangular faces of the cell

! face normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1)) &
                     - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nxdy(nn1) = -(z_node(nn3) - z_node(nn1))                   &
                               + (z_node(nn2) - z_node(nn1))
                    nxdy(nn2) =   z_node(nn3) - z_node(nn1)
                    nxdy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nxdz(nn1) = -(y_node(nn2) - y_node(nn1))                   &
                               + (y_node(nn3) - y_node(nn1))
                    nxdz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nxdz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1)) &
                     - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    nydx(nn1) = -(z_node(nn2) - z_node(nn1))                   &
                               + (z_node(nn3) - z_node(nn1))
                    nydx(nn2) = -(z_node(nn3) - z_node(nn1))
                    nydx(nn3) =  (z_node(nn2) - z_node(nn1))

                    nydz(nn1) = -(x_node(nn3) - x_node(nn1))                   &
                               + (x_node(nn2) - x_node(nn1))
                    nydz(nn2) =  (x_node(nn3) - x_node(nn1))
                    nydz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1)) &
                     - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nzdx(nn1) = -(y_node(nn3) - y_node(nn1))                   &
                               + (y_node(nn2) - y_node(nn1))
                    nzdx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nzdx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nzdy(nn1) = -(x_node(nn2) - x_node(nn1))                   &
                               + (x_node(nn3) - x_node(nn1))
                    nzdy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nzdy(nn3) =  (x_node(nn2) - x_node(nn1))

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavgdx = 0.0_dp
                  yavgdy = 0.0_dp
                  zavgdz = 0.0_dp

                  xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavgdx(nn1) = 1.0_dp
                    xavgdx(nn2) = 1.0_dp
                    xavgdx(nn3) = 1.0_dp

                  yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavgdy(nn1) = 1.0_dp
                    yavgdy(nn2) = 1.0_dp
                    yavgdy(nn3) = 1.0_dp

                  zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavgdz(nn1) = 1.0_dp
                    zavgdz(nn2) = 1.0_dp
                    zavgdz(nn3) = 1.0_dp

! cell volume contributions

                  cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:)              &
                                  + yavg*nydx(:) + zavg*nzdx(:) + xavgdx(:)*nx &
                                                                     )*my_18th
                    cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:)              &
                                  + yavg*nydy(:) + zavg*nzdy(:) + yavgdy(:)*ny &
                                                                     )*my_18th
                    cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:)              &
                                  + yavg*nydz(:) + zavg*nzdz(:) + zavgdz(:)*nz &
                                                                     )*my_18th

                  termx = nx*my_6th
                    termxdx(:) = nxdx(:)*my_6th
                    termxdy(:) = nxdy(:)*my_6th
                    termxdz(:) = nxdz(:)*my_6th
                  termy = ny*my_6th
                    termydx(:) = nydx(:)*my_6th
                    termydy(:) = nydy(:)*my_6th
                    termydz(:) = nydz(:)*my_6th
                  termz = nz*my_6th
                    termzdx(:) = nzdx(:)*my_6th
                    termzdy(:) = nzdy(:)*my_6th
                    termzdz(:) = nzdz(:)*my_6th

! gradient contributions

                  do eqn = 2, 4
                    qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
                      gradx_celldx(eqn,:)= gradx_celldx(eqn,:) + termxdx(:)*qavg
                      gradx_celldy(eqn,:)= gradx_celldy(eqn,:) + termxdy(:)*qavg
                      gradx_celldz(eqn,:)= gradx_celldz(eqn,:) + termxdz(:)*qavg
                    grady_cell(eqn) = grady_cell(eqn) + termy*qavg
                      grady_celldx(eqn,:)= grady_celldx(eqn,:) + termydx(:)*qavg
                      grady_celldy(eqn,:)= grady_celldy(eqn,:) + termydy(:)*qavg
                      grady_celldz(eqn,:)= grady_celldz(eqn,:) + termydz(:)*qavg
                    gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
                      gradz_celldx(eqn,:)= gradz_celldx(eqn,:) + termzdx(:)*qavg
                      gradz_celldy(eqn,:)= gradz_celldy(eqn,:) + termzdy(:)*qavg
                      gradz_celldz(eqn,:)= gradz_celldz(eqn,:) + termzdz(:)*qavg
                  end do

                else

! quadrilateral faces of the cell

! break face up into triangles 1-2-3 and 1-3-4 and add together

! triangle 1: 1-2-3

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg1dx = 0.0_dp
                  yavg1dy = 0.0_dp
                  zavg1dz = 0.0_dp

                  xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavg1dx(nn1) = 1.0_dp
                    xavg1dx(nn2) = 1.0_dp
                    xavg1dx(nn3) = 1.0_dp
                  yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavg1dy(nn1) = 1.0_dp
                    yavg1dy(nn2) = 1.0_dp
                    yavg1dy(nn3) = 1.0_dp
                  zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavg1dz(nn1) = 1.0_dp
                    zavg1dz(nn2) = 1.0_dp
                    zavg1dz(nn3) = 1.0_dp

! triangle 1 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))&
                      - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nx1dy(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn2) - z_node(nn1))
                    nx1dy(nn2) =   z_node(nn3) - z_node(nn1)
                    nx1dy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nx1dz(nn1) = -(y_node(nn2) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nx1dz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nx1dz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))&
                      - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    ny1dx(nn1) = -(z_node(nn2) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    ny1dx(nn2) = -(z_node(nn3) - z_node(nn1))
                    ny1dx(nn3) =  (z_node(nn2) - z_node(nn1))

                    ny1dz(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn2) - x_node(nn1))
                    ny1dz(nn2) =  (x_node(nn3) - x_node(nn1))
                    ny1dz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))&
                      - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nz1dx(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn2) - y_node(nn1))
                    nz1dx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nz1dx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nz1dy(nn1) = -(x_node(nn2) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    nz1dy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nz1dy(nn3) =  (x_node(nn2) - x_node(nn1))

                  term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
                    term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:)               &
                               + zavg1*nz1dx(:) + xavg1dx(:)*nx1
                    term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:)               &
                               + zavg1*nz1dy(:) + yavg1dy(:)*ny1
                    term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:)               &
                               + zavg1*nz1dz(:) + zavg1dz(:)*nz1

! triangle 2: 1-3-4

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg2dx = 0.0_dp
                  yavg2dy = 0.0_dp
                  zavg2dz = 0.0_dp

                  xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
                    xavg2dx(nn1) = 1.0_dp
                    xavg2dx(nn3) = 1.0_dp
                    xavg2dx(nn4) = 1.0_dp
                  yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
                    yavg2dy(nn1) = 1.0_dp
                    yavg2dy(nn3) = 1.0_dp
                    yavg2dy(nn4) = 1.0_dp
                  zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
                    zavg2dz(nn1) = 1.0_dp
                    zavg2dz(nn3) = 1.0_dp
                    zavg2dz(nn4) = 1.0_dp

! triangle 2 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))&
                      - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

                    nx2dy(nn1) = -(z_node(nn4) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    nx2dy(nn3) =   z_node(nn4) - z_node(nn1)
                    nx2dy(nn4) = -(z_node(nn3) - z_node(nn1))

                    nx2dz(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn4) - y_node(nn1))
                    nx2dz(nn3) = -(y_node(nn4) - y_node(nn1))
                    nx2dz(nn4) =  (y_node(nn3) - y_node(nn1))

                  ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))&
                      - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

                    ny2dx(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn4) - z_node(nn1))
                    ny2dx(nn3) = -(z_node(nn4) - z_node(nn1))
                    ny2dx(nn4) =  (z_node(nn3) - z_node(nn1))

                    ny2dz(nn1) = -(x_node(nn4) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    ny2dz(nn3) =  (x_node(nn4) - x_node(nn1))
                    ny2dz(nn4) = -(x_node(nn3) - x_node(nn1))

                  nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))&
                      - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

                    nz2dx(nn1) = -(y_node(nn4) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nz2dx(nn3) =  (y_node(nn4) - y_node(nn1))
                    nz2dx(nn4) = -(y_node(nn3) - y_node(nn1))

                    nz2dy(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn4) - x_node(nn1))
                    nz2dy(nn3) = -(x_node(nn4) - x_node(nn1))
                    nz2dy(nn4) =  (x_node(nn3) - x_node(nn1))

                  term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
                    term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:)               &
                               + zavg2*nz2dx(:) + xavg2dx(:)*nx2
                    term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:)               &
                               + zavg2*nz2dy(:) + yavg2dy(:)*ny2
                    term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:)               &
                               + zavg2*nz2dz(:) + zavg2dz(:)*nz2

! cell volume contributions

                  cell_vol = cell_vol + (term1 + term2)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))  &
                                                                        *my_18th
                    cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))  &
                                                                        *my_18th
                    cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))  &
                                                                        *my_18th

! gradient contributions

                  termx1 = nx1*my_6th
                    termx1dx(:) = nx1dx(:)*my_6th
                    termx1dy(:) = nx1dy(:)*my_6th
                    termx1dz(:) = nx1dz(:)*my_6th
                  termy1 = ny1*my_6th
                    termy1dx(:) = ny1dx(:)*my_6th
                    termy1dy(:) = ny1dy(:)*my_6th
                    termy1dz(:) = ny1dz(:)*my_6th
                  termz1 = nz1*my_6th
                    termz1dx(:) = nz1dx(:)*my_6th
                    termz1dy(:) = nz1dy(:)*my_6th
                    termz1dz(:) = nz1dz(:)*my_6th

                  termx2 = nx2*my_6th
                    termx2dx(:) = nx2dx(:)*my_6th
                    termx2dy(:) = nx2dy(:)*my_6th
                    termx2dz(:) = nx2dz(:)*my_6th
                  termy2 = ny2*my_6th
                    termy2dx(:) = ny2dx(:)*my_6th
                    termy2dy(:) = ny2dy(:)*my_6th
                    termy2dz(:) = ny2dz(:)*my_6th
                  termz2 = nz2*my_6th
                    termz2dx(:) = nz2dx(:)*my_6th
                    termz2dy(:) = nz2dy(:)*my_6th
                    termz2dz(:) = nz2dz(:)*my_6th

                  do eqn = 2, 4
                    qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)
                    gradx_cell(eqn)=gradx_cell(eqn)+ termx1*qavg1 + termx2*qavg2
                      gradx_celldx(eqn,:) = gradx_celldx(eqn,:)                &
                                         + termx1dx(:)*qavg1 + termx2dx(:)*qavg2
                      gradx_celldy(eqn,:) = gradx_celldy(eqn,:)                &
                                         + termx1dy(:)*qavg1 + termx2dy(:)*qavg2
                      gradx_celldz(eqn,:) = gradx_celldz(eqn,:)                &
                                         + termx1dz(:)*qavg1 + termx2dz(:)*qavg2
                    grady_cell(eqn)=grady_cell(eqn)+ termy1*qavg1 + termy2*qavg2
                      grady_celldx(eqn,:) = grady_celldx(eqn,:)                &
                                         + termy1dx(:)*qavg1 + termy2dx(:)*qavg2
                      grady_celldy(eqn,:) = grady_celldy(eqn,:)                &
                                         + termy1dy(:)*qavg1 + termy2dy(:)*qavg2
                      grady_celldz(eqn,:) = grady_celldz(eqn,:)                &
                                         + termy1dz(:)*qavg1 + termy2dz(:)*qavg2
                    gradz_cell(eqn)=gradz_cell(eqn)+ termz1*qavg1 + termz2*qavg2
                      gradz_celldx(eqn,:) = gradz_celldx(eqn,:)                &
                                         + termz1dx(:)*qavg1 + termz2dx(:)*qavg2
                      gradz_celldy(eqn,:) = gradz_celldy(eqn,:)                &
                                         + termz1dy(:)*qavg1 + termz2dy(:)*qavg2
                      gradz_celldz(eqn,:) = gradz_celldz(eqn,:)                &
                                         + termz1dz(:)*qavg1 + termz2dz(:)*qavg2
                  end do

                end if

              end do threed_faces

! need to divide the gradient sums by the grid cell volume to give the
! cell-average Green-Gauss gradients

              cell_vol_inv = my_1/cell_vol
                cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
                cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
                cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

              gradx_cell_new(2:4) = gradx_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)      &
                                                + cell_vol_inv*gradx_celldx(k,:)
                  gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)      &
                                                + cell_vol_inv*gradx_celldy(k,:)
                  gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)      &
                                                + cell_vol_inv*gradx_celldz(k,:)
                end do
              grady_cell_new(2:4) = grady_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)      &
                                                + cell_vol_inv*grady_celldx(k,:)
                  grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)      &
                                                + cell_vol_inv*grady_celldy(k,:)
                  grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)      &
                                                + cell_vol_inv*grady_celldz(k,:)
                end do
              gradz_cell_new(2:4) = gradz_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)      &
                                                + cell_vol_inv*gradz_celldx(k,:)
                  gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)      &
                                                + cell_vol_inv*gradz_celldy(k,:)
                  gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)      &
                                                + cell_vol_inv*gradz_celldz(k,:)
                end do

              ux = gradx_cell_new(2)
                uxdx(:) = gradx_cell_newdx(2,:)
                uxdy(:) = gradx_cell_newdy(2,:)
                uxdz(:) = gradx_cell_newdz(2,:)
              vx = gradx_cell_new(3)
                vxdx(:) = gradx_cell_newdx(3,:)
                vxdy(:) = gradx_cell_newdy(3,:)
                vxdz(:) = gradx_cell_newdz(3,:)
              wx = gradx_cell_new(4)
                wxdx(:) = gradx_cell_newdx(4,:)
                wxdy(:) = gradx_cell_newdy(4,:)
                wxdz(:) = gradx_cell_newdz(4,:)

              uy = grady_cell_new(2)
                uydx(:) = grady_cell_newdx(2,:)
                uydy(:) = grady_cell_newdy(2,:)
                uydz(:) = grady_cell_newdz(2,:)
              vy = grady_cell_new(3)
                vydx(:) = grady_cell_newdx(3,:)
                vydy(:) = grady_cell_newdy(3,:)
                vydz(:) = grady_cell_newdz(3,:)
              wy = grady_cell_new(4)
                wydx(:) = grady_cell_newdx(4,:)
                wydy(:) = grady_cell_newdy(4,:)
                wydz(:) = grady_cell_newdz(4,:)

              uz = gradz_cell_new(2)
                uzdx(:) = gradz_cell_newdx(2,:)
                uzdy(:) = gradz_cell_newdy(2,:)
                uzdz(:) = gradz_cell_newdz(2,:)
              vz = gradz_cell_new(3)
                vzdx(:) = gradz_cell_newdx(3,:)
                vzdy(:) = gradz_cell_newdy(3,:)
                vzdz(:) = gradz_cell_newdz(3,:)
              wz = gradz_cell_new(4)
                wzdx(:) = gradz_cell_newdx(4,:)
                wzdy(:) = gradz_cell_newdy(4,:)
                wzdz(:) = gradz_cell_newdz(4,:)

! now compute components of stress vector acting on the face

              termx = my_2*rei*rmu*(xnorm*my_2*ux                              &
                                  + ynorm*(uy + vx)                            &
                                  + znorm*(uz + wx))
                termxdx(:) = my_2*rei*rmu*((xnorm*my_2*uxdx(:) + ynorm*(uydx(:)&
                           + vxdx(:)) + znorm*(uzdx(:) + wxdx(:)))             &
                           + (xnormdx(:)*my_2*ux + ynormdx(:)*(uy + vx)        &
                           + znormdx(:)*(uz + wx)  ) )
                termxdy(:) = my_2*rei*rmu*((xnorm*my_2*uxdy(:) + ynorm*(uydy(:)&
                           + vxdy(:)) + znorm*(uzdy(:) + wxdy(:)))             &
                           + (xnormdy(:)*my_2*ux + ynormdy(:)*(uy + vx)        &
                           + znormdy(:)*(uz + wx)  ) )
                termxdz(:) = my_2*rei*rmu*((xnorm*my_2*uxdz(:) + ynorm*(uydz(:)&
                           + vxdz(:)) + znorm*(uzdz(:) + wxdz(:)))             &
                           + (xnormdz(:)*my_2*ux + ynormdz(:)*(uy + vx)        &
                           + znormdz(:)*(uz + wx)  ) )

              termy = my_2*rei*rmu*(xnorm*(uy + vx)                            &
                                  + ynorm*my_2*vy                              &
                                  + znorm*(vz + wy))
                termydx(:) = my_2*rei*rmu*( (xnorm*(uydx(:) + vxdx(:))         &
                           + ynorm*my_2*vydx(:) + znorm*(vzdx(:) + wydx(:)))   &
                           + xnormdx(:)*(uy + vx)                              &
                           + ynormdx(:)*my_2*vy + znormdx(:)*(vz + wy) )
                termydy(:) = my_2*rei*rmu*( (xnorm*(uydy(:) + vxdy(:))         &
                           + ynorm*my_2*vydy(:) + znorm*(vzdy(:) + wydy(:)))   &
                           + xnormdy(:)*(uy + vx)                              &
                           + ynormdy(:)*my_2*vy + znormdy(:)*(vz + wy) )
                termydz(:) = my_2*rei*rmu*( (xnorm*(uydz(:) + vxdz(:))         &
                           + ynorm*my_2*vydz(:) + znorm*(vzdz(:) + wydz(:)))   &
                           + xnormdz(:)*(uy + vx)                              &
                           + ynormdz(:)*my_2*vy + znormdz(:)*(vz + wy) )

              termz = my_2*rei*rmu*(xnorm*(uz + wx)                            &
                                  + ynorm*(vz + wy)                            &
                                  + znorm*my_2*wz)
                termzdx(:) = my_2*rei*rmu*( (xnorm*(uzdx(:) + wxdx(:))         &
                             + ynorm*(vzdx(:) + wydx(:)) + znorm*my_2*wzdx(:)) &
                             + xnormdx(:)*(uz + wx)                            &
                             + ynormdx(:)*(vz + wy) + znormdx(:)*my_2*wz )
                termzdy(:) = my_2*rei*rmu*( (xnorm*(uzdy(:) + wxdy(:))         &
                             + ynorm*(vzdy(:) + wydy(:)) + znorm*my_2*wzdy(:)) &
                             + xnormdy(:)*(uz + wx)                            &
                             + ynormdy(:)*(vz + wy) + znormdy(:)*my_2*wz )
                termzdz(:) = my_2*rei*rmu*( (xnorm*(uzdz(:) + wxdz(:))         &
                             + ynorm*(vzdz(:) + wydz(:)) + znorm*my_2*wzdz(:)) &
                             + xnormdz(:)*(uz + wx)                            &
                             + ynormdz(:)*(vz + wy) + znormdz(:)*my_2*wz )

! now dot the stress vector acting on the surface face with
! a unit vector in the drag (lift) direction.  This is the
! magnitude of the friction force acting on the face in the
! drag (lift) direction

! find unit vectors in drag and lift directions

              nxd =   cos(alpha/conv) * cos(yaw/conv)
              nyd = - sin(yaw/conv)
              nzd =   sin(alpha/conv) * cos(yaw/conv)

              nxl = - sin(alpha/conv)
              nyl =   my_0
              nzl =   cos(alpha/conv)

! now do the dot product to get the force in the drag (lift) direction

!             forced = - (termx*nxd + termy*nyd + termz*nzd)
                forceddx(:) =-(termxdx(:)*nxd + termydx(:)*nyd + termzdx(:)*nzd)
                forceddy(:) =-(termxdy(:)*nxd + termydy(:)*nyd + termzdy(:)*nzd)
                forceddz(:) =-(termxdz(:)*nxd + termydz(:)*nyd + termzdz(:)*nzd)

!             forcel = - (termx*nxl + termy*nyl + termz*nzl)
                forceldx(:) =-(termxdx(:)*nxl + termydx(:)*nyl + termzdx(:)*nzl)
                forceldy(:) =-(termxdy(:)*nxl + termydy(:)*nyl + termzdy(:)*nzl)
                forceldz(:) =-(termxdz(:)*nxl + termydz(:)*nyl + termzdz(:)*nzl)

              xmid = (x1 + x2 + x3)/my_3
                xmiddx(:) = (x1dx(:) + x2dx(:) + x3dx(:))/my_3
!               xmiddy(:) = 0.0_dp
!               xmiddz(:) = 0.0_dp

              ymid = (y1 + y2 + y3)/my_3
!               ymiddx(:) = 0.0_dp
                ymiddy(:) = (y1dy(:) + y2dy(:) + y3dy(:))/my_3
!               ymiddz(:) = 0.0_dp

              zmid = (z1 + z2 + z3)/my_3
!               zmiddx(:) = 0.0_dp
!               zmiddy(:) = 0.0_dp
                zmiddz(:) = (z1dz(:) + z2dz(:) + z3dz(:))/my_3

              cxdx(:) = -termxdx(:)
              cxdy(:) = -termxdy(:)
              cxdz(:) = -termxdz(:)
              cydx(:) = -termydx(:)
              cydy(:) = -termydy(:)
              cydz(:) = -termydz(:)
              czdx(:) = -termzdx(:)
              czdy(:) = -termzdy(:)
              czdz(:) = -termzdz(:)

              powerxdx(:) = termxdx(:)*u
              powerxdy(:) = termxdy(:)*u
              powerxdz(:) = termxdz(:)*u

              powerydx(:) = termydx(:)*v
              powerydy(:) = termydy(:)*v
              powerydz(:) = termydz(:)*v

              powerzdx(:) = termzdx(:)*w
              powerzdy(:) = termzdy(:)*w
              powerzdz(:) = termzdz(:)*w

              cmxdx(:) = - termzdx(:)*(ymid-ymc) + termydx(:)*(zmid-zmc)
              cmxdy(:) = - termzdy(:)*(ymid-ymc) + termydy(:)*(zmid-zmc)       &
                         - termz*ymiddy(:)
              cmxdz(:) = - termzdz(:)*(ymid-ymc) + termydz(:)*(zmid-zmc)       &
                         + termy*zmiddz(:)

              cmydx(:) =   termzdx(:)*(xmid-xmc) - termxdx(:)*(zmid-zmc)       &
                         + termz*xmiddx(:)
              cmydy(:) =   termzdy(:)*(xmid-xmc) - termxdy(:)*(zmid-zmc)
              cmydz(:) =   termzdz(:)*(xmid-xmc) - termxdz(:)*(zmid-zmc)       &
                         - termx*zmiddz(:)

              cmzdx(:) = - termydx(:)*(xmid-xmc) + termxdx(:)*(ymid-ymc)       &
                         - termy*xmiddx(:)
              cmzdy(:) = - termydy(:)*(xmid-xmc) + termxdy(:)*(ymid-ymc)       &
                         + termx*ymiddy(:)
              cmzdz(:) = - termydz(:)*(xmid-xmc) + termxdz(:)*(ymid-ymc)

!             force%cdv = force%cdv + forced
!             force%clv = force%clv + forcel

!             force%cxv = force%cxv - termx
!             force%cyv = force%cyv - termy
!             force%czv = force%czv - termz

!             force%cmxv = force%cmxv - termz*(ymid-ymc) + termy*(zmid-zmc)
!             force%cmyv = force%cmyv + termz*(xmid-xmc) - termx*(zmid-zmc)
!             force%cmzv = force%cmzv - termy*(xmid-xmc) + termx*(ymid-ymc)

              component_loop : do k = 1, design%function_data(j)%ncomponents

                if ( design%function_data(j)%component_data(k)%boundary_id     &
                     == 0 ) then
                  if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
                  cd   = force%cd
                  cdv  = force%cdv
                  cl   = force%cl
                  clv  = force%clv
                  cx   = force%cx
                  cy   = force%cy
                  cz   = force%cz
                  cxv  = force%cxv
                  cyv  = force%cyv
                  czv  = force%czv
                  cmx  = force%cmx
                  cmxv = force%cmxv
                  cmy  = force%cmy
                  cmyv = force%cmyv
                  cmz  = force%cmz
                  cmzv = force%cmzv
                  clcd = force%clcd
                  fom  = force%fom
                  propeff= force%propeff
                  rotor_thrust = force%rotor_thrust
                  rpowerx = force%powerx
                  rpowery = force%powery
                  rpowerz = force%powerz
                else if (design%function_data(j)%component_data(k)%boundary_id &
                         == ib ) then
                  cd   = bcforce(ib)%cd
                  cdv  = bcforce(ib)%cdv
                  cl   = bcforce(ib)%cl
                  clv  = bcforce(ib)%clv
                  cx   = bcforce(ib)%cx
                  cy   = bcforce(ib)%cy
                  cz   = bcforce(ib)%cz
                  cxv  = bcforce(ib)%cxv
                  cyv  = bcforce(ib)%cyv
                  czv  = bcforce(ib)%czv
                  cmx  = bcforce(ib)%cmx
                  cmxv = bcforce(ib)%cmxv
                  cmy  = bcforce(ib)%cmy
                  cmyv = bcforce(ib)%cmyv
                  cmz  = bcforce(ib)%cmz
                  cmzv = bcforce(ib)%cmzv
                  rpowerx = bcforce(ib)%powerx
                  rpowery = bcforce(ib)%powery
                  rpowerz = bcforce(ib)%powerz
                else
                  cycle component_loop
                endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxv_id )
                if (.not.design%function_data(j)%averaging) base = cmxv-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyv_id )
                if (.not.design%function_data(j)%averaging) base = cmyv-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzv_id )
                if (.not.design%function_data(j)%averaging) base = cmzv-target
                const = my_1/sref/bref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxv_id )
                if (.not.design%function_data(j)%averaging) base = cxv-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyv_id )
                if (.not.design%function_data(j)%averaging) base = cyv-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czv_id )
                if (.not.design%function_data(j)%averaging) base = czv-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                forcel_finaldx(:) = forceldx(:)*factor
                forcel_finaldy(:) = forceldy(:)*factor
                forcel_finaldz(:) = forceldz(:)*factor

                forced_finaldx(:) = forceddx(:)*factor
                forced_finaldy(:) = forceddy(:)*factor
                forced_finaldz(:) = forceddz(:)*factor

                cx_finaldx(:) = cxdx(:)*factor
                cx_finaldy(:) = cxdy(:)*factor
                cx_finaldz(:) = cxdz(:)*factor

                cy_finaldx(:) = cydx(:)*factor
                cy_finaldy(:) = cydy(:)*factor
                cy_finaldz(:) = cydz(:)*factor

                cz_finaldx(:) = czdx(:)*factor
                cz_finaldy(:) = czdy(:)*factor
                cz_finaldz(:) = czdz(:)*factor

                powerx_finaldx(:) = powerxdx(:)*factor
                powerx_finaldy(:) = powerxdy(:)*factor
                powerx_finaldz(:) = powerxdz(:)*factor

                powery_finaldx(:) = powerydx(:)*factor
                powery_finaldy(:) = powerydy(:)*factor
                powery_finaldz(:) = powerydz(:)*factor

                powerz_finaldx(:) = powerzdx(:)*factor
                powerz_finaldy(:) = powerzdy(:)*factor
                powerz_finaldz(:) = powerzdz(:)*factor

                cmx_finaldx(:) = cmxdx(:)*factor
                cmx_finaldy(:) = cmxdy(:)*factor
                cmx_finaldz(:) = cmxdz(:)*factor

                cmy_finaldx(:) = cmydx(:)*factor
                cmy_finaldy(:) = cmydy(:)*factor
                cmy_finaldz(:) = cmydz(:)*factor

                cmz_finaldx(:) = cmzdx(:)*factor
                cmz_finaldy(:) = cmzdy(:)*factor
                cmz_finaldz(:) = cmzdz(:)*factor

                ioff(:) = 0

                do m = 1, elem(ielem)%node_per_cell
                  node = c2n_cell(m)
                  do i = ia(node), ia(node+1)-1
                    icol = ja(i)
                    if ( icol == node ) ioff(m) = i
                  end do
                end do

                select case (design%function_data(j)%component_data(k)%name)
                case ( cl_id, clv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forcel_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forcel_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forcel_finaldz(m)
                  end do
                case ( cd_id, cdv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forced_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forced_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forced_finaldz(m)
                  end do
                case ( powerx_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerx_finaldz(m)
                  end do
                case ( powery_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powery_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powery_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powery_finaldz(m)
                  end do
                case ( powerz_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerz_finaldz(m)
                  end do
                case ( cx_id, cxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cx_finaldz(m)
                  end do
                case ( cy_id, cyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cy_finaldz(m)
                  end do
                case ( cz_id, czv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cz_finaldz(m)
                  end do
                case ( cmx_id, cmxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmx_finaldz(m)
                  end do
                case ( cmy_id, cmyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmy_finaldz(m)
                  end do
                case ( cmz_id, cmzv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmz_finaldz(m)
                  end do
                case ( clcd_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldx(m) - cl/cd/cd*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldy(m) - cl/cd/cd*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldz(m) - cl/cd/cd*forceddz(m))
                  end do
                case ( fom_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldx(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldy(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldz(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( propeff_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdx(m)/sref        &
                                             - cz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdy(m)/sref        &
                                             - cz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdz(m)/sref        &
                                             - cz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( rotor_thrust_id )
                  sint = sin(thrust_angle)
                  cost = cos(thrust_angle)
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                           (cost*forceldx(m) - sint*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                           (cost*forceldy(m) - sint*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                           (cost*forceldz(m) - sint*forceddz(m))
                  end do

                case default
                end select

              end do component_loop

            endif local_tria

          end do tria_faces


          quad_faces : do n = 1, nbfaceq

            local_quad : if (face_bitq(n) == 1) then

              bnode1 = ibnode(f2nqb(n,1))
              bnode2 = ibnode(f2nqb(n,2))
              bnode3 = ibnode(f2nqb(n,3))
              bnode4 = ibnode(f2nqb(n,4))

              icell = f2nqb(n,5)
              ielem = f2nqb(n,6)

! set some loop indicies and local mapping arrays depending on whether
! we are doing a 2D case or a 3D case

              node_map(:) = 0

              if (twod) then

                face_2d = elem(ielem)%face_2d

                nodes_local = 3
                if (elem(ielem)%local_f2n(face_2d,1) /=                        &
                    elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

                do i=1,nodes_local
                  node_map(i) = elem(ielem)%local_f2n(face_2d,i)
                end do

              else

                nodes_local = elem(ielem)%node_per_cell

                do i=1,nodes_local
                  node_map(i) = i
                end do

              end if

! copy c2n and local_f2n arrays from the derived type so we  minimize
! references to derived types inside loops as much as possible

              do node = 1, elem(ielem)%node_per_cell
                c2n_cell(node) = elem(ielem)%c2n(node,icell)
              end do

! compute cell averaged viscosity by looping over the nodes in the
! element and gathering their contributions, then average at the end

              rmu = 0.0_dp

              x_node(:)   = 0.0_dp
              y_node(:)   = 0.0_dp
              z_node(:)   = 0.0_dp
              u_node(:)   = 0.0_dp
              v_node(:)   = 0.0_dp
              w_node(:)   = 0.0_dp
              q_node(:,:) = 0.0_dp

              node_loop2 : do i_local = 1, nodes_local

                i = node_map(i_local)

                node = c2n_cell(i)

                x_node(i) = x(node)
                y_node(i) = y(node)
                z_node(i) = z(node)

                u_node(i) = qnode(2,node)
                v_node(i) = qnode(3,node)
                w_node(i) = qnode(4,node)

                mu_node = my_1 + amut(node)

                rmu = rmu + mu_node

              end do node_loop2

! now compute cell average by dividing by the number of nodes
! that contributed

              rmu = rmu / real(nodes_local, dp)

              u = (qnode(2,bnode1) + qnode(2,bnode2) + qnode(2,bnode3)         &
                 + qnode(2,bnode4)) / 4.0_dp
              v = (qnode(3,bnode1) + qnode(3,bnode2) + qnode(3,bnode3)         &
                 + qnode(3,bnode4)) / 4.0_dp
              w = (qnode(4,bnode1) + qnode(4,bnode2) + qnode(4,bnode3)         &
                 + qnode(4,bnode4)) / 4.0_dp

! now we get this boundary face's normal

              x1dx(:) = 0.0_dp
              x2dx(:) = 0.0_dp
              x3dx(:) = 0.0_dp
              x4dx(:) = 0.0_dp
              y1dy(:) = 0.0_dp
              y2dy(:) = 0.0_dp
              y3dy(:) = 0.0_dp
              y4dy(:) = 0.0_dp
              z1dz(:) = 0.0_dp
              z2dz(:) = 0.0_dp
              z3dz(:) = 0.0_dp
              z4dz(:) = 0.0_dp

              x1 = x(bnode1)
              y1 = y(bnode1)
              z1 = z(bnode1)

              x2 = x(bnode2)
              y2 = y(bnode2)
              z2 = z(bnode2)

              x3 = x(bnode3)
              y3 = y(bnode3)
              z3 = z(bnode3)

              x4 = x(bnode4)
              y4 = y(bnode4)
              z4 = z(bnode4)

              do node = 1, elem(ielem)%node_per_cell
                if ( bnode1 == c2n_cell(node) ) then
                  x1dx(node) = 1.0_dp
                  y1dy(node) = 1.0_dp
                  z1dz(node) = 1.0_dp
                endif
                if ( bnode2 == c2n_cell(node) ) then
                  x2dx(node) = 1.0_dp
                  y2dy(node) = 1.0_dp
                  z2dz(node) = 1.0_dp
                endif
                if ( bnode3 == c2n_cell(node) ) then
                  x3dx(node) = 1.0_dp
                  y3dy(node) = 1.0_dp
                  z3dz(node) = 1.0_dp
                endif
                if ( bnode4 == c2n_cell(node) ) then
                  x4dx(node) = 1.0_dp
                  y4dy(node) = 1.0_dp
                  z4dz(node) = 1.0_dp
                endif
              end do

! - sign for outward facing normal

              xnorm = -0.5_dp*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
                xnormdx(:) =  0.0_dp
                xnormdy(:) = -0.5_dp*( (y3dy(:) - y1dy(:))*(z4 - z2)           &
                                   - (z3 - z1)*(y4dy(:) - y2dy(:)) )
                xnormdz(:) = -0.5_dp*( (y3 - y1)*(z4dz(:) - z2dz(:))           &
                                   - (z3dz(:) - z1dz(:))*(y4 - y2) )

              ynorm = -0.5_dp*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
                ynormdx(:) = -0.5_dp*( (z3 - z1)*(x4dx(:) - x2dx(:))           &
                                   - (x3dx(:) - x1dx(:))*(z4 - z2) )
                ynormdy(:) =  0.0_dp
                ynormdz(:) = -0.5_dp*( (z3dz(:) - z1dz(:))*(x4 - x2)           &
                                   - (x3 - x1)*(z4dz(:) - z2dz(:)) )

              znorm = -0.5_dp*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )
                znormdx(:) = -0.5_dp*( (x3dx(:) - x1dx(:))*(y4 - y2)           &
                                   - (y3 - y1)*(x4dx(:) - x2dx(:)) )
                znormdy(:) = -0.5_dp*( (x3 - x1)*(y4dy(:) - y2dy(:))           &
                                   - (y3dy(:) - y1dy(:))*(x4 - x2) )
                znormdz(:) =  0.0_dp

              q_node(2,:) = u_node(:)
              q_node(3,:) = v_node(:)
              q_node(4,:) = w_node(:)

              cell_vol = 0.0_dp
                cell_voldx(:) = 0.0_dp
                cell_voldy(:) = 0.0_dp
                cell_voldz(:) = 0.0_dp

              gradx_cell(:) = my_0
                gradx_celldx(:,:) = my_0
                gradx_celldy(:,:) = my_0
                gradx_celldz(:,:) = my_0
              grady_cell(:) = my_0
                grady_celldx(:,:) = my_0
                grady_celldy(:,:) = my_0
                grady_celldz(:,:) = my_0
              gradz_cell(:) = my_0
                gradz_celldx(:,:) = my_0
                gradz_celldy(:,:) = my_0
                gradz_celldz(:,:) = my_0

              threed_faces2 : do iface = 1, elem(ielem)%face_per_cell

                nn1 = elem(ielem)%local_f2n(iface,1)
                nn2 = elem(ielem)%local_f2n(iface,2)
                nn3 = elem(ielem)%local_f2n(iface,3)
                nn4 = elem(ielem)%local_f2n(iface,4)

                nxdx = 0.0_dp
                nxdy = 0.0_dp
                nxdz = 0.0_dp
                nx1dx = 0.0_dp
                nx1dy = 0.0_dp
                nx1dz = 0.0_dp
                nx2dx = 0.0_dp
                nx2dy = 0.0_dp
                nx2dz = 0.0_dp

                nydx = 0.0_dp
                nydy = 0.0_dp
                nydz = 0.0_dp
                ny1dx = 0.0_dp
                ny1dy = 0.0_dp
                ny1dz = 0.0_dp
                ny2dx = 0.0_dp
                ny2dy = 0.0_dp
                ny2dz = 0.0_dp

                nzdx = 0.0_dp
                nzdy = 0.0_dp
                nzdz = 0.0_dp
                nz1dx = 0.0_dp
                nz1dy = 0.0_dp
                nz1dz = 0.0_dp
                nz2dx = 0.0_dp
                nz2dy = 0.0_dp
                nz2dz = 0.0_dp

                if (nn4 == nn1) then

! triangular faces of the cell

! face normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1)) &
                     - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nxdy(nn1) = -(z_node(nn3) - z_node(nn1))                   &
                               + (z_node(nn2) - z_node(nn1))
                    nxdy(nn2) =   z_node(nn3) - z_node(nn1)
                    nxdy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nxdz(nn1) = -(y_node(nn2) - y_node(nn1))                   &
                               + (y_node(nn3) - y_node(nn1))
                    nxdz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nxdz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1)) &
                     - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    nydx(nn1) = -(z_node(nn2) - z_node(nn1))                   &
                               + (z_node(nn3) - z_node(nn1))
                    nydx(nn2) = -(z_node(nn3) - z_node(nn1))
                    nydx(nn3) =  (z_node(nn2) - z_node(nn1))

                    nydz(nn1) = -(x_node(nn3) - x_node(nn1))                   &
                               + (x_node(nn2) - x_node(nn1))
                    nydz(nn2) =  (x_node(nn3) - x_node(nn1))
                    nydz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1)) &
                     - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nzdx(nn1) = -(y_node(nn3) - y_node(nn1))                   &
                               + (y_node(nn2) - y_node(nn1))
                    nzdx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nzdx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nzdy(nn1) = -(x_node(nn2) - x_node(nn1))                   &
                               + (x_node(nn3) - x_node(nn1))
                    nzdy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nzdy(nn3) =  (x_node(nn2) - x_node(nn1))

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavgdx = 0.0_dp
                  yavgdy = 0.0_dp
                  zavgdz = 0.0_dp

                  xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavgdx(nn1) = 1.0_dp
                    xavgdx(nn2) = 1.0_dp
                    xavgdx(nn3) = 1.0_dp

                  yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavgdy(nn1) = 1.0_dp
                    yavgdy(nn2) = 1.0_dp
                    yavgdy(nn3) = 1.0_dp

                  zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavgdz(nn1) = 1.0_dp
                    zavgdz(nn2) = 1.0_dp
                    zavgdz(nn3) = 1.0_dp

! cell volume contributions

                  cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (xavg*nxdx(:)              &
                                  + yavg*nydx(:) + zavg*nzdx(:) + xavgdx(:)*nx &
                                                                       )*my_18th
                    cell_voldy(:) = cell_voldy(:) + (xavg*nxdy(:)              &
                                  + yavg*nydy(:) + zavg*nzdy(:) + yavgdy(:)*ny &
                                                                       )*my_18th
                    cell_voldz(:) = cell_voldz(:) + (xavg*nxdz(:)              &
                                  + yavg*nydz(:) + zavg*nzdz(:) + zavgdz(:)*nz &
                                                                       )*my_18th

                  termx = nx*my_6th
                    termxdx(:) = nxdx(:)*my_6th
                    termxdy(:) = nxdy(:)*my_6th
                    termxdz(:) = nxdz(:)*my_6th
                  termy = ny*my_6th
                    termydx(:) = nydx(:)*my_6th
                    termydy(:) = nydy(:)*my_6th
                    termydz(:) = nydz(:)*my_6th
                  termz = nz*my_6th
                    termzdx(:) = nzdx(:)*my_6th
                    termzdy(:) = nzdy(:)*my_6th
                    termzdz(:) = nzdz(:)*my_6th

! gradient contributions

                  do eqn = 2, 4
                    qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
                      gradx_celldx(eqn,:)= gradx_celldx(eqn,:) + termxdx(:)*qavg
                      gradx_celldy(eqn,:)= gradx_celldy(eqn,:) + termxdy(:)*qavg
                      gradx_celldz(eqn,:)= gradx_celldz(eqn,:) + termxdz(:)*qavg
                    grady_cell(eqn) = grady_cell(eqn) + termy*qavg
                      grady_celldx(eqn,:)= grady_celldx(eqn,:) + termydx(:)*qavg
                      grady_celldy(eqn,:)= grady_celldy(eqn,:) + termydy(:)*qavg
                      grady_celldz(eqn,:)= grady_celldz(eqn,:) + termydz(:)*qavg
                    gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg
                      gradz_celldx(eqn,:)= gradz_celldx(eqn,:) + termzdx(:)*qavg
                      gradz_celldy(eqn,:)= gradz_celldy(eqn,:) + termzdy(:)*qavg
                      gradz_celldz(eqn,:)= gradz_celldz(eqn,:) + termzdz(:)*qavg
                  end do

                else

! quadrilateral faces of the cell

! break face up into triangles 1-2-3 and 1-3-4 and add together

! triangle 1: 1-2-3

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg1dx = 0.0_dp
                  yavg1dy = 0.0_dp
                  zavg1dz = 0.0_dp

                  xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
                    xavg1dx(nn1) = 1.0_dp
                    xavg1dx(nn2) = 1.0_dp
                    xavg1dx(nn3) = 1.0_dp
                  yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
                    yavg1dy(nn1) = 1.0_dp
                    yavg1dy(nn2) = 1.0_dp
                    yavg1dy(nn3) = 1.0_dp
                  zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)
                    zavg1dz(nn1) = 1.0_dp
                    zavg1dz(nn2) = 1.0_dp
                    zavg1dz(nn3) = 1.0_dp

! triangle 1 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))&
                      - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))

                    nx1dy(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn2) - z_node(nn1))
                    nx1dy(nn2) =   z_node(nn3) - z_node(nn1)
                    nx1dy(nn3) = -(z_node(nn2) - z_node(nn1))

                    nx1dz(nn1) = -(y_node(nn2) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nx1dz(nn2) = -(y_node(nn3) - y_node(nn1))
                    nx1dz(nn3) =  (y_node(nn2) - y_node(nn1))

                  ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))&
                      - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))

                    ny1dx(nn1) = -(z_node(nn2) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    ny1dx(nn2) = -(z_node(nn3) - z_node(nn1))
                    ny1dx(nn3) =  (z_node(nn2) - z_node(nn1))

                    ny1dz(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn2) - x_node(nn1))
                    ny1dz(nn2) =  (x_node(nn3) - x_node(nn1))
                    ny1dz(nn3) = -(x_node(nn2) - x_node(nn1))

                  nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))&
                      - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

                    nz1dx(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn2) - y_node(nn1))
                    nz1dx(nn2) =  (y_node(nn3) - y_node(nn1))
                    nz1dx(nn3) = -(y_node(nn2) - y_node(nn1))

                    nz1dy(nn1) = -(x_node(nn2) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    nz1dy(nn2) = -(x_node(nn3) - x_node(nn1))
                    nz1dy(nn3) =  (x_node(nn2) - x_node(nn1))

                  term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1
                    term1dx(:) = xavg1*nx1dx(:) + yavg1*ny1dx(:)               &
                               + zavg1*nz1dx(:) + xavg1dx(:)*nx1
                    term1dy(:) = xavg1*nx1dy(:) + yavg1*ny1dy(:)               &
                               + zavg1*nz1dy(:) + yavg1dy(:)*ny1
                    term1dz(:) = xavg1*nx1dz(:) + yavg1*ny1dz(:)               &
                               + zavg1*nz1dz(:) + zavg1dz(:)*nz1

! triangle 2: 1-3-4

! face centroid (factor of 1/3 deferred till the
! contribution to cell_vol is calculated)

                  xavg2dx = 0.0_dp
                  yavg2dy = 0.0_dp
                  zavg2dz = 0.0_dp

                  xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
                    xavg2dx(nn1) = 1.0_dp
                    xavg2dx(nn3) = 1.0_dp
                    xavg2dx(nn4) = 1.0_dp
                  yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
                    yavg2dy(nn1) = 1.0_dp
                    yavg2dy(nn3) = 1.0_dp
                    yavg2dy(nn4) = 1.0_dp
                  zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)
                    zavg2dz(nn1) = 1.0_dp
                    zavg2dz(nn3) = 1.0_dp
                    zavg2dz(nn4) = 1.0_dp

! triangle 2 normals (factor of 1/2 deferred till cell_vol
! and gradient terms are calculated)

                  nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))&
                      - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))

                    nx2dy(nn1) = -(z_node(nn4) - z_node(nn1))                  &
                                + (z_node(nn3) - z_node(nn1))
                    nx2dy(nn3) =   z_node(nn4) - z_node(nn1)
                    nx2dy(nn4) = -(z_node(nn3) - z_node(nn1))

                    nx2dz(nn1) = -(y_node(nn3) - y_node(nn1))                  &
                                + (y_node(nn4) - y_node(nn1))
                    nx2dz(nn3) = -(y_node(nn4) - y_node(nn1))
                    nx2dz(nn4) =  (y_node(nn3) - y_node(nn1))

                  ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))&
                      - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))

                    ny2dx(nn1) = -(z_node(nn3) - z_node(nn1))                  &
                                + (z_node(nn4) - z_node(nn1))
                    ny2dx(nn3) = -(z_node(nn4) - z_node(nn1))
                    ny2dx(nn4) =  (z_node(nn3) - z_node(nn1))

                    ny2dz(nn1) = -(x_node(nn4) - x_node(nn1))                  &
                                + (x_node(nn3) - x_node(nn1))
                    ny2dz(nn3) =  (x_node(nn4) - x_node(nn1))
                    ny2dz(nn4) = -(x_node(nn3) - x_node(nn1))

                  nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))&
                      - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

                    nz2dx(nn1) = -(y_node(nn4) - y_node(nn1))                  &
                                + (y_node(nn3) - y_node(nn1))
                    nz2dx(nn3) =  (y_node(nn4) - y_node(nn1))
                    nz2dx(nn4) = -(y_node(nn3) - y_node(nn1))

                    nz2dy(nn1) = -(x_node(nn3) - x_node(nn1))                  &
                                + (x_node(nn4) - x_node(nn1))
                    nz2dy(nn3) = -(x_node(nn4) - x_node(nn1))
                    nz2dy(nn4) =  (x_node(nn3) - x_node(nn1))

                  term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2
                    term2dx(:) = xavg2*nx2dx(:) + yavg2*ny2dx(:)               &
                               + zavg2*nz2dx(:) + xavg2dx(:)*nx2
                    term2dy(:) = xavg2*nx2dy(:) + yavg2*ny2dy(:)               &
                               + zavg2*nz2dy(:) + yavg2dy(:)*ny2
                    term2dz(:) = xavg2*nx2dz(:) + yavg2*ny2dz(:)               &
                               + zavg2*nz2dz(:) + zavg2dz(:)*nz2

! cell volume contributions

                  cell_vol = cell_vol + (term1 + term2)*my_18th
                    cell_voldx(:) = cell_voldx(:) + (term1dx(:) + term2dx(:))  &
                                                                        *my_18th
                    cell_voldy(:) = cell_voldy(:) + (term1dy(:) + term2dy(:))  &
                                                                        *my_18th
                    cell_voldz(:) = cell_voldz(:) + (term1dz(:) + term2dz(:))  &
                                                                        *my_18th

! gradient contributions

                  termx1 = nx1*my_6th
                    termx1dx(:) = nx1dx(:)*my_6th
                    termx1dy(:) = nx1dy(:)*my_6th
                    termx1dz(:) = nx1dz(:)*my_6th
                  termy1 = ny1*my_6th
                    termy1dx(:) = ny1dx(:)*my_6th
                    termy1dy(:) = ny1dy(:)*my_6th
                    termy1dz(:) = ny1dz(:)*my_6th
                  termz1 = nz1*my_6th
                    termz1dx(:) = nz1dx(:)*my_6th
                    termz1dy(:) = nz1dy(:)*my_6th
                    termz1dz(:) = nz1dz(:)*my_6th

                  termx2 = nx2*my_6th
                    termx2dx(:) = nx2dx(:)*my_6th
                    termx2dy(:) = nx2dy(:)*my_6th
                    termx2dz(:) = nx2dz(:)*my_6th
                  termy2 = ny2*my_6th
                    termy2dx(:) = ny2dx(:)*my_6th
                    termy2dy(:) = ny2dy(:)*my_6th
                    termy2dz(:) = ny2dz(:)*my_6th
                  termz2 = nz2*my_6th
                    termz2dx(:) = nz2dx(:)*my_6th
                    termz2dy(:) = nz2dy(:)*my_6th
                    termz2dz(:) = nz2dz(:)*my_6th

                  do eqn = 2, 4
                    qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
                    qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)
                    gradx_cell(eqn)=gradx_cell(eqn)+ termx1*qavg1 + termx2*qavg2
                      gradx_celldx(eqn,:) = gradx_celldx(eqn,:)                &
                                         + termx1dx(:)*qavg1 + termx2dx(:)*qavg2
                      gradx_celldy(eqn,:) = gradx_celldy(eqn,:)                &
                                         + termx1dy(:)*qavg1 + termx2dy(:)*qavg2
                      gradx_celldz(eqn,:) = gradx_celldz(eqn,:)                &
                                         + termx1dz(:)*qavg1 + termx2dz(:)*qavg2
                    grady_cell(eqn)=grady_cell(eqn)+ termy1*qavg1 + termy2*qavg2
                      grady_celldx(eqn,:) = grady_celldx(eqn,:)                &
                                         + termy1dx(:)*qavg1 + termy2dx(:)*qavg2
                      grady_celldy(eqn,:) = grady_celldy(eqn,:)                &
                                         + termy1dy(:)*qavg1 + termy2dy(:)*qavg2
                      grady_celldz(eqn,:) = grady_celldz(eqn,:)                &
                                         + termy1dz(:)*qavg1 + termy2dz(:)*qavg2
                    gradz_cell(eqn)=gradz_cell(eqn)+ termz1*qavg1 + termz2*qavg2
                      gradz_celldx(eqn,:) = gradz_celldx(eqn,:)                &
                                         + termz1dx(:)*qavg1 + termz2dx(:)*qavg2
                      gradz_celldy(eqn,:) = gradz_celldy(eqn,:)                &
                                         + termz1dy(:)*qavg1 + termz2dy(:)*qavg2
                      gradz_celldz(eqn,:) = gradz_celldz(eqn,:)                &
                                         + termz1dz(:)*qavg1 + termz2dz(:)*qavg2
                  end do

                end if

              end do threed_faces2

! need to divide the gradient sums by the grid cell volume to give the
! cell-average Green-Gauss gradients

              cell_vol_inv = my_1/cell_vol
                cell_vol_invdx(:) = -my_1/cell_vol/cell_vol*cell_voldx(:)
                cell_vol_invdy(:) = -my_1/cell_vol/cell_vol*cell_voldy(:)
                cell_vol_invdz(:) = -my_1/cell_vol/cell_vol*cell_voldz(:)

              gradx_cell_new(2:4) = gradx_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradx_cell_newdx(k,:) = gradx_cell(k)*cell_vol_invdx(:)      &
                                        + cell_vol_inv*gradx_celldx(k,:)
                  gradx_cell_newdy(k,:) = gradx_cell(k)*cell_vol_invdy(:)      &
                                        + cell_vol_inv*gradx_celldy(k,:)
                  gradx_cell_newdz(k,:) = gradx_cell(k)*cell_vol_invdz(:)      &
                                        + cell_vol_inv*gradx_celldz(k,:)
                end do
              grady_cell_new(2:4) = grady_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  grady_cell_newdx(k,:) = grady_cell(k)*cell_vol_invdx(:)      &
                                        + cell_vol_inv*grady_celldx(k,:)
                  grady_cell_newdy(k,:) = grady_cell(k)*cell_vol_invdy(:)      &
                                        + cell_vol_inv*grady_celldy(k,:)
                  grady_cell_newdz(k,:) = grady_cell(k)*cell_vol_invdz(:)      &
                                        + cell_vol_inv*grady_celldz(k,:)
                end do
              gradz_cell_new(2:4) = gradz_cell(2:4) * cell_vol_inv
                do k = 2, 4
                  gradz_cell_newdx(k,:) = gradz_cell(k)*cell_vol_invdx(:)      &
                                        + cell_vol_inv*gradz_celldx(k,:)
                  gradz_cell_newdy(k,:) = gradz_cell(k)*cell_vol_invdy(:)      &
                                        + cell_vol_inv*gradz_celldy(k,:)
                  gradz_cell_newdz(k,:) = gradz_cell(k)*cell_vol_invdz(:)      &
                                        + cell_vol_inv*gradz_celldz(k,:)
                end do

              ux = gradx_cell_new(2)
                uxdx(:) = gradx_cell_newdx(2,:)
                uxdy(:) = gradx_cell_newdy(2,:)
                uxdz(:) = gradx_cell_newdz(2,:)
              vx = gradx_cell_new(3)
                vxdx(:) = gradx_cell_newdx(3,:)
                vxdy(:) = gradx_cell_newdy(3,:)
                vxdz(:) = gradx_cell_newdz(3,:)
              wx = gradx_cell_new(4)
                wxdx(:) = gradx_cell_newdx(4,:)
                wxdy(:) = gradx_cell_newdy(4,:)
                wxdz(:) = gradx_cell_newdz(4,:)

              uy = grady_cell_new(2)
                uydx(:) = grady_cell_newdx(2,:)
                uydy(:) = grady_cell_newdy(2,:)
                uydz(:) = grady_cell_newdz(2,:)
              vy = grady_cell_new(3)
                vydx(:) = grady_cell_newdx(3,:)
                vydy(:) = grady_cell_newdy(3,:)
                vydz(:) = grady_cell_newdz(3,:)
              wy = grady_cell_new(4)
                wydx(:) = grady_cell_newdx(4,:)
                wydy(:) = grady_cell_newdy(4,:)
                wydz(:) = grady_cell_newdz(4,:)

              uz = gradz_cell_new(2)
                uzdx(:) = gradz_cell_newdx(2,:)
                uzdy(:) = gradz_cell_newdy(2,:)
                uzdz(:) = gradz_cell_newdz(2,:)
              vz = gradz_cell_new(3)
                vzdx(:) = gradz_cell_newdx(3,:)
                vzdy(:) = gradz_cell_newdy(3,:)
                vzdz(:) = gradz_cell_newdz(3,:)
              wz = gradz_cell_new(4)
                wzdx(:) = gradz_cell_newdx(4,:)
                wzdy(:) = gradz_cell_newdy(4,:)
                wzdz(:) = gradz_cell_newdz(4,:)

! now compute components of stress vector acting on the face

              termx = my_2*rei*rmu*(xnorm*my_2*ux                              &
                                  + ynorm*(uy + vx)                            &
                                  + znorm*(uz + wx))
                termxdx(:) = my_2*rei*rmu*((xnorm*my_2*uxdx(:) + ynorm*(uydx(:)&
                           + vxdx(:)) + znorm*(uzdx(:) + wxdx(:)))             &
                           + (xnormdx(:)*my_2*ux + ynormdx(:)*(uy + vx)        &
                           + znormdx(:)*(uz + wx)  ) )
                termxdy(:) = my_2*rei*rmu*((xnorm*my_2*uxdy(:) + ynorm*(uydy(:)&
                           + vxdy(:)) + znorm*(uzdy(:) + wxdy(:)))             &
                           + (xnormdy(:)*my_2*ux + ynormdy(:)*(uy + vx)        &
                           + znormdy(:)*(uz + wx)  ) )
                termxdz(:) = my_2*rei*rmu*((xnorm*my_2*uxdz(:) + ynorm*(uydz(:)&
                           + vxdz(:)) + znorm*(uzdz(:) + wxdz(:)))             &
                           + (xnormdz(:)*my_2*ux + ynormdz(:)*(uy + vx)        &
                           + znormdz(:)*(uz + wx)  ) )

              termy = my_2*rei*rmu*(xnorm*(uy + vx)                            &
                                  + ynorm*my_2*vy                              &
                                  + znorm*(vz + wy))
                termydx(:) = my_2*rei*rmu*( (xnorm*(uydx(:) + vxdx(:))         &
                           + ynorm*my_2*vydx(:) + znorm*(vzdx(:) + wydx(:)))   &
                           + xnormdx(:)*(uy + vx)                              &
                           + ynormdx(:)*my_2*vy + znormdx(:)*(vz + wy) )
                termydy(:) = my_2*rei*rmu*( (xnorm*(uydy(:) + vxdy(:))         &
                           + ynorm*my_2*vydy(:) + znorm*(vzdy(:) + wydy(:)))   &
                           + xnormdy(:)*(uy + vx)                              &
                           + ynormdy(:)*my_2*vy + znormdy(:)*(vz + wy) )
                termydz(:) = my_2*rei*rmu*( (xnorm*(uydz(:) + vxdz(:))         &
                           + ynorm*my_2*vydz(:) + znorm*(vzdz(:) + wydz(:)))   &
                           + xnormdz(:)*(uy + vx)                              &
                           + ynormdz(:)*my_2*vy + znormdz(:)*(vz + wy) )

              termz = my_2*rei*rmu*(xnorm*(uz + wx)                            &
                                  + ynorm*(vz + wy)                            &
                                  + znorm*my_2*wz)
                termzdx(:) = my_2*rei*rmu*( (xnorm*(uzdx(:) + wxdx(:))         &
                             + ynorm*(vzdx(:) + wydx(:)) + znorm*my_2*wzdx(:)) &
                             + xnormdx(:)*(uz + wx)                            &
                             + ynormdx(:)*(vz + wy) + znormdx(:)*my_2*wz )
                termzdy(:) = my_2*rei*rmu*( (xnorm*(uzdy(:) + wxdy(:))         &
                             + ynorm*(vzdy(:) + wydy(:)) + znorm*my_2*wzdy(:)) &
                             + xnormdy(:)*(uz + wx)                            &
                             + ynormdy(:)*(vz + wy) + znormdy(:)*my_2*wz )
                termzdz(:) = my_2*rei*rmu*( (xnorm*(uzdz(:) + wxdz(:))         &
                             + ynorm*(vzdz(:) + wydz(:)) + znorm*my_2*wzdz(:)) &
                             + xnormdz(:)*(uz + wx)                            &
                             + ynormdz(:)*(vz + wy) + znormdz(:)*my_2*wz )

! now dot the stress vector acting on the surface face with
! a unit vector in the drag (lift) direction.  This is the
! magnitude of the friction force acting on the face in the
! drag (lift) direction

! find unit vectors in drag and lift directions

              nxd =   cos(alpha/conv) * cos(yaw/conv)
              nyd = - sin(yaw/conv)
              nzd =   sin(alpha/conv) * cos(yaw/conv)

              nxl = - sin(alpha/conv)
              nyl =   my_0
              nzl =   cos(alpha/conv)

! now do the dot product to get the force in the drag (lift) direction

!             forced = - (termx*nxd + termy*nyd + termz*nzd)
                forceddx(:) =-(termxdx(:)*nxd + termydx(:)*nyd + termzdx(:)*nzd)
                forceddy(:) =-(termxdy(:)*nxd + termydy(:)*nyd + termzdy(:)*nzd)
                forceddz(:) =-(termxdz(:)*nxd + termydz(:)*nyd + termzdz(:)*nzd)

!             forcel = - (termx*nxl + termy*nyl + termz*nzl)
                forceldx(:) =-(termxdx(:)*nxl + termydx(:)*nyl + termzdx(:)*nzl)
                forceldy(:) =-(termxdy(:)*nxl + termydy(:)*nyl + termzdy(:)*nzl)
                forceldz(:) =-(termxdz(:)*nxl + termydz(:)*nyl + termzdz(:)*nzl)

              xmid = (x1 + x2 + x3 + x4)/my_4
                xmiddx(:) = (x1dx(:) + x2dx(:) + x3dx(:) + x4dx(:))/my_4
!               xmiddy(:) = 0.0_dp
!               xmiddz(:) = 0.0_dp

              ymid = (y1 + y2 + y3 + y4)/my_4
!               ymiddx(:) = 0.0_dp
                ymiddy(:) = (y1dy(:) + y2dy(:) + y3dy(:) + y4dy(:))/my_4
!               ymiddz(:) = 0.0_dp

              zmid = (z1 + z2 + z3 + z4)/my_4
!               zmiddx(:) = 0.0_dp
!               zmiddy(:) = 0.0_dp
                zmiddz(:) = (z1dz(:) + z2dz(:) + z3dz(:) + z4dz(:))/my_4

              cxdx(:) = -termxdx(:)
              cxdy(:) = -termxdy(:)
              cxdz(:) = -termxdz(:)
              cydx(:) = -termydx(:)
              cydy(:) = -termydy(:)
              cydz(:) = -termydz(:)
              czdx(:) = -termzdx(:)
              czdy(:) = -termzdy(:)
              czdz(:) = -termzdz(:)

              powerxdx(:) = termxdx(:)*u
              powerxdy(:) = termxdy(:)*u
              powerxdz(:) = termxdz(:)*u

              powerydx(:) = termydx(:)*v
              powerydy(:) = termydy(:)*v
              powerydz(:) = termydz(:)*v

              powerzdx(:) = termzdx(:)*w
              powerzdy(:) = termzdy(:)*w
              powerzdz(:) = termzdz(:)*w

              cmxdx(:) = - termzdx(:)*(ymid-ymc) + termydx(:)*(zmid-zmc)
              cmxdy(:) = - termzdy(:)*(ymid-ymc) + termydy(:)*(zmid-zmc)       &
                         - termz*ymiddy(:)
              cmxdz(:) = - termzdz(:)*(ymid-ymc) + termydz(:)*(zmid-zmc)       &
                         + termy*zmiddz(:)

              cmydx(:) =   termzdx(:)*(xmid-xmc) - termxdx(:)*(zmid-zmc)       &
                         + termz*xmiddx(:)
              cmydy(:) =   termzdy(:)*(xmid-xmc) - termxdy(:)*(zmid-zmc)
              cmydz(:) =   termzdz(:)*(xmid-xmc) - termxdz(:)*(zmid-zmc)       &
                         - termx*zmiddz(:)

              cmzdx(:) = - termydx(:)*(xmid-xmc) + termxdx(:)*(ymid-ymc)       &
                         - termy*xmiddx(:)
              cmzdy(:) = - termydy(:)*(xmid-xmc) + termxdy(:)*(ymid-ymc)       &
                         + termx*ymiddy(:)
              cmzdz(:) = - termydz(:)*(xmid-xmc) + termxdz(:)*(ymid-ymc)

!             force%cdv = force%cdv + forced
!             force%clv = force%clv + forcel

!             force%cxv = force%cxv - termx
!             force%cyv = force%cyv - termy
!             force%czv = force%czv - termz

!             force%cmxv = force%cmxv - termz*(ymid-ymc) + termy*(zmid-zmc)
!             force%cmyv = force%cmyv + termz*(xmid-xmc) - termx*(zmid-zmc)
!             force%cmzv = force%cmzv - termy*(xmid-xmc) + termx*(ymid-ymc)

              component_loop2 : do k = 1, design%function_data(j)%ncomponents

                if ( design%function_data(j)%component_data(k)%boundary_id     &
                     == 0 ) then
                  if ( .not. bcforce(ib)%add_to_total ) cycle component_loop2
                  cd   = force%cd
                  cdv  = force%cdv
                  cl   = force%cl
                  clv  = force%clv
                  cx   = force%cx
                  cy   = force%cy
                  cz   = force%cz
                  cxv  = force%cxv
                  cyv  = force%cyv
                  czv  = force%czv
                  cmx  = force%cmx
                  cmxv = force%cmxv
                  cmy  = force%cmy
                  cmyv = force%cmyv
                  cmz  = force%cmz
                  cmzv = force%cmzv
                  clcd = force%clcd
                  fom  = force%fom
                  propeff= force%propeff
                  rotor_thrust = force%rotor_thrust
                  rpowerx = force%powerx
                  rpowery = force%powery
                  rpowerz = force%powerz
                else if (design%function_data(j)%component_data(k)%boundary_id &
                         == ib ) then
                  cd   = bcforce(ib)%cd
                  cdv  = bcforce(ib)%cdv
                  cl   = bcforce(ib)%cl
                  clv  = bcforce(ib)%clv
                  cx   = bcforce(ib)%cx
                  cy   = bcforce(ib)%cy
                  cz   = bcforce(ib)%cz
                  cxv  = bcforce(ib)%cxv
                  cyv  = bcforce(ib)%cyv
                  czv  = bcforce(ib)%czv
                  cmx  = bcforce(ib)%cmx
                  cmxv = bcforce(ib)%cmxv
                  cmy  = bcforce(ib)%cmy
                  cmyv = bcforce(ib)%cmyv
                  cmz  = bcforce(ib)%cmz
                  cmzv = bcforce(ib)%cmzv
                  rpowerx = bcforce(ib)%powerx
                  rpowery = bcforce(ib)%powery
                  rpowerz = bcforce(ib)%powerz
                else
                  cycle component_loop2
                endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmxv_id )
                if (.not.design%function_data(j)%averaging) base = cmxv-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmyv_id )
                if (.not.design%function_data(j)%averaging) base = cmyv-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmzv_id )
                if (.not.design%function_data(j)%averaging) base = cmzv-target
                const = my_1/sref/bref
              case ( cx_id )
                if (.not.design%function_data(j)%averaging) base = cx-target
                const = my_1/sref
              case ( cxv_id )
                if (.not.design%function_data(j)%averaging) base = cxv-target
                const = my_1/sref
              case ( cy_id )
                if (.not.design%function_data(j)%averaging) base = cy-target
                const = my_1/sref
              case ( cyv_id )
                if (.not.design%function_data(j)%averaging) base = cyv-target
                const = my_1/sref
              case ( cz_id )
                if (.not.design%function_data(j)%averaging) base = cz-target
                const = my_1/sref
              case ( czv_id )
                if (.not.design%function_data(j)%averaging) base = czv-target
                const = my_1/sref
              case ( powerx_id )
                if (.not.design%function_data(j)%averaging)base = rpowerx-target
                const = my_1/sref
              case ( powery_id )
                if (.not.design%function_data(j)%averaging)base = rpowery-target
                const = my_1/sref
              case ( powerz_id )
                if (.not.design%function_data(j)%averaging)base = rpowerz-target
                const = my_1/sref
              case ( clcd_id )
                if (.not.design%function_data(j)%averaging) base = clcd-target
                const = my_1/sref
              case ( fom_id )
                if (.not.design%function_data(j)%averaging) base = fom-target
                const = my_1
              case ( propeff_id )
                if (.not.design%function_data(j)%averaging)base = propeff-target
                const = my_1
              case ( rotor_thrust_id )
                if (.not.design%function_data(j)%averaging) base = rotor_thrust&
                                                                  -target
                const = my_1/sref
              case default
                cycle component_loop2
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

                forcel_finaldx(:) = forceldx(:)*factor
                forcel_finaldy(:) = forceldy(:)*factor
                forcel_finaldz(:) = forceldz(:)*factor

                forced_finaldx(:) = forceddx(:)*factor
                forced_finaldy(:) = forceddy(:)*factor
                forced_finaldz(:) = forceddz(:)*factor

                cx_finaldx(:) = cxdx(:)*factor
                cx_finaldy(:) = cxdy(:)*factor
                cx_finaldz(:) = cxdz(:)*factor

                cy_finaldx(:) = cydx(:)*factor
                cy_finaldy(:) = cydy(:)*factor
                cy_finaldz(:) = cydz(:)*factor

                cz_finaldx(:) = czdx(:)*factor
                cz_finaldy(:) = czdy(:)*factor
                cz_finaldz(:) = czdz(:)*factor

                powerx_finaldx(:) = powerxdx(:)*factor
                powerx_finaldy(:) = powerxdy(:)*factor
                powerx_finaldz(:) = powerxdz(:)*factor

                powery_finaldx(:) = powerydx(:)*factor
                powery_finaldy(:) = powerydy(:)*factor
                powery_finaldz(:) = powerydz(:)*factor

                powerz_finaldx(:) = powerzdx(:)*factor
                powerz_finaldy(:) = powerzdy(:)*factor
                powerz_finaldz(:) = powerzdz(:)*factor

                cmx_finaldx(:) = cmxdx(:)*factor
                cmx_finaldy(:) = cmxdy(:)*factor
                cmx_finaldz(:) = cmxdz(:)*factor

                cmy_finaldx(:) = cmydx(:)*factor
                cmy_finaldy(:) = cmydy(:)*factor
                cmy_finaldz(:) = cmydz(:)*factor

                cmz_finaldx(:) = cmzdx(:)*factor
                cmz_finaldy(:) = cmzdy(:)*factor
                cmz_finaldz(:) = cmzdz(:)*factor

                ioff(:) = 0

                do m = 1, elem(ielem)%node_per_cell
                  node = c2n_cell(m)
                  do i = ia(node), ia(node+1)-1
                    icol = ja(i)
                    if ( icol == node ) ioff(m) = i
                  end do
                end do

                select case (design%function_data(j)%component_data(k)%name)
                case ( cl_id, clv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forcel_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forcel_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forcel_finaldz(m)
                  end do
                case ( cd_id, cdv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + forced_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + forced_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + forced_finaldz(m)
                  end do
                case ( powerx_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerx_finaldz(m)
                  end do
                case ( powery_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powery_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powery_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powery_finaldz(m)
                  end do
                case ( powerz_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + powerz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + powerz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + powerz_finaldz(m)
                  end do
                case ( cx_id, cxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cx_finaldz(m)
                  end do
                case ( cy_id, cyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cy_finaldz(m)
                  end do
                case ( cz_id, czv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cz_finaldz(m)
                  end do
                case ( cmx_id, cmxv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmx_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmx_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmx_finaldz(m)
                  end do
                case ( cmy_id, cmyv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmy_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmy_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmy_finaldz(m)
                  end do
                case ( cmz_id, cmzv_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + cmz_finaldx(m)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + cmz_finaldy(m)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + cmz_finaldz(m)
                  end do
                case ( clcd_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldx(m) - cl/cd/cd*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldy(m) - cl/cd/cd*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                  (1.0_dp/cd*forceldz(m) - cl/cd/cd*forceddz(m))
                  end do
                case ( fom_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldx(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldy(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                     (my_1p5*cl*cl/cmz/cmz*forceldz(m)/sref    &
                                      - cl*cl*cl/cmz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( propeff_id )
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdx(m)/sref        &
                                             - cz/cmz/cmz*cmzdx(m)/sref/bref)
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdy(m)/sref        &
                                             - cz/cmz/cmz*cmzdy(m)/sref/bref)
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) - factor*            &
                                                 (my_1/cmz*czdz(m)/sref        &
                                             - cz/cmz/cmz*cmzdz(m)/sref/bref)
                  end do
                case ( rotor_thrust_id )
                  sint = sin(thrust_angle)
                  cost = cos(thrust_angle)
                  do m = 1, elem(ielem)%node_per_cell
                    dfdx(1,ioff(m),j) = dfdx(1,ioff(m),j) + factor*            &
                                           (cost*forceldx(m) - sint*forceddx(m))
                    dfdx(2,ioff(m),j) = dfdx(2,ioff(m),j) + factor*            &
                                           (cost*forceldy(m) - sint*forceddy(m))
                    dfdx(3,ioff(m),j) = dfdx(3,ioff(m),j) + factor*            &
                                           (cost*forceldz(m) - sint*forceddz(m))
                  end do
                case default
                end select

              end do component_loop2

            endif local_quad

          end do quad_faces

        end do fcn_loop

  end subroutine skinfricxyzi

  include 'viscosity_law.f90'

end module dshape_cost

module turb_sa_2012

  use kinddefs,       only : dp, odp
  use lmpi,           only : lmpi_conditional_stop, lmpi_id, lmpi_master, &
                             lmpi_reduce, lmpi_bcast, lmpi_max_and_maxid
  use info_depr,      only : twod, ngrid, skeleton, grad_x_y_z_contents, &
                             tightly_couple, ntt, mixed
  use turbulence_info, only : turbulence_model_int
  use periodics,      only : periodic
  use turb_sa_const,  only : ddes, cdes, vkar, use_edwards_mod
  use turb_kw_const,  only : turb_compress_model
  use thermo,         only : conserved_q_type, primitive_q_type, q_type
  use fun3d_maximums, only : ngrid_max
  use allocations,    only : my_alloc_ptr
  use bc_types,       only : bcgrid_type
  use element_types,  only : elem_type
  use twod_util,      only : yplane_2d, y_coplanar_tol

  use grid_types,      only : grid_type
  use solution_types,  only : soln_type, compressible, incompressible
  use comprow_types,   only : crow_flow
  use cfl_defs, only : hanim
  use debug_defs,      only : sa_diffusion_eto,                             &
                              fraction_allowable_source,                    &
                              weighted_lsq_source_t, diff_edge_avg_t
  use ddt,             only : ddt3
  use scalar_diffusion,only : check_diagonal_sign
  use cfl_defs,        only : cnld_positive_diagonal_warnings

  use turb_convection, only : turb_resid_conv, turb_jacob_conv
  use wu_defs,         only : times

  use turb_parameters, only : t_diff1, t_diff2, t_diff3, t_diff4

  use turbulence_info, only : des, des_neg

  implicit none

  private

  public :: residual_sa_2012
  public :: jacobian_sa_2012

  public :: debug_vt, constant_rnu_source, constant_rnu_diffusion
  public :: s_type, s_terms

  integer :: view_diag_turb_at_levels = 0 !1,2,3

  type s_type
    real(dp), dimension(:), pointer :: dsdvt
    real(dp), dimension(:), pointer :: s_diag
  end type s_type

  type(s_type),    dimension(:), allocatable :: s_terms

  logical                       :: very_first_time_through = .true.
  logical, dimension(ngrid_max) ::      first_time_residual = .true.

  ! To check by overwriting source term with combinations of 9 gradients.
  integer, dimension(9) :: debug_vt = 0

  ! To check by evaluating source term with constant rnu.
  logical :: constant_rnu_source = .false.

  ! To check by evaluating diffusion term with constant rnu.
  logical :: constant_rnu_diffusion = .false.

  real(dp), dimension(:), allocatable :: s11deriv, s12deriv
  real(dp), dimension(:), allocatable :: s13deriv, s22deriv
  real(dp), dimension(:), allocatable :: s23deriv, s33deriv

  logical :: sderivs_allocated = .false.

  logical :: residual_piece = .true.
  logical :: jacobian_piece = .true.

  logical :: absoft_dummy

contains

!=============================== RESIDUAL_SA_2012 ============================80
!
! Driver routine for residual evaluation for Spalart-Allmaras model
!
!=============================================================================80

  subroutine residual_sa_2012(grid, soln)

    use turb_bc_nc,      only : turb_bc_residual_nc
    use gradient_driver, only : grad_variable
    use turb_parameters, only : turbulent_convection

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln

    integer :: n_sta, fl

    character(len=80) :: variable, gradient

  continue

    fl = grid%igrid

    if ( skeleton > 0 ) then
      write(*,"(1x,2a,L1,a,i0,a,i0)") 'S-A residuals Turbulence Modeling',&
     'Resource...tightly_couple=',tightly_couple,' fl=',fl,               &
     ' diff_edge_avg_t=',diff_edge_avg_t
    endif

    if ( use_edwards_mod ) then
      call lmpi_conditional_stop(1,'use_edwards_mod:residual_sa_2012')
    endif

    if ( very_first_time_through ) then
      allocate( s_terms(ngrid) )
      very_first_time_through = .false.
    endif

    if ( first_time_residual(fl) .and. tightly_couple ) then

      call my_alloc_ptr( s_terms(fl)%dsdvt,  soln%dof0 )
      call my_alloc_ptr( s_terms(fl)%s_diag, soln%dof0 )

    elseif ( first_time_residual(fl) ) then

      call my_alloc_ptr( s_terms(fl)%dsdvt,          1 )
      call my_alloc_ptr( s_terms(fl)%s_diag, soln%dof0 )

    endif

    if ( turbulent_convection /= 0 ) then
      variable = 'turbulence'
      gradient = 'least-squares'
      call grad_variable(grid, soln, variable, gradient )
    endif

    if ( .not.tightly_couple ) then

      n_sta = 1
      call sa_resid_conv_diff( soln%viscous_method,                    &
           fl,    n_sta,         soln%turbres,                         &
           soln%eqn_set,  soln%dof0,     grid%nnodes0,                 &
           grid%nedgeloc, grid%eptr,                                   &
           soln%turb,     soln%q_dof,                                  &
           soln%gradx,    soln%grady,   soln%gradz,                    &
           grid%xn,       grid%yn,      grid%zn,       grid%ra,        &
           grid%x,        grid%y,       grid%z,        grid%facespeed, &
           soln%n_turb,                 soln%n_grd,                    &
           grid%nelem,    grid%elem,    grid%nedgeloc_2d,              &
           soln%amut )

      if ( lmpi_master .and. first_time_residual(fl) ) then
        if ( soln%viscous_method == 0 ) then
          write(*,"(1x,3a,i0)") 'Turbulent diffusion : gradients via : ',&
          'Green-Gauss (element assembly)',' fl=',fl
        elseif ( soln%viscous_method == 2 ) then
          write(*,"(1x,3a,i0)") 'Turbulent diffusion : gradients via : ',&
          'Edge-Terms-Only (inconsistent)',' fl=',fl
        else
          write(*,"(1x,3a,i0)") 'Turbulent diffusion : gradients via : ',&
          trim(grad_x_y_z_contents),' fl=',fl
        endif
      endif

      if ( mixed .and. weighted_lsq_source_t ) then

        if( grad_x_y_z_contents /= 'viscous weighted-least-squares' ) then
          variable = 'viscous'
          gradient = 'weighted-least-squares'
          call grad_variable(grid, soln, variable, gradient)
        endif

      elseif ( grid%origin > 1 ) then !agglomerated_grids

        if( grad_x_y_z_contents /= 'viscous weighted-least-squares' ) then
          variable = 'viscous'
          gradient = 'weighted-least-squares'
          call grad_variable(grid, soln, variable, gradient)
        endif

      elseif ( mixed .and. soln%viscous_method > 0 ) then !mixed elements

        if( grad_x_y_z_contents /= 'viscous weighted-least-squares' .and. &
            grad_x_y_z_contents /= 'turbgrad' ) then
          variable = 'viscous'
          gradient = 'weighted-least-squares'
          call grad_variable(grid, soln, variable, gradient)
        endif

      end if

      if ( lmpi_master .and.  first_time_residual(fl) )             &
      write(*,"(1x,3a,i0)") 'Turbulent sources : gradients via : ', &
      trim(grad_x_y_z_contents),' fl=',fl

      if ( residual_piece ) call times('Turb-Res-BP-source',fl)
      call sa_resid_source( n_sta,         soln%turbres,               &
           soln%eqn_set,  soln%dof0,     grid%nnodes0, grid%nnodes01,  &
           grid%nedgeloc, grid%eptr,                                   &
           soln%turb,     soln%q_dof,   grid%slen,                     &
           soln%gradx,    soln%grady,   soln%gradz,    grid%vol,       &
           grid%xn,       grid%yn,      grid%zn,       grid%ra,        &
           grid%x,        grid%y,       grid%z,                        &
           soln%n_tot,    soln%n_grd,                                  &
           grid%nelem,    grid%elem,    grid%nedgeloc_2d,              &
           grid%iflagslen, grid%des_slen, grid%dxdt, grid%dydt,        &
           grid%dzdt, grid%nbound, grid%bc, grid%node_pairs_2d,        &
           grid%nnodes0_2d )
       if ( residual_piece ) call times('Turb-Res-BP-source',fl)

    else

      ! grad_x_y_z contents need to be unweighted least squares here.

      n_sta = soln%n_q
      call sa_resid_conv_diff( soln%viscous_method,                    &
           fl,    n_sta,             soln%res,                         &
           soln%eqn_set,  soln%dof0,     grid%nnodes0,                 &
           grid%nedgeloc, grid%eptr,                                   &
           soln%turb,     soln%q_dof,                                  &
           soln%gradx,    soln%grady,   soln%gradz,                    &
           grid%xn,       grid%yn,      grid%zn,       grid%ra,        &
           grid%x,        grid%y,       grid%z,        grid%facespeed, &
           soln%n_turb,                 soln%n_grd,                    &
           grid%nelem,    grid%elem,    grid%nedgeloc_2d,              &
           soln%amut )

      if ( grid%origin > 1 ) call lmpi_conditional_stop(1,&
      'tightly-coupled agglom:residual_sa_2012')

      if ( residual_piece ) call times('Turb-Res-BP-source',fl)
      call sa_resid_source( n_sta,           soln%res,                 &
           soln%eqn_set,  soln%dof0,     grid%nnodes0, grid%nnodes01,  &
           grid%nedgeloc, grid%eptr,                                   &
           soln%turb,     soln%q_dof,   grid%slen,                     &
           soln%gradx,    soln%grady,   soln%gradz,    grid%vol,       &
           grid%xn,       grid%yn,      grid%zn,       grid%ra,        &
           grid%x,        grid%y,       grid%z,                        &
           soln%n_tot,    soln%n_grd,                                  &
           grid%nelem,    grid%elem,    grid%nedgeloc_2d,              &
           grid%iflagslen, grid%des_slen, grid%dxdt, grid%dydt,        &
           grid%dzdt, grid%nbound, grid%bc, grid%node_pairs_2d,        &
           grid%nnodes0_2d )
       if ( residual_piece ) call times('Turb-Res-BP-source',fl)

    end if

    first_time_residual(fl) = .false.

    if ( skeleton > 1 ) write(*,*) 'Calling turb_bc_residual_bc.....'
    call turb_bc_residual_nc( grid, soln ) !generalized_turbulence_bc

    residual_piece = .false.

  end subroutine residual_sa_2012


!================================= JACOBIAN_SA ===============================80
!
! Driver routine for jacobians of Spalart-Allmaras model.
!
!=============================================================================80

  subroutine jacobian_sa_2012(grid, soln, crow )

    use turb_bc_nc,      only : turb_bc_jacobian_nc, periodic_lhs_t
    use thermo,          only : etop
    use timeacc,         only : time_diag_nc, set_dtau
    use debug_defs,      only : composite_jacobian_lhs, allow_dt
    use bc_strong,       only : turbulent_jacobian_strong_nc
    use scratch_q,       only : set_temp_q, recover_q
    use nml_code_run_control,  only : debug_jupdate

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: n_sta, warnings, fl

  continue

    fl = grid%igrid

    if ( ( skeleton > 0 .or. debug_jupdate ) .and. lmpi_master ) then
      write(*,"(1x,2a,L1,a,i0)")                               &
      'JUPDATE: S-A Jacobians Turbulence Modeling Resource...',&
      'tightly_couple=',tightly_couple,' grid=',fl
    endif

    if ( soln%eqn_set /= incompressible ) then
      call set_temp_q( 1, grid%cc, 1, fl, soln, 'jacobian_sa_2012')
      call etop(grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set)
    endif

    if ( use_edwards_mod ) then
      call lmpi_conditional_stop(1,'use_edwards_mod:jacobian_sa_2012')
    endif

    if ( .not.tightly_couple ) then

      n_sta = 1
      call sa_jacob_cnld( soln%viscous_method, fl,                           &
                n_sta, soln%a_turb_diag, soln%a_turb_off, soln%dtau,         &
                soln%eqn_set, soln%dof0, grid%nnodes0,  grid%nnodes01,       &
                grid%nedgeloc,                                               &
                grid%eptr, soln%turb, soln%q_dof, grid%slen, soln%gradx,     &
                soln%grady, soln%gradz, grid%vol, grid%xn, grid%yn,          &
                grid%zn, grid%ra,                                            &
                crow%fhelp,                                                  &
                grid%facespeed, soln%n_turb, soln%n_tot,                     &
                crow%g2m, grid%nedgeloc_2d, grid%nelem,                      &
                grid%elem, grid%x, grid%y, grid%z,                           &
                grid%dxdt, grid%dydt, grid%dzdt, crow%ia, crow%ja,           &
                crow%nzg2m, grid%nbound, grid%bc, grid%symmetry,             &
                grid%iflagslen, grid%des_slen, grid%node_pairs_2d,           &
                grid%nnodes0_2d, soln%n_grd, soln%amut )

    else

      n_sta = soln%n_q
      call sa_jacob_cnld( soln%viscous_method, fl,                           &
                n_sta, soln%a_diag, soln%a_off, soln%dtau,                   &
                soln%eqn_set, soln%dof0, grid%nnodes0,  grid%nnodes01,       &
                grid%nedgeloc,                                               &
                grid%eptr, soln%turb, soln%q_dof, grid%slen, soln%gradx,     &
                soln%grady, soln%gradz, grid%vol, grid%xn, grid%yn,          &
                grid%zn, grid%ra, crow%fhelp,                                &
                grid%facespeed, soln%n_turb, soln%n_tot,                     &
                crow%g2m, grid%nedgeloc_2d, grid%nelem,                      &
                grid%elem, grid%x, grid%y, grid%z,                           &
                grid%dxdt, grid%dydt, grid%dzdt, crow%ia, crow%ja,           &
                crow%nzg2m, grid%nbound, grid%bc, grid%symmetry,             &
                grid%iflagslen, grid%des_slen, grid%node_pairs_2d,           &
                grid%nnodes0_2d, soln%n_grd, soln%amut )
    endif

    call turb_bc_jacobian_nc( grid, soln, crow ) !generalized_turbulence_bc

    ! Add in the source terms now that weak boundary terms are added.

    if ( tightly_couple ) then
      call add_s_diag( fl, n_sta, soln%dof0, soln%a_diag,         &
                       grid%slen, soln%dtau, crow%g2m )
    else
      call add_s_diag( fl, n_sta, soln%dof0, soln%a_turb_diag,         &
                       grid%slen, soln%dtau, crow%g2m )
    endif

    if ( soln%eqn_set /= incompressible ) then
      call recover_q( 1, grid%cc, 1, fl, soln )
      q_type = conserved_q_type
    endif

    if ( .not.composite_jacobian_lhs .and. allow_dt ) then

      if ( .not.tightly_couple ) then

        call time_diag_nc(fl, soln%eqn_set,                                &
                     soln%dof0, 1, soln%n_turb,                            &
                     soln%q_dof, soln%a_turb_diag, grid%vol, soln%cdt,     &
                     .true., grid%x, grid%y, grid%z,                       &
                     soln%gradx, soln%grady, soln%gradz, crow%ia, crow%ja, &
                     crow%g2m)

      elseif ( tightly_couple ) then

         call time_diag_nc(fl, soln%eqn_set,                               &
                     soln%dof0, n_sta, soln%n_turb,                        &
                     soln%q_dof, soln%a_diag, grid%vol, soln%cdt,          &
                     .true., grid%x, grid%y, grid%z,                       &
                     soln%gradx, soln%grady, soln%gradz, crow%ia, crow%ja, &
                     crow%g2m)

      endif
      if ( hanim ) then
        call set_dtau( soln%dof0, grid%volq, soln%cdt,           &
                       crow%g2m, soln%dtau )
      endif

    end if

    ! Enforce strong boundary conditions.

    call turbulent_jacobian_strong_nc( grid, soln, crow )

    if ( periodic ) call periodic_lhs_t(grid, soln, crow)

    if ( tightly_couple ) then

      call check_diagonal_sign( fl, n_sta, soln%dof0, soln%a_diag,             &
           crow%g2m, grid%x, grid%y, grid%z, grid%slen, soln%dtau, soln%turb,  &
           warnings, 'End J' )

    else

      call check_diagonal_sign( fl, n_sta, soln%dof0, soln%a_turb_diag,        &
           crow%g2m, grid%x, grid%y, grid%z, grid%slen, soln%dtau, soln%turb,  &
           warnings, 'End J' )

    endif

    jacobian_piece = .false.

  end subroutine jacobian_sa_2012

!================================= SA_RESID_CONV_DIFF ========================80
!
! Residual convection and nonlinear diffusion for Spalart's model
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_resid_conv_diff( viscous_method,                               &
                           fl, n_sta, res, eqn_set,                            &
                           dof0, nnodes0, nedgeloc, eptr, turb,                &
                           qnode, gradx, grady, gradz, xn, yn, zn,             &
                           ra, x, y, z, facespeed,                             &
                           n_turb, n_grd, nelem, elem, nedgeloc_2d, amut )

    use turbulence_info, only : turbulence_model_is_conservative
    use turb_diffusion,  only : turb_resid_diff_element                        &
                              , get_diffusion_coefficients
    use turb_diffusion5, only : turb_resid_diff_element5
    use turb_diffusion6, only : turb_resid_diff_element6

    integer, intent(in) :: viscous_method, fl, n_sta
    integer, intent(in) :: dof0, nnodes0, n_grd
    integer, intent(in) :: eqn_set, nedgeloc, n_turb, nelem, nedgeloc_2d

    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr

    real(dp),  dimension(:,:),      intent(in)    :: turb
    real(dp),  dimension(:,:),      intent(in)    :: qnode
    real(dp),  dimension(:,:),      intent(inout) :: res
    real(dp),  dimension(:,:),      intent(in)    :: gradx, grady, gradz
    real(dp),  dimension(:),        intent(in)    :: x, y, z
    real(dp),  dimension(nedgeloc), intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc), intent(in)    :: facespeed

    type(elem_type), dimension(nelem), intent(in) :: elem
    real(dp),  dimension(:),        intent(in)    :: amut

    real(dp),  dimension(n_turb)   :: diff_const
    real(dp),  dimension(n_turb)   :: diff_const2

    integer :: i, ielem, min_loc, max_loc, ierr

    real(dp) :: min_val, max_val

  continue

    absoft_dummy = turbulence_model_is_conservative ( turbulence_model_int )
    if  (absoft_dummy ) &
    call lmpi_conditional_stop(1,'not_implemented:sa_resid_conv_diff')

    if ( ngrid > 1 ) then

      min_loc = 0           ; max_loc = 0
      min_val = huge(1._dp) ; max_val =-huge(1._dp)

      do i=1,dof0
        if ( turb(1,i) < min_val ) then
          min_val = turb(1,i) ; min_loc = i
        endif
        if ( turb(1,i) > max_val ) then
          max_val = turb(1,i) ; max_loc = i
        endif
      end do

      ierr = 0
      if ( max_loc == 0 .or. min_loc == 0 ) ierr = 1
      call lmpi_conditional_stop(ierr,'NaN:sa_resid_conv_diff')

      if ( view_diag_turb_at_levels > 20 ) then
        write(7000+lmpi_id,"(1x,2(a,i10),2(a,4e20.10))")          &
        ' fl=',fl,' dof0=',dof0,                                  &
        ' turb-min/x/z=',min_val,x(min_loc),y(min_loc),z(min_loc),&
        ' turb-max/x/z=',max_val,x(max_loc),y(max_loc),z(max_loc)
      endif

      if ( view_diag_turb_at_levels > 2 .and. &
           real(min_val,dp) < 1.0e-10_dp ) then
        write(8000+lmpi_id,"(1x,2(a,i10),2(a,4e20.10))")          &
        ' fl=',fl,' dof0=',dof0,                                  &
        ' turb-min/x/z=',min_val,x(min_loc),y(min_loc),z(min_loc),&
        ' turb-max/x/z=',max_val,x(max_loc),y(max_loc),z(max_loc)
      endif

    endif

    select case ( eqn_set )
    case ( compressible )
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,'q_type not primitive:sa_resid_conv_diff')
    case ( incompressible )
    case default
      call lmpi_conditional_stop(1,'eqn_set:sa_resid_conv_diff')
    end select

    ! Convection edge-assembled.

    if ( residual_piece ) call times('Turb-Res-BP-convection',fl)
    call turb_resid_conv( turbulence_model_int,                              &
                          nnodes0, nedgeloc, eptr, turb,                     &
                          qnode, res, xn, yn, zn, ra, facespeed,             &
                          n_turb, n_sta, nedgeloc_2d,                        &
                          x, y, z, gradx, grady, gradz )
    if ( residual_piece ) call times('Turb-Res-BP-convection',fl)

    ! Diffusion terms via element types

    call get_diffusion_coefficients( turbulence_model_int, n_turb, &
                                     diff_const, diff_const2 )

    if ( .not.sa_diffusion_eto .and. viscous_method == 0 ) then

      ! Diffusion terms via element-based integration:

      ! There are 3 diffusion paths:
      ! 1 for diff_edge_avg_t <= 1 (uses general turbulence routines)
      ! 2 for diff_edge_avg_t == 2 (one 2d and one 3d - both 1-eqn)
      ! called in a non-tet loop and a tet-only loop for timing purposes.

      if ( diff_edge_avg_t < 2 ) then !generalized turbulence routines

        if ( residual_piece ) call times('Turb-Res-BP-diffusion-tet',fl)
        do ielem = 1, nelem

          if ( elem(ielem)%type_cell /= 'tet') cycle

          call turb_resid_diff_element ( n_sta, eqn_set, nnodes0,              &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%local_f2e, elem(ielem)%e2n_2d,        &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, elem(ielem)%face_2d, elem(ielem)%chk_norm,&
                             turbulence_model_int, amut,                       &
                             diff_const, diff_const2 )
        enddo
        if ( residual_piece ) call times('Turb-Res-BP-diffusion-tet',fl)


        if ( residual_piece ) call times('Turb-Res-BP-diffusion-nontet',fl)
        do ielem = 1, nelem

          if ( elem(ielem)%type_cell == 'tet') cycle

          call turb_resid_diff_element ( n_sta, eqn_set, nnodes0,              &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%local_f2e, elem(ielem)%e2n_2d,        &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, elem(ielem)%face_2d, elem(ielem)%chk_norm,&
                             turbulence_model_int, amut,                       &
                             diff_const, diff_const2 )
        enddo
        if ( residual_piece ) call times('Turb-Res-BP-diffusion-nontet',fl)

      else !edge-averaged viscosity
           !n_turb=1 routines

        if ( residual_piece ) then
          if ( twod ) then
            call times('Turb-Res-BP-diffusion5-tet',fl)
          else
            call times('Turb-Res-BP-diffusion6-tet',fl)
          endif
        endif
        do ielem = 1, nelem

          if ( elem(ielem)%type_cell /= 'tet') cycle

          if ( twod ) then
          call turb_resid_diff_element5 ( n_sta, eqn_set, nnodes0,             &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%local_f2e, elem(ielem)%e2n_2d,        &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, elem(ielem)%face_2d,                      &
                             elem(ielem)%big_angle,                            &
                             diff_const, diff_const2 )
          else
          call turb_resid_diff_element6 ( n_sta, eqn_set, nnodes0,             &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, elem(ielem)%big_angle,                    &
                             diff_const, diff_const2 )
          endif
        enddo
        if ( residual_piece ) then
          if ( twod ) then
            call times('Turb-Res-BP-diffusion5-tet',fl)
          else
            call times('Turb-Res-BP-diffusion6-tet',fl)
          endif
        endif

        if ( residual_piece ) then
          if ( twod ) then
            call times('Turb-Res-BP-diffusion5-nontet',fl)
          else
            call times('Turb-Res-BP-diffusion6-nontet',fl)
          endif
        endif
        do ielem = 1, nelem

          if ( elem(ielem)%type_cell == 'tet') cycle

          if ( twod ) then
          call turb_resid_diff_element5 ( n_sta, eqn_set, nnodes0,             &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%local_f2e, elem(ielem)%e2n_2d,        &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, elem(ielem)%face_2d,                      &
                             elem(ielem)%big_angle,                            &
                             diff_const, diff_const2 )
          else
          call turb_resid_diff_element6 ( n_sta, eqn_set, nnodes0,             &
                             turb, qnode, res,                                 &
                             elem(ielem)%ncell,                                &
                             elem(ielem)%c2n, x, y, z,                         &
                             elem(ielem)%local_f2n, elem(ielem)%local_e2n,     &
                             elem(ielem)%face_per_cell,                        &
                             elem(ielem)%node_per_cell,                        &
                             elem(ielem)%edge_per_cell, elem(ielem)%type_cell, &
                             n_turb, elem(ielem)%big_angle,                    &
                             diff_const, diff_const2 )
          endif

        enddo
        if ( residual_piece ) then
          if ( twod ) then
            call times('Turb-Res-BP-diffusion5-nontet',fl)
          else
            call times('Turb-Res-BP-diffusion6-nontet',fl)
          endif
        endif

      endif

      ! Diffusion terms via edge-based integration.

    elseif ( sa_diffusion_eto .or. viscous_method == 2 ) then

      call sa_resid_diff_eto( n_sta, eqn_set, nnodes0,                         &
                              nedgeloc, eptr, turb, qnode, res,                &
                              xn, yn, zn, ra, x, y, z,                         &
                              nedgeloc_2d, n_turb )

    elseif ( viscous_method == 1 ) then

      ! Diffusion terms via edge-based integration with avg-lsq gradient.

      call sa_resid_diff_avglsq(                                               &
                             n_sta, eqn_set, nnodes0,                          &
                             nedgeloc, eptr, turb, qnode, res,                 &
                             gradx, grady, gradz, xn, yn, zn, ra, x, y, z,     &
                             nedgeloc_2d, n_turb, n_grd )

    else

      call lmpi_conditional_stop(1,'Invalid:sa_resid_conv_diff')

    endif

  end subroutine sa_resid_conv_diff

!================================= SA_RESID_SOURCE ===========================80
!
! Residual source terms for Spalart's model
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_resid_source( n_sta, res, eqn_set,                             &
                           dof0, nnodes0, nnodes01, nedgeloc, eptr, turb,      &
                           qnode, slen, gradx, grady, gradz, vol, xn, yn, zn,  &
                           ra, x, y, z,                                        &
                           n_tot, n_grd, nelem, elem, nedgeloc_2d,             &
                           iflagslen, des_slen, dxdt, dydt, dzdt, nbound, bc,  &
                           node_pairs_2d, nnodes0_2d )

    use info_depr,       only : tref, xmach, re
    use nml_two_d_trans, only : turb_transition
    use fluid,           only : gamma, sutherland_constant
    use turb_util,       only : strain_tensor_deriv
    use turb_sa_const,   only : sarc, sarc_cr3
    use bc_types,        only : bcgrid_type

    integer, intent(in) :: n_sta
    integer, intent(in) :: dof0, nnodes0, nnodes01, n_tot, n_grd
    integer, intent(in) :: eqn_set, nedgeloc, nelem, nedgeloc_2d
    integer, intent(in) :: nbound, nnodes0_2d

    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr
    integer, dimension(2,nnodes0_2d), intent(in)  :: node_pairs_2d

    real(dp),  dimension(:,:),      intent(in)    :: turb
    real(dp),  dimension(:,:),      intent(in)    :: qnode
    real(dp),  dimension(:,:),      intent(inout) :: res
    real(dp),  dimension(:),        intent(in)    :: slen, des_slen
    real(dp),  dimension(:,:),      intent(in)    :: gradx, grady, gradz
    real(dp),  dimension(:),        intent(in)    :: x, y, z, vol
    real(dp),  dimension(:),        intent(in)    :: dxdt, dydt, dzdt
    real(dp),  dimension(nedgeloc), intent(in)    :: xn, yn, zn, ra
    integer, dimension(:), intent(in) :: iflagslen

    type(elem_type), dimension(nelem), intent(in) :: elem
    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc

    integer :: i, ierr, ierr_ddt

    real(dp)    :: xis, xisabs, rstar, rtilde, fr1
    real(dp)    :: s11,s12,s13,s22,s23,s33,w12,w13,w23

    real(dp) :: cstar
    real(dp) :: p, rho, rnu, source, a_sq, added_dest_turbsq
    real(dp) :: my_xmach, xmre, rhoinv, vt
    real(dp) :: distance, rd, fd, velterm
    logical  :: zero_prod

    type(ddt3) :: source_ddt

    logical :: des_code, turb_compress_model_code

  continue

    des_code = .false.
    if ( ( turbulence_model_int == des )                                       &
    .or. ( turbulence_model_int == des_neg ) ) des_code = .true.

    turb_compress_model_code = .false.
    if ( turb_compress_model == 'ssz' ) turb_compress_model_code = .true.

    velterm = 0._dp

    ierr_ddt = 0

    my_xmach = 0._dp
    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,'q_type not primitive:sa_resid_source')
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:sa_resid_source')
    end select

!   For SARC only, need to get D(Sij)/Dt terms (ignoring time deriv for now)
!   = u_k*d(Sij)/dx_k (summing over k)

    if (sarc) then
      if ( .not. sderivs_allocated ) then
        allocate(s11deriv(nnodes01))
        allocate(s12deriv(nnodes01))
        allocate(s13deriv(nnodes01))
        allocate(s22deriv(nnodes01))
        allocate(s23deriv(nnodes01))
        allocate(s33deriv(nnodes01))
        sderivs_allocated = .true.
      endif
      call strain_tensor_deriv(eqn_set,                                        &
                   nnodes0, nnodes01, nedgeloc, eptr, qnode,                   &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d,                     &
                   nelem, elem, n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,    &
                   s11deriv, s12deriv, s13deriv, s22deriv, s23deriv, s33deriv)
    end if

    ! Source term.

    xmre  = my_xmach / re
    cstar = sutherland_constant / tref
    do i = 1, dof0

      if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

      rho    = 1._dp
      rhoinv = 1._dp
      a_sq   = 1._dp
      if ( eqn_set == compressible ) then
        rho  = qnode(1,i)
        rhoinv = 1._dp/rho
        p    = qnode(5,i)
        a_sq = gamma * p * rhoinv
        rnu  = viscosity_law( cstar, a_sq ) * rhoinv
      else
        rnu  = 1._dp
      end if

      if ( constant_rnu_source ) rnu = 1._dp

      if ( des_code .or. turb_compress_model_code ) then
        velterm = des_velterm( gradx(2,i), grady(2,i), gradz(2,i), &
                               gradx(3,i), grady(3,i), gradz(3,i), &
                               gradx(4,i), grady(4,i), gradz(4,i) )
      endif

      distance = slen(i)
      fd = 0._dp
      if ( des_code ) then
        if ( ddes ) then  ! Delayed DES (DDES) (TCFD 20:181-195 2006)
          rd = turb(1,i)*my_xmach/                                           &
               (sqrt(velterm)*vkar*vkar*slen(i)*slen(i)*re)
          fd = 1._dp - tanh((8.0_dp*rd)*(8.0_dp*rd)*(8.0_dp*rd))
          distance = slen(i) - fd*max(0._dp, slen(i)-cdes*des_slen(i))
        else  ! Standard DES
          distance = min(slen(i),cdes*des_slen(i))
        end if
      end if

      !...velocity term contribution to source.
      vt = sa_source_s( gradx(2,i), grady(2,i), gradz(2,i), &
                        gradx(3,i), grady(3,i), gradz(3,i), &
                        gradx(4,i), grady(4,i), gradz(4,i), fd )

      zero_prod = .false.
      if ( turb_transition ) then ! test for laminar node
        if ( iflagslen(i) < 0 ) zero_prod = .true.
      end if

      added_dest_turbsq = 0._dp
      if ( turb_compress_model_code ) then
        ! compressibility - aiaa 95-0863 Shur et al.
        added_dest_turbsq = 3.5_dp*xmre*rho*velterm/(a_sq)
      endif

      fr1 = 1.0_dp
!     SARC term:
      if (sarc) then
!       Determine s(i,j) and w(i,j)
        s11 = 0.5_dp*(gradx(2,i) + gradx(2,i))
        s12 = 0.5_dp*(grady(2,i) + gradx(3,i))
        s13 = 0.5_dp*(gradz(2,i) + gradx(4,i))
        s22 = 0.5_dp*(grady(3,i) + grady(3,i))
        s23 = 0.5_dp*(gradz(3,i) + grady(4,i))
        s33 = 0.5_dp*(gradz(4,i) + gradz(4,i))
        w12 = 0.5_dp*(grady(2,i) - gradx(3,i))
        w13 = 0.5_dp*(gradz(2,i) - gradx(4,i))
        w23 = 0.5_dp*(gradz(3,i) - grady(4,i))
        xis = s11*s11 + s22*s22 + s33*s33 + 2._dp*s12*s12 + 2._dp*s13*s13 +    &
              2._dp*s23*s23
        xisabs = sqrt(2._dp*xis)
        rstar=xisabs/vt
        rtilde=2._dp/(0.5_dp*(vt**2+xisabs**2))**2*                            &
                  ( -w12*s12deriv(i)*(s11-s22)                                 &
                    -w13*s13deriv(i)*(s11-s33)                                 &
                    -w23*s23deriv(i)*(s22-s33)                                 &
                    +s12*(-w12*(s22deriv(i)-s11deriv(i))                       &
                               -w13*s23deriv(i)-w23*s13deriv(i))               &
                    +s13*(-w13*(s33deriv(i)-s11deriv(i))                       &
                               -w12*s23deriv(i)+w23*s12deriv(i))               &
                    +s23*(-w23*(s33deriv(i)-s22deriv(i))                       &
                               +w12*s13deriv(i)+w13*s12deriv(i)) )
        fr1=4._dp*rstar/(1._dp+rstar)*(1._dp-sarc_cr3*atan(12._dp*rtilde))-1._dp
      end if

      source_ddt = sa_source_ddt( xmre, distance, turb(1,i), vt, rnu, &
                                  zero_prod, added_dest_turbsq, fr1 )

      source  = source_ddt%f

      source = -vol(i)*source

      if ( sum(debug_vt) > 0 ) then
        source =                                                             &
         debug_vt(1)*gradx(2,i)+debug_vt(4)*gradx(3,i)+debug_vt(7)*gradx(4,i)&
        +debug_vt(2)*grady(2,i)+debug_vt(5)*grady(3,i)+debug_vt(8)*grady(4,i)&
        +debug_vt(3)*gradz(2,i)+debug_vt(6)*gradz(3,i)+debug_vt(9)*gradz(4,i)
      endif

      res(n_sta,i) = res(n_sta,i) + source

    end do

    call lmpi_conditional_stop(ierr_ddt,"ERROR_SOURCE_DDT:sa_resid_source")

  end subroutine sa_resid_source

!================================= SA_RESID_DIFF_ETO =========================80
!
! Diffusion residual for edge-based (edge-terms-only) formulation.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_resid_diff_eto( n_sta, eqn_set, nnodes0,                       &
                               nedgeloc, eptr, turb, qnode, res,               &
                               xn, yn, zn, ra, x, y, z,                        &
                               nedgeloc_2d, n_turb )

    use kinddefs,          only : dp
    use info_depr,         only : tref, xmach, re
    use turb_sa_const,     only : sig, cb2
    use fluid,             only : gamma, sutherland_constant
    use debug_defs,        only : gradient_construction_rhs

    integer, intent(in) :: n_sta, eqn_set, n_turb
    integer, intent(in) :: nedgeloc, nedgeloc_2d, nnodes0

    integer,  dimension(2,nedgeloc), intent(in)    :: eptr
    real(dp), dimension(:),          intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),   intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),   intent(in)    :: ra
    real(dp), dimension(:,:),        intent(in)    :: turb
    real(dp), dimension(:,:),        intent(in)    :: qnode
    real(dp), dimension(:,:),        intent(inout) :: res

    integer :: n, nedge_flux_eval, node1, node2, nn

    real(dp) :: xmre, xmre_s
    real(dp) :: aturb1, aturb2
    real(dp) :: cb20, cb2s, turb1, turb2
    real(dp) :: cstar
    real(dp) :: phi, rnu1, rnu2, rho1, rho2
    real(dp) :: my_xmach
    real(dp) :: ex, ey, ez, disi
    real(dp) :: tx, ty, tz, egradt

    real(dp), dimension(3)      :: augment_weight
    real(dp), dimension(n_turb) :: ngradt

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0._dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:sa_resid_diff_eto')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb2s = t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre / sig

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

    if ( skeleton > 0 ) then
      write(*,*) ' Edge-based residuals of turbulent diffusion.'
      write(*,*) ' Edge-terms-only (formally inconsistent).'
    endif

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      nn = n_turb

      ! rnu = laminar viscosity / density

      rho1   = 1._dp
      rho2   = 1._dp
      rnu1   = 1._dp
      rnu2   = 1._dp
      if ( eqn_set == compressible ) then
        rho1 = qnode(1,node1)
        rho2 = qnode(1,node2)
        rnu1 = viscosity_law( cstar, gamma*qnode(5,node1)/rho1 ) / rho1
        rnu2 = viscosity_law( cstar, gamma*qnode(5,node2)/rho2 ) / rho2
      end if

      if ( constant_rnu_diffusion ) rnu1 = 1._dp
      if ( constant_rnu_diffusion ) rnu2 = 1._dp

      turb1 = turb(nn,node1)
      turb2 = turb(nn,node2)

      aturb1 = sa0_turb_abs( turb1, rnu1 )
      aturb2 = sa0_turb_abs( turb2, rnu2 )

      phi = t_diff1*0.5_dp*(  rnu1 +   rnu2) &
          + t_diff2*0.5_dp*(aturb1 + aturb2) &
          +    cb20*0.5_dp*( turb1 +  turb2)

      ! ex, ey, ez is unit vector along edge direction

      ex   = x(node2) - x(node1)
      ey   = y(node2) - y(node1)
      ez   = z(node2) - z(node1)
      disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex   = ex*disi
      ey   = ey*disi
      ez   = ez*disi

      ! directional gradients along edge

      egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

      augment_weight = edge_augment_weight( gradient_construction_rhs,      &
                                            ex, ey, ez, xn(n), yn(n), zn(n) )

      tx = egradt*augment_weight(1)
      ty = egradt*augment_weight(2)
      tz = egradt*augment_weight(3)

      ! turbulent diffusion contribution at dual face [ two terms ]

      !  [area]*[nondimensionalization factor : Mach / Re / sigma ]*
      !         [normal gradient] at dual face

      ! Note no positivity required since cb2 > -1

      ngradt(nn) = xmre_s*ra(n)*( tx*xn(n) + ty*yn(n) + tz*zn(n) )

      if ( node1 <= nnodes0 ) then
        res(n_sta,node1) = res(n_sta,node1) - ( phi - cb2s*turb1 )*ngradt(nn)
      end if

      if ( node2 <= nnodes0 ) then
        res(n_sta,node2) = res(n_sta,node2) + ( phi - cb2s*turb2 )*ngradt(nn)
      end if

    end do edge_loop

  end subroutine sa_resid_diff_eto

!================================= SA_RESID_DIFF_AVGLSQ ======================80
!
! Diffusion residual for edge-based formulation.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_resid_diff_avglsq( n_sta, eqn_set, nnodes0,                    &
                               nedgeloc, eptr, turb, qnode, res,               &
                               gradx, grady, gradz, xn, yn, zn, ra, x, y, z,   &
                               nedgeloc_2d, n_turb, n_grd )

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re
    use turb_sa_const,   only : sig, cb2
    use fluid,           only : gamma, sutherland_constant
    use debug_defs,      only : gradient_construction_rhs

    integer, intent(in) :: n_sta, eqn_set
    integer, intent(in) :: n_turb, n_grd
    integer, intent(in) :: nedgeloc, nedgeloc_2d, nnodes0

    integer,  dimension(2,nedgeloc), intent(in)    :: eptr
    real(dp), dimension(:,:),        intent(in)    :: gradx, grady, gradz
    real(dp), dimension(:),          intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),   intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),   intent(in)    :: ra
    real(dp), dimension(:,:),        intent(in)    :: turb
    real(dp), dimension(:,:),        intent(in)    :: qnode
    real(dp), dimension(:,:),        intent(inout) :: res

    integer :: n, nedge_flux_eval, node1, node2, nn

    real(dp) :: xmre, xmre_s
    real(dp) :: aturb1, aturb2
    real(dp) :: cb20, cb2s, turb1, turb2
    real(dp) :: cstar
    real(dp) :: phi, rnu1, rnu2, rho1, rho2
    real(dp) :: my_xmach
    real(dp) :: txavg, tyavg, tzavg
    real(dp) :: ex, ey, ez, disi
    real(dp) :: tx, ty, tz, egradt, gradt_xi

    real(dp), dimension(3)      :: augment_weight
    real(dp), dimension(n_turb) :: ngradt

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0._dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:sa_resid_diff_avglsq')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb2s = t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre / sig

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

!   Check method for computing gradx,... (diffusion + source terms)

    if ( skeleton > 0 ) then
      write(*,"(1x,3(a))") ' Edge-based (avg_lsq) turbulent diffusion', &
                           ' ...gradients via ',trim(grad_x_y_z_contents)
    endif

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      nn = n_turb

      ! rnu = laminar viscosity / density

      rho1   = 1._dp
      rho2   = 1._dp
      rnu1   = 1._dp
      rnu2   = 1._dp
      if ( eqn_set == compressible ) then
        rho1 = qnode(1,node1)
        rho2 = qnode(1,node2)
        rnu1 = viscosity_law( cstar, gamma*qnode(5,node1)/rho1 ) / rho1
        rnu2 = viscosity_law( cstar, gamma*qnode(5,node2)/rho2 ) / rho2
      end if

      if ( constant_rnu_diffusion ) rnu1 = 1._dp
      if ( constant_rnu_diffusion ) rnu2 = 1._dp

      turb1 = turb(nn,node1)
      turb2 = turb(nn,node2)

      aturb1 = sa0_turb_abs( turb1, rnu1 )
      aturb2 = sa0_turb_abs( turb2, rnu2 )

      phi = t_diff1*0.5_dp*(  rnu1 +   rnu2) &
          + t_diff2*0.5_dp*(aturb1 + aturb2) &
          +    cb20*0.5_dp*( turb1 +  turb2)

      ! average node-based gradients : gradx, grady, gradz

      txavg = 0.5_dp*( gradx(n_grd-n_turb+nn,node1) +                     &
                        gradx(n_grd-n_turb+nn,node2) )
      tyavg = 0.5_dp*( grady(n_grd-n_turb+nn,node1) +                     &
                        grady(n_grd-n_turb+nn,node2) )
      tzavg = 0.5_dp*( gradz(n_grd-n_turb+nn,node1) +                     &
                        gradz(n_grd-n_turb+nn,node2) )

      ! ex, ey, ez is unit vector along edge direction

      ex   = x(node2) - x(node1)
      ey   = y(node2) - y(node1)
      ez   = z(node2) - z(node1)
      disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex   = ex*disi
      ey   = ey*disi
      ez   = ez*disi

      !  average Green-Gauss gradient in edge direction

      gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

      ! directional gradients along edge

      egradt = ( turb(1,node2) - turb(1,node1) )*disi

      augment_weight = edge_augment_weight( gradient_construction_rhs,      &
                                            ex, ey, ez, xn(n), yn(n), zn(n) )

      tx = txavg + ( egradt - gradt_xi )*augment_weight(1)
      ty = tyavg + ( egradt - gradt_xi )*augment_weight(2)
      tz = tzavg + ( egradt - gradt_xi )*augment_weight(3)

      ! turbulent diffusion contribution at dual face [ two terms ]

      ! [area]*[nondimensionalization factor : Mach / Re / sigma ]*
      ! [normal gradient] at dual face

      ngradt(nn) = xmre_s*ra(n)*( tx*xn(n) + ty*yn(n) + tz*zn(n) )

      if ( node1 <= nnodes0 ) then
        res(n_sta,node1) = res(n_sta,node1) - ( phi - cb2s*turb1 )*ngradt(nn)
      end if

      if ( node2 <= nnodes0 ) then
        res(n_sta,node2) = res(n_sta,node2) + ( phi - cb2s*turb2 )*ngradt(nn)
      end if

    end do edge_loop

  end subroutine sa_resid_diff_avglsq


!================================= SA_JACOB_CNLD =============================80
!
! Calculates jacobians of the Spalart model
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_jacob_cnld( viscous_method, fl, n_sta, a_diag, a_off, dtau,    &
                     eqn_set, dof0, nnodes0, nnodes01,                         &
                     nedgeloc, eptr,                                           &
                     turb, qnode, slen, gradx, grady, gradz, vol,              &
                     xn, yn, zn, ra, fhelp, facespeed,                         &
                     n_turb, n_tot,                                            &
                     g2m, nedgeloc_2d, nelem, elem, x, y, z, dxdt, dydt, dzdt, &
                     ia, ja, nzg2m, nbound, bc, symmetry, iflagslen, des_slen, &
                     node_pairs_2d, nnodes0_2d, n_grd, amut )

    use element_types,   only : elem_type
    use bc_names,        only : bc_ignore_2d, bc_is_periodic
    use turb_diffusion,  only : turb_jacob_diff_element                        &
                              , get_diffusion_coefficients
    use turb_diffusion5, only : turb_jacob_diff_element5
    use turb_diffusion6, only : turb_jacob_diff_element6
    use debug_defs,      only : diff_edge_avg_t
    use turbulence_info, only : turbulence_model_is_conservative

    integer, intent(in)  :: viscous_method, fl, n_sta
    integer, intent(in)  :: eqn_set, dof0, nnodes0, nnodes01, n_tot
    integer, intent(in)  :: nedgeloc, n_turb
    integer, intent(in)  :: nelem
    integer, intent(in)  :: nedgeloc_2d, nbound
    integer, intent(in)  :: nnodes0_2d, n_grd

    integer, dimension(2,nedgeloc), intent(in) :: eptr
    integer, dimension(2,nedgeloc), intent(in) :: fhelp
    integer, dimension(:),          intent(in) :: g2m, ia, ja, nzg2m
    integer, dimension(2,nnodes0_2d), intent(in)  :: node_pairs_2d

    integer,   dimension(:),        intent(in)    :: symmetry
    integer,   dimension(:),        intent(in)    :: iflagslen
    real(dp),  dimension(:,:),      intent(in)    :: turb
    real(dp),  dimension(:,:),      intent(in)    :: qnode
    real(dp),  dimension(:),        intent(in)    :: x, y, z, vol, dtau
    real(dp),  dimension(:),        intent(in)    :: slen, des_slen
    real(dp),  dimension(:),        intent(in)    :: dxdt, dydt, dzdt
    real(dp),  dimension(:,:),      intent(in)    :: gradx, grady, gradz
    real(dp),  dimension(nedgeloc), intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc), intent(in)    :: facespeed
    real(dp),  dimension(:,:,:),    intent(inout) :: a_diag
    real(odp), dimension(:,:,:),    intent(inout) :: a_off

    type(bcgrid_type), dimension(nbound), intent(in)  :: bc

    type(elem_type), dimension(nelem), intent(in) :: elem
    real(dp),  dimension(:),        intent(in)    :: amut

    integer :: ielem, ierr, ib, ibc, warnings

    real(dp),  dimension(n_turb)   :: diff_const
    real(dp),  dimension(n_turb)   :: diff_const2

  continue

    absoft_dummy = turbulence_model_is_conservative ( turbulence_model_int )
    if  (absoft_dummy ) &
    call lmpi_conditional_stop(1,'not_implemented:sa_jacob_cnld')

    select case ( eqn_set )
    case ( compressible )
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,'q_type not primitive:sa_jacob_cnld')
    case ( incompressible )
    case default
      call lmpi_conditional_stop(1,'eqn_set:sa_jacob_cnld')
    end select

      a_diag(n_sta,n_sta,:) = 0._dp
      a_off( n_sta,n_sta,:) = 0._odp

      if ( lmpi_master .and. view_diag_turb_at_levels > 0 ) then
        write(7000+lmpi_id,"(3(a,i10),2(a,L1))")              &
        ' Jacobian at ntt=',ntt,' fl=',fl,' dof0=',dof0,      &
        ' tightly_couple=',tightly_couple
      endif

      ! Source and d(source)d(vt) evaluated via node-based loop.

        if ( jacobian_piece ) call times('Turb-Jac-BP-source',fl)
        call source_jacob( n_sta, eqn_set, dof0,                      &
                         turb, qnode, slen, gradx, grady, gradz, vol, &
                         a_diag, g2m, s_terms(fl)%dsdvt,              &
                         s_terms(fl)%s_diag, iflagslen, des_slen,     &
                         nnodes0, nnodes01, nedgeloc, eptr, x, y, z,  &
                         xn, yn, zn, ra, nedgeloc_2d, node_pairs_2d,  &
                         nnodes0_2d, nelem, elem, n_tot, n_grd, dxdt, &
                         dydt, dzdt, nbound, bc )
        if ( jacobian_piece ) call times('Turb-Jac-BP-source',fl)

      if ( .not.tightly_couple ) then

        ! Convection terms.

        if ( jacobian_piece ) call times('Turb-Jac-BP-conv',fl)
        call turb_jacob_conv( nnodes0, nedgeloc, eptr, qnode,                &
                              xn, yn, zn, ra, a_diag,                        &
                              a_off, fhelp, facespeed, n_turb,               &
                              g2m, nedgeloc_2d )
        if ( jacobian_piece ) call times('Turb-Jac-BP-conv',fl)

      else

        ! Edge-assembled Jacobians:
        ! (1) Convection terms
        ! (2) d(source)/d(GGD:velocity) - Green-Gauss-Dual - interior.

        if ( jacobian_piece ) call times('Turb-Jac-BP-conv_dsdv',fl)
        call turb_jac_conv_dsdv( eqn_set, n_sta, nnodes0,                    &
                                 nedgeloc, eptr, turb, qnode,                &
                                 xn, yn, zn, ra, a_diag,                     &
                                 a_off, fhelp, facespeed, g2m, nedgeloc_2d,  &
                                 symmetry, s_terms(fl)%dsdvt,                &
                                 slen, vol, gradx, grady, gradz )

        ! d(source)/d(GGD:velocity)  - Green-Gauss-Dual - bc - element-based.

        do ib = 1,nbound

          ibc = bc(ib)%ibc

          if ( twod .and. bc_ignore_2d(ibc) ) cycle
          if ( bc_is_periodic(ibc) ) cycle

          call dsdv_bc_eb( ib, n_sta, eqn_set, dof0, a_diag, a_off,      &
                           qnode, slen, gradx, grady, gradz, vol,        &
                           g2m, x, y, z, dxdt, dydt, dzdt,               &
                           ia, ja, nzg2m, bc, elem,                      &
                           symmetry, s_terms(fl)%dsdvt )

        enddo
        if ( jacobian_piece ) call times('Turb-Jac-BP-conv_dsdv',fl)

      endif

      ! Diffusion terms via element types

      if ( .not.sa_diffusion_eto .and. viscous_method == 0 ) then

        call get_diffusion_coefficients( turbulence_model_int, n_turb, &
                                         diff_const, diff_const2 )

        ! Diffusion terms via element-based integration:

        ! There are 3 diffusion paths:
        ! 1 for diff_edge_avg_t <= 1 (uses general turbulence routines)
        ! 2 for diff_edge_avg_t == 2 (one 2d and one 3d - both 1-eqn)
        ! called in a non-tet loop and a tet-only loop for timing purposes.

        if ( diff_edge_avg_t <= 1 ) then !generalized turbulence routines

          if ( jacobian_piece ) call times('Turb-Jac-BP-diffusion-tet',fl)
          do ielem = 1, nelem

            if ( elem(ielem)%type_cell /= 'tet') cycle

            call turb_jacob_diff_element (n_sta, eqn_set, nnodes0,             &
                                 turb, qnode, a_diag, a_off,                   &
                                 elem(ielem)%ncell,                            &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell, elem(ielem)%local_f2n, &
                                 elem(ielem)%local_e2n, elem(ielem)%local_f2e, &
                                 elem(ielem)%e2n_2d, elem(ielem)%face_per_cell,&
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 elem(ielem)%face_2d, elem(ielem)%chk_norm,    &
                                 nzg2m, g2m, turbulence_model_int, amut,       &
                                 diff_const, diff_const2 )
          enddo
          if ( jacobian_piece ) call times('Turb-Jac-BP-diffusion-tet',fl)

          if ( jacobian_piece ) call times('Turb-Jac-BP-diffusion-nontet',fl)
          do ielem = 1, nelem

            if ( elem(ielem)%type_cell == 'tet') cycle

            call turb_jacob_diff_element (n_sta, eqn_set, nnodes0,             &
                                 turb, qnode, a_diag, a_off,                   &
                                 elem(ielem)%ncell,                            &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell, elem(ielem)%local_f2n, &
                                 elem(ielem)%local_e2n, elem(ielem)%local_f2e, &
                                 elem(ielem)%e2n_2d, elem(ielem)%face_per_cell,&
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 elem(ielem)%face_2d, elem(ielem)%chk_norm,    &
                                 nzg2m, g2m, turbulence_model_int, amut,       &
                                 diff_const, diff_const2 )
          enddo
          if ( jacobian_piece ) call times('Turb-Jac-BP-diffusion-nontet',fl)

        else !edge-averaged viscosity
             !n_turb=1 routines

          if ( jacobian_piece ) then
            if ( twod ) then
              call times('Turb-Jac-BP-diffusion5-tet',fl)
            else
              call times('Turb-Jac-BP-diffusion6-tet',fl)
            endif
          endif
          do ielem = 1, nelem

            if ( elem(ielem)%type_cell /= 'tet') cycle

            if ( twod ) then
            call turb_jacob_diff_element5 (n_sta, eqn_set, nnodes0,            &
                                 turb, qnode, a_diag, a_off,                   &
                                 elem(ielem)%ncell,                            &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell, elem(ielem)%local_f2n, &
                                 elem(ielem)%local_e2n, elem(ielem)%local_f2e, &
                                 elem(ielem)%e2n_2d, elem(ielem)%face_per_cell,&
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 elem(ielem)%face_2d, elem(ielem)%big_angle,   &
                                 nzg2m, g2m,                                   &
                                 diff_const, diff_const2 )
            else
            call turb_jacob_diff_element6 (n_sta, eqn_set, nnodes0,            &
                                 turb, qnode, a_diag, a_off,                   &
                                 elem(ielem)%ncell,                            &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell,                        &
                                 elem(ielem)%local_f2n, elem(ielem)%local_e2n, &
                                 elem(ielem)%face_per_cell,                    &
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 elem(ielem)%big_angle,                        &
                                 nzg2m, g2m,                                   &
                                 diff_const, diff_const2 )
            endif
          enddo
          if ( jacobian_piece ) then
            if ( twod ) then
              call times('Turb-Jac-BP-diffusion5-tet',fl)
            else
              call times('Turb-Jac-BP-diffusion6-tet',fl)
            endif
          endif


          if ( jacobian_piece ) then
            if ( twod ) then
              call times('Turb-Jac-BP-diffusion5-nontet',fl)
            else
              call times('Turb-Jac-BP-diffusion6-nontet',fl)
            endif
          endif
          do ielem = 1, nelem

            if ( elem(ielem)%type_cell == 'tet') cycle

            if ( twod ) then
            call turb_jacob_diff_element5 (n_sta, eqn_set, nnodes0,            &
                                 turb, qnode, a_diag, a_off,                   &
                                 elem(ielem)%ncell,                            &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell, elem(ielem)%local_f2n, &
                                 elem(ielem)%local_e2n, elem(ielem)%local_f2e, &
                                 elem(ielem)%e2n_2d, elem(ielem)%face_per_cell,&
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 elem(ielem)%face_2d, elem(ielem)%big_angle,   &
                                 nzg2m, g2m,                                   &
                                 diff_const, diff_const2 )
            else
            call turb_jacob_diff_element6 (n_sta, eqn_set, nnodes0,            &
                                 turb, qnode, a_diag, a_off,                   &
                                 elem(ielem)%ncell,                            &
                                 elem(ielem)%c2n, x, y, z,                     &
                                 elem(ielem)%type_cell,                        &
                                 elem(ielem)%local_f2n, elem(ielem)%local_e2n, &
                                 elem(ielem)%face_per_cell,                    &
                                 elem(ielem)%node_per_cell,                    &
                                 elem(ielem)%edge_per_cell, ia, ja, n_turb,    &
                                 elem(ielem)%big_angle,                        &
                                 nzg2m, g2m,                                   &
                                 diff_const, diff_const2 )
            endif
          enddo
          if ( jacobian_piece ) then
            if ( twod ) then
              call times('Turb-Jac-BP-diffusion5-nontet',fl)
            else
              call times('Turb-Jac-BP-diffusion6-nontet',fl)
            endif
          endif

        endif

      ! Diffusion terms via edge-based terms.

      elseif ( sa_diffusion_eto .or. viscous_method == 1      &
                                .or. viscous_method == 2 ) then

        call sa_jacob_diff_eto( n_sta, eqn_set, nnodes0,                       &
                                nedgeloc, eptr, turb, qnode,                   &
                                xn, yn, zn, ra, x, y, z,                       &
                                a_diag, a_off, fhelp, nedgeloc_2d,             &
                                n_turb, g2m)

      else

        call lmpi_conditional_stop(1,'Invalid:sa_jacob_cnld')

      end if

      !Just track this diagnostic.  Letting time step adjustment cover
      !the problem.

      call check_diagonal_sign( fl, n_sta, dof0, a_diag,               &
                                g2m, x, y, z, slen, dtau, turb,        &
                                warnings, 'CNLD:J:Pass=1' )

      call lmpi_reduce( warnings, cnld_positive_diagonal_warnings(fl) )
      call lmpi_bcast(            cnld_positive_diagonal_warnings(fl) )

  end subroutine sa_jacob_cnld

!=============================== TURB_JAC_CONV_DSDV ==========================80
!
! Edge-assembled jacobians of convection terms
! plus jacobians of source terms wrt velocity (Green-Gauss-Dual gradients).
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_jac_conv_dsdv( eqn_set, n_sta, nnodes0,                      &
                                 nedgeloc, eptr, turb, q_dof,                  &
                                 xn, yn, zn, ra, a_diag,                       &
                                 a_off, fhelp, facespeed, g2m, nedgeloc_2d,    &
                                 symmetry, dsdvt,                              &
                                 slen, vol, gradx, grady, gradz )

    use grid_motion_helpers, only : need_grid_velocity
    use turb_parameters,     only : t_conv, ubar_eps

    integer, intent(in) :: eqn_set, n_sta, nnodes0, nedgeloc, nedgeloc_2d

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nedgeloc),   intent(in) :: fhelp
    integer, dimension(:),            intent(in) :: g2m

    integer,   dimension(:),        intent(in)    :: symmetry
    real(dp),  dimension(:,:),      intent(in)    :: turb
    real(dp),  dimension(:,:),      intent(in)    :: q_dof
    real(dp),  dimension(:,:,:),    intent(inout) :: a_diag
    real(odp), dimension(:,:,:),    intent(inout) :: a_off
    real(dp),  dimension(nedgeloc), intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc), intent(in)    :: facespeed
    real(dp),  dimension(:),        intent(in)    :: dsdvt, slen, vol
    real(dp),  dimension(:,:),      intent(in)    :: gradx, grady, gradz

    integer :: n, node1, node2, ioff, row, nedge_jac_eval
    integer :: j, vt_sta, vt_end, ierr

    real(dp) :: area, tarea, diag1, diag2, off1, off2
    real(dp) :: ubar, uminus, uplus, u, v, w
    real(dp) :: xnorm, ynorm, znorm, face_speed
    real(dp) :: term1, dgradx, dgrady, dgradz, duplus, duminus

    real(dp), dimension(4) :: ds_dq1, ds_dq2 !d(source)/dq
    real(dp), dimension(4) :: dfluxdq        !d(convection)/dq

  continue

    select case ( eqn_set )
    case ( compressible )
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,&
      'q_type not primitive:turb_jac_conv_dsdv')
    case ( incompressible )
    case default
      call lmpi_conditional_stop(1,'turb_jac_conv_dsdv: in/cmprss pg')
    end select

    vt_sta = 2
    vt_end = 4
    if ( eqn_set == compressible ) vt_sta = 1

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

    do n = 1, nedge_jac_eval
      node1 = eptr(1,n)
      node2 = eptr(2,n)

      ! Unit normal to dual face and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)
      tarea = t_conv*area

      ! Dual face speed

      face_speed = 0._dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

      u = q_dof(2,node1)
      v = q_dof(3,node1)
      w = q_dof(4,node1)
      ubar   = xnorm*u + ynorm*v + znorm*w - face_speed

      uplus  = 0.5_dp*( ubar + aharten(ubar,ubar_eps) )
      uminus = 0.5_dp*( ubar - aharten(ubar,ubar_eps) )

      diag1 = 0._dp
      off1  = 0._dp

      diag1 = diag1 + uplus  * tarea
      off1  = off1  + uminus * tarea

      if (node1 <= nnodes0) then

        row = g2m(node1)
        a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) + diag1
        ioff        = fhelp(1,n)
        a_off(n_sta,n_sta,ioff) = a_off(n_sta,n_sta,ioff) + real(off1,odp)

        if ( tightly_couple ) then

          duplus  = 0.5_dp*( 1._dp + daharten(ubar,ubar_eps) )
          duminus = 0.5_dp*( 1._dp - daharten(ubar,ubar_eps) )

          dfluxdq(1:4) = dfdubar( eqn_set, duplus, duminus, node1, &
                                  xnorm, ynorm, znorm, q_dof,      &
                                  turb(1,node1), turb(1,node2) )

          do j=1,4
            a_diag(n_sta,j,row) = a_diag(n_sta,j,row) + tarea*dfluxdq(j)
          enddo

        endif

        if ( tightly_couple .and. slen(node1) > epsilon(1._dp) ) then

          term1  = 0.5_dp*area/vol(node1)

          dgradx = + xnorm*term1
          dgrady = + ynorm*term1
          dgradz = + znorm*term1

          ds_dq1(:) = dsdq( eqn_set, node1, node1,             &
                            dsdvt, q_dof, gradx, grady, gradz, &
                            symmetry, dgradx, dgrady, dgradz )

          ds_dq2(:) = dsdq( eqn_set, node1, node2,             &
                            dsdvt, q_dof, gradx, grady, gradz, &
                            symmetry, dgradx, dgrady, dgradz )

          do j=vt_sta,vt_end
            a_diag(n_sta,j, row) = a_diag(n_sta,j, row) + ds_dq1(j)
            a_off( n_sta,j,ioff) = a_off( n_sta,j,ioff) + ds_dq2(j)
          enddo

        endif

      end if

      u = q_dof(2,node2)
      v = q_dof(3,node2)
      w = q_dof(4,node2)

      xnorm = -xnorm
      ynorm = -ynorm
      znorm = -znorm

      ubar   = xnorm*u + ynorm*v + znorm*w - face_speed

      uplus  = 0.5_dp*( ubar + aharten(ubar,ubar_eps) )
      uminus = 0.5_dp*( ubar - aharten(ubar,ubar_eps) )

      diag2 = 0._dp
      off2  = 0._dp

      diag2 = diag2 + uplus  * tarea
      off2  = off2  + uminus * tarea

      if (node2 <= nnodes0) then

        row = g2m(node2)
        a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) + diag2
        ioff        = fhelp(2,n)
        a_off(n_sta,n_sta,ioff) = a_off(n_sta,n_sta,ioff) + real(off2,odp)

        if ( tightly_couple ) then

          duplus  = 0.5_dp*( 1._dp + daharten(ubar,ubar_eps) )
          duminus = 0.5_dp*( 1._dp - daharten(ubar,ubar_eps) )

          dfluxdq(1:4) = dfdubar( eqn_set, duplus, duminus, node2, &
                                  xnorm, ynorm, znorm, q_dof,      &
                                  turb(1,node2), turb(1,node1) )

          do j=1,4
            a_diag(n_sta,j,row) = a_diag(n_sta,j,row) + tarea*dfluxdq(j)
          enddo

        endif

        if ( tightly_couple  .and. slen(node2) > epsilon(1._dp) ) then

          term1  = 0.5_dp*area/vol(node2)

          dgradx = xnorm*term1
          dgrady = ynorm*term1
          dgradz = znorm*term1

          ds_dq1(:) = dsdq( eqn_set, node2, node2,             &
                            dsdvt, q_dof, gradx, grady, gradz, &
                            symmetry, dgradx, dgrady, dgradz )

          ds_dq2(:) = dsdq( eqn_set, node2, node1,             &
                            dsdvt, q_dof, gradx, grady, gradz, &
                            symmetry, dgradx, dgrady, dgradz )
          do j=vt_sta,vt_end
            a_diag(n_sta,j, row) = a_diag(n_sta,j, row) + ds_dq1(j)
            a_off( n_sta,j,ioff) = a_off( n_sta,j,ioff) + ds_dq2(j)
          enddo

        endif

      end if

    end do

  end subroutine turb_jac_conv_dsdv

!=============================== DSDV_BC_EB ==================================80
!
! Close off d(source) d(velocity) Jacobians.
!
!=============================================================================80

  subroutine dsdv_bc_eb( ib, n_sta, eqn_set, dof0, a_diag, a_off,              &
                         q_dof, slen, gradx, grady, gradz, vol,                &
                         g2m, x, y, z, dxdt, dydt, dzdt,                       &
                         ia, ja, nzg2m, bc, elem, symmetry, dsdvt )

    use element_based_bc_util, only : element_based_metrics

    integer, intent(in) :: ib, n_sta, eqn_set, dof0

    integer,   dimension(:),      intent(in)    :: g2m, ia, ja, nzg2m, symmetry
    real(dp),  dimension(:,:),    intent(in)    :: q_dof
    real(dp),  dimension(:),      intent(in)    :: x, y, z, vol, slen, dsdvt
    real(dp),  dimension(:),      intent(in)    :: dxdt, dydt, dzdt
    real(dp),  dimension(:,:),    intent(in)    :: gradx, grady, gradz
    real(dp),  dimension(:,:,:),  intent(inout) :: a_diag
    real(odp), dimension(:,:,:),  intent(inout) :: a_off

    type(elem_type),   dimension(:), intent(in) :: elem
    type(bcgrid_type), dimension(:), intent(in) :: bc

    integer                :: triangle_index
    integer                :: triangle_corner
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer                :: quad_index
    integer                :: quad_corner
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight

    integer  :: node1, j, vt_sta, vt_end, tn1, tnc, qn1, qnc
    integer  :: row, n, off_diag, kk, nodes_per_face

    real(dp) :: xnorm, ynorm, znorm, area, face_speed
    real(dp) :: dgradx, dgrady, dgradz, term0, term1

    real(dp), dimension(4) :: ds_dq

  continue

    vt_sta = 2
    vt_end = 4
    if ( eqn_set == compressible ) vt_sta = 1

    !----------Triangular Faces----------

    loop_tris : do triangle_index = 1,bc(ib)%nbfacet

      nodes_per_face = 3

      corner_tris_loop : do triangle_corner = 1,nodes_per_face

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, triangle_index, triangle_corner                               &
           , 3, triangle_node, triangle_weight                                 &
           , xnorm, ynorm, znorm, area, face_speed )

        tn1 = triangle_node(1)

        if (      tn1  > dof0 ) cycle
        if ( slen(tn1) < epsilon(1._dp) ) cycle !skip source on wall

        row = g2m(tn1)

        term0 = area/vol(tn1)

        term1  = triangle_weight(1)*term0

        dgradx = + xnorm*term1
        dgrady = + ynorm*term1
        dgradz = + znorm*term1

        ds_dq(:) = dsdq( eqn_set, tn1, tn1,                 &
                         dsdvt, q_dof, gradx, grady, gradz, &
                         symmetry, dgradx, dgrady, dgradz )

        do j=vt_sta,vt_end
          a_diag(n_sta,j,row) =        &
          a_diag(n_sta,j,row) + ds_dq(j)
        enddo

        tri_off_diag : do off_diag = 2, nodes_per_face
          n   = 0
          tnc = triangle_node(off_diag)
          do kk = ia(tn1), ia(tn1+1) - 1
            if ( ja(kk) == tnc ) then
              n = nzg2m(kk)
            endif
          end do

          term1  = triangle_weight(off_diag)*term0

          dgradx = + xnorm*term1
          dgrady = + ynorm*term1
          dgradz = + znorm*term1

          ds_dq(:) = dsdq( eqn_set, tn1, tnc,                 &
                           dsdvt, q_dof, gradx, grady, gradz, &
                           symmetry, dgradx, dgrady, dgradz )

          do j=vt_sta,vt_end
            a_off(n_sta,j,n) =        &
            a_off(n_sta,j,n) + ds_dq(j)
          enddo

        end do tri_off_diag

      enddo corner_tris_loop

    enddo loop_tris

    !----------Quadrilateral Faces----------

    loop_quads : do quad_index = 1,bc(ib)%nbfaceq

      nodes_per_face = 4

      corner_quad_loop : do quad_corner = 1,nodes_per_face

        skip_other_twod_plane : if (twod) then
          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(quad_index,quad_corner))
          if( abs(y(node1)-yplane_2d) >= y_coplanar_tol ) &
            cycle corner_quad_loop
        end if skip_other_twod_plane

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, quad_index, quad_corner                                       &
           , 4, quad_node, quad_weight                                         &
           , xnorm, ynorm, znorm, area, face_speed )

        qn1 = quad_node(1)
        if (      qn1  > dof0 ) cycle
        if ( slen(qn1) < epsilon(1._dp) ) cycle !skip source on wall

        row = g2m(qn1)

        term0 = area/vol(qn1)

        term1  = quad_weight(1)*term0

        dgradx = + xnorm*term1
        dgrady = + ynorm*term1
        dgradz = + znorm*term1

        ds_dq(:) = dsdq( eqn_set, qn1, qn1,                 &
                         dsdvt, q_dof, gradx, grady, gradz, &
                         symmetry, dgradx, dgrady, dgradz )

        do j=vt_sta,vt_end
          a_diag(n_sta,j,row) =        &
          a_diag(n_sta,j,row) + ds_dq(j)
        enddo

        quad_off_diag : do off_diag = 2, nodes_per_face

          n = 0
          qnc = quad_node(off_diag)
          do kk = ia(qn1), ia(qn1+1) - 1
            if ( ja(kk) == qnc ) then
              n = nzg2m(kk)
            endif
          end do

          if ( 0 == n ) cycle !skip twod nodes

          term1  = quad_weight(off_diag)*term0

          dgradx = + xnorm*term1
          dgrady = + ynorm*term1
          dgradz = + znorm*term1

          ds_dq(:) = dsdq( eqn_set, qn1, qnc,                 &
                           dsdvt, q_dof, gradx, grady, gradz, &
                           symmetry, dgradx, dgrady, dgradz )

          do j=vt_sta,vt_end
            a_off(n_sta,j,n) =        &
            a_off(n_sta,j,n) + ds_dq(j)
          enddo

        end do quad_off_diag

      enddo corner_quad_loop

    enddo loop_quads

  end subroutine dsdv_bc_eb

!============================= SA_JACOB_DIFF_ETO =============================80
!
! Calculates the diffusion jacobians for Spalart's model (loosely coupled)
! via edge-based integration (edge-terms-only)
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine sa_jacob_diff_eto( n_sta, eqn_set, nnodes0,                       &
                                nedgeloc, eptr, turb, qnode,                   &
                                xn, yn, zn, ra, x, y, z,                       &
                                a_diag, a_off, fhelp, nedgeloc_2d,             &
                                n_turb, g2m)

    use kinddefs,        only : dp, odp
    use info_depr,       only : tref, xmach, re
    use turb_sa_const,   only : sig, cb2
    use fluid,           only : gamma, sutherland_constant
    use debug_defs,      only : gradient_construction_lhs

    integer, intent(in) :: n_sta, eqn_set, n_turb
    integer, intent(in) :: nedgeloc, nedgeloc_2d, nnodes0

    integer, dimension(2,nedgeloc),          intent(in) :: eptr
    integer, dimension(2,nedgeloc),          intent(in) :: fhelp
    integer, dimension(:),                   intent(in) :: g2m

    real(dp),  dimension(:,:),      intent(in)    :: turb
    real(dp),  dimension(:,:),      intent(in)    :: qnode
    real(dp),  dimension(:),        intent(in)    :: x, y, z
    real(dp),  dimension(nedgeloc), intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(:,:,:),    intent(inout) :: a_diag
    real(odp), dimension(:,:,:),    intent(inout) :: a_off

    integer :: n, nedge_jac_eval, row
    integer :: ioff, node1, node2, nn

    real(dp) :: xmre, xmre_s, xmr, my_xmach
    real(dp) :: cb20, cb2s
    real(dp) :: cstar
    real(dp) :: ex, ey, ez, disi, face_term
    real(dp) :: rnu1, rnu2, rho1, rho2

    real(dp) :: phi, terma, termb, termc, dngradt, ngradt
    real(dp) :: turb1, turb2, aturb1, aturb2, daturb1, daturb2

    real(dp), dimension(3) :: augment_weight

  continue

    cstar = sutherland_constant / tref
    my_xmach = 0._dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:sa_jacob_diff_eto')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*cb2
    cb2s = t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre / sig
    xmr    = xmre_s

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    nedge_jac_eval = nedgeloc
    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    endif

    if ( skeleton > 0 ) then
      write(*,*) ' Compute Jacobians of turbulent diffusion terms.'
      write(*,*) ' Using edge-terms only.'
    endif

    edge_loop: do n = 1, nedge_jac_eval

!     Nearest neighbor nodes

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      nn = n_turb

      ! rnu = laminar viscosity / density

      rho1   = 1._dp
      rho2   = 1._dp
      rnu1   = 1._dp
      rnu2   = 1._dp
      if ( eqn_set == compressible ) then
        rho1 = qnode(1,node1)
        rho2 = qnode(1,node2)
        rnu1 = viscosity_law( cstar, gamma*qnode(5,node1)/rho1 ) / rho1
        rnu2 = viscosity_law( cstar, gamma*qnode(5,node2)/rho2 ) / rho2
      end if

      turb1 = turb(nn,node1)
      turb2 = turb(nn,node2)

      aturb1  =  sa0_turb_abs( turb1, rnu1 )
      aturb2  =  sa0_turb_abs( turb2, rnu2 )

      daturb1 = dsa1_turb_abs( turb1, rnu1 )
      daturb2 = dsa1_turb_abs( turb2, rnu2 )

      phi = t_diff1*0.5_dp*(  rnu1 +   rnu2) &
          + t_diff2*0.5_dp*(aturb1 + aturb2) &
          +    cb20*0.5_dp*( turb1 +  turb2)

      ! turbulent diffusion contribution at dual face [ two terms ]
      ! [area]*[nondimensionalization factor: Mach / Re / sigma] at dual face

      face_term = xmr*ra(n)

      ! ex, ey, ez is unit vector along edge direction

      ex   = x(node2) - x(node1)
      ey   = y(node2) - y(node1)
      ez   = z(node2) - z(node1)
      disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

      ex   = ex*disi
      ey   = ey*disi
      ez   = ez*disi

      !  average Green-Gauss gradient in edge direction

      !gradt_xi = trbrexavg(1)*ex + trbreyavg(1)*ey + trbrezavg(1)*ez

      ! directional gradients along edge

      ! egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

      augment_weight = edge_augment_weight( gradient_construction_lhs,      &
                                            ex, ey, ez, xn(n), yn(n), zn(n) )

      !tx = ( egradt  )*augment_weight(1)
      !ty = ( egradt  )*augment_weight(2)
      !tz = ( egradt  )*augment_weight(3)

      ![area]*[nondimensionalization factor : Mach / Re / sigma ]*
      ![edge:face vector dot product]/[edge distance] at dual face

      dngradt = face_term*( augment_weight(1)*xn(n) &
                          + augment_weight(2)*yn(n) &
                          + augment_weight(3)*zn(n) )*disi

      termb = 0._dp
      termc = 0._dp

      if ( node1 <= nnodes0 ) then

        terma         = ( phi - cb2s*turb1 )*dngradt

        ngradt = dngradt*( turb1 - turb2 )

        termb  = ( ( t_diff2*0.5_dp        )*daturb1 &
                 + (    cb20*0.5_dp - cb2s )         )*ngradt
        termc  = ( ( t_diff2*0.5_dp        )*daturb2 &
                 + (    cb20*0.5_dp        )         )*ngradt

        ioff            = fhelp(1,n)
        row             = g2m(node1)
        a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row)  + terma + termb
        a_off(n_sta,n_sta,ioff) =  a_off(n_sta,n_sta,ioff) - terma + termc

      end if

      if ( node2 <= nnodes0 ) then

        terma         = ( phi - cb2s*turb2 )*dngradt

        ngradt  = dngradt*( turb2 - turb1 )

        termb  = ( ( t_diff2*0.5_dp        )*daturb2 &
                 + (    cb20*0.5_dp - cb2s )         )*ngradt
        termc  = ( ( t_diff2*0.5_dp        )*daturb1 &
                 + (    cb20*0.5_dp        )         )*ngradt

        ioff            = fhelp(2,n)
        row             = g2m(node2)
        a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row)  + terma + termb
        a_off(n_sta,n_sta,ioff) =  a_off(n_sta,n_sta,ioff) - terma + termc

      end if

    end do edge_loop

  end subroutine sa_jacob_diff_eto


!================================= SOURCE_JACOB ==============================80
!
! Calculates jacobians of the source terms and some derivatives of vt.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine source_jacob( n_sta, eqn_set, dof0,                               &
                           turb, qnode, slen, gradx, grady, gradz, vol,        &
                           a_diag, g2m, dsdvt, s_diag, iflagslen, des_slen,    &
                           nnodes0, nnodes01, nedgeloc, eptr, x, y, z,         &
                           xn, yn, zn, ra, nedgeloc_2d, node_pairs_2d,         &
                           nnodes0_2d, nelem, elem, n_tot, n_grd, dxdt, dydt,  &
                           dzdt, nbound, bc )

    use info_depr,       only : tref, xmach, re
    use nml_two_d_trans, only : turb_transition
    use fluid,           only : gamma, sutherland_constant
    use turb_sa_const,   only : sarc, sarc_cr3
    use turb_util,       only : strain_tensor_deriv

    integer, intent(in) :: n_sta, eqn_set, dof0
    integer, intent(in) :: nnodes0, nnodes01, nedgeloc, nedgeloc_2d
    integer, intent(in) :: nnodes0_2d, nelem, n_tot, n_grd, nbound

    integer, dimension(:),       intent(in)    :: g2m
    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr
    integer,      dimension(2,nnodes0_2d),   intent(in)    :: node_pairs_2d

    real(dp),  dimension(:),     intent(in)    :: x, y, z, xn, yn, zn, ra
    real(dp),  dimension(:),     intent(in)    :: dxdt, dydt, dzdt
    real(dp),  dimension(:),     intent(in)    :: vol, slen, des_slen
    real(dp),  dimension(:,:),   intent(in)    :: turb
    real(dp),  dimension(:,:),   intent(in)    :: qnode
    real(dp),  dimension(:,:),   intent(in)    :: gradx, grady, gradz

    real(dp),  dimension(:),     intent(inout) :: dsdvt
    real(dp),  dimension(:,:,:), intent(inout) :: a_diag
    real(dp),  dimension(:),     intent(inout) :: s_diag
    integer,   dimension(:),     intent(in)    :: iflagslen

    integer :: i, row, ierr, j, ierr_ddt(0:3)

    real(dp) :: xmre, cstar, vt
    real(dp) :: dsource1, dsource2, dsource3
    real(dp) :: p, rho, rnu, a_sq, rhoinv, my_xmach, diag_drnu
    real(dp) :: distance, rd, fd, velterm
    real(dp) :: xis, xisabs, rstar, rtilde, fr1
    real(dp) :: s11,s12,s13,s22,s23,s33,w12,w13,w23
    real(dp) :: added_dest_turbsq

    real(dp), dimension(5) :: dsa

    logical  :: zero_prod

    type(elem_type), dimension(nelem), intent(in) :: elem
    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc

    type(ddt3) :: source_ddt

    logical :: des_code, turb_compress_model_code

  continue

    des_code = .false.
    if ( ( turbulence_model_int == des )                                       &
    .or. ( turbulence_model_int == des_neg ) ) des_code = .true.

    turb_compress_model_code = .false.
    if ( turb_compress_model == 'ssz' ) turb_compress_model_code = .true.

    velterm = 0._dp

    ierr_ddt(:) = 0

    my_xmach = 0._dp
    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
      ierr = primitive_q_type - q_type
      call lmpi_conditional_stop(ierr,'q_type not primitive:source_jacob')
    case ( incompressible )
      my_xmach = 1._dp
    case default
      call lmpi_conditional_stop(1,'eqn_set:source_jacob')
    end select

    xmre  = my_xmach / re
    cstar = sutherland_constant / tref

!   For SARC only, need to get D(Sij)/Dt terms (ignoring time deriv for now)
!   = u_k*d(Sij)/dx_k (summing over k)

    if (sarc) then
      if ( .not. sderivs_allocated ) then
        allocate(s11deriv(nnodes01))
        allocate(s12deriv(nnodes01))
        allocate(s13deriv(nnodes01))
        allocate(s22deriv(nnodes01))
        allocate(s23deriv(nnodes01))
        allocate(s33deriv(nnodes01))
        sderivs_allocated = .true.
      endif
      call strain_tensor_deriv(eqn_set,                                        &
                   nnodes0, nnodes01, nedgeloc, eptr, qnode,                   &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d,                     &
                   nelem, elem, n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,    &
                   s11deriv, s12deriv, s13deriv, s22deriv, s23deriv, s33deriv)
    end if

    ierr = 0

    do i = 1, dof0

      if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

      rho    = 1._dp
      rhoinv = 1._dp
      a_sq   = 1._dp
      if ( eqn_set == compressible ) then
        rho  = qnode(1,i)
        rhoinv = 1._dp/rho
        p    = qnode(5,i)
        a_sq = gamma * p * rhoinv
        rnu  = viscosity_law( cstar, a_sq ) * rhoinv
      else
        rnu  = 1._dp
      end if

      if ( constant_rnu_source ) rnu = 1._dp

      if ( des_code .or. turb_compress_model_code ) then
        velterm = des_velterm( gradx(2,i), grady(2,i), gradz(2,i), &
                               gradx(3,i), grady(3,i), gradz(3,i), &
                               gradx(4,i), grady(4,i), gradz(4,i) )
      endif

      distance = slen(i)
      fd = 0._dp
      if ( des_code ) then
        if ( ddes ) then  ! Delayed DES (DDES) (TCFD 20:181-195 2006)
          rd = turb(1,i)*my_xmach/                                           &
               (sqrt(velterm)*vkar*vkar*slen(i)*slen(i)*re)
          fd = 1._dp - tanh((8.0_dp*rd)*(8.0_dp*rd)*(8.0_dp*rd))
          distance = slen(i) - fd*max(0._dp, slen(i)-cdes*des_slen(i))
        else  ! Standard DES
          distance = min(slen(i),cdes*des_slen(i))
        end if
      end if

      !...velocity term contribution to source.
      vt = sa_source_s( gradx(2,i), grady(2,i), gradz(2,i), &
                        gradx(3,i), grady(3,i), gradz(3,i), &
                        gradx(4,i), grady(4,i), gradz(4,i), fd )

      zero_prod = .false.
      if ( turb_transition ) then ! test for laminar node
        if ( iflagslen(i) < 0 ) zero_prod = .true.
      end if

      fr1 = 1.0_dp
!     SARC term:
      if (sarc) then
!       Determine s(i,j) and w(i,j)
        s11 = 0.5_dp*(gradx(2,i) + gradx(2,i))
        s12 = 0.5_dp*(grady(2,i) + gradx(3,i))
        s13 = 0.5_dp*(gradz(2,i) + gradx(4,i))
        s22 = 0.5_dp*(grady(3,i) + grady(3,i))
        s23 = 0.5_dp*(gradz(3,i) + grady(4,i))
        s33 = 0.5_dp*(gradz(4,i) + gradz(4,i))
        w12 = 0.5_dp*(grady(2,i) - gradx(3,i))
        w13 = 0.5_dp*(gradz(2,i) - gradx(4,i))
        w23 = 0.5_dp*(gradz(3,i) - grady(4,i))
        xis = s11*s11 + s22*s22 + s33*s33 +                                   &
                2._dp*s12*s12 + 2._dp*s13*s13 + 2._dp*s23*s23
        xisabs = sqrt(2._dp*xis)
        rstar=xisabs/vt
        rtilde=2._dp/(0.5_dp*(vt**2+xisabs**2))**2*                            &
                  ( -w12*s12deriv(i)*(s11-s22)                                 &
                    -w13*s13deriv(i)*(s11-s33)                                 &
                    -w23*s23deriv(i)*(s22-s33)                                 &
                    +s12*(-w12*(s22deriv(i)-s11deriv(i))                       &
                               -w13*s23deriv(i)-w23*s13deriv(i))               &
                    +s13*(-w13*(s33deriv(i)-s11deriv(i))                       &
                               -w12*s23deriv(i)+w23*s12deriv(i))               &
                    +s23*(-w23*(s33deriv(i)-s22deriv(i))                       &
                               +w12*s13deriv(i)+w13*s12deriv(i)) )
        fr1=4._dp*rstar/(1._dp+rstar)*(1._dp-sarc_cr3*atan(12._dp*rtilde))-1._dp
      end if

      added_dest_turbsq = 0._dp
      if ( turb_compress_model_code ) then
        ! compressibility - aiaa 95-0863 Shur et al.
        added_dest_turbsq = 3.5_dp*xmre*rho*velterm/(a_sq)
      endif

      !...d(source)/d(turb,vt,rnu)
      source_ddt = sa_source_ddt( xmre, distance, turb(1,i), vt, rnu, &
                                  zero_prod, added_dest_turbsq, fr1 )

      dsource1 = source_ddt%d(1) !...d(source)/d(turb)

      if ( sum(debug_vt) > 0 ) then
        dsource1 = 0._dp
      endif

      if ( tightly_couple ) then

        dsource2 = source_ddt%d(2) !...d(source)/d(vt)

        dsdvt(i) = - vol(i)*dsource2

        if ( sum(debug_vt) > 0 ) then
          dsdvt(i)  = 1._dp
        endif

      endif

      row = g2m(i)
      s_diag(row) = - vol(i)*dsource1

      if ( tightly_couple .and. eqn_set == compressible ) then

        dsource3 = source_ddt%d(3) !...d(source)/d(rnu)

        diag_drnu = - vol(i)*dsource3

        dsa(1:5) = dqc_via_dnu( diag_drnu, cstar, qnode(1,i), &
                    qnode(2,i), qnode(3,i), qnode(4,i), qnode(5,i) )

        if ( sum(debug_vt) > 0 ) then
          dsa(:) = 0._dp
        elseif ( constant_rnu_source ) then
          dsa(:) = 0._dp
        endif

        do j=1,5
          a_diag(n_sta,j,row) = a_diag(n_sta,j,row) + dsa(j)
        enddo

      endif

    end do

    call lmpi_conditional_stop(ierr_ddt(0),"ERROR_SOURCE_DDT0:source_jacob")
    call lmpi_conditional_stop(ierr_ddt(1),"ERROR_SOURCE_DDT1:source_jacob")
    call lmpi_conditional_stop(ierr_ddt(2),"ERROR_SOURCE_DDT2:source_jacob")
    call lmpi_conditional_stop(ierr_ddt(3),"ERROR_SOURCE_DDT3:source_jacob")

  end subroutine source_jacob

!================================= ADD_S_DIAG ================================80
!
! Add source term to diagonal.
!
!=============================================================================80
  subroutine add_s_diag( fl, n_sta, dof0, a_diag, slen, dtau, g2m )

    use cfl_defs,        only : turbulence_diagonal_fixups,                    &
                                turbulence_diagonal_cfl_equiv,                 &
                                fraction_negative_s_diag,                      &
                                cfl_t_jacobian
    use debug_defs,      only : composite_jacobian_lhs, d_driver_source

    integer,                    intent(in)    :: fl, n_sta, dof0
    integer,  dimension(:),     intent(in)    :: g2m
    real(dp), dimension(:),     intent(in)    :: slen, dtau
    real(dp), dimension(:,:,:), intent(inout) :: a_diag

    integer :: i, row, fixes, negatives, dofg, proc_with_max

    real(dp) :: s_term, diag_existing, cfl_equivalent, temp

  continue

    fixes     = 0
    negatives = 0
    temp      = huge(1._dp)

    do i = 1, dof0

      if ( slen(i) < epsilon(1._dp) ) cycle !skip source on wall

      row = g2m(i)

      s_term = s_terms(fl)%s_diag(row)

      a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) + s_term

      if ( s_term > 0._dp ) cycle

      negatives = negatives + 1

      diag_existing = a_diag(n_sta,n_sta,row) &
        + fraction_allowable_source*dtau(row)/cfl_t_jacobian(fl)

      if ( diag_existing > 0._dp .or. composite_jacobian_lhs ) cycle

      fixes = fixes + 1
      a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) &
                              - ( 1._dp + d_driver_source )*s_term

      ! Compute equivalent fix to CFL

      cfl_equivalent = dtau(row)/( -2._dp*s_term )
      temp           = min( temp, cfl_equivalent )

      if ( view_diag_turb_at_levels == 0 ) cycle

      write(9000+lmpi_id,"(1x,4(a,i0),5(a,e20.10))")  &
      ' ntt=',ntt,' grid=',fl,' i=',i,                &
      ' fix=',fixes,                                  &
      ' slen=',slen(i),                               &
      ' old=',a_diag(n_sta,n_sta,row) + 2._dp*s_term, &
      ' new=',a_diag(n_sta,n_sta,row),                &
      ' s_term=',s_term,' cfl_equiv=',cfl_equivalent

    end do

    call lmpi_reduce( fixes, turbulence_diagonal_fixups(fl) )
    call lmpi_bcast(         turbulence_diagonal_fixups(fl) )

    if ( turbulence_diagonal_fixups(fl) > 0 ) then
      call lmpi_max_and_maxid(real(-temp,dp),proc_with_max)
      call lmpi_bcast(temp,proc_with_max)
      turbulence_diagonal_cfl_equiv(fl) = temp
    else
      turbulence_diagonal_cfl_equiv(fl) = -1._dp
    endif


    i = negatives ; call lmpi_reduce( i, negatives )
    i = dof0      ; call lmpi_reduce( i, dofg )

    if ( lmpi_master )then
      fraction_negative_s_diag(fl) = real( negatives, dp )    &
                                      / real(      dofg, dp )
    endif

    call lmpi_max_and_maxid(real(temp,dp),proc_with_max)

  end subroutine add_s_diag

!======================= DSDQ ================================================80
!
! d(source_n)d(Q_m)
!
!=============================================================================80
  pure function dsdq( eqn_set, n, m, dsdvt, q_dof, gradx, grady, gradz,        &
                 symmetry, dgradx, dgrady, dgradz )

    integer, intent(in) :: eqn_set, n, m

    real(dp),                 intent(in) :: dgradx, dgrady, dgradz
    integer,  dimension(:),   intent(in) :: symmetry
    real(dp), dimension(:),   intent(in) :: dsdvt
    real(dp), dimension(:,:), intent(in) :: q_dof
    real(dp), dimension(:,:), intent(in) :: gradx, grady, gradz

    real(dp) :: ds_du, ds_dv, ds_dw

    real(dp), dimension(9) :: ds_dgrad
    real(dp), dimension(4) :: dsdq
    real(dp), dimension(9) :: sf

  continue

    sf(:) = 1._dp

    select case (symmetry(n))
      case (100,110,101,111)
        sf(2) = 0._dp !uy
        sf(3) = 0._dp !uz
        sf(4) = 0._dp !vx
        sf(7) = 0._dp !wx
    end select

    select case (symmetry(n))
      case (010,011,110,111)
        sf(4) = 0._dp !vx
        sf(6) = 0._dp !vz
        sf(2) = 0._dp !uy
        sf(8) = 0._dp !wy
    end select

    select case (symmetry(n))
      case (001,101,011,111)
        sf(7) = 0._dp !wx
        sf(8) = 0._dp !wy
        sf(3) = 0._dp !uz
        sf(6) = 0._dp !vz
    end select

    ds_dgrad(:) = dsdvt(n) * dsa_source_s(              &
                    gradx(2,n), grady(2,n), gradz(2,n), &
                    gradx(3,n), grady(3,n), gradz(3,n), &
                    gradx(4,n), grady(4,n), gradz(4,n) )

    ds_dgrad(:) = ds_dgrad(:)*sf(:)

    ds_du = ds_dgrad(1)*dgradx &
          + ds_dgrad(2)*dgrady &
          + ds_dgrad(3)*dgradz

    ds_dv = ds_dgrad(4)*dgradx &
          + ds_dgrad(5)*dgrady &
          + ds_dgrad(6)*dgradz

    ds_dw = ds_dgrad(7)*dgradx &
          + ds_dgrad(8)*dgrady &
          + ds_dgrad(9)*dgradz

    if ( sum(debug_vt) > 0 ) then
      ds_du = sf(1)*debug_vt(1)*dgradx&
            + sf(2)*debug_vt(2)*dgrady&
            + sf(3)*debug_vt(3)*dgradz
      ds_dv = sf(4)*debug_vt(4)*dgradx&
            + sf(5)*debug_vt(5)*dgrady&
            + sf(6)*debug_vt(6)*dgradz
      ds_dw = sf(7)*debug_vt(7)*dgradx&
            + sf(8)*debug_vt(8)*dgrady&
            + sf(9)*debug_vt(9)*dgradz
    endif

    dsdq(1:4) = dqc_via_duvw( eqn_set, ds_du, ds_dv, ds_dw,                  &
                              q_dof(1,m), q_dof(2,m), q_dof(3,m), q_dof(4,m) )

  end function dsdq

  include 'edge_augment_weight.f90'
  include 'viscosity_law.f90'
  include 'dviscosity_law.f90'
  include 'aharten.f90'
  include 'daharten.f90'
  include 'sa_source_ddt.f90'
  include 'sa_source_s.f90'
  include 'dsa_source_s.f90'
  include 'sa0_turb_abs.f90'
  include 'dsa1_turb_abs.f90'

  include 'dqc_via_duvw.f90'
  include 'dqc_via_dnu.f90'
  include 'dfdubar.f90'
  include 'des_velterm.f90'

end module turb_sa_2012

!================================= FLUX_ROE ==================================80
!
! This routine computes the fluxes using Roe's flux difference splitting
! and compute the contribution to the flux balance
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function flux_roe(xnorm, ynorm, znorm, area, face_speed, ql, qr, switch,&
                         q0, enrgyl, enrgyr, density, sonic_k, dpdq, n_species,&
                         ndim, n_mom, n_momx, n_momy, n_momz, n_etot, n_pjac,  &
                         n_energy, n_energy_last, n_turb_g, n_turb_ke,         &
                         n_dis_nutl, betat, pel,per, i_electron, couple )

    use kinddefs,        only : dp

    integer,                     intent(in) :: ndim, n_mom, n_momx, n_momy,    &
                                               n_momz, n_etot, n_species,      &
                                               n_pjac, n_energy, n_energy_last,&
                                               n_turb_g, n_turb_ke, n_dis_nutl,&
                                               i_electron
    real(dp),                    intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                    intent(in) :: face_speed, switch
    real(dp),                    intent(in) :: enrgyl, enrgyr, density
    real(dp),                    intent(in) :: pel, per
    real(dp),                    intent(in) :: betat
    real(dp), dimension(ndim),   intent(in) :: ql, qr, q0
    real(dp), dimension(n_mom),  intent(in) :: sonic_k
    real(dp), dimension(n_pjac), intent(in) :: dpdq
    logical,                     intent(in) :: couple

    real(dp), dimension(ndim)             :: flux_roe

    real(dp) :: rho, rhol, rhor, ul, ur, vl, vr, wl, wr, pressl, pressr
    real(dp) :: q2l, q2r, ubarl, ubarr, pressl_tot, pressr_tot
    real(dp) :: wat, u, v, w, q2, h, ubar, c, xc2, ci
    real(dp) :: drho, du, dv, dw, dpress, dubar, drhoi
    real(dp) :: dv1, dv2, dv3, dv4, dv4i, dv5, dv6, dv7, dvt
    real(dp) :: r21, r22, r23, r24, r31, r32, r33, r34
    real(dp) :: r41, r42, r43, r44, r51, r52, r53, r54

    real(dp), dimension(3) :: eig, abseig
    real(dp), dimension(ndim) :: t, fluxl, fluxr

    real(dp), parameter :: my_half = 0.50_dp
    real(dp), parameter :: my_1    =  1.0_dp
    real(dp), parameter :: my_0    =  0.0_dp
    integer             :: i

  continue

   !Get left and right state primitive variables

    if(n_species == 1)then
      rhol   = ql(1)
      rhor   = qr(1)
    else
      rhol   = sum(ql(1:n_species))
      rhor   = sum(qr(1:n_species))
    end if

    ul     = ql(n_momx)
    vl     = ql(n_momy)
    wl     = ql(n_momz)
    pressl = ql(n_etot)
    pressl_tot = pressl

    ur     = qr(n_momx)
    vr     = qr(n_momy)
    wr     = qr(n_momz)
    pressr = qr(n_etot)
    pressr_tot = pressr

!   Compute the remaining needed left and right state variables:

    q2l    = ul*ul + vl*vl + wl*wl
    ubarl  = xnorm*ul + ynorm*vl + znorm*wl - face_speed

    q2r    = ur*ur + vr*vr + wr*wr
    ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed

!   Compute Roe averages

    rho  = density
    wat  = rho/(rho + rhor)
    u    = q0(n_momx)
    v    = q0(n_momy)
    w    = q0(n_momz)
    h    = q0(n_etot)
    q2   = u*u + v*v + w*w
    c    = sonic_k(1)
    ubar = xnorm*u + ynorm*v + znorm*w - face_speed

!   Now compute eigenvalues, eigenvectors, and strengths

    eig(1) = ubar + c
    eig(2) = ubar - c
    eig(3) = ubar

!   Limit the eigenvalues - either adaptive or constant

    abseig = roe_efix(q2l, q2r, ubarl+face_speed, ubarr+face_speed, wat, c,    &
                      ubar, switch, eig)

!   Compute primitive variable jumps

    drho   = rhor - rhol
    dpress = pressr - pressl
    du     = ur - ul
    dv     = vr - vl
    dw     = wr - wl
    dubar  = ubarr - ubarl

!   Interface speed of sound squared

    xc2 = my_1/(c*c)

!   Jumps have units of density

    dv1 = my_half*(dpress + rho*c*dubar)*xc2
    dv2 = my_half*(dpress - rho*c*dubar)*xc2
    dv3 = rho
    dv4 = (c*c*drho - dpress)*xc2

    dvt = my_0
    if ( ( betat > my_0 ) .and. ( n_turb_g > 1 ) ) then
      dvt = betat*(q0(n_turb_ke)*drho + rho*(qr(n_turb_ke) - ql(n_turb_ke)))*xc2
      dv1 = dv1 + my_half*dvt
      dv2 = dv2 + my_half*dvt
      dv4 = dv4 -         dvt
    end if

    dv5 = my_0
    if ( n_energy > 1 ) then
      dv5 = q0(n_energy_last)*dv4 + rho*(qr(n_energy_last) - ql(n_energy_last))
    end if

    dv6 = my_0
    if ( couple .and. n_turb_g > 1 ) then
      dv6 = q0(n_turb_ke )*dv4 +rho*(qr(n_turb_ke ) - ql(n_turb_ke ))
    end if

    dv7 = my_0
    if ( couple .and. n_turb_g > 0 ) then
      dv7 = q0(n_dis_nutl)*dv4 +rho*(qr(n_dis_nutl) - ql(n_dis_nutl))
    end if

    r21 = u + c*xnorm
    r31 = v + c*ynorm
    r41 = w + c*znorm
    r51 = h + c*(ubar+face_speed)

    r22 = u - c*xnorm
    r32 = v - c*ynorm
    r42 = w - c*znorm
    r52 = h - c*(ubar+face_speed)

    r23 = du - dubar*xnorm
    r33 = dv - dubar*ynorm
    r43 = dw - dubar*znorm
    r53 = u*du + v*dv + w*dw - (ubar+face_speed)*dubar

    r24 = u
    r34 = v
    r44 = w
    r54 = q2 - dpdq(1)/dpdq(2) ! same as my_half*q2 for perfect gas

    if(n_species == 1)then
      t(1)      = abseig(1)*dv1    +abseig(2)*dv2    +abseig(3)*dv4
      t(n_etot) = abseig(1)*r51*dv1+abseig(2)*r52*dv2+abseig(3)*r53*dv3        &
                + abseig(3)*r54*dv4
    else
      t(n_etot) = abseig(1)*r51*dv1+abseig(2)*r52*dv2+abseig(3)*r53*dv3
      do i = 1,n_species
        ci    = q0(i)/rho
        drhoi = qr(i) - ql(i)
        dv4i  = (c*c*drhoi - ci*dpress)*xc2 - ci*dvt
        t(i)  = ci*(abseig(1)*dv1    +abseig(2)*dv2) + abseig(3)*dv4i
        t(n_etot) = t(n_etot) + abseig(3)*(q2 - dpdq(i)/dpdq(n_momx))*dv4i
      end do
    end if

!   Compute vib-elec energy flux terms if present

    if(n_energy > 1)then
      t(n_etot) = t(n_etot) - abseig(3)*dpdq(n_momy)*dv5/dpdq(n_momx)
      t(n_energy_last) = q0(n_energy_last)*(abseig(1)*dv1 +abseig(2)*dv2)      &
                       + dv5*abseig(3)
      fluxl(n_energy_last) = ubarl*rhol*ql(n_energy_last)
      fluxr(n_energy_last) = ubarr*rhor*qr(n_energy_last)
      if(i_electron > 0)then
        fluxl(n_energy_last) = fluxl(n_energy_last) &
                             + ubarl*pel + face_speed*pel
        fluxr(n_energy_last) = fluxr(n_energy_last) &
                             + ubarr*per + face_speed*per
      end if
    end if

    if( couple .and. n_turb_g > 0 )then
      t(n_dis_nutl) = q0(n_dis_nutl)*(abseig(1)*dv1 +abseig(2)*dv2)            &
                    + dv7*abseig(3)
      fluxl(n_dis_nutl) = ubarl*rhol*ql(n_dis_nutl)
      fluxr(n_dis_nutl) = ubarr*rhor*qr(n_dis_nutl)
      if(n_turb_g > 1)then
        t(n_etot) = t(n_etot) + (my_1 - betat/dpdq(n_momx))*abseig(3)*dv6
        t(n_turb_ke) = q0(n_turb_ke)*(abseig(1)*dv1 +abseig(2)*dv2)            &
                     + dv6*abseig(3)
        fluxl(n_turb_ke) = ubarl*rhol*ql(n_turb_ke)
        fluxr(n_turb_ke) = ubarr*rhor*qr(n_turb_ke)
        if(betat > my_0)then
          pressl_tot = pressl + betat*rhol*ql(n_turb_ke)
          pressr_tot = pressr + betat*rhor*qr(n_turb_ke)
        end if
      end if
    end if

    t(n_momx)=abseig(1)*r21*dv1+abseig(2)*r22*dv2+abseig(3)*r23*dv3            &
             +abseig(3)*r24*dv4
    t(n_momy)=abseig(1)*r31*dv1+abseig(2)*r32*dv2+abseig(3)*r33*dv3            &
             +abseig(3)*r34*dv4
    t(n_momz)=abseig(1)*r41*dv1+abseig(2)*r42*dv2+abseig(3)*r43*dv3            &
             +abseig(3)*r44*dv4

!   Compute flux using variables from the left side of face

    fluxl(n_momx) = ubarl*rhol*ul + xnorm*pressl_tot
    fluxl(n_momy) = ubarl*rhol*vl + ynorm*pressl_tot
    fluxl(n_momz) = ubarl*rhol*wl + znorm*pressl_tot
    fluxl(n_etot) = ubarl*(enrgyl+pressl_tot) + face_speed*pressl_tot

!   Compute flux using variables from right left side of face

    fluxr(n_momx) = ubarr*rhor*ur + xnorm*pressr_tot
    fluxr(n_momy) = ubarr*rhor*vr + ynorm*pressr_tot
    fluxr(n_momz) = ubarr*rhor*wr + znorm*pressr_tot
    fluxr(n_etot) = ubarr*(enrgyr+pressr_tot)  + face_speed*pressr_tot

!   Compute the contribution to the flux balance

    if(n_species == 1)then
      fluxl(1) = ubarl*rhol
      fluxr(1) = ubarr*rhor
      flux_roe(1:ndim) = my_half*area*(fluxl(1:ndim) + fluxr(1:ndim)           &
                       - t(1:ndim))
    else
      do i = 1,n_species
        flux_roe(i) = my_half*area*(ubarl*ql(i) + ubarr*qr(i) - t(i))
      end do
      flux_roe(n_momx:ndim) = my_half*area*(fluxl(n_momx:ndim)                 &
                                          + fluxr(n_momx:ndim) - t(n_momx:ndim))
    end if

  end function flux_roe

! -*- f90 -*- this turns on the emacs f90 mode

module point_solver_ddq

  use kinddefs,        only : dp, jp, odp, dqp, system_i1
  use lmpi,            only : lmpi_conditional_stop, lmpi_id
  use linear_spectral, only : monitor_rms_sr, check_preconditioner,           &
                              preconditioner_exit, set_preconditioner_target
  use lmpi_app,        only : lmpi_xfer, lmpi_start_xfer, lmpi_complete_xfer
  use interp_defs,     only : sendrecv_type
  use linear_systems,  only : monitor_eqn_group_relax

  implicit none

  private

  public :: point_solve_ddq, point_solve_target

  logical :: allow_exit

  integer :: actual_sweeps = 0

  integer, parameter :: ione = 1, itwo = 2

contains

!================================= POINT_SOLVE_TARGET ========================80
!
! Accumulate targets for point-solve routine.
!
!=============================================================================80

  subroutine point_solve_target( nb, res_matvec, n_eqns, eqns )

    integer, intent(in) :: nb, n_eqns

    integer, dimension(n_eqns), intent(in) :: eqns

    real(jp), dimension(:,:), intent(in) :: res_matvec

    integer :: n, m, j

    real(dqp), dimension(nb)   :: target

  continue

    target(:) = 0.0_dqp

    do n = 1,n_eqns

        m = eqns(n)

        do j = 1, nb
          target(j) = target(j) + res_matvec(j,m)**2
        enddo

    end do

    call set_preconditioner_target( n_eqns, nb, target, 0 )

  end subroutine point_solve_target

!================================= POINT_SOLVE_DDQ ===========================80
!
! Routes code to appropriate low-level point-solve routine.
!
!=============================================================================80

  subroutine point_solve_ddq(colored_sweeps, color_indices, max_colored_sweeps,&
                     ddq, solve_backwards, omega, nb, dq_dim, nr, nm, neqmax,  &
                          neq0, nia, nja, iam, jam,                            &
                          n_eqns, sweeps_to_do, sweeps_actual,                 &
                          res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,     &
                          color_periodic_end, color_boundary_end )

    use info_depr,      only : partial_pivoting
    use periodics,      only : periodic

    integer, intent(in) :: neq0, nia, nja, n_eqns, nb
    integer, intent(in) :: dq_dim, nr, nm, neqmax, sweeps_to_do
    integer, intent(in) :: colored_sweeps, max_colored_sweeps
    integer, intent(inout) :: sweeps_actual

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag, a_diag_lu

    integer(system_i1), dimension(nm,neq0), intent(in) :: pivot_lu

    real(odp), dimension(nm,nm,nja), intent(in) :: a_off

    logical, intent(inout) :: ddq

    type(sendrecv_type), dimension(:), pointer :: sr

    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: nb_check, ierr

    logical :: force_general_path = .false.

  continue

    allow_exit = .true.
    if ( sweeps_actual < 0 ) allow_exit = .false.

    ierr = 0

    ddq = .true.

    if ( .not.periodic .and. .not.partial_pivoting ) then

      nb_check = nb
      if ( force_general_path ) nb_check = -nb

      select case (nb_check)
      case(6)
        call point_solve_6(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case(5)
        call point_solve_5(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case(4)
        call point_solve_4(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case(3)
        call point_solve_3(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case(2)
        call point_solve_2(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case(1)
        call point_solve_1(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case(7:)
        call point_solve_n(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      case default
        call point_solve_n(colored_sweeps, color_indices, max_colored_sweeps,  &
                           neq0,nia,nja,iam,jam,                               &
                           n_eqns,          solve_backwards, omega,            &
                           nb,dq_dim,nr,nm,sweeps_to_do,neqmax,                &
                           res, dq, a_diag, a_diag_lu, a_off, sr,              &
                           color_boundary_end )
      end select

    elseif ( partial_pivoting ) then

      nb_check = nb
      if ( force_general_path ) nb_check = -nb

      select case (nb_check)
      case(5)
        call pp_psolve_5(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         dq_dim, nr, nm, sweeps_to_do, neqmax,                 &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case(4)
        call pp_psolve_4(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         dq_dim, nr, nm, sweeps_to_do, neqmax,                 &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case(3)
        call pp_psolve_3(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         dq_dim, nr, nm, sweeps_to_do, neqmax,                 &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case(2)
        call pp_psolve_2(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         dq_dim, nr, nm, sweeps_to_do, neqmax,                 &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case(1)
        call pp_psolve_1(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         dq_dim, nr, nm, sweeps_to_do, neqmax,                 &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case(6)
        call pp_psolve_6(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         dq_dim, nr, nm, sweeps_to_do, neqmax,                 &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case(7:)
        call pp_psolve_n(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         nb, dq_dim, nr, nm, sweeps_to_do, neqmax,             &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      case default
        call pp_psolve_n(colored_sweeps, color_indices, max_colored_sweeps,    &
                         neq0, nia, nja, iam, jam,                             &
                         n_eqns,            solve_backwards, omega,            &
                         nb, dq_dim, nr, nm, sweeps_to_do, neqmax,             &
                         res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,      &
                         color_boundary_end )
      end select

    else if ( periodic ) then

      !Route monitor_eqn_group_relax through general-block solve
      nb_check = nb
      if ( monitor_eqn_group_relax ) nb_check = -nb

      select case (nb_check)
      case(6)
        call ppoint_solve_6(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,                              &
                            n_eqns,          solve_backwards, omega,           &
                            nb,dq_dim,nr,nm,sweeps_to_do,neqmax,               &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )
      case(5)
        call ppoint_solve_5(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,                              &
                            n_eqns,          solve_backwards, omega,           &
                            nb,dq_dim,nr,nm,sweeps_to_do,neqmax,               &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )
      case(4)
        call ppoint_solve_4(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,                              &
                            n_eqns,          solve_backwards, omega,           &
                            nb,dq_dim,nr,nm,sweeps_to_do,neqmax,               &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )
      case(3)
        call ppoint_solve_3(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,                              &
                            n_eqns,          solve_backwards, omega,           &
                            nb,dq_dim,nr,nm,sweeps_to_do,neqmax,               &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )
      case(2)
        call ppoint_solve_2(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,                              &
                            n_eqns,          solve_backwards, omega,           &
                            nb,dq_dim,nr,nm,sweeps_to_do,neqmax,               &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )
      case(1)
        call ppoint_solve_1(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0,nia,nja,iam,jam,                              &
                            n_eqns,          solve_backwards, omega,           &
                            nb,dq_dim,nr,nm,sweeps_to_do,neqmax,               &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )
      case(7:)

        ierr = 20

      case default

        ierr = 30

      end select

    else

      ierr = 10

    endif

    call lmpi_conditional_stop(ierr,'Not programmed:point_solve_ddq')

    sweeps_actual = actual_sweeps

    if ( sweeps_actual <= 0 ) then
      ierr = 1
      write(*,*) ' lmpi_id,sweeps=',lmpi_id,sweeps_actual,sweeps_to_do
    endif
    call lmpi_conditional_stop(ierr,'Sweeps<0:point_solve_ddq')

  end subroutine point_solve_ddq

!================================ POINT_SOLVE_N ==============================80
!
! Performs G-S iteration on nxn system of specified equation set
!
!=============================================================================80

  subroutine point_solve_n(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                          solve_backwards, omega, nb, dq_dim, nr, nm,          &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_boundary_end )

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag, a_diag_lu

    real(odp), dimension(nm,nm,nja), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: j,j1,k,kk,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride, ipass

    real(jp) :: change_sign

    real(dqp), dimension(nb) :: sum_res
    real(jp), dimension(nb,1) :: b

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    actual_sweeps = 0

    sweeping : do sweep = 1, n_sweeps

      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_dqp

      color_sweep : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) <= 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(ione,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) <= 0 ) then
                start = color_indices(ione,color)
                end   = color_indices(itwo,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(itwo,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            do k = 1, nb
              b(k,1) = change_sign * res(k,n)
            end do

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)
              do k = 1, nb
                do kk = 1, nb
                  b(k,1) = b(k,1) - a_off(k,kk,j)*dq(kk,icol)
                end do
              end do
            end do

            do k = 1, nb
              do kk = 1, nb
                b(k,1) = b(k,1) - a_diag(k,kk,n)*dq(kk,n)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + b(1:nb,1)**2

! Forward

            do j = 2,nb
              j1 = j-1
              b(j:nb,1) = b(j:nb,1) - a_diag_lu(j:nb,j1,n)*b(j1,1)
            end do

! Backward

            do j = nb,1,-1
              if (j<nb) then
                j1 = j+1
                do k = j1,nb
                  b(j,1) = b(j,1) - a_diag_lu(j,k,n)*b(k,1)
                end do
              end if
              b(j,1) = b(j,1) * a_diag_lu(j,j,n)
            end do

! Copy the solution into the dq array

            b(1:nb,1)     =         omega * b(1:nb,1)
            dq(1:nb,n)    =    dq(1:nb,n) + b(1:nb,1)

          end do rhs_solve

! Exchange dq across processors between colors

          if ( color_boundary_end(1) < 0 ) then
            if ( ipass == 2 ) then
              call lmpi_xfer(dq,sr_opt=sr(color))
            endif
          else
            select case(ipass)
            case(1)
              call lmpi_start_xfer(dq,sr_opt=sr(color))
            case(2)
              call lmpi_complete_xfer(dq,sr_opt=sr(color))
            end select
          endif

        end do pass_loop

      end do color_sweep

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_eqns, nb, sum_res,          &
                            'point-res ' )
      endif

      if ( allow_exit ) then
        call check_preconditioner( n_eqns, nb, sum_res, &
                                   'point:block-n')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine point_solve_n

!Begin ReplicateBlock 6
!================================ POINT_SOLVE_6 ==============================80
!
! Performs G-S iteration on 6x6 system of specified equation set
!
!=============================================================================80

  subroutine point_solve_6(colored_sweeps, color_indices, max_colored_sweeps,  &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns, solve_backwards, omega, nb, dq_dim, nr, nm,&
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_boundary_end )

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag, a_diag_lu

    real(odp), dimension(nm,nm,nja), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride, ipass

    real(dqp), dimension(nb)   :: sum_res1
    real(dqp), dimension(nb,1) :: sum_res
    real(jp), dimension(nb,1) :: f

    real(dqp), dimension(nb,1) :: f_dqp
    real(dqp), dimension(nb,1) :: dq_dqp

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:,1) = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) <= 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(ione,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) <= 0 ) then
                start = color_indices(ione,color)
                end   = color_indices(itwo,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(itwo,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            if ( solve_backwards > 0 ) then
               f_dqp(1,1) = -res(1,n)
               f_dqp(2,1) = -res(2,n)
               f_dqp(3,1) = -res(3,n)
               f_dqp(4,1) = -res(4,n)
               f_dqp(5,1) = -res(5,n)
               f_dqp(6,1) = -res(6,n)
            else
               f_dqp(1,1) = res(1,n)
               f_dqp(2,1) = res(2,n)
               f_dqp(3,1) = res(3,n)
               f_dqp(4,1) = res(4,n)
               f_dqp(5,1) = res(5,n)
               f_dqp(6,1) = res(6,n)
            end if

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)

!...Computations access a_off entries sequentially.

              dq_dqp(1,1) = dq(1,icol)
              dq_dqp(2,1) = dq(2,icol)
              dq_dqp(3,1) = dq(3,icol)
              dq_dqp(4,1) = dq(4,icol)
              dq_dqp(5,1) = dq(5,icol)
              dq_dqp(6,1) = dq(6,icol)

              f_dqp(1,1) = f_dqp(1,1) - a_off(1,1,j)*dq_dqp(1,1)
              f_dqp(2,1) = f_dqp(2,1) - a_off(2,1,j)*dq_dqp(1,1)
              f_dqp(3,1) = f_dqp(3,1) - a_off(3,1,j)*dq_dqp(1,1)
              f_dqp(4,1) = f_dqp(4,1) - a_off(4,1,j)*dq_dqp(1,1)
              f_dqp(5,1) = f_dqp(5,1) - a_off(5,1,j)*dq_dqp(1,1)
              f_dqp(6,1) = f_dqp(6,1) - a_off(6,1,j)*dq_dqp(1,1)

              f_dqp(1,1) = f_dqp(1,1) - a_off(1,2,j)*dq_dqp(2,1)
              f_dqp(2,1) = f_dqp(2,1) - a_off(2,2,j)*dq_dqp(2,1)
              f_dqp(3,1) = f_dqp(3,1) - a_off(3,2,j)*dq_dqp(2,1)
              f_dqp(4,1) = f_dqp(4,1) - a_off(4,2,j)*dq_dqp(2,1)
              f_dqp(5,1) = f_dqp(5,1) - a_off(5,2,j)*dq_dqp(2,1)
              f_dqp(6,1) = f_dqp(6,1) - a_off(6,2,j)*dq_dqp(2,1)

              f_dqp(1,1) = f_dqp(1,1) - a_off(1,3,j)*dq_dqp(3,1)
              f_dqp(2,1) = f_dqp(2,1) - a_off(2,3,j)*dq_dqp(3,1)
              f_dqp(3,1) = f_dqp(3,1) - a_off(3,3,j)*dq_dqp(3,1)
              f_dqp(4,1) = f_dqp(4,1) - a_off(4,3,j)*dq_dqp(3,1)
              f_dqp(5,1) = f_dqp(5,1) - a_off(5,3,j)*dq_dqp(3,1)
              f_dqp(6,1) = f_dqp(6,1) - a_off(6,3,j)*dq_dqp(3,1)

              f_dqp(1,1) = f_dqp(1,1) - a_off(1,4,j)*dq_dqp(4,1)
              f_dqp(2,1) = f_dqp(2,1) - a_off(2,4,j)*dq_dqp(4,1)
              f_dqp(3,1) = f_dqp(3,1) - a_off(3,4,j)*dq_dqp(4,1)
              f_dqp(4,1) = f_dqp(4,1) - a_off(4,4,j)*dq_dqp(4,1)
              f_dqp(5,1) = f_dqp(5,1) - a_off(5,4,j)*dq_dqp(4,1)
              f_dqp(6,1) = f_dqp(6,1) - a_off(6,4,j)*dq_dqp(4,1)

              f_dqp(1,1) = f_dqp(1,1) - a_off(1,5,j)*dq_dqp(5,1)
              f_dqp(2,1) = f_dqp(2,1) - a_off(2,5,j)*dq_dqp(5,1)
              f_dqp(3,1) = f_dqp(3,1) - a_off(3,5,j)*dq_dqp(5,1)
              f_dqp(4,1) = f_dqp(4,1) - a_off(4,5,j)*dq_dqp(5,1)
              f_dqp(5,1) = f_dqp(5,1) - a_off(5,5,j)*dq_dqp(5,1)
              f_dqp(6,1) = f_dqp(6,1) - a_off(6,5,j)*dq_dqp(5,1)

              f_dqp(1,1) = f_dqp(1,1) - a_off(1,6,j)*dq_dqp(6,1)
              f_dqp(2,1) = f_dqp(2,1) - a_off(2,6,j)*dq_dqp(6,1)
              f_dqp(3,1) = f_dqp(3,1) - a_off(3,6,j)*dq_dqp(6,1)
              f_dqp(4,1) = f_dqp(4,1) - a_off(4,6,j)*dq_dqp(6,1)
              f_dqp(5,1) = f_dqp(5,1) - a_off(5,6,j)*dq_dqp(6,1)
              f_dqp(6,1) = f_dqp(6,1) - a_off(6,6,j)*dq_dqp(6,1)

            end do

            dq_dqp(1,1) = dq(1,n)
            dq_dqp(2,1) = dq(2,n)
            dq_dqp(3,1) = dq(3,n)
            dq_dqp(4,1) = dq(4,n)
            dq_dqp(5,1) = dq(5,n)
            dq_dqp(6,1) = dq(6,n)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,1,n)*dq_dqp(1,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,1,n)*dq_dqp(1,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,1,n)*dq_dqp(1,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,1,n)*dq_dqp(1,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,1,n)*dq_dqp(1,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,1,n)*dq_dqp(1,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,2,n)*dq_dqp(2,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,2,n)*dq_dqp(2,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,2,n)*dq_dqp(2,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,2,n)*dq_dqp(2,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,2,n)*dq_dqp(2,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,2,n)*dq_dqp(2,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,3,n)*dq_dqp(3,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,3,n)*dq_dqp(3,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,3,n)*dq_dqp(3,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,3,n)*dq_dqp(3,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,3,n)*dq_dqp(3,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,3,n)*dq_dqp(3,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,4,n)*dq_dqp(4,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,4,n)*dq_dqp(4,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,4,n)*dq_dqp(4,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,4,n)*dq_dqp(4,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,4,n)*dq_dqp(4,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,4,n)*dq_dqp(4,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,5,n)*dq_dqp(5,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,5,n)*dq_dqp(5,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,5,n)*dq_dqp(5,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,5,n)*dq_dqp(5,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,5,n)*dq_dqp(5,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,5,n)*dq_dqp(5,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,6,n)*dq_dqp(6,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,6,n)*dq_dqp(6,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,6,n)*dq_dqp(6,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,6,n)*dq_dqp(6,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,6,n)*dq_dqp(6,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,6,n)*dq_dqp(6,1)

            sum_res(1,1) = sum_res(1,1) + f_dqp(1,1)**2
            sum_res(2,1) = sum_res(2,1) + f_dqp(2,1)**2
            sum_res(3,1) = sum_res(3,1) + f_dqp(3,1)**2
            sum_res(4,1) = sum_res(4,1) + f_dqp(4,1)**2
            sum_res(5,1) = sum_res(5,1) + f_dqp(5,1)**2
            sum_res(6,1) = sum_res(6,1) + f_dqp(6,1)**2

! Forward...sequential access to a_diag_lu.

            f(1,1) = f_dqp(1,1)
            f(2,1) = f_dqp(2,1)
            f(3,1) = f_dqp(3,1)
            f(4,1) = f_dqp(4,1)
            f(5,1) = f_dqp(5,1)
            f(6,1) = f_dqp(6,1)

       !  f(1,1) = f(1,1)

            f(2,1) = f(2,1) - a_diag_lu(2,1,n)*f(1,1)
            f(3,1) = f(3,1) - a_diag_lu(3,1,n)*f(1,1)
            f(4,1) = f(4,1) - a_diag_lu(4,1,n)*f(1,1)
            f(5,1) = f(5,1) - a_diag_lu(5,1,n)*f(1,1)
            f(6,1) = f(6,1) - a_diag_lu(6,1,n)*f(1,1)

            f(3,1) = f(3,1) - a_diag_lu(3,2,n)*f(2,1)
            f(4,1) = f(4,1) - a_diag_lu(4,2,n)*f(2,1)
            f(5,1) = f(5,1) - a_diag_lu(5,2,n)*f(2,1)
            f(6,1) = f(6,1) - a_diag_lu(6,2,n)*f(2,1)

            f(4,1) = f(4,1) - a_diag_lu(4,3,n)*f(3,1)
            f(5,1) = f(5,1) - a_diag_lu(5,3,n)*f(3,1)
            f(6,1) = f(6,1) - a_diag_lu(6,3,n)*f(3,1)

            f(5,1) = f(5,1) - a_diag_lu(5,4,n)*f(4,1)
            f(6,1) = f(6,1) - a_diag_lu(6,4,n)*f(4,1)

            f(6,1) = f(6,1) - a_diag_lu(6,5,n)*f(5,1)

! Backward...sequential access to a_diag_lu.

            f(6,1) = f(6,1) * a_diag_lu(6,6,n)
            f(1,1) = f(1,1) - a_diag_lu(1,6,n)*f(6,1)
            f(2,1) = f(2,1) - a_diag_lu(2,6,n)*f(6,1)
            f(3,1) = f(3,1) - a_diag_lu(3,6,n)*f(6,1)
            f(4,1) = f(4,1) - a_diag_lu(4,6,n)*f(6,1)
            f(5,1) = f(5,1) - a_diag_lu(5,6,n)*f(6,1)

            f(5,1) = f(5,1) * a_diag_lu(5,5,n)
            f(1,1) = f(1,1) - a_diag_lu(1,5,n)*f(5,1)
            f(2,1) = f(2,1) - a_diag_lu(2,5,n)*f(5,1)
            f(3,1) = f(3,1) - a_diag_lu(3,5,n)*f(5,1)
            f(4,1) = f(4,1) - a_diag_lu(4,5,n)*f(5,1)

            f(4,1) = f(4,1) * a_diag_lu(4,4,n)
            f(1,1) = f(1,1) - a_diag_lu(1,4,n)*f(4,1)
            f(2,1) = f(2,1) - a_diag_lu(2,4,n)*f(4,1)
            f(3,1) = f(3,1) - a_diag_lu(3,4,n)*f(4,1)

            f(3,1) = f(3,1) * a_diag_lu(3,3,n)
            f(1,1) = f(1,1) - a_diag_lu(1,3,n)*f(3,1)
            f(2,1) = f(2,1) - a_diag_lu(2,3,n)*f(3,1)

            f(2,1) = f(2,1) * a_diag_lu(2,2,n)
            f(1,1) = f(1,1) - a_diag_lu(1,2,n)*f(2,1)

            f(1,1) = f(1,1) * a_diag_lu(1,1,n)

! Copy the solution into the dq array...early use of f entries.

            f(6,1)         =          omega * f(6,1)
            dq(6,n)        = dq(6,n)        + f(6,1)

            f(5,1)         =          omega * f(5,1)
            dq(5,n)        = dq(5,n)        + f(5,1)

            f(4,1)         =          omega * f(4,1)
            dq(4,n)        = dq(4,n)        + f(4,1)

            f(3,1)         =          omega * f(3,1)
            dq(3,n)        = dq(3,n)        + f(3,1)

            f(2,1)         =          omega * f(2,1)
            dq(2,n)        = dq(2,n)        + f(2,1)

            f(1,1)         =          omega * f(1,1)
            dq(1,n)        = dq(1,n)        + f(1,1)

          end do rhs_solve

! Exchange dq across processors between colors

          if ( color_boundary_end(1) < 0 ) then
            if ( ipass == 2 ) then
              call lmpi_xfer(dq,sr_opt=sr(color))
            endif
          else
            select case(ipass)
            case(1)
              call lmpi_start_xfer(dq,sr_opt=sr(color))
            case(2)
              call lmpi_complete_xfer(dq,sr_opt=sr(color))
            end select
          endif

        end do pass_loop

      end do color_sweeps

      sum_res1(:) = sum_res(:,1)

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_eqns, nb, sum_res1,         &
                            'point-res ' )
      endif
      if ( allow_exit ) then
        call check_preconditioner( n_eqns, nb, sum_res1, &
                                   'point')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine point_solve_6
!End ReplicateBlock 6

!Begin ReplicateBlock 6
!================================ PPOINT_SOLVE_6 =============================80
!
! Performs G-S iteration on 6x6 system of specified equation set
! Periodic version
!
!=============================================================================80

  subroutine ppoint_solve_6(colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                          solve_backwards, omega, nb, dq_dim, nr, nm,          &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag, a_diag_lu, a_off, sr,             &
                            color_periodic_end, color_boundary_end )

    use periodics, only : periodic_nature, nperiodic, periodic_data

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_periodic_end
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag, a_diag_lu

    real(odp), dimension(nm,nm,nja), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: j,n,sweep,icol,istart,iend,start,end
    integer :: color, loop_colored_sweeps, max_ind, ipass
    integer :: sweep_start, sweep_end, sweep_stride, group
    integer :: primary_entry, periodic_end

    real(jp), dimension(nb,1) :: f

    real(dqp), dimension(nb)   :: sum_res1
    real(dqp), dimension(nb,1) :: sum_res
    real(dqp), dimension(nb,1) :: f_dqp
    real(dqp), dimension(nb,nperiodic) :: stored_f
    real(dqp), dimension(nb,1) :: dq_dqp

  continue

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    actual_sweeps = 0

    sweeping : do sweep = 1, n_sweeps

      actual_sweeps = actual_sweeps + 1

      sum_res(:,1) = 0.0_dqp

      stored_f(:,:) = 0.0_dqp

      color_sweeps : do color = sweep_start, sweep_end, sweep_stride

        start        =  1
        periodic_end = -1

        if(color <= colored_sweeps) then
          start        = color_indices(ione,color)
          periodic_end = color_periodic_end(color)
        endif

! First gather all contributions to stored_f from periodic points

        gather_stored_f : do n = start, periodic_end

          group = abs(periodic_nature(n))

          if ( group == 0 ) cycle gather_stored_f

          if ( solve_backwards > 0 ) then
             f_dqp(1,1) = -res(1,n)
             f_dqp(2,1) = -res(2,n)
             f_dqp(3,1) = -res(3,n)
             f_dqp(4,1) = -res(4,n)
             f_dqp(5,1) = -res(5,n)
             f_dqp(6,1) = -res(6,n)
          else
             f_dqp(1,1) = res(1,n)
             f_dqp(2,1) = res(2,n)
             f_dqp(3,1) = res(3,n)
             f_dqp(4,1) = res(4,n)
             f_dqp(5,1) = res(5,n)
             f_dqp(6,1) = res(6,n)
          end if

          istart = iam(n)
          iend   = iam(n+1)-1

          do j = istart,iend
            icol = jam(j)

            dq_dqp(1,1) = dq(1,icol)
            dq_dqp(2,1) = dq(2,icol)
            dq_dqp(3,1) = dq(3,icol)
            dq_dqp(4,1) = dq(4,icol)
            dq_dqp(5,1) = dq(5,icol)
            dq_dqp(6,1) = dq(6,icol)

            f_dqp(1,1) = f_dqp(1,1) - a_off(1,1,j)*dq_dqp(1,1)
            f_dqp(2,1) = f_dqp(2,1) - a_off(2,1,j)*dq_dqp(1,1)
            f_dqp(3,1) = f_dqp(3,1) - a_off(3,1,j)*dq_dqp(1,1)
            f_dqp(4,1) = f_dqp(4,1) - a_off(4,1,j)*dq_dqp(1,1)
            f_dqp(5,1) = f_dqp(5,1) - a_off(5,1,j)*dq_dqp(1,1)
            f_dqp(6,1) = f_dqp(6,1) - a_off(6,1,j)*dq_dqp(1,1)

            f_dqp(1,1) = f_dqp(1,1) - a_off(1,2,j)*dq_dqp(2,1)
            f_dqp(2,1) = f_dqp(2,1) - a_off(2,2,j)*dq_dqp(2,1)
            f_dqp(3,1) = f_dqp(3,1) - a_off(3,2,j)*dq_dqp(2,1)
            f_dqp(4,1) = f_dqp(4,1) - a_off(4,2,j)*dq_dqp(2,1)
            f_dqp(5,1) = f_dqp(5,1) - a_off(5,2,j)*dq_dqp(2,1)
            f_dqp(6,1) = f_dqp(6,1) - a_off(6,2,j)*dq_dqp(2,1)

            f_dqp(1,1) = f_dqp(1,1) - a_off(1,3,j)*dq_dqp(3,1)
            f_dqp(2,1) = f_dqp(2,1) - a_off(2,3,j)*dq_dqp(3,1)
            f_dqp(3,1) = f_dqp(3,1) - a_off(3,3,j)*dq_dqp(3,1)
            f_dqp(4,1) = f_dqp(4,1) - a_off(4,3,j)*dq_dqp(3,1)
            f_dqp(5,1) = f_dqp(5,1) - a_off(5,3,j)*dq_dqp(3,1)
            f_dqp(6,1) = f_dqp(6,1) - a_off(6,3,j)*dq_dqp(3,1)

            f_dqp(1,1) = f_dqp(1,1) - a_off(1,4,j)*dq_dqp(4,1)
            f_dqp(2,1) = f_dqp(2,1) - a_off(2,4,j)*dq_dqp(4,1)
            f_dqp(3,1) = f_dqp(3,1) - a_off(3,4,j)*dq_dqp(4,1)
            f_dqp(4,1) = f_dqp(4,1) - a_off(4,4,j)*dq_dqp(4,1)
            f_dqp(5,1) = f_dqp(5,1) - a_off(5,4,j)*dq_dqp(4,1)
            f_dqp(6,1) = f_dqp(6,1) - a_off(6,4,j)*dq_dqp(4,1)

            f_dqp(1,1) = f_dqp(1,1) - a_off(1,5,j)*dq_dqp(5,1)
            f_dqp(2,1) = f_dqp(2,1) - a_off(2,5,j)*dq_dqp(5,1)
            f_dqp(3,1) = f_dqp(3,1) - a_off(3,5,j)*dq_dqp(5,1)
            f_dqp(4,1) = f_dqp(4,1) - a_off(4,5,j)*dq_dqp(5,1)
            f_dqp(5,1) = f_dqp(5,1) - a_off(5,5,j)*dq_dqp(5,1)
            f_dqp(6,1) = f_dqp(6,1) - a_off(6,5,j)*dq_dqp(5,1)

            f_dqp(1,1) = f_dqp(1,1) - a_off(1,6,j)*dq_dqp(6,1)
            f_dqp(2,1) = f_dqp(2,1) - a_off(2,6,j)*dq_dqp(6,1)
            f_dqp(3,1) = f_dqp(3,1) - a_off(3,6,j)*dq_dqp(6,1)
            f_dqp(4,1) = f_dqp(4,1) - a_off(4,6,j)*dq_dqp(6,1)
            f_dqp(5,1) = f_dqp(5,1) - a_off(5,6,j)*dq_dqp(6,1)
            f_dqp(6,1) = f_dqp(6,1) - a_off(6,6,j)*dq_dqp(6,1)

          end do

          if ( periodic_nature(n) > 0 ) then

            dq_dqp(1,1) = dq(1,n)
            dq_dqp(2,1) = dq(2,n)
            dq_dqp(3,1) = dq(3,n)
            dq_dqp(4,1) = dq(4,n)
            dq_dqp(5,1) = dq(5,n)
            dq_dqp(6,1) = dq(6,n)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,1,n)*dq_dqp(1,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,1,n)*dq_dqp(1,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,1,n)*dq_dqp(1,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,1,n)*dq_dqp(1,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,1,n)*dq_dqp(1,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,1,n)*dq_dqp(1,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,2,n)*dq_dqp(2,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,2,n)*dq_dqp(2,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,2,n)*dq_dqp(2,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,2,n)*dq_dqp(2,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,2,n)*dq_dqp(2,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,2,n)*dq_dqp(2,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,3,n)*dq_dqp(3,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,3,n)*dq_dqp(3,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,3,n)*dq_dqp(3,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,3,n)*dq_dqp(3,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,3,n)*dq_dqp(3,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,3,n)*dq_dqp(3,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,4,n)*dq_dqp(4,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,4,n)*dq_dqp(4,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,4,n)*dq_dqp(4,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,4,n)*dq_dqp(4,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,4,n)*dq_dqp(4,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,4,n)*dq_dqp(4,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,5,n)*dq_dqp(5,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,5,n)*dq_dqp(5,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,5,n)*dq_dqp(5,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,5,n)*dq_dqp(5,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,5,n)*dq_dqp(5,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,5,n)*dq_dqp(5,1)

            f_dqp(1,1) = f_dqp(1,1) - a_diag(1,6,n)*dq_dqp(6,1)
            f_dqp(2,1) = f_dqp(2,1) - a_diag(2,6,n)*dq_dqp(6,1)
            f_dqp(3,1) = f_dqp(3,1) - a_diag(3,6,n)*dq_dqp(6,1)
            f_dqp(4,1) = f_dqp(4,1) - a_diag(4,6,n)*dq_dqp(6,1)
            f_dqp(5,1) = f_dqp(5,1) - a_diag(5,6,n)*dq_dqp(6,1)
            f_dqp(6,1) = f_dqp(6,1) - a_diag(6,6,n)*dq_dqp(6,1)

          endif

          stored_f(1,group) = stored_f(1,group) + f_dqp(1,1)
          stored_f(2,group) = stored_f(2,group) + f_dqp(2,1)
          stored_f(3,group) = stored_f(3,group) + f_dqp(3,1)
          stored_f(4,group) = stored_f(4,group) + f_dqp(4,1)
          stored_f(5,group) = stored_f(5,group) + f_dqp(5,1)
          stored_f(6,group) = stored_f(6,group) + f_dqp(6,1)

        end do gather_stored_f

! Now solve for dq everywhere in color

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then

            start = 1
            end   = 0

          else

            max_ind = max(color_boundary_end(color),color_periodic_end(color))

            select case(ipass)
            case(1)
              if ( max_ind == 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(ione,color)
                end   = max_ind
              endif
            case(2)
              if ( max_ind == 0 ) then
                start = color_indices(ione,color)
                end   = color_indices(itwo,color)
              else
                start = max_ind+1
                end   = color_indices(itwo,color)
              endif
            end select

          endif

          rhs_solve : do n = start, end

            group = 0
            if ( n <= periodic_end ) group = periodic_nature(n)

! Do not solve secondary periodic points, they will be copied from
! primary points later

            if ( group < 0 ) cycle rhs_solve

            point_is_periodic : if ( group == 0 ) then

              if ( solve_backwards > 0 ) then
                f_dqp(1,1) = -res(1,n)
                f_dqp(2,1) = -res(2,n)
                f_dqp(3,1) = -res(3,n)
                f_dqp(4,1) = -res(4,n)
                f_dqp(5,1) = -res(5,n)
                f_dqp(6,1) = -res(6,n)
              else
                f_dqp(1,1) = res(1,n)
                f_dqp(2,1) = res(2,n)
                f_dqp(3,1) = res(3,n)
                f_dqp(4,1) = res(4,n)
                f_dqp(5,1) = res(5,n)
                f_dqp(6,1) = res(6,n)
              end if

              istart = iam(n)
              iend   = iam(n+1)-1

              do j = istart,iend
                icol = jam(j)

                dq_dqp(1,1) = dq(1,icol)
                dq_dqp(2,1) = dq(2,icol)
                dq_dqp(3,1) = dq(3,icol)
                dq_dqp(4,1) = dq(4,icol)
                dq_dqp(5,1) = dq(5,icol)
                dq_dqp(6,1) = dq(6,icol)

                f_dqp(1,1) = f_dqp(1,1) - a_off(1,1,j)*dq_dqp(1,1)
                f_dqp(2,1) = f_dqp(2,1) - a_off(2,1,j)*dq_dqp(1,1)
                f_dqp(3,1) = f_dqp(3,1) - a_off(3,1,j)*dq_dqp(1,1)
                f_dqp(4,1) = f_dqp(4,1) - a_off(4,1,j)*dq_dqp(1,1)
                f_dqp(5,1) = f_dqp(5,1) - a_off(5,1,j)*dq_dqp(1,1)
                f_dqp(6,1) = f_dqp(6,1) - a_off(6,1,j)*dq_dqp(1,1)

                f_dqp(1,1) = f_dqp(1,1) - a_off(1,2,j)*dq_dqp(2,1)
                f_dqp(2,1) = f_dqp(2,1) - a_off(2,2,j)*dq_dqp(2,1)
                f_dqp(3,1) = f_dqp(3,1) - a_off(3,2,j)*dq_dqp(2,1)
                f_dqp(4,1) = f_dqp(4,1) - a_off(4,2,j)*dq_dqp(2,1)
                f_dqp(5,1) = f_dqp(5,1) - a_off(5,2,j)*dq_dqp(2,1)
                f_dqp(6,1) = f_dqp(6,1) - a_off(6,2,j)*dq_dqp(2,1)

                f_dqp(1,1) = f_dqp(1,1) - a_off(1,3,j)*dq_dqp(3,1)
                f_dqp(2,1) = f_dqp(2,1) - a_off(2,3,j)*dq_dqp(3,1)
                f_dqp(3,1) = f_dqp(3,1) - a_off(3,3,j)*dq_dqp(3,1)
                f_dqp(4,1) = f_dqp(4,1) - a_off(4,3,j)*dq_dqp(3,1)
                f_dqp(5,1) = f_dqp(5,1) - a_off(5,3,j)*dq_dqp(3,1)
                f_dqp(6,1) = f_dqp(6,1) - a_off(6,3,j)*dq_dqp(3,1)

                f_dqp(1,1) = f_dqp(1,1) - a_off(1,4,j)*dq_dqp(4,1)
                f_dqp(2,1) = f_dqp(2,1) - a_off(2,4,j)*dq_dqp(4,1)
                f_dqp(3,1) = f_dqp(3,1) - a_off(3,4,j)*dq_dqp(4,1)
                f_dqp(4,1) = f_dqp(4,1) - a_off(4,4,j)*dq_dqp(4,1)
                f_dqp(5,1) = f_dqp(5,1) - a_off(5,4,j)*dq_dqp(4,1)
                f_dqp(6,1) = f_dqp(6,1) - a_off(6,4,j)*dq_dqp(4,1)

                f_dqp(1,1) = f_dqp(1,1) - a_off(1,5,j)*dq_dqp(5,1)
                f_dqp(2,1) = f_dqp(2,1) - a_off(2,5,j)*dq_dqp(5,1)
                f_dqp(3,1) = f_dqp(3,1) - a_off(3,5,j)*dq_dqp(5,1)
                f_dqp(4,1) = f_dqp(4,1) - a_off(4,5,j)*dq_dqp(5,1)
                f_dqp(5,1) = f_dqp(5,1) - a_off(5,5,j)*dq_dqp(5,1)
                f_dqp(6,1) = f_dqp(6,1) - a_off(6,5,j)*dq_dqp(5,1)

                f_dqp(1,1) = f_dqp(1,1) - a_off(1,6,j)*dq_dqp(6,1)
                f_dqp(2,1) = f_dqp(2,1) - a_off(2,6,j)*dq_dqp(6,1)
                f_dqp(3,1) = f_dqp(3,1) - a_off(3,6,j)*dq_dqp(6,1)
                f_dqp(4,1) = f_dqp(4,1) - a_off(4,6,j)*dq_dqp(6,1)
                f_dqp(5,1) = f_dqp(5,1) - a_off(5,6,j)*dq_dqp(6,1)
                f_dqp(6,1) = f_dqp(6,1) - a_off(6,6,j)*dq_dqp(6,1)

              end do

              dq_dqp(1,1) = dq(1,n)
              dq_dqp(2,1) = dq(2,n)
              dq_dqp(3,1) = dq(3,n)
              dq_dqp(4,1) = dq(4,n)
              dq_dqp(5,1) = dq(5,n)
              dq_dqp(6,1) = dq(6,n)

              f_dqp(1,1) = f_dqp(1,1) - a_diag(1,1,n)*dq_dqp(1,1)
              f_dqp(2,1) = f_dqp(2,1) - a_diag(2,1,n)*dq_dqp(1,1)
              f_dqp(3,1) = f_dqp(3,1) - a_diag(3,1,n)*dq_dqp(1,1)
              f_dqp(4,1) = f_dqp(4,1) - a_diag(4,1,n)*dq_dqp(1,1)
              f_dqp(5,1) = f_dqp(5,1) - a_diag(5,1,n)*dq_dqp(1,1)
              f_dqp(6,1) = f_dqp(6,1) - a_diag(6,1,n)*dq_dqp(1,1)

              f_dqp(1,1) = f_dqp(1,1) - a_diag(1,2,n)*dq_dqp(2,1)
              f_dqp(2,1) = f_dqp(2,1) - a_diag(2,2,n)*dq_dqp(2,1)
              f_dqp(3,1) = f_dqp(3,1) - a_diag(3,2,n)*dq_dqp(2,1)
              f_dqp(4,1) = f_dqp(4,1) - a_diag(4,2,n)*dq_dqp(2,1)
              f_dqp(5,1) = f_dqp(5,1) - a_diag(5,2,n)*dq_dqp(2,1)
              f_dqp(6,1) = f_dqp(6,1) - a_diag(6,2,n)*dq_dqp(2,1)

              f_dqp(1,1) = f_dqp(1,1) - a_diag(1,3,n)*dq_dqp(3,1)
              f_dqp(2,1) = f_dqp(2,1) - a_diag(2,3,n)*dq_dqp(3,1)
              f_dqp(3,1) = f_dqp(3,1) - a_diag(3,3,n)*dq_dqp(3,1)
              f_dqp(4,1) = f_dqp(4,1) - a_diag(4,3,n)*dq_dqp(3,1)
              f_dqp(5,1) = f_dqp(5,1) - a_diag(5,3,n)*dq_dqp(3,1)
              f_dqp(6,1) = f_dqp(6,1) - a_diag(6,3,n)*dq_dqp(3,1)

              f_dqp(1,1) = f_dqp(1,1) - a_diag(1,4,n)*dq_dqp(4,1)
              f_dqp(2,1) = f_dqp(2,1) - a_diag(2,4,n)*dq_dqp(4,1)
              f_dqp(3,1) = f_dqp(3,1) - a_diag(3,4,n)*dq_dqp(4,1)
              f_dqp(4,1) = f_dqp(4,1) - a_diag(4,4,n)*dq_dqp(4,1)
              f_dqp(5,1) = f_dqp(5,1) - a_diag(5,4,n)*dq_dqp(4,1)
              f_dqp(6,1) = f_dqp(6,1) - a_diag(6,4,n)*dq_dqp(4,1)

              f_dqp(1,1) = f_dqp(1,1) - a_diag(1,5,n)*dq_dqp(5,1)
              f_dqp(2,1) = f_dqp(2,1) - a_diag(2,5,n)*dq_dqp(5,1)
              f_dqp(3,1) = f_dqp(3,1) - a_diag(3,5,n)*dq_dqp(5,1)
              f_dqp(4,1) = f_dqp(4,1) - a_diag(4,5,n)*dq_dqp(5,1)
              f_dqp(5,1) = f_dqp(5,1) - a_diag(5,5,n)*dq_dqp(5,1)
              f_dqp(6,1) = f_dqp(6,1) - a_diag(6,5,n)*dq_dqp(5,1)

              f_dqp(1,1) = f_dqp(1,1) - a_diag(1,6,n)*dq_dqp(6,1)
              f_dqp(2,1) = f_dqp(2,1) - a_diag(2,6,n)*dq_dqp(6,1)
              f_dqp(3,1) = f_dqp(3,1) - a_diag(3,6,n)*dq_dqp(6,1)
              f_dqp(4,1) = f_dqp(4,1) - a_diag(4,6,n)*dq_dqp(6,1)
              f_dqp(5,1) = f_dqp(5,1) - a_diag(5,6,n)*dq_dqp(6,1)
              f_dqp(6,1) = f_dqp(6,1) - a_diag(6,6,n)*dq_dqp(6,1)

            else point_is_periodic

              f_dqp(1,1) = stored_f(1,group)
              f_dqp(2,1) = stored_f(2,group)
              f_dqp(3,1) = stored_f(3,group)
              f_dqp(4,1) = stored_f(4,group)
              f_dqp(5,1) = stored_f(5,group)
              f_dqp(6,1) = stored_f(6,group)

            endif point_is_periodic

            sum_res(1,1) = sum_res(1,1) + f_dqp(1,1)**2
            sum_res(2,1) = sum_res(2,1) + f_dqp(2,1)**2
            sum_res(3,1) = sum_res(3,1) + f_dqp(3,1)**2
            sum_res(4,1) = sum_res(4,1) + f_dqp(4,1)**2
            sum_res(5,1) = sum_res(5,1) + f_dqp(5,1)**2
            sum_res(6,1) = sum_res(6,1) + f_dqp(6,1)**2

! Forward...sequential access to a_diag_lu.

            f(1,1) = f_dqp(1,1)
            f(2,1) = f_dqp(2,1)
            f(3,1) = f_dqp(3,1)
            f(4,1) = f_dqp(4,1)
            f(5,1) = f_dqp(5,1)
            f(6,1) = f_dqp(6,1)

         !  f(1,1) = f(1,1)

            f(2,1) = f(2,1) - a_diag_lu(2,1,n)*f(1,1)
            f(3,1) = f(3,1) - a_diag_lu(3,1,n)*f(1,1)
            f(4,1) = f(4,1) - a_diag_lu(4,1,n)*f(1,1)
            f(5,1) = f(5,1) - a_diag_lu(5,1,n)*f(1,1)
            f(6,1) = f(6,1) - a_diag_lu(6,1,n)*f(1,1)

            f(3,1) = f(3,1) - a_diag_lu(3,2,n)*f(2,1)
            f(4,1) = f(4,1) - a_diag_lu(4,2,n)*f(2,1)
            f(5,1) = f(5,1) - a_diag_lu(5,2,n)*f(2,1)
            f(6,1) = f(6,1) - a_diag_lu(6,2,n)*f(2,1)

            f(4,1) = f(4,1) - a_diag_lu(4,3,n)*f(3,1)
            f(5,1) = f(5,1) - a_diag_lu(5,3,n)*f(3,1)
            f(6,1) = f(6,1) - a_diag_lu(6,3,n)*f(3,1)

            f(5,1) = f(5,1) - a_diag_lu(5,4,n)*f(4,1)
            f(6,1) = f(6,1) - a_diag_lu(6,4,n)*f(4,1)

            f(6,1) = f(6,1) - a_diag_lu(6,5,n)*f(5,1)

! Backward...sequential access to a_diag_lu.

            f(6,1) = f(6,1) * a_diag_lu(6,6,n)
            f(1,1) = f(1,1) - a_diag_lu(1,6,n)*f(6,1)
            f(2,1) = f(2,1) - a_diag_lu(2,6,n)*f(6,1)
            f(3,1) = f(3,1) - a_diag_lu(3,6,n)*f(6,1)
            f(4,1) = f(4,1) - a_diag_lu(4,6,n)*f(6,1)
            f(5,1) = f(5,1) - a_diag_lu(5,6,n)*f(6,1)

            f(5,1) = f(5,1) * a_diag_lu(5,5,n)
            f(1,1) = f(1,1) - a_diag_lu(1,5,n)*f(5,1)
            f(2,1) = f(2,1) - a_diag_lu(2,5,n)*f(5,1)
            f(3,1) = f(3,1) - a_diag_lu(3,5,n)*f(5,1)
            f(4,1) = f(4,1) - a_diag_lu(4,5,n)*f(5,1)

            f(4,1) = f(4,1) * a_diag_lu(4,4,n)
            f(1,1) = f(1,1) - a_diag_lu(1,4,n)*f(4,1)
            f(2,1) = f(2,1) - a_diag_lu(2,4,n)*f(4,1)
            f(3,1) = f(3,1) - a_diag_lu(3,4,n)*f(4,1)

            f(3,1) = f(3,1) * a_diag_lu(3,3,n)
            f(1,1) = f(1,1) - a_diag_lu(1,3,n)*f(3,1)
            f(2,1) = f(2,1) - a_diag_lu(2,3,n)*f(3,1)

            f(2,1) = f(2,1) * a_diag_lu(2,2,n)
            f(1,1) = f(1,1) - a_diag_lu(1,2,n)*f(2,1)

            f(1,1) = f(1,1) * a_diag_lu(1,1,n)

! Copy the solution into the dq array...early use of f entries.

            f(6,1)         =          omega * f(6,1)
            dq(6,n)        = dq(6,n)        + f(6,1)

            f(5,1)         =          omega * f(5,1)
            dq(5,n)        = dq(5,n)        + f(5,1)

            f(4,1)         =          omega * f(4,1)
            dq(4,n)        = dq(4,n)        + f(4,1)

            f(3,1)         =          omega * f(3,1)
            dq(3,n)        = dq(3,n)        + f(3,1)

            f(2,1)         =          omega * f(2,1)
            dq(2,n)        = dq(2,n)        + f(2,1)

            f(1,1)         =          omega * f(1,1)
            dq(1,n)        = dq(1,n)        + f(1,1)

          end do rhs_solve

          if ( ipass == 1 ) then
            update_secondary : do n = start, periodic_end
              group = periodic_nature(n)
              if ( group >= 0 ) cycle update_secondary
              group = abs(group)
              primary_entry = periodic_data(group)%mlist(1)
              dq(1,n) = dq(1,primary_entry)
              dq(2,n) = dq(2,primary_entry)
              dq(3,n) = dq(3,primary_entry)
              dq(4,n) = dq(4,primary_entry)
              dq(5,n) = dq(5,primary_entry)
              dq(6,n) = dq(6,primary_entry)
            end do update_secondary
          endif

! Exchange dq across processors between colors

          if ( color_boundary_end(1) < 0 ) then
            if ( ipass == 2 ) then
              call lmpi_xfer(dq,sr_opt=sr(color))
            endif
          else
            select case(ipass)
            case(1)
              call lmpi_start_xfer(dq,sr_opt=sr(color))
            case(2)
              call lmpi_complete_xfer(dq,sr_opt=sr(color))
            end select
          endif

        end do pass_loop

      end do color_sweeps

      sum_res1(:) = sum_res(:,1)

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_eqns, nb, sum_res1,         &
                            'ppoint-res ' )
      endif
      if ( allow_exit ) then
        call check_preconditioner( n_eqns, nb, sum_res1, &
                                   'ppoint')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine ppoint_solve_6
!End ReplicateBlock 6

!================================ PP_PSOLVE_N ================================80
!
! Performs G-S iteration on nxn system of specified equation set
! with partial pivoting.
!
!=============================================================================80

  subroutine pp_psolve_n(                                                      &
                            colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                          solve_backwards, omega, nb, dq_dim, nr, nm,          &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,   &
                            color_boundary_end )

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax, nb

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag, a_diag_lu

    integer(system_i1), dimension(nm,neq0), intent(in) :: pivot_lu

    real(odp), dimension(nm,nm,nja), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: j, j1, k, kk, n, sweep, icol, istart, iend
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride, start, end, ipass

    real(jp) :: change_sign

    real(dqp), dimension(nb) :: sum_res
    real(jp), dimension(nb,1) :: b, c

  continue

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_dqp

      color_sweep : do color = sweep_start, sweep_end, sweep_stride

        pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) <= 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(ione,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) <= 0 ) then
                start = color_indices(ione,color)
                end   = color_indices(itwo,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(itwo,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            do k = 1, nb
              c(k,1) = change_sign * res(k,n)
            end do

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)
              do k = 1, nb
                do kk = 1, nb
                  c(k,1) = c(k,1) - a_off(k,kk,j)*dq(kk,icol)
                end do
              end do
            end do

            do k = 1, nb
              do kk = 1, nb
                c(k,1) = c(k,1) - a_diag(k,kk,n)*dq(kk,n)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + c(1:nb,1)**2

! Account for LU pivoting : b = pivoted(c)

            do k=1,nb
              b(k,1) = c( pivot_lu(k,n) , 1 )
            enddo

! Forward L*b = b

            do j = 2,nb
              j1 = j-1
              b(j:nb,1) = b(j:nb,1) - a_diag_lu(j:nb,j1,n)*b(j1,1)
            end do

! Backward U*b = b

            b(nb,1) = b(nb,1) * a_diag_lu(nb,nb,n)
            do j = nb-1,1,-1
              j1 = j+1
              do k = j1,nb
                b(j,1) = b(j,1) - a_diag_lu(j,k,n)*b(k,1)
              end do
              b(j,1) = b(j,1) * a_diag_lu(j,j,n)
            end do

! Copy b into the dq array

            b(1:nb,1)     =         omega * b(1:nb,1)
            dq(1:nb,n)    =    dq(1:nb,n) + b(1:nb,1)

          end do rhs_solve

! Exchange dq across processors between colors

          if ( color_boundary_end(1) < 0 ) then
            if ( ipass == 2 ) then
              call lmpi_xfer(dq,sr_opt=sr(color))
            endif
          else
            select case(ipass)
            case(1)
              call lmpi_start_xfer(dq,sr_opt=sr(color))
            case(2)
              call lmpi_complete_xfer(dq,sr_opt=sr(color))
            end select
          endif

        end do pass_loop

      end do color_sweep

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_eqns, nb, sum_res,          &
                            'point-res ' )
      endif
      if ( allow_exit ) then
        call check_preconditioner( n_eqns, nb, sum_res, &
                                   'point:partial-pivoting:block-n')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine pp_psolve_n

!Begin ReplicateBlock 6
!================================ PP_PSOLVE_6 ================================80
!
! Performs G-S iteration on nxn system of specified equation set
! with partial pivoting.
!
!=============================================================================80

  subroutine pp_psolve_6(                                                      &
                            colored_sweeps, color_indices, max_colored_sweeps, &
                            neq0, nia, nja, iam, jam,                          &
                            n_eqns,                                            &
                          solve_backwards, omega, dq_dim, nr, nm,              &
                            n_sweeps, neqmax,                                  &
                            res, dq, a_diag, a_diag_lu, pivot_lu, a_off, sr,   &
                            color_boundary_end )

    integer, intent(in) :: colored_sweeps, max_colored_sweeps, neq0, nia
    integer, intent(in) :: nja, n_eqns, dq_dim, nr, nm, n_sweeps
    integer, intent(in) :: neqmax

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices
    integer, dimension(colored_sweeps),   intent(in) :: color_boundary_end

    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam

    real(dp), dimension(nr,neq0), intent(in) :: res

    real(dqp), dimension(dq_dim,neqmax), intent(inout) :: dq

    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag, a_diag_lu

    integer(system_i1), dimension(nm,neq0), intent(in) :: pivot_lu

    real(odp), dimension(nm,nm,nja), intent(in) :: a_off

    type(sendrecv_type), dimension(:), pointer :: sr

    real(dp), intent(in) :: omega
    integer, intent(in) :: solve_backwards

    integer :: j, k, n, sweep, icol, istart, iend, nbsr
    integer :: color, loop_colored_sweeps
    integer :: sweep_start, sweep_end, sweep_stride, start, end, ipass

    real(dp) :: change_sign

    real(dqp), dimension(6_system_i1) :: sum_res    !_6 via Ruby script

    real(jp), dimension(6_system_i1,1) :: b         !_6 via Ruby script

    real(dqp), dimension(6_system_i1,1) :: c         !_6 via Ruby script

    integer(system_i1), parameter :: nb = 6_system_i1  !_6 via Ruby script

  continue

    nbsr = nb

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    if ( n_eqns <= 0 ) then
      sweep_start  = 1
      sweep_end    = 2
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_dqp

      color_sweep : do color = sweep_start, sweep_end, sweep_stride

          pass_loop : do ipass = 1, 2

          if ( color > colored_sweeps ) then
            start = 1
            end   = 0
          else
            select case(ipass)
            case(1)
              if ( color_boundary_end(color) <= 0 ) then
                start = 1
                end   = 0
              else
                start = color_indices(ione,color)
                end   = color_boundary_end(color)
              endif
            case(2)
              if ( color_boundary_end(color) <= 0 ) then
                start = color_indices(ione,color)
                end   = color_indices(itwo,color)
              else
                start = color_boundary_end(color)+1
                end   = color_indices(itwo,color)
              endif
            end select
          endif

          rhs_solve : do n = start, end

            do k = 1, nb
              c(k,1) = change_sign * res(k,n)
            end do

            istart = iam(n)
            iend   = iam(n+1)-1

            do j = istart,iend
              icol = jam(j)
              do k = 1, nb
                c(k,1) = c(k,1) - a_off(k,1,j)*dq(1,icol) &
                                - a_off(k,2,j)*dq(2,icol) &
                                - a_off(k,3,j)*dq(3,icol) &
                                - a_off(k,4,j)*dq(4,icol) &
                                - a_off(k,5,j)*dq(5,icol) &
                                - a_off(k,6,j)*dq(6,icol)
              end do
            end do

            do k = 1, nb
              c(k,1) = c(k,1) - a_diag(k,1,n)*dq(1,n) &
                              - a_diag(k,2,n)*dq(2,n) &
                              - a_diag(k,3,n)*dq(3,n) &
                              - a_diag(k,4,n)*dq(4,n) &
                              - a_diag(k,5,n)*dq(5,n) &
                              - a_diag(k,6,n)*dq(6,n)
            end do

          sum_res(1:nb) = sum_res(1:nb) + c(1:nb,1)**2

! Account for LU pivoting : b=pivoted(c)

          do k=1,nb
            b(k,1) = c( pivot_lu(k,n) , 1 )
          enddo

! Forward L*b = b

          b(2,1) = b(2,1) - a_diag_lu(2,1,n)*b(1,1)
          b(3,1) = b(3,1) - a_diag_lu(3,1,n)*b(1,1)      &
                          - a_diag_lu(3,2,n)*b(2,1)
          b(4,1) = b(4,1) - a_diag_lu(4,1,n)*b(1,1)      &
                          - a_diag_lu(4,2,n)*b(2,1)      &
                          - a_diag_lu(4,3,n)*b(3,1)
          b(5,1) = b(5,1) - a_diag_lu(5,1,n)*b(1,1)      &
                          - a_diag_lu(5,2,n)*b(2,1)      &
                          - a_diag_lu(5,3,n)*b(3,1)      &
                          - a_diag_lu(5,4,n)*b(4,1)
          b(6,1) = b(6,1) - a_diag_lu(6,1,n)*b(1,1)      &
                          - a_diag_lu(6,2,n)*b(2,1)      &
                          - a_diag_lu(6,3,n)*b(3,1)      &
                          - a_diag_lu(6,4,n)*b(4,1)      &
                          - a_diag_lu(6,5,n)*b(5,1)

! Backward U*b = b

            b(6,1) =   b(6,1)*a_diag_lu(6,6,n)
            b(5,1) = ( b(5,1)                                         &
                              - a_diag_lu(5,6,n)*b(6,1)               &
                                                   )*a_diag_lu(5,5,n)
            b(4,1) = ( b(4,1)                                         &
                              - a_diag_lu(4,5,n)*b(5,1)               &
                              - a_diag_lu(4,6,n)*b(6,1)               &
                                                   )*a_diag_lu(4,4,n)
            b(3,1) = ( b(3,1)                                         &
                              - a_diag_lu(3,4,n)*b(4,1)               &
                              - a_diag_lu(3,5,n)*b(5,1)               &
                              - a_diag_lu(3,6,n)*b(6,1)               &
                                                   )*a_diag_lu(3,3,n)
            b(2,1) = ( b(2,1)                                         &
                              - a_diag_lu(2,3,n)*b(3,1)               &
                              - a_diag_lu(2,4,n)*b(4,1)               &
                              - a_diag_lu(2,5,n)*b(5,1)               &
                              - a_diag_lu(2,6,n)*b(6,1)               &
                                                   )*a_diag_lu(2,2,n)
            b(1,1) = ( b(1,1)                                         &
                              - a_diag_lu(1,2,n)*b(2,1)               &
                              - a_diag_lu(1,3,n)*b(3,1)               &
                              - a_diag_lu(1,4,n)*b(4,1)               &
                              - a_diag_lu(1,5,n)*b(5,1)               &
                              - a_diag_lu(1,6,n)*b(6,1)               &
                                                   )*a_diag_lu(1,1,n)

! Copy b into the dq array

            b(1:nb,1)     =         omega * b(1:nb,1)
            dq(1:nb,n)    =    dq(1:nb,n) + b(1:nb,1)

          end do rhs_solve

! Exchange dq across processors between colors

          if ( color_boundary_end(1) < 0 ) then
            if ( ipass == 2 ) then
              call lmpi_xfer(dq,sr_opt=sr(color))
            endif
          else
            select case(ipass)
            case(1)
              call lmpi_start_xfer(dq,sr_opt=sr(color))
            case(2)
              call lmpi_complete_xfer(dq,sr_opt=sr(color))
            end select
          endif

        end do pass_loop

      end do color_sweep

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_eqns, nbsr, sum_res,          &
                            'point-res ' )
      endif
      if ( allow_exit ) then
        call check_preconditioner( n_eqns, nbsr, sum_res, &
                                   'point:partial-pivoting')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine pp_psolve_6
!End ReplicateBlock 6

end module point_solver_ddq

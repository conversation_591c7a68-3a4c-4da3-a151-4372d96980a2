!================================= LSQ_GRAD_STN ==============================80
!
! Scale, and transform lsq gradients.
!
!=============================================================================80

  pure function lsq_grad_stn( n_grd, t1, t2, t3, scaleir, tr )

    integer,                    intent(in) :: n_grd

    real(dp), dimension(n_grd), intent(in) :: t1, t2, t3
    real(dp),                   intent(in) :: scaleir
    real(dp), dimension(3),     intent(in) :: tr

    real(dp), dimension(n_grd) :: lsq_grad_stn

  continue

    !...Cartesian gradients.
    lsq_grad_stn(:) = ( tr(1)*t1(:) + tr(2)*t2(:) + tr(3)*t3(:) )*scaleir

  end function lsq_grad_stn

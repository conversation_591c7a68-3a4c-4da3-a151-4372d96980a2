#ifndef DDF_WRITER_H
#define DDF_WRITER_H

/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/

/**
 * DDF Binary Writer
 *
 * @short Writer for binary DDF files.
 * @version $Id: ddf_writer.h,v 1.0 2012/09/19 09:02:46 wtjones1 Exp $
 * <AUTHOR>
 */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Write binary DDF file header information accounting for byte ordering.
 *
 * @param name  File name of DDF file
 * @param title  Title for the DDF file (max 80 characters)
 * @param nzone  Number of zones in the DDF file
 * @param solutionTime  Current solution time
 *
 * @return 0 if successful.
 */
extern size_t writeBinaryDDFHeader(const char* name, const char* title, long nzone, double solutionTime);

/**
 * Write binary DDF file Zone data accounting for byte ordering.
 *
 * @param title  Zone title (max 80 characters)
 * @param type  Data type (type=4 for node-centered CFD unstructured)
 * @param npts  Number of points in the zone
 * @param nfunc  Number of function values per point in the zone
 * @param nelem  Number of elements in the zone
 *
 * @return 0 if successful.
 */
extern size_t writeBinaryDDFZoneHeader(const char* title, long type, long npts, long nfunc, long nelem);

/**
 * Write binary DDF file Point data accounting for byte ordering.
 *
 * @param xval  X-Coordinate of the point
 * @param yval  Y-Coordinate of the point
 * @param zval  Z-Coordinate of the point
 * @param pid  Point identifier
 * @param setid  Set identifier
 * @param func  Function values in the zone (nfunc in length)
 *
 * @return 0 if successful.
 */
extern size_t writeBinaryDDFPoint(double xval, double yval, double zval, long pid, long setid, const double* func);

/**
 * Write binary DDF file Element data accounting for byte ordering.
 *
 * @param npe  Number of points defining the element
 * @param id  Element identifier
 * @param pid  Point identifier
 * @param setid  Set identifier
 * @param elem  Element cell-to-node definition (npe in length, values bias 1).
 *
 * @return 0 if successful.
 */
extern size_t writeBinaryDDFElement(long npe, long id, long pid, long setid, const long* elem);

#ifdef __cplusplus
} /* Close extern "C" declaration. */
#endif

#endif /* DDF_WRITER_H */

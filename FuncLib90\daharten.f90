!=============================== DAHARTEN ====================================80
!
! Derivative of the absolute value of an eigenvalue limited function
! Harten entropy limiter function method.
!
!=============================================================================80

  pure function daharten(eigenvalue, eigenvalue_limit)

    use kinddefs,        only : dp

    real(dp), intent(in) :: eigenvalue, eigenvalue_limit
    real(dp)             :: daharten

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: one  = 1.0_dp

    continue

    if ( eigenvalue > zero ) then
      daharten =  one
    else
      daharten = -one
    end if

    if ( abs( eigenvalue ) < eigenvalue_limit ) &
      daharten = eigenvalue / eigenvalue_limit

  end function daharten

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite system_extensions

integer, parameter :: sp = selected_real_kind(P=6)
integer, parameter :: dp = selected_real_kind(P=15)

test floats_not_equal_function
  assert_true(  floats_not_equal( 1.4_dp,                     1.0_dp ) )
  assert_false( floats_not_equal( 1.4_dp,                     1.4_dp ) )
  assert_false( floats_not_equal( 1.4_dp +   spacing(1.0_dp), 1.4_dp ) )
  assert_true(  floats_not_equal( 1.4_dp + 2*spacing(1.0_dp), 1.4_dp ) )
end test

test real_sp_is_a_nan
 real(sp) :: zero = 0.0_sp
 real(sp) :: one  = 1.0_sp
 real(sp) :: nan

 assert_false( is_a_nan(zero) )
 assert_false( is_a_nan( one) )
 assert_false( is_a_nan(-one) )

 nan  = (one / zero) / (one / zero)
 assert_true(  is_a_nan(nan) )
end test

test real_dp_is_a_nan
 real(dp) :: zero = 0.0_dp
 real(dp) :: one  = 1.0_dp
 real(dp) :: nan

 assert_false( is_a_nan(zero) )
 assert_false( is_a_nan( one) )
 assert_false( is_a_nan(-one) )

 nan  = (one / zero) / (one / zero)
 assert_true(  is_a_nan(nan) )
end test

test complex_sp_is_a_nan
 real(sp) :: zero = 0.0_sp
 real(sp) :: one  = 1.0_sp
 real(sp) :: nan
 complex(sp) :: zerozero = cmplx(0.0_sp,0.0_sp,sp)
 complex(sp) :: oneone   = cmplx(1.0_sp,1.0_sp,sp)
 complex(sp) :: nan1
 complex(sp) :: nan2

 assert_false( is_a_nan(zerozero) )
 assert_false( is_a_nan( oneone) )
 assert_false( is_a_nan(-oneone) )

 nan  = (one / zero) / (one / zero)
 nan1  = cmplx(  nan, zero, sp)
 nan2  = cmplx( zero,  nan, sp)
 assert_true( is_a_nan(nan1) )
 assert_true( is_a_nan(nan2) )
end test

test complex_dp_is_a_nan
 real(dp) :: zero = 0.0_dp
 real(dp) :: one  = 1.0_dp
 real(dp) :: nan
 complex(dp) :: zerozero = cmplx(0.0_dp,0.0_dp,dp)
 complex(dp) :: oneone  = cmplx(1.0_dp,1.0_dp,dp)
 complex(dp) :: nan1
 complex(dp) :: nan2

 assert_false( is_a_nan(zerozero) )
 assert_false( is_a_nan( oneone) )
 assert_false( is_a_nan(-oneone) )

 nan  = (one / zero) / (one / zero)
 nan1  = cmplx(  nan, zero, dp)
 nan2  = cmplx( zero,  nan, dp)
 assert_true( is_a_nan(nan1) )
 assert_true( is_a_nan(nan2) )
end test

end test_suite

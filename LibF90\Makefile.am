DIST_SUBDIRS =

include Common.am

LIBCORE_DIR=@libcore_path@
LIBDEFS_DIR=@top_builddir@/libdefs
LIBINIT_DIR=@top_builddir@/libinit
LIBTURB_DIR=@libturb_path@
LIBSMEMRD_DIR=@top_builddir@/libsmemrd
LIBDDFB_DIR=@top_srcdir@/libddfb
FUNCLIB_DIR=@top_builddir@/FuncLib90
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@

noinst_LIBRARIES = libsink.a

libsink_a_LIBADD =
libsink_a_SOURCES = $(libsink_SRCS)
libsink_a_LINK = $(F90LINK)

UNIT_TESTS = \
	adaptation_parameter.fun \
	bc_state.fun \
	component_performance.fun \
	cut_cell.fun \
	cut_sensitivities.fun \
	finite_element_interp.fun \
	flux_bc.fun \
	flux_functions.fun \
	flux_nc.fun \
	flux_nc_opt0.fun \
	forces_hinge.fun \
	grid_helper.fun \
	limiter_functions.fun \
	load_balance.fun \
	nearest_neighbor.fun \
	porous_model.fun \
	porous2.fun \
	sampling_funclib.fun \
	turb_2eqn_routines.fun \
	turb_convection.fun \
	turb_diffusion.fun \
	wall_model.fun \
	wall_model_main.fun

EXTRA_DIST = \
	$(UNIT_TESTS) \
	complex_oldschool.f90 \
	dual_numbering.template \
	LinAlg.rb \
	line_lu_ddq.template \
	line_solver_ddq.template \
	point_lu_ddq.template \
	point_solver_ddq.template

if BUILD_COMPLEX
DIST_SUBDIRS += Complex
endif

#Build Fortran dependencies
%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

module file_utils

  implicit none

  private

  public :: available_unit, available_units
  public :: big_endian_io, little_endian_io
  public :: se_open_big_is_big
  public :: file_exists, file_does_not_exist
  public :: cp, rm, touch

contains

!=============================== AVAILABLE_UNIT ==============================80
!
! Provides an unused file unit number
!
!=============================================================================80

  function available_unit()
    integer :: available_unit
    logical :: unit_exists, in_use
    do available_unit = 100, 100000
      inquire( unit=available_unit, exist=unit_exists, opened=in_use )
      if ( unit_exists .and. .not.in_use ) return
    end do
    write(*,*) 'Error: available_unit: no Fortran units available.'
    available_unit = -1
  end function available_unit

!============================ AVAILABLE_UNITS ================================80
!
! Returns starting unit of a consecutive range of available units
! or -1 if no continguous range is available
!
!  print*, available_units(10)        !=> 100 [w/100-109 unit #s free]
!  print*, available_units(huge(1)+1) !=> -1
!
!=============================================================================80

 function available_units( number )
   integer             :: available_units
   integer, intent(in) :: number
   logical :: existent, in_use
   integer :: unit
   available_units = available_unit()
 1 if ( available_units == -1 .or. available_units > huge(1)-number+1 ) then
     available_units = -1
     return
   end if
   do unit = available_units+number-1, available_units+1, -1
      inquire( unit, exist=existent, opened=in_use )
      if ( .not.existent .or. in_use ) then
        available_units = unit + 1
        goto 1
      end if
   end do

 end function available_units

!================================= BIG_ENDIAN_IO =============================80
!
! Checks for big-endian file i/o
!
!=============================================================================80

  function big_endian_io( opt_unit )

    integer, optional, intent(in) :: opt_unit
    logical                       :: big_endian_io

    integer, parameter :: i1 = selected_int_kind(2) ! one-byte integer
    integer, parameter :: i2 = selected_int_kind(4) ! two-byte integer

    integer(i1) :: byte_one, byte_two
    integer(i2) :: two_byte_int = 1_i2 ! 00000000 00000001 big-endian binary

    integer :: unit

   continue

    if (present(opt_unit)) then
      unit = opt_unit
    else
      unit = available_unit()
    end if

    open(unit,status='scratch',form='unformatted')
      write( unit) two_byte_int
      rewind(unit)
      read(  unit) byte_one, byte_two
    close(unit)

    big_endian_io = ( byte_one == 0 .and. byte_two == 1 )

  end function big_endian_io

  function se_open_big_is_big( opt_unit )

    use system_extensions, only : se_open_big

    integer, optional, intent(in) :: opt_unit
    logical                       :: se_open_big_is_big

    integer, parameter :: i1 = selected_int_kind(2) ! one-byte integer
    integer, parameter :: i2 = selected_int_kind(4) ! two-byte integer

    integer(i1) :: byte_one, byte_two
    integer(i2) :: two_byte_int = 1_i2 ! 00000000 00000001 big-endian binary

    integer :: unit

   continue

    if (present(opt_unit)) then
      unit = opt_unit
    else
      unit = available_unit()
    end if

    call se_open_big(unit,status='scratch',form='unformatted', &
      convert='big_endian')
      write( unit) two_byte_int
      rewind(unit)
      read(  unit) byte_one, byte_two
    close(unit)

    se_open_big_is_big = ( byte_one == 0 .and. byte_two == 1 )

  end function se_open_big_is_big

!=============================== LITTLE_ENDIAN_IO ============================80
!
! Checks for little-endian file i/o
!
!=============================================================================80

  function little_endian_io( opt_unit )

    integer, optional, intent(in) :: opt_unit
    logical                       :: little_endian_io

    integer, parameter :: i1 = selected_int_kind(2) ! one-byte integer
    integer, parameter :: i2 = selected_int_kind(4) ! two-byte integer

    integer(i1) :: byte_one, byte_two
    integer(i2) :: two_byte_int = 1_i2 ! 00000001 00000000 little-endian binary

    integer :: unit

   continue

    if (present(opt_unit)) then
      unit = opt_unit
    else
      unit = available_unit()
    end if

    open(unit,status='scratch',form='unformatted')
      write( unit) two_byte_int
      rewind(unit)
      read(  unit) byte_one, byte_two
    close(unit)

    little_endian_io = ( byte_one == 1 .and. byte_two == 0 )

  end function little_endian_io

!======================================= CP ==================================80
!
! Copies a file
!
!=============================================================================80

  subroutine cp( filename, new_filename )
    use system_extensions, only: se_shell
    character(*), intent(in) :: filename, new_filename
    if (file_exists(filename)) then
      call se_shell( '/bin/cp '//trim(filename)//' '//new_filename )
    end if
  end subroutine cp

!============================ FILE_DOES_NOT_EXIST ============================80
!
! Tests for absence of file
!
!=============================================================================80

  logical function file_does_not_exist( filename )
    character(*), intent(in) :: filename
    file_does_not_exist = .not. file_exists(filename)
  end function file_does_not_exist

!============================= FILE_EXISTS ===================================80
!
! Tests for presence of file
!
!=============================================================================80

  logical function file_exists( filename )
    character(*), intent(in) :: filename
    inquire( file=filename, exist=file_exists )
  end function file_exists

!===================================== RM ====================================80
!
! Removes a file
!
!=============================================================================80

  subroutine rm( filename )
    use system_extensions, only: se_file_unlink
    character(*), intent(in) :: filename
    if (file_exists(filename)) call se_file_unlink(filename)
  end subroutine rm

!================================== TOUCH ====================================80
!
! Creates an empty file
!
!=============================================================================80

  subroutine touch( filename )
    character(*), intent(in)  :: filename
    integer :: lu
    if ( file_does_not_exist(filename) ) then
      lu = available_unit(); open(lu,file=filename); close(lu)
    end if
    ! TODO: to parallel unix, should change modification time of existing file
  end subroutine touch

end module file_utils

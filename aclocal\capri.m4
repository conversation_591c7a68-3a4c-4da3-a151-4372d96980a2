# -*- Autoconf -*-
# autoconf macros for detecting CAPRI installation
#
# Assigned Shell Variables:
#   $with_CAPRI      Path to CAPRI installation (may be 'no')
#   $with_SDK        Path to GEOLAB SDK installation
#
# Assigned Output Variables:
#   @CAPRIheader@    Include path
#   @CAPRIlibrary@   Library path
#   @SDKlibrary@     GEOLAB SDK library path
#   @Xheader@        X11 include path
#   @Xlibrary@       X11 library path
#
# Assigned AM_CONDITIONALS:
#   BUILD_CAPRI_SUPPORT
#   BUILD_SDK_DEPENDENTS
#

AC_DEFUN([CAPRI_LIB_PATH],[

AC_ARG_WITH(CAPRI,
        [[  --with-CAPRI[=ARG]      use CAPRI package [ARG=no]]],
        [with_CAPRI=$withval],      [with_CAPRI="no"])

AC_ARG_WITH(SDK,
        [[  --with-SDK[=ARG]        use GEOLAB SDK package required with CAPRI [ARG=no]]],
        [with_SDK=$withval],        [with_SDK="no"])

AC_CHECKING([CAPRI installation: $with_CAPRI])

if test "$with_CAPRI" != 'no'
then

dnl Check for CAPRI header

  AC_CHECK_FILE([$with_CAPRI/include/capri.h],
                [have_CAPRI='yes'],[have_CAPRI='no'])

  if test "$have_CAPRI" != 'no'
  then
    AC_DEFINE([HAVE_CAPRI],[3],[CAPrI 3.0 API])
    CAPRIheader=$with_CAPRI/include
    CAPRIlibrary=$with_CAPRI/lib
    AM_CONDITIONAL(BUILD_CAPRI_SUPPORT,true)
  else
    AM_CONDITIONAL(BUILD_CAPRI_SUPPORT,false)
    AC_MSG_ERROR([CAPRI requested but not found])
  fi

dnl Check for GEOLAB SDK header

  AC_CHECK_FILE([$with_SDK/include/MeatLib/Common.h],
                [have_SDK='yes'],[have_SDK='no'])

  if test "$have_SDK" != 'no'
  then
    AC_DEFINE([HAVE_SDK],[1],[GEOLAB SDK is available])
    SDKheader=-I$with_SDK/include
    SDKlibrary=-L$with_SDK/lib
    AM_CONDITIONAL(BUILD_SDK_DEPENDENTS,true)
  else
    AC_MSG_ERROR([GEOLAB SDK required but not found])
    AM_CONDITIONAL(BUILD_SDK_DEPENDENTS,false)
    SDKheader=
    SDKlibrary=
  fi

  AC_SUBST([CAPRIheader])
  AC_SUBST([CAPRIlibrary])
  AC_SUBST([SDKlibrary])
  AC_MSG_RESULT([  Found CAPRI installation: $with_CAPRI])

# Finally, find X11 path as CAPRI needs it

  AC_LANG_PUSH(C)
    AC_PATH_X
    if test "$no_x" != 'yes'
    then
      Xheader=$x_includes
      Xlibrary=$x_libraries
    else
      Xheader=
      Xlibrary=
    fi
    AC_SUBST([Xheader])
    AC_SUBST([Xlibrary])
  AC_LANG_POP(C)

else
  SDKheader=
  SDKlibrary=
  AM_CONDITIONAL(BUILD_SDK_DEPENDENTS,false)
  AM_CONDITIONAL(BUILD_CAPRI_SUPPORT,false)
fi

AC_SUBST([SDKheader])
AC_SUBST([SDKlibrary])

])

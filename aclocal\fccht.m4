# -*- Autoconf -*-
#
# Assigned Shell Variables:
#   $enable_fccht      Build with FCCHT wrapper
#
# Assigned AC_DEFINES:
#   HAVE_FCCHT
#
# Assigned AM_CONDITIONALS:
#   BUILD_FCCHT_SUPPORT
#
AC_DEFUN([AX_FCCHT],[

AC_ARG_ENABLE(fccht,
        [[  --enable-fccht          build with FCCHT wrapper [no]]],
        [enable_fccht=$enableval],  [enable_fccht="no"])

if test "$enable_fccht" != 'no'
then
  AC_DEFINE([HAVE_FCCHT],[1],[FCCHT is available])
  AM_CONDITIONAL(BUILD_FCCHT_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_FCCHT_SUPPORT,false)
fi

])


!================================= FLUX_RRHLL ================================80
!
! Calculates the fluxes on the face and computes the contribution to the
! flux balance using a rotated hybrid Roe-HLL flux as described in the paper:
!
! <PERSON><PERSON><PERSON>, H. and Kitamura, K., "Very Simple, Carbuncle-Free,
! Boundary-Layer Preserving, Rotated-Hybrid Riemann Solves", JCP,
! Volume 227, Issue 4, 1 February 2008, Pages 2560-2581
!
! The n1 direction flux uses the HLL flux (two-wave).
! The n2 direction flux uses the Roe flux.
!
! Note that the corresponding Jacobian needs to be implemented, it works OK now
! only with --cd_jac.
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function flux_rrhll(rx1, ry1, rz1, rx2, ry2, rz2,                      &
                           xnorm, ynorm, znorm, area, vol1, vol2,             &
                           gradx1, grady1, gradz1, phi1,                      &
                           gradx2, grady2, gradz2, phi2,                      &
                           face_speed, ql, qr, second, mu)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_2
    use info_depr,       only : adptv_entropy_fix, ivisc
    use fluid,           only : gm1

    real(dp),               intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp),               intent(in) :: vol1, vol2
    real(dp),               intent(in) :: gradx1, grady1, gradz1,              &
                                          gradx2, grady2, gradz2
    real(dp),               intent(in) :: phi1, phi2
    real(dp),               intent(in) :: face_speed, mu
    logical,                intent(in) :: second
    real(dp),                 intent(in) :: xnorm, ynorm, znorm, area
    real(dp), dimension(5),   intent(in) :: ql, qr
    real(dp), dimension(6)               :: flux_rrhll

    real(dp) :: ubar_fsl, ubar_fsr, ubar_fs

    real(dp)                 :: rhol, ul, vl, wl, q2l, pressl, enrgyl, Hl, cl
    real(dp)                 :: rhor, ur, vr, wr, q2r, pressr, enrgyr, Hr, cr
    real(dp), dimension(2)   :: ubarl, ubarr, facspd ! Interface velocities
    real(dp), dimension(3)   :: dq   ! Velocity difference vector
    real(dp)                 :: dqm  ! Magnitude of Velocity difference vector
    real(dp), dimension(3,2) :: rn   ! Normal vectors:n1=rn(:,1) and n2=rn(:,2)
    real(dp), dimension(3,2) :: rn_tmp
    real(dp)                 :: rndn ! Magnitude of rn.
    real(dp), dimension(2)   :: alpha! [dq \cdot n1 and dq \cdot n2]
    real(dp), parameter      :: epsdq = 1.0e-12_dp
    real(dp) :: wat, rho, u, v, w, q2, h, ubar, c, c2 ! Roe-averages
    real(dp), dimension(3)   :: eig,abseig ! Eigenvalues,their absolute values
    real(dp) :: SLm, SRp ! Speeds for the HLL scheme in n1 direction
    real(dp) :: switch, switchv
    real(dp) :: drho, du, dv, dw, dpress, dubar ! Jumps in primitive variables
    real(dp) :: dv1, dv2, dv3, dv4                     ! Wave strengths
    real(dp) :: r21, r22, r23, r24, r31, r32, r33, r34 ! Right-eigenvectors
    real(dp) :: r41, r42, r43, r44, r51, r52, r53, r54 ! Right-eigenvectors
    real(dp), dimension(5) :: fluxl, fluxr, t          ! FL, FR, Dissipation

    integer,  parameter :: behavior = 10
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_2
    real(dp), parameter :: laplcc = my_2

    real(dp) :: ubar_fs1

  continue

!   Get the left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining required left and right state variables

    q2l     = ul*ul + vl*vl + wl*wl
    enrgyl  = pressl/gm1 + my_half*rhol*q2l
    Hl      = (enrgyl + pressl)/rhol
    cl      = sqrt(gm1*(Hl-my_half*q2l))

    q2r     = ur*ur + vr*vr + wr*wr
    enrgyr  = pressr/gm1 + my_half*rhor*q2r
    Hr      = (enrgyr + pressr)/rhor
    cr      = sqrt(gm1*(Hr-my_half*q2r))

!------------------------------------------------------------------------------
!!! 0. Determine n1 and n2 by the velocity-difference vector.

!   Compute the velocity difference vector at the interface.

    dq(1) = ur - ul
    dq(2) = vr - vl
    dq(3) = wr - wl
      dqm = sqrt(dq(1)**2 + dq(2)**2 + dq(3)**2)

!   If dq is large enough, compute n1 = dq / dqm

    if (dqm > epsdq*my_half*(cl+cr)) then

      rn(:,1) = dq(:)/dqm

!     Make sure that n1 is always outward.

      rndn = rn(1,1)*xnorm + rn(2,1)*ynorm + rn(3,1)*znorm
      if (rndn < my_0) rn(:,1) = -rn(:,1)

!     Compute n2 = (n1 x n) x n1

      rn_tmp = rn
      rn(:,2) = cross_product_x(rn_tmp(1,1),rn_tmp(2,1),rn_tmp(3,1),           &
                                xnorm,ynorm,znorm)
      rn_tmp = rn
      rn(:,2) = cross_product_x(rn_tmp(1,2),rn_tmp(2,2),rn_tmp(3,2),           &
                                rn_tmp(1,1),rn_tmp(2,1),rn_tmp(3,1))

      rndn = sqrt(rn(1,2)**2 + rn(2,2)**2 + rn(3,2)**2)
      rn(:,2) = rn(:,2)/( rndn + epsdq )

    else

!   If dq is too small, use the face normal for n2, so that
!   the flux reduces to the standard Roe flux.

      rn(1,2) = xnorm
      rn(2,2) = ynorm
      rn(3,2) = znorm
!      rn(:,1) = my_0

!   Tangent vector

      rn(1,1) = -ynorm + my_0  + znorm
      rn(2,1) =  xnorm - znorm + my_0
      rn(3,1) =  my_0  + ynorm - xnorm
      rndn = sqrt(rn(1,1)**2 + rn(2,1)**2 + rn(3,1)**2)
      rn(:,1) = rn(:,1)/( rndn + epsdq )

    end if

!      At this point, both normal vectors have been completely defined:
!      n1 = rn(:,1) and n2 = rn(:,2).


!------------------------------------------------------------------------------
!!! 1. Compute alpha1=n*n1 and alpha2 = n*n2

    alpha(1) = xnorm*rn(1,1) + ynorm*rn(2,1) + znorm*rn(3,1)
    alpha(2) = xnorm*rn(1,2) + ynorm*rn(2,2) + znorm*rn(3,2)

!------------------------------------------------------------------------------
!!! 2. Compute the left and right physical fluxes.

!   Add normal face speed to the contravariant velocity terms

    ubar_fsl = (xnorm*ul + ynorm*vl + znorm*wl) - face_speed
    ubar_fsr = (xnorm*ur + ynorm*vr + znorm*wr) - face_speed

!   Compute flux using variables from the left side of face

    fluxl(1) = ubar_fsl*rhol
    fluxl(2) = ubar_fsl*rhol*ul + xnorm*pressl
    fluxl(3) = ubar_fsl*rhol*vl + ynorm*pressl
    fluxl(4) = ubar_fsl*rhol*wl + znorm*pressl
    fluxl(5) = ubar_fsl*enrgyl  + (xnorm*ul + ynorm*vl + znorm*wl)*pressl

!   Compute flux using variables from right left side of face

    fluxr(1) = ubar_fsr*rhor
    fluxr(2) = ubar_fsr*rhor*ur + xnorm*pressr
    fluxr(3) = ubar_fsr*rhor*vr + ynorm*pressr
    fluxr(4) = ubar_fsr*rhor*wr + znorm*pressr
    fluxr(5) = ubar_fsr*enrgyr  + (xnorm*ur + ynorm*vr + znorm*wr)*pressr

!------------------------------------------------------------------------------
!!! 3. Compute Roe averages

    rho = sqrt(rhol*rhor)
    wat = rho/(rho + rhor)
    u   = ul*wat + ur*(my_1 - wat)
    v   = vl*wat + vr*(my_1 - wat)
    w   = wl*wat + wr*(my_1 - wat)
    h   = Hl*wat + Hr*(my_1 - wat)
    q2  = u*u + v*v + w*w
    c2  = gm1*(h - my_half*q2)
    c   = sqrt(c2)

!------------------------------------------------------------------------------
!!! 4. Contravariant velocities in the n1 and n2 directions.

!   Compute the face speed in the n1 and n2 directions

    facspd(:) = (rn(1,:)*xnorm + rn(2,:)*ynorm + rn(3,:)*znorm)*face_speed

!   Compute the contravariant velocity in the n1 and n2 directions

     ubarl(:) = rn(1,:)*ul + rn(2,:)*vl + rn(3,:)*wl - facspd(:)
     ubarr(:) = rn(1,:)*ur + rn(2,:)*vr + rn(3,:)*wr - facspd(:)

!------------------------------------------------------------------------------
!!! 5. Compute the speeds associated with HLL (in the n1 direction)

!   Wave speeds for the HLL scheme in n1 direction.

     ubar     = rn(1,1)*u + rn(2,1)*v + rn(3,1)*w
     ubar_fs1 = ubar - facspd(1)
          SLm = min( my_0, ubar_fs1 - c, ubarl(1) - cl )
          SRp = max( my_0, ubar_fs1 + c, ubarr(1) + cr )

!------------------------------------------------------------------------------
!!! 6. Compute the Roe dissipation term based on n2 = rn(:,2).

!   Interface value (for the Roe solver) based on n2 direction.

    ubar        = rn(1,2)*u + rn(2,2)*v + rn(3,2)*w
    ubar_fs     = ubar     - facspd(2)

!   Now compute eigenvalues.

    eig(1) = ubar_fs + c
    eig(2) = ubar_fs - c
    eig(3) = ubar_fs

!   Eigenvalue limiting either adaptive or constant

    switch = my_1

    if (adptv_entropy_fix) then

!     Compute feature detection switch

      switch = iswch_coef(rx1,ry1,rz1,rx2,ry2,rz2,gradx1,grady1,gradz1,        &
                          gradx2,grady2,gradz2,pressl,pressr,phi1,phi2,        &
                          ubarl(2),ubarr(2),q2l,q2r,q2,c2,                     &
                          laplcc,poweri,behavior)

      if (.not. second) switch = my_0

!     Compute the cell face reynolds number to make the extra dissipation vanish
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        switchv  = vswch_coef(wat,rho,q2l,ubarl(2),q2r,ubarr(2),ubar,c,        &
                              vol1,vol2,area,powerv,mu)
        switch = max(switch, switchv)
      end if

    end if

!   Limit the eigenvalues

    abseig = roe_efix(q2l,q2r,ubarl(2),ubarr(2),wat,c,ubar_fs,switch,eig)

!   Combine the wave speeds: Eq.(5.12)

    abseig = alpha(2)*abseig - (   alpha(1)*my_2*SRp*SLm               &
                                 + alpha(2)*(SRp+SLm)*eig  )/(SRp-SLm)

!   Compute primitive variable jumps

    drho   = rhor - rhol
    dpress = pressr - pressl
    du     = ur - ul
    dv     = vr - vl
    dw     = wr - wl
    dubar  = ubarr(2) - ubarl(2)

!   Jumps have units of density

    c2  = c*c
    dv1 = my_half*(dpress + rho*c*dubar)/c2
    dv2 = my_half*(dpress - rho*c*dubar)/c2
    dv3 = rho
    dv4 = (c*c*drho - dpress)/c2

    r21 = u + c*rn(1,2)
    r31 = v + c*rn(2,2)
    r41 = w + c*rn(3,2)
    r51 = h + c*ubar

    r22 = u - c*rn(1,2)
    r32 = v - c*rn(2,2)
    r42 = w - c*rn(3,2)
    r52 = h - c*ubar

    r23 = du - dubar*rn(1,2)
    r33 = dv - dubar*rn(2,2)
    r43 = dw - dubar*rn(3,2)
    r53 = u*du + v*dv + w*dw - ubar*dubar

    r24 = u
    r34 = v
    r44 = w
    r54 = my_half*q2

    t(1)=abseig(1)    *dv1+abseig(2)    *dv2                  +abseig(3)    *dv4
    t(2)=abseig(1)*r21*dv1+abseig(2)*r22*dv2+abseig(3)*r23*dv3+abseig(3)*r24*dv4
    t(3)=abseig(1)*r31*dv1+abseig(2)*r32*dv2+abseig(3)*r33*dv3+abseig(3)*r34*dv4
    t(4)=abseig(1)*r41*dv1+abseig(2)*r42*dv2+abseig(3)*r43*dv3+abseig(3)*r44*dv4
    t(5)=abseig(1)*r51*dv1+abseig(2)*r52*dv2+abseig(3)*r53*dv3+abseig(3)*r54*dv4

!------------------------------------------------------------------------------
!!! 7. Assemble the resutls to form the final form of the numerical flux.
!      Equation (5.11)

   flux_rrhll(1:5) = area*(  (SRp*fluxl(1:5)-SLm*fluxr(1:5))/(SRp-SLm) &
                            - my_half*t(1:5) )

  end function flux_rrhll

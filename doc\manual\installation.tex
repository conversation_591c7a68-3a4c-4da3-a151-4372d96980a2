
% NEXT VERSION: add BAMG and MASSOUD

\section{Installation}\label{c:installation}

\FunThreeD is distributed as gzipped archive of source code. 
The 
\href{http://en.wikipedia.org/wiki/GNU_build_system}{GNU build system}
is used to package and install \FunThreeD.
The required installation steps are detailed in this section.
Due to the large range of capabilities, 
configuring the dependent packages is the most
involved step and is the focus this section.

As was illustrated in the Quick Start section, 
four basic steps are required:
\begin{enumerate}
  \item Extract the source code from the gzipped tarball archive with
        \texttt{tar}
  \item Configure the desired dependencies and compiler options with
        \texttt{configure}
  \item Compile  via \texttt{make}
  \item Install the compiled binaries and supporting scripts via
        \texttt{make install}
\end{enumerate}

If any difficulties arise with the installation process
please, send the entire \texttt{config.log} file produced by 
\texttt{configure} and the full stdout and stderr 
of \texttt{make} to \funsupport.
The user is \emph{strongly} advised against editing the 
\texttt{configure} script or any \file{Makefile} it produces.
We are unable to assist users who have edited these files.

\subsection{Extracting Files}

After downloading the source code as a gzipped tarball, 
the user can unpack it with
\begin{Verbatim}[commandchars=\\\{\}]
  tar zxf fun3d-\version-*.tar.gz
\end{Verbatim}
which will create the directory \texttt{fun3d-\version-*}.
(The \texttt{*} represents a code that the \FunThreeD
uses internally to version the code.) 
If you have do not have a GNU-compatible \cmd{tar},
you may have to insert a separate decompression step, i.e.,
\begin{Verbatim}[commandchars=\\\{\}]
  gzip -d fun3d-\version-*.tar.gz | tar zxf -
\end{Verbatim}

\subsection{Configure Introduction}

The \FunThreeD suite of tools is configured and built via the 
\href{http://en.wikipedia.org/wiki/GNU_build_system}{GNU build system} 
and must be configured first.
Change to this directory, e.g., \texttt{cd fun3d-\version-*}, and execute
\begin{Verbatim}
  ./configure --help
\end{Verbatim}
to see a list of all available compilation options.
When \texttt{configure} is invoked, 
detailed results of all the tests it performs are written to
the file \texttt{config.log}.

Some features of the configure step that have caused problems for
users are:
\begin{itemize}
 \item An incorrect spelling of a \cmd{--enable-*} or \cmd{--with-*}
 option is silently ignored. 
 This will result in the intended option not being included
 in the compiled executable.
 \item Option values containing spaces must be quoted 
to be correctly interpreted by the shell
(i.e., \file{FCFLAGS='-option1 -option2'}).
  \item If the \texttt{configure} command is executed more than once
with different options, 
\texttt{make clean} is required before the \texttt{make} step, 
so that changes to the configuration are correctly reflected 
in the compiled executable.
\end{itemize}

\subsection{Alternative Installation Path}\label{s:install-prefix}

The path to the installation directory is specified by the
\cmd{--prefix=} option.
The default is to install to \path{/usr/local} with
executables placed in \path{/usr/local/bin}.
This default location may not be available if the user
does not have write permission to this directory 
(without root or administrator privileges).

To install to an alternative path (e.g., \path{$HOME/local}), %$ for Emacs
use the \cmd{--prefix=} option to set the installation path
\begin{Verbatim}
  ./configure --prefix=$HOME/local
\end{Verbatim}
%$ for emacs
Finally, to include the \FunThreeD executables in the command search path,
add
\begin{Verbatim}
  setenv PATH $HOME/local/bin:$PATH
\end{Verbatim}
to the \path{~/.cshrc} file or 
the equivalent for your shell.

\subsection{Fortran Compiler Option Tuning (FTune)}

By default, \cmd{configure} will use 
compiler and linker options chosen by the \FunThreeD team.
The process is referred to as ``FTune.''
The users \var{PATH} is searched in a predefined order until the
first \FunThreeD-compatible compiler is found.
When configured with MPI, the build will use \cmd{mpif90} located 
in the \path{bin} directory of the given MPI installation.%
\footnote{To see what the underlying compiler is, use \cmd{mpif90 -show}.}
However, the user can explicitly specify the desired Fortran compiler
via the \var{FC} environment variable.

To directly specify the compiler and linker options,
use the \var{FCFLAGS} and \var{LDFLAGS} environment variables.
The default behavior is to append their values
to the options defined by FTune.
If the \cmd{--disable-ftune} option is given to \cmd{configure},
FTune will be disabled and the values
given by \var{FCFLAGS} and \var{LDFLAGS} will be used explicitly.
For example,
to ensure that the \Intel Fortran compiler \texttt{ifort} is used
with only the \var{-O3}, \var{-ip}, and \var{-lm} options,
\begin{Verbatim}
  ./configure --disable-ftune \
    FC=ifort \
    FCFLAGS='-O3 -ip' \
    LDFLAGS='-lm'
\end{Verbatim}
The order of variables and options are inconsequential,
and single quotation marks (\texttt{'}) are used to protect values
with spaces from the shell.
Some FTune options
may be \emph{unconditionally required} for a given compiler,
as in the case of linking with the math library \var{-lm} above.

\subsection{Complex Variable Version}\label{s:configure-complex}

The \FunThreeD suite can be complied with the real variables
in the code replaced with complex variables by 
a source translation tool.
This permits the computation of forward-mode sensitivities,
see \sectionref{s:complex-run} for details.
To enable, add the \cmd{--enable-complex} configure option to the
\cmd{configure} script.
The complex-valued code can be compiled with \cmd{make complex};
and a \cmd{make install} will place the complex-valued executables in the 
\cmd{bin} installation directory.
Enabling the complex variable version will increase the compile
time.

\subsection{Internal Libraries}\label{s:internals}

\FunThreeD has internal dependencies to libraries that are
distributed with \FunThreeD. 
These libraries are automatically built and linked to \FunThreeD by default.

\subsubsection{KNIFE}\label{s:knife}

The \knife cutcell library provides
cutcell capabilities.
The \cmd{--without-knife} option will disable this library.

\subsubsection{REFINE}\label{s:refine}

The \refine library provides access
mesh adaptation and untangling capabilities.
The \cmd{--without-refine} option will disable this library.

\subsection{External Libraries}\label{s:externals}

\FunThreeD relies on external libraries
to enable some of its advanced applications.
Use \tab{t:configopts} to determine which set of external
libraries are necessary for your applications of interest.
Discussions of each external library are found in the following sections.

It is highly recommended that \FunThreeD is configured to use a parallel 
execution (MPI and ParMETIS) if you plan to perform any advanced calculations.
SUGGAR++ and DiRTlib are only required if overset (chimera) meshes will be used.
The 6-DOF library is only required if six degrees of freedom simulations 
will be performed
(trajectories determined by integrating the equation of motion).
KSOPT, PORT, \SNOPT, \NPSOL, and \DOT are optimization libraries.
At least one of these optimization libraries is required for 
performing design optimization.

\begin{table}
  \centering
  \tabularfont\rowcolors{1}{}{lightgray}
  \caption{Configuration options.}
  \label{t:configopts}
  \newlength{\heightoflongestlabel}
  \settoheight{\heightoflongestlabel}{\rotatebox{60}{Sonic Boom Propagation}}
  \vspace{\heightoflongestlabel}
  \begin{tabular}{r*{12}{|c}} 
    \raisebox{2pt}{Option} &
     \turnbox{60}{Parallel Execution} &
     \turnbox{60}{Overset Motion} &
     \turnbox{60}{Computed Trajectories} &
     \turnbox{60}{Unconstrained Design} &
     \turnbox{60}{Constrained Design} &
     \turnbox{60}{Binary \Tecplot Output} &
     \turnbox{60}{CGNS} &
     \turnbox{60}{Sonic Boom Propagation} \\
    MPI        &x& & & & & & & \\
    ParMETIS   &x& & & & & & & \\
    SUGGAR     & &x& & & & & & \\
    DiRTlib    & &x& & & & & & \\
    6-DOF      & & &x& & & & & \\
    KSOPT      & & & & &x& & & \\
    PORT       & & & &x& & & & \\
    \SNOPT     & & & & &x& & & \\
    \NPSOL     & & & & &x& & & \\
    \DOT       & & & & &x& & & \\
    \Tecplot   & & & & & &x& & \\
    CGNS       & & & & & & &x& \\
    \sboom     & & & & & & & &x\\
  \end{tabular}
\end{table}

\subsubsection{MPI}

MPI provides \FunThreeD's capability to communicate between processors.
Configure with the option

\begin{Verbatim}
  --with-mpi=/path/to/MPI
\end{Verbatim}
where \path{/path/to/MPI} is the directory where MPI is installed.

In addition, \FunThreeD must be executed in an environment that represents
the same MPI installation used for configuration/compilation (e.g. same
mpiexec, mpirun, etc.).  Failure to provide such consistency will result
in undefined behavior and undetermined segmentation faults.

In some cases, MPI may already be installed on the target machine.
If it is not, OpenMPI or MPICH can be used and the option of static MPI
libraries is recommended.  Also, if building OpenMPI or MPICH from source,
it is important to maintain consistency with compilers (both vendor and
version) throughout the build and execution of \FunThreeD and its
dependent libraries.  For example, if OpenMPI is built with gfortran
version 4.4.7, then the execution environment for \FunThreeD must reflect
the same vendor and version, gfortran 4.4.7.  \FunThreeD execution must
also employ the same \path{mpiexec} from the OpenMPI installation used
to build the software.

Some high performance computing environments use a proprietary
MPI implementation that does not provide \texttt{mpif90}.
It that situation, the configure option \texttt{--without-mpif90} 
may be required in combination
with the \texttt{FC} environment variable to explicitly set the compiler.

\paragraph{Verifying the MPI Implementation Functionality}

A simple Fortran program is included in the FUN3D distribution to verify
that the MPI implementation is functional.
This is very helpful for quickly troubleshooting issues with
the MPI implementation.
It is located in \file{utils/MPIcheck}.
From within that directory you should be able to
\begin{Verbatim}
  mpif90 -o mpi_hello_world mpi_hello_world.F90
\end{Verbatim}
and execute on two processors
\begin{Verbatim}
  mpiexec -np 2 ./mpi_hello_world
       0 says, "Hello World!"  5 = 5
       1 says, "Hello World!"  5 = 5
\end{Verbatim}
To verify the Fortran compiler that MPI is built with, try
\begin{Verbatim}
  mpif90 -show
\end{Verbatim}
if the MPI implementation supports it.

\subsubsection{ParMETIS}

Website: \url{http://glaros.dtc.umn.edu/gkhome/metis/parmetis/overview}

The partitioning library ParMETIS is required for parallel execution.
ParMETIS is a parallel graph partitioner that is used to perform domain
decomposition for all parallel \FunThreeD jobs.  It is critical that
\FunThreeD and ParMETIS are compiled with \emph{exactly} the same MPI
installation and compilers.  This includes the C compiler used to compile
MPI, ParMETIS, and FUN3D.

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-parmetis=/path/to/ParMETIS
\end{Verbatim}
where \path{/path/to/ParMETIS} is the directory of the ParMETIS installation.
\FunThreeD expects the \path{/path/to/ParMETIS} directory
to contain the following files in \path{lib} and \path{install}
subdirectories,
\begin{Verbatim}
  /path/to/ParMETIS/lib/libmetis.a
  /path/to/ParMETIS/lib/libparmetis.a
  /path/to/ParMETIS/include/metis.h
  /path/to/ParMETIS/include/parmetis.h
\end{Verbatim}

See the \file{Install.txt} instructions in the 
ParMETIS distribution for build instructions.
\FunThreeD requires both \file{libmetis.a} and \file{libparmetis.a} libraries
and their accompanying header files.
There is an example of commands to build both libraries,
\begin{Verbatim}
  cd parmetis-4.*
   make config prefix=/path/to/ParMETIS
   make install
   cd metis
    make config prefix=/path/to/ParMETIS
    make install
\end{Verbatim}
where \file{/path/to/ParMETIS} matches the \FunThreeD
configure argument.

\subsubsection{SUGGAR++-1.0.10 or Higher}\label{s:suggar}
Website: \url{http://celeritassimtech.com}

SUGGAR++ is used for overset (chimera) applications and assembles 
composite meshes, cuts holes, determines interpolation coefficients, etc.
If configuring with SUGGAR++, \FunThreeD must also be configured with 
\nameref{s:dirtlib}.

SUGGAR++ may be compiled as a stand-alone executable and/or as a library. 
For static overset meshes you will need the stand-alone compilation; 
for moving body simulations you will need to compile both the 
stand-alone executable and the library.
See the documentation that comes with SUGGAR++ for more information on how to 
compile the software. 

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-suggar=/path/to/SUGGAR++
\end{Verbatim}
where \path{/path/to/SUGGAR++} is the directory where SUGGAR++ library archive 
files (\file{.a} files) reside. In this directory, there must be an archive 
file called \file{libsuggar.a}, which is the 
serial compilation of SUGGAR++, and there must also be an archive file 
called \file{libsuggar_mpi.a}, which is the MPI 
compilation of SUGGAR++.

\subsubsection{DiRTlib v1.40 or higher}\label{s:dirtlib}
Website: \url{http://celeritassimtech.com}

The DiRTlib library must be linked to \FunThreeD in order to use the overset
connectivity data computed by \nameref{s:suggar}.
See the documentation that comes with DiRTlib for more information on how to 
compile the software. 

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-dirtlib=/path/to/DiRTlib
\end{Verbatim}
where \path{/path/to/DiRTlib} is the directory where DiRTlib library archive 
files (.a files) reside.
In this directory, there must be an archive file
called \file{libdirt.a}, which is the serial compilation of DiRTlib, and there 
must also be an archive file called 
\file{libdirt_mpich.a}, which is the MPI compilation of DiRTlib.

\subsubsection{6-DOF}\label{s:sixdoflib}
Contact: \href{mailto:<EMAIL>}
{<EMAIL>}

The 6-DOF libraries provide trajectory tracing.
When configuring \FunThreeD, use
\begin{Verbatim}
  --with-sixdof=/path/to/sixdof
\end{Verbatim}
where \path{/path/to/sixdof} is the directory where your 6-DOF installation 
resides.

\subsubsection{KSOPT}

Contact: \href{mailto:<EMAIL>}
{<EMAIL>}

The KSOPT\cite{wrenn-ksopt-nasa-cr} library 
is used for multi-objective and constrained
\FunThreeD-based design optimization.
If you configure \FunThreeD to link to KSOPT, you must use the Fortran 90
implementation of KSOPT with its object files gathered into a
library called \file{libksopt.a}.

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-KSOPT=/path/to/ksopt
\end{Verbatim}
where \path{/path/to/ksopt} is the directory where your KSOPT installation 
resides.

\subsubsection{PORT}
Website: \url{http://www.netlib.org/port}

The PORT library is used for unconstrained \FunThreeD-based design
optimization. 
The Netlib site offers a tarball of the PORT library with a 
\file{Makefile}. 
Download the tarball from Netlib,
but replace the original \file{Makefile} with the file 
included inside the \FunThreeD distribution as \path{Design/PORT.Makefile}.
If you install both the PORT and \NPSOL libraries, 
you may have to comment out low-level BLAS routines 
in one of the two packages because
the linker will report the duplicate versions of these routines.

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-PORT=/path/to/port
\end{Verbatim}
where \path{/path/to/port} is the directory where your PORT installation 
resides.

\subsubsection{\SNOPT}
Website: \url{http://www.sbsi-sol-optimize.com}

The \SNOPT library is used for \FunThreeD-based design optimization.
By default the \SNOPT package builds a shared library.
Either build \SNOPT with the \cmd{--disable-shared} option, 
or add the the \SNOPT install directory to your \file{LD_LIBRARY_PATH}
environment variable to ensure \FunThreeD can find the shared library
at run time.

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-SNOPT=/path/to/snopt
\end{Verbatim}
where \path{/path/to/snopt} is the directory where your \SNOPT installation 
resides.

\subsubsection{\NPSOL}
Website: \url{http://www.sbsi-sol-optimize.com}

The \NPSOL library is used for constrained \FunThreeD-based design optimization.
If you install both the PORT and \NPSOL libraries, 
you may have to comment out low-level BLAS routines 
in one of the two packages because
the linker will report the duplicate versions of these routines.

When configuring \FunThreeD, use
\begin{Verbatim}
  --with-NPSOL=/path/to/npsol
\end{Verbatim}
where \path{/path/to/npsol} is the directory where your \NPSOL installation 
resides.

\subsubsection{\DOT}
Website: \url{http://www.vrand.com/products.html} 

The \DOT library is used for unconstrained or constrained 
\FunThreeD-based design optimization. 
When configuring \FunThreeD, use
\begin{Verbatim}
  --with-DOT=/path/to/dot
\end{Verbatim}
where \path{/path/to/dot} is the directory where your 
\DOT installation resides.

\subsubsection{\Tecplot}

Website: \url{http://www.tecplot.com}

By default,
any \Tecplot output generated from within the flow solver itself
is written as a text file. 
If you have a copy of \Tecplot,
you were provided with a library archive \file{tecio.a}
(or \file{tecio64.a} for 64-bit versions)
that allows for binary output.%
\footnote{The \file{tecio} library that was shipped
  with \textsc{Tecplot360-2008} had a bug that will result in error messages
  when the binary files are written.
  You must get an updated version of the library.
}
You may configure the \FunThreeD suite to use the library via:
\begin{Verbatim}
  --with-tecio=/path/to/tecio
\end{Verbatim}
With this option,
\Tecplot solution data written out from the flow solver will be in binary form.
This results in smaller file sizes and faster importation into \Tecplot.

If you have compiled against the \Tecplot 
\file{tecio} library, you can still request text output
via the \cmd{--ascii_tecplot_output} command line option.

\subsubsection{CGNS}\label{s:install-cgns}

Website: \url{http://www.cgns.org}

The CGNS library is used for working with files written in CGNS format.
CGNS is a convention for writing machine-independent, self-descriptive
data files for CFD and includes implementation software.
\FunThreeD has the capability to translate and write CGNS files.
The translation utilities are only compiled when
CGNS is configured.
Version 2.5 or greater of the CGNS library is required.
To include CGNS, use
\begin{Verbatim}
  --with-CGNS=/path/to/cgns
\end{Verbatim}
where \path{/path/to/cgns} is the directory where the CGNS installation resides.

\subsubsection{\sboom}\label{s:install-sboom}

Contact: \href{mailto:<EMAIL>}
{<EMAIL>}

This package propagates a computed pressure signature to the ground
for sonic boom simulations.
Atmospheric variations are included, and an adjoint version
is available for coupling into design and grid adaptation.
\sboom is distributed as a standalone executable or a static library.
\FunThreeD is not able to interact with the standalone executable;
the static library must be linked.

You may configure the \FunThreeD suite to use the library via:
\begin{Verbatim}
  --with-SBOOM=/path/to/sBOOM
\end{Verbatim}
where \path{/path/to/sBOOM} is the directory where the \sboom installation resides.

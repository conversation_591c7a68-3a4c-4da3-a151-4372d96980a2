module propulsion_types

  use kinddefs, only : dp

  implicit none

  public :: propulsion_system
  public :: engine_type
  public :: station_type

  private

! set up a propulsion system class

  type propulsion_system

    character(len=80) :: name
    integer           :: id
    real(dp)       :: symmetry
    real(dp)       :: area_0 ! free stream
    real(dp)       :: area_1 ! inlet entrace
    real(dp)       :: area_2 ! compressor face
    real(dp)       :: area_3 ! burner entrance
    real(dp)       :: area_4 ! turbine entrance
    real(dp)       :: area_5 ! turbine exit
    real(dp)       :: area_6 ! afterburner
    real(dp)       :: area_7 ! plenum upstream of throat
    real(dp)       :: area_8 ! throat area
    real(dp)       :: area_9 ! exit area
    real(dp)       :: area_max ! maximum area ( used in cti )
    real(dp)       :: diameter_throat
    integer        :: boundary_number

    real(dp)       :: viscosity_freestream
    real(dp)       :: density_freestream_static
    real(dp)       :: u_velocity_freestream
    real(dp)       :: pressure_freestream_static
    real(dp)       :: pressure_freestream_total
    real(dp)       :: temperature_freestream_static
    real(dp)       :: temperature_freestream_total
    real(dp)       :: sonic_speed_freestream

    real(dp)       :: temperature_total_jet
    real(dp)       :: pressure_total_jet

    real(dp)       :: temperature_static_inlet
    real(dp)       :: pressure_static_inlet

    real(dp)       :: nozzle_pressure_ratio
    real(dp)       :: inlet_pressure_ratio
    real(dp)       :: mass_flow_ideal
    real(dp)       :: momentum_actual
    real(dp)       :: pressure_area_actual
    real(dp)       :: mass_flow_actual
    real(dp)       :: mass_flow_actual_dim
    real(dp)       :: flow_area
    real(dp)       :: discharge_coefficient
    real(dp)       :: density_weighted_p_avg

    real(dp)       :: thrust_ideal
    real(dp)       :: thrust_actual
    real(dp)       :: thrust_coefficient

!   average quantities from boundary or sampling integrations
    real(dp)       :: rho_avg
    real(dp)       :: u_avg
    real(dp)       :: p_avg
    real(dp)       :: p_t_avg
    real(dp)       :: t_avg
    real(dp)       :: mach_avg

    real(dp), dimension(3) :: outflow_momentum_flux
    real(dp), dimension(3) :: outflow_delta_pressure
    real(dp), dimension(3) :: inflow_momentum_flux
    real(dp), dimension(3) :: inflow_delta_pressure
    real(dp), dimension(3) :: exit_momentum_flux
    real(dp), dimension(3) :: exit_delta_pressure
    real(dp), dimension(3) :: momentum_flux
    real(dp), dimension(3) :: delta_pressure
    real(dp), dimension(3) :: skin_friction
    real(dp), dimension(3) :: pressure_drag

    real(dp)               :: Reynolds_number_throat
    real(dp)               :: viscosity_total_conditions
    real(dp)               :: theta ! temperature ratio
    real(dp)               :: delta ! pressure ratio

    type(engine_type),  dimension(:), pointer :: engine
    type(engine_type),  dimension(:), pointer :: nozzle
    type(engine_type),  dimension(:), pointer :: inlet
    type(engine_type),  dimension(:), pointer :: bleed

  end type propulsion_system

  type engine_type

    real(dp)       :: pressure_total
    real(dp)       :: temperature_total
    real(dp)       :: pressure_static
    real(dp)       :: temperature_static
    real(dp)       :: npr
    real(dp)       :: ntr
    real(dp)       :: spr
    real(dp)       :: str
    real(dp)       :: massflow_ideal
    real(dp)       :: diameter

    type(station_type), dimension(:), pointer :: station

  end type engine_type

! Set up class for each system station

  type station_type

    integer           :: ibc
    character(len=80) :: kind
    real(dp)          :: area
    real(dp)          :: massflow
    real(dp)          :: momentum
    real(dp)          :: pa
    real(dp)          :: thrust
    real(dp)          :: rhoavg
    real(dp)          :: uavg
    real(dp)          :: pavg
    real(dp)          :: ptavg

  end type station_type

end module propulsion_types

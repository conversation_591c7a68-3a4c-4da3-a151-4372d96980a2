module gcr_defs

  use kinddefs,       only : dp
  use fun3d_maximums, only : ngrid_max, neq_max
  use solution_types, only : linear_projection_type

  implicit none

  private

  public :: rmsmax, rmsloc, rmsloc_rms
  public :: gcr_dq_max, gcr_cr_chk, rt_cr, r_cr
  public :: rt_cr_scalar, r_cr_scalar
  public :: gcr_rms_0, gcr_rms_1, gcr_rms_2, gcr_rms_3, gcr_rms_4
  public :: gcr_cr_scalar
  public :: gcr_projections, field_relaxations, usage, update_within_gcr
  public :: allocate_gcr_defs, allocate_dq_temp
  public :: allocate_content, tc, tn, th
  public :: number_of_lines, entries_per_line
  public :: gcr_m, gcr_t
  public :: dq_temp, previous_walltime, rwu_time, walltime
  public :: nresiduals_m, previous_nresiduals_m
  public :: nresiduals_t, previous_nresiduals_t
  public :: tracked_entries
  public :: sfactor_last_finest_grid, w_search_last_finest_grid

  public :: lm_dq_max, lm_cr, lm_rms_1, lm_rms_2, lm_cr_monitor

  public :: temp_1, temp_2, temp_3, temp_4

  public :: gcr_target_rms, gcr_result, gcr_result_counter

  real(dp) :: gcr_target_rms = -9._dp

  real(dp), dimension(2) :: rt_cr_scalar = -1._dp , r_cr_scalar = -1._dp

  character(len=41), dimension(ngrid_max)   :: gcr_result = ''
  integer,           dimension(ngrid_max,2) :: gcr_result_counter

  real(dp), dimension(:,:), allocatable ::                  &
     gcr_dq_max, gcr_cr_chk, rt_cr, r_cr,                   &
     gcr_rms_0, gcr_rms_1, gcr_rms_2, gcr_rms_3, gcr_rms_4, &
     gcr_cr_scalar,                                         &
     lm_dq_max,  lm_cr, lm_rms_1, lm_rms_2, lm_cr_monitor

  real(dp), dimension(:), allocatable ::  &
    temp_1, temp_2, temp_3, temp_4

  real(dp), dimension(neq_max) :: rmsmax = 0._dp, rmsloc = 0._dp
  real(dp) :: rmsloc_rms = 0._dp

  integer,  dimension(2,ngrid_max) :: gcr_projections    = 0
  real(dp), dimension(2,ngrid_max) :: field_relaxations  = 0._dp

  character(len=6) :: usage = 'GCR:LM'
  logical          :: update_within_gcr = .false.

  type(linear_projection_type), dimension(ngrid_max), target, save :: gcr_t
  type(linear_projection_type), dimension(ngrid_max), target, save :: gcr_m

  !...tecplot content and name.
  real(dp),          dimension(:,:), allocatable :: tc
  character(len=80), dimension(:),   allocatable :: tn

  integer :: nresiduals_m = 0, previous_nresiduals_m = 0
  integer :: nresiduals_t = 0, previous_nresiduals_t = 0

  integer  :: th = 0 !entries stored in tc to be written

  integer, parameter :: number_of_lines = 9+1

  integer, dimension(number_of_lines) :: entries_per_line

  real(dp), dimension(:,:), allocatable :: dq_temp

  real(dp), dimension(2) :: sfactor_last_finest_grid = 1._dp
  real(dp)               :: w_search_last_finest_grid = 1._dp

  integer, parameter :: tracked_entries = 51 !not including 6 (formerly n_q)

  real(dp) :: previous_walltime   = 0._dp, rwu_time = 0._dp, walltime = 0._dp

contains

!================================== ALLOCATE_GCR_DEFS ========================80
!
! Allocate data.
!
!=============================================================================80

  subroutine allocate_gcr_defs( n_q )

    integer, intent(in) :: n_q

  continue

    allocate(   gcr_dq_max(n_q,ngrid_max))
    allocate(    lm_dq_max(n_q,ngrid_max))
    allocate(        lm_cr(n_q,ngrid_max))
    allocate(lm_cr_monitor(n_q,ngrid_max))
    allocate(   gcr_cr_chk(n_q,ngrid_max))
    allocate(        rt_cr(n_q,ngrid_max))
    allocate(         r_cr(n_q,ngrid_max))
    allocate(    gcr_rms_0(n_q,ngrid_max))
    allocate(    gcr_rms_1(n_q,ngrid_max))
    allocate(    gcr_rms_2(n_q,ngrid_max))
    allocate(    gcr_rms_3(n_q,ngrid_max))
    allocate(    gcr_rms_4(n_q,ngrid_max))
    allocate(gcr_cr_scalar(n_q,ngrid_max))
    allocate(     lm_rms_1(n_q,ngrid_max))
    allocate(     lm_rms_2(n_q,ngrid_max))

         lm_dq_max(:,:) = -9._dp
        gcr_dq_max(:,:) = -9._dp
             lm_cr(:,:) = -9._dp
     lm_cr_monitor(:,:) = -9._dp
        gcr_cr_chk(:,:) = -9._dp
             rt_cr(:,:) = -9._dp
              r_cr(:,:) = -9._dp
         gcr_rms_0(:,:) =  0._dp
         gcr_rms_1(:,:) =  0._dp
         gcr_rms_2(:,:) =  0._dp
         gcr_rms_3(:,:) =  0._dp
         gcr_rms_4(:,:) =  0._dp
     gcr_cr_scalar(:,:) = -9._dp
          lm_rms_1(:,:) = -9._dp
          lm_rms_2(:,:) = -9._dp

    allocate(temp_1(n_q))
    allocate(temp_2(n_q))
    allocate(temp_3(n_q))
    allocate(temp_4(n_q))

    temp_1(:) = -9._dp
    temp_2(:) = -9._dp
    temp_3(:) = -9._dp
    temp_4(:) = -9._dp

  end subroutine allocate_gcr_defs

!================================== ALLOCATE_DQ_TEMP =====================80
!
! Allocate data.
!
!=============================================================================80

  subroutine allocate_dq_temp( n_q, dof01 )

    integer, intent(in) :: n_q, dof01

  continue

    if ( .not.allocated(dq_temp) ) then

      allocate(dq_temp(n_q,dof01))

    elseif ( dof01 > size(dq_temp,2) ) then

      deallocate(dq_temp)
      allocate(dq_temp(n_q,dof01))

    endif

  end subroutine allocate_dq_temp

!=============================== ALLOCATE_CONTENT ============================80
!
! Allocate array for plotting.
!
!=============================================================================80
  subroutine allocate_content( previous_fmg_cycles )

    use info_depr,         only : ngrid, ncyc

    use multigrid_defs, only : fmg_cycles_request, fmg_levels_request

    integer,         intent(in)  :: previous_fmg_cycles

    integer :: ncyc_total, size_p, fl

  continue

    if ( ngrid == 1 ) then
      ncyc_total = ncyc
    else
      ncyc_total = 0
      do fl = fmg_levels_request, 1, -1
        ncyc_total = ncyc_total + fmg_cycles_request(fl)
      enddo
    endif

    ncyc_total = ncyc_total + previous_fmg_cycles

    size_p = tracked_entries + 6 !+ n_q
    allocate( tc(size_p, ncyc_total) )
    allocate( tn(size_p) )

    write(*,*) 'allocate_content...size_p=',size_p,' ncyc_total=',ncyc_total

  end subroutine allocate_content

end module gcr_defs

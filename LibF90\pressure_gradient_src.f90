module pressure_gradient_src

  use kinddefs,        only : dp

  implicit none

  private

  public :: pressure_gradient_rhs

contains

!================================ PRESSURE_GRADIENT_RHS ======================80
!
! Subroutine pressure_gradient_rhs  computes the source term contribution
! to the right hand ! side due to a global pressure gradient
!
! Note: this routine assumes primitive varables as input
!
!=============================================================================80

  subroutine pressure_gradient_rhs ( eqn_set, vol, nnodes0, nnodes01, qnode   &
                                   , res, n_tot, njac, pressure_gradient )

    use solution_types, only : compressible, incompressible

    integer, intent(in) :: eqn_set
    integer, intent(in) :: nnodes0, nnodes01, n_tot, njac
    real(dp),  dimension(n_tot,nnodes01),     intent(in)   :: qnode
    real(dp),  dimension(njac,nnodes01),      intent(inout):: res
    real(dp),  dimension(nnodes01),           intent(in)   :: vol
    real(dp),  dimension(3),                  intent(in)   :: pressure_gradient

    integer     :: n
    real(dp)    :: rho

    continue

    select case (eqn_set)

      case (compressible)

        do n = 1, nnodes0

          rho = qnode(1,n)

          res(2,n) = res(2,n) - rho*vol(n)*pressure_gradient(1)
          res(3,n) = res(3,n) - rho*vol(n)*pressure_gradient(2)
          res(4,n) = res(4,n) - rho*vol(n)*pressure_gradient(3)

        end do

      case (incompressible)

        do n = 1, nnodes0

          res(2,n) = res(2,n) - vol(n)*pressure_gradient(1)
          res(3,n) = res(3,n) - vol(n)*pressure_gradient(2)
          res(4,n) = res(4,n) - vol(n)*pressure_gradient(3)

        end do

      case default

      end select

  end subroutine pressure_gradient_rhs

end module pressure_gradient_src

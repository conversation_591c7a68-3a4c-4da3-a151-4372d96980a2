module lsq_cc

  use kinddefs,        only : dp
  use twod_util,       only : q_2d
  use generic_gas_map, only : n_momx, n_momy, n_momz
  use rtiming,         only : rtime
  use lsq_constants,   only : lsqn_max
  use lsq_util,        only : lsq_grad_st, lsq_lu_finalize,                    &
                              lsq_coords_info, lsq_scoords_info, clsq_cc_info
  use debug_defs,      only : debug_q, debug_q_proc, debug_q_loc
  use lmpi,            only : lmpi_id, lmpi_master, lmpi_conditional_stop
  use cell_agglom_defs,only : agglomerate_inviscid_order
  use cc_defs,         only : clsq_bc
  use lsq_types,       only : lsq_ref_type

  implicit none

  private

  public :: set_up_lsq_cc
  public :: clsq_lu_cc, grad_variable_cc, clsq_grad_driver_cc
  public :: update_gradients_cc
  public :: clsq_grad_cc

  logical, parameter :: timing_lsq = .false.

contains

!=================================== UPDATE_GRADIENTS_CC =====================80
!
! Update cell-centered primitive gradients from conserved variables.
!
!=============================================================================80

  subroutine update_gradients_cc( grid, soln, crow )

    use info_depr,         only : skeleton
    use cc_defs,           only : ccrecon, clsq_bc
    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use comprow_types,     only : crow_flow
    use thermo,            only : etop, ptoe, q_type, primitive_q_type
    use node_avg_cc,       only : qt_avg_to_nodes
    use bc_cache_cc,       only : qt_lsq

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    character(len=80) :: variable, gradient

  continue

    if ( soln%viscous_method == 4 ) then
      call qt_avg_to_nodes( grid, soln )
    endif
    if ( clsq_bc .or. soln%viscous_method == 3 ) then
      call qt_lsq( soln%eqn_set, soln%n_q, soln%q_dof, grid%bcc )
    endif

    if ( soln%eqn_set /= 1 ) then
      call etop(grid%ncell01, soln%q_dof, soln%n_tot, soln%eqn_set)
    endif

    variable = 'inviscid'
    gradient = 'least-squares'   ! default
    if ( ccrecon == 2 ) gradient = 'green-gauss'

    call grad_variable_cc( grid, soln, crow, variable, gradient )

    if ( soln%eqn_set /= 1 ) then
      call ptoe(grid%ncell01, soln%q_dof, soln%n_tot, soln%eqn_set)
      !   to fake out accounting so ptoe can be called twice in a row
      q_type = primitive_q_type
      call ptoe(size(soln%qbc_na,2),soln%qbc_na,soln%n_tot, soln%eqn_set)
    endif

    if ( skeleton > 0 ) write(*,"(1x,a,i5,a,i20)") &
    'Updated cell-centered gradients...level=',grid%igrid,' dof=',grid%ncellg

  end subroutine update_gradients_cc

!================================== CLSQ_LU  =================================80
!
! Gets the weights for calculating gradients using mapped least squares.
!
!=============================================================================80
  subroutine clsq_lu( ncell0, xc, yc, zc,                                      &
                      ia, ja, ia_ns,                                           &
                      clsq_ia, clsq_ja,                                        &
                      cgamma, slen, slenxn, slenyn, slenzn,                    &
                      symmetry_x_cells, symmetry_x_coord,                      &
                      symmetry_y_cells, symmetry_y_coord,                      &
                      symmetry_z_cells, symmetry_z_coord,                      &
                      clsq, bcc, cl2g )

    use info_depr,       only : skeleton
    use bc_types,        only : bcc_type
    use lmpi,            only : lmpi_reduce
    use fun3d_constants, only : my_1
    use lsq_constants,   only : mlsq, tf, cg_tol

    integer, intent(in) :: ncell0

    integer, dimension(:), intent(in) :: ia, ja, ia_ns
    integer, dimension(:), intent(in) :: clsq_ia, clsq_ja

    real(dp), dimension(:), intent(in) :: xc, yc, zc
    real(dp), dimension(:), intent(in) :: cgamma, slen
    real(dp), dimension(:), intent(in) :: slenxn, slenyn, slenzn

    integer, dimension(:), intent(in) :: symmetry_x_cells
    integer, dimension(:), intent(in) :: symmetry_y_cells
    integer, dimension(:), intent(in) :: symmetry_z_cells
    integer, dimension(:), intent(in) :: cl2g

    real(dp), dimension(:), intent(in) :: symmetry_x_coord
    real(dp), dimension(:), intent(in) :: symmetry_y_coord
    real(dp), dimension(:), intent(in) :: symmetry_z_coord

    real(dp), dimension(3,3,ncell0), intent(out) :: clsq

    type(bcc_type), intent(in) :: bcc

    integer :: ii, jj, cell, cella, ix, iy, iz
    integer :: fb, clsq_ni, clsq_nb, clsq_n, ierr, detfs

    real(dp) :: dx, dy, dz, det_tolerance

    real(dp), dimension(4,lsqn_max) :: lc

    real(dp), dimension(4) :: sc, lc_max

    real(dp), dimension(3,3) :: a

    logical :: debugging_enabled, debugging, detf

    type(lsq_ref_type) :: lsq_mrefs

  continue

    detfs = 0 ; ierr = 0

    det_tolerance = epsilon( 1.0_dp )

    debugging_enabled = .false.
    if ( debug_q ) then
       if ( lmpi_master ) then
        write(*,*)
        write(*,*) 'Cell-based least-square (CLSQ) LU via clsq_lu.'
        write(*,*) '........clsq_bc=',clsq_bc
        write(*,*) '........debug_q=',debug_q
        write(*,*) '...debug_q_proc=',debug_q_proc
        write(*,*) '....debug_q_loc=',debug_q_loc
        write(*,*) '.debug_q_global=',cl2g(debug_q_loc)
      endif
      if ( lmpi_id == debug_q_proc ) then
        debugging_enabled = .true.
      endif
    elseif( skeleton > 0 )then
      write(*,*)
      write(*,"(1x,a,L1)")                                             &
      'Cell-based least-square (CLSQ) LU via clsq_lu...clsq_bc=',clsq_bc
    endif

    ix = 1 ; iy = 1 ; iz = 1
    do cell = 1,ncell0

      a(:,:) = 0._dp

      debugging = .false.
      if ( debugging_enabled .and. cell == debug_q_loc ) debugging = .true.

      clsq_ni =       ia_ns(  cell)-         ia(cell) &
                  + clsq_ia(cell+1)-    clsq_ia(cell)
      clsq_nb = bcc%clsq_ib(cell+1)-bcc%clsq_ib(cell)
      clsq_n  = clsq_ni + clsq_nb  !interior + boundary

      lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                      &
                               xc(cell), yc(cell), zc(cell),            &
                               my_1, cgamma(cell), slen(cell),          &
                               slenxn(cell), slenyn(cell), slenzn(cell) )

      if ( debugging ) then
        call clsq_cc_info( lsq_mrefs, cell, clsq_ni, clsq_nb, xc, yc, zc, &
                           ia, ja, ia_ns, clsq_ia, clsq_ja,               &
                           cgamma, slen, slenxn, slenyn, slenzn, bcc, cl2g )
      endif

      jj = 0

      do ii=ia(cell),ia_ns(cell)-1

        jj = jj + 1
        cella = ja(ii)

        lc(:,jj) = lsq_coords( lsq_mrefs, tf,                                &
                                xc(cella), yc(cella), zc(cella), slen(cella) )
      enddo

      do ii=clsq_ia(cell),clsq_ia(cell+1)-1

        jj = jj + 1
        cella = clsq_ja(ii)

        lc(:,jj) = lsq_coords( lsq_mrefs, tf,                                &
                                xc(cella), yc(cella), zc(cella), slen(cella) )
      enddo

      do ii=bcc%clsq_ib(cell),bcc%clsq_ib(cell+1)-1

        jj = jj + 1
        fb = bcc%clsq_jb(ii)

        lc(:,jj) = lsq_coords( lsq_mrefs, tf,                                &
                                bcc%xface(fb), bcc%yface(fb), bcc%zface(fb), &
                                bcc%slenface(fb) )
      enddo

      lc_max = lsq_lc_max( jj, lc )

      if ( debugging ) then
        call lsq_coords_info ( lsq_mrefs, jj, lc, lc_max )
        call lsq_scoords_info( jj, lc, lc_max )
      endif

      do ii=1,jj

        sc(:)  = lsq_scoords( lc(:,ii), lc_max )

        a(:,:) = a(:,:) + wrsum( sc )

      enddo

      !...add in an imaginary ghost point for 2D (no dq contribution)
      if ( q_2d ) a(3,3) = a(3,3) + 1.0_dp

      !...account for symmetry...ordering is essential here.

      if ( symmetry_x_cells(ix) == cell ) then
        dx = 2._dp*( symmetry_x_coord(ix) - xc(cell) )
        sc(:) = lsq_scoords_sx( lsq_mrefs, tf,                   &
                                 lc_max, dx, xc(cell), yc(cell), &
                                 zc(cell), slen(cell) )
        a(:,:) = a(:,:) + wrsum( sc )
        ix = ix + 1
      endif

      if ( symmetry_y_cells(iy) == cell ) then
        dy = 2._dp*( symmetry_y_coord(iy) - yc(cell) )
        sc(:) = lsq_scoords_sy( lsq_mrefs, tf,                   &
                                 lc_max, dy, xc(cell), yc(cell), &
                                 zc(cell), slen(cell) )
        a(:,:) = a(:,:) + wrsum( sc )
        iy = iy + 1
      endif

      if ( symmetry_z_cells(iz) == cell ) then
        dz = 2._dp*( symmetry_z_coord(iz) - zc(cell) )
        sc(:) = lsq_scoords_sz( lsq_mrefs, tf,                   &
                                 lc_max, dz, xc(cell), yc(cell), &
                                 zc(cell), slen(cell) )
        a(:,:) = a(:,:) + wrsum( sc )
        iz = iz + 1
      endif

      ierr = ierr + min( 1, abs( jj - clsq_n ) )
      if ( ierr /= 0 ) then
        write(*,"(1x,a,2i10,4i5)")                     &
        ' Stopping...clsq_lu...cell,ncell0,jj,clsq_n=',&
        jj,clsq_n,clsq_ni,clsq_nb
        cycle
      endif

      if ( debugging ) then
        write(*,*) ' Rows of A matrix below:'
        write(*,"(1x,a,    3e20.10)") 'row1:',a(1,1:3)
        write(*,"(1x,a,20x,2e20.10)") 'row2:',a(2,2:3)
        write(*,"(1x,a,40x,1e20.10)") 'row3:',a(3,3:3)
      endif

      call lsq_lu_finalize( det_tolerance, a, detf )
      if ( detf ) detfs = detfs + 1

      clsq(:,:,cell) = a(:,:)

      if ( debugging ) then
        write(*,*) ' Rows of LU matrix below:'
        write(*,"(1x,a,    3e20.10)") 'row1:',a(1,1:3)
        write(*,"(1x,a,20x,2e20.10)") 'row2:',a(2,2:3)
        write(*,"(1x,a,40x,1e20.10)") 'row3:',a(3,3:3)
      endif

    enddo

    call lmpi_conditional_stop(ierr,'accounting error in clsq_lu')

    ierr = detfs
    ii   = detfs ; call lmpi_reduce(ii,detfs)
    if ( detfs > 0 ) then
      write(*,*) 'Determinant below tolerance in clsq_lu...stopping.'
      write(*,*) '...det_tolerance =',det_tolerance
      write(*,*) '...occurrences=',detfs
    endif

    call lmpi_conditional_stop(ierr,'determinants below tolerance in clsq_lu')

  end subroutine clsq_lu

!================================== CLSQ_GRAD_CC  ============================80
!
! Gradients using mapped least squares.
!
!=============================================================================80
  subroutine clsq_grad_cc( cell_start, cell_end, eqn_set, n_tot, n_grd,        &
                        q_dof, gradx, grady, gradz,                            &
                        ncell0, ncell01, xc, yc, zc,                           &
                        ia, ja, ia_ns,                                         &
                        clsq_ia, clsq_ja,                                      &
                        cgamma, slen, slenxn, slenyn, slenzn,                  &
                        symmetry_x_cells, symmetry_x_coord,                    &
                        symmetry_y_cells, symmetry_y_coord,                    &
                        symmetry_z_cells, symmetry_z_coord,                    &
                        clsq, bcc, ierr, first_time_through )

    use bc_types,        only : bcc_type
    use fun3d_constants, only : my_1
    use lsq_constants,   only : mlsq, tf, cg_tol

    integer, intent(in) :: cell_start, cell_end
    integer, intent(in) :: eqn_set, n_tot, n_grd, ncell0, ncell01

    integer, dimension(:),   intent(in) :: ia, ja, ia_ns
    integer, dimension(:),   intent(in) :: clsq_ia, clsq_ja

    real(dp), dimension(:), intent(in) :: xc, yc, zc
    real(dp), dimension(:), intent(in) :: cgamma, slen
    real(dp), dimension(:), intent(in) :: slenxn, slenyn, slenzn

    integer, dimension(:), intent(in) :: symmetry_x_cells
    integer, dimension(:), intent(in) :: symmetry_y_cells
    integer, dimension(:), intent(in) :: symmetry_z_cells

    real(dp), dimension(:), intent(in) :: symmetry_x_coord
    real(dp), dimension(:), intent(in) :: symmetry_y_coord
    real(dp), dimension(:), intent(in) :: symmetry_z_coord

    real(dp), dimension(3,3,ncell0), intent(in) :: clsq

    real(dp), dimension(n_tot,ncell01), intent(in)  :: q_dof
    real(dp), dimension(n_grd,ncell01), intent(out) :: gradx, grady, gradz

    type(bcc_type), intent(in) :: bcc

    integer, intent(inout) :: ierr
    logical, intent(in)    :: first_time_through

    integer :: ii, jj, cell, cella, ix, iy, iz
    integer :: fb, clsq_ni, clsq_nb, clsq_n

    real(dp) :: dx, dy, dz, dqx, dqy, dqz

    integer, dimension(lsqn_max) :: list

    real(dp), dimension(n_grd) :: dq, tgradx, tgrady, tgradz, f1, f2, f3, qp

    real(dp), dimension(3) :: dclsq, dgrad

    real(dp), dimension(4, lsqn_max) :: lc

    real(dp), dimension(4) :: sc, lc_max

    logical :: debugging

    type(lsq_ref_type) :: lsq_mrefs

  continue

    ix = 1 ; iy = 1 ; iz = 1
    do cell = cell_start, cell_end

      debugging = .true.
      if ( .not. first_time_through ) debugging = .false.
      if ( .not. debug_q ) debugging = .false.
      if ( lmpi_id /= debug_q_proc ) debugging = .false.
      if ( cell /= debug_q_loc ) debugging = .false.

      if ( debugging ) then
        write(*,*)
        write(*,*) 'Cell-based least-square (CLSQ) gradients via clsq_grad_cc.'
        write(*,*) '........clsq_bc=',clsq_bc
        write(*,*) '........debug_q=',debug_q
        write(*,*) '...debug_q_proc=',debug_q_proc
        write(*,*) '....debug_q_loc=',debug_q_loc
      endif

      clsq_ni =       ia_ns(  cell)-         ia(cell) &
                  + clsq_ia(cell+1)-    clsq_ia(cell)
      clsq_nb = bcc%clsq_ib(cell+1)-bcc%clsq_ib(cell)
      clsq_n  = clsq_ni + clsq_nb  !interior + boundary

      lsq_mrefs = lsq_map_ref( q_2d, mlsq, cg_tol,                      &
                               xc(cell), yc(cell), zc(cell),            &
                               my_1, cgamma(cell), slen(cell),          &
                               slenxn(cell), slenyn(cell), slenzn(cell) )

      f1(:) = 0._dp
      f2(:) = 0._dp
      f3(:) = 0._dp

      jj = 0
      do ii=ia(cell),ia_ns(cell)-1

        jj = jj + 1
        cella = ja(ii)
        list(jj) = cella

        lc(:,jj) = lsq_coords( lsq_mrefs, tf,                                &
                                xc(cella), yc(cella), zc(cella), slen(cella) )

      enddo

      do ii=clsq_ia(cell),clsq_ia(cell+1)-1

        jj = jj + 1
        cella = clsq_ja(ii)
        list(jj) = cella

        lc(:,jj) = lsq_coords( lsq_mrefs, tf,                                &
                                xc(cella), yc(cella), zc(cella), slen(cella) )

      enddo

      do ii=bcc%clsq_ib(cell),bcc%clsq_ib(cell+1)-1

        jj = jj + 1
        fb = bcc%clsq_jb(ii)

        list(jj) = fb
        lc(:,jj) = lsq_coords( lsq_mrefs, tf,                                &
                                bcc%xface(fb), bcc%yface(fb), bcc%zface(fb), &
                                bcc%slenface(fb) )

      enddo

      lc_max = lsq_lc_max( jj, lc )

      if ( debugging ) then
        write(*,*)
        write(*,"(1x,a,3i10)") ' cell=',cell
        write(*,"(1x,a,7f20.10)") ' q_dof(1:n_grd)=',&
                                    q_dof(1:n_grd,cell)
        write(*,*)
        write(*,*) ' Rows of LU matrix below:'
        write(*,"(1x,a,    3e20.10)") 'row1:',clsq(1,1:3,cell)
        write(*,"(1x,a,20x,2e20.10)") 'row2:',clsq(2,2:3,cell)
        write(*,"(1x,a,40x,1e20.10)") 'row3:',clsq(3,3:3,cell)
      endif

      do ii=1,jj

        sc(:) = lsq_scoords( lc(:,ii), lc_max )

        if ( ii <= clsq_ni ) then
          dq(1:n_grd) = q_dof(1:n_grd,list(ii)) &
                      - q_dof(1:n_grd,cell)
        else
          qp(1:n_grd) = qp_from_qt( eqn_set, n_grd, bcc%qt(1:n_grd,list(ii)) )
          dq(1:n_grd) = qp(1:n_grd) -  q_dof(1:n_grd,cell)
        endif

        dclsq(:) = sc(4)*weights( sc(1), sc(2), sc(3), clsq(1,1,cell) )

        f1(:) = f1(:) + dclsq(1)*dq(:)
        f2(:) = f2(:) + dclsq(2)*dq(:)
        f3(:) = f3(:) + dclsq(3)*dq(:)

        if ( debugging ) then
        write(*,*)
        write(*,"(1x,a,3i10)") '  ii,list(ii)=',        &
                                  ii,list(ii)
        if ( ii <= clsq_ni ) then
          write(*,"(1x,a,7f20.10)") '  q_dof(1:n_grd)=',&
                                       q_dof(1:n_grd,list(ii))
        else
          write(*,"(1x,a,7f20.10)") ' bcc%qt(1:n_grd)=',&
                                      bcc%qt(1:n_grd,list(ii))
        endif
        write(*,"(1x,a,4f20.10)") '     sc(:)=',sc(:)
        write(*,"(1x,a,7f20.10)") '  dclsq(:)=',dclsq(:)
        write(*,"(1x,a,7f20.10)") '     dq(:)=',dq(:)
        write(*,*)
        write(*,*) ' Accumulated gradients in xie,eta,zie:'
        write(*,"(1x,a,7f20.10)") '     f1(:)=',f1(:)
        write(*,"(1x,a,7f20.10)") '     f2(:)=',f2(:)
        write(*,"(1x,a,7f20.10)") '     f3(:)=',f3(:)
        endif
      enddo

      call lsq_grad_st( lsq_mrefs, f1, f2, f3, tgradx, tgrady, tgradz, lc_max )

      if ( debugging ) then
        write(*,*)
        write(*,*) 'Cartesian gradientsbefore symmetry):'
        write(*,"(1x,a,7f20.10)") ' gradx(:)=',tgradx(:)
        write(*,"(1x,a,7f20.10)") ' grady(:)=',tgrady(:)
        write(*,"(1x,a,7f20.10)") ' gradz(:)=',tgradz(:)
      endif

      !...account for symmetry...ordering is essential here.

      if ( symmetry_x_cells(ix) == cell ) then
        dx = 2._dp*( symmetry_x_coord(ix) - xc(cell) )
        sc(:) = lsq_scoords_sx( lsq_mrefs, tf,                   &
                                 lc_max, dx, xc(cell), yc(cell), &
                                 zc(cell), slen(cell) )

        dqx = -2._dp*q_dof(n_momx,cell)

        dclsq(:) = sc(4)*weights( sc(1), sc(2), sc(3), clsq(1,1,cell) )

        dgrad = lsq_gradc( lsq_mrefs, dclsq, lc_max )

        tgradx(n_momx) = tgradx(n_momx) + dgrad(1)*dqx
        tgrady(n_momx) = tgrady(n_momx) + dgrad(2)*dqx
        tgradz(n_momx) = tgradz(n_momx) + dgrad(3)*dqx

        ix = ix + 1
      endif

      if ( symmetry_y_cells(iy) == cell ) then

        dy = 2._dp*( symmetry_y_coord(iy) - yc(cell) )
        sc(:) = lsq_scoords_sy( lsq_mrefs, tf,                   &
                                 lc_max, dy, xc(cell), yc(cell), &
                                 zc(cell), slen(cell) )

        dqy = -2._dp*q_dof(n_momy,cell)

        dclsq(:) = sc(4)*weights( sc(1), sc(2), sc(3), clsq(1,1,cell) )

        dgrad = lsq_gradc( lsq_mrefs, dclsq, lc_max )

        tgradx(n_momy) = tgradx(n_momy) + dgrad(1)*dqy
        tgrady(n_momy) = tgrady(n_momy) + dgrad(2)*dqy
        tgradz(n_momy) = tgradz(n_momy) + dgrad(3)*dqy

        iy = iy + 1
      endif

      if ( symmetry_z_cells(iz) == cell ) then
        dz = 2._dp*( symmetry_z_coord(iz) - zc(cell) )
        sc(:) = lsq_scoords_sz( lsq_mrefs, tf,                   &
                                 lc_max, dz, xc(cell), yc(cell), &
                                 zc(cell), slen(cell) )

        dqz = -2._dp*q_dof(n_momz,cell)

        dclsq(:) = sc(4)*weights( sc(1), sc(2), sc(3), clsq(1,1,cell) )

        dgrad = lsq_gradc( lsq_mrefs, dclsq, lc_max )

        tgradx(n_momz) = tgradx(n_momz) + dgrad(1)*dqz
        tgrady(n_momz) = tgrady(n_momz) + dgrad(2)*dqz
        tgradz(n_momz) = tgradz(n_momz) + dgrad(3)*dqz

        iz = iz + 1
      endif

      if ( debugging ) then
        write(*,*)
        write(*,*) 'Cartesian gradients:'
        write(*,"(1x,a,7f20.10)") ' gradx(:)=',tgradx(:)
        write(*,"(1x,a,7f20.10)") ' grady(:)=',tgrady(:)
        write(*,"(1x,a,7f20.10)") ' gradz(:)=',tgradz(:)
      endif

      ierr = ierr + min( 1, abs( jj - clsq_n ) )
      if ( ierr /= 0 ) then
        write(*,"(1x,3(a,2i10))")                               &
        ' Stopping..clsq_grad_cc......cell,ncell0=',cell,ncell0,&
        ' jj,clsq_n=',jj,clsq_n,                                &
        ' clsq_ni,clsq_nb=',clsq_ni,clsq_nb
        cycle
      endif

      gradx(:,cell) = tgradx(:)
      grady(:,cell) = tgrady(:)
      gradz(:,cell) = tgradz(:)

    enddo

  end subroutine clsq_grad_cc

!================================== SET_UP_LSQ_CC ============================80
!
! Least square set up for the cell-centered option
!
!=============================================================================80
  subroutine set_up_lsq_cc( viscous_method, grid, crow )

    use cc_defs,               only : ccrecon
    use flsq_lu,               only : set_flsq_lu
    use comprow_types,         only : crow_flow
    use grid_types,            only : grid_type
    use info_depr,             only : ivisc

    integer, intent(in) :: viscous_method

    type(grid_type), intent(inout) :: grid
    type(crow_flow), intent(in)    :: crow

  continue

    if ( (viscous_method == 3) .and. (ivisc > 0) ) call set_flsq_lu(grid)

    if ( (agglomerate_inviscid_order == 0) .and. (grid%origin >= 3) ) return

    if ( ccrecon <= 0 ) call clsq_lu_cc( grid, crow )

  end subroutine set_up_lsq_cc

!================================== CLSQ_LU_CC ===============================80
!
! Lu for least-squares gradients for the cell-centered option
!
!=============================================================================80
  subroutine clsq_lu_cc( grid, crow )

    use grid_types,     only : grid_type
    use comprow_types,  only : crow_flow
    use lmpi,           only : lmpi_reduce
    use info_depr,      only : skeleton

    type(grid_type), intent(inout) :: grid
    type(crow_flow), intent(in)    :: crow

    integer, dimension(3) :: sc, sct

  continue

    !...use symmetry bc in least squares always.

    sct(1) = grid%symmetry_x_faces
    sct(2) = grid%symmetry_y_faces
    sct(3) = grid%symmetry_z_faces
    call lmpi_reduce(sct,sc)
    if ( skeleton > 0 ) then
      write(*,*) ' Enforcing symmetry in least squares...q_2d=',q_2d
      write(*,*) ' ....n_momx,n_momy,n_momz=',n_momx,n_momy,n_momz
      write(*,*) ' ..................origin=',grid%origin
      write(*,*) ' ...grid%symmetry_x_faces=',sc(1)
      write(*,*) ' ...grid%symmetry_y_faces=',sc(2)
      write(*,*) ' ...grid%symmetry_z_faces=',sc(3)
    endif

    call clsq_lu(                                                              &
           grid%ncell0, grid%xc, grid%yc, grid%zc,                             &
           crow%ia, crow%ja, crow%ia_ns,                                       &
           grid%rlsq_ia, grid%rlsq_ja,                                         &
           grid%cgamma, grid%slen, grid%slenxn, grid%slenyn, grid%slenzn,      &
           grid%symmetry_x_cells, grid%symmetry_x_coord,                       &
           grid%symmetry_y_cells, grid%symmetry_y_coord,                       &
           grid%symmetry_z_cells, grid%symmetry_z_coord,                       &
           grid%rlsq, grid%bcc, grid%cl2g )

  end subroutine clsq_lu_cc

!=================================== GRAD_VARIABLE_CC ========================80
!
! Calculates gradients using a number of different strategies
!
!=============================================================================80

  subroutine grad_variable_cc( grid, soln, crow,                               &
                               variable_type, gradient_method )

    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use comprow_types,     only : crow_flow
    use info_depr,         only : skeleton
    use cc_defs,           only : ccrecon
    use lmpi,              only : lmpi_conditional_stop
    use lmpi_app,          only : lmpi_xfer, lmpi_set_grid_level
    use node_avg_cc,       only : avg_to_nodes, green_gauss_cc

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    character(len=80), intent(in) :: variable_type
    character(len=80), intent(in) :: gradient_method

    integer :: ierr

  continue

    ierr = 0

    average_to_nodes : if ( (ccrecon == 2) .or.             &
                            (soln%viscous_method == 4) ) then
      if ( skeleton > 0 ) write(*,*) "  Averaging to nodes..."
      call avg_to_nodes(grid,soln)
    endif average_to_nodes

    if ( skeleton > 0 ) write(*,*) "  Calculating gradients (cc)...",       &
                              trim(variable_type),"...",trim(gradient_method)

    if ( trim(variable_type) == 'inviscid' .and.       &
         trim(gradient_method) == 'least-squares' ) then

      call clsq_grad_driver_cc( grid, soln, crow )

    elseif ( trim(variable_type) == 'inviscid' .and.     &
             trim(gradient_method) == 'green-gauss' ) then

      call green_gauss_cc(grid%nnodes01, soln%qavg, grid%x, grid%y, grid%z,  &
                          soln%gradx, soln%grady, soln%gradz, grid%nelem,    &
                          grid%elem, soln%n_tot, soln%n_grd, grid%ncell01,   &
                          grid%ncell0)

    else

      if ( lmpi_master ) then
        write(*,*) '  gradient option not available...stopping'
        write(*,*) '  variable_type requested is ',trim(variable_type)
        write(*,*) '  gradient_method requested is ',trim(gradient_method)
      endif
      ierr = 1

    endif

    call lmpi_conditional_stop(ierr,'incompatible"grad_variable_cc')

    if ( skeleton > 0 ) write(*,*) "  Updating off-processor nodes...&
                                   &gradx/y/z grid=",grid%igrid

    call lmpi_set_grid_level(grid%igrid)
    call lmpi_xfer(soln%gradx)
    call lmpi_xfer(soln%grady)
    call lmpi_xfer(soln%gradz)

  end subroutine grad_variable_cc

!================================== CLSQ_GRAD_DRIVER_CC ======================80
!
! Gradients using least squares for the cell-centered option
!
!=============================================================================80
  subroutine clsq_grad_driver_cc( grid, soln, crow )

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use comprow_types,        only : crow_flow
    use info_depr,            only : ntt, ivisc
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations
    use debug_jacobian,       only : edge_terms_only

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: my_ntt, ierr

    logical :: first_time_through = .true.

  continue

    ierr = 0

    if ( (agglomerate_inviscid_order == 0) .and. (grid%origin >= 3) ) then
      !...reset gradx because we use it for temporary storage in prolongation.
      soln%gradx(:,:) = 0._dp
      return
    endif

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    if ( (my_ntt <= first_order_iterations) .and. (ivisc == 0) ) return

    if ( edge_terms_only ) return

    if ( timing_lsq ) call rtime('start:clsq_grad_driver_cc')

    call clsq_grad_cc( 1,grid%ncell0,                                          &
           soln%eqn_set, soln%n_tot, soln%n_grd,                               &
           soln%q_dof,  soln%gradx, soln%grady, soln%gradz,                    &
           grid%ncell0, grid%ncell01, grid%xc, grid%yc, grid%zc,               &
           crow%ia, crow%ja, crow%ia_ns,                                       &
           grid%rlsq_ia, grid%rlsq_ja,                                         &
           grid%cgamma, grid%slen, grid%slenxn, grid%slenyn, grid%slenzn,      &
           grid%symmetry_x_cells, grid%symmetry_x_coord,                       &
           grid%symmetry_y_cells, grid%symmetry_y_coord,                       &
           grid%symmetry_z_cells, grid%symmetry_z_coord,                       &
           grid%rlsq, grid%bcc, ierr, first_time_through )

    first_time_through = .false.
    call lmpi_conditional_stop( ierr, 'clsq_grad_driver_cc')

    if ( timing_lsq ) call rtime('..end:clsq_grad_driver_cc')

  end subroutine clsq_grad_driver_cc

!================================= WRSUM =====================================80
!
! Entries in clsq summations (with wsq addition)
!
!=============================================================================80

  pure function wrsum( sc )

    real(dp), intent(in), dimension(4) :: sc

    real(dp), dimension(3,3) :: wrsum

    real(dp) :: dx, dy, dz, wsq

  continue

   dx  = sc(1) ; dy  = sc(2) ; dz  = sc(3) ; wsq = sc(4)

   !...upper triangular parts

   wrsum(1,1) = dx*dx*wsq
   wrsum(1,2) = dx*dy*wsq
   wrsum(1,3) = dx*dz*wsq
   wrsum(2,2) = dy*dy*wsq
   wrsum(2,3) = dy*dz*wsq
   wrsum(3,3) = dz*dz*wsq

   !...lower triangular - diagonal parts

   wrsum(2,1) = 0._dp
   wrsum(3,1) = 0._dp
   wrsum(3,2) = 0._dp

  end function wrsum

  include 'weights.f90'
  include 'qp_from_qt.f90'

  include 'lsq_map_ref.f90'
  include 'lsq_coords.f90'
  include 'lsq_gradc.f90'
  include 'lsq_scoords.f90'
  include 'lsq_lc_max.f90'
  include 'lsq_scoords_sx.f90'
  include 'lsq_scoords_sy.f90'
  include 'lsq_scoords_sz.f90'
    include 'mapping_system.f90'
    include 'mapping_coords.f90'
    include 'coords_cylindrical_polar.f90'

end module lsq_cc

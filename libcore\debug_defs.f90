module debug_defs

  use kinddefs, only : dp

  implicit none

  private

  public :: debug_q, debug_q_proc, debug_q_loc, debug_q_global
  public :: debug_face_loc, debug_node_loc, debug_cfl, debug_residual
  public :: inspect_mean_jacobian, inspect_turb_jacobian,  &
            inspect_mean_equation, inspect_turb_equation
  public :: allow_dt
  public :: composite_jacobian_lhs, composite_jacobian_rhs
  public :: composite_jacobian_seed, composite_jacobian_fraction
  public :: check_defect_correction
  public :: stability_rinds, stability_rind_adj
  public :: debug_point_solves, debug_line_solves, debug_lm
  public :: debug_sr, debug_frechet, debug_lines
  public :: debug_rt_nonlinear_control
  public :: implicit_line_sets
  public :: best_practice, bp_solution, bp_discretization
  public :: symmetry_bcs
  public :: sa_diffusion_eto, diff_edge_avg_t, diff_edge_avg_m
  public :: gradient_construction_lhs
  public :: gradient_construction_rhs, alpha
  public :: zero_dq_meanflow
  public :: zero_dq_turbulence
  public :: d_driver_source
  public :: fraction_allowable_source
  public :: multigrid_timing_file
  public :: relaxation_log, ntt_print

  public :: interior_inviscid
  public :: interior_viscous
  public :: boundary_closure_viscous
  public :: boundary_closure_inviscid
  public :: boundary_closure_strong

  public :: ntt_for_jacobian_check
  public :: skip_perturb_input

  public :: write_q_boundary_discrete

  public :: frechet_epsilon_scale

  public :: grid_measures, hiro_agglom

  public :: test_freestream

  public :: integers, reals

  public :: weighted_lsq_diffusion_m
  public :: weighted_lsq_diffusion_t
  public :: weighted_lsq_source_t

  public :: unified_diffusion
  public :: forces_via_fluxes

  public :: constrain_force_integration
  public :: x_min_force_integration, x_max_force_integration
  public :: extract_special_line
  public :: write_negative_turb_files, write_resmax_files

  logical :: write_negative_turb_files = .false.
  logical :: write_resmax_files = .false.
  logical :: constrain_force_integration = .true.
  real(dp) :: x_min_force_integration = -huge(1._dp)
  real(dp) :: x_max_force_integration = +huge(1._dp)

  public :: riembc_debug
  public :: debug_dc

  integer :: forces_via_fluxes = 0

  logical :: weighted_lsq_diffusion_m = .true.
  logical :: weighted_lsq_diffusion_t = .false.
  logical :: weighted_lsq_source_t    = .false.

  logical :: multigrid_timing_file = .false.
  logical :: relaxation_log        = .false.
  integer :: ntt_print             = 1

  integer,  dimension(10) :: integers = 10*0
  real(dp), dimension(10) :: reals = 10*0._dp

  logical :: grid_measures = .false. !=T, dump out cell vol stats and stop
  logical :: hiro_agglom = .false.    !+T, agglomerate and stop

  logical :: debug_q = .false.     ! Flag to debug
  integer :: debug_q_proc   = 0    ! Processor to debug (0 is master)
  integer :: debug_q_loc    = 1    ! q_dof location
  integer :: debug_q_global = 0    ! q_dof global location
  integer :: debug_face_loc = 1    ! Face location
  integer :: debug_node_loc = 1    ! Node location

  logical :: skip_perturb_input = .false.
  logical :: debug_cfl          = .false.
  logical :: debug_residual     = .false.
  integer :: debug_rt_nonlinear_control = 0

  logical :: inspect_mean_jacobian = .false.
  logical :: inspect_turb_jacobian = .false.
  integer :: inspect_mean_equation = 1
  integer :: inspect_turb_equation = 1

  integer, dimension(2) :: debug_point_solves = 0 !meanflow/turb
  integer, dimension(2) :: debug_line_solves  = 0 !meanflow/turb
  logical               :: debug_lm      = .false.!LM and dQ maximums
  logical               :: debug_frechet = .false.!scaling detail
  logical               :: debug_lines   = .false. !Tecplot lines

  logical :: allow_dt = .true.

  logical :: composite_jacobian_lhs = .false.
  logical :: composite_jacobian_rhs = .false.

  integer  :: composite_jacobian_seed = 1 !Seed for random function
  real(dp) :: composite_jacobian_fraction

  logical :: check_defect_correction = .false.

  integer :: stability_rinds = 1
  integer :: stability_rind_adj = 1

  integer :: debug_sr = 0

  logical :: interior_inviscid         = .true.
  logical :: interior_viscous          = .true.
  logical :: boundary_closure_viscous  = .true.
  logical :: boundary_closure_inviscid = .true.
  logical :: boundary_closure_strong   = .true.

  logical :: write_q_boundary_discrete   = .false.

  integer :: ntt_for_jacobian_check = -10

  logical :: best_practice       = .false.
  logical :: bp_solution         = .false.
  logical :: bp_discretization   = .false.
  logical :: symmetry_bcs         = .false.

  logical :: sa_diffusion_eto   = .false.
  integer :: diff_edge_avg_t, diff_edge_avg_m
  integer :: gradient_construction_lhs, gradient_construction_rhs
  real(dp) :: alpha = 4._dp / 3._dp
  logical :: zero_dq_meanflow   = .false.
  logical :: zero_dq_turbulence = .false.

  logical :: extract_special_line  = .false.

  real(dp) :: d_driver_source = 1._dp
  real(dp) :: fraction_allowable_source = 0._dp

  logical, dimension(10) :: implicit_line_sets = .false.

  real(dp) :: frechet_epsilon_scale = 1._dp

! freestream preservation testing information
! test_freestream = .true. to test freestream preservation
! (all bc except symmetry set to farfield riemann and
! any turbulence source terms are zeroed out)

  logical :: test_freestream = .false.

! pull all the turbulence diffusion calculation through one routine

  logical :: unified_diffusion = .false.

!flag to help debug differences between compressible and generic gas
!use of (node based) riemann farfield bc (bc code 5000)

  logical :: riembc_debug = .false.

!flag to print out species results in jacobian checker for GG decoupled path

  logical :: debug_dc = .false.

end module debug_defs

module residual_nonin

  implicit none

  private

  public :: atlam_noninertial

contains

!================================ ATLAM_NONIN ================================80
!
! This does A-transpose times lambda at time-level n
! for the noninertial part of the adjoint residual
!
! Puts the result in res
!
!=============================================================================80

  subroutine atlam_noninertial( nnodes0, nnodes01, vol, adim, ndim, ad, coltag,&
                                rlam, res, nfunctions, iau, aa)

    use kinddefs,         only : dp
    use noninertials,     only : xrotrate_ni,yrotrate_ni,zrotrate_ni
    use adjoint_switches, only : rn, np
    use lmpi,             only : lmpi_master, lmpi_die

    integer, intent(in) :: nnodes0, adim, nnodes01, ndim
    integer, intent(in), optional :: nfunctions

    integer, dimension(:), intent(in), optional :: iau

    real(dp),  dimension(nnodes01),      intent(in)    :: vol
    real(dp),  dimension(adim,nnodes01), intent(in)   , optional :: coltag
    real(dp),  dimension(:,:,:),         intent(in)   , optional :: rlam
    real(dp),  dimension(:,:,:),         intent(inout), optional :: res
    real(dp),  dimension(:,:,:),         intent(inout), optional :: aa
    real(dp),  dimension(5,5),           intent(inout), optional :: ad

    integer :: n, j, iii, jjj, idiag, k, kk

    real(dp), dimension(ndim) :: rlamb
    real(dp), dimension(5,5)  :: a

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) .or.                   &
           .not.present(nfunctions) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_noninertial, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(aa) ) then
      if ( .not.present(iau) ) then
        if ( lmpi_master ) then
          write(*,*) 'aa requested in atlam_noninertial, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    a = 0.0_dp

! Loop over all nodes

    nodes : do n = 1, nnodes0

      a(2,3) = -vol(n)*zrotrate_ni
      a(2,4) =  vol(n)*yrotrate_ni

      a(3,2) =  vol(n)*zrotrate_ni
      a(3,4) = -vol(n)*xrotrate_ni

      a(4,2) = -vol(n)*yrotrate_ni
      a(4,3) =  vol(n)*xrotrate_ni

      if ( fill_res ) then
        if ( rn == n .and. np == n ) then
          do iii = 1, 5
            do jjj = 1, 5
              ad(iii,jjj) = ad(iii,jjj) + coltag(iii,n)*a(iii,jjj)
            end do
          end do
        endif
        functions : do j = 1, nfunctions
          rlamb(1:ndim) = coltag(1:ndim,n)*rlam(1:ndim,n,j)
          do k = 1, ndim
            do kk = 1, ndim
              res(k,n,j) = res(k,n,j) + a(kk,k)*rlamb(kk)
            end do
          end do
        end do functions
      endif

      if ( fill_a ) then
        idiag = iau(n)
        aa(1:ndim,1:ndim,idiag) = aa(1:ndim,1:ndim,idiag) + a(1:ndim,1:ndim)
      endif

    end do nodes

  end subroutine atlam_noninertial

end module residual_nonin

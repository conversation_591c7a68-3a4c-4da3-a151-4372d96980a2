!============================ VISCOSITY_LAW_DDT ==============================80
!
! This routine computes the viscosity for a perfect gas,
! given t (temperature) and cstar (sutherland_constant/tref)
!
!=============================================================================80

  pure function viscosity_law_ddt( cstar, t )

    use kinddefs, only : dp
    use ddt,      only : ddt7, assignment(=), operator(+), operator(*)         &
                       , operator(/), ddt_sqrt

    real(dp),   intent(in) :: cstar
    type(ddt7), intent(in) :: t

    type(ddt7)             :: viscosity_law_ddt
    real(dp), parameter    :: one = 1.0_dp

  continue

    viscosity_law_ddt = (one+cstar)/(t+cstar)*t*ddt_sqrt(t)

  end function viscosity_law_ddt

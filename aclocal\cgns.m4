#
# Assigned Shell Variables:
#   $with_CGNS              Build with CGNS support
#
# Assigned Output Variables:
#   @CGNSinclude@           Path to CGNS includes
#   @CGNSlibrary@           Path to CGNS library
#
# Assigned AM_CONDITIONALS:
#   BUILD_CGNS_SUPPORT
#
AC_DEFUN([AX_CGNS],[

AC_ARG_WITH(CGNS,
        [[  --with-CGNS[=ARG]       CGNS library path [ARG=no]]],
        [with_CGNS=$withval],       [with_CGNS="no"])

if test "$with_CGNS" != 'no'
then
  AC_CHECK_FILE([$with_CGNS/lib/libcgns.a],
                [have_CGNS='yes'],[have_CGNS='no'])

  if test "$have_CGNS" != 'no'
  then
    AC_DEFINE([HAVE_CGNS],[1],[CGNS is available])
    CGNSinclude="$with_CGNS/include"
    CGNSlibrary="$with_CGNS/lib"

    AC_CHECK_LIB([cgns],[cg_elements_partial_write_f],
                 [cg_elements_partial_write='available'],
                 [cg_elements_partial_write='missing'],
                 [-L$with_CGNS/lib])
    if test "$cg_elements_partial_write" == 'available'
    then
      AC_DEFINE([HAVE_CGNS_ELEM_PART_WRITE],[1],
                [CGNS library provides cg_elements_partial_write_f])
    fi

  else
    AC_MSG_ERROR([CGNS requested but not found])
  fi
  AC_SUBST([CGNSinclude])
  AC_SUBST([CGNSlibrary])
  AM_CONDITIONAL(BUILD_CGNS_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_CGNS_SUPPORT,false)
fi

])



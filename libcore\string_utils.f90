module string_utils

  implicit none

  private

  public :: basename, char2int, char2real, clean, compress, dirname, downcase
  public :: empty, geti, getreal, int_width, int_to_s, join, list_to_array
  public :: ljust, range_to_array, rjust, split, sub_string
  !beginNoComplexInterface
  public :: sprintf
  !endNoComplexInterface
  public :: squeeze, strip, upcase

  logical, public :: error_condition

  integer, public, parameter :: MAX_STR_LEN = 1024

  integer, parameter :: MAX_INT_WIDTH = 19 ! digits in 2**62

!=================================== JOIN ====================================80
!
! Join strings with optional separator, either individual strings or an array
!
!  print*, join( 'a', 'b', 'c' )           !=> 'abc'
!  print*, join( (/ 'a', 'b', 'c'/) )      !=> 'abc'
!
!  print*, join( 'a', 'b', 'c', '-' )      !=> 'a-b-c'
!  print*, join( (/ 'a', 'b', 'c'/), '-' ) !=> 'a-b-c'
!
!=============================================================================80

  interface join
    module procedure join_5s
    module procedure join_as
  end interface join

!=================================== SPRINTF =================================80
!
! Limited C-style sprintf, i.e., strings with embedded format specifiers
!
!  print*, sprintf( 'char: %a',    'string' ) !=> 'char: string'
!  print*, sprintf( 'int: %i0',          42 ) !=> 'int: 42'
!  print*, sprintf( 'real: %f4.2',     1.23 ) !=> 'real: 1.23'
!
!=============================================================================80

  interface sprintf
    module procedure sprintf_c
    module procedure sprintf_i
    module procedure sprintf_r
  end interface sprintf

contains

!================================== BASENAME =================================80
!
! Deletes any prefix ending with the last slash '/' character present
! and a suffix, if given.
!
! Note: not windows friendly due to hardcoded separator character.
!
!  print*, basename('/path/to/file1')            !=> 'file1'
!  print*, basename('file2.ext','.ext')          !=> 'file2'
!  print*, basename('/path/to/file3.ext','.ext') !=> 'file3'
!
!=============================================================================80

  function basename( string, suffix )

    character(*),           intent(in) :: string
    character(*), optional, intent(in) :: suffix
    character(len_trim(string))        :: basename

    integer :: first, last

    continue

    first = index( string, '/', back=.true. ) + 1

    if (present(suffix)) then
      last = index( string, trim(suffix) ) - 1
    else
      last = len_trim(string)
    end if

    basename = string(first:last)

  end function basename

!=============================== CHAR2INT ====================================80
!
! Convert character string to an integer
!
!  print*, char2int('123') !=> 123
!  print*, char2int('-1')  !=> -1
!  print*, char2int('7c')  !=> 7, error_condition = .true.
!  print*, char2int('1.2') !=> 1, error_condition = .true.
!  print*, char2int('a6')  !=> 0, error_condition = .true.
!
!=============================================================================80

  function char2int( string )

    integer                  :: char2int
    character(*), intent(in) :: string

    character(len_trim(adjustl(string))) :: sstring

    integer :: first_nondigit_index, io_status

    continue

    error_condition = .false.

    sstring = trim(adjustl(string))

    first_nondigit_index = verify( sstring, '+-0123456789' )

    select case (first_nondigit_index)
    case (0)  ! attempt conversion of entire string
      first_nondigit_index = len(sstring) + 1
    case (1:) ! trailing non-digits or no digits
      if ( first_nondigit_index <= len(sstring) ) error_condition = .true.
    end select

    read(sstring(1:first_nondigit_index-1),*,iostat=io_status) char2int

    if ( io_status /= 0 ) then ! no leading digits found or integer too big
      error_condition = .true.
      char2int = 0
    end if

  end function char2int

!=============================== CHAR2REAL ===================================80
!
! Convert string to an dp-kind real value
!
!  print*, char2real('42.0') !=> 42.0
!  print*, char2real('1e-5') !=> 1e-5
!  print*, char2real('3')    !=> 3.0
!  print*, char2real('5.3k') !=> 5.3, error_condition = .true.
!  print*, char2real('abcd') !=> 0,   error_condition = .true.
!
!=============================================================================80

  function char2real( string, trailing_ok )

    use kinddefs, only : dp

    ! beginNeverComplex
    real(dp)                 :: char2real
    ! endNeverComplex
    character(*),      intent(in) :: string
    logical, optional, intent(in) :: trailing_ok

    character(len_trim(adjustl(string))) :: sstring

    integer :: first_nonfloat_index, io_status
    logical :: opt_trailing_ok

    continue

    opt_trailing_ok = .false.
    if (present(trailing_ok)) opt_trailing_ok = .true.

    error_condition = .false.

    sstring = trim(adjustl(string))

    first_nonfloat_index = verify( sstring, '+-.0123456789dDeE' )

    select case (first_nonfloat_index)
    case (0)  ! attempt conversion of entire string
      first_nonfloat_index = len_trim(sstring) + 1
    case (1:) ! trailing non-digits or no digits
      if ( first_nonfloat_index <= len(sstring) .and. &
           .not. opt_trailing_ok ) then
        error_condition = .true.
      end if
    end select

    read(sstring(1:first_nonfloat_index-1),*,iostat=io_status) char2real

    if ( io_status /= 0 ) then ! no leading digits found or real too big
      error_condition = .true.
      char2real = 0.0_dp
    end if

  end function char2real

!==================================== CLEAN ==================================80
!
! Remove control characters from a string
!
!  print*, clean('a \c\t b \g') !=> 'a b    '
!
!=============================================================================80

  function clean(string)

    character(*), intent(in)    :: string
    character(len_trim(string)) :: clean

    character(1) :: c

    integer :: i, j

    continue

    j = 0
    clean = ''

    do i = 1, len(string)
      c = string(i:i)
      if ( iachar(c) < 32 ) cycle
      j = j + 1
      clean(j:j) = c
    end do

  end function clean

!==================================== COMPRESS ===============================80
!
! Remove whitespace from a string
!
!  print*, compress(' a b [tab] c') !=> 'abc     '
!
!=============================================================================80

  function compress(string)

    character(*), intent(in) :: string
    character(len(string))   :: compress

    character(1) :: c

    integer :: i

    continue

    compress = ''

    do i = 1, len_trim(string)
      c = string(i:i)
      if ( c == ' ' .or. c == char(9) ) cycle
      compress = compress(1:len_trim(compress))//c
    end do

  end function compress

!===================================== DIRNAME ===============================80
!
! Return directory portion of pathname
!
!  print*, dirname('/path/to/file1') !=> '/path/to'
!  print*, dirname('file2.ext')      !=> '.'
!
!=============================================================================80

  function dirname(pathname)

    character(*), intent(in)      :: pathname
    character(len_trim(pathname)) :: dirname

    integer :: last_seperator

    continue

    last_seperator = index( pathname, '/', back=.true. )

    select case (last_seperator)
    case (0)
      dirname = '.'
    case (1)
      dirname = '/'
    case default
      dirname = pathname(1:last_seperator-1)
    end select

  end function dirname

!=================================== DOWNCASE ================================80
!
! Convert upper case letters in a string to lower case letters.
!
!  print*, downcase('sTRING') !=> 'string'
!
!=============================================================================80

  function downcase(string)

    character(*), intent(in) :: string
    character(len(string))   :: downcase

    character(1) :: c
    integer      :: i

    continue

    downcase = string

    do i = 1,len(string)
      c = string(i:i)
      if ( c >= 'A' .and. c <= 'Z') downcase(i:i) = achar(iachar(c) + 32)
    end do

  end function downcase

!==================================== EMPTY ==================================80
!
! Test for empty string
!
!  print*, empty('')  !=> .true.
!  print*, empty(' ') !=> .true.
!  print*, empty('#') !=> .false.
!
!=============================================================================80

  function empty( string )

    logical                  :: empty
    character(*), intent(in) :: string

    continue

    empty = strip(string) == ''

  end function empty

!=================================== GETI ====================================80
!
! Grab the integer after a 'key_char = '
!
!  print*, geti( 'i = 1', 'i' )      !=> 1
!  print*, geti( 'J=213, K=7', 'j' ) !=> 213
!  print*, geti( '8', 'j' )          !=> 0
!
!=============================================================================80

  function geti( string, key )

    integer                   :: geti
    character(*), intent(in)  :: string
    character(*), intent(in)  :: key

    integer :: i, slen

    continue

    geti = 0

    i = index( downcase(string), downcase(key) )

    if ( i == 0 ) return

    i  = i + len(key)

    slen = len(string)

    if ( scan(adjustl(string(i:slen)),'=') == 1 ) then

      i = i + scan(string(i:slen),'=')
      geti = char2int(string(i:slen))

    else ! try looking for another occurrance of key FIXME: make recursive!

      i = index( downcase(string(i:slen)), downcase(key) ) + i - 1
      if ( i == 0 ) return
      i  = i + len(key)
      if ( scan(adjustl(string(i:slen)),'=') /= 1 ) return ! give up
      i = i + scan(string(i:slen),'=')
      geti = char2int(string(i:slen))

    end if

  end function geti

!================================== GETREAL ==================================80
!
! Grab a real out of a text string of the form KEY = 5.483
! where '=' and spaces are optional.
!
!  print*, getreal('time=4.5','TIME') !=> 4.5
!  print*, getreal(' I = 6.0 s','i')  !=> 6.0
!  print*, getreal('','')             !=> 0
!
!=============================================================================80

  function getreal( string, key )

    use kinddefs, only : dp

    real(dp)                  :: getreal
    character(*), intent(in)  :: string
    character(*), intent(in)  :: key

    integer :: i, slen

    continue

    getreal = 0.0_dp

    i = index( downcase(string), downcase(key) )

    if ( i == 0 ) return

    i = i + len(key)

    slen = len(string)

    if ( scan(adjustl(string(i:slen)),'=') == 1 ) then
      i = i + scan(string(i:slen),'=')
    end if

    getreal = char2real( string(i:slen), trailing_ok=.true. )

  end function getreal

!================================== INT_TO_S =================================80
!
! Convert integer to string
!
!  print*, int_to_s(1)  !=> '1'
!  print*, int_to_s(-3) !=> '-3'
!  print*, int_to_s(1000,placeholders=.true.) !=> '1,000'
!
!=============================================================================80

  function int_to_s( intgr, placeholders )

    integer,                  intent(in) :: intgr
    logical,        optional, intent(in) :: placeholders
    character(MAX_INT_WIDTH)             :: int_to_s

    character(MAX_INT_WIDTH) :: temp
    integer :: i, indx, first_digit

    continue

    write( int_to_s, '(i0)' ) intgr

    if (present(placeholders)) then
      if (placeholders) then ! surely there's a better algorithm than this
        temp = ''
        indx = 0
        first_digit = 1
        if ( intgr < 0 ) first_digit = 2
        do i = len_trim(int_to_s), first_digit+1, -1
          temp = int_to_s(i:i)//temp
          indx = indx + 1
          if ( mod(indx,3) == 0 ) temp = ','//trim(temp)
        end do
        int_to_s = int_to_s(1:first_digit)//temp
      end if
    end if

  end function int_to_s

!==================================== INT_WIDTH ==============================80
!
! Width of an integer
!
!  print*, int_width(42) !=> 2
!  print*, int_width(-1) !=> 2
!
!=============================================================================80

  function int_width( intgr )

    integer             :: int_width
    integer, intent(in) :: intgr

    continue

    int_width = max(ceiling(log10(real(abs(intgr)+1))),1)

    if ( intgr < 0 ) int_width = int_width + 1 ! minus sign

  end function int_width

!=================================== LIST_TO_ARRAY ===========================80
!
! Convert a string listing of integers into an array
!
!  call list_to_array( '1, 3-5,8', array )
!  print*, array !=> 1 3 4 5 8
!
!=============================================================================80

  subroutine list_to_array( string, array )

    use allocations, only: my_realloc_ptr

    character(*),          intent(in) :: string
    integer, dimension(:), pointer    :: array
    integer, dimension(:), pointer    :: subarray

    character(len_trim(string)), dimension(:), pointer :: strings

    integer :: i, old_size, inc_size

    continue

    call split( string, strings )

    nullify(array)
    nullify(subarray)
    do i = 1, size(strings)
      call range_to_array( strings(i), subarray )
      if (.not.associated(array)) then
        allocate( array(size(subarray)) )
        array = subarray
      else
        old_size = size(array) ; inc_size = size(subarray)
        call my_realloc_ptr( array, old_size+inc_size )
        array(old_size+1:old_size+inc_size) = subarray
      end if
      deallocate(subarray)
      nullify(subarray)
    end do

    deallocate(strings)

  end subroutine list_to_array

!======================================= LJUST ===============================80
!
! Left justify integer in string of given width, with optional padding
!
!  print*, ljust(42,3)      !=> '42 '
!  print*, ljust(42,4,'0')  !=> '4200'
!  print*, ljust(42,5,'xy') !=> '42xyx'
!  print*, ljust(42,1)      !=> '*'
!
!=============================================================================80

  function ljust( intgr, width, padstr )

    integer,                intent(in) :: intgr
    integer,                intent(in) :: width
    character(*), optional, intent(in) :: padstr
    character(width)                   :: ljust

    character(width) :: padstr_opt, padding

    integer :: fill

    continue

    padstr_opt = ' ' ; if (present(padstr)) padstr_opt = padstr

    fill = width - int_width(intgr)

    if ( fill >= 0 ) then
      padding = repeat( trim(padstr_opt), fill/max(1,len_trim(padstr_opt))+1 )
      write(ljust,'(i0,a)') intgr, padding(1:fill) ! only difference with rjust
    else
      ljust = repeat('*',width)
    end if

  end function ljust

!=================================== RANGE_TO_ARRAY ==========================80
!
! Convert a positive string integer range into an array
!
!  call range_to_array( '1-3', array )
!  print*, array !=> 1 2 3
!
!=============================================================================80

  subroutine range_to_array( string, array )

    character(*),               intent(in) :: string
    integer,      dimension(:), pointer    :: array

    integer :: fulcrum, first, last, i

    nullify(array)

    fulcrum = scan( string, '-' )

    if ( fulcrum < 2 .or. fulcrum > len_trim(string)-1 ) then
      allocate(array(1))
      array(1) = char2int( strip( string ) )
      return
    end if

    first = char2int( strip( string(1:fulcrum-1) ) )
    last  = char2int( strip( string(fulcrum+1:len_trim(string)) ) )

    allocate( array(last-first+1) )

    do i = 1, last - first + 1
      array(i) = first + i - 1
    end do

  end subroutine range_to_array

!================================== RJUST ====================================80
!
! Right justify integer in string of given width, with optional padding
!
!  print*, rjust(42,3)      !=> ' 42'
!  print*, rjust(42,4,'0')  !=> '0042'
!  print*, rjust(42,5,'xy') !=> 'xyx42'
!  print*, rjust(42,1)      !=> '*'
!
!=============================================================================80

  function rjust( intgr, width, padstr )

    integer,                intent(in) :: intgr
    integer,                intent(in) :: width
    character(*), optional, intent(in) :: padstr
    character(width)                   :: rjust

    character(width) :: padstr_opt, padding

    integer :: fill

    continue

    padstr_opt = ' ' ; if (present(padstr)) padstr_opt = padstr

    fill = width - int_width(intgr)

    if ( fill >= 0 ) then
      padding = repeat( trim(padstr_opt), fill/max(1,len_trim(padstr_opt))+1 )
      write(rjust,'(a,i0)') padding(1:fill), intgr ! only difference with ljust
    else
      rjust = repeat('*',width)
    end if

  end function rjust

!=================================== SPLIT ===================================80
!
! Split a comma- and/or space-delimited string into an array of strings.
!
!  call split( 'a,b, c', strings )
!  print*, strings  !=> 'a', 'b', 'c'
!
!  call split( '3 5000  name', strings )
!  print*, strings  !=> '3', '5000', 'name'
!
!=============================================================================80

  subroutine split( string, strings )

    character(*),               intent(in) :: string
    character(*), dimension(:), pointer    :: strings

    character(2) :: delimiters = ', '

    character(len_trim(string)) :: str

    integer, dimension(len_trim(string)) :: delim_idx

    integer :: i, idx, smn, smx, ndelim

    continue

    delimiters = ', '

    str = squeeze(strip(string))

    ndelim = 0

    smn = 1
    smx = len_trim(str)

    find_delimiters: do i = 1, smx-1
      idx = scan( str(smn:smx), delimiters )
      smn = smn + idx
      if ( idx == 1 ) cycle ! matched too early
      if ( idx == 0 ) exit  ! no more matches
      ndelim = ndelim + 1
      delim_idx(ndelim) = smn - 1
    end do find_delimiters

    allocate( strings(ndelim+1) )

    smn = 1
    do i = 1, ndelim
      strings(i) = strip( str(smn:delim_idx(i)-1) )
      smn = delim_idx(i) + 1
    end do
    strings(ndelim+1) = strip( str(smn:smx) )

  end subroutine split

!================================== SQUEEZE ==================================80
!
! Collapse multiple whitespaces in a string (including tab characters)
!
!  print*, squeeze(' a  b    c') !=> ' a b c    '
!
!=============================================================================80

  function squeeze(string)

    character(*), intent(in) :: string
    character(len(string))   :: squeeze

    logical :: prior_whitespace

    character(1) :: c, space=' ', tab=char(9)

    integer :: i, j

    continue

    j = 0
    squeeze = ''
    prior_whitespace = .false.

    do i = 1, len(string)
      c = string(i:i)
      if ( c == space .or. c == tab ) then
        if (prior_whitespace) cycle
        prior_whitespace = .true.
        c = space
      else
        prior_whitespace = .false.
      end if
      j = j + 1
      squeeze(j:j) = c
    end do

  end function squeeze

!==================================== STRIP ==================================80
!
! Strip leading and trailing blanks from a string
!
!  print*, strip(' string ') !=> 'string'
!
!=============================================================================80

  function strip( string )

    character(*),             intent(in) :: string
    character(len_trim(adjustl(string))) :: strip

    continue

    strip = trim(adjustl(string))

  end function strip

!================================= UPCASE ====================================80
!
! Convert lower case letters in a string to upper case letters.
!
!  print*, upcase('String') !=> 'STRING'
!
!=============================================================================80

  function upcase(string)

    character(*), intent(in) :: string
    character(len(string))   :: upcase

    character(1) :: c
    integer      :: i

    continue

    upcase = string

    do i = 1,len(string)
      c = string(i:i)
      if ( c >= 'a' .and. c <= 'z') upcase(i:i) = achar(iachar(c) - 32)
    end do

  end function upcase

!================================ SPRINTF_C ==================================80
!
! sprintf for chararcter strings
!
!=============================================================================80

  function sprintf_c( format_string, string )

    character(*), intent(in) :: string
    character(*), intent(in) :: format_string
    character(MAX_STR_LEN)   :: sprintf_c

    character(MAX_STR_LEN) :: fmt
    integer :: pct

    continue

    call find_format( format_string, fmt, pct )

    write(sprintf_c,fmt) format_string(1:pct-1), string

  end function sprintf_c

!=================================== SPRINTF_I ===============================80
!
! sprintf for integers
!
!=============================================================================80

  function sprintf_i( format_string, intgr )

    integer,      intent(in) :: intgr
    character(*), intent(in) :: format_string
    character(MAX_STR_LEN)   :: sprintf_i

    character(MAX_STR_LEN) :: fmt
    integer :: pct

    continue

    call find_format( format_string, fmt, pct )

    write(sprintf_i,fmt) format_string(1:pct-1), intgr

  end function sprintf_i

!=================================== SPRINTF_R ===============================80
!
! sprintf for reals
!
!=============================================================================80

  function sprintf_r( format_string, flt )

    real,         intent(in) :: flt
    character(*), intent(in) :: format_string
    character(MAX_STR_LEN)   :: sprintf_r

    character(MAX_STR_LEN) :: fmt
    integer :: pct

    continue

    call find_format( format_string, fmt, pct )

    write(sprintf_r,fmt) format_string(1:pct-1), flt

  end function sprintf_r

!================================= FIND_FORMAT ===============================80
!
! Find %-delimited Fortran format in string
!
!=============================================================================80

  subroutine find_format( format_string, fmt, pct )

    character(*), intent(in)  :: format_string
    character(*), intent(out) :: fmt
    integer,      intent(out) :: pct
    integer :: i

    continue

    fmt = '(a,'
    pct = index(format_string,'%')
    do i = pct+1, len_trim(format_string)
      fmt = trim(fmt)//format_string(i:i)
    end do
    fmt = trim(fmt)//')'

  end subroutine find_format

!=================================== JOIN_5S =================================80
!
! Join a series of strings together with optional seprarator
!
!=============================================================================80

  function join_5s( s1, s2, s3, s4, s5, sep )

    character(*),           intent(in) :: s1
    character(*), optional, intent(in) :: s2, s3, s4, s5, sep
    character(MAX_STR_LEN)             :: join_5s

    continue

    if (present(s2).and.present(s3).and.present(s4).and.present(s5)) then
      if (present(sep)) then
        join_5s = trim(s1)//sep//trim(s2)//sep//trim(s3)//sep//trim(s4)//sep//s5
      else
        join_5s = trim(s1)//trim(s2)//trim(s3)//trim(s4)//s5
      end if
    elseif (present(s2).and.present(s3).and.present(s4)) then
      if (present(sep)) then
        join_5s = trim(s1)//sep//trim(s2)//sep//trim(s3)//sep//s4
      else
        join_5s = trim(s1)//trim(s2)//trim(s3)//s4
      end if
    elseif (present(s2).and.present(s3)) then
      if (present(sep)) then
        join_5s = trim(s1)//sep//trim(s2)//sep//s3
      else
        join_5s = trim(s1)//trim(s2)//s3
      end if
    elseif (present(s2)) then
      if (present(sep)) then
        join_5s = trim(s1)//sep//s2
      else
        join_5s = trim(s1)//s2
      end if
    else
      join_5s = s1
    end if

  end function join_5s

!================================= JOIN_AS ===================================80
!
! Join an array of strings together with optional separator
!
!=============================================================================80

  function join_as( strings, sep )

    character(*), dimension(:), intent(in) :: strings
    character(*),     optional, intent(in) :: sep
    character(MAX_STR_LEN)                 :: join_as

    integer :: i

    continue

    join_as = strings(1)

    do i = 2, size(strings)
      if (present(sep)) then
        join_as = trim(join_as)//sep//strings(i)
      else
        join_as = trim(join_as)//strings(i)
      end if
    end do

  end function join_as

!================================== SUB_STRING ===============================80
!
! Checks whether one string (string2) is a sub string of another (string1)
!
!=============================================================================80

  logical function sub_string(string1, string2)

    character(len=*), intent (in) :: string1, string2

    continue

    if (index(trim(adjustl(string1)),trim(adjustl(string2))) > 0) then
     sub_string = .true.
    else
      sub_string = .false.
    end if

  end function sub_string

end module string_utils

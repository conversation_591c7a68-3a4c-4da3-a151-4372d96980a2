!==============================================================================
!
! Inlinable function to compute the shock speed at shock fitting boundary nodes
! Note that this subroutine uses primitive variables and that the left state is
! assumed to be the pre-shock state and the right state is assumed to be the
! post-shock state
!
!==============================================================================
  pure function shk_fit_shkspd(ceqinc, mode, dxsn, dysn, dzsn, ql, qr)

    use kinddefs, only : dp

    real(dp),               intent(in) :: ceqinc ! compat. eq. solution coeff.
    real(dp),               intent(in) :: mode   ! shock interaction mode
    real(dp),               intent(in) :: dxsn   ! x component of shock normal
    real(dp),               intent(in) :: dysn   ! y component of shock normal
    real(dp),               intent(in) :: dzsn   ! z component of shock normal
    real(dp), dimension(5), intent(in) :: ql     ! pre-shock prim. variables
    real(dp), dimension(5), intent(in) :: qr     ! post-shock prim. variables

    real(dp)                           :: shk_fit_shkspd

    real(dp) :: density_l, density_r, dlt_density
    real(dp) :: pressure_l, pressure_r, dlt_pressure
    real(dp) :: ubar_l, ubar_r
    real(dp) :: ss_1, ss_2

    real(dp), parameter :: my_1 = 1._dp

  continue

    ! N.B: The left state is assumed to be the pre-shock state and
    !      the right state is assumed to be the post-shock state

    density_l  = ql(1)
    density_r  = qr(1)
    pressure_l = ql(5)
    pressure_r = qr(5)

    ! Compute the density jump

    dlt_density = density_l - density_r
!   dlt_density = sign(1.0_dp, dlt_density)*max(1.0e-09_dp, abs(dlt_density))

    ! Compute the shock fitting boundary speed depending
    ! on whether the shock is interacting with the boundary or not

    !write(*,*) ' ceqinc  =', ceqinc
    !write(*,*) ' fsfrac  =', fsfrac
    !write(*,*) ' density_l  =', density_l
    !write(*,*) ' density_r  =', density_r
    !write(*,*) ' dlt_density =', dlt_density
    !write(*,*) ' max_density =', max_density
    !write(*,*) ' densfunc,shkdtct =', abs(dlt_density)/max_density, shkdtct

    ubar_l = ql(2)*dxsn + ql(3)*dysn + ql(4)*dzsn

    if ( nint(mode) == 1 ) then

      ! The shock was detected to be interacting with the boundary

      ! Compute the velocity normal to the boundary

      ubar_r = qr(2)*dxsn + qr(3)*dysn + qr(4)*dzsn

      ! Compute the shock speed

      dlt_pressure = pressure_r - pressure_l

      if (dlt_density /= 0.0_dp) then
        ss_1 = (density_l*ubar_l - density_r*ubar_r) / dlt_density
        if ( dlt_pressure /= 0.0_dp) then
          ss_2 = ubar_l - sign(my_1, dlt_pressure) / density_l *               &
                 sqrt(abs(density_l*density_r*dlt_pressure/dlt_density))
        else
          ss_2 = ubar_l
        end if
      else
        ss_1 = 0.0_dp
        ss_2 = ubar_l
      end if

      shk_fit_shkspd = -(ceqinc*ss_1 + (my_1 - ceqinc)*ss_2)

    else

      ! When a shock is not detected to be interacting with the boundary
      ! set the shock speed to the freestream contravarient velocity

      shk_fit_shkspd = ubar_l

    end if

  end function shk_fit_shkspd

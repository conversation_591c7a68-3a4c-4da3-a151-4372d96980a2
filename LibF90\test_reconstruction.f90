module test_reconstruction

  use kinddefs,        only : dp
  use lmpi,            only : lmpi_master, lmpi_bcast, lmpi_reduce,            &
                              lmpi_max_and_maxid, lmpi_conditional_stop,       &
                              lmpi_nproc
  use gradient_driver, only : grad_variable
  use info_depr,       only : skeleton
  use flux_util,       only : mark_boundaries
  use bc_names,        only : symmetry_x, symmetry_y, symmetry_z
  use exact_cylinder,  only : exact_cyl_2d

  implicit none

  private

  public :: reconstruction_dilemma

! gradient testing information
! test_gradient = .true. to check gradients
! implemented within incompressible/perfect gas and mixed element path
  logical :: test_gradient = .false.

! grad_function sets function to check gradients
! ...where grad_function is an integer value
! .....=-1, checks gradients of unity function
! .....= 0,  checks gradients of distance function (single proc only)
! .....= 1/2/3, checks linear function in x/y/z
! .....= 4, checks linear function in x and y and z
! .....= 5/6/7, checks quadratic function in x/y/z
! .....= 8, checks quadratic function in x and y and z
! .....= 9, checks radial function in x/z
! .....= 10, checks symmetric function in y
! .....= 11, checks antisymmetic function in y
! .....= 12, checks cylinder p : pressure
! .....= 13, checks cylinder u : x-velocity
! .....= 14, checks cylinder w : z-velocity
! .....may need distance function
  integer :: grad_function = 8

  integer :: f = 60  !file to write golden information

! check_all_nodes = .true. to include processor-shared nodes in evaluations
  logical :: check_all_nodes = .false.

  integer :: ierr = 0, proc_with_max

  real(dp) :: x_linear, y_linear, z_linear
  real(dp) :: x_quadratic, y_quadratic, z_quadratic

  real(dp) :: tolerance, max_first

  real(dp), parameter :: my_0   = 0.0_dp
  real(dp), parameter :: my_1   = 1.0_dp
  real(dp), parameter :: my_3rd = 1.0_dp/3.0_dp

  logical :: symmetry_x_flag, symmetry_y_flag, symmetry_z_flag

  integer :: i_q

contains

!=========================== RECONSTRUCTION_DILEMMA ==========================80
!
! Perform some tests related to reconstruction.
!
!=============================================================================80

  subroutine reconstruction_dilemma( grid, soln, nml_path )

    use inviscid_flux,  only : first_order_iterations
    use bc_names,       only : bc_used_for_distance_function
    use grid_types,     only : grid_type
    use solution_types, only : soln_type, compressible, incompressible

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    character(len=*), intent(in) :: nml_path

    integer :: first_order_iterations_hold, node, ib, n

    logical :: bypass_testing, test_new_hypothesis = .false.

  continue

!   Read namelist (possibly).

    call read_nml_check_gradient(soln%viscous_method, nml_path)

!   Bypass some paths for reconstruction.

    bypass_testing = .false.

    if ( soln%eqn_set /= compressible .and. &
         soln%eqn_set /= incompressible ) then
      bypass_testing = .true.
    end if

    if (bypass_testing .and. (skeleton > 0)) then
      write(*,*) ' Bypassing reconstruction tests.'
      write(*,*) ' Eqn_set must be compressible (0) or incompressible (1)'
      write(*,*) ' ...eqn_set=',soln%eqn_set
    endif

    if (bypass_testing) return

!   Set some symmetry flags.

    symmetry_x_flag = .false.
    symmetry_y_flag = .false.
    symmetry_z_flag = .false.
    do ib = 1,grid%nbound
      if (grid%bc(ib)%ibc == symmetry_x) symmetry_x_flag = .true.
      if (grid%bc(ib)%ibc == symmetry_y) symmetry_y_flag = .true.
      if (grid%bc(ib)%ibc == symmetry_z) symmetry_z_flag = .true.
    end do

!   Reconstruct the solution and check the errors.

    if(test_new_hypothesis) then
      first_order_iterations_hold = first_order_iterations
      first_order_iterations      = 0
      call reconstruct_check( grid , soln )
      first_order_iterations      = first_order_iterations_hold
    endif

    if(.not.test_gradient) return

!   Only allow two paths when testing gradient.

    if ( soln%eqn_set/=compressible .and. soln%eqn_set/=incompressible ) then
      bypass_testing = .true.
      if(lmpi_master) write(*,*) ' Test_gradient requested.'
      if(lmpi_master) write(*,*) ' Eqn_set must be 0 or 1.'
      if(lmpi_master) write(*,*) ' ...test_gradient=',test_gradient
      if(lmpi_master) write(*,*) ' ...eqn_set=',soln%eqn_set
      call lmpi_conditional_stop(1,"reconstruction_dilemma")
    endif

!   Check the gradients at the nodes.

    first_order_iterations_hold = first_order_iterations
    first_order_iterations      = 0 ! Rese to ensure gradients are calculated.
    if(lmpi_master) write(*,*) 'Resetting first_order_iterations=',&
                                          first_order_iterations

!   Check slen.

    ierr = 0

    do ib=1,grid%nbound
      if (.not. (bc_used_for_distance_function(grid%bc(ib)%ibc))) cycle
      do n=1,grid%bc(ib)%nbnode
        node = grid%bc(ib)%ibnode(n)
        if(node <= grid%nnodes01) then
          if ( grid%slen(node) > 1.0e-12_dp ) ierr = 1
        endif
      end do
    end do
    call lmpi_conditional_stop(ierr,'slen non-zero at wall')

    if(test_gradient) call gradient_check( grid, soln, nml_path )

    first_order_iterations = first_order_iterations_hold

  end subroutine reconstruction_dilemma

!=============================== READ_NML_CHECK_GRADIENT =====================80
!
! Read namelist for checking gradient with several approaches.
!
!=============================================================================80

  subroutine read_nml_check_gradient(viscous_method,nml_path)

    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use info_depr,         only : print_conditional
    use info_depr,         only : mixed, ngrid
    use lmpi,              only : lmpi_master, lmpi_bcast
    use namelist_util,     only : nml_error

    integer, intent(in) :: viscous_method
    character(len=*), intent(in) :: nml_path

    integer :: sunit, iostat1, iostat2

    logical :: show = .false.

    namelist / check_gradient / test_gradient, grad_function, check_all_nodes, &
                                show

    continue

    iostat1 = 0
    iostat2 = 0
    master_read_nml : if ( lmpi_master ) then

      sunit = available_unit()
      call se_open(sunit, file=nml_path, status='old', iostat=iostat1)
      if (iostat1 == 0) then
        show = print_conditional
        read(sunit,iostat=iostat2,nml=check_gradient)
        if ( show ) write(*,nml=check_gradient)
      else
        write(*,*) 'unable to open ', trim(nml_path)
      endif
      close(sunit)

    endif master_read_nml

    call nml_error( iostat1, iostat2, nml_path, 'check_gradient')

    call lmpi_bcast(test_gradient)
    call lmpi_bcast(grad_function)
    call lmpi_bcast(check_all_nodes)

!   check test_gradient parameters

    gradient_check : if(test_gradient) then

      if(ngrid > 1) then
        write(*,*) ' Only set up for single grid.'
        write(*,*) ' FATAL ERROR in read_nml_check_gradient : Stopping'
        write(*,*) ' ...ngrid=',ngrid
        call lmpi_conditional_stop(1,"read_nml_check_gradient1")
      endif !end conditional

      if(lmpi_nproc > 1 .and. grad_function == 0) then
        if(lmpi_master) then
          write(*,*) ' Gradient of distance function only on single processor.'
          write(*,*) ' ...number of processors=',lmpi_nproc
          write(*,*) ' ...grad_function=',grad_function
          write(*,*) ' FATAL ERROR in read_nml_check_gradient : Stopping'
        endif
        call lmpi_conditional_stop(1,"read_nml_check_gradient2")
      endif !end conditional

      if(.not.mixed) then
        write(*,*) ' Mixed element path required.'
        write(*,*) ' FATAL ERROR in read_nml_check_gradient : Stopping'
        call lmpi_conditional_stop(1,"read_nml_check_gradient3")
      endif !end conditional

      if(viscous_method > 0 ) then
        write(*,*) ' Mixed_edge path not allowed anymore for this test.'
        write(*,*) ' ==> Avoiding calculation of temperature/turb gradients.'
        write(*,*) ' FATAL ERROR in read_nml_check_gradient : Stopping'
        call lmpi_conditional_stop(1,"read_nml_check_gradient4")
      endif !end conditional

    endif gradient_check

  end subroutine read_nml_check_gradient

!=================================== GRADIENT_CHECK ==========================80
!
! Calculates some gradients for checking
!
!  Note : The dimensions of x, y, z, gradx, grady, and gradz are nnodes01
!         The on-processor subset are the first nnodes0
!         The array qnode is used as a temporary array (locations 1-4/5)
!
!=============================================================================80

  subroutine gradient_check( grid, soln, nml_path )

    use solve_box,            only : box_solve_fas_finest
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type, compressible, incompressible,  &
                                     generic_gas
    use flow_initialization,  only : init, initi, initialize_backplanes
    use info_depr,            only : twod
    use system_extensions,    only : se_open
    use lmpi,                 only : lmpi_master, lmpi_bcast, lmpi_max_and_maxid
    use nml_nonlinear_solves, only : itime

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    character(len=*), intent(in) :: nml_path

    integer ::times, nodesc, ib, ii, re_loop
    integer :: sf = 6, proc_with_max

    character(len=80) :: goldenfilename

    character(len=80) :: variable, gradient

    real(dp) :: x_d, y_d, z_d, theta, radius
    real(dp) :: r_x, r_z, grad_r, grad_t, grad_r_exact, grad_t_exact
    real(dp) :: x_d_max, y_d_max, z_d_max, re_eps = 0.10_dp

    real(dp), dimension(3) :: grad_q_x, grad_q_z

    logical :: error_qnode

    logical, parameter :: verbose = .false.

    integer, dimension(:), allocatable :: boundary_flag

    character(len=80), dimension(20) :: variable_list

  continue

    i_q = grad_function - 11

    if(lmpi_master) write(*,*) 'Testing gradient...grad_function=',          &
                     grad_function,'  check_all_nodes=',check_all_nodes

!   Allocate temporary array : boundary_flag

    allocate(boundary_flag(grid%nnodes01))

!   Set boundary_flag >= 1 for boundary nodes

    boundary_flag(:) = 0
    do ib = 1,grid%nbound
      call mark_boundaries( boundary_flag,      grid%nnodes01,       &
                            grid%bc(ib)%ibc,    grid%bc(ib)%nbnode,  &
                            grid%bc(ib)%ibnode, twod )
    end do

    goldenfilename = 'gradient.summary'

    if(lmpi_master) then

      call se_open(f,file=goldenfilename,  &
         form='formatted')

      rewind(f)

      !...write some information to facilitate processing
      write(f,'(3x,"twod=",L1)') twod
      write(f,'(3x,"define_box=",L1)') box_solve_fas_finest

      write(*,*) '  soln%n_tot=',soln%n_tot
      write(*,*) '  number of processors =',lmpi_nproc
      write(f,*) '  soln%n_tot=',soln%n_tot
      write(f,*) '  number of processors =',lmpi_nproc

      call qnode_function( grid, soln, grad_function,                   &
                           ierr, write_info=.true. )

    endif

    call lmpi_bcast(ierr)
    call lmpi_conditional_stop(ierr,"gradient_check1")

!   Check for inappropriate selections

    if(twod .and. ((grad_function == 10) .or. (grad_function == 11))) then
      if(lmpi_master) write(*,*) ' Inappropriate grad_function...stopping.'
      call lmpi_conditional_stop(1,"gradient_check2")
    endif

!   Overload soln%q_dof(1,:) array with specific functions
!   Except for antisymmetric function in y - overload soln%q_dof(3,:)

!   First compute some information about h_effective

    if(twod) then
      soln%q_dof(1,1:grid%nnodes0) = sqrt( grid%vol(1:grid%nnodes0) )
    else
      soln%q_dof(1,1:grid%nnodes0) = grid%vol(1:grid%nnodes0)**my_3rd
    endif
    variable_list(1) = 'h_effective'

!   Compute and print some norms of the gradient values

    if(lmpi_master) write(*,*) '  Processing h_effective values.'
    if(lmpi_master) write(f,*) '  Processing h_effective values.'
    error_qnode = .false.
    if(lmpi_master) write(*,*)
    if(lmpi_master) write(*,*) '  Norms for on-processor nodes:'
    if(lmpi_master) write(f,*)
    if(lmpi_master) write(f,*) '  Norms for on-processor nodes:'
    nodesc = grid%nnodes0
    call maxvalues( grid%nnodes01,  nodesc,                          &
                    grid%x,         grid%y,        grid%z,           &
                    soln%q_dof,     boundary_flag,                   &
                    soln%n_tot,     variable_list, sf, error_qnode,  &
                    h_effective = .true.)
    call maxvalues( grid%nnodes01,  nodesc,                          &
                    grid%x,         grid%y,        grid%z,           &
                    soln%q_dof,     boundary_flag,                   &
                    soln%n_tot,     variable_list, f,  error_qnode,  &
                    h_effective = .true.)

    times_loop : do times = 3,1,-1

      call qnode_function( grid, soln, grad_function, ierr )

      call lmpi_conditional_stop(ierr,"gradient_check3")

!     Since we are only interested in gradient of qnode(1,:)/qnode(3,:)

      variable = 'inviscid'

      if(times == 1) then

        if(lmpi_master) then
          write(*,*)
          write(*,*) '  Computing gradients using edge-based Green-Gauss.'
          write(f,*)
          write(f,*) '  Computing gradients using edge-based Green-Gauss.'
        endif

        gradient = 'green-gauss'

      elseif(times == 2) then

        if(lmpi_master) then
          write(*,*)
          write(*,*) '  Computing gradients using least squares.'
          write(f,*)
          write(f,*) '  Computing gradients using least squares.'
        endif

        gradient = 'least-squares'

      elseif(times == 3) then

        if(lmpi_master) then
          write(*,*)
          write(*,*) '  Computing gradients using weighted least squares.'
          write(f,*)
          write(f,*) '  Computing gradients using weighted least squares.'
        endif

        gradient = 'weighted-least-squares'

      end if

      write(f,*) '...variable=',trim(variable),' gradient=',trim(gradient)

      call grad_variable(grid, soln, variable, gradient)

!     Overload soln%q_dof with results

      soln%q_dof(4,:)  = soln%q_dof(1,:)
      if(grad_function == 11) soln%q_dof(4,:) = soln%q_dof(3,:)

      variable_list(4) = 'slen'
      if(grad_function == -1) variable_list(4) = 'unity'
      if(grad_function ==  1) variable_list(4) = 'linear_x'
      if(grad_function ==  2) then
        variable_list(4) = 'linear_y'
        if(twod) variable_list(4) = 'null'
      endif
      if(grad_function ==  3) variable_list(4) = 'linear_z'
      if(grad_function ==  4) then
        variable_list(4) = 'linear_x_y_z'
        if(twod) variable_list(4) = 'linear_x_z'
      endif
      if(grad_function ==  5) variable_list(4) = 'quadratic_x'
      if(grad_function ==  6) then
        variable_list(4) = 'quadratic_y'
        if(twod) variable_list(4) = 'null'
      endif
      if(grad_function ==  7) variable_list(4) = 'quadratic_z'
      if(grad_function ==  8) then
        variable_list(4) = 'quadratic_x_y_z'
        if(twod) variable_list(4) = 'quadratic_x_z'
      endif
      if(grad_function ==  9) variable_list(4) = 'radial_x_z'
      if(grad_function ==  10) variable_list(4) = 'y**2_x_z'
      if(grad_function ==  11) variable_list(4) = 'y**3_x_z'
      if(grad_function ==  12) variable_list(4) = 'cylinder:p'
      if(grad_function ==  13) variable_list(4) = 'cylinder:u'
      if(grad_function ==  14) variable_list(4) = 'cylinder:w'

      if(grad_function == 11) then
        soln%q_dof(1,:)  = soln%gradx(3,:)
        soln%q_dof(2,:)  = soln%grady(3,:)
        soln%q_dof(3,:)  = soln%gradz(3,:)
      else
        soln%q_dof(1,:)  = soln%gradx(1,:)
        soln%q_dof(2,:)  = soln%grady(1,:)
        soln%q_dof(3,:)  = soln%gradz(1,:)
      endif
      variable_list(1) = 'gradx'
      variable_list(2) = 'grady'
      variable_list(3) = 'gradz'

      if ( soln%eqn_set == compressible ) then
        if(twod) then
          soln%q_dof(5,1:grid%nnodes0) = sqrt( grid%vol(1:grid%nnodes0) )
        else
          soln%q_dof(5,1:grid%nnodes0) = grid%vol(1:grid%nnodes0)**my_3rd
        endif
        variable_list(5) = 'h_effective'
      end if

!     Compute and print some norms of the gradient values

      if(lmpi_master) write(*,*) '  Processing values for ',gradient

      error_qnode = .false.
      if(lmpi_master) write(*,*)
      if(lmpi_master) write(*,*) '  Norms for on-processor nodes:'
      nodesc = grid%nnodes0
      call maxvalues( grid%nnodes01,  nodesc,                        &
                      grid%x,         grid%y,        grid%z,         &
                      soln%q_dof,     boundary_flag,                 &
                      soln%n_tot,     variable_list, sf, error_qnode)

      if(lmpi_nproc > 1 .and. check_all_nodes) then
        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Norms for all processor nodes:'
        nodesc = grid%nnodes01
        call maxvalues( grid%nnodes01,  nodesc,                        &
                        grid%x,         grid%y,        grid%z,         &
                        soln%q_dof,     boundary_flag,                 &
                        soln%n_tot,     variable_list, sf, error_qnode )
      endif

!     Compute errors in gradients (if known)

      error_qnode = .true.
      if((grad_function >= 1) .and. (grad_function <= 4)) then
        soln%q_dof(1,:) = soln%q_dof(1,:) - x_linear
        soln%q_dof(2,:) = soln%q_dof(2,:) - y_linear
        soln%q_dof(3,:) = soln%q_dof(3,:) - z_linear
      elseif((grad_function >= 5) .and. (grad_function <= 8)) then
        soln%q_dof(1,:) = soln%q_dof(1,:) - 2.0_dp*x_quadratic*grid%x(:)
        soln%q_dof(2,:) = soln%q_dof(2,:) - 2.0_dp*y_quadratic*grid%y(:)
        soln%q_dof(3,:) = soln%q_dof(3,:) - 2.0_dp*z_quadratic*grid%z(:)
      elseif( grad_function == 9 ) then
        !...magnitude of gradient
        soln%q_dof(2,:) = sqrt( soln%q_dof(1,:)**2 + soln%q_dof(3,:)**2 ) &
                        - 1.0_dp
        !...error in grad_x
        soln%q_dof(1,:) = soln%q_dof(1,:) - grid%x(:)/                  &
                                            sqrt( grid%x(:)*grid%x(:) + &
                                                  grid%z(:)*grid%z(:) )
        !...error in grad_z
        soln%q_dof(3,:) = soln%q_dof(3,:) - grid%z(:)/                  &
                                            sqrt( grid%x(:)*grid%x(:) + &
                                                  grid%z(:)*grid%z(:) )
      elseif( (grad_function >= 12) .and. (grad_function <= 14) ) then
        if(verbose) then
          write(*,*) ' Computed gradients below &
          &(theta,r,x,z,gradx,gradz,gradx_exact,grad_z_exact).'
          do ii=1,grid%nnodes0
            if(abs(grid%y(ii)) > 0.0001_dp) cycle
            theta =  atan2( grid%z(ii) , grid%x(ii) )&
                     *180._dp/acos(-1.0_dp)
            radius = sqrt( grid%x(ii)*grid%x(ii) + &
                           grid%z(ii)*grid%z(ii) )
            call exact_cyl_2d(grid%x(ii), grid%z(ii), grad_q_x, 1, f )
            call exact_cyl_2d(grid%x(ii), grid%z(ii), grad_q_z, 2, f )
            write(*,'(i5,f12.5,e14.7,2f12.5,2f14.7,5x,2f14.7)')        &
              ii,theta,radius,                                         &
              grid%x(ii),grid%z(ii),soln%q_dof(1,ii),soln%q_dof(3,ii), &
              grad_q_x(i_q),grad_q_z(i_q)
          enddo
          write(*,*) ' Computed gradients below &
          &(theta,r,x,z,grad_r,grad_t,grad_r_exact,grad_t_exact).'
          do ii=1,grid%nnodes0
            if(abs(grid%y(ii)) > 0.0001_dp) cycle
            theta =  atan2( grid%z(ii) , grid%x(ii) )&
                     *180._dp/acos(-1.0_dp)
            radius = sqrt( grid%x(ii)*grid%x(ii) + &
                           grid%z(ii)*grid%z(ii) )
            r_x = grid%x(ii)/radius
            r_z = grid%z(ii)/radius
            grad_r = soln%q_dof(1,ii)*r_x + soln%q_dof(3,ii)*r_z
            grad_t =-soln%q_dof(1,ii)*r_z + soln%q_dof(3,ii)*r_x
            call exact_cyl_2d(grid%x(ii), grid%z(ii), grad_q_x, 1, f )
            call exact_cyl_2d(grid%x(ii), grid%z(ii), grad_q_z, 2, f )
            grad_r_exact = grad_q_x(i_q)*r_x + grad_q_z(i_q)*r_z
            grad_t_exact =-grad_q_x(i_q)*r_z + grad_q_z(i_q)*r_x
            write(*,'(i5,f12.5,e14.7,2f12.5,2f14.7,5x,2f14.7)')           &
              ii,theta,radius,                                            &
              grid%x(ii),grid%z(ii),grad_r,grad_t,grad_r_exact,grad_t_exact
          enddo
        endif
        do ii = 1,grid%nnodes0
          radius = sqrt( grid%x(ii)*grid%x(ii) + &
                         grid%z(ii)*grid%z(ii) )
          r_x = grid%x(ii)/radius
          r_z = grid%z(ii)/radius
          grad_r = soln%q_dof(1,ii)*r_x + soln%q_dof(3,ii)*r_z
          grad_t =-soln%q_dof(1,ii)*r_z + soln%q_dof(3,ii)*r_x
          call exact_cyl_2d(grid%x(ii), grid%z(ii), grad_q_x, 1, f )
          call exact_cyl_2d(grid%x(ii), grid%z(ii), grad_q_z, 2, f )
          grad_r_exact = grad_q_x(i_q)*r_x + grad_q_z(i_q)*r_z
          grad_t_exact =-grad_q_x(i_q)*r_z + grad_q_z(i_q)*r_x

          !...magnitude of gradient
          soln%q_dof(2,ii) = sqrt( grad_r**2       + grad_t**2       ) &
                           - sqrt( grad_r_exact**2 + grad_t_exact**2 )
          !...error in grad_radial
          soln%q_dof(1,ii) = grad_r - grad_r_exact
          !...error in grad_theta
          soln%q_dof(3,ii) = grad_t - grad_t_exact

          if(.not.verbose) cycle
          if(ii == 1) write(*,*) ' Errors in gradient below &
                                  &(theta,r,x,z,e_magnitude,e_grad_r,e_grad_t).'
          if(abs(grid%y(ii)) > 0.0001_dp) cycle
          theta =  atan2( grid%z(ii) , grid%x(ii) )*180._dp/acos(-1.0_dp)
          write(*,'(i5,f12.5,e14.7,2f12.5,3e13.5)') ii,theta,radius,   &
                                           grid%x(ii),                 &
                                           grid%z(ii),soln%q_dof(1:3,ii)
        enddo
      elseif(grad_function == 0) then
        soln%q_dof(1,:) = sqrt( soln%gradx(1,:)**2             &
                              + soln%grady(1,:)**2             &
                              + soln%gradz(1,:)**2 ) - my_1
        soln%q_dof(2,:) = my_0
        soln%q_dof(3,:) = my_0
      elseif(grad_function == -1) then
        soln%q_dof(1,:) = soln%q_dof(1,:) - my_0
        soln%q_dof(2,:) = soln%q_dof(2,:) - my_0
        soln%q_dof(3,:) = soln%q_dof(3,:) - my_0
      elseif(grad_function == 10) then
        soln%q_dof(1,:) = soln%q_dof(1,:) - (grid%y(:)**2)*grid%z(:)
        soln%q_dof(2,:) = soln%q_dof(2,:) - 2.0_dp*grid%y(:)*        &
                                                      grid%x(:)*grid%z(:)
        soln%q_dof(3,:) = soln%q_dof(3,:) - (grid%y(:)**2)*grid%x(:)
      elseif(grad_function == 11) then
        soln%q_dof(1,:) = soln%q_dof(1,:) - (grid%y(:)**3)*grid%z(:)
        soln%q_dof(2,:) = soln%q_dof(2,:) - 3.0_dp*(grid%y(:)**2)*    &
                                                       grid%x(:)*grid%z(:)
        soln%q_dof(3,:) = soln%q_dof(3,:) - (grid%y(:)**3)*grid%x(:)
      else
        error_qnode = .false.
      endif

      if(error_qnode) then
        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Processing errors for ',gradient

        variable_list(1) = 'error_gradx'
        variable_list(2) = 'error_grady'
        variable_list(3) = 'error_gradz'
        if(grad_function == 0) then
          variable_list(1) = 'error_gradient'
          variable_list(2) = 'null_function1'
          variable_list(3) = 'null_function2'
        elseif(grad_function == 9) then
          variable_list(1) = 'error_gradx'
          variable_list(2) = 'error_|gradient|'
          variable_list(3) = 'error_gradz'
        elseif( (grad_function >= 12) .and. (grad_function <= 14) ) then
          variable_list(1) = 'error_grad_radial'
          variable_list(2) = 'error_|gradient|'
          variable_list(3) = 'error_grad_theta'
        endif

!       Compute and print the number of values above some tolerance

        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Norms for on-processor nodes:'
        if(lmpi_master) write(f,*)
        if(lmpi_master) write(f,*) '  Norms for on-processor nodes:'
        nodesc = grid%nnodes0
        call etolvalues( grid%nnodes01, nodesc,        soln%q_dof,       &
                         soln%n_tot,    variable_list, grad_function, sf )
        call etolvalues( grid%nnodes01, nodesc,        soln%q_dof,       &
                         soln%n_tot,    variable_list, grad_function, f  )
        if(lmpi_nproc > 1  .and. check_all_nodes) then
        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Norms for all processor nodes:'
        if(lmpi_master) write(f,*)
        if(lmpi_master) write(f,*) '  Norms for all processor nodes:'
        nodesc = grid%nnodes01
        call etolvalues( grid%nnodes01, nodesc,        soln%q_dof,       &
                         soln%n_tot,    variable_list, grad_function, sf )
        call etolvalues( grid%nnodes01, nodesc,        soln%q_dof,       &
                         soln%n_tot,    variable_list, grad_function, f  )
        endif

!       Compute and print some norms of the errors in the gradient values

        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Norms for on-processor nodes:'
        if(lmpi_master) write(f,*)
        if(lmpi_master) write(f,*) '  Norms for on-processor nodes:'
        nodesc = grid%nnodes0
        call maxvalues( grid%nnodes01,  nodesc,                        &
                        grid%x,         grid%y,        grid%z,         &
                        soln%q_dof,     boundary_flag,                 &
                        soln%n_tot,     variable_list, sf, error_qnode )

        call maxvalues( grid%nnodes01,  nodesc,                        &
                        grid%x,         grid%y,        grid%z,         &
                        soln%q_dof,     boundary_flag,                 &
                        soln%n_tot,     variable_list, f,  error_qnode )

        if(lmpi_nproc > 1 .and. check_all_nodes) then
          if(lmpi_master) write(*,*)
          if(lmpi_master) write(*,*) '  Norms for all processor nodes:'
          if(lmpi_master) write(f,*)
          if(lmpi_master) write(f,*) '  Norms for all processor nodes:'
          nodesc = grid%nnodes01
          call maxvalues( grid%nnodes01,  nodesc,                        &
                          grid%x,         grid%y,        grid%z,         &
                          soln%q_dof,     boundary_flag,                 &
                          soln%n_tot,     variable_list, sf, error_qnode )
          call maxvalues( grid%nnodes01,  nodesc,                        &
                          grid%x,         grid%y,        grid%z,         &
                          soln%q_dof,     boundary_flag,                 &
                          soln%n_tot,     variable_list, f,  error_qnode )
        endif
      endif

!     Compute relative errors in gradients (certain functions)

      re_loop = size(grid%x)
      error_qnode = .true.
      if((grad_function >= 5) .and. (grad_function <= 8)) then
        x_d_max = -huge(1.0_dp)
        y_d_max = -huge(1.0_dp)
        z_d_max = -huge(1.0_dp)
        do ii = 1,re_loop
          x_d = x_linear + 2.0_dp*x_quadratic*grid%x(ii)
          y_d = y_linear + 2.0_dp*y_quadratic*grid%y(ii)
          z_d = z_linear + 2.0_dp*z_quadratic*grid%z(ii)

          x_d_max = max( x_d_max , abs(x_d) )
          y_d_max = max( y_d_max , abs(y_d) )
          z_d_max = max( z_d_max , abs(z_d) )
        enddo

!       Gather/broadcast maximums across all processors
        call lmpi_max_and_maxid(real(x_d_max,dp),proc_with_max)
        call lmpi_bcast(x_d_max,proc_with_max)
        call lmpi_max_and_maxid(real(y_d_max,dp),proc_with_max)
        call lmpi_bcast(y_d_max,proc_with_max)
        call lmpi_max_and_maxid(real(z_d_max,dp),proc_with_max)
        call lmpi_bcast(z_d_max,proc_with_max)

        do ii = 1,re_loop
          x_d = x_linear + 2.0_dp*x_quadratic*grid%x(ii)
          if(abs(x_d) > re_eps*x_d_max) then
            soln%q_dof(1,ii) = abs( soln%q_dof(1,ii)/x_d )
          else
            soln%q_dof(1,ii) = my_0
          endif
          y_d = y_linear + 2.0_dp*y_quadratic*grid%y(ii)
          if(abs(y_d) > re_eps*y_d_max) then
            soln%q_dof(2,ii) = abs( soln%q_dof(2,ii)/y_d )
          else
            soln%q_dof(2,ii) = my_0
          endif
          z_d = z_linear + 2.0_dp*z_quadratic*grid%z(ii)
          if(abs(z_d) > re_eps*z_d_max) then
            soln%q_dof(3,ii) = abs( soln%q_dof(3,ii)/z_d )
          else
            soln%q_dof(3,ii) = my_0
          endif
        enddo
      else
        error_qnode = .false.
      endif

      if(error_qnode) then
        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Processing relative errors : ',gradient

        variable_list(1) = 'relative_error_gradx'
        variable_list(2) = 'relative_error_grady'
        variable_list(3) = 'relative_error_gradz'

!       Compute and print the number of values above some tolerance

        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Norms for on-processor nodes:'
        if(lmpi_master) write(f,*)
        if(lmpi_master) write(f,*) '  Norms for on-processor nodes:'

        if(lmpi_master) write(*,*) '  ...ignoring relative error < ',re_eps,&
                                     '  of maximum velocity'
        if(lmpi_master) write(*,*) '  ...x:maximum velocity =',x_d_max
        if(lmpi_master) write(*,*) '  ...y:maximum velocity =',y_d_max
        if(lmpi_master) write(*,*) '  ...z:maximum velocity =',z_d_max
        if(lmpi_master) write(f,*) '  ...ignoring relative error < ',re_eps,&
                                     '  of maximum velocity'
        if(lmpi_master) write(f,*) '  ...x:maximum velocity =',x_d_max
        if(lmpi_master) write(f,*) '  ...y:maximum velocity =',y_d_max
        if(lmpi_master) write(f,*) '  ...z:maximum velocity =',z_d_max
        nodesc = grid%nnodes0
        call etolvalues( grid%nnodes01, nodesc,        soln%q_dof,       &
                         soln%n_tot,    variable_list, grad_function, sf )
        call etolvalues( grid%nnodes01, nodesc,        soln%q_dof,       &
                         soln%n_tot,    variable_list, grad_function, f  )

!       Compute and print some norms of the relative errors

        if(lmpi_master) write(*,*)
        if(lmpi_master) write(*,*) '  Norms for on-processor nodes:'
        if(lmpi_master) write(f,*)
        if(lmpi_master) write(f,*) '  Norms for on-processor nodes:'
        nodesc = grid%nnodes0
        call maxvalues( grid%nnodes01,  nodesc,                        &
                        grid%x,         grid%y,        grid%z,         &
                        soln%q_dof,     boundary_flag,                 &
                        soln%n_tot,     variable_list, sf, error_qnode )

        call maxvalues( grid%nnodes01,  nodesc,                        &
                        grid%x,         grid%y,        grid%z,         &
                        soln%q_dof,     boundary_flag,                 &
                        soln%n_tot,     variable_list, f,  error_qnode )
      endif

!     Reinitialize solution just so we have something in qnode

      select case(soln%eqn_set)
      case (compressible, generic_gas)
        call init(grid%cc, soln%eqn_set, grid%nnodes01, grid%nnodes01,         &
                  soln%q_dof, soln%n_turb, soln%turb,                          &
                  soln%amut, grid%slen, grid%x, grid%y, grid%z, grid%vol,      &
                  soln%n_tot,   soln%njac,     soln%ndim,          soln%n_q,   &
                  soln%n_grd,   grid%nnodes0, nml_path, soln%pressure_jac,     &
                  soln%enthalpy_ij )
      case (incompressible)
        call initi(grid%cc, soln%eqn_set, grid%nnodes01, grid%nnodes01,        &
                   soln%q_dof, soln%n_turb, soln%turb,                         &
                   soln%amut, grid%slen, grid%x, grid%y, grid%z, grid%vol,     &
                   soln%n_tot, soln%njac, soln%ndim, soln%n_q )
      end select

      if ( itime /= 0 ) then
        call initialize_backplanes(soln%n_tot,soln%n_turb,size(soln%q_dof,2),  &
                                   soln%q_dof,soln%qatn,soln%qatn1,soln%qatn2, &
                                   soln%qatn3,soln%qatn4,soln%turb,            &
                                   soln%turbatn,soln%turbatn1,soln%turbatn2,   &
                                   soln%turbatn3,soln%turbatn4,grid%x,grid%z,  &
                                   soln%eqn_set)
      endif

    enddo times_loop

    close(f)

    deallocate(boundary_flag)

    if(lmpi_master) write(*,*) ' Completed gradient_check...stopping'
    if(lmpi_master) write(*,*) ' ...test_gradient=',test_gradient
    if(lmpi_master) write(*,*) ' ...grad_function=',grad_function

  end subroutine gradient_check

!================================ MAXVALUES ==================================80
!
! Find and print some maximums and minimums
!
!=============================================================================80

  subroutine maxvalues( nodes, nodesc, x, y, z, variables, boundary_flag,      &
                        nvars, variable_list, f, error_qnode, h_effective)

    use lmpi,            only : lmpi_master, lmpi_conditional_stop
    use lmpi_app,        only : lmpi_collect_res
    use solve_box,       only : box_x_min, box_x_max, n_box,                   &
                                box_y_min, box_y_max,                          &
                                box_z_min, box_z_max, box_solve_fas_finest

    integer, intent(in) :: nodes, nodesc, f, nvars

    character(len=80), dimension(nvars), intent(in) :: variable_list

    real(dp), dimension(nodes),       intent(in) :: x, y, z
    real(dp), dimension(nvars,nodes), intent(in) :: variables
    integer,  dimension(nodes),       intent(in) :: boundary_flag
    logical,                          intent(in) :: error_qnode
    logical,  intent(in), optional  :: h_effective

    integer :: ivar, ic, j, nloop, n_checks
    integer :: loc_max(1), loc_min(1)
    integer, dimension(nodes) :: unity
    logical, dimension(nodes) :: mask

! beginNeverComplex
    real(dp), dimension(nodes) :: array

    real(dp) :: rms, rcount, scount, tcount, r1
    real(dp) :: x1, x2, y1, y2, z1, z2
    real(dp) :: vmax, xmax, ymax, zmax
    real(dp) :: vmin, xmin, ymin, zmin
    real(dp) :: dum1, dum2, dum3, dum4, dum5
! endNeverComplex

    continue

    !...nominal check (values close to y=0)
    x1 =-huge(1.0_dp)
    x2 = huge(1.0_dp)
    y1 =-0.000001_dp
    y2 = 0.000001_dp
    z1 =-huge(1.0_dp)
    z2 = huge(1.0_dp)

    if(box_solve_fas_finest) then
      if ( n_box > 1 ) call lmpi_conditional_stop(1,"Incompatible:n_box")
      x1 = box_x_min(1)
      x2 = box_x_max(1)
      y1 = box_y_min(1)
      y2 = box_y_max(1)
      z1 = box_z_min(1)
      z2 = box_z_max(1)
    endif

    unity(:) = 1

    if(lmpi_master) then
      write(f,*)
      write(f,*) '  Constrained search limits as below'
      write(f,'(3x,e14.5," =<    x     <= ",e14.5)') x1,x2
      write(f,'(3x,e14.5," =<    y     <= ",e14.5)') y1,y2
      write(f,'(3x,e14.5," =<    z     <= ",e14.5)') z1,z2
    endif

    dum1 = my_0
    dum2 = my_0
    dum3 = my_0
    dum4 = my_0
    dum5 = my_0

    !...determine nodes over the processors
    tcount    = real( nodesc , dp )
    call lmpi_collect_res( tcount, dum1, dum2, dum3, dum4, dum5 )

    nloop = nvars
    if(error_qnode) nloop = 3
    if(present(h_effective)) then
      nloop = 1
    endif
    variable_loop : do ivar = 1,nloop
      if(lmpi_master .and. error_qnode) then
        write(f,*)
        write(f,*) '  For error in variable with name -> ',&
                                  trim(variable_list(ivar))
      elseif(lmpi_master .and. present(h_effective)) then
        write(f,*)
        write(f,*) '  For error indicator with name -> ',&
                                  trim(variable_list(ivar))
      elseif(lmpi_master) then
        write(f,*)
        write(f,*) '  For variable with name -> ',         &
                                  trim(variable_list(ivar))
      endif

!     Set n_checks for unconstrained norms except if
!     looking at error components

      n_checks = 1
      if(error_qnode) n_checks = 4
      if(present(h_effective)) then
        n_checks = 4
      endif
      constrained_loop : do ic=1,n_checks
        if(ic == 1) then
          mask(:) = .false.
          mask(1:nodesc) = .true.
          if(lmpi_master) write(f,*) '  Unconstrained norms.'
        elseif(ic == 2) then
          if(lmpi_master) write(f,*) '  Constrained norms.'
          mask(:) = .false.
          do j=1,nodesc
            mask(j) = (real(x(j),dp) >= x1) .and. (real(x(j),dp) <= x2) &
                .and. (real(y(j),dp) >= y1) .and. (real(y(j),dp) <= y2) &
                .and. (real(z(j),dp) >= z1) .and. (real(z(j),dp) <= z2)
          enddo
        elseif(ic==3) then
          if(lmpi_master) write(f,*)                  &
          '  Unconstrained norms : excluding boundaries.'
          mask(:) = .false.
          mask(1:nodesc) = .true.
          do j=1,nodesc
            if(boundary_flag(j) > 0) mask(j) = .false.
          enddo
        elseif(ic==4) then
          if(lmpi_master) write(f,*)                &
          '  Constrained norms : excluding boundaries.'
          mask(:) = .false.
          do j=1,nodesc
            mask(j) = (real(x(j),dp) >= x1) .and. (real(x(j),dp) <= x2) &
                .and. (real(y(j),dp) >= y1) .and. (real(y(j),dp) <= y2) &
                .and. (real(z(j),dp) >= z1) .and. (real(z(j),dp) <= z2)
          enddo
          do j=1,nodesc
            if(boundary_flag(j) > 0) mask(j) = .false.
          enddo
        endif !end ic conditional

        !...determine nodes over the processors meeting search criteria
        scount    = real( sum( unity(:) , mask=mask(:) ) , dp )
        call lmpi_collect_res( scount, dum1, dum2, dum3, dum4, dum5 )

        if(nint(scount) > 0) then
          !...determine nodes on each processor
          rcount    = real( sum( unity(:) , mask=mask(:) ) , dp )
          if(nint(rcount) > 0) then
            array(:) = real( variables(ivar,:) , dp )
            rms      = sum( array(:)*array(:) , mask=mask(:) )
            r1       = sum( abs(array(:))     , mask=mask(:) )
            loc_max  = maxloc( variables(ivar,:) , mask=mask(:) )
            loc_min  = minloc( variables(ivar,:) , mask=mask(:) )

            !...change sign of minimum for compatibility with lmpi_collect_res
            vmin     = - array( loc_min(1) )
            xmin     = real( x( loc_min(1) ) , dp )
            ymin     = real( y( loc_min(1) ) , dp )
            zmin     = real( z( loc_min(1) ) , dp )

            vmax     =   array( loc_max(1) )
            xmax     = real( x( loc_max(1) ) , dp )
            ymax     = real( y( loc_max(1) ) , dp )
            zmax     = real( z( loc_max(1) ) , dp )
          else

            rms      = my_0
            r1       = my_0

            vmin     =-huge(1.0_dp)
            xmin     = real( x( 1 ) , dp )
            ymin     = real( y( 1 ) , dp )
            zmin     = real( z( 1 ) , dp )

            vmax     =-huge(1.0_dp)
            xmax     = real( x( 1 ) , dp )
            ymax     = real( y( 1 ) , dp )
            zmax     = real( z( 1 ) , dp )
          endif

          call lmpi_collect_res( rms,  rcount, vmin,  &
                                 xmin, ymin,   zmin )

          call lmpi_collect_res( r1, dum2, vmax,  &
                                 xmax, ymax, zmax )

          if(lmpi_master) rms = sqrt( rms / rcount )
          if(lmpi_master) r1  = r1 / rcount

          if((error_qnode .or. present(h_effective)).and. lmpi_master) then
            if(abs(vmax) <= abs(vmin)) then
              write(f,'("   |error| max   =",e22.15,"  at x,y,z=",3e16.7)')    &
              abs(vmin), xmin, ymin, zmin
            else
              write(f,'("   |error| max   =",e22.15,"  at x,y,z=",3e16.7)')    &
              abs(vmax), xmax, ymax, zmax
            endif
            write(f,'("   error rms     =",e22.15,"  values found=",i12)')     &
            rms,nint( rcount )
            write(f,'("   error L1      =",e22.15,"  values found=",i12)')     &
            r1,nint( rcount )
            if ( lmpi_nproc == 1 ) &
            write(f,'("   ..loc_min=",i12,"  loc_max=",i12)') loc_min,loc_max
          elseif(lmpi_master) then
            write(f,'("   minimum =",e22.15,"  at x,y,z=",3e16.7)')            &
            -vmin, xmin, ymin, zmin
            write(f,'("   maximum =",e22.15,"  at x,y,z=",3e16.7)')            &
            vmax, xmax, ymax, zmax
            write(f,'("   rms     =",e22.15,"  values found=",i12)')           &
            rms,nint( rcount )
            if ( lmpi_nproc == 1 ) &
            write(f,'("   ..loc_min=",i12,"  loc_max=",i12)') loc_min,loc_max
          endif
        else
          if(lmpi_master)                                                    &
          write(f,'("   no data found...sum of all nodes=",i10)') nint( tcount )
        endif

      enddo constrained_loop

    enddo variable_loop

  end subroutine maxvalues

!================================ ETOLVALUES =================================80
!
! Find and print some variations above a tolerance
!
!=============================================================================80

  subroutine etolvalues( nodes, nodesc, variables,                             &
                         nvars, variable_list, grad_function, f )

    use lmpi,     only : lmpi_master
    use lmpi_app, only : lmpi_collect_res

    integer,                                   intent(in) :: nodes, nodesc, f
    integer,                                   intent(in) :: nvars
    integer,                                   intent(in) :: grad_function
    character(len=80), dimension(nvars),       intent(in) :: variable_list
    real(dp),          dimension(nvars,nodes), intent(in)  :: variables

    integer :: ivar, ic, ii
    integer :: var_start, var_end

! beginNeverComplex
    real(dp), dimension(nodes)     :: array
    real(dp)                       :: tol, tol_start
    real(dp)                       :: rcount, scount
    real(dp)                       :: d1, d2, d3, d4, d5
! endNeverComplex

    continue

    d1 = my_0
    d2 = my_0
    d3 = my_0
    d4 = my_0
    d5 = my_0

    scount         = real( nodesc , dp )
    call lmpi_collect_res( scount, d1, d2, d3, d4, d5 )
    if(lmpi_master) write(f,*) '  Number of considered nodes=',nint(scount)

    var_start = 4
    var_end   = 4
    tol_start = 0.1_dp

    if (grad_function == -1) then
      tol_start = 0.1_dp
    elseif ( grad_function == 0 ) then
      tol_start = my_1
    elseif ( ( grad_function >= 1 ) .and. ( grad_function <= 14 ) ) then
      var_start = 1
      var_end   = 3
    else
      write(*,*) ' ...stopping in etolvalues'
      write(*,*) ' ...inadmissible grad_function=',grad_function
      call lmpi_conditional_stop(1,"etolvalues")
    endif

    variable_loop : do ivar = var_start,var_end
      if(lmpi_master) then
        write(f,*)
        write(f,*) '  For variable with name -> ',trim(variable_list(ivar))
      endif

      tol = tol_start*100000.0_dp
      array(:) = real( variables(ivar,:) , dp )
      do ic=1,10

        !...count occurrences above tolerance
        rcount = my_0
        do ii=1,nodesc
          if( abs( array(ii) ) >= tol ) rcount = rcount + my_1
        enddo

        call lmpi_collect_res( rcount, d1, d2, d3, d4, d5 )

        if(lmpi_master .and. ic <= 5)                                        &
        write(f,'("   Absolute errors above ",f12.1," =",i10,"  %=",f10.5)') &
         tol, nint( rcount ), real( 100._dp*rcount/scount , dp )
        if(lmpi_master .and. ic > 5)                                         &
        write(f,'("   Absolute errors above ",f12.5," =",i10,"  %=",f10.5)') &
         tol, nint( rcount ), real( 100._dp*rcount/scount , dp )
        tol = tol/10._dp

      enddo

    enddo variable_loop

  end subroutine etolvalues

!=================================== QNODE_FUNCTION ==========================80
!
! Set soln%q_dof according to grad_function.
!
!=============================================================================80

  subroutine qnode_function( grid, soln, grad_function,                        &
                             ierr, write_info )

    use grid_types,             only : grid_type
    use solution_types,         only : soln_type
    use info_depr,              only : twod

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    integer, intent(in)  :: grad_function
    integer, intent(out) :: ierr

    logical, intent(in), optional :: write_info

    integer :: ii

    real(dp), dimension(4) :: q_exact

  continue

    x_linear = my_0
    y_linear = my_0
    z_linear = my_0
    x_quadratic = my_0
    y_quadratic = my_0
    z_quadratic = my_0

    ierr = 0
    if(present(write_info)) then

      write(*,*)

      if(grad_function == 0) then
        write(*,*) '  Computing gradients of distance function'
        write(f,*) '  Computing gradients of distance function'
      elseif(grad_function == -1) then
        write(*,*) '  Computing gradients of constant function'
        write(f,*) '  Computing gradients of constant function'
      elseif(grad_function == 1) then
        write(*,*) '  Computing gradients of linear function in x'
        write(f,*) '  Computing gradients of linear function in x'
      elseif(grad_function == 2) then
        if(twod) then
          write(*,*) '  Computing gradients of constant function in y'
          write(f,*) '  Computing gradients of constant function in y'
        else
          write(*,*) '  Computing gradients of linear function in y'
          write(f,*) '  Computing gradients of linear function in y'
        endif
      elseif(grad_function == 3) then
        write(*,*) '  Computing gradients of linear function in z'
        write(f,*) '  Computing gradients of linear function in z'
      elseif(grad_function == 4) then
        if(twod) then
          write(*,*) '  Computing gradients of linear function in x/z'
          write(f,*) '  Computing gradients of linear function in x/z'
        else
          write(*,*) '  Computing gradients of linear function in x/y/z'
          write(f,*) '  Computing gradients of linear function in x/y/z'
        endif
      elseif(grad_function == 5) then
        write(*,*) '  Computing gradients of quadratic function in x'
        write(f,*) '  Computing gradients of quadratic function in x'
      elseif(grad_function == 6) then
        if(twod) then
          write(*,*) '  Computing gradients of constant function in y'
          write(f,*) '  Computing gradients of constant function in y'
        else
          write(*,*) '  Computing gradients of quadratic function in y'
          write(f,*) '  Computing gradients of quadratic function in y'
        endif
      elseif(grad_function == 7) then
        write(*,*) '  Computing gradients of quadratic function in z'
        write(f,*) '  Computing gradients of quadratic function in z'
      elseif(grad_function == 8) then
        if(twod) then
          write(*,*) '  Computing gradients of quadratic function in x/z'
          write(f,*) '  Computing gradients of quadratic function in x/z'
        else
          write(*,*) '  Computing gradients of quadratic function in x/y/z'
          write(f,*) '  Computing gradients of quadratic function in x/y/z'
        endif

      elseif(grad_function == 9) then
        write(*,*) '  Computing gradients of radial function in x/z'
        write(f,*) '  Computing gradients of radial function in x/z'

      elseif(grad_function == 12) then
        write(*,*) '  Computing gradients of cylinder flow : p in x/z'
        write(f,*) '  Computing gradients of cylinder flow : p in x/z'

      elseif(grad_function == 13) then
        write(*,*) '  Computing gradients of cylinder flow : u in x/z'
        write(f,*) '  Computing gradients of cylinder flow : u in x/z'

      elseif(grad_function == 14) then
        write(*,*) '  Computing gradients of cylinder flow : w in x/z'
        write(f,*) '  Computing gradients of cylinder flow : w in x/z'

      elseif(grad_function == 10) then
        write(*,*) '  Computing gradients of symmetric function in y'
        write(f,*) '  Computing gradients of symmetric function in y'
        if(twod) then
          write(*,*) '  This option not allowed in 2-D mode...stopping'
          ierr = 1
        endif

      elseif(grad_function == 11) then
        write(*,*) '  Computing gradients of antisymmetric function in y'
        write(f,*) '  Computing gradients of antisymmetric function in y'
        if(twod) then
          write(*,*) '  This option not allowed in 2-D mode...stopping'
          ierr = 1
        endif

      else
        write(*,*) '  grad_function not programmed...stopping'
        write(*,*) '  ....grad_function=',grad_function
        ierr = 1
      endif !end grad_function conditional

    endif

    if(present(write_info)) return

    if(grad_function == 0) then
      soln%q_dof(1,:) = grid%slen(:)
    elseif(grad_function == -1) then
      soln%q_dof(1,:) = my_1
    elseif(grad_function == 1) then
      x_linear = my_1
      soln%q_dof(1,:) = x_linear*grid%x(:)
      if(symmetry_x_flag) x_linear = my_0
    elseif(grad_function == 2) then
      y_linear = my_1
      if(twod) y_linear = my_0
      soln%q_dof(1,:) = y_linear*grid%y(:)
      if(symmetry_y_flag) y_linear = my_0
    elseif(grad_function == 3) then
      z_linear = my_1
      soln%q_dof(1,:) = z_linear*grid%z(:)
      if(symmetry_z_flag) z_linear = my_0
    elseif(grad_function == 4) then
      x_linear = 3.0_dp
      y_linear = 2.0_dp
      if(twod) y_linear = my_0
      z_linear = my_1
      if(symmetry_x_flag) x_linear = my_0
      if(symmetry_y_flag) y_linear = my_0
      if(symmetry_z_flag) z_linear = my_0
      soln%q_dof(1,:) = x_linear*grid%x(:) + &
                        y_linear*grid%y(:) + &
                        z_linear*grid%z(:)
    elseif(grad_function == 5) then
      x_quadratic = my_1
      soln%q_dof(1,:) = x_quadratic*grid%x(:)*grid%x(:)
    elseif(grad_function == 6) then
      y_quadratic = my_1
      if(twod) y_quadratic = my_0
      soln%q_dof(1,:) = y_quadratic*grid%y(:)*grid%y(:)
    elseif(grad_function == 7) then
      z_quadratic = my_1
      soln%q_dof(1,:) = z_quadratic*grid%z(:)*grid%z(:)
    elseif(grad_function == 8) then
      x_quadratic = 3.0_dp
      y_quadratic = 2.0_dp
      if(twod) y_quadratic = my_0
      z_quadratic = my_1
      soln%q_dof(1,:) = x_quadratic*grid%x(:)*grid%x(:)  + &
                        y_quadratic*grid%y(:)*grid%y(:)  + &
                        z_quadratic*grid%z(:)*grid%z(:)
    elseif(grad_function == 9) then
      soln%q_dof(1,:) = sqrt( grid%x(:)*grid%x(:) + &
                              grid%z(:)*grid%z(:) )
    elseif( (grad_function >= 12) .and. (grad_function <= 14) ) then
      do ii = 1,grid%nnodes0
        call exact_cyl_2d(grid%x(ii), grid%z(ii), q_exact, 0, f )
        soln%q_dof(1,ii) = q_exact(i_q)
      enddo
    elseif(grad_function == 10) then
      soln%q_dof(1,:) = (grid%y(:)**2)*grid%x(:)*grid%z(:)
    elseif(grad_function == 11) then
      soln%q_dof(3,:) = (grid%y(:)**3)*grid%x(:)*grid%z(:)
    else
      write(*,*) '  grad_function not programmed...stopping'
      write(*,*) '  ....computing qnode.'
      write(*,*) '  ....grad_function=',grad_function
      ierr = 2
    endif !end grad_function conditional

  end subroutine qnode_function

!=============================== RECONSTRUCT_CHECK ===========================80
!
! Reconstruct the solution at the edge midpoints and check errors.
!
!=============================================================================80

  subroutine reconstruct_check( grid, soln )

    use info_depr,              only : mixed, twod
    use grid_types,             only : grid_type
    use solution_types,         only : soln_type

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: grad_function_check, ifunction, igradient, ib
    integer :: gradient_methods

    character(len=80) :: variable, gradient

    integer, dimension(:), allocatable :: boundary_flag

  continue

!   Allocate temporary array : boundary_flag

    allocate(boundary_flag(grid%nnodes01))

!   Set boundary_flag >= 1 for boundary nodes

    boundary_flag(:) = 0
    do ib = 1,grid%nbound
      call mark_boundaries( boundary_flag,      grid%nnodes01,      &
                            grid%bc(ib)%ibc,    grid%bc(ib)%nbnode, &
                            grid%bc(ib)%ibnode, twod )
    end do


    variable = 'inviscid'

!   Check errors associated with reconstruction:
!   ...unweighted least-squares
!   ...weighted least-square (if available)

    gradient_methods = 2
    if(.not.mixed) gradient_methods = 1
    do igradient=1,gradient_methods

      if(igradient == 1) then
        gradient = 'least-squares'
      else
        gradient = 'weighted-least-squares'
      endif

      do ifunction=1,2

        if(ifunction == 1) then
          grad_function_check = 4
          tolerance = 1.0e-12_dp
        else
          grad_function_check = 8
          tolerance = huge(1.0_dp)
        endif

        if(lmpi_master) then
          write(*,*)
          write(*,*) ' Test edge_reconstruction:'
          write(*,*) ' ...grad_function=',grad_function_check
          write(*,*) ' ...gradient=',gradient
        endif

        call qnode_function( grid, soln, grad_function_check, ierr )

        call grad_variable(grid, soln, variable, gradient)

        call check_edge_reconstruction(                                        &
                    grid%nnodes01, grid%nedgeloc,                              &
                    grid%nedgeloc_2d,                                          &
                    soln%gradx,   soln%grady,    soln%gradz,    grid%eptr,     &
                    soln%q_dof,   grid%x,        grid%y,        grid%z,        &
                    soln%n_tot,   soln%n_grd,    ierr, boundary_flag)

        call lmpi_conditional_stop(ierr,"reconstruct_check")
      enddo !function loop
    enddo !gradient loop

    deallocate(boundary_flag)

  end subroutine reconstruct_check

!=========================== CHECK_EDGE_RECONSTRUCTION =======================80
!
! This routine checks edge reconstruction.
!
!=============================================================================80

  subroutine check_edge_reconstruction(                                        &
                  nnodes01, nedgeloc, nedgeloc_2d, gradx, grady,               &
                  gradz, eptr, qnode, x, y, z,                                 &
                  n_tot, n_grd, ierr, boundary_flag)

    use info_depr,          only : twod, kappa_umuscl

    integer, intent(in)  :: n_tot, n_grd
    integer, intent(in)  :: nnodes01
    integer, intent(in)  :: nedgeloc, nedgeloc_2d
    integer, intent(out) :: ierr

    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradz

    integer,   dimension(2,nedgeloc),        intent(in)    :: eptr
    real(dp),  dimension(n_tot,nnodes01),    intent(in)    :: qnode
    real(dp),  dimension(nnodes01),          intent(in)    :: x, y, z

    integer, dimension(nnodes01), intent(in) :: boundary_flag

    integer     :: n, node1, node2, i, itotal, check
    integer     :: nedge_flux_eval

    real(dp) :: value_left, value_right, value_check
    real(dp) :: rx, ry, rz, term1, term2, norm, norm_first

    real(dp) :: xmean, ymean, zmean
    real(dp) :: x_left,  y_left,  z_left
    real(dp) :: x_right, y_right, z_right
    real(dp) ::  dq_edge, dq_left, dq_right

    real(dp) :: error_at_max, t1, t2
    real(dp) :: x_at_max, y_at_max, z_at_max, check_at_max
    real(dp) ::  x_left_at_max,  y_left_at_max,  z_left_at_max
    real(dp) :: x_right_at_max, y_right_at_max, z_right_at_max
    real(dp) ::  recon_left_at_max
    real(dp) :: recon_right_at_max
    real(dp) ::  value_left_at_max
    real(dp) :: value_right_at_max
    real(dp), dimension(3) :: grad_left_at_max, grad_right_at_max, &
                              grad_at_max

    real(dp), dimension(2) :: error, error_first

    integer, dimension(2) :: flag_at_max

    logical, dimension(2) :: extremum, extremum_at_max

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

    ierr   = 0
    itotal = 0
    norm       = my_0
    norm_first = my_0

    error_at_max = -huge(1.0_dp)
    max_first    = -huge(1.0_dp)

    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    else
      nedge_flux_eval = nedgeloc
    end if

!   Loop over all the edges and calculate the inviscid fluxes
!   (in the 2D case, this loop contains edges on only one y=constant plane)

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

!     Left/right/average points.

      x_left = x(node1)
      y_left = y(node1)
      z_left = z(node1)

      x_right = x(node2)
      y_right = y(node2)
      z_right = z(node2)

      xmean = my_half*( x_left + x_right )
      ymean = my_half*( y_left + y_right )
      zmean = my_half*( z_left + z_right )

      value_check = x_linear*xmean       &
                  + y_linear*ymean       &
                  + z_linear*zmean       &
                  + x_quadratic*xmean**2 &
                  + y_quadratic*ymean**2 &
                  + z_quadratic*zmean**2

      error_first(1) = abs( value_check -  qnode(1,node1) )
      error_first(2) = abs( value_check -  qnode(1,node2) )
      max_first = max( error_first(1) , max_first )
      max_first = max( error_first(2) , max_first )

      term1 = my_half*kappa_umuscl
      term2 = (my_1 - kappa_umuscl)

!     Reconstruct to "left" side of face.

      rx  = xmean - x_left
      ry  = ymean - y_left
      rz  = zmean - z_left

!     No limiting

      value_left = qnode(1,node1) + term1*(qnode(1,node2) - qnode(1,node1))    &
                                + term2*gradx(1,node1)*rx                      &
                                + term2*grady(1,node1)*ry                      &
                                + term2*gradz(1,node1)*rz

!     Minmod limiting

      dq_left = gradx(1,node1)*rx + grady(1,node1)*ry + gradz(1,node1)*rz

      dq_edge = ( qnode(1,node2) - qnode(1,node1) ) / 2.0_dp

      extremum(1) = .false.
      if(dq_edge*dq_left < my_0) extremum(1) = .true.

!!!!      dq = minmods(dq_left, dq_edge)  !minmod limiter

!!!!      value_left = qnode(1,node1) + dq

!     Reconstruct to "right" side of face.

      rx = (xmean - x_right)
      ry = (ymean - y_right)
      rz = (zmean - z_right)

!     No limiting

      value_right = qnode(1,node2) + term1*(qnode(1,node1) - qnode(1,node2))   &
                                + term2*gradx(1,node2)*rx                      &
                                + term2*grady(1,node2)*ry                      &
                                + term2*gradz(1,node2)*rz

!     Minmod limiting

      dq_right = gradx(1,node2)*rx + grady(1,node2)*ry + gradz(1,node2)*rz

      dq_edge = - dq_edge

      extremum(2) = .false.
      if(dq_edge*dq_right < my_0) extremum(2) = .true.

      dq_right = minmods(dq_right, dq_edge) !apply minmod limiter

!!!!      value_right = qnode(1,node2) + dq

      error(1)  = abs(  value_left - value_check )
      error(2)  = abs( value_right - value_check )

      do check = 1,2

        itotal     = itotal + 1
        norm       = norm       + abs(       error(check) )
        norm_first = norm_first + abs( error_first(check) )

        if(tolerance < 1.0e-11_dp) then

          !...small tolerance...check versus tolerance.

          if(error(check) > tolerance) then
            ierr         = ierr + 1
          endif

        else

          !...big tolerance...check versus first order.
          if((error(check) > error_first(check)) .and.        &
                                     .not.extremum(check)) then
            ierr         = ierr + 1
          else
            error(check) = my_0
          endif

        endif

        if(error(check) > error_at_max) then
          error_at_max       = error(check)
          x_at_max           = xmean
          y_at_max           = ymean
          z_at_max           = zmean
          x_left_at_max      = x_left
          y_left_at_max      = y_left
          z_left_at_max      = z_left
          x_right_at_max     = x_right
          y_right_at_max     = y_right
          z_right_at_max     = z_right
          recon_left_at_max  =  value_left
          recon_right_at_max = value_right
          check_at_max       = value_check
          value_left_at_max  = qnode(1,node1)
          value_right_at_max = qnode(1,node2)
          grad_left_at_max(1)  = gradx(1,node1)
          grad_left_at_max(2)  = grady(1,node1)
          grad_left_at_max(3)  = gradz(1,node1)
          grad_right_at_max(1) = gradx(1,node2)
          grad_right_at_max(2) = grady(1,node2)
          grad_right_at_max(3) = gradz(1,node2)
          flag_at_max(1)       = boundary_flag(node1)
          flag_at_max(2)       = boundary_flag(node2)
          extremum_at_max(:)   = extremum(:)
        endif
      enddo

    end do edge_loop

    i = ierr              ; call lmpi_reduce(i,ierr)
    i = itotal            ; call lmpi_reduce(i,itotal)
    t1 = norm             ; call lmpi_reduce(t1,norm)
    t1 = norm_first       ; call lmpi_reduce(t1,norm_first)
    norm       =       norm / real( itotal , dp )
    norm_first = norm_first / real( itotal , dp )

    call lmpi_bcast(max_first)

!   Gather/broadcast maximums across all processors
    call lmpi_max_and_maxid(real(error_at_max,dp),proc_with_max)
    call lmpi_bcast(      error_at_max,proc_with_max)
    call lmpi_bcast(          x_at_max,proc_with_max)
    call lmpi_bcast(          y_at_max,proc_with_max)
    call lmpi_bcast(          z_at_max,proc_with_max)
    call lmpi_bcast(     x_left_at_max,proc_with_max)
    call lmpi_bcast(     y_left_at_max,proc_with_max)
    call lmpi_bcast(     z_left_at_max,proc_with_max)
    call lmpi_bcast(    x_right_at_max,proc_with_max)
    call lmpi_bcast(    y_right_at_max,proc_with_max)
    call lmpi_bcast(    z_right_at_max,proc_with_max)
    call lmpi_bcast( recon_left_at_max,proc_with_max)
    call lmpi_bcast(recon_right_at_max,proc_with_max)
    call lmpi_bcast(      check_at_max,proc_with_max)
    call lmpi_bcast( value_left_at_max,proc_with_max)
    call lmpi_bcast(value_right_at_max,proc_with_max)
    call lmpi_bcast(  grad_left_at_max,proc_with_max)
    call lmpi_bcast( grad_right_at_max,proc_with_max)
    call lmpi_bcast(       flag_at_max,proc_with_max)
    call lmpi_bcast(   extremum_at_max,proc_with_max)

    if(lmpi_master) then
      write(*,*) ' ...linear coefficients(x/y/z)   =',  &
                      x_linear,y_linear,z_linear
      write(*,*) ' ...quadratic coefficients(x/y/z)=',  &
                      x_quadratic,y_quadratic,z_quadratic
      write(*,*) ' ...kappa_umuscl=',kappa_umuscl
      write(*,*)
      write(*,*) ' ...Norms of reconstruction errors:'
      write(*,*) ' ...L1 norm (reconstruction)=',real(norm,dp)
      write(*,*) ' ...L1 norm    (first order)=',real(norm_first,dp)
      write(*,*) ' ...maximum (reconstruction)=',real(error_at_max,dp)
      write(*,*) ' ...maximum    (first order)=',real(max_first,dp)
      write(*,*) ' ...tolerance=',real(tolerance,dp)
    endif
    if(lmpi_master .and. ierr > 0) then
      write(*,*)
      write(*,*) ' ...points above tolerance=',ierr
      write(*,*) ' ...points reconstructed=',itotal
      write(*,*)
      write(*,*) ' ...max error =',real(error_at_max,dp)
      write(*,*)
      write(*,*) ' ...boundary_flag(left/right)=', flag_at_max(:)
      write(*,*)
      write(*,*) ' ...extremum(left/right)=',extremum_at_max(:)
      write(*,*)
      write(*,*) ' ...x(interface)=',real(x_at_max,dp)
      write(*,*) ' ...y(interface)=',real(y_at_max,dp)
      write(*,*) ' ...z(interface)=',real(z_at_max,dp)
      write(*,*)
      write(*,*) ' ...x(left)=',real(x_left_at_max,dp)
      write(*,*) ' ...y(left)=',real(y_left_at_max,dp)
      write(*,*) ' ...z(left)=',real(z_left_at_max,dp)
      write(*,*)
      write(*,*) ' ...x(right)=',real(x_right_at_max,dp)
      write(*,*) ' ...y(right)=',real(y_right_at_max,dp)
      write(*,*) ' ...z(right)=',real(z_right_at_max,dp)
      write(*,*)
      write(*,*) ' ...node_value( left)=',real( value_left_at_max,dp)
      write(*,*) ' ...node_value(right)=',real(value_right_at_max,dp)
      write(*,*) ' ...interface value  =',real(      check_at_max,dp)
      write(*,*)
      write(*,*) ' ...reconstruction ( left)=',real( recon_left_at_max,dp)
      write(*,*) ' ...reconstruction (right)=',real(recon_right_at_max,dp)
      write(*,*)
      t1 = real(abs( check_at_max -  recon_left_at_max ),dp)
      t2 = real(abs( check_at_max - recon_right_at_max ),dp)
      write(*,*) ' ...reconstruction error  left (second order)=',t1
      write(*,*) ' ...reconstruction error right (second order)=',t2
      write(*,*)
      t1 = real(abs( check_at_max -  value_left_at_max ),dp)
      t2 = real(abs( check_at_max - value_right_at_max ),dp)
      write(*,*) ' ...reconstruction error  left (first order)=',t1
      write(*,*) ' ...reconstruction error right (first order)=',t2
      write(*,*)
      write(*,*) ' ...grad_left =',real(grad_left_at_max,dp)
      write(*,*) ' ...grad_right=',real(grad_right_at_max,dp)
      write(*,*)
      grad_left_at_max(1)  = x_linear + 2.0_dp*x_quadratic*x_left_at_max
      grad_left_at_max(2)  = y_linear + 2.0_dp*y_quadratic*y_left_at_max
      grad_left_at_max(3)  = z_linear + 2.0_dp*z_quadratic*z_left_at_max
      grad_right_at_max(1) = x_linear + 2.0_dp*x_quadratic*x_right_at_max
      grad_right_at_max(2) = y_linear + 2.0_dp*y_quadratic*y_right_at_max
      grad_right_at_max(3) = z_linear + 2.0_dp*z_quadratic*z_right_at_max
      write(*,*) ' ...grad_left (exact)     =',real( grad_left_at_max,dp)
      write(*,*) ' ...grad_right(exact)     =',real(grad_right_at_max,dp)
      grad_at_max(1)  = x_linear + 2.0_dp*x_quadratic*x_at_max
      grad_at_max(2)  = y_linear + 2.0_dp*y_quadratic*y_at_max
      grad_at_max(3)  = z_linear + 2.0_dp*z_quadratic*z_at_max
      write(*,*) ' ...grad_interface (exact)=',real( grad_at_max,dp)

    endif

  end subroutine check_edge_reconstruction

!   Statements to include the functions that are to be inlined.
!   This is necessary because not all compilers can inline
!   functions that are in a different module.
!   N.B.: The order of the statements must reflect
!         how they are nested in the routine(s)
!         they are invoked from.

  include 'minmods.f90'

end module test_reconstruction


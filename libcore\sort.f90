module sort

  implicit none

  private

  public :: set_up_lookup, lookup

  public :: heap_sort
  public :: binary_search
  public :: binary_search_less
  public :: small_sort
  public :: idx_quicksort
  public :: binary_search_double_eps

  public :: sort_uniq

  public :: reverse

  interface heap_sort
    module procedure heap_sort_integer
    module procedure heap_sort_integer484
    module procedure heap_sort_double
    module procedure heap_sort_no_index_integer
    module procedure heap_sort_no_index_integer48
    module procedure heap_sort_no_idx_real888
  end interface

  interface binary_search
    module procedure binary_search_integer
    module procedure binary_search_integer488
    module procedure binary_search_double
  end interface

  interface small_sort
    module procedure small_sort_integer
    module procedure small_sort_double
  end interface

  interface reverse
    module procedure reverse_integer
    module procedure reverse_double
  end interface

  interface idx_quicksort
    module procedure idx_quicksort_int
    module procedure idx_quicksort_double
  end interface

  public :: selection

  public :: growable_lookup_type, growable_lookup_extend

  type growable_lookup_type
    integer :: linear_sz, linear_ct
    integer :: sorted_sz, sorted_ct
    integer, dimension(:), pointer :: linear
    integer, dimension(:), pointer :: sorted
  end type

contains

!============================= SET_UP_LOOKUP =================================80
!
! Set up sorted list to do inverse lookups
!
!=============================================================================80
  subroutine set_up_lookup( nlocal, l2g, sorted_global, sorted_local )

    integer,               intent(in ) :: nlocal
    integer, dimension(:), intent(in ) :: l2g
    integer, dimension(:), intent(out) :: sorted_local, sorted_global

    integer :: i

  continue

    do i = 1, nlocal
      sorted_global(i) = l2g(i)
    end do

    call heap_sort( nlocal, sorted_global, sorted_local )

    do i = 1, nlocal
      sorted_global(i) = l2g( sorted_local(i) )
    end do

  end subroutine set_up_lookup

!============================= LOOKUP ========================================80
!
! inverse lookup from sorted list with index map
!
!=============================================================================80
  pure &
  function lookup( global_index, nsorted, sorted_global, sorted_local )

    integer                                 :: lookup
    integer,                     intent(in) :: global_index
    integer,                     intent(in) :: nsorted
    integer, dimension(nsorted), intent(in) :: sorted_global, sorted_local

    integer :: position_in_list

    continue

    position_in_list = binary_search(nsorted,sorted_global,global_index)

    if ( position_in_list > 0 ) then ! found
      lookup = sorted_local(position_in_list)
    else ! not found
      lookup = 0
    end if

  end function lookup

!==================================== HEAP_SORT_INTEGER ======================80
!
! integer heap sort for ascending order
!
!=============================================================================80

  subroutine heap_sort_integer(n,arrin,indx)

    integer,                  intent(in) :: n
    integer,    dimension(n), intent(in) :: arrin
    integer,    dimension(n), intent(out):: indx

    integer :: j, l, ir, indxt, i
    integer :: q

    continue

    if (n <= 0) return

    do j=1,n
      indx(j) = j
    end do

    if ( n == 1) return

    l = n/2 + 1
    ir= n
    do
      if(l > 1)then
        l=l-1
        indxt = indx(l)
        q     = arrin(indxt)
      else
        indxt = indx(ir)
        q     = arrin(indxt)
        indx(ir) = indx(1)
        ir = ir - 1
        if(ir == 1)then
          indx(1) = indxt
          return
        end if
      end if
      i=l
      j=l+l
      do
        if(j > ir) exit
        if(j < ir) then ! separate if to avoid array bounds issues
          if (arrin(indx(j)) < arrin(indx(j+1))) j = j+1
        end if
        if(q < arrin(indx(j)))then
          indx(i) = indx(j)
          i = j
          j = j+j
        else
          j = ir+1
        end if
      end do
      indx(i) = indxt
    end do

  end subroutine heap_sort_integer

!==================================== HEAP_SORT_INTEGER484 ===================80
!
! integer heap sort for ascending order
!
!=============================================================================80

  subroutine heap_sort_integer484(n,arrin,indx)

    use kinddefs, only : system_i4, system_i8

    integer(system_i4),               intent(in) :: n
    integer(system_i8), dimension(n), intent(in) :: arrin
    integer(system_i4), dimension(n), intent(out):: indx

    integer :: j, l, ir, indxt, i
    integer(system_i8) :: q

    continue

    if (n <= 0) return

    do j=1,n
      indx(j) = j
    end do

    if (n == 1) return

    l = n/2 + 1
    ir= n
    do
      if(l > 1)then
        l=l-1
        indxt = indx(l)
        q    = arrin(indxt)
      else
        indxt = indx(ir)
        q     = arrin(indxt)
        indx(ir) = indx(1)
        ir = ir - 1
        if(ir == 1)then
          indx(1) = indxt
          return
        end if
      end if
      i=l
      j=l+l
      do
        if(j > ir) exit
        if(j < ir) then ! separate if to avoid array bounds issues
          if (arrin(indx(j)) < arrin(indx(j+1))) j = j+1
        end if
        if(q < arrin(indx(j)))then
          indx(i) = indx(j)
          i = j
          j = j+j
        else
          j = ir+1
        end if
      end do
      indx(i) = indxt
    end do

  end subroutine heap_sort_integer484

!==================================== HEAP_SORT_DOUBLE =======================80
!
! real(dp) heap sort for ascending order
!
!=============================================================================80

  subroutine heap_sort_double(n,arrin,indx)

    use kinddefs, only : dp

    integer,                   intent(in)  :: n
    real(dp), dimension(n),    intent(in)  :: arrin
    integer,     dimension(n), intent(out) :: indx

    integer     :: j, l, ir, indxt, i
    real(dp)    :: q

    continue

    if (n <= 0) return

    do j=1,n
      indx(j) = j
    end do

    if(n  ==  1)return

    l = n/2 + 1
    ir= n
    do
      if(l > 1)then
        l=l-1
        indxt = indx(l)
        q     = arrin(indxt)
      else
        indxt = indx(ir)
        q     = arrin(indxt)
        indx(ir) = indx(1)
        ir = ir - 1
        if(ir == 1)then
          indx(1) = indxt
          return
        end if
      end if
      i=l
      j=l+l
      do
        if(j > ir) exit
        if(j < ir) then ! separate if to avoid array bounds issues
          if (arrin(indx(j)) < arrin(indx(j+1))) j = j+1
        end if
        if(q < arrin(indx(j)))then
          indx(i) = indx(j)
          i = j
          j = j+j
        else
          j = ir+1
        end if
      end do
      indx(i) = indxt
    end do

  end subroutine heap_sort_double

!=============================== heap_sort_no_index_integer ==================80
!
! heap sort (without an indx)
!
!=============================================================================80

  subroutine heap_sort_no_index_integer(nelem,iarr)

    implicit none

    integer,                   intent(in)    :: nelem
    integer, dimension(nelem), intent(inout) :: iarr

    integer :: j, k, nleft, ival, found

  continue

    if ( nelem <= 1 ) return

    nleft = nelem
    k     = nleft/2 + 1

    outer: do
      if ( k > 1 ) then
        k = k - 1
        ival = iarr(k)
      else
        ival = iarr(nleft)
        iarr(nleft) = iarr(1)
        nleft = nleft - 1
        if ( nleft == 1 ) then
          iarr(1) = ival
          exit outer
        end if
      end if

      found = k
      j = k + k
      inner: do
        if ( j > nleft ) exit inner
        if ( j < nleft ) then ! separate if to avoid array bounds issues
          if ( iarr(j) < iarr(j+1) ) j = j + 1
        end if
        if ( ival < iarr(j) ) then
          iarr(found) = iarr(j)
          found = j
          j = j + j
        else
          j = nleft + 1
        end if
      end do inner
      iarr(found) = ival
    end do outer

  end subroutine heap_sort_no_index_integer

!=============================== heap_sort_no_index_integer48 ================80
!
! heap sort (without an indx).
!
!=============================================================================80

  subroutine heap_sort_no_index_integer48(nelem,iarr_i8)

    use kinddefs, only : system_i4, system_i8

    integer(system_i4),                   intent(in)    :: nelem
    integer(system_i8), dimension(nelem), intent(inout) :: iarr_i8

    integer :: j,k, nleft, found
    integer(system_i8) :: ival_i8

  continue

    if (nelem <= 1) return

    nleft = nelem
    k     = nleft/2 + 1

    outer: do
      if (k > 1) then
        k = k - 1
        ival_i8 = iarr_i8(k)
      else
        ival_i8 = iarr_i8(nleft)
        iarr_i8(nleft) = iarr_i8(1)
        nleft = nleft - 1
        if (nleft == 1) then
          iarr_i8(1) = ival_i8
          exit outer
        end if
      end if

      found = k
      j = k+k
      inner: do
        if (j > nleft) exit inner
        if (j < nleft) then ! separate if to avoid array bounds issues
          if (iarr_i8(j) < iarr_i8(j+1)) j = j + 1
        end if
        if (ival_i8 < iarr_i8(j)) then
          iarr_i8(found) = iarr_i8(j)
          found = j
          j = j + j
        else
          j = nleft + 1
        end if
      end do inner
      iarr_i8(found) = ival_i8
    end do outer

  end subroutine heap_sort_no_index_integer48

!=============================== HEAP_SORT_NO_IDX_REAL888 ====================80
!
!  Sort array ra and carry along rb and rc (no index)
!
!=============================================================================80
  subroutine heap_sort_no_idx_real888( nelem, ra, rb, rc )

    use kinddefs, only : dp

    integer,                intent(in   ) :: nelem
    real(dp), dimension(:), intent(inout) :: ra, rb, rc

    real(dp) :: rra, rrb, rrc

    integer :: k, nleft, j, found

    continue

    if ( nelem <= 1 ) return

    nleft = nelem
    k     = nleft/2 + 1

    outer : do
      if ( k > 1 ) then
        k = k - 1
        rra = ra(k)
        rrb = rb(k)
        rrc = rc(k)
      else
        rra = ra(nleft)
        rrb = rb(nleft)
        rrc = rc(nleft)
        ra(nleft) = ra(1)
        rb(nleft) = rb(1)
        rc(nleft) = rc(1)
        nleft = nleft - 1
        if ( nleft == 1 ) then
          ra(1) = rra
          rb(1) = rrb
          rc(1) = rrc
          return
        end if
      end if

      found = k
      j = k + k
      inner: do while ( j <= nleft )
        if ( j < nleft ) then ! separate if to avoid array bounds issues
          if ( ra(j) < ra(j+1) ) j = j + 1
        end if
        if ( rra < ra(j) ) then
          ra(found) = ra(j)
          rb(found) = rb(j)
          rc(found) = rc(j)
          found = j
          j = j + j
        else
          j = nleft + 1
        end if
      end do inner
      ra(found) = rra
      rb(found) = rrb
      rc(found) = rrc
    end do outer

  end subroutine heap_sort_no_idx_real888

!=============================== BINARY_SEARCH_INTEGER =======================80
!
! Binary search for ascending sorted list
!
!=============================================================================80

  pure &
  function binary_search_integer( nnodes, sorted_nodes, node )

    integer                                :: binary_search_integer
    integer,                    intent(in) :: nnodes
    integer, dimension(nnodes), intent(in) :: sorted_nodes
    integer,                    intent(in) :: node

    integer :: lower,upper,mid

    continue

    binary_search_integer = 0

    if ( nnodes < 1 ) return

    if ( ( node < sorted_nodes(1) ).or.( node > sorted_nodes(nnodes) ) ) return

    if ( node == sorted_nodes(1)      ) then
      binary_search_integer = 1      ; return
    end if
    if ( node == sorted_nodes(nnodes) ) then
      binary_search_integer = nnodes ; return
    end if

    lower = 0
    upper = nnodes
    do while ( lower < upper )
      mid = lower + ( upper - lower ) / 2
      if ( sorted_nodes(mid) < node ) then
        lower = mid + 1
      else
        upper = mid
      end if
    end do

    if ( (lower>0) .and. (lower<=nnodes) ) then ! sep. if to avoid array bounds
      if ( sorted_nodes(lower) == node ) binary_search_integer = lower
    end if

  end function binary_search_integer

!=============================== binary_search_i8 ============================80
! binary_search_i8 for ascending sorted list
! The values searched are i8, the number of values is i4 (i.e., function is i4)
!=============================================================================80

  pure &
  function binary_search_integer488(nnodes,sorted_nodes,node)

    use kinddefs, only : system_i4, system_i8

    integer(system_i4)                               :: binary_search_integer488
    integer(system_i4),                    intent(in) :: nnodes
    integer(system_i8), dimension(nnodes), intent(in) :: sorted_nodes
    integer(system_i8),                    intent(in) :: node

    integer :: lower,upper,mid

    continue

    binary_search_integer488 = 0

    if ( nnodes < 1 ) return

    if ( ( node < sorted_nodes(1) ).or.( node > sorted_nodes(nnodes) ) ) return

    if (node == sorted_nodes(1)) then
      binary_search_integer488 = 1; return
    end if
    if (node == sorted_nodes(nnodes)) then
      binary_search_integer488 = nnodes; return
    end if

    lower = 0
    upper = nnodes
    do while (lower < upper)
      mid = lower + (upper - lower) / 2
      if (sorted_nodes(mid) < node) then
        lower = mid + 1
      else
        upper = mid
      end if
    end do

    if ( (lower>0) .and. (lower<=nnodes) ) then ! sep. if to avoid array bounds
      if (sorted_nodes(lower) == node) binary_search_integer488 = lower
    end if

  end function binary_search_integer488

!=============================== binary_search_dp ============================80
! binary_search for ascending sorted list
!=============================================================================80

  pure &
  function binary_search_double(nnodes,sorted_nodes,node)

    use kinddefs, only : dp

    integer                                 :: binary_search_double
    integer,                     intent(in) :: nnodes
    real(dp), dimension(nnodes), intent(in) :: sorted_nodes
    real(dp),                    intent(in) :: node

    integer :: lower,upper,mid

    continue

    binary_search_double = 0

    if (nnodes < 1) return

    if ( ( node < sorted_nodes(1) ).or.( node > sorted_nodes(nnodes) ) ) return

    if ( node == sorted_nodes(1) ) then
      binary_search_double = 1; return
    end if
    if ( node == sorted_nodes(nnodes) ) then
      binary_search_double = nnodes; return
    end if

    lower = 0
    upper = nnodes
    do while (lower < upper)
      mid = lower + (upper - lower) / 2
      if (sorted_nodes(mid) < node) then
        lower = mid + 1
      else
        upper = mid
      end if
    end do

    if ( (lower>0) .and. (lower<=nnodes) ) then ! sep. if to avoid array bounds
      if (sorted_nodes(lower) == node) binary_search_double = lower
    end if

  end function binary_search_double

!=============================== binary_search_less ==========================80
! binary_search for ascending sorted list. If not found, also return an index
! less than the value sought.
!=============================================================================80

  function binary_search_less(nnodes,sorted_nodes,targetnode,iless)

    integer                                :: binary_search_less
    integer,                    intent(in) :: nnodes
    integer, dimension(nnodes), intent(in) :: sorted_nodes
    integer,                    intent(in) :: targetnode
    integer,                    intent(out):: iless

    integer :: lower,upper,mid

    continue

    binary_search_less = 0
    iless              = 0

    if (nnodes <= 0) return
    if (targetnode < sorted_nodes(1)) return
    if (targetnode > sorted_nodes(nnodes)) then
      iless = nnodes
      return
    end if
    if (targetnode == sorted_nodes(1)) then
      binary_search_less = 1
      return
    end if
    if (targetnode == sorted_nodes(nnodes)) then
      binary_search_less = nnodes
      return
    end if

    lower = 1
    upper = nnodes
    mid = (nnodes+1)/2

    do while ((lower < mid) .and. (mid < upper))
      if ( targetnode >= sorted_nodes(mid) ) then
        if ( targetnode == sorted_nodes(mid))then
          binary_search_less = mid
          return
        end if
        lower = mid
      else
        upper = mid
      end if
      mid = (lower+upper)/2
      if (sorted_nodes(mid) <= targetnode) iless = mid
    end do

  end function binary_search_less

!=============================== growable_lookup_extend ======================80
! Insert an element, use linear and sorted lists.
! Initialize, reallocate, and finalize (based on op)
!=============================================================================80

  subroutine growable_lookup_extend(targetnode,op,list1)

    integer, intent(in)    :: targetnode
    integer, intent(in)    :: op   ! -1, finalize--consolidate and deallocate
                                   ! 0,  initialize--allocate estimate
                                   ! 1,  find and/or insert targetnode
    type(growable_lookup_type), intent(inout) :: list1

    integer, dimension(:), allocatable :: temp_val

    integer, parameter :: linear_sz = 50

    integer :: i,iop,all_ct

    continue
    iop = op

    if (iop == 1) then
      if (list1%sorted_ct > 0) then
        i = binary_search(list1%sorted_ct,list1%sorted,targetnode)
        if (i > 0) return
      end if
      if (list1%linear_ct > 0) then
        do i = 1,list1%linear_ct
          if (targetnode == list1%linear(i)) return
        end do
        if (list1%linear_ct < list1%linear_sz) then
          list1%linear_ct = list1%linear_ct + 1
        else
          allocate(temp_val(list1%sorted_ct)); temp_val = 0
          if (list1%sorted_ct > 0) then
            temp_val(1:list1%sorted_ct) = list1%sorted(1:list1%sorted_ct)
          end if
          if (associated(list1%sorted)) deallocate(list1%sorted)
          all_ct = list1%sorted_ct + list1%linear_ct
          allocate(list1%sorted(all_ct)); list1%sorted = 0
          list1%sorted(1:list1%sorted_ct) = temp_val(1:list1%sorted_ct)
          deallocate(temp_val)
          list1%sorted(list1%sorted_ct+1:all_ct) =                          &
            list1%linear(1:list1%linear_ct)
          list1%sorted_ct = all_ct
          call heap_sort(list1%sorted_ct,list1%sorted)

          list1%linear = 0
          list1%linear_ct = 1
        end if
      else
        list1%linear_ct = 1
      end if
      list1%linear(list1%linear_ct) = targetnode
    elseif ((iop == -1).and.(list1%linear_ct > 0)) then ! terminate
      if (list1%sorted_ct > 0) then
        allocate(temp_val(list1%sorted_ct))
        temp_val(1:list1%sorted_ct) = list1%sorted(1:list1%sorted_ct)
        deallocate(list1%sorted)
        all_ct = list1%sorted_ct + list1%linear_ct
        allocate(list1%sorted(all_ct)); list1%sorted = 0
        list1%sorted(1:list1%sorted_ct) = temp_val(1:list1%sorted_ct)
        deallocate(temp_val)
        list1%sorted(list1%sorted_ct+1:all_ct)=list1%linear(1:list1%linear_ct)
        list1%sorted_ct = all_ct
        deallocate(list1%linear)
        call heap_sort(list1%sorted_ct,list1%sorted)
      end if
      list1%linear_ct = 0
      nullify(list1%linear)
    elseif (iop == 0) then
      list1%linear_ct = 0
      list1%linear_sz = linear_sz
      allocate(list1%linear(list1%linear_sz))
      list1%linear    = 0
      list1%sorted_ct = 0
      list1%sorted_sz = 0
    end if

  end subroutine growable_lookup_extend

!============================== reverse ======================================80
! reverse the order of an array in place with O(1) temp space
!=============================================================================80

  subroutine reverse_integer( n, data )
    integer,                   intent(in)    :: n
    integer, dimension(n),     intent(inout) :: data
    integer :: temp
    integer :: i
    continue
    do i = 1, n/2
      temp = data(i)
      data(i) = data(n-i+1)
      data(n-i+1) = temp
    end do
  end subroutine reverse_integer

  subroutine reverse_double( n, data )
    use kinddefs, only : dp
    integer,                    intent(in)    :: n
    real(dp), dimension(n),     intent(inout) :: data
    real(dp) :: temp
    integer :: i
    continue
    do i = 1, n/2
      temp = data(i)
      data(i) = data(n-i+1)
      data(n-i+1) = temp
    end do
  end subroutine reverse_double

!============================== small_sort_integer ===========================80
!
! sort for integer array of n elements from low-to-high; optional
!
! WARNING: method is n**2; only use with small n, (e.g. n < 10)
!
!=============================================================================80

  subroutine small_sort_integer( n, array )

    integer,                   intent(in)    :: n       ! no. elements to sort
    integer, dimension(n),     intent(inout) :: array   ! array to sort lo-to-hi

    integer :: temp, i, j

    continue

    do j = 2, n
      temp = array(j)
      do i = j-1, 1, -1
! keep goto, it prevents incorect compiler optimization
        if (array(i) <= temp) goto 10
        array(i+1) = array(i)
      end do
      i=0
10    array(i+1) = temp
    end do

  end subroutine small_sort_integer

!============================= small_sort_double =============================80
!
! sort for real array of n elements from low-to-high; optional
!
! WARNING: method is n**2; only use with small n, (e.g. n < 10)
!
!=============================================================================80

  subroutine small_sort_double( n, array )

    use kinddefs, only : dp

    integer,                   intent(in)    :: n       ! no. elements to sort
    real(dp), dimension(n),    intent(inout) :: array   ! array to sort lo-to-hi

    real(dp) :: temp
    integer  :: i, j

    continue

    do j = 2, n
      temp = array(j)
      do i = j-1, 1, -1
! keep goto, it prevents incorect compiler optimization
        if (array(i) <= temp) goto 10
        array(i+1) = array(i)
      end do
      i=0
10    array(i+1) = temp
    end do

  end subroutine small_sort_double

!================================ Quick Sort =================================80
!
! This will re-order a portion of an integer array (defined by the indices,
! 'left' and 'right') into ascending/descending order.
! As long as the array values are randomly stored, this is very fast,
! i.e., O(N*logN), not O(N^2).
!
! INPUT:
!
!    idx(:) = [1,2,3, ..., N]
!  value(:) = [value1, value2, value3, ..., valueN]
!      left = 1 (or any value < right)
!     right = N (or any value > left)
!     Order = 1 : ascending  order ( 1 2 3 4 5 )
!             2 : descending order ( 5 4 3 2 1 )
!
! Note: This does not change the value array directly.
!       Instead, it returns the re-ordered index list, idx(:).
!       The re-ordered values can be accessed by value(idx(i)) with i=1,...,N.
!=============================================================================80
  recursive subroutine idx_quicksort_int( idx, value, left, right, order )

    integer,               intent(in)    :: left, right, order
    integer, dimension(:), intent(in)    :: value
    integer, dimension(:), intent(inout) :: idx

    integer :: pivot, new_pivot

    if ( left < right ) then
         pivot = left ! Select a pivot
     new_pivot = idx_qsort_partition_int( idx, value, left, right, pivot, order)
     call idx_quicksort_int( idx, value,          left, new_pivot - 1, order )
     call idx_quicksort_int( idx, value, new_pivot + 1,         right, order )

    endif

  end subroutine idx_quicksort_int


  recursive subroutine idx_quicksort_double( idx, value, left, right, order )

    use kinddefs, only: dp

    integer,                intent(in)    :: left, right, order
    real(dp), dimension(:), intent(in)    :: value
    integer,  dimension(:), intent(inout) :: idx

    integer :: pivot, new_pivot

    if ( left < right ) then
         pivot = left ! Select a pivot
     new_pivot = idx_qsort_partition_double(idx, value, left, right, pivot,    &
                                            order)
     call idx_quicksort_double(idx, value,          left, new_pivot - 1, order)
     call idx_quicksort_double(idx, value, new_pivot + 1,         right, order)

    endif

  end subroutine idx_quicksort_double


!=============================================================================80
!
! This partitions the portion of the array between indexes left and right,
! inclusively, by moving all elements less than or equal to the pivot to
! the left of the pivot position, and all the greater elements to the right.
!
!=============================================================================80
  function idx_qsort_partition_int( idx, value,  left, right, pivot, order)

    integer,               intent(in)    :: left, right, pivot, order
    integer, dimension(:), intent(in)    :: value
    integer, dimension(:), intent(inout) :: idx

    integer :: pivot_value, temp, i, begin_right, idx_qsort_partition_int
    logical :: to_the_left

  continue

! Save off the pivot value.
      pivot_value = value( idx(pivot) )

! Store the pivot at the end of the list.
                 temp = idx(right)
           idx(right) = idx(pivot)
           idx(pivot) = temp

! The index that indicates the begining of the right half elements.
      begin_right = left ! <- initially, all are considered as the right.

! Partitioning: put elements less than pivot value to the left.
   do i = left, right-1

         to_the_left = .false.
    if     ( order == 1 .and. value(idx(i)) < pivot_value) then
         to_the_left = .true.
    elseif ( order == 2 .and. value(idx(i)) > pivot_value) then
         to_the_left = .true.
    endif

    if ( to_the_left ) then
                 temp = idx(i)
     idx(i)           = idx(begin_right)
     idx(begin_right) = temp
          begin_right = begin_right + 1
    endif
   end do

! Place the pivot to the right place (between the left and the right).
                 temp = idx(right)
     idx(right)       = idx(begin_right)
     idx(begin_right) = temp

! Return the begin_right index
     idx_qsort_partition_int = begin_right

  end function idx_qsort_partition_int


  function idx_qsort_partition_double( idx, value,  left, right, pivot, order)

    use kinddefs, only: dp

    integer,                intent(in)    :: left, right, pivot, order
    real(dp), dimension(:), intent(in)    :: value
    integer,  dimension(:), intent(inout) :: idx

    real(dp) :: pivot_value, idx_qsort_partition_double
    integer  :: temp, i, begin_right
    logical  :: to_the_left

  continue

! Save off the pivot value.
      pivot_value = value( idx(pivot) )

! Store the pivot at the end of the list.
                 temp = idx(right)
           idx(right) = idx(pivot)
           idx(pivot) = temp

! The index that indicates the begining of the right half elements.
      begin_right = left ! <- initially, all are considered as the right.

! Partitioning: put elements less than pivot value to the left.
   do i = left, right-1

         to_the_left = .false.
    if     ( order == 1 .and. value(idx(i)) < pivot_value) then
         to_the_left = .true.
    elseif ( order == 2 .and. value(idx(i)) > pivot_value) then
         to_the_left = .true.
    endif

    if ( to_the_left ) then
                 temp = idx(i)
     idx(i)           = idx(begin_right)
     idx(begin_right) = temp
          begin_right = begin_right + 1
    endif
   end do

! Place the pivot to the right place (between the left and the right).
                 temp = idx(right)
     idx(right)       = idx(begin_right)
     idx(begin_right) = temp

! Return the begin_right index
     idx_qsort_partition_double = begin_right

  end function idx_qsort_partition_double

!=============================== selection ===================================80
!
! select the position smallest item in a list
!   http://en.wikipedia.org/wiki/Selection_algorithm
!
!=============================================================================80

  recursive subroutine selection( length, items, position, item )

    use kinddefs, only : dp

    integer,                     intent(in)  :: length
    real(dp), dimension(length), intent(in)  :: items
    integer,                     intent(in)  :: position
    real(dp),                    intent(out) :: item

    real(dp), dimension(length) :: partitioned_items

    real(dp) :: pivot, temp

    integer :: pivot_position, i

    continue

    if ( position < 1 .or. position > length ) then
      item = -huge(0.0_dp)
      write(*,*) 'selection error: position, length', position, length
      return
    end if

    pivot = items(position)
    partitioned_items = items

! stash the pivot in the last entry
    temp = partitioned_items(length)
    partitioned_items(length) = partitioned_items(position)
    partitioned_items(position) = temp

    pivot_position = 1
    do i = 1, length-1
      if ( partitioned_items(i) < pivot ) then
        temp = partitioned_items(pivot_position)
        partitioned_items(pivot_position) = partitioned_items(i)
        partitioned_items(i) = temp
        pivot_position = pivot_position + 1
      end if
    end do

! return the pivot
    temp = partitioned_items(length)
    partitioned_items(length) = partitioned_items(pivot_position)
    partitioned_items(pivot_position) = temp

    if ( position == pivot_position ) then
      item = partitioned_items(pivot_position)
    else
      if ( pivot_position < position ) then
        call selection( length-pivot_position,         &
          partitioned_items( pivot_position+1:length), &
          position-pivot_position,                     &
          item )
      else
        call selection( pivot_position-1,         &
          partitioned_items( 1:pivot_position-1), &
          position,                               &
          item )
      end if
    end if

  end subroutine selection

!================================== sort_uniq ================================80
!
! Sort an integer array, order the uniq values up front, return the number of
! unique values. If optional arguments istart,iend are provided, only sort a
! array slice. Only the values up front are ordered and retained, the remainder
! are to be ignored.
!
!=============================================================================80
  subroutine sort_uniq(array_size,arr,nvals,istart,iend)

    integer,                        intent(in)    :: array_size
    integer, dimension(array_size), intent(inout) :: arr
    integer,                        intent(out)   :: nvals
    integer, optional,              intent(in)    :: istart, iend

    integer :: i,j,is,ie
    integer, dimension(:), allocatable :: temp1

    continue

    nvals = 1
    if (array_size == 1) return

    if (.not.present(istart)) then
      if (array_size > 50) then
        call heap_sort_no_index_integer(array_size,arr)
      else
        call small_sort(array_size,arr)
      end if
      is = 1
      ie = array_size
    else
      is = istart
      if (.not.present(iend)) then
        ie = array_size
      else
        ie = iend
      end if
      j = (ie-is)+1
      allocate(temp1(j))
      temp1(1:j) = arr(is:ie)
      if (j > 50) then
        call heap_sort_no_index_integer(j,temp1)
      else
        call small_sort(j,temp1)
      end if
      arr(is:ie) = temp1(1:j)
      deallocate(temp1)
    end if

    do i = is+1,ie
      if (arr(i) /= arr(i-1)) then
        arr(is+nvals) = arr(i)
        nvals = nvals + 1
      end if
    end do

  end subroutine sort_uniq



!=============================== binary_search_dp_eps ========================80
! binary_search for ascending sorted list
!=============================================================================80

  function binary_search_double_eps(nnodes,sorted_nodes,node, eps)

    use kinddefs, only : dp

    integer                                 :: binary_search_double_eps
    integer,                     intent(in) :: nnodes
    real(dp), dimension(nnodes), intent(in) :: sorted_nodes
    real(dp),                    intent(in) :: node, eps

    integer  :: lower,upper,mid

    continue

    binary_search_double_eps = 0

    if (nnodes < 1) return

    if (abs(node-sorted_nodes(1)) <= eps) then
       binary_search_double_eps = 1
       return
    else if (node < sorted_nodes(1)) then
       return
    end if

    if (abs(node-sorted_nodes(nnodes)) <= eps) then
       binary_search_double_eps = nnodes
       return
    else if (node > sorted_nodes(nnodes)) then
       return
    end if

    lower = 0
    upper = nnodes
    do while (lower < upper)
      mid = lower + (upper - lower) / 2
      if ( abs(node-sorted_nodes(mid)) <= eps) then
         binary_search_double_eps = mid
         return
      end if
      if (sorted_nodes(mid) < node) then
        lower = mid + 1
      else
        upper = mid
      end if
    end do

    if ( (lower>0) .and. (lower<=nnodes) ) then ! sep. if to avoid array bounds
      if (abs(node-sorted_nodes(lower)) <= eps) binary_search_double_eps = lower
    end if

  end function binary_search_double_eps

end module sort

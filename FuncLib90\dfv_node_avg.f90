!================================= DFV_NODE_AVG ==============================80
!
! Linearization of the viscous fluxes using node averaging.
!
!=============================================================================80

  function dfv_node_avg( igrid, xnorm, ynorm, znorm, area,                     &
                         flsq_n, flsq_j, flsq_ja, averaged_nodes,              &
                         n_tot, n_q,                                           &
                         cell1, cell2, ncell01, xc, yc, zc, sc, qcell, amut,   &
                         x, y, z, qt_face )

    use node_avg_cc, only : dqavg, n_qavg_max, dqavg_bc

    integer, intent(in) :: igrid, flsq_n, flsq_j, ncell01
    integer, intent(in) :: cell1, cell2, n_tot, n_q

    integer, dimension(5),       intent(in) :: averaged_nodes
    integer,  dimension(flsq_n), intent(in) :: flsq_ja

    real(dp), intent(in)    :: xnorm, ynorm, znorm, area

    real(dp), dimension(:),             intent(in) :: xc, yc, zc, sc, amut
    real(dp), dimension(:),             intent(in) :: x, y, z
    real(dp), dimension(n_tot,ncell01), intent(in) :: qcell
    real(dp), dimension(:,:),           intent(in) :: qt_face

    integer :: cella, ii, iii, qt_eq, nb
    integer :: face_node, n_face_nodes, node, na_b, n_qavg, ia_flsq, na_cell

    integer, dimension(n_qavg_max) :: cells, iqt

    real(dp) :: amutf, xn, yn, zn

    real(dp), dimension(5) :: ql, qr

    !...qt at face
    real(dp), dimension(5) :: qtf
    !...gradients of qtf
    real(dp), dimension(3,5) :: qtfgrad, grad_qt_ext
    !...linearization of qtf w/r to cell-centers
    real(dp), dimension(5,lsq_face_neighbors_max+2) :: dqtf
    !...linearization of qtf gradients w/r to cell-centers
    real(dp), dimension(3,lsq_face_neighbors_max+2) :: dugrad, dvgrad, &
                                                       dwgrad, dtgrad
    !...linearization of tgradn w/r to cell-centers
    real(dp), dimension(lsq_face_neighbors_max+2) :: dtgradn
    real(dp), dimension(3,6) :: dgrad_q
    real(dp), dimension(3) :: uterm, vterm, wterm, tterm, dgrad_term
    real(dp), dimension(n_qavg_max) :: sw
    real(dp), dimension(n_q, n_qavg_max) :: dw_int, dw_ext
    real(dp), dimension(5,flsq_j) :: qta

    real(dp), dimension(5,5,flsq_j) :: dfv_node_avg

    integer, parameter :: eqn_set = 0, n_mf = 5  !purely compressible.

  continue

    ! Linearization w/r to cell-centers and node-averages.

    amutf = 0.5_dp*( amut(cell1) + amut(cell2) )

    qtf = qtf_node_avg( eqn_set, n_mf,                           &
                        qcell(1:n_mf,cell1), qcell(1:n_mf,cell2) )

    dqtf(1,:) = dqtf_node_avg( flsq_n )
    dqtf(2,:) = dqtf(1,:)
    dqtf(3,:) = dqtf(1,:)
    dqtf(4,:) = dqtf(1,:)
    dqtf(5,:) = dqtf(1,:)

    dgrad_q = dgrad_node_avg( cell1, cell2, averaged_nodes, &
                              xc, yc, zc, x, y, z  )

    n_face_nodes = averaged_nodes(5)

    ! Distribute node-average contributions to cells.
    ! Until boundary conditions come into play, dgrad terms identical.

    ! First the contribution from interior cells.

    dvgrad(:,1:flsq_n) = 0._dp

    dvgrad(:,flsq_n+1) = dgrad_q(:,5)
    dvgrad(:,flsq_n+2) = dgrad_q(:,6)

    na_b = 0
    grad_qt_ext(:,:) = 0._dp
    do face_node=1,n_face_nodes

      dgrad_term(:) = dgrad_q(:,face_node)

      node = averaged_nodes(face_node)

      xn = x(node)
      yn = y(node)
      zn = z(node)

      call dqavg( igrid, xc, yc, zc, sc, xn, yn, zn,      &
                  node, n_qavg_max, n_qavg, nb, sw, cells )

      na_b = na_b + nb

      do ii=1,n_qavg
        vterm(:) = sw(ii)*dgrad_term(:)
        cella = cells(ii)
        if ( cella == cell1 ) then
          dvgrad(:,flsq_n+1) = dvgrad(:,flsq_n+1) + vterm(:)
          cycle
        elseif ( cella == cell2 ) then
          dvgrad(:,flsq_n+2) = dvgrad(:,flsq_n+2) + vterm(:)
          cycle
        endif
        iii = 0
        do ia_flsq=1,flsq_n
          na_cell = flsq_ja(ia_flsq)
          iii = iii + 1
          if ( na_cell == cella ) exit
        enddo
        dvgrad(:,iii) = dvgrad(:,iii) + vterm(:)
      enddo

    enddo

!   Set u,v,w,T entries before adding boundary condition info.

    do ii=1,flsq_n+2
      dugrad(:,ii) = dvgrad(:,ii)
      dwgrad(:,ii) = dvgrad(:,ii)
      dtgrad(:,ii) = dvgrad(:,ii)
    enddo

    if ( na_b == 0 ) n_face_nodes = 0  !skip boundary additions
    do face_node=1,n_face_nodes

      dgrad_term(:) = dgrad_q(:,face_node)

      node = averaged_nodes(face_node)

      xn = x(node)
      yn = y(node)
      zn = z(node)

      call dqavg_bc( igrid, xn, yn, zn, n_q, node, n_qavg_max, &
                     n_qavg, dw_int, dw_ext, cells, iqt )

      do ii=1,n_qavg
        uterm(:) = dw_int(2,ii)*dgrad_term(:)
        vterm(:) = dw_int(3,ii)*dgrad_term(:)
        wterm(:) = dw_int(4,ii)*dgrad_term(:)
        tterm(:) = dw_int(5,ii)*dgrad_term(:)
        do qt_eq=2,n_mf
          grad_qt_ext(:,qt_eq) = grad_qt_ext(:,qt_eq)           &
          + dw_ext(qt_eq,ii)*dgrad_term(:)*qt_face(qt_eq,iqt(ii))
        enddo
        cella = cells(ii)
        if ( cella == cell1 ) then
          dugrad(:,flsq_n+1) = dugrad(:,flsq_n+1) + uterm(:)
          dvgrad(:,flsq_n+1) = dvgrad(:,flsq_n+1) + vterm(:)
          dwgrad(:,flsq_n+1) = dwgrad(:,flsq_n+1) + wterm(:)
          dtgrad(:,flsq_n+1) = dtgrad(:,flsq_n+1) + tterm(:)
          cycle
        elseif ( cella == cell2 ) then
          dugrad(:,flsq_n+2) = dugrad(:,flsq_n+2) + uterm(:)
          dvgrad(:,flsq_n+2) = dvgrad(:,flsq_n+2) + vterm(:)
          dwgrad(:,flsq_n+2) = dwgrad(:,flsq_n+2) + wterm(:)
          dtgrad(:,flsq_n+2) = dtgrad(:,flsq_n+2) + tterm(:)
          cycle
        endif
        iii = 0
        do ia_flsq=1,flsq_n
          na_cell = flsq_ja(ia_flsq)
          iii = iii + 1
          if ( na_cell == cella ) exit
        enddo
        dugrad(:,iii) = dugrad(:,iii) + uterm(:)
        dvgrad(:,iii) = dvgrad(:,iii) + vterm(:)
        dwgrad(:,iii) = dwgrad(:,iii) + wterm(:)
        dtgrad(:,iii) = dtgrad(:,iii) + tterm(:)
      enddo

    enddo

    do ii=1,flsq_n+2
      dtgradn(ii)  = dtgrad(1,ii)*xnorm &
                   + dtgrad(2,ii)*ynorm &
                   + dtgrad(3,ii)*znorm
    enddo

    !  Use linearization and qt_face bc array to compute qtfgrad.

    !...convert to T variables here.
    ql(1:n_mf) = qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cell1) )
    qr(1:n_mf) = qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cell2) )
    qtfgrad(:,2)= dugrad(:,flsq_n+1)*ql(2) &
                + dugrad(:,flsq_n+2)*qr(2)
    qtfgrad(:,3)= dvgrad(:,flsq_n+1)*ql(3) &
                + dvgrad(:,flsq_n+2)*qr(3)
    qtfgrad(:,4)= dwgrad(:,flsq_n+1)*ql(4) &
                + dwgrad(:,flsq_n+2)*qr(4)
    qtfgrad(:,5)= dtgrad(:,flsq_n+1)*ql(5) &
                + dtgrad(:,flsq_n+2)*qr(5)

    do ii=1,flsq_n
      cella  = flsq_ja(ii)
      qta(1:n_mf,ii) = qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cella) )
      qtfgrad(:,2) = qtfgrad(:,2) + dugrad(:,ii)*qta(2,ii)
      qtfgrad(:,3) = qtfgrad(:,3) + dvgrad(:,ii)*qta(3,ii)
      qtfgrad(:,4) = qtfgrad(:,4) + dwgrad(:,ii)*qta(4,ii)
      qtfgrad(:,5) = qtfgrad(:,5) + dtgrad(:,ii)*qta(5,ii)
    enddo
    qta(1:n_mf,flsq_n+1) = qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cell1) )
    qta(1:n_mf,flsq_n+2) = qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cell2) )

    do qt_eq=2,n_mf
      qtfgrad(:,qt_eq) = qtfgrad(:,qt_eq) + grad_qt_ext(:,qt_eq)
    enddo

    dfv_node_avg(:,:,1:flsq_j) =                             &
        dfvf( xnorm, ynorm, znorm, area, amutf, qtf, dqtf,   &
              qtfgrad, dugrad, dvgrad, dwgrad, dtgradn,      &
              flsq_j, qta )

  end function dfv_node_avg

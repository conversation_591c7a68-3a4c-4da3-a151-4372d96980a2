module dmach

  implicit none

  private

  public :: sensmach

contains


!================================= SENSMACH ==================================80
!
! This routine is forming the sensitivity derivative dL/dX,
! where X is the Mach number
!
!=============================================================================80

  subroutine sensmach(grid, soln, sadj, design, dLdX, physical_timestep,       &
                      subiteration)

    use design_types,         only : design_type
    use designs,              only : find_window_value
    use kinddefs,             only : dp
    use allocations,          only : my_alloc_ptr
    use info_depr,            only : ivisc
    use nml_nonlinear_solves, only : itime
    use adjoint_switches,     only : windowing
    use thermo,               only : etop, ptoe
    use bc_names,             only : farfield_riem
    use fun3d_constants,      only : my_0, my_1
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use lmpi,                 only : lmpi_master, lmpi_reduce, lmpi_bcast
    use forces,               only : timestep_contributes

    integer, intent(in) :: physical_timestep, subiteration

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(in)    :: design

    real(dp), dimension(design%nfunctions), intent(out) :: dLdX

    integer :: ib, j, k

    real(dp) :: tempvar, window_value

    real(dp), dimension(design%nfunctions) :: factor

    real(dp), dimension(:), pointer :: dfdminf
    real(dp), dimension(:), pointer :: productD

    logical :: apply_dfdm

  continue

    call my_alloc_ptr(productD,design%nfunctions)

    call etop(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)

    dLdX     = 0.0_dp
    productD = 0.0_dp

    do ib = 1, grid%nbound
      if(grid%bc(ib)%ibc == farfield_riem) then
        call drdmachinv(grid%nnodes0,grid%bc(ib)%nbnode,soln%q_dof,            &
                        sadj%rlam,sadj%coltag,grid%bc(ib)%ibnode,              &
                        grid%bc(ib)%bxn,grid%bc(ib)%byn,grid%bc(ib)%bzn,       &
                        productD,design%nfunctions,soln%adim,soln%n_tot,       &
                        grid%nnodes01)
      endif
    end do

    if(ivisc > 0) then
      call drdmachv(grid%nnodes0,grid%elem(1)%ncell,                           &
                    grid%elem(1)%c2n,grid%x,grid%y,grid%z,soln%q_dof,soln%amut,&
                    productD,sadj%rlam,sadj%coltag,grid%nbound,grid%bc,        &
                    design%nfunctions, soln%adim, soln%n_tot,                  &
                    grid%nnodes01)
    endif

    if(ivisc == 6) then
      call turbresm(soln%eqn_set,grid%nnodes0,grid%nnodes01,grid%nedge,        &
                    grid%nedgeLoc,grid%eptr,soln%turb,soln%q_dof,grid%x,grid%y,&
                    grid%z,grid%slen,soln%gradx,soln%grady,soln%gradz,grid%vol,&
                    grid%xn,grid%yn,grid%zn,grid%ra,soln%dft1,soln%dft2,       &
                    sadj%coltag,sadj%rlam,productD,grid%nbound,grid%bc,        &
                    design%nfunctions,grid%nedgeloc_2d,grid%nnodes0_2d,        &
                    grid%node_pairs_2d,grid%nelem,grid%elem,soln%adim,         &
                    soln%n_tot,soln%n_grd,soln%n_turb, grid%dxdt,grid%dydt,    &
                    grid%dzdt)
    endif

    call ptoe(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)

    call my_alloc_ptr(dfdminf,design%nfunctions)

! Compute the cost function piece

    call dfdmach( dfdminf, grid%elem(1)%c2n, grid%x, grid%y, grid%z,           &
                  soln%q_dof, soln%amut, grid%elem(1)%ncell, grid%nnodes01,    &
                  grid%nbound, grid%bc, design, soln%totforce(1), soln%bcforce,&
                  soln%n_tot )

! If time-dependent, we do not want the residual contribution at the initial
! state - zero it out

    if ( itime > 0 .and. physical_timestep == 1 .and. subiteration == 0 ) then
      do j = 1, design%nfunctions
        productD(j) = my_0
      end do
    endif

! If time-dependent, we only want cost function contributions from specified
! timesteps

    factor = my_0
    add_or_not : if ( itime == 0 ) then
      factor = my_1
    else add_or_not
      do k = 1, design%nfunctions
        apply_dfdm = timestep_contributes(design%function_data(k)%timesteps,   &
                                          physical_timestep)
        if ( apply_dfdm ) factor(k) = my_1

! Find any windowing factor

        if ( windowing ) then
          call find_window_value(design%function_data(k)%timesteps,            &
                                 physical_timestep,window_value)
          factor(k) = factor(k)*window_value
        endif

      end do
    endif add_or_not

!  Now all we need to do is add the contribution
!  from the df/dX term and we'll have the answer

!  We have to flip the sign on the matrix-vector
!  product, since we're subtracting it.

    do j = 1, design%nfunctions
      dLdX(j) = productD(j) + dfdminf(j)*factor(j)
    end do

    deallocate(dfdminf)
    deallocate(productD)

! Reduce the results

    mach_fcn_loop : do j = 1, design%nfunctions
      call lmpi_reduce(dLdX(j),tempvar)
      call lmpi_bcast(tempvar)
      dLdX(j) = tempvar
      if(lmpi_master) then
        write(*,'(a,e23.14)') 'Reduced Mach Number derivative = ',dLdX(j)
        write(67,'(a,e23.14)') 'Reduced Mach Number derivative = ',dLdX(j)
      endif
    end do mach_fcn_loop

  end subroutine sensmach


!================================= DRDMACHINV ================================80
!
!  This routine is for computing dR/d(Mach) for the inviscid residual
!  Also multiply by lambda
!
!=============================================================================80

  subroutine drdmachinv(nnodes0,nbnode,qnode,rlam,coltag,ibnode,               &
                        bxn,byn,bzn,dLdX,nfunctions,adim,n_tot,nnodes01)

    use ivals,           only : u0,v0,w0,c0,s0
    use info_depr,       only : alpha,yaw
    use kinddefs,        only : dp
    use fluid,           only : gamma,gm1

    integer, intent(in) :: nnodes0, adim, n_tot, nbnode, nfunctions
    integer, intent(in) :: nnodes01

    integer, dimension(nbnode), intent(in) :: ibnode

    real(dp), dimension(n_tot,nnodes01),          intent(in)    :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions),intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),           intent(in)    :: coltag
    real(dp), dimension(nbnode),                  intent(in)    :: bxn,byn,bzn
    real(dp), dimension(nfunctions),              intent(inout) :: dLdX

    integer                                            :: i,inode,j

    real(dp)    :: rlen,rhoi,ui,vi
    real(dp)    :: wi,unormi,unormo
    real(dp)    :: a2,a,ai,rplus,rminus
    real(dp)    :: unorm,u,v,w,s,rho,p
    real(dp)    :: e,ubar,duoutdm
    real(dp)    :: dvoutdm,dwoutdm
    real(dp)    :: dunormodm,drminusdm
    real(dp)    :: dadm,drhodm,dunormdm
    real(dp)    :: dudm,dvdm,dwdm
    real(dp)    :: dubardm,dpdm,du2dm
    real(dp)    :: dv2dm,dw2dm
    real(dp)    :: du2pv2pw2dm,dedm
    real(dp)    :: dr1dm,dr2dm,dr3dm
    real(dp)    :: dr4dm,dr5dm,term1
    real(dp)    :: term2,term3,term4
    real(dp)    :: term5,xgm1,pi,conv
    real(dp)    :: ca,sa,cy,sy,uout
    real(dp)    :: vout,wout,cout
    real(dp)    :: xnorm,ynorm,znorm

  continue

    xgm1 = 1.0_dp/gm1

    pi = 4.0_dp*atan(1.0_dp)
    conv = 180.0_dp/pi
    ca = cos( alpha/conv )
    sa = sin( alpha/conv )
    cy = cos( yaw/conv )
    sy = sin( yaw/conv )

    do i = 1,nbnode
      uout = u0
      vout = v0
      wout = w0
      cout = c0
      inode   = ibnode(i)

! Calculate R+ and R-
! Then get the normal velocity and the
! speed of sound on the boundary

      xnorm   = bxn(i)
      ynorm   = byn(i)
      znorm   = bzn(i)
      rlen    = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)
      xnorm   = xnorm/rlen
      ynorm   = ynorm/rlen
      znorm   = znorm/rlen
      rhoi    = qnode(1,inode)
      ui      = qnode(2,inode)
      vi      = qnode(3,inode)
      wi      = qnode(4,inode)
      unormi  = ui*xnorm + vi*ynorm + wi*znorm
      unormo  = uout*xnorm + vout*ynorm + wout*znorm
      a2      = gamma*qnode(5,inode)/qnode(1,inode)
      a       = sqrt(a2)
      ai      = a
      rplus   = unormi + 2.0_dp*a/gm1
      rminus  = unormo - 2.0_dp*cout/gm1

!  Note that the following 2 "if" cases were not taken into
!  account for the dR/dX terms that follow shortly.  I.e.,
!  no supersonic in/outflow for now

      if(unormi > 1.0_dp)rminus=unormi - 2.0_dp*a/gm1
      if(unormi < -1.0_dp)rplus = unormo + 2.0_dp*cout/gm1

      unorm   = 0.5_dp*(rplus + rminus)
      a       = 0.25_dp*gm1*(rplus - rminus)

! If unorm > 0 this is outflow: take variables from inside
! If unorm < 0 this is inflow:  take variables from outside

      if(unorm > 0.0_dp)then
        u = qnode(2,inode) + xnorm*(unorm - unormi)
        v = qnode(3,inode) + ynorm*(unorm - unormi)
        w = qnode(4,inode) + znorm*(unorm - unormi)
        s = ai*ai/(gamma*rhoi**gm1)
      else
        u = uout + xnorm*(unorm - unormo)
        v = vout + ynorm*(unorm - unormo)
        w = wout + znorm*(unorm - unormo)
        s = s0
      end if
      rho  = (a*a/(gamma*s))**xgm1
      p    = rho*a*a/gamma
      e    = p/gm1 + 0.5_dp*rho*(u*u + v*v + w*w)
      ubar = xnorm*u + ynorm*v + znorm*w

!  Now let's form the necessary derivatives and chain rules

      duoutdm = ca * cy
      dvoutdm = - sy
      dwoutdm = sa * cy
      dunormodm = xnorm*duoutdm + ynorm*dvoutdm + znorm*dwoutdm

      drminusdm = dunormodm

      dadm = -0.25_dp*gm1*drminusdm

      drhodm = xgm1*(a*a/(gamma*s))**(xgm1-1.0_dp)*2.0_dp*a/gamma/s*dadm

      dunormdm = 0.5_dp*drminusdm

      if(unorm > 0.0_dp) then
        dudm = xnorm*dunormdm
        dvdm = ynorm*dunormdm
        dwdm = znorm*dunormdm
      else
        dudm = ca*cy + xnorm*dunormdm - xnorm*dunormodm
        dvdm = -sy + ynorm*dunormdm - ynorm*dunormodm
        dwdm = sa*cy + znorm*dunormdm - znorm*dunormodm
      endif

      dubardm = xnorm*dudm + ynorm*dvdm + znorm*dwdm

      dpdm = 1.0_dp / gamma * (rho*2.0_dp*a*dadm + a*a*drhodm)

      du2dm = 2.0_dp * u * dudm
      dv2dm = 2.0_dp * v * dvdm
      dw2dm = 2.0_dp * w * dwdm
      du2pv2pw2dm = du2dm + dv2dm + dw2dm

      dedm = 1.0_dp / gm1 * dpdm +                                             &
             0.5_dp*(rho*du2pv2pw2dm + (u*u + v*v + w*w)*drhodm)

!  Finally, we can form the gradient of Res wrt Mach number

      dr1dm = rlen*(rho*dubardm + ubar*drhodm)
      dr2dm = rlen*(rho*(u*dubardm + ubar*dudm) + u*ubar*drhodm +xnorm*dpdm)
      dr3dm = rlen*(rho*(v*dubardm + ubar*dvdm) + v*ubar*drhodm +ynorm*dpdm)
      dr4dm = rlen*(rho*(w*dubardm + ubar*dwdm) + w*ubar*drhodm +znorm*dpdm)
      dr5dm = rlen*((e+p)*dubardm + ubar*(dedm + dpdm))

!  Now we can form this node's contribution to the
!  matrix-vector product in the sensitivity derivative

      do j = 1, nfunctions
        term1 = dr1dm * coltag(1,inode)*rlam(1,inode,j)
        term2 = dr2dm * coltag(2,inode)*rlam(2,inode,j)
        term3 = dr3dm * coltag(3,inode)*rlam(3,inode,j)
        term4 = dr4dm * coltag(4,inode)*rlam(4,inode,j)
        term5 = dr5dm * coltag(5,inode)*rlam(5,inode,j)

!  Add to the current total

        if(inode <= nnodes0) dLdX(j) = dLdX(j) + term1+term2+term3+term4+term5
      end do

    end do

  end subroutine drdmachinv


!================================= DRDMACHV ==================================80
!
!  This routine is for computing dR/d(Mach) for the viscous residual
!  Also multiply by lambda
!
!  Essentially what we do here is set the Mach number equal
!  to 1.0 throughout this routine
!
!=============================================================================80

  subroutine drdmachv(nnodes0,ncell,                                           &
                      c2n,x,y,z,qnode,amut,dLdX,rlam,coltag,nbound,bc,         &
                      nfunctions, adim, n_tot, nnodes01)

    use info_depr,     only : re, tref
    use fluid,         only : gm1, gamma, sutherland_constant, prandtl
    use bc_types,      only : bcgrid_type
    use bc_names,      only : farfield_riem
    use kinddefs,      only : dp
    use design_types,  only : max_functions

    integer, intent(in) :: nnodes0, adim, n_tot
    integer, intent(in) :: ncell, nfunctions
    integer, intent(in) :: nbound, nnodes01

    integer,     dimension(4,ncell),     intent(in)    :: c2n

    integer                                            :: n,ib,j
    integer                                            :: node1,node2,node3
    integer                                            :: node4

    real(dp), dimension(n_tot,nnodes01),          intent(in) :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions),intent(in) :: rlam
    real(dp), dimension(nnodes01),                intent(in) :: amut
    real(dp), dimension(nnodes01),                intent(in) :: x,y,z
    real(dp), dimension(adim,nnodes01),           intent(in) :: coltag

    real(dp), dimension(nfunctions),    intent(inout) :: dLdX

    real(dp)                                           :: nx1,nx2,nx3,nx4
    real(dp)                                           :: ny1,ny2,ny3,ny4
    real(dp)                                           :: nz1,nz2,nz3,nz4
    real(dp)                                           :: cstar,xmr,c43
    real(dp)                                           :: c23,cgp

    real(dp)                                           :: u1,u2,u3,u4
    real(dp)                                           :: x1,x2,x3,x4
    real(dp)                                           :: y1,y2,y3,y4
    real(dp)                                           :: z1,z2,z3,z4
    real(dp)                                           :: v1,v2,v3,v4,w1,w2
    real(dp)                                           :: w3,w4,t1,t2,t3,t4
    real(dp)                                           :: rmu1,rmu2,rmu3,rmu4
    real(dp)                                           :: rmu,umu,vmu,wmu
    real(dp)                                           :: const,ux,vx,wx,ax
    real(dp)                                           :: uy,vy,wy,ay
    real(dp)                                           :: uz,vz,wz,az
    real(dp)                                           :: e2a,e2b,e2c,e3a,e3b
    real(dp)                                           :: e3c,e4a,e4b,e4c,e5a
    real(dp)                                           :: e5b,e5c,vol

    real(dp), dimension(max_functions)    :: rlam12,rlam13,rlam14
    real(dp), dimension(max_functions)    :: rlam15,rlam22,rlam23
    real(dp), dimension(max_functions)    :: rlam24,rlam25,rlam32
    real(dp), dimension(max_functions)    :: rlam33,rlam34,rlam35
    real(dp), dimension(max_functions)    :: rlam42,rlam43,rlam44
    real(dp), dimension(max_functions)    :: rlam45

    type(bcgrid_type), dimension(nbound),intent(in)    :: bc

    real(dp), parameter    :: my_3 = 3.0_dp

    continue

    cstar = sutherland_constant/tref

    xmr = 1.0_dp/Re
    c43 = 4.0_dp/3.0_dp*xmr
    c23 = 2.0_dp/3.0_dp*xmr
    cgp = xmr/(gm1*prandtl)

! Loop over all the cells and calculate viscous flux

      do n = 1, ncell

        node1 = c2n(1,n)
        node2 = c2n(2,n)
        node3 = c2n(3,n)
        node4 = c2n(4,n)

        do j = 1, nfunctions
          rlam12(j) = coltag(2,node1)*rlam(2,node1,j)
          rlam13(j) = coltag(3,node1)*rlam(3,node1,j)
          rlam14(j) = coltag(4,node1)*rlam(4,node1,j)
          rlam15(j) = coltag(5,node1)*rlam(5,node1,j)

          rlam22(j) = coltag(2,node2)*rlam(2,node2,j)
          rlam23(j) = coltag(3,node2)*rlam(3,node2,j)
          rlam24(j) = coltag(4,node2)*rlam(4,node2,j)
          rlam25(j) = coltag(5,node2)*rlam(5,node2,j)

          rlam32(j) = coltag(2,node3)*rlam(2,node3,j)
          rlam33(j) = coltag(3,node3)*rlam(3,node3,j)
          rlam34(j) = coltag(4,node3)*rlam(4,node3,j)
          rlam35(j) = coltag(5,node3)*rlam(5,node3,j)

          rlam42(j) = coltag(2,node4)*rlam(2,node4,j)
          rlam43(j) = coltag(3,node4)*rlam(3,node4,j)
          rlam44(j) = coltag(4,node4)*rlam(4,node4,j)
          rlam45(j) = coltag(5,node4)*rlam(5,node4,j)
        end do

        x1 = x(node1)
        x2 = x(node2)
        x3 = x(node3)
        x4 = x(node4)

        y1 = y(node1)
        y2 = y(node2)
        y3 = y(node3)
        y4 = y(node4)

        z1 = z(node1)
        z2 = z(node2)
        z3 = z(node3)
        z4 = z(node4)

!   Lets get outward normals

        nx1 = 0.5_dp*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
        ny1 = 0.5_dp*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
        nz1 = 0.5_dp*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

        nx2 = 0.5_dp*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
        ny2 = 0.5_dp*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
        nz2 = 0.5_dp*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

        nx3 = 0.5_dp*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
        ny3 = 0.5_dp*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
        nz3 = 0.5_dp*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

        nx4 = -nx1 -nx2 -nx3
        ny4 = -ny1 -ny2 -ny3
        nz4 = -nz1 -nz2 -nz3

! Compute cell volume

        vol = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                     &
              -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                     &
              +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))/6.0_dp

! Compute cell averaged quantities

        u1 = qnode(2,node1)
        u2 = qnode(2,node2)
        u3 = qnode(2,node3)
        u4 = qnode(2,node4)

        v1 = qnode(3,node1)
        v2 = qnode(3,node2)
        v3 = qnode(3,node3)
        v4 = qnode(3,node4)

        w1 = qnode(4,node1)
        w2 = qnode(4,node2)
        w3 = qnode(4,node3)
        w4 = qnode(4,node4)

        T1 = gamma*qnode(5,node1)/qnode(1,node1)
        T2 = gamma*qnode(5,node2)/qnode(1,node2)
        T3 = gamma*qnode(5,node3)/qnode(1,node3)
        T4 = gamma*qnode(5,node4)/qnode(1,node4)

        rmu1 = viscosity_law( cstar, T1 ) + amut(node1)
        rmu2 = viscosity_law( cstar, T2 ) + amut(node2)
        rmu3 = viscosity_law( cstar, T3 ) + amut(node3)
        rmu4 = viscosity_law( cstar, T4 ) + amut(node4)

        rmu = 0.25_dp*(rmu1 + rmu2 + rmu3 + rmu4)
        umu = 0.25_dp*(u1*rmu1 + u2*rmu2 + u3*rmu3 + u4*rmu4)
        vmu = 0.25_dp*(v1*rmu1 + v2*rmu2 + v3*rmu3 + v4*rmu4)
        wmu = 0.25_dp*(w1*rmu1 + w2*rmu2 + w3*rmu3 + w4*rmu4)

! Compute Gradients

        const = 1.0_dp/(3.0_dp*vol)
        ux = -((u4 - u1)*nx4 + (u2 - u1)*nx2 + (u3 - u1)*nx3)*const
        vx = -((v4 - v1)*nx4 + (v2 - v1)*nx2 + (v3 - v1)*nx3)*const
        wx = -((w4 - w1)*nx4 + (w2 - w1)*nx2 + (w3 - w1)*nx3)*const
        ax = -((T4 - T1)*nx4 + (T2 - T1)*nx2 + (T3 - T1)*nx3)*const

        uy = -((u4 - u1)*ny4 + (u2 - u1)*ny2 + (u3 - u1)*ny3)*const
        vy = -((v4 - v1)*ny4 + (v2 - v1)*ny2 + (v3 - v1)*ny3)*const
        wy = -((w4 - w1)*ny4 + (w2 - w1)*ny2 + (w3 - w1)*ny3)*const
        ay = -((T4 - T1)*ny4 + (T2 - T1)*ny2 + (T3 - T1)*ny3)*const

        uz = -((u4 - u1)*nz4 + (u2 - u1)*nz2 + (u3 - u1)*nz3)*const
        vz = -((v4 - v1)*nz4 + (v2 - v1)*nz2 + (v3 - v1)*nz3)*const
        wz = -((w4 - w1)*nz4 + (w2 - w1)*nz2 + (w3 - w1)*nz3)*const
        az = -((T4 - T1)*nz4 + (T2 - T1)*nz2 + (T3 - T1)*nz3)*const

! Update residual

        e2a = c43*ux - c23*vy - c23*wz
        e2b = xmr*(uy + vx)
        e2c = xmr*(uz + wx)

        e3a = xmr*(uy + vx)
        e3b = c43*vy - c23*ux - c23*wz
        e3c = xmr*(vz + wy)

        e4a = xmr*(uz + wx)
        e4b = xmr*(vz + wy)
        e4c = c43*wz - c23*ux - c23*vy

        e5a = umu*(c43*ux - c23*vy - c23*wz)                                   &
            + vmu*xmr*(uy + vx)                                                &
            + wmu*xmr*(uz + wx)                                                &
            + rmu*cgp*ax
        e5b = vmu*(c43*vy - c23*ux - c23*wz)                                   &
            + umu*xmr*(uy + vx)                                                &
            + wmu*xmr*(vz + wy)                                                &
            + rmu*cgp*ay
        e5c = wmu*(c43*wz - c23*ux - c23*vy)                                   &
            + umu*xmr*(uz + wx)                                                &
            + vmu*xmr*(vz + wy)                                                &
            + rmu*cgp*az

!  For the parallel version, only keep those contributions that
!  come from level-0 nodes.  Zero out the lambdas corresponding
!  to off-processor nodes.

        do j = 1, nfunctions
          if(node1 > nnodes0) then
            rlam12(j) = 0.0_dp
            rlam13(j) = 0.0_dp
            rlam14(j) = 0.0_dp
            rlam15(j) = 0.0_dp
          endif

          if(node2 > nnodes0) then
            rlam22(j) = 0.0_dp
            rlam23(j) = 0.0_dp
            rlam24(j) = 0.0_dp
            rlam25(j) = 0.0_dp
          endif

          if(node3 > nnodes0) then
            rlam32(j) = 0.0_dp
            rlam33(j) = 0.0_dp
            rlam34(j) = 0.0_dp
            rlam35(j) = 0.0_dp
          endif

          if(node4 > nnodes0) then
            rlam42(j) = 0.0_dp
            rlam43(j) = 0.0_dp
            rlam44(j) = 0.0_dp
            rlam45(j) = 0.0_dp
          endif

          dLdX(j) = dLdX(j) - rlam12(j)*rmu*(nx1*e2a + ny1*e2b + nz1*e2c)/my_3
          dLdX(j) = dLdX(j) - rlam13(j)*rmu*(nx1*e3a + ny1*e3b + nz1*e3c)/my_3
          dLdX(j) = dLdX(j) - rlam14(j)*rmu*(nx1*e4a + ny1*e4b + nz1*e4c)/my_3
          dLdX(j) = dLdX(j) - rlam15(j)*(nx1*e5a + ny1*e5b + nz1*e5c)/my_3

          dLdX(j) = dLdX(j) - rlam22(j)*rmu*(nx2*e2a + ny2*e2b + nz2*e2c)/my_3
          dLdX(j) = dLdX(j) - rlam23(j)*rmu*(nx2*e3a + ny2*e3b + nz2*e3c)/my_3
          dLdX(j) = dLdX(j) - rlam24(j)*rmu*(nx2*e4a + ny2*e4b + nz2*e4c)/my_3
          dLdX(j) = dLdX(j) - rlam25(j)*(nx2*e5a + ny2*e5b + nz2*e5c)/my_3

          dLdX(j) = dLdX(j) - rlam32(j)*rmu*(nx3*e2a + ny3*e2b + nz3*e2c)/my_3
          dLdX(j) = dLdX(j) - rlam33(j)*rmu*(nx3*e3a + ny3*e3b + nz3*e3c)/my_3
          dLdX(j) = dLdX(j) - rlam34(j)*rmu*(nx3*e4a + ny3*e4b + nz3*e4c)/my_3
          dLdX(j) = dLdX(j) - rlam35(j)*(nx3*e5a + ny3*e5b + nz3*e5c)/my_3

          dLdX(j) = dLdX(j) - rlam42(j)*rmu*(nx4*e2a + ny4*e2b + nz4*e2c)/my_3
          dLdX(j) = dLdX(j) - rlam43(j)*rmu*(nx4*e3a + ny4*e3b + nz4*e3c)/my_3
          dLdX(j) = dLdX(j) - rlam44(j)*rmu*(nx4*e4a + ny4*e4b + nz4*e4c)/my_3
          dLdX(j) = dLdX(j) - rlam45(j)*(nx4*e5a + ny4*e5b + nz4*e5c)/my_3
        end do

      end do

! Close off the boundary contours

    do ib = 1,nbound
      if(bc(ib)%ibc == farfield_riem) then
        call bc_drdmachv(ncell,bc(ib)%nbnode,bc(ib)%nbfacet,                   &
                         bc(ib)%face_bit,                                      &
                         bc(ib)%ibnode,bc(ib)%f2ntb,coltag,qnode,rlam,x,y,z,   &
                         c2n,amut,dLdX,nfunctions,adim,n_tot,nnodes01)
      endif
    end do

  end subroutine drdmachv


!================================ BC_DRDMACHV ================================80
!
! Closes off viscous flux terms on boundary
!
!=============================================================================80

  subroutine bc_drdmachv(ncell,nbnode,nbfacet,                                 &
                         face_bit,ibnode,f2ntb,coltag,qnode,rlam,x,y,z,c2n,    &
                         amut,dLdX,nfunctions,adim,n_tot,nnodes01)

    use info_depr,     only : tref,re
    use fluid,         only : gm1,gamma,sutherland_constant, prandtl
    use kinddefs,      only : dp
    use design_types,  only : max_functions

    integer, intent(in) :: ncell,nbnode, adim, n_tot, nnodes01
    integer, intent(in) :: nbfacet,nfunctions

    integer,     dimension(nbfacet),     intent(in)    :: face_bit
    integer,     dimension(nbnode),      intent(in)    :: ibnode
    integer,     dimension(nbfacet,5),   intent(in)    :: f2ntb
    integer,     dimension(4,ncell),     intent(in)    :: c2n

    integer                                            :: icell,n,j
    integer                                            :: node1,node2,node3
    integer                                            :: node4

    real(dp), dimension(nnodes01),                intent(in) :: x,y,z
    real(dp), dimension(nnodes01),                intent(in) :: amut
    real(dp), dimension(adim,nnodes01),           intent(in) :: coltag
    real(dp), dimension(n_tot,nnodes01),          intent(in) :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions),intent(in) :: rlam

    real(dp), dimension(nfunctions),    intent(inout) :: dLdX

    real(dp)                                           :: nx1,nx2,nx3,nx4
    real(dp)                                           :: ny1,ny2,ny3,ny4
    real(dp)                                           :: nz1,nz2,nz3,nz4
    real(dp)                                           :: cstar,xmr,c43
    real(dp)                                           :: c23,cgp

    real(dp)                                           :: u1,u2,u3,u4
    real(dp)                                           :: x1,x2,x3,x4
    real(dp)                                           :: y1,y2,y3,y4
    real(dp)                                           :: z1,z2,z3,z4
    real(dp)                                           :: v1,v2,v3,v4,w1,w2
    real(dp)                                           :: w3,w4,t1,t2,t3,t4
    real(dp)                                           :: rmu1,rmu2,rmu3,rmu4
    real(dp)                                           :: rmu,umu,vmu,wmu
    real(dp)                                           :: const,ux,vx,wx,ax
    real(dp)                                           :: uy,vy,wy,ay
    real(dp)                                           :: uz,vz,wz,az
    real(dp)                                           :: vol
    real(dp)                                           :: xnorm,ynorm,znorm
    real(dp)                                           :: term2m,term3m,term4m
    real(dp)                                           :: term5m

    real(dp), dimension(max_functions)    :: rlam12,rlam13,rlam14
    real(dp), dimension(max_functions)    :: rlam15,rlam22,rlam23
    real(dp), dimension(max_functions)    :: rlam24,rlam25,rlam32
    real(dp), dimension(max_functions)    :: rlam33,rlam34,rlam35

    continue

    cstar = sutherland_constant/tref

    xmr = 1.0_dp/Re
    c43 = 4.0_dp/3.0_dp
    c23 = 2.0_dp/3.0_dp
    cgp = 1.0_dp/(gm1*prandtl)

      do n = 1, nbfacet

        if(face_bit(n) == 1) then

          icell = f2ntb(n,4)
          node1 = ibnode(f2ntb(n,1))
          node2 = ibnode(f2ntb(n,2))
          node3 = ibnode(f2ntb(n,3))
          node4 = c2n(1,icell) + c2n(2,icell) + c2n(3,icell)                   &
            + c2n(4,icell) - node1 - node2 - node3

          do j = 1, nfunctions
            rlam12(j) = coltag(2,node1)*rlam(2,node1,j)
            rlam13(j) = coltag(3,node1)*rlam(3,node1,j)
            rlam14(j) = coltag(4,node1)*rlam(4,node1,j)
            rlam15(j) = coltag(5,node1)*rlam(5,node1,j)

            rlam22(j) = coltag(2,node2)*rlam(2,node2,j)
            rlam23(j) = coltag(3,node2)*rlam(3,node2,j)
            rlam24(j) = coltag(4,node2)*rlam(4,node2,j)
            rlam25(j) = coltag(5,node2)*rlam(5,node2,j)

            rlam32(j) = coltag(2,node3)*rlam(2,node3,j)
            rlam33(j) = coltag(3,node3)*rlam(3,node3,j)
            rlam34(j) = coltag(4,node3)*rlam(4,node3,j)
            rlam35(j) = coltag(5,node3)*rlam(5,node3,j)
          end do

          x1 = x(node1)
          x2 = x(node2)
          x3 = x(node3)
          x4 = x(node4)

          y1 = y(node1)
          y2 = y(node2)
          y3 = y(node3)
          y4 = y(node4)

          z1 = z(node1)
          z2 = z(node2)
          z3 = z(node3)
          z4 = z(node4)

!   Lets get outward normals (nx_i is for the face opposite node i)

          nx1 = 0.5_dp*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
          ny1 = 0.5_dp*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
          nz1 = 0.5_dp*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

          nx2 = 0.5_dp*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
          ny2 = 0.5_dp*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
          nz2 = 0.5_dp*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

          nx3 = 0.5_dp*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
          ny3 = 0.5_dp*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
          nz3 = 0.5_dp*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

          nx4 = -nx1 -nx2 -nx3
          ny4 = -ny1 -ny2 -ny3
          nz4 = -nz1 -nz2 -nz3

! Compute cell volume

          vol = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                   &
                -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                   &
                +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))/6.0_dp

! Compute cell averaged quantities

          u1 = qnode(2,node1)
          u2 = qnode(2,node2)
          u3 = qnode(2,node3)
          u4 = qnode(2,node4)

          v1 = qnode(3,node1)
          v2 = qnode(3,node2)
          v3 = qnode(3,node3)
          v4 = qnode(3,node4)

          w1 = qnode(4,node1)
          w2 = qnode(4,node2)
          w3 = qnode(4,node3)
          w4 = qnode(4,node4)

          T1 = gamma*qnode(5,node1)/qnode(1,node1)
          T2 = gamma*qnode(5,node2)/qnode(1,node2)
          T3 = gamma*qnode(5,node3)/qnode(1,node3)
          T4 = gamma*qnode(5,node4)/qnode(1,node4)

          rmu1 = viscosity_law( cstar, T1 ) + amut(node1)
          rmu2 = viscosity_law( cstar, T2 ) + amut(node2)
          rmu3 = viscosity_law( cstar, T3 ) + amut(node3)
          rmu4 = viscosity_law( cstar, T4 ) + amut(node4)

          rmu = 0.25_dp*(rmu1 + rmu2 + rmu3 + rmu4)
          umu = 0.25_dp*(u1*rmu1 + u2*rmu2 + u3*rmu3 + u4*rmu4)
          vmu = 0.25_dp*(v1*rmu1 + v2*rmu2 + v3*rmu3 + v4*rmu4)
          wmu = 0.25_dp*(w1*rmu1 + w2*rmu2 + w3*rmu3 + w4*rmu4)

! Compute velocity gradients for icell

          const = -1.0_dp/(3.0_dp*vol)

          ux = const*((u1-u4)*nx1 + (u2-u4)*nx2 + (u3-u4)*nx3)
          uy = const*((u1-u4)*ny1 + (u2-u4)*ny2 + (u3-u4)*ny3)
          uz = const*((u1-u4)*nz1 + (u2-u4)*nz2 + (u3-u4)*nz3)

          vx = const*((v1-v4)*nx1 + (v2-v4)*nx2 + (v3-v4)*nx3)
          vy = const*((v1-v4)*ny1 + (v2-v4)*ny2 + (v3-v4)*ny3)
          vz = const*((v1-v4)*nz1 + (v2-v4)*nz2 + (v3-v4)*nz3)

          wx = const*((w1-w4)*nx1 + (w2-w4)*nx2 + (w3-w4)*nx3)
          wy = const*((w1-w4)*ny1 + (w2-w4)*ny2 + (w3-w4)*ny3)
          wz = const*((w1-w4)*nz1 + (w2-w4)*nz2 + (w3-w4)*nz3)

          ax = const*((T1-T4)*nx1 + (T2-T4)*nx2 + (T3-T4)*nx3)
          ay = const*((T1-T4)*ny1 + (T2-T4)*ny2 + (T3-T4)*ny3)
          az = const*((T1-T4)*nz1 + (T2-T4)*nz2 + (T3-T4)*nz3)

          xnorm = nx4/3.0_dp
          ynorm = ny4/3.0_dp
          znorm = nz4/3.0_dp

          term2M = xmr*rmu*(xnorm*(c43*ux - c23*(vy + wz))                     &
                           +ynorm*(uy + vx)                                    &
                           +znorm*(uz + wx))

          term3M = xmr*rmu*(xnorm*(uy + vx)                                    &
                           +ynorm*(c43*vy - c23*(ux + wz))                     &
                           +znorm*(vz + wy))

          term4M = xmr*rmu*(xnorm*(uz + wx)                                    &
                           +ynorm*(vz + wy)                                    &
                           +znorm*(c43*wz - c23*(ux + vy)))

          term5M = xmr*(xnorm*(umu*(c43*ux - c23*(vy + wz))                    &
                              +vmu*(uy + vx)                                   &
                              +wmu*(uz + wx)                                   &
                              +rmu*cgp*ax)                                     &
                       +ynorm*(umu*(uy + vx)                                   &
                              +vmu*(c43*vy - c23*(ux + wz))                    &
                              +wmu*(vz + wy)                                   &
                              +rmu*cgp*ay)                                     &
                       +znorm*(umu*(uz + wx)                                   &
                              +vmu*(vz + wy)                                   &
                              +wmu*(c43*wz - c23*(ux + vy))                    &
                              +rmu*cgp*az))

          do j = 1, nfunctions
            dLdX(j) = dLdX(j) - rlam12(j)*term2M
            dLdX(j) = dLdX(j) - rlam13(j)*term3M
            dLdX(j) = dLdX(j) - rlam14(j)*term4M
            dLdX(j) = dLdX(j) - rlam15(j)*term5M

            dLdX(j) = dLdX(j) - rlam22(j)*term2M
            dLdX(j) = dLdX(j) - rlam23(j)*term3M
            dLdX(j) = dLdX(j) - rlam24(j)*term4M
            dLdX(j) = dLdX(j) - rlam25(j)*term5M

            dLdX(j) = dLdX(j) - rlam32(j)*term2M
            dLdX(j) = dLdX(j) - rlam33(j)*term3M
            dLdX(j) = dLdX(j) - rlam34(j)*term4M
            dLdX(j) = dLdX(j) - rlam35(j)*term5M
          end do

        endif

      end do

  end subroutine bc_drdmachv


!================================ DFDMACH ====================================80
!
! Calculates the gradient of the cost function w.r.t. Mach number
!
!=============================================================================80

  subroutine dfdmach(dfdminf,c2n,x,y,z,qnode,amut,ncell,                       &
                     nnodes01,nbound,bc,design,force,bcforce, n_tot)

    use design_types,    only : design_type
    use info_depr,       only : xmach, ivisc
    use lmpi,            only : lmpi_master
    use bc_types,        only : bcgrid_type
    use bc_names,        only : viscous_solid, bc_used_for_force_calculation   &
                              , bc_is_flow_through
    use kinddefs,        only : dp
    use force_types,     only : force_type
    use forces,          only : cl_id, cd_id, clp_id, cdp_id,                  &
                                cmx_id, cmy_id, cmz_id, cmxp_id, cmyp_id,      &
                                cmzp_id, clv_id, cdv_id, cmxv_id, cmyv_id,     &
                                cmzv_id
    use refgeom,         only : sref,bref,cref
    use cut_types,       only : cut_cell_activated, cut_force
    integer, intent(in) :: ncell, nnodes01, n_tot, nbound

    integer, dimension(4,ncell), intent(in) :: c2n

    real(dp), dimension(n_tot,nnodes01),  intent(in) :: qnode
    real(dp), dimension(nnodes01),        intent(in) :: amut,x,y,z

    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(design_type),                    intent(in) :: design
    type(force_type),                     intent(in) :: force
    type(force_type),  dimension(nbound), intent(in) :: bcforce

    real(dp), dimension(design%nfunctions),    intent(out) :: dfdminf

    integer :: ib, j, k

    real(dp)    :: clvm,cdvm,width,average,base,const,factor
    real(dp)    :: cmxvm,cmyvm,cmzvm
    real(dp)    :: weight, target, power
    real(dp)    :: cl,cd,clp,cdp,cmx,cmy,cmz,cmxp,cmyp,cmzp
    real(dp)    :: clv,cdv,cmxv,cmyv,cmzv

    real(dp), parameter    :: my_1 = 1.0_dp
    real(dp), parameter    :: my_2 = 2.0_dp

  continue

    const = 0.0_dp

    dfdminf = 0.0_dp

!  Since this is a constant and doesn't depend on geometry, only add
!  it's contribution if we're on rank 0 (this is arbitrary)

    only_master_contributes : if(lmpi_master) then

      inv_fcn_loop : do j = 1, design%nfunctions

        do ib = 1, nbound
          if( bc_is_flow_through( bc(ib)%ibc ) ) cycle
          if( bc_used_for_force_calculation( bc(ib)%ibc ) ) then

            component_loop : do k = 1, design%function_data(j)%ncomponents

              if(design%function_data(j)%component_data(k)%boundary_id==0)then
                if ( .not. bcforce(ib)%add_to_total ) cycle component_loop
                cd   = force%cd
                cdp  = force%cdp
                cl   = force%cl
                clp  = force%clp
                cmx  = force%cmx
                cmxp = force%cmxp
                cmy  = force%cmy
                cmyp = force%cmyp
                cmz  = force%cmz
                cmzp = force%cmzp
         else if(design%function_data(j)%component_data(k)%boundary_id==ib)then
                cd   = bcforce(ib)%cd
                cdp  = bcforce(ib)%cdp
                cl   = bcforce(ib)%cl
                clp  = bcforce(ib)%clp
                cmx  = bcforce(ib)%cmx
                cmxp = bcforce(ib)%cmxp
                cmy  = bcforce(ib)%cmy
                cmyp = bcforce(ib)%cmyp
                cmz  = bcforce(ib)%cmz
                cmzp = bcforce(ib)%cmzp
              else
                cycle component_loop
              endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1
              case ( cdp_id )
                if (.not.design%function_data(j)%averaging) base = cdp-target
                const = my_1
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1
              case ( clp_id )
                if (.not.design%function_data(j)%averaging) base = clp-target
                const = my_1
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1
              case ( cmxp_id )
                if (.not.design%function_data(j)%averaging) base = cmxp-target
                const = my_1
              case ( cmyp_id )
                if (.not.design%function_data(j)%averaging) base = cmyp-target
                const = my_1
              case ( cmzp_id )
                if (.not.design%function_data(j)%averaging) base = cmzp-target
                const = my_1
              case default
                cycle component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

! In the following chunk of code, the forces used in the dfdminf sums
! must be from the current boundary group, rather than the entire domain
! because of the special way this differentiation is done (the force ends
! up in the equation for the derivative).  Otherwise you get a multiple of
! the actual result you're supposed to get

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cdp
              case ( cdp_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cdp
              case ( cl_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%clp
              case ( clp_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%clp
              case ( cmx_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cmxp
              case ( cmxp_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cmxp
              case ( cmy_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cmyp
              case ( cmyp_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cmyp
              case ( cmz_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cmzp
              case ( cmzp_id )
                dfdminf(j) = dfdminf(j) - my_2/xmach*factor*bcforce(ib)%cmzp
              case default
                cycle component_loop
              end select

            end do component_loop

          endif
        end do

      end do inv_fcn_loop

    endif only_master_contributes

!  Get skin friction piece

    if(ivisc > 0) then

      visc_fcn_loop : do j = 1, design%nfunctions

        do ib = 1, nbound

          clvM = 0.0_dp
          cdvM = 0.0_dp
          cmxvM = 0.0_dp
          cmyvM = 0.0_dp
          cmzvM = 0.0_dp

          if(bc(ib)%ibc == viscous_solid) then
            call skinfricm(c2n,x,y,z,qnode,amut,ncell,                         &
                           clvM,cdvM,cmxvM,cmyvM,cmzvM,nnodes01,bc(ib)%nbnode, &
                           bc(ib)%nbfacet,bc(ib)%face_bit,                     &
                           bc(ib)%ibnode,bc(ib)%f2ntb, n_tot)

            component_loopv : do k = 1, design%function_data(j)%ncomponents

              if(design%function_data(j)%component_data(k)%boundary_id==0)then
                if ( .not. bcforce(ib)%add_to_total ) cycle component_loopv
                cd   = force%cd
                cdv  = force%cdv
                cl   = force%cl
                clv  = force%clv
                cmx  = force%cmx
                cmxv = force%cmxv
                cmy  = force%cmy
                cmyv = force%cmyv
                cmz  = force%cmz
                cmzv = force%cmzv
         else if(design%function_data(j)%component_data(k)%boundary_id==ib)then
                cd   = bcforce(ib)%cd
                cdv  = bcforce(ib)%cdv
                cl   = bcforce(ib)%cl
                clv  = bcforce(ib)%clv
                cmx  = bcforce(ib)%cmx
                cmxv = bcforce(ib)%cmxv
                cmy  = bcforce(ib)%cmy
                cmyv = bcforce(ib)%cmyv
                cmz  = bcforce(ib)%cmz
                cmzv = bcforce(ib)%cmzv
              else
                cycle component_loopv
              endif

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1/sref
              case ( cdv_id )
                if (.not.design%function_data(j)%averaging) base = cdv-target
                const = my_1/sref
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1/sref
              case ( clv_id )
                if (.not.design%function_data(j)%averaging) base = clv-target
                const = my_1/sref
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1/sref/bref
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1/sref/cref
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1/sref/bref
              case ( cmxv_id )
                if (.not.design%function_data(j)%averaging) base = cmxv-target
                const = my_1/sref/bref
              case ( cmyv_id )
                if (.not.design%function_data(j)%averaging) base = cmyv-target
                const = my_1/sref/cref
              case ( cmzv_id )
                if (.not.design%function_data(j)%averaging) base = cmzv-target
                const = my_1/sref/bref
              case default
                cycle component_loopv
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                dfdminf(j) = dfdminf(j) + factor*cdvM
              case ( cdv_id )
                dfdminf(j) = dfdminf(j) + factor*cdvM
              case ( cl_id )
                dfdminf(j) = dfdminf(j) + factor*clvM
              case ( clv_id )
                dfdminf(j) = dfdminf(j) + factor*clvM
              case ( cmx_id )
                dfdminf(j) = dfdminf(j) + factor*cmxvM
              case ( cmxv_id )
                dfdminf(j) = dfdminf(j) + factor*cmxvM
              case ( cmy_id )
                dfdminf(j) = dfdminf(j) + factor*cmyvM
              case ( cmyv_id )
                dfdminf(j) = dfdminf(j) + factor*cmyvM
              case ( cmz_id )
                dfdminf(j) = dfdminf(j) + factor*cmzvM
              case ( cmzv_id )
                dfdminf(j) = dfdminf(j) + factor*cmzvM
              case default
                cycle component_loopv
              end select

            end do component_loopv

          endif

        end do

      end do visc_fcn_loop

    endif

!  Get cut cell piece

    only_master_for_cut_cell : if( lmpi_master .and. cut_cell_activated ) then

      cd   = force%cd
      cdp  = force%cdp
      cl   = force%cl
      clp  = force%clp
      cmx  = force%cmx
      cmxp = force%cmxp
      cmy  = force%cmy
      cmyp = force%cmyp
      cmz  = force%cmz
      cmzp = force%cmzp

      cut_cell_fcn_loop : do j = 1, design%nfunctions

        cut_component_loop : do k = 1, design%function_data(j)%ncomponents

          whole:if(design%function_data(j)%component_data(k)%boundary_id==0)then

              weight  = design%function_data(j)%component_data(k)%weight
              target  = design%function_data(j)%component_data(k)%target
              power   = design%function_data(j)%component_data(k)%power
              width   = design%function_data(j)%timesteps(2) -                 &
                        design%function_data(j)%timesteps(1) + 1
              if ( .not.design%function_data(j)%averaging ) width = 1.0_dp
              average = design%function_data(j)%component_data(k)%value
              base    = average - target

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

              select case (design%function_data(j)%component_data(k)%name)
              case ( cd_id )
                if (.not.design%function_data(j)%averaging) base = cd-target
                const = my_1
              case ( cdp_id )
                if (.not.design%function_data(j)%averaging) base = cdp-target
                const = my_1
              case ( cl_id )
                if (.not.design%function_data(j)%averaging) base = cl-target
                const = my_1
              case ( clp_id )
                if (.not.design%function_data(j)%averaging) base = clp-target
                const = my_1
              case ( cmx_id )
                if (.not.design%function_data(j)%averaging) base = cmx-target
                const = my_1
              case ( cmy_id )
                if (.not.design%function_data(j)%averaging) base = cmy-target
                const = my_1
              case ( cmz_id )
                if (.not.design%function_data(j)%averaging) base = cmz-target
                const = my_1
              case ( cmxp_id )
                if (.not.design%function_data(j)%averaging) base = cmxp-target
                const = my_1
              case ( cmyp_id )
                if (.not.design%function_data(j)%averaging) base = cmyp-target
                const = my_1
              case ( cmzp_id )
                if (.not.design%function_data(j)%averaging) base = cmzp-target
                const = my_1
              case default
                cycle cut_component_loop
              end select

              factor = const*weight*power/width * base**(power-1.0_dp)

            select case (design%function_data(j)%component_data(k)%name)
            case ( cd_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cdp
            case ( cdp_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cdp
            case ( cl_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%clp
            case ( clp_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%clp
            case ( cmx_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cmxp
            case ( cmxp_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cmxp
            case ( cmy_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cmyp
            case ( cmyp_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cmyp
            case ( cmz_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cmzp
            case ( cmzp_id )
              dfdminf(j) = dfdminf(j) - my_2/xmach*factor*cut_force%cmzp
            case default
              cycle cut_component_loop
            end select

          end if whole
        end do cut_component_loop

      end do cut_cell_fcn_loop

    end if only_master_for_cut_cell

  end subroutine dfdmach


!================================ SKINFRICM ==================================80
!
!  This routine gets the derivative of the skin friction wrt
!  freestream Mach number
!
!=============================================================================80

  subroutine skinfricm(c2n,x,y,z,qnode,amut,ncell,                             &
                       clvM,cdvM,cmxvM,cmyvM,cmzvM,nnodes01,nbnode,nbfacet,    &
                       face_bit,ibnode,f2ntb, n_tot)

    use info_depr,       only : re, xmach, tref, alpha, yaw
    use fluid,           only : gm1, gamma, sutherland_constant
    use kinddefs,        only : dp
    use refgeom,         only : xmc,ymc,zmc

    integer,                             intent(in)  :: nbnode, n_tot
    integer,                             intent(in)  :: ncell
    integer,                             intent(in)  :: nnodes01
    integer,                             intent(in)  :: nbfacet

    integer,     dimension(4,ncell),     intent(in)  :: c2n
    integer,     dimension(nbfacet),     intent(in)  :: face_bit
    integer,     dimension(nbnode),      intent(in)  :: ibnode
    integer,     dimension(nbfacet,5),   intent(in)  :: f2ntb

    integer                                          :: n
    integer                                          :: node1,node2,node3
    integer                                          :: node4,icell

    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(nnodes01),        intent(in)    :: x,y,z,amut
    real(dp),                             intent(inout) :: clvm,cdvm
    real(dp),                             intent(inout) :: cmxvm,cmyvm,cmzvm

    real(dp)                                         :: clv,cdv,pi
    real(dp)                                         :: cmxv,cmyv,cmzv
    real(dp)                                         :: nxd, nyd, nzd
    real(dp)                                         :: nxl, nyl, nzl
    real(dp)                                         :: nx1,nx2,nx3,nx4
    real(dp)                                         :: ny1,ny2,ny3,ny4
    real(dp)                                         :: nz1,nz2,nz3,nz4
    real(dp)                                         :: cstar,xmr,c43,c23
    real(dp)                                         :: rho1,rho2,rho3,rho4
    real(dp)                                         :: u1,u2,u3,u4
    real(dp)                                         :: p1,p2,p3,p4
    real(dp)                                         :: x1,x2,x3,x4
    real(dp)                                         :: y1,y2,y3,y4
    real(dp)                                         :: z1,z2,z3,z4
    real(dp)                                         :: v1,v2,v3,v4,w1,w2
    real(dp)                                         :: w3,w4,t1,t2,t3,t4
    real(dp)                                         :: rmu1,rmu2,rmu3,rmu4
    real(dp)                                         :: rmu
    real(dp)                                         :: const,ux,vx,wx
    real(dp)                                         :: uy,vy,wy
    real(dp)                                         :: uz,vz,wz
    real(dp)                                         :: conv,vol,xnorm,ynorm
    real(dp)                                         :: znorm,termx,termxm
    real(dp)                                         :: termy,termym,termz
    real(dp)                                         :: termzm,forced,forcedm
    real(dp)                                         :: forcel,forcelm
    real(dp)                                         :: xmid,ymid,zmid

    continue

!  Some constants

    c43 = 4.0_dp/3.0_dp
    c23 = 2.0_dp/3.0_dp
    xmr = 1.0_dp/xmach / Re
    pi = acos(-1.0_dp)
    conv = 180.0_dp / pi

!  Initialize sums

    clv  = 0.0_dp
    cdv  = 0.0_dp
    cmxv  = 0.0_dp
    cmyv  = 0.0_dp
    cmzv  = 0.0_dp

    cstar = sutherland_constant/Tref

      do n = 1, nbfacet
        if(face_bit(n) == 1) then

          node1 = ibnode(f2ntb(n,1))
          node2 = ibnode(f2ntb(n,2))
          node3 = ibnode(f2ntb(n,3))

          icell = f2ntb(n,4)

          node4 = c2n(1,icell) + c2n(2,icell) + c2n(3,icell)                   &
                + c2n(4,icell) - node1 - node2 - node3

          x1 = x(node1)
          y1 = y(node1)
          z1 = z(node1)

          x2 = x(node2)
          y2 = y(node2)
          z2 = z(node2)

          x3 = x(node3)
          y3 = y(node3)
          z3 = z(node3)

          x4 = x(node4)
          y4 = y(node4)
          z4 = z(node4)

!   Lets get outward normals (nx_i is for the face opposite node i)

          nx1 = 0.5_dp*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
          ny1 = 0.5_dp*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
          nz1 = 0.5_dp*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

          nx2 = 0.5_dp*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
          ny2 = 0.5_dp*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
          nz2 = 0.5_dp*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

          nx3 = 0.5_dp*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
          ny3 = 0.5_dp*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
          nz3 = 0.5_dp*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

          nx4 = -nx1 -nx2 -nx3
          ny4 = -ny1 -ny2 -ny3
          nz4 = -nz1 -nz2 -nz3

! Compute cell volume

          vol = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                   &
                -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                   &
                +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))/6.0_dp

          rho1  = qnode(1,node1)
          u1    = qnode(2,node1)/rho1
          v1    = qnode(3,node1)/rho1
          w1    = qnode(4,node1)/rho1
          p1    = gm1*(qnode(5,node1) - .5_dp*rho1*(u1*u1 + v1*v1 + w1*w1))
          rho2  = qnode(1,node2)
          u2    = qnode(2,node2)/rho2
          v2    = qnode(3,node2)/rho2
          w2    = qnode(4,node2)/rho2
          p2    = gm1*(qnode(5,node2) - .5_dp*rho2*(u2*u2 + v2*v2 + w2*w2))
          rho3  = qnode(1,node3)
          u3    = qnode(2,node3)/rho3
          v3    = qnode(3,node3)/rho3
          w3    = qnode(4,node3)/rho3
          p3    = gm1*(qnode(5,node3) - .5_dp*rho3*(u3*u3 + v3*v3 + w3*w3))
          rho4  = qnode(1,node4)
          u4    = qnode(2,node4)/rho4
          v4    = qnode(3,node4)/rho4
          w4    = qnode(4,node4)/rho4
          p4    = gm1*(qnode(5,node4) - .5_dp*rho4*(u4*u4 + v4*v4 + w4*w4))

!  Compute viscosity for the cell

          T1 = gamma*p1/rho1
          T2 = gamma*p2/rho2
          T3 = gamma*p3/rho3
          T4 = gamma*p4/rho4

          rmu1 = viscosity_law( cstar, T1 ) + amut(node1)
          rmu2 = viscosity_law( cstar, T2 ) + amut(node2)
          rmu3 = viscosity_law( cstar, T3 ) + amut(node3)
          rmu4 = viscosity_law( cstar, T4 ) + amut(node4)

          rmu = 0.25_dp*(rmu1 + rmu2 + rmu3 + rmu4)

!  Now form gradients of velocity

          const = -1.0_dp/(3.0_dp*vol)

          ux = const*((u1-u4)*nx1 + (u2-u4)*nx2 + (u3-u4)*nx3)
          uy = const*((u1-u4)*ny1 + (u2-u4)*ny2 + (u3-u4)*ny3)
          uz = const*((u1-u4)*nz1 + (u2-u4)*nz2 + (u3-u4)*nz3)

          vx = const*((v1-v4)*nx1 + (v2-v4)*nx2 + (v3-v4)*nx3)
          vy = const*((v1-v4)*ny1 + (v2-v4)*ny2 + (v3-v4)*ny3)
          vz = const*((v1-v4)*nz1 + (v2-v4)*nz2 + (v3-v4)*nz3)

          wx = const*((w1-w4)*nx1 + (w2-w4)*nx2 + (w3-w4)*nx3)
          wy = const*((w1-w4)*ny1 + (w2-w4)*ny2 + (w3-w4)*ny3)
          wz = const*((w1-w4)*nz1 + (w2-w4)*nz2 + (w3-w4)*nz3)

          xnorm = nx4
          ynorm = ny4
          znorm = nz4

!  Now compute components of stress vector acting on the face

          termx = 2.0_dp*xmr*rmu*(xnorm*(c43*ux - c23*(vy + wz))               &
                             +ynorm*(uy + vx)                                  &
                             +znorm*(uz + wx))
           termxM = -2.0_dp/Re/xmach/xmach*rmu*(xnorm*(c43*ux-c23*(vy + wz))   &
                                           +ynorm*(uy + vx)                    &
                                           +znorm*(uz + wx))

          termy = 2.0_dp*xmr*rmu*(xnorm*(uy + vx)                              &
                             +ynorm*(c43*vy - c23*(ux + wz))                   &
                             +znorm*(vz + wy))
           termyM = -2.0_dp/Re/xmach/xmach*rmu*(xnorm*(uy + vx)                &
                                           +ynorm*(c43*vy - c23*(ux + wz))     &
                                           +znorm*(vz + wy))

          termz = 2.0_dp*xmr*rmu*(xnorm*(uz + wx)                              &
                             +ynorm*(vz + wy)                                  &
                             +znorm*(c43*wz - c23*(ux + vy)))
           termzM = -2.0_dp/Re/xmach/xmach*rmu*(xnorm*(uz + wx)                &
                                           +ynorm*(vz + wy)                    &
                                           +znorm*(c43*wz - c23*(ux + vy)))

!  Now dot the stress vector acting on the surface face with
!  a unit vector in the drag (lift) direction.  This is the
!  magnitude of the friction force acting on the face in the
!  drag (lift) direction

!  Find unit vectors in drag and lift directions

          nxd =   cos(alpha/conv) * cos(yaw/conv)
          nyd = - sin(yaw/conv)
          nzd =   sin(alpha/conv) * cos(yaw/conv)

          nxl = - sin(alpha/conv)
          nyl =   0.0_dp
          nzl =   cos(alpha/conv)

!  Now do the dot product to get the force in the drag (lift) direction

!  I think the signs are right on the following two equations, but I
!  wouldn't stake my life on it.  They've got to do with the force
!  being on the body or on the fluid.  The way they are set right now
!  gives the logical results (increase in Cd, decrease in Cl).

          forced  = - (termx *nxd + termy *nyd + termz *nzd)
          forcedM = - (termxM*nxd + termyM*nyd + termzM*nzd)
          forcel  = - (termx *nxl + termy *nyl + termz *nzl)
          forcelM = - (termxM*nxl + termyM*nyl + termzM*nzl)

          xmid = (x1 + x2 + x3)/3.0_dp
          ymid = (y1 + y2 + y3)/3.0_dp
          zmid = (z1 + z2 + z3)/3.0_dp

!  Now add things

          cdv  = cdv  + forced
          cdvM = cdvM + forcedM
          clv  = clv  + forcel
          clvM = clvM + forcelM

          cmxv = cmxv - termz*(ymid-ymc) + termy*(zmid-zmc)
            cmxvM = cmxvM - termzM*(ymid-ymc) + termyM*(zmid-zmc)
          cmyv = cmyv + termz*(xmid-xmc) - termx*(zmid-zmc)
            cmyvM = cmyvM + termzM*(xmid-xmc) - termxM*(zmid-zmc)
          cmzv = cmzv - termy*(xmid-xmc) + termx*(ymid-ymc)
            cmzvM = cmzvM - termyM*(xmid-xmc) + termxM*(ymid-ymc)

        endif

      end do

  end subroutine skinfricm


!================================ TURBRESM ===================================80
!
! Differentiates the turb model wrt freestream Mach number
!
!=============================================================================80

  subroutine turbresm(eqn_set, nnodes0,nnodes01,nedge,nedgeLoc,eptr,           &
                      turb,qnode,x,y,z,slen,gradx,grady,gradz,                 &
                      vol,xn,yn,zn,ra,dft1,dft2,coltag,rlam,rprod,             &
                      nbound,bc,nfunctions,nedgeLoc_2d,                        &
                      nnodes0_2d,node_pairs_2d,nelem,elem,                     &
                      adim, n_tot, n_grd, n_turb, dxdt, dydt, dzdt)

    use info_depr,     only : tref, re, xmach
    use fluid,         only : gamma, sutherland_constant
    use turb_sa_const, only : vkar,sig,cb2,cv1,cw1,cw2,cw3,ct3,ct4,cb1,        &
                              use_edwards_mod
    use lmpi_app,      only : lmpi_xfer
    use bc_types,      only : bcgrid_type
    use turb_util,     only : turbgrad
    use kinddefs,      only : dp
    use element_types, only : elem_type
    use design_types,  only : max_functions

    integer, intent(in) :: eqn_set, adim, n_tot, n_grd
    integer,                              intent(in)    :: nedge
    integer,                              intent(in)    :: nfunctions
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: nedgeloc
    integer,                              intent(in)    :: nbound,nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nedgeloc_2d
    integer,                              intent(in)    :: nnodes0_2d
    integer,                              intent(in)    :: nelem

    integer,     dimension(2,nedge),      intent(in)    :: eptr
    integer,     dimension(2,nnodes0_2d), intent(in)    :: node_pairs_2d

    integer                                            :: i,n,j
    integer                                            :: node1,node2

    real(dp), dimension(nedge),                     intent(in)  :: xn,yn,zn,ra
    real(dp), dimension(nedgeloc),                  intent(in)  :: dft1,dft2
    real(dp), dimension(nnodes01),                  intent(in)  :: slen,x,y,z
    real(dp), dimension(nnodes01),                  intent(in)  :: dxdt, dydt
    real(dp), dimension(nnodes01),                  intent(in)  :: dzdt
    real(dp), dimension(n_turb,nnodes01),           intent(in)  :: turb
    real(dp), dimension(nnodes01),                  intent(in)  :: vol
    real(dp), dimension(adim,nnodes01),             intent(in)  :: coltag
    real(dp), dimension(n_tot,nnodes01),            intent(in)  :: qnode
    real(dp), dimension(adim,nnodes01,nfunctions),  intent(in)  :: rlam
    real(dp), dimension(n_grd,nnodes01),            intent(inout) :: gradx
    real(dp), dimension(n_grd,nnodes01),            intent(inout) :: grady
    real(dp), dimension(n_grd,nnodes01),            intent(inout) :: gradz

    real(dp), dimension(nfunctions),    intent(inout) :: rprod

    real(dp)                                           :: cstar,term1
    real(dp)                                           :: term2,res1m,res2m
    real(dp)                                           :: onesix,rho
    real(dp)                                           :: p,uy,uz,vx,vz
    real(dp)                                           :: wx,wy,temp,rnu,chi
    real(dp)                                           :: bot,s,sw,swm,rr,rrm
    real(dp)                                           :: gg,ggm,fivesix,fw
    real(dp)                                           :: term,bottom,factor
    real(dp)                                           :: fwm,ft2,prodm
    real(dp)                                           :: destm
    real(dp)                                           :: sourcem,resm,fv1,fv2
    real(dp)                                           :: terma,termb,vkar2
    real(dp)                                           :: arg,cosharg

    real(dp), dimension(max_functions)    :: rlam6

    real(dp), parameter    :: my_1m12  =  1.e-12_dp

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    type(elem_type),   dimension(nelem),  intent(in) :: elem

  continue

    cstar = sutherland_constant/tref

! Calculate the gradients for the production

    call turbgrad(eqn_set,                                                     &
                  nnodes0,       nnodes01,      nedgeloc,      eptr,           &
                  qnode,         x,             y,             z,              &
                  dxdt,          dydt,          dzdt,                          &
                  xn,            yn,            zn,            ra,             &
                  vol,           gradx,         grady,         gradz,          &
                  nbound,        bc,            nedgeloc_2d,   node_pairs_2d,  &
                  nnodes0_2d,    nelem,         elem, n_tot, n_grd)

    call lmpi_xfer(gradx)
    call lmpi_xfer(grady)
    call lmpi_xfer(gradz)

      do n = 1, nedgeloc
        node1 = eptr(1,n)
        node2 = eptr(2,n)

! Dissipative part

        term1 = dft2(n)
        term2 = cb2*turb(1,node1)*dft1(n)
        terma = Max( term1 - term2, 0.0_dp)
        termb = Min(-term1 + term2, 0.0_dp)

        res1M = 1.0_dp/Re/sig*terma*turb(1,node1)                              &
          + 1.0_dp/Re/sig*termb*turb(1,node2)

        do j = 1, nfunctions
          rlam6(j) = coltag(6,node1)*rlam(6,node1,j)
          if(node1 <= nnodes0) rprod(j) = rprod(j) + res1M*rlam6(j)
        end do

! Now do the other node

        term1 = dft2(n)
        term2 = cb2*turb(1,node2)*dft1(n)
        terma = Max( term1 - term2, 0.0_dp)
        termb = Min(-term1 + term2, 0.0_dp)

        res2M = 1.0_dp/Re/sig*terma*turb(1,node2)                              &
              + 1.0_dp/Re/sig*termb*turb(1,node1)

        do j = 1, nfunctions
          rlam6(j) = coltag(6,node2)*rlam(6,node2,j)
          if(node2 <= nnodes0) rprod(j) = rprod(j) + res2M*rlam6(j)
        end do

      end do

! Now lets compute the source term

    onesix = 1.0_dp/6.0_dp

    regular_source_term : if (.not. use_edwards_mod) then

      source1 : do i = 1,nnodes0

        if (coltag(6,i)<0.1_dp) cycle source1 ! avoid NaN from 1/slen, slen=0

        rho  = qnode(1,i)
        p    = qnode(5,i)

        uy   = grady(2,i)
        uz   = gradz(2,i)
        vx   = gradx(3,i)
        vz   = gradz(3,i)
        wx   = gradx(4,i)
        wy   = grady(4,i)

        temp = gamma*p/rho

        rnu  = viscosity_law( cstar, temp )/rho

! Old way of getting Sw

        chi  = turb(1,i)/rnu
        fv1  = chi**3/(chi**3 + cv1**3)
        fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
        bot  = vkar*vkar*slen(i)*slen(i)
        S    = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2)
        if ( S <= 1.0e-8_dp) S = 1.0e-8_dp
        Sw   = S + xmach/Re*turb(1,i)/bot*fv2
          SwM = 1.0_dp/Re*turb(1,i)/bot*fv2
        if(Sw > 0.00001_dp) then
          SwM = SwM
        else
          Sw = 0.00001_dp
          SwM = 0.0_dp
        endif

        RR   = xmach/Re*turb(1,i)/bot/Sw
          RRm = 1.0_dp/Re*turb(1,i)/bot/Sw                                     &
              - xmach/Re*(turb(1,i)/bot/Sw/Sw*Swm)
        if(RR > 10.0_dp)then
          RR=10.0_dp
          RRM = 0.0_dp
        endif
        GG   = RR + cw2*(RR**6 - RR)
          GGM = RRM + cw2*(6.0_dp*RR**5*RRM - RRM)

        onesix  = 1.0_dp/6.0_dp
        fivesix = -5.0_dp/6.0_dp
        fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
         term =  (1.0_dp + cw3**6)/(GG**6 + cw3**6)
         bottom = (GG**6 + cw3**6)**2
         factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
         fwm = GGm*(term**onesix - factor)

        ft2  = ct3*exp(-ct4*chi*chi)

!        Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,i))
         ProdM = cb1*(1.0_dp - ft2)*SwM*turb(1,i)
        vkar2 = vkar*vkar
!        Dest = xmach/Re*(cw1*fw - cb1/vkar2*ft2)*(turb(1,i)/slen(i))**2
         Destm = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)*(turb(1,i)/slen(i))**2     &
               + xmach/Re*(cw1*fwm)*(turb(1,i)/slen(i))**2

!        source = vol(i)*(Prod - Dest)
         sourceM = vol(i)*(ProdM - DestM)
         resM = -sourceM

        do j = 1, nfunctions
          rlam6(j) = coltag(6,i)*rlam(6,i,j)
          rprod(j) = rprod(j) + resM*rlam6(j)
        end do

      end do source1

    else regular_source_term

      source2 : do i = 1,nnodes0

        if (coltag(6,i)<0.1_dp) cycle source2 ! avoid NaN from 1/slen, slen=0

        rho  = qnode(1,i)
        p    = qnode(5,i)

        uy   = grady(2,i)
        uz   = gradz(2,i)
        vx   = gradx(3,i)
        vz   = gradz(3,i)
        wx   = gradx(4,i)
        wy   = grady(4,i)

        temp = gamma*p/rho

        rnu  = viscosity_law( cstar, temp )/rho

! Old way of getting Sw

        chi  = turb(1,i)/rnu
        chi    = (turb(1,i)+my_1m12) / rnu      ! Adjust turb for Edwards
        fv1  = chi**3/(chi**3 + cv1**3)
        fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
        bot  = vkar*vkar*slen(i)*slen(i)
        S    = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2)
        if ( S <= 1.0e-8_dp) S = 1.0e-8_dp
        Sw   = S + xmach/Re*turb(1,i)/bot*fv2
          SwM = 1.0_dp/Re*turb(1,i)/bot*fv2
        if(Sw > 0.00001_dp) then
          SwM = SwM
        else
          Sw = 0.00001_dp
          SwM = 0.0_dp
        endif

        RR   = xmach/Re*turb(1,i)/bot/Sw
          RRm = 1.0_dp/Re*turb(1,i)/bot/Sw                                     &
              - xmach/Re*(turb(1,i)/bot/Sw/Sw*Swm)
        if(RR > 10.0_dp)then
          RR=10.0_dp
          RRM = 0.0_dp
        endif

! Edwards' mod

        sw  = s*(1.0_dp/chi + fv1)
          SwM = 0.0_dp
        arg = vkar*vkar*slen(i)*slen(i)
        rr  = tanh(xmach/re*turb(1,i)/arg/sw) / tanh(1.0_dp)
          cosharg = xmach/re*turb(1,i)/arg/sw
          if(abs(cosharg) > 15.0_dp) then
            RRM = 0.0_dp
          else
            RRM = 1.0_dp/re*turb(1,i)/arg/sw / tanh(1.0_dp)                    &
                  / cosh(cosharg) / cosh(cosharg)
          endif

! End of Edwards' mod

        GG   = RR + cw2*(RR**6 - RR)
          GGM = RRM + cw2*(6.0_dp*RR**5*RRM - RRM)

        onesix  = 1.0_dp/6.0_dp
        fivesix = -5.0_dp/6.0_dp
        fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
         term =  (1.0_dp + cw3**6)/(GG**6 + cw3**6)
         bottom = (GG**6 + cw3**6)**2
         factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
         fwm = GGm*(term**onesix - factor)

        ft2  = ct3*exp(-ct4*chi*chi)

!        Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,i))
         ProdM = cb1*(1.0_dp - ft2)*SwM*turb(1,i)
        vkar2 = vkar*vkar
!        Dest = xmach/Re*(cw1*fw - cb1/vkar2*ft2)*(turb(1,i)/slen(i))**2
         Destm = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)*(turb(1,i)/slen(i))**2     &
               + xmach/Re*(cw1*fwm)*(turb(1,i)/slen(i))**2

!        source = vol(i)*(Prod - Dest)
         sourceM = vol(i)*(ProdM - DestM)
         resM = -sourceM

        do j = 1, nfunctions
          rlam6(j) = coltag(6,i)*rlam(6,i,j)
          rprod(j) = rprod(j) + resM*rlam6(j)
        end do

      end do source2

    end if regular_source_term

  end subroutine turbresm

  include 'viscosity_law.f90'

end module dmach

module averaging_types

  use kinddefs,      only : dp, system_i1

  implicit none

  private

  public :: averaging_type

  type averaging_type

    integer :: n

    integer,  dimension(:), allocatable :: gdof
    integer,  dimension(:), allocatable :: mdof

    integer(kind=system_i1), dimension(:), allocatable :: skip

    integer, dimension(:), allocatable :: ia, ja

    real(dp), dimension(:), allocatable :: edges

  end type averaging_type

end module averaging_types

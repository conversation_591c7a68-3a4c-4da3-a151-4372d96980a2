
module meshsim_interface

  implicit none

  private

  public :: meshsim_compsetmap
  interface meshsim_compsetmap
    module procedure meshsim_compsetmap_r
    module procedure meshsim_compsetmap_c
  end interface

  public :: meshsim_compsetauxvector
  interface meshsim_compsetauxvector
    module procedure meshsim_compsetauxvector_r
    module procedure meshsim_compsetauxvector_c
  end interface

  public :: meshsim_compsetauxmatrix
  interface meshsim_compsetauxmatrix
    module procedure meshsim_compsetauxmatrix_r
    module procedure meshsim_compsetauxmatrix_c
  end interface

  public :: meshsim_compsetauxmatrix3
  interface meshsim_compsetauxmatrix3
    module procedure meshsim_compsetauxmatrix3_r
    module procedure meshsim_compsetauxmatrix3_c
  end interface

  public :: meshsim_compgetnodes
  interface meshsim_compgetnodes
    module procedure meshsim_compgetnodes_r
    module procedure meshsim_compgetnodes_c
  end interface

  public :: meshsim_compgetauxvector
  interface meshsim_compgetauxvector
    module procedure meshsim_compgetauxvector_r
    module procedure meshsim_compgetauxvector_c
  end interface

  public :: meshsim_compgetauxmatrix
  interface meshsim_compgetauxmatrix
    module procedure meshsim_compgetauxmatrix_r
    module procedure meshsim_compgetauxmatrix_c
  end interface

  public :: meshsim_compgetauxmatrix3
  interface meshsim_compgetauxmatrix3
    module procedure meshsim_compgetauxmatrix3_r
    module procedure meshsim_compgetauxmatrix3_c
  end interface

contains

  subroutine meshsim_compsetmap_r( nnode, map )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode
    real(dp), dimension(6,nnode), intent(in) :: map
    interface
      subroutine meshsim_gridsetmap( nnode, map )
        integer, intent(in) :: nnode
        real(selected_real_kind(15,307)), dimension(6,nnode), intent(in) :: map
      end subroutine meshsim_gridsetmap
    end interface
    continue
    call meshsim_gridsetmap( nnode, map )
  end subroutine meshsim_compsetmap_r

  subroutine meshsim_compsetmap_c( nnode, cmap )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode
    complex(dp), dimension(6,nnode), intent(in) :: cmap
    real(dp), dimension(6,nnode) :: map
    continue
    map = real(cmap,dp)
    call meshsim_compsetmap( nnode, map )
  end subroutine meshsim_compsetmap_c


  subroutine meshsim_compsetauxvector_r( nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    real(dp), dimension(nnode), intent(in) :: x
    interface
      subroutine meshsim_gridsetauxvector( nnode, offset, x )
        integer, intent(in) :: nnode, offset
        real(selected_real_kind(15,307)), dimension(nnode), intent(in) :: x
      end subroutine meshsim_gridsetauxvector
    end interface
    continue
    call meshsim_gridsetauxvector( nnode, offset, x )
  end subroutine meshsim_compsetauxvector_r

  subroutine meshsim_compsetauxvector_c( nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    complex(dp), dimension(nnode), intent(in) :: cx
    real(dp), dimension(nnode) :: x
    continue
    x = real(cx, dp)
    call meshsim_compsetauxvector( nnode, offset, x )
  end subroutine meshsim_compsetauxvector_c


  subroutine meshsim_compsetauxmatrix_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode), intent(in) :: x
    interface
      subroutine meshsim_gridsetauxmatrix( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode), intent(in) :: x
      end subroutine meshsim_gridsetauxmatrix
    end interface
    continue
    call meshsim_gridsetauxmatrix( dmn, nnode, offset, x )
  end subroutine meshsim_compsetauxmatrix_r

  subroutine meshsim_compsetauxmatrix_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode), intent(in) :: cx
    real(dp), dimension(dmn,nnode) :: x
    continue
    x = real(cx, dp)
    call meshsim_compsetauxmatrix( dmn, nnode, offset, x )
  end subroutine meshsim_compsetauxmatrix_c


  subroutine meshsim_compsetauxmatrix3_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode,1), intent(in) :: x
    interface
      subroutine meshsim_gridsetauxmatrix3( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode,1), intent(in):: x
      end subroutine meshsim_gridsetauxmatrix3
    end interface
    continue
    call meshsim_gridsetauxmatrix3( dmn, nnode, offset, x )
  end subroutine meshsim_compsetauxmatrix3_r

  subroutine meshsim_compsetauxmatrix3_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode,1), intent(in) :: cx
    real(dp), dimension(dmn,nnode,1) :: x
    continue
    x = real(cx, dp)
    call meshsim_compsetauxmatrix3( dmn, nnode, offset, x )
  end subroutine meshsim_compsetauxmatrix3_c


  subroutine meshsim_compgetnodes_r( nnode, l2g, x, y, z )
    use kinddefs,        only : dp
    integer,                                            intent(in)  :: nnode
    integer,                          dimension(nnode), intent(out) :: l2g
    real(dp), dimension(nnode), intent(out) :: x,y,z
    interface
      subroutine meshsim_gridgetnodes( nnode, l2g, x, y, z )
        integer,                                            intent(in)  :: nnode
        integer,                          dimension(nnode), intent(out) :: l2g
        real(selected_real_kind(15,307)), dimension(nnode), intent(out) :: x,y,z
      end subroutine meshsim_gridgetnodes
    end interface
    continue
    call meshsim_gridgetnodes( nnode, l2g, x, y, z )
  end subroutine meshsim_compgetnodes_r

  subroutine meshsim_compgetnodes_c( nnode, l2g, cx, cy, cz )
    use kinddefs,        only : dp
    integer,                                            intent(in)  :: nnode
    integer,                          dimension(nnode), intent(out) :: l2g
    complex(dp), dimension(nnode), intent(out) :: cx,cy,cz
    real(dp), dimension(nnode) :: x,y,z
    continue
    call meshsim_compgetnodes( nnode, l2g, x, y, z )
    cx = cmplx( x, 0.0_dp, dp )
    cy = cmplx( y, 0.0_dp, dp )
    cz = cmplx( z, 0.0_dp, dp )
  end subroutine meshsim_compgetnodes_c

  subroutine meshsim_compgetauxvector_r( nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    real(dp), dimension(nnode), intent(out) :: x
    interface
      subroutine meshsim_gridgetauxvector( nnode, offset, x )
        integer, intent(in) :: nnode, offset
        real(selected_real_kind(15,307)), dimension(nnode), intent(out) :: x
      end subroutine meshsim_gridgetauxvector
    end interface
    continue
    call meshsim_gridgetauxvector( nnode, offset, x )
  end subroutine meshsim_compgetauxvector_r

  subroutine meshsim_compgetauxvector_c( nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    complex(dp), dimension(nnode), intent(out) :: cx
    real(dp), dimension(nnode) :: x
    continue
    call meshsim_compgetauxvector( nnode, offset, x )
    cx = cmplx( x, 0.0_dp, dp )
  end subroutine meshsim_compgetauxvector_c


  subroutine meshsim_compgetauxmatrix_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode), intent(out) :: x
    interface
      subroutine meshsim_gridgetauxmatrix( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode), intent(out) :: x
      end subroutine meshsim_gridgetauxmatrix
    end interface
    continue
    call meshsim_gridgetauxmatrix( dmn, nnode, offset, x )
  end subroutine meshsim_compgetauxmatrix_r

  subroutine meshsim_compgetauxmatrix_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode), intent(out) :: cx
    real(dp), dimension(dmn,nnode) :: x
    continue
    call meshsim_compgetauxmatrix( dmn, nnode, offset, x )
    cx = cmplx( x, 0.0_dp, dp )
  end subroutine meshsim_compgetauxmatrix_c


  subroutine meshsim_compgetauxmatrix3_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode,1), intent(out) :: x
    interface
      subroutine meshsim_gridgetauxmatrix3( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode,1), intent(out)::x
      end subroutine meshsim_gridgetauxmatrix3
    end interface
    continue
    call meshsim_gridgetauxmatrix3( dmn, nnode, offset, x )
  end subroutine meshsim_compgetauxmatrix3_r

  subroutine meshsim_compgetauxmatrix3_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode,1), intent(out) :: cx
    real(dp), dimension(dmn,nnode,1) :: x
    continue
    call meshsim_compgetauxmatrix3( dmn, nnode, offset, x )
    cx = cmplx( x, 0.0_dp, dp )
  end subroutine meshsim_compgetauxmatrix3_c

end module meshsim_interface

!================================== FLUX_UNSPLIT_I ===========================80
!
! Unsplit flux...ubar_null enforces tangency explicity.
!
! Newer version that handles moving grid and noninertial rotating reference
! frame cases as well as stationary grid/inertial frame cases.
!
! Reference: <PERSON>eel, R. E., <PERSON>, A. G., and McGrory, W. D.:"Low-Speed,
!            Time-Accurate Validation of GASP Version 4"; AIAA 2005-686
!            43rd AIAA Aerospace Sciences Meeting, Jan. 2005.
!
!=============================================================================80

  pure function flux_unsplit_i( xnorm, ynorm, znorm, area, face_speed, beta,   &
                                ql, ubar_null )

    use kinddefs,        only : dp

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: face_speed, beta
    real(dp), dimension(4), intent(in) :: ql
    logical,                intent(in) :: ubar_null

    real(dp), dimension(4)             :: flux_unsplit_i

    real(dp) :: fluxp1,fluxp2,fluxp3,fluxp4
    real(dp) :: pl, ul, vl, wl
    real(dp) :: ubarl

  continue

    pl = ql(1)
    ul = ql(2)
    vl = ql(3)
    wl = ql(4)

    ubarl  = xnorm*ul + ynorm*vl + znorm*wl
    if ( ubar_null ) ubarl = 0._dp

!   Calculate the flux vector on the left side

    fluxp1 = beta*(ubarl-face_speed)
    fluxp2 = ul*(ubarl-face_speed) + xnorm*pl
    fluxp3 = vl*(ubarl-face_speed) + ynorm*pl
    fluxp4 = wl*(ubarl-face_speed) + znorm*pl

!   Finally, form the numerical flux

    flux_unsplit_i(1)  = area*fluxp1
    flux_unsplit_i(2)  = area*fluxp2
    flux_unsplit_i(3)  = area*fluxp3
    flux_unsplit_i(4)  = area*fluxp4

  end function flux_unsplit_i

!================================= LSQ_SCOORDS ===============================80
!
! Scaled least squares coordinate and weighting.
!
!=============================================================================80

  pure function lsq_scoords( lc, lc_max )

    use lsq_constants, only : wflsq1, wflsq2

    real(dp), intent(in), dimension(4) :: lc
    real(dp), intent(in), dimension(4) :: lc_max

    real(dp), dimension(4) :: lsq_scoords

    real(dp) :: xie, eta, zie, inv_distancesq

  continue

    xie = lc(1)
    eta = lc(2)
    zie = lc(3)
    inv_distancesq = lc(4)

    lsq_scoords(1) = xie/lc_max(1)
    lsq_scoords(2) = eta/lc_max(2)
    lsq_scoords(3) = zie/lc_max(3)

    lsq_scoords(4) = wflsq1*1._dp + wflsq2*inv_distancesq/lc_max(4)

  end function lsq_scoords

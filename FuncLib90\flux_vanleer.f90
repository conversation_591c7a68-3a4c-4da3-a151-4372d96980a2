!================================= FLUX_VANLEER ==============================80
!
! Calculates the fluxes on the face and compute the contribution to the
! flux balance using vanLeers flux vector splitting scheme
!
! Note that this subroutine uses primitive variables
!
!=============================================================================80

  pure function flux_vanleer(xnorm, ynorm, znorm, area, face_speed, ql, qr)

    use kinddefs,        only : dp
    use fluid,           only : gamma, gm1

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: face_speed
    real(dp), dimension(5), intent(in) :: ql, qr
    real(dp), dimension(5)             :: flux_vanleer

    real(dp) :: rhol, ul, vl, wl, pressl
    real(dp) :: rhor, ur, vr, wr, pressr
    real(dp) :: ubl2a, ubr2a
    real(dp) :: q2, enrgy, c, ubar, fmach
    real(dp) :: fluxm1, fluxm2, fluxm3, fluxm4, fluxm5
    real(dp) :: fluxp1, fluxp2, fluxp3, fluxp4, fluxp5

    real(dp), parameter :: my_0    =  0.0_dp
    real(dp), parameter :: my_4th  = 0.25_dp
    real(dp), parameter :: my_half = 0.50_dp
    real(dp), parameter :: my_1    =  1.0_dp
    real(dp), parameter :: my_2    =  2.0_dp

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Construct additional left state variables

    q2    = ul*ul + vl*vl + wl*wl
    enrgy = pressl/gm1 + my_half*rhol*q2
    c     = sqrt(gamma*pressl/rhol)
    ubar  = xnorm*ul + ynorm*vl + znorm*wl - face_speed
    fmach = ubar/c
    ubl2a = -ubar + my_2*c

!   Construct the plus flux

    if (abs(fmach) < my_1) then
      fluxp1 = area*my_4th*rhol*c*(fmach + my_1)**2
      fluxp2 = fluxp1*(xnorm*ubl2a/gamma + ul)
      fluxp3 = fluxp1*(ynorm*ubl2a/gamma + vl)
      fluxp4 = fluxp1*(znorm*ubl2a/gamma + wl)
      fluxp5 = fluxp1*((-gm1*ubar*ubar                                         &
                        + my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - my_1)     &
                        + my_half*q2 + face_speed*(-ubar + my_2*c)/gamma)
    else if (fmach >= my_1) then
      fluxp1 = area*rhol*ubar
      fluxp2 = area*(rhol*ul*ubar + xnorm*pressl)
      fluxp3 = area*(rhol*vl*ubar + ynorm*pressl)
      fluxp4 = area*(rhol*wl*ubar + znorm*pressl)
      fluxp5 = area*((enrgy + pressl)*ubar + face_speed*pressl)
    else
      fluxp1 = my_0
      fluxp2 = my_0
      fluxp3 = my_0
      fluxp4 = my_0
      fluxp5 = my_0
    end if

!   Construct additional right state variables

    q2    = ur*ur + vr*vr + wr*wr
    enrgy = pressr/gm1 + my_half*rhor*q2
    c     = sqrt(gamma*pressr/rhor)
    ubar  = xnorm*ur + ynorm*vr + znorm*wr - face_speed
    fmach = ubar/c
    ubr2a = -ubar - my_2*c

!   Construct the minus flux

    if (abs(fmach) < my_1) then
      fluxm1 = -area*my_4th*rhor*c*(fmach - my_1)**2
      fluxm2 = fluxm1*(xnorm*ubr2a/gamma + ur)
      fluxm3 = fluxm1*(ynorm*ubr2a/gamma + vr)
      fluxm4 = fluxm1*(znorm*ubr2a/gamma + wr)
      fluxm5 = fluxm1*((-gm1*ubar*ubar                                         &
                        - my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - 1)        &
                        + my_half*q2  + face_speed*(-ubar - my_2*c)/gamma)
    else if (fmach <= -my_1)then
      fluxm1 = area*rhor*ubar
      fluxm2 = area*(rhor*ur*ubar + xnorm*pressr)
      fluxm3 = area*(rhor*vr*ubar + ynorm*pressr)
      fluxm4 = area*(rhor*wr*ubar + znorm*pressr)
      fluxm5 = area*((enrgy + pressr)*ubar + face_speed*pressr)
    else
      fluxm1 = my_0
      fluxm2 = my_0
      fluxm3 = my_0
      fluxm4 = my_0
      fluxm5 = my_0
    end if

!   Compute the contribution to the flux balance

    flux_vanleer(1) = fluxp1 + fluxm1
    flux_vanleer(2) = fluxp2 + fluxm2
    flux_vanleer(3) = fluxp3 + fluxm3
    flux_vanleer(4) = fluxp4 + fluxm4
    flux_vanleer(5) = fluxp5 + fluxm5

  end function flux_vanleer

module porous2

  use kinddefs, only : dp

  implicit none

  private

  public :: porous_rhs
  public :: slater_mdot
  public :: slater_mach
  public :: porous_node_based

contains

!============================== POROUS_MAIN ==================================80
!
! Calling routine for source based porosity
!
!=============================================================================80
  subroutine porous_rhs  ( nnodes0 )

    integer, intent(in) :: nnodes0

  continue

    write(*,*) nnodes0

!   call porous_node_based ( nnodes0, nbnode, ibnode,                          &
!                            n_momx, n_momy, n_momz, n_etot,                   &
!                            need_grid_velocity,                               &
!                            bxn, byn, bzn, bfacespeed, q_dof, res )

  end subroutine porous_rhs


!========================= SLATER_MDOT =======================================80
!
! Massflow  of porous slot using Slater's model
!
!=============================================================================80
  function slater_mdot ( area, phi, p_wall, t_wall, p_plenum )                 &
  result ( mdot_bleed )

  use fluid, only : gm1, gp1
! use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
!                 , operator(-), operator(*), operator(/)           &
!                 , operator(**), ddt_sqrt
  real(dp)                 :: mdot_bleed

  real(dp), intent(in) :: area
  real(dp), intent(in) :: phi
  real(dp), intent(in) :: p_wall
  real(dp), intent(in) :: t_wall
  real(dp), intent(in) :: p_plenum

  real(dp)                 :: q_sonic_s
  real(dp)                 :: mdot_sonic_s

  real(dp), parameter :: one  = 1.0_dp
  real(dp), parameter :: two  = 2.0_dp

  real(dp), parameter :: const1 = 0.57799735_dp
  real(dp), parameter :: const2 = 0.03069346_dp
  real(dp), parameter :: const3 = 0.59361420_dp

  continue

    q_sonic_s = const1                                                         &
              + const2 * p_plenum/p_wall                                       &
              - const3 * (p_plenum/p_wall)**2

    mdot_sonic_s = p_wall * phi * area * ( sqrt(one/t_wall) ) * &
                   (gp1/two)**(-gp1/two*gm1 )

    mdot_bleed = q_sonic_s * mdot_sonic_s

  end function slater_mdot

!========================= SLATER_MACH =======================================80
!
! Mach number of porous slot using Slater's model
!
!=============================================================================80
  function slater_mach ( area, phi, p_wall, t_wall, p_plenum, t_plenum,        &
                         mdot, mach_guess ) result ( mach )

  use fluid, only : gm1, gp1
! use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
!                 , operator(-), operator(*), operator(/)           &
!                 , operator(**), ddt_sqrt
  real(dp)                 :: mach

  real(dp), intent(in) :: area
  real(dp), intent(in) :: phi
  real(dp), intent(in) :: p_wall
  real(dp), intent(in) :: t_wall
  real(dp), intent(in) :: p_plenum
  real(dp), intent(in) :: t_plenum
  real(dp), intent(in) :: mdot
  real(dp), intent(in) :: mach_guess


  real(dp)                 :: p_0, t_0
  real(dp)                 :: q_sonic_s
  real(dp)                 :: f, dfdm
  real(dp)                 :: part1, part2, coef
  real(dp)                 :: dpart1dm, dpart2dm

  integer :: iter

  real(dp), parameter :: zero = 0.0_dp
  real(dp), parameter :: one  = 1.0_dp
  real(dp), parameter :: two  = 2.0_dp

  real(dp), parameter :: const1 = 0.57799735_dp
  real(dp), parameter :: const2 = 0.03069346_dp
  real(dp), parameter :: const3 = 0.59361420_dp

  continue

    q_sonic_s = const1                                                         &
              + const2 * p_plenum/p_wall                                       &
              - const3 * (p_plenum/p_wall)**2

    if ( q_sonic_s > zero ) then
      p_0      = p_wall
      t_0      = t_wall
    else
      p_0      = p_plenum
      t_0      = t_plenum
    end if

    mach     =  mach_guess
    coef = -gp1/(two*gm1)

    iter = 0
    f    = one
    dfdm = one

    do while ( abs(f/dfdm) > 1.0e-8_dp .and. iter < 10 )
      f = p_0 * phi * area * mach * ( sqrt(one/t_0) ) * &
            (one + (gm1/two) *mach**2 )**coef - mdot

      part1    = p_0 * phi * area * mach * ( sqrt(one/t_0) )
      dpart1dm = p_0 * phi * area * ( sqrt(one/t_0) )

      part2    = (one + (gm1/two) *mach**2 )**coef

      dpart2dm = coef*(one + (gm1/two) *mach**2 )**(coef-one) * &
                 gm1 * mach

      dfdm = part1 * dpart2dm + part2 * dpart1dm

      mach = mach - f/dfdm
      iter = iter + 1
!     write(*,'(a,10(1x,f15.5))') 'f/dfdm:...........',f/dfdm, mach
    end do

  end function slater_mach

!========================= POROUS_NODE_BASED =================================80
!
! Apply a porous condition on a boundary as source terms to the N-S
! equations - compressible, node-based
!
!=============================================================================80
! call porous_node_based ( grid%nnodes0,                                       &
!                                      grid%bc(ib)%nbnode, grid%bc(ib)%ibnode, &
!                                n_momx, n_momy, n_momz, n_etot,               &
!                                need_grid_velocity,                           &
!   grid%bc(ib)%bxn, grid%bc(ib)%byn, grid%bc(ib)%bzn, grid%bc(ib)%bfacespeed, &
!   soln%q_dof, soln%res)

  subroutine porous_node_based ( nnodes0, nbnode, ibnode,                      &
                                 n_momx, n_momy, n_momz, n_etot,               &
                                 need_grid_velocity,                           &
                                 bxn, byn, bzn, bfacespeed, q_dof, res )

    use kinddefs,           only : dp
    use fluid,              only : gamma, gm1

    integer,                  intent(in)    :: nnodes0
    integer,                  intent(in)    :: nbnode
    integer,  dimension(:),   intent(in)    :: ibnode
    integer,                  intent(in)    :: n_momx
    integer,                  intent(in)    :: n_momy
    integer,                  intent(in)    :: n_momz
    integer,                  intent(in)    :: n_etot
    logical,                  intent(in)    :: need_grid_velocity
    real(dp), dimension(:),   intent(in)    :: bxn
    real(dp), dimension(:),   intent(in)    :: byn
    real(dp), dimension(:),   intent(in)    :: bzn
    real(dp), dimension(:),   intent(in)    :: bfacespeed
    real(dp), dimension(:,:), intent(in)    :: q_dof
    real(dp), dimension(:,:), intent(inout) :: res

    integer :: inode, i

    real(dp) :: a, a2, ai, mach, mach_guess
    real(dp) :: mdot_bleed
    real(dp) :: face_speed, ubar
    real(dp) :: unormi
    real(dp) :: xnorm, ynorm, znorm, area
    real(dp) :: rhoi, ui, vi, wi, pi, e, h
    real(dp) :: rho, u, v, w, p
    real(dp) :: p_wall, t_wall
    real(dp) :: p_plenum, t_plenum
    real(dp) :: p_0, t_0
    real(dp) :: q_sonic_s
    real(dp) :: res1, res2, res3, res4, res5

    real(dp) :: phi, factor

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: half = 0.5_dp

    real(dp), parameter :: const1 = 0.57799735_dp
    real(dp), parameter :: const2 = 0.03069346_dp
    real(dp), parameter :: const3 = 0.59361420_dp

  continue

!FIXME porosity needs to be brought in separately
    phi    = 0.01_dp

    loop_nodes : do i = 1, nbnode

      inode = ibnode(i)

      xnorm  = bxn(i)
      ynorm  = byn(i)
      znorm  = bzn(i)
      area   = sqrt(xnorm*xnorm + ynorm*ynorm + znorm*znorm)
      xnorm  = xnorm / area
      ynorm  = ynorm / area
      znorm  = znorm / area

      face_speed = zero

      if ( need_grid_velocity ) face_speed = bfacespeed(i)

      rhoi   = q_dof(1,inode)
      ui     = q_dof(n_momx,inode)
      vi     = q_dof(n_momy,inode)
      wi     = q_dof(n_momz,inode)
      pi     = q_dof(n_etot,inode)

      unormi     = ui*xnorm + vi*ynorm + wi*znorm - face_speed
      a2         = gamma * pi / rhoi
      a          = sqrt(a2)
      ai         = a
      mach_guess = unormi / ai

      e    = pi/gm1 + half*rhoi*(ui*ui + vi*vi + wi*wi)
      ubar = xnorm*ui + ynorm*vi + znorm*wi - face_speed

      p_wall = pi
      t_wall = ai

!FIXME p_plenum and t_plenum need to be actually calculated
      factor = 0.95_dp
      p_plenum = factor * p_wall
      t_plenum = t_wall

      q_sonic_s = const1                                                       &
                + const2 * p_plenum/p_wall                                     &
                - const3 * (p_plenum/p_wall)**2

      if ( q_sonic_s > zero ) then
        p_0      = p_wall
        t_0      = t_wall
      else
        p_0      = p_plenum
        t_0      = t_plenum
      end if


      mdot_bleed = slater_mdot ( area, phi, p_wall, t_wall, p_plenum )

      mach = slater_mach ( area, phi, p_wall, t_wall, p_plenum, t_plenum,      &
                           mdot_bleed, mach_guess )

      a    = sqrt(t_0)
      ubar = mach * a - face_speed
      u    = xnorm * ubar
      v    = ynorm * ubar
      w    = znorm * ubar
      p    = p_0
      rho  = gamma * p / t_0
      e    = p/gm1 + half*rho*(u*u + v*v + w*w)
      h    = e + p/rho

      res1 = mdot_bleed
      res2 = mdot_bleed * u + xnorm* (p-p_wall)
      res3 = mdot_bleed * v + ynorm* (p-p_wall)
      res4 = mdot_bleed * w + znorm* (p-p_wall)
      res5 = mdot_bleed * h

      if ( inode <= nnodes0 ) then

        res(1     ,inode) = res(1,     inode) + res1
        res(n_momx,inode) = res(n_momx,inode) + res2
        res(n_momy,inode) = res(n_momy,inode) + res3
        res(n_momz,inode) = res(n_momz,inode) + res4
        res(n_etot,inode) = res(n_etot,inode) + res5 + area*face_speed*p_wall

      end if

    end do loop_nodes

  end subroutine porous_node_based

end module porous2

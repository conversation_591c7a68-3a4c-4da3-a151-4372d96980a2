! Provides compiler-independent, system-call extensions
!
! Note: should not have any USE statements
!       apart those required by compiler vendors.

module system_extensions

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: se_sleep, se_wall_clock, se_wall_time
  public :: se_shell, se_system, se_chdir
  public :: se_open, se_open_big, se_file_unlink, se_flush
  public :: se_exit

  public :: floats_not_equal
  interface floats_not_equal
    module procedure real_floats_not_equal
    module procedure complex_floats_not_equal
  end interface

  public :: is_a_nan ! FIXME: rumored not to work -- remove?
  interface is_a_nan
    module procedure real_r4_is_a_nan
    module procedure real_r8_is_a_nan
    module procedure complex_r4_is_a_nan
    module procedure complex_r8_is_a_nan
  end interface

  public :: is_close_to_a_nan ! FIXME: rumored not to work -- remove?
  interface is_close_to_a_nan
    module procedure real_r4_is_close_to_a_nan
    module procedure real_r8_is_close_to_a_nan
    module procedure complex_r4_is_close_to_a_nan
    module procedure complex_r8_is_close_to_a_nan
  end interface

contains

!============================ SE_FLUSH =======================================80
!
! Modifies the flush system extension for different compilers.
! The unit number is optional, it defaults to 6 if not present.
!
!=============================================================================80

  subroutine se_flush(unit_number)

#ifdef HAVE_F90_UNIX
    use f90_unix_io, only : flush
#endif

    integer, optional, intent(in) :: unit_number

    integer :: unit

    continue

    unit = 6 ! default stdout for most compilers FIXME: find it and set STDOUT
    if (present(unit_number)) unit = unit_number

#ifdef FORTRAN_FLUSH_UNDERSCORE
    call flush_(unit)
#else
    call flush(unit)
#endif

  end subroutine se_flush

!============================ SE_CHDIR =======================================80
!
! a compiler-nuetral change directory function.
!
!=============================================================================80

  subroutine se_chdir(path)

#ifdef HAVE_F90_UNIX
    use f90_unix_dir, only : chdir
#endif

    character(*), intent(in) :: path

#ifdef HAVE_PXF
    integer :: errno
#endif

    continue

#ifdef HAVE_PXF
    call pxfchdir( path, 0, errno ) ! 0 trims path
#else
    call chdir( trim(path) )
#endif

  end subroutine se_chdir

!============================ SE_SLEEP =======================================80
!
! modifies the sleep system extension for different compilers.
!
!=============================================================================80

  subroutine se_sleep(seconds)

#ifdef HAVE_F90_UNIX
    use f90_unix_proc, only : sleep
#endif

    integer, intent(in) :: seconds

#ifdef HAVE_PXF
    integer :: seconds_left, errno
#endif

    continue

#ifdef HAVE_PXF
    call pxfsleep(seconds, seconds_left, errno)
#else
    call sleep(seconds)
#endif

  end subroutine se_sleep

!============================ SE_WALL_CLOCK ==================================80
!
! provides wall clock time
!
!=============================================================================80

  function se_wall_clock()

    use kinddefs, only : dp
#ifdef HAVE_FORTRAN_ETIME
    use kinddefs, only : r4
    real(r4), dimension(2) :: tarray
    real(r4) :: etime
    external etime
#else
    integer :: ticks, ticks_per_sec, max_ticks
#endif

    real(dp) :: se_wall_clock

    continue

#ifdef HAVE_FORTRAN_ETIME
    se_wall_clock = real(etime(tarray),dp)
#else
    call system_clock(count=ticks,count_rate=ticks_per_sec,count_max=max_ticks)

    if ( 0 == ticks_per_sec ) then
      se_wall_clock = 0.0_dp
    else
      se_wall_clock = real(ticks,dp) / real(ticks_per_sec,dp)
    end if
#endif

  end function se_wall_clock

!============================ SE_WALL_TIME ===================================80
!
! provides wall clock time
!
!=============================================================================80

  subroutine se_wall_time(string)
    character(*), intent(in) :: string
    logical :: file_exists
    integer :: i, hwm, rss
    character(80) :: line
  continue
     inquire( file='/proc/self/status', exist=file_exists )
     if (file_exists) then
        open(90000,file='/proc/self/status')
        hwm = 0; rss = 0
        do i = 1, 10000
           read(90000,'(a)',end=99) line
           if (line(1:6)=='VmHWM:') read(line(8:80),*) hwm
           if (line(1:6)=='VmRSS:') read(line(8:80),*) rss
           if ((hwm > 0).and.(rss > 0)) exit
        end do
        99 close(90000)
        write(*,'(" ",a," TIME/Mem(MB): ",3(1x,F8.2,1x))')                     &
          trim(string),se_wall_clock(),hwm/1024.0,rss/1024.0
     else
        write(*,'(" ",a," ",F12.4)') trim(string),se_wall_clock()
     end if
  end subroutine se_wall_time

!============================ SE_SHELL =======================================80
!
! Performs a system call and (optionally) returns the call's error code
!
! Note: a convenience wrapper for the se_system function.
!
!=============================================================================80

  subroutine se_shell( command, exit_status_arg )

    character(*),           intent(in)  :: command
    integer,      optional, intent(out) :: exit_status_arg

    integer :: exit_status

    continue

    exit_status = se_system( command )

    if (present(exit_status_arg)) exit_status_arg = exit_status

  end subroutine se_shell

!============================ SE_SYSTEM ======================================80
!
! Performs a system call and returns the exit status code
!
!=============================================================================80

  function se_system(command)

#ifdef HAVE_F90_UNIX
    use f90_unix_proc, only : system
#endif

    integer                  :: se_system
    character(*), intent(in) :: command

#if !defined(HAVE_F90_UNIX) && !defined(HAVE_PXF)
    integer :: system
#endif

    continue

#ifdef HAVE_F90_UNIX
    call system( trim(command), se_system )
#elif defined(HAVE_PXF)
    call pxfsystem( command, 0, se_system ) ! 0 trims command
#else
    se_system = system( trim(command) )
#endif

  end function se_system

!============================ SE_FILE_UNLINK =================================80
!
! Remove file
!
!=============================================================================80

  subroutine se_file_unlink(file,errno)

#ifdef HAVE_F90_UNIX
    use f90_unix_dir, only : unlink
#endif

    character(*),           intent(in)  :: file
    integer,      optional, intent(out) :: errno

#if ! defined(HAVE_F90_UNIX) && !defined(HAVE_PXF)
    integer :: unlink
#endif
    integer :: lerrno

    continue

#ifdef HAVE_F90_UNIX
    call unlink(file, lerrno)
#elif defined(HAVE_PXF)
    call pxfunlink(file, 0, lerrno) ! 0 trims filename
#else
    lerrno = unlink(file)
#endif

   if (present(errno)) errno = lerrno

  end subroutine se_file_unlink

!==================================== SE_EXIT ================================80
!
! Terminate program and optionally yield non-zero exit status
!
!=============================================================================80

  subroutine se_exit(status_code_arg)

#ifdef HAVE_F90_UNIX
    use f90_unix, only : exit
#endif

    integer, optional, intent(in) :: status_code_arg

    integer :: status_code = 0

    continue

    if (present(status_code_arg)) status_code = status_code_arg

#ifdef HAVE_PXF
    call pxffastexit(status_code)
#else
    call exit(status_code)
#endif

  end subroutine se_exit

!============================= FLOATS_NOT_EQUAL ==============================80
!
! Compare two floating numbers with a spacing tolerance
!
!  print*, floats_not_equal( 1.4_dp, 1.4_dp )                     !=> FALSE
!  print*, floats_not_equal( 1.4_dp, 1.4_dp +   spacing(1.0_dp) ) !=> FALSE
!  print*, floats_not_equal( 1.4_dp, 1.4_dp + 2*spacing(1.0_dp) ) !=> TRUE
!  print*, floats_not_equal( 1.4_dp, 1.0_dp )                     !=> TRUE
!
!=============================================================================80

  pure function real_floats_not_equal( float1, float2 )

    use kinddefs, only : dp

    logical              :: real_floats_not_equal
    real(dp), intent(in) :: float1, float2

    continue

    real_floats_not_equal = abs(float2-float1) > spacing(float1)

  end function real_floats_not_equal

  pure function complex_floats_not_equal( float1, float2 )

    use kinddefs, only : dp

    logical              :: complex_floats_not_equal
    complex(dp), intent(in) :: float1, float2

    continue

    complex_floats_not_equal = &
      abs(real(float2,dp)-real(float1,dp)) > spacing(real(float1,dp))

  end function complex_floats_not_equal

!============================ SE_OPEN  =======================================80
!
! Performs a fortran open statement
!
! Note, the current implementation uses a combinatorial search.
! An initial approach to use blanks for the missing options resulted in the
! compiler not writing to named file (i.e., the Intel compiler wrote to
! fort.xxx if blanks values were tried; open (unit=10,file="abc",form="") ).
!
!=============================================================================80

  subroutine se_open(unit,file,form,status,access,iostat,position)

    integer,                intent(in)  :: unit
    character(*), optional, intent(in)  :: file
    character(*), optional, intent(in)  :: form
    character(*), optional, intent(in)  :: status
    character(*), optional, intent(in)  :: access
    integer,      optional, intent(out) :: iostat
    character(*), optional, intent(in)  :: position

    integer        :: iflag, liostat
    logical        :: debug = .false.
    character(256) :: lfile, lform, lstatus, laccess, lposition

    continue

#ifdef HAVE_OPEN_STREAM
#else
    have_access : if ( present(access) ) then
      access_stream : if ( access == 'stream' .or. access == 'STREAM' ) then
        if ( present(iostat) ) iostat = 1
        write(*,*) &
"system_extensions:se_open(access='stream') not supported by this compiler"
        return
      end if access_stream
    end if have_access
#endif

    iflag = 0
    if (present(file))     iflag = iflag + 00001
    if (present(form))     iflag = iflag + 00010
    if (present(status))   iflag = iflag + 00100
#ifdef HAVE_OPEN_STREAM
    if (present(access)  ) iflag = iflag + 01000
#endif
    if (present(position)) iflag = iflag + 10000

    select case (iflag)
    case (00000)
      open (unit=unit,                                  iostat=liostat)
    case (00001)
      open (unit=unit,file=file,                        iostat=liostat)
    case (00010)
      open (unit=unit,          form=form,              iostat=liostat)
    case (00011)
      open (unit=unit,file=file,form=form,              iostat=liostat)
    case (00100)
      open (unit=unit,                    status=status,iostat=liostat)
    case (00101)
      open (unit=unit,file=file,          status=status,iostat=liostat)
    case (00110)
      open (unit=unit,          form=form,status=status,iostat=liostat)
    case (00111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat)
    case (01000)
      open (unit=unit,                                  iostat=liostat, &
            access=access)
    case (01001)
      open (unit=unit,file=file,                        iostat=liostat, &
            access=access)
    case (01010)
      open (unit=unit,          form=form,              iostat=liostat, &
            access=access)
    case (01011)
      open (unit=unit,file=file,form=form,              iostat=liostat, &
            access=access)
    case (01100)
      open (unit=unit,                    status=status,iostat=liostat, &
            access=access)
    case (01101)
      open (unit=unit,file=file,          status=status,iostat=liostat, &
            access=access)
    case (01110)
      open (unit=unit,          form=form,status=status,iostat=liostat, &
            access=access)
    case (01111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat, &
            access=access)
    case (10001)
      open (unit=unit,file=file,                        iostat=liostat, &
                          position=position)
    case (10011)
      open (unit=unit,file=file,form=form,              iostat=liostat, &
                          position=position)
    case (10111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat, &
                          position=position)
    case (11111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat, &
            access=access,position=position)
    case default
      write(*,*) "error in se_open ", iflag
    end select

    if (debug) then
       lfile     = "''"; if (present(file))     lfile     = file
       lform     = "''"; if (present(form))     lform     = form
       lstatus   = "''"; if (present(status))   lstatus   = status
       laccess   = "''"; if (present(access))   laccess   = access
       lposition = "''"; if (present(position)) lposition = position

       write(*,*)'se_open: open(unit=',unit,',file=',trim(lfile),       &
                 ',form=',trim(lform),                                  &
                 ',status=',trim(lstatus),',access=',trim(laccess),     &
                 ',iostat=',liostat,',position=',lposition,')'
    end if

   if (present(iostat)) iostat = liostat

  end subroutine se_open

  subroutine se_open_big(unit,file,form,status,access,iostat,convert)

    integer,                intent(in)  :: unit
    character(*), optional, intent(in)  :: file
    character(*), optional, intent(in)  :: form
    character(*), optional, intent(in)  :: status
    character(*), optional, intent(in)  :: access
    integer,      optional, intent(out) :: iostat
    character(*), optional, intent(in)  :: convert

    integer        :: iflag, liostat
    logical        :: debug = .false.
    character(256) :: lfile, lform, lstatus, laccess, lconvert

    continue

#ifdef HAVE_OPEN_STREAM
#else
    have_access : if ( present(access) ) then
      access_stream : if ( access == 'stream' .or. access == 'STREAM' ) then
        if ( present(iostat) ) iostat = 1
        write(*,*) &
"system_extensions:se_open_big(access='stream') not supported by this compiler"
        return
      end if access_stream
    end if have_access
#endif

    iflag = 0
    if (present(file))     iflag = iflag + 00001
    if (present(form))     iflag = iflag + 00010
    if (present(status))   iflag = iflag + 00100
#ifdef HAVE_OPEN_STREAM
    if (present(access))   iflag = iflag + 01000
#endif
#ifdef HAVE_OPEN_CONVERT_BIG
    if (present(convert))  iflag = iflag + 10000
#endif

    select case (iflag)
    case (00000)
      open (unit=unit,                                  iostat=liostat)
    case (00001)
      open (unit=unit,file=file,                        iostat=liostat)
    case (00010)
      open (unit=unit,          form=form,              iostat=liostat)
    case (00011)
      open (unit=unit,file=file,form=form,              iostat=liostat)
    case (00100)
      open (unit=unit,                    status=status,iostat=liostat)
    case (00101)
      open (unit=unit,file=file,          status=status,iostat=liostat)
    case (00110)
      open (unit=unit,          form=form,status=status,iostat=liostat)
    case (00111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat)
    case (01000)
      open (unit=unit,                                  iostat=liostat, &
            access=access)
    case (01001)
      open (unit=unit,file=file,                        iostat=liostat, &
            access=access)
    case (01010)
      open (unit=unit,          form=form,              iostat=liostat, &
            access=access)
    case (01011)
      open (unit=unit,file=file,form=form,              iostat=liostat, &
            access=access)
    case (01100)
      open (unit=unit,                    status=status,iostat=liostat, &
            access=access)
    case (01101)
      open (unit=unit,file=file,          status=status,iostat=liostat, &
            access=access)
    case (01110)
      open (unit=unit,          form=form,status=status,iostat=liostat, &
            access=access)
    case (01111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat, &
            access=access)
#ifdef HAVE_OPEN_CONVERT_BIG
    case (10001)
      open (unit=unit,file=file,                        iostat=liostat, &
                          convert=convert)
    case (10010)
      open (unit=unit,          form=form,              iostat=liostat, &
                          convert=convert)
    case (10011)
      open (unit=unit,file=file,form=form,              iostat=liostat, &
                          convert=convert)
    case (10110)
      open (unit=unit,          form=form,status=status,iostat=liostat, &
                          convert=convert)
    case (10111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat, &
                          convert=convert)
    case (11100)
      open (unit=unit,                    status=status,iostat=liostat, &
            access=access,convert=convert)
    case (11101)
      open (unit=unit,file=file,          status=status,iostat=liostat, &
            access=access,convert=convert)
    case (11110)
      open (unit=unit,          form=form,status=status,iostat=liostat, &
            access=access,convert=convert)
    case (11111)
      open (unit=unit,file=file,form=form,status=status,iostat=liostat, &
            access=access,convert=convert)
#endif
    case default
      write(*,*) "error in se_open_big ", iflag
    end select

    if (debug) then
       lfile     = "''"; if (present(file))     lfile     = file
       lform     = "''"; if (present(form))     lform     = form
       lstatus   = "''"; if (present(status))   lstatus   = status
       laccess   = "''"; if (present(access))   laccess   = access
       lconvert  = "''"; if (present(convert))  lconvert  = convert

       write(*,*)'se_open: open(unit=',unit,',file=',trim(lfile),       &
                 ',form=',trim(lform),                                  &
                 ',status=',trim(lstatus),',access=',trim(laccess),     &
                 ',iostat=',liostat,',convert=',lconvert,')'
    end if

   if (present(iostat)) iostat = liostat

 end subroutine se_open_big

!============================ is_a_nan =======================================80
!
! tests spcific number types to see if they are nan's
!
!=============================================================================80
  function real_r4_is_a_nan( rms )

    use kinddefs, only : system_r4

    logical :: real_r4_is_a_nan
    real(system_r4), intent(in) :: rms

    real(system_r4), parameter :: zero = 0.0_system_r4

    continue

    real_r4_is_a_nan = &
      ( .not. ( rms < abs(rms) .or. (rms >= zero) ) )

  end function real_r4_is_a_nan

  function real_r8_is_a_nan( rms )

    use kinddefs, only : system_r8

    logical :: real_r8_is_a_nan
    real(system_r8), intent(in) :: rms

    real(system_r8), parameter :: zero = 0.0_system_r8

    continue

    real_r8_is_a_nan = &
      ( .not. ( rms < abs(rms) .or. (rms >= zero) ) )

  end function real_r8_is_a_nan

  function complex_r4_is_a_nan( rms )

    use kinddefs, only : system_r4

    logical :: complex_r4_is_a_nan
    complex(system_r4), intent(in) :: rms

    continue

    complex_r4_is_a_nan = &
      ( is_a_nan(real(rms,system_r4)) .or. is_a_nan(aimag(rms)) )

  end function complex_r4_is_a_nan

  function complex_r8_is_a_nan( rms )

    use kinddefs, only : system_r8

    logical :: complex_r8_is_a_nan
    complex(system_r8), intent(in) :: rms

    continue

    complex_r8_is_a_nan = &
      ( is_a_nan(real(rms,system_r8)) .or. is_a_nan(aimag(rms)) )

  end function complex_r8_is_a_nan

!============================ IS_CLOSE_TO_A_NAN ==============================80
!
! tests spcific number types to see if they are NaNs,
! (This routine stems from Dave Lockard and more aggressively identifies
!  NaNs that the one above).
!
!=============================================================================80
  function real_r4_is_close_to_a_nan( rms )

    use kinddefs, only : system_r4

    logical :: real_r4_is_close_to_a_nan
    real(system_r4), intent(in) :: rms

    real(system_r4), parameter :: zero = 0.0_system_r4

    continue

    real_r4_is_close_to_a_nan =                              &
      ( ( (rms+rms<=rms) .and. (abs(rms)>tiny(zero)) ) .or.  &
      (  .not. ( (rms<abs(rms)) .or. (rms>=zero) ) ) )

  end function real_r4_is_close_to_a_nan

  function real_r8_is_close_to_a_nan( rms )

    use kinddefs, only : system_r8

    logical :: real_r8_is_close_to_a_nan
    real(system_r8), intent(in) :: rms

    real(system_r8), parameter :: zero = 0.0_system_r8

    continue

    real_r8_is_close_to_a_nan =                              &
      ( ( (rms+rms<=rms) .and. (abs(rms)>tiny(zero)) ) .or.  &
      (  .not. ( (rms<abs(rms)) .or. (rms>=zero) ) ) )

  end function real_r8_is_close_to_a_nan

  function complex_r4_is_close_to_a_nan( rms )

    use kinddefs, only : system_r4

    logical :: complex_r4_is_close_to_a_nan
    complex(system_r4), intent(in) :: rms

    continue

    complex_r4_is_close_to_a_nan =                  &
      ( is_close_to_a_nan(real(rms,system_r4)) .or. &
        is_close_to_a_nan(aimag(rms)) )

  end function complex_r4_is_close_to_a_nan

  function complex_r8_is_close_to_a_nan( rms )

    use kinddefs, only : system_r8

    logical :: complex_r8_is_close_to_a_nan
    complex(system_r8), intent(in) :: rms

    continue

    complex_r8_is_close_to_a_nan =                  &
      ( is_close_to_a_nan(real(rms,system_r8)) .or. &
        is_close_to_a_nan(aimag(rms)) )

  end function complex_r8_is_close_to_a_nan

end module system_extensions

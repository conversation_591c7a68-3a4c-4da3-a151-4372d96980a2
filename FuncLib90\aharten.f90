!=============================== AHARTEN =====================================80
!
! Absolute value of an eigenvalue limited function
! Harten entropy limiter function method.
!
!=============================================================================80

  pure function aharten(eigenvalue, eigenvalue_limit)

    use kinddefs,        only : dp

    real(dp), intent(in) :: eigenvalue, eigenvalue_limit
    real(dp)             :: aharten

    real(dp), parameter :: my_half = 0.50_dp

    continue

    aharten = abs( eigenvalue )

    if ( aharten < eigenvalue_limit ) &
      aharten = my_half*( eigenvalue**2 / eigenvalue_limit + eigenvalue_limit )

  end function aharten

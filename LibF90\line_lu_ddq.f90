!
! DO NOT EDIT this file.  It was generated by the ruby script,
!
! Instead,
! 1. Edit line_lu_ddq.template
! 2. Regenerate this file through the command below:
!               ruby LinAlg.rb line_lu_ddq
! 3. Commit line_lu_ddq.f90 and line_lu_ddq.template
!
module line_lu_ddq

  use kinddefs,        only : dp, jp, odp
  use monitor,         only : compute_direction

  implicit none

  private

  public :: setup_tridiagonals_ddq
  public :: block_tridiag_info

contains

!============================ BLOCK_TRIDIAG_INFO =============================80
!
! Prints some information about the block tridiagonals
!
!=============================================================================80
  subroutine block_tridiag_info(n_lines, first_neq0, neq0, block_size, nr)

    use lmpi,        only : lmpi_reduce
    use info_depr,   only : skeleton

    integer, intent(in)    :: n_lines, neq0, block_size, nr

    integer, dimension(n_lines+1), intent(in) :: first_neq0

    integer :: sum_pts, total_pts, total_neq0
    integer :: line, total_lines

  continue

! Loop over lines

    sum_pts = 0

    loop_over_lines : do line = 1,n_lines

      sum_pts = sum_pts + first_neq0(line+1) - first_neq0(line)

    end do loop_over_lines

    call lmpi_reduce( sum_pts, total_pts)
    call lmpi_reduce( n_lines, total_lines)
    call lmpi_reduce( neq0, total_neq0)

    if(skeleton > 10) then
      write(*,*)
      write(*,*) 'Decomposing tridiagonals...block_sizes=',block_size,nr
      write(*,*) ' ..............Total lines=',total_lines,' Avg. pts/line=',  &
                 real( total_pts , dp) / real( total_lines , dp )
      write(*,*) ' ................Total pts=',total_pts
      write(*,*) ' ...........Field coverage=',                                &
                 real( total_pts , dp) / real( total_neq0 , dp )
    endif


  end subroutine block_tridiag_info

!======================== DECOMPOSE_TRIDIAG ==================================80
!
! Routes the code into the appropriate block tridiagonal decomposition
!
!=============================================================================80
  subroutine decompose_tridiag(n_pts, nb, a, b, c, force_general_path)

    integer, intent(in)           :: n_pts, nb
    integer, intent(in), optional :: force_general_path

    real(dp), dimension(nb,nb,n_pts), intent(inout) :: a,b,c

  continue

    if(.not.present(force_general_path)) then

      select case (nb)
      case(6)
        call decompose_tridiag_6(n_pts, nb, a, b, c)
      case(5)
        call decompose_tridiag_5(n_pts, nb, a, b, c)
      case(4)
        call decompose_tridiag_4(n_pts, nb, a, b, c)
      case(3)
        call decompose_tridiag_3(n_pts, nb, a, b, c)
      case(2)
        call decompose_tridiag_2(n_pts, nb, a, b, c)
      case(1)
        call decompose_tridiag_1(n_pts, nb, a, b, c)
      case default
        call decompose_tridiag_n(n_pts, nb, a, b, c)
      end select

    else

      if(force_general_path == 1) then

        call decompose_tridiag_n(n_pts, nb, a, b, c)

      endif

    endif

  end subroutine decompose_tridiag



!============================ DECOMPOSE_TRIDIAG_N ============================80
!
! Performs decomposition on block tridiagonal NxN
! system of specified equation set.
!
! The linear algebra is based on the solve from CFL3D.
!
!=============================================================================80
  subroutine decompose_tridiag_n(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(inout) :: a,b,c

    integer :: i, j, il, iu
    integer :: m, l, nb1, l1, mm, mm1

    real(dp) :: apv, bpv

  continue

    nb1 = nb - 1

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

! LU decomposition

    if( iu>il ) then ! Really solving a line, not a point
      do m = il,iu-1
        l = m
        l1 = l + 1
        do mm = 1,nb1
          mm1 = mm + 1
          apv = 1._dp / b(mm,mm,l)
          do i = mm1,nb
            bpv = b(i,mm,l) * apv
            do j = mm1,nb
              b(i,j,l) = b(i,j,l) - b(mm,j,l) * bpv
            end do
            do j = 1,nb
              c(i,j,l) = c(i,j,l) - c(mm,j,l) * bpv
            end do
          end do
          do i = 1,nb
            bpv = a(i,mm,l1) * apv
            do j = mm1,nb
              a(i,j,l1) = a(i,j,l1) - b(mm,j,l) * bpv
            end do
            do j = 1,nb
              b(i,j,l1) = b(i,j,l1) - c(mm,j,l) * bpv
            end do
          end do
        end do
        mm = nb
        apv = 1._dp / b(mm,mm,l)
        do i = 1,nb
          bpv = a(i,mm,l1) * apv
          do j = 1,nb
            b(i,j,l1) = b(i,j,l1) - c(mm,j,l) * bpv
          end do
        end do
      end do
    end if

    l = iu  ! Final part of line solve, or point only solve

    do mm = 1,nb1
      mm1 = mm + 1
      apv = 1._dp / b(mm,mm,l)
      do i = mm1,nb
        bpv = b(i,mm,l) * apv
        do j = mm1,nb
           b(i,j,l) = b(i,j,l) - b(mm,j,l) *bpv
        end do
      end do
    end do

  end subroutine decompose_tridiag_n

!======================== SETUP_TRIDIAGONALS_DDQ =============================80
!
! Copies the elements of the coefficient matrix into local arrays
! required for block tridiagonal solves along implicit lines
!
!=============================================================================80
  subroutine setup_tridiagonals_ddq( solve_backwards,neq0, nja,                &
                                     n_lines,n_line_neq0,first_neq0,           &
                                     line_neq0,a_diag,a_off, aa, bb, cc,       &
                                     nonzero_below,nonzero_above,              &
                                     nb, nm, lines, lu_offset, colored_sweeps, &
                                     color_indices,g2m,                        &
                                     ia, nzg2m, s_relaxation )

    use fun3d_maximums, only : max_dof_in_line

    integer, intent(in) :: neq0, nja, colored_sweeps
    integer, intent(in) :: n_line_neq0, n_lines, nb, nm

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(n_line_neq0),      intent(in) :: nonzero_below
    integer, dimension(n_line_neq0),      intent(in) :: nonzero_above
    integer, dimension(n_lines),          intent(in) :: lines
    integer, dimension(:),                intent(in) :: g2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    integer, dimension(n_lines),            intent(out) :: lu_offset
    real(dp), dimension(nm,nm,neq0),        intent(in)  :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)  :: a_off
    real(jp), dimension(nb,nb,n_line_neq0), intent(out) :: aa, bb, cc
    integer, dimension(:),                  intent(in) :: ia
    integer, dimension(:),                  intent(in) :: nzg2m

    real(dp), intent(in), optional :: s_relaxation

    logical, intent(in) :: solve_backwards

    integer :: offset, i, j, k, stride, bottom_location, top_location
    integer :: line, local_eqn_number
    integer :: equation, entry_below, entry_above, row, col, start, end
    integer :: sweep_start, sweep_end, sweep_stride, colored_sweep, n
    integer :: kk, kstart, kend, ioff

    integer, dimension(colored_sweeps) :: start_array, end_array, stride_array

    real(dp), dimension(nb,nb,max_dof_in_line) :: a, b, c

  continue

    do i = 1, max_dof_in_line
      do j = 1, nb
        do k = 1, nb
          a(j,k,i) = 0.0_dp
          b(j,k,i) = 0.0_dp
          c(j,k,i) = 0.0_dp
        end do
      end do
    end do

    ! Compute start, end, and stride for all colored_sweeps

    call compute_direction(colored_sweeps,color_indices,solve_backwards,       &
                           start_array,end_array,stride_array)

    sweep_start  = 1
    sweep_end    = colored_sweeps
    sweep_stride = 1

    if ( solve_backwards ) then
      sweep_start  = colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    offset = 0

    color_loop : do colored_sweep = sweep_start, sweep_end, sweep_stride

      start  = start_array(colored_sweep)
      end    = end_array(colored_sweep)
      stride = stride_array(colored_sweep)

      ! Loop over lines and set up local block tridiagonal system for each

      lines_within_color : do n = start, end, stride

        line = lines(n)

        bottom_location = first_neq0(line)
        top_location    = first_neq0(line+1) - 1

        ! Copy appropriate jacobian blocks into local memory for the solve
        ! We will assume the equations are numbered from 1 to whatever, with
        ! 1 being the first point (lowest point on the line)

        local_eqn_number = 0
        set_up_loop_within_line : do j = bottom_location, top_location

          local_eqn_number = local_eqn_number + 1

          equation    = line_neq0(j)
          entry_below = 0
          entry_above = 0

          ! Fill a for all local equations except the first one

          if( j > bottom_location ) then
            entry_below = nonzero_below(j)
            do row = 1, nb
              do col = 1, nb
                a(row,col,local_eqn_number) = a_off(row,col,entry_below)
              end do
            end do
          endif

          ! Fill b for all local equations

          do row = 1, nb
            do col = 1, nb
              b(row,col,local_eqn_number) = a_diag(row,col,g2m(equation))
            end do
          end do

          ! Fill c for all local equations except the last one

          if( j < top_location ) then
            entry_above = nonzero_above(j)
            do row = 1, nb
              do col = 1, nb
                c(row,col,local_eqn_number) = a_off(row,col,entry_above)
              end do
            end do
          endif

          ! Augment (possibly) b with off-diagonal elements not in line.

          if ( .not. present(s_relaxation) ) cycle

          if ( abs( s_relaxation ) < 0.001_dp ) cycle

          kstart = ia(equation)
          kend   = ia(equation+1) - 1
          do kk = kstart, kend
            ioff = nzg2m(kk)
            if( ioff == entry_below .or. ioff == entry_above ) cycle
            do row = 1, nb
              do col = 1, nb
                b(row,col,local_eqn_number) = b(row,col,local_eqn_number) &
                                            + s_relaxation*a_off(row,col,ioff)
              end do
            end do
          end do

        end do set_up_loop_within_line

        call decompose_tridiag(local_eqn_number, nb, a, b, c)

        local_eqn_number = 0
        install_loop_within_line : do j = bottom_location, top_location

          local_eqn_number = local_eqn_number + 1

          do row = 1, nb
            do col = 1, nb
              aa(row,col,offset+local_eqn_number) = a(row,col,local_eqn_number)
              bb(row,col,offset+local_eqn_number) = b(row,col,local_eqn_number)
              cc(row,col,offset+local_eqn_number) = c(row,col,local_eqn_number)
            end do
          end do

        end do install_loop_within_line

        lu_offset(line) = offset

        offset = offset + local_eqn_number

      end do lines_within_color

    end do color_loop

  end subroutine setup_tridiagonals_ddq

! no comment
!============================ DECOMPOSE_TRIDIAG_1 ============================80
!
! Performs decomposition on block tridiagonal 1x1
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine decompose_tridiag_1(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(in)    :: a
    real(dp), dimension(nb,nb,n_pts), intent(inout) :: b,c

    integer :: i, il, iu
    integer :: row, col
    integer :: il1, ir

  continue

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

    il1 = il + 1
    i   = il

! LU decomposition

    b(1,1,i) = 1.0_dp/b(1,1,i)



    if (i /= iu) then

! c = ainv*c

      do col = 1, 1
        c(1,col,i) = b(1,1,i)*(c(1,col,i))

      end do

    end if

! Forward sweep

    forward_sweep : do i = il1,iu
      ir = i - 1

! First row reduction

      do row = 1, 1
        do col = 1, 1
            b(row,col,i) = b(row,col,i)                           &
                                 - a(row,1,i)*c(1,col,+ir)
        end do
      end do

! LU decomposition

      b(1,1,i) = 1.0_dp/b(1,1,i)



      if (i == iu) cycle forward_sweep

! c = ainv*c

      do col = 1, 1
        c(1,col,i) = b(1,1,i)*(c(1,col,i))

      end do

    end do forward_sweep

  end subroutine decompose_tridiag_1

! no comment
!============================ DECOMPOSE_TRIDIAG_2 ============================80
!
! Performs decomposition on block tridiagonal 2x2
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine decompose_tridiag_2(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(in)    :: a
    real(dp), dimension(nb,nb,n_pts), intent(inout) :: b,c

    integer :: i, il, iu
    integer :: row, col
    integer :: il1, ir

  continue

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

    il1 = il + 1
    i   = il

! LU decomposition

    b(1,1,i) = 1.0_dp/b(1,1,i)
    b(1,2,i) = b(1,1,i)*b(1,2,i)
    b(2,2,i) = 1.0_dp/(b(2,2,i)                                  &
                         -b(2,1,i)*b(1,2,i))



    if (i /= iu) then

! c = ainv*c

      do col = 1, 2
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))

        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,2,i)*c(2,col,i)
      end do

    end if

! Forward sweep

    forward_sweep : do i = il1,iu
      ir = i - 1

! First row reduction

      do row = 1, 2
        do col = 1, 2
            b(row,col,i) = b(row,col,i)                           &
                                 - a(row,1,i)*c(1,col,+ir)        &
                                 - a(row,2,i)*c(2,col,+ir)
        end do
      end do

! LU decomposition

      b(1,1,i) = 1.0_dp/b(1,1,i)
      b(1,2,i) = b(1,1,i)*b(1,2,i)
      b(2,2,i) = 1.0_dp/(b(2,2,i)                                &
                           -b(2,1,i)*b(1,2,i))



      if (i == iu) cycle forward_sweep

! c = ainv*c

      do col = 1, 2
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))

        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,2,i)*c(2,col,i)
      end do

    end do forward_sweep

  end subroutine decompose_tridiag_2

! no comment
!============================ DECOMPOSE_TRIDIAG_3 ============================80
!
! Performs decomposition on block tridiagonal 3x3
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine decompose_tridiag_3(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(in)    :: a
    real(dp), dimension(nb,nb,n_pts), intent(inout) :: b,c

    integer :: i, il, iu
    integer :: row, col
    integer :: il1, ir

  continue

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

    il1 = il + 1
    i   = il

! LU decomposition

    b(1,1,i) = 1.0_dp/b(1,1,i)
    b(1,2,i) = b(1,1,i)*b(1,2,i)
    b(1,3,i) = b(1,1,i)*b(1,3,i)
    b(2,2,i) = 1.0_dp/(b(2,2,i)                                  &
                         -b(2,1,i)*b(1,2,i))
    b(2,3,i) = b(2,2,i)*(b(2,3,i)                                &
                         -b(2,1,i)* b(1,3,i))
    b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
    b(3,3,i) = 1.0_dp/(b(3,3,i)                                  &
                         -b(3,1,i)*b(1,3,i)                      &
                         -b(3,2,i)*b(2,3,i))



    if (i /= iu) then

! c = ainv*c

      do col = 1, 3
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))

        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end if

! Forward sweep

    forward_sweep : do i = il1,iu
      ir = i - 1

! First row reduction

      do row = 1, 3
        do col = 1, 3
            b(row,col,i) = b(row,col,i)                           &
                                 - a(row,1,i)*c(1,col,+ir)        &
                                 - a(row,2,i)*c(2,col,+ir)        &
                                 - a(row,3,i)*c(3,col,+ir)
        end do
      end do

! LU decomposition

      b(1,1,i) = 1.0_dp/b(1,1,i)
      b(1,2,i) = b(1,1,i)*b(1,2,i)
      b(1,3,i) = b(1,1,i)*b(1,3,i)
      b(2,2,i) = 1.0_dp/(b(2,2,i)                                &
                           -b(2,1,i)*b(1,2,i))
      b(2,3,i) = b(2,2,i)*(b(2,3,i)                              &
                           -b(2,1,i)* b(1,3,i))
      b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
      b(3,3,i) = 1.0_dp/(b(3,3,i)                                &
                           -b(3,1,i)*b(1,3,i)                    &
                           -b(3,2,i)*b(2,3,i))



      if (i == iu) cycle forward_sweep

! c = ainv*c

      do col = 1, 3
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))

        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end do forward_sweep

  end subroutine decompose_tridiag_3

! no comment
!============================ DECOMPOSE_TRIDIAG_4 ============================80
!
! Performs decomposition on block tridiagonal 4x4
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine decompose_tridiag_4(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(in)    :: a
    real(dp), dimension(nb,nb,n_pts), intent(inout) :: b,c

    integer :: i, il, iu
    integer :: row, col
    integer :: il1, ir

  continue

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

    il1 = il + 1
    i   = il

! LU decomposition

    b(1,1,i) = 1.0_dp/b(1,1,i)
    b(1,2,i) = b(1,1,i)*b(1,2,i)
    b(1,3,i) = b(1,1,i)*b(1,3,i)
    b(1,4,i) = b(1,1,i)*b(1,4,i)
    b(2,2,i) = 1.0_dp/(b(2,2,i)                                  &
                         -b(2,1,i)*b(1,2,i))
    b(2,3,i) = b(2,2,i)*(b(2,3,i)                                &
                         -b(2,1,i)* b(1,3,i))
    b(2,4,i) = b(2,2,i)*(b(2,4,i)                                &
                         -b(2,1,i)* b(1,4,i))
    b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
    b(3,3,i) = 1.0_dp/(b(3,3,i)                                  &
                         -b(3,1,i)*b(1,3,i)                      &
                         -b(3,2,i)*b(2,3,i))
    b(3,4,i) = b(3,3,i)*(b(3,4,i)                                &
                         -b(3,1,i)* b(1,4,i)                     &
                         -b(3,2,i)* b(2,4,i))

    b(4,2,i) = b(4,2,i)-b(4,1,i)*b(1,2,i)
    b(4,3,i) = b(4,3,i)-b(4,1,i)*b(1,3,i)                        &
                         -b(4,2,i)*b(2,3,i)
    b(4,4,i) = 1.0_dp/(b(4,4,i)                                  &
                         -b(4,1,i)*b(1,4,i)                      &
                         -b(4,2,i)*b(2,4,i)                      &
                         -b(4,3,i)*b(3,4,i))


    if (i /= iu) then

! c = ainv*c

      do col = 1, 4
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))
        c(4,col,i) = b(4,4,i)*(c(4,col,i)                        &
                             - b(4,1,i)*c(1,col,i)               &
                             - b(4,2,i)*c(2,col,i)               &
                             - b(4,3,i)*c(3,col,i))

        c(3,col,i) = c(3,col,i)                                  &
                             - b(3,4,i)*c(4,col,i)
        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,4,i)*c(4,col,i)               &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,4,i)*c(4,col,i)               &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end if

! Forward sweep

    forward_sweep : do i = il1,iu
      ir = i - 1

! First row reduction

      do row = 1, 4
        do col = 1, 4
            b(row,col,i) = b(row,col,i)                           &
                                 - a(row,1,i)*c(1,col,+ir)        &
                                 - a(row,2,i)*c(2,col,+ir)        &
                                 - a(row,3,i)*c(3,col,+ir)        &
                                 - a(row,4,i)*c(4,col,+ir)
        end do
      end do

! LU decomposition

      b(1,1,i) = 1.0_dp/b(1,1,i)
      b(1,2,i) = b(1,1,i)*b(1,2,i)
      b(1,3,i) = b(1,1,i)*b(1,3,i)
      b(1,4,i) = b(1,1,i)*b(1,4,i)
      b(2,2,i) = 1.0_dp/(b(2,2,i)                                &
                           -b(2,1,i)*b(1,2,i))
      b(2,3,i) = b(2,2,i)*(b(2,3,i)                              &
                           -b(2,1,i)* b(1,3,i))
      b(2,4,i) = b(2,2,i)*(b(2,4,i)                              &
                           -b(2,1,i)* b(1,4,i))
      b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
      b(3,3,i) = 1.0_dp/(b(3,3,i)                                &
                           -b(3,1,i)*b(1,3,i)                    &
                           -b(3,2,i)*b(2,3,i))
      b(3,4,i) = b(3,3,i)*(b(3,4,i)                              &
                           -b(3,1,i)* b(1,4,i)                   &
                           -b(3,2,i)* b(2,4,i))

      b(4,2,i) = b(4,2,i)-b(4,1,i)*b(1,2,i)
      b(4,3,i) = b(4,3,i)-b(4,1,i)*b(1,3,i)                      &
                           -b(4,2,i)*b(2,3,i)
      b(4,4,i) = 1.0_dp/(b(4,4,i)                                &
                           -b(4,1,i)*b(1,4,i)                    &
                           -b(4,2,i)*b(2,4,i)                    &
                           -b(4,3,i)*b(3,4,i))


      if (i == iu) cycle forward_sweep

! c = ainv*c

      do col = 1, 4
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))
        c(4,col,i) = b(4,4,i)*(c(4,col,i)                        &
                             - b(4,1,i)*c(1,col,i)               &
                             - b(4,2,i)*c(2,col,i)               &
                             - b(4,3,i)*c(3,col,i))

        c(3,col,i) = c(3,col,i)                                  &
                             - b(3,4,i)*c(4,col,i)
        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,4,i)*c(4,col,i)               &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,4,i)*c(4,col,i)               &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end do forward_sweep

  end subroutine decompose_tridiag_4

! no comment
!============================ DECOMPOSE_TRIDIAG_5 ============================80
!
! Performs decomposition on block tridiagonal 5x5
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine decompose_tridiag_5(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(in)    :: a
    real(dp), dimension(nb,nb,n_pts), intent(inout) :: b,c

    integer :: i, il, iu
    integer :: row, col
    integer :: il1, ir

  continue

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

    il1 = il + 1
    i   = il

! LU decomposition

    b(1,1,i) = 1.0_dp/b(1,1,i)
    b(1,2,i) = b(1,1,i)*b(1,2,i)
    b(1,3,i) = b(1,1,i)*b(1,3,i)
    b(1,4,i) = b(1,1,i)*b(1,4,i)
    b(1,5,i) = b(1,1,i)*b(1,5,i)
    b(2,2,i) = 1.0_dp/(b(2,2,i)                                  &
                         -b(2,1,i)*b(1,2,i))
    b(2,3,i) = b(2,2,i)*(b(2,3,i)                                &
                         -b(2,1,i)* b(1,3,i))
    b(2,4,i) = b(2,2,i)*(b(2,4,i)                                &
                         -b(2,1,i)* b(1,4,i))
    b(2,5,i) = b(2,2,i)*(b(2,5,i)                                &
                         -b(2,1,i)* b(1,5,i))
    b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
    b(3,3,i) = 1.0_dp/(b(3,3,i)                                  &
                         -b(3,1,i)*b(1,3,i)                      &
                         -b(3,2,i)*b(2,3,i))
    b(3,4,i) = b(3,3,i)*(b(3,4,i)                                &
                         -b(3,1,i)* b(1,4,i)                     &
                         -b(3,2,i)* b(2,4,i))
    b(3,5,i) = b(3,3,i)*(b(3,5,i)                                &
                         -b(3,1,i)*b(1,5,i)                      &
                         -b(3,2,i)*b(2,5,i))

    b(4,2,i) = b(4,2,i)-b(4,1,i)*b(1,2,i)
    b(4,3,i) = b(4,3,i)-b(4,1,i)*b(1,3,i)                        &
                         -b(4,2,i)*b(2,3,i)
    b(4,4,i) = 1.0_dp/(b(4,4,i)                                  &
                         -b(4,1,i)*b(1,4,i)                      &
                         -b(4,2,i)*b(2,4,i)                      &
                         -b(4,3,i)*b(3,4,i))
    b(4,5,i) = b(4,4,i)*(b(4,5,i)                                &
                         -b(4,1,i)*b(1,5,i)                      &
                         -b(4,2,i)*b(2,5,i)                      &
                         -b(4,3,i)*b(3,5,i))
    b(5,2,i) = b(5,2,i)-b(5,1,i)*b(1,2,i)
    b(5,3,i) = b(5,3,i)-b(5,1,i)*b(1,3,i)                        &
                         -b(5,2,i)*b(2,3,i)
    b(5,4,i) = b(5,4,i)-b(5,1,i)*b(1,4,i)                        &
                         -b(5,2,i)*b(2,4,i)                      &
                         -b(5,3,i)*b(3,4,i)
    b(5,5,i) = 1.0_dp/(b(5,5,i)                                  &
                         -b(5,1,i)*b(1,5,i)                      &
                         -b(5,2,i)*b(2,5,i)                      &
                         -b(5,3,i)*b(3,5,i)                      &
                         -b(5,4,i)*b(4,5,i))


    if (i /= iu) then

! c = ainv*c

      do col = 1, 5
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))
        c(4,col,i) = b(4,4,i)*(c(4,col,i)                        &
                             - b(4,1,i)*c(1,col,i)               &
                             - b(4,2,i)*c(2,col,i)               &
                             - b(4,3,i)*c(3,col,i))
        c(5,col,i) = b(5,5,i)*(c(5,col,i)                        &
                             - b(5,1,i)*c(1,col,i)               &
                             - b(5,2,i)*c(2,col,i)               &
                             - b(5,3,i)*c(3,col,i)               &
                             - b(5,4,i)*c(4,col,i))

        c(4,col,i) = c(4,col,i)                                  &
                             - b(4,5,i)*c(5,col,i)
        c(3,col,i) = c(3,col,i)                                  &
                             - b(3,5,i)*c(5,col,i)               &
                             - b(3,4,i)*c(4,col,i)
        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,5,i)*c(5,col,i)               &
                             - b(2,4,i)*c(4,col,i)               &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,5,i)*c(5,col,i)               &
                             - b(1,4,i)*c(4,col,i)               &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end if

! Forward sweep

    forward_sweep : do i = il1,iu
      ir = i - 1

! First row reduction

      do row = 1, 5
        do col = 1, 5
            b(row,col,i) = b(row,col,i)                           &
                                 - a(row,1,i)*c(1,col,+ir)        &
                                 - a(row,2,i)*c(2,col,+ir)        &
                                 - a(row,3,i)*c(3,col,+ir)        &
                                 - a(row,4,i)*c(4,col,+ir)        &
                                 - a(row,5,i)*c(5,col,+ir)
        end do
      end do

! LU decomposition

      b(1,1,i) = 1.0_dp/b(1,1,i)
      b(1,2,i) = b(1,1,i)*b(1,2,i)
      b(1,3,i) = b(1,1,i)*b(1,3,i)
      b(1,4,i) = b(1,1,i)*b(1,4,i)
      b(1,5,i) = b(1,1,i)*b(1,5,i)
      b(2,2,i) = 1.0_dp/(b(2,2,i)                                &
                           -b(2,1,i)*b(1,2,i))
      b(2,3,i) = b(2,2,i)*(b(2,3,i)                              &
                           -b(2,1,i)* b(1,3,i))
      b(2,4,i) = b(2,2,i)*(b(2,4,i)                              &
                           -b(2,1,i)* b(1,4,i))
      b(2,5,i) = b(2,2,i)*(b(2,5,i)                              &
                           -b(2,1,i)* b(1,5,i))
      b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
      b(3,3,i) = 1.0_dp/(b(3,3,i)                                &
                           -b(3,1,i)*b(1,3,i)                    &
                           -b(3,2,i)*b(2,3,i))
      b(3,4,i) = b(3,3,i)*(b(3,4,i)                              &
                           -b(3,1,i)* b(1,4,i)                   &
                           -b(3,2,i)* b(2,4,i))
      b(3,5,i) = b(3,3,i)*(b(3,5,i)                              &
                           -b(3,1,i)*b(1,5,i)                    &
                           -b(3,2,i)*b(2,5,i))

      b(4,2,i) = b(4,2,i)-b(4,1,i)*b(1,2,i)
      b(4,3,i) = b(4,3,i)-b(4,1,i)*b(1,3,i)                      &
                           -b(4,2,i)*b(2,3,i)
      b(4,4,i) = 1.0_dp/(b(4,4,i)                                &
                           -b(4,1,i)*b(1,4,i)                    &
                           -b(4,2,i)*b(2,4,i)                    &
                           -b(4,3,i)*b(3,4,i))
      b(4,5,i) = b(4,4,i)*(b(4,5,i)                              &
                           -b(4,1,i)*b(1,5,i)                    &
                           -b(4,2,i)*b(2,5,i)                    &
                           -b(4,3,i)*b(3,5,i))
      b(5,2,i) = b(5,2,i)-b(5,1,i)*b(1,2,i)
      b(5,3,i) = b(5,3,i)-b(5,1,i)*b(1,3,i)                      &
                           -b(5,2,i)*b(2,3,i)
      b(5,4,i) = b(5,4,i)-b(5,1,i)*b(1,4,i)                      &
                           -b(5,2,i)*b(2,4,i)                    &
                           -b(5,3,i)*b(3,4,i)
      b(5,5,i) = 1.0_dp/(b(5,5,i)                                &
                           -b(5,1,i)*b(1,5,i)                    &
                           -b(5,2,i)*b(2,5,i)                    &
                           -b(5,3,i)*b(3,5,i)                    &
                           -b(5,4,i)*b(4,5,i))


      if (i == iu) cycle forward_sweep

! c = ainv*c

      do col = 1, 5
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))
        c(4,col,i) = b(4,4,i)*(c(4,col,i)                        &
                             - b(4,1,i)*c(1,col,i)               &
                             - b(4,2,i)*c(2,col,i)               &
                             - b(4,3,i)*c(3,col,i))
        c(5,col,i) = b(5,5,i)*(c(5,col,i)                        &
                             - b(5,1,i)*c(1,col,i)               &
                             - b(5,2,i)*c(2,col,i)               &
                             - b(5,3,i)*c(3,col,i)               &
                             - b(5,4,i)*c(4,col,i))

        c(4,col,i) = c(4,col,i)                                  &
                             - b(4,5,i)*c(5,col,i)
        c(3,col,i) = c(3,col,i)                                  &
                             - b(3,5,i)*c(5,col,i)               &
                             - b(3,4,i)*c(4,col,i)
        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,5,i)*c(5,col,i)               &
                             - b(2,4,i)*c(4,col,i)               &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,5,i)*c(5,col,i)               &
                             - b(1,4,i)*c(4,col,i)               &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end do forward_sweep

  end subroutine decompose_tridiag_5

! no comment
!============================ DECOMPOSE_TRIDIAG_6 ============================80
!
! Performs decomposition on block tridiagonal 6x6
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine decompose_tridiag_6(n_pts, nb, a, b, c)

    integer, intent(in) :: n_pts, nb

    real(dp), dimension(nb,nb,n_pts), intent(in)    :: a
    real(dp), dimension(nb,nb,n_pts), intent(inout) :: b,c

    integer :: i, il, iu
    integer :: row, col
    integer :: il1, ir

  continue

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

    il = 1
    iu = n_pts

! Inversion of block tridiagonal
! Solution is by upper triangularization
! Block inversions use nonpivoted lu decomposition
! b and c are overloaded

    il1 = il + 1
    i   = il

! LU decomposition

    b(1,1,i) = 1.0_dp/b(1,1,i)
    b(1,2,i) = b(1,1,i)*b(1,2,i)
    b(1,3,i) = b(1,1,i)*b(1,3,i)
    b(1,4,i) = b(1,1,i)*b(1,4,i)
    b(1,5,i) = b(1,1,i)*b(1,5,i)
    b(1,6,i) = b(1,1,i)*b(1,6,i)
    b(2,2,i) = 1.0_dp/(b(2,2,i)                                  &
                         -b(2,1,i)*b(1,2,i))
    b(2,3,i) = b(2,2,i)*(b(2,3,i)                                &
                         -b(2,1,i)* b(1,3,i))
    b(2,4,i) = b(2,2,i)*(b(2,4,i)                                &
                         -b(2,1,i)* b(1,4,i))
    b(2,5,i) = b(2,2,i)*(b(2,5,i)                                &
                         -b(2,1,i)* b(1,5,i))
    b(2,6,i) = b(2,2,i)*(b(2,6,i)                                &
                         -b(2,1,i)* b(1,6,i))
    b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
    b(3,3,i) = 1.0_dp/(b(3,3,i)                                  &
                         -b(3,1,i)*b(1,3,i)                      &
                         -b(3,2,i)*b(2,3,i))
    b(3,4,i) = b(3,3,i)*(b(3,4,i)                                &
                         -b(3,1,i)* b(1,4,i)                     &
                         -b(3,2,i)* b(2,4,i))
    b(3,5,i) = b(3,3,i)*(b(3,5,i)                                &
                         -b(3,1,i)*b(1,5,i)                      &
                         -b(3,2,i)*b(2,5,i))
    b(3,6,i) = b(3,3,i)*(b(3,6,i)                                &
                         -b(3,1,i)*b(1,6,i)                      &
                         -b(3,2,i)*b(2,6,i))

    b(4,2,i) = b(4,2,i)-b(4,1,i)*b(1,2,i)
    b(4,3,i) = b(4,3,i)-b(4,1,i)*b(1,3,i)                        &
                         -b(4,2,i)*b(2,3,i)
    b(4,4,i) = 1.0_dp/(b(4,4,i)                                  &
                         -b(4,1,i)*b(1,4,i)                      &
                         -b(4,2,i)*b(2,4,i)                      &
                         -b(4,3,i)*b(3,4,i))
    b(4,5,i) = b(4,4,i)*(b(4,5,i)                                &
                         -b(4,1,i)*b(1,5,i)                      &
                         -b(4,2,i)*b(2,5,i)                      &
                         -b(4,3,i)*b(3,5,i))
    b(4,6,i) = b(4,4,i)*(b(4,6,i)                                &
                         -b(4,1,i)*b(1,6,i)                      &
                         -b(4,2,i)*b(2,6,i)                      &
                         -b(4,3,i)*b(3,6,i))
    b(5,2,i) = b(5,2,i)-b(5,1,i)*b(1,2,i)
    b(5,3,i) = b(5,3,i)-b(5,1,i)*b(1,3,i)                        &
                         -b(5,2,i)*b(2,3,i)
    b(5,4,i) = b(5,4,i)-b(5,1,i)*b(1,4,i)                        &
                         -b(5,2,i)*b(2,4,i)                      &
                         -b(5,3,i)*b(3,4,i)
    b(5,5,i) = 1.0_dp/(b(5,5,i)                                  &
                         -b(5,1,i)*b(1,5,i)                      &
                         -b(5,2,i)*b(2,5,i)                      &
                         -b(5,3,i)*b(3,5,i)                      &
                         -b(5,4,i)*b(4,5,i))
    b(5,6,i) = b(5,5,i)*(b(5,6,i)                                &
                         -b(5,1,i)*b(1,6,i)                      &
                         -b(5,2,i)*b(2,6,i)                      &
                         -b(5,3,i)*b(3,6,i)                      &
                         -b(5,4,i)*b(4,6,i))

    b(6,2,i) = b(6,2,i)-b(6,1,i)*b(1,2,i)
    b(6,3,i) = b(6,3,i)-b(6,1,i)*b(1,3,i)                        &
                         -b(6,2,i)*b(2,3,i)
    b(6,4,i) = b(6,4,i)-b(6,1,i)*b(1,4,i)                        &
                         -b(6,2,i)*b(2,4,i)                      &
                         -b(6,3,i)*b(3,4,i)
    b(6,5,i) = b(6,5,i)-b(6,1,i)*b(1,5,i)                        &
                         -b(6,2,i)*b(2,5,i)                      &
                         -b(6,3,i)*b(3,5,i)                      &
                         -b(6,4,i)*b(4,5,i)
    b(6,6,i) = 1.0_dp/(b(6,6,i)                                  &
                         -b(6,1,i)*b(1,6,i)                      &
                         -b(6,2,i)*b(2,6,i)                      &
                         -b(6,3,i)*b(3,6,i)                      &
                         -b(6,4,i)*b(4,6,i)                      &
                         -b(6,5,i)*b(5,6,i))

    if (i /= iu) then

! c = ainv*c

      do col = 1, 6
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))
        c(4,col,i) = b(4,4,i)*(c(4,col,i)                        &
                             - b(4,1,i)*c(1,col,i)               &
                             - b(4,2,i)*c(2,col,i)               &
                             - b(4,3,i)*c(3,col,i))
        c(5,col,i) = b(5,5,i)*(c(5,col,i)                        &
                             - b(5,1,i)*c(1,col,i)               &
                             - b(5,2,i)*c(2,col,i)               &
                             - b(5,3,i)*c(3,col,i)               &
                             - b(5,4,i)*c(4,col,i))
        c(6,col,i) = b(6,6,i)*(c(6,col,i)                        &
                             - b(6,1,i)*c(1,col,i)               &
                             - b(6,2,i)*c(2,col,i)               &
                             - b(6,3,i)*c(3,col,i)               &
                             - b(6,4,i)*c(4,col,i)               &
                             - b(6,5,i)*c(5,col,i))

        c(5,col,i) = c(5,col,i)                                  &
                             - b(5,6,i)*c(6,col,i)
        c(4,col,i) = c(4,col,i)                                  &
                             - b(4,6,i)*c(6,col,i)               &
                             - b(4,5,i)*c(5,col,i)
        c(3,col,i) = c(3,col,i)                                  &
                             - b(3,6,i)*c(6,col,i)               &
                             - b(3,5,i)*c(5,col,i)               &
                             - b(3,4,i)*c(4,col,i)
        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,6,i)*c(6,col,i)               &
                             - b(2,5,i)*c(5,col,i)               &
                             - b(2,4,i)*c(4,col,i)               &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,6,i)*c(6,col,i)               &
                             - b(1,5,i)*c(5,col,i)               &
                             - b(1,4,i)*c(4,col,i)               &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end if

! Forward sweep

    forward_sweep : do i = il1,iu
      ir = i - 1

! First row reduction

      do row = 1, 6
        do col = 1, 6
            b(row,col,i) = b(row,col,i)                           &
                                 - a(row,1,i)*c(1,col,+ir)        &
                                 - a(row,2,i)*c(2,col,+ir)        &
                                 - a(row,3,i)*c(3,col,+ir)        &
                                 - a(row,4,i)*c(4,col,+ir)        &
                                 - a(row,5,i)*c(5,col,+ir)        &
                                 - a(row,6,i)*c(6,col,+ir)
        end do
      end do

! LU decomposition

      b(1,1,i) = 1.0_dp/b(1,1,i)
      b(1,2,i) = b(1,1,i)*b(1,2,i)
      b(1,3,i) = b(1,1,i)*b(1,3,i)
      b(1,4,i) = b(1,1,i)*b(1,4,i)
      b(1,5,i) = b(1,1,i)*b(1,5,i)
      b(1,6,i) = b(1,1,i)*b(1,6,i)
      b(2,2,i) = 1.0_dp/(b(2,2,i)                                &
                           -b(2,1,i)*b(1,2,i))
      b(2,3,i) = b(2,2,i)*(b(2,3,i)                              &
                           -b(2,1,i)* b(1,3,i))
      b(2,4,i) = b(2,2,i)*(b(2,4,i)                              &
                           -b(2,1,i)* b(1,4,i))
      b(2,5,i) = b(2,2,i)*(b(2,5,i)                              &
                           -b(2,1,i)* b(1,5,i))
      b(2,6,i) = b(2,2,i)*(b(2,6,i)                              &
                           -b(2,1,i)* b(1,6,i))
      b(3,2,i) = b(3,2,i)-b(3,1,i)*b(1,2,i)
      b(3,3,i) = 1.0_dp/(b(3,3,i)                                &
                           -b(3,1,i)*b(1,3,i)                    &
                           -b(3,2,i)*b(2,3,i))
      b(3,4,i) = b(3,3,i)*(b(3,4,i)                              &
                           -b(3,1,i)* b(1,4,i)                   &
                           -b(3,2,i)* b(2,4,i))
      b(3,5,i) = b(3,3,i)*(b(3,5,i)                              &
                           -b(3,1,i)*b(1,5,i)                    &
                           -b(3,2,i)*b(2,5,i))
      b(3,6,i) = b(3,3,i)*(b(3,6,i)                              &
                           -b(3,1,i)*b(1,6,i)                    &
                           -b(3,2,i)*b(2,6,i))

      b(4,2,i) = b(4,2,i)-b(4,1,i)*b(1,2,i)
      b(4,3,i) = b(4,3,i)-b(4,1,i)*b(1,3,i)                      &
                           -b(4,2,i)*b(2,3,i)
      b(4,4,i) = 1.0_dp/(b(4,4,i)                                &
                           -b(4,1,i)*b(1,4,i)                    &
                           -b(4,2,i)*b(2,4,i)                    &
                           -b(4,3,i)*b(3,4,i))
      b(4,5,i) = b(4,4,i)*(b(4,5,i)                              &
                           -b(4,1,i)*b(1,5,i)                    &
                           -b(4,2,i)*b(2,5,i)                    &
                           -b(4,3,i)*b(3,5,i))
      b(4,6,i) = b(4,4,i)*(b(4,6,i)                              &
                           -b(4,1,i)*b(1,6,i)                    &
                           -b(4,2,i)*b(2,6,i)                    &
                           -b(4,3,i)*b(3,6,i))
      b(5,2,i) = b(5,2,i)-b(5,1,i)*b(1,2,i)
      b(5,3,i) = b(5,3,i)-b(5,1,i)*b(1,3,i)                      &
                           -b(5,2,i)*b(2,3,i)
      b(5,4,i) = b(5,4,i)-b(5,1,i)*b(1,4,i)                      &
                           -b(5,2,i)*b(2,4,i)                    &
                           -b(5,3,i)*b(3,4,i)
      b(5,5,i) = 1.0_dp/(b(5,5,i)                                &
                           -b(5,1,i)*b(1,5,i)                    &
                           -b(5,2,i)*b(2,5,i)                    &
                           -b(5,3,i)*b(3,5,i)                    &
                           -b(5,4,i)*b(4,5,i))
      b(5,6,i) = b(5,5,i)*(b(5,6,i)                              &
                           -b(5,1,i)*b(1,6,i)                    &
                           -b(5,2,i)*b(2,6,i)                    &
                           -b(5,3,i)*b(3,6,i)                    &
                           -b(5,4,i)*b(4,6,i))

      b(6,2,i) = b(6,2,i)-b(6,1,i)*b(1,2,i)
      b(6,3,i) = b(6,3,i)-b(6,1,i)*b(1,3,i)                      &
                           -b(6,2,i)*b(2,3,i)
      b(6,4,i) = b(6,4,i)-b(6,1,i)*b(1,4,i)                      &
                           -b(6,2,i)*b(2,4,i)                    &
                           -b(6,3,i)*b(3,4,i)
      b(6,5,i) = b(6,5,i)-b(6,1,i)*b(1,5,i)                      &
                           -b(6,2,i)*b(2,5,i)                    &
                           -b(6,3,i)*b(3,5,i)                    &
                           -b(6,4,i)*b(4,5,i)
      b(6,6,i) = 1.0_dp/(b(6,6,i)                                &
                           -b(6,1,i)*b(1,6,i)                    &
                           -b(6,2,i)*b(2,6,i)                    &
                           -b(6,3,i)*b(3,6,i)                    &
                           -b(6,4,i)*b(4,6,i)                    &
                           -b(6,5,i)*b(5,6,i))

      if (i == iu) cycle forward_sweep

! c = ainv*c

      do col = 1, 6
        c(1,col,i) = b(1,1,i)*(c(1,col,i))
        c(2,col,i) = b(2,2,i)*(c(2,col,i)                        &
                             - b(2,1,i)*c(1,col,i))
        c(3,col,i) = b(3,3,i)*(c(3,col,i)                        &
                             - b(3,1,i)*c(1,col,i)               &
                             - b(3,2,i)*c(2,col,i))
        c(4,col,i) = b(4,4,i)*(c(4,col,i)                        &
                             - b(4,1,i)*c(1,col,i)               &
                             - b(4,2,i)*c(2,col,i)               &
                             - b(4,3,i)*c(3,col,i))
        c(5,col,i) = b(5,5,i)*(c(5,col,i)                        &
                             - b(5,1,i)*c(1,col,i)               &
                             - b(5,2,i)*c(2,col,i)               &
                             - b(5,3,i)*c(3,col,i)               &
                             - b(5,4,i)*c(4,col,i))
        c(6,col,i) = b(6,6,i)*(c(6,col,i)                        &
                             - b(6,1,i)*c(1,col,i)               &
                             - b(6,2,i)*c(2,col,i)               &
                             - b(6,3,i)*c(3,col,i)               &
                             - b(6,4,i)*c(4,col,i)               &
                             - b(6,5,i)*c(5,col,i))

        c(5,col,i) = c(5,col,i)                                  &
                             - b(5,6,i)*c(6,col,i)
        c(4,col,i) = c(4,col,i)                                  &
                             - b(4,6,i)*c(6,col,i)               &
                             - b(4,5,i)*c(5,col,i)
        c(3,col,i) = c(3,col,i)                                  &
                             - b(3,6,i)*c(6,col,i)               &
                             - b(3,5,i)*c(5,col,i)               &
                             - b(3,4,i)*c(4,col,i)
        c(2,col,i) = c(2,col,i)                                  &
                             - b(2,6,i)*c(6,col,i)               &
                             - b(2,5,i)*c(5,col,i)               &
                             - b(2,4,i)*c(4,col,i)               &
                             - b(2,3,i)*c(3,col,i)
        c(1,col,i) = c(1,col,i)                                  &
                             - b(1,6,i)*c(6,col,i)               &
                             - b(1,5,i)*c(5,col,i)               &
                             - b(1,4,i)*c(4,col,i)               &
                             - b(1,3,i)*c(3,col,i)               &
                             - b(1,2,i)*c(2,col,i)
      end do

    end do forward_sweep

  end subroutine decompose_tridiag_6

end module line_lu_ddq

module inviscid_flux

  use kinddefs,          only : dp

  implicit none

  private

  public :: entropy_fix
  public :: flux_limiter, flux_limiter_teq
  public :: flux_construction, flux_construction_lhs
  public :: freeze_limiter_iteration
  public :: first_order_iterations, iflim, iflim_teq
  public :: first_order_coarse_fmg, first_order_coarse_fas
  public :: first_order_inviscid
  public :: rhs_u_eigenvalue_coef, lhs_u_eigenvalue_coef
  public :: rhs_a_eigenvalue_coef, lhs_a_eigenvalue_coef
  public :: re_min_vswch, re_max_vswch
  public :: pole_gradient
  public :: invoke_mapped_lines, average_off_wall, tecplot_mapped_lines
  public :: x_line, y_line, z_line
  public :: gen_use_perf_jac
  public :: gen_turb_advectn_ho
  public :: lin_press_roe
  public :: mass_flux
  public :: mass_flux_bc
  public :: mean_decouple
  public :: dc_part
  public :: update_mass_frac_iter

  logical :: entropy_fix
  logical :: pole_gradient ! use limiting form of continuity equation across
                           ! pole in association with symmetry_1_strong,
                           ! symmetry_2_strong, or symmetry_3_strong

  character(40) :: flux_limiter, flux_limiter_teq
  character(40) :: flux_construction, flux_construction_lhs

  integer  :: first_order_iterations, iflim, iflim_teq
  integer  :: freeze_limiter_iteration = -1

  real(dp) :: rhs_u_eigenvalue_coef, lhs_u_eigenvalue_coef
  real(dp) :: rhs_a_eigenvalue_coef, lhs_a_eigenvalue_coef
  real(dp) :: re_min_vswch, re_max_vswch

  logical :: invoke_mapped_lines = .false.
  logical :: average_off_wall = .true.
  logical :: tecplot_mapped_lines = .false.

  real(dp), dimension(3) :: x_line = huge(1._dp)
  real(dp), dimension(3) :: y_line = huge(1._dp)
  real(dp), dimension(3) :: z_line = huge(1._dp)

  !for decoupled scheme in generic gas path
  real(dp), dimension(:),     pointer :: mass_flux
  real(dp), dimension(:,:),   pointer :: mass_flux_bc

  logical :: first_order_coarse_fmg = .false.
  logical :: first_order_coarse_fas = .false.
  logical :: first_order_inviscid   = .false.
  logical :: gen_use_perf_jac       = .false.
  logical :: gen_turb_advectn_ho    = .false.
  logical :: lin_press_roe          = .false.
  logical :: mean_decouple          = .false.
  logical :: dc_part                = .false.

  integer :: update_mass_frac_iter  = 1

end module inviscid_flux

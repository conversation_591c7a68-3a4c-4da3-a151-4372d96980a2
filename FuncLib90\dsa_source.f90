!================================= DSA_SOURCE ================================80
!
! This routine computes the Jacobians wrt turbulence of S-A source terms.
!
!=============================================================================80

  pure function dsa_source( xmre, rnu, distance, turb, vgradx, vgrady, vgradz )

    use turb_parameters, only : t_prod, t_dest
    use fun3d_constants, only : my_6th
    use turb_sa_const,   only : cv1, vkar, cw2, cw3, ct3, ct4, cb1, cw1,       &
                                turb_eps, dacles_mariani

    real(dp), intent(in) :: xmre, rnu, distance, turb

    real(dp), dimension(3), intent(in) :: vgradx, vgrady, vgradz

    real(dp) :: dsa_source

    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz, sij_mag
    real(dp) :: chi, gg, rr, s, sw, bot
    real(dp) :: ft2, fv1, fv2, fw, vkar2
    real(dp) :: turb_plus, turb_abs

    real(dp) :: xmre_d2, dturb_plus, dturb_abs, xmre_bot
    real(dp) :: dchi, dfv1, dfv2, drr, dft2, dfw, dgg, dsw
    real(dp) :: dprd, ddest, dterm1, dterm2, gg6
    real(dp) :: sw_limited, dsw_limited, arg_limited

    real(dp), parameter :: my_00001 = 0.00001_dp
    real(dp), parameter :: my_76th  =     7.0_dp/6.0_dp

  continue

    vkar2 = vkar * vkar
    bot   = vkar2 * distance * distance

    xmre_bot = xmre/bot
    xmre_d2  = xmre/( distance**2 )

    ux = vgradx(1)
    uy = vgrady(1)
    uz = vgradz(1)
    vx = vgradx(2)
    vy = vgrady(2)
    vz = vgradz(2)
    wx = vgradx(3)
    wy = vgrady(3)
    wz = vgradz(3)

    s  = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2 )
    if ( s <= 1.0e-8_dp ) s = 1.0e-8_dp

    if ( dacles_mariani ) then
      sij_mag   = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                        +2._dp*(ux**2+vy**2+wz**2))
      s = s + 2._dp*min(-1.0e-8_dp,sij_mag-s)
    end if

    chi = turb / rnu

    dchi = 1.0_dp/rnu
    if ( chi < -5.0_dp ) dchi = 0._dp

    arg_limited = -ct4*chi*chi
    if ( arg_limited >= -700._dp ) then
      ft2  = ct3 * exp(arg_limited)
      dft2 = ft2 *(-ct4*2._dp*chi*dchi)
    else
      ft2  = ct3 * exp(-700._dp)
      dft2 = 0._dp
    endif

    fv1 = chi**3 / ( chi**3 + cv1**3 )
    fv2 = 1._dp - chi/( 1._dp + chi*fv1 )

    dfv1 = ((chi**3 + cv1**3)*3._dp*chi**2*dchi  -                  &
           chi**3*3._dp*chi**2*dchi ) / (chi**3 + cv1**3) ** 2
    dfv2 = - ((1._dp + chi*fv1)*dchi  - chi*(chi*dfv1  +            &
            fv1*dchi )) / (1._dp + chi*fv1)**2

!   Form modified turbulence terms

    turb_abs   =  aharten( turb, turb_eps )
    dturb_abs  = daharten( turb, turb_eps )
    turb_plus  = 0.5_dp*( turb  +  turb_abs )
    dturb_plus = 0.5_dp*( 1._dp + dturb_abs )

    sw  = s + xmre_bot*turb_abs*fv2
    dsw = xmre_bot*( fv2*dturb_abs + turb_abs*dfv2 )

    !..sw_limited is used because of roundoff in the rr term.
    !..Originally FUN3D used the limited term in f_prod but this
    !  is not needed...truncation error studies...Lisbon workshop Oct 2008.
    sw_limited  = sw
    dsw_limited = dsw
    if ( sw < my_00001 ) then
      sw_limited  = my_00001
      dsw_limited = 0._dp
    endif

    rr  = xmre_bot * turb / sw_limited
    drr = xmre_bot *( 1._dp - turb*dsw_limited/sw_limited )/sw_limited
    if ( rr > 10.0_dp ) then
      rr  = 10.0_dp
      drr = 0._dp
    endif

    gg  = rr + cw2*(rr**6-rr)
    dgg = drr + cw2*( 6.0_dp*rr**5-1._dp)*drr

    dterm1 = (1._dp+cw3**6)**(my_6th)
    gg6    = gg**6
    fw     = dterm1 * gg * (gg6+cw3**6)**(-my_6th)

    dfw    = dterm1 * dgg * ( (gg6+cw3**6)**(-my_6th)        &
                            - (gg6+cw3**6)**(-my_76th)*gg6 )

!   prd = cb1*( 1._dp - ft2 )*sw*turb_abs

    dterm1 = cb1*( 1._dp - ft2 )
    dterm2 = cb1*sw*turb_abs

    dprd  = dterm1*( sw*dturb_abs + dsw*turb_abs ) &
          - dterm2*dft2

!   dest    = xmre_d2 * ( cw1*fw - cb1*ft2/vkar2 )*( turb_plus )**2

    dterm1  = xmre_d2 * ( cw1*fw - cb1*ft2/vkar2 )
    dterm2  = xmre_d2 * ( turb_plus )**2

    ddest   = dterm1 * 2._dp*turb_plus*dturb_plus &
            + dterm2 * ( cw1*dfw - cb1*dft2/vkar2 )

!   Source term : production - destruction

    dsa_source   = t_prod*dprd - t_dest*ddest

  end function dsa_source


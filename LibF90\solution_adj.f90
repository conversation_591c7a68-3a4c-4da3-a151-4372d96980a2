module solution_adj

  use kinddefs, only : dp, dqp

  implicit none

  private

  public :: sadj_type
  public :: nullify_sadj
  public :: deallocate_sadj
  public :: set_up_dual_solution
  public :: rresta, wresta

  type sadj_type
    real(dp), pointer, dimension(:,:)   :: coltag   ! Zero out columns
    real(dp), pointer, dimension(:,:,:) :: rlam     ! Costate Variable
    real(dp), pointer, dimension(:,:,:) :: rlamatn  ! Costate Variable at n
    real(dp), pointer, dimension(:,:,:) :: rlamatn1 ! Costate Variable at n-1
    real(dp), pointer, dimension(:,:,:) :: rlamatn2 ! Costate Variable at n-2
    real(dp), pointer, dimension(:,:,:) :: res      ! Residual
    real(dqp),pointer, dimension(:,:,:) :: dq       ! Update
    real(dp), pointer, dimension(:,:,:) :: aa       ! LHS
  end type sadj_type

contains

!============================= NULLIFY_SADJ ==================================80
!
! Nullifies all the pointers in sadj to make sure that their state is
! disassociated and not undefined so it is safe to use the associated intrinsic
!
!=============================================================================80

  subroutine nullify_sadj( sadj )

    type(sadj_type), intent(inout) :: sadj

  continue

    nullify(sadj%coltag)
    nullify(sadj%rlam)
    nullify(sadj%rlamatn)
    nullify(sadj%rlamatn1)
    nullify(sadj%rlamatn2)
    nullify(sadj%res)
    nullify(sadj%dq)
    nullify(sadj%aa)

  end subroutine nullify_sadj

!=============================== DEALLOCATE_SADJ =============================80
!
! Deallocates memory for ALL layers of the sadj derived type
!
!=============================================================================80

  subroutine deallocate_sadj( sadj )

    type(sadj_type), intent(inout) :: sadj

  continue

    if (associated(sadj%coltag))    deallocate(sadj%coltag)
    if (associated(sadj%rlam))      deallocate(sadj%rlam)
    if (associated(sadj%rlamatn))   deallocate(sadj%rlamatn)
    if (associated(sadj%rlamatn1))  deallocate(sadj%rlamatn1)
    if (associated(sadj%rlamatn2))  deallocate(sadj%rlamatn2)
    if (associated(sadj%res))       deallocate(sadj%res)
    if (associated(sadj%dq))        deallocate(sadj%dq)
    if (associated(sadj%aa))        deallocate(sadj%aa)

  end subroutine deallocate_sadj
!============================= SET_UP_NEQ ====================================80
!
! Set some integers associated with unknowns to be solved.
! Cloned from PHYSICS_DEPS/solution.f90 to avoid make dependency issue.
!
!=============================================================================80

  subroutine set_up_neq( grid, soln )

    use info_depr,      only : cc_primal, twod
    use lmpi,           only : lmpi_reduce, lmpi_bcast
    use grid_types,     only : grid_type
    use solution_types, only : soln_type

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    continue

    if ( cc_primal ) then
      soln%neq0 =  grid%ncell0
      soln%neq01=  grid%ncell01
    else
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
    endif

    soln%dof0 = soln%neq0
    if ( twod .and. grid%origin < 3 ) soln%dof0 = soln%dof0/2

    call lmpi_reduce(soln%dof0, soln%dofg)
    call lmpi_bcast(soln%dofg)

  end subroutine set_up_neq

!========================== SET_UP_DUAL_SOLUTION =============================80
!
!  Sets up the solution derived types for the exact dual adjoint solver
!
!=============================================================================80

  subroutine set_up_dual_solution( grid, soln, sadj, design )

    use info_depr,            only : tightly_couple, partial_pivoting, ncyc,   &
                                     mixed
    use nml_nonlinear_solves, only : itime
    use generic_gas_map,      only : n_energy, n_species, n_mom
    use grid_types,           only : grid_type
    use allocations,          only : my_alloc_ptr
    use solution_types,       only : soln_type, nullify_global_bndry_data
    use design_types,         only : design_type
    use force_types,          only : nullify_force, zero_force
    use force_helper,         only : remove_bcforce_from_total
    use solution_types,       only : generic_gas
    use bc_types,             only : allocate_bcsoln

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design

    integer :: ib

    continue

    call my_alloc_ptr(soln%q_dof,       soln%n_tot,         grid%nnodes01)
    call my_alloc_ptr(soln%q_dof_res0,      1,             1)
    call my_alloc_ptr(soln%omega,       1)

    allocate(soln%bcsoln(grid%nbound))
    do ib = 1,grid%nbound
      call allocate_bcsoln(grid%bc(ib)%nbnode,soln%bcsoln(ib))
    end do
    if ( soln%eqn_set == generic_gas )then
     call my_alloc_ptr(soln%enthalpy_ij, n_species, n_energy, grid%nnodes01)
     call my_alloc_ptr(soln%pressure_jac,n_species+n_energy,n_mom,grid%nnodes01)
    else
     call my_alloc_ptr(soln%enthalpy_ij,  1,          1,        1)
     call my_alloc_ptr(soln%pressure_jac, 1,                    1,    1)
    end if

    call my_alloc_ptr(soln%forcing, 1, 1)

    call my_alloc_ptr(soln%gradx,       soln%n_grd,         grid%nnodes01)
    call my_alloc_ptr(soln%grady,       soln%n_grd,         grid%nnodes01)
    call my_alloc_ptr(soln%gradz,       soln%n_grd,         grid%nnodes01)
    call my_alloc_ptr(soln%cdt,                        grid%nnodes0)
    call my_alloc_ptr(soln%phi,         soln%n_grd,         grid%nnodes01)
    call my_alloc_ptr(soln%a_diag,      soln%njac,          soln%njac,         &
                                        grid%nnodes0)
    call my_alloc_ptr(soln%a_diag_lu,   soln%njac,          soln%njac,         &
                                        grid%nnodes0)
    if(partial_pivoting) then
      call my_alloc_ptr(soln%pivot_lu, soln%njac, grid%nnodes0)
    else
      call my_alloc_ptr(soln%pivot_lu, 1, 1)
    endif
    call my_alloc_ptr(soln%dtau,                grid%nnodes0)
    call my_alloc_ptr(soln%dtau_term, soln%n_q, grid%nnodes0)
    soln%dtau_term (:,:)= .true.

    call my_alloc_ptr(soln%diag_has_been_decomposed,   grid%nnodes0)

    if ( tightly_couple ) then
      call my_alloc_ptr(soln%a_turb_diag,    1, 1, 1)
      call my_alloc_ptr(soln%a_turb_diag_lu, 1, 1, 1)
      call my_alloc_ptr(soln%turb_pivot_lu,  1, 1)
    else
      call my_alloc_ptr(soln%a_turb_diag,    max(soln%n_turb,1), &
                                             max(soln%n_turb,1), &
                                             grid%nnodes0)
      call my_alloc_ptr(soln%a_turb_diag_lu, max(soln%n_turb,1), &
                                             max(soln%n_turb,1), &
                                             grid%nnodes0)
      if(partial_pivoting) then
        call my_alloc_ptr(soln%turb_pivot_lu, max(soln%n_turb,1), grid%nnodes0)
      else
        call my_alloc_ptr(soln%turb_pivot_lu, 1, 1)
      endif
    endif

    call my_alloc_ptr(soln%b,           soln%adim,          grid%nnodes0)
    call my_alloc_ptr(soln%dq,          soln%adim,          grid%nnodes01)
    call my_alloc_ptr(soln%res,       1,             1)
    call my_alloc_ptr(soln%ff,          1,             1)
    call my_alloc_ptr(soln%turbff,      1,             1)

    if ( tightly_couple ) then
      call my_alloc_ptr(soln%turbres,   1,             1)
    else
      call my_alloc_ptr(soln%turbres,   max(soln%n_turb,1), grid%nnodes0)
    endif

    if ( mixed ) then
      call my_alloc_ptr(soln%dft1, 1)
      call my_alloc_ptr(soln%dft2, 1)
    else
      call my_alloc_ptr(soln%dft1, grid%nedge)
      call my_alloc_ptr(soln%dft2, grid%nedge)
    endif

    call my_alloc_ptr(soln%flux_efixc,                 grid%nedge)
    call my_alloc_ptr(soln%turb,        max(soln%n_turb,1), grid%nnodes01)
    call my_alloc_ptr(soln%amut,                       grid%nnodes01)

    if ( soln%n_turb > 1 ) then
      call my_alloc_ptr(soln%sst_f1, grid%nnodes01)
      call my_alloc_ptr(soln%sst_f2, grid%nnodes01)
      call my_alloc_ptr(soln%crossD, grid%nnodes0)
    else
      call my_alloc_ptr(soln%sst_f1,      1)
      call my_alloc_ptr(soln%sst_f2,      1)
      call my_alloc_ptr(soln%crossD,      1)
    end if
    if ( soln%n_turb > 0 ) then
      call my_alloc_ptr(soln%wall_function_node, grid%nnodes01)
      call my_alloc_ptr(soln%rhotauij, 6,  grid%nnodes01)
    else
      call my_alloc_ptr(soln%wall_function_node, 1)
      call my_alloc_ptr(soln%rhotauij, 6, 1)
    end if

    allocate(soln%bcforce(grid%nbound))
    call remove_bcforce_from_total(grid%nbound,grid%bc,soln%bcforce,'../Flow/')
    do ib = 1,grid%nbound
      call my_alloc_ptr(soln%bcforce(ib)%cp_t,  max(1,grid%bc(ib)%nbfacet))
      call my_alloc_ptr(soln%bcforce(ib)%cq_t,  max(1,grid%bc(ib)%nbfacet))
      call my_alloc_ptr(soln%bcforce(ib)%cfx_t, max(1,grid%bc(ib)%nbfacet))
      call my_alloc_ptr(soln%bcforce(ib)%cfy_t, max(1,grid%bc(ib)%nbfacet))
      call my_alloc_ptr(soln%bcforce(ib)%cfz_t, max(1,grid%bc(ib)%nbfacet))
      call my_alloc_ptr(soln%bcforce(ib)%cp_q,  max(1,grid%bc(ib)%nbfaceq))
      call my_alloc_ptr(soln%bcforce(ib)%cfx_q, max(1,grid%bc(ib)%nbfaceq))
      call my_alloc_ptr(soln%bcforce(ib)%cfy_q, max(1,grid%bc(ib)%nbfaceq))
      call my_alloc_ptr(soln%bcforce(ib)%cfz_q, max(1,grid%bc(ib)%nbfaceq))
      call my_alloc_ptr(soln%bcforce(ib)%cq_q,  max(1,grid%bc(ib)%nbfaceq))
    end do

    allocate(soln%totforce(1))
    call nullify_force(soln%totforce(1))
    call zero_force(soln%totforce(1))

    call my_alloc_ptr(soln%rmshist,     soln%adim,max(ncyc,1),design%nfunctions)
    call my_alloc_ptr(soln%rmaxhist,    soln%adim,max(ncyc,1),design%nfunctions)
    call my_alloc_ptr(soln%xlochist,    soln%adim,max(ncyc,1),design%nfunctions)
    call my_alloc_ptr(soln%ylochist,    soln%adim,max(ncyc,1),design%nfunctions)
    call my_alloc_ptr(soln%zlochist,    soln%adim,max(ncyc,1),design%nfunctions)
    call my_alloc_ptr(soln%walltime,    max(ncyc,1))

    allocate(soln%global_bndry_data(grid%nbound))
    do ib = 1,grid%nbound
      call nullify_global_bndry_data(soln%global_bndry_data(ib))
    end do

    call my_alloc_ptr(sadj%coltag,      soln%adim, grid%nnodes01)
    call my_alloc_ptr(sadj%rlam,        soln%adim, grid%nnodes01,              &
                                        design%nfunctions)
    call my_alloc_ptr(sadj%res,         soln%adim, grid%nnodes01,              &
                                        design%nfunctions)
    call my_alloc_ptr(sadj%dq,          soln%adim, grid%nnodes01,              &
                                        design%nfunctions)

    if ( itime == 0 ) then
      call my_alloc_ptr(sadj%rlamatn, 1,1,1)
      call my_alloc_ptr(sadj%rlamatn1,1,1,1)
      call my_alloc_ptr(sadj%rlamatn2,1,1,1)
      call my_alloc_ptr(soln%simtime,1)
      call my_alloc_ptr(soln%qatn,        1,             1)
      call my_alloc_ptr(soln%qatn1,       1,             1)
      call my_alloc_ptr(soln%qatn2,       1,             1)
      call my_alloc_ptr(soln%qatn3,       1,             1)
      call my_alloc_ptr(soln%qatn4,       1,             1)
      call my_alloc_ptr(soln%qatp1,       1,             1)
      call my_alloc_ptr(soln%qatp2,       1,             1)
      call my_alloc_ptr(soln%turbatn,     1,             1)
      call my_alloc_ptr(soln%turbatn1,    1,             1)
      call my_alloc_ptr(soln%turbatn2,    1,             1)
      call my_alloc_ptr(soln%turbatn3,    1,             1)
      call my_alloc_ptr(soln%turbatn4,    1,             1)
      call my_alloc_ptr(soln%turbatp1,    1,             1)
      call my_alloc_ptr(soln%turbatp2,    1,             1)
      call my_alloc_ptr(soln%amutatn1,                   1)
      call my_alloc_ptr(soln%amutatp1,                   1)
      call my_alloc_ptr(soln%amutatp2,                   1)
    else
      call my_alloc_ptr(sadj%rlamatn,     soln%adim, grid%nnodes01,            &
                                          design%nfunctions)
      call my_alloc_ptr(sadj%rlamatn1,    soln%adim, grid%nnodes01,            &
                                          design%nfunctions)
      call my_alloc_ptr(sadj%rlamatn2,    soln%adim, grid%nnodes01,            &
                                          design%nfunctions)
      call my_alloc_ptr(soln%simtime,max(ncyc,1))
      call my_alloc_ptr(soln%qatn,        1,             1)
      call my_alloc_ptr(soln%qatn1,       soln%n_tot,    grid%nnodes01)
      call my_alloc_ptr(soln%qatn2,       soln%n_tot,    grid%nnodes01)
      call my_alloc_ptr(soln%qatn3,       1,             1)
      call my_alloc_ptr(soln%qatn4,       1,             1)
      call my_alloc_ptr(soln%qatp1,       soln%n_tot,    grid%nnodes01)
      call my_alloc_ptr(soln%qatp2,       soln%n_tot,    grid%nnodes01)
! We don't really need these turbatn variables, but since we call
! the turbulent residual, it looks to use them
      call my_alloc_ptr(soln%turbatn,     soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%turbatn1,    soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%turbatn2,    soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%turbatn3,    soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%turbatn4,    soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%turbatp1,    soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%turbatp2,    soln%n_turb,   grid%nnodes01)
      call my_alloc_ptr(soln%amutatn1,                   grid%nnodes01)
      call my_alloc_ptr(soln%amutatn2,                   grid%nnodes01)
      call my_alloc_ptr(soln%amutatp1,                   grid%nnodes01)
      call my_alloc_ptr(soln%amutatp2,                   grid%nnodes01)
    endif

    !...neq normally set immediately after readme, so this is a redundant
    !...call in most cases (1 grid shared by 2 solns is the exception).
    call set_up_neq( grid, soln )

  end subroutine set_up_dual_solution

!================================ RRESTA =====================================80
!
! Reads adjoint lamda variables for restarts
!
! Reads Version 4 lamda variables for restarts
!
! MPIIO File Format
!   sentinal  version    date time      (R8, R8, I4, I4)
!   nproc,    nnodesg,   soln%eqn_set   (I4, I4, I4)
!   ivisc     soln%ndim, soln%n_turb    (I4, I4, I4)
!   adim,     nfunctions                (I4, I4)
!
!   xmach,    re,        alpha          (R8, R8, R8)
!   yaw,      tref,      beta           (R8, R8, R8)
!   prandtl, simulation_time            (R8, R8)
!
!   nnodes0                             (I4*nproc)
!
!   lg2                                 (I4*nnodesg)
!
!   sentinal2                           (R8)
!   <adjoint arrays>                    (R8*dim1(varys),dim2(always neq0))
!   sentinal                            (R8)
!
!=============================================================================80

  subroutine rresta(grid,design,soln,sadj,directory,set_info_allocate)

    use kinddefs,          only : dp
    use lmpi,              only : lmpi_id, lmpi_master,                        &
                                  lmpi_file_open, lmpi_file_close, lmpi_bcast, &
                                  lmpi_offset_kind, lmpi_read,                 &
                                  lmpi_conditional_stop, lmpi_mpiio_read_l2g,  &
                                  lmpi_reduce, lmpi_repart
    use lmpi,              only : lmpi_io => lmpi_mpiio_type
    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use design_types,      only : design_type
    use string_utils,      only : sprintf, max_str_len, int_to_s
    use allocations,       only : my_alloc_ptr
    use system_extensions, only : se_open, se_open_big
    use solution_io_helpers, only : data_reader

    type(grid_type),             intent(in)    :: grid
    type(soln_type),             intent(inout) :: soln
    type(sadj_type),             intent(inout) :: sadj
    type(design_type),           intent(in)    :: design
    character(*),                intent(in)    :: directory
    logical,           optional, intent(in)    :: set_info_allocate

    integer :: i,j,k,idummy,iunit,iostat

    integer :: fh
    integer(lmpi_offset_kind) :: offset

    real(dp) :: rvalue,version,rdummy

    integer,  dimension(2)                :: beg, end
    real(dp), dimension(:,:), allocatable :: rtemp2

    character(len=256) :: data_desc, expected_desc, actual_desc
    character(max_str_len) :: filename, restart_file

    integer  :: ivalue1, ivalue2, neq_global
    integer  :: record_length, file_lmpi_io
    real(dp) :: version_restart_old_file
    integer  :: file_adim, file_nnodesg, file_nfunctions

    integer, dimension(:), pointer :: l2g

    continue

    if ( grid%cc ) then
      l2g => grid%cl2g
    else
      l2g => grid%l2g
    end if

    iostat = 0
    filename = trim(directory) // trim(grid%project)

    iunit = 20

    lmpi_io_format : if (lmpi_io == 0) then

       filename = trim(directory)//trim(grid%project)//'_adj.%i0'
       restart_file = sprintf( filename, lmpi_id+1 )

       call se_open(iunit,file=restart_file,                                   &
            form='unformatted',status='old',iostat=iostat)

       if (iostat /= 0) then
         write (*,*) 'error: the file ', trim(restart_file),                   &
              ' not found, stopping...'
         call lmpi_conditional_stop(iostat)
       endif

       read(iunit) version  ! read version number
       version = version

   !   write(*,*) 'restart version number = ', version

! read size
       read(iunit) idummy, idummy,                                             &
                   idummy, idummy, idummy,                                     &
                   idummy, idummy, idummy, file_adim, idummy
       idummy = idummy

! read free-stream quantities and time
       read(iunit) rdummy, rdummy, rdummy, rdummy, &
                   rdummy, rdummy, rdummy, rdummy
       rdummy = rdummy

       if (present(set_info_allocate)) then
         if (set_info_allocate) then
           soln%adim = file_adim
           call my_alloc_ptr(sadj%rlam, soln%adim, grid%nnodes01,              &
                             design%nfunctions)
         end if
       end if

       do i = 1,design%nfunctions
         do j = 1,grid%nnodes0
           do k = 1,soln%adim
             read(iunit) sadj%rlam(k,j,i)
           enddo
         enddo
       enddo

       close(iunit)

    else ! lmpi_io_format

      restart_file = trim(filename) // '.adjoint'

      master_open_and_read_header : if ( lmpi_master ) then

        call se_open(iunit,file=restart_file,                                  &
          form='unformatted',access='stream',status='old',iostat=iostat)
        open_error : if (iostat /= 0 ) then
          write(*,*)'error: the file ', trim(restart_file),                    &
            ' not found, stopping...'
          call lmpi_conditional_stop(iostat)
        end if open_error
        rewind(iunit)
        ! version is written with a fake record
        read(iunit,iostat=iostat) record_length
        read_eof_error : if (iostat /= 0 ) then
          write(*,*)'error: the file ', trim(restart_file),                    &
            ' is zero length, stopping.'
          call lmpi_conditional_stop(iostat)
        end if read_eof_error
        magic_big_endian_number : if ( 16777216 == record_length ) then
          close(iunit)
          call se_open_big(iunit,file=restart_file,                            &
            form='unformatted',access='stream',status='old',                   &
            convert='big_endian',iostat=iostat)
          read(iunit,iostat=iostat) record_length
        end if magic_big_endian_number
         if ( 1 /= record_length ) then
          write(*,*) 'expected restart file ',trim(restart_file)
          write(*,*) 'to have the marker 1, it is',record_length
          write(*,*) 'stopping. (please check format or endian)'
          call lmpi_conditional_stop(1)
        end if
        read(iunit) version_restart_old_file       ! read version number
        read(iunit) record_length ! version is written with a fake record
        if ( 1 /= record_length ) then
          write(*,*) 'expected restart file ',trim(restart_file)
          write(*,*) ' version of restart file=',version_restart_old_file
          write(*,*) ' to have the integer 1, it is',record_length
          write(*,*) 'stopping. (please check format or endian)'
          call lmpi_conditional_stop(1)
        end if
        read(iunit) file_lmpi_io
        if ( lmpi_io /= file_lmpi_io ) then
          write(*,*) 'expected restart file ',trim(restart_file)
          write(*,*) ' to match lmpi_io ',lmpi_io,file_lmpi_io
          write(*,*) 'stopping.'
          call lmpi_conditional_stop(1)
        end if

      end if master_open_and_read_header
      call lmpi_conditional_stop(0)

      fh     = iunit
      offset = 0
      mpi_io_api : if ( 1 == lmpi_io ) then

        restart_file = trim(filename) // '.adjoint_mpi'
        call lmpi_file_open(restart_file, fh, 'RDONLY', 'NULL', iostat)
        if (lmpi_master) write(*,*)"Open file for mpi_io ",trim(restart_file)
        call lmpi_conditional_stop(iostat)

       call lmpi_read(fh,offset,'rsentinal',rvalue)
       if (rvalue == 8.125_dp) then
          call lmpi_conditional_stop(0)
       else
          write(*,*)"MPI_IO Internal error : bad leading sentinal ",rvalue
          write(*,*)"for file ",trim(filename)
          write(*,*)"This means the 1st value in the file was not recognized."
          call lmpi_conditional_stop(1)
       end if
       call lmpi_read(fh,offset,'version',rvalue)

      end if mpi_io_api

      verify_l2g_header :  if (lmpi_master) then
        expected_desc = 'l2g'
        read(iunit) actual_desc
        if ( trim(expected_desc) /= trim(actual_desc) ) then
          write(*,*) 'data header mismatch ',&
            trim(expected_desc),' ',trim(actual_desc)
          call lmpi_conditional_stop(1)
        end if
      end if verify_l2g_header
      call lmpi_conditional_stop(0)

      call lmpi_read(fh,offset,'nproc',  ivalue1)

      call lmpi_reduce( soln%neq0, neq_global )
      call lmpi_bcast( neq_global )

      call lmpi_read(fh,offset,'neq_global',ivalue2)
      if (lmpi_master)                                     &
        write(*,*)"... adjoint restart nproc, neq_global ",&
        ivalue1,ivalue2

      if ( neq_global /= ivalue2 ) then
        write(*,*) 'grid size mismatch with flow restart'
        write(*,*) 'global equations', neq_global, ivalue2
        write(*,*) 'global nodes cells', grid%nnodesg, grid%ncellg
        call lmpi_conditional_stop(1)
      end if
      call lmpi_conditional_stop(0)

! assume at this point that either the number of partitions in restart
! is different or the l2g ordering is different. For efficiency,
! lmpi_repart may be set to false by lmpi_mpiio_read_l2g if l2g match
      lmpi_repart = .true.

      data_desc = 'l2g'
      call lmpi_mpiio_read_l2g(fh,offset,data_desc,ivalue2,              &
                               soln%neq0,soln%neq01,l2g)

      mpi_io_api_sentinal2 : if ( 1 == lmpi_io ) then
        call lmpi_read(fh,offset,'rsentinal',rvalue)
        if (rvalue == 4.25_dp) then
          call lmpi_conditional_stop(0)
        else
          write(*,*)"MPI_IO Internal error : bad second sentinal ",rvalue
          write(*,*)"for file ",trim(filename)
          write(*,*)"This means the l2g may be mismatched."
          write(*,*)"Contact support group."
          call lmpi_conditional_stop(1)
        end if
      end if mpi_io_api_sentinal2

      master_reads_global_size : if ( lmpi_master ) then
        expected_desc = 'adjoint_dimensions'
        read(iunit) actual_desc
        if ( trim(expected_desc) /= trim(actual_desc) ) then
          write(*,*) 'data header mismatch ',&
            trim(expected_desc),trim(actual_desc)
          call lmpi_conditional_stop(1)
        end if
        read(iunit) file_adim, file_nnodesg, file_nfunctions
        file_nnodesg = file_nnodesg ; file_nfunctions = file_nfunctions
      end if master_reads_global_size
      call lmpi_conditional_stop(0)

      call lmpi_bcast(file_adim)

      if (present(set_info_allocate)) then
        if (set_info_allocate) then
          soln%adim = file_adim
          call my_alloc_ptr(sadj%rlam, soln%adim, grid%nnodes01,              &
                            design%nfunctions)
        end if
      end if

      beg(1) = 1
      end(1) = soln%adim
      beg(2) = 1
      end(2) = grid%nnodes0
      allocate(rtemp2(soln%adim,grid%nnodes01)); rtemp2 = 0.0_dp
      do i = 1,design%nfunctions
        expected_desc = 'rlam' // trim(int_to_s(i))
        call data_reader(iunit, expected_desc, rtemp2, beg, end, fh, offset)
        sadj%rlam(1:end(1),1:end(2),i) = rtemp2(1:end(1),1:end(2))
      end do
      deallocate(rtemp2)

      master_verify_eof : if ( lmpi_master ) then
        expected_desc = 'end_of_adjoint'
        read(iunit) actual_desc
        if ( trim(expected_desc) /= trim(actual_desc) ) then
          write(*,*) 'data header mismatch ',&
            trim(expected_desc),' ',trim(actual_desc)
          call lmpi_conditional_stop(1)
        end if

        close(iunit)

      end if master_verify_eof
      call lmpi_conditional_stop(0)

      mpi_io_api_sentinal3_close : if ( 1 == lmpi_io ) then
        ! Final sentinal
        call lmpi_read(fh,offset,'rsentinalf',rvalue)
        if (lmpi_master) write(*,*)"...RREST final sentinal ",rvalue
        if (rvalue == 8.125_dp) then
          call lmpi_conditional_stop(0)
        else
          write(*,*)"MPI_IO Internal error : bad trailing sentinal ",rvalue
          write(*,*)"for file ",trim(filename)
          write(*,*)"This means the last value in the file was not recognized."
          write(*,*)"Contact support group."
          call lmpi_conditional_stop(1)
        end if
        if (lmpi_master) write(*,*)'Closing MPI_IO file ',trim(filename)
        call lmpi_file_close(filename,fh,iostat)
        call lmpi_conditional_stop(iostat)
      end if mpi_io_api_sentinal3_close
    end if lmpi_io_format

    call lmpi_conditional_stop(0)

  end subroutine rresta

!================================ WRESTA =====================================80
!
! Writes adjoint lamda variables for restarts
!
!=============================================================================80

  subroutine wresta(grid,soln,sadj,design,directory)

    use lmpi,           only : lmpi_id, lmpi_nproc, lmpi_conditional_stop
    use info_depr,      only : interleaf
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use design_types,   only : design_type

    type(grid_type),             intent(in) :: grid
    type(soln_type),             intent(in) :: soln
    type(sadj_type),             intent(in) :: sadj
    type(design_type),           intent(in) :: design
    character(*),      optional, intent(in) :: directory

    integer :: chunk, offset, iostat

    continue

    chunk = interleaf
    if (chunk < 1 .or. chunk > lmpi_nproc ) chunk = lmpi_nproc

    blocks : do offset = 0, lmpi_nproc-1, chunk
      iostat = 0
      if  (   offset    <= lmpi_id     &
        .and. lmpi_id <  offset+chunk  &
        .and. lmpi_id <  lmpi_nproc  ) then
        call wresta_interleaf(grid,soln,sadj,design,iostat,directory)
      end if
      call lmpi_conditional_stop(iostat) ! this blocks/syncs
    end do blocks

  end subroutine wresta

!================================ WRESTA_INTERLEAF ===========================80
!
! Writes adjoint lamda variables for restarts
!
!=============================================================================80

  subroutine wresta_interleaf(grid,soln,sadj,design,iostat,directory)

    use kinddefs,          only : dp
    use lmpi,              only : lmpi_id, lmpi_nproc, lmpi_master,            &
                                  lmpi_offset_kind, lmpi_write,                &
                                  lmpi_conditional_stop,                       &
                                  lmpi_file_open, lmpi_file_close,             &
                                  lmpi_reduce, lmpi_bcast
    use lmpi,              only : lmpi_io => lmpi_mpiio_type
    use info_depr,         only : ivisc, beta,                                 &
                                  tref, yaw, alpha, re, xmach, simulation_time
    use fluid,             only : prandtl
    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use design_types,      only : design_type
    use string_utils,      only : sprintf, max_str_len, int_to_s
    use system_extensions, only : se_open
    use solution_io_helpers, only : data_writer

    type(grid_type),             intent(in) :: grid
    type(soln_type),             intent(in) :: soln
    type(sadj_type),             intent(in) :: sadj
    type(design_type),           intent(in) :: design
    integer,                     intent(out):: iostat
    character(*),      optional, intent(in) :: directory

    integer :: i,j,k, iunit, fh, ioerr,ivalue,neq_global
    integer, dimension(2) :: beg, end
    integer(lmpi_offset_kind) :: offset

    real(dp) :: version, rvalue

    real(dp), dimension(:,:), allocatable :: rtemp2

    character (max_str_len) :: filename, restart_file

    character(len=256) :: data_desc

    continue

    ioerr = 0
    iunit = 20

    filename = trim(grid%project)
    if (present(directory)) filename = trim(directory) // trim(grid%project)

    lmpi_io_format : if (lmpi_io == 0) then

      version = 3.0_dp

      filename = trim(filename) // '_adj.%i0'
      restart_file = sprintf(filename,lmpi_id+1)
      if ( lmpi_master ) write(*,*) 'Writing restart file'
      call se_open(iunit,file=restart_file,form='unformatted',iostat=iostat)
      if (iostat /= 0) then
        write(*,*)'error: the file ',restart_file,' not opened, stopping...'
        call lmpi_conditional_stop(1)
      endif

      write(iunit) version  ! write version number

      write(iunit) lmpi_nproc, lmpi_id+1,                                     &
           grid%nnodesG, grid%nnodes0, soln%eqn_set,                          &
           ivisc, soln%ndim, soln%n_turb, soln%adim, design%nfunctions

      write(iunit) xmach, re, alpha, yaw, tref, beta, prandtl, simulation_time

      do i = 1,design%nfunctions
        do j = 1,grid%nnodes0
          do k = 1,soln%adim
            write(iunit) sadj%rlam(k,j,i)
          enddo
        enddo
      enddo

    else ! lmpi_io_format

      version = 11.0_dp

      restart_file = trim(filename) // '.adjoint'

      master_open_and_write_header : if ( lmpi_master ) then
        call se_open(iunit,file=restart_file,&
          form='unformatted',access='stream',&
          status='unknown',iostat=ioerr)

        if (0==ioerr) then
          rewind(iunit)
        else
          write(*,*)'error: the file ',restart_file,' not opened, stopping...'
          call lmpi_conditional_stop(ioerr)
        endif

        write(iunit) 1 ! version is written with a fake record
        write(iunit) version  ! write real/complex legacy version number
        write(iunit) 1 ! version is written with a fake record
        write(iunit) lmpi_io ! restart format

      end if master_open_and_write_header

      call lmpi_conditional_stop(ioerr)

      fh     = iunit
      offset = 0
      mpi_io_api : if ( 1 == lmpi_io ) then

        restart_file = trim(filename) // '.adjoint_mpi'
        call lmpi_file_open(restart_file, fh, 'WRONLY', 'NULL', ioerr)
        if (lmpi_master) write(*,*)"Open file for mpi_io ",trim(restart_file)
        call lmpi_conditional_stop(ioerr)

        rvalue = 8.125_dp;     call lmpi_write(fh,offset,'sentinal',rvalue)
        rvalue = version;      call lmpi_write(fh,offset,'version', rvalue)

      end if mpi_io_api


      data_desc = 'l2g'
      if (lmpi_master) write(iunit) data_desc
      ivalue = lmpi_nproc;   call lmpi_write(fh,offset,'nproc',  ivalue)
      call lmpi_reduce( soln%neq0, neq_global )
      call lmpi_bcast( neq_global )
      ivalue = neq_global; call lmpi_write(fh,offset,'neq_global',ivalue)

      beg(1)    = 1
      end(1)    = soln%neq0
      if ( grid%cc ) then
        call lmpi_write(fh,offset,data_desc,grid%cl2g,beg,end)
      else
        call lmpi_write(fh,offset,data_desc,grid%l2g,beg,end)
      endif

      mpi_io_api_sentinal2 : if ( 1 == lmpi_io ) then
        ! Second sentinal
        rvalue = 4.25_dp; call lmpi_write(fh,offset,'sentinal2',rvalue)
      end if mpi_io_api_sentinal2

      master_write_global_size : if ( lmpi_master ) then
        data_desc = 'adjoint_dimensions'
        write(iunit) data_desc
        write(iunit) soln%adim, grid%nnodesg, design%nfunctions
      end if master_write_global_size

      beg(1) = 1
      end(1) = soln%adim
      beg(2) = 1
      end(2) = grid%nnodes0
      allocate(rtemp2(soln%adim,grid%nnodes0)); rtemp2 = 0.0_dp
      do i = 1,design%nfunctions
        data_desc = 'rlam' // trim(int_to_s(i))
        rtemp2(1:end(1),1:end(2)) = sadj%rlam(1:end(1),1:end(2),i)
        call data_writer(iunit, data_desc, rtemp2, beg, end, fh, offset)
      end do
      deallocate(rtemp2)

      master_write_eof : if (lmpi_master) then
        data_desc = 'end_of_adjoint'
        write(iunit) data_desc
      end if master_write_eof

      mpi_io_api_sentinal3_close : if ( 1 == lmpi_io ) then
        ! Final sentinal
        rvalue = 8.125_dp;     call lmpi_write(fh,offset,'sentinalf',rvalue)
        call lmpi_file_close(restart_file,fh,ioerr)
        call lmpi_conditional_stop(ioerr)
      end if mpi_io_api_sentinal3_close
    end if lmpi_io_format

    close(iunit)

  end subroutine wresta_interleaf

end module solution_adj

!================================ LSTGS_FUNC =================================80
!
! Returns basic lstgs 'terms' in a vector
!
!=============================================================================80

  pure function lstgs_func(dx, dy, dz, r11, r12, r13, r22, r23, r33)

    use kinddefs, only     : dp

    real(dp), intent(in)   :: dx, dy, dz, r11, r12, r13, r22, r23, r33
    real(dp), dimension(3) :: lstgs_func

    real(dp)               :: r11inv, r22inv, w11, w22, w33
    real(dp)               :: r12r11, r13r11, r23r22, rmult
    real(dp)               :: xt1, xt2, xt3, t1, t2, coef1, coef2

    real(dp), parameter    :: my_1 = 1.0_dp

    continue

    r11inv = my_1/r11
    r22inv = my_1/r22

    w11  = r11inv*r11inv
    w22  = r22inv*r22inv
    w33  = my_1/(r33*r33)
    r12r11 = r12*r11inv
    r13r11 = r13*r11inv
    r23r22 = r23*r22inv
    rmult  = (r12r11*r23r22 - r13r11)*w33

    xt1 = dx*r12r11
    xt2 = dx*r13r11
    xt3 = dx*w11

    t1 = w22*r12r11
    t2 = r23r22*w33

    coef1 = dy - xt1
    coef2 = dz - xt2 - r23r22*coef1

    lstgs_func(1) = xt3 - t1*coef1 + rmult*coef2
    lstgs_func(2) = w22*coef1 - t2*coef2
    lstgs_func(3) = w33*coef2

  end function lstgs_func

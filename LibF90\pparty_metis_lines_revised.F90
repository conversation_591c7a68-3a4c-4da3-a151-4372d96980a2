module pparty_metis_lines_revised

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#ifdef HAVE_PARMETIS
  use kinddefs,    only : system_i1, r4, r8
  use local_grid,  only : pp_n<PERSON>, pp_ntail, pp_nsize
  use lmpi,        only : lmpi_nproc, lmpi_id, lmpi_reduce, lmpi_die,          &
                          lmpi_gather, lmpi_gatherv, lmpi_master, lmpi_max,    &
                          lmpi_conditional_stop, lmpi_synchronize,             &
                          lmpi_bcast, lmpi_comm_world

  use pparty_metis, only : my_metis_out, lines_check_islands,                  &
                           check_partition_lines, lines_check_objects
#else
  use lmpi,         only : lmpi_nproc, lmpi_die
#endif
  use pparty_metis, only : adj, adjncy, metis_data

  implicit none

  public :: partition_dof_lines, check_points_2d, find_points_2d

  private

 type line_adj_type
    integer                        :: n_entries
    integer, dimension(:), pointer :: entries
    integer, dimension(:), pointer :: entries_line
  end type line_adj_type

contains

!============================== FIND_POINTS_2D ===============================80
!
! For each pair of non-line (x,z) pairs, set l2g_on and find the matching
! off-plane (x,z) pair and set l2g_off. l2g_on and l2g_off are global numbers.
!
! Only partition line version completed.
! With minor revisions, this can be used for non-partition lines.
!
! Works for only x and z; can easily be modified to work for any combination.
!
!=============================================================================80

  subroutine find_points_2d(grid, nl2g_on,l2g_on,l2g_off)

    use grid_types,  only : grid_type
#ifdef HAVE_PARMETIS
    use sort,        only : heap_sort
#endif

    type(grid_type),       intent(in)  :: grid

    integer,               intent(out) :: nl2g_on ! number of non-line points
    integer, dimension(:), pointer :: l2g_on  ! local  index to non-line points
    integer, dimension(:), pointer :: l2g_off ! global index to non-line points

#ifdef HAVE_PARMETIS

    integer :: i, j, k, m, mm, n_lines, n_pts, lmin, lmax, iostat, n2
    integer :: last, ipe, offm
    integer :: num_pts_in_lines, dof_in_line, my_s, my_e, ierr

    real(r8) :: offx, offz

    integer,            dimension(:), allocatable :: temp1, pts_in_lines, ind
    integer,            dimension(:), allocatable :: tempm, passm
    integer,            dimension(:), allocatable :: offl2g, offl2gall
    integer(system_i1), dimension(:), allocatable :: tag
    real(r8),           dimension(:), allocatable :: tempx, tempz, passx, passz

    character(256) :: filename

    integer, parameter :: uf = 20

  continue

    my_s = pp_nhead(lmpi_id)
    my_e = pp_ntail(lmpi_id)

    if (lmpi_master) write(*,*) "    ... Finding matching 2D points."

! read lines, save: pts_in_lines and num_pts_in_lines

    if (lmpi_master) then ! if (present(partition_lines) ...
       filename = trim(grid%project) // '.lines_fmt'
       open(uf,file=filename,status='old',iostat=iostat)
       read(uf,*) n_lines, n_pts
       read(uf,*) lmin, lmax
       if (.false.) lmax = lmin+n_pts ! CCI

       allocate(temp1(n_lines*lmax)); temp1 = -1

       num_pts_in_lines = 0
       do i = 1, n_lines
         read(uf,*,iostat=iostat) dof_in_line
         do j = 1, dof_in_line
            num_pts_in_lines = num_pts_in_lines + 1
            read(uf,*,iostat=iostat) temp1(num_pts_in_lines)
         enddo
       end do
       close(uf)
       allocate(pts_in_lines(num_pts_in_lines))
       pts_in_lines(1:num_pts_in_lines) = temp1(1:num_pts_in_lines)
       deallocate(temp1)
    end if

    call lmpi_bcast(num_pts_in_lines)
    if (.not.lmpi_master) then
       allocate(pts_in_lines(num_pts_in_lines))
       pts_in_lines = -1
    end if
    call lmpi_bcast(pts_in_lines)

   !if (lmpi_master) write(*,*)"AFTER read lines ",lmpi_id,num_pts_in_lines

    call heap_sort(num_pts_in_lines, pts_in_lines)

    !do i = 1,num_pts_in_lines
    !   write(83000+lmpi_id,*) pts_in_lines(i)
    !end do

! tag local nodes not in lines

    allocate(tag(my_s:my_e)); tag = 1

    nl2g_on = 0
    k = 1
    do i = 1,grid%nnodesg
       j = pts_in_lines(k)
       if (i == j) then
          !write(93000+lmpi_id,*) j
          k = k + 1
          if (k > num_pts_in_lines) k = num_pts_in_lines
       else
          if ((i >= my_s).and.(i <= my_e)) then
             !write(94000+lmpi_id,*) i
             tag(i) = 0
             nl2g_on = nl2g_on + 1
          end if
       end if
    end do

    m = max(1,nl2g_on) ! allocate at least one
    allocate(l2g_on (m)); l2g_on  = 0
    allocate(l2g_off(m)); l2g_off = 0

    !write(*,*)"MY_S ",lmpi_id,my_s,my_e,nl2g_on

! Collect local index to local points not in lines. (if any)

    ierr = 0
    if (nl2g_on > 0) then
       allocate(tempx(nl2g_on)); tempx = 0.0_r8
       allocate(tempz(nl2g_on)); tempz = 0.0_r8
       allocate(tempm(nl2g_on)); tempm = 0 ! l2g
       n2 = 0
       do i = my_s,my_e
          if (tag(i) == 0) then
             n2 = n2 + 1
             j = i - (my_s-1)
             tempx(n2) = grid%x(j)
             tempz(n2) = grid%z(j)
             tempm(n2) = i
             l2g_on(n2) = i
             !write(90000+lmpi_id,'(" ",F15.8," ")') tempx(n2)
          end if
       end do
       if (nl2g_on /= n2) write(*,*)"Internal error nn/n2 ",lmpi_id,nl2g_on,n2
       deallocate(tag)

! Gather sorted x,z,l2g of local points not in lines
! into tempx, tempz, tempm, tempv. A total nl2g_on values.

       allocate(ind(nl2g_on));   ind = 0
       call heap_sort(nl2g_on,tempx,ind)

       allocate(temp1(nl2g_on)); temp1 = tempm
       temp1 = tempm;  tempm  = temp1(ind)
       temp1 = l2g_on; l2g_on = temp1(ind)
       deallocate(temp1)

       do i = 1,nl2g_on
          k = tempm(i) - (my_s-1)
          tempx(i) = grid%x(k)
          tempz(i) = grid%z(k)
       !  write(92000+lmpi_id,'(" ",2(F15.8,1x),2(i0,1x))')                    &
       !    tempx(i),tempz(i),tempm(i)
       end do

!      Find any local pairs and resolve

       out1a: do i = 1,nl2g_on-1
          if (tempx(i) == tempx(i+1)) then
             do j = i+1,nl2g_on
                if (tempx(i) == tempx(j)) then
                   if (tempz(i) == tempz(j)) then
                     !write(295000+lmpi_id,'(" ",2(F15.8,1x),2(i0,1x))')       &
                     !  tempx(i),tempz(i),tempm(i),tempm(j)
                      l2g_off(i) = tempm(j)
                      if (lmpi_nproc == 1) l2g_off(j) = tempm(i)
                      cycle out1a
                   end if
                end if
             end do
          end if
       end do out1a

      !do i = 1,nl2g_on
      !   write(292000+lmpi_id,'(" ",2(F15.8,1x),2(i0,1x))')                   &
      !     tempx(i),tempz(i),tempm(i)
      !  write(293000+lmpi_id,'(" ",2(F15.8,1x))') tempx(i),tempz(i)
      !end do
    end if ! nl2g_on > 0 -- any non-line points

! First search local pairs

    if (lmpi_nproc > 1) then
    do ipe = 0,lmpi_nproc-1

       ! Determine number of non-resolved pairs

       if (lmpi_id == ipe) then
          mm = 0
          if (nl2g_on > 0) then
             do i = 1,nl2g_on
                if (l2g_off(i) == 0) mm = mm + 1
             end do
          end if
         !write(*,*)"_______ MM ",lmpi_id,mm
       end if

! Gather remainder and exchange.

       call lmpi_bcast(mm,ipe)
       if (mm == 0) cycle

       allocate(passx(mm)); passx = 0.0_r8
       allocate(passz(mm)); passz = 0.0_r8
       allocate(passm(mm)); passm = 0
       allocate(offl2g(mm)); offl2g = 0
       if (lmpi_id == ipe) then
          j = 0
          do i = 1,nl2g_on
             if (l2g_off(i) == 0) then
                j = j + 1
                passx(j) = tempx(i)
                passz(j) = tempz(i)
                passm(j) = tempm(i)
               !write(192000+lmpi_id,'(" ",2(F15.8,1x),1(i0,1x))')             &
               !  passx(j),passz(j),passm(j)
             end if
          end do
       end if
       call lmpi_bcast(passx,ipe)
       call lmpi_bcast(passz,ipe)
       call lmpi_bcast(passm,ipe)
      !do i = 1,mm
      !   write(193000+lmpi_id,'(" ",2(F15.8,1x),1(i0,1x))')                   &
      !     passx(i),passz(i),passm(i)
      !end do

      ! Perform search using two sorted lists.

       if ((lmpi_id /= ipe).and.(nl2g_on > 0)) then
          last = 1
          out1: do j = 1,mm
             offx = passx(j)
             offz = passz(j)
             offm = passm(j)
            !write(194000+lmpi_id,'(" ",2(F15.8,1x),1(i0,1x))') offx,offz,offm
1            continue
             if (offx < tempx(last)) cycle
             if (offx > tempx(last)) then
                last = last + 1
                if (last > nl2g_on) exit out1
                goto 1
             end if
             do k = last,nl2g_on
                if (offx == tempx(k)) then
                   if (offz == tempz(k)) then
                      l2g_off(k) = offm
                      offl2g(j) = tempm(k)
                     !write(195000+lmpi_id,'(" ",2(F15.8,1x),2(i0,1x))')       &
                     !  offx,offz,offm,offl2g(j)
                     !write(196000+lmpi_id,'(" ",2(F15.8,1x),2(i0,1x))')       &
                     !  offx,offz,l2g_on(ind(k)),l2g_off(k)
                      cycle out1
                   end if
                end if
             end do ! k
          end do out1
       end if
       deallocate(passx,passz,passm)

       allocate(offl2gall(mm)); offl2gall = 0
       call lmpi_max(offl2g,offl2gall,ipe)
       if (lmpi_id == ipe) then
          do i = 1,mm
           ! if (offl2gall(i) /= 0) then
           !   write(398000+lmpi_id,'(" ",2(F15.8,1x),4(i0,1x))') &
           !    tempx(i),tempz(i),tempm(i),offl2gall(i),tempm(i)-(my_s-1),ind(i)
           ! else
           !    write(399000+lmpi_id,*)"ERROR offl2gall 0 ",lmpi_id,i
           ! end if
             l2g_off(i) = offl2gall(i)
          end do
       end if
       deallocate(offl2g,offl2gall)
    end do
    end if ! lmpi_nproc > 1

! All values should have been found

    ierr = 0
    if (nl2g_on > 0) then
       do i = 1,nl2g_on
          if (l2g_off(i) == 0) then
             j = l2g_on(i)-(my_s-1)
             write(*,*)"ERROR in find_points_2d : either error or not found ", &
               lmpi_id,tempx(j),tempz(j),l2g_on(j)
               ierr = 1
             exit
          end if
       end do

       do i = 1,nl2g_on
          k = (l2g_on(i)-my_s)+1
      !   write(93000+lmpi_id,'(" ",2(F15.8,1x),1(i0,1x))')                    &
      !     grid%x(k),grid%z(k),l2g_on(i)
          j = l2g_on(i)
          m = l2g_off(i)
          k = j - (my_s-1)
      !   write(198000+lmpi_id,'(" ",2(i0,1x),2(F15.8,1x))')                   &
      !     min(j,m),max(j,m),grid%x(k),grid%z(k)
      !   write(199000+lmpi_id,'(" ",2(F15.8,1x),2(i0,1x))')                   &
      !     grid%x(k),grid%z(k),min(j,m),max(j,m)
       end do
    end if

    call lmpi_conditional_stop(ierr,'Internal error in find_points_2d')

#else
    nl2g_on = 0 ! cci
    if (.false.) write(*,*) size(grid%x),associated(l2g_on),associated(l2g_off)
#endif

  end subroutine find_points_2d

!============================== CHECK_POINTS_2D ==============================80
!
! Using find_points_2d information, check the metis data for off plane data.
!
! Perform operation based on the input variable "set_or_stop"
!
!   0 - if differing values found, then set to first plane
!   1 - if differing values found, stop (fatal error)
!
!=============================================================================80

  subroutine check_points_2d(set_or_stop, grid, nl2g_on, l2g_on, l2g_off, mm)

    use grid_types, only : grid_type

    integer,               intent(in) :: set_or_stop
    type(grid_type),       intent(in) :: grid
    integer,               intent(in) :: nl2g_on ! number of non-line points
    integer, dimension(:), pointer :: l2g_on  ! local  index to non-line points
    integer, dimension(:), pointer :: l2g_off ! global index to non-line points
    integer,               intent(out):: mm   ! number changed (all PEs)

#ifdef HAVE_PARMETIS

    integer :: i, j, k, m, ipe, my_s, my_e, mismatch, iostat
    integer, dimension(:), allocatable :: passoff, passmet

    character(256) :: filename

    integer, parameter :: uf = 20
    integer, parameter :: fatal = 1
    integer, parameter :: non_fatal = 0

  continue

    mm = 0
    if (lmpi_master) write(*,*)"    ... Checking metis data for 2d "
    if (lmpi_nproc == 1) return

    my_s = pp_nhead(lmpi_id)
    my_e = pp_ntail(lmpi_id)
    !write(*,*)" check_points_2d : my_s,my_e ",lmpi_id,my_s,my_e

    mismatch = 0
    do ipe = 0,lmpi_nproc-1

       if (lmpi_id == ipe) mm = nl2g_on
       call lmpi_bcast(mm,ipe)
       if (mm == 0) cycle

       allocate(passoff(mm)); passoff = 0
       allocate(passmet(mm)); passmet = 0

       if (lmpi_id == ipe) then
          passoff = l2g_off
          do i = 1,mm
             j = l2g_on(i)-(my_s-1)
             passmet(i) = metis_data(j)
          end do
       end if
       call lmpi_bcast(passoff,ipe)
       call lmpi_bcast(passmet,ipe)

       do i = 1,mm
          if ((passoff(i) >= my_s).and.(passoff(i) <= my_e)) then
             j = passoff(i)-(my_s-1)
             if (metis_data(j) /= passmet(i)) then
                mismatch = mismatch + 1
                if (set_or_stop == fatal) then
                   write(310000+lmpi_id,'(" ",2(F15.8,1x),5(i0,1x)," ")')     &
                     grid%x(j),grid%z(j),i,j,metis_data(j),passoff(i),passmet(i)
                else
                  !write(210000+lmpi_id,'(" ",2(F15.8,1x),5(i0,1x)," ")')     &
                  !  grid%x(j),grid%z(j),i,j,metis_data(j),passoff(i),passmet(i)
                   metis_data(j) = passmet(i)
                end if
             end if
          end if
       end do

       deallocate(passoff,passmet)
    end do
    mm = 0
    call lmpi_reduce(mismatch,mm)
    call lmpi_bcast(mm,0) ! cci

    if (lmpi_master) then
       if (mm == 0) then
          write(*,*)"    ... all metis data matches are expected."
       else
          write(*,*)"    ... found differing partition values ",mm
          if (set_or_stop == non_fatal) then
             write(*,*)"    ... Values reset to match. "
          else
             write(*,*)"    ... Values cause a fatal error. Stopping."
          end if
       end if
    end if
    if (mm /= 0) then
       if ((set_or_stop == fatal).and.(mismatch > 0)) then
          write(filename,'("check_2d_metis_data_",i4.4,".dat")') lmpi_id
          open(uf,file=filename,status='unknown',iostat=iostat)
          do i = 1,nl2g_on
             j = l2g_on(i)-(my_s-1)
             k = min(l2g_on(i),l2g_off(i))
             m = max(l2g_on(i),l2g_off(i))
             write(uf,'(" ",3(F15.8,1x),4(i0,1x))')                            &
               grid%x(j),grid%z(j),grid%y(j),k,m,metis_data(j)
          end do
          if (lmpi_master) write(*,*)"See check_2d_metis_data_xxx.dat files."
       end if
    end if

    if(set_or_stop==fatal)call lmpi_conditional_stop(mm,'EXIT check_points_2d')

#else
    if (.false.) write(*,*) set_or_stop, size(grid%x), nl2g_on, l2g_on, l2g_off
    mm = 0 ! cci
#endif

  end subroutine check_points_2d ! l2g


!============================== PARTITION_DOF_LINES ==========================80
!
! Partitions mesh in parallel
!   (1) Collapse dof in a line to a single dof.
!   (2) Form reduced adjacency graph.
!       Many PEs may have 0 or 1-sized adj, which may be problematic for PM.
!   (3) Distribute adj, adjacency, and node_weights evenly over all PEs
!   (4) Call ParMetis.
!   (5) Map partition values back to the original partition vector
!       noting multiple level of indirection).
!         org - originial adj (non-collapsed)
!         c   - condensed (collapsed lines, condensed adj,adjncy)
!         cd  - condensed and distributed
!
! Validation
!   The adj,adjncy should match regardless of distribution.
!   So call debug_adj, concatenate files, and md5sum.
!      where debug_adj - write (global_number, node_weight, size :: adjncy)
!   Cat(4100) == Cat(4200);   3200 == 3300
!
!=============================================================================80

  subroutine partition_dof_lines( grid, n_pts, n_lines, line, endline)

    use grid_types,       only : grid_type
#ifdef HAVE_PARMETIS
    use info_depr,        only : twod
    use twod_util,        only : yplane_2d, y_coplanar_tol
    use metis_defs,       only : partitioning_only
    use info_depr,        only : pp_cmd_ubvec
    use sort,             only : binary_search, binary_search_less
    use pparty_metis,     only : lines_check_singleton, my_metis_check
    use sort,             only : small_sort
    use lmpi,             only : lmpi_min, lmpi_allgatherv, lmpi_recv, lmpi_send
    use suggar_info,      only : fun3d_comm
    use nml_overset_data, only : dci_on_the_fly
    use allocations,      only : my_alloc_ptr
    use cfl_defs,       only : hanim

    type(grid_type),         intent(in) :: grid
    integer,                 intent(in) :: n_pts, n_lines
    integer, dimension(:,:), intent(in) :: line
    integer, dimension(:),   intent(in) :: endline

    integer :: ncon
    integer :: i, j, k, m
    integer :: edgeCut,numbering,weightflag,generic_comm

    integer :: my_ct, my_s, my_e, myi, is, ie
    integer :: iline, dof_adj, iline_adj, n_adj_est, n_deleted_dof
    integer :: n_off, ii, ierr
    integer :: dof0, dofg, reduced_dofg, icount
    integer :: ict_nproc, ilast, ipe, old_is, old_ie,a,b,c,d, osize

    integer, dimension(:), allocatable :: weight
    integer, dimension(5)               :: options
    integer, dimension(:), allocatable :: temp1, c_metis_data, cd_metis_data
!beginNeverComplex
    real(r4), dimension(:), allocatable :: tpwgts,ubvec
!endNeverComplex

    logical, dimension(:), allocatable :: first

    integer(system_i1), dimension(:), allocatable :: tag_in_line
    logical, dimension(:), allocatable :: solved_dof

    integer, dimension(:), allocatable :: tadj, tadjncy, tvtxdist
    integer, dimension(:), allocatable :: c_tadj, c_tadjncy

    integer, dimension(:), allocatable :: root_dof, which_line, pts
    integer, dimension(:), allocatable :: adj_ct
    integer, dimension(:), allocatable :: map, map_back
    integer, dimension(:), allocatable :: temp_dof, temp_line
    integer, dimension(:), allocatable :: endline_check
    integer, dimension(:), allocatable :: line_pid, line_dof
    integer, dimension(:), allocatable :: temp_proc, temp_proc_global, temp
    integer, dimension(:), allocatable :: deleted_dof, counts
    integer, dimension(:), allocatable :: node_weight

! Condensed start, end, size (adj) after condensed collapsed lines
    integer, dimension(:), allocatable :: c_pp_nsize, c_pp_nhead, c_pp_ntail
    integer                            :: c_my_nsize, c_my_nhead, c_my_ntail

! Condensed, distributed (cd) start, end, size (adj) after redistribute

    integer, dimension(:), allocatable :: cd_pp_nsize, cd_pp_nhead, cd_pp_ntail
    integer                            :: cd_my_nsize, cd_my_nhead, cd_my_ntail

    integer :: condensation_debug = 0

    integer :: n_entries_est, n_adj, n_adj_max, n_adj_min
    type(line_adj_type), dimension(:), allocatable :: line_adj

    integer, dimension(:), allocatable :: edge_weight

    logical :: twod_point_reduction, ideal_objects
    logical :: invoke_lines_check_islands

  continue

    if (lmpi_nproc == 1) then
      if (.not.allocated(metis_data)) allocate(metis_data(pp_nsize(lmpi_id)))
      metis_data = 1
      deallocate(adj)
      deallocate(adjncy)
      return
    endif

    call lmpi_synchronize()
    if (lmpi_master) then
       write(*,*); write(*,*) "    ... Start line partitioning."
    end if

    ierr = 0

    dof0 = size(adj,1) - 1
    i = dof0 ; call lmpi_reduce(i,dofg) ; call lmpi_bcast(dofg)

    ! Set up data to allow for reduction of twod graph.
    ! and check if compatible with current version of ParMetis.

    allocate(solved_dof(dofg)) ; solved_dof = .true.

    reduced_dofg = dofg
    twod_point_reduction = .false.
    if ( twod ) then
      call set_solved_dof( dof0, dofg, reduced_dofg, grid%y, &
                           solved_dof, twod_point_reduction )
    endif

    n_adj_max =-huge(1)
    n_adj_min =+huge(1)
    do i = 1,size(adj,1)-1
      n_adj = adj(i+1)-adj(i)
      n_adj_max = max( n_adj_max, n_adj )
      n_adj_min = min( n_adj_min, n_adj )
      if ( n_adj <= 0 ) ierr = 1
    end do

    i=+n_adj_max ; call lmpi_max(i, n_adj_max)
    i=-n_adj_min ; call lmpi_max(i, n_adj_min)
    call lmpi_conditional_stop(ierr,'adj entries:partition_dof_lines')

    if ( lmpi_master ) then
      write(*,*)
      write(*,*)  ' Parallel partitioning with lines...n_lines=',n_lines
      write(*,*)  ' ...........dofg(original graph)=', dofg
      if ( twod ) then
        write(*,*)' ......................yplane_2d=', yplane_2d
        write(*,*)' .................y_coplanar_tol=', y_coplanar_tol
        write(*,*)' ..on-plane dofg(original graph)=', reduced_dofg
        write(*,*)' ...........twod_point_reduction=', twod_point_reduction
      endif
      write(*,*)  ' ......n_adj_min(original graph)=',-n_adj_min
      write(*,*)  ' ......n_adj_max(original graph)=',+n_adj_max
    end if

    my_s = pp_nhead(lmpi_id)
    my_e = pp_ntail(lmpi_id)

    allocate(line_adj(n_lines))
    do i=1,n_lines
      line_adj(i)%n_entries = 0
      n_entries_est = 2*endline(i)
      if ( twod ) n_entries_est = n_entries_est/2
      call my_alloc_ptr( line_adj(i)%entries,      n_entries_est )
      call my_alloc_ptr( line_adj(i)%entries_line, n_entries_est )
    enddo

    allocate(root_dof(n_lines)); root_dof = 0
    allocate(line_pid(n_lines)); line_pid = -1
    allocate(pts(n_pts));        pts      = 0
    allocate(deleted_dof(n_pts-n_lines)); deleted_dof = 0
    allocate(tag_in_line(dofg)) ; tag_in_line = 0
    allocate(which_line(n_pts)); which_line = 0

    call set_line_structures( n_pts, n_lines, line, endline,                   &
                              my_s, my_e, root_dof, line_pid,                  &
                              pts, deleted_dof, which_line, tag_in_line )

    ! Reduce adjacencies for any point in a line on a processor.

    myi = my_s-1
    line_adj_on_proc : do i = 1,size(adj,1)-1

      myi = myi + 1
      if ( tag_in_line(myi) == 0 ) cycle line_adj_on_proc

      call find_line( myi, iline, ierr, n_pts, pts, which_line, tag_in_line )
      if ( ierr /= 0 )   exit line_adj_on_proc
      if ( iline == 0 ) cycle line_adj_on_proc

      do j = adj(i),adj(i+1)-1
        dof_adj = adjncy(j)
        if ( tag_in_line(dof_adj) == 0 ) then
          call update_line_adj( dof_adj, 0, line_adj(iline) )
        else ! part of a line, may be a candidate
          call find_line( dof_adj, iline_adj, ierr,                            &
                          n_pts, pts, which_line, tag_in_line )
          if ( ierr /= 0 ) exit line_adj_on_proc
          if ( iline_adj == iline ) cycle
          call update_line_adj( dof_adj, iline_adj, line_adj(iline) )
        end if
      end do

    end do line_adj_on_proc
    call lmpi_conditional_stop(ierr,'line_adj_on_proc:partition_dof_lines')

    ! Condense off-processor adjacencies to on-processor adjacency.

    allocate(counts(0:lmpi_nproc-1)); counts = 0
    condensation_line : do iline = 1, n_lines

      call lmpi_gather(line_adj(iline)%n_entries,counts)
      call lmpi_bcast(counts)
      icount = sum(counts)

      if (icount > 0) then
         allocate(temp_dof(icount)); temp_dof = 0
         call lmpi_gatherv(line_adj(iline)%entries,counts(lmpi_id),&
           temp_dof,counts)
         call lmpi_bcast(temp_dof)

         allocate(temp_line(icount)); temp_line = 0
         call lmpi_gatherv(line_adj(iline)%entries_line,counts(lmpi_id),&
           temp_line,counts)
         call lmpi_bcast(temp_line)

         if ( lmpi_id == line_pid(iline) ) then
            do ii = 1,icount
               call update_line_adj(temp_dof(ii),temp_line(ii),line_adj(iline))
            end do
         end if
         deallocate(temp_dof)
         deallocate(temp_line)
      end if

      if ( condensation_debug > 0 ) then
        if ( lmpi_id == line_pid(iline) ) then
          n_off = line_adj(iline)%n_entries
          write(*,"(1x,a,i5,a,i10,a,i10)")                                     &
           ' line=',iline,' Total adjacencies=', n_off,                        &
           ' pid=',line_pid(iline)
          write(6000+lmpi_id,"(1x,a,4i10)")
          write(6000+lmpi_id,"(1x,a,4i10)") '     iline=',iline
          m = 0
          do ii=1,n_off
            if ( line_adj(iline)%entries_line(ii) == 0 ) m = m + 1
          enddo
          write(6000+lmpi_id,"(1x,a,4i10)") ' n_entries(total/line/point)=',   &
          n_off, n_off - m, m
          do ii=1,n_off
            write(6000+lmpi_id,"(1x,2i10)")                                    &
            line_adj(iline)%entries_line(ii),line_adj(iline)%entries(ii)
          enddo
        endif
      endif
     !call lmpi_synchronize()

    enddo condensation_line
    deallocate(counts)

    ! Count entries in reduced adjacency list.

    allocate(first(   n_lines)) !count line adjacencies once
    allocate(line_dof(n_lines)) ;  line_dof      = 0

    allocate(adj_ct(dof0+1)) ; adj_ct  = 0
    allocate(   map(dof0  )) ; map     = 0

    my_ct = 0
    myi = my_s-1
    outer1: do i = 1,dof0
       myi = myi + 1

       if ( tag_in_line(myi) == 0 ) then

          !...points not in a line.

          if ( twod .and. .not.solved_dof( myi) ) cycle outer1

          my_ct  = my_ct + 1
          map(i) = my_ct
          first  = .true. ! count elements of a line once
          do j = adj(i),adj(i+1)-1
             dof_adj = adjncy(j)
             if ( twod .and. .not.solved_dof( dof_adj) ) cycle
             if (tag_in_line(dof_adj)==0) then

               !...adjacent point not in a line.

               adj_ct(my_ct) = adj_ct(my_ct) + 1
             else ! part of a line, may be a candidate

               !...adjacent point in a line

               call find_line( dof_adj, iline_adj, ierr,                       &
                               n_pts, pts, which_line, tag_in_line )
               if ( ierr /= 0 ) exit outer1
               if ( .not. first(iline_adj) ) cycle
               adj_ct(my_ct)    = adj_ct(my_ct) + 1
               first(iline_adj) = .false.
             end if
          end do
       else

         iline = binary_search( n_lines, root_dof, myi )
         if ( iline > 0 ) then
           if ( lmpi_id == line_pid(iline) ) then

             !...root point in a line.

             my_ct           = my_ct + 1
             line_dof(iline) = my_ct
             map(i)          = my_ct

             n_adj_est = line_adj(iline)%n_entries

             adj_ct(my_ct)  = adj_ct(my_ct) + n_adj_est

           end if
         end if
       end if
    end do outer1
    call lmpi_conditional_stop(ierr,'binary_search1:partition_dof_lines')

    ! Populate map_back.

    allocate(map_back(my_ct)); map_back = 0
    j = 0
    do i = 1,dof0
      if ( map(i) > 0 ) then
        j = j + 1
        map_back(j) = i
      end if
    end do

   ! Allocate reduced adjacency list.

    allocate(tadj(my_ct+1));   tadj   = 0

    n_adj_max =-huge(1)
    n_adj_min =+huge(1)
    tadj(1) = 1
    do i = 2,my_ct+1
      n_adj     =  adj_ct(i-1)
      n_adj_max = max( n_adj_max, n_adj )
      n_adj_min = min( n_adj_min, n_adj )
      if ( n_adj <= 0 ) ierr = 1
      tadj(i) = tadj(i-1) + n_adj
    end do
    i=+n_adj_max ; call lmpi_max(i, n_adj_max)
    i=-n_adj_min ; call lmpi_max(i, n_adj_min)

    i = my_ct ; call lmpi_reduce(i, reduced_dofg )
    call lmpi_bcast( reduced_dofg)
    if ( lmpi_master ) then
      write(*,*) ' ...........dofg( reduced_graph)=', reduced_dofg
      write(*,*) ' ......n_adj_min( reduced graph)=',-n_adj_min
      write(*,*) ' ......n_adj_max( reduced graph)=',+n_adj_max
      write(*,*) ' ...dof deleted ( reduced graph)=',n_pts - n_lines
    end if

    call lmpi_conditional_stop(ierr,                                           &
    'reduced graph has n_adj <= 0:partition_dof_lines')

    if ( twod ) then
      call check_partitioning( my_ct,'Before partitioning' )
    endif

    m = tadj(my_ct+1)-1
    allocate(tadjncy(max(m,1))); tadjncy = 0

    ! Populate reduced adjacency list.

    allocate(node_weight(my_ct)); node_weight = 1
    adj_ct = 0
    myi    = my_s-1
    my_ct  = 0
    outer2: do i = 1,dof0
      myi = myi + 1
      if ( tag_in_line(myi)==0 ) then

        !...point not in a line.

        my_ct = my_ct + 1
        first = .true. ! count elements of a line once
        do j = adj(i),adj(i+1)-1
          dof_adj = adjncy(j)
          if ( tag_in_line(dof_adj) == 0 ) then
            m = tadj(my_ct)+adj_ct(my_ct)
            tadjncy(m) = dof_adj
            adj_ct(my_ct) = adj_ct(my_ct)+1
          else ! part of a line, may be a candidate
            call find_line( dof_adj, iline_adj, ierr,                          &
                            n_pts, pts, which_line, tag_in_line )
            if ( ierr /= 0 ) exit outer2
            if ( .not. first(iline_adj) ) cycle
            m             = tadj(my_ct)+adj_ct(my_ct)
            tadjncy(m)    = root_dof(iline_adj)
            adj_ct(my_ct) = adj_ct(my_ct)+1
            first(iline_adj) = .false.
          end if
        end do

      else

        iline = binary_search(n_lines,root_dof,myi)
        if ( iline > 0 ) then
          if ( lmpi_id == line_pid(iline) ) then

            !...root point in a line.

            my_ct  = my_ct + 1
            map(i) = my_ct
            node_weight(my_ct) = endline(iline)

            do ii=1,line_adj(iline)%n_entries
              m         = tadj(my_ct)+adj_ct(my_ct)
              dof_adj   = line_adj(iline)%entries(ii)
              iline_adj = line_adj(iline)%entries_line(ii)
              if ( iline_adj > 0 ) then
                dof_adj = root_dof(iline_adj)
              endif
              tadjncy(m)  = dof_adj
              adj_ct(my_ct) = adj_ct(my_ct) + 1
            enddo

          end if
        end if
      end if
    end do outer2
    call lmpi_conditional_stop(ierr,'binary_search2:partition_dof_lines')

    deallocate(first)

    if (allocated(map)) deallocate(map)

    ! Adjust global numbers in reduced adjacencies
    ! for the dof deleted in the lines.

    n_deleted_dof = n_pts - n_lines
    adjust : do i = 1,my_ct
      do j = tadj(i),tadj(i+1)-1
        dof_adj = tadjncy(j)
        k = binary_search_less( n_deleted_dof, deleted_dof, dof_adj, m)
        if ( k > 0 ) ierr = 1
        tadjncy(j) = tadjncy(j) - m
        if ( ierr > 0 ) exit adjust
      end do
    end do adjust
    deallocate(deleted_dof)
    call lmpi_conditional_stop(ierr,'binary_search_less:partition_dof_lines')

    if ( twod ) then
      call check_partitioning( my_ct,'Before gather of condensed counts' )
    endif

! Gather condensed counts

    allocate(c_pp_nsize(0:lmpi_nproc-1)); c_pp_nsize = 0
    call lmpi_gather(my_ct,c_pp_nsize)
    call lmpi_bcast(c_pp_nsize)

! Compute condensed nhead, ntail,nsize.
! Condensed is collapsed lines adj,adjncy.
! Condensed, distributes is collapsed lines are redistributing adj,adjncy

    allocate(c_pp_nhead(0:lmpi_nproc-1)); c_pp_nhead = 0
    allocate(c_pp_ntail(0:lmpi_nproc-1)); c_pp_ntail = 0

    c_pp_nhead(0) = 1
    do i = 1,lmpi_nproc-1
       c_pp_nhead(i) = c_pp_nhead(i-1)+c_pp_nsize(i-1)
    end do
    do i = 0,lmpi_nproc-1
       c_pp_ntail(i) = (c_pp_nhead(i)+c_pp_nsize(i))-1
    end do
    c_my_nsize = c_pp_nsize(lmpi_id)
    c_my_nhead = c_pp_nhead(lmpi_id)
    c_my_ntail = c_pp_ntail(lmpi_id)

    !write(1100+lmpi_id,*)"c_pp_nsize ",c_pp_nsize
    !write(1100+lmpi_id,*)"c_pp_nhead ",c_pp_nhead
    !write(1100+lmpi_id,*)"c_pp_ntail ",c_pp_ntail

    ! Do not remove
    !call debug_adj(4100, c_my_nhead, c_my_nsize,                              &
    !     size(tadj), size(tadjncy), tadj, tadjncy, node_weight)

! Count nummber of zero dof

    ict_nproc = 0
    do i = 0,lmpi_nproc-1
       if (c_pp_nsize(i) > 0) ict_nproc = ict_nproc + 1
    end do

    if ( lmpi_master ) then
       write(*,*)
       write(*,*) ' Zero dof (reduced graph) remaining on some processors.'
       write(*,*) ' Number of dof (reduced graph) on each processor:'
       write(*,'(1x,30000(1x,i0))') c_pp_nsize
       write(*,*) '  ...ict_nproc(processors with non-zero dof)=',ict_nproc
    end if

    if ( ict_nproc == 1 .and. twod .and. reduced_dofg == n_lines ) then
       write(*,*)"TWOD ill-conditions"
       if ( lmpi_master ) then
         write(*,*) "Single processor contains all lines (and all the dof)."
         write(*,*) "Consider ordering dof in the input grid so lines fall&
                    &on different processors."
         write(*,*) "Or develop a simple fix."
       endif
      !call lmpi_conditional_stop(1,'Twod line failure:partition_dof_lines')
    end if

! Always redistribute (so, only one path to ParMetis)
! Save tadj, tadjncy.

    i = size(tadj); allocate(c_tadj(max(i,1))); c_tadj = 0
    if (i > 0) c_tadj(1:i) = tadj(1:i)
    if (allocated(tadj)) deallocate(tadj)

    i = size(tadjncy); allocate(c_tadjncy(max(i,1))); c_tadjncy = 0
    if (i > 0) c_tadjncy(1:i) = tadjncy(1:i)
    if (allocated(tadjncy)) deallocate(tadjncy)

! Compute new condensed distributed pp_nhead, pp_ntail, pp_nsize

    allocate(cd_pp_nhead(0:lmpi_nproc-1)); cd_pp_nhead = 0
    allocate(cd_pp_ntail(0:lmpi_nproc-1)); cd_pp_ntail = 0
    allocate(cd_pp_nsize(0:lmpi_nproc-1)); cd_pp_nsize = 0

    k = sum(c_pp_nsize)
    cd_pp_nsize = k/lmpi_nproc
    j = k - ((k/lmpi_nproc)*lmpi_nproc)
    if (j > 0) cd_pp_nsize(0:j-1) = cd_pp_nsize(0:j-1) + 1
    if (sum(cd_pp_nsize) /= sum(c_pp_nsize)) then
       write(*,*)"Internal error.c_pp_nsize", sum(c_pp_nsize), sum(cd_pp_nsize)
       write(*,*)"c_pp_nsize  ",c_pp_nsize,  sum(c_pp_nsize)
       write(*,*)"cd_pp_nsize ",cd_pp_nsize, sum(cd_pp_nsize)
       call lmpi_conditional_stop(1,'Internal error c_pp_nsize NOT match.')
    end if
    cd_pp_nhead(0) = 1
    do i = 1,lmpi_nproc-1
       cd_pp_nhead(i) = cd_pp_nhead(i-1)+cd_pp_nsize(i-1)
    end do
    do i = 0,lmpi_nproc-1
       cd_pp_ntail(i) = (cd_pp_nhead(i)+cd_pp_nsize(i))-1
    end do

    !write(1100+lmpi_id,*)"cd_pp_nsize ",cd_pp_nsize
    !write(1100+lmpi_id,*)"cd_pp_nhead ",cd_pp_nhead
    !write(1100+lmpi_id,*)"cd_pp_ntail ",cd_pp_ntail

    cd_my_nhead = cd_pp_nhead(lmpi_id)
    cd_my_ntail = cd_pp_ntail(lmpi_id)
    cd_my_nsize = cd_pp_nsize(lmpi_id)

    allocate(tvtxdist(lmpi_nproc+1)); tvtxdist = 0
    tvtxdist(1:lmpi_nproc) = cd_pp_nhead
    tvtxdist(lmpi_nproc+1) = cd_pp_ntail(lmpi_nproc-1)+1

    if (lmpi_master) then
       write(*,*)
       write(*,*) ' Number of dof (distributed reduced graph) on each PE:'
       write(*,'(1x,30000(1x,i0))') cd_pp_nsize
    end if

    allocate(tadj(cd_my_nsize+1));  tadj = 0

    if (allocated(weight)) deallocate(weight)
    allocate(weight(cd_my_nsize)); weight = 0

! Convert saved tadj to differences, and collect.

    j = c_pp_nsize(lmpi_id)
    if (j > 0) then
       allocate(temp1(j)); temp1 = 0
       do i = 1,j
          temp1(i) = c_tadj(i+1)-c_tadj(i)
       end do
    else
      allocate(temp1(1)); temp1 = 0
    end if

    allocate(temp(sum(c_pp_nsize))); temp = 0
    call lmpi_allgatherv(temp1,c_pp_nsize,temp)
    tadj(1) = 1
    tadj(2:cd_my_nsize+1) = temp(cd_my_nhead:cd_my_ntail)

! Convert adj to local offsets for PM.

    j = 2
    do i = cd_my_nhead, cd_my_ntail
       tadj(j) = tadj(j-1)+tadj(j)
       j = j + 1
    end do

! Gather node weights

    temp1 = 0
    j = c_pp_nsize(lmpi_id)
    if (j > 0) then
      do i = 1,j
         temp1(i) = node_weight(i)
      end do
      k = 0
      if (lmpi_id > 0) k = c_pp_ntail(lmpi_id-1)
    end if
    if (allocated(node_weight)) deallocate(node_weight)

    temp = 0

    call lmpi_allgatherv(temp1,c_pp_nsize,temp)
    weight(1:cd_my_nsize) = temp(cd_my_nhead:cd_my_ntail)
    deallocate(temp1, temp)

! Gather adjncy
!  b:c is the adj(b:c) to move
!  a:b is the adjncy(a:b) to move

    i = tadj(cd_my_nsize+1)-1
    !write(3000+lmpi_id,*)"Alloc tadjncy ",cd_my_nsize+1,i
    allocate(tadjncy(i)); tadjncy = 0
    ierr  = 1
    ilast = 1
    !write(3000+lmpi_id,*)"cd_my_nhead/tail ",cd_my_nhead,cd_my_ntail
    !write(3000+lmpi_id,*)
     do ipe = 0,lmpi_nproc-1
        if (c_pp_nsize(ipe) > 0) then
           old_is = c_pp_nhead(ipe)
           old_ie = c_pp_ntail(ipe)
          !write(3000+lmpi_id,*) 'Old_is,old_ie --------------',old_is,old_ie
           if (lmpi_id == ipe) then
              ! store mine -- if range range avail is in range needed
              if ((old_ie >= cd_my_nhead).and.(old_is <= cd_my_ntail)) then
                                               ! subtrace c offset
                  c = max(cd_my_nhead, old_is) - (c_my_nhead-1)
                  d = min(cd_my_ntail, old_ie) - (c_my_nhead-1)
                  a = c_tadj(c)
                  b = c_tadj(d+1)-1
                  osize = (b-a)+1
                 !write(3000+lmpi_id,'(" Store ",6(i0,1x))')                &
                 !  ilast,c,d,osize,a,b
                  tadjncy(ilast:(ilast+osize)-1) = c_tadjncy(a:b)
                  ilast = ilast+osize
              end if
              ! send others
              do j = 0,lmpi_nproc-1
                 if (j /= lmpi_id) then ! if range avail is in range needed
                    if ((old_ie >= cd_pp_nhead(j)).and.                     &
                        (old_is <= cd_pp_ntail(j))) then ! subtract c offset
                        c = max(cd_pp_nhead(j), old_is) - (c_my_nhead-1)
                        d = min(cd_pp_ntail(j), old_ie) - (c_my_nhead-1)
                        a = c_tadj(c)
                        b = c_tadj(d+1)-1
                        osize = (b-a)+1
                        allocate(temp(osize)); temp = 0
                        temp(1:osize) = c_tadjncy(a:b)
                       !write(3000+lmpi_id,'(" Send ",6(i0,1x))')              &
                       !  j,osize,c,d,a,b
                        call lmpi_send(temp,osize,j,ipe*10,ierr)
                        deallocate(temp)
                    end if
                 end if
              end do
           else
              ! recv and store -- if range avail is in range avail
              if ((old_ie >= cd_my_nhead).and.(old_is <= cd_my_ntail)) then
                                             ! subtract cd offset
                c = max(cd_my_nhead, old_is) - (cd_my_nhead-1)
                d = min(cd_my_ntail, old_ie) - (cd_my_nhead-1)
                a = tadj(c)
                b = tadj(d+1)-1
                osize = (b-a)+1
               !write(3000+lmpi_id,'(" Recv ",6(i0,1x))')                      &
               !  ipe,osize,c,d,a,b
                allocate(temp(osize)); temp = 0
                call lmpi_recv(temp,osize,ipe,ipe*10,ierr)
                tadjncy(ilast:(ilast+osize)-1) = temp(1:osize)
                ilast = ilast+osize
                deallocate(temp)
              end if
           end if
        end if
     end do
     if (allocated(c_tadj))     deallocate(c_tadj)
     if (allocated(c_tadjncy))  deallocate(c_tadjncy)
     if (allocated(c_pp_nsize)) deallocate(c_pp_nsize)
     if (allocated(c_pp_nhead)) deallocate(c_pp_nhead)
     if (allocated(c_pp_ntail)) deallocate(c_pp_ntail)

    ! Do not remove
    !call debug_adj(4200, cd_my_nhead, cd_my_nsize,                            &
    !     size(tadj), size(tadjncy), tadj, tadjncy, weight)

! ParMetis

    numbering  = 1
    options    = 0; options(1) = 0; options(2) = 3; options(3) = 1

    allocate(cd_metis_data(cd_my_nsize)); cd_metis_data = 0

    ncon = 1
    allocate(tpwgts(lmpi_nproc*ncon)); tpwgts = 1.0_r4/real(lmpi_nproc,r4)
    allocate(ubvec(ncon));             ubvec  = real(pp_cmd_ubvec,r4)

    weightflag = 2

    if (allocated(edge_weight)) deallocate(edge_weight)
    allocate(edge_weight(1)); edge_weight = 0

    generic_comm = lmpi_comm_world
    if ( dci_on_the_fly ) generic_comm = fun3d_comm

    edgeCut = 0

    if (lmpi_master) then
       write(*,*)'    ... Calling ParMetis (ParMETIS_V3_PartKway) ...'
       !write(*,'(a40,5(i0,1x))')"         PARMETIS: weightflag,numbering ",   &
       !   weightflag,numbering,ncon,associated(weight),generic_comm
    end if

    call ParMETIS_V3_PartKway(                                                 &
         tvtxdist, tadj, tadjncy, weight, edge_weight,                         &
         weightflag, numbering, ncon, lmpi_nproc, tpwgts, ubvec,               &
         options, edgeCut, cd_metis_data, generic_comm)

    if (lmpi_master) write(*,*) '  ...Resultant edgeCut = ',edgeCut

    do i = 1,cd_my_nsize
       if ((cd_metis_data(i) <= 0).or.(cd_metis_data(i) > lmpi_nproc))         &
          call lmpi_conditional_stop(1,'Invalid partition vector value')
    end do

    deallocate(tvtxdist,edge_weight,ubvec,tpwgts)
    if (allocated(adj_ct)) deallocate(adj_ct)

    ! Check for singletons (one-node islands) on condensed,distributed lines

    i = size(tadj)
    j = size(tadjncy)
    k = size(cd_metis_data)

    if ( lmpi_master .and. twod ) write(*,*) ' lines_check_singleton.'

    call lines_check_singleton(i,j,k, i-1,j, tadj,tadjncy,weight,cd_metis_data)
    deallocate(weight)

! Redistribute ParMetis vector from cd (condensed,distributed) to c (condensed).

   ! Do not remove (3200 should match 3300)
   !do i = 1,cd_my_nsize
   !   write(3200+lmpi_id,*) cd_my_nhead+i-1,cd_metis_data(i)
   !end do

    allocate(c_metis_data(c_my_nsize)); c_metis_data = -1
    allocate(temp(cd_pp_nsize(0))); temp = 0
    is = 1
    do ipe = 0,lmpi_nproc-1
       j = cd_pp_nsize(ipe)
       ie = (is + j)-1
       if (lmpi_id == ipe) temp(1:j) = cd_metis_data(1:j)
       call lmpi_bcast(temp,ipe)
       if (c_my_nsize > 0) then
          if ((c_my_nhead <= ie).and.(c_my_ntail >= is)) then
             c = max(c_my_nhead, is) - (cd_pp_nhead(ipe)-1)
             d = min(c_my_ntail, ie) - (cd_pp_nhead(ipe)-1)
             a = max(c_my_nhead, is) - (c_my_nhead-1)
             b = min(c_my_ntail, ie) - (c_my_nhead-1)
             c_metis_data(a:b) = temp(c:d)
            !write(3100+lmpi_id,'("c_metis_data(",i0,":",i0,                   &
            !  ") = cd_metis_data(",i0,":",i0,")")')a,b,c,d
          end if
       end if
       is = ie + 1
    end do
    deallocate(temp)
    deallocate(cd_metis_data)
    if (allocated(cd_pp_nsize)) deallocate(cd_pp_nsize)
    if (allocated(cd_pp_nhead)) deallocate(cd_pp_nhead)
    if (allocated(cd_pp_ntail)) deallocate(cd_pp_ntail)

    ierr = 0
    do i = 1,c_my_nsize
      !write(3300+lmpi_id,*) c_my_nhead+i-1,c_metis_data(i)
       if (c_metis_data(i) == -1) ierr = 1
    end do
    call lmpi_conditional_stop(ierr,'Internal error. Uninitialized data.')

    deallocate(tadj, tadjncy)

    ! Populate metis_data for dof not in line and line root dof.

    allocate(metis_data(pp_nsize(lmpi_id))); metis_data = -1
    do i = 1,size(map_back)
      metis_data(map_back(i)) = c_metis_data(i)
    end do
    deallocate(map_back)

    ! Populate metis_data for dof in line.

    allocate( endline_check(  n_lines)); endline_check   = 0

    allocate( temp_proc(       n_lines)); temp_proc        = -1
    allocate( temp_proc_global(n_lines)); temp_proc_global = -1

    do iline = 1, n_lines
      if ( lmpi_id == line_pid(iline) ) then
        temp_proc(iline) = c_metis_data( line_dof(iline) )
      endif
    enddo

    call lmpi_max( temp_proc, temp_proc_global )
    call lmpi_bcast( temp_proc_global )

    myi = my_s-1
    do i = 1,dof0
       myi = myi + 1
       if ( tag_in_line(myi) == 0 ) cycle
       call find_line( myi, iline, ierr, n_pts, pts, which_line, tag_in_line )
       if ( ierr /= 0 ) exit
       metis_data(i) = temp_proc_global(iline)
       endline_check(iline) = endline_check(iline) + 1
    end do
    call lmpi_conditional_stop(ierr,'metis_data:partition_dof_lines')
    if (allocated(tag_in_line)) deallocate(tag_in_line)

    ! Populate metis for twod points not in a line.

    if ( twod .and. twod_point_reduction ) then
      call set_off_plane( my_s, my_e, dof0, dofg, adj, adjncy,                 &
                          solved_dof, metis_data )
    endif
    if (allocated(solved_dof)) deallocate(solved_dof)
    deallocate(c_metis_data)

    ! Check number of assignments on each line with endline.

    ierr = 0
    do i=1,n_lines
      call lmpi_reduce(endline_check(i),j)
      call lmpi_bcast(j)
      ierr = endline(i) - j
      call lmpi_conditional_stop(ierr,'endline_check:partition_dof_lines')
    enddo

    deallocate( line_pid, line_dof )
    deallocate( temp_proc, temp_proc_global )
    deallocate( root_dof, endline_check )
    deallocate( which_line, pts )

    ! Ensure all values are set.

    ierr = 0
    do i = 1,size(metis_data)
      if ( metis_data(i) == -1 ) then
        ierr = 1
        exit
      endif
    end do
    call lmpi_conditional_stop(ierr,'metis_data not set:partition_dof_lines')

    if ( lmpi_master ) write(*,*) ' lines_check_objects.'

    call lines_check_objects( ideal_objects )

    if ( lmpi_master ) write(*,*) ' ideal_objects=',ideal_objects

    if ( lmpi_master ) write(*,*) ' check_partition_lines.'

    call check_partition_lines(n_lines,line,endline)

    invoke_lines_check_islands = .true.
    if (  hanim ) then
      if ( ideal_objects ) invoke_lines_check_islands = .false.
      if ( twod .and. reduced_dofg /= n_lines ) &
      invoke_lines_check_islands = .false.
    else
       write(*,*)"SKIP lines_check_islands -- if singleton is done."
       write(*,*)"NOTE lines_check_islands does not just CHECK."
    end if

    if ( invoke_lines_check_islands ) then

      call lines_check_islands(size(adj),size(adjncy),adj,adjncy,grid%project)

    endif

    if (allocated(line_adj)) then
       do i = 1,size(line_adj)
          if (associated(line_adj(i)%entries)) deallocate(line_adj(i)%entries)
          if (associated(line_adj(i)%entries_line))                            &
             deallocate(line_adj(i)%entries_line)
       end do
       deallocate(line_adj)
    end if

    call my_metis_check(size(metis_data),metis_data)

    if ( partitioning_only ) then
       call lmpi_reduce(pp_nsize(lmpi_id),dofg)
       call my_metis_out(dofg,grid%project)
       call lmpi_conditional_stop(1,&
       'Stopping after writing project.metisout:partition_dof_lines.')
    end if

! solved_dof line_adj tag_in_line counts  node_weight
! c_pp_nsize c_pp_nhead c_pp_ntail
! cd_pp_nsize cd_pp_nhead cd_pp_ntail
! c_tadj c_tadjncy

#else
    type(grid_type),         intent(in) :: grid
    integer,                 intent(in) :: n_pts, n_lines
    integer, dimension(:,:), intent(in) :: line
    integer, dimension(:),   intent(in) :: endline

  continue

! if the solver was run on a single processor, set the part vector to
! identity and simply return; no need to call ParMetis

    if (lmpi_nproc == 1) then
      if (.not.allocated(metis_data)) allocate(metis_data(grid%nnodesg))
      metis_data = 1
      deallocate(adj)
      deallocate(adjncy)
    else
      write(*,*) 'Solver run on more than one processor, but ParMetis has not'
      write(*,*) 'been linked against - exiting.'
      if (.false.)                                                             &
      write(*,*) 'Suppress compiler warning ',n_pts,n_lines,                   &
                  size(line,1),size(endline)
      call lmpi_die
    end if
#endif

  end subroutine partition_dof_lines

#ifdef HAVE_PARMETIS

!============================== DEBUG_ADJ ====================================80
!
!=============================================================================80

  subroutine debug_adj(iounit, nhead, nsize, ntadj, ntadjncy, tadj, tadjncy,   &
                       node_weight)

    use sort, only : small_sort

    integer,                      intent(in) :: iounit
    integer,                      intent(in) :: nhead  ! global tadj number
    integer,                      intent(in) :: nsize  ! ntadj-1
    integer,                      intent(in) :: ntadj, ntadjncy
    integer, dimension(ntadj),    intent(in) :: tadj
    integer, dimension(ntadjncy), intent(in) :: tadjncy
    integer, dimension(nsize),    intent(in), optional :: node_weight

    integer :: i, j, k
    integer, dimension(:), allocatable :: temp1, nw

  continue

    if (nsize > 0) then
       allocate(nw(nsize)); nw = 0
       if (present(node_weight)) then
          nw = node_weight
       else
          nw = 1
       end if
       do i = 1,nsize
          j = tadj(i+1)-tadj(i)
          k = (nhead + i)-1
          if (j > 1) then
             allocate(temp1(j))
             temp1 = tadjncy(tadj(i):tadj(i+1)-1)
             call small_sort(j,temp1)
             write(iounit+lmpi_id,'(3(1x,i0)," :: ",500(i0,1x))')              &
               k,nw(i),tadj(i+1)-tadj(i),temp1(1:j)
             deallocate(temp1)
          else if (j == 1) then
             write(iounit+lmpi_id,'(3(1x,i0)," :: ",500(i0,1x))')              &
               k,nw(i),1,tadjncy(tadj(i))
          else
             write(iounit+lmpi_id,'(3(1x,i0)," :: ",500(i0,1x))') k,0,0
          end if
       end do
       deallocate(nw)
    end if

  end subroutine debug_adj

!============================== SET_LINE_STRUCTURES ==========================80
!
! Set line data structures.
!
!=============================================================================80

  subroutine set_line_structures( n_pts, n_lines, line, endline,               &
                                  my_s, my_e, root_dof, line_pid,              &
                                  pts, deleted_dof, which_line, tag_in_line )

    use sort,        only : heap_sort, binary_search

    integer,                 intent(in) :: n_pts, n_lines, my_s, my_e
    integer, dimension(:,:), intent(in) :: line
    integer, dimension(:),   intent(in) :: endline

    integer(system_i1), dimension(:), intent(inout) :: tag_in_line
    integer,            dimension(:), intent(inout) :: root_dof, line_pid
    integer,            dimension(:), intent(inout) :: pts, deleted_dof
    integer,            dimension(:), intent(inout) :: which_line

    integer :: i, j, k, ict, dof, lines_on_me, ierr, n_deleted_dof

    integer, dimension(20) :: duplicate

  continue

    ierr = 0

    ! Convert line data structure to pts and order it.

    ict = 0
    do i = 1,n_lines
      do j = 1,endline(i)
        ict      = ict + 1
        pts(ict) = line(i,j)
      end do
    end do

    call heap_sort( n_pts, pts )

    ! Test if any point is in more than one line.

    do i = 1,n_pts-1
      if ( pts(i) < pts(i+1) ) cycle
      if ( lmpi_master ) write(*,"(1x,a,i10,2i20)") &
      ' Duplicate dof in line set..i,pts(i),pts(i+1)=',i,pts(i),pts(i+1)
      ierr = ierr + 1
      duplicate(ierr) = pts(i)
      if ( ierr >= 20 ) exit
    end do
    k = ierr
    do ierr=1,k
      if ( lmpi_master ) write(*,*)
      if ( lmpi_master ) write(*,"(1x,a,i10,a,i10)") &
      ' ierr=',ierr,' global index of duplicate=',duplicate(ierr)
      do i=1,n_lines
        do j = 1,endline(i)
          if ( line(i,j) == duplicate(ierr) ) then
            if ( lmpi_master ) write(*,"(1x,4(a,i10))") &
            ' line=',i,' of',n_lines,' position=',j,' of ',endline(i)
          endif
        end do
      end do
    enddo
    call lmpi_conditional_stop(k,'duplicate points in line:set_line_structures')

    if ( lmpi_master ) write(*,*) ' ...converted lines to ordered pts array'

    ! Associate each dof in the sorted pts array with a line.

    do i = 1,n_lines
      do j = 1,endline(i)
        k = binary_search( n_pts, pts, line(i,j) )
        if ( k > 0 ) which_line(k) = i
      end do
    end do

    ! Tag all points in any line.

    do j = 1,n_pts
      tag_in_line( pts(j) ) = 1
    end do

    if ( lmpi_master ) write(*,*) ' ...set which_line and tag_in_line arrays'

    ! Select processor onto which line will be collapsed.

    line_on_proc : do i = 1,n_lines
      root_dof(i) = line(i,1)
    enddo line_on_proc

    ! Set deleted_dof array.

    n_deleted_dof = 0
    do i = 1,n_lines
      dof = root_dof(i)
      do j = 1,endline(i)
        if ( line(i,j) == dof ) cycle
        n_deleted_dof = n_deleted_dof + 1
        deleted_dof(n_deleted_dof) = line(i,j)
      end do
    end do

    ierr = n_deleted_dof - size(deleted_dof,1)
    call lmpi_conditional_stop(ierr,'deleted_dof:set_line_structures')

    call heap_sort( n_lines, root_dof )

    ! Set processor of root dof and associated processor.

    lines_on_me = 0
    do i = 1,n_lines
      dof = root_dof(i)
      if ( (dof >= my_s).and.(dof <= my_e) ) then
        lines_on_me = lines_on_me + 1
        line_pid(i) = lmpi_id
      endif
      if ( dof > my_e ) exit
    end do

    call lmpi_reduce(lines_on_me,ierr) ; call lmpi_bcast(ierr)
    ierr = ierr - n_lines
    call lmpi_conditional_stop(ierr,'line count error:set_line_structures')

    call heap_sort(n_deleted_dof,deleted_dof)

  end subroutine set_line_structures

!============================== UPDATE_LINE_ADJ ==============================80
!
! Update line adjacency.
!
!=============================================================================80

  subroutine update_line_adj( entry, entry_line, line_adj )

    use allocations, only : my_realloc_ptr

    integer,             intent(in)    :: entry, entry_line
    type(line_adj_type), intent(inout) :: line_adj

    integer :: n, i

    logical :: found

  continue

    n     = line_adj%n_entries
    found = .false.

    if ( n > 0 .and. entry_line > 0 ) then
      do i = 1,n
        if ( line_adj%entries_line(i) == 0 ) cycle
        if ( line_adj%entries_line(i) /= entry_line ) cycle
        found = .true. ; exit
      enddo
    endif

    if ( found ) return

    if ( n > 0 ) then
      do i = 1,n
        if ( line_adj%entries(i)  /= entry ) cycle
        found = .true. ; exit
      enddo
    endif

    if ( found ) return

    n                        = n + 1
    line_adj%n_entries       = n
    line_adj%entries(n)      = entry
    line_adj%entries_line(n) = entry_line

    if ( n == size( line_adj%entries,1) ) then
      call my_realloc_ptr( line_adj%entries,      2*n )
      call my_realloc_ptr( line_adj%entries_line, 2*n )
    endif

  end subroutine update_line_adj

!============================== FIND_LINE ====================================80
!
! Find line number if within a line.
!
!=============================================================================80

  subroutine find_line( dof, iline, ierr,                                      &
                        n_pts, pts, which_line, tag_in_line )

    use sort, only : binary_search

    integer,                          intent(in)  :: n_pts, dof
    integer,                          intent(out) :: iline, ierr

    integer(system_i1), dimension(:), intent(in) :: tag_in_line
    integer,            dimension(:), intent(in) :: pts, which_line

    integer :: k

  continue

    ierr  = 0 ; iline = 0

    if ( tag_in_line(dof) == 0 ) then
      ierr = 10 ; return
    endif

    k = binary_search( n_pts, pts, dof )
    if ( k <= 0 ) then
      ierr = 1
    else
      iline = which_line( k )
    endif

  end subroutine find_line

!============================== SET_SOLVED_DOF ===============================80
!
! Set global solved_dof flag (twod).
!
!=============================================================================80

  subroutine set_solved_dof( dof0, dofg, reduced_dofg, y, solved_dof, &
                             twod_point_reduction )

    use kinddefs,    only : dp
    use twod_util,   only : yplane_2d, y_coplanar_tol

    integer,                intent(in)    :: dof0, dofg
    integer,                intent(out)   :: reduced_dofg
    real(dp), dimension(:), intent(in)    :: y
    logical,  dimension(:), intent(inout) :: solved_dof
    logical,                intent(inout) :: twod_point_reduction

    integer :: i, j, n_local, ierr, ipe, n_global

    logical, dimension(:), allocatable :: on_plane
    logical, dimension(:), allocatable :: temp_logical

    integer, dimension(lmpi_nproc) :: lp, lpr, dofp, dofpr

  continue

    ! Check if ParMetis capable to support point reduction.
    ! (i.e., at least one solved dof must be on every processor).

    reduced_dofg = dofg/2
    lp = 0
    dofp = 0
    do j = 1,dof0
      if ( abs( y(j) - yplane_2d  ) < y_coplanar_tol ) then
        lp(lmpi_id+1) = 1
        dofp(lmpi_id+1) = dofp(lmpi_id+1) + 1
       !exit
      endif
    enddo
    call lmpi_reduce(lp,lpr) ; call lmpi_bcast(lpr)
    call lmpi_reduce(dofp,dofpr) ; call lmpi_bcast(dofpr)

    if ( lmpi_master ) then
      write(*,*)
      write(*,*) ' Before partitioning 2D NC (twod) dof are as follows:'
      write(*,*) ' Non-zero number of dof on each processor:'
      write(*,'(1x,30000(1x,i0))') lpr
      write(*,*) ' Number of dof on each processor:'
      write(*,'(1x,30000(1x,i0))') dofpr
    endif

    if ( sum(lpr) /= lmpi_nproc ) return

    twod_point_reduction = .true.

    ! Set on_plane for dofs to be solved.

    allocate(on_plane(dof0))  ; on_plane  = .true.

    reduced_dofg = 0
    i = 0
    do j = 1,dof0
      if ( abs( y(j) - yplane_2d  ) < y_coplanar_tol ) then
        i = i + 1
      else
        on_plane(j) = .false.
      endif
    enddo
    call lmpi_reduce(i,reduced_dofg)

    ierr = 0
    if ( lmpi_master ) ierr = dofg/2 - reduced_dofg
    call lmpi_conditional_stop(ierr,'2D NC (twod) error:set_solved_dof')

    n_global = 0
    construct_solved_dof : do ipe = 0, lmpi_nproc - 1

      n_local = 0
      if ( ipe == lmpi_id ) n_local = dof0

      call lmpi_bcast(n_local,ipe)

      allocate( temp_logical( n_local) )

      if ( ipe == lmpi_id ) then
        do i=1,n_local
          temp_logical(i)  = on_plane(i)
        enddo
      endif
      call lmpi_bcast(temp_logical, ipe)

      do i=1,n_local
        solved_dof(n_global+i) = temp_logical(i)
      enddo

      deallocate( temp_logical )

      n_global = n_global + n_local

    enddo construct_solved_dof

    ierr = n_global - dofg
    call lmpi_conditional_stop(ierr,'globals not set:set_solved_dof')

    deallocate( on_plane )

  end subroutine set_solved_dof

!============================== CHECK_PARTITIONING ===========================80
!
! Check dof within a partition.
!
!=============================================================================80

  subroutine check_partitioning( my_ct, site )

    integer,          intent(in) :: my_ct
    character(len=*), intent(in) :: site

    integer, dimension(lmpi_nproc) :: lp, lpr, dofp, dofpr

    integer :: ip

  continue

    lp = 0
    dofp = 0
    do ip=1,lmpi_nproc
      if ( ip - 1 /= lmpi_id ) cycle
      if ( my_ct > 0 ) lp(ip) = 1
      dofp(ip) = my_ct
    enddo

    call lmpi_reduce(lp,lpr)     ; call lmpi_bcast(lpr)
    call lmpi_reduce(dofp,dofpr) ; call lmpi_bcast(dofpr)

    if ( lmpi_master ) then
      write(*,*)
      write(*,'(1x,3a)') &
      ' Distribution of dof is as follows at site : ',trim(site)
      write(*,*) &
      ' Non-zero number of dof on each processor:'
      write(*,'(1x,30000(1x,i0))') lpr
      write(*,*) &
      ' Number of dof on each processor:'
      write(*,'(1x,30000(1x,i0))') dofpr
    endif

  end subroutine check_partitioning

!============================== SET_OFF_PLANE ================================80
!
! Set metis flag for off-plane points not in a line (twod).
!
!=============================================================================80

  subroutine set_off_plane( my_s, my_e, dof0, dofg, adj, adjncy, &
                            solved_dof, metis_data )

    integer, intent(in) :: my_s, my_e, dof0, dofg

    integer,            dimension(:), intent(in)    :: adj, adjncy
    logical,            dimension(:), intent(in)    :: solved_dof
    integer,            dimension(:), intent(inout) :: metis_data

    integer :: i, j, ipe, myi, dof_adj
    integer :: n_set, n_off, n_off_plane, ierr

    integer, dimension(:), allocatable :: dof_off_plane
    integer, dimension(:), allocatable :: pid_off_plane

    integer, dimension(:), allocatable :: temp_dof, temp_pid

  continue

    ierr = 0

    ! Find number of off-plane entries.

    n_off_plane = 0
    myi   = my_s-1
    do i = 1,dof0
      myi = myi + 1
      if ( .not.solved_dof(myi) ) cycle
      do j = adj(i),adj(i+1)-1
        dof_adj = adjncy(j)
        if ( solved_dof(dof_adj) ) cycle
        n_off_plane = n_off_plane + 1
      enddo
    enddo

    i = n_off_plane ; call lmpi_reduce(i, j)
    if ( lmpi_master ) ierr = j - dofg/2
    call lmpi_conditional_stop(ierr,'off_plane accounting:set_off_plane')

    allocate(dof_off_plane(max(1,n_off_plane)))  ; dof_off_plane  = 0
    allocate(pid_off_plane(max(1,n_off_plane)))  ; pid_off_plane  = -1

    ! Set off-plane entries.

    n_off_plane = 0
    myi = my_s-1
    do i = 1,dof0
      myi = myi + 1
      if ( .not.solved_dof(myi) ) cycle
      do j = adj(i),adj(i+1)-1
        dof_adj = adjncy(j)
        if ( solved_dof(dof_adj) ) cycle
        n_off_plane = n_off_plane + 1
        dof_off_plane(n_off_plane) = dof_adj
        pid_off_plane(n_off_plane) = metis_data(i)
      enddo
    enddo

    do i = 1,n_off_plane
      if ( dof_off_plane(i) <= 0 .or. dof_off_plane(i) > dofg ) ierr = 1
    enddo
    call lmpi_conditional_stop(ierr,'off-plane dof:set_off_plane')

    do i = 1,n_off_plane
      if ( pid_off_plane(i) == -1 ) ierr = 1
      if ( pid_off_plane(i) > lmpi_nproc ) ierr =1
    enddo
    call lmpi_conditional_stop(ierr,'off-plane pid:set_off_plane')

    n_set = 0
    exchange : do ipe = 0, lmpi_nproc - 1

      n_off = 0

      if ( ipe == lmpi_id ) n_off = n_off_plane

      call lmpi_bcast(n_off,ipe)

      if ( n_off == 0 ) cycle

      allocate( temp_dof( n_off) )
      allocate( temp_pid( n_off) )
      if ( ipe == lmpi_id ) then
        do j=1,n_off
          temp_dof(j) = dof_off_plane(j)
          temp_pid(j) = pid_off_plane(j)
        enddo
      endif
      call lmpi_bcast(temp_dof, ipe)
      call lmpi_bcast(temp_pid, ipe)

      do j=1,n_off

        if ( temp_dof(j) < my_s .or. temp_dof(j) > my_e ) cycle

        myi = my_s-1
        brute_force: do i = 1,dof0
          myi = myi + 1
          if ( myi == temp_dof(j) ) then
            n_set = n_set + 1

            metis_data(i) = temp_pid(j)
            exit brute_force
          endif
        enddo brute_force

      enddo

      deallocate( temp_dof, temp_pid )

    enddo exchange

    i = n_set ; call lmpi_reduce(i,n_set)
    if ( lmpi_master ) ierr = n_set - dofg/2
    call lmpi_conditional_stop(ierr,'off_plane not set:set_off_plane')

    deallocate( dof_off_plane, pid_off_plane )

  end subroutine set_off_plane

!============================== TWOD_ALL_LINES ===============================80
!
! Partition for special case - twod and all lines.
!
!=============================================================================80

  subroutine twod_all_lines( my_cts, n_lines, my_ct,                           &
                             adj_ct, tadj, tadjncy, temp_metis_data )

    integer,               intent(in)    :: n_lines, my_ct
    integer, dimension(:), intent(in)    :: my_cts, adj_ct, tadj, tadjncy
    integer, dimension(:), intent(inout) :: temp_metis_data

    integer :: i, ii, iii, m, ipe
    integer :: lines_proc, remainder, extras
    integer :: start, next, next_start

  continue

    do i = 1,lmpi_nproc
      if (my_cts(i) > 0) ipe = i-1
    end do

    if ( lmpi_id == ipe ) then

      remainder  = n_lines - (n_lines/lmpi_nproc)*lmpi_nproc

      start = -1
      do i = 1,my_ct
        if ( adj_ct(i) > 1 ) cycle
        start = i
        exit
      end do

      extras = 0
      do i=1,lmpi_nproc

        extras = extras + 1
        lines_proc  = n_lines/lmpi_nproc
        if ( extras <= remainder ) lines_proc = lines_proc + 1

        do ii=1,lines_proc
           temp_metis_data(start) = i
           do iii=1,adj_ct(start)
             m = tadj(start) + iii -1
             next = tadjncy(m)
             if ( temp_metis_data(next) == 0 ) next_start = next
           enddo
           start = next_start
        enddo

      enddo

      write(*,*)
      write(*,*) ' Processor allocations(temp_metis_data):'
      write(*,'(8(1x,i10))') temp_metis_data(1:my_ct)

    endif

  end subroutine twod_all_lines
#endif

end module pparty_metis_lines_revised

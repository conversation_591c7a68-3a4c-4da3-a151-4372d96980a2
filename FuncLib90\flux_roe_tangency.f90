!================================= FLUX_ROE_TANGENCY =========================80
!
! This routine computes the fluxes using Roe's flux difference splitting
! and compute the contribution to the flux balance
!
! Note that this function uses primitive variables
!
! Simpler expression for flux given:
!         (1) "right" state is relected state
!         (2) no entropy fix and face_speed = 0.
!
!=============================================================================80

  pure function flux_roe_tangency( xnorm, ynorm, znorm, area, ql )

    use kinddefs,        only : dp

    use fluid,           only : gm1, gamma

    real(dp), intent(in) :: xnorm, ynorm, znorm, area

    real(dp), dimension(5), intent(in) :: ql

    real(dp), dimension(5)             :: flux_roe_tangency

    real(dp) :: rhol, ul, vl, wl, pressl
    real(dp) :: ubarl, c, c2l, term

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    ubarl  = xnorm*ul + ynorm*vl + znorm*wl

    c2l = gamma*pressl/rhol
    c   = sqrt( c2l + gm1*my_half*ubarl**2 )

    term = pressl + rhol*( ubarl**2 + c*ubarl )

    flux_roe_tangency(1) = my_0
    flux_roe_tangency(2) = area*xnorm*term
    flux_roe_tangency(3) = area*ynorm*term
    flux_roe_tangency(4) = area*znorm*term
    flux_roe_tangency(5) = my_0

  end function flux_roe_tangency

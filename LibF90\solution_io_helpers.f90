! data readers and writers used by solution module
!
! Note: junit used within is a complete hack -- couldn't
! figure out how to use the one from the parent module
! without introducing a circular dependency. --Bil

module solution_io_helpers

  use kinddefs,        only : dp

  implicit none

  private
  public  :: data_reader, data_writer, data_skipper

  interface data_reader

    module procedure data_reader_char80_0
    module procedure data_reader_char80_1

    module procedure data_reader_real_0
    module procedure data_reader_real_1
    module procedure data_reader_real_1_parallel
    module procedure data_reader_real_2
    module procedure data_reader_real_2_parallel
    module procedure data_reader_real_3

    module procedure data_reader_cmplx_0
    module procedure data_reader_cmplx_1
    module procedure data_reader_cmplx_1_parallel
    module procedure data_reader_cmplx_2
    module procedure data_reader_cmplx_2_parallel
    module procedure data_reader_cmplx_3

    module procedure data_reader_int_0
    module procedure data_reader_int_1
    module procedure data_reader_int_2

  end interface

  interface data_writer

    module procedure data_writer_char80_0
    module procedure data_writer_char80_1

    module procedure data_writer_real_0
    module procedure data_writer_real_1
    module procedure data_writer_real_1_parallel
    module procedure data_writer_real_2
    module procedure data_writer_real_2_parallel
    module procedure data_writer_real_3

    module procedure data_writer_cmplx_0
    module procedure data_writer_cmplx_1
    module procedure data_writer_cmplx_1_parallel
    module procedure data_writer_cmplx_2
    module procedure data_writer_cmplx_2_parallel
    module procedure data_writer_cmplx_3

    module procedure data_writer_int_0
    module procedure data_writer_int_1
    module procedure data_writer_int_2

  end interface

  integer :: junit
  integer, parameter :: w_u = 300
  integer, parameter :: r_u = 200

contains

!=============================== DATA_READER_REAL_0 ==========================80
!
! Reads a real(dp) scalar from a file
!
!=============================================================================80

  subroutine data_reader_real_0(iunit, data_desc, my_real, dummy)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,           optional, intent(in)  :: dummy
    character(len=256),          intent(in)  :: data_desc
    integer,                     intent(in)  :: iunit
    real(dp),                    intent(out) :: my_real

    character(len=256)                       :: data_desc_file
    integer                                  :: ierr, rank, rank_file
    real(dp)                                 :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 0

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_0'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_0'
        write(*,*) 'Data_descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    if (present(dummy)) then
      read(iunit) my_dummy
    else
      read(iunit) my_real
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      if (present(dummy)) then
        write(junit,*) my_dummy, '(dummy data skipped)'
      else
        write(junit,*) my_real
      end if
    end if

  end subroutine data_reader_real_0


!=============================== DATA_READER_REAL_1 ==========================80
!
! Reads a real(dp) vector from a file
!
!=============================================================================80

  subroutine data_reader_real_1(iunit, data_desc, my_real, beg, end, dummy,    &
                                local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    real(dp), dimension(:),          intent(out) :: my_real

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, beg1, end1
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    real(dp)                                     :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 1

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_1'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_1'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_1'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)

    if (present(dummy)) then
      read(iunit) (my_dummy,i=beg1,end1)
    else
      if (present(local_to_global)) then
        read(iunit) (my_real(local_to_global(i)),i=beg1,end1)
      else
        read(iunit) (my_real(i),i=beg1,end1)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          write(junit,*) my_dummy, '(dummy data skipped)'
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            write(junit,*) my_real(local_to_global(i))
          end do
        else
          do i=beg1,end1
            write(junit,*) my_real(i)
          end do
        end if
      end if
    end if

  end subroutine data_reader_real_1
  subroutine data_reader_real_1_parallel(iunit, data_desc, my_real, beg, end,&
                                fh, offset, assign_read_data )

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_read, lmpi_offset_kind, lmpi_max,       &
                                  lmpi_bcast
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    integer,                         intent(in)  :: fh
    integer(lmpi_offset_kind),       intent(inout) :: offset

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    real(dp), dimension(:),          intent(out) :: my_real
    logical, optional,               intent(in)  :: assign_read_data
    logical                                      :: assign_data

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, beg1, end1
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file
    integer                                      :: global_ierr

    real(dp) :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 1

    read_desc : if ( 0 < len_trim(data_desc) .and. &
                     ( lmpi_master .or. 0 == lmpi_io) ) then
      read(iunit,iostat=ierr) data_desc_file

      if ( 0 /= ierr ) then
        if (lmpi_master) then
          write(*,*) 'Error reading data descriptor: ', trim(data_desc)
          call se_flush()
        end if
      end if
      if (trim(data_desc_file) /= trim(data_desc)) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_1'
          write(*,*) 'Expected data descriptor: ', trim(data_desc)
          write(*,*) 'File     data descriptor: ', trim(data_desc_file)
          call se_flush()
        end if
      end if
    end if read_desc
    call lmpi_max(abs(ierr),global_ierr)
    call lmpi_bcast(global_ierr)
    if ( 0 /= global_ierr ) return

    read_size : if (lmpi_master .or. 0 == lmpi_io) then
      read(iunit) rank_file
      if (rank_file /= rank) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_1'
          write(*,*) 'Data descriptor    : ', trim(data_desc)
          write(*,*) 'Expected array rank: ', rank
          write(*,*) 'File     array rank: ', rank_file
          call se_flush()
        end if
        call lmpi_conditional_stop(1)
      end if

      read(iunit) (dims_file(i),i=1,rank)

      do i=1,rank
        if ( 0 == lmpi_io .and. dims_file(i) /= (end(i)-beg(i)+1) ) then
          ierr = 1
          if (lmpi_master) then
            write(*,*) 'Error in data_reader_real_1'
            write(*,*) 'Data descriptor              : ', trim(data_desc)
            write(*,*) 'Expected dimension for index : ',i,' : ',end(i)-beg(i)+1
            write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
            call se_flush()
          end if
          call lmpi_conditional_stop(1)
        end if
      end do

    end if read_size
    call lmpi_conditional_stop(0)

    beg1 = beg(1)
    end1 = end(1)

    assign_data = .true.
    if ( present( assign_read_data ) ) assign_data = assign_read_data

    if ( assign_data ) then
      if ( 0 == lmpi_io ) then
        read(iunit) (my_real(i),i=beg1,end1)
      else
        call lmpi_read(fh,offset,data_desc,my_real,beg,end)
      end if
    else
      if (lmpi_master .or. 0 == lmpi_io) read(iunit) (my_dummy,i=1,dims_file(1))
      my_dummy = 0.0_dp
      my_dummy = my_dummy
    end if

    if (pp_cmd_outformat) then
      call lmpi_bcast(data_desc_file)
      call lmpi_bcast(rank_file)
      call lmpi_bcast(dims_file)
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      do i=beg1,end1
        write(junit,*) my_real(i)
      end do
    end if

  end subroutine data_reader_real_1_parallel


!============================== DATA_READER_REAL_2 ===========================80
!
! Reads a 2 dimensional real(dp) array from a file
!
!=============================================================================80

  subroutine data_reader_real_2(iunit, data_desc, my_real, beg, end, dummy,    &
                                local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,  dimension(:),          intent(in)  :: beg, end

    real(dp), dimension(:,:),        intent(out) :: my_real

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, beg1, end1
    integer                                      :: beg2, end2
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    real(dp)                                     :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 2

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_2'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_2'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_2'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (present(dummy)) then
      read(iunit) ((my_dummy,i=beg1,end1),j=beg2,end2)
    else
      if (present(local_to_global)) then
        read(iunit) ((my_real(i,local_to_global(j)),i=beg1,end1),j=beg2,end2)
      else
        read(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_dummy, '(dummy data skipped)'
          end do
        end do
      else
        if (present(local_to_global)) then
         do j=beg2,end2
           write(junit,'(100(ES16.8,1x))') my_real(beg1:end1,local_to_global(j))
         end do
        else
          do j=beg2,end2
             write(junit,'(100(ES16.8,1x))') my_real(beg1:end1,j)
          end do
        end if
      end if
    end if

  end subroutine data_reader_real_2

  subroutine data_reader_real_2_parallel(iunit, data_desc, my_real, beg, end, &
                                         fh, offset, assign_read_data )

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_read, lmpi_offset_kind, lmpi_max,       &
                                  lmpi_bcast
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    integer,                         intent(in)  :: fh
    integer(lmpi_offset_kind),       intent(inout) :: offset

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,  dimension(:),          intent(in)  :: beg, end

    real(dp), dimension(:,:),        intent(out) :: my_real !DANACHECK
    logical, optional,               intent(in)  :: assign_read_data
    logical                                      :: assign_data

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, beg1, end1
    integer                                      :: beg2, end2
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file
    integer                                      :: global_ierr

    real(dp) :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 2

    read_desc : if ( 0 < len_trim(data_desc) .and. &
                     ( lmpi_master .or. 0 == lmpi_io) ) then
      read(iunit,iostat=ierr) data_desc_file
      if ( 0 /= ierr ) then
        if (lmpi_master) then
          write(*,*) 'Error reading data descriptor: ', trim(data_desc)
          call se_flush()
        end if
      end if
      if (trim(data_desc_file) /= trim(data_desc)) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_2'
          write(*,*) 'Expected data descriptor: ', trim(data_desc)
          write(*,*) 'File     data descriptor: ', trim(data_desc_file)
          call se_flush()
        end if
      end if
    end if read_desc
    call lmpi_max(abs(ierr),global_ierr)
    call lmpi_bcast(global_ierr)
    if ( 0 /= global_ierr ) return
    call lmpi_bcast(data_desc_file)

    read_size : if (lmpi_master .or. 0 == lmpi_io) then
      read(iunit) rank_file
      if (rank_file /= rank) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_2'
          write(*,*) 'Data descriptor    : ', trim(data_desc)
          write(*,*) 'Expected array rank: ', rank
          write(*,*) 'File     array rank: ', rank_file
          call se_flush()
        end if
        call lmpi_conditional_stop(1)
      end if

      read(iunit) (dims_file(i),i=1,rank)

      do i=1,rank
        if ( 0 == lmpi_io .and.  dims_file(i) /= (end(i)-beg(i)+1) ) then
          if (lmpi_master) then
            write(*,*) 'Error in data_reader_real_2'
            write(*,*) 'Data descriptor              : ', trim(data_desc)
            write(*,*) 'Expected dimension for index : ',i,' : ',end(i)-beg(i)+1
            write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
            call se_flush()
          end if
          call lmpi_conditional_stop(1)
        end if
      end do

    end if read_size
    call lmpi_conditional_stop(0)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    assign_data = .true.
    if ( present( assign_read_data ) ) assign_data = assign_read_data

    if ( assign_data ) then
      if ( 0 ==lmpi_io ) then
        read(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
      else
        call lmpi_read(fh,offset,data_desc,my_real,beg,end)
      end if
    else
      if (lmpi_master .or. 0 == lmpi_io)                                       &
        read(iunit) (my_dummy,i=1,dims_file(1)*dims_file(2))
      my_dummy = 0.0_dp
      my_dummy = my_dummy
    end if

    if (pp_cmd_outformat) then
      call lmpi_bcast(rank_file)
      call lmpi_bcast(dims_file)
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      do j=beg2,end2
        write(junit,'(100(ES16.8,1x))') my_real(beg1:end1,j)
      end do
    end if

  end subroutine data_reader_real_2_parallel


!============================== DATA_READER_REAL_3 ===========================80
!
! Reads a 3 dimensional real(dp) array from a file
!
!=============================================================================80

  subroutine data_reader_real_3(iunit, data_desc, my_real, beg, end, dummy,    &
                                local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,  dimension(:),          intent(in)  :: beg, end

    real(dp), dimension(:,:,:),      intent(out) :: my_real

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, k, beg1, end1
    integer                                      :: beg2, end2, beg3, end3
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    real(dp)                                     :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 3

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_3'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_3'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_3'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)
    beg3 = beg(3)
    end3 = end(3)

    if (present(dummy)) then
      read(iunit) (((my_dummy,i=beg1,end1),j=beg2,end2),k=beg3,end3)
    else
      if (present(local_to_global)) then
        read(iunit) (((my_real(i,j,local_to_global(k)),i=beg1,end1),           &
                                                   j=beg2,end2),k=beg3,end3)
      else
        read(iunit) (((my_real(i,j,k),i=beg1,end1),j=beg2,end2),k=beg3,end3)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          do j=beg2,end2
            do k=beg3,end3
              write(junit,*) my_dummy, '(dummy data skipped)'
            end do
          end do
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            do j=beg2,end2
              do k=beg3,end3
                write(junit,*) my_real(i,j,local_to_global(k))
              end do
            end do
          end do
        else
          do i=beg1,end1
            do j=beg2,end2
              do k=beg3,end3
                write(junit,*) my_real(i,j,k)
              end do
            end do
          end do
        end if
      end if
    end if

  end subroutine data_reader_real_3


! BEGIN COMPLEX

!=============================== DATA_READER_CMPLX_0 =========================80
!
! Reads a complex(dp) scalar from a file
!
!=============================================================================80

  subroutine data_reader_cmplx_0(iunit, data_desc, my_real, dummy)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,           optional, intent(in)  :: dummy
    character(len=256),          intent(in)  :: data_desc
    integer,                     intent(in)  :: iunit
    complex(dp),                 intent(out) :: my_real

    character(len=256)                       :: data_desc_file
    integer                                  :: ierr, rank, rank_file
    complex(dp)                              :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 0

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_real_0'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_0'
        write(*,*) 'Data_descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    if (present(dummy)) then
      read(iunit) my_dummy
    else
      read(iunit) my_real
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      if (present(dummy)) then
        write(junit,*) my_dummy, '(dummy data skipped)'
      else
        write(junit,*) my_real
      end if
    end if

  end subroutine data_reader_cmplx_0


!=============================== DATA_READER_CMPLX_1 =========================80
!
! Reads a complex(dp) vector from a file
!
!=============================================================================80

  subroutine data_reader_cmplx_1(iunit, data_desc, my_real, beg, end, dummy,   &
                                local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    complex(dp), dimension(:),       intent(out) :: my_real

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, beg1, end1
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    complex(dp)                                  :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 1

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_1'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_1'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx_1'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)

    if (present(dummy)) then
      read(iunit) (my_dummy,i=beg1,end1)
    else
      if (present(local_to_global)) then
        read(iunit) (my_real(local_to_global(i)),i=beg1,end1)
      else
        read(iunit) (my_real(i),i=beg1,end1)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          write(junit,*) my_dummy, '(dummy data skipped)'
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            write(junit,*) my_real(local_to_global(i))
          end do
        else
          do i=beg1,end1
            write(junit,*) my_real(i)
          end do
        end if
      end if
    end if

  end subroutine data_reader_cmplx_1

  subroutine data_reader_cmplx_1_parallel(iunit, data_desc, my_real, beg, end,&
                                fh, offset, assign_read_data )

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_read, lmpi_offset_kind, lmpi_max,       &
                                  lmpi_bcast
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    integer,                         intent(in)  :: fh
    integer(lmpi_offset_kind),       intent(inout) :: offset

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    complex(dp), dimension(:),          intent(out) :: my_real
    logical, optional,               intent(in)  :: assign_read_data
    logical                                      :: assign_data

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, beg1, end1
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file
    integer                                      :: global_ierr

    complex(dp) :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 1

    read_desc : if ( 0 < len_trim(data_desc) .and. &
                     ( lmpi_master .or. 0 == lmpi_io) ) then
      read(iunit,iostat=ierr) data_desc_file

      if ( 0 /= ierr ) then
        if (lmpi_master) then
          write(*,*) 'Error reading data descriptor: ', trim(data_desc)
          call se_flush()
        end if
      end if
      if (trim(data_desc_file) /= trim(data_desc)) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx_1'
          write(*,*) 'Expected data descriptor: ', trim(data_desc)
          write(*,*) 'File     data descriptor: ', trim(data_desc_file)
          call se_flush()
        end if
      end if
    end if read_desc
    call lmpi_max(abs(ierr),global_ierr)
    call lmpi_bcast(global_ierr)
    if ( 0 /= global_ierr ) return

    read_size : if (lmpi_master .or. 0 == lmpi_io) then
      read(iunit) rank_file
      if (rank_file /= rank) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx_1'
          write(*,*) 'Data descriptor    : ', trim(data_desc)
          write(*,*) 'Expected array rank: ', rank
          write(*,*) 'File     array rank: ', rank_file
          call se_flush()
        end if
        call lmpi_conditional_stop(1)
      end if

      read(iunit) (dims_file(i),i=1,rank)

      do i=1,rank
        if ( 0 == lmpi_io .and. dims_file(i) /= (end(i)-beg(i)+1) ) then
          ierr = 1
          if (lmpi_master) then
            write(*,*) 'Error in data_reader_cmplx_1'
            write(*,*) 'Data descriptor              : ', trim(data_desc)
            write(*,*) 'Expected dimension for index : ',i,' : ',end(i)-beg(i)+1
            write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
            call se_flush()
          end if
          call lmpi_conditional_stop(1)
        end if
      end do

    end if read_size
    call lmpi_conditional_stop(0)

    beg1 = beg(1)
    end1 = end(1)

    assign_data = .true.
    if ( present( assign_read_data ) ) assign_data = assign_read_data

    if ( assign_data ) then
      if ( 0 == lmpi_io ) then
        read(iunit) (my_real(i),i=beg1,end1)
      else
        call lmpi_read(fh,offset,data_desc,my_real,beg,end)
      end if
    else
      if (lmpi_master .or. 0 == lmpi_io) read(iunit) (my_dummy,i=1,dims_file(1))
      my_dummy = 0.0_dp
      my_dummy = my_dummy
    end if

    if (pp_cmd_outformat) then
      call lmpi_bcast(data_desc_file)
      call lmpi_bcast(rank_file)
      call lmpi_bcast(dims_file)
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      do i=beg1,end1
        write(junit,*) my_real(i)
      end do
    end if

  end subroutine data_reader_cmplx_1_parallel



!============================== DATA_READER_REAL_2 ===========================80
!
! Reads a 2 dimensional complex(dp) array from a file
!
!=============================================================================80

  subroutine data_reader_cmplx_2(iunit, data_desc, my_real, beg, end, dummy,   &
                                local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    complex(dp), dimension(:,:),     intent(out) :: my_real

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, beg1, end1
    integer                                      :: beg2, end2
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    complex(dp)                                  :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 2

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_2'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_2'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx_2'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (present(dummy)) then
      read(iunit) ((my_dummy,i=beg1,end1),j=beg2,end2)
    else
      if (present(local_to_global)) then
        read(iunit) ((my_real(i,local_to_global(j)),i=beg1,end1),j=beg2,end2)
      else
        read(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_dummy, '(dummy data skipped)'
          end do
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            do j=beg2,end2
              write(junit,*) my_real(i,local_to_global(j))
            end do
          end do
        else
          do i=beg1,end1
            do j=beg2,end2
              write(junit,*) my_real(i,j)
            end do
          end do
        end if
      end if
    end if

  end subroutine data_reader_cmplx_2

  subroutine data_reader_cmplx_2_parallel(iunit, data_desc, my_real, beg, end, &
                                         fh, offset, assign_read_data )

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_read, lmpi_offset_kind, lmpi_max,       &
                                  lmpi_bcast
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    integer,                         intent(in)  :: fh
    integer(lmpi_offset_kind),       intent(inout) :: offset

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,  dimension(:),          intent(in)  :: beg, end

    complex(dp), dimension(:,:),        intent(out) :: my_real
    logical, optional,               intent(in)  :: assign_read_data
    logical                                      :: assign_data

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, beg1, end1
    integer                                      :: beg2, end2
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file
    integer                                      :: global_ierr

    complex(dp) :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 2

    read_desc : if ( 0 < len_trim(data_desc) .and. &
                     ( lmpi_master .or. 0 == lmpi_io) ) then
      read(iunit,iostat=ierr) data_desc_file
      if ( 0 /= ierr ) then
        if (lmpi_master) then
          write(*,*) 'Error reading data descriptor: ', trim(data_desc)
          call se_flush()
        end if
      end if
      if (trim(data_desc_file) /= trim(data_desc)) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx_2'
          write(*,*) 'Expected data descriptor: ', trim(data_desc)
          write(*,*) 'File     data descriptor: ', trim(data_desc_file)
          call se_flush()
        end if
      end if
    end if read_desc
    call lmpi_max(abs(ierr),global_ierr)
    call lmpi_bcast(global_ierr)
    if ( 0 /= global_ierr ) return

    read_size : if (lmpi_master .or. 0 == lmpi_io) then
      read(iunit) rank_file
      if (rank_file /= rank) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx_2'
          write(*,*) 'Data descriptor    : ', trim(data_desc)
          write(*,*) 'Expected array rank: ', rank
          write(*,*) 'File     array rank: ', rank_file
          call se_flush()
        end if
        call lmpi_conditional_stop(1)
      end if

      read(iunit) (dims_file(i),i=1,rank)

      do i=1,rank
        if ( 0 == lmpi_io .and.  dims_file(i) /= (end(i)-beg(i)+1) ) then
          if (lmpi_master) then
            write(*,*) 'Error in data_reader_cmplx_2'
            write(*,*) 'Data descriptor              : ', trim(data_desc)
            write(*,*) 'Expected dimension for index : ',i,' : ',end(i)-beg(i)+1
            write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
            call se_flush()
          end if
          call lmpi_conditional_stop(1)
        end if
      end do

    end if read_size
    call lmpi_conditional_stop(0)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    assign_data = .true.
    if ( present( assign_read_data ) ) assign_data = assign_read_data

    if ( assign_data ) then
      if ( 0 ==lmpi_io ) then
        read(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
      else
        call lmpi_read(fh,offset,data_desc,my_real,beg,end)
      end if
    else
      if (lmpi_master .or. 0 == lmpi_io) &
        read(iunit) (my_dummy,i=1,dims_file(1)*dims_file(2))
      my_dummy = 0.0_dp
      my_dummy = my_dummy
    end if

    if (pp_cmd_outformat) then
      call lmpi_bcast(data_desc_file)
      call lmpi_bcast(rank_file)
      call lmpi_bcast(dims_file)
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      do i=beg1,end1
        do j=beg2,end2
          write(junit,*) my_real(i,j)
        end do
      end do
    end if

  end subroutine data_reader_cmplx_2_parallel



!============================== DATA_READER_CMPLX_3 ==========================80
!
! Reads a 3 dimensional complex(dp) array from a file
!
!=============================================================================80

  subroutine data_reader_cmplx_3(iunit, data_desc, my_real, beg, end, dummy,   &
                                local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    complex(dp), dimension(:,:,:),   intent(out) :: my_real

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, k, beg1, end1
    integer                                      :: beg2, end2, beg3, end3
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    complex(dp)                                  :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 3

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_3'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_cmplx_3'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_cmplx3'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)
    beg3 = beg(3)
    end3 = end(3)

    if (present(dummy)) then
      read(iunit) (((my_dummy,i=beg1,end1),j=beg2,end2),k=beg3,end3)
    else
      if (present(local_to_global)) then
        read(iunit) (((my_real(i,j,local_to_global(k)),i=beg1,end1),           &
                                                   j=beg2,end2),k=beg3,end3)
      else
        read(iunit) (((my_real(i,j,k),i=beg1,end1),j=beg2,end2),k=beg3,end3)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_dummy, '(dummy data skipped)'
          end do
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            do j=beg2,end2
              do k=beg3,end3
                write(junit,*) my_real(i,j,local_to_global(k))
              end do
            end do
          end do
        else
          do i=beg1,end1
            do j=beg2,end2
              do k=beg3,end3
                write(junit,*) my_real(i,j,k)
              end do
            end do
          end do
        end if
      end if
    end if

  end subroutine data_reader_cmplx_3

! END COMPLEX


!=============================== DATA_READER_INT_0 ===========================80
!
! Reads a integer scalar from a file
!
!=============================================================================80

  subroutine data_reader_int_0(iunit, data_desc, my_int, dummy)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,           optional, intent(in)  :: dummy

    character(len=256),          intent(in)  :: data_desc

    integer,                     intent(out) :: my_int
    integer,                     intent(in)  :: iunit

    character(len=256)                       :: data_desc_file

    integer                                  :: ierr
    integer                                  :: rank, rank_file

    integer                                  :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 0

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_int_0'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_int_0'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    if (present(dummy)) then
      read(iunit) my_dummy
    else
      read(iunit) my_int
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      if (present(dummy)) then
        write(junit,*) my_dummy, '(dummy data skipped)'
      else
        write(junit,*) my_int
      end if
    end if
  end subroutine data_reader_int_0


!=============================== DATA_READER_INT_1 ===========================80
!
! Reads a integer vector from a file
!
!=============================================================================80

  subroutine data_reader_int_1(iunit, data_desc, my_int, beg, end, dummy,      &
                               local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,    dimension(:),        intent(out) :: my_int
    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, beg1, end1
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    integer                                      :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 1

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_int_1'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_int_1'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_int_1'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)

    if (present(dummy)) then
      read(iunit) (my_dummy,i=beg1,end1)
    else
      if (present(local_to_global)) then
        read(iunit) (my_int(local_to_global(i)),i=beg1,end1)
      else
        read(iunit) (my_int(i),i=beg1,end1)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          write(junit,*) my_dummy, '(dummy data skipped)'
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            write(junit,*) my_int(local_to_global(i))
          end do
        else
          do i=beg1,end1
            write(junit,*) my_int(i)
          end do
        end if
      end if
    end if

  end subroutine data_reader_int_1


!============================== DATA_READER_INT_2 ============================80
!
! Reads a 2 dimensional integer array from a file
!
!=============================================================================80

  subroutine data_reader_int_2(iunit, data_desc, my_int, beg, end, dummy,      &
                               local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional, intent(in)  :: dummy

    integer, dimension(:), optional, intent(in)  :: local_to_global

    character(len=256),              intent(in)  :: data_desc

    integer,    dimension(:,:),      intent(out) :: my_int
    integer,                         intent(in)  :: iunit
    integer,     dimension(:),       intent(in)  :: beg, end

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, j, beg1, end1
    integer                                      :: beg2, end2
    integer                                      :: rank, rank_file
    integer, dimension(size(beg))                :: dims_file

    integer                                      :: my_dummy

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 2

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_int_2'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_int_2'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1) ) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_int_2'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (present(dummy)) then
      read(iunit) ((my_dummy,i=beg1,end1),j=beg2,end2)
    else
      if (present(local_to_global)) then
        read(iunit) ((my_int(i,local_to_global(j)),i=beg1,end1),j=beg2,end2)
      else
        read(iunit) ((my_int(i,j),i=beg1,end1),j=beg2,end2)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_dummy, '(dummy data skipped)'
          end do
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            do j=beg2,end2
              write(junit,*) my_int(i,local_to_global(j))
            end do
          end do
        else
          do i=beg1,end1
            do j=beg2,end2
              write(junit,*) my_int(i,j)
            end do
          end do
        end if
      end if
    end if

  end subroutine data_reader_int_2


!============================== DATA_READER_CHAR80_0 =========================80
!
! Reads a len=80 character string from a file
!
!=============================================================================80

  subroutine data_reader_char80_0(iunit, data_desc, my_char, dummy)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,           optional, intent(in)  :: dummy

    character(len=80),           intent(out) :: my_char
    character(len=256),          intent(in)  :: data_desc

    integer,                     intent(in)  :: iunit

    character(len=256)                       :: data_desc_file
    character(len=80)                        :: my_dummy

    integer                                  :: ierr
    integer                                  :: rank, rank_file

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 0

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_char80_0'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_char80_0'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    if (present(dummy)) then
      read(iunit) my_dummy
    else
      read(iunit) my_char
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      if (present(dummy)) then
        write(junit,*) trim(my_dummy), '(dummy data skipped)'
      else
        write(junit,*) trim(my_char)
      end if
    end if

  end subroutine data_reader_char80_0


!============================== DATA_READER_CHAR80_1 =========================80
!
! Reads a vector of len=80 character strings from a file
!
!=============================================================================80

  subroutine data_reader_char80_1(iunit, data_desc, my_char, beg, end, dummy,  &
                                  local_to_global)

    use lmpi,              only : lmpi_id, lmpi_master, lmpi_conditional_stop, &
                                  lmpi_die
    use info_depr,         only : pp_cmd_outformat
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    logical,               optional,  intent(in)  :: dummy

    integer, dimension(:), optional,  intent(in)  :: local_to_global

    character(len=80), dimension(:),  intent(out) :: my_char
    character(len=256),               intent(in)  :: data_desc

    integer,                          intent(in)  :: iunit
    integer,           dimension(:),  intent(in)  :: beg, end

    character(len=256)                            :: data_desc_file
    character(len=80)                             :: my_dummy

    integer                                       :: i, ierr, beg1, end1
    integer                                       :: rank, rank_file
    integer, dimension(size(beg))                 :: dims_file

  continue

    if (pp_cmd_outformat) junit = iunit+r_u+lmpi_id+1

    ierr = 0

    rank = 1

!   read data descriptor

    read(iunit,iostat=ierr) data_desc_file

    if ( 0 /= ierr ) then
      if (lmpi_master) then
        write(*,*) 'Error reading data descriptor: ', trim(data_desc)
        call se_flush()
      end if
      return
    end if

    if (trim(data_desc_file) /= trim(data_desc)) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_char_1'
        write(*,*) 'Expected data descriptor: ', trim(data_desc)
        write(*,*) 'File     data descriptor: ', trim(data_desc_file)
        call se_flush()
      end if
    end if

!   read rank

    read(iunit) rank_file

    if (rank_file /= rank) then
      ierr = 1
      if (lmpi_master) then
        write(*,*) 'Error in data_reader_char_1'
        write(*,*) 'Data descriptor    : ', trim(data_desc)
        write(*,*) 'Expected array rank: ', rank
        write(*,*) 'File     array rank: ', rank_file
        call se_flush()
      end if
    end if

!   read dimensions

    read(iunit) (dims_file(i),i=1,rank)

    do i=1,rank
      if ( dims_file(i) /= (end(i)-beg(i)+1)) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_char80_1'
          write(*,*) 'Data descriptor              : ', trim(data_desc)
          write(*,*) 'Expected dimension for index : ',i,' : ', end(i)-beg(i)+1
          write(*,*) 'File     dimension for index : ',i,' : ', dims_file(i)
          call se_flush()
        end if
      end if
    end do

    if (lmpi_io == 0) then
       call lmpi_conditional_stop(ierr)
    else
       if (ierr == 1) call lmpi_die()
    end if

    beg1 = beg(1)
    end1 = end(1)

    if (present(dummy)) then
      read(iunit) (my_dummy,i=beg1,end1)
    else
      if (present(local_to_global)) then
        read(iunit) (my_char(local_to_global(i)),i=beg1,end1)
      else
        read(iunit) (my_char(i),i=beg1,end1)
      end if
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc_file ',trim(data_desc_file)
      write(junit,*) 'data_rank_file ',rank_file
      write(junit,*) 'data_dims_file ',(dims_file(i),i=1,rank_file)
      if (present(dummy)) then
        do i=beg1,end1
          write(junit,*) trim(my_dummy), '(dummy data skipped)'
        end do
      else
        if (present(local_to_global)) then
          do i=beg1,end1
            write(junit,*) trim(my_char(local_to_global(i)))
          end do
        else
          do i=beg1,end1
            write(junit,*) trim(my_char(i))
          end do
        end if
      end if
    end if

  end subroutine data_reader_char80_1


!=============================== DATA_WRITER_REAL_0 ==========================80
!
! Writes a real(dp) scalar to a file
!
!=============================================================================80

  subroutine data_writer_real_0(iunit, data_desc, my_real)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),          intent(in) :: data_desc

    integer,                     intent(in) :: iunit

    real(dp),                    intent(in) :: my_real

    integer                                 :: rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 0

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

    write(iunit) my_real

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) my_real
    end if

  end subroutine data_writer_real_0


!=============================== DATA_WRITER_REAL_1 ==========================80
!
! Writes a real(dp) vector to a file
!
!=============================================================================80

  subroutine data_writer_real_1(iunit, data_desc, my_real, beg, end,           &
                                local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),               intent(in) :: data_desc

    integer,                          intent(in) :: iunit
    integer,  dimension(:),           intent(in) :: beg, end

    integer,  dimension(:), optional, intent(in) :: local_to_global

    real(dp), dimension(:),           intent(in) :: my_real

    integer                                      :: i, beg1, end1, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 1

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)

    if (present(local_to_global)) then
      write(iunit) (my_real(local_to_global(i)),i=beg1,end1)
    else
      write(iunit) (my_real(i),i=beg1,end1)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          write(junit,*) my_real(local_to_global(i))
        end do
      else
        do i=beg1,end1
          write(junit,*) my_real(i)
        end do
      end if
    end if

  end subroutine data_writer_real_1

  subroutine data_writer_real_1_parallel(iunit, data_desc, my_real, beg, end,  &
                                fh, offset)

    use lmpi,              only : lmpi_id, lmpi_offset_kind, lmpi_write,&
                                  lmpi_master, lmpi_reduce
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use info_depr,         only : pp_cmd_outformat

    character(len=256),               intent(in) :: data_desc

    integer,                          intent(in) :: iunit
    integer,  dimension(:),           intent(in) :: beg, end

    integer,                            intent(in) :: fh
    integer(lmpi_offset_kind),          intent(inout) :: offset

    real(dp), dimension(:),           intent(in) :: my_real

    integer                                      :: i, beg1, end1, rank
    integer                                      :: length

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 1

    write_array_size : if (0 == lmpi_io) then
      write(iunit) data_desc
      write(iunit) rank
      write(iunit) (end(i)-beg(i)+1,i=1,rank)
    else
      call lmpi_reduce(end(1)-beg(1)+1,length)
      if ( lmpi_master ) then
        write(iunit) data_desc
        write(iunit) rank
        write(iunit) length
      end if
    end if write_array_size

    beg1 = beg(1)
    end1 = end(1)

    if (0 == lmpi_io) then
      write(iunit) (my_real(i),i=beg1,end1)
    else
      call lmpi_write(fh,offset,data_desc,my_real,beg,end)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      do i=beg1,end1
        write(junit,*) my_real(i)
      end do
    end if

  end subroutine data_writer_real_1_parallel


!============================== DATA_WRITER_REAL_2 ===========================80
!
! Writes a 2 dimensional real(dp) array to a file
!
!=============================================================================80

  subroutine data_writer_real_2(iunit, data_desc, my_real, beg, end,           &
                                local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                 intent(in) :: data_desc

    integer,                            intent(in) :: iunit
    integer,  dimension(:),             intent(in) :: beg, end

    integer,  dimension(:),   optional, intent(in) :: local_to_global

    real(dp), dimension(:,:),           intent(in) :: my_real

    integer                                        :: i, j, beg1, end1
    integer                                        :: beg2, end2, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 2

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (present(local_to_global)) then
      write(iunit) ((my_real(i,local_to_global(j)),i=beg1,end1),j=beg2,end2)
    else
      write(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
         do j=beg2,end2
           write(junit,'(100(ES16.8,1x))') my_real(beg1:end1,local_to_global(j))
         end do
      else
         do j=beg2,end2
           write(junit,'(100(ES16.8,1x))') my_real(beg1:end1,j)
         end do
      end if
    end if

  end subroutine data_writer_real_2

  subroutine data_writer_real_2_parallel(iunit, data_desc, my_real, beg, end, &
                                         fh, offset)

    use lmpi,              only : lmpi_id, lmpi_offset_kind, lmpi_write,&
                                  lmpi_master, lmpi_reduce
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                 intent(in) :: data_desc

    integer,                            intent(in) :: iunit
    integer,  dimension(:),             intent(in) :: beg, end

    real(dp), dimension(:,:),           intent(in) :: my_real

    integer,                            intent(in) :: fh
    integer(lmpi_offset_kind),          intent(inout) :: offset

    integer                                        :: i, j, beg1, end1
    integer                                        :: beg2, end2, rank
    integer                                        :: length

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 2

    write_array_size : if (0 == lmpi_io) then
      write(iunit) data_desc
      write(iunit) rank
      write(iunit) (end(i)-beg(i)+1,i=1,rank)
    else
      call lmpi_reduce(end(2)-beg(2)+1,length)
      if ( lmpi_master ) then
        write(iunit) data_desc
        write(iunit) rank
        write(iunit) end(1)-beg(1)+1, length
      end if
    end if write_array_size

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (0 == lmpi_io) then
      write(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
    else
      call lmpi_write(fh,offset,data_desc,my_real,beg,end)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      do j=beg2,end2
         write(junit,'(100(ES16.8,1x))') my_real(beg1:end1,j)
      end do
    end if

  end subroutine data_writer_real_2_parallel


!============================== DATA_WRITER_REAL_3 ===========================80
!
! Writes a 3 dimensional real(dp) array to a file
!
!=============================================================================80

  subroutine data_writer_real_3(iunit, data_desc, my_real, beg, end,           &
                                local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                   intent(in) :: data_desc

    integer,                              intent(in) :: iunit
    integer,  dimension(:),               intent(in) :: beg, end

    integer,  dimension(:),     optional, intent(in) :: local_to_global

    real(dp), dimension(:,:,:),           intent(in) :: my_real

    integer                                          :: i, j, k, beg1, end1
    integer                                          :: beg2, end2, beg3, end3
    integer                                          :: rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 3

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)
    beg3 = beg(3)
    end3 = end(3)

    if (present(local_to_global)) then
      write(iunit) (((my_real(i,j,local_to_global(k)),i=beg1,end1),            &
                                                  j=beg2,end2),k=beg3,end3)
    else
      write(iunit) (((my_real(i,j,k),i=beg1,end1),j=beg2,end2),k=beg3,end3)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          do j=beg2,end2
            do k=beg3,end3
              write(junit,*) my_real(i,j,local_to_global(k))
            end do
          end do
        end do
      else
        do i=beg1,end1
          do j=beg2,end2
            do k=beg3,end3
              write(junit,*) my_real(i,j,k)
            end do
          end do
        end do
      end if
    end if

  end subroutine data_writer_real_3


! BEGIN COMPLEX

!============================== DATA_WRITER_CMPLX_0 ==========================80
!
! Writes a complex(dp) scalar to a file
!
!=============================================================================80

  subroutine data_writer_cmplx_0(iunit, data_desc, my_real)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),          intent(in) :: data_desc

    integer,                     intent(in) :: iunit

    complex(dp),                    intent(in) :: my_real

    integer                                 :: rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 0

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

    write(iunit) my_real

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) my_real
    end if

  end subroutine data_writer_cmplx_0


!=============================== DATA_WRITER_CMPLX_1 =========================80
!
! Writes a complex(dp) vector to a file
!
!=============================================================================80

  subroutine data_writer_cmplx_1(iunit, data_desc, my_real, beg, end,          &
                                local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                  intent(in) :: data_desc

    integer,                             intent(in) :: iunit
    integer,     dimension(:),           intent(in) :: beg, end

    integer,     dimension(:), optional, intent(in) :: local_to_global

    complex(dp), dimension(:),           intent(in) :: my_real

    integer                                         :: i, beg1, end1, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 1

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)

    if (present(local_to_global)) then
      write(iunit) (my_real(local_to_global(i)),i=beg1,end1)
    else
      write(iunit) (my_real(i),i=beg1,end1)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          write(junit,*) my_real(local_to_global(i))
        end do
      else
        do i=beg1,end1
          write(junit,*) my_real(i)
        end do
      end if
    end if

  end subroutine data_writer_cmplx_1
  subroutine data_writer_cmplx_1_parallel(iunit, data_desc, my_real, beg, end, &
                                fh, offset)

    use lmpi,              only : lmpi_id, lmpi_offset_kind, lmpi_write,&
                                  lmpi_master, lmpi_reduce
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use info_depr,         only : pp_cmd_outformat

    character(len=256),               intent(in) :: data_desc

    integer,                          intent(in) :: iunit
    integer,  dimension(:),           intent(in) :: beg, end

    integer,                            intent(in) :: fh
    integer(lmpi_offset_kind),          intent(inout) :: offset

    complex(dp), dimension(:),           intent(in) :: my_real

    integer                                      :: i, beg1, end1, rank
    integer                                      :: length

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 1

    write_array_size : if (0 == lmpi_io) then
      write(iunit) data_desc
      write(iunit) rank
      write(iunit) (end(i)-beg(i)+1,i=1,rank)
    else
      call lmpi_reduce(end(1)-beg(1)+1,length)
      if ( lmpi_master ) then
        write(iunit) data_desc
        write(iunit) rank
        write(iunit) length
      end if
    end if write_array_size

    beg1 = beg(1)
    end1 = end(1)

    if (0 == lmpi_io) then
      write(iunit) (my_real(i),i=beg1,end1)
    else
      call lmpi_write(fh,offset,data_desc,my_real,beg,end)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      do i=beg1,end1
        write(junit,*) my_real(i)
      end do
    end if

  end subroutine data_writer_cmplx_1_parallel


!============================= DATA_WRITER_CMPLX_2 ===========================80
!
! Writes a 2 dimensional real(dp) array to a file
!
!=============================================================================80

  subroutine data_writer_cmplx_2(iunit, data_desc, my_real, beg, end,          &
                                local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                  intent(in) :: data_desc

    integer,                             intent(in) :: iunit
    integer,     dimension(:),           intent(in) :: beg, end

    integer,     dimension(:), optional, intent(in) :: local_to_global

    complex(dp), dimension(:,:),         intent(in) :: my_real

    integer                                         :: i, j, beg1, end1
    integer                                         :: beg2, end2, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 2

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (present(local_to_global)) then
      write(iunit) ((my_real(i,local_to_global(j)),i=beg1,end1),j=beg2,end2)
    else
      write(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_real(i,local_to_global(j))
          end do
        end do
      else
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_real(i,j)
          end do
        end do
      end if
    end if

  end subroutine data_writer_cmplx_2

  subroutine data_writer_cmplx_2_parallel(iunit, data_desc, my_real, beg, end, &
                                         fh, offset)

    use lmpi,              only : lmpi_id, lmpi_offset_kind, lmpi_write,&
                                  lmpi_master, lmpi_reduce
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                 intent(in) :: data_desc

    integer,                            intent(in) :: iunit
    integer,  dimension(:),             intent(in) :: beg, end

    complex(dp), dimension(:,:),           intent(in) :: my_real

    integer,                            intent(in) :: fh
    integer(lmpi_offset_kind),          intent(inout) :: offset

    integer                                        :: i, j, beg1, end1
    integer                                        :: beg2, end2, rank
    integer                                        :: length

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 2

    write_array_size : if (0 == lmpi_io) then
      write(iunit) data_desc
      write(iunit) rank
      write(iunit) (end(i)-beg(i)+1,i=1,rank)
    else
      call lmpi_reduce(end(2)-beg(2)+1,length)
      if ( lmpi_master ) then
        write(iunit) data_desc
        write(iunit) rank
        write(iunit) end(1)-beg(1)+1, length
      end if
    end if write_array_size

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (0 == lmpi_io) then
      write(iunit) ((my_real(i,j),i=beg1,end1),j=beg2,end2)
    else
      call lmpi_write(fh,offset,data_desc,my_real,beg,end)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      do i=beg1,end1
        do j=beg2,end2
          write(junit,*) my_real(i,j)
        end do
      end do
    end if

  end subroutine data_writer_cmplx_2_parallel


!============================= DATA_WRITER_CMPLX_3 ===========================80
!
! Writes a 3 dimensional complex(dp) array to a file
!
!=============================================================================80

  subroutine data_writer_cmplx_3(iunit, data_desc, my_real, beg, end,          &
                                local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                  intent(in) :: data_desc

    integer,                             intent(in) :: iunit
    integer,     dimension(:),           intent(in) :: beg, end

    integer,     dimension(:), optional, intent(in) :: local_to_global

    complex(dp), dimension(:,:,:),       intent(in) :: my_real

    integer                                         :: i, j, k, beg1, end1
    integer                                         :: beg2, end2, beg3, end3
    integer                                         :: rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 3

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)
    beg3 = beg(3)
    end3 = end(3)

    if (present(local_to_global)) then
      write(iunit) (((my_real(i,j,local_to_global(k)),i=beg1,end1),            &
                                                  j=beg2,end2),k=beg3,end3)
    else
      write(iunit) (((my_real(i,j,k),i=beg1,end1),j=beg2,end2),k=beg3,end3)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          do j=beg2,end2
            do k=beg3,end3
              write(junit,*) my_real(i,j,local_to_global(k))
            end do
          end do
        end do
      else
        do i=beg1,end1
          do j=beg2,end2
            do k=beg3,end3
              write(junit,*) my_real(i,j,k)
            end do
          end do
        end do
      end if
    end if

  end subroutine data_writer_cmplx_3

! END COMPLEX


!=============================== DATA_WRITER_INT_0 ===========================80
!
! Writes a integer scalar from a file
!
!=============================================================================80

  subroutine data_writer_int_0(iunit, data_desc, my_int)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),          intent(in) :: data_desc

    integer,                     intent(in) :: iunit

    integer,                     intent(in)  :: my_int

    integer                                  :: rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 0

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

    write(iunit) my_int

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) my_int
    end if

  end subroutine data_writer_int_0


!=============================== DATA_WRITER_INT_1 ===========================80
!
! Writes a integer vector from a file
!
!=============================================================================80

  subroutine data_writer_int_1(iunit, data_desc, my_int, beg, end,             &
                               local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),              intent(in) :: data_desc

    integer,                         intent(in) :: iunit
    integer, dimension(:),           intent(in) :: beg, end

    integer, dimension(:), optional, intent(in) :: local_to_global

    integer, dimension(:),           intent(in) :: my_int

    integer                                     :: i, beg1, end1, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 1

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)

    if (present(local_to_global)) then
      write(iunit) (my_int(local_to_global(i)),i=beg1,end1)
    else
      write(iunit) (my_int(i),i=beg1,end1)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          write(junit,*) my_int(local_to_global(i))
        end do
      else
        do i=beg1,end1
          write(junit,*) my_int(i)
        end do
      end if
    end if

  end subroutine data_writer_int_1


!============================== DATA_WRITER_INT_2 ============================80
!
! Writes a 2 dimensional integer array from a file
!
!=============================================================================80

  subroutine data_writer_int_2(iunit, data_desc, my_int, beg, end,             &
                               local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),              intent(in) :: data_desc

    integer,                         intent(in) :: iunit
    integer, dimension(:),           intent(in) :: beg, end

    integer, dimension(:), optional, intent(in) :: local_to_global

    integer, dimension(:,:),         intent(in) :: my_int

    integer                                     :: i, j, beg1, end1
    integer                                     :: beg2, end2, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 2

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)
    beg2 = beg(2)
    end2 = end(2)

    if (present(local_to_global)) then
      write(iunit) ((my_int(i,local_to_global(j)),i=beg1,end1),j=beg2,end2)
    else
      write(iunit) ((my_int(i,j),i=beg1,end1),j=beg2,end2)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_int(i,local_to_global(j))
          end do
        end do
      else
        do i=beg1,end1
          do j=beg2,end2
            write(junit,*) my_int(i,j)
          end do
        end do
      end if
    end if

  end subroutine data_writer_int_2


!============================== DATA_WRITER_CHAR80_0 =========================80
!
! Writes a len=80 character string from a file
!
!=============================================================================80

  subroutine data_writer_char80_0(iunit, data_desc, my_char)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),          intent(in) :: data_desc

    integer,                     intent(in) :: iunit

    character(len=80),           intent(in) :: my_char

    integer  :: rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 0

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

    write(iunit) my_char

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) trim(my_char)
    end if

  end subroutine data_writer_char80_0


!============================== DATA_WRITER_CHAR80_1 =========================80
!
! Writes a vector of len=80 character strings from a file
!
!=============================================================================80

  subroutine data_writer_char80_1(iunit, data_desc, my_char, beg, end,         &
                                  local_to_global)

    use lmpi,              only : lmpi_id
    use info_depr,         only : pp_cmd_outformat

    character(len=256),                        intent(in) :: data_desc

    integer,                                   intent(in) :: iunit
    integer,           dimension(:),           intent(in) :: beg, end

    integer,           dimension(:), optional, intent(in) :: local_to_global

    character(len=80), dimension(:),           intent(in) :: my_char

    integer                                               :: i, beg1, end1, rank

  continue

    if (pp_cmd_outformat) junit = iunit+w_u+lmpi_id+1

    rank = 1

!   write data descriptor

    write(iunit) data_desc

!   write rank

    write(iunit) rank

!   write array dimensions

    write(iunit) (end(i)-beg(i)+1,i=1,rank)

    beg1 = beg(1)
    end1 = end(1)

    if (present(local_to_global)) then
      write(iunit) (my_char(local_to_global(i)),i=beg1,end1)
    else
      write(iunit) (my_char(i),i=beg1,end1)
    end if

    if (pp_cmd_outformat) then
      write(junit,*) 'data_desc ',trim(data_desc)
      write(junit,*) 'data_rank ',rank
      write(junit,*) 'data_dims ',(end(i)-beg(i)+1,i=1,rank)
      if (present(local_to_global)) then
        do i=beg1,end1
          write(junit,*) trim(my_char(local_to_global(i)))
        end do
      else
        do i=beg1,end1
          write(junit,*) trim(my_char(i))
        end do
      end if
    end if

  end subroutine data_writer_char80_1


  subroutine data_skipper(iunit, data_desc)

    use lmpi, only : lmpi_master, lmpi_max, lmpi_bcast
    use lmpi, only : lmpi_io => lmpi_mpiio_type
    use system_extensions, only : se_flush

    character(len=256),              intent(in)  :: data_desc

    integer,                         intent(in)  :: iunit

    character(len=256)                           :: data_desc_file

    integer                                      :: ierr, i, total
    integer                                      :: rank_file
    integer, dimension(7)                        :: dims_file
    integer                                      :: global_ierr

    real(dp) :: my_dummy

  continue

    ierr = 0

    read_desc : if ( 0 < len_trim(data_desc) .and. &
                     ( lmpi_master .or. 0 == lmpi_io) ) then
      read(iunit,iostat=ierr) data_desc_file
      if ( 0 /= ierr ) then
        if (lmpi_master) then
          write(*,*) 'Error reading data descriptor: ', trim(data_desc)
          call se_flush()
        end if
      end if
      if (trim(data_desc_file) /= trim(data_desc)) then
        ierr = 1
        if (lmpi_master) then
          write(*,*) 'Error in data_reader_real_2'
          write(*,*) 'Expected data descriptor: ', trim(data_desc)
          write(*,*) 'File     data descriptor: ', trim(data_desc_file)
          call se_flush()
        end if
      end if
    end if read_desc
    call lmpi_max(abs(ierr),global_ierr)
    call lmpi_bcast(global_ierr)
    if ( 0 /= global_ierr ) return

    read_size : if (lmpi_master .or. 0 == lmpi_io) then
      read(iunit) rank_file
      read(iunit) (dims_file(i),i=1,rank_file)
    end if read_size
    total = 1
    do i = 1, rank_file
      total = total*dims_file(i)
    end do
    if (lmpi_master .or. 0 == lmpi_io) &
        read(iunit) (my_dummy,i=1,total)
    my_dummy = 0.0_dp
    my_dummy = my_dummy

  end subroutine data_skipper

end module solution_io_helpers

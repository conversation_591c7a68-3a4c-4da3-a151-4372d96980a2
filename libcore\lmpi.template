! -*- f90 -*- this turns on the emacs f90 mode
! vi:syntax=fortran
!
! Run fortran_template.rb to generate lmpi.F90 from lmpi.template
! then check both this file and lmpi.F90 into the repository.
!     ruby fortran_template.rb lmpi

module lmpi

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs, only : dp, system_r4, system_r8, r8, &
                system_i1, system_i4, system_i8

  implicit none

  private

#ifdef HAVE_MPI
  include 'mpif.h'
#endif

!tempInsertInterface

  public :: lmpi_reduce_maxloc2               ! get the max of any processor
                                              ! and its location
  interface lmpi_reduce_maxloc2
    module procedure integer_reduce_maxloc2
    module procedure single_reduce_maxloc2
    module procedure double_reduce_maxloc2
    module procedure complex_single_reduce_maxloc2
    module procedure complex_double_reduce_maxloc2
  end interface

  public :: lmpi_read
  interface lmpi_read
    module procedure integer_scalar_read_master
    module procedure integer_vector_read
    module procedure double_scalar_read_master
    module procedure double_vector_read
    module procedure double_matrix_read
    module procedure cmpxr8_scalar_read_master
    module procedure cmpxr8_vector_read
    module procedure cmpxr8_matrix_read
  end interface

  public :: lmpi_write
  interface lmpi_write
    module procedure integer_scalar_write_master
    module procedure integer_vector_write
    module procedure double_scalar_write_master
    module procedure double_vector_write
    module procedure double_matrix_write
    module procedure cmpxr8_scalar_write_master
    module procedure cmpxr8_vector_write
    module procedure cmpxr8_matrix_write
  end interface

  public :: lmpi_max_and_maxid
  interface lmpi_max_and_maxid
    module procedure lmpi_max_and_maxid_int
    module procedure lmpi_max_and_maxid_r8
  end interface

  logical :: lmpi_started = .false.      ! mpi initialized

! Note: defaults are for sequential case

  integer :: lmpi_id       = 0           ! mpi processor id number
  integer :: lmpi_id_world = 0           ! mpi processor id number in comm_world
  integer :: lmpi_nproc    = 1           ! and total number of processes
  integer :: lmpi_comm     = 0           ! mpi communicaion world
  logical :: lmpi_master   = .true.      ! set in lmpi_start
  logical :: db            = .false.     ! Debugging statements for MPI IO
! integer, save :: write_count   = 0
! integer, save :: read_count    = 0
! integer :: jj

! MPI_IO related variables

  integer :: lmpi_mpiio_type = 2       ! If lmpi_io, then 1 MPI_IO, 2 stream
  logical :: lmpi_repart     = .false. ! Repartition with MPI_IO

#ifdef HAVE_MPI
  integer :: lmpi_mpiio_nblock
  integer :: lmpi_mpiio_nnodesg

  integer, dimension(:), allocatable :: lmpi_mpiio_cnt
  integer, dimension(:), allocatable :: lmpi_mpiio_put
  integer, dimension(:), allocatable :: lmpi_mpiio_get
  integer, dimension(:), allocatable :: lmpi_mpiio_ibuffer

  integer, parameter :: lmpi_mpiio_buffer_limit = 8388608 ! 8MB (bytes) ! 8000
  integer, parameter :: lmpi_mpiio_buffer_nelem = 1048576 ! 8MB/8 elems ! 1000
! integer, parameter :: lmpi_mpiio_buffer_limit = 8000    ! For testing
! integer, parameter :: lmpi_mpiio_buffer_nelem = 1000    ! For testing

  real(system_i8) :: wtime_at_start = 0.0_system_i8
#endif


! Flag to indicate if communicator has been split

  logical :: have_multiple_comms = .false.

  public :: lmpi_id, lmpi_nproc, lmpi_comm, lmpi_master, lmpi_repart
  public :: lmpi_id_world, lmpi_mpiio_type, have_multiple_comms
  public :: lmpi_started

! mpi work arrays that hold send and recive non-blocking communicaion requests

  integer, dimension(:), allocatable :: sendreq, recvreq

! mpi work arrays that hold send and recive non-blocking communicaion status

  public :: sendreq, recvreq

! Useful MPI constants and variables

#ifdef HAVE_MPI
  integer, parameter :: lmpi_success     = MPI_SUCCESS
  integer, parameter :: lmpi_error       = MPI_ERROR
  integer, parameter :: lmpi_status_size = MPI_STATUS_SIZE

  integer, parameter :: lmpi_tag         = MPI_TAG
  integer, parameter :: lmpi_source      = MPI_SOURCE
  integer, parameter :: lmpi_any_tag     = MPI_ANY_TAG
  integer, parameter :: lmpi_any_source  = MPI_ANY_SOURCE
  integer, parameter :: lmpi_comm_world  = MPI_COMM_WORLD
  integer, parameter :: lmpi_offset_kind = MPI_OFFSET_KIND

#else
  integer, parameter :: lmpi_success     = 1
  integer, parameter :: lmpi_error       = 0
  integer, parameter :: lmpi_status_size = 4

  integer, parameter :: lmpi_tag         = 100
  integer, parameter :: lmpi_source      = 100
  integer, parameter :: lmpi_any_tag     = -1000
  integer, parameter :: lmpi_any_source  = -1000
  integer, parameter :: lmpi_comm_world  = -1000
  integer, parameter :: lmpi_offset_kind = system_i8
#endif

  public :: LMPI_SUCCESS, LMPI_ERROR,   LMPI_STATUS_SIZE, LMPI_TAG,            &
            LMPI_SOURCE,  LMPI_ANY_TAG, LMPI_ANY_SOURCE,  LMPI_COMM_WORLD,     &
            LMPI_OFFSET_KIND

  contains

!============================= LMPI_CONDITONAL_STOP =========================80
!
! take one or zero as a an input. if any of the processors enter one then
! everyone stops (public)
!
!=============================================================================80

  subroutine lmpi_conditional_stop(istop_flag, error_message)

    use system_extensions, only : se_exit

    integer,                    intent(in) :: istop_flag
    character(len=*), optional, intent(in) :: error_message

    integer :: istop_total, i, ipe

    character(200) :: compare, message

    continue

    if (lmpi_started) then
      call lmpi_reduce(abs(istop_flag),istop_total)
      call lmpi_bcast(istop_total)
    else
      istop_total = abs(istop_flag)
    end if

    if ( istop_total > 0 ) then

      if (lmpi_started) then

        i = abs( istop_flag )
        call lmpi_max_and_maxid( i, ipe )
        call lmpi_bcast(ipe)

        message = 'blank'
        if ( present(error_message) ) then
          message        = error_message
        endif

        if ( lmpi_id == ipe ) then
         write(*,"(1x,a,i0,a,i0,a,i0)")&
          ' lmpi_conditional_stop, total-stop=', istop_total,&
          ' max-stop=', abs( istop_flag )
          write(*,"(1x,a,i0,2a)") 'lmpi_id=',lmpi_id,&
                                  ' Site=',trim(message)
        end if

        compare = message
        call lmpi_bcast(compare,ipe)

        if ( compare /= message ) then
          write(*,"(1x,a,i0,2a)") 'lmpi_id=',lmpi_id,&
                                  ' Site=',trim(message)
        endif

      else

        write(*,*)' lmpi_conditional_stop, total stop with ', istop_total
        if ( present(error_message) ) write(*,*) error_message

      endif

      if (lmpi_started) call lmpi_die
      call se_exit( 1 )

    endif

  end subroutine lmpi_conditional_stop


!====================== LMPI_MAX_AND_MAXID ===================================80
!
! Takes in the local value of a real-valued function and returns the maximum
! of these across all processors and which processor that global max
! occurred on.  The correct values are bcast'ed internally and
! therefore all processors should see the results of this routine.
!
!=============================================================================80

  subroutine lmpi_max_and_maxid_r8(value, processor)!tempProtectPrivate

    integer, intent(out) :: processor

    real(dp),    intent(in) :: value

    real(dp), dimension(2,1)    :: in, out

  continue

    in(1,1) = value
    in(2,1) = real(lmpi_id,dp)

    call lmpi_reduce_maxloc2( in, out )

    if (lmpi_master) then
      processor = nint(out(2,1))
    end if

#ifdef HAVE_MPI
    call lmpi_bcast(processor)
#endif

  end subroutine lmpi_max_and_maxid_r8


!====================== LMPI_MAX_AND_MAXID ===================================80
!
! Takes in the local value of a real-valued function and returns the maximum
! of these across all processors and which processor that global max
! occurred on.  The correct values are bcast'ed internally and
! therefore all processors should see the results of this routine.
!
!=============================================================================80

  subroutine lmpi_max_and_maxid_int(value, processor)!tempProtectPrivate

    integer, intent(out) :: processor

    integer,     intent(in) :: value

    integer,  dimension(2,1)    :: in, out

  continue

    in(1,1) = value
    in(2,1) = lmpi_id

    call lmpi_reduce_maxloc2( in, out )

    if (lmpi_master) processor = out(2,1)

#ifdef HAVE_MPI
    call lmpi_bcast(processor)
#endif

  end subroutine lmpi_max_and_maxid_int

!================================== LMPI_START ===============================80
!
! Starts up MPI processes (public)
!
!=============================================================================80

  subroutine lmpi_start(my_id,my_nproc)

    integer, optional, intent(out) :: my_id, my_nproc

#ifdef HAVE_MPI
    integer                        :: ierr
#endif

!  Note: lmpi_{id,nproc,comm,master} are module variables.

    continue

#ifdef HAVE_MPI
    lmpi_comm = mpi_comm_world

    if (.not. lmpi_started) then
      call mpi_init(ierr)

      init_error: if( ierr /= mpi_success )then
        print*, "mpi_init failed......"
      endif init_error

      lmpi_started = .true. ! Assume call mpi_initialized(lmpi_started,ierr)
    endif

    wtime_at_start = mpi_wtime()

    call lmpi_set_comm(mpi_comm_world)
    call mpi_comm_rank(mpi_comm_world, lmpi_id_world, ierr)

#endif

    have_id : if (present(my_id)) then
      my_id = lmpi_id
    endif have_id

    have_proc : if (present(my_nproc)) then
      my_nproc = lmpi_nproc
    endif have_proc

  end subroutine lmpi_start


!================================== LMPI_SET_COMM ============================80
!
! Set lmpi_set_comm.
!
! Because this is re-entrant, we must ensure that comm related storage
! (i.e., sendreq, recvreq) are deallocated before
! allocated.
!
!=============================================================================80

  subroutine lmpi_set_comm(new_comm)

    integer, intent(in) :: new_comm

#ifdef HAVE_MPI
    integer                        :: ierr, astat
#endif

!  Note: lmpi_{id,nproc,comm,master} are module variables.

    continue

#ifdef HAVE_MPI
    lmpi_comm = new_comm

    call mpi_comm_rank(lmpi_comm,lmpi_id,ierr)

    rank_error: if( ierr /= mpi_success )then
      print*, "mpi_comm_rank failed......"
    endif rank_error

    call mpi_comm_size(lmpi_comm,lmpi_nproc,ierr)

    size_error: if( ierr /= mpi_success )then
      print*, "mpi_comm_size failed......"
    endif size_error

    lmpi_master = ( lmpi_id == 0 )

! sendreq, recvreq :: first deallocate, then allocate.

    astat = 0
    if (allocated(sendreq)) deallocate(sendreq, stat = astat)
    if(astat /= 0) print *, "lmpi: allocate(sendreq(lmpi_nproc-1)) failed"

    allocate(sendreq(lmpi_nproc-1), stat = astat)
    if(astat /= 0) print *, "lmpi: allocate(sendreq(lmpi_nproc-1)) failed"

    if (allocated(recvreq)) deallocate(recvreq, stat = astat)
    if(astat /= 0) print *, "lmpi: allocate(recvreq(lmpi_nproc-1)) failed"

    allocate(recvreq(lmpi_nproc-1), stat = astat)
    if(astat /= 0) print *, "lmpi: allocate(recvreq(lmpi_nproc-1)) failed"

#else
    if (.false.) write(*,*) new_comm ! avoid compiler warnings
#endif

  end subroutine lmpi_set_comm


!================================== LMPI_CREATE_NEW_COMM =====================80
!
! Create two distinct communicators, with the first suggar_nproc processors
! in suggar_comm, the rest in fun3d_comm.
!
! Note, the user should call mpi_comm_free on each communicator when done
! using them; and definitely before re-using them.
!
!=============================================================================80

  subroutine lmpi_create_new_comm(world_comm,n,offset,new_comm)

    integer, intent(in)  :: world_comm, n, offset
    integer, intent(out) :: new_comm

#ifdef HAVE_MPI
    integer :: ierr, rank, i
    integer :: world_group, temp_group

    integer, dimension(:), allocatable :: split_ranks
#endif

  continue

#ifdef HAVE_MPI
    call mpi_comm_group(world_comm, world_group, ierr)

    allocate(split_ranks(n))

    do i = 1, n
      rank = i + offset - 1
      split_ranks(i) = rank
    end do

    call mpi_group_incl(world_group,n,split_ranks,temp_group,ierr)
    call mpi_comm_create(world_comm,temp_group,new_comm,ierr)
    call mpi_group_free(temp_group,ierr)

    deallocate(split_ranks)

    have_multiple_comms = .true.
#else
    new_comm = 4
    if (.false.) write(*,*) world_comm, n, offset
#endif

end subroutine lmpi_create_new_comm


!================================== LMPI_COMM_FREE ===========================80
!
! Free communicators
!
!=============================================================================80

  subroutine lmpi_comm_free(comm)

  integer, intent(in) :: comm

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    call mpi_comm_free(comm,ierr)

    mpi_comm_free_error: if( ierr /= mpi_success )then
      print*, "mpi_comm_free failed......"
    endif mpi_comm_free_error
#else
    if (.false.) write(*,*) comm ! avoid compiler warnings
#endif

  end subroutine lmpi_comm_free


!================================== LMPI_FINALIZE ============================80
!
! Gracefully terminates MPI processes (public)
!
!=============================================================================80

  subroutine lmpi_finalize

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_started) then
      call mpi_finalize(ierr)

      final_error: if( ierr /= mpi_success )then
        print*, "mpi_finalize failed......"
      endif final_error
    endif
#endif

  end subroutine lmpi_finalize


!================================== LMPI_DIE =================================80
!
! Prematurely terminates MPI processes (public)
!
!=============================================================================80

  subroutine lmpi_die

    use system_extensions, only : se_exit

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

    if (have_multiple_comms) then  ! can't gracefully stop if comm is split
#ifdef HAVE_MPI
      call mpi_abort(lmpi_comm, ierr)

      final_error: if( ierr /= mpi_success )then
        print*, "mpi_abort failed......"
      endif final_error
#endif
    else
      call lmpi_finalize ! graceful exit
    end if

    call se_exit(1)

  end subroutine lmpi_die

!================================== LMPI_SYNCHRONIZE =========================80
!
! use mpi barrier to synchronize proc.
!
!=============================================================================80

  subroutine lmpi_synchronize(alt_comm)

    integer, optional, intent(in) :: alt_comm

#ifdef HAVE_MPI
    integer :: ierr, icomm
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = present(alt_comm)

    if ((lmpi_nproc == 1).and.(.not. is_present)) return ! mpi with one proc

    icomm = lmpi_comm
    if (is_present) icomm = alt_comm

    call mpi_barrier(icomm, ierr)

    barr_error: if( ierr /= mpi_success )then
      print*, "mpi_barrier in lmpi_synchronize failed......"
    endif barr_error
#else
   if (.false.) write(*,*) alt_comm ! avoid compiler warnings
#endif
  end subroutine lmpi_synchronize

!================================== LMPI_DURATION ============================80
!
! Elapsed time from lmpi_start
!
!=============================================================================80

  subroutine lmpi_duration(duration_in_sec)

    real(system_r8), intent(out) :: duration_in_sec

    continue

    duration_in_sec = -1.0_system_r8

#ifdef HAVE_MPI

    duration_in_sec = real(mpi_wtime()-wtime_at_start,dp)

#endif

  end subroutine lmpi_duration

!=============================== LMPI_FILE_OPEN ==============================80
!
! MPI_FILE_OPEN.
! rw optons: "RDONLY", "WRONLY", "RDWR  "
!
!=============================================================================80

  subroutine lmpi_file_open(filename, fh, rw, info, jerr, alt_comm)

    use system_extensions, only : se_shell, se_open

    character(len=*),  intent(in)    :: filename, info
    character(len=6),  intent(in)    :: rw
    integer,           intent(inout) :: fh
    integer,           intent(inout) :: jerr
    integer, optional, intent(in)    :: alt_comm

    integer :: icomm
#ifndef HAVE_MPI
    character(len=80) :: command
#endif

  continue

    if (lmpi_mpiio_type == 10) then
       lmpi_mpiio_type = 1
       db = .true.
    else if (lmpi_mpiio_type == 20) then
       lmpi_mpiio_type = 2
       db = .true.
    end if

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

#ifdef HAVE_MPI

    if (db.and.lmpi_master) then
       write(*,*) "file open ",lmpi_mpiio_type,trim(filename)," ",trim(rw)
    end if

    if (allocated(lmpi_mpiio_cnt)) deallocate(lmpi_mpiio_cnt)
    if (allocated(lmpi_mpiio_put)) deallocate(lmpi_mpiio_put)
    if (allocated(lmpi_mpiio_get)) deallocate(lmpi_mpiio_get)
    jerr = 0
    if (lmpi_mpiio_type == 1) then
       if (lmpi_master) write(*,*)'FILE OPEN (MPI_type1) ',trim(rw)
       if (rw == "RDONLY") then
          call MPI_FILE_OPEN(icomm,trim(filename),                             &
                   MPI_MODE_RDONLY,MPI_INFO_NULL, fh, jerr)
       elseif (rw == "WRONLY") then
          call MPI_FILE_OPEN(icomm,trim(filename),                             &
                   MPI_MODE_CREATE+MPI_MODE_WRONLY,MPI_INFO_NULL, fh, jerr)
       elseif (rw == "RDWR  ") then
          call MPI_FILE_OPEN(icomm,trim(filename),                             &
                   MPI_MODE_CREATE+MPI_MODE_RDWR,  MPI_INFO_NULL, fh, jerr)
       else
          write(*,*)"invalid file mode ",rw
          jerr = 1
       end if
    else if (lmpi_mpiio_type == 2) then
       fh = 103
       if (lmpi_master) then
         !write(*,*)'FILE OPEN (MPI_type2) ',trim(filename)," ",trim(rw)
         call se_open(fh,file=filename,form='unformatted',access="stream",     &
                      status='unknown',iostat=jerr)
       end if
    else
       write(*,*)"invalid lmpi_mpiio_type ",lmpi_mpiio_type
       jerr = 1
    end if
    if (jerr /= 0) write(*,*)'lmpi_file_open jerr = ',lmpi_id,jerr,trim(info)
#else
    jerr = 0
    fh   = 103
    if (db.and.lmpi_master) write(*,*)'FILE OPEN (SEQ) ',trim(rw)
    if (rw == "RDONLY") then
      open(fh,file=trim(filename),form='unformatted',iostat=jerr)
    elseif (rw == "WRONLY") then
      open(fh,file=trim(filename),form='unformatted',iostat=jerr)
    elseif (rw == "RDWR  ") then
      open(fh,file=trim(filename),form='unformatted',iostat=jerr)
    else
       write(*,*)"lmpi_file_open: invalid file mode ",trim(rw)," ",trim(info)
       if (.false.) write(*,*)'icomm ',icomm
       stop
       if (.false.) then
          command = 'touch ' // trim(filename)
          call se_shell(command)
          call se_open(fh,file=filename,form='unformatted',                    &
               access="stream",status='unknown',iostat=jerr)
       end if
    end if
    if (jerr /= 0) write(*,*)'lmpi_file_open ERROR (non-mpi) ',jerr,           &
                     trim(filename)," ",fh,trim(rw)," ",trim(info)
#endif

  end subroutine lmpi_file_open

!=============================== LMPI_FILE_CLOSE =============================80
!
! MPI_FILE_CLOSE.
!
!=============================================================================80

  subroutine lmpi_file_close(filename, fh, ierr)

    character(len=*), intent(in)    :: filename
    integer,          intent(in)    :: fh
    integer,          intent(inout) :: ierr

  continue

#ifdef HAVE_MPI
    if (db.and.lmpi_master)                                                    &
       write(*,*)"lmpi_file_close(MPI) START ",lmpi_id,fh,trim(filename)
    ierr = 0
    if (lmpi_mpiio_type == 1) then
       call MPI_FILE_CLOSE(fh,ierr)
    else if (lmpi_mpiio_type == 2) then
       if (lmpi_master) close(fh)
    else
      ierr = 1
      write(*,*)"Invalid lmpi_mpiio_type ",lmpi_mpiio_type
    end if
    if (ierr /= 0) then
       write(*,*)"Error on LMPI_CLOSE ",trim(filename),fh,ierr
    end if
    if (db.and.lmpi_master)                                                    &
       write(*,*)"lmpi_file_close(MPI) END ",lmpi_id,fh,trim(filename)
#else
    if (db)                                                                    &
       write(*,*)"lmpi_file_close(non-MPI) START ",lmpi_id,fh,trim(filename)
    ierr = 0
    close(fh)
    if (.false.)                                                               &
       write(*,*) "lmpi_file_close ",trim(filename)," ",fh,ierr
#endif

  end subroutine lmpi_file_close

!============================== LMPI_MPIIO_READ_L2G ==========================80
!
! Master uses old_nnodes and mpiio_buffer to determine the number of block
! to read the flowfield data. This routine determines the number of block,
! and mapping of the data for subsequent reads.
!
!=============================================================================80

  subroutine lmpi_mpiio_read_l2g(fh,offset,data_desc,old_nnodesg,              &
             new_nnodes0,new_nnodes,new_l2g)!tempProtectPrivate

    integer, intent(in) :: fh,old_nnodesg,new_nnodes0,new_nnodes
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer, dimension(new_nnodes), intent(in)    :: new_l2g

#ifdef HAVE_MPI
    integer :: i,k,m,n, ierr, isize, ifound, iblock, ict, new_nnodes1
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status
    integer, dimension(:), allocatable  :: itemp0, itemp0_ind, itemp1
    integer, dimension(:), allocatable  :: itemp1_ind, itemp1_copy

    logical :: level1_already_sorted
    integer :: new_l2g_matches_old_l2g

    integer, dimension(lmpi_nproc+1) :: first_index_on_part
    integer :: processor
    integer :: first_index_in_ibuffer, last_index_in_ibuffer
    integer :: global_index, ibuffer_index, new_index
  continue

    ierr = 0
    lmpi_mpiio_nnodesg = old_nnodesg
    gsize = lmpi_mpiio_nnodesg*4 ! bytes

    lmpi_mpiio_nblock = lmpi_mpiio_nnodesg/lmpi_mpiio_buffer_nelem
    if ((lmpi_mpiio_nblock*lmpi_mpiio_buffer_nelem) < lmpi_mpiio_nnodesg)      &
         lmpi_mpiio_nblock = lmpi_mpiio_nblock+1

    if ( allocated( lmpi_mpiio_cnt ) ) deallocate( lmpi_mpiio_cnt )
    allocate(lmpi_mpiio_cnt(lmpi_mpiio_nblock))
    lmpi_mpiio_cnt = 0

    if ( allocated( lmpi_mpiio_put ) ) deallocate( lmpi_mpiio_put )
    allocate(lmpi_mpiio_put(new_nnodes)); lmpi_mpiio_put = 0
    if ( allocated( lmpi_mpiio_get ) ) deallocate( lmpi_mpiio_get )
    allocate(lmpi_mpiio_get(new_nnodes)); lmpi_mpiio_get = 0

    isize = lmpi_mpiio_buffer_nelem
    if (lmpi_mpiio_nblock == 1) isize = lmpi_mpiio_nnodesg
    allocate(lmpi_mpiio_ibuffer(isize)); lmpi_mpiio_ibuffer = 0

!   temp storage for level 0 l2g
    allocate(itemp0    (new_nnodes0)); itemp0     = new_l2g(1:new_nnodes0)
    allocate(itemp0_ind(new_nnodes0)); itemp0_ind = 0
    call heap_sort_integer(new_nnodes0,new_l2g,itemp0_ind)
    itemp0 = new_l2g(itemp0_ind)

!   temp storage for level 1 l2g (for binary search)
    new_nnodes1 = new_nnodes-new_nnodes0
    allocate(itemp1(new_nnodes1))
    itemp1 = new_l2g(new_nnodes0+1:new_nnodes)

! Assert level1 nodes sorted
    level1_already_sorted = .true.
    test_sort_of_itemp : do i = 2,new_nnodes1
       if (itemp1(i) < itemp1(i-1)) then
          level1_already_sorted = .false.
          exit test_sort_of_itemp
       end if
    end do test_sort_of_itemp

    sort_itemp_if_needed : if ( .not.level1_already_sorted ) then
      allocate(itemp1_ind(new_nnodes1)); itemp1_ind = 0
      call heap_sort_integer(new_nnodes1,itemp1,itemp1_ind)
      allocate(itemp1_copy(new_nnodes1)); itemp1_copy = itemp1
      itemp1 = itemp1_copy(itemp1_ind)
      deallocate(itemp1_copy)
    endif sort_itemp_if_needed

    first_index_on_part= 0
    call lmpi_gather(new_nnodes0, first_index_on_part(1:lmpi_nproc))
    call lmpi_bcast(first_index_on_part)
    do processor = lmpi_nproc+1, 2, -1
      first_index_on_part(processor) = sum(first_index_on_part(1:processor-1))
    end do
    first_index_on_part(1) = 0
    first_index_on_part = first_index_on_part + 1

    new_l2g_matches_old_l2g = 0

    ierr   = 0
    ict    = 0
    ifound = 0
    first_index_in_ibuffer = 1
    iblock_loop : do iblock = 1,lmpi_mpiio_nblock
       if((iblock==lmpi_mpiio_nblock).and.(ict+isize > lmpi_mpiio_nnodesg))then
          isize = lmpi_mpiio_nnodesg - ict
       end if

       if (lmpi_master) then
          if (lmpi_mpiio_type == 1) then
             call MPI_FILE_READ_AT(fh, offset, lmpi_mpiio_ibuffer, isize,      &
                  MPI_INTEGER, mpi_status, ierr)
          else if (lmpi_mpiio_type == 2) then
             read(fh) lmpi_mpiio_ibuffer(1:isize)
          end if
       end if

       offset = offset + isize*4
       call lmpi_bcast(lmpi_mpiio_ibuffer)

       last_index_in_ibuffer = first_index_in_ibuffer + isize - 1
       verify_l2g_match : do global_index =                                    &
            max( first_index_on_part(lmpi_id+1),   first_index_in_ibuffer),    &
            min( first_index_on_part(lmpi_id+2)-1, last_index_in_ibuffer )
         ibuffer_index = global_index - first_index_in_ibuffer + 1
         new_index = global_index - first_index_on_part(lmpi_id+1) + 1
         if ( new_l2g(new_index) /= lmpi_mpiio_ibuffer(ibuffer_index) ) then
           new_l2g_matches_old_l2g = 1
         end if
       end do verify_l2g_match
       first_index_in_ibuffer = last_index_in_ibuffer + 1

! Debugging
!      lmpi_mpiio_oldl2g(ict+1:ict+isize) = lmpi_mpiio_ibuffer

       outer1: do i = 1,isize
          m = lmpi_mpiio_ibuffer(i)
          k = binary_search(new_nnodes0,itemp0,m)
          found_in_level0 : if (k > 0) then
             n = itemp0_ind(k)
          else
             k = binary_search(new_nnodes1,itemp1,m)
             found_in_level1 : if (k > 0) then
                direct_address_for_level1 : if ( level1_already_sorted ) then
                   n = k + new_nnodes0
                else
                   n = itemp1_ind(k) + new_nnodes0
                endif direct_address_for_level1
             endif found_in_level1
          end if found_in_level0
          on_processor : if (k > 0) then
             lmpi_mpiio_cnt(iblock) = lmpi_mpiio_cnt(iblock) + 1
             ifound = ifound + 1
             lmpi_mpiio_put(ifound) = n
             lmpi_mpiio_get(ifound) = i
          end if on_processor
       end do outer1

       ict = ict + isize
    end do iblock_loop

    i = new_l2g_matches_old_l2g
    call lmpi_max( i, new_l2g_matches_old_l2g )
    call lmpi_bcast( new_l2g_matches_old_l2g )
    l2g_matches : if ( new_l2g_matches_old_l2g == 0 ) then
      lmpi_repart = .false.
      deallocate( lmpi_mpiio_cnt )
      deallocate( lmpi_mpiio_put )
      deallocate( lmpi_mpiio_get )
    end if l2g_matches

    if (.not.level1_already_sorted) deallocate(itemp1_ind)

    if (ifound /= new_nnodes) then
       ierr = 1
       write(*,*)"MPIIO Internal error. Some nodes not found."
       write(*,*)"Contact support ",lmpi_id,ierr,ifound,new_nnodes
    end if
    call lmpi_conditional_stop(ierr,'lmpi:lmpi_mpiio_read_l2g')

    deallocate(lmpi_mpiio_ibuffer)
    deallocate(itemp0,itemp0_ind,itemp1)

    if (.false.) write(*,*)"lmpi_mpiio_read_l2g ",trim(data_desc)

#else
    integer :: node, idummy
    continue
    if (lmpi_mpiio_type == 2) then
      do node = 1, old_nnodesg
        read(fh) idummy
      end do
    else
      if (lmpi_master) then
        write(*,*) 'lmpi:lmpi_mpiio_read_l2g not implented',                   &
                   ' for lmpi_mpiio_type ', lmpi_mpiio_type
      end if
      call lmpi_conditional_stop(1,'lmpi:lmpi_mpiio_read_l2g')
    end if
    if (.false.) write(*,*)"lmpi_mpiio_read_l2g ",                             &
      fh,offset," ",trim(data_desc)," ",                                       &
      old_nnodesg,new_nnodes0,new_nnodes,new_l2g, idummy
#endif

  end subroutine lmpi_mpiio_read_l2g

!=============================== binary_search ===============================80
! binary_search for ascending sorted list
! If sort.f90 moves from libcore to LibF90, then remove this copy and use that.
!=============================================================================80

  function binary_search(nnodes,sortednodes,targetnode)

    integer                                :: binary_search
    integer,                    intent(in) :: nnodes
    integer, dimension(nnodes), intent(in) :: sortednodes
    integer,                    intent(in) :: targetnode

    integer :: lower,upper,mid

    continue

    binary_search = 0

    if (nnodes <= 0) return

    if ( targetnode < sortednodes(1) .or.                                      &
         targetnode > sortednodes(nnodes) ) then
      return
    end if

    lower = 1
    upper = nnodes
    mid = (nnodes+1)/2

    do while ((lower < mid) .and. (mid < upper))
      if (   targetnode >= sortednodes(mid) ) then
        if ( targetnode == sortednodes(mid))then
          binary_search = mid
          return
        end if
        lower = mid
      else
        upper = mid
      end if
      mid = (lower+upper)/2
    end do
    if((mid<nnodes/2).and.(targetnode==sortednodes(1)))                        &
      binary_search = 1
    if((mid>nnodes/2).and.(targetnode==sortednodes(nnodes)))                   &
      binary_search = nnodes

  end function binary_search

!==================================== HEAP_SORT_INTEGER ======================80
!
! integer heap sort for ascending order
!
!=============================================================================80

  subroutine heap_sort_integer(n,arrin,indx)

    integer,                  intent(in) :: n
    integer,    dimension(n), intent(in) :: arrin
    integer,    dimension(n), intent(out):: indx

    integer :: j, l, ir, indxt, i
    integer :: q

    continue

    if (n <= 0) return

    do j=1,n
      indx(j) = j
    end do

    if(n  ==  1)return

    l = n/2 + 1
    ir= n
    do
      if(l > 1)then
        l=l-1
        indxt = indx(l)
         q     = arrin(indxt)
      else
        indxt = indx(ir)
        q     = arrin(indxt)
        indx(ir) = indx(1)
        ir = ir - 1
        if(ir == 1)then
          indx(1) = indxt
          return
        end if
      end if
      i=l
      j=l+l
      do
        if(j > ir) exit
        if(j < ir) then
          if (arrin(indx(j)) < arrin(indx(j+1))) j = j+1
        end if
        if(q < arrin(indx(j)))then
          indx(i) = indx(j)
          i = j
          j = j+j
        else
          j = ir+1
        end if
      end do
      indx(i) = indxt
    end do

  end subroutine heap_sort_integer

!============================== INTEGER_SCALAR_READ_MASTER ===================80
!
! Master reads an integer scalar from a file
!
!=============================================================================80

  subroutine integer_scalar_read_master(fh,offset,data_desc,my_value)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer,                        intent(inout) :: my_value

    integer                             :: ierr
#ifdef HAVE_MPI
    integer                             :: isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = 1 ! elements, not bytes
    gsize = 4

    if (lmpi_master) then
       if (lmpi_mpiio_type == 1) then
          call MPI_FILE_READ_AT(fh, offset, my_value, isize,                   &
               MPI_INTEGER, mpi_status, ierr)
       else
          read(fh, iostat=ierr) my_value
       end if
       if ( 0 /= ierr ) then
         write(*,*) 'lmpi:integer_scalar_read_master read failed ',            &
           'lmpi_mpiio_type ',lmpi_mpiio_type,' iostat ',ierr
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_scalar_read_master')
    call lmpi_bcast(my_value)

    offset = offset + gsize
    if (.false.) write(*,*)"integer_scalar_read_master ",trim(data_desc)

#else
    continue
    read(fh, iostat=ierr) my_value
    if ( 0 /= ierr ) then
      write(*,*) 'lmpi:integer_scalar_read_master read failed ',               &
        'HAVE_MPI not defined',' iostat ',ierr
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_scalar_read_master')
    if (.false.)                                                               &
    write(*,*)"integer_scalar_read_master ",                                   &
      fh,offset," ",trim(data_desc)," ",my_value
#endif

  end subroutine integer_scalar_read_master

!============================== INTEGER_VECTOR_READ_MASTER ===================80
!
! Master reads a contiguous 1 dimensional integer array from a file
!
!=============================================================================80

  subroutine integer_vector_read_master(fh,offset,data_desc,my_int,beg,end)

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer,  dimension(:),         intent(inout) :: my_int
    integer,                        intent(in)    :: beg, end

    integer                             :: ierr
#ifdef HAVE_MPI
    integer                             :: isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = (end-beg)+1 ! elements, not bytes
    gsize = isize*4

    if (lmpi_master) then
       if (lmpi_mpiio_type == 1) then
          call MPI_FILE_READ_AT(fh, offset, my_int, isize, MPI_INTEGER,        &
                                mpi_status, ierr)
       else
          read(fh, iostat=ierr) my_int(beg:end)
       end if
       if ( 0 /= ierr ) then
         write(*,*) 'lmpi:integer_vector_read_master read failed ',            &
           'lmpi_mpiio_type ',lmpi_mpiio_type,' iostat ',ierr
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_vector_read_master')

    offset = offset + gsize
    if (.false.) write(*,*)"integer_vector_read_master ",trim(data_desc)

#else
    continue
    read(fh, iostat=ierr) my_int(beg:end)
    if ( 0 /= ierr ) then
      write(*,*) 'lmpi:integer_vector_read_master read failed ',               &
        'HAVE_MPI not defined',' iostat ',ierr
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_vector_read_master')
    if (.false.)                                                               &
    write(*,*)"integer_vector_read_master ",                                   &
      fh,offset," ",trim(data_desc)," ",my_int(beg:end),beg,end
#endif

  end subroutine integer_vector_read_master

!============================== INTEGER_VECTOR_WRITE_MASTER ==================80
!
! Master writes a contiguous 1 dimensional integer array from a file
!
!=============================================================================80

  subroutine integer_vector_write_master(fh,offset,data_desc,my_int,beg,end)

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer,  dimension(:),         intent(in)    :: my_int
    integer,                        intent(in)    :: beg, end

#ifdef HAVE_MPI
    integer                             :: ierr, isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = (end-beg)+1 ! elements, not bytes
    gsize = isize*4

    if (lmpi_master) then
       if (lmpi_mpiio_type == 1) then
          call MPI_FILE_WRITE_AT(fh, offset, my_int, isize, MPI_INTEGER,       &
                                 mpi_status, ierr)
       else
          write(fh) my_int(beg:end)
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_vector_write_master')

    offset = offset + gsize
    if (.false.) write(*,*)"integer_vector_write_master ",trim(data_desc)

#else
    continue
    write(fh) my_int(beg:end)
    if (.false.)                                                               &
    write(*,*)"INTEGER_vector_write_master ",                                  &
      fh,offset," ",trim(data_desc)," ",my_int(beg:end),beg,end
#endif

  end subroutine integer_vector_write_master

!============================== DOUBLE_SCALAR_READ ===========================80
!
! Master reads a real(dp) scalar from a file
!
!=============================================================================80

  subroutine double_scalar_read_master(fh,offset,data_desc,my_real)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    real(system_r8),                intent(inout) :: my_real

    integer                             :: ierr
#ifdef HAVE_MPI
    integer                             :: isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = 1 ! elements, not bytes
    gsize = 8

    if (lmpi_master) then
       if (lmpi_mpiio_type == 1) then
          call MPI_FILE_READ_AT(fh, offset, my_real, isize,                    &
               MPI_DOUBLE_PRECISION, mpi_status, ierr)
       else
          read(fh, iostat=ierr) my_real
       end if
       if ( 0 /= ierr ) then
         write(*,*) 'lmpi:double_scalar_read_master read failed ',             &
           'lmpi_mpiio_type ',lmpi_mpiio_type,' iostat ',ierr
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:double_scalar_read_master')
    call lmpi_bcast(my_real)

    offset = offset + gsize
    if (.false.) write(*,*)"double_scalar_read_master ",trim(data_desc)

#else
    continue
    read(fh, iostat=ierr) my_real
    if ( 0 /= ierr ) then
      write(*,*) 'lmpi:real_scalar_read_master read failed ',                  &
        'HAVE_MPI not defined',' iostat ',ierr
    end if
    call lmpi_conditional_stop(ierr,'lmpi:double_scalar_read_master')
    if (.false.)                                                               &
    write(*,*)"double_scalar_read_master ",                                    &
      fh,offset," ",trim(data_desc)," ",my_real
#endif

  end subroutine double_scalar_read_master


!============================== INTEGER_VECTOR_READ ==========================80
!
! Master reads a contiguous 1 dimensional integer array from a file
!
!=============================================================================80

  subroutine integer_vector_read(fh,offset,data_desc,my_int,beg,end)

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer,  dimension(:),         intent(inout) :: my_int
    integer,  dimension(:),         intent(in)    :: beg, end

#ifdef HAVE_MPI
    integer                             :: i, ierr, isize, ipe
    integer, dimension(:), allocatable  :: off, itemp
    integer(kind=lmpi_offset_kind)      :: gsize,l_offset
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = (end(1)-beg(1))+1 ! elements, not bytes

    allocate(off(0:lmpi_nproc-1)); off = 0
    call lmpi_allgather(isize,off)

    gsize = 0
    do i = 0,lmpi_nproc-1
       gsize = gsize + off(i)*4 ! bytes
    end do

    l_offset = 0
    if (lmpi_id > 0) then
       do i = 0,lmpi_id-1
          l_offset = l_offset + off(i)*4 ! bytes
       end do
    end if

    if (lmpi_mpiio_type == 1) then
       call MPI_FILE_READ_AT_ALL(fh, offset+l_offset, my_int, isize,           &
            MPI_INTEGER, mpi_status, ierr)
    else
       if (lmpi_master) then
          read(fh) my_int(beg(1):end(1))
          do ipe = 1,lmpi_nproc-1
             isize = off(ipe)
             allocate(itemp(isize)); itemp = 0
             read(fh) itemp(1:isize)
             call lmpi_send(itemp,isize,ipe,100,ierr)
             deallocate(itemp)
          end do
       else
          isize = off(lmpi_id)
          allocate(itemp(isize)); itemp = 0
          call lmpi_recv(itemp,isize,0,100,ierr)
          my_int(beg(1):end(1)) = itemp(1:isize)
          deallocate(itemp)
       end if
    end if

    call lmpi_conditional_stop(ierr,'lmpi:integer_vector_read')
    deallocate(off)

    offset = offset + gsize
    if (.false.) write(*,*)"integer_vector_read ",trim(data_desc)

#else
    continue
    read(fh) my_int(beg(1):end(1))
    if (.false.)                                                               &
    write(*,*)"integer_vector_read ",                                          &
      fh,offset," ",trim(data_desc)," ",my_int(beg(1):end(1)),beg(1),end(1)
#endif

  end subroutine integer_vector_read

!============================== DOUBLE_VECTOR_READ ===========================80
!
! Reads a 1 dimensional real(dp) array to a file
!
!=============================================================================80

  subroutine double_vector_read(fh,offset,data_desc,my_real,beg,end,l2g)!tempProtectPrivate

    integer,                         intent(in)    :: fh
    integer(kind=lmpi_offset_kind),  intent(inout) :: offset
    character(len=*),                intent(in)    :: data_desc
    real(system_r8), dimension(:),   intent(inout) :: my_real
    integer, dimension(:),           intent(in)    :: beg, end
    integer, dimension(:), optional, intent(in)    :: l2g

    integer :: i
    real(system_r8), dimension(:),   allocatable :: temp1

#ifdef HAVE_MPI
    integer                             :: ierr,isize, ipe, j
    integer(kind=lmpi_offset_kind)      :: gsize,l_offset,iblock,ifound,ict
    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

    logical :: reorder

  continue

    reorder = present(l2g)
    ierr   = 0

    if (.not.lmpi_repart) then
       isize = (end(1)-beg(1))+1 ! elements, not bytes

       allocate(off(0:lmpi_nproc-1)); off = 0
       call lmpi_allgather(isize,off)

       gsize = 0
       do i = 0,lmpi_nproc-1
          gsize = gsize + off(i)*8 ! bytes
       end do

       l_offset = 0
       if (lmpi_id > 0) then
          do i = 0,lmpi_id-1
             l_offset = l_offset + off(i)*8 ! bytes
          end do
       end if

       if (lmpi_mpiio_type == 1) then
          allocate(temp1(beg(1):end(1))); temp1 = 0.0_dp
          call MPI_FILE_READ_AT_ALL(fh, offset+l_offset, temp1, isize,         &
               MPI_DOUBLE_PRECISION, mpi_status, ierr)
          if (.not.reorder) then
                my_real(beg(1):end(1)) = temp1(beg(1):end(1))
          else
             do i = beg(1),end(1)
                my_real(l2g(i)) = temp1(i)
             end do
          end if
          deallocate(temp1)
       else ! type == 2
          if (.not.reorder) then
             if (lmpi_master) then
                read(fh) my_real(beg(1):end(1))
                do ipe = 1,lmpi_nproc-1
                   isize = off(ipe)
                   allocate(temp1(isize)); temp1 = 0.0_dp
                   read(fh) temp1(1:isize)
                   call lmpi_send(temp1,isize,ipe,100,ierr)
                   deallocate(temp1)
                end do
             else
                isize = off(lmpi_id)
                allocate(temp1(isize)); temp1 = 0.0_dp
                call lmpi_recv(temp1,isize,0,100,ierr)
                my_real(beg(1):end(1)) = temp1(1:isize)
                deallocate(temp1)
             end if
          else
             if (lmpi_master) then
                allocate(temp1(beg(1):end(1))); temp1 = 0.0_dp
                read(fh) temp1(beg(1):end(1))
                do i = beg(1),end(1)
                   my_real(l2g(i)) = temp1(i)
                end do
                deallocate(temp1)

                do ipe = 1,lmpi_nproc-1
                   isize = off(ipe)
                   allocate(temp1(1:isize)); temp1 = 0.0_dp
                   read(fh) temp1(1:isize)
                   call lmpi_send(temp1,isize,ipe,100,ierr)
                   deallocate(temp1)
                end do
             else
                isize = off(lmpi_id)
                allocate(temp1(isize)); temp1 = 0.0_dp
                call lmpi_recv(temp1,isize,0,100,ierr)
                j = 0
                do i = beg(1),end(1)
                   j = j + 1
                   my_real(l2g(i)) = temp1(j)
                end do
                deallocate(temp1)
             end if
          end if
       end if
       offset = offset + gsize
       deallocate(off)

    else ! repartition
      isize = lmpi_mpiio_buffer_nelem
      if (lmpi_mpiio_nblock == 1) isize = lmpi_mpiio_nnodesg

       allocate(temp1(isize)); temp1 = 0.0_dp
       ict    = 0
       ifound = 0
       do iblock = 1,lmpi_mpiio_nblock
          if ((iblock == lmpi_mpiio_nblock).and.(lmpi_mpiio_nblock > 1)) then
             isize = lmpi_mpiio_nnodesg - ict
             deallocate(temp1); allocate(temp1(isize))
          end if
          if (lmpi_master) then
             if (lmpi_mpiio_type == 1) then
                call MPI_FILE_READ_AT(fh, offset, temp1, isize,                &
                                      MPI_DOUBLE_PRECISION, mpi_status, ierr)
             else
                read(fh) temp1(1:isize)
             end if
          end if
          offset = offset + isize*8
          call lmpi_bcast(temp1)
          do i = 1,lmpi_mpiio_cnt(iblock)
             ifound = ifound + 1
             my_real(lmpi_mpiio_put(ifound)) = temp1(lmpi_mpiio_get(ifound))
          end do
          ict = ict + isize
       end do
       deallocate(temp1)
    end if
    if (ierr /= 0) write(*,*)"ERROR READ Repart ",lmpi_id,trim(data_desc)
    call lmpi_conditional_stop(ierr,'lmpi:double_vector_read')

#else
    continue
    if (.not.present(l2g)) then
       read(fh) my_real(beg(1):end(1))
    else
       allocate(temp1(beg(1):end(1))); temp1 = 0.0_dp
       read(fh) temp1(beg(1):end(1))
       do i = beg(1),end(1)
          my_real(l2g(i)) = temp1(i)
       end do
       deallocate(temp1)
    end if
    if (.false.)                                                               &
    write(*,*)"double_vector_read ",                                           &
      fh,offset," ",trim(data_desc)," ",                                       &
      my_real(beg(1):end(1)),beg(1),end(1),present(l2g)
#endif

  end subroutine double_vector_read

!============================== DOUBLE_MATRIX_READ ===========================80
!
! Reads a 2 dimensional real(system_r8) array to a file
!
!=============================================================================80

  subroutine double_matrix_read(fh,offset,data_desc,my_real,beg,end,l2g)!tempProtectPrivate

    integer,                         intent(in)    :: fh
    integer(kind=lmpi_offset_kind),  intent(inout) :: offset
    character(len=*),                intent(in)    :: data_desc
    real(system_r8), dimension(:,:), intent(inout) :: my_real
    integer, dimension(:),           intent(in)    :: beg, end
    integer, dimension(:), optional, intent(in)    :: l2g

    integer :: i
    real(system_r8), dimension(:,:), allocatable :: temp2

#ifdef HAVE_MPI
    integer :: ierr,isize, ict,ifound,ipe
    integer :: dim1, dim2, iblock
    integer(kind=lmpi_offset_kind)      :: gsize,l_offset
    integer, dimension(0:lmpi_nproc-1)  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status
    logical :: reorder

  continue

    reorder = present(l2g)
    ierr = 0
    dim1 = (end(1)-beg(1)) + 1
    dim2 = (end(2)-beg(2)) + 1

    if (.not.lmpi_repart) then

       isize = dim1*dim2 ! elements, not bytes

       off = 0
       call lmpi_allgather(isize,off)

       gsize = 0
       do i = 0,lmpi_nproc-1
          gsize = gsize + off(i)*8 ! bytes
       end do

       l_offset = 0
       if (lmpi_id > 0) then
          do i = 0,lmpi_id-1
             l_offset = l_offset + off(i)*8 ! bytes
          end do
       end if

       if (lmpi_mpiio_type == 1) then
          allocate(temp2(beg(1):end(1),dim2)); temp2 = 0.0_system_r8
          call MPI_FILE_READ_AT_ALL(fh, offset+l_offset, temp2, isize,         &
               MPI_DOUBLE_PRECISION, mpi_status, ierr)
          if (.not.reorder) then
             my_real(beg(1):end(1),beg(2):end(2)) =                            &
                temp2(beg(1):end(1),beg(2):end(2))
          else
             do i = beg(2),end(2)
                my_real(beg(1):end(1),l2g(i)) = temp2(beg(1):end(1),i)
             end do
          end if
          deallocate(temp2)
       else
          if (.not.reorder) then
             if (lmpi_master) then
                read(fh) my_real(beg(1):end(1),beg(2):end(2))
                do ipe = 1,lmpi_nproc-1
                   dim2 = off(ipe)/dim1
                   allocate(temp2(dim1,dim2)); temp2 = 0.0_system_r8
                   read(fh) temp2(1:dim1,1:dim2)
                   call lmpi_send(temp2,dim1*dim2,ipe,100,ierr)
                   deallocate(temp2)
                end do
             else
                allocate(temp2(dim1,dim2)); temp2 = 0.0_dp
                call lmpi_recv(temp2,dim1*dim2,0,100,ierr)
                do i = 1,dim2
                   my_real(beg(1):end(1),i) = temp2(1:dim1,i)
                end do
                deallocate(temp2)
             end if
          else
             if (lmpi_master) then
                allocate(temp2(beg(1):end(1),beg(2):end(2)))
                temp2 = 0.0_system_r8
                read(fh) temp2(beg(1):end(1),beg(2):end(2))
                do i = beg(2),end(2)
                   my_real(beg(1):end(1),l2g(i)) = temp2(beg(1):end(1),i)
                end do
                deallocate(temp2)

                do ipe = 1,lmpi_nproc-1
                   dim2 = off(ipe)/dim1
                   allocate(temp2(dim1,dim2)); temp2 = 0.0_system_r8
                   read(fh) temp2(1:dim1,1:dim2)
                   call lmpi_send(temp2,dim1*dim2,ipe,100,ierr)
                   deallocate(temp2)
                end do
             else
                dim2 = off(lmpi_id)/dim1
                allocate(temp2(dim1,dim2)); temp2 = 0.0_dp
                call lmpi_recv(temp2,dim1*dim2,0,100,ierr)
                do i = beg(2),end(2)
                   my_real(beg(1):end(1),l2g(i)) = temp2(1:dim1,i)
                end do
                deallocate(temp2)
             end if
          end if
       end if
       offset = offset + gsize

    else ! repartition

      isize = lmpi_mpiio_buffer_nelem
      if (lmpi_mpiio_nblock == 1) isize = lmpi_mpiio_nnodesg

       allocate(temp2(dim1,isize)); temp2 = 0.0_dp
       ict    = 0
       ifound = 0
       do iblock = 1,lmpi_mpiio_nblock
          if ((iblock == lmpi_mpiio_nblock).and.(lmpi_mpiio_nblock > 1)) then
             isize = lmpi_mpiio_nnodesg - ict
             deallocate(temp2); allocate(temp2(dim1,isize)); temp2 = 0.0_dp
          end if
          if (lmpi_master) then
             if (lmpi_mpiio_type == 1) then
                call MPI_FILE_READ_AT(fh, offset, temp2, isize*dim1,           &
                                      MPI_DOUBLE_PRECISION, mpi_status, ierr)
             else
                read(fh) temp2(1:dim1,1:isize)
             end if
          end if
          offset = offset + isize*dim1*8

          call lmpi_bcast(temp2)
          do i = 1,lmpi_mpiio_cnt(iblock)
             ifound = ifound + 1
             my_real(beg(1):end(1), lmpi_mpiio_put(ifound)) =                  &
                                temp2(1:dim1,lmpi_mpiio_get(ifound))
          end do
          ict = ict + isize
       end do
       deallocate(temp2)
    end if
    if (ierr /= 0) write(*,*)"ERROR READ Repart ",lmpi_id,trim(data_desc)
    call lmpi_conditional_stop(ierr,'lmpi:double_matrix_read')

#else
    continue
    if (.not.present(l2g)) then
       read(fh) my_real(beg(1):end(1),beg(2):end(2))
    else
       allocate(temp2(beg(1):end(1),beg(2):end(2))); temp2 = 0.0_system_r8
       read(fh) temp2(beg(1):end(1),beg(2):end(2))
       do i = beg(2),end(2)
          my_real(beg(1):end(1),l2g(i)) = temp2(beg(1):end(1),i)
       end do
       deallocate(temp2)
    end if

    if (.false.)                                                               &
    write(*,*)"double_matrix_read ",                                           &
      fh,offset," ",trim(data_desc)," ",my_real(beg(1):end(1),beg(2):end(2)),  &
      beg(1:2),end(1:2),present(l2g)
#endif

  end subroutine double_matrix_read

!============================== INTEGER_SCALAR_WRITE_MASTER ==================80
!
! Master writes an integer scalar from a file
!
!=============================================================================80

  subroutine integer_scalar_write_master(fh,offset,data_desc,my_value)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer,                        intent(inout) :: my_value

#ifdef HAVE_MPI
    integer                             :: ierr, isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = 1 ! elements, not bytes
    gsize = 4

    if (lmpi_master) then
       if (lmpi_mpiio_type == 1) then
          call MPI_FILE_WRITE_AT(fh, offset, my_value, isize,                  &
               MPI_INTEGER, mpi_status, ierr)
       else
          write(fh) my_value
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_scalar_write_master')

    offset = offset + gsize
    if (.false.) write(*,*) "integer_scalar_write_master ",trim(data_desc)

#else
    continue
    write(fh) my_value

    if (.false.)                                                               &
    write(*,*) "integer_scalar_write_master ",                                 &
      fh,offset," ",trim(data_desc)," ",my_value
#endif

  end subroutine integer_scalar_write_master

!============================== INTEGER_VECTOR_WRITE =========================80
!
! Writes a 1 dimensional integer array to a file
!
!=============================================================================80

  subroutine integer_vector_write(fh,offset,data_desc,my_int,beg,end)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    integer,  dimension(:),         intent(in)    :: my_int
    integer,  dimension(:),         intent(in)    :: beg, end

#ifdef HAVE_MPI
    integer :: i, ierr,isize, ipe
    integer(kind=lmpi_offset_kind) :: gsize,l_offset

    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

    integer, dimension(:), allocatable :: temp1

  continue

    ierr  = 0
    isize = (end(1)-beg(1))+1 ! elements, not bytes

    allocate(off(0:lmpi_nproc-1)); off = 0
    call lmpi_allgather(isize,off)

    gsize = 0
    do i = 0,lmpi_nproc-1
       gsize = gsize + off(i)*4 ! bytes
    end do

    l_offset = 0
    if (lmpi_id > 0) then
       do i = 0,lmpi_id-1
          l_offset = l_offset + off(i)*4 ! bytes
       end do
    end if

    if (lmpi_mpiio_type == 1) then
       allocate(temp1(1:isize))
       temp1(1:isize) = my_int(beg(1):end(1))
       call MPI_FILE_WRITE_AT_ALL(fh, offset+l_offset, temp1, isize,           &
            MPI_INTEGER, mpi_status, ierr)
       deallocate(temp1)
    else
       if (lmpi_master) then
          write(fh) my_int(beg(1):end(1))
          do ipe = 1,lmpi_nproc-1
             isize = off(ipe)
             allocate(temp1(isize)); temp1 = 0.0_dp
             call lmpi_recv(temp1,isize,ipe,100,ierr)
             write(fh) temp1(1:isize)
             deallocate(temp1)
          end do
       else
          allocate(temp1(isize))
          temp1(1:isize) = my_int(beg(1):end(1))
          call lmpi_send(temp1,isize,0,100,ierr)
          deallocate(temp1)
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:integer_vector_write')
    deallocate(off)

    offset = offset + gsize
    if (.false.) write(*,*)"integer_vector_write ",trim(data_desc)

#else
    continue
    write(fh) my_int(beg(1):end(1))

    if (.false.)                                                               &
    write(*,*)"integer_vector_write ",                                         &
      fh,offset," ",trim(data_desc)," ",my_int(beg(1):end(1)),beg(1),end(1)
#endif

  end subroutine integer_vector_write

!============================== DOUBLE_SCALAR_WRITE ==========================80
!
! Master writes a real(dp) scalar from a file
!
!=============================================================================80

  subroutine double_scalar_write_master(fh,offset,data_desc,my_real)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    real(system_r8),                intent(inout) :: my_real

#ifdef HAVE_MPI
    integer                             :: ierr, isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = 1 ! elements, not bytes
    gsize = 8

    if (lmpi_master) then
       if (lmpi_mpiio_type == 1) then
          call MPI_FILE_WRITE_AT(fh, offset, my_real, isize,                   &
               MPI_DOUBLE_PRECISION, mpi_status, ierr)
       else
          write(fh) my_real
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:double_scalar_write_master')

    offset = offset + gsize
    if (.false.) write(*,*)"double_scalar_write_master ",trim(data_desc)

#else
    continue

    write(fh) my_real

    if (.false.)                                                               &
    write(*,*)"double_scalar_write_master ",                                   &
      fh,offset," ",trim(data_desc)," ",my_real
#endif

  end subroutine double_scalar_write_master

!============================== DOUBLE_VECTOR_WRITE ==========================80
!
! Writes a 1 dimensional real(system_r8) array to a file
!
!=============================================================================80

  subroutine double_vector_write(fh,offset,data_desc,my_real,beg,end)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    real(system_r8), dimension(:),  intent(in)    :: my_real
    integer,  dimension(:),         intent(in)    :: beg, end

#ifdef HAVE_MPI
    integer :: i, ierr,isize, ipe
    integer(kind=lmpi_offset_kind) :: gsize,l_offset

    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

    real(system_r8), dimension(:), allocatable :: temp1

  continue

    ierr  = 0
    isize = (end(1)-beg(1))+1 ! elements, not bytes

    allocate(off(0:lmpi_nproc-1)); off = 0
    call lmpi_allgather(isize,off)

    gsize = 0
    do i = 0,lmpi_nproc-1
       gsize = gsize + off(i)*8 ! bytes
    end do

    l_offset = 0
    if (lmpi_id > 0) then
       do i = 0,lmpi_id-1
          l_offset = l_offset + off(i)*8 ! bytes
       end do
    end if

    if (lmpi_mpiio_type == 1) then
       allocate(temp1(isize))
       temp1 = my_real(beg(1):end(1))
       call MPI_FILE_WRITE_AT_ALL(fh, offset+l_offset, temp1, isize,           &
            MPI_DOUBLE_PRECISION, mpi_status, ierr)
       deallocate(temp1)
    else
       if (lmpi_master) then
          write(fh) my_real(beg(1):end(1))
          do ipe = 1,lmpi_nproc-1
             isize = off(ipe)
             allocate(temp1(isize)); temp1 = 0.0_dp
             call lmpi_recv(temp1,isize,ipe,100,ierr)
             write(fh) temp1(1:isize)
             deallocate(temp1)
          end do
       else
          allocate(temp1(isize))
          temp1 = my_real(beg(1):end(1))
          call lmpi_send(temp1,isize,0,100,ierr)
          deallocate(temp1)
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:double_vector_write')
    deallocate(off)

    offset = offset + gsize
    if (.false.) write(*,*)"double_vector_write ",trim(data_desc)

#else
    continue
    write(fh) my_real(beg(1):end(1))

    if (.false.)                                                               &
    write(*,*)"double_vector_write ",                                          &
      fh,offset," ",trim(data_desc)," ",my_real(beg(1):end(1)),beg(1),end(1)
#endif

  end subroutine double_vector_write

!============================== DOUBLE_MATRIX_WRITE ==========================80
!
! Writes a 2 dimensional real(system_r8) array to a file
!
!=============================================================================80

  subroutine double_matrix_write(fh,offset,data_desc,my_real,beg,end)!tempProtectPrivate

    integer,                         intent(in)    :: fh
    integer(kind=lmpi_offset_kind),  intent(inout) :: offset
    character(len=*),                intent(in)    :: data_desc
    real(system_r8), dimension(:,:), intent(in)    :: my_real
    integer,  dimension(:),          intent(in)    :: beg, end

    real(system_r8), dimension(:,:), allocatable :: temp2
    integer :: dim1, dim2

#ifdef HAVE_MPI
    integer :: i, ierr,isize, ipe
    integer(kind= lmpi_offset_kind) :: gsize,l_offset

    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr = 0
    dim1 = (end(1)-beg(1)) + 1
    dim2 = (end(2)-beg(2)) + 1
    isize = dim1*dim2 ! elements, not bytes

    allocate(off(0:lmpi_nproc-1)); off = 0
    call lmpi_allgather(isize,off)

    gsize = 0
    do i = 0,lmpi_nproc-1
       gsize = gsize + off(i)*8 ! bytes
    end do

    l_offset = 0
    if (lmpi_id > 0) then
       do i = 0,lmpi_id-1
          l_offset = l_offset + off(i)*8 ! bytes
       end do
    end if

    if (lmpi_mpiio_type == 1) then
       allocate(temp2(dim1,dim2))
       temp2 = my_real(beg(1):end(1),beg(2):end(2))
       call MPI_FILE_WRITE_AT_ALL(fh, offset+l_offset, temp2, isize,           &
            MPI_DOUBLE_PRECISION, mpi_status, ierr)
       deallocate(temp2)
    else
       if (lmpi_master) then
          allocate(temp2(dim1,dim2))
          temp2 = my_real(beg(1):end(1),beg(2):end(2))
          write(fh) temp2
          deallocate(temp2)
          do ipe = 1,lmpi_nproc-1
             dim2 = off(ipe)/dim1
             allocate(temp2(dim1,dim2)); temp2 = 0.0_dp
             call lmpi_recv(temp2,dim1*dim2,ipe,100,ierr)
             write(fh) temp2(1:dim1,1:dim2)
             deallocate(temp2)
          end do
       else
         allocate(temp2(dim1,dim2))
         temp2 = my_real(beg(1):end(1),beg(2):end(2))
         call lmpi_send(temp2,dim1*dim2,0,100,ierr)
         deallocate(temp2)
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:double_matrix_write')
    deallocate(off)

    offset = offset + gsize
    if (.false.) write(*,*)"double_matrix_write ",trim(data_desc)

#else
    continue
    dim1 = (end(1)-beg(1)) + 1
    dim2 = (end(2)-beg(2)) + 1
    allocate(temp2(dim1,dim2))
    temp2 = my_real(beg(1):end(1),beg(2):end(2))
    write(fh) temp2(beg(1):end(1),beg(2):end(2))

    if (.false.)                                                               &
    write(*,*)"double_matrix_write ",                                          &
      fh,offset," ",trim(data_desc)," ",                                       &
      temp2(beg(1):end(1),beg(2):end(2)),beg(1:2),end(1:2)
    deallocate(temp2)
#endif

  end subroutine double_matrix_write

!============================== CMPXR8_SCALAR_READ ===========================80
!
! Master reads a complex(system_r8) scalar from a file
!
!=============================================================================80

  subroutine cmpxr8_scalar_read_master(fh,offset,data_desc,my_real)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    complex(system_r8),             intent(inout) :: my_real

#ifdef HAVE_MPI
    integer                             :: ierr, isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = 1 ! elements, not bytes
    gsize = 16

    if (lmpi_master)                                                           &
    call MPI_FILE_READ_AT(fh, offset, my_real, isize,                          &
         MPI_DOUBLE_COMPLEX, mpi_status, ierr)
    call lmpi_conditional_stop(ierr,'lmpi:cmpxr8_scalar_read_master')
    call lmpi_bcast(my_real)

    offset = offset + gsize
    if (.false.) write(*,*) "cmpxr8_scalar_read_master ",trim(data_desc)

#else
    continue
    read(fh) my_real

    if (.false.)                                                               &
    write(*,*) "cmpxr8_scalar_read_master ",                                   &
      fh,offset," ",trim(data_desc)," ",my_real
#endif

  end subroutine cmpxr8_scalar_read_master

!============================== CMPXR8_VECTOR_READ ===========================80
!
! Reads a 1 dimensional complex(system_r8) array to a file
!
!=============================================================================80

  subroutine cmpxr8_vector_read(fh,offset,data_desc,my_real,beg,end,l2g)!tempProtectPrivate

    integer,                          intent(in)    :: fh
    integer(kind=lmpi_offset_kind),   intent(inout) :: offset
    character(len=*),                 intent(in)    :: data_desc
    complex(system_r8), dimension(:), intent(inout) :: my_real
    integer, dimension(:),            intent(in)    :: beg, end
    integer, dimension(:), optional,  intent(in)    :: l2g

    integer :: i
    complex(system_r8), dimension(:), allocatable :: temp1
    integer, parameter :: sys_size  = r8

#ifdef HAVE_MPI
    integer                             :: ierr,isize, ipe, j
    integer(kind=lmpi_offset_kind)      :: gsize,l_offset,iblock,ifound,ict
    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

    logical            :: reorder
    integer, parameter :: byte_size = 16

  continue

    reorder = present(l2g)
    ierr   = 0

    if (.not.lmpi_repart) then
       isize = (end(1)-beg(1))+1 ! elements, not bytes

       allocate(off(0:lmpi_nproc-1)); off = 0
       call lmpi_allgather(isize,off)

       gsize = 0
       do i = 0,lmpi_nproc-1
          gsize = gsize + off(i)*byte_size ! bytes
       end do

       l_offset = 0
       if (lmpi_id > 0) then
          do i = 0,lmpi_id-1
             l_offset = l_offset + off(i)*byte_size ! bytes
          end do
       end if

       if (lmpi_mpiio_type == 1) then
          allocate(temp1(beg(1):end(1))); temp1 = 0.0_sys_size
          call MPI_FILE_READ_AT_ALL(fh, offset+l_offset, temp1, isize,         &
               MPI_DOUBLE_COMPLEX, mpi_status, ierr)
          if (.not.reorder) then
                my_real(beg(1):end(1)) = temp1(beg(1):end(1))
          else
             do i = beg(1),end(1)
                my_real(l2g(i)) = temp1(i)
             end do
          end if
          deallocate(temp1)
       else ! type == 2
          if (.not.reorder) then
             if (lmpi_master) then
                read(fh) my_real(beg(1):end(1))
                do ipe = 1,lmpi_nproc-1
                   isize = off(ipe)
                   allocate(temp1(isize)); temp1 = 0.0_sys_size
                   read(fh) temp1(1:isize)
                   call lmpi_send(temp1,isize,ipe,100,ierr)
                   deallocate(temp1)
                end do
             else
                isize = off(lmpi_id)
                allocate(temp1(isize)); temp1 = 0.0_sys_size
                call lmpi_recv(temp1,isize,0,100,ierr)
                my_real(beg(1):end(1)) = temp1(1:isize)
                deallocate(temp1)
             end if
          else
             if (lmpi_master) then
                allocate(temp1(beg(1):end(1))); temp1 = 0.0_sys_size
                read(fh) temp1(beg(1):end(1))
                do i = beg(1),end(1)
                   my_real(l2g(i)) = temp1(i)
                end do
                deallocate(temp1)

                do ipe = 1,lmpi_nproc-1
                   isize = off(ipe)
                   allocate(temp1(1:isize)); temp1 = 0.0_sys_size
                   read(fh) temp1(1:isize)
                   call lmpi_send(temp1,isize,ipe,100,ierr)
                   deallocate(temp1)
                end do
             else
                isize = off(lmpi_id)
                allocate(temp1(isize)); temp1 = 0.0_sys_size
                call lmpi_recv(temp1,isize,0,100,ierr)
                j = 0
                do i = beg(1),end(1)
                   j = j + 1
                   my_real(l2g(i)) = temp1(j)
                end do
                deallocate(temp1)
             end if
          end if
       end if
       offset = offset + gsize
       deallocate(off)

    else ! repartition
      isize = lmpi_mpiio_buffer_nelem
      if (lmpi_mpiio_nblock == 1) isize = lmpi_mpiio_nnodesg

       allocate(temp1(isize)); temp1 = 0.0_sys_size
       ict    = 0
       ifound = 0
       do iblock = 1,lmpi_mpiio_nblock
          if ((iblock == lmpi_mpiio_nblock).and.(lmpi_mpiio_nblock > 1)) then
             isize = lmpi_mpiio_nnodesg - ict
             deallocate(temp1); allocate(temp1(isize))
          end if
          if (lmpi_master) then
             if (lmpi_mpiio_type == 1) then
                call MPI_FILE_READ_AT(fh, offset, temp1, isize,                &
                                      MPI_DOUBLE_COMPLEX, mpi_status, ierr)
             else
                read(fh) temp1(1:isize)
             end if
          end if
          offset = offset + isize*byte_size
          call lmpi_bcast(temp1)
          do i = 1,lmpi_mpiio_cnt(iblock)
             ifound = ifound + 1
             my_real(lmpi_mpiio_put(ifound)) = temp1(lmpi_mpiio_get(ifound))
          end do
          ict = ict + isize
       end do
       deallocate(temp1)
    end if
    if (ierr /= 0) write(*,*)"ERROR READ Repart ",lmpi_id,trim(data_desc)
    call lmpi_conditional_stop(ierr,'lmpi:cmpxr8_vector_read')

#else
    continue
    if (.not.present(l2g)) then
       read(fh) my_real(beg(1):end(1))
    else
       allocate(temp1(beg(1):end(1))); temp1 = 0.0_sys_size
       read(fh) temp1(beg(1):end(1))
       do i = beg(1),end(1)
          my_real(l2g(i)) = temp1(i)
       end do
       deallocate(temp1)
    end if
    if (.false.)                                                               &
    write(*,*)"cmpxr8_vector_read ",                                           &
      fh,offset," ",trim(data_desc)," ",                                       &
      my_real(beg(1):end(1)),beg(1),end(1),present(l2g)
#endif

  end subroutine cmpxr8_vector_read

!============================== CMPXR8_MATRIX_READ ===========================80
!
! Reads a 2 dimensional complex(system_r8) array to a file
!
!=============================================================================80

  subroutine cmpxr8_matrix_read(fh,offset,data_desc,my_real,beg,end,l2g)!tempProtectPrivate

    integer,                            intent(in)    :: fh
    integer(kind=lmpi_offset_kind),     intent(inout) :: offset
    character(len=*),                   intent(in)    :: data_desc
    complex(system_r8), dimension(:,:), intent(inout) :: my_real
    integer, dimension(:),              intent(in)    :: beg, end
    integer, dimension(:), optional,    intent(in)    :: l2g

    integer :: i
    complex(system_r8), dimension(:,:), allocatable :: temp2
    integer, parameter :: sys_size  = r8

#ifdef HAVE_MPI
    integer :: ierr,isize, ict,ifound,ipe
    integer :: dim1, dim2, iblock
    integer(kind=lmpi_offset_kind)      :: gsize,l_offset
    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

    logical :: reorder
    integer, parameter :: byte_size = 16

  continue

    reorder = present(l2g)
    ierr = 0
    dim1 = (end(1)-beg(1)) + 1
    dim2 = (end(2)-beg(2)) + 1

    if (.not.lmpi_repart) then

       isize = dim1*dim2 ! elements, not bytes

       allocate(off(0:lmpi_nproc-1)); off = 0
       call lmpi_allgather(isize,off)

       gsize = 0
       do i = 0,lmpi_nproc-1
          gsize = gsize + off(i)*byte_size ! bytes
       end do

       l_offset = 0
       if (lmpi_id > 0) then
          do i = 0,lmpi_id-1
             l_offset = l_offset + off(i)*byte_size ! bytes
          end do
       end if

       if (lmpi_mpiio_type == 1) then
          allocate(temp2(beg(1):end(1),dim2)); temp2 = 0.0_sys_size
          call MPI_FILE_READ_AT_ALL(fh, offset+l_offset, temp2, isize,         &
               MPI_DOUBLE_COMPLEX, mpi_status, ierr)
          if (.not.reorder) then
             my_real(beg(1):end(1),beg(2):end(2)) =                            &
                temp2(beg(1):end(1),beg(2):end(2))
          else
             do i = beg(2),end(2)
                my_real(beg(1):end(1),l2g(i)) = temp2(beg(1):end(1),i)
             end do
          end if
          deallocate(temp2)
       else
          if (.not.reorder) then
             if (lmpi_master) then
                read(fh) my_real(beg(1):end(1),beg(2):end(2))
                do ipe = 1,lmpi_nproc-1
                   dim2 = off(ipe)/dim1
                   allocate(temp2(dim1,dim2)); temp2 = 0.0_sys_size
                   read(fh) temp2(1:dim1,1:dim2)
                   call lmpi_send(temp2,dim1*dim2,ipe,100,ierr)
                   deallocate(temp2)
                end do
             else
                allocate(temp2(dim1,dim2)); temp2 = 0.0_dp
                call lmpi_recv(temp2,dim1*dim2,0,100,ierr)
                do i = 1,dim2
                   my_real(beg(1):end(1),i) = temp2(1:dim1,i)
                end do
                deallocate(temp2)
             end if
          else
             if (lmpi_master) then
                allocate(temp2(beg(1):end(1),beg(2):end(2)))
                temp2 = 0.0_sys_size
                read(fh) temp2(beg(1):end(1),beg(2):end(2))
                do i = beg(2),end(2)
                   my_real(beg(1):end(1),l2g(i)) = temp2(beg(1):end(1),i)
                end do
                deallocate(temp2)

                do ipe = 1,lmpi_nproc-1
                   dim2 = off(ipe)/dim1
                   allocate(temp2(dim1,dim2)); temp2 = 0.0_sys_size
                   read(fh) temp2(1:dim1,1:dim2)
                   call lmpi_send(temp2,dim1*dim2,ipe,100,ierr)
                   deallocate(temp2)
                end do
             else
                dim2 = off(lmpi_id)/dim1
                allocate(temp2(dim1,dim2)); temp2 = 0.0_dp
                call lmpi_recv(temp2,dim1*dim2,0,100,ierr)
                do i = beg(2),end(2)
                   my_real(beg(1):end(1),l2g(i)) = temp2(1:dim1,i)
                end do
                deallocate(temp2)
             end if
          end if
       end if
       offset = offset + gsize
       deallocate(off)

    else ! repartition

      isize = lmpi_mpiio_buffer_nelem
      if (lmpi_mpiio_nblock == 1) isize = lmpi_mpiio_nnodesg

       allocate(temp2(dim1,isize)); temp2 = 0.0_dp
       ict    = 0
       ifound = 0
       do iblock = 1,lmpi_mpiio_nblock
          if ((iblock == lmpi_mpiio_nblock).and.(lmpi_mpiio_nblock > 1)) then
             isize = lmpi_mpiio_nnodesg - ict
             deallocate(temp2); allocate(temp2(dim1,isize)); temp2 = 0.0_dp
          end if
          if (lmpi_master) then
             if (lmpi_mpiio_type == 1) then
                call MPI_FILE_READ_AT(fh, offset, temp2, isize*dim1,           &
                                      MPI_DOUBLE_COMPLEX, mpi_status, ierr)
             else
                read(fh) temp2(1:dim1,1:isize)
             end if
          end if
          offset = offset + isize*dim1*byte_size

          call lmpi_bcast(temp2)
          do i = 1,lmpi_mpiio_cnt(iblock)
             ifound = ifound + 1
             my_real(beg(1):end(1), lmpi_mpiio_put(ifound)) =                  &
                                temp2(1:dim1,lmpi_mpiio_get(ifound))
          end do
          ict = ict + isize
       end do
       deallocate(temp2)
    end if
    if (ierr /= 0) write(*,*)"ERROR READ Repart ",lmpi_id,trim(data_desc)
    call lmpi_conditional_stop(ierr,'lmpi:cmpxr8_matrix_read')

#else
    continue
    if (.not.present(l2g)) then
       read(fh) my_real(beg(1):end(1),beg(2):end(2))
    else
       allocate(temp2(beg(1):end(1),beg(2):end(2))); temp2 = 0.0_sys_size
       read(fh) temp2(beg(1):end(1),beg(2):end(2))
       do i = beg(2),end(2)
          my_real(beg(1):end(1),l2g(i)) = temp2(beg(1):end(1),i)
       end do
       deallocate(temp2)
    end if

    if (.false.)                                                               &
    write(*,*)"cmpxr8_matrix_read ",                                           &
      fh,offset," ",trim(data_desc)," ",my_real(beg(1):end(1),beg(2):end(2)),  &
      beg(1:2),end(1:2),present(l2g)
#endif

  end subroutine cmpxr8_matrix_read

!============================== CMPXR8_SCALAR_WRITE ==========================80
!
! Master writes a complex(system_r8) scalar from a file
!
!=============================================================================80

  subroutine cmpxr8_scalar_write_master(fh,offset,data_desc,my_real)!tempProtectPrivate

    integer,                        intent(in)    :: fh
    integer(kind=lmpi_offset_kind), intent(inout) :: offset
    character(len=*),               intent(in)    :: data_desc
    complex(system_r8),             intent(inout) :: my_real

#ifdef HAVE_MPI
    integer                             :: ierr, isize
    integer(kind=lmpi_offset_kind)      :: gsize
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr  = 0
    isize = 1 ! elements, not bytes
    gsize = 16

    if (lmpi_master)                                                           &
    call MPI_FILE_WRITE_AT(fh, offset, my_real, isize,                         &
         MPI_DOUBLE_COMPLEX, mpi_status, ierr)
    call lmpi_conditional_stop(ierr,'lmpi:cmpxr8_scalar_write_master')

    offset = offset + gsize
    if (.false.) write(*,*) "cmpxr8_scalar_write_master ",trim(data_desc)

#else
    continue
    write(fh) my_real

    if (.false.)                                                               &
    write(*,*) "cmpxr8_scalar_write_master ",                                  &
      fh,offset," ",trim(data_desc)," ",my_real
#endif

  end subroutine cmpxr8_scalar_write_master

!============================== CMPRX8_VECTOR_WRITE ==========================80
!
! Writes a 1 dimensional complex(system_r8) array to a file
!
!=============================================================================80

  subroutine cmpxr8_vector_write(fh,offset,data_desc,my_real,beg,end)!tempProtectPrivate

    integer,                          intent(in)    :: fh
    integer(kind=lmpi_offset_kind),   intent(inout) :: offset
    character(len=*),                 intent(in)    :: data_desc
    complex(system_r8), dimension(:), intent(in)    :: my_real
    integer,  dimension(:),           intent(in)    :: beg, end

#ifdef HAVE_MPI
    integer :: i, ierr,isize,ipe
    integer(kind=lmpi_offset_kind) :: gsize,l_offset

    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

    complex(system_r8), dimension(:), allocatable :: temp1

  continue

    ierr  = 0
    isize = (end(1)-beg(1))+1 ! elements, not bytes

    allocate(off(0:lmpi_nproc-1)); off = 0
    call lmpi_allgather(isize,off)

    gsize = 0
    do i = 0,lmpi_nproc-1
       gsize = gsize + off(i)*16 ! bytes
    end do

    l_offset = 0
    if (lmpi_id > 0) then
       do i = 0,lmpi_id-1
          l_offset = l_offset + off(i)*16 ! bytes
       end do
    end if

    if (lmpi_mpiio_type == 1) then
       allocate(temp1(isize))
       temp1 = my_real(beg(1):end(1))
       call MPI_FILE_WRITE_AT_ALL(fh, offset+l_offset, temp1, isize,           &
            MPI_DOUBLE_COMPLEX, mpi_status, ierr)
       deallocate(temp1)
    else
       if (lmpi_master) then
          write(fh) my_real(beg(1):end(1))
          do ipe = 1,lmpi_nproc-1
             isize = off(ipe)
             allocate(temp1(isize)); temp1 = cmplx(0.0_dp,0.0_dp,dp)
             call lmpi_recv(temp1,isize,ipe,100,ierr)
             write(fh) temp1(1:isize)
             deallocate(temp1)
          end do
       else
         allocate(temp1(isize))
         temp1 = my_real(beg(1):end(1))
         call lmpi_send(temp1,isize,0,100,ierr)
         deallocate(temp1)
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:cmpxr8_vector_write')
    deallocate(off)

    offset = offset + gsize
    if (.false.) write(*,*)"cmpxr8_vector_write",trim(data_desc)

#else
    continue
    write(fh) my_real(beg(1):end(1))

    if (.false.)                                                               &
    write(*,*) "cmpxr8_vector_write ",                                         &
      fh,offset," ",trim(data_desc)," ",my_real(beg(1):end(1)),beg(1),end(1)
#endif

  end subroutine cmpxr8_vector_write

!============================== CMPXR8_MATRIX_WRITE ==========================80
!
! Writes a 2 dimensional complex(system_r8) array to a file
!
!=============================================================================80

  subroutine cmpxr8_matrix_write(fh,offset,data_desc,my_real,beg,end)!tempProtectPrivate

    integer,                             intent(in)    :: fh
    integer(kind=lmpi_offset_kind),      intent(inout) :: offset
    character(len=*),                    intent(in)    :: data_desc
    complex(system_r8), dimension(:,:),  intent(in)    :: my_real
    integer,  dimension(:),              intent(in)    :: beg, end

    complex(system_r8), dimension(:,:), allocatable :: temp2
    integer :: dim1, dim2

#ifdef HAVE_MPI
    integer :: i, ierr,isize, ipe
    integer(kind=lmpi_offset_kind) :: gsize,l_offset

    integer, dimension(:), allocatable  :: off
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status

  continue

    ierr = 0
    dim1 = (end(1)-beg(1)) + 1
    dim2 = (end(2)-beg(2)) + 1
    isize = dim1*dim2 ! elements, not bytes

    allocate(off(0:lmpi_nproc-1)); off = 0
    call lmpi_allgather(isize,off)

    gsize = 0
    do i = 0,lmpi_nproc-1
       gsize = gsize + off(i)*16 ! bytes
    end do

    l_offset = 0
    if (lmpi_id > 0) then
       do i = 0,lmpi_id-1
          l_offset = l_offset + off(i)*16 ! bytes
       end do
    end if

    if (lmpi_mpiio_type == 1) then
       allocate(temp2(dim1,dim2))
       temp2 = my_real(beg(1):end(1),beg(2):end(2))
       call MPI_FILE_WRITE_AT_ALL(fh, offset+l_offset, temp2, isize,           &
            MPI_DOUBLE_COMPLEX, mpi_status, ierr)
       deallocate(temp2)
    else
       if (lmpi_master) then
          allocate(temp2(dim1,dim2))
          temp2 = my_real(beg(1):end(1),beg(2):end(2))
          write(fh) temp2
          deallocate(temp2)
          do ipe = 1,lmpi_nproc-1
             dim2 = off(ipe)/dim1
             allocate(temp2(dim1,dim2)); temp2 = 0.0_dp
             call lmpi_recv(temp2,dim1*dim2,ipe,100,ierr)
             write(fh) temp2(1:dim1,1:dim2)
             deallocate(temp2)
          end do
       else
         allocate(temp2(dim1,dim2))
         temp2 = my_real(beg(1):end(1),beg(2):end(2))
         call lmpi_send(temp2,dim1*dim2,0,100,ierr)
         deallocate(temp2)
       end if
    end if
    call lmpi_conditional_stop(ierr,'lmpi:cmpxr8_matrix_write')
    deallocate(off)

    offset = offset + gsize
    if (.false.) write(*,*)"cmpxr8_matrix_write ",trim(data_desc)
#else
    continue

    dim1 = (end(1)-beg(1)) + 1
    dim2 = (end(2)-beg(2)) + 1
    allocate(temp2(dim1,dim2))
    temp2 = my_real(beg(1):end(1),beg(2):end(2))
    write(fh) temp2(beg(1):end(1),beg(2):end(2))
    if (.false.)                                                               &
    write(*,*)"cmpxr8_matrix_write ",                                          &
      fh,offset," ",trim(data_desc)," ",                                       &
      temp2(beg(1):end(1),beg(2):end(2)),beg(1:2),end(1:2)
    deallocate(temp2)

#endif

  end subroutine cmpxr8_matrix_write

!-------------------------------------- END   Public Parts  -----

!-------------------------------------- START Private Parts -----

!tempStartExpand(lmpi_bcast)
!============================= CHARACTER_BCAST ===============================80
!
! Description goes here
!
!=============================================================================80

  subroutine character_bcast(buffer,transmitter,alt_comm) !tempProtectOnce

    character(len=*),           intent(inout) :: buffer
    integer, optional,          intent(in)    :: transmitter
    integer, optional,          intent(in)    :: alt_comm

#ifdef HAVE_MPI
    integer :: ierr, icomm
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = (present(alt_comm))

    if ((lmpi_nproc == 1).and.(.not. is_present)) return ! mpi with one proc

    icomm = lmpi_comm
    if (is_present) icomm = alt_comm

    if (present(transmitter) ) then
      call mpi_bcast(buffer, len(buffer),                                      &
        mpi_character, transmitter, icomm, ierr)
    else
      call mpi_bcast(buffer, len(buffer),                                      &
        mpi_character, 0, icomm, ierr)
    end if

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast of character failed......"
    endif bcast_error
#else
    if (.false.) write(*,*) alt_comm, transmitter, buffer ! avoid compiler warn
#endif

  end subroutine character_bcast

!================================== tempName_scalar_bcast
!
! broadcast function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_scalar_bcast(buffer,transmitter,alt_comm)

    tempType, intent(inout) :: buffer
    integer,  optional, intent(in)    :: transmitter
    integer,  optional, intent(in)    :: alt_comm

#ifdef HAVE_MPI
    integer :: ierr, icomm
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = present(alt_comm)

    if ((lmpi_nproc == 1).and.(.not. is_present)) return ! mpi with one proc

    icomm = lmpi_comm
    if (is_present) icomm = alt_comm

    if (present(transmitter) ) then
      call mpi_bcast(buffer, 1,                                                &
        tempMPIType, transmitter, icomm, ierr)
    else
      call mpi_bcast(buffer, 1,                                                &
        tempMPIType, 0, icomm, ierr)
    end if

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in tempSub failed..."
    endif bcast_error
#else
    if (.false.) write(*,*) transmitter, alt_comm, buffer ! avoid compiler warn
#endif

  end subroutine tempName_scalar_bcast


!================================== tempName_vector_bcast
!
! broadcast function for tempName vector data (private)
!
!=============================================================================80

  subroutine tempName_vector_bcast(buffer,transmitter,alt_comm)

    tempType, dimension(:), intent(inout) :: buffer
    integer,  optional,     intent(in)    :: transmitter
    integer,  optional,     intent(in)    :: alt_comm

#ifdef HAVE_MPI
    integer :: ierr, icomm
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = present(alt_comm)

    if ((lmpi_nproc == 1).and.(.not. is_present)) return !  mpi with one proc

    icomm = lmpi_comm
    if (is_present) icomm = alt_comm

    if (present(transmitter) ) then
      call mpi_bcast(buffer, size(buffer),                                     &
        tempMPIType, transmitter, icomm, ierr)
    else
      call mpi_bcast(buffer, size(buffer),                                     &
        tempMPIType, 0, icomm, ierr)
    end if

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in tempSub failed..."
    endif bcast_error
#else
    if (.false.) write(*,*) transmitter, alt_comm, buffer ! avoid compiler warn
#endif

  end subroutine tempName_vector_bcast

!================================ tempName_matrix_bcast
!
! broadcast function for tempName rank 2 data (private)
!
!=============================================================================80

  subroutine tempName_matrix_bcast(buffer,transmitter,alt_comm)

    tempType, dimension(:,:), intent(inout) :: buffer
    integer,  optional,       intent(in)    :: transmitter
    integer,  optional,       intent(in)    :: alt_comm

#ifdef HAVE_MPI
    integer :: ierr, icomm
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = present(alt_comm)

    if ((lmpi_nproc == 1).and.(.not. is_present)) return ! mpi with one proc

    icomm = lmpi_comm
    if (is_present) icomm = alt_comm

    if (present(transmitter) ) then
      call mpi_bcast(buffer, size(buffer,1)*size(buffer,2),                    &
        tempMPIType, transmitter, icomm, ierr)
    else
      call mpi_bcast(buffer, size(buffer,1)*size(buffer,2),                    &
        tempMPIType, 0, icomm, ierr)
    end if

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in tempSub failed..."
    endif bcast_error
#else
    if (.false.) write(*,*) transmitter, alt_comm, buffer ! avoid compiler warn
#endif

  end subroutine tempName_matrix_bcast


!================================ tempName_tensor_bcast
!
! broadcast function for tempName rank 2 data (private)
!
!=============================================================================80

  subroutine tempName_tensor_bcast(buffer,transmitter)

    tempType, dimension(:,:,:), intent(inout) :: buffer
    integer,  optional,         intent(in)    :: transmitter

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

    if (present(transmitter) ) then
      call mpi_bcast(buffer, size(buffer,1)*size(buffer,2)*size(buffer,3),     &
        tempMPIType, transmitter, lmpi_comm, ierr)
    else
      call mpi_bcast(buffer, size(buffer,1)*size(buffer,2)*size(buffer,3),     &
        tempMPIType, 0, lmpi_comm, ierr)
    end if

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in tempSub failed..."
    endif bcast_error
#else
    if (.false.) write(*,*) transmitter, buffer ! avoid compiler warnings
#endif

  end subroutine tempName_tensor_bcast


!tempEndExpand
!tempStartExpand(lmpi_reduce)
!================================== tempName_scalar_reduce
!
! reduction function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_scalar_reduce(bufferin, bufferout)

    tempType, intent(in)  :: bufferin
    tempType, intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    call mpi_reduce(bufferin, bufferout, 1,                                    &
         tempMPIType, mpi_sum, 0, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_scalar_reduce


!================================== tempName_vector_reduce
!
! reduction function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_reduce(bufferin, bufferout)

    tempType, dimension(:), intent(in)    :: bufferin
    tempType, dimension(:), intent(inout) :: bufferout

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    call mpi_reduce(bufferin, bufferout, size(bufferin,1),                     &
         tempMPIType, mpi_sum, 0, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_vector_reduce


!================================== tempName_matrix_reduce
!
! reduction function for tempName matrix (private)
!
!=============================================================================80

  subroutine tempName_matrix_reduce(bufferin, bufferout)

    tempType, dimension(:,:), intent(in)  :: bufferin
    tempType, dimension(:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    call mpi_reduce(bufferin, bufferout, size(bufferin,1)*size(bufferin,2),    &
         tempMPIType, mpi_sum, 0, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_matrix_reduce


!================================== tempName_tensor_reduce
!
! reduction function for tempName tensor (private)
!
!=============================================================================80

  subroutine tempName_tensor_reduce(bufferin, bufferout)

    tempType, dimension(:,:,:), intent(in)  :: bufferin
    tempType, dimension(:,:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    call mpi_reduce(bufferin, bufferout,                                       &
                    size(bufferin,1)*size(bufferin,2)*size(bufferin,3),        &
                    tempMPIType, mpi_sum, 0, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_tensor_reduce


!tempEndExpand
!tempStartExpand(lmpi_max)
!================================== tempName_scalar_max
!
! max function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_scalar_max(bufferin, bufferout, receiver)

    tempType, intent(in)  :: bufferin
    tempType, intent(out) :: bufferout
    integer, optional, intent(in)  :: receiver

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    if (present(receiver)) then
      call mpi_reduce(bufferin, bufferout, 1,                                  &
        tempMPIType, mpi_max, receiver, lmpi_comm, ierr)
    else
      call mpi_reduce(bufferin, bufferout, 1,                                  &
        tempMPIType, mpi_max, 0, lmpi_comm, ierr)
    end if

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine tempName_scalar_max

!================================== tempName_vector_max
!
! max function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_max(bufferin, bufferout, receiver)

    tempType, dimension(:), intent(in)  :: bufferin
    tempType, dimension(:), intent(out) :: bufferout
    integer,  optional,     intent(in)  :: receiver

#ifdef HAVE_MPI
    integer                               :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    if (present(receiver)) then
      call mpi_reduce(bufferin, bufferout, size(bufferin,1),                   &
        tempMPIType, mpi_max, receiver, lmpi_comm, ierr)
    else
      call mpi_reduce(bufferin, bufferout, size(bufferin,1),                   &
        tempMPIType, mpi_max, 0, lmpi_comm, ierr)
    end if

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine tempName_vector_max

!================================== tempName_matrix_max
!
! max function for tempName matrix (private)
!
!=============================================================================80

  subroutine tempName_matrix_max(bufferin, bufferout, receiver)

    tempType, dimension(:,:), intent(in)  :: bufferin
    tempType, dimension(:,:), intent(out) :: bufferout
    integer,  optional,       intent(in)  :: receiver

#ifdef HAVE_MPI
    integer                                 :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    if (present(receiver)) then
      call mpi_reduce(bufferin, bufferout, size(bufferin,1)*size(bufferin,2),  &
        tempMPIType, mpi_max, receiver, lmpi_comm, ierr)
    else
      call mpi_reduce(bufferin, bufferout, size(bufferin,1)*size(bufferin,2),  &
        tempMPIType, mpi_max, 0, lmpi_comm, ierr)
    end if

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine tempName_matrix_max

!tempEndExpand
!tempStartExpand(lmpi_min)
!================================== tempName_scalar_min
!
! min function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_scalar_min(bufferin, bufferout, receiver)

    tempType, intent(in)  :: bufferin
    tempType, intent(out) :: bufferout
    integer, optional, intent(in)  :: receiver

#ifdef HAVE_MPI
    integer                              :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    if (present(receiver)) then
      call mpi_reduce(bufferin, bufferout, 1,                                  &
        tempMPIType, mpi_min, receiver, lmpi_comm, ierr)
    else
      call mpi_reduce(bufferin, bufferout, 1,                                  &
        tempMPIType, mpi_min, 0, lmpi_comm, ierr)
    end if

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine tempName_scalar_min

!================================== tempName_vector_min
!
! min function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_min(bufferin, bufferout, receiver)

    tempType, dimension(:), intent(in)  :: bufferin
    tempType, dimension(:), intent(out) :: bufferout
    integer,  optional,     intent(in)  :: receiver

#ifdef HAVE_MPI
    integer                              :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    if (present(receiver)) then
      call mpi_reduce(bufferin, bufferout, size(bufferin,1),                   &
        tempMPIType, mpi_min, receiver, lmpi_comm, ierr)
    else
      call mpi_reduce(bufferin, bufferout, size(bufferin,1),                   &
        tempMPIType, mpi_min, 0, lmpi_comm, ierr)
    end if

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_reduce in tempSub failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine tempName_vector_min

!tempEndExpand
!tempStartExpand(lmpi_gather)
!================================== tempName_scalar_gather
!
! gather function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_scalar_gather(bufferin, arrayout, alt_root, alt_comm)

    tempType,                        intent(in)  :: bufferin
    tempType, dimension(lmpi_nproc), intent(out) :: arrayout

    integer, optional, intent(in) :: alt_comm, alt_root

#ifdef HAVE_MPI
    integer :: ierr, temp_comm, temp_root

    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI

    is_present = present(alt_comm)

    temp_comm = lmpi_comm
    if ( present(alt_comm) ) temp_comm = alt_comm
    temp_root = 0
    if ( present(alt_root) ) temp_root = alt_root

    if (lmpi_nproc == 1 .and. .not.is_present) then ! for mpi with one proc
      arrayout(1) = bufferin
      return
    endif

    call mpi_gather(                                                           &
      bufferin, 1, tempMPIType,                                                &
      arrayout, 1, tempMPIType, temp_root,                                     &
      temp_comm, ierr)

    gather_error: if( ierr /= mpi_success )then
      print*, "mpi_gather in tempSub failed..."
    endif gather_error
#else
    arrayout(1) = bufferin ! need to copy the data to the output
    if ( present(alt_comm) .and. .false.) write(*,*) alt_comm
    if ( present(alt_root) .and. .false.) write(*,*) alt_root
#endif

  end subroutine tempName_scalar_gather

!================================== tempName_vector_gather
!
! gather function for tempname vector (private)
!
!=============================================================================80

  subroutine tempName_vector_gather(bufferin, matrixout)

    tempType, dimension(:),   intent(in)  :: bufferin
    tempType, dimension(:,:), intent(out) :: matrixout

#ifdef HAVE_MPI
    integer :: ndim
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      matrixout(:,1) = bufferin
      return
    endif

    ndim = size(bufferin,1)
    call mpi_gather(                                                           &
      bufferin,  ndim, tempMPIType,                                            &
      matrixout, ndim, tempMPIType, 0,                                         &
      lmpi_comm, ierr)

    gather_error: if( ierr /= mpi_success )then
      print*, "mpi_gather in tempSub failed..."
    endif gather_error
#else
    matrixout(:,1) = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_vector_gather
!tempEndExpand


!tempStartExpand(lmpi_gatherv)
!================================== tempName_vector_gatherv
!
! gatherv function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_gatherv(bufferin, countin, bufferout, countsout,&
    displace, root, comm, nproc)

    tempType, dimension(:), intent(in)  :: bufferin
    integer,                        intent(in)  :: countin
    tempType, dimension(:), intent(out) :: bufferout
    integer, dimension(lmpi_nproc), intent(in)  :: countsout
    integer,                         optional, intent(in)  :: root
    integer,                         optional, intent(in)  :: comm
    integer,                         optional, intent(in)  :: nproc
    integer, dimension(:), optional, intent(in) :: displace

#ifdef HAVE_MPI
    integer :: n, ierr, local_root, local_comm, temp_proc
    integer, dimension(:), allocatable :: displacement
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = present(comm)

    if (lmpi_nproc == 1 .and. .not.is_present) then ! for mpi with one proc
      bufferout(1:countsout(1)) = bufferin(1:countsout(1))
      return
    endif

    temp_proc = lmpi_nproc
    if ( present(nproc) ) temp_proc = nproc

    allocate(displacement(temp_proc))

    if ( present(displace) ) then
      displacement = displace
    else
      displacement = 0
      do n=2,lmpi_nproc
        displacement(n) = displacement(n-1) + countsout(n-1)
      end do
    endif

    local_root = 0
    if (present(root)) local_root = root

    local_comm = lmpi_comm
    if (present(comm)) local_comm = comm

    call mpi_gatherv(                                                          &
      bufferin, countin, tempMPIType,                                          &
      bufferout, countsout, displacement, tempMPIType,                         &
      local_root, local_comm, ierr)

    gatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_gatherv in tempSub failed..."
    endif gatherv_error

    deallocate(displacement)
#else
    bufferout(1:countsout(1)) = bufferin(1:countsout(1))
    if (present(root)) return
    if (present(comm)) return
    if (present(displace)) return
    if (present(nproc)) return
    if ( .false. ) write(*,*) countin
#endif

  end subroutine tempName_vector_gatherv

!================================== tempName_matrix_gatherv
!
! gatherv function for tempName matrix (private)
!
!=============================================================================80

  subroutine tempName_matrix_gatherv(bufferin, countin, bufferout, countsout,&
    root, comm )

    tempType, dimension(:,:), intent(in)  :: bufferin
    integer,                        intent(in)  :: countin
    tempType, dimension(:,:), intent(out) :: bufferout
    integer,  dimension(lmpi_nproc), intent(in)  :: countsout
    integer,                         optional, intent(in)  :: root
    integer,                         optional, intent(in)  :: comm

#ifdef HAVE_MPI
    integer :: n, leadingdimension, ierr, local_root, local_comm
    integer, dimension(lmpi_nproc) :: displacement, unrolledcounts
    logical :: is_present
#endif

    continue

#ifdef HAVE_MPI
    is_present = present(comm)

    if (lmpi_nproc == 1 .and. .not.is_present) then ! for mpi with one proc
      bufferout(:,1:countsout(1)) = bufferin(:,1:countsout(1))
      return
    endif

    leadingdimension = size(bufferin,1)
    unrolledcounts = countsout * leadingdimension
    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + unrolledcounts(n-1)
    end do

    local_root = 0
    if (present(root)) local_root = root

    local_comm = lmpi_comm
    if (present(comm)) local_comm = comm

    call mpi_gatherv(                                                          &
      bufferin, leadingdimension*countin, tempMPIType,                         &
      bufferout, unrolledcounts, displacement, tempMPIType,                    &
      local_root, local_comm, ierr)

    gatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_gatherv in tempSub failed..."
    endif gatherv_error
#else
    bufferout(:,1:countsout(1)) = bufferin(:,1:countsout(1))
    if (present(root)) return
    if (present(comm)) return
    if ( .false. ) write(*,*) countin
#endif

  end subroutine tempName_matrix_gatherv

!================================== tempName_tensor_gatherv
!
! gatherv function for tempName matrix (private)
!
!=============================================================================80

  subroutine tempName_tensor_gatherv(bufferin, counts, bufferout)

    tempType, dimension(:,:,:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: counts
    tempType, dimension(:,:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: n, leadingdimension, nextdimension, ierr
    integer, dimension(lmpi_nproc) :: displacement, unrolledcounts
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(:,:,1:counts(1)) = bufferin(:,:,1:counts(1))
      return
    endif

    leadingdimension = size(bufferin,1)
    nextdimension    = size(bufferin,2)
    unrolledcounts = counts * leadingdimension * nextdimension
    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + unrolledcounts(n-1)
    end do

    call mpi_gatherv(                                                       &
      bufferin, unrolledcounts(lmpi_id+1), tempMPIType,                        &
      bufferout, unrolledcounts, displacement, tempMPIType,                    &
      0, lmpi_comm, ierr)

    gatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_gatherv in tempSub failed..."
    endif gatherv_error
#else
    bufferout(:,:,1:counts(1)) = bufferin(:,:,1:counts(1))
#endif

  end subroutine tempName_tensor_gatherv
!tempEndExpand

!tempStartExpand(lmpi_allgather)
!================================== tempName_scalar_allgather
!
! allgather function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_scalar_allgather(bufferin, arrayout)

    tempType,                        intent(in)    :: bufferin
    tempType, dimension(lmpi_nproc), intent(out)   :: arrayout

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      arrayout(1) = bufferin
      return
    endif

    call mpi_allgather(                                                        &
      bufferin, 1, tempMPIType,                                                &
      arrayout, 1, tempMPIType,                                                &
      lmpi_comm, ierr)

    allgather_error: if( ierr /= mpi_success )then
      print*, "mpi_allgather in tempSub failed..."
    endif allgather_error
#else
    arrayout(1) = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_scalar_allgather

!================================== tempName_vector_allgather
!
! allgather function for tempName scalar (private)
!
!=============================================================================80

  subroutine tempName_vector_allgather(bufferin, matrixout)

    tempType, dimension(:),   intent(in)    :: bufferin
    tempType, dimension(:,:), intent(out)   :: matrixout

#ifdef HAVE_MPI
    integer :: ndim
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      matrixout(:,1) = bufferin
      return
    endif

    ndim = size(bufferin,1)
    call mpi_allgather(                                                        &
      bufferin,  ndim, tempMPIType,                                            &
      matrixout, ndim, tempMPIType,                                            &
      lmpi_comm, ierr)

    allgather_error: if( ierr /= mpi_success )then
      print*, "mpi_allgather in tempSub failed..."
    endif allgather_error
#else
    matrixout(:,1) = bufferin ! need to copy the data to the output
#endif

  end subroutine tempName_vector_allgather
!tempEndExpand

!tempStartExpand(lmpi_allgatherv)
!================================== tempName_vector_allgatherv
!
! allgatherv function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_allgatherv(bufferin, counts, bufferout)

    tempType, dimension(:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: counts
    tempType, dimension(:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: n, ierr
    integer, dimension(lmpi_nproc) :: displacement
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(1:counts(1)) = bufferin(1:counts(1))
      return
    endif

    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + counts(n-1)
    end do

    call mpi_allgatherv(                                                       &
      bufferin, counts(lmpi_id+1), tempMPIType,                                &
      bufferout, counts, displacement, tempMPIType,                            &
      lmpi_comm, ierr)

    allgatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_allgatherv in tempSub failed..."
    endif allgatherv_error
#else
    bufferout(1:counts(1)) = bufferin(1:counts(1))
#endif

  end subroutine tempName_vector_allgatherv

!================================== tempName_matrix_allgatherv
!
! allgatherv function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_matrix_allgatherv(bufferin, counts, bufferout)

    tempType, dimension(:,:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: counts
    tempType, dimension(:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: n, leadingdimension, ierr
    integer, dimension(lmpi_nproc) :: displacement, unrolledcounts
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(:,1:counts(1)) = bufferin(:,1:counts(1))
      return
    endif

    leadingdimension = size(bufferin,1)
    unrolledcounts = counts * leadingdimension
    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + unrolledcounts(n-1)
    end do

    call mpi_allgatherv(                                                       &
      bufferin, unrolledcounts(lmpi_id+1), tempMPIType,                        &
      bufferout, unrolledcounts, displacement, tempMPIType,                    &
      lmpi_comm, ierr)

    allgatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_allgatherv in tempSub failed..."
    endif allgatherv_error
#else
    bufferout(:,1:counts(1)) = bufferin(:,1:counts(1))
#endif

  end subroutine tempName_matrix_allgatherv

!================================== tempName_tensor_allgatherv
!
! allgatherv function for tempName tensor (private)
!
!=============================================================================80

  subroutine tempName_tensor_allgatherv(bufferin, counts, bufferout)

    tempType, dimension(:,:,:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: counts
    tempType, dimension(:,:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: n, leadingdimension, nextdimension, ierr
    integer, dimension(lmpi_nproc) :: displacement, unrolledcounts
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(:,:,1:counts(1)) = bufferin(:,:,1:counts(1))
      return
    endif

    leadingdimension = size(bufferin,1)
    nextdimension    = size(bufferin,2)
    unrolledcounts = counts * leadingdimension * nextdimension
    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + unrolledcounts(n-1)
    end do

    call mpi_allgatherv(                                                       &
      bufferin, unrolledcounts(lmpi_id+1), tempMPIType,                        &
      bufferout, unrolledcounts, displacement, tempMPIType,                    &
      lmpi_comm, ierr)

    allgatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_allgatherv in tempSub failed..."
    endif allgatherv_error
#else
    bufferout(:,:,1:counts(1)) = bufferin(:,:,1:counts(1))
#endif

  end subroutine tempName_tensor_allgatherv
!tempEndExpand

!tempStartExpand(lmpi_alltoall)
!================================== tempName_vector_alltoall
!
! alltoall function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_alltoall(bufferin, bufferout)

    tempType, dimension(lmpi_nproc), intent(in)  :: bufferin
    tempType, dimension(lmpi_nproc), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    call mpi_alltoall(                                                         &
      bufferin,  1, tempMPIType,                                               &
      bufferout, 1, tempMPIType,                                               &
      lmpi_comm, ierr)

    alltoall_error: if( ierr /= mpi_success )then
      print*, "mpi_alltoall in tempSub failed..."
    endif alltoall_error
#else
    bufferout = bufferin
#endif

  end subroutine tempName_vector_alltoall
!tempEndExpand

!tempStartExpand(lmpi_alltoallv)
!================================== tempName_vector_alltoallv
!
! alltoallv function for tempName vector (private)
!
!=============================================================================80

  subroutine tempName_vector_alltoallv(bufferin, countin, bufferout, countout)

    tempType, dimension(:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: countin
    tempType, dimension(:), intent(out) :: bufferout
    integer,  dimension(lmpi_nproc), intent(in)  :: countout

#ifdef HAVE_MPI
    integer :: n, ierr
    integer, dimension(lmpi_nproc) :: send_disp, recv_disp
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(1:countout(1)) = bufferin(1:countin(1))
      return
    endif

    send_disp = 0
    do n=2,lmpi_nproc
      send_disp(n) = send_disp(n-1) + countin(n-1)
    end do

    recv_disp = 0
    do n=2,lmpi_nproc
      recv_disp(n) = recv_disp(n-1) + countout(n-1)
    end do

    call mpi_alltoallv(                                                        &
      bufferin, countin, send_disp, tempMPIType,                               &
      bufferout, countout, recv_disp, tempMPIType,                             &
      lmpi_comm, ierr)

    alltoallv_error: if( ierr /= mpi_success )then
      print*, "mpi_alltoallv in tempSub failed..."
    endif alltoallv_error
#else
    bufferout(1:countout(1)) = bufferin(1:countin(1))
#endif

  end subroutine tempName_vector_alltoallv

!================================== tempName_matrix_alltoallv
!
! alltoallv function for tempName matrix (private)
!
!=============================================================================80

  subroutine tempName_matrix_alltoallv(bufferin, countin, bufferout, countout)

    tempType, dimension(:,:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: countin
    tempType, dimension(:,:), intent(out) :: bufferout
    integer,  dimension(lmpi_nproc), intent(in)  :: countout

#ifdef HAVE_MPI
    integer :: n, ierr, ldim
    integer, dimension(lmpi_nproc) :: send_count, recv_count
    integer, dimension(lmpi_nproc) :: send_disp, recv_disp
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(:,1:countout(1)) = bufferin(:,1:countin(1))
      return
    endif

    ldim = size(bufferin,1)

    send_count = ldim*countin
    send_disp = 0
    do n=2,lmpi_nproc
      send_disp(n) = send_disp(n-1) + send_count(n-1)
    end do

    recv_count = ldim*countout
    recv_disp = 0
    do n=2,lmpi_nproc
      recv_disp(n) = recv_disp(n-1) + recv_count(n-1)
    end do

    call mpi_alltoallv(                                                        &
      bufferin, send_count, send_disp, tempMPIType,                               &
      bufferout, recv_count, recv_disp, tempMPIType,                             &
      lmpi_comm, ierr)

    alltoallv_error: if( ierr /= mpi_success )then
      print*, "mpi_alltoallv in tempSub failed..."
    endif alltoallv_error
#else
    bufferout(:,1:countout(1)) = bufferin(:,1:countin(1))
#endif

  end subroutine tempName_matrix_alltoallv

!================================== tempName_tensor_alltoallv
!
! alltoallv function for tempName tensor (private)
!
!=============================================================================80

  subroutine tempName_tensor_alltoallv(bufferin, countin, bufferout, countout)

    tempType, dimension(:,:,:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: countin
    tempType, dimension(:,:,:), intent(out) :: bufferout
    integer,  dimension(lmpi_nproc), intent(in)  :: countout

#ifdef HAVE_MPI
    integer :: n, ierr, ldim
    integer, dimension(lmpi_nproc) :: send_count, recv_count
    integer, dimension(lmpi_nproc) :: send_disp, recv_disp
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(:,:,1:countout(1)) = bufferin(:,:,1:countin(1))
      return
    endif

    ldim = size(bufferin,1) * size(bufferin,2)

    send_count = ldim*countin
    send_disp = 0
    do n=2,lmpi_nproc
      send_disp(n) = send_disp(n-1) + send_count(n-1)
    end do

    recv_count = ldim*countout
    recv_disp = 0
    do n=2,lmpi_nproc
      recv_disp(n) = recv_disp(n-1) + recv_count(n-1)
    end do

    call mpi_alltoallv(                                                        &
      bufferin, send_count, send_disp, tempMPIType,                            &
      bufferout, recv_count, recv_disp, tempMPIType,                           &
      lmpi_comm, ierr)

    alltoallv_error: if( ierr /= mpi_success )then
      print*, "mpi_alltoallv in tempSub failed..."
    endif alltoallv_error
#else
    bufferout(:,:,1:countout(1)) = bufferin(:,:,1:countin(1))
#endif

  end subroutine tempName_tensor_alltoallv
!tempEndExpand

!tempStartExpand(lmpi_alltoallv2)
!================================== tempName_matrix_alltoallv2
!
! alltoallv function for tempName matrix with send/recv (private)
!
!=============================================================================80

  subroutine tempName_matrix_alltoallv2(bufferin, countin, bufferout, countout)

    tempType, dimension(:,:), intent(in)  :: bufferin
    integer,  dimension(lmpi_nproc), intent(in)  :: countin
    tempType, dimension(:,:), intent(out) :: bufferout
    integer,  dimension(lmpi_nproc), intent(in)  :: countout

#ifdef HAVE_MPI
    integer :: ierr, ldim, mpitag, length
    integer :: nrecv, nsend
    integer :: displacement, in_displacement, out_displacement
    integer :: i, j, i_other_proc
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(:,1:countout(1)) = bufferin(:,1:countin(1))
      return
    endif

    ldim = size(bufferin,1)

    nrecv = 0
    displacement=0
    recv_loop : do i_other_proc = 0, lmpi_nproc-1
      if_not_me_r : if (i_other_proc /= lmpi_id) then
        length = countout(i_other_proc+1)
        rlen : if (length>0) then
          nrecv  = nrecv + 1
          mpitag = 1
          call lmpi_irecv(bufferout, ldim*length,                     &
                          i_other_proc, mpitag, recvreq(nrecv), ierr, &
                          1, 1+displacement)
          displacement = displacement + length
        end if rlen
      else
        out_displacement = displacement
      end if if_not_me_r
    end do recv_loop

    nsend=0
    displacement=0
    send_loop : do i_other_proc = 0, lmpi_nproc-1
      if_not_me_s : if (i_other_proc /= lmpi_id) then
         length = countin(i_other_proc+1)
         slen : if (length>0) then
          nsend  = nsend + 1
          mpitag = 1
          call lmpi_isend( bufferin, ldim*length,                      &
                           i_other_proc, mpitag, sendreq(nsend), ierr, &
                           1, 1+displacement)
          displacement = displacement + length
        end if slen
      else
        in_displacement = displacement
      end if if_not_me_s
    end do send_loop

    do j = 1, countin(lmpi_id+1)
      do i = 1, ldim
        bufferout(i,j+out_displacement) = bufferin(i,j+in_displacement)
      end do
    end do

    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

#else
    bufferout(:,1:countout(1)) = bufferin(:,1:countin(1))
#endif

  end subroutine tempName_matrix_alltoallv2
!tempEndExpand

!tempStartExpand(lmpi_send_recv)
!================================== tempName_vector_send_recv
!
! True send_recv (MPI_sendrecv) function for tempName vector (private)
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_vector_send_recv(sendbuf,sendcount,dest,sendtag,&
                                       recvbuf,recvcount,src, recvtag,ierr)

    tempType, dimension(:), intent(in)  :: sendbuf
    tempType, dimension(:), intent(out) :: recvbuf
    integer,                intent(in)  :: sendcount, dest, sendtag
    integer,                intent(in)  :: recvcount, src,  recvtag
    integer,                intent(out) :: ierr

#ifdef HAVE_MPI
    integer, dimension(MPI_STATUS_SIZE) :: mpi_status
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
#endif

    integer :: idest, isrc, isendtag, irecvtag

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      recvbuf = sendbuf
      return
    endif

    isendtag = sendtag
    if (sendtag == i_any_tag) isendtag = MPI_ANY_TAG

    irecvtag = recvtag
    if (recvtag == i_any_tag) irecvtag = MPI_ANY_TAG

    idest = dest
    if (dest == i_any_source) idest    = MPI_ANY_SOURCE

    isrc = src
    if (src == i_any_source)  isrc     = MPI_ANY_SOURCE

    call mpi_sendrecv(sendbuf,sendcount,tempMPIType,idest,isendtag,  &
                      recvbuf,recvcount,tempMPIType,isrc, irecvtag,  &
                      lmpi_comm,mpi_status,ierr)

    send_recv_error: if( ierr /= mpi_success )then
      print*, "mpi_sendrecv in tempSub failed..."
    endif send_recv_error
#else
    recvbuf = sendbuf ! need to copy the data to the output
! avoid compiler warnings
    ierr = 0
    if (sendcount==recvcount) then
       idest = dest
       isrc  = src
       isendtag = sendtag
       irecvtag = recvtag
       if (.false.) write(*,*) idest,isrc,isendtag,irecvtag
    end if
#endif

  end subroutine tempName_vector_send_recv
!tempEndExpand

!tempStartExpand(lmpi_send)
!================================== tempName_scalar_send
!
! MPI_send function for tempName scalar.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_scalar_send(buf,dest,tag,ierr,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType,               intent(in)  :: buf
    integer,                intent(in)  :: dest, tag
    integer,                intent(out) :: ierr
    integer, optional,      intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, idest, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    idest = dest
    if (dest == i_any_source) idest = MPI_ANY_SOURCE

    call mpi_send(buf,1,tempMPIType,idest,itag,icomm,ierr)

    send_error: if( ierr /= mpi_success )then
      print*, "mpi_send in tempSub failed..."
    endif send_error
#else
! avoid compiler warnings
    ierr = 1
    if (.false.) write(*,*) buf, dest, tag, alt_comm
    write(*,*) "tempName_scalar_send is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_scalar_send

!================================== tempName_vector_send
!
! MPI_send function for tempName vector.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_vector_send(buf,counts,dest,tag,ierr,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:), intent(in)  :: buf
    integer,                intent(in)  :: counts, dest, tag
    integer,                intent(out) :: ierr
    integer, optional,      intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, idest, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    idest = dest
    if (dest == i_any_source) idest = MPI_ANY_SOURCE

    call mpi_send(buf,counts,tempMPIType,idest,itag,icomm,ierr)

    send_error: if( ierr /= mpi_success )then
      print*, "mpi_send in tempSub failed..."
    endif send_error
#else
! avoid compiler warnings
    ierr = 1
    if (.false.) write(*,*) counts, buf, dest, tag, alt_comm
    write(*,*) "tempName_vector_send is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_vector_send

!================================== tempName_matrix_send
!
! MPI_send function for tempName contingous matrix.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_matrix_send(buf,counts,dest,tag,ierr,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:,:), intent(in)  :: buf
    integer,                  intent(in)  :: counts, dest, tag
    integer,                  intent(out) :: ierr
    integer, optional,        intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, idest, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    idest = dest
    if (dest == i_any_source) idest = MPI_ANY_SOURCE

    call mpi_send(buf,counts,tempMPIType,idest,itag,icomm,ierr)

    send_error: if( ierr /= mpi_success )then
      print*, "mpi_send in tempSub failed..."
    endif send_error
#else
! avoid compiler warnings
    if (counts==(counts+1)) then
       write(*,*) tag, dest, counts
       write(*,*) buf
    end if
    if (.false. .and. present(alt_comm) ) write(*,*) alt_comm
    ierr = 1
    write(*,*) "tempName_matrix_send is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_matrix_send

!================================== tempName_tensor_send
!
! MPI_send function for tempName contingous matrix.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_tensor_send(buf,counts,dest,tag,ierr,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:,:,:), intent(in)  :: buf
    integer,                  intent(in)  :: counts, dest, tag
    integer,                  intent(out) :: ierr
    integer, optional,        intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, idest, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    idest = dest
    if (dest == i_any_source) idest = MPI_ANY_SOURCE

    call mpi_send(buf,counts,tempMPIType,idest,itag,icomm,ierr)

    send_error: if( ierr /= mpi_success )then
      print*, "mpi_send in tempSub failed..."
    endif send_error
#else
! avoid compiler warnings
    if (counts==(counts+1)) then
       write(*,*) tag, dest, counts
       write(*,*) buf
    end if
    if (.false. .and. present(alt_comm) ) write(*,*) alt_comm
    ierr = 1
    write(*,*) "tempName_tensor_send is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_tensor_send

!tempEndExpand

!tempStartExpand(lmpi_recv)
!================================== tempName_scalar_recv
!
! MPI_recv function for tempName scalar.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_scalar_recv(buf,src,tag,ierr,jtag,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType,               intent(out) :: buf
    integer,                intent(in)  :: src, tag
    integer,                intent(out) :: ierr
    integer, optional,      intent(out) :: jtag
    integer, optional,      intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, isrc, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
    integer, dimension(lmpi_status_size) :: status
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    isrc = src
    if (src == i_any_source) isrc = MPI_ANY_SOURCE

!   write(*,*)' tempName_scalar_recv; src,tag,isrc,itag = ',src,tag,isrc,itag

    call mpi_recv(buf,1,tempMPIType,&
                  isrc,itag,icomm,status,ierr)
    if (present(jtag)) jtag = status(MPI_TAG)

    recv_error: if( ierr /= mpi_success )then
      print*, "mpi_recv in tempSub failed..."
    endif recv_error
#else
! avoid compiler warnings
    if (.false.) then
      buf = tempConstant
      write(*,*) src, tag
    end if
    if (present(jtag)) jtag = 1
    if (.false. .and. present(alt_comm) ) write(*,*) alt_comm
    ierr = 0
    write(*,*) "tempName_scalar_recv is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_scalar_recv

!================================== tempName_vector_recv
!
! MPI_recv function for tempName vector.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_vector_recv(buf,counts,src,tag,ierr,jtag,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:), intent(out) :: buf
    integer,                intent(in)  :: counts, src, tag
    integer,                intent(out) :: ierr
    integer, optional,      intent(out) :: jtag
    integer, optional,      intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, isrc, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
    integer, dimension(lmpi_status_size) :: status
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    isrc = src
    if (src == i_any_source) isrc = MPI_ANY_SOURCE

!   write(*,*)' tempName_vector_recv; src,tag,isrc,itag = ',src,tag,isrc,itag

    call mpi_recv(buf,counts,tempMPIType,&
                  isrc,itag,icomm,status,ierr)
    if (present(jtag)) jtag = status(MPI_TAG)

    recv_error: if( ierr /= mpi_success )then
      print*, "mpi_recv in tempSub failed..."
    endif recv_error
#else
! avoid compiler warnings
    if (counts==(counts+1)) then
      buf(1) = tempConstant
      write(*,*) src, tag
    end if
    if (present(jtag)) jtag = 1
    if (.false. .and. present(alt_comm) ) write(*,*) alt_comm
    ierr = 0
    write(*,*) "tempName_vector_recv is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_vector_recv

!================================== tempName_matrix_recv
!
! MPI_recv function for tempName contiguous vector.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_matrix_recv(buf,counts,src,tag,ierr,jtag,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:,:), intent(out) :: buf
    integer,                  intent(in)  :: counts, src, tag
    integer,                  intent(out) :: ierr
    integer, optional,        intent(out) :: jtag
    integer, optional,        intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, isrc, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
    integer, dimension(lmpi_status_size) :: status
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    isrc = src
    if (src == i_any_source) isrc = MPI_ANY_SOURCE

!   write(*,*)' tempName_maxtrix_recv; src,tag,isrc,itag = ',src,tag,isrc,itag

    call mpi_recv(buf,counts,tempMPIType,&
                  isrc,itag,icomm,status,ierr)
    if (present(jtag)) jtag = status(MPI_TAG)

    recv_error: if( ierr /= mpi_success )then
      print*, "mpi_recv in tempSub failed..."
    endif recv_error
#else
! avoid compiler warnings
    if (counts==(counts+1)) then
      buf(1,1) = tempConstant
      write(*,*) src, tag
    end if
    if (present(jtag)) jtag = 1
    if (.false. .and. present(alt_comm) ) write(*,*) alt_comm
    ierr = 1
    write(*,*) "tempName_matrix_recv is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_matrix_recv

!================================== tempName_tensor_recv
!
! MPI_recv function for tempName contiguous vector.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use -1000. (TBD will replace with LMPI_..)
!
!=============================================================================80

  subroutine tempName_tensor_recv(buf,counts,src,tag,ierr,jtag,alt_comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:,:,:), intent(out) :: buf
    integer,                  intent(in)  :: counts, src, tag
    integer,                  intent(out) :: ierr
    integer, optional,        intent(out) :: jtag
    integer, optional,        intent(in)  :: alt_comm

#ifdef HAVE_MPI
    integer :: itag, isrc, icomm
    integer, parameter :: i_any_source = -1000
    integer, parameter :: i_any_tag    = -1000
    integer, dimension(lmpi_status_size) :: status
#endif

    continue

#ifdef HAVE_MPI

    icomm = lmpi_comm
    if (present(alt_comm)) icomm = alt_comm

    itag = tag
    if (tag == i_any_tag) itag = MPI_ANY_TAG

    isrc = src
    if (src == i_any_source) isrc = MPI_ANY_SOURCE

!   write(*,*)' tempName_tensor_recv; src,tag,isrc,itag = ',src,tag,isrc,itag

    call mpi_recv(buf,counts,tempMPIType,&
                  isrc,itag,icomm,status,ierr)
    if (present(jtag)) jtag = status(MPI_TAG)

    recv_error: if( ierr /= mpi_success )then
      print*, "mpi_recv in tempSub failed..."
    endif recv_error
#else
! avoid compiler warnings
    if (counts==(counts+1)) then
      buf(1,1,1) = tempConstant
      write(*,*) src, tag
    end if
    if (present(jtag)) jtag = 1
    if (.false. .and. present(alt_comm) ) write(*,*) alt_comm
    ierr = 1
    write(*,*) "tempName_tensor_recv is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_tensor_recv

!tempEndExpand

!tempStartExpand(lmpi_isend)
!================================== tempName_vector_isend
!
! MPI_isend function for tempName vector.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use LMPI_ANY_TAG and LMPI_ANY_SOURCE.
!
!=============================================================================80

  subroutine tempName_vector_isend(buf,counts,dest,tag,request, &
               ierr,ioff1,comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:), intent(in)    :: buf
    integer,                intent(in)    :: counts, dest, tag
    integer,                intent(inout) :: request
    integer,                intent(out)   :: ierr
    integer, optional,      intent(in)    :: ioff1 ! if index > 1
    integer, optional,      intent(in)    :: comm

#ifdef HAVE_MPI

    integer :: fwa1,fwa2,local_comm

    continue

    fwa2 = 1
    fwa1 = fwa2
    if (present(ioff1)) fwa1 = ioff1

    local_comm = lmpi_comm
    if ( present(comm) ) local_comm = comm

!   write(*,*)' tempName_vector_isend; dest,tag,fwa1 = ',dest,tag,fwa1

    call mpi_isend(buf(fwa1),counts,tempMPIType, &
                   dest,tag,local_comm,request,ierr)

    send_error: if( ierr /= mpi_success )then
      print*, "mpi_isend in tempSub failed..."
    endif send_error
#else
! avoid compiler warnings
    if (counts==(1+counts)) then
      write(*,*) buf
      write(*,*) dest, tag, request
      if (present(ioff1)) write(*,*) ioff1
      if (present(comm)) write(*,*) comm
    end if
    ierr = 1
    write(*,*) "tempName_vector_isend is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_vector_isend

!================================== tempName_matrix_isend
!
! MPI_isend function for tempName contingous matrix.
!
! For MPI_ANY_TAG or MPI_ANY_SOURCE use LMPI_ANY_TAG and LMPI_ANY_SOURCE.
!
!=============================================================================80

  subroutine tempName_matrix_isend(buf,counts,dest,tag,request, &
              ierr,ioff1,ioff2,comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:,:), intent(in)    :: buf
    integer,                  intent(in)    :: counts, dest, tag
    integer,                  intent(inout) :: request
    integer,                  intent(out)   :: ierr
    integer, optional,        intent(in)    :: ioff1, ioff2
    integer, optional,        intent(in)    :: comm

#ifdef HAVE_MPI
    integer :: fwa1,fwa2,local_comm

    continue

    fwa1 = 1
    if (present(ioff1)) fwa1 = ioff1
    fwa2 = 1
    if (present(ioff2)) fwa2 = ioff2

    local_comm = lmpi_comm
    if ( present(comm) ) local_comm = comm

!   write(*,*)' tempName_matrix_isend; dest,tag,fwa1,fwa2 = ',dest,tag,fwa1,fwa2

    call mpi_isend(buf(fwa1,fwa2),counts,tempMPIType, &
                   dest,tag,local_comm,request,ierr)

    send_error: if( ierr /= mpi_success )then
      print*, "mpi_isend in tempSub failed..."
    endif send_error
#else
! avoid compiler warnings
    if (counts==(1+counts)) then
      write(*,*) buf, dest, tag, request
      if (present(ioff1)) write(*,*) ioff1
      if (present(ioff2)) write(*,*) ioff2
      if (present(comm)) write(*,*) comm
    end if
    ierr = 1
    write(*,*) "tempName_matrix_isend is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_matrix_isend
!tempEndExpand


!tempStartExpand(lmpi_irecv)
!================================== tempName_vector_irecv
!
! MPI_irecv function for tempName vector.
!
!=============================================================================80

  subroutine tempName_vector_irecv(buf,counts,src,tag,request, &
              ierr,ioff1,comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:), intent(out)   :: buf
    integer,                intent(in)    :: counts, src, tag
    integer,                intent(inout) :: request
    integer,                intent(out)   :: ierr
    integer, optional,      intent(in)    :: ioff1
    integer, optional,      intent(in)    :: comm

#ifdef HAVE_MPI

    integer :: fwa1,fwa2,local_comm

    continue

    fwa2 = 1
    fwa1 = fwa2
    if (present(ioff1)) fwa1 = ioff1

    local_comm = lmpi_comm
    if ( present(comm) ) local_comm = comm

!   write(*,*)' tempName_vector_irecv; src,tag,fwa1 = ',src,tag,fwa1

    call mpi_irecv(buf(fwa1),counts,tempMPIType,src, &
          tag,local_comm,request,ierr)

    irecv_error: if( ierr /= mpi_success )then
      print*, "mpi_irecv in tempSub failed..."
    endif irecv_error
#else
! avoid compiler warnings
    if (counts==(1+counts)) then
      buf(1) = tempConstant
      write(*,*) src, tag, request
      if (present(ioff1)) write(*,*) ioff1
      if (present(comm)) write(*,*) comm
    end if
    ierr = 1
    write(*,*) "tempName_vector_irecv is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_vector_irecv

!================================== tempName_matrix_irecv
!
! MPI_irecv function for tempName contiguous vector.
!
!=============================================================================80

  subroutine tempName_matrix_irecv(buf,counts,src,tag,request, &
              ierr,ioff1,ioff2,comm)

#ifndef HAVE_MPI
    use system_extensions, only : se_exit
#endif

    tempType, dimension(:,:), intent(out)   :: buf
    integer,                  intent(in)    :: counts, src, tag
    integer,                  intent(inout) :: request
    integer,                  intent(out)   :: ierr
    integer, optional,        intent(in)    :: ioff1, ioff2
    integer, optional,        intent(in)    :: comm

#ifdef HAVE_MPI
    integer :: fwa1,fwa2,local_comm

    continue

    fwa1 = 1
    if (present(ioff1)) fwa1 = ioff1
    fwa2 = 1
    if (present(ioff2)) fwa2 = ioff2

    local_comm = lmpi_comm
    if ( present(comm) ) local_comm = comm

!   write(*,*)' tempName_maxtrix_irecv; src,tag,fwa1,fwa2 = ',src,tag,fwa1,fwa2

    call mpi_irecv(buf(fwa1,fwa2),counts,tempMPIType,src, &
          tag,local_comm,request,ierr)

    irecv_error: if( ierr /= mpi_success )then
      print*, "mpi_irecv in tempSub failed..."
    endif irecv_error
#else
! avoid compiler warnings
    if (counts==(1+counts)) then
      buf(1,1) = tempConstant
      write(*,*) src, tag, request
      if (present(ioff1)) write(*,*) ioff1
      if (present(ioff2)) write(*,*) ioff2
      if (present(comm)) write(*,*) comm
    end if
    ierr = 0
    write(*,*) "tempName_matrix_irecv is not implemented outside a MPI context."
    call se_exit(1)
#endif

  end subroutine tempName_matrix_irecv
!tempEndExpand


!=============================== INTEGER_REDUCE_MAXLOC2 ======================80
!
! Generic reduce maxloc function for single precision integer data (private)
!
!=============================================================================80

  subroutine integer_reduce_maxloc2(bufferin, bufferout)!tempProtectPrivate

    integer, dimension(:,:), intent(in)  :: bufferin
    integer, dimension(:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer                                      :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! mpi with one proc
      bufferout = bufferin
      return
    endif

    wrong_size : if(size(bufferin,1) /= 2) then
      print*, "ERROR: lmpi_reduce_maxloc2 data must have 2 rows"
      return
    endif wrong_size

    call mpi_reduce(bufferin, bufferout, size(bufferin,2),                     &
      mpi_2integer, mpi_maxloc, 0, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "lmpi_reduce_maxloc2 of single failed......"
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine integer_reduce_maxloc2


!=============================== SINGLE_REDUCE_MAXLOC2 =======================80
!
! Generic reduce maxloc function for single precision real data (private)
!
!=============================================================================80

  subroutine single_reduce_maxloc2(bufferin, bufferout)!tempProtectPrivate

    real(system_r4), dimension(:,:), intent(in)  :: bufferin
    real(system_r4), dimension(:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer                                      :: ierr
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! mpi with one proc
      bufferout = bufferin
      return
    endif

    wrong_size : if(size(bufferin,1) /= 2) then
      print*, "ERROR: lmpi_reduce_maxloc2 data must have 2 rows"
      return
    endif wrong_size

    call mpi_reduce(bufferin, bufferout, size(bufferin,2),                     &
      mpi_2real, mpi_maxloc, 0, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "lmpi_reduce_maxloc2 of single failed......"
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine single_reduce_maxloc2


!=============================== DOUBLE_REDUCE_MAXLOC2 =======================80
!
! Generic reduce maxloc function for double precision real data (private)
!
! Non maxloc2 version of code is included, just commented out.
! This version has been successfully run against the test suite.
!
!=============================================================================80

  subroutine double_reduce_maxloc2(bufferin, bufferout)!tempProtectPrivate

    real(system_r8), dimension(:,:), intent(in)  :: bufferin
    real(system_r8), dimension(:,:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer                                      :: ierr
!   integer                                      :: temp_n, i
!   real(system_r8), dimension(:), allocatable   :: tempin2
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for sequential_mode and mpi with one proc
      bufferout = bufferin
      return
    endif

    wrong_size : if(size(bufferin,1) /= 2) then
      print*, "ERROR: lmpi_reduce_maxloc2 data must have 2 rows"
      return
    endif wrong_size

    call mpi_reduce(bufferin, bufferout, size(bufferin,2),                     &
      mpi_2double_precision, mpi_maxloc, 0, lmpi_comm, ierr)

!   temp_n = size(bufferin,2)
!
!   call mpi_reduce(bufferin(1,1:temp_n), bufferout(1,1:temp_n), temp_n,       &
!     MPI_DOUBLE_PRECISION, MPI_MAX, 0, lmpi_comm, ierr)
!
!   call mpi_bcast(bufferout(1,1:temp_n), temp_n,                              &
!     MPI_DOUBLE_PRECISION, 0, lmpi_comm, ierr)
!
!   allocate(tempin2(temp_n))
!   tempin2(1:temp_n) = 1.0E30
!
!   do i = 1,temp_n
!      if (bufferout(1,i) == bufferin(1,i)) tempin2(i) = bufferin(2,i)
!   end do
!
!   call mpi_reduce(tempin2, bufferout(2,1:temp_n), temp_n,                    &
!     MPI_DOUBLE_PRECISION, MPI_MIN, 0, lmpi_comm, ierr)
!
!   deallocate(tempin2)

    reduce_error: if( ierr /= mpi_success )then
      print*, "lmpi_reduce_maxloc2 of single failed......"
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
#endif

  end subroutine double_reduce_maxloc2


!=============================== COMPLEX_SINGLE_REDUCE_MAXLOC2 ===============80
!
! Generic reduce maxloc function for single precision complex data (private)
!
!=============================================================================80

  subroutine complex_single_reduce_maxloc2(complex_in, complex_out)!tempProtectPrivate

    complex(system_r4), dimension(:,:), intent(in)  :: complex_in
    complex(system_r4), dimension(:,:), intent(out) :: complex_out

#ifdef HAVE_MPI
    integer                                         :: ierr
    real(system_r4), dimension(:,:), allocatable    :: real_in
    real(system_r4), dimension(:,:), allocatable    :: real_out, i1_out, i2_out
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! mpi with one proc
      complex_out = complex_in
      return
    endif

    wrong_size : if(size(complex_in,1) /= 2) then
      print*, "ERROR: lmpi_reduce_maxloc2 data must have 2 rows"
      return
    endif wrong_size

    allocate(real_in(size(complex_in,1),size(complex_in,2)))
    allocate(real_out(size(complex_out,1),size(complex_out,2)))
    allocate(i1_out(size(complex_out,1),size(complex_out,2)))
    allocate(i2_out(size(complex_out,1),size(complex_out,2)))

    real_in(1,:) = real(complex_in(1,:),system_r4)

    real_in(2,:) = aimag(complex_in(1,:))
    call mpi_reduce(real_in, i1_out, size(real_in,2),                          &
      mpi_2real, mpi_maxloc, 0, lmpi_comm, ierr)

    real_in(2,:) = aimag(complex_in(2,:))
    call mpi_reduce(real_in, i2_out, size(real_in,2),                        &
      mpi_2real, mpi_maxloc, 0, lmpi_comm, ierr)

    real_in = real(complex_in,system_r4)
    call mpi_reduce(real_in, real_out, size(real_in,2),                        &
      mpi_2real, mpi_maxloc, 0, lmpi_comm, ierr)

    complex_out(1,:) = cmplx(real_out(1,:),i1_out(2,:),system_r4)
    complex_out(2,:) = cmplx(real_out(2,:),i2_out(2,:),system_r4)

    reduce_error: if( ierr /= mpi_success )then
      print*, "lmpi_reduce_maxloc2 of single failed......"
    endif reduce_error
    deallocate(real_in, real_out, i1_out, i2_out)
#else
    complex_out = complex_in ! need to copy the data to the output
#endif

  end subroutine complex_single_reduce_maxloc2


!=============================== COMPLEX_DOUBLE_REDUCE_MAXLOC2 ===============80
!
! Generic reduce maxloc function for double precision complex data (private)
!
!=============================================================================80

  subroutine complex_double_reduce_maxloc2(complex_in, complex_out)!tempProtectPrivate

    complex(system_r8), dimension(:,:), intent(in)  :: complex_in
    complex(system_r8), dimension(:,:), intent(out) :: complex_out

#ifdef HAVE_MPI
    integer                                         :: ierr
    real(system_r8), dimension(:,:), allocatable    :: real_in
    real(system_r8), dimension(:,:), allocatable    :: real_out, i1_out, i2_out
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! mpi with one proc
      complex_out = complex_in
      return
    endif

    wrong_size : if(size(complex_in,1) /= 2) then
      print*, "ERROR: lmpi_reduce_maxloc2 data must have 2 rows"
      return
    endif wrong_size

    allocate(real_in(size(complex_in,1),size(complex_in,2)))
    allocate(real_out(size(complex_out,1),size(complex_out,2)))
    allocate(i1_out(size(complex_out,1),size(complex_out,2)))
    allocate(i2_out(size(complex_out,1),size(complex_out,2)))

    real_in(1,:) = real(complex_in(1,:),system_r8)

    real_in(2,:) = aimag(complex_in(1,:))
    call mpi_reduce(real_in, i1_out, size(real_in,2),                          &
      mpi_2double_precision, mpi_maxloc, 0, lmpi_comm, ierr)

    real_in(2,:) = aimag(complex_in(2,:))
    call mpi_reduce(real_in, i2_out, size(real_in,2),                          &
      mpi_2double_precision, mpi_maxloc, 0, lmpi_comm, ierr)

    real_in = real(complex_in,system_r8)
    call mpi_reduce(real_in, real_out, size(real_in,2),                        &
      mpi_2double_precision, mpi_maxloc, 0, lmpi_comm, ierr)

    complex_out(1,:) = cmplx(real_out(1,:),i1_out(2,:),system_r8)
    complex_out(2,:) = cmplx(real_out(2,:),i2_out(2,:),system_r8)

    reduce_error: if( ierr /= mpi_success )then
      print*, "lmpi_reduce_maxloc2 of double failed......"
    endif reduce_error
    deallocate(real_in, real_out, i1_out, i2_out)
#else
    complex_out = complex_in ! need to copy the data to the output
#endif

  end subroutine complex_double_reduce_maxloc2


!=============================== integr_vector_sendrecv ======================80
!
! blocking (non_index) send/receive pair for rank 1 integr data
! Added as a standalone routine (until additional types are needed).
!
!=============================================================================80

  subroutine integr_vector_sendrecv(senddata, sendsize, sendid,                &
                                    recvdata, recvsize, recvid)

    integer, dimension(:), intent(in)  :: senddata
    integer,               intent(in)  :: sendsize, sendid
    integer, dimension(:), intent(out) :: recvdata
    integer,               intent(in)  :: recvsize, recvid

#ifdef HAVE_MPI
    integer :: mpi_err
    integer, dimension(lmpi_status_size) :: status
#endif

    continue

#ifdef HAVE_MPI
      call mpi_sendrecv(                                                       &
        senddata,sendsize,MPI_INTEGER,sendid,1,                                &
        recvdata,recvsize,MPI_INTEGER,recvid,1,                                &
        lmpi_comm,status,mpi_err)

     if (mpi_err /= MPI_SUCCESS)                                               &
        write(*,*) 'MPI_ERR in integr_vector_sendrecv = ',mpi_err
#else
     write(*,*) 'integr_vector_sendrecv not implemented for non-HAVE_MPI'
     if (lmpi_nproc == 1) recvdata(1) = senddata(1)
     if ((sendsize==recvsize).and.(sendid==recvid)) return
#endif

  end subroutine integr_vector_sendrecv


!================================== lmpi_integer1_vector_allgatherv
!
! allgatherv function for integer1 vector
!
!=============================================================================80

  subroutine lmpi_integer1_vector_allgatherv(bufferin, counts, bufferout)

    integer(system_i1), dimension(:), intent(in)  :: bufferin
    integer,    dimension(lmpi_nproc), intent(in)  :: counts
    integer(system_i1), dimension(:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer                        :: n, ierr
    integer, dimension(lmpi_nproc) :: displacement
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(1:counts(1)) = bufferin(1:counts(1))
      return
    endif

    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + counts(n-1)
    end do

    call mpi_allgatherv(                                                       &
             bufferin,  counts(lmpi_id+1),    MPI_BYTE,                        &
             bufferout, counts, displacement, MPI_BYTE, lmpi_comm, ierr)

    allgatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_allgatherv in lmpi_integer1_vector_allgatherv failed..."
    endif allgatherv_error
#else
    bufferout(1:counts(1)) = bufferin(1:counts(1))
#endif

  end subroutine lmpi_integer1_vector_allgatherv


!================================== lmpi_integer_vector_allgatherv
!
! allgatherv function for integer vector
!
!=============================================================================80

  subroutine lmpi_integer_vector_allgatherv(bufferin, counts, bufferout)

    integer, dimension(:), intent(in)  :: bufferin
    integer, dimension(lmpi_nproc), intent(in)  :: counts
    integer, dimension(:), intent(out) :: bufferout

#ifdef HAVE_MPI
    integer :: n, ierr
    integer, dimension(lmpi_nproc) :: displacement
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout(1:counts(1)) = bufferin(1:counts(1))
      return
    endif

    displacement = 0
    do n=2,lmpi_nproc
      displacement(n) = displacement(n-1) + counts(n-1)
    end do

    call mpi_allgatherv(                                                       &
             bufferin,  counts(lmpi_id+1),         MPI_INTEGER,                &
             bufferout, counts, displacement, MPI_INTEGER,lmpi_comm,ierr)

    allgatherv_error: if( ierr /= mpi_success )then
      print*, "mpi_allgatherv in lmpi_integer_vector_allgatherv failed..."
    endif allgatherv_error
#else
    bufferout(1:counts(1)) = bufferin(1:counts(1))
#endif

  end subroutine lmpi_integer_vector_allgatherv


!================================== lmpi_waitall
!
! Wrapper for mpi_waitall
!
!=============================================================================80

  subroutine lmpi_waitall(counts, array_of_requests, ierror)

    integer,                 intent(in)    :: counts
    integer, dimension(:),   intent(inout) :: array_of_requests
    integer,                 intent(out)   :: ierror

#ifdef HAVE_MPI
    integer, dimension(lmpi_status_size, counts) :: array_of_statuses
#endif

    continue

#ifdef HAVE_MPI
    array_of_statuses = 0
    call mpi_waitall(counts,array_of_requests,array_of_statuses,ierror)

    waitall_error: if( ierror /= MPI_SUCCESS )then
      print*, "MPI_WAITALL failed in lmpi_waitall ..."
    endif waitall_error
#else
! avoid compiler warnings
    ierror = 0
    if (.false.) write(*,*) counts, array_of_requests
#endif

  end subroutine lmpi_waitall


!================================== lmpi_waitall_w_status
!
! Wrapper for mpi_waitall_w_status
!
!=============================================================================80

  subroutine lmpi_waitall_w_status(counts, array_of_requests, &
                                   array_of_statuses, ierror)

    integer,                 intent(in)    :: counts
    integer, dimension(:),   intent(inout) :: array_of_requests
    integer, dimension(:,:), intent(inout) :: array_of_statuses
    integer,                 intent(out)   :: ierror

    continue

#ifdef HAVE_MPI
    array_of_statuses = 0
    call mpi_waitall(counts,array_of_requests,array_of_statuses,ierror)

    waitall_error: if( ierror /= MPI_SUCCESS )then
      print*, "MPI_WAITALL failed in lmpi_waitall_w_status ..."
    endif waitall_error
#else
! avoid compiler warnings
    ierror = 0
    if (.false.) write(*,*) counts, array_of_requests, array_of_statuses
#endif

  end subroutine lmpi_waitall_w_status

!================================== lmpi_integer1_vector_bcast
!
! broadcast function for MPI_INTEGER1 vector data
!
!=============================================================================80

  subroutine lmpi_integer1_vector_bcast(buffer,transmitter)

    integer(system_i1), dimension(:), intent(inout) :: buffer
    integer,    optional,     intent(in)    :: transmitter

#ifdef HAVE_MPI
    integer :: ierr, root
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return !  mpi with one proc

    root = 0
    if (present(transmitter) ) root = transmitter
    call mpi_bcast(buffer, size(buffer), MPI_BYTE,     root, lmpi_comm, ierr)

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in lmpi_integer1_vector_bcast failed..."
    endif bcast_error
#else
! avoid compiler warnings
    if (.false.) write(*,*) transmitter, buffer
#endif

  end subroutine lmpi_integer1_vector_bcast


!================================== lmpi_integer1_vector_reduce
!
! broadcast function for MPI_INTEGER1 vector reduce
!
!=============================================================================80

  subroutine lmpi_integer1_vector_max(bufferin, bufferout, receiver)

    integer(system_i1), dimension(:), intent(in)  :: bufferin
    integer(system_i1), dimension(:), intent(out) :: bufferout
    integer,    optional,     intent(in)  :: receiver

#ifdef HAVE_MPI
    integer                              :: ierr, root, i, j, ipe
    integer, dimension(:), allocatable   :: t0, t1, t2
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    root = 0
    if (present(receiver)) root = receiver

    allocate(t0(lmpi_nproc)) ! sizes
    j = 0
    do i = 1,size(bufferin)
       if (bufferin(i) >= 0) j = j + 1
    end do
    call lmpi_gather(j,t0)

    if (lmpi_id == root) then

       bufferout = bufferin

       j = maxval(t0)
       allocate(t1(j)) ! l2g
       allocate(t2(j)) ! color
       do ipe = 2,lmpi_nproc
          t1 = 0; t2 = 0
          call lmpi_recv(t1,t0(ipe),ipe-1,100,ierr)
          call lmpi_recv(t2,t0(ipe),ipe-1,200,ierr)
          do i = 1,t0(ipe)
             j = bufferout(t1(i))
             if (t2(i) > j) bufferout(t1(i)) = t2(i)
          end do
       end do
       deallocate(t1,t2)
    else
       allocate(t1(j))
       allocate(t2(j))
       j = 0
       do i = 1,size(bufferin)
          if (bufferin(i) > 0) then
             j = j + 1
             t1(j) = i
             t2(j) = bufferin(i)
          end if
       end do
       call lmpi_send(t1, j, 0, 100, ierr)
       call lmpi_send(t2, j, 0, 200, ierr)
       deallocate(t1,t2)
    end if

    deallocate(t0)

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in lmpi_integer1_vector_max failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine lmpi_integer1_vector_max


!================================== lmpi_byte_vector_bor
!
! broadcast function for MPI_BYTE vector bor (bit-wide or)
!
!=============================================================================80

  subroutine lmpi_byte_vector_bor(bufferin, bufferout, receiver)

    logical(1), dimension(:), intent(in)  :: bufferin
    logical(1), dimension(:), intent(out) :: bufferout
    integer,    optional,     intent(in)  :: receiver

#ifdef HAVE_MPI
    integer                               :: ierr, root
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) then ! for mpi with one proc
      bufferout = bufferin
      return
    endif

    root = 0
    if (present(receiver)) root = receiver

      call mpi_reduce(bufferin, bufferout, size(bufferin,1),                   &
        MPI_BYTE, mpi_bor, root, lmpi_comm, ierr)

    reduce_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in lmpi_byte_vector_bor failed..."
    endif reduce_error
#else
    bufferout = bufferin ! need to copy the data to the output
    if (.false.) write(*,*) receiver ! avoid compiler warnings
#endif

  end subroutine lmpi_byte_vector_bor

!================================== lmpi_byte_vector_bcast
!
! broadcast function for MPI_BYTE vector data
!
!=============================================================================80

  subroutine lmpi_byte_vector_bcast(buffer,transmitter)

    logical(1), dimension(:),   intent(inout) :: buffer
    integer,    optional,       intent(in)    :: transmitter

#ifdef HAVE_MPI
    integer                              :: ierr, root
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return !  mpi with one proc

    root = 0
    if (present(transmitter) ) root = transmitter
    call mpi_bcast(buffer, size(buffer), MPI_BYTE, root, lmpi_comm, ierr)

    bcast_error: if( ierr /= mpi_success )then
      print*, "mpi_bcast in lmpi_byte_vector_bcast failed..."
    endif bcast_error
#else
    if (.false.) write(*,*) transmitter, buffer ! avoid compiler warnings
#endif

  end subroutine lmpi_byte_vector_bcast

end module lmpi

module overset_defs

  implicit none

  private

  public  :: formatted_dci_file
  public  :: output_orphans
  public  :: dci, dci_period_old
  public  :: interpolate_fringes
  public  :: debug_overset
  public  :: imesh_order

  logical            :: formatted_dci_file = .true.
  logical, parameter :: debug_overset = .false.

  integer :: dci                 =  0       ! Current index of a dci file sequence
  integer :: dci_period_old      =  0       ! previous value (from restart)
  logical :: output_orphans      = .false.  ! Output orphans for plotting
  logical :: interpolate_fringes = .false. ! Perform fringe interps on our side

  integer, dimension(:), allocatable :: imesh_order ! imesh order (for adapt)

end module overset_defs

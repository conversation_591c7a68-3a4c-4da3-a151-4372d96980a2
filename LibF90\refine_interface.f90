
module refine_interface

  implicit none

  private

  public :: refine_fortran_init
  interface refine_fortran_init
    module procedure refine_fortran_init_r
    module procedure refine_fortran_init_c
  end interface

  public :: refine_fortran_node
  interface refine_fortran_node
    module procedure refine_fortran_node_r
    module procedure refine_fortran_node_c
  end interface

  public :: refine_fortran_import_metric
  interface refine_fortran_import_metric
    module procedure refine_fortran_import_metric_r
    module procedure refine_fortran_import_metric_c
  end interface

  public :: refine_fortran_import_ratio
  interface refine_fortran_import_ratio
    module procedure refine_fortran_import_ratio_r
    module procedure refine_fortran_import_ratio_c
  end interface

  public :: refine_fortran_import_aux
  interface refine_fortran_import_aux
    module procedure refine_fortran_import_aux_r
    module procedure refine_fortran_import_aux_c
  end interface

  public :: refine_fortran_aux
  interface refine_fortran_aux
    module procedure refine_fortran_aux_r
    module procedure refine_fortran_aux_c
  end interface

  public :: refine_gridcreate
  interface refine_gridcreate
    module procedure gridcreate_r
    module procedure gridcreate_c
  end interface

  public :: refine_gridsetmap
  interface refine_gridsetmap
    module procedure gridsetmap_r
    module procedure gridsetmap_c
  end interface

  public :: refine_gridgetmap
  interface refine_gridgetmap
    module procedure gridgetmap_r
    module procedure gridgetmap_c
  end interface

  public :: refine_gridsetauxvector
  interface refine_gridsetauxvector
    module procedure gridsetauxvector_r
    module procedure gridsetauxvector_c
  end interface

  public :: refine_gridsetauxmatrix
  interface refine_gridsetauxmatrix
    module procedure gridsetauxmatrix_r
    module procedure gridsetauxmatrix_c
  end interface

  public :: refine_gridsetauxmatrix3
  interface refine_gridsetauxmatrix3
    module procedure gridsetauxmatrix3_r
    module procedure gridsetauxmatrix3_c
  end interface

  public :: refine_gridGetNodes
  interface refine_gridGetNodes
    module procedure gridGetNodes_r
    module procedure gridGetNodes_c
  end interface

  public :: refine_gridgetauxvector
  interface refine_gridgetauxvector
    module procedure gridgetauxvector_r
    module procedure gridgetauxvector_c
  end interface

  public :: refine_gridgetauxmatrix
  interface refine_gridgetauxmatrix
    module procedure gridgetauxmatrix_r
    module procedure gridgetauxmatrix_c
  end interface

  public :: refine_gridgetauxmatrix3
  interface refine_gridgetauxmatrix3
    module procedure gridgetauxmatrix3_r
    module procedure gridgetauxmatrix3_c
  end interface

contains

  function refine_fortran_init_r( nnodes01, nnodesg, l2g, part, partition, &
    x, y, z)
    use kinddefs,        only : dp
    integer :: refine_fortran_init_r
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nnodesg
    integer, dimension(nnodes01), intent(in) :: l2g, part
    integer, intent(in) :: partition
    real(dp),dimension(nnodes01),intent(in) :: x,y,z
    interface
      function ref_fortran_init( nnodes01, nnodesg, l2g, part, partition, x,y,z)
        integer :: ref_fortran_init
        integer, intent(in) :: nnodes01
        integer, intent(in) :: nnodesg
        integer, dimension(nnodes01), intent(in) :: l2g, part
        integer, intent(in) :: partition
        real(selected_real_kind(15,307)),dimension(nnodes01),intent(in) :: x,y,z
      end function ref_fortran_init
    end interface
    continue
    refine_fortran_init_r = &
      ref_fortran_init( nnodes01, nnodesg, l2g, part, partition, x,y,z)
  end function refine_fortran_init_r

  function refine_fortran_init_c( nnodes01, nnodesg, l2g, part, partition, &
    cx,cy,cz)
    use kinddefs,        only : dp
    integer :: refine_fortran_init_c
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nnodesg
    integer, dimension(nnodes01), intent(in) :: l2g, part
    integer, intent(in) :: partition
    complex(dp),dimension(nnodes01),intent(in) :: cx,cy,cz
    real(dp), dimension(nnodes01) :: x,y,z
    continue
    x = real(cx, dp)
    y = real(cy, dp)
    z = real(cz, dp)
    refine_fortran_init_c = &
      refine_fortran_init( nnodes01, nnodesg, l2g, part, partition, x,y,z )
  end function refine_fortran_init_c

  function refine_fortran_node_r( nnodes01, l2g, x, y, z)
    use kinddefs,        only : dp
    integer :: refine_fortran_node_r
    integer, intent(in) :: nnodes01
    integer, dimension(nnodes01), intent(out) :: l2g
    real(dp),dimension(nnodes01),intent(out) :: x,y,z
    interface
      function ref_fortran_node( nnodes01, l2g, x,y,z)
        integer :: ref_fortran_node
        integer, intent(in) :: nnodes01
        integer, dimension(nnodes01), intent(out) :: l2g
        real(selected_real_kind(15,307)),dimension(nnodes01),intent(out):: x,y,z
      end function ref_fortran_node
    end interface
    continue
    refine_fortran_node_r = &
      ref_fortran_node( nnodes01, l2g, x,y,z)
  end function refine_fortran_node_r

  function refine_fortran_node_c( nnodes01,l2g, cx,cy,cz)
    use kinddefs,        only : dp
    integer :: refine_fortran_node_c
    integer, intent(in) :: nnodes01
    integer, dimension(nnodes01), intent(out) :: l2g
    complex(dp),dimension(nnodes01),intent(out) :: cx,cy,cz
    real(dp), dimension(nnodes01) :: x,y,z
    continue
    refine_fortran_node_c = &
      refine_fortran_node( nnodes01, l2g, x,y,z )
    cx = cmplx( x, 0.0_dp, dp )
    cy = cmplx( y, 0.0_dp, dp )
    cz = cmplx( z, 0.0_dp, dp )
  end function refine_fortran_node_c

  function refine_fortran_import_metric_r( nnode, map )
    use kinddefs,        only : dp
    integer :: refine_fortran_import_metric_r
    integer, intent(in) :: nnode
    real(dp), dimension(6,nnode), intent(in) :: map
    interface
      function ref_fortran_import_metric( nnode, map )
        integer :: ref_fortran_import_metric
        integer, intent(in) :: nnode
        real(selected_real_kind(15,307)), dimension(6,nnode), intent(in) :: map
      end function ref_fortran_import_metric
    end interface
    continue
    refine_fortran_import_metric_r = ref_fortran_import_metric( nnode, map )
  end function refine_fortran_import_metric_r

  function refine_fortran_import_metric_c( nnode, cmap )
    use kinddefs,        only : dp
    integer :: refine_fortran_import_metric_c
    integer, intent(in) :: nnode
    complex(dp), dimension(6,nnode), intent(in) :: cmap
    real(dp), dimension(6,nnode) :: map
    continue
    map = real(cmap,dp)
    refine_fortran_import_metric_c = refine_fortran_import_metric( nnode, map )
  end function refine_fortran_import_metric_c

  function refine_fortran_import_ratio_r( nnode, ratio )
    use kinddefs,        only : dp
    integer :: refine_fortran_import_ratio_r
    integer, intent(in) :: nnode
    real(dp), dimension(nnode), intent(in) :: ratio
    interface
      function ref_fortran_import_ratio( nnode, ratio )
        integer :: ref_fortran_import_ratio
        integer, intent(in) :: nnode
        real(selected_real_kind(15,307)), dimension(nnode), intent(in) :: &
          ratio
      end function ref_fortran_import_ratio
    end interface
    continue
    refine_fortran_import_ratio_r = ref_fortran_import_ratio( nnode, ratio )
  end function refine_fortran_import_ratio_r

  function refine_fortran_import_ratio_c( nnode, cratio )
    use kinddefs,        only : dp
    integer :: refine_fortran_import_ratio_c
    integer, intent(in) :: nnode
    complex(dp), dimension(nnode), intent(in) :: cratio
    real(dp), dimension(nnode) :: ratio
    continue
    ratio = real(cratio,dp)
    refine_fortran_import_ratio_c = refine_fortran_import_ratio( nnode, ratio )
  end function refine_fortran_import_ratio_c

  function refine_fortran_import_aux_r( ldim, nnode, offset, aux )
    use kinddefs,        only : dp
    integer :: refine_fortran_import_aux_r
    integer, intent(in) :: ldim, nnode, offset
    real(dp), dimension(ldim,nnode), intent(in) :: aux
    interface
      function ref_fortran_import_aux( ldim, nnode, offset, aux )
        integer :: ref_fortran_import_aux
        integer, intent(in) :: ldim, nnode, offset
        real(selected_real_kind(15,307)), &
          dimension(ldim,nnode), intent(in) :: aux
      end function ref_fortran_import_aux
    end interface
    continue
    refine_fortran_import_aux_r = &
      ref_fortran_import_aux( ldim, nnode, offset, aux )
  end function refine_fortran_import_aux_r

  function refine_fortran_import_aux_c( ldim, nnode, offset, caux )
    use kinddefs,        only : dp
    integer :: refine_fortran_import_aux_c
    integer, intent(in) :: ldim, nnode, offset
    complex(dp), dimension(ldim,nnode), intent(in) :: caux
    real(dp), dimension(ldim,nnode) :: aux
    continue
    aux = real(caux,dp)
    refine_fortran_import_aux_c = &
      refine_fortran_import_aux( ldim, nnode, offset, aux )
  end function refine_fortran_import_aux_c

  function refine_fortran_aux_r( ldim, nnode, offset, aux )
    use kinddefs,        only : dp
    integer :: refine_fortran_aux_r
    integer, intent(in) :: ldim, nnode, offset
    real(dp), dimension(ldim,nnode), intent(out) :: aux
    interface
      function ref_fortran_aux( ldim, nnode, offset, aux )
        integer :: ref_fortran_aux
        integer, intent(in) :: ldim, nnode, offset
        real(selected_real_kind(15,307)), &
          dimension(ldim,nnode), intent(out) :: aux
      end function ref_fortran_aux
    end interface
    continue
    refine_fortran_aux_r = &
      ref_fortran_aux( ldim, nnode, offset, aux )
  end function refine_fortran_aux_r

  function refine_fortran_aux_c( ldim, nnode, offset, caux )
    use kinddefs,        only : dp
    integer :: refine_fortran_aux_c
    integer, intent(in) :: ldim, nnode, offset
    complex(dp), dimension(ldim,nnode), intent(out) :: caux
    real(dp), dimension(ldim,nnode) :: aux
    continue
    refine_fortran_aux_c = &
      refine_fortran_aux( ldim, nnode, offset, aux )
    caux = cmplx( aux, 0.0_dp, dp )
  end function refine_fortran_aux_c

  subroutine gridcreate_r( partid, nnode, x, y, z )
    use kinddefs,        only : dp
    integer, intent(in) :: partid, nnode
    real(dp), dimension(nnode), intent(in) :: x,y,z
    interface
      subroutine gridcreate( partid, nnode, x, y, z )
        integer, intent(in) :: partid, nnode
        real(selected_real_kind(15,307)), dimension(nnode), intent(in) :: x,y,z
      end subroutine gridcreate
    end interface
    continue
    call gridcreate( partid, nnode, x, y, z )
  end subroutine gridcreate_r

  subroutine gridcreate_c( partid, nnode, cx, cy, cz )
    use kinddefs,        only : dp
    integer, intent(in) :: partid, nnode
    complex(dp), dimension(nnode), intent(in) :: cx, cy, cz
    real(dp), dimension(nnode) :: x,y,z
    continue
    x = real(cx, dp)
    y = real(cy, dp)
    z = real(cz, dp)
    call refine_gridcreate( partid, nnode, x, y, z )
  end subroutine gridcreate_c


  subroutine gridsetmap_r( nnode, map )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode
    real(dp), dimension(6,nnode), intent(in) :: map
    interface
      subroutine gridsetmap( nnode, map )
        integer, intent(in) :: nnode
        real(selected_real_kind(15,307)), dimension(6,nnode), intent(in) :: map
      end subroutine gridsetmap
    end interface
    continue
    call gridsetmap( nnode, map )
  end subroutine gridsetmap_r

  subroutine gridsetmap_c( nnode, cmap )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode
    complex(dp), dimension(6,nnode), intent(in) :: cmap
    real(dp), dimension(6,nnode) :: map
    continue
    map = real(cmap,dp)
    call refine_gridsetmap( nnode, map )
  end subroutine gridsetmap_c


  subroutine gridgetmap_r( nnode, map )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode
    real(dp), dimension(6,nnode), intent(out) :: map
    interface
      subroutine gridgetmap( nnode, map )
        integer, intent(in) :: nnode
        real(selected_real_kind(15,307)), dimension(6,nnode), intent(out) :: map
      end subroutine gridgetmap
    end interface
    continue
    call gridgetmap( nnode, map )
  end subroutine gridgetmap_r

  subroutine gridgetmap_c( nnode, cmap )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode
    complex(dp), dimension(6,nnode), intent(out) :: cmap
    real(dp), dimension(6,nnode) :: map
    continue
    call refine_gridgetmap( nnode, map )
    cmap = cmplx( map, 0.0_dp, dp )
  end subroutine gridgetmap_c


  subroutine gridsetauxvector_r( nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    real(dp), dimension(nnode), intent(in) :: x
    interface
      subroutine gridsetauxvector( nnode, offset, x )
        integer, intent(in) :: nnode, offset
        real(selected_real_kind(15,307)), dimension(nnode), intent(in) :: x
      end subroutine gridsetauxvector
    end interface
    continue
    call gridsetauxvector( nnode, offset, x )
  end subroutine gridsetauxvector_r

  subroutine gridsetauxvector_c( nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    complex(dp), dimension(nnode), intent(in) :: cx
    real(dp), dimension(nnode) :: x
    continue
    x = real(cx, dp)
    call refine_gridsetauxvector( nnode, offset, x )
  end subroutine gridsetauxvector_c


  subroutine gridsetauxmatrix_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode), intent(in) :: x
    interface
      subroutine gridsetauxmatrix( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode), intent(in) :: x
      end subroutine gridsetauxmatrix
    end interface
    continue
    call gridsetauxmatrix( dmn, nnode, offset, x )
  end subroutine gridsetauxmatrix_r

  subroutine gridsetauxmatrix_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode), intent(in) :: cx
    real(dp), dimension(dmn,nnode) :: x
    continue
    x = real(cx, dp)
    call refine_gridsetauxmatrix( dmn, nnode, offset, x )
  end subroutine gridsetauxmatrix_c


  subroutine gridsetauxmatrix3_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode,1), intent(in) :: x
    interface
      subroutine gridsetauxmatrix3( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode,1), intent(in):: x
      end subroutine gridsetauxmatrix3
    end interface
    continue
    call gridsetauxmatrix3( dmn, nnode, offset, x )
  end subroutine gridsetauxmatrix3_r

  subroutine gridsetauxmatrix3_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode,1), intent(in) :: cx
    real(dp), dimension(dmn,nnode,1) :: x
    continue
    x = real(cx, dp)
    call refine_gridsetauxmatrix3( dmn, nnode, offset, x )
  end subroutine gridsetauxmatrix3_c


  subroutine gridGetNodes_r( nnode, l2g, x, y, z )
    use kinddefs,        only : dp
    integer,                                            intent(in)  :: nnode
    integer,                          dimension(nnode), intent(out) :: l2g
    real(dp), dimension(nnode), intent(out) :: x,y,z
    interface
      subroutine gridGetNodes( nnode, l2g, x, y, z )
        integer,                                            intent(in)  :: nnode
        integer,                          dimension(nnode), intent(out) :: l2g
        real(selected_real_kind(15,307)), dimension(nnode), intent(out) :: x,y,z
      end subroutine gridGetNodes
    end interface
    continue
    call gridGetNodes( nnode, l2g, x, y, z )
  end subroutine gridGetNodes_r

  subroutine gridGetNodes_c( nnode, l2g, cx, cy, cz )
    use kinddefs,        only : dp
    integer,                                            intent(in)  :: nnode
    integer,                          dimension(nnode), intent(out) :: l2g
    complex(dp), dimension(nnode), intent(out) :: cx,cy,cz
    real(dp), dimension(nnode) :: x,y,z
    continue
    call refine_gridGetNodes( nnode, l2g, x, y, z )
    cx = cmplx( x, 0.0_dp, dp )
    cy = cmplx( y, 0.0_dp, dp )
    cz = cmplx( z, 0.0_dp, dp )
  end subroutine gridGetNodes_c

  subroutine gridgetauxvector_r( nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    real(dp), dimension(nnode), intent(out) :: x
    interface
      subroutine gridgetauxvector( nnode, offset, x )
        integer, intent(in) :: nnode, offset
        real(selected_real_kind(15,307)), dimension(nnode), intent(out) :: x
      end subroutine gridgetauxvector
    end interface
    continue
    call gridgetauxvector( nnode, offset, x )
  end subroutine gridgetauxvector_r

  subroutine gridgetauxvector_c( nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: nnode, offset
    complex(dp), dimension(nnode), intent(out) :: cx
    real(dp), dimension(nnode) :: x
    continue
    call refine_gridgetauxvector( nnode, offset, x )
    cx = cmplx( x, 0.0_dp, dp )
  end subroutine gridgetauxvector_c


  subroutine gridgetauxmatrix_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode), intent(out) :: x
    interface
      subroutine gridgetauxmatrix( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode), intent(out) :: x
      end subroutine gridgetauxmatrix
    end interface
    continue
    call gridgetauxmatrix( dmn, nnode, offset, x )
  end subroutine gridgetauxmatrix_r

  subroutine gridgetauxmatrix_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode), intent(out) :: cx
    real(dp), dimension(dmn,nnode) :: x
    continue
    call refine_gridgetauxmatrix( dmn, nnode, offset, x )
    cx = cmplx( x, 0.0_dp, dp )
  end subroutine gridgetauxmatrix_c


  subroutine gridgetauxmatrix3_r( dmn, nnode, offset, x )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    real(dp), dimension(dmn,nnode,1), intent(out) :: x
    interface
      subroutine gridgetauxmatrix3( dmn, nnode, offset, x )
        integer, intent(in) :: dmn, nnode, offset
        real(selected_real_kind(15,307)), dimension(dmn,nnode,1), intent(out)::x
      end subroutine gridgetauxmatrix3
    end interface
    continue
    call gridgetauxmatrix3( dmn, nnode, offset, x )
  end subroutine gridgetauxmatrix3_r

  subroutine gridgetauxmatrix3_c( dmn, nnode, offset, cx )
    use kinddefs,        only : dp
    integer, intent(in) :: dmn, nnode, offset
    complex(dp), dimension(dmn,nnode,1), intent(out) :: cx
    real(dp), dimension(dmn,nnode,1) :: x
    continue
    call refine_gridgetauxmatrix3( dmn, nnode, offset, x )
    cx = cmplx( x, 0.0_dp, dp )
  end subroutine gridgetauxmatrix3_c

end module refine_interface

/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/
#include <stdio.h>

#include "ioswap.h"

size_t fread_BigE(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
  size_t i;
  size_t ret;
  char   *tmp=(char *)ptr;

  ret = fread(ptr,size,nmemb,stream);

  for( i=0; i<nmemb; i++ ) {
    IO_ENDIANSWAPBIG(size,tmp);
    tmp += size;
  }

  return( ret );
}

size_t fread_LittleE(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
  size_t i;
  size_t ret;
  char   *tmp=(char *)ptr;

  ret = fread(ptr,size,nmemb,stream);

  for( i=0; i<nmemb; i++ ) {
    IO_ENDIANSWAPLITTLE(size,tmp);
    tmp += size;
  }

  return( ret );
}

size_t fwrite_BigE(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
  size_t i;
  size_t ret;
  char   *tmp;

  for( i=0,tmp=(char *)ptr; i<nmemb; i++ ) {
    IO_ENDIANSWAPBIG(size,tmp);
    tmp += size;
  }

  ret = fwrite(ptr,size,nmemb,stream);

  for( i=0,tmp=(char *)ptr; i<nmemb; i++ ) {
    IO_ENDIANSWAPBIG(size,tmp);
    tmp += size;
  }

  return( ret );
}

size_t fwrite_LittleE(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
  size_t i;
  size_t ret;
  char   *tmp;

  for( i=0,tmp=(char *)ptr; i<nmemb; i++ ) {
    IO_ENDIANSWAPLITTLE(size,tmp);
    tmp += size;
  }

  ret = fwrite(ptr,size,nmemb,stream);

  for( i=0,tmp=(char *)ptr; i<nmemb; i++ ) {
    IO_ENDIANSWAPLITTLE(size,tmp);
    tmp += size;
  }

  return( ret );
}

int IsBigEndian()
{
  short word = 0x4321;

  if((*(char *)&word) != 0x21 )
    return 1; /* true */
  else
    return 0; /* false */
}

size_t fread_Swapped(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
#if ( ( (defined(__BYTE_ORDER) && (__BYTE_ORDER==__LITTLE_ENDIAN)) || \
        (defined(_BYTE_ORDER) && (_BYTE_ORDER==_LITTLE_ENDIAN)) ||    \
        (defined(BYTE_ORDER) && (BYTE_ORDER==LITTLE_ENDIAN)) ||       \
        (defined(WINNT)) ) &&                                         \
      !defined(NO_BYTE_SWAP) )
  return fread_BigE(ptr,size,nmemb,stream);
#else
  return fread_LittleE(ptr,size,nmemb,stream);
#endif
}

size_t fwrite_Swapped(void *ptr, size_t size, size_t nmemb, FILE *stream)
{
#if ( ( (defined(__BYTE_ORDER) && (__BYTE_ORDER==__LITTLE_ENDIAN)) || \
        (defined(_BYTE_ORDER) && (_BYTE_ORDER==_LITTLE_ENDIAN)) ||    \
        (defined(BYTE_ORDER) && (BYTE_ORDER==LITTLE_ENDIAN)) ||       \
        (defined(WINNT)) ) &&                                         \
      !defined(NO_BYTE_SWAP) )
  return fwrite_BigE(ptr,size,nmemb,stream);
#else
  return fwrite_LittleE(ptr,size,nmemb,stream);
#endif
}

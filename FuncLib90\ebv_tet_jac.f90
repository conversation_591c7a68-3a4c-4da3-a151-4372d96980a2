!=============================== EBV_TET_JAC =================================80
!
! Edge-based viscous jacobians for tetrahedra
!
! Note that this function uses conservative variables
!
!=============================================================================80
  pure function ebv_tet_jac(r1,u1,v1,w1,e1,r2,u2,v2,w2,e2,cstar,cgp,cgpt,      &
                            amut1,amut2,w,xmr,r1inv,r2inv)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_1p5
    use fluid,           only : gamma, ggm1, gm1

    real(dp), intent(in) :: r1,u1,v1,w1,e1,r2,u2,v2,w2,e2,cstar,cgp,cgpt
    real(dp), intent(in) :: amut1,amut2,xmr,r1inv,r2inv

    real(dp), dimension(10), intent(in) :: w
    real(dp), dimension(5,5,2)          :: ebv_tet_jac

    real(dp) :: p1,p2,t1,t2,du,dv,dw,mu1,mu2,mucgp,mu,umu,vmu,wmu
    real(dp) :: dur1,dum1,dur2,dum2,dvr1,dvn1,dvr2,dvn2,dwr1,dwl1,dwr2,dwl2
    real(dp) :: dar1,dam1,dan1,dal1,dae1,dar2,dam2,dan2,dal2,dae2
    real(dp) :: umur1,umum1,umur2,umum2,vmur1,vmun1,vmur2,vmun2
    real(dp) :: wmur1,wmul1,wmur2,wmul2

  continue

    p1 = gm1 * (e1-my_half*r1*(u1*u1+v1*v1+w1*w1))
    p2 = gm1 * (e2-my_half*r2*(u2*u2+v2*v2+w2*w2))

    t1 = gamma * p1*r1inv
    t2 = gamma * p2*r2inv

    du = u2-u1
      dur1 =  u1*r1inv
      dum1 = -my_1*r1inv
      dur2 = -u2*r2inv
      dum2 =  my_1*r2inv

    dv = v2-v1
      dvr1 =  v1*r1inv
      dvn1 = -my_1*r1inv
      dvr2 = -v2*r2inv
      dvn2 =  my_1*r2inv

    dw = w2-w1
      dwr1 =  w1*r1inv
      dwl1 = -my_1*r1inv
      dwr2 = -w2*r2inv
      dwl2 =  my_1*r2inv

!   da = t2-t1
      dar1 = -ggm1*(u1*u1 + v1*v1 + w1*w1 - e1*r1inv)*r1inv
      dam1 =  ggm1*u1*r1inv
      dan1 =  ggm1*v1*r1inv
      dal1 =  ggm1*w1*r1inv
      dae1 = -ggm1*r1inv

      dar2 =  ggm1*(u2*u2 + v2*v2 + w2*w2 - e2*r2inv)*r2inv
      dam2 = -ggm1*u2*r2inv
      dan2 = -ggm1*v2*r2inv
      dal2 = -ggm1*w2*r2inv
      dae2 =  ggm1*r2inv

    mu1 = (my_1 + cstar)/(t1 + cstar)*t1**my_1p5
    mu2 = (my_1 + cstar)/(t2 + cstar)*t2**my_1p5

    mucgp = my_half*(cgp*(mu1 + mu2) + cgpt*(amut1 + amut2))

    mu1 = mu1 + amut1
    mu2 = mu2 + amut2

    mu = my_half*(mu1 + mu2)

    umu = my_half*(u2+u1)*mu
      umur1 = -my_half*u1*r1inv*mu
      umum1 =  my_half*r1inv*mu
      umur2 = -my_half*u2*r2inv*mu
      umum2 =  my_half*r2inv*mu

    vmu = my_half*(v2+v1)*mu
      vmur1 = -my_half*v1*r1inv*mu
      vmun1 =  my_half*r1inv*mu
      vmur2 = -my_half*v2*r2inv*mu
      vmun2 =  my_half*r2inv*mu

    wmu = my_half*(w2+w1)*mu
      wmur1 = -my_half*w1*r1inv*mu
      wmul1 =  my_half*r1inv*mu
      wmur2 = -my_half*w2*r2inv*mu
      wmul2 =  my_half*r2inv*mu

! df/dq1

    ebv_tet_jac(1,:,1) = my_0

    ebv_tet_jac(2,1,1) = -mu*xmr*(w(1)*dur1 + w(2)*dvr1 + w(3)*dwr1)
    ebv_tet_jac(2,2,1) = -mu*xmr*(w(1)*dum1                      )
    ebv_tet_jac(2,3,1) = -mu*xmr*(           w(2)*dvn1           )
    ebv_tet_jac(2,4,1) = -mu*xmr*(                      w(3)*dwl1)
    ebv_tet_jac(2,5,1) =  my_0

    ebv_tet_jac(3,1,1) = -mu*xmr*(w(4)*dur1 + w(5)*dvr1 + w(6)*dwr1)
    ebv_tet_jac(3,2,1) = -mu*xmr*(w(4)*dum1                      )
    ebv_tet_jac(3,3,1) = -mu*xmr*(           w(5)*dvn1           )
    ebv_tet_jac(3,4,1) = -mu*xmr*(                      w(6)*dwl1)
    ebv_tet_jac(3,5,1) =  my_0

    ebv_tet_jac(4,1,1) = -mu*xmr*(w(7)*dur1 + w(8)*dvr1 + w(9)*dwr1)
    ebv_tet_jac(4,2,1) = -mu*xmr*(w(7)*dum1                      )
    ebv_tet_jac(4,3,1) = -mu*xmr*(           w(8)*dvn1           )
    ebv_tet_jac(4,4,1) = -mu*xmr*(                      w(9)*dwl1)
    ebv_tet_jac(4,5,1) =  my_0

    ebv_tet_jac(5,1,1) = -(xmr*(du*(umur1*w(1) + vmur1*w(4) + wmur1*w(7))      &
                       + (umu*w(1) + vmu*w(4) + wmu*w(7))*dur1                 &
                    + dv*(umur1*w(2) + vmur1*w(5) + wmur1*w(8))                &
                       + (umu*w(2) + vmu*w(5) + wmu*w(8))*dvr1                 &
                    + dw*(umur1*w(3) + vmur1*w(6) + wmur1*w(9))                &
                       + (umu*w(3) + vmu*w(6) + wmu*w(9))*dwr1                 &
                        + mucgp*dar1*w(10)))
    ebv_tet_jac(5,2,1) = -(xmr*(du*(umum1*w(1)                        )        &
                       + (umu*w(1) + vmu*w(4) + wmu*w(7))*dum1                 &
                    + dv*(umum1*w(2)                        )                  &
                    + dw*(umum1*w(3)                        )                  &
                        + mucgp*dam1*w(10)))
    ebv_tet_jac(5,3,1) = -(xmr*(du*(            vmun1*w(4)            )        &
                    + dv*(            vmun1*w(5)            )                  &
                       + (umu*w(2) + vmu*w(5) + wmu*w(8))*dvn1                 &
                    + dw*(            vmun1*w(6)            )                  &
                        + mucgp*dan1*w(10)))
    ebv_tet_jac(5,4,1) = -(xmr*(du*(                        wmul1*w(7))        &
                    + dv*(                        wmul1*w(8))                  &
                    + dw*(                        wmul1*w(9))                  &
                       + (umu*w(3) + vmu*w(6) + wmu*w(9))*dwl1                 &
                        + mucgp*dal1*w(10)))
    ebv_tet_jac(5,5,1) = -(xmr*(mucgp*dae1*w(10)))

! df/dq2

    ebv_tet_jac(1,:,2) = my_0

    ebv_tet_jac(2,1,2) = -mu*xmr*(w(1)*dur2 + w(2)*dvr2 + w(3)*dwr2)
    ebv_tet_jac(2,2,2) = -mu*xmr*(w(1)*dum2                      )
    ebv_tet_jac(2,3,2) = -mu*xmr*(           w(2)*dvn2           )
    ebv_tet_jac(2,4,2) = -mu*xmr*(                      w(3)*dwl2)
    ebv_tet_jac(2,5,2) =  my_0

    ebv_tet_jac(3,1,2) = -mu*xmr*(w(4)*dur2 + w(5)*dvr2 + w(6)*dwr2)
    ebv_tet_jac(3,2,2) = -mu*xmr*(w(4)*dum2                      )
    ebv_tet_jac(3,3,2) = -mu*xmr*(           w(5)*dvn2           )
    ebv_tet_jac(3,4,2) = -mu*xmr*(                      w(6)*dwl2)
    ebv_tet_jac(3,5,2) =  my_0

    ebv_tet_jac(4,1,2) = -mu*xmr*(w(7)*dur2 + w(8)*dvr2 + w(9)*dwr2)
    ebv_tet_jac(4,2,2) = -mu*xmr*(w(7)*dum2                      )
    ebv_tet_jac(4,3,2) = -mu*xmr*(           w(8)*dvn2           )
    ebv_tet_jac(4,4,2) = -mu*xmr*(                      w(9)*dwl2)
    ebv_tet_jac(4,5,2) =  my_0

    ebv_tet_jac(5,1,2) = -(xmr*(du*(umur2*w(1) + vmur2*w(4) + wmur2*w(7))      &
                       + (umu*w(1) + vmu*w(4) + wmu*w(7))*dur2                 &
                    + dv*(umur2*w(2) + vmur2*w(5) + wmur2*w(8))                &
                       + (umu*w(2) + vmu*w(5) + wmu*w(8))*dvr2                 &
                    + dw*(umur2*w(3) + vmur2*w(6) + wmur2*w(9))                &
                       + (umu*w(3) + vmu*w(6) + wmu*w(9))*dwr2                 &
                        + mucgp*dar2*w(10)))
    ebv_tet_jac(5,2,2) = -(xmr*(du*(umum2*w(1)                        )        &
                       + (umu*w(1) + vmu*w(4) + wmu*w(7))*dum2                 &
                    + dv*(umum2*w(2)                        )                  &
                    + dw*(umum2*w(3)                        )                  &
                        + mucgp*dam2*w(10)))
    ebv_tet_jac(5,3,2) = -(xmr*(du*(            vmun2*w(4)            )        &
                    + dv*(            vmun2*w(5)            )                  &
                       + (umu*w(2) + vmu*w(5) + wmu*w(8))*dvn2                 &
                    + dw*(            vmun2*w(6)            )                  &
                        + mucgp*dan2*w(10)))
    ebv_tet_jac(5,4,2) = -(xmr*(du*(                        wmul2*w(7))        &
                    + dv*(                        wmul2*w(8))                  &
                    + dw*(                        wmul2*w(9))                  &
                       + (umu*w(3) + vmu*w(6) + wmu*w(9))*dwl2                 &
                        + mucgp*dal2*w(10)))
    ebv_tet_jac(5,5,2) = -(xmr*(mucgp*dae2*w(10)))

  end function ebv_tet_jac

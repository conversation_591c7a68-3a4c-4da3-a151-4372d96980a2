module fun3d_constants

  use kinddefs, only : dp

  implicit none

  private

  public :: my_0
  public :: my_1
  public :: my_1p5
  public :: my_2
  public :: my_3
  public :: my_4
  public :: my_5
  public :: my_6
  public :: my_7
  public :: my_8
  public :: my_9
  public :: my_11
  public :: my_14
  public :: my_16
  public :: my_24
  public :: my_100
  public :: my_180
  public :: my_16th
  public :: my_4th
  public :: my_3rd
  public :: my_half
  public :: my_5th
  public :: my_6th
  public :: my_7th
  public :: my_9th

  public :: zero, one, two, half

  public :: pi, conv, rad_from_deg, deg_from_rad
  public :: flim_llim

  real(dp), parameter :: my_0    =  0.0_dp
  real(dp), parameter :: my_1    =  1.0_dp
  real(dp), parameter :: my_1p5  =  1.5_dp
  real(dp), parameter :: my_2    =  2.0_dp
  real(dp), parameter :: my_3    =  3.0_dp
  real(dp), parameter :: my_4    =  4.0_dp
  real(dp), parameter :: my_5    =  5.0_dp
  real(dp), parameter :: my_6    =  6.0_dp
  real(dp), parameter :: my_7    =  7.0_dp
  real(dp), parameter :: my_8    =  8.0_dp
  real(dp), parameter :: my_9    =  9.0_dp
  real(dp), parameter :: my_11   = 11.0_dp
  real(dp), parameter :: my_14   = 14.0_dp
  real(dp), parameter :: my_16   = 16.0_dp
  real(dp), parameter :: my_24   = 24.0_dp
  real(dp), parameter :: my_100  = 100.0_dp
  real(dp), parameter :: my_180  = 180.0_dp
  real(dp), parameter :: my_16th = my_1/my_16
  real(dp), parameter :: my_4th  = 0.25_dp
  real(dp), parameter :: my_3rd  = my_1/my_3
  real(dp), parameter :: my_half = 0.50_dp
  real(dp), parameter :: my_5th  = my_1/my_5
  real(dp), parameter :: my_6th  = my_1/my_6
  real(dp), parameter :: my_7th  = my_1/my_7
  real(dp), parameter :: my_9th  = my_1/my_9

  real(dp), parameter :: pi = 3.14159265358979323846264338327950288419716939_dp
  real(dp), parameter :: conv = 180._dp/pi
  real(dp), parameter :: rad_from_deg = pi/180._dp
  real(dp), parameter :: deg_from_rad = 180._dp/pi

  real(dp) :: flim_llim = 1.0e+04_dp*epsilon(1.0_dp) ! only real part when cmplx

  real(dp), parameter :: zero   = 0.00_dp
  real(dp), parameter :: one    = 1.00_dp
  real(dp), parameter :: two    = 2.00_dp
  real(dp), parameter :: half   = 0.50_dp

end module fun3d_constants

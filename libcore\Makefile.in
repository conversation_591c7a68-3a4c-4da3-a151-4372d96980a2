# Makefile.in generated by automake 1.11.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008, 2009  Free Software Foundation,
# Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
DIST_COMMON = README $(srcdir)/Common.am $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in $(top_srcdir)/make.rules AUTHORS \
	ChangeLog NEWS
@BUILD_COMPLEX_TRUE@am__append_1 = Complex
subdir = libcore
SUBDIRS =
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps =  \
	$(top_srcdir)/aclocal/ax_f90_module_extension.m4 \
	$(top_srcdir)/aclocal/ax_f90_module_flag.m4 \
	$(top_srcdir)/aclocal/capri.m4 $(top_srcdir)/aclocal/cgns.m4 \
	$(top_srcdir)/aclocal/cuda.m4 \
	$(top_srcdir)/aclocal/dynamic_loading.m4 \
	$(top_srcdir)/aclocal/f90_tuner.m4 \
	$(top_srcdir)/aclocal/f90_unix.m4 \
	$(top_srcdir)/aclocal/fccht.m4 \
	$(top_srcdir)/aclocal/fortran_2003_environment.m4 \
	$(top_srcdir)/aclocal/fortran_asynchronous_io.m4 \
	$(top_srcdir)/aclocal/fortran_c_interoperability.m4 \
	$(top_srcdir)/aclocal/fortran_etime.m4 \
	$(top_srcdir)/aclocal/fortran_open_big_endian.m4 \
	$(top_srcdir)/aclocal/fortran_open_stream.m4 \
	$(top_srcdir)/aclocal/fortran_posix_interface.m4 \
	$(top_srcdir)/aclocal/fun3d.m4 $(top_srcdir)/aclocal/gsi.m4 \
	$(top_srcdir)/aclocal/meshsim.m4 $(top_srcdir)/aclocal/mpi.m4 \
	$(top_srcdir)/aclocal/parmetis.m4 \
	$(top_srcdir)/aclocal/resource_limit.m4 \
	$(top_srcdir)/aclocal/zoltan.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__installdirs = "$(DESTDIR)$(libdir)"
LIBRARIES = $(lib_LIBRARIES)
AR = ar
ARFLAGS = cru
libcore_a_AR = $(AR) $(ARFLAGS)
libcore_a_DEPENDENCIES =
am__objects_1 = adjoint_switches.$(OBJEXT) allocate_gen.$(OBJEXT) \
	allocations.$(OBJEXT) av2p0_lapack_util.$(OBJEXT) \
	av3p2_lapack_util.$(OBJEXT) b_discrete_types.$(OBJEXT) \
	bc_names.$(OBJEXT) bc_types.$(OBJEXT) blas.$(OBJEXT) \
	complex_functions.$(OBJEXT) comprow_types.$(OBJEXT) \
	convection_defs.$(OBJEXT) c_utilities.$(OBJEXT) ddt.$(OBJEXT) \
	debug_defs.$(OBJEXT) debug_output.$(OBJEXT) \
	eigen_eispack.$(OBJEXT) element_types.$(OBJEXT) \
	f2kcli.$(OBJEXT) file_utils.$(OBJEXT) fluid.$(OBJEXT) \
	force_types.$(OBJEXT) fun3d_constants.$(OBJEXT) \
	fun3d_maximums.$(OBJEXT) generic_gas_map.$(OBJEXT) \
	info_depr.$(OBJEXT) interp_defs.$(OBJEXT) \
	invert_lapack.$(OBJEXT) ivals.$(OBJEXT) kinddefs.$(OBJEXT) \
	linear_algebra.$(OBJEXT) lmpi.$(OBJEXT) lmpi_app.$(OBJEXT) \
	logger.$(OBJEXT) namelist_util.$(OBJEXT) \
	openacc_vars.$(OBJEXT) physics_types.$(OBJEXT) \
	solution_types.$(OBJEXT) sort.$(OBJEXT) string_utils.$(OBJEXT) \
	system_extensions.$(OBJEXT) utilities.$(OBJEXT) \
	versions.$(OBJEXT)
am__objects_2 =
am__objects_3 = $(am__objects_2)
am__objects_4 = $(am__objects_3)
am_libcore_a_OBJECTS = $(am__objects_1) $(am__objects_4)
libcore_a_OBJECTS = $(am_libcore_a_OBJECTS)
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
PPFCCOMPILE = $(FC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_FCFLAGS) $(FCFLAGS)
FCLD = $(FC)
FCLINK = $(FCLD) $(AM_FCFLAGS) $(FCFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o \
	$@
FCCOMPILE = $(FC) $(AM_FCFLAGS) $(FCFLAGS)
SOURCES = $(libcore_a_SOURCES)
DIST_SOURCES = $(libcore_a_SOURCES)
RECURSIVE_TARGETS = all-recursive check-recursive dvi-recursive \
	html-recursive info-recursive install-data-recursive \
	install-dvi-recursive install-exec-recursive \
	install-html-recursive install-info-recursive \
	install-pdf-recursive install-ps-recursive install-recursive \
	installcheck-recursive installdirs-recursive pdf-recursive \
	ps-recursive uninstall-recursive
RECURSIVE_CLEAN_TARGETS = mostlyclean-recursive clean-recursive	\
  distclean-recursive maintainer-clean-recursive
AM_RECURSIVE_TARGETS = $(RECURSIVE_TARGETS:-recursive=) \
	$(RECURSIVE_CLEAN_TARGETS:-recursive=) tags TAGS ctags CTAGS \
	distdir
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
am__relativize = \
  dir0=`pwd`; \
  sed_first='s,^\([^/]*\)/.*$$,\1,'; \
  sed_rest='s,^[^/]*/*,,'; \
  sed_last='s,^.*/\([^/]*\)$$,\1,'; \
  sed_butlast='s,/*[^/]*$$,,'; \
  while test -n "$$dir1"; do \
    first=`echo "$$dir1" | sed -e "$$sed_first"`; \
    if test "$$first" != "."; then \
      if test "$$first" = ".."; then \
        dir2=`echo "$$dir0" | sed -e "$$sed_last"`/"$$dir2"; \
        dir0=`echo "$$dir0" | sed -e "$$sed_butlast"`; \
      else \
        first2=`echo "$$dir2" | sed -e "$$sed_first"`; \
        if test "$$first2" = "$$first"; then \
          dir2=`echo "$$dir2" | sed -e "$$sed_rest"`; \
        else \
          dir2="../$$dir2"; \
        fi; \
        dir0="$$dir0"/"$$first"; \
      fi; \
    fi; \
    dir1=`echo "$$dir1" | sed -e "$$sed_rest"`; \
  done; \
  reldir="$$dir2"
ACLOCAL = @ACLOCAL@
ACLOCAL_AMFLAGS = @ACLOCAL_AMFLAGS@
AMTAR = @AMTAR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BIBTEX = @BIBTEX@
CAPRIheader = @CAPRIheader@
CAPRIlibrary = @CAPRIlibrary@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CGNSinclude = @CGNSinclude@
CGNSlibrary = @CGNSlibrary@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CUDACC = @CUDACC@
CUDAFLAGS = @CUDAFLAGS@
CUDA_LIB_PATH = @CUDA_LIB_PATH@
CXX = @CXX@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F90_EXT_LIB = @F90_EXT_LIB@
FC = @FC@
FCFLAGS = @FCFLAGS@
FCLIBS = @FCLIBS@
FC_MODEXT = @FC_MODEXT@
FC_MODINC = @FC_MODINC@
GREP = @GREP@
HAVE_F2PY = @HAVE_F2PY@
HAVE_RUBY = @HAVE_RUBY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
KNIFE_SUBDIR = @KNIFE_SUBDIR@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
MOD_DEP_COMPILER = @MOD_DEP_COMPILER@
MPIF90 = @MPIF90@
MPIINC = @MPIINC@
MPIRUN = @MPIRUN@
MPI_EXT = @MPI_EXT@
MPI_Prefix = @MPI_Prefix@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PDFLATEX = @PDFLATEX@
PERL5 = @PERL5@
PHYSICS_TYPE = @PHYSICS_TYPE@
PYTHON = @PYTHON@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SUBDIR = @PYTHON_SUBDIR@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
REFINE_SUBDIR = @REFINE_SUBDIR@
SBOOMlibrary = @SBOOMlibrary@
SDKheader = @SDKheader@
SDKlibrary = @SDKlibrary@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SIXDOFLIBS = @SIXDOFLIBS@
STRIP = @STRIP@
TECIOLIBS = @TECIOLIBS@
VERSION = @VERSION@
VisItinclude = @VisItinclude@
VisItlibrary = @VisItlibrary@
XMKMF = @XMKMF@
Xheader = @Xheader@
Xlibrary = @Xlibrary@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_FC = @ac_ct_FC@
ac_empty = @ac_empty@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
dirtlibrary = @dirtlibrary@
docdir = @docdir@
dotlibrary = @dotlibrary@
dvidir = @dvidir@
dymorelibrary = @dymorelibrary@
exec_prefix = @exec_prefix@
fcompiler = @fcompiler@
have_bibtex = @have_bibtex@
have_latex = @have_latex@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
irslibrary = @irslibrary@
knife_deps = @knife_deps@
knife_ldadd = @knife_ldadd@
ksoptlibrary = @ksoptlibrary@
libcore_path = @libcore_path@
libdir = @libdir@
libexecdir = @libexecdir@
libturb_path = @libturb_path@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
meshsim_ldadd = @meshsim_ldadd@
mkdir_p = @mkdir_p@
mpi_ldadd = @mpi_ldadd@
npsollibrary = @npsollibrary@
oldincludedir = @oldincludedir@
parmetis_include = @parmetis_include@
parmetis_ldadd = @parmetis_ldadd@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
portlibrary = @portlibrary@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
punditinclude = @punditinclude@
punditlibrary = @punditlibrary@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
refine_deps = @refine_deps@
refine_ldadd = @refine_ldadd@
sbindir = @sbindir@
sdxlibrary = @sdxlibrary@
sfelibrary = @sfelibrary@
sharedstatedir = @sharedstatedir@
snoptlibrary = @snoptlibrary@
sparskitlibrary = @sparskitlibrary@
srcdir = @srcdir@
ssdclibrary = @ssdclibrary@
subdirs = @subdirs@
suggarlibrary = @suggarlibrary@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
zoltan_include = @zoltan_include@
zoltan_ldadd = @zoltan_ldadd@
DIST_SUBDIRS = $(am__append_1)
libcore_SRCS = \
	adjoint_switches.f90 \
	allocate_gen.f90 \
	allocations.f90 \
	av2p0_lapack_util.f90 \
	av3p2_lapack_util.f90 \
	b_discrete_types.f90 \
	bc_names.f90 \
	bc_types.f90 \
	blas.f90 \
	complex_functions.f90 \
	comprow_types.f90 \
	convection_defs.f90 \
	c_utilities.F90 \
	ddt.f90 \
	debug_defs.f90 \
	debug_output.f90 \
	eigen_eispack.f90 \
	element_types.F90 \
	f2kcli.F90 \
	file_utils.f90 \
	fluid.f90 \
	force_types.f90 \
	fun3d_constants.f90 \
	fun3d_maximums.f90 \
	generic_gas_map.f90 \
	info_depr.f90 \
	interp_defs.f90 \
	invert_lapack.f90 \
	ivals.f90 \
	kinddefs.F90 \
	linear_algebra.f90 \
	lmpi.F90 \
	lmpi_app.F90 \
	logger.f90 \
	namelist_util.f90 \
	openacc_vars.f90 \
	physics_types.f90 \
	solution_types.f90 \
	sort.f90 \
	string_utils.f90 \
	system_extensions.F90 \
	utilities.f90 \
	versions.F90

@BUILD_MPI_FALSE@AM_FCFLAGS = \
@BUILD_MPI_FALSE@	$(FC_MODINC)@top_builddir@

@BUILD_MPI_TRUE@AM_FCFLAGS = \
@BUILD_MPI_TRUE@	$(FC_MODINC)@MPIINC@ \
@BUILD_MPI_TRUE@	$(FC_MODINC)@top_builddir@


# remove *.mod when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT)  mpif.h *.time *.mod *.fh *.d
libcore_f90s = $(libcore_SRCS:.F90=.f90)
libcore_deps = $(libcore_f90s:.f90=.d)
SUFFIXES = .d
BUILT_SOURCES = $(libcore_deps)

# Install Fortran module files alongside the library
lib_MODULES = $(libcore_f90s:.f90=.$(FC_MODEXT))
lib_LIBRARIES = libcore.a
libcore_a_SOURCES = $(libcore_SRCS) $(libcore_deps)
libcore_a_LIBADD = 
libcore_a_LINK = $(F90LINK)
UNIT_TESTS = \
	allocations.fun \
	ddt.fun \
	file_utils.fun \
	linear_algebra.fun \
	string_utils.fun \
	sort.fun \
	system_extensions.fun \
	utilities.fun

EXTRA_DIST = \
	$(UNIT_TESTS) \
	fortran_template.rb \
	lmpi.template \
	lmpi_app.template \
	remove_stalemods.sh

libMODULES_INSTALL = $(INSTALL_DATA)
all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-recursive

.SUFFIXES:
.SUFFIXES: .d .F90 .f90 .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am $(srcdir)/Common.am $(top_srcdir)/make.rules $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu libcore/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu libcore/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-libLIBRARIES: $(lib_LIBRARIES)
	@$(NORMAL_INSTALL)
	test -z "$(libdir)" || $(MKDIR_P) "$(DESTDIR)$(libdir)"
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(INSTALL_DATA) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(INSTALL_DATA) $$list2 "$(DESTDIR)$(libdir)" || exit $$?; }
	@$(POST_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  if test -f $$p; then \
	    $(am__strip_dir) \
	    echo " ( cd '$(DESTDIR)$(libdir)' && $(RANLIB) $$f )"; \
	    ( cd "$(DESTDIR)$(libdir)" && $(RANLIB) $$f ) || exit $$?; \
	  else :; fi; \
	done

uninstall-libLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	test -n "$$files" || exit 0; \
	echo " ( cd '$(DESTDIR)$(libdir)' && rm -f "$$files" )"; \
	cd "$(DESTDIR)$(libdir)" && rm -f $$files

clean-libLIBRARIES:
	-test -z "$(lib_LIBRARIES)" || rm -f $(lib_LIBRARIES)
libcore.a: $(libcore_a_OBJECTS) $(libcore_a_DEPENDENCIES) 
	-rm -f libcore.a
	$(libcore_a_AR) libcore.a $(libcore_a_OBJECTS) $(libcore_a_LIBADD)
	$(RANLIB) libcore.a

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

.F90.o:
	$(PPFCCOMPILE) -c -o $@ $<

.F90.obj:
	$(PPFCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.f90.o:
	$(FCCOMPILE) -c -o $@ $<

.f90.obj:
	$(FCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

# This directory's subdirectories are mostly independent; you can cd
# into them and run `make' without going through this Makefile.
# To change the values of `make' variables: instead of editing Makefiles,
# (1) if the variable is set in `config.status', edit `config.status'
#     (which will cause the Makefiles to be regenerated when you run `make');
# (2) otherwise, pass the desired values on the `make' command line.
$(RECURSIVE_TARGETS):
	@fail= failcom='exit 1'; \
	for f in x $$MAKEFLAGS; do \
	  case $$f in \
	    *=* | --[!k]*);; \
	    *k*) failcom='fail=yes';; \
	  esac; \
	done; \
	dot_seen=no; \
	target=`echo $@ | sed s/-recursive//`; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    dot_seen=yes; \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done; \
	if test "$$dot_seen" = "no"; then \
	  $(MAKE) $(AM_MAKEFLAGS) "$$target-am" || exit 1; \
	fi; test -z "$$fail"

$(RECURSIVE_CLEAN_TARGETS):
	@fail= failcom='exit 1'; \
	for f in x $$MAKEFLAGS; do \
	  case $$f in \
	    *=* | --[!k]*);; \
	    *k*) failcom='fail=yes';; \
	  esac; \
	done; \
	dot_seen=no; \
	case "$@" in \
	  distclean-* | maintainer-clean-*) list='$(DIST_SUBDIRS)' ;; \
	  *) list='$(SUBDIRS)' ;; \
	esac; \
	rev=''; for subdir in $$list; do \
	  if test "$$subdir" = "."; then :; else \
	    rev="$$subdir $$rev"; \
	  fi; \
	done; \
	rev="$$rev ."; \
	target=`echo $@ | sed s/-recursive//`; \
	for subdir in $$rev; do \
	  echo "Making $$target in $$subdir"; \
	  if test "$$subdir" = "."; then \
	    local_target="$$target-am"; \
	  else \
	    local_target="$$target"; \
	  fi; \
	  ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) $$local_target) \
	  || eval $$failcom; \
	done && test -z "$$fail"
tags-recursive:
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  test "$$subdir" = . || ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) tags); \
	done
ctags-recursive:
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  test "$$subdir" = . || ($(am__cd) $$subdir && $(MAKE) $(AM_MAKEFLAGS) ctags); \
	done

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS: tags-recursive $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	set x; \
	here=`pwd`; \
	if ($(ETAGS) --etags-include --version) >/dev/null 2>&1; then \
	  include_option=--etags-include; \
	  empty_fix=.; \
	else \
	  include_option=--include; \
	  empty_fix=; \
	fi; \
	list='$(SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test ! -f $$subdir/TAGS || \
	      set "$$@" "$$include_option=$$here/$$subdir/TAGS"; \
	  fi; \
	done; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: CTAGS
CTAGS: ctags-recursive $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    test -d "$(distdir)/$$subdir" \
	    || $(MKDIR_P) "$(distdir)/$$subdir" \
	    || exit 1; \
	  fi; \
	done
	@list='$(DIST_SUBDIRS)'; for subdir in $$list; do \
	  if test "$$subdir" = .; then :; else \
	    dir1=$$subdir; dir2="$(distdir)/$$subdir"; \
	    $(am__relativize); \
	    new_distdir=$$reldir; \
	    dir1=$$subdir; dir2="$(top_distdir)"; \
	    $(am__relativize); \
	    new_top_distdir=$$reldir; \
	    echo " (cd $$subdir && $(MAKE) $(AM_MAKEFLAGS) top_distdir="$$new_top_distdir" distdir="$$new_distdir" \\"; \
	    echo "     am__remove_distdir=: am__skip_length_check=: am__skip_mode_fix=: distdir)"; \
	    ($(am__cd) $$subdir && \
	      $(MAKE) $(AM_MAKEFLAGS) \
	        top_distdir="$$new_top_distdir" \
	        distdir="$$new_distdir" \
		am__remove_distdir=: \
		am__skip_length_check=: \
		am__skip_mode_fix=: \
	        distdir) \
	      || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-recursive
all-am: Makefile $(LIBRARIES) all-local
installdirs: installdirs-recursive
installdirs-am:
	for dir in "$(DESTDIR)$(libdir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-recursive
install-exec: install-exec-recursive
install-data: install-data-recursive
uninstall: uninstall-recursive

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-recursive
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-recursive

clean-am: clean-generic clean-libLIBRARIES mostlyclean-am

distclean: distclean-recursive
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-recursive

dvi-am:

html: html-recursive

html-am:

info: info-recursive

info-am:

install-data-am:

install-dvi: install-dvi-recursive

install-dvi-am:

install-exec-am: install-exec-local install-libLIBRARIES

install-html: install-html-recursive

install-html-am:

install-info: install-info-recursive

install-info-am:

install-man:

install-pdf: install-pdf-recursive

install-pdf-am:

install-ps: install-ps-recursive

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-recursive
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-recursive

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-recursive

pdf-am:

ps: ps-recursive

ps-am:

uninstall-am: uninstall-libLIBRARIES uninstall-local

.MAKE: $(RECURSIVE_CLEAN_TARGETS) $(RECURSIVE_TARGETS) all check \
	ctags-recursive install install-am install-strip \
	tags-recursive

.PHONY: $(RECURSIVE_CLEAN_TARGETS) $(RECURSIVE_TARGETS) CTAGS GTAGS \
	all all-am all-local check check-am clean clean-generic \
	clean-libLIBRARIES ctags ctags-recursive distclean \
	distclean-compile distclean-generic distclean-tags distdir dvi \
	dvi-am html html-am info info-am install install-am \
	install-data install-data-am install-dvi install-dvi-am \
	install-exec install-exec-am install-exec-local install-html \
	install-html-am install-info install-info-am \
	install-libLIBRARIES install-man install-pdf install-pdf-am \
	install-ps install-ps-am install-strip installcheck \
	installcheck-am installdirs installdirs-am maintainer-clean \
	maintainer-clean-generic mostlyclean mostlyclean-compile \
	mostlyclean-generic pdf pdf-am ps ps-am tags tags-recursive \
	uninstall uninstall-am uninstall-libLIBRARIES uninstall-local


-include $(libcore_deps)
%.o %.$(FC_MODEXT): %.F90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(PPFCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.obj %.$(FC_MODEXT): %.F90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(PPFCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.o %.$(FC_MODEXT): %.f90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(FCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.obj %.$(FC_MODEXT): %.f90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(FCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

all-local:
	@$(top_srcdir)/remove_stalemods.sh $(FC_MODEXT) $(sort $(lib_MODULES))

clean-stalemods:
	@if `/bin/ls *.o > /dev/null 2>&1`; \
	then \
	  for i in *.o; \
	   do \
	     root=`echo "$$i" | sed 's/\.o$$//'`; \
	     if test ! -e "$(srcdir)/$$root.f90" -a ! -e "$(srcdir)/$$root.F90" -a ! -e "$(srcdir)/$$root.c"; \
	     then \
	       echo "Removing $(srcdir)/$$root objects..."; \
	       /bin/rm -f "$$root.o" "$$root.$(FC_MODEXT)" "$$root.fh" "$$root.mod" "$$root.d"; \
	     fi; \
	   done; \
	fi

clean-stalemods-complex:
	@if `/bin/ls *.o > /dev/null 2>&1`; \
	then \
	  for i in *.o; \
	   do \
	     root=`echo "$$i" | sed 's/\.o$$//'`; \
	     if test ! -e "$(srcdir)/../$$root.f90" -a ! -e "$(srcdir)/../$$root.F90" -a ! -e "$(srcdir)/../$$root.c"; \
	     then \
	       echo "Removing $(srcdir)/../$$root objects..."; \
	       /bin/rm -f "$$root.o" "$$root.$(FC_MODEXT)" "$$root.fh" "$$root.mod" "$$root.d" "$$root.f90" "$$root.F90"; \
	     fi; \
	   done; \
	fi

ordered_targets:
	@$(MAKE) clean
	@$(MAKE) -n | grep "^\$(FC)" | sed 's/.*-o /       /' | \
	                            sed 's/\.o .*/\.f90 \\/'

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ > $@
install-exec-local:
	@$(NORMAL_INSTALL)
	test -z "$(libdir)" || $(mkdir_p) "$(DESTDIR)$(libdir)"
	@list="*.$(FC_MODEXT)"; for p in $$list; do \
	  if test -f $$p; then \
	    f=$(am__strip_dir) \
	    echo " $(libMODULES_INSTALL) '$$p' '$(DESTDIR)$(libdir)/$$f'"; \
	    $(libMODULES_INSTALL) "$$p" "$(DESTDIR)$(libdir)/$$f"; \
	  else :; fi; \
	done
	@$(POST_INSTALL)
	@if test -e Complex/libcore.a; then \
	  (cd Complex; $(MAKE) install) \
	else :; fi;

uninstall-local:
	@$(NORMAL_UNINSTALL)
	@list="*.$(FC_MODEXT)"; for p in $$list; do \
	  p=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(libdir)/$$p'"; \
	  rm -f "$(DESTDIR)$(libdir)/$$p"; \
	done
	@if test -e Complex/libcore.a; then \
	  (cd Complex; $(MAKE) uninstall) \
	else :; fi;

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

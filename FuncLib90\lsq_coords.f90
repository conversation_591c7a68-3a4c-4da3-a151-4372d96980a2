!================================= LSQ_COORDSN ===============================80
!
! Least squares coordinates (xie,eta,zie) for nodes.
!
!=============================================================================80

  pure function lsq_coords( lsq_map_ref, tf, xc, yc, zc, slenc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_map_ref

    real(dp),           intent(in) :: tf, xc, yc, zc, slenc

    real(dp),         dimension(4) :: lsq_coords

  continue

    if ( lsq_map_ref%mapr == 0 ) then !no transformation.

      lsq_coords(1) = xc - lsq_map_ref%xr
      lsq_coords(2) = yc - lsq_map_ref%yr
      lsq_coords(3) = zc - lsq_map_ref%zr

    else if ( lsq_map_ref%mapr == 1 ) then !cartesian transform.

      lsq_coords(1:3) = mapping_coords(xc, yc, zc,                             &
                                        lsq_map_ref%xr, lsq_map_ref%yr,        &
                                        lsq_map_ref%zr, lsq_map_ref%tr)

    else if ( lsq_map_ref%mapr == 2 ) then !distance mapping transform.

      lsq_coords(1:3) = mapping_coords(xc, yc, zc,                             &
                                        lsq_map_ref%xr, lsq_map_ref%yr,        &
                                        lsq_map_ref%zr, lsq_map_ref%tr,        &
                                        slenc, lsq_map_ref%slenr)

    else if ( lsq_map_ref%mapr == 3 ) then !cylindrical polar transform.

      lsq_coords(1:3) = coords_cylindrical_polar(xc, yc, zc, lsq_map_ref%xr,   &
                                                 lsq_map_ref%yr, lsq_map_ref%zr)

    end if

    lsq_coords(4) = 1.0_dp /                                                   &
                     (lsq_coords(1)**2+lsq_coords(2)**2+tf*lsq_coords(3)**2)

    lsq_coords(1:3) = lsq_coords(1:3)*lsq_map_ref%scaleir

  end function lsq_coords

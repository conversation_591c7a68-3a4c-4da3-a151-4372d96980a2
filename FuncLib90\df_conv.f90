!============================ DF_CONV ========================================80
!
! Flux difference split convection flux jacobians (within incompressible path).
!
!=============================================================================80

  subroutine df_conv(xnorm, ynorm, znorm, area, dfl, dfr )

    use kinddefs,        only : dp
    use convection_defs, only : cu, cv, cw

    real(dp), intent(in) :: xnorm, ynorm, znorm, area

    real(dp), dimension(4,4), intent(out) :: dfl, dfr

    real(dp) :: ubar, eig1

  continue

    dfl(:,:) = 0._dp
    dfr(:,:) = 0._dp

    ubar = xnorm*cu + ynorm*cv + znorm*cw
    eig1 = abs(ubar)

    dfl(1,1) = ( ubar + eig1 )*area*0.5_dp
    dfl(2,2) = ( ubar + eig1 )*area*0.5_dp
    dfl(3,3) = ( ubar + eig1 )*area*0.5_dp
    dfl(4,4) = ( ubar + eig1 )*area*0.5_dp

    dfr(1,1) = ( ubar - eig1 )*area*0.5_dp
    dfr(2,2) = ( ubar - eig1 )*area*0.5_dp
    dfr(3,3) = ( ubar - eig1 )*area*0.5_dp
    dfr(4,4) = ( ubar - eig1 )*area*0.5_dp

  end subroutine df_conv

module sensitivity

  implicit none

  private

  public :: perform_sensitivity_analysis
  public :: sensitivity_counter

  integer, dimension(:), pointer :: sensitivity_counter

contains

!===================== PERFORM_SENSITIVITY_ANALYSIS ==========================80
!
!  Perform an adjoint-based sensitivity analysis
!
!    - Execute parameterization package for each body to obtain
!      surface grid sensitivities
!    - Run adjoint solver
!
!  We come in here assuming we are in the top level directory
!  looking down on Adjoint/Flow/Rubberize directories
!
!=============================================================================80
  subroutine perform_sensitivity_analysis(restart,imodel,n_design_pts,         &
                                          what_to_do,lss_flag)

    use design_types,         only : design_type, command_type, sleep_delay,   &
                                     max_string_length
    use design_io_helpers,    only : set_up_design ! complex join point
    use designs,              only : read_command_lines, free_design,          &
                                     clo_exists, build_command, write_customdv,&
                                     find_clo_np, get_input_data,              &
                                     write_jet_dvs, write_sculptor,            &
                                     save_user_files
    use system_extensions,    only : se_shell, se_chdir, se_sleep, se_flush
    use string_utils,         only : sprintf, max_str_len, int_to_s
    use nml_code_run_control, only : sd_file_format
    use lmpi,                 only : lmpi_die
    use nml_design,           only : mpirun_prefix, n_body_transforms,         &
                                     body_transforms, adjoint_nproc,           &
                                     user_def_executable

    integer, intent(in)    :: imodel, n_design_pts, what_to_do
    integer, intent(inout) :: restart

    logical, intent(in) :: lss_flag

    type(design_type)                         :: design
    type(command_type), dimension(:), pointer :: command_line_options

    character(len=80)                :: massoud_command_prefix = 'massoud '
    character(len=80)                :: sculptor_command_prefix = 'sculptor -d '
    character(len=80)                :: jets_command_prefix = 'jets '
    character(len=80)                :: massoud_command, jets_command
    character(len=80)                :: sculptor_command
    character(max_str_len)           :: filename
    character(len=180)               :: rutro_command
    character(len=80)                :: project, temp_string
    character(len=max_string_length) :: dualsolve_command
    character(len=max_string_length) :: dualsolve_command1
    character(len=max_string_length) :: dualsolve_command2
    character(len=max_string_length) :: chaos_command
    character(len=20)                :: grid_format, data_format

    integer :: ncommand_line_options, num_spatial_parts
    integer :: i, eqn_set, ivisc, j, itime, ncyc, nslices
    integer :: code

  continue

! Increment the counter for the number of times we've been in here for the
! current design point

    sensitivity_counter(imodel) = sensitivity_counter(imodel) + 1

! Get the current design information - we have to be in a lower directory
! for this routine

    call se_chdir('Flow')
    call get_input_data(project,eqn_set,ivisc,itime,ncyc,grid_format,          &
                        data_format,nslices)
    call set_up_design(eqn_set, itime, design)
    call se_chdir('..')

! Get the command line options to use for each code

    call read_command_lines(ncommand_line_options, command_line_options)

! compute the surface grid linearizations for each body

    shape_gradient : if ( design%shape_active ) then

      body_loop : do i = 1, design%nbodies

        select case(design%body_data(i)%parameterization)
        case (0)    ! fake body; do nothing
        case (1)    ! run massoud

          call write_customdv(i,design%body_data(i)%ndv,                       &
                              design%body_data(i)%value)

          massoud_command = trim(massoud_command_prefix) // ' ' //             &
                            sprintf('massoud.%i0',i)

          call se_chdir('Rubberize')

          write(*,*) 'Sleeping to allow file system time to catch up...'
          call se_flush(6)
          call se_sleep(sleep_delay)
          write(*,*) 'Executing: ',trim(massoud_command),' Body=',i
          call se_shell(massoud_command)

          do j = 1, n_body_transforms
            if ( body_transforms(j) == i ) then
              if ( trim(sd_file_format) == 'stream' ) then
                filename = trim(sprintf('model.ddf.%i0',i))//'.sd1'
                rutro_command = 'rutro --ddf '//trim(filename)//' -t'
              else
                filename = trim(sprintf('model.tec.%i0',i))//'.sd1'
                rutro_command = 'rutro '      //trim(filename)//' -t'
              endif
              filename = sprintf('transforms.%i0',i)
              rutro_command = trim(rutro_command)//' '//trim(filename)//' -i'
              write(*,*) 'Sleeping to allow file system time to catch up...'
              call se_flush(6)
              call se_sleep(sleep_delay)
              write(*,*) 'Executing: ',trim(rutro_command)
              call se_shell(rutro_command)
            endif
          end do

          call se_chdir('..')

        case (2)    ! nothing for bandaids

        case (3)    ! run jets

          call write_jet_dvs(i,design%body_data(i)%ndv,                        &
                             design%body_data(i)%value)

          jets_command = &
            trim(jets_command_prefix)//' '//trim(sprintf('jets.%i0',i))//' 1'

          call se_chdir('Rubberize')

          write(*,*) 'Sleeping to allow file system time to catch up...'
          call se_flush(6)
          call se_sleep(sleep_delay)
          write(*,*) 'Executing: ',trim(jets_command)
          call se_shell(jets_command)

          call se_chdir('..')

        case (4)    ! run sculptor

          call write_sculptor(project,design%body_data(i)%ndv,                 &
                              design%body_data(i)%value)

          sculptor_command = trim(sculptor_command_prefix)//' '//              &
                            trim(project)//'.def'

          call se_chdir('Rubberize')

          write(*,*) 'Sleeping to allow file system time to catch up...'
          call se_flush(6)
          call se_sleep(sleep_delay)
          write(*,*) 'Executing: ',trim(sculptor_command),' Body=',i
          call se_shell(sculptor_command)

          call se_chdir('..')

        case (5)    ! run user-defined

          call write_customdv(i,design%body_data(i)%ndv,                       &
                              design%body_data(i)%value)

          massoud_command= trim(user_def_executable) // ' ' // trim(int_to_s(i))

          call se_chdir('Rubberize')

          write(*,*) 'Sleeping to allow file system time to catch up...'
          call se_flush(6)
          call se_sleep(sleep_delay)
          write(*,*) 'Executing: ',trim(massoud_command),' Body=',i
          call se_shell(massoud_command)

          call se_chdir('..')

        case default

          write(*,*) 'Unknown parameterization type in perform_sens_analysis.'
          stop

        end select

      end do body_loop

    endif shape_gradient

! run the adjoint solver

! Put the command line for the dual solver together

    dualsolve_command = mpirun_prefix

! Add any MPI options

    call build_command(ncommand_line_options, command_line_options,            &
                       dualsolve_command, 'mpirun')

! Specify the number of processors explicitly in case suggar was used with
! flow solver, but only if the user hasn't already specified a -np option.
! If they have, let their input trump this.

    num_spatial_parts = -1

    if (clo_exists(ncommand_line_options,command_line_options,'-np ','mpirun'))&
                                                                            then
      write(*,*) 'Detected a -np option specified by the user for the adjoint'
      write(*,*) 'solver.  Using this value for adjoint solver execution...'

      num_spatial_parts= find_clo_np(ncommand_line_options,command_line_options)
    else
      if (adjoint_nproc > 0) then
        dualsolve_command = trim(dualsolve_command) // ' -np'
        write(temp_string,'(i10)') adjoint_nproc
        dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
        num_spatial_parts = adjoint_nproc  ! But will not work if comm is split!
      else
        if ( lss_flag ) then
          write(*,*) 'Error: no way to determine number of spatial partitions'
          write(*,*) 'for LSS command line.'
          call lmpi_die
          stop
        endif
      endif
    endif

! Add the executable name

    dualsolve_command = trim(dualsolve_command) // ' dual_mpi'

! Add any command line options

    call build_command(ncommand_line_options, command_line_options,            &
                       dualsolve_command, 'adjoint')

! Add current Mach number and angle of attack if active

    if ( design%mach_active ) then
      dualsolve_command = trim(dualsolve_command) // ' --xmach '
      write(temp_string,'(f25.15)') design%mach_value
      dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
    endif

    if ( design%alpha_active ) then
      dualsolve_command = trim(dualsolve_command) // ' --alpha '
      write(temp_string,'(f25.15)') design%alpha_value
      dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
    endif

    if ( design%yaw_active ) then
      dualsolve_command = trim(dualsolve_command) // ' --yaw '
      write(temp_string,'(f25.15)') design%yaw_value
      dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
    endif

    if ( design%xrate_active ) then
      dualsolve_command = trim(dualsolve_command) // ' --xrotrate_ni '
      write(temp_string,'(f25.15)') design%xrate_value
      dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
    endif

    if ( design%yrate_active ) then
      dualsolve_command = trim(dualsolve_command) // ' --yrotrate_ni '
      write(temp_string,'(f25.15)') design%yrate_value
      dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
    endif

    if ( design%zrate_active ) then
      dualsolve_command = trim(dualsolve_command) // ' --zrotrate_ni '
      write(temp_string,'(f25.15)') design%zrate_value
      dualsolve_command = trim(dualsolve_command) // ' ' // trim(temp_string)
    endif

! If steady, add the getgrad command line option and the restart option

    if ( itime == 0 ) then
      dualsolve_command=trim(dualsolve_command) // ' --getgrad'
      dualsolve_command=sprintf(trim(dualsolve_command)//' --irest %i0',restart)
    endif

! We have written out the grid in stream format, so force this

    dualsolve_command  = trim(dualsolve_command) // ' --force_stream_file'

! Go down to the Adjoint directory and call the dual solver

    call se_chdir('Adjoint')

    write(*,*) 'Sleeping to allow file system time to catch up...'
    call se_flush(6)
    call se_sleep(sleep_delay)

    shadowing_or_not : if ( lss_flag ) then

      dualsolve_command1 = trim(dualsolve_command) // ' --write_for_chaos'

      write(*,*) 'Executing: ',trim(dualsolve_command1)
      call se_shell(dualsolve_command1, code)
      if (code /= 0) then
        write(*,*) 'Failed: ',trim(dualsolve_command1)
        stop
      end if

      chaos_command = trim(mpirun_prefix) // ' -np ' // trim(int_to_s(ncyc))   &
                      // ' ./chaos ' // trim(int_to_s(num_spatial_parts))      &
                      // ' ' // trim(project)
      write(*,*) 'Sleeping to allow file system time to catch up...'
      call se_flush(6)
      call se_sleep(sleep_delay)
      write(*,*) 'Executing: ',trim(chaos_command)
      call se_shell(chaos_command, code)
      if (code /= 0) then
        write(*,*) 'Failed: ',trim(chaos_command)
        stop
      end if

      dualsolve_command2 = trim(dualsolve_command) // ' --read_for_chaos'

      write(*,*) 'Sleeping to allow file system time to catch up...'
      call se_flush(6)
      call se_sleep(sleep_delay)
      write(*,*) 'Executing: ',trim(dualsolve_command2)
      call se_shell(dualsolve_command2, code)
      if (code /= 0) then
        write(*,*) 'Failed: ',trim(dualsolve_command2)
        stop
      end if

    else shadowing_or_not

      write(*,*) 'Executing: ',trim(dualsolve_command)
      call se_shell(dualsolve_command, code)
      if (code /= 0) then
        write(*,*) 'Failed: ',trim(dualsolve_command)
        stop
      end if

    endif shadowing_or_not

    call se_chdir('..')

! If steady, we can restart from now on

    if ( itime == 0 ) restart = 1

! Free the memory we have allocated in here

    call free_design(design)

    do i = 1, ncommand_line_options
      deallocate(command_line_options(i)%command)
    end do

! Check for files_to_save.data and act on any with the keyword 'Adjoint',
! appending the current counter to any specified filenames

    call save_user_files('Adjoint',sensitivity_counter(imodel))

! If all we wanted to do was a sensitivity analysis and we're on the last model,
! then we're done - stop

    if ( sensitivity_counter(imodel) == 1 .and. imodel == n_design_pts .and.   &
         what_to_do == 2) then
      write(*,*) 'Sensitivity analysis complete.'
      stop
    end if

  end subroutine perform_sensitivity_analysis

end module sensitivity

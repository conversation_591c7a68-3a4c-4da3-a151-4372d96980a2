Types = \
	averaging_types.f90 \
	crow_na_types.f90 \
	cut_types.f90 \
	design_types.f90 \
	grid_types.f90 \
	line_types.f90 \
	lsq_types.f90 \
	moving_body_types.f90 \
	propulsion_types.f90 \
	relax_types.f90 \
	sampling_types.f90

Defs = \
	agglom_defs.f90 \
	b_q_defs.f90 \
	cc_defs.f90 \
	cell_agglom_defs.f90 \
	cfl_defs.f90 \
	dual_agglom_defs.f90 \
	element_defs.f90 \
	exact_defs.f90 \
	gcr_defs.f90 \
	lsq_defs.f90 \
	metis_defs.f90 \
	multigrid_defs.f90 \
	overset_defs.f90 \
	rad_defs.f90 \
	stability_defs.f90 \
	wu_defs.f90
	
libdefs_SRCS = \
	$(Types) \
	$(Defs) \
	grid_helper.f90 \
	inviscid_flux.f90 \
	lsq_constants.f90 \
	real_constants.f90 \
	refine_adaptation_input.f90 \
	twod_util.f90 \
	convection.f90 \
	pundit.F90 \
	suggar_info.f90 \
	io_helpers.f90 \
	code_status.f90 \
	timings.f90 \
	openacc.F90 \
	refgeom.f90 \
	linear_systems.f90

if BUILD_MPI
AM_FCFLAGS = \
	$(FC_MODINC)@MPIINC@ \
	$(FC_MODINC)@top_builddir@ \
	$(FC_MODINC)$(LIBCORE_DIR)
else
AM_FCFLAGS = \
	$(FC_MODINC)@top_builddir@ \
	$(FC_MODINC)$(LIBCORE_DIR)
endif

# FIXME: remove *.mod *.fh when mod_suffix works on OS X
CLEANFILES = *.$(FC_MODEXT) mpif.h *.time *.mod *.fh *.d

libdefs_f90s=$(libdefs_SRCS:.F90=.f90)
libdefs_deps=$(libdefs_f90s:.f90=.d)

SUFFIXES = .d

BUILT_SOURCES = $(libdefs_deps)

-include $(libdefs_deps)
include $(top_srcdir)/make.rules

ordered_targets:
	@$(MAKE) clean
	@$(MAKE) -n | grep "^\$(FC)" | sed 's/.*-o /	   /' | \
								sed 's/\.o .*/\.f90 \\/'

lib_MODULES = $(libdefs_f90s:.f90=.$(FC_MODEXT))

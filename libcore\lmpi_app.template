! -*- f90 -*- this turns on the emacs f90 mode
!
! Run fortran_template.rb to generate lmpi.F90 from lmpi_app.template
! then check both this file and lmpi_app.F90 into the repository.
! ./fortran_template.rb lmpi_app

module lmpi_app

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,    only : dp, system_i1, system_r4, system_r8, system_i8, dqp, &
                          system_i4

  use interp_defs, only : sr, sr_allocated, sendrecv_type,                     &
                          basic_sendrecv_type
  use lmpi,        only : lmpi_id, lmpi_nproc, lmpi_master,                    &
                          lmpi_reduce, lmpi_max, lmpi_bcast,                   &
#ifdef TIME_MPI
                          lmpi_time, lmpi_timed, lmpi_synchronize,             &
                          lmpi_min,                                            &
#endif
                          lmpi_reduce_maxloc2
#ifdef HAVE_MPI
  use interp_defs, only : nullify_sendrecv
  use lmpi,        only : lmpi_die, lmpi_comm, lmpi_waitall,                   &
                          sendreq, recvreq,                                    &
                          lmpi_send, lmpi_recv,                                &
                          lmpi_success, lmpi_error, lmpi_status_size,          &
                          lmpi_isend, lmpi_irecv
#endif

  implicit none

  private

#ifdef HAVE_MPI
  include 'mpif.h'
  integer :: nsend, nrecv ! number of send and recv messages
#endif

#ifdef HAVE_MPI
  interface operator(+)
    module procedure add_logical
  end interface

  interface operator(*)
    module procedure multiply_logical_logical
    module procedure multiply_single_logical
    module procedure multiply_double_logical
    module procedure multiply_complex_r4_logical
    module procedure multiply_complex_r8_logical
  end interface

  interface max
    module procedure max_logical
    module procedure max_complex_r4
    module procedure max_complex_r8
  end interface
#endif

  public :: lmpi_collect_res
  interface lmpi_collect_res
    module procedure double_collect_res
    module procedure double_complex_collect_res
  end interface

  public :: lmpi_start_cudampi_xfer
  interface lmpi_start_cudampi_xfer
    module procedure lmpi_start_cudampi_xfer_r4
    module procedure lmpi_start_cudampi_xfer_r8
    module procedure lmpi_start_cudampi_xfer_c4
    module procedure lmpi_start_cudampi_xfer_c8
  end interface

  public :: lmpi_complete_cudampi_xfer
  interface lmpi_complete_cudampi_xfer
    module procedure lmpi_complete_cudampi_xfer_r4
    module procedure lmpi_complete_cudampi_xfer_r8
    module procedure lmpi_complete_cudampi_xfer_c4
    module procedure lmpi_complete_cudampi_xfer_c8
  end interface

!tempInsertInterface

! ---------------------------------------- private only varables

  integer, parameter :: ndatalevel        = 4 ! number of mpi ghost levels
  integer            :: defaultghostlevel = 1 ! default mpi ghost level
  integer, parameter :: edgeghostlevel    = 3 ! mpi ghost level for edges

  integer :: gl = 1         ! Current grid level
  integer :: max_gl = 1     ! Max number of grid levels


  ! check_xfer variable to check array bounds for lmpi_xfer

#if HAVE_MPI
  logical :: check_xfer = .false., check_xfer01 = .false.
#endif
  integer, dimension(20) :: xfer01

! sr1 has the send and recv data pairs for level-1 nodes
! sre has the send and recv data pairs for edges
! src has the send and recv data pairs for cells (cell-centered)

  type (basic_sendrecv_type), dimension(:), pointer :: sr1, src

  public :: sr1, src

#ifdef TIME_MPI
! Timing

    logical :: lmpi_app_timing      = .false.
    logical :: lmpi_app_timing_sync = .false.

    real(dp) :: lmpi_start_time     = 0.0_dp
    real(dp) :: lmpi_end_time       = 0.0_dp

    integer  :: lmpi_xfer1_count = 0
    integer  :: lmpi_xfer2_count = 0
    integer  :: lmpi_xfer3_count = 0
    integer  :: lmpi_xfer4_count = 0
    integer  :: lmpi_xfer5_count = 0
    integer  :: lmpi_xfer6_count = 0
    real(dp) :: lmpi_xfer1_time  = 0.0_dp
    real(dp) :: lmpi_xfer2_time  = 0.0_dp
    real(dp) :: lmpi_xfer3_time  = 0.0_dp
    real(dp) :: lmpi_xfer4_time  = 0.0_dp
    real(dp) :: lmpi_xfer5_time  = 0.0_dp
    real(dp) :: lmpi_xfer6_time  = 0.0_dp
#endif

! CUDA-enabled MPI

    public :: openacc_sendproc, openacc_recvproc
    public :: openacc_sendindex, openacc_recvindex
    public :: openacc_sendproc_offset, openacc_recvproc_offset
    public :: openacc_sendindex_offset, openacc_recvindex_offset
    public :: openacc_sendbuf, openacc_recvbuf
    public :: openacc_csendbuf, openacc_crecvbuf

    integer, dimension(:), pointer :: openacc_sendproc         => null()
    integer, dimension(:), pointer :: openacc_recvproc         => null()
    integer, dimension(:), pointer :: openacc_sendindex        => null()
    integer, dimension(:), pointer :: openacc_recvindex        => null()
    integer, dimension(:), pointer :: openacc_sendproc_offset  => null()
    integer, dimension(:), pointer :: openacc_recvproc_offset  => null()
    integer, dimension(:), pointer :: openacc_sendindex_offset => null()
    integer, dimension(:), pointer :: openacc_recvindex_offset => null()

    real(dqp), dimension(:,:), pointer :: openacc_sendbuf => null()
    real(dqp), dimension(:,:), pointer :: openacc_recvbuf => null()

    complex(dqp), dimension(:,:), pointer :: openacc_csendbuf => null()
    complex(dqp), dimension(:,:), pointer :: openacc_crecvbuf => null()

  contains

!================================== lmpi_set_defaultghostlevel ===============80
!
! Set defaultghostlevel
!
!=============================================================================80

  subroutine lmpi_set_defaultghostlevel(ghost_level_arg)

    integer,  intent(in) :: ghost_level_arg

    continue

    defaultghostlevel = ghost_level_arg

  end subroutine lmpi_set_defaultghostlevel

!=========================== LMPI_SET_CHECK_XFER01 ===========================80
!
! set check_xfer01 (to bypass checks for info stored only on fine grid).
!
!=============================================================================80

  subroutine lmpi_set_check_xfer01( argument )

    logical, intent(in) :: argument

  continue

#ifdef HAVE_MPI
    check_xfer01 = argument
#else
    print*,argument
#endif

  end subroutine lmpi_set_check_xfer01

!================================== LMPI_SET_XFER01 ==========================80
!
! set xfer01 information.
!
!=============================================================================80

  subroutine lmpi_set_xfer01( fl, argument )

    use lmpi, only : lmpi_conditional_stop

    integer, intent(in) :: fl, argument

    integer :: ierr

  continue

    ierr = 0
    if ( fl > 20 ) ierr = 1
    call lmpi_conditional_stop(ierr,'xfer01 size:set_xfer01')
    xfer01(fl) = argument
#ifdef HAVE_MPI
    if ( fl == max_gl ) check_xfer01 = .true.
#endif

  end subroutine lmpi_set_xfer01

!================================== CHECK_SIZE01 =============================80
!
! check size of xfer information
!
!=============================================================================80

  subroutine check_size01( argument, site )

    use lmpi, only : lmpi_conditional_stop

    integer,                intent(in) :: argument
    character(*), optional, intent(in) :: site

    integer :: ierr

  continue

    ierr = xfer01(gl) - argument
    if ( ierr /= 0 ) then
       write(*,*) ' gl=',gl,' xfer size array=',argument,&
                            ' expected size=',xfer01(gl)
      if ( present(site) ) write(*,*) ' site=',site
    endif
    call lmpi_conditional_stop(ierr,'multigrid xfer issue:check_size01')

  end subroutine check_size01

!========================== LMPI_APP_TIME ====================================80
!
! Start/stop lmpi_app timing.
!
!=============================================================================80

  subroutine lmpi_app_time(flag)

  character(len=*), intent(in) :: flag
#ifdef TIME_MPI
  character(len=10) :: text10

  integer  :: iunit
  integer  :: imin, imax
  real(dp) :: rmin, rmax

   continue

   iunit = 1000+lmpi_id

   text10 = "          "
   text10 = flag

   if ((text10(1:4) == 'stop').or.(text10(1:4) == 'STOP')) then

      lmpi_app_timing     = .false.

      call lmpi_time(flag)

      if (lmpi_xfer1_count > 0) then
       write(iunit,*) lmpi_id,': xfer ',lmpi_xfer1_count,lmpi_xfer1_time
       call lmpi_min(lmpi_xfer1_time,rmin)
       call lmpi_max(lmpi_xfer1_time,rmax)
       call lmpi_min(lmpi_xfer1_count,imin)
       call lmpi_max(lmpi_xfer1_count,imax)
       if (lmpi_master) write(*,'(a25,F20.12,1x,F20.12,"  (",2(i12,1x),")")')  &
          'min,max xfer        = ',rmin,rmax,imin,imax
      end if

      if (lmpi_xfer2_count > 0) then
       write(iunit,*) lmpi_id,': xfer start ',lmpi_xfer2_count,lmpi_xfer2_time
       call lmpi_min(lmpi_xfer2_time,rmin)
       call lmpi_max(lmpi_xfer2_time,rmax)
       call lmpi_min(lmpi_xfer2_count,imin)
       call lmpi_max(lmpi_xfer2_count,imax)
       if (lmpi_master) write(*,'(a25,F20.12,1x,F20.12,"  (",2(i12,1x),")")')  &
          'min,max xfer start  = ',rmin,rmax,imin,imax
      end if

      if (lmpi_xfer3_count > 0) then
       write(iunit,*) lmpi_id,': sumnode ',lmpi_xfer3_count,lmpi_xfer3_time
       call lmpi_min(lmpi_xfer3_time,rmin)
       call lmpi_max(lmpi_xfer3_time,rmax)
       call lmpi_min(lmpi_xfer3_count,imin)
       call lmpi_max(lmpi_xfer3_count,imax)
       if (lmpi_master) write(*,'(a25,F20.12,1x,F20.12,"  (",2(i12,1x),")")')  &
          'min,max sumnode     = ',rmin,rmax,imin,imax
      end if

      if (lmpi_xfer4_count > 0) then
       write(iunit,*) lmpi_id,': interp ',lmpi_xfer4_count,lmpi_xfer4_time
       call lmpi_min(lmpi_xfer4_time,rmin)
       call lmpi_max(lmpi_xfer4_time,rmax)
       call lmpi_min(lmpi_xfer4_count,imin)
       call lmpi_max(lmpi_xfer4_count,imax)
       if (lmpi_master) write(*,'(a25,F20.12,1x,F20.12,"  (",2(i12,1x),")")')  &
          'min,max interp      = ',rmin,rmax,imin,imax
      end if

      if (lmpi_xfer5_count > 0) then
       write(iunit,*) lmpi_id,': restrict ',lmpi_xfer5_count,lmpi_xfer5_time
       call lmpi_min(lmpi_xfer5_time,rmin)
       call lmpi_max(lmpi_xfer5_time,rmax)
       call lmpi_min(lmpi_xfer5_count,imin)
       call lmpi_max(lmpi_xfer5_count,imax)
       if (lmpi_master) write(*,'(a25,F20.12,1x,F20.12,"  (",2(i12,1x),")")')  &
          'min,max restrict    = ',rmin,rmax,imin,imax
      end if

      if (lmpi_xfer6_count > 0) then
       write(iunit,*) lmpi_id,': xfer jac ',lmpi_xfer6_count,lmpi_xfer6_time
       call lmpi_min(lmpi_xfer6_time,rmin)
       call lmpi_max(lmpi_xfer6_time,rmax)
       call lmpi_min(lmpi_xfer6_count,imin)
       call lmpi_max(lmpi_xfer6_count,imax)
       if (lmpi_master) write(*,'(a25,F20.12,1x,F20.12,"  (",2(i12,1x),")")')  &
          'min,max xfer jac    = ',rmin,rmax,imin,imax
      end if

      lmpi_xfer1_count = 0
      lmpi_xfer2_count = 0
      lmpi_xfer3_count = 0
      lmpi_xfer4_count = 0
      lmpi_xfer5_count = 0
      lmpi_xfer6_count = 0
      lmpi_xfer1_time  = 0.0_dp
      lmpi_xfer2_time  = 0.0_dp
      lmpi_xfer3_time  = 0.0_dp
      lmpi_xfer4_time  = 0.0_dp
      lmpi_xfer5_time  = 0.0_dp
      lmpi_xfer6_time  = 0.0_dp

   end if

   if ((text10(1:10) == 'start_sync').or.(text10(1:10) == 'START_SYNC')) then
      lmpi_app_timing_sync = .true.
      if (lmpi_master) write(*,*)'LMPI_APP START_SYNC'
   end if

   if ((text10(1:5) == 'start').or.(text10(1:5) == 'START')) then

      if (lmpi_master) write(*,*)'LMPI_APP START'
      lmpi_app_timing     = .true.
      call lmpi_time(flag)

      lmpi_xfer1_count = 0
      lmpi_xfer2_count = 0
      lmpi_xfer3_count = 0
      lmpi_xfer4_count = 0
      lmpi_xfer5_count = 0
      lmpi_xfer6_count = 0
      lmpi_xfer1_time  = 0.0_dp
      lmpi_xfer2_time  = 0.0_dp
      lmpi_xfer3_time  = 0.0_dp
      lmpi_xfer4_time  = 0.0_dp
      lmpi_xfer5_time  = 0.0_dp
      lmpi_xfer6_time  = 0.0_dp

   end if
#else
   continue

   if (lmpi_master) write(*,*)'lmpi_app_time called but not instrumented ',flag
#endif

  end subroutine lmpi_app_time

!================================== LMPI_GRID_LEVEL ==========================80
!
! returns the current multigrid level
!
!=============================================================================80

  function lmpi_grid_level()

    integer :: lmpi_grid_level

!  integer grid_level is defined at top as a "global" variable

    continue

    lmpi_grid_level = gl

  end function lmpi_grid_level


!============================== LMPI_MAX_GRID_LEVEL ==========================80
!
! returns the max multigrid level
!
!=============================================================================80

  function lmpi_max_grid_level()

    integer :: lmpi_max_grid_level

!  integer max_grid_level is defined at top as a "global" variable

    continue

    lmpi_max_grid_level = max_gl

  end function lmpi_max_grid_level


!============================== LMPI_SET_GRID_LEVEL ==========================80
!
! sets the current multigrid level
!
!=============================================================================80

  subroutine lmpi_set_grid_level(level)

    integer, intent(in) :: level

!  integer grid_level is defined at top as a "global" variable

    continue

    gl = level

  end subroutine lmpi_set_grid_level


!========================== LMPI_SET_MAX_GRID_LEVEL ==========================80
!
! sets the max multigrid level
!
!=============================================================================80

  subroutine lmpi_set_max_grid_level(level)

    integer, intent(in) :: level

!  integer max_grid_level is defined at top as a "global" variable

    continue

    max_gl = level

  end subroutine lmpi_set_max_grid_level


!========================== LMPI_SET_CHECK_XFER ==============================80
!
! sets the default check_xfer value used in lmpi_xfer routines
!
!=============================================================================80

  subroutine lmpi_set_check_xfer(check_xfer_value)

    logical, optional, intent(in) :: check_xfer_value

    continue

#ifdef HAVE_MPI
    if(present(check_xfer_value)) then
      check_xfer = check_xfer_value
    else
      check_xfer = .true.
    endif
#else
    if(present(check_xfer_value)) print*,check_xfer_value
#endif

  end subroutine lmpi_set_check_xfer


!================================== LMPI_README ==============================80
!
! Fills the the sendproc, recvproc, sendindex, and recvindex arrays
! by reading from gridpart file (public)
!
!
!=============================================================================80

  subroutine lmpi_readme(iunit, ghost_level_arg)

    use lmpi,        only : lmpi_conditional_stop

    integer,           intent(in) :: iunit
    integer, optional, intent(in) :: ghost_level_arg

    integer :: npart_loc, idata
    integer :: local_ndatalevel
    integer :: read_status
#ifdef HAVE_MPI
    integer :: i,j
#endif

    continue

    if (present(ghost_level_arg)) defaultghostlevel = ghost_level_arg

    local_ndatalevel = ndatalevel
!    local_ndatalevel = 3 ! uncomment to read pre-cell-centered part files

    read_status = 0

#ifdef HAVE_MPI
    if ( .not. sr_allocated ) then
      allocate(sr(ndatalevel,max_gl))
      sr_allocated = .TRUE.
      do i=1,ndatalevel
        do j=1,max_gl
          call nullify_sendrecv(sr(i,j))
        end do
      end do
    end if

    if ( 0 == read_status )                                                    &
      read(iunit,iostat=read_status) npart_loc,                                &
        ( sr(idata,gl)%size_sendindex, sr(idata,gl)%size_recvindex,            &
          idata = 1, local_ndatalevel )
#else
    read(iunit) npart_loc
    if (.false.) write(*,*) npart_loc ! avoid compiler warnings
#endif

#ifdef HAVE_MPI
    if (npart_loc > 1 ) then

      do idata = 1, ndatalevel
        call lmpi_deallocate_sendrecv(idata)

        sr(idata,gl)%sendrecv_allocated = .true.
        allocate(sr(idata,gl)%sendproc(npart_loc+1))
        allocate(sr(idata,gl)%recvproc(npart_loc+1))
        i = max(1,sr(idata,gl)%size_sendindex)
        j = max(1,sr(idata,gl)%size_recvindex)
        allocate(sr(idata,gl)%sendindex(i))
        allocate(sr(idata,gl)%recvindex(j))

        data_present_in_file : if ( idata <= local_ndatalevel ) then
          if ( 0 == read_status )                                              &
            read(iunit,iostat=read_status)                                     &
              (sr(idata,gl)%sendproc(i),i=1,npart_loc+1),                      &
              (sr(idata,gl)%recvproc(i),i=1,npart_loc+1)
          if ( 0 == read_status )                                              &
            read(iunit,iostat=read_status)                                     &
              (sr(idata,gl)%sendindex(i),i=1,sr(idata,gl)%size_sendindex),     &
              (sr(idata,gl)%recvindex(i),i=1,sr(idata,gl)%size_recvindex)
        end if data_present_in_file

      enddo

    else

      do idata = 1, local_ndatalevel
        if ( 0 == read_status )                                                &
          read(iunit,iostat=read_status) ! one level of stuff
        if ( 0 == read_status )                                                &
          read(iunit,iostat=read_status) ! one level of stuff
      enddo

    endif

#else
    do idata = 1, local_ndatalevel
        if ( 0 == read_status )                                                &
          read(iunit,iostat=read_status) ! one level of stuff
        if ( 0 == read_status )                                                &
          read(iunit,iostat=read_status) ! one level of stuff
    end do
#endif

    read_status_error : if ( 0 /= read_status ) then
      write(*,*)                                                               &
        'part file read error in lmpi_app:lmpi_readme, ',                      &
        'this may be due to old part files, ',                                 &
        'see comments in this subroutine to fix'
    end if read_status_error

    call lmpi_conditional_stop(read_status)

  end subroutine lmpi_readme


!================================== LMPI_WRITEME =============================80
!
! Writes the the sendproc, recvproc, sendindex, and recvindex arrays
!  to gridpart file (public)
!
!=============================================================================80

  subroutine lmpi_writeme(iunit, outformat)

    integer, intent(in) :: iunit
    logical, intent(in) :: outformat

    character(len=80) :: iformatdesc
    integer           :: idata

#ifdef HAVE_MPI
    integer :: i
    integer :: nparts
#endif

    continue

    iformatdesc = '(i10)'

#ifdef HAVE_MPI
    nparts = lmpi_nproc

    if (outformat) then
      write (iunit,iformatdesc) nparts,                                        &
        (sr(idata,gl)%size_sendindex, sr(idata,gl)%size_recvindex,             &
        idata=1,ndatalevel)
    else
      write (iunit) nparts,                                                    &
        (sr(idata,gl)%size_sendindex, sr(idata,gl)%size_recvindex,             &
        idata=1,ndatalevel)
    endif

    if (nparts > 1 ) then

      if (outformat) then

        do idata =1,ndatalevel

          write(iunit,iformatdesc)                                             &
            (sr(idata,gl)%sendproc(i),i=1,nparts+1),                           &
            (sr(idata,gl)%recvproc(i),i=1,nparts+1)
          write(iunit,iformatdesc)                                             &
            (sr(idata,gl)%sendindex(i),i=1,sr(idata,gl)%size_sendindex),       &
            (sr(idata,gl)%recvindex(i),i=1,sr(idata,gl)%size_recvindex)

        enddo

      else

        do idata =1,ndatalevel

          write(iunit)                                                         &
            (sr(idata,gl)%sendproc(i),i=1,nparts+1),                           &
            (sr(idata,gl)%recvproc(i),i=1,nparts+1)
          write(iunit)                                                         &
            (sr(idata,gl)%sendindex(i),i=1,sr(idata,gl)%size_sendindex),       &
            (sr(idata,gl)%recvindex(i),i=1,sr(idata,gl)%size_recvindex)

        enddo

      endif

    else

      if (outformat) then

        do idata = 1, ndatalevel

          write(iunit,iformatdesc) -999 ! one level of stuff
          write(iunit,iformatdesc) -999 ! one level of stuff

        enddo

      else

        do idata = 1, ndatalevel

          write(iunit) -999 ! one level of stuff
          write(iunit) -999 ! one level of stuff

        enddo

      endif

    endif

#else
    if (outformat) then
      write(iunit,iformatdesc) lmpi_nproc
      do idata = 1, ndatalevel
        write(iunit,iformatdesc) -999 ! one level of stuff
        write(iunit,iformatdesc) -999 ! one level of stuff
      end do
    else
      write(iunit) lmpi_nproc
      do idata = 1, ndatalevel
        write(iunit) -999 ! one level of stuff
        write(iunit) -999 ! one level of stuff
      end do
    end if
#endif

  end subroutine lmpi_writeme


!================================== LMPI_GETPROC_XFER ========================80
!
! Get send/recv proc.
!
!=============================================================================80

  subroutine lmpi_getproc_xfer(sendproc, recvproc )

    integer, dimension(lmpi_nproc+1), intent(out) :: sendproc,  recvproc

    integer :: lvl

    continue

    lvl = defaultghostlevel

    if (sr_allocated) then
      sendproc  = sr(lvl,gl)%sendproc
      recvproc  = sr(lvl,gl)%recvproc
    else
      sendproc  = -999
      recvproc  = -999
    end if

  end subroutine lmpi_getproc_xfer

!================================== LMPI_GETINDEX_XFER =======================80
!
! Get send/recv index.
!
!=============================================================================80

  subroutine lmpi_getindex_xfer(senddim, recvdim, sendindex, recvindex )

    integer, intent(in) :: senddim, recvdim
    integer, dimension(senddim), intent(out) :: sendindex
    integer, dimension(recvdim), intent(out) :: recvindex

    integer :: lvl

    continue

    lvl = defaultghostlevel

    if (sr_allocated) then
      sendindex(1:senddim) = sr(lvl,gl)%sendindex(1:senddim)
      recvindex(1:recvdim) = sr(lvl,gl)%recvindex(1:recvdim)
    else
      sendindex(1:senddim)  = -999
      recvindex(1:recvdim)  = -999
    end if

  end subroutine lmpi_getindex_xfer


!================================== LMPI_TEST_XFER ===========================80
!
! test the lmpi_xfer routines
!
!=============================================================================80

  subroutine lmpi_test_xfer( lastlocalnode, lastghostnode, l2g )

#ifdef HAVE_MPI
    use lmpi,        only : lmpi_conditional_stop
#endif

    integer,               intent(in) :: lastlocalnode, lastghostnode
    integer, dimension(:), intent(in) :: l2g

#ifdef HAVE_MPI
    integer :: node, istop
    integer, dimension(lastghostnode) :: globalnodeindex
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! for mpi with one proc

    globalnodeindex = -1
    do node = 1, lastlocalnode
      globalnodeindex(node) = l2g(node)
    end do
    call lmpi_xfer(globalnodeindex)
    istop = 0
    do node = 1, lastlocalnode
      if (globalnodeindex(node) /= l2g(node)) then
        write(*,'(a,i4,a,i10,a,2i10)') ' proc ', lmpi_id, ' local node ',node,&
          ' overwritten ', globalnodeindex(node), l2g(node)
        istop = 1
      end if
    end do
    do node = lastlocalnode+1, lastghostnode
      if (globalnodeindex(node) /= l2g(node)) then
        write(*,'(a,i4,a,i10,a,2i10)') ' proc ', lmpi_id, ' ghost node ',node,&
          ' incorrect ', globalnodeindex(node), l2g(node)
        istop = 1
      end if
    end do
    call lmpi_conditional_stop(istop)
#else
! avoid compiler warnings
    if (.false.) write(*,*) lastlocalnode,lastghostnode, l2g
#endif

  end subroutine lmpi_test_xfer

!============================ LMPI_RESET_SENDRECV ============================80
!
! reset the values of the sr send recieve pairs used by lmpi_xfer
!
!=============================================================================80

  subroutine lmpi_reset_sendrecv( lvl, sendproc, sendindex, recvproc, recvindex)

#ifdef HAVE_MPI
    use lmpi,        only : lmpi_conditional_stop
#endif

    integer,               intent(in) :: lvl
    integer, dimension(:), intent(in) :: sendproc, sendindex
    integer, dimension(:), intent(in) :: recvproc, recvindex

#ifdef HAVE_MPI
    integer :: index
    integer :: istop
    integer :: senddim, recvdim
#endif

    continue

#ifdef HAVE_MPI

    if (lmpi_nproc == 1) return ! for mpi with one proc

    call lmpi_deallocate_sendrecv(lvl)

    istop = 0
    if ( size(sendproc,1) /= lmpi_nproc+1 .or. &
         size(recvproc,1) /= lmpi_nproc+1 ) then
      istop = 1
      write(*,*) lmpi_id, "lmpi_reset_sendrecv: sendproc, recvproc size",    &
        lmpi_nproc+1, size(sendproc,1), size(recvproc,1)
    end if
    call lmpi_conditional_stop(istop)

    senddim = sendproc(lmpi_nproc+1) - 1
    recvdim = recvproc(lmpi_nproc+1) - 1

    istop = 0
    if (size(sendindex,1) /= senddim .or. size(recvindex,1) /= recvdim ) then
      istop = 1
      write(*,*) lmpi_id, "lmpi_reset_sendrecv: sendindex, recvindex size",  &
        size(sendindex,1), senddim, size(recvindex,1), recvdim
    end if
    call lmpi_conditional_stop(istop)

    sr(lvl,gl)%sendrecv_allocated = .true.
    allocate(sr(lvl,gl)%sendproc(lmpi_nproc+1))
    allocate(sr(lvl,gl)%recvproc(lmpi_nproc+1))
    do index = 1, lmpi_nproc + 1
      sr(lvl,gl)%sendproc(index) = sendproc(index)
      sr(lvl,gl)%recvproc(index) = recvproc(index)
    end do

    sr(lvl,gl)%size_sendindex = senddim
    allocate(sr(lvl,gl)%sendindex(max(1,senddim)))
    do index = 1, senddim
      sr(lvl,gl)%sendindex(index) = sendindex(index)
    end do

    sr(lvl,gl)%size_recvindex = recvdim
    allocate(sr(lvl,gl)%recvindex(max(1,recvdim)))
    do index = 1, recvdim
      sr(lvl,gl)%recvindex(index) = recvindex(index)
    end do

#else
! avoid compiler warnings
    if (.false.) write(*,*) lvl, sendindex, sendproc, recvindex, recvproc
#endif

  end subroutine lmpi_reset_sendrecv


!====================== DOUBLE_COLLECT_RES ===================================80
!
! Collects local residual contribution to global residual from each processor
! (public)
!
! Note, the mpi_maxloc2 within mpi_reduce takes two values (denoted here as
! val1 and val2).  mpi_maxloc2 finds the largest val1 amoung all processors,
! then returns the smallest val2.  If two or more processors match max(val1),
! then val2 is the smallest.  The intent of this routine is to keep all
! xloc,yloc,zloc tuples together as an entity.  The previous version of this
! routine would allow elements of the tuple to be gathered from different
! processors.  I.e., if rmax matched two or more PEs, then the smallest
! xloc,yloc,zloc amoung those PEs would be returned; where each loc value
! would be determined independently.
!
! The current approach finds the max(val1) and the associated min(pid).
! Then the loc values are obtained from the single source (min(pid)).
!
!=============================================================================80

  subroutine double_collect_res(rms, cputime, rmax, xloc, yloc, zloc)!tempProtectPrivate

    real(dp),                    intent(inout) :: rms, cputime
    real(dp),                    intent(inout) :: rmax,xloc,yloc,zloc

    real(dp), dimension (2)                    :: r,s
    real(dp), dimension (2,1)                  :: in, out

    integer                             :: id_of_max_rmax
#ifdef HAVE_MPI
    integer                              :: ierr
    real(dp), dimension (3)              :: out_loc
#endif

    continue

    r(01) = rms
    r(02) = cputime

    call lmpi_reduce(r,s)

    call lmpi_bcast(s)

    rms     = s(01)
    cputime = s(02)

!   find max global residual location

    in(1,1) = rmax
    in(2,1) = real(lmpi_id,dp)

    call lmpi_reduce_maxloc2( in, out )
    if (lmpi_master) then
       rmax = out(1,1)
       id_of_max_rmax = int(out(2,1))
    end if

#ifdef HAVE_MPI
    call lmpi_bcast(id_of_max_rmax)

    if (id_of_max_rmax /= 0) then
       if (lmpi_id == 0) then
          !write(*,*)lmpi_id,': recv from ',id_of_max_rmax
          call lmpi_recv(out_loc,3,id_of_max_rmax,1,ierr)
          xloc = out_loc(1)
          yloc = out_loc(2)
          zloc = out_loc(3)
       else if (lmpi_id == id_of_max_rmax) then
          !write(*,*)lmpi_id,': send to  ',0
          out_loc(1) = xloc
          out_loc(2) = yloc
          out_loc(3) = zloc
          call lmpi_send(out_loc,3,0,1,ierr)
       end if
    end if
#else
! avoid compiler warnings
    if (.false.) write(*,*) rmax, xloc, yloc, zloc, id_of_max_rmax
#endif

  end subroutine double_collect_res


!====================== DOUBLE_COMPLEX_COLLECT_RES ===========================80
!
! Collects local residual contribution to global residual from each processor
! (public)
!
! This complex version only reduces the real parts!!!
!
!=============================================================================80

  subroutine double_complex_collect_res(rms, cputime, rmax, xloc, yloc, zloc)!tempProtectPrivate

    complex(dp),                    intent(inout) :: rms, cputime
    complex(dp),                    intent(inout) :: rmax,xloc,yloc,zloc

    real(dp), dimension (2)                    :: r,s
    real(dp), dimension (2,1)                  :: in, out

    integer :: id_of_max_rmax

#ifdef HAVE_MPI
    integer, dimension(lmpi_status_size) :: status
    integer                              :: ierr
#endif

    continue

    r(01) = real(rms,dp)
    r(02) = real(cputime,dp)

    call lmpi_reduce(r,s)

    call lmpi_bcast(s)

    rms     = cmplx(s(01), aimag(rms), dp)
    cputime = cmplx(s(02), aimag(cputime), dp)

!   find max global residual location

    in(1,1) = real(rmax,    dp)
    in(2,1) = real(lmpi_id, dp)

    call lmpi_reduce_maxloc2( in, out )
    if (lmpi_master) then
       rmax = cmplx(out(1,1), aimag(rmax), dp)
       id_of_max_rmax = int(out(2,1))
    end if

#ifdef HAVE_MPI
    call lmpi_bcast(id_of_max_rmax)

    if (id_of_max_rmax /= 0) then
       if (lmpi_id == 0) then
         !write(*,*)lmpi_id,': recv from ',id_of_max_rmax
         !write(*,*)'recv(2)'
         call mpi_recv(xloc,1,mpi_double_complex,id_of_max_rmax, &
                      1,lmpi_comm,status,ierr)
         call mpi_recv(yloc,1,mpi_double_complex,id_of_max_rmax, &
                      1,lmpi_comm,status,ierr)
         call mpi_recv(zloc,1,mpi_double_complex,id_of_max_rmax, &
                      1,lmpi_comm,status,ierr)
       else if (lmpi_id == id_of_max_rmax) then
         !write(*,*)lmpi_id,': send to  ',0
         !write(*,*)'send(2)'
         call mpi_send(xloc,1,mpi_double_complex,0,1,lmpi_comm,ierr)
         call mpi_send(yloc,1,mpi_double_complex,0,1,lmpi_comm,ierr)
         call mpi_send(zloc,1,mpi_double_complex,0,1,lmpi_comm,ierr)
       end if
    end if
#else
! avoid compiler warnings
    if (.false.) write(*,*) rmax, xloc, yloc, zloc, id_of_max_rmax
#endif

  end subroutine double_complex_collect_res


!============================ LMPI_START_CUDAMPI_XFER_R4 =====================80
!
! Initiate non-blocking send/receive pair for rank 2 single data using
! CUDA-enabled MPI
!
!=============================================================================80
  subroutine lmpi_start_cudampi_xfer_r4(color,data,sr_loc)!tempProtectPrivate

    integer, intent(in) :: color

    real(system_r4), dimension(:,:), intent(in) :: data

    type(sendrecv_type), intent(in) :: sr_loc

#ifdef HAVE_MPI
    integer :: ierr, mpitag, length, nleaddim, inde, inode, l, i_other_proc
    integer :: sendproc_offset, sendind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)


! Post the receives

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle recv_loop

      length = sr_loc%recvproc(i_other_proc+2) - sr_loc%recvproc(i_other_proc+1)

      if ( length > 0 ) then

        nrecv  = nrecv + 1
        mpitag = 2

        write(*,*) lmpi_id, 'recving length = ', length
        write(*,*) lmpi_id, 'sr_loc%recvproc(i_other_proc+1) = ',              &
                   sr_loc%recvproc(i_other_proc+1)

!$acc data present(openacc_recvbuf)
!$acc host_data use_device(openacc_recvbuf)
        call mpi_irecv(openacc_recvbuf(1,sr_loc%recvproc(i_other_proc+1)),     &
                       length*nleaddim,mpi_real,i_other_proc,mpitag,lmpi_comm, &
                       recvreq(nrecv),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_irecv (lmpi_start_cudampi_xfer) failed... '
        endif

      endif

    enddo recv_loop





! Fill the send buffer on the device

    sendproc_offset = openacc_sendproc_offset(color)
    sendind_offset  = openacc_sendindex_offset(color)

    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      write(500+lmpi_id,*) 'packing up ', inode
    end do

!$acc parallel present(openacc_sendproc,openacc_sendindex,data,                &
!$acc                  openacc_sendbuf)
!$acc loop
    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      do l = 1, nleaddim
        openacc_sendbuf(l,inde) = data(l,inode)
      enddo
    end do
!$acc end parallel

!$acc update host(openacc_sendbuf)

    do l = 1, size(openacc_sendbuf,2)
      write(700+lmpi_id,'(5(e15.7,1x))') openacc_sendbuf(:,l)
    end do


! Post the sends

    nsend = 0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle send_loop

      length = sr_loc%sendproc(i_other_proc+2) - sr_loc%sendproc(i_other_proc+1)

      if ( length > 0 ) then

        nsend  = nsend + 1
        mpitag = 2

        write(*,*) lmpi_id, 'sending length = ', length

!$acc data present(openacc_sendbuf)
!$acc host_data use_device(openacc_sendbuf)
        call mpi_isend(openacc_sendbuf(1,sr_loc%sendproc(i_other_proc+1)),     &
                       length*nleaddim,mpi_real,i_other_proc,mpitag,lmpi_comm, &
                       sendreq(nsend),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_isend (lmpi_start_cudampi_xfer) failed...'
        endif

      endif

    enddo send_loop

#else
    if (.false.) write(*,*) color, data, sr_loc%size_sendindex
#endif

  end subroutine lmpi_start_cudampi_xfer_r4


!============================ LMPI_START_CUDAMPI_XFER_R8 =====================80
!
! Initiate non-blocking send/receive pair for rank 2 double data using
! CUDA-enabled MPI
!
!=============================================================================80
  subroutine lmpi_start_cudampi_xfer_r8(color,data,sr_loc)!tempProtectPrivate

    integer, intent(in) :: color

    real(system_r8), dimension(:,:), intent(in) :: data

    type(sendrecv_type), intent(in) :: sr_loc

#ifdef HAVE_MPI
    integer :: ierr, mpitag, length, nleaddim, inde, inode, l, i_other_proc
    integer :: sendproc_offset, sendind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)


! Post the receives

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle recv_loop

      length = sr_loc%recvproc(i_other_proc+2) - sr_loc%recvproc(i_other_proc+1)

      if ( length > 0 ) then

        nrecv  = nrecv + 1
        mpitag = 2

        write(*,*) lmpi_id, 'recving length = ', length
        write(*,*) lmpi_id, 'sr_loc%recvproc(i_other_proc+1) = ',              &
                   sr_loc%recvproc(i_other_proc+1)

!$acc data present(openacc_recvbuf)
!$acc host_data use_device(openacc_recvbuf)
        call mpi_irecv(openacc_recvbuf(1,sr_loc%recvproc(i_other_proc+1)),     &
                       length*nleaddim,mpi_double_precision,i_other_proc,      &
                       mpitag,lmpi_comm,recvreq(nrecv),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_irecv (lmpi_start_cudampi_xfer) failed... '
        endif

      endif

    enddo recv_loop





! Fill the send buffer on the device

    sendproc_offset = openacc_sendproc_offset(color)
    sendind_offset  = openacc_sendindex_offset(color)

    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      write(500+lmpi_id,*) 'packing up ', inode
    end do

!$acc parallel present(openacc_sendproc,openacc_sendindex,data,                &
!$acc                  openacc_sendbuf)
!$acc loop
    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      do l = 1, nleaddim
        openacc_sendbuf(l,inde) = data(l,inode)
      enddo
    end do
!$acc end parallel

!$acc update host(openacc_sendbuf)

    do l = 1, size(openacc_sendbuf,2)
      write(700+lmpi_id,'(5(e15.7,1x))') openacc_sendbuf(:,l)
    end do


! Post the sends

    nsend = 0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle send_loop

      length = sr_loc%sendproc(i_other_proc+2) - sr_loc%sendproc(i_other_proc+1)

      if ( length > 0 ) then

        nsend  = nsend + 1
        mpitag = 2

        write(*,*) lmpi_id, 'sending length = ', length

!$acc data present(openacc_sendbuf)
!$acc host_data use_device(openacc_sendbuf)
        call mpi_isend(openacc_sendbuf(1,sr_loc%sendproc(i_other_proc+1)),     &
                       length*nleaddim,mpi_double_precision,i_other_proc,      &
                       mpitag,lmpi_comm,sendreq(nsend),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_isend (lmpi_start_cudampi_xfer) failed...'
        endif

      endif

    enddo send_loop

#else
    if (.false.) write(*,*) color, data, sr_loc%size_sendindex
#endif

  end subroutine lmpi_start_cudampi_xfer_r8


!============================ LMPI_START_CUDAMPI_XFER_C4 =====================80
!
! Initiate non-blocking send/receive pair for rank 2 single data using
! CUDA-enabled MPI - complex
!
!=============================================================================80
  subroutine lmpi_start_cudampi_xfer_c4(color,data,sr_loc)!tempProtectPrivate

    integer, intent(in) :: color

    complex(system_r4), dimension(:,:), intent(in) :: data

    type(sendrecv_type), intent(in) :: sr_loc

#ifdef HAVE_MPI
    integer :: ierr, mpitag, length, nleaddim, inde, inode, l, i_other_proc
    integer :: sendproc_offset, sendind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)


! Post the receives

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle recv_loop

      length = sr_loc%recvproc(i_other_proc+2) - sr_loc%recvproc(i_other_proc+1)

      if ( length > 0 ) then

        nrecv  = nrecv + 1
        mpitag = 2

        write(*,*) lmpi_id, 'recving length = ', length
        write(*,*) lmpi_id, 'sr_loc%recvproc(i_other_proc+1) = ',              &
                   sr_loc%recvproc(i_other_proc+1)

!$acc data present(openacc_crecvbuf)
!$acc host_data use_device(openacc_crecvbuf)
        call mpi_irecv(openacc_crecvbuf(1,sr_loc%recvproc(i_other_proc+1)),    &
                     length*nleaddim,mpi_complex,i_other_proc,mpitag,lmpi_comm,&
                     recvreq(nrecv),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_irecv (lmpi_start_cudampi_xfer) failed... '
        endif

      endif

    enddo recv_loop





! Fill the send buffer on the device

    sendproc_offset = openacc_sendproc_offset(color)
    sendind_offset  = openacc_sendindex_offset(color)

    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      write(500+lmpi_id,*) 'packing up ', inode
    end do

!$acc parallel present(openacc_sendproc,openacc_sendindex,data,                &
!$acc                  openacc_csendbuf)
!$acc loop
    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      do l = 1, nleaddim
        openacc_csendbuf(l,inde) = data(l,inode)
      enddo
    end do
!$acc end parallel

!$acc update host(openacc_csendbuf)

    do l = 1, size(openacc_csendbuf,2)
      write(700+lmpi_id,'(5(e15.7,1x))') openacc_csendbuf(:,l)
    end do


! Post the sends

    nsend = 0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle send_loop

      length = sr_loc%sendproc(i_other_proc+2) - sr_loc%sendproc(i_other_proc+1)

      if ( length > 0 ) then

        nsend  = nsend + 1
        mpitag = 2

        write(*,*) lmpi_id, 'sending length = ', length

!$acc data present(openacc_csendbuf)
!$acc host_data use_device(openacc_csendbuf)
        call mpi_isend(openacc_csendbuf(1,sr_loc%sendproc(i_other_proc+1)),    &
                     length*nleaddim,mpi_complex,i_other_proc,mpitag,lmpi_comm,&
                     sendreq(nsend),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_isend (lmpi_start_cudampi_xfer) failed...'
        endif

      endif

    enddo send_loop

#else
    if (.false.) write(*,*) color, data, sr_loc%size_sendindex
#endif

  end subroutine lmpi_start_cudampi_xfer_c4


!============================ LMPI_START_CUDAMPI_XFER_C8 =====================80
!
! Initiate non-blocking send/receive pair for rank 2 double data using
! CUDA-enabled MPI - complex
!
!=============================================================================80
  subroutine lmpi_start_cudampi_xfer_c8(color,data,sr_loc)!tempProtectPrivate

    integer, intent(in) :: color

    complex(system_r8), dimension(:,:), intent(in) :: data

    type(sendrecv_type), intent(in) :: sr_loc

#ifdef HAVE_MPI
    integer :: ierr, mpitag, length, nleaddim, inde, inode, l, i_other_proc
    integer :: sendproc_offset, sendind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)


! Post the receives

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle recv_loop

      length = sr_loc%recvproc(i_other_proc+2) - sr_loc%recvproc(i_other_proc+1)

      if ( length > 0 ) then

        nrecv  = nrecv + 1
        mpitag = 2

        write(*,*) lmpi_id, 'recving length = ', length
        write(*,*) lmpi_id, 'sr_loc%recvproc(i_other_proc+1) = ',              &
                   sr_loc%recvproc(i_other_proc+1)

!$acc data present(openacc_crecvbuf)
!$acc host_data use_device(openacc_crecvbuf)
        call mpi_irecv(openacc_crecvbuf(1,sr_loc%recvproc(i_other_proc+1)),    &
                       length*nleaddim,mpi_double_complex,i_other_proc,        &
                       mpitag,lmpi_comm,recvreq(nrecv),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_irecv (lmpi_start_cudampi_xfer) failed... '
        endif

      endif

    enddo recv_loop





! Fill the send buffer on the device

    sendproc_offset = openacc_sendproc_offset(color)
    sendind_offset  = openacc_sendindex_offset(color)

    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      write(500+lmpi_id,*) 'packing up ', inode
    end do

!$acc parallel present(openacc_sendproc,openacc_sendindex,data,                &
!$acc                  openacc_csendbuf)
!$acc loop
    do inde = 1, openacc_sendproc(lmpi_nproc+1+sendproc_offset)-1
      inode = openacc_sendindex(inde+sendind_offset)
      do l = 1, nleaddim
        openacc_csendbuf(l,inde) = data(l,inode)
      enddo
    end do
!$acc end parallel

!$acc update host(openacc_csendbuf)

    do l = 1, size(openacc_csendbuf,2)
      write(700+lmpi_id,'(5(e15.7,1x))') openacc_csendbuf(:,l)
    end do


! Post the sends

    nsend = 0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if ( i_other_proc == lmpi_id ) cycle send_loop

      length = sr_loc%sendproc(i_other_proc+2) - sr_loc%sendproc(i_other_proc+1)

      if ( length > 0 ) then

        nsend  = nsend + 1
        mpitag = 2

        write(*,*) lmpi_id, 'sending length = ', length

!$acc data present(openacc_csendbuf)
!$acc host_data use_device(openacc_csendbuf)
        call mpi_isend(openacc_csendbuf(1,sr_loc%sendproc(i_other_proc+1)),    &
                       length*nleaddim,mpi_double_complex,i_other_proc,        &
                       mpitag,lmpi_comm,sendreq(nsend),ierr)
!$acc end host_data
!$acc end data

        if ( ierr /= lmpi_success ) then
          print *, 'mpi_isend (lmpi_start_cudampi_xfer) failed...'
        endif

      endif

    enddo send_loop

#else
    if (.false.) write(*,*) color, data, sr_loc%size_sendindex
#endif

  end subroutine lmpi_start_cudampi_xfer_c8


!========================= LMPI_COMPLETE_CUDAMPI_XFER_R4 =====================80
!
! Finalize non-blocking send/receive pair for rank 2 single data using
! CUDA-enabled MPI
!
!=============================================================================80
  subroutine lmpi_complete_cudampi_xfer_r4(color,data)!tempProtectPrivate

    integer, intent(in) :: color

    real(system_r4), dimension(:,:), intent(inout) :: data

#ifdef HAVE_MPI
    integer :: ierr, inde, inode, l, nleaddim, recvproc_offset, recvind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)

! Wait for all mpi communication to complete

    ierr = lmpi_success
    if ( nsend > 0 ) call lmpi_waitall(nsend, sendreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'send mpi_wait in complete_cudampi_xfer failed...'
    endif

    ierr = lmpi_success
    if ( nrecv > 0 ) call lmpi_waitall(nrecv, recvreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'recv mpi_wait in complete_cudampi_xfer failed...'
    endif

!$acc update host(openacc_recvbuf)

    do l = 1, size(openacc_recvbuf,2)
      write(800+lmpi_id,'(5(e15.7,1x))') openacc_recvbuf(:,l)
    end do

! Empty the recv buffer on the device

    recvproc_offset = openacc_recvproc_offset(color)
    recvind_offset  = openacc_recvindex_offset(color)

    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      write(600+lmpi_id,*) 'storing off ', inode
    enddo

!$acc parallel present(openacc_recvproc,openacc_recvindex,data,                &
!$acc                  openacc_recvbuf)
!$acc loop
    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      do l = 1, nleaddim
        data(l,inode) = openacc_recvbuf(l,inde)
      enddo
    enddo
!$acc end parallel

#else
    if (.false.) write(*,*) color, data
#endif

  end subroutine lmpi_complete_cudampi_xfer_r4


!========================= LMPI_COMPLETE_CUDAMPI_XFER_R8 =====================80
!
! Finalize non-blocking send/receive pair for rank 2 double data using
! CUDA-enabled MPI
!
!=============================================================================80
  subroutine lmpi_complete_cudampi_xfer_r8(color,data)!tempProtectPrivate

    integer, intent(in) :: color

    real(system_r8), dimension(:,:), intent(inout) :: data

#ifdef HAVE_MPI
    integer :: ierr, inde, inode, l, nleaddim, recvproc_offset, recvind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)

! Wait for all mpi communication to complete

    ierr = lmpi_success
    if ( nsend > 0 ) call lmpi_waitall(nsend, sendreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'send mpi_wait in complete_cudampi_xfer failed...'
    endif

    ierr = lmpi_success
    if ( nrecv > 0 ) call lmpi_waitall(nrecv, recvreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'recv mpi_wait in complete_cudampi_xfer failed...'
    endif

!$acc update host(openacc_recvbuf)

    do l = 1, size(openacc_recvbuf,2)
      write(800+lmpi_id,'(5(e15.7,1x))') openacc_recvbuf(:,l)
    end do

! Empty the recv buffer on the device

    recvproc_offset = openacc_recvproc_offset(color)
    recvind_offset  = openacc_recvindex_offset(color)

    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      write(600+lmpi_id,*) 'storing off ', inode
    enddo

!$acc parallel present(openacc_recvproc,openacc_recvindex,data,                &
!$acc                  openacc_recvbuf)
!$acc loop
    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      do l = 1, nleaddim
        data(l,inode) = openacc_recvbuf(l,inde)
      enddo
    enddo
!$acc end parallel

#else
    if (.false.) write(*,*) color, data
#endif

  end subroutine lmpi_complete_cudampi_xfer_r8


!========================= LMPI_COMPLETE_CUDAMPI_XFER_C4 =====================80
!
! Finalize non-blocking send/receive pair for rank 2 single data using
! CUDA-enabled MPI - complex
!
!=============================================================================80
  subroutine lmpi_complete_cudampi_xfer_c4(color,data)!tempProtectPrivate

    integer, intent(in) :: color

    complex(system_r4), dimension(:,:), intent(inout) :: data

#ifdef HAVE_MPI
    integer :: ierr, inde, inode, l, nleaddim, recvproc_offset, recvind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)

! Wait for all mpi communication to complete

    ierr = lmpi_success
    if ( nsend > 0 ) call lmpi_waitall(nsend, sendreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'send mpi_wait in complete_cudampi_xfer failed...'
    endif

    ierr = lmpi_success
    if ( nrecv > 0 ) call lmpi_waitall(nrecv, recvreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'recv mpi_wait in complete_cudampi_xfer failed...'
    endif

!$acc update host(openacc_crecvbuf)

    do l = 1, size(openacc_crecvbuf,2)
      write(800+lmpi_id,'(5(e15.7,1x))') openacc_crecvbuf(:,l)
    end do

! Empty the recv buffer on the device

    recvproc_offset = openacc_recvproc_offset(color)
    recvind_offset  = openacc_recvindex_offset(color)

    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      write(600+lmpi_id,*) 'storing off ', inode
    enddo

!$acc parallel present(openacc_recvproc,openacc_recvindex,data,                &
!$acc                  openacc_crecvbuf)
!$acc loop
    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      do l = 1, nleaddim
        data(l,inode) = openacc_crecvbuf(l,inde)
      enddo
    enddo
!$acc end parallel

#else
    if (.false.) write(*,*) color, data
#endif

  end subroutine lmpi_complete_cudampi_xfer_c4


!========================= LMPI_COMPLETE_CUDAMPI_XFER_C8 =====================80
!
! Finalize non-blocking send/receive pair for rank 2 double data using
! CUDA-enabled MPI - complex
!
!=============================================================================80
  subroutine lmpi_complete_cudampi_xfer_c8(color,data)!tempProtectPrivate

    integer, intent(in) :: color

    complex(system_r8), dimension(:,:), intent(inout) :: data

#ifdef HAVE_MPI
    integer :: ierr, inde, inode, l, nleaddim, recvproc_offset, recvind_offset
#endif

  continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return

    nleaddim = size(data,1)

! Wait for all mpi communication to complete

    ierr = lmpi_success
    if ( nsend > 0 ) call lmpi_waitall(nsend, sendreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'send mpi_wait in complete_cudampi_xfer failed...'
    endif

    ierr = lmpi_success
    if ( nrecv > 0 ) call lmpi_waitall(nrecv, recvreq, ierr)

    if ( ierr /= lmpi_success ) then
      print *, 'recv mpi_wait in complete_cudampi_xfer failed...'
    endif

!$acc update host(openacc_crecvbuf)

    do l = 1, size(openacc_crecvbuf,2)
      write(800+lmpi_id,'(5(e15.7,1x))') openacc_crecvbuf(:,l)
    end do

! Empty the recv buffer on the device

    recvproc_offset = openacc_recvproc_offset(color)
    recvind_offset  = openacc_recvindex_offset(color)

    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      write(600+lmpi_id,*) 'storing off ', inode
    enddo

!$acc parallel present(openacc_recvproc,openacc_recvindex,data,                &
!$acc                  openacc_crecvbuf)
!$acc loop
    do inde = 1, openacc_recvproc(lmpi_nproc+1+recvproc_offset) - 1
      inode = openacc_recvindex(inde+recvind_offset)
      do l = 1, nleaddim
        data(l,inode) = openacc_crecvbuf(l,inde)
      enddo
    enddo
!$acc end parallel

#else
    if (.false.) write(*,*) color, data
#endif

  end subroutine lmpi_complete_cudampi_xfer_c8

!tempStartExpand(lmpi_collect_forces)
!============================= tempName_COLLECT_FORCES
!
! Collects local force contributions to global forces from each processor
! (public)
!
!=============================================================================80
  subroutine tempName_collect_forces(clp,cdp,clv,cdv,cmxp,cmyp,cmzp,cmxv,cmyv, &
                                 cmzv,cxp,cyp,czp,cxv,cyv,czv,cl,cd,cmx,       &
                                 cmy,cmz,cx,cy,cz,heat,powerx,powery,powerz,   &
                                 mass,press,pt,tt,temp,rho,area)

    tempType, intent(inout) :: clp,cdp,clv,cdv
    tempType, intent(inout) :: cmxp,cmyp,cmzp
    tempType, intent(inout) :: cmxv,cmyv,cmzv
    tempType, intent(inout) :: cxp,cyp,czp,cxv,cyv,czv
    tempType, intent(inout) :: cl,cd,cmx,cmy,cmz,cx,cy,cz,heat
    tempType, intent(inout) :: powerx,powery,powerz
    tempType, intent(inout), optional :: mass, press, pt, rho, area
    tempType, intent(inout), optional :: tt, temp

    tempType, dimension (35) :: r,s

    continue

    r = tempConstant

    r(01) = clp
    r(02) = cdp
    r(03) = clv
    r(04) = cdv

    r(05) = cmxp
    r(06) = cmyp
    r(07) = cmzp
    r(08) = cmxv
    r(09) = cmyv
    r(10) = cmzv

    r(11) = cxp
    r(12) = cyp
    r(13) = czp
    r(14) = cxv
    r(15) = cyv
    r(16) = czv

    r(17) = cl
    r(18) = cd
    r(19) = cmx
    r(20) = cmy
    r(21) = cmz
    r(22) = cx
    r(23) = cy
    r(24) = cz
    r(25) = heat
    r(26) = powerx
    r(27) = powery
    r(28) = powerz

    if ( present(mass)  ) r(29) = mass
    if ( present(press) ) r(30) = press
    if ( present(pt)    ) r(31) = pt
    if ( present(tt)    ) r(32) = tt
    if ( present(temp)  ) r(33) = temp
    if ( present(rho)   ) r(34) = rho
    if ( present(area)  ) r(35) = area

    call lmpi_reduce(r,s)

    call lmpi_bcast(s)

    clp  = s(01)
    cdp  = s(02)
    clv  = s(03)
    cdv  = s(04)

    cmxp = s(05)
    cmyp = s(06)
    cmzp = s(07)
    cmxv = s(08)
    cmyv = s(09)
    cmzv = s(10)

    cxp  = s(11)
    cyp  = s(12)
    czp  = s(13)
    cxv  = s(14)
    cyv  = s(15)
    czv  = s(16)

    cl   = s(17)
    cd   = s(18)
    cmx  = s(19)
    cmy  = s(20)
    cmz  = s(21)
    cx   = s(22)
    cy   = s(23)
    cz   = s(24)
    heat = s(25)
    powerx= s(26)
    powery= s(27)
    powerz= s(28)

    if ( present(mass)  ) mass  = s(29)
    if ( present(press) ) press = s(30)
    if ( present(pt)    ) pt    = s(31)
    if ( present(tt)    ) tt    = s(32)
    if ( present(temp)  ) temp  = s(33)
    if ( present(rho)   ) rho   = s(34)
    if ( present(area)  ) area  = s(35)

  end subroutine tempName_collect_forces
!tempEndExpand

!tempStartExpand(lmpi_collect_mflux)
!============================= tempName_COLLECT_MFLUX
!
! Collects local force contributions to global flux from each processor
! (public)
!
!=============================================================================80
  subroutine tempName_collect_mflux(mflux_integral, eflux_integral,            &
                                    weflux_integral )

    tempType, intent(inout) :: mflux_integral, &
                               eflux_integral, &
                               weflux_integral

    tempType, dimension (3) :: r,s

    continue

    r(1) =  mflux_integral
    r(2) =  eflux_integral
    r(3) = weflux_integral

    call lmpi_reduce(r,s)

    call lmpi_bcast(s)

    mflux_integral  = s(1)
    eflux_integral  = s(2)
    weflux_integral = s(3)

  end subroutine tempName_collect_mflux
!tempEndExpand


!tempStartExpand(lmpi_xfer)
!=============================== tempName_vector_xfer
!
! non-blocking send/receive pair for rank 1 tempName data on nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_vector_xfer(data, ghostlevel_arg, site, sr_opt)

    tempType,            dimension(:), intent(inout) :: data
    integer,             optional,     intent(in)    :: ghostlevel_arg
    character(*),        optional,     intent(in)    :: site
    type(sendrecv_type), optional,     intent(in)    :: sr_opt

#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer1_count = lmpi_xfer1_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time = mpi_wtime()
    end if
#endif
#endif

    call lmpi_start_xfer(data, ghostlevel_arg, site, sr_opt)
    call lmpi_complete_xfer(data, ghostlevel_arg, site, sr_opt)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer1_time = lmpi_xfer1_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_vector_xfer

!=============================== tempName_matrix_xfer
!
! Generic non-blocking send/receive pair for rank 2 tempName nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_matrix_xfer(data, ghostlevel_arg, site, sr_opt )

    tempType,         dimension(:,:), intent(inout) :: data
    integer,             optional,    intent(in)    :: ghostlevel_arg
    character(*),        optional,    intent(in)    :: site
    type(sendrecv_type), optional,    intent(in)    :: sr_opt

#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
#ifdef TIME_MPI
   lmpi_Itime           = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer1_count = lmpi_xfer1_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif
#endif

    call lmpi_start_xfer(data, ghostlevel_arg, site, sr_opt)
    call lmpi_complete_xfer(data, ghostlevel_arg, site, sr_opt)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer1_time = lmpi_xfer1_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_matrix_xfer
!tempEndExpand

!tempStartExpand(lmpi_put)
!=============================== tempName_vector_put
!
! non-blocking send/receive pair for rank 1 tempName data
! (private)
!
! used to put a local array into off-processor arrays.
!
!=============================================================================80

  subroutine tempName_vector_put(data1, data2, sr, site )

    tempType, dimension(:), intent(in)    :: data1
    tempType, dimension(:), intent(inout) :: data2
    type(sendrecv_type),    intent(inout) :: sr
    character(*),        optional,    intent(in)    :: site

#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer1_count = lmpi_xfer1_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time = mpi_wtime()
    end if
#endif
#endif

    call lmpi_sta_xfer(data1, sr, site)
    call lmpi_end_xfer(data2, sr, site)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer1_time = lmpi_xfer1_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_vector_put

!=============================== tempName_matrix_put
!
! Generic non-blocking send/receive pair for rank 2 tempName data
! (private)
!
! used to put a local array into off-processor arrays.
!
!=============================================================================80

  subroutine tempName_matrix_put(data1, data2, sr, site )

    tempType, dimension(:,:), intent(in)    :: data1
    tempType, dimension(:,:), intent(inout) :: data2
    type(sendrecv_type),      intent(inout) :: sr
    character(*),        optional,    intent(in)    :: site

#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
#ifdef TIME_MPI
   lmpi_Itime           = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer1_count = lmpi_xfer1_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif
#endif

    call lmpi_sta_xfer(data1, sr, site)
    call lmpi_end_xfer(data2, sr, site)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer1_time = lmpi_xfer1_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_matrix_put
!tempEndExpand

!tempStartExpand(lmpi_add)
!=============================== tempName_vector_add
!
! non-blocking send/receive pair for rank 1 tempName data
! (private)
!
! used to put a local array into off-processor arrays.
!
!=============================================================================80

  subroutine tempName_vector_add(data1, data2, sr, site)

    tempType, dimension(:), intent(in)    :: data1
    tempType, dimension(:), intent(inout) :: data2
    type(sendrecv_type),    intent(inout) :: sr
    character(*),        optional,     intent(in)    :: site


#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer1_count = lmpi_xfer1_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time = mpi_wtime()
    end if
#endif
#endif

    call lmpi_sta_xfer(data1, sr, site)
    call lmpi_enda_xfer(data2, sr, site)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer1_time = lmpi_xfer1_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_vector_add

!=============================== tempName_matrix_add
!
! Generic non-blocking send/receive pair for rank 2 tempName data
! (private)
!
! used to put a local array into off-processor arrays.
!
!=============================================================================80

  subroutine tempName_matrix_add(data1, data2, sr, site )

    tempType, dimension(:,:), intent(in)    :: data1
    tempType, dimension(:,:), intent(inout) :: data2
    type(sendrecv_type),      intent(inout) :: sr
    character(*),        optional,    intent(in)    :: site


#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
#ifdef TIME_MPI
   lmpi_Itime           = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer1_count = lmpi_xfer1_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif
#endif

    call lmpi_sta_xfer(data1, sr, site)
    call lmpi_enda_xfer(data2, sr, site)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer1_time = lmpi_xfer1_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_matrix_add
!tempEndExpand


!tempStartExpand(lmpi_start_xfer)
!=============================== tempName_start_vector_xfer
!
! non-blocking send/receive pair for rank 1 tempName data on nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_start_vector_xfer(data, ghostlevel_arg, site, sr_opt)

    tempType,      dimension(:),           intent(inout) :: data
    integer,                     optional, intent(in)    :: ghostlevel_arg
    character(*),                optional, intent(in)    :: site
    type(sendrecv_type), target, optional, intent(in)    :: sr_opt

#ifdef HAVE_MPI
    integer :: ierr, errc, errcm   ! communicator, error flags
    integer :: mpitag, length      ! unique message tag and length
    integer :: recvdim, senddim    ! extents of mpi work array
    integer :: inde, inode         ! mpi packing loop indexes
    integer :: i_other_proc        ! processor loop index
    integer :: astat               ! allocation status
    integer :: lvl                 ! mpi ghost node level
    integer :: size_allowed        ! allowable size of the array
#ifdef TIME_MPI
    logical :: lmpi_Itime          ! True if this is the outermost driver
#endif
#endif

    type(sendrecv_type), pointer :: sr_loc

    continue

#ifdef HAVE_MPI

    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer2_count = lmpi_xfer2_count + 1
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    size_allowed = size( data , 1 )
    errc         = 0

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

! Point a local pointer to the specific sr we want to use for the xfer

    if ( present(sr_opt) ) then
      sr_loc => sr_opt
    else
      sr_loc => sr(lvl,gl)
      if ( check_xfer01 ) call check_size01( size(data,1), site )
    endif

    senddim  = sr_loc%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr_loc%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr_loc%tempName_vec_allocated ) then

    else chk_alloc

      allocate(sr_loc%tempName_vec_recvdata(max(1,recvdim)), stat = astat)
      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_recvdata(nleaddim,recvdim)) failed"

      allocate(sr_loc%tempName_vec_senddata(max(1,senddim)), stat = astat)
      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_senddata(nleaddim,senddim)) failed"

      sr_loc%tempName_vec_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr_loc%recvproc(i_other_proc+2)                           &
               - sr_loc%recvproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 1 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

          call lmpi_irecv(sr_loc%tempName_vec_recvdata, length,            &
                 i_other_proc, mpitag, recvreq(nrecv), ierr,                   &
                 sr_loc%recvproc(i_other_proc+1))

        ! call mpi_irecv( sr_loc%tempName_vec_recvdata( &
        !      sr_loc%recvproc(i_other_proc+1) ),       &
        !      length, tempMPIType, i_other_proc, mpitag,   &
        !      lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv (tempSub) failed"
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node inforvection to send arrays

    if(check_xfer) then

      sindex_check : do inde = 1, sr_loc%sendproc(lmpi_nproc+1) - 1
        inode = sr_loc%sendindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 1 xfer...size_allowed=',size_allowed
          write(*,*) ' sindex : loop assignment operation...id,inode=',&
                     lmpi_id,inode
          write(*,*) ' (tempSub) failed lvl,gl=',lvl,gl
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit sindex_check
        endif
      enddo sindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    sindex : do inde = 1, sr_loc%sendproc(lmpi_nproc+1) - 1
      inode = sr_loc%sendindex(inde)
      sr_loc%tempName_vec_senddata(inde) = data(inode)
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr_loc%sendproc(i_other_proc+2)                           &
               - sr_loc%sendproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 1 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

          call lmpi_isend( sr_loc%tempName_vec_senddata,       &
               length, i_other_proc, mpitag,                       &
               sendreq(nsend), ierr,                               &
               sr_loc%sendproc(i_other_proc+1))

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend in (tempSub) failed... "
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

    ! call lmpi_complete_xfer to finish

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer2_time = lmpi_xfer2_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (present(sr_opt)) sr_loc => sr_opt
    if (.false.) write(*,*) ghostlevel_arg, data, site, sr_loc%size_sendindex
#endif

  end subroutine tempName_start_vector_xfer

!=============================== tempName_start_matrix_xfer
!
! Generic non-blocking send/receive pair for rank 2 tempName nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_start_matrix_xfer(data, ghostlevel_arg, site, sr_opt )

    tempType,    dimension(:,:),           intent(inout) :: data
    integer,                     optional, intent(in)    :: ghostlevel_arg
    character(len=*),            optional, intent(in)    :: site
    type(sendrecv_type), target, optional, intent(in)    :: sr_opt

#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: errc, errcm    ! communicator, error flag
    integer :: mpitag, length ! unique message tag and length
    integer :: nleaddim       ! extent of leading dimension
    integer :: nallocdim      ! extent of leading dimension
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode,l        ! mpi packing loop indexes
    integer :: i_other_proc   ! processor loop index
    integer :: astat          ! allocate status
    integer :: lvl            ! mpi ghost level to transfer
    integer :: size_allowed   ! allowable size of the array
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    type(sendrecv_type), pointer :: sr_loc

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer2_count = lmpi_xfer2_count + 1
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

    nleaddim = size(data,1)
    size_allowed = size( data , 2 )

! Point a local pointer to the specific sr we want to use for the xfer

    if ( present(sr_opt) ) then
      sr_loc => sr_opt
    else
      sr_loc => sr(lvl,gl)
      if ( check_xfer01 ) call check_size01( size(data,2), site )
    endif

    errc         = 0

    senddim  = sr_loc%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr_loc%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr_loc%tempName_mat_allocated ) then
      nallocdim = size(sr_loc%tempName_mat_recvdata,1)
      leader : if ( nallocdim < nleaddim) then

        deallocate(sr_loc%tempName_mat_recvdata)
        deallocate(sr_loc%tempName_mat_senddata)

        allocate(sr_loc%tempName_mat_recvdata(max(1,nleaddim),             &
                 max(1,recvdim)), stat = astat)

        if(astat /= 0) print *,                                                &
          "lmpi: re allocate(tempName_mat_recvdata(nleaddim,recvdim)) failed"

        allocate(sr_loc%tempName_mat_senddata(max(1,nleaddim),             &
                 max(1,senddim)), stat = astat)

        if(astat /= 0) print *,                                                &
          "lmpi: re tempName_mat_senddata(nleaddim,senddim)) failed"

        nallocdim = nleaddim

      endif leader
    else chk_alloc

      nallocdim = nleaddim

      allocate(sr_loc%tempName_mat_recvdata(max(1,nleaddim),               &
               max(1,recvdim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_mat_recvdata(nleaddim,recvdim)) failed"

      allocate(sr_loc%tempName_mat_senddata(max(1,nleaddim),               &
               max(1,senddim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_mat_senddata(nleaddim,senddim)) failed"

      sr_loc%tempName_mat_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr_loc%recvproc(i_other_proc+2)                           &
               - sr_loc%recvproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 2 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

          call lmpi_irecv(sr_loc%tempName_mat_recvdata,          &
               length*nallocdim,                                     &
               i_other_proc, mpitag, recvreq(nrecv), ierr,           &
               1, sr_loc%recvproc(i_other_proc+1))

        ! call mpi_irecv( sr_loc%tempName_mat_recvdata(        &
        !      1, sr_loc%recvproc(i_other_proc+1) ),           &
        !      length*nallocdim, tempMPIType,                      &
        !      i_other_proc, mpitag, lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv (tempSub) failed... "
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node information to send arrays

    if(check_xfer) then

      sindex_check : do inde = 1, sr_loc%sendproc(lmpi_nproc+1) - 1
        inode = sr_loc%sendindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 2 xfer...size_allowed=',size_allowed
          write(*,*) ' sindex : loop assignment operation...id,inode=', &
                     lmpi_id,inode
          write(*,*) ' lmpi_xfer (tempSub) failed...lvl,gl=',&
                      lvl,gl
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit sindex_check
        endif
      enddo sindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    sindex : do inde = 1, sr_loc%sendproc(lmpi_nproc+1) - 1
      inode = sr_loc%sendindex(inde)
      slead : do l = 1, nleaddim
        sr_loc%tempName_mat_senddata(l,inde) = data(l,inode)
      enddo slead
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr_loc%sendproc(i_other_proc+2)                           &
               - sr_loc%sendproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 2 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

          call lmpi_isend(sr_loc%tempName_mat_senddata, &
               length*nallocdim,                            &
               i_other_proc, mpitag, sendreq(nsend), ierr,  &
               1, sr_loc%sendproc(i_other_proc+1))

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend (tempSub) failed..."
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

    ! call lmpi_complete_xfer to finish

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer2_time = lmpi_xfer2_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (present(sr_opt)) sr_loc => sr_opt
    if (.false.) write(*,*) ghostlevel_arg, data, site, sr_loc%size_sendindex
#endif

  end subroutine tempName_start_matrix_xfer

!tempEndExpand


!tempStartExpand(lmpi_xfer_fem)
!=============================== tempName_xfer_fem
!
! non-blocking send/receive pair for rank 1 tempName data on nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_xfer_fem(recvlength, sendlength, sr_in,                  &
                               representative_data, nvars)

    integer, intent(in) :: nvars

    integer, dimension(:), intent(in) :: recvlength, sendlength

! to disambiguate interface:

    tempType, intent(in) :: representative_data

    type(sendrecv_type), intent(inout) :: sr_in

#ifdef HAVE_MPI
    integer :: ierr                ! error flag
    integer :: startval            ! error flag
    integer :: mpitag              ! unique message tag
    integer :: i_other_proc        ! processor loop index
#endif

    continue

    if ( .false. ) write(*,*) representative_data

#ifdef HAVE_MPI

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        rlen : if (recvlength(i_other_proc+1)>0) then

          nrecv  = nrecv + 1
          mpitag = 1 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

          startval = nvars*(sr_in%recvproc(i_other_proc+1)-1) + 1

          call lmpi_irecv(sr_in%tempName_vec_recvdata,           &
                          recvlength(i_other_proc+1),            &
                 i_other_proc, mpitag, recvreq(nrecv), ierr,     &
                 startval)
!                1)
!                3*sr_in%recvproc(i_other_proc+1))
!                sr_in%recvproc(i_other_proc+1)) ! original

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv (tempSub) failed"
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend = 0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        slen : if (sendlength(i_other_proc+1)>0) then

          nsend  = nsend + 1
          mpitag = 1 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

          startval = nvars*(sr_in%sendproc(i_other_proc+1)-1) + 1

          call lmpi_isend( sr_in%tempName_vec_senddata,            &
               sendlength(i_other_proc+1), i_other_proc, mpitag,   &
               sendreq(nsend), ierr,                               &
               startval)
!              1)
!              3*sr_in%sendproc(i_other_proc+1))
!              sr_in%sendproc(i_other_proc+1)) ! original

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend in (tempSub) failed... "
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

! wait for all mpi communication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait lmpi_xfer_fem failed"
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in (tempSub) failed..."
      return
    endif wait_recv_error

#else
! avoid compiler warnings
    if (.false.) write(*,*) recvlength(1), sendlength(1),     &
                            sr_in%size_sendindex, nvars
#endif

  end subroutine tempName_xfer_fem
!tempEndExpand


!tempStartExpand(lmpi_complete_xfer)
!=============================== tempName_complete_vector_xfer
!
! non-blocking send/receive pair for rank 1 tempName data on nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_complete_vector_xfer(data, ghostlevel_arg, site, sr_opt)

    tempType,      dimension(:),           intent(inout) :: data
    integer,                     optional, intent(in)    :: ghostlevel_arg
    character(*),                optional, intent(in)    :: site
    type(sendrecv_type), target, optional, intent(in)    :: sr_opt

#ifdef HAVE_MPI
    integer :: ierr, errc, errcm   ! communicator, error flags
    integer :: recvdim, senddim    ! extents of mpi work array
    integer :: inde, inode         ! mpi packing loop indexes
    integer :: lvl                 ! mpi ghost node level
    integer :: size_allowed        ! allowable size of the array
#endif

    type(sendrecv_type), pointer :: sr_loc

    continue

#ifdef HAVE_MPI

    if (lmpi_nproc == 1) return ! mpi with one proc

    size_allowed = size( data , 1 )
    errc         = 0

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

! Point a local pointer to the specific sr we want to use for the xfer

    if ( present(sr_opt) ) then
      sr_loc => sr_opt
    else
      sr_loc => sr(lvl,gl)
      if ( check_xfer01 ) call check_size01( size(data,1), site )
    endif

    senddim  = sr_loc%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr_loc%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait (tempSub) failed"
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in (tempSub) failed..."
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    if(check_xfer) then

      rindex_check : do inde = 1, sr_loc%recvproc(lmpi_nproc+1) - 1
        inode = sr_loc%recvindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 1 xfer...size_allowed=',size_allowed
          write(*,*) ' rindex : loop assignment operation...id,inode=',&
                     lmpi_id,inode
          write(*,*) ' (tempSub) failed lvl,gl=',lvl,gl
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit rindex_check
        endif
      enddo rindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    rindex : do inde = 1, sr_loc%recvproc(lmpi_nproc+1) - 1
      inode = sr_loc%recvindex(inde)
      data(inode) = sr_loc%tempName_vec_recvdata(inde)
    enddo rindex

#else
! avoid compiler warnings
    if (present(sr_opt)) sr_loc => sr_opt
    if (.false.) write(*,*) ghostlevel_arg, data, site, sr_loc%size_sendindex
#endif

  end subroutine tempName_complete_vector_xfer

!=============================== tempName_complete_matrix_xfer
!
! Generic non-blocking send/receive pair for rank 2 tempName nodes
! (private)
!
! used to share a local array with all the other nodes to fill partition
! boundary ghost nodes.
!
!=============================================================================80

  subroutine tempName_matrix_complete_xfer(data, ghostlevel_arg, site, sr_opt )

    tempType,    dimension(:,:),           intent(inout) :: data
    integer,                     optional, intent(in)    :: ghostlevel_arg
    character(*),                optional, intent(in)    :: site
    type(sendrecv_type), target, optional, intent(in)    :: sr_opt

#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: errc, errcm    ! communicator, error flag
    integer :: nleaddim       ! extent of leading dimension
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode,l        ! mpi packing loop indexes
    integer :: lvl            ! mpi ghost level to transfer
    integer :: size_allowed   ! allowable size of the array
#endif

    type(sendrecv_type), pointer :: sr_loc

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

    nleaddim = size(data,1)
    size_allowed = size( data , 2 )

! Point a local pointer to the specific sr we want to use for the xfer

    if ( present(sr_opt) ) then
      sr_loc => sr_opt
    else
      sr_loc => sr(lvl,gl)
      if ( check_xfer01 ) call check_size01( size(data,2), site )
    endif

    errc         = 0

    senddim  = sr_loc%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr_loc%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait in send tempSub failed..."
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in recv tempSub failed..."
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    if(check_xfer) then

      rindex_check : do inde = 1, sr_loc%recvproc(lmpi_nproc+1) - 1
        inode = sr_loc%recvindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 2 xfer...size_allowed=',size_allowed
          write(*,*) ' rindex : loop assignment operation...id,inode=', &
                     lmpi_id,inode
          write(*,*) ' lmpi_xfer(tempSub)failed...lvl,gl=',&
                     lvl,gl
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit rindex_check
        endif
      enddo rindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    rindex : do inde = 1, sr_loc%recvproc(lmpi_nproc+1) - 1
      inode = sr_loc%recvindex(inde)
      rlead : do l = 1, nleaddim
        data(l,inode) = sr_loc%tempName_mat_recvdata(l,inde)
      enddo rlead
    enddo rindex

#else
! avoid compiler warnings
    if (present(sr_opt)) sr_loc => sr_opt
    if (.false.) write(*,*) ghostlevel_arg, data, site, sr_loc%size_sendindex
#endif

  end subroutine tempName_matrix_complete_xfer
!tempEndExpand


!tempStartExpand(lmpi_sta_xfer)
!=============================== tempName_sta_vector_xfer
!
! non-blocking send/receive pair for rank 1 tempName data
! (private)
!
! used to share a local array.
!
!=============================================================================80

  subroutine tempName_sta_vector_xfer(data, sr, site)

    tempType,      dimension(:),  intent(in)     :: data
    type(sendrecv_type),          intent(inout) :: sr
    character(*),       optional, intent(in)    :: site

#ifdef HAVE_MPI
    integer :: ierr, errc, errcm   ! communicator, error flags
    integer :: mpitag, length      ! unique message tag and length
    integer :: recvdim, senddim    ! extents of mpi work array
    integer :: inde, inode         ! mpi packing loop indexes
    integer :: i_other_proc        ! processor loop index
    integer :: astat               ! allocation status
    integer :: size_allowed        ! allowable size of the array
#ifdef TIME_MPI
    logical :: lmpi_Itime          ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI

    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer2_count = lmpi_xfer2_count + 1
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    size_allowed = size( data , 1 )
    errc         = 0

    senddim  = sr%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr%tempName_vec_allocated ) then

    else chk_alloc

      allocate(sr%tempName_vec_recvdata(max(1,recvdim)), stat = astat)
      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_recvdata(nleaddim,recvdim)) failed"

      allocate(sr%tempName_vec_senddata(max(1,senddim)), stat = astat)
      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_senddata(nleaddim,senddim)) failed"

      sr%tempName_vec_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr%recvproc(i_other_proc+2)                           &
               - sr%recvproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 1 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

          call lmpi_irecv(sr%tempName_vec_recvdata, length,            &
                 i_other_proc, mpitag, recvreq(nrecv), ierr,           &
                 sr%recvproc(i_other_proc+1))

        ! call mpi_irecv( sr%tempName_vec_recvdata( &
        !      sr%recvproc(i_other_proc+1) ),       &
        !      length, tempMPIType, i_other_proc, mpitag,   &
        !      lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv (tempSub) failed"
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node inforvection to send arrays

    if(check_xfer) then

      sindex_check : do inde = 1, sr%sendproc(lmpi_nproc+1) - 1
        inode = sr%sendindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 1 xfer...size_allowed=',size_allowed
          write(*,*) ' sindex : loop assignment operation...id,inode=',&
                     lmpi_id,inode
          write(*,*) ' (tempSub) failed'
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit sindex_check
        endif
      enddo sindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    sindex : do inde = 1, sr%sendproc(lmpi_nproc+1) - 1
      inode = sr%sendindex(inde)
      sr%tempName_vec_senddata(inde) = data(inode)
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr%sendproc(i_other_proc+2)                           &
               - sr%sendproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 1 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

          call lmpi_isend( sr%tempName_vec_senddata,               &
               length, i_other_proc, mpitag,                       &
               sendreq(nsend), ierr,                               &
               sr%sendproc(i_other_proc+1))

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend in (tempSub) failed"
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

    ! call lmpi_complete_xfer to finish

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer2_time = lmpi_xfer2_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (.false.) write(*,*) data, site, sr%size_sendindex
#endif

  end subroutine tempName_sta_vector_xfer

!=============================== tempName_sta_matrix_xfer
!
! Generic non-blocking send/receive pair for rank 2 tempName
! (private)
!
! used to share a local array.
!
!=============================================================================80

  subroutine tempName_sta_matrix_xfer(data, sr, site )

    tempType,    dimension(:,:),  intent(in)    :: data
    type(sendrecv_type),          intent(inout) :: sr
    character(len=*),   optional, intent(in)    :: site


#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: errc, errcm    ! communicator, error flag
    integer :: mpitag, length ! unique message tag and length
    integer :: nleaddim       ! extent of leading dimension
    integer :: nallocdim      ! extent of leading dimension
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode,l        ! mpi packing loop indexes
    integer :: i_other_proc   ! processor loop index
    integer :: astat          ! allocate status
    integer :: size_allowed   ! allowable size of the array
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer2_count = lmpi_xfer2_count + 1
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    nleaddim = size(data,1)
    size_allowed = size( data , 2 )
    errc         = 0

    senddim  = sr%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr%tempName_mat_allocated ) then
      nallocdim = size(sr%tempName_mat_recvdata,1)
      leader : if ( nallocdim < nleaddim) then

        deallocate(sr%tempName_mat_recvdata)
        deallocate(sr%tempName_mat_senddata)

        allocate(sr%tempName_mat_recvdata(max(1,nleaddim),             &
                 max(1,recvdim)), stat = astat)

        if(astat /= 0) print *,                                                &
          "lmpi: re allocate(tempName_mat_recvdata(nleaddim,recvdim)) failed"

        allocate(sr%tempName_mat_senddata(max(1,nleaddim),             &
                 max(1,senddim)), stat = astat)

        if(astat /= 0) print *,                                                &
          "lmpi: re tempName_mat_senddata(nleaddim,senddim)) failed"

        nallocdim = nleaddim

      endif leader
    else chk_alloc

      nallocdim = nleaddim

      allocate(sr%tempName_mat_recvdata(max(1,nleaddim),               &
               max(1,recvdim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_mat_recvdata(nleaddim,recvdim)) failed"

      allocate(sr%tempName_mat_senddata(max(1,nleaddim),               &
               max(1,senddim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_mat_senddata(nleaddim,senddim)) failed"

      sr%tempName_mat_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr%recvproc(i_other_proc+2)                           &
               - sr%recvproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 2 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

          call lmpi_irecv(sr%tempName_mat_recvdata,                  &
               length*nallocdim,                                     &
               i_other_proc, mpitag, recvreq(nrecv), ierr,           &
               1, sr%recvproc(i_other_proc+1))

        ! call mpi_irecv( sr%tempName_mat_recvdata(        &
        !      1, sr%recvproc(i_other_proc+1) ),           &
        !      length*nallocdim, tempMPIType,                      &
        !      i_other_proc, mpitag, lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv (tempSub) failed"
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node information to send arrays

    if(check_xfer) then

      sindex_check : do inde = 1, sr%sendproc(lmpi_nproc+1) - 1
        inode = sr%sendindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 2 xfer...size_allowed=',size_allowed
          write(*,*) ' sindex : loop assignment operation...id,inode=', &
                     lmpi_id,inode
          write(*,*) ' lmpi_xfer (tempSub) failed...gl=', gl
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit sindex_check
        endif
      enddo sindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    sindex : do inde = 1, sr%sendproc(lmpi_nproc+1) - 1
      inode = sr%sendindex(inde)
      slead : do l = 1, nleaddim
        sr%tempName_mat_senddata(l,inde) = data(l,inode)
      enddo slead
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr%sendproc(i_other_proc+2)                           &
               - sr%sendproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 2 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

          call lmpi_isend(sr%tempName_mat_senddata,         &
               length*nallocdim,                            &
               i_other_proc, mpitag, sendreq(nsend), ierr,  &
               1, sr%sendproc(i_other_proc+1))

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend (tempSub) failed..."
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

    ! call lmpi_complete_xfer to finish

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer2_time = lmpi_xfer2_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (.false.) write(*,*) data, site, sr%size_sendindex
#endif

  end subroutine tempName_sta_matrix_xfer

!tempEndExpand

!tempStartExpand(lmpi_end_xfer)
!=============================== tempName_end_vector_xfer
!
! non-blocking send/receive pair for rank 1 tempName data
! (private)
!
! used to update data with data from other processors.
!
!=============================================================================80

  subroutine tempName_end_vector_xfer(data, sr, site)

    tempType,      dimension(:),           intent(inout) :: data
    type(sendrecv_type),                   intent(in)    :: sr
    character(*),                optional, intent(in)    :: site

#ifdef HAVE_MPI
    integer :: ierr, errc, errcm   ! communicator, error flags
    integer :: recvdim, senddim    ! extents of mpi work array
    integer :: inde, inode         ! mpi packing loop indexes
    integer :: size_allowed        ! allowable size of the array
#endif

    continue

#ifdef HAVE_MPI

    if (lmpi_nproc == 1) return ! mpi with one proc

    size_allowed = size( data , 1 )
    errc         = 0

    senddim  = sr%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait (tempSub) failed"
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in (tempSub) failed"
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    if(check_xfer) then

      rindex_check : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
        inode = sr%recvindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 1 xfer...size_allowed=',size_allowed
          write(*,*) ' rindex : loop assignment operation...id,inode=',&
                     lmpi_id,inode
          write(*,*) ' (tempSub) failed'
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit rindex_check
        endif
      enddo rindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    rindex : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
      inode = sr%recvindex(inde)
      data(inode) = sr%tempName_vec_recvdata(inde)
    enddo rindex

#else
! avoid compiler warnings
    if (.false.) write(*,*) data, site, sr%size_sendindex
#endif

  end subroutine tempName_end_vector_xfer

!=============================== tempName_end_matrix_xfer
!
! Generic non-blocking send/receive pair for rank 2 tempName
! (private)
!
! used to update data with data from other processors.
!
!=============================================================================80

  subroutine tempName_matrix_end_xfer(data, sr, site )

    tempType,    dimension(:,:),           intent(inout) :: data
    type(sendrecv_type),                   intent(in)    :: sr
    character(*),                optional, intent(in)    :: site


#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: errc, errcm    ! communicator, error flag
    integer :: nleaddim       ! extent of leading dimension
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode,l        ! mpi packing loop indexes
    integer :: size_allowed   ! allowable size of the array
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

    nleaddim = size(data,1)
    size_allowed = size( data , 2 )
    errc         = 0

    senddim  = sr%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait in send tempSub failed"
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in recv tempSub failed"
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    if(check_xfer) then

      rindex_check : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
        inode = sr%recvindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 2 xfer...size_allowed=',size_allowed
          write(*,*) ' rindex : loop assignment operation...id,inode=', &
                     lmpi_id,inode
          write(*,*) ' lmpi_xfer(tempSub)failed'
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit rindex_check
        endif
      enddo rindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    rindex : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
      inode = sr%recvindex(inde)
      rlead : do l = 1, nleaddim
        data(l,inode) = sr%tempName_mat_recvdata(l,inde)
      enddo rlead
    enddo rindex

#else
! avoid compiler warnings
    if (.false.) write(*,*) data, site, sr%size_sendindex
#endif

  end subroutine tempName_matrix_end_xfer
!tempEndExpand

!tempStartExpand(lmpi_enda_xfer)
!=============================== tempName_enda_vector_xfer
!
! non-blocking send/receive pair for rank 1 tempName data
! (private)
!
! used to update data with data from other processors.
!
!=============================================================================80

  subroutine tempName_enda_vector_xfer(data, sr, site)

    tempType,      dimension(:),           intent(inout) :: data
    type(sendrecv_type),                   intent(in)    :: sr
    character(*),                optional, intent(in)    :: site

#ifdef HAVE_MPI
    integer :: ierr, errc, errcm   ! communicator, error flags
    integer :: recvdim, senddim    ! extents of mpi work array
    integer :: inde, inode         ! mpi packing loop indexes
    integer :: size_allowed        ! allowable size of the array
#endif

    continue

#ifdef HAVE_MPI

    if (lmpi_nproc == 1) return ! mpi with one proc

    size_allowed = size( data , 1 )
    errc         = 0

    senddim  = sr%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait (tempSub) failed"
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in (tempSub) failed"
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    if(check_xfer) then

      rindex_check : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
        inode = sr%recvindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 1 xfer...size_allowed=',size_allowed
          write(*,*) ' rindex : loop assignment operation...id,inode=',&
                     lmpi_id,inode
          write(*,*) ' (tempSub) failed'
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit rindex_check
        endif
      enddo rindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    rindex : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
      inode = sr%recvindex(inde)
      data(inode) = data(inode) + sr%tempName_vec_recvdata(inde)
    enddo rindex

#else
! avoid compiler warnings
    if (.false.) write(*,*) data, site, sr%size_sendindex
#endif

  end subroutine tempName_enda_vector_xfer

!=============================== tempName_enda_matrix_xfer
!
! Generic non-blocking send/receive pair for rank 2 tempName
! (private)
!
! used to update data with data from other processors.
!
!=============================================================================80

  subroutine tempName_matrix_enda_xfer(data, sr, site )

    tempType,    dimension(:,:),           intent(inout) :: data
    type(sendrecv_type),                   intent(in)    :: sr
    character(*),                optional, intent(in)    :: site


#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: errc, errcm    ! communicator, error flag
    integer :: nleaddim       ! extent of leading dimension
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode,l        ! mpi packing loop indexes
    integer :: size_allowed   ! allowable size of the array
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

    nleaddim = size(data,1)
    size_allowed = size( data , 2 )
    errc         = 0

    senddim  = sr%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait in send tempSub failed"
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in recv tempSub failed"
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    if(check_xfer) then

      rindex_check : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
        inode = sr%recvindex(inde)
        if((inode <= 0) .or. (inode > size_allowed)) then
          write(*,*) ' problem in rank 2 xfer...size_allowed=',size_allowed
          write(*,*) ' rindex : loop assignment operation...id,inode=', &
                     lmpi_id,inode
          write(*,*) ' lmpi_xfer(tempSub)failed'
          if(present(site)) write(*,*) ' ......Instance = ',site
          errc = 1
          exit rindex_check
        endif
      enddo rindex_check

      call lmpi_max(errc,errcm)
      call lmpi_bcast(errcm)
      if (errcm > 0) call lmpi_die

    endif

    rindex : do inde = 1, sr%recvproc(lmpi_nproc+1) - 1
      inode = sr%recvindex(inde)
      rlead : do l = 1, nleaddim
        data(l,inode) = data(l,inode) + sr%tempName_mat_recvdata(l,inde)
      enddo rlead
    enddo rindex

#else
! avoid compiler warnings
    if (.false.) write(*,*) data, site, sr%size_sendindex
#endif

  end subroutine tempName_matrix_enda_xfer
!tempEndExpand


!tempStartExpand(lmpi_xferedge)
!=============================== tempName_vector_xferedge
!
! non-blocking send/receive pair for rank 1 tempName data on edges
! (private)
!
! used to share a local array with all the other edges to fill partition
! boundary ghost edges.
!
!=============================================================================80

  subroutine tempName_vector_xferedge(data, ghostlevel_arg, site )

    tempType,         dimension(:), intent(inout) :: data
    integer,          optional,     intent(in)    :: ghostlevel_arg
    character(len=*), optional,     intent(in)    :: site

#ifdef HAVE_MPI
    integer :: lvl                 ! mpi ghost node level
#endif

    continue

#ifdef HAVE_MPI
   if (lmpi_nproc == 1) return ! for sequential_mode and mpi with one proc

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = edgeghostlevel
    endif

    if(present(site)) then
      call lmpi_xfer( data, ghostlevel_arg=lvl, site=site )
    else
      call lmpi_xfer( data, ghostlevel_arg=lvl )
    endif
#else
! avoid compiler warnings
    if (.false.) write(*,*) edgeghostlevel, ghostlevel_arg, data, site
#endif

  end subroutine tempName_vector_xferedge
!tempEndExpand

!tempStartExpand(lmpi_xfer_jacobian)
!========================== tempName_XFER_JACOBIAN ===========================80
!
!  Routine to transfer equation rows in a_off across processors.
!  This is so that we only have to form terms on level-0 nodes, then
!  this will transfer the ghosts
!
!=============================================================================80
  subroutine tempName_xfer_jacobian(bs, iam, jam, g2m, m2g, a_off,             &
                                    nnodes0, nnodes01, l2g,                    &
                                    nsorted, sortedglobal, sortedlocal)

    use lmpi,        only : lmpi_conditional_stop
    use sort,        only : binary_search

    integer,                     intent(in)    :: bs     ! block size
    integer, dimension(:),       intent(in)    :: iam, jam, g2m, m2g
    tempType, dimension(:,:,:),  intent(inout) :: a_off
    integer,                     intent(in)    :: nnodes0, nnodes01
    integer, dimension(nnodes01),intent(in)    :: l2g
    integer,                     intent(in)    :: nsorted
    integer, dimension(nsorted), intent(in)    :: sortedglobal, sortedlocal

    integer, parameter :: ghost_level     = 1

    integer :: n_rows_to_send, n_rows_to_recv, jstart, jend, column_num
    integer :: proc_max_columns, packed_index, row, max_columns, entry_index
    integer :: j, global_col, local_col, row_column_count, col
    integer :: column_found_status
#ifdef HAVE_MPI
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    integer, dimension(:,:), pointer :: send_jac_column, recv_jac_column

    tempType, dimension(:,:,:,:), allocatable :: send_jac_entries
    tempType, dimension(:,:,:,:), allocatable :: recv_jac_entries

  continue

! This routine breaks down for one processor so just return in
! this case.  Obviously we wouldn't need to exchange rows anyways

    if ( lmpi_nproc == 1 ) return

#ifdef HAVE_MPI
#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer6_count = lmpi_xfer6_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif
#endif

! Number of Jacobian rows to send and receive

    n_rows_to_send= sr(ghost_level,gl)%sendproc(lmpi_nproc+1) - 1
    n_rows_to_recv= sr(ghost_level,gl)%recvproc(lmpi_nproc+1) - 1

! Determine the max number of columns in any of the rows that we are sending out

    proc_max_columns = 0
    do packed_index = 1, n_rows_to_send
      row = g2m(sr(ghost_level,gl)%sendindex(packed_index))
      jstart = iam(row)
      jend   = iam(row+1)-1
      row_column_count = jend-jstart+1
      proc_max_columns = max(proc_max_columns, row_column_count)
    end do

! Figure out the global max number of columns to be sent out

    call lmpi_max(proc_max_columns, max_columns)
    call lmpi_bcast(max_columns)

    allocate(send_jac_column(max_columns,n_rows_to_send))
    allocate(recv_jac_column(max_columns,n_rows_to_recv))
    allocate(send_jac_entries(bs,bs,max_columns,n_rows_to_send))
    allocate(recv_jac_entries(bs,bs,max_columns,n_rows_to_recv))

    send_jac_column = 0                      ! to mark entries as unused
    recv_jac_column = 0                      ! to mark entries as unused

! Pack the data

    do packed_index = 1, n_rows_to_send
      row = g2m(sr(ghost_level,gl)%sendindex(packed_index))
      entry_index = 0
      do j = iam(row), iam(row+1)-1
        entry_index = entry_index + 1
        col = m2g(jam(j))
        send_jac_column(entry_index,packed_index) = l2g(col)
        send_jac_entries(:,:,entry_index,packed_index) = a_off(:,:,j)
      end do
    end do

! Send the data

    call lmpi_sendreceive(send_jac_column,                                     &
                          sr(ghost_level,gl)%sendproc,            &
                          recv_jac_column,                                     &
                          sr(ghost_level,gl)%recvproc )

    call lmpi_sendreceive(send_jac_entries,                                    &
                          sr(ghost_level,gl)%sendproc,            &
                          recv_jac_entries,                                    &
                          sr(ghost_level,gl)%recvproc )

! Loop over ghost rows and see if we need to unpack and store each received row

    column_found_status = 0
    for_each_packed_row : do packed_index = 1, n_rows_to_recv

      row = g2m(sr(ghost_level,gl)%recvindex(packed_index))

      unpack_row : do entry_index = 1, max_columns
        global_col = recv_jac_column(entry_index,packed_index)
        if ( global_col < 1 ) exit unpack_row        ! reached end of row
        local_col = binary_search(nsorted,sortedglobal,global_col)
        if (local_col > 0) then
          local_col = sortedlocal(local_col)
        else
          local_col = 0
        end if

! local_node = 0 if past level 1
! also kick out if greater than level 1

        if (local_col < 1 .or. local_col > nnodes0) cycle unpack_row

! use search over ja to compute j from the local_node (aka the column)

        jstart = iam(row)
        jend   = iam(row+1)-1
        hunt_for_col : do j = jstart, jend
          column_num = m2g(jam(j))
          if ( column_num == local_col ) exit hunt_for_col
        end do hunt_for_col

        if ( j > jend ) then
          write(*,*) 'xfer_jacobian: Bug in finding column'
          column_found_status = 1
          exit for_each_packed_row
        endif

        a_off(:,:,j) = recv_jac_entries(:,:,entry_index,packed_index)

      end do unpack_row
    end do for_each_packed_row
    call lmpi_conditional_stop(column_found_status)

! Deallocate some memory we don't need anymore

    deallocate(send_jac_column,recv_jac_column)
    deallocate(send_jac_entries,recv_jac_entries)

#ifdef HAVE_MPI
#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer6_time = lmpi_xfer6_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif
#endif

  end subroutine tempName_xfer_jacobian
!tempEndExpand

!tempStartExpand(lmpi_sumnode)
!=============================== tempName_vector_sumnode
!
! non-blocking send/receive pair for summing rank 1 tempName
!   ghost node data on the level 0 nodes  (private)

!=============================================================================80

  subroutine tempName_vector_sumnode(data, ghostlevel_arg )

    tempType,        dimension(:),   intent(inout) :: data
    integer,         optional,       intent(in)    :: ghostlevel_arg

#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: mpitag, length ! unique message tag and length
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode          ! mpi packing loop indexes
    integer :: i_other_proc   ! processor loop index
    integer :: astat          ! allocate status
    integer :: lvl            ! mpi ghost level to transfer
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer3_count = lmpi_xfer3_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

    senddim  = sr(lvl,gl)%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr(lvl,gl)%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr(lvl,gl)%tempName_vec_allocated ) then

    else chk_alloc

      allocate(sr(lvl,gl)%tempName_vec_recvdata(max(1,recvdim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_recvdata(recvdim)) failed"

      allocate(sr(lvl,gl)%tempName_vec_senddata(max(1,senddim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_senddata(senddim)) failed"

      sr(lvl,gl)%tempName_vec_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr(lvl,gl)%sendproc(i_other_proc+2)                           &
               - sr(lvl,gl)%sendproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 0 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

!         write(*,*)'irecv(5)'
          call mpi_irecv( sr(lvl,gl)%tempName_vec_senddata(        &
               sr(lvl,gl)%sendproc(i_other_proc+1) ),              &
               length, tempMPIType,                                &
               i_other_proc, mpitag, lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv in tempSub failed... "
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node information to send arrays

    sindex : do inde = 1, sr(lvl,gl)%recvproc(lmpi_nproc+1) - 1
      inode = sr(lvl,gl)%recvindex(inde)
      sr(lvl,gl)%tempName_vec_recvdata(inde) = data(inode)
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr(lvl,gl)%recvproc(i_other_proc+2)                           &
               - sr(lvl,gl)%recvproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 0 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

!         write(*,*)'isend(5)'
          call mpi_isend( sr(lvl,gl)%tempName_vec_recvdata(        &
               sr(lvl,gl)%recvproc(i_other_proc+1) ),              &
               length, tempMPIType,                                &
               i_other_proc, mpitag, lmpi_comm, sendreq(nsend), ierr )

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend in tempSub failed... "
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait in send tempSub failed..."
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in recv tempSub failed..."
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    rindex : do inde = 1, sr(lvl,gl)%sendproc(lmpi_nproc+1) - 1
      inode = sr(lvl,gl)%sendindex(inde)
      data(inode) = data(inode) + sr(lvl,gl)%tempName_vec_senddata(inde)
    enddo rindex

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer3_time = lmpi_xfer3_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (.false.) write(*,*) ghostlevel_arg, data
#endif

  end subroutine tempName_vector_sumnode

!=============================== tempName_matrix_sumnode
!
! non-blocking send/receive pair for summing rank 2 tempName
!   ghost node data on the level 0 nodes  (private)

!=============================================================================80

  subroutine tempName_matrix_sumnode(data, ghostlevel_arg )

    tempType,        dimension(:,:), intent(inout) :: data
    integer,         optional,       intent(in)    :: ghostlevel_arg

#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: mpitag, length ! unique message tag and length
    integer :: nleaddim       ! extent of leading dimension
    integer :: nallocdim      ! extent of leading dimension
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode,l        ! mpi packing loop indexes
    integer :: i_other_proc   ! processor loop index
    integer :: astat          ! allocate status
    integer :: lvl            ! mpi ghost level to transfer
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer3_count = lmpi_xfer3_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

    nleaddim = size(data,1)
    senddim  = sr(lvl,gl)%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr(lvl,gl)%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr(lvl,gl)%tempName_mat_allocated ) then
      nallocdim = size(sr(lvl,gl)%tempName_mat_recvdata,1)
      leader : if ( nallocdim < nleaddim) then

        deallocate(sr(lvl,gl)%tempName_mat_recvdata)
        deallocate(sr(lvl,gl)%tempName_mat_senddata)

        allocate(sr(lvl,gl)%tempName_mat_recvdata(max(1,nleaddim),             &
                 max(1,recvdim)), stat = astat)

        if(astat /= 0) print *,                                                &
          "lmpi: re allocate(tempName_mat_recvdata(nleaddim,recvdim)) failed"

        allocate(sr(lvl,gl)%tempName_mat_senddata(max(1,nleaddim),             &
                 max(1,senddim)), stat = astat)

        if(astat /= 0) print *,                                                &
          "lmpi: re tempName_mat_senddata(nleaddim,senddim)) failed"

        nallocdim = nleaddim

      endif leader

    else chk_alloc

      nallocdim = nleaddim

      allocate(sr(lvl,gl)%tempName_mat_recvdata(max(1,nleaddim),               &
               max(1,recvdim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_mat_recvdata(nleaddim,recvdim)) failed"

      allocate(sr(lvl,gl)%tempName_mat_senddata(max(1,nleaddim),               &
               max(1,senddim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_mat_senddata(nleaddim,senddim)) failed"

      sr(lvl,gl)%tempName_mat_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr(lvl,gl)%sendproc(i_other_proc+2)                           &
               - sr(lvl,gl)%sendproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 0 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

!         write(*,*)'irecv(6)'
          call mpi_irecv( sr(lvl,gl)%tempName_mat_senddata(        &
               1, sr(lvl,gl)%sendproc(i_other_proc+1) ),           &
               length*nallocdim, tempMPIType,                      &
               i_other_proc, mpitag, lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv in tempSub failed... "
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node information to send arrays

    sindex : do inde = 1, sr(lvl,gl)%recvproc(lmpi_nproc+1) - 1
      inode = sr(lvl,gl)%recvindex(inde)
      slead : do l = 1, nleaddim
        sr(lvl,gl)%tempName_mat_recvdata(l,inde) = data(l,inode)
      enddo slead
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr(lvl,gl)%recvproc(i_other_proc+2)                           &
               - sr(lvl,gl)%recvproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 0 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

!         write(*,*)'isend(6)'
          call mpi_isend( sr(lvl,gl)%tempName_mat_recvdata(        &
               1, sr(lvl,gl)%recvproc(i_other_proc+1) ),           &
               length*nallocdim, tempMPIType,                      &
               i_other_proc, mpitag, lmpi_comm, sendreq(nsend), ierr )

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend in tempSub failed... "
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait in send tempSub failed..."
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in recv tempSub failed..."
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    rindex : do inde = 1, sr(lvl,gl)%sendproc(lmpi_nproc+1) - 1
      inode = sr(lvl,gl)%sendindex(inde)
      rlead : do l = 1, nleaddim
        data(l,inode) = data(l,inode) + sr(lvl,gl)%tempName_mat_senddata(l,inde)
      enddo rlead
    enddo rindex

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer3_time = lmpi_xfer3_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (.false.) write(*,*) ghostlevel_arg, data
#endif

  end subroutine tempName_matrix_sumnode

!tempEndExpand
!tempStartExpand(lmpi_maxnode)
!=============================== tempName_vector_maxnode
!
! non-blocking send/receive pair for maxing rank 1 tempName
!   ghost node data on the level 0 nodes  (private)

!=============================================================================80

  subroutine tempName_vector_maxnode(data, ghostlevel_arg )

    tempType,        dimension(:),   intent(inout) :: data
    integer,         optional,       intent(in)    :: ghostlevel_arg

#ifdef HAVE_MPI
    integer :: ierr           ! communicator, error flag
    integer :: mpitag, length ! unique message tag and length
    integer :: recvdim        ! extents of mpi work array
    integer :: senddim        ! extents of mpi work array
    integer :: inde           ! mpi packing loop indexes
    integer :: inode          ! mpi packing loop indexes
    integer :: i_other_proc   ! processor loop index
    integer :: astat          ! allocate status
    integer :: lvl            ! mpi ghost level to transfer
#ifdef TIME_MPI
    logical :: lmpi_Itime     ! True if this is the outermost driver
#endif
#endif

    continue

#ifdef HAVE_MPI
    if (lmpi_nproc == 1) return ! mpi with one proc

#ifdef TIME_MPI
    lmpi_Itime          = .false.
    if (lmpi_app_timing.and..not.lmpi_timed) then
       lmpi_Itime       = .true.
       lmpi_timed       = .true.
       lmpi_xfer3_count = lmpi_xfer3_count + 1
       if (lmpi_app_timing_sync) call lmpi_synchronize()
       lmpi_start_time  = mpi_wtime()
    end if
#endif

    if( present(ghostlevel_arg) )then
      lvl = ghostlevel_arg
    else
      lvl = defaultghostlevel
    endif

    senddim  = sr(lvl,gl)%sendproc(lmpi_nproc+1) - 1
    recvdim  = sr(lvl,gl)%recvproc(lmpi_nproc+1) - 1

! check if mpi work array is allocated and the correct size
    chk_alloc : if ( sr(lvl,gl)%tempName_vec_allocated ) then

    else chk_alloc

      allocate(sr(lvl,gl)%tempName_vec_recvdata(max(1,recvdim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_recvdata(recvdim)) failed"

      allocate(sr(lvl,gl)%tempName_vec_senddata(max(1,senddim)), stat = astat)

      if(astat /= 0) print *,                                                  &
        "lmpi: allocate(tempName_vec_senddata(senddim)) failed"

      sr(lvl,gl)%tempName_vec_allocated = .TRUE.

    endif chk_alloc

! post all the mpi recv commands first.

    nrecv = 0

    recv_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_r : if (i_other_proc == lmpi_id) then
! duh... i already have it
        else

        length = sr(lvl,gl)%sendproc(i_other_proc+2)                           &
               - sr(lvl,gl)%sendproc(i_other_proc+1)

        rlen : if (length>0) then

          nrecv  = nrecv + 1
          mpitag = 0 ! mpitag = lmpi_id * lmpi_nproc + i_other_proc

!         write(*,*)'irecv(7)'
          call mpi_irecv( sr(lvl,gl)%tempName_vec_senddata(        &
               sr(lvl,gl)%sendproc(i_other_proc+1) ),              &
               length, tempMPIType,                                &
               i_other_proc, mpitag, lmpi_comm , recvreq(nrecv), ierr )

          irecv_error: if( ierr /= lmpi_success )then
            print *, "mpi_irecv in tempSub failed... "
          endif irecv_error

        endif rlen
      endif if_me_r
    enddo recv_loop

! copy node information to send arrays

    sindex : do inde = 1, sr(lvl,gl)%recvproc(lmpi_nproc+1) - 1
      inode = sr(lvl,gl)%recvindex(inde)
      sr(lvl,gl)%tempName_vec_recvdata(inde) = data(inode)
    enddo sindex

! post all the mpi send commands second
! (note that the send mpitag is the inverse of the recv command).

    nsend=0

    send_loop : do i_other_proc = 0, lmpi_nproc-1

      if_me_s : if (i_other_proc == lmpi_id) then
! duh... why send it to myself ?
      else

        length = sr(lvl,gl)%recvproc(i_other_proc+2)                           &
               - sr(lvl,gl)%recvproc(i_other_proc+1)

        slen : if (length>0) then

          nsend  = nsend + 1
          mpitag = 0 ! mpitag = i_other_proc * lmpi_nproc + lmpi_id

!         write(*,*)'isend(7)'
          call mpi_isend( sr(lvl,gl)%tempName_vec_recvdata(        &
               sr(lvl,gl)%recvproc(i_other_proc+1) ),              &
               length, tempMPIType,                                &
               i_other_proc, mpitag, lmpi_comm, sendreq(nsend), ierr )

          isend_error: if( ierr /= lmpi_success )then
            print *, "mpi_isend in tempSub failed... "
          endif isend_error

        endif slen
      endif if_me_s
    enddo send_loop

! wait for all mpi comnunication to complete

    ierr = lmpi_success
    if (nsend>0) call lmpi_waitall(nsend, sendreq, ierr)

    wait_send_error: if( ierr /= lmpi_success )then
      print *, "send mpi_wait in send tempSub failed..."
      return
    endif wait_send_error

    ierr = lmpi_success
    if (nrecv>0) call lmpi_waitall(nrecv, recvreq, ierr)

    wait_recv_error: if( ierr /= lmpi_success )then
      print *, "recv mpi_wait in recv tempSub failed..."
      return
    endif wait_recv_error

! copy recv'd node information to local arrays

    rindex : do inde = 1, sr(lvl,gl)%sendproc(lmpi_nproc+1) - 1
      inode = sr(lvl,gl)%sendindex(inde)
      data(inode) = max(data(inode),sr(lvl,gl)%tempName_vec_senddata(inde))
    enddo rindex

#ifdef TIME_MPI
    if (lmpi_app_timing.and.lmpi_Itime) then
       lmpi_end_time   = mpi_wtime()
       lmpi_Itime      = .false.
       lmpi_timed      = .false.
       lmpi_xfer3_time = lmpi_xfer3_time + (lmpi_end_time - lmpi_start_time)
    end if
#endif

#else
! avoid compiler warnings
    if (.false.) write(*,*) ghostlevel_arg, data
#endif

  end subroutine tempName_vector_maxnode

!tempEndExpand


!tempStartExpand(lmpi_sendreceive)
!=============================== tempName_vector_sendreceive
!
! non-blocking send/receive pair for rank 1 tempName data
! (private)
!
!=============================================================================80

  subroutine tempName_vector_sendreceive(senddata, sendindex,                  &
                                         recvdata, recvindex )

    tempType,        dimension(:),            intent(in)  :: senddata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: sendindex
    tempType,        dimension(:),            intent(out) :: recvdata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: recvindex

#ifdef HAVE_MPI
    integer :: received, sent ! number of send and recv messages
    integer :: recvId, sendId ! processorId+1 of send and recv
    integer :: messagelength  ! duh...
    integer :: mpitag         ! unique message tag
    integer :: ierror         ! mpi error reporting
#endif

    continue

#ifdef HAVE_MPI

    received = 0
    recvId = lmpi_id + 1
    do sendId = 1, lmpi_nproc
      messagelength = recvindex(sendId+1) - recvindex(sendId)
      if (messagelength > 0) then
        received = received + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'irecv(8)'
        call mpi_irecv(                       &
          recvdata(recvindex(sendId)),        &
          messagelength,                      &
          tempMPIType, sendId-1, mpitag,      &
          lmpi_comm, recvreq(received), ierror)
      end if
    end do

    sent = 0
    sendId = lmpi_id + 1
    do recvId = 1, lmpi_nproc
      messagelength = sendindex(recvId+1) - sendindex(recvId)
      if (messagelength > 0) then
        sent = sent + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'isend(8)'
        call mpi_isend(                   &
          senddata(sendindex(recvId)),    &
          messagelength,                  &
          tempMPIType, recvId-1, mpitag,  &
          lmpi_comm, sendreq(sent), ierror)
      end if
    end do

    if (sent>0)     call lmpi_waitall(sent,     sendreq, ierror)
    if (received>0) call lmpi_waitall(received, recvreq, ierror)

#else
    recvdata(recvindex(1):recvindex(2)-1)=senddata(sendindex(1):sendindex(2)-1)
#endif

  end subroutine tempName_vector_sendreceive

!=============================== tempName_matrix_sendreceive
!
! non-blocking send/receive pair for rank 2 tempName data
! (private)
!
!=============================================================================80

  subroutine tempName_matrix_sendreceive(senddata, sendindex,                  &
                                         recvdata, recvindex )

    tempType,        dimension(:,:),          intent(in)  :: senddata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: sendindex
    tempType,        dimension(:,:),          intent(out) :: recvdata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: recvindex

#ifdef HAVE_MPI
    integer :: nleaddim       ! extent of leading dimension
    integer :: received, sent ! number of send and recv messages
    integer :: recvId, sendId ! processorId+1 of send and recv
    integer :: messagelength  ! duh...
    integer :: mpitag         ! unique message tag
    integer :: ierror         ! mpi error reporting
#endif

    continue

#ifdef HAVE_MPI

    nleaddim = size(senddata,1)

    received = 0
    recvId = lmpi_id + 1
    do sendId = 1, lmpi_nproc
      messagelength = recvindex(sendId+1) - recvindex(sendId)
      if (messagelength > 0) then
        received = received + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'irecv(9)'
        call mpi_irecv(                         &
             recvdata(1,recvindex(sendId)),     &
             messagelength*nleaddim,            &
             tempMPIType, sendId-1, mpitag,     &
             lmpi_comm, recvreq(received), ierror)
      end if
    end do

    sent = 0
    sendId = lmpi_id + 1
    do recvId = 1, lmpi_nproc
      messagelength = sendindex(recvId+1) - sendindex(recvId)
      if (messagelength > 0) then
        sent = sent + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'isend(9)'
        call mpi_isend(                  &
          senddata(1,sendindex(recvId)), &
          messagelength*nleaddim,        &
          tempMPIType, recvId-1, mpitag, &
          lmpi_comm, sendreq(sent), ierror)
      end if
    end do

    if (sent>0)     call lmpi_waitall(sent,     sendreq, ierror)
    if (received>0) call lmpi_waitall(received, recvreq, ierror)

#else
    recvdata(:,recvindex(1):recvindex(2)-1) =                                  &
    senddata(:,sendindex(1):sendindex(2)-1)
#endif

  end subroutine tempName_matrix_sendreceive

!=============================== tempName_matrx3_sendreceive
!
! non-blocking send/receive pair for rank 3 tempName data
! (private)
!
!=============================================================================80

  subroutine tempName_matrx3_sendreceive(senddata, sendindex,                  &
                                         recvdata, recvindex )

    tempType,        dimension(:,:,:),        intent(in)  :: senddata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: sendindex
    tempType,        dimension(:,:,:),        intent(out) :: recvdata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: recvindex

#ifdef HAVE_MPI
    integer :: mleaddim, nleaddim ! extent of leading dimensions
    integer :: received, sent     ! number of send and recv messages
    integer :: recvId, sendId     ! processorId+1 of send and recv
    integer :: messagelength      ! duh...
    integer :: mpitag             ! unique message tag
    integer :: ierror             ! mpi error reporting
#endif

    continue

#ifdef HAVE_MPI

    mleaddim = size(senddata,1)
    nleaddim = size(senddata,2)

    received = 0
    recvId = lmpi_id + 1
    do sendId = 1, lmpi_nproc
      messagelength = recvindex(sendId+1) - recvindex(sendId)
      if (messagelength > 0) then
        received = received + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'irecv(9)'
        call mpi_irecv(                         &
             recvdata(1,1,recvindex(sendId)),   &
             messagelength*mleaddim*nleaddim,   &
             tempMPIType, sendId-1, mpitag,     &
             lmpi_comm, recvreq(received), ierror)
      end if
    end do

    sent = 0
    sendId = lmpi_id + 1
    do recvId = 1, lmpi_nproc
      messagelength = sendindex(recvId+1) - sendindex(recvId)
      if (messagelength > 0) then
        sent = sent + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'isend(9)'
        call mpi_isend(                    &
          senddata(1,1,sendindex(recvId)), &
          messagelength*mleaddim*nleaddim, &
          tempMPIType, recvId-1, mpitag,   &
          lmpi_comm, sendreq(sent), ierror)
      end if
    end do

    if (sent>0)     call lmpi_waitall(sent,     sendreq, ierror)
    if (received>0) call lmpi_waitall(received, recvreq, ierror)

#else
    recvdata(:,:,recvindex(1):recvindex(2)-1) =                                 &
    senddata(:,:,sendindex(1):sendindex(2)-1)
#endif

  end subroutine tempName_matrx3_sendreceive

!=============================== tempName_matrx4_sendreceive
!
! non-blocking send/receive pair for rank 4 tempName data
! (private)
!
!=============================================================================80

  subroutine tempName_matrx4_sendreceive(senddata, sendindex,                  &
                                         recvdata, recvindex )

    tempType,        dimension(:,:,:,:),      intent(in)  :: senddata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: sendindex
    tempType,        dimension(:,:,:,:),      intent(out) :: recvdata
    integer,         dimension(lmpi_nproc+1), intent(in)  :: recvindex

#ifdef HAVE_MPI
    integer :: lleaddim, mleaddim, nleaddim ! extent of leading dimensions
    integer :: received, sent               ! number of send and recv messages
    integer :: recvId, sendId               ! processorId+1 of send and recv
    integer :: messagelength                ! duh...
    integer :: mpitag                       ! unique message tag
    integer :: ierror                       ! mpi error reporting
#endif

    continue

#ifdef HAVE_MPI

    lleaddim = size(senddata,1)
    mleaddim = size(senddata,2)
    nleaddim = size(senddata,3)

    received = 0
    recvId = lmpi_id + 1
    do sendId = 1, lmpi_nproc
      messagelength = recvindex(sendId+1) - recvindex(sendId)
      if (messagelength > 0) then
        received = received + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'irecv(9)'
        call mpi_irecv(                                &
             recvdata(1,1,1,recvindex(sendId)),        &
             messagelength*lleaddim*mleaddim*nleaddim, &
             tempMPIType, sendId-1, mpitag,            &
             lmpi_comm, recvreq(received), ierror)
      end if
    end do

    sent = 0
    sendId = lmpi_id + 1
    do recvId = 1, lmpi_nproc
      messagelength = sendindex(recvId+1) - sendindex(recvId)
      if (messagelength > 0) then
        sent = sent + 1
        mpitag = 0 ! mpitag = recvId * lmpi_nproc + sendId
!       write(*,*)'isend(9)'
        call mpi_isend(                             &
          senddata(1,1,1,sendindex(recvId)),        &
          messagelength*lleaddim*mleaddim*nleaddim, &
          tempMPIType, recvId-1, mpitag,            &
          lmpi_comm, sendreq(sent), ierror)
      end if
    end do

    if (sent>0)     call lmpi_waitall(sent,     sendreq, ierror)
    if (received>0) call lmpi_waitall(received, recvreq, ierror)

#else
    recvdata(:,:,:,recvindex(1):recvindex(2)-1) =                              &
    senddata(:,:,:,sendindex(1):sendindex(2)-1)
#endif

  end subroutine tempName_matrx4_sendreceive

!tempEndExpand

!================================== LMPI_COPY ================================80
!
! Fills the the sendproc, recvproc, sendindex, and recvindex arrays
! from sr1.
!
! For cell-centered (cc), this is a work in progress.
!
!=============================================================================80

  subroutine lmpi_copy(gl,sr1,sre)

#ifdef HAVE_MPI

    use lmpi,        only : lmpi_conditional_stop, lmpi_nproc
    use interp_defs, only : basic_sendrecv_type

    integer,                                  intent(in) :: gl
    type (basic_sendrecv_type), dimension(:), intent(in) :: sr1, sre

    integer :: i,j,local_ndatalevel,p1, idata

  continue

    p1 = lmpi_id + 1

    local_ndatalevel = ndatalevel

    if ( .not. sr_allocated ) then
      allocate(sr(local_ndatalevel,max_gl))
      sr_allocated = .TRUE.
      do i=1,local_ndatalevel
        do j=1,max_gl
          call nullify_sendrecv(sr(i,j))
        end do
      end do
    end if

    if (lmpi_nproc > 1 ) then

       call lmpi_deallocate_sendrecv(1)

       sr(1,gl)%sendrecv_allocated = .true.

       allocate(sr(1,gl)%sendproc(lmpi_nproc+1))
       sr(1,gl)%sendproc = sr1(p1)%sendproc

       allocate(sr(1,gl)%recvproc(lmpi_nproc+1))
       sr(1,gl)%recvproc = sr1(p1)%recvproc

       i = 1
       if (associated(sr1(p1)%sendindex)) i = size(sr1(p1)%sendindex)
       allocate(sr(1,gl)%sendindex(i));   sr(1,gl)%sendindex = 0
       if (associated(sr1(p1)%sendindex)) then
          sr(1,gl)%sendindex = sr1(p1)%sendindex
          sr(1,gl)%size_sendindex = i
       else
          sr(1,gl)%size_sendindex = 0
       end if

       j = 1
       if (associated(sr1(p1)%recvindex)) j = size(sr1(p1)%recvindex)
       allocate(sr(1,gl)%recvindex(j));   sr(1,gl)%recvindex = 0
       if (associated(sr1(p1)%recvindex)) then
          sr(1,gl)%recvindex = sr1(p1)%recvindex
          sr(1,gl)%size_recvindex = j
       else
          sr(1,gl)%size_recvindex = 0
       end if

! sr2
       call lmpi_deallocate_sendrecv(2)

       sr(2,gl)%sendrecv_allocated = .true.
       allocate(sr(2,gl)%sendproc(lmpi_nproc+1))
       sr(2,gl)%sendproc = 1 ! sr2(p1)%sendproc sr2 DUMMY

       allocate(sr(2,gl)%recvproc(lmpi_nproc+1))
       sr(2,gl)%recvproc = 1 ! sr2(p1)%recvproc sr2 DUMMY

       i = 1
       ! if (associated(sr2(p1)%sendindex)) i = size(sr2(p1)%sendindex)
       allocate(sr(2,gl)%sendindex(i));   sr(2,gl)%sendindex=0
       !if (associated(sr2(p1)%sendindex)) then
       !  sr(2,gl)%sendindex=sr2(p1)%sendindex
       !  sr(2,gl)%size_sendindex = i
       !else
          sr(2,gl)%size_sendindex = 0
       !end if

       j = 1
       !if (associated(sr2(p1)%recvindex)) j = size(sr2(p1)%recvindex)
       allocate(sr(2,gl)%recvindex(j));   sr(2,gl)%recvindex=0
       !if (associated(sr2(p1)%recvindex)) then
       !  sr(2,gl)%recvindex=sr2(p1)%recvindex
       !  sr(2,gl)%size_recvindex = j
       !else
          sr(2,gl)%size_recvindex = 0
       !end if

! sre

       call lmpi_deallocate_sendrecv(3)

       sr(3,gl)%sendrecv_allocated = .true.

       allocate(sr(3,gl)%sendproc(lmpi_nproc+1))
       sr(3,gl)%sendproc = sre(p1)%sendproc

       allocate(sr(3,gl)%recvproc(lmpi_nproc+1))
       sr(3,gl)%recvproc = sre(p1)%recvproc

       i = 1
       if (associated(sre(p1)%sendindex)) i = size(sre(p1)%sendindex)
       allocate(sr(3,gl)%sendindex(i));   sr(3,gl)%sendindex = 0
       if (associated(sre(p1)%sendindex)) then
         sr(3,gl)%sendindex=sre(p1)%sendindex
         sr(3,gl)%size_sendindex = i
       else
         sr(3,gl)%size_sendindex = 0
       end if

       j = 1
       if (associated(sre(p1)%recvindex)) j = size(sre(p1)%recvindex)
       allocate(sr(3,gl)%recvindex(j));   sr(3,gl)%recvindex = 0
       if (associated(sre(p1)%recvindex)) then
          sr(3,gl)%recvindex=sre(p1)%recvindex
          sr(3,gl)%size_recvindex = j
       else
         sr(3,gl)%size_recvindex = 0
       end if

    endif ! lmpi_nproc > 1

! excess to match lmpi_readme

    do idata = 4,ndatalevel
       call lmpi_deallocate_sendrecv(idata)

      sr(idata,gl)%sendrecv_allocated = .true.
      allocate(sr(idata,gl)%sendproc(lmpi_nproc+1)); sr(idata,gl)%sendproc  = 1
      allocate(sr(idata,gl)%recvproc(lmpi_nproc+1)); sr(idata,gl)%recvproc  = 1
      allocate(sr(idata,gl)%sendindex(1));           sr(idata,gl)%sendindex = 1
      allocate(sr(idata,gl)%recvindex(1));           sr(idata,gl)%recvindex = 1

    end do

  end subroutine lmpi_copy

#else
    use interp_defs, only : basic_sendrecv_type

    integer,                                  intent(in) :: gl
    type (basic_sendrecv_type), dimension(:), intent(in) :: sr1, sre

  continue

    if (.false.) write(*,*) gl,max_gl,associated(sr1(1)%sendindex),            &
                            associated(sre(1)%sendindex)

  end subroutine lmpi_copy

#endif

!-------------------------------------- START Private Parts -----
#ifdef HAVE_MPI

!============================ LMPI_DEALLOCATE_SENDRECV =======================80
!
! deallocate the sr send recieve arrays used by lmpi_xfer
!
!=============================================================================80

  subroutine lmpi_deallocate_sendrecv( lvl )!tempProtectPrivate

    integer, intent(in) :: lvl

    continue

    if (lmpi_nproc == 1) return ! for mpi with one proc
    if ( .not. sr_allocated ) return ! if the sr array is not allocated

    if (sr(lvl,gl)%sendrecv_allocated) then
      sr(lvl,gl)%sendrecv_allocated = .false.
      deallocate(sr(lvl,gl)%sendproc)
      deallocate(sr(lvl,gl)%recvproc)
      deallocate(sr(lvl,gl)%sendindex)
      deallocate(sr(lvl,gl)%recvindex)
    end if

    if (sr(lvl,gl)%integr_mat_allocated) then
      sr(lvl,gl)%integr_mat_allocated = .false.
      deallocate(sr(lvl,gl)%integr_mat_senddata)
      deallocate(sr(lvl,gl)%integr_mat_recvdata)
    end if

    if (sr(lvl,gl)%integr_vec_allocated) then
      sr(lvl,gl)%integr_vec_allocated = .false.
      deallocate(sr(lvl,gl)%integr_vec_senddata)
      deallocate(sr(lvl,gl)%integr_vec_recvdata)
    end if

    if (sr(lvl,gl)%integ8_mat_allocated) then
      sr(lvl,gl)%integ8_mat_allocated = .false.
      deallocate(sr(lvl,gl)%integ8_mat_senddata)
      deallocate(sr(lvl,gl)%integ8_mat_recvdata)
    end if

    if (sr(lvl,gl)%integ8_vec_allocated) then
      sr(lvl,gl)%integ8_vec_allocated = .false.
      deallocate(sr(lvl,gl)%integ8_vec_senddata)
      deallocate(sr(lvl,gl)%integ8_vec_recvdata)
    end if

    if (sr(lvl,gl)%logicl_mat_allocated) then
      sr(lvl,gl)%logicl_mat_allocated = .false.
      deallocate(sr(lvl,gl)%logicl_mat_senddata)
      deallocate(sr(lvl,gl)%logicl_mat_recvdata)
    end if

    if (sr(lvl,gl)%logicl_vec_allocated) then
      sr(lvl,gl)%logicl_vec_allocated = .false.
      deallocate(sr(lvl,gl)%logicl_vec_senddata)
      deallocate(sr(lvl,gl)%logicl_vec_recvdata)
    end if

    if (sr(lvl,gl)%cmpxr8_mat_allocated) then
      sr(lvl,gl)%cmpxr8_mat_allocated = .false.
      deallocate(sr(lvl,gl)%cmpxr8_mat_senddata)
      deallocate(sr(lvl,gl)%cmpxr8_mat_recvdata)
    end if

    if (sr(lvl,gl)%cmpxr8_vec_allocated) then
      sr(lvl,gl)%cmpxr8_vec_allocated = .false.
      deallocate(sr(lvl,gl)%cmpxr8_vec_senddata)
      deallocate(sr(lvl,gl)%cmpxr8_vec_recvdata)
    end if

    if (sr(lvl,gl)%cmpxr4_mat_allocated) then
      sr(lvl,gl)%cmpxr4_mat_allocated = .false.
      deallocate(sr(lvl,gl)%cmpxr4_mat_senddata)
      deallocate(sr(lvl,gl)%cmpxr4_mat_recvdata)
    end if

    if (sr(lvl,gl)%cmpxr4_vec_allocated) then
      sr(lvl,gl)%cmpxr4_vec_allocated = .false.
      deallocate(sr(lvl,gl)%cmpxr4_vec_senddata)
      deallocate(sr(lvl,gl)%cmpxr4_vec_recvdata)
    end if

    if (sr(lvl,gl)%double_mat_allocated) then
      sr(lvl,gl)%double_mat_allocated = .false.
      deallocate(sr(lvl,gl)%double_mat_senddata)
      deallocate(sr(lvl,gl)%double_mat_recvdata)
    end if

    if (sr(lvl,gl)%double_vec_allocated) then
      sr(lvl,gl)%double_vec_allocated = .false.
      deallocate(sr(lvl,gl)%double_vec_senddata)
      deallocate(sr(lvl,gl)%double_vec_recvdata)
    end if

    if (sr(lvl,gl)%single_mat_allocated) then
      sr(lvl,gl)%single_mat_allocated = .false.
      deallocate(sr(lvl,gl)%single_mat_senddata)
      deallocate(sr(lvl,gl)%single_mat_recvdata)
    end if

    if (sr(lvl,gl)%single_vec_allocated) then
      sr(lvl,gl)%single_vec_allocated = .false.
      deallocate(sr(lvl,gl)%single_vec_senddata)
      deallocate(sr(lvl,gl)%single_vec_recvdata)
    end if

  end subroutine lmpi_deallocate_sendrecv

!================================== add_logical ==============================80
!
! adds logicals by or'ing them
!
!=============================================================================80

  function add_logical(l1, l2) !tempProtectPrivate
    logical, intent(in) :: l1, l2
    logical             :: add_logical

    continue

    add_logical = ( l1 .or. l2 )

  end function add_logical


!============================== multiply_logical_logical =====================80
!
! multiplys logicals by and'ing them
!
!=============================================================================80

  function multiply_logical_logical(l1, l2) !tempProtectPrivate
    logical, intent(in) :: l1, l2
    logical             :: multiply_logical_logical

    continue

    multiply_logical_logical = ( l1 .and. l2 )

  end function multiply_logical_logical


!============================== multiply_single_logical ======================80
!
! multiplys singles and logicals with magic
!
!=============================================================================80

  function multiply_single_logical(l1, l2) !tempProtectPrivate
    real(system_r4), intent(in) :: l1
    logical,         intent(in) :: l2
    logical                     :: multiply_single_logical

    continue

    multiply_single_logical = .false.
    if (l2) then
       write(*,*) 'lmpi_app::multiply_single_logical: should never occur.'
       write(*,*) 'l1,l2 = ',l1,l2
       stop ! FIXME: should be lmpi_die?
    end if

  end function multiply_single_logical


!============================== multiply_double_logical ======================80
!
! multiplys doubles and logicals with magic
!
!=============================================================================80

  function multiply_double_logical(l1, l2) !tempProtectPrivate
    real(system_r8), intent(in) :: l1
    logical,         intent(in) :: l2
    logical                     :: multiply_double_logical

    continue

    multiply_double_logical = .false.
    if (l2) then
       write(*,*) 'lmpi_app::multiply_double_logical: should never occur.'
       write(*,*) 'l1,l2 = ',l1,l2
       stop ! FIXME: should be lmpi_die?
    end if

  end function multiply_double_logical


!========================== multiply_complex_r4_logical ======================80
!
! multiplys complex_r4s and logicals with magic
!
!=============================================================================80

  function multiply_complex_r4_logical(l1, l2) !tempProtectPrivate
    complex(system_r4), intent(in) :: l1
    logical,            intent(in) :: l2
    logical                     :: multiply_complex_r4_logical

    continue

    multiply_complex_r4_logical = .false.
    if (l2) then
       write(*,*) 'lmpi_app::multiply_complex_r4_logical: should never occur.'
       write(*,*) 'l1,l2 = ',l1,l2
       stop ! FIXME: should be lmpi_die?
    end if

  end function multiply_complex_r4_logical


!========================== multiply_complex_r8_logical ======================80
!
! multiplys complex_r8s and logicals with magic
!
!=============================================================================80

  function multiply_complex_r8_logical(l1, l2) !tempProtectPrivate
    complex(system_r8), intent(in) :: l1
    logical,            intent(in) :: l2
    logical                     :: multiply_complex_r8_logical

    continue

    multiply_complex_r8_logical = .false.
    if (l2) then
       write(*,*) 'lmpi_app::multiply_complex_r8_logical: should never occur.'
       write(*,*) 'l1,l2 = ',l1,l2
       stop ! FIXME: should be lmpi_die?
    end if

  end function multiply_complex_r8_logical


!================================== max_logical ==============================80
!
! max's logicals by or'ing them
!
!=============================================================================80

  function max_logical(l1, l2) !tempProtectPrivate
    logical, intent(in) :: l1, l2
    logical             :: max_logical

    continue

    max_logical = ( l1 .or. l2 )

  end function max_logical


!================================== max_complex_r4 ===========================80
!
! max's complex r4 by maxing the real parts
!
!=============================================================================80

  function max_complex_r4(c1, c2) !tempProtectPrivate
    complex(system_r4), intent(in) :: c1, c2
    complex(system_r4)             :: max_complex_r4

    continue

    if( real(c1,system_r4) > real(c2,system_r4) ) then
      max_complex_r4 = c1
    else
      max_complex_r4 = c2
    endif

  end function max_complex_r4


!================================== max_complex_r8 ===========================80
!
! max's complex r8 by maxing the real parts
!
!=============================================================================80

  function max_complex_r8(c1, c2) !tempProtectPrivate
    complex(system_r8), intent(in) :: c1, c2
    complex(system_r8)             :: max_complex_r8

    continue

    if( real(c1,system_r8) > real(c2,system_r8) ) then
      max_complex_r8 = c1
    else
      max_complex_r8 = c2
    endif

  end function max_complex_r8

#endif
!-------------------------------------- START Private Parts -----


!=============================== lmpi_write_stats ===========================80
!
! Calculate and write load balance based on grid and SR information.
! Weight information used in partitioning are not shown.
! Overset not implemented.
!
!=============================================================================80

  subroutine lmpi_write_stats(project,nnodes0,nnodes01,nedgeloc,nedge)

    use lmpi,       only : lmpi_nproc, lmpi_master, lmpi_gather, lmpi_id,      &
                           lmpi_synchronize

    character(len=*), intent(in) :: project
    integer,          intent(in) :: nnodes0, nnodes01, nedgeloc, nedge

    integer :: j,nsends,nrecvs,ipe,nrecv,i_other_proc,length
    integer, dimension(:), allocatable :: q0,q1

    real(dp) :: ri, rj, svol,rvol

    integer,  dimension(:), allocatable :: iwork_arr
    real(dp), dimension(:), allocatable :: work_arr

    character(len=256) :: filename
    integer, parameter :: iunit = 172

  continue

    allocate(iwork_arr(lmpi_nproc)); iwork_arr = 0

    if ( lmpi_master ) then
      filename = trim(project) // '.load_balance_lmpi_stats'
      open(iunit,file=filename,status='unknown',form='formatted',              &
           position='rewind')
      write(iunit,*)
      write(iunit,*) "WORK"
    endif

    call lmpi_gather(nnodes0,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Lev0: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(nnodes01-nnodes0,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Lev1: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(nnodes01,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "  Lev01: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

! Communication

    if (lmpi_master) write(iunit,*)

    call lmpi_sr_stats(nsends,nrecvs,svol,rvol)
    call lmpi_gather(nsends,iwork_arr)
    if (lmpi_master) then
      write(iunit,*)"COMM"
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Send: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(nrecvs,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Recv: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if
    deallocate(iwork_arr)

    allocate(work_arr(lmpi_nproc)); work_arr = 0
    call lmpi_gather(svol,work_arr)
    if (lmpi_master) then
      ri = minval(work_arr)
      rj = maxval(work_arr)
      write(iunit,'(a40,3(F15.0,1x),F8.2,1x)')                                 &
        "   Svol: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(rvol,work_arr)
    if (lmpi_master) then
      ri = minval(work_arr)
      rj = maxval(work_arr)
      write(iunit,'(a40,3(F15.0,1x),F8.2,1x)')                                 &
        "   Rvol: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if
    deallocate(work_arr)

    if (lmpi_master) close(iunit)

    filename = trim(project) // '.load_balance_lmpi_stats'
    allocate(q0(lmpi_nproc)); q0 = 0
    allocate(q1(lmpi_nproc)); q1 = 0
    do ipe = 0,lmpi_nproc-1
       call lmpi_synchronize
       if (ipe == lmpi_id) then
          open(iunit,file=filename,status='old',form='formatted',              &
            position='append')
          if (ipe == 0) then
             write(iunit,*)
             write(iunit,*) "SR Information"
          end if
          write(iunit,'("    IPE ",i0,"  Rvol,Svol :: ",i0,1x,i0)')            &
            ipe,sr(defaultghostlevel,1)%recvproc(lmpi_nproc+1)-1,              &
                sr(defaultghostlevel,1)%sendproc(lmpi_nproc+1)-1

          nrecv = 0
          do i_other_proc = 1,lmpi_nproc
             if (i_other_proc /= ipe+1) then
                length = sr(defaultghostlevel,1)%recvproc(i_other_proc+1)      &
                       - sr(defaultghostlevel,1)%recvproc(i_other_proc)
                if (length>0) then
                   nrecv  = nrecv + 1
                   q0(nrecv) = i_other_proc-1
                   q1(nrecv) = length
                end if
             end if
          end do
          if (nrecv > 0) then
            write(iunit,'(11x,"Recv ",i0,1x,100(1x,"(",i0,1x,i0,")"))')&
                  nrecv,(q0(j),q1(j),j=1,nrecv)
          else
            write(iunit,'(11x,"Recv ",i0)') nrecv
          end if

          nrecv = 0
          do i_other_proc = 1, lmpi_nproc
             if (i_other_proc /= ipe+1) then
                length = sr(defaultghostlevel,1)%sendproc(i_other_proc+1)      &
                       - sr(defaultghostlevel,1)%sendproc(i_other_proc)
                if (length>0) then
                   nrecv  = nrecv + 1
                   q0(nrecv) = i_other_proc-1
                   q1(nrecv) = length
                end if
             end if
          end do
          if (nrecv > 0) then
            write(iunit,'(11x,"Send ",i0,1x,100(1x,"(",i0,1x,i0,")"))')        &
                  nrecv,(q0(j),q1(j),j=1,nrecv)
          else
            write(iunit,'(11x,"Send ",i0)') nrecv
          end if
          deallocate(q0,q1)

          write(iunit,'(11x,"Nodes (0,1,01) :: ",3(i0,1x))')                   &
            nnodes0,nnodes01-nnodes0,nnodes01
          write(iunit,'(11x,"Edges          :: ",2(i0,1x))') nedgeloc,nedge
          write(iunit,*)

          close(iunit)
       end if
    end do

  end subroutine lmpi_write_stats

!============================== LMPI_SR_STATS ================================80
!
! Return stats about the SR array
!
!=============================================================================80

  subroutine lmpi_sr_stats(nsends,nrecvs,svol,rvol)

    use lmpi, only : lmpi_nproc

    integer,  intent(out) :: nsends,nrecvs
    real(dp), intent(out) :: svol,rvol

    integer  :: ipart, i, d1, d2

  continue

    ipart =  1

    nsends  = 0
    svol    = 0._dp
    do i = 1,lmpi_nproc
       d1 = sr(ipart,1)%sendproc(i+1)-sr(ipart,1)%sendproc(i)
       svol = svol + d1*1._dp
       if (d1 > 0) nsends = nsends + 1
    end do

    nrecvs = 0
    rvol   = 0._dp
    do i = 1,lmpi_nproc
       d2 = sr(ipart,1)%recvproc(i+1)-sr(ipart,1)%recvproc(i)
       rvol = rvol + d2*1._dp
       if (d2 > 0) nrecvs = nrecvs + 1
    end do

  end subroutine lmpi_sr_stats

end module lmpi_app

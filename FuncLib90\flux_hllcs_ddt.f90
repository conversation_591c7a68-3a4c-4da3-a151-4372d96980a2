!================================= FLUX_HLLCS_ddt ============================80
!
! Opeerator overloaded version of the:
! HLLC positivity-preserving flux function per <PERSON><PERSON>, <PERSON><PERSON>,
! <PERSON><PERSON>, and <PERSON><PERSON><PERSON><PERSON>, "On the Choice of Wavespeeds for the
! HLLC Rieman Solver", SIAM Journal of Scientific Computation, Volume
! 18, Number 6, pages 1553-1570, November 1997 (available online).
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function flux_hllcs_ddt(rx1, ry1, rz1, rx2, ry2, rz2,                   &
                               xnorm, ynorm, znorm, area, vol1, vol2,          &
                               gradx1, grady1, gradz1, phi1,                   &
                               gradx2, grady2, gradz2, phi2,                   &
                               facespeed, ql, qr, second, mu)

    use kinddefs,        only : dp
    use ddt,             only : ddt5, assignment(=), operator(+), operator(-), &
                                operator(*), operator(/), operator(**),        &
                                operator(>), operator(<), operator(==),        &
                                ddt_min, ddt_max, ddt_sqrt
    use fun3d_constants, only : my_0, my_half, my_1, my_2

    use info_depr,       only : adptv_entropy_fix, ivisc
    use fluid,           only : gm1

    real(dp),                 intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp),                 intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                 intent(in) :: vol1, vol2
    type(ddt5),               intent(in) :: gradx1, grady1, gradz1,            &
                                            gradx2, grady2, gradz2
    real(dp),                 intent(in) :: phi1, phi2
    real(dp),                 intent(in) :: facespeed, mu
    type(ddt5), dimension(5), intent(in) :: ql, qr
    type(ddt5), dimension(6)             :: flux_hllcs_ddt

    logical,                 intent(in) :: second

    type(ddt5) :: rhol, ul, vl, wl, q2l, pressl, energyl, Hl, ubarl, cl
    type(ddt5) :: rhor, ur, vr, wr, q2r, pressr, energyr, Hr, ubarr, cr
    type(ddt5) :: rho, wat, u, v, w, q2, ubar, h, c, c2
    type(ddt5) :: SL, SR, SM, presss
    type(ddt5) :: switch, switchv
    type(ddt5) :: rhosl, rhousl, rhovsl, rhowsl, energysl
    type(ddt5) :: rhosr, rhousr, rhovsr, rhowsr, energysr
    type(ddt5) :: FL1, FL2, FL3, FL4, FL5
    type(ddt5) :: FR1, FR2, FR3, FR4, FR5
    type(ddt5) :: Fhllc1, Fhllc2, Fhllc3, Fhllc4, Fhllc5

    integer,  parameter :: behavior = 10
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_2
    real(dp), parameter :: laplcc = my_2

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining needed left and right state variables:

    q2l     = ul*ul + vl*vl + wl*wl
    energyl = pressl/gm1 + my_half*rhol*q2l
    Hl      = (energyl + pressl)/rhol
    ubarl   = xnorm*ul + ynorm*vl + znorm*wl - facespeed
    cl      = ddt_sqrt(gm1*(Hl-my_half*q2l))

    q2r     = ur*ur + vr*vr + wr*wr
    energyr = pressr/gm1 + my_half*rhor*q2r
    Hr      = (energyr + pressr)/rhor
    ubarr   = xnorm*ur + ynorm*vr + znorm*wr - facespeed
    cr      = ddt_sqrt(gm1*(Hr-my_half*q2r))

!   Left flux:

    FL1 = rhol*ubarl
    FL2 = rhol*ul*ubarl + xnorm*pressl
    FL3 = rhol*vl*ubarl + ynorm*pressl
    FL4 = rhol*wl*ubarl + znorm*pressl
    FL5 = (energyl + pressl)*ubarl

!   Right flux:

    FR1 = rhor*ubarr
    FR2 = rhor*ur*ubarr + xnorm*pressr
    FR3 = rhor*vr*ubarr + ynorm*pressr
    FR4 = rhor*wr*ubarr + znorm*pressr
    FR5 = (energyr + pressr)*ubarr

!   Roe averages:

    rho  = ddt_sqrt(rhol*rhor)
    wat  = rho/(rho + rhor)
    u    = ul*wat + ur*(my_1 - wat)
    v    = vl*wat + vr*(my_1 - wat)
    w    = wl*wat + wr*(my_1 - wat)
    h    = Hl*wat + Hr*(my_1 - wat)
    q2   = u*u + v*v + w*w
    c2   = gm1*(h - my_half*q2)
    c    = ddt_sqrt(c2)
    ubar = xnorm*u + ynorm*v + znorm*w - facespeed

!   Acoustic wave speed estimates:

    SL = ddt_min( ubarl-cl, ubar-c )
    SR = ddt_max( ubarr+cr, ubar+c )

    switch = my_1

!   Load the proper flux based on wave speeds:

    if ( SL > my_0 ) then ! use left state

      Fhllc1 = FL1
      Fhllc2 = FL2
      Fhllc3 = FL3
      Fhllc4 = FL4
      Fhllc5 = FL5

    else if ( SR < my_0 ) then ! use right state

      Fhllc1 = FR1
      Fhllc2 = FR2
      Fhllc3 = FR3
      Fhllc4 = FR4
      Fhllc5 = FR5

    else

!   If the adaptive scheme is turned on call the feature detection scheme to
!   switch the interfaces from being evaluated using the HLLC interior state
!   to the HLL interior state

      if (adptv_entropy_fix) then

!       Inviscid feature detection

        switch = iswch_coef_ddt(rx1,ry1,rz1,rx2,ry2,rz2,gradx1,grady1,gradz1,  &
                                gradx2,grady2,gradz2,pressl,pressr,phi1,phi2,  &
                                ubarl,ubarr,q2l,q2r,q2,c2,                     &
                                laplcc,poweri,behavior)

        if (.not. second) switch = my_0

!       Compute the cell face reynolds number to make the feature detection
!       vaninsh on low Reynolds number cells faces

        if (ivisc >= 2) then
          switchv = vswch_coef_ddt(wat,rho,                                    &
                                   q2l,ubarl,q2r,ubarr,ubar,c,vol1,vol2,area,  &
                                   powerv,mu)
          if (switchv > switch) switch = my_1
        end if

      end if

!     Evaluate the interface interior state based on the switch: (0=HLL, 1=HLLC)

      if (switch == my_1) then

!       Compute the HLLC interior fluxes at interfaces tagged with a 1

!       Contact velocity:

        SM = (rhor*ubarr*(SR-ubarr)-rhol*ubarl*(SL-ubarl)+pressl-pressr)       &
           / (rhor*(SR-ubarr)-rhol*(SL-ubarl))

!       Interior pressure:

        presss = rhol*(ubarl-SL)*(ubarl-SM) + pressl

!       State on left side of contact:

        rhosl    = rhol*(SL-ubarl)                                   / (SL-SM)
        rhousl   = ( (SL-ubarl)*rhol*ul + (presss-pressl)*xnorm )    / (SL-SM)
        rhovsl   = ( (SL-ubarl)*rhol*vl + (presss-pressl)*ynorm )    / (SL-SM)
        rhowsl   = ( (SL-ubarl)*rhol*wl + (presss-pressl)*znorm )    / (SL-SM)
        energysl = ( (SL-ubarl)*energyl - pressl*ubarl + presss*SM ) / (SL-SM)

!       State on right side of contact:

        rhosr    = rhor*(SR-ubarr)                                   / (SR-SM)
        rhousr   = ( (SR-ubarr)*rhor*ur + (presss-pressr)*xnorm )    / (SR-SM)
        rhovsr   = ( (SR-ubarr)*rhor*vr + (presss-pressr)*ynorm )    / (SR-SM)
        rhowsr   = ( (SR-ubarr)*rhor*wr + (presss-pressr)*znorm )    / (SR-SM)
        energysr = ( (SR-ubarr)*energyr - pressr*ubarr + presss*SM ) / (SR-SM)

        if ( my_0 < SM ) then ! use left interior state

          Fhllc1 = FL1 + SL*(rhosl-rhol)
          Fhllc2 = FL2 + SL*(rhousl-rhol*ul)
          Fhllc3 = FL3 + SL*(rhovsl-rhol*vl)
          Fhllc4 = FL4 + SL*(rhowsl-rhol*wl)
          Fhllc5 = FL5 + SL*(energysl-energyl)

        else ! use right interior state

          Fhllc1 = FR1 + SR*(rhosr-rhor)
          Fhllc2 = FR2 + SR*(rhousr-rhor*ur)
          Fhllc3 = FR3 + SR*(rhovsr-rhor*vr)
          Fhllc4 = FR4 + SR*(rhowsr-rhor*wr)
          Fhllc5 = FR5 + SR*(energysr-energyr)

        end if

      else

!       Compute the HLL interior fluxes at interfaces tagged with a 0

        Fhllc1 = (SR*FL1 - SL*FR1 + SL*SR*(rhor-rhol))/(SR-SL)
        Fhllc2 = (SR*FL2 - SL*FR2 + SL*SR*(rhor*ur-rhol*ul))/(SR-SL)
        Fhllc3 = (SR*FL3 - SL*FR3 + SL*SR*(rhor*vr-rhol*vl))/(SR-SL)
        Fhllc4 = (SR*FL4 - SL*FR4 + SL*SR*(rhor*wr-rhol*wl))/(SR-SL)
        Fhllc5 = (SR*FL5 - SL*FR5 + SL*SR*(energyr-energyl))/(SR-SL)

      end if

    end if

!   Compute the contribution to the flux balance

    flux_hllcs_ddt(1) = area*Fhllc1
    flux_hllcs_ddt(2) = area*Fhllc2
    flux_hllcs_ddt(3) = area*Fhllc3
    flux_hllcs_ddt(4) = area*Fhllc4
    flux_hllcs_ddt(5) = area*Fhllc5

    flux_hllcs_ddt(6) = switch

  end function flux_hllcs_ddt

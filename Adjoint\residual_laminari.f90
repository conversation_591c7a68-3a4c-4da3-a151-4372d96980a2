module residual_laminari

  implicit none

  private

  public :: dvisrhs_mix5

contains

!================================= DVISRHS_MIX5 ==============================80
!
! This routine computes the incompressible viscous flux jacobians in general
! (mixed) elements and constructs the adjoint residual (dR/dQ)^T * lambda
! Routine can either store the matrix explicitly or carry out the matrix-vector
! product itself, based on which optional arguments are present in the call
!
! Note: qnode is assumed to contain conserved variables
!
! Linearizes wrt turbulence quantity as well
!
!=============================================================================80
  subroutine dvisrhs_mix5(nnodes0, nnodes01, ncell, c2n, x, y, z, qnode, amut, &
                          local_f2n, local_e2n, chk_norm, local_f2e, e2n_2d,   &
                          face_per_cell, node_per_cell, edge_per_cell,         &
                          type_cell, n_tot, adim, face_2d, n_turb, turb,       &
                          nfunctions, rlam, coltag, res, ia, ja, iau, aa)

    use kinddefs,         only : dp
    use info_depr,        only : re, ivgrd, twod, use_edge_gradients, ivisc
    use element_defs,     only : max_node_per_cell, max_face_per_cell,         &
                                 max_edge_per_cell
    use utilities,        only : cell_jacobians, cell_gradients
    use turb_sa_const,    only : cv1
    use adjoint_switches, only : use_bp_model
    use debug_defs,       only : gradient_construction_rhs

    integer, intent(in) :: n_tot, adim, nnodes0, nnodes01, ncell, face_per_cell
    integer, intent(in) :: node_per_cell, edge_per_cell
    integer, intent(in) :: face_2d, n_turb
    integer, intent(in), optional :: nfunctions

    integer, dimension(node_per_cell,ncell),         intent(in) :: c2n
    integer, dimension(face_per_cell,4),             intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),             intent(in) :: local_e2n
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm
    integer, dimension(face_per_cell,4),             intent(in) :: local_f2e
    integer, dimension(4,2),                         intent(in) :: e2n_2d
    integer, dimension(:),                 optional, intent(in) :: ia, ja, iau

    real(dp), dimension(nnodes01),                    intent(in)    :: x,y,z
    real(dp), dimension(n_tot,nnodes01),              intent(in)    :: qnode
    real(dp), dimension(nnodes01),                    intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01),             intent(in)    :: turb
    real(dp), dimension(:,:,:),             optional, intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),     optional, intent(in)    :: coltag
    real(dp), dimension(:,:,:),             optional, intent(inout) :: aa
    real(dp), dimension(:,:,:),             optional, intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    logical :: edge_gradients

    integer :: n, nodec, j, idiag, ioff, k, column
    integer :: ie, i, ii, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node
    integer :: n1, n2, n3, n4, n5, n6
    integer :: big_angle, ifcn

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: rei, u1, u2, v1, v2, w1, w2
    real(dp) :: dot, disi, areai, xnf, ynf, znf
    real(dp) :: ex, ey, ez, factor
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: egradu, egradv, egradw
    real(dp) :: gradu_xi, gradv_xi, gradw_xi
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz
    real(dp) :: uxavg, uyavg, uzavg, vxavg, vyavg, vzavg
    real(dp) :: wxavg, wyavg, wzavg
    real(dp) :: rmu
    real(dp) :: dtxxdm, dtxxdt
    real(dp) :: dtxydm, dtxydn, dtxydt
    real(dp) :: dtxzdm, dtxzdl, dtxzdt
    real(dp) :: dtyydn, dtyydt
    real(dp) :: dtyzdn, dtyzdl, dtyzdt
    real(dp) :: dtzzdl, dtzzdt
    real(dp) :: rax, ray, raz
    real(dp) :: dgradu_xidm
    real(dp) :: dgradv_xidn
    real(dp) :: dgradw_xidl
    real(dp) :: chi,dchidt
    real(dp) :: fv1,fv1dt,psi,dpsidt,base,dbasedt
    real(dp) :: dmutdt, mu_node

    real(dp), dimension(max_node_per_cell) :: duxavgdm
    real(dp), dimension(max_node_per_cell) :: duyavgdm
    real(dp), dimension(max_node_per_cell) :: duzavgdm
    real(dp), dimension(max_node_per_cell) :: dvxavgdn
    real(dp), dimension(max_node_per_cell) :: dvyavgdn
    real(dp), dimension(max_node_per_cell) :: dvzavgdn
    real(dp), dimension(max_node_per_cell) :: dwxavgdl
    real(dp), dimension(max_node_per_cell) :: dwyavgdl
    real(dp), dimension(max_node_per_cell) :: dwzavgdl
    real(dp), dimension(max_node_per_cell) :: duxdm
    real(dp), dimension(max_node_per_cell) :: duydm
    real(dp), dimension(max_node_per_cell) :: duzdm
    real(dp), dimension(max_node_per_cell) :: dvxdn
    real(dp), dimension(max_node_per_cell) :: dvydn
    real(dp), dimension(max_node_per_cell) :: dvzdn
    real(dp), dimension(max_node_per_cell) :: dwxdl
    real(dp), dimension(max_node_per_cell) :: dwydl
    real(dp), dimension(max_node_per_cell) :: dwzdl
    real(dp), dimension(max_node_per_cell) :: dvf2dm, dvf2dt
    real(dp), dimension(max_node_per_cell) :: dvf2dn, dvf2dl
    real(dp), dimension(max_node_per_cell) :: dvf3dm, dvf3dt
    real(dp), dimension(max_node_per_cell) :: dvf3dn, dvf3dl
    real(dp), dimension(max_node_per_cell) :: dvf4dm, dvf4dt
    real(dp), dimension(max_node_per_cell) :: dvf4dn, dvf4dl
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell) :: dgradz_celldq
    real(dp), dimension(max_face_per_cell) :: nx, ny, nz
    real(dp), dimension(max_node_per_cell) :: turb_node
    real(dp), dimension(max_node_per_cell) :: u_node, v_node, w_node
    real(dp), dimension(3,max_node_per_cell) :: q_node
    real(dp), dimension(3)                 :: gradx_cell, grady_cell
    real(dp), dimension(3)                 :: gradz_cell
    real(dp), dimension(max_node_per_cell) :: drmudt
    real(dp), dimension(4,5)               :: a
    real(dp), dimension(3)                 :: augment_weight

    real(dp), parameter :: my_haf  = 0.50_dp
    real(dp), parameter :: my_mxd  = 0.99939_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_2    = 2.0_dp

    logical :: form_matvec = .false.
    logical :: form_matrix = .false.

  continue

    if ( present(res) ) form_matvec = .true.
    if ( present(aa) )  form_matrix = .true.

    if ( (.not.form_matvec) .and. (.not.form_matrix)) then
      write(*,*) 'WARNING:  dvisrhs_mix5 called but did not request output...'
      return
    endif

    rei = 1.0_dp/re

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    edge_map(:) = 0
    node_map(:) = 0

    if (twod) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      cell_vol = 0.0_dp

      ux = 0._dp
      vx = 0._dp
      wx = 0._dp

      uy = 0._dp
      vy = 0._dp
      wy = 0._dp

      uz = 0._dp
      vz = 0._dp
      wz = 0._dp

      rmu = 0.0_dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

      turb_node(:)= 0._dp
      u_node(:)   = 0._dp
      v_node(:)   = 0._dp
      w_node(:)   = 0._dp
      x_node(:)   = 0._dp
      y_node(:)   = 0._dp
      z_node(:)   = 0._dp
      q_node(:,:) = 0._dp

      duxdm(:) = 0._dp
      duydm(:) = 0._dp
      duzdm(:) = 0._dp

      dvxdn(:) = 0._dp
      dvydn(:) = 0._dp
      dvzdn(:) = 0._dp

      dwxdl(:) = 0._dp
      dwydl(:) = 0._dp
      dwzdl(:) = 0._dp

      duxavgdm(:) = 0._dp
      dvxavgdn(:) = 0._dp
      dwxavgdl(:) = 0._dp

      duyavgdm(:) = 0._dp
      dvyavgdn(:) = 0._dp
      dwyavgdl(:) = 0._dp

      duzavgdm(:) = 0._dp
      dvzavgdn(:) = 0._dp
      dwzavgdl(:) = 0._dp

      drmudt(:) = 0.0_dp

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop_1 : do i_local = 1, nodes_local

        i = node_map(i_local)

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        u_node(i) = qnode(2,node)
        v_node(i) = qnode(3,node)
        w_node(i) = qnode(4,node)
        if ( ivisc > 2 ) turb_node(i) = turb(1,node)
        mu_node    = 1.0_dp + amut(node)

        rmu = rmu + mu_node

      end do node_loop_1

      fact = 1._dp / real(nodes_local, dp)

      rmu = rmu*fact

      do i = 1, node_per_cell

!       global node number

        node = c2n(i,n)

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

      end do

      fact = 1._dp / real(node_per_cell, dp)

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

      if ( ivisc > 2 ) then
        node_loop_2 : do i_local = 1, nodes_local
          i = node_map(i_local)
          chi = turb_node(i)
          dchidt = 1.0_dp
          if ( use_bp_model ) then
            psi = chi
              dpsidt = dchidt
            if ( psi < 10.0_dp ) then
              base = 1.0_dp + exp(20.0_dp*chi)
                dbasedt = exp(20.0_dp*chi)*20.0_dp*dchidt
              psi = 0.05_dp*log(base)
                dpsidt = 0.05_dp/base*dbasedt
            endif
            fv1 = psi**3 / (psi**3 + cv1**3)
              fv1dt = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidt                 &
                       - psi**3*3.0_dp*psi**2*dpsidt ) / (psi**3 + cv1**3)**2
!           mut = max( 0._dp , turb_node(i)*fv1 )
            if ( turb_node(i)*fv1 > 0.0_dp ) then
              dmutdt = turb_node(i)*fv1dt + fv1
            else
              dmutdt = 0.0_dp
            endif
          else
            fv1 = chi**3/(chi**3 + cv1**3)
            fv1dt = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidt                    &
                - chi**3*3.0_dp*chi**2*dchidt)/(chi**3+cv1**3)/(chi**3 + cv1**3)
!           mut = turb_node(i)*fv1
            dmutdt = turb_node(i)*fv1dt + fv1
          endif
          drmudt(i) = dmutdt
        end do node_loop_2
      endif

!     divide the viscosity sum derivatives by the averaging factor

      fact = 1._dp / real(nodes_local, dp)

      drmudt = drmudt*fact

! Get the gradients in the primal cell via Green-Gauss

      q_node(1,:) = u_node(:)
      q_node(2,:) = v_node(:)
      q_node(3,:) = w_node(:)

      call cell_gradients(edges_local, max_node_per_cell,face_per_cell,        &
                          x_node, y_node, z_node, 3, q_node, local_f2n,        &
                          e2n_2d, gradx_cell, grady_cell, gradz_cell,          &
                          cell_vol, nx, ny, nz)

      uxavg = gradx_cell(1)
      vxavg = gradx_cell(2)
      wxavg = gradx_cell(3)

      uyavg = grady_cell(1)
      vyavg = grady_cell(2)
      wyavg = grady_cell(3)

      uzavg = gradz_cell(1)
      vzavg = gradz_cell(2)
      wzavg = gradz_cell(3)

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note: these are with respect to the primitive variables

      call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,       &
                          x_node, y_node, z_node, local_f2n, e2n_2d,           &
                          dgradx_celldq, dgrady_celldq, dgradz_celldq,         &
                          cell_vol, nx, ny, nz)

!     convert to jacobians with respect to conservative variables

      if (twod) then

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          duxavgdm(i) = dgradx_celldq(i)
          dwxavgdl(i) = dgradx_celldq(i)

          duzavgdm(i) = dgradz_celldq(i)
          dwzavgdl(i) = dgradz_celldq(i)

        end do

      else

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          duxavgdm(i) = dgradx_celldq(i)
          dvxavgdn(i) = dgradx_celldq(i)
          dwxavgdl(i) = dgradx_celldq(i)

          duyavgdm(i) = dgrady_celldq(i)
          dvyavgdn(i) = dgrady_celldq(i)
          dwyavgdl(i) = dgrady_celldq(i)

          duzavgdm(i) = dgradz_celldq(i)
          dvzavgdn(i) = dgradz_celldq(i)
          dwzavgdl(i) = dgradz_celldq(i)

        end do

      end if

! Check cell-face angles (dot product) - if greater that a specified value,
! we skip the contribution from this cell (currently for 3D cases only)

!     note: only need to check upper part of matrix chk_norm since
!     dot products commute (A*B = B*A) and the diagonal indicates
!     the dot product of a face with itself; also note that nx, ny, nz
!     are not unit normals so we must scale the dot product accordingly

      if (ivgrd == 1 .and. .not.(twod)) then

        big_angle = 0

        do i=1,face_per_cell

          do j=i+1,face_per_cell

            if (chk_norm(i,j) > 0) then
              dot = nx(i)*nx(j) + ny(i)*ny(j) + nz(i)*nz(j)
!             scale to unit normals
              dot = dot / sqrt(nx(i)*nx(i) + ny(i)*ny(i) + nz(i)*nz(i))
              dot = dot / sqrt(nx(j)*nx(j) + ny(j)*ny(j) + nz(j)*nz(j))
              if (dot >= my_mxd) big_angle = big_angle + 1
            end if

          end do

        end do

        if ( big_angle > 0 )  cycle cell_loop

      end if

! Next loop over the edges in the cell and get each ones'
! contribution to the jacobian

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

! Get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x(n1) + x(n2))/2._dp
        ym = (y(n1) + y(n2))/2._dp
        zm = (z(n1) + z(n2))/2._dp

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))/4._dp
          yl = (y(n1) + y(n2) + y(n3) + y(n4))/4._dp
          zl = (z(n1) + z(n2) + z(n3) + z(n4))/4._dp

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))/3._dp
          yl = (y(n1) + y(n2) + y(n3))/3._dp
          zl = (z(n1) + z(n2) + z(n3))/3._dp

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))/4._dp
          yr = (y(n1) + y(n2) + y(n5) + y(n6))/4._dp
          zr = (z(n1) + z(n2) + z(n5) + z(n6))/4._dp

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))/3._dp
          yr = (y(n1) + y(n2) + y(n5))/3._dp
          zr = (z(n1) + z(n2) + z(n5))/3._dp

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_haf
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_haf
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_haf

! Get (jacobians of) gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity on
! non-simplicial meshes.

! For tets in 3D or prisms in 2D, edge gradients add no new info
! so there is no need to do the extra work

        edge_gradients = use_edge_gradients

        if (type_cell == 'tet') edge_gradients = .false.
        if (twod .and. type_cell == 'prz') edge_gradients = .false.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = 1._dp/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf   = areax*areai
          ynf   = areay*areai
          znf   = areaz*areai

          augment_weight = edge_augment_weight( gradient_construction_rhs, &
                                                ex, ey, ez, xnf, ynf, znf )

!         primitive variables at nodes 1 and 2

          u1 = qnode(2,n1)
          v1 = qnode(3,n1)
          w1 = qnode(4,n1)

          u2 = qnode(2,n2)
          v2 = qnode(3,n2)
          w2 = qnode(4,n2)

!           directional gradients along edge

            egradu = ( u2 - u1 )*disi
            egradv = ( v2 - v1 )*disi
            egradw = ( w2 - w1 )*disi

!           average Green-Gauss gradient in edge direction

            gradu_xi = uxavg*ex + uyavg*ey + uzavg*ez
            gradv_xi = vxavg*ex + vyavg*ey + vzavg*ez
            gradw_xi = wxavg*ex + wyavg*ey + wzavg*ez

!           combine gradient contributions from edge and primal cell

            ux = uxavg + ( egradu - gradu_xi )*augment_weight(1)
            uy = uyavg + ( egradu - gradu_xi )*augment_weight(2)
            uz = uzavg + ( egradu - gradu_xi )*augment_weight(3)

            vx = vxavg + ( egradv - gradv_xi )*augment_weight(1)
            vy = vyavg + ( egradv - gradv_xi )*augment_weight(2)
            vz = vzavg + ( egradv - gradv_xi )*augment_weight(3)

            wx = wxavg + ( egradw - gradw_xi )*augment_weight(1)
            wy = wyavg + ( egradw - gradw_xi )*augment_weight(2)
            wz = wzavg + ( egradw - gradw_xi )*augment_weight(3)

!           write the above gradients as
!           ux = uxavg - (uxavg*ex + uyavg*ey + uzavg*ez)*ex + (u2-u1)*disi*ex
!                <-----------------avg_term---------------->   <--edge_term-->
!           etc.

!           first get the avg_term pieces; all active cell nodes contribute

            do i_local = 1, nodes_local

!             local node number

              i = node_map(i_local)

!             u-gradient avg terms

              dgradu_xidm = duxavgdm(i)*ex + duyavgdm(i)*ey +  duzavgdm(i)*ez

              duxdm(i) = duxavgdm(i) - dgradu_xidm*augment_weight(1)
              duydm(i) = duyavgdm(i) - dgradu_xidm*augment_weight(2)
              duzdm(i) = duzavgdm(i) - dgradu_xidm*augment_weight(3)

!             v-gradient avg terms

              dgradv_xidn = dvxavgdn(i)*ex + dvyavgdn(i)*ey +  dvzavgdn(i)*ez

              dvxdn(i) = dvxavgdn(i) - dgradv_xidn*augment_weight(1)
              dvydn(i) = dvyavgdn(i) - dgradv_xidn*augment_weight(2)
              dvzdn(i) = dvzavgdn(i) - dgradv_xidn*augment_weight(3)

!             w-gradient avg terms

              dgradw_xidl = dwxavgdl(i)*ex + dwyavgdl(i)*ey +  dwzavgdl(i)*ez

              dwxdl(i) = dwxavgdl(i) - dgradw_xidl*augment_weight(1)
              dwydl(i) = dwyavgdl(i) - dgradw_xidl*augment_weight(2)
              dwzdl(i) = dwzavgdl(i) - dgradw_xidl*augment_weight(3)

            end do

!           next get the edge_term pieces; only the two edge nodes contribute
!           u-gradient edge terms

            duxdm(n1_loc) = duxdm(n1_loc) - disi*augment_weight(1)
            duxdm(n2_loc) = duxdm(n2_loc) + disi*augment_weight(1)
            duydm(n1_loc) = duydm(n1_loc) - disi*augment_weight(2)
            duydm(n2_loc) = duydm(n2_loc) + disi*augment_weight(2)
            duzdm(n1_loc) = duzdm(n1_loc) - disi*augment_weight(3)
            duzdm(n2_loc) = duzdm(n2_loc) + disi*augment_weight(3)

!           v-gradient edge terms

            dvxdn(n1_loc) = dvxdn(n1_loc) - disi*augment_weight(1)
            dvxdn(n2_loc) = dvxdn(n2_loc) + disi*augment_weight(1)
            dvydn(n1_loc) = dvydn(n1_loc) - disi*augment_weight(2)
            dvydn(n2_loc) = dvydn(n2_loc) + disi*augment_weight(2)
            dvzdn(n1_loc) = dvzdn(n1_loc) - disi*augment_weight(3)
            dvzdn(n2_loc) = dvzdn(n2_loc) + disi*augment_weight(3)

!           w-gradient edge terms

            dwxdl(n1_loc) = dwxdl(n1_loc) - disi*augment_weight(1)
            dwxdl(n2_loc) = dwxdl(n2_loc) + disi*augment_weight(1)
            dwydl(n1_loc) = dwydl(n1_loc) - disi*augment_weight(2)
            dwydl(n2_loc) = dwydl(n2_loc) + disi*augment_weight(2)
            dwzdl(n1_loc) = dwzdl(n1_loc) - disi*augment_weight(3)
            dwzdl(n2_loc) = dwzdl(n2_loc) + disi*augment_weight(3)

        else include_edge_gradients

!         just use Green-Gauss cell-average gradients (this
!         is what the baseline code does for tets)

          ux = uxavg
          uy = uyavg
          uz = uzavg

          vx = vxavg
          vy = vyavg
          vz = vzavg

          wx = wxavg
          wy = wyavg
          wz = wzavg

!         only have the unaltered, average green-gauss contributions;
!         all active cell nodes contribute

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           u-gradient avg terms

            duxdm(i) = duxavgdm(i)
            duydm(i) = duyavgdm(i)
            duzdm(i) = duzavgdm(i)

!           v-gradient avg terms

            dvxdn(i) = dvxavgdn(i)
            dvydn(i) = dvyavgdn(i)
            dvzdn(i) = dvzavgdn(i)

!           w-gradient avg terms

            dwxdl(i) = dwxavgdl(i)
            dwydl(i) = dwyavgdl(i)
            dwzdl(i) = dwzavgdl(i)

          end do

        end if include_edge_gradients

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       [nondimensionalization factor rei ] * [ viscosity ]
!       [ unit normal and area ] at dual face

        rax = rei*areax
        ray = rei*areay
        raz = rei*areaz

!       form some intermediate Jacobians at all nodes

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

! Stress pieces

          dtxxdm = rmu*my_2*duxdm(i)

          dtxydm = rmu*duydm(i)
          dtxydn = rmu*dvxdn(i)

          dtxzdm = rmu*duzdm(i)
          dtxzdl = rmu*dwxdl(i)

          dtyydn = rmu*my_2*dvydn(i)

          dtyzdn = rmu*dvzdn(i)
          dtyzdl = rmu*dwydl(i)

          dtzzdl = rmu*my_2*dwzdl(i)

! Add stress contributions

          dvf2dm(i) = rax*dtxxdm + ray*dtxydm + raz*dtxzdm
          dvf2dn(i) =              ray*dtxydn
          dvf2dl(i) =                           raz*dtxzdl
          dvf2dt(i) = 0.0_dp

          dvf3dm(i) = rax*dtxydm
          dvf3dn(i) = rax*dtxydn + ray*dtyydn + raz*dtyzdn
          dvf3dl(i) =                           raz*dtyzdl
          dvf3dt(i) = 0.0_dp

          dvf4dm(i) = rax*dtxzdm
          dvf4dn(i) =              ray*dtyzdn
          dvf4dl(i) = rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
          dvf4dt(i) = 0.0_dp

! Viscosity pieces

          dtxxdt = drmudt(i)*my_2*ux

          dtxydt = drmudt(i)*(uy + vx)

          dtxzdt = drmudt(i)*(uz + wx)

          dtyydt = drmudt(i)*my_2*vy

          dtyzdt = drmudt(i)*(vz + wy)

          dtzzdt = drmudt(i)*my_2*wz

! Add viscosity contributions

          dvf2dt(i) = dvf2dt(i) + rax*dtxxdt + ray*dtxydt + raz*dtxzdt

          dvf3dt(i) = dvf3dt(i) + rax*dtxydt + ray*dtyydt + raz*dtyzdt

          dvf4dt(i) = dvf4dt(i) + rax*dtxzdt + ray*dtyzdt + raz*dtzzdt

        end do

! Assemble final Jacobian matrices into sparse matrix form

        factor = +my_1

        edge_node_loop : do ii = 1,2

!         diagonal contributions

!         local (i) and global (node) numbers

          if (ii == 1) then
            i = n1_loc
            node = c2n(n1_loc,n)
          else
            i = n2_loc
            node = c2n(n2_loc,n)
          end if

          factor = -my_1*factor

          local_contrib1 : if ( node <= nnodes0 ) then

            a(2,2) = factor*dvf2dm(i)
            a(2,3) = factor*dvf2dn(i)
            a(2,4) = factor*dvf2dl(i)
            a(2,5) = factor*dvf2dt(i)

            a(3,2) = factor*dvf3dm(i)
            a(3,3) = factor*dvf3dn(i)
            a(3,4) = factor*dvf3dl(i)
            a(3,5) = factor*dvf3dt(i)

            a(4,2) = factor*dvf4dm(i)
            a(4,3) = factor*dvf4dn(i)
            a(4,4) = factor*dvf4dl(i)
            a(4,5) = factor*dvf4dt(i)

            if ( form_matrix ) then

! Add contributions to stored adjoint matrix

              idiag = iau(node)

              aa(2,2,idiag) = aa(2,2,idiag) + a(2,2)
              aa(2,3,idiag) = aa(2,3,idiag) + a(2,3)
              aa(2,4,idiag) = aa(2,4,idiag) + a(2,4)
              if ( ivisc > 2 ) aa(2,5,idiag) = aa(2,5,idiag) + a(2,5)

              aa(3,2,idiag) = aa(3,2,idiag) + a(3,2)
              aa(3,3,idiag) = aa(3,3,idiag) + a(3,3)
              aa(3,4,idiag) = aa(3,4,idiag) + a(3,4)
              if ( ivisc > 2 ) aa(3,5,idiag) = aa(3,5,idiag) + a(3,5)

              aa(4,2,idiag) = aa(4,2,idiag) + a(4,2)
              aa(4,3,idiag) = aa(4,3,idiag) + a(4,3)
              aa(4,4,idiag) = aa(4,4,idiag) + a(4,4)
              if ( ivisc > 2 ) aa(4,5,idiag) = aa(4,5,idiag) + a(4,5)

            endif

            if ( form_matvec ) then

! Add contributions to adjoint residual

              do ifcn = 1, nfunctions
                res(2,node,ifcn) = res(2,node,ifcn)                            &
                                 + a(2,2)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,2)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,2)*coltag(4,node)*rlam(4,node,ifcn)

                res(3,node,ifcn) = res(3,node,ifcn)                            &
                                 + a(2,3)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,3)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,3)*coltag(4,node)*rlam(4,node,ifcn)

                res(4,node,ifcn) = res(4,node,ifcn)                            &
                                 + a(2,4)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,4)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,4)*coltag(4,node)*rlam(4,node,ifcn)

                if ( ivisc > 2 ) then
                  res(5,node,ifcn) = res(5,node,ifcn)                          &
                                   + a(2,5)*coltag(2,node)*rlam(2,node,ifcn)   &
                                   + a(3,5)*coltag(3,node)*rlam(3,node,ifcn)   &
                                   + a(4,5)*coltag(4,node)*rlam(4,node,ifcn)
                endif
              end do
            endif

          end if local_contrib1

!         off-diagonal contributions
!         (place into transposed block locations)

          node_loop_4 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n(i,n)

            if (nodec == node) cycle node_loop_4   ! already did diagonal

            if ( nodec <= nnodes0 ) then

              a(2,2) = factor*dvf2dm(i)
              a(2,3) = factor*dvf2dn(i)
              a(2,4) = factor*dvf2dl(i)
              a(2,5) = factor*dvf2dt(i)

              a(3,2) = factor*dvf3dm(i)
              a(3,3) = factor*dvf3dn(i)
              a(3,4) = factor*dvf3dl(i)
              a(3,5) = factor*dvf3dt(i)

              a(4,2) = factor*dvf4dm(i)
              a(4,3) = factor*dvf4dn(i)
              a(4,4) = factor*dvf4dl(i)
              a(4,5) = factor*dvf4dt(i)

              if ( form_matrix ) then

! Determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == node) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                             node,' to the off diagonal of node ',nodec
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                aa(2,2,ioff) = aa(2,2,ioff) + a(2,2)
                aa(2,3,ioff) = aa(2,3,ioff) + a(2,3)
                aa(2,4,ioff) = aa(2,4,ioff) + a(2,4)
                if ( ivisc > 2 ) aa(2,5,ioff) = aa(2,5,ioff) + a(2,5)

                aa(3,2,ioff) = aa(3,2,ioff) + a(3,2)
                aa(3,3,ioff) = aa(3,3,ioff) + a(3,3)
                aa(3,4,ioff) = aa(3,4,ioff) + a(3,4)
                if ( ivisc > 2 ) aa(3,5,ioff) = aa(3,5,ioff) + a(3,5)

                aa(4,2,ioff) = aa(4,2,ioff) + a(4,2)
                aa(4,3,ioff) = aa(4,3,ioff) + a(4,3)
                aa(4,4,ioff) = aa(4,4,ioff) + a(4,4)
                if ( ivisc > 2 ) aa(4,5,ioff) = aa(4,5,ioff) + a(4,5)

              endif

              if ( form_matvec ) then

! Add contributions to adjoint residual

                do ifcn = 1, nfunctions
                  res(2,nodec,ifcn) = res(2,nodec,ifcn)                        &
                                    + a(2,2)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,2)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,2)*coltag(4,node)*rlam(4,node,ifcn)

                  res(3,nodec,ifcn) = res(3,nodec,ifcn)                        &
                                    + a(2,3)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,3)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,3)*coltag(4,node)*rlam(4,node,ifcn)

                  res(4,nodec,ifcn) = res(4,nodec,ifcn)                        &
                                    + a(2,4)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,4)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,4)*coltag(4,node)*rlam(4,node,ifcn)

                  if ( ivisc > 2 ) then
                    res(5,nodec,ifcn) = res(5,nodec,ifcn)                      &
                                      + a(2,5)*coltag(2,node)*rlam(2,node,ifcn)&
                                      + a(3,5)*coltag(3,node)*rlam(3,node,ifcn)&
                                      + a(4,5)*coltag(4,node)*rlam(4,node,ifcn)
                  endif
                end do
              endif

            end if

          end do node_loop_4

        end do edge_node_loop

      end do edge_loop

    end do cell_loop

  end subroutine dvisrhs_mix5

  include 'edge_augment_weight.f90'

end module residual_laminari

!=================================== SMTHLMV =================================80
!
! Smooth (differentiable) flux limiter applied to a vector
! This is the CFL3D differentiable limiter from AIAA-90-0429 (1990)
!
!=============================================================================80

  pure function smthlmv(grad_a, grad_b, eps2, ndim)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_2, my_3

    integer,                   intent(in) :: ndim
    real(dp),                  intent(in) :: eps2
    real(dp), dimension(ndim), intent(in) :: grad_a, grad_b
    real(dp), dimension(ndim)             :: smthlmv

  continue

! From AIAA-90-0429 (1990)

    smthlmv = (grad_b*(grad_a**2+my_2*eps2) + grad_a*(my_2*grad_b**2+eps2)) /  &
              (my_2*grad_b**2 - grad_b*grad_a + my_2*grad_a**2 + my_3*eps2)

  end function smthlmv

#ifndef ENGINE_LIBRARY_H
#define ENGINE_LIBRARY_H

#include <string>
#include "rtwtypes.h"
#include "engine_app.h"
#include <dlfcn.h>
#include <stdio.h>

class EngineLibrary{

    public:
        EngineLibrary(std::string libraryName);
        ~EngineLibrary();
        void (*model_initialize)(boolean_T);
        void (*model_step)(void);
        void (*model_terminate)(void);
        ExtU_lib_engine_T (*model_in);
        ExtY_lib_engine_T (*model_out);
    private:
        void *handleLib;

        void openLibrary(std::string libraryName);
        void setFunctionPointers();
};

#endif

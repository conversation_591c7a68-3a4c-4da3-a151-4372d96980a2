module timeacc

  use kinddefs,             only : dp

  implicit none

  private

  public :: add_time_terms, ramp_cfl
  public :: subit_resids, subit_resids_turb
  public :: open_subit_resids
  public :: write_subit_resids
  public :: update_temporal_backplanes, init_grid_backplanes
  public :: comp_qtimeavg
  public :: new_timestep, new_backwards_timestep
  public :: savetatn
  public :: savevolatn
  public :: savexyzatn
  public :: savetransformatn
  public :: time_diag_nc, time_diag_cc, set_dtau
  public :: temporal_err_flow
  public :: temporal_err_turb
  public :: sumtot, sumtot_turb, terr_flow_rmsg, terr_turb_rmsg
  public :: temporal_backplanes, timestep_scalars
  public :: stage_value_predictor
  public :: moving_vortex, moving_vortex_err
  public :: moving_vortex_data, moving_vortex_datai
  public :: set_simulation_time, stage_alloc
  public :: pseudo_sub

  real(dp), dimension(:), pointer, save :: sumi, sumi_turb
  real(dp), dimension(:), pointer, save :: sumtot,sumtot_turb
  real(dp), dimension(:), pointer, save :: terr_flow_rmsl
  real(dp), dimension(:), pointer, save :: terr_flow_rmsg
  real(dp), dimension(:), pointer, save :: werr_vortex_rmsl
  real(dp), dimension(:), pointer, save :: werr_vortex_rmsg
  real(dp), dimension(:), pointer, save :: err_vortex_rmsl
  real(dp), dimension(:), pointer, save :: err_vortex_rmsg
  real(dp), dimension(:), pointer, save :: err_vortex_maxl
  real(dp), dimension(:), pointer, save :: err_vortex_maxg
  real(dp), dimension(:), pointer, save :: terr_turb_rmsl
  real(dp), dimension(:), pointer, save :: terr_turb_rmsg

  integer :: iunit_subit    ! unit number for output
  integer :: stage_alloc    ! maximum number of stages for allocate
  integer :: pseudo_sub = 1 !  subiteration counter

  logical :: moving_vortex = .false.  ! run moving vortex case

contains

!=============================== SET_SIMULATION_TIME =========================80
!
! Set/Rest simulation (nondimensional) time
!
!=============================================================================80

  subroutine set_simulation_time(simulation_time_atn)

    use info_depr,            only : simulation_time
    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : dt_vec, t_stage
    use lmpi,                 only : lmpi_master
    use system_extensions,    only : se_flush

    real(dp), optional, intent(in) :: simulation_time_atn

    real(dp) :: starting_simulation_time, reset_time

    continue

    if ( .not.(present(simulation_time_atn)) ) then

!     Initialize simulation time

      starting_simulation_time = simulation_time
      reset_time               = simulation_time

      if ( simulation_time <= 0._dp .and. itime /= 0 ) reset_time = 0.0_dp

!     Reset the simulation time if appropriate

!     use this to restart from old restart files
!     reset_time = prior_iters*dt

      if (abs(reset_time - starting_simulation_time) > epsilon(1._dp)) then

        if ( lmpi_master ) then
          write(*,*)
          write(*,'(a,e20.12)') ' starting simulation time before resetting ', &
                     starting_simulation_time
        end if

        starting_simulation_time = reset_time
        simulation_time          = reset_time

        if ( lmpi_master ) then
          write(*,'(a,e20.12)') ' re-setting starting simulation time to    ', &
                     starting_simulation_time
          write(*,*)
          call se_flush(6)
        end if

      end if

    else

!     Advance simulation time by dt for time accurate cases
!     (or partial steps for multistage schemes)

      if ( itime /= 0 ) then
        if (itime <= 3)  simulation_time = simulation_time_atn + dt_vec(2)
        if (itime == 4) then
          if (t_stage == 1 ) simulation_time = simulation_time_atn + dt_vec(2)
          if (t_stage == 2 ) simulation_time = simulation_time_atn + dt_vec(2) &
                                                                   + dt_vec(1)
          if (t_stage == 3 ) simulation_time = simulation_time_atn + dt_vec(2)
        endif
!       if (itime == 5)  simulation_time = simulation_time_atn                 &
!                                        + dt_vec(2) * c(t_stage+1)
      end if

    end if

  end subroutine set_simulation_time

!=================================== NEW_TIMESTEP ============================80
!
! Compute new timestep and shuffle backplane dt's
!
!=============================================================================80

  subroutine new_timestep()

    use nml_nonlinear_solves, only : dt
    use timeacc_coeffs,       only : dt_vec

  continue

    dt_vec(4) = dt_vec(3)
    dt_vec(3) = dt_vec(2)
    dt_vec(2) = dt
    dt_vec(1) = dt

  end subroutine new_timestep

!=========================== NEW_BACKWARDS_TIMESTEP ==========================80
!
! Compute old timestep and shuffle backplane dt's
!
!=============================================================================80

  subroutine new_backwards_timestep()

    use nml_nonlinear_solves, only : dt
    use timeacc_coeffs,       only : dt_vec

  continue

    dt_vec(4) = dt_vec(3)
    dt_vec(3) = dt_vec(2)
    dt_vec(2) = dt
    dt_vec(1) = dt

  end subroutine new_backwards_timestep

!============================ UPDATE_TEMPORAL_BACKPLANES =====================80
!
! Driver routine to store and shuffle temporal backplane data
!
!=============================================================================80

  subroutine update_temporal_backplanes( soln, grid, soln_only, grid_only )

    use solution_types, only : soln_type, generic_gas
    use grid_types,     only : grid_type
    use info_depr,      only : skeleton
    use nml_global,     only : moving_grid

    type(grid_type),           intent(inout) :: grid
    type(soln_type),           intent(inout) :: soln
    logical,         optional, intent(in)    :: grid_only, soln_only

    logical :: store_grid, store_soln

    continue

    store_grid = .true.
    store_soln = .true.

    if ( (present(soln_only)) .and. (.not.present(grid_only)) ) then
      store_grid = .false.
    end if
    if ( (present(grid_only)) .and. (.not.present(soln_only)) ) then
      store_soln = .false.
    end if

    if (.not. moving_grid) store_grid = .false.

    if (store_grid) then

      call savexyzatn(grid%nnodes01, grid%x,     grid%xatn,                    &
                      grid%xatn1,    grid%xatn2, grid%xatn3,                   &
                      grid%xatn4,    grid%y,     grid%yatn,                    &
                      grid%yatn1,    grid%yatn2, grid%yatn3,                   &
                      grid%yatn4,    grid%z,     grid%zatn,                    &
                      grid%zatn1,    grid%zatn2, grid%zatn3,                   &
                      grid%zatn4)

      if (trim(grid%grid_motion) /= 'static') then

        call savevolatn(grid%nnodes01, grid%vol,     grid%volatn,              &
                        grid%volatn1,  grid%volatn2, grid%volatn3,             &
                        grid%volatn4)

      end if

      if (.not. store_soln) return

    end if

    if (store_soln) then

      if (skeleton > 0) write(*,*) 'Call saveqatn.'
      call saveqatn(soln%n_tot,                                                &
                    grid%nnodes01, soln%q_dof, soln%qatn, soln%qatn1,          &
                    soln%qatn2,    soln%qatn3, soln%qatn4)

      if ( ( soln%n_turb > 0 ) .and. ( soln%eqn_set /= generic_gas ) ) then
        if (skeleton > 0) write(*,*) 'Call savetatn.'
        call savetatn(grid%nnodes01, soln%turb,     soln%turbatn,              &
                      soln%turbatn1, soln%turbatn2, soln%turbatn3,             &
                      soln%turbatn4, soln%n_turb)
      end if

    end if

  end subroutine update_temporal_backplanes

!================================ SAVEQATN ===================================80
!
! Save qnode at time level n
!
!=============================================================================80

  subroutine saveqatn( n_tot, nnodes01, qnode, qatn, qatn1, qatn2, qatn3, qatn4)

    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : t_stage, back_planes

    integer,                             intent(in)    :: nnodes01, n_tot
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(n_tot,nnodes01), intent(inout) :: qatn
    real(dp), dimension(n_tot,nnodes01), intent(inout) :: qatn1
    real(dp), dimension(n_tot,nnodes01), intent(inout) :: qatn2
    real(dp), dimension(n_tot,nnodes01), intent(inout) :: qatn3
    real(dp), dimension(n_tot,nnodes01), intent(inout) :: qatn4

    continue

!  Here is the shuffle strategy for MEBDF4
!
!       || Stage | Stage |  T Stage ||  T Stage
!       ||       |       |  m       ||  m
!       ||       |       |  p       ||  p
!       ---------------------------------------
!       ||   1   |   2   |      3   ||      1
!       ||       |       |          ||
!  n+2  ||       |   Q  -->(Q3)    --->(Q3) Q
!       ||       |       |          ||
!  n+1  ||   Q  -->  Qn -->(Q4) Q  ------>  Qn
!       ||       |       |          ||
!  n    ||   Qn -->  Q1 ----->  Qn ------>  Q1
!       ||       |       |          ||
!  n-1  ||   Q1 -->  Q2 ----->  Q1 ------>  Q2
!       ||       |       |          ||
!  n-2  ||   Q2 -->  Q3 ----->  Q2  ||

    if ( itime <= 3 .or. (itime == 4 .and. back_planes <= 2) ) then

       if( itime >= 2 ) qatn2(:,:) = qatn1(:,:)
       if( itime >= 1 ) qatn1(:,:) =  qatn(:,:)
       if( itime >= 1 )  qatn(:,:) = qnode(:,:)

    elseif ( itime == 4 .and. back_planes == 3 ) then

      if    ( t_stage == 1 ) then

       qatn2(:,:) = qatn1(:,:)
       qatn1(:,:) =  qatn(:,:)
        qatn(:,:) = qnode(:,:)

      elseif( t_stage == 2 ) then

       qatn3(:,:) = qatn2(:,:)
       qatn2(:,:) = qatn1(:,:)
       qatn1(:,:) =  qatn(:,:)
        qatn(:,:) = qnode(:,:)

      elseif( t_stage == 3 ) then

       qatn4(:,:) =  qatn(:,:)
        qatn(:,:) = qatn1(:,:)
       qatn1(:,:) = qatn2(:,:)
       qatn2(:,:) = qatn3(:,:)
       qatn3(:,:) = qnode(:,:)

      endif

    endif

  end subroutine saveqatn

!================================ SAVETATN ===================================80
!
! Save turbulence quantity at time level n
!
!=============================================================================80

  subroutine savetatn( nnodes01, turb, turbatn, turbatn1, turbatn2, turbatn3,  &
                       turbatn4, n_turb )

    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : t_stage, back_planes

    integer,                              intent(in)    :: nnodes01, n_turb
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turbatn
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turbatn1
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turbatn2
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turbatn3
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turbatn4

    continue

!   lets start shuffling data according to how many planes of data we have
!   See saveqatn for complete description of how the shuffle works

    if( itime <= 3 .or. (itime == 4 .and. back_planes <= 2) ) then

       if( itime >= 2 ) turbatn2(:,:) = turbatn1(:,:)
       if( itime >= 1 ) turbatn1(:,:) =  turbatn(:,:)
       if( itime >= 1 )  turbatn(:,:) =     turb(:,:)

    elseif(itime == 4 .and. back_planes == 3) then

      if    ( t_stage == 1 ) then

       turbatn2(:,:) = turbatn1(:,:)
       turbatn1(:,:) =  turbatn(:,:)
        turbatn(:,:) =     turb(:,:)

      elseif( t_stage == 2 ) then

       turbatn3(:,:) = turbatn2(:,:)
       turbatn2(:,:) = turbatn1(:,:)
       turbatn1(:,:) =  turbatn(:,:)
        turbatn(:,:) =     turb(:,:)

      elseif( t_stage == 3 ) then

       turbatn4(:,:) =  turbatn(:,:)
        turbatn(:,:) = turbatn1(:,:)
       turbatn1(:,:) = turbatn2(:,:)
       turbatn2(:,:) = turbatn3(:,:)
       turbatn3(:,:) =     turb(:,:)

      endif

    endif

  end subroutine savetatn

!================================ SAVEVOLATN =================================80
!
! Save volume quantity at time level n
!
!=============================================================================80

  subroutine savevolatn( nnodes01, vol, volatn, volatn1, volatn2, volatn3,     &
                         volatn4 )

    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : t_stage, back_planes

    integer,                       intent(in)    :: nnodes01
    real(dp), dimension(nnodes01), intent(in)    :: vol
    real(dp), dimension(nnodes01), intent(inout) :: volatn
    real(dp), dimension(nnodes01), intent(inout) :: volatn1
    real(dp), dimension(nnodes01), intent(inout) :: volatn2
    real(dp), dimension(nnodes01), intent(inout) :: volatn3
    real(dp), dimension(nnodes01), intent(inout) :: volatn4

    continue

!   lets start shuffling data according to how many planes of data we have

    if ( itime <= 3 .or. (itime == 4 .and. back_planes <= 2) ) then

       if ( itime >= 2 ) volatn2(:) = volatn1(:)
       if ( itime >= 1 ) volatn1(:) =  volatn(:)
       if ( itime >= 1 )  volatn(:) =     vol(:)

    elseif ( itime == 4 .and. back_planes == 3 ) then

      if    ( t_stage == 1 ) then

       volatn2(:) = volatn1(:)
       volatn1(:) =  volatn(:)
        volatn(:) =     vol(:)

      elseif( t_stage == 2 ) then

       volatn3(:) = volatn2(:)
       volatn2(:) = volatn1(:)
       volatn1(:) =  volatn(:)
        volatn(:) =     vol(:)

      elseif( t_stage == 3 ) then

       volatn4(:) =  volatn(:)
        volatn(:) = volatn1(:)
       volatn1(:) = volatn2(:)
       volatn2(:) = volatn3(:)
       volatn3(:) =     vol(:)

      endif

    endif

  end subroutine savevolatn

!================================ SAVEXYZATN =================================80
!
! Save x,y,z at time level n
!
!=============================================================================80

  subroutine savexyzatn( nnodes01, x, xatn, xatn1, xatn2, xatn3, xatn4,        &
                                   y, yatn, yatn1, yatn2, yatn3, yatn4,        &
                                   z, zatn, zatn1, zatn2, zatn3, zatn4 )

    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : t_stage, back_planes

    integer,                       intent(in)    :: nnodes01
    real(dp), dimension(nnodes01), intent(in)    :: x,y,z
    real(dp), dimension(nnodes01), intent(inout) :: xatn,yatn,zatn
    real(dp), dimension(nnodes01), intent(inout) :: xatn1,yatn1,zatn1
    real(dp), dimension(nnodes01), intent(inout) :: xatn2,yatn2,zatn2
    real(dp), dimension(nnodes01), intent(inout) :: xatn3,yatn3,zatn3
    real(dp), dimension(nnodes01), intent(inout) :: xatn4,yatn4,zatn4

    continue

!   lets start shuffling data according to how many planes of data we have

    if( itime <= 3 .or. (itime == 4 .and. back_planes <= 2) ) then

       if( itime >= 2 ) then
         xatn2(:) = xatn1(:)
         yatn2(:) = yatn1(:)
         zatn2(:) = zatn1(:)
       endif

       if( itime >= 1 ) then
         xatn1(:) = xatn(:)
         yatn1(:) = yatn(:)
         zatn1(:) = zatn(:)

         xatn(:)  = x(:)
         yatn(:)  = y(:)
         zatn(:)  = z(:)
       endif

    elseif(itime == 4 .and. back_planes == 3) then

      if    ( t_stage == 1 ) then

         xatn2(:) = xatn1(:)
         yatn2(:) = yatn1(:)
         zatn2(:) = zatn1(:)

         xatn1(:) = xatn(:)
         yatn1(:) = yatn(:)
         zatn1(:) = zatn(:)

         xatn(:)  = x(:)
         yatn(:)  = y(:)
         zatn(:)  = z(:)

      elseif( t_stage == 2 ) then

         xatn3(:) = xatn2(:)
         yatn3(:) = yatn2(:)
         zatn3(:) = zatn2(:)

         xatn2(:) = xatn1(:)
         yatn2(:) = yatn1(:)
         zatn2(:) = zatn1(:)

         xatn1(:) = xatn(:)
         yatn1(:) = yatn(:)
         zatn1(:) = zatn(:)

         xatn(:)  = x(:)
         yatn(:)  = y(:)
         zatn(:)  = z(:)

      elseif( t_stage == 3 ) then

         xatn4(:) = xatn(:)
         yatn4(:) = yatn(:)
         zatn4(:) = zatn(:)

         xatn(:) = xatn1(:)
         yatn(:) = yatn1(:)
         zatn(:) = zatn1(:)

         xatn1(:) = xatn2(:)
         yatn1(:) = yatn2(:)
         zatn1(:) = zatn2(:)

         xatn2(:) = xatn3(:)
         yatn2(:) = yatn3(:)
         zatn2(:) = zatn3(:)

         xatn3(:) = x(:)
         yatn3(:) = y(:)
         zatn3(:) = z(:)

      endif

    endif

  end subroutine savexyzatn

!============================= SAVETRANSFORMATN ==============================80
!
! Save transform matrix at time level n
!
!=============================================================================80

  subroutine savetransformatn( transform_matrix, transform_matrixatn,          &
                               transform_matrixatn1, transform_matrixatn2,     &
                               transform_matrixatn3, transform_matrixatn4 )

    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : t_stage, back_planes

    real(dp), dimension(4,4), intent(in)    :: transform_matrix
    real(dp), dimension(4,4), intent(inout) :: transform_matrixatn
    real(dp), dimension(4,4), intent(inout) :: transform_matrixatn1
    real(dp), dimension(4,4), intent(inout) :: transform_matrixatn2
    real(dp), dimension(4,4), intent(inout) :: transform_matrixatn3
    real(dp), dimension(4,4), intent(inout) :: transform_matrixatn4

    continue

!   lets start shuffling data according to how many planes of data we have

    if ( itime <= 3 .or. (itime == 4 .and. back_planes <= 2) ) then

       if ( itime >= 2 ) transform_matrixatn2(:,:) = transform_matrixatn1(:,:)
       if ( itime >= 1 ) transform_matrixatn1(:,:) =  transform_matrixatn(:,:)
       if ( itime >= 1 )  transform_matrixatn(:,:) =     transform_matrix(:,:)

    elseif (itime == 4 .and. back_planes == 3 ) then

      if    ( t_stage == 1 ) then

       transform_matrixatn2(:,:) = transform_matrixatn1(:,:)
       transform_matrixatn1(:,:) =  transform_matrixatn(:,:)
        transform_matrixatn(:,:) =     transform_matrix(:,:)

      elseif( t_stage == 2 ) then

       transform_matrixatn3(:,:) = transform_matrixatn2(:,:)
       transform_matrixatn2(:,:) = transform_matrixatn1(:,:)
       transform_matrixatn1(:,:) =  transform_matrixatn(:,:)
        transform_matrixatn(:,:) =     transform_matrix(:,:)

      elseif( t_stage == 3 ) then

       transform_matrixatn4(:,:) =  transform_matrixatn(:,:)
        transform_matrixatn(:,:) = transform_matrixatn1(:,:)
       transform_matrixatn1(:,:) = transform_matrixatn2(:,:)
       transform_matrixatn2(:,:) = transform_matrixatn3(:,:)
       transform_matrixatn3(:,:) =     transform_matrix(:,:)

      endif

    endif

  end subroutine savetransformatn

!============================== INIT_GRID_BACKPLANES =========================80
!
! Copies starting grid volume to temporal backplanes
!
!=============================================================================80

  subroutine init_grid_backplanes(grid)

    use grid_types,     only : grid_type

    type(grid_type), intent(inout) :: grid

  continue

   if ( size(grid%volatn) == size(grid%vol) ) then
     grid%volatn(:)  = grid%vol(:)
   end if
   if ( size(grid%volatn1) == size(grid%vol) ) then
     grid%volatn1(:) = grid%vol(:)
   end if
   if ( size(grid%volatn2) == size(grid%vol) ) then
     grid%volatn2(:) = grid%vol(:)
   end if

   if ( size(grid%xatn) == size(grid%x) ) then
     grid%xatn(:)  = grid%x(:)
   end if
   if ( size(grid%xatn1) == size(grid%x) ) then
     grid%xatn1(:) = grid%x(:)
   end if
   if ( size(grid%xatn2) == size(grid%x) ) then
     grid%xatn2(:) = grid%x(:)
   end if

   if ( size(grid%yatn) == size(grid%y) ) then
     grid%yatn(:)  = grid%y(:)
   end if
   if ( size(grid%yatn1) == size(grid%y) ) then
     grid%yatn1(:) = grid%y(:)
   end if
   if ( size(grid%yatn2) == size(grid%y) ) then
     grid%yatn2(:) = grid%y(:)
   end if

   if ( size(grid%zatn) == size(grid%z) ) then
     grid%zatn(:)  = grid%z(:)
   end if
   if ( size(grid%zatn1) == size(grid%z) ) then
     grid%zatn1(:) = grid%z(:)
   end if
   if ( size(grid%zatn2) == size(grid%z) ) then
     grid%zatn2(:) = grid%z(:)
   end if

  end subroutine init_grid_backplanes

!================================ ADD_TIME_TERMS =============================80
!
! Driver routine to add time terms to the residuals
!
!=============================================================================80

  subroutine add_time_terms( grid, soln, turb_only )

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use info_depr,      only : tightly_couple, skeleton
    use debug_defs,     only : composite_jacobian_lhs

    type(grid_type),           intent(in)    :: grid
    type(soln_type),           intent(inout) :: soln
    logical,         optional, intent(in)    :: turb_only

    continue

!   don't add time terms if checking jacobians

    if (composite_jacobian_lhs) return

    if (.not.(present(turb_only))) then

      if (skeleton > 0) write(*,*) 'calling add'

      call adddt(soln%eqn_set, grid%nnodes0, grid%nnodes01,    grid%vol,       &
                 grid%volatn1, grid%volatn2,     grid%volatn3,                 &
                 grid%volatn4, soln%q_dof,       soln%qatn,                    &
                 soln%qatn1,   soln%qatn2,       soln%qatn3,                   &
                 soln%qatn4,   soln%res,                                       &
                 grid%res_gcl, grid%grid_motion,                               &
                 soln%ndim,    soln%n_tot,       soln%njac)

      if (tightly_couple) then

        if (skeleton > 0) write(*,*) 'calling add_turb (tightly coupled)'

        call adddt_turb(grid%nnodes0,  grid%nnodes01,                          &
                        grid%vol,      grid%volatn1,                           &
                        grid%volatn2,  grid%volatn3,                           &
                        grid%volatn4,  soln%turb,                              &
                        soln%turbatn,  soln%turbatn1,                          &
                        soln%turbatn2, soln%turbatn3,                          &
                        soln%turbatn4, soln%res(soln%ndim+1:soln%njac,:),      &
                        grid%res_gcl,  grid%grid_motion,                       &
                        soln%n_turb)

      end if

    else

      if (skeleton > 0) write(*,*) 'calling adddt_turb (loosely coupled)'

      call adddt_turb(grid%nnodes0,  grid%nnodes01, grid%vol,                  &
                      grid%volatn1,  grid%volatn2,  grid%volatn3,              &
                      grid%volatn4,  soln%turb,                                &
                      soln%turbatn,  soln%turbatn1, soln%turbatn2,             &
                      soln%turbatn3, soln%turbatn4,                            &
                      soln%turbres,                                            &
                      grid%res_gcl,  grid%grid_motion,                         &
                      soln%n_turb)

    end if

  end subroutine add_time_terms

!================================== ADDDT ====================================80
!
!  Add time term to residual if doing time-accurate
!
!=============================================================================80

  subroutine adddt( eqn_set, nnodes0, nnodes01,                                &
                    vol, volatn1, volatn2, volatn3, volatn4,                   &
                    qnode, qatn,qatn1, qatn2, qatn3, qatn4,                    &
                    res, res_gcl, grid_motion, ndim, n_tot, njac )

    use info_depr,            only : beta
    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : tcoeffn, b_vec_mebdf, t_stage, back_planes
    use solution_types,       only : incompressible

    integer,                             intent(in)    :: eqn_set
    character(80),                       intent(in)    :: grid_motion
    integer,                             intent(in)    :: nnodes0
    integer,                             intent(in)    :: nnodes01
    integer,                             intent(in)    :: ndim
    integer,                             intent(in)    :: n_tot
    integer,                             intent(in)    :: njac
    real(dp), dimension(nnodes01),       intent(in)    :: vol
    real(dp), dimension(nnodes01),       intent(in)    :: volatn1
    real(dp), dimension(nnodes01),       intent(in)    :: volatn2
    real(dp), dimension(nnodes01),       intent(in)    :: volatn3
    real(dp), dimension(nnodes01),       intent(in)    :: volatn4
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn1
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn2
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn3
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn4
    real(dp), dimension(njac,nnodes01),  intent(inout) :: res
    real(dp), dimension(1,nnodes01),     intent(in)    :: res_gcl

    integer :: i, n1

  continue

!   n1 indicates which eqn number to begin adding the time terms to; for
!   incompressible flow continuity equation has no physical time term

    if ( eqn_set == incompressible ) then
      n1 = 2               ! skip time physical term for continuity
    else
      n1 = 1               ! all equations have physical time terms
    end if

!   Add the appropriate time terms.  Don't use high-order schemes if you don't
!   have the data  The "back_plane" argument accounts for how many planes of
!   data currently exist in the temporal registers.

!   first-order backward time ==================================================

!   Account for BDF1, BDF2, BDF3, or MEBDF4 but only one plane of good
!   back_plane data

    if ( (itime >= 1) .and. (itime <= 4) .and. (back_planes == 1) ) then
      moving_mesh1: if (trim(grid_motion) /= 'static') then
        do i = 1, nnodes0
          if ( eqn_set==incompressible ) res(1,i) = res(1,i) + beta*res_gcl(1,i)
          res(n1:ndim,i) = res(n1:ndim,i) +                                    &
                           res_gcl(1,i)*qatn(n1:ndim,i) +                      &
                           vol(i)*tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))
        end do
      else ! static mesh
        do i = 1, nnodes0
          res(n1:ndim,i) = res(n1:ndim,i)                                      &
          + vol(i)*tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))
        end do
      end if moving_mesh1
    end if

!   second-order backward time =================================================

!   Account for BDF2, BDF3, or MEBDF4 but only two planes of good
!   back_plane data

    if ( (itime >= 2) .and. (itime <= 4) .and. (back_planes == 2) ) then
      moving_mesh2: if (trim(grid_motion) /= 'static') then
        do i = 1, nnodes0
          if ( eqn_set==incompressible ) res(1,i) = res(1,i) + beta*res_gcl(1,i)
          res(n1:ndim,i) = res(n1:ndim,i)                                      &
          + res_gcl(1,i)*qatn(n1:ndim,i)                                       &
          + vol(i)    *tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))           &
          + volatn1(i)*tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))
        end do
      else ! static mesh
        do i = 1, nnodes0
          res(n1:ndim,i) = res(n1:ndim,i)                                      &
          + vol(i)   *(tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))           &
          +            tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i)))
        end do
      end if moving_mesh2
    end if

!   third-order backward time ==================================================

    if ( (itime == 3) .and. (back_planes == 3) ) then
      moving_mesh3: if (trim(grid_motion) /= 'static') then
        do i = 1, nnodes0
          if ( eqn_set==incompressible ) res(1,i) = res(1,i) + beta*res_gcl(1,i)
          res(n1:ndim,i) = res(n1:ndim,i)                                      &
          + res_gcl(1,i)*qatn(n1:ndim,i)                                       &
          + vol(i)       *tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))        &
          + volatn1(i)   *tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))        &
          + volatn2(i)   *tcoeffn(4)*(qatn2(n1:ndim,i)-qatn(n1:ndim,i))
        end do
      else ! static mesh
        do i = 1, nnodes0
          res(n1:ndim,i) = res(n1:ndim,i)                                      &
          + vol(i)   *(tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))           &
          +            tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))           &
          +            tcoeffn(4)*(qatn2(n1:ndim,i)-qatn(n1:ndim,i)))
        end do
      end if moving_mesh3
    endif

!   4th-order MEBDF scheme =====================================================

    if ( (itime == 4 .and. back_planes == 3) ) then

!  Here is the shuffle strategy for MEBDF4
!
!       || Stage | Stage |  T Stage ||  T Stage
!       ||       |       |  m       ||  m
!       ||       |       |  p       ||  p
!       ---------------------------------------
!       ||   1   |   2   |      3   ||      1
!       ||       |       |          ||
!  n+2  ||       |   Q  -->(Q3)    --->(Q3) Q
!       ||       |       |          ||
!  n+1  ||   Q  -->  Qn -->(Q4) Q  ------>  Qn
!       ||       |       |          ||
!  n    ||   Qn -->  Q1 ----->  Qn ------>  Q1
!       ||       |       |          ||
!  n-1  ||   Q1 -->  Q2 ----->  Q1 ------>  Q2
!       ||       |       |          ||
!  n-2  ||   Q2 -->  Q3 ----->  Q2  ||

      if (t_stage <= 2) then
        moving_mesh4a: if (trim(grid_motion) /= 'static') then
          do i = 1, nnodes0
            if (eqn_set==incompressible) res(1,i) = res(1,i) + beta*res_gcl(1,i)
            res(n1:ndim,i) = res(n1:ndim,i)                                    &
            + res_gcl(1,i)*qatn(n1:ndim,i)                                     &
            + vol(i)       *tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))      &
            + volatn1(i)   *tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))      &
            + volatn2(i)   *tcoeffn(4)*(qatn2(n1:ndim,i)-qatn(n1:ndim,i))
          end do
        else ! static mesh
          do i = 1, nnodes0
            res(n1:ndim,i) = res(n1:ndim,i)                                    &
            + vol(i)   *(tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))         &
            +            tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))         &
            +            tcoeffn(4)*(qatn2(n1:ndim,i)-qatn(n1:ndim,i)))
          end do
        end if moving_mesh4a
      else   !     Now we take care of the Third (final) stage of MEBDF4
        moving_mesh4b: if (trim(grid_motion) /= 'static') then
          do i = 1, nnodes0
           if (eqn_set==incompressible) res(1,i) = res(1,i) + beta*res_gcl(1,i)
           res(n1:ndim,i) = res(n1:ndim,i)                                &
           + res_gcl(1,i)*qatn(n1:ndim,i)                                 &
           + volatn3(i)* b_vec_mebdf(1)*(qatn3(n1:ndim,i)-qatn(n1:ndim,i))& !n+2
           + volatn4(i)* b_vec_mebdf(2)*(qatn4(n1:ndim,i)-qatn(n1:ndim,i))& !old
           + vol    (i)     *tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))& !new
           + volatn1(i)     *tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))& !n-1
           + volatn2(i)     *tcoeffn(4)*(qatn2(n1:ndim,i)-qatn(n1:ndim,i))  !n-2
          end do
        else ! static mesh
          do i = 1, nnodes0
           res(n1:ndim,i) = res(n1:ndim,i)                                &
           + vol(i)   *(                                                  &
           +        b_vec_mebdf(1)*(qatn3(n1:ndim,i)-qatn(n1:ndim,i))     & !n+2
           +        b_vec_mebdf(2)*(qatn4(n1:ndim,i)-qatn(n1:ndim,i))     & !old
           +            tcoeffn(1)*(qnode(n1:ndim,i)-qatn(n1:ndim,i))     & !new
           +            tcoeffn(3)*(qatn1(n1:ndim,i)-qatn(n1:ndim,i))     & !n-1
           +            tcoeffn(4)*(qatn2(n1:ndim,i)-qatn(n1:ndim,i)) )     !n-2
          end do
        end if moving_mesh4b
      end if
    end if                                                         ! End MEBDF4

!   4th-order ESDIRK4 scheme ===================================================

  end subroutine adddt

!================================== ADDDT_TURB ===============================80
!
!  Add time term to turbulence residual if doing time-accurate
!
!=============================================================================80

  subroutine adddt_turb( nnodes0, nnodes01, vol, volatn1, volatn2, volatn3,    &
                         volatn4, turb, turbatn, turbatn1, turbatn2, turbatn3, &
                         turbatn4, res, res_gcl, grid_motion, n_turb )

    use nml_nonlinear_solves, only : itime
    use timeacc_coeffs,       only : tcoeffn, b_vec_mebdf, t_stage, back_planes

    character(80),                        intent(in)    :: grid_motion
    integer,                              intent(in)    :: nnodes0,nnodes01
    integer,                              intent(in)    :: n_turb
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(nnodes01),        intent(in)    :: volatn1
    real(dp), dimension(nnodes01),        intent(in)    :: volatn2
    real(dp), dimension(nnodes01),        intent(in)    :: volatn3
    real(dp), dimension(nnodes01),        intent(in)    :: volatn4
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn1
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn2
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn3
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn4
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(1,nnodes01),      intent(in)    :: res_gcl

    integer :: i

    continue

!   Add the appropriate time terms.  Don't use high-order schemes if you don't
!   have the data. The "back_plane" argument accounts for how many planes of
!   data currently exist in the Temporal registers.

!   first-order backward time ==================================================

!   Account for BDF1, BDF2, BDF3, or MEBDF4 but only one plane of good
!   back_plane data

    if ( (itime >= 1) .and. (itime <= 4) .and. (back_planes == 1) ) then
      moving_mesh1: if (trim(grid_motion) /= 'static') then
        do i = 1, nnodes0
          res(1:n_turb,i) = res(1:n_turb,i)                                    &
          + res_gcl(1,i)*turbatn(1:n_turb,i)                                   &
          + vol(i)   *tcoeffn(1)*(turb(1:n_turb,i)-turbatn(1:n_turb,i))
        end do
      else ! static mesh
        do i = 1, nnodes0
          res(1:n_turb,i) = res(1:n_turb,i)                                    &
          + vol(i)   *tcoeffn(1)*(turb(1:n_turb,i)-turbatn(1:n_turb,i))
        end do
      end if moving_mesh1
    end if

!   second-order backward time =================================================

!   Account for BDF2, BDF3, or MEBDF4 but only two planes of good
!   back_plane data

    if ( (itime >= 2) .and. (itime <= 4) .and. (back_planes == 2) ) then
      moving_mesh2: if (trim(grid_motion) /= 'static') then
        do i = 1, nnodes0
          res(1:n_turb,i) = res(1:n_turb,i)                                    &
          + res_gcl(1,i)*turbatn(1:n_turb,i)                                   &
          + vol(i)       *tcoeffn(1)*(turb(1:n_turb,i)    -turbatn(1:n_turb,i))&
          + volatn1(i)   *tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i))
        end do
      else ! static mesh
        do i = 1, nnodes0
          res(1:n_turb,i) = res(1:n_turb,i)                                    &
          + vol(i)   *(tcoeffn(1)*(turb(1:n_turb,i)    -turbatn(1:n_turb,i))   &
          +            tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i)))
        end do
      end if moving_mesh2
    end if

!   third-order backward time ==================================================

    if ( (itime == 3 .and. back_planes == 3) ) then
      moving_mesh3: if (trim(grid_motion) /= 'static') then
        do i = 1, nnodes0
          res(1:n_turb,i) = res(1:n_turb,i)                                    &
          + res_gcl(1,i)*turbatn(1:n_turb,i)                                   &
          + vol(i)       *tcoeffn(1)*(turb(1:n_turb,i)    -turbatn(1:n_turb,i))&
          + volatn1(i)   *tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i))&
          + volatn2(i)   *tcoeffn(4)*(turbatn2(1:n_turb,i)-turbatn(1:n_turb,i))
        end do
      else ! static mesh
        do i = 1, nnodes0
          res(1:n_turb,i) = res(1:n_turb,i)                                    &
          + vol(i)   *(tcoeffn(1)*(turb(1:n_turb,i)    -turbatn(1:n_turb,i))   &
          +            tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i))   &
          +            tcoeffn(4)*(turbatn2(1:n_turb,i)-turbatn(1:n_turb,i)))
        end do
      end if moving_mesh3
    end if

!   4th-order MEBDF scheme =====================================================

    if ( (itime == 4) .and. (back_planes == 3) ) then
      if (t_stage <= 2) then
        moving_mesh4a: if (trim(grid_motion) /= 'static') then
          do i = 1, nnodes0
            res(1:n_turb,i) = res(1:n_turb,i)                                  &
            + res_gcl(1,i)*turbatn(1:n_turb,i)                                 &
            + vol(i)     *tcoeffn(1)*(turb(1:n_turb,i)    -turbatn(1:n_turb,i))&
            + volatn1(i) *tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i))&
            + volatn2(i) *tcoeffn(4)*(turbatn2(1:n_turb,i)-turbatn(1:n_turb,i))
          end do
        else ! static mesh
          do i = 1, nnodes0
            res(1:n_turb,i) = res(1:n_turb,i)                                  &
            + vol(i) *(tcoeffn(1)*(turb(1:n_turb,i)    -turbatn(1:n_turb,i))   &
            +          tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i))   &
            +          tcoeffn(4)*(turbatn2(1:n_turb,i)-turbatn(1:n_turb,i)))
          end do
        end if moving_mesh4a
      else ! Now take care of the third stage
        moving_mesh4b: if (trim(grid_motion) /= 'static') then
          do i = 1, nnodes0
            res(1:n_turb,i) = res(1:n_turb,i)                                  &
            + res_gcl(1,i)*turbatn(1:n_turb,i)                                 &
            + volatn3(i)*b_vec_mebdf(1)*                                       &
                (turbatn3(1:n_turb,i)-turbatn(1:n_turb,i))                     &
            + volatn4(i)*b_vec_mebdf(2)*                                       &
                (turbatn4(1:n_turb,i)-turbatn(1:n_turb,i))                     &
            + vol    (i) *tcoeffn(1)*(turb    (1:n_turb,i)-turbatn(1:n_turb,i))&
            + volatn1(i) *tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i))&
            + volatn2(i)  *tcoeffn(4)*(turbatn2(1:n_turb,i)-turbatn(1:n_turb,i))
          end do
        else ! static mesh
          do i = 1, nnodes0
            res(1:n_turb,i) = res(1:n_turb,i)                                  &
            + vol(i)   *(                                                      &
            +        b_vec_mebdf(1)*(turbatn3(1:n_turb,i)-turbatn(1:n_turb,i)) &
            +        b_vec_mebdf(2)*(turbatn4(1:n_turb,i)-turbatn(1:n_turb,i)) &
            +            tcoeffn(1)*(turb    (1:n_turb,i)-turbatn(1:n_turb,i)) &
            +            tcoeffn(3)*(turbatn1(1:n_turb,i)-turbatn(1:n_turb,i)) &
            +            tcoeffn(4)*(turbatn2(1:n_turb,i)-turbatn(1:n_turb,i)) )
          end do
        end if moving_mesh4b
      end if
    end if

  end subroutine adddt_turb

!=================================== TIME_DIAG_NC ============================80
!
!  Add the time term to the diagonal of the LHS
!
!=============================================================================80

  subroutine time_diag_nc( fl, eqn_set, dof0, n_sta, block_size,               &
                           qnode, a_diag, vol, cdt, use_turb_cfl,              &
                           x, y, z, gradx, grady, gradz, ia, ja, g2m )

    use info_depr,            only : ntt, tightly_couple, mixed
    use nml_nonlinear_solves, only : itime
    use inviscid_flux,        only : first_order_iterations
    use solution_types,       only : compressible, incompressible, generic_gas
    use linear_systems,       only : cfl_inf_meanflow, adaptive_cfl,           &
                                     cfl_inf_turbulence
    use timeacc_coeffs,       only : tcoeffn
    use lmpi,                 only : lmpi_conditional_stop
    use periodics,            only : nperiodic, periodic_data, periodic

    integer,                    intent(in)    :: fl, eqn_set
    integer,                    intent(in)    :: dof0
    integer,                    intent(in)    :: n_sta
    integer,                    intent(in)    :: block_size
    integer,  dimension(:),     intent(in)    :: ia, ja, g2m
    real(dp), dimension(:),     intent(in)    :: vol
    real(dp), dimension(:,:),   intent(in)    :: qnode
    real(dp), dimension(:),     intent(in)    :: x,y,z
    real(dp), dimension(:,:),   intent(in)    :: gradx
    real(dp), dimension(:,:),   intent(in)    :: grady
    real(dp), dimension(:,:),   intent(in)    :: gradz
    real(dp), dimension(:,:,:), intent(inout) :: a_diag
    real(dp), dimension(:),     intent(in)    :: cdt
    logical,                    intent(in)    :: use_turb_cfl

    integer :: i, j, jstart, jend, n, row, node1, node2, t

    integer, dimension(dof0) :: periodic_tag

    real(dp) :: cfl, cflturb, pcoef, second, volume_sum
    real(dp) :: gradpxi, gradpyi, gradpzi
    real(dp) :: gradpxn, gradpyn, gradpzn

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: power   = 3.0_dp

  continue

!   Put the vol/dt terms on the diagonal of a

!   choose turbulent cfl number ramping or mean flow cfl number ramping

    if (use_turb_cfl) then
      cfl = ramp_cfl(fl,use_turb_parameters=use_turb_cfl)
      if ( itime == 0 .and. cfl_inf_turbulence ) return
    else
      cfl = ramp_cfl(fl)
      if ( itime == 0 .and. cfl_inf_meanflow ) return
    end if

!   Pick up a turbulent CFL number in case we are tightly coupled

    cflturb = cfl
    if ( tightly_couple ) then
      cflturb = ramp_cfl(fl,use_turb_parameters=.true.)
      if ( itime /= 0 ) then
        call lmpi_conditional_stop(1,'time-acc/tightly_couple:time_diag_nc')
      endif
      if ( adaptive_cfl ) then
        call lmpi_conditional_stop(1,'adapt_cfl/tightly_couple:time_diag_nc')
      endif
    endif

    time_accurate_or_not : if (itime /= 0) then

      if ( eqn_set == incompressible .and. .not.(use_turb_cfl)) then

        do i = 1,dof0
          row = g2m(i)
          a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) &
                                  + vol(i)/(cfl*cdt(i))
          do j = 2, block_size
            a_diag(n_sta+j-1,n_sta+j-1,row) = a_diag(n_sta+j-1,n_sta+j-1,row) &
                          + vol(i)/(cfl*cdt(i)) + vol(i)*tcoeffn(1)
          end do
        end do

      else

        if ( .not. periodic ) then

          do i = 1,dof0
            row = g2m(i)
            do j = 1, block_size
              a_diag(n_sta+j-1,n_sta+j-1,row) = &
              a_diag(n_sta+j-1,n_sta+j-1,row)   &
                            + vol(i)/(cfl*cdt(i)) + vol(i)*tcoeffn(1)
            end do
          end do

        else

! Tag the periodic nodes and add the timeterm to the diagonals on the primary
! plane.  Do not add anything on the secondary plane because it will screw
! things up when we later combine jacobians across the planes.  Note that cdt
! was already collapsed across the planes when we formed it

          periodic_tag = 0

          set_loop : do i = 1, nperiodic
            node1 = periodic_data(i)%list(1)
            volume_sum = vol(node1)
            periodic_tag(node1) = 1
            row = g2m(node1)
            do j = 2, periodic_data(i)%n
              node2 = periodic_data(i)%list(j)
              periodic_tag(node2) = 1
              volume_sum = volume_sum + vol(node2)
            end do
            do j = 1, block_size
              a_diag(n_sta+j-1,n_sta+j-1,row) =                           &
              a_diag(n_sta+j-1,n_sta+j-1,row)                             &
                              + (volume_sum)/(cfl*cdt(node1))             &
                              + (volume_sum)*tcoeffn(1)
            end do
          end do set_loop

          do i = 1,dof0
            if ( periodic_tag(i) /= 0 ) cycle
            row = g2m(i)
            do j = 1, block_size
              a_diag(n_sta+j-1,n_sta+j-1,row) = &
              a_diag(n_sta+j-1,n_sta+j-1,row)   &
                                      + vol(i)/(cfl*cdt(i)) + vol(i)*tcoeffn(1)
            end do
          end do

        end if

      end if

    else time_accurate_or_not  ! not branch

      if ( adaptive_cfl  ) then

        second = my_0
        if (ntt > first_order_iterations) second = my_1

        do i = 1,dof0

          gradpxi = second*gradx(5,i)
          gradpyi = second*grady(5,i)
          gradpzi = second*gradz(5,i)

          pcoef = my_1
          jstart = ia(i)
          jend   = ia(i+1) - 1
          three_D_worst_case : do j = jstart, jend
            n = ja(j)
            gradpxn = second*gradx(5,n)
            gradpyn = second*grady(5,n)
            gradpzn = second*gradz(5,n)
            pcoef = min(pcoef,pswitch(x(i),y(i),z(i),x(n),y(n),z(n),       &
                                      qnode(5,i),qnode(5,n),               &
                                      gradpxi,gradpyi,gradpzi,             &
                                      gradpxn,gradpyn,gradpzn,             &
                                      my_half,my_half,my_1,power,'delt'))
          end do three_D_worst_case

          cfl = ramp_cfl(fl,pcoef=pcoef)

          row = g2m(i)
          do j = 1, block_size
            a_diag(n_sta+j-1,n_sta+j-1,row) = &
            a_diag(n_sta+j-1,n_sta+j-1,row)   &
                                  + vol(i)/(cfl*cdt(i))
          end do
        end do

      else

        if ( tightly_couple ) then

          do i = 1,dof0
            row = g2m(i)
            do j = 1, block_size
              a_diag(n_sta+j-1,n_sta+j-1,row) = &
              a_diag(n_sta+j-1,n_sta+j-1,row)   &
                                    + vol(i)/(cfl*cdt(i))
            end do
            if ( .not.mixed .and. .not.use_turb_cfl ) then
              do t=n_sta-1+block_size+1,size(a_diag,1)
                a_diag(t,t,row) = a_diag(t,t,row) + vol(i)/(cflturb*cdt(i))
              enddo
            endif
          end do

        else

          if ( .not. periodic ) then

!$acc parallel present(g2m,a_diag,vol,cdt)
!$acc loop gang vector

            do i = 1,dof0
              row = g2m(i)
              do j = 1, block_size
                a_diag(n_sta+j-1,n_sta+j-1,row) = &
                a_diag(n_sta+j-1,n_sta+j-1,row)   &
                              + vol(i)/(cfl*cdt(i))
              end do
            end do
!$acc end parallel

          else

! Tag the periodic nodes and add the timeterm to the diagonals on the primary
! plane.  Do not add anything on the secondary plane because it will screw
! things up when we later combine jacobians across the planes.  Note that cdt
! was already collapsed across the planes when we formed it

            periodic_tag = 0

            set_loop2 : do i = 1, nperiodic
              node1 = periodic_data(i)%list(1)
              volume_sum = vol(node1)
              periodic_tag(node1) = 1
              row = g2m(node1)
              do j = 2, periodic_data(i)%n
                node2 = periodic_data(i)%list(j)
                periodic_tag(node2) = 1
                volume_sum = volume_sum + vol(node2)
              end do
              do j = 1, block_size
                a_diag(n_sta+j-1,n_sta+j-1,row) = &
                a_diag(n_sta+j-1,n_sta+j-1,row)   &
                       + (volume_sum)/(cfl*cdt(node1))
              end do
            end do set_loop2

            do i = 1,dof0
              if ( periodic_tag(i) /= 0 ) cycle
              row = g2m(i)
              do j = 1, block_size
                a_diag(n_sta+j-1,n_sta+j-1,row) = &
                a_diag(n_sta+j-1,n_sta+j-1,row)   &
                          + vol(i)/(cfl*cdt(i))
              end do
            end do

          endif

        endif

      endif

      if ( eqn_set == compressible ) then
        call time_diag_prec( fl, dof0, qnode, a_diag, vol, cdt,            &
                             use_turb_cfl, g2m, n_sta)
      !CHECK
      else if ( eqn_set == generic_gas ) then
        call time_diag_prec( fl, dof0, qnode, a_diag, vol, cdt,            &
                             use_turb_cfl, g2m, n_sta)
      end if

    endif time_accurate_or_not

  end subroutine time_diag_nc

!================================== SUBIT_RESIDS ============================80
!
!  Compute l2 norms of mean flow subiteration residuals
!
!=============================================================================80

  subroutine subit_resids( dof0, nnodes01, res, ndim, njac, n_eqns, point_dofs,&
                           m2g )

    use allocations,             only : my_alloc_ptr
    use lmpi,                    only : lmpi_master, lmpi_bcast, lmpi_reduce
    use nml_governing_equations, only : ssdc_flag

    integer,                            intent(in) :: dof0
    integer,                            intent(in) :: nnodes01
    integer,                            intent(in) :: ndim
    integer,                            intent(in) :: njac
    integer,                            intent(in) :: n_eqns

    integer, dimension(:), intent(in) :: point_dofs, m2g

    real(dp), dimension(njac,nnodes01), intent(in) :: res

    integer :: i, l2norm_factor, inode, nodesum

    logical, save :: init = .true.

    continue

    if (init) then
      init = .false.
      call my_alloc_ptr(sumi, ndim)
      call my_alloc_ptr(sumtot, ndim)
    end if

    sumi(1:ndim) = 0._dp

! We monitor residuals differently when SSDC scheme is active. Since
! only a portion of the mesh is solved with FUN3D, only monitor the
! equations associated with those specific mesh points.

    if ( ssdc_flag ) then

      do i = 1, n_eqns
        inode = point_dofs(i) ! in matrix numbering
        inode = m2g(inode)    ! in grid numbering
        sumi(1:ndim) = sumi(1:ndim) + res(1:ndim,inode)*res(1:ndim,inode)
      end do

      nodesum = n_eqns

    else

      do i = 1, dof0
        sumi(1:ndim) = sumi(1:ndim) + res(1:ndim,i)*res(1:ndim,i)
      end do

      nodesum = dof0

    endif

!   reduce and broadcast the subiteration residual

    call lmpi_reduce(sumi,sumtot)
    call lmpi_reduce(nodesum,l2norm_factor)

    if (lmpi_master) then
      if ( l2norm_factor == 0 ) then
        sumtot(:) = sqrt(sumtot(:))
      else
        sumtot(:) = sqrt(sumtot(:)/real(l2norm_factor,dp))
      endif
    end if

    call lmpi_bcast(sumtot)

  end subroutine subit_resids

!=============================== SUBIT_RESIDS_TURB ===========================80
!
!  Compute l2 norms of turbulence subiteration residuals
!
!=============================================================================80

  subroutine subit_resids_turb( dof0, nnodes01, res, n_turb, n_eqns,           &
                                point_dofs, m2g )

    use allocations,             only : my_alloc_ptr
    use lmpi,                    only : lmpi_master, lmpi_bcast, lmpi_reduce
    use nml_governing_equations, only : ssdc_flag

    integer,                              intent(in) :: dof0,nnodes01
    integer,                              intent(in) :: n_turb,n_eqns

    integer, dimension(:), intent(in) :: point_dofs, m2g

    real(dp), dimension(n_turb,nnodes01), intent(in) :: res

    integer :: i, l2norm_factor, inode, nodesum

    logical, save :: init = .true.

    continue

    if (init) then
      init = .false.
      call my_alloc_ptr(sumi_turb, n_turb)
      call my_alloc_ptr(sumtot_turb, n_turb)
    end if

    sumi_turb(1:n_turb) = 0._dp

! We monitor residuals differently when SSDC scheme is active. Since
! only a portion of the mesh is solved with FUN3D, only monitor the
! equations associated with those specific mesh points.

    if ( ssdc_flag ) then

      do i = 1, n_eqns
        inode = point_dofs(i) ! in matrix numbering
        inode = m2g(inode)    ! in grid numbering
        sumi_turb(1:n_turb) = sumi_turb(1:n_turb)                              &
                            + res(1:n_turb,inode)*res(1:n_turb,inode)
      end do

      nodesum = n_eqns

    else

      do i = 1, dof0
        sumi_turb(1:n_turb) = sumi_turb(1:n_turb)                              &
                            + res(1:n_turb,i)*res(1:n_turb,i)
      end do

      nodesum = dof0

    endif

!   reduce and broadcast the subiteration turb residual

    call lmpi_reduce(sumi_turb,sumtot_turb)
    call lmpi_reduce(nodesum,l2norm_factor)

    if (lmpi_master) then
      if ( l2norm_factor == 0 ) then
        sumtot_turb(:) = sqrt(sumtot_turb(:))
      else
        sumtot_turb(:) = sqrt(sumtot_turb(:)/real(l2norm_factor,dp))
      endif
    end if

    call lmpi_bcast(sumtot_turb)

  end subroutine subit_resids_turb

!================================= OPEN_SUBIT_RESIDS =========================80
!
!  Opens file to write out time-accurate subiteration residuals
!
!=============================================================================80

  subroutine open_subit_resids( project, ndim, n_turb, njac, eqn_set )

    use lmpi,              only : lmpi_master
    use system_extensions, only : se_open
    use solution_types,    only : generic_gas
    use info_depr,         only : title, tightly_couple
    use string_utils,      only : sprintf
    use file_utils,        only : available_unit

    character(*), intent(in) :: project
    integer,      intent(in) :: ndim, n_turb, njac, eqn_set

    character(80)  :: temp_string
    character(800) :: res_string

    integer :: eqn, njac_hist

    continue

    if (.not. lmpi_master) return

    iunit_subit = available_unit()

    call se_open(iunit_subit,trim(project)//'_subhist.dat')

    res_string = ' "R_1"'

    njac_hist = njac
    if (.not.tightly_couple .and. eqn_set/=generic_gas ) njac_hist = ndim+n_turb

    do eqn = 1, njac_hist-1
      temp_string = trim(sprintf(' "R_%i0',eqn+1))//'"'
      res_string = trim(res_string) // trim(temp_string)
    end do

    res_string = trim(res_string) // ' "C_x" "C_y" "C_z" "C_M_x" "C_M_y"'      &
                                  // ' "C_M_z" "C_L" "C_D"'

    write(iunit_subit,'(a)') 'TITLE="' // trim(adjustl(title)) // '"'

    write(iunit_subit,'(6a)') 'VARIABLES="Fractional_Time_Step"',              &
                              trim(res_string)

  end subroutine open_subit_resids

!================================= WRITE_SUBIT_RESIDS ========================80
!
!  Write all subiteration residuals to screen (and file) if doing time-accurate
!
!=============================================================================80

  subroutine write_subit_resids( eqn_set, project, ndim, n_turb, njac,         &
                                 totforce, n_turb_ke, n_dis_nutl )

    use info_depr,            only : ntt, simulation_time
    use nml_nonlinear_solves, only : itime
    use nml_nonlinear_solves, only : subiters
    use timeacc_coeffs,       only : t_stage
    use generic_gas_map,      only : n_species, n_momx, n_momy, n_momz,        &
                                     n_etot, n_energy, n_turb_g
    use io,                   only : prior_iters
    use lmpi,                 only : lmpi_master
    use solution_types,       only : compressible, incompressible, generic_gas
    use system_extensions,    only : se_flush
    use force_types,          only : force_type

    integer,           intent(in) :: eqn_set
    character(80),     intent(in) :: project
    integer,           intent(in) :: ndim, n_turb, njac
    type(force_type),  intent(in) :: totforce
    integer, optional, intent(in) :: n_turb_ke, n_dis_nutl

    logical, save :: open_file = .true.

    continue

    if (lmpi_master) then

      if (open_file) then
        call open_subit_resids(project, ndim, n_turb, njac, eqn_set)
        open_file = .false.
      end if

      if (pseudo_sub == 1) then
        if(itime >= 4) then
          write(*,*)
          if    (itime == 4) then
            write(*,'(a,i5,a,i4)')                                             &
               " Begin MEBDF4 Time Step ",ntt,", Stage ",t_stage
          elseif(itime == 5) then
            write(*,'(a,i5,a,i4)')                                             &
               " Begin ESDIRK4 Time Step ",ntt,", Stage ",t_stage
          endif
        else
           write(*,*)
           write(*,'(a,i0)') " Begin Time Step ",ntt
        endif
      endif

      write(*,"(' subiteration',i4,'   simulation_time =',e14.7)")             &
                 pseudo_sub, real(simulation_time,dp)

      select case(eqn_set)

        case (compressible,incompressible)

          write(*,"('   mean flow res         = ',10(e14.7,1x))")              &
                sumtot(1:ndim)

          write(*,"('   rms flow temporal err = ',10(e14.7,1x))")              &
                terr_flow_rmsg(1:ndim)

          if (moving_vortex) then
            write(*,"('   Weighted vortex L2.err at time = ',10(e14.7,1x))")   &
                werr_vortex_rmsg(1:ndim)
            write(*,"('   total vortex L2.err at time = ',10(e14.7,1x))")      &
                err_vortex_rmsg(1:ndim)
            write(*,"('   total vortex Max.err at time = ',10(e14.7,1x))")     &
                err_vortex_maxg(1:ndim)
          end if

          if (n_turb > 0) then

            write(*,"('   turbulent res         = ',10(e14.7,1x))")            &
                  sumtot_turb(1:n_turb)

            write(*,"('   rms turb temporal err = ',10(e14.7,1x))")            &
                  terr_turb_rmsg(1:n_turb)

            write(iunit_subit,'(e14.7,10(e15.7))')                             &
                  real(ntt + prior_iters,dp) +                                 &
                  real(pseudo_sub-1,dp)/real(subiters,dp),                     &
                  sumtot(1:ndim), sumtot_turb(1:n_turb),                       &
                  totforce%cx,  totforce%cy,  totforce%cz,                     &
                  totforce%cmx, totforce%cmy, totforce%cmz,                    &
                  totforce%cl,  totforce%cd

            call se_flush(iunit_subit)

          else

            write(iunit_subit,'(e14.7,10(e15.7))')                             &
                  real(ntt + prior_iters,dp) +                                 &
                  real(pseudo_sub-1,dp)/real(subiters,dp),                     &
                  sumtot(1:ndim),                                              &
                  totforce%cx,  totforce%cy,  totforce%cz,                     &
                  totforce%cmx, totforce%cmy, totforce%cmz,                    &
                  totforce%cl,  totforce%cd

            call se_flush(iunit_subit)

          end if

        case (generic_gas)

          write(*,"('   mean flow res = ',10(e14.7,1x))")                      &
                sum(sumtot(1:n_species)),sumtot(n_momx),sumtot(n_momy),        &
                sumtot(n_momz),sum(sumtot(n_etot:n_momz+n_energy))

          if (n_turb_g > 0 .and. present(n_turb_ke) .and. present(n_dis_nutl)) &
          write(*,"('   turbulent res = ',999(e14.7,1x))")                     &
                sumtot(n_turb_ke:n_dis_nutl)

          write(iunit_subit,'(e14.7,50(e15.7))')                               &
                real(ntt + prior_iters,dp) +                                   &
                real(pseudo_sub-1,dp)/real(subiters,dp),                       &
                sum(sumtot(1:n_species)),sumtot(n_momx),sumtot(n_momy),        &
                sumtot(n_momz),sum(sumtot(n_etot:n_momz+n_energy))
          call se_flush(iunit_subit)

      end select

    end if

  end subroutine write_subit_resids

!=============================== TEMPORAL_ERR_FLOW ===========================80
!
!  Compute temporal error in flow equations committed during subiteration loop
!
!=============================================================================80

  subroutine temporal_err_flow( eqn_set,                                       &
                                nnodes0, nnodes01, vol, volatn, volatn1,       &
                                volatn2, qnode, qatn, qatn1,                   &
                                qatn2, qatn3, qatn4, terr_flow, grid_motion,   &
                                ndim, n_tot, n_eqns, point_dofs, m2g )

    use info_depr,               only : ntt
    use nml_nonlinear_solves,    only : itime
    use allocations,             only : my_alloc_ptr
    use lmpi,                    only : lmpi_master, lmpi_bcast, lmpi_reduce
    use timeacc_coeffs,          only : tcoeffn, tcoeff2, t_stage, back_planes
    use solution_types,          only : incompressible
    use string_utils,            only : sub_string
    use nml_governing_equations, only : ssdc_flag

    integer, intent(in) :: eqn_set
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: ndim
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_eqns

    integer, dimension(:), intent(in) :: point_dofs, m2g

    real(dp), dimension(nnodes01),       intent(in)    :: vol
    real(dp), dimension(nnodes01),       intent(in)    :: volatn
    real(dp), dimension(nnodes01),       intent(in)    :: volatn1
    real(dp), dimension(nnodes01),       intent(in)    :: volatn2
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn1
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn2
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn3
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qatn4
    real(dp), dimension(n_tot,nnodes01), intent(inout) :: terr_flow

    character(6), intent(in) :: grid_motion

    integer :: i, n1, l2norm_factor, inode, nodesum

    logical, save :: init = .true.

    continue

! FIXME: use automatic arrays

    if (init) then
      init = .false.
      call my_alloc_ptr(terr_flow_rmsl, ndim)
      call my_alloc_ptr(terr_flow_rmsg, ndim)
    end if

!   initialize temporal error vectors

      terr_flow_rmsl(1:ndim) = 0._dp
      terr_flow_rmsg(1:ndim) = 0._dp

!   n1 indicates which eqn number to begin adding the time terms to; for
!   incompressible flow continuity equation has no physical time term

    if ( eqn_set == incompressible ) then
      n1 = 2               ! skip time physical term for continuity
    else
      n1 = 1               ! all equations have physical time terms
    end if

    if ( back_planes == 1 ) return

    if (itime <= 2) then

      if ( sub_string(grid_motion,'deform') ) then

!      deforming mesh

       do i = 1, nnodes0
         terr_flow(n1:ndim,i) =                                                &
             ( (tcoeffn(1) - tcoeff2(1)) * qnode(n1:ndim,i) * vol    (i)       &
             + (tcoeffn(2) - tcoeff2(2)) * qatn (n1:ndim,i) * volatn (i)       &
             + (tcoeffn(3) - tcoeff2(3)) * qatn1(n1:ndim,i) * volatn1(i) )
       end do

       else

!      rigid mesh

       do i = 1, nnodes0
         terr_flow(n1:ndim,i) =((tcoeffn(1) - tcoeff2(1)) * qnode(n1:ndim,i)   &
                              + (tcoeffn(2) - tcoeff2(2)) * qatn (n1:ndim,i)   &
                              + (tcoeffn(3) - tcoeff2(3)) * qatn1(n1:ndim,i))  &
                              *  vol(i)
       end do

      endif

    else if (itime == 3) then

      if ( sub_string(grid_motion,'deform') ) then

!      deforming mesh

       do i = 1, nnodes0
         terr_flow(n1:ndim,i) =                                                &
             ( (tcoeffn(1) - tcoeff2(1)) * qnode(n1:ndim,i) * vol    (i)       &
             + (tcoeffn(2) - tcoeff2(2)) * qatn (n1:ndim,i) * volatn (i)       &
             + (tcoeffn(3) - tcoeff2(3)) * qatn1(n1:ndim,i) * volatn1(i)       &
             + (tcoeffn(4) - tcoeff2(4)) * qatn2(n1:ndim,i) * volatn2(i) )
       end do

      else

!      rigid mesh

       do i = 1, nnodes0
         terr_flow(n1:ndim,i) =((tcoeffn(1) - tcoeff2(1)) * qnode(n1:ndim,i)   &
                              + (tcoeffn(2) - tcoeff2(2)) * qatn (n1:ndim,i)   &
                              + (tcoeffn(3) - tcoeff2(3)) * qatn1(n1:ndim,i)   &
                              + (tcoeffn(4) - tcoeff2(4)) * qatn2(n1:ndim,i))  &
                              *  vol(i)
       end do
      endif

    else if (itime == 4) then

!       ---------------------------------------
!       ||   1   |   2   |      3   ||      1
!       ||       |       |          ||
!  n+2  ||       |   Q  -->(Q3)    --->(Q3) Q
!       ||       |       |          ||
!  n+1  ||   Q  -->  Qn -->(Q4) Q  ------>  Qn
!       ||       |       |          ||
!  n    ||   Qn -->  Q1 ----->  Qn ------>  Q1
!       ||       |       |          ||
!  n-1  ||   Q1 -->  Q2 ----->  Q1 ------>  Q2
!       ||       |       |          ||
!  n-2  ||   Q2 -->  Q3 ----->  Q2  ||

      if    (t_stage == 1 .and. ntt /= 1 ) then
        do i = 1, nnodes0
          terr_flow(n1:ndim,i) =  (qnode(n1:ndim,i) - qatn3(n1:ndim,i))*vol(i)
        end do
      elseif(t_stage == 3) then
        do i = 1, nnodes0
          terr_flow(n1:ndim,i) =  (qnode(n1:ndim,i) - qatn4(n1:ndim,i))*vol(i)
        end do
      endif

    else if (itime == 5) then   !   here is the RK hook

    end if

!   compute rms of temporal error on local node

    terr_flow_rmsl(1:ndim) = 0._dp

    if ( ssdc_flag ) then

      do i = 1, n_eqns
        inode = point_dofs(i) ! in matrix numbering
        inode = m2g(inode)    ! in grid numbering
        terr_flow_rmsl(1:ndim)=terr_flow_rmsl(1:ndim)+terr_flow(1:ndim,inode)**2
      end do

      nodesum = n_eqns

    else

      do i = 1, nnodes0
        terr_flow_rmsl(1:ndim) = terr_flow_rmsl(1:ndim) + terr_flow(1:ndim,i)**2
      end do

      nodesum = nnodes0

    endif

!   reduce and broadcast the estimated temporal error

    call lmpi_reduce(terr_flow_rmsl,terr_flow_rmsg)

    call lmpi_reduce(nodesum,l2norm_factor)

    if (lmpi_master) then
      if ( l2norm_factor == 0 ) then
        terr_flow_rmsg = sqrt(terr_flow_rmsg)
      else
        terr_flow_rmsg = sqrt(terr_flow_rmsg/real(l2norm_factor,dp))
      endif
    end if

    call lmpi_bcast(terr_flow_rmsg)

  end subroutine temporal_err_flow

!=============================== TEMPORAL_ERR_TURB ===========================80
!
!  Compute temporal error in turbulence  committed during subiteration loop
!
!=============================================================================80

  subroutine temporal_err_turb( nnodes0, nnodes01,                             &
                                vol, volatn, volatn1, volatn2,                 &
                                turb, turbatn, turbatn1, turbatn2, turbatn3,   &
                                turbatn4, terr_turb,                           &
                                grid_motion, n_turb, n_eqns, point_dofs, m2g )

    use info_depr,               only : ntt
    use nml_nonlinear_solves,    only : itime
    use allocations,             only : my_alloc_ptr
    use lmpi,                    only : lmpi_master, lmpi_bcast, lmpi_reduce
    use timeacc_coeffs,          only : tcoeffn, tcoeff2, t_stage, back_planes
    use string_utils,            only : sub_string
    use nml_governing_equations, only : ssdc_flag

    integer, intent(in) :: nnodes0,nnodes01
    integer, intent(in) :: n_turb,n_eqns

    integer, dimension(:), intent(in) :: point_dofs, m2g

    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(nnodes01),        intent(in)    :: volatn
    real(dp), dimension(nnodes01),        intent(in)    :: volatn1
    real(dp), dimension(nnodes01),        intent(in)    :: volatn2
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn1
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn2
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn3
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turbatn4
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: terr_turb

    character(len=6), intent(in) :: grid_motion

    integer :: i, l2norm_factor, inode, nodesum

    logical, save :: init = .true.

    continue

! FIXME: use automatic arrays

    if (init) then
      init = .false.
      call my_alloc_ptr(terr_turb_rmsl, n_turb)
      call my_alloc_ptr(terr_turb_rmsg, n_turb)
    end if

!   initialize temporal error vectors

      terr_turb_rmsl(1:n_turb) = 0._dp
      terr_turb_rmsg(1:n_turb) = 0._dp

    if ( back_planes == 1 ) return

    if (itime <= 2) then

      if ( sub_string(grid_motion,'deform') ) then

!      deforming mesh

       do i = 1, nnodes0
         terr_turb(1:n_turb,i) =                                               &
             ( (tcoeffn(1) - tcoeff2(1)) * turb    (1:n_turb,i) * vol    (i)   &
             + (tcoeffn(2) - tcoeff2(2)) * turbatn (1:n_turb,i) * volatn (i)   &
             + (tcoeffn(3) - tcoeff2(3)) * turbatn1(1:n_turb,i) * volatn1(i) )
       end do

      else

!      rigid mesh

      do i = 1, nnodes0
        terr_turb(1:n_turb,i) =((tcoeffn(1) - tcoeff2(1))*turb    (1:n_turb,i) &
                              + (tcoeffn(2) - tcoeff2(2))*turbatn (1:n_turb,i) &
                              + (tcoeffn(3) - tcoeff2(3))*turbatn1(1:n_turb,i))&
                              *  vol(i)
      end do

     endif

    else if (itime == 3) then

      if ( sub_string(grid_motion,'deform') ) then

!      deforming mesh

       do i = 1, nnodes0
         terr_turb(1:n_turb,i) =                                               &
             ( (tcoeffn(1) - tcoeff2(1)) * turb    (1:n_turb,i) * vol    (i)   &
             + (tcoeffn(2) - tcoeff2(2)) * turbatn (1:n_turb,i) * volatn (i)   &
             + (tcoeffn(3) - tcoeff2(3)) * turbatn1(1:n_turb,i) * volatn1(i)   &
             + (tcoeffn(4) - tcoeff2(4)) * turbatn2(1:n_turb,i) * volatn2(i) )
       end do

      else

!      rigid mesh

      do i = 1, nnodes0
        terr_turb(1:n_turb,i) =((tcoeffn(1) - tcoeff2(1))*turb    (1:n_turb,i) &
                              + (tcoeffn(2) - tcoeff2(2))*turbatn (1:n_turb,i) &
                              + (tcoeffn(3) - tcoeff2(3))*turbatn1(1:n_turb,i) &
                              + (tcoeffn(4) - tcoeff2(4))*turbatn2(1:n_turb,i))&
                              *  vol(i)
      end do

     endif

    else if (itime == 4) then

      if    (t_stage == 1 .and. ntt /= 1 ) then
       do i = 1, nnodes0
       terr_turb(1:n_turb,i) = (turb(1:n_turb,i) - turbatn3(1:n_turb,i))*vol(i)
       end do
      elseif(t_stage == 3) then
       do i = 1, nnodes0
       terr_turb(1:n_turb,i) = (turb(1:n_turb,i) - turbatn4(1:n_turb,i))*vol(i)
       end do
      endif

    else if (itime == 5) then   !  here is the hook for the RK scheme

    end if

!   compute rms of temporal error on local node

    terr_turb_rmsl(1:n_turb) = 0._dp

    if ( ssdc_flag ) then

      do i = 1, n_eqns
        inode = point_dofs(i) ! in matrix numbering
        inode = m2g(inode)    ! in grid numbering
        terr_turb_rmsl(1:n_turb) = terr_turb_rmsl(1:n_turb)                    &
                                 + terr_turb(1:n_turb,inode)**2
      end do

      nodesum = n_eqns

    else

      do i = 1, nnodes0
        terr_turb_rmsl(1:n_turb) = terr_turb_rmsl(1:n_turb)                    &
                                 + terr_turb(1:n_turb,i)**2
      end do

      nodesum = nnodes0

    endif

!   reduce and broadcast the estimated temporal error

    call lmpi_reduce(terr_turb_rmsl,terr_turb_rmsg)

    call lmpi_reduce(nodesum,l2norm_factor)

    if (lmpi_master) then
      if ( l2norm_factor == 0 ) then
        terr_turb_rmsg = sqrt(terr_turb_rmsg)
      else
        terr_turb_rmsg = sqrt(terr_turb_rmsg/real(l2norm_factor,dp))
      endif
    end if

    call lmpi_bcast(terr_turb_rmsg)

  end subroutine temporal_err_turb

!=============================== MOVING_VORTEX_ERR ===========================80
!
!  Compute error in cfd solutions for moving vortex compared to exact solution
!
!=============================================================================80

  subroutine moving_vortex_err( nnodes0, nnodes01, qnode, err_vortex, x, z,    &
                                vol, nnodesg, sim_time, ndim, n_tot, eqn_set )

    use allocations, only : my_alloc_ptr
    use lmpi,        only : lmpi_master, lmpi_bcast, lmpi_reduce
    use solution_types, only : incompressible

    integer,                             intent(in)    :: nnodes0
    integer,                             intent(in)    :: nnodes01
    integer,                             intent(in)    :: ndim
    integer,                             intent(in)    :: n_tot
    integer,                             intent(in)    :: nnodesg
    integer,                             intent(in)    :: eqn_set
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nnodes01),       intent(in)    :: x,z, vol
    real(dp), dimension(ndim ,nnodes01), intent(inout) :: err_vortex
    real(dp),                            intent(in)    :: sim_time

    real(dp) :: uu, vv, ww, q1, q2, q3, q4, q5, total_vol, total_volg

    integer :: i, n, l2norm_factor

    logical, save :: init = .true.

    continue

! FIXME: use automatic arrays

    if (init) then
      call my_alloc_ptr(werr_vortex_rmsl, ndim)
      call my_alloc_ptr(werr_vortex_rmsg, ndim)
      call my_alloc_ptr(err_vortex_rmsl, ndim)
      call my_alloc_ptr(err_vortex_rmsg, ndim)
      call my_alloc_ptr(err_vortex_maxl, ndim)
      call my_alloc_ptr(err_vortex_maxg, ndim)
    end if

!   initialize temporal error vectors

    werr_vortex_rmsl(1:ndim) = 0._dp
    werr_vortex_rmsg(1:ndim) = 0._dp
    err_vortex_rmsl(1:ndim) = 0._dp
    err_vortex_rmsg(1:ndim) = 0._dp
    err_vortex_maxl(1:ndim) = 0._dp
    err_vortex_maxg(1:ndim) = 0._dp

    do i = 1, nnodes0

      if( eqn_set == incompressible) then

        call moving_vortex_datai(x(i),z(i),sim_time,q1,q2,q3,q4)
        err_vortex(1,i) = qnode(1,i) - q1
        err_vortex(2,i) = qnode(2,i) - q2
        err_vortex(3,i) = qnode(3,i) - q3
        err_vortex(4,i) = qnode(4,i) - q4

      else

        call moving_vortex_data(x(i),z(i),sim_time,q1,q2,q3,q4,q5)

        uu = q2/q1
        vv = q3/q1
        ww = q4/q1

        err_vortex(1,i) = qnode(1,i) - q1
  !     err_vortex(2,i) = qnode(2,i) - q2
  !     err_vortex(3,i) = qnode(3,i) - q3
  !     err_vortex(4,i) = qnode(4,i) - q4
        err_vortex(2,i) = qnode(2,i)/qnode(1,i) - uu
        err_vortex(3,i) = qnode(3,i)/qnode(1,i) - vv
        err_vortex(4,i) = qnode(4,i)/qnode(1,i) - ww
        err_vortex(5,i) = qnode(5,i) - q5

      end if

    end do

!   compute rms of solution error on local node

    err_vortex_rmsl(1:ndim) = 0._dp
    werr_vortex_rmsl(1:ndim) = 0._dp
    total_vol  = 0._dp
    total_volg = 0._dp
    do i = 1, nnodes0
      err_vortex_rmsl(1:ndim) = err_vortex_rmsl(1:ndim)                        &
                              + err_vortex(1:ndim,i)**2
      werr_vortex_rmsl(1:ndim)= werr_vortex_rmsl(1:ndim)                       &
                              +(err_vortex(1:ndim,i)**2)*vol(i)
      total_vol = total_vol + vol(i)
    end do
    do i = 1, nnodes0
      do n = 1,ndim
        if ( abs(err_vortex(n,i)) >= err_vortex_maxl(n))                       &
          err_vortex_maxl(n) = abs (err_vortex(n,i))
      end do
    end do

!   reduce and broadcast the estimated temporal error

    call lmpi_reduce(werr_vortex_rmsl,werr_vortex_rmsg)
    call lmpi_reduce(err_vortex_rmsl,err_vortex_rmsg)
    call lmpi_reduce(err_vortex_maxl,err_vortex_maxg)
    call lmpi_reduce(total_vol,total_volg)

    if (lmpi_master) then
      l2norm_factor = nnodesg
      err_vortex_rmsg = sqrt(err_vortex_rmsg/real(l2norm_factor,dp))
      werr_vortex_rmsg = sqrt(werr_vortex_rmsg/total_volg)
    end if

    call lmpi_bcast(werr_vortex_rmsg)
    call lmpi_bcast(err_vortex_rmsg)
    call lmpi_bcast(err_vortex_maxg)

    if (init) then
      write(*,"('init weighted rms err vortex = ',5(e14.7,1x))")               &
           werr_vortex_rmsg(1:ndim)
      write(*,"('init rms err vortex = ',5(e14.7,1x))") err_vortex_rmsg(1:ndim)
      write(*,"('init max err vortex = ',5(e14.7,1x))") err_vortex_maxg(1:ndim)
    end if

    init = .false.

  end subroutine moving_vortex_err

!================== HOW MANY BACKPLANES OF TEMPORAL DATA =====================80
!
!   Compute the number of planes of temporal data the simulation currently has.
!   Planes are computed based on current knowledge of back_planes, and itime
!   Note that the routine is entered AFTER the completion of a step.  The first
!   Step is computed based on stored information (or startup information)
!
!=============================================================================80

  subroutine temporal_backplanes(itime, back_planes, stage)

    integer, intent(in)    :: itime
    integer, intent(inout) :: back_planes, stage

    integer :: previous_back_planes

  continue

    previous_back_planes = back_planes

    if    (itime == 1) then

      back_planes = 1
      stage       = 1

    elseif(itime == 2) then

      back_planes = 2
      stage       = 1

    elseif(itime == 3) then

      if    (previous_back_planes == 1) then
        back_planes = 2
        stage       = 1
      elseif(previous_back_planes >= 2) then
        back_planes = 3
        stage       = 1
      endif

    elseif(itime == 4) then

      if    (previous_back_planes == 1) then
        back_planes = 2
        stage       = 1
      elseif(previous_back_planes >= 2) then
        back_planes = 3
        stage       = 3
      endif

    elseif(itime == 5) then

      back_planes = 1
      stage       = 5

    endif

    end subroutine temporal_backplanes

!============= STAGE VALUE PREDICTOR FOR TIME-ACCURATE DATA ==================80
!
!   Use the availabe information from previous stages (steps) to construct
!   a starting guess for the solution at the new timestep.  It is only used
!   to start the nonlinear iteration.
!
!=============================================================================80

  subroutine stage_value_predictor(soln)

    use info_depr,            only : ivisc
    use nml_nonlinear_solves, only : itime
    use solution_types,       only : soln_type, generic_gas
    use timeacc_coeffs,       only : t_stage

    type(soln_type), intent(inout) :: soln

    logical, save :: init = .false.

  continue

    if ( itime == 4 ) then

      if     ( t_stage == 1 .and. init) then
        soln%q_dof(:,:) = soln%qatn3(:,:)
        if ((ivisc >= 6 ) .and. soln%eqn_set /= generic_gas ) &
          soln%turb(:,:) = soln%turbatn3(:,:)
      else if( t_stage == 3 ) then
        soln%q_dof(:,:) = soln%qatn4(:,:)
        if ((ivisc >= 6 ) .and. soln%eqn_set /= generic_gas ) &
          soln%turb(:,:) = soln%turbatn4(:,:)
      end if

      init = .true.

    else if( itime == 5 ) then !  here is the hook for the RK scheme

    end if

  end subroutine stage_value_predictor

!=========================== MOVING_VORTEX_DATA ==============================80
!
!   Calculate the pointwise values of the moving vortex, given the position
!   and the time
!
!=============================================================================80

  subroutine moving_vortex_data( x, z, sim_time, q1, q2, q3, q4, q5 )

    use info_depr, only : xmach
    use ivals,     only : rho0, p0, u0, v0
    use fluid,     only : gamma, gm1

    real(dp), intent(in)  :: x,z,sim_time
    real(dp), intent(out) :: q1,q2,q3,q4,q5

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_haf  = 0.5_dp

    real(dp) :: pi, xvort, zvort, c1, c2, arg1, xtemp, ztemp, ptemp,     &
                utemp, vtemp, wtemp, eps

  continue

!   compute appropriate parameters for the moving_vortex

    pi    = acos(-my_1)
    xvort = 10.0_dp
    zvort = my_0

    eps = 1.0_dp
    c1    = my_haf*eps/pi                     !  here is the circulation
    c2    = my_haf *c1*c1*gm1*xmach*xmach

!   compute exact solution for moving vortex and the error in cfd solutions

    xtemp      = x - xvort - sim_time*u0
    ztemp      = z - zvort
    arg1       = my_1 - xtemp**2 - ztemp**2
    q1         = rho0* (my_1 - c2*exp(arg1) )**( my_1/gm1)
    ptemp      = p0  * (my_1 - c2*exp(arg1) )**(gamma/gm1)
    utemp      = u0  * (my_1 - c1*ztemp*exp(my_haf*arg1) )
    vtemp      = v0
    wtemp      = u0  * (       c1*xtemp*exp(my_haf*arg1) )
    q2         = q1    *  utemp
    q3         = q1    *  vtemp
    q4         = q1    *  wtemp
    q5         = q1    * (ptemp / q1 / gm1                                     &
               + my_haf*(utemp**2 + vtemp**2 + wtemp**2 )  )

  end subroutine moving_vortex_data

!=========================== MOVING_VORTEX_DATAI =============================80
!
!   Calculate the pointwise values of the moving vortex, given the position
!   and the time (incompressible version)
!
!=============================================================================80

  subroutine moving_vortex_datai( x, z, sim_time, q1, q2, q3, q4 )

    use ivals,     only : p0, u0, v0

    real(dp), intent(in)  :: x,z,sim_time
    real(dp), intent(out) :: q1,q2,q3,q4

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_haf  = 0.5_dp

    real(dp) :: xvort, zvort, c1, arg1, xtemp, ztemp, ptemp,                   &
                utemp, vtemp, wtemp

  continue

!   compute appropriate parameters for the moving_vortex

    xvort = -12.0_dp
    zvort = my_0
    c1    = 0.02_dp

!   compute exact solution for mod_ehs vortex and the error in cfd solutions

    xtemp      = x - xvort - sim_time*u0
    ztemp      = z - zvort
    arg1       = -xtemp**2 - ztemp**2
    utemp      = u0  * (my_1 - c1*ztemp*exp(my_haf*arg1) )
    vtemp      = v0
    wtemp      = u0  * (       c1*xtemp*exp(my_haf*arg1) )
    ptemp      = p0 - my_haf*(u0*c1)**2*exp(arg1)
    q1         = ptemp
    q2         = utemp
    q3         = vtemp
    q4         = wtemp

  end subroutine moving_vortex_datai

!================================ COMP_QTIMEAVG ==============================80
!
! Compute time-averaged quantities
!
!=============================================================================80

  subroutine comp_qtimeavg( n_tot, n_grd, nnodes0, nnodes01, qnode, q_time_avg,&
                            qq_time_avg, eqn_set, amut,                        &
                            gradx, grady, gradz )

    use lmpi,           only : lmpi_master
    use lmpi_app,       only : lmpi_xfer
    use thermo,         only : q_type, conserved_q_type
    use solution_types, only : compressible
    use nml_time_avg_params,  only : tavg_header_version, num_time_avg

    integer,                               intent(in)    :: n_tot
    integer,                               intent(in)    :: n_grd
    integer,                               intent(in)    :: nnodes0
    integer,                               intent(in)    :: nnodes01
    integer,                               intent(in)    :: eqn_set
    real(dp), dimension(n_tot,nnodes01),   intent(in)    :: qnode
    real(dp), dimension(n_tot*2,nnodes01), intent(inout) :: q_time_avg
    real(dp), dimension(      5,nnodes01), intent(inout) :: qq_time_avg
    real(dp), dimension(      nnodes01),   intent(in)    :: amut
    real(dp), dimension(n_grd,nnodes01),   intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),   intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),   intent(in)    :: gradz

    integer :: i

    real(dp), dimension(n_tot) :: q
    real(dp), dimension(3,3)   :: gradv
    real(dp)                   :: vort, vort_x, vort_y, vort_z
    real(dp)                   :: local_var1, local_var2, local_var3

    continue

!   initialize data
    local_var1 = 0.0_dp
    local_var2 = 0.0_dp
    local_var3 = 0.0_dp

!    Compute running time-averages

!   Increment the counter for the number of time averages
    num_time_avg = num_time_avg + 1
    if(lmpi_master) write(*,*) 'num_time_avg=', num_time_avg

    do i = 1, nnodes0

      q = qnode(:,i)
      if ( eqn_set == compressible .and. q_type == conserved_q_type ) &
        q = in_primitive_variables(qnode(:,i))

      q_time_avg(1:n_tot,i)  = &
        (q_time_avg(1:n_tot,i)*(num_time_avg-1) + q )/num_time_avg

      q_time_avg(1+n_tot:2*n_tot,i)  = &
        (q_time_avg(1+n_tot:2*n_tot,i)*(num_time_avg-1) + q**2 )/num_time_avg

       gradv = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                   &
                        , gradx(3,i), grady(3,i), gradz(3,i)                   &
                        , gradx(4,i), grady(4,i), gradz(4,i) )
      vort    = get_vort( gradv )
      vort_x  = gradv(3,2)-gradv(2,3)
      vort_y  = gradv(1,3)-gradv(3,1)
      vort_z  = gradv(2,1)-gradv(1,2)

      if ( tavg_header_version == 2 ) then
        local_var1 = q(2)*q(3)
        local_var2 = q(2)*q(4)
        local_var3 = q(3)*q(4)
      else if ( tavg_header_version == 3 ) then
        local_var1 = vort_x
        local_var2 = vort_y
        local_var3 = vort_z
      end if

      qq_time_avg(1,i)  = &
                 (qq_time_avg(1,i)*(num_time_avg-1) + local_var1 )/num_time_avg
      qq_time_avg(2,i)  = &
                 (qq_time_avg(2,i)*(num_time_avg-1) + local_var2 )/num_time_avg
      qq_time_avg(3,i)  = &
                 (qq_time_avg(3,i)*(num_time_avg-1) + local_var3 )/num_time_avg
      qq_time_avg(4,i)  = &
                 (qq_time_avg(4,i)*(num_time_avg-1) + amut(i)   )/num_time_avg
      qq_time_avg(5,i)  = &
                 (qq_time_avg(5,i)*(num_time_avg-1) + vort      )/num_time_avg

    enddo

    call lmpi_xfer(q_time_avg)
    call lmpi_xfer(qq_time_avg)

  end subroutine comp_qtimeavg

!============================ TIMESTEP_SCALARS ===============================80
!
!   Determines how many backplanes, the stage and use_prior at startup
!
!=============================================================================80

  subroutine timestep_scalars()

    use nml_global,           only : irest
    use nml_nonlinear_solves, only : itime
    use io,                   only : use_prior
    use timeacc_coeffs,       only : back_planes, stage

  continue

    if(itime <= 3) stage_alloc = 1        !  stage_alloc is used to allocate
    if(itime == 4) stage_alloc = 3        !  memory.  It is the maximum
    if(itime == 5) stage_alloc = 5        !  dimension of the method

    if    (irest == -1 ) then             !  Time-accurate from Steady State
      irest       =  1
      use_prior   =  0
      back_planes =  1
      stage       =  1
    elseif(irest ==  0 ) then             !  Time-accurate: free-stream IC
      irest       =  0
      use_prior   =  0
      back_planes =  1
      stage       =  1
    elseif(irest ==  1 ) then             !  Time-accurate from stored data
      irest       =  1
      use_prior   =  1
      back_planes =  1                    !  back_planes overwritten by startup
        if(itime <=  3 ) stage = 1
        if(itime ==  4 ) stage = 3
        if(itime ==  5 ) stage = 5
    endif

  end subroutine timestep_scalars

!=================================== SET_DTAU  ===============================80
!
!  Set a local time stepping contribution to the diagonal of the LHS
!
!=============================================================================80

  subroutine set_dtau( dof0, volq, cdt, g2m, dtau )

    use info_depr, only : skeleton

    integer,                intent(in)    :: dof0
    integer,  dimension(:), intent(in)    :: g2m
    real(dp), dimension(:), intent(in)    :: volq, cdt
    real(dp), dimension(:), intent(inout) :: dtau

    integer :: i, row

  continue

    if ( skeleton > 0 ) write(*,*) 'Setting dtau term (matrix order)'

    do i = 1,dof0
      row = g2m(i)
      dtau(row) = volq(i)/(cdt(i))
    end do

  end subroutine set_dtau

!=================================== TIME_DIAG_PREC ==========================80
!
!  Preconditioned : Add the time term to the diagonal of the LHS
!
!=============================================================================80

  subroutine time_diag_prec( fl, dof0, qnode, a_diag,                       &
                             vol, cdt, use_turb_cfl, g2m, n_sta )

    use info_depr, only : lowmach_prec, prec_mach_star,                        &
                          prec_mach_trans1, prec_mach_trans2, prec_alt,        &
                          prec_dtau_skip
    use fluid,     only : gm1, ggm1
    use lmpi,      only : lmpi_conditional_stop

    integer,                    intent(in)    :: fl, dof0, n_sta
    integer,  dimension(:),     intent(in)    :: g2m
    real(dp), dimension(:),     intent(in)    :: vol
    real(dp), dimension(:,:),   intent(in)    :: qnode
    real(dp), dimension(:,:,:), intent(inout) :: a_diag
    real(dp), dimension(:),     intent(in)    :: cdt
    logical,                    intent(in)    :: use_turb_cfl

    integer :: i, row, ierr

    real(dp) :: mach, beta, beta_star, mach_rel, q2, c2, u, v, w
    real(dp) :: cfl, term1, term

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

  continue

    ierr = 0

!   Put the vol/dt terms on the diagonal of a

!   choose turbulent cfl number ramping or mean flow cfl number ramping

    if (use_turb_cfl) then
      cfl = ramp_cfl(fl,use_turb_parameters=use_turb_cfl)
    else
      cfl = ramp_cfl(fl)
    end if

!         Lowspeed preconditioning additions corresponding to M (G-I) M^-1
!         where G is preconditioning matrix

    if (.not.use_turb_cfl) then !3
      if(lowmach_prec .and. prec_dtau_skip == 0) then !2
        if(prec_alt == 1) then

          if ( n_sta /= 1 ) ierr = 1
          call lmpi_conditional_stop(ierr,'n_sta:1:time_diag_prec')

          do i = 1,dof0

            u  = qnode(2,i)/qnode(1,i)
            v  = qnode(3,i)/qnode(1,i)
            w  = qnode(4,i)/qnode(1,i)

            q2 = u**2 + v**2 + w**2
            c2 = ggm1*( qnode(5,i)/qnode(1,i) - my_half*q2 )

!           Compute beta based on local Mach number

            beta_star = prec_mach_star**2
            mach      = sqrt( q2/c2 )

            if(mach >= prec_mach_trans2) then
              beta = my_1
            elseif(mach <= prec_mach_trans1) then
              beta = beta_star
            else
              mach_rel = (mach-prec_mach_trans1)/&
                         (prec_mach_trans2-prec_mach_trans1)
              beta = beta_star  + (beta_star-my_1)*(2.0_dp*mach_rel**2)    &
                                                  *( -1.5_dp + mach_rel)
            endif

!           Simple additions only to last equation

            term1 = (my_1/beta - my_1)*vol(i)/(cfl*cdt(i))
            row = g2m(i)
            a_diag(5,1,row) = a_diag(5,1,row) + term1*q2*my_half
            a_diag(5,2,row) = a_diag(5,2,row) - term1*u
            a_diag(5,3,row) = a_diag(5,3,row) - term1*v
            a_diag(5,4,row) = a_diag(5,4,row) - term1*w
            a_diag(5,5,row) = a_diag(5,5,row) + term1

          end do

        elseif(prec_alt == 0) then

          if ( n_sta /= 1 ) ierr = 1
          call lmpi_conditional_stop(ierr,'n_sta:2:time_diag_prec')

          do i = 1,dof0

            u  = qnode(2,i)/qnode(1,i)
            v  = qnode(3,i)/qnode(1,i)
            w  = qnode(4,i)/qnode(1,i)

            q2 = u**2 + v**2 + w**2
            c2 = ggm1*( qnode(5,i)/qnode(1,i) - my_half*q2 )

!           Compute beta based on local Mach number

            beta_star = prec_mach_star**2
            mach      = sqrt( q2/c2 )

            if(mach >= prec_mach_trans2) then
              beta = my_1
            elseif(mach <= prec_mach_trans1) then
              beta = beta_star
            else
              mach_rel = (mach-prec_mach_trans1)/&
                         (prec_mach_trans2-prec_mach_trans1)
              beta = beta_star  + (beta_star-my_1)*(2.0_dp*mach_rel**2)    &
                                                  *( -1.5_dp + mach_rel)
            endif

!           Additions to entire Jacobian matrix

            row = g2m(i)
            term1 = gm1*(my_1/beta - my_1)*vol(i)/(c2*cfl*cdt(i))
            a_diag(1,1,row) = a_diag(1,1,row) + term1*q2*my_half
            a_diag(1,2,row) = a_diag(1,2,row) - term1*u
            a_diag(1,3,row) = a_diag(1,3,row) - term1*v
            a_diag(1,4,row) = a_diag(1,4,row) - term1*w
            a_diag(1,5,row) = a_diag(1,5,row) + term1

            term = u*term1
            a_diag(2,1,row) = a_diag(2,1,row) + term*q2*my_half
            a_diag(2,2,row) = a_diag(2,2,row) - term*u
            a_diag(2,3,row) = a_diag(2,3,row) - term*v
            a_diag(2,4,row) = a_diag(2,4,row) - term*w
            a_diag(2,5,row) = a_diag(2,5,row) + term

            term = v*term1
            a_diag(3,1,row) = a_diag(3,1,row) + term*q2*my_half
            a_diag(3,2,row) = a_diag(3,2,row) - term*u
            a_diag(3,3,row) = a_diag(3,3,row) - term*v
            a_diag(3,4,row) = a_diag(3,4,row) - term*w
            a_diag(3,5,row) = a_diag(3,5,row) + term

            term = w*term1
            a_diag(4,1,row) = a_diag(4,1,row) + term*q2*my_half
            a_diag(4,2,row) = a_diag(4,2,row) - term*u
            a_diag(4,3,row) = a_diag(4,3,row) - term*v
            a_diag(4,4,row) = a_diag(4,4,row) - term*w
            a_diag(4,5,row) = a_diag(4,5,row) + term

            term = (c2/gm1 + q2*my_half)*term1
            a_diag(5,1,row) = a_diag(5,1,row) + term*q2*my_half
            a_diag(5,2,row) = a_diag(5,2,row) - term*u
            a_diag(5,3,row) = a_diag(5,3,row) - term*v
            a_diag(5,4,row) = a_diag(5,4,row) - term*w
            a_diag(5,5,row) = a_diag(5,5,row) + term
          end do

        endif
      endif !2
    endif !3

  end subroutine time_diag_prec

!=================================== TIME_DIAG_CC ============================80
!
!  Cell-centered : Add the time term to the diagonal of the LHS
!
!=============================================================================80

  subroutine time_diag_cc( fl, eqn_set, block_size, qnode, a_diag, cdt,        &
                           ncell0, ncell01, cell_vol,                          &
                           use_turb_cfl, xc, yc, zc, gradx, grady, gradz,      &
                           ia, ja, g2m )

    use info_depr,            only : ntt, tightly_couple
    use nml_nonlinear_solves, only : itime
    use solution_types,       only : generic_gas
    use inviscid_flux,        only : first_order_iterations
    use linear_systems,       only : cfl_inf_meanflow, adaptive_cfl,           &
                                     cfl_inf_turbulence
    use lmpi,                 only : lmpi_conditional_stop

    integer,                      intent(in)    :: fl, eqn_set
    integer,                      intent(in)    :: block_size
    integer,                      intent(in)    :: ncell0
    integer,                      intent(in)    :: ncell01
    integer,  dimension(:),       intent(in)    :: ia, ja, g2m
    real(dp), dimension(ncell01), intent(in)    :: cell_vol
    real(dp), dimension(:,:),     intent(in)    :: qnode
    real(dp), dimension(ncell01), intent(in)    :: xc,yc,zc
    real(dp), dimension(:,:),     intent(in)    :: gradx
    real(dp), dimension(:,:),     intent(in)    :: grady
    real(dp), dimension(:,:),     intent(in)    :: gradz
    real(dp), dimension(:,:,:),   intent(inout) :: a_diag
    real(dp), dimension(:),       intent(in)    :: cdt
    logical,                      intent(in)    :: use_turb_cfl

    integer :: i, j, jstart, jend, n, row

    real(dp) :: cfl, pcoef, second
    real(dp) :: gradpxi, gradpyi, gradpzi
    real(dp) :: gradpxn, gradpyn, gradpzn

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: power   = 3.0_dp

  continue

!   Put the vol/dt terms on the diagonal of a

!   choose turbulent cfl number ramping or mean flow cfl number ramping

    if (use_turb_cfl) then
      cfl = ramp_cfl(fl,use_turb_parameters=use_turb_cfl)
      if ( itime == 0 .and. cfl_inf_turbulence ) return
    else
      cfl = ramp_cfl(fl)
      if ( itime == 0 .and. cfl_inf_meanflow ) return
    end if

    if ( eqn_set == generic_gas ) then
      call lmpi_conditional_stop(1,'eqn_set:time_diag_cc')
    endif

    if (itime /= 0) then
      call lmpi_conditional_stop(1,'time-accurate:time_diag_cc')
    endif

    if ( tightly_couple ) then
      call lmpi_conditional_stop(1,'tightly_couple:time_diag_cc')
    endif

    if ( adaptive_cfl ) then

      second = my_0
      if (ntt > first_order_iterations) second = my_1

      do i = 1,ncell0

        gradpxi = second*gradx(5,i)
        gradpyi = second*grady(5,i)
        gradpzi = second*gradz(5,i)

        pcoef  = my_1
        jstart = ia(i)
        jend   = ia(i+1) - 1
        cc_three_D_worst_case : do j = jstart, jend
          n = ja(j)
          gradpxn = second*gradx(5,n)
          gradpyn = second*grady(5,n)
          gradpzn = second*gradz(5,n)
          pcoef = min(pcoef,pswitch(xc(i),yc(i),zc(i),xc(n),yc(n),zc(n), &
                                    qnode(5,i),qnode(5,n),               &
                                    gradpxi,gradpyi,gradpzi,             &
                                    gradpxn,gradpyn,gradpzn,             &
                                    my_half,my_half,my_1,power,'delt'))
        end do cc_three_D_worst_case

        cfl = ramp_cfl(fl,pcoef=pcoef)

        row = g2m(i)
        do j = 1, block_size
          a_diag(j,j,row) = a_diag(j,j,row) + cell_vol(i)/(cfl*cdt(i))
        end do
      end do

    else

      do i = 1, ncell0
        row = g2m(i)
        do j = 1, block_size
          a_diag(j,j,row) = a_diag(j,j,row) + cell_vol(i)/(cfl*cdt(i))
        end do
      end do

    end if

  end subroutine time_diag_cc

!=============================== RAMP_CFL ====================================80
!
! Determine the current CFL from the ramping strategy.
!
!=============================================================================80
  function ramp_cfl( level, use_turb_parameters, pcoef )

    use kinddefs,             only : dp
    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use cfl_defs,           only : hanim
    use cfl_defs,             only : cfl1, cfl2, cflturb1, cflturb2, cfl_floor,&
                                     cfl_m_frechet, cfl_t_frechet, iramp

    integer,  intent(in)           :: level
    real(dp), intent(in), optional :: pcoef
    logical,  intent(in), optional :: use_turb_parameters

    real(dp) :: ramp_cfl

    integer :: step

    real(dp) :: cfl_ramp1, cfl_ramp2

    real(dp) :: slope

  continue

    if ( itime == 0 ) then
      step = ntt
    else
      step = pseudo_sub
    end if

    if ( .not. present(use_turb_parameters) ) then

      cfl_ramp1 = cfl1
      cfl_ramp2 = cfl2

      ramp_cfl = cfl_m_frechet(level)

    else

      cfl_ramp1 = cflturb1
      cfl_ramp2 = cflturb2

      ramp_cfl = cfl_t_frechet(level)

    end if

    if ( .not.hanim ) then

      slope = 1._dp
      if ( iramp > 1 ) then
        slope = (cfl_ramp2 - cfl_ramp1)/real(iramp-1,dp)
      endif

      ramp_cfl = cfl_ramp1 + real(step-1,dp)*slope

      ramp_cfl = max( cfl_ramp1, min( cfl_ramp2, ramp_cfl ) )

    endif

    if ( present(pcoef) ) then
      if ( ramp_cfl > cfl_floor )                         &
      ramp_cfl = pcoef*ramp_cfl + (1._dp - pcoef)*cfl_floor
    end if

  end function ramp_cfl

  include 'pswitch.f90'
  include 'in_primitive_variables.f90'
  include 'set_gradv.f90'
  include 'get_vort.f90'

end module timeacc

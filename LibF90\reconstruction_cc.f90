module reconstruction_cc

  use kinddefs, only : dp
  use bc_types, only : bcc_type
  use lmpi,     only : lmpi_die, lmpi_master

  implicit none

  private

  public :: gsblimcc
  public :: pressure_limiter_coeffcc

contains

!================================= GSBLIMCC ==================================80
!
!               Generalized Stencil Based Limiter Cell Centered
!
! Tim Barths stencil based flux limiter modified for the cell centered scheme
! and generalized to use the minmod, van <PERSON>, smooth (van Albada),
! smooth(CFL3D) or smooth(Venkat) limiter functions.
! Where:
!   iflim = 13 : TVD minmod
!   iflim = 14 : TVD van Leer
!   iflim = 15 : TVB smooth ala van Albada
!   iflim = 16 : TVB smooth ala CFL3D
!   iflim = 17 : TVB smooth ala Venkat
!
!=============================================================================80

  subroutine gsblimcc(ncell0, ncell01, nface,                                  &
                      grid_origin, ia_dim, ja_dim, rlsq_ia, rlsq_ja,           &
                      bcc, qcell, phi, gradx, grady, gradz,                    &
                      xc, yc, zc, cell_vol,                                    &
                      x_face, y_face, z_face,                                  &
                      xn_face, yn_face, zn_face, area_face,                    &
                      fptr, n_grd, n_tot, ndim, eqn_set )

    use fun3d_constants,          only : my_0, my_half, my_3rd, my_4th,        &
                                         my_1, my_2, my_3, my_4, my_6, pi
    use fluid,                    only : gamma, gm1, sutherland_constant
    use inviscid_flux,            only : first_order_iterations, iflim,        &
                                         freeze_limiter_iteration
    use info_depr,                only : ntt, twod, kappa_umuscl, epscoef,     &
                                         ivisc, tref, xmach, re,               &
                                         pr_limiter_coeff, wall_limit_less
    use nml_nonlinear_solves,     only : itime
    use timeacc,                  only : pseudo_sub
    use bc_names,                 only : symmetry_x, symmetry_y, symmetry_z,   &
                                         bc_null
    use generic_gas_map,          only : n_etot, n_sonic_k, n_amu_k
    use solution_types,           only : generic_gas
    use nml_governing_equations,  only : viscous_terms

    integer,                            intent(in)    :: ncell0, ncell01
    integer,                            intent(in)    :: ndim, eqn_set
    integer,                            intent(in)    :: nface
    integer,                            intent(in)    :: grid_origin
    integer,                            intent(in)    :: ia_dim, ja_dim
    integer, dimension(ia_dim),         intent(in)    :: rlsq_ia
    integer, dimension(ja_dim),         intent(in)    :: rlsq_ja
    integer,                            intent(in)    :: n_grd, n_tot

    real(dp), dimension(n_tot,ncell01), intent(in)    :: qcell
    real(dp), dimension(n_grd,ncell01), intent(inout) :: phi

    real(dp), dimension(n_grd,ncell01), intent(in)    :: gradx,grady,gradz
    real(dp), dimension(ncell01),       intent(in)    :: xc, yc, zc
    real(dp), dimension(ncell01),       intent(in)    :: cell_vol
    real(dp), dimension(nface),         intent(in)    :: x_face
    real(dp), dimension(nface),         intent(in)    :: y_face
    real(dp), dimension(nface),         intent(in)    :: z_face
    real(dp), dimension(nface),         intent(in)    :: xn_face
    real(dp), dimension(nface),         intent(in)    :: yn_face
    real(dp), dimension(nface),         intent(in)    :: zn_face
    real(dp), dimension(nface),         intent(in)    :: area_face

    integer,  dimension(6,nface),       intent(in)    :: fptr

!   real, dimension(n_grd,ncell01), intent(in), optional :: phi_cc FIXME

    type(bcc_type),                     intent(in)    :: bcc

    ! temporary automatic arrays to form limiter
    real(dp), dimension(n_grd,ncell01)    :: qmin
    real(dp), dimension(n_grd,ncell01)    :: qmax
    real(dp), dimension(ncell01)         :: cellmach

    ! saved copy of limiter to permit freezing
    real(dp), dimension(n_grd,ncell01)   :: phi_original

    ! limiter blending
    real(dp), dimension(nface)           :: fl_coeff
    real(dp), dimension(ncell01)         :: pcoefn

    integer  :: i, ia, n, k, my_ntt
    integer  :: cell1, cell2, eq, ibc

    real(dp) :: rx, ry, rz, rx1, ry1, rz1, rx2, ry2, rz2
    real(dp) :: temp, pcoef,  pr_limiter_coeff_g

    real(dp) :: dqm, dqp, omega, eps
    real(dp) :: my_fact1, my_fact2, my_exp, xmr
    real(dp) :: cstar
    real(dp) :: rho, u, v, w, press, ut2, enrgy, Ht, sos2
    real(dp) :: rho1, press1, t1, a1, u1, diam1, mu1, Re1, vcoef1
    real(dp) :: rho2, press2, t2, a2, u2, diam2, mu2, Re2, vcoef2
    real(dp) :: machf1, machf2, fmsmn1, fmsmn2
    real(dp) :: pcofn1, pcofn2
    real(dp) :: c_gen, mu

    real(dp), dimension(1:5) :: gqr, dq, q1, q2

!   logical :: monitor_phi_contribs = .false. FIXME

    integer, parameter  :: powervi = 4
    real(dp), parameter :: powervr = 35.0_dp/50.0_dp
    real(dp), parameter :: Re_min  = 35.0_dp
    real(dp), parameter :: dRe     = 30.0_dp

    real(dp), parameter :: powerir      = 0.25_dp
    real(dp), parameter :: phi_min_mach = 0.35_dp
    real(dp), parameter :: dphi_mach    = 0.65_dp
    real(dp), parameter :: plc_min_mach = 0.35_dp
    real(dp), parameter :: dplc_mach    = 0.35_dp

  continue

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    my_exp   = my_3rd
    my_fact1 = my_6
    my_fact2 = my_3
    if (twod) then
      my_exp   = my_half
      my_fact1 = my_4
      my_fact2 = my_1
    end if

    if (ivisc >= 2) then
      cstar = sutherland_constant/tref
      xmr = xmach/re
    else
      cstar = my_0
      xmr = my_0
    end if

    omega = my_2

! Store the current value of the limiter
! if we're going to potentially freeze it

    if ( freeze_limiter_iteration >=0 ) then
      phi_original = phi
    end if

! Loop over the cells and initialize phi to 1

    do i = 1,ncell01
      phi(1:5,i) = my_1
    end do

! if not second order leave

    if (my_ntt < first_order_iterations) return

! Figure out if we're going to keep track of which edge determines
! phi for each node (needed for adjoint)

!   if ( present(phi_cc) ) monitor_phi_contribs = .true.

! Loop over the cells and initialize qmin qnd qmax and compter the Mach no.

    set_up_loop: do i = 1,ncell01

      qmax(1:5,i) = qcell(1:5,i)
      qmin(1:5,i) = qcell(1:5,i)

      rho   = qcell(1,i)
      u     = qcell(2,i)
      v     = qcell(3,i)
      w     = qcell(4,i)
      press = qcell(5,i)
      ut2   = u*u + v*v + w*w
      enrgy = press/gm1 + my_half*rho*ut2
      Ht    = (enrgy + press)/rho
      sos2  = gm1*(Ht-my_half*ut2)

      cellmach(i) = sqrt(ut2/sos2)

    end do set_up_loop

! Find the q max and min of the face neighbor cell part of
! the gradient stencil

    qmin_qmax_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

! Check cell1

      qmax(1:5,cell1) = max(qmax(1:5,cell1), qcell(1:5,cell2))
      qmin(1:5,cell1) = min(qmin(1:5,cell1), qcell(1:5,cell2))

! Check cell2

      qmax(1:5,cell2) = max(qmax(1:5,cell2), qcell(1:5,cell1))
      qmin(1:5,cell2) = min(qmin(1:5,cell2), qcell(1:5,cell1))

    end do qmin_qmax_loop

! Extend the search to include the q max and min of the cells
! used to "augment" the gradient stencil when needed

    if ((grid_origin == 1) .or. (grid_origin >= 3)) then
      augmentation_cell_loop: do cell1 = 1,ncell0
        do ia=rlsq_ia(cell1),rlsq_ia(cell1+1)-1
          cell2 = rlsq_ja(ia)
          qmax(1:5,cell1) = max(qmax(1:5,cell1), qcell(1:5,cell2))
          qmin(1:5,cell1) = min(qmin(1:5,cell1), qcell(1:5,cell2))
        end do
      end do augmentation_cell_loop
    end if

! Scan for possible new qmin and qmax due to certain boundary conditions
! 1) symmetry
! 2) others to follow later perhaps

    bc_cell_loop: do n = 1, bcc%n_faces0

      ibc = bcc%ibc(n) ; if ( ibc == bc_null ) cycle
      select case(ibc)
      case ( symmetry_x, symmetry_y, symmetry_z )
        cell1 = bcc%cell(n)
        eq = 0
        if ( ibc == symmetry_x ) then
          eq = 2
        else if ( ibc == symmetry_y ) then
          eq = 3
        else if( ibc == symmetry_z ) then
          eq = 4
        end if
        qmax(eq,cell1) = max(qmax(eq,cell1), -qcell(eq,cell1))
        qmin(eq,cell1) = min(qmin(eq,cell1), -qcell(eq,cell1))
      end select

    end do bc_cell_loop

! Now that we have found the max and min of the stencil cells
! lets do the extrapolation to the face and "limit" the gradient
! based on those bounds such that no new extrema are produced

    gradient_limiter_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

! Compute the vector from the cell 1 center to the cell face center

      rx = x_face(n) - xc(cell1)
      ry = y_face(n) - yc(cell1)
      rz = z_face(n) - zc(cell1)

! Compute the cell 1 limiter coefficient

      eps = epscoef*(my_fact2*(my_fact1*cell_vol(cell1)/pi)**my_exp)**3

! Reconstruct to the cell 1 side of the interface

      gqr(1:5) = gradx(1:5,cell1)*rx+grady(1:5,cell1)*ry+gradz(1:5,cell1)*rz
      q1(1:5)  = qcell(1:5,cell1) + gqr(1:5)

! Compute the limiter coefficient array at cell 1 using a
! generalization of the Barth dual based limiting method

      do k = 1, 5

        if (q1(k) /= qcell(k,cell1)) then

          if (q1(k) > qcell(k,cell1)) then
            dqp = qmax(k,cell1) - qcell(k,cell1)
          else if (q1(k) < qcell(k,cell1))  then
            dqp = qmin(k,cell1) - qcell(k,cell1)
          end if

          dqm = q1(k) - qcell(k,cell1)
          dqp = my_half*dqp

          temp = my_1
          if (iflim == 13) then
            temp = minmods(dqm, omega*dqp) / dqm
          else if (iflim == 14) then
            temp = vlflxls(dqm, dqp) / dqm
          else if (iflim == 15) then
            temp = vaflxls(dqm, dqp, eps) / dqm
          else if (iflim == 16) then
            temp = smthlms(dqm, dqp, eps) / dqm
          else if (iflim == 17) then
            temp = vkflxls(dqm, my_2*dqp, eps) / dqm
          else
            temp = my_0
            write(*,*) 'Error: in reconstruction_cc::gsblimcc: invalid iflim:',&
                       iflim
            call lmpi_die
          end if

          phi(k,cell1) = max(my_0, min(phi(k,cell1), min(my_1,temp)))

        end if

      end do

! Compute the vector from the cell 2 center to the cell face center

      rx = x_face(n) - xc(cell2)
      ry = y_face(n) - yc(cell2)
      rz = z_face(n) - zc(cell2)

! Compute the cell 2 limiter coefficient

      eps = epscoef*(my_fact2*(my_fact1*cell_vol(cell2)/pi)**my_exp)**3

! Reconstruct to the cell 2 side of the interface

      gqr(1:5) = gradx(1:5,cell2)*rx+grady(1:5,cell2)*ry+gradz(1:5,cell2)*rz
      q2(1:5)  = qcell(1:5,cell2) + gqr(1:5)

! Compute the limiter coefficient array at cell 2 using a
! generalization of the Barth dual based limiting method

      do k = 1, 5

        if (q2(k) /= qcell(k,cell2)) then

          if (q2(k) > qcell(k,cell2)) then
            dqp = qmax(k,cell2) - qcell(k,cell2)
          else if (q2(k) < qcell(k,cell2))  then
            dqp = qmin(k,cell2) - qcell(k,cell2)
          end if

          dqm = q2(k) - qcell(k,cell2)
          dqp = my_half*dqp

          if (iflim == 13) then
            temp = minmods(dqm, omega*dqp) / dqm
          else if (iflim == 14) then
            temp = vlflxls(dqm, dqp) / dqm
          else if (iflim == 15) then
            temp = vaflxls(dqm, dqp, eps) / dqm
          else if (iflim == 16) then
            temp = smthlms(dqm, dqp, eps) / dqm
          else if (iflim == 17) then
            temp = vkflxls(dqm, my_2*dqp, eps) / dqm
          else
            temp = my_0
            write(*,*) 'Error: in reconstruction_cc::gsblimcc: invalid iflim:',&
                       iflim
            call lmpi_die
          end if

          phi(k,cell2) = max(my_0, min(phi(k,cell2), min(my_1,temp)))

        end if

      end do

    end do gradient_limiter_loop

! Modifiy the gradient limiter based on the local Mach no., Reynolds no. and
! the pressure gradient and laplacian

    call pressure_limiter_coeffcc(n_tot, ncell01, ncell0, nface, fptr,       &
                                  qcell, fl_coeff)

!   Loop over all the edges and calculate the pressure limiter effect on phi

    pcoefn = my_1
    pressure_limiter_loop: do n = 1, nface

!     Get location of the cells on opposite sides of the dual interface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

!     Compute the normalized undivided pressure ratio and
!     pressure Laplacian based limiter

      pr_limiter_coeff_g = pr_limiter_coeff
      pr_limiter_coeff = fl_coeff(n)
      if ( eqn_set == generic_gas ) then
        c_gen = my_half*(qcell(n_sonic_k(1),cell1)+qcell(n_sonic_k(1),cell2))
        if ( ivisc >= 2 ) then
          mu1 = qcell(n_amu_k(1),cell1)
          mu2 = qcell(n_amu_k(1),cell2)
        else
          mu1 = my_0
          mu2 = my_0
        end if
      else
        c_gen = my_0
        if ( ivisc >= 2 ) then
          xmr = xmach/re
          mu1 = viscosity_law(cstar,gamma*qcell(5,cell1)/qcell(1,cell1))*xmr
          mu2 = viscosity_law(cstar,gamma*qcell(5,cell2)/qcell(1,cell2))*xmr
        else
          mu1 = my_0
          mu2 = my_0
        end if
      end if
      mu = my_half*(mu1 + mu2)
      pcoef=pressure_limiter(xc(cell1),yc(cell1),zc(cell1),                    &
                             xc(cell2),yc(cell2),zc(cell2),                    &
                             xn_face(n),yn_face(n),zn_face(n),area_face(n),    &
                             cell_vol(cell1),cell_vol(cell2),                  &
                             qcell(1:ndim,cell1),qcell(1:ndim,cell2),          &
                             gradx(n_etot,cell1),grady(n_etot,cell1),          &
                             gradz(n_etot,cell1),gradx(n_etot,cell2),          &
                             grady(n_etot,cell2),gradz(n_etot,cell2),          &
                             ndim,  eqn_set, mu, gm1, viscous_terms, powervi,  &
                             my_0,my_1,my_2,my_1,0,c_gen)
      pr_limiter_coeff = pr_limiter_coeff_g

!     Use the min of the limiter coefficients

      pcoefn(cell1) = max(my_0, min(pcoef, pcoefn(cell1)))
      pcoefn(cell2) = max(my_0, min(pcoef, pcoefn(cell2)))

    end do pressure_limiter_loop

!   Modify the limiter coefficients as a function of
!   the Mach no. and cell Reynolds no. at the nodes

    if (wall_limit_less) then

      mach_no_dependency_loop: do n = 1, nface

        cell1 = fptr(1,n)
        cell2 = fptr(2,n)

!       Constrain the gradient and pressure limiter coeficients
!       in the subsonic portion of the flow

        if (cellmach(cell1) < my_1) then
          machf1 = max(my_0,min(my_1,(cellmach(cell1)-phi_min_mach)/dphi_mach))
          fmsmn1 = my_half*(cos(machf1*pi)+my_1)
          phi(:,cell1) = max(fmsmn1**powerir, phi(:,cell1))

          machf1 = max(my_0,min(my_1,(cellmach(cell1)-plc_min_mach)/dplc_mach))
          pcofn1 = my_half*(cos(machf1*pi)+my_1)
          pcoefn(cell1) = max(pcofn1, pcoefn(cell1))
        end if

        if (cellmach(cell2) < my_1) then
          machf2 = max(my_0,min(my_1,(cellmach(cell2)-phi_min_mach)/dphi_mach))
          fmsmn2 = my_half*(cos(machf2*pi)+my_1)
          phi(:,cell2) = max(fmsmn2**powerir, phi(:,cell2))

          machf2 = max(my_0,min(my_1,(cellmach(cell2)-plc_min_mach)/dplc_mach))
          pcofn2 = my_half*(cos(machf2*pi)+my_1)
          pcoefn(cell2) = max(pcofn2, pcoefn(cell2))
        end if

      end do mach_no_dependency_loop

    end if

!   Compute the composite limiter using the
!   min of the pressure and the gradient limiters

    composite_limiter_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

      do k = 1, 5
        phi(k,cell1) = min(pcoefn(cell1),phi(k,cell1))
        phi(k,cell2) = min(pcoefn(cell2),phi(k,cell2))
      end do

    end do composite_limiter_loop

!   Constrain the limiter coefficients when the cell Reynolds number is small

    if (wall_limit_less .and. ivisc >= 2) then
      cell_Re_no_dependency_loop: do n = 1, nface

        cell1 = fptr(1,n)
        cell2 = fptr(2,n)

        rho1    = qcell(1,cell1)
        press1  = qcell(5,cell1)
        t1      = gamma*press1/rho1
        a1      = sqrt(t1)
        u1      = cellmach(cell1)*a1
        diam1   = (my_fact1*cell_vol(cell1)/pi)**my_exp
        mu1     = viscosity_law( cstar, t1 )
        Re1     = rho1*u1*diam1/(mu1*xmr)
        vcoef1  = max(my_0, min(my_1, (Re1-Re_min)/dRe))
        vcoef1  = my_half*(cos(vcoef1*pi)+my_1)
        phi(:,cell1) = max(vcoef1**powervr, phi(:,cell1))

        rho2    = qcell(1,cell2)
        press2  = qcell(5,cell2)
        t2      = gamma*press2/rho2
        a2      = sqrt(t2)
        u2      = cellmach(cell2)*a2
        diam2   = (my_fact1*cell_vol(cell2)/pi)**my_exp
        mu2     = viscosity_law( cstar, t2 )
        Re2     = rho2*u2*diam2/(mu2*xmr)
        vcoef2  = max(my_0, min(my_1, (Re2-Re_min)/dRe))
        vcoef2  = my_half*(cos(vcoef2*pi)+my_1)
        phi(:,cell2) = max(vcoef2**powervr, phi(:,cell2))

      end do cell_Re_no_dependency_loop
    end if

! Enforce realizability based on the result of the reconstructed
! density and pressure

    real_recon_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

      rx1 = x_face(n) - xc(cell1)
      ry1 = y_face(n) - yc(cell1)
      rz1 = z_face(n) - zc(cell1)

      rx2 = x_face(n) - xc(cell2)
      ry2 = y_face(n) - yc(cell2)
      rz2 = z_face(n) - zc(cell2)

      do k = 1, 5, 4
        gqr(k) = gradx(k,cell1)*rx1 + grady(k,cell1)*ry1 + gradz(k,cell1)*rz1
        dq(k)  = qcell(k,cell2) - qcell(k,cell1)
          dq(k) = my_4th*((my_1-kappa_umuscl)*(my_4*gqr(k)-dq(k)) +            &
                          (my_1+kappa_umuscl)*dq(k))
        q1(k) = qcell(k,cell1) + phi(k,cell1)*dq(k)

        gqr(k) = gradx(k,cell2)*rx2 + grady(k,cell2)*ry2 + gradz(k,cell2)*rz2
        dq(k)  = qcell(k,cell1) - qcell(k,cell2)
          dq(k) = my_4th*((my_1-kappa_umuscl)*(my_4*gqr(k)-dq(k)) +            &
                          (my_1+kappa_umuscl)*dq(k))
        q2(k) = qcell(k,cell2) + phi(k,cell2)*dq(k)
      end do

      if ((cell1 <= ncell0) .and. ((q1(1) <= my_0) .or. (q1(5) <= my_0) .or.   &
                                   (q2(1) <= my_0) .or. (q2(5) <= my_0)))      &
      phi(1:5,cell1) = my_0
      if ((cell2 <= ncell0) .and. ((q1(1) <= my_0) .or. (q1(5) <= my_0) .or.   &
                                   (q2(1) <= my_0) .or. (q2(5) <= my_0)))      &
      phi(1:5,cell2) = my_0

    end do real_recon_loop

! Now if we want to freeze the limiter, evaluate the reconstruction using the
! original limiter.  If the reconstruction is successful, keep the original
! phi.  However, if it fails, use the new value of phi computed above.  In
! this way, we hope that the limiter will be frozen throughout the majority
! of the field, while it adjusts maybe occasionally here or there to keep the
! reconstruction from failing.

    frozen_limiter: if ( freeze_limiter_iteration >=0 .and. &
                         ntt > freeze_limiter_iteration ) then

      call test_reconstruct_frozen_lim_cc(ncell0,ncell01,nface,                &
                                          fptr,xc,yc,zc,cell_vol,              &
                                          x_face,y_face,z_face,                &
                                          gradx,grady,gradz,qcell,             &
                                          phi_original,phi,n_grd,ndim,n_tot)

! Now load phi with the original values, except for the ones
! that were reevaluated above

      phi = phi_original

    end if frozen_limiter

  end subroutine gsblimcc


!===================== TEST_RECONSTRUCTION_WITH_FROZEN_LIM_CC ================80
!
!  Test reconstruction with frozen limiter.  If reconstruction fails, replace
!  local values with updated limiter.
!
!=============================================================================80

  subroutine test_reconstruct_frozen_lim_cc(ncell0,ncell01,nface,              &
                                            fptr,xc,yc,zc,cell_vol,            &
                                            x_face,y_face,z_face,              &
                                            gradx,grady,gradz,qcell,           &
                                            phi,phi_new,n_grd,ndim,n_tot)

    use kinddefs,        only : dp
    use lmpi,            only : lmpi_reduce, lmpi_bcast
    use inviscid_flux,   only : flux_construction

    integer, intent(in) :: ncell0, ncell01, nface
    integer, intent(in) :: n_grd, ndim, n_tot

    integer,  dimension(6,nface),       intent(in)    :: fptr

    real(dp), dimension(n_grd,ncell01), intent(inout) :: phi
    real(dp), dimension(n_grd,ncell01), intent(in)    :: phi_new
    real(dp), dimension(ncell01),       intent(in)    :: xc, yc, zc
    real(dp), dimension(ncell01),       intent(in)    :: cell_vol
    real(dp), dimension(n_grd,ncell01), intent(in)    :: gradx, grady, gradz
    real(dp), dimension(n_tot,ncell01), intent(in)    :: qcell
    real(dp), dimension(nface),         intent(in)    :: x_face, y_face, z_face

    ! automatic array to mark cells that require the phi_new limiter
    logical, dimension(ncell0) :: cell_tag

    integer :: i, bad_cell_sum, global_bad_cell_sum, global_cell_sum

    real(dp)            :: percentage
    real(dp), parameter :: upper_limit = 100.0_dp

  continue

! Initialize the cell tag to false

    cell_tag = .false.

! Test the higher order reconstruction

    select case ( flux_construction )
    case ('vanleer','roe','dldfss')
      call test_frozen_limiter_cc(ncell0,ncell01,nface,fptr,                   &
                                  xc,yc,zc,cell_vol,x_face,y_face,z_face,      &
                                  gradx,grady,gradz,qcell,                     &
                                  phi,cell_tag,n_grd,ndim,n_tot)
    case default
      write(*,*) 'Uncoded option in test_reconstruct_frozen_limiter_cc'
      write(*,*) ' ..........flux_construction=',flux_construction
      call lmpi_die
    end select

! Count up the number of cells that went "bad" and reset their limiter

    bad_cell_sum = 0
    global_bad_cell_sum = 0

    do i = 1, ncell0
      if ( cell_tag(i) ) then
        bad_cell_sum = bad_cell_sum + 1
        phi(1,i) = phi_new(1,i)
        phi(2,i) = phi_new(2,i)
        phi(3,i) = phi_new(3,i)
        phi(4,i) = phi_new(4,i)
        phi(5,i) = phi_new(5,i)
      endif
    end do

    call lmpi_reduce(ncell0, global_cell_sum)
    call lmpi_bcast(global_cell_sum)
    call lmpi_reduce(bad_cell_sum, global_bad_cell_sum)
    call lmpi_bcast(global_bad_cell_sum)

    if ( lmpi_master .and. global_bad_cell_sum > 0 ) then
      percentage = 100.0_dp*real(global_bad_cell_sum, dp)/                     &
                            real(global_cell_sum, dp)
      percentage = min(percentage, upper_limit)
      write(*,'(7x,a28,f7.2,a30)') 'The limiter was replaced in ', percentage, &
                                   ' % of the frozen limiter cells'
    endif

  end subroutine test_reconstruct_frozen_lim_cc

!=============================== TEST_FROZEN_LIMITER_CC ======================80
!
!  Test higher order reconstructed states with frozen limiter
!
!=============================================================================80

  subroutine test_frozen_limiter_cc(ncell0,ncell01,nface,fptr,                 &
                                    xc,yc,zc,cell_vol,x_face,y_face,z_face,    &
                                    gradx,grady,gradz,qcell,                   &
                                    phi,cell_tag,n_grd,ndim,n_tot)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_3rd, my_1, my_2, my_3, my_4, my_6, pi
    use info_depr,       only : twod, epscoef, kappa_umuscl
    use inviscid_flux,   only : iflim

    integer, intent(in) :: ncell0, ncell01, nface
    integer, intent(in) :: n_grd, ndim, n_tot

    integer,  dimension(6,nface),       intent(in)    :: fptr

    real(dp), dimension(n_grd,ncell01), intent(inout) :: phi
    real(dp), dimension(ncell01),       intent(in)    :: xc, yc, zc
    real(dp), dimension(ncell01),       intent(in)    :: cell_vol
    real(dp), dimension(n_grd,ncell01), intent(in)    :: gradx, grady, gradz
    real(dp), dimension(n_tot,ncell01), intent(in)    :: qcell
    real(dp), dimension(nface),         intent(in)    :: x_face, y_face, z_face

    logical, dimension(ncell0),         intent(inout) :: cell_tag

    integer :: n, cell1, cell2

    real(dp) :: r1x, r1y, r1z, r2x, r2y, r2z, eps1, eps2
    real(dp) :: omega
    real(dp) :: my_exp, my_fact1, my_fact2, diam1, diam2

    real(dp), dimension(ndim+1) :: ql, qr

    logical :: second

  continue

    omega = my_2

! Test the limited reconstruction using the frozen limiter values

    second = .true.

    my_exp   = my_3rd
    my_fact1 = my_6
    my_fact2 = my_3
    if (.not. twod) then
      my_fact1 = my_4
      my_fact2 = my_1
    end if

!   Loop over all the faces

    face_loop: do n = 1, nface

      cell1 = fptr(1,n)
      cell2 = fptr(2,n)

!     Get the vectors from the cell center to the cell face center

      r1x = x_face(n) - xc(cell1)
      r1y = y_face(n) - yc(cell1)
      r1z = z_face(n) - zc(cell1)

      r2x = x_face(n) - xc(cell2)
      r2y = y_face(n) - yc(cell2)
      r2z = z_face(n) - zc(cell2)

!     Compute the smooth limiter epsilons if needed

      eps1 = my_1
      eps2 = my_1
      if ((iflim == 15) .or. (iflim == 16) .or. (iflim == 17)) then
        diam1 = (my_fact1*cell_vol(cell1)/pi)**my_exp
        diam2 = (my_fact1*cell_vol(cell2)/pi)**my_exp
        eps1  = (my_fact2*diam1)**3
        eps2  = (my_fact2*diam2)**3
      end if

!     Compute the reconstructed primitive variable states

      ql = qfcc(r1x,r1y,r1z,gradx(1:ndim,cell1),grady(1:ndim,cell1),           &
                gradz(1:ndim,cell1),qcell(1:ndim,cell1),qcell(1:ndim,cell2),   &
                phi(1:ndim,cell1),eps1,epscoef,kappa_umuscl,second,            &
                2,iflim,omega,ndim)

      qr = qfcc(r2x,r2y,r2z,gradx(1:ndim,cell2),grady(1:ndim,cell2),           &
                gradz(1:ndim,cell2),qcell(1:ndim,cell2),qcell(1:ndim,cell1),   &
                phi(1:ndim,cell2),eps2,epscoef,kappa_umuscl,second,            &
                2,iflim,omega,ndim)

!     Check for the occurance of reconstruction realizability violations
!     If they occur then tag the cell for re-evaluation of the limiter

      if((cell1 <= ncell0) .and.                                               &
        ((ql(ndim+1) /= my_0) .or. (qr(ndim+1) /= my_0)))cell_tag(cell1) =.true.
      if((cell2 <= ncell0) .and.                                               &
        ((ql(ndim+1) /= my_0) .or. (qr(ndim+1) /= my_0)))cell_tag(cell2) =.true.

    end do face_loop

  end subroutine test_frozen_limiter_cc


!============================= PRESSURE_LIMITER_COEFF ========================80
!
! This routine computes the local pressure limiter coefficient as a function
! of the maximum Mach number that occurs in the nodes spanned by the higher
! order reconstruction stencil
!
!=============================================================================80

  subroutine pressure_limiter_coeffcc(n_tot, ncell01, ncell0, nface, fptr,&
                                      qcell, pl_coeff)

    use kinddefs,        only : system_r8
    use fun3d_constants, only : my_0, my_half, my_1, pi
    use fluid,           only : gamma
    use info_depr,       only : pr_limiter_coeff
    use lmpi,            only : lmpi_max, lmpi_bcast
    use lmpi_app,        only : lmpi_maxnode, lmpi_xfer

    integer,                            intent(in)  :: n_tot
    integer,                            intent(in)  :: ncell01
    integer,                            intent(in)  :: ncell0
    integer,                            intent(in)  :: nface

    integer,  dimension(6,nface),       intent(in)  :: fptr

    real(dp), dimension(n_tot,ncell01), intent(in)  :: qcell

    real(dp), dimension(nface),         intent(out) :: pl_coeff

    integer  :: cell1, cell2, i

    real(dp) :: rho, u, v, w, q2, p
    real(dp) :: machcell1, machcell2, cell1mach, cell2mach, face_mach
    real(dp) :: max_mach_p, max_mach_g, pl_coeff_min
    real(system_r8) :: real_max_mach_p, real_max_mach_g ! avoid complexification

    real(dp), dimension(ncell0) :: max_stencil_mach

    real(dp), parameter :: upper_mach = 8.0_dp
    real(dp), parameter :: pl_coeff_max_l = my_half
    real(dp), parameter :: pl_coeff_max_g = pl_coeff_max_l

  continue

!   Loop over the nodes making up the higher order stencl used to reconstruct
!   the data at the interface

!   Initialize the level 1 values of max_stencil_mach to zero

    max_stencil_mach = my_0
    max_mach_p = my_0

!   Set the max Mach number to the local Mach number at each cell center

    do i = 1, ncell0
      rho = qcell(1,i)
      u   = qcell(2,i)
      v   = qcell(3,i)
      w   = qcell(4,i)
      q2  = u*u + v*v + w*w
      p   = qcell(5,i)
      max_stencil_mach(i) = sqrt(q2*rho/(gamma*p))
      max_mach_p = max(max_mach_p, max_stencil_mach(i))
    end do

! Determine and communicate the global max Mach number

    real_max_mach_p = max_mach_p
    call lmpi_max(real_max_mach_p, real_max_mach_g)
    max_mach_g = real_max_mach_g
    call lmpi_bcast(max_mach_g)

! Determine the minumum allowable pressure limiter coefficient

    pl_coeff_min = max(my_0, pl_coeff_max_g*my_half*(my_1 +                    &
                       cos(pi*(my_1 + max_mach_g/upper_mach))))
    pl_coeff_min = pr_limiter_coeff*pl_coeff_min

!   Loop over the faces to find the max Mach number of the stencil cell centers
!   that are on-processor

    do i = 1, nface

      cell1 = fptr(1,i)
      cell2 = fptr(2,i)

      machcell1 = max_stencil_mach(cell1)
      machcell2 = max_stencil_mach(cell2)

      rho = qcell(1,cell1)
      u   = qcell(2,cell1)
      v   = qcell(3,cell1)
      w   = qcell(4,cell1)
      q2  = u*u + v*v + w*w
      p   = qcell(5,cell1)
      cell1mach = sqrt(q2*rho/(gamma*p))

      rho = qcell(1,cell2)
      u   = qcell(2,cell2)
      v   = qcell(3,cell2)
      w   = qcell(4,cell2)
      q2  = u*u + v*v + w*w
      p   = qcell(5,cell2)
      cell2mach = sqrt(q2*rho/(gamma*p))

!     Find the max Mach numbers

      if (machcell1 > cell2mach .and. cell2 <= ncell0)                         &
      max_stencil_mach(cell2) = cell1mach

      if (machcell2 > cell1mach .and. cell1 <= ncell0)                         &
      max_stencil_mach(cell1) = cell2mach

    end do

! Look across processor boundaries to find the max of the local cell center
! and any off processor nodes that connect to it

    call lmpi_maxnode(max_stencil_mach)

! Communicate the max Mach number to the ghost nodes on adjacent processors

    call lmpi_xfer(max_stencil_mach)

!   Compute the limiter scaling coefficient based on the max edge stencil
!   Mach number

    pl_coeff = my_0
    do i = 1, nface
      cell1 = fptr(1,i)
      cell2 = fptr(2,i)
      face_mach = max(max_stencil_mach(cell1), max_stencil_mach(cell2))
      pl_coeff(i) = pl_coeff_min
      pl_coeff(i) = max(my_0, pl_coeff_max_l*my_half*(my_1 +                   &
                        cos(pi*(my_1 + face_mach/upper_mach))))
      if (face_mach >= upper_mach) pl_coeff(i) = pl_coeff_max_l
      pl_coeff(i) = max(pl_coeff_min, pl_coeff(i))
    end do

  end subroutine pressure_limiter_coeffcc

!   Statements to include the functions that are to be inlined.
!   This is necessary because not all compilers can inline
!   functions that are in a different module.
!   N.B.: The order of the statements must reflect
!         how they are nested in the routine(s)
!         they are invoked from.

  include 'viscosity_law.f90'
  include 'minmods.f90'
  include 'vlflxls.f90'
  include 'vaflxls.f90'
  include 'smthlms.f90'
  include 'vkflxls.f90'
  include 'pressure_limiter.f90'
  include 'pswitch.f90'
  include 'vswch_coef.f90'
  include 'vswch_coef_orig.f90'
  include 'qfcc.f90'
  include 'dqccm.f90'
  include 'minmodv.f90'
  include 'vlflxlv.f90'
  include 'vaflxlv.f90'
  include 'smthlmv.f90'

end module reconstruction_cc

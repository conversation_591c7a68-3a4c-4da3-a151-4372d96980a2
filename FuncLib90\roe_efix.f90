


!=============================== ROE_EFIX ====================================80
!
! Limit the magnitude of the eigenvalues in order to prevent entropy violations
! such as carbuncles and sonic rarefactions
!
!=============================================================================80

  pure function roe_efix(q2l, q2r, ubarl, ubarr, roewat, c, ubar_fs, scoef, eig)

    use kinddefs,          only : dp
    use info_depr,         only : adptv_entropy_fix
    use inviscid_flux,     only : rhs_a_eigenvalue_coef, rhs_u_eigenvalue_coef

    real(dp),               intent(in) :: q2l, q2r
    real(dp),               intent(in) :: ubarl, ubarr
    real(dp),               intent(in) :: roewat, ubar_fs, c
    real(dp),               intent(in) :: scoef
    real(dp), dimension(3), intent(in) :: eig
    real(dp), dimension(3)             :: roe_efix

    real(dp)               :: utngl, utngr, utang, maxeig
    real(dp)               :: aecoef, cecoef, fa, faeps, absfa
    real(dp), dimension(3) :: eigeps

    real(dp), parameter :: my_0    =  0.0_dp
    real(dp), parameter :: my_1    =  1.0_dp

  continue

    if (adptv_entropy_fix) then

!     Maximum possible eigenvalue(s)

      utngl  = sqrt(max(my_0, q2l-ubarl*ubarl))
      utngr  = sqrt(max(my_0, q2r-ubarr*ubarr))
      utang  = utngl*roewat + utngr*(my_1 - roewat)
      maxeig = max(abs( ubar_fs ), abs( utang )) + c

!     Acoustic eigenvalue limiter coefficent

!     Remove upper limit of 0.25 for rhs_a_eigenvalue_coef for all paths
!     aecoef    = min(my_4th, rhs_a_eigenvalue_coef*(my_1-scoef))
      aecoef    = rhs_a_eigenvalue_coef*(my_1-scoef)
      aecoef    = max(0.01_dp, min(my_1, aecoef))

!     Convective eigenvalue limiter coefficient

!     Remove upper limit of 0.50 for rhs_u_eigenvalue_coef for all paths
!     cecoef    = min(my_half, rhs_u_eigenvalue_coef*(my_1-scoef))
      cecoef    = rhs_u_eigenvalue_coef*(my_1-scoef)
      cecoef    = max(0.00001_dp, min(my_1, cecoef))

    else

!     Constant eigenvalue limiting

      fa    = ubar_fs
      faeps = 0.05_dp*c
      absfa = aharten(fa,faeps)

      maxeig = absfa + c

!     Or limit the eigenvalues as fraction of local maximum

!     Acoustic eigenvalue limiter coefficient

      aecoef = rhs_a_eigenvalue_coef

!     Convective eigenvalue limiter coefficient

      cecoef = rhs_u_eigenvalue_coef

    end if

!   maxeig = 0.5_dp*(sqrt(q2l) + sqrt(q2r)) + c ! Future option for generic gas

!   Acoustic eigenvalue limiters

    eigeps(1) = aecoef*maxeig
    eigeps(2) = aecoef*maxeig

!   Convective eigenvalue limiter

    eigeps(3) = cecoef*maxeig

!   Limit eigenvalues

    roe_efix(1) = aharten(eig(1),eigeps(1))
    roe_efix(2) = aharten(eig(2),eigeps(2))
    roe_efix(3) = aharten(eig(3),eigeps(3))

  end function roe_efix

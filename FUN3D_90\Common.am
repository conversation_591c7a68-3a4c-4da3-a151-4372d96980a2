libFUN3DFlow_SRCS = \
	composite_jacobian.f90 \
	composite_jacobian_util.f90 \
	defect_correction.f90 \
	flow.F90 \
	survey_matrix.f90

nodet_SRCS = \
	main.f90

nodet_LDSTUFF = \
	libFUN3DFlow.a \
	$(PHYSICS_DEPS_DIR)/libFUN3DPhysicsDeps.a \
	$(PHYSICS_DIR)/libFUN3DPhysics.a \
	$(LIBF90_DIR)/libsink.a \
	$(LIBINIT_DIR)/libinit.a \
	$(LIBDDFB_DIR)/libDDFB.a \
	$(LIBSMEMRD_DIR)/libsmemrd.a \
	$(LIBTURB_DIR)/libturb.a \
	$(LIBDEFS_DIR)/libdefs.a \
	$(ENGINESIM_DIR)/libenginesim.a

if BUILD_IRS_SUPPORT
nodet_LDSTUFF += -L@irslibrary@ -lirs
endif

if BUILD_SSDC_SUPPORT
nodet_LDSTUFF += -L@ssdclibrary@ -lssdc
endif

if BUILD_SFE_SUPPORT
nodet_LDSTUFF += -L@sfelibrary@ -lsfe
endif

if BUILD_SPARSKIT_SUPPORT
nodet_LDSTUFF += -L@sparskitlibrary@ -lskit
endif

nodet_LDSTUFF += $(LIBCORE_DIR)/libcore.a

if BUILD_PARMETIS_SUPPORT
nodet_LDSTUFF += @parmetis_ldadd@
endif

if BUILD_DIRTLIB_SUPPORT
if BUILD_MPI
nodet_LDSTUFF += \
	-L@dirtlibrary@ -ldirt_mpich -lp3d
else
nodet_LDSTUFF += \
	-L@dirtlibrary@ -ldirt -lp3d
endif
endif

if BUILD_SUGGAR_SUPPORT
if BUILD_MPI
nodet_LDSTUFF += \
	-L@suggarlibrary@ -lsuggar_mpi -lp3d -lexpat -lpthread -lstdc++
else
nodet_LDSTUFF += \
	-L@suggarlibrary@ -lsuggar -lp3d -lexpat -lpthread -lstdc++
endif
endif

if BUILD_DYMORE_SUPPORT
nodet_LDSTUFF += -L@dymorelibrary@ -ldymore4
endif

if BUILD_RCAS_SDX_SUPPORT
nodet_LDSTUFF += -L@sdxlibrary@ -lsdx
endif

if BUILD_MESHSIM_SUPPORT
nodet_LDSTUFF += @meshsim_ldadd@
endif

if BUILD_REFINE_SUPPORT
nodet_LDSTUFF += @refine_ldadd@
endif

if BUILD_TECIO_SUPPORT
nodet_LDSTUFF += @TECIOLIBS@
endif

if BUILD_CGNS_SUPPORT
nodet_LDSTUFF += -L@CGNSlibrary@ -lcgns
endif

if BUILD_SBOOM_SUPPORT
nodet_LDSTUFF += -L@SBOOMlibrary@ -lsboomadjoint
endif

if BUILD_PUNDIT_SUPPORT
nodet_LDSTUFF += -L@punditlibrary@ -lPUNDIT
endif

if BUILD_SIXDOF_SUPPORT
nodet_LDSTUFF += \
	-L@SIXDOFLIBS@/Motion/lib -lmo \
	-L@SIXDOFLIBS@/HT/lib -lht \
	-L@SIXDOFLIBS@/EXP/lib -lexp
endif

if BUILD_KNIFE_SUPPORT
nodet_LDSTUFF += @knife_ldadd@
endif

if BUILD_VISIT_SUPPORT
nodet_LDSTUFF += -L@VisItlibrary@ -lsimV2f -lsimV2 -ldl
endif

nodet_LDSTUFF += @F90_EXT_LIB@ @zoltan_ldadd@

if BUILD_CUDA_SUPPORT
nodet_LDSTUFF += -L@CUDA_LIB_PATH@ -lcudart -lcuda
endif

if BUILD_FORTRAN_C_INTEROP_SUPPORT
nodet_LDSTUFF += -lstdc++
endif

AM_FCFLAGS = \
	$(FC_MODINC)$(LIBCORE_DIR)\
	$(FC_MODINC)$(LIBDEFS_DIR)\
	$(FC_MODINC)$(LIBTURB_DIR) \
	$(FC_MODINC)$(LIBSMEMRD_DIR) \
	$(FC_MODINC)$(LIBINIT_DIR)\
	$(FC_MODINC)$(LIBF90_DIR) \
	$(FC_MODINC)$(PHYSICS_DIR) \
	$(FC_MODINC)$(PHYSICS_DEPS_DIR) \
	$(FC_MODINC)@top_builddir@

# remove *.mod *.fh when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT)  mpif.h *.time *.mod *.fh *.d
CLEANFILES += $(BUILT_SOURCES)

libFUN3DFlow_f90s=$(libFUN3DFlow_SRCS:.F90=.f90)
libFUN3DFlow_f90s+=$(nodet_SRCS:.F90=.f90)
libFUN3DFlow_deps=$(libFUN3DFlow_f90s:.f90=.d)

DISTCLEANFILES = $(libFUN3DFlow_deps)

SUFFIXES = .d

BUILT_SOURCES = $(libFUN3DFlow_deps)

-include $(libFUN3DFlow_deps)
include $(top_srcdir)/make.rules

CORE_LIBS = libcore.a
DEFS_LIBS = libdefs.a
INIT_LIBS = libinit.a
TURB_LIBS = libturb.a
SMEMRD_LIBS = libsmemrd.a
FUN3D_F90_LIBS = libsink.a

lib_MODULES = $(libFUN3DFlow_f90s:.f90=.$(FC_MODEXT))

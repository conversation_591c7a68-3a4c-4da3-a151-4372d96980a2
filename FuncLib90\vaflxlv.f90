


!=================================== VAFLXLV =================================80
!
! vanAlbada flux limiter applied to a vector
!
!=============================================================================80

  pure function vaflxlv(grad_a, grad_b, eps2, ndim)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_2

    integer,                   intent(in) :: ndim
    real(dp),                  intent(in) :: eps2
    real(dp), dimension(ndim), intent(in) :: grad_a, grad_b
    real(dp), dimension(ndim)             :: vaflxlv

  continue

    vaflxlv = (grad_a*(grad_b**2+eps2)+grad_b*(grad_a**2+eps2))/               &
              (grad_a**2+grad_b**2+my_2*eps2)

  end function vaflxlv

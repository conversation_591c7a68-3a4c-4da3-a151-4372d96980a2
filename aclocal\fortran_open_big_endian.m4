AC_DEFUN([AX_FORTRAN_OPEN_CONVERT_BIG],
[AC_CACHE_CHECK([fortran open for convert='big_endian'],
 ax_cv_fortran_open_convert_big,
 [AC_LANG_PUSH(Fortran)
  AC_COMPILE_IFELSE(
  [
       program main
       open(10,file='biggie.dat',convert='big_endian')
       end
  ],
  [ax_cv_fortran_open_convert_big=yes],
  [ax_cv_fortran_open_convert_big=no] 
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_open_convert_big" != 'no'
then
 AC_DEFINE([HAVE_OPEN_CONVERT_BIG],[1],[fortran open accepts convert=big_endian])
fi
])


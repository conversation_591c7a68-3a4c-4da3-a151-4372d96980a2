module wall_model

   use kinddefs,             only : dp
   use info_depr,            only : skeleton, ntt
   use wall_model_functions, only : kappa, k_1, omega_1

  implicit none

  private

    public :: wall_reynolds_stress
    public :: wall_function_reynolds_stress
    public :: update_wall_function_bc

    public :: wall_function_bc_info
    public :: return_dual_volume
    public :: update_wall_mu_t
    public :: get_element_qp,          get_element_qp_ddt

    public :: dlr_sa_f
    public :: dlr_kw_f
    public :: dlr_kw_bc

    public :: hybrid_wilcox_bc
    public :: wilcox_homogeneous_bc

    public :: spalart_bc

  contains

!========================== UPDATE_WALL_FUNCTION_BC ==========================80
!
!  Loop through all wall boundaries and update the effective turbulent
!  eddy viscosity due to using a wall function as well as the blended
!  omega boundary condition using Eqns. (19) - (22) in AIAA-2004-0581
!  "Wall Function Boundary Conditions Including Heat Transfer and
!  Compressibility for Transport Turbulence Models," R.<PERSON><PERSON> and
!  C.C. Nelson
!
!=============================================================================80
  subroutine update_wall_function_bc ( grid, soln, ib )

    use kinddefs,              only : dp
    use grid_types,            only : grid_type
    use solution_types,        only : soln_type
!   use bc_types,              only : bcgrid_type, bcsoln_type
    use info_depr,             only : xmach, re, twod
    use twod_util,             only : yplane_2d, y_coplanar_tol

    integer,                intent(in)    :: ib
    type(grid_type),        intent(in)    :: grid
    type(soln_type),        intent(inout) :: soln

    integer                :: node
    integer                :: inode
    real(dp)               :: xmr
    real(dp)               :: k_bc, omega_bc, mu_t_bc

  continue

    xmr = xmach / re

    if ( grid%bc(ib)%nbnode == 0 ) return

    do node = 1, grid%bc(ib)%nbnode

      inode = grid%bc(ib)%ibnode(node)
      if( (abs(grid%y(inode)-yplane_2d) >= y_coplanar_tol) .and. twod ) cycle

      call wall_function_bc_info ( grid, ib, node, xmr,                        &
                soln%bcsoln(ib)%u_tau(node),    soln%bcsoln(ib)%v_corr(node),  &
                soln%bcsoln(ib)%yplus_wf(node), soln%bcsoln(ib)%rho(node),     &
                soln%bcsoln(ib)%nu(node),       soln%bcsoln(ib)%phi_wf(node),  &
                k_bc,                           omega_bc,                      &
                mu_t_bc )

      soln%bcsoln(ib)%mu_t_wf(node)  = mu_t_bc
      soln%bcsoln(ib)%k_wf(node)     = k_bc
      soln%bcsoln(ib)%omega_wf(node) = omega_bc

    enddo

  end subroutine update_wall_function_bc

!============================== WALL_FUNCTION_BC_INFO ========================80
!
!  Determines the wall function boundary condition nodal values
!
!=============================================================================80
  subroutine wall_function_bc_info ( grid, ib, boundary_node, xmr,             &
                                u_tau, v_corrected, y_plus_visc, rho, nu, phi, &
                                k_bc, omega_bc, mu_t_bc )

    use ddt,                 only : assignment(=), operator(/)
    use grid_types,          only : grid_type
    use kinddefs,            only : dp
    use turb_kw_const,       only : beta1, cmu_0
    use turbulence_info,     only : wf_bc_int, remove_phi

    use wall_function_names, only : wilcox, wilcox_homogeneous,                &
                                    menter_omega_bc, wilcox_omega_bc,          &
                                    loglaw_omega_bc,                           &
                                    dlr, sa, spalart_homogeneous, spalart,     &
                                    dlr_alt, sttw

!   use solution_types,  only : compressible
!   use generic_gas_map, only : n_momx, n_momy, n_momz

    type(grid_type),                   intent(in)  :: grid
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: boundary_node
    real(dp),                          intent(in)  :: xmr
    real(dp),                          intent(in)  :: u_tau
    real(dp),                          intent(in)  :: v_corrected
    real(dp),                          intent(in)  :: y_plus_visc
    real(dp),                          intent(in)  :: rho
    real(dp),                          intent(in)  :: nu
    real(dp),                          intent(in)  :: phi
    real(dp),                          intent(out) :: k_bc
    real(dp),                          intent(out) :: omega_bc
    real(dp),                          intent(out) :: mu_t_bc

    real(dp)                                 :: rho_avg
    real(dp)                                 :: slen_wall

    real(dp)                                 :: omega_bc_menter
    real(dp)                                 :: omega_bc_wilcox
    real(dp)                                 :: omega_bc_log

    real(dp), parameter                      :: zero           = 0.0_dp
    real(dp), parameter                      :: ten            = 10.0_dp

  continue

    omega_bc      = zero
    k_bc          = zero
    mu_t_bc       = zero

    slen_wall     = grid%bc(ib)%slen_wall(boundary_node)

    omega_bc_wilcox  =  6.0_dp * nu / beta1 * ( xmr / slen_wall )**2
    omega_bc_menter  = ten * omega_bc_wilcox
    omega_bc_log     = abs(u_tau) * xmr / ( sqrt( cmu_0 ) * kappa * slen_wall )

!FIXME - bring in actual rho_avg somehow
    rho_avg = rho

    select case ( wf_bc_int )
    case ( wilcox_omega_bc )

      omega_bc   = omega_bc_wilcox
        k_bc     = zero
        mu_t_bc  = zero

    case ( sttw, menter_omega_bc )

      omega_bc   = omega_bc_menter
        k_bc     = zero
        mu_t_bc  = zero

    case ( loglaw_omega_bc )

      omega_bc   = omega_bc_log
        k_bc     = zero
        mu_t_bc  = zero

    case ( dlr )

      call dlr_kw_bc( omega_bc_wilcox, omega_bc_log, rho, y_plus_visc,         &
                      k_bc, omega_bc , mu_t_bc )

    case ( dlr_alt )

      call dlr_kw_bc( omega_bc_menter, omega_bc_log, rho, y_plus_visc,         &
                      k_bc, omega_bc , mu_t_bc )

    case ( wilcox )

      call hybrid_wilcox_bc( omega_bc_menter, omega_bc_log, xmr, u_tau, phi,   &
                      slen_wall, rho, rho_avg, nu, remove_phi,                 &
                      k_bc, omega_bc , mu_t_bc )

    case ( wilcox_homogeneous )

      call wilcox_homogeneous_bc( omega_bc_log, xmr, u_tau, phi, slen_wall,    &
                                  rho, rho_avg, nu,                            &
                                  k_bc, omega_bc , mu_t_bc )

    case ( spalart )

      call spalart_bc( xmr, u_tau, slen_wall, v_corrected, rho_avg, nu,        &
                         k_bc, mu_t_bc )

    case ( sa, spalart_homogeneous )

      k_bc       = zero
      mu_t_bc    = zero

    case default

      omega_bc = -1.0_dp
      mu_t_bc  = -1.0_dp
      k_bc     = -1.0_dp

    end select

      if ( skeleton > 20 ) then
        write(6,'(a,10(1x,f20.10))') 'omega:......' &
         , omega_bc_menter, omega_bc_wilcox, omega_bc
        write(6,'(a,i8,10(1x,f20.10))') 'bc_info =   ' &
         , boundary_node, slen_wall, mu_t_bc, omega_bc, k_bc, u_tau
      endif

  end subroutine wall_function_bc_info

!============================ WALL_REYNOLDS_STRESS ===========================80
!
! Closes off viscous flux on boundaries for the general (mixed) element case
! for compressible. It is the generalization of the original tetrahedral
! formulation to more general elements.
!
! Note: qnode assumed to contain primitive variables
!
!=============================================================================80
  function wall_reynolds_stress ( grid, q_dof, amut, ib,                       &
                                  face_index, nodes_per_face, xmr,             &
                                  model_strain_form_int, eqn_set,              &
                                  unorm_bc )                                   &
  result ( rhotauij )

    use grid_types,      only : grid_type
    use info_depr,       only : twod, tref
    use fluid,           only : gamma, sutherland_constant !, gm1, prandtl
    use generic_gas_map, only : n_momx, n_momy, n_momz
    use element_defs,    only : max_face_per_cell, max_node_per_cell
    use utilities,       only : cell_gradients_ddt
    use turbulence_info, only : traceless
    use bc_strong_nc,    only : wall_properties
    use ddt,             only : ddt5, assignment(=), operator(-)               &
                              , operator(*), operator(+), operator(/)          &
                              , ddt5_identity


    type(ddt5), dimension(3,3)                                 :: rhotauij

    type(grid_type),            intent(in) :: grid
    real(dp), dimension(:,:),   intent(in) :: q_dof
    real(dp), dimension(:),     intent(in) :: amut
    integer,                    intent(in) :: ib
    integer,                    intent(in) :: face_index
    integer,                    intent(in) :: nodes_per_face
    real(dp),                   intent(in) :: xmr
    integer,                    intent(in) :: model_strain_form_int
    integer,                    intent(in) :: eqn_set
    real(dp), dimension(:),     intent(in) :: unorm_bc

    integer, dimension(max_node_per_cell)  :: c2n_cell
    integer, dimension(max_node_per_cell)  :: wall
    integer, dimension(max_node_per_cell)  :: node_map

    integer                                    :: i, icell, ielem, face_2d
    integer                                    :: i_local
    integer                                    :: node
    integer                                    :: edges_local, nodes_local
    integer                                    :: corner_node
    integer                                    :: cell_node
    integer                                    :: boundary_node

    real(dp)                                   :: cell_vol
    real(dp)                                   :: fact
    real(dp)                                   :: cstar

    real(dp), dimension(max_face_per_cell)      :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node

    type(ddt5), dimension(max_node_per_cell)    :: rho_node
    type(ddt5), dimension(max_node_per_cell)    :: u_node, v_node, w_node
    type(ddt5), dimension(max_node_per_cell)    :: mu_node
    type(ddt5), dimension(max_node_per_cell)    :: T_node
    type(ddt5), dimension(max_node_per_cell)    :: p_node
    type(ddt5), dimension(5,max_node_per_cell)  :: q_node
    type(ddt5), dimension(5)                    :: qnode
    type(ddt5), dimension(5)                    :: gradx_cell, grady_cell
    type(ddt5), dimension(5)                    :: gradz_cell
    type(ddt5), dimension(3,3)                  :: gradv
    type(ddt5), dimension(3,3)                  :: sij
    type(ddt5), dimension(3,3)                  :: sijbar
    type(ddt5)                                  :: rmu
    type(ddt5)                                  :: rho_avg
    type(ddt5)                                  :: wall_temp

    real(dp), dimension(5)                      :: q
    real(dp), dimension(5)                      :: qp_exact
    real(dp)                                    :: heating
    real(dp)                                    :: t_old
    real(dp), dimension(max_node_per_cell)      :: wall_dxdt
    real(dp), dimension(max_node_per_cell)      :: wall_dydt
    real(dp), dimension(max_node_per_cell)      :: wall_dzdt

    real(dp),  parameter                   :: zero   = 0.0_dp
    real(dp),  parameter                   :: two    = 2.0_dp
    real(dp),  parameter                   :: third  = 1.0_dp/3.0_dp

  continue

    cstar     = sutherland_constant / tref
    heating   = zero

    icell = 0
    ielem = 0
    node  = 0

!  global node numbers of the cell/face nodes on the boundary
    select case ( nodes_per_face )
      case ( 3 )
        icell = grid%bc(ib)%f2ntb(face_index,4)
        ielem = grid%bc(ib)%f2ntb(face_index,5)
      case ( 4 )
        icell = grid%bc(ib)%f2nqb(face_index,5)
        ielem = grid%bc(ib)%f2nqb(face_index,6)
    end select

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

    do cell_node = 1, grid%elem(ielem)%node_per_cell
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    node_map(:) = 0

    if (twod) then

          face_2d = grid%elem(ielem)%face_2d

          nodes_local = 3
          if (grid%elem(ielem)%local_f2n(face_2d,1) /=                         &
              grid%elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = grid%elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (grid%elem(ielem)%local_f2e(face_2d,1) /=                         &
              grid%elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

    else

          nodes_local = grid%elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = grid%elem(ielem)%edge_per_cell

    end if

! Set flag to indicate which cell nodes correspond to wall nodes

    wall      = 0         ! default to no nodes in cell on a solid wall
    wall_dxdt = 0._dp  ! zero unless moving wall
    wall_dydt = 0._dp
    wall_dzdt = 0._dp

    select case ( nodes_per_face )
    case ( 3 )

      do corner_node = 1, nodes_per_face
        boundary_node = grid%bc(ib)%f2ntb(face_index,corner_node)
        node          = grid%bc(ib)%ibnode(boundary_node)
        do cell_node = 1, grid%elem(ielem)%node_per_cell
          if (c2n_cell(cell_node) == node) then
            wall(cell_node) = 1    ! node on a solid wall
            call wall_properties( ib, boundary_node, grid, eqn_set, 5          &
                                , qp_exact, heating, t_old, unorm_bc)
            wall_dxdt(cell_node) = qp_exact(2)
            wall_dydt(cell_node) = qp_exact(3)
            wall_dzdt(cell_node) = qp_exact(4)
          end if
        end do
      end do

!     wall_temp = qp_exact(5)

    case ( 4 )

      do corner_node = 1, nodes_per_face
        boundary_node = grid%bc(ib)%f2nqb(face_index,corner_node)
        node          = grid%bc(ib)%ibnode(boundary_node)
        do cell_node = 1, grid%elem(ielem)%node_per_cell
          if (c2n_cell(cell_node) == node) then
            wall(cell_node) = 1    ! node on a solid wall
            call wall_properties( ib, boundary_node, grid, eqn_set, 5          &
                                , qp_exact, heating, t_old, unorm_bc)
            wall_dxdt(cell_node) = qp_exact(2)
            wall_dydt(cell_node) = qp_exact(3)
            wall_dzdt(cell_node) = qp_exact(4)
          end if
        end do
      end do

!      wall_temp = qp_exact(5)

    end select

! Compute cell averages, cell center, and set up some local solution arrays

        rmu       = zero
        rho_avg   = zero
        qnode     = zero
        x_node    = zero
        y_node    = zero
        z_node    = zero
        u_node    = zero
        v_node    = zero
        w_node    = zero
        T_node    = zero
        p_node    = zero
        mu_node   = zero
        q_node    = zero

!-----------------------------------------------------------------------------80

        node_loop2 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          cell_node = c2n_cell(i)

          x_node(i) = grid%x(cell_node)
          y_node(i) = grid%y(cell_node)
          z_node(i) = grid%z(cell_node)

            q(1) = q_dof(1,cell_node)
            q(2) = q_dof(n_momx,cell_node)
            q(3) = q_dof(n_momy,cell_node)
            q(4) = q_dof(n_momz,cell_node)
            q(5) = q_dof(5,cell_node)

          qnode       = ddt5_identity(q)

          fact = real(wall(i), dp)    ! 1 if WEAK wall point, 0 otherwise

          qnode(2)%f = qnode(2)%f*(1._dp - fact) + fact*wall_dxdt(i)
          qnode(3)%f = qnode(3)%f*(1._dp - fact) + fact*wall_dydt(i)
          qnode(4)%f = qnode(4)%f*(1._dp - fact) + fact*wall_dzdt(i)

          rho_node(i) = qnode(1)
          u_node(i)   = qnode(2)
          v_node(i)   = qnode(3)
          w_node(i)   = qnode(4)
          p_node(i)   = qnode(5)

          wall_temp   = gamma*p_node(i)/rho_node(i)
          T_node(i)   = gamma*p_node(i)/rho_node(i)*(1._dp - fact)             &
                      + fact*wall_temp
          mu_node(i)  = amut(cell_node)
! use laminar viscosity instead
          mu_node(i)  = viscosity_law_ddt5 ( cstar, T_node(i) )
          rmu         = rmu     + mu_node(i)
          rho_avg     = rho_avg + rho_node(i)

        end do node_loop2

    if ( skeleton > 21 ) then
!     write(6,'(a,15(1x,f15.6))') ' rho_node    ', rho_node%f
      write(6,'(a,15(1x,f15.6))') ' u_node      ',        &
      u_node(1)%f, u_node(2)%f, u_node(3)%f, u_node(4)%f, &
      u_node(5)%f, u_node(6)%f, u_node(7)%f, u_node(8)%f
      write(6,'(a,15(1x,f15.6))') ' v_node      ',        &
      v_node(1)%f, v_node(2)%f, v_node(3)%f, v_node(4)%f, &
      v_node(5)%f, v_node(6)%f, v_node(7)%f, v_node(8)%f
      write(6,'(a,15(1x,f15.6))') ' w_node      ',        &
      w_node(1)%f, w_node(2)%f, w_node(3)%f, w_node(4)%f, &
      w_node(5)%f, w_node(6)%f, w_node(7)%f, w_node(8)%f
!     write(6,'(a,15(1x,f15.6))') ' p_node      ', p_node%f
!     write(6,'(a,15(1x,f15.6))') ' T_node      ', T_node%f
!     write(6,'(a,15(1x,f15.6))') 'mu_node      ', mu_node%f
    endif

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu     = rmu*fact
        rho_avg = rho_avg*fact

!-----------------------------------------------------------------------------80
! Get the gradients in the primal cell via Green-Gauss

        q_node(1,:) = rho_node(:)
        q_node(2,:) = u_node(:)
        q_node(3,:) = v_node(:)
        q_node(4,:) = w_node(:)
        q_node(5,:) = p_node(:)

        call cell_gradients_ddt( edges_local, max_node_per_cell,               &
                            grid%elem(ielem)%face_per_cell,                    &
                            x_node, y_node, z_node,                            &
                            5, q_node, grid%elem(ielem)%local_f2n,             &
                            grid%elem(ielem)%e2n_2d, gradx_cell, grady_cell,   &
                            gradz_cell, cell_vol, nx, ny, nz)

        gradv(1,1) = gradx_cell(2)
        gradv(2,1) = gradx_cell(3)
        gradv(3,1) = gradx_cell(4)

        gradv(1,2) = grady_cell(2)
        gradv(2,2) = grady_cell(3)
        gradv(3,2) = grady_cell(4)

        gradv(1,3) = gradz_cell(2)
        gradv(2,3) = gradz_cell(3)
        gradv(3,3) = gradz_cell(4)

    if ( skeleton > 21 ) then
      write(6,'(a,15(1x,f20.10))') 'ux,vx,wx     ', &
      gradv(1,1)%f, gradv(2,1)%f, gradv(3,1)%f
      write(6,'(a,15(1x,f20.10))') 'uy,vy,wy     ', &
      gradv(1,2)%f, gradv(2,2)%f, gradv(3,2)%f
      write(6,'(a,15(1x,f20.10))') 'uz,vz,wz     ', &
      gradv(1,3)%f, gradv(2,3)%f, gradv(3,3)%f
      write(6,'(a,15(1x,f20.10))') 'rmu          ', rmu
    endif

! components of symmetric stress tensor - traceless S_ij
        sij    = get_sij_ddt5 ( gradv )
        sijbar = sij
        if ( model_strain_form_int == traceless ) then
          sijbar(1,1) = sij(1,1) - third*(sij(1,1)+sij(2,2)+sij(3,3))
          sijbar(2,2) = sij(2,2) - third*(sij(1,1)+sij(2,2)+sij(3,3))
          sijbar(3,3) = sij(3,3) - third*(sij(1,1)+sij(2,2)+sij(3,3))
        endif

        ! [nondimensionalization factor xmr ] * [ viscosity ]
        ! [ unit normal and area ] at dual face

      rhotauij      = two * rmu * rho_avg * sijbar * xmr

!   write(6,'(a,2i6,15(1x,f20.10))') 'wf:.........  ', ib, boundary_node, &

  end function wall_reynolds_stress

!==================== WALL_FUNCTION_REYNOLDS_STRESS ==========================80
!
!  Determines the wall function boundary Reynolds stresses
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!  The function expects q_dof in primitive, compressible path variable type.
!
!=============================================================================80
       function wall_function_reynolds_stress ( grid, q_dof, amut,             &
                                  gradx, grady, gradz, ib,                     &
                                  face_index, face_corner,                     &
                                  nodes_per_face,                              &
                                  max_node_per_cell,        xmr,               &
                                  gamma, sutherland_constant, tref,            &
                                  turbulent_prandtl,                           &
                                  xnorm, ynorm, znorm, area,                   &
                                  eqn_set, unorm_bc,                           &
                                  u_tau_init,                                  &
                                  area_local, yplus_local, uplus_local,        &
                                  u_tau_local, nu_local, rho_local,            &
                                  v_corr_local, phi_local,                     &
                                  f1_nonconv_local,                            &
                                  f2_nonconv_local,                            &
                                  f3_nonconv_local,                            &
                                  f4_nonconv_local                             &
                                ) result ( momentum_flux )

    use bc_strong_nc,    only : wall_properties
    use ddt,             only : ddt5, operator (*), operator(/), assignment(=),&
                                operator(+), ddt_abs,          operator(-),    &
                                ddt_sqrt, operator(**), vdot_ddt, ddt5_identity
    use element_defs,    only : max_face_per_cell
    use generic_gas_map, only : n_momx, n_momy, n_momz
    use geometry_utils,  only : fetch_element_nodes
    use grid_types,      only : grid_type
    use info_depr,       only : twod
    use kinddefs,        only : dp
    use turbulence_info, only : u_tau_int, model_strain_form_int,              &
                                remove_phi, limit_phi, traceless
    use sampling_funclib, only : get_taul
    use utilities,       only : cell_gradients_ddt


    use wall_function_names, only : dlr_kw, dlr_sa, quartic, loglaw, viscous

    use wall_model_functions, only : corrected_velocity_ddt,                   &
                                     phi_scalar,                               &
                                     wilcox_loglaw_f,                          &
                                     loglaw_f,                                 &
                                     use_previous_utau

    real(dp), dimension(5)                         :: momentum_flux
    type(ddt5), dimension(3,3)                     :: rhotauij

    type(grid_type),                   intent(in)  :: grid
    real(dp),          dimension(:,:), intent(in)  :: q_dof
    real(dp),          dimension(:  ), intent(in)  :: amut
    real(dp),          dimension(:,:), intent(in)  :: gradx
    real(dp),          dimension(:,:), intent(in)  :: grady
    real(dp),          dimension(:,:), intent(in)  :: gradz
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: face_index
    integer,                           intent(in)  :: face_corner
    integer,                           intent(in)  :: nodes_per_face
    integer,                           intent(in)  :: max_node_per_cell
    real(dp),                          intent(in)  :: xmr
    real(dp),                          intent(in)  :: gamma
    real(dp),                          intent(in)  :: turbulent_prandtl
    real(dp),                          intent(in)  :: sutherland_constant
    real(dp),                          intent(in)  :: tref
    integer,                           intent(in)  :: eqn_set
    real(dp),                          intent(in)  :: xnorm
    real(dp),                          intent(in)  :: ynorm
    real(dp),                          intent(in)  :: znorm
    real(dp),                          intent(in)  :: area
    real(dp),          dimension(:  ), intent(in)  :: unorm_bc
    real(dp),                          intent(in)  :: u_tau_init
    real(dp), dimension(:),            intent(inout) :: area_local
    real(dp), dimension(:),            intent(inout) :: u_tau_local
    real(dp), dimension(:),            intent(inout) :: nu_local
    real(dp), dimension(:),            intent(inout) :: rho_local
    real(dp), dimension(:),            intent(inout) :: v_corr_local
    real(dp), dimension(:),            intent(inout) :: yplus_local
    real(dp), dimension(:),            intent(inout) :: uplus_local
    real(dp), dimension(:),            intent(inout) :: phi_local
    real(dp),                          intent(inout) :: f1_nonconv_local
    real(dp),                          intent(inout) :: f2_nonconv_local
    real(dp),                          intent(inout) :: f3_nonconv_local
    real(dp),                          intent(inout) :: f4_nonconv_local

    integer, dimension(max_node_per_cell)   :: c2n_cell
    integer, dimension(max_node_per_cell)   :: wall
    integer, dimension(max_node_per_cell)   :: node_map

    type(ddt5), dimension(5)                 :: q_vol_avg
    type(ddt5), dimension(5)                 :: q_bcface_avg
    type(ddt5), dimension(3)                 :: gradp_vd
    type(ddt5), dimension(3)                 :: vel_avg

    type(ddt5)                               :: tau_wall
    type(ddt5)                               :: v_corrected
    type(ddt5)                               :: rho_avg, p_avg, mu_avg, nu_avg
    type(ddt5)                               :: rho, t, t_avg
    type(ddt5)                               :: gradp
    type(ddt5)                               :: u_tau
    type(ddt5)                               :: u_tau_visc
    type(ddt5)                               :: u_tau_wf
    type(ddt5)                               :: slip_avg_mag
    type(ddt5)                               :: rho_bcface
    type(ddt5)                               :: p
    type(ddt5), dimension(3)                 :: vel_norm_avg
    type(ddt5), dimension(3)                 :: vel_slip_avg
    type(ddt5)                               :: slip_avg_sq
    type(ddt5)                 :: tau_wall_rs
    type(ddt5), dimension(max_node_per_cell)    :: rho_node
    type(ddt5), dimension(max_node_per_cell)    :: u_node, v_node, w_node
    type(ddt5), dimension(max_node_per_cell)    :: mu_node
    type(ddt5), dimension(max_node_per_cell)    :: T_node
    type(ddt5), dimension(max_node_per_cell)    :: p_node
    type(ddt5), dimension(5,max_node_per_cell)  :: q_node
    type(ddt5), dimension(5)                    :: qnode
    type(ddt5), dimension(5)                    :: gradx_cell, grady_cell
    type(ddt5), dimension(5)                    :: gradz_cell
    type(ddt5), dimension(3,3)                  :: gradv
    type(ddt5), dimension(3,3)                  :: sij
    type(ddt5), dimension(3,3)                  :: sijbar
    type(ddt5)                                  :: rmu
    type(ddt5)                                  :: wall_temp

    real(dp), dimension(max_face_per_cell)   :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)   :: x_node, y_node, z_node
    real(dp), dimension(0:max_node_per_cell) :: px, py, pz
    real(dp), dimension(3)                   :: components
    real(dp), dimension(3)                   :: normal
    real(dp), dimension(3)                   :: x2
    real(dp), dimension(3)                   :: gradx_local
    real(dp), dimension(3)                   :: grady_local
    real(dp), dimension(3)                   :: gradz_local
    real(dp), dimension(3,3)                 :: tau_test
    real(dp)                                 :: y_plus_visc
    real(dp)                                 :: cell_vol
    real(dp)                                 :: fact
    real(dp)                                 :: areax, areay, areaz
    real(dp)                                 :: nodes_bc_r
    real(dp)                                 :: nodes_vol_r

    real(dp)                                 :: distance
    real(dp)                                 :: slen_wall
    real(dp)                                 :: gm1
    real(dp)                                 :: cstar

    integer                                  :: icell, ielem
    integer                                  :: i, i_local, node
    integer                                  :: face_2d
    integer                                  :: nodes_local
    integer                                  :: edges_local
    integer                                  :: nodes_bc
    integer                                  :: nodes_vol
    integer                                  :: cell_node
    integer                                  :: corner_node
    integer                                  :: boundary_node
    integer                                  :: bnode

    real(dp), dimension(5)                   :: q
    real(dp), dimension(5)                   :: qp_exact
    real(dp)                                 :: heating
    real(dp)                                 :: t_old
    real(dp), dimension(max_node_per_cell)   :: wall_dxdt
    real(dp), dimension(max_node_per_cell)   :: wall_dydt
    real(dp), dimension(max_node_per_cell)   :: wall_dzdt
    real(dp)                                 :: x_bc, y_bc, z_bc
    real(dp)                                 :: x_avg, y_avg, z_avg
    real(dp)                                 :: vf2, vf3, vf4
    real(dp)                                 :: u_tau_first
    real(dp)                                 :: phi
    real(dp)                                 :: u_plus
    real(dp)                                 :: y_plus

    real(dp), parameter                      :: zero       = 0.0_dp
    real(dp), parameter                      :: onequarter = 0.25_dp
    real(dp), parameter                      :: third      = 1.0_dp/3.0_dp
    real(dp), parameter                      :: half       = 0.5_dp
    real(dp), parameter                      :: one        = 1.0_dp
    real(dp), parameter                      :: two        = 2.0_dp

!   logical                                :: laminar_node

  continue

    gm1           = gamma - one
    cstar         = sutherland_constant / tref
    heating       = zero

    vel_avg          = zero
    f1_nonconv_local = 0
    f2_nonconv_local = 0
    f3_nonconv_local = 0
    f4_nonconv_local = 0
    momentum_flux    = 0

!   get the element type and cell index associated
!   with the boundary face from the face-to-node array
    icell = 0
    ielem = 0

    select case( nodes_per_face )
      case( 3 )
        icell         = grid%bc(ib)%f2ntb(face_index,4)
        ielem         = grid%bc(ib)%f2ntb(face_index,5)
        boundary_node = grid%bc(ib)%f2ntb(face_index,mod(face_corner+0-1,3)+1)
      case( 4 )
        icell         = grid%bc(ib)%f2nqb(face_index,5)
        ielem         = grid%bc(ib)%f2nqb(face_index,6)
        boundary_node = grid%bc(ib)%f2nqb(face_index,mod(face_corner+0-1,4)+1)
    end select

!   distance according to the code based on nodes
    areax    = -xnorm*area
    areay    = -ynorm*area
    areaz    = -znorm*area
    normal   = -(/xnorm, ynorm, znorm/)
    distance = get_element_slen ( grid, ib, face_index, nodes_per_face, normal )
    slen_wall = grid%bc(ib)%slen_wall(boundary_node) ! alternate distance

!      Set some loop indicies and local mapping
!      arrays depending on whether we are doing
!      a 2D case or a 3D case.
!      Copy c2n and local_f2n arrays from the
!      derived type so as to minimize references to
!      derived types inside loops as much as possible

    do cell_node = 1, grid%elem(ielem)%node_per_cell
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    node_map(:) = 0

    if (twod) then

          face_2d = grid%elem(ielem)%face_2d

          nodes_local = 3
          if (grid%elem(ielem)%local_f2n(face_2d,1) /=                         &
              grid%elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = grid%elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (grid%elem(ielem)%local_f2e(face_2d,1) /=                         &
              grid%elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

    else

          nodes_local = grid%elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = grid%elem(ielem)%edge_per_cell

    end if

! Set flag to indicate which cell nodes correspond to wall nodes

    wall      = 0         ! default to no nodes in cell on a solid wall
    wall_dxdt = 0._dp  ! zero unless moving wall
    wall_dydt = 0._dp
    wall_dzdt = 0._dp

    select case ( nodes_per_face )
    case ( 3 )

      do corner_node = 1, nodes_per_face
        bnode         = grid%bc(ib)%f2ntb(face_index,corner_node)
        node          = grid%bc(ib)%ibnode(bnode)
        do cell_node = 1, grid%elem(ielem)%node_per_cell
          if (c2n_cell(cell_node) == node) then
            wall(cell_node) = 1    ! node on a solid wall
            call wall_properties( ib, bnode, grid, eqn_set, 5                  &
                                , qp_exact, heating, t_old, unorm_bc)
            wall_dxdt(cell_node) = qp_exact(2)
            wall_dydt(cell_node) = qp_exact(3)
            wall_dzdt(cell_node) = qp_exact(4)
          end if
        end do
      end do

    case ( 4 )

      do corner_node = 1, nodes_per_face
        bnode         = grid%bc(ib)%f2nqb(face_index,corner_node)
        node          = grid%bc(ib)%ibnode(bnode)
        do cell_node = 1, grid%elem(ielem)%node_per_cell
          if (c2n_cell(cell_node) == node) then
            wall(cell_node) = 1    ! node on a solid wall
            call wall_properties( ib, bnode, grid, eqn_set, 5                  &
                                , qp_exact, heating, t_old, unorm_bc)
            wall_dxdt(cell_node) = qp_exact(2)
            wall_dydt(cell_node) = qp_exact(3)
            wall_dzdt(cell_node) = qp_exact(4)
          end if
        end do
      end do

!      wall_temp = qp_exact(5)

    end select

! Compute cell averages, cell center, and set up some local solution arrays

        rmu       = zero
!       rho_avg   = zero
        qnode     = zero
        x_node    = zero
        y_node    = zero
        z_node    = zero
        u_node    = zero
        v_node    = zero
        w_node    = zero
        T_node    = zero
        p_node    = zero
        mu_node   = zero
        q_node    = zero
        gradp_vd  = zero
        x_bc      = zero
        y_bc      = zero
        z_bc      = zero
        x_avg     = zero
        y_avg     = zero
        z_avg     = zero

        q_bcface_avg    = zero
        q_vol_avg       = zero

        nodes_bc  = 0
        nodes_vol = 0
!       laminar_node = .false.

!-----------------------------------------------------------------------------80

        node_loop2 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          cell_node = c2n_cell(i)

          x_node(i)    = grid%x(cell_node)
          y_node(i)    = grid%y(cell_node)
          z_node(i)    = grid%z(cell_node)

            q(1) = q_dof(1,cell_node)
            q(2) = q_dof(n_momx,cell_node)
            q(3) = q_dof(n_momy,cell_node)
            q(4) = q_dof(n_momz,cell_node)
            q(5) = q_dof(5,cell_node)

          qnode       = ddt5_identity(q)

          fact = real(wall(i), dp)    ! 1 if WEAK wall point, 0 otherwise

          qnode(2)%f = qnode(2)%f*(1._dp - fact) + fact*wall_dxdt(i)
          qnode(3)%f = qnode(3)%f*(1._dp - fact) + fact*wall_dydt(i)
          qnode(4)%f = qnode(4)%f*(1._dp - fact) + fact*wall_dzdt(i)

          rho_node(i) = qnode(1)
          u_node(i)   = qnode(2)
          v_node(i)   = qnode(3)
          w_node(i)   = qnode(4)
          p_node(i)   = qnode(5)

          wall_temp   = gamma*p_node(i)/rho_node(i)
          T_node(i)   = gamma*p_node(i)/rho_node(i)*(1._dp - fact)             &
                      + fact*wall_temp
          mu_node(i)  = amut(cell_node)
! use laminar viscosity instead
          mu_node(i)  = viscosity_law_ddt5 ( cstar, T_node(i) )
          rmu         = rmu     + mu_node(i)
!         rho_avg     = rho_avg + rho_node(i)

         gradp_vd(1) = gradp_vd(1) + gradx(5,cell_node)
         gradp_vd(2) = gradp_vd(2) + grady(5,cell_node)
         gradp_vd(3) = gradp_vd(3) + gradz(5,cell_node)
!        if ( grid%iflagslen(node) < 0 ) laminar_node = .true.

         if ( wall(i) == 1 ) then
           nodes_bc = nodes_bc + 1
           x_bc     = x_bc + x_node(i)
           y_bc     = y_bc + y_node(i)
           z_bc     = z_bc + z_node(i)
           q_bcface_avg(1:5) = q_bcface_avg(1:5) + qnode(1:5)
         else
           nodes_vol = nodes_vol + 1
           x_avg     = x_avg    + x_node(i)
           y_avg     = y_avg    + y_node(i)
           z_avg     = z_avg    + z_node(i)
           q_vol_avg(1:5) = q_vol_avg(1:5) + qnode(1:5)
         end if

        end do node_loop2

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact        = 1._dp / real(nodes_local, dp)
        nodes_bc_r  = 1._dp / real(nodes_bc, dp)
        nodes_vol_r = 1._dp / real(nodes_vol, dp)

        rmu        = rmu      * fact
!       rho_avg    = rho_avg  * fact
        x_bc       = x_bc     * nodes_bc_r
        y_bc       = y_bc     * nodes_bc_r
        z_bc       = z_bc     * nodes_bc_r
        x_avg      = x_avg    * nodes_vol_r
        y_avg      = y_avg    * nodes_vol_r
        z_avg      = z_avg    * nodes_vol_r
        if ( twod ) then
          y_bc     = zero
          y_avg     = zero
        end if
        q_bcface_avg = q_bcface_avg * nodes_bc_r
        q_vol_avg    = q_vol_avg    * nodes_vol_r
        gradp_vd     = gradp_vd     * nodes_vol_r

      rho_bcface      = q_bcface_avg(1)

      rho_avg     = q_vol_avg(1)
      p_avg       = q_vol_avg(5)
      t_avg       = gamma * p_avg / rho_avg
!-----------------------------------------------------------------------------80
! Get the gradients in the primal cell via Green-Gauss

        q_node(1,:) = rho_node(:)
        q_node(2,:) = u_node(:)
        q_node(3,:) = v_node(:)
        q_node(4,:) = w_node(:)
        q_node(5,:) = p_node(:)

        call cell_gradients_ddt( edges_local, max_node_per_cell,               &
                            grid%elem(ielem)%face_per_cell,                    &
                            x_node, y_node, z_node,                            &
                            5, q_node, grid%elem(ielem)%local_f2n,             &
                            grid%elem(ielem)%e2n_2d, gradx_cell, grady_cell,   &
                            gradz_cell, cell_vol, nx, ny, nz)

        gradv(1,1) = gradx_cell(2)
        gradv(2,1) = gradx_cell(3)
        gradv(3,1) = gradx_cell(4)

        gradv(1,2) = grady_cell(2)
        gradv(2,2) = grady_cell(3)
        gradv(3,2) = grady_cell(4)

        gradv(1,3) = gradz_cell(2)
        gradv(2,3) = gradz_cell(3)
        gradv(3,3) = gradz_cell(4)

    if ( skeleton > 21 ) then
      write(6,'(a,15(1x,f20.10))') 'ux,vx,wx     ', &
      gradv(1,1)%f, gradv(2,1)%f, gradv(3,1)%f
      write(6,'(a,15(1x,f20.10))') 'uy,vy,wy     ', &
      gradv(1,2)%f, gradv(2,2)%f, gradv(3,2)%f
      write(6,'(a,15(1x,f20.10))') 'uz,vz,wz     ', &
      gradv(1,3)%f, gradv(2,3)%f, gradv(3,3)%f
      write(6,'(a,15(1x,f20.10))') 'rmu          ', rmu
    endif

! components of symmetric stress tensor - traceless S_ij
        sij    = get_sij_ddt5 ( gradv )
        sijbar = sij
        if ( model_strain_form_int == traceless ) then
          sijbar(1,1) = sij(1,1) - third*(sij(1,1)+sij(2,2)+sij(3,3))
          sijbar(2,2) = sij(2,2) - third*(sij(1,1)+sij(2,2)+sij(3,3))
          sijbar(3,3) = sij(3,3) - third*(sij(1,1)+sij(2,2)+sij(3,3))
        endif

        ! [nondimensionalization factor xmr ] * [ viscosity ]
        ! [ unit normal and area ] at dual face

      rhotauij         = two * rmu * rho_bcface * sijbar * xmr

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!   Place element geometry information in the local arrays (px,py,pz)
    call fetch_element_nodes( grid, ib, face_index, nodes_per_face, px, py, pz )
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    x2           = zero

    ! calculate the slip velocity on boundary element
    vel_avg(1)   = q_vol_avg(2) - qp_exact(2)
    vel_avg(2)   = q_vol_avg(3) - qp_exact(3)
    vel_avg(3)   = q_vol_avg(4) - qp_exact(4)

    vel_norm_avg = vdot_ddt(vel_avg,normal)
    vel_slip_avg = vel_avg - vel_norm_avg*normal
    slip_avg_sq  = vdot_ddt(vel_slip_avg,vel_slip_avg)
    slip_avg_mag = ddt_sqrt(slip_avg_sq)

if ( skeleton > 20 ) then
write(6,'(a,15(1x,f20.10))') 'vel_avg:......',vel_avg(1:3)%f
write(6,'(a,15(1x,f20.10))') 'vel_slip_avg:1',vel_slip_avg(1)
write(6,'(a,15(1x,f20.10))') 'vel_slip_avg:2',vel_slip_avg(2)
write(6,'(a,15(1x,f20.10))') 'vel_slip_avg:3',vel_slip_avg(3)
end if

    p            = p_avg
    t            = t_avg - half*gm1*turbulent_prandtl*slip_avg_sq
    rho          = rho_avg * ( t_avg / t )
    mu_avg       = viscosity_law_ddt5 ( cstar, t )
    nu_avg       = mu_avg / rho
    v_corrected  = corrected_velocity_ddt ( gamma, turbulent_prandtl, t        &
                                         , slip_avg_mag )

    tau_wall = zero

    singular_point:  if ( abs(slip_avg_sq%f) > zero ) then

      x2         = vel_slip_avg / ddt_sqrt(slip_avg_sq)

      gradp       = x2(1)*gradx_cell(5)  &
                  + x2(2)*grady_cell(5)  &
                  + x2(3)*gradz_cell(5)

      gradx_local(1:3) = gradx_cell(2:4)%f
      grady_local(1:3) = grady_cell(2:4)%f
      gradz_local(1:3) = gradz_cell(2:4)%f

      tau_test    = get_taul ( gradx_local(1:3),                               &
                               grady_local(1:3),                               &
                               gradz_local(1:3))
      components  = matmul( tau_test, normal )
      tau_wall_rs = rmu*dot_product(x2,components)
      u_tau_visc  = ddt_sqrt(ddt_abs(tau_wall_rs)/rho_avg)*sqrt(xmr)
      y_plus_visc = u_tau_visc * slen_wall    / ( nu_avg * xmr )

      u_tau = ddt_sqrt(v_corrected * nu_avg * xmr / slen_wall )

    if ( skeleton > 20 ) then
    write(6,'(a,15(1x,f20.10))') 'tauij(1,1)      ', rhotauij(1,1)
    write(6,'(a,15(1x,f20.10))') 'tauij(1,2)      ', rhotauij(1,2)
    write(6,'(a,15(1x,f20.10))') 'tauij(1,3)      ', rhotauij(1,3)
    write(6,'(a,15(1x,f20.10))') 'tauij(2,1)      ', rhotauij(2,1)
    write(6,'(a,15(1x,f20.10))') 'tauij(2,2)      ', rhotauij(2,2)
    write(6,'(a,15(1x,f20.10))') 'tauij(3,3)      ', rhotauij(3,3)
    write(6,'(a,15(1x,f20.10))') 'components:     ', components(1:3)
    write(6,'(a,15(1x,f20.10))') 'x2:             ', x2
    write(6,'(a,15(1x,f20.10))') 'xyznorm:        ', xnorm,ynorm,znorm
    write(6,'(a,15(1x,f20.10))') 'tau_wall_rs..',tau_wall_rs%f
    write(6,'(a,15(1x,f20.10))') 'u_tau_visc...',u_tau_visc%f
    write(6,'(a,15(1x,f20.10))') 'u_tau........',u_tau%f
    write(6,'(a,15(1x,f20.10))') 'y_plus_visc..',y_plus_visc
    end if

!-----------------------------------------------------------------------------80
if (  skeleton > 22 ) then

    write(*,*)
write(*,*)
write(6,'(a,3i5,15(1x,es15.5))') '                ', ib, face_index, face_corner
    write(6,'(a,10(1x,f15.7))') 'normal          ', normal
    write(6,'(a,10(1x,f15.7))') '              x2', x2
    write(6,'(a ,3(1x,f15.7))') 'slip     ',vel_slip_avg%f
    write(6,'(a ,3(1x,f15.7))') 'v_cor    ',v_corrected%f
    write(6,'(a ,3(1x,f15.7))') 'gradp    ',gradp_vd%f
    write(6,'(a ,3(1x,f15.7))') 'dpdx2    ',gradp%f
    write(6,'(a ,3(1x,f15.7))') 'slen_wall',slen_wall
    write(6,'(a ,3(1x,f15.7))') 'd        ',distance
    write(6,'(a ,3(1x,f15.7))') 'nu_avg   ',nu_avg%f
    write(6,'(a ,3(1x,f15.7))') 'rho      ',rho_avg%f
    write(6,'(a ,3(1x,f15.7))') 'xmr      ',xmr
    write(6,'(a,15(1x,f15.7))') 'volume dual:  ', rho_avg%f, vel_avg%f,p_avg%f
    write(6,'(a,15(1x,f15.7))') 'corrected:    ', rho%f, v_corrected%f,p%f

endif

!-----------------------------------------------------------------------------80
!   gradp       = gradp_vd(1)*x2(1) + gradp_vd(2)*x2(2) + gradp_vd(3)*x2(3)

    u_tau_first = u_tau_visc
    if ( use_previous_utau .and. ntt > 1 ) u_tau_first = u_tau_init
!-----------------------------------------------------------------------------80
    u_tau     = u_tau_visc
    ! u_tau blending
    select case ( u_tau_int )
    case ( dlr_sa )

      u_tau = dlr_sa_f ( xmr, v_corrected%f, slen_wall, rho%f, mu_avg%f,       &
                         u_tau_first )

    case ( dlr_kw )

      u_tau = dlr_kw_f ( xmr, v_corrected%f, slen_wall, rho%f, mu_avg%f,       &
                         u_tau_first )

    case ( loglaw )

      u_tau = loglaw_f ( xmr, v_corrected%f, slen_wall, rho%f, mu_avg%f,       &
                      u_tau_first )

    case ( quartic )

      u_tau_wf  = wilcox_loglaw_f ( xmr, v_corrected%f, slen_wall, rho%f,      &
                                 mu_avg%f, gradp%f, u_tau_first,               &
                                 remove_phi, limit_phi )
      u_tau    = u_tau_visc**4 + u_tau_wf**4
      if ( u_tau%f > tiny(0.0_dp) )  u_tau = u_tau**onequarter

    case ( viscous )

      u_tau  = u_tau_visc

    case default

      u_tau       = zero

    end select

! If inside a specificed laminar flow region, reset u_tau using
! uplus = yplus
!   if ( turb_transition .and. laminar_node )                                  &
!   u_tau = ddt_sqrt(v_corrected * nu_avg * xmr / distance )

    tau_wall =   rho * u_tau * u_tau
    phi      = phi_scalar ( xmr, rho_avg%f, gradp%f, u_tau%f, nu_avg%f )
    y_plus   = u_tau * slen_wall    / ( nu_avg * xmr )
    u_plus   = v_corrected / u_tau

!-----------------------------------------------------------------------------80

if ( skeleton > 31 ) then
    write(6,'(a,3i5,15(1x,es15.5))') '          ', &
    ib, face_index, face_corner, x2
    write(6,'(a,15(1x,f20.10))') 'distance        ', distance
    write(6,'(a,15(1x,f20.10))') 't               ', t%f
    write(6,'(a,15(1x,f20.10))') 'rho             ', rho%f
    write(6,'(a,15(1x,f20.10))') 't               ', t%f
    write(6,'(a,15(1x,f20.10))') 'rho             ', rho%f
    write(6,'(a,15(1x,f20.10))') 'mu_avg          ', mu_avg%f
    write(6,'(a,15(1x,f20.10))') 'vel_slip_avg     ', &
    vel_slip_avg(1)%f, vel_slip_avg(2)%f, vel_slip_avg(3)%f
    write(6,'(a,15(1x,f20.10))') 'xyznorm         ', xnorm, ynorm, znorm
    write(6,'(a,15(1x,f20.10))') 'u_tau_visc      ', u_tau_visc
    write(6,'(a,15(1x,f20.10))') 'y_plus_visc     ', y_plus_visc
    write(6,'(a,15(1x,f20.10))') 'wf-y_plus     ', y_plus
    write(6,'(a,15(1x,f20.10))') 'wf-u_plus     ', u_plus
    write(6,'(a,15(1x,f20.10))') 'wf-vel_avg     ', vel_avg%f
    write(6,'(a,15(1x,f20.10))') 'wf-v_corr     ', v_corrected%f
    write(6,'(a,15(1x,f20.10))') 'u_tau         ', u_tau
    write(6,'(a,12(1x,f20.10))') 'tau_wall_rs:..',tau_wall_rs%f
    write(*,'(a,12(f25.15))') 'p        ',p
    write(*,'(a,12(f25.15))') ' area    ',areax, areay, areaz, area
  endif

    end if singular_point

    vf2 = areax*rhotauij(1,1)%f + areay*rhotauij(1,2)%f + areaz*rhotauij(1,3)%f
    vf3 = areax*rhotauij(2,1)%f + areay*rhotauij(2,2)%f + areaz*rhotauij(2,3)%f
    vf4 = areax*rhotauij(3,1)%f + areay*rhotauij(3,2)%f + areaz*rhotauij(3,3)%f

    momentum_flux(2) = area * tau_wall%f * x2(1)
    momentum_flux(3) = area * tau_wall%f * x2(2)
    momentum_flux(4) = area * tau_wall%f * x2(3)

 if ( skeleton > 22 ) then
write(*,*)
  write(6,'(a,15(1x,f20.10))') 'flux 2:.....    ', vf2, momentum_flux(2)
  write(6,'(a,15(1x,f20.10))') 'flux 3:.....    ', vf3, momentum_flux(3)
  write(6,'(a,15(1x,f20.10))') 'flux 4:.....    ', vf4, momentum_flux(4)
 endif

      area_local(boundary_node)   = area_local(boundary_node)  + area
      u_tau_local(boundary_node)  = u_tau_local(boundary_node) + area*u_tau%f
      nu_local(boundary_node)     = nu_local(boundary_node)    + area*nu_avg%f
      rho_local(boundary_node)    = rho_local(boundary_node)   + area*rho%f
      v_corr_local(boundary_node) = v_corr_local(boundary_node)                &
                                                        + area*v_corrected%f
      uplus_local(boundary_node)  = uplus_local(boundary_node) + area*u_plus
      yplus_local(boundary_node)  = yplus_local(boundary_node) + area*y_plus
      phi_local(boundary_node)    = phi_local(boundary_node)   + area*phi
!   write(6,'(a,2i6,15(1x,f20.10))') 'wf:.........  ', ib, boundary_node, &
!    area, u_tau%f, nu_avg%f,rho%f,v_corrected%f,u_plus,y_plus,phi

  end function wall_function_reynolds_stress

!=========================== RETURN_DUAL_VOLUME ==============================80
!
!=============================================================================80
       function return_dual_volume   ( x, y, z, bc, nelem, elem                &
                             , ib, face_index, face_corner                     &
                             , nodes_per_face ) result ( dual_vol_center )

    use kinddefs,      only : dp
    use bc_types,      only : bcgrid_type
    use element_types, only : elem_type

    real(dp), dimension(3)                          :: dual_vol_center

    real(dp), dimension(:),             intent(in)  :: x
    real(dp), dimension(:),             intent(in)  :: y
    real(dp), dimension(:),             intent(in)  :: z
    type(bcgrid_type), dimension(:),    intent(in)  :: bc
    integer,                            intent(in)  :: ib
    integer,                            intent(in)  :: nelem
    type(elem_type), dimension(nelem),  intent(in)  :: elem
    integer,                            intent(in)  :: face_index
    integer,                            intent(in)  :: face_corner
    integer,                            intent(in)  :: nodes_per_face

    integer,  dimension(4)                :: cnode
    integer,  dimension(2)                :: enode
    integer,  dimension(4,6)              :: n2fe_tet
    integer,  dimension(8,6)              :: n2fe_hex
    integer,  dimension(6,6)              :: n2fe_prz
    integer,  dimension(5,8)              :: n2fe_pyr

    real(dp)               :: x1, y1, z1
    real(dp)               :: xc, yc, zc
    real(dp)               :: xf1, yf1, zf1
    real(dp)               :: xf2, yf2, zf2
    real(dp)               :: xf3, yf3, zf3
    real(dp)               :: xf4, yf4, zf4
    real(dp)               :: xe1, ye1, ze1
    real(dp)               :: xe2, ye2, ze2
    real(dp)               :: xe3, ye3, ze3
    real(dp)               :: xe4, ye4, ze4

    real(dp), parameter    :: zero  = 0.0_dp

    integer :: fnode
    integer :: node
    integer :: edge
    integer :: face
    integer :: icell
    integer :: ielem
    integer :: number_of_nodes

  continue

    ! list of faces and edges associated with each corner for each element type
    ! n2fe(corner_node,1:8)=(/face1,face2,face3,face4,edge1,edge2,edge3,edge4 /)

    n2fe_tet(1,1:6) = (/ 1,3,4,1,2,3 /)
    n2fe_tet(2,1:6) = (/ 1,2,4,1,4,5 /)
    n2fe_tet(3,1:6) = (/ 1,2,3,2,4,6 /)
    n2fe_tet(4,1:6) = (/ 2,3,4,3,5,6 /)

    n2fe_hex(1,1:6) = (/ 1,3,5,1,2,3   /)
    n2fe_hex(2,1:6) = (/ 1,4,5,1,4,5   /)
    n2fe_hex(3,1:6) = (/ 1,3,6,2,6,7   /)
    n2fe_hex(4,1:6) = (/ 1,4,6,4,6,8   /)
    n2fe_hex(5,1:6) = (/ 2,3,5,3,9,10  /)
    n2fe_hex(6,1:6) = (/ 2,4,5,5,9,11  /)
    n2fe_hex(7,1:6) = (/ 2,3,6,7,10,12 /)
    n2fe_hex(8,1:6) = (/ 2,4,6,8,11,12 /)

    n2fe_prz(1,1:6) = (/ 2,3,4,1,2,3 /)
    n2fe_prz(2,1:6) = (/ 1,3,4,1,4,5 /)
    n2fe_prz(3,1:6) = (/ 1,3,5,4,6,7 /)
    n2fe_prz(4,1:6) = (/ 2,3,5,2,6,8 /)
    n2fe_prz(5,1:6) = (/ 1,4,5,5,7,9 /)
    n2fe_prz(6,1:6) = (/ 2,4,5,3,8,9 /)

    n2fe_pyr(1,1:8) = (/ 1,3,5,0,1,5,8,0 /)
    n2fe_pyr(2,1:8) = (/ 1,4,5,0,2,5,6,0 /)
    n2fe_pyr(3,1:8) = (/ 2,4,5,0,3,6,7,0 /)
    n2fe_pyr(4,1:8) = (/ 2,3,5,0,4,7,8,0 /)
    n2fe_pyr(5,1:8) = (/ 1,2,3,4,1,2,3,4 /)

    icell = 0
    ielem = 0
    fnode = 0
    face  = 0
    edge  = 0

    x1  = zero
    y1  = zero
    z1  = zero
    xf4 = zero
    yf4 = zero
    zf4 = zero

    select case( nodes_per_face )
    case( 3 )
      fnode  = bc(ib)%ibnode(bc(ib)%f2ntb(face_index,mod(face_corner+0-1,3)+1))
      icell  = bc(ib)%f2ntb(face_index,4)
      ielem  = bc(ib)%f2ntb(face_index,5)

    case( 4 )
      fnode  = bc(ib)%ibnode(bc(ib)%f2nqb(face_index,mod(face_corner+0-1,4)+1))
      icell  = bc(ib)%f2nqb(face_index,5)
      ielem  = bc(ib)%f2nqb(face_index,6)

    end select

    ! calculate center of element
    xc    = zero
    yc    = zero
    zc    = zero

    do node = 1, elem(ielem)%node_per_cell

        cnode(1) = elem(ielem)%c2n(node,icell)
        xc = xc + x(cnode(1)) / real((elem(ielem)%node_per_cell),dp)
        yc = yc + y(cnode(1)) / real((elem(ielem)%node_per_cell),dp)
        zc = zc + z(cnode(1)) / real((elem(ielem)%node_per_cell),dp)

    end do

    ! determine which local node of the element is the face corner
    node = 1
    node_loop: do while (  node <=  elem(ielem)%node_per_cell )

        cnode(1) = elem(ielem)%c2n(node,icell)

        if ( (fnode-cnode(1)) == 0 ) then
          ! current point
          x1 = x(cnode(1))
          y1 = y(cnode(1))
          z1 = z(cnode(1))
          exit
        endif

      node = node + 1

    end do node_loop

!----------------------------- center of face -------------------------------80-
    select case ( elem(ielem)%node_per_cell )
    case ( 4 )
      face = n2fe_tet(node,1)
    case ( 5 )
      face = n2fe_pyr(node,1)
    case ( 6 )
      face = n2fe_prz(node,1)
    case ( 8 )
      face = n2fe_hex(node,1)
    end select

    number_of_nodes = 4
    if ( elem(ielem)%local_f2n(face,1) ==                                    &
         elem(ielem)%local_f2n(face,4) ) number_of_nodes = 3

    if ( number_of_nodes == 3 ) then
      cnode(1:3) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:3),icell)
      xf1 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) ) / 3.0_dp
      yf1 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) ) / 3.0_dp
      zf1 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) ) / 3.0_dp

    else
      cnode(1:4) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:4),icell)
      xf1 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) + x(cnode(4)) ) / 4.0_dp
      yf1 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) + y(cnode(4)) ) / 4.0_dp
      zf1 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) + z(cnode(4)) ) / 4.0_dp

    endif

!----------------------------------------------------------------------------80-
    select case ( elem(ielem)%node_per_cell )
    case ( 4 )
      face = n2fe_tet(node,2)
    case ( 5 )
      face = n2fe_pyr(node,2)
    case ( 6 )
      face = n2fe_prz(node,2)
    case ( 8 )
      face = n2fe_hex(node,2)
    end select

    number_of_nodes = 4
    if ( elem(ielem)%local_f2n(face,1) ==                                    &
         elem(ielem)%local_f2n(face,4) ) number_of_nodes = 3

    if ( number_of_nodes == 3 ) then
      cnode(1:3) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:3),icell)
      xf2 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) ) / 3.0_dp
      yf2 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) ) / 3.0_dp
      zf2 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) ) / 3.0_dp

    else
      cnode(1:4) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:4),icell)
      xf2 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) + x(cnode(4)) ) / 4.0_dp
      yf2 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) + y(cnode(4)) ) / 4.0_dp
      zf2 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) + z(cnode(4)) ) / 4.0_dp

    endif

!----------------------------------------------------------------------------80-
    select case ( elem(ielem)%node_per_cell )
    case ( 4 )
      face = n2fe_tet(node,3)
    case ( 5 )
      face = n2fe_pyr(node,3)
    case ( 6 )
      face = n2fe_prz(node,3)
    case ( 8 )
      face = n2fe_hex(node,3)
    end select

    number_of_nodes = 4
    if ( elem(ielem)%local_f2n(face,1) ==                                    &
         elem(ielem)%local_f2n(face,4) ) number_of_nodes = 3

    if ( number_of_nodes == 3 ) then
      cnode(1:3) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:3),icell)
      xf3 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) ) / 3.0_dp
      yf3 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) ) / 3.0_dp
      zf3 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) ) / 3.0_dp

    else
      cnode(1:4) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:4),icell)
      xf3 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) + x(cnode(4)) ) / 4.0_dp
      yf3 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) + y(cnode(4)) ) / 4.0_dp
      zf3 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) + z(cnode(4)) ) / 4.0_dp

    endif

!----------------------------------------------------------------------------80-
!   exception case:  "tip" of pyramid has four faces
    if ( node == 5 .and. elem(ielem)%node_per_cell == 5 ) then

      face            = n2fe_pyr(node,4)
      number_of_nodes = 3

      cnode(1:3) = elem(ielem)%c2n(elem(ielem)%local_f2n(face,1:3),icell)
      xf4 = ( x(cnode(1)) + x(cnode(2)) + x(cnode(3)) ) / 3.0_dp
      yf4 = ( y(cnode(1)) + y(cnode(2)) + y(cnode(3)) ) / 3.0_dp
      zf4 = ( z(cnode(1)) + z(cnode(2)) + z(cnode(3)) ) / 3.0_dp

    endif

!----------------------------------------------------------------------------80-
!----------------------------------------------------------------------------80-
!----------------------------------------------------------------------------80-
!----------------------------------------------------------------------------80-
    select case ( elem(ielem)%node_per_cell )
    case ( 4 )
      edge = n2fe_tet(node,4)
    case ( 5 )
      edge = n2fe_pyr(node,5)
    case ( 6 )
      edge = n2fe_prz(node,4)
    case ( 8 )
      edge = n2fe_hex(node,4)
    end select

    enode(1:2) = elem(ielem)%c2n(elem(ielem)%local_e2n(edge,1:2),icell)
    xe1        = (x(enode(1))+x(enode(2)))/2.0_dp
    ye1        = (y(enode(1))+y(enode(2)))/2.0_dp
    ze1        = (z(enode(1))+z(enode(2)))/2.0_dp
!   write(6,'(a,i3,3x,3(f10.5))') 'edge = ', edge, xe1, ye1, ze1

    select case ( elem(ielem)%node_per_cell )
    case ( 4 )
      edge = n2fe_tet(node,5)
    case ( 5 )
      edge = n2fe_pyr(node,6)
    case ( 6 )
      edge = n2fe_prz(node,5)
    case ( 8 )
      edge = n2fe_hex(node,5)
    end select

    enode(1:2) = elem(ielem)%c2n(elem(ielem)%local_e2n(edge,1:2),icell)
    xe2        = (x(enode(1))+x(enode(2)))/2.0_dp
    ye2        = (y(enode(1))+y(enode(2)))/2.0_dp
    ze2        = (z(enode(1))+z(enode(2)))/2.0_dp
!   write(6,'(a,i3,3x,3(f10.5))') 'edge = ', edge, xe2, ye2, ze2

    select case ( elem(ielem)%node_per_cell )
    case ( 4 )
      edge = n2fe_tet(node,6)
    case ( 5 )
      edge = n2fe_pyr(node,7)
    case ( 6 )
      edge = n2fe_prz(node,6)
    case ( 8 )
      edge = n2fe_hex(node,6)
    end select

    enode(1:2) = elem(ielem)%c2n(elem(ielem)%local_e2n(edge,1:2),icell)
    xe3        = (x(enode(1))+x(enode(2)))/2.0_dp
    ye3        = (y(enode(1))+y(enode(2)))/2.0_dp
    ze3        = (z(enode(1))+z(enode(2)))/2.0_dp
!   write(6,'(a,i3,3x,3(f10.5))') '3: edge = ', edge, xe3, ye3, ze3

    ! add and divide
    dual_vol_center(1) = (x1+xc+xf1+xf2+xf3+xe1+xe2+xe3)/8.0_dp
    dual_vol_center(2) = (y1+yc+yf1+yf2+yf3+ye1+ye2+ye3)/8.0_dp
    dual_vol_center(3) = (z1+zc+zf1+zf2+zf3+ze1+ze2+ze3)/8.0_dp

!   exception case:  "tip" of pyramid has four edges
    if ( node == 5 .and. elem(ielem)%node_per_cell == 5 ) then

      edge = n2fe_pyr(node,8)

      enode(1:2) = elem(ielem)%c2n(elem(ielem)%local_e2n(edge,1:2),icell)
      xe4        = (x(enode(1))+x(enode(2)))/2.0_dp
      ye4        = (y(enode(1))+y(enode(2)))/2.0_dp
      ze4        = (z(enode(1))+z(enode(2)))/2.0_dp
!     write(6,'(a,i3,3x,3(f10.5))') '4: edge = ', edge, xe4, ye4, ze4

      ! add and divide
      dual_vol_center(1) = (x1+xc+xf1+xf2+xf3+xf4+xe1+xe2+xe3+xe4)/10.0_dp
      dual_vol_center(2) = (y1+yc+yf1+yf2+yf3+yf4+ye1+ye2+ye3+ye4)/10.0_dp
      dual_vol_center(3) = (z1+zc+zf1+zf2+zf3+zf4+ze1+ze2+ze3+ze4)/10.0_dp

    endif

  end function return_dual_volume

!========================== UPDATE_WALL_MU_T =================================80
!
!  Loop through all wall boundaries and update the effective turbulent
!  eddy viscosity due to using a wall function as well as the blended
!  omega boundary condition using Eqns. (19) - (22) in AIAA-2004-0581
!  "Wall Function Boundary Conditions Including Heat Transfer and
!  Compressibility for Transport Turbulence Models," R.H.Nichols and
!  C.C. Nelson
!
!=============================================================================80
  subroutine update_wall_mu_t ( ib, bc, bcsoln, amut )

    use kinddefs,           only : dp
    use bc_types,           only : bcgrid_type, bcsoln_type

    integer,                           intent(in)    :: ib
    type(bcgrid_type), dimension(:),   intent(in)    :: bc
    type(bcsoln_type), dimension(:),   intent(in)    :: bcsoln
    real(dp), dimension(:),            intent(inout) :: amut

    integer                :: i, node

  continue

    if ( bc(ib)%nbnode == 0 ) return

    do i = 1, bc(ib)%nbnode

      node       = bc(ib)%ibnode(i)
      amut(node) = bcsoln(ib)%mu_t_wf(i)

    enddo

  end subroutine update_wall_mu_t

!============================ GET_ELEMENT_QP =================================80
!
! Pick out nodal information for the element attached to the boundary face
!
!=============================================================================80

  function get_element_qp_ddt ( q_dof, c2n_cell, node_map, eqn_set             &
                                   , nodes_per_cell, n_var )                   &
                          result ( qp_ddt )

    use kinddefs,                 only : dp
    use generic_gas_map,          only : n_momx, n_momy, n_momz
    use element_defs,             only : max_node_per_cell
    use ddt,                      only : ddt5, assignment(=), ddt5_identity    &
                                       , operator(-), operator(+)
    use thermo,                   only : q_type, primitive_q_type              &
                                       , conserved_q_type
    use solution_types,           only : compressible

    integer,                             intent(in)  :: n_var

    type(ddt5), dimension(n_var,max_node_per_cell)   :: qp_ddt
    real(dp), dimension(n_var)                       :: qnode

    real(dp), dimension(:,:),            intent(in)  :: q_dof
    integer,  dimension(:),              intent(in)  :: c2n_cell
    integer,  dimension(:),              intent(in)  :: node_map
    integer,                             intent(in)  :: eqn_set
    integer,                             intent(in)  :: nodes_per_cell

    integer :: i, i_local, node

    continue

    node_loop : do i_local = 1, nodes_per_cell

! local node number
      i     = node_map(i_local)
      node  = c2n_cell(i)

      qnode(1)   = q_dof(1,node)
      if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
        qnode(n_momx) = q_dof(n_momx,node)
        qnode(n_momy) = q_dof(n_momy,node)
        qnode(n_momz) = q_dof(n_momz,node)
        qnode(5)      = q_dof(5,node)

      elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
        qnode(n_momx) = q_dof(n_momx,node) / qnode(1)
        qnode(n_momy) = q_dof(n_momy,node) / qnode(1)
        qnode(n_momz) = q_dof(n_momz,node) / qnode(1)
        qnode(5)      = get_p(q_dof(1:5,node))

      endif

      qp_ddt(1:5,i) = ddt5_identity(qnode(1:5))

    end do node_loop

  end function get_element_qp_ddt

!============================ GET_ELEMENT_QP =================================80
!
! Pick out nodal information for the element attached to the boundary face
!
!=============================================================================80

  function get_element_qp     ( q_dof, c2n_cell, node_map, eqn_set             &
                                   , nodes_per_cell, n_var )                   &
                          result ( qp )

    use kinddefs,                 only : dp
    use generic_gas_map,          only : n_momx, n_momy, n_momz
    use element_defs,             only : max_node_per_cell
    use thermo,                   only : q_type, primitive_q_type              &
                                       , conserved_q_type
    use solution_types,           only : compressible

    integer,                             intent(in)  :: n_var

    real(dp),   dimension(n_var,max_node_per_cell)   :: qp
    real(dp), dimension(n_var)                       :: qnode

    real(dp), dimension(:,:),            intent(in)  :: q_dof
    integer,  dimension(:),              intent(in)  :: c2n_cell
    integer,  dimension(:),              intent(in)  :: node_map
    integer,                             intent(in)  :: eqn_set
    integer,                             intent(in)  :: nodes_per_cell

    integer :: i, i_local, node

    continue

    node_loop : do i_local = 1, nodes_per_cell

! local node number
      i     = node_map(i_local)
      node  = c2n_cell(i)

      qnode(1)   = q_dof(1,node)
      if ( eqn_set == compressible .and. q_type == primitive_q_type ) then
        qnode(n_momx) = q_dof(n_momx,node)
        qnode(n_momy) = q_dof(n_momy,node)
        qnode(n_momz) = q_dof(n_momz,node)
        qnode(5)      = q_dof(5,node)

      elseif ( eqn_set == compressible .and. q_type == conserved_q_type ) then
        qnode(n_momx) = q_dof(n_momx,node) / qnode(1)
        qnode(n_momy) = q_dof(n_momy,node) / qnode(1)
        qnode(n_momz) = q_dof(n_momz,node) / qnode(1)
        qnode(5)      = get_p(q_dof(1:5,node))

      endif

      qp(1:5,i) = qnode(1:5)

    end do node_loop

  end function get_element_qp

!============================ GET_ELEMENT_SLEN ===============================80
!
!  Estimate of cell "height"
!
!=============================================================================80
  function get_element_slen ( grid, ib, face_index, nodes_per_face, normal )   &
      result ( element_slen )

    use kinddefs,   only : dp
    use grid_types, only : grid_type

    type(grid_type),        intent(in) :: grid
    integer,                intent(in) :: ib
    integer,                intent(in) :: face_index
    integer,                intent(in) :: nodes_per_face
    real(dp), dimension(3), intent(in) :: normal

    real(dp)                          :: element_slen

    real(dp)                          :: total_weight_on
    real(dp)                          :: total_weight_off

    real(dp)                          :: xave_off, yave_off, zave_off
    real(dp)                          :: xave_on,  yave_on,  zave_on
    real(dp)                          :: dx, dy, dz

    integer                           :: cell_node, face_node, node
    integer, dimension(8)             :: node_flag
    integer                           :: ielem, icell
    integer                           :: nodes_per_cell

  continue

    total_weight_on  = 0._dp
    total_weight_off = 0._dp
    xave_off         = 0._dp
    yave_off         = 0._dp
    zave_off         = 0._dp
    xave_on          = 0._dp
    yave_on          = 0._dp
    zave_on          = 0._dp
    icell            = 0
    ielem            = 0
    node             = 0

   select case( nodes_per_face )
      case( 3 )
        icell        = grid%bc(ib)%f2ntb(face_index,4)
        ielem        = grid%bc(ib)%f2ntb(face_index,5)
      case( 4 )
        icell        = grid%bc(ib)%f2nqb(face_index,5)
        ielem        = grid%bc(ib)%f2nqb(face_index,6)
    end select

    nodes_per_cell    = grid%elem(ielem)%node_per_cell

    node_flag        = 1
!---heat transfer
    do cell_node = 1, nodes_per_cell
      do face_node = 1, nodes_per_face

          if      ( nodes_per_face == 3 ) then
            node = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,face_node))
          else if ( nodes_per_face == 4 ) then
            node = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,face_node))
          endif
!           if a cell node lies on the surface, set its flag to zero
        if (grid%elem(ielem)%c2n(cell_node,icell) == node) then
           node_flag(cell_node) = 0
        end if

      end do
    end do

    do cell_node = 1, nodes_per_cell
      node = grid%elem(ielem)%c2n(cell_node,icell)
      if (node_flag(cell_node) == 0) then     ! surface nodes
        xave_on = xave_on + grid%x(node)
        yave_on = yave_on + grid%y(node)
        zave_on = zave_on + grid%z(node)
        total_weight_on = total_weight_on + 1.0_dp
      else                                    ! off-surface nodes
        xave_off = xave_off + grid%x(node)
        yave_off = yave_off + grid%y(node)
        zave_off = zave_off + grid%z(node)
        total_weight_off = total_weight_off + 1.0_dp
      end if
    end do

    xave_on = xave_on/total_weight_on
    yave_on = yave_on/total_weight_on
    zave_on = zave_on/total_weight_on

    xave_off = xave_off/total_weight_off
    yave_off = yave_off/total_weight_off
    zave_off = zave_off/total_weight_off

    dx           = xave_off - xave_on
    dy           = yave_off - yave_on
    dz           = zave_off - zave_on
    element_slen = (dx*normal(1) + dy*normal(2) + dz*normal(3))

  end function get_element_slen

!
!=============================================================================80

!========================== DLR_SA_F =======================================80
!
!  Calculates the friction velocity using the method proposed
!  by Knopp et al.
!
!=============================================================================80

  function dlr_sa_f ( xmr, velocity, distance, rho, mu, u_tau_visc )           &
   result ( u_tau )

    use kinddefs,             only : dp
    use wall_model_functions, only : u_tau_rei_m, spalding_5term_u_tau,        &
                                     dlr_f_log

    implicit none

    real(dp), intent(in) :: xmr
    real(dp), intent(in) :: velocity
    real(dp), intent(in) :: distance
    real(dp), intent(in) :: rho
    real(dp), intent(in) :: mu
    real(dp), intent(in) :: u_tau_visc

    real(dp)             :: u_tau
    real(dp)             :: u_tau_wf
    real(dp)             :: f_rei_m
    real(dp)             :: f_log
    real(dp)             :: f_sp_5
    real(dp)             :: f_sa
    real(dp)             :: arg
    real(dp)             :: phi_sa
    real(dp)             :: y_plus
    real(dp)             :: f3_nonconv_local
    real(dp)             :: f4_nonconv_local

    real(dp), parameter  :: one = 1.0_dp

!   real(dp), parameter :: tanh_coef = 1.0_dp
!   real(dp), parameter :: tanh_coef_log = 27.0_dp
!   real(dp), parameter :: tanh_coef_kw = 50.0_dp
    real(dp), parameter :: tanh_coef_sa = 24.0_dp
!   real(dp), parameter :: tanh_coef_omega = 14.0_dp

  continue

      f3_nonconv_local = 0
      f4_nonconv_local = 0

      ! JCompPhys 220, 19-40, eq. 30
      f_log     = dlr_f_log ( xmr, velocity, distance, rho, mu, u_tau_visc )

      if ( f_log == -one ) then
        f3_nonconv_local = f3_nonconv_local + 1
        f_log            = u_tau_visc
      end if

      f_rei_m   = u_tau_rei_m ( xmr, velocity, distance, rho, mu, u_tau_visc )
      u_tau_wf  = f_rei_m

      y_plus = u_tau_wf * distance * rho / ( mu * xmr )

      if ( y_plus < 250.0_dp ) then
        f_sp_5    = spalding_5term_u_tau ( xmr, velocity, distance     &
                                             , rho, mu, 5, u_tau_wf )

        if ( f_sp_5 == -one ) then
          f4_nonconv_local = f4_nonconv_local + 1
          f_sp_5           = u_tau_visc
        end if

        ! JCompPhys 220, 19-40, eq. 27
        arg       = y_plus / tanh_coef_sa
        phi_sa    = tanh(arg**3)
        f_sa      = ( one - phi_sa ) * f_sp_5 + phi_sa * f_rei_m
        u_tau     = f_sa
      else
        u_tau     = u_tau_wf
      end if

if ( skeleton > 20 ) then
write(*,*)
write(*,'(a,f15.7)') 'u_t_visc', u_tau_visc
write(*,*) '---------------------'
write(*,'(a,f15.7)') 'f_log:..', f_log
write(*,'(a,f15.7)') 'f_rei_m.', f_rei_m
write(*,*)
write(*,'(a,f15.7)') 'f_sp_5:.', f_sp_5
write(*,'(a,es15.6)') 'arg:....', arg
write(*,'(a,f15.7)') 'phi_sa:.', phi_sa
write(*,*)
write(*,'(a,f15.7)') 'f_sa:...', f_sa
write(*,*)
end if

  end function dlr_sa_f

!========================== DLR_KW_F =======================================80
!
!  Calculates the friction velocity using the method proposed
!  by Knopp et al.
!
!=============================================================================80

  function dlr_kw_f ( xmr, velocity, distance, rho, mu, u_tau_visc )           &
    result ( u_tau )

    use kinddefs,             only : dp
    use wall_model_functions, only : u_tau_rei_m, spalding_3term_u_tau,        &
                                     dlr_f_log

    implicit none

    real(dp), intent(in) :: xmr
    real(dp), intent(in) :: velocity
    real(dp), intent(in) :: distance
    real(dp), intent(in) :: rho
    real(dp), intent(in) :: mu
    real(dp), intent(in) :: u_tau_visc

    real(dp)             :: u_tau
    real(dp)             :: u_tau_wf
    real(dp)             :: f_rei_m
    real(dp)             :: f_log
    real(dp)             :: f_sp_3
    real(dp)             :: f_kw
    real(dp)             :: arg
    real(dp)             :: phi_kw
    real(dp)             :: y_plus
    real(dp)             :: f3_nonconv_local
    real(dp)             :: f4_nonconv_local

    real(dp), parameter  :: one = 1.0_dp

!   real(dp), parameter :: tanh_coef = 1.0_dp
!   real(dp), parameter :: tanh_coef_log = 27.0_dp
    real(dp), parameter :: tanh_coef_kw = 50.0_dp
!   real(dp), parameter :: tanh_coef_omega = 14.0_dp

  continue

      f3_nonconv_local = 0
      f4_nonconv_local = 0

      ! JCompPhys 220, 19-40, eq. 30
      f_log     = dlr_f_log ( xmr, velocity, distance, rho, mu, u_tau_visc )

      if ( f_log == -one ) then
        f3_nonconv_local = f3_nonconv_local + 1
        f_log            = u_tau_visc
      end if

      f_rei_m   = u_tau_rei_m ( xmr, velocity, distance, rho, mu, u_tau_visc )
      u_tau_wf  = f_rei_m

      y_plus = u_tau_wf * distance * rho / ( mu * xmr )

      if ( y_plus < 250.0_dp ) then
        f_sp_3    = spalding_3term_u_tau ( xmr, velocity, distance     &
                                             , rho, mu, 3, u_tau_wf )

        if ( f_sp_3 == -one ) then
          f4_nonconv_local = f4_nonconv_local + 1
          f_sp_3           = u_tau_visc
        end if

        ! JCompPhys 220, 19-40, eq. 27
        arg       = y_plus / tanh_coef_kw
        phi_kw    = tanh(arg**2)
        f_kw      = ( one - phi_kw ) * f_sp_3 + phi_kw * f_rei_m
        u_tau     = f_kw
      else
        u_tau     = u_tau_wf
      end if

if ( skeleton > 20 ) then
write(*,*)
write(*,'(a,f15.7)') 'u_t_visc', u_tau_visc
write(*,*) '---------------------'
write(*,'(a,f15.7)') 'f_log:..', f_log
write(*,'(a,es15.6)') 'arg:....', arg
write(*,'(a,f15.7)') 'f_rei_m.', f_rei_m
write(*,*)
write(*,'(a,f15.7)') 'f_sp_3:.', f_sp_3
write(*,'(a,es15.6)') 'arg:....', arg
write(*,'(a,f15.7)') 'phi_kw:.', phi_kw
write(*,*)
write(*,'(a,f15.7)') 'f_kw:...', f_kw
write(*,*)
end if

  end function dlr_kw_f

!========================== DLR_KW_BC ======================================80
!
!  Calculates the boundary conditions for the 2-equation kw model
!  for the wall function model by Knopp et al.
!
!=============================================================================80
  subroutine dlr_kw_bc( omega_bc_inner, omega_bc_outer,                        &
                        rho, y_plus_visc, k_bc, omega_bc , mu_t_bc )

    use kinddefs,             only : dp

    implicit none

    real(dp), intent(in)  :: omega_bc_inner
    real(dp), intent(in)  :: omega_bc_outer
    real(dp), intent(in)  :: rho
    real(dp), intent(in)  :: y_plus_visc

    real(dp), intent(out) :: k_bc
    real(dp), intent(out) :: omega_bc
    real(dp), intent(out) :: mu_t_bc

    real(dp) :: omega_b1
    real(dp) :: omega_b2
    real(dp) :: arg
    real(dp) :: phi


    real(dp), parameter   :: zero            = 0.0_dp
    real(dp), parameter   :: one             = 1.0_dp
    real(dp), parameter   :: onept2          = 1.2_dp
    real(dp), parameter   :: onept2i         = 1.0_dp/1.2_dp
    real(dp), parameter   :: tanh_coef_omega = 10.0_dp

  continue

    omega_b1   = omega_bc_inner + omega_bc_outer
    omega_b2   = ( omega_bc_inner**onept2 + omega_bc_outer**onept2)**onept2i

    arg        = y_plus_visc / tanh_coef_omega
    phi        = tanh(arg**4)
    omega_bc   = phi * omega_b1 + ( one - phi ) * omega_b2

    k_bc       = zero
    mu_t_bc    = rho * k_bc / omega_bc

  end subroutine dlr_kw_bc

!=================== WILCOX_HOMOGENEOUS_BC ===================================80
!
!  Calculates the boundary conditions for the 2-equation kw model
!  for the wall function model by Wilcox with pressure gradients,
!  but with a homogeneous boundary condition for the turbulent kinetic
!  engergy
!
!=============================================================================80
  subroutine wilcox_homogeneous_bc( omega_bc_log, xmr, u_tau, phi,            &
             distance, rho, rho_avg, nu, k_bc, omega_bc , mu_t_bc )

    use kinddefs,             only : dp

    implicit none

    real(dp), intent(in)  :: omega_bc_log
    real(dp), intent(in)  :: xmr
    real(dp), intent(in)  :: u_tau
    real(dp), intent(in)  :: phi
    real(dp), intent(in)  :: distance
    real(dp), intent(in)  :: rho
    real(dp), intent(in)  :: rho_avg
    real(dp), intent(in)  :: nu


    real(dp), intent(out) :: k_bc
    real(dp), intent(out) :: omega_bc
    real(dp), intent(out) :: mu_t_bc

    real(dp) :: factor
    real(dp) :: y_plus

    real(dp), parameter   :: zero            = 0.0_dp
    real(dp), parameter   :: one             = 1.0_dp

  continue

    y_plus     = u_tau * distance / ( nu * xmr )

    factor     = omega_1 * y_plus * phi
    omega_bc   = omega_bc_log * sqrt(rho_avg/rho) * ( one + factor )

    k_bc       = zero
    mu_t_bc    = rho * k_bc / omega_bc

  end subroutine wilcox_homogeneous_bc

!=================== WILCOX_BC ===============================================80
!
!  Calculates the boundary conditions for the 2-equation kw model
!  for the wall function model by Wilcox with pressure gradients
!
!=============================================================================80
  subroutine hybrid_wilcox_bc( omega_bc_ittw, omega_bc_log, xmr, u_tau, phi,   &
                        distance, rho, rho_avg, nu, remove_phi,                &
                        k_bc, omega_bc , mu_t_bc )

    use kinddefs,             only : dp
    use turb_kw_const,        only : cmu_0

    implicit none

    real(dp), intent(in)  :: omega_bc_ittw
    real(dp), intent(in)  :: omega_bc_log
    real(dp), intent(in)  :: xmr
    real(dp), intent(in)  :: u_tau
    real(dp), intent(in)  :: phi
    real(dp), intent(in)  :: distance
    real(dp), intent(in)  :: rho
    real(dp), intent(in)  :: rho_avg
    real(dp), intent(in)  :: nu
    logical,  intent(in)  :: remove_phi


    real(dp), intent(out) :: k_bc
    real(dp), intent(out) :: omega_bc
    real(dp), intent(out) :: mu_t_bc

    real(dp) :: w_factor
    real(dp) :: k_factor
!   real(dp) :: phi_yplus
    real(dp) :: omega_bc_wf
    real(dp) :: k_bc_wf
!   real(dp) :: arg
    real(dp) :: y_plus

    real(dp), parameter   :: zero            = 0.0_dp
    real(dp), parameter   :: one             = 1.0_dp
!   real(dp), parameter   :: tanh_coef_omega = 10.0_dp

  continue

    y_plus   = u_tau * distance / ( nu * xmr )

    w_factor     = omega_1 * y_plus * phi
    k_factor     =     k_1 * y_plus * phi

    if ( remove_phi ) then
      w_factor     = zero
      k_factor     = zero
    end if

    if ( abs(w_factor) > 0.01_dp ) then
      if ( w_factor > zero ) then
        w_factor   = 0.01_dp
        k_factor   = 0.01_dp
      else
        w_factor   = -0.01_dp
        k_factor   = -0.01_dp
      endif
    endif

    omega_bc_wf  = omega_bc_log * sqrt(rho_avg/rho) * ( one + w_factor )
    k_bc_wf      = (rho_avg/rho) * ( u_tau * u_tau / ( sqrt( cmu_0 ) ) )       &
                                                    * ( one + k_factor )

    omega_bc   = sqrt(omega_bc_wf**2+omega_bc_ittw**2)
    k_bc       = k_bc_wf
    mu_t_bc    = rho * k_bc / omega_bc
!write(6,'(a,15(1x,f20.10))') 'hybrid:....   ', omega_bc_ittw, omega_bc_log, &
!omega_bc, k_bc_wf, k_bc, phi, w_factor, k_factor

  end subroutine hybrid_wilcox_bc

!===================================== SPALART_BC ============================80
!
!  Calculates the boundary conditions for the 1-equation S-A model.
!  AIAA-1996-0292, Eq. (6), Frink
!
!=============================================================================80
  subroutine spalart_bc( xmr, u_tau, distance, velocity, rho_avg, nu_avg,      &
                         k_bc, mu_t_bc )

    use kinddefs,             only : dp
    use wall_model_functions, only : spalart_nu_tilde
    use turbulence_info,      only : tanh_coef

    implicit none

    real(dp), intent(in)  :: xmr
    real(dp), intent(in)  :: u_tau
    real(dp), intent(in)  :: distance
    real(dp), intent(in)  :: velocity
    real(dp), intent(in)  :: rho_avg
    real(dp), intent(in)  :: nu_avg


    real(dp), intent(out) :: k_bc
    real(dp), intent(out) :: mu_t_bc

    real(dp) :: y_plus
    real(dp) :: u_plus
    real(dp) :: mu_avg
    real(dp) :: mu_t_sp3
    real(dp) :: arg
    real(dp) :: phi_b1
    real(dp) :: nu_hat

    real(dp), parameter   :: one     = 1.0_dp
    real(dp), parameter   :: two     = 2.0_dp
    real(dp), parameter   :: fivept2 = 5.2_dp
    real(dp), parameter   :: six     = 6.0_dp

  continue

    mu_avg   = nu_avg * rho_avg
    y_plus   = u_tau * distance / ( nu_avg * xmr )
    u_plus   = velocity / u_tau

    mu_t_sp3 = mu_avg * kappa * exp(-kappa*fivept2) * (                        &
               exp(kappa*u_plus)                                               &
              - one                                                            &
              - kappa*u_plus                                                   &
              - ((kappa*u_plus)**2)/two                                        &
              - ((kappa*u_plus)**3)/six )

     arg     = y_plus / tanh_coef
     phi_b1  = tanh(arg**4)
     mu_t_bc = phi_b1 * mu_t_sp3
!     mu_t_wf = max ( muavg, mu_t_wf )
     nu_hat  = mu_t_bc / mu_avg
     k_bc    = spalart_nu_tilde( mu_avg, nu_hat ) ! nu_tilde

  end subroutine spalart_bc

  include 'get_sij_ddt5.f90'
  include 'viscosity_law_ddt5.f90'
  include 'get_p.f90'

end module wall_model

################################################################################
########################### Design Variable Information ########################
################################################################################
Global design variables (Mach number, AOA, Yaw, Noninertial rates)
  Var Active         Value               Lower Bound            Upper Bound
 Mach    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
  AOA    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
  Yaw    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
xrate    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
yrate    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
zrate    0   0.000000000000000E+00  0.000000000000000E+00  0.000000000000000E+00
Number of bodies
    2
Rigid motion design variables for body 1 (name of body 1, less than 80 cols)
  Var Active         Value               Lower Bound            Upper Bound
RotRate  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotFreq  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotAmpl  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotOrgx  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotOrgy  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotOrgz  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotVecx  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotVecy  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotVecz  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnRate  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnFreq  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnAmpl  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnVecx  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnVecy  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnVecz  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
Parameterization Scheme (Massoud=1 Bandaids=2 Sculptor=4 User-Defined=5)
    1
Number of shape variables for body 1
    3
Index Active         Value               Lower Bound            Upper Bound
    1    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
    2    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
    3    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
Rigid motion design variables for body 2 (name of body 2, less than 80 cols)
  Var Active         Value               Lower Bound            Upper Bound
RotRate  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotFreq  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotAmpl  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotOrgx  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotOrgy  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotOrgz  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotVecx  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotVecy  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
RotVecz  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnRate  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnFreq  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnAmpl  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnVecx  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnVecy  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
TrnVecz  1   0.000000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
Parameterization Scheme (Massoud=1 Bandaids=2 Sculptor=4)
    1
Number of shape variables for body 2
    2
Index Active         Value               Lower Bound            Upper Bound
    1    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
    2    1   0.100000000000000E+01  0.000000000000000E+00  0.500000000000000E+01
################################################################################
############################### Function Information ###########################
################################################################################
Number of composite functions for design problem statement
    2
################################################################################
Cost function (1) or constraint (2)
    1
If constraint, lower and upper bounds
    0.1 0.5
Number of components for function   1
    4
Physical timestep interval where function is defined
    1 1
Composite function weight, target, and power
1.0 0.0 1.0
Components of function   1: boundary id (0=all)/name/value/weight/target/power
    2 clp           0.873650000000000    1.000    0.00000 1.000
    3 cdv           0.887650000000000    1.000    0.00000 1.000
    0 cx            0.887650000000000    1.000    0.00000 1.000
    5 cmxv          0.887650000000000    1.000    0.00000 1.000
Current value of function   1
   0.000000000000000
Current derivatives of function wrt global design variables
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   2
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   2
   0.000000000000000
   0.000000000000000
################################################################################
Cost function (1) or constraint (2)
    2
If constraint, lower and upper bounds
    -0.3 100.0
Number of components for function   2
    2
Physical timestep interval where function is defined
    1 1
Composite function weight, target, and power
1.0 0.0 1.0
Components of function   2: boundary id (0=all)/name/value/weight/target/power
    2 clp           0.873650000000000    1.000    0.00000 1.000
    3 cdv           0.887650000000000    1.000    0.00000 1.000
Current value of function   2
   0.000000000000000
Current derivatives of function wrt global design variables
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   1
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt rigid motion design variables of body   2
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
   0.000000000000000
Current derivatives of function wrt shape design variables of body   2
   0.000000000000000
   0.000000000000000

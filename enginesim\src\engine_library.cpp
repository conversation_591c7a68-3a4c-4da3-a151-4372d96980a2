#include "engine_library.h"

// Required for dynamic libraries on unix.
//#include <stddef.h>
#include <stdio.h>

#include <stdexcept>


EngineLibrary::EngineLibrary(std::string filename)
	:handleLib(NULL)
{
    openLibrary(filename);
    setFunctionPointers();
}

EngineLibrary::~EngineLibrary(){
    dlclose(handleLib);
}

void EngineLibrary::openLibrary(std::string filename){
    handleLib = dlopen(filename.c_str(), RTLD_NOW);
	if(handleLib == NULL){
        throw std::logic_error("Cannot open the specified library");
	}
}

void EngineLibrary::setFunctionPointers(){
    //--- Function pointers to functions in the engine library.
    model_initialize =(void(*)(boolean_T))dlsym(handleLib , "lib_engine_initialize");
    model_step       =(void(*)(void))dlsym(handleLib ,      "lib_engine_step");
    model_terminate  =(void(*)(void))dlsym(handleLib ,      "lib_engine_terminate");

    //--- Pointers to internal structs of the engine library.
    // Used to set input data.
    model_in         =(ExtU_lib_engine_T*)dlsym(handleLib , "lib_engine_U");
    // Used to retrieve output data.
    model_out        =(ExtY_lib_engine_T*)dlsym(handleLib , "lib_engine_Y");

    if( 
            model_initialize == NULL or
            model_step       == NULL or
            model_terminate  == NULL or
            model_in         == NULL or
            model_out        == NULL
      ){
        throw std::logic_error("Could not initialize functions and data from the engine library.");
    }
}

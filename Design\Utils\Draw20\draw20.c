/* This code reads in the coordinates of and airfoil and draws them */

#include <stdio.h>
#include <stdlib.h>
#include <math.h>

#include <Xm/Xm.h>
#include <Xm/DrawingA.h>
#include <Xm/Label.h>
#include <Xm/Form.h>
#include <Xm/PushB.h>
#include <Xm/ToggleB.h>
#include <Xm/RowColumn.h>
#include <Xm/CascadeB.h>
#include <Xm/FileSB.h>
#include <Xm/SelectioB.h>
#include <Xm/Frame.h>
#include <Xm/Scale.h>
#include <Xm/Separator.h>
#include <X11/Shell.h>

#define OK   1 
#define CANCEL 2
#define maxPoints 5000
#define maxSegments 200
#define maxElements 50
#define margin 40
#define toolWidth 50
#define toolHeight 300
#define MAX(x,y) (((x) > (y))?(x):(y))
#define MIN(x,y) (((x) < (y))?(x):(y))

#define RESET 0
#define SHOWREALSIZE 1
#define ZOOMBUTTON 2
#define TRANSLATE 3
#define PICKPOINT 4
#define SYMBOLBUTTON 99

#define PICKMOVEX  5
#define PICKMOVEY  6
#define REVERT     7
#define MOVEPOINT  8
#define MOVEPOINTX 9
#define MOVEPOINTY 10
#define RESTOREPOINT 11

int buttonNumbers[]={0,1,2,3,4,5,6,7,8,9,10,11,12,13,14};
int numElements; /* How many elements does this geometry have */
/*int element2segment[]={0,3,7,9}; */
int element2segment[maxElements];
int segment2point[][1] = {{0},{0},{0},{0},{0},{0},{0},{0},{0}};
int symbolSize = 8;
int start_x, start_y;
int old_x, old_y;
long start_x_long, start_y_long;
int filetype; /* 0=Geometry, 1=design.data 2=pressure distribution */

int zoomOn = False;
int pickOn = False;
int translateOn   = False;
int pickMoveX    = False;
int pickMoveY    = False;
int revertLimits = False;
int movePoint    = False;
int movePointX   = False;
int movePointY   = False;
int restorePoint = False;

int showSymbols = False;
int showLines = True;
int showFitSymbols = False;
int showFitLines = True;
int showBsymbols = True;
int showLimits = True;

int fileRead = False; /* Change this to true when a valid file is read in */
int activePoint = -1; /* When picking points, this is the active one */
int activeSegment = -1; /* Which segment is active */
int activeCP =-1; /* Which control point is active */
int activeElement=-1;      /* Which element is the active control point on */
int numKids;
int whichelement=0;
int baselineOrder=3;
int baselineNoControl=20;
int baselineNoEval=129;
int maxPointsOnElement = 500;
int somethingFit = 0;

/* Variables read in from design.data */
int ialpha = 0;
int imach  = 0;
int ntran  = 0;
double domega1 = 0.;
double domega2 = 0.;
double domega3 = 0.;
double domega4 = 0.;
double xmach = 0.;
double xmachmin  = 990.;
double xmachmax  = -990.;
double alpha = 0.;
double alphamin = 990.;
double alphamax = -990.;
double dtran[maxElements], mintran[maxElements], maxtran[maxElements];

const int AOK = 1; 
const int ACANCEL = 2;
 
XtAppContext context;
XmStringCharSet char_set=XmSTRING_DEFAULT_CHARSET;
 
GC gc, gc_red, gc_green, gc_green_filled, gc_xor, gc_filled, gc_xor_filled;
Widget toplevel;
Widget form;
Widget toolshell, toolform, toolbar, toolbuttons[10], toolbuttons2[20];
Widget toolFrame, toolform2;
Widget controlFrame, toolbar2;
Widget splineFrame;
Widget drawing_area;
Widget menuBar;
Widget fileMenu;
Widget openItem;
Widget quitItem;
Widget filedialog;
Widget fileTypeOption=0;

Widget symbolMenu;
Widget inputcurveline;
Widget inputcurvesymbol;
Widget splinecurveline;
Widget splinecurvesymbol;
Widget Bsplinesymbol;
Widget Bsplinelimits;

Widget elementPicker; /* Option menu to choose the element of interest */
Widget elementOption; /* Use to activate the option pulldown */
Widget whichElement[100]; /* Menu items inside option menu */
Widget degreeScale;
Widget pointScale;
Widget separator;
Widget fitpushbutton;
Widget reevaluatepushbutton;
Widget numControlPoints[maxSegments];

/* Parameters for parameterization type options */
#define Uniform 0
#define Chord   1
#define Centrip 2
Widget paramPulldown; /* Pull down menu pane   */
Widget paramOption;   /* Pull down option menu */
Widget paramPush[3];  /* Pushbuttons for pane  */
void createParamOption(Widget w);
void paramCB(Widget w, XtPointer client_data, XmPushButtonCallbackStruct *call_data);
void forces(int ielem);
const int UNIFORM = 0;
const int CHORD = 1;
const int CENTRIP = 2;


struct{
   double x, y;      /* Coordinates of points */
   double t;         /* Parameter at each point */
}point[maxPoints];

typedef struct seg{
   int numberOfPoints;    /* Number of points on each segment          */
   int noControl;         /* Number of control points for this segment */
   int maxcontrol;        /* Max number of control points for segment  */
   int type;              /* 0=inviscid 1=viscous 2=far-field          */
   double *x;             /* x-coordinate for each point               */
   double *y;             /* y-coordinate for each point               */
   double *tval;          /* t-parameter (local for each segment)      */
}SEGMENT;
SEGMENT segment[maxSegments];

typedef struct elems{
   int noKnots;           /* Number of knots in the fitted knot sequence          */
   int noControl;         /* Number of control points for the fitted geometry     */
   int noSegments;        /* Number of segments for each element                  */
   int firstSegment;      /* The number of the first segment defining the element */
   int lastSegment;       /* The number of the last segment defining the element  */
   int order;             /* Order of the B-spline                                */
   int numEval;           /* Number of points on B-spline surface                 */
   int fitted;            /* If zero this has not been previously fit             */
   int maxdegree;         /* Maximum degree of polynomial for element min(npts-1) */
   int itranx, itrany;    /* If we are translating the element                    */
   int irotate;           /* If we are rotating the element                       */
   int parameterization;  /* Type 1=uniform 2=chord 3=centripital                 */
   int initialNum;        /* If reading design.data we need to know number of pts */
   double xrot, yrot;     /* Point of rotation for each element                   */
   double *knots;         /* knots for the element                                */
   double *xcontrol;      /* Position of x-control points                         */
   double *ycontrol;      /* Position of y-control points                         */
   double *xupper;        /* Maximum x-position for control points                */
   double *xlower;        /* Minimum x-position for control points                */
   double *yupper;        /* Maximum y-position for control points                */
   double *ylower;        /* Minimum y-position for control points                */
   double *xfitted;       /* x-position of points on fitted surface               */
   double *yfitted;       /* y-position of points on fitted surface               */
   double *xcontrolO;     /* Saved position of x-control points                   */
   double *ycontrolO;     /* Saved Position of y-control points                   */
   double *uvalue;        /* parameter for evaluating spline                      */
   double *uRead;         /* Parameter read in from existing design.data file     */
}ELEMENT;
ELEMENT element[maxElements];

int npoints; /* Number of points */
double minX,maxX; /* min and max X in the data set */
double minY,maxY; /* min and max Y in the data set */
double cminX=0, cmaxX=1; /* min and max X currently active */
double cminY=0, cmaxY=1; /* min and max Y currently active */
double sminX,smaxX; /* min and max X for the axis when manipulating Cp*/
double sminY,smaxY; /* min and max Y for the axis when manipulating Cp*/
int currentHeight;
int currentWidth;
int fileread = 0;

int exposeCount=0;

/* Declare functions */
int main(int argc, char *argv[]);
void setup_gc(void);
int exposureCB(Widget w,XtPointer client_data, XmDrawingAreaCallbackStruct *call_data);
void resizeCB(Widget w,XtPointer client_data, XmDrawingAreaCallbackStruct call_data);
void getCoords(char *datafile);
void getCps(char *datafile);
void readCps(char *datafile);
void promptCB(Widget w, XtPointer client_data, XmSelectionBoxCallbackStruct *call_data);
void maxXY(void);
void setXYScale(void);
void icalloc(int size,int **p);
void fcalloc(int size,double **p);
void drawPoints(void);
void fileDialogCB(Widget w, XtPointer client_data, XmSelectionBoxCallbackStruct *call_data);
void toolButtonCB(Widget w, XtPointer client_data, XmToggleButtonCallbackStruct *call_data);
void pickPoint(int xpoint, int ypoint);
void pickControlPoint(int xpoint, int ypoint);
void createOptionMenu(Widget w);
Widget makeOptionMenu(char *menu_name, Widget parent);
Widget makeOptionItem(char *item_name, Widget menu);
Widget makeFileOptionMenu(char *menu_name, Widget parent);
Widget makeFileOptionItem(char *item_name, Widget menu);
void elementOptionCB(Widget w, XtPointer client_data, XmAnyCallbackStruct *call_data);
void fileOptionCB(Widget w, XtPointer client_data, XmAnyCallbackStruct *call_data);
void degreeScaleCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data);
void pointScaleCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data);
void fitPushButtonCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data);
void controlPointsCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data);
void deleteSegScale(void);
void createSegScale(int create, int element);
void makeToolShell(void);
void makeDesignToolShell(void);
void makePressureToolShell(void);
extern void segB(void);
extern void evalBSpline(void);
void writeFile(void);
void writePressureFile(void);
void getDesignData(char *datafile);
void getNtran(char *datafile);

/* Functions for the menu bar */
void fileCB(Widget w, char *client_data, XmAnyCallbackStruct *call_data);
Widget makeMenuItem(char *item_name,caddr_t client_data, Widget menu, int type, int onOff);
Widget makeMenu(char *menu_name, Widget menu_bar, int tearOff);
void createMenus(Widget menu_bar);


/* Functions to handle the mouse clicks when using the zoom box */

void buttonStart(Widget w, XtPointer client_data, XEvent *event);
void buttonDrag(Widget w, XtPointer client_data, XEvent *event);
void buttonDone(Widget w, XtPointer client_data, XEvent *event);

int main(int argc, char *argv[])
{
    Arg al[20];
    int ac;
    int i;

    /* create the toplevel shell */
    toplevel = XtAppInitialize(&context,"MyDraw",NULL,0,&argc,argv,NULL,NULL,0);
 
    /* Create the form widget */
    ac = 0;
    XtSetArg(al[ac], XmNtitle, "NASA LANGLEY - Draw20"); ac++;
    XtSetArg(al[ac], XmNiconName, "draw20"); ac++;
    XtSetArg(al[ac], XmNx, 0); ac++;
    XtSetArg(al[ac], XmNy, 0); ac++;
    XtSetArg(al[ac], XmNwidth, 500); ac++;
    XtSetArg(al[ac], XmNheight, 600); ac++;
    form = XmCreateForm(toplevel,"form",al,ac);
    XtManageChild(form);

/* set the currentHeight and currentWidth to the height and width of the window */
    ac=0;
    XtSetArg(al[ac],XmNheight,&currentHeight); ac++;
    XtSetArg(al[ac],XmNwidth,&currentWidth); ac++;
    XtGetValues(form,al,ac);

    /* Create the menus and attach to the form widget */
    ac = 0;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++;
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++;
    menuBar = XmCreateMenuBar(form,"menuBar",al,ac);
    XtManageChild(menuBar);

    /* Create a drawing area widget and */
    /* attach the drawing area to the form */
    ac = 0;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++;
    XtSetArg(al[ac],XmNtopWidget,menuBar); ac++;
    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++;
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;
/*    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; */
/*    XtSetArg(al[ac],XmNleftWidget,toolbar); ac++; */
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++;
    drawing_area=XmCreateDrawingArea(form,"drawing_area",al,ac);
    XtManageChild(drawing_area);
    XtAddCallback(drawing_area,XmNexposeCallback,(XtCallbackProc)exposureCB,NULL);
    XtAddCallback(drawing_area,XmNresizeCallback,(XtCallbackProc)resizeCB,NULL);

    setup_gc();
    createMenus(menuBar);

/* Create the file dialog */
   ac = 0;
   filedialog = XmCreateFileSelectionDialog(toplevel,"filedialog",al,ac);
   XtAddCallback(filedialog, XmNokCallback, (XtCallbackProc)fileDialogCB, (XtPointer)&AOK);
   XtAddCallback(filedialog, XmNcancelCallback, (XtCallbackProc)fileDialogCB, (XtPointer)&ACANCEL); 
   XtUnmanageChild(XmSelectionBoxGetChild(filedialog,XmDIALOG_HELP_BUTTON));

   fileTypeOption = makeFileOptionMenu("File Type",filedialog);
   makeFileOptionItem("Geometry",fileTypeOption);
   makeFileOptionItem("Design Data",fileTypeOption);
   makeFileOptionItem("Pressure Data",fileTypeOption);

/* Add the event handlers for the drawing area */
   XtAddEventHandler(drawing_area, ButtonPressMask,   FALSE, (XtEventHandler)buttonStart, NULL);
   XtAddEventHandler(drawing_area, ButtonReleaseMask, FALSE, (XtEventHandler)buttonDone, NULL);
   XtAddEventHandler(drawing_area, ButtonMotionMask,  FALSE, (XtEventHandler)buttonDrag, NULL);

    XtRealizeWidget(toplevel);
/*    XtRealizeWidget(toolshell); */
    XtAppMainLoop(context);
}

/*========================== TOOLBUTTONCB ==================================*/
/*                                                                          */
/* Call back for the buttons on the tool bar                                */
/*                                                                          */
/*==========================================================================*/
void toolButtonCB(Widget w, XtPointer client_data, XmToggleButtonCallbackStruct *call_data)
{
   Arg al[10];
   int ac;
   int whichtool;
   int button;
   double dx,dy,avex,avey;
   whichtool = *((int *)client_data);
   switch(whichtool)
   {
      case ZOOMBUTTON:
         if(call_data->set==True)
         {
           zoomOn = True;
           translateOn = False;
           pickOn = False; 
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
           zoomOn = False;
         }
         break;
      case TRANSLATE:
         if(call_data->set==True)
         {
           translateOn = True;
           zoomOn = False;
           pickOn = False; 
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
           translateOn = False;
         }
         break;
      case PICKPOINT:
         if(call_data->set==True)
         {
           pickOn = True;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          pickOn = False;
         }
         break;
      case PICKMOVEX:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = True;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          pickMoveX = False;
         }
         break;
      case PICKMOVEY:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = False;
           pickMoveY  = True;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          pickMoveY = False;
         }
         break;

      case REVERT:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn  = False;
           pickMoveX    = False;
           pickMoveY    = False;
           revertLimits = True;
           movePoint    = False;
           movePointX   = False;
           movePointY   = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          revertLimits = False;
         }
         break;
      case MOVEPOINT:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = True;
           movePointX = False;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          movePoint = False;
         }
         break;
      case MOVEPOINTX:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = True;
           movePointY = False;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          movePointX = False;
         }
         break;
      case MOVEPOINTY:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = True;
           restorePoint = False;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[RESTOREPOINT],False,True);
         }
         else
         {
          movePointY = False;
         }
         break;
      case RESTOREPOINT:
         if(call_data->set==True)
         {
           pickOn = False;
           zoomOn = False;
           translateOn = False;
           pickMoveX  = False;
           pickMoveY  = False;
           revertLimits     = False;
           movePoint  = False;
           movePointX = False;
           movePointY = False;
           restorePoint = True;
           XmToggleButtonSetState(toolbuttons[ZOOMBUTTON],False,True);
           XmToggleButtonSetState(toolbuttons[PICKPOINT],False,True);
           XmToggleButtonSetState(toolbuttons[TRANSLATE],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEX],False,True);
           XmToggleButtonSetState(toolbuttons2[PICKMOVEY],False,True);
           XmToggleButtonSetState(toolbuttons2[REVERT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINT],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTX],False,True);
           XmToggleButtonSetState(toolbuttons2[MOVEPOINTY],False,True);
         }
         else
         {
          restorePoint = False;
         }
         break;
      case SHOWREALSIZE:
         if(call_data->set==True)
         {
           XmToggleButtonSetState(toolbuttons[SHOWREALSIZE],False,True);
/* Find the min,max, and average coordinates */
           dx = maxX - minX;
           dy = maxY - minY;
           avex = .5*(maxX + minX);
           avey = .5*(maxY + minY);
/* If dx is bigger, scale y */
           if(dx >= dy)
           {
             cminX = minX;
             cmaxX = maxX;
             cminY = avey - .5*dx;
             cmaxY = avey + .5*dx;
           }
           else if(dy >= dx)
           {
             cminY = minY;
             cmaxY = maxY;
             cminX = avex - .5*dy;
             cmaxX = avex + .5*dy;
           }
         cminX = minX;
         cmaxX = maxX;
         cminY = minY;
         cmaxY = maxY;
           if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
           drawPoints();
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
         if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
         drawPoints();
         }
         break;
      case RESET:
         if(call_data->set==True)
         {
           XmToggleButtonSetState(toolbuttons[RESET],False,True);
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
         cminX = minX;
         cmaxX = maxX;
         cminY = minY;
         cmaxY = maxY;
         setXYScale();
         if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
         drawPoints();
         }
         break;
   }
}
/*========================== PICKPOINT =====================================*/
/*                                                                          */
/* Pick a point and highlight it                                            */
/*                                                                          */
/*==========================================================================*/
void pickPoint(int xpoint, int ypoint)
{
    Arg al[10];
    int ac;
    int i,j,k, iloc;
    int w0,w1,h0,h1;
    double x1,y1,x0,y0;
    double dist, temp;
    double slopeX, interceptX;
    double slopeY, interceptY;
    XExposeEvent *event;

    h0 = margin;
    w0 = margin;
    h1 = currentHeight - margin;
    w1 = currentWidth - margin;

/* We have the coordinate of the point in screen coordinates */
/* Lets convert it to (x,y) so we can find the closest point */

    slopeX = (cmaxX - cminX)/(double)(w1 - w0);
    interceptX = .5*(cmaxX + cminX - slopeX*(w0 + w1));
    x0 = (double)(slopeX*xpoint + interceptX);
    slopeY = (cmaxY - cminY)/(double)(h0 - h1);
    interceptY = .5*(cmaxY + cminY - slopeY*(h0 + h1));
    y0 = (double)(slopeY*ypoint + interceptY);

/* Now find the closest point */
    dist = 10000.0;

    if(filetype !=2)
    {
      for(i=0; i<numElements; i++)
      {
        for(j=element[i].firstSegment; j<=element[i].lastSegment; j++)
        {
          for(k=0; k<segment[j].numberOfPoints; k++)
          {
            temp = (segment[j].x[k] - x0)*(segment[j].x[k] - x0) + (segment[j].y[k] - y0)*(segment[j].y[k] - y0);
            if(temp <= dist)
            {
              activeSegment = j;
              dist = temp;
              iloc = k;
            }
          }
        }
      }
    }
    if(filetype ==2)
    {
      for(i=0; i<numElements; i++)
      {
        for(j=0; j<element[i].noControl; j++)
        {
          temp = (element[i].xfitted[j] - x0)*(element[i].xfitted[j] - x0)
               + (element[i].ycontrol[j] - y0)*(element[i].ycontrol[j] - y0);
          if(temp <= dist)
          {
            k = i;
            dist = temp;
            iloc = j;
          }
        }
      }
      printf("In Pickpoint x = %lf Cp=%lf\n",element[k].xfitted[iloc],-element[k].ycontrol[iloc]);
      activeElement = k;
      activeCP = iloc;
    }

   activePoint = iloc;

/* Now we know which segment and point gives the closest point */
     
   XClearArea(XtDisplay(drawing_area),XtWindow(drawing_area),0,0,0,0,True); 
}
/*========================== PICKCONTROLPOINT ==============================*/
/*                                                                          */
/* Pick a control point and highlight it                                    */
/*                                                                          */
/*==========================================================================*/
void pickControlPoint(int xpoint, int ypoint)
{
    Arg al[10];
    int ac;
    int i,j,k, iloc;
    int w0,w1,h0,h1;
    double x1,y1,x0,y0;
    double dist, temp;
    double slopeX, interceptX;
    double slopeY, interceptY;
    XExposeEvent *event;

    h0 = margin;
    w0 = margin;
    h1 = currentHeight - margin;
    w1 = currentWidth - margin;

/* We have the coordinate of the point in screen coordinates */
/* Lets convert it to (x,y) so we can find the closest point */

    slopeX = (cmaxX - cminX)/(double)(w1 - w0);
    interceptX = .5*(cmaxX + cminX - slopeX*(w0 + w1));
    x0 = (double)(slopeX*xpoint + interceptX);
    slopeY = (cmaxY - cminY)/(double)(h0 - h1);
    interceptY = .5*(cmaxY + cminY - slopeY*(h0 + h1));
    y0 = (double)(slopeY*ypoint + interceptY);

/* Now find the closest point */
    dist = 10000.0;

    for(i=0; i<numElements; i++)
    {
      if(element[i].fitted)
      {
        for(j=0; j<=element[i].noControl; j++)
        {
        temp = (element[i].xcontrol[j] - x0)*(element[i].xcontrol[j] - x0) + (element[i].ycontrol[j] - y0)*(element[i].ycontrol[j] - y0);
          if(temp <= dist)
          {
            activeElement = i;
            dist = temp;
            iloc = j;
          }
        }
      }
    }

   activeCP = iloc;

/* Now we know which segment and point gives the closest point */
     
   XClearArea(XtDisplay(drawing_area),XtWindow(drawing_area),0,0,0,0,True); 
}
/*========================== FILECB ========================================*/
/*                                                                          */
/* Call back for the file menu                                              */
/*                                                                          */
/*==========================================================================*/
void fileCB(Widget w, char *client_data, XmAnyCallbackStruct *call_data)
{
/*    printf("%s\n",client_data); */
    if(strcmp(client_data,"Open")==0)
    {
     fileread = 1;
     XtManageChild(filedialog);
    }

    if(strcmp(client_data,"Quit")==0)
    {
      if(filetype !=2)writeFile();
      if(filetype ==2)writePressureFile();
      exit(0);
    }

    if(strcmp(client_data,"Input Line")==0)
    {
      printf("Input Line\n");
      if(!showLines)
      {
        showLines = True;
      }
      else
      {
        showLines = False;
      }
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
       if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
       drawPoints();
    }
    if(strcmp(client_data,"Input Symbol")==0)
    {
      printf("Input Symbol\n");
      if(!showSymbols)
      {
        showSymbols = True;
      }
      else
      {
        showSymbols = False;
      }
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
       if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
       drawPoints();
    }

    if(strcmp(client_data,"Spline Line")==0)
    {
      printf("Spline Line\n");
      if(!showFitLines)
      {
        showFitLines = True;
      }
      else
      {
        showFitLines = False;
      }
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
       if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
       drawPoints();
    }
    if(strcmp(client_data,"Spline Symbol")==0)
    {
      printf("Spline Symbol\n");
      if(!showFitSymbols)
      {
        showFitSymbols = True;
      }
      else
      {
        showFitSymbols = False;
      }
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
       if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
       drawPoints();
    }
    if(strcmp(client_data,"Control Points")==0)
    {
      printf("Control Points\n");
      if(!showBsymbols)
      {
        showBsymbols = True;
      }
      else
      {
        showBsymbols = False;
      }
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
       if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
       drawPoints();
    }
    if(strcmp(client_data,"Limits")==0)
    {
      printf("Limits\n");
      if(!showLimits)
      {
        showLimits = True;
      }
      else
      {
        showLimits = False;
      }
/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
       if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
       drawPoints();
    }
}

/*========================== WRITEFILE =====================================*/
/*                                                                          */
/* Write output into design.data                                            */
/* output  = design.data type file                                          */
/* output2 = spline.out type file                                           */
/* output3 = maker.input type file                                          */
/*                                                                          */
/*==========================================================================*/
void writeFile(void)
{
     FILE *output, *output2, *output3;
     int nbodies,nconst,nobj,nside,ishape;
     int itranx, itrany, irotate;
     int i,j,k,jj, icount;
     int iloc1, iloc2;
     int numberOfPoints;
     int index[maxSegments];
     int order, nseg;
     int index1, index2;
     int seg1;
     int ktran;
     int kk;
     double xrot, yrot, scale, grad;
     double xcontrol, xupper, xlower;
     double ycontrol, yupper, ylower;
     double x1,y1,x2,y2;
     double dist1, dist2, temp;
     double dx,dy;
     double dt, t[maxSegments];

/* Before doing anything, check to see if all the elements have been fit */

   j = 0;
   printf("In writeFile j=%d\n",j);
   for(i=0; i<numElements; i++)
   {
     if(!element[i].fitted)
     {
       printf("element %d not fit\n",i);
       j++;
     }
   }
   if(j)
   {
     printf("Cant write files since elements arent fit\n");
     exit(2);
   }
/* Write the design.data file */
      if( (output = fopen("design.dog","w")) == (FILE *)NULL)
      {
        printf("can't open design.data file");
        exit(2);
      }
      nbodies = numElements;
      nconst  = 0;
      nobj    = 1;
      nside   = 1;
/*      imach   = 0; */
/*      ialpha  = 0; */
      ishape  = 1;
      itranx  = 0;
      itrany  = 0;
      irotate = 0;
      xrot    = 0.;
      yrot    = 0.;
      scale   = 1.;
      grad    = 99.;
      fprintf(output,"    NBODIES    NCONST      NOBJ     NSIDE     IMACH         IALPHA   ISHAPE\n");
      fprintf(output,"      %d           %d          %d        %d        %d              %d       %d\n",
      nbodies,nconst,nobj,nside,imach,ialpha,ishape);
      for(i=0; i<nbodies; i++)
      {
        itranx  = element[i].itranx;
        itrany  = element[i].itrany;
        irotate = element[i].irotate;
        xrot    = element[i].xrot;
        yrot    = element[i].yrot;
        fprintf(output," Element  %d    NDESIGN    NKNOTS   ITRANX  ITRANY    IROTATE   XROT       YROT\n",i+1);
        if(i==0)
        {
         fprintf(output,"                  %d        %d        %d      %d         %d      %.9le   %.9le\n",
         2*element[i].noControl+imach+ialpha+ntran,element[i].noKnots,itranx,itrany,irotate,xrot,yrot);
        }
        else
        {
         fprintf(output,"                  %d        %d        %d      %d         %d      %.9le   %.9le\n",
         2*element[i].noControl,element[i].noKnots,itranx,itrany,irotate,xrot,yrot);
        }
        fprintf(output,"  Design Variables     Lower         Upper          Scale\n");
        icount = 0;
        if(imach == 1 && i==0)
        {
          icount += 1;
          fprintf(output," %d     %.9le     %.9le     %.9le      %.9le\n",icount,xmach,xmachmin,xmachmax,scale);
          if(ialpha == 1)
          {
            icount += 1;
            fprintf(output," %d     %.9le     %.9le     %.9le      %.9le\n",icount,alpha,alphamin,alphamax,scale);
          }
        }
        else
        {
          if(ialpha == 1 && i==0)
          {
            icount += 1;
            fprintf(output," %d     %.9le     %.9le     %.9le      %.9le\n",icount,alpha,alphamin,alphamax,scale);
          }
        }

       if(ntran >= 1 && i==0)
       {
         for(kk=0; kk < ntran; kk++)
         {
            icount += 1;
            fprintf(output," %d     %.9le     %.9le     %.9le      %.9le\n",icount,dtran[kk],mintran[kk],maxtran[kk],scale);
         }
       }

        for(j=0; j<element[i].noControl; j++)
        {
          xcontrol = element[i].xcontrol[j];
          xupper   = element[i].xupper[j];
          xlower   = element[i].xlower[j];
          ycontrol = element[i].ycontrol[j];
          yupper   = element[i].yupper[j];
          ylower   = element[i].ylower[j];
          icount += 1;
          fprintf(output," %d     %.9le     %.9le     %.9le      %.9le\n",icount,xcontrol,xlower,xupper,scale);
          icount += 1;
          fprintf(output," %d     %.9le     %.9le     %.9le      %.9le\n",icount,ycontrol,ylower,yupper,scale);
         }
       }
         fprintf(output,"   Objective Functions\n");
         fprintf(output,"   1     0.0    \n");

/* Print out the gradients */
      for(i=0; i<nbodies; i++)
      {
        fprintf(output," Gradient of Design Variables for element  %d\n",i+1);
        if(i==0)
        {
          for(j=0; j<2*element[i].noControl + imach + ialpha + ntran; j++)
          {
            fprintf(output," %d     %.9le\n",j+1,grad);
          }
        }
        else
        {
          for(j=0; j<2*element[i].noControl; j++)
          {
            fprintf(output," %d     %.9le\n",j+1,grad);
          }
        }
       
       }
       fprintf(output,"   Functions: Omega1    Omega2     Omega3     Omega4\n");
       fprintf(output,"            (cd-cdd)^2 (cp-cpd)^2 (cl-cld)^2    cd/cl\n");
       fprintf(output,"        %.9le     %.9le     %.9le      %.9le\n",domega1,domega2,domega3,domega4);
       fprintf(output," Target values: clstar       cdstar       cmstar\n");
       fprintf(output,"                0.0000       0.0000       0.0000\n");
       fprintf(output," Aerodynamic coefficients: cltot      cdtot     cmtot\n");
       fprintf(output,"                          0.0000      0.0000    0.0000\n");
/* Print out the knot sequence */
      for(i=0; i<nbodies; i++)
      {
        fprintf(output," Knot sequence for element %d\n",i+1);
        for(j=0; j<element[i].noKnots; j++)
        {
          fprintf(output," %.9le\n",element[i].knots[j]);
        }
      }
/* Print out the parameterization for each body */
      for(i=0; i<nbodies; i++)
      {
        fprintf(output," %d Parameterization for element %d\n",element[i].numEval,i+1);
        for(j=0; j<element[i].numEval; j++)
        {
          fprintf(output," %.9le\n",element[i].uvalue[j]);
        }
      }

/* Write the spline.out type of file (input to grid generation ) */
      
      if( (output2 = fopen("spline.dog","w")) == (FILE *)NULL)
      {
        printf("can't open spline.dog file");
        exit(3);
      }
      fprintf(output2," %d\n",numElements);
      for(i=0; i<numElements; i++)
      {
        dt = 1./(double)((element[i].numEval - 1));
        order = element[i].order;
        nseg = 0;

/* Loop over the knots for the element and find the places where there is a break */

        fprintf(output2," %d\n",element[i].noSegments);
        for(j=0; j<=element[i].noKnots-element[i].order; j++)
        {
           if(element[i].knots[j] == element[i].knots[j+order-1])
           {
             t[nseg] = element[i].knots[j];
             index[nseg] = j;
             nseg++;
           }
        }
        printf("For element %d there are %d segments\n",i,nseg-1);
        seg1 = element[i].firstSegment;
        index1 = 0;
        for(j=0; j<nseg-1; j++)
        {
          printf(" segment %d t=%lf\n",j,t[j]);
/*
          index1 = t[j]/dt;
          index2 = t[j+1]/dt;
*/

/* Find the correct index2 */
          index2 = index1 + 1;
          while(element[i].uvalue[index2] < t[j+1] && index2 < element[i].numEval - 1)
          {
            index2 += 1;
          }
        
          fprintf(output2," %d %d\n",index2 - index1 +1,segment[seg1].type);
          seg1 += 1;
          for(k=index1; k<=index2; k++)
          {
            fprintf(output2," %.9le %.9le\n",element[i].xfitted[k],element[i].yfitted[k]);
          }
          index1 = index2;
        }
      }

/* Now write output for maker.input */
      if( (output3 = fopen("maker.dog","w")) == (FILE *)NULL)
      {
        printf("can't open maker.dog file");
        exit(3);
      }
      fprintf(output3," %d\n",numElements);
      for(i=0; i<numElements; i++)
      {
        fprintf(output3," %d\n",element[i].numEval-1);
      }
      for(i=0; i<numElements; i++)
      {
        for(j=0; j<element[i].numEval-1; j++)
        {
          fprintf(output3," %.9le %.9le\n",element[i].xfitted[j],element[i].yfitted[j]);
        }
      }
        
      exit(0);
}
/*========================== WRITEPRESSUREFILE =============================*/
/*                                                                          */
/* Write output into design.data                                            */
/* output  = design.cps type file                                           */
/*                                                                          */
/*==========================================================================*/
void writePressureFile(void)
{
     FILE *output;
     int i,j,k, nbodies;
     int numberOfPoints;
     double xcoord, ycoord, cpvalue, ones;

/* Before doing anything, check to see if all the elements have been fit */

   j = 0;
   printf("In writeFile j=%d\n",j);
   for(i=0; i<numElements; i++)
   {
     if(!element[i].fitted)
     {
       printf("element %d not fit\n",i);
       j++;
     }
   }
   if(j)
   {
     printf("Cant write files since elements arent fit\n");
     exit(2);
   }
/* Write the design.cps file */
      if( (output = fopen("design.cpdog","w")) == (FILE *)NULL)
      {
        printf("can't open design.cpdog file");
        exit(2);
      }
      nbodies = numElements;
      fprintf(output,"# Viscous cps\n");
      fprintf(output,"   4\n");
      fprintf(output,"   x/c\n");
      fprintf(output,"   c_p\n");
      fprintf(output,"   y\n");
      fprintf(output,"   ones\n");
      for(i=0; i<nbodies; i++)
      {
        fprintf(output," %d\n",element[i].noControl);
        for(j=0; j<element[i].noControl; j++)
        {
          xcoord  =  element[i].xfitted[j];
          ycoord  =  element[i].yfitted[j];
          cpvalue = -element[i].ycontrol[j];
          ones    =  element[i].xupper[j];
          fprintf(output," %.9le    %.9le     %.9le     %.9le\n",xcoord,cpvalue,ycoord,ones);
         }
       }
      exit(0);
}
/*========================== MAKEMENU ======================================*/
/*                                                                          */
/* Creates a menu on the menu bar                                           */
/*                                                                          */
/*==========================================================================*/
Widget makeMenu(char *menu_name, Widget menu_bar,int tearOff)
{
    int ac;
    Arg al[10];
    Widget menu, cascade;
    ac = 0;
    if(tearOff) 
    {
#ifdef XmNtearOffModel
      XtSetArg(al[ac],XmNtearOffModel,XmTEAR_OFF_ENABLED); ac++;
#endif
    }
    menu=XmCreatePulldownMenu(menu_bar,menu_name,al,ac);
    ac=0;
    XtSetArg (al[ac],XmNsubMenuId, menu); ac++;
    XtSetArg(al[ac],XmNlabelString, XmStringCreateLtoR(menu_name,char_set)); ac++;
    cascade=XmCreateCascadeButton(menu_bar,menu_name,al,ac);
    XtManageChild(cascade);
    return(menu);
}

/*========================== CREATEMENUS ===================================*/
/*                                                                          */
/* Creates all the menus for the program                                    */
/*                                                                          */
/*==========================================================================*/
void createMenus(Widget menu_bar)
{
    int tearOff;
    int type; /* 0=regular 1=toggle */
    /* create the file menu */
    tearOff = 0;
    type = 0;
    fileMenu=makeMenu("File",menu_bar,tearOff);
    openItem=makeMenuItem("Open","Open",fileMenu,type,True);
    quitItem=makeMenuItem("Quit","Quit",fileMenu,type,True);

    tearOff = 1;
    type = 1;
    symbolMenu=makeMenu("Symbols",menu_bar,tearOff);
    inputcurveline=makeMenuItem("Input Line","Input Line",symbolMenu,type,True);
    inputcurvesymbol=makeMenuItem("Input Symbol","Input Symbol",symbolMenu,type,False);
    splinecurveline=makeMenuItem("Spline Line","Spline Line",symbolMenu,type,True);
    splinecurvesymbol=makeMenuItem("Spline Symbol","Spline Symbol",symbolMenu,type,False);
    Bsplinesymbol=makeMenuItem("Control Points","Control Points",symbolMenu,type,True);
    Bsplinelimits=makeMenuItem("Limits","Limits",symbolMenu,type,True);
 
}

/*========================== SETUP_GC ======================================*/
/*                                                                          */
/* Set up the graphic context                                               */
/*                                                                          */
/*==========================================================================*/
void setup_gc(void)
/* set up the graphics context. */
{
    int foreground,background;
    XGCValues vals;
    Arg al[10];
    XColor desired, actual;
    Colormap cmap;
    int ac;
 
    /* get the current fg and bg colors. */
    ac=0;
    XtSetArg(al[ac],XmNforeground,&foreground); ac++;
    XtSetArg(al[ac],XmNbackground,&background); ac++;
    XtGetValues(drawing_area,al,ac);
 
    /* create the gc. */
    vals.foreground = foreground;
    vals.background = background;
    gc=XtGetGC(drawing_area,GCForeground | GCBackground,&vals);

    cmap = DefaultColormapOfScreen(XtScreen(drawing_area));
    XAllocNamedColor(XtDisplay(drawing_area),cmap,"red",&desired,&actual);
    vals.foreground = actual.pixel;
    vals.background = background;
    gc_red=XtGetGC(drawing_area,GCForeground | GCBackground,&vals);

    cmap = DefaultColormapOfScreen(XtScreen(drawing_area));
    XAllocNamedColor(XtDisplay(drawing_area),cmap,"blue",&desired,&actual);
    vals.foreground = actual.pixel;
    vals.background = background;
    gc_green=XtGetGC(drawing_area,GCForeground | GCBackground,&vals);

    /* Filled for highlighting a control point */
    vals.fill_style = FillSolid;
    gc_green_filled=XtGetGC(drawing_area,GCForeground | GCBackground | GCFillStyle,&vals);

    /* gc for filled circles */
    vals.foreground = foreground;
    vals.background = background;
    vals.fill_style = FillSolid;
    gc_filled=XtGetGC(drawing_area,GCForeground | GCBackground | GCFillStyle,&vals);

    /* gc for XOR */
    vals.foreground = foreground ^ background;
    vals.function  = GXxor;
    vals.line_style = LineOnOffDash;
    gc_xor = XtGetGC(drawing_area,GCForeground | GCBackground | GCFunction | GCLineStyle, &vals);

    /* gc for XOR and filled*/
    gc_xor_filled = XtGetGC(drawing_area,GCForeground | GCBackground | GCFunction | GCFillStyle, &vals);

}

/*========================== EXPOSURECB ====================================*/
/*                                                                          */
/* Call back for exposure events                                            */
/*                                                                          */
/*==========================================================================*/
int exposureCB(Widget w,XtPointer client_data, XmDrawingAreaCallbackStruct *call_data)
/* called whenever drawing area is exposed. */
{
    Arg al[10];
    int ac;
    int i;
    double x1,y1,x2,y2;
    double slopeX, interceptX;
    double slopeY, interceptY;
    XExposeEvent *event;

/* If a file has been read so there is data, then proceed; otherwise return */
   if(fileread == 0)return((int)0);

/* Get the event and check to how many expose events we have */
/* We only want to redraw on the first one since we redraw the whole thing */
   event = (XExposeEvent *)call_data->event;
   if((int)event->count == 0)
   drawPoints();
}
/*========================== DRAWPOINTS ====================================*/
/*                                                                          */
/* Draws the coordinates of an airfoil                                      */
/*                                                                          */
/*==========================================================================*/
void drawPoints(void)
{
    Arg al[10];
    int ac;
    int i,j,k;
    int w0,w1,h0,h1;
    int x1,y1,x2,y2;
    int xlower, xupper;
    int ylower, yupper;
    int nTick; /* Number of tick marks */
    int tcount; /* Counter for tick marks */
    double slopeX, interceptX;
    double slopeY, interceptY;
    double deltay;
    XExposeEvent *event;
    char labels[10];

    h0 = margin;
    w0 = margin;
    h1 = currentHeight - margin;
    w1 = currentWidth - margin;

    slopeX = (double)(w0 - w1)/(cminX - cmaxX);
    interceptX = .5*((double)w0 + (double)w1 - slopeX*(cmaxX + cminX));
    slopeY = (double)(h0 - h1)/(cmaxY - cminY);
    interceptY = .5*((double)h0 + (double)h1 - slopeY*(cmaxY + cminY));

    for(i = 0; i<numElements; i++)
    {
     if(showLines)
     {
       for(j=element[i].firstSegment; j<=element[i].lastSegment; j++)
       {
         for(k=0; k<segment[j].numberOfPoints-1; k++)
         {
           x1 = (int)(slopeX*segment[j].x[k]   + interceptX);
           x2 = (int)(slopeX*segment[j].x[k+1] + interceptX);
           y1 = (int)(slopeY*segment[j].y[k]   + interceptY);
           y2 = (int)(slopeY*segment[j].y[k+1] + interceptY);
           XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
         }
       }
     }
      if(element[i].fitted)
      {
        if(showFitLines)
        {
          for(k=0; k<element[i].numEval-1; k++)
          {
            x1 = (int)(slopeX*element[i].xfitted[k]   + interceptX);
            x2 = (int)(slopeX*element[i].xfitted[k+1] + interceptX);
            y1 = (int)(slopeY*element[i].yfitted[k]   + interceptY);
            y2 = (int)(slopeY*element[i].yfitted[k+1] + interceptY);
            XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc_red,x1,y1,x2,y2);
          }
        }
        if(showFitSymbols)
        {
          for(k=0; k<element[i].numEval; k++)
          {
            x1 = (int)(slopeX*element[i].xfitted[k] + interceptX - (double)symbolSize/2);
            y1 = (int)(slopeY*element[i].yfitted[k] + interceptY - (double)symbolSize/2);
            XDrawArc(XtDisplay(drawing_area),XtWindow(drawing_area),gc_red,x1,y1,symbolSize,symbolSize,0,23040);
          }
        }
        if(showLimits)
        {
          for(k=0; k<element[i].noControl; k++)
          {
            x1 = (int)(slopeX*element[i].xcontrol[k]   + interceptX);
            y1 = (int)(slopeY*element[i].ycontrol[k]   + interceptY);
            if(element[i].xupper[k] >= element[i].xcontrol[k])
            {
              x2 = (int)(slopeX*element[i].xupper[k] + interceptX);
              y2 = y1;
              XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green,x1,y1,x2,y2);
            }
            if(element[i].xlower[k] <= element[i].xcontrol[k])
            {
              x2 = (int)(slopeX*element[i].xlower[k] + interceptX);
              y2 = y1;
              XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green,x1,y1,x2,y2);
            }
            if(element[i].yupper[k] >= element[i].ycontrol[k])
            {
              y2 = (int)(slopeY*element[i].yupper[k] + interceptY);
              x2 = x1;
              XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green,x1,y1,x2,y2);
            }
            if(element[i].ylower[k] <= element[i].ycontrol[k])
            {
              y2 = (int)(slopeY*element[i].ylower[k] + interceptY);
              x2 = x1;
              XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green,x1,y1,x2,y2);
            }
          }
        }
        if(showBsymbols)
        {
          for(k=0; k<element[i].noControl; k++)
          {
             if(!(i%2))
             {
               x1 = (int)(slopeX*element[i].xcontrol[k] + interceptX - (double)symbolSize/2);
               y1 = (int)(slopeY*element[i].ycontrol[k] + interceptY - (double)symbolSize/2);
               XDrawArc(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green,x1,y1,symbolSize,symbolSize,0,23040);
             }
             else
             {
               x1 = (int)(slopeX*element[i].xcontrol[k] + interceptX - (double)symbolSize/2);
               y1 = (int)(slopeY*element[i].ycontrol[k] + interceptY - (double)symbolSize/2);
               XDrawRectangle(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green,x1,y1,symbolSize,symbolSize);
             }
             if(filetype==2 && k<=element[i].noControl-2)
             {
               x1 = (int)(slopeX*element[i].xcontrol[k] + interceptX);
               y1 = (int)(slopeY*element[i].ycontrol[k] + interceptY);
               x2 = (int)(slopeX*element[i].xcontrol[k+1] + interceptX);
               y2 = (int)(slopeY*element[i].ycontrol[k+1] + interceptY);
               XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc_red,x1,y1,x2,y2);
             }
          }
            if(filetype==2) /* If working with a pressure distribution, draw the axis */
            {
               forces(i);
               /* y-axis */
               x1 = (int)(slopeX*cminX + interceptX);
               y1 = (int)(slopeY*minY + interceptY);
               x2 = (int)(slopeX*cminX + interceptX);
               y2 = (int)(slopeY*maxY+ interceptY);
               XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
               /* x-axis */
               x1 = (int)(slopeX*cminX + interceptX);
               y1 = (int)(slopeY*.5*(minY + maxY) + interceptY);
               y1 = (int)(slopeY*0.0*(minY + maxY) + interceptY);
               x2 = (int)(slopeX*maxX + interceptX);
               y2 = (int)(slopeY*.5*(minY + maxY) + interceptY);
               y2 = (int)(slopeY*0.0*(minY + maxY) + interceptY);
               XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
               /* Draw tick marks along lines of constant y */
               /* First from zero to maxY                   */
                nTick = 2*(int)maxY;
                deltay = maxY/((double)nTick);
                for(tcount=1; tcount <=nTick; tcount+=2)
                {
                  x1 = (int)(slopeX*cminX + interceptX);
                  y1 = (int)(slopeY*tcount*deltay + interceptY);
                  x2 = (int)(slopeX*cminX + 5 + interceptX);
                  y2 = y1;
                  XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
                  x1 = (int)(slopeX*cminX + interceptX);
                  y1 = (int)(slopeY*(tcount+1)*deltay + interceptY);
                  x2 = (int)(slopeX*cminX + 10 + interceptX);
                  y2 = y1;
                  XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
                  /* Put the label on */
                  sprintf(labels,"%3d",(int)(-(tcount + 1)*deltay));
                  XDrawString(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1-margin+1,y1,labels,strlen(labels));
                }
               /* Now from minY to zero                   */
                nTick = abs(2*(int)minY);
                deltay = fabs(minY/((double)nTick));
                for(tcount=1; tcount <=nTick; tcount+=2)
                {
                  x1 = (int)(slopeX*cminX + interceptX);
                  y1 = (int)(slopeY*(-tcount*deltay) + interceptY);
                  x2 = (int)(slopeX*cminX + 5 + interceptX);
                  y2 = y1;
                  XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
                  x1 = (int)(slopeX*cminX + interceptX);
                  y1 = (int)(slopeY*(-(tcount+1)*deltay) + interceptY);
                  x2 = (int)(slopeX*cminX + 10 + interceptX);
                  y2 = y1;
                  XDrawLine(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,x2,y2);
                  /* Put the label on */
                  sprintf(labels,"%3d",(int)((tcount + 1)*deltay));
                  XDrawString(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1-margin+1,y1,labels,strlen(labels));
                }
                
            }
        } /* End of if on showBsymbols */
      }
    }
    if(showSymbols==1)
    {
      for(i = 0; i<numElements; i++)
      {
       for(j=element[i].firstSegment; j<=element[i].lastSegment; j++)
       {
         for(k=0; k<segment[j].numberOfPoints; k++)
         {
           x1 = (int)(slopeX*segment[j].x[k] + interceptX - (double)symbolSize/2);
           y1 = (int)(slopeY*segment[j].y[k] + interceptY - (double)symbolSize/2);
           XDrawArc(XtDisplay(drawing_area),XtWindow(drawing_area),gc,x1,y1,symbolSize,symbolSize,0,23040);
         }
       }
      }
    }
    if(activePoint != -1 && filetype !=2)
    {
       x1 = (int)(slopeX*segment[activeSegment].x[activePoint] + interceptX - (double)symbolSize/2);
       y1 = (int)(slopeY*segment[activeSegment].y[activePoint] + interceptY - (double)symbolSize/2);
       XFillArc(XtDisplay(drawing_area),XtWindow(drawing_area),gc_filled,x1,y1,symbolSize,symbolSize,0,23040);
    }
    if(activeCP != -1)
    {
       x1 = (int)(slopeX*element[activeElement].xcontrol[activeCP] + interceptX - (double)symbolSize/2);
       y1 = (int)(slopeY*element[activeElement].ycontrol[activeCP] + interceptY - (double)symbolSize/2);
       XFillArc(XtDisplay(drawing_area),XtWindow(drawing_area),gc_green_filled,x1,y1,symbolSize,symbolSize,0,23040);
    }
    if(filetype !=2)printf("In drawPoints activeSegment and activePoint = %d %d\n",activeSegment+1,activePoint+1);
}
/*========================== RESIZECB ======================================*/
/*                                                                          */
/* Call back for resize of drawing area                                     */
/*                                                                          */
/*==========================================================================*/
void resizeCB(Widget w,XtPointer client_data, XmDrawingAreaCallbackStruct call_data)
/* called whenever drawing area is exposed. */
{
    Arg al[10];
    int ac;
    int i;
    Dimension height,width;
    double x1,y1,x2,y2;
    double slopeX, interceptX;
    double slopeY, interceptY;

/* If the widget is realized, clear the drawing area in */
/* case the expose event is generated from a resize     */
   if(XtIsRealized(w))XClearArea(XtDisplay(w),XtWindow(w),0,0,0,0,True);

/* Get the current size of the drawing area */
    ac=0;
    XtSetArg(al[ac],XmNheight,&height); ac++;
    XtSetArg(al[ac],XmNwidth,&width); ac++;
    XtGetValues(drawing_area,al,ac);
    currentHeight = (int)height;
    currentWidth  = (int)width;
}

/*============================ MAXXY =======================================*/
/*                                                                          */
/* Find the max and min coordinates                                         */
/*                                                                          */
/*==========================================================================*/
void maxXY(void)
{
   int i,j,k;
   double test;

   maxX = -99000.;
   minX =  99000.;
   maxY = -99000.;
   minY =  99000.;

   for(i=0; i<numElements; i++)
   {
     for(j=element[i].firstSegment; j<=element[i].lastSegment; j++)
     {
       for(k=0; k<segment[j].numberOfPoints; k++)
       {
          maxX = MAX(maxX,segment[j].x[k]);
          minX = MIN(minX,segment[j].x[k]);
          maxY = MAX(maxY,segment[j].y[k]);
          minY = MIN(minY,segment[j].y[k]);
       }
     }
      if(element[i].fitted)
      {
        for(k=0; k<element[i].noControl; k++)
        {
           maxX = MAX(maxX,element[i].xcontrol[k]);
           minX = MIN(minX,element[i].xcontrol[k]);
           maxY = MAX(maxY,element[i].ycontrol[k]);
           minY = MIN(minY,element[i].ycontrol[k]);
        }
      }
   }

/* If we are manipulating the pressure distribution, then try to find a reasonable scale for the y-axis */
      if(filetype==2)
      {
        k = 1;
        printf("minY maxY=%lf %lf\n",minY,maxY);
        /* Find smaxY */
        while(k<1000)
        {
         test = maxY/((double)k++);
         if(test <= 1.0)break; 
        }
        printf("K value=%d\n",k);
        smaxY = (double)(k-1);
        /* Now find sminY */
        k = 1;
        while(k<1000)
        {
         test = abs(minY)/((double)k++);
         if(test <= 1.0)break; 
        }
        printf("K min value=%d\n",k);
        sminY = (double)(k);
        if(minY<=0.)sminY = -(double)(k);
        printf("sminY smaxY=%lf %lf\n",sminY,smaxY);
        /* Now, reset min and max Y so they correspond */
        minY = sminY;
        maxY = smaxY;
      }

    printf("min and max X = %lf %lf\n",minX,maxX);
    printf("min and max Y = %lf %lf\n",minY,maxY);
}

/*========================== GETCOORDS =====================================*/
/*                                                                          */
/* Read in the coordinates of the airfoil                                   */
/*                                                                          */
/*==========================================================================*/
void getCoords(char *datafile)
{
   int i,j,k;
   int idum;
   int maxdegree, maxcontrol;
   FILE *input;
   FILE *outtest;

/* Open file */
/*   if( (input = fopen("fort.91","r")) == (FILE *)NULL) */
   if( (input = fopen(datafile,"r")) == (FILE *)NULL)
   {
     printf("can't open data file");
     exit(2);
   }
   if( (outtest = fopen("fort.91","w")) == (FILE *)NULL)
   {
     printf("can't open outtest file");
     exit(2);
   }

/* Read data */
   fscanf(input,"%d\n", &numElements);
   fprintf(outtest,"%d\n", numElements);
   idum = 0; /* Used to keep track of the segment numbers */
   for(i=0; i<numElements; i++)
   {
     maxdegree   = 10000; /* Just set to something big */
     fscanf(input,"%d\n", &element[i].noSegments);
     fprintf(outtest,"%d\n", element[i].noSegments);
     printf("npoints=%d\n", element[i].noSegments);
     element[i].firstSegment = idum;
     element[i].lastSegment  = idum + element[i].noSegments -1;
     element2segment[i] = element[i].firstSegment;
     element[i].order = baselineOrder;
     element[i].numEval = baselineNoEval;
     if(fileRead==False)element[i].fitted = 0; /* If we havent previously read in a file     */
                                          /* then initialize whether or not it has been fit  */
                                         /* This is used in memory management for allocating */
   /* Allocate a small amount of memory for uvalue. This will be reallocated as we need it inside evalbspline */
     element[i].uvalue = (double *)calloc(1,sizeof(double));

/* Now, for each segment on the airfoil read in the points */

     for(j=0; j< element[i].noSegments;j++)
     {
       maxcontrol = 10000; /* Just set to something big */
       fscanf(input,"%d %d\n",&(segment[idum].numberOfPoints),&(segment[idum].type));
       fprintf(outtest," %d %d\n",segment[idum].numberOfPoints,segment[idum].type);
       fcalloc(segment[idum].numberOfPoints, &(segment[idum].x));
       fcalloc(segment[idum].numberOfPoints, &(segment[idum].y));
       fcalloc(segment[idum].numberOfPoints, &(segment[idum].tval));
       maxdegree = MIN(maxdegree,segment[idum].numberOfPoints-1);
       maxcontrol = MIN(maxcontrol,segment[idum].numberOfPoints);
       maxcontrol = 200; /* This MUST BE FIXED LATER so we properly account for the max # of control points */
       segment[idum].noControl = MIN(baselineNoControl,maxcontrol);
       segment[idum].maxcontrol = maxcontrol;
       printf("in COORDS segment[%d].noControl=%d\n",idum,segment[idum].maxcontrol);
       for(k=0; k< segment[idum].numberOfPoints; k++)
       {
         fscanf(input," %le %le\n", &(segment[idum].x[k]), &(segment[idum].y[k]));
         fprintf(outtest," %le %le\n", segment[idum].x[k], segment[idum].y[k]);
/*         segment[idum].noControl = baselineNoControl; */
       } /* End of reading points on a segment */
       idum += 1;
     }   /* End of loop over segments          */
     element[i].order = MIN(baselineOrder,maxdegree);
     element[i].maxdegree = maxdegree;
   }     /* End of loop over elements          */
   element2segment[numElements] = element[numElements-1].lastSegment + 1;
   fileRead = True;
/* Set the symbol menu so you can turn lines/symbols off */
   XtSetSensitive(inputcurveline,True);
   XtSetSensitive(inputcurvesymbol,True);
/*   exit(3); */
}
/*========================== GETDESIGNDATA =================================*/
/*                                                                          */
/* Read in the design.data file                                             */
/*                                                                          */
/*==========================================================================*/
void getDesignData(char *datafile)
{
   int i,j,k, jj;
   int idum, ishape;
   int ndesign[maxElements];
   int maxdegree, maxcontrol;
   int ktran;
   char dumstring[501];
   FILE *input;
   FILE *outtest;

/* Open file */
/*   if( (input = fopen("fort.91","r")) == (FILE *)NULL) */
   if( (input = fopen(datafile,"r")) == (FILE *)NULL)
   {
     printf("can't open data file");
     exit(2);
   }
   if( (outtest = fopen("fort.91","w")) == (FILE *)NULL)
   {
     printf("can't open outtest file");
     exit(2);
   }

   rewind(input);

/* Read data */
   fgets(dumstring,500,input); /* Title line */
   fscanf(input,"%d %*d %*d %*d %d %d %d\n", &numElements, &imach, &ialpha, &ishape);
   fprintf(outtest,"%d\n", numElements);

/* Read the design data for each element */
   for(i=0; i<numElements; i++)
   {
     fgets(dumstring,500,input);
     fscanf(input,"%d %d %d %d %d %lf %lf\n", &ndesign[i], &element[i].noKnots,
               &element[i].itranx, &element[i].itrany, &element[i].irotate, &element[i].xrot, &element[i].yrot);
     element[i].noControl = ndesign[i]/2;
     if(i == 0)element[i].noControl = (ndesign[i] - imach - ialpha - ntran)/2;

/* Allocate memory for this element */
     fcalloc(element[i].noControl, &(element[i].xcontrol));
     fcalloc(element[i].noControl, &(element[i].ycontrol));
     fcalloc(element[i].noControl, &(element[i].xlower));
     fcalloc(element[i].noControl, &(element[i].xupper));
     fcalloc(element[i].noControl, &(element[i].ylower));
     fcalloc(element[i].noControl, &(element[i].yupper));
     fcalloc(element[i].noControl, &(element[i].xcontrolO));
     fcalloc(element[i].noControl, &(element[i].ycontrolO));
     fcalloc(element[i].noKnots,   &(element[i].knots));


     fgets(dumstring,500,input);
     element[i].order = element[i].noKnots - element[i].noControl + 1;
     printf("In getDesignData order = %d\n",element[i].order);
     element[i].numEval = baselineNoEval;

/* Allocate these later once we have read in the size */
/*     fcalloc(element[i].numEval, &(element[i].xfitted)); */
/*     fcalloc(element[i].numEval, &(element[i].yfitted)); */
     element[i].fitted = 1; /* If we havent previously read in a file     */
                                          /* then initialize whether or not it has been fit  */
                                         /* This is used in memory management for allocating */
     somethingFit = 1;
/* Now, read in the control points */

     jj = 0;
     for(j=0; j< element[i].noControl;j++)
     {
       if((i==0 && j==0) && (imach ==1 || ialpha ==1))
       {
         if(imach == 1)
         {
          fscanf(input," %*d %lf %lf %lf %*lf\n",&xmach, &xmachmin, &xmachmax);
          if(ialpha == 1)
          {
           fscanf(input," %*d %lf %lf %lf %*lf\n",&alpha, &alphamin, &alphamax);
          }
         }
         else
         {
           fscanf(input," %*d %lf %lf %lf %*lf\n",&alpha, &alphamin, &alphamax);
         }
       }
       if((i==0 && j==0) && ntran >= 1)
       {
         for(ktran=0; ktran < ntran; ktran++)
         {
           fscanf(input, " %*d %lf %lf %lf %*lf\n",&dtran[ktran], &mintran[ktran], &maxtran[ktran]);
         }
       }
       fscanf(input," %*d %lf %lf %lf %*lf\n",&element[i].xcontrol[jj], &element[i].xlower[jj], &element[i].xupper[jj]);
       fscanf(input," %*d %lf %lf %lf %*lf\n",&element[i].ycontrol[jj], &element[i].ylower[jj], &element[i].yupper[jj]);
       element[i].xcontrolO[jj] = element[i].xcontrol[jj];
       element[i].ycontrolO[jj] = element[i].ycontrol[jj];
       jj += 1;
       
      }
/* Make it so there is only one segment on each element */
     element[i].noSegments = 1;
     element[i].firstSegment = i;
     element[i].lastSegment = i;
     segment[i].numberOfPoints = 2;
     fcalloc(segment[i].numberOfPoints, &(segment[i].x));
     fcalloc(segment[i].numberOfPoints, &(segment[i].y));
     segment[i].x[0] = element[i].xcontrol[0];
     segment[i].y[0] = element[i].ycontrol[0];
     segment[i].x[1] = element[i].xcontrol[element[i].noControl-1];
     segment[i].y[1] = element[i].ycontrol[element[i].noControl-1];

     }

/* Objective function line */
     fgets(dumstring,500,input);
     fgets(dumstring,500,input);
/* Read the gradients */
     for(i=0; i<numElements; i++)
     {
       fgets(dumstring,500,input);
        
       for(j=0; j< ndesign[i]; j++)
       {
         fgets(dumstring,500,input);
       }
     }

/* Read in 2 blank lines */
         fgets(dumstring,500,input);
         fgets(dumstring,500,input);
/* Now read domega's */
         fscanf(input,"%lf %lf %lf %lf\n",&domega1,&domega2,&domega3,&domega4);
         fgets(dumstring,500,input);
         fgets(dumstring,500,input);
         fgets(dumstring,500,input);
         fgets(dumstring,500,input);

/* Now read in the knots */
     for(i=0; i<numElements; i++)
     {
       fgets(dumstring,500,input);
        
       for(j=0; j< element[i].noKnots; j++)
       {
         fscanf(input,"%lf\n",&(element[i].knots[j]));
       }
     }

/* Now read in the parameterization for each element */
     for(i=0; i<numElements; i++)
     {
       fgets(dumstring,500,input); 
       sscanf(dumstring,"%d",&(element[i].numEval)); 

/*       fscanf(input,"%d",&(element[i].numEval)); */
       element[i].initialNum = element[i].numEval;
       printf("In GETDESIGN numEval for element %d= %d\n",i,element[i].numEval);
       fcalloc(1,&(element[i].uvalue));
       fcalloc(element[i].numEval,&(element[i].uRead));
       fcalloc(element[i].numEval, &(element[i].xfitted));
       fcalloc(element[i].numEval, &(element[i].yfitted));
        
       for(j=0; j< element[i].numEval; j++)
       {
         fscanf(input,"%lf\n",&(element[i].uRead[j]));
         printf("%d %lf\n",j,element[i].uRead[j]);
       }
     }

   fileRead = True;
/* Set the symbol menu so you can turn lines/symbols off */
   XtSetSensitive(inputcurveline,False);
   XtSetSensitive(inputcurvesymbol,False);
   showLines = False;
   showSymbols = False;

    printf("DONE WITH GETDESIGN DATA\n");
/*   exit(3); */
}
/*========================== GETNTRAN ======================================*/
/*                                                                          */
/* Read in the design.data file and figure out what ntran is                */
/*                                                                          */
/*==========================================================================*/
void getNtran(char *datafile)
{
   int i,j,k, jj;
   int idum, ishape;
   int ndesign[maxElements];
   int maxdegree, maxcontrol;
   char dumstring[501];
   FILE *input;
   FILE *outtest;

/* Open file */
   if( (input = fopen(datafile,"r")) == (FILE *)NULL)
   {
     printf("can't open data file");
     exit(2);
   }
   if( (outtest = fopen("fort.91","w")) == (FILE *)NULL)
   {
     printf("can't open outtest file");
     exit(2);
   }

/* Read data */
   fgets(dumstring,500,input); /* Title line */
   fscanf(input,"%d %*d %*d %*d %d %d %d\n", &numElements, &imach, &ialpha, &ishape);

/* Read the design data for each element */
   for(i=0; i<numElements; i++)
   {
     fgets(dumstring,500,input);
     fscanf(input,"%d %d %d %d %d %lf %lf\n", &ndesign[i], &element[i].noKnots,
               &element[i].itranx,&element[i].itrany, &element[i].irotate, &element[i].xrot, &element[i].yrot);
     ntran += element[i].itranx + element[i].itrany + element[i].irotate;
     fgets(dumstring,500,input);

/* Now, read in the design variables */

     for(j=0; j< ndesign[i]; j++)
     {
       fgets(dumstring,500,input);
       
     }

     }

     printf("In getNtran ntran= %d\n",ntran);
     fclose(input);
/*   exit(3); */
}

/*========================== FCALLOC =======================================*/
/*                                                                          */
/* Allocates memory for floating point numbers                              */
/*                                                                          */
/*==========================================================================*/
void fcalloc(int size, double **p)
{
int rsize;
    rsize = MAX(size,1);
    if((*p = (double *)calloc(rsize,sizeof(double))) == (double *)NULL){
      printf("allocation of memory failed in fcalloc");
      exit(rsize);
   }
}
/*========================== ICALLOC =======================================*/
/*                                                                          */
/* Allocates memory for integers                                            */
/*                                                                          */
/*==========================================================================*/
void icalloc(int size,int **p)
{
int rsize;
    rsize = MAX(size,1);
    if((*p = (int *)calloc(rsize,sizeof(int))) == (int *)NULL){
      printf("allocation of memory failed in icalloc");
      exit(rsize);
   }
}

/*========================== MAKEMENUITEM ==================================*/
/*                                                                          */
/* Adds a menu item to a menu                                               */
/*                                                                          */
/*==========================================================================*/
Widget makeMenuItem(char *item_name,caddr_t client_data, Widget menu, int type, int onOff)
{
    int ac;
    Arg al[10];
    Widget item;
 
    ac = 0;
    XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR(item_name,char_set)); ac++;
    if(!type)
    {
      item=XmCreatePushButton(menu,item_name,al,ac);
      XtManageChild(item);
      XtAddCallback(item,XmNactivateCallback,(XtCallbackProc)fileCB,client_data);
      XtSetSensitive(item,True);
    }
    if(type)
    {
      XtSetArg(al[ac],XmNindicatorOn,True); ac++;
      item=XmCreateToggleButton(menu,item_name,al,ac);
      XtManageChild(item);
      XtAddCallback(item,XmNvalueChangedCallback,(XtCallbackProc)fileCB,client_data);
/*      XtSetSensitive(item,onOff); */
      XmToggleButtonSetState(item,onOff,False);
    }
    return(item);
}
/*============================= FILEDIALOGCB ===========================*/
/*                                                                      */
/* When the file call back is invoced and we need to read a file        */
/*                                                                      */
/*======================================================================*/
void fileDialogCB(Widget w, XtPointer client_data, XmSelectionBoxCallbackStruct *call_data)
{
   int i;
   char *datafile;
   switch(*((int *)client_data))
   {
      case OK:
         XmStringGetLtoR(call_data->value,char_set,&datafile);
         switch(filetype)
         {
           case 0:
             getCoords(datafile);
             break;
           case 1:
             getNtran(datafile);
             getDesignData(datafile);
             for(i=0; i<numElements; i++)
             {
              whichelement = i;
              evalBSpline();
             }
             whichelement = 0;
             somethingFit = 1;
             break;
           case 2:
             getCps(datafile);
             break;
         }

         XtFree(datafile);
         XClearArea(XtDisplay(drawing_area),XtWindow(drawing_area),0,0,0,0,True); 
         maxXY();
         setXYScale();
/*
         cminX = minX; 
         cmaxX = maxX;
         cminY = minY;
         cmaxY = maxY;
*/
         drawPoints();
         if(fileRead && filetype==0)makeToolShell();
         if(fileRead && filetype==1)makeDesignToolShell();
         if(fileRead && filetype==2)makePressureToolShell();
         break;
      case CANCEL:
         printf("CANCEL selected\n");
         break;
   }
   XtUnmanageChild(w);
}

/*============================= BUTTONSTART ============================*/
/*                                                                      */
/* When we first depress the left mouse button while the zoom is on     */
/*                                                                      */
/*======================================================================*/
void buttonStart(Widget w, XtPointer client_data, XEvent *event)
{
   double saveElement;
   double slopeX,interceptX;
   double slopeY,interceptY;
   int w0,w1,h0,h1;
   unsigned int button;
   button = event->xbutton.button; 
   h0 = margin;
   w0 = margin;
   h1 = currentHeight - margin;
   w1 = currentWidth - margin;
   if(zoomOn && button==Button1)
   {
     old_x = start_x = event->xbutton.x;
     old_y = start_y = event->xbutton.y;
     XDrawLine(XtDisplay(w), XtWindow(w), gc_xor, start_x, start_y, old_x, old_y);
   }
   if(translateOn && button==Button1)
   {
     old_x = start_x = event->xbutton.x;
     old_y = start_y = event->xbutton.y;
   }
   if(pickOn && fileRead && button==Button1)
   {
     pickPoint(event->xbutton.x, event->xbutton.y);
   }
   if((pickMoveX || pickMoveY) && fileRead && button==Button1 && somethingFit && element[whichelement].fitted)  /*DogTEST */
   {
     pickControlPoint(event->xbutton.x, event->xbutton.y);
/* Now we know which point to start at, lets save it      */
/* Note that this is the screen coordinate (not physical) */
     slopeX = (double)(w0 - w1)/(cminX - cmaxX);
     interceptX = .5*((double)w0 + (double)w1 - slopeX*(cmaxX + cminX));
     slopeY = (double)(h0 - h1)/(cmaxY - cminY);
     interceptY = .5*((double)h0 + (double)h1 - slopeY*(cmaxY + cminY));
     old_x = start_x = (int)(slopeX*element[activeElement].xcontrol[activeCP]   + interceptX);
     old_y = start_y = (int)(slopeY*element[activeElement].ycontrol[activeCP]   + interceptY);
     start_x_long = (long)(slopeX*element[activeElement].xcontrol[activeCP]   + interceptX);
     start_y_long = (long)(slopeY*element[activeElement].ycontrol[activeCP]   + interceptY);

/*     old_x = start_x = event->xbutton.x; */
/*     old_y = start_y = event->xbutton.y; */
   }
/*
   if((pickMoveX || pickMoveY) && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     old_x = event->xbutton.x;
     old_y = event->xbutton.y;
   }
*/
   if(revertLimits && fileRead && button==Button1 && somethingFit && element[whichelement].fitted)
   {
     pickControlPoint(event->xbutton.x, event->xbutton.y);
     element[activeElement].xupper[activeCP] = -990.;
     element[activeElement].xlower[activeCP] =  990.;
     element[activeElement].yupper[activeCP] = -990.;
     element[activeElement].ylower[activeCP] =  990.;
     if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
     drawPoints();
   }
   if(restorePoint && fileRead && button==Button1 && somethingFit && element[whichelement].fitted)
   {
     pickControlPoint(event->xbutton.x, event->xbutton.y);
     element[activeElement].xcontrol[activeCP] = element[activeElement].xcontrolO[activeCP];
     element[activeElement].ycontrol[activeCP] = element[activeElement].ycontrolO[activeCP];
     saveElement = whichelement;
     whichelement = activeElement;
     if(filetype !=2)evalBSpline();
     whichelement = saveElement;
     if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
     drawPoints();
   }
   if((movePoint || movePointX || movePointY) && fileRead && button==Button1 && somethingFit && element[whichelement].fitted)
   {
     pickControlPoint(event->xbutton.x, event->xbutton.y);
/* Now we know which point to start at, lets save it      */
/* Note that this is the screen coordinate (not physical) */
     slopeX = (double)(w0 - w1)/(cminX - cmaxX);
     interceptX = .5*((double)w0 + (double)w1 - slopeX*(cmaxX + cminX));
     slopeY = (double)(h0 - h1)/(cmaxY - cminY);
     interceptY = .5*((double)h0 + (double)h1 - slopeY*(cmaxY + cminY));
     old_x = start_x = (int)(slopeX*element[activeElement].xcontrol[activeCP]   + interceptX);
     old_y = start_y = (int)(slopeY*element[activeElement].ycontrol[activeCP]   + interceptY);
     start_x_long = (long)(slopeX*element[activeElement].xcontrol[activeCP]   + interceptX);
     start_y_long = (long)(slopeY*element[activeElement].ycontrol[activeCP]   + interceptY);
   }
}
/*============================= BUTTONDRAG =============================*/
/*                                                                      */
/* While we are draging the mouse this draws the box                    */
/*                                                                      */
/*======================================================================*/
void buttonDrag(Widget w, XtPointer client_data, XEvent *event)
{
   unsigned int button;
   button = event->xbutton.button; 
   if(zoomOn && button==Button1)
   {
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, start_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, old_x, start_y, old_x, old_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, old_x, old_y, start_x, old_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, old_y, start_x, start_y); 
     old_x = event->xbutton.x;
     old_y = event->xbutton.y;
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, start_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, old_x, start_y, old_x, old_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, old_x, old_y, start_x, old_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, old_y, start_x, start_y); 
   }
   if(pickMoveX && fileRead && button==Button2 && somethingFit && element[whichelement].fitted) /* y is constant */
   {
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
     old_x = event->xbutton.x;
/*     old_y = event->xbutton.y; */
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
   }
   if(pickMoveY && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
/*     old_x = event->xbutton.x; */
     old_y = event->xbutton.y;
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
   }
   if(movePoint && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
     old_x = event->xbutton.x;
     old_y = event->xbutton.y;
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
   }
   if(movePointX && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
     old_x = event->xbutton.x;
/*     old_y = event->xbutton.y; */
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
   }
   if(movePointY && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
/*     old_x = event->xbutton.x; */
     old_y = event->xbutton.y;
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, old_y); 
   }
}
/*============================= BUTTONDONE =============================*/
/*                                                                      */
/* When we are finished drawing the zoom box                            */
/*                                                                      */
/*======================================================================*/
void buttonDone(Widget w, XtPointer client_data, XEvent *event)
{
   int h0,w0,h1,w1;
   int saveElement;
   double slopeX,interceptX;
   double slopeY,interceptY;
   double aspectRatio;
   double wCenter, wLeft, wRight;
   double hCenter, hLeft, hRight;
   double wNew, hNew;
   double xstart, ystart;
   double xcurrent, ycurrent;
   unsigned int button;
   button = event->xbutton.button; 
   if(zoomOn && button==Button1)
   {
     old_x = event->xbutton.x;
     old_y = event->xbutton.y;
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, start_y, old_x, start_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, old_x, start_y, old_x, old_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, old_x, old_y, start_x, old_y); 
     XDrawLine(XtDisplay(w),XtWindow(w), gc_xor, start_x, old_y, start_x, start_y); 

    h0 = margin;
    w0 = margin;
    h1 = currentHeight - margin;
    w1 = currentWidth - margin;
    wCenter = .5*((double)start_x + (double)old_x);
    hCenter = .5*((double)start_y + (double)old_y);
    aspectRatio = (double)(w1 - w0)/(double)(h1 - h0);
    wNew = (double)(old_x - start_x);
    hNew = (double)(old_y - start_y);
    if(wNew >= hNew)
    {
      hNew = wNew/aspectRatio;
    }
    else
    {
     wNew = hNew*aspectRatio;
    }
    start_x = (int)(wCenter - .5*wNew);
    old_x   = (int)(wCenter + .5*wNew);
    start_y = (int)(hCenter - .5*hNew);
    old_y   = (int)(hCenter + .5*hNew);


     slopeX = (cmaxX - cminX)/(double)(w1 - w0);
     interceptX = .5*(cmaxX + cminX - slopeX*(w0 + w1));
     cminX = slopeX*start_x + interceptX;
     cmaxX = slopeX*old_x + interceptX;

     slopeY = (cmaxY - cminY)/(double)(h0 - h1);
     interceptY = .5*(cmaxY + cminY - slopeY*(h0 + h1));
     cmaxY = slopeY*start_y + interceptY;
     cminY = slopeY*old_y + interceptY;
     if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
   }
   if(translateOn && button==Button1)
   {
     h0 = margin;
     w0 = margin;
     h1 = currentHeight - margin;
     w1 = currentWidth - margin;
     old_x = event->xbutton.x;
     old_y = event->xbutton.y;
     wNew = (double)(old_x - start_x); /* Change in position (screen coordinates )*/
     hNew = (double)(old_y - start_y); /* Change in position (screen coordinates )*/
/* Now we need to just convert the change in position from screen coordinates to x and y */
     slopeX = (cmaxX - cminX)/(double)(w1 - w0);
     cminX = cminX - slopeX*wNew;
     cmaxX = cmaxX - slopeX*wNew;

     slopeY = (cmaxY - cminY)/(double)(h0 - h1);
     cmaxY = cmaxY - slopeY*hNew;
     cminY = cminY - slopeY*hNew;
     if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
   }
   if((pickMoveX || pickMoveY) && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     h0 = margin;
     w0 = margin;
     h1 = currentHeight - margin;
     w1 = currentWidth - margin;
     slopeX = (cmaxX - cminX)/(double)(w1 - w0);
     interceptX = .5*(cmaxX + cminX - slopeX*(double)(w0 + w1));
     slopeY = (cmaxY - cminY)/(double)(h0 - h1);
     interceptY = .5*(cmaxY + cminY - slopeY*(double)(h0 + h1));

     if(pickMoveX)
     {
       old_x = event->xbutton.x;
       xstart   = slopeX*start_x + (double)interceptX;
       xcurrent = slopeX*old_x + (double)interceptX;
       if(xcurrent <= xstart) /* Set minimum and check to see if maximum has been reset */
       {
         element[activeElement].xlower[activeCP] = xcurrent;
         if(xstart >= element[activeElement].xupper[activeCP])
         {
           element[activeElement].xupper[activeCP] = element[activeElement].xcontrol[activeCP];
         }
       }
       if(xcurrent >= xstart)
       {
         element[activeElement].xupper[activeCP] = xcurrent;
         if(xstart <= element[activeElement].xlower[activeCP])
         {
           element[activeElement].xlower[activeCP] = element[activeElement].xcontrol[activeCP];
         }
       }
     }
     if(pickMoveY)
     {
       old_y = event->xbutton.y;
       ystart   = slopeY*start_y + (double)interceptY;
       ycurrent = slopeY*old_y + (double)interceptY;
       if(ycurrent <= ystart)
       {
         element[activeElement].ylower[activeCP] = ycurrent;
         if(ystart >= element[activeElement].yupper[activeCP])
         {
           element[activeElement].yupper[activeCP] = element[activeElement].ycontrol[activeCP];
         }
       }
       if(ycurrent >= ystart)
       {
         element[activeElement].yupper[activeCP] = ycurrent;
         if(ystart <= element[activeElement].ylower[activeCP])
         {
           element[activeElement].ylower[activeCP] = element[activeElement].ycontrol[activeCP];
         }
        }
     }
     XDrawLine(XtDisplay(w),XtWindow(w), gc_green, start_x, start_y, old_x, old_y); 
   }
/*
   if(revertLimits && fileRead && button==Button1 && somethingFit && element[whichelement].fitted)
   {
     element[activeElement].xupper[activeCP] = -990.;
     element[activeElement].xlower[activeCP] =  990.;
     element[activeElement].yupper[activeCP] = -990.;
     element[activeElement].ylower[activeCP] =  990.;
     if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
     drawPoints();
   }
*/
   if((movePoint || movePointX || movePointY) && fileRead && button==Button2 && somethingFit && element[whichelement].fitted)
   {
     h0 = margin;
     w0 = margin;
     h1 = currentHeight - margin;
     w1 = currentWidth - margin;
     slopeX = (cmaxX - cminX)/(double)(w1 - w0);
     interceptX = .5*(cmaxX + cminX - slopeX*(double)(w0 + w1));
     slopeY = (cmaxY - cminY)/(double)(h0 - h1);
     interceptY = .5*(cmaxY + cminY - slopeY*(double)(h0 + h1));

     if(movePoint)
     {
       old_x = event->xbutton.x;
       xstart   = slopeX*start_x + (double)interceptX;
       xcurrent = slopeX*old_x + (double)interceptX;
       element[activeElement].xcontrol[activeCP] = xcurrent;
       old_y = event->xbutton.y;
       ystart   = slopeY*start_y + (double)interceptY;
       ycurrent = slopeY*old_y + (double)interceptY;
       element[activeElement].ycontrol[activeCP] = ycurrent;
     }
     if(movePointX)
     {
       old_x = event->xbutton.x;
       xstart   = slopeX*start_x + (double)interceptX;
       xcurrent = slopeX*old_x + (double)interceptX;
       element[activeElement].xcontrol[activeCP] = xcurrent;
     }
     if(movePointY)
     {
       old_y = event->xbutton.y;
       ystart   = slopeY*start_y + (double)interceptY;
       ycurrent = slopeY*old_y + (double)interceptY;
       element[activeElement].ycontrol[activeCP] = ycurrent;
     }
/* Reset the limits */
     if(filetype != 2)
     {
      element[activeElement].xlower[activeCP] =  990.;
      element[activeElement].xupper[activeCP] = -990.;
      element[activeElement].ylower[activeCP] =  990.;
      element[activeElement].yupper[activeCP] = -990.;
     }
     XDrawLine(XtDisplay(w),XtWindow(w), gc_green, start_x, start_y, old_x, old_y); 
     saveElement = whichelement;
     whichelement = activeElement;
     if(filetype !=2)evalBSpline();
     whichelement = saveElement;
     if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
     drawPoints();
   }
}


/*========================== CREATEOPTIONMENU ==============================*/
/*                                                                          */
/* Creates the option menu for picking the element of interest for splining */
/*                                                                          */
/*==========================================================================*/
void createOptionMenu(Widget w)
{
    int i;
     char *number;
     char element[12];
    /* create the options menu for picking the number of elements */
    elementPicker=makeOptionMenu("Element Number",w);

    for(i = 0; i< numElements; i++)
    {
      sprintf(element,"%d\0",i+1);
      whichElement[i]=makeOptionItem(element,elementPicker); 
    }
 
}

/*========================== MAKEOPTIONMENU ================================*/
/*                                                                          */
/* Creates an option menu                                                   */
/*                                                                          */
/*==========================================================================*/
Widget makeOptionMenu(char *menu_name, Widget parent) 
{
    int ac;
    Arg al[10];
    Widget menu;
 
    menu=XmCreatePulldownMenu(parent,menu_name,NULL,0);
    ac=0;
    XtSetArg (al[ac],XmNsubMenuId, menu); ac++;
    XtSetArg(al[ac],XmNlabelString, XmStringCreateLtoR(menu_name,char_set)); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    elementOption = XmCreateOptionMenu(parent,menu_name,al,ac);
    XtManageChild(elementOption);
    return(menu);
}
/*========================== MAKEOPTIONITEM ================================*/
/*                                                                          */
/* Adds a menu item to the option menu                                      */
/*                                                                          */
/*==========================================================================*/
Widget makeOptionItem(char *item_name, Widget menu)
{
    int ac;
    Arg al[10];
    Widget item;
 
    ac = 0;
    XtSetArg(al[ac],XmNlabelString,
        XmStringCreateLtoR(item_name,char_set)); ac++;
    item=XmCreatePushButton(menu,item_name,al,ac);
    XtManageChild(item);
    XtAddCallback(item,XmNactivateCallback,(XtCallbackProc)elementOptionCB,NULL);
    XtSetSensitive(item,True);
    return(item);
}

/*========================== elementOptionCB ===============================*/
/*                                                                          */
/* Call back for the file menu                                              */
/*                                                                          */
/*==========================================================================*/
void elementOptionCB(Widget w, XtPointer client_data, XmAnyCallbackStruct *call_data)
{
    Arg al[10];
    int ac;
    int i;
    int number;
    int paramtype;
    XmString name;
    char *label;

/* Get the label from the widget to find out which widget it is */
    ac=0;
    XtSetArg(al[ac],XmNlabelString,&name); ac++;
    XtGetValues(w,al,ac);

    XmStringGetLtoR(name,char_set,&label);

    printf("%s\n",label);
    sscanf(label,"%d",&i);
    i -= 1;
    printf("In elementOptionCB i = %d\n",i);
    whichelement = i;  /* The current element of interest */
    XtFree(label);

/* Set the order of the spline */
    if(filetype==0)
    {
      number = element[whichelement].order;
      XmScaleSetValue(degreeScale,number);
      /* Set the maximum order of the spline for this element */
      number = element[whichelement].maxdegree;
      ac = 0;
      XtSetArg(al[ac],XmNmaximum,number); ac++;
      XtSetValues(degreeScale,al,ac);
    }

/* Set the pointscale widget for this element */
    number = element[whichelement].numEval;
    ac = 0;
    XtSetArg(al[ac],XmNvalue,number); ac++;
    XtSetValues(pointScale,al,ac);
    if(element[whichelement].fitted)
    {
      XtSetSensitive(reevaluatepushbutton,True);
    }
    else
    {
      XtSetSensitive(reevaluatepushbutton,False);
    }

/* If we are fitting a geometry */
/* Set the type of parameterization used for this element */
   if(filetype ==0)
   {
    paramtype = element[whichelement].parameterization;
    ac = 0;
    if(paramtype == 0)
    {
       XtSetArg(al[ac],XmNmenuHistory,paramPush[0]);ac++;
    }
    else if(paramtype == 1)
    {
       XtSetArg(al[ac],XmNmenuHistory,paramPush[1]);ac++;
    }
    else
    {
       XtSetArg(al[ac],XmNmenuHistory,paramPush[2]);ac++;
    }
    /*  XtSetValues(paramPulldown,al,ac); */
    XtSetValues(paramOption,al,ac);
   }
   

/* Delete the previous scale widgets and create the new ones */
    if(fileRead)deleteSegScale();
    if(fileRead)createSegScale(0,i);
}
/*========================== DEGREESCALECB =================================*/
/*                                                                          */
/* Call back for the degree scale widget                                    */
/*                                                                          */
/*==========================================================================*/
void degreeScaleCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data)
{
    int value;
    value = call_data->value;
    printf("The value of the degree scale is %d\n",value);
    element[whichelement].order = value;
}      
/*========================== POINTSCALECB ==================================*/
/*                                                                          */
/* Call back for the point scale widget that sets the number of points      */
/*                                                                          */
/*==========================================================================*/
void pointScaleCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data)
{
    int value;
    value = call_data->value;
    printf("The value of the point scale is %d\n",value);
    element[whichelement].numEval = value;
}      
/*========================== FITPUSHBUTTONCB ===============================*/
/*                                                                          */
/* Call back for the pushbutton widget initiates the B-spline fit           */
/*                                                                          */
/*==========================================================================*/
void fitPushButtonCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data)
{
    int i,j;
    segB();

/* Set the sliders for the segments */
      for(i = 0; i<element[whichelement].noSegments; i++)
      {
        j = element[whichelement].firstSegment + i;
        XmScaleSetValue(numControlPoints[i],segment[j].noControl);
      }

    evalBSpline();
    somethingFit = 1;
    if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
    maxXY();
/*    setXYScale(); */
    drawPoints();

/* Once we have fit a curve, make the symbol menu items sensitive */
   XtSetSensitive(splinecurveline,True);
   XtSetSensitive(splinecurvesymbol,True);
   XtSetSensitive(Bsplinesymbol,True);
   XtSetSensitive(reevaluatepushbutton,True);
}      
/*========================== REEVALUATECB ==================================*/
/*                                                                          */
/* Call back for the pushbutton widget. Just re-evaluates the B-spline      */
/*                                                                          */
/*==========================================================================*/
void reEvaluateCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data)
{
    if(somethingFit)
    {
/* Be sure and reallocate the memory to hold the points */
      free(element[whichelement].xfitted);
      free(element[whichelement].yfitted);
      fcalloc(element[whichelement].numEval, &(element[whichelement].xfitted));
      fcalloc(element[whichelement].numEval, &(element[whichelement].yfitted));

      evalBSpline();
      somethingFit = 1;
      if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
      maxXY();
      drawPoints();
    }
}      
/*========================== CREATESEGSCALE ================================*/
/*                                                                          */
/* For the current element of interest, create a scale widget               */
/* for each segment on the element                                          */
/*                                                                          */
/*==========================================================================*/
void createSegScale(int create, int Element)
{
    Arg al[20];
    int ac;
    int i, number;
    int maxNoOfSegments=0;
    int noOfSegments;
    int maxcontrol, currentvalue;
    Widget previous = separator;
    char text[31];

/* If createing the segments for the first time */
   if(create)
   {

/* Find the maximum number of segments on any element */
      for(i=0; i<numElements; i++)
      {
        noOfSegments = element2segment[i+1] - element2segment[i];
        if(noOfSegments >= maxNoOfSegments)maxNoOfSegments = noOfSegments;
      }

      for(i = 0; i<maxNoOfSegments; i++)
      {
        sprintf(text,"Control points for segment %d\0",i+1);
/*                             */
/* Now create the scale widget */
/*                             */
        maxcontrol = segment[i].maxcontrol;
        currentvalue = MIN(20,maxcontrol);
        segment[i].noControl = currentvalue;

        ac = 0;
        XtSetArg(al[ac],XmNtitleString,XmStringCreateLtoR(text,char_set)); ac++;
        XtSetArg(al[ac],XmNorientation,XmHORIZONTAL); ac++;
        XtSetArg(al[ac],XmNmaximum,maxcontrol); ac++;
        XtSetArg(al[ac],XmNminimum,2); ac++;
        XtSetArg(al[ac],XmNvalue,currentvalue); ac++;
        XtSetArg(al[ac],XmNshowValue,True); ac++;
        XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
        XtSetArg(al[ac],XmNtopWidget,previous); ac++; 
        XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
        XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
/*        XtSetArg(al[ac],XmNsensitive,False); ac++; */
        numControlPoints[i] = XmCreateScale(toolform2,"controlpoints",al,ac);
        XtManageChild(numControlPoints[i]); 
        XtAddCallback(numControlPoints[i],XmNvalueChangedCallback,(XtCallbackProc)controlPointsCB,NULL);
        previous = numControlPoints[i];
        XtSetSensitive(numControlPoints[i],False);
      }
/* Now make the scale widgets for the first element sensitive */
        noOfSegments = element2segment[1] - element2segment[0];
        for(i=0; i<noOfSegments; i++)
        {
          XtSetSensitive(numControlPoints[i],True);
        }
   }
   else
   {
     noOfSegments = element2segment[Element+1] - element2segment[Element];
     for(i = 0; i<noOfSegments; i++)
     {
       maxcontrol = segment[element[whichelement].firstSegment + i].maxcontrol;
       number = segment[element[whichelement].firstSegment + i].noControl;
       if(number >= maxcontrol)number = maxcontrol;
       ac = 0;
       XtSetArg(al[ac],XmNvalue,number); ac++;
       XtSetValues(numControlPoints[i],al,ac);
       XtSetSensitive(numControlPoints[i],True);
     }
   }
    XFlush(XtDisplay(toplevel));
}

/*========================== DELETESEGSCALE ================================*/
/*                                                                          */
/* This just unmanages all the scale widgets for the segments               */
/*                                                                          */
/*==========================================================================*/
void deleteSegScale()
{
    Arg al[10];
    int ac;
    int i=0;
    int maxNoOfSegments=0;
    int noOfSegments;

/* Find the maximum number of segments on any element */
      for(i=0; i<numElements; i++)
      {
        noOfSegments = element2segment[i+1] - element2segment[i];
        if(noOfSegments >= maxNoOfSegments)maxNoOfSegments = noOfSegments;
      }

/* Now just make them not sensitive */

    for(i=0; i<maxNoOfSegments; i++)
    {
       XtSetSensitive(numControlPoints[i],False);
    }
    XFlush(XtDisplay(toplevel));
}

/*========================== controlPointsCB ===============================*/
/*                                                                          */
/* Call back for the control points options                                 */
/*                                                                          */
/*==========================================================================*/
void controlPointsCB(Widget w, XtPointer client_data, XmScaleCallbackStruct *call_data)
{  
    Arg al[10];
    int ac;
    int value;
    int i;
    int maxcontrol;
    XmString name;
    char *label;

/* Get the label from the widget to find out which widget it is */
    ac=0;
    XtSetArg(al[ac],XmNtitleString,&name); ac++;
    XtGetValues(w,al,ac);

    XmStringGetLtoR(name,char_set,&label);
    printf("%s\n",label);
    sscanf(label,"Control points for segment %d",&i);
    i -= 1;
    printf("The segment is %d\n",i);
    XtFree(label);


    value = call_data->value;
    printf("The value of the control point scale is %d\n",value);
    segment[element[whichelement].firstSegment + i].noControl = value;
}      
/*========================== MAKETOOLSHELL==================================*/
/*                                                                          */
/* Consolodated all the calls needed for making the shell for the tools     */
/*                                                                          */
/*==========================================================================*/
void makeToolShell(void)
{
    Arg al[20];
    int ac;
    int i;

    /* Create a shell widget to hold the tool bar */
    ac = 0;

    toolshell = XtAppCreateShell("Shell","Shell",applicationShellWidgetClass,XtDisplay(toplevel),al,ac); 
/*    toolshell = XtAppCreateShell("MyDraw","MyDraw",topLevelShellWidgetClass,XtDisplay(toplevel),al,ac); */ 

    /* Create the form widget to hold the tool bar */
    ac = 0;
    toolform = XmCreateForm(toolshell,"toolform",al,ac);
    XtManageChild(toolform);

/* Create a frame widget to hold the tools */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    toolFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(toolFrame); 

    /* Create the tool bar rowcolumn */
    ac = 0;
/*    XtSetArg(al[ac],XmNpacking,XmPACK_COLUMN); ac++; */
    XtSetArg(al[ac],XmNpacking,XmPACK_TIGHT); ac++;
    XtSetArg(al[ac],XmNorientation,XmVERTICAL); ac++; 
    XtSetArg(al[ac],XmNadjustLast,False); ac++;
    XtSetArg(al[ac],XmNnumColumns,1); ac++;
/*    XtSetArg(al[ac],XmNresizeHeight,True); ac++;              */
/*    XtSetArg(al[ac],XmNresizeWidth,True); ac++;               */
/*    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++;    */
/*    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++;   */
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNentryAlignment,XmALIGNMENT_CENTER); ac++;
    toolbar = XmCreateRowColumn(toolFrame,"toolbar",al,ac);
    XtManageChild(toolbar); 

    /* Create the push buttons */
    for(i=0; i<5; i++)
    {
      ac = 0;
      if(i==ZOOMBUTTON)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Zoom",char_set)); ac++;
      }
      else if(i==SHOWREALSIZE)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Expanded size",char_set)); ac++;
      }
      else if(i==RESET)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Reset",char_set)); ac++;
        XtSetArg(al[ac],XmNindicatorOn,False); ac++;
      }
      else if(i==TRANSLATE)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Translate",char_set)); ac++;
      }
      else if(i==PICKPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Identify",char_set)); ac++;
      }
      else
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Button dog",char_set)); ac++;
      }
/*      toolbuttons[i] = XmCreatePushButton(toolbar,"button",al,ac); */
/*      XtAddCallback(toolbuttons[i],XmNactivateCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]); */
      if(i==RESET || i==SHOWREALSIZE)
      {
        toolbuttons[i] = XmCreatePushButton(toolbar,"toggle",al,ac);
        XtAddCallback(toolbuttons[i],XmNactivateCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      }
      else
      {
        toolbuttons[i] = XmCreateToggleButton(toolbar,"toggle",al,ac);
        XtAddCallback(toolbuttons[i],XmNvalueChangedCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      }
      XtManageChild(toolbuttons[i]); 
    }

/* Now create the frame widget and the necessary   */
/* toggles so we can manipulate the control points */
/* Create a frame widget to hold the tools */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftWidget,toolFrame); ac++;
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    controlFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(controlFrame); 

    /* Create the tool bar rowcolumn */
    ac = 0;
/*    XtSetArg(al[ac],XmNpacking,XmPACK_COLUMN); ac++; */
    XtSetArg(al[ac],XmNpacking,XmPACK_TIGHT); ac++;
    XtSetArg(al[ac],XmNorientation,XmVERTICAL); ac++; 
    XtSetArg(al[ac],XmNadjustLast,False); ac++;
    XtSetArg(al[ac],XmNnumColumns,1); ac++;
/*    XtSetArg(al[ac],XmNresizeHeight,True); ac++;              */
/*    XtSetArg(al[ac],XmNresizeWidth,True); ac++;               */
/*    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++;    */
/*    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++;   */
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNentryAlignment,XmALIGNMENT_CENTER); ac++;
    toolbar2 = XmCreateRowColumn(controlFrame,"toolbar2",al,ac);
    XtManageChild(toolbar2); 

    /* Create the push buttons */
    for(i=5; i<12; i++)
    {
      ac = 0;
      if(i==PICKMOVEX)
      {
/*        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/move x",char_set)); ac++; */
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/limit x",char_set)); ac++;
      }
      else if(i==PICKMOVEY)
      {
/*        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/move y",char_set)); ac++; */
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/limit y",char_set)); ac++;
      }
      else if(i==REVERT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/freeze",char_set)); ac++;
      }
      else if(i==MOVEPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point",char_set)); ac++;
      }
      else if(i==MOVEPOINTX)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point/x",char_set)); ac++;
      }
      else if(i==MOVEPOINTY)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point/y",char_set)); ac++;
      }
      else if(i==RESTOREPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Restore Point",char_set)); ac++;
      }
      toolbuttons2[i] = XmCreateToggleButton(toolbar2,"toggle",al,ac);
      XtAddCallback(toolbuttons2[i],XmNvalueChangedCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      XtManageChild(toolbuttons2[i]); 
    }

/* Create another frame widget to hold the tools used for fitting the B-splines */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNtopWidget,controlFrame); ac++; 
/*    XtSetArg(al[ac],XmNtopWidget,toolFrame); ac++;  */
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    splineFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(splineFrame); 

/* Put a form widget inside this frame */
    ac = 0;
    toolform2 = XmCreateForm(splineFrame,"toolform2",al,ac);
    XtManageChild(toolform2);

/* Now create an option menu to go inside this form */
    createOptionMenu(toolform2);

/* Create the option menu for choosing the parameterization */
    createParamOption(toolform2);
/*                                                        */
/* Make the pushbutton that gives the ok to fit the curve */
/*                                                        */
    ac = 0;
    XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Fit Curve",char_set)); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftWidget,elementOption); ac++; 
    fitpushbutton = XmCreatePushButton(toolform2,"fitpushbutton",al,ac);
    XtManageChild(fitpushbutton);
    XtAddCallback(fitpushbutton,XmNactivateCallback,(XtCallbackProc)fitPushButtonCB,NULL);
/*                                                        */
/* Make the pushbutton that is used to reevaluate the curve */
/*                                                        */
    ac = 0;
    XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Re-evaluate",char_set)); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNtopWidget,fitpushbutton); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftWidget,elementOption); ac++; 
    reevaluatepushbutton = XmCreatePushButton(toolform2,"reevaluatepushbutton",al,ac);
    XtManageChild(reevaluatepushbutton);
    XtAddCallback(reevaluatepushbutton,XmNactivateCallback,(XtCallbackProc)reEvaluateCB,NULL);
    XtSetSensitive(reevaluatepushbutton,False);

/* Now create the scale widget that we use to set the */
/* degree of the B-spline                             */

    ac = 0;
    XtSetArg(al[ac],XmNtitleString,XmStringCreate("Degree of B-spline",char_set)); ac++;
    XtSetArg(al[ac],XmNorientation,XmHORIZONTAL); ac++;
    XtSetArg(al[ac],XmNmaximum,element[0].maxdegree); ac++;
    XtSetArg(al[ac],XmNminimum,1); ac++;
    XtSetArg(al[ac],XmNvalue,element[0].order); ac++;
    XtSetArg(al[ac],XmNshowValue,True); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
/*    XtSetArg(al[ac],XmNtopWidget,reevaluatepushbutton); ac++; */
    XtSetArg(al[ac],XmNtopWidget,paramOption); ac++; 
    degreeScale = XmCreateScale(toolform2,"degreeScale",al,ac);
    XtManageChild(degreeScale);
    XtAddCallback(degreeScale,XmNvalueChangedCallback,(XtCallbackProc)degreeScaleCB,NULL);
/*                                      */ 
/* Scale widget for specifying how many */
/* points you want on each element      */
/*                                      */ 
    ac = 0;
    XtSetArg(al[ac],XmNtitleString,XmStringCreate("Number of points on element",char_set)); ac++;
    XtSetArg(al[ac],XmNorientation,XmHORIZONTAL); ac++;
    XtSetArg(al[ac],XmNmaximum,maxPointsOnElement); ac++;
    XtSetArg(al[ac],XmNminimum,20); ac++;
    XtSetArg(al[ac],XmNvalue,baselineNoEval); ac++;
    XtSetArg(al[ac],XmNshowValue,True); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopWidget,degreeScale); ac++; 
    pointScale = XmCreateScale(toolform2,"pointscale",al,ac);
    XtManageChild(pointScale);
    XtAddCallback(pointScale,XmNvalueChangedCallback,(XtCallbackProc)pointScaleCB,NULL);

/* Put a separator */
    ac = 0;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopWidget,pointScale); ac++; 
    XtSetArg(al[ac],XmNseparatorType,XmDOUBLE_LINE); ac++; 
    separator = XmCreateSeparator(toolform2,"separator",al,ac);
    XtManageChild(separator);

/* Now create a bunch of scale widgets for the segments on the elements */
    if(fileRead)createSegScale(1,0);

/* Find out how many kids we have */
    ac=0;
    XtSetArg(al[ac],XmNnumChildren,&numKids); ac++;
    XtGetValues(toolFrame,al,ac);
    printf("number of kids for toolFrame = %d\n",numKids);
    ac=0;
    XtSetArg(al[ac],XmNnumChildren,&numKids); ac++;
    XtGetValues(toolbar,al,ac);
    printf("number of kids for toolbar = %d\n",numKids);
    ac=0;
    XtSetArg(al[ac],XmNnumChildren,&numKids); ac++;
    XtGetValues(toolform,al,ac);
    printf("number of kids for toolform = %d\n",numKids);

    XtRealizeWidget(toolshell);

}

/*========================== MAKEDESIGNTOOLSHELL ===========================*/
/*                                                                          */
/* Tool shell for when we read in design file                               */
/*                                                                          */
/*==========================================================================*/
void makeDesignToolShell(void)
{
    Arg al[20];
    int ac;
    int i;

    /* Create a shell widget to hold the tool bar */
    ac = 0;

    toolshell = XtAppCreateShell("Shell","Shell",applicationShellWidgetClass,XtDisplay(toplevel),al,ac); 
/*    toolshell = XtAppCreateShell("MyDraw","MyDraw",topLevelShellWidgetClass,XtDisplay(toplevel),al,ac); */ 

    /* Create the form widget to hold the tool bar */
    ac = 0;
    toolform = XmCreateForm(toolshell,"toolform",al,ac);
    XtManageChild(toolform);

/* Create a frame widget to hold the tools */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    toolFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(toolFrame); 

    /* Create the tool bar rowcolumn */
    ac = 0;
/*    XtSetArg(al[ac],XmNpacking,XmPACK_COLUMN); ac++; */
    XtSetArg(al[ac],XmNpacking,XmPACK_TIGHT); ac++;
    XtSetArg(al[ac],XmNorientation,XmVERTICAL); ac++; 
    XtSetArg(al[ac],XmNadjustLast,False); ac++;
    XtSetArg(al[ac],XmNnumColumns,1); ac++;
/*    XtSetArg(al[ac],XmNresizeHeight,True); ac++;              */
/*    XtSetArg(al[ac],XmNresizeWidth,True); ac++;               */
/*    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++;    */
/*    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++;   */
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNentryAlignment,XmALIGNMENT_CENTER); ac++;
    toolbar = XmCreateRowColumn(toolFrame,"toolbar",al,ac);
    XtManageChild(toolbar); 

    /* Create the push buttons */
    for(i=0; i<5; i++)
    {
      ac = 0;
      if(i==ZOOMBUTTON)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Zoom",char_set)); ac++;
      }
      else if(i==SHOWREALSIZE)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Expanded size",char_set)); ac++;
      }
      else if(i==RESET)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Reset",char_set)); ac++;
        XtSetArg(al[ac],XmNindicatorOn,False); ac++;
      }
      else if(i==TRANSLATE)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Translate",char_set)); ac++;
      }
      else if(i==PICKPOINT)
      { 
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Identify",char_set)); ac++; 
      } 
      else
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Button dog",char_set)); ac++;
      }
/*      toolbuttons[i] = XmCreatePushButton(toolbar,"button",al,ac); */
/*      XtAddCallback(toolbuttons[i],XmNactivateCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]); */
      if(i==RESET || i==SHOWREALSIZE)
      {
        toolbuttons[i] = XmCreatePushButton(toolbar,"toggle",al,ac);
        XtAddCallback(toolbuttons[i],XmNactivateCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      }
      else
      {
        toolbuttons[i] = XmCreateToggleButton(toolbar,"toggle",al,ac);
        XtAddCallback(toolbuttons[i],XmNvalueChangedCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      }
      XtManageChild(toolbuttons[i]); 
    }

/* Unmanage the PICKPOINT (Identify) widget since it isnt relavent for this */
      XtUnmanageChild(toolbuttons[4]);

/* Now create the frame widget and the necessary   */
/* toggles so we can manipulate the control points */
/* Create a frame widget to hold the tools */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftWidget,toolFrame); ac++;
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    controlFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(controlFrame); 

    /* Create the tool bar rowcolumn */
    ac = 0;
/*    XtSetArg(al[ac],XmNpacking,XmPACK_COLUMN); ac++; */
    XtSetArg(al[ac],XmNpacking,XmPACK_TIGHT); ac++;
    XtSetArg(al[ac],XmNorientation,XmVERTICAL); ac++; 
    XtSetArg(al[ac],XmNadjustLast,False); ac++;
    XtSetArg(al[ac],XmNnumColumns,1); ac++;
/*    XtSetArg(al[ac],XmNresizeHeight,True); ac++;              */
/*    XtSetArg(al[ac],XmNresizeWidth,True); ac++;               */
/*    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++;    */
/*    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++;   */
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNentryAlignment,XmALIGNMENT_CENTER); ac++;
    toolbar2 = XmCreateRowColumn(controlFrame,"toolbar2",al,ac);
    XtManageChild(toolbar2); 

    /* Create the push buttons */
    for(i=5; i<12; i++)
    {
      ac = 0;
      if(i==PICKMOVEX)
      {
/*        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/move x",char_set)); ac++; */
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/limit x",char_set)); ac++;
      }
      else if(i==PICKMOVEY)
      {
/*        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/move y",char_set)); ac++; */
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/limit y",char_set)); ac++;
      }
      else if(i==REVERT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/freeze",char_set)); ac++;
      }
      else if(i==MOVEPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point",char_set)); ac++;
      }
      else if(i==MOVEPOINTX)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point/x",char_set)); ac++;
      }
      else if(i==MOVEPOINTY)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point/y",char_set)); ac++;
      }
      else if(i==RESTOREPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Restore Point",char_set)); ac++;
      }
      toolbuttons2[i] = XmCreateToggleButton(toolbar2,"toggle",al,ac);
      XtAddCallback(toolbuttons2[i],XmNvalueChangedCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      XtManageChild(toolbuttons2[i]); 
    }

/* Create another frame widget to hold the tools used for fitting the B-splines */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNtopWidget,controlFrame); ac++; 
/*    XtSetArg(al[ac],XmNtopWidget,toolFrame); ac++;  */
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    splineFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(splineFrame); 

/* Put a form widget inside this frame */
    ac = 0;
    toolform2 = XmCreateForm(splineFrame,"toolform2",al,ac);
    XtManageChild(toolform2);

/* Now create an option menu to go inside this form */
    createOptionMenu(toolform2);
/*                                                          */
/* Make the pushbutton that is used to reevaluate the curve */
/*                                                          */
    ac = 0;
    XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Re-evaluate",char_set)); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftWidget,elementOption); ac++; 
    reevaluatepushbutton = XmCreatePushButton(toolform2,"reevaluatepushbutton",al,ac);
    XtManageChild(reevaluatepushbutton);
    XtAddCallback(reevaluatepushbutton,XmNactivateCallback,(XtCallbackProc)reEvaluateCB,NULL);
    XtSetSensitive(reevaluatepushbutton,True);

/*                                      */ 
/* Scale widget for specifying how many */
/* points you want on each element      */
/*                                      */ 
    ac = 0;
    XtSetArg(al[ac],XmNtitleString,XmStringCreate("Number of points on element",char_set)); ac++;
    XtSetArg(al[ac],XmNorientation,XmHORIZONTAL); ac++;
    XtSetArg(al[ac],XmNmaximum,maxPointsOnElement); ac++;
    XtSetArg(al[ac],XmNminimum,20); ac++;
/*    XtSetArg(al[ac],XmNvalue,baselineNoEval); ac++; */
    XtSetArg(al[ac],XmNvalue,element[0].initialNum); ac++;
    XtSetArg(al[ac],XmNshowValue,True); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopWidget,elementOption); ac++; 
    pointScale = XmCreateScale(toolform2,"pointscale",al,ac);
    XtManageChild(pointScale);
    XtAddCallback(pointScale,XmNvalueChangedCallback,(XtCallbackProc)pointScaleCB,NULL);

    XtRealizeWidget(toolshell);

}
/*========================== MAKEPRESSURETOOLSHELL =========================*/
/*                                                                          */
/* Tool shell for when we read in a pressure file                           */
/*                                                                          */
/*==========================================================================*/
void makePressureToolShell(void)
{
    Arg al[20];
    int ac;
    int i;

    /* Create a shell widget to hold the tool bar */
    ac = 0;

    toolshell = XtAppCreateShell("Shell","Shell",applicationShellWidgetClass,XtDisplay(toplevel),al,ac); 
/*    toolshell = XtAppCreateShell("MyDraw","MyDraw",topLevelShellWidgetClass,XtDisplay(toplevel),al,ac); */ 

    /* Create the form widget to hold the tool bar */
    ac = 0;
    toolform = XmCreateForm(toolshell,"toolform",al,ac);
    XtManageChild(toolform);

/* Create a frame widget to hold the tools */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
/*    XtSetArg(al[ac],XmNbottomAttachment,XmATTACH_FORM); ac++; */
/*    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++;  */
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    toolFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(toolFrame); 

    /* Create the tool bar rowcolumn */
    ac = 0;
/*    XtSetArg(al[ac],XmNpacking,XmPACK_COLUMN); ac++; */
    XtSetArg(al[ac],XmNpacking,XmPACK_TIGHT); ac++;
    XtSetArg(al[ac],XmNorientation,XmVERTICAL); ac++; 
    XtSetArg(al[ac],XmNadjustLast,False); ac++;
    XtSetArg(al[ac],XmNnumColumns,1); ac++;
    XtSetArg(al[ac],XmNentryAlignment,XmALIGNMENT_CENTER); ac++;
    toolbar = XmCreateRowColumn(toolFrame,"toolbar",al,ac);
    XtManageChild(toolbar); 

    /* Create the push buttons */
    for(i=0; i<5; i++)
    {
      ac = 0;
      if(i==ZOOMBUTTON)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Zoom",char_set)); ac++;
      }
      else if(i==SHOWREALSIZE)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Expanded size",char_set)); ac++;
      }
      else if(i==RESET)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Reset",char_set)); ac++;
        XtSetArg(al[ac],XmNindicatorOn,False); ac++;
      }
      else if(i==TRANSLATE)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Translate",char_set)); ac++;
      }
      else if(i==PICKPOINT)
      { 
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Query Cp",char_set)); ac++; 
      } 
      else
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Button dog",char_set)); ac++;
      }
/*      toolbuttons[i] = XmCreatePushButton(toolbar,"button",al,ac); */
/*      XtAddCallback(toolbuttons[i],XmNactivateCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]); */
      if(i==RESET || i==SHOWREALSIZE)
      {
        toolbuttons[i] = XmCreatePushButton(toolbar,"toggle",al,ac);
        XtAddCallback(toolbuttons[i],XmNactivateCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      }
      else
      {
        toolbuttons[i] = XmCreateToggleButton(toolbar,"toggle",al,ac);
        XtAddCallback(toolbuttons[i],XmNvalueChangedCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      }
      XtManageChild(toolbuttons[i]); 
    }

/* Unmanage the PICKPOINT (Identify) widget since it isnt relavent for this */
/* Also, unmanage the "Expanded view" */
/*      XtUnmanageChild(toolbuttons[4]); */
      XtUnmanageChild(toolbuttons[1]);

/* Now create the frame widget and the necessary   */
/* toggles so we can manipulate the control points */
/* Create a frame widget to hold the tools */
    ac = 0;
    XtSetArg(al[ac],XmNmarginWidth,  5); ac++;
    XtSetArg(al[ac],XmNmarginHeight, 5); ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNleftAttachment,XmATTACH_WIDGET); ac++; 
    XtSetArg(al[ac],XmNleftWidget,toolFrame); ac++;
    XtSetArg(al[ac],XmNtopOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightOffset,10); ac++; 
    XtSetArg(al[ac],XmNbottomOffset,10); ac++; 
    XtSetArg(al[ac],XmNleftOffset,10); ac++; 
    XtSetArg(al[ac],XmNrightAttachment,XmATTACH_FORM); ac++; 
    XtSetArg(al[ac],XmNshadowType,XmSHADOW_ETCHED_IN); ac++;
    controlFrame = XmCreateFrame(toolform,"toolFrame",al,ac); 
    XtManageChild(controlFrame); 

    /* Create the tool bar rowcolumn */
    ac = 0;
    XtSetArg(al[ac],XmNpacking,XmPACK_TIGHT); ac++;
    XtSetArg(al[ac],XmNorientation,XmVERTICAL); ac++; 
    XtSetArg(al[ac],XmNadjustLast,False); ac++;
    XtSetArg(al[ac],XmNnumColumns,1); ac++;
    XtSetArg(al[ac],XmNentryAlignment,XmALIGNMENT_CENTER); ac++;
    toolbar2 = XmCreateRowColumn(controlFrame,"toolbar2",al,ac);
    XtManageChild(toolbar2); 

    /* Create the push buttons */
    for(i=5; i<12; i++)
    {
      ac = 0;
      if(i==PICKMOVEX)
      {
/*        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/move x",char_set)); ac++; */
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/limit x",char_set)); ac++;
      }
      else if(i==PICKMOVEY)
      {
/*        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/move y",char_set)); ac++; */
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/limit y",char_set)); ac++;
      }
      else if(i==REVERT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Pick/freeze",char_set)); ac++;
      }
      else if(i==MOVEPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point",char_set)); ac++;
      }
      else if(i==MOVEPOINTX)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Move Point/x",char_set)); ac++;
      }
      else if(i==MOVEPOINTY)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Modify Cp",char_set)); ac++;
      }
      else if(i==RESTOREPOINT)
      {
        XtSetArg(al[ac],XmNlabelString,XmStringCreate("Restore Cp",char_set)); ac++;
      }
      toolbuttons2[i] = XmCreateToggleButton(toolbar2,"toggle",al,ac);
      XtAddCallback(toolbuttons2[i],XmNvalueChangedCallback,(XtCallbackProc)toolButtonCB,(XtPointer)&buttonNumbers[i]);
      XtManageChild(toolbuttons2[i]); 
    }

/* Unmannage all of these but pick/y and restore */
      XtUnmanageChild(toolbuttons2[5]);
      XtUnmanageChild(toolbuttons2[6]);
      XtUnmanageChild(toolbuttons2[7]);
      XtUnmanageChild(toolbuttons2[8]);
      XtUnmanageChild(toolbuttons2[9]);

    XtRealizeWidget(toolshell);

}

/*========================== SETXYSCALE ====================================*/
/*                                                                          */
/* Sets the values of cminX etc.. so the geometry looks right               */
/*                                                                          */
/*==========================================================================*/
void setXYScale()
{
    double dx,dy,avex,avey;
/* Find the min,max, and average coordinates */
    dx = maxX - minX;
    dy = maxY - minY;
    avex = .5*(maxX + minX);
    avey = .5*(maxY + minY);
/* If dx is bigger, scale y */
    if(dx >= dy)
      {
        cminX = minX;
        cmaxX = maxX;
        cminY = avey - .5*dx;
        cmaxY = avey + .5*dx;
      }
      else if(dy >= dx)
      {
        cminY = minY;
        cmaxY = maxY;
        cminX = avex - .5*dy;
        cmaxX = avex + .5*dy;
      }

      if(filetype==2)
      {
        cminX = minX;
        cmaxX = maxX;
        cminY = minY;
        cmaxY = maxY;
      }
}
/*========================== MAKEFILEOPTIONMENU ============================*/
/*                                                                          */
/* Creates an option menu that lets us choose what type of file we want     */
/*                                                                          */
/*==========================================================================*/
Widget makeFileOptionMenu(char *menu_name, Widget parent) 
{
    int ac;
    Arg al[10];
    Widget menu;
 
    menu=XmCreatePulldownMenu(parent,menu_name,NULL,0);
    ac=0;
    XtSetArg (al[ac],XmNsubMenuId, menu); ac++;
    XtSetArg(al[ac],XmNlabelString, XmStringCreateLtoR(menu_name,char_set)); ac++;
    fileTypeOption = XmCreateOptionMenu(parent,menu_name,al,ac);
    XtManageChild(fileTypeOption);
    return(menu);
}

/*========================== MAKEFILEOPTIONITEM ============================*/
/*                                                                          */
/* Adds a menu item to the option menu                                      */
/*                                                                          */
/*==========================================================================*/
Widget makeFileOptionItem(char *item_name, Widget menu)
{
    int ac;
    Arg al[10];
    Widget item;
 
    ac = 0;
    XtSetArg(al[ac],XmNlabelString,
        XmStringCreateLtoR(item_name,char_set)); ac++;
    item=XmCreatePushButton(menu,item_name,al,ac);
    XtManageChild(item);
    XtAddCallback(item,XmNactivateCallback,(XtCallbackProc)fileOptionCB,NULL);
    XtSetSensitive(item,True);
    return(item);
}
/*========================== fileOptionCB ==================================*/
/*                                                                          */
/* Call back for the file menu                                              */
/*                                                                          */
/*==========================================================================*/
void fileOptionCB(Widget w, XtPointer client_data, XmAnyCallbackStruct *call_data)
{
    Arg al[10];
    int ac;
    int i;
    int number;
    XmString name;
    char *label;

/* Get the label from the widget to find out which widget it is */
    ac=0;
    XtSetArg(al[ac],XmNlabelString,&name); ac++;
    XtGetValues(w,al,ac);

    XmStringGetLtoR(name,char_set,&label);

    if(strcmp(label,"Geometry")==0)
    {
     filetype = 0;
    }
    if(strcmp(label,"Design Data")==0)
    {
     filetype = 1;
    }
    if(strcmp(label,"Pressure Data")==0)
    {
     filetype = 2;
    }

    printf("In fileOptionCB filetype = %d\n",filetype);
    XtFree(label);
}
/*========================== GETCPS ========================================*/
/*                                                                          */
/* Read the pressures from the input file                                   */
/*                                                                          */
/*==========================================================================*/
void getCps(char *datafile)
{
    Arg al[20];
    int ac;
    int i,j;
    int Answer=0;
    int npoints[maxElements];
    char dumstring[501];
    double xcoord, ycoord, cpvalue, ones;
    Widget promptdialog;
    Widget scale;
    FILE *input;
    FILE *output;


/* Now, open up a dialog widget to find out */
/* how many elements there are              */

   ac = 0;
   XtSetArg(al[ac],XmNselectionLabelString,XmStringCreateLtoR("Input the number of elements",char_set)); ac++;
   promptdialog = XmCreatePromptDialog(toplevel, "Element Dialog",al,ac);
   XtAddCallback(promptdialog,XmNokCallback,(XtCallbackProc)promptCB,(XtPointer)&Answer);
   XtAddCallback(promptdialog,XmNcancelCallback,(XtCallbackProc)promptCB,(XtPointer)&Answer);
   XtUnmanageChild(XmSelectionBoxGetChild(promptdialog,XmDIALOG_HELP_BUTTON));
   XtManageChild(promptdialog);

   while(Answer==0)XtAppProcessEvent(context, XtIMAll);
   if(Answer==1)readCps(datafile);
}
/*========================== READCPS =======================================*/
/*                                                                          */
/* Read the pressures from the input file                                   */
/*                                                                          */
/*==========================================================================*/
void readCps(char *datafile)
{
    int i,j;
    int npoints[maxElements];
    char dumstring[501];
    double xcoord, ycoord, cpvalue, ones;
    FILE *input;
    FILE *output;

   if( (input = fopen(datafile,"r")) == (FILE *)NULL)
   {
     printf("can't open data file");
     exit(2);
   }
   if( (output = fopen("dog2.out","w")) == (FILE *)NULL)
   {
     printf("can't open dog2.out file");
     exit(2);
   }

/* Read 6 blank lines */
     fgets(dumstring,500,input);
     fgets(dumstring,500,input);
     fgets(dumstring,500,input);
     fgets(dumstring,500,input);
     fgets(dumstring,500,input);
     fgets(dumstring,500,input);

/* For each element, read in the data                  */
/* Allowcate memory for all this stuff                 */
/* We will use xfitted and yfitted for the coordinates */
/* of the airfoil and ycontrol for the Cp values       */
/* Use xcontrol for the ones                           */

     for(i=0; i<numElements; i++)
     {
       fscanf(input,"%d",&(element[i].noControl));
       element[i].numEval = element[i].noControl; /* Set this so we can draw the lines between the points */
       element[i].fitted = 1;
       fcalloc(element[i].noControl,&(element[i].xfitted));   /* x-coordinate     */
       fcalloc(element[i].noControl,&(element[i].yfitted));   /* y-coordinate     */
       fcalloc(element[i].noControl,&(element[i].xcontrol));  /* x value          */
       fcalloc(element[i].noControl,&(element[i].ycontrol));  /* Cp value         */
       fcalloc(element[i].noControl,&(element[i].xcontrolO)); /* x  value (saved) */
       fcalloc(element[i].noControl,&(element[i].ycontrolO)); /* Cp value (saved) */
       fcalloc(element[i].noControl,&(element[i].xupper));    /* Ones             */

/* Read the data */
fprintf(output,"%d\n",element[i].noControl);
       for(j=0; j<element[i].noControl; j++)
       {
         fscanf(input,"%lf %lf %lf %lf\n",&xcoord, &cpvalue, &ycoord, &ones);
fprintf(output,"%lf %lf %lf %lf\n",xcoord, cpvalue, ycoord, ones);
         element[i].xfitted[j] = xcoord;
         element[i].yfitted[j] = ycoord;
         element[i].xcontrol[j] = xcoord;
         element[i].ycontrol[j] = -cpvalue;
         element[i].xcontrolO[j] = xcoord;
         element[i].ycontrolO[j] = -cpvalue;
         element[i].xupper[j] = ones;
        }
      }

fileRead = True;
somethingFit = 1;
XtSetSensitive(inputcurveline,False);
XtSetSensitive(inputcurvesymbol,False);
XtSetSensitive(splinecurveline,False);
XtSetSensitive(splinecurvesymbol,False);
XtSetSensitive(Bsplinelimits,False);
showSymbols = False;
showLines = False;
showFitSymbols = False;
showFitLines = False;
showBsymbols = True;
showLimits = False;

/*    printf("At End of readCps: exiting\n"); */
/*    exit(3); */
}
/*========================== PROMPTCB ======================================*/
/*                                                                          */
/* Call back for prompt dialog to get the number of elements                */
/*                                                                          */
/*==========================================================================*/
void promptCB(Widget w, XtPointer client_data, XmSelectionBoxCallbackStruct *call_data)
{
    int reason;
    char *s;
    reason = call_data->reason;
    switch(reason)
    {
      case XmCR_OK:
      /* Get the string from the event structure */
      XmStringGetLtoR(call_data->value,char_set,&s);
      printf("number of elements=%s\n",s);
      sscanf(s,"%d",&numElements);
      printf("The number of elements is %d\n",numElements);
      XtFree(s);
      *(int *)client_data = 1;
      break;
      case XmCR_CANCEL:
        printf("CANCEL in promptCB\n");
        *(int *)client_data = 2;
        break;
    }
    XtUnmanageChild(w);
/*    printf("Exiting in promptCB\n"); */
/*    exit(3);                         */
}



/*XXXXXX*/
/*========================== CREATEPARAMOPTION =============================*/
/*                                                                          */
/* Creates the option menu for picking the type of parameterization         */
/*                                                                          */
/* Inputs:                                                                  */
/* Widget w - parent widget for the paramPulldown and paramOption widgets   */
/*                                                                          */
/*==========================================================================*/
void createParamOption(Widget w)
{
    Arg al[20];
    int ac;
    int i;

    /* Create the option menu pane */
    paramPulldown = XmCreatePulldownMenu(w,"Param",NULL,0);

    /* Now make the actual option menu */
    ac = 0;
    XtSetArg(al[ac],XmNsubMenuId,paramPulldown);ac++;
    XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Type",char_set));ac++;
    XtSetArg(al[ac],XmNtopAttachment,XmATTACH_WIDGET); ac++;
    XtSetArg(al[ac],XmNtopWidget,elementOption); ac++;
    paramOption = XmCreateOptionMenu(w,"poption",al,ac);
    XtManageChild(paramOption);

    /* Finally lets make the pushbuttons */
       ac = 0;
       XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Uniform",char_set));ac++;
       paramPush[0] = XmCreatePushButton(paramPulldown,"uniform",al,ac);
       XtManageChild(paramPush[0]);
       XtAddCallback(paramPush[0],XmNactivateCallback,(XtCallbackProc)paramCB,(XtPointer)&UNIFORM);

       ac = 0;
       XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Chord",char_set));ac++;
       paramPush[1] = XmCreatePushButton(paramPulldown,"chord",al,ac);
       XtManageChild(paramPush[1]);
       XtAddCallback(paramPush[1],XmNactivateCallback,(XtCallbackProc)paramCB,(XtPointer)&CHORD);

       ac = 0;
       XtSetArg(al[ac],XmNlabelString,XmStringCreateLtoR("Centripetal",char_set));ac++;
       paramPush[2] = XmCreatePushButton(paramPulldown,"cent",al,ac);
       XtManageChild(paramPush[2]);
       XtAddCallback(paramPush[2],XmNactivateCallback,(XtCallbackProc)paramCB,(XtPointer)&CENTRIP);

}

/*================================== paramCB ===============================*/
/*                                                                          */
/* Call back menu that set the type of parameterization for each curve      */
/*                                                                          */
/*==========================================================================*/
void paramCB(Widget w, XtPointer client_data, XmPushButtonCallbackStruct *call_data)
{
      int type;
      type = *(int *)client_data;
      element[whichelement].parameterization = type;
      printf("Inside paramCB type = %d\n",type);
      element[whichelement].fitted = 0; /* If we play with the parameterization, we need to refit */
      XtSetSensitive(reevaluatepushbutton,False);
      if(XtIsRealized(drawing_area))XClearArea(XtDisplay(toplevel),XtWindow(drawing_area),0,0,0,0,True);
      drawPoints();

}

/*================================== FORCES ================================*/
/*                                                                          */
/* Compute the forces on each element                                       */
/*                                                                          */
/*==========================================================================*/
void forces(int ielem)
{
   int j;
   double lift=0.0, drag=0.0, moment=0.0;
   double xr=0.25, yr=0.;
   double x1,y1,cp1,x2,y2,cp2;
   double xnorm,ynorm,rlen,cpface,dcx,dcy,xmid,ymid;
/*
   double sna=0.2419;
   double csa=0.9702;
   double sna=0.0000;
   double csa=1.0000;
*/

   double sna=0.01745;
   double csa=0.9998;

   for(j=0; j<element[ielem].noControl-1; j++)
   {
     x1  =  element[ielem].xfitted[j];
     y1  =  element[ielem].yfitted[j];
     cp1 = -element[ielem].ycontrol[j];
     x2  =  element[ielem].xfitted[j+1];
     y2  =  element[ielem].yfitted[j+1];
     cp2 = -element[ielem].ycontrol[j+1];

     xnorm =   y2 - y1;
     ynorm = -(x2 - x1);
     rlen  = sqrt(xnorm*xnorm + ynorm*ynorm);
     xnorm = xnorm/rlen;
     ynorm = ynorm/rlen;
     cpface = .5*(cp1 + cp2);
     dcx    = cpface*xnorm*rlen;
     dcy    = cpface*ynorm*rlen;
     lift   = lift - dcx*sna + dcy*csa;
     drag   = drag + dcx*csa + dcy*sna;

     xmid   = .5*(x1 + x2);
     ymid   = .5*(y1 + y2);
     moment = moment + (xmid - xr)*dcy - (ymid - yr)*dcx;
   }

   printf(" Element %d Lift=%lf Drag=%lf Moment=%lf\n",ielem,lift,drag,moment);
}


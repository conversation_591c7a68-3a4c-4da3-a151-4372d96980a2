!=============================== GET_P =====================================80
!
! Calculates pressure in cal_perf_compress path
!
!=============================================================================80
  pure function get_p ( q ) result( p )

    use kinddefs, only : dp
    use fluid,    only : gm1

    real(dp)                           :: p
    real(dp), dimension(5), intent(in) :: q

    real(dp) :: u, v, w

  continue

    u = q(2)/q(1)
    v = q(3)/q(1)
    w = q(4)/q(1)
    p = gm1 *( q(5) - 0.5_dp*q(1)*(u*u + v*v + w*w) )

  end function get_p

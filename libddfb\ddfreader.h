#ifndef DDF_READER_H
#define DDF_READER_H

/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/

/**
 * DDF Binary Reader
 *
 * @short Reader for binary DDF files.
 * @version $Id: ddf_reader.h,v 1.0 2012/09/19 09:02:46 wtjones1 Exp $
 * <AUTHOR>
 */

#ifdef __cplusplus
extern "C" {
#endif

/**
 * Read binary DDF file header information accounting for byte ordering.
 *
 * @param name  File name of DDF file
 * @param title  Title for the DDF file (max 80 characters)
 * @param version  Version of the DDF file (max 20 characters)
 * @param nzone  Number of zones in the DDF file
 * @param solutionTime  Current solution time
 *
 * @return 0 if successful.
 */
extern size_t readBinaryDDFHeader(char* name, char title[80], char version[20], long* nzone, double* solutionTime);

/**
 * Read binary DDF file Zone Header accounting for byte ordering.
 *
 * @param title  Zone title (max 80 characters)
 * @param type  Data type (type=4 for node-centered CFD unstructured)
 * @param npts  Number of points in the zone
 * @param nfunc  Number of function values per point in the zone
 * @param nelem  Number of elements in the zone
 *
 * @return 0 if successful.
 */
extern size_t readBinaryDDFZoneHeader(char title[80], long* type, long* npts, long* nfunc, long* nelem);

/**
 * Read binary DDF file Point data accounting for byte ordering.
 *
 * @param xval  X-Coordinate of the point
 * @param yval  Y-Coordinate of the point
 * @param zval  Z-Coordinate of the point
 * @param pid  Point identifier in the zone
 * @param setid  Set identifier
 * @param func  Function values in the zone (nfunc in length)
 *
 * @return 0 if successful.
 */
extern size_t readBinaryDDFPoint(double* xval, double* yval, double* zval, long* pid, long* setid, double* func);

/**
 * Read binary DDF file Element data accounting for byte ordering.
 *
 * @param npe  Number of points defining the element
 * @param id  Element identifier
 * @param pid  Point identifier
 * @param setid  Set identifier
 * @param elem  Element cell-to-node definition (npe in length, values bias 1).
 *              The element data will be skipped and the file pointer
 *              incremented if elem is NULL.
 *
 * @return 0 if successful.
 */
extern size_t readBinaryDDFElement(long* npe, long* id, long* pid, long* setid, long* elem);

/**
 * Rewind binary DDF file to the start of the current zone data.
 *
 * @return 0 if successful.
 */
extern size_t readBinaryDDFRewindZone(void);

/**
 * Forceably complete reading Binary DDF file and explicitly close the file
 *
 * @return 0 if successful.
 */
extern size_t readBinaryDDFComplete(void);

#ifdef __cplusplus
} /* Close extern "C" declaration. */
#endif

#endif /* DDF_READER_H */

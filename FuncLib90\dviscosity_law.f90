!================================= DVISCOSITY_LAW ============================80
!
! This routine computes the viscosity for a perfect gas,
! given t (temperature) and cstar (sutherland_constant/tref)
!
!=============================================================================80

  pure function dviscosity_law( cstar, t )

    real(dp), intent(in) :: cstar, t

    real(dp) :: dviscosity_law

    real(dp) :: viscosity_law, tstar, tstari

  continue

    tstar  = t+cstar
    tstari = 1._dp/tstar

    viscosity_law = (1.0_dp+cstar)*tstari*t**1.5_dp

    dviscosity_law = viscosity_law*( 0.5_dp + 1.5_dp*cstar/t )*tstari

  end function dviscosity_law

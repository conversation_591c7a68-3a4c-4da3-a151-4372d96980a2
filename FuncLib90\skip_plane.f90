!=========================== SKIP_PLANE ======================================80
!
! Decide on keeping the node or not for twod
!
!=============================================================================80
  pure function skip_plane ( twod, y )

    use twod_util,   only : yplane_2d, y_coplanar_tol

    logical              :: skip_plane
    logical,  intent(in) :: twod
    real(dp), intent(in) :: y

  continue

    skip_plane = .false.

    if (twod) then
      if( abs(y-yplane_2d) >= y_coplanar_tol ) skip_plane = .true.
    end if

  end function skip_plane

#ifndef _IO_H_
#define _IO_H_

/******************************************************************************
 *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: <PERSON>
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     This material is declared a work of the U.S. Government
 *                     and is not subject to copyright protection in the
 *                     United States.
 *
 ******************************************************************************/

/**
 * Byteswapping binary stream input/output routines.
 *
 * @short Byteswapping binary stream input/output.
 * @version $Id: ioswap.h,v 1.0 2012/09/19 09:02:46 wtjones1 Exp $
 * <AUTHOR>
 */
#include <stdlib.h>	/* Needed to define byte order constants (endian.h) */

#ifdef __cplusplus
extern "C" {
#endif

#define IO_BYTESWAP(size_,x)               \
{                                          \
  register int  ii_,sz_=size_-1;           \
  register char *c_=(char *)(x);           \
  if( size_ > 1 )                          \
    for( ii_=0; ii_<size_/2; ++ii_ ) {     \
      *(c_+ii_)       ^= *(c_+(sz_-ii_));  \
      *(c_+(sz_-ii_)) ^= *(c_+ii_);        \
      *(c_+ii_)       ^= *(c_+(sz_-ii_));  \
    }                                      \
}

#if ( ( (defined(__BYTE_ORDER) && (__BYTE_ORDER==__LITTLE_ENDIAN)) || \
        (defined(_BYTE_ORDER) && (_BYTE_ORDER==_LITTLE_ENDIAN)) ||    \
        (defined(BYTE_ORDER) && (BYTE_ORDER==LITTLE_ENDIAN)) ||       \
        (defined(WINNT)) ) &&                                         \
      !defined(NO_BYTE_SWAP) )

#define IO_ENDIANSWAPBIG(size_,x)    IO_BYTESWAP(size_,x)
#define IO_ENDIANSWAPLITTLE(size_,x) {}

#else

#define IO_ENDIANSWAPBIG(size_,x) {}
#define IO_ENDIANSWAPLITTLE(size_,x) IO_BYTESWAP(size_,x)

#endif

#define IOSWAP_REF 0x1234

/**
 * Big Endian binary stream input.
 *
 * @param ptr   location to store elements of data
 * @param size  number of bytes of data element
 * @param nmemb   number of elements of data.
 * @param stream   input stream pointer
 *
 * @return number of items successfully read.
 */
extern size_t fread_BigE(void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * Little Endian binary stream input.
 *
 * @param ptr   location to store elements of data
 * @param size  number of bytes of data element
 * @param nmemb   number of elements of data.
 * @param stream   input stream pointer
 *
 * @return number of items successfully read.
 */
extern size_t fread_LittleE(void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * Big Endian binary stream output.
 *
 * @param ptr   location of elements of data
 * @param size  number of bytes of data element
 * @param nmemb   number of elements of data.
 * @param stream   output stream pointer
 *
 * @return number of items successfully written.
 */
extern size_t fwrite_BigE(void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * Big Endian binary stream output.
 *
 * @param ptr   location of elements of data
 * @param size  number of bytes of data element
 * @param nmemb   number of elements of data.
 * @param stream   output stream pointer
 *
 * @return number of items successfully written.
 */
extern size_t fwrite_LittleE(void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * Determines if machine is Big or Little Endian.
 *
 * @return 1 if machine is Big Endian, 0 if Little Endian.
 */
extern int IsBigEndian();

/**
 * Non-Native Endian binary stream input.
 *
 * @param ptr   location of elements of data
 * @param size  number of bytes of data element
 * @param nmemb   number of elements of data.
 * @param stream   output stream pointer
 *
 * @return number of items successfully written.
 */
extern size_t fread_Swapped(void *ptr, size_t size, size_t nmemb, FILE *stream);

/**
 * Non-Native Endian binary stream output.
 *
 * @param ptr   location of elements of data
 * @param size  number of bytes of data element
 * @param nmemb   number of elements of data.
 * @param stream   output stream pointer
 *
 * @return number of items successfully written.
 */
extern size_t fwrite_Swapped(void *ptr, size_t size, size_t nmemb, FILE *stream);

#ifdef __cplusplus
} /* Close extern "C" declaration. */
#endif

#endif

LIBCORE_DIR=@libcore_path@/Complex
LIBTURB_DIR=@libturb_path@/Complex
LIBSMEMRD_DIR=@top_builddir@/libsmemrd/Complex
LIBDDFB_DIR=@top_builddir@/libddfb
FUNCLIB_DIR=@top_builddir@/FuncLib90/Complex
LIBDEFS_DIR=@top_builddir@/libdefs/Complex
LIBINIT_DIR=@top_builddir@/libinit/Complex
LIBF90_DIR=@top_builddir@/LibF90/Complex
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@/Complex
PHYSICS_DEPS_DIR=@top_builddir@/PHYSICS_DEPS/Complex
ENGINESIM_DIR=@top_builddir@/enginesim/src
FUN3D_90_DIR=@top_builddir@/FUN3D_90/Complex

include ../Common.am

noinst_LIBRARIES = libFUN3DFlow.a

bin_PROGRAMS = complex_nodet@MPI_EXT@

libFUN3DFlow_a_LIBADD =
nodist_libFUN3DFlow_a_SOURCES = $(libFUN3DFlow_SRCS)
libFUN3DFlow_a_LINK = $(F90LINK)

complex_nodet@MPI_EXT@_LDADD = $(nodet_LDSTUFF)
nodist_complex_nodet@MPI_EXT@_SOURCES = $(nodet_SRCS)

BUILT_SOURCES += \
	$(nodist_complex_nodet@MPI_EXT@_SOURCES) \
	$(nodist_libFUN3DFlow_a_SOURCES)

%.f90: $(top_srcdir)/FUN3D_90/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/FUN3D_90/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@


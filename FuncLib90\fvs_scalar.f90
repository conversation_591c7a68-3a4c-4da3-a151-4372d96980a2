!================================= FVS_SCALAR ================================80
!
! Flux on the face via scalar flux vector splitting scheme.
!
!=============================================================================80

  pure function fvs_scalar( eigl, eigr, ql, qr, eiga_eps )

    use kinddefs,        only : dp

    real(dp), intent(in) :: eigl, eigr, ql, qr, eiga_eps
    real(dp)             :: fvs_scalar

    real(dp) :: eigal, eigar, eiglp, eigrm

  continue

     eigal = aharten( eigl, eiga_eps )
     eigar = aharten( eigr, eiga_eps )

     eiglp = ( eigl + eigal )*0.5_dp
     eigrm = ( eigr - eigar )*0.5_dp

     fvs_scalar = eiglp*ql + eigrm*qr

  end function fvs_scalar

!======================= DQC_VIA_DUVW ========================================80
!
! d(Q-conservative) via d(Q-primitive-velocity-vector) = d(u,v,w)
!
!=============================================================================80
  pure function dqc_via_duvw( eqn_set, du, dv, dw, rho, u, v, w )

    use solution_types, only : compressible

    integer,    intent(in) :: eqn_set
    real(dp),   intent(in) :: du, dv, dw, rho, u, v, w

    real(dp), dimension(4) :: dqc_via_duvw

  continue

    dqc_via_duvw(1) = 0._dp
    dqc_via_duvw(2) = du
    dqc_via_duvw(3) = dv
    dqc_via_duvw(4) = dw

    if ( eqn_set == compressible ) then

      !...d(u)/dQ = -u/rho, 1/rho,     0,      0
      !...d(v)/dQ = -v/rho,     0  1/rho,      0
      !...d(w)/dQ = -w/rho,     0      0,  1/rho

      dqc_via_duvw(1) = -( u*du + v*dv + w*dw )

      dqc_via_duvw(1:4) =  dqc_via_duvw(1:4) / rho

    endif

  end function dqc_via_duvw

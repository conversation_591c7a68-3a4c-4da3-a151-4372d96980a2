\section*{Quick Start\label{c:start}}

This section takes you from source code tarball to a rudimentary flow solution
using single processor execution on a typical Unix-style environment
(e.g.~Linux, \Mac OS) with a Fortran compiler and the GNU Make utility.
\FunThreeD is most commonly executed in parallel, 
but the intent here is to provide the most basic installation,
setup, and execution of the \FunThreeD flow solver 
without the complexity of any third-party libraries or packages.

See \sectionref{s:obtaining} for instructions on obtaining the \FunThreeD
source code tarball.
Once you have it, unpack the source code tarball,
configure it for your system (\sectionref{c:installation}),
compile it, and add the executables directory to your search path.
For C Shell, e.g.,
\begin{Verbatim}[commandchars=+\[\]]
  tar zxf fun3d-+version-*.tar.gz
  cd fun3d-+version-*
  mkdir _seq
  cd _seq
    ../configure --prefix=${PWD}
    make install
    setenv PATH ${PWD}/bin:${PATH}
  cd ..
\end{Verbatim}
%$ for Emacs
For Bourne Shell, the \file{setenv} command is \file{export PATH=${PWD}/bin:${PATH}}. 
The change to the \file{PATH} environment variable can be made permanently
by adding the \file{setenv} or \file{export} command
to your shell start up file.
Next, move to the \path{doc/quick_start} directory,
\begin{Verbatim}
  cd doc/quick_start
\end{Verbatim}
where you will find a very coarse 3D wing grid (\file{inv_wing.fgrid})
intended for inviscid flow simulation (\sectionref{s:grids}).
Also in this directory are 
the associated boundary conditions
file \file{inv_wing.mapbc} (\sectionref{s:boundaryconditions})
and a \FunThreeD input file \file{fun3d.nml} in Fortran
namelist format (\sectionref{s:fun3d.nml}).

Execute the flow solver (\sectionref{s:flow-exec}) by running the command
\VerbatimInput{step_01.sh}
This should produce screen output similar to
\VerbatimInput[fontsize=\scriptsize,numbers=left,firstline=1,lastline=51]%
  {step_01.out}
\vdots
\VerbatimInput[fontsize=\scriptsize,numbers=left,firstline=171]%
  {step_01.out}

If \FunThreeD completed successfully, 
a Mach 0.7 inviscid flow over a very coarse representation of 
an ONERA M6 semi-span wing\cite{agard-ar-138} at two degrees angle of attack
is available.
If not, please refer to Troubleshooting on page~\pageref{s:help}.

With visualization software capable of reading \Tecplot files,
you can visualize various surface quantities
with \file{inv_wing_tec_boundary.dat}
as shown by the pressure contours in \fig{f:qs:solution}.
\begin{figure}
  \centering
  \includegraphics[width=0.85\linewidth]%
    {inv_wing_tec_boundary}
  \caption{Mach 0.7 flow about a coarse ONERA M6 semi-span wing
           at 2 degrees angle of attack.}
  \label{f:qs:solution}
\end{figure}
Iterative convergence history can be plotted from \file{inv_wing_hist.dat}
as shown in \fig{f:qs:history}.
\begin{figure}
  \centering
  \includegraphics{inv_wing_hist}
  \caption{Iterative convergence history for coarse ONERA M6 wing.}
  \label{f:qs:history}
\end{figure}
Histories of all five conservation equation residual norms 
are denoted \cmd{R_1}--\cmd{R_5}, 
and the lift coefficient convergence history is denoted \cmd{C_L}.

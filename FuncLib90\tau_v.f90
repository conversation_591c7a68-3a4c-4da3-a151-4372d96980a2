!================================= TAU_V =====================================80
!
! Full viscous fluxes for compressible.
!
! Incoming variables and gradients are primitive variables (var1,u,v,w,T).
!
!=============================================================================80
  pure function tau_v( xnorm, ynorm, znorm, area,                              &
                       qtgrad, u, v, w, mu, amut )

    use flux_constants,        only : xmr, cgp, cgpt, c43, c23

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, u, v, w, mu, amut

    real(dp), dimension(3,5), intent(in) :: qtgrad !qtgrad(:,1) unused by intent

    real(dp), dimension(5) :: tau_v

    real(dp) :: muta, mucgpa
    real(dp) :: t_xx, t_yy, t_zz, t_xy, t_xz, t_yz

  continue

!   xmr   = xmach/re
!   c43   = xmr*4._dp/3._dp
!   c23   = xmr*2._dp/3._dp
!   cgp   = xmr/(gm1*prandtl)
!   cgpt  = xmr/(gm1*turbulent_prandtl)

    muta   = (     mu +      amut )*area
    mucgpa = ( cgp*mu + cgpt*amut )*area

!   ngradt = xnorm*qtgradx(5) + ynorm*qtgrady(5) + znorm*qtgradz(5)

!   t_xx =   c43*ux - c23*vy - c23*wz
!   t_yy = - c23*ux + c43*vy - c23*wz
!   t_zz = - c23*ux - c23*vy + c43*wz

!   t_xy = xmr*(uy + vx)
!   t_xz = xmr*(uz + wx)

!   t_yz = xmr*(vz + wy)

    t_xx =   c43*qtgrad(1,2) - c23*qtgrad(2,3) - c23*qtgrad(3,4)
    t_yy = - c23*qtgrad(1,2) + c43*qtgrad(2,3) - c23*qtgrad(3,4)
    t_zz = - c23*qtgrad(1,2) - c23*qtgrad(2,3) + c43*qtgrad(3,4)

    t_xy = xmr*(qtgrad(2,2) + qtgrad(1,3))
    t_xz = xmr*(qtgrad(3,2) + qtgrad(1,4))

    t_yz = xmr*(qtgrad(3,3) + qtgrad(2,4))

    tau_v(1) = 0._dp
    tau_v(2) = -muta*(xnorm*t_xx + ynorm*t_xy + znorm*t_xz)
    tau_v(3) = -muta*(xnorm*t_xy + ynorm*t_yy + znorm*t_yz)
    tau_v(4) = -muta*(xnorm*t_xz + ynorm*t_yz + znorm*t_zz)
    tau_v(5) =  u*tau_v(2) + v*tau_v(3) + w*tau_v(4)                      &
     - mucgpa*( xnorm*qtgrad(1,5) + ynorm*qtgrad(2,5) + znorm*qtgrad(3,5) )

  end function tau_v

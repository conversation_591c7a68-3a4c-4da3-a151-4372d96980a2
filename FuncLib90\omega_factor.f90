!============================= OMEGA_FACTOR ==================================80
!
! Find relaxation factor of update.
!
!=============================================================================80
  pure function omega_factor( limit_x, x, dx, limit_dx_threshold )

    use kinddefs,        only : dp

    real(dp), intent(in) :: limit_x, x, dx, limit_dx_threshold
    real(dp)             :: omega_factor

  continue

    omega_factor = 1._dp

    if ( abs(dx) < limit_dx_threshold ) return

    omega_factor = ( limit_x - x ) / dx


  end function omega_factor

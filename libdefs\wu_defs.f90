module wu_defs

  use kinddefs,        only : dp
  use lmpi,            only : lmpi_master, lmpi_conditional_stop, &
                              lmpi_synchronize
  use fun3d_maximums,  only : ngrid_max

  implicit none

  private

  public :: times, clocks_initial, wall_cr_inv, dof_grid
  public :: section_calls, section_cpu, section_name, n_section
  public :: eqn_group_passes, eqn_group_work
  public :: jacobians_mean, jacobians_turb
  public :: residuals_mean, residuals_turb, enveloped_timing
  public :: section_enveloped, jacobian_v_cycle_counter, v_cycle_part
  public :: intermittent_failures

  ! wall clock time. system_clock is a standard fortran90 routine.
  integer  :: wall_cr     = 0       ! wall count rate
  real(dp) :: wall_cr_inv = 0.0_dp  ! inverse of wall count rate
  integer  :: clocks_initial

  integer, parameter :: n_section_max = 200

  integer :: n_section

  character(len=80), dimension(n_section_max)           :: section_name
  integer,           dimension(n_section_max,ngrid_max) :: section_calls
  real(dp),          dimension(n_section_max,ngrid_max) :: section_cpu
  integer,           dimension(n_section_max,ngrid_max) :: clocks
  integer,           dimension(n_section_max,ngrid_max) :: section_enveloped

  integer, dimension(ngrid_max,2) :: eqn_group_passes = 0
  integer, dimension(ngrid_max,2) :: eqn_group_work   = 0
  integer, dimension(ngrid_max)   :: jacobians_mean   = 0
  integer, dimension(ngrid_max)   :: jacobians_turb   = 0
  integer, dimension(ngrid_max)   :: residuals_mean   = 0
  integer, dimension(ngrid_max)   :: residuals_turb   = 0

  integer :: v_cycle_part = 1
  integer, dimension(ngrid_max,2,2) :: jacobian_v_cycle_counter = 0

  integer,  dimension(ngrid_max) :: dof_grid

  logical :: enveloped_timing = .false.

  integer :: intermittent_failures = 0

contains

!=============================== TIMES ======================================80
!
! Accumulate time for a section of code.
!
!=============================================================================80

  subroutine times(section,grid)

    use info_depr, only : skeleton

    character(len=*), intent(in)           :: section
    integer,          intent(in), optional :: grid

    integer :: clocks_current, i, s, g, sl, gl

    logical, save :: first_time_through = .true.

    logical :: debug_times = .false., debug_envelope = .false.

    real(dp) :: slice_cpu

    character(80) :: particular_code

  continue

   !particular_code = 'PreconditionerRelax-MeanFlow'
    particular_code = ''

    call lmpi_synchronize()

    g=1
    if ( present(grid) ) g = grid

    call system_clock(clocks_current)

    if ( first_time_through ) then
      call system_clock(count_rate=wall_cr)
      wall_cr_inv = 1.0_dp/wall_cr
      clocks_initial = clocks_current
      first_time_through = .false.
      section_name(:) = ''
      section_calls(:,:) = 0
      section_enveloped(:,:) = 0
      section_cpu(:,:)   = 0._dp
      if ( debug_times ) then
        write(301,"(1x,2(4x,a),15x,a,6x,a,9x,a,17x,a,1x,a)")              &
        's','g','calls','current-clocks','clocks(s,g)','cpu','section_name'
      endif
      n_section = 3
      section_name(1) = 'FUN3D-StartUp'
      section_name(2) = 'FMG-StartUp'
      section_name(3) = 'FMG-FAS'
    end if

    s = 0
    do i=1,n_section
      if ( trim(section) /= trim(section_name(i)) ) cycle
      s = i
      exit
    enddo

    if ( s == 0 .and. n_section < n_section_max ) then
      n_section               = n_section + 1
      section_name(n_section) = trim(section)
      s                       = n_section
    elseif ( s == 0 ) then
      call lmpi_conditional_stop(1,'n_section_max too small')
    endif

    if ( .not. lmpi_master ) return

    section_calls(s,g) = section_calls(s,g) + 1

    if ( section_calls(s,g) /= (section_calls(s,g)/2)*2 ) then
      clocks(s,g) = clocks_current
    else
      slice_cpu = + ( clocks_current - clocks(s,g) )*wall_cr_inv
      if ( real(slice_cpu,dp) >= 0._dp ) then
        section_cpu(s,g) = section_cpu(s,g) + slice_cpu
      else
        intermittent_failures = intermittent_failures + 1
        if ( intermittent_failures <= 20 .and. skeleton > 0)     &
        write(*,*) ' Negative wall time...slice_cpu=',slice_cpu, &
                   ' clocks_current=',clocks_current,            &
                   ' clocks(s,g)=',clocks(s,g),                  &
                   ' s=',s,' g=',g,' section=',trim(section_name(s))
      endif
      if ( trim(section_name(s)) == trim(particular_code) ) then
        if ( section_calls(s,g) == 2 ) then
          write(301,*) 'TITLE="Timing:',trim(section_name(s)),'"'
          write(301,*) ' VARIABLES= "s" "g" "call" "slice_time" "time"'
          write(301,*) 'ZONE T="',trim(section_name(s)),'"'
        endif
        write(301,"(1x,2i5,i20,2e20.10)")       &
        s,g,section_calls(s,g)/2,slice_cpu,section_cpu(s,g)
      endif
    endif

    if ( debug_times ) then
      write(301,"(1x,2i5,3i20,f20.4,2(1x,a))")            &
      s,g,section_calls(s,g),clocks_current,clocks(s,g),  &
      real(section_cpu(s,g),dp), trim(section_name(s)),trim(section)
    endif

    do sl=4,n_section
      if ( sl == s ) cycle
      do gl=1,ngrid_max
        if ( section_calls(sl,gl) == 0 ) cycle
        if ( section_calls(sl,gl) == (section_calls(sl,gl)/2)*2 ) cycle
          enveloped_timing = .true.
          section_enveloped(s,g) = sl
          if ( debug_envelope ) then
            write(301,"(1x,2i5,i20,1x,a)")            &
            s,g,section_calls(s,g),trim(section_name(s))
            write(301,"(1x,2i5,i20,1x,a)")            &
            sl,gl,section_calls(sl,gl),trim(section_name(sl))
          endif
          exit
        enddo
    enddo

  end subroutine times

end module wu_defs

chaos_SRCS = \
chaos.F90 \
chaos_brute_force.F90 \
chaos_construction.f90 \
chaos_datas.F90 \
chaos_readers.F90 \
outputter.f90

if BUILD_MPI
AM_FCFLAGS = \
	$(FC_MODINC)@MPIINC@ \
	$(FC_MODINC)@top_builddir@ \
	$(FC_MODINC)$(LIBCORE_DIR)
else
AM_FCFLAGS = \
	$(FC_MODINC)@top_builddir@ \
	$(FC_MODINC)$(LIBCORE_DIR)
endif

# FIXME: remove *.mod *.fh when mod_suffix works on OS X
CLEANFILES = *.$(FC_MODEXT) mpif.h *.time *.mod *.fh *.d

chaos_f90s=$(chaos_SRCS:.F90=.f90)
chaos_deps=$(chaos_f90s:.f90=.d)

DISTCLEANFILES = $(chaos_deps)

SUFFIXES = .d

BUILT_SOURCES = $(chaos_deps)

-include $(chaos_deps)
include $(top_srcdir)/make.rules

ordered_targets:
	@$(MAKE) clean
	@$(MAKE) -n | grep "^\$(FC)" | sed 's/.*-o /	   /' | \
								sed 's/\.o .*/\.f90 \\/'

lib_MODULES = $(chaos_f90s:.f90=.$(FC_MODEXT))

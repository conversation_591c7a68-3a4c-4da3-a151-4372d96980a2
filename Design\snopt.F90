module snopt

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,          only : dp
  use design_types,      only : max_string_length

  implicit none

  private

  public :: snopt_driver
  public :: usrfun

  integer :: snopt_what_to_do
  integer :: snopt_io

  integer, dimension(:), pointer :: restart_flow
  integer, dimension(:), pointer :: restart_dual

  logical :: snopt_lss_flag
  logical :: snopt_restart_optimization

  character(len=max_string_length) :: snopt_ammo_directory
  character(len=max_string_length), dimension(:), pointer :: desc_directory

  real(dp), dimension(:),   pointer    :: c, d
  real(dp), dimension(:,:), pointer    :: last_flow_dvs
  real(dp), dimension(:,:), pointer    :: last_adjoint_dvs

  integer                :: objrow = 1          ! Which row is objective func

  integer, parameter  :: print_file = 57
  integer, parameter  :: sum_file   = 58

#ifdef HAVE_SNOPT
  real(dp), parameter :: optimality_tolerance = 1.e-4_dp ! Basically
                                                         ! the number
                                                         ! of digits
                                                         ! agreement
                                                         ! between
                                                         ! final and
                                                         ! optimal objs
#endif

contains

!============================== SNOPT_DRIVER =================================80
!
!  Performs a multipoint optimization using SNOPT
!
!=============================================================================80
  subroutine snopt_driver(max_design_cycles,what_to_do,restart_optimization,   &
                          io,ammo_directory,lss_flag)

    use allocations,       only : my_alloc_ptr
    use kinddefs,          only : dp
    use design_types,      only : opt_data_type
    use designs,           only : load_optimization_data, get_design_variables,&
                                  free_optimization_data
    use lmpi,              only : lmpi_die
    use system_extensions, only : se_chdir
#ifdef HAVE_SNOPT
    use nml_design,        only : feas_tol_val, n_design_pts, model_variables
#else
    use nml_design,        only : n_design_pts, model_variables
#endif

    integer, intent(in) :: max_design_cycles, what_to_do, io

    logical, intent(in) :: lss_flag
    logical, intent(in) :: restart_optimization

    character(len=*), intent(in) :: ammo_directory

    integer :: i,j               ! Loop indices
    integer :: n                 ! Number of design variables
    integer :: nobj              ! Number of objective functions
    integer :: nclin             ! Number of linear constraints
    integer :: ncnln             ! Number of nonlinear constraints
    integer :: nf                ! Number of total functions
    integer :: lena              ! dimension of the array a
    integer :: nea               ! Non-zeros in the array a
    integer :: leng              ! dimension of the array g
    integer :: neg               ! Non-zeros in the array g
    integer :: counter           ! Indexing variable

    integer, dimension(:),    pointer :: iafun, javar
    integer, dimension(:),    pointer :: igfun, jgvar
    integer, dimension(:),    pointer :: xstate, fstate

    real(dp), dimension(:),   pointer :: x
    real(dp), dimension(:),   pointer :: xlow, xupp
    real(dp), dimension(:),   pointer :: flow, fupp
    real(dp), dimension(:),   pointer :: a
    real(dp), dimension(:),   pointer :: f
    real(dp), dimension(:),   pointer :: xmul, fmul

    type(opt_data_type), dimension(n_design_pts) :: opt_data

    integer  :: lencw = 500
    integer  :: leniw = 500
    integer  :: lenrw = 500

    integer  :: errors

    character(len=8), dimension(:), pointer :: cw
    integer, dimension(:),          pointer :: iw
    real(dp), dimension(:),         pointer :: rw

    integer :: nfname, nxname
    character(len=8), dimension(:), pointer :: xnames, fnames

#ifdef HAVE_SNOPT
    integer                :: start = 0  ! FIXME: What to do about restarts?
    integer                :: mincw, miniw, minrw
    integer                :: ns
    integer                :: ninf

    real(dp)               :: sinf
    real(dp)               :: objadd = 0.0_dp
    real(dp), parameter    :: infbnd = 1.0e+20_dp
#endif

  continue

! Open the SNOPT output files

    open(print_file,file='snopt.printfile')
    open(sum_file,file='snopt.summaryfile')

    call my_alloc_ptr(cw,  8, lencw)
    call my_alloc_ptr(iw,     leniw)
    call my_alloc_ptr(rw,     lenrw)

#ifdef HAVE_SNOPT
    call sninit(0, 0, cw, lencw, iw, leniw, rw, lenrw)
#endif

! Load the optimization data for each model

    do i = 1, n_design_pts
      call se_chdir(model_variables(i)%model_directory)
      opt_data(i)%allocated = .false.
      call load_optimization_data(opt_data(i),io,'SNOPT: Location 1')
      opt_data(i)%scale = 1.0_dp
    end do

! Load the design variable info into the SNOPT-style array
! using the values from the first model.  We assume the
! parameterization, bounds, active DV's, etc are the same
! across all models

    call get_design_variables(n_design_pts,opt_data,n,x,xlow,xupp)

    nobj   = 0
    nclin  = 0
    ncnln  = 0

    do i = 1, n_design_pts
      nobj = nobj + opt_data(i)%nobjectives
      ncnln = ncnln + opt_data(i)%nconstraints
    end do

    nf     = 1 + nclin + ncnln     ! Single composite Objective Function

    nea    = nclin*n
    neg    = nf*n       ! Assume all entries are non-zero

    lena   = max(1,nea)
    leng   = max(1,neg)

    call my_alloc_ptr(a,      lena)
    call my_alloc_ptr(iafun,  lena)
    call my_alloc_ptr(javar,  lena)
    call my_alloc_ptr(igfun,  leng)
    call my_alloc_ptr(jgvar,  leng)

    call my_alloc_ptr(xmul,   n)
    call my_alloc_ptr(xstate, n)
    call my_alloc_ptr(f,      nf)
    call my_alloc_ptr(fmul,   nf)
    call my_alloc_ptr(flow,   nf)
    call my_alloc_ptr(fupp,   nf)
    call my_alloc_ptr(fstate, nf)

! Set the upper and lower bounds on the constraints

    counter = objrow
    do i = 1, n_design_pts
      if ( opt_data(i)%nconstraints > 0 ) then
        do j = 1, opt_data(i)%nconstraints
          counter = counter + 1
          flow(counter) = opt_data(i)%constraint_lower_bound(j)
          fupp(counter) = opt_data(i)%constraint_upper_bound(j)
        end do
      endif
    end do

! Free memory

    do i = 1, n_design_pts
      call free_optimization_data(opt_data(i))
    end do

! Initialize and set some data in the snopt data module since we can't
! pass it in through argument lists

    call initialize_snopt_data(n,what_to_do,restart_optimization,io,           &
                               ammo_directory,lss_flag)

    call establish_scaling(n,xlow,xupp,x)

! Determine the true work size required for the problem

    nxname = 1
    nfname = 1
    call my_alloc_ptr(xnames,  8, nxname)
    call my_alloc_ptr(fnames,  8, nfname)

#ifdef HAVE_SNOPT
    call snmema(errors, nf, n, nxname, nfname, nea, neg, mincw, miniw, minrw,  &
                cw, lencw, iw, leniw, rw, lenrw)

    if ( errors /= 104 ) then
        write(*,*) 'Work Array Size Estimation Error. ',errors
        call lmpi_die
    endif

    lencw = mincw
    leniw = miniw
    lenrw = minrw

! User's Guide says to allocate and copy temp arrays to final cw, iw, and rw.
! This seems give more weight to simply calling snInit() again with new arrays.

    deallocate(cw, iw, rw)

    call my_alloc_ptr(cw,  8, lencw)
    call my_alloc_ptr(iw,     leniw)
    call my_alloc_ptr(rw,     lenrw)
    call sninit(print_file, sum_file, cw, lencw, iw, leniw, rw, lenrw)

! Set some defaults for the SNOPT package

    errors = 0
    call snseti('Derivative option', 1,                                        &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Derivative option: 1'

    call snseti('Major iterations limit', max_design_cycles, print_file,       &
                sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Major iterations limit: ', max_design_cycles

    call snsetr('Major feasibility tolerance', feas_tol_val,                   &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Major feasibility tolerance: ', feas_tol_val

    call snsetr('Major optimality tolerance', optimality_tolerance,            &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Major optimality tolerance: ', optimality_tolerance

    call snseti('Major print level', 20,                                       &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Major print level: 20'

    call snseti('Minor print level', 20,                                       &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Minor print level: 20'

    call snseti('Print file', print_file,                                      &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Print file: ', print_file

    call snseti('Print frequency', 100,                                        &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Print frequency: ', 100

    call snseti('Summary file', sum_file,                                      &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Summary file: ', sum_file

    call snseti('Summary frequency', 100,                                      &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Summary frequency: ', 100

    call snseti('Verify level', -1,                                            &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Verify level: -1'

    call snsetr('Infinite bound', infbnd,                                      &
                print_file, sum_file, errors, cw, lencw, iw, leniw, rw, lenrw)
    write(io,*) 'Infinite bound: ', infbnd

    if ( errors /= 0 ) then
        write(*,'(a,i,a)') 'There were ',errors,' errors setting options.'
        call lmpi_die
    endif

!call snspec() here for overrides

! Set the upper and lower bounds on the objective to make it "free"

    flow(objrow) = -infbnd      ! Unbounded => "free"
    fupp(objrow) =  infbnd      ! Unbounded => "free"

    write(io,*) 'heading into snOptA: xlow, xupp...'
    do i = 1, size(xlow,1)
      write(io,*) xlow(i), xupp(i)
    end do

    write(io,*) 'heading into snOptA: flow, fupp...'
    do i = 1, size(flow,1)
      write(io,*) flow(i), fupp(i)
    end do

! Assume gradients are all non-zero for now

    counter = 0
    rows_of_g : do i = 1, nf
      columns_of_g : do j = 1, n
        counter = counter + 1
        igfun(counter) = i
        jgvar(counter) = j
      end do columns_of_g
    end do rows_of_g
    neg = leng

    xstate = 0
    fstate = 0
    fmul = 0.0_dp

    call snopta(start,nf,n,nxname,nfname,                                      &
                objadd,objrow,'FUN3D   ',usrfun,                               &
                iafun,javar,lena,nea,a,                                        &
                igfun,jgvar,leng,neg,                                          &
                xlow,xupp,xnames,flow,fupp,fnames,                             &
                x,xstate,xmul,f,fstate,fmul,                                   &
                errors,mincw,miniw,minrw,                                      &
                ns,ninf,sinf,                                                  &
                cw,lencw,iw,leniw,rw,lenrw,                                    &
                cw,lencw,iw,leniw,rw,lenrw)
#else
    errors = max_design_cycles ! Try to quiet the g95 compiler
    write(*,*) 'You do not have SNOPT installed.'
    call lmpi_die
#endif

! Set some data from the snopt data module that SNOPT may have set

    do i = 1, n_design_pts
      model_variables(i)%restart_flow = restart_flow(i)
      model_variables(i)%restart_dual = restart_dual(i)
    end do

    call interpret_snopt_output(errors)

    deallocate(cw, iw, rw, fnames, xnames, fstate, fupp, flow, fmul, f, xstate,&
               xupp, xlow, xmul, x, jgvar, igfun, javar, iafun, a)

! Close the SNOPT output files

    close(print_file)
    close(sum_file)

  end subroutine snopt_driver


!============================== INTERPRET_SNOPT_OUTPUT =======================80
!
!  Interprets the flag returned from SNOPT after an optimization
!
!=============================================================================80

  subroutine interpret_snopt_output(snopt_flag)

    use system_extensions, only : se_flush

    integer, intent(in) :: snopt_flag

  continue

    write(*,*) 'SNOPT has returned the following flag: ',snopt_flag

    select case ( snopt_flag )
    case (1)
      write(*,*) 'Finished successfully'
      write(*,*) 'The optimality conditions where satisfied'
    case (2)
      write(*,*) 'Finished successfully'
      write(*,*) 'A feasible point was found'
    case (3)
      write(*,*) 'Finished successfully'
      write(*,*) 'However, the requested accuracy could not be achieved'
    case (11)
      write(*,*) 'The problem appears to be infeasible.'
      write(*,*) 'Infeasible linear constraints'
    case (12)
      write(*,*) 'The problem appears to be infeasible.'
      write(*,*) 'Infeasible linear equalities'
    case (13)
      write(*,*) 'The problem appears to be infeasible.'
      write(*,*) 'Nonlinear infeasibilities minimized'
    case (14)
      write(*,*) 'The problem appears to be infeasible.'
      write(*,*) 'Infeasibilities minimized'
    case (21)
      write(*,*) 'The problem appears to be unbounded.'
      write(*,*) 'Unbounded objective'
    case (22)
      write(*,*) 'The problem appears to be unbounded.'
      write(*,*) 'Constraint violation limit reached'
    case (31)
      write(*,*) 'Resource limit error.'
      write(*,*) 'Iteration limit reached'
    case (32)
      write(*,*) 'Resource limit error.'
      write(*,*) 'Major iteration limit reached'
    case (33)
      write(*,*) 'Resource limit error.'
      write(*,*) 'The superbasics limit is too small'
    case (41)
      write(*,*) 'Terminated after numerical difficulties.'
      write(*,*) 'Current point cannot be improved'
    case (42)
      write(*,*) 'Terminated after numerical difficulties.'
      write(*,*) 'Singular basis'
    case (43)
      write(*,*) 'Terminated after numerical difficulties.'
      write(*,*) 'Cannot satisfy the general constraints'
    case (44)
      write(*,*) 'Terminated after numerical difficulties.'
      write(*,*) 'Ill-conditioned null-space basis'
    case (51)
      write(*,*) 'Error in the user-supplied functions.'
      write(*,*) 'Incorrect objective derivatives'
    case (52)
      write(*,*) 'Error in the user-supplied functions.'
      write(*,*) 'Incorrect constraint derivatives'
    case (61)
      write(*,*) 'Undefined user-supplied functions.'
      write(*,*) 'Undefined function at the first feasible point'
    case (62)
      write(*,*) 'Undefined user-supplied functions.'
      write(*,*) 'Undefined function at the initial point'
    case (63)
      write(*,*) 'Undefined user-supplied functions.'
      write(*,*) 'Unable to proceed into undefined region'
    case (71)
      write(*,*) 'User requested termination.'
      write(*,*) 'Terminated during function evaluation'
    case (74)
      write(*,*) 'User requested termination.'
      write(*,*) 'Terminated from monitor routine'
    case (81)
      write(*,*) 'Insufficient storage allocated.'
      write(*,*) 'Work arrays must have at least 500 elements'
    case (82)
      write(*,*) 'Insufficient storage allocated.'
      write(*,*) 'Not enough character storage'
    case (83)
      write(*,*) 'Insufficient storage allocated.'
      write(*,*) 'Not enough integer storage'
    case (84)
      write(*,*) 'Insufficient storage allocated.'
      write(*,*) 'Not enough real storage'
    case (91)
      write(*,*) 'Input arguments out of range.'
      write(*,*) 'Invalid input argument'
    case (92)
      write(*,*) 'Input arguments out of range.'
      write(*,*) 'Basis file dimensions do not match this problem'
    case (141)
      write(*,*) 'System error.'
      write(*,*) 'Wrong number of basic variables'
    case (142)
      write(*,*) 'System error.'
      write(*,*) 'Error in basis package'
    case default
      write(*,*) 'Nothing seems to be known about this SNOPT flag...'
    end select

    call se_flush(6)

  end subroutine interpret_snopt_output


!============================== INITIALIZE_SNOPT_DATA ========================80
!
!  Initializes some data in the SNOPT data module
!
!=============================================================================80
  subroutine initialize_snopt_data(n,what_to_do,restart_optimization,io,       &
                                   ammo_directory,lss_flag)

    use allocations,  only : my_alloc_ptr
    use kinddefs,     only : dp
    use nml_design,   only : model_variables, n_design_pts
    use design_types, only : max_string_length

    integer, intent(in) :: n, what_to_do, io

    logical, intent(in) :: lss_flag, restart_optimization

    character(len=*), intent(in) :: ammo_directory

    integer :: i

  continue

    call my_alloc_ptr(restart_flow,   n_design_pts)
    call my_alloc_ptr(restart_dual,   n_design_pts)
    call my_alloc_ptr(desc_directory, max_string_length, n_design_pts)

    do i = 1, n_design_pts
      restart_flow(i)   = model_variables(i)%restart_flow
      restart_dual(i)   = model_variables(i)%restart_dual
      desc_directory(i) = model_variables(i)%desc_directory
    end do

    snopt_ammo_directory       = ammo_directory
    snopt_what_to_do           = what_to_do
    snopt_restart_optimization = restart_optimization
    snopt_io                   = io
    snopt_lss_flag             = lss_flag

    call my_alloc_ptr(last_flow_dvs,    n_design_pts, n)
    call my_alloc_ptr(last_adjoint_dvs, n_design_pts, n)

! Allocate routine sets things to zero, so we ought
! to set it to something crazy, since this could
! very well be our starting design point

    last_flow_dvs    = huge(1.0_dp)
    last_adjoint_dvs = huge(1.0_dp)

  end subroutine initialize_snopt_data

!============================== ESTABLISH_SCALING ============================80
!
!  Establishes scaling coefficients for SNOPT and also scales the dvs and bounds
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine establish_scaling(n,xlow,xupp,x)

    use allocations, only : my_alloc_ptr
    use kinddefs,    only : dp

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x
    real(dp), dimension(:), intent(inout) :: xlow, xupp

  continue

    call my_alloc_ptr(c,n)
    call my_alloc_ptr(d,n)

    c(1:n) = 0.5_dp*(xlow(1:n)+xupp(1:n))
    d(1:n) = 0.5_dp*(xupp(1:n)-xlow(1:n))

! scale the bounds

    xlow(1:n) = (xlow(1:n)-c(1:n)) / d(1:n)
    xupp(1:n) = (xupp(1:n)-c(1:n)) / d(1:n)

! scale the dvs

    x(1:n) = (x(1:n)-c(1:n)) / d(1:n)

  end subroutine establish_scaling


!============================== OBJECTIVE ====================================80
!
!  Evaluates the objective function and gradients for SNOPT
!
!  Be very careful to see if we have already been to this design point
!  with the flow solver and/or the adjoint solver
!
!  Before doing an adjoint, make sure the flow solution is at the correct
!  design point
!
!=============================================================================80
subroutine objective( n, x, needf, f, needg, g )

  use kinddefs,          only : dp
  use design_types,      only : opt_data_type
  use designs,           only : load_optimization_data, set_design_variables,  &
                                unload_optimization_data, been_there,          &
                                free_optimization_data, combine_mp_functions,  &
                                combine_mp_gradients
  use analysis,          only : perform_analysis
  use sensitivity,       only : perform_sensitivity_analysis
  use system_extensions, only : se_chdir
  use utilities,         only : check_for_stop
  use file_utils,        only : rm
  use nml_design,        only : n_design_pts, model_variables

  integer,                intent(in)     :: n
  integer,                intent(in)     :: needf, needg

  real(dp), dimension(n), intent(inout)  :: x
  real(dp),               intent(out)    :: f
  real(dp), dimension(n), intent(inout)  :: g

  integer :: i, istop

  type(opt_data_type), dimension(n_design_pts) :: opt_data

continue

! Stop if user requested us to

  call se_chdir(snopt_ammo_directory)
  call check_for_stop(istop)
  if ( istop > 0 ) then
    call rm('stop.dat')
    write(*,*) 'User requested premature stop...'
    stop
  endif

! Unscale the problem

  call unscale_obj(n,x,g)

! Update rubber.data info

  do i = 1, n_design_pts
    call se_chdir(model_variables(i)%model_directory)
    opt_data(i)%allocated = .false.
    call load_optimization_data(opt_data(i),snopt_io,'SNOPT: Location 2')
    call set_design_variables(n_design_pts,i,opt_data(i),x)
    call unload_optimization_data(opt_data(i),snopt_io,'Top of OBJECTIVE')
  end do

! Compute objective function if we haven't already done it at this x

  if ( needf > 0 ) then

    f = 0.0_dp

    find_obj1 : do i = 1, n_design_pts
      if ( opt_data(i)%nobjectives > 0 ) then
        if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
          call se_chdir(model_variables(i)%model_directory)
          write(*,*) 'Analysis of Model ',i,'...'
          call perform_analysis(restart_flow(i),                               &
                                i, n_design_pts, snopt_what_to_do,             &
                                snopt_restart_optimization,                    &
                                desc_directory(i))
          call load_optimization_data(opt_data(i),snopt_io,'SNOPT: Location 3')
          last_flow_dvs(i,:) = x
        endif
      endif
    end do find_obj1

! Set the objective function value heading back into SNOPT

    call combine_mp_functions(f,opt_data)

  endif

! Compute gradient of objective function

  if ( needg > 0 ) then

    g = 0.0_dp

    find_obj3 : do i = 1, n_design_pts

      if ( opt_data(i)%nobjectives > 0 ) then

        if ( .not. been_there(n,x,last_adjoint_dvs(i,:),.false.) ) then

          call se_chdir(model_variables(i)%model_directory)

          if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
            write(*,*) 'Analysis of Model ',i,'...'
            call perform_analysis(restart_flow(i),                             &
                                  i, n_design_pts, snopt_what_to_do,           &
                                  snopt_restart_optimization,                  &
                                  desc_directory(i))
            last_flow_dvs(i,:) = x
          endif

          write(*,*) 'Sensitivity of Model ',i,'...'
          call perform_sensitivity_analysis(restart_dual(i),                   &
                                            i,n_design_pts,snopt_what_to_do,   &
                                            snopt_lss_flag)

          call load_optimization_data(opt_data(i),snopt_io,'SNOPT: Location 4')
          last_adjoint_dvs(i,:) = x

        endif

      endif

    end do find_obj3

! Set the gradient values heading back into SNOPT

    call combine_mp_gradients(n,g,opt_data)

  endif

! Free memory

  do i = 1, n_design_pts
    call free_optimization_data(opt_data(i))
  end do

! Scale the problem

  call scale_obj(n,x,g)

end subroutine objective


!============================== CONSTRAINTS ==================================80
!
!  Evaluates the nonlinear constraints and gradients for SNOPT
!
!  Be very careful to see if we have already been to this design point
!  with the flow solver and/or the adjoint solver
!
!  Before doing an adjoint, make sure the flow solution is at the correct
!  design point
!
!=============================================================================80
subroutine constraints( n, nf, neg, x, needf, f, needg, g )

  use kinddefs,           only : dp
  use design_types,       only : opt_data_type
  use designs,            only : load_optimization_data, set_design_variables, &
                                 unload_optimization_data, been_there,         &
                                 free_optimization_data
  use analysis,           only : perform_analysis
  use sensitivity,        only : perform_sensitivity_analysis
  use system_extensions,  only : se_chdir
  use utilities,          only : check_for_stop
  use file_utils,         only : rm
  use nml_design,         only : n_design_pts, model_variables

  integer,                    intent(in)    :: n, nf, neg
  integer,                    intent(in)    :: needf, needg

  real(dp), dimension(n),     intent(inout) :: x
  real(dp), dimension(nf),    intent(out)   :: f
  real(dp), dimension(neg),   intent(out)   :: g

  integer :: i, j, k, counter, istop

  type(opt_data_type), dimension(n_design_pts) :: opt_data

continue

! Stop if user requested us to

  call se_chdir(snopt_ammo_directory)
  call check_for_stop(istop)
  if ( istop > 0 ) then
    call rm('stop.dat')
    write(*,*) 'User requested premature stop...'
    stop
  endif

! Unscale the problem

  call unscale_con(n,nf,x,g)

! Update rubber.data info

  do i = 1, n_design_pts
    call se_chdir(model_variables(i)%model_directory)
    opt_data(i)%allocated = .false.
    call load_optimization_data(opt_data(i),snopt_io,'SNOPT: Location 5')
    call set_design_variables(n_design_pts,i,opt_data(i),x)
    call unload_optimization_data(opt_data(i),snopt_io,'Top of FUNCON')
  end do

! Compute constraint if we haven't already done it at this x

  if ( needf > 0 ) then

    find_con1 : do i = 1, n_design_pts
      if ( opt_data(i)%nconstraints > 0 ) then
        if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
          call se_chdir(model_variables(i)%model_directory)
          write(*,*) 'Analysis of Model ',i,'...'
          call perform_analysis(restart_flow(i),                               &
                                i, n_design_pts, snopt_what_to_do,             &
                                snopt_restart_optimization,                    &
                                desc_directory(i))
          call load_optimization_data(opt_data(i),snopt_io,'SNOPT: Location 6')
          last_flow_dvs(i,:) = x
        endif
      endif
    end do find_con1

! Set the constraint value heading back into SNOPT

    counter = 1                    ! First function is objective
    find_con2 : do i = 1, n_design_pts
      if ( opt_data(i)%nconstraints > 0 ) then
        do j = 1, opt_data(i)%nconstraints
          counter = counter + 1
          f(counter) = opt_data(i)%constraints(j)
        end do
      endif
    end do find_con2

  endif

! Compute gradient of constraint if we haven't already done it at this x

  if ( needg > 0 ) then

    find_con3 : do i = 1, n_design_pts

      if ( opt_data(i)%nconstraints > 0 ) then

        if ( .not. been_there(n,x,last_adjoint_dvs(i,:),.false.) ) then

          call se_chdir(model_variables(i)%model_directory)

          if ( .not. been_there(n,x,last_flow_dvs(i,:),.false.) ) then
            write(*,*) 'Analysis of Model ',i,'...'
            call perform_analysis(restart_flow(i),                             &
                                  i, n_design_pts, snopt_what_to_do,           &
                                  snopt_restart_optimization,                  &
                                  desc_directory(i))
            last_flow_dvs(i,:) = x
          endif

          write(*,*) 'Sensitivity of Model ',i,'...'
          call perform_sensitivity_analysis(restart_dual(i),                   &
                                            i,n_design_pts,snopt_what_to_do,   &
                                            snopt_lss_flag)

          call load_optimization_data(opt_data(i),snopt_io,'SNOPT: Location 7')
          last_adjoint_dvs(i,:) = x

        endif

      endif

    end do find_con3

! Set the gradient values heading back into SNOPT

    counter = n                    ! First function is objective
    g(n+1:neg) = 0.0_dp
    find_con4 : do i = 1, n_design_pts
      if ( opt_data(i)%nconstraints > 0 ) then
        do j = 1, opt_data(i)%nconstraints
          do k = 1, opt_data(i)%ndv
            if ( opt_data(i)%coupled(k) ) then
              counter = counter + 1
              g(counter) = opt_data(i)%constraint_gradients(j,k)
            else  ! Embed zero gradients to variables of other models
              counter = counter + i
              g(counter) = opt_data(i)%constraint_gradients(j,k)
              counter = counter + (n_design_pts-i)
            endif
          end do
        end do
      endif
    end do find_con4

  endif

! Free memory

  do i = 1, n_design_pts
    call free_optimization_data(opt_data(i))
  end do

! Scale the problem

  call scale_con(n,nf,x,g)

end subroutine constraints

!============================== UNSCALE_OBJ ==================================80
!
!  Unscales objective stuff for SNOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_obj(n,x,g)

    use kinddefs,   only : dp

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x, g

  continue

! unscale the DV's

    x(1:n) = d(1:n)*x(1:n) + c(1:n)

! unscale the gradient

    g(1:n) = g(1:n)/d(1:n)

  end subroutine unscale_obj


!============================== SCALE_OBJ ====================================80
!
!  Scales objective stuff for SNOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_obj(n,x,g)

    use kinddefs,   only : dp

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x, g

  continue

! scale the DV's

    x(1:n) = (x(1:n)-c(1:n)) / d(1:n)

! scale the gradient

    g(1:n) = d(1:n)*g(1:n)

  end subroutine scale_obj


!============================== UNSCALE_CON ==================================80
!
!  Unscales constraint stuff for SNOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_con(n,nf,x,g)

    use kinddefs,   only : dp

    integer, intent(in) :: n, nf

    real(dp), dimension(n),     intent(inout) :: x
    real(dp), dimension(nf*n),  intent(inout) :: g

    integer :: i
!   integer :: j, counter
    integer :: rbeg, rend

  continue

! unscale the DV's

    x(1:n) = d(1:n)*x(1:n) + c(1:n)

! unscale the constraint gradients

    rbeg = n + 1
    do i = 2, nf
      rend = rbeg + n - 1
      g(rbeg:rend) = g(rbeg:rend)/d(1:n)
      rbeg = rend + 1
    end do

!   counter = n
!   do i = 2, nf
!     do j = 1, n
!       counter = counter + 1
!       g(counter) = g(counter)/d(j)
!     end do
!   end do
  end subroutine unscale_con


!============================== SCALE_CON ====================================80
!
!  Scales constraint stuff for SNOPT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_con(n,nf,x,g)

    use kinddefs,   only : dp

    integer, intent(in) :: n, nf

    real(dp), dimension(n),     intent(inout) :: x
    real(dp), dimension(nf*n),  intent(inout) :: g

    integer :: i
!   integer :: j, counter
    integer :: rbeg, rend

  continue

! scale the DV's

    x(1:n) = (x(1:n)-c(1:n)) / d(1:n)

! scale the constraint gradients

    rbeg = n + 1
    do i = 2, nf
      rend = rbeg + n - 1
      g(rbeg:rend) = d(1:n)*g(rbeg:rend)
      rbeg = rend + 1
    end do

!   counter = n
!   do i = 2, nf
!     do j = 1, n
!       counter = counter + 1
!       g(counter) = d(j)*g(counter)
!     end do
!   end do

  end subroutine scale_con


!============================== SNOPT_USRFUN =================================80
!
!  Provides the external interface to the function and gradients for SNOPT
!
!  Be very careful to see if we have already been to this design point
!  with the flow solver and/or the adjoint solver
!
!  Before doing an adjoint, make sure the flow solution is at the correct
!  design point
!
!=============================================================================80
  subroutine usrfun( status, n, x, needf, nf, f, needg, neg, g,                &
                     cu, lencu, iu, leniu, ru, lenru )

    use kinddefs,          only : dp
    use system_extensions, only : se_flush

    integer, intent(in)    :: status
    integer, intent(in)    :: n                         ! Num DV
    integer, intent(in)    :: needf, nf                 ! Num functions
    integer, intent(in)    :: needg, neg                ! Num derivatives
    integer, intent(in)    :: lencu, leniu, lenru

    real(dp), dimension(n),             intent(inout) :: x
    real(dp), dimension(nf),            intent(inout) :: f
    real(dp), dimension(neg),           intent(inout) :: g      ! g = g(nf,n)

    character(len=8), dimension(lencu), intent(in)    :: cu
    integer, dimension(leniu),          intent(in)    :: iu
    real(dp), dimension(lenru),         intent(in)    :: ru

  continue

    if ( .false. ) write(*,*) cu(1), iu(1), ru(1)

    if ( status >= 2 ) return

    call objective( n, x, needf, f(1), needg, g )
    call constraints( n, nf, neg, x, needf, f, needg, g )

    call se_flush(print_file)
    call se_flush(sum_file)

!   if( stop.dat ) then
!     return -2
!   endif

  end subroutine usrfun

end module snopt

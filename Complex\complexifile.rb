#!/usr/bin/env ruby

require 'fileutils'
include FileUtils

##
# Files that should not be complexified

SKIP_FILES = %w[ allocations.f90
                 complex_functions.f90
                 complex_oldschool.f90 
                 comprow.f90
                 engine_interface.F90
                 exact_airfoil.f90
                 file_utils.f90
                 invert_lapack.f90
                 knife_interface.F90
                 kinddefs.f90
                 lmpi.F90
                 lmpi_app.F90
                 load_balance.f90
                 meshsim_grid_adapter.F90
                 meshsim_interface.f90
                 refine_grid_adapter.F90
                 refine_interface.f90
                 solution_io_helpers.f90
                 system_extensions.F90 
                 unravel_interface.F90 
                 debug_output.f90 ]

##
# Intrinsic functions that have a complex interface

INTRINSICS = %w( abs acos asin atan atan2
                 ceiling cosh cpu_time dim log10
                 max maxloc maxval min minloc minval nint
                 random_number sign tan tanh )

##
# Pattern for finding Fortran scoping units

PROGRAM_UNITS = "(module|subroutine|function|program)"

##
# Output directory
outdir = "./"
prefix = ""

##
# Routine-level manipulations (the heart of complexification).
#
# The FileHandler class breaks up a file into "routines",
# which are largely subroutines and functions.

class Routine < Array

  def complexify
    convert_intrinsics.
    change_real_declaration_to_complex.
    complex_interface.
    set_complex_mode_flag_true
    make_complex_copy.
    temp_grid_is_real
  end

  def change_real_declaration_to_complex
    make_grid_real_types
    replace_real_with_complex
  end

  def complex_interface
    make_complex_interface
    rename_grid_type_to_grid_real_type
  end

  def mark_never_complex
    map! do |line|
      line.sub( /(\w*grid_)type(\W)/, '\1real_type\2' ).
           sub( /(begin|end)NeverComplex/i, '' )
    end
    insert_after_first_statement( '!beginNeverComplex' ).push "!endNeverComplex\n"
  end

  def set_complex_mode_flag_true
    map! do |line|
      line.sub( /complex_mode\s*=\s*\.false\./i, 'complex_mode=.true.' )
    end
  end

  def temp_grid_is_real
    map! do |line|
      line.sub( /grid_type(.*temp_grid)/i, 'grid_real_type\1' )
    end
  end

  private
  
  def convert_intrinsics
    convert_intrinsics_helper
    use_complex_module
  end

  def make_complex_copy
    copy_and_replace( /beginMakeComplexCopy/, /endMakeComplexCopy/ ) do |buffer|
      buffer.map{ |line|
        line.gsub( /real\s*\(\s*(.*?),\s*(dp|jp|mp|odp|dqp|krp|r4|r8|rk).*?\)/i,
                   'aimag(\1)' ).
              sub( /filename\s*=(.+)$/i,
                   'filename=\1 // \'_imaginary\'')
      }.join
    end
  end

  def replace_real_with_complex
    complexify = true
    each do |line|
      complexify = false  if line.include? 'beginNeverComplex'
      line.sub!( /^(\s*)real\s*\(\s*(dp|jp|mp|odp|dqp|krp|r4|r8|rk)/i,
                 '\1complex(\2' )  if complexify
      complexify = true  if line.include? 'endNeverComplex'
    end
  end 

  def make_complex_interface
    copy_and_replace( /beginMakeComplexInterface/,
                      /endMakeComplexInterface/ ) do |buffer|
      line = ''
      buffer.join.scan( /::\s*(\w+)/ ) do |routine|
        routine_name = routine.first
        line << "interface #{routine_name}\n"
        line << "module procedure #{routine_name}, #{routine_name}_complex\n"
        line << "end interface\n"
      end
      line
    end
  end

  def rename_grid_type_to_grid_real_type
    map! do |line|
      line.sub( /^(\s*Public\s*::\s*\w*grid)(_type\W*)/i,
                '\1\2\1_real\2' ).
           sub( /(only\s*:\s*)(\w*grid)(_type)\!complex_needs_real_version_too/i,
                '\1\2\3, \2_real\3' )
    end
  end

  def convert_intrinsics_helper
    return self  if first.match( /beginNeverComplex/ )
    INTRINSICS.each do |intrinsic|
      complexify = true
      map! do |line|
        complexify = false  if line.include? 'beginNeverComplex'
        line.gsub!( /(\W)(#{intrinsic}\s*\()/i, '\1cc\2' ) if complexify
        complexify = true  if line.include? 'endNeverComplex'
        line
      end
    end
  end

  def use_complex_module
    return self  if first.match( /beginNeverComplex/ )
    keywords = {}
    INTRINSICS.map{ |i| "cc#{i}" }.each{ |ci| keywords[ci] = ci }
    {'>'=>'.gt.', '<'=>'.lt.', '<='=>'.le.', '>='=>'.ge.'}.each do |k,v|
      keywords[".*#{k}.*"] =
        keywords[v.gsub(/(\.)(\w\w)(\.)/,'.*\.\2\..*')] = "operator(#{k})"
    end
    use_buffer = []
    keywords.each do |k,v|
      matches = grep( /\W#{k}\W/i ).delete_if do |e|
        e.index(/!/) and ( e.index(/!/) < e.index( /#{k.gsub(/\.\*/,'')}/i ) )
      end
      use_buffer.push v  unless matches.empty?
    end
    unless use_buffer.empty?
      use_buffer.uniq!
      unless first.match( /#{PROGRAM_UNITS}/i )
        raise RuntimeError, "Routine does not start with #{PROGRAM_UNITS}\n" <<
        to_s << "\nUse Buffer:\n" << use_buffer.to_s
      else
        insert_after_first_statement '  use complex_functions, only:' +
        " #{use_buffer.join(',')}"
      end
    end
    self
  end

  def make_grid_real_types
    copy_and_replace( /^\s*type\s+\w*grid_type/,
    /^\s*end\s+type\s+\w*grid_type/ ) do |buffer|
      buffer.unshift "!beginNeverComplex\n"
      buffer.push "!endNeverComplex\n"
      buffer.map{ |l| l.gsub( /(\w*grid)(_type)/, '\1_real\2' ) }.join
    end
  end

  def insert_after_first_statement( text )
    detect{ |line| line.concat( text+"\n" )  unless line.match( /(^\s*!|\&)/ ) }
    self
  end

  def copy_and_replace( begin_delimiter, end_delimiter )
    in_block = false
    buffer = []
    each do |line|
      in_block = true  if line.match begin_delimiter
      if in_block
        buffer.push line
        if line.match end_delimiter
          in_block = false
          line.concat yield( buffer )
          buffer.clear
        end
      end
    end
  end

  def insert_text_before_mark( text, mark )
    map do |line|
      line.replace( text << "\n" << line )  if line.match mark
    end
  end

  def insert_text_after_mark( text, mark )
    map do |line|
      line.concat( text << "\n" )  if line.match mark
    end
  end

end # class Routine

##
# Splits files up and hands them to Routines for processsing

class FileHandler < Array

  def initialize( filename=nil )
    @file_contents = IO.readlines filename  if filename
  end

  def complexify
    split_into_routines
    make_complex_version_of_routines.map{ |r| r.complexify }.flatten
  end

  private

  def insert_after_first_statement(text)
    @file_contents.each do |line|
      next  if line.match( /(^\s*!|\&|^\s*\n)/ )
      line = line.concat( text << "\n" )
      break
    end
  end

  def make_complex_version_of_routines
    pattern = /^(\s*(|end)\s*subroutine\s+)(#{routines_to_copy.join("|")})(\W)/
    duplicates = []
    each do |r|
      unless r.grep(pattern).empty?
        duplicates.push(  
          Routine.new.replace(
            r.map do |l| 
              if l.match( /^\s*end\s+module/i )
                l = ''
              else
                l.sub( pattern,'\1\3_complex\4' )
              end
            end
          )
        )
        duplicates.push r.mark_never_complex
      else
        duplicates.push r
      end
    end
    self.replace duplicates
  end

  def routines_to_copy
    duplicate = []
    @file_contents.join.
    scan(/beginMakeComplexInterface(.*)endMakeComplexInterface/im) do |publics|
      duplicate.replace publics.join.scan( /::\s*(\w+)/ ).flatten
    end
    duplicate
  end

  def split_into_routines
    buffer, complexify, prev_line = [], true, ''
    @file_contents.each do |line|
      if prev_line !~ /^\s*(?!(!|'|"))\s*interface/i and
         line =~ /^\s*(?!(end|!|'|"))(|\S+\s+)#{PROGRAM_UNITS}\s+(?!procedure)/i then
        push buffer.dup
        complexify = false  if buffer.last =~ /beginNeverComplex/i
        buffer.clear
        buffer.push "!beginNeverComplex\n"  unless complexify
        complexify = true
        prev_line = ''
      end
      prev_line = line  unless line.empty? or line.match( /^\s*(!|'|")/ )
      buffer.push line
    end
    push(buffer).delete_if{|r| r.empty?}.map!{|r| Routine.new.replace(r)}
  end

end # class FileHandler

##
#  Executable script

if __FILE__ == $0

  require 'optparse'

  opts = OptionParser.new do |opts|
    opts.banner = "Usage: complexifile.rb [options] file"

    opts.separator ""
    opts.separator "Specific options:"

    opts.on("-o","--out=MANDATORY","Directory for output") do |o|
      outdir = o || "./"
    end
    opts.on("-p","--prefix PREFIX","Prefix for output files") do |p|
      prefix = p || ""
    end
  end

  opts.parse!(ARGV)

  source_file = ARGV.first
  file = File.basename(source_file)
  output = "#{outdir}/#{prefix}#{file}"

  if SKIP_FILES.include?( file ) then
    #$stdout.print "Copying       #{source_file} ... "
    $stdout.flush
    `cp #{source_file} #{output}`
    #$stdout.puts "complete."
    $stdout.flush

  else
    #$stdout.print "Complexifying #{source_file} ... "
    $stdout.flush
    File.open(output,'w') do |current|
      current.puts "! Note: autogenerated by #$0"
      current.puts "!       from #{source_file}"
      current.puts "!       on #{Time.now}\n\n"
      current.puts FileHandler.new(source_file).complexify
    end
    #$stdout.puts "complete."
    $stdout.flush

    ##
    # Special file handling
    # modify files to use complex_oldschool::readme_oldschool instead of
    # io::readme

    case file
    when "flow.F90"
      new_file_contents = File.read(output).
        sub( /use io,\s*only\s*:\s*readme/,
             'use complex_oldschool, only: readme   =>readme_oldschool' )
      File.open(output,'w'){ |f| f.puts new_file_contents }
    end

  end

end

module dshape

  use kinddefs, only : dp

  implicit none

  private

  public :: sensshape, write_column_sum, write_column_sum_dynamic
  public :: column_sum_initial_state, dvol_dgrid

  integer :: n_stored_planes

  type stored_planes_type
    integer :: residual_timestep  ! What residual is linearized
    integer :: grid_timestep      ! What grid the linearization is wrt

    real(dp), dimension(:,:,:), pointer :: drdxl  ! dR/dX (spatial+GCL)
  end type stored_planes_type

  type(stored_planes_type), dimension(:), allocatable :: stored_planes

contains


!================================ SENSSHAPE ==================================80
!
!  This routine gets derivatives wrt shape
!
!=============================================================================80
  subroutine sensshape(grid,crow,soln,sadj,design,getg,ntt)

    use design_types,       only : design_type, cpstar_data
    use designs,            only : find_window_value, pstag_obj
    use info_depr,          only : physical_timestep
    use nml_nonlinear_solves,            only : itime
    use nml_noninertial_reference_frame, only : noninertial
    use nml_global,         only : moving_grid
    use kinddefs,           only : dp
    use bc_names,           only : bc_has_skin_friction,                       &
                                   bc_used_for_force_calculation,              &
                                   bc_is_flow_through
    use thermo,             only : ptoe, etop
    use reconstruction,     only : lstgs
    use lmpi,               only : lmpi_die
    use lmpi_app,           only : lmpi_xfer
    use dshape_cost,        only : pressurexyz, pressurexyzi, skinfricxyz,     &
                                   skinfricxyzi, dfdx_cpstar
    use dshape_residual,    only : drdgeom, dcoriolis
    use forces,             only : timestep_contributes, boom_targ_id,         &
                                   sboom_id, pstag_id, ae_id, mass_flow_id,    &
                                   tpr_rat_id, ad_eff_id
    use grid_types,         only : grid_type
    use comprow_types,      only : crow_type
    use solution_types,     only : soln_type, compressible, incompressible
    use solution_adj,       only : sadj_type
    use solution_getg,      only : getg_type
    use crinkle_cut,        only : ray_functions, sboom_dtdx
    use adjoint_switches,   only : sonic_boom_cost, windowing, pstag_cost,     &
                                   ae_cost
    use fun3d_constants,    only : my_0
    use nml_overset_data,   only : overset_flag
    use equiv_area,         only : ae_obj
    use internal_flow_fcns, only : mass_flow_obj, tpr_adeff_obj

    integer,           intent(in   ) :: ntt
    type(crow_type),   intent(in   ) :: crow
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in   ) :: design
    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln
    type(getg_type),   intent(inout) :: getg

    integer :: ib, i, k, rn, np, ae_fcn, bound_id

    real(dp) :: boom_value, weight, target, power, factorboom, factor

    real(dp), dimension(6,3) :: pointsum

    logical :: apply_dfdx, sboom_present

    logical, parameter :: debug = .false.

    character(10) :: name

  continue

    getg%dfdx  = 0.0_dp
    getg%drdxl = 0.0_dp

    if ( soln%eqn_set == compressible ) then
      call etop( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
    end if

    call lstgs( soln%viscous_method,                                           &
                grid%nnodes0,grid%nnodes01,grid%nedgeloc,grid%eptr,            &
                grid%symmetry,soln%q_dof,                                      &
                soln%gradx,soln%grady,soln%gradz,grid%x,grid%y,grid%z,         &
                grid%r11,grid%r12,grid%r13,grid%r22,grid%r23,grid%r33,         &
                soln%n_tot, soln%n_grd, soln%turb, soln%n_turb,                &
                soln%eqn_set, soln%ndim )

    call lmpi_xfer(soln%gradx)
    call lmpi_xfer(soln%grady)
    call lmpi_xfer(soln%gradz)

    cost_function_contribution: do ib = 1, grid%nbound

      if ( bc_is_flow_through( grid%bc(ib)%ibc ) ) cycle

      if ( bc_used_for_force_calculation( grid%bc(ib)%ibc ) ) then

        select case ( soln%eqn_set )
        case(compressible)
          call pressurexyz( grid%nnodes01,grid%x,grid%y,grid%z,soln%q_dof,     &
                            grid%bc(ib)%nbnode,grid%bc(ib)%nbfacet,            &
                            grid%bc(ib)%nbfaceq, grid%bc(ib)%face_bit,         &
                            grid%bc(ib)%face_bitq, grid%bc(ib)%ibnode,         &
                            grid%bc(ib)%f2ntb, grid%bc(ib)%f2nqb, design,      &
                            soln%totforce(ntt), crow%ia,crow%ja,getg%dfdx,     &
                            crow%nnz01,ib,grid%nbound,soln%bcforce,soln%ndim )
        case(incompressible)
          call pressurexyzi( grid%nnodes01,grid%x,grid%y,grid%z,soln%q_dof,    &
                             grid%bc(ib)%nbnode,grid%bc(ib)%nbfacet,           &
                             grid%bc(ib)%face_bit,grid%bc(ib)%ibnode,          &
                             grid%bc(ib)%f2ntb,design,soln%totforce(ntt),      &
                             crow%ia,crow%ja,getg%dfdx,crow%nnz01,ib,          &
                             grid%nbound,soln%bcforce,soln%ndim,               &
                             grid%bc(ib)%nbfaceq,grid%bc(ib)%face_bitq,        &
                             grid%bc(ib)%f2nqb )
        end select

        if ( bc_has_skin_friction( grid%bc(ib)%ibc ) ) then
          select case ( soln%eqn_set )
          case(compressible)
            call skinfricxyz( grid%nnodes01,grid%x,grid%y,grid%z,soln%q_dof,   &
                              soln%amut,grid%bc(ib)%nbnode,                    &
                              grid%bc(ib)%nbfacet,grid%bc(ib)%face_bit,        &
                              grid%bc(ib)%ibnode,grid%bc(ib)%f2ntb,design,     &
                              soln%totforce(ntt),crow%ia,crow%ja,              &
                              getg%dfdx,crow%nnz01,ib,                         &
                              grid%nbound,soln%bcforce,soln%ndim,grid%nelem,   &
                              grid%elem, grid%bc(ib)%nbfaceq,                  &
                              grid%bc(ib)%face_bitq, grid%bc(ib)%f2nqb )
          case(incompressible)
            call skinfricxyzi(grid%nnodes01,grid%x,grid%y,grid%z,soln%q_dof,   &
                              soln%amut,grid%bc(ib)%nbnode,                    &
                              grid%bc(ib)%nbfacet,grid%bc(ib)%face_bit,        &
                              grid%bc(ib)%ibnode,grid%bc(ib)%f2ntb,design,     &
                              soln%totforce(ntt),crow%ia,crow%ja,              &
                              getg%dfdx,crow%nnz01,ib,                         &
                              grid%nbound,soln%bcforce,soln%ndim,grid%nelem,   &
                              grid%elem, grid%bc(ib)%nbfaceq,                  &
                              grid%bc(ib)%face_bitq, grid%bc(ib)%f2nqb )
          end select
        endif

      endif

    end do cost_function_contribution

! Pick up any sonic boom pieces if requested

    boom_contributions : if ( sonic_boom_cost ) then

      if ( soln%eqn_set == compressible ) then
        call ptoe( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

! Do any SBOOM function

      sboom_present = .false.

      f_loop : do i = 1, design%nfunctions
        do k = 1, design%function_data(i)%ncomponents
          name = design%function_data(i)%component_data(k)%name
          if ( trim(name) == sboom_id ) then
            call sboom_dtdx( grid,soln,design%nfunctions,getg%drdxl,i,getg%ntp )
            sboom_present = .true.
            exit f_loop
          endif
        end do
      end do f_loop

! First we have to get the current value of the boom function, since
! our cost function formula needs it:  weight*(boom-boom*)**p.
!                                              ^^^^
! (With lift and drag, we form and store them at the very start of execution,
!  which is why we don't see them double-computed like this in this routine)

      if ( .not. sboom_present ) then  ! boom_targ must be present

        call ray_functions(grid,soln,design%nfunctions,1,                      &
                           cost_to_return=boom_value)

! Now that we know the current value of the boom function, we can
! construct the boom linearization for the adjoint RHS

        fcn_loop : do i = 1, design%nfunctions

          component_loop : do k = 1, design%function_data(i)%ncomponents

            weight  = design%function_data(i)%component_data(k)%weight
            target  = design%function_data(i)%component_data(k)%target
            power   = design%function_data(i)%component_data(k)%power

            factorboom = weight*power*(boom_value - target)**(power-1.0_dp)

            name = design%function_data(i)%component_data(k)%name

            if ( trim(name) == boom_targ_id ) then
              call ray_functions(grid,soln,design%nfunctions,crow%nnz01,       &
                                 factor=factorboom,fcn_ind=i,ia=crow%ia,       &
                                 ja=crow%ja,dfdx=getg%dfdx)
            endif

          end do component_loop

        end do fcn_loop

      endif

      if ( soln%eqn_set== compressible ) then
        call etop( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

    endif boom_contributions

! Pick up target pressure distribution function

    if ( cpstar_data ) then
      call dfdx_cpstar( soln%eqn_set,grid%nnodes0,grid%nnodes01,grid%x,grid%z, &
                        grid%l2g,design,soln%q_dof,soln%n_tot,crow%ia,crow%ja, &
                        getg%dfdx,crow%nnz01)
    endif

! Stagnation pressure function contributions

    if ( pstag_cost ) then

      if ( soln%eqn_set == compressible ) then
        call ptoe( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

      fcn_loop2 : do i = 1, design%nfunctions
        component_loop2 : do k = 1, design%function_data(i)%ncomponents

          weight  = design%function_data(i)%component_data(k)%weight
          target  = design%function_data(i)%component_data(k)%target
          power   = design%function_data(i)%component_data(k)%power
          name    = design%function_data(i)%component_data(k)%name

          if ( trim(name) == pstag_id ) then
            call pstag_obj(grid%nedgeloc,grid%nnodes0,grid%eptr,grid%l2g,      &
                           grid%x,grid%y,grid%z,soln%q_dof,weight,target,power,&
                           dfdx=getg%dfdx,ifcn=i,ia=crow%ia,ja=crow%ja)
          endif

        end do component_loop2
      end do fcn_loop2

      if ( soln%eqn_set == compressible ) then
        call etop( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

    endif

! Equivalent area function contributions

    if ( ae_cost ) then

      ae_fcn = 0

      if ( soln%eqn_set == compressible ) then
        call ptoe( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

      fcn_loop3 : do i = 1, design%nfunctions
        component_loop3 : do k = 1, design%function_data(i)%ncomponents

          weight  = design%function_data(i)%component_data(k)%weight
          target  = design%function_data(i)%component_data(k)%target
          power   = design%function_data(i)%component_data(k)%power
          name    = design%function_data(i)%component_data(k)%name

          if ( trim(name) == ae_id ) then
            ae_fcn = ae_fcn + 1
            call ae_obj(grid,soln,ae_fcn,weight,target,power,n=i,ia=crow%ia,   &
                        ja=crow%ja,dfdx=getg%dfdx)
          endif

        end do component_loop3
      end do fcn_loop3

      if ( soln%eqn_set == compressible ) then
        call etop( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

    endif

! Internal flow function contributions

    fcn_loop4 : do i = 1, design%nfunctions
      component_loop4 : do k = 1, design%function_data(i)%ncomponents

        weight   = design%function_data(i)%component_data(k)%weight
        target   = design%function_data(i)%component_data(k)%target
        power    = design%function_data(i)%component_data(k)%power
        name     = design%function_data(i)%component_data(k)%name
        bound_id = design%function_data(i)%component_data(k)%boundary_id

        if ( trim(name) == mass_flow_id ) then

          if ( soln%eqn_set == compressible ) then
            call ptoe(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)
          endif

          call mass_flow_obj(grid,soln,bound_id,weight,target,power,ifcn=i,    &
                             ia=crow%ia,ja=crow%ja,dfdx=getg%dfdx)

          if ( soln%eqn_set == compressible ) then
            call etop(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)
          endif

        endif

        if ( trim(name) == tpr_rat_id .or. trim(name) == ad_eff_id ) then

          if ( soln%eqn_set == compressible ) then
            call ptoe(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)
          endif

          call tpr_adeff_obj(grid,soln,weight,target,power,name,ifcn=i,        &
                             ia=crow%ia,ja=crow%ja,dfdx=getg%dfdx)

          if ( soln%eqn_set == compressible ) then
            call etop(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)
          endif

        endif

      end do component_loop4
    end do fcn_loop4

! Determine if we want each cost function contribution

    do k = 1, design%nfunctions
      apply_dfdx = .false.
      if ( itime == 0 ) then
        apply_dfdx = .true.
      else
        apply_dfdx = timestep_contributes( design%function_data(k)%timesteps,  &
                                           physical_timestep)
      endif
      if ( .not. apply_dfdx ) getg%dfdx(:,:,k) = my_0

! Find any windowing factor

      if ( windowing ) then
        call find_window_value(design%function_data(k)%timesteps,              &
                               physical_timestep,factor)
        getg%dfdx(:,:,k) = factor*getg%dfdx(:,:,k)
      endif

    end do

!  Now get the residual piece

    rn = 0
    np = 0
    if ( debug ) then
      write(*,*) 'Enter res node'
      read(*,*) rn
      write(*,*) 'Enter perturbed node'
      read(*,*) np
      write(*,*) 'res     node in global numbering: ', grid%l2g(rn)
      write(*,*) 'perturb node in global numbering: ', grid%l2g(np)
    endif

    pointsum = 0.0_dp
    call drdgeom( soln%eqn_set,getg%ntp,                                       &
                  grid%nnodes0,grid%nnodes01,                                  &
                  grid%nedge,grid%nedgeLoc,soln%gradx,soln%grady,soln%gradz,   &
                  grid%eptr,soln%q_dof,grid%x,grid%y,grid%z,grid%xn,grid%yn,   &
                  grid%zn,grid%ra,sadj%rlam,                                   &
                  soln%amut,crow%ia,crow%ia_ns,crow%ja,                        &
                  sadj%coltag,                                                 &
                  grid%slen,soln%turb,grid%vol,soln%dft1,                      &
                  soln%dft2,grid%nbound,grid%bc,                               &
                  design%nfunctions,grid,grid%iflagslen,                       &
                  getg%sourceterm_sum,grid%l2g,grid%nedgeloc_2d,               &
                  grid%nnodes0_2d,grid%node_pairs_2d,                          &
                  grid%nelem,grid%elem,soln%phi,                               &
                  soln%ndim, soln%adim, soln%n_turb, soln%n_tot, soln%n_grd,   &
                  grid%facespeed,grid%dxdt,grid%dydt,grid%dzdt,getg%drdxl,     &
                  pointsum,rn,np,debug,soln%n_q,grid%symmetry,grid%r11,        &
                  grid%r12,grid%r13,grid%r22,grid%r23,grid%r33)

! Pick up any Coriolis linearizations for noninertial cases

    if ( noninertial ) call dcoriolis(design,sadj,soln,getg,grid%nelem,        &
                                      grid%nnodes0,grid%nedgeloc,grid%nbound,  &
                                      grid%elem,grid%x,grid%y,grid%z,grid%bc,  &
                                      soln%eqn_set)

    if ( debug ) then
      do i = 1, 6
        write(*,'(a,3(e22.15,1x))') 'After dcoriolis = ', pointsum(i,1:3)
      end do
      call lmpi_die
    endif

! Finally, if time-dependent, we need to pick up the d(vol)/dX and GCL pieces

    if ( itime > 0 ) then
      if ( soln%eqn_set == compressible ) then
        call ptoe( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif
      call dvol_dgrid(crow,design,sadj,soln,getg,grid%nelem,grid%nnodes0,      &
                      grid%nedgeloc,grid%nbound,grid%elem,grid%x,grid%y,grid%z,&
                      .false.,grid%bc)
      if ( moving_grid ) call dgcl_dgrid(grid,soln,sadj,design,getg)
      if ( soln%eqn_set == compressible ) then
        call etop( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
      endif
    endif

    if ( soln%eqn_set == compressible ) then
      call ptoe( size(soln%q_dof,2), soln%q_dof, soln%n_tot, soln%eqn_set )
    endif

! Pick up any overset terms

    if ( overset_flag ) then
      call linearize_overset_weights(grid,soln,sadj,getg,design)
    endif

    if ( debug ) call lmpi_die

  end subroutine sensshape


!==================== LINEARIZE_OVERSET_WEIGHTS ==============================80
!
!  Linearize the overset weighting coefficients wrt xyz coords
!
!=============================================================================80
  subroutine linearize_overset_weights(grid,soln,sadj,getg,design)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type, compressible, incompressible
    use solution_adj,   only : sadj_type
    use dirtlib,        only : nreceptors, receptors, tet_jacobian, invert_3x3,&
                               pyr_jacobian, prz_jacobian, hex_jacobian
    use lmpi,           only : lmpi_nproc, lmpi_id, lmpi_alltoall, lmpi_die,   &
                               lmpi_alltoallv
    use solution_getg,  only : getg_type
    use kinddefs,       only : dp
    use design_types,   only : design_type

    type(grid_type),   intent(in) :: grid
    type(soln_type),   intent(in) :: soln
    type(sadj_type),   intent(in) :: sadj
    type(design_type), intent(in) :: design
    type(getg_type),   intent(inout) :: getg

    integer :: i, receptor, k, n, m
    integer :: recp_total_length, rank, j
    integer :: donr_total_length, donor, item

    integer, dimension(:), allocatable :: recp_mess_length
    integer, dimension(:), allocatable :: recp_mess_start
    integer, dimension(:), allocatable :: recp_donors
    integer, dimension(:), allocatable :: donr_mess_length
    integer, dimension(:), allocatable :: donr_donors
    integer, dimension(:), allocatable :: recp_mess_length1
    integer, dimension(:), allocatable :: recp_mess_length2
    integer, dimension(:), allocatable :: recp_mess_length3

    real(dp) :: rlam1,rlam2,rlam3,rlam4,rlam5,rlam6

    real(dp), dimension(8,9,3)                 :: dwdx
    real(dp), dimension(3)                     :: greek,dwdg,b
    real(dp), dimension(3)                     :: r0,r1,r2,r3,r4,r5,r6,r7
    real(dp), dimension(3,3)                   :: jacobian, jac_inv
    real(dp), dimension(8)                     :: lx,ly,lz
    real(dp), dimension(8,6)                   :: q
    real(dp), dimension(3,8,design%nfunctions) :: overset_terms

    real(dp), dimension(:,:),   allocatable :: recp_xyzq
    real(dp), dimension(:,:),   allocatable :: donr_xyzq
    real(dp), dimension(:,:,:), allocatable :: recp_contribs
    real(dp), dimension(:,:,:), allocatable :: donr_contribs

  continue

    getg%overset_terms = 0.0_dp

    rlam1 = 0.0_dp ! avoid compiler warning
    rlam2 = 0.0_dp ! avoid compiler warning
    rlam3 = 0.0_dp ! avoid compiler warning
    rlam4 = 0.0_dp ! avoid compiler warning
    rlam5 = 0.0_dp ! avoid compiler warning
    rlam6 = 0.0_dp ! avoid compiler warning

! count up how many off-processor donors there are

    allocate(recp_mess_length(lmpi_nproc))
    recp_total_length = 0
    recp_mess_length  = 0

    nonlocal_fringe_donors : do i = 1, nreceptors
      count_mess_lengths : do j = 1, receptors(i)%ndonors
        rank  = receptors(i)%donor_ranks(j)
        if ( rank /= lmpi_id ) then
          recp_mess_length(rank+1) = recp_mess_length(rank+1) + 1
          recp_total_length = recp_total_length + 1
        endif
      end do count_mess_lengths
    end do nonlocal_fringe_donors

    allocate(recp_donors(recp_total_length))
    allocate(recp_mess_start(lmpi_nproc))

    recp_mess_start(1) = 1

! create CRS of where the donor ranks come from

    do rank = 0, lmpi_nproc-2
      recp_mess_start(rank+2) = recp_mess_start(rank+1)+recp_mess_length(rank+1)
    end do

! fill up the CRS

    recp_mess_length = 0
    nonlocal_fringe_donors2 : do i = 1, nreceptors
      count_mess_lengths2 : do j = 1, receptors(i)%ndonors
        donor = receptors(i)%donor_indices(j)
        rank  = receptors(i)%donor_ranks(j)
        if ( rank /= lmpi_id ) then
          item = recp_mess_start(rank+1) + recp_mess_length(rank+1)
          recp_donors(item) = donor
          recp_mess_length(rank+1) = recp_mess_length(rank+1) + 1
        endif
      end do count_mess_lengths2
    end do nonlocal_fringe_donors2

    allocate(donr_mess_length(0:lmpi_nproc-1))

! share CRS with others

    call lmpi_alltoall( recp_mess_length, donr_mess_length )

! off-processors fill up donor data to return
! make their own CRS

    donr_total_length = 0
    do rank = 0, lmpi_nproc-1
      donr_total_length = donr_total_length + donr_mess_length(rank)
    end do

    allocate(donr_donors(donr_total_length))
    allocate(donr_xyzq(3+soln%n_tot+soln%n_turb,donr_total_length))

! off-processors are receiving list of donor-receivers

    call lmpi_alltoallv( recp_donors, recp_mess_length,                        &
                         donr_donors, donr_mess_length )

! fill up the CRS bins with the data

    do i = 1, donr_total_length
      donor = donr_donors(i)
      donr_xyzq(1,i) = grid%x(donor)
      donr_xyzq(2,i) = grid%y(donor)
      donr_xyzq(3,i) = grid%z(donor)
      do j = 1, soln%n_tot
        donr_xyzq(3+j,i) = soln%q_dof(j,donor)
      end do
      do j = 1, soln%n_turb
        donr_xyzq(3+soln%n_tot+j,i) = soln%turb(j,donor)
      end do
    end do

    allocate(recp_xyzq(3+soln%n_tot+soln%n_turb,recp_total_length))

! data goes back to receptors

    call lmpi_alltoallv( donr_xyzq, donr_mess_length,                          &
                         recp_xyzq, recp_mess_length )

! do the interpolations

    allocate(recp_contribs(3,design%nfunctions,recp_total_length))

    allocate(recp_mess_length1(lmpi_nproc))
    allocate(recp_mess_length2(lmpi_nproc))
    allocate(recp_mess_length3(lmpi_nproc))

    recp_mess_length1 = 0
    recp_mess_length2 = 0
    recp_mess_length3 = 0

! Take care of the fringe points

    fringes : do i = 1, nreceptors

      receptor = receptors(i)%node

      greek(:) = receptors(i)%greek_coords(:)

      do j = 1, receptors(i)%ndonors
        rank = receptors(i)%donor_ranks(j)
        if ( rank == lmpi_id ) then
          donor = receptors(i)%donor_indices(j)
          lx(j) = grid%x(donor)
          ly(j) = grid%y(donor)
          lz(j) = grid%z(donor)
        else
          item = recp_mess_start(rank+1) + recp_mess_length1(rank+1)
          recp_mess_length1(rank+1) = recp_mess_length1(rank+1) + 1
          lx(j) = recp_xyzq(1,item)
          ly(j) = recp_xyzq(2,item)
          lz(j) = recp_xyzq(3,item)
        endif
      end do

      r0(1) = lx(1); r0(2) = ly(1); r0(3) = lz(1)
      r1(1) = lx(2); r1(2) = ly(2); r1(3) = lz(2)
      r2(1) = lx(3); r2(2) = ly(3); r2(3) = lz(3)
      r3(1) = lx(4); r3(2) = ly(4); r3(3) = lz(4)

      select case(receptors(i)%ndonors)
      case(4)  ! tet
      case(5)  ! pyr
        r4(1) = lx(5); r4(2) = ly(5); r4(3) = lz(5)
      case(6)  ! prz
        r4(1) = lx(5); r4(2) = ly(5); r4(3) = lz(5)
        r5(1) = lx(6); r5(2) = ly(6); r5(3) = lz(6)
      case(8)  ! hex
        r4(1) = lx(5); r4(2) = ly(5); r4(3) = lz(5)
        r5(1) = lx(6); r5(2) = ly(6); r5(3) = lz(6)
        r6(1) = lx(7); r6(2) = ly(7); r6(3) = lz(7)
        r7(1) = lx(8); r7(2) = ly(8); r7(3) = lz(8)
      case default
        write(*,*) 'Unknown number of donors: ', receptors(i)%ndonors
        call lmpi_die
        stop
      end select

! Compute the weights here and linearize them

      select case(receptors(i)%ndonors)
      case(4)  ! tet
        jacobian(:,:) = tet_jacobian(r0,r1,r2,r3)
      case(5)  ! pyr
        jacobian(:,:) = pyr_jacobian(greek,r0,r1,r2,r3,r4)
      case(6)  ! prz
        jacobian(:,:) = prz_jacobian(greek,r0,r1,r2,r3,r4,r5)
      case(8)  ! hex
        jacobian(:,:) = hex_jacobian(greek,r0,r1,r2,r3,r4,r5,r6,r7)
      end select

      jac_inv = invert_3x3(jacobian)

! Form dwdx vector:
!  1st index is donor index - this is the weight we are linearizing
!  2nd index is donor index, (n+1)th entry is receptor - this is the point
!  we are linearizing WRT
!  3rd index is coordinate x, y, or z

      do j = 1, receptors(i)%ndonors

        dwdg = weight_wrt_greek(receptors(i)%ndonors,j,greek)
        b = matmul(dwdg,jac_inv)

        do k = 1, receptors(i)%ndonors
          do n = 1, 3
            dwdx(j,k,n) = -b(n)*receptors(i)%donor_weights(k)
          end do
        end do

        do n = 1, 3
          dwdx(j,receptors(i)%ndonors+1,n) = b(n)
        end do

      end do

!  qij : i is the node number, j is the governing eqn

      q = 0.0_dp

      do j = 1, receptors(i)%ndonors
        rank = receptors(i)%donor_ranks(j)
        if ( rank == lmpi_id ) then
          q(j,1:soln%n_tot) =                                                  &
                          soln%q_dof(1:soln%n_tot,receptors(i)%donor_indices(j))
          if ( soln%n_turb > 0 ) then
            q(j,soln%n_tot+1) = soln%turb(1,receptors(i)%donor_indices(j))
          endif
        else
          item = recp_mess_start(rank+1) + recp_mess_length2(rank+1)
          recp_mess_length2(rank+1) = recp_mess_length2(rank+1) + 1
          q(j,1:soln%n_tot+soln%n_turb) =                                      &
                                      recp_xyzq(4:3+soln%n_tot+soln%n_turb,item)
        endif
      end do

! Bear in mind here that the A matrix is defined to have a positive one on the
! diagonal and the negative of the weights on the off-diagonals.  Since we have
! computed all of the weights here with a positive sign, we need to subtract the
! contributions below to account for this minus sign built into the A matrix.

      fcn_loop : do k = 1, design%nfunctions

        select case ( soln%eqn_set )
        case( compressible )
          rlam1 = sadj%rlam(1,receptor,k)
          rlam2 = sadj%rlam(2,receptor,k)
          rlam3 = sadj%rlam(3,receptor,k)
          rlam4 = sadj%rlam(4,receptor,k)
          rlam5 = sadj%rlam(5,receptor,k)
          rlam6 = 0.0_dp
          if ( soln%n_turb > 0 ) rlam6 = sadj%rlam(6,receptor,k)
        case( incompressible )
          rlam1 = sadj%rlam(1,receptor,k)
          rlam2 = sadj%rlam(2,receptor,k)
          rlam3 = sadj%rlam(3,receptor,k)
          rlam4 = sadj%rlam(4,receptor,k)
          rlam5 = 0.0_dp
          rlam6 = 0.0_dp
          if ( soln%n_turb > 0 ) rlam5 = sadj%rlam(5,receptor,k)
        case default
          write(*,*)'linearize_overset_weights only coded for comp. or incomp.'
          call lmpi_die
        end select

! Add contributions to receptor

        do j = 1, receptors(i)%ndonors
          do n = 1, 3
            getg%overset_terms(n,receptor,k) = getg%overset_terms(n,receptor,k)&
                        - (dwdx(j,receptors(i)%ndonors+1,n)*q(j,1))*rlam1      &
                        - (dwdx(j,receptors(i)%ndonors+1,n)*q(j,2))*rlam2      &
                        - (dwdx(j,receptors(i)%ndonors+1,n)*q(j,3))*rlam3      &
                        - (dwdx(j,receptors(i)%ndonors+1,n)*q(j,4))*rlam4      &
                        - (dwdx(j,receptors(i)%ndonors+1,n)*q(j,5))*rlam5      &
                        - (dwdx(j,receptors(i)%ndonors+1,n)*q(j,6))*rlam6
          end do
        end do

! Add contributions to donors

        overset_terms(:,:,k) = 0.0_dp

        do m = 1, receptors(i)%ndonors
          do n = 1, 3
            do j = 1, receptors(i)%ndonors
              overset_terms(n,m,k) = overset_terms(n,m,k)                      &
                          - (dwdx(j,m,n)*q(j,1))*rlam1                         &
                          - (dwdx(j,m,n)*q(j,2))*rlam2                         &
                          - (dwdx(j,m,n)*q(j,3))*rlam3                         &
                          - (dwdx(j,m,n)*q(j,4))*rlam4                         &
                          - (dwdx(j,m,n)*q(j,5))*rlam5                         &
                          - (dwdx(j,m,n)*q(j,6))*rlam6
            end do
          end do
        end do

      end do fcn_loop

      do j = 1, receptors(i)%ndonors
        rank = receptors(i)%donor_ranks(j)
        if ( rank == lmpi_id ) then
          getg%overset_terms(:,receptors(i)%donor_indices(j),:) =              &
          getg%overset_terms(:,receptors(i)%donor_indices(j),:)                &
          + overset_terms(:,j,:)
        else
          item = recp_mess_start(rank+1) + recp_mess_length3(rank+1)
          recp_mess_length3(rank+1) = recp_mess_length3(rank+1) + 1
          recp_contribs(:,:,item) = overset_terms(:,j,:)
        endif
      end do

    end do fringes

    allocate(donr_contribs(3,design%nfunctions,donr_total_length))

    call lmpi_alltoallv(recp_contribs, recp_mess_length,                       &
                        donr_contribs, donr_mess_length)

    do i = 1, donr_total_length
      donor = donr_donors(i)
      do j = 1, 3
        getg%overset_terms(j,donor,:) = getg%overset_terms(j,donor,:)          &
                                      + donr_contribs(j,:,i)
      end do
    end do

! free memory

    deallocate(recp_mess_length,recp_donors,recp_mess_start)
    deallocate(donr_mess_length,donr_donors,donr_xyzq,recp_xyzq)
    deallocate(recp_mess_length1,recp_mess_length2,recp_mess_length3)
    deallocate(donr_contribs,recp_contribs)

! Note there are no contributions from orphans or blanks, since their weighting
! coefficients do not depend on grid coordinates

  end subroutine linearize_overset_weights


!================================ WEIGHT_WRT_GREEK ===========================80
!
!  Linearizes weights wrt greek variables
!
!=============================================================================80
  function weight_wrt_greek(ndonors,donor,greek)

    use kinddefs, only : dp

    integer, intent(in) :: ndonors, donor

    real(dp), dimension(3), intent(in) :: greek

    real(dp), dimension(3) :: weight_wrt_greek

    real(dp) :: xi, eta, zeta

  continue

    xi   = greek(1)
    eta  = greek(2)
    zeta = greek(3)

    if ( .false. ) write(*,*) xi, eta, zeta

    select case(ndonors)
    case(4)  ! tet
      select case(donor)
      case(1)
!     receptors(i)%donor_weights(1) = 1.0_dp-xi-eta-zeta
        weight_wrt_greek(1) = -1.0_dp
        weight_wrt_greek(2) = -1.0_dp
        weight_wrt_greek(3) = -1.0_dp
      case(2)
!     receptors(i)%donor_weights(2) = xi
        weight_wrt_greek(1) =  1.0_dp
        weight_wrt_greek(2) =  0.0_dp
        weight_wrt_greek(3) =  0.0_dp
      case(3)
!     receptors(i)%donor_weights(3) = eta
        weight_wrt_greek(1) =  0.0_dp
        weight_wrt_greek(2) =  1.0_dp
        weight_wrt_greek(3) =  0.0_dp
      case(4)
!     receptors(i)%donor_weights(4) = zeta
        weight_wrt_greek(1) =  0.0_dp
        weight_wrt_greek(2) =  0.0_dp
        weight_wrt_greek(3) =  1.0_dp
      end select
    case(5)  ! pyr
      select case(donor)
      case(1)
!     receptors(i)%donor_weights(1) = (1.0_dp-xi)*(1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(1) = -(1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(2) = -(1.0_dp-xi) *(1.0_dp-zeta)
        weight_wrt_greek(3) = -(1.0_dp-xi) *(1.0_dp-eta)
      case(2)
!     receptors(i)%donor_weights(2) = xi*(1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(1) = (1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(2) = -xi*(1.0_dp-zeta)
        weight_wrt_greek(3) = -xi*(1.0_dp-eta)
      case(3)
!     receptors(i)%donor_weights(3) = xi*eta*(1.0_dp-zeta)
        weight_wrt_greek(1) = eta*(1.0_dp-zeta)
        weight_wrt_greek(2) = xi*(1.0_dp-zeta)
        weight_wrt_greek(3) = -xi*eta
      case(4)
!     receptors(i)%donor_weights(4) = eta*(1.0_dp-xi)*(1.0_dp-zeta)
        weight_wrt_greek(1) = -eta*(1.0_dp-zeta)
        weight_wrt_greek(2) = (1.0_dp-xi)*(1.0_dp-zeta)
        weight_wrt_greek(3) = -eta*(1.0_dp-xi)
      case(5)
!     receptors(i)%donor_weights(5) = zeta
        weight_wrt_greek(1) = 0.0_dp
        weight_wrt_greek(2) = 0.0_dp
        weight_wrt_greek(3) = 1.0_dp
      end select
    case(6)  ! prz
      select case(donor)
      case(1)
!     receptors(i)%donor_weights(1) = 1.0_dp-xi-eta-zeta+xi*zeta+eta*zeta
        weight_wrt_greek(1) = -1.0_dp+zeta
        weight_wrt_greek(2) = -1.0_dp+zeta
        weight_wrt_greek(3) = -1.0_dp+xi+eta
      case(2)
!     receptors(i)%donor_weights(2) = xi*(1.0_dp-zeta)
        weight_wrt_greek(1) = (1.0_dp-zeta)
        weight_wrt_greek(2) = 0.0_dp
        weight_wrt_greek(3) = -xi
      case(3)
!     receptors(i)%donor_weights(3) = eta*(1.0_dp-zeta)
        weight_wrt_greek(1) = 0.0_dp
        weight_wrt_greek(2) = (1.0_dp-zeta)
        weight_wrt_greek(3) = -eta
      case(4)
!     receptors(i)%donor_weights(4) = zeta*(1.0_dp-xi-eta)
        weight_wrt_greek(1) = -zeta
        weight_wrt_greek(2) = -zeta
        weight_wrt_greek(3) = (1.0_dp-xi-eta)
      case(5)
!     receptors(i)%donor_weights(5) = xi*zeta
        weight_wrt_greek(1) = zeta
        weight_wrt_greek(2) = 0.0_dp
        weight_wrt_greek(3) = xi
      case(6)
!     receptors(i)%donor_weights(6) = eta*zeta
        weight_wrt_greek(1) = 0.0_dp
        weight_wrt_greek(2) = zeta
        weight_wrt_greek(3) = eta
      end select
    case(8)  ! hex
      select case(donor)
      case(1)
!     receptors(i)%donor_weights(1) = (1.0_dp-xi)*(1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(1) = -(1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(2) = -(1.0_dp-xi) *(1.0_dp-zeta)
        weight_wrt_greek(3) = -(1.0_dp-xi) *(1.0_dp-eta)
      case(2)
!     receptors(i)%donor_weights(2) = xi*(1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(1) = (1.0_dp-eta)*(1.0_dp-zeta)
        weight_wrt_greek(2) = -xi*(1.0_dp-zeta)
        weight_wrt_greek(3) = -xi*(1.0_dp-eta)
      case(3)
!     receptors(i)%donor_weights(3) = xi*eta*(1.0_dp-zeta)
        weight_wrt_greek(1) = eta*(1.0_dp-zeta)
        weight_wrt_greek(2) = xi*(1.0_dp-zeta)
        weight_wrt_greek(3) = -xi*eta
      case(4)
!     receptors(i)%donor_weights(4) = (1.0_dp-xi)*eta*(1.0_dp-zeta)
        weight_wrt_greek(1) = -eta*(1.0_dp-zeta)
        weight_wrt_greek(2) = (1.0_dp-xi)*(1.0_dp-zeta)
        weight_wrt_greek(3) = -(1.0_dp-xi)*eta
      case(5)
!     receptors(i)%donor_weights(5) = (1.0_dp-xi)*(1.0_dp-eta)*zeta
        weight_wrt_greek(1) = -(1.0_dp-eta)*zeta
        weight_wrt_greek(2) = -(1.0_dp-xi)*zeta
        weight_wrt_greek(3) = (1.0_dp-xi)*(1.0_dp-eta)
      case(6)
!     receptors(i)%donor_weights(6) = xi*(1.0_dp-eta)*zeta
        weight_wrt_greek(1) = (1.0_dp-eta)*zeta
        weight_wrt_greek(2) = -xi*zeta
        weight_wrt_greek(3) = xi*(1.0_dp-eta)
      case(7)
!     receptors(i)%donor_weights(7) = xi*eta*zeta
        weight_wrt_greek(1) = eta*zeta
        weight_wrt_greek(2) = xi*zeta
        weight_wrt_greek(3) = xi*eta
      case(8)
!     receptors(i)%donor_weights(8) = (1.0_dp-xi)*eta*zeta
        weight_wrt_greek(1) = -eta*zeta
        weight_wrt_greek(2) = (1.0_dp-xi)*zeta
        weight_wrt_greek(3) = (1.0_dp-xi)*eta
      end select
    end select

  end function weight_wrt_greek


!================================ WRITE_COLUMN_SUM ===========================80
!
!  Perform a column sum and write this out to disk as the RHS
!  for the grid adjoint computation
!
!=============================================================================80
  subroutine write_column_sum(nnodes0,nnodes01,nfunctions,nnz01,ntp,ia,ja,     &
                              drdxl,dfdx,dvdx,sourceterm_sum,overset_terms,    &
                              output)

    use kinddefs,         only : dp
    use lmpi,             only : lmpi_master, lmpi_reduce, lmpi_die
    use lmpi_app,         only : lmpi_sumnode
    use info_depr,        only : ivisc
    use nml_overset_data, only : overset_flag

    integer,                                        intent(in) :: nnodes0
    integer,                                        intent(in) :: nfunctions
    integer,                                        intent(in) :: nnz01
    integer,                                        intent(in) :: nnodes01, ntp
    integer,  dimension(:),                         intent(in) :: ia
    integer,  dimension(:),                         intent(in) :: ja
    real(dp), dimension(3,nnz01,nfunctions),        intent(in) :: dvdx
    real(dp), dimension(3,nnz01,nfunctions),        intent(in) :: dfdx
    real(dp), dimension(3,nnodes01,nfunctions,ntp), intent(in) :: drdxl
    real(dp), dimension(3,nnodes01,nfunctions),     intent(in) :: sourceterm_sum
    real(dp), dimension(3,nnodes01,nfunctions),     intent(in) :: overset_terms
    real(dp), dimension(3,nnodes0,nfunctions),      intent(out):: output

    integer :: i,j,k,jstart,jend,jcol

    real(dp) :: golden_sum, reduced_golden_sum

    real(dp), dimension(3,nnodes01,nfunctions) :: colsum

  continue

    if ( ntp > 1 ) then
      write(*,*) 'Need to upgrade write_column_sum to read/write/shuffle'
      write(*,*) 'multiple timeplanes...'
      call lmpi_die
    endif

! Do a column sum on A for each function then write it out
! Also add the Spalart source term contributions if needed

    fcn_loop : do k = 1, nfunctions

      colsum(:,:,k) = 0.0_dp

! We have to loop out to the first set of ghosts because the cost
! function pieces go out that far (the boundary elements are only
! done on certain processors (i.e., face_bit stuff), even if some
! of their nodes are ghosts)
! Note this is ok to do even with the residual pieces included in
! A because we restricted ourselves to not add off-processor rows
! for those, except again, for boundary elements (face_bit stuff)

      do i = 1, nnodes01

        colsum(:,i,k) = colsum(:,i,k) + drdxl(:,i,k,1)

        if ( overset_flag ) then
          colsum(:,i,k) = colsum(:,i,k) + overset_terms(:,i,k)
        endif

        jstart = ia(i)
        jend   = ia(i+1) - 1
        do j = jstart, jend
          jcol = ja(j)
          if ( jcol <= nnodes01 ) then
            colsum(1,jcol,k) = colsum(1,jcol,k) + dfdx(1,j,k) + dvdx(1,j,k)
            colsum(2,jcol,k) = colsum(2,jcol,k) + dfdx(2,j,k) + dvdx(2,j,k)
            colsum(3,jcol,k) = colsum(3,jcol,k) + dfdx(3,j,k) + dvdx(3,j,k)
          endif
        end do

        if ( ivisc >= 6 ) then
          colsum(1,i,k) = colsum(1,i,k) + sourceterm_sum(1,i,k)
          colsum(2,i,k) = colsum(2,i,k) + sourceterm_sum(2,i,k)
          colsum(3,i,k) = colsum(3,i,k) + sourceterm_sum(3,i,k)
        endif

      end do

! Sum up off processor contributions into local entries

      call lmpi_sumnode(colsum(:,:,k))

      output(:,:,k) = colsum(:,1:nnodes0,k)

! Dump out something to the golden file for checking

      golden_sum = 0.0_dp
      do i = 1, nnodes0
        do j = 1, 3
          golden_sum = golden_sum + output(j,i,k)
        end do
      end do

      call lmpi_reduce(golden_sum,reduced_golden_sum)

      if ( lmpi_master ) then
        write(67,*) reduced_golden_sum
      endif

    end do fcn_loop

  end subroutine write_column_sum


!=========================== WRITE_COLUMN_SUM_DYNAMIC ========================80
!
!  Perform a column sum and write this out to disk as the RHS
!  for the grid adjoint computation
!
!  Dynamic grid version with accumulation/storage of backplane info
!
!=============================================================================80
  subroutine write_column_sum_dynamic(nnodes0,nnodes01,nfunctions,nnz01,ntp,ia,&
                                      ja,drdxl,dfdx,dvdx,sourceterm_sum,       &
                                      overset_terms,output)

    use kinddefs,             only : dp
    use allocations,          only : my_alloc_ptr
    use lmpi,                 only : lmpi_master, lmpi_reduce, lmpi_die
    use lmpi_app,             only : lmpi_sumnode
    use info_depr,            only : ivisc, physical_timestep, ncyc
    use nml_nonlinear_solves, only : itime
    use nml_overset_data,     only : overset_flag

    integer,                                        intent(in) :: nnodes0
    integer,                                        intent(in) :: nfunctions
    integer,                                        intent(in) :: nnz01
    integer,                                        intent(in) :: nnodes01, ntp
    integer,  dimension(:),                         intent(in) :: ia
    integer,  dimension(:),                         intent(in) :: ja
    real(dp), dimension(3,nnz01,nfunctions),        intent(in) :: dvdx
    real(dp), dimension(3,nnz01,nfunctions),        intent(in) :: dfdx
    real(dp), dimension(3,nnodes01,nfunctions,ntp), intent(in) :: drdxl
    real(dp), dimension(3,nnodes01,nfunctions),     intent(in) :: sourceterm_sum
    real(dp), dimension(3,nnodes01,nfunctions),     intent(in) :: overset_terms
    real(dp), dimension(3,nnodes0,nfunctions),      intent(out):: output

    integer :: i,j,k,jstart,jend,jcol,n
    integer :: reqd_residual_timestep,reqd_grid_timestep,ii
    integer :: rev_residual_timestep,rev_grid_timestep

    real(dp) :: golden_sum, reduced_golden_sum

    real(dp), dimension(3,nnodes01,nfunctions) :: colsum

    logical :: data_found, data_stored
    logical, save :: first_time_through = .true.

  continue

! Set up memory for the stored timeplanes forward in physical time

! Figure out the max number of combinations we'll be storing on disk and
! allocate the memory for them

    perform_setup_stuff : if ( first_time_through ) then

      select case(itime)
      case(1)                      ! BDF1
        n_stored_planes = 1
      case(2)                      ! BDF2
        n_stored_planes = 3
      case(3)                      ! BDF3/BDF2opt
        n_stored_planes = 6
      case default
        write(*,*) 'Bad value for itime in write_column_sum_dynamic', itime
        call lmpi_die
      end select

      allocate(stored_planes(n_stored_planes))

      do i = 1, n_stored_planes
        stored_planes(i)%residual_timestep = 0
        stored_planes(i)%grid_timestep     = 0
        call my_alloc_ptr(stored_planes(i)%drdxl,3,nnodes01,nfunctions)
      end do

      first_time_through = .false.

    end if perform_setup_stuff

! Now add up all of the pieces to get the RHS for the current grid adjoint

! Do a column sum on A then write it out
! Also add the Spalart source term contributions if needed

    colsum = 0.0_dp

! We have to loop out to the first set of ghosts because the cost
! function pieces go out that far (the boundary elements are only
! done on certain processors (i.e., face_bit stuff), even if some
! of their nodes are ghosts)
! Note this is ok to do even with the residual pieces included in
! A because we restricted ourselves to not add off-processor rows
! for those, except again, for boundary elements (face_bit stuff)

! First the cost function and dvol/dgrid pieces, which do not
! depend on the backplanes

    do k = 1, nfunctions
      do i = 1, nnodes01
        jstart = ia(i)
        jend   = ia(i+1) - 1
        do j = jstart, jend
          jcol = ja(j)
          if ( jcol <= nnodes01 ) then
            colsum(1,jcol,k) = colsum(1,jcol,k) + dfdx(1,j,k) + dvdx(1,j,k)
            colsum(2,jcol,k) = colsum(2,jcol,k) + dfdx(2,j,k) + dvdx(2,j,k)
            colsum(3,jcol,k) = colsum(3,jcol,k) + dfdx(3,j,k) + dvdx(3,j,k)
          endif
        end do
      end do
    end do

! Now add in the dR/dX and dRgcl/dX pieces from the current
! time linearization (ie, the diagonal piece dRi/dXi)
! Note the turbulence model source terms also show up here, as they
! have no dependence on backplanes either

    do k = 1, nfunctions
      do i = 1, nnodes01

        if ( overset_flag ) then
          colsum(:,i,k) = colsum(:,i,k) + overset_terms(:,i,k)
        endif

        colsum(:,i,k) = colsum(:,i,k) + drdxl(:,i,k,1)
        if ( ivisc >= 6 ) then
          colsum(1,i,k) = colsum(1,i,k) + sourceterm_sum(1,i,k)
          colsum(2,i,k) = colsum(2,i,k) + sourceterm_sum(2,i,k)
          colsum(3,i,k) = colsum(3,i,k) + sourceterm_sum(3,i,k)
        endif

      end do
    end do

! Now loop over the time planes forward in physical time (that were
! hopefully already out on disk) and accumulate the additional dR/dX
! and dRgcl/dX pieces

    gather_planes : do n = 1, ntp-1  ! Already got the diagonal piece above

! Requested time levels

      reqd_residual_timestep = physical_timestep + n
      reqd_grid_timestep     = physical_timestep

      if ( reqd_residual_timestep > ncyc ) cycle gather_planes

      data_found = .false.

      search_stored_data : do ii = 1, n_stored_planes

        if(stored_planes(ii)%residual_timestep==reqd_residual_timestep .and.   &
           stored_planes(ii)%grid_timestep==reqd_grid_timestep) then

          do k = 1, nfunctions
            do i = 1, nnodes01
              colsum(:,i,k)=colsum(:,i,k)+stored_planes(ii)%drdxl(:,i,k)
            end do
          end do

          data_found = .true.
          exit search_stored_data

        endif

      end do search_stored_data

      if ( .not. data_found ) then
        write(*,*) 'ERROR: backplane data not found in stored file'
        write(*,*) 'reqd_residual_timestep = ', reqd_residual_timestep
        write(*,*) 'reqd_grid_timestep     = ', reqd_grid_timestep
        call lmpi_die
      endif

    end do gather_planes

! We should now have the full grid adjoint RHS for the current timestep,
! including all accumulated terms forward in time for dR/dX and dRgcl/dX

! Sum up off processor contributions into local entries

    do k = 1, nfunctions
      call lmpi_sumnode(colsum(:,:,k))
    end do

    output(:,:,:) = colsum(:,1:nnodes0,:)

! Now figure out what data needs to be stored on disk for grid adjoint
! RHS's at subsequent timesteps

    examine_and_store : do n = 1, ntp-1  ! Don't need to store diagonal

      rev_residual_timestep = physical_timestep
      rev_grid_timestep     = physical_timestep-n

      if ( rev_grid_timestep < 0 ) cycle examine_and_store

! Look through current stored data and overwrite levels we will no longer need
! (If stored data is a residual at a timelevel higher than what we'll
!  subsequently need, OR if the stored data is wrt a grid timestep higher than
!  what we'll subsequently need, OR if the stored data was just initialized
!  above with both step indicators set to zero)

      data_stored = .false.

      search_stored_data2 : do ii = 1, n_stored_planes

        if((stored_planes(ii)%residual_timestep>(physical_timestep-1+(ntp-1))) &
           .or. (stored_planes(ii)%grid_timestep >= physical_timestep)         &
           .or.                                                                &
           (stored_planes(ii)%residual_timestep == 0 .and.                     &
            stored_planes(ii)%grid_timestep == 0) ) then

! safe to overwrite this data with the fresh data

          stored_planes(ii)%residual_timestep = rev_residual_timestep
          stored_planes(ii)%grid_timestep     = rev_grid_timestep
          stored_planes(ii)%drdxl(:,:,:)      = drdxl(:,:,:,n+1)

          data_stored = .true.
          exit search_stored_data2

        endif

      end do search_stored_data2

      if ( .not. data_stored ) then
        write(*,*) 'ERROR: location not found to store backplane data!'
        write(*,*) 'rev_residual_timestep = ', rev_residual_timestep
        write(*,*) 'rev_grid_timestep     = ', rev_grid_timestep
        call lmpi_die
      endif

    end do examine_and_store

! Dump out something to the golden file for checking

    golden_sum = 0.0_dp
    do k = 1, nfunctions
      do i = 1, nnodes0
        do j = 1, 3
          golden_sum = golden_sum + output(j,i,k)
        end do
      end do
    end do

    call lmpi_reduce(golden_sum,reduced_golden_sum)

    if ( lmpi_master ) then
      write(67,*) reduced_golden_sum
    endif

  end subroutine write_column_sum_dynamic


!=========================== COLUMN_SUM_INITIAL_STATE ========================80
!
!  Perform a column sum and write this out to disk as the RHS
!  for the grid adjoint computation
!
!  Dynamic grid version with accumulation/storage of backplane info
!  Version to simply read in linearizations wrt initial state
!
!=============================================================================80
  subroutine column_sum_initial_state(nnodes0,nnodes01,nfunctions,nnz01,ntp,ia,&
                                      ja,dvdx,output)

    use kinddefs, only : dp
    use lmpi,     only : lmpi_master, lmpi_reduce, lmpi_die
    use lmpi_app, only : lmpi_sumnode
    use info_depr,only : physical_timestep, ncyc

    integer,                                   intent(in ) :: nnodes0
    integer,                                   intent(in ) :: nfunctions, nnz01
    integer,                                   intent(in ) :: nnodes01, ntp
    integer,  dimension(:),                    intent(in ) :: ia
    integer,  dimension(:),                    intent(in ) :: ja
    real(dp), dimension(3,nnz01,nfunctions),   intent(in ) :: dvdx
    real(dp), dimension(3,nnodes0,nfunctions), intent(out) :: output

    integer :: i,j,k,jstart,jend,jcol,n
    integer :: reqd_residual_timestep,reqd_grid_timestep,ii

    real(dp) :: golden_sum, reduced_golden_sum

    real(dp), dimension(3,nnodes01,nfunctions) :: colsum

    logical :: data_found

  continue

! Now add up all of the pieces to get the RHS for the current grid adjoint

! Do a column sum on A then write it out

    colsum = 0.0_dp

! We have to loop out to the first set of ghosts because the cost
! function pieces go out that far (the boundary elements are only
! done on certain processors (i.e., face_bit stuff), even if some
! of their nodes are ghosts)
! Note this is ok to do even with the residual pieces included in
! A because we restricted ourselves to not add off-processor rows
! for those, except again, for boundary elements (face_bit stuff)

! First the dvol/dgrid pieces, which do not depend on the backplanes

    do k = 1, nfunctions
      do i = 1, nnodes01
        jstart = ia(i)
        jend   = ia(i+1) - 1
        do j = jstart, jend
          jcol = ja(j)
          if ( jcol <= nnodes01 ) then
            colsum(1,jcol,k) = colsum(1,jcol,k) + dvdx(1,j,k)
            colsum(2,jcol,k) = colsum(2,jcol,k) + dvdx(2,j,k)
            colsum(3,jcol,k) = colsum(3,jcol,k) + dvdx(3,j,k)
          endif
        end do
      end do
    end do

! Now loop over the time planes forward in physical time (that were
! hopefully already out on disk) and accumulate the additional dR/dX
! and dRgcl/dX pieces

    gather_planes : do n = 1, ntp-1  ! Already got the diagonal piece above

! Requested time levels

      reqd_residual_timestep = physical_timestep + n
      reqd_grid_timestep     = physical_timestep

      if ( reqd_residual_timestep > ncyc ) cycle gather_planes

      data_found = .false.

      search_stored_data : do ii = 1, n_stored_planes

        if(stored_planes(ii)%residual_timestep==reqd_residual_timestep .and.   &
           stored_planes(ii)%grid_timestep==reqd_grid_timestep) then

          do k = 1, nfunctions
            do i = 1, nnodes01
              colsum(:,i,k)=colsum(:,i,k)+stored_planes(ii)%drdxl(:,i,k)
            end do
          end do

          data_found = .true.
          exit search_stored_data

        endif

      end do search_stored_data

      if ( .not. data_found ) then
        write(*,*) 'ERROR: backplane data not found in stored file'
        write(*,*) 'reqd_residual_timestep = ', reqd_residual_timestep
        write(*,*) 'reqd_grid_timestep     = ', reqd_grid_timestep
        call lmpi_die
      endif

    end do gather_planes

! We should now have the full grid adjoint RHS for the current timestep,
! including all accumulated terms forward in time for dR/dX and dRgcl/dX

! Sum up off processor contributions into local entries

    do k = 1, nfunctions
      call lmpi_sumnode(colsum(:,:,k))
    end do

    output(:,:,:) = colsum(:,1:nnodes0,:)

! Dump out something to the golden file for checking

    golden_sum = 0.0_dp
    do k = 1, nfunctions
      do i = 1, nnodes0
        do j = 1, 3
          golden_sum = golden_sum + output(j,i,k)
        end do
      end do
    end do

    call lmpi_reduce(golden_sum,reduced_golden_sum)

    if ( lmpi_master ) then
      write(67,*) reduced_golden_sum
    endif

  end subroutine column_sum_initial_state


!================================= DGCL_DGRID ================================80
!
! Linearizes the GCL wrt the grid
!
! Note: the terms are incomplete until closed off in dgcl_dgridb
!
!=============================================================================80
  subroutine dgcl_dgrid(grid,soln,sadj,design,getg)

    use kinddefs,        only : dp
    use element_defs,    only : max_node_per_cell
    use info_depr,       only : physical_timestep, beta, ivisc,            &
                                simulation_time
    use nml_nonlinear_solves,            only : ibdf2opt, itime, dt
    use nml_noninertial_reference_frame, only : noninertial
    use fun3d_constants, only : my_half, my_1, my_2, my_1p5, my_3, my_6, my_11,&
                                my_0, my_4
    use noninertials,    only : xrotrate_ni, yrotrate_ni, zrotrate_ni
    use grid_types,      only : grid_type
    use solution_types,  only : soln_type, incompressible
    use solution_adj,    only : sadj_type
    use design_types,    only : design_type
    use solution_getg,   only : getg_type
    use lmpi_app,        only : lmpi_xfer

    type(grid_type),   intent(in)    :: grid
    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(in)    :: design
    type(soln_type),   intent(inout) :: soln
    type(getg_type),   intent(inout) :: getg

    integer :: edge,node,n,ielem,ind,ifcn
    integer :: j,k,ie,i,inode,node1,node2
    integer :: n1,n2,n3,n4,n5,n6,nn1,nn2,nn3,nn4,nn5,nn6,nn

    real(dp) :: denom,fact,stored_simulation_time,factor
    real(dp) :: xl1,xr1,xl2,xr2,xm1,xm2
    real(dp) :: yl1,yr1,yl2,yr2,ym1,ym2
    real(dp) :: zl1,zr1,zl2,zr2,zm1,zm2
    real(dp) :: xorig1,xorig2,yorig1,yorig2
    real(dp) :: zorig1,zorig2
    real(dp) :: dxdtc,dydtc,dzdtc,dxdtm,dydtm,dzdtm
    real(dp) :: dxdtl,dydtl,dzdtl,dxdtr,dydtr,dzdtr
    real(dp) :: areax1,areay1,areaz1
    real(dp) :: areax2,areay2,areaz2
    real(dp) :: dxdtavg,dydtavg,dzdtavg

    real(dp), dimension(max_node_per_cell) :: xc,yc,zc

    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dxcdx
    real(dp), dimension(max_node_per_cell) :: dxm1dx
    real(dp), dimension(max_node_per_cell) :: dxl1dx
    real(dp), dimension(max_node_per_cell) :: dxr1dx
    real(dp), dimension(max_node_per_cell) :: dxm2dx
    real(dp), dimension(max_node_per_cell) :: dxl2dx
    real(dp), dimension(max_node_per_cell) :: dxr2dx

    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dycdy
    real(dp), dimension(max_node_per_cell) :: dym1dy
    real(dp), dimension(max_node_per_cell) :: dyl1dy
    real(dp), dimension(max_node_per_cell) :: dyr1dy
    real(dp), dimension(max_node_per_cell) :: dym2dy
    real(dp), dimension(max_node_per_cell) :: dyl2dy
    real(dp), dimension(max_node_per_cell) :: dyr2dy

    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dzcdz
    real(dp), dimension(max_node_per_cell) :: dzm1dz
    real(dp), dimension(max_node_per_cell) :: dzl1dz
    real(dp), dimension(max_node_per_cell) :: dzr1dz
    real(dp), dimension(max_node_per_cell) :: dzm2dz
    real(dp), dimension(max_node_per_cell) :: dzl2dz
    real(dp), dimension(max_node_per_cell) :: dzr2dz

    real(dp), dimension(max_node_per_cell) :: dxorig1dx
    real(dp), dimension(max_node_per_cell) :: dyorig1dy
    real(dp), dimension(max_node_per_cell) :: dzorig1dz
    real(dp), dimension(max_node_per_cell) :: dxorig2dx
    real(dp), dimension(max_node_per_cell) :: dyorig2dy
    real(dp), dimension(max_node_per_cell) :: dzorig2dz

    real(dp), dimension(max_node_per_cell) :: dkxdx
    real(dp), dimension(max_node_per_cell) :: dkxdy
    real(dp), dimension(max_node_per_cell) :: dkxdz

    real(dp), dimension(max_node_per_cell) :: dkydx
    real(dp), dimension(max_node_per_cell) :: dkydy
    real(dp), dimension(max_node_per_cell) :: dkydz

    real(dp), dimension(max_node_per_cell) :: dkzdx
    real(dp), dimension(max_node_per_cell) :: dkzdy
    real(dp), dimension(max_node_per_cell) :: dkzdz

    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtcdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtcdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtcdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtcdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtcdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtcdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtcdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtcdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtcdz

    real(dp), dimension(max_node_per_cell,getg%ntp) :: dfacespeeddx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dfacespeeddy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dfacespeeddz

    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtmdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtmdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtmdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtmdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtmdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtmdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtmdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtmdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtmdz

    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtldx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtldy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtldz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtldx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtldy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtldz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtldx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtldy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtldz

    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtrdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtrdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtrdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtrdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtrdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtrdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtrdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtrdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtrdz

    real(dp), dimension(max_node_per_cell) :: dareax1dx,dareax1dy,dareax1dz
    real(dp), dimension(max_node_per_cell) :: dareay1dx,dareay1dy,dareay1dz
    real(dp), dimension(max_node_per_cell) :: dareaz1dx,dareaz1dy,dareaz1dz
    real(dp), dimension(max_node_per_cell) :: dareax2dx,dareax2dy,dareax2dz
    real(dp), dimension(max_node_per_cell) :: dareay2dx,dareay2dy,dareay2dz
    real(dp), dimension(max_node_per_cell) :: dareaz2dx,dareaz2dy,dareaz2dz

    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtavgdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtavgdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddxdtavgdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtavgdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtavgdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddydtavgdz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtavgdx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtavgdy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: ddzdtavgdz

    real(dp), dimension(max_node_per_cell,getg%ntp) ::dterm1dx,dterm1dy,dterm1dz
    real(dp), dimension(max_node_per_cell,getg%ntp) ::dterm2dx,dterm2dy,dterm2dz

    real(dp), dimension(soln%n_tot,grid%nnodes0)  :: Qn, Qnm1
    real(dp), dimension(soln%n_turb,grid%nnodes0) :: turbn, turbnm1
    real(dp), dimension(getg%ntp) :: time_coeff

    real(dp), dimension(max_node_per_cell,getg%ntp) :: dres_gcl1dx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dres_gcl1dy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dres_gcl1dz
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dres_gcl2dx
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dres_gcl2dy
    real(dp), dimension(max_node_per_cell,getg%ntp) :: dres_gcl2dz

    logical :: apply_turb_hack

  continue

! First get the Q planes that we need
!  Qn is in qnode right now

    stored_simulation_time = simulation_time
    Qn(1:soln%n_tot,1:grid%nnodes0) = soln%q_dof(1:soln%n_tot,1:grid%nnodes0)
    if ( ivisc >= 6 ) then
      turbn(1:soln%n_turb,1:grid%nnodes0) =                                    &
                                         soln%turb(1:soln%n_turb,1:grid%nnodes0)
    endif

! Now load Q from the previous timestep and stick it into Qnm1

    apply_turb_hack = .false.
    if ( physical_timestep == 1 ) then
      apply_turb_hack = .true.
    endif

    Qnm1(1:soln%n_tot,1:grid%nnodes0)=soln%qatn1(1:soln%n_tot,1:grid%nnodes0)
    if ( ivisc >= 6 ) then
      turbnm1(1:soln%n_turb,1:grid%nnodes0) =                                  &
                                   soln%turbatn1(1:soln%n_turb,1:grid%nnodes0)
    endif

    if ( ivisc >= 6 .and. apply_turb_hack ) then
      do i = 1, grid%nnodes0
        do j = 1, soln%n_turb
          ind = soln%n_tot + j
          if ( sadj%coltag(ind,i) < 0.1_dp ) turbnm1(j,i) = 0.0_dp
        end do
      end do
    endif

! Set qnode back to Qn and xfer it just to be safe

    simulation_time = stored_simulation_time
    soln%q_dof(1:soln%n_tot,1:grid%nnodes0) = Qn(1:soln%n_tot,1:grid%nnodes0)
    call lmpi_xfer(soln%q_dof)
    if ( ivisc >= 6 ) then
      soln%turb(1:soln%n_turb,1:grid%nnodes0) =                                &
                                             turbn(1:soln%n_turb,1:grid%nnodes0)
      call lmpi_xfer(soln%turb)
    endif

! Set the time-derivative coefficient that goes with the node speed terms

    if ( physical_timestep == 1 .or. itime == 1 ) then       !BDF1
      time_coeff(1) =  my_1/dt
      time_coeff(2) = -my_1/dt
    else if ( physical_timestep == 2 .or. itime == 2 ) then  !BDF2
      time_coeff(1) =  my_1p5/dt
      time_coeff(2) = -my_2/dt
      time_coeff(3) =  my_half/dt
    else if ( itime == 3 ) then
      if ( ibdf2opt == 0 ) then                              !BDF3
        time_coeff(1) =  my_11/my_6/dt
        time_coeff(2) = -my_3/dt
        time_coeff(3) =  my_1p5/dt
        time_coeff(4) = -my_1/my_3/dt
      else                                                   !BDF2opt
        time_coeff(1) =  (0.48_dp*my_11/my_6 + 0.52_dp*my_1p5)/dt
        time_coeff(2) = (-0.48_dp*my_3       - 0.52_dp*my_2)/dt
        time_coeff(3) =  (0.48_dp*my_1p5     + 0.52_dp*my_half)/dt
        time_coeff(4) = (-0.48_dp*my_2/my_6)/dt
      endif
    endif

! Linearize the facespeeds

    elem_loop : do ielem = 1, grid%nelem

      denom = real(grid%elem(ielem)%node_per_cell,dp)

      cell_loop : do n = 1, grid%elem(ielem)%ncell

!     what follows are the d(normal)/d(coord) pieces
!     These are derived from the dualmetric subroutine in grid_metrics

!     cell center (avg of all points in cell)

        xc(:) = 0.0_dp
        yc(:) = 0.0_dp
        zc(:) = 0.0_dp
        dxdtc = 0.0_dp
        dydtc = 0.0_dp
        dzdtc = 0.0_dp

        do nn = 1,grid%elem(ielem)%node_per_cell
          node  = grid%elem(ielem)%c2n(nn,n)
          do k = 1, grid%elem(ielem)%node_per_cell
            xc(k) = xc(k) + (grid%x(node) - grid%x(grid%elem(ielem)%c2n(k,n)))
            yc(k) = yc(k) + (grid%y(node) - grid%y(grid%elem(ielem)%c2n(k,n)))
            zc(k) = zc(k) + (grid%z(node) - grid%z(grid%elem(ielem)%c2n(k,n)))
          end do
          dxdtc = dxdtc + grid%dxdt(node)
          dydtc = dydtc + grid%dydt(node)
          dzdtc = dzdtc + grid%dzdt(node)
        end do

        do k = 1, grid%elem(ielem)%node_per_cell
          xc(k) = xc(k)/denom
          yc(k) = yc(k)/denom
          zc(k) = zc(k)/denom
        end do

        dxdtc = dxdtc/denom
        dydtc = dydtc/denom
        dzdtc = dzdtc/denom

        do j = 1, grid%elem(ielem)%node_per_cell
          do k = 1, grid%elem(ielem)%node_per_cell
            if ( j == k ) then
              dxcdx(j,k) = 1.0_dp/denom - 1.0_dp
              dycdy(j,k) = 1.0_dp/denom - 1.0_dp
              dzcdz(j,k) = 1.0_dp/denom - 1.0_dp
            else
              dxcdx(j,k) = 1.0_dp/denom
              dycdy(j,k) = 1.0_dp/denom
              dzcdz(j,k) = 1.0_dp/denom
            endif
          end do
        end do

        if ( noninertial ) then
          ddxdtcdx(:,1) = my_0
          ddxdtcdy(:,1) = -zrotrate_ni/denom
          ddxdtcdz(:,1) =  yrotrate_ni/denom

          ddydtcdx(:,1) =  zrotrate_ni/denom
          ddydtcdy(:,1) = my_0
          ddydtcdz(:,1) = -xrotrate_ni/denom

          ddzdtcdx(:,1) = -yrotrate_ni/denom
          ddzdtcdy(:,1) =  xrotrate_ni/denom
          ddzdtcdz(:,1) = my_0
        else
          do i = 1, getg%ntp
            ddxdtcdx(:,i) = time_coeff(i)/denom
            ddydtcdy(:,i) = time_coeff(i)/denom
            ddzdtcdz(:,i) = time_coeff(i)/denom
          end do
        endif

!     loop over edges of the cell

        edge_loop_cell : do ie = 1,grid%elem(ielem)%edge_per_cell

!       initialize local sensitivity arrays

          dkxdx(:) = 0.0_dp
          dkydx(:) = 0.0_dp
          dkzdx(:) = 0.0_dp

          dkxdy(:) = 0.0_dp
          dkydy(:) = 0.0_dp
          dkzdy(:) = 0.0_dp

          dkxdz(:) = 0.0_dp
          dkydz(:) = 0.0_dp
          dkzdz(:) = 0.0_dp

          dxorig1dx(:) = 0.0_dp
          dyorig1dy(:) = 0.0_dp
          dzorig1dz(:) = 0.0_dp

          dxorig2dx(:) = 0.0_dp
          dyorig2dy(:) = 0.0_dp
          dzorig2dz(:) = 0.0_dp

          dxm1dx(:) = 0.0_dp
          dym1dy(:) = 0.0_dp
          dzm1dz(:) = 0.0_dp

          dxm2dx(:) = 0.0_dp
          dym2dy(:) = 0.0_dp
          dzm2dz(:) = 0.0_dp

          dxl1dx(:) = 0.0_dp
          dyl1dy(:) = 0.0_dp
          dzl1dz(:) = 0.0_dp

          dxl2dx(:) = 0.0_dp
          dyl2dy(:) = 0.0_dp
          dzl2dz(:) = 0.0_dp

          dxr1dx(:) = 0.0_dp
          dyr1dy(:) = 0.0_dp
          dzr1dz(:) = 0.0_dp

          dxr2dx(:) = 0.0_dp
          dyr2dy(:) = 0.0_dp
          dzr2dz(:) = 0.0_dp

          dfacespeeddx(:,:) = 0.0_dp
          dfacespeeddy(:,:) = 0.0_dp
          dfacespeeddz(:,:) = 0.0_dp

          ddxdtmdx(:,:) = 0.0_dp
          ddxdtmdy(:,:) = 0.0_dp
          ddxdtmdz(:,:) = 0.0_dp

          ddydtmdx(:,:) = 0.0_dp
          ddydtmdy(:,:) = 0.0_dp
          ddydtmdz(:,:) = 0.0_dp

          ddzdtmdx(:,:) = 0.0_dp
          ddzdtmdy(:,:) = 0.0_dp
          ddzdtmdz(:,:) = 0.0_dp

          ddxdtldx(:,:) = 0.0_dp
          ddxdtldy(:,:) = 0.0_dp
          ddxdtldz(:,:) = 0.0_dp

          ddydtldx(:,:) = 0.0_dp
          ddydtldy(:,:) = 0.0_dp
          ddydtldz(:,:) = 0.0_dp

          ddzdtldx(:,:) = 0.0_dp
          ddzdtldy(:,:) = 0.0_dp
          ddzdtldz(:,:) = 0.0_dp

          ddxdtrdx(:,:) = 0.0_dp
          ddxdtrdy(:,:) = 0.0_dp
          ddxdtrdz(:,:) = 0.0_dp

          ddydtrdx(:,:) = 0.0_dp
          ddydtrdy(:,:) = 0.0_dp
          ddydtrdz(:,:) = 0.0_dp

          ddzdtrdx(:,:) = 0.0_dp
          ddzdtrdy(:,:) = 0.0_dp
          ddzdtrdz(:,:) = 0.0_dp

!         global edge number

          edge = grid%elem(ielem)%c2e(ie,n)

          if (edge > grid%nedgeloc) cycle edge_loop_cell

!         global node numbers of edge endpoints

          n1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),n)
          n2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),n)

          node1 = grid%eptr(1,edge)
          node2 = grid%eptr(2,edge)

!         cell node numbers of edge endpoints

          nn1 = grid%elem(ielem)%local_e2n(ie,1)
          nn2 = grid%elem(ielem)%local_e2n(ie,2)

!         Establish reference origins at each edge endpoint to avoid roundoff

          xorig1 = grid%x(n1)
          yorig1 = grid%y(n1)
          zorig1 = grid%z(n1)

          xorig2 = grid%x(n2)
          yorig2 = grid%y(n2)
          zorig2 = grid%z(n2)

          dxorig1dx(nn1) = 1.0_dp
          dyorig1dy(nn1) = 1.0_dp
          dzorig1dz(nn1) = 1.0_dp

          dxorig2dx(nn2) = 1.0_dp
          dyorig2dy(nn2) = 1.0_dp
          dzorig2dz(nn2) = 1.0_dp

!         edge midpoint referenced to local origins

          xm1 = ((grid%x(n1)-xorig1) + (grid%x(n2)-xorig1))/2._dp
          ym1 = ((grid%y(n1)-yorig1) + (grid%y(n2)-yorig1))/2._dp
          zm1 = ((grid%z(n1)-zorig1) + (grid%z(n2)-zorig1))/2._dp

          dxm1dx(nn1)=1.0_dp/2._dp-dxorig1dx(nn1)/2.0_dp-dxorig1dx(nn1)/2.0_dp
          dym1dy(nn1)=1.0_dp/2._dp-dyorig1dy(nn1)/2.0_dp-dyorig1dy(nn1)/2.0_dp
          dzm1dz(nn1)=1.0_dp/2._dp-dzorig1dz(nn1)/2.0_dp-dzorig1dz(nn1)/2.0_dp

          dxm1dx(nn2)=1.0_dp/2._dp-dxorig1dx(nn2)/2.0_dp-dxorig1dx(nn2)/2.0_dp
          dym1dy(nn2)=1.0_dp/2._dp-dyorig1dy(nn2)/2.0_dp-dyorig1dy(nn2)/2.0_dp
          dzm1dz(nn2)=1.0_dp/2._dp-dzorig1dz(nn2)/2.0_dp-dzorig1dz(nn2)/2.0_dp

          xm2 = ((grid%x(n1)-xorig2) + (grid%x(n2)-xorig2))/2._dp
          ym2 = ((grid%y(n1)-yorig2) + (grid%y(n2)-yorig2))/2._dp
          zm2 = ((grid%z(n1)-zorig2) + (grid%z(n2)-zorig2))/2._dp

          dxm2dx(nn1)=1.0_dp/2._dp-dxorig2dx(nn1)/2.0_dp-dxorig2dx(nn1)/2.0_dp
          dym2dy(nn1)=1.0_dp/2._dp-dyorig2dy(nn1)/2.0_dp-dyorig2dy(nn1)/2.0_dp
          dzm2dz(nn1)=1.0_dp/2._dp-dzorig2dz(nn1)/2.0_dp-dzorig2dz(nn1)/2.0_dp

          dxm2dx(nn2)=1.0_dp/2._dp-dxorig2dx(nn2)/2.0_dp-dxorig2dx(nn2)/2.0_dp
          dym2dy(nn2)=1.0_dp/2._dp-dyorig2dy(nn2)/2.0_dp-dyorig2dy(nn2)/2.0_dp
          dzm2dz(nn2)=1.0_dp/2._dp-dzorig2dz(nn2)/2.0_dp-dzorig2dz(nn2)/2.0_dp

! facespeed stuff

          dxdtm = (grid%dxdt(n1) + grid%dxdt(n2))/2._dp
          dydtm = (grid%dydt(n1) + grid%dydt(n2))/2._dp
          dzdtm = (grid%dzdt(n1) + grid%dzdt(n2))/2._dp

          if ( noninertial ) then
            ddxdtmdx(nn1,1) = my_0
            ddxdtmdy(nn1,1) = -zrotrate_ni/my_2
            ddxdtmdz(nn1,1) =  yrotrate_ni/my_2

            ddydtmdx(nn1,1) =  zrotrate_ni/my_2
            ddydtmdy(nn1,1) = my_0
            ddydtmdz(nn1,1) = -xrotrate_ni/my_2

            ddzdtmdx(nn1,1) = -yrotrate_ni/my_2
            ddzdtmdy(nn1,1) =  xrotrate_ni/my_2
            ddzdtmdz(nn1,1) = my_0

            ddxdtmdx(nn2,1) = my_0
            ddxdtmdy(nn2,1) = -zrotrate_ni/my_2
            ddxdtmdz(nn2,1) =  yrotrate_ni/my_2

            ddydtmdx(nn2,1) =  zrotrate_ni/my_2
            ddydtmdy(nn2,1) = my_0
            ddydtmdz(nn2,1) = -xrotrate_ni/my_2

            ddzdtmdx(nn2,1) = -yrotrate_ni/my_2
            ddzdtmdy(nn2,1) =  xrotrate_ni/my_2
            ddzdtmdz(nn2,1) = my_0
          else
            do i = 1, getg%ntp
              ddxdtmdx(nn1,i) = time_coeff(i)/2._dp
              ddydtmdy(nn1,i) = time_coeff(i)/2._dp
              ddzdtmdz(nn1,i) = time_coeff(i)/2._dp

              ddxdtmdx(nn2,i) = time_coeff(i)/2._dp
              ddydtmdy(nn2,i) = time_coeff(i)/2._dp
              ddzdtmdz(nn2,i) = time_coeff(i)/2._dp
            end do
          endif

!         compute left face centroid referenced to local origins

          n3 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,3),n)

          nn3 = grid%elem(ielem)%local_e2n(ie,3)

          if (grid%elem(ielem)%local_e2n(ie,4) /= 0) then

!           quad face

            n4 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,4),n)

            nn4 = grid%elem(ielem)%local_e2n(ie,4)

            xl1 = ((grid%x(n1)-xorig1) + (grid%x(n2)-xorig1) +                 &
                   (grid%x(n3)-xorig1) + (grid%x(n4)-xorig1))/4._dp
            yl1 = ((grid%y(n1)-yorig1) + (grid%y(n2)-yorig1) +                 &
                   (grid%y(n3)-yorig1) + (grid%y(n4)-yorig1))/4._dp
            zl1 = ((grid%z(n1)-zorig1) + (grid%z(n2)-zorig1) +                 &
                   (grid%z(n3)-zorig1) + (grid%z(n4)-zorig1))/4._dp

            dxl1dx(nn1) = 1.0_dp/4._dp - dxorig1dx(nn1)
            dyl1dy(nn1) = 1.0_dp/4._dp - dyorig1dy(nn1)
            dzl1dz(nn1) = 1.0_dp/4._dp - dzorig1dz(nn1)

            dxl1dx(nn2) = 1.0_dp/4._dp - dxorig1dx(nn2)
            dyl1dy(nn2) = 1.0_dp/4._dp - dyorig1dy(nn2)
            dzl1dz(nn2) = 1.0_dp/4._dp - dzorig1dz(nn2)

            dxl1dx(nn3) = 1.0_dp/4._dp - dxorig1dx(nn3)
            dyl1dy(nn3) = 1.0_dp/4._dp - dyorig1dy(nn3)
            dzl1dz(nn3) = 1.0_dp/4._dp - dzorig1dz(nn3)

            dxl1dx(nn4) = 1.0_dp/4._dp - dxorig1dx(nn4)
            dyl1dy(nn4) = 1.0_dp/4._dp - dyorig1dy(nn4)
            dzl1dz(nn4) = 1.0_dp/4._dp - dzorig1dz(nn4)

            xl2 = ((grid%x(n1)-xorig2) + (grid%x(n2)-xorig2) +                 &
                   (grid%x(n3)-xorig2) + (grid%x(n4)-xorig2))/4._dp
            yl2 = ((grid%y(n1)-yorig2) + (grid%y(n2)-yorig2) +                 &
                   (grid%y(n3)-yorig2) + (grid%y(n4)-yorig2))/4._dp
            zl2 = ((grid%z(n1)-zorig2) + (grid%z(n2)-zorig2) +                 &
                   (grid%z(n3)-zorig2) + (grid%z(n4)-zorig2))/4._dp

            dxl2dx(nn1) = 1.0_dp/4._dp - dxorig2dx(nn1)
            dyl2dy(nn1) = 1.0_dp/4._dp - dyorig2dy(nn1)
            dzl2dz(nn1) = 1.0_dp/4._dp - dzorig2dz(nn1)

            dxl2dx(nn2) = 1.0_dp/4._dp - dxorig2dx(nn2)
            dyl2dy(nn2) = 1.0_dp/4._dp - dyorig2dy(nn2)
            dzl2dz(nn2) = 1.0_dp/4._dp - dzorig2dz(nn2)

            dxl2dx(nn3) = 1.0_dp/4._dp - dxorig2dx(nn3)
            dyl2dy(nn3) = 1.0_dp/4._dp - dyorig2dy(nn3)
            dzl2dz(nn3) = 1.0_dp/4._dp - dzorig2dz(nn3)

            dxl2dx(nn4) = 1.0_dp/4._dp - dxorig2dx(nn4)
            dyl2dy(nn4) = 1.0_dp/4._dp - dyorig2dy(nn4)
            dzl2dz(nn4) = 1.0_dp/4._dp - dzorig2dz(nn4)

            dxdtl = (grid%dxdt(n1) + grid%dxdt(n2) + grid%dxdt(n3)             &
                   + grid%dxdt(n4))/4._dp
            dydtl = (grid%dydt(n1) + grid%dydt(n2) + grid%dydt(n3)             &
                   + grid%dydt(n4))/4._dp
            dzdtl = (grid%dzdt(n1) + grid%dzdt(n2) + grid%dzdt(n3)             &
                   + grid%dzdt(n4))/4._dp

            if ( noninertial ) then
              ddxdtldx(nn1,1) = my_0
              ddxdtldy(nn1,1) = -zrotrate_ni/my_4
              ddxdtldz(nn1,1) =  yrotrate_ni/my_4

              ddydtldx(nn1,1) =  zrotrate_ni/my_4
              ddydtldy(nn1,1) = my_0
              ddydtldz(nn1,1) = -xrotrate_ni/my_4

              ddzdtldx(nn1,1) = -yrotrate_ni/my_4
              ddzdtldy(nn1,1) =  xrotrate_ni/my_4
              ddzdtldz(nn1,1) = my_0

              ddxdtldx(nn2,1) = my_0
              ddxdtldy(nn2,1) = -zrotrate_ni/my_4
              ddxdtldz(nn2,1) =  yrotrate_ni/my_4

              ddydtldx(nn2,1) =  zrotrate_ni/my_4
              ddydtldy(nn2,1) = my_0
              ddydtldz(nn2,1) = -xrotrate_ni/my_4

              ddzdtldx(nn2,1) = -yrotrate_ni/my_4
              ddzdtldy(nn2,1) =  xrotrate_ni/my_4
              ddzdtldz(nn2,1) = my_0

              ddxdtldx(nn3,1) = my_0
              ddxdtldy(nn3,1) = -zrotrate_ni/my_4
              ddxdtldz(nn3,1) =  yrotrate_ni/my_4

              ddydtldx(nn3,1) =  zrotrate_ni/my_4
              ddydtldy(nn3,1) = my_0
              ddydtldz(nn3,1) = -xrotrate_ni/my_4

              ddzdtldx(nn3,1) = -yrotrate_ni/my_4
              ddzdtldy(nn3,1) =  xrotrate_ni/my_4
              ddzdtldz(nn3,1) = my_0

              ddxdtldx(nn4,1) = my_0
              ddxdtldy(nn4,1) = -zrotrate_ni/my_4
              ddxdtldz(nn4,1) =  yrotrate_ni/my_4

              ddydtldx(nn4,1) =  zrotrate_ni/my_4
              ddydtldy(nn4,1) = my_0
              ddydtldz(nn4,1) = -xrotrate_ni/my_4

              ddzdtldx(nn4,1) = -yrotrate_ni/my_4
              ddzdtldy(nn4,1) =  xrotrate_ni/my_4
              ddzdtldz(nn4,1) = my_0
            else
              do i = 1, getg%ntp
                ddxdtldx(nn1,i) = time_coeff(i)/4._dp
                ddydtldy(nn1,i) = time_coeff(i)/4._dp
                ddzdtldz(nn1,i) = time_coeff(i)/4._dp

                ddxdtldx(nn2,i) = time_coeff(i)/4._dp
                ddydtldy(nn2,i) = time_coeff(i)/4._dp
                ddzdtldz(nn2,i) = time_coeff(i)/4._dp

                ddxdtldx(nn3,i) = time_coeff(i)/4._dp
                ddydtldy(nn3,i) = time_coeff(i)/4._dp
                ddzdtldz(nn3,i) = time_coeff(i)/4._dp

                ddxdtldx(nn4,i) = time_coeff(i)/4._dp
                ddydtldy(nn4,i) = time_coeff(i)/4._dp
                ddzdtldz(nn4,i) = time_coeff(i)/4._dp
              end do
            endif

          else

!           tria face

            xl1 = ((grid%x(n1)-xorig1) + (grid%x(n2)-xorig1)                   &
                 + (grid%x(n3)-xorig1))/3._dp
            yl1 = ((grid%y(n1)-yorig1) + (grid%y(n2)-yorig1)                   &
                 + (grid%y(n3)-yorig1))/3._dp
            zl1 = ((grid%z(n1)-zorig1) + (grid%z(n2)-zorig1)                   &
                 + (grid%z(n3)-zorig1))/3._dp

            dxl1dx(nn1) = 1.0_dp/3._dp - dxorig1dx(nn1)
            dyl1dy(nn1) = 1.0_dp/3._dp - dyorig1dy(nn1)
            dzl1dz(nn1) = 1.0_dp/3._dp - dzorig1dz(nn1)

            dxl1dx(nn2) = 1.0_dp/3._dp - dxorig1dx(nn2)
            dyl1dy(nn2) = 1.0_dp/3._dp - dyorig1dy(nn2)
            dzl1dz(nn2) = 1.0_dp/3._dp - dzorig1dz(nn2)

            dxl1dx(nn3) = 1.0_dp/3._dp - dxorig1dx(nn3)
            dyl1dy(nn3) = 1.0_dp/3._dp - dyorig1dy(nn3)
            dzl1dz(nn3) = 1.0_dp/3._dp - dzorig1dz(nn3)

            xl2 = ((grid%x(n1)-xorig2) + (grid%x(n2)-xorig2)                   &
                 + (grid%x(n3)-xorig2))/3._dp
            yl2 = ((grid%y(n1)-yorig2) + (grid%y(n2)-yorig2)                   &
                 + (grid%y(n3)-yorig2))/3._dp
            zl2 = ((grid%z(n1)-zorig2) + (grid%z(n2)-zorig2)                   &
                 + (grid%z(n3)-zorig2))/3._dp

            dxl2dx(nn1) = 1.0_dp/3._dp - dxorig2dx(nn1)
            dyl2dy(nn1) = 1.0_dp/3._dp - dyorig2dy(nn1)
            dzl2dz(nn1) = 1.0_dp/3._dp - dzorig2dz(nn1)

            dxl2dx(nn2) = 1.0_dp/3._dp - dxorig2dx(nn2)
            dyl2dy(nn2) = 1.0_dp/3._dp - dyorig2dy(nn2)
            dzl2dz(nn2) = 1.0_dp/3._dp - dzorig2dz(nn2)

            dxl2dx(nn3) = 1.0_dp/3._dp - dxorig2dx(nn3)
            dyl2dy(nn3) = 1.0_dp/3._dp - dyorig2dy(nn3)
            dzl2dz(nn3) = 1.0_dp/3._dp - dzorig2dz(nn3)

            dxdtl = (grid%dxdt(n1) + grid%dxdt(n2) + grid%dxdt(n3))/3._dp
            dydtl = (grid%dydt(n1) + grid%dydt(n2) + grid%dydt(n3))/3._dp
            dzdtl = (grid%dzdt(n1) + grid%dzdt(n2) + grid%dzdt(n3))/3._dp

            if ( noninertial ) then
              ddxdtldx(nn1,1) = my_0
              ddxdtldy(nn1,1) = -zrotrate_ni/my_3
              ddxdtldz(nn1,1) =  yrotrate_ni/my_3

              ddydtldx(nn1,1) =  zrotrate_ni/my_3
              ddydtldy(nn1,1) = my_0
              ddydtldz(nn1,1) = -xrotrate_ni/my_3

              ddzdtldx(nn1,1) = -yrotrate_ni/my_3
              ddzdtldy(nn1,1) =  xrotrate_ni/my_3
              ddzdtldz(nn1,1) = my_0

              ddxdtldx(nn2,1) = my_0
              ddxdtldy(nn2,1) = -zrotrate_ni/my_3
              ddxdtldz(nn2,1) =  yrotrate_ni/my_3

              ddydtldx(nn2,1) =  zrotrate_ni/my_3
              ddydtldy(nn2,1) = my_0
              ddydtldz(nn2,1) = -xrotrate_ni/my_3

              ddzdtldx(nn2,1) = -yrotrate_ni/my_3
              ddzdtldy(nn2,1) =  xrotrate_ni/my_3
              ddzdtldz(nn2,1) = my_0

              ddxdtldx(nn3,1) = my_0
              ddxdtldy(nn3,1) = -zrotrate_ni/my_3
              ddxdtldz(nn3,1) =  yrotrate_ni/my_3

              ddydtldx(nn3,1) =  zrotrate_ni/my_3
              ddydtldy(nn3,1) = my_0
              ddydtldz(nn3,1) = -xrotrate_ni/my_3

              ddzdtldx(nn3,1) = -yrotrate_ni/my_3
              ddzdtldy(nn3,1) =  xrotrate_ni/my_3
              ddzdtldz(nn3,1) = my_0
            else
              do i = 1, getg%ntp
                ddxdtldx(nn1,i) = time_coeff(i)/3._dp
                ddydtldy(nn1,i) = time_coeff(i)/3._dp
                ddzdtldz(nn1,i) = time_coeff(i)/3._dp

                ddxdtldx(nn2,i) = time_coeff(i)/3._dp
                ddydtldy(nn2,i) = time_coeff(i)/3._dp
                ddzdtldz(nn2,i) = time_coeff(i)/3._dp

                ddxdtldx(nn3,i) = time_coeff(i)/3._dp
                ddydtldy(nn3,i) = time_coeff(i)/3._dp
                ddzdtldz(nn3,i) = time_coeff(i)/3._dp
              end do
            endif

          end if

!         compute right face centroid

          n5 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,5),n)

          nn5 = grid%elem(ielem)%local_e2n(ie,5)

          if (grid%elem(ielem)%local_e2n(ie,6) /= 0) then

!           quad face

            n6 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,6),n)

            nn6 = grid%elem(ielem)%local_e2n(ie,6)

            xr1 = ((grid%x(n1)-xorig1) + (grid%x(n2)-xorig1) +                 &
                   (grid%x(n5)-xorig1) + (grid%x(n6)-xorig1))/4._dp
            yr1 = ((grid%y(n1)-yorig1) + (grid%y(n2)-yorig1) +                 &
                   (grid%y(n5)-yorig1) + (grid%y(n6)-yorig1))/4._dp
            zr1 = ((grid%z(n1)-zorig1) + (grid%z(n2)-zorig1) +                 &
                   (grid%z(n5)-zorig1) + (grid%z(n6)-zorig1))/4._dp

            dxr1dx(nn1) = 1.0_dp/4._dp - dxorig1dx(nn1)
            dyr1dy(nn1) = 1.0_dp/4._dp - dyorig1dy(nn1)
            dzr1dz(nn1) = 1.0_dp/4._dp - dzorig1dz(nn1)

            dxr1dx(nn2) = 1.0_dp/4._dp - dxorig1dx(nn2)
            dyr1dy(nn2) = 1.0_dp/4._dp - dyorig1dy(nn2)
            dzr1dz(nn2) = 1.0_dp/4._dp - dzorig1dz(nn2)

            dxr1dx(nn5) = 1.0_dp/4._dp - dxorig1dx(nn5)
            dyr1dy(nn5) = 1.0_dp/4._dp - dyorig1dy(nn5)
            dzr1dz(nn5) = 1.0_dp/4._dp - dzorig1dz(nn5)

            dxr1dx(nn6) = 1.0_dp/4._dp - dxorig1dx(nn6)
            dyr1dy(nn6) = 1.0_dp/4._dp - dyorig1dy(nn6)
            dzr1dz(nn6) = 1.0_dp/4._dp - dzorig1dz(nn6)

            xr2 = ((grid%x(n1)-xorig2) + (grid%x(n2)-xorig2) +                 &
                   (grid%x(n5)-xorig2) + (grid%x(n6)-xorig2))/4._dp
            yr2 = ((grid%y(n1)-yorig2) + (grid%y(n2)-yorig2) +                 &
                   (grid%y(n5)-yorig2) + (grid%y(n6)-yorig2))/4._dp
            zr2 = ((grid%z(n1)-zorig2) + (grid%z(n2)-zorig2) +                 &
                   (grid%z(n5)-zorig2) + (grid%z(n6)-zorig2))/4._dp

            dxr2dx(nn1) = 1.0_dp/4._dp - dxorig2dx(nn1)
            dyr2dy(nn1) = 1.0_dp/4._dp - dyorig2dy(nn1)
            dzr2dz(nn1) = 1.0_dp/4._dp - dzorig2dz(nn1)

            dxr2dx(nn2) = 1.0_dp/4._dp - dxorig2dx(nn2)
            dyr2dy(nn2) = 1.0_dp/4._dp - dyorig2dy(nn2)
            dzr2dz(nn2) = 1.0_dp/4._dp - dzorig2dz(nn2)

            dxr2dx(nn5) = 1.0_dp/4._dp - dxorig2dx(nn5)
            dyr2dy(nn5) = 1.0_dp/4._dp - dyorig2dy(nn5)
            dzr2dz(nn5) = 1.0_dp/4._dp - dzorig2dz(nn5)

            dxr2dx(nn6) = 1.0_dp/4._dp - dxorig2dx(nn6)
            dyr2dy(nn6) = 1.0_dp/4._dp - dyorig2dy(nn6)
            dzr2dz(nn6) = 1.0_dp/4._dp - dzorig2dz(nn6)

! facespeed stuff

            dxdtr = (grid%dxdt(n1) + grid%dxdt(n2) + grid%dxdt(n5)             &
                   + grid%dxdt(n6))/4._dp
            dydtr = (grid%dydt(n1) + grid%dydt(n2) + grid%dydt(n5)             &
                   + grid%dydt(n6))/4._dp
            dzdtr = (grid%dzdt(n1) + grid%dzdt(n2) + grid%dzdt(n5)             &
                   + grid%dzdt(n6))/4._dp

            if ( noninertial ) then
              ddxdtrdx(nn1,1) = my_0
              ddxdtrdy(nn1,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn1,1) =  yrotrate_ni/my_3

              ddydtrdx(nn1,1) =  zrotrate_ni/my_3
              ddydtrdy(nn1,1) = my_0
              ddydtrdz(nn1,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn1,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn1,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn1,1) = my_0

              ddxdtrdx(nn2,1) = my_0
              ddxdtrdy(nn2,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn2,1) =  yrotrate_ni/my_3

              ddydtrdx(nn2,1) =  zrotrate_ni/my_3
              ddydtrdy(nn2,1) = my_0
              ddydtrdz(nn2,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn2,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn2,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn2,1) = my_0

              ddxdtrdx(nn5,1) = my_0
              ddxdtrdy(nn5,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn5,1) =  yrotrate_ni/my_3

              ddydtrdx(nn5,1) =  zrotrate_ni/my_3
              ddydtrdy(nn5,1) = my_0
              ddydtrdz(nn5,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn5,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn5,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn5,1) = my_0

              ddxdtrdx(nn6,1) = my_0
              ddxdtrdy(nn6,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn6,1) =  yrotrate_ni/my_3

              ddydtrdx(nn6,1) =  zrotrate_ni/my_3
              ddydtrdy(nn6,1) = my_0
              ddydtrdz(nn6,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn6,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn6,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn6,1) = my_0
            else
              do i = 1, getg%ntp
                ddxdtrdx(nn1,i) = time_coeff(i)/4._dp
                ddydtrdy(nn1,i) = time_coeff(i)/4._dp
                ddzdtrdz(nn1,i) = time_coeff(i)/4._dp

                ddxdtrdx(nn2,i) = time_coeff(i)/4._dp
                ddydtrdy(nn2,i) = time_coeff(i)/4._dp
                ddzdtrdz(nn2,i) = time_coeff(i)/4._dp

                ddxdtrdx(nn5,i) = time_coeff(i)/4._dp
                ddydtrdy(nn5,i) = time_coeff(i)/4._dp
                ddzdtrdz(nn5,i) = time_coeff(i)/4._dp

                ddxdtrdx(nn6,i) = time_coeff(i)/4._dp
                ddydtrdy(nn6,i) = time_coeff(i)/4._dp
                ddzdtrdz(nn6,i) = time_coeff(i)/4._dp
              end do
            endif

          else

!           tria face

            xr1 = ((grid%x(n1)-xorig1) + (grid%x(n2)-xorig1)                   &
                 + (grid%x(n5)-xorig1))/3._dp
            yr1 = ((grid%y(n1)-yorig1) + (grid%y(n2)-yorig1)                   &
                 + (grid%y(n5)-yorig1))/3._dp
            zr1 = ((grid%z(n1)-zorig1) + (grid%z(n2)-zorig1)                   &
                 + (grid%z(n5)-zorig1))/3._dp

            dxr1dx(nn1) = 1.0_dp/3._dp - dxorig1dx(nn1)
            dyr1dy(nn1) = 1.0_dp/3._dp - dyorig1dy(nn1)
            dzr1dz(nn1) = 1.0_dp/3._dp - dzorig1dz(nn1)

            dxr1dx(nn2) = 1.0_dp/3._dp - dxorig1dx(nn2)
            dyr1dy(nn2) = 1.0_dp/3._dp - dyorig1dy(nn2)
            dzr1dz(nn2) = 1.0_dp/3._dp - dzorig1dz(nn2)

            dxr1dx(nn5) = 1.0_dp/3._dp - dxorig1dx(nn5)
            dyr1dy(nn5) = 1.0_dp/3._dp - dyorig1dy(nn5)
            dzr1dz(nn5) = 1.0_dp/3._dp - dzorig1dz(nn5)

            xr2 = ((grid%x(n1)-xorig2) + (grid%x(n2)-xorig2)                   &
                 + (grid%x(n5)-xorig2))/3._dp
            yr2 = ((grid%y(n1)-yorig2) + (grid%y(n2)-yorig2)                   &
                 + (grid%y(n5)-yorig2))/3._dp
            zr2 = ((grid%z(n1)-zorig2) + (grid%z(n2)-zorig2)                   &
                 + (grid%z(n5)-zorig2))/3._dp

            dxr2dx(nn1) = 1.0_dp/3._dp - dxorig2dx(nn1)
            dyr2dy(nn1) = 1.0_dp/3._dp - dyorig2dy(nn1)
            dzr2dz(nn1) = 1.0_dp/3._dp - dzorig2dz(nn1)

            dxr2dx(nn2) = 1.0_dp/3._dp - dxorig2dx(nn2)
            dyr2dy(nn2) = 1.0_dp/3._dp - dyorig2dy(nn2)
            dzr2dz(nn2) = 1.0_dp/3._dp - dzorig2dz(nn2)

            dxr2dx(nn5) = 1.0_dp/3._dp - dxorig2dx(nn5)
            dyr2dy(nn5) = 1.0_dp/3._dp - dyorig2dy(nn5)
            dzr2dz(nn5) = 1.0_dp/3._dp - dzorig2dz(nn5)

! facespeed stuff

            dxdtr = (grid%dxdt(n1) + grid%dxdt(n2) + grid%dxdt(n5))/3._dp
            dydtr = (grid%dydt(n1) + grid%dydt(n2) + grid%dydt(n5))/3._dp
            dzdtr = (grid%dzdt(n1) + grid%dzdt(n2) + grid%dzdt(n5))/3._dp

            if ( noninertial ) then
              ddxdtrdx(nn1,1) = my_0
              ddxdtrdy(nn1,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn1,1) =  yrotrate_ni/my_3

              ddydtrdx(nn1,1) =  zrotrate_ni/my_3
              ddydtrdy(nn1,1) = my_0
              ddydtrdz(nn1,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn1,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn1,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn1,1) = my_0

              ddxdtrdx(nn2,1) = my_0
              ddxdtrdy(nn2,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn2,1) =  yrotrate_ni/my_3

              ddydtrdx(nn2,1) =  zrotrate_ni/my_3
              ddydtrdy(nn2,1) = my_0
              ddydtrdz(nn2,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn2,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn2,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn2,1) = my_0

              ddxdtrdx(nn5,1) = my_0
              ddxdtrdy(nn5,1) = -zrotrate_ni/my_3
              ddxdtrdz(nn5,1) =  yrotrate_ni/my_3

              ddydtrdx(nn5,1) =  zrotrate_ni/my_3
              ddydtrdy(nn5,1) = my_0
              ddydtrdz(nn5,1) = -xrotrate_ni/my_3

              ddzdtrdx(nn5,1) = -yrotrate_ni/my_3
              ddzdtrdy(nn5,1) =  xrotrate_ni/my_3
              ddzdtrdz(nn5,1) = my_0
            else
              do i = 1, getg%ntp
                ddxdtrdx(nn1,i) = time_coeff(i)/3._dp
                ddydtrdy(nn1,i) = time_coeff(i)/3._dp
                ddzdtrdz(nn1,i) = time_coeff(i)/3._dp

                ddxdtrdx(nn2,i) = time_coeff(i)/3._dp
                ddydtrdy(nn2,i) = time_coeff(i)/3._dp
                ddzdtrdz(nn2,i) = time_coeff(i)/3._dp

                ddxdtrdx(nn5,i) = time_coeff(i)/3._dp
                ddydtrdy(nn5,i) = time_coeff(i)/3._dp
                ddzdtrdz(nn5,i) = time_coeff(i)/3._dp
              end do
            endif

          end if

!         local (positive) normals point from n1 to n2; ensure these point in
!         the same direction dictated by the eptr array (positive pointing from
!         eptr(1,edge) to eptr(2,edge))

          fact = 1._dp
          if (n1 == grid%eptr(2,edge)) fact = -1._dp

!         (dual) triangle xm-xr-xc

          areax1 = 0.5_dp*((yr1-ym1)*(zc(grid%elem(ielem)%local_e2n(ie,1))-zm1)&
                         - (zr1-zm1)*(yc(grid%elem(ielem)%local_e2n(ie,1))-ym1))
            dareax1dx(:)=0.0_dp
            dareax1dy(:)=0.5_dp*( (dyr1dy(:)-dym1dy(:))*                       &
                                     (zc(grid%elem(ielem)%local_e2n(ie,1))-zm1)&
                         - (zr1-zm1)*(dycdy(grid%elem(ielem)%local_e2n(ie,1),:)&
                                -dym1dy(:)) )
          dareax1dz(:)=0.5_dp*((yr1-ym1)*                                      &
                       (dzcdz(grid%elem(ielem)%local_e2n(ie,1),:) -dzm1dz(:))  &
            - (dzr1dz(:)-dzm1dz(:))*(yc(grid%elem(ielem)%local_e2n(ie,1))-ym1) )

          areay1 = 0.5_dp*((zr1-zm1)*(xc(grid%elem(ielem)%local_e2n(ie,1))-xm1)&
                         - (xr1-xm1)*(zc(grid%elem(ielem)%local_e2n(ie,1))-zm1))
          dareay1dx(:)=0.5_dp*( (zr1-zm1)*                                     &
                (dxcdx(grid%elem(ielem)%local_e2n(ie,1),:)-dxm1dx(:))          &
             - (dxr1dx(:)-dxm1dx(:))*(zc(grid%elem(ielem)%local_e2n(ie,1))-zm1))
            dareay1dy(:)=0.0_dp
            dareay1dz(:)=0.5_dp*( (dzr1dz(:)-dzm1dz(:))*                       &
                                (xc(grid%elem(ielem)%local_e2n(ie,1))-xm1)     &
              - (xr1-xm1)*(dzcdz(grid%elem(ielem)%local_e2n(ie,1),:)-dzm1dz(:)))

          areaz1 = 0.5_dp*((xr1-xm1)*(yc(grid%elem(ielem)%local_e2n(ie,1))-ym1)&
                         - (yr1-ym1)*(xc(grid%elem(ielem)%local_e2n(ie,1))-xm1))
            dareaz1dx(:)=0.5_dp*( (dxr1dx(:)-dxm1dx(:))*                       &
                                     (yc(grid%elem(ielem)%local_e2n(ie,1))-ym1)&
                         - (yr1-ym1)*(dxcdx(grid%elem(ielem)%local_e2n(ie,1),:)&
                                -dxm1dx(:)) )
          dareaz1dy(:)=0.5_dp*( (xr1-xm1)*                                     &
               (dycdy(grid%elem(ielem)%local_e2n(ie,1),:) -dym1dy(:))          &
             - (dyr1dy(:)-dym1dy(:))*(xc(grid%elem(ielem)%local_e2n(ie,1))-xm1))
            dareaz1dz(:)=0.0_dp


          areax2 = 0.5_dp*((yr2-ym2)*(zc(grid%elem(ielem)%local_e2n(ie,2))-zm2)&
                         - (zr2-zm2)*(yc(grid%elem(ielem)%local_e2n(ie,2))-ym2))
            dareax2dx(:)=0.0_dp
            dareax2dy(:)=0.5_dp*( (dyr2dy(:)-dym2dy(:))*                       &
                                     (zc(grid%elem(ielem)%local_e2n(ie,2))-zm2)&
              - (zr2-zm2)*(dycdy(grid%elem(ielem)%local_e2n(ie,2),:)-dym2dy(:)))
          dareax2dz(:)=0.5_dp*( (yr2-ym2)*                                     &
                         (dzcdz(grid%elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:)) &
             - (dzr2dz(:)-dzm2dz(:))*(yc(grid%elem(ielem)%local_e2n(ie,2))-ym2))

          areay2 = 0.5_dp*((zr2-zm2)*(xc(grid%elem(ielem)%local_e2n(ie,2))-xm2)&
                          -(xr2-xm2)*(zc(grid%elem(ielem)%local_e2n(ie,2))-zm2))
          dareay2dx(:)=0.5_dp*((zr2-zm2)*                                      &
                     (dxcdx(grid%elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:))     &
             - (dxr2dx(:)-dxm2dx(:))*(zc(grid%elem(ielem)%local_e2n(ie,2))-zm2))
            dareay2dy(:)=0.0_dp
            dareay2dz(:)=0.5_dp*( (dzr2dz(:)-dzm2dz(:))*                       &
                                     (xc(grid%elem(ielem)%local_e2n(ie,2))-xm2)&
              - (xr2-xm2)*(dzcdz(grid%elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:)))

          areaz2 = 0.5_dp*((xr2-xm2)*(yc(grid%elem(ielem)%local_e2n(ie,2))-ym2)&
                         - (yr2-ym2)*(xc(grid%elem(ielem)%local_e2n(ie,2))-xm2))
            dareaz2dx(:)=0.5_dp*( (dxr2dx(:)-dxm2dx(:))*                       &
                                     (yc(grid%elem(ielem)%local_e2n(ie,2))-ym2)&
              - (yr2-ym2)*(dxcdx(grid%elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:)))
          dareaz2dy(:)=0.5_dp*( (xr2-xm2)*                                     &
                  (dycdy(grid%elem(ielem)%local_e2n(ie,2),:)-dym2dy(:))        &
             - (dyr2dy(:)-dym2dy(:))*(xc(grid%elem(ielem)%local_e2n(ie,2))-xm2))
            dareaz2dz(:)=0.0_dp

!         xn(edge) = xn(edge) + 0.5_dp*(areax1+areax2)*fact
!         yn(edge) = yn(edge) + 0.5_dp*(areay1+areay2)*fact
!         zn(edge) = zn(edge) + 0.5_dp*(areaz1+areaz2)*fact

          dkxdx(:) = dkxdx(:) + 0.0_dp
          dkxdy(:) = dkxdy(:) + 0.5_dp*(dareax1dy(:) + dareax2dy(:))*fact
          dkxdz(:) = dkxdz(:) + 0.5_dp*(dareax1dz(:) + dareax2dz(:))*fact

          dkydx(:) = dkydx(:) + 0.5_dp*(dareay1dx(:) + dareay2dx(:))*fact
          dkydy(:) = dkydy(:) + 0.0_dp
          dkydz(:) = dkydz(:) + 0.5_dp*(dareay1dz(:) + dareay2dz(:))*fact

          dkzdx(:) = dkzdx(:) + 0.5_dp*(dareaz1dx(:) + dareaz2dx(:))*fact
          dkzdy(:) = dkzdy(:) + 0.5_dp*(dareaz1dy(:) + dareaz2dy(:))*fact
          dkzdz(:) = dkzdz(:) + 0.0_dp

! facespeed stuff

          dxdtavg = (dxdtm + dxdtr + dxdtc)/3._dp
          dydtavg = (dydtm + dydtr + dydtc)/3._dp
          dzdtavg = (dzdtm + dzdtr + dzdtc)/3._dp

          if ( noninertial ) then
!           term1 = dxdtavg*areax1 + dydtavg*areay1 + dzdtavg*areaz1
!           term2 = dxdtavg*areax2 + dydtavg*areay2 + dzdtavg*areaz2
            ddxdtavgdx(:,1)=(ddxdtmdx(:,1)+ ddxdtrdx(:,1) + ddxdtcdx(:,1))/3._dp
            ddxdtavgdy(:,1)=(ddxdtmdy(:,1)+ ddxdtrdy(:,1) + ddxdtcdy(:,1))/3._dp
            ddxdtavgdz(:,1)=(ddxdtmdz(:,1)+ ddxdtrdz(:,1) + ddxdtcdz(:,1))/3._dp

            ddydtavgdx(:,1)=(ddydtmdx(:,1)+ ddydtrdx(:,1) + ddydtcdx(:,1))/3._dp
            ddydtavgdy(:,1)=(ddydtmdy(:,1)+ ddydtrdy(:,1) + ddydtcdy(:,1))/3._dp
            ddydtavgdz(:,1)=(ddydtmdz(:,1)+ ddydtrdz(:,1) + ddydtcdz(:,1))/3._dp

            ddzdtavgdx(:,1)=(ddzdtmdx(:,1)+ ddzdtrdx(:,1) + ddzdtcdx(:,1))/3._dp
            ddzdtavgdy(:,1)=(ddzdtmdy(:,1)+ ddzdtrdy(:,1) + ddzdtcdy(:,1))/3._dp
            ddzdtavgdz(:,1)=(ddzdtmdz(:,1)+ ddzdtrdz(:,1) + ddzdtcdz(:,1))/3._dp

            dterm1dx(:,1) = dxdtavg*dareax1dx(:) + areax1*ddxdtavgdx(:,1)      &
                          + dydtavg*dareay1dx(:) + areay1*ddydtavgdx(:,1)      &
                          + dzdtavg*dareaz1dx(:) + areaz1*ddzdtavgdx(:,1)
            dterm1dy(:,1) = dxdtavg*dareax1dy(:) + areax1*ddxdtavgdy(:,1)      &
                          + dydtavg*dareay1dy(:) + areay1*ddydtavgdy(:,1)      &
                          + dzdtavg*dareaz1dy(:) + areaz1*ddzdtavgdy(:,1)
            dterm1dz(:,1) = dxdtavg*dareax1dz(:) + areax1*ddxdtavgdz(:,1)      &
                          + dydtavg*dareay1dz(:) + areay1*ddydtavgdz(:,1)      &
                          + dzdtavg*dareaz1dz(:) + areaz1*ddzdtavgdz(:,1)

            dterm2dx(:,1) = dxdtavg*dareax2dx(:) + areax2*ddxdtavgdx(:,1)      &
                          + dydtavg*dareay2dx(:) + areay2*ddydtavgdx(:,1)      &
                          + dzdtavg*dareaz2dx(:) + areaz2*ddzdtavgdx(:,1)
            dterm2dy(:,1) = dxdtavg*dareax2dy(:) + areax2*ddxdtavgdy(:,1)      &
                          + dydtavg*dareay2dy(:) + areay2*ddydtavgdy(:,1)      &
                          + dzdtavg*dareaz2dy(:) + areaz2*ddzdtavgdy(:,1)
            dterm2dz(:,1) = dxdtavg*dareax2dz(:) + areax2*ddxdtavgdz(:,1)      &
                          + dydtavg*dareay2dz(:) + areay2*ddydtavgdz(:,1)      &
                          + dzdtavg*dareaz2dz(:) + areaz2*ddzdtavgdz(:,1)

!           facespeed(edge) = facespeed(edge) + 0.5_dp*(term1+term2)*fact
            dfacespeeddx(:,1) = dfacespeeddx(:,1) + 0.5_dp*(dterm1dx(:,1)+     &
                                                            dterm2dx(:,1))*fact
            dfacespeeddy(:,1) = dfacespeeddy(:,1) + 0.5_dp*(dterm1dy(:,1)+     &
                                                            dterm2dy(:,1))*fact
            dfacespeeddz(:,1) = dfacespeeddz(:,1) + 0.5_dp*(dterm1dz(:,1)+     &
                                                            dterm2dz(:,1))*fact
          else
            do i = 1, getg%ntp
              ddxdtavgdx(:,i)=(ddxdtmdx(:,i)+ddxdtrdx(:,i)+ddxdtcdx(:,i))/3._dp
              ddydtavgdy(:,i)=(ddydtmdy(:,i)+ddydtrdy(:,i)+ddydtcdy(:,i))/3._dp
              ddzdtavgdz(:,i)=(ddzdtmdz(:,i)+ddzdtrdz(:,i)+ddzdtcdz(:,i))/3._dp

! Area only depends on coords at current time level

!           term1 = dxdtavg*areax1 + dydtavg*areay1 + dzdtavg*areaz1
!           term2 = dxdtavg*areax2 + dydtavg*areay2 + dzdtavg*areaz2

              if ( i == 1 ) then
                dterm1dx(:,i) = dxdtavg*dareax1dx(:) + areax1*ddxdtavgdx(:,i) +&
                                dydtavg*dareay1dx(:) + dzdtavg*dareaz1dx(:)
                dterm1dy(:,i) = dxdtavg*dareax1dy(:) + areay1*ddydtavgdy(:,i) +&
                                dydtavg*dareay1dy(:) + dzdtavg*dareaz1dy(:)
                dterm1dz(:,i) = dxdtavg*dareax1dz(:) + areaz1*ddzdtavgdz(:,i) +&
                                dydtavg*dareay1dz(:) + dzdtavg*dareaz1dz(:)

                dterm2dx(:,i) = dxdtavg*dareax2dx(:) + areax2*ddxdtavgdx(:,i) +&
                                dydtavg*dareay2dx(:) + dzdtavg*dareaz2dx(:)
                dterm2dy(:,i) = dxdtavg*dareax2dy(:) + areay2*ddydtavgdy(:,i) +&
                                dydtavg*dareay2dy(:) + dzdtavg*dareaz2dy(:)
                dterm2dz(:,i) = dxdtavg*dareax2dz(:) + areaz2*ddzdtavgdz(:,i) +&
                                dydtavg*dareay2dz(:) + dzdtavg*dareaz2dz(:)
              else
                dterm1dx(:,i) = areax1*ddxdtavgdx(:,i)
                dterm1dy(:,i) = areay1*ddydtavgdy(:,i)
                dterm1dz(:,i) = areaz1*ddzdtavgdz(:,i)

                dterm2dx(:,i) = areax2*ddxdtavgdx(:,i)
                dterm2dy(:,i) = areay2*ddydtavgdy(:,i)
                dterm2dz(:,i) = areaz2*ddzdtavgdz(:,i)
              endif

!           facespeed(edge) = facespeed(edge) + 0.5_dp*(term1+term2)*fact
              dfacespeeddx(:,i)=dfacespeeddx(:,i) + 0.5_dp*(dterm1dx(:,i)+     &
                                                            dterm2dx(:,i))*fact
              dfacespeeddy(:,i)=dfacespeeddy(:,i) + 0.5_dp*(dterm1dy(:,i)+     &
                                                            dterm2dy(:,i))*fact
              dfacespeeddz(:,i)=dfacespeeddz(:,i) + 0.5_dp*(dterm1dz(:,i)+     &
                                                            dterm2dz(:,i))*fact
            end do
          endif

!         (dual) triangle xm-xc-xl

          areax1 = 0.5_dp*((yc(grid%elem(ielem)%local_e2n(ie,1))-ym1)*(zl1-zm1)&
                          -(zc(grid%elem(ielem)%local_e2n(ie,1))-zm1)*(yl1-ym1))
            dareax1dx(:)=0.0_dp
            dareax1dy(:)=0.5_dp*( (dycdy(grid%elem(ielem)%local_e2n(ie,1),:)   &
                        -dym1dy(:))*(zl1-zm1)                                  &
             - (zc(grid%elem(ielem)%local_e2n(ie,1))-zm1)*(dyl1dy(:)-dym1dy(:)))
            dareax1dz(:)=0.5_dp*( (yc(grid%elem(ielem)%local_e2n(ie,1))-ym1)*  &
                      (dzl1dz(:)-dzm1dz(:))                                    &
              - (dzcdz(grid%elem(ielem)%local_e2n(ie,1),:)-dzm1dz(:))*(yl1-ym1))

          areay1 = 0.5_dp*((zc(grid%elem(ielem)%local_e2n(ie,1))-zm1)*(xl1-xm1)&
                          -(xc(grid%elem(ielem)%local_e2n(ie,1))-xm1)*(zl1-zm1))
            dareay1dx(:)=0.5_dp*( (zc(grid%elem(ielem)%local_e2n(ie,1))-zm1)*  &
                       (dxl1dx(:)-dxm1dx(:))                                   &
              - (dxcdx(grid%elem(ielem)%local_e2n(ie,1),:)-dxm1dx(:))*(zl1-zm1))
            dareay1dy(:)=0.0_dp
            dareay1dz(:)=0.5_dp*( (dzcdz(grid%elem(ielem)%local_e2n(ie,1),:)-  &
                         dzm1dz(:))*(xl1-xm1)                                  &
             - (xc(grid%elem(ielem)%local_e2n(ie,1))-xm1)*(dzl1dz(:)-dzm1dz(:)))

          areaz1 = 0.5_dp*((xc(grid%elem(ielem)%local_e2n(ie,1))-xm1)*(yl1-ym1)&
                          -(yc(grid%elem(ielem)%local_e2n(ie,1))-ym1)*(xl1-xm1))
            dareaz1dx(:)=0.5_dp*( (dxcdx(grid%elem(ielem)%local_e2n(ie,1),:)-  &
                        dxm1dx(:))*(yl1-ym1)                                   &
                               - (yc(grid%elem(ielem)%local_e2n(ie,1))-ym1)*   &
                            (dxl1dx(:)-dxm1dx(:)) )
            dareaz1dy(:)=0.5_dp*( (xc(grid%elem(ielem)%local_e2n(ie,1))-xm1)*  &
                                  (dyl1dy(:)-dym1dy(:))                        &
              - (dycdy(grid%elem(ielem)%local_e2n(ie,1),:)-dym1dy(:))*(xl1-xm1))
            dareaz1dz(:)=0.0_dp

          areax2 = 0.5_dp*((yc(grid%elem(ielem)%local_e2n(ie,2))-ym2)*(zl2-zm2)&
                          -(zc(grid%elem(ielem)%local_e2n(ie,2))-zm2)*(yl2-ym2))
            dareax2dx(:)=0.0_dp
            dareax2dy(:)=0.5_dp*( (dycdy(grid%elem(ielem)%local_e2n(ie,2),:)-  &
                          dym2dy(:))*(zl2-zm2)                                 &
             - (zc(grid%elem(ielem)%local_e2n(ie,2))-zm2)*(dyl2dy(:)-dym2dy(:)))
            dareax2dz(:)=0.5_dp*( (yc(grid%elem(ielem)%local_e2n(ie,2))-ym2)*  &
                         (dzl2dz(:)-dzm2dz(:))                                 &
              - (dzcdz(grid%elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:))*(yl2-ym2))

          areay2 = 0.5_dp*((zc(grid%elem(ielem)%local_e2n(ie,2))-zm2)*(xl2-xm2)&
                          -(xc(grid%elem(ielem)%local_e2n(ie,2))-xm2)*(zl2-zm2))
            dareay2dx(:)=0.5_dp*( (zc(grid%elem(ielem)%local_e2n(ie,2))-zm2)   &
                        *(dxl2dx(:)-dxm2dx(:))                                 &
              - (dxcdx(grid%elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:))*(zl2-zm2))
            dareay2dy(:)=0.0_dp
            dareay2dz(:)=0.5_dp*( (dzcdz(grid%elem(ielem)%local_e2n(ie,2),:)   &
                        -dzm2dz(:))*(xl2-xm2)                                  &
             - (xc(grid%elem(ielem)%local_e2n(ie,2))-xm2)*(dzl2dz(:)-dzm2dz(:)))

          areaz2 = 0.5_dp*((xc(grid%elem(ielem)%local_e2n(ie,2))-xm2)*(yl2-ym2)&
                          -(yc(grid%elem(ielem)%local_e2n(ie,2))-ym2)*(xl2-xm2))
            dareaz2dx(:)=0.5_dp*( (dxcdx(grid%elem(ielem)%local_e2n(ie,2),:)-  &
                          dxm2dx(:))*(yl2-ym2)                                 &
             - (yc(grid%elem(ielem)%local_e2n(ie,2))-ym2)*(dxl2dx(:)-dxm2dx(:)))
            dareaz2dy(:)=0.5_dp*( (xc(grid%elem(ielem)%local_e2n(ie,2))-xm2)*  &
                       (dyl2dy(:)-dym2dy(:))                                   &
              - (dycdy(grid%elem(ielem)%local_e2n(ie,2),:)-dym2dy(:))*(xl2-xm2))
            dareaz2dz(:)=0.0_dp

!         xn(edge) = xn(edge) + 0.5_dp*(areax1+areax2)*fact
!         yn(edge) = yn(edge) + 0.5_dp*(areay1+areay2)*fact
!         zn(edge) = zn(edge) + 0.5_dp*(areaz1+areaz2)*fact

          dkxdx(:) = dkxdx(:) + 0.0_dp
          dkxdy(:) = dkxdy(:) + 0.5_dp*(dareax1dy(:) + dareax2dy(:))*fact
          dkxdz(:) = dkxdz(:) + 0.5_dp*(dareax1dz(:) + dareax2dz(:))*fact

          dkydx(:) = dkydx(:) + 0.5_dp*(dareay1dx(:) + dareay2dx(:))*fact
          dkydy(:) = dkydy(:) + 0.0_dp
          dkydz(:) = dkydz(:) + 0.5_dp*(dareay1dz(:) + dareay2dz(:))*fact

          dkzdx(:) = dkzdx(:) + 0.5_dp*(dareaz1dx(:) + dareaz2dx(:))*fact
          dkzdy(:) = dkzdy(:) + 0.5_dp*(dareaz1dy(:) + dareaz2dy(:))*fact
          dkzdz(:) = dkzdz(:) + 0.0_dp

! facespeed stuff

          dxdtavg = (dxdtm + dxdtl + dxdtc)/3._dp
          dydtavg = (dydtm + dydtl + dydtc)/3._dp
          dzdtavg = (dzdtm + dzdtl + dzdtc)/3._dp

          if ( noninertial ) then
            ddxdtavgdx(:,1)=(ddxdtmdx(:,1)+ ddxdtldx(:,1) + ddxdtcdx(:,1))/3._dp
            ddxdtavgdy(:,1)=(ddxdtmdy(:,1)+ ddxdtldy(:,1) + ddxdtcdy(:,1))/3._dp
            ddxdtavgdz(:,1)=(ddxdtmdz(:,1)+ ddxdtldz(:,1) + ddxdtcdz(:,1))/3._dp

            ddydtavgdx(:,1)=(ddydtmdx(:,1)+ ddydtldx(:,1) + ddydtcdx(:,1))/3._dp
            ddydtavgdy(:,1)=(ddydtmdy(:,1)+ ddydtldy(:,1) + ddydtcdy(:,1))/3._dp
            ddydtavgdz(:,1)=(ddydtmdz(:,1)+ ddydtldz(:,1) + ddydtcdz(:,1))/3._dp

            ddzdtavgdx(:,1)=(ddzdtmdx(:,1)+ ddzdtldx(:,1) + ddzdtcdx(:,1))/3._dp
            ddzdtavgdy(:,1)=(ddzdtmdy(:,1)+ ddzdtldy(:,1) + ddzdtcdy(:,1))/3._dp
            ddzdtavgdz(:,1)=(ddzdtmdz(:,1)+ ddzdtldz(:,1) + ddzdtcdz(:,1))/3._dp

!           term1    = dxdtavg*areax1 + dydtavg*areay1 + dzdtavg*areaz1
!           term2    = dxdtavg*areax2 + dydtavg*areay2 + dzdtavg*areaz2

            dterm1dx(:,1) = dxdtavg*dareax1dx(:) + areax1*ddxdtavgdx(:,1)      &
                          + dydtavg*dareay1dx(:) + areay1*ddydtavgdx(:,1)      &
                          + dzdtavg*dareaz1dx(:) + areaz1*ddzdtavgdx(:,1)
            dterm1dy(:,1) = dxdtavg*dareax1dy(:) + areax1*ddxdtavgdy(:,1)      &
                          + dydtavg*dareay1dy(:) + areay1*ddydtavgdy(:,1)      &
                          + dzdtavg*dareaz1dy(:) + areaz1*ddzdtavgdy(:,1)
            dterm1dz(:,1) = dxdtavg*dareax1dz(:) + areax1*ddxdtavgdz(:,1)      &
                          + dydtavg*dareay1dz(:) + areay1*ddydtavgdz(:,1)      &
                          + dzdtavg*dareaz1dz(:) + areaz1*ddzdtavgdz(:,1)

            dterm2dx(:,1) = dxdtavg*dareax2dx(:) + areax2*ddxdtavgdx(:,1)      &
                          + dydtavg*dareay2dx(:) + areay2*ddydtavgdx(:,1)      &
                          + dzdtavg*dareaz2dx(:) + areaz2*ddzdtavgdx(:,1)
            dterm2dy(:,1) = dxdtavg*dareax2dy(:) + areax2*ddxdtavgdy(:,1)      &
                          + dydtavg*dareay2dy(:) + areay2*ddydtavgdy(:,1)      &
                          + dzdtavg*dareaz2dy(:) + areaz2*ddzdtavgdy(:,1)
            dterm2dz(:,1) = dxdtavg*dareax2dz(:) + areax2*ddxdtavgdz(:,1)      &
                          + dydtavg*dareay2dz(:) + areay2*ddydtavgdz(:,1)      &
                          + dzdtavg*dareaz2dz(:) + areaz2*ddzdtavgdz(:,1)

!           facespeed(edge) = facespeed(edge) + 0.5_dp*(term1+term2)*fact
            dfacespeeddx(:,1) = dfacespeeddx(:,1) + 0.5_dp*(dterm1dx(:,1)+     &
                                                            dterm2dx(:,1))*fact
            dfacespeeddy(:,1) = dfacespeeddy(:,1) + 0.5_dp*(dterm1dy(:,1)+     &
                                                            dterm2dy(:,1))*fact
            dfacespeeddz(:,1) = dfacespeeddz(:,1) + 0.5_dp*(dterm1dz(:,1)+     &
                                                            dterm2dz(:,1))*fact
          else
            do i = 1, getg%ntp
              ddxdtavgdx(:,i)=(ddxdtmdx(:,i)+ddxdtldx(:,i)+ddxdtcdx(:,i))/3._dp
              ddydtavgdy(:,i)=(ddydtmdy(:,i)+ddydtldy(:,i)+ddydtcdy(:,i))/3._dp
              ddzdtavgdz(:,i)=(ddzdtmdz(:,i)+ddzdtldz(:,i)+ddzdtcdz(:,i))/3._dp

! Area only depends on coords at current time level

!           term1    = dxdtavg*areax1 + dydtavg*areay1 + dzdtavg*areaz1
!           term2    = dxdtavg*areax2 + dydtavg*areay2 + dzdtavg*areaz2

              if ( i == 1 ) then
                dterm1dx(:,i) = dxdtavg*dareax1dx(:) + areax1*ddxdtavgdx(:,i) +&
                                dydtavg*dareay1dx(:) + dzdtavg*dareaz1dx(:)
                dterm1dy(:,i) = dxdtavg*dareax1dy(:) + areay1*ddydtavgdy(:,i) +&
                                dydtavg*dareay1dy(:) + dzdtavg*dareaz1dy(:)
                dterm1dz(:,i) = dxdtavg*dareax1dz(:) + areaz1*ddzdtavgdz(:,i) +&
                                dydtavg*dareay1dz(:) + dzdtavg*dareaz1dz(:)

                dterm2dx(:,i) = dxdtavg*dareax2dx(:) + areax2*ddxdtavgdx(:,i) +&
                                dydtavg*dareay2dx(:) + dzdtavg*dareaz2dx(:)
                dterm2dy(:,i) = dxdtavg*dareax2dy(:) + areay2*ddydtavgdy(:,i) +&
                                dydtavg*dareay2dy(:) + dzdtavg*dareaz2dy(:)
                dterm2dz(:,i) = dxdtavg*dareax2dz(:) + areaz2*ddzdtavgdz(:,i) +&
                                dydtavg*dareay2dz(:) + dzdtavg*dareaz2dz(:)
              else
                dterm1dx(:,i) = areax1*ddxdtavgdx(:,i)
                dterm1dy(:,i) = areay1*ddydtavgdy(:,i)
                dterm1dz(:,i) = areaz1*ddzdtavgdz(:,i)

                dterm2dx(:,i) = areax2*ddxdtavgdx(:,i)
                dterm2dy(:,i) = areay2*ddydtavgdy(:,i)
                dterm2dz(:,i) = areaz2*ddzdtavgdz(:,i)
              endif

!           facespeed(edge) = facespeed(edge) + 0.5_dp*(term1+term2)*fact
              dfacespeeddx(:,i)=dfacespeeddx(:,i) + 0.5_dp*(dterm1dx(:,i)+     &
                                                            dterm2dx(:,i))*fact
              dfacespeeddy(:,i)=dfacespeeddy(:,i) + 0.5_dp*(dterm1dy(:,i)+     &
                                                            dterm2dy(:,i))*fact
              dfacespeeddz(:,i)=dfacespeeddz(:,i) + 0.5_dp*(dterm1dz(:,i)+     &
                                                            dterm2dz(:,i))*fact
            end do

          endif

! Add the contributions

          local_n1b : if ( node1 <= grid%nnodes0 ) then

!           grid%res_gcl(1,node1) = grid%res_gcl(1,node1) + facespeed

            do i = 1, getg%ntp
              dres_gcl1dx(:,i) = dfacespeeddx(:,i)
              dres_gcl1dy(:,i) = dfacespeeddy(:,i)
              dres_gcl1dz(:,i) = dfacespeeddz(:,i)
            end do

            fcn_loop1b : do ifcn = 1, design%nfunctions

! Form the Q-transpose*lambda dot product factor for the current cost function

              factor = my_0
              do j = 1, soln%n_tot
                if ( soln%eqn_set == incompressible .and. j == 1 ) then
                  factor = factor                                              &
                             + beta*sadj%rlam(j,node1,ifcn)*sadj%coltag(j,node1)
                else
                  factor = factor                                              &
                    + Qnm1(j,node1)*sadj%rlam(j,node1,ifcn)*sadj%coltag(j,node1)
                endif
              end do
              do j = 1, soln%n_turb
                factor = factor                                                &
                          + turbnm1(j,node1)*sadj%rlam(soln%n_tot+j,node1,ifcn)&
                                          *sadj%coltag(soln%n_tot+j,node1)
              end do

              do i = 1, getg%ntp
                do nn = 1, grid%elem(ielem)%node_per_cell
                  inode = grid%elem(ielem)%c2n(nn,n)
                  getg%drdxl(1,inode,ifcn,i) = getg%drdxl(1,inode,ifcn,i)      &
                                                      + dres_gcl1dx(nn,i)*factor
                  getg%drdxl(2,inode,ifcn,i) = getg%drdxl(2,inode,ifcn,i)      &
                                                      + dres_gcl1dy(nn,i)*factor
                  getg%drdxl(3,inode,ifcn,i) = getg%drdxl(3,inode,ifcn,i)      &
                                                      + dres_gcl1dz(nn,i)*factor
                end do
              end do

            end do fcn_loop1b

          endif local_n1b

          local_n2b : if ( node2 <= grid%nnodes0 ) then

!           grid%res_gcl(1,node2) = grid%res_gcl(1,node2) - facespeed

            do i = 1, getg%ntp
              dres_gcl2dx(:,i) = -dfacespeeddx(:,i)
              dres_gcl2dy(:,i) = -dfacespeeddy(:,i)
              dres_gcl2dz(:,i) = -dfacespeeddz(:,i)
            end do

            fcn_loop2b : do ifcn = 1, design%nfunctions

! Form the Q-transpose*lambda dot product factor for the current cost function

              factor = my_0
              do j = 1, soln%n_tot
                if ( soln%eqn_set == incompressible .and. j == 1 ) then
                  factor = factor                                              &
                             + beta*sadj%rlam(j,node2,ifcn)*sadj%coltag(j,node2)
                else
                  factor = factor                                              &
                    + Qnm1(j,node2)*sadj%rlam(j,node2,ifcn)*sadj%coltag(j,node2)
                endif
              end do
              do j = 1, soln%n_turb
                factor = factor                                                &
                          + turbnm1(j,node2)*sadj%rlam(soln%n_tot+j,node2,ifcn)&
                                          *sadj%coltag(soln%n_tot+j,node2)
              end do

              do i = 1, getg%ntp
                do nn = 1, grid%elem(ielem)%node_per_cell
                  inode = grid%elem(ielem)%c2n(nn,n)
                  getg%drdxl(1,inode,ifcn,i) = getg%drdxl(1,inode,ifcn,i)      &
                                                      + dres_gcl2dx(nn,i)*factor
                  getg%drdxl(2,inode,ifcn,i) = getg%drdxl(2,inode,ifcn,i)      &
                                                      + dres_gcl2dy(nn,i)*factor
                  getg%drdxl(3,inode,ifcn,i) = getg%drdxl(3,inode,ifcn,i)      &
                                                      + dres_gcl2dz(nn,i)*factor
                end do
              end do

            end do fcn_loop2b

          endif local_n2b

        end do edge_loop_cell

      end do cell_loop

    end do elem_loop

! Now we need to close off the boundaries

    call dgcl_dgridb(grid,soln,sadj,design,getg,Qnm1,turbnm1,time_coeff)

  end subroutine dgcl_dgrid


!================================= DGCL_DGRIDB ===============================80
!
! Linearizes the GCL wrt the grid on the boundaries
!
! Note: computation was begun in dgcl_dgrid
!
!=============================================================================80
  subroutine dgcl_dgridb(grid,soln,sadj,design,getg,Qnm1,turbnm1,time_coeff)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_2, my_3, my_4
    use grid_types,      only : grid_type
    use solution_types,  only : soln_type, incompressible
    use solution_adj,    only : sadj_type
    use design_types,    only : design_type
    use solution_getg,   only : getg_type
    use info_depr,       only : beta

    type(sadj_type),                               intent(in   ) :: sadj
    type(design_type),                             intent(in   ) :: design
    type(grid_type),                               intent(in   ) :: grid
    type(soln_type),                               intent(inout) :: soln
    type(getg_type),                               intent(inout) :: getg
    real(dp), dimension(getg%ntp),                 intent(in   ) :: time_coeff
    real(dp), dimension(soln%n_tot,grid%nnodes0),  intent(in   ) :: Qnm1
    real(dp), dimension(soln%n_turb,grid%nnodes0), intent(in   ) :: turbnm1

    integer :: face, nn, node1, node2, node3, node4, ib, ifcn, j
    integer :: n1, n2, n3, n4, nn1, nn2, nn3, nn4, i
    integer :: cnode1, cnode2, cnode3, cnode4

    real(dp) :: factor
    real(dp) :: areax, areay, areaz
    real(dp) :: x0, y0, z0, dxdt0, dydt0, dzdt0
    real(dp) :: dxdtc, dydtc, dzdtc
    real(dp) :: xr, yr, zr, dxdtr, dydtr, dzdtr
    real(dp) :: xl, yl, zl, dxdtl, dydtl, dzdtl
    real(dp) :: dxdtavg, dydtavg, dzdtavg
    real(dp) :: xorig1,yorig1,zorig1
    real(dp) :: xorig2,yorig2,zorig2
    real(dp) :: xorig3,yorig3,zorig3
    real(dp) :: xorig4,yorig4,zorig4
    real(dp) :: xorig,yorig,zorig
    real(dp) :: xc_ref,yc_ref,zc_ref,piece1,piece2
    real(dp) :: xorig1x1,xorig1x2,xorig1x3,xorig1x4
    real(dp) :: yorig1y1,yorig1y2,yorig1y3,yorig1y4
    real(dp) :: zorig1z1,zorig1z2,zorig1z3,zorig1z4
    real(dp) :: xorig2x1,xorig2x2,xorig2x3,xorig2x4
    real(dp) :: yorig2y1,yorig2y2,yorig2y3,yorig2y4
    real(dp) :: zorig2z1,zorig2z2,zorig2z3,zorig2z4
    real(dp) :: xorig3x1,xorig3x2,xorig3x3,xorig3x4
    real(dp) :: yorig3y1,yorig3y2,yorig3y3,yorig3y4
    real(dp) :: zorig3z1,zorig3z2,zorig3z3,zorig3z4
    real(dp) :: xorig4x1,xorig4x2,xorig4x3,xorig4x4
    real(dp) :: yorig4y1,yorig4y2,yorig4y3,yorig4y4
    real(dp) :: zorig4z1,zorig4z2,zorig4z3,zorig4z4
    real(dp) :: xorigx1,xorigx2,xorigx3,xorigx4
    real(dp) :: yorigy1,yorigy2,yorigy3,yorigy4
    real(dp) :: zorigz1,zorigz2,zorigz3,zorigz4
    real(dp) :: xc_refx1,xc_refx2,xc_refx3,xc_refx4
    real(dp) :: yc_refy1,yc_refy2,yc_refy3,yc_refy4
    real(dp) :: zc_refz1,zc_refz2,zc_refz3,zc_refz4
    real(dp) :: x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4
    real(dp) :: x0x1,x0x2,x0x3,x0x4,y0y1,y0y2,y0y3,y0y4,z0z1,z0z2,z0z3,z0z4
    real(dp) :: x1x1,x1x2,x1x3,x1x4,x2x1,x2x2,x2x3,x2x4,x3x1,x3x2,x3x3
    real(dp) :: x4x1,x4x2,x4x3,x4x4
    real(dp) :: y1y1,y1y2,y1y3,y1y4,y2y1,y2y2,y2y3,y2y4,y3y1,y3y2,y3y3
    real(dp) :: y4y1,y4y2,y4y3,y4y4
    real(dp) :: z1z1,z1z2,z1z3,z1z4,z2z1,z2z2,z2z3,z2z4,z3z1,z3z2,z3z3
    real(dp) :: z4z1,z4z2,z4z3,z4z4
    real(dp) :: xlx1,xlx2,xlx3,xlx4,yly1,yly2,yly3,yly4,zlz1,zlz2,zlz3,zlz4
    real(dp) :: xrx1,xrx2,xrx3,xrx4,yry1,yry2,yry3,yry4,zrz1,zrz2,zrz3,zrz4
    real(dp) :: areaxx1,areaxx2,areaxx3,areaxx4
    real(dp) :: areaxy1,areaxy2,areaxy3,areaxy4
    real(dp) :: areaxz1,areaxz2,areaxz3,areaxz4
    real(dp) :: areayx1,areayx2,areayx3,areayx4
    real(dp) :: areayy1,areayy2,areayy3,areayy4
    real(dp) :: areayz1,areayz2,areayz3,areayz4
    real(dp) :: areazx1,areazx2,areazx3,areazx4
    real(dp) :: areazy1,areazy2,areazy3,areazy4
    real(dp) :: areazz1,areazz2,areazz3,areazz4

    real(dp), dimension(4) :: xc, yc, zc
    real(dp), dimension(4) :: xcx1,xcx2,xcx3,xcx4
    real(dp), dimension(4) :: ycy1,ycy2,ycy3,ycy4
    real(dp), dimension(4) :: zcz1,zcz2,zcz3,zcz4
    real(dp), dimension(getg%ntp) :: dxdtcx1,dxdtcx2,dxdtcx3,dxdtcx4
    real(dp), dimension(getg%ntp) :: dydtcy1,dydtcy2,dydtcy3,dydtcy4
    real(dp), dimension(getg%ntp) :: dzdtcz1,dzdtcz2,dzdtcz3,dzdtcz4
    real(dp), dimension(getg%ntp) :: dxdt0x1,dxdt0x2,dxdt0x3,dxdt0x4
    real(dp), dimension(getg%ntp) :: dydt0y1,dydt0y2,dydt0y3,dydt0y4
    real(dp), dimension(getg%ntp) :: dzdt0z1,dzdt0z2,dzdt0z3,dzdt0z4
    real(dp), dimension(getg%ntp) :: bfacespeedx1,bfacespeedx2,bfacespeedx3
    real(dp), dimension(getg%ntp) :: bfacespeedy1,bfacespeedy2,bfacespeedy3
    real(dp), dimension(getg%ntp) :: bfacespeedz1,bfacespeedz2,bfacespeedz3
    real(dp), dimension(getg%ntp) :: bfacespeedx4
    real(dp), dimension(getg%ntp) :: bfacespeedy4
    real(dp), dimension(getg%ntp) :: bfacespeedz4
    real(dp), dimension(getg%ntp) :: piece1x1,piece1x2,piece1x3,piece1x4
    real(dp), dimension(getg%ntp) :: piece1y1,piece1y2,piece1y3,piece1y4
    real(dp), dimension(getg%ntp) :: piece1z1,piece1z2,piece1z3,piece1z4
    real(dp), dimension(getg%ntp) :: piece2x1,piece2x2,piece2x3,piece2x4
    real(dp), dimension(getg%ntp) :: piece2y1,piece2y2,piece2y3,piece2y4
    real(dp), dimension(getg%ntp) :: piece2z1,piece2z2,piece2z3,piece2z4
    real(dp), dimension(getg%ntp) :: dxdtlx1,dxdtlx2,dxdtlx3,dxdtlx4
    real(dp), dimension(getg%ntp) :: dydtly1,dydtly2,dydtly3,dydtly4
    real(dp), dimension(getg%ntp) :: dzdtlz1,dzdtlz2,dzdtlz3,dzdtlz4
    real(dp), dimension(getg%ntp) :: dxdtrx1,dxdtrx2,dxdtrx3,dxdtrx4
    real(dp), dimension(getg%ntp) :: dydtry1,dydtry2,dydtry3,dydtry4
    real(dp), dimension(getg%ntp) :: dzdtrz1,dzdtrz2,dzdtrz3,dzdtrz4
    real(dp), dimension(getg%ntp) :: dxdtavgx1,dxdtavgx2,dxdtavgx3,dxdtavgx4
    real(dp), dimension(getg%ntp) :: dxdtavgy1,dxdtavgy2,dxdtavgy3,dxdtavgy4
    real(dp), dimension(getg%ntp) :: dxdtavgz1,dxdtavgz2,dxdtavgz3,dxdtavgz4
    real(dp), dimension(getg%ntp) :: dydtavgx1,dydtavgx2,dydtavgx3,dydtavgx4
    real(dp), dimension(getg%ntp) :: dydtavgy1,dydtavgy2,dydtavgy3,dydtavgy4
    real(dp), dimension(getg%ntp) :: dydtavgz1,dydtavgz2,dydtavgz3,dydtavgz4
    real(dp), dimension(getg%ntp) :: dzdtavgx1,dzdtavgx2,dzdtavgx3,dzdtavgx4
    real(dp), dimension(getg%ntp) :: dzdtavgy1,dzdtavgy2,dzdtavgy3,dzdtavgy4
    real(dp), dimension(getg%ntp) :: dzdtavgz1,dzdtavgz2,dzdtavgz3,dzdtavgz4
    real(dp), dimension(getg%ntp) :: termx1,termx2,termx3,termx4
    real(dp), dimension(getg%ntp) :: termy1,termy2,termy3,termy4
    real(dp), dimension(getg%ntp) :: termz1,termz2,termz3,termz4
    real(dp), dimension(getg%ntp) :: res_gcl_x1,res_gcl_x2,res_gcl_x3
    real(dp), dimension(getg%ntp) :: res_gcl_y1,res_gcl_y2,res_gcl_y3
    real(dp), dimension(getg%ntp) :: res_gcl_z1,res_gcl_z2,res_gcl_z3
    real(dp), dimension(getg%ntp) :: res_gcl_x4
    real(dp), dimension(getg%ntp) :: res_gcl_y4
    real(dp), dimension(getg%ntp) :: res_gcl_z4

  continue

! avoid used uninitialized warnings

  nn1 = 0; nn2 = 0; nn3 = 0; nn4 = 0

  x1x1=my_0; x1x2=my_0; x1x3=my_0; x1x4=my_0
  x2x1=my_0; x2x2=my_0; x2x3=my_0; x2x4=my_0
  x3x1=my_0; x3x2=my_0; x3x3=my_0
  x4x1=my_0; x4x2=my_0; x4x3=my_0; x4x4=my_0
  y1y1=my_0; y1y2=my_0; y1y3=my_0; y1y4=my_0
  y2y1=my_0; y2y2=my_0; y2y3=my_0; y2y4=my_0
  y3y1=my_0; y3y2=my_0; y3y3=my_0
  y4y1=my_0; y4y2=my_0; y4y3=my_0; y4y4=my_0
  z1z1=my_0; z1z2=my_0; z1z3=my_0; z1z4=my_0
  z2z1=my_0; z2z2=my_0; z2z3=my_0; z2z4=my_0
  z3z1=my_0; z3z2=my_0; z3z3=my_0
  z4z1=my_0; z4z2=my_0; z4z3=my_0; z4z4=my_0

  xorig=my_0; xorigx1=my_0; xorigx2=my_0; xorigx3=my_0; xorigx4=my_0
  yorig=my_0; yorigy1=my_0; yorigy2=my_0; yorigy3=my_0; yorigy4=my_0
  zorig=my_0; zorigz1=my_0; zorigz2=my_0; zorigz3=my_0; zorigz4=my_0

  xc_ref=my_0; xc_refx1=my_0; xc_refx2=my_0; xc_refx3=my_0; xc_refx4=my_0
  yc_ref=my_0; yc_refy1=my_0; yc_refy2=my_0; yc_refy3=my_0; yc_refy4=my_0
  zc_ref=my_0; zc_refz1=my_0; zc_refz2=my_0; zc_refz3=my_0; zc_refz4=my_0

    bound_loop : do ib = 1, grid%nbound

      tria_face_loop : do face = 1, grid%bc(ib)%nbfacet

        n1 = grid%bc(ib)%f2ntb(face,1)
        n2 = grid%bc(ib)%f2ntb(face,2)
        n3 = grid%bc(ib)%f2ntb(face,3)

        node1 = grid%bc(ib)%ibnode(n1)
        node2 = grid%bc(ib)%ibnode(n2)
        node3 = grid%bc(ib)%ibnode(n3)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3

        xorig1 = grid%x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0

        yorig1 = grid%y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0

        zorig1 = grid%z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0

        xorig2 = grid%x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0

        yorig2 = grid%y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0

        zorig2 = grid%z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0

        xorig3 = grid%x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1

        yorig3 = grid%y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1

        zorig3 = grid%z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1

        xc(1)=((grid%x(node2)-xorig1) + (grid%x(node3)-xorig1))/my_3
          xcx1(1)=(-xorig1x1-xorig1x1)/my_3
          xcx2(1)=my_1/my_3
          xcx3(1)=my_1/my_3

        yc(1)=((grid%y(node2)-yorig1) + (grid%y(node3)-yorig1))/my_3
          ycy1(1)=(-yorig1y1-yorig1y1)/my_3
          ycy2(1)=my_1/my_3
          ycy3(1)=my_1/my_3

        zc(1)=((grid%z(node2)-zorig1) + (grid%z(node3)-zorig1))/my_3
          zcz1(1)=(-zorig1z1-zorig1z1)/my_3
          zcz2(1)=my_1/my_3
          zcz3(1)=my_1/my_3

        xc(2)=((grid%x(node1)-xorig2) + (grid%x(node3)-xorig2))/my_3
          xcx1(2)=my_1/my_3
          xcx2(2)=(-xorig2x2-xorig2x2)/my_3
          xcx3(2)=my_1/my_3

        yc(2)=((grid%y(node1)-yorig2) + (grid%y(node3)-yorig2))/my_3
          ycy1(2)=my_1/my_3
          ycy2(2)=(-yorig2y2-yorig2y2)/my_3
          ycy3(2)=my_1/my_3

        zc(2)=((grid%z(node1)-zorig2) + (grid%z(node3)-zorig2))/my_3
          zcz1(2)=my_1/my_3
          zcz2(2)=(-zorig2z2-zorig2z2)/my_3
          zcz3(2)=my_1/my_3

        xc(3)=((grid%x(node1)-xorig3) + (grid%x(node2)-xorig3))/my_3
          xcx1(3)=my_1/my_3
          xcx2(3)=my_1/my_3
          xcx3(3)=(-xorig3x3-xorig3x3)/my_3

        yc(3)=((grid%y(node1)-yorig3) + (grid%y(node2)-yorig3))/my_3
          ycy1(3)=my_1/my_3
          ycy2(3)=my_1/my_3
          ycy3(3)=(-yorig3y3-yorig3y3)/my_3

        zc(3)=((grid%z(node1)-zorig3) + (grid%z(node2)-zorig3))/my_3
          zcz1(3)=my_1/my_3
          zcz2(3)=my_1/my_3
          zcz3(3)=(-zorig3z3-zorig3z3)/my_3

        dxdtc = (grid%bc(ib)%bdxdt(n1) + grid%bc(ib)%bdxdt(n2)                 &
               + grid%bc(ib)%bdxdt(n3))/my_3
          do i = 1, getg%ntp
            dxdtcx1(i) = time_coeff(i)/my_3
            dxdtcx2(i) = time_coeff(i)/my_3
            dxdtcx3(i) = time_coeff(i)/my_3
          end do

        dydtc = (grid%bc(ib)%bdydt(n1) + grid%bc(ib)%bdydt(n2)                 &
               + grid%bc(ib)%bdydt(n3))/my_3
          do i = 1, getg%ntp
            dydtcy1(i) = time_coeff(i)/my_3
            dydtcy2(i) = time_coeff(i)/my_3
            dydtcy3(i) = time_coeff(i)/my_3
          end do

        dzdtc = (grid%bc(ib)%bdzdt(n1) + grid%bc(ib)%bdzdt(n2)                 &
               + grid%bc(ib)%bdzdt(n3))/my_3
          do i = 1, getg%ntp
            dzdtcz1(i) = time_coeff(i)/my_3
            dzdtcz2(i) = time_coeff(i)/my_3
            dzdtcz3(i) = time_coeff(i)/my_3
          end do

        tria_node_loop : do nn = 1, 3

          do i = 1, getg%ntp
            bfacespeedx1(i) = 0.0_dp
            bfacespeedx2(i) = 0.0_dp
            bfacespeedx3(i) = 0.0_dp

            bfacespeedy1(i) = 0.0_dp
            bfacespeedy2(i) = 0.0_dp
            bfacespeedy3(i) = 0.0_dp

            bfacespeedz1(i) = 0.0_dp
            bfacespeedz2(i) = 0.0_dp
            bfacespeedz3(i) = 0.0_dp
          end do

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
            case(3)
              nn1 = n3
              nn2 = n1
              nn3 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
            case default
          end select

          node1 = grid%bc(ib)%ibnode(nn1)
          node2 = grid%bc(ib)%ibnode(nn2)
          node3 = grid%bc(ib)%ibnode(nn3)

          x1 = grid%x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
            endif

          x2 = grid%x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
            endif

          x3 = grid%x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
            endif

          y1 = grid%y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
            endif

          y2 = grid%y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
            endif

          y3 = grid%y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
            endif

          z1 = grid%z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
            endif

          z2 = grid%z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
            endif

          z3 = grid%z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3

          dxdt0 = grid%bc(ib)%bdxdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                dxdt0x1(i) = time_coeff(i)
                dxdt0x2(i) = my_0
                dxdt0x3(i) = my_0
              else if ( node1 == cnode2 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = time_coeff(i)
                dxdt0x3(i) = my_0
              else if ( node1 == cnode3 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = my_0
                dxdt0x3(i) = time_coeff(i)
              endif
            end do

          dydt0 = grid%bc(ib)%bdydt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                dydt0y1(i) = time_coeff(i)
                dydt0y2(i) = my_0
                dydt0y3(i) = my_0
              else if ( node1 == cnode2 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = time_coeff(i)
                dydt0y3(i) = my_0
              else if ( node1 == cnode3 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = my_0
                dydt0y3(i) = time_coeff(i)
              endif
            end do

          dzdt0 = grid%bc(ib)%bdzdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                dzdt0z1(i) = time_coeff(i)
                dzdt0z2(i) = my_0
                dzdt0z3(i) = my_0
              else if ( node1 == cnode2 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = time_coeff(i)
                dzdt0z3(i) = my_0
              else if ( node1 == cnode3 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = my_0
                dzdt0z3(i) = time_coeff(i)
              endif
            end do

          xl = ((x1-xorig) + (x3-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x3x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x3x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x3x3-xorigx3))/my_2

          yl = ((y1-yorig) + (y3-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y3y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y3y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y3y3-yorigy3))/my_2

          zl = ((z1-zorig) + (z3-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z3z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z3z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z3z3-zorigz3))/my_2

          piece1 = grid%bc(ib)%bdxdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdxdt(nn3)
            do i = 1, getg%ntp
              if ( node3 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
              else if ( node3 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
              else if ( node3 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
              endif
            end do

          dxdtl = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dxdtlx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtlx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtlx3(i) = (piece1x3(i) + piece2x3(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdydt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdydt(nn3)
            do i = 1, getg%ntp
              if ( node3 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
              else if ( node3 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
              else if ( node3 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
              endif
            end do

          dydtl = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dydtly1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtly2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtly3(i) = (piece1y3(i) + piece2y3(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdzdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdzdt(nn3)
            do i = 1, getg%ntp
              if ( node3 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
              else if ( node3 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
              else if ( node3 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
              endif
            end do

          dzdtl = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dzdtlz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtlz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtlz3(i) = (piece1z3(i) + piece2z3(i))/my_2
            end do

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2

          piece1 = grid%bc(ib)%bdxdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdxdt(nn2)
            do i = 1, getg%ntp
              if ( node2 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
              endif
            end do

          dxdtr = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dxdtrx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtrx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtrx3(i) = (piece1x3(i) + piece2x3(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdydt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdydt(nn2)
            do i = 1, getg%ntp
              if ( node2 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
              endif
            end do

          dydtr = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dydtry1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtry2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtry3(i) = (piece1y3(i) + piece2y3(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdzdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdzdt(nn2)
            do i = 1, getg%ntp
              if ( node2 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
              endif
            end do

          dzdtr = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dzdtrz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtrz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtrz3(i) = (piece1z3(i) + piece2z3(i))/my_2
            end do

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

          dxdtavg = (dxdt0 + dxdtr + dxdtc)/my_3
            do i = 1, getg%ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtrx1(i) + dxdtcx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtrx2(i) + dxdtcx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtrx3(i) + dxdtcx3(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
            end do

          dydtavg = (dydt0 + dydtr + dydtc)/my_3
            do i = 1, getg%ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtry1(i) + dydtcy1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtry2(i) + dydtcy2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtry3(i) + dydtcy3(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtr + dzdtc)/my_3
            do i = 1, getg%ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtrz1(i) + dzdtcz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtrz2(i) + dzdtcz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtrz3(i) + dzdtcz3(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
            do i = 1, getg%ntp

! area only depends on grid coords at current time level

             if ( i == 1 ) then
              termx1(i)=dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                     + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
              termx2(i)=dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                     + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
              termx3(i)=dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                     + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)

              termy1(i)=dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                     + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
              termy2(i)=dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                     + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
              termy3(i)=dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                     + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)

              termz1(i)=dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                     + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
              termz2(i)=dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                     + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
              termz3(i)=dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                     + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
             else
              termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i)+areaz*dzdtavgx1(i)
              termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i)+areaz*dzdtavgx2(i)
              termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i)+areaz*dzdtavgx3(i)

              termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i)+areaz*dzdtavgy1(i)
              termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i)+areaz*dzdtavgy2(i)
              termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i)+areaz*dzdtavgy3(i)

              termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i)+areaz*dzdtavgz1(i)
              termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i)+areaz*dzdtavgz2(i)
              termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i)+areaz*dzdtavgz3(i)
             endif
            end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, getg%ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
            end do

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

          dxdtavg = (dxdt0 + dxdtc + dxdtl)/my_3
            do i = 1, getg%ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtcx1(i) + dxdtlx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtcx2(i) + dxdtlx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtcx3(i) + dxdtlx3(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
            end do

          dydtavg = (dydt0 + dydtc + dydtl)/my_3
            do i = 1, getg%ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtcy1(i) + dydtly1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtcy2(i) + dydtly2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtcy3(i) + dydtly3(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtc + dzdtl)/my_3
            do i = 1, getg%ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtcz1(i) + dzdtlz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtcz2(i) + dzdtlz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtcz3(i) + dzdtlz3(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
            do i = 1, getg%ntp

! area only depends on grid coords at current time level

             if ( i == 1 ) then
              termx1(i)=dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                     + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
              termx2(i)=dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                     + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
              termx3(i)=dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                     + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)

              termy1(i)=dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                     + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
              termy2(i)=dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                     + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
              termy3(i)=dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                     + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)

              termz1(i)=dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                     + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
              termz2(i)=dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                     + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
              termz3(i)=dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                     + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
             else
              termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i)+areaz*dzdtavgx1(i)
              termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i)+areaz*dzdtavgx2(i)
              termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i)+areaz*dzdtavgx3(i)

              termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i)+areaz*dzdtavgy1(i)
              termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i)+areaz*dzdtavgy2(i)
              termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i)+areaz*dzdtavgy3(i)

              termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i)+areaz*dzdtavgz1(i)
              termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i)+areaz*dzdtavgz2(i)
              termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i)+areaz*dzdtavgz3(i)
             endif
            end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, getg%ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
            end do

          local_node1b : if ( node1 <= grid%nnodes0 ) then

!           res_gcl(1,node1) = res_gcl(1,node1) + bfacespeed

            do i = 1, getg%ntp
              res_gcl_x1(i) = bfacespeedx1(i)
              res_gcl_x2(i) = bfacespeedx2(i)
              res_gcl_x3(i) = bfacespeedx3(i)

              res_gcl_y1(i) = bfacespeedy1(i)
              res_gcl_y2(i) = bfacespeedy2(i)
              res_gcl_y3(i) = bfacespeedy3(i)

              res_gcl_z1(i) = bfacespeedz1(i)
              res_gcl_z2(i) = bfacespeedz2(i)
              res_gcl_z3(i) = bfacespeedz3(i)
            end do

            fcn_loop1b : do ifcn = 1, design%nfunctions

! Form the Q-transpose*lambda dot product factor for the current cost function

              factor = my_0
              do j = 1, soln%n_tot
                if ( soln%eqn_set == incompressible .and. j == 1 ) then
                  factor = factor                                              &
                             + beta*sadj%rlam(j,node1,ifcn)*sadj%coltag(j,node1)
                else
                  factor = factor                                              &
                    + Qnm1(j,node1)*sadj%rlam(j,node1,ifcn)*sadj%coltag(j,node1)
                endif
              end do
              do j = 1, soln%n_turb
                factor = factor                                                &
                          + turbnm1(j,node1)*sadj%rlam(soln%n_tot+j,node1,ifcn)&
                                          *sadj%coltag(soln%n_tot+j,node1)
              end do

              do i = 1, getg%ntp
                getg%drdxl(1,cnode1,ifcn,i) = getg%drdxl(1,cnode1,ifcn,i)      &
                                                          + res_gcl_x1(i)*factor
                getg%drdxl(1,cnode2,ifcn,i) = getg%drdxl(1,cnode2,ifcn,i)      &
                                                          + res_gcl_x2(i)*factor
                getg%drdxl(1,cnode3,ifcn,i) = getg%drdxl(1,cnode3,ifcn,i)      &
                                                          + res_gcl_x3(i)*factor

                getg%drdxl(2,cnode1,ifcn,i) = getg%drdxl(2,cnode1,ifcn,i)      &
                                                          + res_gcl_y1(i)*factor
                getg%drdxl(2,cnode2,ifcn,i) = getg%drdxl(2,cnode2,ifcn,i)      &
                                                          + res_gcl_y2(i)*factor
                getg%drdxl(2,cnode3,ifcn,i) = getg%drdxl(2,cnode3,ifcn,i)      &
                                                          + res_gcl_y3(i)*factor

                getg%drdxl(3,cnode1,ifcn,i) = getg%drdxl(3,cnode1,ifcn,i)      &
                                                          + res_gcl_z1(i)*factor
                getg%drdxl(3,cnode2,ifcn,i) = getg%drdxl(3,cnode2,ifcn,i)      &
                                                          + res_gcl_z2(i)*factor
                getg%drdxl(3,cnode3,ifcn,i) = getg%drdxl(3,cnode3,ifcn,i)      &
                                                          + res_gcl_z3(i)*factor
              end do

            end do fcn_loop1b

          endif local_node1b

        end do tria_node_loop

      end do tria_face_loop

      quad_face_loop : do face = 1, grid%bc(ib)%nbfaceq

        n1 = grid%bc(ib)%f2nqb(face,1)
        n2 = grid%bc(ib)%f2nqb(face,2)
        n3 = grid%bc(ib)%f2nqb(face,3)
        n4 = grid%bc(ib)%f2nqb(face,4)

        node1 = grid%bc(ib)%ibnode(n1)
        node2 = grid%bc(ib)%ibnode(n2)
        node3 = grid%bc(ib)%ibnode(n3)
        node4 = grid%bc(ib)%ibnode(n4)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3
        cnode4 = node4

        xorig1 = grid%x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0
          xorig1x4 = my_0

        yorig1 = grid%y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0
          yorig1y4 = my_0

        zorig1 = grid%z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0
          zorig1z4 = my_0

        xorig2 = grid%x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0
          xorig2x4 = my_0

        yorig2 = grid%y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0
          yorig2y4 = my_0

        zorig2 = grid%z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0
          zorig2z4 = my_0

        xorig3 = grid%x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1
          xorig3x4 = my_0

        yorig3 = grid%y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1
          yorig3y4 = my_0

        zorig3 = grid%z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1
          zorig3z4 = my_0

        xorig4 = grid%x(node4)
          xorig4x1 = my_0
          xorig4x2 = my_0
          xorig4x3 = my_0
          xorig4x4 = my_1

        yorig4 = grid%y(node4)
          yorig4y1 = my_0
          yorig4y2 = my_0
          yorig4y3 = my_0
          yorig4y4 = my_1

        zorig4 = grid%z(node4)
          zorig4z1 = my_0
          zorig4z2 = my_0
          zorig4z3 = my_0
          zorig4z4 = my_1

        xc(1)=((grid%x(node2)-xorig1) + (grid%x(node3)-xorig1)                 &
             + (grid%x(node4)-xorig1))/my_4
          xcx1(1)=(-xorig1x1-xorig1x1-xorig1x1)/my_4
          xcx2(1)=my_1/my_4
          xcx3(1)=my_1/my_4
          xcx4(1)=my_1/my_4

        yc(1)=((grid%y(node2)-yorig1) + (grid%y(node3)-yorig1)                 &
             + (grid%y(node4)-yorig1))/my_4
          ycy1(1)=(-yorig1y1-yorig1y1-yorig1y1)/my_4
          ycy2(1)=my_1/my_4
          ycy3(1)=my_1/my_4
          ycy4(1)=my_1/my_4

        zc(1)=((grid%z(node2)-zorig1) + (grid%z(node3)-zorig1)                 &
             + (grid%z(node4)-zorig1))/my_4
          zcz1(1)=(-zorig1z1-zorig1z1-zorig1z1)/my_4
          zcz2(1)=my_1/my_4
          zcz3(1)=my_1/my_4
          zcz4(1)=my_1/my_4

        xc(2)=((grid%x(node1)-xorig2) + (grid%x(node3)-xorig2)                 &
             + (grid%x(node4)-xorig2))/my_4
          xcx1(2)=my_1/my_4
          xcx2(2)=(-xorig2x2-xorig2x2-xorig2x2)/my_4
          xcx3(2)=my_1/my_4
          xcx4(2)=my_1/my_4

        yc(2)=((grid%y(node1)-yorig2) + (grid%y(node3)-yorig2)                 &
             + (grid%y(node4)-yorig2))/my_4
          ycy1(2)=my_1/my_4
          ycy2(2)=(-yorig2y2-yorig2y2-yorig2y2)/my_4
          ycy3(2)=my_1/my_4
          ycy4(2)=my_1/my_4

        zc(2)=((grid%z(node1)-zorig2) + (grid%z(node3)-zorig2)                 &
             + (grid%z(node4)-zorig2))/my_4
          zcz1(2)=my_1/my_4
          zcz2(2)=(-zorig2z2-zorig2z2-zorig2z2)/my_4
          zcz3(2)=my_1/my_4
          zcz4(2)=my_1/my_4

        xc(3)=((grid%x(node1)-xorig3) + (grid%x(node2)-xorig3)                 &
             + (grid%x(node4)-xorig3))/my_4
          xcx1(3)=my_1/my_4
          xcx2(3)=my_1/my_4
          xcx3(3)=(-xorig3x3-xorig3x3-xorig3x3)/my_4
          xcx4(3)=my_1/my_4

        yc(3)=((grid%y(node1)-yorig3) + (grid%y(node2)-yorig3)                 &
             + (grid%y(node4)-yorig3))/my_4
          ycy1(3)=my_1/my_4
          ycy2(3)=my_1/my_4
          ycy3(3)=(-yorig3y3-yorig3y3-yorig3y3)/my_4
          ycy4(3)=my_1/my_4

        zc(3)=((grid%z(node1)-zorig3) + (grid%z(node2)-zorig3)                 &
             + (grid%z(node4)-zorig3))/my_4
          zcz1(3)=my_1/my_4
          zcz2(3)=my_1/my_4
          zcz3(3)=(-zorig3z3-zorig3z3-zorig3z3)/my_4
          zcz4(3)=my_1/my_4

        xc(4)=((grid%x(node1)-xorig4) + (grid%x(node2)-xorig4)                 &
             + (grid%x(node3)-xorig4))/my_4
          xcx1(4)=my_1/my_4
          xcx2(4)=my_1/my_4
          xcx3(4)=my_1/my_4
          xcx4(4)=(-xorig4x4-xorig4x4-xorig4x4)/my_4

        yc(4)=((grid%y(node1)-yorig4) + (grid%y(node2)-yorig4)                 &
             + (grid%y(node3)-yorig4))/my_4
          ycy1(4)=my_1/my_4
          ycy2(4)=my_1/my_4
          ycy3(4)=my_1/my_4
          ycy4(4)=(-yorig4y4-yorig4y4-yorig4y4)/my_4

        zc(4)=((grid%z(node1)-zorig4) + (grid%z(node2)-zorig4)                 &
             + (grid%z(node3)-zorig4))/my_4
          zcz1(4)=my_1/my_4
          zcz2(4)=my_1/my_4
          zcz3(4)=my_1/my_4
          zcz4(4)=(-zorig4z4-zorig4z4-zorig4z4)/my_4

        dxdtc = (grid%bc(ib)%bdxdt(n1) + grid%bc(ib)%bdxdt(n2)                 &
               + grid%bc(ib)%bdxdt(n3) + grid%bc(ib)%bdxdt(n4))/my_4
          do i = 1, getg%ntp
            dxdtcx1(i) = time_coeff(i)/my_4
            dxdtcx2(i) = time_coeff(i)/my_4
            dxdtcx3(i) = time_coeff(i)/my_4
            dxdtcx4(i) = time_coeff(i)/my_4
          end do

        dydtc = (grid%bc(ib)%bdydt(n1) + grid%bc(ib)%bdydt(n2)                 &
               + grid%bc(ib)%bdydt(n3) + grid%bc(ib)%bdydt(n4))/my_4
          do i = 1, getg%ntp
            dydtcy1(i) = time_coeff(i)/my_4
            dydtcy2(i) = time_coeff(i)/my_4
            dydtcy3(i) = time_coeff(i)/my_4
            dydtcy4(i) = time_coeff(i)/my_4
          end do

        dzdtc = (grid%bc(ib)%bdzdt(n1) + grid%bc(ib)%bdzdt(n2)                 &
               + grid%bc(ib)%bdzdt(n3) + grid%bc(ib)%bdzdt(n4))/my_4
          do i = 1, getg%ntp
            dzdtcz1(i) = time_coeff(i)/my_4
            dzdtcz2(i) = time_coeff(i)/my_4
            dzdtcz3(i) = time_coeff(i)/my_4
            dzdtcz4(i) = time_coeff(i)/my_4
          end do

        quad_node_loop : do nn = 1, 4

          do i = 1, getg%ntp
            bfacespeedx1(i) = 0.0_dp
            bfacespeedx2(i) = 0.0_dp
            bfacespeedx3(i) = 0.0_dp
            bfacespeedx4(i) = 0.0_dp

            bfacespeedy1(i) = 0.0_dp
            bfacespeedy2(i) = 0.0_dp
            bfacespeedy3(i) = 0.0_dp
            bfacespeedy4(i) = 0.0_dp

            bfacespeedz1(i) = 0.0_dp
            bfacespeedz2(i) = 0.0_dp
            bfacespeedz3(i) = 0.0_dp
            bfacespeedz4(i) = 0.0_dp
          end do

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              nn4 = n4
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
                xorigx4 = xorig1x4
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
                yorigy4 = yorig1y4
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
                zorigz4 = zorig1z4
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
                xc_refx4 = xcx4(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
                yc_refy4 = ycy4(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
                zc_refz4 = zcz4(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n4
              nn4 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
                xorigx4 = xorig2x4
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
                yorigy4 = yorig2y4
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
                zorigz4 = zorig2z4
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
                xc_refx4 = xcx4(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
                yc_refy4 = ycy4(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
                zc_refz4 = zcz4(2)
            case(3)
              nn1 = n3
              nn2 = n4
              nn3 = n1
              nn4 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
                xorigx4 = xorig3x4
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
                yorigy4 = yorig3y4
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
                zorigz4 = zorig3z4
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
                xc_refx4 = xcx4(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
                yc_refy4 = ycy4(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
                zc_refz4 = zcz4(3)
            case(4)
              nn1 = n4
              nn2 = n1
              nn3 = n2
              nn4 = n3
              xorig = xorig4
                xorigx1 = xorig4x1
                xorigx2 = xorig4x2
                xorigx3 = xorig4x3
                xorigx4 = xorig4x4
              yorig = yorig4
                yorigy1 = yorig4y1
                yorigy2 = yorig4y2
                yorigy3 = yorig4y3
                yorigy4 = yorig4y4
              zorig = zorig4
                zorigz1 = zorig4z1
                zorigz2 = zorig4z2
                zorigz3 = zorig4z3
                zorigz4 = zorig4z4
              xc_ref = xc(4)
                xc_refx1 = xcx1(4)
                xc_refx2 = xcx2(4)
                xc_refx3 = xcx3(4)
                xc_refx4 = xcx4(4)
              yc_ref = yc(4)
                yc_refy1 = ycy1(4)
                yc_refy2 = ycy2(4)
                yc_refy3 = ycy3(4)
                yc_refy4 = ycy4(4)
              zc_ref = zc(4)
                zc_refz1 = zcz1(4)
                zc_refz2 = zcz2(4)
                zc_refz3 = zcz3(4)
                zc_refz4 = zcz4(4)
            case default
          end select

          node1 = grid%bc(ib)%ibnode(nn1)
          node2 = grid%bc(ib)%ibnode(nn2)
          node3 = grid%bc(ib)%ibnode(nn3)
          node4 = grid%bc(ib)%ibnode(nn4)

          x1 = grid%x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
              x1x4 = my_0
            else if ( node1 == cnode4 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_1
            endif

          x2 = grid%x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
              x2x4 = my_0
            else if ( node2 == cnode4 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_1
            endif

          x3 = grid%x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
!             x3x4 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
!             x3x4 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
!             x3x4 = my_0
            else if ( node3 == cnode4 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_0
!             x3x4 = my_1
            endif

          x4 = grid%x(node4)
            if ( node4 == cnode1 ) then
              x4x1 = my_1
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode2 ) then
              x4x1 = my_0
              x4x2 = my_1
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode3 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_1
              x4x4 = my_0
            else if ( node4 == cnode4 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_1
            endif

          y1 = grid%y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
              y1y4 = my_0
            else if ( node1 == cnode4 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_1
            endif

          y2 = grid%y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
              y2y4 = my_0
            else if ( node2 == cnode4 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_1
            endif

          y3 = grid%y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
!             y3y4 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
!             y3y4 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
!             y3y4 = my_0
            else if ( node3 == cnode4 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_0
!             y3y4 = my_1
            endif

          y4 = grid%y(node4)
            if ( node4 == cnode1 ) then
              y4y1 = my_1
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode2 ) then
              y4y1 = my_0
              y4y2 = my_1
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode3 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_1
              y4y4 = my_0
            else if ( node4 == cnode4 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_1
            endif

          z1 = grid%z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
              z1z4 = my_0
            else if ( node1 == cnode4 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_1
            endif

          z2 = grid%z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
              z2z4 = my_0
            else if ( node2 == cnode4 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_1
            endif

          z3 = grid%z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
!             z3z4 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
!             z3z4 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
!             z3z4 = my_0
            else if ( node3 == cnode4 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_0
!             z3z4 = my_1
            endif

          z4 = grid%z(node4)
            if ( node4 == cnode1 ) then
              z4z1 = my_1
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode2 ) then
              z4z1 = my_0
              z4z2 = my_1
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode3 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_1
              z4z4 = my_0
            else if ( node4 == cnode4 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
            x0x4 = x1x4 - xorigx4
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
            y0y4 = y1y4 - yorigy4
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3
            z0z4 = z1z4 - zorigz4

          dxdt0 = grid%bc(ib)%bdxdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                dxdt0x1(i) = time_coeff(i)
                dxdt0x2(i) = my_0
                dxdt0x3(i) = my_0
                dxdt0x4(i) = my_0
              else if ( node1 == cnode2 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = time_coeff(i)
                dxdt0x3(i) = my_0
                dxdt0x4(i) = my_0
              else if ( node1 == cnode3 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = my_0
                dxdt0x3(i) = time_coeff(i)
                dxdt0x4(i) = my_0
              else if ( node1 == cnode4 ) then
                dxdt0x1(i) = my_0
                dxdt0x2(i) = my_0
                dxdt0x3(i) = my_0
                dxdt0x4(i) = time_coeff(i)
              endif
            end do

          dydt0 = grid%bc(ib)%bdydt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                dydt0y1(i) = time_coeff(i)
                dydt0y2(i) = my_0
                dydt0y3(i) = my_0
                dydt0y4(i) = my_0
              else if ( node1 == cnode2 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = time_coeff(i)
                dydt0y3(i) = my_0
                dydt0y4(i) = my_0
              else if ( node1 == cnode3 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = my_0
                dydt0y3(i) = time_coeff(i)
                dydt0y4(i) = my_0
              else if ( node1 == cnode4 ) then
                dydt0y1(i) = my_0
                dydt0y2(i) = my_0
                dydt0y3(i) = my_0
                dydt0y4(i) = time_coeff(i)
              endif
            end do

          dzdt0 = grid%bc(ib)%bdzdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                dzdt0z1(i) = time_coeff(i)
                dzdt0z2(i) = my_0
                dzdt0z3(i) = my_0
                dzdt0z4(i) = my_0
              else if ( node1 == cnode2 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = time_coeff(i)
                dzdt0z3(i) = my_0
                dzdt0z4(i) = my_0
              else if ( node1 == cnode3 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = my_0
                dzdt0z3(i) = time_coeff(i)
                dzdt0z4(i) = my_0
              else if ( node1 == cnode4 ) then
                dzdt0z1(i) = my_0
                dzdt0z2(i) = my_0
                dzdt0z3(i) = my_0
                dzdt0z4(i) = time_coeff(i)
              endif
            end do

          xl = ((x1-xorig) + (x4-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x4x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x4x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x4x3-xorigx3))/my_2
            xlx4 = ((x1x4-xorigx4) + (x4x4-xorigx4))/my_2

          yl = ((y1-yorig) + (y4-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y4y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y4y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y4y3-yorigy3))/my_2
            yly4 = ((y1y4-yorigy4) + (y4y4-yorigy4))/my_2

          zl = ((z1-zorig) + (z4-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z4z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z4z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z4z3-zorigz3))/my_2
            zlz4 = ((z1z4-zorigz4) + (z4z4-zorigz4))/my_2

          piece1 = grid%bc(ib)%bdxdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
                piece1x4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdxdt(nn4)
            do i = 1, getg%ntp
              if ( node4 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node4 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node4 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
                piece2x4(i) = my_0
              else if ( node4 == cnode4 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = time_coeff(i)
              endif
            end do

          dxdtl = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dxdtlx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtlx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtlx3(i) = (piece1x3(i) + piece2x3(i))/my_2
              dxdtlx4(i) = (piece1x4(i) + piece2x4(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdydt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
                piece1y4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdydt(nn4)
            do i = 1, getg%ntp
              if ( node4 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node4 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node4 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
                piece2y4(i) = my_0
              else if ( node4 == cnode4 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = time_coeff(i)
              endif
            end do

          dydtl = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dydtly1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtly2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtly3(i) = (piece1y3(i) + piece2y3(i))/my_2
              dydtly4(i) = (piece1y4(i) + piece2y4(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdzdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
                piece1z4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdzdt(nn4)
            do i = 1, getg%ntp
              if ( node4 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node4 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node4 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
                piece2z4(i) = my_0
              else if ( node4 == cnode4 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = time_coeff(i)
              endif
            end do

          dzdtl = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dzdtlz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtlz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtlz3(i) = (piece1z3(i) + piece2z3(i))/my_2
              dzdtlz4(i) = (piece1z4(i) + piece2z4(i))/my_2
            end do

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2
            xrx4 = ((x1x4-xorigx4) + (x2x4-xorigx4))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2
            yry4 = ((y1y4-yorigy4) + (y2y4-yorigy4))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2
            zrz4 = ((z1z4-zorigz4) + (z2z4-zorigz4))/my_2

          piece1 = grid%bc(ib)%bdxdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1x1(i) = time_coeff(i)
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1x1(i) = my_0
                piece1x2(i) = time_coeff(i)
                piece1x3(i) = my_0
                piece1x4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = time_coeff(i)
                piece1x4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1x1(i) = my_0
                piece1x2(i) = my_0
                piece1x3(i) = my_0
                piece1x4(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdxdt(nn2)
            do i = 1, getg%ntp
              if ( node2 == cnode1 ) then
                piece2x1(i) = time_coeff(i)
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2x1(i) = my_0
                piece2x2(i) = time_coeff(i)
                piece2x3(i) = my_0
                piece2x4(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = time_coeff(i)
                piece2x4(i) = my_0
              else if ( node2 == cnode4 ) then
                piece2x1(i) = my_0
                piece2x2(i) = my_0
                piece2x3(i) = my_0
                piece2x4(i) = time_coeff(i)
              endif
            end do

          dxdtr = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dxdtrx1(i) = (piece1x1(i) + piece2x1(i))/my_2
              dxdtrx2(i) = (piece1x2(i) + piece2x2(i))/my_2
              dxdtrx3(i) = (piece1x3(i) + piece2x3(i))/my_2
              dxdtrx4(i) = (piece1x4(i) + piece2x4(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdydt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1y1(i) = time_coeff(i)
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1y1(i) = my_0
                piece1y2(i) = time_coeff(i)
                piece1y3(i) = my_0
                piece1y4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = time_coeff(i)
                piece1y4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1y1(i) = my_0
                piece1y2(i) = my_0
                piece1y3(i) = my_0
                piece1y4(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdydt(nn2)
            do i = 1, getg%ntp
              if ( node2 == cnode1 ) then
                piece2y1(i) = time_coeff(i)
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2y1(i) = my_0
                piece2y2(i) = time_coeff(i)
                piece2y3(i) = my_0
                piece2y4(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = time_coeff(i)
                piece2y4(i) = my_0
              else if ( node2 == cnode4 ) then
                piece2y1(i) = my_0
                piece2y2(i) = my_0
                piece2y3(i) = my_0
                piece2y4(i) = time_coeff(i)
              endif
            end do

          dydtr = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dydtry1(i) = (piece1y1(i) + piece2y1(i))/my_2
              dydtry2(i) = (piece1y2(i) + piece2y2(i))/my_2
              dydtry3(i) = (piece1y3(i) + piece2y3(i))/my_2
              dydtry4(i) = (piece1y4(i) + piece2y4(i))/my_2
            end do

          piece1 = grid%bc(ib)%bdzdt(nn1)
            do i = 1, getg%ntp
              if ( node1 == cnode1 ) then
                piece1z1(i) = time_coeff(i)
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode2 ) then
                piece1z1(i) = my_0
                piece1z2(i) = time_coeff(i)
                piece1z3(i) = my_0
                piece1z4(i) = my_0
              else if ( node1 == cnode3 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = time_coeff(i)
                piece1z4(i) = my_0
              else if ( node1 == cnode4 ) then
                piece1z1(i) = my_0
                piece1z2(i) = my_0
                piece1z3(i) = my_0
                piece1z4(i) = time_coeff(i)
              endif
            end do

          piece2 = grid%bc(ib)%bdzdt(nn2)
            do i = 1, getg%ntp
              if ( node2 == cnode1 ) then
                piece2z1(i) = time_coeff(i)
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node2 == cnode2 ) then
                piece2z1(i) = my_0
                piece2z2(i) = time_coeff(i)
                piece2z3(i) = my_0
                piece2z4(i) = my_0
              else if ( node2 == cnode3 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = time_coeff(i)
                piece2z4(i) = my_0
              else if ( node2 == cnode4 ) then
                piece2z1(i) = my_0
                piece2z2(i) = my_0
                piece2z3(i) = my_0
                piece2z4(i) = time_coeff(i)
              endif
            end do

          dzdtr = (piece1 + piece2)/my_2
            do i = 1, getg%ntp
              dzdtrz1(i) = (piece1z1(i) + piece2z1(i))/my_2
              dzdtrz2(i) = (piece1z2(i) + piece2z2(i))/my_2
              dzdtrz3(i) = (piece1z3(i) + piece2z3(i))/my_2
              dzdtrz4(i) = (piece1z4(i) + piece2z4(i))/my_2
            end do

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))
            areaxy4 = my_half*((yry4-y0y4)*(zc_ref-z0)- (zr-z0)*(yc_refy4-y0y4))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))
            areaxz4 = my_half*((yr-y0)*(zc_refz4-z0z4)- (zrz4-z0z4)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))
            areayx4 = my_half*((zr-z0)*(xc_refx4-x0x4)- (xrx4-x0x4)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))
            areayz4 = my_half*((zrz4-z0z4)*(xc_ref-x0)- (xr-x0)*(zc_refz4-z0z4))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))
            areazx4 = my_half*((xrx4-x0x4)*(yc_ref-y0)- (yr-y0)*(xc_refx4-x0x4))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))
            areazy4 = my_half*((xr-x0)*(yc_refy4-y0y4)- (yry4-y0y4)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

          dxdtavg = (dxdt0 + dxdtr + dxdtc)/my_3
            do i = 1, getg%ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtrx1(i) + dxdtcx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtrx2(i) + dxdtcx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtrx3(i) + dxdtcx3(i))/my_3
              dxdtavgx4(i) = (dxdt0x4(i) + dxdtrx4(i) + dxdtcx4(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0
              dxdtavgy4(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
              dxdtavgz4(i) = my_0
            end do

          dydtavg = (dydt0 + dydtr + dydtc)/my_3
            do i = 1, getg%ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0
              dydtavgx4(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtry1(i) + dydtcy1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtry2(i) + dydtcy2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtry3(i) + dydtcy3(i))/my_3
              dydtavgy4(i) = (dydt0y4(i) + dydtry4(i) + dydtcy4(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
              dydtavgz4(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtr + dzdtc)/my_3
            do i = 1, getg%ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0
              dzdtavgx4(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0
              dzdtavgy4(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtrz1(i) + dzdtcz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtrz2(i) + dzdtcz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtrz3(i) + dzdtcz3(i))/my_3
              dzdtavgz4(i) = (dzdt0z4(i) + dzdtrz4(i) + dzdtcz4(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
            do i = 1, getg%ntp

! area only depends on grid coords at current time level

             if ( i == 1 ) then
              termx1(i)=dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                     + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
              termx2(i)=dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                     + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
              termx3(i)=dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                     + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)
              termx4(i)=dxdtavg*areaxx4 + areax*dxdtavgx4(i) + dydtavg*areayx4 &
                     + areay*dydtavgx4(i) + dzdtavg*areazx4 + areaz*dzdtavgx4(i)

              termy1(i)=dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                     + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
              termy2(i)=dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                     + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
              termy3(i)=dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                     + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)
              termy4(i)=dxdtavg*areaxy4 + areax*dxdtavgy4(i) + dydtavg*areayy4 &
                     + areay*dydtavgy4(i) + dzdtavg*areazy4 + areaz*dzdtavgy4(i)

              termz1(i)=dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                     + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
              termz2(i)=dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                     + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
              termz3(i)=dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                     + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
              termz4(i)=dxdtavg*areaxz4 + areax*dxdtavgz4(i) + dydtavg*areayz4 &
                     + areay*dydtavgz4(i) + dzdtavg*areazz4 + areaz*dzdtavgz4(i)
             else
              termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i)+areaz*dzdtavgx1(i)
              termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i)+areaz*dzdtavgx2(i)
              termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i)+areaz*dzdtavgx3(i)
              termx4(i)=areax*dxdtavgx4(i)+areay*dydtavgx4(i)+areaz*dzdtavgx4(i)

              termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i)+areaz*dzdtavgy1(i)
              termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i)+areaz*dzdtavgy2(i)
              termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i)+areaz*dzdtavgy3(i)
              termy4(i)=areax*dxdtavgy4(i)+areay*dydtavgy4(i)+areaz*dzdtavgy4(i)

              termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i)+areaz*dzdtavgz1(i)
              termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i)+areaz*dzdtavgz2(i)
              termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i)+areaz*dzdtavgz3(i)
              termz4(i)=areax*dxdtavgz4(i)+areay*dydtavgz4(i)+areaz*dzdtavgz4(i)
             endif
            end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, getg%ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)
              bfacespeedx4(i) = bfacespeedx4(i) - termx4(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)
              bfacespeedy4(i) = bfacespeedy4(i) - termy4(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
              bfacespeedz4(i) = bfacespeedz4(i) - termz4(i)
            end do

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))
            areaxy4 = my_half*((yc_refy4-y0y4)*(zl-z0)- (zc_ref-z0)*(yly4-y0y4))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))
            areaxz4 = my_half*((yc_ref-y0)*(zlz4-z0z4)- (zc_refz4-z0z4)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))
            areayx4 = my_half*((zc_ref-z0)*(xlx4-x0x4)- (xc_refx4-x0x4)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))
            areayz4 = my_half*((zc_refz4-z0z4)*(xl-x0)- (xc_ref-x0)*(zlz4-z0z4))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))
            areazx4 = my_half*((xc_refx4-x0x4)*(yl-y0)- (yc_ref-y0)*(xlx4-x0x4))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))
            areazy4 = my_half*((xc_ref-x0)*(yly4-y0y4)- (yc_refy4-y0y4)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

          dxdtavg = (dxdt0 + dxdtc + dxdtl)/my_3
            do i = 1, getg%ntp
              dxdtavgx1(i) = (dxdt0x1(i) + dxdtcx1(i) + dxdtlx1(i))/my_3
              dxdtavgx2(i) = (dxdt0x2(i) + dxdtcx2(i) + dxdtlx2(i))/my_3
              dxdtavgx3(i) = (dxdt0x3(i) + dxdtcx3(i) + dxdtlx3(i))/my_3
              dxdtavgx4(i) = (dxdt0x4(i) + dxdtcx4(i) + dxdtlx4(i))/my_3

              dxdtavgy1(i) = my_0
              dxdtavgy2(i) = my_0
              dxdtavgy3(i) = my_0
              dxdtavgy4(i) = my_0

              dxdtavgz1(i) = my_0
              dxdtavgz2(i) = my_0
              dxdtavgz3(i) = my_0
              dxdtavgz4(i) = my_0
            end do

          dydtavg = (dydt0 + dydtc + dydtl)/my_3
            do i = 1, getg%ntp
              dydtavgx1(i) = my_0
              dydtavgx2(i) = my_0
              dydtavgx3(i) = my_0
              dydtavgx4(i) = my_0

              dydtavgy1(i) = (dydt0y1(i) + dydtcy1(i) + dydtly1(i))/my_3
              dydtavgy2(i) = (dydt0y2(i) + dydtcy2(i) + dydtly2(i))/my_3
              dydtavgy3(i) = (dydt0y3(i) + dydtcy3(i) + dydtly3(i))/my_3
              dydtavgy4(i) = (dydt0y4(i) + dydtcy4(i) + dydtly4(i))/my_3

              dydtavgz1(i) = my_0
              dydtavgz2(i) = my_0
              dydtavgz3(i) = my_0
              dydtavgz4(i) = my_0
            end do

          dzdtavg = (dzdt0 + dzdtc + dzdtl)/my_3
            do i = 1, getg%ntp
              dzdtavgx1(i) = my_0
              dzdtavgx2(i) = my_0
              dzdtavgx3(i) = my_0
              dzdtavgx4(i) = my_0

              dzdtavgy1(i) = my_0
              dzdtavgy2(i) = my_0
              dzdtavgy3(i) = my_0
              dzdtavgy4(i) = my_0

              dzdtavgz1(i) = (dzdt0z1(i) + dzdtcz1(i) + dzdtlz1(i))/my_3
              dzdtavgz2(i) = (dzdt0z2(i) + dzdtcz2(i) + dzdtlz2(i))/my_3
              dzdtavgz3(i) = (dzdt0z3(i) + dzdtcz3(i) + dzdtlz3(i))/my_3
              dzdtavgz4(i) = (dzdt0z4(i) + dzdtcz4(i) + dzdtlz4(i))/my_3
            end do

!         term = dxdtavg*areax + dydtavg*areay + dzdtavg*areaz
            do i = 1, getg%ntp

! area only depends on grid coords at current time level

             if ( i == 1 ) then
              termx1(i)=dxdtavg*areaxx1 + areax*dxdtavgx1(i) + dydtavg*areayx1 &
                     + areay*dydtavgx1(i) + dzdtavg*areazx1 + areaz*dzdtavgx1(i)
              termx2(i)=dxdtavg*areaxx2 + areax*dxdtavgx2(i) + dydtavg*areayx2 &
                     + areay*dydtavgx2(i) + dzdtavg*areazx2 + areaz*dzdtavgx2(i)
              termx3(i)=dxdtavg*areaxx3 + areax*dxdtavgx3(i) + dydtavg*areayx3 &
                     + areay*dydtavgx3(i) + dzdtavg*areazx3 + areaz*dzdtavgx3(i)
              termx4(i)=dxdtavg*areaxx4 + areax*dxdtavgx4(i) + dydtavg*areayx4 &
                     + areay*dydtavgx4(i) + dzdtavg*areazx4 + areaz*dzdtavgx4(i)

              termy1(i)=dxdtavg*areaxy1 + areax*dxdtavgy1(i) + dydtavg*areayy1 &
                     + areay*dydtavgy1(i) + dzdtavg*areazy1 + areaz*dzdtavgy1(i)
              termy2(i)=dxdtavg*areaxy2 + areax*dxdtavgy2(i) + dydtavg*areayy2 &
                     + areay*dydtavgy2(i) + dzdtavg*areazy2 + areaz*dzdtavgy2(i)
              termy3(i)=dxdtavg*areaxy3 + areax*dxdtavgy3(i) + dydtavg*areayy3 &
                     + areay*dydtavgy3(i) + dzdtavg*areazy3 + areaz*dzdtavgy3(i)
              termy4(i)=dxdtavg*areaxy4 + areax*dxdtavgy4(i) + dydtavg*areayy4 &
                     + areay*dydtavgy4(i) + dzdtavg*areazy4 + areaz*dzdtavgy4(i)

              termz1(i)=dxdtavg*areaxz1 + areax*dxdtavgz1(i) + dydtavg*areayz1 &
                     + areay*dydtavgz1(i) + dzdtavg*areazz1 + areaz*dzdtavgz1(i)
              termz2(i)=dxdtavg*areaxz2 + areax*dxdtavgz2(i) + dydtavg*areayz2 &
                     + areay*dydtavgz2(i) + dzdtavg*areazz2 + areaz*dzdtavgz2(i)
              termz3(i)=dxdtavg*areaxz3 + areax*dxdtavgz3(i) + dydtavg*areayz3 &
                     + areay*dydtavgz3(i) + dzdtavg*areazz3 + areaz*dzdtavgz3(i)
              termz4(i)=dxdtavg*areaxz4 + areax*dxdtavgz4(i) + dydtavg*areayz4 &
                     + areay*dydtavgz4(i) + dzdtavg*areazz4 + areaz*dzdtavgz4(i)
             else
              termx1(i)=areax*dxdtavgx1(i)+areay*dydtavgx1(i)+areaz*dzdtavgx1(i)
              termx2(i)=areax*dxdtavgx2(i)+areay*dydtavgx2(i)+areaz*dzdtavgx2(i)
              termx3(i)=areax*dxdtavgx3(i)+areay*dydtavgx3(i)+areaz*dzdtavgx3(i)
              termx4(i)=areax*dxdtavgx4(i)+areay*dydtavgx4(i)+areaz*dzdtavgx4(i)

              termy1(i)=areax*dxdtavgy1(i)+areay*dydtavgy1(i)+areaz*dzdtavgy1(i)
              termy2(i)=areax*dxdtavgy2(i)+areay*dydtavgy2(i)+areaz*dzdtavgy2(i)
              termy3(i)=areax*dxdtavgy3(i)+areay*dydtavgy3(i)+areaz*dzdtavgy3(i)
              termy4(i)=areax*dxdtavgy4(i)+areay*dydtavgy4(i)+areaz*dzdtavgy4(i)

              termz1(i)=areax*dxdtavgz1(i)+areay*dydtavgz1(i)+areaz*dzdtavgz1(i)
              termz2(i)=areax*dxdtavgz2(i)+areay*dydtavgz2(i)+areaz*dzdtavgz2(i)
              termz3(i)=areax*dxdtavgz3(i)+areay*dydtavgz3(i)+areaz*dzdtavgz3(i)
              termz4(i)=areax*dxdtavgz4(i)+areay*dydtavgz4(i)+areaz*dzdtavgz4(i)
             endif
            end do

!         bfacespeed(nn1) = bfacespeed(nn1) - term

!         bfacespeed = -term
            do i = 1, getg%ntp
              bfacespeedx1(i) = bfacespeedx1(i) - termx1(i)
              bfacespeedx2(i) = bfacespeedx2(i) - termx2(i)
              bfacespeedx3(i) = bfacespeedx3(i) - termx3(i)
              bfacespeedx4(i) = bfacespeedx4(i) - termx4(i)

              bfacespeedy1(i) = bfacespeedy1(i) - termy1(i)
              bfacespeedy2(i) = bfacespeedy2(i) - termy2(i)
              bfacespeedy3(i) = bfacespeedy3(i) - termy3(i)
              bfacespeedy4(i) = bfacespeedy4(i) - termy4(i)

              bfacespeedz1(i) = bfacespeedz1(i) - termz1(i)
              bfacespeedz2(i) = bfacespeedz2(i) - termz2(i)
              bfacespeedz3(i) = bfacespeedz3(i) - termz3(i)
              bfacespeedz4(i) = bfacespeedz4(i) - termz4(i)
            end do

          local_node1c : if ( node1 <= grid%nnodes0 ) then

!           res_gcl(1,node1) = res_gcl(1,node1) + bfacespeed

            do i = 1, getg%ntp
              res_gcl_x1(i) = bfacespeedx1(i)
              res_gcl_x2(i) = bfacespeedx2(i)
              res_gcl_x3(i) = bfacespeedx3(i)
              res_gcl_x4(i) = bfacespeedx4(i)

              res_gcl_y1(i) = bfacespeedy1(i)
              res_gcl_y2(i) = bfacespeedy2(i)
              res_gcl_y3(i) = bfacespeedy3(i)
              res_gcl_y4(i) = bfacespeedy4(i)

              res_gcl_z1(i) = bfacespeedz1(i)
              res_gcl_z2(i) = bfacespeedz2(i)
              res_gcl_z3(i) = bfacespeedz3(i)
              res_gcl_z4(i) = bfacespeedz4(i)
            end do

            fcn_loop1c : do ifcn = 1, design%nfunctions

! Form the Q-transpose*lambda dot product factor for the current cost function

              factor = my_0
              do j = 1, soln%n_tot
                if ( soln%eqn_set == incompressible .and. j == 1 ) then
                  factor = factor                                              &
                             + beta*sadj%rlam(j,node1,ifcn)*sadj%coltag(j,node1)
                else
                  factor = factor                                              &
                    + Qnm1(j,node1)*sadj%rlam(j,node1,ifcn)*sadj%coltag(j,node1)
                endif
              end do
              do j = 1, soln%n_turb
                factor = factor                                                &
                          + turbnm1(j,node1)*sadj%rlam(soln%n_tot+j,node1,ifcn)&
                                          *sadj%coltag(soln%n_tot+j,node1)
              end do

              do i = 1, getg%ntp
                getg%drdxl(1,cnode1,ifcn,i) = getg%drdxl(1,cnode1,ifcn,i)      &
                                                          + res_gcl_x1(i)*factor
                getg%drdxl(1,cnode2,ifcn,i) = getg%drdxl(1,cnode2,ifcn,i)      &
                                                          + res_gcl_x2(i)*factor
                getg%drdxl(1,cnode3,ifcn,i) = getg%drdxl(1,cnode3,ifcn,i)      &
                                                          + res_gcl_x3(i)*factor
                getg%drdxl(1,cnode4,ifcn,i) = getg%drdxl(1,cnode4,ifcn,i)      &
                                                          + res_gcl_x4(i)*factor

                getg%drdxl(2,cnode1,ifcn,i) = getg%drdxl(2,cnode1,ifcn,i)      &
                                                          + res_gcl_y1(i)*factor
                getg%drdxl(2,cnode2,ifcn,i) = getg%drdxl(2,cnode2,ifcn,i)      &
                                                          + res_gcl_y2(i)*factor
                getg%drdxl(2,cnode3,ifcn,i) = getg%drdxl(2,cnode3,ifcn,i)      &
                                                          + res_gcl_y3(i)*factor
                getg%drdxl(2,cnode4,ifcn,i) = getg%drdxl(2,cnode4,ifcn,i)      &
                                                          + res_gcl_y4(i)*factor

                getg%drdxl(3,cnode1,ifcn,i) = getg%drdxl(3,cnode1,ifcn,i)      &
                                                          + res_gcl_z1(i)*factor
                getg%drdxl(3,cnode2,ifcn,i) = getg%drdxl(3,cnode2,ifcn,i)      &
                                                          + res_gcl_z2(i)*factor
                getg%drdxl(3,cnode3,ifcn,i) = getg%drdxl(3,cnode3,ifcn,i)      &
                                                          + res_gcl_z3(i)*factor
                getg%drdxl(3,cnode4,ifcn,i) = getg%drdxl(3,cnode4,ifcn,i)      &
                                                          + res_gcl_z4(i)*factor
              end do

            end do fcn_loop1c

          endif local_node1c

        end do quad_node_loop

      end do quad_face_loop

    end do bound_loop

  end subroutine dgcl_dgridb


!================================ DVOL_DGRID =================================80
!
!  Include volume linearization piece for time-dependent:
!  -d(vol)/dX * (Qn - Qn-1)/dt * lambda-f
!
!  Note rlam(n+2) is stored in sadj%rlamatn1
!       rlam(n+3) is stored in sadj%rlamatn2
!
!=============================================================================80
  subroutine dvol_dgrid(crow,design,sadj,soln,getg,nelem,nnodes0,nedgeloc,     &
                        nbound,elem,x,y,z,at_initial_state,bc)

    use element_types,        only : elem_type
    use element_defs,         only : max_node_per_cell
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use comprow_types,        only : crow_type
    use solution_getg,        only : getg_type
    use design_types,         only : design_type
    use info_depr,            only : physical_timestep, ivisc, ncyc,           &
                                     simulation_time
    use nml_nonlinear_solves, only : itime
    use kinddefs,             only : dp
    use lmpi_app,             only : lmpi_xfer
    use fun3d_constants,      only : my_0
    use bc_types,             only : bcgrid_type

    integer, intent(in) :: nelem, nnodes0, nedgeloc, nbound

    real(dp), dimension(:), intent(in) :: x, y, z

    type(crow_type),                     intent(in)    :: crow
    type(design_type),                   intent(in)    :: design
    type(elem_type),   dimension(nelem), intent(in)    :: elem
    type(bcgrid_type), dimension(:),     intent(in)    :: bc
    type(sadj_type),                     intent(inout) :: sadj
    type(soln_type),                     intent(inout) :: soln
    type(getg_type),                     intent(inout) :: getg

    logical, intent(in) :: at_initial_state

    integer :: ielem, n, nn, node, k, j, ie, edge, n1, n2
    integer :: nn1, nn2, n3, nn3, n4, nn4, n5, nn5, n6, nn6, i, ind
    integer :: inode, icol, ioff

    real(dp) :: denom
    real(dp) :: xorig1,xorig2,yorig1,yorig2,zorig1,zorig2
    real(dp) :: xl1,xr1,xl2,xr2,xm1,xm2,xavg1,yavg1,zavg1,xavg2,yavg2,zavg2
    real(dp) :: yl1,yr1,yl2,yr2,ym1,ym2,areax1,areay1,areaz1
    real(dp) :: zl1,zr1,zl2,zr2,zm1,zm2,areax2,areay2,areaz2
    real(dp) :: stored_simulation_time

    real(dp), dimension(max_node_per_cell) :: xc,yc,zc
    real(dp), dimension(max_node_per_cell) :: dareax1dy, dareax1dz
    real(dp), dimension(max_node_per_cell) :: dareay1dx, dareay1dz
    real(dp), dimension(max_node_per_cell) :: dareaz1dx, dareaz1dy
    real(dp), dimension(max_node_per_cell) :: dareax2dy, dareax2dz
    real(dp), dimension(max_node_per_cell) :: dareay2dx, dareay2dz
    real(dp), dimension(max_node_per_cell) :: dareaz2dx, dareaz2dy
    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dxcdx
    real(dp), dimension(max_node_per_cell) :: dxm1dx
    real(dp), dimension(max_node_per_cell) :: dxl1dx
    real(dp), dimension(max_node_per_cell) :: dxr1dx
    real(dp), dimension(max_node_per_cell) :: dxm2dx
    real(dp), dimension(max_node_per_cell) :: dxl2dx
    real(dp), dimension(max_node_per_cell) :: dxr2dx
    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dycdy
    real(dp), dimension(max_node_per_cell) :: dym1dy
    real(dp), dimension(max_node_per_cell) :: dyl1dy
    real(dp), dimension(max_node_per_cell) :: dyr1dy
    real(dp), dimension(max_node_per_cell) :: dym2dy
    real(dp), dimension(max_node_per_cell) :: dyl2dy
    real(dp), dimension(max_node_per_cell) :: dyr2dy
    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dzcdz
    real(dp), dimension(max_node_per_cell) :: dzm1dz
    real(dp), dimension(max_node_per_cell) :: dzl1dz
    real(dp), dimension(max_node_per_cell) :: dzr1dz
    real(dp), dimension(max_node_per_cell) :: dzm2dz
    real(dp), dimension(max_node_per_cell) :: dzl2dz
    real(dp), dimension(max_node_per_cell) :: dzr2dz
    real(dp), dimension(max_node_per_cell) :: dxorig1dx
    real(dp), dimension(max_node_per_cell) :: dyorig1dy
    real(dp), dimension(max_node_per_cell) :: dzorig1dz
    real(dp), dimension(max_node_per_cell) :: dxorig2dx
    real(dp), dimension(max_node_per_cell) :: dyorig2dy
    real(dp), dimension(max_node_per_cell) :: dzorig2dz
    real(dp), dimension(max_node_per_cell) :: xavg1dx,yavg1dy,zavg1dz
    real(dp), dimension(max_node_per_cell) :: xavg2dx,yavg2dy,zavg2dz
    real(dp), dimension(max_node_per_cell) :: term1dx,term1dy,term1dz
    real(dp), dimension(max_node_per_cell) :: term2dx,term2dy,term2dz
    real(dp), dimension(max_node_per_cell) :: vol1dx,vol1dy,vol1dz
    real(dp), dimension(max_node_per_cell) :: vol2dx,vol2dy,vol2dz
    real(dp), dimension(max_node_per_cell,design%nfunctions) :: factor
    real(dp), dimension(:,:), allocatable :: q0, q1, q2, turb0, turb1, turb2
    real(dp), dimension(:,:), allocatable :: qn, qnm1, qnp1, qnp2
    real(dp), dimension(:,:), allocatable :: turbn, turbnm1, turbnp1, turbnp2

    logical :: apply_turb_hack

  continue

! No dV/dX terms for initial state if doing BDF1

    if ( at_initial_state .and. itime == 1 ) return

    ioff = 0 ! compiler warning

    if ( at_initial_state ) then
      allocate(q0(soln%n_tot,nnodes0))
      allocate(q1(soln%n_tot,nnodes0))
      allocate(q2(soln%n_tot,nnodes0))
      allocate(turb0(soln%n_turb,nnodes0))
      allocate(turb1(soln%n_turb,nnodes0))
      allocate(turb2(soln%n_turb,nnodes0))
      allocate(qn(1,1))
      allocate(qnm1(1,1))
      allocate(qnp1(1,1))
      allocate(qnp2(1,1))
      allocate(turbn(1,1))
      allocate(turbnm1(1,1))
      allocate(turbnp1(1,1))
      allocate(turbnp2(1,1))
    else
      allocate(q0(1,1))
      allocate(q1(1,1))
      allocate(q2(1,1))
      allocate(turb0(1,1))
      allocate(turb1(1,1))
      allocate(turb2(1,1))
      allocate(qn(soln%n_tot,nnodes0))
      allocate(qnm1(soln%n_tot,nnodes0))
      allocate(qnp1(soln%n_tot,nnodes0))
      allocate(qnp2(soln%n_tot,nnodes0))
      allocate(turbn(soln%n_turb,nnodes0))
      allocate(turbnm1(soln%n_turb,nnodes0))
      allocate(turbnp1(soln%n_turb,nnodes0))
      allocate(turbnp2(soln%n_turb,nnodes0))
    endif

! First get the Q planes that we need

    load_q : if ( at_initial_state ) then

!  q0 is in qnode right now

      stored_simulation_time = simulation_time
      q0(1:soln%n_tot,1:nnodes0) = soln%q_dof(1:soln%n_tot,1:nnodes0)
      if ( ivisc >= 6 ) then
        turb0(1:soln%n_turb,1:nnodes0) = soln%turb(1:soln%n_turb,1:nnodes0)
      endif

! Now load Q from the next timestep and stick it into q1

      q1    = my_0
      turb1 = my_0

      q1(1:soln%n_tot,1:nnodes0)=soln%qatp1(1:soln%n_tot,1:nnodes0)
      if ( ivisc >= 6 ) then
        turb1(1:soln%n_turb,1:nnodes0) = soln%turbatp1(1:soln%n_turb,1:nnodes0)
      endif

! Now load Q from the next timestep and stick it into q2

      q2    = my_0
      turb2 = my_0

      load_q2 : if ( ncyc >= 2 ) then

        q2(1:soln%n_tot,1:nnodes0)=soln%qatp2(1:soln%n_tot,1:nnodes0)
        if ( ivisc >= 6 ) then
          turb2(1:soln%n_turb,1:nnodes0)=soln%turbatp2(1:soln%n_turb,1:nnodes0)
        endif

      endif load_q2

! Set qnode back to q0 and xfer it just to be safe

      simulation_time = stored_simulation_time
      soln%q_dof(1:soln%n_tot,1:nnodes0) = q0(1:soln%n_tot,1:nnodes0)
      call lmpi_xfer(soln%q_dof)
      if ( ivisc >= 6 ) then
        soln%turb(1:soln%n_turb,1:nnodes0) = turb0(1:soln%n_turb,1:nnodes0)
        call lmpi_xfer(soln%turb)
      endif

    else load_q

!  qn is in qnode right now

      stored_simulation_time = simulation_time
      qn(1:soln%n_tot,1:nnodes0) = soln%q_dof(1:soln%n_tot,1:nnodes0)
      if ( ivisc >= 6 ) then
        turbn(1:soln%n_turb,1:nnodes0) = soln%turb(1:soln%n_turb,1:nnodes0)
      endif

! Now load Q from the previous timestep and stick it into qnm1

      apply_turb_hack = .false.
      if ( physical_timestep == 1 ) then
        apply_turb_hack = .true.
      endif

      qnm1(1:soln%n_tot,1:nnodes0)= soln%qatn1(1:soln%n_tot,1:nnodes0)
      if ( ivisc >= 6 ) then
        turbnm1(1:soln%n_turb,1:nnodes0)=soln%turbatn1(1:soln%n_turb,1:nnodes0)
      endif

      if ( ivisc >= 6 .and. apply_turb_hack ) then
        do i = 1, nnodes0
          do j = 1, soln%n_turb
            ind = soln%n_tot + j
            if ( sadj%coltag(ind,i) < 0.1_dp ) turbnm1(j,i) = 0.0_dp
          end do
        end do
      endif

! Now load Q from the next timestep and stick it into qnp1

      qnp1    = my_0
      turbnp1 = my_0

      load_qnp1 : if ( physical_timestep /= ncyc ) then

        qnp1(1:soln%n_tot,1:nnodes0)=soln%qatp1(1:soln%n_tot,1:nnodes0)
         if ( ivisc >= 6 ) then
         turbnp1(1:soln%n_turb,1:nnodes0)=soln%turbatp1(1:soln%n_turb,1:nnodes0)
         endif

      endif load_qnp1

! Now load Q from the next timestep and stick it into qnp2

      qnp2    = my_0
      turbnp2 = my_0

      load_qnp2 : if ( physical_timestep /= ncyc .and.                         &
                       physical_timestep /= ncyc-1 ) then

        qnp2(1:soln%n_tot,1:nnodes0)=soln%qatp2(1:soln%n_tot,1:nnodes0)
         if ( ivisc >= 6 ) then
         turbnp2(1:soln%n_turb,1:nnodes0)=soln%turbatp2(1:soln%n_turb,1:nnodes0)
         endif

      endif load_qnp2

! Set qnode back to qn and xfer it just to be safe

      simulation_time = stored_simulation_time
      soln%q_dof(1:soln%n_tot,1:nnodes0) = qn(1:soln%n_tot,1:nnodes0)
      call lmpi_xfer(soln%q_dof)
      if ( ivisc >= 6 ) then
        soln%turb(1:soln%n_turb,1:nnodes0) = turbn(1:soln%n_turb,1:nnodes0)
        call lmpi_xfer(soln%turb)
      endif

    endif load_q

! Now linearize dual volume and compute the terms we want

    elem_loop1 : do ielem = 1, nelem

     cell_loop : do n = 1, elem(ielem)%ncell

! first compute the dQ*rlam factor at each node of the cell

      call get_factor(design,at_initial_state,soln,elem,nnodes0,sadj,q0,q1,q2, &
                      qn,qnm1,qnp1,qnp2,turb0,turb1,turb2,turbn,turbnm1,       &
                      turbnp1,turbnp2,n,ielem,factor)

!     what follows are the d(normal)/d(coord) pieces

      denom = real(elem(ielem)%node_per_cell,dp)

!     cell center (avg of all points in cell)

      xc(:) = 0.0_dp
      yc(:) = 0.0_dp
      zc(:) = 0.0_dp

      do nn = 1,elem(ielem)%node_per_cell
        node  = elem(ielem)%c2n(nn,n)
        do k = 1, elem(ielem)%node_per_cell
          xc(k) = xc(k) + (x(node) - x(elem(ielem)%c2n(k,n)))
          yc(k) = yc(k) + (y(node) - y(elem(ielem)%c2n(k,n)))
          zc(k) = zc(k) + (z(node) - z(elem(ielem)%c2n(k,n)))
        end do
      end do

      do k = 1, elem(ielem)%node_per_cell
        xc(k) = xc(k)/denom
        yc(k) = yc(k)/denom
        zc(k) = zc(k)/denom
      end do

      do j = 1, elem(ielem)%node_per_cell
        do k = 1, elem(ielem)%node_per_cell
          if ( j == k ) then
            dxcdx(j,k) = 1.0_dp/denom - 1.0_dp
            dycdy(j,k) = 1.0_dp/denom - 1.0_dp
            dzcdz(j,k) = 1.0_dp/denom - 1.0_dp
          else
            dxcdx(j,k) = 1.0_dp/denom
            dycdy(j,k) = 1.0_dp/denom
            dzcdz(j,k) = 1.0_dp/denom
          endif
        end do
      end do

!     loop over edges of the cell

      edge_loop_cell : do ie = 1,elem(ielem)%edge_per_cell

!       initialize local sensitivity arrays

        dxorig1dx(:) = 0.0_dp
        dyorig1dy(:) = 0.0_dp
        dzorig1dz(:) = 0.0_dp

        dxorig2dx(:) = 0.0_dp
        dyorig2dy(:) = 0.0_dp
        dzorig2dz(:) = 0.0_dp

        dxm1dx(:) = 0.0_dp
        dym1dy(:) = 0.0_dp
        dzm1dz(:) = 0.0_dp

        dxm2dx(:) = 0.0_dp
        dym2dy(:) = 0.0_dp
        dzm2dz(:) = 0.0_dp

        dxl1dx(:) = 0.0_dp
        dyl1dy(:) = 0.0_dp
        dzl1dz(:) = 0.0_dp

        dxl2dx(:) = 0.0_dp
        dyl2dy(:) = 0.0_dp
        dzl2dz(:) = 0.0_dp

        dxr1dx(:) = 0.0_dp
        dyr1dy(:) = 0.0_dp
        dzr1dz(:) = 0.0_dp

        dxr2dx(:) = 0.0_dp
        dyr2dy(:) = 0.0_dp
        dzr2dz(:) = 0.0_dp

        vol1dx(:) = 0.0_dp
        vol1dy(:) = 0.0_dp
        vol1dz(:) = 0.0_dp

        vol2dx(:) = 0.0_dp
        vol2dy(:) = 0.0_dp
        vol2dz(:) = 0.0_dp

!       global edge number

        edge = elem(ielem)%c2e(ie,n)

        if (edge > nedgeloc) cycle edge_loop_cell

!       global node numbers of edge endpoints

        n1 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,1),n)
        n2 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,2),n)

!       cell node numbers of edge endpoints

        nn1 = elem(ielem)%local_e2n(ie,1)
        nn2 = elem(ielem)%local_e2n(ie,2)

!       Establish reference origins at each edge endpoint to avoid roundoff

        xorig1 = x(n1)
        yorig1 = y(n1)
        zorig1 = z(n1)

        xorig2 = x(n2)
        yorig2 = y(n2)
        zorig2 = z(n2)

        dxorig1dx(nn1) = 1.0_dp
        dyorig1dy(nn1) = 1.0_dp
        dzorig1dz(nn1) = 1.0_dp

        dxorig2dx(nn2) = 1.0_dp
        dyorig2dy(nn2) = 1.0_dp
        dzorig2dz(nn2) = 1.0_dp

!       edge midpoint referenced to local origins

        xm1 = ((x(n1)-xorig1) + (x(n2)-xorig1))/2._dp
        ym1 = ((y(n1)-yorig1) + (y(n2)-yorig1))/2._dp
        zm1 = ((z(n1)-zorig1) + (z(n2)-zorig1))/2._dp

        dxm1dx(nn1)=1.0_dp/2._dp - dxorig1dx(nn1)/2.0_dp - dxorig1dx(nn1)/2.0_dp
        dym1dy(nn1)=1.0_dp/2._dp - dyorig1dy(nn1)/2.0_dp - dyorig1dy(nn1)/2.0_dp
        dzm1dz(nn1)=1.0_dp/2._dp - dzorig1dz(nn1)/2.0_dp - dzorig1dz(nn1)/2.0_dp

        dxm1dx(nn2)=1.0_dp/2._dp - dxorig1dx(nn2)/2.0_dp - dxorig1dx(nn2)/2.0_dp
        dym1dy(nn2)=1.0_dp/2._dp - dyorig1dy(nn2)/2.0_dp - dyorig1dy(nn2)/2.0_dp
        dzm1dz(nn2)=1.0_dp/2._dp - dzorig1dz(nn2)/2.0_dp - dzorig1dz(nn2)/2.0_dp

        xm2 = ((x(n1)-xorig2) + (x(n2)-xorig2))/2._dp
        ym2 = ((y(n1)-yorig2) + (y(n2)-yorig2))/2._dp
        zm2 = ((z(n1)-zorig2) + (z(n2)-zorig2))/2._dp

        dxm2dx(nn1)=1.0_dp/2._dp - dxorig2dx(nn1)/2.0_dp - dxorig2dx(nn1)/2.0_dp
        dym2dy(nn1)=1.0_dp/2._dp - dyorig2dy(nn1)/2.0_dp - dyorig2dy(nn1)/2.0_dp
        dzm2dz(nn1)=1.0_dp/2._dp - dzorig2dz(nn1)/2.0_dp - dzorig2dz(nn1)/2.0_dp

        dxm2dx(nn2)=1.0_dp/2._dp - dxorig2dx(nn2)/2.0_dp - dxorig2dx(nn2)/2.0_dp
        dym2dy(nn2)=1.0_dp/2._dp - dyorig2dy(nn2)/2.0_dp - dyorig2dy(nn2)/2.0_dp
        dzm2dz(nn2)=1.0_dp/2._dp - dzorig2dz(nn2)/2.0_dp - dzorig2dz(nn2)/2.0_dp

!       compute left face centroid referenced to local origins

        n3 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,3),n)

        nn3 = elem(ielem)%local_e2n(ie,3)

        if (elem(ielem)%local_e2n(ie,4) /= 0) then

!         quad face

          n4 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,4),n)

          nn4 = elem(ielem)%local_e2n(ie,4)

          xl1 = ((x(n1)-xorig1) + (x(n2)-xorig1) +                             &
                 (x(n3)-xorig1) + (x(n4)-xorig1))/4._dp
          yl1 = ((y(n1)-yorig1) + (y(n2)-yorig1) +                             &
                 (y(n3)-yorig1) + (y(n4)-yorig1))/4._dp
          zl1 = ((z(n1)-zorig1) + (z(n2)-zorig1) +                             &
                 (z(n3)-zorig1) + (z(n4)-zorig1))/4._dp

          dxl1dx(nn1) = 1.0_dp/4._dp - dxorig1dx(nn1)
          dyl1dy(nn1) = 1.0_dp/4._dp - dyorig1dy(nn1)
          dzl1dz(nn1) = 1.0_dp/4._dp - dzorig1dz(nn1)

          dxl1dx(nn2) = 1.0_dp/4._dp - dxorig1dx(nn2)
          dyl1dy(nn2) = 1.0_dp/4._dp - dyorig1dy(nn2)
          dzl1dz(nn2) = 1.0_dp/4._dp - dzorig1dz(nn2)

          dxl1dx(nn3) = 1.0_dp/4._dp - dxorig1dx(nn3)
          dyl1dy(nn3) = 1.0_dp/4._dp - dyorig1dy(nn3)
          dzl1dz(nn3) = 1.0_dp/4._dp - dzorig1dz(nn3)

          dxl1dx(nn4) = 1.0_dp/4._dp - dxorig1dx(nn4)
          dyl1dy(nn4) = 1.0_dp/4._dp - dyorig1dy(nn4)
          dzl1dz(nn4) = 1.0_dp/4._dp - dzorig1dz(nn4)

          xl2 = ((x(n1)-xorig2) + (x(n2)-xorig2) +                             &
                 (x(n3)-xorig2) + (x(n4)-xorig2))/4._dp
          yl2 = ((y(n1)-yorig2) + (y(n2)-yorig2) +                             &
                 (y(n3)-yorig2) + (y(n4)-yorig2))/4._dp
          zl2 = ((z(n1)-zorig2) + (z(n2)-zorig2) +                             &
                 (z(n3)-zorig2) + (z(n4)-zorig2))/4._dp

          dxl2dx(nn1) = 1.0_dp/4._dp - dxorig2dx(nn1)
          dyl2dy(nn1) = 1.0_dp/4._dp - dyorig2dy(nn1)
          dzl2dz(nn1) = 1.0_dp/4._dp - dzorig2dz(nn1)

          dxl2dx(nn2) = 1.0_dp/4._dp - dxorig2dx(nn2)
          dyl2dy(nn2) = 1.0_dp/4._dp - dyorig2dy(nn2)
          dzl2dz(nn2) = 1.0_dp/4._dp - dzorig2dz(nn2)

          dxl2dx(nn3) = 1.0_dp/4._dp - dxorig2dx(nn3)
          dyl2dy(nn3) = 1.0_dp/4._dp - dyorig2dy(nn3)
          dzl2dz(nn3) = 1.0_dp/4._dp - dzorig2dz(nn3)

          dxl2dx(nn4) = 1.0_dp/4._dp - dxorig2dx(nn4)
          dyl2dy(nn4) = 1.0_dp/4._dp - dyorig2dy(nn4)
          dzl2dz(nn4) = 1.0_dp/4._dp - dzorig2dz(nn4)

        else

!         tria face

          xl1 = ((x(n1)-xorig1) + (x(n2)-xorig1) + (x(n3)-xorig1))/3._dp
          yl1 = ((y(n1)-yorig1) + (y(n2)-yorig1) + (y(n3)-yorig1))/3._dp
          zl1 = ((z(n1)-zorig1) + (z(n2)-zorig1) + (z(n3)-zorig1))/3._dp

          dxl1dx(nn1) = 1.0_dp/3._dp - dxorig1dx(nn1)
          dyl1dy(nn1) = 1.0_dp/3._dp - dyorig1dy(nn1)
          dzl1dz(nn1) = 1.0_dp/3._dp - dzorig1dz(nn1)

          dxl1dx(nn2) = 1.0_dp/3._dp - dxorig1dx(nn2)
          dyl1dy(nn2) = 1.0_dp/3._dp - dyorig1dy(nn2)
          dzl1dz(nn2) = 1.0_dp/3._dp - dzorig1dz(nn2)

          dxl1dx(nn3) = 1.0_dp/3._dp - dxorig1dx(nn3)
          dyl1dy(nn3) = 1.0_dp/3._dp - dyorig1dy(nn3)
          dzl1dz(nn3) = 1.0_dp/3._dp - dzorig1dz(nn3)

          xl2 = ((x(n1)-xorig2) + (x(n2)-xorig2) + (x(n3)-xorig2))/3._dp
          yl2 = ((y(n1)-yorig2) + (y(n2)-yorig2) + (y(n3)-yorig2))/3._dp
          zl2 = ((z(n1)-zorig2) + (z(n2)-zorig2) + (z(n3)-zorig2))/3._dp

          dxl2dx(nn1) = 1.0_dp/3._dp - dxorig2dx(nn1)
          dyl2dy(nn1) = 1.0_dp/3._dp - dyorig2dy(nn1)
          dzl2dz(nn1) = 1.0_dp/3._dp - dzorig2dz(nn1)

          dxl2dx(nn2) = 1.0_dp/3._dp - dxorig2dx(nn2)
          dyl2dy(nn2) = 1.0_dp/3._dp - dyorig2dy(nn2)
          dzl2dz(nn2) = 1.0_dp/3._dp - dzorig2dz(nn2)

          dxl2dx(nn3) = 1.0_dp/3._dp - dxorig2dx(nn3)
          dyl2dy(nn3) = 1.0_dp/3._dp - dyorig2dy(nn3)
          dzl2dz(nn3) = 1.0_dp/3._dp - dzorig2dz(nn3)

        end if

!       compute right face centroid

        n5 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,5),n)

        nn5 = elem(ielem)%local_e2n(ie,5)

        if (elem(ielem)%local_e2n(ie,6) /= 0) then

!         quad face

          n6 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,6),n)

          nn6 = elem(ielem)%local_e2n(ie,6)

          xr1 = ((x(n1)-xorig1) + (x(n2)-xorig1) +                             &
                 (x(n5)-xorig1) + (x(n6)-xorig1))/4._dp
          yr1 = ((y(n1)-yorig1) + (y(n2)-yorig1) +                             &
                 (y(n5)-yorig1) + (y(n6)-yorig1))/4._dp
          zr1 = ((z(n1)-zorig1) + (z(n2)-zorig1) +                             &
                 (z(n5)-zorig1) + (z(n6)-zorig1))/4._dp

          dxr1dx(nn1) = 1.0_dp/4._dp - dxorig1dx(nn1)
          dyr1dy(nn1) = 1.0_dp/4._dp - dyorig1dy(nn1)
          dzr1dz(nn1) = 1.0_dp/4._dp - dzorig1dz(nn1)

          dxr1dx(nn2) = 1.0_dp/4._dp - dxorig1dx(nn2)
          dyr1dy(nn2) = 1.0_dp/4._dp - dyorig1dy(nn2)
          dzr1dz(nn2) = 1.0_dp/4._dp - dzorig1dz(nn2)

          dxr1dx(nn5) = 1.0_dp/4._dp - dxorig1dx(nn5)
          dyr1dy(nn5) = 1.0_dp/4._dp - dyorig1dy(nn5)
          dzr1dz(nn5) = 1.0_dp/4._dp - dzorig1dz(nn5)

          dxr1dx(nn6) = 1.0_dp/4._dp - dxorig1dx(nn6)
          dyr1dy(nn6) = 1.0_dp/4._dp - dyorig1dy(nn6)
          dzr1dz(nn6) = 1.0_dp/4._dp - dzorig1dz(nn6)

          xr2 = ((x(n1)-xorig2) + (x(n2)-xorig2) +                             &
                 (x(n5)-xorig2) + (x(n6)-xorig2))/4._dp
          yr2 = ((y(n1)-yorig2) + (y(n2)-yorig2) +                             &
                 (y(n5)-yorig2) + (y(n6)-yorig2))/4._dp
          zr2 = ((z(n1)-zorig2) + (z(n2)-zorig2) +                             &
                 (z(n5)-zorig2) + (z(n6)-zorig2))/4._dp

          dxr2dx(nn1) = 1.0_dp/4._dp - dxorig2dx(nn1)
          dyr2dy(nn1) = 1.0_dp/4._dp - dyorig2dy(nn1)
          dzr2dz(nn1) = 1.0_dp/4._dp - dzorig2dz(nn1)

          dxr2dx(nn2) = 1.0_dp/4._dp - dxorig2dx(nn2)
          dyr2dy(nn2) = 1.0_dp/4._dp - dyorig2dy(nn2)
          dzr2dz(nn2) = 1.0_dp/4._dp - dzorig2dz(nn2)

          dxr2dx(nn5) = 1.0_dp/4._dp - dxorig2dx(nn5)
          dyr2dy(nn5) = 1.0_dp/4._dp - dyorig2dy(nn5)
          dzr2dz(nn5) = 1.0_dp/4._dp - dzorig2dz(nn5)

          dxr2dx(nn6) = 1.0_dp/4._dp - dxorig2dx(nn6)
          dyr2dy(nn6) = 1.0_dp/4._dp - dyorig2dy(nn6)
          dzr2dz(nn6) = 1.0_dp/4._dp - dzorig2dz(nn6)

        else

!         tria face

          xr1 = ((x(n1)-xorig1) + (x(n2)-xorig1) + (x(n5)-xorig1))/3._dp
          yr1 = ((y(n1)-yorig1) + (y(n2)-yorig1) + (y(n5)-yorig1))/3._dp
          zr1 = ((z(n1)-zorig1) + (z(n2)-zorig1) + (z(n5)-zorig1))/3._dp

          dxr1dx(nn1) = 1.0_dp/3._dp - dxorig1dx(nn1)
          dyr1dy(nn1) = 1.0_dp/3._dp - dyorig1dy(nn1)
          dzr1dz(nn1) = 1.0_dp/3._dp - dzorig1dz(nn1)

          dxr1dx(nn2) = 1.0_dp/3._dp - dxorig1dx(nn2)
          dyr1dy(nn2) = 1.0_dp/3._dp - dyorig1dy(nn2)
          dzr1dz(nn2) = 1.0_dp/3._dp - dzorig1dz(nn2)

          dxr1dx(nn5) = 1.0_dp/3._dp - dxorig1dx(nn5)
          dyr1dy(nn5) = 1.0_dp/3._dp - dyorig1dy(nn5)
          dzr1dz(nn5) = 1.0_dp/3._dp - dzorig1dz(nn5)

          xr2 = ((x(n1)-xorig2) + (x(n2)-xorig2) + (x(n5)-xorig2))/3._dp
          yr2 = ((y(n1)-yorig2) + (y(n2)-yorig2) + (y(n5)-yorig2))/3._dp
          zr2 = ((z(n1)-zorig2) + (z(n2)-zorig2) + (z(n5)-zorig2))/3._dp

          dxr2dx(nn1) = 1.0_dp/3._dp - dxorig2dx(nn1)
          dyr2dy(nn1) = 1.0_dp/3._dp - dyorig2dy(nn1)
          dzr2dz(nn1) = 1.0_dp/3._dp - dzorig2dz(nn1)

          dxr2dx(nn2) = 1.0_dp/3._dp - dxorig2dx(nn2)
          dyr2dy(nn2) = 1.0_dp/3._dp - dyorig2dy(nn2)
          dzr2dz(nn2) = 1.0_dp/3._dp - dzorig2dz(nn2)

          dxr2dx(nn5) = 1.0_dp/3._dp - dxorig2dx(nn5)
          dyr2dy(nn5) = 1.0_dp/3._dp - dyorig2dy(nn5)
          dzr2dz(nn5) = 1.0_dp/3._dp - dzorig2dz(nn5)

        end if

!       (dual) triangle xm-xr-xc

        areax1 = 0.5_dp*( (yr1-ym1)*(zc(elem(ielem)%local_e2n(ie,1))-zm1)      &
                        - (zr1-zm1)*(yc(elem(ielem)%local_e2n(ie,1))-ym1) )
!         dareax1dx(:)=0.0_dp
          dareax1dy(:)=0.5_dp*( (dyr1dy(:)-dym1dy(:))*                         &
                       (zc(elem(ielem)%local_e2n(ie,1))-zm1)                   &
                       - (zr1-zm1)*(dycdy(elem(ielem)%local_e2n(ie,1),:)       &
                       -dym1dy(:)) )
          dareax1dz(:)=0.5_dp*( (yr1-ym1)*(dzcdz(elem(ielem)%local_e2n(ie,1),:)&
                      -dzm1dz(:)) - (dzr1dz(:)-dzm1dz(:))                      &
                      *(yc(elem(ielem)%local_e2n(ie,1))-ym1) )

        areay1 = 0.5_dp*( (zr1-zm1)*(xc(elem(ielem)%local_e2n(ie,1))-xm1)      &
                        - (xr1-xm1)*(zc(elem(ielem)%local_e2n(ie,1))-zm1) )
          dareay1dx(:)=0.5_dp*( (zr1-zm1)*(dxcdx(elem(ielem)%local_e2n(ie,1),:)&
                      -dxm1dx(:)) - (dxr1dx(:)-dxm1dx(:))                      &
                      *(zc(elem(ielem)%local_e2n(ie,1))-zm1) )
!         dareay1dy(:)=0.0_dp
          dareay1dz(:)=0.5_dp*( (dzr1dz(:)-dzm1dz(:))                          &
                      *(xc(elem(ielem)%local_e2n(ie,1))-xm1)                   &
                    -(xr1-xm1)*(dzcdz(elem(ielem)%local_e2n(ie,1),:)-dzm1dz(:)))

        areaz1 = 0.5_dp*( (xr1-xm1)*(yc(elem(ielem)%local_e2n(ie,1))-ym1)      &
                        - (yr1-ym1)*(xc(elem(ielem)%local_e2n(ie,1))-xm1) )
          dareaz1dx(:)=0.5_dp*( (dxr1dx(:)-dxm1dx(:))                          &
                      *(yc(elem(ielem)%local_e2n(ie,1))-ym1)                   &
                      - (yr1-ym1)*(dxcdx(elem(ielem)%local_e2n(ie,1),:)-       &
                      dxm1dx(:)) )
          dareaz1dy(:)=0.5_dp*( (xr1-xm1)*(dycdy(elem(ielem)%local_e2n(ie,1),:)&
                      -dym1dy(:)) - (dyr1dy(:)-dym1dy(:))                      &
                      *(xc(elem(ielem)%local_e2n(ie,1))-xm1) )
!         dareaz1dz(:)=0.0_dp


        areax2 = 0.5_dp*( (yr2-ym2)*(zc(elem(ielem)%local_e2n(ie,2))-zm2)      &
                        - (zr2-zm2)*(yc(elem(ielem)%local_e2n(ie,2))-ym2) )
!         dareax2dx(:)=0.0_dp
          dareax2dy(:)=0.5_dp*( (dyr2dy(:)-dym2dy(:))                          &
                      *(zc(elem(ielem)%local_e2n(ie,2))-zm2)                   &
                    -(zr2-zm2)*(dycdy(elem(ielem)%local_e2n(ie,2),:)-dym2dy(:)))
          dareax2dz(:)=0.5_dp*( (yr2-ym2)*(dzcdz(elem(ielem)%local_e2n(ie,2),:)&
                      -dzm2dz(:)) - (dzr2dz(:)-dzm2dz(:))                      &
                      *(yc(elem(ielem)%local_e2n(ie,2))-ym2) )

        areay2 = 0.5_dp*( (zr2-zm2)*(xc(elem(ielem)%local_e2n(ie,2))-xm2)      &
                        - (xr2-xm2)*(zc(elem(ielem)%local_e2n(ie,2))-zm2) )
          dareay2dx(:)=0.5_dp*( (zr2-zm2)*(dxcdx(elem(ielem)%local_e2n(ie,2),:)&
                      -dxm2dx(:)) - (dxr2dx(:)-dxm2dx(:))                      &
                      *(zc(elem(ielem)%local_e2n(ie,2))-zm2) )
!         dareay2dy(:)=0.0_dp
          dareay2dz(:)=0.5_dp*( (dzr2dz(:)-dzm2dz(:))                          &
                      *(xc(elem(ielem)%local_e2n(ie,2))-xm2)                   &
                  - (xr2-xm2)*(dzcdz(elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:)) )

        areaz2 = 0.5_dp*( (xr2-xm2)*(yc(elem(ielem)%local_e2n(ie,2))-ym2)      &
                        - (yr2-ym2)*(xc(elem(ielem)%local_e2n(ie,2))-xm2) )
          dareaz2dx(:)=0.5_dp*( (dxr2dx(:)-dxm2dx(:))                          &
                      *(yc(elem(ielem)%local_e2n(ie,2))-ym2)                   &
                  - (yr2-ym2)*(dxcdx(elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:)) )
          dareaz2dy(:)=0.5_dp*( (xr2-xm2)*(dycdy(elem(ielem)%local_e2n(ie,2),:)&
                      -dym2dy(:)) - (dyr2dy(:)-dym2dy(:))                      &
                      *(xc(elem(ielem)%local_e2n(ie,2))-xm2) )
!         dareaz2dz(:)=0.0_dp

        xavg1 = (xm1 + xr1 + xc(elem(ielem)%local_e2n(ie,1)))/3._dp
          xavg1dx(:) = (dxm1dx(:) + dxr1dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,1),:))/3._dp

        yavg1 = (ym1 + yr1 + yc(elem(ielem)%local_e2n(ie,1)))/3._dp
          yavg1dy(:) = (dym1dy(:) + dyr1dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,1),:))/3._dp

        zavg1 = (zm1 + zr1 + zc(elem(ielem)%local_e2n(ie,1)))/3._dp
          zavg1dz(:) = (dzm1dz(:) + dzr1dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,1),:))/3._dp

        xavg2 = (xm2 + xr2 + xc(elem(ielem)%local_e2n(ie,2)))/3._dp
          xavg2dx(:) = (dxm2dx(:) + dxr2dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,2),:))/3._dp

        yavg2 = (ym2 + yr2 + yc(elem(ielem)%local_e2n(ie,2)))/3._dp
          yavg2dy(:) = (dym2dy(:) + dyr2dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,2),:))/3._dp

        zavg2 = (zm2 + zr2 + zc(elem(ielem)%local_e2n(ie,2)))/3._dp
          zavg2dz(:) = (dzm2dz(:) + dzr2dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,2),:))/3._dp

!       term1 = (xavg1*areax1 + yavg1*areay1 + zavg1*areaz1)/3._dp
         term1dx(:)=(yavg1*dareay1dx(:)+zavg1*dareaz1dx(:) + xavg1dx(:)*areax1)&
                   /3._dp
         term1dy(:)=(xavg1*dareax1dy(:)+zavg1*dareaz1dy(:) + yavg1dy(:)*areay1)&
                   /3._dp
         term1dz(:)=(xavg1*dareax1dz(:)+yavg1*dareay1dz(:) + zavg1dz(:)*areaz1)&
                   /3._dp

!       term2 = (xavg2*areax2 + yavg2*areay2 + zavg2*areaz2)/3._dp
         term2dx(:)=(yavg2*dareay2dx(:)+zavg2*dareaz2dx(:) + xavg2dx(:)*areax2)&
                   /3._dp
         term2dy(:)=(xavg2*dareax2dy(:)+zavg2*dareaz2dy(:) + yavg2dy(:)*areay2)&
                   /3._dp
         term2dz(:)=(xavg2*dareax2dz(:)+yavg2*dareay2dz(:) + zavg2dz(:)*areaz2)&
                   /3._dp

!       vol1 = vol1 + term1
!       vol2 = vol2 - term2

        vol1dx(:) = vol1dx(:) + term1dx(:)
        vol1dy(:) = vol1dy(:) + term1dy(:)
        vol1dz(:) = vol1dz(:) + term1dz(:)

        vol2dx(:) = vol2dx(:) - term2dx(:)
        vol2dy(:) = vol2dy(:) - term2dy(:)
        vol2dz(:) = vol2dz(:) - term2dz(:)

!       (dual) triangle xm-xc-xl

        areax1 = 0.5_dp*( (yc(elem(ielem)%local_e2n(ie,1))-ym1)*(zl1-zm1)      &
                        - (zc(elem(ielem)%local_e2n(ie,1))-zm1)*(yl1-ym1) )
!         dareax1dx(:)=0.0_dp
          dareax1dy(:)=0.5_dp*( (dycdy(elem(ielem)%local_e2n(ie,1),:)          &
                      -dym1dy(:))*(zl1-zm1)                                    &
                 - (zc(elem(ielem)%local_e2n(ie,1))-zm1)*(dyl1dy(:)-dym1dy(:)) )
          dareax1dz(:)=0.5_dp*( (yc(elem(ielem)%local_e2n(ie,1))-ym1)          &
                      *(dzl1dz(:)-dzm1dz(:))                                   &
                  - (dzcdz(elem(ielem)%local_e2n(ie,1),:)-dzm1dz(:))*(yl1-ym1) )

        areay1 = 0.5_dp*( (zc(elem(ielem)%local_e2n(ie,1))-zm1)*(xl1-xm1)      &
                        - (xc(elem(ielem)%local_e2n(ie,1))-xm1)*(zl1-zm1) )
          dareay1dx(:)=0.5_dp*( (zc(elem(ielem)%local_e2n(ie,1))-zm1)          &
                      *(dxl1dx(:)-dxm1dx(:))                                   &
                  - (dxcdx(elem(ielem)%local_e2n(ie,1),:)-dxm1dx(:))*(zl1-zm1) )
!         dareay1dy(:)=0.0_dp
          dareay1dz(:)=0.5_dp*( (dzcdz(elem(ielem)%local_e2n(ie,1),:)          &
                      -dzm1dz(:))*(xl1-xm1)                                    &
                 - (xc(elem(ielem)%local_e2n(ie,1))-xm1)*(dzl1dz(:)-dzm1dz(:)) )

        areaz1 = 0.5_dp*( (xc(elem(ielem)%local_e2n(ie,1))-xm1)*(yl1-ym1)      &
                        - (yc(elem(ielem)%local_e2n(ie,1))-ym1)*(xl1-xm1) )
          dareaz1dx(:)=0.5_dp*( (dxcdx(elem(ielem)%local_e2n(ie,1),:)          &
                      -dxm1dx(:))*(yl1-ym1)                                    &
                 - (yc(elem(ielem)%local_e2n(ie,1))-ym1)*(dxl1dx(:)-dxm1dx(:)) )
          dareaz1dy(:)=0.5_dp*( (xc(elem(ielem)%local_e2n(ie,1))-xm1)          &
                      *(dyl1dy(:)-dym1dy(:))                                   &
                  - (dycdy(elem(ielem)%local_e2n(ie,1),:)-dym1dy(:))*(xl1-xm1) )
!         dareaz1dz(:)=0.0_dp

        areax2 = 0.5_dp*( (yc(elem(ielem)%local_e2n(ie,2))-ym2)*(zl2-zm2)      &
                        - (zc(elem(ielem)%local_e2n(ie,2))-zm2)*(yl2-ym2) )
!         dareax2dx(:)=0.0_dp
          dareax2dy(:)=0.5_dp*( (dycdy(elem(ielem)%local_e2n(ie,2),:)          &
                      -dym2dy(:))*(zl2-zm2)                                    &
                 - (zc(elem(ielem)%local_e2n(ie,2))-zm2)*(dyl2dy(:)-dym2dy(:)) )
          dareax2dz(:)=0.5_dp*( (yc(elem(ielem)%local_e2n(ie,2))-ym2)          &
                      *(dzl2dz(:)-dzm2dz(:))                                   &
                  - (dzcdz(elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:))*(yl2-ym2) )

        areay2 = 0.5_dp*( (zc(elem(ielem)%local_e2n(ie,2))-zm2)*(xl2-xm2)      &
                        - (xc(elem(ielem)%local_e2n(ie,2))-xm2)*(zl2-zm2) )
          dareay2dx(:)=0.5_dp*( (zc(elem(ielem)%local_e2n(ie,2))-zm2)          &
                      *(dxl2dx(:)-dxm2dx(:))                                   &
                  - (dxcdx(elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:))*(zl2-zm2) )
!         dareay2dy(:)=0.0_dp
          dareay2dz(:)=0.5_dp*( (dzcdz(elem(ielem)%local_e2n(ie,2),:)          &
                      -dzm2dz(:))*(xl2-xm2)                                    &
                 - (xc(elem(ielem)%local_e2n(ie,2))-xm2)*(dzl2dz(:)-dzm2dz(:)) )

        areaz2 = 0.5_dp*( (xc(elem(ielem)%local_e2n(ie,2))-xm2)*(yl2-ym2)      &
                        - (yc(elem(ielem)%local_e2n(ie,2))-ym2)*(xl2-xm2) )
          dareaz2dx(:)=0.5_dp*( (dxcdx(elem(ielem)%local_e2n(ie,2),:)          &
                      -dxm2dx(:))*(yl2-ym2)                                    &
                 - (yc(elem(ielem)%local_e2n(ie,2))-ym2)*(dxl2dx(:)-dxm2dx(:)) )
          dareaz2dy(:)=0.5_dp*( (xc(elem(ielem)%local_e2n(ie,2))-xm2)          &
                      *(dyl2dy(:)-dym2dy(:))                                   &
                  - (dycdy(elem(ielem)%local_e2n(ie,2),:)-dym2dy(:))*(xl2-xm2) )
!         dareaz2dz(:)=0.0_dp

        xavg1 = (xm1 + xl1 + xc(elem(ielem)%local_e2n(ie,1)))/3._dp
          xavg1dx(:) = (dxm1dx(:) + dxl1dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,1),:))/3._dp

        yavg1 = (ym1 + yl1 + yc(elem(ielem)%local_e2n(ie,1)))/3._dp
          yavg1dy(:) = (dym1dy(:) + dyl1dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,1),:))/3._dp

        zavg1 = (zm1 + zl1 + zc(elem(ielem)%local_e2n(ie,1)))/3._dp
          zavg1dz(:) = (dzm1dz(:) + dzl1dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,1),:))/3._dp

        xavg2 = (xm2 + xl2 + xc(elem(ielem)%local_e2n(ie,2)))/3._dp
          xavg2dx(:) = (dxm2dx(:) + dxl2dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,2),:))/3._dp

        yavg2 = (ym2 + yl2 + yc(elem(ielem)%local_e2n(ie,2)))/3._dp
          yavg2dy(:) = (dym2dy(:) + dyl2dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,2),:))/3._dp

        zavg2 = (zm2 + zl2 + zc(elem(ielem)%local_e2n(ie,2)))/3._dp
          zavg2dz(:) = (dzm2dz(:) + dzl2dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,2),:))/3._dp

!       term1 = (xavg1*areax1 + yavg1*areay1 + zavg1*areaz1)/3._dp
         term1dx(:)=(yavg1*dareay1dx(:)+zavg1*dareaz1dx(:) + xavg1dx(:)*areax1)&
                   /3._dp
         term1dy(:)=(xavg1*dareax1dy(:)+zavg1*dareaz1dy(:) + yavg1dy(:)*areay1)&
                   /3._dp
         term1dz(:)=(xavg1*dareax1dz(:)+yavg1*dareay1dz(:) + zavg1dz(:)*areaz1)&
                   /3._dp

!       term2 = (xavg2*areax2 + yavg2*areay2 + zavg2*areaz2)/3._dp
         term2dx(:)=(yavg2*dareay2dx(:)+zavg2*dareaz2dx(:) + xavg2dx(:)*areax2)&
                   /3._dp
         term2dy(:)=(xavg2*dareax2dy(:)+zavg2*dareaz2dy(:) + yavg2dy(:)*areay2)&
                   /3._dp
         term2dz(:)=(xavg2*dareax2dz(:)+yavg2*dareay2dz(:) + zavg2dz(:)*areaz2)&
                   /3._dp

!       vol1 = vol1 + term1
!       vol2 = vol2 - term2

        vol1dx(:) = vol1dx(:) + term1dx(:)
        vol1dy(:) = vol1dy(:) + term1dy(:)
        vol1dz(:) = vol1dz(:) + term1dz(:)

        vol2dx(:) = vol2dx(:) - term2dx(:)
        vol2dy(:) = vol2dy(:) - term2dy(:)
        vol2dz(:) = vol2dz(:) - term2dz(:)

        if ( n1 <= nnodes0 ) then
          do k = 1, elem(ielem)%node_per_cell
            inode = elem(ielem)%c2n(k,n)
            do i = crow%ia(n1), crow%ia(n1+1)-1
              icol = crow%ja(i)
              if ( icol == inode ) ioff = i
            end do
            do j = 1, design%nfunctions
              getg%dvdx(1,ioff,j)=getg%dvdx(1,ioff,j) + vol1dx(k)*factor(nn1,j)
              getg%dvdx(2,ioff,j)=getg%dvdx(2,ioff,j) + vol1dy(k)*factor(nn1,j)
              getg%dvdx(3,ioff,j)=getg%dvdx(3,ioff,j) + vol1dz(k)*factor(nn1,j)
            end do
          end do
        endif

        if ( n2 <= nnodes0 ) then
          do k = 1, elem(ielem)%node_per_cell
            inode = elem(ielem)%c2n(k,n)
            do i = crow%ia(n2), crow%ia(n2+1)-1
              icol = crow%ja(i)
              if ( icol == inode ) ioff = i
            end do
            do j = 1, design%nfunctions
              getg%dvdx(1,ioff,j)=getg%dvdx(1,ioff,j) + vol2dx(k)*factor(nn2,j)
              getg%dvdx(2,ioff,j)=getg%dvdx(2,ioff,j) + vol2dy(k)*factor(nn2,j)
              getg%dvdx(3,ioff,j)=getg%dvdx(3,ioff,j) + vol2dz(k)*factor(nn2,j)
            end do
          end do
        endif

      end do edge_loop_cell

     end do cell_loop

    end do elem_loop1

! close off the boundaries

    call dvol_dgridb(nnodes0,nbound,nelem,elem,x,y,z,bc,soln,sadj,design,getg, &
                     q0,q1,q2,qn,turb0,turb1,turb2,turbn,qnm1,qnp1,qnp2,       &
                     turbnm1,turbnp1,turbnp2,crow,at_initial_state)

    deallocate(q0,q1,q2,turb0,turb1,turb2)
    deallocate(qn,qnm1,qnp1,qnp2,turbn,turbnm1,turbnp1,turbnp2)

  end subroutine dvol_dgrid


!================================= DVOL_DGRIDB ===============================80
!
! Linearizes the volumes wrt the grid on the boundaries
!
!=============================================================================80
  subroutine dvol_dgridb(nnodes0,nbound,nelem,elem,x,y,z,bc,soln,sadj,design,  &
                         getg,q0,q1,q2,qn,turb0,turb1,turb2,turbn,qnm1,qnp1,   &
                         qnp2,turbnm1,turbnp1,turbnp2,crow,at_initial_state)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_2, my_3, my_4
    use solution_types,  only : soln_type
    use solution_adj,    only : sadj_type
    use design_types,    only : design_type
    use solution_getg,   only : getg_type
    use bc_types,        only : bcgrid_type
    use element_types,   only : elem_type
    use element_defs,    only : max_node_per_cell
    use comprow_types,   only : crow_type
    use lmpi,            only : lmpi_die

    integer, intent(in) :: nnodes0, nbound, nelem

    real(dp), dimension(:),   intent(in) :: x, y, z
    real(dp), dimension(:,:), intent(in) :: q0,q1,q2,qn
    real(dp), dimension(:,:), intent(in) :: qnm1,qnp1,qnp2
    real(dp), dimension(:,:), intent(in) :: turb0,turb1
    real(dp), dimension(:,:), intent(in) :: turb2,turbn
    real(dp), dimension(:,:), intent(in) :: turbnm1,turbnp1
    real(dp), dimension(:,:), intent(in) :: turbnp2

    type(crow_type),                     intent(in)    :: crow
    type(sadj_type),                     intent(in)    :: sadj
    type(design_type),                   intent(in)    :: design
    type(soln_type),                     intent(in)    :: soln
    type(elem_type),   dimension(nelem), intent(in)    :: elem
    type(bcgrid_type), dimension(:),     intent(in)    :: bc
    type(getg_type),                     intent(inout) :: getg

    logical, intent(in) :: at_initial_state

    integer :: face, nn, node1, node2, node3, node4, ib, inode, k, j
    integer :: n1, n2, n3, n4, nn1, nn2, nn3, nn4, icell, ielem, icol
    integer :: cnode1, cnode2, cnode3, cnode4, ioff1, ioff2, ioff3, ioff4, i

    real(dp) :: areax, areay, areaz
    real(dp) :: x0, y0, z0
    real(dp) :: xr, yr, zr
    real(dp) :: xl, yl, zl
    real(dp) :: xorig1,yorig1,zorig1
    real(dp) :: xorig2,yorig2,zorig2
    real(dp) :: xorig3,yorig3,zorig3
    real(dp) :: xorig4,yorig4,zorig4
    real(dp) :: xorig,yorig,zorig
    real(dp) :: xc_ref,yc_ref,zc_ref
    real(dp) :: xorig1x1,xorig1x2,xorig1x3,xorig1x4
    real(dp) :: yorig1y1,yorig1y2,yorig1y3,yorig1y4
    real(dp) :: zorig1z1,zorig1z2,zorig1z3,zorig1z4
    real(dp) :: xorig2x1,xorig2x2,xorig2x3,xorig2x4
    real(dp) :: yorig2y1,yorig2y2,yorig2y3,yorig2y4
    real(dp) :: zorig2z1,zorig2z2,zorig2z3,zorig2z4
    real(dp) :: xorig3x1,xorig3x2,xorig3x3,xorig3x4
    real(dp) :: yorig3y1,yorig3y2,yorig3y3,yorig3y4
    real(dp) :: zorig3z1,zorig3z2,zorig3z3,zorig3z4
    real(dp) :: xorig4x1,xorig4x2,xorig4x3,xorig4x4
    real(dp) :: yorig4y1,yorig4y2,yorig4y3,yorig4y4
    real(dp) :: zorig4z1,zorig4z2,zorig4z3,zorig4z4
    real(dp) :: xorigx1,xorigx2,xorigx3,xorigx4
    real(dp) :: yorigy1,yorigy2,yorigy3,yorigy4
    real(dp) :: zorigz1,zorigz2,zorigz3,zorigz4
    real(dp) :: xc_refx1,xc_refx2,xc_refx3,xc_refx4
    real(dp) :: yc_refy1,yc_refy2,yc_refy3,yc_refy4
    real(dp) :: zc_refz1,zc_refz2,zc_refz3,zc_refz4
    real(dp) :: x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4
    real(dp) :: x0x1,x0x2,x0x3,x0x4,y0y1,y0y2,y0y3,y0y4,z0z1,z0z2,z0z3,z0z4
    real(dp) :: x1x1,x1x2,x1x3,x1x4,x2x1,x2x2,x2x3,x2x4,x3x1,x3x2,x3x3
    real(dp) :: y1y1,y1y2,y1y3,y1y4,y2y1,y2y2,y2y3,y2y4,y3y1,y3y2,y3y3
    real(dp) :: z1z1,z1z2,z1z3,z1z4,z2z1,z2z2,z2z3,z2z4,z3z1,z3z2,z3z3
    real(dp) :: x4x1,x4x2,x4x3,x4x4,y4y1,y4y2,y4y3,y4y4,z4z1,z4z2,z4z3,z4z4
    real(dp) :: xlx1,xlx2,xlx3,xlx4,yly1,yly2,yly3,yly4,zlz1,zlz2,zlz3,zlz4
    real(dp) :: xrx1,xrx2,xrx3,xrx4,yry1,yry2,yry3,yry4,zrz1,zrz2,zrz3,zrz4
    real(dp) :: areaxx1,areaxx2,areaxx3,areaxx4
    real(dp) :: areaxy1,areaxy2,areaxy3,areaxy4
    real(dp) :: areaxz1,areaxz2,areaxz3,areaxz4
    real(dp) :: areayx1,areayx2,areayx3,areayx4
    real(dp) :: areayy1,areayy2,areayy3,areayy4
    real(dp) :: areayz1,areayz2,areayz3,areayz4
    real(dp) :: areazx1,areazx2,areazx3,areazx4
    real(dp) :: areazy1,areazy2,areazy3,areazy4
    real(dp) :: areazz1,areazz2,areazz3,areazz4
    real(dp) :: volx1,volx2,volx3,volx4
    real(dp) :: voly1,voly2,voly3,voly4
    real(dp) :: volz1,volz2,volz3,volz4
    real(dp) :: xavg,yavg,zavg
    real(dp) :: xavgx1,xavgx2,xavgx3,xavgx4
    real(dp) :: yavgy1,yavgy2,yavgy3,yavgy4
    real(dp) :: zavgz1,zavgz2,zavgz3,zavgz4
    real(dp) :: termx1,termx2,termx3,termx4
    real(dp) :: termy1,termy2,termy3,termy4
    real(dp) :: termz1,termz2,termz3,termz4

    real(dp), dimension(4) :: xc, yc, zc
    real(dp), dimension(4) :: xcx1,xcx2,xcx3,ycy1,ycy2,ycy3,zcz1,zcz2,zcz3
    real(dp), dimension(4) :: xcx4,ycy4,zcz4
    real(dp), dimension(max_node_per_cell,design%nfunctions) :: factor

  continue

! avoid used uninitialized warnings

    nn1 = 0; nn2 = 0; nn3 = 0; nn4 = 0

    x1x1=my_0; x1x2=my_0; x1x3=my_0; x1x4=my_0
    x2x1=my_0; x2x2=my_0; x2x3=my_0; x2x4=my_0
    x3x1=my_0; x3x2=my_0; x3x3=my_0
    x4x1=my_0; x4x2=my_0; x4x3=my_0; x4x4=my_0
    y1y1=my_0; y1y2=my_0; y1y3=my_0; y1y4=my_0
    y2y1=my_0; y2y2=my_0; y2y3=my_0; y2y4=my_0
    y3y1=my_0; y3y2=my_0; y3y3=my_0
    y4y1=my_0; y4y2=my_0; y4y3=my_0; y4y4=my_0
    z1z1=my_0; z1z2=my_0; z1z3=my_0; z1z4=my_0
    z2z1=my_0; z2z2=my_0; z2z3=my_0; z2z4=my_0
    z3z1=my_0; z3z2=my_0; z3z3=my_0
    z4z1=my_0; z4z2=my_0; z4z3=my_0; z4z4=my_0

    xorig=my_0; xorigx1=my_0; xorigx2=my_0; xorigx3=my_0; xorigx4=my_0
    yorig=my_0; yorigy1=my_0; yorigy2=my_0; yorigy3=my_0; yorigy4=my_0
    zorig=my_0; zorigz1=my_0; zorigz2=my_0; zorigz3=my_0; zorigz4=my_0

    xc_ref=my_0; xc_refx1=my_0; xc_refx2=my_0; xc_refx3=my_0; xc_refx4=my_0
    yc_ref=my_0; yc_refy1=my_0; yc_refy2=my_0; yc_refy3=my_0; yc_refy4=my_0
    zc_ref=my_0; zc_refz1=my_0; zc_refz2=my_0; zc_refz3=my_0; zc_refz4=my_0

    bound_loop : do ib = 1, nbound

      tria_face_loop : do face = 1, bc(ib)%nbfacet

        n1 = bc(ib)%f2ntb(face,1)
        n2 = bc(ib)%f2ntb(face,2)
        n3 = bc(ib)%f2ntb(face,3)
        icell = bc(ib)%f2ntb(face,4)         ! global cell number
        ielem = bc(ib)%f2ntb(face,5)         ! cell type indicator

        node1 = bc(ib)%ibnode(n1)
        node2 = bc(ib)%ibnode(n2)
        node3 = bc(ib)%ibnode(n3)

! first compute the dQ*rlam factor at each node of the cell

        call get_factor(design,at_initial_state,soln,elem,nnodes0,sadj,q0,q1,  &
                        q2,qn,qnm1,qnp1,qnp2,turb0,turb1,turb2,turbn,turbnm1,  &
                        turbnp1,turbnp2,icell,ielem,factor)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3

        xorig1 = x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0

        yorig1 = y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0

        zorig1 = z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0

        xorig2 = x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0

        yorig2 = y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0

        zorig2 = z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0

        xorig3 = x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1

        yorig3 = y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1

        zorig3 = z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1

        xc(1)=((x(node2)-xorig1) + (x(node3)-xorig1))/my_3
          xcx1(1)=(-xorig1x1-xorig1x1)/my_3
          xcx2(1)=my_1/my_3
          xcx3(1)=my_1/my_3

        yc(1)=((y(node2)-yorig1) + (y(node3)-yorig1))/my_3
          ycy1(1)=(-yorig1y1-yorig1y1)/my_3
          ycy2(1)=my_1/my_3
          ycy3(1)=my_1/my_3

        zc(1)=((z(node2)-zorig1) + (z(node3)-zorig1))/my_3
          zcz1(1)=(-zorig1z1-zorig1z1)/my_3
          zcz2(1)=my_1/my_3
          zcz3(1)=my_1/my_3

        xc(2)=((x(node1)-xorig2) + (x(node3)-xorig2))/my_3
          xcx1(2)=my_1/my_3
          xcx2(2)=(-xorig2x2-xorig2x2)/my_3
          xcx3(2)=my_1/my_3

        yc(2)=((y(node1)-yorig2) + (y(node3)-yorig2))/my_3
          ycy1(2)=my_1/my_3
          ycy2(2)=(-yorig2y2-yorig2y2)/my_3
          ycy3(2)=my_1/my_3

        zc(2)=((z(node1)-zorig2) + (z(node3)-zorig2))/my_3
          zcz1(2)=my_1/my_3
          zcz2(2)=(-zorig2z2-zorig2z2)/my_3
          zcz3(2)=my_1/my_3

        xc(3)=((x(node1)-xorig3) + (x(node2)-xorig3))/my_3
          xcx1(3)=my_1/my_3
          xcx2(3)=my_1/my_3
          xcx3(3)=(-xorig3x3-xorig3x3)/my_3

        yc(3)=((y(node1)-yorig3) + (y(node2)-yorig3))/my_3
          ycy1(3)=my_1/my_3
          ycy2(3)=my_1/my_3
          ycy3(3)=(-yorig3y3-yorig3y3)/my_3

        zc(3)=((z(node1)-zorig3) + (z(node2)-zorig3))/my_3
          zcz1(3)=my_1/my_3
          zcz2(3)=my_1/my_3
          zcz3(3)=(-zorig3z3-zorig3z3)/my_3

        tria_node_loop : do nn = 1, 3

          volx1 = 0.0_dp
          volx2 = 0.0_dp
          volx3 = 0.0_dp

          voly1 = 0.0_dp
          voly2 = 0.0_dp
          voly3 = 0.0_dp

          volz1 = 0.0_dp
          volz2 = 0.0_dp
          volz3 = 0.0_dp

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
            case(3)
              nn1 = n3
              nn2 = n1
              nn3 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
            case default
          end select

          node1 = bc(ib)%ibnode(nn1)
          node2 = bc(ib)%ibnode(nn2)
          node3 = bc(ib)%ibnode(nn3)

          x1 = x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
            endif

          x2 = x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
            endif

          x3 = x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
            endif

          y1 = y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
            endif

          y2 = y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
            endif

          y3 = y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
            endif

          z1 = z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
            endif

          z2 = z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
            endif

          z3 = z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3

          xl = ((x1-xorig) + (x3-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x3x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x3x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x3x3-xorigx3))/my_2

          yl = ((y1-yorig) + (y3-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y3y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y3y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y3y3-yorigy3))/my_2

          zl = ((z1-zorig) + (z3-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z3z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z3z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z3z3-zorigz3))/my_2

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

        xavg = (x0 + xr + xc_ref)/3._dp
          xavgx1 = (x0x1 + xrx1 + xc_refx1)/3._dp
          xavgx2 = (x0x2 + xrx2 + xc_refx2)/3._dp
          xavgx3 = (x0x3 + xrx3 + xc_refx3)/3._dp

        yavg = (y0 + yr + yc_ref)/3._dp
          yavgy1 = (y0y1 + yry1 + yc_refy1)/3._dp
          yavgy2 = (y0y2 + yry2 + yc_refy2)/3._dp
          yavgy3 = (y0y3 + yry3 + yc_refy3)/3._dp

        zavg = (z0 + zr + zc_ref)/3._dp
          zavgz1 = (z0z1 + zrz1 + zc_refz1)/3._dp
          zavgz2 = (z0z2 + zrz2 + zc_refz2)/3._dp
          zavgz3 = (z0z3 + zrz3 + zc_refz3)/3._dp

!       term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
          termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
          termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
          termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp

          termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
          termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
          termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp

          termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
          termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
          termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp

!       vol(node1) = vol(node1) - term

          volx1 = volx1 - termx1
          volx2 = volx2 - termx2
          volx3 = volx3 - termx3

          voly1 = voly1 - termy1
          voly2 = voly2 - termy2
          voly3 = voly3 - termy3

          volz1 = volz1 - termz1
          volz2 = volz2 - termz2
          volz3 = volz3 - termz3

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

          xavg = (x0 + xc_ref + xl)/3._dp
            xavgx1 = (x0x1 + xlx1 + xc_refx1)/3._dp
            xavgx2 = (x0x2 + xlx2 + xc_refx2)/3._dp
            xavgx3 = (x0x3 + xlx3 + xc_refx3)/3._dp

          yavg = (y0 + yc_ref + yl)/3._dp
            yavgy1 = (y0y1 + yly1 + yc_refy1)/3._dp
            yavgy2 = (y0y2 + yly2 + yc_refy2)/3._dp
            yavgy3 = (y0y3 + yly3 + yc_refy3)/3._dp

          zavg = (z0 + zc_ref + zl)/3._dp
            zavgz1 = (z0z1 + zlz1 + zc_refz1)/3._dp
            zavgz2 = (z0z2 + zlz2 + zc_refz2)/3._dp
            zavgz3 = (z0z3 + zlz3 + zc_refz3)/3._dp

!         term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
            termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
            termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
            termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp

            termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
            termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
            termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp

            termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
            termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
            termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp

!         vol(node1) = vol(node1) - term

            volx1 = volx1 - termx1
            volx2 = volx2 - termx2
            volx3 = volx3 - termx3

            voly1 = voly1 - termy1
            voly2 = voly2 - termy2
            voly3 = voly3 - termy3

            volz1 = volz1 - termz1
            volz2 = volz2 - termz2
            volz3 = volz3 - termz3

          if ( node1 <= nnodes0 ) then

            ioff1 = 0
            ioff2 = 0
            ioff3 = 0
            do i = crow%ia(node1), crow%ia(node1+1)-1
              icol = crow%ja(i)
              if ( icol == cnode1 ) ioff1 = i
              if ( icol == cnode2 ) ioff2 = i
              if ( icol == cnode3 ) ioff3 = i
            end do

            if ( ioff1 == 0 .or. ioff2 == 0 .or. ioff3 == 0 ) then
              write(*,*) 'Trouble locating ioff in dvol_dgridb.'
              call lmpi_die
              stop
            endif

            inode = 0
            do k = 1, elem(ielem)%node_per_cell
              if ( elem(ielem)%c2n(k,icell) == node1 ) inode = k
            end do

            if ( inode == 0 ) then
              write(*,*) 'Trouble locating inode in dvol_dgridb.'
              call lmpi_die
              stop
            endif

            do j = 1, design%nfunctions
              getg%dvdx(1,ioff1,j)=getg%dvdx(1,ioff1,j) + volx1*factor(inode,j)
              getg%dvdx(2,ioff1,j)=getg%dvdx(2,ioff1,j) + voly1*factor(inode,j)
              getg%dvdx(3,ioff1,j)=getg%dvdx(3,ioff1,j) + volz1*factor(inode,j)

              getg%dvdx(1,ioff2,j)=getg%dvdx(1,ioff2,j) + volx2*factor(inode,j)
              getg%dvdx(2,ioff2,j)=getg%dvdx(2,ioff2,j) + voly2*factor(inode,j)
              getg%dvdx(3,ioff2,j)=getg%dvdx(3,ioff2,j) + volz2*factor(inode,j)

              getg%dvdx(1,ioff3,j)=getg%dvdx(1,ioff3,j) + volx3*factor(inode,j)
              getg%dvdx(2,ioff3,j)=getg%dvdx(2,ioff3,j) + voly3*factor(inode,j)
              getg%dvdx(3,ioff3,j)=getg%dvdx(3,ioff3,j) + volz3*factor(inode,j)
            end do

          endif

        end do tria_node_loop

      end do tria_face_loop

      quad_face_loop : do face = 1, bc(ib)%nbfaceq

        n1 = bc(ib)%f2nqb(face,1)
        n2 = bc(ib)%f2nqb(face,2)
        n3 = bc(ib)%f2nqb(face,3)
        n4 = bc(ib)%f2nqb(face,4)
        icell = bc(ib)%f2nqb(face,5)         ! global cell number
        ielem = bc(ib)%f2nqb(face,6)         ! cell type indicator

        node1 = bc(ib)%ibnode(n1)
        node2 = bc(ib)%ibnode(n2)
        node3 = bc(ib)%ibnode(n3)
        node4 = bc(ib)%ibnode(n4)

! first compute the dQ*rlam factor at each node of the cell

        call get_factor(design,at_initial_state,soln,elem,nnodes0,sadj,q0,q1,  &
                        q2,qn,qnm1,qnp1,qnp2,turb0,turb1,turb2,turbn,turbnm1,  &
                        turbnp1,turbnp2,icell,ielem,factor)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3
        cnode4 = node4

        xorig1 = x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0
          xorig1x4 = my_0

        yorig1 = y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0
          yorig1y4 = my_0

        zorig1 = z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0
          zorig1z4 = my_0

        xorig2 = x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0
          xorig2x4 = my_0

        yorig2 = y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0
          yorig2y4 = my_0

        zorig2 = z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0
          zorig2z4 = my_0

        xorig3 = x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1
          xorig3x4 = my_0

        yorig3 = y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1
          yorig3y4 = my_0

        zorig3 = z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1
          zorig3z4 = my_0

        xorig4 = x(node4)
          xorig4x1 = my_0
          xorig4x2 = my_0
          xorig4x3 = my_0
          xorig4x4 = my_1

        yorig4 = y(node4)
          yorig4y1 = my_0
          yorig4y2 = my_0
          yorig4y3 = my_0
          yorig4y4 = my_1

        zorig4 = z(node4)
          zorig4z1 = my_0
          zorig4z2 = my_0
          zorig4z3 = my_0
          zorig4z4 = my_1

        xc(1)=((x(node2)-xorig1) + (x(node3)-xorig1) + (x(node4)-xorig1))/my_4
          xcx1(1)=(-xorig1x1-xorig1x1-xorig1x1)/my_4
          xcx2(1)=my_1/my_4
          xcx3(1)=my_1/my_4
          xcx4(1)=my_1/my_4

        yc(1)=((y(node2)-yorig1) + (y(node3)-yorig1) + (y(node4)-yorig1))/my_4
          ycy1(1)=(-yorig1y1-yorig1y1-yorig1y1)/my_4
          ycy2(1)=my_1/my_4
          ycy3(1)=my_1/my_4
          ycy4(1)=my_1/my_4

        zc(1)=((z(node2)-zorig1) + (z(node3)-zorig1) + (z(node4)-zorig1))/my_4
          zcz1(1)=(-zorig1z1-zorig1z1-zorig1z1)/my_4
          zcz2(1)=my_1/my_4
          zcz3(1)=my_1/my_4
          zcz4(1)=my_1/my_4

        xc(2)=((x(node1)-xorig2) + (x(node3)-xorig2) + (x(node4)-xorig2))/my_4
          xcx1(2)=my_1/my_4
          xcx2(2)=(-xorig2x2-xorig2x2-xorig2x2)/my_4
          xcx3(2)=my_1/my_4
          xcx4(2)=my_1/my_4

        yc(2)=((y(node1)-yorig2) + (y(node3)-yorig2) + (y(node4)-yorig2))/my_4
          ycy1(2)=my_1/my_4
          ycy2(2)=(-yorig2y2-yorig2y2-yorig2y2)/my_4
          ycy3(2)=my_1/my_4
          ycy4(2)=my_1/my_4

        zc(2)=((z(node1)-zorig2) + (z(node3)-zorig2) + (z(node4)-zorig2))/my_4
          zcz1(2)=my_1/my_4
          zcz2(2)=(-zorig2z2-zorig2z2-zorig2z2)/my_4
          zcz3(2)=my_1/my_4
          zcz4(2)=my_1/my_4

        xc(3)=((x(node1)-xorig3) + (x(node2)-xorig3) + (x(node4)-xorig3))/my_4
          xcx1(3)=my_1/my_4
          xcx2(3)=my_1/my_4
          xcx3(3)=(-xorig3x3-xorig3x3-xorig3x3)/my_4
          xcx4(3)=my_1/my_4

        yc(3)=((y(node1)-yorig3) + (y(node2)-yorig3) + (y(node4)-yorig3))/my_4
          ycy1(3)=my_1/my_4
          ycy2(3)=my_1/my_4
          ycy3(3)=(-yorig3y3-yorig3y3-yorig3y3)/my_4
          ycy4(3)=my_1/my_4

        zc(3)=((z(node1)-zorig3) + (z(node2)-zorig3) + (z(node4)-zorig3))/my_4
          zcz1(3)=my_1/my_4
          zcz2(3)=my_1/my_4
          zcz3(3)=(-zorig3z3-zorig3z3-zorig3z3)/my_4
          zcz4(3)=my_1/my_4



        xc(4)=((x(node1)-xorig4) + (x(node2)-xorig4) + (x(node3)-xorig4))/my_4
          xcx1(4)=my_1/my_4
          xcx2(4)=my_1/my_4
          xcx3(4)=my_1/my_4
          xcx4(4)=(-xorig4x4-xorig4x4-xorig4x4)/my_4

        yc(4)=((y(node1)-yorig4) + (y(node2)-yorig4) + (y(node3)-yorig4))/my_4
          ycy1(4)=my_1/my_4
          ycy2(4)=my_1/my_4
          ycy3(4)=my_1/my_4
          ycy4(4)=(-yorig4y4-yorig4y4-yorig4y4)/my_4

        zc(4)=((z(node1)-zorig4) + (z(node2)-zorig4) + (z(node3)-zorig4))/my_4
          zcz1(4)=my_1/my_4
          zcz2(4)=my_1/my_4
          zcz3(4)=my_1/my_4
          zcz4(4)=(-zorig4z4-zorig4z4-zorig4z4)/my_4

        quad_node_loop : do nn = 1, 4

          volx1 = 0.0_dp
          volx2 = 0.0_dp
          volx3 = 0.0_dp
          volx4 = 0.0_dp

          voly1 = 0.0_dp
          voly2 = 0.0_dp
          voly3 = 0.0_dp
          voly4 = 0.0_dp

          volz1 = 0.0_dp
          volz2 = 0.0_dp
          volz3 = 0.0_dp
          volz4 = 0.0_dp

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              nn4 = n4
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
                xorigx4 = xorig1x4
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
                yorigy4 = yorig1y4
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
                zorigz4 = zorig1z4
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
                xc_refx4 = xcx4(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
                yc_refy4 = ycy4(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
                zc_refz4 = zcz4(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n4
              nn4 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
                xorigx4 = xorig2x4
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
                yorigy4 = yorig2y4
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
                zorigz4 = zorig2z4
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
                xc_refx4 = xcx4(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
                yc_refy4 = ycy4(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
                zc_refz4 = zcz4(2)
            case(3)
              nn1 = n3
              nn2 = n4
              nn3 = n1
              nn4 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
                xorigx4 = xorig3x4
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
                yorigy4 = yorig3y4
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
                zorigz4 = zorig3z4
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
                xc_refx4 = xcx4(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
                yc_refy4 = ycy4(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
                zc_refz4 = zcz4(3)
            case(4)
              nn1 = n4
              nn2 = n1
              nn3 = n2
              nn4 = n3
              xorig = xorig4
                xorigx1 = xorig4x1
                xorigx2 = xorig4x2
                xorigx3 = xorig4x3
                xorigx4 = xorig4x4
              yorig = yorig4
                yorigy1 = yorig4y1
                yorigy2 = yorig4y2
                yorigy3 = yorig4y3
                yorigy4 = yorig4y4
              zorig = zorig4
                zorigz1 = zorig4z1
                zorigz2 = zorig4z2
                zorigz3 = zorig4z3
                zorigz4 = zorig4z4
              xc_ref = xc(4)
                xc_refx1 = xcx1(4)
                xc_refx2 = xcx2(4)
                xc_refx3 = xcx3(4)
                xc_refx4 = xcx4(4)
              yc_ref = yc(4)
                yc_refy1 = ycy1(4)
                yc_refy2 = ycy2(4)
                yc_refy3 = ycy3(4)
                yc_refy4 = ycy4(4)
              zc_ref = zc(4)
                zc_refz1 = zcz1(4)
                zc_refz2 = zcz2(4)
                zc_refz3 = zcz3(4)
                zc_refz4 = zcz4(4)
            case default
          end select

          node1 = bc(ib)%ibnode(nn1)
          node2 = bc(ib)%ibnode(nn2)
          node3 = bc(ib)%ibnode(nn3)
          node4 = bc(ib)%ibnode(nn4)

          x1 = x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
              x1x4 = my_0
            else if ( node1 == cnode4 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_1
            endif

          x2 = x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
              x2x4 = my_0
            else if ( node2 == cnode4 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_1
            endif

          x3 = x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
!             x3x4 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
!             x3x4 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
!             x3x4 = my_0
            else if ( node3 == cnode4 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_0
!             x3x4 = my_1
            endif

          x4 = x(node4)
            if ( node4 == cnode1 ) then
              x4x1 = my_1
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode2 ) then
              x4x1 = my_0
              x4x2 = my_1
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode3 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_1
              x4x4 = my_0
            else if ( node4 == cnode4 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_1
            endif

          y1 = y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
              y1y4 = my_0
            else if ( node1 == cnode4 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_1
            endif

          y2 = y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
              y2y4 = my_0
            else if ( node2 == cnode4 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_1
            endif

          y3 = y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
!             y3y4 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
!             y3y4 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
!             y3y4 = my_0
            else if ( node3 == cnode4 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_0
!             y3y4 = my_1
            endif

          y4 = y(node4)
            if ( node4 == cnode1 ) then
              y4y1 = my_1
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode2 ) then
              y4y1 = my_0
              y4y2 = my_1
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode3 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_1
              y4y4 = my_0
            else if ( node4 == cnode4 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_1
            endif

          z1 = z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
              z1z4 = my_0
            else if ( node1 == cnode4 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_1
            endif

          z2 = z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
              z2z4 = my_0
            else if ( node2 == cnode4 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_1
            endif

          z3 = z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
!             z3z4 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
!             z3z4 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
!             z3z4 = my_0
            else if ( node3 == cnode4 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_0
!             z3z4 = my_1
            endif

          z4 = z(node4)
            if ( node4 == cnode1 ) then
              z4z1 = my_1
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode2 ) then
              z4z1 = my_0
              z4z2 = my_1
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode3 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_1
              z4z4 = my_0
            else if ( node4 == cnode4 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
            x0x4 = x1x4 - xorigx4
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
            y0y4 = y1y4 - yorigy4
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3
            z0z4 = z1z4 - zorigz4

          xl = ((x1-xorig) + (x4-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x4x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x4x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x4x3-xorigx3))/my_2
            xlx4 = ((x1x4-xorigx4) + (x4x4-xorigx4))/my_2

          yl = ((y1-yorig) + (y4-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y4y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y4y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y4y3-yorigy3))/my_2
            yly4 = ((y1y4-yorigy4) + (y4y4-yorigy4))/my_2

          zl = ((z1-zorig) + (z4-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z4z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z4z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z4z3-zorigz3))/my_2
            zlz4 = ((z1z4-zorigz4) + (z4z4-zorigz4))/my_2

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2
            xrx4 = ((x1x4-xorigx4) + (x2x4-xorigx4))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2
            yry4 = ((y1y4-yorigy4) + (y2y4-yorigy4))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2
            zrz4 = ((z1z4-zorigz4) + (z2z4-zorigz4))/my_2

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))
            areaxy4 = my_half*((yry4-y0y4)*(zc_ref-z0)- (zr-z0)*(yc_refy4-y0y4))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))
            areaxz4 = my_half*((yr-y0)*(zc_refz4-z0z4)- (zrz4-z0z4)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))
            areayx4 = my_half*((zr-z0)*(xc_refx4-x0x4)- (xrx4-x0x4)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))
            areayz4 = my_half*((zrz4-z0z4)*(xc_ref-x0)- (xr-x0)*(zc_refz4-z0z4))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))
            areazx4 = my_half*((xrx4-x0x4)*(yc_ref-y0)- (yr-y0)*(xc_refx4-x0x4))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))
            areazy4 = my_half*((xr-x0)*(yc_refy4-y0y4)- (yry4-y0y4)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

        xavg = (x0 + xr + xc_ref)/3._dp
          xavgx1 = (x0x1 + xrx1 + xc_refx1)/3._dp
          xavgx2 = (x0x2 + xrx2 + xc_refx2)/3._dp
          xavgx3 = (x0x3 + xrx3 + xc_refx3)/3._dp
          xavgx4 = (x0x4 + xrx4 + xc_refx4)/3._dp

        yavg = (y0 + yr + yc_ref)/3._dp
          yavgy1 = (y0y1 + yry1 + yc_refy1)/3._dp
          yavgy2 = (y0y2 + yry2 + yc_refy2)/3._dp
          yavgy3 = (y0y3 + yry3 + yc_refy3)/3._dp
          yavgy4 = (y0y4 + yry4 + yc_refy4)/3._dp

        zavg = (z0 + zr + zc_ref)/3._dp
          zavgz1 = (z0z1 + zrz1 + zc_refz1)/3._dp
          zavgz2 = (z0z2 + zrz2 + zc_refz2)/3._dp
          zavgz3 = (z0z3 + zrz3 + zc_refz3)/3._dp
          zavgz4 = (z0z4 + zrz4 + zc_refz4)/3._dp

!       term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
          termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
          termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
          termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp
          termx4 = (xavg*areaxx4+yavg*areayx4+zavg*areazx4+xavgx4*areax)/3._dp

          termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
          termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
          termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp
          termy4 = (xavg*areaxy4+yavg*areayy4+zavg*areazy4+yavgy4*areay)/3._dp

          termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
          termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
          termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp
          termz4 = (xavg*areaxz4+yavg*areayz4+zavg*areazz4+zavgz4*areaz)/3._dp

!       vol(node1) = vol(node1) - term

          volx1 = volx1 - termx1
          volx2 = volx2 - termx2
          volx3 = volx3 - termx3
          volx4 = volx4 - termx4

          voly1 = voly1 - termy1
          voly2 = voly2 - termy2
          voly3 = voly3 - termy3
          voly4 = voly4 - termy4

          volz1 = volz1 - termz1
          volz2 = volz2 - termz2
          volz3 = volz3 - termz3
          volz4 = volz4 - termz4

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))
            areaxy4 = my_half*((yc_refy4-y0y4)*(zl-z0)- (zc_ref-z0)*(yly4-y0y4))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))
            areaxz4 = my_half*((yc_ref-y0)*(zlz4-z0z4)- (zc_refz4-z0z4)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))
            areayx4 = my_half*((zc_ref-z0)*(xlx4-x0x4)- (xc_refx4-x0x4)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))
            areayz4 = my_half*((zc_refz4-z0z4)*(xl-x0)- (xc_ref-x0)*(zlz4-z0z4))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))
            areazx4 = my_half*((xc_refx4-x0x4)*(yl-y0)- (yc_ref-y0)*(xlx4-x0x4))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))
            areazy4 = my_half*((xc_ref-x0)*(yly4-y0y4)- (yc_refy4-y0y4)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

          xavg = (x0 + xc_ref + xl)/3._dp
            xavgx1 = (x0x1 + xlx1 + xc_refx1)/3._dp
            xavgx2 = (x0x2 + xlx2 + xc_refx2)/3._dp
            xavgx3 = (x0x3 + xlx3 + xc_refx3)/3._dp
            xavgx4 = (x0x4 + xlx4 + xc_refx4)/3._dp

          yavg = (y0 + yc_ref + yl)/3._dp
            yavgy1 = (y0y1 + yly1 + yc_refy1)/3._dp
            yavgy2 = (y0y2 + yly2 + yc_refy2)/3._dp
            yavgy3 = (y0y3 + yly3 + yc_refy3)/3._dp
            yavgy4 = (y0y4 + yly4 + yc_refy4)/3._dp

          zavg = (z0 + zc_ref + zl)/3._dp
            zavgz1 = (z0z1 + zlz1 + zc_refz1)/3._dp
            zavgz2 = (z0z2 + zlz2 + zc_refz2)/3._dp
            zavgz3 = (z0z3 + zlz3 + zc_refz3)/3._dp
            zavgz4 = (z0z4 + zlz4 + zc_refz4)/3._dp

!         term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
            termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
            termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
            termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp
            termx4 = (xavg*areaxx4+yavg*areayx4+zavg*areazx4+xavgx4*areax)/3._dp

            termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
            termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
            termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp
            termy4 = (xavg*areaxy4+yavg*areayy4+zavg*areazy4+yavgy4*areay)/3._dp

            termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
            termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
            termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp
            termz4 = (xavg*areaxz4+yavg*areayz4+zavg*areazz4+zavgz4*areaz)/3._dp

!         vol(node1) = vol(node1) - term

            volx1 = volx1 - termx1
            volx2 = volx2 - termx2
            volx3 = volx3 - termx3
            volx4 = volx4 - termx4

            voly1 = voly1 - termy1
            voly2 = voly2 - termy2
            voly3 = voly3 - termy3
            voly4 = voly4 - termy4

            volz1 = volz1 - termz1
            volz2 = volz2 - termz2
            volz3 = volz3 - termz3
            volz4 = volz4 - termz4

          if ( node1 <= nnodes0 ) then

            ioff1 = 0
            ioff2 = 0
            ioff3 = 0
            ioff4 = 0
            do i = crow%ia(node1), crow%ia(node1+1)-1
              icol = crow%ja(i)
              if ( icol == cnode1 ) ioff1 = i
              if ( icol == cnode2 ) ioff2 = i
              if ( icol == cnode3 ) ioff3 = i
              if ( icol == cnode4 ) ioff4 = i
            end do

            if( ioff1 == 0 .or. ioff2 == 0 .or. ioff3 == 0 .or. ioff4 == 0 )then
              write(*,*) 'Trouble locating ioff in dvol_dgridb.'
              call lmpi_die
              stop
            endif

            inode = 0
            do k = 1, elem(ielem)%node_per_cell
              if ( elem(ielem)%c2n(k,icell) == node1 ) inode = k
            end do

            if ( inode == 0 ) then
              write(*,*) 'Trouble locating inode in dvol_dgridb.'
              call lmpi_die
              stop
            endif

            do j = 1, design%nfunctions
              getg%dvdx(1,ioff1,j)=getg%dvdx(1,ioff1,j) + volx1*factor(inode,j)
              getg%dvdx(2,ioff1,j)=getg%dvdx(2,ioff1,j) + voly1*factor(inode,j)
              getg%dvdx(3,ioff1,j)=getg%dvdx(3,ioff1,j) + volz1*factor(inode,j)

              getg%dvdx(1,ioff2,j)=getg%dvdx(1,ioff2,j) + volx2*factor(inode,j)
              getg%dvdx(2,ioff2,j)=getg%dvdx(2,ioff2,j) + voly2*factor(inode,j)
              getg%dvdx(3,ioff2,j)=getg%dvdx(3,ioff2,j) + volz2*factor(inode,j)

              getg%dvdx(1,ioff3,j)=getg%dvdx(1,ioff3,j) + volx3*factor(inode,j)
              getg%dvdx(2,ioff3,j)=getg%dvdx(2,ioff3,j) + voly3*factor(inode,j)
              getg%dvdx(3,ioff3,j)=getg%dvdx(3,ioff3,j) + volz3*factor(inode,j)

              getg%dvdx(1,ioff4,j)=getg%dvdx(1,ioff4,j) + volx4*factor(inode,j)
              getg%dvdx(2,ioff4,j)=getg%dvdx(2,ioff4,j) + voly4*factor(inode,j)
              getg%dvdx(3,ioff4,j)=getg%dvdx(3,ioff4,j) + volz4*factor(inode,j)
            end do

          endif

        end do quad_node_loop

      end do quad_face_loop

    end do bound_loop

  end subroutine dvol_dgridb


!================================ GET_FACTOR =================================80
!
!  Get the factor (Qn - Qn-1)/dt * lambda-f
!
!=============================================================================80
  subroutine get_factor(design,at_initial_state,soln,elem,nnodes0,sadj,q0,q1,  &
                        q2,qn,qnm1,qnp1,qnp2,turb0,turb1,turb2,turbn,turbnm1,  &
                        turbnp1,turbnp2,n,ielem,factor)

    use kinddefs,             only : dp
    use design_types,         only : design_type
    use info_depr,            only : ivisc, physical_timestep
    use nml_nonlinear_solves, only : ibdf2opt, itime, dt
    use nml_global,           only : moving_grid
    use solution_types,       only : soln_type, incompressible
    use element_types,        only : elem_type
    use nml_overset_data,     only : overset_flag
    use designs,              only : coltag_nonoverset, iblankn1, iblankn2
    use fun3d_constants,      only : my_half, my_1, my_1p5, my_2, my_3, my_6,  &
                                     my_11
    use solution_adj,         only : sadj_type
    use lmpi,                 only : lmpi_die

    integer, intent(in) :: nnodes0, n, ielem

    real(dp), dimension(:,:), intent(in)  :: q0, q1, q2, qn, qnm1, qnp1, qnp2
    real(dp), dimension(:,:), intent(in)  :: turb0, turb1, turb2, turbn, turbnm1
    real(dp), dimension(:,:), intent(in)  :: turbnp1, turbnp2
    real(dp), dimension(:,:), intent(out) :: factor

    logical, intent(in) :: at_initial_state

    type(design_type),               intent(in) :: design
    type(soln_type),                 intent(in) :: soln
    type(sadj_type),                 intent(in) :: sadj
    type(elem_type),   dimension(:), intent(in) :: elem

    integer :: ifcn, i, k, nd, ind

    real(dp) :: a, c, d

  continue

! compute the dQ*rlam factor at each node of the cell

    factor(:,:) = 0.0_dp

    function_loop : do ifcn = 1, design%nfunctions

      build_factor : if ( at_initial_state ) then

        backplanes1 : if ( itime == 2 ) then          ! BDF2

          do i = 1, soln%n_tot
            if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
            node_loop2a : do k = 1, elem(ielem)%node_per_cell
              nd = elem(ielem)%c2n(k,n)
              if ( nd <= nnodes0 ) then
               if ( overset_flag .and. moving_grid ) then
                if ( iblankn1(nd) == 1 ) then
                 factor(k,ifcn) = factor(k,ifcn)                             &
                + my_half*(q0(i,nd)-q1(i,nd))*sadj%rlamatn1(i,nd,ifcn)       &
                                                   *coltag_nonoverset(i,nd)/dt
                endif
               else
                factor(k,ifcn) = factor(k,ifcn)                              &
                + my_half*(q0(i,nd)-q1(i,nd))*sadj%rlamatn1(i,nd,ifcn)       &
                                                         *sadj%coltag(i,nd)/dt
               endif
              endif
            end do node_loop2a
          end do
          if ( ivisc >= 6 ) then
            do i = 1, soln%n_turb
              ind = soln%n_tot + i
              node_loop2b : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                  if ( iblankn1(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
           + my_half*(turb0(i,nd)-turb1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)    &
                                                 *coltag_nonoverset(ind,nd)/dt
                  endif
                 else
                  factor(k,ifcn) = factor(k,ifcn)                            &
           + my_half*(turb0(i,nd)-turb1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)    &
                                                       *sadj%coltag(ind,nd)/dt
                 endif
                endif
              end do node_loop2b
            end do
          endif

        else if ( itime == 3 ) then          ! BDF3/2opt

          if ( ibdf2opt == 0 ) then          ! BDF3
            d = -my_1/my_3
          else if ( ibdf2opt == 1 ) then     ! BDF2opt
            d = -0.48_dp*my_2/my_6
          else
            d = 0.0_dp ! avoid uninit warning
            write(*,*) 'Unknown value for ibdf2opt: ', ibdf2opt
            call lmpi_die
          endif

          do i = 1, soln%n_tot
            if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
            node_loop4a : do k = 1, elem(ielem)%node_per_cell
              nd = elem(ielem)%c2n(k,n)
              if ( nd <= nnodes0 ) then
               if ( overset_flag .and. moving_grid ) then
                if ( iblankn1(nd) == 1 ) then
                 factor(k,ifcn) = factor(k,ifcn)                             &
                      + my_half*(q0(i,nd)-q1(i,nd))*sadj%rlamatn1(i,nd,ifcn) &
                                                   *coltag_nonoverset(i,nd)/dt
                endif
                if ( iblankn2(nd) == 1 ) then
                 factor(k,ifcn) = factor(k,ifcn)                             &
                          + d*(q0(i,nd)-q2(i,nd))*sadj%rlamatn2(i,nd,ifcn)   &
                                                   *coltag_nonoverset(i,nd)/dt
                endif
               else
                factor(k,ifcn) = factor(k,ifcn)                              &
                      + my_half*(q0(i,nd)-q1(i,nd))*sadj%rlamatn1(i,nd,ifcn) &
                                                       *sadj%coltag(i,nd)/dt &
                          + d*(q0(i,nd)-q2(i,nd))*sadj%rlamatn2(i,nd,ifcn)   &
                                                       *sadj%coltag(i,nd)/dt
               endif
              endif
            end do node_loop4a
          end do
          if ( ivisc >= 6 ) then
            do i = 1, soln%n_turb
              ind = soln%n_tot + i
              node_loop4b : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                  if ( iblankn1(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
         + my_half*(turb0(i,nd)-turb1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)      &
                                                 *coltag_nonoverset(ind,nd)/dt
                  endif
                  if ( iblankn2(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                     + d*(turb0(i,nd)-turb2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                  endif
                 else
                  factor(k,ifcn) = factor(k,ifcn)                            &
         + my_half*(turb0(i,nd)-turb1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)      &
                                                    *sadj%coltag(ind,nd)/dt  &
                     + d*(turb0(i,nd)-turb2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                    *sadj%coltag(ind,nd)/dt
                 endif
                endif
              end do node_loop4b
            end do
          endif

        endif backplanes1

      else build_factor

        backplanes2 : if ( itime == 1 ) then   ! BDF1

          do i = 1, soln%n_tot
            if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
            node_loop1a : do k = 1, elem(ielem)%node_per_cell
              nd = elem(ielem)%c2n(k,n)
              if ( nd <= nnodes0 ) then
               factor(k,ifcn) = factor(k,ifcn)                               &
                + (qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)                 &
                                                         *sadj%coltag(i,nd)/dt
              endif
            end do node_loop1a
          end do
          if ( ivisc >= 6 ) then
            do i = 1, soln%n_turb
              ind = soln%n_tot + i
              node_loop1b : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                  factor(k,ifcn) = factor(k,ifcn)                            &
                   + (turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)      &
                                                       *sadj%coltag(ind,nd)/dt
                endif
              end do node_loop1b
            end do
          endif

        else if ( itime == 2 ) then          ! BDF2

          if ( physical_timestep == 1 ) then      ! on timestep #1 of BDF2

            do i = 1, soln%n_tot
              if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
              node_loop2c : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                     + (qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)            &
                         *sadj%coltag(i,nd)/dt
                   if ( iblankn1(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                     + my_half*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)&
                         *coltag_nonoverset(i,nd)/dt
                   endif
                 else
                   factor(k,ifcn) = factor(k,ifcn)                           &
                            + (qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)     &
                                                       *sadj%coltag(i,nd)/dt &
                    + my_half*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn) &
                                                         *sadj%coltag(i,nd)/dt
                 endif
                endif
              end do node_loop2c
            end do
            if ( ivisc >= 6 ) then
              do i = 1, soln%n_turb
                ind = soln%n_tot + i
                node_loop2d : do k = 1, elem(ielem)%node_per_cell
                  nd = elem(ielem)%c2n(k,n)
                  if ( nd <= nnodes0 ) then
                   if ( overset_flag .and. moving_grid ) then
                    factor(k,ifcn) = factor(k,ifcn)                          &
                     + (turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt
                    if ( iblankn1(nd) == 1 ) then
                      factor(k,ifcn) = factor(k,ifcn)                        &
                        + my_half*(turbn(i,nd)-turbnp1(i,nd))                &
                      *sadj%rlamatn1(ind,nd,ifcn)*coltag_nonoverset(ind,nd)/dt
                    endif
                   else
                    factor(k,ifcn) = factor(k,ifcn)                          &
                     + (turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                     *sadj%coltag(ind,nd)/dt &
             + my_half*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt
                   endif
                  endif
                end do node_loop2d
              end do
            endif

          else                                    ! on timestep #n of BDF2

            do i = 1, soln%n_tot
              if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
              node_loop3a : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                     + my_1p5*(qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)     &
                                                         *sadj%coltag(i,nd)/dt
                   if ( iblankn1(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                    + my_half*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn) &
                                                   *coltag_nonoverset(i,nd)/dt
                   endif
                 else
                   factor(k,ifcn) = factor(k,ifcn)                           &
                     + my_1p5*(qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)     &
                                                       *sadj%coltag(i,nd)/dt &
                    + my_half*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn) &
                                                         *sadj%coltag(i,nd)/dt
                 endif
                endif
              end do node_loop3a
            end do
            if ( ivisc >= 6 ) then
              do i = 1, soln%n_turb
                ind = soln%n_tot + i
                node_loop3b : do k = 1, elem(ielem)%node_per_cell
                  nd = elem(ielem)%c2n(k,n)
                  if ( nd <= nnodes0 ) then
                   if ( overset_flag .and. moving_grid ) then
                    factor(k,ifcn) = factor(k,ifcn)                          &
                  + my_1p5*(turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt
                    if ( iblankn1(nd) == 1 ) then
                      factor(k,ifcn) = factor(k,ifcn)                        &
             + my_half*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                   else
                    factor(k,ifcn) = factor(k,ifcn)                          &
                  + my_1p5*(turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt&
             + my_half*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt
                   endif
                  endif
                end do node_loop3b
              end do
            endif

          endif

        else if ( itime == 3 ) then          ! BDF3/2opt

          if ( ibdf2opt == 0 ) then          ! BDF3
            a =  my_11/my_6
            c =  my_1p5
            d = -my_1/my_3
          else if ( ibdf2opt == 1 ) then     ! BDF2opt
            a =  0.48_dp*my_11/my_6 + 0.52_dp*my_1p5
            c =  0.48_dp*my_1p5     + 0.52_dp*my_half
            d = -0.48_dp*my_2/my_6
          else
            a=0.0_dp; c=0.0_dp; d=0.0_dp ! avoid uninit warn
            write(*,*) 'Unknown value for ibdf2opt: ', ibdf2opt
            call lmpi_die
          endif

          if ( physical_timestep == 1 ) then     ! on timestep #1 of BDF3/2opt

            do i = 1, soln%n_tot
              if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
              node_loop4c : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                  factor(k,ifcn) = factor(k,ifcn)                            &
                           + (qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)      &
                                                         *sadj%coltag(i,nd)/dt
                  if ( iblankn1(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                         + c*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)  &
                                                   *coltag_nonoverset(i,nd)/dt
                  endif
                  if ( iblankn2(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                         + d*(qn(i,nd)-qnp2(i,nd))*sadj%rlamatn2(i,nd,ifcn)  &
                                                   *coltag_nonoverset(i,nd)/dt
                  endif
                 else
                  factor(k,ifcn) = factor(k,ifcn)                            &
                           + (qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)      &
                                                        *sadj%coltag(i,nd)/dt&
                         + c*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)  &
                                                        *sadj%coltag(i,nd)/dt&
                         + d*(qn(i,nd)-qnp2(i,nd))*sadj%rlamatn2(i,nd,ifcn)  &
                                                         *sadj%coltag(i,nd)/dt
                 endif
                endif
              end do node_loop4c
            end do
            if ( ivisc >= 6 ) then
              do i = 1, soln%n_turb
                ind = soln%n_tot + i
                node_loop4d : do k = 1, elem(ielem)%node_per_cell
                  nd = elem(ielem)%c2n(k,n)
                  if ( nd <= nnodes0 ) then
                   if ( overset_flag .and. moving_grid ) then
                    factor(k,ifcn) = factor(k,ifcn)                          &
                     + (turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt
                    if ( iblankn1(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                   + c*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                    if ( iblankn2(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                   + d*(turbn(i,nd)-turbnp2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                   else
                    factor(k,ifcn) = factor(k,ifcn)                          &
                     + (turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt&
                   + c*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt&
                   + d*(turbn(i,nd)-turbnp2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt
                   endif
                  endif
                end do node_loop4d
              end do
            endif

          else if ( physical_timestep == 2 ) then !on timestep #2 of BDF3/2opt

            do i = 1, soln%n_tot
              if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
              node_loop5a : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                  factor(k,ifcn) = factor(k,ifcn)                            &
                   + my_1p5*(qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)       &
                                                         *sadj%coltag(i,nd)/dt
                  if ( iblankn1(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                        + c*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)   &
                                                   *coltag_nonoverset(i,nd)/dt
                  endif
                  if ( iblankn2(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                        + d*(qn(i,nd)-qnp2(i,nd))*sadj%rlamatn2(i,nd,ifcn)   &
                                                   *coltag_nonoverset(i,nd)/dt
                  endif
                 else
                  factor(k,ifcn) = factor(k,ifcn)                            &
                   + my_1p5*(qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)       &
                                                       *sadj%coltag(i,nd)/dt &
                        + c*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)   &
                                                       *sadj%coltag(i,nd)/dt &
                        + d*(qn(i,nd)-qnp2(i,nd))*sadj%rlamatn2(i,nd,ifcn)   &
                                                         *sadj%coltag(i,nd)/dt
                 endif
                endif
              end do node_loop5a
            end do
            if ( ivisc >= 6 ) then
              do i = 1, soln%n_turb
                ind = soln%n_tot + i
                node_loop5b : do k = 1, elem(ielem)%node_per_cell
                  nd = elem(ielem)%c2n(k,n)
                  if ( nd <= nnodes0 ) then
                   if ( overset_flag .and. moving_grid ) then
                    factor(k,ifcn) = factor(k,ifcn)                          &
              + my_1p5*(turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt
                    if ( iblankn1(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                   + c*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                    if ( iblankn2(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                   + d*(turbn(i,nd)-turbnp2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                   else
                    factor(k,ifcn) = factor(k,ifcn)                          &
              + my_1p5*(turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt&
                   + c*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt&
                   + d*(turbn(i,nd)-turbnp2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt
                   endif
                  endif
                end do node_loop5b
              end do
            endif

          else                                   ! on timestep #n of BDF3/2opt

            do i = 1, soln%n_tot
              if ( soln%eqn_set == incompressible .and. i == 1 ) cycle
              node_loop6a : do k = 1, elem(ielem)%node_per_cell
                nd = elem(ielem)%c2n(k,n)
                if ( nd <= nnodes0 ) then
                 if ( overset_flag .and. moving_grid ) then
                  factor(k,ifcn) = factor(k,ifcn)                            &
                        + a*(qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)       &
                                                         *sadj%coltag(i,nd)/dt
                  if ( iblankn1(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                        + c*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)   &
                                                   *coltag_nonoverset(i,nd)/dt
                  endif
                  if ( iblankn2(nd) == 1 ) then
                   factor(k,ifcn) = factor(k,ifcn)                           &
                        + d*(qn(i,nd)-qnp2(i,nd))*sadj%rlamatn2(i,nd,ifcn)   &
                                                   *coltag_nonoverset(i,nd)/dt
                  endif
                 else
                  factor(k,ifcn) = factor(k,ifcn)                            &
                        + a*(qn(i,nd)-qnm1(i,nd))*sadj%rlam(i,nd,ifcn)       &
                                                       *sadj%coltag(i,nd)/dt &
                        + c*(qn(i,nd)-qnp1(i,nd))*sadj%rlamatn1(i,nd,ifcn)   &
                                                       *sadj%coltag(i,nd)/dt &
                        + d*(qn(i,nd)-qnp2(i,nd))*sadj%rlamatn2(i,nd,ifcn)   &
                                                         *sadj%coltag(i,nd)/dt
                 endif
                endif
              end do node_loop6a
            end do
            if ( ivisc >= 6 ) then
              do i = 1, soln%n_turb
                ind = soln%n_tot + i
                node_loop6b : do k = 1, elem(ielem)%node_per_cell
                  nd = elem(ielem)%c2n(k,n)
                  if ( nd <= nnodes0 ) then
                   if ( overset_flag .and. moving_grid ) then
                    factor(k,ifcn) = factor(k,ifcn)                          &
                   + a*(turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt
                    if ( iblankn1(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                   + c*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                    if ( iblankn2(nd) == 1 ) then
                     factor(k,ifcn) = factor(k,ifcn)                         &
                   + d*(turbn(i,nd)-turbnp2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                 *coltag_nonoverset(ind,nd)/dt
                    endif
                   else
                    factor(k,ifcn) = factor(k,ifcn)                          &
                   + a*(turbn(i,nd)-turbnm1(i,nd))*sadj%rlam(ind,nd,ifcn)    &
                                                      *sadj%coltag(ind,nd)/dt&
                   + c*(turbn(i,nd)-turbnp1(i,nd))*sadj%rlamatn1(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt&
                   + d*(turbn(i,nd)-turbnp2(i,nd))*sadj%rlamatn2(ind,nd,ifcn)&
                                                      *sadj%coltag(ind,nd)/dt
                   endif
                  endif
                end do node_loop6b
              end do
            endif

          endif

        endif backplanes2

      endif build_factor

    end do function_loop

  end subroutine get_factor

end module dshape

# Makefile.in generated by automake 1.11.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008, 2009  Free Software Foundation,
# Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@

# -*- Makefile -*-



VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
bin_PROGRAMS = knife-convert$(EXEEXT) knife-vis$(EXEEXT)
EXTRA_PROGRAMS = knife-cut$(EXEEXT) knife-profile$(EXEEXT)
subdir = knife/src
DIST_COMMON = $(libknife_a_include_HEADERS) $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps =  \
	$(top_srcdir)/aclocal/ax_f90_module_extension.m4 \
	$(top_srcdir)/aclocal/ax_f90_module_flag.m4 \
	$(top_srcdir)/aclocal/capri.m4 $(top_srcdir)/aclocal/cgns.m4 \
	$(top_srcdir)/aclocal/cuda.m4 \
	$(top_srcdir)/aclocal/dynamic_loading.m4 \
	$(top_srcdir)/aclocal/f90_tuner.m4 \
	$(top_srcdir)/aclocal/f90_unix.m4 \
	$(top_srcdir)/aclocal/fccht.m4 \
	$(top_srcdir)/aclocal/fortran_2003_environment.m4 \
	$(top_srcdir)/aclocal/fortran_asynchronous_io.m4 \
	$(top_srcdir)/aclocal/fortran_c_interoperability.m4 \
	$(top_srcdir)/aclocal/fortran_etime.m4 \
	$(top_srcdir)/aclocal/fortran_open_big_endian.m4 \
	$(top_srcdir)/aclocal/fortran_open_stream.m4 \
	$(top_srcdir)/aclocal/fortran_posix_interface.m4 \
	$(top_srcdir)/aclocal/fun3d.m4 $(top_srcdir)/aclocal/gsi.m4 \
	$(top_srcdir)/aclocal/meshsim.m4 $(top_srcdir)/aclocal/mpi.m4 \
	$(top_srcdir)/aclocal/parmetis.m4 \
	$(top_srcdir)/aclocal/resource_limit.m4 \
	$(top_srcdir)/aclocal/zoltan.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
am__vpath_adj_setup = srcdirstrip=`echo "$(srcdir)" | sed 's|.|.|g'`;
am__vpath_adj = case $$p in \
    $(srcdir)/*) f=`echo "$$p" | sed "s|^$$srcdirstrip/||"`;; \
    *) f=$$p;; \
  esac;
am__strip_dir = f=`echo $$p | sed -e 's|^.*/||'`;
am__install_max = 40
am__nobase_strip_setup = \
  srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*|]/\\\\&/g'`
am__nobase_strip = \
  for p in $$list; do echo "$$p"; done | sed -e "s|$$srcdirstrip/||"
am__nobase_list = $(am__nobase_strip_setup); \
  for p in $$list; do echo "$$p $$p"; done | \
  sed "s| $$srcdirstrip/| |;"' / .*\//!s/ .*/ ./; s,\( .*\)/[^/]*$$,\1,' | \
  $(AWK) 'BEGIN { files["."] = "" } { files[$$2] = files[$$2] " " $$1; \
    if (++n[$$2] == $(am__install_max)) \
      { print $$2, files[$$2]; n[$$2] = 0; files[$$2] = "" } } \
    END { for (dir in files) print dir, files[dir] }'
am__base_list = \
  sed '$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;$$!N;s/\n/ /g' | \
  sed '$$!N;$$!N;$$!N;$$!N;s/\n/ /g'
am__installdirs = "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" \
	"$(DESTDIR)$(libknife_a_includedir)"
LIBRARIES = $(lib_LIBRARIES)
AR = ar
ARFLAGS = cru
libknife_a_AR = $(AR) $(ARFLAGS)
libknife_a_LIBADD =
am__objects_1 = adj.$(OBJEXT) array.$(OBJEXT) set.$(OBJEXT) \
	primal.$(OBJEXT) node.$(OBJEXT) segment.$(OBJEXT) \
	triangle.$(OBJEXT) mask.$(OBJEXT) poly.$(OBJEXT) \
	surface.$(OBJEXT) domain.$(OBJEXT) intersection.$(OBJEXT) \
	cut.$(OBJEXT) near.$(OBJEXT) subnode.$(OBJEXT) \
	subtri.$(OBJEXT) loop.$(OBJEXT) logger.$(OBJEXT) \
	knife_fortran.$(OBJEXT)
am_libknife_a_OBJECTS = $(am__objects_1)
libknife_a_OBJECTS = $(am_libknife_a_OBJECTS)
PROGRAMS = $(bin_PROGRAMS)
am_knife_convert_OBJECTS = knife_convert.$(OBJEXT)
knife_convert_OBJECTS = $(am_knife_convert_OBJECTS)
knife_convert_DEPENDENCIES = libknife.a
am_knife_cut_OBJECTS = knife_cut.$(OBJEXT)
knife_cut_OBJECTS = $(am_knife_cut_OBJECTS)
knife_cut_DEPENDENCIES = libknife.a
am__objects_2 = knife_profile-adj.$(OBJEXT) \
	knife_profile-array.$(OBJEXT) knife_profile-set.$(OBJEXT) \
	knife_profile-primal.$(OBJEXT) knife_profile-node.$(OBJEXT) \
	knife_profile-segment.$(OBJEXT) \
	knife_profile-triangle.$(OBJEXT) knife_profile-mask.$(OBJEXT) \
	knife_profile-poly.$(OBJEXT) knife_profile-surface.$(OBJEXT) \
	knife_profile-domain.$(OBJEXT) \
	knife_profile-intersection.$(OBJEXT) \
	knife_profile-cut.$(OBJEXT) knife_profile-near.$(OBJEXT) \
	knife_profile-subnode.$(OBJEXT) knife_profile-subtri.$(OBJEXT) \
	knife_profile-loop.$(OBJEXT) knife_profile-logger.$(OBJEXT) \
	knife_profile-knife_fortran.$(OBJEXT)
am_knife_profile_OBJECTS = knife_profile-knife_cut.$(OBJEXT) \
	$(am__objects_2)
knife_profile_OBJECTS = $(am_knife_profile_OBJECTS)
knife_profile_DEPENDENCIES =
knife_profile_LINK = $(CCLD) $(knife_profile_CFLAGS) $(CFLAGS) \
	$(knife_profile_LDFLAGS) $(LDFLAGS) -o $@
am_knife_vis_OBJECTS = knife_vis.$(OBJEXT)
knife_vis_OBJECTS = $(am_knife_vis_OBJECTS)
knife_vis_DEPENDENCIES = libknife.a
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
depcomp = $(SHELL) $(top_srcdir)/config/depcomp
am__depfiles_maybe = depfiles
am__mv = mv -f
COMPILE = $(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) \
	$(CPPFLAGS) $(AM_CFLAGS) $(CFLAGS)
CCLD = $(CC)
LINK = $(CCLD) $(AM_CFLAGS) $(CFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o $@
SOURCES = $(libknife_a_SOURCES) $(knife_convert_SOURCES) \
	$(knife_cut_SOURCES) $(knife_profile_SOURCES) \
	$(knife_vis_SOURCES)
DIST_SOURCES = $(libknife_a_SOURCES) $(knife_convert_SOURCES) \
	$(knife_cut_SOURCES) $(knife_profile_SOURCES) \
	$(knife_vis_SOURCES)
HEADERS = $(libknife_a_include_HEADERS)
ETAGS = etags
CTAGS = ctags
am__tty_colors = \
red=; grn=; lgn=; blu=; std=
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ACLOCAL_AMFLAGS = @ACLOCAL_AMFLAGS@
AMTAR = @AMTAR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BIBTEX = @BIBTEX@
CAPRIheader = @CAPRIheader@
CAPRIlibrary = @CAPRIlibrary@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CGNSinclude = @CGNSinclude@
CGNSlibrary = @CGNSlibrary@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CUDACC = @CUDACC@
CUDAFLAGS = @CUDAFLAGS@
CUDA_LIB_PATH = @CUDA_LIB_PATH@
CXX = @CXX@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F90_EXT_LIB = @F90_EXT_LIB@
FC = @FC@
FCFLAGS = @FCFLAGS@
FCLIBS = @FCLIBS@
FC_MODEXT = @FC_MODEXT@
FC_MODINC = @FC_MODINC@
GREP = @GREP@
HAVE_F2PY = @HAVE_F2PY@
HAVE_RUBY = @HAVE_RUBY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
KNIFE_SUBDIR = @KNIFE_SUBDIR@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
MOD_DEP_COMPILER = @MOD_DEP_COMPILER@
MPIF90 = @MPIF90@
MPIINC = @MPIINC@
MPIRUN = @MPIRUN@
MPI_EXT = @MPI_EXT@
MPI_Prefix = @MPI_Prefix@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PDFLATEX = @PDFLATEX@
PERL5 = @PERL5@
PHYSICS_TYPE = @PHYSICS_TYPE@
PYTHON = @PYTHON@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SUBDIR = @PYTHON_SUBDIR@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
REFINE_SUBDIR = @REFINE_SUBDIR@
SBOOMlibrary = @SBOOMlibrary@
SDKheader = @SDKheader@
SDKlibrary = @SDKlibrary@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SIXDOFLIBS = @SIXDOFLIBS@
STRIP = @STRIP@
TECIOLIBS = @TECIOLIBS@
VERSION = @VERSION@
VisItinclude = @VisItinclude@
VisItlibrary = @VisItlibrary@
XMKMF = @XMKMF@
Xheader = @Xheader@
Xlibrary = @Xlibrary@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_FC = @ac_ct_FC@
ac_empty = @ac_empty@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
dirtlibrary = @dirtlibrary@
docdir = @docdir@
dotlibrary = @dotlibrary@
dvidir = @dvidir@
dymorelibrary = @dymorelibrary@
exec_prefix = @exec_prefix@
fcompiler = @fcompiler@
have_bibtex = @have_bibtex@
have_latex = @have_latex@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
irslibrary = @irslibrary@
knife_deps = @knife_deps@
knife_ldadd = @knife_ldadd@
ksoptlibrary = @ksoptlibrary@
libcore_path = @libcore_path@
libdir = @libdir@
libexecdir = @libexecdir@
libturb_path = @libturb_path@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
meshsim_ldadd = @meshsim_ldadd@
mkdir_p = @mkdir_p@
mpi_ldadd = @mpi_ldadd@
npsollibrary = @npsollibrary@
oldincludedir = @oldincludedir@
parmetis_include = @parmetis_include@
parmetis_ldadd = @parmetis_ldadd@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
portlibrary = @portlibrary@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
punditinclude = @punditinclude@
punditlibrary = @punditlibrary@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
refine_deps = @refine_deps@
refine_ldadd = @refine_ldadd@
sbindir = @sbindir@
sdxlibrary = @sdxlibrary@
sfelibrary = @sfelibrary@
sharedstatedir = @sharedstatedir@
snoptlibrary = @snoptlibrary@
sparskitlibrary = @sparskitlibrary@
srcdir = @srcdir@
ssdclibrary = @ssdclibrary@
subdirs = @subdirs@
suggarlibrary = @suggarlibrary@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
zoltan_include = @zoltan_include@
zoltan_ldadd = @zoltan_ldadd@
lib_LIBRARIES = libknife.a
library_sources = \
	knife_definitions.h \
	adj.h adj.c \
	array.h array.c \
	set.h set.c \
	primal.h primal.c \
	node.h node.c \
	segment.h segment.c \
	triangle.h triangle.c \
	mask.h mask.c \
	poly.h poly.c \
	surface.h surface.c \
	domain.h domain.c \
	intersection.h intersection.c \
	cut.h cut.c \
	near.h near.c \
	subnode.h subnode.c \
	subtri.h subtri.c \
	loop.h loop.c \
	logger.h logger.c \
	knife_fortran.c

libknife_a_SOURCES = $(library_sources)
libknife_a_includedir = @prefix@/include
libknife_a_include_HEADERS = \
	knife_definitions.h \
	adj.h \
	array.h \
	set.h \
	primal.h \
	node.h \
	segment.h \
	triangle.h \
	mask.h \
	poly.h \
	surface.h \
	domain.h \
	intersection.h \
	cut.h \
	near.h \
	subnode.h \
	subtri.h \
	loop.h \
	logger.h

knife_convert_SOURCES = knife_convert.c
knife_convert_LDADD = libknife.a -lm
knife_vis_SOURCES = knife_vis.c
knife_vis_LDADD = libknife.a -lm
knife_cut_SOURCES = knife_cut.c
knife_cut_LDADD = libknife.a -lm
knife_profile_SOURCES = knife_cut.c ${library_sources}
knife_profile_CFLAGS = -pg
knife_profile_LDFLAGS = -pg
knife_profile_LDADD = -lm
EXTRA_DIST = build_ruby_extension.rb RubyExtensionBuilder.rb test_runner.rb \
	adj_ruby.c adj_test.rb \
	primal_ruby.c primal_test.rb \
	node_ruby.c node_test.rb 

TESTS = test_runner.rb
all: all-am

.SUFFIXES:
.SUFFIXES: .c .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am  $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu knife/src/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu knife/src/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
install-libLIBRARIES: $(lib_LIBRARIES)
	@$(NORMAL_INSTALL)
	test -z "$(libdir)" || $(MKDIR_P) "$(DESTDIR)$(libdir)"
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	list2=; for p in $$list; do \
	  if test -f $$p; then \
	    list2="$$list2 $$p"; \
	  else :; fi; \
	done; \
	test -z "$$list2" || { \
	  echo " $(INSTALL_DATA) $$list2 '$(DESTDIR)$(libdir)'"; \
	  $(INSTALL_DATA) $$list2 "$(DESTDIR)$(libdir)" || exit $$?; }
	@$(POST_INSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	for p in $$list; do \
	  if test -f $$p; then \
	    $(am__strip_dir) \
	    echo " ( cd '$(DESTDIR)$(libdir)' && $(RANLIB) $$f )"; \
	    ( cd "$(DESTDIR)$(libdir)" && $(RANLIB) $$f ) || exit $$?; \
	  else :; fi; \
	done

uninstall-libLIBRARIES:
	@$(NORMAL_UNINSTALL)
	@list='$(lib_LIBRARIES)'; test -n "$(libdir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	test -n "$$files" || exit 0; \
	echo " ( cd '$(DESTDIR)$(libdir)' && rm -f "$$files" )"; \
	cd "$(DESTDIR)$(libdir)" && rm -f $$files

clean-libLIBRARIES:
	-test -z "$(lib_LIBRARIES)" || rm -f $(lib_LIBRARIES)
libknife.a: $(libknife_a_OBJECTS) $(libknife_a_DEPENDENCIES) 
	-rm -f libknife.a
	$(libknife_a_AR) libknife.a $(libknife_a_OBJECTS) $(libknife_a_LIBADD)
	$(RANLIB) libknife.a
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	test -z "$(bindir)" || $(MKDIR_P) "$(DESTDIR)$(bindir)"
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p; \
	  then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	      echo " $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	      $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' `; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	-test -z "$(bin_PROGRAMS)" || rm -f $(bin_PROGRAMS)
knife-convert$(EXEEXT): $(knife_convert_OBJECTS) $(knife_convert_DEPENDENCIES) 
	@rm -f knife-convert$(EXEEXT)
	$(LINK) $(knife_convert_OBJECTS) $(knife_convert_LDADD) $(LIBS)
knife-cut$(EXEEXT): $(knife_cut_OBJECTS) $(knife_cut_DEPENDENCIES) 
	@rm -f knife-cut$(EXEEXT)
	$(LINK) $(knife_cut_OBJECTS) $(knife_cut_LDADD) $(LIBS)
knife-profile$(EXEEXT): $(knife_profile_OBJECTS) $(knife_profile_DEPENDENCIES) 
	@rm -f knife-profile$(EXEEXT)
	$(knife_profile_LINK) $(knife_profile_OBJECTS) $(knife_profile_LDADD) $(LIBS)
knife-vis$(EXEEXT): $(knife_vis_OBJECTS) $(knife_vis_DEPENDENCIES) 
	@rm -f knife-vis$(EXEEXT)
	$(LINK) $(knife_vis_OBJECTS) $(knife_vis_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/adj.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/array.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/cut.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/domain.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/intersection.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_convert.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_cut.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_fortran.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-adj.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-array.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-cut.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-domain.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-intersection.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-knife_cut.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-knife_fortran.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-logger.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-loop.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-mask.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-near.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-node.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-poly.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-primal.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-segment.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-set.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-subnode.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-subtri.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-surface.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_profile-triangle.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/knife_vis.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/logger.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/loop.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/mask.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/near.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/node.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/poly.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/primal.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/segment.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/set.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/subnode.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/subtri.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/surface.Po@am__quote@
@AMDEP_TRUE@@am__include@ @am__quote@./$(DEPDIR)/triangle.Po@am__quote@

.c.o:
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ $<
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c $<

.c.obj:
@am__fastdepCC_TRUE@	$(COMPILE) -MT $@ -MD -MP -MF $(DEPDIR)/$*.Tpo -c -o $@ `$(CYGPATH_W) '$<'`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/$*.Tpo $(DEPDIR)/$*.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='$<' object='$@' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(COMPILE) -c `$(CYGPATH_W) '$<'`

knife_profile-knife_cut.o: knife_cut.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-knife_cut.o -MD -MP -MF $(DEPDIR)/knife_profile-knife_cut.Tpo -c -o knife_profile-knife_cut.o `test -f 'knife_cut.c' || echo '$(srcdir)/'`knife_cut.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-knife_cut.Tpo $(DEPDIR)/knife_profile-knife_cut.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='knife_cut.c' object='knife_profile-knife_cut.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-knife_cut.o `test -f 'knife_cut.c' || echo '$(srcdir)/'`knife_cut.c

knife_profile-knife_cut.obj: knife_cut.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-knife_cut.obj -MD -MP -MF $(DEPDIR)/knife_profile-knife_cut.Tpo -c -o knife_profile-knife_cut.obj `if test -f 'knife_cut.c'; then $(CYGPATH_W) 'knife_cut.c'; else $(CYGPATH_W) '$(srcdir)/knife_cut.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-knife_cut.Tpo $(DEPDIR)/knife_profile-knife_cut.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='knife_cut.c' object='knife_profile-knife_cut.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-knife_cut.obj `if test -f 'knife_cut.c'; then $(CYGPATH_W) 'knife_cut.c'; else $(CYGPATH_W) '$(srcdir)/knife_cut.c'; fi`

knife_profile-adj.o: adj.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-adj.o -MD -MP -MF $(DEPDIR)/knife_profile-adj.Tpo -c -o knife_profile-adj.o `test -f 'adj.c' || echo '$(srcdir)/'`adj.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-adj.Tpo $(DEPDIR)/knife_profile-adj.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='adj.c' object='knife_profile-adj.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-adj.o `test -f 'adj.c' || echo '$(srcdir)/'`adj.c

knife_profile-adj.obj: adj.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-adj.obj -MD -MP -MF $(DEPDIR)/knife_profile-adj.Tpo -c -o knife_profile-adj.obj `if test -f 'adj.c'; then $(CYGPATH_W) 'adj.c'; else $(CYGPATH_W) '$(srcdir)/adj.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-adj.Tpo $(DEPDIR)/knife_profile-adj.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='adj.c' object='knife_profile-adj.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-adj.obj `if test -f 'adj.c'; then $(CYGPATH_W) 'adj.c'; else $(CYGPATH_W) '$(srcdir)/adj.c'; fi`

knife_profile-array.o: array.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-array.o -MD -MP -MF $(DEPDIR)/knife_profile-array.Tpo -c -o knife_profile-array.o `test -f 'array.c' || echo '$(srcdir)/'`array.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-array.Tpo $(DEPDIR)/knife_profile-array.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='array.c' object='knife_profile-array.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-array.o `test -f 'array.c' || echo '$(srcdir)/'`array.c

knife_profile-array.obj: array.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-array.obj -MD -MP -MF $(DEPDIR)/knife_profile-array.Tpo -c -o knife_profile-array.obj `if test -f 'array.c'; then $(CYGPATH_W) 'array.c'; else $(CYGPATH_W) '$(srcdir)/array.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-array.Tpo $(DEPDIR)/knife_profile-array.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='array.c' object='knife_profile-array.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-array.obj `if test -f 'array.c'; then $(CYGPATH_W) 'array.c'; else $(CYGPATH_W) '$(srcdir)/array.c'; fi`

knife_profile-set.o: set.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-set.o -MD -MP -MF $(DEPDIR)/knife_profile-set.Tpo -c -o knife_profile-set.o `test -f 'set.c' || echo '$(srcdir)/'`set.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-set.Tpo $(DEPDIR)/knife_profile-set.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='set.c' object='knife_profile-set.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-set.o `test -f 'set.c' || echo '$(srcdir)/'`set.c

knife_profile-set.obj: set.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-set.obj -MD -MP -MF $(DEPDIR)/knife_profile-set.Tpo -c -o knife_profile-set.obj `if test -f 'set.c'; then $(CYGPATH_W) 'set.c'; else $(CYGPATH_W) '$(srcdir)/set.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-set.Tpo $(DEPDIR)/knife_profile-set.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='set.c' object='knife_profile-set.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-set.obj `if test -f 'set.c'; then $(CYGPATH_W) 'set.c'; else $(CYGPATH_W) '$(srcdir)/set.c'; fi`

knife_profile-primal.o: primal.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-primal.o -MD -MP -MF $(DEPDIR)/knife_profile-primal.Tpo -c -o knife_profile-primal.o `test -f 'primal.c' || echo '$(srcdir)/'`primal.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-primal.Tpo $(DEPDIR)/knife_profile-primal.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='primal.c' object='knife_profile-primal.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-primal.o `test -f 'primal.c' || echo '$(srcdir)/'`primal.c

knife_profile-primal.obj: primal.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-primal.obj -MD -MP -MF $(DEPDIR)/knife_profile-primal.Tpo -c -o knife_profile-primal.obj `if test -f 'primal.c'; then $(CYGPATH_W) 'primal.c'; else $(CYGPATH_W) '$(srcdir)/primal.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-primal.Tpo $(DEPDIR)/knife_profile-primal.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='primal.c' object='knife_profile-primal.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-primal.obj `if test -f 'primal.c'; then $(CYGPATH_W) 'primal.c'; else $(CYGPATH_W) '$(srcdir)/primal.c'; fi`

knife_profile-node.o: node.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-node.o -MD -MP -MF $(DEPDIR)/knife_profile-node.Tpo -c -o knife_profile-node.o `test -f 'node.c' || echo '$(srcdir)/'`node.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-node.Tpo $(DEPDIR)/knife_profile-node.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='node.c' object='knife_profile-node.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-node.o `test -f 'node.c' || echo '$(srcdir)/'`node.c

knife_profile-node.obj: node.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-node.obj -MD -MP -MF $(DEPDIR)/knife_profile-node.Tpo -c -o knife_profile-node.obj `if test -f 'node.c'; then $(CYGPATH_W) 'node.c'; else $(CYGPATH_W) '$(srcdir)/node.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-node.Tpo $(DEPDIR)/knife_profile-node.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='node.c' object='knife_profile-node.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-node.obj `if test -f 'node.c'; then $(CYGPATH_W) 'node.c'; else $(CYGPATH_W) '$(srcdir)/node.c'; fi`

knife_profile-segment.o: segment.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-segment.o -MD -MP -MF $(DEPDIR)/knife_profile-segment.Tpo -c -o knife_profile-segment.o `test -f 'segment.c' || echo '$(srcdir)/'`segment.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-segment.Tpo $(DEPDIR)/knife_profile-segment.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='segment.c' object='knife_profile-segment.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-segment.o `test -f 'segment.c' || echo '$(srcdir)/'`segment.c

knife_profile-segment.obj: segment.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-segment.obj -MD -MP -MF $(DEPDIR)/knife_profile-segment.Tpo -c -o knife_profile-segment.obj `if test -f 'segment.c'; then $(CYGPATH_W) 'segment.c'; else $(CYGPATH_W) '$(srcdir)/segment.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-segment.Tpo $(DEPDIR)/knife_profile-segment.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='segment.c' object='knife_profile-segment.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-segment.obj `if test -f 'segment.c'; then $(CYGPATH_W) 'segment.c'; else $(CYGPATH_W) '$(srcdir)/segment.c'; fi`

knife_profile-triangle.o: triangle.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-triangle.o -MD -MP -MF $(DEPDIR)/knife_profile-triangle.Tpo -c -o knife_profile-triangle.o `test -f 'triangle.c' || echo '$(srcdir)/'`triangle.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-triangle.Tpo $(DEPDIR)/knife_profile-triangle.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='triangle.c' object='knife_profile-triangle.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-triangle.o `test -f 'triangle.c' || echo '$(srcdir)/'`triangle.c

knife_profile-triangle.obj: triangle.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-triangle.obj -MD -MP -MF $(DEPDIR)/knife_profile-triangle.Tpo -c -o knife_profile-triangle.obj `if test -f 'triangle.c'; then $(CYGPATH_W) 'triangle.c'; else $(CYGPATH_W) '$(srcdir)/triangle.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-triangle.Tpo $(DEPDIR)/knife_profile-triangle.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='triangle.c' object='knife_profile-triangle.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-triangle.obj `if test -f 'triangle.c'; then $(CYGPATH_W) 'triangle.c'; else $(CYGPATH_W) '$(srcdir)/triangle.c'; fi`

knife_profile-mask.o: mask.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-mask.o -MD -MP -MF $(DEPDIR)/knife_profile-mask.Tpo -c -o knife_profile-mask.o `test -f 'mask.c' || echo '$(srcdir)/'`mask.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-mask.Tpo $(DEPDIR)/knife_profile-mask.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='mask.c' object='knife_profile-mask.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-mask.o `test -f 'mask.c' || echo '$(srcdir)/'`mask.c

knife_profile-mask.obj: mask.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-mask.obj -MD -MP -MF $(DEPDIR)/knife_profile-mask.Tpo -c -o knife_profile-mask.obj `if test -f 'mask.c'; then $(CYGPATH_W) 'mask.c'; else $(CYGPATH_W) '$(srcdir)/mask.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-mask.Tpo $(DEPDIR)/knife_profile-mask.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='mask.c' object='knife_profile-mask.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-mask.obj `if test -f 'mask.c'; then $(CYGPATH_W) 'mask.c'; else $(CYGPATH_W) '$(srcdir)/mask.c'; fi`

knife_profile-poly.o: poly.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-poly.o -MD -MP -MF $(DEPDIR)/knife_profile-poly.Tpo -c -o knife_profile-poly.o `test -f 'poly.c' || echo '$(srcdir)/'`poly.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-poly.Tpo $(DEPDIR)/knife_profile-poly.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='poly.c' object='knife_profile-poly.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-poly.o `test -f 'poly.c' || echo '$(srcdir)/'`poly.c

knife_profile-poly.obj: poly.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-poly.obj -MD -MP -MF $(DEPDIR)/knife_profile-poly.Tpo -c -o knife_profile-poly.obj `if test -f 'poly.c'; then $(CYGPATH_W) 'poly.c'; else $(CYGPATH_W) '$(srcdir)/poly.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-poly.Tpo $(DEPDIR)/knife_profile-poly.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='poly.c' object='knife_profile-poly.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-poly.obj `if test -f 'poly.c'; then $(CYGPATH_W) 'poly.c'; else $(CYGPATH_W) '$(srcdir)/poly.c'; fi`

knife_profile-surface.o: surface.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-surface.o -MD -MP -MF $(DEPDIR)/knife_profile-surface.Tpo -c -o knife_profile-surface.o `test -f 'surface.c' || echo '$(srcdir)/'`surface.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-surface.Tpo $(DEPDIR)/knife_profile-surface.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='surface.c' object='knife_profile-surface.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-surface.o `test -f 'surface.c' || echo '$(srcdir)/'`surface.c

knife_profile-surface.obj: surface.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-surface.obj -MD -MP -MF $(DEPDIR)/knife_profile-surface.Tpo -c -o knife_profile-surface.obj `if test -f 'surface.c'; then $(CYGPATH_W) 'surface.c'; else $(CYGPATH_W) '$(srcdir)/surface.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-surface.Tpo $(DEPDIR)/knife_profile-surface.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='surface.c' object='knife_profile-surface.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-surface.obj `if test -f 'surface.c'; then $(CYGPATH_W) 'surface.c'; else $(CYGPATH_W) '$(srcdir)/surface.c'; fi`

knife_profile-domain.o: domain.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-domain.o -MD -MP -MF $(DEPDIR)/knife_profile-domain.Tpo -c -o knife_profile-domain.o `test -f 'domain.c' || echo '$(srcdir)/'`domain.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-domain.Tpo $(DEPDIR)/knife_profile-domain.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='domain.c' object='knife_profile-domain.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-domain.o `test -f 'domain.c' || echo '$(srcdir)/'`domain.c

knife_profile-domain.obj: domain.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-domain.obj -MD -MP -MF $(DEPDIR)/knife_profile-domain.Tpo -c -o knife_profile-domain.obj `if test -f 'domain.c'; then $(CYGPATH_W) 'domain.c'; else $(CYGPATH_W) '$(srcdir)/domain.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-domain.Tpo $(DEPDIR)/knife_profile-domain.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='domain.c' object='knife_profile-domain.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-domain.obj `if test -f 'domain.c'; then $(CYGPATH_W) 'domain.c'; else $(CYGPATH_W) '$(srcdir)/domain.c'; fi`

knife_profile-intersection.o: intersection.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-intersection.o -MD -MP -MF $(DEPDIR)/knife_profile-intersection.Tpo -c -o knife_profile-intersection.o `test -f 'intersection.c' || echo '$(srcdir)/'`intersection.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-intersection.Tpo $(DEPDIR)/knife_profile-intersection.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='intersection.c' object='knife_profile-intersection.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-intersection.o `test -f 'intersection.c' || echo '$(srcdir)/'`intersection.c

knife_profile-intersection.obj: intersection.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-intersection.obj -MD -MP -MF $(DEPDIR)/knife_profile-intersection.Tpo -c -o knife_profile-intersection.obj `if test -f 'intersection.c'; then $(CYGPATH_W) 'intersection.c'; else $(CYGPATH_W) '$(srcdir)/intersection.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-intersection.Tpo $(DEPDIR)/knife_profile-intersection.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='intersection.c' object='knife_profile-intersection.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-intersection.obj `if test -f 'intersection.c'; then $(CYGPATH_W) 'intersection.c'; else $(CYGPATH_W) '$(srcdir)/intersection.c'; fi`

knife_profile-cut.o: cut.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-cut.o -MD -MP -MF $(DEPDIR)/knife_profile-cut.Tpo -c -o knife_profile-cut.o `test -f 'cut.c' || echo '$(srcdir)/'`cut.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-cut.Tpo $(DEPDIR)/knife_profile-cut.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='cut.c' object='knife_profile-cut.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-cut.o `test -f 'cut.c' || echo '$(srcdir)/'`cut.c

knife_profile-cut.obj: cut.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-cut.obj -MD -MP -MF $(DEPDIR)/knife_profile-cut.Tpo -c -o knife_profile-cut.obj `if test -f 'cut.c'; then $(CYGPATH_W) 'cut.c'; else $(CYGPATH_W) '$(srcdir)/cut.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-cut.Tpo $(DEPDIR)/knife_profile-cut.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='cut.c' object='knife_profile-cut.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-cut.obj `if test -f 'cut.c'; then $(CYGPATH_W) 'cut.c'; else $(CYGPATH_W) '$(srcdir)/cut.c'; fi`

knife_profile-near.o: near.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-near.o -MD -MP -MF $(DEPDIR)/knife_profile-near.Tpo -c -o knife_profile-near.o `test -f 'near.c' || echo '$(srcdir)/'`near.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-near.Tpo $(DEPDIR)/knife_profile-near.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='near.c' object='knife_profile-near.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-near.o `test -f 'near.c' || echo '$(srcdir)/'`near.c

knife_profile-near.obj: near.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-near.obj -MD -MP -MF $(DEPDIR)/knife_profile-near.Tpo -c -o knife_profile-near.obj `if test -f 'near.c'; then $(CYGPATH_W) 'near.c'; else $(CYGPATH_W) '$(srcdir)/near.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-near.Tpo $(DEPDIR)/knife_profile-near.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='near.c' object='knife_profile-near.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-near.obj `if test -f 'near.c'; then $(CYGPATH_W) 'near.c'; else $(CYGPATH_W) '$(srcdir)/near.c'; fi`

knife_profile-subnode.o: subnode.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-subnode.o -MD -MP -MF $(DEPDIR)/knife_profile-subnode.Tpo -c -o knife_profile-subnode.o `test -f 'subnode.c' || echo '$(srcdir)/'`subnode.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-subnode.Tpo $(DEPDIR)/knife_profile-subnode.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='subnode.c' object='knife_profile-subnode.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-subnode.o `test -f 'subnode.c' || echo '$(srcdir)/'`subnode.c

knife_profile-subnode.obj: subnode.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-subnode.obj -MD -MP -MF $(DEPDIR)/knife_profile-subnode.Tpo -c -o knife_profile-subnode.obj `if test -f 'subnode.c'; then $(CYGPATH_W) 'subnode.c'; else $(CYGPATH_W) '$(srcdir)/subnode.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-subnode.Tpo $(DEPDIR)/knife_profile-subnode.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='subnode.c' object='knife_profile-subnode.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-subnode.obj `if test -f 'subnode.c'; then $(CYGPATH_W) 'subnode.c'; else $(CYGPATH_W) '$(srcdir)/subnode.c'; fi`

knife_profile-subtri.o: subtri.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-subtri.o -MD -MP -MF $(DEPDIR)/knife_profile-subtri.Tpo -c -o knife_profile-subtri.o `test -f 'subtri.c' || echo '$(srcdir)/'`subtri.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-subtri.Tpo $(DEPDIR)/knife_profile-subtri.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='subtri.c' object='knife_profile-subtri.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-subtri.o `test -f 'subtri.c' || echo '$(srcdir)/'`subtri.c

knife_profile-subtri.obj: subtri.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-subtri.obj -MD -MP -MF $(DEPDIR)/knife_profile-subtri.Tpo -c -o knife_profile-subtri.obj `if test -f 'subtri.c'; then $(CYGPATH_W) 'subtri.c'; else $(CYGPATH_W) '$(srcdir)/subtri.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-subtri.Tpo $(DEPDIR)/knife_profile-subtri.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='subtri.c' object='knife_profile-subtri.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-subtri.obj `if test -f 'subtri.c'; then $(CYGPATH_W) 'subtri.c'; else $(CYGPATH_W) '$(srcdir)/subtri.c'; fi`

knife_profile-loop.o: loop.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-loop.o -MD -MP -MF $(DEPDIR)/knife_profile-loop.Tpo -c -o knife_profile-loop.o `test -f 'loop.c' || echo '$(srcdir)/'`loop.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-loop.Tpo $(DEPDIR)/knife_profile-loop.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='loop.c' object='knife_profile-loop.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-loop.o `test -f 'loop.c' || echo '$(srcdir)/'`loop.c

knife_profile-loop.obj: loop.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-loop.obj -MD -MP -MF $(DEPDIR)/knife_profile-loop.Tpo -c -o knife_profile-loop.obj `if test -f 'loop.c'; then $(CYGPATH_W) 'loop.c'; else $(CYGPATH_W) '$(srcdir)/loop.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-loop.Tpo $(DEPDIR)/knife_profile-loop.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='loop.c' object='knife_profile-loop.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-loop.obj `if test -f 'loop.c'; then $(CYGPATH_W) 'loop.c'; else $(CYGPATH_W) '$(srcdir)/loop.c'; fi`

knife_profile-logger.o: logger.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-logger.o -MD -MP -MF $(DEPDIR)/knife_profile-logger.Tpo -c -o knife_profile-logger.o `test -f 'logger.c' || echo '$(srcdir)/'`logger.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-logger.Tpo $(DEPDIR)/knife_profile-logger.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='logger.c' object='knife_profile-logger.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-logger.o `test -f 'logger.c' || echo '$(srcdir)/'`logger.c

knife_profile-logger.obj: logger.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-logger.obj -MD -MP -MF $(DEPDIR)/knife_profile-logger.Tpo -c -o knife_profile-logger.obj `if test -f 'logger.c'; then $(CYGPATH_W) 'logger.c'; else $(CYGPATH_W) '$(srcdir)/logger.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-logger.Tpo $(DEPDIR)/knife_profile-logger.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='logger.c' object='knife_profile-logger.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-logger.obj `if test -f 'logger.c'; then $(CYGPATH_W) 'logger.c'; else $(CYGPATH_W) '$(srcdir)/logger.c'; fi`

knife_profile-knife_fortran.o: knife_fortran.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-knife_fortran.o -MD -MP -MF $(DEPDIR)/knife_profile-knife_fortran.Tpo -c -o knife_profile-knife_fortran.o `test -f 'knife_fortran.c' || echo '$(srcdir)/'`knife_fortran.c
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-knife_fortran.Tpo $(DEPDIR)/knife_profile-knife_fortran.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='knife_fortran.c' object='knife_profile-knife_fortran.o' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-knife_fortran.o `test -f 'knife_fortran.c' || echo '$(srcdir)/'`knife_fortran.c

knife_profile-knife_fortran.obj: knife_fortran.c
@am__fastdepCC_TRUE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -MT knife_profile-knife_fortran.obj -MD -MP -MF $(DEPDIR)/knife_profile-knife_fortran.Tpo -c -o knife_profile-knife_fortran.obj `if test -f 'knife_fortran.c'; then $(CYGPATH_W) 'knife_fortran.c'; else $(CYGPATH_W) '$(srcdir)/knife_fortran.c'; fi`
@am__fastdepCC_TRUE@	$(am__mv) $(DEPDIR)/knife_profile-knife_fortran.Tpo $(DEPDIR)/knife_profile-knife_fortran.Po
@AMDEP_TRUE@@am__fastdepCC_FALSE@	source='knife_fortran.c' object='knife_profile-knife_fortran.obj' libtool=no @AMDEPBACKSLASH@
@AMDEP_TRUE@@am__fastdepCC_FALSE@	DEPDIR=$(DEPDIR) $(CCDEPMODE) $(depcomp) @AMDEPBACKSLASH@
@am__fastdepCC_FALSE@	$(CC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) $(AM_CPPFLAGS) $(CPPFLAGS) $(knife_profile_CFLAGS) $(CFLAGS) -c -o knife_profile-knife_fortran.obj `if test -f 'knife_fortran.c'; then $(CYGPATH_W) 'knife_fortran.c'; else $(CYGPATH_W) '$(srcdir)/knife_fortran.c'; fi`
install-libknife_a_includeHEADERS: $(libknife_a_include_HEADERS)
	@$(NORMAL_INSTALL)
	test -z "$(libknife_a_includedir)" || $(MKDIR_P) "$(DESTDIR)$(libknife_a_includedir)"
	@list='$(libknife_a_include_HEADERS)'; test -n "$(libknife_a_includedir)" || list=; \
	for p in $$list; do \
	  if test -f "$$p"; then d=; else d="$(srcdir)/"; fi; \
	  echo "$$d$$p"; \
	done | $(am__base_list) | \
	while read files; do \
	  echo " $(INSTALL_HEADER) $$files '$(DESTDIR)$(libknife_a_includedir)'"; \
	  $(INSTALL_HEADER) $$files "$(DESTDIR)$(libknife_a_includedir)" || exit $$?; \
	done

uninstall-libknife_a_includeHEADERS:
	@$(NORMAL_UNINSTALL)
	@list='$(libknife_a_include_HEADERS)'; test -n "$(libknife_a_includedir)" || list=; \
	files=`for p in $$list; do echo $$p; done | sed -e 's|^.*/||'`; \
	test -n "$$files" || exit 0; \
	echo " ( cd '$(DESTDIR)$(libknife_a_includedir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(libknife_a_includedir)" && rm -f $$files

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	set x; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

check-TESTS: $(TESTS)
	@failed=0; all=0; xfail=0; xpass=0; skip=0; \
	srcdir=$(srcdir); export srcdir; \
	list=' $(TESTS) '; \
	$(am__tty_colors); \
	if test -n "$$list"; then \
	  for tst in $$list; do \
	    if test -f ./$$tst; then dir=./; \
	    elif test -f $$tst; then dir=; \
	    else dir="$(srcdir)/"; fi; \
	    if $(TESTS_ENVIRONMENT) $${dir}$$tst; then \
	      all=`expr $$all + 1`; \
	      case " $(XFAIL_TESTS) " in \
	      *[\ \	]$$tst[\ \	]*) \
		xpass=`expr $$xpass + 1`; \
		failed=`expr $$failed + 1`; \
		col=$$red; res=XPASS; \
	      ;; \
	      *) \
		col=$$grn; res=PASS; \
	      ;; \
	      esac; \
	    elif test $$? -ne 77; then \
	      all=`expr $$all + 1`; \
	      case " $(XFAIL_TESTS) " in \
	      *[\ \	]$$tst[\ \	]*) \
		xfail=`expr $$xfail + 1`; \
		col=$$lgn; res=XFAIL; \
	      ;; \
	      *) \
		failed=`expr $$failed + 1`; \
		col=$$red; res=FAIL; \
	      ;; \
	      esac; \
	    else \
	      skip=`expr $$skip + 1`; \
	      col=$$blu; res=SKIP; \
	    fi; \
	    echo "$${col}$$res$${std}: $$tst"; \
	  done; \
	  if test "$$all" -eq 1; then \
	    tests="test"; \
	    All=""; \
	  else \
	    tests="tests"; \
	    All="All "; \
	  fi; \
	  if test "$$failed" -eq 0; then \
	    if test "$$xfail" -eq 0; then \
	      banner="$$All$$all $$tests passed"; \
	    else \
	      if test "$$xfail" -eq 1; then failures=failure; else failures=failures; fi; \
	      banner="$$All$$all $$tests behaved as expected ($$xfail expected $$failures)"; \
	    fi; \
	  else \
	    if test "$$xpass" -eq 0; then \
	      banner="$$failed of $$all $$tests failed"; \
	    else \
	      if test "$$xpass" -eq 1; then passes=pass; else passes=passes; fi; \
	      banner="$$failed of $$all $$tests did not behave as expected ($$xpass unexpected $$passes)"; \
	    fi; \
	  fi; \
	  dashes="$$banner"; \
	  skipped=""; \
	  if test "$$skip" -ne 0; then \
	    if test "$$skip" -eq 1; then \
	      skipped="($$skip test was not run)"; \
	    else \
	      skipped="($$skip tests were not run)"; \
	    fi; \
	    test `echo "$$skipped" | wc -c` -le `echo "$$banner" | wc -c` || \
	      dashes="$$skipped"; \
	  fi; \
	  report=""; \
	  if test "$$failed" -ne 0 && test -n "$(PACKAGE_BUGREPORT)"; then \
	    report="Please report to $(PACKAGE_BUGREPORT)"; \
	    test `echo "$$report" | wc -c` -le `echo "$$banner" | wc -c` || \
	      dashes="$$report"; \
	  fi; \
	  dashes=`echo "$$dashes" | sed s/./=/g`; \
	  if test "$$failed" -eq 0; then \
	    echo "$$grn$$dashes"; \
	  else \
	    echo "$$red$$dashes"; \
	  fi; \
	  echo "$$banner"; \
	  test -z "$$skipped" || echo "$$skipped"; \
	  test -z "$$report" || echo "$$report"; \
	  echo "$$dashes$$std"; \
	  test "$$failed" -eq 0; \
	else :; fi

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
	$(MAKE) $(AM_MAKEFLAGS) check-TESTS
check: check-am
all-am: Makefile $(LIBRARIES) $(PROGRAMS) $(HEADERS)
installdirs:
	for dir in "$(DESTDIR)$(libdir)" "$(DESTDIR)$(bindir)" "$(DESTDIR)$(libknife_a_includedir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-libLIBRARIES \
	mostlyclean-am

distclean: distclean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am: install-libknife_a_includeHEADERS

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS install-libLIBRARIES

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -rf ./$(DEPDIR)
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS uninstall-libLIBRARIES \
	uninstall-libknife_a_includeHEADERS

.MAKE: check-am install-am install-strip

.PHONY: CTAGS GTAGS all all-am check check-TESTS check-am clean \
	clean-binPROGRAMS clean-generic clean-libLIBRARIES ctags \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-binPROGRAMS install-data install-data-am \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-libLIBRARIES install-libknife_a_includeHEADERS \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic pdf pdf-am \
	ps ps-am tags uninstall uninstall-am uninstall-binPROGRAMS \
	uninstall-libLIBRARIES uninstall-libknife_a_includeHEADERS


# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

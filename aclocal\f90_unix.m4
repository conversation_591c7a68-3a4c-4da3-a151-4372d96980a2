AC_DEFUN([AX_F90_UNIX],
[AC_CACHE_CHECK([fortran provides f90_unix module],
 ax_cv_f90_unix,
 [AC_LANG_PUSH(Fortran)
  AC_LINK_IFELSE(
  [
       program main
       use f90_unix_io,   only : flush
       use f90_unix_dir,  only : chdir
       use f90_unix_proc, only : sleep
       use f90_unix_proc, only : system
       use f90_unix_dir,  only : unlink
       use f90_unix,      only : exit
       call flush(6)
       call chdir(".")
       call sleep(1)
       call system("touch temp_file_f90_unix")
       call unlink("temp_file_f90_unix")
       call exit(0)
       end
  ],
  [ax_cv_f90_unix=yes],
  [ax_cv_f90_unix=no]
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_f90_unix" != 'no'
then
 AC_DEFINE([HAVE_F90_UNIX],[1],[fortran provides f90_unix module])
fi
])


module ssdc_interface

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,                only : dp
  use nml_governing_equations, only : discretization

  implicit none

  private

  public :: ssdc_init, ssdc_timestep, mask_ssdc
  public :: ssdc_overset_update, ssdc_soln_dump, all_ssdc
  public :: metis_data_ssdc

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
  integer, dimension(:), pointer :: dummy_imesh, dummy_iblank
#endif

  integer, dimension(:), allocatable :: metis_data_ssdc

  real(dp), dimension(:,:),   pointer     :: dummy_turb
  real(dp), dimension(:,:,:), allocatable :: viscous_flux, viscous_flux_atn1

  logical :: all_ssdc  = .false.  ! SSDC is solving entire problem

contains

!============================== SSDC_INIT ====================================80
!
!  Load SSDC with its data
!
!=============================================================================80
  subroutine ssdc_init(filename,grid,soln)

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
    use fluid,                   only : gamma, prandtl
    use info_depr,               only : xmach, alpha, ivisc, tref, re
    use nml_overset_data,        only : overset_flag
    use pundit,                  only : pundit_flag
    use lmpi,                    only : lmpi_max, lmpi_bcast, lmpi_master,     &
                                        lmpi_die
    use allocations,             only : my_alloc_ptr
    use ivals,                   only : rho0, u0, v0, w0, p0
    use kinddefs,                only : dp
    use dirtlib,                 only : nreceptors, receptors, receptor_type
    use turb_parameters,         only : turbulence_exterior
    use nml_global,              only : irest, moving_grid
    use nml_periodicity,         only : periodic_dir, periodic_tol
    use refgeom,                 only : sref
    use nml_governing_equations, only : max_component_grids
#else
    use lmpi,            only : lmpi_master, lmpi_die
#endif

    use grid_types,      only : grid_type
    use solution_types,  only : soln_type

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln

    character(len=80), intent(in) :: filename

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
    integer :: max_grid, itemp, i, dummy_nreceptors

    real(dp), dimension(5) :: freestream

    logical :: viscous_flag, overset_soln, found_ssdc, found_finvol
    logical :: turbulent_flag, read_restart

    type(receptor_type), dimension(:), allocatable :: dummy_receptors
#endif

  continue

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)

! some basic sanity checks and problem setup, then initialize SSDC

    viscous_flag   = ( ivisc > 0 )
    turbulent_flag = ( ivisc > 2 )
    overset_soln   = ( overset_flag .or. pundit_flag )
    read_restart   = ( irest == 1 )

    if ( .not. overset_soln ) all_ssdc = .true.

    freestream(1) = rho0
    freestream(2) = u0
    freestream(3) = v0
    freestream(4) = w0
    freestream(5) = p0

    overset_case : if ( overset_soln ) then

! First determine the number of component grids involved by examining imesh

      max_grid = maxval(grid%imesh)  ! remember this is 0-based

      call lmpi_max(max_grid,itemp)
      max_grid = itemp
      call lmpi_bcast(max_grid)

! Make sure there are not more component grids than allowed by namelist input

      if ( max_grid+1 > max_component_grids ) then
        if ( lmpi_master ) then
          write(*,*) 'More component grids present than supported for SSDC.'
        endif
        call lmpi_die
        stop
      endif

      found_ssdc   = .false.
      found_finvol = .false.

      do i = 1, max_grid+1

        if ( trim(discretization(i)) == 'ssdc' ) found_ssdc = .true.
        if ( trim(discretization(i)) == 'finite-volume' ) found_finvol = .true.

! Make sure each component grid has been assigned a valid discretization scheme

        if ( trim(discretization(i)) /= 'finite-volume' .and.                  &
             trim(discretization(i)) /= 'ssdc' ) then
          if ( lmpi_master ) then
            write(*,*) 'Unknown discretization for grid ', i,                  &
                        trim(discretization(i))
          endif
          call lmpi_die
          stop
        endif

      end do

! If no component grid has been marked for SSDC, then why did we ask for SSDC
! in the first place

      if ( .not. found_ssdc ) then
        if ( lmpi_master ) then
          write(*,*) 'SSDC discretization requested but not assigned to any'
          write(*,*) 'component grids.'
        endif
        call lmpi_die
        stop
      endif

! If multiple schemes are requested, make sure there are at least that
! many component grids found

      if ( found_ssdc .and. found_finvol ) then
        if ( max_grid < 1 ) then
          if ( lmpi_master ) then
            write(*,*) 'Not enough component grids available for number'
            write(*,*) 'of schemes requested.'
          endif
          call lmpi_die
          stop
        endif
      endif

      call ssdc_initiate(filename,grid%nedge,grid%nelem,grid%elem,             &
                         grid%nnodes0,grid%nnodes01,grid%nbound,grid%bc,grid%x,&
                         grid%y,grid%z,gamma,prandtl,xmach,alpha,viscous_flag, &
                         overset_soln,grid%iblank,grid%imesh,                  &
                         max_component_grids,discretization,tref,re,freestream,&
                         nreceptors,receptors,grid%l2g,grid%project,           &
                         turbulent_flag,soln%n_turb,                           &
                         turbulence_exterior(1:soln%n_turb),read_restart,      &
                         moving_grid,soln%n_tot,soln%neq01,soln%q_dof,         &
                         periodic_dir,periodic_tol,sref,grid%nnodesg,          &
                         size(metis_data_ssdc),metis_data_ssdc,all_ssdc)

    else overset_case

! Only one grid present; if we asked to use SSDC, then it must be for the
! entire grid

      discretization(1) = 'ssdc'

      call my_alloc_ptr(dummy_iblank,grid%nnodes01)
      dummy_iblank(:) = 1

      call my_alloc_ptr(dummy_imesh, grid%nnodes01)
      dummy_imesh(:) = 0

      dummy_nreceptors = 1
      allocate(dummy_receptors(dummy_nreceptors))

      call ssdc_initiate(filename,grid%nedge,grid%nelem,grid%elem,             &
                         grid%nnodes0,grid%nnodes01,grid%nbound,grid%bc,grid%x,&
                         grid%y,grid%z,gamma,prandtl,xmach,alpha,viscous_flag, &
                         overset_soln,dummy_iblank,dummy_imesh,                &
                         max_component_grids,discretization,tref,re,freestream,&
                         dummy_nreceptors,dummy_receptors,grid%l2g,            &
                         grid%project,turbulent_flag,soln%n_turb,              &
                         turbulence_exterior(1:soln%n_turb),read_restart,      &
                         moving_grid,soln%n_tot,soln%neq01,soln%q_dof,         &
                         periodic_dir,periodic_tol,sref,grid%nnodesg,          &
                         size(metis_data_ssdc),metis_data_ssdc,all_ssdc)

    endif overset_case

#else

    if ( lmpi_master ) then
      write(*,*) 'This FUN3D executable not linked to SSDC library.'
    endif
    call lmpi_die
    stop
    if ( trim(filename) == '' ) then
    endif
    if ( grid%nnodes0 == 0 ) then
    endif
    if ( soln%eqn_set == 0 ) then
    endif

#endif

  end subroutine ssdc_init


!============================== SSDC_TIMESTEP ================================80
!
!  Execute one timestep of SSDC scheme
!
!=============================================================================80
  subroutine ssdc_timestep(soln,grid,iterations)

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
    use info_depr,               only : simulation_time
    use nml_nonlinear_solves,    only : dt
    use kinddefs,                only : dp
    use lmpi,                    only : lmpi_die
    use nml_governing_equations, only : max_component_grids
#else
    use lmpi,                    only : lmpi_master, lmpi_die
#endif
    use solution_types,          only : soln_type
    use grid_types,              only : grid_type
    use info_depr,               only : ivisc
    use thermo,                  only : etop, ptoe
    use nml_overset_data,        only : overset_flag
    use pundit,                  only : pundit_flag
    use allocations,             only : my_alloc_ptr

    integer, intent(in) :: iterations

    type(grid_type), intent(in) :: grid

    type(soln_type), intent(inout) :: soln

    integer :: ielem, inode, imesh_val

    logical, save :: init = .true.

  continue

! Initialization

    if ( init ) then

      if ( ivisc > 0 .and. (overset_flag .or. pundit_flag) ) then
        do ielem = 1, grid%nelem
          if ( trim(grid%elem(ielem)%type_cell) /= 'tet' ) then
            inode = grid%elem(ielem)%c2n(1,1)
            imesh_val = grid%imesh(inode) + 1 ! shift to 1-based
            if ( trim(discretization(imesh_val)) == 'finite-volume' ) then
              write(*,*) 'ssdc_timestep not set up for non-tet viscous elements'
              call lmpi_die
              stop
            endif
          endif
        end do
      endif

      allocate(viscous_flux     (4,3,soln%neq01))
      allocate(viscous_flux_atn1(4,3,soln%neq01))

      viscous_flux      = 0.0_dp
      viscous_flux_atn1 = 0.0_dp

      if ( soln%n_turb == 0 ) then
        call my_alloc_ptr(dummy_turb, 1, grid%nnodes01)
        dummy_turb(:,:) = 0.0_dp
      endif

      init = .false.

    endif

! Shuffle and compute updated viscous fluxes if needed

    get_visc_fluxes : if ( ivisc > 0 .and. (overset_flag .or. pundit_flag)) then

      viscous_flux_atn1 = viscous_flux

      call etop(grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set)
      call viscous_flux_tet(grid%nnodes0, grid%nnodes01, grid%elem(1)%ncell,   &
                            grid%elem(1)%c2n, grid%x, grid%y, grid%z,          &
                            soln%q_dof, soln%amut, soln%n_tot,                 &
                            grid%vol)
      call ptoe(grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set)

    endif get_visc_fluxes

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
! SSDC time starts at zero

    if ( soln%n_turb > 0 ) then
      if ( overset_flag .or. pundit_flag ) then
        call ssdc_advance_timestep(simulation_time-dt, dt, soln%n_tot,         &
                                   soln%neq01, soln%q_dof, soln%qatn1,         &
                                   viscous_flux, viscous_flux_atn1,            &
                                   max_component_grids, discretization,        &
                                   grid%imesh,soln%n_turb,soln%turb,iterations,&
                                   grid%nelem,grid%elem)
      else
        call ssdc_advance_timestep(simulation_time-dt, dt, soln%n_tot,         &
                                   soln%neq01, soln%q_dof, soln%qatn1,         &
                                   viscous_flux, viscous_flux_atn1,            &
                                   max_component_grids, discretization,        &
                                   dummy_imesh,soln%n_turb,soln%turb,          &
                                   iterations,grid%nelem,grid%elem)
      endif
    else
      if ( overset_flag .or. pundit_flag ) then
        call ssdc_advance_timestep(simulation_time-dt, dt, soln%n_tot,         &
                                   soln%neq01, soln%q_dof, soln%qatn1,         &
                                   viscous_flux, viscous_flux_atn1,            &
                                   max_component_grids, discretization,        &
                                   grid%imesh,1,dummy_turb,iterations,         &
                                   grid%nelem,grid%elem)
      else
        call ssdc_advance_timestep(simulation_time-dt, dt, soln%n_tot,         &
                                   soln%neq01, soln%q_dof, soln%qatn1,         &
                                   viscous_flux, viscous_flux_atn1,            &
                                   max_component_grids, discretization,        &
                                   dummy_imesh,1,dummy_turb,iterations,        &
                                   grid%nelem,grid%elem)
      endif
    endif
#else
    if ( lmpi_master ) then
      write(*,*) 'Executable has not been compiled against the SSDC library.'
    endif
    call lmpi_die
    stop
    if ( soln%dof0 == 0 ) then
    endif
    if ( iterations == 0 ) then
    endif

#endif

  end subroutine ssdc_timestep


!================================ MASK_SSDC ==================================80
!
! Mask nodes that will be solved with SSDC scheme
!
!=============================================================================80
  subroutine mask_ssdc(neq0, neq01, eqn_mask, imesh)

    use nml_overset_data, only : overset_flag
    use pundit,           only : pundit_flag

    integer, intent(in)  :: neq0, neq01

    integer, dimension(neq01), intent(in)  :: imesh
    integer, dimension(neq0),  intent(out) :: eqn_mask

    integer :: i, imesh_val

  continue

    if ( overset_flag .or. pundit_flag ) then

      eqn_mask(:) = 0  ! solve everything with finite-volume by default

      do i = 1, neq0
        imesh_val = imesh(i) + 1 ! shift to 1-based
        if ( trim(discretization(imesh_val)) == 'ssdc' ) eqn_mask(i) = 1
      end do

    else

      eqn_mask(:) = 1  ! solve everything with ssdc

    endif

  end subroutine mask_ssdc


!================================ SSDC_OVERSET_UPDATE ========================80
!
! Tell SSDC to updates its overset information
!
!=============================================================================80
  subroutine ssdc_overset_update(grid)

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
    use dirtlib,                 only : nreceptors, receptors
    use nml_governing_equations, only : max_component_grids
#else
    use lmpi,                    only : lmpi_master, lmpi_die
#endif

    use grid_types,              only : grid_type

    type(grid_type), intent(in) :: grid

  continue

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
    call ssdc_update_overset(grid%nnodes0,grid%nnodes01,grid%nelem,grid%iblank,&
                             grid%imesh,max_component_grids,discretization,    &
                             nreceptors,receptors,grid%elem,grid%x,grid%y,     &
                             grid%z,grid%l2g)
#else
    if ( lmpi_master ) then
      write(*,*) 'Executable has not been compiled against the SSDC library.'
    endif
    call lmpi_die
    stop
    if ( grid%nnodes0 == 0 ) then
    endif
#endif

  end subroutine ssdc_overset_update


!================================ SSDC_OVERSET_UPDATE ========================80
!
! Tell SSDC to dump its solution
!
!=============================================================================80
  subroutine ssdc_soln_dump(iterations)

#if !defined(HAVE_SSDC) || !defined(HAVE_SPARSKIT)
    use lmpi, only : lmpi_master, lmpi_die
#endif

    integer, intent(in) :: iterations

  continue

#if defined(HAVE_SSDC) && defined(HAVE_SPARSKIT)
    call ssdc_write_soln(iterations)
#else
    if ( lmpi_master ) then
      write(*,*) 'Executable has not been compiled against the SSDC library.'
    endif
    if ( .false. ) write(*,*) iterations
    call lmpi_die
    stop
#endif

  end subroutine ssdc_soln_dump


!========================= VISCOUS_FLUX_TET ==================================80
!
! Form volume-weighted viscous flux vector at nodes for tet elements
!
! Note this includes the conversion from FUN3D to SSDC nondimensionalization
!
!=============================================================================80
  subroutine viscous_flux_tet(nnodes0, nnodes01, ncell, c2n, x, y, z, qnode,   &
                              amut, n_tot, volume)

    use info_depr,       only : tref, xmach, re, ivgrd
    use fluid,           only : gamma, gm1, sutherland_constant, prandtl
    use turb_parameters, only : turbulent_prandtl

    integer,                                    intent(in)    :: n_tot
    integer,                                    intent(in)    :: nnodes0
    integer,                                    intent(in)    :: nnodes01
    integer,                                    intent(in)    :: ncell

    integer,      dimension(4,ncell),           intent(in)    :: c2n

    real(dp),  dimension(nnodes01),             intent(in)    :: x, y, z
    real(dp),  dimension(:),                    intent(in)    :: volume
    real(dp),  dimension(n_tot,nnodes01),       intent(in)    :: qnode
    real(dp),  dimension(nnodes01),             intent(in)    :: amut

    integer     :: n, node1, node2, node3, node4

    real(dp)    :: t1,t2,t3,t4,eta,factor,factor2
    real(dp)    :: const,dot
    real(dp)    :: nx1,nx2,nx3,nx4
    real(dp)    :: ny1,ny2,ny3,ny4,nz1,nz2,nz3,nz4,rmu,rmu1,rmu2
    real(dp)    :: rmu3,rmu4,u1,u2,u3,u4,usize1,usize2,usize3
    real(dp)    :: usize4,ux,ux1,ux2,ux3,ux4,uy,uy1,uy2,uy3,uy4
    real(dp)    :: uz,uz1,uz2,uz3,uz4,v1,v2,v3,v4,vol,vx
    real(dp)    :: vy,vz,w1,w2,w3,w4,wx,wy,wz,x1,x2,x3,x4
    real(dp)    :: y1,y2,y3,y4,z1,z2,z3,z4
    real(dp)    :: phi0, dual_vol, vol_wt
    real(dp)    :: tx, ty, tz, tqx, tqy, tqz
    real(dp)    :: txx, txy, txz, tyx, tyy, tyz, tzx, tzy, tzz

    real(dp), parameter :: my_4th = 0.25_dp
    real(dp), parameter :: my_mxd = 0.99939_dp
    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_4   = 4.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
    real(dp), parameter :: my_6th = my_1/my_6
    real(dp), parameter :: c43    = my_4/my_3
    real(dp), parameter :: c23    = my_2/my_3

    real(dp), save :: cstar, xmr, cgp, cgpt
    real(dp), save :: umu, vmu, wmu

    logical, save :: init = .true.

  continue

    viscous_flux = 0.0_dp

!   Allocate space for working arrays on first pass only

    if (init) then
      init = .false.
      cstar = sutherland_constant/tref
      xmr   = xmach/re
      cgp   = my_1/(gm1*prandtl)
      cgpt  = my_1/(gm1*turbulent_prandtl)
      umu   = my_0
      vmu   = my_0
      wmu   = my_0
    end if

    factor  = 1.0_dp/xmach
    factor2 = 1.0_dp/xmach/xmach

!   Loop over all the cells and calculate viscous flux

      cell_loop: do n = 1, ncell

        node1 = c2n(1,n)
        node2 = c2n(2,n)
        node3 = c2n(3,n)
        node4 = c2n(4,n)

        x1 = x(node1)
        x2 = x(node2)
        x3 = x(node3)
        x4 = x(node4)

        y1 = y(node1)
        y2 = y(node2)
        y3 = y(node3)
        y4 = y(node4)

        z1 = z(node1)
        z2 = z(node2)
        z3 = z(node3)
        z4 = z(node4)

!       Lets get outward normals

        nx1 = my_6th*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
        ny1 = my_6th*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
        nz1 = my_6th*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

        nx2 = my_6th*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
        ny2 = my_6th*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
        nz2 = my_6th*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

        nx3 = my_6th*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
        ny3 = my_6th*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
        nz3 = my_6th*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

        nx4 = -nx1 -nx2 -nx3
        ny4 = -ny1 -ny2 -ny3
        nz4 = -nz1 -nz2 -nz3

        if (ivgrd == 1) then

!         Normalize the normals and compute dot products. If an angle
!         is detected that is bigger than 178 degrees, try ignoring
!         the contribution from this cell

          usize1 = my_1/sqrt(nx1*nx1 + ny1*ny1 + nz1*nz1)
          ux1 = nx1*usize1
          uy1 = ny1*usize1
          uz1 = nz1*usize1
          usize2 = my_1/sqrt(nx2*nx2 + ny2*ny2 + nz2*nz2)
          ux2 = nx2*usize2
          uy2 = ny2*usize2
          uz2 = nz2*usize2
          usize3 = my_1/sqrt(nx3*nx3 + ny3*ny3 + nz3*nz3)
          ux3 = nx3*usize3
          uy3 = ny3*usize3
          uz3 = nz3*usize3
          usize4 = my_1/sqrt(nx4*nx4 + ny4*ny4 + nz4*nz4)
          ux4 = nx4*usize4
          uy4 = ny4*usize4
          uz4 = nz4*usize4

          dot = ux1*ux2 + uy1*uy2 + uz1*uz2
          if (dot >= my_mxd) cycle
          dot = ux1*ux3 + uy1*uy3 + uz1*uz3
          if (dot >= my_mxd) cycle
          dot = ux1*ux4 + uy1*uy4 + uz1*uz4
          if (dot >= my_mxd) cycle
          dot = ux2*ux3 + uy2*uy3 + uz2*uz3
          if (dot >= my_mxd) cycle
          dot = ux2*ux4 + uy2*uy4 + uz2*uz4
          if (dot >= my_mxd) cycle
          dot = ux3*ux4 + uy3*uy4 + uz3*uz4
          if (dot >= my_mxd) cycle

        end if

!       Compute cell volume

        vol = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                     &
              -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                     &
              +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))*my_6th

! contribution to dual volume is 1/4th of this

        dual_vol = 0.25_dp*vol

!       Compute cell averaged quantities

        u1 = qnode(2,node1)
        v1 = qnode(3,node1)
        w1 = qnode(4,node1)
        u2 = qnode(2,node2)
        v2 = qnode(3,node2)
        w2 = qnode(4,node2)
        u3 = qnode(2,node3)
        v3 = qnode(3,node3)
        w3 = qnode(4,node3)
        u4 = qnode(2,node4)
        v4 = qnode(3,node4)
        w4 = qnode(4,node4)

        t1 = gamma*qnode(5,node1)/qnode(1,node1)
        t2 = gamma*qnode(5,node2)/qnode(1,node2)
        t3 = gamma*qnode(5,node3)/qnode(1,node3)
        t4 = gamma*qnode(5,node4)/qnode(1,node4)
        rmu1 = viscosity_law( cstar, t1 )
        rmu2 = viscosity_law( cstar, t2 )
        rmu3 = viscosity_law( cstar, t3 )
        rmu4 = viscosity_law( cstar, t4 )
        eta  = my_4th*( cgp *(rmu1 + rmu2                                      &
                            + rmu3 + rmu4)                                     &
                      + cgpt *(amut(node1) + amut(node2)                       &
                             + amut(node3) + amut(node4)) )
        rmu1 = rmu1 + amut(node1)
        rmu2 = rmu2 + amut(node2)
        rmu3 = rmu3 + amut(node3)
        rmu4 = rmu4 + amut(node4)
        rmu  = my_4th*(rmu1 + rmu2 + rmu3 + rmu4)
        umu  = my_4th*(u1*rmu1 + u2*rmu2 + u3*rmu3 + u4*rmu4)
        vmu  = my_4th*(v1*rmu1 + v2*rmu2 + v3*rmu3 + v4*rmu4)
        wmu  = my_4th*(w1*rmu1 + w2*rmu2 + w3*rmu3 + w4*rmu4)
        phi0 = xmr

!       Compute Gradients

        const = phi0/vol
        ux = -((u4 - u1)*nx4 + (u2 - u1)*nx2 + (u3 - u1)*nx3)*const
        vx = -((v4 - v1)*nx4 + (v2 - v1)*nx2 + (v3 - v1)*nx3)*const
        wx = -((w4 - w1)*nx4 + (w2 - w1)*nx2 + (w3 - w1)*nx3)*const
        tx = -((t4 - t1)*nx4 + (t2 - t1)*nx2 + (t3 - t1)*nx3)*const

        uy = -((u4 - u1)*ny4 + (u2 - u1)*ny2 + (u3 - u1)*ny3)*const
        vy = -((v4 - v1)*ny4 + (v2 - v1)*ny2 + (v3 - v1)*ny3)*const
        wy = -((w4 - w1)*ny4 + (w2 - w1)*ny2 + (w3 - w1)*ny3)*const
        ty = -((t4 - t1)*ny4 + (t2 - t1)*ny2 + (t3 - t1)*ny3)*const

        uz = -((u4 - u1)*nz4 + (u2 - u1)*nz2 + (u3 - u1)*nz3)*const
        vz = -((v4 - v1)*nz4 + (v2 - v1)*nz2 + (v3 - v1)*nz3)*const
        wz = -((w4 - w1)*nz4 + (w2 - w1)*nz2 + (w3 - w1)*nz3)*const
        tz = -((t4 - t1)*nz4 + (t2 - t1)*nz2 + (t3 - t1)*nz3)*const

!       Update residual

        txx = factor*rmu*(c43*ux - c23*vy - c23*wz)
        txy = factor*rmu*(uy + vx)
        txz = factor*rmu*(uz + wx)

        tyx = factor*rmu*(uy + vx)
        tyy = factor*rmu*(c43*vy - c23*ux - c23*wz)
        tyz = factor*rmu*(vz + wy)

        tzx = factor*rmu*(uz + wx)
        tzy = factor*rmu*(vz + wy)
        tzz = factor*rmu*(c43*wz - c23*ux - c23*vy)

        tqx = eta*tx
        tqy = eta*ty
        tqz = eta*tz

        tqx = tqx + factor2*(umu*(c43*ux - c23*vy - c23*wz)+vmu*(uy + vx)+     &
                    wmu*(uz + wx))
        tqy = tqy + factor2*(umu*(uy + vx)+vmu*(c43*vy - c23*ux - c23*wz)+     &
                    wmu*(vz + wy))
        tqz = tqz + factor2*(umu*(uz + wx)+vmu*(vz + wy)+                      &
                    wmu*(c43*wz - c23*ux - c23*vy))

! Store volume-weighted flux vectors

        if (node1 <= nnodes0) then

          vol_wt = dual_vol/volume(node1)

          viscous_flux(1,1,node1) = viscous_flux(1,1,node1) - vol_wt*txx
          viscous_flux(1,2,node1) = viscous_flux(1,2,node1) - vol_wt*txy
          viscous_flux(1,3,node1) = viscous_flux(1,3,node1) - vol_wt*txz

          viscous_flux(2,1,node1) = viscous_flux(2,1,node1) - vol_wt*tyx
          viscous_flux(2,2,node1) = viscous_flux(2,2,node1) - vol_wt*tyy
          viscous_flux(2,3,node1) = viscous_flux(2,3,node1) - vol_wt*tyz

          viscous_flux(3,1,node1) = viscous_flux(3,1,node1) - vol_wt*tzx
          viscous_flux(3,2,node1) = viscous_flux(3,2,node1) - vol_wt*tzy
          viscous_flux(3,3,node1) = viscous_flux(3,3,node1) - vol_wt*tzz

          viscous_flux(4,1,node1) = viscous_flux(4,1,node1) - vol_wt*tqx
          viscous_flux(4,2,node1) = viscous_flux(4,2,node1) - vol_wt*tqy
          viscous_flux(4,3,node1) = viscous_flux(4,3,node1) - vol_wt*tqz
        end if

        if (node2 <= nnodes0) then

          vol_wt = dual_vol/volume(node2)

          viscous_flux(1,1,node2) = viscous_flux(1,1,node2) - vol_wt*txx
          viscous_flux(1,2,node2) = viscous_flux(1,2,node2) - vol_wt*txy
          viscous_flux(1,3,node2) = viscous_flux(1,3,node2) - vol_wt*txz

          viscous_flux(2,1,node2) = viscous_flux(2,1,node2) - vol_wt*tyx
          viscous_flux(2,2,node2) = viscous_flux(2,2,node2) - vol_wt*tyy
          viscous_flux(2,3,node2) = viscous_flux(2,3,node2) - vol_wt*tyz

          viscous_flux(3,1,node2) = viscous_flux(3,1,node2) - vol_wt*tzx
          viscous_flux(3,2,node2) = viscous_flux(3,2,node2) - vol_wt*tzy
          viscous_flux(3,3,node2) = viscous_flux(3,3,node2) - vol_wt*tzz

          viscous_flux(4,1,node2) = viscous_flux(4,1,node2) - vol_wt*tqx
          viscous_flux(4,2,node2) = viscous_flux(4,2,node2) - vol_wt*tqy
          viscous_flux(4,3,node2) = viscous_flux(4,3,node2) - vol_wt*tqz
        end if

        if (node3 <= nnodes0) then

          vol_wt = dual_vol/volume(node3)

          viscous_flux(1,1,node3) = viscous_flux(1,1,node3) - vol_wt*txx
          viscous_flux(1,2,node3) = viscous_flux(1,2,node3) - vol_wt*txy
          viscous_flux(1,3,node3) = viscous_flux(1,3,node3) - vol_wt*txz

          viscous_flux(2,1,node3) = viscous_flux(2,1,node3) - vol_wt*tyx
          viscous_flux(2,2,node3) = viscous_flux(2,2,node3) - vol_wt*tyy
          viscous_flux(2,3,node3) = viscous_flux(2,3,node3) - vol_wt*tyz

          viscous_flux(3,1,node3) = viscous_flux(3,1,node3) - vol_wt*tzx
          viscous_flux(3,2,node3) = viscous_flux(3,2,node3) - vol_wt*tzy
          viscous_flux(3,3,node3) = viscous_flux(3,3,node3) - vol_wt*tzz

          viscous_flux(4,1,node3) = viscous_flux(4,1,node3) - vol_wt*tqx
          viscous_flux(4,2,node3) = viscous_flux(4,2,node3) - vol_wt*tqy
          viscous_flux(4,3,node3) = viscous_flux(4,3,node3) - vol_wt*tqz
        end if

        if (node4 <= nnodes0) then

          vol_wt = dual_vol/volume(node4)

          viscous_flux(1,1,node4) = viscous_flux(1,1,node4) - vol_wt*txx
          viscous_flux(1,2,node4) = viscous_flux(1,2,node4) - vol_wt*txy
          viscous_flux(1,3,node4) = viscous_flux(1,3,node4) - vol_wt*txz

          viscous_flux(2,1,node4) = viscous_flux(2,1,node4) - vol_wt*tyx
          viscous_flux(2,2,node4) = viscous_flux(2,2,node4) - vol_wt*tyy
          viscous_flux(2,3,node4) = viscous_flux(2,3,node4) - vol_wt*tyz

          viscous_flux(3,1,node4) = viscous_flux(3,1,node4) - vol_wt*tzx
          viscous_flux(3,2,node4) = viscous_flux(3,2,node4) - vol_wt*tzy
          viscous_flux(3,3,node4) = viscous_flux(3,3,node4) - vol_wt*tzz

          viscous_flux(4,1,node4) = viscous_flux(4,1,node4) - vol_wt*tqx
          viscous_flux(4,2,node4) = viscous_flux(4,2,node4) - vol_wt*tqy
          viscous_flux(4,3,node4) = viscous_flux(4,3,node4) - vol_wt*tqz
        end if

      end do cell_loop

  end subroutine viscous_flux_tet

  include 'viscosity_law.f90'

end module ssdc_interface

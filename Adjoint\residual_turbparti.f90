module residual_turbparti

  implicit none

  private

  public :: turbparti, fillturbi

contains

!================================= TURBPARTI =================================80
!
!                                 t
!  This routine computes the dR/dQ * lambda contribution from the
!  turbulence model for the residual
!
!=============================================================================80
  subroutine turbparti(nnodes0,nnodes01,x,y,z,qnode,gradx,grady,gradz,turb,    &
                       slen,vol,rlam,coltag,res,nedge,nedgeLoc,eptr,ra,xn,yn,  &
                       zn,dft1,dft2,nbound,bc,nfunctions,ndim,n_turb,adim,     &
                       facespeed,eqn_set,n_q,dxdt,dydt,dzdt,elem,nelem,symmetry)

    use bc_types,          only : bcgrid_type
    use kinddefs,          only : dp
    use element_types,     only : elem_type
    use residual_turbpart, only : bc_conv_turb
    use info_depr,         only : mixed
    use adjoint_switches,  only : use_bp_model

    integer, intent(in) :: nnodes0,nfunctions,n_turb,ndim,adim,eqn_set,n_q
    integer, intent(in) :: nedge,nedgeloc,nbound,nnodes01,nelem

    integer, dimension(:),       intent(in) :: symmetry
    integer, dimension(2,nedge), intent(in) :: eptr

    real(dp), dimension(nnodes01),                  intent(in)    :: x,y,z
    real(dp), dimension(nnodes01),                  intent(in)    :: dxdt
    real(dp), dimension(nnodes01),                  intent(in)    :: dydt
    real(dp), dimension(nnodes01),                  intent(in)    :: dzdt
    real(dp), dimension(ndim,nnodes01),             intent(in)    :: qnode
    real(dp), dimension(ndim,nnodes01),             intent(in)    :: gradx
    real(dp), dimension(ndim,nnodes01),             intent(in)    :: grady
    real(dp), dimension(ndim,nnodes01),             intent(in)    :: gradz
    real(dp), dimension(nnodes01),                  intent(in)    :: slen,vol
    real(dp), dimension(n_turb,nnodes01),           intent(in)    :: turb
    real(dp), dimension(adim,nnodes01, nfunctions), intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),             intent(in)    :: coltag
    real(dp), dimension(nedge),                     intent(in)    :: ra
    real(dp), dimension(nedge),                     intent(in)    :: xn,yn,zn
    real(dp), dimension(nedge),                     intent(in)    :: facespeed
    real(dp), dimension(nedge),                     intent(in)    :: dft1
    real(dp), dimension(nedge),                     intent(in)    :: dft2
    real(dp), dimension(adim,nnodes01, nfunctions), intent(inout) :: res

    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(elem_type),   dimension(:),      intent(in) :: elem

    integer :: i

  continue

    call turbpart_convectioni(nnodes0,nnodes01,n_turb,nfunctions,adim,ndim,    &
                              nedge,nedgeloc,eptr,turb,xn,yn,zn,ra,facespeed,  &
                              qnode,res=res,coltag=coltag,rlam=rlam)

    if ( mixed .or. use_bp_model ) then
      do i = 1, nelem
        call turbpart_mixed_diffusioni(nnodes0,nnodes01,elem(i)%ncell,         &
                                       elem(i)%c2n,x,y,z,elem(i)%local_f2n,    &
                                       elem(i)%local_e2n,elem(i)%e2n_2d,       &
                                       elem(i)%face_per_cell,                  &
                                       elem(i)%node_per_cell,                  &
                                       elem(i)%edge_per_cell,elem(i)%type_cell,&
                                       adim,nfunctions,n_turb,turb,            &
                                       elem(i)%chk_norm,                       &
                                       coltag=coltag,rlam=rlam,res=res)
      end do
    else
      call turbpart_tet_diffusioni(nnodes0,nnodes01,x,y,z,turb,elem(1)%ncell,  &
                                   elem(1)%c2n,elem(1)%c2e,nedge,nedgeLoc,eptr,&
                                   dft1,dft2,nfunctions,n_turb,rlamb=rlam,     &
                                   res=res,coltag=coltag)
    endif

    call turbpart_sourcei(nnodes0,nnodes01,gradx,grady,gradz,turb,slen,vol,    &
                          nedge,nedgeLoc,eptr,ra,xn,yn,zn,nfunctions,ndim,adim,&
                          n_turb,coltag,symmetry,rlam=rlam,res=res)

    call bc_turbpart_sourcei(nnodes0,nnodes01,x,y,z,gradx,grady,gradz,turb,    &
                             slen,vol,nbound,bc,nfunctions,ndim,adim,n_turb,   &
                             coltag,elem,dxdt,dydt,dzdt,symmetry,rlam=rlam,    &
                             res=res)

    call bc_conv_turb(nbound,qnode,turb,eqn_set,n_q,n_turb,bc,x,y,z,nfunctions,&
                      nnodes0,dxdt,dydt,dzdt,elem,rlam=rlam,res=res,           &
                      coltag=coltag)

  end subroutine turbparti


!================================= FILLTURBI =================================80
!
!                                 t
! This routine computes the dR/dQ * lambda contribution from the
! turbulence model for the residual
!
!=============================================================================80
  subroutine fillturbi(nnodes01,nnodes0,x,y,z,vol,qnode,gradx,grady,gradz,slen,&
                       coltag,turb,nedge,nedgeLoc,eptr,xn,yn,zn,ra,dft1,dft2,  &
                       nnz,A,iau,fhelp,nbound,bc,ndim,adim,n_turb,facespeed,   &
                       eqn_set,n_q,nfunctions,dxdt,dydt,dzdt,elem,ia,ja,nelem, &
                       symmetry)

    use kinddefs,          only : dp
    use bc_types,          only : bcgrid_type
    use element_types,     only : elem_type
    use residual_turbpart, only : bc_conv_turb
    use info_depr,         only : mixed
    use adjoint_switches,  only : use_bp_model

    integer, intent(in) :: nnodes01, nnodes0, ndim, adim, n_turb, eqn_set, nelem
    integer, intent(in) :: nfunctions, nedge, nedgeloc, nnz, nbound, n_q

    integer, dimension(2,nedge),  intent(in) :: eptr
    integer, dimension(2,nedge),  intent(in) :: fhelp
    integer, dimension(nnodes01), intent(in) :: iau
    integer, dimension(:),        intent(in) :: ia, ja, symmetry

    real(dp), dimension(nnodes01),        intent(in) :: dxdt, dydt, dzdt
    real(dp), dimension(nnodes01),        intent(in) :: x, y, z, vol
    real(dp), dimension(ndim,nnodes01),   intent(in) :: qnode
    real(dp), dimension(ndim,nnodes01),   intent(in) :: gradx, grady, gradz
    real(dp), dimension(nnodes01),        intent(in) :: slen
    real(dp), dimension(adim,nnodes01),   intent(in) :: coltag
    real(dp), dimension(n_turb,nnodes01), intent(in) :: turb
    real(dp), dimension(nedge),           intent(in) :: xn,yn,zn,ra
    real(dp), dimension(nedge),           intent(in) :: facespeed
    real(dp), dimension(nedge),           intent(in) :: dft1, dft2
    real(dp), dimension(adim,adim,nnz),   intent(inout) :: A

    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(elem_type),   dimension(:),      intent(in) :: elem

    integer :: i

  continue

    call turbpart_convectioni(nnodes0,nnodes01,n_turb,nfunctions,adim,ndim,    &
                              nedge,nedgeloc,eptr,turb,xn,yn,zn,ra,facespeed,  &
                              qnode,iau=iau,fhelp=fhelp,a=a)

    if ( mixed .or. use_bp_model ) then
      do i = 1, nelem
        call turbpart_mixed_diffusioni(nnodes0,nnodes01,elem(i)%ncell,         &
                                       elem(i)%c2n,x,y,z,elem(i)%local_f2n,    &
                                       elem(i)%local_e2n,elem(i)%e2n_2d,       &
                                       elem(i)%face_per_cell,                  &
                                       elem(i)%node_per_cell,                  &
                                       elem(i)%edge_per_cell,elem(i)%type_cell,&
                                       adim,nfunctions,n_turb,turb,            &
                                       elem(i)%chk_norm,                       &
                                       ia=ia,ja=ja,iau=iau,aa=a)
      end do
    else
      call turbpart_tet_diffusioni(nnodes0,nnodes01,x,y,z,turb,elem(1)%ncell,  &
                                   elem(1)%c2n,elem(1)%c2e,nedge,nedgeLoc,eptr,&
                                   dft1,dft2,nfunctions,n_turb,iau=iau,        &
                                   fhelp=fhelp,a=a)
    endif

    call turbpart_sourcei(nnodes0,nnodes01,gradx,grady,gradz,turb,slen,vol,    &
                          nedge,nedgeLoc,eptr,ra,xn,yn,zn,nfunctions,ndim,adim,&
                          n_turb,coltag,symmetry,iau=iau,fhelp=fhelp,a=a)

    call bc_turbpart_sourcei(nnodes0,nnodes01,x,y,z,gradx,grady,gradz,turb,    &
                             slen,vol,nbound,bc,nfunctions,ndim,adim,n_turb,   &
                             coltag,elem,dxdt,dydt,dzdt,symmetry,ia=ia,ja=ja,  &
                             iau=iau,fhelp=fhelp,a=a)

    call bc_conv_turb(nbound,qnode,turb,eqn_set,n_q,n_turb,bc,x,y,z,nfunctions,&
                      nnodes0,dxdt,dydt,dzdt,elem,a=a,iau=iau)

  end subroutine fillturbi


!====================== TURBPART_CONVECTIONI =================================80
!
!                                 t
!  This routine computes the dR/dQ * lambda contribution from the
!  turbulence model for the residual
!
!   convection pieces
!
!=============================================================================80
  subroutine turbpart_convectioni(nnodes0,nnodes01,n_turb,nfunctions,adim,ndim,&
                                  nedge,nedgeloc,eptr,turb,xn,yn,zn,ra,        &
                                  facespeed,qnode,res,coltag,rlam,iau,fhelp,a)

    use kinddefs,            only : dp
    use grid_motion_helpers, only : need_grid_velocity
    use turb_parameters,     only : ubar_eps
    use design_types,        only : max_functions
    use lmpi,                only : lmpi_master, lmpi_die

    integer, intent(in) :: nnodes0,nnodes01,n_turb,nfunctions,adim,nedge
    integer, intent(in) :: nedgeloc,ndim

    integer, dimension(2,nedge), intent(in) :: eptr
    integer, dimension(:),       intent(in), optional :: iau
    integer, dimension(:,:),     intent(in), optional :: fhelp

    real(dp), dimension(ndim,nnodes01),   intent(in) :: qnode
    real(dp), dimension(nedgeloc),        intent(in) :: facespeed
    real(dp), dimension(nedge),           intent(in) :: ra
    real(dp), dimension(nedge),           intent(in) :: xn,yn,zn
    real(dp), dimension(n_turb,nnodes01), intent(in) :: turb
    real(dp), dimension(adim,nnodes01),   intent(in),    optional :: coltag
    real(dp), dimension(:,:,:),           intent(in),    optional :: rlam
    real(dp), dimension(:,:,:),           intent(inout), optional :: res
    real(dp), dimension(:,:,:),           intent(inout), optional :: a

    integer :: i,node1,node2,n,idiag,ioff

    real(dp) :: u,v,w,ubar,area
    real(dp) :: ubarq11,ubarq12,ubarq13,ubarq14
    real(dp) :: ubarq21,ubarq22,ubarq23,ubarq24
    real(dp) :: uplus
    real(dp) :: uplusq11,uplusq12,uplusq13,uplusq14
    real(dp) :: uplusq21,uplusq22,uplusq23,uplusq24
    real(dp) :: uminus
    real(dp) :: uminusq11,uminusq12,uminusq13,uminusq14
    real(dp) :: uminusq21,uminusq22,uminusq23,uminusq24
    real(dp) :: res1q11,res1q12,res1q13,res1q14,res1t1
    real(dp) :: res1q21,res1q22,res1q23,res1q24,res1t2
    real(dp) :: res2q11,res2q12,res2q13,res2q14,res2t1
    real(dp) :: res2q21,res2q22,res2q23,res2q24,res2t2
    real(dp) :: face_speed
    real(dp) :: xnorm,ynorm,znorm

    real(dp), dimension(max_functions) :: rlam15,rlam25

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) ) then
        if ( lmpi_master ) then
          write(*,*)'res requested in turbpart_convection but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if (.not.present(iau) .or. .not.present(fhelp)) then
        if ( lmpi_master ) then
          write(*,*)'a requested in turbpart_convection but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    edge_loop_1060 : do n = 1, nedgeloc
      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Unit normal to face and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

!     Dual face speed

      face_speed = 0.0_dp

      if ( need_grid_velocity ) then
        face_speed = facespeed(n)
      endif

! First node
! Convective part

      u      = qnode(2,node1)
      v      = qnode(3,node1)
      w      = qnode(4,node1)
      ubar   = xnorm*u + ynorm*v + znorm*w - face_speed
      ubarq11 = 0.0_dp
      ubarq12 = xnorm
      ubarq13 = ynorm
      ubarq14 = znorm

      ubarq21 = 0.0_dp
      ubarq22 = 0.0_dp
      ubarq23 = 0.0_dp
      ubarq24 = 0.0_dp

      uplus  = 0.5_dp*( ubar + aharten(ubar,ubar_eps) )
        uplusq11 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq11
        uplusq12 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq12
        uplusq13 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq13
        uplusq14 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq14

        uplusq21 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq21
        uplusq22 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq22
        uplusq23 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq23
        uplusq24 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq24

      uminus  = 0.5_dp*( ubar - aharten(ubar,ubar_eps) )
        uminusq11 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq11
        uminusq12 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq12
        uminusq13 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq13
        uminusq14 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq14

        uminusq21 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq21
        uminusq22 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq22
        uminusq23 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq23
        uminusq24 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq24

!  Remember we are just doing the convective piece here

      res1q11 = (uplusq11*turb(1,node1)+uminusq11*turb(1,node2))*area
      res1q12 = (uplusq12*turb(1,node1)+uminusq12*turb(1,node2))*area
      res1q13 = (uplusq13*turb(1,node1)+uminusq13*turb(1,node2))*area
      res1q14 = (uplusq14*turb(1,node1)+uminusq14*turb(1,node2))*area
      res1t1  =  uplus*area

      res1q21 = (uplusq21*turb(1,node1)+uminusq21*turb(1,node2))*area
      res1q22 = (uplusq22*turb(1,node1)+uminusq22*turb(1,node2))*area
      res1q23 = (uplusq23*turb(1,node1)+uminusq23*turb(1,node2))*area
      res1q24 = (uplusq24*turb(1,node1)+uminusq24*turb(1,node2))*area
      res1t2  =  uminus*area

!  Add contributions to mat-vec product

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam15(i) = coltag(5,node1)*rlam(5,node1,i)

          if(node1 <= nnodes0) then
            res(1,node1,i) = res(1,node1,i) + res1q11*rlam15(i)
            res(2,node1,i) = res(2,node1,i) + res1q12*rlam15(i)
            res(3,node1,i) = res(3,node1,i) + res1q13*rlam15(i)
            res(4,node1,i) = res(4,node1,i) + res1q14*rlam15(i)
            res(5,node1,i) = res(5,node1,i) + res1t1 *rlam15(i)
          endif

          if(node2 <= nnodes0) then
            res(1,node2,i) = res(1,node2,i) + res1q21*rlam15(i)
            res(2,node2,i) = res(2,node2,i) + res1q22*rlam15(i)
            res(3,node2,i) = res(3,node2,i) + res1q23*rlam15(i)
            res(4,node2,i) = res(4,node2,i) + res1q24*rlam15(i)
            res(5,node2,i) = res(5,node2,i) + res1t2 *rlam15(i)
          endif
        end do
      endif

      if ( fill_a ) then
        idiag = iau(node1)
        ioff  = fhelp(2,n)

        if(node1 <= nnodes0) then
          A(5,1,idiag) = A(5,1,idiag) + res1q11
          A(5,2,idiag) = A(5,2,idiag) + res1q12
          A(5,3,idiag) = A(5,3,idiag) + res1q13
          A(5,4,idiag) = A(5,4,idiag) + res1q14
          A(5,5,idiag) = A(5,5,idiag) + res1t1
        endif

        if(node2 <= nnodes0) then
          A(5,1,ioff) = A(5,1,ioff) + res1q21
          A(5,2,ioff) = A(5,2,ioff) + res1q22
          A(5,3,ioff) = A(5,3,ioff) + res1q23
          A(5,4,ioff) = A(5,4,ioff) + res1q24
          A(5,5,ioff) = A(5,5,ioff) + res1t2
        endif
      endif

! Now do the other node (note that ubar pts in other direction. This
! is because I'm too stupid to work out all the sign changes but
! I need an outward pointing normal for the convective stuff

      u      = qnode(2,node2)
      v      = qnode(3,node2)
      w      = qnode(4,node2)
      ubar   = -xnorm*u - ynorm*v - znorm*w + face_speed
      ubarq11 = 0.0_dp
      ubarq12 = 0.0_dp
      ubarq13 = 0.0_dp
      ubarq14 = 0.0_dp

      ubarq21 = 0.0_dp
      ubarq22 = -xnorm
      ubarq23 = -ynorm
      ubarq24 = -znorm

      uplus  = 0.5_dp*( ubar + aharten(ubar,ubar_eps) )
        uplusq11 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq11
        uplusq12 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq12
        uplusq13 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq13
        uplusq14 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq14

        uplusq21 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq21
        uplusq22 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq22
        uplusq23 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq23
        uplusq24 = 0.5_dp*( 1.0_dp + daharten(ubar,ubar_eps) ) * ubarq24

      uminus  = 0.5_dp*( ubar - aharten(ubar,ubar_eps) )
        uminusq11 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq11
        uminusq12 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq12
        uminusq13 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq13
        uminusq14 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq14

        uminusq21 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq21
        uminusq22 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq22
        uminusq23 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq23
        uminusq24 = 0.5_dp*( 1.0_dp - daharten(ubar,ubar_eps) ) * ubarq24

!  Remember we are just doing the convective piece here

      res2q11 = (uplusq11*turb(1,node2)+uminusq11*turb(1,node1))*area
      res2q12 = (uplusq12*turb(1,node2)+uminusq12*turb(1,node1))*area
      res2q13 = (uplusq13*turb(1,node2)+uminusq13*turb(1,node1))*area
      res2q14 = (uplusq14*turb(1,node2)+uminusq14*turb(1,node1))*area
      res2t1  =  uminus*area

      res2q21 = (uplusq21*turb(1,node2)+uminusq21*turb(1,node1))*area
      res2q22 = (uplusq22*turb(1,node2)+uminusq22*turb(1,node1))*area
      res2q23 = (uplusq23*turb(1,node2)+uminusq23*turb(1,node1))*area
      res2q24 = (uplusq24*turb(1,node2)+uminusq24*turb(1,node1))*area
      res2t2  =  uplus*area

!  Add contributions to mat-vec product

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam25(i) = coltag(5,node2)*rlam(5,node2,i)

          if(node1 <= nnodes0) then
            res(1,node1,i) = res(1,node1,i) + res2q11*rlam25(i)
            res(2,node1,i) = res(2,node1,i) + res2q12*rlam25(i)
            res(3,node1,i) = res(3,node1,i) + res2q13*rlam25(i)
            res(4,node1,i) = res(4,node1,i) + res2q14*rlam25(i)
            res(5,node1,i) = res(5,node1,i) + res2t1 *rlam25(i)
          endif

          if(node2 <= nnodes0) then
            res(1,node2,i) = res(1,node2,i) + res2q21*rlam25(i)
            res(2,node2,i) = res(2,node2,i) + res2q22*rlam25(i)
            res(3,node2,i) = res(3,node2,i) + res2q23*rlam25(i)
            res(4,node2,i) = res(4,node2,i) + res2q24*rlam25(i)
            res(5,node2,i) = res(5,node2,i) + res2t2 *rlam25(i)
          endif
        end do
      endif

      if ( fill_a ) then
        idiag = iau(node2)
        ioff  = fhelp(1,n)

        if(node2 <= nnodes0) then
          A(5,1,idiag) = A(5,1,idiag) + res2q21
          A(5,2,idiag) = A(5,2,idiag) + res2q22
          A(5,3,idiag) = A(5,3,idiag) + res2q23
          A(5,4,idiag) = A(5,4,idiag) + res2q24
          A(5,5,idiag) = A(5,5,idiag) + res2t2
        endif

        if(node1 <= nnodes0) then
          A(5,1,ioff) = A(5,1,ioff) + res2q11
          A(5,2,ioff) = A(5,2,ioff) + res2q12
          A(5,3,ioff) = A(5,3,ioff) + res2q13
          A(5,4,ioff) = A(5,4,ioff) + res2q14
          A(5,5,ioff) = A(5,5,ioff) + res2t1
        endif
      endif

    enddo edge_loop_1060

  end subroutine turbpart_convectioni


!====================== TURBPART_TET_DIFFUSION ===============================80
!
!                                 t
!  This routine computes the dR/dQ * lambda contribution from the
!  turbulence model for the residual
!
!   diffusion pieces for tets
!
!=============================================================================80
  subroutine turbpart_tet_diffusioni(nnodes0,nnodes01,x,y,z,turb,ncell,c2n,c2e,&
                                     nedge,nedgeLoc,eptr,dft1,dft2,nfunctions, &
                                     n_turb,rlamb,res,coltag,iau,fhelp,a)

    use kinddefs,      only : dp
    use info_depr,     only : re
    use turb_sa_const, only : sig, cb2
    use design_types,  only : max_functions
    use lmpi,          only : lmpi_master, lmpi_die

    integer, intent(in) :: nnodes0,nnodes01,nfunctions,n_turb
    integer, intent(in) :: ncell
    integer, intent(in) :: nedge,nedgeLoc

    integer, dimension(4,ncell), intent(in) :: c2n
    integer, dimension(6,ncell), intent(in) :: c2e
    integer, dimension(2,nedge), intent(in) :: eptr
    integer, dimension(:),       intent(in), optional :: iau
    integer, dimension(:,:),     intent(in), optional :: fhelp

    real(dp), dimension(nnodes01),        intent(in)    :: x,y,z
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(nedge),           intent(in)    :: dft1
    real(dp), dimension(nedge),           intent(in)    :: dft2
    real(dp), dimension(:,:,:),           intent(in),    optional :: rlamb
    real(dp), dimension(:,:),             intent(in),    optional :: coltag
    real(dp), dimension(:,:,:),           intent(inout), optional :: res
    real(dp), dimension(:,:,:),           intent(inout), optional :: a

    integer :: inode1,inode2,inode3,inode4,idiag,ioff,ioff1,ioff2,ioff3
    integer :: i,node1,node2,n,irow2,irow3,ie,iedge,lnode1,lnode2
    integer :: onode1,onode2,oedge1,oedge2,oedge3,oedge4
    integer :: iedge1,iedge2,iedge3,iedge4,iedge5,iedge6

    real(dp) :: nx1,nx2,nx3,nx4
    real(dp) :: ny1,ny2,ny3,ny4
    real(dp) :: nz1,nz2,nz3,nz4

    real(dp) :: x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4

    real(dp) :: wxx12,wxx13,wxx14,wxx23,wxx24,wxx34
    real(dp) :: wyy12,wyy13,wyy14,wyy34,wyy23,wyy24
    real(dp) :: wzz12,wzz13,wzz14,wzz34,wzz23,wzz24

    real(dp) :: const,cona,conb,rmr

    real(dp) :: dft1e1q11,dft1e1q12,dft1e1q13,dft1e1q14,dft1e1t1
    real(dp) :: dft1e1q21,dft1e1q22,dft1e1q23,dft1e1q24,dft1e1t2
    real(dp) :: dft1e1q31,dft1e1q32,dft1e1q33,dft1e1q34,dft1e1t3
    real(dp) :: dft1e1q41,dft1e1q42,dft1e1q43,dft1e1q44,dft1e1t4

    real(dp) :: dft1e2q11,dft1e2q12,dft1e2q13,dft1e2q14,dft1e2t1
    real(dp) :: dft1e2q21,dft1e2q22,dft1e2q23,dft1e2q24,dft1e2t2
    real(dp) :: dft1e2q31,dft1e2q32,dft1e2q33,dft1e2q34,dft1e2t3
    real(dp) :: dft1e2q41,dft1e2q42,dft1e2q43,dft1e2q44,dft1e2t4

    real(dp) :: dft1e3q11,dft1e3q12,dft1e3q13,dft1e3q14,dft1e3t1
    real(dp) :: dft1e3q21,dft1e3q22,dft1e3q23,dft1e3q24,dft1e3t2
    real(dp) :: dft1e3q31,dft1e3q32,dft1e3q33,dft1e3q34,dft1e3t3
    real(dp) :: dft1e3q41,dft1e3q42,dft1e3q43,dft1e3q44,dft1e3t4

    real(dp) :: dft1e4q11,dft1e4q12,dft1e4q13,dft1e4q14,dft1e4t1
    real(dp) :: dft1e4q21,dft1e4q22,dft1e4q23,dft1e4q24,dft1e4t2
    real(dp) :: dft1e4q31,dft1e4q32,dft1e4q33,dft1e4q34,dft1e4t3
    real(dp) :: dft1e4q41,dft1e4q42,dft1e4q43,dft1e4q44,dft1e4t4

    real(dp) :: dft1e5q11,dft1e5q12,dft1e5q13,dft1e5q14,dft1e5t1
    real(dp) :: dft1e5q21,dft1e5q22,dft1e5q23,dft1e5q24,dft1e5t2
    real(dp) :: dft1e5q31,dft1e5q32,dft1e5q33,dft1e5q34,dft1e5t3
    real(dp) :: dft1e5q41,dft1e5q42,dft1e5q43,dft1e5q44,dft1e5t4

    real(dp) :: dft1e6q11,dft1e6q12,dft1e6q13,dft1e6q14,dft1e6t1
    real(dp) :: dft1e6q21,dft1e6q22,dft1e6q23,dft1e6q24,dft1e6t2
    real(dp) :: dft1e6q31,dft1e6q32,dft1e6q33,dft1e6q34,dft1e6t3
    real(dp) :: dft1e6q41,dft1e6q42,dft1e6q43,dft1e6q44,dft1e6t4

    real(dp) :: dft2e1q11,dft2e1q12,dft2e1q13,dft2e1q14,dft2e1t1
    real(dp) :: dft2e1q21,dft2e1q22,dft2e1q23,dft2e1q24,dft2e1t2
    real(dp) :: dft2e1q31,dft2e1q32,dft2e1q33,dft2e1q34,dft2e1t3
    real(dp) :: dft2e1q41,dft2e1q42,dft2e1q43,dft2e1q44,dft2e1t4

    real(dp) :: dft2e2q11,dft2e2q12,dft2e2q13,dft2e2q14,dft2e2t1
    real(dp) :: dft2e2q21,dft2e2q22,dft2e2q23,dft2e2q24,dft2e2t2
    real(dp) :: dft2e2q31,dft2e2q32,dft2e2q33,dft2e2q34,dft2e2t3
    real(dp) :: dft2e2q41,dft2e2q42,dft2e2q43,dft2e2q44,dft2e2t4

    real(dp) :: dft2e3q11,dft2e3q12,dft2e3q13,dft2e3q14,dft2e3t1
    real(dp) :: dft2e3q21,dft2e3q22,dft2e3q23,dft2e3q24,dft2e3t2
    real(dp) :: dft2e3q31,dft2e3q32,dft2e3q33,dft2e3q34,dft2e3t3
    real(dp) :: dft2e3q41,dft2e3q42,dft2e3q43,dft2e3q44,dft2e3t4

    real(dp) :: dft2e4q11,dft2e4q12,dft2e4q13,dft2e4q14,dft2e4t1
    real(dp) :: dft2e4q21,dft2e4q22,dft2e4q23,dft2e4q24,dft2e4t2
    real(dp) :: dft2e4q31,dft2e4q32,dft2e4q33,dft2e4q34,dft2e4t3
    real(dp) :: dft2e4q41,dft2e4q42,dft2e4q43,dft2e4q44,dft2e4t4

    real(dp) :: dft2e5q11,dft2e5q12,dft2e5q13,dft2e5q14,dft2e5t1
    real(dp) :: dft2e5q21,dft2e5q22,dft2e5q23,dft2e5q24,dft2e5t2
    real(dp) :: dft2e5q31,dft2e5q32,dft2e5q33,dft2e5q34,dft2e5t3
    real(dp) :: dft2e5q41,dft2e5q42,dft2e5q43,dft2e5q44,dft2e5t4

    real(dp) :: dft2e6q11,dft2e6q12,dft2e6q13,dft2e6q14,dft2e6t1
    real(dp) :: dft2e6q21,dft2e6q22,dft2e6q23,dft2e6q24,dft2e6t2
    real(dp) :: dft2e6q31,dft2e6q32,dft2e6q33,dft2e6q34,dft2e6t3
    real(dp) :: dft2e6q41,dft2e6q42,dft2e6q43,dft2e6q44,dft2e6t4

    real(dp) :: dogq1,dogq2,dogq3,dogq4,dogt1

    real(dp) :: phiq11,phiq12,phiq13,phiq14,phit1
    real(dp) :: phiq21,phiq22,phiq23,phiq24,phit2
    real(dp) :: phiq31,phiq32,phiq33,phiq34,phit3
    real(dp) :: phiq41,phiq42,phiq43,phiq44,phit4

    real(dp) :: res1q11,res1q12,res1q13,res1q14,res1t1
    real(dp) :: res1q21,res1q22,res1q23,res1q24,res1t2
    real(dp) :: res1q31,res1q32,res1q33,res1q34,res1t3
    real(dp) :: res1q41,res1q42,res1q43,res1q44,res1t4

    real(dp) :: res2q11,res2q12,res2q13,res2q14,res2t1
    real(dp) :: res2q21,res2q22,res2q23,res2q24,res2t2
    real(dp) :: res2q31,res2q32,res2q33,res2q34,res2t3
    real(dp) :: res2q41,res2q42,res2q43,res2q44,res2t4

    real(dp) :: rnu1q11, rnu1q12, rnu1q13, rnu1q14, rnu1t1
    real(dp) :: rnu1q21, rnu1q22, rnu1q23, rnu1q24, rnu1t2
    real(dp) :: rnu1q31, rnu1q32, rnu1q33, rnu1q34, rnu1t3
    real(dp) :: rnu1q41, rnu1q42, rnu1q43, rnu1q44, rnu1t4

    real(dp) :: rnu2q11, rnu2q12, rnu2q13, rnu2q14, rnu2t1
    real(dp) :: rnu2q21, rnu2q22, rnu2q23, rnu2q24, rnu2t2
    real(dp) :: rnu2q31, rnu2q32, rnu2q33, rnu2q34, rnu2t3
    real(dp) :: rnu2q41, rnu2q42, rnu2q43, rnu2q44, rnu2t4

    real(dp) :: rnu3q11, rnu3q12, rnu3q13, rnu3q14, rnu3t1
    real(dp) :: rnu3q21, rnu3q22, rnu3q23, rnu3q24, rnu3t2
    real(dp) :: rnu3q31, rnu3q32, rnu3q33, rnu3q34, rnu3t3
    real(dp) :: rnu3q41, rnu3q42, rnu3q43, rnu3q44, rnu3t4

    real(dp) :: rnu4q11, rnu4q12, rnu4q13, rnu4q14, rnu4t1
    real(dp) :: rnu4q21, rnu4q22, rnu4q23, rnu4q24, rnu4t2
    real(dp) :: rnu4q31, rnu4q32, rnu4q33, rnu4q34, rnu4t3
    real(dp) :: rnu4q41, rnu4q42, rnu4q43, rnu4q44, rnu4t4

    real(dp) :: rnuq11,rnuq12,rnuq13,rnuq14,rnut1
    real(dp) :: rnuq21,rnuq22,rnuq23,rnuq24,rnut2
    real(dp) :: rnuq31,rnuq32,rnuq33,rnuq34,rnut3
    real(dp) :: rnuq41,rnuq42,rnuq43,rnuq44,rnut4

    real(dp) :: term1
    real(dp) :: term1q11,term1q12,term1q13,term1q14,term1t1
    real(dp) :: term1q21,term1q22,term1q23,term1q24,term1t2
    real(dp) :: term1q31,term1q32,term1q33,term1q34,term1t3
    real(dp) :: term1q41,term1q42,term1q43,term1q44,term1t4

    real(dp) :: term2
    real(dp) :: term2q11,term2q12,term2q13,term2q14,term2t1
    real(dp) :: term2q21,term2q22,term2q23,term2q24,term2t2
    real(dp) :: term2q31,term2q32,term2q33,term2q34,term2t3
    real(dp) :: term2q41,term2q42,term2q43,term2q44,term2t4

    real(dp) :: terma
    real(dp) :: termaq11,termaq12,termaq13,termaq14,termat1
    real(dp) :: termaq21,termaq22,termaq23,termaq24,termat2
    real(dp) :: termaq31,termaq32,termaq33,termaq34,termat3
    real(dp) :: termaq41,termaq42,termaq43,termaq44,termat4

    real(dp) :: termb
    real(dp) :: termbq11,termbq12,termbq13,termbq14,termbt1
    real(dp) :: termbq21,termbq22,termbq23,termbq24,termbt2
    real(dp) :: termbq31,termbq32,termbq33,termbq34,termbt3
    real(dp) :: termbq41,termbq42,termbq43,termbq44,termbt4

    real(dp) :: volume,wcon

    real(dp) :: trbre1q11,trbre1q12,trbre1q13,trbre1q14,trbre1t1
    real(dp) :: trbre1q21,trbre1q22,trbre1q23,trbre1q24,trbre1t2
    real(dp) :: trbre1q31,trbre1q32,trbre1q33,trbre1q34,trbre1t3
    real(dp) :: trbre1q41,trbre1q42,trbre1q43,trbre1q44,trbre1t4

    real(dp) :: trbre2q11,trbre2q12,trbre2q13,trbre2q14,trbre2t1
    real(dp) :: trbre2q21,trbre2q22,trbre2q23,trbre2q24,trbre2t2
    real(dp) :: trbre2q31,trbre2q32,trbre2q33,trbre2q34,trbre2t3
    real(dp) :: trbre2q41,trbre2q42,trbre2q43,trbre2q44,trbre2t4

    real(dp) :: trbre3q11,trbre3q12,trbre3q13,trbre3q14,trbre3t1
    real(dp) :: trbre3q21,trbre3q22,trbre3q23,trbre3q24,trbre3t2
    real(dp) :: trbre3q31,trbre3q32,trbre3q33,trbre3q34,trbre3t3
    real(dp) :: trbre3q41,trbre3q42,trbre3q43,trbre3q44,trbre3t4

    real(dp) :: trbre4q11,trbre4q12,trbre4q13,trbre4q14,trbre4t1
    real(dp) :: trbre4q21,trbre4q22,trbre4q23,trbre4q24,trbre4t2
    real(dp) :: trbre4q31,trbre4q32,trbre4q33,trbre4q34,trbre4t3
    real(dp) :: trbre4q41,trbre4q42,trbre4q43,trbre4q44,trbre4t4

    real(dp) :: trbreq11,trbreq12,trbreq13,trbreq14,trbret1
    real(dp) :: trbreq21,trbreq22,trbreq23,trbreq24,trbret2
    real(dp) :: trbreq31,trbreq32,trbreq33,trbreq34,trbret3
    real(dp) :: trbreq41,trbreq42,trbreq43,trbreq44,trbret4

    real(dp), dimension(max_functions) :: rlam,rlam15,rlam25

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlamb) ) then
        if ( lmpi_master ) then
          write(*,*)'res requested in turbpart_tet_diff but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if (.not.present(iau) .or. .not.present(fhelp)) then
        if ( lmpi_master ) then
          write(*,*)'a requested in turbpart_tet_diff but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    ioff1 = 0; ioff2 = 0; ioff3 = 0
    oedge1 = 0; oedge2 = 0; oedge3 = 0; oedge4 = 0
    onode1 = 0; onode2 = 0
    term1q11 = 0.0_dp
    term1q12 = 0.0_dp
    term1q13 = 0.0_dp
    term1q14 = 0.0_dp
    term1t1  = 0.0_dp
    term1q21 = 0.0_dp
    term1q22 = 0.0_dp
    term1q23 = 0.0_dp
    term1q24 = 0.0_dp
    term1t2  = 0.0_dp
    term1q31 = 0.0_dp
    term1q32 = 0.0_dp
    term1q33 = 0.0_dp
    term1q34 = 0.0_dp
    term1t3  = 0.0_dp
    term1q41 = 0.0_dp
    term1q42 = 0.0_dp
    term1q43 = 0.0_dp
    term1q44 = 0.0_dp
    term1t4  = 0.0_dp
    term2q11 = 0.0_dp
    term2q12 = 0.0_dp
    term2q13 = 0.0_dp
    term2q14 = 0.0_dp
    term2t1  = 0.0_dp
    term2q21 = 0.0_dp
    term2q22 = 0.0_dp
    term2q23 = 0.0_dp
    term2q24 = 0.0_dp
    term2t2  = 0.0_dp
    term2q31 = 0.0_dp
    term2q32 = 0.0_dp
    term2q33 = 0.0_dp
    term2q34 = 0.0_dp
    term2t3  = 0.0_dp
    term2q41 = 0.0_dp
    term2q42 = 0.0_dp
    term2q43 = 0.0_dp
    term2q44 = 0.0_dp
    term2t4  = 0.0_dp

!  First hold terma and termb constant wrt turb, and
!  do the easy part with a loop over edges

    edge_2nd_4060 : do n = 1, nedgeloc
      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Unit normal to face and area

! Dissipative part

      rmr   = 1.0_dp/Re/sig
      term1 = dft2(n)
      term2 = cb2*turb(1,node1)*dft1(n)
      terma = Max( term1 - term2, 0.0_dp)
      termb = Min(-term1 + term2, 0.0_dp)

      res1t1 = rmr*terma
      res1t2 = rmr*termb

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam15(i) = coltag(5,node1)*rlamb(5,node1,i)

          if(node1 <= nnodes0) then
            res(5,node1,i) = res(5,node1,i) + res1t1*rlam15(i)
          endif
          if(node2 <= nnodes0) then
            res(5,node2,i) = res(5,node2,i) + res1t2*rlam15(i)
          endif
        end do
      endif

      if ( fill_a ) then
        idiag = iau(node1)
        ioff  = fhelp(2,n)

        if(node1 <= nnodes0) then
          A(5,5,idiag) = A(5,5,idiag) + res1t1
        endif
        if(node2 <= nnodes0) then
          A(5,5,ioff) = A(5,5,ioff) + res1t2
        endif
      endif

! Now do the other node (note that ubar pts in other direction. This
! is because I'm too stupid to work out all the sign changes but
! I need an outward pointing normal for the convective stuff

      term1 = dft2(n)
      term2 = cb2*turb(1,node2)*dft1(n)
      terma = Max( term1 - term2, 0.0_dp)
      termb = Min(-term1 + term2, 0.0_dp)

      res2t1 = rmr*termb
      res2t2 = rmr*terma

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam25(i) = coltag(5,node2)*rlamb(5,node2,i)

          if(node1 <= nnodes0) then
            res(5,node1,i) = res(5,node1,i) + res2t1*rlam25(i)
          endif
          if(node2 <= nnodes0) then
            res(5,node2,i) = res(5,node2,i) + res2t2*rlam25(i)
          endif
        end do
      endif

      if ( fill_a ) then
        idiag = iau(node2)
        ioff  = fhelp(1,n)

        if(node2 <= nnodes0) then
          A(5,5,idiag) = A(5,5,idiag) + res2t2
        endif
        if(node1 <= nnodes0) then
          A(5,5,ioff) = A(5,5,ioff) + res2t1
        endif
      endif

    enddo edge_2nd_4060

!  Now take care of varying terma and termb.  This is a
!  real dog and has to be done with a loop over cells.

    cell_loop_1020 : do n = 1, ncell

! First get the four nodes and the six edges
! For now we'll just use the cell-to-node pointers

      inode1 = c2n(1,n)
      inode2 = c2n(2,n)
      inode3 = c2n(3,n)
      inode4 = c2n(4,n)

      iedge1 = c2e(1,n) !Between nodes 1-2
      iedge2 = c2e(2,n) !Between nodes 1-3
      iedge3 = c2e(3,n) !Between nodes 1-4
      iedge4 = c2e(4,n) !Between nodes 2-3
      iedge5 = c2e(5,n) !Between nodes 2-4
      iedge6 = c2e(6,n) !Between nodes 3-4

      x1 = x(inode1)
      x2 = x(inode2)
      x3 = x(inode3)
      x4 = x(inode4)

      y1 = y(inode1)
      y2 = y(inode2)
      y3 = y(inode3)
      y4 = y(inode4)

      z1 = z(inode1)
      z2 = z(inode2)
      z3 = z(inode3)
      z4 = z(inode4)

!   Lets get outward normals (nx_i is for the face opposite node i)

      nx1 = 0.5_dp*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
      ny1 = 0.5_dp*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
      nz1 = 0.5_dp*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

      nx2 = 0.5_dp*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
      ny2 = 0.5_dp*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
      nz2 = 0.5_dp*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

      nx3 = 0.5_dp*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
      ny3 = 0.5_dp*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
      nz3 = 0.5_dp*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

      nx4 = -nx1 -nx2 -nx3
      ny4 = -ny1 -ny2 -ny3
      nz4 = -nz1 -nz2 -nz3

! Compute cell volume

      volume = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                    &
        -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                           &
        +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))/6.0_dp

! Compute cell averaged quantities you need

!     rnu1 = 1.0_dp
      rnu1q11 = 0.0_dp
      rnu1q12 = 0.0_dp
      rnu1q13 = 0.0_dp
      rnu1q14 = 0.0_dp
      rnu1t1  = 0.0_dp

      rnu1q21 = 0.0_dp
      rnu1q22 = 0.0_dp
      rnu1q23 = 0.0_dp
      rnu1q24 = 0.0_dp
      rnu1t2  = 0.0_dp

      rnu1q31 = 0.0_dp
      rnu1q32 = 0.0_dp
      rnu1q33 = 0.0_dp
      rnu1q34 = 0.0_dp
      rnu1t3  = 0.0_dp

      rnu1q41 = 0.0_dp
      rnu1q42 = 0.0_dp
      rnu1q43 = 0.0_dp
      rnu1q44 = 0.0_dp
      rnu1t4  = 0.0_dp

!     rnu2 = 1.0_dp
      rnu2q11 = 0.0_dp
      rnu2q12 = 0.0_dp
      rnu2q13 = 0.0_dp
      rnu2q14 = 0.0_dp
      rnu2t1  = 0.0_dp

      rnu2q21 = 0.0_dp
      rnu2q22 = 0.0_dp
      rnu2q23 = 0.0_dp
      rnu2q24 = 0.0_dp
      rnu2t2  = 0.0_dp

      rnu2q31 = 0.0_dp
      rnu2q32 = 0.0_dp
      rnu2q33 = 0.0_dp
      rnu2q34 = 0.0_dp
      rnu2t3  = 0.0_dp

      rnu2q41 = 0.0_dp
      rnu2q42 = 0.0_dp
      rnu2q43 = 0.0_dp
      rnu2q44 = 0.0_dp
      rnu2t4  = 0.0_dp

!     rnu3 = 1.0_dp
      rnu3q11 = 0.0_dp
      rnu3q12 = 0.0_dp
      rnu3q13 = 0.0_dp
      rnu3q14 = 0.0_dp
      rnu3t1  = 0.0_dp

      rnu3q21 = 0.0_dp
      rnu3q22 = 0.0_dp
      rnu3q23 = 0.0_dp
      rnu3q24 = 0.0_dp
      rnu3t2  = 0.0_dp

      rnu3q31 = 0.0_dp
      rnu3q32 = 0.0_dp
      rnu3q33 = 0.0_dp
      rnu3q34 = 0.0_dp
      rnu3t3  = 0.0_dp

      rnu3q41 = 0.0_dp
      rnu3q42 = 0.0_dp
      rnu3q43 = 0.0_dp
      rnu3q44 = 0.0_dp
      rnu3t4  = 0.0_dp

!     rnu4 = 1.0_dp
      rnu4q11 = 0.0_dp
      rnu4q12 = 0.0_dp
      rnu4q13 = 0.0_dp
      rnu4q14 = 0.0_dp
      rnu4t1  = 0.0_dp

      rnu4q21 = 0.0_dp
      rnu4q22 = 0.0_dp
      rnu4q23 = 0.0_dp
      rnu4q24 = 0.0_dp
      rnu4t2  = 0.0_dp

      rnu4q31 = 0.0_dp
      rnu4q32 = 0.0_dp
      rnu4q33 = 0.0_dp
      rnu4q34 = 0.0_dp
      rnu4t3  = 0.0_dp

      rnu4q41 = 0.0_dp
      rnu4q42 = 0.0_dp
      rnu4q43 = 0.0_dp
      rnu4q44 = 0.0_dp
      rnu4t4  = 0.0_dp

!     trbre1 = turb(1,inode1)
      trbre1q11 = 0.0_dp
      trbre1q12 = 0.0_dp
      trbre1q13 = 0.0_dp
      trbre1q14 = 0.0_dp
      trbre1t1  = 1.0_dp

      trbre1q21 = 0.0_dp
      trbre1q22 = 0.0_dp
      trbre1q23 = 0.0_dp
      trbre1q24 = 0.0_dp
      trbre1t2  = 0.0_dp

      trbre1q31 = 0.0_dp
      trbre1q32 = 0.0_dp
      trbre1q33 = 0.0_dp
      trbre1q34 = 0.0_dp
      trbre1t3  = 0.0_dp

      trbre1q41 = 0.0_dp
      trbre1q42 = 0.0_dp
      trbre1q43 = 0.0_dp
      trbre1q44 = 0.0_dp
      trbre1t4  = 0.0_dp

!     trbre2 = turb(1,inode2)
      trbre2q11 = 0.0_dp
      trbre2q12 = 0.0_dp
      trbre2q13 = 0.0_dp
      trbre2q14 = 0.0_dp
      trbre2t1  = 0.0_dp

      trbre2q21 = 0.0_dp
      trbre2q22 = 0.0_dp
      trbre2q23 = 0.0_dp
      trbre2q24 = 0.0_dp
      trbre2t2  = 1.0_dp

      trbre2q31 = 0.0_dp
      trbre2q32 = 0.0_dp
      trbre2q33 = 0.0_dp
      trbre2q34 = 0.0_dp
      trbre2t3  = 0.0_dp

      trbre2q41 = 0.0_dp
      trbre2q42 = 0.0_dp
      trbre2q43 = 0.0_dp
      trbre2q44 = 0.0_dp
      trbre2t4  = 0.0_dp

!     trbre3 = turb(1,inode3)
      trbre3q11 = 0.0_dp
      trbre3q12 = 0.0_dp
      trbre3q13 = 0.0_dp
      trbre3q14 = 0.0_dp
      trbre3t1  = 0.0_dp

      trbre3q21 = 0.0_dp
      trbre3q22 = 0.0_dp
      trbre3q23 = 0.0_dp
      trbre3q24 = 0.0_dp
      trbre3t2  = 0.0_dp

      trbre3q31 = 0.0_dp
      trbre3q32 = 0.0_dp
      trbre3q33 = 0.0_dp
      trbre3q34 = 0.0_dp
      trbre3t3  = 1.0_dp

      trbre3q41 = 0.0_dp
      trbre3q42 = 0.0_dp
      trbre3q43 = 0.0_dp
      trbre3q44 = 0.0_dp
      trbre3t4  = 0.0_dp

!     trbre4 = turb(1,inode4)
      trbre4q11 = 0.0_dp
      trbre4q12 = 0.0_dp
      trbre4q13 = 0.0_dp
      trbre4q14 = 0.0_dp
      trbre4t1  = 0.0_dp

      trbre4q21 = 0.0_dp
      trbre4q22 = 0.0_dp
      trbre4q23 = 0.0_dp
      trbre4q24 = 0.0_dp
      trbre4t2  = 0.0_dp

      trbre4q31 = 0.0_dp
      trbre4q32 = 0.0_dp
      trbre4q33 = 0.0_dp
      trbre4q34 = 0.0_dp
      trbre4t3  = 0.0_dp

      trbre4q41 = 0.0_dp
      trbre4q42 = 0.0_dp
      trbre4q43 = 0.0_dp
      trbre4q44 = 0.0_dp
      trbre4t4  = 1.0_dp

!     rnu   = 0.25_dp*(rnu1 + rnu2 + rnu3 + rnu4)
      rnuq11 = 0.25_dp*(rnu1q11 + rnu2q11 + rnu3q11 + rnu4q11)
      rnuq12 = 0.25_dp*(rnu1q12 + rnu2q12 + rnu3q12 + rnu4q12)
      rnuq13 = 0.25_dp*(rnu1q13 + rnu2q13 + rnu3q13 + rnu4q13)
      rnuq14 = 0.25_dp*(rnu1q14 + rnu2q14 + rnu3q14 + rnu4q14)
      rnut1  = 0.25_dp*(rnu1t1  + rnu2t1  + rnu3t1  + rnu4t1 )

      rnuq21 = 0.25_dp*(rnu1q21 + rnu2q21 + rnu3q21 + rnu4q21)
      rnuq22 = 0.25_dp*(rnu1q22 + rnu2q22 + rnu3q22 + rnu4q22)
      rnuq23 = 0.25_dp*(rnu1q23 + rnu2q23 + rnu3q23 + rnu4q23)
      rnuq24 = 0.25_dp*(rnu1q24 + rnu2q24 + rnu3q24 + rnu4q24)
      rnut2  = 0.25_dp*(rnu1t2  + rnu2t2  + rnu3t2  + rnu4t2 )

      rnuq31 = 0.25_dp*(rnu1q31 + rnu2q31 + rnu3q31 + rnu4q31)
      rnuq32 = 0.25_dp*(rnu1q32 + rnu2q32 + rnu3q32 + rnu4q32)
      rnuq33 = 0.25_dp*(rnu1q33 + rnu2q33 + rnu3q33 + rnu4q33)
      rnuq34 = 0.25_dp*(rnu1q34 + rnu2q34 + rnu3q34 + rnu4q34)
      rnut3  = 0.25_dp*(rnu1t3  + rnu2t3  + rnu3t3  + rnu4t3 )

      rnuq41 = 0.25_dp*(rnu1q41 + rnu2q41 + rnu3q41 + rnu4q41)
      rnuq42 = 0.25_dp*(rnu1q42 + rnu2q42 + rnu3q42 + rnu4q42)
      rnuq43 = 0.25_dp*(rnu1q43 + rnu2q43 + rnu3q43 + rnu4q43)
      rnuq44 = 0.25_dp*(rnu1q44 + rnu2q44 + rnu3q44 + rnu4q44)
      rnut4  = 0.25_dp*(rnu1t4  + rnu2t4  + rnu3t4  + rnu4t4 )

!     trbre = 0.25_dp*(trbre1 + trbre2 +  trbre3 + trbre4)
      trbreq11=0.25_dp*(trbre1q11 + trbre2q11 + trbre3q11 + trbre4q11)
      trbreq12=0.25_dp*(trbre1q12 + trbre2q12 + trbre3q12 + trbre4q12)
      trbreq13=0.25_dp*(trbre1q13 + trbre2q13 + trbre3q13 + trbre4q13)
      trbreq14=0.25_dp*(trbre1q14 + trbre2q14 + trbre3q14 + trbre4q14)
      trbret1 =0.25_dp*(trbre1t1  + trbre2t1  + trbre3t1  + trbre4t1 )

      trbreq21=0.25_dp*(trbre1q21 + trbre2q21 + trbre3q21 + trbre4q21)
      trbreq22=0.25_dp*(trbre1q22 + trbre2q22 + trbre3q22 + trbre4q22)
      trbreq23=0.25_dp*(trbre1q23 + trbre2q23 + trbre3q23 + trbre4q23)
      trbreq24=0.25_dp*(trbre1q24 + trbre2q24 + trbre3q24 + trbre4q24)
      trbret2 =0.25_dp*(trbre1t2  + trbre2t2  + trbre3t2  + trbre4t2 )

      trbreq31=0.25_dp*(trbre1q31 + trbre2q31 + trbre3q31 + trbre4q31)
      trbreq32=0.25_dp*(trbre1q32 + trbre2q32 + trbre3q32 + trbre4q32)
      trbreq33=0.25_dp*(trbre1q33 + trbre2q33 + trbre3q33 + trbre4q33)
      trbreq34=0.25_dp*(trbre1q34 + trbre2q34 + trbre3q34 + trbre4q34)
      trbret3 =0.25_dp*(trbre1t3  + trbre2t3  + trbre3t3  + trbre4t3 )

      trbreq41=0.25_dp*(trbre1q41 + trbre2q41 + trbre3q41 + trbre4q41)
      trbreq42=0.25_dp*(trbre1q42 + trbre2q42 + trbre3q42 + trbre4q42)
      trbreq43=0.25_dp*(trbre1q43 + trbre2q43 + trbre3q43 + trbre4q43)
      trbreq44=0.25_dp*(trbre1q44 + trbre2q44 + trbre3q44 + trbre4q44)
      trbret4 =0.25_dp*(trbre1t4  + trbre2t4  + trbre3t4  + trbre4t4 )

!     phi = rnu + (1.0_dp + cb2)*trbre
      phiq11 = rnuq11 + (1.0_dp + cb2)*trbreq11
      phiq12 = rnuq12 + (1.0_dp + cb2)*trbreq12
      phiq13 = rnuq13 + (1.0_dp + cb2)*trbreq13
      phiq14 = rnuq14 + (1.0_dp + cb2)*trbreq14
      phit1  = rnut1  + (1.0_dp + cb2)*trbret1

      phiq21 = rnuq21 + (1.0_dp + cb2)*trbreq21
      phiq22 = rnuq22 + (1.0_dp + cb2)*trbreq22
      phiq23 = rnuq23 + (1.0_dp + cb2)*trbreq23
      phiq24 = rnuq24 + (1.0_dp + cb2)*trbreq24
      phit2  = rnut2  + (1.0_dp + cb2)*trbret2

      phiq31 = rnuq31 + (1.0_dp + cb2)*trbreq31
      phiq32 = rnuq32 + (1.0_dp + cb2)*trbreq32
      phiq33 = rnuq33 + (1.0_dp + cb2)*trbreq33
      phiq34 = rnuq34 + (1.0_dp + cb2)*trbreq34
      phit3  = rnut3  + (1.0_dp + cb2)*trbret3

      phiq41 = rnuq41 + (1.0_dp + cb2)*trbreq41
      phiq42 = rnuq42 + (1.0_dp + cb2)*trbreq42
      phiq43 = rnuq43 + (1.0_dp + cb2)*trbreq43
      phiq44 = rnuq44 + (1.0_dp + cb2)*trbreq44
      phit4  = rnut4  + (1.0_dp + cb2)*trbret4

! Now lets compute some weights
! Only need the symmetric ones

      const = 1.0_dp/(9.0_dp*volume)
      wxx12 = -nx2*nx1*const
      wxx13 = -nx3*nx1*const
      wxx14 = -nx4*nx1*const
      wxx34 = -nx4*nx3*const
      wxx23 = -nx3*nx2*const
      wxx24 = -nx4*nx2*const

      wyy12 = -ny2*ny1*const
      wyy13 = -ny3*ny1*const
      wyy14 = -ny4*ny1*const
      wyy34 = -ny4*ny3*const
      wyy23 = -ny3*ny2*const
      wyy24 = -ny4*ny2*const

      wzz12 = -nz2*nz1*const
      wzz13 = -nz3*nz1*const
      wzz14 = -nz4*nz1*const
      wzz34 = -nz4*nz3*const
      wzz23 = -nz3*nz2*const
      wzz24 = -nz4*nz2*const

! Now, since all these terms are symmetric this is easier than before
! because we dont care what "type" of node it is

! For edge along 1-2

!  Note all derivatives of wcon are zilch, it just depends on geometry

      wcon = wxx12 + wyy12 + wzz12
!     dft1(edge1) = dft1(edge1) + wcon
!     dft2(edge1) = dft2(edge1) + phi*wcon
      dft2e1q11 = phiq11*wcon
      dft2e1q12 = phiq12*wcon
      dft2e1q13 = phiq13*wcon
      dft2e1q14 = phiq14*wcon
      dft2e1t1  = phit1 *wcon

      dft2e1q21 = phiq21*wcon
      dft2e1q22 = phiq22*wcon
      dft2e1q23 = phiq23*wcon
      dft2e1q24 = phiq24*wcon
      dft2e1t2  = phit2 *wcon

      dft2e1q31 = phiq31*wcon
      dft2e1q32 = phiq32*wcon
      dft2e1q33 = phiq33*wcon
      dft2e1q34 = phiq34*wcon
      dft2e1t3  = phit3 *wcon

      dft2e1q41 = phiq41*wcon
      dft2e1q42 = phiq42*wcon
      dft2e1q43 = phiq43*wcon
      dft2e1q44 = phiq44*wcon
      dft2e1t4  = phit4 *wcon

! For edge along 1-3

      wcon = wxx13 + wyy13 + wzz13
!     dft1(edge2) = dft1(edge2) + wcon
!     dft2(edge2) = dft2(edge2) + phi*wcon
      dft2e2q11 = phiq11*wcon
      dft2e2q12 = phiq12*wcon
      dft2e2q13 = phiq13*wcon
      dft2e2q14 = phiq14*wcon
      dft2e2t1  = phit1 *wcon

      dft2e2q21 = phiq21*wcon
      dft2e2q22 = phiq22*wcon
      dft2e2q23 = phiq23*wcon
      dft2e2q24 = phiq24*wcon
      dft2e2t2  = phit2 *wcon

      dft2e2q31 = phiq31*wcon
      dft2e2q32 = phiq32*wcon
      dft2e2q33 = phiq33*wcon
      dft2e2q34 = phiq34*wcon
      dft2e2t3  = phit3 *wcon

      dft2e2q41 = phiq41*wcon
      dft2e2q42 = phiq42*wcon
      dft2e2q43 = phiq43*wcon
      dft2e2q44 = phiq44*wcon
      dft2e2t4  = phit4 *wcon

! For edge along 1-4

      wcon = wxx14 + wyy14 + wzz14
!     dft1(edge3) = dft1(edge3) + wcon
!     dft2(edge3) = dft2(edge3) + phi*wcon
      dft2e3q11 = phiq11*wcon
      dft2e3q12 = phiq12*wcon
      dft2e3q13 = phiq13*wcon
      dft2e3q14 = phiq14*wcon
      dft2e3t1  = phit1 *wcon

      dft2e3q21 = phiq21*wcon
      dft2e3q22 = phiq22*wcon
      dft2e3q23 = phiq23*wcon
      dft2e3q24 = phiq24*wcon
      dft2e3t2  = phit2 *wcon

      dft2e3q31 = phiq31*wcon
      dft2e3q32 = phiq32*wcon
      dft2e3q33 = phiq33*wcon
      dft2e3q34 = phiq34*wcon
      dft2e3t3  = phit3 *wcon

      dft2e3q41 = phiq41*wcon
      dft2e3q42 = phiq42*wcon
      dft2e3q43 = phiq43*wcon
      dft2e3q44 = phiq44*wcon
      dft2e3t4  = phit4 *wcon

! For edge along 2-3

      wcon = wxx23 + wyy23 + wzz23
!     dft1(edge4) = dft1(edge4) + wcon
!     dft2(edge4) = dft2(edge4) + phi*wcon
      dft2e4q11 = phiq11*wcon
      dft2e4q12 = phiq12*wcon
      dft2e4q13 = phiq13*wcon
      dft2e4q14 = phiq14*wcon
      dft2e4t1  = phit1 *wcon

      dft2e4q21 = phiq21*wcon
      dft2e4q22 = phiq22*wcon
      dft2e4q23 = phiq23*wcon
      dft2e4q24 = phiq24*wcon
      dft2e4t2  = phit2 *wcon

      dft2e4q31 = phiq31*wcon
      dft2e4q32 = phiq32*wcon
      dft2e4q33 = phiq33*wcon
      dft2e4q34 = phiq34*wcon
      dft2e4t3  = phit3 *wcon

      dft2e4q41 = phiq41*wcon
      dft2e4q42 = phiq42*wcon
      dft2e4q43 = phiq43*wcon
      dft2e4q44 = phiq44*wcon
      dft2e4t4  = phit4 *wcon

! For edge along 2-4

      wcon = wxx24 + wyy24 + wzz24
!     dft1(edge5) = dft1(edge5) + wcon
!     dft2(edge5) = dft2(edge5) + phi*wcon
      dft2e5q11 = phiq11*wcon
      dft2e5q12 = phiq12*wcon
      dft2e5q13 = phiq13*wcon
      dft2e5q14 = phiq14*wcon
      dft2e5t1  = phit1 *wcon

      dft2e5q21 = phiq21*wcon
      dft2e5q22 = phiq22*wcon
      dft2e5q23 = phiq23*wcon
      dft2e5q24 = phiq24*wcon
      dft2e5t2  = phit2 *wcon

      dft2e5q31 = phiq31*wcon
      dft2e5q32 = phiq32*wcon
      dft2e5q33 = phiq33*wcon
      dft2e5q34 = phiq34*wcon
      dft2e5t3  = phit3 *wcon

      dft2e5q41 = phiq41*wcon
      dft2e5q42 = phiq42*wcon
      dft2e5q43 = phiq43*wcon
      dft2e5q44 = phiq44*wcon
      dft2e5t4  = phit4 *wcon

! For edge along 3-4

      wcon = wxx34 + wyy34 + wzz34
!     dft1(edge6) = dft1(edge6) + wcon
!     dft2(edge6) = dft2(edge6) + phi*wcon
      dft2e6q11 = phiq11*wcon
      dft2e6q12 = phiq12*wcon
      dft2e6q13 = phiq13*wcon
      dft2e6q14 = phiq14*wcon
      dft2e6t1  = phit1 *wcon

      dft2e6q21 = phiq21*wcon
      dft2e6q22 = phiq22*wcon
      dft2e6q23 = phiq23*wcon
      dft2e6q24 = phiq24*wcon
      dft2e6t2  = phit2 *wcon

      dft2e6q31 = phiq31*wcon
      dft2e6q32 = phiq32*wcon
      dft2e6q33 = phiq33*wcon
      dft2e6q34 = phiq34*wcon
      dft2e6t3  = phit3 *wcon

      dft2e6q41 = phiq41*wcon
      dft2e6q42 = phiq42*wcon
      dft2e6q43 = phiq43*wcon
      dft2e6q44 = phiq44*wcon
      dft2e6t4  = phit4 *wcon

!  Zero out all dft1 derivatives

      dft1e1q11 = 0.0_dp
      dft1e1q12 = 0.0_dp
      dft1e1q13 = 0.0_dp
      dft1e1q14 = 0.0_dp
      dft1e1t1  = 0.0_dp

      dft1e1q21 = 0.0_dp
      dft1e1q22 = 0.0_dp
      dft1e1q23 = 0.0_dp
      dft1e1q24 = 0.0_dp
      dft1e1t2  = 0.0_dp

      dft1e1q31 = 0.0_dp
      dft1e1q32 = 0.0_dp
      dft1e1q33 = 0.0_dp
      dft1e1q34 = 0.0_dp
      dft1e1t3  = 0.0_dp

      dft1e1q41 = 0.0_dp
      dft1e1q42 = 0.0_dp
      dft1e1q43 = 0.0_dp
      dft1e1q44 = 0.0_dp
      dft1e1t4  = 0.0_dp

      dft1e2q11 = 0.0_dp
      dft1e2q12 = 0.0_dp
      dft1e2q13 = 0.0_dp
      dft1e2q14 = 0.0_dp
      dft1e2t1  = 0.0_dp

      dft1e2q21 = 0.0_dp
      dft1e2q22 = 0.0_dp
      dft1e2q23 = 0.0_dp
      dft1e2q24 = 0.0_dp
      dft1e2t2  = 0.0_dp

      dft1e2q31 = 0.0_dp
      dft1e2q32 = 0.0_dp
      dft1e2q33 = 0.0_dp
      dft1e2q34 = 0.0_dp
      dft1e2t3  = 0.0_dp

      dft1e2q41 = 0.0_dp
      dft1e2q42 = 0.0_dp
      dft1e2q43 = 0.0_dp
      dft1e2q44 = 0.0_dp
      dft1e2t4  = 0.0_dp

      dft1e3q11 = 0.0_dp
      dft1e3q12 = 0.0_dp
      dft1e3q13 = 0.0_dp
      dft1e3q14 = 0.0_dp
      dft1e3t1  = 0.0_dp

      dft1e3q21 = 0.0_dp
      dft1e3q22 = 0.0_dp
      dft1e3q23 = 0.0_dp
      dft1e3q24 = 0.0_dp
      dft1e3t2  = 0.0_dp

      dft1e3q31 = 0.0_dp
      dft1e3q32 = 0.0_dp
      dft1e3q33 = 0.0_dp
      dft1e3q34 = 0.0_dp
      dft1e3t3  = 0.0_dp

      dft1e3q41 = 0.0_dp
      dft1e3q42 = 0.0_dp
      dft1e3q43 = 0.0_dp
      dft1e3q44 = 0.0_dp
      dft1e3t4  = 0.0_dp

      dft1e4q11 = 0.0_dp
      dft1e4q12 = 0.0_dp
      dft1e4q13 = 0.0_dp
      dft1e4q14 = 0.0_dp
      dft1e4t1  = 0.0_dp

      dft1e4q21 = 0.0_dp
      dft1e4q22 = 0.0_dp
      dft1e4q23 = 0.0_dp
      dft1e4q24 = 0.0_dp
      dft1e4t2  = 0.0_dp

      dft1e4q31 = 0.0_dp
      dft1e4q32 = 0.0_dp
      dft1e4q33 = 0.0_dp
      dft1e4q34 = 0.0_dp
      dft1e4t3  = 0.0_dp

      dft1e4q41 = 0.0_dp
      dft1e4q42 = 0.0_dp
      dft1e4q43 = 0.0_dp
      dft1e4q44 = 0.0_dp
      dft1e4t4  = 0.0_dp

      dft1e5q11 = 0.0_dp
      dft1e5q12 = 0.0_dp
      dft1e5q13 = 0.0_dp
      dft1e5q14 = 0.0_dp
      dft1e5t1  = 0.0_dp

      dft1e5q21 = 0.0_dp
      dft1e5q22 = 0.0_dp
      dft1e5q23 = 0.0_dp
      dft1e5q24 = 0.0_dp
      dft1e5t2  = 0.0_dp

      dft1e5q31 = 0.0_dp
      dft1e5q32 = 0.0_dp
      dft1e5q33 = 0.0_dp
      dft1e5q34 = 0.0_dp
      dft1e5t3  = 0.0_dp

      dft1e5q41 = 0.0_dp
      dft1e5q42 = 0.0_dp
      dft1e5q43 = 0.0_dp
      dft1e5q44 = 0.0_dp
      dft1e5t4  = 0.0_dp

      dft1e6q11 = 0.0_dp
      dft1e6q12 = 0.0_dp
      dft1e6q13 = 0.0_dp
      dft1e6q14 = 0.0_dp
      dft1e6t1  = 0.0_dp

      dft1e6q21 = 0.0_dp
      dft1e6q22 = 0.0_dp
      dft1e6q23 = 0.0_dp
      dft1e6q24 = 0.0_dp
      dft1e6t2  = 0.0_dp

      dft1e6q31 = 0.0_dp
      dft1e6q32 = 0.0_dp
      dft1e6q33 = 0.0_dp
      dft1e6q34 = 0.0_dp
      dft1e6t3  = 0.0_dp

      dft1e6q41 = 0.0_dp
      dft1e6q42 = 0.0_dp
      dft1e6q43 = 0.0_dp
      dft1e6q44 = 0.0_dp
      dft1e6t4  = 0.0_dp

!  Now that we have all of the derivatives, let's look at
!  forming the actual dR/dQ contributions now

      edge_loop : do ie = 1, 6

        select case (ie)
        case (1)
          iedge = iedge1
          lnode1 = inode1
          lnode2 = inode2
        case (2)
          iedge = iedge2
          lnode1 = inode1
          lnode2 = inode3
        case (3)
          iedge = iedge3
          lnode1 = inode1
          lnode2 = inode4
        case (4)
          iedge = iedge4
          lnode1 = inode2
          lnode2 = inode3
        case (5)
          iedge = iedge5
          lnode1 = inode2
          lnode2 = inode4
        case (6)
          iedge = iedge6
          lnode1 = inode3
          lnode2 = inode4
        end select

        node1 = eptr(1,iedge)
        node2 = eptr(2,iedge)

!  New approach:  Let's swap the node numbers on the dft2 derivatives
!  up front if needed and go from there.

      if(node1 /= lnode1) then

        select case (ie)
        case (1)
          dogq1     = dft2e1q11
          dogq2     = dft2e1q12
          dogq3     = dft2e1q13
          dogq4     = dft2e1q14
          dogt1     = dft2e1t1

          dft2e1q11 = dft2e1q21
          dft2e1q12 = dft2e1q22
          dft2e1q13 = dft2e1q23
          dft2e1q14 = dft2e1q24
          dft2e1t1  = dft2e1t2

          dft2e1q21 = dogq1
          dft2e1q22 = dogq2
          dft2e1q23 = dogq3
          dft2e1q24 = dogq4
          dft2e1t2  = dogt1
        case (2)
          dogq1     = dft2e2q11
          dogq2     = dft2e2q12
          dogq3     = dft2e2q13
          dogq4     = dft2e2q14
          dogt1     = dft2e2t1

          dft2e2q11 = dft2e2q31
          dft2e2q12 = dft2e2q32
          dft2e2q13 = dft2e2q33
          dft2e2q14 = dft2e2q34
          dft2e2t1  = dft2e2t3

          dft2e2q31 = dogq1
          dft2e2q32 = dogq2
          dft2e2q33 = dogq3
          dft2e2q34 = dogq4
          dft2e2t3  = dogt1
        case (3)
          dogq1     = dft2e3q11
          dogq2     = dft2e3q12
          dogq3     = dft2e3q13
          dogq4     = dft2e3q14
          dogt1     = dft2e3t1

          dft2e3q11 = dft2e3q41
          dft2e3q12 = dft2e3q42
          dft2e3q13 = dft2e3q43
          dft2e3q14 = dft2e3q44
          dft2e3t1  = dft2e3t4

          dft2e3q41 = dogq1
          dft2e3q42 = dogq2
          dft2e3q43 = dogq3
          dft2e3q44 = dogq4
          dft2e3t4  = dogt1
        case (4)
          dogq1     = dft2e4q21
          dogq2     = dft2e4q22
          dogq3     = dft2e4q23
          dogq4     = dft2e4q24
          dogt1     = dft2e4t2

          dft2e4q21 = dft2e4q31
          dft2e4q22 = dft2e4q32
          dft2e4q23 = dft2e4q33
          dft2e4q24 = dft2e4q34
          dft2e4t2  = dft2e4t3

          dft2e4q31 = dogq1
          dft2e4q32 = dogq2
          dft2e4q33 = dogq3
          dft2e4q34 = dogq4
          dft2e4t3  = dogt1
        case (5)
          dogq1     = dft2e5q21
          dogq2     = dft2e5q22
          dogq3     = dft2e5q23
          dogq4     = dft2e5q24
          dogt1     = dft2e5t2

          dft2e5q21 = dft2e5q41
          dft2e5q22 = dft2e5q42
          dft2e5q23 = dft2e5q43
          dft2e5q24 = dft2e5q44
          dft2e5t2  = dft2e5t4

          dft2e5q41 = dogq1
          dft2e5q42 = dogq2
          dft2e5q43 = dogq3
          dft2e5q44 = dogq4
          dft2e5t4  = dogt1
        case (6)
          dogq1     = dft2e6q31
          dogq2     = dft2e6q32
          dogq3     = dft2e6q33
          dogq4     = dft2e6q34
          dogt1     = dft2e6t3

          dft2e6q31 = dft2e6q41
          dft2e6q32 = dft2e6q42
          dft2e6q33 = dft2e6q43
          dft2e6q34 = dft2e6q44
          dft2e6t3  = dft2e6t4

          dft2e6q41 = dogq1
          dft2e6q42 = dogq2
          dft2e6q43 = dogq3
          dft2e6q44 = dogq4
          dft2e6t4  = dogt1
        end select
      endif

! Dissipative part
!         (term1 = phi*(wxx + wyy + wzz))
!         (term2 = cb2*turb*(wxx + wyy + wzz))

      rmr   = 1.0_dp/Re/sig

!  Now we have to be VERY careful here with term1 and term2.  If the edge is
!  oriented inode1 ---> inode2, then the derivatives will
!  be straightforward.  BUT, if the edge is oriented
!  inode2 ---> inode1, then all of the derivatives that
!  depend on quantities from above (dft1 and dft2 derivatives)
!  will have to be reversed!  (This is because the derivatives
!  computed above are wrt 'inodes', instead of 'nodes'.
!  This really is a pain.

      select case (ie)
      case(1)
        wcon = wxx12 + wyy12 + wzz12
        term1q11 = dft2e1q11
        term1q12 = dft2e1q12
        term1q13 = dft2e1q13
        term1q14 = dft2e1q14
        term1t1  = dft2e1t1

        term1q21 = dft2e1q21
        term1q22 = dft2e1q22
        term1q23 = dft2e1q23
        term1q24 = dft2e1q24
        term1t2  = dft2e1t2

        term1q31 = dft2e1q31
        term1q32 = dft2e1q32
        term1q33 = dft2e1q33
        term1q34 = dft2e1q34
        term1t3  = dft2e1t3

        term1q41 = dft2e1q41
        term1q42 = dft2e1q42
        term1q43 = dft2e1q43
        term1q44 = dft2e1q44
        term1t4  = dft2e1t4

        term2 = cb2*turb(1,node1)*wcon
        term2q11 = cb2*turb(1,node1)*dft1e1q11
        term2q12 = cb2*turb(1,node1)*dft1e1q12
        term2q13 = cb2*turb(1,node1)*dft1e1q13
        term2q14 = cb2*turb(1,node1)*dft1e1q14
        term2t1  = cb2*(turb(1,node1)*dft1e1t1 + wcon)

        term2q21 = cb2*turb(1,node1)*dft1e1q21
        term2q22 = cb2*turb(1,node1)*dft1e1q22
        term2q23 = cb2*turb(1,node1)*dft1e1q23
        term2q24 = cb2*turb(1,node1)*dft1e1q24
        term2t2  = cb2*turb(1,node1)*dft1e1t2

        term2q31 = cb2*turb(1,node1)*dft1e1q31
        term2q32 = cb2*turb(1,node1)*dft1e1q32
        term2q33 = cb2*turb(1,node1)*dft1e1q33
        term2q34 = cb2*turb(1,node1)*dft1e1q34
        term2t3  = cb2*turb(1,node1)*dft1e1t3

        term2q41 = cb2*turb(1,node1)*dft1e1q41
        term2q42 = cb2*turb(1,node1)*dft1e1q42
        term2q43 = cb2*turb(1,node1)*dft1e1q43
        term2q44 = cb2*turb(1,node1)*dft1e1q44
        term2t4  = cb2*turb(1,node1)*dft1e1t4
      case(2)
        wcon = wxx13 + wyy13 + wzz13
        term1q11 = dft2e2q11
        term1q12 = dft2e2q12
        term1q13 = dft2e2q13
        term1q14 = dft2e2q14
        term1t1  = dft2e2t1

        term1q31 = dft2e2q21
        term1q32 = dft2e2q22
        term1q33 = dft2e2q23
        term1q34 = dft2e2q24
        term1t3  = dft2e2t2

        term1q21 = dft2e2q31
        term1q22 = dft2e2q32
        term1q23 = dft2e2q33
        term1q24 = dft2e2q34
        term1t2  = dft2e2t3

        term1q41 = dft2e2q41
        term1q42 = dft2e2q42
        term1q43 = dft2e2q43
        term1q44 = dft2e2q44
        term1t4  = dft2e2t4

        term2 = cb2*turb(1,node1)*wcon
        term2q11 = cb2*turb(1,node1)*dft1e2q11
        term2q12 = cb2*turb(1,node1)*dft1e2q12
        term2q13 = cb2*turb(1,node1)*dft1e2q13
        term2q14 = cb2*turb(1,node1)*dft1e2q14
        term2t1  = cb2*(turb(1,node1)*dft1e2t1 + wcon)

        term2q31 = cb2*turb(1,node1)*dft1e2q21
        term2q32 = cb2*turb(1,node1)*dft1e2q22
        term2q33 = cb2*turb(1,node1)*dft1e2q23
        term2q34 = cb2*turb(1,node1)*dft1e2q24
        term2t3  = cb2*turb(1,node1)*dft1e2t2

        term2q21 = cb2*turb(1,node1)*dft1e2q31
        term2q22 = cb2*turb(1,node1)*dft1e2q32
        term2q23 = cb2*turb(1,node1)*dft1e2q33
        term2q24 = cb2*turb(1,node1)*dft1e2q34
        term2t2  = cb2*turb(1,node1)*dft1e2t3

        term2q41 = cb2*turb(1,node1)*dft1e2q41
        term2q42 = cb2*turb(1,node1)*dft1e2q42
        term2q43 = cb2*turb(1,node1)*dft1e2q43
        term2q44 = cb2*turb(1,node1)*dft1e2q44
        term2t4  = cb2*turb(1,node1)*dft1e2t4
      case(3)
        wcon = wxx14 + wyy14 + wzz14
        term1q11 = dft2e3q11
        term1q12 = dft2e3q12
        term1q13 = dft2e3q13
        term1q14 = dft2e3q14
        term1t1  = dft2e3t1

        term1q41 = dft2e3q31
        term1q42 = dft2e3q32
        term1q43 = dft2e3q33
        term1q44 = dft2e3q34
        term1t4  = dft2e3t3

        term1q21 = dft2e3q41
        term1q22 = dft2e3q42
        term1q23 = dft2e3q43
        term1q24 = dft2e3q44
        term1t2  = dft2e3t4

        term1q31 = dft2e3q21
        term1q32 = dft2e3q22
        term1q33 = dft2e3q23
        term1q34 = dft2e3q24
        term1t3  = dft2e3t2

        term2 = cb2*turb(1,node1)*wcon
        term2q11 = cb2*turb(1,node1)*dft1e3q11
        term2q12 = cb2*turb(1,node1)*dft1e3q12
        term2q13 = cb2*turb(1,node1)*dft1e3q13
        term2q14 = cb2*turb(1,node1)*dft1e3q14
        term2t1  = cb2*(turb(1,node1)*dft1e3t1 + wcon)

        term2q41 = cb2*turb(1,node1)*dft1e3q31
        term2q42 = cb2*turb(1,node1)*dft1e3q32
        term2q43 = cb2*turb(1,node1)*dft1e3q33
        term2q44 = cb2*turb(1,node1)*dft1e3q34
        term2t4  = cb2*turb(1,node1)*dft1e3t3

        term2q21 = cb2*turb(1,node1)*dft1e3q41
        term2q22 = cb2*turb(1,node1)*dft1e3q42
        term2q23 = cb2*turb(1,node1)*dft1e3q43
        term2q24 = cb2*turb(1,node1)*dft1e3q44
        term2t2  = cb2*turb(1,node1)*dft1e3t4

        term2q31 = cb2*turb(1,node1)*dft1e3q21
        term2q32 = cb2*turb(1,node1)*dft1e3q22
        term2q33 = cb2*turb(1,node1)*dft1e3q23
        term2q34 = cb2*turb(1,node1)*dft1e3q24
        term2t3  = cb2*turb(1,node1)*dft1e3t2
      case(4)
        wcon = wxx23 + wyy23 + wzz23
        term1q21 = dft2e4q31
        term1q22 = dft2e4q32
        term1q23 = dft2e4q33
        term1q24 = dft2e4q34
        term1t2  = dft2e4t3

        term1q31 = dft2e4q11
        term1q32 = dft2e4q12
        term1q33 = dft2e4q13
        term1q34 = dft2e4q14
        term1t3  = dft2e4t1

        term1q11 = dft2e4q21
        term1q12 = dft2e4q22
        term1q13 = dft2e4q23
        term1q14 = dft2e4q24
        term1t1  = dft2e4t2

        term1q41 = dft2e4q41
        term1q42 = dft2e4q42
        term1q43 = dft2e4q43
        term1q44 = dft2e4q44
        term1t4  = dft2e4t4

        term2 = cb2*turb(1,node1)*wcon
        term2q21 = cb2*turb(1,node1)*dft1e4q31
        term2q22 = cb2*turb(1,node1)*dft1e4q32
        term2q23 = cb2*turb(1,node1)*dft1e4q33
        term2q24 = cb2*turb(1,node1)*dft1e4q34
        term2t2  = cb2*turb(1,node1)*dft1e4t3

        term2q31 = cb2*turb(1,node1)*dft1e4q11
        term2q32 = cb2*turb(1,node1)*dft1e4q12
        term2q33 = cb2*turb(1,node1)*dft1e4q13
        term2q34 = cb2*turb(1,node1)*dft1e4q14
        term2t3  = cb2*turb(1,node1)*dft1e4t1

        term2q11 = cb2*turb(1,node1)*dft1e4q21
        term2q12 = cb2*turb(1,node1)*dft1e4q22
        term2q13 = cb2*turb(1,node1)*dft1e4q23
        term2q14 = cb2*turb(1,node1)*dft1e4q24
        term2t1  = cb2*(turb(1,node1)*dft1e4t2 + wcon)

        term2q41 = cb2*turb(1,node1)*dft1e4q41
        term2q42 = cb2*turb(1,node1)*dft1e4q42
        term2q43 = cb2*turb(1,node1)*dft1e4q43
        term2q44 = cb2*turb(1,node1)*dft1e4q44
        term2t4  = cb2*turb(1,node1)*dft1e4t4
      case(5)
        wcon = wxx24 + wyy24 + wzz24
        term1q21 = dft2e5q41
        term1q22 = dft2e5q42
        term1q23 = dft2e5q43
        term1q24 = dft2e5q44
        term1t2  = dft2e5t4

        term1q41 = dft2e5q31
        term1q42 = dft2e5q32
        term1q43 = dft2e5q33
        term1q44 = dft2e5q34
        term1t4  = dft2e5t3

        term1q11 = dft2e5q21
        term1q12 = dft2e5q22
        term1q13 = dft2e5q23
        term1q14 = dft2e5q24
        term1t1  = dft2e5t2

        term1q31 = dft2e5q11
        term1q32 = dft2e5q12
        term1q33 = dft2e5q13
        term1q34 = dft2e5q14
        term1t3  = dft2e5t1

        term2 = cb2*turb(1,node1)*wcon
        term2q21 = cb2*turb(1,node1)*dft1e5q41
        term2q22 = cb2*turb(1,node1)*dft1e5q42
        term2q23 = cb2*turb(1,node1)*dft1e5q43
        term2q24 = cb2*turb(1,node1)*dft1e5q44
        term2t2  = cb2*turb(1,node1)*dft1e5t4

        term2q41 = cb2*turb(1,node1)*dft1e5q31
        term2q42 = cb2*turb(1,node1)*dft1e5q32
        term2q43 = cb2*turb(1,node1)*dft1e5q33
        term2q44 = cb2*turb(1,node1)*dft1e5q34
        term2t4  = cb2*turb(1,node1)*dft1e5t3

        term2q11 = cb2*turb(1,node1)*dft1e5q21
        term2q12 = cb2*turb(1,node1)*dft1e5q22
        term2q13 = cb2*turb(1,node1)*dft1e5q23
        term2q14 = cb2*turb(1,node1)*dft1e5q24
        term2t1  = cb2*(turb(1,node1)*dft1e5t2 + wcon)

        term2q31 = cb2*turb(1,node1)*dft1e5q11
        term2q32 = cb2*turb(1,node1)*dft1e5q12
        term2q33 = cb2*turb(1,node1)*dft1e5q13
        term2q34 = cb2*turb(1,node1)*dft1e5q14
        term2t3  = cb2*turb(1,node1)*dft1e5t1
      case(6)
        wcon = wxx34 + wyy34 + wzz34
        term1q31 = dft2e6q11
        term1q32 = dft2e6q12
        term1q33 = dft2e6q13
        term1q34 = dft2e6q14
        term1t3  = dft2e6t1

        term1q41 = dft2e6q21
        term1q42 = dft2e6q22
        term1q43 = dft2e6q23
        term1q44 = dft2e6q24
        term1t4  = dft2e6t2

        term1q11 = dft2e6q31
        term1q12 = dft2e6q32
        term1q13 = dft2e6q33
        term1q14 = dft2e6q34
        term1t1  = dft2e6t3

        term1q21 = dft2e6q41
        term1q22 = dft2e6q42
        term1q23 = dft2e6q43
        term1q24 = dft2e6q44
        term1t2  = dft2e6t4

        term2 = cb2*turb(1,node1)*wcon
        term2q31 = cb2*turb(1,node1)*dft1e6q11
        term2q32 = cb2*turb(1,node1)*dft1e6q12
        term2q33 = cb2*turb(1,node1)*dft1e6q13
        term2q34 = cb2*turb(1,node1)*dft1e6q14
        term2t3  = cb2*turb(1,node1)*dft1e6t1

        term2q41 = cb2*turb(1,node1)*dft1e6q21
        term2q42 = cb2*turb(1,node1)*dft1e6q22
        term2q43 = cb2*turb(1,node1)*dft1e6q23
        term2q44 = cb2*turb(1,node1)*dft1e6q24
        term2t4  = cb2*turb(1,node1)*dft1e6t2

        term2q11 = cb2*turb(1,node1)*dft1e6q31
        term2q12 = cb2*turb(1,node1)*dft1e6q32
        term2q13 = cb2*turb(1,node1)*dft1e6q33
        term2q14 = cb2*turb(1,node1)*dft1e6q34
        term2t1  = cb2*(turb(1,node1)*dft1e6t3 + wcon)

        term2q21 = cb2*turb(1,node1)*dft1e6q41
        term2q22 = cb2*turb(1,node1)*dft1e6q42
        term2q23 = cb2*turb(1,node1)*dft1e6q43
        term2q24 = cb2*turb(1,node1)*dft1e6q44
        term2t2  = cb2*turb(1,node1)*dft1e6t4
      end select

      term1 = dft2(iedge)
      term2 = cb2*turb(1,node1)*dft1(iedge)
      terma = Max( term1 - term2, 0.0_dp)
      termb = Min(-term1 + term2, 0.0_dp)
      cona = 1.0_dp
      conb = 1.0_dp
      if(term1 - term2 <= 0.0_dp)cona = 0.0_dp
      if(-term1 + term2 >= 0.0_dp)conb = 0.0_dp
      terma =  term1 - term2
      termaq11 = cona*(term1q11 - term2q11)
      termaq12 = cona*(term1q12 - term2q12)
      termaq13 = cona*(term1q13 - term2q13)
      termaq14 = cona*(term1q14 - term2q14)
      termat1  = cona*(term1t1  - term2t1 )

      termaq21 = cona*(term1q21 - term2q21)
      termaq22 = cona*(term1q22 - term2q22)
      termaq23 = cona*(term1q23 - term2q23)
      termaq24 = cona*(term1q24 - term2q24)
      termat2  = cona*(term1t2  - term2t2 )

      termaq31 = cona*(term1q31 - term2q31)
      termaq32 = cona*(term1q32 - term2q32)
      termaq33 = cona*(term1q33 - term2q33)
      termaq34 = cona*(term1q34 - term2q34)
      termat3  = cona*(term1t3  - term2t3 )

      termaq41 = cona*(term1q41 - term2q41)
      termaq42 = cona*(term1q42 - term2q42)
      termaq43 = cona*(term1q43 - term2q43)
      termaq44 = cona*(term1q44 - term2q44)
      termat4  = cona*(term1t4  - term2t4 )

      termb = -term1 + term2
      termbq11 = conb*(-term1q11 + term2q11)
      termbq12 = conb*(-term1q12 + term2q12)
      termbq13 = conb*(-term1q13 + term2q13)
      termbq14 = conb*(-term1q14 + term2q14)
      termbt1  = conb*(-term1t1  + term2t1)

      termbq21 = conb*(-term1q21 + term2q21)
      termbq22 = conb*(-term1q22 + term2q22)
      termbq23 = conb*(-term1q23 + term2q23)
      termbq24 = conb*(-term1q24 + term2q24)
      termbt2  = conb*(-term1t2  + term2t2)

      termbq31 = conb*(-term1q31 + term2q31)
      termbq32 = conb*(-term1q32 + term2q32)
      termbq33 = conb*(-term1q33 + term2q33)
      termbq34 = conb*(-term1q34 + term2q34)
      termbt3  = conb*(-term1t3  + term2t3)

      termbq41 = conb*(-term1q41 + term2q41)
      termbq42 = conb*(-term1q42 + term2q42)
      termbq43 = conb*(-term1q43 + term2q43)
      termbq44 = conb*(-term1q44 + term2q44)
      termbt4  = conb*(-term1t4  + term2t4 )

!  Remember we took care of the convective terms already

      res1q11 = rmr*termaq11*turb(1,node1) + rmr*termbq11*turb(1,node2)
      res1q12 = rmr*termaq12*turb(1,node1) + rmr*termbq12*turb(1,node2)
      res1q13 = rmr*termaq13*turb(1,node1) + rmr*termbq13*turb(1,node2)
      res1q14 = rmr*termaq14*turb(1,node1) + rmr*termbq14*turb(1,node2)
      res1t1  = rmr*((turb(1,node1)*termat1) + (turb(1,node2)*termbt1))

      res1q21 = rmr*termaq21*turb(1,node1) + rmr*termbq21*turb(1,node2)
      res1q22 = rmr*termaq22*turb(1,node1) + rmr*termbq22*turb(1,node2)
      res1q23 = rmr*termaq23*turb(1,node1) + rmr*termbq23*turb(1,node2)
      res1q24 = rmr*termaq24*turb(1,node1) + rmr*termbq24*turb(1,node2)
      res1t2  = rmr*((turb(1,node1)*termat2) + (turb(1,node2)*termbt2))

      res1q31 = rmr*termaq31*turb(1,node1) + rmr*termbq31*turb(1,node2)
      res1q32 = rmr*termaq32*turb(1,node1) + rmr*termbq32*turb(1,node2)
      res1q33 = rmr*termaq33*turb(1,node1) + rmr*termbq33*turb(1,node2)
      res1q34 = rmr*termaq34*turb(1,node1) + rmr*termbq34*turb(1,node2)
      res1t3  = rmr*((turb(1,node1)*termat3) + (turb(1,node2)*termbt3))

      res1q41 = rmr*termaq41*turb(1,node1) + rmr*termbq41*turb(1,node2)
      res1q42 = rmr*termaq42*turb(1,node1) + rmr*termbq42*turb(1,node2)
      res1q43 = rmr*termaq43*turb(1,node1) + rmr*termbq43*turb(1,node2)
      res1q44 = rmr*termaq44*turb(1,node1) + rmr*termbq44*turb(1,node2)
      res1t4  = rmr*((turb(1,node1)*termat4) + (turb(1,node2)*termbt4))

!  Diagonal entry

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam(i) = coltag(5,node1)*rlamb(5,node1,i)

          if(node1 <= nnodes0) then
            res(1,node1,i) = res(1,node1,i) + res1q11*rlam(i)
            res(2,node1,i) = res(2,node1,i) + res1q12*rlam(i)
            res(3,node1,i) = res(3,node1,i) + res1q13*rlam(i)
            res(4,node1,i) = res(4,node1,i) + res1q14*rlam(i)
            res(5,node1,i) = res(5,node1,i) + res1t1 *rlam(i)
          endif
        end do
      endif

!  Now do the off-diagonals

      if ( fill_res ) then
        if(node2 <= nnodes0) then
          do i = 1, nfunctions
            res(1,node2,i) = res(1,node2,i) + res1q21*rlam(i)
            res(2,node2,i) = res(2,node2,i) + res1q22*rlam(i)
            res(3,node2,i) = res(3,node2,i) + res1q23*rlam(i)
            res(4,node2,i) = res(4,node2,i) + res1q24*rlam(i)
            res(5,node2,i) = res(5,node2,i) + res1t2 *rlam(i)
          end do
        endif
      endif

      select case(ie)
      case(1)
        onode1 = inode3
        onode2 = inode4
        oedge1 = iedge2
        oedge2 = iedge3
        oedge3 = iedge4
        oedge4 = iedge5
      case(2)
        onode1 = inode2
        onode2 = inode4
        oedge1 = iedge1
        oedge2 = iedge3
        oedge3 = iedge4
        oedge4 = iedge6
      case(3)
        onode1 = inode2
        onode2 = inode3
        oedge1 = iedge1
        oedge2 = iedge2
        oedge3 = iedge5
        oedge4 = iedge6
      case(4)
        onode1 = inode1
        onode2 = inode4
        oedge1 = iedge1
        oedge2 = iedge5
        oedge3 = iedge2
        oedge4 = iedge6
      case(5)
        onode1 = inode1
        onode2 = inode3
        oedge1 = iedge1
        oedge2 = iedge4
        oedge3 = iedge3
        oedge4 = iedge6
      case(6)
        onode1 = inode1
        onode2 = inode2
        oedge1 = iedge2
        oedge2 = iedge4
        oedge3 = iedge3
        oedge4 = iedge5
      end select

      if ( fill_res ) then
        if(onode1 <= nnodes0) then
          do i = 1, nfunctions
            res(1,onode1,i) = res(1,onode1,i) + res1q31*rlam(i)
            res(2,onode1,i) = res(2,onode1,i) + res1q32*rlam(i)
            res(3,onode1,i) = res(3,onode1,i) + res1q33*rlam(i)
            res(4,onode1,i) = res(4,onode1,i) + res1q34*rlam(i)
            res(5,onode1,i) = res(5,onode1,i) + res1t3 *rlam(i)
          end do
        endif
      endif

      if ( fill_res ) then
        if(onode2 <= nnodes0) then
          do i = 1, nfunctions
            res(1,onode2,i) = res(1,onode2,i) + res1q41*rlam(i)
            res(2,onode2,i) = res(2,onode2,i) + res1q42*rlam(i)
            res(3,onode2,i) = res(3,onode2,i) + res1q43*rlam(i)
            res(4,onode2,i) = res(4,onode2,i) + res1q44*rlam(i)
            res(5,onode2,i) = res(5,onode2,i) + res1t4 *rlam(i)
          end do
        endif
      endif

      if ( fill_a ) then

        idiag = iau(node1)
        if ( node2 <= nnodes0 ) ioff1 = fhelp(2,iedge)

        irow2 = onode1
        irow3 = onode2

        if(node1 == lnode1) then

          if(node1 == eptr(1,oedge1)) then
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(2,oedge1)
          else
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(1,oedge1)
          endif

          if(node1 == eptr(1,oedge2)) then
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(2,oedge2)
          else
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(1,oedge2)
          endif

        else

          if(node1 == eptr(1,oedge3)) then
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(2,oedge3)
          else
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(1,oedge3)
          endif

          if(node1 == eptr(1,oedge4)) then
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(2,oedge4)
          else
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(1,oedge4)
          endif

        endif

        if(node1 <= nnodes0) then
          A(5,1,idiag) = A(5,1,idiag) + res1q11
          A(5,2,idiag) = A(5,2,idiag) + res1q12
          A(5,3,idiag) = A(5,3,idiag) + res1q13
          A(5,4,idiag) = A(5,4,idiag) + res1q14
          A(5,5,idiag) = A(5,5,idiag) + res1t1
        endif

        if(node2 <= nnodes0) then
          A(5,1,ioff1) = A(5,1,ioff1) + res1q21
          A(5,2,ioff1) = A(5,2,ioff1) + res1q22
          A(5,3,ioff1) = A(5,3,ioff1) + res1q23
          A(5,4,ioff1) = A(5,4,ioff1) + res1q24
          A(5,5,ioff1) = A(5,5,ioff1) + res1t2
        endif

        if(irow2 <= nnodes0) then
          A(5,1,ioff2) = A(5,1,ioff2) + res1q31
          A(5,2,ioff2) = A(5,2,ioff2) + res1q32
          A(5,3,ioff2) = A(5,3,ioff2) + res1q33
          A(5,4,ioff2) = A(5,4,ioff2) + res1q34
          A(5,5,ioff2) = A(5,5,ioff2) + res1t3
        endif

        if(irow3 <= nnodes0) then
          A(5,1,ioff3) = A(5,1,ioff3) + res1q41
          A(5,2,ioff3) = A(5,2,ioff3) + res1q42
          A(5,3,ioff3) = A(5,3,ioff3) + res1q43
          A(5,4,ioff3) = A(5,4,ioff3) + res1q44
          A(5,5,ioff3) = A(5,5,ioff3) + res1t4
        endif

      endif

!  Ok that does it for node1 of this edge, now do node2

      select case (ie)
      case (1)
        wcon = wxx12 + wyy12 + wzz12
        term1q11 = dft2e1q11
        term1q12 = dft2e1q12
        term1q13 = dft2e1q13
        term1q14 = dft2e1q14
        term1t1  = dft2e1t1

        term1q21 = dft2e1q21
        term1q22 = dft2e1q22
        term1q23 = dft2e1q23
        term1q24 = dft2e1q24
        term1t2  = dft2e1t2

        term1q31 = dft2e1q31
        term1q32 = dft2e1q32
        term1q33 = dft2e1q33
        term1q34 = dft2e1q34
        term1t3  = dft2e1t3

        term1q41 = dft2e1q41
        term1q42 = dft2e1q42
        term1q43 = dft2e1q43
        term1q44 = dft2e1q44
        term1t4  = dft2e1t4

        term2 = cb2*turb(1,node2)*wcon
        term2q11 = cb2*turb(1,node2)*dft1e1q11
        term2q12 = cb2*turb(1,node2)*dft1e1q12
        term2q13 = cb2*turb(1,node2)*dft1e1q13
        term2q14 = cb2*turb(1,node2)*dft1e1q14
        term2t1  = cb2*turb(1,node2)*dft1e1t1

        term2q21 = cb2*turb(1,node2)*dft1e1q21
        term2q22 = cb2*turb(1,node2)*dft1e1q22
        term2q23 = cb2*turb(1,node2)*dft1e1q23
        term2q24 = cb2*turb(1,node2)*dft1e1q24
        term2t2  = cb2*(turb(1,node2)*dft1e1t2 + wcon)

        term2q31 = cb2*turb(1,node2)*dft1e1q31
        term2q32 = cb2*turb(1,node2)*dft1e1q32
        term2q33 = cb2*turb(1,node2)*dft1e1q33
        term2q34 = cb2*turb(1,node2)*dft1e1q34
        term2t3  = cb2*turb(1,node2)*dft1e1t3

        term2q41 = cb2*turb(1,node2)*dft1e1q41
        term2q42 = cb2*turb(1,node2)*dft1e1q42
        term2q43 = cb2*turb(1,node2)*dft1e1q43
        term2q44 = cb2*turb(1,node2)*dft1e1q44
        term2t4  = cb2*turb(1,node2)*dft1e1t4
      case (2)
        wcon = wxx13 + wyy13 + wzz13
        term1q11 = dft2e2q11
        term1q12 = dft2e2q12
        term1q13 = dft2e2q13
        term1q14 = dft2e2q14
        term1t1  = dft2e2t1

        term1q31 = dft2e2q21
        term1q32 = dft2e2q22
        term1q33 = dft2e2q23
        term1q34 = dft2e2q24
        term1t3  = dft2e2t2

        term1q21 = dft2e2q31
        term1q22 = dft2e2q32
        term1q23 = dft2e2q33
        term1q24 = dft2e2q34
        term1t2  = dft2e2t3

        term1q41 = dft2e2q41
        term1q42 = dft2e2q42
        term1q43 = dft2e2q43
        term1q44 = dft2e2q44
        term1t4  = dft2e2t4

        term2 = cb2*turb(1,node2)*wcon
        term2q11 = cb2*turb(1,node2)*dft1e2q11
        term2q12 = cb2*turb(1,node2)*dft1e2q12
        term2q13 = cb2*turb(1,node2)*dft1e2q13
        term2q14 = cb2*turb(1,node2)*dft1e2q14
        term2t1  = cb2*turb(1,node2)*dft1e2t1

        term2q31 = cb2*turb(1,node2)*dft1e2q21
        term2q32 = cb2*turb(1,node2)*dft1e2q22
        term2q33 = cb2*turb(1,node2)*dft1e2q23
        term2q34 = cb2*turb(1,node2)*dft1e2q24
        term2t3  = cb2*turb(1,node2)*dft1e2t2

        term2q21 = cb2*turb(1,node2)*dft1e2q31
        term2q22 = cb2*turb(1,node2)*dft1e2q32
        term2q23 = cb2*turb(1,node2)*dft1e2q33
        term2q24 = cb2*turb(1,node2)*dft1e2q34
        term2t2  = cb2*(turb(1,node2)*dft1e2t3 + wcon)

        term2q41 = cb2*turb(1,node2)*dft1e2q41
        term2q42 = cb2*turb(1,node2)*dft1e2q42
        term2q43 = cb2*turb(1,node2)*dft1e2q43
        term2q44 = cb2*turb(1,node2)*dft1e2q44
        term2t4  = cb2*turb(1,node2)*dft1e2t4
      case (3)
        wcon = wxx14 + wyy14 + wzz14
        term1q11 = dft2e3q11
        term1q12 = dft2e3q12
        term1q13 = dft2e3q13
        term1q14 = dft2e3q14
        term1t1  = dft2e3t1

        term1q41 = dft2e3q31
        term1q42 = dft2e3q32
        term1q43 = dft2e3q33
        term1q44 = dft2e3q34
        term1t4  = dft2e3t3

        term1q21 = dft2e3q41
        term1q22 = dft2e3q42
        term1q23 = dft2e3q43
        term1q24 = dft2e3q44
        term1t2  = dft2e3t4

        term1q31 = dft2e3q21
        term1q32 = dft2e3q22
        term1q33 = dft2e3q23
        term1q34 = dft2e3q24
        term1t3  = dft2e3t2

        term2 = cb2*turb(1,node2)*wcon
        term2q11 = cb2*turb(1,node2)*dft1e3q11
        term2q12 = cb2*turb(1,node2)*dft1e3q12
        term2q13 = cb2*turb(1,node2)*dft1e3q13
        term2q14 = cb2*turb(1,node2)*dft1e3q14
        term2t1  = cb2*turb(1,node2)*dft1e3t1

        term2q41 = cb2*turb(1,node2)*dft1e3q31
        term2q42 = cb2*turb(1,node2)*dft1e3q32
        term2q43 = cb2*turb(1,node2)*dft1e3q33
        term2q44 = cb2*turb(1,node2)*dft1e3q34
        term2t4  = cb2*turb(1,node2)*dft1e3t3

        term2q21 = cb2*turb(1,node2)*dft1e3q41
        term2q22 = cb2*turb(1,node2)*dft1e3q42
        term2q23 = cb2*turb(1,node2)*dft1e3q43
        term2q24 = cb2*turb(1,node2)*dft1e3q44
        term2t2  = cb2*(turb(1,node2)*dft1e3t4 + wcon)

        term2q31 = cb2*turb(1,node2)*dft1e3q21
        term2q32 = cb2*turb(1,node2)*dft1e3q22
        term2q33 = cb2*turb(1,node2)*dft1e3q23
        term2q34 = cb2*turb(1,node2)*dft1e3q24
        term2t3  = cb2*turb(1,node2)*dft1e3t2
      case (4)
        wcon = wxx23 + wyy23 + wzz23
        term1q21 = dft2e4q31
        term1q22 = dft2e4q32
        term1q23 = dft2e4q33
        term1q24 = dft2e4q34
        term1t2  = dft2e4t3

        term1q31 = dft2e4q11
        term1q32 = dft2e4q12
        term1q33 = dft2e4q13
        term1q34 = dft2e4q14
        term1t3  = dft2e4t1

        term1q11 = dft2e4q21
        term1q12 = dft2e4q22
        term1q13 = dft2e4q23
        term1q14 = dft2e4q24
        term1t1  = dft2e4t2

        term1q41 = dft2e4q41
        term1q42 = dft2e4q42
        term1q43 = dft2e4q43
        term1q44 = dft2e4q44
        term1t4  = dft2e4t4

        term2 = cb2*turb(1,node2)*wcon
        term2q21 = cb2*turb(1,node2)*dft1e4q31
        term2q22 = cb2*turb(1,node2)*dft1e4q32
        term2q23 = cb2*turb(1,node2)*dft1e4q33
        term2q24 = cb2*turb(1,node2)*dft1e4q34
        term2t2  = cb2*(turb(1,node2)*dft1e4t3 + wcon)

        term2q31 = cb2*turb(1,node2)*dft1e4q11
        term2q32 = cb2*turb(1,node2)*dft1e4q12
        term2q33 = cb2*turb(1,node2)*dft1e4q13
        term2q34 = cb2*turb(1,node2)*dft1e4q14
        term2t3  = cb2*turb(1,node2)*dft1e4t1

        term2q11 = cb2*turb(1,node2)*dft1e4q21
        term2q12 = cb2*turb(1,node2)*dft1e4q22
        term2q13 = cb2*turb(1,node2)*dft1e4q23
        term2q14 = cb2*turb(1,node2)*dft1e4q24
        term2t1  = cb2*turb(1,node2)*dft1e4t2

        term2q41 = cb2*turb(1,node2)*dft1e4q41
        term2q42 = cb2*turb(1,node2)*dft1e4q42
        term2q43 = cb2*turb(1,node2)*dft1e4q43
        term2q44 = cb2*turb(1,node2)*dft1e4q44
        term2t4  = cb2*turb(1,node2)*dft1e4t4
      case (5)
        wcon = wxx24 + wyy24 + wzz24
        term1q21 = dft2e5q41
        term1q22 = dft2e5q42
        term1q23 = dft2e5q43
        term1q24 = dft2e5q44
        term1t2  = dft2e5t4

        term1q41 = dft2e5q31
        term1q42 = dft2e5q32
        term1q43 = dft2e5q33
        term1q44 = dft2e5q34
        term1t4  = dft2e5t3

        term1q11 = dft2e5q21
        term1q12 = dft2e5q22
        term1q13 = dft2e5q23
        term1q14 = dft2e5q24
        term1t1  = dft2e5t2

        term1q31 = dft2e5q11
        term1q32 = dft2e5q12
        term1q33 = dft2e5q13
        term1q34 = dft2e5q14
        term1t3  = dft2e5t1

        term2 = cb2*turb(1,node2)*wcon
        term2q21 = cb2*turb(1,node2)*dft1e5q41
        term2q22 = cb2*turb(1,node2)*dft1e5q42
        term2q23 = cb2*turb(1,node2)*dft1e5q43
        term2q24 = cb2*turb(1,node2)*dft1e5q44
        term2t2  = cb2*(turb(1,node2)*dft1e5t4 + wcon)

        term2q41 = cb2*turb(1,node2)*dft1e5q31
        term2q42 = cb2*turb(1,node2)*dft1e5q32
        term2q43 = cb2*turb(1,node2)*dft1e5q33
        term2q44 = cb2*turb(1,node2)*dft1e5q34
        term2t4  = cb2*turb(1,node2)*dft1e5t3

        term2q11 = cb2*turb(1,node2)*dft1e5q21
        term2q12 = cb2*turb(1,node2)*dft1e5q22
        term2q13 = cb2*turb(1,node2)*dft1e5q23
        term2q14 = cb2*turb(1,node2)*dft1e5q24
        term2t1  = cb2*turb(1,node2)*dft1e5t2

        term2q31 = cb2*turb(1,node2)*dft1e5q11
        term2q32 = cb2*turb(1,node2)*dft1e5q12
        term2q33 = cb2*turb(1,node2)*dft1e5q13
        term2q34 = cb2*turb(1,node2)*dft1e5q14
        term2t3  = cb2*turb(1,node2)*dft1e5t1
      case (6)
        wcon = wxx34 + wyy34 + wzz34
        term1q31 = dft2e6q11
        term1q32 = dft2e6q12
        term1q33 = dft2e6q13
        term1q34 = dft2e6q14
        term1t3  = dft2e6t1

        term1q41 = dft2e6q21
        term1q42 = dft2e6q22
        term1q43 = dft2e6q23
        term1q44 = dft2e6q24
        term1t4  = dft2e6t2

        term1q11 = dft2e6q31
        term1q12 = dft2e6q32
        term1q13 = dft2e6q33
        term1q14 = dft2e6q34
        term1t1  = dft2e6t3

        term1q21 = dft2e6q41
        term1q22 = dft2e6q42
        term1q23 = dft2e6q43
        term1q24 = dft2e6q44
        term1t2  = dft2e6t4

        term2 = cb2*turb(1,node2)*wcon
        term2q31 = cb2*turb(1,node2)*dft1e6q11
        term2q32 = cb2*turb(1,node2)*dft1e6q12
        term2q33 = cb2*turb(1,node2)*dft1e6q13
        term2q34 = cb2*turb(1,node2)*dft1e6q14
        term2t3  = cb2*turb(1,node2)*dft1e6t1

        term2q41 = cb2*turb(1,node2)*dft1e6q21
        term2q42 = cb2*turb(1,node2)*dft1e6q22
        term2q43 = cb2*turb(1,node2)*dft1e6q23
        term2q44 = cb2*turb(1,node2)*dft1e6q24
        term2t4  = cb2*turb(1,node2)*dft1e6t2

        term2q11 = cb2*turb(1,node2)*dft1e6q31
        term2q12 = cb2*turb(1,node2)*dft1e6q32
        term2q13 = cb2*turb(1,node2)*dft1e6q33
        term2q14 = cb2*turb(1,node2)*dft1e6q34
        term2t1  = cb2*turb(1,node2)*dft1e6t3

        term2q21 = cb2*turb(1,node2)*dft1e6q41
        term2q22 = cb2*turb(1,node2)*dft1e6q42
        term2q23 = cb2*turb(1,node2)*dft1e6q43
        term2q24 = cb2*turb(1,node2)*dft1e6q44
        term2t2  = cb2*(turb(1,node2)*dft1e6t4 + wcon)
      end select

      term1 = dft2(iedge)
      term2 = cb2*turb(1,node2)*dft1(iedge)
      terma = Max( term1 - term2, 0.0_dp)
      termb = Min(-term1 + term2, 0.0_dp)
      cona = 1.0_dp
      conb = 1.0_dp
      if(term1 - term2 <= 0.0_dp)cona = 0.0_dp
      if(-term1 + term2 >= 0.0_dp)conb = 0.0_dp
      terma =  term1 - term2
      termaq11 = cona*(term1q11 - term2q11)
      termaq12 = cona*(term1q12 - term2q12)
      termaq13 = cona*(term1q13 - term2q13)
      termaq14 = cona*(term1q14 - term2q14)
      termat1  = cona*(term1t1  - term2t1 )

      termaq21 = cona*(term1q21 - term2q21)
      termaq22 = cona*(term1q22 - term2q22)
      termaq23 = cona*(term1q23 - term2q23)
      termaq24 = cona*(term1q24 - term2q24)
      termat2  = cona*(term1t2  - term2t2 )

      termaq31 = cona*(term1q31 - term2q31)
      termaq32 = cona*(term1q32 - term2q32)
      termaq33 = cona*(term1q33 - term2q33)
      termaq34 = cona*(term1q34 - term2q34)
      termat3  = cona*(term1t3  - term2t3 )

      termaq41 = cona*(term1q41 - term2q41)
      termaq42 = cona*(term1q42 - term2q42)
      termaq43 = cona*(term1q43 - term2q43)
      termaq44 = cona*(term1q44 - term2q44)
      termat4  = cona*(term1t4  - term2t4 )

      termb = -term1 + term2
      termbq11 = conb*(-term1q11 + term2q11)
      termbq12 = conb*(-term1q12 + term2q12)
      termbq13 = conb*(-term1q13 + term2q13)
      termbq14 = conb*(-term1q14 + term2q14)
      termbt1  = conb*(-term1t1  + term2t1)

      termbq21 = conb*(-term1q21 + term2q21)
      termbq22 = conb*(-term1q22 + term2q22)
      termbq23 = conb*(-term1q23 + term2q23)
      termbq24 = conb*(-term1q24 + term2q24)
      termbt2  = conb*(-term1t2  + term2t2)

      termbq31 = conb*(-term1q31 + term2q31)
      termbq32 = conb*(-term1q32 + term2q32)
      termbq33 = conb*(-term1q33 + term2q33)
      termbq34 = conb*(-term1q34 + term2q34)
      termbt3  = conb*(-term1t3  + term2t3)

      termbq41 = conb*(-term1q41 + term2q41)
      termbq42 = conb*(-term1q42 + term2q42)
      termbq43 = conb*(-term1q43 + term2q43)
      termbq44 = conb*(-term1q44 + term2q44)
      termbt4  = conb*(-term1t4  + term2t4 )

!  Remember we took care of the convective terms already

      res2q11 = rmr*termaq11*turb(1,node2) + rmr*termbq11*turb(1,node1)
      res2q12 = rmr*termaq12*turb(1,node2) + rmr*termbq12*turb(1,node1)
      res2q13 = rmr*termaq13*turb(1,node2) + rmr*termbq13*turb(1,node1)
      res2q14 = rmr*termaq14*turb(1,node2) + rmr*termbq14*turb(1,node1)
      res2t1  = rmr*((turb(1,node2)*termat1) + (turb(1,node1)*termbt1))

      res2q21 = rmr*termaq21*turb(1,node2) + rmr*termbq21*turb(1,node1)
      res2q22 = rmr*termaq22*turb(1,node2) + rmr*termbq22*turb(1,node1)
      res2q23 = rmr*termaq23*turb(1,node2) + rmr*termbq23*turb(1,node1)
      res2q24 = rmr*termaq24*turb(1,node2) + rmr*termbq24*turb(1,node1)
      res2t2  = rmr*((turb(1,node2)*termat2) + (turb(1,node1)*termbt2))

      res2q31 = rmr*termaq31*turb(1,node2) + rmr*termbq31*turb(1,node1)
      res2q32 = rmr*termaq32*turb(1,node2) + rmr*termbq32*turb(1,node1)
      res2q33 = rmr*termaq33*turb(1,node2) + rmr*termbq33*turb(1,node1)
      res2q34 = rmr*termaq34*turb(1,node2) + rmr*termbq34*turb(1,node1)
      res2t3  = rmr*((turb(1,node2)*termat3) + (turb(1,node1)*termbt3))

      res2q41 = rmr*termaq41*turb(1,node2) + rmr*termbq41*turb(1,node1)
      res2q42 = rmr*termaq42*turb(1,node2) + rmr*termbq42*turb(1,node1)
      res2q43 = rmr*termaq43*turb(1,node2) + rmr*termbq43*turb(1,node1)
      res2q44 = rmr*termaq44*turb(1,node2) + rmr*termbq44*turb(1,node1)
      res2t4  = rmr*((turb(1,node2)*termat4) + (turb(1,node1)*termbt4))

!  Diagonal entry

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam(i) = coltag(5,node2)*rlamb(5,node2,i)

          if(node2 <= nnodes0) then
            res(1,node2,i) = res(1,node2,i) + res2q21*rlam(i)
            res(2,node2,i) = res(2,node2,i) + res2q22*rlam(i)
            res(3,node2,i) = res(3,node2,i) + res2q23*rlam(i)
            res(4,node2,i) = res(4,node2,i) + res2q24*rlam(i)
            res(5,node2,i) = res(5,node2,i) + res2t2 *rlam(i)
          endif
        end do
      endif

!  Now do the off-diagonals

      if ( fill_res ) then
        if(node1 <= nnodes0) then
          do i = 1, nfunctions
            res(1,node1,i) = res(1,node1,i) + res2q11*rlam(i)
            res(2,node1,i) = res(2,node1,i) + res2q12*rlam(i)
            res(3,node1,i) = res(3,node1,i) + res2q13*rlam(i)
            res(4,node1,i) = res(4,node1,i) + res2q14*rlam(i)
            res(5,node1,i) = res(5,node1,i) + res2t1 *rlam(i)
          end do
        endif
      endif

      select case(ie)
      case(1)
        onode1 = inode3
        onode2 = inode4
        oedge1 = iedge2
        oedge2 = iedge3
        oedge3 = iedge4
        oedge4 = iedge5
      case(2)
        onode1 = inode2
        onode2 = inode4
        oedge1 = iedge1
        oedge2 = iedge3
        oedge3 = iedge4
        oedge4 = iedge6
      case(3)
        onode1 = inode2
        onode2 = inode3
        oedge1 = iedge1
        oedge2 = iedge2
        oedge3 = iedge5
        oedge4 = iedge6
      case(4)
        onode1 = inode1
        onode2 = inode4
        oedge1 = iedge1
        oedge2 = iedge5
        oedge3 = iedge2
        oedge4 = iedge6
      case(5)
        onode1 = inode1
        onode2 = inode3
        oedge1 = iedge1
        oedge2 = iedge4
        oedge3 = iedge3
        oedge4 = iedge6
      case(6)
        onode1 = inode1
        onode2 = inode2
        oedge1 = iedge2
        oedge2 = iedge4
        oedge3 = iedge3
        oedge4 = iedge5
      end select

      if ( fill_res ) then
        if(onode1 <= nnodes0) then
          do i = 1, nfunctions
            res(1,onode1,i) = res(1,onode1,i) + res2q31*rlam(i)
            res(2,onode1,i) = res(2,onode1,i) + res2q32*rlam(i)
            res(3,onode1,i) = res(3,onode1,i) + res2q33*rlam(i)
            res(4,onode1,i) = res(4,onode1,i) + res2q34*rlam(i)
            res(5,onode1,i) = res(5,onode1,i) + res2t3 *rlam(i)
          end do
        endif
      endif

      if ( fill_res ) then
        if(onode2 <= nnodes0) then
          do i = 1, nfunctions
            res(1,onode2,i) = res(1,onode2,i) + res2q41*rlam(i)
            res(2,onode2,i) = res(2,onode2,i) + res2q42*rlam(i)
            res(3,onode2,i) = res(3,onode2,i) + res2q43*rlam(i)
            res(4,onode2,i) = res(4,onode2,i) + res2q44*rlam(i)
            res(5,onode2,i) = res(5,onode2,i) + res2t4 *rlam(i)
          end do
        endif
      endif

      if ( fill_a ) then

        idiag = iau(node2)
        if ( node1 <= nnodes0 ) ioff1 = fhelp(1,iedge)

        irow2 = onode1
        irow3 = onode2

        if(node2 == lnode2) then

          if(node2 == eptr(1,oedge3)) then
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(2,oedge3)
          else
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(1,oedge3)
          endif

          if(node2 == eptr(1,oedge4)) then
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(2,oedge4)
          else
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(1,oedge4)
          endif

        else

          if(node2 == eptr(1,oedge1)) then
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(2,oedge1)
          else
            if ( irow2 <= nnodes0 ) ioff2 = fhelp(1,oedge1)
          endif

          if(node2 == eptr(1,oedge2)) then
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(2,oedge2)
          else
            if ( irow3 <= nnodes0 ) ioff3 = fhelp(1,oedge2)
          endif

        endif

        if(node2 <= nnodes0) then
          A(5,1,idiag) = A(5,1,idiag) + res2q21
          A(5,2,idiag) = A(5,2,idiag) + res2q22
          A(5,3,idiag) = A(5,3,idiag) + res2q23
          A(5,4,idiag) = A(5,4,idiag) + res2q24
          A(5,5,idiag) = A(5,5,idiag) + res2t2
        endif

        if(node1 <= nnodes0) then
          A(5,1,ioff1) = A(5,1,ioff1) + res2q11
          A(5,2,ioff1) = A(5,2,ioff1) + res2q12
          A(5,3,ioff1) = A(5,3,ioff1) + res2q13
          A(5,4,ioff1) = A(5,4,ioff1) + res2q14
          A(5,5,ioff1) = A(5,5,ioff1) + res2t1
        endif

        if(irow2 <= nnodes0) then
          A(5,1,ioff2) = A(5,1,ioff2) + res2q31
          A(5,2,ioff2) = A(5,2,ioff2) + res2q32
          A(5,3,ioff2) = A(5,3,ioff2) + res2q33
          A(5,4,ioff2) = A(5,4,ioff2) + res2q34
          A(5,5,ioff2) = A(5,5,ioff2) + res2t3
        endif

        if(irow3 <= nnodes0) then
          A(5,1,ioff3) = A(5,1,ioff3) + res2q41
          A(5,2,ioff3) = A(5,2,ioff3) + res2q42
          A(5,3,ioff3) = A(5,3,ioff3) + res2q43
          A(5,4,ioff3) = A(5,4,ioff3) + res2q44
          A(5,5,ioff3) = A(5,5,ioff3) + res2t4
        endif

      endif

      end do edge_loop

    enddo cell_loop_1020

  end subroutine turbpart_tet_diffusioni


!================================= TURBPART_SOURCEI ==========================80
!
!                                 t
!  This routine computes the dR/dQ * lambda contribution from the
!  turbulence model for the residual
!
!  source term pieces
!
!=============================================================================80
  subroutine turbpart_sourcei(nnodes0,nnodes01,gradx,grady,gradz,turb,slen,vol,&
                              nedge,nedgeLoc,eptr,ra,xn,yn,zn,nfunctions,ndim, &
                              adim,n_turb,coltag,symmetry,rlam,res,iau,fhelp,a)

    use kinddefs,         only : dp
    use info_depr,        only : re
    use turb_sa_const,    only : ct3, cv1, vkar, cw2, cw3, ct4, cb1, cw1,      &
                                 dacles_mariani, use_edwards_mod
    use design_types,     only : max_functions
    use lmpi,             only : lmpi_master, lmpi_die
    use flux_symmetry,    only : has_x_symmetry, has_y_symmetry, has_z_symmetry
    use adjoint_switches, only : use_bp_model

    integer, intent(in) :: nnodes0,nnodes01,nfunctions,n_turb
    integer, intent(in) :: ndim,adim
    integer, intent(in) :: nedge,nedgeLoc

    integer, dimension(2,nedge), intent(in) :: eptr
    integer, dimension(:),       intent(in) :: symmetry
    integer, dimension(:),       intent(in), optional :: iau
    integer, dimension(:,:),     intent(in), optional :: fhelp

    real(dp),dimension(ndim,nnodes01),   intent(in) :: gradx
    real(dp),dimension(ndim,nnodes01),   intent(in) :: grady
    real(dp),dimension(ndim,nnodes01),   intent(in) :: gradz
    real(dp),dimension(nnodes01),        intent(in) :: slen,vol
    real(dp),dimension(n_turb,nnodes01), intent(in) :: turb
    real(dp),dimension(nedge),           intent(in) :: ra
    real(dp),dimension(nedge),           intent(in) :: xn,yn,zn
    real(dp),dimension(adim,nnodes01),   intent(in) :: coltag
    real(dp),dimension(:,:,:),           intent(in),    optional :: rlam
    real(dp),dimension(:,:,:),           intent(inout), optional :: res
    real(dp),dimension(:,:,:),           intent(inout), optional :: a

    integer :: idiag,ioff
    integer :: i,node1,node2,n,j

    real(dp) :: xnorm,ynorm,znorm,xmr

    real(dp) :: area,onesix

    real(dp) :: chi
    real(dp) :: chiq1,chiq2,chiq3,chiq4,chit
    real(dp) :: base
    real(dp) :: baseq1,baseq2,baseq3,baseq4,baset
    real(dp) :: turb_abs
    real(dp) :: turb_absq1,turb_absq2,turb_absq3,turb_absq4,turb_abst
    real(dp) :: s_bar
    real(dp) :: s_barq1,s_barq2,s_barq3,s_barq4,s_bart
    real(dp) :: term1
    real(dp) :: term1q1,term1q2,term1q3,term1q4,term1t
    real(dp) :: term2
    real(dp) :: term2q1,term2q2,term2q3,term2q4,term2t
    real(dp) :: temp_arg
    real(dp) :: temp_argq1,temp_argq2,temp_argq3,temp_argq4,temp_argt

    real(dp) :: bot,arg,bottom

    real(dp) :: destq1,destq2,destq3,destq4,destt
    real(dp) :: destq11,destq12,destq13,destq14,destt1
    real(dp) :: destq21,destq22,destq23,destq24,destt2

    real(dp) :: fv1
    real(dp) :: fv1q1,fv1q2,fv1q3,fv1q4,fv1t
    real(dp) :: fv2
    real(dp) :: fv2q1,fv2q2,fv2q3,fv2q4,fv2t

    real(dp) :: distance,fivesix,factor

    real(dp) :: gg
    real(dp) :: ggq1,ggq2,ggq3,ggq4,ggt
    real(dp) :: ggq11,ggq12,ggq13,ggq14
    real(dp) :: ggq21,ggq22,ggq23,ggq24

    real(dp) :: fw
    real(dp) :: fwq1,fwq2,fwq3,fwq4,fwt
    real(dp) :: fwq11,fwq12,fwq13,fwq14
    real(dp) :: fwq21,fwq22,fwq23,fwq24

    real(dp) :: ft2
    real(dp) :: ft2q1,ft2q2,ft2q3,ft2q4,ft2t
    real(dp) :: ft2q11,ft2q12,ft2q13,ft2q14
    real(dp) :: ft2q21,ft2q22,ft2q23,ft2q24

    real(dp) :: prodq1,prodq2,prodq3,prodq4,prodt
    real(dp) :: prodq11,prodq12,prodq13,prodq14,prodt1
    real(dp) :: prodq21,prodq22,prodq23,prodq24,prodt2

    real(dp) :: resq1,resq2,resq3,resq4,rest

    real(dp) :: rbot
    real(dp) :: rbotq1,rbotq2,rbotq3,rbotq4,rbott

    real(dp) :: q2q11,q2q12,q2q13,q2q14,q2t1
    real(dp) :: q2q21,q2q22,q2q23,q2q24,q2t2

    real(dp) :: q3q11,q3q12,q3q13,q3q14,q3t1
    real(dp) :: q3q21,q3q22,q3q23,q3q24,q3t2

    real(dp) :: q4q11,q4q12,q4q13,q4q14,q4t1
    real(dp) :: q4q21,q4q22,q4q23,q4q24,q4t2

    real(dp) :: res1q11,res1q12,res1q13,res1q14,res1t1
    real(dp) :: res1q21,res1q22,res1q23,res1q24,res1t2

    real(dp) :: res2q11,res2q12,res2q13,res2q14,res2t1
    real(dp) :: res2q21,res2q22,res2q23,res2q24,res2t2

    real(dp) :: rpiece
    real(dp) :: rpieceq1,rpieceq2,rpieceq3,rpieceq4,rpiecet

    real(dp) :: rpiece1
    real(dp) :: rpiece1q1,rpiece1q2,rpiece1q3,rpiece1q4,rpiece1t

    real(dp) :: rpiece2
    real(dp) :: rpiece2q1,rpiece2q2,rpiece2q3,rpiece2q4,rpiece2t

    real(dp) :: rnu
    real(dp) :: rnuq1,rnuq2,rnuq3,rnuq4,rnut

    real(dp) :: s
    real(dp) :: sq11,sq12,sq13,sq14,st1
    real(dp) :: sq21,sq22,sq23,sq24,st2

    real(dp) :: sw
    real(dp) :: swq1,swq2,swq3,swq4,swt
    real(dp) :: swq11,swq12,swq13,swq14,swt1
    real(dp) :: swq21,swq22,swq23,swq24,swt2

    real(dp) :: rr
    real(dp) :: rrq1,rrq2,rrq3,rrq4,rrt
    real(dp) :: rrq11,rrq12,rrq13,rrq14
    real(dp) :: rrq21,rrq22,rrq23,rrq24

    real(dp) :: rtop
    real(dp) :: rtopq1,rtopq2,rtopq3,rtopq4,rtopt

    real(dp) :: sourceq1,sourceq2,sourceq3,sourceq4,sourcet
    real(dp) :: sourceq11,sourceq12,sourceq13,sourceq14,sourcet1
    real(dp) :: sourceq21,sourceq22,sourceq23,sourceq24,sourcet2

    real(dp) :: tanharg
    real(dp) :: tanhargq1,tanhargq2,tanhargq3,tanhargq4,tanhargt
    real(dp) :: tanhargq11,tanhargq12,tanhargq13,tanhargq14
    real(dp) :: tanhargq21,tanhargq22,tanhargq23,tanhargq24

    real(dp) :: term,vkar2

    real(dp) :: uy
    real(dp) :: uyq11,uyq12,uyq13,uyq14,uyt1
    real(dp) :: uyq21,uyq22,uyq23,uyq24,uyt2

    real(dp) :: uz
    real(dp) :: uzq11,uzq12,uzq13,uzq14,uzt1
    real(dp) :: uzq21,uzq22,uzq23,uzq24,uzt2

    real(dp) :: vx
    real(dp) :: vxq11,vxq12,vxq13,vxq14,vxt1
    real(dp) :: vxq21,vxq22,vxq23,vxq24,vxt2

    real(dp) :: vz
    real(dp) :: vzq11,vzq12,vzq13,vzq14,vzt1
    real(dp) :: vzq21,vzq22,vzq23,vzq24,vzt2

    real(dp) :: wx
    real(dp) :: wxq11,wxq12,wxq13,wxq14,wxt1
    real(dp) :: wxq21,wxq22,wxq23,wxq24,wxt2

    real(dp) :: wy
    real(dp) :: wyq11,wyq12,wyq13,wyq14,wyt1
    real(dp) :: wyq21,wyq22,wyq23,wyq24,wyt2

    real(dp) :: ux,vy,wz,sij_mag
    real(dp) :: uxq11,uxq12,uxq13,uxq14,uxt1
    real(dp) :: uxq21,uxq22,uxq23,uxq24,uxt2
    real(dp) :: vyq11,vyq12,vyq13,vyq14,vyt1
    real(dp) :: vyq21,vyq22,vyq23,vyq24,vyt2
    real(dp) :: wzq11,wzq12,wzq13,wzq14,wzt1
    real(dp) :: wzq21,wzq22,wzq23,wzq24,wzt2
    real(dp) :: sij_magq11,sij_magq12,sij_magq13,sij_magq14,sij_magt1
    real(dp) :: sij_magq21,sij_magq22,sij_magq23,sij_magq24,sij_magt2

    real(dp) :: term1q11,term1q12,term1q13,term1q14,term1t1
    real(dp) :: term1q21,term1q22,term1q23,term1q24,term1t2
    real(dp) :: term2q11,term2q12,term2q13,term2q14,term2t1
    real(dp) :: term2q21,term2q22,term2q23,term2q24,term2t2

    real(dp), dimension(max_functions) :: rlam5

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_half= 0.5_dp

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(rlam) ) then
        if ( lmpi_master ) then
          write(*,*)'res requested in turbpart_source but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if (.not.present(iau) .or. .not.present(fhelp)) then
        if ( lmpi_master ) then
          write(*,*)'a requested in turbpart_source but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    if ( use_bp_model ) then
      if ( use_edwards_mod .or. dacles_mariani ) then
        write(*,*) 'Option not accounted for in turbpart_source...'
        call lmpi_die
        stop
      endif
    endif

    turb_abs   = 0.0_dp
    turb_absq1 = 0.0_dp
    turb_absq2 = 0.0_dp
    turb_absq3 = 0.0_dp
    turb_absq4 = 0.0_dp
    turb_abst  = 0.0_dp

!  For this part, we'll hold vorticity constant
!  Pick it up later in another loop

    onesix = 1.0_dp/6.0_dp
    node_loop_source : do i = 1,nnodes0

      if (coltag(5,i) < 0.1_dp) cycle node_loop_source

      ux   = gradx(2,i)
      uy   = grady(2,i)
      uz   = gradz(2,i)
      vx   = gradx(3,i)
      vy   = grady(3,i)
      vz   = gradz(3,i)
      wx   = gradx(4,i)
      wy   = grady(4,i)
      wz   = gradz(4,i)

      rnu  = 1.0_dp
      rnuq1 = 0.0_dp
      rnuq2 = 0.0_dp
      rnuq3 = 0.0_dp
      rnuq4 = 0.0_dp
      rnut  = 0.0_dp

! Old way of getting Sw

      edwards_chi1 : if(.not. use_edwards_mod) then

        chi  = turb(1,i)/rnu
        chiq1 = -turb(1,i)/rnu/rnu * rnuq1
        chiq2 = -turb(1,i)/rnu/rnu * rnuq2
        chiq3 = -turb(1,i)/rnu/rnu * rnuq3
        chiq4 = -turb(1,i)/rnu/rnu * rnuq4
        chit  = (rnu - turb(1,i)*rnut) / rnu / rnu

        if ( use_bp_model ) then
          if ( chi < 10.0_dp ) then
            base = 1.0_dp + exp( 20.0_dp*chi )
              baseq1 = exp(20.0_dp*chi)*20.0_dp*chiq1
              baseq2 = exp(20.0_dp*chi)*20.0_dp*chiq2
              baseq3 = exp(20.0_dp*chi)*20.0_dp*chiq3
              baseq4 = exp(20.0_dp*chi)*20.0_dp*chiq4
              baset = exp(20.0_dp*chi)*20.0_dp*chit
            chi = 0.05_dp * log (base)
              chiq1 = 0.05_dp/base*baseq1
              chiq2 = 0.05_dp/base*baseq2
              chiq3 = 0.05_dp/base*baseq3
              chiq4 = 0.05_dp/base*baseq4
              chit = 0.05_dp/base*baset
          endif
        endif

      else edwards_chi1
        chi  = (turb(1,i)+1.e-12_dp)/rnu
        chiq1 = -(turb(1,i)+1.e-12_dp)/rnu/rnu * rnuq1
        chiq2 = -(turb(1,i)+1.e-12_dp)/rnu/rnu * rnuq2
        chiq3 = -(turb(1,i)+1.e-12_dp)/rnu/rnu * rnuq3
        chiq4 = -(turb(1,i)+1.e-12_dp)/rnu/rnu * rnuq4
        chit  = (rnu - (turb(1,i)+1.e-12_dp)*rnut) / rnu / rnu
      endif edwards_chi1

      if ( use_bp_model .and. chi < -5.0_dp ) then
        fv1   = 0.0_dp
        fv1q1 = 0.0_dp
        fv1q2 = 0.0_dp
        fv1q3 = 0.0_dp
        fv1q4 = 0.0_dp
        fv1t  = 0.0_dp
      else
        fv1  = chi**3/(chi**3 + cv1**3)
        fv1q1 = ( (chi**3 + cv1**3)*3.0_dp*chi**2*chiq1                        &
                 - chi**3*3.0_dp*chi**2*chiq1 ) / (chi**3 + cv1**3)**2
        fv1q2 = ( (chi**3 + cv1**3)*3.0_dp*chi**2*chiq2                        &
                 - chi**3*3.0_dp*chi**2*chiq2 ) / (chi**3 + cv1**3)**2
        fv1q3 = ( (chi**3 + cv1**3)*3.0_dp*chi**2*chiq3                        &
                 - chi**3*3.0_dp*chi**2*chiq3 ) / (chi**3 + cv1**3)**2
        fv1q4 = ( (chi**3 + cv1**3)*3.0_dp*chi**2*chiq4                        &
                 - chi**3*3.0_dp*chi**2*chiq4 ) / (chi**3 + cv1**3)**2
        fv1t  = ( (chi**3 + cv1**3)*3.0_dp*chi**2*chit                         &
                 - chi**3*3.0_dp*chi**2*chit  ) / (chi**3 + cv1**3)**2
      endif

      fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
      fv2q1 = - ( (1.0_dp + chi*fv1)*chiq1                                     &
                 - chi*(chi*fv1q1 + fv1*chiq1) ) / (1.0_dp + chi*fv1)**2
      fv2q2 = - ( (1.0_dp + chi*fv1)*chiq2                                     &
                 - chi*(chi*fv1q2 + fv1*chiq2) ) / (1.0_dp + chi*fv1)**2
      fv2q3 = - ( (1.0_dp + chi*fv1)*chiq3                                     &
                 - chi*(chi*fv1q3 + fv1*chiq3) ) / (1.0_dp + chi*fv1)**2
      fv2q4 = - ( (1.0_dp + chi*fv1)*chiq4                                     &
                 - chi*(chi*fv1q4 + fv1*chiq4) ) / (1.0_dp + chi*fv1)**2
      fv2t  = - ( (1.0_dp + chi*fv1)*chit                                      &
                 - chi*(chi*fv1t  + fv1*chit ) ) / (1.0_dp + chi*fv1)**2

      bot  = vkar*vkar*slen(i)*slen(i)

! Right now we are only linearizing the contributions
! that dont include the vorticity so treat it as fixed.
! We will pick up this term later.

      S    = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2)
      if ( S <= 1.0e-8_dp) S = 1.0e-8_dp

      if ( dacles_mariani ) then
        sij_mag   = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                      &
                          +2._dp*(ux**2+vy**2+wz**2))
        s = s + 2._dp*min(my_0,sij_mag-s)
      end if

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

      edwards_mod1 : if(.not. use_edwards_mod) then

        if ( use_bp_model ) then

          turb_abs  = chi*rnu
            turb_absq1 = chi*rnuq1 + rnu*chiq1
            turb_absq2 = chi*rnuq2 + rnu*chiq2
            turb_absq3 = chi*rnuq3 + rnu*chiq3
            turb_absq4 = chi*rnuq4 + rnu*chiq4
            turb_abst  = chi*rnut  + rnu*chit

          Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
          Swq1 = 1.0_dp/Re*(turb_absq1*fv2/bot + turb_abs*((bot*fv2q1)/bot/bot))
          Swq2 = 1.0_dp/Re*(turb_absq2*fv2/bot + turb_abs*((bot*fv2q2)/bot/bot))
          Swq3 = 1.0_dp/Re*(turb_absq3*fv2/bot + turb_abs*((bot*fv2q3)/bot/bot))
          Swq4 = 1.0_dp/Re*(turb_absq4*fv2/bot + turb_abs*((bot*fv2q4)/bot/bot))
          Swt  = 1.0_dp/Re*(turb_abst *fv2/bot + turb_abs*((bot*fv2t) /bot/bot))

          if ( Sw < 0.3_dp*S ) then
            s_bar = 1.0_dp/Re/bot*turb_abs*fv2
              s_barq1 = 1.0_dp/Re*(turb_absq1*fv2/bot + turb_abs*((bot*fv2q1)  &
                      / bot / bot))
              s_barq2 = 1.0_dp/Re*(turb_absq2*fv2/bot + turb_abs*((bot*fv2q2)  &
                      / bot / bot))
              s_barq3 = 1.0_dp/Re*(turb_absq3*fv2/bot + turb_abs*((bot*fv2q3)  &
                      / bot / bot))
              s_barq4 = 1.0_dp/Re*(turb_absq4*fv2/bot + turb_abs*((bot*fv2q4)  &
                      / bot / bot))
              s_bart  = 1.0_dp/Re*(turb_abst *fv2/bot + turb_abs*((bot*fv2t)   &
                      / bot / bot))
            term1 = 0.49_dp*S + 0.9_dp*s_bar
              term1q1 = 0.9_dp*s_barq1
              term1q2 = 0.9_dp*s_barq2
              term1q3 = 0.9_dp*s_barq3
              term1q4 = 0.9_dp*s_barq4
              term1t  = 0.9_dp*s_bart
            term2 = -0.5_dp*S - s_bar
              term2q1 = -s_barq1
              term2q2 = -s_barq2
              term2q3 = -s_barq3
              term2q4 = -s_barq4
              term2t  = -s_bart
            Sw = S + S*term1/term2
              Swq1 = S*((term2*term1q1-term1*term2q1)/term2/term2)
              Swq2 = S*((term2*term1q2-term1*term2q2)/term2/term2)
              Swq3 = S*((term2*term1q3-term1*term2q3)/term2/term2)
              Swq4 = S*((term2*term1q4-term1*term2q4)/term2/term2)
              Swt  = S*((term2*term1t -term1*term2t )/term2/term2)
          endif

        else

          Sw   = S + 1.0_dp/Re*turb(1,i)/bot*fv2
          Swq1 = 1.0_dp/Re*turb(1,i) * (bot*fv2q1) / bot / bot
          Swq2 = 1.0_dp/Re*turb(1,i) * (bot*fv2q2) / bot / bot
          Swq3 = 1.0_dp/Re*turb(1,i) * (bot*fv2q3) / bot / bot
          Swq4 = 1.0_dp/Re*turb(1,i) * (bot*fv2q4) / bot / bot
          Swt  = 1.0_dp/Re*(fv2/bot + turb(1,i)*((bot*fv2t)/bot/bot))
          if(Sw > 0.00001_dp) then
            Swq1 = Swq1
            Swq2 = Swq2
            Swq3 = Swq3
            Swq4 = Swq4
            Swt  = Swt
          else
            Sw = 0.00001_dp
            Swq1 = 0.0_dp
            Swq2 = 0.0_dp
            Swq3 = 0.0_dp
            Swq4 = 0.0_dp
            Swt  = 0.0_dp
          endif

        endif

        RR   = 1.0_dp/Re*turb(1,i)/bot/Sw
        RRq1 = 1.0_dp/Re*turb(1,i) * (-1.0_dp) * (bot*Swq1) / (bot*Sw)**2
        RRq2 = 1.0_dp/Re*turb(1,i) * (-1.0_dp) * (bot*Swq2) / (bot*Sw)**2
        RRq3 = 1.0_dp/Re*turb(1,i) * (-1.0_dp) * (bot*Swq3) / (bot*Sw)**2
        RRq4 = 1.0_dp/Re*turb(1,i) * (-1.0_dp) * (bot*Swq4) / (bot*Sw)**2
        RRt  = 1.0_dp/Re*(1.0_dp/bot/Sw + turb(1,i)*((-1.0_dp) * (bot*Swt)     &
          / (bot*Sw)**2))

        if(RR > 10.0_dp) then
          RR = 10.0_dp
          RRq1 = 0.0_dp
          RRq2 = 0.0_dp
          RRq3 = 0.0_dp
          RRq4 = 0.0_dp
          RRt  = 0.0_dp
        endif

      else edwards_mod1

        sw = s*(1.0_dp/chi + fv1)
        swq1 = s*(-1.0_dp/chi/chi*chiq1 + fv1q1)
        swq2 = s*(-1.0_dp/chi/chi*chiq2 + fv1q2)
        swq3 = s*(-1.0_dp/chi/chi*chiq3 + fv1q3)
        swq4 = s*(-1.0_dp/chi/chi*chiq4 + fv1q4)
        swt  = s*(-1.0_dp/chi/chi*chit  + fv1t )

        distance = slen(i)
        arg = vkar*vkar*distance*distance

        tanharg = 1.0_dp/re*turb(1,i)/arg/sw
        tanhargq1 = -1.0_dp*1.0_dp/re*turb(1,i)/arg/sw/sw * swq1
        tanhargq2 = -1.0_dp*1.0_dp/re*turb(1,i)/arg/sw/sw * swq2
        tanhargq3 = -1.0_dp*1.0_dp/re*turb(1,i)/arg/sw/sw * swq3
        tanhargq4 = -1.0_dp*1.0_dp/re*turb(1,i)/arg/sw/sw * swq4
        tanhargt  = -1.0_dp*1.0_dp/re*turb(1,i)/arg/sw/sw * swt

        if(tanharg < 0.0_dp)                                                   &
          write(*,*) 'Negative tanharg, fix limiting on cosh()'

        rr = tanh(tanharg) / tanh(1.0_dp)
        if(tanharg > 15.0_dp) then
          rrq1 = 0.0_dp
          rrq2 = 0.0_dp
          rrq3 = 0.0_dp
          rrq4 = 0.0_dp
          rrt  = 0.0_dp
        else
          rrq1=1.0_dp/tanh(1.0_dp)*tanhargq1/cosh(tanharg)/cosh(tanharg)
          rrq2=1.0_dp/tanh(1.0_dp)*tanhargq2/cosh(tanharg)/cosh(tanharg)
          rrq3=1.0_dp/tanh(1.0_dp)*tanhargq3/cosh(tanharg)/cosh(tanharg)
          rrq4=1.0_dp/tanh(1.0_dp)*tanhargq4/cosh(tanharg)/cosh(tanharg)
          rrt =1.0_dp/tanh(1.0_dp)*tanhargt /cosh(tanharg)/cosh(tanharg)
        endif

      endif edwards_mod1

      GG   = RR + cw2*(RR**6 - RR)
      GGq1 = RRq1 + cw2*(6.0_dp*RR**5*RRq1 - RRq1)
      GGq2 = RRq2 + cw2*(6.0_dp*RR**5*RRq2 - RRq2)
      GGq3 = RRq3 + cw2*(6.0_dp*RR**5*RRq3 - RRq3)
      GGq4 = RRq4 + cw2*(6.0_dp*RR**5*RRq4 - RRq4)
      GGt  = RRt  + cw2*(6.0_dp*RR**5*RRt  - RRt )

      if ( use_bp_model .and. GG > 1000.0_dp ) then
        GG = 1000.0_dp
         GGq1 = 0.0_dp
         GGq2 = 0.0_dp
         GGq3 = 0.0_dp
         GGq4 = 0.0_dp
         GGt  = 0.0_dp
      endif

      fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
      rtop = 1.0_dp + cw3**6
      rtopq1 = 0.0_dp
      rtopq2 = 0.0_dp
      rtopq3 = 0.0_dp
      rtopq4 = 0.0_dp
      rtopt  = 0.0_dp
      rbot = GG**6 + cw3**6
      rbotq1 = 6.0_dp*GG**5*GGq1
      rbotq2 = 6.0_dp*GG**5*GGq2
      rbotq3 = 6.0_dp*GG**5*GGq3
      rbotq4 = 6.0_dp*GG**5*GGq4
      rbott  = 6.0_dp*GG**5*GGt
      rpiece = rtop / rbot
      rpieceq1 = (rbot*rtopq1 - rtop*rbotq1) / rbot**2
      rpieceq2 = (rbot*rtopq2 - rtop*rbotq2) / rbot**2
      rpieceq3 = (rbot*rtopq3 - rtop*rbotq3) / rbot**2
      rpieceq4 = (rbot*rtopq4 - rtop*rbotq4) / rbot**2
      rpiecet  = (rbot*rtopt  - rtop*rbott ) / rbot**2
      fw = GG*rpiece**(1.0_dp/6.0_dp)
      fwq1 = GG*(1.0_dp/6.0_dp)*rpiece**(-5.0_dp/6.0_dp)*rpieceq1              &
           + rpiece**(1.0_dp/6.0_dp)*GGq1
      fwq2 = GG*(1.0_dp/6.0_dp)*rpiece**(-5.0_dp/6.0_dp)*rpieceq2              &
           + rpiece**(1.0_dp/6.0_dp)*GGq2
      fwq3 = GG*(1.0_dp/6.0_dp)*rpiece**(-5.0_dp/6.0_dp)*rpieceq3              &
           + rpiece**(1.0_dp/6.0_dp)*GGq3
      fwq4 = GG*(1.0_dp/6.0_dp)*rpiece**(-5.0_dp/6.0_dp)*rpieceq4              &
           + rpiece**(1.0_dp/6.0_dp)*GGq4
      fwt  = GG*(1.0_dp/6.0_dp)*rpiece**(-5.0_dp/6.0_dp)*rpiecet               &
           + rpiece**(1.0_dp/6.)*GGt

      temp_arg = -ct4*chi*chi
        temp_argq1 = -2.0_dp*ct4*chi*chiq1
        temp_argq2 = -2.0_dp*ct4*chi*chiq2
        temp_argq3 = -2.0_dp*ct4*chi*chiq3
        temp_argq4 = -2.0_dp*ct4*chi*chiq4
        temp_argt  = -2.0_dp*ct4*chi*chit

      if ( use_bp_model .and. temp_arg < -700.0_dp ) then
        temp_arg = -700.0_dp
          temp_argq1 = 0.0_dp
          temp_argq2 = 0.0_dp
          temp_argq3 = 0.0_dp
          temp_argq4 = 0.0_dp
          temp_argt  = 0.0_dp
      endif

      ft2  = ct3*exp(temp_arg)
      ft2q1 = ft2*temp_argq1
      ft2q2 = ft2*temp_argq2
      ft2q3 = ft2*temp_argq3
      ft2q4 = ft2*temp_argq4
      ft2t  = ft2*temp_argt

!     Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,i))
!     Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)  ! for use_bp_model

      rpiece1 = cb1*(1.0_dp - ft2)
      rpiece1q1 = -cb1*ft2q1
      rpiece1q2 = -cb1*ft2q2
      rpiece1q3 = -cb1*ft2q3
      rpiece1q4 = -cb1*ft2q4
      rpiece1t  = -cb1*ft2t

      if ( use_bp_model ) then
        rpiece2 = Sw*turb_abs
        rpiece2q1 = Sw*turb_absq1 + turb_abs*Swq1
        rpiece2q2 = Sw*turb_absq2 + turb_abs*Swq2
        rpiece2q3 = Sw*turb_absq3 + turb_abs*Swq3
        rpiece2q4 = Sw*turb_absq4 + turb_abs*Swq4
        rpiece2t  = Sw*turb_abst  + turb_abs*Swt
      else
        rpiece2 = Sw*turb(1,i)
        rpiece2q1 = turb(1,i)*Swq1
        rpiece2q2 = turb(1,i)*Swq2
        rpiece2q3 = turb(1,i)*Swq3
        rpiece2q4 = turb(1,i)*Swq4
        rpiece2t  = Sw + turb(1,i)*Swt
      endif

!     Prod = rpiece1*rpiece2
      Prodq1 = rpiece1*rpiece2q1 + rpiece2*rpiece1q1
      Prodq2 = rpiece1*rpiece2q2 + rpiece2*rpiece1q2
      Prodq3 = rpiece1*rpiece2q3 + rpiece2*rpiece1q3
      Prodq4 = rpiece1*rpiece2q4 + rpiece2*rpiece1q4
      Prodt  = rpiece1*rpiece2t  + rpiece2*rpiece1t

      vkar2 = vkar*vkar

!     Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)*(turb(1,i)/slen(i))**2
!     Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)*(turb_abs/slen(i))**2 ! bp_model

      rpiece1 = cw1*fw - cb1/vkar2*ft2
      rpiece1q1 = cw1*fwq1 - cb1/vkar2*ft2q1
      rpiece1q2 = cw1*fwq2 - cb1/vkar2*ft2q2
      rpiece1q3 = cw1*fwq3 - cb1/vkar2*ft2q3
      rpiece1q4 = cw1*fwq4 - cb1/vkar2*ft2q4
      rpiece1t  = cw1*fwt  - cb1/vkar2*ft2t

      if ( use_bp_model ) then
        rpiece2 = (turb_abs/slen(i)) ** 2
        rpiece2q1 = 2.0_dp*turb_abs*turb_absq1/slen(i)/slen(i)
        rpiece2q2 = 2.0_dp*turb_abs*turb_absq2/slen(i)/slen(i)
        rpiece2q3 = 2.0_dp*turb_abs*turb_absq3/slen(i)/slen(i)
        rpiece2q4 = 2.0_dp*turb_abs*turb_absq4/slen(i)/slen(i)
        rpiece2t  = 1.0_dp/slen(i)**2 * 2.0_dp*turb_abs*turb_abst
      else
        rpiece2 = (turb(1,i)/slen(i)) ** 2
        rpiece2q1 = 0.0_dp
        rpiece2q2 = 0.0_dp
        rpiece2q3 = 0.0_dp
        rpiece2q4 = 0.0_dp
        rpiece2t  = 1.0_dp/slen(i)**2 * 2.0_dp * turb(1,i)
      endif

!     Dest = 1.0_dp/Re*rpiece1*rpiece2
      Destq1 = 1.0_dp/Re*(rpiece1*rpiece2q1 + rpiece2*rpiece1q1)
      Destq2 = 1.0_dp/Re*(rpiece1*rpiece2q2 + rpiece2*rpiece1q2)
      Destq3 = 1.0_dp/Re*(rpiece1*rpiece2q3 + rpiece2*rpiece1q3)
      Destq4 = 1.0_dp/Re*(rpiece1*rpiece2q4 + rpiece2*rpiece1q4)
      Destt  = 1.0_dp/Re*(rpiece1*rpiece2t  + rpiece2*rpiece1t )

!     source = vol(i)*(Prod - Dest)
      sourceq1 = vol(i)*(Prodq1 - Destq1)
      sourceq2 = vol(i)*(Prodq2 - Destq2)
      sourceq3 = vol(i)*(Prodq3 - Destq3)
      sourceq4 = vol(i)*(Prodq4 - Destq4)
      sourcet  = vol(i)*(Prodt  - Destt )

      resq1 = -sourceq1
      resq2 = -sourceq2
      resq3 = -sourceq3
      resq4 = -sourceq4
      rest  = -sourcet

      if ( fill_res ) then
        do j = 1, nfunctions
          rlam5(j) = coltag(5,i)*rlam(5,i,j)
          res(1,i,j) = res(1,i,j) + resq1*rlam5(j)
          res(2,i,j) = res(2,i,j) + resq2*rlam5(j)
          res(3,i,j) = res(3,i,j) + resq3*rlam5(j)
          res(4,i,j) = res(4,i,j) + resq4*rlam5(j)
          res(5,i,j) = res(5,i,j) + rest *rlam5(j)
        end do
      endif

      if ( fill_a ) then
        idiag = iau(i)
        A(5,1,idiag) = A(5,1,idiag) + resq1
        A(5,2,idiag) = A(5,2,idiag) + resq2
        A(5,3,idiag) = A(5,3,idiag) + resq3
        A(5,4,idiag) = A(5,4,idiag) + resq4
        A(5,5,idiag) = A(5,5,idiag) + rest
      endif

    enddo node_loop_source

! Now pick up the off-diagonal pieces due to the production term.

! Loop over the edges and pick up the contributions
! from the gradients. Notice that this only effects
! off-diagonal terms so I wont do anything about the diagonal

    edge_off_diag_production_3060 : do n = 1,nedgeloc
      node1 = eptr(1,n)
      node2 = eptr(2,n)

      area = ra(n)
      xnorm = area*xn(n)
      ynorm = area*yn(n)
      znorm = area*zn(n)

! Data and derivatives at each node

!     q2 = 0.5_dp*(qnode(2,node2) + qnode(2,node1))
      q2q11 = 0.0_dp
      q2q12 = 0.5_dp
      q2q13 = 0.0_dp
      q2q14 = 0.0_dp
      q2t1  = 0.0_dp

      q2q21 = 0.0_dp
      q2q22 = 0.5_dp
      q2q23 = 0.0_dp
      q2q24 = 0.0_dp
      q2t2  = 0.0_dp

!     q3 = 0.5_dp*(qnode(3,node2) + qnode(3,node1))
      q3q11 = 0.0_dp
      q3q12 = 0.0_dp
      q3q13 = 0.5_dp
      q3q14 = 0.0_dp
      q3t1  = 0.0_dp

      q3q21 = 0.0_dp
      q3q22 = 0.0_dp
      q3q23 = 0.5_dp
      q3q24 = 0.0_dp
      q3t2  = 0.0_dp

!     q4 = 0.5_dp*(qnode(4,node2) + qnode(4,node1))
      q4q11 = 0.0_dp
      q4q12 = 0.0_dp
      q4q13 = 0.0_dp
      q4q14 = 0.5_dp
      q4t1  = 0.0_dp

      q4q21 = 0.0_dp
      q4q22 = 0.0_dp
      q4q23 = 0.0_dp
      q4q24 = 0.5_dp
      q4t2  = 0.0_dp

! If we are updating the residual at node1, then
! we will pick up a derivative w.r.t. node2.
! Keep in mind that in this part of the code,
! we are only taking into account linearization
! from the vorticity part of the source term; the
! other parts are already taken into account.

! to not divide by slen on surface for -fpe0 NaNa
      node1_of_edge_used : if ( coltag(5,node1) > 0.1_dp ) then

! dR1/d()2

      rnu  = 1.0_dp

      edwards_chi2 : if(.not. use_edwards_mod) then
        chi  = turb(1,node1)/rnu
        if ( use_bp_model .and. chi < 10.0_dp ) then
          chi = 0.05_dp * log ( 1.0_dp + exp( 20.0_dp*chi ) )
        endif
      else edwards_chi2
        chi  = (turb(1,node1)+1.e-12_dp)/rnu
      endif edwards_chi2

      if ( use_bp_model .and. chi < -5.0_dp ) then
        fv1 = 0.0_dp
      else
        fv1 = chi**3/(chi**3 + cv1**3)
      endif

      fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
      bot  = vkar*vkar*slen(node1)*slen(node1)

! Get derivatives of the vorticity source term.
! Since this only contributes to the off-diagonal,
! dont compute linearization w.r.t. node1. (we'll
! do that later)

      ux   = gradx(2,node1)
      uxq11 = my_1/vol(node1)*xnorm*q2q11
      uxq12 = my_1/vol(node1)*xnorm*q2q12
      uxq13 = my_1/vol(node1)*xnorm*q2q13
      uxq14 = my_1/vol(node1)*xnorm*q2q14
      uxt1  = my_1/vol(node1)*xnorm*q2t1

      uxq21 = my_1/vol(node1)*xnorm*q2q21
      uxq22 = my_1/vol(node1)*xnorm*q2q22
      uxq23 = my_1/vol(node1)*xnorm*q2q23
      uxq24 = my_1/vol(node1)*xnorm*q2q24
      uxt2  = my_1/vol(node1)*xnorm*q2t2


      uy   = grady(2,node1)
      uyq11 = 1.0_dp/vol(node1)*ynorm*q2q11
      uyq12 = 1.0_dp/vol(node1)*ynorm*q2q12
      uyq13 = 1.0_dp/vol(node1)*ynorm*q2q13
      uyq14 = 1.0_dp/vol(node1)*ynorm*q2q14
      uyt1  = 1.0_dp/vol(node1)*ynorm*q2t1

      uyq21 = 1.0_dp/vol(node1)*ynorm*q2q21
      uyq22 = 1.0_dp/vol(node1)*ynorm*q2q22
      uyq23 = 1.0_dp/vol(node1)*ynorm*q2q23
      uyq24 = 1.0_dp/vol(node1)*ynorm*q2q24
      uyt2  = 1.0_dp/vol(node1)*ynorm*q2t2
      uz   = gradz(2,node1)
      uzq11 = 1.0_dp/vol(node1)*znorm*q2q11
      uzq12 = 1.0_dp/vol(node1)*znorm*q2q12
      uzq13 = 1.0_dp/vol(node1)*znorm*q2q13
      uzq14 = 1.0_dp/vol(node1)*znorm*q2q14
      uzt1  = 1.0_dp/vol(node1)*znorm*q2t1

      uzq21 = 1.0_dp/vol(node1)*znorm*q2q21
      uzq22 = 1.0_dp/vol(node1)*znorm*q2q22
      uzq23 = 1.0_dp/vol(node1)*znorm*q2q23
      uzq24 = 1.0_dp/vol(node1)*znorm*q2q24
      uzt2  = 1.0_dp/vol(node1)*znorm*q2t2
      vx   = gradx(3,node1)
      vxq11 = 1.0_dp/vol(node1)*xnorm*q3q11
      vxq12 = 1.0_dp/vol(node1)*xnorm*q3q12
      vxq13 = 1.0_dp/vol(node1)*xnorm*q3q13
      vxq14 = 1.0_dp/vol(node1)*xnorm*q3q14
      vxt1  = 1.0_dp/vol(node1)*xnorm*q3t1

      vxq21 = 1.0_dp/vol(node1)*xnorm*q3q21
      vxq22 = 1.0_dp/vol(node1)*xnorm*q3q22
      vxq23 = 1.0_dp/vol(node1)*xnorm*q3q23
      vxq24 = 1.0_dp/vol(node1)*xnorm*q3q24
      vxt2  = 1.0_dp/vol(node1)*xnorm*q3t2

      vy   = grady(3,node1)
      vyq11 = my_1/vol(node1)*ynorm*q3q11
      vyq12 = my_1/vol(node1)*ynorm*q3q12
      vyq13 = my_1/vol(node1)*ynorm*q3q13
      vyq14 = my_1/vol(node1)*ynorm*q3q14
      vyt1  = my_1/vol(node1)*ynorm*q3t1

      vyq21 = my_1/vol(node1)*ynorm*q3q21
      vyq22 = my_1/vol(node1)*ynorm*q3q22
      vyq23 = my_1/vol(node1)*ynorm*q3q23
      vyq24 = my_1/vol(node1)*ynorm*q3q24
      vyt2  = my_1/vol(node1)*ynorm*q3t2

      vz   = gradz(3,node1)
      vzq11 = 1.0_dp/vol(node1)*znorm*q3q11
      vzq12 = 1.0_dp/vol(node1)*znorm*q3q12
      vzq13 = 1.0_dp/vol(node1)*znorm*q3q13
      vzq14 = 1.0_dp/vol(node1)*znorm*q3q14
      vzt1  = 1.0_dp/vol(node1)*znorm*q3t1

      vzq21 = 1.0_dp/vol(node1)*znorm*q3q21
      vzq22 = 1.0_dp/vol(node1)*znorm*q3q22
      vzq23 = 1.0_dp/vol(node1)*znorm*q3q23
      vzq24 = 1.0_dp/vol(node1)*znorm*q3q24
      vzt2  = 1.0_dp/vol(node1)*znorm*q3t2
      wx   = gradx(4,node1)
      wxq11 = 1.0_dp/vol(node1)*xnorm*q4q11
      wxq12 = 1.0_dp/vol(node1)*xnorm*q4q12
      wxq13 = 1.0_dp/vol(node1)*xnorm*q4q13
      wxq14 = 1.0_dp/vol(node1)*xnorm*q4q14
      wxt1  = 1.0_dp/vol(node1)*xnorm*q4t1

      wxq21 = 1.0_dp/vol(node1)*xnorm*q4q21
      wxq22 = 1.0_dp/vol(node1)*xnorm*q4q22
      wxq23 = 1.0_dp/vol(node1)*xnorm*q4q23
      wxq24 = 1.0_dp/vol(node1)*xnorm*q4q24
      wxt2  = 1.0_dp/vol(node1)*xnorm*q4t2
      wy   = grady(4,node1)
      wyq11 = 1.0_dp/vol(node1)*ynorm*q4q11
      wyq12 = 1.0_dp/vol(node1)*ynorm*q4q12
      wyq13 = 1.0_dp/vol(node1)*ynorm*q4q13
      wyq14 = 1.0_dp/vol(node1)*ynorm*q4q14
      wyt1  = 1.0_dp/vol(node1)*ynorm*q4t1

      wyq21 = 1.0_dp/vol(node1)*ynorm*q4q21
      wyq22 = 1.0_dp/vol(node1)*ynorm*q4q22
      wyq23 = 1.0_dp/vol(node1)*ynorm*q4q23
      wyq24 = 1.0_dp/vol(node1)*ynorm*q4q24
      wyt2  = 1.0_dp/vol(node1)*ynorm*q4t2

      wz   = gradz(4,node1)
      wzq11 = my_1/vol(node1)*znorm*q4q11
      wzq12 = my_1/vol(node1)*znorm*q4q12
      wzq13 = my_1/vol(node1)*znorm*q4q13
      wzq14 = my_1/vol(node1)*znorm*q4q14
      wzt1  = my_1/vol(node1)*znorm*q4t1

      wzq21 = my_1/vol(node1)*znorm*q4q21
      wzq22 = my_1/vol(node1)*znorm*q4q22
      wzq23 = my_1/vol(node1)*znorm*q4q23
      wzq24 = my_1/vol(node1)*znorm*q4q24
      wzt2  = my_1/vol(node1)*znorm*q4t2


      if ( has_x_symmetry(symmetry(node1)) ) then

        uy = my_0
          uyq11 = my_0
          uyq12 = my_0
          uyq13 = my_0
          uyq14 = my_0
          uyt1  = my_0

          uyq21 = my_0
          uyq22 = my_0
          uyq23 = my_0
          uyq24 = my_0
          uyt2  = my_0

        uz = my_0
          uzq11 = my_0
          uzq12 = my_0
          uzq13 = my_0
          uzq14 = my_0
          uzt1  = my_0

          uzq21 = my_0
          uzq22 = my_0
          uzq23 = my_0
          uzq24 = my_0
          uzt2  = my_0

        vx = my_0
          vxq11 = my_0
          vxq12 = my_0
          vxq13 = my_0
          vxq14 = my_0
          vxt1  = my_0

          vxq21 = my_0
          vxq22 = my_0
          vxq23 = my_0
          vxq24 = my_0
          vxt2  = my_0

        wx = my_0
          wxq11 = my_0
          wxq12 = my_0
          wxq13 = my_0
          wxq14 = my_0
          wxt1  = my_0

          wxq21 = my_0
          wxq22 = my_0
          wxq23 = my_0
          wxq24 = my_0
          wxt2  = my_0

      endif

      if ( has_y_symmetry(symmetry(node1)) ) then

        vx = my_0
          vxq11 = my_0
          vxq12 = my_0
          vxq13 = my_0
          vxq14 = my_0
          vxt1  = my_0

          vxq21 = my_0
          vxq22 = my_0
          vxq23 = my_0
          vxq24 = my_0
          vxt2  = my_0

        vz = my_0
          vzq11 = my_0
          vzq12 = my_0
          vzq13 = my_0
          vzq14 = my_0
          vzt1  = my_0

          vzq21 = my_0
          vzq22 = my_0
          vzq23 = my_0
          vzq24 = my_0
          vzt2  = my_0

        uy = my_0
          uyq11 = my_0
          uyq12 = my_0
          uyq13 = my_0
          uyq14 = my_0
          uyt1  = my_0

          uyq21 = my_0
          uyq22 = my_0
          uyq23 = my_0
          uyq24 = my_0
          uyt2  = my_0

        wy = my_0
          wyq11 = my_0
          wyq12 = my_0
          wyq13 = my_0
          wyq14 = my_0
          wyt1  = my_0

          wyq21 = my_0
          wyq22 = my_0
          wyq23 = my_0
          wyq24 = my_0
          wyt2  = my_0

      endif

      if ( has_z_symmetry(symmetry(node1)) ) then

        wx = my_0
          wxq11 = my_0
          wxq12 = my_0
          wxq13 = my_0
          wxq14 = my_0
          wxt1  = my_0

          wxq21 = my_0
          wxq22 = my_0
          wxq23 = my_0
          wxq24 = my_0
          wxt2  = my_0

        wy = my_0
          wyq11 = my_0
          wyq12 = my_0
          wyq13 = my_0
          wyq14 = my_0
          wyt1  = my_0

          wyq21 = my_0
          wyq22 = my_0
          wyq23 = my_0
          wyq24 = my_0
          wyt2  = my_0

        uz = my_0
          uzq11 = my_0
          uzq12 = my_0
          uzq13 = my_0
          uzq14 = my_0
          uzt1  = my_0

          uzq21 = my_0
          uzq22 = my_0
          uzq23 = my_0
          uzq24 = my_0
          uzt2  = my_0

        vz = my_0
          vzq11 = my_0
          vzq12 = my_0
          vzq13 = my_0
          vzq14 = my_0
          vzt1  = my_0

          vzq21 = my_0
          vzq22 = my_0
          vzq23 = my_0
          vzq24 = my_0
          vzt2  = my_0

      endif

      S    = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2)

      if ( S <= 1.0e-8_dp) then

        S = 1.0e-8_dp
        Sq11 = 0.0_dp
        Sq12 = 0.0_dp
        Sq13 = 0.0_dp
        Sq14 = 0.0_dp
        St1  = 0.0_dp

        Sq21 = 0.0_dp
        Sq22 = 0.0_dp
        Sq23 = 0.0_dp
        Sq24 = 0.0_dp
        St2  = 0.0_dp

      else

        Sq11 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq11-vzq11)                            &
                     + 2.0_dp*(uz-wx)*(uzq11-wxq11)                            &
                     + 2.0_dp*(vx-uy)*(vxq11-uyq11))/S
        Sq12 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq12-vzq12)                            &
                     + 2.0_dp*(uz-wx)*(uzq12-wxq12)                            &
                     + 2.0_dp*(vx-uy)*(vxq12-uyq12))/S
        Sq13 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq13-vzq13)                            &
                     + 2.0_dp*(uz-wx)*(uzq13-wxq13)                            &
                     + 2.0_dp*(vx-uy)*(vxq13-uyq13))/S
        Sq14 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq14-vzq14)                            &
                     + 2.0_dp*(uz-wx)*(uzq14-wxq14)                            &
                     + 2.0_dp*(vx-uy)*(vxq14-uyq14))/S
        St1  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt1 -vzt1 )                            &
                     + 2.0_dp*(uz-wx)*(uzt1 -wxt1 )                            &
                     + 2.0_dp*(vx-uy)*(vxt1 -uyt1 ))/S

        Sq21 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq21-vzq21)                            &
                     + 2.0_dp*(uz-wx)*(uzq21-wxq21)                            &
                     + 2.0_dp*(vx-uy)*(vxq21-uyq21))/S
        Sq22 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq22-vzq22)                            &
                     + 2.0_dp*(uz-wx)*(uzq22-wxq22)                            &
                     + 2.0_dp*(vx-uy)*(vxq22-uyq22))/S
        Sq23 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq23-vzq23)                            &
                     + 2.0_dp*(uz-wx)*(uzq23-wxq23)                            &
                     + 2.0_dp*(vx-uy)*(vxq23-uyq23))/S
        Sq24 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq24-vzq24)                            &
                     + 2.0_dp*(uz-wx)*(uzq24-wxq24)                            &
                     + 2.0_dp*(vx-uy)*(vxq24-uyq24))/S
        St2  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt2 -vzt2 )                            &
                     + 2.0_dp*(uz-wx)*(uzt2 -wxt2 )                            &
                     + 2.0_dp*(vx-uy)*(vxt2 -uyt2 ))/S

      endif

      if ( dacles_mariani ) then
        sij_mag = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                          +2._dp*(ux**2+vy**2+wz**2))
          sij_magq11 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq11+vzq11)          &
                                        +  my_2*(uz+wx)*(uzq11+wxq11)          &
                                        +  my_2*(vx+uy)*(vxq11+uyq11)          &
                                               + my_2*(my_2*ux*uxq11           &
                                                     + my_2*vy*vyq11           &
                                                     + my_2*wz*wzq11) )
          sij_magq12 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq12+vzq12)          &
                                        +  my_2*(uz+wx)*(uzq12+wxq12)          &
                                        +  my_2*(vx+uy)*(vxq12+uyq12)          &
                                               + my_2*(my_2*ux*uxq12           &
                                                     + my_2*vy*vyq12           &
                                                     + my_2*wz*wzq12) )
          sij_magq13 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq13+vzq13)          &
                                        +  my_2*(uz+wx)*(uzq13+wxq13)          &
                                        +  my_2*(vx+uy)*(vxq13+uyq13)          &
                                               + my_2*(my_2*ux*uxq13           &
                                                     + my_2*vy*vyq13           &
                                                     + my_2*wz*wzq13) )
          sij_magq14 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq14+vzq14)          &
                                        +  my_2*(uz+wx)*(uzq14+wxq14)          &
                                        +  my_2*(vx+uy)*(vxq14+uyq14)          &
                                               + my_2*(my_2*ux*uxq14           &
                                                     + my_2*vy*vyq14           &
                                                     + my_2*wz*wzq14) )
          sij_magt1  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt1 +vzt1 )          &
                                        +  my_2*(uz+wx)*(uzt1 +wxt1 )          &
                                        +  my_2*(vx+uy)*(vxt1 +uyt1 )          &
                                               + my_2*(my_2*ux*uxt1            &
                                                     + my_2*vy*vyt1            &
                                                     + my_2*wz*wzt1 ) )

          sij_magq21 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq21+vzq21)          &
                                        +  my_2*(uz+wx)*(uzq21+wxq21)          &
                                        +  my_2*(vx+uy)*(vxq21+uyq21)          &
                                               + my_2*(my_2*ux*uxq21           &
                                                     + my_2*vy*vyq21           &
                                                     + my_2*wz*wzq21) )
          sij_magq22 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq22+vzq22)          &
                                        +  my_2*(uz+wx)*(uzq22+wxq22)          &
                                        +  my_2*(vx+uy)*(vxq22+uyq22)          &
                                               + my_2*(my_2*ux*uxq22           &
                                                     + my_2*vy*vyq22           &
                                                     + my_2*wz*wzq22) )
          sij_magq23 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq23+vzq23)          &
                                        +  my_2*(uz+wx)*(uzq23+wxq23)          &
                                        +  my_2*(vx+uy)*(vxq23+uyq23)          &
                                               + my_2*(my_2*ux*uxq23           &
                                                     + my_2*vy*vyq23           &
                                                     + my_2*wz*wzq23) )
          sij_magq24 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq24+vzq24)          &
                                        +  my_2*(uz+wx)*(uzq24+wxq24)          &
                                        +  my_2*(vx+uy)*(vxq24+uyq24)          &
                                               + my_2*(my_2*ux*uxq24           &
                                                     + my_2*vy*vyq24           &
                                                     + my_2*wz*wzq24) )
          sij_magt2  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt2 +vzt2 )          &
                                        +  my_2*(uz+wx)*(uzt2 +wxt2 )          &
                                        +  my_2*(vx+uy)*(vxt2 +uyt2 )          &
                                               + my_2*(my_2*ux*uxt2            &
                                                     + my_2*vy*vyt2            &
                                                     + my_2*wz*wzt2 ) )
        if ( (sij_mag-s) < my_0 ) then

          s = s + 2._dp*min(my_0,sij_mag-s)

          sq11 = sq11 + my_2*(sij_magq11 - sq11)
          sq12 = sq12 + my_2*(sij_magq12 - sq12)
          sq13 = sq13 + my_2*(sij_magq13 - sq13)
          sq14 = sq14 + my_2*(sij_magq14 - sq14)
          st1  = st1  + my_2*(sij_magt1  - st1 )

          sq21 = sq21 + my_2*(sij_magq21 - sq21)
          sq22 = sq22 + my_2*(sij_magq22 - sq22)
          sq23 = sq23 + my_2*(sij_magq23 - sq23)
          sq24 = sq24 + my_2*(sij_magq24 - sq24)
          st2  = st2  + my_2*(sij_magt2  - st2 )
        endif

      end if

!  Remember we are only linearizing vorticity this time

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

      if ( use_bp_model ) turb_abs = chi*rnu

      edwards_mod2 : if(.not. use_edwards_mod) then

        if ( use_bp_model ) then

          Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
          Swq11 = Sq11
          Swq12 = Sq12
          Swq13 = Sq13
          Swq14 = Sq14
          Swt1  = St1

          Swq21 = Sq21
          Swq22 = Sq22
          Swq23 = Sq23
          Swq24 = Sq24
          Swt2  = St2

          if ( Sw < 0.3_dp*S ) then
            s_bar = 1.0_dp/Re*turb_abs/bot*fv2
            term1 = 0.49_dp*S + 0.9_dp*s_bar
              term1q11 = 0.49_dp*Sq11
              term1q12 = 0.49_dp*Sq12
              term1q13 = 0.49_dp*Sq13
              term1q14 = 0.49_dp*Sq14
              term1t1  = 0.49_dp*St1

              term1q21 = 0.49_dp*Sq21
              term1q22 = 0.49_dp*Sq22
              term1q23 = 0.49_dp*Sq23
              term1q24 = 0.49_dp*Sq24
              term1t2  = 0.49_dp*St2

            term2 = -0.5_dp*S - s_bar
              term2q11 = -0.5_dp*Sq11
              term2q12 = -0.5_dp*Sq12
              term2q13 = -0.5_dp*Sq13
              term2q14 = -0.5_dp*Sq14
              term2t1  = -0.5_dp*St1

              term2q21 = -0.5_dp*Sq21
              term2q22 = -0.5_dp*Sq22
              term2q23 = -0.5_dp*Sq23
              term2q24 = -0.5_dp*Sq24
              term2t2  = -0.5_dp*St2
            Sw = S + S*term1/term2
              Swq11 = Sq11 + S*((term2*term1q11-term1*term2q11)/term2/term2)   &
                    + term1/term2*Sq11
              Swq12 = Sq12 + S*((term2*term1q12-term1*term2q12)/term2/term2)   &
                    + term1/term2*Sq12
              Swq13 = Sq13 + S*((term2*term1q13-term1*term2q13)/term2/term2)   &
                    + term1/term2*Sq13
              Swq14 = Sq14 + S*((term2*term1q14-term1*term2q14)/term2/term2)   &
                    + term1/term2*Sq14
              Swt1  = St1  + S*((term2*term1t1 -term1*term2t1 )/term2/term2)   &
                    + term1/term2*St1

              Swq21 = Sq21 + S*((term2*term1q21-term1*term2q21)/term2/term2)   &
                    + term1/term2*Sq21
              Swq22 = Sq22 + S*((term2*term1q22-term1*term2q22)/term2/term2)   &
                    + term1/term2*Sq22
              Swq23 = Sq23 + S*((term2*term1q23-term1*term2q23)/term2/term2)   &
                    + term1/term2*Sq23
              Swq24 = Sq24 + S*((term2*term1q24-term1*term2q24)/term2/term2)   &
                    + term1/term2*Sq24
              Swt2  = St2  + S*((term2*term1t2 -term1*term2t2 )/term2/term2)   &
                    + term1/term2*St2
          endif

        else

          Sw   = S + 1.0_dp/Re*turb(1,node1)/bot*fv2
          Swq11 = Sq11
          Swq12 = Sq12
          Swq13 = Sq13
          Swq14 = Sq14
          Swt1  = St1

          Swq21 = Sq21
          Swq22 = Sq22
          Swq23 = Sq23
          Swq24 = Sq24
          Swt2  = St2

          if(Sw <= 0.00001_dp) then
            Sw = 0.00001_dp
            Swq11 = 0.0_dp
            Swq12 = 0.0_dp
            Swq13 = 0.0_dp
            Swq14 = 0.0_dp
            Swt1  = 0.0_dp

            Swq21 = 0.0_dp
            Swq22 = 0.0_dp
            Swq23 = 0.0_dp
            Swq24 = 0.0_dp
            Swt2  = 0.0_dp
          endif

        endif

        RR   = (1.0_dp/Re*turb(1,node1)/bot)/Sw
        RRq11 = -RR*Swq11/Sw
        RRq12 = -RR*Swq12/Sw
        RRq13 = -RR*Swq13/Sw
        RRq14 = -RR*Swq14/Sw

        RRq21 = -RR*Swq21/Sw
        RRq22 = -RR*Swq22/Sw
        RRq23 = -RR*Swq23/Sw
        RRq24 = -RR*Swq24/Sw

        if(RR > 10.0_dp) then
          RR = 10.0_dp
          RRq11 = 0.0_dp
          RRq12 = 0.0_dp
          RRq13 = 0.0_dp
          RRq14 = 0.0_dp

          RRq21 = 0.0_dp
          RRq22 = 0.0_dp
          RRq23 = 0.0_dp
          RRq24 = 0.0_dp
        endif

      else edwards_mod2

        sw = s*(1.0_dp/chi + fv1)
        swq11 = sq11*(1.0_dp/chi + fv1)
        swq12 = sq12*(1.0_dp/chi + fv1)
        swq13 = sq13*(1.0_dp/chi + fv1)
        swq14 = sq14*(1.0_dp/chi + fv1)
        swt1  = st1 *(1.0_dp/chi + fv1)

        swq21 = sq21*(1.0_dp/chi + fv1)
        swq22 = sq22*(1.0_dp/chi + fv1)
        swq23 = sq23*(1.0_dp/chi + fv1)
        swq24 = sq24*(1.0_dp/chi + fv1)
        swt2  = st2 *(1.0_dp/chi + fv1)

        distance = slen(node1)
        arg = vkar*vkar*distance*distance

        tanharg = 1.0_dp/re*turb(1,node1)/arg/sw
        tanhargq11 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq11
        tanhargq12 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq12
        tanhargq13 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq13
        tanhargq14 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq14

        tanhargq21 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq21
        tanhargq22 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq22
        tanhargq23 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq23
        tanhargq24 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq24

        if (tanharg < 0.0_dp)                                                  &
          write(*,*) 'Negative tanharg, fix limiting on cosh()'

        rr = tanh(tanharg) / tanh(1.0_dp)
        if(tanharg > 15.0_dp) then
          rrq11 = 0.0_dp
          rrq12 = 0.0_dp
          rrq13 = 0.0_dp
          rrq14 = 0.0_dp

          rrq21 = 0.0_dp
          rrq22 = 0.0_dp
          rrq23 = 0.0_dp
          rrq24 = 0.0_dp
        else
          rrq11=1.0_dp/tanh(1.0_dp)*tanhargq11/cosh(tanharg)/cosh(tanharg)
          rrq12=1.0_dp/tanh(1.0_dp)*tanhargq12/cosh(tanharg)/cosh(tanharg)
          rrq13=1.0_dp/tanh(1.0_dp)*tanhargq13/cosh(tanharg)/cosh(tanharg)
          rrq14=1.0_dp/tanh(1.0_dp)*tanhargq14/cosh(tanharg)/cosh(tanharg)

          rrq21=1.0_dp/tanh(1.0_dp)*tanhargq21/cosh(tanharg)/cosh(tanharg)
          rrq22=1.0_dp/tanh(1.0_dp)*tanhargq22/cosh(tanharg)/cosh(tanharg)
          rrq23=1.0_dp/tanh(1.0_dp)*tanhargq23/cosh(tanharg)/cosh(tanharg)
          rrq24=1.0_dp/tanh(1.0_dp)*tanhargq24/cosh(tanharg)/cosh(tanharg)
        endif

      endif edwards_mod2

      GG   = RR + cw2*(RR**6 - RR)
      GGq11 = RRq11 + cw2*(6.0_dp*RR**5*RRq11 - RRq11)
      GGq12 = RRq12 + cw2*(6.0_dp*RR**5*RRq12 - RRq12)
      GGq13 = RRq13 + cw2*(6.0_dp*RR**5*RRq13 - RRq13)
      GGq14 = RRq14 + cw2*(6.0_dp*RR**5*RRq14 - RRq14)

      GGq21 = RRq21 + cw2*(6.0_dp*RR**5*RRq21 - RRq21)
      GGq22 = RRq22 + cw2*(6.0_dp*RR**5*RRq22 - RRq22)
      GGq23 = RRq23 + cw2*(6.0_dp*RR**5*RRq23 - RRq23)
      GGq24 = RRq24 + cw2*(6.0_dp*RR**5*RRq24 - RRq24)

      onesix = 1.0_dp/6.0_dp
      fivesix = -5.0_dp/6.0_dp
      fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
      term = (1.0_dp + cw3**6)/(GG**6 + cw3**6)
      bottom = (GG**6 + cw3**6)**2
      factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
      fwq11 = GGq11*(term**onesix - factor)
      fwq12 = GGq12*(term**onesix - factor)
      fwq13 = GGq13*(term**onesix - factor)
      fwq14 = GGq14*(term**onesix - factor)

      fwq21 = GGq21*(term**onesix - factor)
      fwq22 = GGq22*(term**onesix - factor)
      fwq23 = GGq23*(term**onesix - factor)
      fwq24 = GGq24*(term**onesix - factor)

      temp_arg = -ct4*chi*chi

      if ( use_bp_model .and. temp_arg < -700.0_dp ) temp_arg = -700.0_dp

      ft2  = ct3*exp(temp_arg)
      ft2q11 = 0.0_dp
      ft2q12 = 0.0_dp
      ft2q13 = 0.0_dp
      ft2q14 = 0.0_dp

      ft2q21 = 0.0_dp
      ft2q22 = 0.0_dp
      ft2q23 = 0.0_dp
      ft2q24 = 0.0_dp

!     Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,node1))
!     Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)  ! for use_bp_model

      if ( use_bp_model ) then

        Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb_abs)
        Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb_abs)
        Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb_abs)
        Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb_abs)
        Prodt1  = (cb1*(1.0_dp - ft2)*Swt1 *turb_abs)

        Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb_abs)
        Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb_abs)
        Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb_abs)
        Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb_abs)
        Prodt2  = (cb1*(1.0_dp - ft2)*Swt2 *turb_abs)

      else

        Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb(1,node1))
        Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb(1,node1))
        Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb(1,node1))
        Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb(1,node1))
        Prodt1  = (cb1*(1.0_dp - ft2)*Swt1*turb(1,node1))

        Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb(1,node1))
        Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb(1,node1))
        Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb(1,node1))
        Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb(1,node1))
        Prodt2  = (cb1*(1.0_dp - ft2)*Swt2*turb(1,node1))

      endif

      vkar2 = vkar*vkar
!     Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                               &
!           *(turb(1,node1)/slen(node1))**2
!     Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                               &
!           *(turb_abs/slen(node1))**2        ! for use_bp_model

      xmr = 1.0_dp/Re

      if ( use_bp_model ) then
        factor = (turb_abs/slen(node1))**2
        Destq11 = xmr*(cw1*fwq11 - cb1/vkar2*ft2q11)*factor
        Destq12 = xmr*(cw1*fwq12 - cb1/vkar2*ft2q12)*factor
        Destq13 = xmr*(cw1*fwq13 - cb1/vkar2*ft2q13)*factor
        Destq14 = xmr*(cw1*fwq14 - cb1/vkar2*ft2q14)*factor
        Destt1  = 0.0_dp
        Destq21 = xmr*(cw1*fwq21 - cb1/vkar2*ft2q21)*factor
        Destq22 = xmr*(cw1*fwq22 - cb1/vkar2*ft2q22)*factor
        Destq23 = xmr*(cw1*fwq23 - cb1/vkar2*ft2q23)*factor
        Destq24 = xmr*(cw1*fwq24 - cb1/vkar2*ft2q24)*factor
        Destt2  = 0.0_dp
      else
        factor = (turb(1,node1)/slen(node1))**2
        Destq11 = xmr*(cw1*fwq11 - cb1/vkar2*ft2q11)*factor
        Destq12 = xmr*(cw1*fwq12 - cb1/vkar2*ft2q12)*factor
        Destq13 = xmr*(cw1*fwq13 - cb1/vkar2*ft2q13)*factor
        Destq14 = xmr*(cw1*fwq14 - cb1/vkar2*ft2q14)*factor
        Destt1  = 0.0_dp
        Destq21 = xmr*(cw1*fwq21 - cb1/vkar2*ft2q21)*factor
        Destq22 = xmr*(cw1*fwq22 - cb1/vkar2*ft2q22)*factor
        Destq23 = xmr*(cw1*fwq23 - cb1/vkar2*ft2q23)*factor
        Destq24 = xmr*(cw1*fwq24 - cb1/vkar2*ft2q24)*factor
        Destt2  = 0.0_dp
      endif

!     source = vol(node1)*(Prod - Dest)
      sourceq11 = vol(node1)*(Prodq11 - Destq11)
      sourceq12 = vol(node1)*(Prodq12 - Destq12)
      sourceq13 = vol(node1)*(Prodq13 - Destq13)
      sourceq14 = vol(node1)*(Prodq14 - Destq14)
      sourcet1  = vol(node1)*(Prodt1  - Destt1 )

      sourceq21 = vol(node1)*(Prodq21 - Destq21)
      sourceq22 = vol(node1)*(Prodq22 - Destq22)
      sourceq23 = vol(node1)*(Prodq23 - Destq23)
      sourceq24 = vol(node1)*(Prodq24 - Destq24)
      sourcet2  = vol(node1)*(Prodt2  - Destt2 )

      res1q11 = -sourceq11
      res1q12 = -sourceq12
      res1q13 = -sourceq13
      res1q14 = -sourceq14
      res1t1  = -sourcet1

      res1q21 = -sourceq21
      res1q22 = -sourceq22
      res1q23 = -sourceq23
      res1q24 = -sourceq24
      res1t2  = -sourcet2

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam5(i) = coltag(5,node1)*rlam(5,node1,i)
          if(node1 <= nnodes0 .and. coltag(5,node1) > 0.1_dp) then
            res(1,node1,i) = res(1,node1,i) + res1q11*rlam5(i)
            res(2,node1,i) = res(2,node1,i) + res1q12*rlam5(i)
            res(3,node1,i) = res(3,node1,i) + res1q13*rlam5(i)
            res(4,node1,i) = res(4,node1,i) + res1q14*rlam5(i)
            res(5,node1,i) = res(5,node1,i) + res1t1 *rlam5(i)
          endif
        end do
      endif

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam5(i) = coltag(5,node1)*rlam(5,node1,i)
          if(node2 <= nnodes0 .and. coltag(5,node1) > 0.1_dp) then
            res(1,node2,i) = res(1,node2,i) + res1q21*rlam5(i)
            res(2,node2,i) = res(2,node2,i) + res1q22*rlam5(i)
            res(3,node2,i) = res(3,node2,i) + res1q23*rlam5(i)
            res(4,node2,i) = res(4,node2,i) + res1q24*rlam5(i)
            res(5,node2,i) = res(5,node2,i) + res1t2 *rlam5(i)
          endif
        end do
      endif

      if ( fill_a ) then
        idiag = iau(node1)
        ioff  = fhelp(2,n)

        if(node1 <= nnodes0) then
          A(5,1,idiag) = A(5,1,idiag) + res1q11
          A(5,2,idiag) = A(5,2,idiag) + res1q12
          A(5,3,idiag) = A(5,3,idiag) + res1q13
          A(5,4,idiag) = A(5,4,idiag) + res1q14
          A(5,5,idiag) = A(5,5,idiag) + res1t1
        endif

        if(node2 <= nnodes0) then
          A(5,1,ioff) = A(5,1,ioff) + res1q21
          A(5,2,ioff) = A(5,2,ioff) + res1q22
          A(5,3,ioff) = A(5,3,ioff) + res1q23
          A(5,4,ioff) = A(5,4,ioff) + res1q24
          A(5,5,ioff) = A(5,5,ioff) + res1t2
        endif
      endif

     end if node1_of_edge_used

!  Now let's do the linearization at node2

! If we are updating the residual at node2, then
! we will pick up a derivative w.r.t. node1.
! Keep in mind that in this part of the code,
! we are only taking into account linearization
! from the vorticity part of the source term; the
! other parts are already taken into account.

! to not divide by slen on surface for -fpe0 NaNa
      node2_of_edge_used : if ( coltag(5,node2) > 0.1_dp ) then

      rnu  = 1.0_dp

      edwards_chi3 : if(.not. use_edwards_mod) then
        chi  = turb(1,node2)/rnu
        if ( use_bp_model .and. chi < 10.0_dp ) then
          chi = 0.05_dp * log ( 1.0_dp + exp( 20.0_dp*chi ) )
        endif
      else edwards_chi3
        chi  = (turb(1,node2)+1.e-12_dp)/rnu
      endif edwards_chi3

      if ( use_bp_model .and. chi < -5.0_dp ) then
        fv1 = 0.0_dp
      else
        fv1 = chi**3/(chi**3 + cv1**3)
      endif

      fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
      bot  = vkar*vkar*slen(node2)*slen(node2)

! Get derivatives of the vorticity source term.
! Since this only contributes to the off-diagonal,
! dont compute linearization w.r.t. node1. (we'll
! do that later)

      ux   = gradx(2,node2)
      uxq11 = -my_1/vol(node2)*xnorm*q2q11
      uxq12 = -my_1/vol(node2)*xnorm*q2q12
      uxq13 = -my_1/vol(node2)*xnorm*q2q13
      uxq14 = -my_1/vol(node2)*xnorm*q2q14
      uxt1  = -my_1/vol(node2)*xnorm*q2t1

      uxq21 = -my_1/vol(node2)*xnorm*q2q21
      uxq22 = -my_1/vol(node2)*xnorm*q2q22
      uxq23 = -my_1/vol(node2)*xnorm*q2q23
      uxq24 = -my_1/vol(node2)*xnorm*q2q24
      uxt2  = -my_1/vol(node2)*xnorm*q2t2


      uy   = grady(2,node2)
      uyq11 = -1.0_dp/vol(node2)*ynorm*q2q11
      uyq12 = -1.0_dp/vol(node2)*ynorm*q2q12
      uyq13 = -1.0_dp/vol(node2)*ynorm*q2q13
      uyq14 = -1.0_dp/vol(node2)*ynorm*q2q14
      uyt1  = -1.0_dp/vol(node2)*ynorm*q2t1

      uyq21 = -1.0_dp/vol(node2)*ynorm*q2q21
      uyq22 = -1.0_dp/vol(node2)*ynorm*q2q22
      uyq23 = -1.0_dp/vol(node2)*ynorm*q2q23
      uyq24 = -1.0_dp/vol(node2)*ynorm*q2q24
      uyt2  = -1.0_dp/vol(node2)*ynorm*q2t2
      uz   = gradz(2,node2)
      uzq11 = -1.0_dp/vol(node2)*znorm*q2q11
      uzq12 = -1.0_dp/vol(node2)*znorm*q2q12
      uzq13 = -1.0_dp/vol(node2)*znorm*q2q13
      uzq14 = -1.0_dp/vol(node2)*znorm*q2q14
      uzt1  = -1.0_dp/vol(node2)*znorm*q2t1

      uzq21 = -1.0_dp/vol(node2)*znorm*q2q21
      uzq22 = -1.0_dp/vol(node2)*znorm*q2q22
      uzq23 = -1.0_dp/vol(node2)*znorm*q2q23
      uzq24 = -1.0_dp/vol(node2)*znorm*q2q24
      uzt2  = -1.0_dp/vol(node2)*znorm*q2t2
      vx   = gradx(3,node2)
      vxq11 = -1.0_dp/vol(node2)*xnorm*q3q11
      vxq12 = -1.0_dp/vol(node2)*xnorm*q3q12
      vxq13 = -1.0_dp/vol(node2)*xnorm*q3q13
      vxq14 = -1.0_dp/vol(node2)*xnorm*q3q14
      vxt1  = -1.0_dp/vol(node2)*xnorm*q3t1

      vxq21 = -1.0_dp/vol(node2)*xnorm*q3q21
      vxq22 = -1.0_dp/vol(node2)*xnorm*q3q22
      vxq23 = -1.0_dp/vol(node2)*xnorm*q3q23
      vxq24 = -1.0_dp/vol(node2)*xnorm*q3q24
      vxt2  = -1.0_dp/vol(node2)*xnorm*q3t2

      vy   = grady(3,node2)
      vyq11 = -my_1/vol(node2)*ynorm*q3q11
      vyq12 = -my_1/vol(node2)*ynorm*q3q12
      vyq13 = -my_1/vol(node2)*ynorm*q3q13
      vyq14 = -my_1/vol(node2)*ynorm*q3q14
      vyt1  = -my_1/vol(node2)*ynorm*q3t1

      vyq21 = -my_1/vol(node2)*ynorm*q3q21
      vyq22 = -my_1/vol(node2)*ynorm*q3q22
      vyq23 = -my_1/vol(node2)*ynorm*q3q23
      vyq24 = -my_1/vol(node2)*ynorm*q3q24
      vyt2  = -my_1/vol(node2)*ynorm*q3t2


      vz   = gradz(3,node2)
      vzq11 = -1.0_dp/vol(node2)*znorm*q3q11
      vzq12 = -1.0_dp/vol(node2)*znorm*q3q12
      vzq13 = -1.0_dp/vol(node2)*znorm*q3q13
      vzq14 = -1.0_dp/vol(node2)*znorm*q3q14
      vzt1  = -1.0_dp/vol(node2)*znorm*q3t1

      vzq21 = -1.0_dp/vol(node2)*znorm*q3q21
      vzq22 = -1.0_dp/vol(node2)*znorm*q3q22
      vzq23 = -1.0_dp/vol(node2)*znorm*q3q23
      vzq24 = -1.0_dp/vol(node2)*znorm*q3q24
      vzt2  = -1.0_dp/vol(node2)*znorm*q3t2
      wx   = gradx(4,node2)
      wxq11 = -1.0_dp/vol(node2)*xnorm*q4q11
      wxq12 = -1.0_dp/vol(node2)*xnorm*q4q12
      wxq13 = -1.0_dp/vol(node2)*xnorm*q4q13
      wxq14 = -1.0_dp/vol(node2)*xnorm*q4q14
      wxt1  = -1.0_dp/vol(node2)*xnorm*q4t1

      wxq21 = -1.0_dp/vol(node2)*xnorm*q4q21
      wxq22 = -1.0_dp/vol(node2)*xnorm*q4q22
      wxq23 = -1.0_dp/vol(node2)*xnorm*q4q23
      wxq24 = -1.0_dp/vol(node2)*xnorm*q4q24
      wxt2  = -1.0_dp/vol(node2)*xnorm*q4t2
      wy   = grady(4,node2)
      wyq11 = -1.0_dp/vol(node2)*ynorm*q4q11
      wyq12 = -1.0_dp/vol(node2)*ynorm*q4q12
      wyq13 = -1.0_dp/vol(node2)*ynorm*q4q13
      wyq14 = -1.0_dp/vol(node2)*ynorm*q4q14
      wyt1  = -1.0_dp/vol(node2)*ynorm*q4t1

      wyq21 = -1.0_dp/vol(node2)*ynorm*q4q21
      wyq22 = -1.0_dp/vol(node2)*ynorm*q4q22
      wyq23 = -1.0_dp/vol(node2)*ynorm*q4q23
      wyq24 = -1.0_dp/vol(node2)*ynorm*q4q24
      wyt2  = -1.0_dp/vol(node2)*ynorm*q4t2

      wz   = gradz(4,node2)
      wzq11 = -my_1/vol(node2)*znorm*q4q11
      wzq12 = -my_1/vol(node2)*znorm*q4q12
      wzq13 = -my_1/vol(node2)*znorm*q4q13
      wzq14 = -my_1/vol(node2)*znorm*q4q14
      wzt1  = -my_1/vol(node2)*znorm*q4t1

      wzq21 = -my_1/vol(node2)*znorm*q4q21
      wzq22 = -my_1/vol(node2)*znorm*q4q22
      wzq23 = -my_1/vol(node2)*znorm*q4q23
      wzq24 = -my_1/vol(node2)*znorm*q4q24
      wzt2  = -my_1/vol(node2)*znorm*q4t2


      if ( has_x_symmetry(symmetry(node2)) ) then

        uy = my_0
          uyq11 = my_0
          uyq12 = my_0
          uyq13 = my_0
          uyq14 = my_0
          uyt1  = my_0

          uyq21 = my_0
          uyq22 = my_0
          uyq23 = my_0
          uyq24 = my_0
          uyt2  = my_0

        uz = my_0
          uzq11 = my_0
          uzq12 = my_0
          uzq13 = my_0
          uzq14 = my_0
          uzt1  = my_0

          uzq21 = my_0
          uzq22 = my_0
          uzq23 = my_0
          uzq24 = my_0
          uzt2  = my_0

        vx = my_0
          vxq11 = my_0
          vxq12 = my_0
          vxq13 = my_0
          vxq14 = my_0
          vxt1  = my_0

          vxq21 = my_0
          vxq22 = my_0
          vxq23 = my_0
          vxq24 = my_0
          vxt2  = my_0

        wx = my_0
          wxq11 = my_0
          wxq12 = my_0
          wxq13 = my_0
          wxq14 = my_0
          wxt1  = my_0

          wxq21 = my_0
          wxq22 = my_0
          wxq23 = my_0
          wxq24 = my_0
          wxt2  = my_0

      endif

      if ( has_y_symmetry(symmetry(node2)) ) then

        vx = my_0
          vxq11 = my_0
          vxq12 = my_0
          vxq13 = my_0
          vxq14 = my_0
          vxt1  = my_0

          vxq21 = my_0
          vxq22 = my_0
          vxq23 = my_0
          vxq24 = my_0
          vxt2  = my_0

        vz = my_0
          vzq11 = my_0
          vzq12 = my_0
          vzq13 = my_0
          vzq14 = my_0
          vzt1  = my_0

          vzq21 = my_0
          vzq22 = my_0
          vzq23 = my_0
          vzq24 = my_0
          vzt2  = my_0

        uy = my_0
          uyq11 = my_0
          uyq12 = my_0
          uyq13 = my_0
          uyq14 = my_0
          uyt1  = my_0

          uyq21 = my_0
          uyq22 = my_0
          uyq23 = my_0
          uyq24 = my_0
          uyt2  = my_0

        wy = my_0
          wyq11 = my_0
          wyq12 = my_0
          wyq13 = my_0
          wyq14 = my_0
          wyt1  = my_0

          wyq21 = my_0
          wyq22 = my_0
          wyq23 = my_0
          wyq24 = my_0
          wyt2  = my_0

      endif

      if ( has_z_symmetry(symmetry(node2)) ) then

        wx = my_0
          wxq11 = my_0
          wxq12 = my_0
          wxq13 = my_0
          wxq14 = my_0
          wxt1  = my_0

          wxq21 = my_0
          wxq22 = my_0
          wxq23 = my_0
          wxq24 = my_0
          wxt2  = my_0

        wy = my_0
          wyq11 = my_0
          wyq12 = my_0
          wyq13 = my_0
          wyq14 = my_0
          wyt1  = my_0

          wyq21 = my_0
          wyq22 = my_0
          wyq23 = my_0
          wyq24 = my_0
          wyt2  = my_0

        uz = my_0
          uzq11 = my_0
          uzq12 = my_0
          uzq13 = my_0
          uzq14 = my_0
          uzt1  = my_0

          uzq21 = my_0
          uzq22 = my_0
          uzq23 = my_0
          uzq24 = my_0
          uzt2  = my_0

        vz = my_0
          vzq11 = my_0
          vzq12 = my_0
          vzq13 = my_0
          vzq14 = my_0
          vzt1  = my_0

          vzq21 = my_0
          vzq22 = my_0
          vzq23 = my_0
          vzq24 = my_0
          vzt2  = my_0

      endif

      S    = sqrt( (wy-vz)**2 + (uz-wx)**2 + (vx-uy)**2)

      if ( S <= 1.0e-8_dp) then

        S = 1.0e-8_dp
        Sq11 = 0.0_dp
        Sq12 = 0.0_dp
        Sq13 = 0.0_dp
        Sq14 = 0.0_dp
        St1  = 0.0_dp

        Sq21 = 0.0_dp
        Sq22 = 0.0_dp
        Sq23 = 0.0_dp
        Sq24 = 0.0_dp
        St2  = 0.0_dp

      else

        Sq11 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq11-vzq11)                            &
                     + 2.0_dp*(uz-wx)*(uzq11-wxq11)                            &
                     + 2.0_dp*(vx-uy)*(vxq11-uyq11))/S
        Sq12 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq12-vzq12)                            &
                     + 2.0_dp*(uz-wx)*(uzq12-wxq12)                            &
                     + 2.0_dp*(vx-uy)*(vxq12-uyq12))/S
        Sq13 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq13-vzq13)                            &
                     + 2.0_dp*(uz-wx)*(uzq13-wxq13)                            &
                     + 2.0_dp*(vx-uy)*(vxq13-uyq13))/S
        Sq14 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq14-vzq14)                            &
                     + 2.0_dp*(uz-wx)*(uzq14-wxq14)                            &
                     + 2.0_dp*(vx-uy)*(vxq14-uyq14))/S
        St1  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt1 -vzt1 )                            &
                     + 2.0_dp*(uz-wx)*(uzt1 -wxt1 )                            &
                     + 2.0_dp*(vx-uy)*(vxt1 -uyt1 ))/S

        Sq21 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq21-vzq21)                            &
                     + 2.0_dp*(uz-wx)*(uzq21-wxq21)                            &
                     + 2.0_dp*(vx-uy)*(vxq21-uyq21))/S
        Sq22 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq22-vzq22)                            &
                     + 2.0_dp*(uz-wx)*(uzq22-wxq22)                            &
                     + 2.0_dp*(vx-uy)*(vxq22-uyq22))/S
        Sq23 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq23-vzq23)                            &
                     + 2.0_dp*(uz-wx)*(uzq23-wxq23)                            &
                     + 2.0_dp*(vx-uy)*(vxq23-uyq23))/S
        Sq24 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq24-vzq24)                            &
                     + 2.0_dp*(uz-wx)*(uzq24-wxq24)                            &
                     + 2.0_dp*(vx-uy)*(vxq24-uyq24))/S
        St2  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt2 -vzt2 )                            &
                     + 2.0_dp*(uz-wx)*(uzt2 -wxt2 )                            &
                     + 2.0_dp*(vx-uy)*(vxt2 -uyt2 ))/S

      endif

      if ( dacles_mariani ) then
        sij_mag = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                          +2._dp*(ux**2+vy**2+wz**2))
          sij_magq11 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq11+vzq11)          &
                                        +  my_2*(uz+wx)*(uzq11+wxq11)          &
                                        +  my_2*(vx+uy)*(vxq11+uyq11)          &
                                               + my_2*(my_2*ux*uxq11           &
                                                     + my_2*vy*vyq11           &
                                                     + my_2*wz*wzq11) )
          sij_magq12 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq12+vzq12)          &
                                        +  my_2*(uz+wx)*(uzq12+wxq12)          &
                                        +  my_2*(vx+uy)*(vxq12+uyq12)          &
                                               + my_2*(my_2*ux*uxq12           &
                                                     + my_2*vy*vyq12           &
                                                     + my_2*wz*wzq12) )
          sij_magq13 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq13+vzq13)          &
                                        +  my_2*(uz+wx)*(uzq13+wxq13)          &
                                        +  my_2*(vx+uy)*(vxq13+uyq13)          &
                                               + my_2*(my_2*ux*uxq13           &
                                                     + my_2*vy*vyq13           &
                                                     + my_2*wz*wzq13) )
          sij_magq14 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq14+vzq14)          &
                                        +  my_2*(uz+wx)*(uzq14+wxq14)          &
                                        +  my_2*(vx+uy)*(vxq14+uyq14)          &
                                               + my_2*(my_2*ux*uxq14           &
                                                     + my_2*vy*vyq14           &
                                                     + my_2*wz*wzq14) )
          sij_magt1  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt1 +vzt1 )          &
                                        +  my_2*(uz+wx)*(uzt1 +wxt1 )          &
                                        +  my_2*(vx+uy)*(vxt1 +uyt1 )          &
                                               + my_2*(my_2*ux*uxt1            &
                                                     + my_2*vy*vyt1            &
                                                     + my_2*wz*wzt1 ) )

          sij_magq21 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq21+vzq21)          &
                                        +  my_2*(uz+wx)*(uzq21+wxq21)          &
                                        +  my_2*(vx+uy)*(vxq21+uyq21)          &
                                               + my_2*(my_2*ux*uxq21           &
                                                     + my_2*vy*vyq21           &
                                                     + my_2*wz*wzq21) )
          sij_magq22 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq22+vzq22)          &
                                        +  my_2*(uz+wx)*(uzq22+wxq22)          &
                                        +  my_2*(vx+uy)*(vxq22+uyq22)          &
                                               + my_2*(my_2*ux*uxq22           &
                                                     + my_2*vy*vyq22           &
                                                     + my_2*wz*wzq22) )
          sij_magq23 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq23+vzq23)          &
                                        +  my_2*(uz+wx)*(uzq23+wxq23)          &
                                        +  my_2*(vx+uy)*(vxq23+uyq23)          &
                                               + my_2*(my_2*ux*uxq23           &
                                                     + my_2*vy*vyq23           &
                                                     + my_2*wz*wzq23) )
          sij_magq24 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq24+vzq24)          &
                                        +  my_2*(uz+wx)*(uzq24+wxq24)          &
                                        +  my_2*(vx+uy)*(vxq24+uyq24)          &
                                               + my_2*(my_2*ux*uxq24           &
                                                     + my_2*vy*vyq24           &
                                                     + my_2*wz*wzq24) )
          sij_magt2  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt2 +vzt2 )          &
                                        +  my_2*(uz+wx)*(uzt2 +wxt2 )          &
                                        +  my_2*(vx+uy)*(vxt2 +uyt2 )          &
                                               + my_2*(my_2*ux*uxt2            &
                                                     + my_2*vy*vyt2            &
                                                     + my_2*wz*wzt2 ) )
        if ( (sij_mag-s) < my_0 ) then

          s = s + 2._dp*min(my_0,sij_mag-s)

          sq11 = sq11 + my_2*(sij_magq11 - sq11)
          sq12 = sq12 + my_2*(sij_magq12 - sq12)
          sq13 = sq13 + my_2*(sij_magq13 - sq13)
          sq14 = sq14 + my_2*(sij_magq14 - sq14)
          st1  = st1  + my_2*(sij_magt1  - st1 )

          sq21 = sq21 + my_2*(sij_magq21 - sq21)
          sq22 = sq22 + my_2*(sij_magq22 - sq22)
          sq23 = sq23 + my_2*(sij_magq23 - sq23)
          sq24 = sq24 + my_2*(sij_magq24 - sq24)
          st2  = st2  + my_2*(sij_magt2  - st2 )
        endif

      end if


!  Remember we are only linearizing vorticity this time

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

      if ( use_bp_model ) turb_abs = chi*rnu

      edwards_mod3 : if(.not. use_edwards_mod) then

        if ( use_bp_model ) then

          Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
          Swq11 = Sq11
          Swq12 = Sq12
          Swq13 = Sq13
          Swq14 = Sq14
          Swt1  = St1

          Swq21 = Sq21
          Swq22 = Sq22
          Swq23 = Sq23
          Swq24 = Sq24
          Swt2  = St2

          if ( Sw < 0.3_dp*S ) then
            s_bar = 1.0_dp/Re*turb_abs/bot*fv2
            term1 = 0.49_dp*S + 0.9_dp*s_bar
              term1q11 = 0.49_dp*Sq11
              term1q12 = 0.49_dp*Sq12
              term1q13 = 0.49_dp*Sq13
              term1q14 = 0.49_dp*Sq14
              term1t1  = 0.49_dp*St1

              term1q21 = 0.49_dp*Sq21
              term1q22 = 0.49_dp*Sq22
              term1q23 = 0.49_dp*Sq23
              term1q24 = 0.49_dp*Sq24
              term1t2  = 0.49_dp*St2

            term2 = -0.5_dp*S - s_bar
              term2q11 = -0.5_dp*Sq11
              term2q12 = -0.5_dp*Sq12
              term2q13 = -0.5_dp*Sq13
              term2q14 = -0.5_dp*Sq14
              term2t1  = -0.5_dp*St1

              term2q21 = -0.5_dp*Sq21
              term2q22 = -0.5_dp*Sq22
              term2q23 = -0.5_dp*Sq23
              term2q24 = -0.5_dp*Sq24
              term2t2  = -0.5_dp*St2

            Sw = S + S*term1/term2
              Swq11 = Sq11 + S*((term2*term1q11-term1*term2q11)/term2/term2)   &
                    + term1/term2*Sq11
              Swq12 = Sq12 + S*((term2*term1q12-term1*term2q12)/term2/term2)   &
                    + term1/term2*Sq12
              Swq13 = Sq13 + S*((term2*term1q13-term1*term2q13)/term2/term2)   &
                    + term1/term2*Sq13
              Swq14 = Sq14 + S*((term2*term1q14-term1*term2q14)/term2/term2)   &
                    + term1/term2*Sq14
              Swt1  = St1  + S*((term2*term1t1 -term1*term2t1 )/term2/term2)   &
                    + term1/term2*St1

              Swq21 = Sq21 + S*((term2*term1q21-term1*term2q21)/term2/term2)   &
                    + term1/term2*Sq21
              Swq22 = Sq22 + S*((term2*term1q22-term1*term2q22)/term2/term2)   &
                    + term1/term2*Sq22
              Swq23 = Sq23 + S*((term2*term1q23-term1*term2q23)/term2/term2)   &
                    + term1/term2*Sq23
              Swq24 = Sq24 + S*((term2*term1q24-term1*term2q24)/term2/term2)   &
                    + term1/term2*Sq24
              Swt2  = St2  + S*((term2*term1t2 -term1*term2t2 )/term2/term2)   &
                    + term1/term2*St2
          endif

        else

          Sw   = S + 1.0_dp/Re*turb(1,node2)/bot*fv2
          Swq11 = Sq11
          Swq12 = Sq12
          Swq13 = Sq13
          Swq14 = Sq14
          Swt1  = St1

          Swq21 = Sq21
          Swq22 = Sq22
          Swq23 = Sq23
          Swq24 = Sq24
          Swt2  = St2

          if(Sw <= 0.00001_dp) then
            Sw = 0.00001_dp
            Swq11 = 0.0_dp
            Swq12 = 0.0_dp
            Swq13 = 0.0_dp
            Swq14 = 0.0_dp
            Swt1  = 0.0_dp

            Swq21 = 0.0_dp
            Swq22 = 0.0_dp
            Swq23 = 0.0_dp
            Swq24 = 0.0_dp
            Swt2  = 0.0_dp
          endif

        endif

        RR   = 1.0_dp/Re*turb(1,node2)/bot/Sw
        RRq11 = -RR*Swq11/Sw
        RRq12 = -RR*Swq12/Sw
        RRq13 = -RR*Swq13/Sw
        RRq14 = -RR*Swq14/Sw

        RRq21 = -RR*Swq21/Sw
        RRq22 = -RR*Swq22/Sw
        RRq23 = -RR*Swq23/Sw
        RRq24 = -RR*Swq24/Sw

        if(RR > 10.0_dp) then
          RR = 10.0_dp
          RRq11 = 0.0_dp
          RRq12 = 0.0_dp
          RRq13 = 0.0_dp
          RRq14 = 0.0_dp

          RRq21 = 0.0_dp
          RRq22 = 0.0_dp
          RRq23 = 0.0_dp
          RRq24 = 0.0_dp
        endif

      else edwards_mod3

        sw = s*(1.0_dp/chi + fv1)
        swq11 = sq11*(1.0_dp/chi + fv1)
        swq12 = sq12*(1.0_dp/chi + fv1)
        swq13 = sq13*(1.0_dp/chi + fv1)
        swq14 = sq14*(1.0_dp/chi + fv1)
        swt1  = st1 *(1.0_dp/chi + fv1)

        swq21 = sq21*(1.0_dp/chi + fv1)
        swq22 = sq22*(1.0_dp/chi + fv1)
        swq23 = sq23*(1.0_dp/chi + fv1)
        swq24 = sq24*(1.0_dp/chi + fv1)
        swt2  = st2 *(1.0_dp/chi + fv1)

        distance = slen(node2)
        arg = vkar*vkar*distance*distance

        tanharg = 1.0_dp/re*turb(1,node2)/arg/sw
        tanhargq11 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq11
        tanhargq12 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq12
        tanhargq13 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq13
        tanhargq14 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq14

        tanhargq21 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq21
        tanhargq22 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq22
        tanhargq23 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq23
        tanhargq24 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq24

        if(tanharg < 0.0_dp)                                                   &
          write(*,*) 'Negative tanharg, fix limiting on cosh()'

        rr = tanh(tanharg) / tanh(1.0_dp)
        if(tanharg > 15.0_dp) then
          rrq11 = 0.0_dp
          rrq12 = 0.0_dp
          rrq13 = 0.0_dp
          rrq14 = 0.0_dp

          rrq21 = 0.0_dp
          rrq22 = 0.0_dp
          rrq23 = 0.0_dp
          rrq24 = 0.0_dp
        else
          rrq11=1.0_dp/tanh(1.0_dp)*tanhargq11/cosh(tanharg)/cosh(tanharg)
          rrq12=1.0_dp/tanh(1.0_dp)*tanhargq12/cosh(tanharg)/cosh(tanharg)
          rrq13=1.0_dp/tanh(1.0_dp)*tanhargq13/cosh(tanharg)/cosh(tanharg)
          rrq14=1.0_dp/tanh(1.0_dp)*tanhargq14/cosh(tanharg)/cosh(tanharg)

          rrq21=1.0_dp/tanh(1.0_dp)*tanhargq21/cosh(tanharg)/cosh(tanharg)
          rrq22=1.0_dp/tanh(1.0_dp)*tanhargq22/cosh(tanharg)/cosh(tanharg)
          rrq23=1.0_dp/tanh(1.0_dp)*tanhargq23/cosh(tanharg)/cosh(tanharg)
          rrq24=1.0_dp/tanh(1.0_dp)*tanhargq24/cosh(tanharg)/cosh(tanharg)
        endif

      endif edwards_mod3

      GG   = RR + cw2*(RR**6 - RR)
      GGq11 = RRq11 + cw2*(6.0_dp*RR**5*RRq11 - RRq11)
      GGq12 = RRq12 + cw2*(6.0_dp*RR**5*RRq12 - RRq12)
      GGq13 = RRq13 + cw2*(6.0_dp*RR**5*RRq13 - RRq13)
      GGq14 = RRq14 + cw2*(6.0_dp*RR**5*RRq14 - RRq14)

      GGq21 = RRq21 + cw2*(6.0_dp*RR**5*RRq21 - RRq21)
      GGq22 = RRq22 + cw2*(6.0_dp*RR**5*RRq22 - RRq22)
      GGq23 = RRq23 + cw2*(6.0_dp*RR**5*RRq23 - RRq23)
      GGq24 = RRq24 + cw2*(6.0_dp*RR**5*RRq24 - RRq24)

      onesix = 1.0_dp/6.0_dp
      fivesix = -5.0_dp/6.0_dp
      fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
      term = (1.0_dp + cw3**6)/(GG**6 + cw3**6)
      bottom = (GG**6 + cw3**6)**2
      factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
      fwq11 = GGq11*(term**onesix - factor)
      fwq12 = GGq12*(term**onesix - factor)
      fwq13 = GGq13*(term**onesix - factor)
      fwq14 = GGq14*(term**onesix - factor)

      fwq21 = GGq21*(term**onesix - factor)
      fwq22 = GGq22*(term**onesix - factor)
      fwq23 = GGq23*(term**onesix - factor)
      fwq24 = GGq24*(term**onesix - factor)

      temp_arg = -ct4*chi*chi

      if ( use_bp_model .and. temp_arg < -700.0_dp ) temp_arg = -700.0_dp

      ft2  = ct3*exp(temp_arg)
      ft2q11 = 0.0_dp
      ft2q12 = 0.0_dp
      ft2q13 = 0.0_dp
      ft2q14 = 0.0_dp

      ft2q21 = 0.0_dp
      ft2q22 = 0.0_dp
      ft2q23 = 0.0_dp
      ft2q24 = 0.0_dp

!     Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,node2))
!     Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)   ! for use_bp_model

      if ( use_bp_model ) then

        Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb_abs)
        Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb_abs)
        Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb_abs)
        Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb_abs)
        Prodt1  = (cb1*(1.0_dp - ft2)*Swt1 *turb_abs)

        Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb_abs)
        Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb_abs)
        Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb_abs)
        Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb_abs)
        Prodt2  = (cb1*(1.0_dp - ft2)*Swt2 *turb_abs)

      else

        Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb(1,node2))
        Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb(1,node2))
        Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb(1,node2))
        Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb(1,node2))
        Prodt1  = (cb1*(1.0_dp - ft2)*Swt1*turb(1,node2))

        Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb(1,node2))
        Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb(1,node2))
        Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb(1,node2))
        Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb(1,node2))
        Prodt2  = (cb1*(1.0_dp - ft2)*Swt2*turb(1,node2))

      endif

      vkar2 = vkar*vkar

!     Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                                &
!           *(turb(1,node2)/slen(node2))**2
!     Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                                &
!           *(turb_abs/slen(node2))**2   ! for use_bp_model

      xmr = 1.0_dp/Re

      if ( use_bp_model ) then

        factor = (turb_abs/slen(node2))**2
        Destq11 = xmr*(cw1*fwq11 - cb1/vkar2*ft2q11)*factor
        Destq12 = xmr*(cw1*fwq12 - cb1/vkar2*ft2q12)*factor
        Destq13 = xmr*(cw1*fwq13 - cb1/vkar2*ft2q13)*factor
        Destq14 = xmr*(cw1*fwq14 - cb1/vkar2*ft2q14)*factor
        Destt1  = 0.0_dp

        Destq21 = xmr*(cw1*fwq21 - cb1/vkar2*ft2q21)*factor
        Destq22 = xmr*(cw1*fwq22 - cb1/vkar2*ft2q22)*factor
        Destq23 = xmr*(cw1*fwq23 - cb1/vkar2*ft2q23)*factor
        Destq24 = xmr*(cw1*fwq24 - cb1/vkar2*ft2q24)*factor
        Destt2  = 0.0_dp

      else

        factor = (turb(1,node2)/slen(node2))**2
        Destq11 = xmr*(cw1*fwq11 - cb1/vkar2*ft2q11)*factor
        Destq12 = xmr*(cw1*fwq12 - cb1/vkar2*ft2q12)*factor
        Destq13 = xmr*(cw1*fwq13 - cb1/vkar2*ft2q13)*factor
        Destq14 = xmr*(cw1*fwq14 - cb1/vkar2*ft2q14)*factor
        Destt1  = 0.0_dp

        Destq21 = xmr*(cw1*fwq21 - cb1/vkar2*ft2q21)*factor
        Destq22 = xmr*(cw1*fwq22 - cb1/vkar2*ft2q22)*factor
        Destq23 = xmr*(cw1*fwq23 - cb1/vkar2*ft2q23)*factor
        Destq24 = xmr*(cw1*fwq24 - cb1/vkar2*ft2q24)*factor
        Destt2  = 0.0_dp

      endif

!     source = vol(node2)*(Prod - Dest)
      sourceq11 = vol(node2)*(Prodq11 - Destq11)
      sourceq12 = vol(node2)*(Prodq12 - Destq12)
      sourceq13 = vol(node2)*(Prodq13 - Destq13)
      sourceq14 = vol(node2)*(Prodq14 - Destq14)
      sourcet1  = vol(node2)*(Prodt1  - Destt1 )

      sourceq21 = vol(node2)*(Prodq21 - Destq21)
      sourceq22 = vol(node2)*(Prodq22 - Destq22)
      sourceq23 = vol(node2)*(Prodq23 - Destq23)
      sourceq24 = vol(node2)*(Prodq24 - Destq24)
      sourcet2  = vol(node2)*(Prodt2  - Destt2 )

      res2q11 = -sourceq11
      res2q12 = -sourceq12
      res2q13 = -sourceq13
      res2q14 = -sourceq14
      res2t1  = -sourcet1

      res2q21 = -sourceq21
      res2q22 = -sourceq22
      res2q23 = -sourceq23
      res2q24 = -sourceq24
      res2t2  = -sourcet2

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam5(i) = coltag(5,node2)*rlam(5,node2,i)
          if(node2 <= nnodes0 .and. coltag(5,node2) > 0.1_dp) then
            res(1,node2,i) = res(1,node2,i) + res2q21*rlam5(i)
            res(2,node2,i) = res(2,node2,i) + res2q22*rlam5(i)
            res(3,node2,i) = res(3,node2,i) + res2q23*rlam5(i)
            res(4,node2,i) = res(4,node2,i) + res2q24*rlam5(i)
            res(5,node2,i) = res(5,node2,i) + res2t2 *rlam5(i)
          endif
        end do
      endif

      if ( fill_res ) then
        do i = 1, nfunctions
          rlam5(i) = coltag(5,node2)*rlam(5,node2,i)
          if(node1 <= nnodes0.and. coltag(5,node2) > 0.1_dp) then
            res(1,node1,i) = res(1,node1,i) + res2q11*rlam5(i)
            res(2,node1,i) = res(2,node1,i) + res2q12*rlam5(i)
            res(3,node1,i) = res(3,node1,i) + res2q13*rlam5(i)
            res(4,node1,i) = res(4,node1,i) + res2q14*rlam5(i)
            res(5,node1,i) = res(5,node1,i) + res2t1 *rlam5(i)
          endif
        end do
      endif

      if ( fill_a ) then
        idiag = iau(node2)
        ioff  = fhelp(1,n)

        if(node2 <= nnodes0) then
          A(5,1,idiag) = A(5,1,idiag) + res2q21
          A(5,2,idiag) = A(5,2,idiag) + res2q22
          A(5,3,idiag) = A(5,3,idiag) + res2q23
          A(5,4,idiag) = A(5,4,idiag) + res2q24
          A(5,5,idiag) = A(5,5,idiag) + res2t2
        endif

        if(node1 <= nnodes0) then
          A(5,1,ioff) = A(5,1,ioff) + res2q11
          A(5,2,ioff) = A(5,2,ioff) + res2q12
          A(5,3,ioff) = A(5,3,ioff) + res2q13
          A(5,4,ioff) = A(5,4,ioff) + res2q14
          A(5,5,ioff) = A(5,5,ioff) + res2t1
        endif
      endif

    end if node2_of_edge_used

    end do edge_off_diag_production_3060

  end subroutine turbpart_sourcei


!============================= BC_TURBPART_SOURCEI ===========================80
!
!                                 t
!  This routine computes the dR/dQ * lambda contribution from the
!  turbulence model for the residual
!
!  Boundary contributions
!
!=============================================================================80
  subroutine bc_turbpart_sourcei(nnodes0,nnodes01,x,y,z,gradx,grady,gradz,turb,&
                                 slen,vol,nbound,bc,nfunctions,ndim,adim,      &
                                 n_turb,coltag,elem,dxdt,dydt,dzdt,symmetry,   &
                                 rlam,res,ia,ja,iau,fhelp,a)

    use kinddefs,              only : dp
    use info_depr,             only : re
    use turb_sa_const,         only : ct3, cv1, vkar, cw2, cw3, ct4, cb1, cw1, &
                                      dacles_mariani, use_edwards_mod
    use bc_types,              only : bcgrid_type
    use design_types,          only : max_functions
    use lmpi,                  only : lmpi_master, lmpi_die
    use element_types,         only : elem_type
    use element_based_bc_util, only : element_based_metrics
    use flux_symmetry,         only : has_x_symmetry, has_y_symmetry,          &
                                      has_z_symmetry
    use adjoint_switches,      only : use_bp_model

    integer, intent(in) :: nnodes0,nnodes01,nfunctions,n_turb
    integer, intent(in) :: ndim,adim
    integer, intent(in) :: nbound

    integer, dimension(:),   intent(in) :: symmetry
    integer, dimension(:),   intent(in), optional :: iau,ia,ja
    integer, dimension(:,:), intent(in), optional :: fhelp

    real(dp),dimension(nnodes01),        intent(in) :: x,y,z
    real(dp),dimension(nnodes01),        intent(in) :: dxdt, dydt, dzdt
    real(dp),dimension(ndim,nnodes01),   intent(in) :: gradx
    real(dp),dimension(ndim,nnodes01),   intent(in) :: grady
    real(dp),dimension(ndim,nnodes01),   intent(in) :: gradz
    real(dp),dimension(nnodes01),        intent(in) :: slen,vol
    real(dp),dimension(n_turb,nnodes01), intent(in) :: turb
    real(dp),dimension(adim,nnodes01),   intent(in) :: coltag
    real(dp),dimension(:,:,:),           intent(in),    optional :: rlam
    real(dp),dimension(:,:,:),           intent(inout), optional :: res
    real(dp),dimension(:,:,:),           intent(inout), optional :: a

    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(elem_type),   dimension(:),      intent(in) :: elem

    integer :: ib,node3,ielem,node,jstart,jend
    integer :: i,node1,node2,n,j,idiag,ioff1,ioff2,jcol
    integer :: quad_corner

    integer, dimension(4) :: quad_node

    real(dp) :: xnorm,ynorm,znorm,xmr,area,face_speed

    real(dp) :: x1,x2,x3,y1,y2,y3,z1,z2,z3

    real(dp) :: onesix

    real(dp) :: chi
    real(dp) :: turb_abs
    real(dp) :: s_bar
    real(dp) :: term1
    real(dp) :: term1q11,term1q12,term1q13,term1q14,term1t1
    real(dp) :: term1q21,term1q22,term1q23,term1q24,term1t2
    real(dp) :: term1q31,term1q32,term1q33,term1q34,term1t3
    real(dp) :: term1q1,term1q2,term1q3,term1q4,term1t
    real(dp) :: term2
    real(dp) :: term2q11,term2q12,term2q13,term2q14,term2t1
    real(dp) :: term2q21,term2q22,term2q23,term2q24,term2t2
    real(dp) :: term2q31,term2q32,term2q33,term2q34,term2t3
    real(dp) :: term2q1,term2q2,term2q3,term2q4,term2t
    real(dp) :: temp_arg

    real(dp) :: bot,arg,bottom

    real(dp) :: destq1,destq2,destq3,destq4,destt
    real(dp) :: destq11,destq12,destq13,destq14,destt1
    real(dp) :: destq21,destq22,destq23,destq24,destt2
    real(dp) :: destq31,destq32,destq33,destq34,destt3

    real(dp) :: c68,c18,ax,ay,az,bx,by,bz

    real(dp) :: fv1
    real(dp) :: fv2

    real(dp) :: distance,fivesix,factor

    real(dp) :: gg
    real(dp) :: ggq1,ggq2,ggq3,ggq4
    real(dp) :: ggq11,ggq12,ggq13,ggq14
    real(dp) :: ggq21,ggq22,ggq23,ggq24
    real(dp) :: ggq31,ggq32,ggq33,ggq34

    real(dp) :: fwq1,fwq2,fwq3,fwq4
    real(dp) :: fwq11,fwq12,fwq13,fwq14
    real(dp) :: fwq21,fwq22,fwq23,fwq24
    real(dp) :: fwq31,fwq32,fwq33,fwq34

    real(dp) :: ft2

    real(dp) :: prodq1,prodq2,prodq3,prodq4,prodt
    real(dp) :: prodq11,prodq12,prodq13,prodq14,prodt1
    real(dp) :: prodq21,prodq22,prodq23,prodq24,prodt2
    real(dp) :: prodq31,prodq32,prodq33,prodq34,prodt3

    real(dp) :: q2q11,q2q12,q2q13,q2q14,q2t1
    real(dp) :: q2q21,q2q22,q2q23,q2q24,q2t2
    real(dp) :: q2q31,q2q32,q2q33,q2q34,q2t3

    real(dp) :: q3q11,q3q12,q3q13,q3q14,q3t1
    real(dp) :: q3q21,q3q22,q3q23,q3q24,q3t2
    real(dp) :: q3q31,q3q32,q3q33,q3q34,q3t3

    real(dp) :: q4q11,q4q12,q4q13,q4q14,q4t1
    real(dp) :: q4q21,q4q22,q4q23,q4q24,q4t2
    real(dp) :: q4q31,q4q32,q4q33,q4q34,q4t3

    real(dp) :: resq1,resq2,resq3,resq4,rest
    real(dp) :: res1q11,res1q12,res1q13,res1q14,res1t1
    real(dp) :: res1q21,res1q22,res1q23,res1q24,res1t2
    real(dp) :: res1q31,res1q32,res1q33,res1q34,res1t3

    real(dp) :: res2q11,res2q12,res2q13,res2q14,res2t1
    real(dp) :: res2q21,res2q22,res2q23,res2q24,res2t2
    real(dp) :: res2q31,res2q32,res2q33,res2q34,res2t3

    real(dp) :: res3q11,res3q12,res3q13,res3q14,res3t1
    real(dp) :: res3q21,res3q22,res3q23,res3q24,res3t2
    real(dp) :: res3q31,res3q32,res3q33,res3q34,res3t3

    real(dp) :: rnu

    real(dp) :: s
    real(dp) :: sq1,sq2,sq3,sq4,st
    real(dp) :: sq11,sq12,sq13,sq14,st1
    real(dp) :: sq21,sq22,sq23,sq24,st2
    real(dp) :: sq31,sq32,sq33,sq34,st3

    real(dp) :: sw
    real(dp) :: swq1,swq2,swq3,swq4,swt
    real(dp) :: swq11,swq12,swq13,swq14,swt1
    real(dp) :: swq21,swq22,swq23,swq24,swt2
    real(dp) :: swq31,swq32,swq33,swq34,swt3

    real(dp) :: rr
    real(dp) :: rrq1,rrq2,rrq3,rrq4
    real(dp) :: rrq11,rrq12,rrq13,rrq14
    real(dp) :: rrq21,rrq22,rrq23,rrq24
    real(dp) :: rrq31,rrq32,rrq33,rrq34

    real(dp) :: sourceq1,sourceq2,sourceq3,sourceq4,sourcet
    real(dp) :: sourceq11,sourceq12,sourceq13,sourceq14,sourcet1
    real(dp) :: sourceq21,sourceq22,sourceq23,sourceq24,sourcet2
    real(dp) :: sourceq31,sourceq32,sourceq33,sourceq34,sourcet3

    real(dp) :: tanharg
    real(dp) :: tanhargq1,tanhargq2,tanhargq3,tanhargq4
    real(dp) :: tanhargq11,tanhargq12,tanhargq13,tanhargq14
    real(dp) :: tanhargq21,tanhargq22,tanhargq23,tanhargq24
    real(dp) :: tanhargq31,tanhargq32,tanhargq33,tanhargq34

    real(dp) :: term

    real(dp) :: uy,uyd
    real(dp) :: uyq1,uyq2,uyq3,uyq4,uyt
    real(dp) :: uyq11,uyq12,uyq13,uyq14,uyt1
    real(dp) :: uyq21,uyq22,uyq23,uyq24,uyt2
    real(dp) :: uyq31,uyq32,uyq33,uyq34,uyt3

    real(dp) :: uz,uzd
    real(dp) :: uzq1,uzq2,uzq3,uzq4,uzt
    real(dp) :: uzq11,uzq12,uzq13,uzq14,uzt1
    real(dp) :: uzq21,uzq22,uzq23,uzq24,uzt2
    real(dp) :: uzq31,uzq32,uzq33,uzq34,uzt3

    real(dp) :: vx,vxd
    real(dp) :: vxq1,vxq2,vxq3,vxq4,vxt
    real(dp) :: vxq11,vxq12,vxq13,vxq14,vxt1
    real(dp) :: vxq21,vxq22,vxq23,vxq24,vxt2
    real(dp) :: vxq31,vxq32,vxq33,vxq34,vxt3

    real(dp) :: vz,vzd
    real(dp) :: vzq1,vzq2,vzq3,vzq4,vzt
    real(dp) :: vzq11,vzq12,vzq13,vzq14,vzt1
    real(dp) :: vzq21,vzq22,vzq23,vzq24,vzt2
    real(dp) :: vzq31,vzq32,vzq33,vzq34,vzt3

    real(dp) :: wx,wxd
    real(dp) :: wxq1,wxq2,wxq3,wxq4,wxt
    real(dp) :: wxq11,wxq12,wxq13,wxq14,wxt1
    real(dp) :: wxq21,wxq22,wxq23,wxq24,wxt2
    real(dp) :: wxq31,wxq32,wxq33,wxq34,wxt3

    real(dp) :: wy,wyd
    real(dp) :: wyq1,wyq2,wyq3,wyq4,wyt
    real(dp) :: wyq11,wyq12,wyq13,wyq14,wyt1
    real(dp) :: wyq21,wyq22,wyq23,wyq24,wyt2
    real(dp) :: wyq31,wyq32,wyq33,wyq34,wyt3

    real(dp) :: ux,vy,wz,sij_mag
    real(dp) :: uxq1,uxq2,uxq3,uxq4,uxt
    real(dp) :: uxq11,uxq12,uxq13,uxq14,uxt1
    real(dp) :: uxq21,uxq22,uxq23,uxq24,uxt2
    real(dp) :: uxq31,uxq32,uxq33,uxq34,uxt3
    real(dp) :: vyq1,vyq2,vyq3,vyq4,vyt
    real(dp) :: vyq11,vyq12,vyq13,vyq14,vyt1
    real(dp) :: vyq21,vyq22,vyq23,vyq24,vyt2
    real(dp) :: vyq31,vyq32,vyq33,vyq34,vyt3
    real(dp) :: wzq1,wzq2,wzq3,wzq4,wzt
    real(dp) :: wzq11,wzq12,wzq13,wzq14,wzt1
    real(dp) :: wzq21,wzq22,wzq23,wzq24,wzt2
    real(dp) :: wzq31,wzq32,wzq33,wzq34,wzt3
    real(dp) :: sij_magq1,sij_magq2,sij_magq3,sij_magq4,sij_magt
    real(dp) :: sij_magq11,sij_magq12,sij_magq13,sij_magq14,sij_magt1
    real(dp) :: sij_magq21,sij_magq22,sij_magq23,sij_magq24,sij_magt2
    real(dp) :: sij_magq31,sij_magq32,sij_magq33,sij_magq34,sij_magt3

    real(dp), dimension(max_functions) :: rlam5
    real(dp), dimension(4) :: quad_weight

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_half= 0.5_dp

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(rlam) ) then
        if ( lmpi_master ) then
          write(*,*)'res requested in bc_turbpart_source but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if (.not.present(iau) .or. .not.present(fhelp) .or. .not.present(ia) .or.&
          .not.present(ja)) then
        if ( lmpi_master ) then
          write(*,*)'a requested in bc_turbpart_source but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    if ( use_bp_model ) then
      if ( use_edwards_mod .or. dacles_mariani ) then
        write(*,*) 'Option not accounted for in bc_turbpart_source...'
        call lmpi_die
        stop
      endif
    endif

    turb_abs = 0.0_dp

    loop_bound : do ib = 1, nbound

      face_boundary_tris : do n = 1, bc(ib)%nbfacet

        node1 = bc(ib)%ibnode(bc(ib)%f2ntb(n,1))
        node2 = bc(ib)%ibnode(bc(ib)%f2ntb(n,2))
        node3 = bc(ib)%ibnode(bc(ib)%f2ntb(n,3))
        ielem = bc(ib)%f2ntb(n,5)

! weighting switches based on type of cell!

        c68 = 6.0_dp/8.0_dp
        c18 = 1.0_dp/8.0_dp

        if ( trim(elem(ielem)%type_cell) /= 'tet' ) then
          c68 = 1.0_dp
          c18 = 0.0_dp
        endif

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

!           norm points away from grid interior
!           norm magnitude is 1/3 of surface triangle area

        xnorm = -0.5_dp*(ay*bz - by*az)/3.0_dp
        ynorm =  0.5_dp*(ax*bz - bx*az)/3.0_dp
        znorm = -0.5_dp*(ax*by - bx*ay)/3.0_dp

!       q2 = c68*u1 + c18*(u2 + u3)
        q2q11 = 0.0_dp
        q2q12 = c68
        q2q13 = 0.0_dp
        q2q14 = 0.0_dp
        q2t1  = 0.0_dp

        q2q21 = 0.0_dp
        q2q22 = c18
        q2q23 = 0.0_dp
        q2q24 = 0.0_dp
        q2t2  = 0.0_dp

        q2q31 = 0.0_dp
        q2q32 = c18
        q2q33 = 0.0_dp
        q2q34 = 0.0_dp
        q2t3  = 0.0_dp

!       q3 = c68*v1 + c18*(v2 + v3)
        q3q11 = 0.0_dp
        q3q12 = 0.0_dp
        q3q13 = c68
        q3q14 = 0.0_dp
        q3t1  = 0.0_dp

        q3q21 = 0.0_dp
        q3q22 = 0.0_dp
        q3q23 = c18
        q3q24 = 0.0_dp
        q3t2  = 0.0_dp

        q3q31 = 0.0_dp
        q3q32 = 0.0_dp
        q3q33 = c18
        q3q34 = 0.0_dp
        q3t3  = 0.0_dp

!       q4 = c68*w1 + c18*(w2 + w3)
        q4q11 = 0.0_dp
        q4q12 = 0.0_dp
        q4q13 = 0.0_dp
        q4q14 = c68
        q4t1  = 0.0_dp

        q4q21 = 0.0_dp
        q4q22 = 0.0_dp
        q4q23 = 0.0_dp
        q4q24 = c18
        q4t2  = 0.0_dp

        q4q31 = 0.0_dp
        q4q32 = 0.0_dp
        q4q33 = 0.0_dp
        q4q34 = c18
        q4t3  = 0.0_dp

! If we are updating the residual at node1, then
! we will pick up a derivative w.r.t. node2 and node3.
! Keep in mind that in this part of the code,
! we are only taking into account linearization
! from the vorticity part of the source term; the
! other parts are already taken into account.

! to not divide by slen on surface for -fpe0 NaNa
      node1_of_face_used : if ( coltag(5,node1) > 0.1_dp ) then

! dR1/d()2
        rnu  = 1.0_dp

        edwards_chi4 : if(.not. use_edwards_mod) then
          chi  = turb(1,node1)/rnu
          if ( use_bp_model .and. chi < 10.0_dp ) then
            chi = 0.05_dp * log ( 1.0_dp + exp( 20.0_dp*chi ) )
          endif
        else edwards_chi4
          chi  = (turb(1,node1)+1.e-12_dp)/rnu
        endif edwards_chi4

        if ( use_bp_model .and. chi < -5.0_dp ) then
          fv1 = 0.0_dp
        else
          fv1 = chi**3/(chi**3 + cv1**3)
        endif

        fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
        bot  = vkar*vkar*slen(node1)*slen(node1)

! Get derivatives of the vorticity source term.
! Since this only contributes to the off-diagonal,
! dont compute linearization w.r.t. node1. (we'll
! do that later)

!       uxd  = gradx(2,node1)
        ux   = gradx(2,node1)
        uxq11 = my_1/vol(node1)*xnorm*q2q11
        uxq12 = my_1/vol(node1)*xnorm*q2q12
        uxq13 = my_1/vol(node1)*xnorm*q2q13
        uxq14 = my_1/vol(node1)*xnorm*q2q14
        uxt1  = my_1/vol(node1)*xnorm*q2t1

        uxq21 = my_1/vol(node1)*xnorm*q2q21
        uxq22 = my_1/vol(node1)*xnorm*q2q22
        uxq23 = my_1/vol(node1)*xnorm*q2q23
        uxq24 = my_1/vol(node1)*xnorm*q2q24
        uxt2  = my_1/vol(node1)*xnorm*q2t2

        uxq31 = my_1/vol(node1)*xnorm*q2q31
        uxq32 = my_1/vol(node1)*xnorm*q2q32
        uxq33 = my_1/vol(node1)*xnorm*q2q33
        uxq34 = my_1/vol(node1)*xnorm*q2q34
        uxt3  = my_1/vol(node1)*xnorm*q2t3


        uyd  = grady(2,node1)
        uy   = grady(2,node1)
        uyq11 = 1.0_dp/vol(node1)*ynorm*q2q11
        uyq12 = 1.0_dp/vol(node1)*ynorm*q2q12
        uyq13 = 1.0_dp/vol(node1)*ynorm*q2q13
        uyq14 = 1.0_dp/vol(node1)*ynorm*q2q14
        uyt1  = 1.0_dp/vol(node1)*ynorm*q2t1

        uyq21 = 1.0_dp/vol(node1)*ynorm*q2q21
        uyq22 = 1.0_dp/vol(node1)*ynorm*q2q22
        uyq23 = 1.0_dp/vol(node1)*ynorm*q2q23
        uyq24 = 1.0_dp/vol(node1)*ynorm*q2q24
        uyt2  = 1.0_dp/vol(node1)*ynorm*q2t2

        uyq31 = 1.0_dp/vol(node1)*ynorm*q2q31
        uyq32 = 1.0_dp/vol(node1)*ynorm*q2q32
        uyq33 = 1.0_dp/vol(node1)*ynorm*q2q33
        uyq34 = 1.0_dp/vol(node1)*ynorm*q2q34
        uyt3  = 1.0_dp/vol(node1)*ynorm*q2t3

        uzd  = gradz(2,node1)
        uz   = gradz(2,node1)
        uzq11 = 1.0_dp/vol(node1)*znorm*q2q11
        uzq12 = 1.0_dp/vol(node1)*znorm*q2q12
        uzq13 = 1.0_dp/vol(node1)*znorm*q2q13
        uzq14 = 1.0_dp/vol(node1)*znorm*q2q14
        uzt1  = 1.0_dp/vol(node1)*znorm*q2t1

        uzq21 = 1.0_dp/vol(node1)*znorm*q2q21
        uzq22 = 1.0_dp/vol(node1)*znorm*q2q22
        uzq23 = 1.0_dp/vol(node1)*znorm*q2q23
        uzq24 = 1.0_dp/vol(node1)*znorm*q2q24
        uzt2  = 1.0_dp/vol(node1)*znorm*q2t2

        uzq31 = 1.0_dp/vol(node1)*znorm*q2q31
        uzq32 = 1.0_dp/vol(node1)*znorm*q2q32
        uzq33 = 1.0_dp/vol(node1)*znorm*q2q33
        uzq34 = 1.0_dp/vol(node1)*znorm*q2q34
        uzt3  = 1.0_dp/vol(node1)*znorm*q2t3

        vxd  = gradx(3,node1)
        vx   = gradx(3,node1)
        vxq11 = 1.0_dp/vol(node1)*xnorm*q3q11
        vxq12 = 1.0_dp/vol(node1)*xnorm*q3q12
        vxq13 = 1.0_dp/vol(node1)*xnorm*q3q13
        vxq14 = 1.0_dp/vol(node1)*xnorm*q3q14
        vxt1  = 1.0_dp/vol(node1)*xnorm*q3t1

        vxq21 = 1.0_dp/vol(node1)*xnorm*q3q21
        vxq22 = 1.0_dp/vol(node1)*xnorm*q3q22
        vxq23 = 1.0_dp/vol(node1)*xnorm*q3q23
        vxq24 = 1.0_dp/vol(node1)*xnorm*q3q24
        vxt2  = 1.0_dp/vol(node1)*xnorm*q3t2

        vxq31 = 1.0_dp/vol(node1)*xnorm*q3q31
        vxq32 = 1.0_dp/vol(node1)*xnorm*q3q32
        vxq33 = 1.0_dp/vol(node1)*xnorm*q3q33
        vxq34 = 1.0_dp/vol(node1)*xnorm*q3q34
        vxt3  = 1.0_dp/vol(node1)*xnorm*q3t3

!       vyd  = grady(3,node1)
        vy   = grady(3,node1)
        vyq11 = my_1/vol(node1)*ynorm*q3q11
        vyq12 = my_1/vol(node1)*ynorm*q3q12
        vyq13 = my_1/vol(node1)*ynorm*q3q13
        vyq14 = my_1/vol(node1)*ynorm*q3q14
        vyt1  = my_1/vol(node1)*ynorm*q3t1

        vyq21 = my_1/vol(node1)*ynorm*q3q21
        vyq22 = my_1/vol(node1)*ynorm*q3q22
        vyq23 = my_1/vol(node1)*ynorm*q3q23
        vyq24 = my_1/vol(node1)*ynorm*q3q24
        vyt2  = my_1/vol(node1)*ynorm*q3t2

        vyq31 = my_1/vol(node1)*ynorm*q3q31
        vyq32 = my_1/vol(node1)*ynorm*q3q32
        vyq33 = my_1/vol(node1)*ynorm*q3q33
        vyq34 = my_1/vol(node1)*ynorm*q3q34
        vyt3  = my_1/vol(node1)*ynorm*q3t3


        vzd  = gradz(3,node1)
        vz   = gradz(3,node1)
        vzq11 = 1.0_dp/vol(node1)*znorm*q3q11
        vzq12 = 1.0_dp/vol(node1)*znorm*q3q12
        vzq13 = 1.0_dp/vol(node1)*znorm*q3q13
        vzq14 = 1.0_dp/vol(node1)*znorm*q3q14
        vzt1  = 1.0_dp/vol(node1)*znorm*q3t1

        vzq21 = 1.0_dp/vol(node1)*znorm*q3q21
        vzq22 = 1.0_dp/vol(node1)*znorm*q3q22
        vzq23 = 1.0_dp/vol(node1)*znorm*q3q23
        vzq24 = 1.0_dp/vol(node1)*znorm*q3q24
        vzt2  = 1.0_dp/vol(node1)*znorm*q3t2

        vzq31 = 1.0_dp/vol(node1)*znorm*q3q31
        vzq32 = 1.0_dp/vol(node1)*znorm*q3q32
        vzq33 = 1.0_dp/vol(node1)*znorm*q3q33
        vzq34 = 1.0_dp/vol(node1)*znorm*q3q34
        vzt3  = 1.0_dp/vol(node1)*znorm*q3t3

        wxd  = gradx(4,node1)
        wx   = gradx(4,node1)
        wxq11 = 1.0_dp/vol(node1)*xnorm*q4q11
        wxq12 = 1.0_dp/vol(node1)*xnorm*q4q12
        wxq13 = 1.0_dp/vol(node1)*xnorm*q4q13
        wxq14 = 1.0_dp/vol(node1)*xnorm*q4q14
        wxt1  = 1.0_dp/vol(node1)*xnorm*q4t1

        wxq21 = 1.0_dp/vol(node1)*xnorm*q4q21
        wxq22 = 1.0_dp/vol(node1)*xnorm*q4q22
        wxq23 = 1.0_dp/vol(node1)*xnorm*q4q23
        wxq24 = 1.0_dp/vol(node1)*xnorm*q4q24
        wxt2  = 1.0_dp/vol(node1)*xnorm*q4t2

        wxq31 = 1.0_dp/vol(node1)*xnorm*q4q31
        wxq32 = 1.0_dp/vol(node1)*xnorm*q4q32
        wxq33 = 1.0_dp/vol(node1)*xnorm*q4q33
        wxq34 = 1.0_dp/vol(node1)*xnorm*q4q34
        wxt3  = 1.0_dp/vol(node1)*xnorm*q4t3

        wyd  = grady(4,node1)
        wy   = grady(4,node1)
        wyq11 = 1.0_dp/vol(node1)*ynorm*q4q11
        wyq12 = 1.0_dp/vol(node1)*ynorm*q4q12
        wyq13 = 1.0_dp/vol(node1)*ynorm*q4q13
        wyq14 = 1.0_dp/vol(node1)*ynorm*q4q14
        wyt1  = 1.0_dp/vol(node1)*ynorm*q4t1

        wyq21 = 1.0_dp/vol(node1)*ynorm*q4q21
        wyq22 = 1.0_dp/vol(node1)*ynorm*q4q22
        wyq23 = 1.0_dp/vol(node1)*ynorm*q4q23
        wyq24 = 1.0_dp/vol(node1)*ynorm*q4q24
        wyt2  = 1.0_dp/vol(node1)*ynorm*q4t2

        wyq31 = 1.0_dp/vol(node1)*ynorm*q4q31
        wyq32 = 1.0_dp/vol(node1)*ynorm*q4q32
        wyq33 = 1.0_dp/vol(node1)*ynorm*q4q33
        wyq34 = 1.0_dp/vol(node1)*ynorm*q4q34
        wyt3  = 1.0_dp/vol(node1)*ynorm*q4t3

!       wzd  = gradz(4,node1)
        wz   = gradz(4,node1)
        wzq11 = my_1/vol(node1)*znorm*q4q11
        wzq12 = my_1/vol(node1)*znorm*q4q12
        wzq13 = my_1/vol(node1)*znorm*q4q13
        wzq14 = my_1/vol(node1)*znorm*q4q14
        wzt1  = my_1/vol(node1)*znorm*q4t1

        wzq21 = my_1/vol(node1)*znorm*q4q21
        wzq22 = my_1/vol(node1)*znorm*q4q22
        wzq23 = my_1/vol(node1)*znorm*q4q23
        wzq24 = my_1/vol(node1)*znorm*q4q24
        wzt2  = my_1/vol(node1)*znorm*q4t2

        wzq31 = my_1/vol(node1)*znorm*q4q31
        wzq32 = my_1/vol(node1)*znorm*q4q32
        wzq33 = my_1/vol(node1)*znorm*q4q33
        wzq34 = my_1/vol(node1)*znorm*q4q34
        wzt3  = my_1/vol(node1)*znorm*q4t3


        if ( has_x_symmetry(symmetry(node1)) ) then

          uyd = my_0
          uy  = my_0
            uyq11 = my_0
            uyq12 = my_0
            uyq13 = my_0
            uyq14 = my_0
            uyt1  = my_0

            uyq21 = my_0
            uyq22 = my_0
            uyq23 = my_0
            uyq24 = my_0
            uyt2  = my_0

            uyq31 = my_0
            uyq32 = my_0
            uyq33 = my_0
            uyq34 = my_0
            uyt3  = my_0

          uzd = my_0
          uz  = my_0
            uzq11 = my_0
            uzq12 = my_0
            uzq13 = my_0
            uzq14 = my_0
            uzt1  = my_0

            uzq21 = my_0
            uzq22 = my_0
            uzq23 = my_0
            uzq24 = my_0
            uzt2  = my_0

            uzq31 = my_0
            uzq32 = my_0
            uzq33 = my_0
            uzq34 = my_0
            uzt3  = my_0

          vxd = my_0
          vx  = my_0
            vxq11 = my_0
            vxq12 = my_0
            vxq13 = my_0
            vxq14 = my_0
            vxt1  = my_0

            vxq21 = my_0
            vxq22 = my_0
            vxq23 = my_0
            vxq24 = my_0
            vxt2  = my_0

            vxq31 = my_0
            vxq32 = my_0
            vxq33 = my_0
            vxq34 = my_0
            vxt3  = my_0

          wxd = my_0
          wx  = my_0
            wxq11 = my_0
            wxq12 = my_0
            wxq13 = my_0
            wxq14 = my_0
            wxt1  = my_0

            wxq21 = my_0
            wxq22 = my_0
            wxq23 = my_0
            wxq24 = my_0
            wxt2  = my_0

            wxq31 = my_0
            wxq32 = my_0
            wxq33 = my_0
            wxq34 = my_0
            wxt3  = my_0

        endif

        if ( has_y_symmetry(symmetry(node1)) ) then

          vxd = my_0
          vx  = my_0
            vxq11 = my_0
            vxq12 = my_0
            vxq13 = my_0
            vxq14 = my_0
            vxt1  = my_0

            vxq21 = my_0
            vxq22 = my_0
            vxq23 = my_0
            vxq24 = my_0
            vxt2  = my_0

            vxq31 = my_0
            vxq32 = my_0
            vxq33 = my_0
            vxq34 = my_0
            vxt3  = my_0

          vzd = my_0
          vz  = my_0
            vzq11 = my_0
            vzq12 = my_0
            vzq13 = my_0
            vzq14 = my_0
            vzt1  = my_0

            vzq21 = my_0
            vzq22 = my_0
            vzq23 = my_0
            vzq24 = my_0
            vzt2  = my_0

            vzq31 = my_0
            vzq32 = my_0
            vzq33 = my_0
            vzq34 = my_0
            vzt3  = my_0

          uyd = my_0
          uy  = my_0
            uyq11 = my_0
            uyq12 = my_0
            uyq13 = my_0
            uyq14 = my_0
            uyt1  = my_0

            uyq21 = my_0
            uyq22 = my_0
            uyq23 = my_0
            uyq24 = my_0
            uyt2  = my_0

            uyq31 = my_0
            uyq32 = my_0
            uyq33 = my_0
            uyq34 = my_0
            uyt3  = my_0

          wyd = my_0
          wy  = my_0
            wyq11 = my_0
            wyq12 = my_0
            wyq13 = my_0
            wyq14 = my_0
            wyt1  = my_0

            wyq21 = my_0
            wyq22 = my_0
            wyq23 = my_0
            wyq24 = my_0
            wyt2  = my_0

            wyq31 = my_0
            wyq32 = my_0
            wyq33 = my_0
            wyq34 = my_0
            wyt3  = my_0

        endif

        if ( has_z_symmetry(symmetry(node1)) ) then

          wxd = my_0
          wx  = my_0
            wxq11 = my_0
            wxq12 = my_0
            wxq13 = my_0
            wxq14 = my_0
            wxt1  = my_0

            wxq21 = my_0
            wxq22 = my_0
            wxq23 = my_0
            wxq24 = my_0
            wxt2  = my_0

            wxq31 = my_0
            wxq32 = my_0
            wxq33 = my_0
            wxq34 = my_0
            wxt3  = my_0

          wyd = my_0
          wy  = my_0
            wyq11 = my_0
            wyq12 = my_0
            wyq13 = my_0
            wyq14 = my_0
            wyt1  = my_0

            wyq21 = my_0
            wyq22 = my_0
            wyq23 = my_0
            wyq24 = my_0
            wyt2  = my_0

            wyq31 = my_0
            wyq32 = my_0
            wyq33 = my_0
            wyq34 = my_0
            wyt3  = my_0

          uzd = my_0
          uz  = my_0
            uzq11 = my_0
            uzq12 = my_0
            uzq13 = my_0
            uzq14 = my_0
            uzt1  = my_0

            uzq21 = my_0
            uzq22 = my_0
            uzq23 = my_0
            uzq24 = my_0
            uzt2  = my_0

            uzq31 = my_0
            uzq32 = my_0
            uzq33 = my_0
            uzq34 = my_0
            uzt3  = my_0

          vzd = my_0
          vz  = my_0
            vzq11 = my_0
            vzq12 = my_0
            vzq13 = my_0
            vzq14 = my_0
            vzt1  = my_0

            vzq21 = my_0
            vzq22 = my_0
            vzq23 = my_0
            vzq24 = my_0
            vzt2  = my_0

            vzq31 = my_0
            vzq32 = my_0
            vzq33 = my_0
            vzq34 = my_0
            vzt3  = my_0

        endif

        S    = sqrt( (wyd-vzd)**2 + (uzd-wxd)**2 + (vxd-uyd)**2)

        if ( S <= 1.0e-8_dp) then

          S = 1.0e-8_dp
          Sq11 = 0.0_dp
          Sq12 = 0.0_dp
          Sq13 = 0.0_dp
          Sq14 = 0.0_dp
          St1  = 0.0_dp

          Sq21 = 0.0_dp
          Sq22 = 0.0_dp
          Sq23 = 0.0_dp
          Sq24 = 0.0_dp
          St2  = 0.0_dp

          Sq31 = 0.0_dp
          Sq32 = 0.0_dp
          Sq33 = 0.0_dp
          Sq34 = 0.0_dp
          St3  = 0.0_dp

        else

          Sq11 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq11-vzq11)                          &
                       + 2.0_dp*(uz-wx)*(uzq11-wxq11)                          &
                       + 2.0_dp*(vx-uy)*(vxq11-uyq11)) / S
          Sq12 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq12-vzq12)                          &
                       + 2.0_dp*(uz-wx)*(uzq12-wxq12)                          &
                       + 2.0_dp*(vx-uy)*(vxq12-uyq12)) / S
          Sq13 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq13-vzq13)                          &
                       + 2.0_dp*(uz-wx)*(uzq13-wxq13)                          &
                       + 2.0_dp*(vx-uy)*(vxq13-uyq13)) / S
          Sq14 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq14-vzq14)                          &
                       + 2.0_dp*(uz-wx)*(uzq14-wxq14)                          &
                       + 2.0_dp*(vx-uy)*(vxq14-uyq14)) / S
          St1  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt1 -vzt1 )                          &
                       + 2.0_dp*(uz-wx)*(uzt1 -wxt1 )                          &
                       + 2.0_dp*(vx-uy)*(vxt1 -uyt1 )) / S

          Sq21 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq21-vzq21)                          &
                       + 2.0_dp*(uz-wx)*(uzq21-wxq21)                          &
                       + 2.0_dp*(vx-uy)*(vxq21-uyq21)) / S
          Sq22 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq22-vzq22)                          &
                       + 2.0_dp*(uz-wx)*(uzq22-wxq22)                          &
                       + 2.0_dp*(vx-uy)*(vxq22-uyq22)) / S
          Sq23 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq23-vzq23)                          &
                       + 2.0_dp*(uz-wx)*(uzq23-wxq23)                          &
                       + 2.0_dp*(vx-uy)*(vxq23-uyq23)) / S
          Sq24 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq24-vzq24)                          &
                       + 2.0_dp*(uz-wx)*(uzq24-wxq24)                          &
                       + 2.0_dp*(vx-uy)*(vxq24-uyq24)) / S
          St2  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt2 -vzt2 )                          &
                       + 2.0_dp*(uz-wx)*(uzt2 -wxt2 )                          &
                       + 2.0_dp*(vx-uy)*(vxt2 -uyt2 )) / S

          Sq31 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq31-vzq31)                          &
                       + 2.0_dp*(uz-wx)*(uzq31-wxq31)                          &
                       + 2.0_dp*(vx-uy)*(vxq31-uyq31)) / S
          Sq32 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq32-vzq32)                          &
                       + 2.0_dp*(uz-wx)*(uzq32-wxq32)                          &
                       + 2.0_dp*(vx-uy)*(vxq32-uyq32)) / S
          Sq33 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq33-vzq33)                          &
                       + 2.0_dp*(uz-wx)*(uzq33-wxq33)                          &
                       + 2.0_dp*(vx-uy)*(vxq33-uyq33)) / S
          Sq34 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq34-vzq34)                          &
                       + 2.0_dp*(uz-wx)*(uzq34-wxq34)                          &
                       + 2.0_dp*(vx-uy)*(vxq34-uyq34)) / S
          St3  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt3 -vzt3 )                          &
                       + 2.0_dp*(uz-wx)*(uzt3 -wxt3 )                          &
                       + 2.0_dp*(vx-uy)*(vxt3 -uyt3 )) / S

        endif

      if ( dacles_mariani ) then
        sij_mag = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                          +2._dp*(ux**2+vy**2+wz**2))
          sij_magq11 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq11+vzq11)          &
                                        +  my_2*(uz+wx)*(uzq11+wxq11)          &
                                        +  my_2*(vx+uy)*(vxq11+uyq11)          &
                                               + my_2*(my_2*ux*uxq11           &
                                                     + my_2*vy*vyq11           &
                                                     + my_2*wz*wzq11) )
          sij_magq12 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq12+vzq12)          &
                                        +  my_2*(uz+wx)*(uzq12+wxq12)          &
                                        +  my_2*(vx+uy)*(vxq12+uyq12)          &
                                               + my_2*(my_2*ux*uxq12           &
                                                     + my_2*vy*vyq12           &
                                                     + my_2*wz*wzq12) )
          sij_magq13 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq13+vzq13)          &
                                        +  my_2*(uz+wx)*(uzq13+wxq13)          &
                                        +  my_2*(vx+uy)*(vxq13+uyq13)          &
                                               + my_2*(my_2*ux*uxq13           &
                                                     + my_2*vy*vyq13           &
                                                     + my_2*wz*wzq13) )
          sij_magq14 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq14+vzq14)          &
                                        +  my_2*(uz+wx)*(uzq14+wxq14)          &
                                        +  my_2*(vx+uy)*(vxq14+uyq14)          &
                                               + my_2*(my_2*ux*uxq14           &
                                                     + my_2*vy*vyq14           &
                                                     + my_2*wz*wzq14) )
          sij_magt1  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt1 +vzt1 )          &
                                        +  my_2*(uz+wx)*(uzt1 +wxt1 )          &
                                        +  my_2*(vx+uy)*(vxt1 +uyt1 )          &
                                               + my_2*(my_2*ux*uxt1            &
                                                     + my_2*vy*vyt1            &
                                                     + my_2*wz*wzt1 ) )

          sij_magq21 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq21+vzq21)          &
                                        +  my_2*(uz+wx)*(uzq21+wxq21)          &
                                        +  my_2*(vx+uy)*(vxq21+uyq21)          &
                                               + my_2*(my_2*ux*uxq21           &
                                                     + my_2*vy*vyq21           &
                                                     + my_2*wz*wzq21) )
          sij_magq22 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq22+vzq22)          &
                                        +  my_2*(uz+wx)*(uzq22+wxq22)          &
                                        +  my_2*(vx+uy)*(vxq22+uyq22)          &
                                               + my_2*(my_2*ux*uxq22           &
                                                     + my_2*vy*vyq22           &
                                                     + my_2*wz*wzq22) )
          sij_magq23 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq23+vzq23)          &
                                        +  my_2*(uz+wx)*(uzq23+wxq23)          &
                                        +  my_2*(vx+uy)*(vxq23+uyq23)          &
                                               + my_2*(my_2*ux*uxq23           &
                                                     + my_2*vy*vyq23           &
                                                     + my_2*wz*wzq23) )
          sij_magq24 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq24+vzq24)          &
                                        +  my_2*(uz+wx)*(uzq24+wxq24)          &
                                        +  my_2*(vx+uy)*(vxq24+uyq24)          &
                                               + my_2*(my_2*ux*uxq24           &
                                                     + my_2*vy*vyq24           &
                                                     + my_2*wz*wzq24) )
          sij_magt2  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt2 +vzt2 )          &
                                        +  my_2*(uz+wx)*(uzt2 +wxt2 )          &
                                        +  my_2*(vx+uy)*(vxt2 +uyt2 )          &
                                               + my_2*(my_2*ux*uxt2            &
                                                     + my_2*vy*vyt2            &
                                                     + my_2*wz*wzt2 ) )

          sij_magq31 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq31+vzq31)          &
                                        +  my_2*(uz+wx)*(uzq31+wxq31)          &
                                        +  my_2*(vx+uy)*(vxq31+uyq31)          &
                                               + my_2*(my_2*ux*uxq31           &
                                                     + my_2*vy*vyq31           &
                                                     + my_2*wz*wzq31) )
          sij_magq32 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq32+vzq32)          &
                                        +  my_2*(uz+wx)*(uzq32+wxq32)          &
                                        +  my_2*(vx+uy)*(vxq32+uyq32)          &
                                               + my_2*(my_2*ux*uxq32           &
                                                     + my_2*vy*vyq32           &
                                                     + my_2*wz*wzq32) )
          sij_magq33 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq33+vzq33)          &
                                        +  my_2*(uz+wx)*(uzq33+wxq33)          &
                                        +  my_2*(vx+uy)*(vxq33+uyq33)          &
                                               + my_2*(my_2*ux*uxq33           &
                                                     + my_2*vy*vyq33           &
                                                     + my_2*wz*wzq33) )
          sij_magq34 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq34+vzq34)          &
                                        +  my_2*(uz+wx)*(uzq34+wxq34)          &
                                        +  my_2*(vx+uy)*(vxq34+uyq34)          &
                                               + my_2*(my_2*ux*uxq34           &
                                                     + my_2*vy*vyq34           &
                                                     + my_2*wz*wzq34) )
          sij_magt3  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt3 +vzt3 )          &
                                        +  my_2*(uz+wx)*(uzt3 +wxt3 )          &
                                        +  my_2*(vx+uy)*(vxt3 +uyt3 )          &
                                               + my_2*(my_2*ux*uxt3            &
                                                     + my_2*vy*vyt3            &
                                                     + my_2*wz*wzt3 ) )
        if ( (sij_mag-s) < my_0 ) then

          s = s + 2._dp*min(my_0,sij_mag-s)

          sq11 = sq11 + my_2*(sij_magq11 - sq11)
          sq12 = sq12 + my_2*(sij_magq12 - sq12)
          sq13 = sq13 + my_2*(sij_magq13 - sq13)
          sq14 = sq14 + my_2*(sij_magq14 - sq14)
          st1  = st1  + my_2*(sij_magt1  - st1 )

          sq21 = sq21 + my_2*(sij_magq21 - sq21)
          sq22 = sq22 + my_2*(sij_magq22 - sq22)
          sq23 = sq23 + my_2*(sij_magq23 - sq23)
          sq24 = sq24 + my_2*(sij_magq24 - sq24)
          st2  = st2  + my_2*(sij_magt2  - st2 )

          sq31 = sq31 + my_2*(sij_magq31 - sq31)
          sq32 = sq32 + my_2*(sij_magq32 - sq32)
          sq33 = sq33 + my_2*(sij_magq33 - sq33)
          sq34 = sq34 + my_2*(sij_magq34 - sq34)
          st3  = st3  + my_2*(sij_magt3  - st3 )
        endif

      end if


!  Remember we are only linearizing vorticity this time

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

        if ( use_bp_model ) turb_abs = chi*rnu

        edwards_mod4 : if(.not. use_edwards_mod) then

          if ( use_bp_model ) then

            Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
            Swq11 = Sq11
            Swq12 = Sq12
            Swq13 = Sq13
            Swq14 = Sq14
            Swt1  = St1

            Swq21 = Sq21
            Swq22 = Sq22
            Swq23 = Sq23
            Swq24 = Sq24
            Swt2  = St2

            Swq31 = Sq31
            Swq32 = Sq32
            Swq33 = Sq33
            Swq34 = Sq34
            Swt3  = St3

            if ( Sw < 0.3_dp*S ) then
              s_bar = 1.0_dp/Re*turb_abs/bot*fv2
              term1 = 0.49_dp*S + 0.9_dp*s_bar
                term1q11 = 0.49_dp*Sq11
                term1q12 = 0.49_dp*Sq12
                term1q13 = 0.49_dp*Sq13
                term1q14 = 0.49_dp*Sq14
                term1t1  = 0.49_dp*St1

                term1q21 = 0.49_dp*Sq21
                term1q22 = 0.49_dp*Sq22
                term1q23 = 0.49_dp*Sq23
                term1q24 = 0.49_dp*Sq24
                term1t2  = 0.49_dp*St2

                term1q31 = 0.49_dp*Sq31
                term1q32 = 0.49_dp*Sq32
                term1q33 = 0.49_dp*Sq33
                term1q34 = 0.49_dp*Sq34
                term1t3  = 0.49_dp*St3

              term2 = -0.5_dp*S - s_bar
                term2q11 = -0.5_dp*Sq11
                term2q12 = -0.5_dp*Sq12
                term2q13 = -0.5_dp*Sq13
                term2q14 = -0.5_dp*Sq14
                term2t1  = -0.5_dp*St1

                term2q21 = -0.5_dp*Sq21
                term2q22 = -0.5_dp*Sq22
                term2q23 = -0.5_dp*Sq23
                term2q24 = -0.5_dp*Sq24
                term2t2  = -0.5_dp*St2

                term2q31 = -0.5_dp*Sq31
                term2q32 = -0.5_dp*Sq32
                term2q33 = -0.5_dp*Sq33
                term2q34 = -0.5_dp*Sq34
                term2t3  = -0.5_dp*St3

              Sw = S + S*term1/term2
                Swq11 = Sq11 + S*((term2*term1q11-term1*term2q11)/term2/term2) &
                      + term1/term2*Sq11
                Swq12 = Sq12 + S*((term2*term1q12-term1*term2q12)/term2/term2) &
                      + term1/term2*Sq12
                Swq13 = Sq13 + S*((term2*term1q13-term1*term2q13)/term2/term2) &
                      + term1/term2*Sq13
                Swq14 = Sq14 + S*((term2*term1q14-term1*term2q14)/term2/term2) &
                      + term1/term2*Sq14
                Swt1  = St1  + S*((term2*term1t1 -term1*term2t1 )/term2/term2) &
                      + term1/term2*St1

                Swq21 = Sq21 + S*((term2*term1q21-term1*term2q21)/term2/term2) &
                      + term1/term2*Sq21
                Swq22 = Sq22 + S*((term2*term1q22-term1*term2q22)/term2/term2) &
                      + term1/term2*Sq22
                Swq23 = Sq23 + S*((term2*term1q23-term1*term2q23)/term2/term2) &
                      + term1/term2*Sq23
                Swq24 = Sq24 + S*((term2*term1q24-term1*term2q24)/term2/term2) &
                      + term1/term2*Sq24
                Swt2  = St2  + S*((term2*term1t2 -term1*term2t2 )/term2/term2) &
                      + term1/term2*St2

                Swq31 = Sq31 + S*((term2*term1q31-term1*term2q31)/term2/term2) &
                      + term1/term2*Sq31
                Swq32 = Sq32 + S*((term2*term1q32-term1*term2q32)/term2/term2) &
                      + term1/term2*Sq32
                Swq33 = Sq33 + S*((term2*term1q33-term1*term2q33)/term2/term2) &
                      + term1/term2*Sq33
                Swq34 = Sq34 + S*((term2*term1q34-term1*term2q34)/term2/term2) &
                      + term1/term2*Sq34
                Swt3  = St3  + S*((term2*term1t3 -term1*term2t3 )/term2/term2) &
                      + term1/term2*St3
            endif

          else

            Sw   = S + 1.0_dp/Re*turb(1,node1)/bot*fv2
            Swq11 = Sq11
            Swq12 = Sq12
            Swq13 = Sq13
            Swq14 = Sq14
            Swt1  = St1

            Swq21 = Sq21
            Swq22 = Sq22
            Swq23 = Sq23
            Swq24 = Sq24
            Swt2  = St2

            Swq31 = Sq31
            Swq32 = Sq32
            Swq33 = Sq33
            Swq34 = Sq34
            Swt3  = St3

            if(Sw <= 0.00001_dp) then
              Sw = 0.00001_dp
              Swq11 = 0.0_dp
              Swq12 = 0.0_dp
              Swq13 = 0.0_dp
              Swq14 = 0.0_dp
              Swt1  = 0.0_dp

              Swq21 = 0.0_dp
              Swq22 = 0.0_dp
              Swq23 = 0.0_dp
              Swq24 = 0.0_dp
              Swt2  = 0.0_dp

              Swq31 = 0.0_dp
              Swq32 = 0.0_dp
              Swq33 = 0.0_dp
              Swq34 = 0.0_dp
              Swt3  = 0.0_dp
            endif

          endif

          RR   = 1.0_dp/Re*turb(1,node1)/bot/Sw
          RRq11 = -RR*Swq11/Sw
          RRq12 = -RR*Swq12/Sw
          RRq13 = -RR*Swq13/Sw
          RRq14 = -RR*Swq14/Sw

          RRq21 = -RR*Swq21/Sw
          RRq22 = -RR*Swq22/Sw
          RRq23 = -RR*Swq23/Sw
          RRq24 = -RR*Swq24/Sw

          RRq31 = -RR*Swq31/Sw
          RRq32 = -RR*Swq32/Sw
          RRq33 = -RR*Swq33/Sw
          RRq34 = -RR*Swq34/Sw

          if(RR > 10.0_dp) then
            RR = 10.0_dp
            RRq11 = 0.0_dp
            RRq12 = 0.0_dp
            RRq13 = 0.0_dp
            RRq14 = 0.0_dp

            RRq21 = 0.0_dp
            RRq22 = 0.0_dp
            RRq23 = 0.0_dp
            RRq24 = 0.0_dp

            RRq31 = 0.0_dp
            RRq32 = 0.0_dp
            RRq33 = 0.0_dp
            RRq34 = 0.0_dp
          endif

        else edwards_mod4

          sw = s*(1.0_dp/chi + fv1)
          swq11 = sq11*(1.0_dp/chi + fv1)
          swq12 = sq12*(1.0_dp/chi + fv1)
          swq13 = sq13*(1.0_dp/chi + fv1)
          swq14 = sq14*(1.0_dp/chi + fv1)
          swt1  = st1 *(1.0_dp/chi + fv1)

          swq21 = sq21*(1.0_dp/chi + fv1)
          swq22 = sq22*(1.0_dp/chi + fv1)
          swq23 = sq23*(1.0_dp/chi + fv1)
          swq24 = sq24*(1.0_dp/chi + fv1)
          swt2  = st2 *(1.0_dp/chi + fv1)

          swq31 = sq31*(1.0_dp/chi + fv1)
          swq32 = sq32*(1.0_dp/chi + fv1)
          swq33 = sq33*(1.0_dp/chi + fv1)
          swq34 = sq34*(1.0_dp/chi + fv1)
          swt3  = st3 *(1.0_dp/chi + fv1)

          distance = slen(node1)
          arg = vkar*vkar*distance*distance

          tanharg = 1.0_dp/re*turb(1,node1)/arg/sw
          tanhargq11 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq11
          tanhargq12 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq12
          tanhargq13 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq13
          tanhargq14 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq14

          tanhargq21 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq21
          tanhargq22 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq22
          tanhargq23 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq23
          tanhargq24 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq24

          tanhargq31 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq31
          tanhargq32 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq32
          tanhargq33 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq33
          tanhargq34 = -1.0_dp*1.0_dp/re*turb(1,node1)/arg/sw/sw * swq34

          if(tanharg < 0.0_dp)                                                 &
            write(*,*) 'Negative tanharg, fix limiting on cosh()'

          rr = tanh(tanharg) / tanh(1.0_dp)
          if(tanharg > 15.0_dp) then
            rrq11 = 0.0_dp
            rrq12 = 0.0_dp
            rrq13 = 0.0_dp
            rrq14 = 0.0_dp

            rrq21 = 0.0_dp
            rrq22 = 0.0_dp
            rrq23 = 0.0_dp
            rrq24 = 0.0_dp

            rrq31 = 0.0_dp
            rrq32 = 0.0_dp
            rrq33 = 0.0_dp
            rrq34 = 0.0_dp
          else
            rrq11=1.0_dp/tanh(1.0_dp)*tanhargq11                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq12=1.0_dp/tanh(1.0_dp)*tanhargq12                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq13=1.0_dp/tanh(1.0_dp)*tanhargq13                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq14=1.0_dp/tanh(1.0_dp)*tanhargq14                               &
                 /cosh(tanharg)/cosh(tanharg)

            rrq21=1.0_dp/tanh(1.0_dp)*tanhargq21                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq22=1.0_dp/tanh(1.0_dp)*tanhargq22                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq23=1.0_dp/tanh(1.0_dp)*tanhargq23                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq24=1.0_dp/tanh(1.0_dp)*tanhargq24                               &
                 /cosh(tanharg)/cosh(tanharg)

            rrq31=1.0_dp/tanh(1.0_dp)*tanhargq31                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq32=1.0_dp/tanh(1.0_dp)*tanhargq32                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq33=1.0_dp/tanh(1.0_dp)*tanhargq33                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq34=1.0_dp/tanh(1.0_dp)*tanhargq34                               &
                 /cosh(tanharg)/cosh(tanharg)
          endif

        endif edwards_mod4

        GG   = RR + cw2*(RR**6 - RR)
        GGq11 = RRq11 + cw2*(6.0_dp*RR**5*RRq11 - RRq11)
        GGq12 = RRq12 + cw2*(6.0_dp*RR**5*RRq12 - RRq12)
        GGq13 = RRq13 + cw2*(6.0_dp*RR**5*RRq13 - RRq13)
        GGq14 = RRq14 + cw2*(6.0_dp*RR**5*RRq14 - RRq14)

        GGq21 = RRq21 + cw2*(6.0_dp*RR**5*RRq21 - RRq21)
        GGq22 = RRq22 + cw2*(6.0_dp*RR**5*RRq22 - RRq22)
        GGq23 = RRq23 + cw2*(6.0_dp*RR**5*RRq23 - RRq23)
        GGq24 = RRq24 + cw2*(6.0_dp*RR**5*RRq24 - RRq24)

        GGq31 = RRq31 + cw2*(6.0_dp*RR**5*RRq31 - RRq31)
        GGq32 = RRq32 + cw2*(6.0_dp*RR**5*RRq32 - RRq32)
        GGq33 = RRq33 + cw2*(6.0_dp*RR**5*RRq33 - RRq33)
        GGq34 = RRq34 + cw2*(6.0_dp*RR**5*RRq34 - RRq34)

        onesix = 1.0_dp/6.0_dp
        fivesix = -5.0_dp/6.0_dp
!       fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
        term = (1.0_dp + cw3**6)/(GG**6 + cw3**6)
        bottom = (GG**6 + cw3**6)**2
        factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
        fwq11 = GGq11*(term**onesix - factor)
        fwq12 = GGq12*(term**onesix - factor)
        fwq13 = GGq13*(term**onesix - factor)
        fwq14 = GGq14*(term**onesix - factor)

        fwq21 = GGq21*(term**onesix - factor)
        fwq22 = GGq22*(term**onesix - factor)
        fwq23 = GGq23*(term**onesix - factor)
        fwq24 = GGq24*(term**onesix - factor)

        fwq31 = GGq31*(term**onesix - factor)
        fwq32 = GGq32*(term**onesix - factor)
        fwq33 = GGq33*(term**onesix - factor)
        fwq34 = GGq34*(term**onesix - factor)

        temp_arg = -ct4*chi*chi

        if ( use_bp_model .and. temp_arg < -700.0_dp ) temp_arg = -700.0_dp

        ft2  = ct3*exp(temp_arg)

!       Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,node1))
!       Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)  ! for use_bp_model

        if ( use_bp_model ) then

          Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb_abs)
          Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb_abs)
          Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb_abs)
          Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb_abs)
          Prodt1  = (cb1*(1.0_dp - ft2)*Swt1 *turb_abs)

          Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb_abs)
          Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb_abs)
          Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb_abs)
          Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb_abs)
          Prodt2  = (cb1*(1.0_dp - ft2)*Swt2 *turb_abs)

          Prodq31 = (cb1*(1.0_dp - ft2)*Swq31*turb_abs)
          Prodq32 = (cb1*(1.0_dp - ft2)*Swq32*turb_abs)
          Prodq33 = (cb1*(1.0_dp - ft2)*Swq33*turb_abs)
          Prodq34 = (cb1*(1.0_dp - ft2)*Swq34*turb_abs)
          Prodt3  = (cb1*(1.0_dp - ft2)*Swt3 *turb_abs)

        else

          Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb(1,node1))
          Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb(1,node1))
          Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb(1,node1))
          Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb(1,node1))
          Prodt1  = (cb1*(1.0_dp - ft2)*Swt1*turb(1,node1))

          Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb(1,node1))
          Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb(1,node1))
          Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb(1,node1))
          Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb(1,node1))
          Prodt2  = (cb1*(1.0_dp - ft2)*Swt2*turb(1,node1))

          Prodq31 = (cb1*(1.0_dp - ft2)*Swq31*turb(1,node1))
          Prodq32 = (cb1*(1.0_dp - ft2)*Swq32*turb(1,node1))
          Prodq33 = (cb1*(1.0_dp - ft2)*Swq33*turb(1,node1))
          Prodq34 = (cb1*(1.0_dp - ft2)*Swq34*turb(1,node1))
          Prodt3  = (cb1*(1.0_dp - ft2)*Swt3*turb(1,node1))

        endif

!       vkar2 = vkar*vkar
!       Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                              &
!            *(turb(1,node1)/slen(node1))**2
!       Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                              &
!             *(turb_abs/slen(node1))**2        ! for use_bp_model

        xmr = 1.0_dp/Re

        if ( use_bp_model ) then
          factor = (turb_abs/slen(node1))**2
        else
          factor = (turb(1,node1)/slen(node1))**2
        endif

        Destq11 = xmr*(cw1*fwq11)*factor
        Destq12 = xmr*(cw1*fwq12)*factor
        Destq13 = xmr*(cw1*fwq13)*factor
        Destq14 = xmr*(cw1*fwq14)*factor
        Destt1  = 0.0_dp
        Destq21 = xmr*(cw1*fwq21)*factor
        Destq22 = xmr*(cw1*fwq22)*factor
        Destq23 = xmr*(cw1*fwq23)*factor
        Destq24 = xmr*(cw1*fwq24)*factor
        Destt2  = 0.0_dp
        Destq31 = xmr*(cw1*fwq31)*factor
        Destq32 = xmr*(cw1*fwq32)*factor
        Destq33 = xmr*(cw1*fwq33)*factor
        Destq34 = xmr*(cw1*fwq34)*factor
        Destt3  = 0.0_dp

!       source = vol(node1)*(Prod - Dest)
        sourceq11 = vol(node1)*(Prodq11 - Destq11)
        sourceq12 = vol(node1)*(Prodq12 - Destq12)
        sourceq13 = vol(node1)*(Prodq13 - Destq13)
        sourceq14 = vol(node1)*(Prodq14 - Destq14)
        sourcet1  = vol(node1)*(Prodt1  - Destt1 )

        sourceq21 = vol(node1)*(Prodq21 - Destq21)
        sourceq22 = vol(node1)*(Prodq22 - Destq22)
        sourceq23 = vol(node1)*(Prodq23 - Destq23)
        sourceq24 = vol(node1)*(Prodq24 - Destq24)
        sourcet2  = vol(node1)*(Prodt2  - Destt2 )

        sourceq31 = vol(node1)*(Prodq31 - Destq31)
        sourceq32 = vol(node1)*(Prodq32 - Destq32)
        sourceq33 = vol(node1)*(Prodq33 - Destq33)
        sourceq34 = vol(node1)*(Prodq34 - Destq34)
        sourcet3  = vol(node1)*(Prodt3  - Destt3 )

        res1q11 = -sourceq11
        res1q12 = -sourceq12
        res1q13 = -sourceq13
        res1q14 = -sourceq14
        res1t1  = -sourcet1

        res1q21 = -sourceq21
        res1q22 = -sourceq22
        res1q23 = -sourceq23
        res1q24 = -sourceq24
        res1t2  = -sourcet2

        res1q31 = -sourceq31
        res1q32 = -sourceq32
        res1q33 = -sourceq33
        res1q34 = -sourceq34
        res1t3  = -sourcet3

        if ( fill_res ) then
          do i = 1, nfunctions
            rlam5(i) = coltag(5,node1)*rlam(5,node1,i)
            if(node1 <= nnodes0 .and. coltag(5,node1) > 0.1_dp) then
              res(1,node1,i) = res(1,node1,i) + res1q11*rlam5(i)
              res(2,node1,i) = res(2,node1,i) + res1q12*rlam5(i)
              res(3,node1,i) = res(3,node1,i) + res1q13*rlam5(i)
              res(4,node1,i) = res(4,node1,i) + res1q14*rlam5(i)
              res(5,node1,i) = res(5,node1,i) + res1t1 *rlam5(i)
            endif
          end do

          do i = 1, nfunctions
            rlam5(i) = coltag(5,node1)*rlam(5,node1,i)
            if(node2 <= nnodes0 .and. coltag(5,node1) > 0.1_dp) then
              res(1,node2,i) = res(1,node2,i) + res1q21*rlam5(i)
              res(2,node2,i) = res(2,node2,i) + res1q22*rlam5(i)
              res(3,node2,i) = res(3,node2,i) + res1q23*rlam5(i)
              res(4,node2,i) = res(4,node2,i) + res1q24*rlam5(i)
              res(5,node2,i) = res(5,node2,i) + res1t2 *rlam5(i)
            endif
          end do

          do i = 1, nfunctions
            rlam5(i) = coltag(5,node1)*rlam(5,node1,i)
            if(node3 <= nnodes0.and. coltag(5,node1) > 0.1_dp) then
              res(1,node3,i) = res(1,node3,i) + res1q31*rlam5(i)
              res(2,node3,i) = res(2,node3,i) + res1q32*rlam5(i)
              res(3,node3,i) = res(3,node3,i) + res1q33*rlam5(i)
              res(4,node3,i) = res(4,node3,i) + res1q34*rlam5(i)
              res(5,node3,i) = res(5,node3,i) + res1t3 *rlam5(i)
            endif
          end do
        endif

        contribs1 : if ( fill_a ) then

          local_node1_diag : if(node1 <= nnodes0) then
            idiag = iau(node1)
            A(5,1,idiag) = A(5,1,idiag) + res1q11
            A(5,2,idiag) = A(5,2,idiag) + res1q12
            A(5,3,idiag) = A(5,3,idiag) + res1q13
            A(5,4,idiag) = A(5,4,idiag) + res1q14
            A(5,5,idiag) = A(5,5,idiag) + res1t1
          endif local_node1_diag

!  Ok, let's locate the off-diagonal locations

          if(node2 <= nnodes0) then
            jstart = ia(node2)
            jend   = ia(node2+1)-1
            ioff1 = 0
            do j = jstart, jend
              jcol = ja(j)
              if ( jcol == node1 ) ioff1 = j
            end do
            if ( ioff1 == 0 ) then
              write(*,*) 'trouble locating ioff1'
              call lmpi_die
              stop
            endif

            A(5,1,ioff1) = A(5,1,ioff1) + res1q21
            A(5,2,ioff1) = A(5,2,ioff1) + res1q22
            A(5,3,ioff1) = A(5,3,ioff1) + res1q23
            A(5,4,ioff1) = A(5,4,ioff1) + res1q24
            A(5,5,ioff1) = A(5,5,ioff1) + res1t2
          end if

!  Now repeat the same process for node3

          if(node3 <= nnodes0) then
            jstart = ia(node3)
            jend   = ia(node3+1)-1
            ioff2 = 0
            do j = jstart, jend
              jcol = ja(j)
              if ( jcol == node1 ) ioff2 = j
            end do
            if ( ioff2 == 0 ) then
              write(*,*) 'trouble locating ioff2'
              call lmpi_die
              stop
            endif

            A(5,1,ioff2) = A(5,1,ioff2) + res1q31
            A(5,2,ioff2) = A(5,2,ioff2) + res1q32
            A(5,3,ioff2) = A(5,3,ioff2) + res1q33
            A(5,4,ioff2) = A(5,4,ioff2) + res1q34
            A(5,5,ioff2) = A(5,5,ioff2) + res1t3
          endif

        endif contribs1

      end if node1_of_face_used

!  Now let's do the linearization at node2

! If we are updating the residual at node2, then
! we will pick up a derivative w.r.t. node1.
! Keep in mind that in this part of the code,
! we are only taking into account linearization
! from the vorticity part of the source term; the
! other parts are already taken into account.


!       q2 = c68*u2 + c18*(u1 + u3)
        q2q11 = 0.0_dp
        q2q12 = c18
        q2q13 = 0.0_dp
        q2q14 = 0.0_dp
        q2t1  = 0.0_dp

        q2q21 = 0.0_dp
        q2q22 = c68
        q2q23 = 0.0_dp
        q2q24 = 0.0_dp
        q2t2  = 0.0_dp

        q2q31 = 0.0_dp
        q2q32 = c18
        q2q33 = 0.0_dp
        q2q34 = 0.0_dp
        q2t3  = 0.0_dp

!       q3 = c68*v2 + c18*(v1 + v3)
        q3q11 = 0.0_dp
        q3q12 = 0.0_dp
        q3q13 = c18
        q3q14 = 0.0_dp
        q3t1  = 0.0_dp

        q3q21 = 0.0_dp
        q3q22 = 0.0_dp
        q3q23 = c68
        q3q24 = 0.0_dp
        q3t2  = 0.0_dp

        q3q31 = 0.0_dp
        q3q32 = 0.0_dp
        q3q33 = c18
        q3q34 = 0.0_dp
        q3t3  = 0.0_dp

!       q4 = c68*w2 + c18*(w1 + w3)
        q4q11 = 0.0_dp
        q4q12 = 0.0_dp
        q4q13 = 0.0_dp
        q4q14 = c18
        q4t1  = 0.0_dp

        q4q21 = 0.0_dp
        q4q22 = 0.0_dp
        q4q23 = 0.0_dp
        q4q24 = c68
        q4t2  = 0.0_dp

        q4q31 = 0.0_dp
        q4q32 = 0.0_dp
        q4q33 = 0.0_dp
        q4q34 = c18
        q4t3  = 0.0_dp

! to not divide by slen on surface for -fpe0 NaNa
      node2_of_face_used : if ( coltag(5,node2) > 0.1_dp ) then

! dR2/d()1
        rnu  = 1.0_dp

        edwards_chi5 : if(.not. use_edwards_mod) then
          chi  = turb(1,node2)/rnu
          if ( use_bp_model .and. chi < 10.0_dp ) then
            chi = 0.05_dp * log ( 1.0_dp + exp( 20.0_dp*chi ) )
          endif
        else edwards_chi5
          chi  = (turb(1,node2)+1.e-12_dp)/rnu
        endif edwards_chi5

        if ( use_bp_model .and. chi < -5.0_dp ) then
          fv1 = 0.0_dp
        else
          fv1 = chi**3/(chi**3 + cv1**3)
        endif

        fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
        bot  = vkar*vkar*slen(node2)*slen(node2)

! Get derivatives of the vorticity source term.
! Since this only contributes to the off-diagonal,
! dont compute linearization w.r.t. node1. (we'll
! do that later)

!       uxd  = gradx(2,node2)
        ux   = gradx(2,node2)
        uxq11 = my_1/vol(node2)*xnorm*q2q11
        uxq12 = my_1/vol(node2)*xnorm*q2q12
        uxq13 = my_1/vol(node2)*xnorm*q2q13
        uxq14 = my_1/vol(node2)*xnorm*q2q14
        uxt1  = my_1/vol(node2)*xnorm*q2t1

        uxq21 = my_1/vol(node2)*xnorm*q2q21
        uxq22 = my_1/vol(node2)*xnorm*q2q22
        uxq23 = my_1/vol(node2)*xnorm*q2q23
        uxq24 = my_1/vol(node2)*xnorm*q2q24
        uxt2  = my_1/vol(node2)*xnorm*q2t2

        uxq31 = my_1/vol(node2)*xnorm*q2q31
        uxq32 = my_1/vol(node2)*xnorm*q2q32
        uxq33 = my_1/vol(node2)*xnorm*q2q33
        uxq34 = my_1/vol(node2)*xnorm*q2q34
        uxt3  = my_1/vol(node2)*xnorm*q2t3


        uyd  = grady(2,node2)
        uy   = grady(2,node2)
        uyq11 = 1.0_dp/vol(node2)*ynorm*q2q11
        uyq12 = 1.0_dp/vol(node2)*ynorm*q2q12
        uyq13 = 1.0_dp/vol(node2)*ynorm*q2q13
        uyq14 = 1.0_dp/vol(node2)*ynorm*q2q14
        uyt1  = 1.0_dp/vol(node2)*ynorm*q2t1

        uyq21 = 1.0_dp/vol(node2)*ynorm*q2q21
        uyq22 = 1.0_dp/vol(node2)*ynorm*q2q22
        uyq23 = 1.0_dp/vol(node2)*ynorm*q2q23
        uyq24 = 1.0_dp/vol(node2)*ynorm*q2q24
        uyt2  = 1.0_dp/vol(node2)*ynorm*q2t2

        uyq31 = 1.0_dp/vol(node2)*ynorm*q2q31
        uyq32 = 1.0_dp/vol(node2)*ynorm*q2q32
        uyq33 = 1.0_dp/vol(node2)*ynorm*q2q33
        uyq34 = 1.0_dp/vol(node2)*ynorm*q2q34
        uyt3  = 1.0_dp/vol(node2)*ynorm*q2t3

        uzd  = gradz(2,node2)
        uz   = gradz(2,node2)
        uzq11 = 1.0_dp/vol(node2)*znorm*q2q11
        uzq12 = 1.0_dp/vol(node2)*znorm*q2q12
        uzq13 = 1.0_dp/vol(node2)*znorm*q2q13
        uzq14 = 1.0_dp/vol(node2)*znorm*q2q14
        uzt1  = 1.0_dp/vol(node2)*znorm*q2t1

        uzq21 = 1.0_dp/vol(node2)*znorm*q2q21
        uzq22 = 1.0_dp/vol(node2)*znorm*q2q22
        uzq23 = 1.0_dp/vol(node2)*znorm*q2q23
        uzq24 = 1.0_dp/vol(node2)*znorm*q2q24
        uzt2  = 1.0_dp/vol(node2)*znorm*q2t2

        uzq31 = 1.0_dp/vol(node2)*znorm*q2q31
        uzq32 = 1.0_dp/vol(node2)*znorm*q2q32
        uzq33 = 1.0_dp/vol(node2)*znorm*q2q33
        uzq34 = 1.0_dp/vol(node2)*znorm*q2q34
        uzt3  = 1.0_dp/vol(node2)*znorm*q2t3

        vxd  = gradx(3,node2)
        vx   = gradx(3,node2)
        vxq11 = 1.0_dp/vol(node2)*xnorm*q3q11
        vxq12 = 1.0_dp/vol(node2)*xnorm*q3q12
        vxq13 = 1.0_dp/vol(node2)*xnorm*q3q13
        vxq14 = 1.0_dp/vol(node2)*xnorm*q3q14
        vxt1  = 1.0_dp/vol(node2)*xnorm*q3t1

        vxq21 = 1.0_dp/vol(node2)*xnorm*q3q21
        vxq22 = 1.0_dp/vol(node2)*xnorm*q3q22
        vxq23 = 1.0_dp/vol(node2)*xnorm*q3q23
        vxq24 = 1.0_dp/vol(node2)*xnorm*q3q24
        vxt2  = 1.0_dp/vol(node2)*xnorm*q3t2

        vxq31 = 1.0_dp/vol(node2)*xnorm*q3q31
        vxq32 = 1.0_dp/vol(node2)*xnorm*q3q32
        vxq33 = 1.0_dp/vol(node2)*xnorm*q3q33
        vxq34 = 1.0_dp/vol(node2)*xnorm*q3q34
        vxt3  = 1.0_dp/vol(node2)*xnorm*q3t3

!       vyd  = grady(3,node2)
        vy   = grady(3,node2)
        vyq11 = my_1/vol(node2)*ynorm*q3q11
        vyq12 = my_1/vol(node2)*ynorm*q3q12
        vyq13 = my_1/vol(node2)*ynorm*q3q13
        vyq14 = my_1/vol(node2)*ynorm*q3q14
        vyt1  = my_1/vol(node2)*ynorm*q3t1

        vyq21 = my_1/vol(node2)*ynorm*q3q21
        vyq22 = my_1/vol(node2)*ynorm*q3q22
        vyq23 = my_1/vol(node2)*ynorm*q3q23
        vyq24 = my_1/vol(node2)*ynorm*q3q24
        vyt2  = my_1/vol(node2)*ynorm*q3t2

        vyq31 = my_1/vol(node2)*ynorm*q3q31
        vyq32 = my_1/vol(node2)*ynorm*q3q32
        vyq33 = my_1/vol(node2)*ynorm*q3q33
        vyq34 = my_1/vol(node2)*ynorm*q3q34
        vyt3  = my_1/vol(node2)*ynorm*q3t3


        vzd  = gradz(3,node2)
        vz   = gradz(3,node2)
        vzq11 = 1.0_dp/vol(node2)*znorm*q3q11
        vzq12 = 1.0_dp/vol(node2)*znorm*q3q12
        vzq13 = 1.0_dp/vol(node2)*znorm*q3q13
        vzq14 = 1.0_dp/vol(node2)*znorm*q3q14
        vzt1  = 1.0_dp/vol(node2)*znorm*q3t1

        vzq21 = 1.0_dp/vol(node2)*znorm*q3q21
        vzq22 = 1.0_dp/vol(node2)*znorm*q3q22
        vzq23 = 1.0_dp/vol(node2)*znorm*q3q23
        vzq24 = 1.0_dp/vol(node2)*znorm*q3q24
        vzt2  = 1.0_dp/vol(node2)*znorm*q3t2

        vzq31 = 1.0_dp/vol(node2)*znorm*q3q31
        vzq32 = 1.0_dp/vol(node2)*znorm*q3q32
        vzq33 = 1.0_dp/vol(node2)*znorm*q3q33
        vzq34 = 1.0_dp/vol(node2)*znorm*q3q34
        vzt3  = 1.0_dp/vol(node2)*znorm*q3t3

        wxd  = gradx(4,node2)
        wx   = gradx(4,node2)
        wxq11 = 1.0_dp/vol(node2)*xnorm*q4q11
        wxq12 = 1.0_dp/vol(node2)*xnorm*q4q12
        wxq13 = 1.0_dp/vol(node2)*xnorm*q4q13
        wxq14 = 1.0_dp/vol(node2)*xnorm*q4q14
        wxt1  = 1.0_dp/vol(node2)*xnorm*q4t1

        wxq21 = 1.0_dp/vol(node2)*xnorm*q4q21
        wxq22 = 1.0_dp/vol(node2)*xnorm*q4q22
        wxq23 = 1.0_dp/vol(node2)*xnorm*q4q23
        wxq24 = 1.0_dp/vol(node2)*xnorm*q4q24
        wxt2  = 1.0_dp/vol(node2)*xnorm*q4t2

        wxq31 = 1.0_dp/vol(node2)*xnorm*q4q31
        wxq32 = 1.0_dp/vol(node2)*xnorm*q4q32
        wxq33 = 1.0_dp/vol(node2)*xnorm*q4q33
        wxq34 = 1.0_dp/vol(node2)*xnorm*q4q34
        wxt3  = 1.0_dp/vol(node2)*xnorm*q4t3

        wyd  = grady(4,node2)
        wy   = grady(4,node2)
        wyq11 = 1.0_dp/vol(node2)*ynorm*q4q11
        wyq12 = 1.0_dp/vol(node2)*ynorm*q4q12
        wyq13 = 1.0_dp/vol(node2)*ynorm*q4q13
        wyq14 = 1.0_dp/vol(node2)*ynorm*q4q14
        wyt1  = 1.0_dp/vol(node2)*ynorm*q4t1

        wyq21 = 1.0_dp/vol(node2)*ynorm*q4q21
        wyq22 = 1.0_dp/vol(node2)*ynorm*q4q22
        wyq23 = 1.0_dp/vol(node2)*ynorm*q4q23
        wyq24 = 1.0_dp/vol(node2)*ynorm*q4q24
        wyt2  = 1.0_dp/vol(node2)*ynorm*q4t2

        wyq31 = 1.0_dp/vol(node2)*ynorm*q4q31
        wyq32 = 1.0_dp/vol(node2)*ynorm*q4q32
        wyq33 = 1.0_dp/vol(node2)*ynorm*q4q33
        wyq34 = 1.0_dp/vol(node2)*ynorm*q4q34
        wyt3  = 1.0_dp/vol(node2)*ynorm*q4t3

!       wzd  = gradz(4,node2)
        wz   = gradz(4,node2)
        wzq11 = my_1/vol(node2)*znorm*q4q11
        wzq12 = my_1/vol(node2)*znorm*q4q12
        wzq13 = my_1/vol(node2)*znorm*q4q13
        wzq14 = my_1/vol(node2)*znorm*q4q14
        wzt1  = my_1/vol(node2)*znorm*q4t1

        wzq21 = my_1/vol(node2)*znorm*q4q21
        wzq22 = my_1/vol(node2)*znorm*q4q22
        wzq23 = my_1/vol(node2)*znorm*q4q23
        wzq24 = my_1/vol(node2)*znorm*q4q24
        wzt2  = my_1/vol(node2)*znorm*q4t2

        wzq31 = my_1/vol(node2)*znorm*q4q31
        wzq32 = my_1/vol(node2)*znorm*q4q32
        wzq33 = my_1/vol(node2)*znorm*q4q33
        wzq34 = my_1/vol(node2)*znorm*q4q34
        wzt3  = my_1/vol(node2)*znorm*q4t3


        if ( has_x_symmetry(symmetry(node2)) ) then

          uyd = my_0
          uy  = my_0
            uyq11 = my_0
            uyq12 = my_0
            uyq13 = my_0
            uyq14 = my_0
            uyt1  = my_0

            uyq21 = my_0
            uyq22 = my_0
            uyq23 = my_0
            uyq24 = my_0
            uyt2  = my_0

            uyq31 = my_0
            uyq32 = my_0
            uyq33 = my_0
            uyq34 = my_0
            uyt3  = my_0

          uzd = my_0
          uz  = my_0
            uzq11 = my_0
            uzq12 = my_0
            uzq13 = my_0
            uzq14 = my_0
            uzt1  = my_0

            uzq21 = my_0
            uzq22 = my_0
            uzq23 = my_0
            uzq24 = my_0
            uzt2  = my_0

            uzq31 = my_0
            uzq32 = my_0
            uzq33 = my_0
            uzq34 = my_0
            uzt3  = my_0

          vxd = my_0
          vx  = my_0
            vxq11 = my_0
            vxq12 = my_0
            vxq13 = my_0
            vxq14 = my_0
            vxt1  = my_0

            vxq21 = my_0
            vxq22 = my_0
            vxq23 = my_0
            vxq24 = my_0
            vxt2  = my_0

            vxq31 = my_0
            vxq32 = my_0
            vxq33 = my_0
            vxq34 = my_0
            vxt3  = my_0

          wxd = my_0
          wx  = my_0
            wxq11 = my_0
            wxq12 = my_0
            wxq13 = my_0
            wxq14 = my_0
            wxt1  = my_0

            wxq21 = my_0
            wxq22 = my_0
            wxq23 = my_0
            wxq24 = my_0
            wxt2  = my_0

            wxq31 = my_0
            wxq32 = my_0
            wxq33 = my_0
            wxq34 = my_0
            wxt3  = my_0

        endif

        if ( has_y_symmetry(symmetry(node2)) ) then

          vxd = my_0
          vx  = my_0
            vxq11 = my_0
            vxq12 = my_0
            vxq13 = my_0
            vxq14 = my_0
            vxt1  = my_0

            vxq21 = my_0
            vxq22 = my_0
            vxq23 = my_0
            vxq24 = my_0
            vxt2  = my_0

            vxq31 = my_0
            vxq32 = my_0
            vxq33 = my_0
            vxq34 = my_0
            vxt3  = my_0

          vzd = my_0
          vz  = my_0
            vzq11 = my_0
            vzq12 = my_0
            vzq13 = my_0
            vzq14 = my_0
            vzt1  = my_0

            vzq21 = my_0
            vzq22 = my_0
            vzq23 = my_0
            vzq24 = my_0
            vzt2  = my_0

            vzq31 = my_0
            vzq32 = my_0
            vzq33 = my_0
            vzq34 = my_0
            vzt3  = my_0

          uyd = my_0
          uy  = my_0
            uyq11 = my_0
            uyq12 = my_0
            uyq13 = my_0
            uyq14 = my_0
            uyt1  = my_0

            uyq21 = my_0
            uyq22 = my_0
            uyq23 = my_0
            uyq24 = my_0
            uyt2  = my_0

            uyq31 = my_0
            uyq32 = my_0
            uyq33 = my_0
            uyq34 = my_0
            uyt3  = my_0

          wyd = my_0
          wy  = my_0
            wyq11 = my_0
            wyq12 = my_0
            wyq13 = my_0
            wyq14 = my_0
            wyt1  = my_0

            wyq21 = my_0
            wyq22 = my_0
            wyq23 = my_0
            wyq24 = my_0
            wyt2  = my_0

            wyq31 = my_0
            wyq32 = my_0
            wyq33 = my_0
            wyq34 = my_0
            wyt3  = my_0

        endif

        if ( has_z_symmetry(symmetry(node2)) ) then

          wxd = my_0
          wx  = my_0
            wxq11 = my_0
            wxq12 = my_0
            wxq13 = my_0
            wxq14 = my_0
            wxt1  = my_0

            wxq21 = my_0
            wxq22 = my_0
            wxq23 = my_0
            wxq24 = my_0
            wxt2  = my_0

            wxq31 = my_0
            wxq32 = my_0
            wxq33 = my_0
            wxq34 = my_0
            wxt3  = my_0

          wyd = my_0
          wy  = my_0
            wyq11 = my_0
            wyq12 = my_0
            wyq13 = my_0
            wyq14 = my_0
            wyt1  = my_0

            wyq21 = my_0
            wyq22 = my_0
            wyq23 = my_0
            wyq24 = my_0
            wyt2  = my_0

            wyq31 = my_0
            wyq32 = my_0
            wyq33 = my_0
            wyq34 = my_0
            wyt3  = my_0

          uzd = my_0
          uz  = my_0
            uzq11 = my_0
            uzq12 = my_0
            uzq13 = my_0
            uzq14 = my_0
            uzt1  = my_0

            uzq21 = my_0
            uzq22 = my_0
            uzq23 = my_0
            uzq24 = my_0
            uzt2  = my_0

            uzq31 = my_0
            uzq32 = my_0
            uzq33 = my_0
            uzq34 = my_0
            uzt3  = my_0

          vzd = my_0
          vz  = my_0
            vzq11 = my_0
            vzq12 = my_0
            vzq13 = my_0
            vzq14 = my_0
            vzt1  = my_0

            vzq21 = my_0
            vzq22 = my_0
            vzq23 = my_0
            vzq24 = my_0
            vzt2  = my_0

            vzq31 = my_0
            vzq32 = my_0
            vzq33 = my_0
            vzq34 = my_0
            vzt3  = my_0

        endif

        S    = sqrt( (wyd-vzd)**2 + (uzd-wxd)**2 + (vxd-uyd)**2)

        if ( S <= 1.0e-8_dp) then

          S = 1.0e-8_dp
          Sq11 = 0.0_dp
          Sq12 = 0.0_dp
          Sq13 = 0.0_dp
          Sq14 = 0.0_dp
          St1  = 0.0_dp

          Sq21 = 0.0_dp
          Sq22 = 0.0_dp
          Sq23 = 0.0_dp
          Sq24 = 0.0_dp
          St2  = 0.0_dp

          Sq31 = 0.0_dp
          Sq32 = 0.0_dp
          Sq33 = 0.0_dp
          Sq34 = 0.0_dp
          St3  = 0.0_dp

        else

          Sq11 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq11-vzq11)                          &
                       + 2.0_dp*(uz-wx)*(uzq11-wxq11)                          &
                       + 2.0_dp*(vx-uy)*(vxq11-uyq11)) / S
          Sq12 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq12-vzq12)                          &
                       + 2.0_dp*(uz-wx)*(uzq12-wxq12)                          &
                       + 2.0_dp*(vx-uy)*(vxq12-uyq12)) / S
          Sq13 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq13-vzq13)                          &
                       + 2.0_dp*(uz-wx)*(uzq13-wxq13)                          &
                       + 2.0_dp*(vx-uy)*(vxq13-uyq13)) / S
          Sq14 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq14-vzq14)                          &
                       + 2.0_dp*(uz-wx)*(uzq14-wxq14)                          &
                       + 2.0_dp*(vx-uy)*(vxq14-uyq14)) / S
          St1  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt1 -vzt1 )                          &
                       + 2.0_dp*(uz-wx)*(uzt1 -wxt1 )                          &
                       + 2.0_dp*(vx-uy)*(vxt1 -uyt1 )) / S

          Sq21 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq21-vzq21)                          &
                       + 2.0_dp*(uz-wx)*(uzq21-wxq21)                          &
                       + 2.0_dp*(vx-uy)*(vxq21-uyq21)) / S
          Sq22 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq22-vzq22)                          &
                       + 2.0_dp*(uz-wx)*(uzq22-wxq22)                          &
                       + 2.0_dp*(vx-uy)*(vxq22-uyq22)) / S
          Sq23 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq23-vzq23)                          &
                       + 2.0_dp*(uz-wx)*(uzq23-wxq23)                          &
                       + 2.0_dp*(vx-uy)*(vxq23-uyq23)) / S
          Sq24 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq24-vzq24)                          &
                       + 2.0_dp*(uz-wx)*(uzq24-wxq24)                          &
                       + 2.0_dp*(vx-uy)*(vxq24-uyq24)) / S
          St2  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt2 -vzt2 )                          &
                       + 2.0_dp*(uz-wx)*(uzt2 -wxt2 )                          &
                       + 2.0_dp*(vx-uy)*(vxt2 -uyt2 )) / S

          Sq31 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq31-vzq31)                          &
                       + 2.0_dp*(uz-wx)*(uzq31-wxq31)                          &
                       + 2.0_dp*(vx-uy)*(vxq31-uyq31)) / S
          Sq32 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq32-vzq32)                          &
                       + 2.0_dp*(uz-wx)*(uzq32-wxq32)                          &
                       + 2.0_dp*(vx-uy)*(vxq32-uyq32)) / S
          Sq33 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq33-vzq33)                          &
                       + 2.0_dp*(uz-wx)*(uzq33-wxq33)                          &
                       + 2.0_dp*(vx-uy)*(vxq33-uyq33)) / S
          Sq34 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq34-vzq34)                          &
                       + 2.0_dp*(uz-wx)*(uzq34-wxq34)                          &
                       + 2.0_dp*(vx-uy)*(vxq34-uyq34)) / S
          St3  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt3 -vzt3 )                          &
                       + 2.0_dp*(uz-wx)*(uzt3 -wxt3 )                          &
                       + 2.0_dp*(vx-uy)*(vxt3 -uyt3 )) / S

        endif

      if ( dacles_mariani ) then
        sij_mag = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                          +2._dp*(ux**2+vy**2+wz**2))
          sij_magq11 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq11+vzq11)          &
                                        +  my_2*(uz+wx)*(uzq11+wxq11)          &
                                        +  my_2*(vx+uy)*(vxq11+uyq11)          &
                                               + my_2*(my_2*ux*uxq11           &
                                                     + my_2*vy*vyq11           &
                                                     + my_2*wz*wzq11) )
          sij_magq12 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq12+vzq12)          &
                                        +  my_2*(uz+wx)*(uzq12+wxq12)          &
                                        +  my_2*(vx+uy)*(vxq12+uyq12)          &
                                               + my_2*(my_2*ux*uxq12           &
                                                     + my_2*vy*vyq12           &
                                                     + my_2*wz*wzq12) )
          sij_magq13 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq13+vzq13)          &
                                        +  my_2*(uz+wx)*(uzq13+wxq13)          &
                                        +  my_2*(vx+uy)*(vxq13+uyq13)          &
                                               + my_2*(my_2*ux*uxq13           &
                                                     + my_2*vy*vyq13           &
                                                     + my_2*wz*wzq13) )
          sij_magq14 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq14+vzq14)          &
                                        +  my_2*(uz+wx)*(uzq14+wxq14)          &
                                        +  my_2*(vx+uy)*(vxq14+uyq14)          &
                                               + my_2*(my_2*ux*uxq14           &
                                                     + my_2*vy*vyq14           &
                                                     + my_2*wz*wzq14) )
          sij_magt1  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt1 +vzt1 )          &
                                        +  my_2*(uz+wx)*(uzt1 +wxt1 )          &
                                        +  my_2*(vx+uy)*(vxt1 +uyt1 )          &
                                               + my_2*(my_2*ux*uxt1            &
                                                     + my_2*vy*vyt1            &
                                                     + my_2*wz*wzt1 ) )

          sij_magq21 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq21+vzq21)          &
                                        +  my_2*(uz+wx)*(uzq21+wxq21)          &
                                        +  my_2*(vx+uy)*(vxq21+uyq21)          &
                                               + my_2*(my_2*ux*uxq21           &
                                                     + my_2*vy*vyq21           &
                                                     + my_2*wz*wzq21) )
          sij_magq22 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq22+vzq22)          &
                                        +  my_2*(uz+wx)*(uzq22+wxq22)          &
                                        +  my_2*(vx+uy)*(vxq22+uyq22)          &
                                               + my_2*(my_2*ux*uxq22           &
                                                     + my_2*vy*vyq22           &
                                                     + my_2*wz*wzq22) )
          sij_magq23 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq23+vzq23)          &
                                        +  my_2*(uz+wx)*(uzq23+wxq23)          &
                                        +  my_2*(vx+uy)*(vxq23+uyq23)          &
                                               + my_2*(my_2*ux*uxq23           &
                                                     + my_2*vy*vyq23           &
                                                     + my_2*wz*wzq23) )
          sij_magq24 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq24+vzq24)          &
                                        +  my_2*(uz+wx)*(uzq24+wxq24)          &
                                        +  my_2*(vx+uy)*(vxq24+uyq24)          &
                                               + my_2*(my_2*ux*uxq24           &
                                                     + my_2*vy*vyq24           &
                                                     + my_2*wz*wzq24) )
          sij_magt2  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt2 +vzt2 )          &
                                        +  my_2*(uz+wx)*(uzt2 +wxt2 )          &
                                        +  my_2*(vx+uy)*(vxt2 +uyt2 )          &
                                               + my_2*(my_2*ux*uxt2            &
                                                     + my_2*vy*vyt2            &
                                                     + my_2*wz*wzt2 ) )

          sij_magq31 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq31+vzq31)          &
                                        +  my_2*(uz+wx)*(uzq31+wxq31)          &
                                        +  my_2*(vx+uy)*(vxq31+uyq31)          &
                                               + my_2*(my_2*ux*uxq31           &
                                                     + my_2*vy*vyq31           &
                                                     + my_2*wz*wzq31) )
          sij_magq32 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq32+vzq32)          &
                                        +  my_2*(uz+wx)*(uzq32+wxq32)          &
                                        +  my_2*(vx+uy)*(vxq32+uyq32)          &
                                               + my_2*(my_2*ux*uxq32           &
                                                     + my_2*vy*vyq32           &
                                                     + my_2*wz*wzq32) )
          sij_magq33 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq33+vzq33)          &
                                        +  my_2*(uz+wx)*(uzq33+wxq33)          &
                                        +  my_2*(vx+uy)*(vxq33+uyq33)          &
                                               + my_2*(my_2*ux*uxq33           &
                                                     + my_2*vy*vyq33           &
                                                     + my_2*wz*wzq33) )
          sij_magq34 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq34+vzq34)          &
                                        +  my_2*(uz+wx)*(uzq34+wxq34)          &
                                        +  my_2*(vx+uy)*(vxq34+uyq34)          &
                                               + my_2*(my_2*ux*uxq34           &
                                                     + my_2*vy*vyq34           &
                                                     + my_2*wz*wzq34) )
          sij_magt3  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt3 +vzt3 )          &
                                        +  my_2*(uz+wx)*(uzt3 +wxt3 )          &
                                        +  my_2*(vx+uy)*(vxt3 +uyt3 )          &
                                               + my_2*(my_2*ux*uxt3            &
                                                     + my_2*vy*vyt3            &
                                                     + my_2*wz*wzt3 ) )
        if ( (sij_mag-s) < my_0 ) then

          s = s + 2._dp*min(my_0,sij_mag-s)

          sq11 = sq11 + my_2*(sij_magq11 - sq11)
          sq12 = sq12 + my_2*(sij_magq12 - sq12)
          sq13 = sq13 + my_2*(sij_magq13 - sq13)
          sq14 = sq14 + my_2*(sij_magq14 - sq14)
          st1  = st1  + my_2*(sij_magt1  - st1 )

          sq21 = sq21 + my_2*(sij_magq21 - sq21)
          sq22 = sq22 + my_2*(sij_magq22 - sq22)
          sq23 = sq23 + my_2*(sij_magq23 - sq23)
          sq24 = sq24 + my_2*(sij_magq24 - sq24)
          st2  = st2  + my_2*(sij_magt2  - st2 )

          sq31 = sq31 + my_2*(sij_magq31 - sq31)
          sq32 = sq32 + my_2*(sij_magq32 - sq32)
          sq33 = sq33 + my_2*(sij_magq33 - sq33)
          sq34 = sq34 + my_2*(sij_magq34 - sq34)
          st3  = st3  + my_2*(sij_magt3  - st3 )
        endif

      end if


!  Remember we are only linearizing vorticity this time

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

        if ( use_bp_model ) turb_abs = chi*rnu

        edwards_mod5 : if(.not. use_edwards_mod) then

          if ( use_bp_model ) then

            Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
            Swq11 = Sq11
            Swq12 = Sq12
            Swq13 = Sq13
            Swq14 = Sq14
            Swt1  = St1

            Swq21 = Sq21
            Swq22 = Sq22
            Swq23 = Sq23
            Swq24 = Sq24
            Swt2  = St2

            Swq31 = Sq31
            Swq32 = Sq32
            Swq33 = Sq33
            Swq34 = Sq34
            Swt3  = St3

            if ( Sw < 0.3_dp*S ) then
              s_bar = 1.0_dp/Re*turb_abs/bot*fv2
              term1 = 0.49_dp*S + 0.9_dp*s_bar
                term1q11 = 0.49_dp*Sq11
                term1q12 = 0.49_dp*Sq12
                term1q13 = 0.49_dp*Sq13
                term1q14 = 0.49_dp*Sq14
                term1t1  = 0.49_dp*St1

                term1q21 = 0.49_dp*Sq21
                term1q22 = 0.49_dp*Sq22
                term1q23 = 0.49_dp*Sq23
                term1q24 = 0.49_dp*Sq24
                term1t2  = 0.49_dp*St2

                term1q31 = 0.49_dp*Sq31
                term1q32 = 0.49_dp*Sq32
                term1q33 = 0.49_dp*Sq33
                term1q34 = 0.49_dp*Sq34
                term1t3  = 0.49_dp*St3

              term2 = -0.5_dp*S - s_bar
                term2q11 = -0.5_dp*Sq11
                term2q12 = -0.5_dp*Sq12
                term2q13 = -0.5_dp*Sq13
                term2q14 = -0.5_dp*Sq14
                term2t1  = -0.5_dp*St1

                term2q21 = -0.5_dp*Sq21
                term2q22 = -0.5_dp*Sq22
                term2q23 = -0.5_dp*Sq23
                term2q24 = -0.5_dp*Sq24
                term2t2  = -0.5_dp*St2

                term2q31 = -0.5_dp*Sq31
                term2q32 = -0.5_dp*Sq32
                term2q33 = -0.5_dp*Sq33
                term2q34 = -0.5_dp*Sq34
                term2t3  = -0.5_dp*St3

              Sw = S + S*term1/term2
                Swq11 = Sq11 + S*((term2*term1q11-term1*term2q11)/term2/term2) &
                      + term1/term2*Sq11
                Swq12 = Sq12 + S*((term2*term1q12-term1*term2q12)/term2/term2) &
                      + term1/term2*Sq12
                Swq13 = Sq13 + S*((term2*term1q13-term1*term2q13)/term2/term2) &
                      + term1/term2*Sq13
                Swq14 = Sq14 + S*((term2*term1q14-term1*term2q14)/term2/term2) &
                      + term1/term2*Sq14
                Swt1  = St1  + S*((term2*term1t1 -term1*term2t1 )/term2/term2) &
                      + term1/term2*St1

                Swq21 = Sq21 + S*((term2*term1q21-term1*term2q21)/term2/term2) &
                      + term1/term2*Sq21
                Swq22 = Sq22 + S*((term2*term1q22-term1*term2q22)/term2/term2) &
                      + term1/term2*Sq22
                Swq23 = Sq23 + S*((term2*term1q23-term1*term2q23)/term2/term2) &
                      + term1/term2*Sq23
                Swq24 = Sq24 + S*((term2*term1q24-term1*term2q24)/term2/term2) &
                      + term1/term2*Sq24
                Swt2  = St2  + S*((term2*term1t2 -term1*term2t2 )/term2/term2) &
                      + term1/term2*St2

                Swq31 = Sq31 + S*((term2*term1q31-term1*term2q31)/term2/term2) &
                      + term1/term2*Sq31
                Swq32 = Sq32 + S*((term2*term1q32-term1*term2q32)/term2/term2) &
                      + term1/term2*Sq32
                Swq33 = Sq33 + S*((term2*term1q33-term1*term2q33)/term2/term2) &
                      + term1/term2*Sq33
                Swq34 = Sq34 + S*((term2*term1q34-term1*term2q34)/term2/term2) &
                      + term1/term2*Sq34
                Swt3  = St3  + S*((term2*term1t3 -term1*term2t3 )/term2/term2) &
                      + term1/term2*St3
            endif

          else

            Sw   = S + 1.0_dp/Re*turb(1,node2)/bot*fv2
            Swq11 = Sq11
            Swq12 = Sq12
            Swq13 = Sq13
            Swq14 = Sq14
            Swt1  = St1

            Swq21 = Sq21
            Swq22 = Sq22
            Swq23 = Sq23
            Swq24 = Sq24
            Swt2  = St2

            Swq31 = Sq31
            Swq32 = Sq32
            Swq33 = Sq33
            Swq34 = Sq34
            Swt3  = St3

            if(Sw <= 0.00001_dp) then
              Sw = 0.00001_dp
              Swq11 = 0.0_dp
              Swq12 = 0.0_dp
              Swq13 = 0.0_dp
              Swq14 = 0.0_dp
              Swt1  = 0.0_dp

              Swq21 = 0.0_dp
              Swq22 = 0.0_dp
              Swq23 = 0.0_dp
              Swq24 = 0.0_dp
              Swt2  = 0.0_dp

              Swq31 = 0.0_dp
              Swq32 = 0.0_dp
              Swq33 = 0.0_dp
              Swq34 = 0.0_dp
              Swt3  = 0.0_dp
            endif

          endif

          RR   = 1.0_dp/Re*turb(1,node2)/bot/Sw
          RRq11 = -RR*Swq11/Sw
          RRq12 = -RR*Swq12/Sw
          RRq13 = -RR*Swq13/Sw
          RRq14 = -RR*Swq14/Sw

          RRq21 = -RR*Swq21/Sw
          RRq22 = -RR*Swq22/Sw
          RRq23 = -RR*Swq23/Sw
          RRq24 = -RR*Swq24/Sw

          RRq31 = -RR*Swq31/Sw
          RRq32 = -RR*Swq32/Sw
          RRq33 = -RR*Swq33/Sw
          RRq34 = -RR*Swq34/Sw

          if(RR > 10.0_dp) then
            RR = 10.0_dp
            RRq11 = 0.0_dp
            RRq12 = 0.0_dp
            RRq13 = 0.0_dp
            RRq14 = 0.0_dp

            RRq21 = 0.0_dp
            RRq22 = 0.0_dp
            RRq23 = 0.0_dp
            RRq24 = 0.0_dp

            RRq31 = 0.0_dp
            RRq32 = 0.0_dp
            RRq33 = 0.0_dp
            RRq34 = 0.0_dp
          endif

        else edwards_mod5

          sw = s*(1.0_dp/chi + fv1)
          swq11 = sq11*(1.0_dp/chi + fv1)
          swq12 = sq12*(1.0_dp/chi + fv1)
          swq13 = sq13*(1.0_dp/chi + fv1)
          swq14 = sq14*(1.0_dp/chi + fv1)
          swt1  = st1 *(1.0_dp/chi + fv1)

          swq21 = sq21*(1.0_dp/chi + fv1)
          swq22 = sq22*(1.0_dp/chi + fv1)
          swq23 = sq23*(1.0_dp/chi + fv1)
          swq24 = sq24*(1.0_dp/chi + fv1)
          swt2  = st2 *(1.0_dp/chi + fv1)

          swq31 = sq31*(1.0_dp/chi + fv1)
          swq32 = sq32*(1.0_dp/chi + fv1)
          swq33 = sq33*(1.0_dp/chi + fv1)
          swq34 = sq34*(1.0_dp/chi + fv1)
          swt3  = st3 *(1.0_dp/chi + fv1)

          distance = slen(node2)
          arg = vkar*vkar*distance*distance

          tanharg = 1.0_dp/re*turb(1,node2)/arg/sw
          tanhargq11 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq11
          tanhargq12 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq12
          tanhargq13 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq13
          tanhargq14 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq14

          tanhargq21 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq21
          tanhargq22 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq22
          tanhargq23 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq23
          tanhargq24 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq24

          tanhargq31 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq31
          tanhargq32 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq32
          tanhargq33 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq33
          tanhargq34 = -1.0_dp*1.0_dp/re*turb(1,node2)/arg/sw/sw * swq34

          if(tanharg < 0.0_dp)                                                 &
            write(*,*) 'Negative tanharg, fix limiting on cosh()'

          rr = tanh(tanharg) / tanh(1.0_dp)
          if(tanharg > 15.0_dp) then
            rrq11 = 0.0_dp
            rrq12 = 0.0_dp
            rrq13 = 0.0_dp
            rrq14 = 0.0_dp

            rrq21 = 0.0_dp
            rrq22 = 0.0_dp
            rrq23 = 0.0_dp
            rrq24 = 0.0_dp

            rrq31 = 0.0_dp
            rrq32 = 0.0_dp
            rrq33 = 0.0_dp
            rrq34 = 0.0_dp
          else
            rrq11=1.0_dp/tanh(1.0_dp)*tanhargq11                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq12=1.0_dp/tanh(1.0_dp)*tanhargq12                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq13=1.0_dp/tanh(1.0_dp)*tanhargq13                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq14=1.0_dp/tanh(1.0_dp)*tanhargq14                               &
                 /cosh(tanharg)/cosh(tanharg)

            rrq21=1.0_dp/tanh(1.0_dp)*tanhargq21                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq22=1.0_dp/tanh(1.0_dp)*tanhargq22                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq23=1.0_dp/tanh(1.0_dp)*tanhargq23                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq24=1.0_dp/tanh(1.0_dp)*tanhargq24                               &
                 /cosh(tanharg)/cosh(tanharg)

            rrq31=1.0_dp/tanh(1.0_dp)*tanhargq31                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq32=1.0_dp/tanh(1.0_dp)*tanhargq32                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq33=1.0_dp/tanh(1.0_dp)*tanhargq33                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq34=1.0_dp/tanh(1.0_dp)*tanhargq34                               &
                 /cosh(tanharg)/cosh(tanharg)
          endif

        endif edwards_mod5

        GG   = RR + cw2*(RR**6 - RR)
        GGq11 = RRq11 + cw2*(6.0_dp*RR**5*RRq11 - RRq11)
        GGq12 = RRq12 + cw2*(6.0_dp*RR**5*RRq12 - RRq12)
        GGq13 = RRq13 + cw2*(6.0_dp*RR**5*RRq13 - RRq13)
        GGq14 = RRq14 + cw2*(6.0_dp*RR**5*RRq14 - RRq14)

        GGq21 = RRq21 + cw2*(6.0_dp*RR**5*RRq21 - RRq21)
        GGq22 = RRq22 + cw2*(6.0_dp*RR**5*RRq22 - RRq22)
        GGq23 = RRq23 + cw2*(6.0_dp*RR**5*RRq23 - RRq23)
        GGq24 = RRq24 + cw2*(6.0_dp*RR**5*RRq24 - RRq24)

        GGq31 = RRq31 + cw2*(6.0_dp*RR**5*RRq31 - RRq31)
        GGq32 = RRq32 + cw2*(6.0_dp*RR**5*RRq32 - RRq32)
        GGq33 = RRq33 + cw2*(6.0_dp*RR**5*RRq33 - RRq33)
        GGq34 = RRq34 + cw2*(6.0_dp*RR**5*RRq34 - RRq34)

        onesix = 1.0_dp/6.0_dp
        fivesix = -5.0_dp/6.0_dp
!       fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
        term = (1.0_dp + cw3**6)/(GG**6 + cw3**6)
        bottom = (GG**6 + cw3**6)**2
        factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
        fwq11 = GGq11*(term**onesix - factor)
        fwq12 = GGq12*(term**onesix - factor)
        fwq13 = GGq13*(term**onesix - factor)
        fwq14 = GGq14*(term**onesix - factor)

        fwq21 = GGq21*(term**onesix - factor)
        fwq22 = GGq22*(term**onesix - factor)
        fwq23 = GGq23*(term**onesix - factor)
        fwq24 = GGq24*(term**onesix - factor)

        fwq31 = GGq31*(term**onesix - factor)
        fwq32 = GGq32*(term**onesix - factor)
        fwq33 = GGq33*(term**onesix - factor)
        fwq34 = GGq34*(term**onesix - factor)

        temp_arg = -ct4*chi*chi

        if ( use_bp_model .and. temp_arg < -700.0_dp ) temp_arg = -700.0_dp

        ft2  = ct3*exp(temp_arg)

!       Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,node2))
!       Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)  ! for use_bp_model

        if ( use_bp_model ) then

          Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb_abs)
          Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb_abs)
          Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb_abs)
          Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb_abs)
          Prodt1  = (cb1*(1.0_dp - ft2)*Swt1 *turb_abs)

          Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb_abs)
          Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb_abs)
          Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb_abs)
          Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb_abs)
          Prodt2  = (cb1*(1.0_dp - ft2)*Swt2 *turb_abs)

          Prodq31 = (cb1*(1.0_dp - ft2)*Swq31*turb_abs)
          Prodq32 = (cb1*(1.0_dp - ft2)*Swq32*turb_abs)
          Prodq33 = (cb1*(1.0_dp - ft2)*Swq33*turb_abs)
          Prodq34 = (cb1*(1.0_dp - ft2)*Swq34*turb_abs)
          Prodt3  = (cb1*(1.0_dp - ft2)*Swt3 *turb_abs)

        else

          Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb(1,node2))
          Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb(1,node2))
          Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb(1,node2))
          Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb(1,node2))
          Prodt1  = (cb1*(1.0_dp - ft2)*Swt1*turb(1,node2))

          Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb(1,node2))
          Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb(1,node2))
          Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb(1,node2))
          Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb(1,node2))
          Prodt2  = (cb1*(1.0_dp - ft2)*Swt2*turb(1,node2))

          Prodq31 = (cb1*(1.0_dp - ft2)*Swq31*turb(1,node2))
          Prodq32 = (cb1*(1.0_dp - ft2)*Swq32*turb(1,node2))
          Prodq33 = (cb1*(1.0_dp - ft2)*Swq33*turb(1,node2))
          Prodq34 = (cb1*(1.0_dp - ft2)*Swq34*turb(1,node2))
          Prodt3  = (cb1*(1.0_dp - ft2)*Swt3*turb(1,node2))

        endif

!       vkar2 = vkar*vkar
!       Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                              &
!             *(turb(1,node2)/slen(node2))**2
!       Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                              &
!             *(turb_abs/slen(node1))**2        ! for use_bp_model

        xmr = 1.0_dp/Re

        if ( use_bp_model ) then
          factor = (turb_abs/slen(node2))**2
        else
          factor = (turb(1,node2)/slen(node2))**2
        endif

        Destq11 = xmr*(cw1*fwq11)*factor
        Destq12 = xmr*(cw1*fwq12)*factor
        Destq13 = xmr*(cw1*fwq13)*factor
        Destq14 = xmr*(cw1*fwq14)*factor
        Destt1  = 0.0_dp
        Destq21 = xmr*(cw1*fwq21)*factor
        Destq22 = xmr*(cw1*fwq22)*factor
        Destq23 = xmr*(cw1*fwq23)*factor
        Destq24 = xmr*(cw1*fwq24)*factor
        Destt2  = 0.0_dp
        Destq31 = xmr*(cw1*fwq31)*factor
        Destq32 = xmr*(cw1*fwq32)*factor
        Destq33 = xmr*(cw1*fwq33)*factor
        Destq34 = xmr*(cw1*fwq34)*factor
        Destt3  = 0.0_dp

!       source = vol(node2)*(Prod - Dest)
        sourceq11 = vol(node2)*(Prodq11 - Destq11)
        sourceq12 = vol(node2)*(Prodq12 - Destq12)
        sourceq13 = vol(node2)*(Prodq13 - Destq13)
        sourceq14 = vol(node2)*(Prodq14 - Destq14)
        sourcet1  = vol(node2)*(Prodt1  - Destt1 )

        sourceq21 = vol(node2)*(Prodq21 - Destq21)
        sourceq22 = vol(node2)*(Prodq22 - Destq22)
        sourceq23 = vol(node2)*(Prodq23 - Destq23)
        sourceq24 = vol(node2)*(Prodq24 - Destq24)
        sourcet2  = vol(node2)*(Prodt2  - Destt2 )

        sourceq31 = vol(node2)*(Prodq31 - Destq31)
        sourceq32 = vol(node2)*(Prodq32 - Destq32)
        sourceq33 = vol(node2)*(Prodq33 - Destq33)
        sourceq34 = vol(node2)*(Prodq34 - Destq34)
        sourcet3  = vol(node2)*(Prodt3  - Destt3 )

        res2q11 = -sourceq11
        res2q12 = -sourceq12
        res2q13 = -sourceq13
        res2q14 = -sourceq14
        res2t1  = -sourcet1

        res2q21 = -sourceq21
        res2q22 = -sourceq22
        res2q23 = -sourceq23
        res2q24 = -sourceq24
        res2t2  = -sourcet2

        res2q31 = -sourceq31
        res2q32 = -sourceq32
        res2q33 = -sourceq33
        res2q34 = -sourceq34
        res2t3  = -sourcet3

        if ( fill_res ) then
          do i = 1, nfunctions
            rlam5(i) = coltag(5,node2)*rlam(5,node2,i)
            if(node2 <= nnodes0 .and. coltag(5,node2) > 0.1_dp) then
              res(1,node2,i) = res(1,node2,i) + res2q21*rlam5(i)
              res(2,node2,i) = res(2,node2,i) + res2q22*rlam5(i)
              res(3,node2,i) = res(3,node2,i) + res2q23*rlam5(i)
              res(4,node2,i) = res(4,node2,i) + res2q24*rlam5(i)
              res(5,node2,i) = res(5,node2,i) + res2t2 *rlam5(i)
            endif
          end do

          do i = 1, nfunctions
            rlam5(i) = coltag(5,node2)*rlam(5,node2,i)
            if(node1 <= nnodes0.and. coltag(5,node2) > 0.1_dp) then
              res(1,node1,i) = res(1,node1,i) + res2q11*rlam5(i)
              res(2,node1,i) = res(2,node1,i) + res2q12*rlam5(i)
              res(3,node1,i) = res(3,node1,i) + res2q13*rlam5(i)
              res(4,node1,i) = res(4,node1,i) + res2q14*rlam5(i)
              res(5,node1,i) = res(5,node1,i) + res2t1 *rlam5(i)
            endif
          end do

          do i = 1, nfunctions
            rlam5(i) = coltag(5,node2)*rlam(5,node2,i)
            if(node3 <= nnodes0.and. coltag(5,node2) > 0.1_dp) then
              res(1,node3,i) = res(1,node3,i) + res2q31*rlam5(i)
              res(2,node3,i) = res(2,node3,i) + res2q32*rlam5(i)
              res(3,node3,i) = res(3,node3,i) + res2q33*rlam5(i)
              res(4,node3,i) = res(4,node3,i) + res2q34*rlam5(i)
              res(5,node3,i) = res(5,node3,i) + res2t3 *rlam5(i)
            endif
          end do
        endif

        contribs2 : if ( fill_a ) then

          local_node2_diag : if(node2 <= nnodes0) then
            idiag = iau(node2)
            A(5,1,idiag) = A(5,1,idiag) + res2q21
            A(5,2,idiag) = A(5,2,idiag) + res2q22
            A(5,3,idiag) = A(5,3,idiag) + res2q23
            A(5,4,idiag) = A(5,4,idiag) + res2q24
            A(5,5,idiag) = A(5,5,idiag) + res2t2
          endif local_node2_diag

!  Ok, let's locate the off-diagonal locations

          if(node1 <= nnodes0) then
            jstart = ia(node1)
            jend   = ia(node1+1)-1
            ioff1 = 0
            do j = jstart, jend
              jcol = ja(j)
              if ( jcol == node2 ) ioff1 = j
            end do
            if ( ioff1 == 0 ) then
              write(*,*) 'trouble locating ioff1'
              call lmpi_die
              stop
            endif

            A(5,1,ioff1) = A(5,1,ioff1) + res2q11
            A(5,2,ioff1) = A(5,2,ioff1) + res2q12
            A(5,3,ioff1) = A(5,3,ioff1) + res2q13
            A(5,4,ioff1) = A(5,4,ioff1) + res2q14
            A(5,5,ioff1) = A(5,5,ioff1) + res2t1
          end if

!  Now repeat the same process for node3

          if(node3 <= nnodes0) then
            jstart = ia(node3)
            jend   = ia(node3+1)-1
            ioff2 = 0
            do j = jstart, jend
              jcol = ja(j)
              if ( jcol == node2 ) ioff2 = j
            end do
            if ( ioff2 == 0 ) then
              write(*,*) 'trouble locating ioff2'
              call lmpi_die
              stop
            endif

            A(5,1,ioff2) = A(5,1,ioff2) + res2q31
            A(5,2,ioff2) = A(5,2,ioff2) + res2q32
            A(5,3,ioff2) = A(5,3,ioff2) + res2q33
            A(5,4,ioff2) = A(5,4,ioff2) + res2q34
            A(5,5,ioff2) = A(5,5,ioff2) + res2t3
          endif

        endif contribs2

      end if node2_of_face_used

!  Now let's do the linearization at node3

! If we are updating the residual at node3, then
! we will pick up a derivative w.r.t. node1.
! Keep in mind that in this part of the code,
! we are only taking into account linearization
! from the vorticity part of the source term; the
! other parts are already taken into account.


!       q2 = c68*u3 + c18*(u1 + u2)
        q2q11 = 0.0_dp
        q2q12 = c18
        q2q13 = 0.0_dp
        q2q14 = 0.0_dp
        q2t1  = 0.0_dp

        q2q21 = 0.0_dp
        q2q22 = c18
        q2q23 = 0.0_dp
        q2q24 = 0.0_dp
        q2t2  = 0.0_dp

        q2q31 = 0.0_dp
        q2q32 = c68
        q2q33 = 0.0_dp
        q2q34 = 0.0_dp
        q2t3  = 0.0_dp

!       q3 = c68*v3 + c18*(v1 + v2)
        q3q11 = 0.0_dp
        q3q12 = 0.0_dp
        q3q13 = c18
        q3q14 = 0.0_dp
        q3t1  = 0.0_dp

        q3q21 = 0.0_dp
        q3q22 = 0.0_dp
        q3q23 = c18
        q3q24 = 0.0_dp
        q3t2  = 0.0_dp

        q3q31 = 0.0_dp
        q3q32 = 0.0_dp
        q3q33 = c68
        q3q34 = 0.0_dp
        q3t3  = 0.0_dp

!       q4 = c68*w3 + c18*(w1 + w2)
        q4q11 = 0.0_dp
        q4q12 = 0.0_dp
        q4q13 = 0.0_dp
        q4q14 = c18
        q4t1  = 0.0_dp

        q4q21 = 0.0_dp
        q4q22 = 0.0_dp
        q4q23 = 0.0_dp
        q4q24 = c18
        q4t2  = 0.0_dp

        q4q31 = 0.0_dp
        q4q32 = 0.0_dp
        q4q33 = 0.0_dp
        q4q34 = c68
        q4t3  = 0.0_dp

! to not divide by slen on surface for -fpe0 NaNa
      node3_of_edge_used : if ( coltag(5,node3) > 0.1_dp ) then

! dR3/d()1
        rnu  = 1.0_dp

        edwards_chi6 : if(.not. use_edwards_mod) then
          chi  = turb(1,node3)/rnu
          if ( use_bp_model .and. chi < 10.0_dp ) then
            chi = 0.05_dp * log ( 1.0_dp + exp( 20.0_dp*chi ) )
          endif
        else edwards_chi6
          chi  = (turb(1,node3)+1.e-12_dp)/rnu
        endif edwards_chi6

        if ( use_bp_model .and. chi < -5.0_dp ) then
          fv1 = 0.0_dp
        else
          fv1 = chi**3/(chi**3 + cv1**3)
        endif

        fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
        bot  = vkar*vkar*slen(node3)*slen(node3)

! Get derivatives of the vorticity source term.
! Since this only contributes to the off-diagonal,
! dont compute linearization w.r.t. node1. (we'll
! do that later)

!       uxd  = gradx(2,node3)
        ux   = gradx(2,node3)
        uxq11 = my_1/vol(node3)*xnorm*q2q11
        uxq12 = my_1/vol(node3)*xnorm*q2q12
        uxq13 = my_1/vol(node3)*xnorm*q2q13
        uxq14 = my_1/vol(node3)*xnorm*q2q14
        uxt1  = my_1/vol(node3)*xnorm*q2t1

        uxq21 = my_1/vol(node3)*xnorm*q2q21
        uxq22 = my_1/vol(node3)*xnorm*q2q22
        uxq23 = my_1/vol(node3)*xnorm*q2q23
        uxq24 = my_1/vol(node3)*xnorm*q2q24
        uxt2  = my_1/vol(node3)*xnorm*q2t2

        uxq31 = my_1/vol(node3)*xnorm*q2q31
        uxq32 = my_1/vol(node3)*xnorm*q2q32
        uxq33 = my_1/vol(node3)*xnorm*q2q33
        uxq34 = my_1/vol(node3)*xnorm*q2q34
        uxt3  = my_1/vol(node3)*xnorm*q2t3

        uyd  = grady(2,node3)
        uy   = grady(2,node3)
        uyq11 = 1.0_dp/vol(node3)*ynorm*q2q11
        uyq12 = 1.0_dp/vol(node3)*ynorm*q2q12
        uyq13 = 1.0_dp/vol(node3)*ynorm*q2q13
        uyq14 = 1.0_dp/vol(node3)*ynorm*q2q14
        uyt1  = 1.0_dp/vol(node3)*ynorm*q2t1

        uyq21 = 1.0_dp/vol(node3)*ynorm*q2q21
        uyq22 = 1.0_dp/vol(node3)*ynorm*q2q22
        uyq23 = 1.0_dp/vol(node3)*ynorm*q2q23
        uyq24 = 1.0_dp/vol(node3)*ynorm*q2q24
        uyt2  = 1.0_dp/vol(node3)*ynorm*q2t2

        uyq31 = 1.0_dp/vol(node3)*ynorm*q2q31
        uyq32 = 1.0_dp/vol(node3)*ynorm*q2q32
        uyq33 = 1.0_dp/vol(node3)*ynorm*q2q33
        uyq34 = 1.0_dp/vol(node3)*ynorm*q2q34
        uyt3  = 1.0_dp/vol(node3)*ynorm*q2t3

        uzd  = gradz(2,node3)
        uz   = gradz(2,node3)
        uzq11 = 1.0_dp/vol(node3)*znorm*q2q11
        uzq12 = 1.0_dp/vol(node3)*znorm*q2q12
        uzq13 = 1.0_dp/vol(node3)*znorm*q2q13
        uzq14 = 1.0_dp/vol(node3)*znorm*q2q14
        uzt1  = 1.0_dp/vol(node3)*znorm*q2t1

        uzq21 = 1.0_dp/vol(node3)*znorm*q2q21
        uzq22 = 1.0_dp/vol(node3)*znorm*q2q22
        uzq23 = 1.0_dp/vol(node3)*znorm*q2q23
        uzq24 = 1.0_dp/vol(node3)*znorm*q2q24
        uzt2  = 1.0_dp/vol(node3)*znorm*q2t2

        uzq31 = 1.0_dp/vol(node3)*znorm*q2q31
        uzq32 = 1.0_dp/vol(node3)*znorm*q2q32
        uzq33 = 1.0_dp/vol(node3)*znorm*q2q33
        uzq34 = 1.0_dp/vol(node3)*znorm*q2q34
        uzt3  = 1.0_dp/vol(node3)*znorm*q2t3

        vxd  = gradx(3,node3)
        vx   = gradx(3,node3)
        vxq11 = 1.0_dp/vol(node3)*xnorm*q3q11
        vxq12 = 1.0_dp/vol(node3)*xnorm*q3q12
        vxq13 = 1.0_dp/vol(node3)*xnorm*q3q13
        vxq14 = 1.0_dp/vol(node3)*xnorm*q3q14
        vxt1  = 1.0_dp/vol(node3)*xnorm*q3t1

        vxq21 = 1.0_dp/vol(node3)*xnorm*q3q21
        vxq22 = 1.0_dp/vol(node3)*xnorm*q3q22
        vxq23 = 1.0_dp/vol(node3)*xnorm*q3q23
        vxq24 = 1.0_dp/vol(node3)*xnorm*q3q24
        vxt2  = 1.0_dp/vol(node3)*xnorm*q3t2

        vxq31 = 1.0_dp/vol(node3)*xnorm*q3q31
        vxq32 = 1.0_dp/vol(node3)*xnorm*q3q32
        vxq33 = 1.0_dp/vol(node3)*xnorm*q3q33
        vxq34 = 1.0_dp/vol(node3)*xnorm*q3q34
        vxt3  = 1.0_dp/vol(node3)*xnorm*q3t3

!       vyd  = grady(3,node3)
        vy   = grady(3,node3)
        vyq11 = my_1/vol(node3)*ynorm*q3q11
        vyq12 = my_1/vol(node3)*ynorm*q3q12
        vyq13 = my_1/vol(node3)*ynorm*q3q13
        vyq14 = my_1/vol(node3)*ynorm*q3q14
        vyt1  = my_1/vol(node3)*ynorm*q3t1

        vyq21 = my_1/vol(node3)*ynorm*q3q21
        vyq22 = my_1/vol(node3)*ynorm*q3q22
        vyq23 = my_1/vol(node3)*ynorm*q3q23
        vyq24 = my_1/vol(node3)*ynorm*q3q24
        vyt2  = my_1/vol(node3)*ynorm*q3t2

        vyq31 = my_1/vol(node3)*ynorm*q3q31
        vyq32 = my_1/vol(node3)*ynorm*q3q32
        vyq33 = my_1/vol(node3)*ynorm*q3q33
        vyq34 = my_1/vol(node3)*ynorm*q3q34
        vyt3  = my_1/vol(node3)*ynorm*q3t3


        vzd  = gradz(3,node3)
        vz   = gradz(3,node3)
        vzq11 = 1.0_dp/vol(node3)*znorm*q3q11
        vzq12 = 1.0_dp/vol(node3)*znorm*q3q12
        vzq13 = 1.0_dp/vol(node3)*znorm*q3q13
        vzq14 = 1.0_dp/vol(node3)*znorm*q3q14
        vzt1  = 1.0_dp/vol(node3)*znorm*q3t1

        vzq21 = 1.0_dp/vol(node3)*znorm*q3q21
        vzq22 = 1.0_dp/vol(node3)*znorm*q3q22
        vzq23 = 1.0_dp/vol(node3)*znorm*q3q23
        vzq24 = 1.0_dp/vol(node3)*znorm*q3q24
        vzt2  = 1.0_dp/vol(node3)*znorm*q3t2

        vzq31 = 1.0_dp/vol(node3)*znorm*q3q31
        vzq32 = 1.0_dp/vol(node3)*znorm*q3q32
        vzq33 = 1.0_dp/vol(node3)*znorm*q3q33
        vzq34 = 1.0_dp/vol(node3)*znorm*q3q34
        vzt3  = 1.0_dp/vol(node3)*znorm*q3t3

        wxd  = gradx(4,node3)
        wx   = gradx(4,node3)
        wxq11 = 1.0_dp/vol(node3)*xnorm*q4q11
        wxq12 = 1.0_dp/vol(node3)*xnorm*q4q12
        wxq13 = 1.0_dp/vol(node3)*xnorm*q4q13
        wxq14 = 1.0_dp/vol(node3)*xnorm*q4q14
        wxt1  = 1.0_dp/vol(node3)*xnorm*q4t1

        wxq21 = 1.0_dp/vol(node3)*xnorm*q4q21
        wxq22 = 1.0_dp/vol(node3)*xnorm*q4q22
        wxq23 = 1.0_dp/vol(node3)*xnorm*q4q23
        wxq24 = 1.0_dp/vol(node3)*xnorm*q4q24
        wxt2  = 1.0_dp/vol(node3)*xnorm*q4t2

        wxq31 = 1.0_dp/vol(node3)*xnorm*q4q31
        wxq32 = 1.0_dp/vol(node3)*xnorm*q4q32
        wxq33 = 1.0_dp/vol(node3)*xnorm*q4q33
        wxq34 = 1.0_dp/vol(node3)*xnorm*q4q34
        wxt3  = 1.0_dp/vol(node3)*xnorm*q4t3

        wyd  = grady(4,node3)
        wy   = grady(4,node3)
        wyq11 = 1.0_dp/vol(node3)*ynorm*q4q11
        wyq12 = 1.0_dp/vol(node3)*ynorm*q4q12
        wyq13 = 1.0_dp/vol(node3)*ynorm*q4q13
        wyq14 = 1.0_dp/vol(node3)*ynorm*q4q14
        wyt1  = 1.0_dp/vol(node3)*ynorm*q4t1

        wyq21 = 1.0_dp/vol(node3)*ynorm*q4q21
        wyq22 = 1.0_dp/vol(node3)*ynorm*q4q22
        wyq23 = 1.0_dp/vol(node3)*ynorm*q4q23
        wyq24 = 1.0_dp/vol(node3)*ynorm*q4q24
        wyt2  = 1.0_dp/vol(node3)*ynorm*q4t2

        wyq31 = 1.0_dp/vol(node3)*ynorm*q4q31
        wyq32 = 1.0_dp/vol(node3)*ynorm*q4q32
        wyq33 = 1.0_dp/vol(node3)*ynorm*q4q33
        wyq34 = 1.0_dp/vol(node3)*ynorm*q4q34
        wyt3  = 1.0_dp/vol(node3)*ynorm*q4t3

!       wzd  = gradz(4,node3)
        wz   = gradz(4,node3)
        wzq11 = my_1/vol(node3)*znorm*q4q11
        wzq12 = my_1/vol(node3)*znorm*q4q12
        wzq13 = my_1/vol(node3)*znorm*q4q13
        wzq14 = my_1/vol(node3)*znorm*q4q14
        wzt1  = my_1/vol(node3)*znorm*q4t1

        wzq21 = my_1/vol(node3)*znorm*q4q21
        wzq22 = my_1/vol(node3)*znorm*q4q22
        wzq23 = my_1/vol(node3)*znorm*q4q23
        wzq24 = my_1/vol(node3)*znorm*q4q24
        wzt2  = my_1/vol(node3)*znorm*q4t2

        wzq31 = my_1/vol(node3)*znorm*q4q31
        wzq32 = my_1/vol(node3)*znorm*q4q32
        wzq33 = my_1/vol(node3)*znorm*q4q33
        wzq34 = my_1/vol(node3)*znorm*q4q34
        wzt3  = my_1/vol(node3)*znorm*q4t3


        if ( has_x_symmetry(symmetry(node3)) ) then

          uyd = my_0
          uy  = my_0
            uyq11 = my_0
            uyq12 = my_0
            uyq13 = my_0
            uyq14 = my_0
            uyt1  = my_0

            uyq21 = my_0
            uyq22 = my_0
            uyq23 = my_0
            uyq24 = my_0
            uyt2  = my_0

            uyq31 = my_0
            uyq32 = my_0
            uyq33 = my_0
            uyq34 = my_0
            uyt3  = my_0

          uzd = my_0
          uz  = my_0
            uzq11 = my_0
            uzq12 = my_0
            uzq13 = my_0
            uzq14 = my_0
            uzt1  = my_0

            uzq21 = my_0
            uzq22 = my_0
            uzq23 = my_0
            uzq24 = my_0
            uzt2  = my_0

            uzq31 = my_0
            uzq32 = my_0
            uzq33 = my_0
            uzq34 = my_0
            uzt3  = my_0

          vxd = my_0
          vx  = my_0
            vxq11 = my_0
            vxq12 = my_0
            vxq13 = my_0
            vxq14 = my_0
            vxt1  = my_0

            vxq21 = my_0
            vxq22 = my_0
            vxq23 = my_0
            vxq24 = my_0
            vxt2  = my_0

            vxq31 = my_0
            vxq32 = my_0
            vxq33 = my_0
            vxq34 = my_0
            vxt3  = my_0

          wxd = my_0
          wx  = my_0
            wxq11 = my_0
            wxq12 = my_0
            wxq13 = my_0
            wxq14 = my_0
            wxt1  = my_0

            wxq21 = my_0
            wxq22 = my_0
            wxq23 = my_0
            wxq24 = my_0
            wxt2  = my_0

            wxq31 = my_0
            wxq32 = my_0
            wxq33 = my_0
            wxq34 = my_0
            wxt3  = my_0

        endif

        if ( has_y_symmetry(symmetry(node3)) ) then

          vxd = my_0
          vx  = my_0
            vxq11 = my_0
            vxq12 = my_0
            vxq13 = my_0
            vxq14 = my_0
            vxt1  = my_0

            vxq21 = my_0
            vxq22 = my_0
            vxq23 = my_0
            vxq24 = my_0
            vxt2  = my_0

            vxq31 = my_0
            vxq32 = my_0
            vxq33 = my_0
            vxq34 = my_0
            vxt3  = my_0

          vzd = my_0
          vz  = my_0
            vzq11 = my_0
            vzq12 = my_0
            vzq13 = my_0
            vzq14 = my_0
            vzt1  = my_0

            vzq21 = my_0
            vzq22 = my_0
            vzq23 = my_0
            vzq24 = my_0
            vzt2  = my_0

            vzq31 = my_0
            vzq32 = my_0
            vzq33 = my_0
            vzq34 = my_0
            vzt3  = my_0

          uyd = my_0
          uy  = my_0
            uyq11 = my_0
            uyq12 = my_0
            uyq13 = my_0
            uyq14 = my_0
            uyt1  = my_0

            uyq21 = my_0
            uyq22 = my_0
            uyq23 = my_0
            uyq24 = my_0
            uyt2  = my_0

            uyq31 = my_0
            uyq32 = my_0
            uyq33 = my_0
            uyq34 = my_0
            uyt3  = my_0

          wyd = my_0
          wy  = my_0
            wyq11 = my_0
            wyq12 = my_0
            wyq13 = my_0
            wyq14 = my_0
            wyt1  = my_0

            wyq21 = my_0
            wyq22 = my_0
            wyq23 = my_0
            wyq24 = my_0
            wyt2  = my_0

            wyq31 = my_0
            wyq32 = my_0
            wyq33 = my_0
            wyq34 = my_0
            wyt3  = my_0

        endif

        if ( has_z_symmetry(symmetry(node3)) ) then

          wxd = my_0
          wx  = my_0
            wxq11 = my_0
            wxq12 = my_0
            wxq13 = my_0
            wxq14 = my_0
            wxt1  = my_0

            wxq21 = my_0
            wxq22 = my_0
            wxq23 = my_0
            wxq24 = my_0
            wxt2  = my_0

            wxq31 = my_0
            wxq32 = my_0
            wxq33 = my_0
            wxq34 = my_0
            wxt3  = my_0

          wyd = my_0
          wy  = my_0
            wyq11 = my_0
            wyq12 = my_0
            wyq13 = my_0
            wyq14 = my_0
            wyt1  = my_0

            wyq21 = my_0
            wyq22 = my_0
            wyq23 = my_0
            wyq24 = my_0
            wyt2  = my_0

            wyq31 = my_0
            wyq32 = my_0
            wyq33 = my_0
            wyq34 = my_0
            wyt3  = my_0

          uzd = my_0
          uz  = my_0
            uzq11 = my_0
            uzq12 = my_0
            uzq13 = my_0
            uzq14 = my_0
            uzt1  = my_0

            uzq21 = my_0
            uzq22 = my_0
            uzq23 = my_0
            uzq24 = my_0
            uzt2  = my_0

            uzq31 = my_0
            uzq32 = my_0
            uzq33 = my_0
            uzq34 = my_0
            uzt3  = my_0

          vzd = my_0
          vz  = my_0
            vzq11 = my_0
            vzq12 = my_0
            vzq13 = my_0
            vzq14 = my_0
            vzt1  = my_0

            vzq21 = my_0
            vzq22 = my_0
            vzq23 = my_0
            vzq24 = my_0
            vzt2  = my_0

            vzq31 = my_0
            vzq32 = my_0
            vzq33 = my_0
            vzq34 = my_0
            vzt3  = my_0

        endif

        S    = sqrt( (wyd-vzd)**2 + (uzd-wxd)**2 + (vxd-uyd)**2)

        if ( S <= 1.0e-8_dp) then

          S = 1.0e-8_dp
          Sq11 = 0.0_dp
          Sq12 = 0.0_dp
          Sq13 = 0.0_dp
          Sq14 = 0.0_dp
          St1  = 0.0_dp

          Sq21 = 0.0_dp
          Sq22 = 0.0_dp
          Sq23 = 0.0_dp
          Sq24 = 0.0_dp
          St2  = 0.0_dp

          Sq31 = 0.0_dp
          Sq32 = 0.0_dp
          Sq33 = 0.0_dp
          Sq34 = 0.0_dp
          St3  = 0.0_dp

        else

          Sq11 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq11-vzq11)                          &
                       + 2.0_dp*(uz-wx)*(uzq11-wxq11)                          &
                       + 2.0_dp*(vx-uy)*(vxq11-uyq11)) / S
          Sq12 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq12-vzq12)                          &
                       + 2.0_dp*(uz-wx)*(uzq12-wxq12)                          &
                       + 2.0_dp*(vx-uy)*(vxq12-uyq12)) / S
          Sq13 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq13-vzq13)                          &
                       + 2.0_dp*(uz-wx)*(uzq13-wxq13)                          &
                       + 2.0_dp*(vx-uy)*(vxq13-uyq13)) / S
          Sq14 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq14-vzq14)                          &
                       + 2.0_dp*(uz-wx)*(uzq14-wxq14)                          &
                       + 2.0_dp*(vx-uy)*(vxq14-uyq14)) / S
          St1  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt1 -vzt1 )                          &
                       + 2.0_dp*(uz-wx)*(uzt1 -wxt1 )                          &
                       + 2.0_dp*(vx-uy)*(vxt1 -uyt1 )) / S

          Sq21 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq21-vzq21)                          &
                       + 2.0_dp*(uz-wx)*(uzq21-wxq21)                          &
                       + 2.0_dp*(vx-uy)*(vxq21-uyq21)) / S
          Sq22 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq22-vzq22)                          &
                       + 2.0_dp*(uz-wx)*(uzq22-wxq22)                          &
                       + 2.0_dp*(vx-uy)*(vxq22-uyq22)) / S
          Sq23 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq23-vzq23)                          &
                       + 2.0_dp*(uz-wx)*(uzq23-wxq23)                          &
                       + 2.0_dp*(vx-uy)*(vxq23-uyq23)) / S
          Sq24 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq24-vzq24)                          &
                       + 2.0_dp*(uz-wx)*(uzq24-wxq24)                          &
                       + 2.0_dp*(vx-uy)*(vxq24-uyq24)) / S
          St2  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt2 -vzt2 )                          &
                       + 2.0_dp*(uz-wx)*(uzt2 -wxt2 )                          &
                       + 2.0_dp*(vx-uy)*(vxt2 -uyt2 )) / S

          Sq31 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq31-vzq31)                          &
                       + 2.0_dp*(uz-wx)*(uzq31-wxq31)                          &
                       + 2.0_dp*(vx-uy)*(vxq31-uyq31)) / S
          Sq32 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq32-vzq32)                          &
                       + 2.0_dp*(uz-wx)*(uzq32-wxq32)                          &
                       + 2.0_dp*(vx-uy)*(vxq32-uyq32)) / S
          Sq33 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq33-vzq33)                          &
                       + 2.0_dp*(uz-wx)*(uzq33-wxq33)                          &
                       + 2.0_dp*(vx-uy)*(vxq33-uyq33)) / S
          Sq34 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq34-vzq34)                          &
                       + 2.0_dp*(uz-wx)*(uzq34-wxq34)                          &
                       + 2.0_dp*(vx-uy)*(vxq34-uyq34)) / S
          St3  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt3 -vzt3 )                          &
                       + 2.0_dp*(uz-wx)*(uzt3 -wxt3 )                          &
                       + 2.0_dp*(vx-uy)*(vxt3 -uyt3 )) / S

        endif

      if ( dacles_mariani ) then
        sij_mag = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                        &
                          +2._dp*(ux**2+vy**2+wz**2))
          sij_magq11 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq11+vzq11)          &
                                        +  my_2*(uz+wx)*(uzq11+wxq11)          &
                                        +  my_2*(vx+uy)*(vxq11+uyq11)          &
                                               + my_2*(my_2*ux*uxq11           &
                                                     + my_2*vy*vyq11           &
                                                     + my_2*wz*wzq11) )
          sij_magq12 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq12+vzq12)          &
                                        +  my_2*(uz+wx)*(uzq12+wxq12)          &
                                        +  my_2*(vx+uy)*(vxq12+uyq12)          &
                                               + my_2*(my_2*ux*uxq12           &
                                                     + my_2*vy*vyq12           &
                                                     + my_2*wz*wzq12) )
          sij_magq13 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq13+vzq13)          &
                                        +  my_2*(uz+wx)*(uzq13+wxq13)          &
                                        +  my_2*(vx+uy)*(vxq13+uyq13)          &
                                               + my_2*(my_2*ux*uxq13           &
                                                     + my_2*vy*vyq13           &
                                                     + my_2*wz*wzq13) )
          sij_magq14 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq14+vzq14)          &
                                        +  my_2*(uz+wx)*(uzq14+wxq14)          &
                                        +  my_2*(vx+uy)*(vxq14+uyq14)          &
                                               + my_2*(my_2*ux*uxq14           &
                                                     + my_2*vy*vyq14           &
                                                     + my_2*wz*wzq14) )
          sij_magt1  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt1 +vzt1 )          &
                                        +  my_2*(uz+wx)*(uzt1 +wxt1 )          &
                                        +  my_2*(vx+uy)*(vxt1 +uyt1 )          &
                                               + my_2*(my_2*ux*uxt1            &
                                                     + my_2*vy*vyt1            &
                                                     + my_2*wz*wzt1 ) )

          sij_magq21 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq21+vzq21)          &
                                        +  my_2*(uz+wx)*(uzq21+wxq21)          &
                                        +  my_2*(vx+uy)*(vxq21+uyq21)          &
                                               + my_2*(my_2*ux*uxq21           &
                                                     + my_2*vy*vyq21           &
                                                     + my_2*wz*wzq21) )
          sij_magq22 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq22+vzq22)          &
                                        +  my_2*(uz+wx)*(uzq22+wxq22)          &
                                        +  my_2*(vx+uy)*(vxq22+uyq22)          &
                                               + my_2*(my_2*ux*uxq22           &
                                                     + my_2*vy*vyq22           &
                                                     + my_2*wz*wzq22) )
          sij_magq23 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq23+vzq23)          &
                                        +  my_2*(uz+wx)*(uzq23+wxq23)          &
                                        +  my_2*(vx+uy)*(vxq23+uyq23)          &
                                               + my_2*(my_2*ux*uxq23           &
                                                     + my_2*vy*vyq23           &
                                                     + my_2*wz*wzq23) )
          sij_magq24 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq24+vzq24)          &
                                        +  my_2*(uz+wx)*(uzq24+wxq24)          &
                                        +  my_2*(vx+uy)*(vxq24+uyq24)          &
                                               + my_2*(my_2*ux*uxq24           &
                                                     + my_2*vy*vyq24           &
                                                     + my_2*wz*wzq24) )
          sij_magt2  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt2 +vzt2 )          &
                                        +  my_2*(uz+wx)*(uzt2 +wxt2 )          &
                                        +  my_2*(vx+uy)*(vxt2 +uyt2 )          &
                                               + my_2*(my_2*ux*uxt2            &
                                                     + my_2*vy*vyt2            &
                                                     + my_2*wz*wzt2 ) )

          sij_magq31 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq31+vzq31)          &
                                        +  my_2*(uz+wx)*(uzq31+wxq31)          &
                                        +  my_2*(vx+uy)*(vxq31+uyq31)          &
                                               + my_2*(my_2*ux*uxq31           &
                                                     + my_2*vy*vyq31           &
                                                     + my_2*wz*wzq31) )
          sij_magq32 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq32+vzq32)          &
                                        +  my_2*(uz+wx)*(uzq32+wxq32)          &
                                        +  my_2*(vx+uy)*(vxq32+uyq32)          &
                                               + my_2*(my_2*ux*uxq32           &
                                                     + my_2*vy*vyq32           &
                                                     + my_2*wz*wzq32) )
          sij_magq33 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq33+vzq33)          &
                                        +  my_2*(uz+wx)*(uzq33+wxq33)          &
                                        +  my_2*(vx+uy)*(vxq33+uyq33)          &
                                               + my_2*(my_2*ux*uxq33           &
                                                     + my_2*vy*vyq33           &
                                                     + my_2*wz*wzq33) )
          sij_magq34 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq34+vzq34)          &
                                        +  my_2*(uz+wx)*(uzq34+wxq34)          &
                                        +  my_2*(vx+uy)*(vxq34+uyq34)          &
                                               + my_2*(my_2*ux*uxq34           &
                                                     + my_2*vy*vyq34           &
                                                     + my_2*wz*wzq34) )
          sij_magt3  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt3 +vzt3 )          &
                                        +  my_2*(uz+wx)*(uzt3 +wxt3 )          &
                                        +  my_2*(vx+uy)*(vxt3 +uyt3 )          &
                                               + my_2*(my_2*ux*uxt3            &
                                                     + my_2*vy*vyt3            &
                                                     + my_2*wz*wzt3 ) )
        if ( (sij_mag-s) < my_0 ) then

          s = s + 2._dp*min(my_0,sij_mag-s)

          sq11 = sq11 + my_2*(sij_magq11 - sq11)
          sq12 = sq12 + my_2*(sij_magq12 - sq12)
          sq13 = sq13 + my_2*(sij_magq13 - sq13)
          sq14 = sq14 + my_2*(sij_magq14 - sq14)
          st1  = st1  + my_2*(sij_magt1  - st1 )

          sq21 = sq21 + my_2*(sij_magq21 - sq21)
          sq22 = sq22 + my_2*(sij_magq22 - sq22)
          sq23 = sq23 + my_2*(sij_magq23 - sq23)
          sq24 = sq24 + my_2*(sij_magq24 - sq24)
          st2  = st2  + my_2*(sij_magt2  - st2 )

          sq31 = sq31 + my_2*(sij_magq31 - sq31)
          sq32 = sq32 + my_2*(sij_magq32 - sq32)
          sq33 = sq33 + my_2*(sij_magq33 - sq33)
          sq34 = sq34 + my_2*(sij_magq34 - sq34)
          st3  = st3  + my_2*(sij_magt3  - st3 )
        endif

      end if


!  Remember we are only linearizing vorticity this time

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

        if ( use_bp_model ) turb_abs = chi*rnu

        edwards_mod6 : if(.not. use_edwards_mod) then

          if ( use_bp_model ) then

            Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
            Swq11 = Sq11
            Swq12 = Sq12
            Swq13 = Sq13
            Swq14 = Sq14
            Swt1  = St1

            Swq21 = Sq21
            Swq22 = Sq22
            Swq23 = Sq23
            Swq24 = Sq24
            Swt2  = St2

            Swq31 = Sq31
            Swq32 = Sq32
            Swq33 = Sq33
            Swq34 = Sq34
            Swt3  = St3

            if ( Sw < 0.3_dp*S ) then
              s_bar = 1.0_dp/Re*turb_abs/bot*fv2
              term1 = 0.49_dp*S + 0.9_dp*s_bar
                term1q11 = 0.49_dp*Sq11
                term1q12 = 0.49_dp*Sq12
                term1q13 = 0.49_dp*Sq13
                term1q14 = 0.49_dp*Sq14
                term1t1  = 0.49_dp*St1

                term1q21 = 0.49_dp*Sq21
                term1q22 = 0.49_dp*Sq22
                term1q23 = 0.49_dp*Sq23
                term1q24 = 0.49_dp*Sq24
                term1t2  = 0.49_dp*St2

                term1q31 = 0.49_dp*Sq31
                term1q32 = 0.49_dp*Sq32
                term1q33 = 0.49_dp*Sq33
                term1q34 = 0.49_dp*Sq34
                term1t3  = 0.49_dp*St3

              term2 = -0.5_dp*S - s_bar
                term2q11 = -0.5_dp*Sq11
                term2q12 = -0.5_dp*Sq12
                term2q13 = -0.5_dp*Sq13
                term2q14 = -0.5_dp*Sq14
                term2t1  = -0.5_dp*St1

                term2q21 = -0.5_dp*Sq21
                term2q22 = -0.5_dp*Sq22
                term2q23 = -0.5_dp*Sq23
                term2q24 = -0.5_dp*Sq24
                term2t2  = -0.5_dp*St2

                term2q31 = -0.5_dp*Sq31
                term2q32 = -0.5_dp*Sq32
                term2q33 = -0.5_dp*Sq33
                term2q34 = -0.5_dp*Sq34
                term2t3  = -0.5_dp*St3

              Sw = S + S*term1/term2
                Swq11 = Sq11 + S*((term2*term1q11-term1*term2q11)/term2/term2) &
                      + term1/term2*Sq11
                Swq12 = Sq12 + S*((term2*term1q12-term1*term2q12)/term2/term2) &
                      + term1/term2*Sq12
                Swq13 = Sq13 + S*((term2*term1q13-term1*term2q13)/term2/term2) &
                      + term1/term2*Sq13
                Swq14 = Sq14 + S*((term2*term1q14-term1*term2q14)/term2/term2) &
                      + term1/term2*Sq14
                Swt1  = St1  + S*((term2*term1t1 -term1*term2t1 )/term2/term2) &
                      + term1/term2*St1

                Swq21 = Sq21 + S*((term2*term1q21-term1*term2q21)/term2/term2) &
                      + term1/term2*Sq21
                Swq22 = Sq22 + S*((term2*term1q22-term1*term2q22)/term2/term2) &
                      + term1/term2*Sq22
                Swq23 = Sq23 + S*((term2*term1q23-term1*term2q23)/term2/term2) &
                      + term1/term2*Sq23
                Swq24 = Sq24 + S*((term2*term1q24-term1*term2q24)/term2/term2) &
                      + term1/term2*Sq24
                Swt2  = St2  + S*((term2*term1t2 -term1*term2t2 )/term2/term2) &
                      + term1/term2*St2

                Swq31 = Sq31 + S*((term2*term1q31-term1*term2q31)/term2/term2) &
                      + term1/term2*Sq31
                Swq32 = Sq32 + S*((term2*term1q32-term1*term2q32)/term2/term2) &
                      + term1/term2*Sq32
                Swq33 = Sq33 + S*((term2*term1q33-term1*term2q33)/term2/term2) &
                      + term1/term2*Sq33
                Swq34 = Sq34 + S*((term2*term1q34-term1*term2q34)/term2/term2) &
                      + term1/term2*Sq34
                Swt3  = St3  + S*((term2*term1t3 -term1*term2t3 )/term2/term2) &
                      + term1/term2*St3
            endif

          else

            Sw   = S + 1.0_dp/Re*turb(1,node3)/bot*fv2
            Swq11 = Sq11
            Swq12 = Sq12
            Swq13 = Sq13
            Swq14 = Sq14
            Swt1  = St1

            Swq21 = Sq21
            Swq22 = Sq22
            Swq23 = Sq23
            Swq24 = Sq24
            Swt2  = St2

            Swq31 = Sq31
            Swq32 = Sq32
            Swq33 = Sq33
            Swq34 = Sq34
            Swt3  = St3

            if(Sw <= 0.00001_dp) then
              Sw = 0.00001_dp
              Swq11 = 0.0_dp
              Swq12 = 0.0_dp
              Swq13 = 0.0_dp
              Swq14 = 0.0_dp
              Swt1  = 0.0_dp

              Swq21 = 0.0_dp
              Swq22 = 0.0_dp
              Swq23 = 0.0_dp
              Swq24 = 0.0_dp
              Swt2  = 0.0_dp

              Swq31 = 0.0_dp
              Swq32 = 0.0_dp
              Swq33 = 0.0_dp
              Swq34 = 0.0_dp
              Swt3  = 0.0_dp
            endif

          endif

          RR   = 1.0_dp/Re*turb(1,node3)/bot/Sw
          RRq11 = -RR*Swq11/Sw
          RRq12 = -RR*Swq12/Sw
          RRq13 = -RR*Swq13/Sw
          RRq14 = -RR*Swq14/Sw

          RRq21 = -RR*Swq21/Sw
          RRq22 = -RR*Swq22/Sw
          RRq23 = -RR*Swq23/Sw
          RRq24 = -RR*Swq24/Sw

          RRq31 = -RR*Swq31/Sw
          RRq32 = -RR*Swq32/Sw
          RRq33 = -RR*Swq33/Sw
          RRq34 = -RR*Swq34/Sw

          if(RR > 10.0_dp) then
            RR = 10.0_dp
            RRq11 = 0.0_dp
            RRq12 = 0.0_dp
            RRq13 = 0.0_dp
            RRq14 = 0.0_dp

            RRq21 = 0.0_dp
            RRq22 = 0.0_dp
            RRq23 = 0.0_dp
            RRq24 = 0.0_dp

            RRq31 = 0.0_dp
            RRq32 = 0.0_dp
            RRq33 = 0.0_dp
            RRq34 = 0.0_dp
          endif

        else edwards_mod6

          sw = s*(1.0_dp/chi + fv1)
          swq11 = sq11*(1.0_dp/chi + fv1)
          swq12 = sq12*(1.0_dp/chi + fv1)
          swq13 = sq13*(1.0_dp/chi + fv1)
          swq14 = sq14*(1.0_dp/chi + fv1)
          swt1  = st1 *(1.0_dp/chi + fv1)

          swq21 = sq21*(1.0_dp/chi + fv1)
          swq22 = sq22*(1.0_dp/chi + fv1)
          swq23 = sq23*(1.0_dp/chi + fv1)
          swq24 = sq24*(1.0_dp/chi + fv1)
          swt2  = st2 *(1.0_dp/chi + fv1)

          swq31 = sq31*(1.0_dp/chi + fv1)
          swq32 = sq32*(1.0_dp/chi + fv1)
          swq33 = sq33*(1.0_dp/chi + fv1)
          swq34 = sq34*(1.0_dp/chi + fv1)
          swt3  = st3 *(1.0_dp/chi + fv1)

          distance = slen(node3)
          arg = vkar*vkar*distance*distance

          tanharg = 1.0_dp/re*turb(1,node3)/arg/sw
          tanhargq11 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq11
          tanhargq12 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq12
          tanhargq13 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq13
          tanhargq14 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq14

          tanhargq21 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq21
          tanhargq22 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq22
          tanhargq23 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq23
          tanhargq24 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq24

          tanhargq31 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq31
          tanhargq32 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq32
          tanhargq33 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq33
          tanhargq34 = -1.0_dp*1.0_dp/re*turb(1,node3)/arg/sw/sw * swq34

          if(tanharg < 0.0_dp)                                                 &
            write(*,*) 'Negative tanharg, fix limiting on cosh()'

          rr = tanh(tanharg) / tanh(1.0_dp)
          if(tanharg > 15.0_dp) then
            rrq11 = 0.0_dp
            rrq12 = 0.0_dp
            rrq13 = 0.0_dp
            rrq14 = 0.0_dp

            rrq21 = 0.0_dp
            rrq22 = 0.0_dp
            rrq23 = 0.0_dp
            rrq24 = 0.0_dp

            rrq31 = 0.0_dp
            rrq32 = 0.0_dp
            rrq33 = 0.0_dp
            rrq34 = 0.0_dp
          else
            rrq11=1.0_dp/tanh(1.0_dp)*tanhargq11                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq12=1.0_dp/tanh(1.0_dp)*tanhargq12                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq13=1.0_dp/tanh(1.0_dp)*tanhargq13                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq14=1.0_dp/tanh(1.0_dp)*tanhargq14                               &
                 /cosh(tanharg)/cosh(tanharg)

            rrq21=1.0_dp/tanh(1.0_dp)*tanhargq21                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq22=1.0_dp/tanh(1.0_dp)*tanhargq22                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq23=1.0_dp/tanh(1.0_dp)*tanhargq23                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq24=1.0_dp/tanh(1.0_dp)*tanhargq24                               &
                 /cosh(tanharg)/cosh(tanharg)

            rrq31=1.0_dp/tanh(1.0_dp)*tanhargq31                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq32=1.0_dp/tanh(1.0_dp)*tanhargq32                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq33=1.0_dp/tanh(1.0_dp)*tanhargq33                               &
                 /cosh(tanharg)/cosh(tanharg)
            rrq34=1.0_dp/tanh(1.0_dp)*tanhargq34                               &
                 /cosh(tanharg)/cosh(tanharg)
          endif

        endif edwards_mod6

        GG   = RR + cw2*(RR**6 - RR)
        GGq11 = RRq11 + cw2*(6.0_dp*RR**5*RRq11 - RRq11)
        GGq12 = RRq12 + cw2*(6.0_dp*RR**5*RRq12 - RRq12)
        GGq13 = RRq13 + cw2*(6.0_dp*RR**5*RRq13 - RRq13)
        GGq14 = RRq14 + cw2*(6.0_dp*RR**5*RRq14 - RRq14)

        GGq21 = RRq21 + cw2*(6.0_dp*RR**5*RRq21 - RRq21)
        GGq22 = RRq22 + cw2*(6.0_dp*RR**5*RRq22 - RRq22)
        GGq23 = RRq23 + cw2*(6.0_dp*RR**5*RRq23 - RRq23)
        GGq24 = RRq24 + cw2*(6.0_dp*RR**5*RRq24 - RRq24)

        GGq31 = RRq31 + cw2*(6.0_dp*RR**5*RRq31 - RRq31)
        GGq32 = RRq32 + cw2*(6.0_dp*RR**5*RRq32 - RRq32)
        GGq33 = RRq33 + cw2*(6.0_dp*RR**5*RRq33 - RRq33)
        GGq34 = RRq34 + cw2*(6.0_dp*RR**5*RRq34 - RRq34)

        onesix = 1.0_dp/6.0_dp
        fivesix = -5.0_dp/6.0_dp
!       fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
        term = (1.0_dp + cw3**6)/(GG**6 + cw3**6)
        bottom = (GG**6 + cw3**6)**2
        factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
        fwq11 = GGq11*(term**onesix - factor)
        fwq12 = GGq12*(term**onesix - factor)
        fwq13 = GGq13*(term**onesix - factor)
        fwq14 = GGq14*(term**onesix - factor)

        fwq21 = GGq21*(term**onesix - factor)
        fwq22 = GGq22*(term**onesix - factor)
        fwq23 = GGq23*(term**onesix - factor)
        fwq24 = GGq24*(term**onesix - factor)

        fwq31 = GGq31*(term**onesix - factor)
        fwq32 = GGq32*(term**onesix - factor)
        fwq33 = GGq33*(term**onesix - factor)
        fwq34 = GGq34*(term**onesix - factor)

        temp_arg = -ct4*chi*chi

        if ( use_bp_model .and. temp_arg < -700.0_dp ) temp_arg = -700.0_dp

        ft2  = ct3*exp(temp_arg)

!       Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,node3))
!       Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)  ! for use_bp_model

        if ( use_bp_model ) then

          Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb_abs)
          Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb_abs)
          Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb_abs)
          Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb_abs)
          Prodt1  = (cb1*(1.0_dp - ft2)*Swt1 *turb_abs)

          Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb_abs)
          Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb_abs)
          Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb_abs)
          Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb_abs)
          Prodt2  = (cb1*(1.0_dp - ft2)*Swt2 *turb_abs)

          Prodq31 = (cb1*(1.0_dp - ft2)*Swq31*turb_abs)
          Prodq32 = (cb1*(1.0_dp - ft2)*Swq32*turb_abs)
          Prodq33 = (cb1*(1.0_dp - ft2)*Swq33*turb_abs)
          Prodq34 = (cb1*(1.0_dp - ft2)*Swq34*turb_abs)
          Prodt3  = (cb1*(1.0_dp - ft2)*Swt3 *turb_abs)

        else

          Prodq11 = (cb1*(1.0_dp - ft2)*Swq11*turb(1,node3))
          Prodq12 = (cb1*(1.0_dp - ft2)*Swq12*turb(1,node3))
          Prodq13 = (cb1*(1.0_dp - ft2)*Swq13*turb(1,node3))
          Prodq14 = (cb1*(1.0_dp - ft2)*Swq14*turb(1,node3))
          Prodt1  = (cb1*(1.0_dp - ft2)*Swt1*turb(1,node3))

          Prodq21 = (cb1*(1.0_dp - ft2)*Swq21*turb(1,node3))
          Prodq22 = (cb1*(1.0_dp - ft2)*Swq22*turb(1,node3))
          Prodq23 = (cb1*(1.0_dp - ft2)*Swq23*turb(1,node3))
          Prodq24 = (cb1*(1.0_dp - ft2)*Swq24*turb(1,node3))
          Prodt2  = (cb1*(1.0_dp - ft2)*Swt2*turb(1,node3))

          Prodq31 = (cb1*(1.0_dp - ft2)*Swq31*turb(1,node3))
          Prodq32 = (cb1*(1.0_dp - ft2)*Swq32*turb(1,node3))
          Prodq33 = (cb1*(1.0_dp - ft2)*Swq33*turb(1,node3))
          Prodq34 = (cb1*(1.0_dp - ft2)*Swq34*turb(1,node3))
          Prodt3  = (cb1*(1.0_dp - ft2)*Swt3*turb(1,node3))

        endif

!       vkar2 = vkar*vkar
!       Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                              &
!            *(turb(1,node3)/slen(node3))**2
!       Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                              &
!             *(turb_abs/slen(node1))**2        ! for use_bp_model

        xmr = 1.0_dp/Re

        if ( use_bp_model ) then
          factor = (turb_abs/slen(node3))**2
        else
          factor = (turb(1,node3)/slen(node3))**2
        endif

        Destq11 = xmr*(cw1*fwq11)*factor
        Destq12 = xmr*(cw1*fwq12)*factor
        Destq13 = xmr*(cw1*fwq13)*factor
        Destq14 = xmr*(cw1*fwq14)*factor
        Destt1  = 0.0_dp
        Destq21 = xmr*(cw1*fwq21)*factor
        Destq22 = xmr*(cw1*fwq22)*factor
        Destq23 = xmr*(cw1*fwq23)*factor
        Destq24 = xmr*(cw1*fwq24)*factor
        Destt2  = 0.0_dp
        Destq31 = xmr*(cw1*fwq31)*factor
        Destq32 = xmr*(cw1*fwq32)*factor
        Destq33 = xmr*(cw1*fwq33)*factor
        Destq34 = xmr*(cw1*fwq34)*factor
        Destt3  = 0.0_dp

!       source = vol(node3)*(Prod - Dest)
        sourceq11 = vol(node3)*(Prodq11 - Destq11)
        sourceq12 = vol(node3)*(Prodq12 - Destq12)
        sourceq13 = vol(node3)*(Prodq13 - Destq13)
        sourceq14 = vol(node3)*(Prodq14 - Destq14)
        sourcet1  = vol(node3)*(Prodt1  - Destt1 )

        sourceq21 = vol(node3)*(Prodq21 - Destq21)
        sourceq22 = vol(node3)*(Prodq22 - Destq22)
        sourceq23 = vol(node3)*(Prodq23 - Destq23)
        sourceq24 = vol(node3)*(Prodq24 - Destq24)
        sourcet2  = vol(node3)*(Prodt2  - Destt2 )

        sourceq31 = vol(node3)*(Prodq31 - Destq31)
        sourceq32 = vol(node3)*(Prodq32 - Destq32)
        sourceq33 = vol(node3)*(Prodq33 - Destq33)
        sourceq34 = vol(node3)*(Prodq34 - Destq34)
        sourcet3  = vol(node3)*(Prodt3  - Destt3 )

        res3q11 = -sourceq11
        res3q12 = -sourceq12
        res3q13 = -sourceq13
        res3q14 = -sourceq14
        res3t1  = -sourcet1

        res3q21 = -sourceq21
        res3q22 = -sourceq22
        res3q23 = -sourceq23
        res3q24 = -sourceq24
        res3t2  = -sourcet2

        res3q31 = -sourceq31
        res3q32 = -sourceq32
        res3q33 = -sourceq33
        res3q34 = -sourceq34
        res3t3  = -sourcet3

        if ( fill_res ) then
          do i = 1, nfunctions
            rlam5(i) = coltag(5,node3)*rlam(5,node3,i)
            if(node3 <= nnodes0 .and. coltag(5,node3) > 0.1_dp) then
              res(1,node3,i) = res(1,node3,i) + res3q31*rlam5(i)
              res(2,node3,i) = res(2,node3,i) + res3q32*rlam5(i)
              res(3,node3,i) = res(3,node3,i) + res3q33*rlam5(i)
              res(4,node3,i) = res(4,node3,i) + res3q34*rlam5(i)
              res(5,node3,i) = res(5,node3,i) + res3t3 *rlam5(i)
            endif
          end do

          do i = 1, nfunctions
            rlam5(i) = coltag(5,node3)*rlam(5,node3,i)
            if(node1 <= nnodes0 .and. coltag(5,node3) > 0.1_dp) then
              res(1,node1,i) = res(1,node1,i) + res3q11*rlam5(i)
              res(2,node1,i) = res(2,node1,i) + res3q12*rlam5(i)
              res(3,node1,i) = res(3,node1,i) + res3q13*rlam5(i)
              res(4,node1,i) = res(4,node1,i) + res3q14*rlam5(i)
              res(5,node1,i) = res(5,node1,i) + res3t1 *rlam5(i)
            endif
          end do

          do i = 1, nfunctions
            rlam5(i) = coltag(5,node3)*rlam(5,node3,i)
            if(node2 <= nnodes0 .and. coltag(5,node3) > 0.1_dp) then
              res(1,node2,i) = res(1,node2,i) + res3q21*rlam5(i)
              res(2,node2,i) = res(2,node2,i) + res3q22*rlam5(i)
              res(3,node2,i) = res(3,node2,i) + res3q23*rlam5(i)
              res(4,node2,i) = res(4,node2,i) + res3q24*rlam5(i)
              res(5,node2,i) = res(5,node2,i) + res3t2 *rlam5(i)
            endif
          end do
        endif

        contribs3 : if ( fill_a ) then

          local_node3_diag : if(node3 <= nnodes0) then
            idiag = iau(node3)
            A(5,1,idiag) = A(5,1,idiag) + res3q31
            A(5,2,idiag) = A(5,2,idiag) + res3q32
            A(5,3,idiag) = A(5,3,idiag) + res3q33
            A(5,4,idiag) = A(5,4,idiag) + res3q34
            A(5,5,idiag) = A(5,5,idiag) + res3t3
          endif local_node3_diag

!  Ok, let's locate the off-diagonal locations

          if(node1 <= nnodes0) then
            jstart = ia(node1)
            jend   = ia(node1+1)-1
            ioff1 = 0
            do j = jstart, jend
              jcol = ja(j)
              if ( jcol == node3 ) ioff1 = j
            end do
            if ( ioff1 == 0 ) then
              write(*,*) 'trouble locating ioff1'
              call lmpi_die
              stop
            endif

            A(5,1,ioff1) = A(5,1,ioff1) + res3q11
            A(5,2,ioff1) = A(5,2,ioff1) + res3q12
            A(5,3,ioff1) = A(5,3,ioff1) + res3q13
            A(5,4,ioff1) = A(5,4,ioff1) + res3q14
            A(5,5,ioff1) = A(5,5,ioff1) + res3t1
          endif

!  Now repeat the same process for node2

          if(node2 <= nnodes0) then
            jstart = ia(node2)
            jend   = ia(node2+1)-1
            ioff2 = 0
            do j = jstart, jend
              jcol = ja(j)
              if ( jcol == node3 ) ioff2 = j
            end do
            if ( ioff2 == 0 ) then
              write(*,*) 'trouble locating ioff2'
              call lmpi_die
              stop
            endif

            A(5,1,ioff2) = A(5,1,ioff2) + res3q21
            A(5,2,ioff2) = A(5,2,ioff2) + res3q22
            A(5,3,ioff2) = A(5,3,ioff2) + res3q23
            A(5,4,ioff2) = A(5,4,ioff2) + res3q24
            A(5,5,ioff2) = A(5,5,ioff2) + res3t2
          endif

        endif contribs3

      end if node3_of_edge_used

      end do face_boundary_tris

! quads are nice because the quantities only come
! from the corner of interest; no off-diagonals

      face_boundary_quads : do n = 1, bc(ib)%nbfaceq

        corner_loop : do quad_corner = 1, 4

          call element_based_metrics(bc,elem,x,y,z,dxdt,dydt,dzdt,ib,n,        &
                                     quad_corner,4,quad_node,quad_weight,xnorm,&
                                     ynorm,znorm,area,face_speed)

          node = quad_node(1)

          if ( node > nnodes0 ) cycle corner_loop

          xnorm = area*xnorm
          ynorm = area*ynorm
          znorm = area*znorm

! Keep in mind that in this part of the code,
! we are only taking into account linearization
! from the vorticity part of the source term; the
! other parts are already taken into account.

! to not divide by slen on surface for -fpe0 NaNa
          node_of_face_used : if ( coltag(5,node) > 0.1_dp ) then

            rnu  = 1.0_dp

            edwards_chi7 : if ( .not. use_edwards_mod ) then
              chi  = turb(1,node)/rnu
              if ( use_bp_model .and. chi < 10.0_dp ) then
                chi = 0.05_dp * log ( 1.0_dp + exp( 20.0_dp*chi ) )
              endif
            else edwards_chi7
              chi  = (turb(1,node)+1.e-12_dp)/rnu
            endif edwards_chi7

            if ( use_bp_model .and. chi < -5.0_dp ) then
              fv1 = 0.0_dp
            else
              fv1 = chi**3/(chi**3 + cv1**3)
            endif

            fv2  = 1.0_dp - chi/(1.0_dp + chi*fv1)
            bot  = vkar*vkar*slen(node)*slen(node)

! Get derivatives of the vorticity source term.

!           uxd  = gradx(2,node)
            ux   = gradx(2,node)
            uxq1 = 0.0_dp
            uxq2 = my_1/vol(node)*xnorm
            uxq3 = 0.0_dp
            uxq4 = 0.0_dp
            uxt  = 0.0_dp

            uyd  = grady(2,node)
            uy   = grady(2,node)
            uyq1 = 0.0_dp
            uyq2 = my_1/vol(node)*ynorm
            uyq3 = 0.0_dp
            uyq4 = 0.0_dp
            uyt  = 0.0_dp

            uzd  = gradz(2,node)
            uz   = gradz(2,node)
            uzq1 = 0.0_dp
            uzq2 = my_1/vol(node)*znorm
            uzq3 = 0.0_dp
            uzq4 = 0.0_dp
            uzt  = 0.0_dp

            vxd  = gradx(3,node)
            vx   = gradx(3,node)
            vxq1 = 0.0_dp
            vxq2 = 0.0_dp
            vxq3 = my_1/vol(node)*xnorm
            vxq4 = 0.0_dp
            vxt  = 0.0_dp

!           vyd  = grady(3,node)
            vy   = grady(3,node)
            vyq1 = 0.0_dp
            vyq2 = 0.0_dp
            vyq3 = my_1/vol(node)*ynorm
            vyq4 = 0.0_dp
            vyt  = 0.0_dp

            vzd  = gradz(3,node)
            vz   = gradz(3,node)
            vzq1 = 0.0_dp
            vzq2 = 0.0_dp
            vzq3 = my_1/vol(node)*znorm
            vzq4 = 0.0_dp
            vzt  = 0.0_dp

            wxd  = gradx(4,node)
            wx   = gradx(4,node)
            wxq1 = 0.0_dp
            wxq2 = 0.0_dp
            wxq3 = 0.0_dp
            wxq4 = my_1/vol(node)*xnorm
            wxt  = 0.0_dp

            wyd  = grady(4,node)
            wy   = grady(4,node)
            wyq1 = 0.0_dp
            wyq2 = 0.0_dp
            wyq3 = 0.0_dp
            wyq4 = my_1/vol(node)*ynorm
            wyt  = 0.0_dp

!           wzd  = gradz(4,node)
            wz   = gradz(4,node)
            wzq1 = 0.0_dp
            wzq2 = 0.0_dp
            wzq3 = 0.0_dp
            wzq4 = my_1/vol(node)*znorm
            wzt  = 0.0_dp

            if ( has_x_symmetry(symmetry(node)) ) then

              uyd = my_0
              uy  = my_0
                uyq1 = my_0
                uyq2 = my_0
                uyq3 = my_0
                uyq4 = my_0
                uyt  = my_0

              uzd = my_0
              uz  = my_0
                uzq1 = my_0
                uzq2 = my_0
                uzq3 = my_0
                uzq4 = my_0
                uzt  = my_0

              vxd = my_0
              vx  = my_0
                vxq1 = my_0
                vxq2 = my_0
                vxq3 = my_0
                vxq4 = my_0
                vxt  = my_0

              wxd = my_0
              wx  = my_0
                wxq1 = my_0
                wxq2 = my_0
                wxq3 = my_0
                wxq4 = my_0
                wxt  = my_0

            endif

            if ( has_y_symmetry(symmetry(node)) ) then

              vxd = my_0
              vx  = my_0
                vxq1 = my_0
                vxq2 = my_0
                vxq3 = my_0
                vxq4 = my_0
                vxt  = my_0

              vzd = my_0
              vz  = my_0
                vzq1 = my_0
                vzq2 = my_0
                vzq3 = my_0
                vzq4 = my_0
                vzt  = my_0

              uyd = my_0
              uy  = my_0
                uyq1 = my_0
                uyq2 = my_0
                uyq3 = my_0
                uyq4 = my_0
                uyt  = my_0

              wyd = my_0
              wy  = my_0
                wyq1 = my_0
                wyq2 = my_0
                wyq3 = my_0
                wyq4 = my_0
                wyt  = my_0

            endif

            if ( has_z_symmetry(symmetry(node)) ) then

              wxd = my_0
              wx  = my_0
                wxq1 = my_0
                wxq2 = my_0
                wxq3 = my_0
                wxq4 = my_0
                wxt  = my_0

              wyd = my_0
              wy  = my_0
                wyq1 = my_0
                wyq2 = my_0
                wyq3 = my_0
                wyq4 = my_0
                wyt  = my_0

              uzd = my_0
              uz  = my_0
                uzq1 = my_0
                uzq2 = my_0
                uzq3 = my_0
                uzq4 = my_0
                uzt  = my_0

              vzd = my_0
              vz  = my_0
                vzq1 = my_0
                vzq2 = my_0
                vzq3 = my_0
                vzq4 = my_0
                vzt  = my_0

            endif

            S = sqrt( (wyd-vzd)**2 + (uzd-wxd)**2 + (vxd-uyd)**2)

            if ( S <= 1.0e-8_dp) then

              S = 1.0e-8_dp
              Sq1 = 0.0_dp
              Sq2 = 0.0_dp
              Sq3 = 0.0_dp
              Sq4 = 0.0_dp
              St  = 0.0_dp

            else

              Sq1 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq1-vzq1)                         &
                          + 2.0_dp*(uz-wx)*(uzq1-wxq1)                         &
                          + 2.0_dp*(vx-uy)*(vxq1-uyq1)) / S
              Sq2 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq2-vzq2)                         &
                          + 2.0_dp*(uz-wx)*(uzq2-wxq2)                         &
                          + 2.0_dp*(vx-uy)*(vxq2-uyq2)) / S
              Sq3 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq3-vzq3)                         &
                          + 2.0_dp*(uz-wx)*(uzq3-wxq3)                         &
                          + 2.0_dp*(vx-uy)*(vxq3-uyq3)) / S
              Sq4 = 0.5_dp*(2.0_dp*(wy-vz)*(wyq4-vzq4)                         &
                          + 2.0_dp*(uz-wx)*(uzq4-wxq4)                         &
                          + 2.0_dp*(vx-uy)*(vxq4-uyq4)) / S
              St  = 0.5_dp*(2.0_dp*(wy-vz)*(wyt -vzt )                         &
                          + 2.0_dp*(uz-wx)*(uzt -wxt )                         &
                          + 2.0_dp*(vx-uy)*(vxt -uyt )) / S

            endif

            if ( dacles_mariani ) then
              sij_mag = sqrt((wy+vz)**2+(uz+wx)**2+(vx+uy)**2                  &
                                +2._dp*(ux**2+vy**2+wz**2))
              sij_magq1 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq1+vzq1)         &
                                           +  my_2*(uz+wx)*(uzq1+wxq1)         &
                                           +  my_2*(vx+uy)*(vxq1+uyq1)         &
                                                  + my_2*(my_2*ux*uxq1         &
                                                        + my_2*vy*vyq1         &
                                                        + my_2*wz*wzq1) )
              sij_magq2 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq2+vzq2)         &
                                           +  my_2*(uz+wx)*(uzq2+wxq2)         &
                                           +  my_2*(vx+uy)*(vxq2+uyq2)         &
                                                  + my_2*(my_2*ux*uxq2         &
                                                        + my_2*vy*vyq2         &
                                                        + my_2*wz*wzq2) )
              sij_magq3 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq3+vzq3)         &
                                           +  my_2*(uz+wx)*(uzq3+wxq3)         &
                                           +  my_2*(vx+uy)*(vxq3+uyq3)         &
                                                  + my_2*(my_2*ux*uxq3         &
                                                        + my_2*vy*vyq3         &
                                                        + my_2*wz*wzq3) )
              sij_magq4 = my_half/sij_mag * ( my_2*(wy+vz)*(wyq4+vzq4)         &
                                           +  my_2*(uz+wx)*(uzq4+wxq4)         &
                                           +  my_2*(vx+uy)*(vxq4+uyq4)         &
                                                  + my_2*(my_2*ux*uxq4         &
                                                        + my_2*vy*vyq4         &
                                                        + my_2*wz*wzq4) )
              sij_magt  = my_half/sij_mag * ( my_2*(wy+vz)*(wyt +vzt )         &
                                           +  my_2*(uz+wx)*(uzt +wxt )         &
                                           +  my_2*(vx+uy)*(vxt +uyt )         &
                                                  + my_2*(my_2*ux*uxt          &
                                                        + my_2*vy*vyt          &
                                                        + my_2*wz*wzt ) )

              if ( (sij_mag-s) < my_0 ) then
                s = s + 2._dp*min(my_0,sij_mag-s)
                sq1 = sq1 + my_2*(sij_magq1 - sq1)
                sq2 = sq2 + my_2*(sij_magq2 - sq2)
                sq3 = sq3 + my_2*(sij_magq3 - sq3)
                sq4 = sq4 + my_2*(sij_magq4 - sq4)
                st  = st  + my_2*(sij_magt  - st )
              endif
            end if

!  Remember we are only linearizing vorticity this time

! If using Jack Edwards' mod, Sw and RR definitions are slightly
! different

            if ( use_bp_model ) turb_abs = chi*rnu

            edwards_mod7 : if(.not. use_edwards_mod) then

              if ( use_bp_model ) then

                Sw   = S + 1.0_dp/Re*turb_abs/bot*fv2
                Swq1 = Sq1
                Swq2 = Sq2
                Swq3 = Sq3
                Swq4 = Sq4
                Swt  = St

                if ( Sw < 0.3_dp*S ) then
                  s_bar = 1.0_dp/Re*turb_abs/bot*fv2
                  term1 = 0.49_dp*S + 0.9_dp*s_bar
                    term1q1 = 0.49_dp*Sq1
                    term1q2 = 0.49_dp*Sq2
                    term1q3 = 0.49_dp*Sq3
                    term1q4 = 0.49_dp*Sq4
                    term1t  = 0.49_dp*St

                  term2 = -0.5_dp*S - s_bar
                    term2q1 = -0.5_dp*Sq1
                    term2q2 = -0.5_dp*Sq2
                    term2q3 = -0.5_dp*Sq3
                    term2q4 = -0.5_dp*Sq4
                    term2t  = -0.5_dp*St

                  Sw = S + S*term1/term2
                    Swq1 = Sq1 + S*((term2*term1q1-term1*term2q1)/term2/term2) &
                          + term1/term2*Sq1
                    Swq2 = Sq2 + S*((term2*term1q2-term1*term2q2)/term2/term2) &
                          + term1/term2*Sq2
                    Swq3 = Sq3 + S*((term2*term1q3-term1*term2q3)/term2/term2) &
                          + term1/term2*Sq3
                    Swq4 = Sq4 + S*((term2*term1q4-term1*term2q4)/term2/term2) &
                          + term1/term2*Sq4
                    Swt  = St  + S*((term2*term1t -term1*term2t )/term2/term2) &
                          + term1/term2*St
                endif

              else

                Sw   = S + 1.0_dp/Re*turb(1,node)/bot*fv2
                Swq1 = Sq1
                Swq2 = Sq2
                Swq3 = Sq3
                Swq4 = Sq4
                Swt  = St

                if(Sw <= 0.00001_dp) then
                  Sw = 0.00001_dp
                  Swq1 = 0.0_dp
                  Swq2 = 0.0_dp
                  Swq3 = 0.0_dp
                  Swq4 = 0.0_dp
                  Swt  = 0.0_dp
                endif

              endif

              RR   = 1.0_dp/Re*turb(1,node)/bot/Sw
              RRq1 = -RR*Swq1/Sw
              RRq2 = -RR*Swq2/Sw
              RRq3 = -RR*Swq3/Sw
              RRq4 = -RR*Swq4/Sw

              if(RR > 10.0_dp) then
                RR = 10.0_dp
                RRq1 = 0.0_dp
                RRq2 = 0.0_dp
                RRq3 = 0.0_dp
                RRq4 = 0.0_dp
              endif

            else edwards_mod7

              sw = s*(1.0_dp/chi + fv1)
              swq1 = sq1*(1.0_dp/chi + fv1)
              swq2 = sq2*(1.0_dp/chi + fv1)
              swq3 = sq3*(1.0_dp/chi + fv1)
              swq4 = sq4*(1.0_dp/chi + fv1)
              swt  = st *(1.0_dp/chi + fv1)

              distance = slen(node)
              arg = vkar*vkar*distance*distance

              tanharg = 1.0_dp/re*turb(1,node)/arg/sw
              tanhargq1 = -1.0_dp*1.0_dp/re*turb(1,node)/arg/sw/sw * swq1
              tanhargq2 = -1.0_dp*1.0_dp/re*turb(1,node)/arg/sw/sw * swq2
              tanhargq3 = -1.0_dp*1.0_dp/re*turb(1,node)/arg/sw/sw * swq3
              tanhargq4 = -1.0_dp*1.0_dp/re*turb(1,node)/arg/sw/sw * swq4

              if ( tanharg < 0.0_dp)                                           &
                           write(*,*) 'Negative tanharg, fix limiting on cosh()'

              rr = tanh(tanharg) / tanh(1.0_dp)
              if(tanharg > 15.0_dp) then
                rrq1 = 0.0_dp
                rrq2 = 0.0_dp
                rrq3 = 0.0_dp
                rrq4 = 0.0_dp
              else
                rrq1=1.0_dp/tanh(1.0_dp)*tanhargq1/cosh(tanharg)/cosh(tanharg)
                rrq2=1.0_dp/tanh(1.0_dp)*tanhargq2/cosh(tanharg)/cosh(tanharg)
                rrq3=1.0_dp/tanh(1.0_dp)*tanhargq3/cosh(tanharg)/cosh(tanharg)
                rrq4=1.0_dp/tanh(1.0_dp)*tanhargq4/cosh(tanharg)/cosh(tanharg)
              endif

            endif edwards_mod7

            GG   = RR + cw2*(RR**6 - RR)
            GGq1 = RRq1 + cw2*(6.0_dp*RR**5*RRq1 - RRq1)
            GGq2 = RRq2 + cw2*(6.0_dp*RR**5*RRq2 - RRq2)
            GGq3 = RRq3 + cw2*(6.0_dp*RR**5*RRq3 - RRq3)
            GGq4 = RRq4 + cw2*(6.0_dp*RR**5*RRq4 - RRq4)

            onesix = 1.0_dp/6.0_dp
            fivesix = -5.0_dp/6.0_dp
!           fw   = GG*((1.0_dp + cw3**6)/(GG**6 + cw3**6))**(1.0_dp/6.0_dp)
            term = (1.0_dp + cw3**6)/(GG**6 + cw3**6)
            bottom = (GG**6 + cw3**6)**2
            factor = GG**6*(1.0_dp + cw3**6)/bottom*term**fivesix
            fwq1 = GGq1*(term**onesix - factor)
            fwq2 = GGq2*(term**onesix - factor)
            fwq3 = GGq3*(term**onesix - factor)
            fwq4 = GGq4*(term**onesix - factor)

            temp_arg = -ct4*chi*chi

            if ( use_bp_model .and. temp_arg < -700.0_dp ) temp_arg = -700.0_dp

            ft2  = ct3*exp(temp_arg)

!           Prod = (cb1*(1.0_dp - ft2)*Sw*turb(1,node))
!           Prod = (cb1*(1.0_dp - ft2)*Sw*turb_abs)  ! for use_bp_model

            if ( use_bp_model ) then

              Prodq1 = (cb1*(1.0_dp - ft2)*Swq1*turb_abs)
              Prodq2 = (cb1*(1.0_dp - ft2)*Swq2*turb_abs)
              Prodq3 = (cb1*(1.0_dp - ft2)*Swq3*turb_abs)
              Prodq4 = (cb1*(1.0_dp - ft2)*Swq4*turb_abs)
              Prodt  = (cb1*(1.0_dp - ft2)*Swt *turb_abs)

            else

              Prodq1 = (cb1*(1.0_dp - ft2)*Swq1*turb(1,node))
              Prodq2 = (cb1*(1.0_dp - ft2)*Swq2*turb(1,node))
              Prodq3 = (cb1*(1.0_dp - ft2)*Swq3*turb(1,node))
              Prodq4 = (cb1*(1.0_dp - ft2)*Swq4*turb(1,node))
              Prodt  = (cb1*(1.0_dp - ft2)*Swt*turb(1,node))

            endif

!           vkar2 = vkar*vkar
!           Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                          &
!                *(turb(1,node)/slen(node))**2
!           Dest = 1.0_dp/Re*(cw1*fw - cb1/vkar2*ft2)                          &
!                 *(turb_abs/slen(node1))**2        ! for use_bp_model

            xmr = 1.0_dp/Re

            if ( use_bp_model ) then
              factor = (turb_abs/slen(node))**2
            else
              factor = (turb(1,node)/slen(node))**2
            endif

            Destq1 = xmr*(cw1*fwq1)*factor
            Destq2 = xmr*(cw1*fwq2)*factor
            Destq3 = xmr*(cw1*fwq3)*factor
            Destq4 = xmr*(cw1*fwq4)*factor
            Destt  = 0.0_dp

!           source = vol(node)*(Prod - Dest)
            sourceq1 = vol(node)*(Prodq1 - Destq1)
            sourceq2 = vol(node)*(Prodq2 - Destq2)
            sourceq3 = vol(node)*(Prodq3 - Destq3)
            sourceq4 = vol(node)*(Prodq4 - Destq4)
            sourcet  = vol(node)*(Prodt  - Destt )

            resq1 = -sourceq1
            resq2 = -sourceq2
            resq3 = -sourceq3
            resq4 = -sourceq4
            rest  = -sourcet

            if ( fill_res ) then
              do i = 1, nfunctions
                rlam5(i) = coltag(5,node)*rlam(5,node,i)
                if(node <= nnodes0 .and. coltag(5,node) > 0.1_dp) then
                  res(1,node,i) = res(1,node,i) + resq1*rlam5(i)
                  res(2,node,i) = res(2,node,i) + resq2*rlam5(i)
                  res(3,node,i) = res(3,node,i) + resq3*rlam5(i)
                  res(4,node,i) = res(4,node,i) + resq4*rlam5(i)
                  res(5,node,i) = res(5,node,i) + rest *rlam5(i)
                endif
              end do
            endif

            if ( fill_a ) then
              local_node_diag : if(node <= nnodes0) then
                idiag = iau(node)
                A(5,1,idiag) = A(5,1,idiag) + resq1
                A(5,2,idiag) = A(5,2,idiag) + resq2
                A(5,3,idiag) = A(5,3,idiag) + resq3
                A(5,4,idiag) = A(5,4,idiag) + resq4
                A(5,5,idiag) = A(5,5,idiag) + rest
              endif local_node_diag
            endif

          end if node_of_face_used

        end do corner_loop

      end do face_boundary_quads

    end do loop_bound

  end subroutine bc_turbpart_sourcei


!==========================TURBPART_MIXED_DIFFUSIONI =========================80
!
! Linearizes diffusion terms for S-A on mixed elements
!
!=============================================================================80
  subroutine turbpart_mixed_diffusioni(nnodes0,nnodes01,ncell,c2n,x,y,z,       &
                                       local_f2n,local_e2n,e2n_2d,             &
                                       face_per_cell,node_per_cell,            &
                                       edge_per_cell,type_cell,adim,nfunctions,&
                                       n_turb,turb,chk_norm,coltag,rlam,res,ia,&
                                       ja,iau,aa)

    use kinddefs,         only : dp
    use info_depr,        only : re, use_edge_gradients, ivgrd
    use element_defs,     only : max_node_per_cell, max_face_per_cell,         &
                                 max_edge_per_cell
    use utilities,        only : cell_jacobians, big_angle
    use lmpi,             only : lmpi_die, lmpi_master
    use turb_sa_const,    only : sig, cb2
    use adjoint_switches, only : use_bp_model
    use debug_defs,       only : gradient_construction_rhs

    integer, intent(in) :: n_turb, adim, nnodes0, nnodes01, ncell
    integer, intent(in) :: face_per_cell, node_per_cell, edge_per_cell
    integer, intent(in) :: nfunctions

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(:),                   intent(in), optional :: ia
    integer, dimension(:),                   intent(in), optional :: ja
    integer, dimension(:),                   intent(in), optional :: iau
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(nnodes01),        intent(in) :: x,y,z
    real(dp), dimension(n_turb,nnodes01), intent(in) :: turb
    real(dp), dimension(adim,nnodes01),   intent(in),    optional :: coltag
    real(dp), dimension(:,:,:),           intent(in),    optional :: rlam
    real(dp), dimension(:,:,:),           intent(inout), optional :: aa
    real(dp), dimension(:,:,:),           intent(inout), optional :: res

    character(len=3), intent(in) :: type_cell

    logical :: edge_gradients

    integer :: n, nodec, idiag, ioff, k, column, iface
    integer :: ie, i, ii, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node
    integer :: n1, n2, n3, n4, n5, n6
    integer :: ifcn, nn1, nn2, nn3, nn4

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: xmre, xmre_s, trbre, rnu, chi, psi, base
    real(dp) :: disi, xavg, yavg, zavg, areai, xnf, ynf, znf
    real(dp) :: ex, ey, ez, factor, nx, ny, nz
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: egradt
    real(dp) :: gradt_xi
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: phi
    real(dp) :: dgradt_xidt,trbrexavg,trbreyavg,trbrezavg
    real(dp) :: qavg
    real(dp) :: termx,termy,termz,termx1,termx2,termy1,termy2,termz1,termz2
    real(dp) :: xavg1,yavg1,zavg1,nx1,ny1,nz1,nx2,ny2,nz2,xavg2,yavg2,zavg2
    real(dp) :: term1,term2,qavg1,qavg2,cell_vol_inv,turb1,turb2
    real(dp) :: trbrex,trbrey,trbrez,ngradt,turbval,aterm

    real(dp), dimension(max_node_per_cell) :: trbre_node
    real(dp), dimension(max_node_per_cell) :: dtrbrexdt
    real(dp), dimension(max_node_per_cell) :: dtrbreydt
    real(dp), dimension(max_node_per_cell) :: dtrbrezdt
    real(dp), dimension(max_node_per_cell) :: dngradtdt
    real(dp), dimension(max_node_per_cell) :: dphidt
    real(dp), dimension(max_node_per_cell) :: dtrbrexavgdt
    real(dp), dimension(max_node_per_cell) :: dtrbreyavgdt
    real(dp), dimension(max_node_per_cell) :: dtrbrezavgdt
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell) :: dgradz_celldq
    real(dp), dimension(max_node_per_cell) :: dtrbredt, dchidt, dpsidt
    real(dp), dimension(max_node_per_cell) :: dbasedt, dturbvaldt
    real(dp), dimension(max_face_per_cell) :: nxf, nyf, nzf

    real(dp), dimension(3) :: augment_weight

    real(dp), parameter :: my_0    = 0.0_dp
    real(dp), parameter :: my_haf  = 0.50_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_6th  = my_1/6.0_dp
    real(dp), parameter :: my_18th = my_1/18.0_dp

    logical :: skip_viscous_terms
    logical :: form_matvec = .false.
    logical :: form_matrix = .false.

  continue

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in turbpart_mixed_diffusioni,'
          write(*,*) 'but args not present.'
        endif
        call lmpi_die
        stop
      endif
      form_matvec = .true.
    endif

    if ( present(aa) ) then
      if ( .not.present(iau) .or. .not.present(ia) .or. .not.present(ja) ) then
        if ( lmpi_master ) then
          write(*,*) 'a requested in turbpart_mixed_diffusioni,'
          write(*,*) 'but args not present.'
        endif
        call lmpi_die
        stop
      endif
      form_matrix = .true.
    endif

    xmre   = 1.0_dp/re
    xmre_s = xmre/sig
    fact   = 1._dp / real(node_per_cell, dp)

! Set some loop indices and local mapping arrays

    edge_map(:) = 0
    node_map(:) = 0

    nodes_local = node_per_cell

    do i=1,nodes_local
      node_map(i) = i
    end do

    edges_local = edge_per_cell

    do i=1,edges_local
      edge_map(i)  = i
    end do

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      xc    = 0.0_dp
      yc    = 0.0_dp
      zc    = 0.0_dp
      rnu   = 0.0_dp
      trbre = 0.0_dp

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop_1 : do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

!       global node number

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        trbre_node(i) = turb(1,node)

        rnu   = rnu + 1.0_dp
        trbre = trbre + trbre_node(i)
        xc    =  xc + x_node(i)
        yc    =  yc + y_node(i)
        zc    =  zc + z_node(i)

        dtrbredt(i) = fact

        dphidt(i) = (1.0_dp + cb2)*dtrbredt(i)

      end do node_loop_1

!     get cell averages by dividing by the number of nodes that contributed

      xc    = xc*fact
      yc    = yc*fact
      zc    = zc*fact
      rnu   = rnu*fact
      trbre = trbre*fact

      if ( use_bp_model ) then
        chi = trbre / rnu
          dchidt(:) = dtrbredt(:)/rnu
        psi = chi
          dpsidt(:) = dchidt(:)
        if ( chi < 10.0_dp ) then
          base = 1.0_dp + exp(20.0_dp*chi)
            dbasedt(:) = exp(20.0_dp*chi)*20.0_dp*dchidt(:)
          psi = 0.05_dp * log(base)
            dpsidt(:) = 0.05_dp/base*dbasedt(:)
        endif
        trbre = psi*rnu
          dtrbredt(:) =  rnu*dpsidt(:)
      endif

      phi   = rnu + (1.0_dp + cb2)*trbre

      if ( use_bp_model ) then
        dphidt(:) =  (1.0_dp + cb2)*dtrbredt(:)
      endif

! Get the gradients in the primal cell via Green-Gauss

      trbrexavg = 0.0_dp
      trbreyavg = 0.0_dp
      trbrezavg = 0.0_dp

      cell_vol = my_0

      threed_faces : do iface = 1, face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!       triangular faces of the cell

!       face normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))         &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))         &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))         &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!       gradient contributions

          qavg = trbre_node(nn1) + trbre_node(nn2) + trbre_node(nn3)

          trbrexavg = trbrexavg + termx*qavg
          trbreyavg = trbreyavg + termy*qavg
          trbrezavg = trbrezavg + termz*qavg

        else

!       quadrilateral faces of the cell

!       break face up into triangles 1-2-3 and 1-3-4 and add together

!       triangle 1: 1-2-3

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       triangle 1 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))        &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))        &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))        &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!       triangle 2: 1-3-4

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!       triangle 2 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))        &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))        &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))        &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!       cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!       gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          qavg1 = trbre_node(nn1) + trbre_node(nn2) + trbre_node(nn3)
          qavg2 = trbre_node(nn1) + trbre_node(nn3) + trbre_node(nn4)

          trbrexavg = trbrexavg + termx1*qavg1 + termx2*qavg2
          trbreyavg = trbreyavg + termy1*qavg1 + termy2*qavg2
          trbrezavg = trbrezavg + termz1*qavg1 + termz2*qavg2

        end if

      end do threed_faces

!   Need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

      cell_vol_inv = my_1/cell_vol

      trbrexavg = trbrexavg * cell_vol_inv
      trbreyavg = trbreyavg * cell_vol_inv
      trbrezavg = trbrezavg * cell_vol_inv

! Get the jacobians of the gradients in the primal cell via Green-Gauss

      call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,       &
                          x_node, y_node, z_node, local_f2n, e2n_2d,           &
                          dgradx_celldq, dgrady_celldq, dgradz_celldq,         &
                          cell_vol, nxf, nyf, nzf)

      skip_viscous_terms = .false.
      if ( ivgrd == 1 ) &
      skip_viscous_terms = big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )
      if ( skip_viscous_terms ) cycle cell_loop

      do i_local = 1, nodes_local
        i = node_map(i_local)
        dtrbrexavgdt(i) = dgradx_celldq(i)
        dtrbreyavgdt(i) = dgrady_celldq(i)
        dtrbrezavgdt(i) = dgradz_celldq(i)
      end do

! Next loop over the edges in the cell and get each ones'
! contribution to the jacobian

      edge_loop : do ie_local = 1, edges_local

!       local edge number

        ie = edge_map(ie_local)

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

! Get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x(n1) + x(n2))/2._dp
        ym = (y(n1) + y(n2))/2._dp
        zm = (z(n1) + z(n2))/2._dp

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))/4._dp
          yl = (y(n1) + y(n2) + y(n3) + y(n4))/4._dp
          zl = (z(n1) + z(n2) + z(n3) + z(n4))/4._dp

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))/3._dp
          yl = (y(n1) + y(n2) + y(n3))/3._dp
          zl = (z(n1) + z(n2) + z(n3))/3._dp

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))/4._dp
          yr = (y(n1) + y(n2) + y(n5) + y(n6))/4._dp
          zr = (z(n1) + z(n2) + z(n5) + z(n6))/4._dp

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))/3._dp
          yr = (y(n1) + y(n2) + y(n5))/3._dp
          zr = (z(n1) + z(n2) + z(n5))/3._dp

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_haf
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_haf
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_haf

! Get (jacobians of) gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity on
! non-simplicial meshes.

! For tets in 3D or prisms in 2D, edge gradients add no new info
! so there is no need to do the extra work

        edge_gradients = use_edge_gradients

        if ( type_cell == 'tet' ) edge_gradients = .false.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )
          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = 1._dp/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf = areax*areai
          ynf = areay*areai
          znf = areaz*areai

          augment_weight = edge_augment_weight( gradient_construction_rhs, &
                                                ex, ey, ez, xnf, ynf, znf )

          turb1 = turb(1,n1)
          turb2 = turb(1,n2)

!         directional gradients along edge

          egradt = ( turb2 - turb1 )*disi

!         average Green-Gauss gradient in edge direction

          gradt_xi = trbrexavg*ex + trbreyavg*ey + trbrezavg*ez

!         combine gradient contributions from edge and primal cell

          trbrex = trbrexavg + ( egradt - gradt_xi )*augment_weight(1)
          trbrey = trbreyavg + ( egradt - gradt_xi )*augment_weight(2)
          trbrez = trbrezavg + ( egradt - gradt_xi )*augment_weight(3)

!         write the above gradients as
!         ux = uxavg - (uxavg*ex + uyavg*ey + uzavg*ez)*ex + (u2-u1)*disi*ex
!              <-----------------avg_term---------------->   <--edge_term-->
!         etc.

!         first get the avg_term pieces; all active cell nodes contribute

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           t-gradient avg terms

            dgradt_xidt=dtrbrexavgdt(i)*ex+dtrbreyavgdt(i)*ey+dtrbrezavgdt(i)*ez

            dtrbrexdt(i) = dtrbrexavgdt(i) - dgradt_xidt*augment_weight(1)
            dtrbreydt(i) = dtrbreyavgdt(i) - dgradt_xidt*augment_weight(2)
            dtrbrezdt(i) = dtrbrezavgdt(i) - dgradt_xidt*augment_weight(3)

          end do

!         next get the edge_term pieces; only the two edge nodes contribute
!         u-gradient edge terms

          dtrbrexdt(n1_loc) = dtrbrexdt(n1_loc) - disi*augment_weight(1)
          dtrbrexdt(n2_loc) = dtrbrexdt(n2_loc) + disi*augment_weight(1)
          dtrbreydt(n1_loc) = dtrbreydt(n1_loc) - disi*augment_weight(2)
          dtrbreydt(n2_loc) = dtrbreydt(n2_loc) + disi*augment_weight(2)
          dtrbrezdt(n1_loc) = dtrbrezdt(n1_loc) - disi*augment_weight(3)
          dtrbrezdt(n2_loc) = dtrbrezdt(n2_loc) + disi*augment_weight(3)

        else include_edge_gradients

!         just use Green-Gauss cell-average gradients

          trbrex = trbrexavg
          trbrey = trbreyavg
          trbrez = trbrezavg

          do i_local = 1, nodes_local
            i = node_map(i_local)
            dtrbrexdt(i) = dtrbrexavgdt(i)
            dtrbreydt(i) = dtrbreyavgdt(i)
            dtrbrezdt(i) = dtrbrezavgdt(i)
          end do

        end if include_edge_gradients

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re / sigma ]*
!       [normal gradient * area] at dual face

!       Note no positivity required since cb2 > -1

        ngradt = xmre_s*( trbrex*areax + trbrey*areay + trbrez*areaz )

        do i_local = 1, nodes_local
          i = node_map(i_local)
          dngradtdt(i) = xmre_s*(dtrbrexdt(i)*areax + dtrbreydt(i)*areay       &
                               + dtrbrezdt(i)*areaz)
        end do

! Assemble final Jacobian matrices into sparse matrix form

        factor = +my_1

        edge_node_loop : do ii = 1,2

!         diagonal contributions

          if (ii == 1) then
            i = n1_loc
            node = c2n(n1_loc,n)
            turbval = trbre_node(n1_loc)
          else
            i = n2_loc
            node = c2n(n2_loc,n)
            turbval = trbre_node(n2_loc)
          end if

          if ( use_bp_model ) then
            chi = turbval/rnu
              dchidt(:) = 0.0_dp
              dchidt(i) = 1.0_dp/rnu
            psi = chi
              dpsidt(:) = dchidt(:)
            if ( chi < 10.0_dp ) then
              base = 1.0_dp + exp(20.0_dp*chi)
                dbasedt(:) = exp(20.0_dp*chi)*20.0_dp*dchidt(:)
              psi = 0.05_dp * log(base)
                dpsidt(:) = 0.05_dp/base*dbasedt(:)
            endif
            turbval = psi*rnu
              dturbvaldt(:) = rnu*dpsidt(:)
          else
            dturbvaldt(:) = 0.0_dp
            dturbvaldt(i) = 1.0_dp
          endif

          factor = -my_1*factor

          local_contrib1 : if ( node <= nnodes0 ) then

            aterm = factor*((dphidt(i) - cb2*dturbvaldt(i))*ngradt             &
                  + (phi-cb2*turbval)*dngradtdt(i))

            if ( form_matrix ) then
              idiag = iau(node)
              aa(5,5,idiag) = aa(5,5,idiag) + aterm
            endif

            if ( form_matvec ) then
              do ifcn = 1, nfunctions
                res(5,node,ifcn) = res(5,node,ifcn)                            &
                                 + aterm*coltag(5,node)*rlam(5,node,ifcn)
              end do
            endif

          end if local_contrib1

!         off-diagonal contributions
!         (place into transposed block locations)

          node_loop_4 : do i_local = 1, nodes_local

            i = node_map(i_local)

            nodec = c2n(i,n)

            if (nodec == node) cycle node_loop_4   ! already did diagonal

            if ( nodec <= nnodes0 ) then

              aterm = factor*((dphidt(i) - cb2*dturbvaldt(i))*ngradt+          &
                      (phi-cb2*turbval)*dngradtdt(i))

              if ( form_matrix ) then

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == node) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                             node,' to the off diagonal of node ',nodec
                  call lmpi_die
                  stop
                end if

                aa(5,5,ioff) = aa(5,5,ioff) + aterm

              endif

              if ( form_matvec ) then
                do ifcn = 1, nfunctions
                  res(5,nodec,ifcn) = res(5,nodec,ifcn)                        &
                                    + aterm*coltag(5,node)*rlam(5,node,ifcn)
                end do
              endif

            end if

          end do node_loop_4

        end do edge_node_loop

      end do edge_loop

    end do cell_loop

  end subroutine turbpart_mixed_diffusioni

  include "aharten.f90"
  include "daharten.f90"
  include "edge_augment_weight.f90"

end module residual_turbparti

module dual_agglom_defs

  implicit none

  private

  public :: volume_weight_position

!....................(-1)
  public :: node
  public :: t

!....................(0)

  public :: adj_count
  public :: agg_mynparts
  public :: agglom_f2c_org

!....................(1)

  public :: agg_edge_f2c
  public :: l2g_dual
  public :: dual_owner
  public :: par_to_seq_dual_map
  public :: seq_to_par_dual_map
  public :: agg_istart, agg_iend, rev_dual_map

!....................(2)

  public :: agg_dual_nnodes0
  public :: agg_dual_nnodes
  public :: agg_dual_nnodesg

!....................(3)

  public :: eqn_group_line
  public :: corners,  ridges,  valleys
  public :: corners0, ridges0, valleys0
  public :: cornersg, ridgesg, valleysg

!....................(4)

!....................(5)


!....................(6)

  public :: agg_boundary_flag
  public :: agg_boundary_check

!....................(7)

  public :: constrain_lines
  public :: orphan_agglomeration
  public :: parents_minimum
  public :: parent_seed_debug
  public :: dual_prolong_debug

  public :: planar_boundaries
  public :: corner_debug
  public :: read_agglom_f2c
  public :: write_agglom_f2c
  public :: write_tecplot_agg_boundaries
  public :: write_tecplot_dual_agg

  public :: increase_nghbrs_on_valleys

  logical :: volume_weight_position = .true.

  type :: node
     integer,  dimension(:), pointer :: adj_list
  end type node
  type (node), dimension(:), pointer :: t

!....................(0)

  integer, dimension(:), allocatable :: adj_count

  integer, dimension(:), allocatable :: agg_mynparts

! Global array:
! Mapping global point (parent) to global child (agglomerate).
! Accessed locally using grid%l2g (local to global).
  integer, dimension(:), allocatable :: agglom_f2c_org

!....................(1)


  integer, dimension(:), allocatable :: agg_edge_f2c
  integer, dimension(:), allocatable :: l2g_dual
  integer, dimension(:), allocatable :: dual_owner
  integer, dimension(:), allocatable :: par_to_seq_dual_map
  integer, dimension(:), allocatable :: seq_to_par_dual_map
  integer, dimension(:), allocatable :: agg_istart, agg_iend, rev_dual_map

!....................(2)

  integer :: agg_dual_nnodes0 ! Number of duals owned by processor
  integer :: agg_dual_nnodes  ! Number of duals on processor
  integer :: agg_dual_nnodesg ! Sum of agg_dual_nnodes0

!....................(3)

! Parent grid information.

  integer :: eqn_group_line                ! >0 if agglomerating lines
  integer :: corners,  ridges,  valleys    !available on-processor values
  integer :: corners0, ridges0, valleys0   !   solved on-processor values
  integer :: cornersg, ridgesg, valleysg   !   global on-processor values


!....................(4)

!....................(5)


!....................(6)

! Agglomerated grid information.

  integer, dimension(:), allocatable :: agg_boundary_flag
  integer, dimension(:), allocatable :: agg_boundary_check

!....................(7)
! Parameters for dual_coarsening:

  logical :: constrain_lines = .false.

  integer :: orphan_agglomeration = 0 !0,1,2,3
  integer :: parents_minimum

  integer :: parent_seed_debug  = -1
  integer :: dual_prolong_debug = -1

  logical :: planar_boundaries  = .false.
  logical :: corner_debug       = .true.

  logical :: read_agglom_f2c  = .false. ! Read file 'project'.agglom_f2c
  logical :: write_agglom_f2c = .false. ! Write 'project'.agglom_f2c_output

!FIXME - capture in echo_nml somewhere...
!To generate tecplot files for agglomerated grids,
!set write_tecplot_agg_boundaries = T in &dual_agglom.
!Load the target grid, flatplate.545x385_grid_boundary_tec.dat
!(flatplate.545x385_grid_boundary_flag_tec.dat) into Tecplot,
!and then add to it flatplate.545x385.2_DualAgglom_1_grid_boundary_tec.dat,
!which is the agglomerated grid.
!For the agglomerated grid, don't show MESH,
!but show EDGE only with a different color and thickness

  logical :: write_tecplot_agg_boundaries = .false.
  logical :: write_tecplot_dual_agg       = .false.

  logical :: increase_nghbrs_on_valleys = .false.

end module dual_agglom_defs

!============================== ISWCH_COEF ===================================80
!
!  Computes the feature detection coefficient which is then used to compute
!  a flux switching and limiting coefficient depending on the value of behavior:
!
!  ------------------------------------------------------------------
!  | behavior | flux switching and limiting coefficient behavior    |
!  ------------------------------------------------------------------
!  |  = 10    | binary (on/off) flux switching                      |
!  ------------------------------------------------------------------
!  |  =  1    | continuous flux switching and limiting              |
!  ------------------------------------------------------------------
!
!=============================================================================80

  pure function iswch_coef(rx1, ry1, rz1, rx2, ry2, rz2,                       &
                           gradpx1, gradpy1, gradpz1,                          &
                           gradpx2, gradpy2, gradpz2,                          &
                           pressl, pressr, phip1, phip2,                       &
                           ubarl, ubarr, q2l, q2r, q2, a2,                     &
                           laplcc, power, behavior)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, pi

    use inviscid_flux,                only : rhs_u_eigenvalue_coef

    integer,  intent(in) :: behavior

    real(dp), intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp), intent(in) :: gradpx1, gradpy1, gradpz1,                         &
                            gradpx2, gradpy2, gradpz2,                         &
                            phip1, phip2
    real(dp), intent(in) :: pressl, pressr
    real(dp), intent(in) :: ubarl, ubarr, q2l, q2r
    real(dp), intent(in) :: q2, a2, laplcc, power

    real(dp)             :: iswch_coef

    real(dp) :: gradpl, gradpr, pgrad
    real(dp) :: dpx2, dpy2, dpz2, plapc, pcoef, prato
    real(dp) :: utngl, utngr, utot, dutng
    real(dp) :: machno, machf, iswch

    real(dp), parameter :: min_mach = 0.35_dp
    real(dp), parameter :: dmach    = 0.35_dp

  continue

!   Construct the normalized undivided pressure ratio

    prato  = abs(pressr - pressl) / min(pressl, pressr)

!   Construct the normalized magnitude of the undivided pressure gradient

    gradpl = sqrt(gradpx1**2+gradpy1**2+gradpz1**2) *                          &
             sqrt(rx1*rx1+ry1*ry1+rz1*rz1)

    gradpr = sqrt(gradpx2**2+gradpy2**2+gradpz2**2) *                          &
             sqrt(rx2*rx2+ry2*ry2+rz2*rz2)

    pgrad  = max(gradpl, gradpr) / min(pressl, pressr)

!   Construct a crude estimate of the magnitude of the
!   normalized undivided pressure Laplacian

    dpx2 = gradpx2*rx2 - gradpx1*rx1
    dpy2 = gradpy2*ry2 - gradpy1*ry1
    dpz2 = gradpz2*rz2 - gradpz1*rz1

    plapc = laplcc*sqrt(dpx2*dpx2+dpy2*dpy2+dpz2*dpz2) / min(pressl, pressr)

    pcoef = sqrt(max(pgrad, prato)**2 + plapc**2)

!   Compute deltaU(tangent) and U(normal) to compute the inviscid coeff.

    utngl  = sqrt(max(my_0, q2l-ubarl*ubarl))
    utngr  = sqrt(max(my_0, q2r-ubarr*ubarr))
    utot   = sqrt(q2)
    dutng  = min(my_1, abs(utngr-utngl)/(utot+0.000001_dp))

    iswch_coef = min(my_1-dutng, tanh(pcoef)**power)
    iswch_coef = min(my_1, max(my_0, max(iswch_coef, my_1-min(phip1, phip2))))

!   Force the switch to approach 0 in sub-sonic flow, thereby forcing
!   the anti-diffusion term to be active in regions of sub-sonic flow

    machno = sqrt(q2/a2)
    machf  = max(my_0, min(my_1, (machno-min_mach)/dmach))
    iswch  = my_1 - my_half*(cos(machf*pi)+my_1)
    iswch_coef = min(iswch, iswch_coef)

!   Compute the switching/dissipation/limiting coefficient
!   A) when behavior = 10 : compute the binary switch used to switch between the
!      dissipative and non-dissipative fluxes where:
!      iswch_coef = 0 : dissipative flux
!      iswch_coef = 1 : non-dissipative flux
!   or
!   B) when behavior = 01 : compute the continuous switching/limiting function

    if (behavior == 10) then
      if (iswch_coef <= my_1-rhs_u_eigenvalue_coef) then
        iswch_coef = my_1
      else
        iswch_coef = my_0
      end if
    else
      iswch_coef = my_1 - iswch_coef
    end if

  end function iswch_coef

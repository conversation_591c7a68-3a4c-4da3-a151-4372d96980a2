module line_algebraic_turb

  use kinddefs,         only : dp
  use line_types,       only : line_type
  use grid_types,       only : grid_type
  use comprow_types,    only : crow_flow

  implicit none

  private
  public :: algebraic_turb, set_algebraic_turb_lines

  type(line_type), dimension(:), allocatable :: algebraic_turb_lines

contains

!======================= SET_ALGEBRAIC_TURB_LINES ============================80
!
!  Set structured lines data.
!
!=============================================================================80
  subroutine set_algebraic_turb_lines( grid, crow )

    use info_depr,         only : ngrid
    use implicit_lines,    only : set_dof_lines, recast_lines,                 &
                                  read_lines_fmt_write_lines
    use lmpi,              only : lmpi_conditional_stop

    type(grid_type),      intent(in   ) :: grid
    type(crow_flow),      intent(in   ) :: crow

    integer :: n_lines, fl, ierr

    integer, dimension(:),   pointer :: endline
    integer, dimension(:,:), pointer :: line

    logical :: first_time_through = .true.
    character(80) :: flow_dir

  continue

    if ( first_time_through ) then
      allocate(algebraic_turb_lines(ngrid))
      first_time_through = .false.
      flow_dir = ''
      call read_lines_fmt_write_lines(1,flow_dir,grid%project,ierr)
      call lmpi_conditional_stop(ierr,"fmt_to_unfmt lines:line_algebraic_turb")
    endif

    call set_dof_lines( 1, '', grid%project,                                   &
                        line, endline, n_lines,                                &
                        grid%dof0, grid%l2g,                                   &
                        crow%ia, crow%ja, crow%ia_ns,                          &
                        grid%x,  grid%y,  grid%z, .true.  )

    fl = grid%igrid

    call recast_lines( n_lines, line, endline, algebraic_turb_lines(fl),       &
                       grid%y )

    deallocate(line)
    deallocate(endline)

  end subroutine set_algebraic_turb_lines

!============================= ALGEBRAIC_TURB ================================80
!
!  Drives algebraic turbulence models using implicit lines
!
!=============================================================================80

  subroutine algebraic_turb( soln, grid )

    use grid_types,              only : grid_type
    use solution_types,          only : soln_type
    use turb_gen,                only : baldwin_lomax, cebeci_smith
    use generic_gas_map,         only : baldwin_lomax_on, cebeci_smith_on
    use lmpi,                    only : lmpi_conditional_stop
    use bc_names,                only : bc_has_skin_friction

    type(grid_type), intent(inout)   :: grid
    type(soln_type), intent(inout)   :: soln

    real(dp), dimension(:), allocatable, save :: nx, ny, nz
    real(dp) :: den
    integer  :: line_count, line_length, pencil_length, fl
    integer  :: ib, i, line, node, nodel, n0
    logical, dimension(10), save       :: init = .true.
    logical, dimension(:), allocatable :: match

    fl            = grid%igrid

    line_count    = algebraic_turb_lines(fl)%n_lines
    pencil_length = algebraic_turb_lines(fl)%total_dofs
    line_length   = algebraic_turb_lines(fl)%first_entry(2)                    &
                  - algebraic_turb_lines(fl)%first_entry(1)
!   Logic assumes all line lengths are the same.
!   This may not be required in most general case
!   but retain this logic for LAURA functional equivalence tests

    if(init(fl))then
      allocate(nx(line_count), ny(line_count), nz(line_count))
      allocate(match(line_count))
      match = .false.
      nx = 0._dp
      ny = 0._dp
      nz = 0._dp
      do ib = 1,grid%nbound
        if(bc_has_skin_friction(grid%bc(ib)%ibc))then
          surface_loop: do i = 1,grid%bc(ib)%nbnode
            node = grid%bc(ib)%ibnode(i)
            if(node <= grid%nnodes0)then
              search_lines: do line = 1,line_count
                n0 = algebraic_turb_lines(fl)%first_entry(line)
                nodel = algebraic_turb_lines(fl)%line_to_dof_index(n0)
                if(nodel==node)then
                  nx(line) = nx(line) + grid%bc(ib)%bxn(i)
                  ny(line) = ny(line) + grid%bc(ib)%byn(i)
                  nz(line) = nz(line) + grid%bc(ib)%bzn(i)
                  match(line) = .true.
                  exit search_lines
                end if
                if(line == line_count)then
                  write(*,*)"No match found for node on surface. Stopping."
                  write(*,*)"surface node index = ",i," on surface ",ib
                  call lmpi_conditional_stop(1,                                &
                                             "Stop in line_algebraic_turb.f90")
                end if
              end do search_lines
            end if
          end do surface_loop
        end if
      end do
      do line = 1,line_count
        if(match(line))then
          den = sqrt(nx(line)**2 + ny(line)**2 + nz(line)**2)
          nx(line) = nx(line)/den
          ny(line) = ny(line)/den
          nz(line) = nz(line)/den
        else
          write(*,*)"No match found for line root on surface. Stopping."
          write(*,*)"Line root index = ",line
          call lmpi_conditional_stop(1,"Stop in line_algebraic_turb.f90")
        end if
      end do
      deallocate(match)
      init(fl) = .false.
    end if

    if(baldwin_lomax_on)then
      call baldwin_lomax(line_count, line_length, pencil_length,               &
                         algebraic_turb_lines(fl)%first_entry,                 &
                         algebraic_turb_lines(fl)%line_to_dof_index,           &
                         grid%x, grid%y, grid%z,                               &
                         nx, ny, nz,                                           &
                         grid%slen, soln%q_dof, grid%nnodes0)
    end if
    if(cebeci_smith_on)then
      call cebeci_smith(line_count, line_length, pencil_length,                &
                         algebraic_turb_lines(fl)%first_entry,                 &
                         algebraic_turb_lines(fl)%line_to_dof_index,           &
                         grid%x, grid%y, grid%z,                               &
                         nx, ny, nz,                                           &
                         grid%slen, soln%q_dof, grid%nnodes0)
    end if

  end subroutine algebraic_turb
end module line_algebraic_turb

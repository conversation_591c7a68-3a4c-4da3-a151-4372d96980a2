module fun3d_maximums

  implicit none

  private

  public :: max_dof_in_line
  public :: relaxation_steps
  public :: ngrid_max
  public :: max_geom, max_pnts
  public :: max_bounds
  public :: neq_max

  !...maximum dofs in implicit lines:
  integer, parameter :: max_dof_in_line = 1500

  !...relaxation schedules via namelists:
  integer, parameter :: relaxation_steps = 6

  !...maximum number of grids for multigrid:
  integer, parameter :: ngrid_max = 20

  !...sampling_solution namelist:
  integer, parameter :: max_geom = 100   ! max number of geometries
  integer, parameter :: max_pnts = 10000 ! max number of points

! maximum number of boundaries to integrate.
  integer, parameter  :: max_bounds = 10000

! maximum number of equations to be solved.
  integer, parameter :: neq_max = 20

end module fun3d_maximums

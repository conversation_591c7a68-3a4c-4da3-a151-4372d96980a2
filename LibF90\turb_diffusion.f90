module turb_diffusion

  use lmpi,            only : lmpi_conditional_stop
  use kinddefs,        only : dp
  use turb_parameters, only : t_diff1, t_diff2, t_diff3, t_diff4

  use turbulence_info, only : turbulence_model ! for diagnotics flag

  use turbulence_info, only : sa, des,         sa_neg,           des_neg,      &
                          menter_sst,          kw_sst,           kw_sst2003,   &
                          sst_v,               bsl,              sst,          &
                          sst_2003,            asbm_sst,         abid_linear,  &
                          wilcox1988,          wilcox1988_v,     wilcox_kw88,  &
                          wilcox_kw88p,        wilcox_kw98,      wilcox_asm,   &
                          easm_ddes,           EASMko2003_S,     wilcox_kw06,  &
                          kw_des,              wilcox_kw06p,     wilcox2006,   &
                          wilcox2006_v,        k_kL_MEAH2013,    hrles,        &
                          wilcox_les,          kw_lag,           gamma_ret_sst,&
                          WilcoxRS<PERSON>_w2006,     WilcoxRSM_w2006c,               &
                          SSGLRR_RSM_w2012_SD, SSGLRR_RSM_w2012, sst_kkl,      &
                          ras_2011

  implicit none

  private

  public :: get_diffusion_coefficients
  public :: get_local_coeff

  ! Generalized multi-equation turbulence model routines
  ! for element-based node-centered discretizations.
  ! (n_turb=1 variants with diff_edge_avg_t=2 exist elsewhere)
  public :: turb_resid_diff_element
  public :: turb_jacob_diff_element

  ! Driver routines to route between element-based and
  ! edge-based discretizations.
  public :: turb_resid_diff
  public :: turb_jacob_diff

  real(dp), parameter :: zero    = 0.0_dp

  real(dp), dimension(1) :: mu_t_les = 0.0_dp

   ! To check by evaluating diffusion term with constant rnu.
   logical, parameter :: constant_rnu_diffusion = .false.


contains

  pure function get_local_coeff ( turbulence_model_int, n_turb, diff_const &
                                ,  diff_const2, sst_f1 ) result ( coeff )

    use kinddefs,               only : dp
    use turb_hrles_const,       only : prt

    integer,                     intent(in) :: turbulence_model_int
    integer,                     intent(in) :: n_turb
    real(dp), dimension(n_turb), intent(in) :: diff_const
    real(dp), dimension(n_turb), intent(in) :: diff_const2
    real(dp),                    intent(in) :: sst_f1

    real(dp), dimension(n_turb)             :: coeff
    real(dp), parameter                     :: one = 1.0_dp

  continue

    coeff      = diff_const

    select case ( turbulence_model_int )
      case ( menter_sst, kw_sst, kw_sst2003, sst_v, bsl, sst, sst_2003, kw_des &
           )
        coeff(1)   = sst_f1*diff_const(1) + (one - sst_f1)*diff_const2(1)
        coeff(2)   = sst_f1*diff_const(2) + (one - sst_f1)*diff_const2(2)

      case ( asbm_sst)
        coeff(1)   = sst_f1*diff_const(1) + (one - sst_f1)*diff_const2(1)
        coeff(2)   = sst_f1*diff_const(2) + (one - sst_f1)*diff_const2(2)
        coeff(3)   = 10.0_dp

      case ( sst_kkl )
        coeff      = diff_const

      case ( abid_linear,                                                      &
             wilcox1988, wilcox1988_v, wilcox_kw88, wilcox_kw88p ,             &
             wilcox_kw98, wilcox_asm, easm_ddes, EASMko2003_S,                 &
             wilcox_kw06,                                                      &
             wilcox_kw06p, wilcox2006, wilcox2006_v, k_kL_MEAH2013 )
        coeff      = diff_const

      case ( hrles )
        coeff(1)   = sst_f1*diff_const(1) + (one - sst_f1)*prt
        coeff(2)   = sst_f1*diff_const(2) + (one - sst_f1)*diff_const2(2)

      case ( wilcox_les )
        coeff(1)   = sst_f1*diff_const(1) + (one - sst_f1)*prt
        coeff(2)   = diff_const(2)

      case ( kw_lag)
        coeff      = diff_const

      case ( gamma_ret_sst)
        coeff(1)   = sst_f1*diff_const(1) + (one - sst_f1)*diff_const2(1)
        coeff(2)   = sst_f1*diff_const(2) + (one - sst_f1)*diff_const2(2)
        coeff(3)   = diff_const(3)
        coeff(4)   = diff_const(4)

      case ( WilcoxRSM_w2006,WilcoxRSM_w2006c )
        coeff      = diff_const

      case ( SSGLRR_RSM_w2012_SD,SSGLRR_RSM_w2012 )
        coeff(:)   = sst_f1*diff_const(:) + (one - sst_f1)*diff_const2(:)

      case default

        coeff = 0.0_dp

    end select

  end function get_local_coeff

!======================= GET_DIFFUSION_COEFFICIENTS ==========================80
!
! Feed back the proper non-linear diffusion coefficients for
! any turbulence model
!
!=============================================================================80
  subroutine get_diffusion_coefficients( turbulence_model_int                  &
                          , n_turb, diff_const, diff_const2 )

    use kinddefs,               only : dp
    use turb_gammaretsst_const, only : sig_f, sig_thetat
    use turb_sa_const,          only : sig, cb2
    use turb_ke_const,          only : sig_k_ke, sig_e_ke
    use turb_kw_const,          only : sigma_k_w88, sigma_w_w88,               &
                                       sigma_k_w98, sigma_w_w98,               &
                                       sigma_k_w06, sigma_w_w06,               &
                                       sigma_k_lag, sigma_w_lag,               &
                                       sig_k1, sig_w1, sig_k2, sig_w2,         &
                                       sigma_k_kkl, sigma_phi_kkl,             &
                                       sigma_bsl_k1, sigma_bsl_k2,             &
                                       sigma_bsl_w1, sigma_bsl_w2
    use turb_rsm_const,         only : sigma_tij_rsm_w2006, sigma_w_rsm_w2006, &
                                       sigma_w_o, sigma_w_e, d_o, d_e,         &
                                       d_sd_o, d_sd_e

    integer,                     intent(in)  :: turbulence_model_int
    integer,                     intent(in)  :: n_turb
    real(dp), dimension(n_turb), intent(out) :: diff_const
    real(dp), dimension(n_turb), intent(out) :: diff_const2

    real(dp), parameter                     :: one = 1.0_dp

  continue

    diff_const  = 0.0_dp
    diff_const2 = 0.0_dp

    select case ( turbulence_model_int )

    case ( sa, des, sa_neg, des_neg )

       diff_const(1) = sig
      diff_const2(1) = cb2

    case ( ras_2011 )

       diff_const(1) = sig
      diff_const2(1) = cb2

    case ( abid_linear )

      diff_const(1) = sig_k_ke
      diff_const(2) = sig_e_ke

    case ( sst_kkl )

      diff_const(1) = sigma_k_kkl
      diff_const(2) = sigma_phi_kkl

    case ( wilcox1988, wilcox1988_v, wilcox_kw88, wilcox_kw88p )

      diff_const(1) = sigma_k_w88
      diff_const(2) = sigma_w_w88

    case ( wilcox_kw98, wilcox_asm, easm_ddes, EASMko2003_S )

      diff_const(1) = sigma_k_w98
      diff_const(2) = sigma_w_w98

    case ( kw_lag )

      diff_const(1) = sigma_k_lag
      diff_const(2) = sigma_w_lag
      diff_const(3) = 0.0_dp

    case ( wilcox_kw06 )

      diff_const(1) = sigma_k_w06
      diff_const(2) = sigma_w_w06

    case ( k_kL_MEAH2013 )

      diff_const(1) = sigma_k_kkl
      diff_const(2) = sigma_phi_kkl

    case ( wilcox_kw06p, wilcox2006, wilcox2006_v, wilcox_les )

      diff_const(1) = sigma_k_w06
      diff_const(2) = sigma_w_w06

    case ( menter_sst , kw_sst, kw_sst2003, sst_v                      &
         , sst, sst_2003, asbm_sst, kw_des )

       diff_const(1) = sig_k1
       diff_const(2) = sig_w1
      diff_const2(1) = sig_k2
      diff_const2(2) = sig_w2

    case ( bsl )

       diff_const(1) = sigma_bsl_k1
       diff_const(2) = sigma_bsl_w1
      diff_const2(1) = sigma_bsl_k2
      diff_const2(2) = sigma_bsl_w2

    case ( gamma_ret_sst )

       diff_const(1) = sig_k1
       diff_const(2) = sig_w1
      diff_const2(1) = sig_k2
      diff_const2(2) = sig_w2
       diff_const(3) = one/sig_f
       diff_const(4) = one/sig_thetat

    case ( WilcoxRSM_w2006,WilcoxRSM_w2006c )

      diff_const(1:6) = sigma_tij_rsm_w2006
      diff_const(7)   = sigma_w_rsm_w2006

    case ( SSGLRR_RSM_w2012_SD )

      diff_const(1:6) = d_sd_o
      diff_const(7)   = sigma_w_o
      diff_const2(1:6) = d_sd_e
      diff_const2(7)   = sigma_w_e

    case ( SSGLRR_RSM_w2012 )

      diff_const(1:6) = d_o
      diff_const(7)   = sigma_w_o
      diff_const2(1:6) = d_e
      diff_const2(7)   = sigma_w_e

    case default

      diff_const = 0.0_dp

    end select

  end subroutine get_diffusion_coefficients

!======================== TURB_RESID_DIFF_ELEMENT ============================80
!
! Element-based mixed element diffusion for turbulence.
!
!=============================================================================80
  subroutine turb_resid_diff_element ( n_sta, eqn_set, nnodes0, turb,          &
                                 qnode, res, ncell, c2n, x, y, z,              &
                                 local_f2n, local_e2n, local_f2e, e2n_2d,      &
                                 face_per_cell, node_per_cell,                 &
                                 edge_per_cell, type_cell, n_turb, face_2d,    &
                                 chk_norm, turbulence_model_int, amut,         &
                                 diff_const, diff_const2, sst_f1 )

    use kinddefs,        only : dp
    use info_depr,       only : tref, xmach, re, use_edge_gradients, twod      &
                              , skeleton, grad_x_y_z_contents
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_edge_per_cell
    use debug_defs,      only : gradient_construction_rhs
    use solution_types,  only : compressible, incompressible
    use debug_defs,      only : diff_edge_avg_t
    use turb_gammaretsst_const, only : transition_4eqn_on
    use turbulence_info, only : turbulence_model_is_conservative


    integer, intent(in) :: n_sta, eqn_set
    integer, intent(in) :: n_turb
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: face_2d

    integer, dimension(node_per_cell,ncell),  intent(in) :: c2n
    integer, dimension(face_per_cell,4),      intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),      intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),      intent(in) :: local_f2e
    integer, dimension(face_per_cell,face_per_cell),                           &
                                              intent(in) :: chk_norm
    integer, dimension(4,2),                  intent(in) :: e2n_2d

    real(dp), dimension(:),                intent(in)    :: x, y, z
    real(dp), dimension(:,:),              intent(in)    :: turb
    real(dp), dimension(:,:),              intent(in)    :: qnode
    real(dp), dimension(:,:),              intent(inout) :: res
    integer,                               intent(in)    :: turbulence_model_int
    real(dp), dimension(:),                intent(in)    :: amut

    character(len=3),                         intent(in) :: type_cell
    real(dp),           dimension(n_turb),    intent(in) :: diff_const
    real(dp),           dimension(n_turb),    intent(in) :: diff_const2
    real(dp), optional, dimension(:),         intent(in) :: sst_f1

    integer :: n, nn
    integer :: ie, i, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node
    integer :: n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_node_per_cell) :: global_node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: trbre, atrbre, tke, rhotke, omega, rhoomega
    real(dp) :: xmre, xmre_s
    real(dp) :: cb20, cb2s
    real(dp) :: turb_n1, turb_n2
    real(dp) :: phi_ngradt_n1, phi_ngradt_n2
    real(dp) :: cstar
    real(dp) :: rnu
    real(dp) :: rnu1, rnu2, rho1, rho2, aturb1, aturb2
    real(dp) :: fact, my_xmach
    real(dp) :: ex, ey, ez, disi, dai, xnf, ynf, znf
    real(dp) :: egradt, gradt_xi
    real(dp) :: mutc1, mutc2
    real(dp) :: phida_1, phida_2, phida_3
    real(dp) :: temperature

    real(dp), dimension(3) :: augment_weight, da, rc
    real(dp), dimension(3) :: rl, rr, rm

    real(dp), dimension(max_node_per_cell)        :: nu_node
    real(dp), dimension(max_node_per_cell)        :: rho
    real(dp), dimension(max_node_per_cell)        :: rhoinv
    real(dp), dimension(max_node_per_cell)        :: mu_node
    real(dp), dimension(max_node_per_cell,3)      :: rn
    real(dp), dimension(n_turb,max_node_per_cell) :: trbre_node
    real(dp), dimension(n_turb,max_edge_per_cell) :: phi_edge
    real(dp), dimension(n_turb)                   :: phi
    real(dp), dimension(n_turb)                   :: coeff
    real(dp), dimension(n_turb)                   :: coeff_n1
    real(dp), dimension(n_turb)                   :: coeff_n2

    real(dp), dimension(n_turb,3)                 :: grad_cell
    real(dp), dimension(n_turb)                   :: trbrex, trbrey, trbrez
    real(dp), dimension(n_turb)                   :: trbrexavg, trbreyavg
    real(dp), dimension(n_turb)                   :: trbrezavg, ngradt
    real(dp), dimension(n_turb)                   :: mut_node

    real(dp), parameter :: onethird = 1.0_dp/3.0_dp
    real(dp), parameter :: oneforth = 0.25_dp
    real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: one      = 1.0_dp

    logical :: edge_gradients, edge_averaging


  continue

    !For tets in 3D or prisms in 2D, edge gradients add no new info.

                                       edge_gradients = use_edge_gradients
    if (           type_cell == 'tet') edge_gradients = .false.
    if (twod .and. type_cell == 'prz') edge_gradients = .false.

                                                    edge_averaging = .false.
    if (                      diff_edge_avg_t >= 1 ) edge_averaging = .true.
    if ( edge_gradients .and. diff_edge_avg_t == 1 ) edge_averaging = .true.

    if ( skeleton > 0 ) then
      write(*,"(1x,a,a,a)") ' Cell-based residuals of &
      &turbulent diffusion...element=',type_cell
      write(*,*) ' gradx,... via ', trim(grad_x_y_z_contents)
    endif

    cstar = sutherland_constant / tref
    my_xmach = 0._dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1,'eqn_set:turb_resid_diff_element')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*diff_const2(1) ! t_diff3*cb2
    cb2s = t_diff4*diff_const2(1) ! t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre /  diff_const(1) ! xmre / sig

    nodes_local = 0

!   Set some loop indicies and local mapping arrays depending on whether we are
!   doing a 2D case or a 3D case

    edge_map = 0
    node_map = 0

    if ( twod ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

    diffusion_term_cell : do n = 1, ncell

      do i = 1, node_per_cell

        node = c2n(i,n)       ! global node number

        rn(i,1) = x(node)
        rn(i,2) = y(node)
        rn(i,3) = z(node)

      enddo

      rc(:) = element_center( node_per_cell, rn )

!     initialization

         rnu   = 0._dp
       trbre   = 0._dp
      atrbre   = 0._dp

      trbrex(:)       = 0._dp
      trbrey(:)       = 0._dp
      trbrez(:)       = 0._dp
      trbre_node(:,:) = 0._dp
      nu_node(:)      = 0._dp
      phi(:)          = 0._dp
      rho             = 0._dp
      rhoinv          = 0._dp

!     compute cell averages and set up some local solution arrays

      node_loop0 : do i_local = 1, nodes_local

        i               = node_map(i_local) ! local node number
        node            = c2n(i,n)          ! global node number
        global_node_map(i) = node           ! locally stored global node number

        if ( eqn_set == compressible ) then
          rho(i)      = qnode(1,node)
          rhoinv(i)   = one / qnode(1,node)
          temperature = gamma*qnode(5,node)*rhoinv(i)
          nu_node(i)  = viscosity_law( cstar, temperature ) * rhoinv(i)
        else
          rho        = one
          rhoinv     = one
          nu_node(i) = one
        end if

        mu_node(i)         = t_diff1 * rho(i) * nu_node(i)

        do nn = 1, n_turb
          trbre_node(nn,i) = turb(nn,node)
        end do

      end do node_loop0

      test_for_edge_averaging:  if ( edge_averaging ) then
!=============================================================================80
!     edge averaged
!=============================================================================80

        edge_loop_edge_averaging : do ie_local = 1, edges_local

!         local edge number
          ie = edge_map(ie_local)
!         local node numbers of edge endpoints
          n1_loc = local_e2n(ie,1)
          n2_loc = local_e2n(ie,2)
!       global node numbers of edge endpoints
          n1 = c2n(n1_loc,n)
          n2 = c2n(n2_loc,n)

          rho1 = rho(n1_loc)
          rho2 = rho(n2_loc)
          rnu1 = nu_node(n1_loc)
          rnu2 = nu_node(n2_loc)

          if ( constant_rnu_diffusion ) rnu1 = 1._dp
          if ( constant_rnu_diffusion ) rnu2 = 1._dp

          select case ( turbulence_model_int )
          case ( sa, des, sa_neg, des_neg )

              turb_n1 = turb(1,n1)
              turb_n2 = turb(1,n2)
               aturb1 = sa0_turb_abs( turb_n1, rnu1 )
               aturb2 = sa0_turb_abs( turb_n2, rnu2 )

                phi_edge(1,ie)  = t_diff1*0.5_dp*(  rnu1 +   rnu2)     &
                                + t_diff2*0.5_dp*(aturb1 + aturb2)     &
                                +    cb20*0.5_dp*( turb_n1 +  turb_n2)
          case ( ras_2011 )

              turb_n1 = turb(1,n1)
              turb_n2 = turb(1,n2)
               aturb1 = sa0_turb_abs( turb_n1, rnu1 )
               aturb2 = sa0_turb_abs( turb_n2, rnu2 )

                phi_edge(1,ie)  = t_diff1*0.5_dp*(  rnu1 +   rnu2)     &
                                + t_diff2*0.5_dp*(aturb1 + aturb2)     &
                                +    cb20*0.5_dp*( turb_n1 +  turb_n2)
           case default

           coeff_n1 = get_local_coeff ( turbulence_model_int, n_turb,          &
                                        diff_const,  diff_const2, sst_f1(n1) )
           coeff_n2 = get_local_coeff ( turbulence_model_int, n_turb,          &
                                        diff_const,  diff_const2, sst_f1(n2) )
            do nn = 1, n_turb
              mutc1 = amut(n1) /  coeff_n1(nn)
              mutc2 = amut(n2) /  coeff_n2(nn)

              phi_edge(nn,ie) = t_diff1*0.5_dp*( rho1 * rnu1  + rho2 * rnu2  ) &
                              + t_diff2*0.5_dp*(        mutc1 +        mutc2 )
              if ( turbulence_model_int == kw_lag ) then
                phi_edge(3,ie) = 0.0_dp
              else if ( turbulence_model_int == asbm_sst) then
                phi_edge(3,ie) = 1.0_dp
              else if ( turbulence_model_int == gamma_ret_sst ) then
                phi_edge(4,ie) = 0.5_dp                                        &
                             * ( t_diff1*( rho1 * rnu1  + rho2 * rnu2  )       &
                               + t_diff2*(     amut(n1) +     amut(n2) ) )     &
                               / coeff_n1(4)
              endif
            enddo
           end select

       end do edge_loop_edge_averaging

      else test_for_edge_averaging
!=============================================================================80
!     cell averaged
!=============================================================================80
      node_loop1 : do i_local = 1, nodes_local

!       local node number
        i = node_map(i_local)
!       global node number
        node = global_node_map(i) ! c2n(i,n)

        mut_node(1:n_turb) = t_diff2*amut(node)

        if ( n_turb > 1 )                                                      &
        coeff = get_local_coeff ( turbulence_model_int, n_turb, diff_const     &
                                ,  diff_const2, sst_f1(node) )

        if ( constant_rnu_diffusion ) nu_node(i) = 1._dp

          select case ( turbulence_model_int )
          case ( sa, des, sa_neg, des_neg )
               rnu =    rnu +                             nu_node(i)
             trbre =  trbre +               turb(1,node)
            atrbre = atrbre + sa0_turb_abs( turb(1,node), nu_node(i) )

          case ( ras_2011 )
               rnu =    rnu +                             nu_node(i)
             trbre =  trbre +               turb(1,node)
            atrbre = atrbre + sa0_turb_abs( turb(1,node), nu_node(i) )

          case ( menter_sst, kw_sst, kw_sst2003                                &
               , sst_v, sst, sst_2003, bsl, kw_des                             &
               , wilcox1988, wilcox1988_v,wilcox_kw88p                         &
               , asbm_sst, abid_linear, gamma_ret_sst )


          case ( wilcox_kw88                                                   &
               , wilcox_kw98, wilcox_asm, easm_ddes, EASMko2003_S )
            trbre_node(1,i) = turb(1,node) * rhoinv(i)
            trbre_node(2,i) = turb(2,node) * rhoinv(i)

          case ( wilcox_kw06 )
            trbre_node(1,i) = turb(1,node) * rhoinv(i)
            trbre_node(2,i) = turb(2,node) * rhoinv(i)
            mut_node(1)     = t_diff2 * rho(i)*turb(1,node)/turb(2,node)
            mut_node(2)     = t_diff2 * rho(i)*turb(1,node)/turb(2,node)

          case ( wilcox_kw06p, wilcox2006, wilcox2006_v)
            mut_node(1) = t_diff2 * rho(i)*turb(1,node)/turb(2,node)
            mut_node(2) = t_diff2 * rho(i)*turb(1,node)/turb(2,node)

          case ( wilcox_les)
            mut_node(1)= t_diff2*mu_t_les(node)

          case ( kw_lag)
            trbre_node(1,i) = t_diff2 * turb(1,node) * rhoinv(i)
            trbre_node(2,i) = t_diff2 * turb(2,node) * rhoinv(i)
            mut_node(1)     = amut(node) ! turb(3,node)
            mut_node(2)     = amut(node) ! turb(3,node)
            mut_node(3)     = zero

          case ( k_kL_MEAH2013, sst_kkl )
            trbre_node(1,i) = t_diff2 * turb(1,node)
            trbre_node(2,i) = t_diff2 * turb(2,node)

          case ( WilcoxRSM_w2006c )
            trbre_node(1:7,i) = turb(1:7,node) * rhoinv(i)
            rhotke            = -0.5_dp*(turb(1,node)+turb(2,node)+turb(3,node))
            rhoomega          = turb(7,node)
            mut_node(1:n_turb) = t_diff2 * rho(i)*rhotke/rhoomega

          case ( WilcoxRSM_w2006, SSGLRR_RSM_w2012_SD )
            trbre_node(1:7,i) = turb(1:7,node)
            tke               = -0.5_dp*(turb(1,node)+turb(2,node)+turb(3,node))
            omega             = turb(7,node)
            mut_node(1:n_turb) = t_diff2 * rho(i)*tke/omega

          case ( SSGLRR_RSM_w2012 )
            trbre_node(1:7,i) = turb(1:7,node)
            tke               = -0.5_dp*(turb(1,node)+turb(2,node)+turb(3,node))
            omega             = turb(7,node)
            ! note: mut_node(1:6) is not mu_t for Gen Gradient Diff (GGD)
            ! need negative for 1:6 because turb = -Rij
            mut_node(1:6) =-t_diff2 * rho(i)*turb(1:6,node)/            &
                            (0.09_dp*omega)
            mut_node(7)   = t_diff2 * rho(i)*tke/omega

        end select

        n_turb2:  if ( n_turb > 1 ) then

          select case ( turbulence_model_int )
          case ( gamma_ret_sst )
            phi(1) = phi(1) + mu_node(i) + mut_node(1) /coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2) /coeff(2)
            if (transition_4eqn_on) then
              phi(3) = phi(3) +  mu_node(i) + mut_node(3) /coeff(3)
              phi(4) = phi(4) + (mu_node(i) + mut_node(4))/coeff(4)
            endif

          case ( WilcoxRSM_w2006, WilcoxRSM_w2006c, SSGLRR_RSM_w2012_SD )
            phi(1:6) = phi(1:6) +  mu_node(i) + mut_node(1) /coeff(1)
            phi(7  ) = phi(7  ) +  mu_node(i) + mut_node(7) /coeff(7)

          case ( SSGLRR_RSM_w2012 )
            phi(1:3) = phi(1:3) +  mu_node(i) + mut_node(1:3) /coeff(1:3)
            phi(4:6) = phi(4:6)               + mut_node(4:6) /coeff(4:6)
            phi(7)   = phi(7)   +  mu_node(i) + mut_node(7)   /coeff(7)

          case ( kw_lag )
            phi(1) = phi(1) + mu_node(i) + mut_node(1) /coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2) /coeff(2)
            phi(3) = 0.0_dp

          case ( asbm_sst )
            phi(1) = phi(1) + mu_node(i) + mut_node(1) /coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2) /coeff(2)
            phi(3) = phi(3) + 1.0_dp

          case default
            phi(1) = phi(1) + mu_node(i) + mut_node(1) /coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2) /coeff(2)

          end select

        end if n_turb2

      end do node_loop1

!     get cell averages by dividing by the number of nodes that contributed

      fact = 1._dp / real(nodes_local, dp)

        select case ( turbulence_model_int )
          case ( sa, des, sa_neg, des_neg )
                nn = n_turb
               rnu =    rnu*fact
             trbre =  trbre*fact
            atrbre = atrbre*fact

            phi(nn) = t_diff1*   rnu &
                    + t_diff2*atrbre &
                    +    cb20* trbre

          case ( ras_2011 )
                nn = n_turb
               rnu =    rnu*fact
             trbre =  trbre*fact
            atrbre = atrbre*fact

            phi(nn) = t_diff1*   rnu &
                    + t_diff2*atrbre &
                    +    cb20* trbre

          case default

            do nn = 1, n_turb
              phi(nn) = phi(nn)*fact
            end do

        end select

      endif test_for_edge_averaging
!=============================================================================80
!     end averaging logic
!=============================================================================80
!     get the gradients in the primal cell via Green-Gauss

      grad_cell(:,:) = element_grad( edges_local, max_node_per_cell,           &
                                    face_per_cell, rn, n_turb, trbre_node,     &
                                    local_f2n, e2n_2d, chk_norm )

      do nn = 1, n_turb
        trbrexavg(nn) = grad_cell(nn,1)
        trbreyavg(nn) = grad_cell(nn,2)
        trbrezavg(nn) = grad_cell(nn,3)
      end do
!-----------------------------------------------------------------------------80

!     next loop over the edges in the cell and get each ones
!     contribution to the residual

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

!       global node numbers of edge endpoints

        n1 = global_node_map(n1_loc)
        n2 = global_node_map(n2_loc)

        ! Left face centroid.

        if ( n4_loc == 0 ) then

          ! tria cell face.

          rl(1) = (rn(n1_loc,1) + rn(n2_loc,1) + rn(n3_loc,1))*onethird
          rl(2) = (rn(n1_loc,2) + rn(n2_loc,2) + rn(n3_loc,2))*onethird
          rl(3) = (rn(n1_loc,3) + rn(n2_loc,3) + rn(n3_loc,3))*onethird

        else

          ! quad cell face.

          rl(1) = (rn(n1_loc,1) + rn(n2_loc,1)                                 &
                 + rn(n3_loc,1) + rn(n4_loc,1))*oneforth
          rl(2) = (rn(n1_loc,2) + rn(n2_loc,2)                                 &
                 + rn(n3_loc,2) + rn(n4_loc,2))*oneforth
          rl(3) = (rn(n1_loc,3) + rn(n2_loc,3)                                 &
                 + rn(n3_loc,3) + rn(n4_loc,3))*oneforth

        end if

        ! Right face centroid.

        if (n6_loc == 0) then

          ! tria cell face.

          rr(1) = (rn(n1_loc,1) + rn(n2_loc,1) + rn(n5_loc,1))*onethird
          rr(2) = (rn(n1_loc,2) + rn(n2_loc,2) + rn(n5_loc,2))*onethird
          rr(3) = (rn(n1_loc,3) + rn(n2_loc,3) + rn(n5_loc,3))*onethird

        else

          ! quad cell face.

          rr(1) = (rn(n1_loc,1) + rn(n2_loc,1)                                 &
                 + rn(n5_loc,1) + rn(n6_loc,1))*oneforth
          rr(2) = (rn(n1_loc,2) + rn(n2_loc,2)                                 &
                 + rn(n5_loc,2) + rn(n6_loc,2))*oneforth
          rr(3) = (rn(n1_loc,3) + rn(n2_loc,3)                                 &
                 + rn(n5_loc,3) + rn(n6_loc,3))*oneforth

        end if

        ! Edge midpoint.

        rm(1) = (rn(n1_loc,1) + rn(n2_loc,1))*half
        rm(2) = (rn(n1_loc,2) + rn(n2_loc,2))*half
        rm(3) = (rn(n1_loc,3) + rn(n2_loc,3))*half

! contributions to dual normals from the two triangles
! that form part of the dual-cell surface

! area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        da(1) = ((rc(2)-rm(2))*(rl(3)-rr(3)) -                          &
                              (rc(3)-rm(3))*(rl(2)-rr(2)))*half
        da(2) = ((rc(3)-rm(3))*(rl(1)-rr(1)) -                          &
                              (rc(1)-rm(1))*(rl(3)-rr(3)))*half
        da(3) = ((rc(1)-rm(1))*(rl(2)-rr(2)) -                          &
                              (rc(2)-rm(2))*(rl(1)-rr(1)))*half

!       da(:) = element_edge_da( max_node_per_cell, edge_per_cell,             &
!                                ie, local_e2n, rn, rc )

!       get gradients at the dual face; either take gradients for this
!       piece of the dual face to be the same as the cell-average gradient
!       computed above  (which is what the legacy FUN3D solver does for tets),
!       or combine with the edge-gradient to increase h-ellipticity on
!       non-simplicial meshes.

        include_edge_gradients : if ( edge_gradients ) then

          ! ex, ey, ez is unit vector along edge direction.

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          dai = 1._dp/sqrt( da(1)**2 + da(2)**2 + da(3)**2 )
          xnf = da(1)*dai
          ynf = da(2)*dai
          znf = da(3)*dai

          augment_weight = edge_augment_weight( gradient_construction_rhs, &
                                                ex, ey, ez, xnf, ynf, znf )

          do nn = 1, n_turb

            ! directional gradients along edge

            if ( turbulence_model_is_conservative (turbulence_model_int) ) then
              egradt = ( turb(nn,n2)*rhoinv(n2_loc)                            &
                       - turb(nn,n1)*rhoinv(n1_loc) )*disi
            else
              egradt = ( turb(nn,n2) - turb(nn,n1) )*disi
            endif
            !  average Green-Gauss gradient in edge direction

            gradt_xi = trbrexavg(nn)*ex + trbreyavg(nn)*ey + trbrezavg(nn)*ez

            trbrex(nn) = trbrexavg(nn) + ( egradt - gradt_xi )*augment_weight(1)
            trbrey(nn) = trbreyavg(nn) + ( egradt - gradt_xi )*augment_weight(2)
            trbrez(nn) = trbrezavg(nn) + ( egradt - gradt_xi )*augment_weight(3)

          enddo

        else include_edge_gradients

          ! just use Green-Gauss cell-average gradients (this
          ! is what the baseline code does for tets)

          do nn = 1, n_turb

            trbrex(nn) = trbrexavg(nn)
            trbrey(nn) = trbreyavg(nn)
            trbrez(nn) = trbrezavg(nn)

          enddo

        end if include_edge_gradients

!       turbulent diffusion contribution at dual face

!       [nondimensionalization factor : Mach / Re / sigma ]*
!       [normal gradient * area] at dual face

        if (turbulence_model_int == SSGLRR_RSM_w2012 ) then
          turb_loop1c: do nn = 1, 6
            ngradt(nn)  = zero
          end do turb_loop1c
          phida_1 = xmre * (phi(1)*da(1) + phi(4)*da(2) + phi(5)*da(3))
          phida_2 = xmre * (phi(4)*da(1) + phi(2)*da(2) + phi(6)*da(3))
          phida_3 = xmre * (phi(5)*da(1) + phi(6)*da(2) + phi(3)*da(3))
          ngradt(1:6)= zero
          ngradt(7)  = xmre                                                    &
                     * (trbrex(7)*da(1) + trbrey(7)*da(2) + trbrez(7)*da(3))
        else
          turb_loop1d: do nn = 1, n_turb
            ngradt(nn) = xmre                                                  &
                     * (trbrex(nn)*da(1) + trbrey(nn)*da(2) + trbrez(nn)*da(3))
          end do turb_loop1d
          phida_1 = zero
          phida_2 = zero
          phida_3 = zero
        end if
!*****************************************************************************80

          rho1 = rho(n1_loc)
          rho2 = rho(n2_loc)
          rnu1 = nu_node(n1_loc)
          rnu2 = nu_node(n2_loc)

          if ( constant_rnu_diffusion ) rnu1 = 1._dp
          if ( constant_rnu_diffusion ) rnu2 = 1._dp

          select case ( turbulence_model_int )

            case ( sa, des, sa_neg, des_neg )

              turb_n1 = turb(1,n1)
              turb_n2 = turb(1,n2)
!             Note no positivity required since cb2 > -1
              ngradt(1) = xmre_s*( trbrex(1)*da(1) + trbrey(1)*da(2) &
                                 + trbrez(1)*da(3) )

              if ( edge_averaging ) then
                phi(1) = phi_edge(1,ie)
              endif

              phi_ngradt_n1 = (phi(1) - cb2s*turb_n1) * ngradt(1)
              phi_ngradt_n2 = (phi(1) - cb2s*turb_n2) * ngradt(1)

            if ( n1 <= nnodes0 ) then
              res(n_sta,n1) = res(n_sta,n1) - phi_ngradt_n1
            end if

            if ( n2 <= nnodes0 ) then
              res(n_sta,n2) = res(n_sta,n2) + phi_ngradt_n2
            end if

            case ( ras_2011 )

              turb_n1 = turb(1,n1)
              turb_n2 = turb(1,n2)
!             Note no positivity required since cb2 > -1
              ngradt(1) = xmre_s*( trbrex(1)*da(1) + trbrey(1)*da(2) &
                                 + trbrez(1)*da(3) )

              if ( edge_averaging ) then
                phi(1) = phi_edge(1,ie)
              endif

              phi_ngradt_n1 = (phi(1) - cb2s*turb_n1) * ngradt(1)
              phi_ngradt_n2 = (phi(1) - cb2s*turb_n2) * ngradt(1)

            if ( n1 <= nnodes0 ) then
              res(n_sta,n1) = res(n_sta,n1) - phi_ngradt_n1
            end if

            if ( n2 <= nnodes0 ) then
              res(n_sta,n2) = res(n_sta,n2) + phi_ngradt_n2
            end if

!-----------------------------------------------------------------------------80
          case default
            if ( edge_averaging ) then
              phi(:) = phi_edge(:,ie)
            endif
            conservative_formulation : if ( &
              turbulence_model_is_conservative (turbulence_model_int) ) then
              if ( n1 <= nnodes0 ) then
                do nn = 1, n_turb
                  res(nn,n1) = res(nn,n1) - ( phi(nn)*ngradt(nn) )
                end do
              end if
              if ( n2 <= nnodes0 ) then
                do nn = 1, n_turb
                  res(nn,n2) = res(nn,n2) + ( phi(nn)*ngradt(nn) )
                end do
              end if
            else
!-----------------------------------------------------------------------------80
              if (turbulence_model_int == SSGLRR_RSM_w2012 ) then

              if ( n1 <= nnodes0 ) then
                do nn = 1, 6
                  res(nn,n1) = res(nn,n1) - (( phida_1*trbrex(nn) +   &
                                               phida_2*trbrey(nn) +   &
                                               phida_3*trbrez(nn))    &
                                              * rhoinv(n1_loc))
                end do
                res(7,n1) = res(7,n1) - ( phi(7)*ngradt(7)*rhoinv(n1_loc) )
              end if
              if ( n2 <= nnodes0 ) then
                do nn = 1, 6
                  res(nn,n2) = res(nn,n2) + (( phida_1*trbrex(nn) +   &
                                               phida_2*trbrey(nn) +   &
                                               phida_3*trbrez(nn))    &
                                              * rhoinv(n2_loc))
                end do
                res(7,n2) = res(7,n2) + ( phi(7)*ngradt(7)*rhoinv(n2_loc) )
              end if

              else

              if ( n1 <= nnodes0 ) then
                do nn = 1, n_turb
                  res(nn,n1) = res(nn,n1)                                      &
                             - ( phi(nn)*ngradt(nn)*rhoinv(n1_loc) )
                end do
              end if
              if ( n2 <= nnodes0 ) then
                do nn = 1, n_turb
                  res(nn,n2) = res(nn,n2)                                     &
                             + ( phi(nn)*ngradt(nn)*rhoinv(n2_loc) )
                end do
              end if

              end if
            end if conservative_formulation
          end select

!*****************************************************************************80

      end do edge_loop

    end do diffusion_term_cell

  end subroutine turb_resid_diff_element

!========================= TURB_JACOB_DIFF_ELEMENT ===========================80
!
! Diffusion LHS for mixed element formulation on element basis
!
! Diffusion jacobians for turbulence models on
! mixed-element grids via cell-based integration
!
!=============================================================================80
  subroutine turb_jacob_diff_element ( n_sta, eqn_set, nnodes0,                &
!                                nnodes01, max_nnz, nnz01, n_tot,              &
                                 turb, qnode, a_diag, a_off,                   &
                                 ncell, c2n, x, y, z,                          &
                                 type_cell, local_f2n, local_e2n, local_f2e,   &
                                 e2n_2d, face_per_cell, node_per_cell,         &
                                 edge_per_cell, ia, ja, n_turb,                &
                                 face_2d, chk_norm, nzg2m, g2m,                &
                                 turbulence_model_int, amut,                   &
                                 diff_const, diff_const2, sst_f1 )

    use kinddefs,        only : dp, odp
    use info_depr,       only : tref, xmach, re, use_edge_gradients, twod      &
                              , tightly_couple
    use turbulence_info, only : turbulence_model_is_conservative
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use fluid,           only : gamma, sutherland_constant
    use element_defs,    only : max_node_per_cell, max_edge_per_cell
    use debug_defs,      only : gradient_construction_lhs
    use solution_types,  only : compressible, incompressible
    use debug_defs,      only : diff_edge_avg_t

    use turb_gammaretsst_const, only : transition_4eqn_on


    integer, intent(in) :: n_sta, eqn_set
    integer, intent(in) :: n_turb
!   integer, intent(in) :: max_nnz
!   integer, intent(in) :: nnz01
!   integer, intent(in) :: n_tot
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0
!   integer, intent(in) :: nnodes01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(face_per_cell,face_per_cell),               &
                                             intent(in) :: chk_norm
    integer, dimension(4,2),                 intent(in) :: e2n_2d
!   integer, dimension(nnodes01+1),              intent(in)   :: ia
!   integer, dimension(nnz01),                   intent(in)   :: ja
!   real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
!   real(dp),  dimension(nnodes01),              intent(in)    :: amut
!   real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
!   real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
!   real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
!   real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off
    integer, dimension(:),       intent(in)    :: ia
    integer, dimension(:),       intent(in)    :: ja
    integer, dimension(:),       intent(in)    :: g2m
    integer, dimension(:),       intent(in)    :: nzg2m
    real(dp),  dimension(:),     intent(in)    :: x, y, z
    real(dp),  dimension(:),     intent(in)    :: amut
    real(dp),  dimension(:,:),   intent(in)    :: qnode
    real(dp),  dimension(:,:),   intent(in)    :: turb
    real(dp),  dimension(:,:,:), intent(inout) :: a_diag
    real(odp), dimension(:,:,:), intent(inout) :: a_off

    character(len=3),                         intent(in) :: type_cell
    integer,                                  intent(in) :: turbulence_model_int
    real(dp),            dimension(n_turb),   intent(in) :: diff_const
    real(dp),            dimension(n_turb),   intent(in) :: diff_const2
    real(dp),  optional, dimension(:), intent(in) :: sst_f1

    integer :: n, nn, row, jj, nc
    integer :: ie, i, j, ii, ie_local, i_local, j_local, ioff
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node, nodec
    integer :: n3_loc, n4_loc, n5_loc, n6_loc
    integer :: n1, n2
    integer :: k, column
    integer :: n_turb_use

    integer, dimension(max_node_per_cell) :: node_map
    integer, dimension(max_node_per_cell) :: global_node_map
    integer, dimension(max_edge_per_cell) :: edge_map

    real(dp) :: terma, termb, termc, ngradt, trbre, atrbre
    real(dp) :: xmre, xmre_s, cb20, cb2s
    real(dp) :: rnu1, rnu2, rho1, rho2, aturb1, aturb2, turb1, turb2
    real(dp) :: cstar, rnu
    real(dp) :: cell_avg_factor, term_avg
    real(dp) :: factor, my_xmach
    real(dp) :: ex, ey, ez, disi, dai, xnf, ynf, znf
    real(dp) :: dgradt_xi
    real(dp) :: termd, dphidnu
    real(dp) :: mutc1, mutc2
    real(dp) :: tke, rhotke, omega, rhoomega
    real(dp) :: dphida_1,dphida_2,dphida_3
    real(dp) :: temperature


    real(dp), dimension(3) :: augment_weight, da, rc
    real(dp), dimension(5) :: dqdnu

    real(dp), dimension(n_turb,max_node_per_cell) ::   trbre_cn
    real(dp), dimension(n_turb,max_node_per_cell) ::  atrbre_cn
    real(dp), dimension(n_turb,max_node_per_cell) :: datrbre_cn
    real(dp), dimension(n_turb,max_node_per_cell) :: dphidturb

    real(dp), dimension(max_node_per_cell,3)      :: rn

    real(dp), dimension(max_node_per_cell,3) :: dgrad_celldq
    real(dp), dimension(max_node_per_cell) :: dtrbrex, dtrbrey, dtrbrez
    real(dp), dimension(max_node_per_cell) :: dtrbrexavg, dtrbreyavg
    real(dp), dimension(max_node_per_cell) :: dtrbrezavg, dngradt
    real(dp), dimension(max_node_per_cell) :: nu_node
    real(dp), dimension(max_node_per_cell) :: rho
    real(dp), dimension(max_node_per_cell) :: rhoinv
    real(dp), dimension(max_node_per_cell) :: inv_rho_term

    real(dp), dimension(n_turb)                   :: phi
    real(dp), dimension(n_turb,max_edge_per_cell) :: phi_edge
    real(dp), dimension(n_turb)                   :: coeff
    real(dp), dimension(n_turb)                   :: coeff_n1
    real(dp), dimension(n_turb)                   :: coeff_n2
    real(dp), dimension(n_turb)                   :: mut_node
    real(dp), dimension(max_node_per_cell)        :: mu_node

    real(dp), dimension(3) :: rl, rr, rm
    logical :: edge_gradients, edge_averaging

    real(dp), parameter :: zero     = 0.0_dp
    real(dp), parameter :: onethird = 1.0_dp/3.0_dp
    real(dp), parameter :: oneforth = 0.25_dp
    real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: one      = 1.0_dp

    real(dp), dimension(n_turb,node_per_cell,node_per_cell) :: a
    real(dp), dimension(1:5   ,node_per_cell,node_per_cell) :: af

  continue


    !For tets in 3D or prisms in 2D, edge gradients add no new info.

                                       edge_gradients = use_edge_gradients
    if (           type_cell == 'tet') edge_gradients = .false.
    if (twod .and. type_cell == 'prz') edge_gradients = .false.

                                                    edge_averaging = .false.
    if (                      diff_edge_avg_t >= 1 ) edge_averaging = .true.
    if ( edge_gradients .and. diff_edge_avg_t == 1 ) edge_averaging = .true.

                                   n_turb_use = n_turb
    if (turbulence_model_int==gamma_ret_sst .and.   &
        .not.transition_4eqn_on ) n_turb_use = 2

    nn    = 0
    terma = zero
    termb = zero
    termc = zero

    cstar = sutherland_constant / tref
    my_xmach = zero

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1,'eqn_set:turb_jacob_diff_element')
    end select

    !...to characterize four types of turbulent diffusion.
    cb20 = t_diff3*diff_const2(1) ! t_diff3*cb2
    cb2s = t_diff4*diff_const2(1) ! t_diff4*cb2

    xmre   = my_xmach / re
    xmre_s = xmre /  diff_const(1) ! xmre / sig

    nodes_local = 0

    ! Set some loop indicies and local mapping arrays depending
    ! on whether we are doing a 2D case or a 3D case

    edge_map     = 0
    node_map     = 0

    if ( twod ) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

    diffusion_term_cell : do n = 1, ncell

      a  = 0._dp
      af = 0._dp

      do i = 1, node_per_cell

        node = c2n(i,n)       ! global node number

        rn(i,1) = x(node)
        rn(i,2) = y(node)
        rn(i,3) = z(node)

      enddo

      rc(:) = element_center( node_per_cell, rn )

      ! initialization

      dtrbrex(:) = zero
      dtrbrey(:) = zero
      dtrbrez(:) = zero

        trbre_cn(:,:) = zero
       atrbre_cn(:,:) = zero
      datrbre_cn(:,:) = zero

      nu_node(:)      = zero
      dtrbrexavg(:) = zero
      dtrbreyavg(:) = zero
      dtrbrezavg(:) = zero
      phi           = zero
      rho           = zero
      rhoinv        = zero
      inv_rho_term  = one

!     compute cell averages and set up some local solution arrays

      node_loop : do i_local = 1, nodes_local

        i              = node_map(i_local) ! local node number
        node           = c2n(i,n)       ! global node number
        global_node_map(i) = node      ! locally stored global node number

        if ( eqn_set == compressible ) then
          rho(i)          = qnode(1,node)
          rhoinv(i)       = one / qnode(1,node)
          inv_rho_term(i) = rhoinv(i)
          temperature     = gamma*qnode(5,node)*rhoinv(i)
          nu_node(i)      = viscosity_law( cstar, temperature ) * rhoinv(i)
        else
          rho(i)     = one
          rhoinv(i)  = one
          nu_node(i) = one
        end if

        mu_node(i)         = t_diff1 * rho(i) * nu_node(i)
        mut_node(1:n_turb) = t_diff2 * amut(node)


        if ( n_turb > 1 )                                                      &
        coeff = get_local_coeff ( turbulence_model_int, n_turb, diff_const     &
                                ,  diff_const2, sst_f1(node) )

        select case ( turbulence_model_int )
        case ( sa, des, sa_neg, des_neg )
            trbre_cn(1,i) =                turb(1,node)
           atrbre_cn(1,i) =  sa0_turb_abs( turb(1,node), nu_node(i) )
          datrbre_cn(1,i) = dsa1_turb_abs( turb(1,node), nu_node(i) )

        case ( ras_2011 )
            trbre_cn(1,i) =                turb(1,node)
           atrbre_cn(1,i) =  sa0_turb_abs( turb(1,node), nu_node(i) )
          datrbre_cn(1,i) = dsa1_turb_abs( turb(1,node), nu_node(i) )

          ! these models do not locally redefine the eddy viscosity this
          ! is used in the non-linear diffusion term
          case ( menter_sst, kw_sst, kw_sst2003                                &
               , sst_v, sst, sst_2003, bsl, kw_des                             &
               , asbm_sst, abid_linear                                         &
               , wilcox1988, wilcox1988_v, wilcox_kw88p, wilcox_kw88           &
               , wilcox_kw98, wilcox_asm, easm_ddes, EASMko2003_S, sst_kkl)

          ! these models locally redefine the eddy viscosity to be used
          ! in the non-linear diffusion term
          case ( wilcox_kw06                                      &
               , wilcox_kw06p, wilcox2006, wilcox2006_v)
            mut_node(1) = t_diff2*rho(i)*turb(1,node)/turb(2,node)
            mut_node(2) = t_diff2*rho(i)*turb(1,node)/turb(2,node)

          case ( wilcox_les)
            mut_node(1)= t_diff2*mu_t_les(node)

          case ( kw_lag)
            mut_node(1:n_turb) = t_diff2 * amut(node)

          case ( k_kL_MEAH2013 )

          case ( gamma_ret_sst)

          case ( WilcoxRSM_w2006c )
            ! turb(1:6) is defined \rho (-u_i'u_j')
            rhotke      = -0.5_dp*(turb(1,node)+turb(2,node)+turb(3,node))
            rhoomega    = turb(7,node)
            mut_node(1:n_turb) = t_diff2 *rho(i)*rhotke/rhoomega

          case ( WilcoxRSM_w2006, SSGLRR_RSM_w2012_SD )
            ! turb(1:6) is defined (-u_i'u_j')
            tke         = -0.5_dp*(turb(1,node)+turb(2,node)+turb(3,node))
            omega       = turb(7,node)
            mut_node(1:n_turb) = t_diff2 *rho(i)* tke/omega

          case ( SSGLRR_RSM_w2012 )
            tke         = -0.5_dp*(turb(1,node)+turb(2,node)+turb(3,node))
            omega       = turb(7,node)
            ! note: mut_node(1:6) is not mu_t for Gen Gradient Diff (GGD)
            ! need negative for 1:6 because turb = -Rij
            mut_node(1:6) =-t_diff2 * rho(i)*turb(1:6,node)/(0.09_dp*omega)
            mut_node(7)   = t_diff2 * rho(i)*tke/omega

        end select

        n_turb2:  if ( n_turb > 1 ) then

          if ( turbulence_model_int == kw_lag) then
            phi(1) = phi(1) + mu_node(i) + mut_node(1)/coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2)/coeff(2)
            phi(3) = zero

          else if ( turbulence_model_int == asbm_sst) then
            phi(1) = phi(1) + mu_node(i) + mut_node(1)/coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2)/coeff(2)
            phi(3) = phi(3) + 1.0_dp

          else if ( turbulence_model_int == gamma_ret_sst ) then
            phi(1) = phi(1) + mu_node(i) + mut_node(1)/coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2)/coeff(2)
            if ( n_turb_use == n_turb ) then
              phi(3) = phi(3) +  mu_node(i) + mut_node(3) /coeff(3)
              phi(4) = phi(4) + (mu_node(i) + mut_node(4))/coeff(4)
            endif

          else if ( turbulence_model_int == WilcoxRSM_w2006 .or.               &
                    turbulence_model_int == WilcoxRSM_w2006c.or.               &
                    turbulence_model_int == SSGLRR_RSM_w2012_SD ) then
            phi(1:6) = phi(1:6) +  mu_node(i) + mut_node(1)/coeff(1)
            phi(7  ) = phi(7  ) +  mu_node(i) + mut_node(7)/coeff(7)

          else if ( turbulence_model_int == SSGLRR_RSM_w2012 ) then
            phi(1:3) = phi(1:3) +  mu_node(i) + mut_node(1:3) /coeff(1:3)
            phi(4:6) = phi(4:6)               + mut_node(4:6) /coeff(4:6)
            phi(7)   = phi(7)   +  mu_node(i) + mut_node(7)   /coeff(7)

          else
            phi(1) = phi(1) + mu_node(i) + mut_node(1)/coeff(1)
            phi(2) = phi(2) + mu_node(i) + mut_node(2)/coeff(2)

          end if

        end if n_turb2

      end do node_loop

      if ( constant_rnu_diffusion ) nu_node = one

      if ( turbulence_model_is_conservative(turbulence_model_int) ) &
           inv_rho_term = one

      ! averages over contributing nodes

      cell_avg_factor = 1._dp / real(nodes_local, dp)

      turbulence_model2:  select case ( turbulence_model_int )
      case ( sa, des, sa_neg, des_neg )
            nn = 1
           rnu = 0._dp
         trbre = 0._dp
        atrbre = 0._dp
        do i_local=1,nodes_local

          i = node_map(i_local) ! local node number

             rnu =    rnu +     nu_node(i)
           trbre =  trbre +  trbre_cn(1,i)
          atrbre = atrbre + atrbre_cn(1,i)

        enddo
           rnu =    rnu*cell_avg_factor
         trbre =  trbre*cell_avg_factor
        atrbre = atrbre*cell_avg_factor

        phi(nn)   = t_diff1*   rnu &
                  + t_diff2*atrbre &
                  +    cb20* trbre

      case ( ras_2011 )
            nn = 1
           rnu = 0._dp
         trbre = 0._dp
        atrbre = 0._dp
        do i_local=1,nodes_local

          i = node_map(i_local) ! local node number

             rnu =    rnu +     nu_node(i)
           trbre =  trbre +  trbre_cn(1,i)
          atrbre = atrbre + atrbre_cn(1,i)

        enddo
           rnu =    rnu*cell_avg_factor
         trbre =  trbre*cell_avg_factor
        atrbre = atrbre*cell_avg_factor

        phi(nn)   = t_diff1*   rnu &
                  + t_diff2*atrbre &
                  +    cb20* trbre

      case default

        do nn = 1, n_turb_use
          phi(nn) = phi(nn)*cell_avg_factor
        end do

      end select turbulence_model2

      term_avg = cell_avg_factor
      if ( edge_averaging ) term_avg = 0.5_dp
      dphidnu = t_diff1*term_avg

!     Spalart term
      if ( turbulence_model_int == sa                                          &
      .or. turbulence_model_int == des                                         &
      .or. turbulence_model_int == sa_neg                                      &
      .or. turbulence_model_int == des_neg ) then

        do i_local=1,nodes_local

          i = node_map(i_local) ! local node number

          dphidturb(1,i) = ( t_diff2*datrbre_cn(1,i) &
                           +    cb20                 )*term_avg
        enddo

      else if ( turbulence_model_int == ras_2011 ) then

        do i_local=1,nodes_local

          i = node_map(i_local) ! local node number

          dphidturb(1,i) = ( t_diff2*datrbre_cn(1,i) &
                           +    cb20                 )*term_avg
        enddo

      else

          dphidturb = 0.0_dp

      end if

      if ( constant_rnu_diffusion ) dphidnu = 0._dp

      ! jacobians of the gradients in the primal cell via Green-Gauss

      dgrad_celldq = element_dgrad( edges_local, max_node_per_cell,     &
                                    face_per_cell,                      &
                                    rn, local_f2n, e2n_2d, chk_norm )

      ! average gradients

      if (twod) then

        do i_local = 1, nodes_local

          i = node_map(i_local) ! local node number

          dtrbrexavg(i) = dgrad_celldq(i,1)
          dtrbrezavg(i) = dgrad_celldq(i,3)

        end do

      else

        do i_local = 1, nodes_local

          i = node_map(i_local) ! local node number

          dtrbrexavg(i) = dgrad_celldq(i,1)
          dtrbreyavg(i) = dgrad_celldq(i,2)
          dtrbrezavg(i) = dgrad_celldq(i,3)

        end do

      end if

      ! next loop over the edges in the cell for each one's
      ! contribution to the jacobian

      cell_edge_loop : do ie_local = 1,edges_local

        ie = edge_map(ie_local)  ! local edge number

        n1_loc = local_e2n(ie,1) ! local node numbers of edge endpoints
        n2_loc = local_e2n(ie,2) ! local node numbers of edge endpoints
        n3_loc = local_e2n(ie,3)
        n4_loc = local_e2n(ie,4)
        n5_loc = local_e2n(ie,5)
        n6_loc = local_e2n(ie,6)

        n1 = global_node_map(n1_loc) ! global node number of edge endpoints
        n2 = global_node_map(n2_loc) ! global node number of edge endpoints






        ! Left face centroid.

        if ( n4_loc == 0 ) then

          ! tria cell face.

          rl(1) = (rn(n1_loc,1) + rn(n2_loc,1) + rn(n3_loc,1))*onethird
          rl(2) = (rn(n1_loc,2) + rn(n2_loc,2) + rn(n3_loc,2))*onethird
          rl(3) = (rn(n1_loc,3) + rn(n2_loc,3) + rn(n3_loc,3))*onethird

        else

          ! quad cell face.

          rl(1) = (rn(n1_loc,1) + rn(n2_loc,1)                                 &
                 + rn(n3_loc,1) + rn(n4_loc,1))*oneforth
          rl(2) = (rn(n1_loc,2) + rn(n2_loc,2)                                 &
                 + rn(n3_loc,2) + rn(n4_loc,2))*oneforth
          rl(3) = (rn(n1_loc,3) + rn(n2_loc,3)                                 &
                 + rn(n3_loc,3) + rn(n4_loc,3))*oneforth

        end if

        ! Right face centroid.

        if (n6_loc == 0) then

          ! tria cell face.

          rr(1) = (rn(n1_loc,1) + rn(n2_loc,1) + rn(n5_loc,1))*onethird
          rr(2) = (rn(n1_loc,2) + rn(n2_loc,2) + rn(n5_loc,2))*onethird
          rr(3) = (rn(n1_loc,3) + rn(n2_loc,3) + rn(n5_loc,3))*onethird

        else

          ! quad cell face.

          rr(1) = (rn(n1_loc,1) + rn(n2_loc,1)                                 &
                 + rn(n5_loc,1) + rn(n6_loc,1))*oneforth
          rr(2) = (rn(n1_loc,2) + rn(n2_loc,2)                                 &
                 + rn(n5_loc,2) + rn(n6_loc,2))*oneforth
          rr(3) = (rn(n1_loc,3) + rn(n2_loc,3)                                 &
                 + rn(n5_loc,3) + rn(n6_loc,3))*oneforth

        end if

        ! Edge midpoint.

        rm(1) = (rn(n1_loc,1) + rn(n2_loc,1))*half
        rm(2) = (rn(n1_loc,2) + rn(n2_loc,2))*half
        rm(3) = (rn(n1_loc,3) + rn(n2_loc,3))*half

! contributions to dual normals from the two triangles
! that form part of the dual-cell surface

! area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        da(1) = ((rc(2)-rm(2))*(rl(3)-rr(3)) -                          &
                              (rc(3)-rm(3))*(rl(2)-rr(2)))*half
        da(2) = ((rc(3)-rm(3))*(rl(1)-rr(1)) -                          &
                              (rc(1)-rm(1))*(rl(3)-rr(3)))*half
        da(3) = ((rc(1)-rm(1))*(rl(2)-rr(2)) -                          &
                              (rc(2)-rm(2))*(rl(1)-rr(1)))*half

!       da(:) = element_edge_da( max_node_per_cell, edge_per_cell,             &
!                                ie, local_e2n, rn, rc )

        !jacobians of gradients at the dual face; either take gradients
        !for this piece of the dual face to be the same as the cell-average
        !gradient computed above  (which is what the legacy FUN3D solver does
        !for tets), or combine with the edge-gradient to increase h-ellipticity
        !non-simplicial meshes.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = 1._dp/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          dai = 1._dp/sqrt( da(1)**2 + da(2)**2 + da(3)**2 )
          xnf = da(1)*dai
          ynf = da(2)*dai
          znf = da(3)*dai

          !  average Green-Gauss gradient in edge direction

          !gradt_xi = trbrexavg(1)*ex + trbreyavg(1)*ey + trbrezavg(1)*ez

          ! directional gradients along edge

          !egradt = ( turb(nn,n2) - turb(nn,n1) )*disi

          augment_weight = edge_augment_weight( gradient_construction_lhs, &
                                                ex, ey, ez, xnf, ynf, znf )

          !trbrex(1) = trbrexavg(1) + ( egradt - gradt_xi )*augment_weight(1)
          !trbrey(1) = trbreyavg(1) + ( egradt - gradt_xi )*augment_weight(2)
          !trbrez(1) = trbrezavg(1) + ( egradt - gradt_xi )*augment_weight(3)

          ! avg_term pieces; all active cell nodes contribute.

          do i_local = 1, nodes_local

            !local node number

            i = node_map(i_local) ! local node number

            dgradt_xi = dtrbrexavg(i)*ex+dtrbreyavg(i)*ey+dtrbrezavg(i)*ez

            dtrbrex(i) = dtrbrexavg(i) - dgradt_xi*augment_weight(1)
            dtrbrey(i) = dtrbreyavg(i) - dgradt_xi*augment_weight(2)
            dtrbrez(i) = dtrbrezavg(i) - dgradt_xi*augment_weight(3)

          end do

          ! edge_term pieces; only the two edge nodes contribute.

          dtrbrex(n1_loc) = dtrbrex(n1_loc) - disi*augment_weight(1)
          dtrbrex(n2_loc) = dtrbrex(n2_loc) + disi*augment_weight(1)
          dtrbrey(n1_loc) = dtrbrey(n1_loc) - disi*augment_weight(2)
          dtrbrey(n2_loc) = dtrbrey(n2_loc) + disi*augment_weight(2)
          dtrbrez(n1_loc) = dtrbrez(n1_loc) - disi*augment_weight(3)
          dtrbrez(n2_loc) = dtrbrez(n2_loc) + disi*augment_weight(3)

        else include_edge_gradients

          ! only have the unaltered, average green-gauss contributions;
          ! all active nodes in the cell contribute

            do i_local = 1, nodes_local

              i = node_map(i_local) ! local node number

              dtrbrex(i) = dtrbrexavg(i)
              dtrbrey(i) = dtrbreyavg(i)
              dtrbrez(i) = dtrbrezavg(i)

            end do

        end if include_edge_gradients

        ! Jacobian of normal gradient oriented from n1 to n2.

        ngradt = 0._dp

!=============================================================================80
!=============================================================================80
        turbulence_model3:  select case ( turbulence_model_int )
        case ( sa, des, sa_neg, des_neg )
          dngradt(:) = xmre_s*( dtrbrex(:)*da(1) + dtrbrey(:)*da(2) &
                              + dtrbrez(:)*da(3) )

          do i_local = 1, nodes_local

            i = node_map(i_local) ! local node number

            ngradt  = ngradt + dngradt(i)*trbre_cn(1,i)
          enddo

        case ( ras_2011 )
          dngradt(:) = xmre_s*( dtrbrex(:)*da(1) + dtrbrey(:)*da(2) &
                              + dtrbrez(:)*da(3) )

          do i_local = 1, nodes_local

            i = node_map(i_local) ! local node number

            ngradt  = ngradt + dngradt(i)*trbre_cn(1,i)
          enddo

        case ( SSGLRR_RSM_w2012 )
          dphida_1 = xmre * (phi(1)*da(1) + phi(4)*da(2) + phi(5)*da(3))
          dphida_2 = xmre * (phi(4)*da(1) + phi(2)*da(2) + phi(6)*da(3))
          dphida_3 = xmre * (phi(5)*da(1) + phi(6)*da(2) + phi(3)*da(3))
          dngradt(:)  = xmre                                                   &
                 * (dtrbrex(:)*da(1) + dtrbrey(:)*da(2) + dtrbrez(:)*da(3))

        case default

          dphida_1 = zero
          dphida_2 = zero
          dphida_3 = zero
          dngradt(:) = xmre*( dtrbrex(:)*da(1) + dtrbrey(:)*da(2)              &
                     +        dtrbrez(:)*da(3) )

        end select turbulence_model3
!=============================================================================80
!=============================================================================80

        ! assemble final Jacobian matrices into sparse matrix form

!=============================================================================80
        edge_average1:  if ( edge_averaging ) then

!         n1 = c2n(n1_loc,n) ! global node number
!         n2 = c2n(n2_loc,n) ! global node number
          n1 = global_node_map(n1_loc) ! locally stored global node number
          n2 = global_node_map(n2_loc) ! locally stored global node number

          rho1   = 1._dp
          rho2   = 1._dp
          rnu1   = 1._dp
          rnu2   = 1._dp
          if ( eqn_set == compressible ) then
            rho1 = qnode(1,n1)
            rho2 = qnode(1,n2)
            rnu1 = viscosity_law( cstar, gamma*qnode(5,n1)/rho1 ) / rho1
            rnu2 = viscosity_law( cstar, gamma*qnode(5,n2)/rho2 ) / rho2
          end if

          if ( constant_rnu_diffusion ) rnu1 = 1._dp
          if ( constant_rnu_diffusion ) rnu2 = 1._dp

          select case ( turbulence_model_int )
          case ( sa, des, sa_neg, des_neg )
            turb1 = turb(1,n1)
            turb2 = turb(1,n2)
            aturb1 = sa0_turb_abs( turb1, rnu1 )
            aturb2 = sa0_turb_abs( turb2, rnu2 )

            phi(1)  = t_diff1*0.5_dp*(  rnu1 +   rnu2) &
                    + t_diff2*0.5_dp*(aturb1 + aturb2) &
                    +    cb20*0.5_dp*( turb1 +  turb2)
          case default

           coeff_n1 = get_local_coeff ( turbulence_model_int, n_turb,          &
                                        diff_const,  diff_const2, sst_f1(n1) )
           coeff_n2 = get_local_coeff ( turbulence_model_int, n_turb,          &
                                        diff_const,  diff_const2, sst_f1(n2) )
            do nn = 1, n_turb
              mutc1 = amut(n1) /  coeff_n1(nn)
              mutc2 = amut(n2) /  coeff_n2(nn)

              phi_edge(nn,ie) = t_diff1*0.5_dp*( rho1 * rnu1  + rho2 * rnu2  ) &
                              + t_diff2*0.5_dp*(        mutc1 +        mutc2 )
            enddo
            if ( turbulence_model_int == gamma_ret_sst ) then
              phi_edge(4,ie) = 0.5_dp                                          &
                             * ( t_diff1*( rho1 * rnu1  + rho2 * rnu2  )       &
                               + t_diff2*( amut(n1) + amut(n2) ) ) / coeff_n1(4)
            else if ( turbulence_model_int == asbm_sst) then
              phi_edge(3,ie) = 1.0_dp
            endif

          end select

        endif edge_average1
!=============================================================================80

        factor = -1._dp

        turbulence_model6:  select case ( turbulence_model_int )
!=============================================================================80
!
!       Spalart models, cell averaged
!
!=============================================================================80
        case ( sa, des, sa_neg, des_neg )

          nn = 1
          edge_node_loop_sa : do ii = 1,2

          ! diagonal contributions

          if (ii == 1) then
            i = n1_loc
            node = global_node_map(i)      ! c2n(n1_loc,n) ! global node number
            n2   = global_node_map(n2_loc) ! c2n(n2_loc,n)
          else
            i = n2_loc
            node = global_node_map(i)      ! c2n(n2_loc,n) ! global node number
            n2   = global_node_map(n1_loc) ! c2n(n1_loc,n)
          end if

          !n1c = local_e2n(ie,1) ! local node numbers of edge endpoints
          !n2c = local_e2n(ie,2)

          !if ( n1 <= nnodes0 ) then
          !  res(n_sta,n1) = res(n_sta,n1) - ( phi - cb2s*turb1 )*ngradt(1)
          !end if

          !if ( n2 <= nnodes0 ) then
          !  res(n_sta,n2) = res(n_sta,n2) + ( phi - cb2s*turb2 )*ngradt(1)
          !end if

          factor = -1._dp*factor

          terma  = phi(nn) - cb2s*turb(1,node)

          termb  = -factor*( dphidturb(1,i) - cb2s )*ngradt

          node_test1_sa:  if ( node <= nnodes0 ) then

            a(1,i,i) = a(1,i,i)                               &
                                    - factor*terma*dngradt(i) &
                                    + termb

            if ( tightly_couple .and. eqn_set == compressible ) then

              termd  = -factor*( dphidnu )*ngradt

              nc = node
              dqdnu(1:5) = dqc_via_dnu( termd, cstar, qnode(1,nc), &
               qnode(2,nc), qnode(3,nc), qnode(4,nc), qnode(5,nc) )
              do jj=1,5
                af(jj,i,i) = af(jj,i,i) + dqdnu(jj)
              enddo

            endif

          end if node_test1_sa

          ! off-diagonal contributions

          node_loop_2_sa : do j_local = 1, nodes_local

            j = node_map(j_local) ! local node number

            nodec = global_node_map(j) ! c2n(j,n)      ! global node number

            if (nodec == node) cycle node_loop_2_sa

            node_test2_sa: if ( node <= nnodes0 ) then

              ! avoid unused entries in a_off
              if (node > nnodes0 .and. nodec > nnodes0) cycle node_loop_2_sa


              ! res(n1) = res(n1) - ( phi - cb2s*turb1 )*ngradt
              ! res(n2) = res(n2) + ( phi - cb2s*turb2 )*ngradt

              if ( edge_averaging .and. nodec /= n2 ) then
                termc = 0._dp
              else
                termc  = -factor*( dphidturb(1,j)               )*ngradt
              endif

              a(1,i,j) = a(1,i,j)                               &
                                      - factor*terma*dngradt(j) &
                                      + termc

              if ( tightly_couple .and. eqn_set == compressible ) then
              if ( ( edge_averaging .and. nodec == n2 ) .or. &
                   ( .not.edge_averaging )                   ) then

                nc = nodec
                dqdnu(1:5) = dqc_via_dnu( termd, cstar, qnode(1,nc), &
                 qnode(2,nc), qnode(3,nc), qnode(4,nc), qnode(5,nc) )
                do jj=1,5
                  af(jj,i,j) = af(jj,i,j) + dqdnu(jj)
                enddo

              endif
              endif
            end if node_test2_sa

          end do node_loop_2_sa

        end do edge_node_loop_sa
!=============================================================================80
!
!       n_turb > 1 models, cell averaged
!
!=============================================================================80
        case ( SSGLRR_RSM_w2012 )

!         edge averaged diffusion
          if ( edge_averaging ) then
            phi(:)=phi_edge(:,ie) ! ie is the edge loop index
          endif

          edge_node_loop_7turb : do ii = 1, 2
!           diagonal contributions

            if (ii == 1) then
              i = n1_loc
              node = global_node_map(n1_loc) ! global node number
            else
              i = n2_loc
              node = global_node_map(n2_loc) ! global node number
            end if

            factor = -one*factor

              do nn = 1, 6
                a(nn,i,i) = a(nn,i,i)                                        &
                - factor * (dphida_1*dtrbrex(i) +                            &
                            dphida_2*dtrbrey(i) +                            &
                            dphida_3*dtrbrez(i))                             &
                                            *inv_rho_term(i)
              enddo
              a(7,i,i) = a(7,i,i)                                            &
                - factor * phi(7) * dngradt(i) * inv_rho_term(i)

!           loop over local nodes of element whilst sitting at one of two
!           endpoints of each edge of cell_edge_loop

            ! off-diagonal contributions

            node_loop_2_7turb : do j_local = 1, nodes_local

              j = node_map(j_local) ! local node number

              if ( j == i ) cycle node_loop_2_7turb

              nodec = global_node_map(j) ! c2n(j,n)      ! global node number

              do nn = 1, 6
                a(nn,i,j)     = a(nn,i,j)                                  &
              - factor * (dphida_1*dtrbrex(j) +                            &
                          dphida_2*dtrbrey(j) +                            &
                          dphida_3*dtrbrez(j))                             &
                                          *inv_rho_term(i)
              enddo
              a(7,i,j) = a(7,i,j)                                          &
                - factor * phi(7) * dngradt(j) * inv_rho_term(i)

            end do node_loop_2_7turb

          end do edge_node_loop_7turb

        case ( sst, sst_v, bsl, wilcox2006, wilcox2006_v, sst_kkl )

!         edge averaged diffusion
          if ( edge_averaging ) then
            phi(:)=phi_edge(:,ie) ! ie is the edge loop index
          endif

          edge_node_loop_2turb : do ii = 1, 2
!           diagonal contributions

            if (ii == 1) then
              i = n1_loc
            else
              i = n2_loc
            end if

            factor = -one*factor

            a(1,i,i) = a(1,i,i) - factor * phi(1)*dngradt(i)*inv_rho_term(i)
            a(2,i,i) = a(2,i,i) - factor * phi(2)*dngradt(i)*inv_rho_term(i)

!           loop over local nodes of element whilst sitting at one of two
!           endpoints of each edge of cell_edge_loop

            ! off-diagonal contributions

            node_loop_2_2turb : do j_local = 1, nodes_local

              j = node_map(j_local) ! local node number

              if ( j == i ) cycle node_loop_2_2turb

              a(1,i,j) = a(1,i,j) - factor * phi(1)*dngradt(j)*inv_rho_term(i)
              a(2,i,j) = a(2,i,j) - factor * phi(2)*dngradt(j)*inv_rho_term(i)

            end do node_loop_2_2turb

          end do edge_node_loop_2turb

        case default turbulence_model6

!         edge averaged diffusion
          if ( edge_averaging ) then
            phi(:)=phi_edge(:,ie) ! ie is the edge loop index
          endif

          edge_node_loop_nturb : do ii = 1, 2
!           diagonal contributions

            if (ii == 1) then
              i = n1_loc
              node = global_node_map(n1_loc) ! global node number
            else
              i = n2_loc
              node = global_node_map(n2_loc) ! global node number
            end if

            factor = -one*factor

              do nn = 1, n_turb_use
                a(nn,i,i) = a(nn,i,i)                                        &
                - factor * phi(nn) * dngradt(i) * inv_rho_term(i)
              enddo

!           loop over local nodes of element whilst sitting at one of two
!           endpoints of each edge of cell_edge_loop

            ! off-diagonal contributions

            node_loop_2_nturb : do j_local = 1, nodes_local

              j = node_map(j_local) ! local node number

              if ( j == i ) cycle node_loop_2_nturb

              nodec = global_node_map(j)      ! global node number

                do nn = 1, n_turb_use
                  a(nn,i,j)     = a(nn,i,j)                                  &
                           - factor * phi(nn) * dngradt(j) * inv_rho_term(i)
                end do

            end do node_loop_2_nturb

          end do edge_node_loop_nturb

        end select turbulence_model6

      end do cell_edge_loop


      install_jacobians:  select case ( n_turb )

      case ( 1 )

      ! Install Jacobians into global arrays.

      outer_install : do i_local = 1, nodes_local

        i = node_map(i_local) ! local node number

        node = global_node_map(i) ! c2n(i,n)
        if ( node <= nnodes0 ) then

          inner_install : do j_local = 1, nodes_local

            j = node_map(j_local) ! local node number

            if ( i == j ) then

              row = g2m(node)

              a_diag(n_sta,n_sta,row) = a_diag(n_sta,n_sta,row) &
                                      + a(1,i,j)

              if ( tightly_couple .and. eqn_set == compressible ) then

                do jj=1,5
                a_diag(n_sta,jj,row) = a_diag(n_sta,jj,row) &
                                      + af(jj,i,j)
                enddo

              endif

            else

              nodec = global_node_map(j) ! nodec = c2n(j,n)

                ioff = 0
                search : do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) then
                    ioff = nzg2m(k)
                    exit search
                  endif
                end do search

                a_off(n_sta,n_sta,ioff) = a_off(n_sta,n_sta,ioff) &
                                        + real(a(1,i,j),odp)

                if ( tightly_couple .and. eqn_set == compressible ) then

                  do jj=1,5
                  a_off(n_sta,jj,ioff) = a_off(n_sta,jj,ioff) &
                                        + real(af(jj,i,j),odp)
                  enddo

                endif

              endif

            end do inner_install

          end if
        end do outer_install

      case ( 2 )

        outer_install_2turb: do i_local = 1, nodes_local

          i = node_map(i_local) ! local node number

          node = global_node_map(i) ! node = c2n(i,n)
          if ( node <= nnodes0 ) then

            inner_install_2turb: do j_local = 1, nodes_local

              j = node_map(j_local) ! local node number

              diag_2turb: if ( i == j ) then

                row = g2m(node)

                a_diag(1,1,row) = a_diag(1,1,row) + a(1,i,j)
                a_diag(2,2,row) = a_diag(2,2,row) + a(2,i,j)

              else

                nodec = global_node_map(j) ! nodec = c2n(j,n)

                ioff = 0
                search_2turb : do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) then
                    ioff = nzg2m(k)
                    exit search_2turb
                  endif
                end do search_2turb

                a_off(1,1,ioff) = a_off(1,1,ioff) + real(a(1,i,j),odp)
                a_off(2,2,ioff) = a_off(2,2,ioff) + real(a(2,i,j),odp)

              endif diag_2turb

            end do inner_install_2turb

          end if

        end do outer_install_2turb

      case default

        outer_install_nturb: do i_local = 1, nodes_local

          i = node_map(i_local) ! local node number

          node = global_node_map(i) ! node = c2n(i,n)
          if ( node <= nnodes0 ) then

            inner_install_nturb: do j_local = 1, nodes_local

              j = node_map(j_local) ! local node number

              diag_nturb: if ( i == j ) then

                row = g2m(node)

                do nn = 1, n_turb_use
                  a_diag(nn,nn,row) = a_diag(nn,nn,row) + a(nn,i,j)
                end do

              else

                nodec = global_node_map(j) ! nodec = c2n(j,n)

                ioff = 0
                search_nturb : do k = ia(node), ia(node+1) - 1
                  column = ja(k)
                  if (column == nodec) then
                    ioff = nzg2m(k)
                    exit search_nturb
                  endif
                end do search_nturb

                do nn = 1, n_turb_use
                  a_off(nn,nn,ioff) = a_off(nn,nn,ioff) + real(a(nn,i,j),odp)
                end do

              endif diag_nturb

            end do inner_install_nturb

          end if

        end do outer_install_nturb

      end select install_jacobians

    end do diffusion_term_cell


  end subroutine turb_jacob_diff_element

!================================= TURB_RESID_DIFF ===========================80
!
! Diffusion residual for mixed element formulation.
!
! Calculates the residual for 2-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid_diff        (                                          &
                            eqn_set, viscous_method, nnodes0, nnodes01,        &
                            nedgeloc, eptr, turb, qnode, res, gradx, grady,    &
                            gradz, xn, yn, zn, ra, ielem, ncell, c2n,          &
                            x, y, z, local_f2n, local_e2n, local_f2e,          &
                            e2n_2d, nedgeloc_2d, face_per_cell,                &
                            node_per_cell, edge_per_cell, type_cell,           &
                            n_turb, n_tot, n_grd, face_2d, amut                &
                          , sst_f1, chk_norm )

    use kinddefs,        only : dp
    use info_depr,       only : skeleton, grad_x_y_z_contents
    use turbulence_info, only : turbulence_model_int
!   use debug_defs,      only : unified_diffusion

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: ncell
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d
    integer, intent(in) :: face_2d

    integer,  dimension(2,nedgeloc),          intent(in) :: eptr
    integer,  dimension(node_per_cell,ncell), intent(in) :: c2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer,  dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer,  dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer,  dimension(4,2),                 intent(in) :: e2n_2d

    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1
    integer,  dimension(face_per_cell, face_per_cell), intent(in) :: chk_norm

    real(dp), dimension(n_turb) :: diff_const
    real(dp), dimension(n_turb) :: diff_const2

    character(len=3), intent(in) :: type_cell
    integer :: n_sta

  continue

    call get_diffusion_coefficients( turbulence_model_int, n_turb              &
                          , diff_const, diff_const2 )

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

!   Diffusion terms:

    diffusion_terms : if ( viscous_method > 0 ) then
!     Check method for computing gradx,... (diffusion + source terms)
      if(skeleton > 0) then
        write(*,*) ' Edge-based residuals of decoupled turbulent diffusion.'
        write(*,*) ' Using weighted least squares for average gradient.'
        write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
      endif
!     if(trim(grad_x_y_z_contents) /= 'viscous weighted-least-squares') then
!       if(lmpi_master) then
!         write(*,*) ' Failure in edge-based turb residuals...stopping.'
!         write(*,*) ' gradx,...computed via ', trim(grad_x_y_z_contents)
!       endif
!       call lmpi_conditional_stop(1,&
!       'gradient method:resid_mix_diff')
!     endif
! edge based
        call resid_mix_diff_edge(eqn_set, viscous_method, nnodes0, nnodes01,   &
                            nedgeloc, eptr, turb, qnode, res, gradx, grady,    &
                            gradz, xn, yn, zn, ra, ielem,                      &
                            x, y, z, nedgeloc_2d, n_turb, n_tot, n_grd, amut   &
                           , sst_f1 )

    else diffusion_terms
      if ( skeleton > 0 )                                                      &
      write(*,*) 'Calling turb_resid_diff_element in turb_resid_diff.'

      n_sta = 1
      call turb_resid_diff_element ( n_sta, eqn_set, nnodes0,                  &
                             turb, qnode, res, ncell, c2n, x, y, z,            &
                             local_f2n, local_e2n, local_f2e, e2n_2d,          &
                             face_per_cell, node_per_cell, edge_per_cell,      &
                             type_cell, n_turb, face_2d, chk_norm,             &
                             turbulence_model_int, amut,                       &
                             diff_const, diff_const2, sst_f1 )

    end if diffusion_terms

  end subroutine turb_resid_diff

!========================= RESID_MIX_DIFF_EDGE ===============================80
!
! Diffusion residual for mixed element formulation.
!
! Calculates the residual for 2-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine resid_mix_diff_edge(eqn_set, viscous_method, nnodes0, nnodes01,   &
                            nedgeloc, eptr, turb, qnode, res, gradx, grady,    &
                            gradz, xn, yn, zn, ra, ielem,                      &
                            x, y, z, nedgeloc_2d, n_turb, n_tot, n_grd, amut   &
                           , sst_f1 )

    use kinddefs,       only : dp
    use info_depr,      only : tref, xmach, re, twod
    use debug_defs,     only : gradient_construction_rhs
    use solution_types, only : compressible, incompressible
    use turb_hrles_const, only : prt
    use turbulence_info,  only : turbulence_model_int
    use turb_ke_const,  only : sig_k_ke, sig_e_ke
    use turb_kw_const,  only : sig_w1, sig_w2, sig_k1, sig_k2,                 &
                               sigma_k_w88, sigma_w_w88,                       &
                               sigma_k_w98, sigma_w_w98,                       &
                               sigma_k_lag, sigma_w_lag,                       &
                               sigma_k_asm, sigma_w_asm,                       &
                               sigma_k_w06, sigma_w_w06,                       &
                               sigma_k_kkl, sigma_phi_kkl
    use fluid,          only : gamma, sutherland_constant
    use utilities,      only : tangents

    integer, intent(in) :: eqn_set, viscous_method
    integer, intent(in) :: ielem
    integer, intent(in) :: n_turb
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_grd
    integer, intent(in) :: nedgeloc
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: nedgeloc_2d

    integer,  dimension(2,nedgeloc),          intent(in) :: eptr

    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(nnodes01),        intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1


    integer :: n, nedge_flux_eval
    integer :: node1, node2
    integer :: nn

    real(dp) :: coeff1, coeff2
    real(dp) :: xmr, phi1, phi2
    real(dp) :: mu_node1, mu_node2, mut_node1, mut_node2
    real(dp) :: cstar
    real(dp) :: phi
    real(dp) :: rnu1, rnu2
    real(dp) :: rho1, p1, temp1, rho2, p2, temp2
    real(dp) :: lx, ly, lz, mx, my, mz, deti
    real(dp) :: my_xmach
    real(dp) :: txavg, tyavg, tzavg, gradt_xi
    real(dp) :: ex, ey, ez, disi
    real(dp) :: tx, ty, tz, egradt
    real(dp) :: lgradt, mgradt, rho1inv, rho2inv
    real(dp) :: sig_k1u, sig_k2u, sig_w2u

    real(dp), parameter :: half     = 0.5_dp
    real(dp), parameter :: one      = 1.0_dp
    real(dp), parameter :: my_tiny  = tiny(1.0_dp)

    real(dp), dimension(n_turb) :: coe1, coe2
    real(dp), dimension(3)      :: augment_weight
    real(dp), dimension(3,3)    :: b
    real(dp), dimension(n_turb) :: ngradt

  continue

    sig_k1u   = zero
    sig_k2u   = zero
    sig_w2u   = zero
    mut_node1 = zero
    mut_node2 = zero
    coeff1    = one
    coeff2    = one
    phi1      = zero
    phi2      = zero

      sig_k1u = sig_k1
      sig_k2u = sig_k2
      sig_w2u = sig_w2

    tx = 0._dp ; ty = 0._dp ; tz = 0._dp

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time

    if ( ( viscous_method > 0 ) .and. ielem > 1) return

    cstar = sutherland_constant / tref
    my_xmach = 0.0_dp

    select case ( eqn_set )
    case ( compressible )
      my_xmach = xmach
    case ( incompressible )
      my_xmach = one
    case default
      call lmpi_conditional_stop(1,'turb_resid_mix: only for in/compress pg')
    end select

    xmr  = my_xmach / re

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    endif

!   Diffusion terms:

      mixed_edge_loop: do n = 1, nedge_flux_eval

!       loop over all the faces and calculate the turbulent diffusion terms
!       (in the 2D case, this loop contains edges on only one y=constant plane)
!       turb(nn,node) contains the turbulence values
!       gradx(n_grd) contains gradx of turb, ...
!
!       Break into two contributions - directional and the average gradient -
!       and enforce M-property on only the directional piece

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        rho1   = one
        rho2   = one
        rnu1   = one
        rnu2   = one

        if ( eqn_set == compressible ) then
          rho1   = qnode(1,node1)
          rho2   = qnode(1,node2)
          rho1inv = one /rho1
          rho2inv = one /rho2
          p1     = qnode(5,node1)
          p2     = qnode(5,node2)

          temp1  = gamma*p1*rho1inv
          temp2  = gamma*p2*rho2inv

          rnu1   = viscosity_law( cstar, temp1 ) * rho1inv
          rnu2   = viscosity_law( cstar, temp2 ) * rho2inv
        end if

        coe1(1) = sig_k1u
        coe2(1) = sig_k2u
        coe1(2) = sig_w1
        coe2(2) = sig_w2u

        turbvariable_loop1 : do nn = 1, n_turb

          mu_node1  = t_diff1*rnu1*rho1
          mu_node2  = t_diff1*rnu2*rho2
          mut_node1 = t_diff2*amut(node1)
          mut_node2 = t_diff2*amut(node2)

          select case ( turbulence_model_int )
          case ( menter_sst, kw_sst, kw_sst2003                          &
               , sst_v, sst, sst_2003, asbm_sst, bsl, kw_des )
            coeff1 = sst_f1(node1)*coe1(nn) + (one - sst_f1(node1))*coe2(nn)
            coeff2 = sst_f1(node2)*coe1(nn) + (one - sst_f1(node2))*coe2(nn)
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( abid_linear )
            if ( nn == 1 ) then
              coeff1 = sig_k_ke
              coeff2 = sig_k_ke
            else
              coeff1 = sig_e_ke
              coeff2 = sig_e_ke
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( sst_kkl )
            if ( nn == 1 ) then
              coeff1 = sigma_k_kkl
              coeff2 = sigma_k_kkl
            else
              coeff1 = sigma_phi_kkl
              coeff2 = sigma_phi_kkl
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( wilcox1988,  wilcox1988_v, wilcox_kw88p,  wilcox_kw88 )
            if ( nn == 1 ) then
              coeff1 = sigma_k_w88
              coeff2 = sigma_k_w88
            else
              coeff1 = sigma_w_w88
              coeff2 = sigma_w_w88
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( wilcox_kw98 )
            if ( nn == 1 ) then
              coeff1 = sigma_k_w98
              coeff2 = sigma_k_w98
            else
              coeff1 = sigma_w_w98
              coeff2 = sigma_w_w98
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( kw_lag )
            mut_node1 = t_diff2*amut(node1)
            mut_node2 = t_diff2*amut(node2)
            if ( nn == 1 ) then
              phi1      = mu_node1 + mut_node1/sigma_k_lag
              phi2      = mu_node2 + mut_node2/sigma_k_lag
            else if ( nn == 2 ) then
              phi1      = mu_node1 + mut_node1/sigma_w_lag
              phi2      = mu_node2 + mut_node2/sigma_w_lag
            else
              phi1      = zero
              phi2      = zero
            endif
          case ( wilcox_asm, easm_ddes, EASMko2003_S )
            if ( nn == 1 ) then
              coeff1 = sigma_k_asm
              coeff2 = sigma_k_asm
            else
              coeff1 = sigma_w_asm
              coeff2 = sigma_w_asm
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( wilcox_kw06 )
            mut_node1 = qnode(1,node1)*turb(1,node1)/turb(2,node1)
            mut_node2 = qnode(1,node2)*turb(1,node2)/turb(2,node2)
            mut_node1 = t_diff2*max( mut_node1, my_tiny )
            mut_node2 = t_diff2*max( mut_node2, my_tiny )
            if ( nn == 1 ) then
              coeff1 = sigma_k_w06
              coeff2 = sigma_k_w06
            else
              coeff1 = sigma_w_w06
              coeff2 = sigma_w_w06
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( wilcox_kw06p, wilcox2006, wilcox2006_v )
            mut_node1 = qnode(1,node1)*turb(1,node1)/turb(2,node1)
            mut_node2 = qnode(1,node2)*turb(1,node2)/turb(2,node2)
            mut_node1 = t_diff2*max( mut_node1, my_tiny )
            mut_node2 = t_diff2*max( mut_node2, my_tiny )
            if ( nn == 1 ) then
              coeff1 = sigma_k_w06
              coeff2 = sigma_k_w06
            else
              coeff1 = sigma_w_w06
              coeff2 = sigma_w_w06
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case ( wilcox_les )
            if ( nn == 1 ) then
              mut_node1 = t_diff2*mu_t_les(node1)
              mut_node2 = t_diff2*mu_t_les(node2)
              coeff1 = sst_f1(node1)*sigma_k_w06 + (one - sst_f1(node1))*prt
              coeff2 = sst_f1(node2)*sigma_k_w06 + (one - sst_f1(node2))*prt
              phi1   = mu_node1 + mut_node1/coeff1
              phi2   = mu_node2 + mut_node2/coeff2
            else
              mut_node1 = t_diff2*amut(node1)
              mut_node2 = t_diff2*amut(node2)
              coeff1 = sigma_w_w06
              coeff2 = sigma_w_w06
              phi1   = mu_node1 + mut_node1/coeff1
              phi2   = mu_node2 + mut_node2/coeff2
            endif
          case ( k_kL_MEAH2013 )
            if ( nn == 1 ) then
              coeff1 = sigma_k_kkl
              coeff2 = sigma_k_kkl
            else
              coeff1 = sigma_phi_kkl
              coeff2 = sigma_phi_kkl
            endif
            phi1   = mu_node1 + mut_node1/coeff1
            phi2   = mu_node2 + mut_node2/coeff2
          case default
            write(*,*)                                                     &
            'Error in routine resid mix diff...unknown turbulence_model: ',&
            turbulence_model
            call lmpi_conditional_stop(1,                                  &
            'unknown turbulence_model:resid_mix_diff_edge')
          end select

          phi = half*(phi1 + phi2)

!         average node-based gradients : gradx, grady, gradz

          txavg = half*( gradx(n_grd-n_turb+nn,node1) +                     &
                            gradx(n_grd-n_turb+nn,node2) )
          tyavg = half*( grady(n_grd-n_turb+nn,node1) +                     &
                            grady(n_grd-n_turb+nn,node2) )
          tzavg = half*( gradz(n_grd-n_turb+nn,node1) +                     &
                            gradz(n_grd-n_turb+nn,node2) )

!         ex, ey, ez is unit vector along edge direction

          ex   = x(node2) - x(node1)
          ey   = y(node2) - y(node1)
          ez   = z(node2) - z(node1)
          disi = one/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          if( viscous_method == 2 ) then

!         directional gradients along edge

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          tx = egradt*ex
          ty = egradt*ey
          tz = egradt*ez

          elseif( gradient_construction_rhs == 0 ) then

!         average Green-Gauss gradient in edge direction

!         directional gradients along edge

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          augment_weight = edge_augment_weight( gradient_construction_rhs,     &
                                            ex, ey, ez, xn(n), yn(n), zn(n) )
          gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

!         resolve gradient contributions from edge and nodes
!         u, v, w, and speed-of-sound-squared (i.e., perfect gas temperature)

          tx = txavg + ( egradt - gradt_xi )*augment_weight(1)
          ty = tyavg + ( egradt - gradt_xi )*augment_weight(2)
          tz = tzavg + ( egradt - gradt_xi )*augment_weight(3)

          elseif( gradient_construction_rhs == 1 ) then

          !...find tangent vectors in the dual face
          call tangents(xn(n), yn(n), zn(n), lx, ly, lz, mx, my, mz)

          !...find inverse elements of transformation matrix
          deti =  one / ( ex*( ly*mz - lz*my ) &
                        + ey*( lz*mx - lx*mz ) &
                        + ez*( lx*my - ly*mx ) )

          b(1,1) =  deti*( ly*mz - lz*my )
          b(1,2) = -deti*( ey*mz - ez*my )
          b(1,3) =  deti*( ey*lz - ez*ly )

          b(2,1) = -deti*( lx*mz - lz*mx )
          b(2,2) =  deti*( ex*mz - ez*mx )
          b(2,3) = -deti*( ex*lz - ez*lx )

          b(3,1) =  deti*( lx*my - ly*mx )
          b(3,2) = -deti*( ex*my - ey*mx )
          b(3,3) =  deti*( ex*ly - ey*lx )

!         directional gradients

          egradt = ( turb(nn,node2) - turb(nn,node1) )*disi

          lgradt = txavg*lx + tyavg*ly + tzavg*lz

          mgradt = txavg*mx + tyavg*my + tzavg*mz

!         resolve gradient contributions from edge and dual face

          tx = b(1,1)*egradt + b(1,2)*lgradt + b(1,3)*mgradt
          ty = b(2,1)*egradt + b(2,2)*lgradt + b(2,3)*mgradt
          tz = b(3,1)*egradt + b(3,2)*lgradt + b(3,3)*mgradt

          endif

!         turbulent diffusion contribution at dual face [ two terms ]

!         [area]*[nondimensionalization factor : Mach / Re / sigma ]*
!         [normal gradient] at dual face

          ngradt(nn) = xmr*ra(n)*( tx*xn(n) + ty*yn(n) + tz*zn(n) )
!if ( node1 == 2 .or. node2 == 2 ) then
!write(6,'(a,i5)')          'edge             = ', n
!write(6,'(a,i5)')          'eqn              = ', nn
!write(6,'(a,8(1x,f10.5))') 'tavg             = ', txavg, tyavg, tzavg
!write(6,'(a,8(1x,f10.5))') 'ex, ey, ez       = ', ex, ey, ez
!write(6,'(a,8(1x,f10.5))') 'tx, ty, tz       = ', tx, ty, tz
!write(6,'(a,8(1x,f10.5))') 'xn, yn, zn, ra   = ', xn(n), yn(n), zn(n), ra(n)
!write(6,'(a,8(1x,f10.5))') 'gradt_xi         = ', gradt_xi
!write(6,'(a,8(1x,f10.5))') 'egradt           = ', egradt
!write(6,'(a,8(1x,f10.5))') 'ngradt(nn)       = ', ngradt(nn)
!endif
          if ( node1 <= nnodes0 ) then
            res(nn,node1) = res(nn,node1) - ( phi*ngradt(nn) )
          end if

          if ( node2 <= nnodes0 ) then
            res(nn,node2) = res(nn,node2) + ( phi*ngradt(nn) )
          end if
!write(6,'(a,10(1x,f20.10))') 'diff_edge ', &
!    egradt , gradt_xi, ngradt(nn), phi, ngradt(nn)*phi

        end do turbvariable_loop1

      end do mixed_edge_loop

  end subroutine resid_mix_diff_edge


!================================ JACOB_MIX_DIFF =============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for 2-eqn models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob_diff( eqn_set, nnodes0, nnodes01, max_nnz,             &
                              qnode, a_diag, a_off, ncell, c2n, x, y, z,       &
                              type_cell,                                       &
                            local_f2n, local_e2n,local_f2e, e2n_2d,            &
                            face_per_cell, node_per_cell, edge_per_cell, nnz01,&
                            ia, ja, n_turb, n_tot, face_2d, nzg2m, g2m, amut,  &
                            turb, sst_f1, chk_norm )

    use kinddefs,        only : dp, odp
    use turbulence_info, only : turbulence_model_int

    integer, intent(in) :: eqn_set
    integer, intent(in) :: n_tot
    integer, intent(in) :: n_turb
    integer, intent(in) :: max_nnz
    integer, intent(in) :: ncell
    integer, intent(in) :: face_per_cell
    integer, intent(in) :: node_per_cell
    integer, intent(in) :: edge_per_cell
    integer, intent(in) :: face_2d
    integer, intent(in) :: nnodes0, nnodes01
    integer, intent(in) :: nnz01

    integer, dimension(node_per_cell,ncell), intent(in) :: c2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),     intent(in) :: local_e2n
    integer, dimension(face_per_cell,4),     intent(in) :: local_f2e
    integer, dimension(4,2),                 intent(in) :: e2n_2d
    integer, dimension(nnodes01+1),          intent(in) :: ia
    integer, dimension(nnz01),               intent(in) :: ja
    integer, dimension(:),                   intent(in) :: nzg2m, g2m

    real(dp),  dimension(nnodes01),              intent(in)    :: x, y, z
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(nnodes01),              intent(in)    :: amut
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(n_turb,n_turb,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1
    integer,  dimension(face_per_cell, face_per_cell), intent(in) :: chk_norm

    character(len=3), intent(in) :: type_cell

    real(dp), dimension(n_turb) :: diff_const
    real(dp), dimension(n_turb) :: diff_const2
    integer :: n_sta

  continue

      call get_diffusion_coefficients( turbulence_model_int, n_turb            &
                          , diff_const, diff_const2 )

      n_sta = 1
      call turb_jacob_diff_element( n_sta, eqn_set, nnodes0,                   &
                              !nnodes01, max_nnz, nnz01, n_tot,                &
                               turb, qnode, a_diag, a_off,                     &
                               ncell, c2n, x, y, z,                            &
                               type_cell, local_f2n, local_e2n, local_f2e,     &
                               e2n_2d, face_per_cell, node_per_cell,           &
                               edge_per_cell, ia, ja, n_turb,                  &
                               face_2d, chk_norm, nzg2m, g2m,                  &
                               turbulence_model_int, amut,                     &
                               diff_const, diff_const2, sst_f1 )

  end subroutine turb_jacob_diff

!=============================== ELEMENT_EDGE_DA =============================80
!
! Dual area at an edge.
!
!=============================================================================80

  pure function element_edge_da( node_per_cell, edge_per_cell,                 &
                                 ie, local_e2n, r, rc )

    integer,                              intent(in)  :: node_per_cell
    integer,                              intent(in)  :: edge_per_cell
    integer,                              intent(in)  :: ie
    integer,  dimension(edge_per_cell,6), intent(in)  :: local_e2n
    real(dp), dimension(node_per_cell,3), intent(in)  :: r
    real(dp), dimension(3),               intent(in)  :: rc
    real(dp), dimension(3)                            :: element_edge_da

    real(dp), dimension(3) :: rl, rr, rm

    integer :: n1, n2, n3, n4, n5, n6

    real(dp), parameter :: onethird = 1.0_dp/3.0_dp
    real(dp), parameter :: oneforth = 0.25_dp
    real(dp), parameter :: half     = 0.5_dp

  continue

    n1 = local_e2n(ie,1)
    n2 = local_e2n(ie,2)
    n3 = local_e2n(ie,3)
    n4 = local_e2n(ie,4)
    n5 = local_e2n(ie,5)
    n6 = local_e2n(ie,6)

    ! Left face centroid.

    if ( n4 == 0 ) then

      ! tria cell face.

      rl(1) = (r(n1,1) + r(n2,1) + r(n3,1))*onethird
      rl(2) = (r(n1,2) + r(n2,2) + r(n3,2))*onethird
      rl(3) = (r(n1,3) + r(n2,3) + r(n3,3))*onethird

    else

      ! quad cell face.

      rl(1) = (r(n1,1) + r(n2,1) + r(n3,1) + r(n4,1))*oneforth
      rl(2) = (r(n1,2) + r(n2,2) + r(n3,2) + r(n4,2))*oneforth
      rl(3) = (r(n1,3) + r(n2,3) + r(n3,3) + r(n4,3))*oneforth

    end if

    ! Right face centroid.

    if (n6 == 0) then

      ! tria cell face.

      rr(1) = (r(n1,1) + r(n2,1) + r(n5,1))*onethird
      rr(2) = (r(n1,2) + r(n2,2) + r(n5,2))*onethird
      rr(3) = (r(n1,3) + r(n2,3) + r(n5,3))*onethird

    else

      ! quad cell face.

      rr(1) = (r(n1,1) + r(n2,1) + r(n5,1) + r(n6,1))*oneforth
      rr(2) = (r(n1,2) + r(n2,2) + r(n5,2) + r(n6,2))*oneforth
      rr(3) = (r(n1,3) + r(n2,3) + r(n5,3) + r(n6,3))*oneforth

    end if

    ! Edge midpoint.

    rm(1) = (r(n1,1) + r(n2,1))*half
    rm(2) = (r(n1,2) + r(n2,2))*half
    rm(3) = (r(n1,3) + r(n2,3))*half

! contributions to dual normals from the two triangles
! that form part of the dual-cell surface

! area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

    element_edge_da(1) = ((rc(2)-rm(2))*(rl(3)-rr(3)) -                        &
                          (rc(3)-rm(3))*(rl(2)-rr(2)))*half
    element_edge_da(2) = ((rc(3)-rm(3))*(rl(1)-rr(1)) -                        &
                          (rc(1)-rm(1))*(rl(3)-rr(3)))*half
    element_edge_da(3) = ((rc(1)-rm(1))*(rl(2)-rr(2)) -                        &
                          (rc(2)-rm(2))*(rl(1)-rr(1)))*half

  end function element_edge_da

  include 'viscosity_law.f90'
  include 'dviscosity_law.f90'
  include 'edge_augment_weight.f90'
  include 'element_center.f90'
  include 'element_grad.f90'
  include 'element_dgrad.f90'
  include 'sa0_turb_abs.f90'
  include 'dsa1_turb_abs.f90'
  include 'element_big_angle.f90'
  include 'dqc_via_dnu.f90'

end module turb_diffusion

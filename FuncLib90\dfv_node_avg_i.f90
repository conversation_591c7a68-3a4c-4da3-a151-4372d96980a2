!================================= DFV_NODE_AVG_I ============================80
!
! Linearization of the viscous fluxes using node averaging.
! (Fills Jacobians (3,4)...skipping continuity).
!
!=============================================================================80

  function dfv_node_avg_i( igrid, xnorm, ynorm, znorm, area,                   &
                         flsq_n, flsq_j, flsq_ja, averaged_nodes,              &
                         n_q, cell1, cell2, xc, yc, zc, sc, amut, x, y, z )

    use node_avg_cc, only : dqavg, n_qavg_max, dqavg_bc

    integer, intent(in) :: igrid, flsq_n, flsq_j
    integer, intent(in) :: cell1, cell2, n_q

    integer, dimension(5),       intent(in) :: averaged_nodes
    integer,  dimension(flsq_n), intent(in) :: flsq_ja

    real(dp), intent(in)    :: xnorm, ynorm, znorm, area

    real(dp), dimension(:),             intent(in) :: xc, yc, zc, sc, amut
    real(dp), dimension(:),             intent(in) :: x, y, z

    integer :: cella, ii, iii, nb
    integer :: face_node, n_face_nodes, node, na_b, n_qavg, ia_flsq, na_cell

    integer, dimension(n_qavg_max) :: cells, iqt

    real(dp) :: amutf, xn, yn, zn

    !...linearization of u,v,w gradients w/r to cell-centers
    real(dp), dimension(3,lsq_face_neighbors_max+2) :: dugrad, dvgrad, dwgrad
    real(dp), dimension(3,6) :: dgrad_q
    real(dp), dimension(3) :: uterm, vterm, wterm, dgrad_term
    real(dp), dimension(n_qavg_max) :: sw
    real(dp), dimension(n_q, n_qavg_max) :: dw_int, dw_ext

    real(dp), dimension(4,4,flsq_j) :: dfv_node_avg_i

  continue

    ! Linearization w/r to cell-centers and node-averages.

    amutf = 0.5_dp*( amut(cell1) + amut(cell2) )

    dgrad_q = dgrad_node_avg( cell1, cell2, averaged_nodes, &
                              xc, yc, zc, x, y, z  )

    n_face_nodes = averaged_nodes(5)

    ! Distribute node-average contributions to cells.
    ! Until boundary conditions come into play, dgrad terms identical.

    ! First the contribution from interior cells.

    dvgrad(:,1:flsq_n) = 0._dp

    dvgrad(:,flsq_n+1) = dgrad_q(:,5)
    dvgrad(:,flsq_n+2) = dgrad_q(:,6)

    na_b = 0
    do face_node=1,n_face_nodes

      dgrad_term(:) = dgrad_q(:,face_node)

      node = averaged_nodes(face_node)

      xn = x(node)
      yn = y(node)
      zn = z(node)

      call dqavg( igrid, xc, yc, zc, sc, xn, yn, zn,      &
                  node, n_qavg_max, n_qavg, nb, sw, cells )

      na_b = na_b + nb

      do ii=1,n_qavg
        vterm(:) = sw(ii)*dgrad_term(:)
        cella = cells(ii)
        if ( cella == cell1 ) then
          dvgrad(:,flsq_n+1) = dvgrad(:,flsq_n+1) + vterm(:)
          cycle
        elseif ( cella == cell2 ) then
          dvgrad(:,flsq_n+2) = dvgrad(:,flsq_n+2) + vterm(:)
          cycle
        endif
        iii = 0
        do ia_flsq=1,flsq_n
          na_cell = flsq_ja(ia_flsq)
          iii = iii + 1
          if ( na_cell == cella ) exit
        enddo
        dvgrad(:,iii) = dvgrad(:,iii) + vterm(:)
      enddo

    enddo

!   Set u,v,w entries before adding boundary condition info.

    do ii=1,flsq_n+2
      dugrad(:,ii) = dvgrad(:,ii)
      dwgrad(:,ii) = dvgrad(:,ii)
    enddo

    if ( na_b == 0 ) n_face_nodes = 0  !skip boundary additions
    do face_node=1,n_face_nodes

      dgrad_term(:) = dgrad_q(:,face_node)

      node = averaged_nodes(face_node)

      xn = x(node)
      yn = y(node)
      zn = z(node)

      call dqavg_bc( igrid, xn, yn, zn, n_q, node, n_qavg_max, &
                     n_qavg, dw_int, dw_ext, cells, iqt )

      do ii=1,n_qavg
        uterm(:) = dw_int(2,ii)*dgrad_term(:)
        vterm(:) = dw_int(3,ii)*dgrad_term(:)
        wterm(:) = dw_int(4,ii)*dgrad_term(:)
        cella = cells(ii)
        if ( cella == cell1 ) then
          dugrad(:,flsq_n+1) = dugrad(:,flsq_n+1) + uterm(:)
          dvgrad(:,flsq_n+1) = dvgrad(:,flsq_n+1) + vterm(:)
          dwgrad(:,flsq_n+1) = dwgrad(:,flsq_n+1) + wterm(:)
          cycle
        elseif ( cella == cell2 ) then
          dugrad(:,flsq_n+2) = dugrad(:,flsq_n+2) + uterm(:)
          dvgrad(:,flsq_n+2) = dvgrad(:,flsq_n+2) + vterm(:)
          dwgrad(:,flsq_n+2) = dwgrad(:,flsq_n+2) + wterm(:)
          cycle
        endif
        iii = 0
        do ia_flsq=1,flsq_n
          na_cell = flsq_ja(ia_flsq)
          iii = iii + 1
          if ( na_cell == cella ) exit
        enddo
        dugrad(:,iii) = dugrad(:,iii) + uterm(:)
        dvgrad(:,iii) = dvgrad(:,iii) + vterm(:)
        dwgrad(:,iii) = dwgrad(:,iii) + wterm(:)
      enddo

    enddo

    dfv_node_avg_i(:,:,1:flsq_j) =                     &
        dfvf_i( xnorm, ynorm, znorm, area, amutf,      &
                dugrad, dvgrad, dwgrad, flsq_j )

  end function dfv_node_avg_i

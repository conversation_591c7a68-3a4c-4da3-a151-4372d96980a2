module turb_bc_nc

  use kinddefs,              only : dp, odp
  use exact_defs,            only : ic_exact
  use info_depr,             only : twod, skeleton, tightly_couple
  use lmpi,                  only : lmpi_conditional_stop
  use comprow_types,         only : crow_flow
  use cfl_defs,  only : hanim
  use grid_types,            only : grid_type
  use solution_types,        only : soln_type
  use bc_types,              only : bcgrid_type, bcsoln_type
  use element_types,         only : elem_type
  use exact,                 only : exact_q
  use thermo,                only : primitive_q_type, q_type
  use grid_motion_helpers,   only : need_grid_velocity
  use turb_parameters,       only : t_conv, ubar_eps, turbulence_exterior,     &
                                    set_turbulence_exterior
  use element_based_bc_util, only : element_based_metrics
  use twod_util,             only : yplane_2d, y_coplanar_tol
  use exact_lisbon_backstep, only : set_q_backstep_vt
  use bc_names,              only : bc_name_index, bc_ignore_2d,               &
                                    bc_strong_turb, bc_dirichlet,              &
                                    symmetry_x, symmetry_y, symmetry_z,        &
                                    symmetry_1_strong, symmetry_2_strong,      &
                                    symmetry_3_strong,                         &
                                    dirichlet, dirichlet_viscous,              &
                                    dirichlet_discrete, dirichlet_lisbon,      &
                                    lisbon_inflow_profile, bc_is_periodic,     &
                                    viscous_solid, viscous_solid_trs,          &
                                    viscous_wall_function, viscous_wf_trs
  use b_q_defs,              only : b_discrete
  use turb_parameters,       only : turbulent_convection

  implicit none

  private

  ! Turbulence boundary conditions for the node-centered path.
  ! The legacy node-centered simplification of a first-order accurate
  ! and nonconservative treatment of convection is exploited.

  ! Either:                            Comment:
  ! (1) Convection equation treatment  Element-based, preferably, because lumped
  !                                    normals (bxn,byn,bzn) can be disparate
  !                                    with element directed areas, leading to
  !                                    incorrect inflow/outflow decision.
  ! (2) Symmetry                       Nothing is done.
  ! (3) Strong boundary condition      Specified turbulence.

  ! Note: Diffusion is treated at the boundaries only in the element-based
  !       path using existing velocity and cell-gradient values.

  ! Strong boundary condition usage:

  ! type      function                        usage
  ! --------  ------------------------------  -----------
  ! residual  res = value-value_desired       relaxation
  ! jacobian  linearization of above          relaxation
  ! q         soln%q = value_desired          FAS restriction
  !                                           FAS prolongation
  !                                           FAS IMG perturbations
  ! dq        turbres = 0                     FAS prolongation
  !                                           FAS restriction
  !                                           FAS ICG restriction
  !                                           FAS ICG prolongation

  ! Note: turbulence residual are in %turbres for baseline path
  !       and in %res for tightly_couple path.

  public :: turb_bc_residual_nc
  public :: turb_bc_jacobian_nc
  public :: bc_strong_turb_res_nc
  public :: bc_strong_turb_dq_nc
  public :: bc_strong_turb_q_nc
  public :: bc_strong_turb_jacobian_nc
  public :: periodic_lhs_t, periodic_rhs_t
  public :: get_turb_ext

  character(80) :: bc_name

contains

!=============================== TURB_BC_RESIDUAL_NC =========================80
!
! Residuals for turbulence bc.
!
!=============================================================================80
  subroutine turb_bc_residual_nc( grid, soln )

    use solution_types, only : compressible

    type(grid_type), intent(in   ) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: n_sta, ib, ibc, ierr, n_q, n_turb

  continue

    ierr = 0
    if ( soln%eqn_set == compressible ) ierr = primitive_q_type - q_type
    call lmpi_conditional_stop(ierr,'q_type not primitive:turb_bc_residual_nc')

    if ( grid%origin >= 3 ) then
      do ib = 1, grid%nbound
        ierr = max( grid%bc(ib)%nbfacet, grid%bc(ib)%nbfaceq )
        call lmpi_conditional_stop(ierr,'inconsistent:turb_bc_residual_nc')
      enddo
    endif

    if ( soln%n_turb > 1 ) then
      do ib = 1, grid%nbound
        if ( grid%bc(ib)%ibc /= lisbon_inflow_profile ) cycle
        call lmpi_conditional_stop(1,'need set_q_backstep_vt:bc_turb_conv')
      enddo
    endif

    call set_turbulence_exterior
    if ( soln%n_turb > size(turbulence_exterior) ) ierr = 1
    call lmpi_conditional_stop(ierr,'turbulence_exterior:turb_bc_residual_nc')

    n_q    = soln%n_q
    n_turb = soln%n_turb

    do ib = 1, grid%nbound

      ibc = grid%bc(ib)%ibc
      call bc_name_index(ibc,bc_name,.true.)

      if ( twod .and. bc_ignore_2d(ibc) ) cycle

      if ( skeleton > 1 .and. bc_strong_turb(ibc) )      &
      write(*,"(1x,a,i0,a,i4,2a)")                       &
      'turb_bc_residual_bc: Grid=',grid%igrid,' ib=',ib, &
      ' cycle bc_strong_turb=',trim(bc_name)

      if ( skeleton > 1 .and. bc_dirichlet(ibc) )        &
      write(*,"(1x,a,i0,a,i4,2a)")                       &
      'turb_bc_residual_bc: Grid=',grid%igrid,' ib=',ib, &
      ' cycle bc_dirichlet=',trim(bc_name)

      if ( bc_strong_turb(ibc) ) cycle
      if ( bc_dirichlet(ibc) ) cycle

      if ( ibc == symmetry_x .or.         &
           ibc == symmetry_y .or.         &
           ibc == symmetry_z .or.         &
           ibc == symmetry_1_strong .or.  &
           ibc == symmetry_2_strong .or.  &
           ibc == symmetry_3_strong .or.  &
           bc_is_periodic(ibc) ) cycle

      if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)")   &
      'turb_bc_residual_bc: Grid=',grid%igrid,' ib=',ib, &
      ' turb-res:bc=',trim(bc_name)

      if ( .not.tightly_couple ) then
        n_sta = 1
        call bc_turb_conv( soln%eqn_set,           soln%turbres, n_sta, &
                           n_q, n_turb, soln%dof0,                      &
                           soln%turb, soln%q_dof,                       &
                           ib, grid%bc, grid%elem,                      &
                           grid%origin, grid%x, grid%y, grid%z,         &
                           grid%dxdt, grid%dydt, grid%dzdt,             &
                           soln%gradx, soln%grady, soln%gradz )
      else
        n_sta = n_q-n_turb+1
        call bc_turb_conv( soln%eqn_set,               soln%res, n_sta, &
                           n_q, n_turb, soln%dof0,                      &
                           soln%turb, soln%q_dof,                       &
                           ib, grid%bc, grid%elem,                      &
                           grid%origin, grid%x, grid%y, grid%z,         &
                           grid%dxdt, grid%dydt, grid%dzdt,             &
                           soln%gradx, soln%grady, soln%gradz )
      endif

    end do

  end subroutine turb_bc_residual_nc

!=============================== TURB_BC_JACOBIAN_NC =========================80
!
! Jacobians for turbulence bc.
!
!=============================================================================80
  subroutine turb_bc_jacobian_nc( grid, soln, crow )

    use solution_types, only : compressible

    type(grid_type), intent(in   ) :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in   ) :: crow

    integer :: n_sta, ib, ibc, ierr, n_q, n_turb

  continue

    ierr = 0
    if ( soln%eqn_set == compressible ) ierr = primitive_q_type - q_type
    call lmpi_conditional_stop(ierr,'q_type not primitive:turb_bc_jacobian_nc')

    n_q    = soln%n_q
    n_turb = soln%n_turb

    do ib = 1, grid%nbound

      ibc = grid%bc(ib)%ibc

      if ( twod .and. bc_ignore_2d(ibc) ) cycle

      if ( bc_strong_turb(ibc) ) cycle
      if ( bc_dirichlet(ibc) ) cycle

      if ( ibc == symmetry_x .or.         &
           ibc == symmetry_y .or.         &
           ibc == symmetry_z .or.         &
           ibc == symmetry_1_strong .or.  &
           ibc == symmetry_2_strong .or.  &
           ibc == symmetry_3_strong .or.  &
           bc_is_periodic(ibc) ) cycle

      call bc_name_index(ibc,bc_name,.true.)
      if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
      'Grid=',grid%igrid,' ib=',ib,' turb-res:bc=',trim(bc_name)

      if ( .not.tightly_couple ) then
        n_sta = 1
        call bc_turb_conv_jacob( soln%eqn_set, n_sta, n_q, n_turb,          &
                                 soln%dof0,               soln%a_turb_diag, &
                                 soln%turb, soln%q_dof, crow%g2m,           &
                                 ib, grid%bc, grid%elem,                    &
                                 grid%origin, grid%x, grid%y, grid%z,       &
                                 grid%dxdt, grid%dydt, grid%dzdt )
      else
        n_sta = n_q-n_turb+1
        call bc_turb_conv_jacob( soln%eqn_set, n_sta, n_q, n_turb,          &
                                 soln%dof0,                    soln%a_diag, &
                                 soln%turb, soln%q_dof, crow%g2m,           &
                                 ib, grid%bc, grid%elem,                    &
                                 grid%origin, grid%x, grid%y, grid%z,       &
                                 grid%dxdt, grid%dydt, grid%dzdt )
      endif

    end do

  end subroutine turb_bc_jacobian_nc

!================================ BC_TURB_CONV ===============================80
!
! Boundary closure using convection for general turbulence model.
!
!=============================================================================80
  subroutine bc_turb_conv( eqn_set, res, n_sta, n_q, n_turb,                   &
                           dof0, turb, q_dof, ib, bc, elem, origin, x, y, z,   &
                           dxdt, dydt, dzdt, gradx, grady, gradz )

    integer,                           intent(in   ) :: eqn_set
    integer,                           intent(in   ) :: n_sta
    integer,                           intent(in   ) :: n_q
    integer,                           intent(in   ) :: n_turb
    integer,                           intent(in   ) :: dof0
    integer,                           intent(in   ) :: ib
    integer,                           intent(in   ) :: origin
    type(elem_type),   dimension(:),   intent(in   ) :: elem
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:),   intent(in   ) :: x, y, z
    real(dp),          dimension(:),   intent(in   ) :: dxdt, dydt, dzdt
    real(dp),          dimension(:,:), intent(in   ) :: turb, q_dof
    real(dp),          dimension(:,:), intent(in)    :: gradx, grady, gradz
    real(dp),          dimension(:,:), intent(inout) :: res

    integer :: i, node

    real(dp) :: xnorm, ynorm, znorm, area, face_speed

    integer                :: triangle_index, tn1, qn1, node1
    integer                :: triangle_corner
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer                :: quad_index
    integer                :: quad_corner
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight

    real(dp), dimension(3) :: rf

  continue

    !----------Triangular Faces----------

    loop_tris : do triangle_index = 1,bc(ib)%nbfacet

      corner_tris_loop : do triangle_corner = 1,3

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, triangle_index, triangle_corner                               &
           , 3, triangle_node, triangle_weight                                 &
           , xnorm, ynorm, znorm, area, face_speed )

        tn1 = triangle_node(1)

        if( tn1 > dof0 ) cycle

        if ( turbulent_convection == 0 ) then
          rf(1) = x(tn1)
          rf(2) = y(tn1)
          rf(3) = z(tn1)
        else
          rf(:) = 0._dp
          do i=1,3
            rf(1) = rf(1) + triangle_weight(i)*x(triangle_node(i))
            rf(2) = rf(2) + triangle_weight(i)*y(triangle_node(i))
            rf(3) = rf(3) + triangle_weight(i)*z(triangle_node(i))
          enddo
        endif

        call turb_flux( eqn_set, tn1, n_sta, n_q, n_turb,           &
                        turb, q_dof, ib, bc, x, y, z,               &
                        xnorm, ynorm, znorm, area, face_speed, res, &
                        gradx, grady, gradz, rf )

      enddo corner_tris_loop

    enddo loop_tris

    !----------Quadrilateral Faces----------

    loop_quads : do quad_index = 1,bc(ib)%nbfaceq

      corner_quad_loop : do quad_corner = 1, 4

        skip_other_twod_plane : if (twod) then
          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(quad_index,quad_corner))
          if( abs(y(node1)-yplane_2d) >= y_coplanar_tol ) &
            cycle corner_quad_loop
        end if skip_other_twod_plane

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, quad_index, quad_corner                                       &
           , 4, quad_node, quad_weight                                         &
           , xnorm, ynorm, znorm, area, face_speed )

        qn1 = quad_node(1)

        if ( qn1 > dof0 ) cycle

        if ( turbulent_convection == 0 ) then
          rf(1) = x(qn1)
          rf(2) = y(qn1)
          rf(3) = z(qn1)
        else
          rf(:) = 0._dp
          do i=1,4
            rf(1) = rf(1) + quad_weight(i)*x(quad_node(i))
            rf(2) = rf(2) + quad_weight(i)*y(quad_node(i))
            rf(3) = rf(3) + quad_weight(i)*z(quad_node(i))
          enddo
        endif

        call turb_flux( eqn_set, qn1, n_sta, n_q, n_turb,           &
                        turb, q_dof, ib, bc, x, y, z,               &
                        xnorm, ynorm, znorm, area, face_speed, res, &
                        gradx, grady, gradz, rf )

      enddo corner_quad_loop

    enddo loop_quads

    if ( origin < 3 ) return !non-agglomerated grid (elements exist)

    do i = 1,bc(ib)%nbnode

      node = bc(ib)%ibnode(i)

      rf(1) = x(node)
      rf(2) = y(node)
      rf(3) = z(node)

      if (node > dof0 ) cycle

      xnorm = bc(ib)%bxn(i) !directed areas
      ynorm = bc(ib)%byn(i)
      znorm = bc(ib)%bzn(i)
      area  = sqrt( xnorm**2 + ynorm**2 + znorm**2 )
      xnorm = xnorm/area
      ynorm = ynorm/area
      znorm = znorm/area

      face_speed = 0.0_dp
      if (need_grid_velocity) then
        face_speed = bc(ib)%bfacespeed(i)
      end if

      call turb_flux( eqn_set, node, n_sta, n_q, n_turb,                  &
                      turb, q_dof, ib, bc, x, y, z,                       &
                      xnorm, ynorm, znorm, area, face_speed, res,         &
                      gradx, grady, gradz, rf )

    end do

  end subroutine bc_turb_conv

!================================== BC_TURB_CONV_JACOB =======================80
!
! Boundary closure using convection for general turbulence model.
!
!=============================================================================80
  subroutine bc_turb_conv_jacob( eqn_set, n_sta, n_q, n_turb,                  &
                                 dof0, a_diag, turb, q_dof, g2m,               &
                                 ib, bc, elem, origin, x, y, z,                &
                                 dxdt, dydt, dzdt )

    integer,                             intent(in   ) :: eqn_set
    integer,                             intent(in   ) :: n_sta
    integer,                             intent(in   ) :: n_q
    integer,                             intent(in   ) :: n_turb
    integer,                             intent(in   ) :: dof0
    integer,                             intent(in   ) :: ib
    integer,                             intent(in   ) :: origin
    type(elem_type),   dimension(:),     intent(in   ) :: elem
    type(bcgrid_type), dimension(:),     intent(in   ) :: bc
    integer,           dimension(:),     intent(in   ) :: g2m
    real(dp),          dimension(:),     intent(in   ) :: x, y, z
    real(dp),          dimension(:),     intent(in   ) :: dxdt, dydt, dzdt
    real(dp),          dimension(:,:),   intent(in   ) :: turb, q_dof
    real(dp),          dimension(:,:,:), intent(inout) :: a_diag

    integer :: i, node

    real(dp) :: xnorm, ynorm, znorm, area, face_speed

    integer                :: triangle_index, tn1, qn1, node1
    integer                :: triangle_corner
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer                :: quad_index
    integer                :: quad_corner
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight

  continue

    !----------Triangular Faces----------

    loop_tris : do triangle_index = 1,bc(ib)%nbfacet

      corner_tris_loop : do triangle_corner = 1,3

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, triangle_index, triangle_corner                               &
           , 3, triangle_node, triangle_weight                                 &
           , xnorm, ynorm, znorm, area, face_speed )

        tn1 = triangle_node(1)

        if ( tn1 > dof0 ) cycle

        call turb_flux_jacobian( eqn_set, tn1, n_sta, n_q, n_turb,     &
                                 a_diag, turb, q_dof, g2m,             &
                                 ib, bc, x, y, z,                      &
                                 xnorm, ynorm, znorm, area, face_speed )

      enddo corner_tris_loop

    enddo loop_tris

    !----------Quadrilateral Faces----------

    loop_quads : do quad_index = 1,bc(ib)%nbfaceq

      corner_quad_loop : do quad_corner = 1, 4

        skip_other_twod_plane : if (twod) then
          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(quad_index,quad_corner))
          if( abs(y(node1)-yplane_2d) >= y_coplanar_tol ) &
            cycle corner_quad_loop
        end if skip_other_twod_plane

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, quad_index, quad_corner                                       &
           , 4, quad_node, quad_weight                                         &
           , xnorm, ynorm, znorm, area, face_speed )

        qn1 = quad_node(1)

        if ( qn1 > dof0 ) cycle

        call turb_flux_jacobian( eqn_set, qn1, n_sta, n_q, n_turb,     &
                                 a_diag, turb, q_dof, g2m,             &
                                 ib, bc, x, y, z,                      &
                                 xnorm, ynorm, znorm, area, face_speed )

      enddo corner_quad_loop

    enddo loop_quads

    if ( origin < 3 ) return !non-agglomerated grid (elements exist)

    do i = 1,bc(ib)%nbnode

      node = bc(ib)%ibnode(i)

      if (node > dof0 ) cycle

      xnorm = bc(ib)%bxn(i) !directed areas
      ynorm = bc(ib)%byn(i)
      znorm = bc(ib)%bzn(i)
      area  = sqrt( xnorm**2 + ynorm**2 + znorm**2 )
      xnorm = xnorm/area
      ynorm = ynorm/area
      znorm = znorm/area

      face_speed = 0.0_dp
      if (need_grid_velocity) then
        face_speed = bc(ib)%bfacespeed(i)
      end if

      call turb_flux_jacobian( eqn_set, node, n_sta, n_q, n_turb,    &
                               a_diag, turb, q_dof, g2m,             &
                               ib, bc, x, y, z,                      &
                               xnorm, ynorm, znorm, area, face_speed )
    enddo

  end subroutine bc_turb_conv_jacob

!================================ GET_TURB_EXT ===============================80
!
! Get external turbulence.
!
!=============================================================================80
  subroutine get_turb_ext( eqn_set, turb_ext, n_q, n_turb,                     &
                           ib, bc, x_ext, y_ext, z_ext )

    integer,                              intent(in ) :: eqn_set
    integer,                              intent(in ) :: n_q
    integer,                              intent(in ) :: n_turb
    integer,                              intent(in ) :: ib
    type(bcgrid_type), dimension(:),      intent(in ) :: bc
    real(dp),                             intent(in ) :: x_ext, y_ext, z_ext
    real(dp),          dimension(n_turb), intent(out) :: turb_ext

    integer :: ierr

    real(dp) :: vt

    real(dp), dimension(n_q) :: q_exact

  continue

    turb_ext(1:n_turb) = turbulence_exterior(1:n_turb)

    if ( ic_exact ) then

      call exact_q( eqn_set, n_q, x_ext, y_ext, z_ext, q_exact, type = 1)

       turb_ext(1:n_turb) = q_exact(n_q-n_turb+1:n_q)

    elseif( bc(ib)%ibc == lisbon_inflow_profile ) then

      call set_q_backstep_vt(0, x_ext, z_ext, vt, ierr )

      turb_ext(1:n_turb) = vt

    end if

  end subroutine get_turb_ext

!================================ TURB_FLUX ==================================80
!
! Convective flux turbulence.
!
!=============================================================================80
  subroutine turb_flux( eqn_set, node, n_sta, n_q, n_turb,                     &
                        turb, q_dof, ib, bc, x, y, z,                          &
                        xnorm, ynorm, znorm, area, face_speed, res,            &
                        gradx, grady, gradz, rf )

    integer,                           intent(in   ) :: eqn_set
    integer,                           intent(in   ) :: node
    integer,                           intent(in   ) :: n_sta
    integer,                           intent(in   ) :: n_q
    integer,                           intent(in   ) :: n_turb
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),                          intent(in   ) :: xnorm, ynorm, znorm
    real(dp),                          intent(in   ) :: area, face_speed
    real(dp),          dimension(:),   intent(in   ) :: x, y, z
    real(dp),          dimension(:,:), intent(in   ) :: turb, q_dof
    real(dp),          dimension(:,:), intent(inout) :: res
    real(dp),          dimension(:,:), intent(in)    :: gradx, grady, gradz
    real(dp), dimension(3),            intent(in)    :: rf

    integer :: t, eq, n1, n2

    real(dp) :: u, v, w, ubar, uplus, uminus, tarea

    real(dp), dimension(n_turb) :: turb_ext, turb_int, flux

  continue

    n1 = n_q - n_turb + 1
    n2 = n_q

    tarea = t_conv*area

    u = q_dof(2,node)
    v = q_dof(3,node)
    w = q_dof(4,node)

    ubar   = u*xnorm + v*ynorm + w*znorm - face_speed

    uplus  = 0.5_dp*( ubar + aharten(ubar,ubar_eps) )
    uminus = 0.5_dp*( ubar - aharten(ubar,ubar_eps) )

    if ( turbulent_convection == 0 ) then
      turb_int(1:n_turb) = turb(1:n_turb,node)
    else
      turb_int(1:n_turb) = turb(1:n_turb,node)                   &
                         + gradx(n1:n2,node)*( rf(1) - x(node) ) &
                         + grady(n1:n2,node)*( rf(2) - y(node) ) &
                         + gradz(n1:n2,node)*( rf(3) - z(node) )
    endif

    call get_turb_ext( eqn_set, turb_ext, n_q, n_turb, &
                       ib, bc, rf(1), rf(2), rf(3) )

    flux(1:n_turb) = uplus *turb_int(1:n_turb)  &
                   + uminus*turb_ext(1:n_turb)

    do t=1,n_turb
      eq = n_sta + t - 1
      res(eq,node) = res(eq,node) + tarea*flux(t)
    enddo

  end subroutine turb_flux

!================================== TURB_FLUX_JACOBIAN =======================80
!
! Jacobians of convective flux for turbulence.
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_flux_jacobian( eqn_set, node, n_sta, n_q, n_turb,            &
                                 a_diag, turb, q_dof, g2m,                     &
                                 ib, bc, x, y, z,                              &
                                 xnorm, ynorm, znorm, area, face_speed )

    integer,                             intent(in   ) :: eqn_set
    integer,                             intent(in   ) :: node
    integer,                             intent(in   ) :: n_sta
    integer,                             intent(in   ) :: n_q
    integer,                             intent(in   ) :: n_turb
    integer,                             intent(in   ) :: ib
    type(bcgrid_type), dimension(:),     intent(in   ) :: bc
    integer,           dimension(:),     intent(in   ) :: g2m
    real(dp),                            intent(in   ) :: xnorm, ynorm, znorm
    real(dp),                            intent(in   ) :: area, face_speed
    real(dp),          dimension(:),     intent(in   ) :: x, y, z
    real(dp),          dimension(:,:),   intent(in   ) :: turb, q_dof
    real(dp),          dimension(:,:,:), intent(inout) :: a_diag

    integer :: j, row, t, eq

    real(dp) :: u, v, w, ubar, uplus, duplus, duminus, tarea

    real(dp), dimension(n_turb) :: turb_ext, turb_int
    real(dp), dimension(4)      :: dfluxdq

  continue

    tarea = t_conv*area

    turb_int(1:n_turb) = turb(1:n_turb,node)

    call get_turb_ext( eqn_set, turb_ext, n_q, n_turb,  &
                       ib, bc, x(node), y(node), z(node) )

    u = q_dof(2,node)
    v = q_dof(3,node)
    w = q_dof(4,node)

    ubar  = u*xnorm + v*ynorm + w*znorm - face_speed

    uplus  = 0.5_dp*( ubar + aharten(ubar,ubar_eps) )

    row = g2m(node)
    do t=1,n_turb
      eq = n_sta + t - 1
      a_diag(eq,eq,row) = a_diag(eq,eq,row) + tarea*uplus
    end do

    if ( tightly_couple ) then

      duplus  = 0.5_dp*( 1._dp + daharten(ubar,ubar_eps) )
      duminus = 0.5_dp*( 1._dp - daharten(ubar,ubar_eps) )

      do t=1,n_turb
        eq = n_sta + t - 1

        dfluxdq(1:4) = dfdubar( eqn_set, duplus, duminus, node, &
                                xnorm, ynorm, znorm, q_dof,     &
                                turb_int(t), turb_ext(t) )

        do j=1,4
          a_diag(eq,j,row) = a_diag(eq,j,row) + tarea*dfluxdq(j)
        enddo
      end do
    endif

  end subroutine turb_flux_jacobian

!============================== BC_STRONG_TURB_RES_NC ========================80
!
! Set residuals for strong boundary conditions.
!
!=============================================================================80
  subroutine bc_strong_turb_res_nc( grid, soln )

    use averagings,    only : invoke_averaging, averaging_rhs

    type(grid_type), intent(in   ) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: ib, ibc, n_sta, igrid, n_end, n_mf

  continue

    n_mf = soln%n_q - soln%n_turb

    igrid = grid%igrid

    if ( invoke_averaging ) then

      if ( .not.tightly_couple ) then

        n_sta = 1
        n_end = soln%n_turb
        call averaging_rhs( grid%igrid, soln%eqn_set, n_sta, n_end, &
                            soln%turb, soln%turbres, n_mf, .false. )
      else

        call lmpi_conditional_stop(1,'Not coded:bc_strong_turb_res_nc')

      endif

    endif

    strong_bc_dirichlet : do ib = 1, grid%nbound
        if ( skeleton > 20 ) write(*,"(1x,a,i0,a,i4,a,i6)") &
        'bc_strong_turb_res_nc: Grid=',igrid,' ib=',ib,     &
        ', ibc =', grid%bc(ib)%ibc

      ibc = grid%bc(ib)%ibc

      if ( twod .and. bc_ignore_2d(ibc) ) cycle

      if ( bc_strong_turb(ibc)      .or. &
           ibc == dirichlet         .or. &
           ibc == dirichlet_viscous      ) then

        call bc_name_index(ibc,bc_name,.true.)
        if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
        'Grid=',igrid,' ib=',ib,' turb-res:bc=',trim(bc_name)

        if ( .not.tightly_couple ) then
          n_sta = 1
          call dirichlet_turb_rhs( n_sta, soln%turbres,          &
           soln%eqn_set, grid%nnodes0, soln%n_q,  soln%n_turb,   &
           soln%q_dof, soln%turb, ib, grid%bc, grid%x, grid%y,   &
           grid%z, soln%bcsoln )
        else
          n_sta = soln%n_q-soln%n_turb+1
          call dirichlet_turb_rhs( n_sta, soln%res,              &
           soln%eqn_set, grid%nnodes0, soln%n_q,  soln%n_turb,   &
           soln%q_dof, soln%turb, ib, grid%bc, grid%x, grid%y,   &
           grid%z, soln%bcsoln )
        endif

      endif

!     need to insure appropriate dirichlet BCs
!     on symmetry for 7-eqn models
      if ( soln%n_turb == 7 .and. (ibc == symmetry_x .or.         &
           ibc == symmetry_y .or. ibc == symmetry_z) ) then

        if ( .not.tightly_couple ) then
          n_sta = 1
          call dirichlet_turb_rhs_sym7( n_sta, soln%turbres,      &
               grid%nnodes0, soln%turb, ib, grid%bc )
        else
          n_sta = soln%n_q-soln%n_turb+1
          call dirichlet_turb_rhs_sym7( n_sta, soln%res,          &
               grid%nnodes0, soln%turb, ib, grid%bc )
        endif

      endif

      if ( ibc == dirichlet_discrete ) then

        call bc_name_index(ibc,bc_name,.true.)
        if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
        'Grid=',igrid,' ib=',ib,' turb-res:bc=',trim(bc_name)

        if ( .not.tightly_couple ) then
          n_sta = 1
          call dirichlet_discrete_turb_rhs( n_sta, soln%turbres, &
           grid%nnodes0, soln%n_q,  soln%n_turb,                 &
           soln%turb, ib, grid%bc, b_discrete(igrid)%b_q(ib)%q )
        else
          n_sta = soln%n_q-soln%n_turb+1
          call dirichlet_discrete_turb_rhs( n_sta, soln%res,     &
           grid%nnodes0, soln%n_q,  soln%n_turb,                 &
           soln%turb, ib, grid%bc, b_discrete(igrid)%b_q(ib)%q )
        endif

      end if

    end do strong_bc_dirichlet

!SP note: dirichlet_lisbon needs to be treated more generally.!FIXME
!SP note: no exact solution but profile specified via q_exact.!FIXME
!SP note: dirichlet_lisbon needs to be treated more generally.!FIXME

    dirichlet_lisbon_FIXME : do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc /= dirichlet_lisbon ) cycle

      call bc_name_index(ibc,bc_name,.true.)
      if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
      'Grid=',igrid,' ib=',ib,' turb-res:bc=',trim(bc_name)

      n_sta = 1
      call dirichlet_turb_rhs( n_sta, soln%turbres,              &
           soln%eqn_set, grid%nnodes0, soln%n_q,  soln%n_turb,   &
           soln%q_dof, soln%turb, ib, grid%bc, grid%x, grid%y,   &
           grid%z, soln%bcsoln )

    end do dirichlet_lisbon_FIXME

   end subroutine bc_strong_turb_res_nc

!==============================  BC_STRONG_TURB_JACOBIAN_NC ==================80
!
! Set jacobians for strong boundary conditions.
!
!=============================================================================80
  subroutine bc_strong_turb_jacobian_nc( grid, soln, crow )

    use turbulence_info,   only : turbulence_model_int,                        &
                                  gamma_ret_sst,    WilcoxRSM_w2006,           &
                                  WilcoxRSM_w2006c, SSGLRR_RSM_w2012_SD,       &
                                  SSGLRR_RSM_w2012
        use averagings,    only : invoke_averaging, averaging_lhs

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: ib, ibc, n_sta, n_end, n_turb_assign, igrid, n_mf
    integer :: n_sta_dtau

  continue

    igrid = grid%igrid

    n_mf = soln%n_q - soln%n_turb

    if ( invoke_averaging ) then

      if ( .not.tightly_couple ) then
        n_sta = 1
        n_end = soln%n_turb
        n_sta_dtau = n_mf + 1
        call averaging_lhs( igrid, soln%eqn_set, n_sta, n_end, soln%turb, &
                            soln%a_turb_diag, soln%a_turb_off,            &
                            soln%dtau_term, n_sta_dtau,                   &
                            crow%iam, crow%jam, crow%m2g, n_mf, .false. )
      else
        if ( invoke_averaging ) call lmpi_conditional_stop(1,&
        'Not coded:bc_strong_turb_jacobian_nc')
      endif

    end if

    strong_bc_dirichlet : do ib = 1, grid%nbound

      ibc = grid%bc(ib)%ibc

      if ( twod .and. bc_ignore_2d(ibc) ) cycle

      if ( bc_strong_turb(ibc)       .or. &
           ibc == dirichlet          .or. &
           ibc == dirichlet_viscous  .or. &
           ibc == dirichlet_discrete      ) then

        call bc_name_index(ibc,bc_name,.true.)
        if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
        'Grid=',grid%igrid,' ib=',ib,' turb-J:bc=',trim(bc_name)

        n_turb_assign = soln%n_turb
!       for this model, dirichlet only for turb eqns 1 and 2
        if ( turbulence_model_int == gamma_ret_sst) then
          n_turb_assign = 2
        else if ( turbulence_model_int == WilcoxRSM_w2006 .or.           &
                  turbulence_model_int == WilcoxRSM_w2006c.or.           &
                  turbulence_model_int == SSGLRR_RSM_w2012_SD .or.       &
                  turbulence_model_int == SSGLRR_RSM_w2012 ) then
          n_turb_assign = 7
        end if
        if ( .not.tightly_couple ) then
          n_sta = 1
          n_end = n_turb_assign
          n_sta_dtau = n_mf + 1
          call dirichlet_lhs( n_sta, n_end, grid%nnodes0,              &
            soln%a_turb_diag, soln%a_turb_off,                         &
            soln%dtau_term, n_sta_dtau,                                &
            crow%iam, crow%g2m, ib, grid%bc )
        else
          n_sta = soln%n_q-soln%n_turb+1
          n_end = n_sta + n_turb_assign - 1
          n_sta_dtau = n_mf + 1
          call dirichlet_lhs( n_sta, n_end, grid%nnodes0,              &
            soln%a_diag, soln%a_off,                                   &
            soln%dtau_term, n_sta_dtau,                                &
            crow%iam, crow%g2m, ib, grid%bc )
        endif

      end if

!     Jacobian for appropriate dirichlet BCs
!     on symmetry for 7-eqn models
      if ( soln%n_turb == 7 .and. (ibc == symmetry_x .or.         &
           ibc == symmetry_y .or. ibc == symmetry_z) ) then

        if ( .not.tightly_couple ) then
          n_sta = 1
          n_sta_dtau = n_mf + 1
          call dirichlet_lhs_sym7( n_sta, grid%nnodes0,                &
            soln%a_turb_diag, soln%a_turb_off,                         &
            soln%dtau_term, n_sta_dtau,                                &
            crow%iam, crow%g2m, ib, grid%bc )
        else
          n_sta = soln%n_q-soln%n_turb+1
          n_sta_dtau = n_mf + 1
          call dirichlet_lhs_sym7( n_sta, grid%nnodes0,                &
            soln%a_diag, soln%a_off,                                   &
            soln%dtau_term, n_sta_dtau,                                &
            crow%iam, crow%g2m, ib, grid%bc )
        endif

      end if

    end do strong_bc_dirichlet

!SP note: dirichlet_lisbon needs to be treated more generally.!FIXME
!SP note: no exact solution but profile specified via q_exact.!FIXME
!SP note: dirichlet_lisbon needs to be treated more generally.!FIXME

    dirichlet_lisbon_FIXME : do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc /= dirichlet_lisbon ) cycle

      if ( skeleton > 1 ) write(*,*) 'Enforce dirichlet_lisbon bc...RHS'

      call bc_name_index(ibc,bc_name,.true.)
      if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
      'Grid=',grid%igrid,' ib=',ib,' turb-J:bc=',trim(bc_name)
      n_sta_dtau = n_mf + 1
      call dirichlet_lhs( 1, soln%n_turb, grid%nnodes0,            &
             soln%a_turb_diag, soln%a_turb_off,                    &
             soln%dtau_term, n_sta_dtau,                           &
             crow%iam, crow%g2m , ib, grid%bc)

    end do dirichlet_lisbon_FIXME

  end subroutine bc_strong_turb_jacobian_nc


!============================== BC_STRONG_TURB_Q_NC ==========================80
!
! Set strong boundary condition corrections.
!
!=============================================================================80
  subroutine bc_strong_turb_q_nc( grid, soln )

    use averagings,    only : invoke_averaging

    type(grid_type), intent(in   ) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: ib, ibc, igrid

  continue

    if ( invoke_averaging ) call lmpi_conditional_stop(1,&
    'Not coded:bc_strong_turb_q_nc')

    igrid = grid%igrid

    if ( ic_exact .or. hanim ) then

    bc_dirichlet : do ib = 1, grid%nbound

      ibc = grid%bc(ib)%ibc

      if ( twod .and. bc_ignore_2d(ibc) ) cycle

      if ( bc_strong_turb(ibc)      .or. &
           ibc == dirichlet         .or. &
           ibc == dirichlet_viscous      ) then

        call bc_name_index(ibc,bc_name,.true.)
         if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
         'Grid=',igrid,' ib=',ib,' turb-q:bc=',trim(bc_name)
        call dirichlet_turb( soln%eqn_set, grid%nnodes0,                       &
             soln%n_q,           soln%n_turb,                                  &
             soln%turb,          ib, grid%bc, grid%x, grid%y, grid%z )

      end if

!     need to insure appropriate dirichlet BCs
!     on symmetry for 7-eqn models
      if ( soln%n_turb == 7 .and. (ibc == symmetry_x .or.         &
           ibc == symmetry_y .or. ibc == symmetry_z) ) then

        call dirichlet_turb_sym7( grid%nnodes0, soln%turb, ib, grid%bc )

      end if

      if ( ibc == dirichlet_discrete ) then

        call bc_name_index(ibc,bc_name,.true.)
         if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
         'Grid=',igrid,' ib=',ib,' turb-q:bc=',trim(bc_name)
        call dirichlet_discrete_turb( grid%nnodes0,                       &
             soln%n_q,           soln%n_turb,                             &
             soln%turb,          ib, grid%bc, b_discrete(igrid)%b_q(ib)%q )

      end if

    enddo bc_dirichlet

    endif

  end subroutine bc_strong_turb_q_nc

!============================== BC_STRONG_TURB_DQ_NC =========================80
!
! Set residuals for strong boundary conditions.
!
!=============================================================================80
  subroutine bc_strong_turb_dq_nc( grid, soln, crow )

    use averagings,    only : invoke_averaging

    type(grid_type),           intent(in   ) :: grid
    type(soln_type),           intent(inout) :: soln
    type(crow_flow), optional, intent(in   ) :: crow

    integer :: ib, ibc, n_sta

  continue

    if ( invoke_averaging ) call lmpi_conditional_stop(1,&
    'Not coded:bc_strong_turb_dq_nc')

    if ( ic_exact .or. hanim ) then

    bc_dirichlet : do ib = 1, grid%nbound

      ibc = grid%bc(ib)%ibc
      if ( twod .and. bc_ignore_2d(ibc) ) cycle

      if ( bc_strong_turb(ibc)       .or. &
           ibc == dirichlet          .or. &
           ibc == dirichlet_viscous  .or. &
           ibc == dirichlet_discrete      ) then

        call bc_name_index(ibc,bc_name,.true.)
        if ( present( crow ) ) then
          if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
          'Grid=',grid%igrid,' ib=',ib,'   CS turb-dq:bc=',trim(bc_name)

          n_sta = 1
          call dirichlet_dturb_rhs( n_sta, soln%turbres,        &
               grid%nnodes0, soln%n_turb, ib, grid%bc, crow%g2m )
        else
          if ( skeleton > 1 ) write(*,"(1x,a,i0,a,i4,2a)") &
          'Grid=',grid%igrid,' ib=',ib,' FAS turb-dq:bc=',trim(bc_name)
          n_sta = 1
          call dirichlet_dturb_rhs( n_sta, soln%turbres, &
               grid%nnodes0, soln%n_turb, ib, grid%bc )
        endif
      end if

!     need to insure appropriate dirichlet BCs
!     on symmetry for 7-eqn models
      if ( soln%n_turb == 7 .and. (ibc == symmetry_x .or.         &
           ibc == symmetry_y .or. ibc == symmetry_z) ) then

        if ( present( crow ) ) then
          n_sta = 1
          call dirichlet_dturb_rhs_sym7( n_sta, soln%turbres,        &
               grid%nnodes0, ib, grid%bc, crow%g2m )
        else
          n_sta = 1
          call dirichlet_dturb_rhs_sym7( n_sta, soln%turbres, &
               grid%nnodes0, ib, grid%bc )
        endif
      end if

    enddo bc_dirichlet

    endif

   end subroutine bc_strong_turb_dq_nc

!=============================== DIRICHLET_TURB_RHS ==========================80
!
! Enforce strong residuals.
!
!=============================================================================80

  subroutine dirichlet_turb_rhs( n_sta, res, eqn_set, nnodes0, n_q, n_turb,    &
                                 qnode, turb, ib, bc, x, y, z, bcsoln )

    use thermo,          only : conserved_q_type, q_type, etop
    use turb_2eqn_routines,       only : wall_turbulence_kw_sst
    use turb_kw_lag,      only : wall_turbulence_kw_lag
    use turb_sst,         only : wall_turbulence_sst
    use turb_wilcox_kw88, only : wall_turbulence_wilcox_kw88
    use turb_sst_kkl,     only : wall_turbulence_sst_kkl
    use turb_4eqn,        only : wall_turbulence_gammaretsst
    use turb_7eqn,        only : wall_turbulence_stressomega
    use solution_types,   only : compressible
    use turbulence_info,  only : turbulence_model_int, turbulence_model, sa,   &
                                des,              sa_neg,        des_neg,      &
                                abid_linear,                     menter_sst,   &
                                kw_sst,           kw_sst2003,    bsl,          &
                                sst_v,            sst,           sst_2003,     &
                                wilcox2006,       wilcox2006_v,  wilcox1988,   &
                                wilcox1988_v,     wilcox_kw88p,  wilcox_kw88,  &
                                wilcox_kw98,      wilcox_kw06,   wilcox_kw06p, &
                                wilcox_asm,       EASMko2003_S,  kw_des,       &
                                easm_ddes,        wilcox_les,    hrles,        &
                                kw_lag,           k_kL_MEAH2013, asbm_sst,     &
                                gamma_ret_sst,    WilcoxRSM_w2006,             &
                                WilcoxRSM_w2006c, SSGLRR_RSM_w2012_SD,         &
                                SSGLRR_RSM_w2012, sst_kkl, ras_2011

    integer,                           intent(in   ) :: n_sta
    integer,                           intent(in   ) :: eqn_set
    integer,                           intent(in   ) :: nnodes0
    integer,                           intent(in   ) :: n_q
    integer,                           intent(in   ) :: n_turb
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:,:), intent(in   ) :: turb, qnode
    real(dp),          dimension(:,:), intent(inout) :: res
    real(dp),          dimension(:),   intent(in   ) :: x, y, z
    type(bcsoln_type), dimension(:),   intent(in   ) :: bcsoln

    integer :: i, node, eq, ibc, ierr, n_mf, n_turb_assign

    real(dp)                 :: slen_wall
    real(dp)                 :: omega_wf
    real(dp)                 :: tke_off
    real(dp), dimension(n_q) :: qexact, turb_wall, qlocal

  continue

   if ( skeleton > 2 )                                                         &
   write(*,*) 'Calling wall_turbulence in dirichlet_turb_rhs:....',            &
   turbulence_model
   if ( skeleton > 2 ) &
   write(*,*) ' dirichlet_turb_rhs>exact_q>ic_exact:.....',ic_exact

    n_mf = n_q - n_turb
    n_turb_assign = n_turb

    ierr = 0
    if ( eqn_set == compressible ) ierr = conserved_q_type - q_type
    call lmpi_conditional_stop(ierr,'q_type:dirichlet_turb_rhs')

    ibc = bc(ib)%ibc

    qexact(:) = 0._dp
    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle

      if ( ic_exact ) then
        call exact_q( eqn_set, n_q,                           &
                       x(node), y(node), z(node), qexact )
      else if (                                               &
              turbulence_model_int == abid_linear             &
         .or. turbulence_model_int == menter_sst              &
         .or. turbulence_model_int == kw_sst                  &
         .or. turbulence_model_int == kw_sst2003              &
         .or. turbulence_model_int == bsl                     &
         .or. turbulence_model_int == sst_v                   &
         .or. turbulence_model_int == sst_2003                &
         .or. turbulence_model_int == wilcox2006              &
         .or. turbulence_model_int == wilcox2006_v            &
         .or. turbulence_model_int == wilcox1988              &
         .or. turbulence_model_int == wilcox1988_v            &
         .or. turbulence_model_int == wilcox_kw88p            &
         .or. turbulence_model_int == wilcox_kw98             &
         .or. turbulence_model_int == wilcox_kw06             &
         .or. turbulence_model_int == wilcox_kw06p            &
         .or. turbulence_model_int == wilcox_asm              &
         .or. turbulence_model_int == EASMko2003_S            &
         .or. turbulence_model_int == kw_des                  &
         .or. turbulence_model_int == easm_ddes               &
         .or. turbulence_model_int == wilcox_les              &
         .or. turbulence_model_int == hrles                   &
         .or. turbulence_model_int == k_kL_MEAH2013           &
         ) then

          slen_wall      = bc(ib)%slen_wall(i)
          qlocal(1:n_mf) = qnode(1:n_mf,node)
          tke_off        = bcsoln(ib)%gradn_sqrtk(i) * bcsoln(ib)%gradn_sqrtk(i)

          if ( eqn_set == compressible ) then
            call etop( 1, qlocal, n_mf, eqn_set, ignore=.true. )
          endif
          call wall_turbulence_kw_sst( eqn_set, ibc, slen_wall, qlocal         &
                                     , tke_off, turb_wall                      &
                                     , bcsoln(ib)%k_wf(i)                      &
                                     , bcsoln(ib)%omega_wf(i)                  &
                                     , bcsoln(ib)%mu_t_wf(i) )
          do eq=1,n_turb
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo

      !===================
      else if ( turbulence_model_int == sst ) then

          slen_wall      = bc(ib)%slen_wall(i)
          qlocal(1:n_mf) = qnode(1:n_mf,node)

          if ( eqn_set == compressible ) then
            call etop( 1, qlocal, n_mf, eqn_set, ignore=.true. )
          endif
          call wall_turbulence_sst( eqn_set, ibc, slen_wall, qlocal,           &
                                       turb_wall,                              &
                                       bcsoln(ib)%k_wf(i),                     &
                                       bcsoln(ib)%omega_wf(i),                 &
                                       bcsoln(ib)%mu_t_wf(i) )
          do eq=1,n_turb
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo
      !===================
      else if ( turbulence_model_int == wilcox_kw88 ) then

          slen_wall      = bc(ib)%slen_wall(i)
          qlocal(1:n_mf) = qnode(1:n_mf,node)

          if ( eqn_set == compressible ) then
            call etop( 1, qlocal, n_mf, eqn_set, ignore=.true. )
          endif
          call wall_turbulence_wilcox_kw88( eqn_set, ibc, slen_wall, qlocal,   &
               turb_wall, bcsoln(ib)%k_wf(i), bcsoln(ib)%omega_wf(i),          &
               bcsoln(ib)%mu_t_wf(i) )

          do eq=1,n_turb
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo
      !===================
      else if ( turbulence_model_int == sst_kkl ) then

          call wall_turbulence_sst_kkl( ibc, turb_wall                         &
                                     , bcsoln(ib)%k_wf(i)                      &
                                     , bcsoln(ib)%omega_wf(i) )
          do eq=1,n_turb
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo

        else if ( turbulence_model_int == kw_lag  ) then

          slen_wall      = bc(ib)%slen_wall(i)
          qlocal(1:n_mf) = qnode(1:n_mf,node)

          if ( eqn_set == compressible ) then
            call etop( 1, qlocal, n_mf, eqn_set, ignore=.true. )
          endif
          call wall_turbulence_kw_lag( eqn_set, ibc, slen_wall, qlocal,        &
                                     turb_wall,                                &
                                     bcsoln(ib)%k_wf(i),                       &
                                     bcsoln(ib)%omega_wf(i),                   &
                                     bcsoln(ib)%mu_t_wf(i) )

          do eq = 1, n_turb
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo

      else if ( turbulence_model_int == asbm_sst ) then
      ! Note: for now rely on wall quantities being set earlier in the code
          do eq=1,n_turb
            qexact(n_q - n_turb + eq) = turb(eq,node)
          enddo

      elseif ( turbulence_model_int == gamma_ret_sst ) then

          n_turb_assign = 2
          slen_wall      = bc(ib)%slen_wall(i)
          qlocal(1:n_mf) = qnode(1:n_mf,node)
          if ( eqn_set == compressible ) then
            call etop( 1, qlocal, n_mf, eqn_set, ignore=.true. )
          endif
          call wall_turbulence_gammaretsst( eqn_set, ibc, slen_wall, qlocal,   &
                                     turb_wall )
!         for this model, dirichlet only for turb eqns 1 and 2
          do eq=1,n_turb_assign
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo

      elseif ( turbulence_model_int == WilcoxRSM_w2006 .or.                    &
               turbulence_model_int == WilcoxRSM_w2006c.or.                    &
               turbulence_model_int == SSGLRR_RSM_w2012_SD .or.                &
               turbulence_model_int == SSGLRR_RSM_w2012 ) then

          slen_wall      = bc(ib)%slen_wall(i)
          omega_wf       = bcsoln(ib)%omega_wf(i)
          qlocal(1:n_mf) = qnode(1:n_mf,node)
          if ( eqn_set == compressible ) then
            call etop( 1, qlocal, n_mf, eqn_set, ignore=.true. )
          endif
          call wall_turbulence_stressomega( eqn_set, ibc, slen_wall, qlocal,   &
                                     turb_wall, omega_wf )
          do eq = 1, n_turb
            qexact(n_q - n_turb + eq) = turb_wall(eq)
          enddo

      elseif ( turbulence_model_int == sa            &
        .or.   turbulence_model_int == des           &
        .or.   turbulence_model_int == sa_neg        &
        .or.   turbulence_model_int == des_neg ) then

        eq = 1
        select case ( ibc )
          case ( viscous_solid, viscous_solid_trs )
            qexact(n_q - n_turb + eq) = 0.0_dp
          case ( viscous_wall_function, viscous_wf_trs )
            qexact(n_q - n_turb + eq) = bcsoln(ib)%k_wf(i)
          case default
            qexact(n_q - n_turb + eq) = 0.0_dp
        end select

      elseif ( turbulence_model_int == ras_2011 ) then

        eq = 1
        select case ( ibc )
          case ( viscous_solid, viscous_solid_trs )
            qexact(n_q - n_turb + eq) = 0.0_dp
          case default
            qexact(n_q - n_turb + eq) = 0.0_dp
        end select

      endif

      do eq = 1,n_turb_assign
        res(n_sta+eq-1,node) = turb(eq,node) - qexact(n_q - n_turb + eq)
      enddo

    end do

  end subroutine dirichlet_turb_rhs

!========================== DIRICHLET_TURB_RHS_SYM7 ==========================80
!
! Enforce strong residuals for particular 7-eqn turb eqns at symmetry.
!
!=============================================================================80

  subroutine dirichlet_turb_rhs_sym7( n_sta, res, nnodes0, turb, ib, bc )

    use bc_names,        only : symmetry_x, symmetry_y, symmetry_z

    integer,                           intent(in   ) :: n_sta
    integer,                           intent(in   ) :: nnodes0
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:,:), intent(in   ) :: turb
    real(dp),          dimension(:,:), intent(inout) :: res

    integer :: i, node, ibc

  continue

! only use this routine if 7-eqn model is being employed

    ibc = bc(ib)%ibc

    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle

        if ( ibc == symmetry_x ) then
          res(n_sta+4-1,node) = turb(4,node) - 0._dp
          res(n_sta+5-1,node) = turb(5,node) - 0._dp
        else if ( ibc == symmetry_y ) then
          res(n_sta+4-1,node) = turb(4,node) - 0._dp
          res(n_sta+6-1,node) = turb(6,node) - 0._dp
        else if ( ibc == symmetry_z ) then
          res(n_sta+5-1,node) = turb(5,node) - 0._dp
          res(n_sta+6-1,node) = turb(6,node) - 0._dp
        end if

    end do

  end subroutine dirichlet_turb_rhs_sym7

!=============================== DIRICHLET_DISCRETE_TURB_RHS =================80
!
! Enforce strong residuals.
!
!=============================================================================80

  subroutine dirichlet_discrete_turb_rhs( n_sta, res, nnodes0,                 &
                                          n_q, n_turb, turb, ib, bc, q )

    integer,                           intent(in   ) :: n_sta
    integer,                           intent(in   ) :: nnodes0
    integer,                           intent(in   ) :: n_q
    integer,                           intent(in   ) :: n_turb
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:,:), intent(in   ) :: turb, q
    real(dp),          dimension(:,:), intent(inout) :: res

    integer :: i, node, eq, ibn

  continue

    ibn = 0
    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle
      ibn = ibn + 1
      do eq = 1,n_turb
        res(n_sta+eq-1,node) = turb(eq,node) - q(n_q - n_turb + eq, ibn)
      enddo
    end do

  end subroutine dirichlet_discrete_turb_rhs

!=============================== DIRICHLET_TURB ==============================80
!
! Enforce strong q.
!
!=============================================================================80

  subroutine dirichlet_turb( eqn_set, nnodes0, n_q, n_turb,                    &
                             turb, ib, bc, x, y, z )

    integer,                           intent(in   ) :: eqn_set
    integer,                           intent(in   ) :: nnodes0
    integer,                           intent(in   ) :: n_q
    integer,                           intent(in   ) :: n_turb
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:,:), intent(inout) :: turb
    real(dp),          dimension(:),   intent(in   ) :: x, y, z

    integer :: i, node, eq

    real(dp), dimension(n_q) :: qexact

  continue

    qexact(:) = 0._dp
    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle
      if ( ic_exact ) call exact_q( eqn_set, n_q,                     &
                                    x(node), y(node), z(node), qexact )
      do eq = 1,n_turb
        turb(eq,node) = qexact(n_q - n_turb + eq)
      enddo
    end do

  end subroutine dirichlet_turb

!========================== DIRICHLET_TURB_SYM7 ==============================80
!
! Enforce strong q for particular 7-eqn turb eqns at symmetry.
!
!=============================================================================80

  subroutine dirichlet_turb_sym7( nnodes0, turb, ib, bc )

    use bc_names, only : symmetry_x, symmetry_y, symmetry_z

    integer,                           intent(in   ) :: nnodes0
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:,:), intent(inout) :: turb

    integer :: i, node, ibc

  continue

! only use this routine if 7-eqn model is being employed

    ibc = bc(ib)%ibc

    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle
      if ( ibc == symmetry_x ) then
        turb(4,node) = 0._dp
        turb(5,node) = 0._dp
      else if ( ibc == symmetry_y ) then
        turb(4,node) = 0._dp
        turb(6,node) = 0._dp
      else if ( ibc == symmetry_z ) then
        turb(5,node) = 0._dp
        turb(6,node) = 0._dp
      end if
    end do

  end subroutine dirichlet_turb_sym7

!=============================== DIRICHLET_DISCRETE_TURB =====================80
!
! Enforce strong q.
!
!=============================================================================80

  subroutine dirichlet_discrete_turb( nnodes0, n_q, n_turb,                    &
                                      turb, ib, bc, q )

    integer,                           intent(in   ) :: nnodes0
    integer,                           intent(in   ) :: n_q
    integer,                           intent(in   ) :: n_turb
    integer,                           intent(in   ) :: ib
    type(bcgrid_type), dimension(:),   intent(in   ) :: bc
    real(dp),          dimension(:,:), intent(inout) :: turb
    real(dp),          dimension(:,:), intent(in   ) :: q

    integer :: i, node, eq, ibn

  continue

    ibn = 0
    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle
      ibn = ibn + 1
      do eq = 1,n_turb
        turb(eq,node) = q(n_q - n_turb + eq, ibn)
      enddo
    end do

  end subroutine dirichlet_discrete_turb

!=============================== DIRICHLET_DTURB_RHS =========================80
!
! Enforce strong dq.
!
!=============================================================================80

  subroutine dirichlet_dturb_rhs( n_sta, res, nnodes0, n_turb,                 &
                                  ib, bc, g2m )

    integer,                                     intent(in   ) :: n_sta
    integer,                                     intent(in   ) :: nnodes0
    integer,                                     intent(in   ) :: n_turb
    integer,                                     intent(in   ) :: ib
    type(bcgrid_type), dimension(:),             intent(in   ) :: bc
    real(dp),          dimension(:,:),           intent(inout) :: res
    integer,           dimension(:),   optional, intent(in   ) :: g2m

    integer :: i, node, eq, mdof

  continue

    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle
      mdof = node
      if ( present( g2m ) ) mdof = g2m(node)
      do eq = 1,n_turb
        res(n_sta+eq-1,mdof) = 0._dp
      enddo
    end do

  end subroutine dirichlet_dturb_rhs

!========================== DIRICHLET_DTURB_RHS_SYM7 =========================80
!
! Enforce strong dq for particular 7-eqn turb eqns at symmetry.
!
!=============================================================================80

  subroutine dirichlet_dturb_rhs_sym7( n_sta, res, nnodes0, ib, bc, g2m )

    use bc_names,        only : symmetry_x, symmetry_y, symmetry_z

    integer,                                     intent(in   ) :: n_sta
    integer,                                     intent(in   ) :: nnodes0
    integer,                                     intent(in   ) :: ib
    type(bcgrid_type), dimension(:),             intent(in   ) :: bc
    real(dp),          dimension(:,:),           intent(inout) :: res
    integer,           dimension(:),   optional, intent(in   ) :: g2m

    integer :: i, node, mdof, ibc

  continue

! only use this routine if 7-eqn model is being employed

    ibc = bc(ib)%ibc

    do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)
      if ( node > nnodes0 ) cycle
      mdof = node
      if ( present( g2m ) ) mdof = g2m(node)
      if ( ibc == symmetry_x ) then
        res(n_sta+4-1,mdof) = 0._dp
        res(n_sta+5-1,mdof) = 0._dp
      else if ( ibc == symmetry_y ) then
        res(n_sta+4-1,mdof) = 0._dp
        res(n_sta+6-1,mdof) = 0._dp
      else if ( ibc == symmetry_z ) then
        res(n_sta+5-1,mdof) = 0._dp
        res(n_sta+6-1,mdof) = 0._dp
      end if
    end do

  end subroutine dirichlet_dturb_rhs_sym7

!=============================== DIRICHLET_LHS ===============================80
!
! Enforce dirichlet conditions at the next time level.
!
!=============================================================================80
  subroutine dirichlet_lhs( n_sta, n_end, nnodes0, a_diag, a_off,              &
                            dtau_term, n_sta_dtau, iam, g2m, ib, bc )

    integer, intent(in) :: n_sta, n_end, n_sta_dtau, nnodes0, ib
    type(bcgrid_type), dimension(:),     intent(in   ) :: bc
    integer,           dimension(:),     intent(in   ) :: iam, g2m
    real(dp),          dimension(:,:,:), intent(inout) :: a_diag
    real(odp),         dimension(:,:,:), intent(inout) :: a_off
    logical,           dimension(:,:),   intent(inout) :: dtau_term

    integer :: i, j, jstart, jend, eq1, eq2
    integer :: node, row

    real(dp) :: diag

  continue

    diag = 1._dp

    zero_diags : do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)

      if(node > nnodes0) cycle

      row = g2m(node)

      do eq1=n_sta,n_end
        do eq2=1,size(a_diag,1)
          a_diag(eq1,eq2,row) = 0._dp
        enddo
      enddo

      do eq1=n_sta,n_end
        a_diag(eq1,eq1,row) = diag
      enddo

      do eq1=n_sta_dtau,n_sta_dtau+n_end-n_sta
        dtau_term(eq1,row) = .false.
      enddo

    end do zero_diags

    zero_off_diags : do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)

      if(node > nnodes0) cycle

      row = g2m(node)

      jstart = iam(row)
      jend   = iam(row+1) - 1

      do j = jstart, jend

        do eq1=n_sta,n_end
          do eq2=1,size(a_diag,1)
            a_off(eq1,eq2,j) = 0._odp
          enddo
        enddo

      end do

    end do zero_off_diags

  end subroutine dirichlet_lhs

!=========================== DIRICHLET_LHS_SYM7 ==============================80
!
! Enforce dirichlet conditions at the next time level
! for particular 7-eqn turb eqns at symmetry.
!
!=============================================================================80
  subroutine dirichlet_lhs_sym7( n_sta, nnodes0, a_diag, a_off,                &
                            dtau_term, n_sta_dtau, iam, g2m, ib, bc )

    use bc_names,        only : symmetry_x, symmetry_y, symmetry_z

    integer, intent(in) :: n_sta, n_sta_dtau, nnodes0, ib
    type(bcgrid_type), dimension(:),     intent(in   ) :: bc
    integer,           dimension(:),     intent(in   ) :: iam, g2m
    real(dp),          dimension(:,:,:), intent(inout) :: a_diag
    real(odp),         dimension(:,:,:), intent(inout) :: a_off
    logical,           dimension(:,:),   intent(inout) :: dtau_term

    integer :: i, j, jstart, jend, eq2
    integer :: node, row, ibc

    real(dp) :: diag

  continue

! only use this routine if 7-eqn model is being employed

    diag = 1._dp

    ibc = bc(ib)%ibc

    zero_diags : do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)

      if(node > nnodes0) cycle

      row = g2m(node)

      if ( ibc == symmetry_x ) then
        do eq2=1,size(a_diag,1)
          a_diag(n_sta+4-1,eq2,row) = 0._dp
          a_diag(n_sta+5-1,eq2,row) = 0._dp
        enddo
      else if ( ibc == symmetry_y ) then
        do eq2=1,size(a_diag,1)
          a_diag(n_sta+4-1,eq2,row) = 0._dp
          a_diag(n_sta+6-1,eq2,row) = 0._dp
        enddo
      else if ( ibc == symmetry_z ) then
        do eq2=1,size(a_diag,1)
          a_diag(n_sta+5-1,eq2,row) = 0._dp
          a_diag(n_sta+6-1,eq2,row) = 0._dp
        enddo
      end if

      if ( ibc == symmetry_x ) then
        a_diag(n_sta+4-1,n_sta+4-1,row) = diag
        a_diag(n_sta+5-1,n_sta+5-1,row) = diag
      else if ( ibc == symmetry_y ) then
        a_diag(n_sta+4-1,n_sta+4-1,row) = diag
        a_diag(n_sta+6-1,n_sta+6-1,row) = diag
      else if ( ibc == symmetry_z ) then
        a_diag(n_sta+5-1,n_sta+5-1,row) = diag
        a_diag(n_sta+6-1,n_sta+6-1,row) = diag
      end if

      if ( ibc == symmetry_x ) then
        dtau_term(n_sta_dtau+4-1,row) = .false.
        dtau_term(n_sta_dtau+5-1,row) = .false.
      else if ( ibc == symmetry_y ) then
        dtau_term(n_sta_dtau+4-1,row) = .false.
        dtau_term(n_sta_dtau+6-1,row) = .false.
      else if ( ibc == symmetry_z ) then
        dtau_term(n_sta_dtau+5-1,row) = .false.
        dtau_term(n_sta_dtau+6-1,row) = .false.
      end if

    end do zero_diags

    zero_off_diags : do i = 1,bc(ib)%nbnode
      node = bc(ib)%ibnode(i)

      if(node > nnodes0) cycle

      row = g2m(node)

      jstart = iam(row)
      jend   = iam(row+1) - 1

      do j = jstart, jend

        if ( ibc == symmetry_x ) then
          do eq2=1,size(a_diag,1)
            a_off(n_sta+4-1,eq2,j) = 0._odp
            a_off(n_sta+5-1,eq2,j) = 0._odp
          enddo
        else if ( ibc == symmetry_y ) then
          do eq2=1,size(a_diag,1)
            a_off(n_sta+4-1,eq2,j) = 0._odp
            a_off(n_sta+6-1,eq2,j) = 0._odp
          enddo
        else if ( ibc == symmetry_z ) then
          do eq2=1,size(a_diag,1)
            a_off(n_sta+5-1,eq2,j) = 0._odp
            a_off(n_sta+6-1,eq2,j) = 0._odp
          enddo
        end if

      end do

    end do zero_off_diags

  end subroutine dirichlet_lhs_sym7

!================================ PERIODIC_RHS_T =============================80
!
! Add all of the secondary plane residuals to the primary plane residuals.
! Zero out the secondary plane residuals.
!
!=============================================================================80
  subroutine periodic_rhs_t( n_sta, n_turb, res )

    use periodics,       only : nperiodic, periodic_data
    use fun3d_constants, only : my_0

    integer,                  intent(in   ) :: n_sta, n_turb
    real(dp), dimension(:,:), intent(inout) :: res

    integer :: i, node1, node2, eq, t, j

  continue

    do i = 1, nperiodic
      node1 = periodic_data(i)%list(1)
      do j = 2, periodic_data(i)%n
        node2 = periodic_data(i)%list(j)
        do t = 1, n_turb
          eq = n_sta + t - 1
          res(eq,node1) = res(eq,node1) + res(eq,node2)
          res(eq,node2) = my_0
        end do
      end do
    end do

  end subroutine periodic_rhs_t

!================================ PERIODIC_LHS_T =============================80
!
!   Treat the LHS for periodic boundary conditions
!   Note this is only the diagonal term; the off-diagonals
!   will get treated during the actual linear solve
!
!=============================================================================80
  subroutine periodic_lhs_t( grid, soln, crow )

    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow
    use grid_types,     only : grid_type
    use bc_names,       only : viscous_solid, viscous_solid_trs,               &
                               viscous_wall_rough

    type(grid_type), intent(in   ) :: grid
    type(crow_flow), intent(in   ) :: crow
    type(soln_type), intent(inout) :: soln

    integer :: j, inode, ib, n_sta

    integer, dimension(grid%nnodes0) :: viscous_node_tag

  continue

    ! Set up a tag for nodes that are on viscous boundaries.
    ! (Inconsistent with meanflow treatment).

    viscous_node_tag = 0

    if ( .not.hanim ) then ! bypass to be consistent with meanflow
    do ib = 1, grid%nbound
      if ( grid%bc(ib)%ibc == viscous_solid     .or.                           &
           grid%bc(ib)%ibc == viscous_solid_trs .or.                           &
           grid%bc(ib)%ibc == viscous_wall_rough) then

        node_loop : do j = 1, grid%bc(ib)%nbnode
          inode = grid%bc(ib)%ibnode(j)
          if ( inode > grid%nnodes0 ) cycle node_loop
          viscous_node_tag(inode) = 1
        end do node_loop

      endif
    end do
    endif

    if ( .not.tightly_couple ) then
      n_sta = 1
      call periodic_diag_modify( n_sta, soln%n_turb, viscous_node_tag, &
                                 crow%g2m, soln%a_turb_diag )
    else
      n_sta = soln%n_q-soln%n_turb+1
      call periodic_diag_modify( n_sta, soln%n_turb, viscous_node_tag, &
                                 crow%g2m, soln%a_diag )
    endif

  end subroutine periodic_lhs_t

!================================ PERIODIC_DIAG_MODIFY =======================80
!
! Add all of the secondary plane diagonals to the primary plane diagonals,
! except if viscous
! Zero out the secondary plane diagonals (identity actually)
!
!=============================================================================80
  subroutine periodic_diag_modify(n_sta, n_turb, viscous_node_tag, g2m, a_diag)

    use periodics,       only : nperiodic, periodic_data
    use fun3d_constants, only : my_0, my_1

    integer,                    intent(in   ) :: n_sta, n_turb
    integer,  dimension(:),     intent(in   ) :: viscous_node_tag, g2m
    real(dp), dimension(:,:,:), intent(inout) :: a_diag

    integer :: i, node1, node2, row1, row2, eq, t, j

  continue

    do i = 1, nperiodic
      node1 = periodic_data(i)%list(1)
      row1 = g2m(node1)
      do j = 2, periodic_data(i)%n
        node2 = periodic_data(i)%list(j)
        row2  = g2m(node2)
        if ( viscous_node_tag(node1) /= 1 ) then
          do t = 1, n_turb
            eq = n_sta + t - 1
            a_diag(eq,:,row1) = a_diag(eq,:,row1) + a_diag(eq,:,row2)
          enddo
        endif
        do t = 1, n_turb
          eq = n_sta + t - 1
          a_diag(eq, :,row2) = my_0
          a_diag(eq,eq,row2) = my_1
        end do
      end do
    end do

  end subroutine periodic_diag_modify

  include 'aharten.f90'
  include 'daharten.f90'
  include 'dqc_via_duvw.f90'
  include 'dfdubar.f90'

end module turb_bc_nc

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-
!-----------------------------------------------------------------------------80

test_suite porous2

  integer, parameter  :: dp = selected_real_kind(P=15)

  real(dp), parameter       :: tol = 1.0e-8

! real(dp), parameter       :: turbulent_prandtl   =  0.72_dp
! real(dp), parameter       :: sutherland_constant =  198.6_dp
! real(dp), parameter       :: tref                =  540.0_dp

!=============================================================================80
!=============================================================================80
!=============================================================================80
test slater

  use fluid, only : setup_fluid_gamma, gamma, gm1, gp1
! use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
!                 , operator(-), operator(*), operator(/)           &
!                 , operator(**), ddt_sqrt

 
! real(dp),   dimension(5) :: q
! type(ddt5), dimension(5) :: q_ddt
  real(dp)                 :: p_0, t_0
  real(dp)                 :: p_wall, t_wall
  real(dp)                 :: p_plenum, t_plenum
  real(dp)                 :: actual
  real(dp)                 :: phi
  real(dp)                 :: area
  real(dp)                 :: q_sonic_s
  real(dp)                 :: mach
  real(dp)                 :: mach_guess
  real(dp)                 :: mdot
  real(dp)                 :: mdot_sonic_s
  real(dp)                 :: mdot_bleed
  real(dp)                 :: const1, const2, const3
  real(dp)                 :: f, dfdm
  real(dp)                 :: part1, part2, coef
  real(dp)                 :: dpart1dm, dpart2dm

! type(ddt5)               :: rho, u, v, w, p, e, unorm
  integer :: iter

  real(dp), parameter :: zero = 0.0_dp
  real(dp), parameter :: one  = 1.0_dp
  real(dp), parameter :: two  = 2.0_dp

  call setup_fluid_gamma

  const1 = 0.57799735_dp 
  const2 = 0.03069346_dp 
  const3 = 0.59361420_dp 

  mach_guess = 0.1_dp
  area     =  0.001_dp
  phi      =  0.05_dp
  p_wall   = one/gamma
  t_wall   = one

  p_plenum = 0.95_dp*p_wall
  t_plenum = 1.00_dp*t_wall

  q_sonic_s = const1                                                             &
            + const2 * p_plenum/p_wall                                           &
            - const3 * (p_plenum/p_wall)**2

  if ( q_sonic_s > zero ) then
    p_0      = p_wall
    t_0      = t_wall
  else
    p_0      = p_plenum
    t_0      = t_plenum
  end if

  mdot_sonic_s = p_wall * phi * area * ( sqrt(one/t_wall) ) * &
                 (gp1/two)**(-gp1/two*gm1 )
  
  mdot_bleed = q_sonic_s * mdot_sonic_s

  mach     =  0.05_dp
  mdot = mdot_bleed
  coef = -gp1/(two*gm1)
  iter = 0
  f    = one
  dfdm = one

  do while ( abs(f/dfdm) > 1.0e-8_dp .and. iter < 10 )
  f = p_0 * phi * area * mach * ( sqrt(one/t_0) ) * &
            (one + (gm1/two) *mach**2 )**coef - mdot

  part1    = p_0 * phi * area * mach * ( sqrt(one/t_0) )
  dpart1dm = p_0 * phi * area * ( sqrt(one/t_0) )

  part2    = (one + (gm1/two) *mach**2 )**coef

  dpart2dm = coef*(one + (gm1/two) *mach**2 )**(coef-one) * &
             gm1 * mach

  dfdm = part1 * dpart2dm + part2 * dpart1dm

  mach = mach - f/dfdm
  iter = iter + 1

  end do
  
    write(*,'(a,10(1x,f15.5))') 'q_sonic_s:........',q_sonic_s
    write(*,'(a,10(1x,f15.5))') 'mdot_sonic_s::....',mdot_sonic_s
    write(*,'(a,10(1x,f15.5))') 'mdot_bleed:.......',mdot_bleed
    write(*,'(a,10(1x,f15.5))') 'mach:.............',mach
    actual = slater_mdot ( area, phi, p_wall, t_wall, p_plenum )

    assert_equal_within(  mdot,  actual, tol)

    actual = slater_mach ( area, phi, p_wall, t_wall, p_plenum, t_plenum,      &
                        mdot, mach_guess )

   write(6,'(a,f15.10,a)') 'assert_equal_within( mach, actual, tol)'

assert_equal_within(  mach,  actual, tol)
 
end test
!=============================================================================80
!=============================================================================80
test node_based

  use fluid, only : setup_fluid_gamma, gamma

  integer                  :: nnodes0, nbnode
  integer, dimension(1)    :: ibnode
  integer                  :: n_momx, n_momy, n_momz, n_etot
  logical                  :: need_grid_velocity
  real(dp), dimension(1)   :: bxn, byn, bzn, bfacespeed
  real(dp), dimension(5,1) :: q_dof, res

  call setup_fluid_gamma

  nnodes0   = 1
  nbnode    = 1
  ibnode(1) = 1
  n_momx    = 2
  n_momy    = 3
  n_momz    = 4
  n_etot    = 5

  need_grid_velocity = .false.
  bxn(1)             = 0.0717_dp
  byn(1)             = 0.0_dp
  bzn(1)             = 0.0717_dp
  bfacespeed(1)      = 0.0_dp
  q_dof(1,1)         = 0.9_dp
  q_dof(n_momx,1)    = 0.001_dp
  q_dof(n_momy,1)    = 0.0_dp
  q_dof(n_momz,1)    = 0.001_dp
  q_dof(n_etot,1)    = 0.95*1.0_dp/gamma
  res                = 0.0_dp

   call porous_node_based ( nnodes0, nbnode, ibnode,        &
                            n_momx, n_momy, n_momz, n_etot, &
                            need_grid_velocity,             &
                            bxn, byn, bzn, bfacespeed, q_dof, res ) 

   write(6,'(a,f15.10,a)') 'assert_equal_within(',res(1,1),', res(1,1), tol)'
   write(6,'(a,f15.10,a)') 'assert_equal_within(',res(2,1),', res(2,1), tol)'
   write(6,'(a,f15.10,a)') 'assert_equal_within(',res(3,1),', res(3,1), tol)'
   write(6,'(a,f15.10,a)') 'assert_equal_within(',res(4,1),', res(4,1), tol)'
   write(6,'(a,f15.10,a)') 'assert_equal_within(',res(5,1),', res(5,1), tol)'

  assert_equal_within(   0.0000444189, res(1,1), tol)
  assert_equal_within(   0.0000020886, res(2,1), tol)
  assert_equal_within(   0.0000000000, res(3,1), tol)
  assert_equal_within(   0.0000020886, res(4,1), tol)
  assert_equal_within(   0.0001080416, res(5,1), tol)


end test


!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80

end test_suite

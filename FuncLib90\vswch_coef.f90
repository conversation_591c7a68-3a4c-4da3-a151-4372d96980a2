
!============================== VSWCH_COEF ===================================80
!
!  Computes the viscous switching coefficient used turn off the inviscid
!  switching coefficient on cell faces that have small cell reynolds numbers
!
!=============================================================================80

  pure function vswch_coef(roewat, rho, q2l, ubarl, q2r, ubarr, ubar, c,       &
                           vol1, vol2, area, power, MU_face)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_1, my_half
    use inviscid_flux,   only : re_min_vswch, re_max_vswch

    integer,  intent(in) :: power

    real(dp), intent(in) :: roewat
    real(dp), intent(in) :: rho
    real(dp), intent(in) :: q2l, ubarl, q2r, ubarr, ubar, c
    real(dp), intent(in) :: vol1, vol2, area
    real(dp), intent(in) :: MU_face

    real(dp)             :: vswch_coef

    real(dp) :: utngl, utngr, utang
    real(dp) :: RO_face, U_face, L_face, RE_face

  continue

    RO_face = rho
    utngl   = sqrt(max(my_0, q2l-ubarl*ubarl))
    utngr   = sqrt(max(my_0, q2r-ubarr*ubarr))
    utang   = utngl*roewat + utngr*(my_1 - roewat)
    U_face  = max(abs(ubar), utang) + c
    L_face  = my_half*(vol1 + vol2)/area

!   Compute the cell face reynolds number to make the coeff. behave as desired
!   vcoef = f(RE-face) when RE_min <= RE_face >= RE_max

    RE_face = RO_face*U_face*L_face/MU_face

!   Compute the viscous scaling coeff. such that the dissipation of the inviscid
!   scheme is minimized when the cell face Reynolds number is lower than RE_min
!   vswch_coef = f(RE-face) when RE_min <= RE_face >= RE_max

    vswch_coef = max(my_0, min(my_1, (RE_face-re_min_vswch)/                   &
                 (re_max_vswch-re_min_vswch)))**power
    vswch_coef = my_1 - vswch_coef

  end function vswch_coef

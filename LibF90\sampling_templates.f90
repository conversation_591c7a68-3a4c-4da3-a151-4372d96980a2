  module sampling_templates

  use kinddefs,           only : dp
  use lmpi,               only : lmpi_master, lmpi_bcast, lmpi_id,             &
                                 lmpi_nproc, lmpi_allgather,                   &
                                 lmpi_reduce, lmpi_conditional_stop
  use info_depr,          only : skeleton
  use sampling_headers,   only : verbose

  implicit none

  private

  public :: survey_template

contains

!=========================== SURVEY_TEMPLATE =================================80
!
! Extract flowfield data onto user-specified surfaces - sphere, box, plane, etc
!
!=============================================================================80

  subroutine survey_template( grid, soln, xyz_dim, sadj )

    use allocations,        only : my_alloc_ptr, my_realloc_ptr
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use info_depr,          only : twod
    use nml_global,         only : moving_grid
    use system_extensions,  only : se_flush
    use sampling_headers,   only : number_of_geometries, init_template         &
                                 , p2c_local_cells, p2c_local_bdy              &
                                 , p2c_local_face, p2c_local_elem              &
                                 , point_is_found                              &
                                 , point_is_found_global, number_of_points     &
                                 , type_of_data, have_sampling_points          &
                                 , crinkle, c2f, sample, nodal
    use sampling_funclib,   only : find_closet_element, line_survey_blanking   &
                                 , analyse_volume_statistics                   &
                                 , analyse_mesh_statistics, cell2face
    use solution_adj,       only : sadj_type

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: xyz_dim
    type(sadj_type), optional,       intent(in)    :: sadj

    integer                   :: element_set
    integer                   :: total_ncell
    character(len=3)          :: type_cell
    integer                   :: face_per_cell

    integer                               :: ivol

    integer                               :: total_survey_cells
    integer                               :: n_tria, n_tria_est
    integer,  dimension(:,:),     pointer :: local_trinode_map
    integer,  dimension(:  ),     pointer :: total_trias

    integer                               :: total_survey_edges
    integer                               :: edge, n_edges, n_edge_est
    real(dp), dimension(:),       pointer :: edge_donor_weight1
    real(dp), dimension(:),       pointer :: edge_donor_weight2
    integer,  dimension(:),       pointer :: edge_donor_node1
    integer,  dimension(:),       pointer :: edge_donor_node2

    real(dp), dimension(:),       allocatable :: edge_donor_weight
    integer,  dimension(:,:),     allocatable :: edge_donor_list
    integer,  dimension(:),       allocatable :: e2ln ! edge to local node
    integer,  dimension(:),       allocatable :: p2ln ! point to local node

! total sampling data
    integer                               :: ipts, stop_not_found
    integer                               :: n_lines
    integer                               :: test_dim
    integer                               :: total_points
    integer                               :: i
    integer                               :: tria

    real(dp), dimension(3)       :: n
    character(len=80)            :: geo_name

!    integer :: icell, node

  continue
!-----------------------------------------------------------------------------80
!--start-------slice_count----------------------------------------------------80
!-----------------------------------------------------------------------------80

    geometry_count:  do ivol = 1, number_of_geometries

      geo_name = sample(ivol)%geo_type_name

        initialize_template: if ( init_template(ivol) )  then

        select case ( geo_name )

          case ('boundary_points')

            p2c_local_cells(ivol,:) = 0
            p2c_local_bdy(ivol,:)   = 0
            p2c_local_face(ivol,:)  = 0
            p2c_local_elem(ivol,:)  = 0

            do ipts = 1, number_of_points(ivol)
              call point_survey_template( grid, ivol, ipts )

              if ( verbose .and. point_is_found(ivol,ipts) == 1 ) then
                write(6,'(a,7i5)') 'survey_template',lmpi_id, ivol,ipts, &
                 p2c_local_cells(ivol,ipts), p2c_local_elem(ivol,ipts),  &
                 p2c_local_bdy(ivol,ipts)
               endif
            enddo
            call lmpi_reduce( point_is_found, point_is_found_global )
            call lmpi_bcast( point_is_found_global )

!           if ( lmpi_master ) then
!           do ipts = 1, number_of_points(ivol)
!               write(6,'(a,7i5)') 'survey_template',ivol, ipts, &
!               p2c_local_cells(ivol,ipts), p2c_local_elem(ivol,ipts),  &
!               p2c_local_bdy(ivol,ipts), point_is_found_global(ivol,ipts)
!           enddo
!           endif

!           allocate(total_point_vector(lmpi_nproc)); total_point_vector = 0
!           call lmpi_allgather(local_count, total_cell_vector)
!           total_points = sum(total_point_vector)
            total_points = sum(point_is_found_global(ivol,:))

            if ( lmpi_master ) then
              if ( total_points == number_of_points(ivol) ) then
              write(*,'(a,i0,a,i0,a,i0,a)') 'For geometry# ',ivol, &
              ':  Number of point found/requested = ',             &
               total_points,'/',number_of_points(ivol),            &
               '.  No points lost.'
              else
                write(*,'(a,i0,a,i0,a,i0,a)') 'For geometry# ',ivol, &
                ':  Number of point found/requested = ',             &
                total_points,'/',number_of_points(ivol)
                do ipts = 1, number_of_points(ivol)
                  if ( point_is_found_global(ivol,ipts)/=0 ) cycle
                  write(*,'(15x,a,i0,a,i0,a,i0)') 'Point # ',ipts,' was lost.'
                enddo
                write(*,'(a,a,a,i0,a)') 'Consider either increasing',       &
                ' dist_tolerance in the namelist or moving the requested',  &
                ' point closer to the boundary.'
              endif
            endif

          case ('volume_points','streamsurface')

            p2c_local_cells(ivol,:) = 0
            p2c_local_bdy(ivol,:)   = 0
            p2c_local_face(ivol,:)  = 0
            p2c_local_elem(ivol,:)  = 0

            do ipts = 1, number_of_points(ivol)
              call point_survey_template( grid, ivol, ipts )
            enddo

            call lmpi_reduce( point_is_found, point_is_found_global )
            call lmpi_bcast( point_is_found_global )

            stop_not_found = 0

            do ipts = 1, number_of_points(ivol)
              if ( point_is_found_global(ivol,ipts) == 0 ) then
                call find_closet_element( grid, ivol, ipts )
              endif
            enddo
            if (lmpi_master) then
              do ipts = 1, number_of_points(ivol)
                if ( point_is_found_global(ivol,ipts) == 0 ) then
                  call se_flush()
                  stop_not_found = stop_not_found + 1
                endif
              enddo
            end if

            call lmpi_conditional_stop(stop_not_found,                         &
              "sampling_templates: point(s) requested not found in domain")

            if ( geo_name == 'streamsurface') then
              element_type_count:  do element_set = 1, grid%nelem

                type_cell     =  grid%elem(element_set)%type_cell
                face_per_cell =  grid%elem(element_set)%face_per_cell

                if ( verbose )                                                 &
                  write(*,*) lmpi_id,' Calculating cell2face',type_cell

                if ( type_cell == 'tet') then
                  if ( .not.allocated(c2f) ) then
                    allocate( c2f(face_per_cell,                               &
                              grid%elem(element_set)%ncell) )
                  endif

                  call cell2face( grid%elem(element_set)%c2n, c2f              &
                                , grid%elem(element_set)%ncell, grid%nnodesg )
                endif
              enddo element_type_count
            endif

          case ('schlieren')

            n_lines = sample(ivol)%number_of_lines

            if ( .not.associated(sample(ivol)%l2c_local_image) ) then
              call my_alloc_ptr( sample(ivol)%l2c_local_image, n_lines )
            else
              test_dim  = size(sample(ivol)%l2c_local_image,1)
              if ( test_dim /= n_lines ) then
              call my_realloc_ptr( sample(ivol)%l2c_local_image, n_lines )
              endif
            endif

              call line_survey_blanking( grid, ivol )

        case default ! everything else
!-----------------------------------------------------------------------------80
!             start volume template
!-----------------------------------------------------------------------------80
!     Allocate the storage for interpolation data; this needs to be based on the
!     number of cut triangles (not yet known), so we'll make an estimate and
!     then check as we go and reallocate if needed

          total_ncell = 0
          do element_set = 1, grid%nelem
            total_ncell = total_ncell + grid%elem(element_set)%ncell
          end do
          total_ncell = max(1, total_ncell)

          n_tria_est = 10*total_ncell**(2._dp/3._dp)
          n_edge_est = grid%nedge
          if (twod) n_tria_est = total_ncell

          n_tria  = 0
          n_edges = 0

sample_type_1: select case ( type_of_data(ivol) )
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
          case ('volume_statistics')

              call analyse_volume_statistics( grid, ivol )

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
          case ('mesh_statistics')

              call analyse_mesh_statistics( grid, ivol )

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
          case ('boundary')
            call my_alloc_ptr(edge_donor_node1,   n_edge_est)
            call my_alloc_ptr(edge_donor_node2,   n_edge_est)
            call my_alloc_ptr(edge_donor_weight1, n_edge_est)
            call my_alloc_ptr(edge_donor_weight2, n_edge_est)
            call boundary_survey_template( grid, ivol, geo_name                &
                                   , edge_donor_weight1, edge_donor_weight2    &
                                   , edge_donor_node1, edge_donor_node2        &
                                   , n, n_edges                                &
                                    )
            call my_alloc_ptr( sample(ivol)%edge_w1 , max(1,n_edges) )
            call my_alloc_ptr( sample(ivol)%edge_w2 , max(1,n_edges) )
            call my_alloc_ptr( sample(ivol)%edge_n1 , max(1,n_edges) )
            call my_alloc_ptr( sample(ivol)%edge_n2 , max(1,n_edges) )

            do edge = 1, n_edges
              sample(ivol)%edge_w1(edge) = edge_donor_weight1(edge)
              sample(ivol)%edge_w2(edge) = edge_donor_weight2(edge)
              sample(ivol)%edge_n1(edge) = edge_donor_node1(edge)
              sample(ivol)%edge_n2(edge) = edge_donor_node2(edge)
            enddo

            sample(ivol)%n_edges   = n_edges
            call my_alloc_ptr( sample(ivol)%total_edges, lmpi_nproc )
            sample(ivol)%total_edges = 0
            call lmpi_allgather(n_edges,sample(ivol)%total_edges)
            total_survey_edges              = sum(sample(ivol)%total_edges)
            sample(ivol)%total_survey_edges = total_survey_edges
            if ( skeleton > 1 ) then
              write(*,'(a,128i6)') 'total_edges    =',sample(ivol)%total_edges
              write(*,'(a,i12,a,i8)') 'total_survey_edges=',total_survey_edges &
                 , ' for volume = ', ivol
            endif
            deallocate(edge_donor_weight1)
            deallocate(edge_donor_weight2)
            deallocate(edge_donor_node1)
            deallocate(edge_donor_node2)
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
          case ('volume','integrated')

            call my_alloc_ptr(local_trinode_map, 4, max(1, n_tria_est))
            allocate ( edge_donor_list  ( 2, max(1, n_edge_est)))
            allocate ( edge_donor_weight(    max(1, n_edge_est)))
            allocate (              e2ln(    max(1, n_edge_est)))
            allocate (              p2ln(    max(1, n_edge_est)))


            call volume_survey_template( grid, soln, ivol, geo_name, xyz_dim   &
                                     , n, n_tria                               &
                                     , edge_donor_list, edge_donor_weight      &
                                     , e2ln, p2ln, local_trinode_map, n_edges  &
                                     , sadj )

! determine the total number of triangles in sample from all partitions
            call my_alloc_ptr( total_trias, lmpi_nproc ) ; total_trias = 0
            call lmpi_allgather( n_tria, total_trias )

! total triangles in sampling
            total_survey_cells              = sum(total_trias)
            sample(ivol)%total_survey_cells = total_survey_cells
            deallocate(total_trias)

! determine the total number of edges in sample from all partitions
            sample(ivol)%n_edges            = n_edges

!----------------------------------------------------------------------------80
!       Put the 'crinkle' back in to the sample
            if ( crinkle ) edge_donor_weight = 0.5_dp
            if ( nodal )   edge_donor_weight = 0.0_dp
!----------------------------------------------------------------------------80
!set up the edge/node based weights and list for each sampling volume
       call my_alloc_ptr( sample(ivol)%edge_weight ,    max(1,n_edges) )
       call my_alloc_ptr( sample(ivol)%edge_list   , 2, max(1,n_edges) )
       call my_alloc_ptr( sample(ivol)%trinode_map , 4, max(1,n_tria) )

!      sample(ivol)%edge_list  (:,1:n_edges) = edge_donor_list  (:,1:n_edges)
!      sample(ivol)%trinode_map(:,1:n_tria)  = local_trinode_map(:,1:n_tria)

            if ( n_edges > 0 ) then
              do edge = 1, n_edges
                  sample(ivol)%edge_list  (1,edge) = edge_donor_list(1,edge)
                  sample(ivol)%edge_list  (2,edge) = edge_donor_list(2,edge)
                  sample(ivol)%edge_weight(edge) =   edge_donor_weight(edge)
              enddo
              do tria = 1, n_tria
                do i = 1, 4
                  sample(ivol)%trinode_map(i,tria)  = local_trinode_map(i,tria)
                enddo
              enddo
            endif

            sample(ivol)%n_tria   = n_tria

            if ( skeleton > 1 ) then
              write(*,*) 'crinkle = ',crinkle
!             write(*,*) 'data_dumping,id=',lmpi_id, size(tri1_data),n_tria
              write(*,*) 'total_survey_cells=',total_survey_cells
            endif


            deallocate(p2ln)
            deallocate(e2ln)
            deallocate(edge_donor_list)
            deallocate(edge_donor_weight)
            deallocate(local_trinode_map)
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
        end select sample_type_1

      end select

      if ( .not.moving_grid ) init_template(ivol) = .false.

      if ( geo_name == 'isosurface' .or.                                       &
           geo_name == 'isocrinkle' .or.                                       &
           geo_name == 'streamsurface'                                         &
         ) init_template(ivol) = .true.

      have_sampling_points(ivol) = .true.

      endif initialize_template

    enddo geometry_count

  end subroutine survey_template

!----------------------CREATE_VOLUME_TEMPLATE---------------------------------80
!
!  Calculate the weights between the two nodes cut by the sampling geometry
!
!-----------------------------------------------------------------------------80

  subroutine volume_survey_template( grid, soln, ivol, geo_name, xyz_dim       &
                                   , n, n_tria                                 &
                                   , edge_donor_list, edge_donor_weight        &
                                   , e2ln, p2ln, local_trinode_map, n_edges    &
                                   , sadj )

    use grid_types,          only : grid_type
    use solution_types,      only : soln_type
    use nml_global,          only : moving_grid
    use grid_motion_helpers, only : get_body_id_from_name,                     &
                                    transform_coord, transform_normal
    use moving_body_types,   only : moving_body, observer
    use grid_helper,         only : grid_cell_unique
    use sampling_headers,    only : move_with_body, sample
    use sampling_types,      only : sample_type
    use sampling_funclib,    only : iso_crinkle,                isInsideBlock  &
                                  ,        is_opposite, is_tri_in_window       &
                                  , tet_edge_cut_map                           &
                                  , hex_edge_cut_map                           &
                                  , prz_edge_cut_map                           &
                                  , pyr_edge_cut_map                           &
                                  , split_pyr_edge_map                         &
                                  , hex_4edge_sort                             &
                                  , elem_5edge_sort                            &
                                  , elem_6edge_sort                            &
                                  , prz_4edge_sort                             &
                                  , iso_stats                                  &
                                  , get_edge_vector                            &
                                  , triangle_stats                             &
                                  , iso_nodal_values                           &
                                  , intersects_cyl                             &
                                  , intersects_cone
    use solution_adj,        only : sadj_type
    use linear_algebra,      only : identity_matrix

    type(grid_type),          intent(in)    :: grid
    type(soln_type),          intent(in)    :: soln
    type(sadj_type), optional, intent(in)   :: sadj

    integer,                  intent(in)    :: ivol
    character(len=80),        intent(in)    :: geo_name
    integer,                  intent(in)    :: xyz_dim
    real(dp), dimension(3),   intent(out)   :: n
    integer,                  intent(inout) :: n_tria

    real(dp), dimension(:),   intent(inout) :: edge_donor_weight
    integer,  dimension(:,:), intent(inout) :: edge_donor_list
    integer,  dimension(:),   intent(inout) :: e2ln
    integer,  dimension(:),   intent(inout) :: p2ln
    integer,  dimension(:,:),       pointer :: local_trinode_map
    integer,                  intent(inout) :: n_edges

    real(dp)                 :: r0
    real(dp), dimension(3)   :: pa
    real(dp), dimension(3)   :: p1, p2, p3, p4, p5, p6, p7, p8, po
    real(dp), dimension(3)   :: t1, t2
    real(dp), dimension(8)   :: nodal_values
    real(dp), dimension(3,8) :: corners

    integer,  dimension(:),  allocatable  :: cut_edge_map
    integer,  dimension(:),  allocatable  :: cut_edge_map1
    integer,  dimension(:),  allocatable  :: cut_edge_map2
    integer,  dimension(:),  allocatable  :: cut_edge_map3
    integer,  dimension(:),  allocatable  :: cut_edge_map4
    integer,  dimension(:),  allocatable  :: cut_edge_map_in

    integer, dimension(6) :: cut_edges_list

    integer :: cell, node, ierr
    integer :: cellnode
    integer :: cut
    integer :: cut_edges
    integer :: edge
    integer :: edge_per_cell
    integer :: face_per_cell
    integer :: element_set
    integer :: ncell
    integer :: node_per_cell
    integer :: tris
    integer :: node1, node2
    integer :: node1_part, node2_part
    integer :: body_id
    integer :: tri1, tri2
    integer :: node1a, node2a, node3a
    integer :: node1b, node2b, node3b

    character(len=80)      :: iso_variable
    real(dp)               :: iso_val

    logical :: opposite
    logical :: inside

    character(len=3)  :: type_cell
    character(len=80) :: body_name

    real(dp)                 :: cossq
    real(dp), dimension(3)   :: v
    real(dp), dimension(3)   :: a
    real(dp), dimension(4,4) :: transform
    real(dp)                 :: r
    real(dp)                 :: iso1, iso2

    real(dp) :: node_x
    real(dp) :: node_y
    real(dp) :: node_z
    real(dp) :: check_slen

    real(dp), parameter :: zero = 0.0_dp

    logical  :: iso_box
    real(dp) :: slen_min
    real(dp), dimension(3) :: edge_vector

!   real(dp), dimension(4)              :: f, g
!   real(dp), dimension(:), allocatable :: mass

    type(sample_type) :: initial_sample

  continue

!---rtb
!   setup to have sampling surface move with a body

    if (trim(adjustl(move_with_body(ivol))) /= '') then

!     set up the transform matrix needed to move the sampling surface; default
!     to identity transform (no motion)

      transform = identity_matrix(4)

      if (moving_grid) then
        body_name = trim(adjustl(move_with_body(ivol)))
        if (trim(adjustl(body_name)) == 'observer') then
          transform(:,:) = observer%transform_matrix(:,:)
        else
          call get_body_id_from_name(body_name, body_id, ierr)
          if (body_id /= 0) then
            transform(:,:) = moving_body(body_id)%transform_matrix(:,:)
          end if
        end if
      end if

!     store off a copy of the sample data that we need to transform - restored
!     at the end of this routine

      initial_sample%p1 = sample(ivol)%p1
      initial_sample%p2 = sample(ivol)%p2
      initial_sample%p3 = sample(ivol)%p3
      initial_sample%p4 = sample(ivol)%p4
      initial_sample%p5 = sample(ivol)%p5
      initial_sample%p6 = sample(ivol)%p6
      initial_sample%p7 = sample(ivol)%p7
      initial_sample%p8 = sample(ivol)%p8

      initial_sample%p1_cylinder = sample(ivol)%p1_cylinder
      initial_sample%p2_cylinder = sample(ivol)%p2_cylinder

      initial_sample%po = sample(ivol)%po
      initial_sample%n  = sample(ivol)%n

      initial_sample%vertex = sample(ivol)%vertex
      initial_sample%p1_cone = sample(ivol)%p1_cone
      initial_sample%p2_cone = sample(ivol)%p2_cone

      initial_sample%center = sample(ivol)%center

!     transform the parameters to the appropriate values based on the
!     body they are associated with

      call transform_coord(sample(ivol)%p1(1), sample(ivol)%p1(2),             &
                           sample(ivol)%p1(3), transform)
      call transform_coord(sample(ivol)%p2(1), sample(ivol)%p2(2),             &
                           sample(ivol)%p2(3), transform)
      call transform_coord(sample(ivol)%p3(1), sample(ivol)%p3(2),             &
                           sample(ivol)%p3(3), transform)
      call transform_coord(sample(ivol)%p4(1), sample(ivol)%p4(2),             &
                           sample(ivol)%p4(3), transform)
      call transform_coord(sample(ivol)%p5(1), sample(ivol)%p5(2),             &
                           sample(ivol)%p5(3), transform)
      call transform_coord(sample(ivol)%p6(1), sample(ivol)%p6(2),             &
                           sample(ivol)%p6(3), transform)
      call transform_coord(sample(ivol)%p7(1), sample(ivol)%p7(2),             &
                           sample(ivol)%p7(3), transform)
      call transform_coord(sample(ivol)%p8(1), sample(ivol)%p8(2),             &
                           sample(ivol)%p8(3), transform)

      call transform_coord(sample(ivol)%p1_cylinder(1),                        &
                           sample(ivol)%p1_cylinder(2),                        &
                           sample(ivol)%p1_cylinder(3), transform)
      call transform_coord(sample(ivol)%p2_cylinder(1),                        &
                           sample(ivol)%p2_cylinder(2),                        &
                           sample(ivol)%p2_cylinder(3), transform)

      call transform_coord(sample(ivol)%po(1), sample(ivol)%po(2),             &
                           sample(ivol)%po(3), transform)

      call transform_coord(sample(ivol)%vertex(1), sample(ivol)%vertex(2),     &
                           sample(ivol)%vertex(3), transform)

      call transform_coord(sample(ivol)%p1_cone(1), sample(ivol)%p1_cone(2),   &
                           sample(ivol)%p1_cone(3), transform)

      call transform_coord(sample(ivol)%p2_cone(1), sample(ivol)%p2_cone(2),   &
                           sample(ivol)%p2_cone(3), transform)

      call transform_coord(sample(ivol)%center(1), sample(ivol)%center(2),     &
                           sample(ivol)%center(3), transform)

      call transform_normal(sample(ivol)%n(1), sample(ivol)%n(2),              &
                            sample(ivol)%n(3), transform)

    end if
!---rtb

    edge_donor_weight  = 0.0_dp
    edge_donor_list    = 0
    e2ln               = 0
    p2ln               = 0
    local_trinode_map  = 0

    n  = 0.0_dp
    r0 = 0.0_dp
    p1 = 0.0_dp
    p2 = 0.0_dp
    p3 = 0.0_dp
    p4 = 0.0_dp
    p5 = 0.0_dp
    p6 = 0.0_dp
    p7 = 0.0_dp
    p8 = 0.0_dp
    pa = 0.0_dp

      element_type_count:  do element_set = 1, grid%nelem

        type_cell     =  grid%elem(element_set)%type_cell
        ncell         =  grid%elem(element_set)%ncell
        node_per_cell =  grid%elem(element_set)%node_per_cell
        edge_per_cell =  grid%elem(element_set)%edge_per_cell
        face_per_cell =  grid%elem(element_set)%face_per_cell
!       allocate( mass(face_per_cell) )
!       c2n           => grid%elem(element_set)%c2n
!       c2e           => grid%elem(element_set)%c2e

        if ( skeleton > 2 ) then
          write(6,'(a,i8)') '  element_set=',element_set
          write(6,'(a,a)')  '     type_cel=',type_cell
          write(6,'(a,i8)') 'node_per_cell=',node_per_cell
          write(6,'(a,i8)') 'edge_per_cell=',edge_per_cell
          write(6,'(a,i8)') 'face_per_cell=',face_per_cell
          write(6,'(a,i8)') '        ncell=',ncell
          write(6,'(a,i8)') '         ivol=',ivol
          write(6,'(a,a )') '     geo_name=',geo_name
        endif

        allocate ( cut_edge_map   ( edge_per_cell ) )
        allocate ( cut_edge_map1  ( edge_per_cell ) )
        allocate ( cut_edge_map2  ( edge_per_cell ) )
        allocate ( cut_edge_map3  ( edge_per_cell ) )
        allocate ( cut_edge_map4  ( edge_per_cell ) )
        allocate ( cut_edge_map_in( edge_per_cell ) )

!-----------------------------------------------------------------------------80
!--start-------element_loop---------------------------------------------------80
!-----------------------------------------------------------------------------80

        node       = 0
        element_loop : do cell = 1, ncell

          opposite = .false.
          inside   = .false.

          if ( .not. grid_cell_unique(grid,                                    &
                     grid%elem(element_set)%c2n(1:node_per_cell,cell) )        &
                        ) cycle element_loop

          element_loop_geo_case : select case ( geo_name )

            case ('points')

              cycle element_loop

            case ('line')

              cycle element_loop

            case ('streamsurface')

              cycle element_loop

            case ('isosurface','isocrinkle')

                  iso_val      = sample(ivol)%iso_val
                  iso_variable = sample(ivol)%iso_variable
                  iso_box      = sample(ivol)%iso_box
                  slen_min     = sample(ivol)%slen_min

              do edge = 1, edge_per_cell

                node1 = grid%elem(element_set)%local_e2n(edge,1)
                node2 = grid%elem(element_set)%local_e2n(edge,2)

                node1_part = grid%elem(element_set)%c2n(node1,cell)
                node2_part = grid%elem(element_set)%c2n(node2,cell)

                edge_vector = get_edge_vector ( grid, node1, node2 )
                call iso_nodal_values( iso_variable,node1_part,node2_part,     &
                                       edge_vector, soln, iso1, iso2, sadj )
                nodal_values(node1) = (iso1 - iso_val)
                nodal_values(node2) = (iso2 - iso_val)

                check_slen = 0.5*(grid%slen(node1_part)+grid%slen(node1_part))

                if ( iso_box ) then
                  node_x = 0.5*(grid%x(node1_part)+grid%x(node2_part))
                  node_y = 0.5*(grid%y(node1_part)+grid%y(node2_part))
                  node_z = 0.5*(grid%z(node1_part)+grid%z(node2_part))

                  if ( node_x >= sample(ivol)%x_lower .and.               &
                       node_x <= sample(ivol)%x_upper .and.               &
                       node_y >= sample(ivol)%y_lower .and.               &
                       node_y <= sample(ivol)%y_upper .and.               &
                       node_z >= sample(ivol)%z_lower .and.               &
                       node_z <= sample(ivol)%z_upper .and.               &
                       check_slen >= slen_min ) then
                    if ( ((iso1 - iso_val) * (iso2 - iso_val)) < zero ) then
                      opposite = .true.
                    endif
                  else
                    opposite = .false.
                  endif

                else
                  if ( check_slen >= slen_min ) then
                    if ( ((iso1 - iso_val) * (iso2 - iso_val)) < zero ) then
                      opposite = .true.
                    end if
                  else
                    opposite = .false.
                  end if
                end if

              enddo

              if ( opposite ) inside=.true.

            case ('box')

            sample(ivol)%geo_type = 1 ! box
            p1 = sample(ivol)%p1
            p2 = sample(ivol)%p2
            p3 = sample(ivol)%p3
            p4 = sample(ivol)%p4
            p5 = sample(ivol)%p5
            p6 = sample(ivol)%p6
            p7 = sample(ivol)%p7
            p8 = sample(ivol)%p8
            sample(ivol)%po(1:3)  =                                            &
                       0.125_dp * ( p1(1:3) + p2(1:3) + p3(1:3) + p4(1:3)      &
                                  + p5(1:3) + p6(1:3) + p7(1:3) + p8(1:3) )
            po(1:3)  = sample(ivol)%po(1:3)

              cellnode_loop:  do cellnode = 1, node_per_cell

                node = grid%elem(element_set)%c2n(cellnode,cell)
                pa(1)  = grid%x(node)
                pa(2)  = grid%y(node)
                pa(3)  = grid%z(node)
                nodal_values(cellnode) = -1.0_dp

                if ( isInsideBlock ( ivol, pa ) ) &
                     nodal_values(cellnode) = 1.0_dp

              end do cellnode_loop

              opposite = is_opposite ( type_cell , nodal_values )
              if ( opposite ) inside=.true.

            case ('filledbox')

            sample(ivol)%geo_type = 1 ! box
            p1 = sample(ivol)%p1
            p2 = sample(ivol)%p2
            p3 = sample(ivol)%p3
            p4 = sample(ivol)%p4
            p5 = sample(ivol)%p5
            p6 = sample(ivol)%p6
            p7 = sample(ivol)%p7
            p8 = sample(ivol)%p8
            sample(ivol)%po(1:3)  =                                            &
                       0.125_dp * ( p1(1:3) + p2(1:3) + p3(1:3) + p4(1:3)      &
                                  + p5(1:3) + p6(1:3) + p7(1:3) + p8(1:3) )
            po(1:3)  = sample(ivol)%po(1:3)

              cellnode_loop_fb:  do cellnode = 1, node_per_cell

                node = grid%elem(element_set)%c2n(cellnode,cell)
                pa(1)  = grid%x(node)
                pa(2)  = grid%y(node)
                pa(3)  = grid%z(node)
                nodal_values(cellnode) = -1.0_dp

                if ( isInsideBlock ( ivol, pa ) ) &
                     nodal_values(cellnode) = 1.0_dp

                if ( nodal_values(cellnode) > 0.0_dp ) inside = .true.

              end do cellnode_loop_fb

              if ( inside ) opposite=.true.

            case ('cylinder')

              sample(ivol)%geo_type = 4 ! cylinder
              p1    = sample(ivol)%p1_cylinder
              p2    = sample(ivol)%p2_cylinder
              r     = sample(ivol)%r_cylinder

              n     = p2 - p1
              if ( ( skeleton > 2 ) .and. ( cell == 1 ) ) then
                write(*,*) '-------------------------------'
                write(6,'(a,a )') '     geo_name=',geo_name
                write(6,'(a,3(1x,es12.5))') '     p1      =',p1
                write(6,'(a,3(1x,es12.5))') '     p2      =',p2
                write(6,'(a,3(1x,es12.5))') '      r      =',r
                write(6,'(a,3(1x,es12.5))') '      n      =',n
                write(*,*) '-------------------------------'
              endif

              cellnode_loop_cyl:  do cellnode = 1, node_per_cell
                 node = grid%elem(element_set)%c2n(cellnode,cell)
                 corners(1,cellnode)  = grid%x(node)
                 corners(2,cellnode)  = grid%y(node)
                 corners(3,cellnode)  = grid%z(node)
                 nodal_values(cellnode) = -1.0_dp
               end do cellnode_loop_cyl

             call intersects_cyl( type_cell, node_per_cell, edge_per_cell     &
                                , p1, p2, r, corners, n, nodal_values )

              opposite = is_opposite ( type_cell , nodal_values )
              if ( opposite ) inside=.true.

            case ('cone')

              sample(ivol)%geo_type = 3 ! cone
              cossq = sample(ivol)%cossq
              v     = sample(ivol)%vertex
              a     = sample(ivol)%n
              p1    = sample(ivol)%p1_cone
              p2    = sample(ivol)%p2_cone
              if ( ( skeleton > 2 ) .and. ( cell == 1 ) ) then
                write(*,*) '-------------------------------'
                write(6,'(a,a )') '     geo_name=',geo_name
                write(6,'(a,1x,es12.5)')    '  cossq      =',cossq
                write(6,'(a,3(1x,es12.5))') '      v      =',v
                write(6,'(a,3(1x,es12.5))') '      a      =',a
                write(6,'(a,3(1x,es12.5))') '     p1      =',p1
                write(6,'(a,3(1x,es12.5))') '     p2      =',p2
                write(*,*) '-------------------------------'
              endif

              cellnode_loop_cone:  do cellnode = 1, node_per_cell
                 node = grid%elem(element_set)%c2n(cellnode,cell)
                 corners(1,cellnode)  = grid%x(node)
                 corners(2,cellnode)  = grid%y(node)
                 corners(3,cellnode)  = grid%z(node)
                 nodal_values(cellnode) = -1.0_dp
               end do cellnode_loop_cone

             call intersects_cone( type_cell, node_per_cell, edge_per_cell     &
                                , p1, p2, corners, a, v, cossq, nodal_values )

              opposite = is_opposite ( type_cell , nodal_values )
              if ( opposite ) inside=.true.

            case ('sphere')

              ! nodal_values() == 0.0 on integration surface

              sample(ivol)%geo_type = 2 ! sphere
              po(1:3)  = sample(ivol)%center(1:3) ! sphere center
              r0       = sample(ivol)%radius    ! sphere radius

              cellnode_loop_sphere:  do cellnode = 1, node_per_cell

                node = grid%elem(element_set)%c2n(cellnode,cell)
                pa(1)  = grid%x(node)
                pa(2)  = grid%y(node)
                pa(3)  = grid%z(node)
                nodal_values(cellnode) =  sqrt( (pa(1) - po(1))**2             &
                                              + (pa(2) - po(2))**2             &
                                              + (pa(3) - po(3))**2 ) - r0

              end do cellnode_loop_sphere

              opposite = is_opposite ( type_cell , nodal_values )
              if ( opposite ) inside=.true.

            case ('plane')

              sample(ivol)%geo_type = 5 ! plane
              po(1:3) = sample(ivol)%po(1:3)  ! center
              n(1:3)  = sample(ivol)%n(1:3)   ! normal

              cellnode_loop_plane:  do cellnode = 1, node_per_cell

                node = grid%elem(element_set)%c2n(cellnode,cell)
                pa(1)  = grid%x(node)
                pa(2)  = grid%y(node)
                pa(3)  = grid%z(node)
                nodal_values(cellnode) = ( pa(1) - po(1) ) * n(1)              &
                                       + ( pa(2) - po(2) ) * n(2)              &
                                       + ( pa(3) - po(3) ) * n(3)

              end do cellnode_loop_plane

              opposite = is_opposite ( type_cell , nodal_values )
              if ( opposite ) inside=.true.

            case ('quad')

            sample(ivol)%geo_type = 6 ! quadralateral
            p1(1:3)  = sample(ivol)%p1(1:3)
            p2(1:3)  = sample(ivol)%p2(1:3)
            p3(1:3)  = sample(ivol)%p3(1:3)
            p4(1:3)  = sample(ivol)%p4(1:3)
            sample(ivol)%po(1:3)  =                                         &
                     0.25_dp * ( p1(1:3) + p2(1:3) + p3(1:3) + p4(1:3) )
            t1 = p2 - p1
            t2 = p3 - p1
            n = cross_product(t1,t2)
            sample(ivol)%n(1:3)=n(1:3)
!           n = cross_product((p2-p1),(p3-p1))
            po(1:3)  = sample(ivol)%po(1:3)
              cellnode_loop_quad:  do cellnode = 1, node_per_cell

                node = grid%elem(element_set)%c2n(cellnode,cell)
                ! nodal_values() == 0.0 on integration surface
                pa(1)  = grid%x(node)
                pa(2)  = grid%y(node)
                pa(3)  = grid%z(node)

                nodal_values(cellnode) = ( pa(1) - po(1) ) * n(1)              &
                                       + ( pa(2) - po(2) ) * n(2)              &
                                       + ( pa(3) - po(3) ) * n(3)

              end do cellnode_loop_quad

              opposite = is_opposite ( type_cell , nodal_values )

              if ( opposite ) then

                if ( type_cell == 'tet' ) then
                  call tet_edge_cut_map(nodal_values, cut_edge_map)
                else if ( type_cell == 'hex' ) then
                  call hex_edge_cut_map(nodal_values, cut_edge_map)
                else if ( type_cell == 'prz' ) then
                  call prz_edge_cut_map(nodal_values, cut_edge_map)
                else if ( type_cell == 'pyr' ) then
                  call pyr_edge_cut_map(nodal_values, cut_edge_map)
                endif

                call is_tri_in_window( xyz_dim, grid%x, grid%y, grid%z         &
                                       , grid%elem(element_set)%c2n(:,cell)    &
                                       , cut_edge_map                          &
                                       , type_cell                             &
                                       , edge_per_cell                         &
                                       , geo_name, sample(ivol), inside )
              endif

            case ('circle')

            sample(ivol)%geo_type = 7 ! quadralateral
            po(1:3)  = sample(ivol)%po(1:3)
             n(1:3)  = sample(ivol)%n(1:3)
            r0       = sample(ivol)%r0
              cellnode_loop_circle:  do cellnode = 1, node_per_cell

                node = grid%elem(element_set)%c2n(cellnode,cell)
!               if ( node > nnodes0 ) cycle element_loop
                ! nodal_values() == 0.0 on integration surface
                pa(1)  = grid%x(node)
                pa(2)  = grid%y(node)
                pa(3)  = grid%z(node)

!               nodal_values(cellnode) = tet_vol_value( p1, p3, p2, pa )
                nodal_values(cellnode) = ( pa(1) - po(1) ) * n(1)              &
                                       + ( pa(2) - po(2) ) * n(2)              &
                                       + ( pa(3) - po(3) ) * n(3)

              end do cellnode_loop_circle

              if ( type_cell == 'tet' ) then
                call tet_edge_cut_map(nodal_values, cut_edge_map)
              else if ( type_cell == 'hex' ) then
                call hex_edge_cut_map(nodal_values, cut_edge_map)
              else if ( type_cell == 'prz' ) then
                call prz_edge_cut_map(nodal_values, cut_edge_map)
              else if ( type_cell == 'pyr' ) then
                call pyr_edge_cut_map(nodal_values, cut_edge_map)
              endif

              call is_tri_in_window( xyz_dim, grid%x, grid%y, grid%z           &
                                     , grid%elem(element_set)%c2n(:,cell)      &
                                     , cut_edge_map                            &
                                     , type_cell                               &
                                     , edge_per_cell                           &
                                     , geo_name, sample(ivol), inside )

              if ( inside ) opposite=.true.
            case default

          end select element_loop_geo_case

!         to increase performance, pop out of element loop as soon as possible

          if ( (.not.opposite) .or. (.not.inside) ) cycle element_loop

          not_point_survey_1 :  if ( geo_name /= 'points' ) then

            if ( type_cell == 'tet' ) then
              call tet_edge_cut_map(nodal_values, cut_edge_map)
            else if ( type_cell == 'hex' ) then
              call hex_edge_cut_map(nodal_values, cut_edge_map)
            else if ( type_cell == 'prz' ) then
              call prz_edge_cut_map(nodal_values, cut_edge_map)
            else if ( type_cell == 'pyr' ) then
              call pyr_edge_cut_map(nodal_values, cut_edge_map)
            endif

!           This sum tells us how many edges in the current cell were cut by the
!           integration surface

            cut_edges = sum(cut_edge_map)

                if (geo_name == 'isocrinkle') then
                  call iso_crinkle( iso_val, iso_variable,                     &
                                  cut_edges, cut_edge_map,                     &
                                  cell, edge_per_cell, face_per_cell,          &
                                  soln, grid%elem(element_set)%c2n,            &
                                  n_tria,                                      &
                                  grid%elem(element_set)%local_f2n,            &
                                  grid%elem(element_set)%c2e,                  &
                                  grid%elem(element_set)%local_e2n, e2ln,      &
                                  local_trinode_map, n_edges, p2ln,            &
                                  edge_donor_list, edge_donor_weight, grid,    &
                                  sadj )
                endif

            select case (cut_edges)

              case(0)

              case(3)   ! Tet slice forms a super triangle (unfolded from node)

                if (geo_name == 'isocrinkle') then
                else if (geo_name == 'isosurface') then
                  call iso_stats( iso_val, iso_variable, cut_edge_map,         &
                                  cell, edge_per_cell, grid,          soln,    &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                  n_tria,                                      &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight, sadj )

                else
                  call triangle_stats( cut_edge_map,                           &
                                     cell, edge_per_cell,                      &
                                     sample(ivol)%geo_type_name,               &
                                     sample(ivol),                             &
                                     grid%x, grid%y, grid%z,                   &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                     n_tria, grid%l2g,                         &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight )
                end if

              case(4)   ! Tet slice forms a quad (unfolded along edge)
                        ! decomp into tris

                if ( type_cell == 'tet' ) then
                  cut_edge_map1 = cut_edge_map
                  cut_edge_map2 = cut_edge_map
                  if (0 == cut_edge_map(1)) then
                    cut_edge_map1(3) = 0
                    cut_edge_map2(4) = 0
                  else if (0 == cut_edge_map(2)) then
                    cut_edge_map1(1) = 0
                    cut_edge_map2(6) = 0
                  else if (0 == cut_edge_map(3)) then
                    cut_edge_map1(2) = 0
                    cut_edge_map2(5) = 0
                  end if
                else if ( type_cell == 'prz') then
                  call prz_4edge_sort( cut_edge_map, edge_per_cell             &
                       , sample(ivol)%geo_type_name,   sample(ivol)            &
                       , grid%x,grid%y, grid%z                                 &
                       , grid%elem(element_set)%c2n(:,cell)                    &
                       , cut_edge_map1, cut_edge_map2                          &
                       )
                else if ( type_cell == 'pyr') then
                  call split_pyr_edge_map(cut_edge_map, cut_edge_map1,         &
                                          cut_edge_map2)
                else if ( type_cell == 'hex') then
                  call hex_4edge_sort( cut_edge_map, edge_per_cell             &
                       , sample(ivol)%geo_type_name, sample(ivol)              &
                       , grid%x,grid%y, grid%z                                 &
                       , grid%elem(element_set)%c2n(:,cell)                    &
                       , cut_edge_map1, cut_edge_map2                          &
                       )
                endif

                if (geo_name == 'isocrinkle') then
                else if (geo_name == 'isosurface') then

                  call iso_stats( iso_val, iso_variable, cut_edge_map1,        &
                                  cell, edge_per_cell, grid,          soln,    &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                  n_tria,                                      &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight, sadj)

                  call iso_stats( iso_val, iso_variable, cut_edge_map2,        &
                                  cell, edge_per_cell, grid,          soln,    &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                  n_tria,                                      &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight, sadj)

                else
                call triangle_stats( cut_edge_map1,                            &
                                     cell, edge_per_cell,                      &
                                     sample(ivol)%geo_type_name,               &
                                     sample(ivol),                             &
                                     grid%x, grid%y, grid%z,                   &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                     n_tria, grid%l2g,                         &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight )
                call triangle_stats( cut_edge_map2,                            &
                                     cell, edge_per_cell,                      &
                                     sample(ivol)%geo_type_name,               &
                                     sample(ivol),                             &
                                     grid%x, grid%y, grid%z,                   &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                     n_tria, grid%l2g,                         &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight )
                end if

              case(5)

                if ( skeleton > 2 ) then
                  if ( type_cell == 'tet') then
                    write(6,'(a,12i3)') 'case 5-map tet ',cut_edge_map
                  else if ( type_cell == 'pyr') then
                    write(6,'(a,12i3)') 'case 5-map pyr ',cut_edge_map
                  else if ( type_cell == 'prz') then
                    write(6,'(a,12i3)') 'case 5-map prz ',cut_edge_map
                  else if ( type_cell == 'hex') then
                    write(6,'(a,12i3)') 'case 5-map hex ',cut_edge_map
                  endif
                endif

                call elem_5edge_sort( cut_edge_map, cell, edge_per_cell        &
                         , sample(ivol)%geo_type_name, sample(ivol)            &
                         , grid,                   soln                        &
                         , grid%elem(element_set)%c2n                          &
                         , grid%elem(element_set)%local_e2n                    &
                         , iso_val, iso_variable                               &
                         , cut_edge_map1, cut_edge_map2, cut_edge_map3, sadj   &
                         )

                do tris = 1, 3
                  select case (tris)
                    case (1)
                    cut_edge_map_in = cut_edge_map1
                    case (2)
                    cut_edge_map_in = cut_edge_map2
                    case (3)
                    cut_edge_map_in = cut_edge_map3
                  end select
                  if ( geo_name=='isocrinkle') then
                  else if ( geo_name=='isosurface') then
                  call iso_stats( iso_val, iso_variable, cut_edge_map_in,      &
                                  cell, edge_per_cell, grid,          soln,    &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                  n_tria,                                      &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight, sadj )
                  else
                  call triangle_stats( cut_edge_map_in,                        &
                                       cell, edge_per_cell,                    &
                                       sample(ivol)%geo_type_name,             &
                                       sample(ivol),                           &
                                       grid%x,                                 &
                                       grid%y,                                 &
                                       grid%z,                                 &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                       n_tria, grid%l2g,                       &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight )
                 endif
                 enddo

              case(6)
                if ( skeleton > 2 ) then
                  if ( type_cell == 'tet') then
                    write(6,'(a,12i3)') 'case 6-map tet ',cut_edge_map
                  else if ( type_cell == 'pyr') then
                    write(6,'(a,12i3)') 'case 6-map pyr ',cut_edge_map
                  else if ( type_cell == 'prz' ) then
                    write(6,'(a,12i3)') 'case 6-map prz ',cut_edge_map
                  else if ( type_cell == 'hex' ) then
                    write(6,'(a,12i3)') 'case 6-map hex ',cut_edge_map
                  endif
                endif

                call elem_6edge_sort( cut_edge_map, cell, edge_per_cell        &
                         , sample(ivol)%geo_type_name, sample(ivol)            &
                         , grid,                   soln                        &
                         , grid%elem(element_set)%c2n                          &
                         , grid%elem(element_set)%local_e2n                    &
                         , iso_val, iso_variable                               &
                         , cut_edge_map1, cut_edge_map2, cut_edge_map3         &
                         , cut_edge_map4, sadj                                 &
                         )

                if ( skeleton > 2 ) then
                  cut = 0
                  do edge = 1 , 12
                    if ( cut_edge_map(edge) == 1 ) then
                      cut = cut + 1
                      cut_edges_list(cut) = edge
                    endif
                  enddo
                  write(6,'(6i3)') cut_edges_list
                endif
                do tris = 1, 4
                  select case (tris)
                    case (1)
                    cut_edge_map_in = cut_edge_map1
                    case (2)
                    cut_edge_map_in = cut_edge_map2
                    case (3)
                    cut_edge_map_in = cut_edge_map3
                    case (4)
                    cut_edge_map_in = cut_edge_map4
                  end select
                  if ( geo_name=='isocrinkle') then
                  else if ( geo_name=='isosurface') then
                  call iso_stats( iso_val, iso_variable, cut_edge_map_in,      &
                                  cell, edge_per_cell, grid,          soln,    &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                  n_tria,                                      &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight, sadj )
                  else
                  call triangle_stats( cut_edge_map_in,                        &
                                       cell, edge_per_cell,                    &
                                       sample(ivol)%geo_type_name,             &
                                       sample(ivol),                           &
                                       grid%x,                                 &
                                       grid%y,                                 &
                                       grid%z,                                 &
                                  grid%elem(element_set)%c2n,                  &
                                  grid%elem(element_set)%c2e,                  &
                                       n_tria, grid%l2g,                       &
                                  grid%elem(element_set)%local_e2n,            &
                                  local_trinode_map, n_edges, e2ln,            &
                                  edge_donor_list, edge_donor_weight )
                  endif
                 enddo
              case(7)
!                      write(6,'(a,12i3)') 'case 7-map ',cut_edge_map
              case(8)
!                      write(6,'(a,12i3)') 'case 8-map ',cut_edge_map

              case default

            end select

          endif not_point_survey_1

        end do element_loop



!-----------------------------------------------------------------------------80
!--end---------element_loop---------------------------------------------------80
!-----------------------------------------------------------------------------80

        deallocate (cut_edge_map)
        deallocate (cut_edge_map1)
        deallocate (cut_edge_map2)
        deallocate (cut_edge_map3)
        deallocate (cut_edge_map4)
        deallocate (cut_edge_map_in)
!       deallocate (mass)

      end do element_type_count

         if ( geo_name=='isocrinkle') then
           do tri1 = 1, n_tria
             node1a = local_trinode_map(1,tri1)
             node2a = local_trinode_map(2,tri1)
             node3a = local_trinode_map(3,tri1)
             do tri2 = tri1+1, n_tria
               node1b = local_trinode_map(1,tri2)
               node2b = local_trinode_map(2,tri2)
               node3b = local_trinode_map(3,tri2)
               if ( node1a==node1b ) then
                 if ( ( node2a==node2b ).and. ( node3a==node3b ) ) then
 write(*,'(i5,a,i5,5i8)' ) lmpi_id,' one-a',tri1,local_trinode_map(1:3,tri1)
 write(*,'(i5,a,i5,5i8)' ) lmpi_id,' one-b',tri2,local_trinode_map(1:3,tri2)
                 endif
                 if ( ( node2a==node3b ).and. ( node3a==node2b ) ) then
 write(*,'(i5,a,i5,5i8)' ) lmpi_id,' two-a',tri1,local_trinode_map(1:3,tri1)
 write(*,'(i5,a,i5,5i8)' ) lmpi_id,' two-b',tri2,local_trinode_map(1:3,tri2)
                 endif
               endif
             enddo
           enddo
         endif

!---rtb
!     restore the original sample data if altered to move with body

      if (trim(adjustl(move_with_body(ivol))) /= '') then

        sample(ivol)%p1 = initial_sample%p1
        sample(ivol)%p2 = initial_sample%p2
        sample(ivol)%p3 = initial_sample%p3
        sample(ivol)%p4 = initial_sample%p4
        sample(ivol)%p5 = initial_sample%p5
        sample(ivol)%p6 = initial_sample%p6
        sample(ivol)%p7 = initial_sample%p7
        sample(ivol)%p8 = initial_sample%p8

        sample(ivol)%p1_cylinder = initial_sample%p1_cylinder
        sample(ivol)%p2_cylinder = initial_sample%p2_cylinder

        sample(ivol)%po = initial_sample%po
        sample(ivol)%n  = initial_sample%n

        sample(ivol)%vertex  = initial_sample%vertex
        sample(ivol)%p1_cone = initial_sample%p1_cone
        sample(ivol)%p2_cone = initial_sample%p2_cone

        sample(ivol)%center = initial_sample%center

      end if
!---rtb

  end subroutine volume_survey_template

!----------------------CREATE_VOLUME_TEMPLATE---------------------------------80
!
!  Calculate the weights between the two nodes cut but the sampling geometry
!
!-----------------------------------------------------------------------------80

  subroutine boundary_survey_template( grid, ivol, geo_name                    &
                                   , edge_donor_weight1, edge_donor_weight2    &
                                   , edge_donor_node1, edge_donor_node2        &
                                   , n, n_edges                                &
                                   )

    use grid_types,         only : grid_type
    use sampling_headers,   only : sample, patch_list_count
    use sampling_funclib,   only : e2nt, e2nq, face_edge_cuts                  &
                                 , is_inside_cylinder, edge_zero_parameter
!   use bc_types,           only : tri_side_node, quad_side_node

    type(grid_type),          intent(in)           :: grid
    integer,                  intent(in)           :: ivol
    character(len=80),        intent(in)           :: geo_name
    real(dp), dimension(:),         pointer        :: edge_donor_weight1
    real(dp), dimension(:),         pointer        :: edge_donor_weight2
    integer,  dimension(:),         pointer        :: edge_donor_node1
    integer,  dimension(:),         pointer        :: edge_donor_node2
    real(dp), dimension(3),   intent(out)          :: n
    integer,                  intent(inout)        :: n_edges

    real(dp), dimension(3)   :: pa, pb
    real(dp), dimension(3)   :: p1, p2
    real(dp), dimension(3)   :: po
    real(dp), dimension(4)   :: nodal_values
    real(dp)                 :: weight1, weight2
    real(dp)                 :: r

    integer,  dimension(2)   :: cut_edges

    integer :: ib, node, node1, node2
    integer :: face_index, face_corner, n_corner
    integer :: local1, local2
    integer :: i, list_count

    continue

    n  = 0.0_dp
    pa = 0.0_dp
    pb = 0.0_dp
!----------Triangular Faces----------
    if ( verbose ) then
      write(6,'(a,a)') 'boundary_survey_template, geo_name=',geo_name
    endif

    n_edges    = 0
! account for either old style boundary_list input or newer patch_list input
    list_count = max( 1, patch_list_count(ivol) )

    boundary_list_loop:     do i = 1, list_count
                     ib = sample(ivol)%patch_list(i)
      if ( verbose ) then
        write(6,'(i5,a,7i5)') lmpi_id, ', boundary_survey_template', &
          ivol, patch_list_count(ivol), list_count, i, ib,           &
          sample(ivol)%patch_list(i)
      endif
!-----------------------------------------------------------------------------80
!--loop triangle boundary face elements---------------------------------------80
!-----------------------------------------------------------------------------80
      loop_tris : do face_index = 1,grid%bc(ib)%nbfacet
        n_corner = 3

        tri_loop_geo_case : select case ( geo_name )

            case ('boundary_points','volume_points')

              cycle loop_tris

            case ('plane')

              sample(ivol)%geo_type = 5 ! plane
              po(1:3) = sample(ivol)%po(1:3)  ! center
              n(1:3)  = sample(ivol)%n(1:3)   ! normal

            edge_loop_plane: do face_corner = 1, n_corner

              node   = &
              grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,face_corner))
              pa(1)  = grid%x(node)
              pa(2)  = grid%y(node)
              pa(3)  = grid%z(node)
              nodal_values(face_corner) = ( pa(1) - po(1) ) * n(1)           &
                                        + ( pa(2) - po(2) ) * n(2)           &
                                        + ( pa(3) - po(3) ) * n(3)
            end do edge_loop_plane
            cut_edges = face_edge_cuts(3, e2nt, nodal_values)
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
        if ( cut_edges(1) == 0 ) cycle

          local1 = e2nt(cut_edges(1),1)
          local2 = e2nt(cut_edges(1),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2

          local1 = e2nt(cut_edges(2),1)
          local2 = e2nt(cut_edges(2),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
            case ('cylinder')
              sample(ivol)%geo_type = 4 ! cylinder
              p1                    = sample(ivol)%p1_cylinder
              p2                    = sample(ivol)%p2_cylinder
              r                     = sample(ivol)%r_cylinder
              nodal_values          = -1

            edge_loop_cyl: do face_corner = 1,3

              node   = &
              grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,face_corner))
              pa(1)  = grid%x(node)
              pa(2)  = grid%y(node)
              pa(3)  = grid%z(node)
              if ( is_inside_cylinder( pa, p1, p2, r ) )                       &
                nodal_values(face_corner) = 1
            end do edge_loop_cyl
            cut_edges = face_edge_cuts(3, e2nt, nodal_values)
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
        if ( cut_edges(1) == 0 ) cycle

          local1 = e2nt(cut_edges(1),1)
          local2 = e2nt(cut_edges(1),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2

          local1 = e2nt(cut_edges(2),1)
          local2 = e2nt(cut_edges(2),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
          case default

              cycle loop_tris

          end select tri_loop_geo_case
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    enddo loop_tris
!----------Quadrilateral Faces----------

    n_corner = 4
    loop_quads : do face_index = 1,grid%bc(ib)%nbfaceq

      element_loop_quad_case : select case ( geo_name )

      case ('points')
        cycle loop_quads

      case ('plane')

        sample(ivol)%geo_type = 5 ! plane
        po(1:3) = sample(ivol)%po(1:3)  ! center
        n(1:3)  = sample(ivol)%n(1:3)   ! normal

        corner_quad_loop : do face_corner = 1, n_corner

          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index, &
                                     mod(face_corner+0-1,n_corner)+1))
          pa(1)  = grid%x(node1)
          pa(2)  = grid%y(node1)
          pa(3)  = grid%z(node1)
          nodal_values(face_corner) = ( pa(1) - po(1) ) * n(1)           &
                                    + ( pa(2) - po(2) ) * n(2)           &
                                    + ( pa(3) - po(3) ) * n(3)
        end do corner_quad_loop
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
        cut_edges = face_edge_cuts(4, e2nq, nodal_values)

        if ( cut_edges(1) == 0 ) cycle

          local1 = e2nq(cut_edges(1),1)
          local2 = e2nq(cut_edges(1),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2

          local1 = e2nq(cut_edges(2),1)
          local2 = e2nq(cut_edges(2),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80

      case ('cylinder')

        sample(ivol)%geo_type = 4 ! cylinder
        p1                    = sample(ivol)%p1_cylinder
        p2                    = sample(ivol)%p2_cylinder
        r                     = sample(ivol)%r_cylinder
        nodal_values          = -1
        cut_edges             = -1

        quad_loop_cyl: do face_corner = 1, n_corner

          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index, &
                                   mod(face_corner+0-1,n_corner)+1))
          pa(1)  = grid%x(node1)
          pa(2)  = grid%y(node1)
          pa(3)  = grid%z(node1)
          if ( is_inside_cylinder( pa, p1, p2, r ) )                           &
              nodal_values(face_corner) = 1
        end do quad_loop_cyl
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
        cut_edges = face_edge_cuts(4, e2nq, nodal_values)

        if ( cut_edges(1) == 0 ) cycle

          local1 = e2nq(cut_edges(1),1)
          local2 = e2nq(cut_edges(1),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2

          local1 = e2nq(cut_edges(2),1)
          local2 = e2nq(cut_edges(2),2)
          node1   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local1))
          node2   = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,local2))
          pa(1) = grid%x(node1); pa(2) = grid%y(node1); pa(3) = grid%z(node1)
          pb(1) = grid%x(node2); pb(2) = grid%y(node2); pb(3) = grid%z(node2)
          call edge_zero_parameter( geo_name, sample(ivol)            &
                                  , weight1, weight2, pa, pb )
          n_edges = n_edges + 1
          edge_donor_node1(n_edges) = node1
          edge_donor_node2(n_edges) = node2
          edge_donor_weight1(n_edges) = weight1
          edge_donor_weight2(n_edges) = weight2
!-----------------------------------------------------------------------------80
!  Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY Not DRY
!-----------------------------------------------------------------------------80
          case default

             edge_donor_node1(n_edges) = 0
             edge_donor_node2(n_edges) = 0
             edge_donor_weight1(n_edges) = 0.0_dp
             edge_donor_weight2(n_edges) = 0.0_dp

          end select element_loop_quad_case
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    enddo loop_quads
  enddo boundary_list_loop

  end subroutine boundary_survey_template

!========================= POINT_SURVEY_TEMPLATE =============================80
!
! Search for element surrounding specified point or boundary face that might
! surround the requested point
!
!=============================================================================80

  subroutine point_survey_template( grid, ivol, ipts )

    use grid_types,         only : grid_type
    use grid_helper,        only : grid_cell_unique
    use geometry_utils,     only : get_normal, line_plane_point2
    use sampling_funclib,   only : is_inside_triangle2, is_inside_element      &
                                 , is_inside_quad, getNodeNumberTri            &
                                 , getNodeNumberQuad

    use sampling_headers,   only : dist_tolerance, point_is_found              &
                                 , p2c_local_cells, p2c_local_face             &
                                 , p2c_local_elem                              &
                                 , p2c_local_bdy, snap_output_xyz              &
                                 , point_is_found_global, sample

    type(grid_type),                  intent(in)   :: grid
    integer,                          intent(in)   :: ivol
    integer,                          intent(in)   :: ipts

    integer                               :: element_set
    integer                               :: node_per_cell
    integer,  dimension(:,:), pointer     :: c2n
    integer                               :: ib
    integer                               :: cell
    integer                               :: node
!   integer                               :: globalnode
    integer                               :: face_index
    integer, dimension(4)                 :: corner
    real(dp), dimension(0:8)              :: px, py, pz
    real(dp), dimension(3)                :: pc, po, p1, p2, p3, p4
    real(dp), dimension(3)                :: t0
    real(dp), dimension(3)                :: p_snap
    real(dp), dimension(3)                :: n
!   real(dp), dimension(3)                :: lambda

    character(len=3)                      :: type_cell

    real(dp), parameter                   :: one_forth = 1.0_dp/4.0_dp
    real(dp), parameter                   :: one_third = 1.0_dp/3.0_dp

    real(dp)                              :: dist, dist_tri, dist_quad
    integer                               :: flag_tri, flag_quad
    integer                               :: ib_tri, ib_quad
    integer                               :: face_tri, face_quad
    integer                               :: elem_tri, elem_quad
    real(dp), dimension(3)                :: p_tri, p_quad
    real(dp)                              :: d1, d2, d3, d4, max_edge

    logical                               :: intersects_face

  continue

    sample(ivol)%geo_type     = 0 ! point
    po(1:3)                   = sample(ivol)%point_list(1:3,ipts)
    point_is_found(ivol,ipts) = 0
    node                      = 0
    elem_tri  = 0
    elem_quad = 0
    flag_tri  = 0
    ib_tri    = 0
    face_tri  = 0
    flag_quad = 0
    ib_quad   = 0
    face_quad = 0
    p_tri     = 0.0_dp
    p_quad    = 0.0_dp
    dist_tri  = huge(1.0)
    dist_quad = huge(1.0)

    geo_case : select case ( sample(ivol)%geo_type_name )

    case ( 'boundary_points' )

      loop_bdy:  do ib = 1, grid%nbound

        if ( point_is_found(ivol,ipts) == 1 ) cycle loop_bdy

        tris:  if ( grid%bc(ib)%nbfacet > 0 ) then

        loop_tris : do face_index = 1,grid%bc(ib)%nbfacet

          if ( point_is_found(ivol,ipts) == 1 ) cycle loop_tris

          corner(1:3) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,1:3))
! elminate ghost overlap cells on partition boundaries
          node = getNodeNumberTri( grid, corner )
          if ( node > grid%nnodes0 ) cycle loop_tris

          dist            = huge(0.0_dp)
          intersects_face = .false.
          p1(1)  = grid%x(corner(1))
          p1(2)  = grid%y(corner(1))
          p1(3)  = grid%z(corner(1))
          p2(1)  = grid%x(corner(2))
          p2(2)  = grid%y(corner(2))
          p2(3)  = grid%z(corner(2))
          p3(1)  = grid%x(corner(3))
          p3(2)  = grid%y(corner(3))
          p3(3)  = grid%z(corner(3))
! test to see if the point is in reasonable proximity to the boundary
! distance from point to boundary face center
          pc     = one_third*(p1+p2+p3)
          t0     = pc-po
          dist     = sqrt(dot_product(t0,t0))
! longest local boundary face edge length
          d1 = sqrt(dot_product(p2-p1,p2-p1))
          d2 = sqrt(dot_product(p3-p2,p3-p2))
          d3 = sqrt(dot_product(p1-p3,p1-p3))
          max_edge = max( d1, d2, d3 )

          if ( dist > dist_tolerance .and. dist > max_edge ) cycle

          intersects_face = is_inside_triangle2 ( po, p1, p2, p3 )

          if ( intersects_face  ) then
            if ( snap_output_xyz ) then
              n      = get_normal( p1, p2, p3 )
              p_snap = line_plane_point2( p1, p2, p3, po, n )
              sample(ivol)%print_list(1:3,ipts) = p_snap
            endif
            p2c_local_cells(ivol,ipts) = face_index
            p2c_local_bdy(ivol,ipts)   = ib
            p2c_local_elem(ivol,ipts)  = grid%bc(ib)%f2ntb(face_index,5)
            p2c_local_face(ivol,ipts)  = 1
            point_is_found(ivol,ipts)  = 1
          else
            n       = get_normal( p1, p2, p3 )
            p_snap  = line_plane_point2( p1, p2, p3, po, n )
            if ( dist < dist_tri ) then
              dist_tri = dist
              ib_tri   = ib
              elem_tri = grid%bc(ib)%f2ntb(face_index,5)
              face_tri = face_index
              p_tri    = p_snap
              flag_tri = 1
            endif
          endif

          if ( intersects_face .and. verbose ) then
            write(6,'(i6,3(a,i6),3(1x,f15.5))')  lmpi_id,                   &
            ', Point',ipts,' is in tri face',face_index,' of boundary', ib  &
            , po
          endif

        end do loop_tris

        endif tris


        quads:  if ( grid%bc(ib)%nbfaceq > 0 ) then

        loop_quads : do face_index = 1,grid%bc(ib)%nbfaceq

          if ( point_is_found(ivol,ipts) == 1 ) cycle loop_quads

          corner(1:4) = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,1:4))
! elminate ghost overlap cells on partition boundaries
          node = getNodeNumberQuad( grid, corner )
          if ( node > grid%nnodes0 ) cycle loop_quads

          dist            = huge(0.0_dp)
          intersects_face = .false.
          p1(1)  = grid%x(corner(1))
          p1(2)  = grid%y(corner(1))
          p1(3)  = grid%z(corner(1))
          p2(1)  = grid%x(corner(2))
          p2(2)  = grid%y(corner(2))
          p2(3)  = grid%z(corner(2))
          p3(1)  = grid%x(corner(3))
          p3(2)  = grid%y(corner(3))
          p3(3)  = grid%z(corner(3))
          p4(1)  = grid%x(corner(4))
          p4(2)  = grid%y(corner(4))
          p4(3)  = grid%z(corner(4))
! test to see if the point is in reasonable proximity to the boundary
! 10 is just an arbitrary choice
          pc     = one_forth*(p1+p2+p3+p4)
          t0     = pc-po
          dist   = sqrt(dot_product(t0,t0))
! longest local boundary face edge length
          d1 = sqrt(dot_product(p2-p1,p2-p1))
          d2 = sqrt(dot_product(p3-p2,p3-p2))
          d3 = sqrt(dot_product(p3-p4,p3-p4))
          d4 = sqrt(dot_product(p4-p1,p4-p1))
          max_edge = max( d1, d2, d3, d4 )

          if ( dist > dist_tolerance .and. dist > max_edge ) cycle

          intersects_face = is_inside_quad( po, p1, p2, p3, p4 )

          if ( intersects_face ) then
            if ( snap_output_xyz ) then
              n      = get_normal( p1, p2, p3 )
              p_snap = line_plane_point2( p1, p2, p3, po, n )
              sample(ivol)%print_list(1:3,ipts) = p_snap
            endif
            p2c_local_cells(ivol,ipts) = face_index
            p2c_local_bdy(ivol,ipts)   = ib
            p2c_local_elem(ivol,ipts)  = grid%bc(ib)%f2nqb(face_index,6)
            p2c_local_face(ivol,ipts)  = 2
            point_is_found(ivol,ipts)  = 1
          else
            n       = get_normal( p1, p2, p3 )
            p_snap  = line_plane_point2( p1, p2, p3, po, n )
            if ( dist < dist_quad ) then
              dist_quad = dist
              ib_quad   = ib
              elem_quad = grid%bc(ib)%f2nqb(face_index,6)
              face_quad = face_index
              p_quad    = p_snap
              flag_quad = 1
            endif
          endif

          if ( intersects_face ) then
           if ( verbose ) write(6,'(i4,4(a,i6),a,1x,f12.4,2(a,3(1x,f10.3)))') &
           lmpi_id, ', Point',ipts,' is in quad face',face_index,             &
         ' of boundary ', ib, ' for geometry ',ivol,', dist=',dist,', p=', po,&
           ', pc=', pc
           endif

          end do loop_quads

        end if quads

      end do loop_bdy

      call lmpi_reduce( point_is_found, point_is_found_global )
      call lmpi_bcast( point_is_found_global )

      if ( flag_tri ==1 .and. point_is_found_global(ivol,ipts) == 0 ) then
          sample(ivol)%print_list(1:3,ipts) = p_tri
          p2c_local_cells(ivol,ipts) = face_tri
          p2c_local_bdy(ivol,ipts)   = ib_tri
          p2c_local_elem(ivol,ipts)  = elem_tri
          p2c_local_face(ivol,ipts)  = 1
          point_is_found(ivol,ipts)  = 1
          if ( verbose )                                 &
            write(6,'(i6,3(a,i6),3(1x,f15.5))') lmpi_id, &
            ', Point',ipts,' is resetting to tri face',  &
            face_index,' of boundary', ib, po
!       endif
      endif
      if ( flag_quad ==1 .and. point_is_found_global(ivol,ipts) == 0 ) then
        if ( dist_quad < dist_tri ) then
          sample(ivol)%print_list(1:3,ipts) = p_quad
          p2c_local_cells(ivol,ipts) = face_quad
          p2c_local_bdy(ivol,ipts)   = ib_quad
          p2c_local_elem(ivol,ipts)  = elem_quad
          p2c_local_face(ivol,ipts)  = 2
          point_is_found(ivol,ipts)  = 1
          if ( verbose )                               &
             write(6,'(3(a,i6))')                      &
            'Point',ipts,' is resetting to quad face', &
            face_index,' of boundary', ib
        endif
      endif

    case ( 'volume_points','streamsurface' )

      element_type_count:  do element_set = 1, grid%nelem

        type_cell     =  grid%elem(element_set)%type_cell
        node_per_cell =  grid%elem(element_set)%node_per_cell
        c2n           => grid%elem(element_set)%c2n

        if ( skeleton > 2 ) then
          write(6,'(a,i8)') 'pst-  element_set=',element_set
          write(6,'(a,a)')  'pst-     type_cel=',type_cell
          write(6,'(a,i8)') 'pst-node_per_cell=',node_per_cell
          write(6,'(a,i8)') 'pst-ncell        =',grid%elem(element_set)%ncell
          write(6,'(a,3(1x,f12.4))') 'pst-  points     =',po
        endif

        element_loop : do cell = 1, grid%elem(element_set)%ncell

          if ( .not. grid_cell_unique(grid,                                    &
                     grid%elem(element_set)%c2n(1:node_per_cell,cell) )        &
                        ) cycle element_loop

          px(0)                = po(1)
          py(0)                = po(2)
          pz(0)                = po(3)
          px(1:node_per_cell)  = grid%x(c2n(1:node_per_cell,cell))
          py(1:node_per_cell)  = grid%y(c2n(1:node_per_cell,cell))
          pz(1:node_per_cell)  = grid%z(c2n(1:node_per_cell,cell))

          if ( is_inside_element ( node_per_cell, px, py, pz ) ) then

            p2c_local_cells(ivol,ipts) = cell
            p2c_local_elem(ivol,ipts)  = element_set
            point_is_found(ivol,ipts)  = 1

            if ( verbose ) then
              write(6,'(a,i4,a,i3,a,i5,a,i10,a,a)')                &
                         ' lmpi_id=', lmpi_id                      &
                       , ',   ivol=', ivol                         &
                       , ',  point=', ipts                         &
                       , ',    p2l=', p2c_local_cells(ivol,ipts)   &
                       , ',   type= ', type_cell
            endif

          endif

        end do element_loop

      end do element_type_count

    case default

      return

    end select geo_case

  end subroutine point_survey_template

  include 'cross_product.f90'

end module sampling_templates

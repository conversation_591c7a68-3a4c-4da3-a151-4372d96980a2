!   vim: set filetype=fortran:
! emacs: -*- f90 -*-
!-----------------------------------------------------------------------------80

test_suite wall_model_main


  integer, parameter  :: dp = selected_real_kind(P=15)

  real(dp),  dimension(3,3) :: gradv
  real(dp),  dimension(3,3) :: s
  real(dp),  dimension(3,3) :: w

  real(dp), parameter       :: tol = 1.0e-8

  real(dp), parameter       :: gamma               =  1.4_dp
  real(dp), parameter       :: turbulent_prandtl   =  0.72_dp
  real(dp), parameter       :: sutherland_constant =  198.6_dp
  real(dp), parameter       :: tref                =  540.0_dp
  real(dp), parameter       :: kappa               =  0.41_dp
  real(dp), parameter       :: b                   =  5.0_dp
  real(dp), parameter       :: cmu_0               =  0.09_dp

  integer,  parameter       :: traceless           =  1


  real(dp), parameter, dimension(3,3) :: delta =   &
            reshape((/1.,0.,0.,0.,1.,0.,0.,0.,1./),(/3,3/))

!=============================================================================80
!=============================================================================80
!=============================================================================80
test test_pressure_jacobian_array

  use ddt, only : ddt5, assignment(=), operator(*), operator(-), operator(+)
  use ddt, only : ddt5_identity, operator(/)

  type(ddt5), dimension(5) :: f, g, h, q_ddt, eqn
  type(ddt5)               :: rho, u, v, w, p, e, ubar

  real(dp), dimension(5) :: q
  real(dp), dimension(3) :: area

  real(dp), parameter :: zero  = 0.0_dp
  real(dp), parameter :: half  = 0.5_dp
  real(dp), parameter :: one   = 1.0_dp
  real(dp), parameter :: gamma = 1.4_dp

  area  = (/5.0_dp, 5.0_dp, 5.0_dp/)

  q     = (/1.0_dp, 1.0_dp, 1.0_dp, 1.0_dp, 2.0_dp/)
  q_ddt = ddt5_identity(q)

  rho   = q_ddt(1)
  u     = q_ddt(2)
  v     = q_ddt(3)
  w     = q_ddt(4)
  p     = q_ddt(5)
  e     = (p/(gamma-one)) + half*rho*(u*u+v*v+w*w)
  ubar  = u*area(1) + v*area(2) + w*area(3)

  f(1) = zero
  f(2) = p*delta(1,1)
  f(3) = p*delta(1,2)
  f(4) = p*delta(1,2)
  f(5) = p*u

  g(1) = zero
  g(2) = p*delta(2,1)
  g(3) = p*delta(2,2)
  g(4) = p*delta(2,3)
  g(5) = p*v

  h(1) = zero
  h(2) = p*delta(3,1)
  h(3) = p*delta(3,2)
  h(4) = p*delta(3,3)
  h(5) = p*w

  eqn(1) = f(1)*area(1) + g(1)*area(2) + h(1)*area(3)
  eqn(2) = f(2)*area(1) + g(2)*area(2) + h(2)*area(3)
  eqn(3) = f(3)*area(1) + g(3)*area(2) + h(3)*area(3)
  eqn(4) = f(4)*area(1) + g(4)*area(2) + h(4)*area(3)
  eqn(5) = f(5)*area(1) + g(5)*area(2) + h(5)*area(3)

  write(6,'(10(1x,f12.5))')  eqn(1)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(2)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(3)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(4)%d(1:5)
  write(6,'(10(1x,f12.5))')  eqn(5)%d(1:5)

  assert_real_equal(   0.0_dp,  eqn(5)%d(1) ) 
  assert_real_equal(  10.0_dp,  eqn(5)%d(2) ) 
  assert_real_equal(  10.0_dp,  eqn(5)%d(3) ) 
  assert_real_equal(  10.0_dp,  eqn(5)%d(4) ) 
  assert_real_equal(  15.0_dp,  eqn(5)%d(5) ) 

end test

test test_weak_wall_jacobians

  use grid_types,    only : grid_type
! use soln_types,    only : soln_type
  use grids,         only : nullify_grid, allocate_grid, deallocate_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types, only : elem_type
  use allocations,   only : my_alloc_ptr, my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
  use element_defs,  only : max_node_per_cell, max_face_per_cell
  use fluid,         only : setup_fluid_gamma, setup_sutherland_constant
  use info_depr,     only : xmach, re
  use thermo,        only : q_type, primitive_q_type
  use bc_names,      only : twall
  use turbulence_info, only : model_strain_form_int
  use nml_boundary_conditions, only : wall_velocity

  type(grid_type)                   :: grid
! type(soln_type)                   :: soln
  real(dp),          dimension(5,9) :: q_dof
  real(dp),          dimension(  9) :: amut
  real(dp),          dimension(2,9) :: turb
  real(dp),          dimension(5,5) :: deqn_dq
  real(dp),          dimension(5,5) :: deqn_dq1
  real(dp),          dimension(5,5) :: deqn_dq2
  real(dp)                          :: xmr
  real(dp)                          :: xnorm
  real(dp)                          :: ynorm
  real(dp)                          :: znorm
  real(dp)                          :: area
  real(dp), dimension(1)            :: unorm_bc
! type(bcgrid_type), dimension(1)   :: bc
  type(bcsoln_type), dimension(1)   :: bcsoln
! integer                           :: nelem
! type(elem_type),   dimension(2)   :: elem
  integer                           :: ib
! integer                           :: ibc
  integer                           :: nodes_per_face
  integer                           :: face_index
  integer                           :: corner_index
  integer                           :: eqn_set

  integer                           :: cell
  integer                           :: njac
  integer                           :: n_tot

  max_face_per_cell = 6
  max_node_per_cell = 8
  eqn_set         = 0
  cell            = 1
  njac            = 5
  n_tot           = 5

  call setup_fluid_gamma
  call setup_sutherland_constant

  call nullify_grid ( grid )
  grid%nnodes0  = 9
  grid%nnodes01 = 9
  allocate ( grid%x( 9 ) )
  allocate ( grid%y( 9 ) )
  allocate ( grid%z( 9 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(2) )

  call allocate_bcsoln   ( 9, bcsoln(1) )

  call initialize_elem ( grid%elem(1), 'pyr' )
  call initialize_elem ( grid%elem(2), 'hex' )
  call allocate_elem ( grid%elem(2), .false. )

  grid%nelem      = 2
  grid%elem%ncell = 2

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.0_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 0.0_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

  amut(1)    = 1.0e-6_dp
  amut(2)    = 1.0e-6_dp
  amut(3)    = 1.0e-6_dp
  amut(4)    = 1.0e-6_dp
  amut(5)    = 1.0e-6_dp
  amut(6)    = 1.0e-6_dp
  amut(7)    = 1.0e-6_dp
  amut(8)    = 1.0e-6_dp
  amut(9)    = 1.0e-6_dp

  q_dof(1,:) = 1.0_dp

  q_dof(2,1) = 0.0001_dp
  q_dof(2,2) = 0.0001_dp
  q_dof(2,3) = 0.0001_dp
  q_dof(2,4) = 0.0001_dp
  q_dof(2,5) = 0.0010_dp
  q_dof(2,6) = 0.0010_dp
  q_dof(2,7) = 0.0010_dp
  q_dof(2,8) = 0.0010_dp
  q_dof(2,9) = 0.0005_dp

  q_dof(3,1) = 0.00010_dp
  q_dof(3,2) = 0.00010_dp
  q_dof(3,3) = 0.00010_dp
  q_dof(3,4) = 0.00010_dp
  q_dof(3,5) = 0.00015_dp
  q_dof(3,6) = 0.00015_dp
  q_dof(3,7) = 0.00015_dp
  q_dof(3,8) = 0.00015_dp
  q_dof(3,9) = 0.00005_dp

  q_dof(4,1) = 0.00010_dp
  q_dof(4,2) = 0.00010_dp
  q_dof(4,3) = 0.00010_dp
  q_dof(4,4) = 0.00010_dp
  q_dof(4,5) = 0.00015_dp
  q_dof(4,6) = 0.00015_dp
  q_dof(4,7) = 0.00015_dp
  q_dof(4,8) = 0.00015_dp
  q_dof(4,9) = 0.00005_dp


  q_dof(5,:) = 0.714_dp

  turb(1,:) = 0.0001_dp
  turb(2,:) = .001_dp

  ib                  = 1
  grid%bc(ib)%ibc     = 4110 ! viscous wall function boundary
  grid%bc(ib)%nbnode  = 5 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 1
  grid%bc(ib)%nbfaceq = 1

  ! account for one triangle face and one quad face
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2ntb ( grid%bc(ib)%nbfacet, 5 ) )
  allocate ( grid%bc(ib)%f2nqb ( grid%bc(ib)%nbfaceq, 6 ) )
  allocate ( bcsoln(ib)%mu_t_wf ( grid%bc(ib)%nbnode ) )
  allocate ( bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )
  allocate ( grid%elem(1)%c2n  ( 5, 1 ) )
  allocate ( grid%elem(2)%c2n  ( 8, 1 ) )

  wall_velocity(1,:) = (/0.3_dp,0.2_dp,0.1_dp/)

  face_index     = 1
  nodes_per_face = 3
  grid%bc(ib)%f2ntb(face_index,1) = 1 ! 9 ! face 1, node 1
  grid%bc(ib)%f2ntb(face_index,2) = 5 ! 4 ! face 1, node 2
  grid%bc(ib)%f2ntb(face_index,3) = 4 ! 1 ! face 1, node 3
  grid%bc(ib)%f2ntb(face_index,4) = 1 ! cell number
  grid%bc(ib)%f2ntb(face_index,5) = 1 ! element type
  grid%bc(ib)%nbfacet             = 1 ! number of tri faces

  face_index     = 1
  nodes_per_face = 4
  grid%bc(ib)%f2nqb(face_index,1) = 1 ! 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 4 ! 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 2 ! 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 2 ! element type
  grid%bc(ib)%nbfaceq             = 1 ! number of quad faces

  grid%bc(ib)%ibnode(1) = 1 ! boundary node
  grid%bc(ib)%ibnode(2) = 2 ! boundary node
  grid%bc(ib)%ibnode(3) = 3 ! boundary node
  grid%bc(ib)%ibnode(4) = 4 ! boundary node
  grid%bc(ib)%ibnode(5) = 9 ! boundary node

  cell                = 1
  grid%elem(1)%c2n(1,cell) = 7 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 8
  grid%elem(1)%c2n(3,cell) = 4
  grid%elem(1)%c2n(4,cell) = 1
  grid%elem(1)%c2n(5,cell) = 9
  grid%elem(1)%node_per_cell = 5

  cell                = 1
  grid%elem(2)%c2n(1,cell) = 1
  grid%elem(2)%c2n(2,cell) = 7
  grid%elem(2)%c2n(3,cell) = 2
  grid%elem(2)%c2n(4,cell) = 5
  grid%elem(2)%c2n(5,cell) = 4
  grid%elem(2)%c2n(6,cell) = 8
  grid%elem(2)%c2n(7,cell) = 3
  grid%elem(2)%c2n(8,cell) = 6
  grid%elem(2)%node_per_cell = 8

! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  xmr   = xmach / re
  model_strain_form_int = traceless
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible
  face_index      = 1
  corner_index    = 1
  nodes_per_face  = 4
  xnorm           = 1.0_dp
  ynorm           = 1.0_dp
  znorm           = 1.0_dp
  area            = 1.0_dp
  unorm_bc        = 0.0_dp

  write(*,*) 'ib=',ib

  deqn_dq1 = jacobian_viscous_weak_tauij (grid, q_dof, amut, ib     &
                         , face_index, corner_index                 &
                         , xnorm, ynorm, znorm, area                &
                         , nodes_per_face, xmr                      &
                         , model_strain_form_int, eqn_set, unorm_bc )
!  do i = 1, 5
!  write(6,'(10(1x,f20.10))')  (deqn_dq1(j,i),j=1,5)
!  enddo

  deqn_dq2 = jacobian_viscous_weak_pressure (                       &
                           xnorm, ynorm, znorm, area                &
                          )

  deqn_dq = deqn_dq1 - deqn_dq2
! do i = 1, 5
! write(6,'(10(1x,f20.10))')  (deqn_dq(j,i),j=1,5)
! enddo

  write(6,'(a,f20.10,a)') '! equation 1'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,1),'_dp,  deqn_dq(1,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,2),'_dp,  deqn_dq(1,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,3),'_dp,  deqn_dq(1,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,4),'_dp,  deqn_dq(1,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,5),'_dp,  deqn_dq(1,5) ) '
  write(6,'(a,f20.10,a)') '! equation 2'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,1),'_dp,  deqn_dq(2,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,2),'_dp,  deqn_dq(2,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,3),'_dp,  deqn_dq(2,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,4),'_dp,  deqn_dq(2,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,5),'_dp,  deqn_dq(2,5) ) '
  write(6,'(a,f20.10,a)') '! equation 3'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,1),'_dp,  deqn_dq(3,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,2),'_dp,  deqn_dq(3,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,3),'_dp,  deqn_dq(3,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,4),'_dp,  deqn_dq(3,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,5),'_dp,  deqn_dq(3,5) ) '
  write(6,'(a,f20.10,a)') '! equation 4'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,1),'_dp,  deqn_dq(4,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,2),'_dp,  deqn_dq(4,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,3),'_dp,  deqn_dq(4,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,4),'_dp,  deqn_dq(4,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,5),'_dp,  deqn_dq(4,5) ) '
  write(6,'(a,f20.10,a)') '! equation 5'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,1),'_dp,  deqn_dq(5,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,2),'_dp,  deqn_dq(5,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,3),'_dp,  deqn_dq(5,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,4),'_dp,  deqn_dq(5,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,5),'_dp,  deqn_dq(5,5) ) '


! equation 1
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,1) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,2) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,3) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,4) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,5) ) 
! equation 2
assert_real_equal(        0.0000000000_dp,  deqn_dq(2,1) ) 
assert_real_equal(     -291.4922683207_dp,  deqn_dq(2,2) ) 
assert_real_equal(        4.1641752617_dp,  deqn_dq(2,3) ) 
assert_real_equal(      154.0744846838_dp,  deqn_dq(2,4) ) 
assert_real_equal(      -64.1407174793_dp,  deqn_dq(2,5) ) 
! equation 3
assert_real_equal(        0.0000000000_dp,  deqn_dq(3,1) ) 
assert_real_equal(      -16.6567010469_dp,  deqn_dq(3,2) ) 
assert_real_equal(     -295.6564435824_dp,  deqn_dq(3,3) ) 
assert_real_equal(      141.5819588986_dp,  deqn_dq(3,4) ) 
assert_real_equal(      -36.3533857509_dp,  deqn_dq(3,5) ) 
! equation 4
assert_real_equal(        0.0000000000_dp,  deqn_dq(4,1) ) 
assert_real_equal(     -241.5221651800_dp,  deqn_dq(4,2) ) 
assert_real_equal(     -233.1938146565_dp,  deqn_dq(4,3) ) 
assert_real_equal(     -370.6115982934_dp,  deqn_dq(4,4) ) 
assert_real_equal(     -176.1167980853_dp,  deqn_dq(4,5) ) 
! equation 5
assert_real_equal(        0.0000000000_dp,  deqn_dq(5,1) ) 
assert_real_equal(      -12.6482399139_dp,  deqn_dq(5,2) ) 
assert_real_equal(       -8.2017465824_dp,  deqn_dq(5,3) ) 
assert_real_equal(      -12.4534215769_dp,  deqn_dq(5,4) ) 
assert_real_equal(    -1005.7095566755_dp,  deqn_dq(5,5) ) 

  write(*,*) 'Finish part 1 test_weak_wall_flux'

!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.5e-2_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 1.0e-2_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 1.0e-2_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  xmr   = xmach / re
  deqn_dq = 0.0_dp
  model_strain_form_int = traceless
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible
! model_strain_form_int = 'incompressible'
  face_index      = 1
  nodes_per_face  = 3

  write(*,*) 'ib=',ib

  deqn_dq1 = jacobian_viscous_weak_tauij (grid, q_dof, amut, ib     &
                         , face_index, corner_index                 &
                         , xnorm, ynorm, znorm, area                &
                         , nodes_per_face, xmr                      &
                         , model_strain_form_int, eqn_set, unorm_bc )

  deqn_dq2 = jacobian_viscous_weak_pressure (                       &
                           xnorm, ynorm, znorm, area                &
                          )

  deqn_dq = deqn_dq1 - deqn_dq2

! do i = 1, 5
! write(6,'(10(1x,f20.10))')  (deqn_dq(j,i),j=1,5)
! enddo

  write(6,'(a,f20.10,a)') '! equation 1'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,1),'_dp,  deqn_dq(1,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,2),'_dp,  deqn_dq(1,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,3),'_dp,  deqn_dq(1,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,4),'_dp,  deqn_dq(1,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(1,5),'_dp,  deqn_dq(1,5) ) '
  write(6,'(a,f20.10,a)') '! equation 2'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,1),'_dp,  deqn_dq(2,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,2),'_dp,  deqn_dq(2,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,3),'_dp,  deqn_dq(2,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,4),'_dp,  deqn_dq(2,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(2,5),'_dp,  deqn_dq(2,5) ) '
  write(6,'(a,f20.10,a)') '! equation 3'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,1),'_dp,  deqn_dq(3,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,2),'_dp,  deqn_dq(3,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,3),'_dp,  deqn_dq(3,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,4),'_dp,  deqn_dq(3,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(3,5),'_dp,  deqn_dq(3,5) ) '
  write(6,'(a,f20.10,a)') '! equation 4'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,1),'_dp,  deqn_dq(4,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,2),'_dp,  deqn_dq(4,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,3),'_dp,  deqn_dq(4,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,4),'_dp,  deqn_dq(4,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(4,5),'_dp,  deqn_dq(4,5) ) '
  write(6,'(a,f20.10,a)') '! equation 5'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,1),'_dp,  deqn_dq(5,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,2),'_dp,  deqn_dq(5,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,3),'_dp,  deqn_dq(5,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,4),'_dp,  deqn_dq(5,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',deqn_dq(5,5),'_dp,  deqn_dq(5,5) ) '


! equation 1
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,1) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,2) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,3) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,4) ) 
assert_real_equal(        0.0000000000_dp,  deqn_dq(1,5) ) 
! equation 2
assert_real_equal(        0.0000000000_dp,  deqn_dq(2,1) ) 
assert_real_equal(     -855.0439870740_dp,  deqn_dq(2,2) ) 
assert_real_equal(       30.5372852526_dp,  deqn_dq(2,3) ) 
assert_real_equal(      563.5517187533_dp,  deqn_dq(2,4) ) 
assert_real_equal(     -126.5709699469_dp,  deqn_dq(2,5) ) 
! equation 3
assert_real_equal(        0.0000000000_dp,  deqn_dq(3,1) ) 
assert_real_equal(      -38.8656357761_dp,  deqn_dq(3,2) ) 
assert_real_equal(     -868.9245712797_dp,  deqn_dq(3,3) ) 
assert_real_equal(      521.9099661361_dp,  deqn_dq(3,4) ) 
assert_real_equal(      -63.7576186058_dp,  deqn_dq(3,5) ) 
! equation 4
assert_real_equal(        0.0000000000_dp,  deqn_dq(4,1) ) 
assert_real_equal(     -838.3872860271_dp,  deqn_dq(4,2) ) 
assert_real_equal(     -810.6261176156_dp,  deqn_dq(4,3) ) 
assert_real_equal(    -1135.4317880301_dp,  deqn_dq(4,4) ) 
assert_real_equal(     -312.2226686924_dp,  deqn_dq(4,5) ) 
! equation 5
assert_real_equal(        0.0000000000_dp,  deqn_dq(5,1) ) 
assert_real_equal(      -58.9135523204_dp,  deqn_dq(5,2) ) 
assert_real_equal(      -38.1598802483_dp,  deqn_dq(5,3) ) 
assert_real_equal(      -22.6599204623_dp,  deqn_dq(5,4) ) 
assert_real_equal(    -3002.9895490412_dp,  deqn_dq(5,5) ) 


  call deallocate_elem ( grid%elem(1) )
! call deallocate_bc   ( bcsoln(1) )
  call deallocate_bc   ( grid%bc(1) )

  write(6,'(a,10(1x,f20.10))') 'fin test_weak_wall_jacobians'

end test

test test_wall_flux

  use grid_types,    only : grid_type
! use soln_types,    only : soln_type
  use grids,         only : nullify_grid, allocate_grid, deallocate_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types, only : elem_type
  use allocations,   only : my_alloc_ptr, my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
  use element_defs,  only : max_node_per_cell, max_face_per_cell
  use fluid,         only : setup_fluid_gamma, setup_sutherland_constant
  use info_depr,     only : xmach, re
  use thermo,        only : q_type, primitive_q_type
  use bc_names,      only : twall
  use turbulence_info, only : model_strain_form_int
  use nml_boundary_conditions, only : wall_velocity

  type(grid_type)                   :: grid
! type(soln_type)                   :: soln
  real(dp),          dimension(5,9) :: q_dof
  real(dp),          dimension(  9) :: amut
  real(dp),          dimension(5,9) :: res
  real(dp),          dimension(2,9) :: turb
! type(bcgrid_type), dimension(1)   :: bc
  type(bcsoln_type), dimension(1)   :: bcsoln
! integer                           :: nelem
! type(elem_type),   dimension(2)   :: elem
  integer                           :: ib
! integer                           :: ibc
  integer                           :: nodes_per_face
  integer                           :: face_index
  integer                           :: eqn_set

  integer                           :: cell
  integer                           :: njac
  integer                           :: n_tot
  integer                           :: i

  max_face_per_cell = 6
  max_node_per_cell = 8
  eqn_set         = 0
  cell            = 1
  njac            = 5
  n_tot           = 5

  call setup_fluid_gamma
  call setup_sutherland_constant

  call nullify_grid ( grid )
  grid%nnodes0  = 9
  grid%nnodes01 = 9
  allocate ( grid%x( 9 ) )
  allocate ( grid%y( 9 ) )
  allocate ( grid%z( 9 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(2) )

  call allocate_bcsoln   ( 9, bcsoln(1) )

  call initialize_elem ( grid%elem(1), 'pyr' )
  call initialize_elem ( grid%elem(2), 'hex' )
  call allocate_elem ( grid%elem(2), .false. )

  grid%nelem      = 2
  grid%elem%ncell = 2

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.0_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 0.0_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

  amut(1)    = 1.0e-6_dp
  amut(2)    = 1.0e-6_dp
  amut(3)    = 1.0e-6_dp
  amut(4)    = 1.0e-6_dp
  amut(5)    = 1.0e-6_dp
  amut(6)    = 1.0e-6_dp
  amut(7)    = 1.0e-6_dp
  amut(8)    = 1.0e-6_dp
  amut(9)    = 1.0e-6_dp

  q_dof(1,:) = 1.0_dp

  q_dof(2,1) = 0.0001_dp
  q_dof(2,2) = 0.0001_dp
  q_dof(2,3) = 0.0001_dp
  q_dof(2,4) = 0.0001_dp
  q_dof(2,5) = 0.0010_dp
  q_dof(2,6) = 0.0010_dp
  q_dof(2,7) = 0.0010_dp
  q_dof(2,8) = 0.0010_dp
  q_dof(2,9) = 0.0005_dp

  q_dof(3,1) = 0.00010_dp
  q_dof(3,2) = 0.00010_dp
  q_dof(3,3) = 0.00010_dp
  q_dof(3,4) = 0.00010_dp
  q_dof(3,5) = 0.00015_dp
  q_dof(3,6) = 0.00015_dp
  q_dof(3,7) = 0.00015_dp
  q_dof(3,8) = 0.00015_dp
  q_dof(3,9) = 0.00005_dp

  q_dof(4,1) = 0.00010_dp
  q_dof(4,2) = 0.00010_dp
  q_dof(4,3) = 0.00010_dp
  q_dof(4,4) = 0.00010_dp
  q_dof(4,5) = 0.00015_dp
  q_dof(4,6) = 0.00015_dp
  q_dof(4,7) = 0.00015_dp
  q_dof(4,8) = 0.00015_dp
  q_dof(4,9) = 0.00005_dp


  q_dof(5,:) = 0.714_dp

  turb(1,:) = 0.0001_dp
  turb(2,:) = .001_dp

  ib                  = 1
  grid%bc(ib)%ibc     = 4110 ! viscous wall function boundary
  grid%bc(ib)%nbnode  = 5 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 1
  grid%bc(ib)%nbfaceq = 1

  ! account for one triangle face and one quad face
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2ntb ( grid%bc(ib)%nbfacet, 5 ) )
  allocate ( grid%bc(ib)%f2nqb ( grid%bc(ib)%nbfaceq, 6 ) )
  allocate ( bcsoln(ib)%mu_t_wf ( grid%bc(ib)%nbnode ) )
  allocate ( bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )
  allocate ( grid%elem(1)%c2n  ( 5, 1 ) )
  allocate ( grid%elem(2)%c2n  ( 8, 1 ) )

  wall_velocity(1,:) = (/0.3_dp,0.2_dp,0.1_dp/)

  face_index     = 1
  nodes_per_face = 3
  grid%bc(ib)%f2ntb(face_index,1) = 1 ! 9 ! face 1, node 1
  grid%bc(ib)%f2ntb(face_index,2) = 5 ! 4 ! face 1, node 2
  grid%bc(ib)%f2ntb(face_index,3) = 4 ! 1 ! face 1, node 3
  grid%bc(ib)%f2ntb(face_index,4) = 1 ! cell number
  grid%bc(ib)%f2ntb(face_index,5) = 1 ! element type
  grid%bc(ib)%nbfacet             = 1 ! number of tri faces

  face_index     = 1
  nodes_per_face = 4
  grid%bc(ib)%f2nqb(face_index,1) = 1 ! 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 4 ! 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 2 ! 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 2 ! element type
  grid%bc(ib)%nbfaceq             = 1 ! number of quad faces

  grid%bc(ib)%ibnode(1) = 1 ! boundary node
  grid%bc(ib)%ibnode(2) = 2 ! boundary node
  grid%bc(ib)%ibnode(3) = 3 ! boundary node
  grid%bc(ib)%ibnode(4) = 4 ! boundary node
  grid%bc(ib)%ibnode(5) = 9 ! boundary node

  cell                = 1
  grid%elem(1)%c2n(1,cell) = 7 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 8
  grid%elem(1)%c2n(3,cell) = 4
  grid%elem(1)%c2n(4,cell) = 1
  grid%elem(1)%c2n(5,cell) = 9
  grid%elem(1)%node_per_cell = 5

  cell                = 1
  grid%elem(2)%c2n(1,cell) = 1
  grid%elem(2)%c2n(2,cell) = 7
  grid%elem(2)%c2n(3,cell) = 2
  grid%elem(2)%c2n(4,cell) = 5
  grid%elem(2)%c2n(5,cell) = 4
  grid%elem(2)%c2n(6,cell) = 8
  grid%elem(2)%c2n(7,cell) = 3
  grid%elem(2)%c2n(8,cell) = 6
  grid%elem(2)%node_per_cell = 8

! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  model_strain_form_int = traceless
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  write(*,*) 'ib=',ib
  call test_weak_wall_flux  (grid, q_dof, amut, res, ib, eqn_set )

  do i = 1, 9
  write(6,'(10(1x,f20.10))')  res(1,i),res(2,i),res(3,i),res(4,i),res(5,i)
  enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,1),',  res(2,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,1),',  res(3,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,1),',  res(4,1) ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,2),',  res(2,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,2),',  res(3,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,2),',  res(4,2) ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,3),',  res(2,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,3),',  res(3,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,3),',  res(4,3) ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,4),',  res(2,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,4),',  res(3,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,4),',  res(4,4) ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,5),',  res(2,5) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,5),',  res(3,5) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,5),',  res(4,5) ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,9),',  res(2,9) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,9),',  res(3,9) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,9),',  res(4,9) ) '

! node1
assert_real_equal(      149.7430136809,  res(2,1) ) 
assert_real_equal(       99.9276669086,  res(3,1) ) 
assert_real_equal(       67.1805926504,  res(4,1) ) 
! node2
assert_real_equal(      112.0579562930,  res(2,2) ) 
assert_real_equal(       74.8989383450,  res(3,2) ) 
assert_real_equal(       49.8951479860,  res(4,2) ) 
! node3
assert_real_equal(      112.0579562930,  res(2,3) ) 
assert_real_equal(       74.8989383450,  res(3,3) ) 
assert_real_equal(       49.8951479860,  res(4,3) ) 
! node4
assert_real_equal(      149.7430136809,  res(2,4) ) 
assert_real_equal(       99.9276669086,  res(3,4) ) 
assert_real_equal(       67.1805926504,  res(4,4) ) 
! node5
assert_real_equal(        0.0000000000,  res(2,5) ) 
assert_real_equal(        0.0000000000,  res(3,5) ) 
assert_real_equal(        0.0000000000,  res(4,5) ) 
! node9
assert_real_equal(       37.6850573879,  res(2,9) ) 
assert_real_equal(       25.0287285636,  res(3,9) ) 
assert_real_equal(       17.2854446644,  res(4,9) ) 

  write(*,*) 'Finish part 1 test_weak_wall_flux'

!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.5e-2_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 1.0e-2_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 1.0e-2_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  xmach = 1.5_dp
  re    = 0.02_dp
  res   = 0.0_dp
  model_strain_form_int = traceless
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible
! model_strain_form_int = 'incompressible'

  write(*,*) 'ib=',ib

  call test_weak_wall_flux  (grid, q_dof, amut, res, ib, eqn_set )

  do i = 1, 9
  write(6,'(10(1x,f20.10))')  res(1,i),res(2,i),res(3,i),res(4,i),res(5,i)
  enddo
  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,1),',  res(2,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,1),',  res(3,1) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,1),',  res(4,1) ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,2),',  res(2,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,2),',  res(3,2) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,2),',  res(4,2) ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,3),',  res(2,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,3),',  res(3,3) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,3),',  res(4,3) ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,4),',  res(2,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,4),',  res(3,4) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,4),',  res(4,4) ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,5),',  res(2,5) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,5),',  res(3,5) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,5),',  res(4,5) ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(2,9),',  res(2,9) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(3,9),',  res(3,9) ) '
  write(6,'(a,f20.10,a)') 'assert_real_equal(',res(4,9),',  res(4,9) ) '


! node1
assert_real_equal(      171.0665703896,  res(2,1) ) 
assert_real_equal(      114.3051953836,  res(3,1) ) 
assert_real_equal(       75.6504303349,  res(4,1) ) 
! node2
assert_real_equal(      129.3730617847,  res(2,2) ) 
assert_real_equal(       86.4266940891,  res(3,2) ) 
assert_real_equal(       56.8594739634,  res(4,2) ) 
! node3
assert_real_equal(      129.3013386206,  res(2,3) ) 
assert_real_equal(       86.4251671931,  res(3,3) ) 
assert_real_equal(       57.1826281129,  res(4,3) ) 
! node4
assert_real_equal(      170.9948472255,  res(2,4) ) 
assert_real_equal(      114.3036684877,  res(3,4) ) 
assert_real_equal(       75.9735844844,  res(4,4) ) 
! node5
assert_real_equal(        0.0000000000,  res(2,5) ) 
assert_real_equal(        0.0000000000,  res(3,5) ) 
assert_real_equal(        0.0000000000,  res(4,5) ) 
! node9
assert_real_equal(       41.6919817089,  res(2,9) ) 
assert_real_equal(       27.8055164153,  res(3,9) ) 
assert_real_equal(       19.0065612254,  res(4,9) ) 


  call deallocate_elem ( grid%elem(1) )
! call deallocate_bc   ( bcsoln(1) )
  call deallocate_bc   ( grid%bc(1) )

  write(6,'(a,10(1x,f20.10))') 'fin test_weak_wall_flux'

end test

!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!               finish test_weak_wall_flux unit test
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80
!=============================================================================80

test return_weak_wall_flux_ddt

  use grid_types,    only : grid_type
  use grids,         only : nullify_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc
  use element_types, only : elem_type
  use allocations,   only : my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
! use ddt,           only : ddt5, assignment(=)

  type(grid_type)                   :: grid
  real(dp),          dimension(5,8) :: q_dof
  real(dp),          dimension(5,8) :: gradx, grady, gradz
  real(dp),          dimension(  8) :: amut
  integer                           :: ib
  integer                           :: face_index
  integer                           :: face_corner
  integer                           :: nodes_per_face
  integer                           :: eqn_set

  integer                           :: cell

  real(dp)                          :: tref
  real(dp)                          :: xmach
  real(dp)                          :: reyno
  real(dp)                          :: xmr
  real(dp)                          :: xnorm
  real(dp)                          :: ynorm
  real(dp)                          :: znorm
  real(dp)                          :: area
  real(dp), dimension(1)            :: unorm_bc
  real(dp)                          :: facespeed

  real(dp),   dimension(5)          :: actual
  real(dp),   dimension(5)          :: stresses
  real(dp),   dimension(5)          :: pressure
  integer                           :: max_node_per_cell = 8


  call nullify_grid ( grid )
  allocate ( grid%x( 8 ) )
  allocate ( grid%y( 8 ) )
  allocate ( grid%z( 8 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(1) )

  call nullify_elem ( grid%elem(1) )
  call initialize_elem ( grid%elem(1), 'hex' )
  call allocate_elem ( grid%elem(1), .false. )

  grid%nelem      = 1
  grid%elem%ncell = 1


  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 0.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 5.0e-2_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 0.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 1.0_dp; grid%y(4) = 0.0_dp; grid%z(4) = 5.0e-2_dp
  grid%x(5) = 0.0_dp; grid%y(5) = 1.0_dp; grid%z(5) = 0.0_dp
  grid%x(6) = 0.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 1.0_dp; grid%y(7) = 1.0_dp; grid%z(7) = 0.0_dp
  grid%x(8) = 1.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp

  amut       = 1.0e-6_dp

  q_dof(1,:) = 1.0_dp

  q_dof(2,1) = 0.001_dp
  q_dof(2,2) = 0.001_dp
  q_dof(2,3) = 0.001_dp
  q_dof(2,4) = 0.001_dp
  q_dof(2,5) = 0.0011_dp
  q_dof(2,6) = 0.0011_dp
  q_dof(2,7) = 0.0011_dp
  q_dof(2,8) = 0.0011_dp

  q_dof(3,:) = 0.0_dp

  q_dof(4,1) = 0.00015_dp
  q_dof(4,2) = 0.00015_dp
  q_dof(4,3) = 0.00015_dp
  q_dof(4,4) = 0.00015_dp
  q_dof(4,5) = 0.0001_dp
  q_dof(4,6) = 0.0001_dp
  q_dof(4,7) = 0.0001_dp
  q_dof(4,8) = 0.0001_dp

  q_dof(5,:) = 0.714_dp

! gradient pressure
  gradx(5,1) = 0.00_dp
  gradx(5,2) = 0.00_dp
  gradx(5,3) = 0.00_dp
  gradx(5,4) = 0.00_dp
  gradx(5,5) = 0.10_dp
  gradx(5,6) = 0.10_dp
  gradx(5,7) = 0.10_dp
  gradx(5,8) = 0.10_dp

  grady(5,1) = 0.10_dp
  grady(5,2) = 0.10_dp
  grady(5,3) = 0.10_dp
  grady(5,4) = 0.10_dp
  grady(5,5) = 0.00_dp
  grady(5,6) = 0.00_dp
  grady(5,7) = 0.00_dp
  grady(5,8) = 0.00_dp

  gradz(5,:) = 0.00_dp

  ib                  = 1
  grid%bc(ib)%ibc     = 4110
  grid%bc(ib)%nbnode  = 4 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 0
  grid%bc(ib)%nbfaceq = 1

! account for one face quad
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2nqb( grid%bc(ib)%nbfaceq , 6 ) )
  allocate ( grid%elem(1)%c2n  ( 8, 1 ) )

  cell           = 1
  face_index     = 1
  face_corner    = 2
  nodes_per_face = 4
  eqn_set = 0

  grid%bc(ib)%f2nqb(face_index,1) = 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 1 ! element type

  grid%bc(ib)%ibnode(grid%bc(1)%f2nqb(face_index,1)) = 5 ! boundary node count
  grid%bc(ib)%ibnode(grid%bc(1)%f2nqb(face_index,2)) = 7 ! boundary node count
  grid%bc(ib)%ibnode(grid%bc(1)%f2nqb(face_index,3)) = 3 ! boundary node count
  grid%bc(ib)%ibnode(grid%bc(1)%f2nqb(face_index,4)) = 1 ! boundary node count

  grid%elem(1)%c2n(1,cell) = 1 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 2
  grid%elem(1)%c2n(3,cell) = 3
  grid%elem(1)%c2n(4,cell) = 4
  grid%elem(1)%c2n(5,cell) = 5
  grid%elem(1)%c2n(6,cell) = 6
  grid%elem(1)%c2n(7,cell) = 7
  grid%elem(1)%c2n(8,cell) = 8

  xnorm =  0.0_dp
  ynorm =  0.0_dp
  znorm =  1.0_dp
  area  = 0.25_dp
  unorm_bc  = 0.0_dp

  tref    = 540.0_dp ! [R]
  xmach   = 0.1_dp
  reyno   = 1.0e+7
  xmr     = xmach / reyno
  facespeed = 0.0_dp

   stresses = residual_viscous_weak_tauij ( grid, q_dof, amut            &
                                , ib, face_index, nodes_per_face         &
                                , xmr, xnorm, ynorm, znorm, area         &
                                , eqn_set, unorm_bc )

   pressure = residual_viscous_weak_pressure ( grid, q_dof               &
                                , ib, face_index, face_corner            &
                                , nodes_per_face                         &
                                , max_node_per_cell                      &
                                , xnorm, ynorm, znorm, area              &
                                , eqn_set )

     actual = stresses - pressure


  write(6,'(a,10(1x,es25.15))') 'flux         : ', actual

  assert_equal_within(  0.0_dp,         actual(1), 1.0e-6 ) 
  assert_equal_within( -7.47083E-08_dp, actual(2), 1.0e-6 )
  assert_equal_within(  0.0_dp,         actual(3), 1.0e-6 )
  assert_equal_within(  0.1785_dp,      actual(4), 1.0e-6 )
  assert_equal_within(  0.0_dp,         actual(5), 1.0e-6 )
 
  call deallocate_elem ( grid%elem(1) )
  call deallocate_bc   ( grid%bc(1) )

end test

end test_suite

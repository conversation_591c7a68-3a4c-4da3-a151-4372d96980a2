!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite sampling_funclib

integer, parameter :: dp = selected_real_kind(P=15)

setup
  use fluid,                 only : gamma, gm1
  gamma = 1.4_dp
  gm1   = gamma - 1.0_dp
end setup

test sphere_line_test

  real(dp), parameter :: tol = 0.0001_dp
  real(dp)                 :: x , y , z
  real(dp)                 :: t
  real(dp)                 :: a, b, c, error
  real(dp), dimension(3) :: p0, n, pa, pb
  real(dp)               :: r0

  p0(1)  = 0.0_dp
  p0(2)  = 0.0_dp
  p0(3)  = 0.0_dp
  n(1)   = 1.0_dp
  n(2)   = 0.0_dp
  n(3)   = 0.0_dp
  pa(1)  = 0.5_dp
  pa(2)  = 0.0_dp
  pa(3)  = 0.0_dp
  pb(1)  = 1.5_dp
  pb(2)  = 0.0_dp
  pb(3)  = 0.0_dp

  call plane_line(p0,n,pa,pb,t)

  a     = pb(1) - pa(1)
  b     = pb(2) - pa(2)
  c     = pb(3) - pa(3)
  x     = pa(1) + t * a
  y     = pa(2) + t * b
  z     = pa(3) + t * c
  error = abs( sqrt( (x-p0(1))**2 + (y-p0(2))**2 + (z-p0(3))**2 ) )
  assert_equal_within( 0.0 , error , tol )




  p0(1)  = 1.0_dp
  p0(2)  = 0.0_dp
  p0(3)  = 1.0_dp
  n(1)   = -1.0_dp
  n(2)   =  0.0_dp
  n(3)   = -1.0_dp
  pa(1)  = 0.0_dp
  pa(2)  = 0.0_dp
  pa(3)  = 0.5_dp
  pb(1)  = 2.5_dp
  pb(2)  = 0.0_dp
  pb(3)  = 0.5_dp

  call plane_line(p0,n,pa,pb,t)

  a     = pb(1) - pa(1)
  b     = pb(2) - pa(2)
  c     = pb(3) - pa(3)
  x     = pa(1) + t * a
  y     = pa(2) + t * b
  z     = pa(3) + t * c

  assert_equal_within( 1.5 , x , tol )
  assert_equal_within( 0.5 , z , tol )




  p0(1)  = 0.0_dp
  p0(2)  = 0.0_dp
  p0(3)  = 0.0_dp
  r0     = 1.0_dp
  pa(1)  = 0.5_dp
  pa(2)  = 0.0_dp
  pa(3)  = 0.0_dp
  pb(1)  = 1.5_dp
  pb(2)  = 0.0_dp
  pb(3)  = 0.0_dp

  call sphere_line( p0, r0, pa, pb, t)

  a     = pb(1) - pa(1)
  b     = pb(2) - pa(2)
  c     = pb(3) - pa(3)
  x     = pa(1) + t * a
  y     = pa(2) + t * b
  z     = pa(3) + t * c
  error = abs( r0 - sqrt( (x-p0(1))**2 + (y-p0(2))**2 + (z-p0(3))**2 ) )

  assert_equal_within( 0.0 , error , tol )


  p0(1)  = 1.0_dp
  p0(2)  = 1.0_dp
  p0(3)  = 1.0_dp
  r0     = 1.0_dp
  pa(1)  = 1.5_dp
  pa(2)  = 1.5_dp
  pa(3)  = 1.5_dp
  pb(1)  = 2.5_dp
  pb(2)  = 2.5_dp
  pb(3)  = 2.5_dp

  call sphere_line( p0, r0, pa, pb, t)

  a     = pb(1) - pa(1)
  b     = pb(2) - pa(2)
  c     = pb(3) - pa(3)
  x     = pa(1) + t * a
  y     = pa(2) + t * b
  z     = pa(3) + t * c
  error = abs( r0 - sqrt( (x-p0(1))**2 + (y-p0(2))**2 + (z-p0(3))**2 ) )

  assert_equal_within( 0.0 , error , tol )

end test

test interior_test

  real(dp), parameter :: tol = 0.0001_dp
  real(dp), dimension(3)   :: p0, p1, p2, p3, p4, pa
  real(dp)                 :: pos
  logical                  :: inside

  p1(1)  =  0.0_dp
  p1(2)  =  0.0_dp
  p1(3)  =  0.0_dp

  p2(1)  = -0.1_dp
  p2(2)  =  0.0_dp
  p2(3)  =  0.9_dp

  p3(1)  =  0.9_dp
  p3(2)  =  0.0_dp
  p3(3)  =  1.1_dp

  p4(1)  =  1.1_dp
  p4(2)  =  0.0_dp
  p4(3)  =  0.0_dp

  p0(1:3)  =  0.25_dp*(p1(1:3)+p2(1:3)+p3(1:3)+p4(1:3))

  pa(1) = -1.1_dp; pa(2) = 0.0_dp; pa(3)=  1.0_dp; pos=1
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) = -0.8_dp; pa(2) = 0.0_dp; pa(3)=  0.9_dp; pos=2
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) = -0.5_dp; pa(2) = 0.0_dp; pa(3)=  0.8_dp; pos=3
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) = -0.2_dp; pa(2) = 0.0_dp; pa(3)=  0.7_dp; pos=4
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) =  0.1_dp; pa(2) = 0.0_dp; pa(3)=  0.6_dp; pos=5
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_true( inside )

  pa(1) =  0.4_dp; pa(2) = 0.0_dp; pa(3)=  0.5_dp; pos=6
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_true( inside )

  pa(1) =  0.7_dp; pa(2) = 0.0_dp; pa(3)=  0.4_dp; pos=7
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_true( inside )

  pa(1) =  1.0_dp; pa(2) = 0.0_dp; pa(3)=  0.3_dp; pos=8
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_true( inside )

  pa(1) =  1.3_dp; pa(2) = 0.0_dp; pa(3)=  0.2_dp; pos=9
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) =  1.6_dp; pa(2) = 0.0_dp; pa(3)=  0.1_dp; pos=10
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) =  1.9_dp; pa(2) = 0.0_dp; pa(3)=  0.0_dp; pos=11
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

  pa(1) =  2.1_dp; pa(2) = 0.0_dp; pa(3)= -0.1_dp; pos=12
  inside = is_inside_quad ( pa, p1, p2, p3, p4 )
  assert_false( inside )

end test



test is_quad_flat_test

  use sampling_types, only : sample_type

  type(sample_type) :: quad_slice

  logical :: flat

  quad_slice%p1(1) = 0.0_dp
  quad_slice%p1(2) = 0.0_dp
  quad_slice%p1(3) = 0.0_dp

  quad_slice%p2(1) = 0.0_dp
  quad_slice%p2(2) = 0.0_dp
  quad_slice%p2(3) = 0.0_dp
 
  quad_slice%p3(1) = 0.0_dp
  quad_slice%p3(2) = 0.0_dp
  quad_slice%p3(3) = 0.0_dp

  quad_slice%p4(1) = 0.0_dp
  quad_slice%p4(2) = 0.0_dp
  quad_slice%p4(3) = 0.0_dp

  call is_quad_flat(quad_slice,flat)

  assert_false( flat )

  quad_slice%p1(1) = 1.0_dp
  quad_slice%p1(2) = 0.0_dp
  quad_slice%p1(3) = 0.0_dp

  quad_slice%p2(1) = 1.0_dp
  quad_slice%p2(2) = 0.0_dp
  quad_slice%p2(3) = 5.0_dp
 
  quad_slice%p3(1) = 1.0_dp
  quad_slice%p3(2) = 5.0_dp
  quad_slice%p3(3) = 5.0_dp

  quad_slice%p4(1) = 1.0_dp
  quad_slice%p4(2) = 5.0_dp
  quad_slice%p4(3) = 0.0_dp

  call is_quad_flat(quad_slice,flat)

  assert_true( flat )

end test

test tet_vol_value_test

  real(dp), parameter :: tol = 0.0001_dp
  real(dp), dimension(3) :: p1, p2, p3, p4, pa, pb, pint
  real(dp)               :: s

  logical :: intersect

  p1(1) = 0.0_dp
  p1(2) = 0.0_dp
  p1(3) = 0.0_dp

  p2(1) = 0.0_dp
  p2(2) = 0.0_dp
  p2(3) = 0.0_dp
 
  p3(1) = 0.0_dp
  p3(2) = 0.0_dp
  p3(3) = 0.0_dp

  pa(1) = 0.0_dp
  pa(2) = 0.0_dp
  pa(3) = 0.0_dp

  assert_equal_within( 0.0 , tet_vol_value( p1, p2, p3, pa ), tol )

  p1(1) =  0.0_dp
  p1(2) =  0.0_dp
  p1(3) =  0.5_dp

  p2(1) =  0.5_dp
  p2(2) =  0.0_dp
  p2(3) = -0.5_dp

  p3(1) = -0.5_dp
  p3(2) =  0.0_dp
  p3(3) = -0.5_dp

  p4(1) = -0.5_dp
  p4(2) =  0.0_dp
  p4(3) =  0.5_dp

  pa(1) =  0.0_dp
  pa(2) =  0.5_dp
  pa(3) =  0.0_dp

  pb(1) =  0.0_dp
  pb(2) = -0.5_dp
  pb(3) =  0.0_dp

  assert_equal_within(  0.50 , tet_vol_value( p1, p3, p2, pa ) , tol )
  assert_equal_within(  0.50 , tet_vol_value( p3, p2, p1, pa ) , tol )
  assert_equal_within(  0.50 , tet_vol_value( p2, p1, p3, pa ) , tol )
  assert_equal_within(  0.25 , tet_vol_value( p1, p4, p3, pa ) , tol )
  assert_equal_within(  0.25 , tet_vol_value( p4, p3, p1, pa ) , tol )
  assert_equal_within(  0.25 , tet_vol_value( p3, p1, p4, pa ) , tol )

  assert_equal_within( -0.50 , tet_vol_value( p1, p3, p2, pb ) , tol )
  assert_equal_within( -0.50 , tet_vol_value( p3, p2, p1, pb ) , tol )
  assert_equal_within( -0.50 , tet_vol_value( p2, p1, p3, pb ) , tol )
  assert_equal_within( -0.25 , tet_vol_value( p1, p4, p3, pb ) , tol )
  assert_equal_within( -0.25 , tet_vol_value( p4, p3, p1, pb ) , tol )
  assert_equal_within( -0.25 , tet_vol_value( p3, p1, p4, pb ) , tol )

!  write(6,'(a,6(1x,es10.3))') 'tet-132=',tet_vol_value( pa, p3, p2, pb )
!  write(6,'(a,6(1x,es10.3))') 'tet-321=',tet_vol_value( pa, p2, p1, pb )
!  write(6,'(a,6(1x,es10.3))') 'tet-213=',tet_vol_value( pa, p1, p3, pb )

  if (                                                                         &
      (     ( tet_vol_value( pa, p3, p2, pb ) < 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p2, p1, pb ) < 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p1, p3, pb ) < 0.0d+0 ) )                     &
 .or. (     ( tet_vol_value( pa, p3, p2, pb ) > 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p2, p1, pb ) > 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p1, p3, pb ) > 0.0d+0 ) )                     &
     ) then
     intersect = .true.
  else
     intersect = .false.
  endif

  assert_true( intersect )

  pa(1) =  0.1_dp
  pa(2) =  0.5_dp
  pa(3) =  0.1_dp

  pb(1) =  0.1_dp
  pb(2) = -0.5_dp
  pb(3) =  0.1_dp

  call intersection ( p1, p3, p2, pa, pb, pint, s )
  write(6,'(a,6(1x,es10.3))') 'pint=',pint,s

  if (                                                                         &
      (     ( tet_vol_value( pa, p4, p3, pb ) < 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p3, p1, pb ) < 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p1, p4, pb ) < 0.0d+0 ) )                     &
 .or. (     ( tet_vol_value( pa, p4, p3, pb ) > 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p3, p1, pb ) > 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p1, p4, pb ) > 0.0d+0 ) )                     &
     ) then
     intersect = .true.
  else
     intersect = .false.
  endif

  assert_false( intersect )

!  write(6,'(6(1x,f10.3))') tet_vol_value( pa, p4, p3, pb )
!  write(6,'(6(1x,f10.3))') tet_vol_value( pa, p3, p1, pb )
!  write(6,'(6(1x,f10.3))') tet_vol_value( pa, p1, p4, pb )


end test

test inside_box_test

  real(dp), parameter :: tol = 0.0001_dp
  real(dp), parameter :: zero = 0.0_dp
  real(dp), dimension(3) :: p1, p2, p3, p4, p5, p6, p7, p8, pa, pb, pint
  real(dp), dimension(3) :: lower_corner, upper_corner, c0, c1, c2
  real(dp)               :: s
  integer                :: hex_face

  logical :: intersect

  lower_corner(1)=-1.0_dp
  lower_corner(2)=-1.0_dp
  lower_corner(3)=-1.0_dp
  upper_corner(1)= 1.0_dp
  upper_corner(2)= 1.0_dp
  upper_corner(3)= 1.0_dp

  p1(1) = lower_corner(1)
  p1(2) = lower_corner(2)
  p1(3) = lower_corner(3)

  p2(1) = lower_corner(1)
  p2(2) = lower_corner(2)
  p2(3) = upper_corner(3)

  p3(1) = upper_corner(1)
  p3(2) = lower_corner(2)
  p3(3) = lower_corner(3)

  p4(1) = upper_corner(1)
  p4(2) = lower_corner(2)
  p4(3) = upper_corner(3)

  p5(1) = lower_corner(1)
  p5(2) = upper_corner(2)
  p5(3) = lower_corner(3)

  p6(1) = lower_corner(1)
  p6(2) = upper_corner(2)
  p6(3) = upper_corner(3)

  p7(1) = upper_corner(1)
  p7(2) = upper_corner(2)
  p7(3) = lower_corner(3)

  p8(1) = upper_corner(1)
  p8(2) = upper_corner(2)
  p8(3) = upper_corner(3)


  pa(1) = lower_corner(1) + 0.500_dp * ( upper_corner(1) - lower_corner(1) )
  pa(2) = lower_corner(1) + 0.500_dp * ( upper_corner(2) - lower_corner(2) )
  pa(3) = lower_corner(1) + 0.500_dp * ( upper_corner(3) - lower_corner(3) )

  assert_equal_within(  4.00 , tet_vol_value( p1, p3, p2, pa ) , tol )
  assert_equal_within(  4.00 , tet_vol_value( p3, p2, p1, pa ) , tol )
  assert_equal_within(  4.00 , tet_vol_value( p2, p1, p3, pa ) , tol )
  assert_equal_within( -4.00 , tet_vol_value( p1, p4, p3, pa ) , tol )
  assert_equal_within( -4.00 , tet_vol_value( p4, p3, p1, pa ) , tol )
  assert_equal_within( -4.00 , tet_vol_value( p3, p1, p4, pa ) , tol )

! write(6,'(a,1x,f10.3))') '132', tet_vol_value( p1, p3, p2, pa )
! write(6,'(a,1x,f10.3))') '321', tet_vol_value( p3, p2, p1, pa )
! write(6,'(a,1x,f10.3))') '213', tet_vol_value( p2, p1, p3, pa )
! write(6,'(a,1x,f10.3))') '143', tet_vol_value( p1, p4, p3, pa )
! write(6,'(a,1x,f10.3))') '431', tet_vol_value( p4, p3, p1, pa )
! write(6,'(a,1x,f10.3))') '314', tet_vol_value( p3, p1, p4, pa )
! write(6,'(a,1x,f10.3))') '234', tet_vol_value( p2, p3, p4, pa )
! write(6,'(a,1x,f10.3))') '248', tet_vol_value( p2, p4, p8, pa )
! write(6,'(a,1x,f10.3))') '286', tet_vol_value( p2, p8, p6, pa )
! write(6,'(a,1x,f10.3))') '251', tet_vol_value( p2, p5, p1, pa )
! write(6,'(a,1x,f10.3))') '265', tet_vol_value( p2, p6, p5, pa )

  pb(1) = lower_corner(1) + 0.400_dp * ( upper_corner(1) - lower_corner(1) )
  pb(2) = lower_corner(1) - 1.200_dp * ( upper_corner(2) - lower_corner(2) )
  pb(3) = lower_corner(1) + 0.400_dp * ( upper_corner(3) - lower_corner(3) )

! write(6,'(a,3(1x,f10.3)))') 'p1  ', p1
! write(6,'(a,3(1x,f10.3)))') 'p2  ', p2
! write(6,'(a,3(1x,f10.3)))') 'p3  ', p3
! write(6,'(a,3(1x,f10.3)))') 'pa  ', pa
! write(6,'(a,3(1x,f10.3)))') 'pb  ', pb

! write(6,'(a,1x,f10.3))') 'a32b', tet_vol_value( pa, p3, p2, pb )
! write(6,'(a,1x,f10.3))') 'a21b', tet_vol_value( pa, p2, p1, pb )
! write(6,'(a,1x,f10.3))') 'a13b', tet_vol_value( pa, p1, p3, pb )


  if (                                                                         &
      (     ( tet_vol_value( pa, p3, p2, pb ) < 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p2, p1, pb ) < 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p1, p3, pb ) < 0.0d+0 ) )                     &
 .or. (     ( tet_vol_value( pa, p3, p2, pb ) > 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p2, p1, pb ) > 0.0d+0 )                       &
      .and. ( tet_vol_value( pa, p1, p3, pb ) > 0.0d+0 ) )                     &
     ) then
     intersect = .true.
  else
     intersect = .false.
  endif

  assert_true( intersect )



  pa(1) =  0.1_dp
  pa(2) =  0.9_dp
  pa(3) =  1.8_dp

  pb(1) =  0.2_dp
  pb(2) =  0.8_dp
  pb(3) =  0.9_dp

 call hex_face_parameter( p1,p2,p3,p4,p5,p6,p7,p8,pa,pb,hex_face,c0,c1,c2 )

  call intersection ( c0, c1, c2, pa, pb, pint, s )
! write(6,'(a,i5,6(1x,f10.4))') '    012-pint=',hex_face,pint,s

  assert_equal( 4 , hex_face  )
  assert_equal_within( 1.0 , pint(3) , tol )





  pa(1) =  0.1_dp
  pa(2) =  1.8_dp
  pa(3) =  0.9_dp

  pb(1) =  0.2_dp
  pb(2) =  0.9_dp
  pb(3) =  0.8_dp

 call hex_face_parameter( p1,p2,p3,p4,p5,p6,p7,p8,pa,pb,hex_face,c0,c1,c2 )

  call intersection ( c0, c1, c2, pa, pb, pint, s )
! write(6,'(a,i5,6(1x,f10.4))') '    012-pint=',hex_face,pint,s

  assert_equal( 7 , hex_face  )
  assert_equal_within( 1.0 , pint(2) , tol )




  pa(1) =  1.8_dp
  pa(2) =  0.1_dp
  pa(3) =  0.9_dp

  pb(1) =  0.9_dp
  pb(2) =  0.2_dp
  pb(3) =  0.8_dp

 call hex_face_parameter( p1,p2,p3,p4,p5,p6,p7,p8,pa,pb,hex_face,c0,c1,c2 )

  call intersection ( c0, c1, c2, pa, pb, pint, s )
! write(6,'(a,i5,6(1x,f10.4))') '    012-pint=',hex_face,pint,s

  assert_equal( 11 , hex_face  )
  assert_equal_within( 1.0 , pint(1) , tol )

end test

end test_suite

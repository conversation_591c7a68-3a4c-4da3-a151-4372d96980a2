
! tetrahedral cut cell simulation data definitions

module cut_types

  use kinddefs,        only : dp
  use force_types,     only : force_type

  implicit none

  private

  public :: cut_cell_activated
  public :: cut_cell_visualize

  public :: topo_int, topo_cut, topo_ext

  public :: cut_type, cut_edge_type, cut_face_type, cut_sens_type
  public :: cut_bc_map_type

  public :: cut
  public :: cut_force

  public :: required_lstgs_degree

  public :: cut_lap_limit, cut_lap_coef

  public :: cut_bc_extrapolation_order

  logical :: cut_cell_activated     = .false.
  logical :: cut_cell_visualize     = .false.

  integer, parameter :: topo_int = 2
  integer, parameter :: topo_cut = 1
  integer, parameter :: topo_ext = 0

  type cut_edge_type
    integer :: left_node, right_node
    integer :: parent_edge
    integer :: number_of_triangles
    real(dp), dimension(:,:), pointer    :: triangle_node1
    real(dp), dimension(:,:), pointer    :: triangle_node2
    real(dp), dimension(:,:), pointer    :: triangle_node3
    real(dp), dimension(:,:), pointer    :: triangle_normal
    real(dp), dimension(:),   pointer    :: triangle_area
    integer,  dimension(:,:,:), pointer  :: parent_int
    real(dp), dimension(:,:,:), pointer  :: parent_xyz
  end type cut_edge_type

  type cut_face_type
    integer :: node
    integer :: ibc
    integer :: number_of_triangles
    real(dp), dimension(:,:), pointer    :: triangle_node1
    real(dp), dimension(:,:), pointer    :: triangle_node2
    real(dp), dimension(:,:), pointer    :: triangle_node3
    real(dp), dimension(:,:), pointer    :: triangle_normal
    real(dp), dimension(:),   pointer    :: triangle_area
    integer,  dimension(:),   pointer    :: triangle_ibc
    integer,  dimension(:,:,:), pointer  :: parent_int
    real(dp), dimension(:,:,:), pointer  :: parent_xyz
  end type cut_face_type

  type cut_sens_type
    integer :: node
    integer :: number_of_triangles
    integer,  dimension(:,:),     pointer    :: constraint_type
    real(dp), dimension(:,:,:,:), pointer    :: constraint_xyz
  end type cut_sens_type

  type cut_type
    integer :: uncut_edges
    integer,  dimension(:), pointer :: uncut_edge_index
    integer :: cut_edges
    type(cut_edge_type), dimension(:), pointer :: cut_edge
    integer :: cut_bcs
    type(cut_face_type), dimension(:), pointer :: cut_bc
    integer :: cut_surfs
    type(cut_face_type), dimension(:), pointer :: cut_surf
    integer :: cut_senss
    type(cut_sens_type), dimension(:), pointer :: cut_sens
    integer :: orig_nnodes0, orig_nnodes01
    integer, dimension(:), pointer :: u2c ! uncut to first cut region map
    integer, dimension(:), pointer :: topo
    integer, dimension(:), pointer :: parent
    integer, dimension(:), pointer :: region

    integer :: surf_nnode
    integer :: surf_ntri
    real(dp), dimension(:,:), pointer :: surf_xyz
    integer,  dimension(:,:), pointer :: surf_t2n
    integer,  dimension(:),   pointer :: surf_global

! node-to-node gradient reconstruction stencil
    integer, dimension(:), pointer :: firstn2n
    integer, dimension(:), pointer :: n2n

    real(dp), dimension(:), pointer    :: vol
  end type cut_type

  type cut_bc_map_type
    integer :: default
    integer :: nface
    integer, dimension(:,:), pointer :: face2condition
  end type cut_bc_map_type

  type(cut_type)   :: cut
  type(force_type) :: cut_force

  integer :: required_lstgs_degree = 6

  real(dp)    :: cut_lap_limit = -1.0_dp ! 0.02_dp
  real(dp)    :: cut_lap_coef  =  0.0_dp ! 1000.0_dp

  ! for supersonic robustness
  real(dp)    :: cut_bc_extrapolation_order  =  1.0_dp

end module cut_types

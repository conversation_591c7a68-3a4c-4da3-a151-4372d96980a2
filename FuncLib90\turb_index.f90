!=============================  TURBULENCE_INDEX =============================80
!
!  Computes Spalart's turbulence index (uses amut to solve for equivalent
!  nuwiggle, so it works for all eddy-viscosity models).
!  HOWEVER, SA eddy viscosity behaves as y^4 near walls, so the formula
!  is strictly only correct for SA.  Other models may behave differently.
!  Therefore, when used for other models, the
!  turbulence index is at best only an approximate (crude) indicator.
!  Here, amut must be the turb viscosity OFF the wall, at location slen
!  (where slen represents the min distance OFF the wall)
!
!=============================================================================80

  pure function turb_index( n_grd, n_tot, eqn_set, qnode, amut,                &
                                   gradx, grady, gradz, slen )

    use kinddefs,       only : dp
    use info_depr,      only : re, xmach, tref
    use fluid,          only : gamma, sutherland_constant
    use solution_types, only : compressible

    real(dp)                               :: turb_index
    integer,                    intent(in) :: n_grd, n_tot, eqn_set
    real(dp), dimension(n_tot), intent(in) :: qnode
    real(dp), dimension(n_grd), intent(in) :: gradx, grady, gradz
    real(dp),                   intent(in) :: slen
    real(dp),                   intent(in) :: amut

    real(dp) :: re_xm, rho, p
    real(dp) :: mu_lam, xnuwiggle, vortmag, delta, temp, cstar
    integer  :: n

  continue

    if (amut <= -1.0_dp) then
     turb_index = 0.0_dp
     return
    end if

    if ( eqn_set == compressible ) then
      re_xm     = re/xmach
      rho       = qnode(1)
      p         = qnode(5)
    else
      re_xm     = re
      rho       = 1._dp
      p         = 1._dp
    end if

    vortmag = sqrt( ( grady(4) - gradz(3) )**2 + ( gradz(2) - gradx(4) )**2 +  &
                    ( gradx(3) - grady(2) )**2 )
    cstar  = sutherland_constant / tref
    temp   = gamma * p / rho
    mu_lam = viscosity_law(cstar,temp)

!   iterate to get nuwiggle from amut
    xnuwiggle = 500.0_dp
    do n=1,50
      delta = ( xnuwiggle**4 - amut*xnuwiggle**3 - amut*(7.1_dp**3))/          &
              ( 4._dp*xnuwiggle**3 - 3._dp*amut*xnuwiggle**2 )
      xnuwiggle = xnuwiggle - delta
!     iteration may not be working
      xnuwiggle = max( xnuwiggle, 1.e-20_dp )
      xnuwiggle = min( xnuwiggle, 5000._dp )
    enddo

    turb_index = xnuwiggle/slen*sqrt(rho/re_xm)/(0.41_dp*sqrt(mu_lam*vortmag))
    turb_index = min( turb_index, 1.0_dp )

  end function turb_index

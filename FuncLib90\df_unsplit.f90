!============================ DF_UNSPLIT =====================================80
!
! Unsplit flux jacobians...ubar_null enforces tangency explicity.
! Note: ql are conservative variables
!
!=============================================================================80

  pure function df_unsplit(xnorm, ynorm, znorm, area, face_speed, ql, ubar_null)

    use kinddefs,  only : dp
    use fluid,     only : gm1

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(5), intent(in) :: ql

    logical,                intent(in) :: ubar_null

    real(dp), dimension(5,5)         :: df_unsplit

    real(dp) :: flux1rl,flux1ul,flux1vl,flux1wl
    real(dp) :: flux2rl,flux2ul,flux2vl,flux2wl,flux2pl
    real(dp) :: flux3rl,flux3ul,flux3vl,flux3wl,flux3pl
    real(dp) :: flux4rl,flux4ul,flux4vl,flux4wl,flux4pl
    real(dp) :: flux5rl,flux5ul,flux5vl,flux5wl,flux5pl

    real(dp) :: ubarl
    real(dp) :: ubarlrl,ubarlul,ubarlvl,ubarlwl

    real(dp) :: enrgyl
    real(dp) :: enrgylpl

    real(dp) :: pressl
    real(dp) :: presslrl,presslul,presslvl,presslwl,presslpl

    real(dp) :: q2l
    real(dp) :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp) :: ul
    real(dp) :: ulrl,ulul

    real(dp) :: vl
    real(dp) :: vlrl,vlvl

    real(dp) :: wl
    real(dp) :: wlrl,wlwl

    real(dp) :: rhol
    real(dp) :: rholrl

  continue

! Primitive variables on "left" side of face

      rhol = ql(1)
        rholrl = 1.0_dp
      ul = ql(2) / rhol
        ulrl = -ul/rhol
        ulul = 1.0_dp / rhol
      vl = ql(3) / rhol
        vlrl = -vl/rhol
        vlvl = 1.0_dp / rhol
      wl = ql(4) / rhol
        wlrl = -wl/rhol
        wlwl = 1.0_dp / rhol

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = ql(5)
        enrgylpl = 1.0_dp

      pressl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
        presslrl = -0.5_dp*gm1*(rhol*q2lrl + q2l*rholrl)
        presslul = -0.5_dp*gm1*rhol*q2lul
        presslvl = -0.5_dp*gm1*rhol*q2lvl
        presslwl = -0.5_dp*gm1*rhol*q2lwl
        presslpl = gm1

      if ( .not.ubar_null ) then
        ubarl   = xnorm*ul + ynorm*vl + znorm*wl - face_speed
        ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
        ubarlul = xnorm*ulul
        ubarlvl = ynorm*vlvl
        ubarlwl = znorm*wlwl
      else
        ubarl   = - face_speed
        ubarlrl = 0._dp
        ubarlul = 0._dp
        ubarlvl = 0._dp
        ubarlwl = 0._dp
      endif

!           fluxl1 = area*rhol*ubarl

        flux1rl = rhol*ubarlrl + ubarl*rholrl
        flux1ul = rhol*ubarlul
        flux1vl = rhol*ubarlvl
        flux1wl = rhol*ubarlwl

!           fluxl2 = area*(rhol*ul*ubarl + xnorm*pressl)

        flux2rl = rhol*(ul*ubarlrl+ubarl*ulrl) +                          &
                  ul*ubarl*rholrl + xnorm*presslrl
        flux2ul = rhol*(ul*ubarlul+ubarl*ulul) + xnorm*presslul
        flux2vl = rhol*(ul*ubarlvl) + xnorm*presslvl
        flux2wl = rhol*(ul*ubarlwl) + xnorm*presslwl
        flux2pl = xnorm*presslpl

!           fluxl3 = area*(rhol*vl*ubarl + ynorm*pressl)

        flux3rl = rhol*(vl*ubarlrl+ubarl*vlrl) +                          &
                  vl*ubarl*rholrl + ynorm*presslrl
        flux3ul = rhol*(vl*ubarlul) + ynorm*presslul
        flux3vl = rhol*(vl*ubarlvl+ubarl*vlvl) + ynorm*presslvl
        flux3wl = rhol*(vl*ubarlwl) + ynorm*presslwl
        flux3pl = ynorm*presslpl

!           fluxl4 = area*(rhol*wl*ubarl + znorm*pressl)

        flux4rl = rhol*(wl*ubarlrl+ubarl*wlrl) +                          &
                  wl*ubarl*rholrl + znorm*presslrl
        flux4ul = rhol*(wl*ubarlul) + znorm*presslul
        flux4vl = rhol*(wl*ubarlvl) + znorm*presslvl
        flux4wl = rhol*(wl*ubarlwl+ubarl*wlwl) + znorm*presslwl
        flux4pl = znorm*presslpl

!           fluxl5 = area*(enrgyl + pressl)*ubarl + area*face_speed*pressl

        flux5rl = (enrgyl + pressl)*ubarlrl +                             &
                  ubarl*(presslrl) + face_speed*presslrl
        flux5ul = (enrgyl + pressl)*ubarlul +                             &
                  ubarl*(presslul) + face_speed*presslul
        flux5vl = (enrgyl + pressl)*ubarlvl +                             &
                  ubarl*(presslvl) + face_speed*presslvl
        flux5wl = (enrgyl + pressl)*ubarlwl +                             &
                  ubarl*(presslwl) + face_speed*presslwl
        flux5pl = ubarl*(enrgylpl+presslpl) + face_speed*presslpl


      df_unsplit(1,1) = area*(flux1rl)
      df_unsplit(1,2) = area*(flux1ul)
      df_unsplit(1,3) = area*(flux1vl)
      df_unsplit(1,4) = area*(flux1wl)
      df_unsplit(1,5) = 0._dp

      df_unsplit(2,1) = area*(flux2rl)
      df_unsplit(2,2) = area*(flux2ul)
      df_unsplit(2,3) = area*(flux2vl)
      df_unsplit(2,4) = area*(flux2wl)
      df_unsplit(2,5) = area*(flux2pl)

      df_unsplit(3,1) = area*(flux3rl)
      df_unsplit(3,2) = area*(flux3ul)
      df_unsplit(3,3) = area*(flux3vl)
      df_unsplit(3,4) = area*(flux3wl)
      df_unsplit(3,5) = area*(flux3pl)

      df_unsplit(4,1) = area*(flux4rl)
      df_unsplit(4,2) = area*(flux4ul)
      df_unsplit(4,3) = area*(flux4vl)
      df_unsplit(4,4) = area*(flux4wl)
      df_unsplit(4,5) = area*(flux4pl)

      df_unsplit(5,1) = area*(flux5rl)
      df_unsplit(5,2) = area*(flux5ul)
      df_unsplit(5,3) = area*(flux5vl)
      df_unsplit(5,4) = area*(flux5wl)
      df_unsplit(5,5) = area*(flux5pl)

  end function df_unsplit

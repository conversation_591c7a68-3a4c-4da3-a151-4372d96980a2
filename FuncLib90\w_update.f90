!================================= W_UPDATE ==================================80
!
! Find minimum abs(relaxation factor) between incoming value and
! current update preserving sign of relaxation factor.
!
!=============================================================================80
  pure function w_update( limit_x, x, dx, limit_dx_threshold, w_in )

    use kinddefs,        only : dp

    real(dp), intent(in) :: limit_x, x, dx, limit_dx_threshold, w_in
    real(dp)             :: w_update

    real(dp) :: w_temp

  continue

    w_update = w_in                             !incoming signed w

    if ( abs(dx) < limit_dx_threshold ) return  !dx <threshold

    w_temp = ( limit_x - x ) / dx               !w = actual/requested

    if ( dx > limit_dx_threshold ) then         ! increasing dx

      if ( w_temp  < abs( w_in ) ) then
        w_update = w_temp
      endif

    else                                        ! decreasing dx

      if ( w_temp  < abs( w_in ) ) then
        w_update = -w_temp
      endif

    endif

  end function w_update

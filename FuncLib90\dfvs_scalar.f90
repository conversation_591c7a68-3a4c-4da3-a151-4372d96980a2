!================================= DFVS_SCALAR ===============================80
!
! Flux on the face via scalar flux vector splitting scheme.
!
!=============================================================================80

  pure function dfvs_scalar( eigl, eigr, eiga_eps )

    use kinddefs,        only : dp

    real(dp), intent(in)     :: eigl, eigr, eiga_eps
    real(dp), dimension(2)   :: dfvs_scalar

    real(dp) :: eigal, eigar, eiglp, eigrm

  continue

    eigal = aharten( eigl, eiga_eps )
    eigar = aharten( eigr, eiga_eps )

    eiglp = ( eigl + eigal )*0.5_dp

    eigrm = ( eigr - eigar )*0.5_dp

    dfvs_scalar(1) = eiglp
    dfvs_scalar(2) = eigrm

  end function dfvs_scalar

!============================= GRAD_NODE_AVG ====== ==========================80
!
! This routine computes gradients using node averaged data.
!
!=============================================================================80
  pure function grad_node_avg( n_tot, n_q, n_s, n_e,                           &
                               cell1, cell2, nodes_to_avg,                     &
                               ncell01, xc, yc, zc, qcell,                     &
                               nnodes01, x, y, z, qtavg )

    use twod_util,      only : q_2d

    integer, intent(in) :: n_q, n_tot, n_s, n_e, cell1, cell2
    integer, intent(in) :: ncell01, nnodes01

    integer,  dimension(5), intent(in) :: nodes_to_avg

    real(dp), dimension(ncell01),       intent(in) :: xc, yc, zc
    real(dp), dimension(n_tot,ncell01), intent(in) :: qcell
    real(dp), dimension(nnodes01),      intent(in) :: x, y, z
    real(dp), dimension(n_q,nnodes01),  intent(in) :: qtavg

    integer :: n_face_nodes, node1, node2, node3, node4, n

    real(dp) :: dx, dy, dz

    real(dp), dimension(n_e-n_s+1) :: dqc, dq1, dq2
    real(dp), dimension(3,3) :: t, ti

    real(dp), dimension(3,n_e-n_s+1) :: grad_node_avg

  continue

    n = n_e - n_s + 1

!   Node-center connection.

    dx = xc(cell2) - xc(cell1)
    dy = yc(cell2) - yc(cell1)
    dz = zc(cell2) - zc(cell1)

    dqc(1:n) = qcell(n_s:n_e,cell2) - qcell(n_s:n_e,cell1)

    n_face_nodes = nodes_to_avg(5)
    node1 = nodes_to_avg(1)
    node2 = nodes_to_avg(2)
    node3 = nodes_to_avg(3)
    node4 = nodes_to_avg(4)

    if ( .not.q_2d .and. (n_face_nodes == 3) ) then

      t(1,1) = dx
      t(1,2) = dy
      t(1,3) = dz

      t(2,1) = x(node2) - x(node1)
      t(2,2) = y(node2) - y(node1)
      t(2,3) = z(node2) - z(node1)

      t(3,1) = x(node3) - x(node1)
      t(3,2) = y(node3) - y(node1)
      t(3,3) = z(node3) - z(node1)

      dq1(1:n) = qtavg(n_s:n_e,node2) - qtavg(n_s:n_e,node1)
      dq2(1:n) = qtavg(n_s:n_e,node3) - qtavg(n_s:n_e,node1)

    elseif ( .not.q_2d ) then

      !...assume a cyclic ordering of the points.

      t(1,1) = dx
      t(1,2) = dy
      t(1,3) = dz

      t(2,1) = 0.5_dp*( x(node1) + x(node2) - x(node3) - x(node4) )
      t(2,2) = 0.5_dp*( y(node1) + y(node2) - y(node3) - y(node4) )
      t(2,3) = 0.5_dp*( z(node1) + z(node2) - z(node3) - z(node4) )

      t(3,1) = 0.5_dp*( x(node2) + x(node3) - x(node1) - x(node4) )
      t(3,2) = 0.5_dp*( y(node2) + y(node3) - y(node1) - y(node4) )
      t(3,3) = 0.5_dp*( z(node2) + z(node3) - z(node1) - z(node4) )

      dq1(1:n) = 0.5_dp*( qtavg(n_s:n_e,node1) + qtavg(n_s:n_e,node2) &
                        - qtavg(n_s:n_e,node3) - qtavg(n_s:n_e,node4) )

      dq2(1:n) = 0.5_dp*( qtavg(n_s:n_e,node2) + qtavg(n_s:n_e,node3) &
                        - qtavg(n_s:n_e,node1) - qtavg(n_s:n_e,node4) )

    elseif ( q_2d ) then

      t(1,1) = dx
      t(1,2) = 0._dp
      t(1,3) = dz

      t(2,1) = x(node2) - x(node1)
      t(2,2) = 0._dp
      t(2,3) = z(node2) - z(node1)

      t(3,1) = 0._dp
      t(3,2) = 1._dp
      t(3,3) = 0._dp

      dq1(1:n) = qtavg(n_s:n_e,node2) - qtavg(n_s:n_e,node1)
      dq2(1:n) = 0._dp

    endif

    ti = tinverse( t )

    grad_node_avg(1,:) = ti(1,1)*dqc(:) + ti(1,2)*dq1(:) + ti(1,3)*dq2(:)
    grad_node_avg(2,:) = ti(2,1)*dqc(:) + ti(2,2)*dq1(:) + ti(2,3)*dq2(:)
    grad_node_avg(3,:) = ti(3,1)*dqc(:) + ti(3,2)*dq1(:) + ti(3,3)*dq2(:)

  end function grad_node_avg

module relax_types

  use kinddefs, only : dp

  implicit none

  private

  public :: schedule_type, relax_type
  public :: write_schedule
  public :: relax_name_len, relax_name_index
  public :: set_implicit_line_sets

  integer, parameter :: relax_name_len = 22
  integer, parameter :: direction_name_len = 10

  type schedule_type
    integer :: n_meanflow_iters    ! No of meanflow iters to perform on schedule
    integer :: n_turb_iters        ! No of turb iters to perform on schedule
    integer :: n_steps             ! No of steps in schedule

    integer,  dimension(:), pointer :: type        ! Type of each step
    integer,  dimension(:), pointer :: sweeps      ! # sweeps in each step
    integer,  dimension(:), pointer :: turb_sweeps ! # turb sweeps in each step
    integer,  dimension(:), pointer :: direction   ! direction of sweep
    real(dp), dimension(:), pointer :: relax_factor_meanflow
    real(dp), dimension(:), pointer :: relax_factor_turbulence
    real(dp), dimension(:), pointer :: relax_factor_meanflow_cg
    real(dp), dimension(:), pointer :: relax_factor_turbulence_cg
  end type schedule_type

  type relax_type
    type(schedule_type), dimension(:), pointer :: schedule ! Relaxation schedule
  end type relax_type

  integer, parameter :: n_relaxations = 13

  character(len=relax_name_len), dimension(n_relaxations) :: relax_names
                  !'1234567890123456789012','1234567890123456789012'
  data relax_names/'line-multicolor',       'point-multicolor',      &
                   'point-multicolor-swath','point-downstream',      &
                   'gmres',                 'ilu(0)',                &
                   'gmres-newtk',           'ilu(k)',                &
                   'line-downstream',                                &
                   'line2-multicolor',      'line2-downstream',      &
                   'line-jacobi',           'line2-jacobi'/


contains

!==================================== RELAX_NAME_INDEX =======================80
!
! Two modes, based on integer_to_character logical:
! =T, Generates a character name for the various relaxation types.
! =F, Generates an integer for the various relaxation names.
!
!=============================================================================80

  subroutine relax_name_index( relax_i, relax_name, ierr, integer_to_character )

    integer,                   intent(inout) :: relax_i
    character(relax_name_len), intent(inout) :: relax_name
    logical,                   intent(in)    :: integer_to_character
    integer,                   intent(inout) :: ierr

    integer :: i

  continue

    conversion : if ( integer_to_character ) then

      select case (relax_i)

        case (1:n_relaxations)

          relax_name = trim( relax_names(relax_i) )

        case default

          relax_name = 'empty-group' ; ierr = 1

          write(*,*) 'Relaxation index unknown to function relax_name.'
          write(*,*) '...relax_i=',relax_i

      end select

    else conversion

      relax_i = 0
      do i=1,n_relaxations
        if ( trim(relax_name) /= trim( relax_names(i) ) ) cycle
        relax_i = i
        exit
      enddo

      if ( relax_i == 0 ) then

          ierr = 1

          write(*,*)'Relaxation name unknown to function relax_name_index.'
          write(*,*)'...relax_name=', trim(relax_name)
          write(*,"(1x,i2,a)") n_relaxations,' known names are below:'
          do i=1,n_relaxations
            write(*,"(1x,20x,a)") trim(relax_names(i))
          enddo

      endif

    endif conversion

  end subroutine relax_name_index

!==================================== DIRECTION_NAME_INDEX ===================80
!
! Two modes, based on integer_to_character logical:
! =T, Generates a character name for the various relaxation directions.
! =F, Generates an integer for the various relaxation directions.
!
!=============================================================================80

  subroutine direction_name_index( dir_i, dir_name, ierr, integer_to_character )

    integer,                       intent(inout) :: dir_i
    character(direction_name_len), intent(inout) :: dir_name
    logical,                       intent(in)    :: integer_to_character
    integer,                       intent(inout) :: ierr

  continue

    conversion : if ( integer_to_character ) then

      select case (dir_i)

        case (1)    !1234567890
          dir_name = 'forward'
        case (2)
          dir_name = 'backward'

        case default

          dir_name = 'invalid' ; ierr = 1

          write(*,*) 'Index unknown to function direction_name_index.'
          write(*,*) '...dir_i=',dir_i

      end select

    else conversion

      select case (trim(dir_name))

        case ('forward')
          dir_i = 1
        case ('backward')
          dir_i = 2

        case default

          dir_i = 0000 ; ierr = 1

          write(*,*)'Name unknown to function direction_name_index.'
          write(*,*)'...dir_name=', trim(dir_name)

      end select

    endif conversion

  end subroutine direction_name_index

!=========================== WRITE_SCHEDULE ==================================80
!
! Write schedule derived type.
!
!=============================================================================80

  subroutine write_schedule( schedule, ix, s_offset, ierr )

    integer,                           intent(in)  :: ix, s_offset
    integer,                           intent(out) :: ierr
    type(schedule_type), dimension(:), intent(in)  :: schedule

    integer  :: i, j, i1, i2, i3, i4
    real(dp) :: r1, r2, r3, r4

    character(len=relax_name_len)     :: name1
    character(len=direction_name_len) :: name2

  continue

    ierr = 0

    write(ix,*) ' Summary of schedule derived type information:'
    do i = 1+s_offset, 3+s_offset

      write(ix,*)
      i1 = schedule(i)%n_meanflow_iters
      i2 = schedule(i)%n_turb_iters
      if ( i1 == 0 .and. i2 == 0 ) then
        if ( i-s_offset == 1) then
          write(ix,*) '    Pre-Global relaxation schedule not invoked'
        elseif ( i-s_offset == 2 ) then
          write(ix,*) '        Global relaxation schedule not invoked'
        else
          write(ix,*) '   Post-Global relaxation schedule not invoked'
        endif
        write(ix,*)
        cycle
      else
        if ( i-s_offset == 1) then
          write(ix,*) '    Pre-Global relaxation schedule=',i
        elseif ( i-s_offset == 2 ) then
          write(ix,*) '        Global relaxation schedule=',i
        else
          write(ix,*) '   Post-Global relaxation schedule=',i
        endif
        write(ix,*)
        write(ix,*)   '                  ...Meanflow  repetitions=',i1
        write(ix,*)   '                  ...Turbulent repetitions=',i2
      endif

      i1 = schedule(i)%n_steps
      write(ix,*)
      write(ix,*)   '           ...Steps in relaxation schedule=',i1

      write(ix,*)
      write(ix,"(1x,52x,a,a)")              &
      ' <-----------Meanflow------------> ',&
      ' <-----------Turbulence------------>'
      write(ix,"(1x,33x,20x,11x,1x,2a,13x,1x,2a)") &
      '          ',' CoarseGrid','          ','   CoarseGrid'
      write(ix,"(1x,33x,20x,11x,1x,2a,13x,1x,2a)") &
      'Relaxation',' Relaxation','Relaxation','   Relaxation'
      write(ix,"(1x,11x,a,18x,6x,a,1x,a,5x,a,2(5x,a,5x,a,5x,a,2x))") &
      'Step','Type','Direction',                                     &
      'Sweeps','Factor','Factor','Sweeps','Factor','  Factor'
      do j = 1, schedule(i)%n_steps
        i3 = schedule(i)%type(j)
        i4 = schedule(i)%direction(j)
        i1 = schedule(i)%sweeps(j)
        i2 = schedule(i)%turb_sweeps(j)
        r1 = schedule(i)%relax_factor_meanflow(j)
        r2 = schedule(i)%relax_factor_turbulence(j)
        r3 = schedule(i)%relax_factor_meanflow_cg(j)
        r4 = schedule(i)%relax_factor_turbulence_cg(j)
        call relax_name_index( i3, name1, ierr, .true. )
        call direction_name_index( i4, name2, ierr, .true. )
        write(ix,"(1x,11x,a,i10,1x,a,i10,2f11.3,i11,2f13.3)") &
        name1,i3,name2,                                       &
        i1,real(r1,dp),real(r3,dp),i2,real(r2,dp),real(r4,dp)
      end do

    end do
    write(ix,*)

  end subroutine write_schedule

!=============================== SET_IMPLICIT_LINE_SETS ======================80
!
! Set implicit line sets.
!
!=============================================================================80

  subroutine set_implicit_line_sets( steps, type )

    use debug_defs, only : implicit_line_sets
    use lmpi,       only : lmpi_master

    integer,               intent(in) :: steps
    integer, dimension(:), intent(in) :: type

    integer :: i

  continue

    do i = 1, steps
      select case (type(i))
      case (1,9,12)
        implicit_line_sets(1) = .true.
      case (10,11,13)
        implicit_line_sets(2) = .true.
      case default
      end select
    end do

    if ( lmpi_master ) then
      do i = 1, size(implicit_line_sets,1)
        if ( .not.implicit_line_sets(i) ) cycle
        write(*,"(1x,a,i2,a,L1)") ' Implicit line set(i=',i,')=',&
        implicit_line_sets(i)
      enddo
    endif

  end subroutine set_implicit_line_sets

end module relax_types

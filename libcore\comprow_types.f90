module comprow_types

  use interp_defs, only : sendrecv_type

  implicit none

  private

  public :: crow_type, crow_flow
  public :: deallocate_crow_type, deallocate_crow_flow

  type crow_type
    integer :: nnz0  ! Number of nonzeros
    integer :: nnz01 ! Number of nonzeros

! Stuff for ILU(0)
    integer, dimension(:),   pointer :: ia    => null()
    integer, dimension(:),   pointer :: iau   => null()
    integer, dimension(:),   pointer :: ja    => null()
    integer, dimension(:,:), pointer :: fhelp => null()

! Pointer to first location of non-simply-connected adjacency
    integer, dimension(:),   pointer :: ia_ns => null()

  end type crow_type

  type crow_flow
    integer :: nnz0   ! Number of nonzeros
    integer :: nnz01  ! Number of nonzeros

! Stuff for ILU(0)
    integer, dimension(:),   pointer :: ia    => null()
    integer, dimension(:),   pointer :: ja    => null()
    integer, dimension(:,:), pointer :: fhelp => null()

! Pointer to first location of non-simply-connected adjacency
    integer, dimension(:),   pointer :: ia_ns => null()

! Arrays for dual-numbering scheme

    integer, dimension(:),   pointer :: m2g   => null() ! matrix-to-grid mapping
    integer, dimension(:),   pointer :: g2m   => null() ! grid-to_matrix mapping
    integer, dimension(:),   pointer :: nzg2m => null() ! onzero g2m mapping
    integer, dimension(:),   pointer :: iam   => null() ! Stuff for ILU(0)
    integer, dimension(:),   pointer :: jam   => null() ! Stuff for ILU(0)

! Send/recv info for xfer in matrix numbering
    type(sendrecv_type) :: sr_m

    integer, dimension(:),   pointer :: fia => null()   ! Nsolve
    integer, dimension(:),   pointer :: fja => null()   ! Nsolve

  end type crow_flow

contains

  subroutine deallocate_crow_type(crow)
    type(crow_type), intent(inout) :: crow
    continue
    if (associated(crow%ia)) deallocate(crow%ia)
    crow%ia => null()
    if (associated(crow%iau)) deallocate(crow%iau)
    crow%iau => null()
    if (associated(crow%ja)) deallocate(crow%ja)
    crow%ja => null()
    if (associated(crow%fhelp)) deallocate(crow%fhelp)
    crow%fhelp => null()
    if (associated(crow%ia_ns)) deallocate(crow%ia_ns)
    crow%ia_ns => null()
  end subroutine deallocate_crow_type

  subroutine deallocate_crow_flow(crow)
    use interp_defs, only : deallocate_sendrecv
    type(crow_flow), intent(inout) :: crow
    continue
    if (associated(crow%ia)) deallocate(crow%ia)
    crow%ia => null()
    if (associated(crow%ja)) deallocate(crow%ja)
    crow%ja => null()
    if (associated(crow%fhelp)) deallocate(crow%fhelp)
    crow%fhelp => null()
    if (associated(crow%ia_ns)) deallocate(crow%ia_ns)
    crow%ia_ns => null()
    if (associated(crow%m2g)) deallocate(crow%m2g)
    crow%m2g => null()
    if (associated(crow%g2m)) deallocate(crow%g2m)
    crow%g2m => null()
    if (associated(crow%nzg2m)) deallocate(crow%nzg2m)
    crow%nzg2m => null()
    if (associated(crow%iam)) deallocate(crow%iam)
    crow%iam => null()
    if (associated(crow%jam)) deallocate(crow%jam)
    crow%jam => null()

    call deallocate_sendrecv(crow%sr_m)

    if (associated(crow%fia)) deallocate(crow%fia)
    crow%fia => null()
    if (associated(crow%fja)) deallocate(crow%fja)
    crow%fja => null()
  end subroutine deallocate_crow_flow

end module comprow_types

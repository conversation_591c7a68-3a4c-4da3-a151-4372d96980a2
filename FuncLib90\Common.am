FuncLib90_SRCS = \
	adaptive_entropy_fix.f90 \
	aharten.f90 \
	bary_tet.f90 \
	characteristic_difference.f90 \
	coords_cylindrical_polar.f90 \
	cross_product.f90 \
	cross_product_x.f90 \
	crow_entry.f90 \
	daharten.f90 \
	des_velterm.f90 \
	det_3x3.f90 \
	det_4x4.f90 \
	df_conv.f90 \
	dfc_tangency.f90 \
	dfc_tangency_i.f90 \
	dfdubar.f90 \
	dfl_tangency.f90 \
	dflroe.f90 \
	dflroe_aj.f90 \
	dflroe_i.f90 \
	dfroe.f90 \
	dfroe_aj.f90 \
	dfroe_i.f90 \
	dfduc3_i.f90 \
	df_unsplit.f90 \
	df_unsplit_i.f90 \
	dfv_eb.f90 \
	dfv_eb_i.f90 \
	dfv_flsq.f90 \
	dfv_flsq_i.f90 \
	dfv_mom_dgrad.f90 \
	dfv_node_avg.f90 \
	dfv_node_avg_i.f90 \
	dfvf.f90 \
	dfvf_i.f90 \
	dfvs_scalar.f90 \
	dgrad_node_avg.f90 \
	dgradn_flsq.f90 \
	dkw_source.f90 \
	dmut_sa.f90 \
	dq.f90 \
	dqc_via_dnu.f90 \
	dqc_via_duvw.f90 \
	dqccm.f90 \
	dqt_flsq.f90 \
	dqtf_node_avg.f90 \
	dqumuscl.f90 \
	dsa1_psi.f90 \
	dsa1_turb_abs.f90 \
	dsa3_psi.f90 \
	dsa_source.f90 \
	dsa_source_s.f90 \
	dviscosity_law.f90 \
	ebv_tet_flux.f90 \
	ebv_tet_jac.f90 \
	edge_augment_weight.f90 \
	edge_ngrad.f90 \
	element_center.f90 \
	element_edge_da.f90 \
	element_big_angle.f90 \
	element_grad.f90 \
	element_dgrad.f90 \
	flsq_map_ref.f90 \
	flsq_wsq.f90 \
	flux_aufs.f90 \
	flux_conv.f90 \
	flux_ecroe.f90 \
	flux_hllcs.f90 \
	flux_hllcs_ddt.f90 \
	flux_ldfss.f90 \
	flux_ldfss_ddt.f90 \
	flux_proe.f90 \
	flux_roe.f90 \
	flux_roe_i.f90 \
	flux_roe_tangency.f90 \
	flux_roe_dc.f90 \
	flux_rrhll.f90 \
	flux_stvd.f90 \
	flux_turb_advection.f90 \
	flux_vanleer.f90 \
	flux_unsplit.f90 \
	flux_unsplit_i.f90 \
	flux_upwind_hvisc17.f90 \
	flux_visc.f90 \
	flux_visc_i.f90 \
	fv_flsq.f90 \
	fv_flsq_i.f90 \
	fv_node_avg.f90 \
	fv_node_avg_i.f90 \
	fvs_scalar.f90 \
	get_p.f90 \
	get_q.f90 \
	get_q_ddt.f90 \
	grad_face_lsq.f90 \
	grad_flsq.f90 \
	grad_in_tet.f90 \
	grad_node_avg.f90 \
	hrles_blend.f90 \
	in_conserved_variables.f90 \
	in_primitive_variables.f90 \
	iswch_coef.f90 \
	iswch_coef_ddt.f90 \
	keep_turb_data.f90 \
	kw_blend.f90 \
	kw_source.f90 \
	log_mean.f90 \
	lsq_coords.f90 \
	lsq_grad_stn.f90 \
	lsq_gradc.f90 \
	lsq_lc_max.f90 \
	lsq_map_ref.f90 \
	lsq_scoords.f90 \
	lsq_scoords_sx.f90 \
	lsq_scoords_sy.f90 \
	lsq_scoords_sz.f90 \
	lstgs_func.f90 \
	mapping_coords.f90 \
	mapping_system.f90 \
	minmods.f90 \
	minmodv.f90 \
	nodes_to_node_avg.f90 \
	omega_factor.f90 \
	pressure_limiter.f90 \
	pswitch.f90 \
	q_criterion.f90 \
	q_face_lsq.f90 \
	qf.f90 \
	qfcc.f90 \
	qp_from_qt.f90 \
	qr_tangency.f90 \
	qr_tangency_i.f90 \
	qt_from_qc.f90 \
	qt_from_qp.f90 \
	qtf_node_avg.f90 \
	qtgrad_node_avg.f90 \
	r_vec.f90 \
	reconstruct_weight.f90 \
	roe_avg.f90 \
	roe_efix.f90 \
	roe_efix_u.f90 \
	sa0_turb_abs.f90 \
	sa_source.f90 \
	setup_t.f90 \
	setup_t_inverse.f90 \
	shk_fit_shkspd.f90 \
	skin_fric.f90 \
	skip_plane.f90 \
	smthlms.f90 \
	smthlmv.f90 \
	tang_vecs.f90 \
	tau_v.f90 \
	tau_v_i.f90 \
	tinv_3d.f90 \
	tinverse.f90 \
	trv_from_t.f90 \
	turb_fluctuations.f90 \
	turb_index.f90 \
	turb_limit.f90 \
	vaflxls.f90 \
	vaflxlv.f90 \
	viscosity_law_ddt.f90 \
	viscosity_law_ddt5.f90 \
	vkflxls.f90 \
	vlflxls.f90 \
	vlflxlv.f90 \
	vnlrFJ.f90 \
	vnlrFJc.f90 \
	vol_tet.f90 \
	vswch_coef.f90 \
	vswch_coef_ddt.f90 \
	vswch_coef_orig.f90 \
	vswch_coef_orig_ddt.f90 \
	w_update.f90 \
	weights.f90 \
	y_plus.f90

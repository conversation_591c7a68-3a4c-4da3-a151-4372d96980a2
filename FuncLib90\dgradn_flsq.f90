!================================= DGRADN_FLSQ ===============================80
!
! Linearization of turbulent normal gradients using face-based-lsq.
!
!=============================================================================80

  function dgradn_flsq( lsq_mref, cell1, cell2,                                &
                         tx, ty, tz,                                           &
                         ncell01, xc, yc, zc,                                  &
                         flsq_lu, flsq_ja,                                     &
                         slen,                                                 &
                         face, flsq_n, flsq_ni, flsq_nb, bcc )

    use lsq_constants,  only : wflsq1, flsqn_max, tf
    use bc_types,       only : bcc_type
    use kinddefs,       only : system_i1
    use lsq_types,      only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mref

    type(bcc_type), intent(in) :: bcc

    integer, intent(in) :: cell1, cell2, ncell01
    integer, intent(in) :: face, flsq_n, flsq_ni, flsq_nb

    real(dp), intent(in) :: tx, ty, tz

    real(dp), dimension(:), intent(in) :: xc, yc, zc, slen

    real(dp), dimension(4,4),     intent(in) :: flsq_lu
    integer,  dimension(flsq_ni), intent(in) :: flsq_ja

    integer(system_i1), dimension(bcc%n_qt) :: dqt

    integer :: ii, jj, cella, fb, ib, ibc

    real(dp) :: xiel, etal, ziel
    real(dp) :: ex, ey, ez, deti
    real(dp) :: wsq, scalei, degradn
    real(dp) :: dlgrad, dmgrad, degrad
    real(dp) :: dc0, dc1, dc2, dc3
    real(dp), dimension(3) :: dfgrad
    real(dp), dimension(4) :: f, lc_max
    real(dp), dimension(3,3) :: tef, trf

    real(dp), dimension(4,flsqn_max) :: lc
    real(dp), dimension(flsqn_max) :: face_wsq

    real(dp), dimension(flsq_ni+2) :: dgradn_flsq

  continue

    !...Set coordinate transformation.
    trf = mapping_system( tx, ty, tz)

    scalei = flsq_lu(1,1)

    !...ex, ey, ez is unit vector along edge direction
    ex  = 0.5_dp*( xc(cell2) - xc(cell1) )*scalei
    ey  = 0.5_dp*( yc(cell2) - yc(cell1) )*scalei
    ez  = 0.5_dp*( zc(cell2) - zc(cell1) )*scalei

    deti =  1._dp / ( ex*tx + ey*ty + ez*tz )

    tef = tinv_3d( ex, ey, ez, trf(2,1), trf(2,2), trf(2,3),      &
                               trf(3,1), trf(3,2), trf(3,3), deti )

    face_wsq(1:flsq_n+2) = 1._dp
    if( abs(wflsq1-1._dp) > 1.0e-06_dp  .and. (flsq_nb == 0) ) then
      face_wsq(1:flsq_n+2) = flsq_wsq( flsq_n, cell1, cell2, ncell01, &
                                       xc, yc, zc, flsq_ja )
    endif

    jj  = 0
    do ii=1,flsq_ni
      cella = flsq_ja(ii)
      jj = jj + 1
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             xc(cella), yc(cella), zc(cella), slen(cella) )
    enddo
    jj = jj + 1
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell1), yc(cell1), zc(cell1), slen(cell1) )
    jj = jj + 1
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell2), yc(cell2), zc(cell2), slen(cell2) )
    do ii=bcc%flsq_ib(face),bcc%flsq_ib(face+1)-1
      fb = bcc%flsq_jb(ii)
      jj = jj + 1
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             bcc%xface(fb), bcc%yface(fb), bcc%zface(fb), &
                             bcc%slenface(fb) )
    enddo

    lc_max = lsq_lc_max( jj, lc, face_wsq )

    ii_loop2 : do ii=1,jj

      wsq  = face_wsq(ii)
      xiel = lc(1,ii)/lc_max(1)
      etal = lc(2,ii)/lc_max(2)
      ziel = lc(3,ii)/lc_max(3)

      dc0 =      wsq
      dc1 =  xiel*wsq
      dc2 =  etal*wsq
      dc3 =  ziel*wsq

! Forward...sequential access to flsq_lu.

      f(1) = dc0
      f(2) = dc1 - flsq_lu(2,1)*f(1)
      f(3) = dc2 - flsq_lu(3,1)*f(1)
      f(4) = dc3 - flsq_lu(4,1)*f(1)

      f(3) = f(3) - flsq_lu(3,2)*f(2)
      f(4) = f(4) - flsq_lu(4,2)*f(2)

      f(4) = f(4) - flsq_lu(4,3)*f(3)

! Backward...sequential access to flsq_lu.

      f(4) = f(4) * flsq_lu(4,4)
      f(2) = f(2) - flsq_lu(2,4)*f(4)
      f(3) = f(3) - flsq_lu(3,4)*f(4)

      f(3) = f(3) * flsq_lu(3,3)
      f(2) = f(2) - flsq_lu(2,3)*f(3)

      f(2) = f(2) * flsq_lu(2,2)

!     dc3 = f(4)
!     dc2 = f(3)
!     dc1 = f(2)
!     dc0 = f(1)

      !...Cartesian gradients at the face.
      dfgrad = lsq_gradc( lsq_mref, f(2:4), lc_max )

      !...gradients in face directions.
      dlgrad = dfgrad(1)*trf(2,1) + dfgrad(2)*trf(2,2) + dfgrad(3)*trf(2,3)
      dmgrad = dfgrad(1)*trf(3,1) + dfgrad(2)*trf(3,2) + dfgrad(3)*trf(3,3)

      !...resolve gradients from edge and face.
      f(1) = tef(1,2)*dlgrad + tef(1,3)*dmgrad
      f(2) = tef(2,2)*dlgrad + tef(2,3)*dmgrad
      f(3) = tef(3,2)*dlgrad + tef(3,3)*dmgrad

      if ( ii <= flsq_ni + 2 ) then
        dgradn_flsq(ii) = ( tx*f(1) + ty*f(2) + tz*f(3) )
      else
        ib    = bcc%flsq_ib(face) + ii - flsq_ni - 3
        fb    = bcc%flsq_jb(ib)
        cella = bcc%cell(fb)
        ibc   = bcc%ibc(fb)
        dqt   = dqt_flsq( ibc, bcc%n_qt )
        ib    = 0
        if ( cella == cell1 ) ib = flsq_ni+1
        if ( cella == cell2 ) ib = flsq_ni+2
        dgradn_flsq(ib) = dgradn_flsq(ib)                      &
        + ( tx*f(1) + ty*f(2) + tz*f(3) )*real( dqt(bcc%n_qt), dp )
      endif

    enddo ii_loop2

    !...directional gradients along edge.
    degrad = 0.5_dp*scalei

    !...resolve gradients from edge and face.
    f(1) = tef(1,1)*degrad
    f(2) = tef(2,1)*degrad
    f(3) = tef(3,1)*degrad

    degradn = ( tx*f(1) + ty*f(2) + tz*f(3) )

    dgradn_flsq(flsq_ni+1) = dgradn_flsq(flsq_ni+1) - degradn
    dgradn_flsq(flsq_ni+2) = dgradn_flsq(flsq_ni+2) + degradn

  end function dgradn_flsq

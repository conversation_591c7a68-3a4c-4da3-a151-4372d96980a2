#ifndef ENGINE_APP_H 
#define ENGINE_APP_H

#include "engine_data.h"

extern "C"
{
    void initialize_engine(void** cStruct_ptr, const char *cycle_name);

    //--- When recieving from fortran in this way we have to capture the input variables by reference.
    EngineOutput simulate_engine(void** cStruct_ptr, 
            double &pt_in, 
            double &TT_in,
            double &Time, 
            double &mdot_bypass,
            double &mdot_core,
            double &mdot_vce
            );

    void finalize_engine(void** cStruct_ptr);
}

#include "rtwtypes.h"
typedef struct {
  real_T Time;            /* '<Root>/Time' */
  real_T P_in;            /* '<Root>/P_in' */
  real_T T_in;            /* '<Root>/T_in' */
  real_T mdot_bypass;     /* '<Root>/mdot_bypass' */
  real_T mdot_core;       /* '<Root>/mdot_core' */
  real_T mdot_vce;        /* '<Root>/mdot_vce' */
} ExtU_lib_engine_T;

typedef struct {
  real_T P_vce;           /* '<Root>/P_vce' */
  real_T T_vce;           /* '<Root>/T_vce' */
  real_T P_bypass;        /* '<Root>/P_bypass' */
  real_T T_bypass;        /* '<Root>/T_bypass' */
  real_T P_core;          /* '<Root>/P_core' */
  real_T T_core;          /* '<Root>/T_core' */
  real_T VCEmdot;         /* '<Root>/VCE mdot' */
} ExtY_lib_engine_T;



#endif

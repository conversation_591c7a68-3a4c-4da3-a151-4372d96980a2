module utilities

  use kinddefs, only : dp

  implicit none

  private

  public :: check_for_stop, my_clock
  public :: tangents, tinverse, tangents_2d, tangents_3d
  public :: minmod
  public :: cell_gradients, cell_jacobians
  public :: cell_gradients_ddt
  public :: big_angle

contains

!================================== CHECK_FOR_STOP ===========================80
!
! Check for the existance of a file called stop.dat and set a stop flag
!
!=============================================================================80

  subroutine check_for_stop(istop)

    use lmpi,              only : lmpi_master, lmpi_bcast
    use system_extensions, only : se_open, se_flush
    use file_utils,        only : available_unit

    integer, intent(out) :: istop

    integer       :: ifileerr
    integer, save :: istop_last = 0
    integer       :: iunit

    continue

    if (lmpi_master) then
      iunit  = available_unit()
      istop = 0
      call se_open(iunit,file='stop.dat',status='old',iostat=ifileerr)
      if (ifileerr == 0) then
        read(iunit,*,iostat=ifileerr) istop
        close(iunit)
        if (istop /= istop_last) then
          write(*,*)
          write(*,*) 'stop.dat file found, user requested stop at cycle ', istop
          write(*,*)
          call se_flush()
          istop_last = istop
        end if
      end if
    end if

    call lmpi_bcast(istop)

  end subroutine check_for_stop

!=================================== TINVERSE ================================80
!
! Compute inverse of 3x3 transformation matrix (also available in FuncLib)
!
!=============================================================================80

  subroutine tinverse( t , tinv )

    real(dp), dimension(3,3), intent(in)  :: t
    real(dp), dimension(3,3), intent(out) :: tinv

    real(dp) :: deti

    continue

    !...find inverse elements of transformation matrix
    deti =  1.0_dp / ( t(1,1)*( t(2,2)*t(3,3) - t(3,2)*t(2,3) )             &
                     + t(2,1)*( t(3,2)*t(1,3) - t(1,2)*t(3,3) )             &
                     + t(3,1)*( t(1,2)*t(2,3) - t(2,2)*t(1,3) ) )

    tinv(1,1) =  deti*( t(2,2)*t(3,3) - t(3,2)*t(2,3) )
    tinv(1,2) =  deti*( t(3,2)*t(1,3) - t(1,2)*t(3,3) )
    tinv(1,3) =  deti*( t(1,2)*t(2,3) - t(2,2)*t(1,3) )

    tinv(2,1) = -deti*( t(2,1)*t(3,3) - t(3,1)*t(2,3) )
    tinv(2,2) = -deti*( t(3,1)*t(1,3) - t(1,1)*t(3,3) )
    tinv(2,3) = -deti*( t(1,1)*t(2,3) - t(2,1)*t(1,3) )

    tinv(3,1) =  deti*( t(2,1)*t(3,2) - t(3,1)*t(2,2) )
    tinv(3,2) =  deti*( t(3,1)*t(1,2) - t(1,1)*t(3,2) )
    tinv(3,3) =  deti*( t(1,1)*t(2,2) - t(2,1)*t(1,2) )

  end subroutine tinverse

!=================================== TANGENTS ================================80
!
! Compute unit surface tangent vectors given normal to surface
!
!=============================================================================80

  pure subroutine tangents(nx, ny, nz, lx, ly, lz, mx, my, mz, u, v, w)

    use fun3d_constants, only : my_1

    real(dp),           intent(in)  :: nx, ny, nz  ! unit normal components
    real(dp), optional, intent(in)  :: u, v, w     ! velocity components
    real(dp),           intent(out) :: lx, ly, lz  ! unit tangent components
    real(dp),           intent(out) :: mx, my, mz  ! unit tangent components

    real(dp) :: denom, denominv ! denominator to recover unit vector

    continue

    if(present(u) .and. present(v) .and. present(w))then
      lx = u*(1._dp - nx)
      ly = v*(1._dp - ny)
      lz = w*(1._dp - nz)
      denom = sqrt(lx**2 + ly**2 + lz**2)
      if(denom < 1.e-06_dp)go to 10
      lx = lx/denom
      ly = ly/denom
      lz = lz/denom
      mx = ny*lz-nz*ly
      my = nz*lx-nx*lz
      mz = nx*ly-ny*lx
      return
    end if

    10 continue

!   Because nx**2+ny**2+nz**2=1, at least one component
!   must be larger than sqrt(1/3).
    if(abs(nx)>0.577_dp)then
      ly = -nx
      lz =  nx
      lx = (ny-nz)
    else if(abs(ny)>0.577_dp)then
      lx =  ny
      lz = -ny
      ly = (nz-nx)
    else
      lx = -nz
      ly =  nz
      lz = (nx-ny)
    end if
    denom = sqrt(lx**2+ly**2+lz**2)
    denominv = my_1/denom
    lx = lx*denominv
    ly = ly*denominv
    lz = lz*denominv
    mx = ny*lz-nz*ly
    my = nz*lx-nx*lz
    mz = nx*ly-ny*lx

  end subroutine tangents

!=================================== TANGENTS_3D =============================80
!
! Compute unit surface tangent vectors given normal to surface.
!
!=============================================================================80

  pure subroutine tangents_3d(nx, ny, nz, lx, ly, lz, mx, my, mz)

    real(dp), intent(in)  :: nx, ny, nz  ! unit normal components
    real(dp), intent(out) :: lx, ly, lz  ! unit tangent components
    real(dp), intent(out) :: mx, my, mz  ! unit tangent components

    real(dp) :: denom ! denominator to recover unit vector

    continue

!   Because nx**2+ny**2+nz**2=1, at least one component
!   must be larger than sqrt(1/3).
    if(abs(nx)>0.577_dp)then
      ly = -nx
      lz =  nx
      lx = (ny-nz)
    else if(abs(ny)>0.577_dp)then
      lx =  ny
      lz = -ny
      ly = (nz-nx)
    else
      lx = -nz
      ly =  nz
      lz = (nx-ny)
    end if
    denom = 1.0/sqrt(lx**2+ly**2+lz**2)
    lx = lx*denom
    ly = ly*denom
    lz = lz*denom
    mx = ny*lz-nz*ly
    my = nz*lx-nx*lz
    mz = nx*ly-ny*lx

  end subroutine tangents_3d

!=================================== TANGENTS_2D =============================80
!
! Compute unit surface tangent vectors given normal to surface.
!
!=============================================================================80

  pure subroutine tangents_2d(nx, ny, nz, lx, ly, lz, mx, my, mz )

    real(dp), intent(in)  :: nx, ny, nz  ! unit normal components
    real(dp), intent(out) :: lx, ly, lz  ! unit tangent components
    real(dp), intent(out) :: mx, my, mz  ! unit tangent components

  continue

    lx = -nz
    lz =  nx
    ly = 0._dp

    mx = ny*lz-nz*ly
    my = nz*lx-nx*lz
    mz = nx*ly-ny*lx

  end subroutine tangents_2d

!=================================== MINMOD ==================================80
!
! Compute minmod function for flux limiting
!
!=============================================================================80

  pure function minmod(x,y,ndim)

    integer,                   intent(in) :: ndim
    real(dp), dimension(ndim)             :: minmod
    real(dp), dimension(:   ), intent(in) :: x, y


    real(dp) :: xs

    integer :: i

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp

  continue

    do i=1,ndim
      xs = sign(my_1,x(i))
      minmod(i) = xs * max(my_0,min(abs(x(i)),xs*y(i)))
    end do

  end function minmod

!================================= CELL_GRADIENTS_DDT ========================80
!
! This routine computes the gradients in a primal cell via Green-Gauss in
! 2 or 3 dimensions
!
!=============================================================================80

  subroutine cell_gradients_ddt(edges_local, node_per_cell, face_per_cell,     &
                            x_node, y_node, z_node, qdim, q_node, local_f2n,   &
                            e2n_2d, gradx_cell, grady_cell, gradz_cell,        &
                            cell_vol, nxf, nyf, nzf)

    use info_depr,       only : twod
    use debug_defs,      only : bp_solution
    use fun3d_constants, only : my_1, my_0, my_half, my_4th, my_6th
    use lmpi,            only : lmpi_conditional_stop
    use ddt,             only : ddt5, assignment(=), operator(-)               &
                              , operator(*), operator(+), operator(/)

    integer,                                 intent(in)  :: edges_local
    integer,                                 intent(in)  :: qdim
    integer,                                 intent(in)  :: node_per_cell
    integer,                                 intent(in)  :: face_per_cell
    integer,  dimension(:,:),                intent(in)  :: local_f2n
    integer,  dimension(4,2),                intent(in)  :: e2n_2d

    real(dp), dimension(node_per_cell),        intent(in)  :: x_node
    real(dp), dimension(node_per_cell),        intent(in)  :: y_node
    real(dp), dimension(node_per_cell),        intent(in)  :: z_node
    type(ddt5), dimension(qdim,node_per_cell), intent(in)  :: q_node
    type(ddt5), dimension(qdim),               intent(out) :: gradx_cell
    type(ddt5), dimension(qdim),               intent(out) :: grady_cell
    type(ddt5), dimension(qdim),               intent(out) :: gradz_cell
    real(dp), dimension(face_per_cell),        intent(out) :: nxf
    real(dp), dimension(face_per_cell),        intent(out) :: nyf
    real(dp), dimension(face_per_cell),        intent(out) :: nzf

    real(dp),                                  intent(out) :: cell_vol

    integer :: eqn, iface, ie_local
    integer :: nn1, nn2, nn3, nn4

    type(ddt5) :: qavg, qavg0, qavg1, qavg2
    real(dp) :: xavg, yavg, zavg, cell_vol_inv
    real(dp) :: xavg0, yavg0, zavg0
    real(dp) :: xavg1, yavg1, zavg1
    real(dp) :: xavg2, yavg2, zavg2
    real(dp) :: x1, x2, x3, x4, nx, nx0, nx1, nx2
    real(dp) :: y1, y2, y3, y4, ny, ny0, ny1, ny2
    real(dp) :: z1, z2, z3, z4, nz, nz0, nz1, nz2
    real(dp) :: termx, termy, termz
    real(dp) :: termx0, termy0, termz0
    real(dp) :: termx1, termy1, termz1
    real(dp) :: termx2, termy2, termz2
    real(dp) :: term0, term1, term2

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp), parameter :: my_24th = 1.0_dp/24.0_dp
    real(dp), parameter :: my_8th  = 1.0_dp/8.0_dp

    continue

    if ( qdim /= 5 ) then
      write(*,*) 'At this time cell_gradients_ddt is coded for qdim = 5 only'
      call lmpi_conditional_stop(1,'cell_gradient_ddt')
    endif

!   initialization

    gradx_cell(:) = my_0
    grady_cell(:) = my_0
    gradz_cell(:) = my_0

    cell_vol = my_0

    cell_dimension : if (twod) then

      twod_edges : do ie_local = 1,edges_local

!       local node numbers of edge endpoints

        nn1 = e2n_2d(ie_local,1)
        nn2 = e2n_2d(ie_local,2)

        x1 = x_node(nn1)
        x2 = x_node(nn2)

        z1 = z_node(nn1)
        z2 = z_node(nn2)

!       edge midpoint (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

        xavg = x1 + x2
        zavg = z1 + z2

!       edge normals

        nx = -(z2 - z1)
        nz =  (x2 - x1)

!       cell volume (area) contributions

        cell_vol = cell_vol + (xavg*nx + zavg*nz)*my_4th

        termx = nx*my_half
        termz = nz*my_half

!       gradient contributions

        do eqn = 1,qdim

          qavg = q_node(eqn,nn1) + q_node(eqn,nn2)

          gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
          gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg

        end do

      end do twod_edges

    else cell_dimension

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

          x1 = x_node(nn1)
          x2 = x_node(nn2)
          x3 = x_node(nn3)

          y1 = y_node(nn1)
          y2 = y_node(nn2)
          y3 = y_node(nn3)

          z1 = z_node(nn1)
          z2 = z_node(nn2)
          z3 = z_node(nn3)

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x1 + x2 + x3
          yavg = y1 + y2 + y3
          zavg = z1 + z2 + z3

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!         gradient contributions

          do eqn = 1,qdim

            qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)

            gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
            grady_cell(eqn) = grady_cell(eqn) + termy*qavg
            gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg

          end do

        else

!         quadrilateral faces of the cell

          x1 = x_node(nn1)
          x2 = x_node(nn2)
          x3 = x_node(nn3)
          x4 = x_node(nn4)

          y1 = y_node(nn1)
          y2 = y_node(nn2)
          y3 = y_node(nn3)
          y4 = y_node(nn4)

          z1 = z_node(nn1)
          z2 = z_node(nn2)
          z3 = z_node(nn3)
          z4 = z_node(nn4)

          if ( bp_solution ) then

!         Use simple quadratures to evaluate gradients.  The resulting
!         volume is identical to the volume computed by averaging the
!         volumes associated with the two diagonal split choices.  The
!         quadratures for gradient are not identical, but simpler and
!         consistent across elements which share faces.

!         Define face as average plane 1-2-3-4.  This average plane passes
!         through the center of the quad and through each edge midpoint.
!         The nodes of the quad are displaced from this plane by an identical
!         amount (+-+-) confirming the plane is a least squares fit.

!         face centroid (factor of 1/4 deferred till the
!         contribution to cell_vol is calculated)

          xavg0 = x1 + x2 + x3 + x4
          yavg0 = y1 + y2 + y3 + y4
          zavg0 = z1 + z2 + z3 + z4

!         normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx0 = (y2 - y4)*(z3 - z1) - (z2 - z4)*(y3 - y1)
          ny0 = (z2 - z4)*(x3 - x1) - (x2 - x4)*(z3 - z1)
          nz0 = (x2 - x4)*(y3 - y1) - (y2 - y4)*(x3 - x1)

          term0 = xavg0*nx0 + yavg0*ny0 + zavg0*nz0

!         cell volume contributions

          cell_vol = cell_vol + term0*my_24th

!         gradient contributions

          termx0 = nx0*my_8th
          termy0 = ny0*my_8th
          termz0 = nz0*my_8th

          do eqn = 1,qdim

            qavg0 = q_node(eqn,nn1) + q_node(eqn,nn2) &
                  + q_node(eqn,nn3) + q_node(eqn,nn4)

            gradx_cell(eqn) = gradx_cell(eqn) + termx0*qavg0
            grady_cell(eqn) = grady_cell(eqn) + termy0*qavg0
            gradz_cell(eqn) = gradz_cell(eqn) + termz0*qavg0

          end do

          nx = nx0
          ny = ny0
          nz = nz0

          else

!         FIXME: Asymmetrical choice.
!         FIXME: Inconsistent volumes with preprocessing step.
!         FIXME: Inconsistent between elements that share faces.

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x1 + x2 + x3
          yavg1 = y1 + y2 + y3
          zavg1 = z1 + z2 + z3

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny1 = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz1 = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x1 + x3 + x4
          yavg2 = y1 + y3 + y4
          zavg2 = z1 + z3 + z4

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y3 - y1)*(z4 - z1) - (z3 - z1)*(y4 - y1)
          ny2 = (z3 - z1)*(x4 - x1) - (x3 - x1)*(z4 - z1)
          nz2 = (x3 - x1)*(y4 - y1) - (y3 - y1)*(x4 - x1)

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!         gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          do eqn = 1,qdim

            qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
            qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)

            gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
            grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
            gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2

          end do

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

          endif

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

    end if cell_dimension

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

    cell_vol_inv = my_1/cell_vol

    gradx_cell(:) = gradx_cell(:) * cell_vol_inv
    grady_cell(:) = grady_cell(:) * cell_vol_inv
    gradz_cell(:) = gradz_cell(:) * cell_vol_inv

  end subroutine cell_gradients_ddt

!================================= CELL_GRADIENTS ============================80
!
! This routine computes the gradients in a primal cell via Green-Gauss in
! 2 or 3 dimensions
!
!=============================================================================80

  subroutine cell_gradients(edges_local, node_per_cell, face_per_cell,         &
                            x_node, y_node, z_node, qdim, q_node, local_f2n,   &
                            e2n_2d, gradx_cell, grady_cell, gradz_cell,        &
                            cell_vol, nxf, nyf, nzf)

    use info_depr,       only : twod
    use debug_defs,      only : bp_solution
    use fun3d_constants, only : my_1, my_0, my_half, my_4th, my_6th

    integer,                                 intent(in)  :: edges_local
    integer,                                 intent(in)  :: qdim
    integer,                                 intent(in)  :: node_per_cell
    integer,                                 intent(in)  :: face_per_cell
    integer,  dimension(:,:),                intent(in)  :: local_f2n
    integer,  dimension(4,2),                intent(in)  :: e2n_2d

    real(dp), dimension(node_per_cell),      intent(in)  :: x_node
    real(dp), dimension(node_per_cell),      intent(in)  :: y_node
    real(dp), dimension(node_per_cell),      intent(in)  :: z_node
    real(dp), dimension(qdim,node_per_cell), intent(in)  :: q_node
    real(dp), dimension(qdim),               intent(out) :: gradx_cell
    real(dp), dimension(qdim),               intent(out) :: grady_cell
    real(dp), dimension(qdim),               intent(out) :: gradz_cell
    real(dp), dimension(face_per_cell),      intent(out) :: nxf
    real(dp), dimension(face_per_cell),      intent(out) :: nyf
    real(dp), dimension(face_per_cell),      intent(out) :: nzf

    real(dp),                                intent(out) :: cell_vol

    integer :: eqn, iface, ie_local
    integer :: nn1, nn2, nn3, nn4

    real(dp) :: xavg, yavg, zavg, qavg, cell_vol_inv
    real(dp) :: xavg0, yavg0, zavg0, qavg0
    real(dp) :: xavg1, yavg1, zavg1, qavg1
    real(dp) :: xavg2, yavg2, zavg2, qavg2
    real(dp) :: x1, x2, x3, x4, nx, nx0, nx1, nx2
    real(dp) :: y1, y2, y3, y4, ny, ny0, ny1, ny2
    real(dp) :: z1, z2, z3, z4, nz, nz0, nz1, nz2
    real(dp) :: termx, termy, termz
    real(dp) :: termx0, termy0, termz0
    real(dp) :: termx1, termy1, termz1
    real(dp) :: termx2, termy2, termz2
    real(dp) :: term0, term1, term2

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp), parameter :: my_24th = 1.0_dp/24.0_dp
    real(dp), parameter :: my_8th  = 1.0_dp/8.0_dp

    continue

!   initialization

    gradx_cell(:) = my_0
    grady_cell(:) = my_0
    gradz_cell(:) = my_0

    cell_vol = my_0

    cell_dimension : if (twod) then

      twod_edges : do ie_local = 1,edges_local

!       local node numbers of edge endpoints

        nn1 = e2n_2d(ie_local,1)
        nn2 = e2n_2d(ie_local,2)

        x1 = x_node(nn1)
        x2 = x_node(nn2)

        z1 = z_node(nn1)
        z2 = z_node(nn2)

!       edge midpoint (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

        xavg = x1 + x2
        zavg = z1 + z2

!       edge normals

        nx = -(z2 - z1)
        nz =  (x2 - x1)

!       cell volume (area) contributions

        cell_vol = cell_vol + (xavg*nx + zavg*nz)*my_4th

        termx = nx*my_half
        termz = nz*my_half

!       gradient contributions

        do eqn = 1,qdim

          qavg = q_node(eqn,nn1) + q_node(eqn,nn2)

          gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
          gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg

        end do

      end do twod_edges

    else cell_dimension

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

          x1 = x_node(nn1)
          x2 = x_node(nn2)
          x3 = x_node(nn3)

          y1 = y_node(nn1)
          y2 = y_node(nn2)
          y3 = y_node(nn3)

          z1 = z_node(nn1)
          z2 = z_node(nn2)
          z3 = z_node(nn3)

!         face normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x1 + x2 + x3
          yavg = y1 + y2 + y3
          zavg = z1 + z2 + z3

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!         gradient contributions

          do eqn = 1,qdim

            qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)

            gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
            grady_cell(eqn) = grady_cell(eqn) + termy*qavg
            gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg

          end do

        else

!         quadrilateral faces of the cell

          x1 = x_node(nn1)
          x2 = x_node(nn2)
          x3 = x_node(nn3)
          x4 = x_node(nn4)

          y1 = y_node(nn1)
          y2 = y_node(nn2)
          y3 = y_node(nn3)
          y4 = y_node(nn4)

          z1 = z_node(nn1)
          z2 = z_node(nn2)
          z3 = z_node(nn3)
          z4 = z_node(nn4)

          if ( bp_solution ) then

!         Use simple quadratures to evaluate gradients.  The resulting
!         volume is identical to the volume computed by averaging the
!         volumes associated with the two diagonal split choices.  The
!         quadratures for gradient are not identical, but simpler and
!         consistent across elements which share faces.

!         Define face as average plane 1-2-3-4.  This average plane passes
!         through the center of the quad and through each edge midpoint.
!         The nodes of the quad are displaced from this plane by an identical
!         amount (+-+-) confirming the plane is a least squares fit.

!         face centroid (factor of 1/4 deferred till the
!         contribution to cell_vol is calculated)

          xavg0 = x1 + x2 + x3 + x4
          yavg0 = y1 + y2 + y3 + y4
          zavg0 = z1 + z2 + z3 + z4

!         normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx0 = (y2 - y4)*(z3 - z1) - (z2 - z4)*(y3 - y1)
          ny0 = (z2 - z4)*(x3 - x1) - (x2 - x4)*(z3 - z1)
          nz0 = (x2 - x4)*(y3 - y1) - (y2 - y4)*(x3 - x1)

          term0 = xavg0*nx0 + yavg0*ny0 + zavg0*nz0

!         cell volume contributions

          cell_vol = cell_vol + term0*my_24th

!         gradient contributions

          termx0 = nx0*my_8th
          termy0 = ny0*my_8th
          termz0 = nz0*my_8th

          do eqn = 1,qdim

            qavg0 = q_node(eqn,nn1) + q_node(eqn,nn2) &
                  + q_node(eqn,nn3) + q_node(eqn,nn4)

            gradx_cell(eqn) = gradx_cell(eqn) + termx0*qavg0
            grady_cell(eqn) = grady_cell(eqn) + termy0*qavg0
            gradz_cell(eqn) = gradz_cell(eqn) + termz0*qavg0

          end do

          nx = nx0
          ny = ny0
          nz = nz0

          else

!         FIXME: Asymmetrical choice.
!         FIXME: Inconsistent volumes with preprocessing step.
!         FIXME: Inconsistent between elements that share faces.

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x1 + x2 + x3
          yavg1 = y1 + y2 + y3
          zavg1 = z1 + z2 + z3

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny1 = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz1 = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x1 + x3 + x4
          yavg2 = y1 + y3 + y4
          zavg2 = z1 + z3 + z4

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y3 - y1)*(z4 - z1) - (z3 - z1)*(y4 - y1)
          ny2 = (z3 - z1)*(x4 - x1) - (x3 - x1)*(z4 - z1)
          nz2 = (x3 - x1)*(y4 - y1) - (y3 - y1)*(x4 - x1)

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

!         gradient contributions

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

          do eqn = 1,qdim

            qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
            qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)

            gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
            grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
            gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2

          end do

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

          endif

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

    end if cell_dimension

!   need to divide the gradient sums by the grid cell volume to give the
!   cell-average Green-Gauss gradients

    cell_vol_inv = my_1/cell_vol

    gradx_cell(:) = gradx_cell(:) * cell_vol_inv
    grady_cell(:) = grady_cell(:) * cell_vol_inv
    gradz_cell(:) = gradz_cell(:) * cell_vol_inv

  end subroutine cell_gradients

!================================= CELL_JACOBIANS ============================80
!
! This routine computes the jacobians of gradients in a primal cell with respect
! to PRIMITIVE variables via Green-Gauss in 2 or 3 dimensions
!
!=============================================================================80

  subroutine cell_jacobians(edges_local, node_per_cell, face_per_cell, x_node, &
                            y_node, z_node, local_f2n, e2n_2d, dgradx_celldq,  &
                            dgrady_celldq, dgradz_celldq, cell_vol, nxf, nyf,  &
                            nzf)

    use info_depr,       only : twod
    use debug_defs,      only : bp_solution
    use fun3d_constants, only : my_1, my_0, my_half, my_4th, my_6th

    integer,                              intent(in)  :: edges_local
    integer,                              intent(in)  :: node_per_cell
    integer,                              intent(in)  :: face_per_cell
    integer,  dimension(:,:),             intent(in)  :: local_f2n
    integer,  dimension(4,2),             intent(in)  :: e2n_2d
    real(dp), dimension(node_per_cell),   intent(in)  :: x_node
    real(dp), dimension(node_per_cell),   intent(in)  :: y_node
    real(dp), dimension(node_per_cell),   intent(in)  :: z_node
    real(dp), dimension(node_per_cell),   intent(out) :: dgradx_celldq
    real(dp), dimension(node_per_cell),   intent(out) :: dgrady_celldq
    real(dp), dimension(node_per_cell),   intent(out) :: dgradz_celldq
    real(dp), dimension(face_per_cell),   intent(out) :: nxf
    real(dp), dimension(face_per_cell),   intent(out) :: nyf
    real(dp), dimension(face_per_cell),   intent(out) :: nzf

    real(dp),                             intent(out) :: cell_vol

    integer :: iface, ie_local
    integer :: nn1, nn2, nn3, nn4

    real(dp) :: xavg, yavg, zavg, cell_vol_inv
    real(dp) :: xavg0, yavg0, zavg0
    real(dp) :: xavg1, yavg1, zavg1
    real(dp) :: xavg2, yavg2, zavg2
    real(dp) :: x1, x2, x3, x4, nx, nx0, nx1, nx2
    real(dp) :: y1, y2, y3, y4, ny, ny0, ny1, ny2
    real(dp) :: z1, z2, z3, z4, nz, nz0, nz1, nz2
    real(dp) :: termx, termy, termz
    real(dp) :: termx0, termy0, termz0
    real(dp) :: termx1, termy1, termz1
    real(dp) :: termx2, termy2, termz2
    real(dp) :: term0, term1, term2

    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp), parameter :: my_24th = 1.0_dp/24.0_dp
    real(dp), parameter :: my_8th  = 1.0_dp/8.0_dp

    continue

!   initialization

    dgradx_celldq(:) = my_0
    dgrady_celldq(:) = my_0
    dgradz_celldq(:) = my_0

    cell_vol = my_0

    cell_dimension : if (twod) then

      twod_edges : do ie_local = 1,edges_local

!       local node numbers of edge endpoints

        nn1 = e2n_2d(ie_local,1)
        nn2 = e2n_2d(ie_local,2)

        x1 = x_node(nn1)
        x2 = x_node(nn2)

        z1 = z_node(nn1)
        z2 = z_node(nn2)

!       edge midpoint (factor of 1/2 deferred till cell_vol
!       and jacobian terms are calculated)

        xavg = x1 + x2
        zavg = z1 + z2

!       edge normals

        nx = -(z2 - z1)
        nz =  (x2 - x1)

!       cell volume (area) contributions

        cell_vol = cell_vol + (xavg*nx + zavg*nz)*my_4th

        termx = nx*my_half
        termz = nz*my_half

!       jacobian contributions
!       note: jacobian pieces end up depending on geometry only, so
!       unlike the cell fluxes, there is no need to loop over qdim;
!       the loop is left here as commented for reference

!       do eqn = 1,qdim

!         qavg = q_node(eqn,nn1) + q_node(eqn,nn2)

!         gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
!         gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg

          dgradx_celldq(nn1) = dgradx_celldq(nn1) + termx
          dgradz_celldq(nn1) = dgradz_celldq(nn1) + termz

          dgradz_celldq(nn2) = dgradz_celldq(nn2) + termz
          dgradx_celldq(nn2) = dgradx_celldq(nn2) + termx

!       end do

      end do twod_edges

    else cell_dimension

      threed_faces : do iface = 1,face_per_cell

        nn1 = local_f2n(iface,1)
        nn2 = local_f2n(iface,2)
        nn3 = local_f2n(iface,3)
        nn4 = local_f2n(iface,4)

        if (nn4 == nn1) then

!         triangular faces of the cell

          x1 = x_node(nn1)
          x2 = x_node(nn2)
          x3 = x_node(nn3)

          y1 = y_node(nn1)
          y2 = y_node(nn2)
          y3 = y_node(nn3)

          z1 = z_node(nn1)
          z2 = z_node(nn2)
          z3 = z_node(nn3)

!         face normals (factor of 1/2 deferred till cell_vol
!         and jacobian terms are calculated)

          nx = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg = x1 + x2 + x3
          yavg = y1 + y2 + y3
          zavg = z1 + z2 + z3

!         cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

          termx = nx*my_6th
          termy = ny*my_6th
          termz = nz*my_6th

!         jacobian contributions
!         note: jacobian pieces end up depending on geometry only, so
!         unlike the cell fluxes, there is no need to loop over qdim;
!         the loop is left here as commented for reference

!         do eqn = 1,qdim

!           qavg = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)

!           gradx_cell(eqn) = gradx_cell(eqn) + termx*qavg
!           grady_cell(eqn) = grady_cell(eqn) + termy*qavg
!           gradz_cell(eqn) = gradz_cell(eqn) + termz*qavg

            dgradx_celldq(nn1) = dgradx_celldq(nn1) + termx
            dgrady_celldq(nn1) = dgrady_celldq(nn1) + termy
            dgradz_celldq(nn1) = dgradz_celldq(nn1) + termz

            dgradx_celldq(nn2) = dgradx_celldq(nn2) + termx
            dgrady_celldq(nn2) = dgrady_celldq(nn2) + termy
            dgradz_celldq(nn2) = dgradz_celldq(nn2) + termz

            dgradx_celldq(nn3) = dgradx_celldq(nn3) + termx
            dgrady_celldq(nn3) = dgrady_celldq(nn3) + termy
            dgradz_celldq(nn3) = dgradz_celldq(nn3) + termz

!         end do

        else

!         quadrilateral faces of the cell

          x1 = x_node(nn1)
          x2 = x_node(nn2)
          x3 = x_node(nn3)
          x4 = x_node(nn4)

          y1 = y_node(nn1)
          y2 = y_node(nn2)
          y3 = y_node(nn3)
          y4 = y_node(nn4)

          z1 = z_node(nn1)
          z2 = z_node(nn2)
          z3 = z_node(nn3)
          z4 = z_node(nn4)

          if ( bp_solution ) then

!         Use simple quadratures to evaluate gradients.  The resulting
!         volume is identical to the volume computed by averaging the
!         volumes associated with the two diagonal split choices.  The
!         quadratures for gradient are not identical, but simpler and
!         consistent across elements which share faces.

!         Define face as average plane 1-2-3-4.  This average plane passes
!         through the center of the quad and through each edge midpoint.
!         The nodes of the quad are displaced from this plane by an identical
!         amount (+-+-) confirming the plane is a least squares fit.

!         face centroid (factor of 1/4 deferred till the
!         contribution to cell_vol is calculated)

          xavg0 = x1 + x2 + x3 + x4
          yavg0 = y1 + y2 + y3 + y4
          zavg0 = z1 + z2 + z3 + z4

!         normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx0 = (y2 - y4)*(z3 - z1) - (z2 - z4)*(y3 - y1)
          ny0 = (z2 - z4)*(x3 - x1) - (x2 - x4)*(z3 - z1)
          nz0 = (x2 - x4)*(y3 - y1) - (y2 - y4)*(x3 - x1)

          term0 = xavg0*nx0 + yavg0*ny0 + zavg0*nz0

!         cell volume contributions

          cell_vol = cell_vol + term0*my_24th

          termx0 = nx0*my_8th
          termy0 = ny0*my_8th
          termz0 = nz0*my_8th

!         jacobian contributions
!         note: jacobian pieces end up depending on geometry only, so
!         unlike the cell fluxes, there is no need to loop over qdim;
!         the loop is left here as commented for reference

!         do eqn = 1,qdim

!           qavg0 = q_node(eqn,nn1) + q_node(eqn,nn2) &
!                 + q_node(eqn,nn3) + q_node(eqn,nn4)

!           gradx_cell(eqn) = gradx_cell(eqn) + termx0*qavg0
!           grady_cell(eqn) = grady_cell(eqn) + termy0*qavg0
!           gradz_cell(eqn) = gradz_cell(eqn) + termz0*qavg0

            dgradx_celldq(nn1) = dgradx_celldq(nn1) + termx0
            dgrady_celldq(nn1) = dgrady_celldq(nn1) + termy0
            dgradz_celldq(nn1) = dgradz_celldq(nn1) + termz0

            dgradx_celldq(nn2) = dgradx_celldq(nn2) + termx0
            dgrady_celldq(nn2) = dgrady_celldq(nn2) + termy0
            dgradz_celldq(nn2) = dgradz_celldq(nn2) + termz0

            dgradx_celldq(nn3) = dgradx_celldq(nn3) + termx0
            dgrady_celldq(nn3) = dgrady_celldq(nn3) + termy0
            dgradz_celldq(nn3) = dgradz_celldq(nn3) + termz0

            dgradx_celldq(nn4) = dgradx_celldq(nn4) + termx0
            dgrady_celldq(nn4) = dgrady_celldq(nn4) + termy0
            dgradz_celldq(nn4) = dgradz_celldq(nn4) + termz0

!         end do

          nx = nx0
          ny = ny0
          nz = nz0

          else

!         FIXME: Asymmetrical choice.
!         FIXME: Inconsistent volumes with preprocessing step.
!         FIXME: Inconsistent between elements that share faces.

!         break face up into triangles 1-2-3 and 1-3-4 and add together

!         triangle 1: 1-2-3

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg1 = x1 + x2 + x3
          yavg1 = y1 + y2 + y3
          zavg1 = z1 + z2 + z3

!         triangle 1 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx1 = (y2 - y1)*(z3 - z1) - (z2 - z1)*(y3 - y1)
          ny1 = (z2 - z1)*(x3 - x1) - (x2 - x1)*(z3 - z1)
          nz1 = (x2 - x1)*(y3 - y1) - (y2 - y1)*(x3 - x1)

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!         triangle 2: 1-3-4

!         face centroid (factor of 1/3 deferred till the
!         contribution to cell_vol is calculated)

          xavg2 = x1 + x3 + x4
          yavg2 = y1 + y3 + y4
          zavg2 = z1 + z3 + z4

!         triangle 2 normals (factor of 1/2 deferred till cell_vol
!         and gradient terms are calculated)

          nx2 = (y3 - y1)*(z4 - z1) - (z3 - z1)*(y4 - y1)
          ny2 = (z3 - z1)*(x4 - x1) - (x3 - x1)*(z4 - z1)
          nz2 = (x3 - x1)*(y4 - y1) - (y3 - y1)*(x4 - x1)

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!         cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

          termx1 = nx1*my_6th
          termy1 = ny1*my_6th
          termz1 = nz1*my_6th

          termx2 = nx2*my_6th
          termy2 = ny2*my_6th
          termz2 = nz2*my_6th

!         jacobian contributions
!         note: jacobian pieces end up depending on geometry only, so
!         unlike the cell fluxes, there is no need to loop over qdim;
!         the loop is left here as commented for reference

!         do eqn = 1,qdim

!           qavg1 = q_node(eqn,nn1) + q_node(eqn,nn2) + q_node(eqn,nn3)
!           qavg2 = q_node(eqn,nn1) + q_node(eqn,nn3) + q_node(eqn,nn4)

!           gradx_cell(eqn) = gradx_cell(eqn) + termx1*qavg1 + termx2*qavg2
!           grady_cell(eqn) = grady_cell(eqn) + termy1*qavg1 + termy2*qavg2
!           gradz_cell(eqn) = gradz_cell(eqn) + termz1*qavg1 + termz2*qavg2

            dgradx_celldq(nn1) = dgradx_celldq(nn1) + termx1 + termx2
            dgrady_celldq(nn1) = dgrady_celldq(nn1) + termy1 + termy2
            dgradz_celldq(nn1) = dgradz_celldq(nn1) + termz1 + termz2

            dgradx_celldq(nn2) = dgradx_celldq(nn2) + termx1
            dgrady_celldq(nn2) = dgrady_celldq(nn2) + termy1
            dgradz_celldq(nn2) = dgradz_celldq(nn2) + termz1

            dgradx_celldq(nn3) = dgradx_celldq(nn3) + termx1 + termx2
            dgrady_celldq(nn3) = dgrady_celldq(nn3) + termy1 + termy2
            dgradz_celldq(nn3) = dgradz_celldq(nn3) + termz1 + termz2

            dgradx_celldq(nn4) = dgradx_celldq(nn4) + termx2
            dgrady_celldq(nn4) = dgrady_celldq(nn4) + termy2
            dgradz_celldq(nn4) = dgradz_celldq(nn4) + termz2

!         end do

          nx = nx1 + nx2
          ny = ny1 + ny2
          nz = nz1 + nz2

          endif

        end if

        nxf(iface) = nx
        nyf(iface) = ny
        nzf(iface) = nz

      end do threed_faces

    end if cell_dimension

!   need to divide the jacobain sums by the grid cell volume to give the
!   cell-average Green-Gauss jacobians

    cell_vol_inv = my_1 / cell_vol

    dgradx_celldq(:) = dgradx_celldq(:) * cell_vol_inv
    dgrady_celldq(:) = dgrady_celldq(:) * cell_vol_inv
    dgradz_celldq(:) = dgradz_celldq(:) * cell_vol_inv

  end subroutine cell_jacobians

!================================= BIG_ANGLE ========================80
!
! Check if cell-face angles greater that a specified value and return
! .true. to skip the contribution from this cell (3D only).
!
!=============================================================================80

  function big_angle( face_per_cell, nxf, nyf, nzf, chk_norm )

    integer,                            intent(in)  :: face_per_cell
    integer,  dimension(face_per_cell,face_per_cell),              &
                                        intent(in)  :: chk_norm
    real(dp), dimension(face_per_cell), intent(in)  :: nxf, nyf, nzf

    logical                                         :: big_angle

    integer :: i, j

    real(dp) :: dot

    real(dp), parameter :: my_mxd = 0.99939_dp

  continue

    big_angle = .false.

    ! SP Note: only need to check upper part of matrix chk_norm since
    ! dot products commute (A*B = B*A) and the diagonal indicates
    ! the dot product of a face with itself; also note that nx, ny, nz
    ! are not unit normals so we must scale the dot product accordingly

    face_loop : do i=1,face_per_cell

      do j=i+1,face_per_cell

        if (chk_norm(i,j) > 0) then
          dot = nxf(i)*nxf(j) + nyf(i)*nyf(j) + nzf(i)*nzf(j)
          !  scale to unit normals
          dot = dot / sqrt(nxf(i)*nxf(i) + nyf(i)*nyf(i) + nzf(i)*nzf(i))
          dot = dot / sqrt(nxf(j)*nxf(j) + nyf(j)*nyf(j) + nzf(j)*nzf(j))
          if ( dot >= my_mxd ) then
            big_angle = .true.
            exit face_loop
          endif
        end if

      end do

    end do face_loop

  end function big_angle


!================================== MY_CLOCK =================================80
!
! Gets the current CPU time (user + system) and sets argument my_time
!
!=============================================================================80

  subroutine my_clock(my_time)

    use system_extensions, only : se_wall_clock

    real(dp), intent(out) :: my_time

  continue

    my_time = se_wall_clock()

  end subroutine my_clock

end module utilities

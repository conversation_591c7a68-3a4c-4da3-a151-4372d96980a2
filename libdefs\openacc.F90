module openacc

  implicit none

  private

  public :: use_openacc, use_cudampi
  public :: openacc_initialize, openacc_shutdown

  logical :: use_cudampi

#if _OPENACC
  logical :: use_openacc = .true.
#else
  logical :: use_openacc = .false.
#endif

contains

!================================ OPENACC_INITIALIZE =========================80
!
! Initialize some things related to OpenACC
!
!=============================================================================80
  subroutine openacc_initialize(grid,soln,crow,need_grid_velocity)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow
    use openacc_vars,   only : openacc_tet_c2n, openacc_tet_c2e,               &
                               openacc_pyr_c2n, openacc_pyr_c2e,               &
                               openacc_prz_c2n, openacc_prz_c2e,               &
                               openacc_hex_c2n, openacc_hex_c2e,               &
                               openacc_ntet_in_color, openacc_npyr_in_color,   &
                               openacc_nprz_in_color, openacc_nhex_in_color,   &
                               openacc_ntet_colors, openacc_npyr_colors,       &
                               openacc_nprz_colors, openacc_nhex_colors

    type(grid_type), dimension(:), intent(in) :: grid
    type(soln_type), dimension(:), intent(in) :: soln
    type(crow_flow), dimension(:), intent(in) :: crow
    logical,                       intent(in) :: need_grid_velocity

    integer :: i

  continue

! Set up the edge coloring

    call openacc_color_edges(grid(1),crow(1),need_grid_velocity)

! Set up the element coloring

    allocate(openacc_ntet_in_color(1))
    allocate(openacc_tet_c2n(1,1))
    allocate(openacc_tet_c2e(1,1))
    allocate(openacc_npyr_in_color(1))
    allocate(openacc_pyr_c2n(1,1))
    allocate(openacc_pyr_c2e(1,1))
    allocate(openacc_nprz_in_color(1))
    allocate(openacc_prz_c2n(1,1))
    allocate(openacc_prz_c2e(1,1))
    allocate(openacc_nhex_in_color(1))
    allocate(openacc_hex_c2n(1,1))
    allocate(openacc_hex_c2e(1,1))

    do i = 1, grid(1)%nelem
      select case(grid(1)%elem(i)%type_cell)
      case('tet')
        call openacc_color_cells(grid(1),i,openacc_ntet_colors,                &
                                 openacc_ntet_in_color,openacc_tet_c2n,        &
                                 openacc_tet_c2e)
      case('pyr')
        call openacc_color_cells(grid(1),i,openacc_npyr_colors,                &
                                 openacc_npyr_in_color,openacc_pyr_c2n,        &
                                 openacc_pyr_c2e)
      case('prz')
        call openacc_color_cells(grid(1),i,openacc_nprz_colors,                &
                                 openacc_nprz_in_color,openacc_prz_c2n,        &
                                 openacc_prz_c2e)
      case('hex')
        call openacc_color_cells(grid(1),i,openacc_nhex_colors,                &
                                 openacc_nhex_in_color,openacc_hex_c2n,        &
                                 openacc_hex_c2e)
      end select
    end do

! Set up communication infrastructure for CUDA-enabled MPI

    call openacc_setup_cudampi(soln(1))

! Create and move some data to the device

    call openacc_enter_exit_data(grid,soln,crow,1)

  end subroutine openacc_initialize


!================================ OPENACC_SHUTDOWN ===========================80
!
! Shut down some things related to OpenACC
!
!=============================================================================80
  subroutine openacc_shutdown(grid,soln,crow)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow

    type(grid_type), dimension(:), intent(in) :: grid
    type(soln_type), dimension(:), intent(in) :: soln
    type(crow_flow), dimension(:), intent(in) :: crow

  continue

    call openacc_enter_exit_data(grid,soln,crow,2)

  end subroutine openacc_shutdown


!============================= OPENACC_ENTER_EXIT_DATA =======================80
!
!  Unstructured data setup for device
!
!=============================================================================80
  subroutine openacc_enter_exit_data(grid,soln,crow,key)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow
    use lmpi_app,       only : openacc_sendproc, openacc_recvproc,             &
                               openacc_sendindex, openacc_recvindex,           &
                               openacc_sendproc_offset,                        &
                               openacc_recvproc_offset,                        &
                               openacc_sendindex_offset,                       &
                               openacc_recvindex_offset, openacc_sendbuf,      &
                               openacc_recvbuf, openacc_csendbuf,              &
                               openacc_crecvbuf
    use openacc_vars,   only : iam, jam, g2m, fhelp, vol, x, y, z, eptr,       &
                               openacc_eptr, openacc_fhelp, openacc_tet_c2n,   &
                               openacc_pyr_c2n, openacc_prz_c2n,               &
                               openacc_hex_c2n, openacc_tet_c2e,               &
                               openacc_pyr_c2e, openacc_prz_c2e,               &
                               openacc_hex_c2e, openacc_xn, openacc_yn,        &
                               openacc_zn, openacc_ra, openacc_facespeed,      &
                               openacc_weight, cdt, amut, dq, res, qdof,       &
                               a_diag, a_diag_lu, a_off

    integer, intent(in) :: key

    type(grid_type), dimension(:), intent(in) :: grid
    type(soln_type), dimension(:), intent(in) :: soln
    type(crow_flow), dimension(:), intent(in) :: crow

  continue

! First set up and copy over the static arrays

    iam => crow(1)%iam
    if ( key == 1 ) then
!$acc enter data copyin(iam)
    else if ( key == 2 ) then
!$acc exit data delete(iam)
    endif

    jam => crow(1)%jam
    if ( key == 1 ) then
!$acc enter data copyin(jam)
    else if ( key == 2 ) then
!$acc exit data delete(jam)
    endif

    g2m => crow(1)%g2m
    if ( key == 1 ) then
!$acc enter data copyin(g2m)
    else if ( key == 2 ) then
!$acc exit data delete(g2m)
    endif

    fhelp => crow(1)%fhelp
    if ( key == 1 ) then
!$acc enter data copyin(fhelp)
    else if ( key == 2 ) then
!$acc exit data delete(fhelp)
    endif

    vol => grid(1)%vol
    if ( key == 1 ) then
!$acc enter data copyin(vol)
    else if ( key == 2 ) then
!$acc exit data delete(vol)
    endif

    x => grid(1)%x
    if ( key == 1 ) then
!$acc enter data copyin(x)
    else if ( key == 2 ) then
!$acc exit data delete(x)
    endif

    y => grid(1)%y
    if ( key == 1 ) then
!$acc enter data copyin(y)
    else if ( key == 2 ) then
!$acc exit data delete(y)
    endif

    z => grid(1)%z
    if ( key == 1 ) then
!$acc enter data copyin(z)
    else if ( key == 2 ) then
!$acc exit data delete(z)
    endif

    eptr => grid(1)%eptr
    if ( key == 1 ) then
!$acc enter data copyin(eptr)
    else if ( key == 2 ) then
!$acc exit data delete(eptr)
    endif

    if ( key == 1 ) then
!$acc enter data copyin(openacc_eptr, openacc_fhelp, openacc_xn, openacc_yn,   &
!$acc                   openacc_zn, openacc_ra, openacc_facespeed,             &
!$acc                   openacc_weight, openacc_tet_c2n, openacc_pyr_c2n,      &
!$acc                   openacc_prz_c2n, openacc_hex_c2n, openacc_tet_c2e,     &
!$acc                   openacc_pyr_c2e, openacc_prz_c2e, openacc_hex_c2e,     &
!$acc                   openacc_sendproc, openacc_sendproc_offset,             &
!$acc                   openacc_recvproc, openacc_recvproc_offset,             &
!$acc                   openacc_sendindex, openacc_recvindex,                  &
!$acc                   openacc_sendindex_offset, openacc_recvindex_offset)
    else if ( key == 2 ) then
!$acc exit  data delete(openacc_eptr, openacc_fhelp, openacc_xn, openacc_yn,   &
!$acc                   openacc_zn, openacc_ra, openacc_facespeed,             &
!$acc                   openacc_weight, openacc_tet_c2n, openacc_pyr_c2n,      &
!$acc                   openacc_prz_c2n, openacc_hex_c2n, openacc_tet_c2e,     &
!$acc                   openacc_pyr_c2e, openacc_prz_c2e, openacc_hex_c2e,     &
!$acc                   openacc_sendproc, openacc_sendproc_offset,             &
!$acc                   openacc_recvproc, openacc_recvproc_offset,             &
!$acc                   openacc_sendindex, openacc_recvindex,                  &
!$acc                   openacc_sendindex_offset, openacc_recvindex_offset)
    endif

! Now set up the other arrays that are not static

    cdt => soln(1)%cdt
    if ( key == 1 ) then
!$acc enter data create(cdt)
    else if ( key == 2 ) then
!$acc exit data delete(cdt)
    endif

    amut => soln(1)%amut
    if ( key == 1 ) then
!$acc enter data create(amut)
    else if ( key == 2 ) then
!$acc exit data delete(amut)
    endif

    dq => soln(1)%dq
    if ( key == 1 ) then
!$acc enter data create(dq)
    else if ( key == 2 ) then
!$acc exit data delete(dq)
    endif

    res => soln(1)%res
    if ( key == 1 ) then
!$acc enter data create(res)
    else if ( key == 2 ) then
!$acc exit data delete(res)
    endif

    qdof => soln(1)%q_dof
    if ( key == 1 ) then
!$acc enter data create(qdof)
    else if ( key == 2 ) then
!$acc exit data delete(qdof)
    endif

    a_diag => soln(1)%a_diag
    if ( key == 1 ) then
!$acc enter data create(a_diag)
    else if ( key == 2 ) then
!$acc exit data delete(a_diag)
    endif

    a_diag_lu => soln(1)%a_diag_lu
    if ( key == 1 ) then
!$acc enter data create(a_diag_lu)
    else if ( key == 2 ) then
!$acc exit data delete(a_diag_lu)
    endif

    a_off => soln(1)%a_off
    if ( key == 1 ) then
!$acc enter data create(a_off)
    else if ( key == 2 ) then
!$acc exit data delete(a_off)
    endif

    if ( key == 1 ) then
!$acc enter data create(openacc_sendbuf, openacc_recvbuf)
!$acc enter data create(openacc_csendbuf, openacc_crecvbuf)
    else if ( key == 2 ) then
!$acc exit  data delete(openacc_sendbuf, openacc_recvbuf)
!$acc exit  data delete(openacc_csendbuf, openacc_crecvbuf)
    endif

    if ( .false. ) write(*,*) openacc_tet_c2n, openacc_prz_c2n
    if ( .false. ) write(*,*) openacc_pyr_c2n, openacc_hex_c2n
    if ( .false. ) write(*,*) openacc_tet_c2e, openacc_prz_c2e
    if ( .false. ) write(*,*) openacc_pyr_c2e, openacc_hex_c2e
    if ( .false. ) write(*,*) openacc_fhelp, openacc_facespeed, openacc_eptr
    if ( .false. ) write(*,*) openacc_xn, openacc_yn, openacc_zn, openacc_ra
    if ( .false. ) write(*,*) openacc_weight
    if ( .false. ) write(*,*) openacc_recvproc, openacc_recvproc_offset
    if ( .false. ) write(*,*) openacc_sendproc, openacc_sendproc_offset
    if ( .false. ) write(*,*) openacc_recvindex, openacc_recvindex_offset
    if ( .false. ) write(*,*) openacc_sendindex, openacc_sendindex_offset
    if ( .false. ) write(*,*) openacc_recvbuf, openacc_sendbuf
    if ( .false. ) write(*,*) openacc_crecvbuf, openacc_csendbuf

  end subroutine openacc_enter_exit_data


!=============================== OPENACC_SETUP_CUDAMPI =======================80
!
!  Set up infrastructure for using CUDA-enabled MPI
!
!=============================================================================80
  subroutine openacc_setup_cudampi(soln)

    use solution_types, only : soln_type
    use lmpi,           only : lmpi_nproc
    use lmpi_app,       only : openacc_sendproc, openacc_recvproc,             &
                               openacc_sendindex, openacc_recvindex,           &
                               openacc_sendproc_offset,                        &
                               openacc_recvproc_offset,                        &
                               openacc_sendindex_offset,                       &
                               openacc_recvindex_offset,                       &
                               openacc_sendbuf, openacc_recvbuf,               &
                               openacc_csendbuf, openacc_crecvbuf
    use info_depr,      only : complex_mode

    type(soln_type), intent(in) :: soln

    integer :: i, j, max_send, max_recv, sendproc_size, recvproc_size, dm1, dm2
    integer :: sendind_size, recvind_size, sendproc_start, recvproc_start
    integer :: sendind_start, recvind_start, ncolor

  continue

    setup_for_cudampi : if ( use_cudampi ) then

! Find multicolor point group and determine largest required send
! and receive buffers across all of the colors

      max_send = -99
      max_recv = -99

      do i = 1, size(soln%eqn_groups,1)
       if ( soln%eqn_groups(i)%solve_type /= 'point-multicolor' ) cycle
       colors : do j = 1, soln%eqn_groups(i)%max_colored_sweeps
        max_send=max(max_send,soln%eqn_groups(i)%sr(j)%sendproc(lmpi_nproc+1)-1)
        max_recv=max(max_recv,soln%eqn_groups(i)%sr(j)%recvproc(lmpi_nproc+1)-1)
       end do colors
       exit
      end do

      allocate(openacc_sendbuf(size(soln%dq,1),max_send))
      allocate(openacc_recvbuf(size(soln%dq,1),max_recv))

      if ( complex_mode ) then
        allocate(openacc_csendbuf(size(soln%dq,1),max_send))
        allocate(openacc_crecvbuf(size(soln%dq,1),max_recv))
      else
        allocate(openacc_csendbuf(1,max_send))
        allocate(openacc_crecvbuf(1,max_recv))
      endif

! Now concatenate the sendproc, recvproc, sendindex, recvindex arrays from
! all colors into single arrays that the device can deal with.  Will need new
! auxiliary arrays to help us index into the new versions.

    sendproc_size = 0
    recvproc_size = 0
    sendind_size  = 0
    recvind_size  = 0

    do i = 1, size(soln%eqn_groups,1)
      if ( soln%eqn_groups(i)%solve_type /= 'point-multicolor' ) cycle
      do j = 1, soln%eqn_groups(i)%max_colored_sweeps
        sendproc_size=sendproc_size + size(soln%eqn_groups(i)%sr(j)%sendproc,1)
        recvproc_size=recvproc_size + size(soln%eqn_groups(i)%sr(j)%recvproc,1)
        sendind_size =sendind_size  + size(soln%eqn_groups(i)%sr(j)%sendindex,1)
        recvind_size =recvind_size  + size(soln%eqn_groups(i)%sr(j)%recvindex,1)
      end do
      ncolor = soln%eqn_groups(i)%max_colored_sweeps
      exit
    end do

    allocate(openacc_sendproc(sendproc_size))
    allocate(openacc_recvproc(recvproc_size))
    allocate(openacc_sendindex(sendind_size))
    allocate(openacc_recvindex(recvind_size))

    allocate(openacc_sendproc_offset(ncolor))
    allocate(openacc_recvproc_offset(ncolor))
    allocate(openacc_sendindex_offset(ncolor))
    allocate(openacc_recvindex_offset(ncolor))

    sendproc_start = 1
    recvproc_start = 1
    sendind_start  = 1
    recvind_start  = 1

    do i = 1, size(soln%eqn_groups,1)
      if ( soln%eqn_groups(i)%solve_type /= 'point-multicolor' ) cycle
      do j = 1, soln%eqn_groups(i)%max_colored_sweeps

        dm1 = size(soln%eqn_groups(i)%sr(j)%sendproc,1)
        dm2 = sendproc_start + dm1 - 1
        openacc_sendproc(sendproc_start:dm2) =                                 &
                                        soln%eqn_groups(i)%sr(j)%sendproc(1:dm1)
        openacc_sendproc_offset(j) = sendproc_start-1
        sendproc_start = dm2 + 1

        dm1 = size(soln%eqn_groups(i)%sr(j)%recvproc,1)
        dm2 = recvproc_start + dm1 - 1
        openacc_recvproc(recvproc_start:dm2) =                                 &
                                        soln%eqn_groups(i)%sr(j)%recvproc(1:dm1)
        openacc_recvproc_offset(j) = recvproc_start-1
        recvproc_start = dm2 + 1

        dm1 = size(soln%eqn_groups(i)%sr(j)%sendindex,1)
        dm2 = sendind_start + dm1 - 1
        openacc_sendindex(sendind_start:dm2) =                                 &
                                       soln%eqn_groups(i)%sr(j)%sendindex(1:dm1)
        openacc_sendindex_offset(j) = sendind_start-1
        sendind_start = dm2 + 1

        dm1 = size(soln%eqn_groups(i)%sr(j)%recvindex,1)
        dm2 = recvind_start + dm1 - 1
        openacc_recvindex(recvind_start:dm2) =                                 &
                                       soln%eqn_groups(i)%sr(j)%recvindex(1:dm1)
        openacc_recvindex_offset(j) = recvind_start-1
        recvind_start = dm2 + 1

      end do
      exit
    end do

    else setup_for_cudampi

      allocate(openacc_sendbuf(1,1))
      allocate(openacc_recvbuf(1,1))
      allocate(openacc_csendbuf(1,1))
      allocate(openacc_crecvbuf(1,1))

      allocate(openacc_sendproc(1))
      allocate(openacc_recvproc(1))
      allocate(openacc_sendindex(1))
      allocate(openacc_recvindex(1))

      allocate(openacc_sendproc_offset(1))
      allocate(openacc_recvproc_offset(1))
      allocate(openacc_sendindex_offset(1))
      allocate(openacc_recvindex_offset(1))

    endif setup_for_cudampi

  end subroutine openacc_setup_cudampi


!============================== OPENACC_COLOR_EDGES ==========================80
!
! Color edges into groups such that no edges in a given color share a node
!
!=============================================================================80
  subroutine openacc_color_edges(grid,crow,need_grid_velocity)

    use lmpi,          only : lmpi_die
    use allocations,   only : my_alloc_ptr, my_realloc_ptr
    use info_depr,     only : ebv_tets
    use grid_types,    only : grid_type
    use comprow_types, only : crow_flow
    use openacc_vars,  only : openacc_eptr, openacc_fhelp, openacc_xn,         &
                              openacc_yn, openacc_zn, openacc_ra,              &
                              openacc_weight, openacc_facespeed,               &
                              openacc_nedge_in_color, openacc_nedge_colors

    type(grid_type), intent(in) :: grid
    type(crow_flow), intent(in) :: crow
    logical,         intent(in) :: need_grid_velocity

    integer :: i, j, min_color, n1, n2, nedge_remaining, new_edges

    integer, dimension(grid%nedgeloc) :: edge_color
    integer, dimension(grid%nnodes01) :: node_tag

  continue

    openacc_nedge_colors = 0
    edge_color(:)        = 0
    nedge_remaining      = grid%nedgeloc

    call my_alloc_ptr(openacc_nedge_in_color,1)

    coloring : do

      if ( nedge_remaining == 0 ) exit coloring

      openacc_nedge_colors = openacc_nedge_colors + 1

      call my_realloc_ptr(openacc_nedge_in_color,openacc_nedge_colors)

      new_edges = 0

      node_tag(:) = 0

      search_edges : do i = 1, grid%nedgeloc

        if ( edge_color(i) /= 0 ) cycle search_edges

        n1 = grid%eptr(1,i)
        n2 = grid%eptr(2,i)

        if ( node_tag(n1) /= 0 .or. node_tag(n2) /= 0 ) cycle search_edges

        edge_color(i) = openacc_nedge_colors

        node_tag(n1) = 1
        node_tag(n2) = 1

        new_edges = new_edges + 1
        nedge_remaining = nedge_remaining - 1

      end do search_edges

      if ( new_edges == 0 .and. nedge_remaining > 0 ) then
        write(*,*) 'Error in finding edge in color...'
        call lmpi_die
        stop
      endif

      openacc_nedge_in_color(openacc_nedge_colors) = new_edges

    end do coloring

! Make sure everyone got colored

    min_color = minval(edge_color)

    if ( min_color /= 1 ) then
      write(*,*) 'Edge(s) not colored in color_edges...'
      call lmpi_die
      stop
    endif

    if ( sum(openacc_nedge_in_color) /= grid%nedgeloc ) then
      write(*,*) 'Incorrect number of colored edges...'
      call lmpi_die
      stop
    endif

    allocate(openacc_eptr(2,grid%nedge))
    allocate(openacc_fhelp(2,grid%nedgeloc))
    allocate(openacc_xn(grid%nedgeloc))
    allocate(openacc_yn(grid%nedgeloc))
    allocate(openacc_zn(grid%nedgeloc))
    allocate(openacc_ra(grid%nedgeloc))

    if ( ebv_tets ) then
      allocate(openacc_weight(10,grid%nedgeloc))
    else
      allocate(openacc_weight(1,1))
    endif

    if ( need_grid_velocity ) then
      allocate(openacc_facespeed(grid%nedgeloc))
    else
      allocate(openacc_facespeed(1))
    endif

    new_edges = 0

    do i = 1, openacc_nedge_colors
      do j = 1, grid%nedgeloc
        if ( edge_color(j) == i ) then
          new_edges = new_edges + 1
          openacc_eptr(:,new_edges)    = grid%eptr(:,j)
          openacc_fhelp(:,new_edges)   = crow%fhelp(:,j)
          openacc_xn(new_edges)        = grid%xn(j)
          openacc_yn(new_edges)        = grid%yn(j)
          openacc_zn(new_edges)        = grid%zn(j)
          openacc_ra(new_edges)        = grid%ra(j)
          if ( ebv_tets ) openacc_weight(:,new_edges)  = grid%weight(:,j)
          if (need_grid_velocity) openacc_facespeed(new_edges)=grid%facespeed(j)
        endif
      end do
    end do

  end subroutine openacc_color_edges


!============================== OPENACC_COLOR_CELLS ==========================80
!
! Color cells into groups such that no cells in a given color share a node
! Operates on any cell type based on the input data
!
!=============================================================================80
  subroutine openacc_color_cells(grid,ielem,ncell_colors,ncell_in_color,c2n,c2e)

    use lmpi,        only : lmpi_die
    use allocations, only : my_realloc_ptr
    use grid_types,  only : grid_type

    integer, intent(in)  :: ielem
    integer, intent(out) :: ncell_colors

    integer, dimension(:),   pointer :: ncell_in_color
    integer, dimension(:,:), pointer :: c2n, c2e

    type(grid_type), intent(in) :: grid

    integer :: i, j, min_color, ncell_remaining, new_cells, inode

    integer, dimension(grid%elem(ielem)%ncell) :: cell_color
    integer, dimension(grid%nnodes01) :: node_tag

  continue

    ncell_colors    = 0
    cell_color(:)   = 0
    ncell_remaining = grid%elem(ielem)%ncell

    coloring : do

      if ( ncell_remaining == 0 ) exit coloring

      ncell_colors = ncell_colors + 1

      call my_realloc_ptr(ncell_in_color,ncell_colors)

      new_cells = 0

      node_tag(:) = 0

      search_cells : do i = 1, grid%elem(ielem)%ncell

        if ( cell_color(i) /= 0 ) cycle search_cells

        do j = 1, grid%elem(ielem)%node_per_cell
          inode = grid%elem(ielem)%c2n(j,i)
          if ( node_tag(inode) /= 0 ) cycle search_cells
        end do

        cell_color(i) = ncell_colors

        do j = 1, grid%elem(ielem)%node_per_cell
          inode = grid%elem(ielem)%c2n(j,i)
          node_tag(inode) = 1
        end do

        new_cells = new_cells + 1
        ncell_remaining = ncell_remaining - 1

      end do search_cells

      if ( new_cells == 0 .and. ncell_remaining > 0 ) then
        write(*,*) 'Error in finding cell in color...'
        call lmpi_die
        stop
      endif

      ncell_in_color(ncell_colors) = new_cells

    end do coloring

! Make sure everyone got colored

    min_color = minval(cell_color)

    if ( min_color /= 1 ) then
      write(*,*) 'Cell(s) not colored in color_cells...'
      call lmpi_die
      stop
    endif

    if ( sum(ncell_in_color) /= grid%elem(ielem)%ncell ) then
      write(*,*) 'Incorrect number of colored cells...'
      call lmpi_die
      stop
    endif

    call my_realloc_ptr(c2n,grid%elem(ielem)%node_per_cell,                    &
                            grid%elem(ielem)%ncell)
    call my_realloc_ptr(c2e,grid%elem(ielem)%edge_per_cell,                    &
                            grid%elem(ielem)%ncell)

    new_cells = 0

    do i = 1, ncell_colors
      do j = 1, grid%elem(ielem)%ncell
        if ( cell_color(j) == i ) then
          new_cells = new_cells + 1
          c2n(:,new_cells) = grid%elem(ielem)%c2n(:,j)
          c2e(:,new_cells) = grid%elem(ielem)%c2e(:,j)
        endif
      end do
    end do

  end subroutine openacc_color_cells

end module openacc

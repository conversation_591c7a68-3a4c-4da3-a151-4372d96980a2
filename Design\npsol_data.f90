module npsol_data

  use kinddefs,     only : dp
  use design_types, only : max_string_length

  implicit none

  integer :: npsol_what_to_do
  integer :: npsol_io

  integer, dimension(:), pointer :: npsol_restart_flow
  integer, dimension(:), pointer :: npsol_restart_dual

  logical :: npsol_lss_flag
  logical :: npsol_restart_optimization

  character(len=max_string_length) :: npsol_ammo_directory
  character(len=max_string_length), dimension(:), pointer ::npsol_desc_directory

  real(dp), dimension(:),   pointer    :: c, d
  real(dp), dimension(:,:), pointer    :: last_flow_dvs
  real(dp), dimension(:,:), pointer    :: last_adjoint_dvs

  private
  public :: npsol_restart_flow, npsol_restart_dual, npsol_desc_directory
  public :: last_flow_dvs, last_adjoint_dvs, npsol_lss_flag
  public :: npsol_what_to_do, npsol_restart_optimization, npsol_io
  public :: c, d, npsol_ammo_directory

end module npsol_data

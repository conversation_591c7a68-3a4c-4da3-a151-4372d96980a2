module openacc_vars

  use kinddefs, only : dp, dqp, jp, odp

  implicit none

  private

! Handles for asynchronous data transfers

  integer, parameter, public :: residual_handle = 1
  integer, parameter, public :: aoff_handle     = 2
  integer, parameter, public :: adiag_handle    = 3
  integer, parameter, public :: temporal_handle = 4

! Pointers to derived type members

  integer, dimension(:),   pointer, public :: iam   => null()
  integer, dimension(:),   pointer, public :: jam   => null()
  integer, dimension(:),   pointer, public :: g2m   => null()
  integer, dimension(:,:), pointer, public :: fhelp => null()
  integer, dimension(:,:), pointer, public :: eptr  => null()

  real(dp),  dimension(:),     pointer, public :: x         => null()
  real(dp),  dimension(:),     pointer, public :: y         => null()
  real(dp),  dimension(:),     pointer, public :: z         => null()
  real(dp),  dimension(:),     pointer, public :: vol       => null()
  real(dp),  dimension(:),     pointer, public :: cdt       => null()
  real(dp),  dimension(:),     pointer, public :: amut      => null()
  real(dqp), dimension(:,:),   pointer, public :: dq        => null()
  real(dp),  dimension(:,:),   pointer, public :: res       => null()
  real(dp),  dimension(:,:),   pointer, public :: qdof      => null()
  real(dp),  dimension(:,:,:), pointer, public :: a_diag    => null()
  real(jp),  dimension(:,:,:), pointer, public :: a_diag_lu => null()
  real(odp), dimension(:,:,:), pointer, public :: a_off     => null()

! Colored edge and element data

  integer, public :: openacc_nedge_colors
  integer, public :: openacc_ntet_colors
  integer, public :: openacc_npyr_colors
  integer, public :: openacc_nprz_colors
  integer, public :: openacc_nhex_colors

  integer, dimension(:),   pointer, public :: openacc_nedge_in_color => null()
  integer, dimension(:),   pointer, public :: openacc_ntet_in_color  => null()
  integer, dimension(:),   pointer, public :: openacc_npyr_in_color  => null()
  integer, dimension(:),   pointer, public :: openacc_nprz_in_color  => null()
  integer, dimension(:),   pointer, public :: openacc_nhex_in_color  => null()
  integer, dimension(:,:), pointer, public :: openacc_eptr           => null()
  integer, dimension(:,:), pointer, public :: openacc_fhelp          => null()
  integer, dimension(:,:), pointer, public :: openacc_tet_c2n        => null()
  integer, dimension(:,:), pointer, public :: openacc_pyr_c2n        => null()
  integer, dimension(:,:), pointer, public :: openacc_prz_c2n        => null()
  integer, dimension(:,:), pointer, public :: openacc_hex_c2n        => null()
  integer, dimension(:,:), pointer, public :: openacc_tet_c2e        => null()
  integer, dimension(:,:), pointer, public :: openacc_pyr_c2e        => null()
  integer, dimension(:,:), pointer, public :: openacc_prz_c2e        => null()
  integer, dimension(:,:), pointer, public :: openacc_hex_c2e        => null()

  real(dp), dimension(:),   pointer, public :: openacc_xn        => null()
  real(dp), dimension(:),   pointer, public :: openacc_yn        => null()
  real(dp), dimension(:),   pointer, public :: openacc_zn        => null()
  real(dp), dimension(:),   pointer, public :: openacc_ra        => null()
  real(dp), dimension(:),   pointer, public :: openacc_facespeed => null()
  real(dp), dimension(:,:), pointer, public :: openacc_weight    => null()

end module openacc_vars

!===================================== SKIN_FRIC =============================80
!
!  Computes skin friction at a point on a viscous surface; here, negative skin
!  friction is assumed when the inner product of the skin friction vector and
!  the input reference velocity vector (typically the freestream velocity
!  vector) is negative; this is a slightly different aproach than in post_party
!
!=============================================================================80

  pure function skin_fric(u_ref, v_ref, w_ref, cfx, cfy, cfz)

    use kinddefs, only : dp

    real(dp), intent(in) :: u_ref, v_ref, w_ref, cfx, cfy, cfz

    real(dp) :: skin_fric, sgn

  continue

    sgn = 1.0_dp

    if ( (cfx*u_ref + cfy*v_ref + cfz*w_ref) < 0._dp ) sgn = -1.0_dp

    skin_fric = sgn*sqrt(cfx**2 + cfy**2 + cfz**2)

  end function skin_fric

!================================= LSQ_GRADC =================================80
!
! Cartesian gradients from scale, transform, and scale of local gradients.
!
!=============================================================================80

  pure function lsq_gradc( lsq_mrefs, lsq_grad, lc_max )

    use lsq_types,           only : lsq_ref_type

    type(lsq_ref_type),     intent(in) :: lsq_mrefs
    real(dp), dimension(3), intent(in) :: lsq_grad
    real(dp), dimension(4), intent(in) :: lc_max
    real(dp), dimension(3)             :: lsq_gradc

    real(dp), dimension(3) :: t

  continue

    !...account for scaling.
    t(1) = lsq_grad(1)/lc_max(1)
    t(2) = lsq_grad(2)/lc_max(2)
    t(3) = lsq_grad(3)/lc_max(3)

    !...Cartesian gradients.
    lsq_gradc(1) = ( lsq_mrefs%tr(1,1)*t(1) &
                   + lsq_mrefs%tr(2,1)*t(2) &
                   + lsq_mrefs%tr(3,1)*t(3) )*lsq_mrefs%scaleir
    lsq_gradc(2) = ( lsq_mrefs%tr(1,2)*t(1) &
                   + lsq_mrefs%tr(2,2)*t(2) &
                   + lsq_mrefs%tr(3,2)*t(3) )*lsq_mrefs%scaleir
    lsq_gradc(3) = ( lsq_mrefs%tr(1,3)*t(1) &
                   + lsq_mrefs%tr(2,3)*t(2) &
                   + lsq_mrefs%tr(3,3)*t(3) )*lsq_mrefs%scaleir

  end function lsq_gradc

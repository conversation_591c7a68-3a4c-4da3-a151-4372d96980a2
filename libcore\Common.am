libcore_SRCS = \
	adjoint_switches.f90 \
	allocate_gen.f90 \
	allocations.f90 \
	av2p0_lapack_util.f90 \
	av3p2_lapack_util.f90 \
	b_discrete_types.f90 \
	bc_names.f90 \
	bc_types.f90 \
	blas.f90 \
	complex_functions.f90 \
	comprow_types.f90 \
	convection_defs.f90 \
	c_utilities.F90 \
	ddt.f90 \
	debug_defs.f90 \
	debug_output.f90 \
	eigen_eispack.f90 \
	element_types.F90 \
	f2kcli.F90 \
	file_utils.f90 \
	fluid.f90 \
	force_types.f90 \
	fun3d_constants.f90 \
	fun3d_maximums.f90 \
	generic_gas_map.f90 \
	info_depr.f90 \
	interp_defs.f90 \
	invert_lapack.f90 \
	ivals.f90 \
	kinddefs.F90 \
	linear_algebra.f90 \
	lmpi.F90 \
	lmpi_app.F90 \
	logger.f90 \
	namelist_util.f90 \
	openacc_vars.f90 \
	physics_types.f90 \
	solution_types.f90 \
	sort.f90 \
	string_utils.f90 \
	system_extensions.F90 \
	utilities.f90 \
	versions.F90

if BUILD_MPI
AM_FCFLAGS = \
	$(FC_MODINC)@MPIINC@ \
	$(FC_MODINC)@top_builddir@
else
AM_FCFLAGS = \
	$(FC_MODINC)@top_builddir@
endif

# remove *.mod when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT)  mpif.h *.time *.mod *.fh *.d

libcore_f90s=$(libcore_SRCS:.F90=.f90)
libcore_deps=$(libcore_f90s:.f90=.d)

SUFFIXES = .d

BUILT_SOURCES = $(libcore_deps)

-include $(libcore_deps)
include $(top_srcdir)/make.rules

ordered_targets:
	@$(MAKE) clean
	@$(MAKE) -n | grep "^\$(FC)" | sed 's/.*-o /       /' | \
	                            sed 's/\.o .*/\.f90 \\/'

# Install Fortran module files alongside the library
lib_MODULES = $(libcore_f90s:.f90=.$(FC_MODEXT))

!================================= DQTF_NODE_AVG =============================80
!
! Linearizations of face (u,v,w,T) variables.
!
!=============================================================================80
  pure function dqtf_node_avg( flsq_n )

    integer, intent(in) :: flsq_n

    real(dp), dimension(flsq_n+2) :: dqtf_node_avg

  continue

    dqtf_node_avg(1:flsq_n) = 0._dp
    dqtf_node_avg(flsq_n+1) = 0.5_dp
    dqtf_node_avg(flsq_n+2) = 0.5_dp

  end function dqtf_node_avg

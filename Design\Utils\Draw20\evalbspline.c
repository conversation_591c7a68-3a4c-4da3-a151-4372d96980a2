#include <stdio.h>
#include <stdlib.h>
#include <math.h>
#ifndef __APPLE__
#include <malloc.h>
#endif

#define maxSegments 200
#define maxElements 50

typedef struct seg{
   int numberOfPoints;    /* Number of points on each segment */
   int noControl;         /* Number of control points for this segment */
   int maxcontrol;        /* Max number of control points for segment  */
   int type;              /* 0=inviscid 1=viscous 2=far-field          */
   double *x;             /* x-coordinate for each point      */
   double *y;             /* y-coordinate for each point      */
   double *tval;          /* t-parameter (local for each segment)      */
}SEGMENT;
extern SEGMENT segment[maxSegments];

typedef struct elems{
   int noKnots;           /* Number of knots in the fitted knot sequence          */
   int noControl;         /* Number of control points for the fitted geometry     */
   int noSegments;        /* Number of segments for each element                  */
   int firstSegment;      /* The number of the first segment defining the element */
   int lastSegment;       /* The number of the last segment defining the element  */
   int order;             /* Order of the B-spline                                */
   int numEval;           /* Number of points on B-spline surface                 */
   int fitted;            /* If zero this has not been previously fit             */
   int maxdegree;         /* Maximum degree of polynomial for element min(npts-1) */
   int itranx, itrany;    /* If we are translating the element                    */
   int irotate;           /* If we are rotating the element                       */
   int parameterization;  /* Type 1=uniform 2=chord 3=centripital                 */
   int initialNum;        /* If reading design.data we need to know number of pts */
   double xrot, yrot;     /* Point of rotation for each element                   */
   double *knots;         /* knots for the element                                */
   double *xcontrol;      /* Position of x-control points                         */
   double *ycontrol;      /* Position of y-control points                         */
   double *xupper;        /* Maximum x-position for control points                */
   double *xlower;        /* Minimum x-position for control points                */
   double *yupper;        /* Maximum y-position for control points                */
   double *ylower;        /* Minimum y-position for control points                */
   double *xfitted;       /* x-position of points on fitted surface               */
   double *yfitted;       /* y-position of points on fitted surface               */
   double *xcontrolO;     /* Saved position of x-control points                   */
   double *ycontrolO;     /* Saved Position of y-control points                   */
   double *uvalue;        /* parameter for evaluating spline                      */
   double *uRead;         /* Parameter read in from existing design.data file     */
}ELEMENT;

extern ELEMENT element[maxElements];
extern int whichelement;
extern int filetype; /* 0=Geometry, 1=design.data 2=pressure distribution */

void evalBSpline(void);
void getTvals(void);
void setTvals(void);
void setJoint(void);

void evalBSpline()
  {
  FILE *OUT;
  int i,j,k;
  int nknot;
  int ncontrol;
  double order;
  double *knot,*xcontrol,*ycontrol;
  int npts;
  int ndum;
  double xcoord,ycoord;
  double *uvals,*basis1,*basis2;

  npts = element[whichelement].numEval;
/*  printf("Number of points:\n"); kyle */
/*  scanf(" %d",&npts); kyle */

/*  IN = fopen("bspline.out","r"); */
/*  fscanf(IN," %d",&nknot); */
  nknot = element[whichelement].noKnots;
  knot   = (double *) calloc(nknot,sizeof(double));
  for (i=0; i < nknot; i++)
  {
/*     fscanf(IN," %g",&knot[i]); kyle */
     knot[i] = element[whichelement].knots[i];
  }
/*  fscanf(IN," %d",&ncontrol); kyle */
  ncontrol = element[whichelement].noControl;
  xcontrol = (double *) calloc(ncontrol,sizeof(double));
  ycontrol = (double *) calloc(ncontrol,sizeof(double));
  for (i=0; i < ncontrol; i++)
  {
/*     fscanf(IN," %g %g",&xcontrol[i],&ycontrol[i]); */
     xcontrol[i] = element[whichelement].xcontrol[i];
     ycontrol[i] = element[whichelement].ycontrol[i];
  }
/*  fclose(IN); kyle */

  uvals  = (double *) calloc(npts,sizeof(double));
  basis1 = (double *) calloc(ncontrol*npts,sizeof(double));
  basis2 = (double *) calloc(ncontrol*npts,sizeof(double));

  order = nknot - ncontrol + 1;

  /* Distribute points uniformly between knots 1 & nknot */
  for (i=0; i < npts; i++)
     uvals[i] = knot[0] + (double)i/(npts-1)*(knot[nknot-1]-knot[0]);

/* Fill the uvals array */
     if(filetype == 0)getTvals();
     if(filetype == 1)setTvals();

/* Set a point on the breaks */
     setJoint();

     for (i=0; i < npts; i++)
     uvals[i] = element[whichelement].uvalue[i];


  /* Compute the B-spline basis functions at each data point */
  /* Zeroth order functions first */
  for (i=0; i < npts; i++)
     {
     basis2[ncontrol*i] = 0.0;
     for (j=1; j < ncontrol; j++)
        {
        if (uvals[i] >= knot[j-1] && uvals[i] < knot[j])
           basis2[ncontrol*i+j] = 1.0;
        else basis2[ncontrol*i+j] = 0.0;
        }
     }
  /* Starting with zeroth order, build up to order */
  for (i=1; i <= order; i++)
     {
     /* First, copy basis2 to basis1 & clear basis2 */
     for (j=0; j < ncontrol*npts; j++)
        {
        basis1[j] = basis2[j];
        basis2[j] = 0.0;
        }
     for (j=0; j < npts; j++)
        {
        for (k=0; k < ncontrol; k++)
           {
           if (k-1 >= 0 && k+i-1 < nknot &&
               knot[k-1] != knot[k+i-1])
              basis2[ncontrol*j+k] += (uvals[j]-knot[k-1]) /
                    (knot[k+i-1]-knot[k-1]) * basis1[ncontrol*j+k];
           if (k+i < nknot && knot[k+i] != knot[k])
              basis2[ncontrol*j+k] += (knot[k+i]-uvals[j]) /
                    (knot[k+i]-knot[k]) * basis1[ncontrol*j+k+1];
           }
        }
     }
  /* Evaluate spline & output coordinates */
  OUT = fopen("coord.out","w");
  fprintf(OUT,"%d\n",3);
  fprintf(OUT,"i\n");
  fprintf(OUT,"x\n");
  fprintf(OUT,"y\n");
  fprintf(OUT,"%d\n",npts);
  fprintf(OUT,"%d %g %g\n",1,xcontrol[0],ycontrol[0]);
  ndum = segment[element[whichelement].lastSegment].numberOfPoints;
  element[whichelement].xfitted[0] = segment[element[whichelement].firstSegment].x[0];
  element[whichelement].yfitted[0] = segment[element[whichelement].firstSegment].y[0];
  element[whichelement].xfitted[npts-1] = segment[element[whichelement].lastSegment].x[ndum-1];
  element[whichelement].yfitted[npts-1] = segment[element[whichelement].lastSegment].y[ndum-1];
  for (i=1; i < npts-1; i++)
     {
     xcoord = 0.0;
     ycoord = 0.0;
     for (j=0; j < ncontrol; j++)
        {
        xcoord += xcontrol[j]*basis2[ncontrol*i+j];
        ycoord += ycontrol[j]*basis2[ncontrol*i+j];
        }
     fprintf(OUT,"%d %g %g\n",i+1,xcoord,ycoord);
       element[whichelement].xfitted[i] = xcoord;
       element[whichelement].yfitted[i] = ycoord;
     }
  fprintf(OUT,"%d %g %g\n",ncontrol,xcontrol[ncontrol-1],ycontrol[ncontrol-1]);
  fclose(OUT);
/* write control points to fort.21 (for kyle) */
  OUT = fopen("fort.21","w");
  fprintf(OUT,"%d\n",2);
  fprintf(OUT,"x\n");
  fprintf(OUT,"y\n");
  fprintf(OUT,"%d\n",ncontrol);
  for (i=0; i < ncontrol; i++)
     fprintf(OUT,"%g %g\n",xcontrol[i],ycontrol[i]);
  fclose(OUT);
  free((char *)basis2);
  free((char *)basis1);
  free((char *)uvals);
  free((char *)ycontrol);
  free((char *)xcontrol);
  free((char *)knot);
  }

/*=================================== GETTVALS =========================== */
/*                                                                         */
/* Get the parameter values for the element by assembling the              */
/* contributions from each segment                                         */
/*                                                                         */
/*======================================================================== */
void getTvals()
{
   int i,j;
   int numSegs;
   int nparam;
   int firstSeg, lastSeg;
   int npts;
   int icount;
   int iseg1;
   double tcurrent;
   double t1,t2;
   double s1,s2;
   double tlast;
   double *segT;

   numSegs = element[whichelement].noSegments;
   firstSeg = element[whichelement].firstSegment;
   lastSeg  = element[whichelement].lastSegment;
   npts     = element[whichelement].numEval;

/* Find out how many parameter values lie on the element         */
/* For the first segment, count all of them. This way, if there  */
/* if only one segment or if it is open, it will count correctly */
   nparam = 0;
   tlast  = 0.;
   for(i = 0; i<numSegs; i++)
   {
     if(i == 0)
     {
       nparam += segment[firstSeg + i].numberOfPoints;
       tlast  += segment[firstSeg + i].tval[segment[firstSeg + i].numberOfPoints - 1];
     }
     else
     {
       nparam += segment[firstSeg + i].numberOfPoints - 1;
       tlast  += segment[firstSeg + i].tval[segment[firstSeg + i].numberOfPoints - 1];
     }
   }

/* We need to fill the array of parameter vectors for the element */
/* Each segment starts at t=zero so lets just cat the parameter vector together */

     fcalloc(nparam, &segT);

/*     tlast = segment[lastSeg].tval[segment[lastSeg].numberOfPoints - 1]; */
     icount = 0;
     tcurrent = segment[firstSeg].tval[segment[firstSeg].numberOfPoints-1]; /* Use to keep a running total */
     for(i = 0; i<numSegs; i++)
     {
       if(i == 0)
       {
         for(j = 0; j<segment[firstSeg].numberOfPoints; j++)
         {
           segT[icount++] = segment[firstSeg].tval[j]/tlast;
         }
       }
       else
       {
         for(j = 1; j<segment[firstSeg + i].numberOfPoints; j++)
         {
           tcurrent += (segment[firstSeg + i].tval[j] - segment[firstSeg + i].tval[j-1]) ;
           segT[icount++] = tcurrent/tlast;
         }

       } 
     }

/* At this point, we have an array (segT) that has all the parameter values */
/* for all the points that describe the original geometry. What we need to  */
/* now is use this information to get a value of the parameter at each point */
/* where we want to actually evaluate the spline.                            */

/* First, reallocate uvalue for the element in case it is not the correct size */
      if((element[whichelement].uvalue = (double *)realloc((double *)element[whichelement].uvalue,
                                                   npts*sizeof(double))) == (double *)NULL)
      {
        printf("Reallocating uvalue in GETTVALS fails ");
        exit(3);
      }

/* Now loop over all the points on the element and interpolate the value of segT */

      iseg1 = 0;
      for(i = 0; i<npts; i++)
      {
        tcurrent = (double)i/(double)(npts - 1);
        for(j = iseg1; j<nparam-1; j++)
        {
          t1 = (double)j/(double)(nparam - 1);
          t2 = (double)(j + 1)/(double)(nparam - 1);
          if(tcurrent >= t1 && tcurrent <= t2)
          {
             s1 = segT[j];
             s2 = segT[j+1];
             element[whichelement].uvalue[i] = segT[j] + (s2 - s1)/(t2 - t1)*(tcurrent - t1);
             iseg1 = j;
             goto Loop;
          }
        }
Loop:;
       }

/* Free the locally allocated memory */
   free(segT);

}

/*=================================== SETTVALS =========================== */
/*                                                                         */
/* Set the parameter values for the element using the parameters           */
/* read in from design.data                                                */
/*                                                                         */
/*======================================================================== */
void setTvals()
{
   int i,j;
   int nparam;
   int npts;
   int iseg1;
   double tcurrent;
   double t1,t2;
   double s1,s2;

   nparam = element[whichelement].initialNum; /* Number of points read in design.data */
   npts   = element[whichelement].numEval;    /* Number of points desired             */

/* At this point, we have an array (segT) that has all the parameter values */
/* for all the points that describe the original geometry. What we need to  */
/* now is use this information to get a value of the parameter at each point */
/* where we want to actually evaluate the spline.                            */

/* First, reallocate uvalue for the element in case it is not the correct size */
      if((element[whichelement].uvalue = (double *)realloc((double *)element[whichelement].uvalue,
                                                   npts*sizeof(double))) == (double *)NULL)
      {
        printf("Reallocating uvalue in GETTVALS fails ");
        exit(3);
      }

/* Now loop over all the points on the element and interpolate the values read in from design.data */

      iseg1 = 0;
      for(i = 0; i<npts; i++)
      {
        tcurrent = (double)i/(double)(npts - 1);
        for(j = iseg1; j<nparam-1; j++)
        {
          t1 = (double)j/(double)(nparam - 1);
          t2 = (double)(j + 1)/(double)(nparam - 1);
          if(tcurrent >= t1 && tcurrent <= t2)
          {
             s1 = element[whichelement].uRead[j];
             s2 = element[whichelement].uRead[j+1];
             element[whichelement].uvalue[i] = s1 + (s2 - s1)/(t2 - t1)*(tcurrent - t1);
             iseg1 = j;
             goto Loop;
          }
        }
Loop:;
       }

}
/*=================================== SETJOINT =========================== */
/*                                                                         */
/* For each element, loop over the segments that make up that element      */
/* and find the uval that most closely matches the beginning tval for      */
/* the segment and move the uval to the corresponding tval. This should    */
/* preserve sharp corners instead of rounding over them if you don't       */
/* evaluate the spline at just the right place initially.                  */
/*                                                                         */
/*======================================================================== */
void setJoint()
{
   int i,j;
   int npts;
   int index, jstart;
   int nseg;
   int order;
   double tcurrent, tcompare;
   double t[maxSegments];
   double dist, distmax;

   npts     = element[whichelement].numEval;

/* Loop over the knots for the element and find the places where there is a break */
/* Save this location in the t array. */

/*        element[whichelement].order = element[whichelement].noKnots - element[whichelement].noControl + 1; */

        order = element[whichelement].order;
        nseg = 0;
        for(j=0; j<=element[whichelement].noKnots-element[whichelement].order; j++)
        {
           if(element[whichelement].knots[j] == element[whichelement].knots[j+order-1])
           {
             t[nseg] = element[whichelement].knots[j];
             nseg++;  
           }
        }

/* Now, we know how many breaks there are so lets look at the ones NOT at the ends */
/* and then find the uvalue for the element that matches this most closely. The    */
/* reason we wont look at the ends is because a point already gets forced there.   */

      jstart = 0;
      index  = 0;
      for(i = 1; i< nseg-1; i++)
      {
        tcompare = t[i];
        distmax = 100000.;
        for(j = jstart; j<npts; j++)
        {
          tcurrent = element[whichelement].uvalue[j];
/*
          if(tcurrent > tcompare)break;
*/
          dist = fabs(tcurrent - tcompare);
          if(dist <= distmax)
          {
            distmax = dist;
            index = j;
          }
          if(tcurrent > tcompare)break;
        }
/* Now, just set uvalue[index] to tcompare */
        jstart = index;
        element[whichelement].uvalue[index] = tcompare;
       }


}

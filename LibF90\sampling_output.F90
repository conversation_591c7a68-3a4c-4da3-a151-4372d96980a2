  module sampling_output

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,           only : dp, r4
  use fun3d_maximums,     only : max_geom
  use lmpi,               only : lmpi_master, lmpi_bcast,           lmpi_id,   &
                                 lmpi_nproc, lmpi_gatherv, lmpi_allgather,     &
                                 lmpi_reduce,                                  &
                                 lmpi_synchronize
  use info_depr,          only : skeleton

  implicit none

  private

  public :: survey_write
  public :: output_flowfield_sampling
  public :: end_fwh_write

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
  integer, dimension(:), allocatable, asynchronous :: fwh_bndry_points
  integer, dimension(:), allocatable, asynchronous :: fwh_output_fixed
  integer, dimension(:), allocatable, asynchronous :: fwh_data_period
#else
  integer, dimension(:), allocatable :: fwh_bndry_points
  integer, dimension(:), allocatable :: fwh_output_fixed
  integer, dimension(:), allocatable :: fwh_data_period
#endif

  integer, dimension(:), allocatable :: fwh_unit_g
  integer, dimension(:), allocatable :: fwh_unit_p

  type fwh_data_type
    real(r4), dimension(:,:), allocatable :: xyz
    real(r4), dimension(:,:), allocatable :: normal
    real(r4), dimension(:,:), allocatable :: solution
  end type fwh_data_type

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
  type(fwh_data_type), dimension(:), allocatable, asynchronous :: fwh_data
#else
  type(fwh_data_type), dimension(:), allocatable :: fwh_data
#endif

contains

!==================================== SURVEY_WRITE ===========================80
!
! Write flowfield data from user-specified surfaces - sphere, box, plane, etc
!
!=============================================================================80

  subroutine survey_write( grid, soln, time_step, sadj )

    use grid_types,          only : grid_type
    use solution_types,      only : soln_type
    use solution_adj,        only : sadj_type
    use nml_sampling_output, only : need_turb_variables_m=>need_turb_variables,&
                                    need_gradients_m=>need_gradients,          &
                                    need_slen_m=>need_slen,                    &
                                    n_output_variables_m=>n_output_variables,  &
                                    output_variables_m=>output_variables

    use string_utils,        only : split

    use sampling_headers,    only : number_of_geometries, init_template        &
                                  , need_turb_variables, n_output_variables    &
                                  , output_variables, need_gradients, plot     &
                                  , need_slen, variable_list, project_name     &
                                  , type_of_data, sample, asynchronous_fwh

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: time_step
    type(sadj_type), optional,       intent(in)    :: sadj

    integer                                  :: ivol, iu_g, iu_p
    logical                                  :: animate_choice
    logical                                  :: adjoint_mode

    integer                                  :: nn
    integer                                  :: num_out_var
    character(len=80), dimension(:), pointer :: out_var
    character(len=80)                        :: geo_name

  continue

! Make sure any asynchronous FWH output is done

    if ( asynchronous_fwh ) then
      if ( allocated(fwh_unit_g) .and. allocated(fwh_unit_p) ) then
        do ivol = 1, number_of_geometries
          if ( type_of_data(ivol) == 'volume' .and. plot(ivol) == 'fwh' ) then
            iu_g = fwh_unit_g(ivol)
            iu_p = fwh_unit_p(ivol)
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
            wait(iu_g)
            wait(iu_p)
#else
            if ( .false. ) write(*,*) iu_g, iu_p
#endif
          endif
        end do
      endif
    endif

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    geometry_count:  do ivol = 1, number_of_geometries

      geo_name       = sample(ivol)%geo_type_name

      animate_choice = animate_now( ivol, adjoint_mode )

      n_output_variables  = n_output_variables_m
      output_variables(:) = output_variables_m(:)
      need_turb_variables = need_turb_variables_m
      need_gradients      = need_gradients_m
      need_slen           = need_slen_m

      if ( soln%n_turb == 0 ) then
        need_turb_variables = .false.
      else
        need_turb_variables = .true.
      endif
      need_gradients      = .true.
      need_slen           = .true.

      if ( variable_list(ivol) /= '') then
        call split( variable_list(ivol), out_var )
        num_out_var = size(out_var)
        if ( skeleton > 2 ) then
          write(6,'(a,i5,a)') 'Variable list for ivol = ',ivol
          write(6,'(a,i5,a)') 'num_out_var=', num_out_var
          write(6,'(80(a,1x))') (trim(adjustl(out_var(nn))),nn=1,num_out_var)
          write(6,'(a,1l1)') 'animate_choice = ',animate_choice
        endif
        n_output_variables  = num_out_var
        do nn = 1, num_out_var
          output_variables(nn) = out_var(nn)
        enddo
        deallocate(out_var)
      endif

      animate_conditional:  if ( animate_choice ) then

      if ( ( geo_name == 'boundary_points') &
      .or. ( geo_name == 'volume_points'  ) ) then
        if ( skeleton > 5 ) &
        write(6,'(a,i5,a)') 'Call write_point_data, ivol = ',ivol

        call write_point_data( grid, soln, ivol, time_step, sadj )

      else if ( geo_name == 'line') then

        call write_line_data( grid, soln, ivol, time_step, sadj )

      else if ( geo_name == 'streamsurface') then

        call write_stream_data( grid, soln, ivol, time_step )

      else if ( geo_name == 'schlieren') then

        call write_schlieren_data( grid, soln, ivol, time_step )

      else

        select case ( type_of_data(ivol) )
        case ('boundary')
! Write out data associated with sampling of a boundary surface.

         call write_boundary_data( grid, soln, ivol, time_step, sadj )

        case ('volume')
! Write out data associated with sampling of the volume mesh.

          call write_volume_data( grid, soln, ivol, time_step, sadj )

        case ('integrated')
! Write out integrated quantities for data associated with sampling
! of the volume mesh.
        if ( skeleton > 5 ) &
        write(6,'(a,i5,a)') 'Call write_integrated_data, ivol = ',ivol

          call write_integrated_data( grid, soln, ivol, time_step, sadj )

        end select

      endif

      endif animate_conditional


      if ( init_template(ivol) ) then
        if (associated(sample(ivol)%edge_n2))then
          deallocate(sample(ivol)%edge_n2) ; nullify(sample(ivol)%edge_n2)
        end if
        if (associated(sample(ivol)%edge_n1))then
          deallocate(sample(ivol)%edge_n1) ; nullify(sample(ivol)%edge_n1)
        end if
        if (associated(sample(ivol)%edge_w2))then
          deallocate(sample(ivol)%edge_w2) ; nullify(sample(ivol)%edge_w2)
        end if
        if (associated(sample(ivol)%edge_w1))then
          deallocate(sample(ivol)%edge_w1) ; nullify(sample(ivol)%edge_w1)
        end if
        if (associated(sample(ivol)%edge_weight))then
          deallocate(sample(ivol)%edge_weight)
          nullify(sample(ivol)%edge_weight)
        end if
        if (associated(sample(ivol)%edge_list))then
          deallocate(sample(ivol)%edge_list)
          nullify(sample(ivol)%edge_list)
        end if
        if (associated(sample(ivol)%trinode_map))then
          deallocate(sample(ivol)%trinode_map)
          nullify(sample(ivol)%trinode_map)
        end if
      end if

    enddo geometry_count

! Now send any asynchronous FWH output to disk

    if ( asynchronous_fwh ) then
      do ivol = 1, number_of_geometries
        call fwh_surveyp_outputa( project_name, ivol )
      end do
    endif

  end subroutine survey_write

!========================= OUTPUT_FLOWFIELD_SAMPLING =========================80
!
! Logical function to determine whether a tecplot file containing solution
! data on one or more user-specied surfaces are to be output during the
! current time step
!
!=============================================================================80

  logical function output_flowfield_sampling(adjoint_mode)

    use sampling_headers, only : number_of_geometries

    logical,  intent(in) :: adjoint_mode

    integer :: ivol

  continue

    output_flowfield_sampling = .false.

    do ivol = 1, number_of_geometries
      if ( animate_now( ivol, adjoint_mode ) ) then
        output_flowfield_sampling = .true.
      endif
    enddo

  end function output_flowfield_sampling


!============================ ANIMATE_NOW ====================================80
!
! local logical function to determine whether a tecplot file containing solution
! data on one or more user-specied surfaces are to be output during the
! current time step
!
!=============================================================================80

  function animate_now( ivol, adjoint_mode ) result( animate )

    use info_depr,            only : ntt, simulation_time, physical_timestep,  &
                                     subiteration
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters
    use sampling_headers,    only : sampling_frequency
    use adjoint_switches,    only : new_krylov

    logical                :: animate
    integer,  intent(in)   :: ivol
    logical,  intent(in)   :: adjoint_mode

    integer :: iter
!   integer :: istop

  continue

    animate = .false.

! if using the outer krylov wrapper (in adjoint), must base decisions here
! on search direction

    if ( new_krylov ) then

      iter = subiteration + prior_iters
!     if ( ngrid > 1 ) iter = subiteration
      if ( adjoint_mode .and. itime /= 0 ) iter =  physical_timestep

      if ( sampling_frequency(ivol) /= 0 ) then
        if (iter/sampling_frequency(ivol)                                      &
            *abs(sampling_frequency(ivol))==iter) then
          animate = .true.
        end if
      end if

    else

      iter = ntt + prior_iters
      if ( adjoint_mode .and. itime /= 0 ) iter =  physical_timestep

      if  ( sampling_frequency(ivol) /= 0 ) then
        if (iter/sampling_frequency(ivol)                                      &
            *abs(sampling_frequency(ivol))==iter) then
          animate = .true.
        end if
      end if

    endif

!   to output initial state for unsteady cases

    if ( simulation_time <= 0._dp )  then
      if ( sampling_frequency(ivol) > 0 .and. itime /= 0 ) then
        animate = .true.
      end if
    end if

  end function animate_now


!================================= WRITE_BOUNDARY_DATA =======================80
!
! Write flowfield data from user-specified surfaces - sphere, box, plane, etc
!
!=============================================================================80

  subroutine write_boundary_data( grid, soln, ivol, time_step, sadj )

    use allocations,        only : my_alloc_ptr
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use solution_adj,       only : sadj_type
    use sampling_headers,   only : append_timestep                             &
                                 , n_output_variables, output_variables        &
                                 , data_p, global_node1, global_node2          &
                                 , q_master, edge_master                       &
                                 , project_name, label, verbose, sample        &
                                 , append_history
    use sampling_gather,    only : gather_edge_data
    use sampling_funclib,   only : collect_edges

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step
    type(sadj_type), optional,       intent(in)    :: sadj

    integer                               :: iostat
    integer                               :: n_edges
    integer                               :: total_survey_edges
    real(dp), dimension(:,:),     pointer :: edge_data

! total sampling data
    integer , dimension(:,:),     pointer :: local_node1
    integer , dimension(:,:),     pointer :: local_node2

    logical :: adjoint_mode

  continue

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    n_edges            = sample(ivol)%n_edges
    total_survey_edges = sample(ivol)%total_survey_edges

    if ( verbose ) then
    write(*,'(a,i12,a,i8)') 'write-total_survey_edges=',total_survey_edges &
             , ' for volume = ', ivol
    endif

    call my_alloc_ptr( edge_data  , n_output_variables, max(1,n_edges) )
    call my_alloc_ptr( local_node1, 1,                  max(1,n_edges) )
    call my_alloc_ptr( local_node2, 1,                  max(1,n_edges) )
    call gather_edge_data(grid, soln, n_edges, n_output_variables,             &
                          output_variables,                                    &
                          sample(ivol)%edge_n1,                                &
                          sample(ivol)%edge_n2,                                &
                          sample(ivol)%edge_w1,                                &
                          sample(ivol)%edge_w2,                                &
                          edge_data, local_node1, local_node2, sadj )

    allocate( data_p      ( n_output_variables, total_survey_edges )           &
                  , stat=iostat )
    if ( iostat /= 0 ) write(*,*) 'Allocate error data_p '
    allocate( global_node1( 3      , total_survey_edges ), stat=iostat )
    if ( iostat /= 0 ) write(*,*) 'Allocate error global_node1'
    allocate( global_node2( 3      , total_survey_edges ), stat=iostat )
    if ( iostat /= 0 ) write(*,*) 'Allocate error global_node2'

    data_p = 0.0_dp

!       gather

    call lmpi_gatherv(   edge_data, sample(ivol)%total_edges(lmpi_id+1),&
      data_p, sample(ivol)%total_edges )
    call lmpi_gatherv( local_node1, sample(ivol)%total_edges(lmpi_id+1),&
      global_node1, sample(ivol)%total_edges )
    call lmpi_gatherv( local_node2, sample(ivol)%total_edges(lmpi_id+1),&
      global_node2, sample(ivol)%total_edges )
    if ( lmpi_master ) then
      iostat = 0
      allocate (    q_master( n_output_variables, total_survey_edges )         &
                    , stat=iostat )

      if ( lmpi_master .and. iostat /= 0 ) &
         write(*,*) 'Allocate error q_master-write boundary data'

      iostat = 0
      allocate ( edge_master( total_survey_edges ) , stat=iostat )
      if ( iostat /= 0 ) &
         write(*,*) 'Allocate error edge_master-write boundary data'
!        write(*,'(i5,2(a,i10))') lmpi_id,',write-total_survey_edges=', &
!               total_survey_edges, ' for volume =', ivol
!        write(*,'(i5,2(a,i10))') lmpi_id,',write-size(edge_master)=', &
!               size(edge_master), ' for volume =', ivol

      call collect_edges ( total_survey_edges )

      call tecplot_edge_output(project_name, time_step,                        &
                                 ivol, n_output_variables,                     &
                                 output_variables, append_timestep(ivol),      &
                                 label(ivol), append_history(ivol),            &
                                 adjoint_mode )

      deallocate(edge_master)
      deallocate(   q_master)
    endif

    deallocate(global_node2)
    deallocate(global_node1)
    deallocate(data_p)
    deallocate(local_node2)
    deallocate(local_node1)
    deallocate(edge_data)

  end subroutine write_boundary_data

!============================ WRITE_VOLUME_DATA ==============================80
!
! Write flowfield data from user-specified surfaces - sphere, box, plane, etc
!
!=============================================================================80

  subroutine write_volume_data( grid, soln, ivol, time_step, sadj )

    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : itime
    use allocations,          only : my_alloc_ptr
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use sampling_headers,     only : append_timestep, n_output_variables       &
                                   , output_variables, plot, label             &
                                   , project_name, sample                      &
                                   , append_history, asynchronous_fwh

    use sampling_funclib,     only : get_avg_data
    use sampling_gather,      only : gather_node_data
    use kinddefs,             only : r4

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step
    type(sadj_type), optional,       intent(in)    :: sadj

    integer                               :: n_tria
    integer                               :: nnodes

    real(dp), dimension(:,:),     pointer :: edge_data
    real(r4), dimension(:,:), allocatable :: data_avg

    character(len=80)                     :: geo_name

    logical :: adjoint_mode

  continue

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    geo_name = sample(ivol)%geo_type_name

!-----------------------------------------------------------------------------80
!  edge based
!-----------------------------------------------------------------------------80
    n_tria             = sample(ivol)%n_tria
    nnodes             = sample(ivol)%n_edges

    nullify(edge_data)
    call my_alloc_ptr( edge_data  , n_output_variables, max(1,nnodes) )

    call gather_node_data(grid, soln, nnodes, n_output_variables,              &
                          output_variables,                                    &
                          sample(ivol)%edge_list,                              &
                          sample(ivol)%edge_weight,                            &
                          edge_data, sadj )

!-----------------------------------------------------------------------------80
!       output the sampled data in either tecplot or fieldview format
! 29July2010 - partition based writes work only with tecplot currently
!-----------------------------------------------------------------------------80
    select case ( plot(ivol) )

      case ( 'tecplot' )

        call tecplot_surveyp_output( geo_name, project_name, time_step, ivol,  &
                               n_output_variables, output_variables,           &
                               append_timestep(ivol),                          &
                               nnodes, n_tria, sample(ivol)%trinode_map,       &
                               edge_data, label(ivol)                          &
                              , append_history(ivol), adjoint_mode )

      case ( 'fwh' )

        allocate ( data_avg(n_output_variables, n_tria) )

        data_avg = get_avg_data( n_tria, nnodes, edge_data                     &
                   , sample(ivol)%trinode_map, sample(ivol)%window_normal)

        if ( asynchronous_fwh ) then
          call fwh_surveyp_gather( ivol, n_output_variables, output_variables, &
                                   n_tria, data_avg )
        else
          call fwh_surveyp_output( project_name,            ivol,              &
                                   n_output_variables, output_variables,       &
                                   n_tria, data_avg )
        endif

        deallocate ( data_avg )


!       case ( 'fieldview' )

!         call fieldview_survey_output( cell_count_master )

    end select

    lmpi_master_2: if ( lmpi_master ) then
      if ( adjoint_mode .and. itime /= 0 ) then
        write(*,'(2x,2(a,i0),2a)')                                             &
                  'Time step: ',physical_timestep,                             &
          ', Geometry number: ',ivol,                                          &
                     ', Type: ',trim(geo_name)
      else
        write(*,'(2x,2(a,i0),2a)')                                             &
                  'Time step: ',time_step,                                     &
          ', Geometry number: ',ivol,                                          &
                     ', Type: ',trim(geo_name)
      end if

    end if lmpi_master_2

    deallocate ( edge_data )
    nullify ( edge_data )

  end subroutine write_volume_data

!========================== WRITE_INTEGRATED_DATA ============================80
!
! Write averaged flowfield and force data from user-specified surfaces
!
!=============================================================================80

  subroutine write_integrated_data( grid, soln, ivol, time_step, sadj )

    use allocations,          only : my_alloc_ptr
    use component_performance, only : loop_over_triangles
    use fluid,                only : gamma
    use info_depr,            only : physical_timestep
    use ivals,                only : p0
    use grid_types,           only : grid_type
    use nml_nonlinear_solves, only : itime
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use sampling_gather,      only : gather_node_data
    use sampling_headers,     only : sample, n_output_variables,               &
                                     output_variables
!   use sampling_headers,     only : append_timestep, n_output_variables       &
!                                  , output_variables, plot, label             &
!                                  , project_name                              &
!                                  , append_history, asynchronous_fwh
    use string_utils,         only : int_to_s


    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step
    type(sadj_type), optional,       intent(in)    :: sadj

    integer                               :: n_tria
    integer                               :: nnodes
    integer                               :: total_triangles

    real(dp), dimension(:,:),     pointer :: edge_data
    real(dp), dimension(:,:),     pointer :: tri1_data
    real(dp), dimension(:,:),     pointer :: tri2_data
    real(dp), dimension(:,:),     pointer :: tri3_data
    real(dp), dimension(:,:), allocatable :: data_p1
    real(dp), dimension(:,:), allocatable :: data_p2
    real(dp), dimension(:,:), allocatable :: data_p3
    integer,  dimension(:),   allocatable :: total_trias

    character(len=80)                     :: geo_name

    logical :: adjoint_mode

    real(dp), dimension(3) :: n
    real(dp)               :: temperature, a, mach
    real(dp)               :: u_avg, v_avg, w_avg
    real(dp)               :: rho, u, v, w, p
    integer                :: nt, node1, node2, node3

    real(dp) :: area_patch
    real(dp) :: rho_avg
    real(dp) :: p_avg
    real(dp) :: pt_avg
    real(dp) :: massflow
    real(dp), dimension(3) :: mom_f
    real(dp), dimension(3) :: del_p

  continue

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    geo_name = sample(ivol)%geo_type_name

    n_tria             = sample(ivol)%n_tria
    nnodes             = sample(ivol)%n_edges

    nullify( edge_data )

    call my_alloc_ptr( edge_data  , n_output_variables, max(1,nnodes) )

    call gather_node_data(grid, soln, nnodes, n_output_variables,              &
                          output_variables,                                    &
                          sample(ivol)%edge_list,                              &
                          sample(ivol)%edge_weight,                            &
                          edge_data, sadj )

!-----------------------------------------------------------------------------80
!     Figure out total number of triangles in sample from all partitions and
!     gather in one place.  If the mesh is sufficiently mesh, and the sampled
!     surface is sufficiently large, working the averaging on the master node
!     may prove untenable.

    nullify( tri1_data )
    nullify( tri2_data )
    nullify( tri3_data )

    call my_alloc_ptr( tri1_data, 8, n_tria )
    call my_alloc_ptr( tri2_data, 8, n_tria )
    call my_alloc_ptr( tri3_data, 8, n_tria )

    do nt = 1, n_tria
      node1   = sample(ivol)%trinode_map(1,nt)
      node2   = sample(ivol)%trinode_map(2,nt)
      node3   = sample(ivol)%trinode_map(3,nt)
      tri1_data(1:8,nt) = edge_data(1:8,node1)
      tri2_data(1:8,nt) = edge_data(1:8,node2)
      tri3_data(1:8,nt) = edge_data(1:8,node3)
    end do

      allocate(total_trias(lmpi_nproc)); total_trias = 0
      call lmpi_allgather(n_tria,total_trias)
      total_triangles = sum(total_trias) ! total triangles in sampling

      if ( skeleton > 1 ) then
        write(*,*) 'data_dumping,id=',lmpi_id, size(tri1_data),n_tria
        if ( lmpi_master ) then
          write(*,'(a,128i6)') 'total_trias    =',total_trias
          write(*,*) 'total_triangles=',total_triangles
        endif
      endif

!     allocate an array to gather all the partition cuts into

      allocate(data_p1(8,total_triangles))
      allocate(data_p2(8,total_triangles))
      allocate(data_p3(8,total_triangles))

      data_p1 = 0.0_dp
      data_p2 = 0.0_dp
      data_p3 = 0.0_dp
!     gather
      call lmpi_gatherv(tri1_data, total_trias(lmpi_id+1), data_p1, total_trias)
      call lmpi_gatherv(tri2_data, total_trias(lmpi_id+1), data_p2, total_trias)
      call lmpi_gatherv(tri3_data, total_trias(lmpi_id+1), data_p3, total_trias)
!
!-----------------------------------------------------------------------------80
!   Call integration routine

    if ( lmpi_master ) then
        n = sample(ivol)%window_normal
        call loop_over_triangles( total_triangles,                             &
                                  data_p1, data_p2, data_p3,                   &
                                  p0, n, area_patch,                           &
                                  rho_avg, u_avg, v_avg, w_avg, p_avg, pt_avg, &
                                  massflow, mom_f, del_p )
        if ( skeleton > 1 ) then
          write(*,*) 'loop over triangles,',total_triangles
          write(*,'(a,1x,es12.5)')    'area_patch =',area_patch
          write(*,'(a,1x,es12.5)')    'rho avg    =',rho_avg/area_patch
          write(*,'(a,1x,es12.5)')    'pt avg     =',pt_avg/area_patch
          write(*,'(a,1x,es12.5)')    'massflow   =',massflow
          write(*,'(a,3(1x,es12.5))') 'mom_f      =',mom_f
          write(*,'(a,3(1x,es12.5))') 'del_p      =',del_p
          write(*,*)
        end if

        rho = rho_avg / area_patch
        u   = u_avg / area_patch
        v   = v_avg / area_patch
        w   = w_avg / area_patch
        p   = p_avg / area_patch

     temperature   = gamma * p / rho
     a             = sqrt( temperature )
     mach          = abs(u) / a

     if ( adjoint_mode .and. itime /= 0 ) then
          write(*,'(2x,2(a,i0),4a)')                                           &
                   'Time step: ',physical_timestep,                            &
            ' Geometry number: ',ivol,                                         &
                      ', Type: ',trim(geo_name),                               &
       ', Triangles integrated: ',int_to_s(total_triangles,placeholders=.true.)
     else
          write(*,'(2x,2(a,i0),4a)')                                           &
                   'Time step: ',time_step,                                    &
            ' Geometry number: ',ivol,                                         &
                      ', Type: ',trim(geo_name),                               &
       ', Triangles integrated: ',int_to_s(total_triangles,placeholders=.true.)
     end if

     write(6,'(a,10(1x,f15.5))') 'rho  average:   ', rho
     write(6,'(a,10(1x,f15.5))') 'u    average:   ', u
     write(6,'(a,10(1x,f15.5))') 'v    average:   ', v
     write(6,'(a,10(1x,f15.5))') 'w    average:   ', w
     write(6,'(a,10(1x,f15.5))') 'p    average:   ', p
     write(6,'(a,10(1x,f15.5))') 'Mach average:   ', mach
     write(6,'(a,10(1x,es15.5))') 'mass flow:      ', massflow
     write(6,'(a,10(1x,es15.5))') 'patch area:     ', area_patch

    endif

    deallocate( data_p3 )
    deallocate( data_p2 )
    deallocate( data_p1 )
    deallocate( total_trias )

    deallocate ( tri3_data )
    deallocate ( tri2_data )
    deallocate ( tri1_data )
    deallocate ( edge_data )

    nullify ( tri3_data )
    nullify ( tri2_data )
    nullify ( tri1_data )
    nullify ( edge_data )

  end subroutine write_integrated_data

!============================ WRITE_POINT_DATA ===============================80
!
! Write flowfield data from user-specified points
!
!=============================================================================80
  subroutine write_point_data( grid, soln, ivol, time_step, sadj )

    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : itime

    use string_utils,         only : int_to_s
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use sampling_headers,     only : append_timestep, number_of_points         &
                                   , n_output_variables                        &
                                   , output_variables, point_is_found_global   &
                                   , verbose, edge_master, q_master            &
                                   , edge_count_master, plot, label            &
                                   , project_name, sample
    use sampling_gather,      only : gather_point_data
    use solution_adj,         only : sadj_type

    use sampling_headers,     only : p2c_local_elem                            &
                                   , p2c_local_bdy

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    type(sadj_type), optional,       intent(in)    :: sadj
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step

    integer                               :: iostat
    integer                               :: n_points
    integer                               :: n_dim
    integer                               :: total_points
    integer                               :: i, nn
    integer                               :: ipts
    real(dp), dimension(:,:), allocatable :: local_data
    real(dp), dimension(:,:), allocatable :: global_data
    integer,  dimension(:  ), allocatable :: total_point_vector

    character(len=80)                   :: var
    character(len=80)                   :: geo_name

    real(dp) :: dist2
    real(dp), dimension(3)                :: p1
    integer :: best, processor
    real(dp), dimension(lmpi_nproc) :: all_dist2

    logical :: adjoint_mode

!    integer, dimension(:), allocatable :: p2c_elem, global_elem
!    integer, dimension(:), allocatable :: p2c_cells, global_cells
!    integer, dimension(:), allocatable :: total_point_vector


  continue

    adjoint_mode = .false.
    if ( present(sadj) ) adjoint_mode = .true.

    geo_name = sample(ivol)%geo_type_name

    n_points             = number_of_points(ivol)
    allocate(total_point_vector(lmpi_nproc)); total_point_vector = 0
    call lmpi_allgather(n_points, total_point_vector)
    total_points = sum(total_point_vector)
    call lmpi_bcast( total_points )

    if ( sample(ivol)%geo_type_name == 'line' ) then

      if ( skeleton > 1 ) then
        write(*,'(a,128i8)') 'local point hits=',total_point_vector
        write(*,*) 'total point hits   =',total_points
      endif
      n_dim = total_points

    else
      n_dim = number_of_points(ivol)
    endif

    if ( skeleton > 5 ) then
      write(*,'(a,128i8)') 'ivol            =',ivol
      write(*,'(a,128i8)') 'number of points=',number_of_points(ivol)
      write(*,*)           'total point hits=',total_points
    endif

    allocate ( local_data  ( n_output_variables, n_dim) )
    allocate ( global_data ( n_output_variables, n_dim) )
    local_data  = 0.0_dp
    global_data = 0.0_dp

    call gather_point_data(grid, soln, ivol, n_output_variables,       &
                           output_variables, local_data, sadj )


    geo_data_type:  if ( sample(ivol)%geo_type_name == 'line' ) then
      call lmpi_gatherv( local_data, total_point_vector(lmpi_id+1), &
        global_data, total_point_vector )
    else if ( sample(ivol)%geo_type_name == 'boundary_points' ) then

      do ipts = 1, number_of_points(ivol)
        dist2 = huge(1.0_dp)

        if ( 0 /= p2c_local_bdy(ivol,ipts) ) then
          if ( 0 /= grid%bc(p2c_local_bdy(ivol,ipts))                          &
                        %f2ntb(p2c_local_elem(ivol,ipts),1) ) then
          ! tris
            p1(1) = grid%x(grid%bc(p2c_local_bdy(ivol,ipts))%ibnode( &
     grid%bc(p2c_local_bdy(ivol,ipts))%f2ntb(p2c_local_elem(ivol,ipts),1)))
            p1(2) = grid%y(grid%bc(p2c_local_bdy(ivol,ipts))%ibnode( &
     grid%bc(p2c_local_bdy(ivol,ipts))%f2ntb(p2c_local_elem(ivol,ipts),1)))
            p1(3) = grid%z(grid%bc(p2c_local_bdy(ivol,ipts))%ibnode( &
     grid%bc(p2c_local_bdy(ivol,ipts))%f2ntb(p2c_local_elem(ivol,ipts),1)))
            dist2 = dot_product( p1 - sample(ivol)%point_list(1:3,ipts), &
                                 p1 - sample(ivol)%point_list(1:3,ipts) )
          else
          ! quads
            p1(1) = grid%x(grid%bc(p2c_local_bdy(ivol,ipts))%ibnode( &
     grid%bc(p2c_local_bdy(ivol,ipts))%f2nqb(p2c_local_elem(ivol,ipts),1)))
            p1(2) = grid%y(grid%bc(p2c_local_bdy(ivol,ipts))%ibnode( &
     grid%bc(p2c_local_bdy(ivol,ipts))%f2nqb(p2c_local_elem(ivol,ipts),1)))
            p1(3) = grid%z(grid%bc(p2c_local_bdy(ivol,ipts))%ibnode( &
     grid%bc(p2c_local_bdy(ivol,ipts))%f2nqb(p2c_local_elem(ivol,ipts),1)))
            dist2 = dot_product( p1 - sample(ivol)%point_list(1:3,ipts), &
                                 p1 - sample(ivol)%point_list(1:3,ipts) )
          end if
        end if

        call lmpi_allgather(dist2,all_dist2)
        if (lmpi_master) then
          best = 1
          do processor = 2,lmpi_nproc
            if ( all_dist2(processor) < all_dist2(best) ) best = processor
          end do
        end if
        call lmpi_bcast(best)
        global_data(:,ipts) = local_data(:,ipts)
        call lmpi_bcast(global_data(:,ipts),best-1)
      end do
    else
      call lmpi_reduce( local_data, global_data )
    endif geo_data_type

    call lmpi_bcast( global_data )

    if ( lmpi_master ) then
      do ipts = 1, number_of_points(ivol)
        if ( point_is_found_global(ivol,ipts)/= 0 ) cycle
!       write(6,'(15x,a)') 'Resetting lost point data'
        do nn = 1, n_output_variables
          var = trim(adjustl(output_variables(nn)))
          select case ( var )
          case('x')
            global_data(nn,ipts) = sample(ivol)%print_list(1,ipts)
          case('y')
            global_data(nn,ipts) = sample(ivol)%print_list(2,ipts)
          case('z')
            global_data(nn,ipts) = sample(ivol)%print_list(3,ipts)
          case('rho','p','temp','rho_tavg','p_tavg')
            global_data(nn,ipts) = 999.0
          case default
            global_data(nn,ipts) = -999.0
          end select
        end do
      end do
    endif

    if ( lmpi_master ) then
      if ( verbose ) write(*,*) 'Allocate edge_master-write_point_data',n_dim
      allocate ( edge_master( n_dim ) , stat=iostat )
      if ( iostat /= 0 ) &
        write(*,*) 'Allocate error edge_master-write_point_data'
      allocate( q_master( n_output_variables, n_dim ), stat=iostat )
      if ( iostat /= 0 ) write(*,*) 'Allocate error q_master-write_point_data'
        edge_count_master = 0

        do i = 1, n_dim
          edge_count_master             = edge_count_master + 1
          edge_master(i)                = edge_count_master
          q_master(:,edge_count_master) = global_data(:,i)
      enddo
    endif

    deallocate( total_point_vector )
    deallocate( local_data )
    deallocate( global_data )
!-----------------------------------------------------------------------------80
!       output the sampled data in either tecplot or fieldview format
! 29July2010 - partition based writes work only with tecplot currently
!-----------------------------------------------------------------------------80
      select case ( plot(ivol) )

        case ( 'tecplot' )

          call tecplot_point_output( project_name, time_step, ivol,            &
                                 n_output_variables, output_variables,         &
                                 append_timestep(ivol), label(ivol),           &
                                 adjoint_mode )

!       case ( 'fieldview' )

!         call fieldview_survey_output( cell_count_master )

        case ( 'serial_history' )

          call serial_history_point_output( project_name, time_step            &
                                 , ivol, n_output_variables, label(ivol) )

      end select

!-----------------------------------------------------------------------------80
      lmpi_master_2: if ( lmpi_master ) then
        if ( adjoint_mode .and. itime /= 0 ) then
          write(*,'(2x,2(a,i0),4a)')                                           &
                   'Time step: ',physical_timestep,                            &
            ' Geometry number: ',ivol,                                         &
                      ', Type: ',trim(geo_name),                               &
              ', Nodes output: ',int_to_s(total_points,placeholders=.true.)
        else
          write(*,'(2x,2(a,i0),4a)')                                           &
                   'Time step: ',time_step,                                    &
            ' Geometry number: ',ivol,                                         &
                      ', Type: ',trim(geo_name),                               &
              ', Nodes output: ',int_to_s(total_points,placeholders=.true.)
        end if
      end if lmpi_master_2

    if ( lmpi_master ) then
      deallocate(edge_master)
      deallocate(   q_master)
    endif

  end subroutine write_point_data

!============================ WRITE_STREAM_DATA ==============================80
!
! Write flowfield data from user-specified points
!
!=============================================================================80
  subroutine write_stream_data( grid, soln, ivol, time_step )

    use file_utils,         only : available_unit
    use grid_types,         only : grid_type
    use element_defs,       only : node_per_hex
    use solution_types,     only : soln_type
    use sampling_headers,   only : number_of_points                            &
                                 , p2c_local_cells, p2c_local_elem             &
                                 , n_output_variables                          &
                                 , verbose, edge_master, q_master              &
                                 , edge_count_master, sample
!   use sampling_gather,    only : gather_point_data
    use sampling_funclib,   only : getMass
    use geometry_utils,     only : get_area_normal, triangle_bary

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step

    integer                               :: iostat
    integer                               :: n_points
    integer                               :: n_dim
    integer                               :: total_points
    integer                               :: i
    integer                               :: ipts
    integer                               :: c2n_single(node_per_hex)
    real(dp), dimension(:,:), allocatable :: local_data
    real(dp), dimension(:,:), allocatable :: global_data
    integer,  dimension(:  ), allocatable :: total_point_vector

!   character(len=80)                   :: var
    character(len=80)                   :: geo_name

    real(dp), dimension(4)              :: f, g, m
    real(dp), dimension(3)              :: p, p1, p2, p3, lambda, n
    real(dp), dimension(:), allocatable :: mass
    integer,  dimension(3)              :: node
    real(dp) :: dummy

    integer :: element_set, cell, face
    integer :: face_per_cell
    integer :: node_per_cell
    integer :: j, k, l
    integer :: iunit
    real(dp) :: g12, g13, g23, g34, g41, g42
    real(dp) :: neg_mass, pos_mass

  continue


!   element_type_count: do element_set = 1, grid%nelem
!     type_cell     =  grid%elem(element_set)%type_cell
!     if ( type_cell == 'tet') then
!       ntet = grid%elem(element_set)%ncellg
!       number_of_faces = 4
!      allocate( c2f(number_of_faces, ntet) )
!     endif
!       call cell2face( grid%elem(element_set)%c2n, c2f, ntet, grid%nnodesg )
!     endif
!   enddo element_type_count
    iunit = available_unit()
    open(iunit,file='fg.dat')
    write(iunit,'(a)') 'title="fg"'
    write(iunit,'(a)') 'variables=f,g'
    write(iunit,'(a)') 'zone,nodes=4,elements=1,f=fepoint,et=tetrahedron'

    geo_name = sample(ivol)%geo_type_name

    n_points             = number_of_points(ivol)
    allocate(total_point_vector(lmpi_nproc)); total_point_vector = 0
    call lmpi_allgather(n_points, total_point_vector)
    total_points = sum(total_point_vector)
    call lmpi_bcast( total_points )
    n_dim = n_points

    loop_ipts: do ipts = 1, number_of_points(ivol)

      neg_mass = 0.0_dp
      pos_mass = 0.0_dp
      f        = 0.0_dp
      g        = 0.0_dp
      cell     = p2c_local_cells(ivol,ipts)

      loop_cell_faces:  if ( cell /= 0 ) then
write(6,'(i5,a,es15.5)') lmpi_id,'geo',time_step
write(6,'(i5,a,a)') lmpi_id,'geo',geo_name
write(6,'(i5,a,8i10)') lmpi_id,'points',ivol,ipts &
        , p2c_local_cells(ivol,ipts) , p2c_local_elem(ivol,ipts)

        element_set   = p2c_local_elem(ivol,ipts)
        node_per_cell = grid%elem(element_set)%node_per_cell
        face_per_cell =  grid%elem(element_set)%face_per_cell

        allocate( mass(face_per_cell) )

              c2n_single = 0
              c2n_single(1:node_per_cell) =                           &
                grid%elem(element_set)%c2n(1:node_per_cell,cell)
              mass = getMass ( face_per_cell, node_per_cell           &
                   , soln%q_dof, grid%x, grid%y, grid%z               &
                   , c2n_single, soln%n_tot, grid%nnodesg)

             do face = 1, face_per_cell
               if ( mass(face) < 0.0_dp ) neg_mass = neg_mass + mass(face)
               if ( mass(face) > 0.0_dp ) pos_mass = pos_mass + mass(face)
             enddo
write(6,'(a,12(1x,f20.10))') 'pos_mass ',pos_mass
write(6,'(a,12(1x,f20.10))') 'ratio_mass ',pos_mass/neg_mass

              if ( mass(1) /= 0.0_dp  &
             .and. mass(2) /= 0.0_dp  &
             .and. mass(3) /= 0.0_dp  &
             .and. mass(4) /= 0.0_dp  &
                 ) then
              m(1) = mass(1)/pos_mass
              m(2) = mass(2)/pos_mass
              m(3) = mass(3)/pos_mass
              m(4) = mass(4)/pos_mass

!             if ( m(4) > 0.0_dp ) then
!               m = -m
!             endif
write(6,'(a,12(1x,f20.10))') 'm',m,sum(m)
              f(1) = 0.0_dp;g(1)= 0.0_dp
              f(2) = 1.0_dp;g(2)= 0.0_dp
              f(3) = 0.0_dp;g(3)=-2.0_dp*m(4)
              f(4) =  -m(2)/m(4)
              g(4) = 2.0_dp*m(3)

write(6,'(i5,a,12(1x,es15.5))') lmpi_id,'stream-f',f
write(6,'(i5,a,12(1x,es15.5))') lmpi_id,'stream-g',g
    write(iunit,*) f(1), g(1)
    write(iunit,*) f(2), g(2)
    write(iunit,*) f(3), g(3)
    write(iunit,*) f(4), g(4)
    write(iunit,*) '1 2 3 4'

dummy = 0.0_dp
p(1)=0.01; p(2)=0.01; p(3)=0.0
             do face = 1, face_per_cell
   node(1:3) = grid%elem(element_set)%local_f2n(face,1:3)
   p1(1)=f(node(1)); p1(2)=g(node(1)); p1(3)=0.0
   p2(1)=f(node(2)); p2(2)=g(node(2)); p2(3)=0.0
   p3(1)=f(node(3)); p3(2)=g(node(3)); p3(3)=0.0
   n = get_area_normal(p1,p2,p3)
                lambda = triangle_bary ( p, p1, p2, p3 )
write(6,'(i5,a,i5,12(1x,f15.5))') lmpi_id,'lambda  ',face, lambda
dummy = dummy+n(3)
              enddo
              else
              endif
    do face = 1, face_per_cell ! tets only here
      i = mod(face+0-1,4)+1
      j = mod(face****,4)+1
      k = mod(face****,4)+1
      l = mod(face****,4)+1
      m(i) = 0.5_dp*( f(j)*(g(k)-g(l)) -f(k)*(g(l)-g(j)) +f(l)*(g(j)-g(k)))
    enddo
    write(*,*)
    write(6,'(a,12(1x,es20.10))') 'm    ',m,sum(m)

    g12 = g(1)-g(2)
    g13 = g(1)-g(3)
    g23 = g(2)-g(3)
    g34 = g(3)-g(4)
    g41 = g(4)-g(1)
    g42 = g(4)-g(2)
    write(6,'(a,12(1x,es20.10))') 'm    ',        &
           0.5_dp*( f(2)*g34 -f(3)*g42 +f(4)*g23) &
          ,0.5_dp*( f(3)*g41 -f(4)*g13 -f(1)*g34) &
          ,0.5_dp*( f(4)*g12 -f(1)*g42 +f(2)*g41) &
          ,0.5_dp*(-f(1)*g23 +f(2)*g13 -f(3)*g12)
    write(*,*)

    deallocate(mass)

    endif loop_cell_faces
    enddo loop_ipts


    allocate ( local_data  ( n_output_variables, n_points) )
    allocate ( global_data ( n_output_variables, n_points) )
    local_data  = 0.0_dp
    global_data = 0.0_dp

!   call gather_point_data(grid, soln, ivol, n_output_variables,       &
!                          output_variables, local_data )

!   if ( sample(ivol)%geo_type_name == 'line' ) then
!     call lmpi_gatherv( local_data, total_point_vector, global_data )
!   else
      call lmpi_reduce( local_data, global_data )
!   endif

    call lmpi_bcast( global_data )

!   if ( lmpi_master ) then
!     do ipts = 1, number_of_points(ivol)
!       if ( point_is_found_global(ivol,ipts)/= 0 ) cycle
!       write(6,'(15x,a)') 'Resetting lost point data'
!       do nn = 1, n_output_variables
!         global_data(nn,ipts) = -999.0
!         var = trim(adjustl(output_variables(nn)))
!         select case ( var )
!         case('rho','p','temp','rho_tavg','p_tavg')
!           global_data(nn,ipts) = 999.0
!         end select
!       end do
!     end do
!   endif

    if ( lmpi_master ) then
      if ( verbose ) write(*,*) 'Allocate edge_master-write_point_data',n_dim
      allocate ( edge_master( n_dim ) , stat=iostat )
      if ( iostat /= 0 ) &
        write(*,*) 'Allocate error edge_master-write_point_data'
      allocate( q_master( n_output_variables, n_dim ), stat=iostat )
      if ( iostat /= 0 ) write(*,*) 'Allocate error q_master-write_point_data'
        edge_count_master = 0

        do i = 1, n_points
          edge_count_master             = edge_count_master + 1
          edge_master(i)                = edge_count_master
          q_master(:,edge_count_master) = global_data(:,i)
      enddo
    endif

    deallocate( total_point_vector )
    deallocate( local_data )
    deallocate( global_data )
!-----------------------------------------------------------------------------80
!       output the sampled data in either tecplot or fieldview format
! 29July2010 - partition based writes work only with tecplot currently
!-----------------------------------------------------------------------------80
!     select case ( plot(ivol) )

!       case ( 'tecplot' )

!         call tecplot_point_output( project_name, time_step, ivol,            &
!                                n_output_variables, output_variables,         &
!                                append_timestep(ivol), label(ivol) )

!     end select

!-----------------------------------------------------------------------------80
!     lmpi_master_2: if ( lmpi_master ) then
!       write(*,'(2x,a,i0,4a)')                                                &
!        ' Geometry number: ',ivol,                                            &
!                  ', Type: ',trim(geo_name),                                  &
!          ', Nodes output: ',int_to_s(total_points,placeholders=.true.)
!       end if lmpi_master_2

!   if ( lmpi_master ) then
!     deallocate(edge_master)
!     deallocate(   q_master)
!   endif

  end subroutine write_stream_data

!============================== TECPLOT_SURVEYP_OUTPUT =======================80
!
!  Routine to output a tecplot file containing partition survey data
!
!=============================================================================80
  subroutine tecplot_surveyp_output( geo_name, project, time_step, geom_number,&
                                     n_output_variables, output_variables,     &
                                     append_timestep,                          &
                                     n_edges, n_tria, trinode_map, edge_data,  &
                                     label, append_history,                    &
                                     adjoint_mode )

    use info_depr,            only : simulation_time, physical_timestep
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters
    use tecplot_io_helpers,   only : get_tec_file_extension,                   &
                                     write_sampling_tec, tecplot_has_binary
    use nml_sampling_output,  only : max_smp_vars
    use file_utils,           only : rm, file_exists, available_unit
    use lmpi,                 only : lmpi_send, lmpi_recv
    use global_image,         only : global_image_tecplot_zone
    use sampling_headers,     only : sampling_strands

    character(len=80),        intent(in) :: geo_name
    character(len=80),        intent(in) :: project
    integer,                  intent(in) :: time_step
    integer,                  intent(in) :: geom_number
    integer,                  intent(in) :: n_output_variables
    logical,                  intent(in) :: append_timestep
    integer,                  intent(in) :: n_edges
    integer,                  intent(in) :: n_tria
    integer,  dimension(:,:), intent(in) :: trinode_map
    real(dp), dimension(:,:), intent(in) :: edge_data
    character(len=80),        intent(in) :: label
    logical,                  intent(in) :: append_history, adjoint_mode

    character(len=80), dimension(max_smp_vars),  intent(in) :: output_variables

    integer                               :: proc_id, n
    integer                               :: master_id, ierr
    integer                               :: temp_n_edges, temp_n_tria
    integer,  dimension(2)                :: isendbuf, irecvbuf
    integer,  dimension(:,:), allocatable :: temp_map

    real(dp), dimension(:,:), allocatable :: temp_data

    character(len=3)   :: tec_file_extension
    character(len=80)  :: geom, step, restart_file
    character(len=256) :: filename, variable_list, file_title, zone_title
    character(len=256) :: full_title

    real(dp)           :: time_val

    integer :: unit, strandid

  continue

!   set the file name

    write(geom,"(i0)") geom_number

    if (append_timestep) then
      if ( adjoint_mode .and. itime /= 0 ) then
        write(step,"(i0)")    physical_timestep
        step = '_timestep' // trim(adjustl(step))
      else
        write(step,"(i0)")    time_step+prior_iters
        step = '_timestep' // trim(adjustl(step))
      endif
    else
      step = ''
    end if

    if (itime == 0) then
      time_val = real(time_step+prior_iters, dp)
    else
      time_val = simulation_time
    end if

    restart_file = trim(adjustl(project))

    call get_tec_file_extension(tec_file_extension)

    filename = trim(adjustl(restart_file)) // '_tec_sampling_geom'             &
            // trim(adjustl(geom))                                             &
            // trim(adjustl(step)) // '.'                                      &
            // trim(adjustl(tec_file_extension))

    if ( trim(adjustl(label)) /= '' ) then
      if ( append_history ) then
        filename = trim(adjustl(restart_file)) // '_'                          &
                // trim(adjustl(label)) // '.'                                 &
                // trim(adjustl(tec_file_extension))
      else
        filename = trim(adjustl(restart_file)) // '_'                          &
                // trim(adjustl(label))                                        &
                // trim(adjustl(step)) // '.'                                  &
                // trim(adjustl(tec_file_extension))
       endif
     endif

!   set the file title
    file_title ="solution sampling"

!   create the list of variables
    variable_list = ''
    do n=1,n_output_variables
      variable_list = trim(adjustl(variable_list)) // ' ' //                   &
                      trim(adjustl(output_variables(n))) // ' '
    end do

!   set the zone title

    write(zone_title,'(3a)')  '"geometry ' // trim(adjustl(geom)) // '"'
    if ( trim(adjustl(label)) /= '' ) then
      write(zone_title,'(3a)')  '"' // trim(adjustl(label)) // '"'
    endif

    if ( lmpi_master ) then

      if ( file_exists(filename) .and. .not.append_timestep                    &
           .and. .not.append_history ) then
        call rm(filename)
      endif

      write(*,'(1x,2a)') 'Writing Sampling output: ',trim(filename)

    end if

    binary_tec: if ( tecplot_has_binary() ) then

    processor_loop : do proc_id = 0, lmpi_nproc-1

      call lmpi_synchronize()

      master_id = 0

      master_needs_data : if (proc_id > 0) then

!       send data to master

        if (proc_id == lmpi_id) then

          isendbuf(1) = n_edges
          isendbuf(2) = n_tria

          call lmpi_send(isendbuf,    2,                             master_id,&
                         proc_id+1*lmpi_nproc, ierr)

          call lmpi_send(edge_data, n_output_variables*n_edges,      master_id,&
                         proc_id+2*lmpi_nproc, ierr)

          call lmpi_send(trinode_map, 4*n_tria,                      master_id,&
                         proc_id+3*lmpi_nproc, ierr)
        end if

        if (lmpi_master) then

          call lmpi_recv(irecvbuf,  2,                               proc_id,  &
                         proc_id+1*lmpi_nproc, ierr)

          temp_n_edges = irecvbuf(1)
          temp_n_tria  = irecvbuf(2)
          allocate(temp_data(n_output_variables, max(1,temp_n_edges)))
          allocate(temp_map(4, max(1,temp_n_tria)))

          call lmpi_recv(temp_data, n_output_variables*temp_n_edges, proc_id,  &
                         proc_id+2*lmpi_nproc, ierr)

          call lmpi_recv(temp_map,    4*temp_n_tria,                 proc_id,  &
                          proc_id+3*lmpi_nproc, ierr)

        end if

!       master writes data

        if (lmpi_master) then

          call write_sampling_tec(n_output_variables, temp_n_edges,            &
                                  temp_n_tria, temp_map, temp_data,            &
                                  file_title, zone_title, variable_list,       &
                                  filename, geom_number, proc_id, time_val,    &
                                  append_history )

          deallocate(temp_data)
          deallocate(temp_map)

        end if

      else master_needs_data  ! master already has data

        if (lmpi_master) then

          call write_sampling_tec(n_output_variables, n_edges,                 &
                                  n_tria, trinode_map, edge_data,              &
                                  file_title, zone_title, variable_list,       &
                                  filename, geom_number, proc_id, time_val,    &
                                  append_history )

        end if

      end if master_needs_data

    end do processor_loop

    else

      if (lmpi_master) then

        if ( file_exists(filename) ) then
          if ( append_history ) then
            unit = available_unit()
            open(unit,file=filename,position='append')
          else
            unit = available_unit()
            open(unit,file=filename)
          endif
        else
          unit = available_unit()
          open(unit,file=filename)
          rewind(unit)
        endif

        write(unit,'(3a)') 'title="',trim(adjustl(file_title)),'"'
        write(unit,'(2a)') 'variables = ', trim(adjustl(variable_list))
      end if

      strandid = 0
      if ( sampling_strands(geom_number) ) strandid = geom_number

      write(full_title,"(a,', solutiontime=',e14.7,', strandid=',i0)") &
        trim(zone_title), real(time_val,dp), strandid

      if ( geo_name == 'isocrinkle' ) then
      call global_image_tecplot_zone( n_output_variables, n_edges, edge_data, &
                                      4, n_tria, trinode_map(1:4,:),          &
                                      unit, full_title, 'quadrilateral' )
      else
      call global_image_tecplot_zone( n_output_variables, n_edges, edge_data, &
                                      3, n_tria, trinode_map(1:3,:),          &
                                      unit, full_title, 'triangle' )
      endif
      if (lmpi_master) then
        close(unit)
      end if

    end if binary_tec

  end subroutine tecplot_surveyp_output

!============================== FWH_SURVEYP_OUTPUT ===========================80
!
!  Routine to output fwh data
!
!=============================================================================80
  subroutine fwh_surveyp_output( project,            geom_number,              &
                                 n_output_variables, output_variables,         &
                                 n_tria, data_avg )

    use allocations,           only : my_alloc_ptr
    use file_utils,            only : available_unit
    use info_depr,             only : ncyc
    use nml_fwh_acoustic_data, only : fwh_data_freq
    use nml_nonlinear_solves,  only : dt
    use system_extensions,     only : se_open
    use nml_sampling_output,   only : max_smp_vars
    use kinddefs,              only : r4
    use sampling_headers,      only : verbose, fwh_formatted
    use nml_global,            only : irest

    character(len=80),                       intent(in) :: project
    integer,                                 intent(in) :: geom_number
    integer,                                 intent(in) :: n_output_variables
    integer,                                 intent(in) :: n_tria
    real(r4), dimension(n_output_variables,n_tria), intent(in) :: data_avg

    character(len=80), dimension(max_smp_vars),  intent(in) :: output_variables

    integer                               :: ib

    logical, dimension(max_geom),         save :: boundaries_not_set = .true.
    integer, dimension(max_geom),         save :: fwh_output_count = 0

    integer :: i, j, nn, num

    logical                                :: echo_data

!   local variables for normals of boundary faces
    real(r4), dimension(:,:), allocatable      :: normal, xyz
    real(r4)                                   :: rho_avg
    real(r4)                                   :: rhou_avg
    real(r4)                                   :: rhov_avg
    real(r4)                                   :: rhow_avg
    real(r4)                                   :: pressure_avg
    real(r4)                                   :: fwh_time, fwh_period

    integer, dimension(max_geom),         save :: output_fixed
    integer, dimension(max_geom),         save :: bndry_points

    character(len=1024),                  save :: title
    character(len=80)                          :: filename_p, filename_g
    character(len=32)                          :: id

    integer, dimension(max_geom), save         :: unit_g
    integer, dimension(max_geom), save         :: unit_p
    integer                                    :: iu_g
    integer                                    :: iu_p

    integer,       parameter :: MAGIC_NUMBER = 42
    integer,       parameter :: FWH_VERSION = 1
    integer,       parameter :: FWH_VERSION_SUB = 0
    character(32), parameter :: UNITS = '[]'
    integer,       parameter :: GEOM = 1
    integer,       parameter :: FUNCTIONAL_FLOW_DATA = 2
    integer,       parameter :: UNSTRUCTURED = 2
    integer,       parameter :: CONSTANT = 1
    integer,       parameter :: PERIODIC = 2
    integer,       parameter :: APERIODIC = 3
    integer,       parameter :: CONST_PERIODIC = 4
    integer,       parameter :: CONST_APERIODIC = 5
    integer,       parameter :: NODE_CENTERED = 1
!   integer,       parameter :: PRESSURE_DATA = 1
    integer,       parameter :: FLOW_PARAMETERS = 3
    integer,       parameter :: SINGLE_PRECISION = 1
    integer,       parameter :: ZERO = 0

    integer                                     :: iostat
    integer                                     :: total_survey_tria
    integer,  dimension(:),   pointer           :: survey_tria
    real(r4), dimension(:,:), allocatable       :: global_data
    integer,  dimension(:),   allocatable, save :: nn_arr

  continue

    echo_data = .true.
    if (.not. lmpi_master) echo_data = .false.
    echo_data = .false.

    ib        = geom_number
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!   gather global data
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    call my_alloc_ptr( survey_tria, lmpi_nproc )
    survey_tria       = 0
    call lmpi_allgather(n_tria,survey_tria)
    total_survey_tria = sum(survey_tria)

    if ( echo_data  ) then
       write(*,'(a,128i6)') 'survey_tria    =',survey_tria
       write(*,*) 'total_survey_tria=',total_survey_tria
    endif

    allocate( global_data ( n_output_variables, total_survey_tria )           &
              , stat=iostat )
    if ( iostat /= 0 ) write(*,*) 'Allocate error global_data '
    global_data = 0.0_r4
    call lmpi_gatherv( data_avg, survey_tria(lmpi_id+1), &
      global_data, survey_tria )
    deallocate(survey_tria)
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!  end gather global data
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    if (echo_data) write(*,'(a,i0,a,i0)')                                      &
        '...geometry selected to dump fwh data = ', ib

!   first time through, determine which boundaries we will output for
!   animation - the user may override the default output types by
!   providing an auxilary file
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!   similar structure as fwh_global_surface_unf
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    master_writes_headers:  if ( lmpi_master ) then

!-----------------------------------------------------------------------------80
      set_boundaries : if ( boundaries_not_set(ib) ) then

      if ( .not. allocated(nn_arr) ) allocate(nn_arr(n_output_variables))
       nn_arr = 0
        do i  = 1, n_output_variables
           select case (trim(adjustl(output_variables(i))))
           case('rho')
             nn_arr(1) = i
           case('u' )
             nn_arr(2) = i
           case('v' )
             nn_arr(3) = i
           case('w' )
             nn_arr(4) = i
           case('p_gage')
             nn_arr(5) = i
          end select
        end do

        if (echo_data) &
        write(*,'(a,i0,a,i0)') '...set allocation for geometry ', ib

        title                  = trim(project)
        boundaries_not_set(ib) = .false.
!-----------------------------------------------------------------------------80
!warning Look for ways to read "output_fixed(ib)" via input files
        output_fixed(ib)       = CONST_APERIODIC
        bndry_points(ib)       = total_survey_tria

        write(id, '(I3.3)') ib
        id = adjustl(id)
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
! zero_count
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
        zero_count: if ( fwh_output_count(ib) == 0 ) then

!         Write headers for output files

        write(id, '(I3.3)') ib
        id = adjustl(id)
!-------------------- geometry unit
        unit_g(ib) = available_unit()
        iu_g       = unit_g(ib)
        if ( skeleton > 2 ) &
        write(*,'(a,i5,a,i5)') "Opening for header writes, iu_g=", iu_g

        filename_g = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_grid.bin"
        if ( fwh_formatted ) then
          call se_open(iu_g,file=filename_g,form='formatted')
        else
          call se_open(iu_g,file=filename_g,form='unformatted',access='stream')
        endif
        rewind(iu_g)
        if ( fwh_formatted ) then
          write(iu_g,*) MAGIC_NUMBER
          write(iu_g,*) FWH_VERSION, FWH_VERSION_SUB
          write(iu_g,*) UNITS
          write(iu_g,*) title
          write(iu_g,*) GEOM
          write(iu_g,*) 1
          write(iu_g,*) UNSTRUCTURED
          if ( output_fixed(ib) == CONST_PERIODIC .or.                       &
               output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_g,*) CONSTANT
          else
            write(iu_g,*) output_fixed(ib)
          endif
          write(iu_g,*) NODE_CENTERED
          write(iu_g,*) SINGLE_PRECISION
          write(iu_g,*) ZERO, ZERO
          write(iu_g,*) id
          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( output_fixed(ib) == APERIODIC ) then
            write(iu_g,*) num
          else if( output_fixed(ib) == PERIODIC ) then
            write(iu_g,*) fwh_period, num
          end if

          write(iu_g,*) bndry_points(ib)
          write(iu_g,*) ZERO
        else
          write(iu_g) MAGIC_NUMBER
          write(iu_g) FWH_VERSION, FWH_VERSION_SUB
          write(iu_g) UNITS
          write(iu_g) title
          write(iu_g) GEOM
          write(iu_g) 1
          write(iu_g) UNSTRUCTURED
          if ( output_fixed(ib) == CONST_PERIODIC .or.                       &
               output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_g) CONSTANT
          else
            write(iu_g) output_fixed(ib)
          endif
          write(iu_g) NODE_CENTERED
          write(iu_g) SINGLE_PRECISION
          write(iu_g) ZERO, ZERO
          write(iu_g) id
          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( output_fixed(ib) == APERIODIC ) then
            write(iu_g) num
          else if( output_fixed(ib) == PERIODIC ) then
            write(iu_g) fwh_period, num
          end if

          write(iu_g) bndry_points(ib)
          write(iu_g) ZERO
       endif

!-------------------- data unit
        unit_p(ib) = available_unit()
        iu_p       = unit_p(ib)
        filename_p = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_data.bin"
        if ( skeleton > 2 ) &
        write(*,'(a,i5,a,i5)') "Opening for header writes, iu_p=", iu_p
        if ( fwh_formatted ) then
          call se_open(iu_p,file=filename_p,form='formatted')
        else
          call se_open(iu_p,file=filename_p,form='unformatted',access='stream')
        endif
        rewind(iu_p)

        if ( fwh_formatted ) then
          write(iu_p,*) MAGIC_NUMBER
          write(iu_p,*) FWH_VERSION, FWH_VERSION_SUB
          write(iu_p,*) title
          write(iu_p,*) FUNCTIONAL_FLOW_DATA
          write(iu_p,*) 1
          write(iu_p,*) UNSTRUCTURED
          if ( output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p,*) PERIODIC
          else if( output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p,*) APERIODIC
          else
            write(iu_p,*) output_fixed(ib)
          endif
          write(iu_p,*) NODE_CENTERED
!         write(iu_p,*) PRESSURE_DATA
          write(iu_p,*) FLOW_PARAMETERS
          write(iu_p,*) output_fixed(ib)
          write(iu_p,*) SINGLE_PRECISION
          write(iu_p,*) 1, ZERO
          write(iu_p,*) 1, 1
          write(iu_p,*) id

          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( output_fixed(ib) == APERIODIC ) then
            write(iu_p,*) num
          else if( output_fixed(ib) == PERIODIC ) then
            write(iu_p,*) fwh_period, num
          else if( output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p,*) num
          else if( output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p,*) fwh_period, num
          end if

          write(iu_p,*) bndry_points(ib) ! npoints
        else
          write(iu_p) MAGIC_NUMBER
          write(iu_p) FWH_VERSION, FWH_VERSION_SUB
          write(iu_p) title
          write(iu_p) FUNCTIONAL_FLOW_DATA
          write(iu_p) 1
          write(iu_p) UNSTRUCTURED
          if ( output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p) PERIODIC
          else if( output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p) APERIODIC
          else
            write(iu_p) output_fixed(ib)
          endif
          write(iu_p) NODE_CENTERED
!         write(iu_p) PRESSURE_DATA
          write(iu_p) FLOW_PARAMETERS
          write(iu_p) output_fixed(ib)
          write(iu_p) SINGLE_PRECISION
          write(iu_p) 1, ZERO
          write(iu_p) 1, 1
          write(iu_p) id

          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( output_fixed(ib) == APERIODIC ) then
            write(iu_p) num ! aperiodic case, # of time steps
          else if( output_fixed(ib) == PERIODIC ) then
            write(iu_p) fwh_period, num ! periodic case, period & # time steps
          else if( output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p) num ! aperiodic case, # of time steps
          else if( output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p) fwh_period, num
          end if

          write(iu_p) bndry_points(ib) ! npoints

        endif

      end if zero_count
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
! zero_count
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80

      endif set_boundaries

    end if master_writes_headers

!-----------------------------------------------------------------------------80
    grid_gather_write: if (lmpi_master) then

      fwh_time = real(dt * fwh_output_count(ib) * fwh_data_freq)

      if ( verbose ) then
      write(*,*)
      write(*,'(a,i5,a,i5)') "Writing fwh_data file for for geometry ", ib
      write(*,'(a,1x,es15.5)') "fwh_time = ", fwh_time
      write(*,'(a,i5,a,i5)') "output_fixed(ib) = ", output_fixed(ib)
      endif

      write(id, '(I3.3)') ib
      id         = adjustl(id)
      iu_g       = unit_g(ib)
      filename_g = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_grid.bin"
!-----------------------------------------------------------------------------80

      geometry_write:  if ( fwh_output_count(ib) == 0 ) then

        allocate(    xyz(bndry_points(ib), 3) )
        allocate( normal(bndry_points(ib), 3) )

        normals_face_loop : do j = 1, bndry_points(ib)
          do nn = 1, n_output_variables
            if ( trim(adjustl(output_variables(nn))) == 'x') then
              xyz(j,1)  = global_data(nn,j)
            else if ( trim(adjustl(output_variables(nn))) == 'y') then
              xyz(j,2)  = global_data(nn,j)
            else if ( trim(adjustl(output_variables(nn))) == 'z') then
              xyz(j,3)  = global_data(nn,j)
             else if ( trim(adjustl(output_variables(nn))) == 'nx') then
               normal(j,1)  = global_data(nn,j)
             else if ( trim(adjustl(output_variables(nn))) == 'ny') then
               normal(j,2)  = global_data(nn,j)
             else if ( trim(adjustl(output_variables(nn))) == 'nz') then
               normal(j,3)  = global_data(nn,j)
            endif
          enddo
        end do normals_face_loop

        if ( skeleton > 2 ) write(*,'(a,a,a,i5,a,i5)') &
           "Writing to ",trim(filename_g)," for xyznormal writes, iu_g=", iu_g
      if ( fwh_formatted ) then
        normals_face_loop_test : do j = 1, bndry_points(ib)
          write(iu_g,'(6(1x,es15.8))') &
          xyz(j,1), xyz(j,2), xyz(j,3), normal(j,1), normal(j,2), normal(j,3)
        end do normals_face_loop_test
      else
        write(iu_g) xyz
        write(iu_g) normal
      endif
      close(iu_g)

      if( allocated( xyz          ) ) deallocate( xyz          )
      if( allocated( normal       ) ) deallocate( normal       )

      endif geometry_write

    end if grid_gather_write
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!    write data
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    data_gather_write : if ( lmpi_master ) then

      write(id, '(I3.3)') ib
      id         = adjustl(id)
      iu_p       = unit_p(ib)
      filename_p = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_data.bin"
      if ( skeleton > 2 )  write(*,'(a,a,a,i5,a,i5)') &
           "Writing to ",trim(filename_p)," for data writes, iu_p=", iu_p

      if ( (fwh_output_count(ib) == 0                                          &
         .and.  output_fixed(ib) == CONSTANT) .or.                             &
         .not. (output_fixed(ib) == CONSTANT) ) then

        if ( fwh_formatted ) then
          write(iu_p,*) fwh_time
        else
          write(iu_p) fwh_time
        endif

        do j = 1,bndry_points(ib)
           rho_avg      =         global_data(nn_arr(1),j)
           rhou_avg     = rho_avg*global_data(nn_arr(2),j)
           rhov_avg     = rho_avg*global_data(nn_arr(3),j)
           rhow_avg     = rho_avg*global_data(nn_arr(4),j)
           pressure_avg =         global_data(nn_arr(5),j)
          if ( fwh_formatted ) then
            write(iu_p,'(5(1x,es15.8))') &
            rho_avg, rhou_avg, rhov_avg, rhow_avg, pressure_avg
          else
            write(iu_p) rho_avg, rhou_avg, rhov_avg, rhow_avg, pressure_avg
          endif
        end do

      end if

    end if data_gather_write
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!   end write data
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80

    fwh_output_count(ib) = fwh_output_count(ib) + 1
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
!   end write footer
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    if ( lmpi_master ) then
      deallocate (global_data)
    endif

  end subroutine fwh_surveyp_output


!============================== FWH_SURVEYP_GATHER ===========================80
!
!  Routine to gather fwh data for subsequent asynchronous IO
!
!=============================================================================80
  subroutine fwh_surveyp_gather( ib, n_output_variables, output_variables,     &
                                 n_tria, data_avg )

    use allocations,           only : my_alloc_ptr
    use nml_sampling_output,   only : max_smp_vars
    use kinddefs,              only : r4
    use sampling_headers,      only : number_of_geometries
    use nml_fwh_acoustic_data, only : steps_per_period

    integer,                                 intent(in) :: ib
    integer,                                 intent(in) :: n_output_variables
    integer,                                 intent(in) :: n_tria
    real(r4), dimension(n_output_variables,n_tria), intent(in) :: data_avg

    character(len=80), dimension(max_smp_vars),  intent(in) :: output_variables

    logical, dimension(max_geom),         save :: boundaries_not_set = .true.
    integer, dimension(max_geom),         save :: fwh_output_count = 0

    integer :: i, j, nn, dim1

    integer,       parameter :: CONSTANT = 1
    integer,       parameter :: CONST_APERIODIC = 5

    integer                                     :: iostat
    integer                                     :: total_survey_tria
    integer,  dimension(:),   pointer           :: survey_tria
    real(r4), dimension(:,:), allocatable       :: global_data
    integer,  dimension(:),   allocatable, save :: nn_arr

  continue

    if ( .not. allocated(fwh_data) ) then
      allocate(fwh_data(number_of_geometries))
    endif
    if ( .not. allocated(fwh_bndry_points) ) then
      allocate(fwh_bndry_points(number_of_geometries))
    endif
    if ( .not. allocated(fwh_output_fixed) ) then
      allocate(fwh_output_fixed(number_of_geometries))
    endif
    if ( .not. allocated(fwh_data_period) ) then
      allocate(fwh_data_period(number_of_geometries))
    endif

    call my_alloc_ptr( survey_tria, lmpi_nproc )
    survey_tria       = 0
    call lmpi_allgather(n_tria,survey_tria)
    total_survey_tria = sum(survey_tria)

    allocate( global_data ( n_output_variables, total_survey_tria )           &
              , stat=iostat )
    if ( iostat /= 0 ) write(*,*) 'Allocate error global_data '
    global_data = 0.0_r4
    call lmpi_gatherv( data_avg, survey_tria(lmpi_id+1), &
      global_data, survey_tria )
    deallocate(survey_tria)

!   first time through, determine which boundaries we will output for
!   animation - the user may override the default output types by
!   providing an auxilary file

    master_writes_headers:  if ( lmpi_master ) then

!-----------------------------------------------------------------------------80
      set_boundaries : if ( boundaries_not_set(ib) ) then

      if ( .not. allocated(nn_arr) ) allocate(nn_arr(n_output_variables))
       nn_arr = 0
        do i  = 1, n_output_variables
           select case (trim(adjustl(output_variables(i))))
           case('rho')
             nn_arr(1) = i
           case('u' )
             nn_arr(2) = i
           case('v' )
             nn_arr(3) = i
           case('w' )
             nn_arr(4) = i
           case('p_gage')
             nn_arr(5) = i
          end select
        end do

        boundaries_not_set(ib) = .false.
        fwh_output_fixed(ib)   = CONST_APERIODIC
        fwh_bndry_points(ib)   = total_survey_tria
        fwh_data_period(ib)    = steps_per_period(1)

      endif set_boundaries

    end if master_writes_headers

    grid_gather_write: if (lmpi_master) then

      geometry_write:  if ( fwh_output_count(ib) == 0 ) then

        if ( allocated(fwh_data(ib)%xyz) ) then
          dim1 = size(fwh_data(ib)%xyz,1)
          if ( dim1 < fwh_bndry_points(ib) ) then
            deallocate(fwh_data(ib)%xyz)
            allocate(fwh_data(ib)%xyz(fwh_bndry_points(ib),3))
          endif
        else
          allocate(fwh_data(ib)%xyz(fwh_bndry_points(ib),3))
        endif

        if ( allocated(fwh_data(ib)%normal) ) then
          dim1 = size(fwh_data(ib)%normal,1)
          if ( dim1 < fwh_bndry_points(ib) ) then
            deallocate(fwh_data(ib)%normal)
            allocate(fwh_data(ib)%normal(fwh_bndry_points(ib),3))
          endif
        else
          allocate(fwh_data(ib)%normal(fwh_bndry_points(ib),3))
        endif

        normals_face_loop : do j = 1, fwh_bndry_points(ib)
          do nn = 1, n_output_variables
            if ( trim(adjustl(output_variables(nn))) == 'x') then
              fwh_data(ib)%xyz(j,1)  = global_data(nn,j)
            else if ( trim(adjustl(output_variables(nn))) == 'y') then
              fwh_data(ib)%xyz(j,2)  = global_data(nn,j)
            else if ( trim(adjustl(output_variables(nn))) == 'z') then
              fwh_data(ib)%xyz(j,3)  = global_data(nn,j)
             else if ( trim(adjustl(output_variables(nn))) == 'nx') then
               fwh_data(ib)%normal(j,1)  = global_data(nn,j)
             else if ( trim(adjustl(output_variables(nn))) == 'ny') then
               fwh_data(ib)%normal(j,2)  = global_data(nn,j)
             else if ( trim(adjustl(output_variables(nn))) == 'nz') then
               fwh_data(ib)%normal(j,3)  = global_data(nn,j)
            endif
          enddo
        end do normals_face_loop

      endif geometry_write

    end if grid_gather_write

    data_gather_write : if ( lmpi_master ) then

      if ( (fwh_output_count(ib) == 0                                          &
         .and.  fwh_output_fixed(ib) == CONSTANT) .or.                         &
         .not. (fwh_output_fixed(ib) == CONSTANT) ) then

        if ( allocated(fwh_data(ib)%solution) ) then
          dim1 = size(fwh_data(ib)%solution,2)
          if ( dim1 < fwh_bndry_points(ib) ) then
            deallocate(fwh_data(ib)%solution)
            allocate(fwh_data(ib)%solution(5,fwh_bndry_points(ib)))
          endif
        else
          allocate(fwh_data(ib)%solution(5,fwh_bndry_points(ib)))
        endif

        do j = 1,fwh_bndry_points(ib)
          fwh_data(ib)%solution(1,j) = global_data(nn_arr(1),j)
          fwh_data(ib)%solution(2,j) =                                         &
                               global_data(nn_arr(1),j)*global_data(nn_arr(2),j)
          fwh_data(ib)%solution(3,j) =                                         &
                               global_data(nn_arr(1),j)*global_data(nn_arr(3),j)
          fwh_data(ib)%solution(4,j) =                                         &
                               global_data(nn_arr(1),j)*global_data(nn_arr(4),j)
          fwh_data(ib)%solution(5,j) = global_data(nn_arr(5),j)
        end do

      end if

    end if data_gather_write

    fwh_output_count(ib) = fwh_output_count(ib) + 1

    if ( lmpi_master ) deallocate (global_data)

  end subroutine fwh_surveyp_gather


!============================== FWH_SURVEYP_OUTPUTA ==========================80
!
!  Routine to output asynchronous fwh data
!
!=============================================================================80
  subroutine fwh_surveyp_outputa( project, ib )

    use file_utils,            only : available_unit
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    use info_depr,             only : ncyc
    use nml_fwh_acoustic_data, only : fwh_data_freq
    use nml_global,            only : irest
#else
    use nml_fwh_acoustic_data, only : fwh_data_freq
#endif
    use nml_nonlinear_solves,  only : dt
    use kinddefs,              only : r4
    use sampling_headers,      only : verbose, fwh_formatted,                  &
                                      number_of_geometries
#ifndef HAVE_FORTRAN_ASYNCHRONOUS_IO
    use lmpi,                  only : lmpi_die
#endif

    character(len=80),                       intent(in) :: project
    integer,                                 intent(in) :: ib

    logical, dimension(max_geom),         save :: boundaries_not_set = .true.
    integer, dimension(max_geom),         save :: fwh_output_count = 0

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    integer, asynchronous :: num
#endif
    integer :: j

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    real(r4), asynchronous                     :: fwh_time, fwh_period
#else
    real(r4)                                   :: fwh_time
#endif

    character(len=80)                          :: filename_p, filename_g

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    character(len=1024), asynchronous,    save :: title
    character(len=32),   asynchronous          :: id
#else
    character(len=1024), save :: title
    character(len=32)         :: id
#endif

    integer                                    :: iu_g
    integer                                    :: iu_p

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
    integer, parameter :: MAGIC_NUMBER = 42
    integer, parameter :: FWH_VERSION = 1
    integer, parameter :: FWH_VERSION_SUB = 0
    integer, parameter :: GEOM = 1
    integer, parameter :: FUNCTIONAL_FLOW_DATA = 2
    integer, parameter :: UNSTRUCTURED = 2
    integer, parameter :: CONSTANT = 1
    integer, parameter :: PERIODIC = 2
    integer, parameter :: APERIODIC = 3
    integer, parameter :: CONST_PERIODIC = 4
    integer, parameter :: CONST_APERIODIC = 5
    integer, parameter :: NODE_CENTERED = 1
    integer, parameter :: FLOW_PARAMETERS = 3
    integer, parameter :: SINGLE_PRECISION = 1
    integer, parameter :: ZERO = 0

    character(32), parameter :: UNITS = '[]'
#endif

  continue

    if ( .not. allocated(fwh_unit_g) ) then
      allocate(fwh_unit_g(number_of_geometries)); fwh_unit_g(:) = 0
    endif
    if ( .not. allocated(fwh_unit_p) ) then
      allocate(fwh_unit_p(number_of_geometries)); fwh_unit_p(:) = 0
    endif

    master_writes_headers:  if ( lmpi_master ) then

      set_boundaries : if ( boundaries_not_set(ib) ) then

        title                  = trim(project)
        boundaries_not_set(ib) = .false.

        write(id, '(I3.3)') ib
        id = adjustl(id)
        zero_count: if ( fwh_output_count(ib) == 0 ) then

!         Write headers for output files

        write(id, '(I3.3)') ib
        id = adjustl(id)
!-------------------- geometry unit
        fwh_unit_g(ib) = available_unit()
        iu_g       = fwh_unit_g(ib)
        if ( skeleton > 2 ) &
        write(*,'(a,i5,a,i5)') "Opening for header writes, iu_g=", iu_g

        filename_g = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_grid.bin"

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
        if ( fwh_formatted ) then
          open(iu_g,file=filename_g,form='formatted',asynchronous='yes')
        else
          open(iu_g,file=filename_g,form='unformatted',access='stream',        &
               asynchronous='yes')
        endif
#else
        write(*,*) 'Compiler does not support asynchronous I/O.'
        call lmpi_die
#endif
        rewind(iu_g)
        if ( fwh_formatted ) then
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
          write(iu_g,*,asynchronous='yes') MAGIC_NUMBER
          write(iu_g,*,asynchronous='yes') FWH_VERSION, FWH_VERSION_SUB
          write(iu_g,*,asynchronous='yes') UNITS
          write(iu_g,*,asynchronous='yes') title
          write(iu_g,*,asynchronous='yes') GEOM
          write(iu_g,*,asynchronous='yes') 1
          write(iu_g,*,asynchronous='yes') UNSTRUCTURED
          if ( fwh_output_fixed(ib) == CONST_PERIODIC .or.                     &
               fwh_output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_g,*,asynchronous='yes') CONSTANT
          else
            write(iu_g,*,asynchronous='yes') fwh_output_fixed(ib)
          endif
          write(iu_g,*,asynchronous='yes') NODE_CENTERED
          write(iu_g,*,asynchronous='yes') SINGLE_PRECISION
          write(iu_g,*,asynchronous='yes') ZERO, ZERO
          write(iu_g,*,asynchronous='yes') id
          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( fwh_output_fixed(ib) == APERIODIC ) then
            write(iu_g,*,asynchronous='yes') num
          else if( fwh_output_fixed(ib) == PERIODIC ) then
            write(iu_g,*,asynchronous='yes') fwh_period, num
          end if

          write(iu_g,*,asynchronous='yes') fwh_bndry_points(ib)
          write(iu_g,*,asynchronous='yes') ZERO
#endif
        else
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
          write(iu_g,asynchronous='yes') MAGIC_NUMBER
          write(iu_g,asynchronous='yes') FWH_VERSION, FWH_VERSION_SUB
          write(iu_g,asynchronous='yes') UNITS
          write(iu_g,asynchronous='yes') title
          write(iu_g,asynchronous='yes') GEOM
          write(iu_g,asynchronous='yes') 1
          write(iu_g,asynchronous='yes') UNSTRUCTURED
          if ( fwh_output_fixed(ib) == CONST_PERIODIC .or.                     &
               fwh_output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_g,asynchronous='yes') CONSTANT
          else
            write(iu_g,asynchronous='yes') fwh_output_fixed(ib)
          endif
          write(iu_g,asynchronous='yes') NODE_CENTERED
          write(iu_g,asynchronous='yes') SINGLE_PRECISION
          write(iu_g,asynchronous='yes') ZERO, ZERO
          write(iu_g,asynchronous='yes') id
          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( fwh_output_fixed(ib) == APERIODIC ) then
            write(iu_g,asynchronous='yes') num
          else if( fwh_output_fixed(ib) == PERIODIC ) then
            write(iu_g,asynchronous='yes') fwh_period, num
          end if

          write(iu_g,asynchronous='yes') fwh_bndry_points(ib)
          write(iu_g,asynchronous='yes') ZERO
#endif
       endif

!-------------------- data unit
        fwh_unit_p(ib) = available_unit()
        iu_p       = fwh_unit_p(ib)
        filename_p = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_data.bin"
        if ( skeleton > 2 ) &
        write(*,'(a,i5,a,i5)') "Opening for header writes, iu_p=", iu_p
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
        if ( fwh_formatted ) then
          open(iu_p,file=filename_p,form='formatted',asynchronous='yes')
        else
          open(iu_p,file=filename_p,form='unformatted',access='stream',        &
               asynchronous='yes')
        endif
#else
        write(*,*) 'Compiler does not support asynchronous I/O.'
        call lmpi_die
#endif
        rewind(iu_p)

        if ( fwh_formatted ) then
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
          write(iu_p,*,asynchronous='yes') MAGIC_NUMBER
          write(iu_p,*,asynchronous='yes') FWH_VERSION, FWH_VERSION_SUB
          write(iu_p,*,asynchronous='yes') title
          write(iu_p,*,asynchronous='yes') FUNCTIONAL_FLOW_DATA
          write(iu_p,*,asynchronous='yes') 1
          write(iu_p,*,asynchronous='yes') UNSTRUCTURED
          if ( fwh_output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p,*,asynchronous='yes') PERIODIC
          else if( fwh_output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p,*,asynchronous='yes') APERIODIC
          else
            write(iu_p,*,asynchronous='yes') fwh_output_fixed(ib)
          endif
          write(iu_p,*,asynchronous='yes') NODE_CENTERED
!         write(iu_p,*) PRESSURE_DATA
          write(iu_p,*,asynchronous='yes') FLOW_PARAMETERS
          write(iu_p,*,asynchronous='yes') fwh_output_fixed(ib)
          write(iu_p,*,asynchronous='yes') SINGLE_PRECISION
          write(iu_p,*,asynchronous='yes') 1, ZERO
          write(iu_p,*,asynchronous='yes') 1, 1
          write(iu_p,*,asynchronous='yes') id

          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( fwh_output_fixed(ib) == APERIODIC ) then
            write(iu_p,*,asynchronous='yes') num
          else if( fwh_output_fixed(ib) == PERIODIC ) then
            write(iu_p,*,asynchronous='yes') fwh_period, num
          else if( fwh_output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p,*,asynchronous='yes') num
          else if( fwh_output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p,*,asynchronous='yes') fwh_period, num
          end if

          write(iu_p,*,asynchronous='yes') fwh_bndry_points(ib) ! npoints
#endif
        else
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
          write(iu_p,asynchronous='yes') MAGIC_NUMBER
          write(iu_p,asynchronous='yes') FWH_VERSION, FWH_VERSION_SUB
          write(iu_p,asynchronous='yes') title
          write(iu_p,asynchronous='yes') FUNCTIONAL_FLOW_DATA
          write(iu_p,asynchronous='yes') 1
          write(iu_p,asynchronous='yes') UNSTRUCTURED
          if ( fwh_output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p,asynchronous='yes') PERIODIC
          else if( fwh_output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p,asynchronous='yes') APERIODIC
          else
            write(iu_p,asynchronous='yes') fwh_output_fixed(ib)
          endif
          write(iu_p,asynchronous='yes') NODE_CENTERED
!         write(iu_p) PRESSURE_DATA
          write(iu_p,asynchronous='yes') FLOW_PARAMETERS
          write(iu_p,asynchronous='yes') fwh_output_fixed(ib)
          write(iu_p,asynchronous='yes') SINGLE_PRECISION
          write(iu_p,asynchronous='yes') 1, ZERO
          write(iu_p,asynchronous='yes') 1, 1
          write(iu_p,asynchronous='yes') id

          if (irest == 1 ) then
            num = ncyc/fwh_data_freq
          else
            num = ncyc/fwh_data_freq + 1
          end if
          fwh_period = real(dt * fwh_data_period(ib))
          if ( fwh_output_fixed(ib) == APERIODIC ) then
            write(iu_p,asynchronous='yes') num ! aperiodic case, # of time steps
          else if( fwh_output_fixed(ib) == PERIODIC ) then
            write(iu_p,asynchronous='yes') fwh_period, num ! periodic case,
                                                           ! period & # of time
                                                           ! steps
          else if( fwh_output_fixed(ib) == CONST_APERIODIC ) then
            write(iu_p,asynchronous='yes') num ! aperiodic case, # of time steps
          else if( fwh_output_fixed(ib) == CONST_PERIODIC ) then
            write(iu_p,asynchronous='yes') fwh_period, num
          end if

          write(iu_p,asynchronous='yes') fwh_bndry_points(ib) ! npoints
#endif

        endif

      end if zero_count

      endif set_boundaries

    end if master_writes_headers

    grid_gather_write: if (lmpi_master) then

      fwh_time = real(dt * fwh_output_count(ib) * fwh_data_freq)

      if ( verbose ) then
      write(*,*)
      write(*,'(a,i5,a,i5)') "Writing fwh_data file for for geometry ", ib
      write(*,'(a,1x,es15.5)') "fwh_time = ", fwh_time
      write(*,'(a,i5,a,i5)') "fwh_output_fixed(ib) = ", fwh_output_fixed(ib)
      endif

      write(id, '(I3.3)') ib
      id         = adjustl(id)
      iu_g       = fwh_unit_g(ib)
      filename_g = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_grid.bin"

      geometry_write:  if ( fwh_output_count(ib) == 0 ) then

        if ( skeleton > 2 ) write(*,'(a,a,a,i5,a,i5)') &
           "Writing to ",trim(filename_g)," for xyznormal writes, iu_g=", iu_g
      if ( fwh_formatted ) then
        normals_face_loop_test : do j = 1, fwh_bndry_points(ib)
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
          write(iu_g,'(6(1x,es15.8))',asynchronous='yes')                      &
          fwh_data(ib)%xyz(j,1), fwh_data(ib)%xyz(j,2), fwh_data(ib)%xyz(j,3), &
          fwh_data(ib)%normal(j,1), fwh_data(ib)%normal(j,2),                  &
          fwh_data(ib)%normal(j,3)
#endif
        end do normals_face_loop_test
      else
#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
        write(iu_g,asynchronous='yes') fwh_data(ib)%xyz
        write(iu_g,asynchronous='yes') fwh_data(ib)%normal
#endif
      endif
      close(iu_g)

      endif geometry_write

    end if grid_gather_write

    data_gather_write : if ( lmpi_master ) then

      write(id, '(I3.3)') ib
      id         = adjustl(id)
      iu_p       = fwh_unit_p(ib)
      filename_p = trim(title)//'_sampling_geom_'//trim(id)//"_fwh_data.bin"
      if ( skeleton > 2 )  write(*,'(a,a,a,i5,a,i5)') &
           "Writing to ",trim(filename_p)," for data writes, iu_p=", iu_p

#ifdef HAVE_FORTRAN_ASYNCHRONOUS_IO
      if ( (fwh_output_count(ib) == 0                                          &
         .and.  fwh_output_fixed(ib) == CONSTANT) .or.                         &
         .not. (fwh_output_fixed(ib) == CONSTANT) ) then

        if ( fwh_formatted ) then
          write(iu_p,*,asynchronous='yes') fwh_time
        else
          write(iu_p,asynchronous='yes') fwh_time
        endif

        if ( fwh_formatted ) then
          write(iu_p,'(5(1x,es15.8))',asynchronous='yes') fwh_data(ib)%solution
        else
          write(iu_p,asynchronous='yes') fwh_data(ib)%solution
        endif

      end if
#endif

    end if data_gather_write

    fwh_output_count(ib) = fwh_output_count(ib) + 1

  end subroutine fwh_surveyp_outputa


!======================== END_FWH_WRITE ======================================80
!
! finish up asynchronous FWH IO
!
!=============================================================================80
  subroutine end_fwh_write()

    use lmpi,             only : lmpi_master
    use sampling_headers, only : number_of_geometries, asynchronous_fwh

    integer :: i

  continue

    if ( .not. asynchronous_fwh ) return

    do i = 1, number_of_geometries

      if ( fwh_unit_g(i) > 0 ) then
        if ( lmpi_master ) then
          write(*,*) 'Closing asynchronous FW-H data file...'
        endif
        close(fwh_unit_g(i))
      endif

      if ( fwh_unit_p(i) > 0 ) then
        if ( lmpi_master ) then
          write(*,*) 'Closing asynchronous FW-H data file...'
        endif
        close(fwh_unit_p(i))
      endif

    end do

  end subroutine end_fwh_write


!=============================== TECPLOT_EDGE_OUTPUT =========================80
!
!  Routine to output a tecplot file containing field survey data
!
!=============================================================================80

  subroutine tecplot_edge_output( project, time_step, geom_number,             &
                                    n_output_variables, output_variables,      &
                                    append_timestep, label, append_history,    &
                                    adjoint_mode )

    use info_depr,            only : simulation_time, physical_timestep
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters
    use tecplot_io_helpers,   only : get_tec_file_extension
    use nml_sampling_output,  only : max_smp_vars
    use sampling_headers,     only : edge_count_master, q_master
    use file_utils,           only : file_exists

    character(len=80), intent(in) :: project
    integer,           intent(in) :: time_step
    integer,           intent(in) :: geom_number
    integer,           intent(in) :: n_output_variables
    logical,           intent(in) :: append_timestep
    character(len=80), intent(in) :: label
    logical,           intent(in) :: append_history, adjoint_mode

    character(len=80), dimension(max_smp_vars),  intent(in) :: output_variables

    integer, parameter :: iunit = 502
    integer            :: ib, first_bndry, last_bndry, n, edge

    character(len=3)   :: tec_file_extension
    character(len=80)  :: geom, step, restart_file
    character(len=256) :: filename, variable_list, file_title, zone_title

    real(dp)           :: time_val

  continue

    master_performs_output : if (lmpi_master) then

      if ( skeleton > 2 ) then
      write(*,'(2a,i0,a,i0)') " Writing Tecplot flow sampling file,",          &
                              " geometry ", geom_number, " for time step ",    &
                              time_step
      endif

!     set the file name

      write(geom,"(i0)") geom_number
      if (append_timestep) then
        if ( adjoint_mode .and. itime /= 0 ) then
          write(step,"(i0)")    physical_timestep
          step = '_timestep' // trim(adjustl(step))
        else
          write(step,"(i0)")    time_step+prior_iters
          step = '_timestep' // trim(adjustl(step))
        endif
      else
        step = ''
      end if

      if (itime == 0) then
        time_val = real(time_step+prior_iters, dp)
      else
        time_val = simulation_time
      end if

      restart_file = trim(adjustl(project))

      call get_tec_file_extension(tec_file_extension)

      filename = trim(adjustl(restart_file)) // '_tec_sampling_geom'           &
               // trim(adjustl(geom))  // trim(adjustl(step))                  &
               // '.' // trim(adjustl(tec_file_extension))

      if ( trim(adjustl(label)) /= '') then
        if ( append_history ) then
          filename = trim(adjustl(restart_file)) // '_'                        &
                  // trim(adjustl(label)) // '.'                               &
                  // trim(adjustl(tec_file_extension))
        else
          filename = trim(adjustl(restart_file)) // '_'                        &
                  // trim(adjustl(label))                                      &
                  // trim(adjustl(step)) // '.'                                &
                  // trim(adjustl(tec_file_extension))
         endif
       endif

!     set the file title

      file_title ="solution sampling"

!     create the list of variables

      variable_list = ''
      do n=1,n_output_variables
        variable_list = trim(adjustl(variable_list)) // ' ' //                 &
                        trim(adjustl(output_variables(n))) // ' '
      end do

!     set the zone title

      write(zone_title,*)  '"geometry ' // trim(adjustl(geom)) // '"'
      if ( trim(adjustl(label)) /= '' ) then
        write(zone_title,'(3a)')  '"' // trim(adjustl(label)) // '"'
      endif

      ib          = 1
      first_bndry = 1
      last_bndry  = 1
!     status      = 0  ! to get this far, sufficient memory must be available

      if (ib == first_bndry) then
        if ( file_exists(filename) ) then
          if ( append_history ) then
            open(iunit,file=filename,position='append')
          else
            open(iunit,file=filename)
          endif
        else
          open(iunit,file=filename)
          rewind(iunit)
        endif

        write(iunit,'(3a)') 'title="',trim(adjustl(file_title)),'"'
        write(iunit,'(2a)') 'variables = ', trim(adjustl(variable_list))
      end if

       write(iunit,'(a,i12,a)') 'zone,i=',edge_count_master,',f=block'
       do n = 1, n_output_variables
         write(iunit,'(10e14.6)') (q_master(n,edge), edge=1,edge_count_master)
       enddo
!   close output file after last boundary is written
      if (ib == last_bndry) then
        close(iunit)
      end if

      if (.false.) write(*,*) time_val  ! to supress compiler warning with
                                        ! call above commented out
      close(iunit)

    end if master_performs_output

  end subroutine tecplot_edge_output


!=============================== TECPLOT_EDGE_OUTPUT =========================80
!
!  Routine to output a tecplot file containing field survey data
!
!=============================================================================80

  subroutine tecplot_point_output( project, time_step, geom_number,            &
                                    n_output_variables, output_variables,      &
                                    append_timestep, label, adjoint_mode )

    use info_depr,            only : simulation_time, physical_timestep
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters
    use tecplot_io_helpers,   only : get_tec_file_extension
    use nml_sampling_output,  only : max_smp_vars
    use sampling_headers,     only : q_master, edge_count_master,              &
                                     sampling_strands

    character(len=80), intent(in) :: project
    integer,           intent(in) :: time_step
    integer,           intent(in) :: geom_number
    integer,           intent(in) :: n_output_variables
    logical,           intent(in) :: append_timestep, adjoint_mode
    character(len=80), intent(in) :: label

!   real(dp), dimension(:,:),     pointer :: point_data

    character(len=80), dimension(max_smp_vars),  intent(in) :: output_variables

    integer, parameter :: iunit = 201
    integer            :: ib, first_bndry, last_bndry, n, edge
    integer            :: strandid

    character(len=3)   :: tec_file_extension
    character(len=80)  :: geom, step, restart_file
    character(len=256) :: filename, variable_list, file_title, zone_title
    character(len=256) :: full_title

    real(dp)           :: time_val

  continue

    master_performs_output : if (lmpi_master) then

      if ( skeleton > 2 ) then
      write(*,'(2a,i0,a,i0)') " Writing Tecplot flow sampling file,",          &
                              " geometry ", geom_number, " for time step ",    &
                              time_step
      endif

!     set the file name

      write(geom,"(i0)") geom_number
      if (append_timestep) then
        if ( adjoint_mode .and. itime /= 0 ) then
          write(step,"(i0)")    physical_timestep
          step = '_timestep' // trim(adjustl(step))
        else
          write(step,"(i0)")    time_step+prior_iters
          step = '_timestep' // trim(adjustl(step))
        endif
      else
        step = ''
      end if

      if (itime == 0) then
        time_val = real(time_step+prior_iters, dp)
      else
        time_val = simulation_time
      end if

      restart_file = trim(adjustl(project))

      call get_tec_file_extension(tec_file_extension)

      filename = trim(adjustl(restart_file)) // '_tec_sampling_geom'           &
               // trim(adjustl(geom))  // trim(adjustl(step))                  &
               // '.' // trim(adjustl(tec_file_extension))

      if ( trim(adjustl(label)) /= '' ) then
        filename = trim(adjustl(restart_file)) // '_'                          &
                // trim(adjustl(label))                                        &
                // trim(adjustl(step)) // '.'                                  &
                // trim(adjustl(tec_file_extension))
       endif

!     set the file title

      file_title ="point-line sampling"

!     create the list of variables

      variable_list = ''
      do n=1,n_output_variables
        variable_list = trim(adjustl(variable_list)) // ' ' //                 &
                        trim(adjustl(output_variables(n))) // ' '
      end do

!     set the zone title

      write(zone_title,*)  '"geometry ' // trim(adjustl(geom)) // '"'
      if ( trim(adjustl(label)) /= '' ) then
        write(zone_title,'(3a)')  '"' // trim(adjustl(label)) // '"'
      endif

      strandid = 0
      if ( sampling_strands(geom_number) ) strandid = geom_number

      write(full_title,"(a,', solutiontime=',e14.7,', strandid=',i0)")         &
        trim(zone_title), real(time_val,dp), strandid

      ib          = 1
      first_bndry = 1
      last_bndry  = 1
!     status      = 0  ! to get this far, sufficient memory must be available

      if (ib == first_bndry) then
        open(unit=iunit,file=filename)
        rewind(iunit)
        write(iunit,'(3a)') 'title="',trim(adjustl(file_title)),'"'
        write(iunit,'(2a)') 'variables = ', trim(adjustl(variable_list))
      end if


       write(iunit,'(a,a,a,i12,a)') 'zone t=',trim(full_title),                &
                               ' ,i=',edge_count_master,',f=block'
       do n = 1, n_output_variables
         write(iunit,'(10e14.6)') (q_master(n,edge), edge=1,edge_count_master)
       enddo
!   close output file after last boundary is written
      if (ib == last_bndry) then
        close(iunit)
      end if

      if (.false.) write(*,*) time_val  ! to supress compiler warning with
                                        ! call above commented out
      close(iunit)

    end if master_performs_output

  end subroutine tecplot_point_output


!============================ TECPLOT_SCHLIEREN_OUTPUT =======================80
!
!  Routine to output a tecplot file containing schlieren data
!
!=============================================================================80
  subroutine tecplot_schlieren_output( project, time_step, geom_number,        &
                             nlines, nrow, ncol, nvars, output_data,           &
                             schlieren_variables, append_timestep,             &
                             frequency, label )

    use info_depr,            only : simulation_time
    use nml_nonlinear_solves, only : itime
    use io,                   only : prior_iters
    use tecplot_io_helpers,   only : write_schlieren_tec, get_tec_file_extension


    character(len=80),                          intent(in) :: project
    integer,                                    intent(in) :: time_step
    integer,                                    intent(in) :: geom_number
    integer,                                    intent(in) :: nlines
    integer,                                    intent(in) :: nrow
    integer,                                    intent(in) :: ncol
    integer,                                    intent(in) :: nvars
    real(dp),          dimension(nvars,nlines), intent(in) :: output_data
    character(len=80), dimension(nvars),    intent(in) :: schlieren_variables
    logical,                                    intent(in) :: append_timestep
    integer,                                    intent(in) :: frequency
    character(len=80),                          intent(in) :: label


    integer, parameter :: iunit = 201
    integer            :: n, proc_id

    character(len=3)   :: tec_file_extension
    character(len=80)  :: geom, step, restart_file
    character(len=256) :: filename, variable_list, file_title, zone_title

    real(dp)           :: time_val

  continue

!   master_performs_output : if (lmpi_master) then

    if ( skeleton > 2 ) then
    write(*,'(2a,i0,a,i0)') " Writing Tecplot schlieren file,",                &
                            " geometry ", geom_number, " for time step ",      &
                            time_step
    endif

!     set the file name

    write(geom,"(i0)") geom_number
    if (append_timestep) then
      write(step,"(i0)")    time_step+prior_iters
      step = '_timestep' // trim(adjustl(step))
    else
      step = ''
    end if

    if (itime == 0) then
      time_val = real(time_step+prior_iters, dp)
    else
      time_val = simulation_time
    end if

    restart_file = trim(adjustl(project))

    call get_tec_file_extension(tec_file_extension)

    filename = trim(adjustl(restart_file)) // '_tec_schlieren_geom'            &
             // trim(adjustl(geom))  // trim(adjustl(step))                    &
             // '.' // trim(adjustl(tec_file_extension))
    if ( trim(adjustl(label)) /= '' ) then
      filename = trim(adjustl(restart_file)) // '_'                            &
              // trim(adjustl(label))                                          &
              // trim(adjustl(step)) // '.'                                    &
              // trim(adjustl(tec_file_extension))
     endif

!     set the zone title

    write(zone_title,*)  '"geometry '// trim(adjustl(geom)) // '"'
      if ( trim(adjustl(label)) /= '' ) then
        write(zone_title,'(3a)')  '"' // trim(adjustl(label)) // '"'
      endif

!     set the file title

    file_title ="Schlieren Image"

!     create the list of variables

    variable_list = ''
    do n = 1, nvars
        variable_list = trim(adjustl(variable_list)) // ' ' //                 &
                        trim(adjustl(schlieren_variables(n))) // ' '
    end do

    proc_id = 0

    call write_schlieren_tec( nvars, nlines, nrow, ncol, output_data,          &
                            file_title, zone_title, variable_list,             &
                            filename, geom_number, proc_id, time_val,          &
                            frequency )

    close(iunit)

!   end if master_performs_output

  end subroutine tecplot_schlieren_output

!-------------------------- SERIAL_HISTORY_POINT_OUTPUT ----------------------80
!
!  Specialized file format for lost history writes of point data
!
!-----------------------------------------------------------------------------80

  subroutine serial_history_point_output( project, time_step, ivol,            &
                                 n_output_variables, label )

    use io,                 only : prior_iters
    use system_extensions,  only : se_open
    use file_utils,         only : rm, file_does_not_exist, file_exists        &
                                 , available_unit
    use string_utils,       only : strip, empty
    use sampling_headers,   only : init_write, have_points, sunit_pts          &
                                 , number_of_points, q_master                  &
                                 , edge_count_master, sample

    character(len=80), intent(in)                :: project
    integer,           intent(in)                :: time_step
    integer,           intent(in)                :: ivol
    integer,           intent(in)                :: n_output_variables
    character(len=80), intent(in)                :: label

    integer                      :: ipts
    integer                      :: n
    character(len=80)            :: filename
    character(len=80)            :: geom
    character(len=80)            :: geo_name

  continue

    master_print: if ( lmpi_master ) then

      geo_name = sample(ivol)%geo_type_name

      init_write_1: if ( init_write(ivol) .and. have_points(ivol) ) then

        sunit_pts(ivol) = available_unit()

        write(geom,'(i0)') ivol
        filename = strip(project)//'_sampling_point_'//strip(geom)//'.dat'
        if ( .not.empty(label) ) then
          filename = strip(project)//'_'//strip(label)//'.dat'
        end if

        if ( file_exists(filename) ) then
          if ( time_step+prior_iters == 0 ) then
            write(*,*) 'Fresh start -- removing '//trim(filename)
            call rm(filename)
          else
            write(*,*) 'Restart -- appending '//trim(filename)
            open(sunit_pts(ivol), file=filename, position='append')
          end if
        end if

        if ( file_does_not_exist(filename) ) then
          write(*,*) 'Creating '//trim(filename)
          call se_open( sunit_pts(ivol), file=filename )
          if ( geo_name == 'boundary_points' .or. &
               geo_name == 'volume_points' ) then
             do ipts = 1, number_of_points(ivol)
               write(sunit_pts(ivol),'(3(1x,es15.8))')                         &
               sample(ivol)%print_list(:,ipts)
             end do
         end if
        end if

      end if init_write_1

      init_write(ivol) = .false.

      geo_name_1: if ( geo_name == 'boundary_points' .or. &
                       geo_name == 'volume_points' ) then

        if ( skeleton > 2 ) then
          write(*,'(a,2(1x,i0))')                                              &
            'Writing serial history: time_step, prior_iters',                  &
            time_step, prior_iters
        end if
        write(sunit_pts(ivol),'(i10,50(1x,es14.6))') time_step+prior_iters,    &
          ((q_master(n,ipts),ipts=1,edge_count_master),n=1,n_output_variables)

      end if geo_name_1

    end if master_print

  end subroutine serial_history_point_output

!============================== WRITE_SCHLIEREN_DATA =========================80
!
!  Create schlieren image and call tecplot output routines
!
!=============================================================================80
  subroutine write_schlieren_data( grid, soln, ivol, time_step )

    use grid_helper,        only : grid_cell_unique
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use reconstruction,     only : get_vector_grad
    use element_defs,       only : max_node_per_cell
    use sampling_headers,   only : sampling_frequency                          &
                                 , n_schlieren_variables, schlieren_variables  &
                                 , append_timestep                             &
                                 , number_of_lines, label, sample              &
                                 , make_shadow
    use geometry_utils,     only : line_plane_point2
    use sampling_funclib,   only : get_span, is_inside_triangle2, face_gradrho &
                                 , is_inside_box_status, get_window_index      &
                                 , is_face_here_or_there
    use generic_gas_map,    only : n_density, n_momx, n_momz, n_pressure_k
    use solution_types,     only : generic_gas

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step

    real(dp), dimension(:,:), allocatable :: image_global
    real(dp), dimension(:,:), allocatable :: image_local
    real(dp), dimension(:,:), allocatable :: output_data
! x, y, z, rho and grad rho on intersected faces

!   character(len=3)                      :: type_cell
    real(dp), dimension(3)                :: px, py, pz
    real(dp), dimension(3)                :: r, n
    real(dp), dimension(5,8)              :: qp
    real(dp), dimension(3,8)              :: grad_rho
    real(dp), dimension(1:8)              :: qf1
    real(dp), dimension(1:8)              :: qf2

    real(dp), dimension(2,3)              :: span
    integer,  dimension(:  ), allocatable :: total_cell_vector

    real(dp)                              :: delta_z
    real(dp)                              :: r_index1, r_index2
    real(dp)                              :: delta_e_x1, delta_e_y1, delta_e_z1
    real(dp)                              :: delta_e_x2, delta_e_y2, delta_e_z2

    character(len=80)                     :: geo_name

    integer                               :: cell
    integer                               :: element_set
    integer                               :: node
    integer                               :: node_per_cell
!   integer                               :: index2
    integer                               :: total_cells
    integer                               :: ilines, jlines, line_index,n_lines
    integer                               :: i_start, i_fin, j_start, j_fin
    integer                               :: local_node
    integer                               :: local_count

    logical                               :: is_inside_box

    real(dp), dimension(3)              :: p1, p2, p3
    real(dp), dimension(3)              :: p, pf1, pf2
    real(dp), dimension(3)              :: t1

    logical  :: face1
!   real(dp) :: dist1, dist2, dist3, dist4
    logical,  dimension(6) :: face
    real(dp), dimension(6) :: dist
    integer :: nn1, nn2, nn3, nn4
    integer :: face_per_cell
    integer :: face_index
    real(dp) :: nx, ny, nz
    real(dp) :: nx1, ny1, nz1
    real(dp) :: nx2, ny2, nz2
    real(dp) :: xavg, yavg, zavg
    real(dp) :: xavg1, yavg1, zavg1
    real(dp) :: xavg2, yavg2, zavg2
    real(dp) :: term1, term2
    real(dp) :: cell_vol
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), parameter :: my_18th = 1.0_dp/18.0_dp
    real(dp) :: max_dist, min_dist
    integer  :: max_face, min_face
    real(dp), dimension(5,3) :: q
    real(dp), dimension(3,3) ::  gradrho
    integer :: frequency

    real(dp), parameter       :: glad_dale = 0.2257e-3 ! [m^3/kg]

    real(dp), dimension(3,sample(ivol)%number_of_lines) ::  shadow

    real(dp), dimension(:), allocatable :: gradx
    real(dp), dimension(:), allocatable :: grady
    real(dp), dimension(:), allocatable :: gradz

    continue

    frequency       = sampling_frequency(ivol)

    if ( skeleton > 2 ) write(*,*) 'geometry = ', ivol

    allocate(gradx(grid%nnodes01))
    allocate(grady(grid%nnodes01))
    allocate(gradz(grid%nnodes01))

    call get_vector_grad(soln%q_dof(n_density,:),                            &
                         gradx,                                              &
                         grady,                                              &
                         gradz,                                              &
                         grid%nnodes0, grid%nnodes01, grid%x, grid%y, grid%z,&
                         grid%nedgeloc, grid%eptr, grid%symmetry )

    geo_name = sample(ivol)%geo_type_name

    if ( geo_name /= 'schlieren' ) return

    n_lines = sample(ivol)%number_of_lines
    allocate ( image_local  (3, n_lines ) )
    if ( skeleton > 2 ) write(*,*) 'allocate image_local'

    image_local  = 0.0_dp
    local_count  = 0
!  equation of plane n(1)*x + n(2)*y + n(3)*z + d = 0
!  all points in the plane satisfy n . p = k
!  any point a in the plane satisfies n . ( p - a ) = 0

    i_start = 0
    i_fin   = 0
    j_start = 0
    j_fin   = 0

! span(1,:) - minimum coordinates of element
! span(2,:) - maximum coordinates of element
!-----------------------------------------------------------------------------80
!------------  element type count --------------------------------------------80
!-----------------------------------------------------------------------------80
    element_type_count:  do element_set = 1, grid%nelem

      face_per_cell =  grid%elem(element_set)%face_per_cell
      node_per_cell =  grid%elem(element_set)%node_per_cell
!     type_cell     =  grid%elem(element_set)%type_cell

!-----------------------------------------------------------------------------80
!--------------- cell loop for each element type -----------------------------80
!-----------------------------------------------------------------------------80
      element_loop : do cell = 1, grid%elem(element_set)%ncell

        span = get_span ( grid, element_set, cell, node_per_cell )
        is_inside_box = is_inside_box_status( ivol, span )

!           if ( sample(ivol)%plot_lines ) then
!            if ( element_set == 1 ) then
!              if ( sample(ivol)%l2c_local_count(ilines) > 0 ) then
!              write(lmpi_id+250,'(a)') 'variables=x,y,z'
!              write(lmpi_id+250,'(a,i10,a,i10,a)') 'zone,n='              &
!              ,4*sample(ivol)%l2c_local_count(ilines)                     &
!              ,',e=',sample(ivol)%l2c_local_count(ilines)                 &
!              ,',zonetype=fetetrahedron,datapacking=block'
!            endif
!          endif

        is_unique: if ( is_inside_box .and.                                    &
              grid_cell_unique(grid,grid%elem(element_set)%c2n(1:4,cell) )     &
               ) then

        call get_window_index( ivol, span, i_start, i_fin, j_start, j_fin )
!-----------------------------------------------------------------------------80
        row_loop: do ilines = i_start, i_fin
!-----------------------------------------------------------------------------80
          column_loop: do jlines = j_start, j_fin

          px       = 0.0_dp
          py       = 0.0_dp
          pz       = 0.0_dp
          qp       = 0.0_dp
          p        = 0.0_dp
          grad_rho = 0.0_dp
          max_dist = 0.0_dp
          max_face = -99
          min_dist = huge(1.0)
          min_face = 99
          dist     = -1.0_dp
          face     = .false.
          face1    = .false.
          cell_vol = 0.0_dp

          line_index = (ilines-1)*sample(ivol)%number_of_rows + jlines

          image_blank: if ( sample(ivol)%l2c_local_image(line_index) == 0 ) then

              r(1:3)   = sample(ivol)%line_list(1:3,line_index)
              n(1:3)   = sample(ivol)%window_normal(1:3)
!             px(0)    = sample(ivol)%line_list(1,line_index)
!             py(0)    = sample(ivol)%line_list(2,line_index)
!             pz(0)    = sample(ivol)%line_list(3,line_index)

! load element geometry
              do node = 1, node_per_cell

                local_node       = grid%elem(element_set)%c2n(node,cell)
                x_node(node)     = grid%x(local_node)
                y_node(node)     = grid%y(local_node)
                z_node(node)     = grid%z(local_node)

                if(soln%eqn_set == generic_gas)then
                  qp(1,node) = soln%q_dof(n_density,local_node)
                  qp(2:4,node) = soln%q_dof(n_momx:n_momz,local_node)/         &
                                 soln%q_dof(n_density,local_node)
                  qp(5,node) = soln%q_dof(n_pressure_k(1),local_node)
                else
                  qp(1:5,node) = soln%q_dof(1:5, local_node)
                end if
                grad_rho(1,node) = gradx(local_node)
                grad_rho(2,node) = grady(local_node)
                grad_rho(3,node) = gradz(local_node)

              enddo

! examine each face for intersection
              threed_faces: do face_index = 1, face_per_cell

                nn1 = grid%elem(element_set)%local_f2n(face_index,1)
                nn2 = grid%elem(element_set)%local_f2n(face_index,2)
                nn3 = grid%elem(element_set)%local_f2n(face_index,3)
                nn4 = grid%elem(element_set)%local_f2n(face_index,4)

        if (nn4 == nn1) then

          p1(1) = x_node(nn1); p1(2) = y_node(nn1); p1(3) = z_node(nn1)
          p2(1) = x_node(nn2); p2(2) = y_node(nn2); p2(3) = z_node(nn2)
          p3(1) = x_node(nn3); p3(2) = y_node(nn3); p3(3) = z_node(nn3)
          p     = line_plane_point2( p1, p2, p3, r, n )
!         if ( p(1) < negative_huge ) then
            t1               = p - r
            dist(face_index) = sqrt(dot_product(t1,t1))
            face(face_index) = is_inside_triangle2 ( p, p1, p2, p3 )
!         endif

          if ( face(face_index ) ) then
            face1 = face(face_index)
            call is_face_here_or_there( face_index  &
            , dist(face_index), max_dist, min_dist, max_face, min_face)
            if ( face_index == min_face ) then
              pf1 = p
              px(1:3)=(/p1(1),p2(1),p3(1)/)
              py(1:3)=(/p1(2),p2(2),p3(2)/)
              pz(1:3)=(/p1(3),p2(3),p3(3)/)
              q(1:5,1) = qp(1:5,nn1)
              q(1:5,2) = qp(1:5,nn2)
              q(1:5,3) = qp(1:5,nn3)
              gradrho(1:3,1) = grad_rho(1:3,nn1)
              gradrho(1:3,2) = grad_rho(1:3,nn2)
              gradrho(1:3,3) = grad_rho(1:3,nn3)
              qf1(1:8) = face_gradrho( px, py, pz, pf1, q, gradrho )
            endif
            if ( face_index == max_face ) then
              pf2 = p
              px(1:3)=(/p1(1),p2(1),p3(1)/)
              py(1:3)=(/p1(2),p2(2),p3(2)/)
              pz(1:3)=(/p1(3),p2(3),p3(3)/)
              q(1:5,1) = qp(1:5,nn1)
              q(1:5,2) = qp(1:5,nn2)
              q(1:5,3) = qp(1:5,nn3)
              gradrho(1:3,1) = grad_rho(1:3,nn1)
              gradrho(1:3,2) = grad_rho(1:3,nn2)
              gradrho(1:3,3) = grad_rho(1:3,nn3)
              qf2(1:8) = face_gradrho( px, py, pz, pf2, q, gradrho )
            endif
          endif


!       triangular faces of the cell

!       face normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))       &
             - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))       &
             - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))       &
             - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       cell volume contributions

          cell_vol = cell_vol + (xavg*nx + yavg*ny + zavg*nz)*my_18th

    else

          p1(1) = x_node(nn1); p1(2) = y_node(nn1); p1(3) = z_node(nn1)
          p2(1) = x_node(nn2); p2(2) = y_node(nn2); p2(3) = z_node(nn2)
          p3(1) = x_node(nn3); p3(2) = y_node(nn3); p3(3) = z_node(nn3)
          p     = line_plane_point2( p1, p2, p3, r, n )
!         if ( p(1) < negative_huge ) then
            t1               = p - r
            dist(face_index) = sqrt(dot_product(t1,t1))
            face(face_index) = is_inside_triangle2 ( p, p1, p2, p3 )
!         endif
          if ( face(face_index ) ) then
            face1 = face(face_index)
            call is_face_here_or_there( face_index  &
            , dist(face_index), max_dist, min_dist, max_face, min_face)
            if ( face_index == min_face ) then
              pf1 = p
              px(1:3)=(/p1(1),p2(1),p3(1)/)
              py(1:3)=(/p1(2),p2(2),p3(2)/)
              pz(1:3)=(/p1(3),p2(3),p3(3)/)
              q(1:5,1) = qp(1:5,nn1)
              q(1:5,2) = qp(1:5,nn2)
              q(1:5,3) = qp(1:5,nn3)
              gradrho(1:3,1) = grad_rho(1:3,nn1)
              gradrho(1:3,2) = grad_rho(1:3,nn2)
              gradrho(1:3,3) = grad_rho(1:3,nn3)
              qf1(1:8) = face_gradrho( px, py, pz, pf1, q, gradrho )
            endif
            if ( face_index == max_face ) then
              pf2 = p
              px(1:3)=(/p1(1),p2(1),p3(1)/)
              py(1:3)=(/p1(2),p2(2),p3(2)/)
              pz(1:3)=(/p1(3),p2(3),p3(3)/)
              q(1:5,1) = qp(1:5,nn1)
              q(1:5,2) = qp(1:5,nn2)
              q(1:5,3) = qp(1:5,nn3)
              gradrho(1:3,1) = grad_rho(1:3,nn1)
              gradrho(1:3,2) = grad_rho(1:3,nn2)
              gradrho(1:3,3) = grad_rho(1:3,nn3)
              qf2(1:8) = face_gradrho( px, py, pz, pf2, q, gradrho )
            endif
          endif


          if ( .not.face(face_index) ) then
            p1(1) = x_node(nn1); p1(2) = y_node(nn1); p1(3) = z_node(nn1)
            p2(1) = x_node(nn3); p2(2) = y_node(nn3); p2(3) = z_node(nn3)
            p3(1) = x_node(nn4); p3(2) = y_node(nn4); p3(3) = z_node(nn4)
            p     = line_plane_point2( p1, p2, p3, r, n )
!           if ( p(1) < negative_huge ) then
              t1               = p - r
              dist(face_index) = sqrt(dot_product(t1,t1))
              face(face_index) = is_inside_triangle2 ( p, p1, p2, p3 )
!           endif
            if ( face(face_index ) ) then
              face1 = face(face_index)
              call is_face_here_or_there( face_index  &
              , dist(face_index), max_dist, min_dist, max_face, min_face)
              if ( face_index == min_face ) then
                pf1 = p
                px(1:3)=(/p1(1),p2(1),p3(1)/)
                py(1:3)=(/p1(2),p2(2),p3(2)/)
                pz(1:3)=(/p1(3),p2(3),p3(3)/)
              q(1:5,1) = qp(1:5,nn1)
              q(1:5,2) = qp(1:5,nn2)
              q(1:5,3) = qp(1:5,nn3)
              gradrho(1:3,1) = grad_rho(1:3,nn1)
              gradrho(1:3,2) = grad_rho(1:3,nn2)
              gradrho(1:3,3) = grad_rho(1:3,nn3)
                qf1(1:8) = face_gradrho( px, py, pz, pf1, q, gradrho )
              endif
              if ( face_index == max_face ) then
                pf2 = p
                px(1:3)=(/p1(1),p2(1),p3(1)/)
                py(1:3)=(/p1(2),p2(2),p3(2)/)
                pz(1:3)=(/p1(3),p2(3),p3(3)/)
              q(1:5,1) = qp(1:5,nn1)
              q(1:5,2) = qp(1:5,nn2)
              q(1:5,3) = qp(1:5,nn3)
              gradrho(1:3,1) = grad_rho(1:3,nn1)
              gradrho(1:3,2) = grad_rho(1:3,nn2)
              gradrho(1:3,3) = grad_rho(1:3,nn3)
                qf2(1:8) = face_gradrho( px, py, pz, pf2, q, gradrho )
              endif
            endif
          endif

!       quadrilateral faces of the cell
!       break face up into triangles 1-2-3 and 1-3-4 and add together
!       triangle 1: 1-2-3
!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg1 = x_node(nn1) + x_node(nn2) + x_node(nn3)
          yavg1 = y_node(nn1) + y_node(nn2) + y_node(nn3)
          zavg1 = z_node(nn1) + z_node(nn2) + z_node(nn3)

!       triangle 1 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx1 = (y_node(nn2) - y_node(nn1))*(z_node(nn3) - z_node(nn1))      &
              - (z_node(nn2) - z_node(nn1))*(y_node(nn3) - y_node(nn1))
          ny1 = (z_node(nn2) - z_node(nn1))*(x_node(nn3) - x_node(nn1))      &
              - (x_node(nn2) - x_node(nn1))*(z_node(nn3) - z_node(nn1))
          nz1 = (x_node(nn2) - x_node(nn1))*(y_node(nn3) - y_node(nn1))      &
              - (y_node(nn2) - y_node(nn1))*(x_node(nn3) - x_node(nn1))

          term1 = xavg1*nx1 + yavg1*ny1 + zavg1*nz1

!       triangle 2: 1-3-4

!       face centroid (factor of 1/3 deferred till the
!       contribution to cell_vol is calculated)

          xavg2 = x_node(nn1) + x_node(nn3) + x_node(nn4)
          yavg2 = y_node(nn1) + y_node(nn3) + y_node(nn4)
          zavg2 = z_node(nn1) + z_node(nn3) + z_node(nn4)

!       triangle 2 normals (factor of 1/2 deferred till cell_vol
!       and gradient terms are calculated)

          nx2 = (y_node(nn3) - y_node(nn1))*(z_node(nn4) - z_node(nn1))      &
              - (z_node(nn3) - z_node(nn1))*(y_node(nn4) - y_node(nn1))
          ny2 = (z_node(nn3) - z_node(nn1))*(x_node(nn4) - x_node(nn1))      &
              - (x_node(nn3) - x_node(nn1))*(z_node(nn4) - z_node(nn1))
          nz2 = (x_node(nn3) - x_node(nn1))*(y_node(nn4) - y_node(nn1))      &
              - (y_node(nn3) - y_node(nn1))*(x_node(nn4) - x_node(nn1))

          term2 = xavg2*nx2 + yavg2*ny2 + zavg2*nz2

!       cell volume contributions

          cell_vol = cell_vol + (term1 + term2)*my_18th

        end if

      end do threed_faces

      delta_z = ( cell_vol ) ** (1.0_dp/3.0_dp)

              if ( face1 ) then

                local_count             = local_count + 1

                r_index1 = 1.0_dp + glad_dale * qf1(5)
                delta_e_x1 = glad_dale * qf1(6) / r_index1
                delta_e_y1 = glad_dale * qf1(7) / r_index1
                delta_e_z1 = glad_dale * qf1(8) / r_index1
                r_index2 = 1.0_dp + glad_dale * qf2(5)
                delta_e_x2 = glad_dale * qf2(6) / r_index2
                delta_e_y2 = glad_dale * qf2(7) / r_index2
                delta_e_z2 = glad_dale * qf2(8) / r_index2

                image_local(1,line_index) = image_local(1,line_index) &
                                    + 0.5_dp*(delta_e_x1+delta_e_x2) * delta_z
                image_local(2,line_index) = image_local(2,line_index) &
                                    + 0.5_dp*(delta_e_y1+delta_e_y2) * delta_z
                image_local(3,line_index) = image_local(3,line_index) &
                                    + 0.5_dp*(delta_e_z1+delta_e_z2) * delta_z


            if ( sample(ivol)%plot_lines ) then
!            if ( element_set == 1 ) then
!              if ( sample(ivol)%l2c_local_count(ilines) > 0 ) then

!              write(lmpi_id+250,'(10(1x,es12.6))')  ((                    &
!              grid%x(grid%elem(element_set)%c2n(node,                     &
!              sample(ivol)%l2c_local_cells(ilines,icell))),node=1,4),     &
!              icell = 1, sample(ivol)%l2c_local_count(ilines) )

!              write(lmpi_id+250,'(10(1x,es12.6))')  ((                    &
!              grid%y(grid%elem(element_set)%c2n(node,                     &
!              sample(ivol)%l2c_local_cells(ilines,icell))),node=1,4),     &
!              icell = 1, sample(ivol)%l2c_local_count(ilines) )

!              write(lmpi_id+250,'(10(1x,es12.6))') ((                     &
!              grid%z(grid%elem(element_set)%c2n(node,                     &
!              sample(ivol)%l2c_local_cells(ilines,icell))),node=1,4),     &
!              icell = 1, sample(ivol)%l2c_local_count(ilines) )

!              do icell = 1, sample(ivol)%l2c_local_count(ilines)
!              write(lmpi_id+250,'(4i10)') &
!              4*icell-3 ,4*icell-2,4*icell-1, 4*icell
!              enddo
!              endif
!          endif
           endif

              endif

            endif image_blank

          enddo column_loop

        enddo row_loop

      endif is_unique

    end do element_loop

  end do element_type_count

    deallocate(gradx)
    deallocate(grady)
    deallocate(gradz)

!-----------------------------------------------------------------------------80
! Gather pixel data from each partion in to a global image file
!-----------------------------------------------------------------------------80
    allocate ( image_global (3, n_lines ) )
    image_global = 0.0_dp
    call lmpi_reduce( image_local, image_global )
    call lmpi_bcast( image_global )

    allocate(total_cell_vector(lmpi_nproc)); total_cell_vector = 0
    call lmpi_allgather(local_count, total_cell_vector)
    total_cells = sum(total_cell_vector)

    if ( skeleton > 1 ) then
      write(*,'(a,128i8)') 'local cell hits=',total_cell_vector
      write(*,*) 'total cell hits   =',total_cells
    endif

    deallocate( total_cell_vector )
    deallocate( image_local  )

    if ( lmpi_master ) then
!------------------------------------------------------------------------------
! Prepare data for tecplot calling routines
    n_schlieren_variables  = 7
    allocate( output_data( n_schlieren_variables, n_lines ) )
!------------------------------------------------------------------------------
!     create the list of variables
      schlieren_variables(1) = 'x'
      schlieren_variables(2) = 'y'
      schlieren_variables(3) = 'z'
      schlieren_variables(4) = 'I<sub>x</sub>'
      schlieren_variables(5) = 'I<sub>y</sub>'
      schlieren_variables(6) = 'I<sub>z</sub>'
      schlieren_variables(7) = 'iblank'

      if ( make_shadow ) then
        call shadow_image ( number_of_lines(ivol), image_global, shadow )
      endif

      do ilines = 1, number_of_lines(ivol)

        output_data(1,ilines) = sample(ivol)%line_list(1,ilines)
        output_data(2,ilines) = sample(ivol)%line_list(2,ilines)
        output_data(3,ilines) = sample(ivol)%line_list(3,ilines)
        if ( make_shadow ) then
        output_data(4,ilines) = shadow(1,ilines)
        output_data(5,ilines) = shadow(2,ilines)
        output_data(6,ilines) = shadow(3,ilines)
        else
        output_data(4,ilines) = image_global(1,ilines)
        output_data(5,ilines) = image_global(2,ilines)
        output_data(6,ilines) = image_global(3,ilines)
        endif
        output_data(7,ilines) = float(sample(ivol)%l2c_local_image(ilines))

      enddo

      call tecplot_schlieren_output( grid%project, time_step, ivol,            &
                                    number_of_lines(ivol),                     &
                                    sample(ivol)%number_of_rows,               &
                                    sample(ivol)%number_of_columns,            &
                                    n_schlieren_variables,                     &
                                    output_data,                               &
                                    schlieren_variables, append_timestep(ivol),&
                                    frequency, label(ivol) )

      deallocate ( output_data )

    endif

    deallocate ( image_global )

  end subroutine write_schlieren_data

!----------------------------- SHADOW_IMAGE ----------------------------------80
!------------------------------------------------------------------------------
! shadowgraph post-processing - reference shadowgraph.f90 written
! by David Saunders, ELORET Corporation/NASA Ames Research Center, CA
!
!     Derive the indicated image type from the integrated functions of
!     refractive index.  Use the same pixel coordinates as the array of
!     incident rays, since deflections are tiny.  [Is this naive?]
!     Note that tan (eps) ~eps for these small deflections.
!
! Modified j-r.carlson, May 2011 to deal with all 3 direction, modified
!                       array shapes
!------------------------------------------------------------------------------
  subroutine shadow_image ( n_lines, image_global, shadow )

    use sampling_funclib, only : stddev

    integer,                         intent(in)  :: n_lines
    real(dp), dimension(3, n_lines), intent(in)  :: image_global
    real(dp), dimension(3, n_lines), intent(out) :: shadow

!     Local variables:

    integer  :: i
    real(dp) :: epsx_min, epsy_min, epsz_min
    real(dp) :: epsx_max, epsy_max, epsz_max
    real(dp) :: eps_scale
!   real(dp) :: x_deflected, y_deflected, z_deflected
!   real(dp) :: x1, y1, z1
    real(dp) :: xmean, ymean, zmean
    real(dp) :: xsd, ysd, zsd
    real(dp) :: dlimit, dmax
!   real(dp) :: raw
    real(dp) :: clip_factor
    real(dp) :: xlimit1, ylimit1, zlimit1
    real(dp) :: xlimit2, ylimit2, zlimit2
!     Local constants:

    real(dp), parameter :: angle_limit = 0.07  ! Radians, ~4 degrees
    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: one = 1.0_dp

  continue

    shadow      = zero
    clip_factor = one

!   x1 = line_list(1,1)
!   y1 = line_list(2,1)
!   z1 = line_list(3,1)

!           Caution:  Heuristics ahead!
!           Shift/scale the integrated deflection angles so that they don't
!           exceed the range of validity of tan (eps) ~eps, which is about
!           +/4 degrees ~+/- 0.07 radians.  Then the camera distance can
!           vary the absolute distance deflections.

    epsx_max = image_global(1,1);  epsx_min = epsx_max
    epsy_max = image_global(2,1);  epsy_min = epsy_max
    epsz_max = image_global(3,1);  epsz_min = epsz_max

    do i = 1, n_lines
      epsx_max = max (image_global(1,i), epsx_max)
      epsx_min = min (image_global(1,i), epsx_min)
      epsy_max = max (image_global(2,i), epsy_max)
      epsy_min = min (image_global(2,i), epsy_min)
      epsz_max = max (image_global(3,i), epsz_max)
      epsz_min = min (image_global(3,i), epsz_min)
    end do

                                   dmax = abs(epsx_max)
      if ( abs (epsx_min) > dmax ) dmax = abs(epsx_min)
      if ( abs (epsy_max) > dmax ) dmax = abs(epsx_max)
      if ( abs (epsy_min) > dmax ) dmax = abs(epsx_min)
      if ( abs (epsz_max) > dmax ) dmax = abs(epsx_max)
      if ( abs (epsz_min) > dmax ) dmax = abs(epsx_min)

      eps_scale = angle_limit / dmax

!           However ...
!           Propulsion plumes can give extreme data ranges where the large
!           values swamp the smaller values of interest.  Therefore, scale
!           the deflections based on their standard deviation, not their max.
!           In this call to a one-pass method, 3 is the stride:

      call stddev (n_lines, image_global(1,:), xmean, xsd)
      call stddev (n_lines, image_global(2,:), ymean, ysd)
      call stddev (n_lines, image_global(3,:), zmean, zsd)

      write (6, '(/, a, 1p, 3e14.6)') &
         'Mean x,y, z deflections (raw/unscaled):     ', xmean, ymean, zmean
      write (6, '(a, 1p, 3e14.6)') &
         'Corresponding standard deviations:          ', xsd, ysd, zsd
      write (6, '(/, a, 1p, e14.6)') &
         'Max/min-based scale for deflection angles:  ', eps_scale

      dlimit = clip_factor * max (xsd, ysd, zsd)  ! Large clip has no effect
      write(6,'(a,1x,es15.5)') 'dlimit =',dlimit
      write(6,'(a,1x,es15.5)') 'dmax   =',dmax

      if (dlimit < dmax) then

         eps_scale = angle_limit / dlimit

         write (6, '(/, a, 1p, e14.6)') &
            'Standard-deviation-based scale (initial):   ', eps_scale

         xlimit1 = xmean - dlimit;  xlimit2 = xmean + dlimit
         ylimit1 = ymean - dlimit;  ylimit2 = ymean + dlimit
         zlimit1 = zmean - dlimit;  zlimit2 = zmean + dlimit

         write(6,'(a,2(1x,es15.5))') 'xlimit =',xlimit1, xlimit2
         write(6,'(a,2(1x,es15.5))') 'ylimit =',ylimit1, ylimit2
         write(6,'(a,2(1x,es15.5))') 'zlimit =',zlimit1, zlimit2

       do i = 1, n_lines
         if (image_global(1,i) == zero) cycle ! Ray missed CFD grid
         shadow(1,i) = max (xlimit1, min (xlimit2, image_global(1,i)))
         shadow(2,i) = max (ylimit1, min (ylimit2, image_global(2,i)))
         shadow(3,i) = max (zlimit1, min (zlimit2, image_global(3,i)))
       end do

         call stddev (n_lines, shadow(1,:), xmean, xsd)
         call stddev (n_lines, shadow(2,:), ymean, ysd)
         call stddev (n_lines, shadow(3,:), zmean, zsd)

         dlimit    = clip_factor * max (xsd, zsd)
         eps_scale = angle_limit / dlimit

         write (6, '(/, a, 1p, 2e14.6)') &
            'Mean x & z deflections (adjusted/unscaled): ', xmean, zmean
         write (6, '(a, 1p, 2e14.6)') &
            'Corresponding standard deviations:          ', xsd, zsd
         write (6, '(/, a, 1p, e14.6)') &
            'Standard-deviation-based scale (final):     ', eps_scale

      end if

!         eps_scale = camera_distance * eps_scale
!  Contouring the deflected offsets from the "lower left"pixel shows an
!  interesting view of the flow features when the two sets of contours
!  are overlaid.

!     do i = 1, n_lines
!           if (shadow(1,i) == zero) cycle ! No intersection w/ CFD

!           x_deflected = eps_scale*shadow(1,i) + ximage(i)
!           deflected_i(i) = (x_deflected - x1)/dx + one
!           ic = max (1, min (ipixels, nint ((x_deflected - x1)/dx + 1)))

!           y_deflected = eps_scale*shadow(2,i) + yimage(i)
!           deflected_j(i) = (y_deflected - y1)/dy + one
!           jc = max (1, min (jpixels, nint ((y_deflected - y1)/dy + 1)))

!           z_deflected = eps_scale*shadow(3,i) + zimage(i)
!           deflected_k(i) = (z_deflected - z1)/dz + one
!           kc = max (1, min (kpixels, nint ((z_deflected - z1)/dz + 1)))

!           fimage(i) = fimage(i) + one
!        end do
!     end do

  end subroutine shadow_image


!============================== WRITE_LINE_DATA ==============================80
!
!
!
!=============================================================================80
  subroutine write_line_data( grid, soln, ivol, time_step, sadj )

    use allocations,        only : my_alloc_ptr, my_realloc_ptr
    use grid_helper,        only : grid_cell_unique
    use grid_types,         only : grid_type
!   use lmpi,               only : lmpi_bcast, lmpi_max
    use solution_types,     only : soln_type
    use element_defs,       only : max_node_per_cell
    use sampling_headers,   only : verbose, number_of_points, p2c_local_cells  &
                                 , p2c_local_elem, sample                      &
                                 , point_is_found, point_is_found_global
    use sampling_funclib,   only : get_span, is_inside_cylinder                &
                                 , is_inside_triangle2, is_face_here_or_there
    use solution_adj,       only : sadj_type
    use geometry_utils,     only : line_plane_point2, line_is_parallel

    type(grid_type),                 intent(in)    :: grid
    type(soln_type),                 intent(inout) :: soln
    type(sadj_type), optional,       intent(in)    :: sadj
    integer,                         intent(in)    :: ivol
    integer,                         intent(in)    :: time_step

    real(dp), dimension(:,:), pointer :: point_list
    integer,  dimension(:  ), pointer :: p2c_local
    integer,  dimension(:  ), pointer :: p2e_local

    real(dp), dimension(3)                :: r, n, p, p1, p2, p3
    real(dp), dimension(3)                :: pf1
    real(dp), dimension(3)                :: pf2

    real(dp), dimension(2,3)              :: span

    real(dp)                              :: radius

    integer                               :: cell
    integer                               :: element_set
    integer                               :: node
    integer                               :: node_per_cell
    integer                               :: ipts
    integer                               :: local_node
    integer                               :: local_count
    integer                               :: n_pts_est
    integer                               :: test_dim, new_dim

    logical                               :: is_inside_box

    real(dp), dimension(3)              :: t1
    character(len=4)                    :: face_type

    logical  :: face1

    logical,  dimension(6) :: face_hit
    real(dp), dimension(6) :: dist
    integer :: nn1, nn2, nn3, nn4
    integer :: face_per_cell
    integer :: face_index
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp) :: dot, max_dist, min_dist
    integer  :: max_face, min_face

    real(dp), parameter                   :: half = 0.5_dp

  continue

    n_pts_est = 100

    nullify( point_list )
    if ( .not.associated(point_list) ) then
      call my_alloc_ptr( point_list, 3, n_pts_est )
    else
      test_dim  = size(point_list,2)/3
      if ( test_dim /= n_pts_est ) then
        call my_realloc_ptr( point_list, 3, n_pts_est )
      endif
    endif

    nullify( p2c_local )
    if ( .not.associated(p2c_local) ) then
      call my_alloc_ptr( p2c_local, n_pts_est )
    else
      test_dim  = size(p2c_local,1)
      if ( test_dim /= n_pts_est ) then
        call my_realloc_ptr( p2c_local, n_pts_est )
      endif
    endif

    nullify( p2e_local )
    if ( .not.associated(p2e_local) ) then
      call my_alloc_ptr( p2e_local, n_pts_est )
    else
      test_dim  = size(p2e_local,1)
      if ( test_dim /= n_pts_est ) then
        call my_realloc_ptr( p2e_local, n_pts_est )
      endif
    endif

    p2c_local = 0
    p2e_local = 0

      n       = sample(ivol)%p2_line - sample(ivol)%p1_line
      n       = n / sqrt(dot_product(n,n))
      r       = sample(ivol)%p1_line
      local_count = 0

! span(1,:) - minimum coordinates of element
! span(2,:) - maximum coordinates of element
!-----------------------------------------------------------------------------80
!------------  element type count --------------------------------------------80
!-----------------------------------------------------------------------------80
      element_type_count:  do element_set = 1, grid%nelem

        face_per_cell =  grid%elem(element_set)%face_per_cell
        node_per_cell =  grid%elem(element_set)%node_per_cell
!       type_cell     =  grid%elem(element_set)%type_cell

!-----------------------------------------------------------------------------80
!--------------- cell loop for each element type -----------------------------80
!-----------------------------------------------------------------------------80
        element_loop : do cell = 1, grid%elem(element_set)%ncell

          span = get_span ( grid, element_set, cell, node_per_cell )
          radius = 1.0_dp*sqrt( (span(1,1)-span(2,1))**2    &
                              + (span(1,2)-span(2,2))**2    &
                              + (span(1,3)-span(2,3))**2)
          p(1) = half*(span(1,1)+span(2,1))
          p(2) = half*(span(1,2)+span(2,2))
          p(3) = half*(span(1,3)+span(2,3))
          is_inside_box = is_inside_cylinder( p, sample(ivol)%p1_line &
                                               , sample(ivol)%p2_line, radius )
!         is_inside_box = .true.

        is_unique: if ( is_inside_box .and.                                    &
              grid_cell_unique(grid,grid%elem(element_set)%c2n(1:4,cell) )     &
               ) then

          max_dist = 0.0_dp
          max_face = -99
          min_dist = huge(1.0)
          min_face = 99
          dist     = -1.0_dp
          face_hit = .false.
          face1    = .false.
          pf1      = 0.0_dp
          pf2      = 0.0_dp

! load element geometry
          do node = 1, node_per_cell

            local_node       = grid%elem(element_set)%c2n(node,cell)
            x_node(node)     = grid%x(local_node)
            y_node(node)     = grid%y(local_node)
            z_node(node)     = grid%z(local_node)

          enddo
!-----------------------------------------------------------------------------80
!--------------------- face index loop ---------------------------------------80
!-----------------------------------------------------------------------------80
! examine each face for intersection
            threed_faces: do face_index = 1, face_per_cell

              nn1 = grid%elem(element_set)%local_f2n(face_index,1)
              nn2 = grid%elem(element_set)%local_f2n(face_index,2)
              nn3 = grid%elem(element_set)%local_f2n(face_index,3)
              nn4 = grid%elem(element_set)%local_f2n(face_index,4)

              face_type = 'quad'
              if (nn4 == nn1) face_type = 'tri'

              select case ( face_type)
              case( 'tri' )

                p1(1) = x_node(nn1); p1(2) = y_node(nn1); p1(3) = z_node(nn1)
                p2(1) = x_node(nn2); p2(2) = y_node(nn2); p2(3) = z_node(nn2)
                p3(1) = x_node(nn3); p3(2) = y_node(nn3); p3(3) = z_node(nn3)

                if ( line_is_parallel ( p1, p2, p3, n ) ) then
                  face_hit(face_index) = .false.
                else
                  p                    = line_plane_point2( p1, p2, p3, r, n )
                  t1                   = p - r
                  dist(face_index)     = sqrt(dot_product(t1,t1))
                  face_hit(face_index) = is_inside_triangle2 ( p, p1, p2, p3 )
                endif

                if ( face_hit(face_index ) ) then
                  face1 = face_hit(face_index)
                  call is_face_here_or_there( face_index  &
                  , dist(face_index), max_dist, min_dist, max_face, min_face)
                  if ( face_index == min_face ) then
                    pf1 = p
                  endif
                  if ( face_index == max_face ) then
                    pf2 = p
                  endif
                endif

              case( 'quad' )

! ToDo:  At SOME time in the future...there may occur a stradling point
! that won't end up in either triangle when cut along the 1-3 quad diagonal
!--> Possibly swap the diagonal and "try again" for the intersection

! split quad in to two tris and test each tri
! tri 1
                p1(1) = x_node(nn1); p1(2) = y_node(nn1); p1(3) = z_node(nn1)
                p2(1) = x_node(nn2); p2(2) = y_node(nn2); p2(3) = z_node(nn2)
                p3(1) = x_node(nn3); p3(2) = y_node(nn3); p3(3) = z_node(nn3)

                if ( line_is_parallel ( p1, p2, p3, n ) ) then
                  face_hit(face_index) = .false.
                  dist(face_index)     = 0.0_dp
                else
                  p                    = line_plane_point2( p1, p2, p3, r, n )
                  if (  ( p(1) < 1e+8_dp )    &
                  .and. ( p(2) < 1e+8_dp )    &
                  .and. ( p(3) < 1e+8_dp ) ) then
                    t1                   = p - r
                  else
                    t1                   = 0.0_dp
                  end if
                  dot                  = min(1e+12_dp, dot_product(t1,t1))
                  dist(face_index)     = sqrt(dot)
                  face_hit(face_index) = is_inside_triangle2 ( p, p1, p2, p3 )
                end if

                if ( face_hit(face_index ) ) then
                  face1 = face_hit(face_index)
                  call is_face_here_or_there( face_index  &
                  , dist(face_index), max_dist, min_dist, max_face, min_face)
                  if ( face_index == min_face ) then
                    pf1 = p
                  endif
                  if ( face_index == max_face ) then
                    pf2 = p
                  endif
                endif
! tri 2
              if ( .not.face_hit(face_index) ) then
                p1(1) = x_node(nn1); p1(2) = y_node(nn1); p1(3) = z_node(nn1)
                p2(1) = x_node(nn3); p2(2) = y_node(nn3); p2(3) = z_node(nn3)
                p3(1) = x_node(nn4); p3(2) = y_node(nn4); p3(3) = z_node(nn4)

                if ( line_is_parallel ( p1, p2, p3, n ) ) then
                  face_hit(face_index) = .false.
                  dist(face_index)     = 0.0_dp
                else
                  p                    = line_plane_point2( p1, p2, p3, r, n )
                  t1                   = p - r
                  dist(face_index)     = sqrt(dot_product(t1,t1))
                  face_hit(face_index) = is_inside_triangle2 ( p, p1, p2, p3 )
                endif

                if ( face_hit(face_index ) ) then
                  face1 = face_hit(face_index)
                  call is_face_here_or_there( face_index  &
                  , dist(face_index), max_dist, min_dist, max_face, min_face)
                  if ( face_index == min_face ) then
                    pf1 = p
                  endif
                  if ( face_index == max_face ) then
                    pf2 = p
                  endif
                  endif
                endif

              end select

            end do threed_faces
!-----------------------------------------------------------------------------80
!--------------------- face index loop ---------------------------------------80
!-----------------------------------------------------------------------------80

            int_face_true: if ( face1 ) then

            if ( sample(ivol)%plot_lines ) then
!            if ( element_set == 1 ) then
!              if ( sample(ivol)%l2c_local_count(ilines) > 0 ) then

!              write(lmpi_id+250,'(10(1x,es12.6))')  ((                    &
!              grid%x(grid%elem(element_set)%c2n(node,                     &
!              sample(ivol)%l2c_local_cells(ilines,icell))),node=1,4),     &
!              icell = 1, sample(ivol)%l2c_local_count(ilines) )

!              write(lmpi_id+250,'(10(1x,es12.6))')  ((                    &
!              grid%y(grid%elem(element_set)%c2n(node,                     &
!              sample(ivol)%l2c_local_cells(ilines,icell))),node=1,4),     &
!              icell = 1, sample(ivol)%l2c_local_count(ilines) )

!              write(lmpi_id+250,'(10(1x,es12.6))') ((                     &
!              grid%z(grid%elem(element_set)%c2n(node,                     &
!              sample(ivol)%l2c_local_cells(ilines,icell))),node=1,4),     &
!              icell = 1, sample(ivol)%l2c_local_count(ilines) )

!              do icell = 1, sample(ivol)%l2c_local_count(ilines)
!              write(lmpi_id+250,'(4i10)') &
!              4*icell-3 ,4*icell-2,4*icell-1, 4*icell
!              enddo
!              endif
!          endif
           endif


              local_count = local_count + 1
              test_dim    = size(p2c_local)
              if ( local_count > test_dim ) then
                new_dim   = test_dim * 1.25_dp
                call my_realloc_ptr( p2c_local , new_dim )
                call my_realloc_ptr( p2e_local , new_dim )
                call my_realloc_ptr( point_list, 3, new_dim )
                if ( verbose )                                             &
                write(6,'(a, i6,a,i6,a,i6)') 'On node ', lmpi_id,          &
                ', bumping p2c_local from ',test_dim,' to ', new_dim
              endif

              point_list(:,local_count) = half * ( pf1 + pf2 )
              p2c_local (  local_count) = cell
              p2e_local (  local_count) = element_set

!  if ( lmpi_id == 11 ) then
! write(6,'(a,i4,a,8l1,6(1x,es12.4))') &
!  'alternate',lmpi_id,type_cell,face(1:6)
! write(6,'(a,i4,a,8(1x,es12.4))') &
!  'alternate',lmpi_id,type_cell,dist(1:6)
! write(6,'(a,i4,a,6i8,6(1x,es12.4))') &
!  'alternate',lmpi_id,type_cell,max_face, min_face,cell, element_set
! write(6,'(a,i4,a,i4,8(1x,es12.4))') &
!  'alternate,pf',lmpi_id,type_cell,local_count,point_list(:,local_count)
!  endif
!  write(6,'(i5,1x,a,4i8,8(1x,es20.5))') lmpi_id,'line loop', local_count,   &
! p2c_local(local_count), p2e_local(local_count), number_of_points(ivol), &
! point_list(:,local_count)

            endif int_face_true

            number_of_points(ivol) = local_count


          endif is_unique

        end do element_loop

      end do element_type_count

    test_dim = size( p2c_local_cells, 2 )
    if ( local_count > test_dim ) then
          write(6,'(a, i6,a,i6,a,i6)') 'On node ', lmpi_id, &
         ', p2c_local_cells is ',test_dim,' but need ', local_count
    endif

    do ipts = 1, number_of_points(ivol)
      p2c_local_cells(ivol,ipts) = p2c_local(ipts)
      p2c_local_elem (ivol,ipts) = p2e_local(ipts)
      sample(ivol)%print_list(1:3,ipts) = point_list(1:3,ipts)
      point_is_found(ivol,ipts) = 1
    enddo

    call lmpi_reduce( point_is_found, point_is_found_global )
    call lmpi_bcast( point_is_found_global )

    nullify( p2c_local )
    nullify( p2e_local )
    nullify( point_list )

    call write_point_data( grid, soln, ivol, time_step, sadj )

  end subroutine write_line_data

end module sampling_output

!================================ QR_TANGENCY_I ==============================80
!
! Form tangency "right" state from "left" state assuming tangency (slip).
!
!=============================================================================80

  pure function qr_tangency_i( xnorm, ynorm, znorm, face_speed, ql )

    real(dp), intent(in) :: xnorm, ynorm, znorm, face_speed

    real(dp), dimension(4), intent(in) :: ql

    real(dp), dimension(4) :: qr_tangency_i

    real(dp) :: ubar

  continue

    ubar = xnorm*ql(2) + ynorm*ql(3) + znorm*ql(4) - face_speed

    qr_tangency_i(1) = ql(1)
    qr_tangency_i(2) = ql(2) - 2.0_dp*ubar*xnorm
    qr_tangency_i(3) = ql(3) - 2.0_dp*ubar*ynorm
    qr_tangency_i(4) = ql(4) - 2.0_dp*ubar*znorm

  end function qr_tangency_i

!================================= MAPPING_SYSTEM ============================80
!
! Coordinate system for approximate-mapping coordinates (xie,eta,zie).
!
!=============================================================================80

  pure function mapping_system( tx, ty, tz )

    use twod_util,       only : q_2d

    real(dp), intent(in) :: tx, ty, tz

    real(dp) :: lx, ly, lz, mx, my, mz, term

    real(dp), dimension(3,3) :: mapping_system

  continue

    !...tx, ty, tz is unit vector along distance normal direction.
    !   xie = tx*dx + ty*dy + tz*dz
    !   eta = lx*dx + ly*dy + lz*dz
    !   zie = mx*dx + my*dy + mz*dz

    if ( q_2d ) then
      lx = -tz
      lz =  tx
      ly = 0._dp
    else
      if(abs(tx)>0.577_dp)then
        ly = -tx
        lz =  tx
        lx = (ty-tz)
      else if(abs(ty)>0.577_dp)then
        lx =  ty
        lz = -ty
        ly = (tz-tx)
      else
        lx = -tz
        ly =  tz
        lz = (tx-ty)
      end if
      term = 1._dp/sqrt(lx*lx+ly*ly+lz*lz)
      lx = lx*term
      ly = ly*term
      lz = lz*term
    endif

    mx = ty*lz-tz*ly
    my = tz*lx-tx*lz
    mz = tx*ly-ty*lx

    mapping_system(1,1) = tx  !xie
    mapping_system(1,2) = ty
    mapping_system(1,3) = tz

    mapping_system(2,1) = lx  !eta
    mapping_system(2,2) = ly
    mapping_system(2,3) = lz

    mapping_system(3,1) = mx  !zie
    mapping_system(3,2) = my
    mapping_system(3,3) = mz

  end function mapping_system

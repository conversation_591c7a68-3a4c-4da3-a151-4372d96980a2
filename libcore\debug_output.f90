module debug_output

use kinddefs,   only : dp, system_r8
use lmpi,       only : lmpi_master

implicit none

private

public :: dbout

interface dbout

  module procedure r
  module procedure r1
  module procedure r2
  !module procedure r2_dq
  module procedure r3

  module procedure c
  module procedure c1
  module procedure c2
  module procedure c3

  module procedure i
  module procedure i1
  module procedure i2
  module procedure i3

end interface

contains

subroutine r(input, iname)

  real(dp), intent(in) :: input

  character(len=*), intent(in) :: iname

100 format(A,1x,A,1x,A,2000(1x,g11.4))

  if(lmpi_master) write(*,100) "CHECK:",iname,"=",input

end subroutine r

subroutine r1(input, iname, swap)

  real(dp), dimension (:), intent(in) :: input

  character(len=*),  intent(in) :: iname
  integer, optional, intent(in) :: swap

  integer :: i, size1


100 format(A,1x,A,1x,A,2000(1x,g11.4))

  if(present(swap)) then
    size1 = size(input,1)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(i)
    end do
  else
    if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(:)
  end if

end subroutine r1

subroutine r2(input, iname, swap)

  real(dp), dimension (:,:), intent(in) :: input

  character(len=*),  intent(in) :: iname
  integer, optional, intent(in) :: swap

  integer :: i, size1

100 format(A,1x,A,1x,A,2000(1x,g11.4))

  if(present(swap)) then
    size1 = size(input,2)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(:,i)
    end do
  else
    size1 = size(input,1)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(i,:)
    end do
  end if

end subroutine r2

subroutine r3(input, iname)

  real(dp), dimension (:,:,:), intent(in) :: input

  character(len=*),  intent(in) :: iname

  integer :: i, n, size1, size3

100 format(A,1x,A,1x,A,2000(1x,g11.4))

  size1 = size(input,1)
  size3 = size(input,3)

  do n = 1,size3
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(i,:,n)
    end do
    if(lmpi_master) write(*,*) "CHECK:"
  end do

end subroutine r3

subroutine i(input, iname)

  integer, intent(in) :: input

  character(len=*),  intent(in) :: iname

100 format(A,1x,A,1x,A,2000(1x,i11))

  if(lmpi_master) write(*,100) "CHECK:",iname,"=",input

end subroutine i

subroutine c(input, iname)

  complex(system_r8), intent(in) :: input
  character(len=*),   intent(in) :: iname

100 format(A,1x,A,1x,A,2000(1x,g11.4))

  if(lmpi_master) write(*,100) "CHECK: real",iname,"=",real(input,dp)
  if(lmpi_master) write(*,100) "CHECK: imag",iname,"=",aimag(input)

end subroutine c

subroutine c1(input, iname, swap)

  complex(system_r8), dimension (:), intent(in) :: input

  character(len=*),  intent(in) :: iname
  integer, optional, intent(in) :: swap

  integer :: i, size1


100 format(A,1x,A,1x,A,2000(1x,g11.4))

  if(present(swap)) then
    size1 = size(input,1)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: real",iname,"=",real(input(i),dp)
      if(lmpi_master) write(*,100) "CHECK: imag",iname,"=",aimag(input(i))
    end do
  else
    if(lmpi_master) write(*,100) "CHECK: real",iname,"=",real(input(:),dp)
    if(lmpi_master) write(*,100) "CHECK: imag",iname,"=",aimag(input(:))
  end if

end subroutine c1

subroutine c2(input, iname, swap)

  complex(system_r8), dimension (:,:), intent(in) :: input

  character(len=*),  intent(in) :: iname
  integer, optional, intent(in) :: swap

  integer :: i, size1

100 format(A,1x,A,1x,A,2000(1x,g11.4))

  if(present(swap)) then
    size1 = size(input,2)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: real",iname,"=",real(input(:,i),dp)
    end do
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: imag",iname,"=",aimag(input(:,i))
    end do
  else
    size1 = size(input,1)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: real",iname,"=",real(input(i,:),dp)
    end do
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: imag",iname,"=",aimag(input(i,:))
    end do
  end if

end subroutine c2

subroutine c3(input, iname)

  complex(system_r8), dimension (:,:,:), intent(in) :: input

  character(len=*),  intent(in) :: iname

  integer :: i, n, size1, size3

100 format(A,1x,A,1x,A,2000(1x,g11.4))

  size1 = size(input,1)
  size3 = size(input,3)

  do n = 1,size3
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: real",iname,"=",real(input(i,:,n),dp)
    end do
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK: imag",iname,"=",aimag(input(i,:,n))
    end do
    if(lmpi_master) write(*,*) "CHECK:"
  end do

end subroutine c3

subroutine i1(input, iname, swap)

  integer, dimension (:), intent(in) :: input

  character(len=*),  intent(in) :: iname
  integer, optional, intent(in) :: swap

  integer :: i, size1

100 format(A,1x,A,1x,A,2000(1x,i11))

  if(present(swap)) then
    size1 = size(input,1)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(i)
    end do
  else
    if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(:)
  end if

end subroutine i1

subroutine i2(input, iname, swap)

  integer, dimension (:,:), intent(in) :: input

  character(len=*),  intent(in) :: iname
  integer, optional, intent(in) :: swap

  integer :: i, size1

100 format(A,1x,A,1x,A,2000(1x,i11))

  if(present(swap)) then
    size1 = size(input,2)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(:,i)
    end do
  else
    size1 = size(input,2)
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(i,:)
    end do
  end if

end subroutine i2

subroutine i3(input, iname)

  integer, dimension (:,:,:), intent(in) :: input

  character(len=*),  intent(in) :: iname

  integer :: i, n, size1, size3

100 format(A,1x,A,1x,A,2000(1x,i11))

  size1 = size(input,1)
  size3 = size(input,3)

  do n = 1,size3
    do i = 1,size1
      if(lmpi_master) write(*,100) "CHECK:",iname,"=",input(i,:,n)
    end do
    if(lmpi_master) write(*,*) "CHECK:"
  end do

end subroutine i3

end module debug_output

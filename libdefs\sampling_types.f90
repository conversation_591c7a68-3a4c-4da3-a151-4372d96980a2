  module sampling_types

  use kinddefs,           only : dp

  implicit none

  public :: sample_type

  type sample_type

    character(len=80)      :: geo_type_name  ! type of slice geometry
    character(len=80)      :: label ! name for output file
    integer                :: geo_type       ! type of slice geometry
    integer                :: frequency
    logical                :: final_write
    logical                :: strands
    real(dp), dimension(3) :: pmin ! centroid
    real(dp), dimension(3) :: pmax ! centroid
    real(dp), dimension(3) :: po   ! centroid
    real(dp), dimension(3) :: upper_corner
    real(dp), dimension(3) :: lower_corner
    real(dp), dimension(3) :: center
    real(dp), dimension(3) :: vertex ! cone vertex
    real(dp)               :: cossq  ! square of cosine of cone angle
    real(dp)               :: radius
    real(dp), dimension(3) :: p1_line
    real(dp), dimension(3) :: p2_line
    real(dp), dimension(3) :: p1_cylinder
    real(dp), dimension(3) :: p2_cylinder
    real(dp)               :: r_cylinder
    real(dp), dimension(3) :: p1   ! first corner
    real(dp), dimension(3) :: p2   ! second corner
    real(dp), dimension(3) :: p3   ! third corner
    real(dp), dimension(3) :: p4   ! forth corner
    real(dp), dimension(3) :: p5   ! box lower first corner
    real(dp), dimension(3) :: p6   ! box lower second corner
    real(dp), dimension(3) :: p7   ! box lower third corner
    real(dp), dimension(3) :: p8   ! box lower forth corner
    integer                :: face_number
    real(dp), dimension(3) :: n    ! normal vector at po
    real(dp)               :: r0   ! radius for cyl/sphere/cone
    real(dp)               :: r1   ! radius for cyl/sphere/cone

    real(dp), dimension(3,10000) :: point_list       ! points
    real(dp), dimension(3,10000) :: print_list       ! points

    character(len=1)                         :: schlieren_aspect
    integer                                  :: number_of_rows
    integer                                  :: number_of_columns
    integer                                  :: number_of_lines
    real(dp)                                 :: window_height
    real(dp)                                 :: window_width
    real(dp), dimension(3)                   :: window_center
    real(dp), dimension(3)                   :: model_center
    real(dp), dimension(3)                   :: window_normal
    real(dp), dimension(3)                   :: theta_hat
    real(dp), dimension(3)                   :: phi_hat
    real(dp), dimension(3)                   :: r_hat
    real(dp), dimension(3,8)                 :: window_box
    real(dp)                                 :: delta_x
    real(dp)                                 :: delta_y
    logical                                  :: plot_lines
    integer ,  dimension(:  ), pointer       :: l2c_local_image => null()

    integer ,  dimension(:  ), pointer       :: blanking_list => null()
    integer ,  dimension(:  ), pointer       :: patch_list => null()

    real(dp)               :: x0_sphere     ! x-coordinate for sphere
    real(dp)               :: y0_sphere     ! y-coordinate for sphere
    real(dp)               :: z0_sphere     ! z-coordinate for sphere
    real(dp)               :: r0_sphere     ! radius for sphere

    real(dp), dimension(3) :: p0_cone ! vertex of cone
    real(dp), dimension(3) :: p1_cone ! first point for cylinder/cone axis
    real(dp), dimension(3) :: p2_cone ! second point for cylinder/cone axis
    real(dp)               :: r1_cone ! radius for cylinder/cone
    real(dp)               :: r2_cone ! radius for cylinder/cone

    real(dp)               :: x0_plane      ! x-coordinate for plane
    real(dp)               :: y0_plane      ! y-coordinate for plane
    real(dp)               :: z0_plane      ! z-coordinate for plane
    real(dp)               :: nx_plane      ! x-normal for plane
    real(dp)               :: ny_plane      ! y-normal for plane
    real(dp)               :: nz_plane      ! z-normal for plane

    real(dp), dimension(:,:), pointer :: donor_w1 => null()
    real(dp), dimension(:,:), pointer :: donor_w2 => null()
    integer,  dimension(:,:), pointer :: donor_n1 => null()
    integer,  dimension(:,:), pointer :: donor_n2 => null()
    integer                           :: n_tria
    integer                           :: total_survey_cells

    real(dp), dimension(:,:), pointer :: line_list => null()  ! lines
    integer,  dimension(:,:), pointer :: trinode_map => null()
    real(dp), dimension(:  ), pointer :: edge_weight => null()
    integer,  dimension(:,:), pointer :: edge_list => null()
    real(dp), dimension(:  ), pointer :: edge_w1 => null()
    real(dp), dimension(:  ), pointer :: edge_w2 => null()
    integer,  dimension(:  ), pointer :: edge_n1 => null()
    integer,  dimension(:  ), pointer :: edge_n2 => null()
    integer,  dimension(:  ), pointer :: total_edges => null()
    integer                           :: n_edges
    integer                           :: total_survey_edges

    real(dp)                          :: iso_val
    character(len=80)                 :: iso_variable
    real(dp)                          :: slen_min
    logical                           :: iso_box
    real(dp)                          :: x_lower
    real(dp)                          :: x_upper
    real(dp)                          :: y_lower
    real(dp)                          :: y_upper
    real(dp)                          :: z_lower
    real(dp)                          :: z_upper

  end type sample_type

end module sampling_types

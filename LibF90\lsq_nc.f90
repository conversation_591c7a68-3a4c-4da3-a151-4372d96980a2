module lsq_nc

  use kinddefs,        only : dp
  use twod_util,       only : yplane_2d, y_coplanar_tol
  use generic_gas_map, only : n_momx, n_momy, n_momz
  use cgamma_data,     only : cgamman
  use lmpi,            only : lmpi_conditional_stop, lmpi_id

  implicit none

  private

  public :: nlsq_lu, nlsq_grad, nlsq_max_edges

contains

!=========================== NLSQ_MAX_EDGES  =================================80
!
! Update edges for mapped least square.
!
!=============================================================================80
  subroutine nlsq_max_edges( fl, nnodes0, symmetry, x, y, z )

    use info_depr,       only : twod, skeleton
    use lsq_constants,   only : lsqn_max
    use lmpi,            only : lmpi_max

    integer,                intent(in) :: fl, nnodes0
    integer,  dimension(:), intent(in) :: symmetry
    real(dp), dimension(:), intent(in) :: x, y, z

    integer :: ii, node, nodea, nlsq_ni, n_edge, lsqn_maxg

    real(dp) :: dx, dy, dz

    logical, dimension(3) :: rx, ry, rz

  continue

    do node = 1,nnodes0

      if ( twod .and. ( abs(y(node)-yplane_2d) > y_coplanar_tol ) ) cycle

      nlsq_ni = 0
      do ii=1,cgamman(fl)%neighbors(node)%n
        nodea = cgamman(fl)%neighbors(node)%list(ii)
        if ( twod .and. abs(y(nodea)-yplane_2d) > y_coplanar_tol) cycle
        nlsq_ni = nlsq_ni + 1
        if ( symmetry(node) == 0 ) cycle
        dx = x(nodea) - x(node)
        dy = y(nodea) - y(node)
        dz = z(nodea) - z(node)
        call symmetry_reflections( symmetry(node), dx, dy, dz, &
                                   n_edge, rx, ry, rz )
        nlsq_ni = nlsq_ni + n_edge

      enddo

      lsqn_max = max( nlsq_ni, lsqn_max )

    enddo

    call lmpi_max( lsqn_max, lsqn_maxg )

    if ( skeleton > 0 ) then
      write(*,*) 'LSQ: Maximum edges reset via nlsq_max_edges (nc path):'
      write(*,*) 'LSQ:.....lsqn_max=', lsqn_maxg,' level=',fl
    endif


  end subroutine nlsq_max_edges

!================================== NLSQ_LU  =================================80
!
! Find the LU decomposition used to get gradients using mapped least squares.
!
!=============================================================================80
  subroutine nlsq_lu( fl, nnodes0, symmetry, x, y, z, cgamma,                  &
                      slen, slenxn, slenyn, slenzn, nlsq, nbound, bc )

    use lmpi,            only : lmpi_bcast, lmpi_reduce, lmpi_master, &
                                lmpi_max_and_maxid
    use fun3d_constants, only : my_1
    use info_depr,       only : twod, ngrid, grad_x_y_z_contents, testing
    use twod_util,       only : q_2d
    use lsq_constants,   only : lsqn_max
    use lsq_util,        only : lsq_lu_finalize
    use lsq_constants,   only : mlsq, tf, cg_tol
    use lsq_types,       only : lsq_ref_type
    use choleski_lapack, only : choleski_lu
    use lsq_defs,        only : lapack_uplo, type_fit, type_solve, fit_used
    use bc_types,        only : bcgrid_type
    use bc_names,        only : bc_ignore_2d


    integer,                    intent(in)  :: fl, nnodes0, nbound
    integer,  dimension(:),     intent(in)  :: symmetry
    real(dp), dimension(:),     intent(in)  :: x, y, z
    real(dp), dimension(:),     intent(in)  :: cgamma
    real(dp), dimension(:),     intent(in)  :: slen
    real(dp), dimension(:),     intent(in)  :: slenxn, slenyn, slenzn
    real(dp), dimension(:,:,:), intent(out) :: nlsq

    type(bcgrid_type), dimension(:), intent(in) :: bc

    type(lsq_ref_type) :: lsq_mrefs

    integer :: ii, jj, node, nodea, nlsq_ni, detfs, n_edge, edge
    integer :: ierr, serr, merr, ne, ipass, npass, werr
    integer, dimension(6) :: failed_quadratics, idum
    integer, dimension(2) :: quadratic_fits, jdum
    integer :: i, inode, j, ib, proc_at_max

    real(dp) :: det_tolerance, dx, dy, dz, dq, xa, ya, za, mcheck
    real(dp) :: ct_error, ct_error_max

    real(dp), parameter :: eps_fact = 1000.0_dp

    real(dp), dimension(4,lsqn_max) :: lc

    real(dp), dimension(4) :: sc, lc_max

    real(dp), dimension(3) :: adnlsq
    real(dp), dimension(9) :: bdnlsq, ct, ce

    real(dp), dimension(9)   :: l_vect
    real(dp), dimension(3,3) :: a, da, ta, a_temp
    real(dp), dimension(9,9) :: b, db, tb, b_temp

    logical, dimension(3) :: rx, ry, rz

    logical :: detf, debug

    logical, dimension(nnodes0) :: boundary_point

    logical, save :: first_time_here = .true.

    logical, dimension(:), allocatable, save :: first_time_on_grid_level

    character(len=40) :: dynamic_fit

  continue

    ct_error_max = 0._dp

    da(:,:) = 0.0_dp
    db(:,:) = 0.0_dp

    grad_x_y_z_contents = 'inviscid '// trim(type_fit) // ' LSQ'

    ! Allocate array on each level to track type of LSQ fit.
    ! May be reset during processing.

    if ( first_time_here ) then
      allocate(first_time_on_grid_level(ngrid))
      first_time_on_grid_level(:) = .true.
      first_time_here = .false.
      allocate(fit_used(ngrid))
    endif

    if ( first_time_on_grid_level(fl) ) then
      allocate(fit_used(fl)%fit(nnodes0))
      fit_used(fl)%fit(:) = trim(type_fit)
      first_time_on_grid_level(fl) = .false.
    endif

    boundary_point = .false.
    do ib = 1, nbound
      if ( twod .and. bc_ignore_2d(bc(ib)%ibc) ) cycle
      if ( testing .and. type_fit == 'quadratic' ) cycle
      do j = 1, bc(ib)%nbnode
        inode = bc(ib)%ibnode(j)
        if ( inode > nnodes0 ) cycle
        boundary_point(inode) = .true.
        fit_used(fl)%fit(inode) = 'linear'
      end do
    end do

    det_tolerance = epsilon( 1.0_dp )

    ierr = 0
    serr = 0
    merr = 0
    werr = 0
    detfs = 0
    failed_quadratics(:) = 0
    quadratic_fits(:)    = 0

    node_loop : do node = 1, nnodes0

      if ( twod .and. ( abs(y(node)-yplane_2d) > y_coplanar_tol ) ) cycle

      dynamic_fit = fit_used(fl)%fit(node)
      npass = 1
      if ( dynamic_fit == 'quadratic' ) then
        quadratic_fits(1) = quadratic_fits(1) + 1
        npass = 2
      endif

      !Count neighbors, including symmetry reflections.

      nlsq_ni = 0
      do ii=1,cgamman(fl)%neighbors(node)%n
        nodea = cgamman(fl)%neighbors(node)%list(ii)
        if ( twod .and. abs(y(nodea)-yplane_2d) > y_coplanar_tol) cycle
        nlsq_ni = nlsq_ni + 1
        if ( symmetry(node) == 0 ) cycle
        dx = x(nodea) - x(node)
        dy = y(nodea) - y(node)
        dz = z(nodea) - z(node)
        call symmetry_reflections( symmetry(node), dx, dy, dz,               &
                                   n_edge, rx, ry, rz )
        nlsq_ni = nlsq_ni + n_edge
      enddo

      if ( nlsq_ni > lsqn_max ) then
        write(*,*) ' nlsq_ni=',nlsq_ni,' lsqn_max=',lsqn_max
        serr = 1
        exit node_loop
      endif

      ! Initialize generalized mapping reference.

      lsq_mrefs = lsq_map_ref(q_2d,mlsq,cg_tol,x(node),y(node),z(node),      &
                              my_1,cgamma(node),slen(node),slenxn(node),     &
                              slenyn(node),slenzn(node))

      debug = .false.
     !if ( dynamic_fit == 'quadratic' ) debug= .true.
     !if ( lmpi_id == 5 .and. node == 330 ) debug = .true.
     !if ( lmpi_id == 0 .and. node == 21 ) debug = .true.
      if ( debug ) write(*,"(1x,a,i10,a,3f20.10,a,i5,a,L5)") &
      ' n0=',node,' xyz=',x(node),y(node),z(node),           &
      ' nlsq_ni=',nlsq_ni,' boundary=',boundary_point(node)

      jj     = 0
      n_edge = 0
      do ii=1,cgamman(fl)%neighbors(node)%n
        nodea = cgamman(fl)%neighbors(node)%list(ii)
        if ( twod .and. abs(y(nodea)-yplane_2d) > y_coplanar_tol) cycle

        if ( debug ) write(*,"(1x,a,i10,a,3f20.10,i4)") &
        ' na=',nodea,' xyz=',x(nodea),y(nodea),z(nodea),jj

        jj = jj + 1

        ! Local cordinates.

        lc(:,jj) = lsq_coords(lsq_mrefs,tf,x(nodea),y(nodea),z(nodea),       &
                               slen(nodea))

        if ( debug ) write(*,"(1x,a,i10,a,4f20.10,i4)") &
        ' na=',nodea,'  lc=',lc(:,jj),jj

        ! Account for symmetry reflections.

        if ( symmetry(node) /= 0 ) then
          dx = x(nodea) - x(node)
          dy = y(nodea) - y(node)
          dz = z(nodea) - z(node)
          call symmetry_reflections( symmetry(node), dx, dy, dz,             &
                                     n_edge, rx, ry, rz )
          do edge=1,n_edge
            jj = jj + 1

            xa = x(nodea)
            ya = y(nodea)
            za = z(nodea)
            if ( rx(edge) ) xa = x(node) - dx
            if ( ry(edge) ) ya = y(node) - dy
            if ( rz(edge) ) za = z(node) - dz
            lc(:,jj) = lsq_coords(lsq_mrefs,tf,xa, ya, za, slen(nodea))

            if ( debug ) write(*,"(1x,a,i10,a,3f20.10,a,3L5)") &
            ' ns=',nodea,' xyz=',xa,ya,za,                     &
            ' rx/ry/rz=',rx(edge),ry(edge),rz(edge)

            if ( debug ) write(*,"(1x,a,i10,a,4f20.10,i4)") &
            ' na=',nodea,'  lc=',lc(:,jj),jj

          enddo
        endif
      enddo

      ! Max of local weighted coordinates.

      lc_max = lsq_lc_max( jj, lc )

      pass_loop : do ipass = 1, npass

        dynamic_fit = fit_used(fl)%fit(node)

        if ( ipass == 2 ) dynamic_fit = 'linear'

        if ( debug ) write(*,"(1x,a,i2,1x,a)") &
        ' ipass,dynamic_fit=',ipass,trim(dynamic_fit)

        ne = 3
        if( dynamic_fit /= 'linear' ) ne=9

        a(:,:) = 0._dp
        b(:,:) = 0._dp

        if ( dynamic_fit == 'linear' ) then

          ! Scale the coordinates by the max and add contributions.

          do ii=1,jj
            sc(:)  = lsq_scoords( lc(:,ii), lc_max )
            a(:,:) = a(:,:) + wrsum( sc )
          enddo

          ! Add imaginary point for 2D (no dq contribution).

          if ( twod ) a(3,3) = a(3,3) + 1.0_dp

          ! If needed, fill lower part of symmetric matrix.

          if ( lapack_uplo == 'L' .and. &
               type_solve == 'choleski' ) call fill_symmetric_lower( a )

        elseif ( dynamic_fit == 'quadratic' ) then

          ! Scale the coordinates by the max and add contributions.

          do ii=1,jj
            sc(:)  = lsq_scoords( lc(:,ii), lc_max )
            b(:,:) = b(:,:) + wrsumq( sc )
          enddo

          ! Add imaginary point for 2D (no dq contribution).

          if ( twod ) then
            b(3,3) = b(3,3) + 1.0_dp
            b(6,6) = b(6,6) + 1.0_dp
            b(8,8) = b(8,8) + 1.0_dp
            b(9,9) = b(9,9) + 1.0_dp
          endif

          ! If needed, fill lower part of symmetric matrix.

          if ( lapack_uplo == 'L' ) call fill_symmetric_lower( b )

        endif

        ! Sanity check.

        ii = jj - nlsq_ni
        if ( ii /= 0 ) then
          ierr = ierr + 1
          write(*,"(1x,a,6(i0,1x))") ' Stopping...mismatch in nlsq_lu...',     &
          lmpi_id,node,symmetry(node),jj,nlsq_ni,n_edge
          exit node_loop
        endif

        ! Decompose the matrix.

        if ( dynamic_fit == 'linear' .and. type_solve == 'block-lu' ) then

          call lsq_lu_finalize( det_tolerance, a, detf )

          if ( detf ) detfs = detfs + 1
          if ( detfs > 0 ) then
            write(*,"(1x,2a,i0,a,i0)") 'Failed linear fit : lsq_lu_finalize',&
            ' lmpi_id=',lmpi_id,' node=',node
            exit node_loop
          endif

          nlsq(1:ne,1:ne,node) = a(1:ne,1:ne)
          fit_used(fl)%fit(node) = 'linear'

        elseif ( dynamic_fit == 'linear' ) then

          if ( debug ) then
            a_temp = a
            write(*,*) ' LSQ matrix...lc_max=',lc_max
            do i=1,3
              write(*,"(1x,a,i2,5e20.10/(6x,5e20.10))") ' i=',i,a(i,:)
            enddo
          endif

          call scale_symmetric_matrix( 3, a, l_vect, detfs )

          if ( detfs > 0 ) then
            write(*,"(1x,2a,i0,a,i0)") 'Failure linear LSQ scaling',&
            ' lmpi_id=',lmpi_id,' node=',node
            exit node_loop
          endif

          do i = 1, 3
           da(i,i) = l_vect(i)
          end do

          if ( debug ) then
            mcheck = 0._dp
            ta = matmul(da,a)
            ta = matmul(ta,da)
            do i=1,3
              do j=1,3
                mcheck = max( mcheck, abs( a_temp(i,j)-ta(i,j) ) )
              enddo
            enddo
            write(*,*) ' Recovered matrix max error=',mcheck
            if ( abs( mcheck ) > 100._dp*epsilon(1._dp) ) merr = merr + 1
          endif

          call choleski_lu( 3, a, detfs )
          if ( detfs > 0 ) then
            write(*,"(1x,2a,i0,a,i0)") 'Failed linear fit : choleski_lu',&
            ' lmpi_id=',lmpi_id,' node=',node
            exit node_loop
          else if ( detfs < 0 ) then
            write(*,"(1x,2a,i0,a,i0)") 'choleski_lu reports a bad argument',&
            ' lmpi_id=',lmpi_id,' node=',node
            detfs = -detfs
            exit node_loop
          endif

          do i = 1, 3
           da(i,i) = l_vect(i)
          end do

          if ( lapack_uplo == 'U' ) then
            a = matmul(a,da)
          else
            a = matmul(da,a)
          endif

          ! Compute a trial function - no symmetry.

          ct(1:3) = 0._dp
          ce(1:3) = 1._dp
          if ( twod ) then
            ce(3) = 0._dp
          endif
          do ii=1,jj
            sc(:) = lsq_scoords( lc(:,ii), lc_max )
            dq = ce(1)*sc(1) + ce(2)*sc(2) + ce(3)*sc(3)
            adnlsq(:) = sc(4)*weightsl(sc(1),sc(2),sc(3),a(1,1),werr)
            ct(1:3) = ct(1:3) + adnlsq(1:3)*dq
          enddo

          if ( debug ) write(*,*) 'ct(1:3)=',ct(1:3)

          ct(1:3) = ct(1:3) - ce(1:3)
          ct_error = sum( abs( ct(1:3) ) )
          ct_error_max = max( ct_error_max, ct_error )

          if ( werr > 0 ) exit node_loop

          nlsq(1:ne,1:ne,node) = a(1:ne,1:ne)
          fit_used(fl)%fit(node) = 'linear'

        elseif ( dynamic_fit == 'quadratic' ) then

          ! Count when number of neighbors insufficient.

          if ( ( .not.twod .and. jj < 9 ) .or. &
               (      twod .and. jj < 5 ) ) then
            failed_quadratics(5) = failed_quadratics(5) + 1
          endif

          if ( debug ) then
          b_temp = b
          call choleski_lu( 9, b_temp, detfs )

          ! Exit if choleski reports a problem with the matrix.

          if ( detfs > 0 ) then
            write(*,*) 'choleski_lu determinant failure...detfs=',detfs
          else if ( detfs < 0 ) then
            write(*,*) 'choleski_lu argument failure...detfs=',detfs
          endif
          endif

          if ( debug ) then
            b_temp = b
            write(*,*)
            write(*,*) ' Scaling:lc_max=',lc_max
            write(*,*)
            write(*,*) ' LSQ matrix(unscaled):'
            do i=1,9
              write(*,"(1x,a,i2,5e20.10/(6x,5e20.10))") ' i=',i,b(i,:)
            enddo
          endif

          call scale_symmetric_matrix( 9, b, l_vect, detfs )

          if ( detfs > 0 ) then
            failed_quadratics(1) = failed_quadratics(1) + 1
            write(*,"(1x,2a,i0,a,i0)") 'Failure quadratic LSQ scaling',&
            ' lmpi_id=',lmpi_id,' node=',node
            cycle pass_loop
          endif

          if ( debug ) then
            write(*,*)
            write(*,*) 'Diagonal of scaling matrix:'
            do i=1,9
              write(*,"(1x,a,i2,5e20.10/(6x,5e20.10))") ' i=',i,l_vect(i)
            enddo
            write(*,*)
            write(*,*) 'LSQ matrix(scaled):'
            do i=1,9
              write(*,"(1x,a,i2,5e20.10/(6x,5e20.10))") ' i=',i,b(i,:)
            enddo
          endif

          do i = 1, 9
           db(i,i) = l_vect(i)
          end do

          if ( debug ) then
            mcheck = 0._dp
            tb = matmul(db,b)
            tb = matmul(tb,db)
            do i=1,9
              do j=1,9
                mcheck = max( mcheck, abs( b_temp(i,j)-tb(i,j) ) )
              enddo
            enddo
            write(*,*) ' Recovered matrix max error=',mcheck
            if ( abs( mcheck ) > 100._dp*epsilon(1._dp) ) merr = merr + 1
          endif

          call choleski_lu( 9, b, detfs )

          ! Exit if choleski reports a problem with the matrix.

          if ( detfs > 0 ) then
            failed_quadratics(2) = failed_quadratics(2) + 1
            if ( debug .and. failed_quadratics(2) > 0 ) write(*,*) &
            ' detfs...xyz=',detfs,x(node),y(node),z(node)
            cycle pass_loop
          else if ( detfs < 0 ) then
            write(*,"(1x,2a,i0,a,i0)") 'choleski_lu reports a bad argument',&
            ' lmpi_id=',lmpi_id,' node=',node
            detfs = -detfs
            exit node_loop
          endif

          ! Add more restrictive criterion on the LSQ system
          ! based on the magnitude of the diagonal entries.

          if ( debug ) then
            do i=1,9
              write(*,"(1x,a,i2,9e12.5)") ' i=',i,b(i,:)
            enddo
            do i = 1, 9
              write(*,"(1x,a,i2,9e12.5)") &
              ' i=',i,b(i,i)*b(i,i),eps_fact*epsilon(1.0_dp)
            enddo
          endif

          do i = 1, 9
            if ( b(i,i)*b(i,i) < eps_fact*epsilon(1.0_dp) ) then
              failed_quadratics(3) = failed_quadratics(3) + 1
              if ( debug .and. failed_quadratics(3) > 0 ) write(*,*) &
              ' i,b*b...xyz=',i,b(i,i)*b(i,i),x(node),y(node),z(node)
              cycle pass_loop
            endif
          end do

          if ( lapack_uplo == 'U' ) then
            b = matmul(b,db)
          else
            b = matmul(db,b)
          endif

          ! Compute a trial function - no symmetry.

          ct(:) = 0._dp
          ce(1:9) = 1._dp
          if ( twod ) then
            ce(3) = 0._dp
            ce(6) = 0._dp
            ce(8) = 0._dp
            ce(9) = 0._dp
          endif
          do ii=1,jj
            sc(:) = lsq_scoords( lc(:,ii), lc_max )
            dq = ce(1)*sc(1)       + ce(2)*      sc(2) + ce(3)*      sc(3) &
               + ce(4)*sc(1)*sc(1) + ce(5)*sc(1)*sc(2) + ce(6)*sc(1)*sc(3) &
                                   + ce(7)*sc(2)*sc(2) + ce(8)*sc(2)*sc(3) &
                                                       + ce(9)*sc(3)*sc(3)
            bdnlsq(:) = sc(4)*weightsq(sc(1),sc(2),sc(3),b(1,1),werr)
            ct(:) = ct(:) + bdnlsq(:)*dq
          enddo

          if ( debug ) write(*,*) 'ct(1:9)=',ct(1:9)

          ct = ct - ce
          ct_error = sum( abs( ct ) )
          ct_error_max = max( ct_error_max, ct_error )

          if ( ct_error > 1000._dp*eps_fact*epsilon(1.0_dp) ) then
            failed_quadratics(4) = failed_quadratics(4) + 1
            write(*,"(1x,2a,i0,a,i0,a,e20.12)") 'Error in test function',&
            ' lmpi_id=',lmpi_id,' node=',node,' ct_error=',ct_error
            cycle pass_loop
          endif

          if ( werr > 0 ) exit node_loop

          ! Revert if number of neighbors insufficient.

          if ( ( .not.twod .and. jj < 9 ) .or. &
               (      twod .and. jj < 5 ) ) then
            write(*,"(1x,2a,i0,a,i0)") 'Insufficient points in stencil',&
            ' lmpi_id=',lmpi_id,' node=',node
            cycle pass_loop
          endif

          nlsq(1:ne,1:ne,node) = b(1:ne,1:ne)
          fit_used(fl)%fit(node) = 'quadratic'
          quadratic_fits(2) = quadratic_fits(2) + 1

          exit pass_loop

        endif

      end do pass_loop

    enddo node_loop

    if ( type_fit == 'quadratic' ) then
      failed_quadratics(6) = sum( failed_quadratics(1:5) )
      idum = failed_quadratics ; call lmpi_reduce(idum,failed_quadratics)
      if ( lmpi_master ) then
        write(*,"(i10,a)") failed_quadratics(1),&
        ' Failures:scaling'
        write(*,"(i10,a)") failed_quadratics(2),&
        ' Failures:choleski'
        write(*,"(i10,a)") failed_quadratics(3),&
        ' Failures:choleski:diagonal < tolerance'
        write(*,"(i10,a)") failed_quadratics(4),&
        ' Failures:choleski:test coefficents > tolerance'
        write(*,"(i10,a)") failed_quadratics(5),&
        ' Failures:expected:insufficient points'
      endif
      if ( lmpi_master ) write(*,"(1x,a,6(i0,1x))")  &
      'Failed quadratic fits= ',failed_quadratics(1:6)
      jdum = quadratic_fits ; call lmpi_reduce(jdum,quadratic_fits)
      if ( lmpi_master ) write(*,"(1x,a,5(i0,1x))")                    &
      'Quadratic fits:attempted/completed/failed=',quadratic_fits(1:2),&
      quadratic_fits(1)-quadratic_fits(2)
    endif

    call lmpi_conditional_stop(ierr,'accounting failure:nlsq_lu')
    call lmpi_conditional_stop(serr,'dimension failure:nlsq_lu')
    call lmpi_conditional_stop(merr,'scaling failure:nlsq_lu')
    call lmpi_conditional_stop(werr,'testing failure:nlsq_lu')

    ii=detfs
    call lmpi_reduce(ii,detfs)
    call lmpi_bcast(detfs)
    if ( detfs > 0 ) then
      if ( lmpi_master ) then
        write(*,*) 'Failures in nlsq_lu...stopping.'
        write(*,*) '...det_tolerance =',det_tolerance
        write(*,*) '...occurrences=',detfs
      endif
    endif
    call lmpi_conditional_stop(ii,'solve failure:nlsq_lu')

    call lmpi_max_and_maxid(real(ct_error_max,dp),proc_at_max)
    call lmpi_bcast(ct_error_max,proc_at_max)
    if ( lmpi_master ) write(*,"(1x,a,e20.12)")                    &
    'LSQ test function maximum error=',ct_error_max

  end subroutine nlsq_lu


!================================== NLSQ_GRAD  ===============================80
!
! Gradients using mapped least squares.
!
!=============================================================================80
  subroutine nlsq_grad( fl, n_mf, n_grd, q_dof, turb,                          &
                        gradx, grady, gradz, nnodes0,                          &
                        nnodes01, symmetry, x, y, z, cgamma, slen, slenxn,     &
                        slenyn, slenzn, nlsq, eqn_set, nbound, bc )

    use fun3d_constants, only : my_1
    use info_depr,       only : twod, grad_x_y_z_contents
    use twod_util,       only : q_2d
    use lsq_constants,   only : lsqn_max
    use lsq_types,       only : lsq_ref_type
    use lsq_constants,   only : mlsq, tf, cg_tol
    use lmpi,            only : lmpi_master, lmpi_reduce
    use lmpi_app,        only : lmpi_xfer
    use solution_types,  only : generic_gas
    use lsq_defs,        only : type_solve, fit_used, type_fit
    use bc_types,        only : bcgrid_type
    use bc_names,        only : bc_ignore_2d

    integer,                    intent(in)  :: fl, n_mf, n_grd
    integer,                    intent(in)  :: nnodes0, nnodes01
    integer,                    intent(in)  :: eqn_set, nbound
    integer,  dimension(:),     intent(in)  :: symmetry
    real(dp), dimension(:),     intent(in)  :: x, y, z
    real(dp), dimension(:),     intent(in)  :: cgamma, slen
    real(dp), dimension(:),     intent(in)  :: slenxn, slenyn, slenzn
    real(dp), dimension(:,:,:), intent(in)  :: nlsq
    real(dp), dimension(:,:),   intent(in)  :: q_dof, turb
    real(dp), dimension(:,:),   intent(out) :: gradx, grady, gradz

    type(bcgrid_type), dimension(:), intent(in) :: bc

    type(lsq_ref_type) :: lsq_mrefs

    integer :: ii, jj, node, nodea, nqq, nlsq_ni, ierr, werr
    integer :: surface_nodes_in_stencil, count_bad_surface_stencil
    integer :: edge, n_edge, ne, j, ib, inode, n_turb

    integer, dimension(lsqn_max) :: list, cx, cy, cz

    real(dp) :: dx, dy, dz, xa, ya, za

    real(dp), dimension(n_grd) :: dq, f1, f2, f3

    real(dp), dimension(9) :: bdnlsq
    real(dp), dimension(3) :: adnlsq

    real(dp), dimension(4, lsqn_max) :: lc

    real(dp), dimension(4) :: sc, lc_max

    logical, save :: first_time_through = .true.

    logical, dimension(3) :: rx, ry, rz

    real(dp), dimension(3,3) :: a
    real(dp), dimension(9,9) :: b

    logical, dimension(nnodes01) :: boundary_point

    logical :: debug

  continue

    grad_x_y_z_contents = 'mapped '// trim(type_fit) // ' LSQ'

    if ( eqn_set == generic_gas ) then
      call lmpi_conditional_stop(ierr,"not programmed:nlsq_grad")
    else
      nqq    = n_grd
      n_turb = nqq - n_mf
    end if

    boundary_point(:) = .false.

    do ib = 1, nbound
      if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle
      do j = 1, bc(ib)%nbnode
        inode = bc(ib)%ibnode(j)
        if ( inode > nnodes0 ) cycle
        boundary_point(inode) = .true.
      end do
    end do

    call lmpi_xfer(boundary_point)

    ierr = 0
    werr = 0

    count_bad_surface_stencil = 0

    node_loop : do node = 1, nnodes0

      if ( fit_used(fl)%fit(node) == 'linear' ) then
        ne = 3
        a(1:ne,1:ne) = nlsq(1:ne,1:ne,node)
      else
        ne = 9
        b(1:ne,1:ne) = nlsq(1:ne,1:ne,node)
      endif

      if ( twod .and. ( abs(y(node)-yplane_2d) > y_coplanar_tol ) ) cycle

      debug = .false.
     !if ( lmpi_id == 5 .and. node == 330 ) debug = .true.

      surface_nodes_in_stencil = 0

      if ( boundary_point(node) ) then
        surface_nodes_in_stencil = surface_nodes_in_stencil + 1
      endif

      nlsq_ni = 0
      do ii=1,cgamman(fl)%neighbors(node)%n
        nodea = cgamman(fl)%neighbors(node)%list(ii)
        if ( twod .and. abs(y(nodea)-yplane_2d) > y_coplanar_tol) cycle
        nlsq_ni = nlsq_ni + 1
        if ( symmetry(node) == 0 ) cycle
        dx = x(nodea) - x(node)
        dy = y(nodea) - y(node)
        dz = z(nodea) - z(node)
        call symmetry_reflections( symmetry(node), dx, dy, dz, &
                                   n_edge, rx, ry, rz )
        nlsq_ni = nlsq_ni + n_edge
      enddo

      lsq_mrefs = lsq_map_ref(q_2d,mlsq,cg_tol,x(node),y(node),z(node),      &
                              my_1,cgamma(node),slen(node),slenxn(node),     &
                              slenyn(node),slenzn(node))
      f1(:) = 0._dp
      f2(:) = 0._dp
      f3(:) = 0._dp

      jj = 0
      cx = 0
      cy = 0
      cz = 0
      do ii=1,cgamman(fl)%neighbors(node)%n
        nodea = cgamman(fl)%neighbors(node)%list(ii)
        if ( twod .and. abs(y(nodea)-yplane_2d) > y_coplanar_tol) cycle
        jj = jj + 1
        list(jj) = nodea
        if ( boundary_point(nodea) ) then
          surface_nodes_in_stencil = surface_nodes_in_stencil + 1
        endif
        lc(:,jj) = lsq_coords(lsq_mrefs,tf,x(nodea),y(nodea),z(nodea),       &
                               slen(nodea))
        if ( symmetry(node) /= 0 ) then
          dx = x(nodea) - x(node)
          dy = y(nodea) - y(node)
          dz = z(nodea) - z(node)
          call symmetry_reflections( symmetry(node), dx, dy, dz, &
                                     n_edge, rx, ry, rz )
          do edge=1,n_edge
            jj = jj + 1
            list(jj) = nodea
            xa = x(nodea)
            ya = y(nodea)
            za = z(nodea)
            if ( rx(edge) ) xa = x(node) - dx
            if ( ry(edge) ) ya = y(node) - dy
            if ( rz(edge) ) za = z(node) - dz
            if ( rx(edge) ) cx(jj) = n_momx
            if ( ry(edge) ) cy(jj) = n_momy
            if ( rz(edge) ) cz(jj) = n_momz
            lc(:,jj) = lsq_coords(lsq_mrefs,tf,xa, ya, za,       &
                                  slen(nodea))
          enddo
        endif
      enddo

      lc_max = lsq_lc_max( jj, lc )

!      if (surface_nodes_in_stencil == jj+1) then
!       gradx(1:nqq,node) = 0.0_dp
!       grady(1:nqq,node) = 0.0_dp
!       gradz(1:nqq,node) = 0.0_dp
!       count_bad_surface_stencil = count_bad_surface_stencil + 1
!       cycle
!     endif

      if ( fit_used(fl)%fit(node) == 'linear' ) then

        if ( type_solve == 'block-lu' ) then
          do ii=1,jj
            sc(:) = lsq_scoords( lc(:,ii), lc_max )
            dq(1:n_mf) = q_dof(1:n_mf,list(ii)) - q_dof(1:n_mf,node)
            if(cx(ii)/=0)dq(cx(ii)) = -q_dof(cx(ii),list(ii))-q_dof(cx(ii),node)
            if(cy(ii)/=0)dq(cy(ii)) = -q_dof(cy(ii),list(ii))-q_dof(cy(ii),node)
            if(cz(ii)/=0)dq(cz(ii)) = -q_dof(cz(ii),list(ii))-q_dof(cz(ii),node)
            if ( nqq > n_mf ) then
              dq(n_mf+1:nqq) = turb(1:n_turb,list(ii)) - turb(1:n_turb,node)
            endif
            adnlsq(:) = sc(4)*weights(sc(1),sc(2),sc(3),a(1,1))
            f1(1:nqq) = f1(1:nqq) + adnlsq(1)*dq(1:nqq)
            f2(1:nqq) = f2(1:nqq) + adnlsq(2)*dq(1:nqq)
            f3(1:nqq) = f3(1:nqq) + adnlsq(3)*dq(1:nqq)
          enddo
        else
          do ii=1,jj
            sc(:) = lsq_scoords( lc(:,ii), lc_max )
            dq(1:n_mf) = q_dof(1:n_mf,list(ii)) - q_dof(1:n_mf,node)
            if(cx(ii)/=0)dq(cx(ii)) = -q_dof(cx(ii),list(ii))-q_dof(cx(ii),node)
            if(cy(ii)/=0)dq(cy(ii)) = -q_dof(cy(ii),list(ii))-q_dof(cy(ii),node)
            if(cz(ii)/=0)dq(cz(ii)) = -q_dof(cz(ii),list(ii))-q_dof(cz(ii),node)
            if ( nqq > n_mf ) then
              dq(n_mf+1:nqq) = turb(1:n_turb,list(ii)) - turb(1:n_turb,node)
            endif
            adnlsq(:) = sc(4)*weightsl(sc(1),sc(2),sc(3),a(1,1),werr)
            f1(1:nqq) = f1(1:nqq) + adnlsq(1)*dq(1:nqq)
            f2(1:nqq) = f2(1:nqq) + adnlsq(2)*dq(1:nqq)
            f3(1:nqq) = f3(1:nqq) + adnlsq(3)*dq(1:nqq)
          enddo
        endif

      elseif ( fit_used(fl)%fit(node) == 'quadratic' ) then

        do ii=1,jj
          sc(:) = lsq_scoords( lc(:,ii), lc_max )
          dq(1:n_mf) = q_dof(1:n_mf,list(ii)) - q_dof(1:n_mf,node)
          if(cx(ii)/=0)dq(cx(ii)) = -q_dof(cx(ii),list(ii)) - q_dof(cx(ii),node)
          if(cy(ii)/=0)dq(cy(ii)) = -q_dof(cy(ii),list(ii)) - q_dof(cy(ii),node)
          if(cz(ii)/=0)dq(cz(ii)) = -q_dof(cz(ii),list(ii)) - q_dof(cz(ii),node)
          if ( nqq > n_mf ) then
            dq(n_mf+1:nqq) = turb(1:n_turb,list(ii)) - turb(1:n_turb,node)
          endif
          bdnlsq(:) = sc(4)*weightsq(sc(1),sc(2),sc(3),b(1,1),werr)
          f1(1:nqq) = f1(1:nqq) + bdnlsq(1)*dq(1:nqq)
          f2(1:nqq) = f2(1:nqq) + bdnlsq(2)*dq(1:nqq)
          f3(1:nqq) = f3(1:nqq) + bdnlsq(3)*dq(1:nqq)
        enddo

      endif

      if ( werr > 0 ) exit

      f1(1:nqq) = f1(1:nqq)/lc_max(1)
      f2(1:nqq) = f2(1:nqq)/lc_max(2)
      f3(1:nqq) = f3(1:nqq)/lc_max(3)

      ii = jj - nlsq_ni
      if ( ii /= 0 ) then
        ierr = ierr + 1
        write(*,"(1x,a,6(i0,1x))") ' Stopping...mismatch in nlsq_grad...',&
        lmpi_id,node,symmetry(node),jj,nlsq_ni,n_edge
        exit
      endif

      gradx(1:nqq,node) = lsq_grad_stn(nqq,  f1,f2,f3,                    &
                                   lsq_mrefs%scaleir,lsq_mrefs%tr(:,1))
      grady(1:nqq,node) = lsq_grad_stn(nqq,  f1,f2,f3,                    &
                                   lsq_mrefs%scaleir,lsq_mrefs%tr(:,2))
      gradz(1:nqq,node) = lsq_grad_stn(nqq,  f1,f2,f3,                    &
                                   lsq_mrefs%scaleir,lsq_mrefs%tr(:,3))

      if ( .not. debug ) cycle

      write(*,"(1x,a,6e12.5)") ' gradx=',gradx(1:nqq,node)
      write(*,"(1x,a,6e12.5)") ' grady=',grady(1:nqq,node)
      write(*,"(1x,a,6e12.5)") ' gradz=',gradz(1:nqq,node)

    enddo node_loop

    call lmpi_conditional_stop(werr,'weight-error:nlsq_grad')

    if (first_time_through) then
      first_time_through = .false.
      ii = count_bad_surface_stencil
      call lmpi_reduce(ii,count_bad_surface_stencil)
      if (lmpi_master .and. count_bad_surface_stencil > 0) then
        write(6,*) 'Stencils with all surface nodes in nlsq_grad.'
        write(6,*) '...Setting first order at these surface nodes.'
        write(6,*) '...count_bad_surface_stencil = ', count_bad_surface_stencil
      endif
    endif

    call lmpi_conditional_stop(ierr,"nlsq_grad")

  end subroutine nlsq_grad

!================================= WEIGHTSL ==================================80
!
! Weights in linear least square using LAPACK routines.
!
!=============================================================================80
  function weightsl( dx, dy, dz, lu_lapack, ierr )

    use choleski_lapack, only : choleski_solve

    real(dp),                 intent(in) :: dx, dy, dz
    real(dp), dimension(3,3), intent(in) :: lu_lapack
    integer,                  intent(out) :: ierr

    real(dp), dimension(3) :: weightsl

    real(dp), dimension(3,1) :: b_lapack

    integer :: n_lapack, nrhs_lapack

  continue

    n_lapack    = 3
    nrhs_lapack = 1
    ierr        = 0

    b_lapack(1,1) = dx
    b_lapack(2,1) = dy
    b_lapack(3,1) = dz

    call choleski_solve( n_lapack, nrhs_lapack, lu_lapack, b_lapack, ierr )

    weightsl(1:3) = b_lapack(1:3,1)

  end function weightsl

!================================= WEIGHTSQ ==================================80
!
! Weights in quadratic least square using LAPACK routines.
!
!=============================================================================80
  function weightsq( dx, dy, dz, lu_lapack, ierr  )

    use choleski_lapack, only : choleski_solve

    real(dp),                 intent(in)  :: dx, dy, dz
    real(dp), dimension(9,9), intent(in)  :: lu_lapack
    integer,                  intent(out) :: ierr

    real(dp), dimension(9) :: weightsq

    real(dp), dimension(9,1) :: b_lapack

    integer :: n_lapack, nrhs_lapack

  continue

    n_lapack    = 9
    nrhs_lapack = 1
    ierr        = 0

    b_lapack(1,1) = dx
    b_lapack(2,1) = dy
    b_lapack(3,1) = dz

    b_lapack(4,1) = dx*dx
    b_lapack(5,1) = dx*dy
    b_lapack(6,1) = dx*dz

    b_lapack(7,1) = dy*dy
    b_lapack(8,1) = dy*dz

    b_lapack(9,1) = dz*dz

    call choleski_solve( n_lapack, nrhs_lapack, lu_lapack, &
                         b_lapack, ierr )

    weightsq(1:9) = b_lapack(1:9,1)

  end function weightsq

!================================= WRSUM =====================================80
!
! Entries in nlsq summations (with wsq addition) for linear fit.
!
!=============================================================================80
  pure function wrsum( sc )

    real(dp), dimension(4), intent(in) :: sc

    real(dp), dimension(3,3) :: wrsum

    real(dp) :: dx, dy, dz, wsq

  continue

    dx  = sc(1) ; dy  = sc(2) ; dz  = sc(3) ; wsq = sc(4)

    !...upper triangular parts

    wrsum(1,1) = dx*dx*wsq
    wrsum(1,2) = dx*dy*wsq
    wrsum(1,3) = dx*dz*wsq
    wrsum(2,2) = dy*dy*wsq
    wrsum(2,3) = dy*dz*wsq
    wrsum(3,3) = dz*dz*wsq

    !...lower triangular - diagonal parts

    wrsum(2,1) = 0._dp
    wrsum(3,1) = 0._dp
    wrsum(3,2) = 0._dp

  end function wrsum

!================================= WRSUMQ ====================================80
!
! Entries in nlsq summations (with wsq addition) for quadratic fit.
!
!=============================================================================80
  pure function wrsumq( sc )

    real(dp), dimension(4), intent(in) :: sc

    real(dp), dimension(9,9) :: wrsumq

    real(dp) :: dx, dy, dz, wsq

    real(dp), dimension(9) :: terms

  continue

    wrsumq(:,:) = 0._dp

    dx  = sc(1) ; dy  = sc(2) ; dz  = sc(3) ; wsq = sc(4)

    terms(1) = dx*wsq
    terms(2) = dy*wsq
    terms(3) = dz*wsq

    terms(4) = dx*terms(1)
    terms(5) = dx*terms(2)
    terms(6) = dx*terms(3)

    terms(7) = dy*terms(2)
    terms(8) = dy*terms(3)

    terms(9) = dz*terms(3)

    !...upper triangular parts

    wrsumq(1,1:9) = dx*terms(1:9)

    wrsumq(2,1:9) = dy*terms(1:9)

    wrsumq(3,1:9) = dz*terms(1:9)

    wrsumq(4,1:9) = dx*wrsumq(1,1:9)

    wrsumq(5,1:9) = dx*wrsumq(2,1:9)

    wrsumq(6,1:9) = dx*wrsumq(3,1:9)

    wrsumq(7,1:9) = dy*wrsumq(2,1:9)

    wrsumq(8,1:9) = dy*wrsumq(3,1:9)

    wrsumq(9,1:9) = dz*wrsumq(3,1:9)

  end function wrsumq

!===================================== FILL_SYMMETRIC_LOWER ==================80
!
! Fill lower part of symmetric matrix.
!
!=============================================================================80

  subroutine fill_symmetric_lower( a )

    real(dp), intent(inout) :: a(:,:)

    integer :: i, j, n

  continue

    n = size(a,1)

    do i=1,n
      if ( i >= n ) cycle
      do j=1,n
        if ( j >= i ) cycle
        a(i,j) = a(j,i)
      enddo
    enddo

  end subroutine fill_symmetric_lower

!================================== SCALE_SYMMETRIC_MATRIX  ==================80
!
! Normalize a matrix by row (via D_left) and column (via D_right) scalings.
! A = D_left * A_scaled * D_right where D_left = D_right via symmetry.
!
!=============================================================================80
  subroutine scale_symmetric_matrix( n, b, l_vect, ierr )

    integer,                  intent(in)    :: n
    integer,                  intent(out)   :: ierr
    real(dp), dimension(:,:), intent(inout) :: b
    real(dp), dimension(:),   intent(out)   :: l_vect

    integer :: i, j, counter

    integer, dimension(1) :: biggest

    real(dp) :: max_value_in_row

    real(dp), parameter :: eps_fact = 1000.0_dp

    real(dp), dimension(n)   :: temp

  continue

    ierr = 0

    l_vect = 1.0_dp

    iterations : do j = 1, n-1
      counter = 0
      rows : do i = 1, n
        temp(:) = abs(b(i,:))
        temp(i) = sqrt(temp(i))
        biggest = maxloc(temp)  ! location of maximum value of temp(:)
        max_value_in_row = temp(biggest(1))

        if ( abs( max_value_in_row - 1.0_dp ) < 10.0_dp*epsilon(1.0_dp) ) then
          counter = counter + 1
          cycle rows
        endif

        if ( max_value_in_row < eps_fact*epsilon(1.0_dp) ) then
         ierr = i
         return
        endif

        b(i,:) = b(i,:) / max_value_in_row

        b(:,i) = b(:,i) / max_value_in_row

        l_vect(i) = l_vect(i)*max_value_in_row

      end do rows
      if ( counter == n ) exit iterations !early exit??
    end do iterations

  end subroutine scale_symmetric_matrix

!=============================== SYMMETRY_REFLECTIONS ========================80
!
! Returns virtual edges from symmetry boundary conditions.
!
!=============================================================================80
  subroutine symmetry_reflections(symmetry, dx, dy, dz, n_edge, rx, ry, rz )

    integer,               intent(in)   :: symmetry
    real(dp),              intent(in)   :: dx, dy, dz
    integer,               intent(out)  :: n_edge
    logical, dimension(3), intent(out)  :: rx, ry, rz

    real(dp), parameter :: symmetry_tol = 1.0e-08_dp

  continue

    n_edge = 0
    rx     = .false.
    ry     = .false.
    rz     = .false.

    if(symmetry == 100 .and. (abs(dx) > symmetry_tol)) then

      n_edge = 1
      rx(1)  = .true.

    elseif(symmetry == 010 .and. (abs(dy) > symmetry_tol)) then

      n_edge = 1
      ry(1)  = .true.

    elseif(symmetry == 001 .and. (abs(dz) > symmetry_tol)) then

      n_edge = 1
      rz(1)  = .true.

    elseif(symmetry == 110 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dy) > symmetry_tol))) then
      n_edge = 3
      rx(1)  = .true.
      ry(2)  = .true.
      rx(3)  = .true.
      ry(3)  = .true.

    elseif(symmetry == 101 .and. ((abs(dx) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

      n_edge = 3
      rx(1)  = .true.
      rz(2)  = .true.
      rx(3)  = .true.
      rz(3)  = .true.

    elseif(symmetry == 011 .and. ((abs(dy) > symmetry_tol)      &
                           .and.  (abs(dz) > symmetry_tol))) then

      n_edge = 3
      ry(1)  = .true.
      rz(2)  = .true.
      ry(3)  = .true.
      rz(3)  = .true.

    elseif( ( (symmetry == 110) .or. (symmetry == 101) ) &
      .and. (abs(dx) > symmetry_tol) ) then

      n_edge = 1
      rx(1)  = .true.

    elseif( ( (symmetry == 110) .or. (symmetry == 011) ) &
      .and. (abs(dy) > symmetry_tol) ) then

      n_edge = 1
      ry(1)  = .true.

    elseif( ( (symmetry == 101) .or. (symmetry == 011) ) &
      .and. (abs(dz) > symmetry_tol) ) then

      n_edge = 1
      rz(1)  = .true.

    endif

  end subroutine symmetry_reflections

!   Statements to include the functions that are to be inlined.
!   This is necessary because not all compilers can inline
!   functions that are in a different module.
!   N.B.: The order of the statements must reflect
!         how they are nested in the routine(s)
!         they are invoked from.


  include 'lsq_lc_max.f90'
  include 'lsq_scoords.f90'
  include 'weights.f90'
  include 'lsq_map_ref.f90'
  include 'lsq_coords.f90'
  include 'lsq_grad_stn.f90'
  include 'mapping_system.f90'
  include 'mapping_coords.f90'
  include 'coords_cylindrical_polar.f90'

end module lsq_nc

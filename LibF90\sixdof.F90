module sixdof

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,          only : dp
  use moving_body_types, only : moving_body_type
  use nml_grid_motion,   only : n_moving_bodies

#ifdef HAVE_SIXDOF
  use nml_sixdof,        only : mass, cg_x, cg_y, cg_z, i_xx,                  &
                                i_yy, i_zz, i_xy, i_xz,i_yz, cg_x0, cg_y0,     &
                                cg_z0, body_lin_vel, body_ang_vel, euler_ang,  &
                                transform, ignore_x_aeroforce,                 &
                                ignore_y_aeroforce, ignore_z_aeroforce,        &
                                ignore_x_aeromoment, ignore_y_aeromoment,      &
                                ignore_z_aeromoment, print_sixdof_summary,     &
                                use_specified_aero_data, gravity_dir,          &
                                gravity_mag, n_extforce, n_extmoment,          &
                                file_extforce, file_extmoment, max_bodies
#endif

  implicit none

  private

  public :: cleanup_sixdof
  public :: set_up_sixdof, update_sixdof

#ifdef HAVE_SIXDOF
! Body definition/identification parameter
  integer  :: body_id(max_bodies)
! Displacement of CG
  real(dp) :: displacement(3,max_bodies)
#endif

#ifdef HAVE_SIXDOF
  real(dp) :: use_aeroforce(3,max_bodies), use_aeromoment(3,max_bodies)
#endif

! Names for aerodynamic forces and moments

#ifdef HAVE_SIXDOF
  character(80) :: aeroforce, aeromoment
#endif

  type externalforce_type
    character(80)                   :: force_name
    integer                         :: n_data_pts
    integer                         :: repeat_flag
    integer                         :: coord_sys
    real(dp), dimension(:), pointer :: time
    real(dp), dimension(:), pointer :: fx
    real(dp), dimension(:), pointer :: fy
    real(dp), dimension(:), pointer :: fz
    real(dp), dimension(:), pointer :: xloc
    real(dp), dimension(:), pointer :: yloc
    real(dp), dimension(:), pointer :: zloc
  end type externalforce_type

  type external_force_type
    integer                                         :: n_extforce
    type(externalforce_type), dimension(:), pointer :: external_force
  end type external_force_type

#ifdef HAVE_SIXDOF
  type(external_force_type), dimension(:), allocatable :: body_external_force
#endif

  type externalmoment_type
    character(80)                   :: moment_name
    integer                         :: n_data_pts
    integer                         :: repeat_flag
    integer                         :: coord_sys
    real(dp), dimension(:), pointer :: time
    real(dp), dimension(:), pointer :: mx
    real(dp), dimension(:), pointer :: my
    real(dp), dimension(:), pointer :: mz
  end type externalmoment_type

  type external_moment_type
    integer                                          :: n_extmoment
    type(externalmoment_type), dimension(:), pointer :: external_moment
  end type external_moment_type

#ifdef HAVE_SIXDOF
  type(external_moment_type), dimension(:), allocatable :: body_external_moment
#endif

#ifdef HAVE_SIXDOF
! Convenience transform matrices to convert between FUN3D coordinates and LIBMO
! coordinates, etc.
  real(dp), dimension(4,4,max_bodies) :: fun3d_to_libmo, libmo_to_fun3d
  real(dp), dimension(4,4,max_bodies) :: inertial_to_fun3d
#endif

contains


#ifdef HAVE_SIXDOF

!================================== SET_UP_SIXDOF ============================80
!
! Intializes 6DOF library
!
!=============================================================================80

  subroutine set_up_sixdof(simulation_time, dt, moving_body)

    use lmpi,                only : lmpi_master, lmpi_conditional_stop
    use system_extensions,   only : se_flush
    use nml_grid_motion,     only : inertia_factor, mass_factor,               &
                                    gravity_factor, length_factor,             &
                                    time_factor, velocity_factor,              &
                                    force_factor, moment_factor,               &
                                    aero_force_factor, aero_xmoment_factor,    &
                                    aero_ymoment_factor, aero_zmoment_factor
    use grid_motion_helpers, only : external_force_file_setup,                 &
                                    aero_force_file_setup,                     &
                                    get_inverse_transform
    use string_utils,        only : sub_string

    real(dp),        intent(in)    :: simulation_time, dt

    type(moving_body_type), dimension(n_moving_bodies), intent(inout) ::       &
                                                                   moving_body

    real(dp)                    :: fmag, mmag, cgx, cgy, cgz
    real(dp)                    :: dx, dy, dz, vx, vy, vz
    real(dp)                    :: omegax, omegay, omegaz
    real(dp)                    :: yaw, pitch, roll
    real(dp)                    :: dimensional_time, dimensional_dt
    real(dp), dimension(3,3)    :: iixy
    real(dp), dimension(3)      :: fm
    real(dp), dimension(4,4)    :: trans, inv_trans

    character(len=80)           :: body_name

    integer                     :: body, n, id, istat, ierr, i, j
    integer                     :: def_g_mag, def_g_dir

  continue

    ierr = 0

!   Convert nondimensional flow solver time data to dimensional

    dimensional_time = simulation_time/time_factor
    dimensional_dt   = dt/time_factor

!   Set up the transforms between FUN3D coords and LIBMO coords
!   (do for all moving bodies, not just 6DOF ones in case body 1
!   is not a 6DOF body - we use transform of body 1 to get the
!   gravity vector in LIBMO coords)

    do body = 1,n_moving_bodies
      cgx = cg_x(body)
      cgy = cg_y(body)
      cgz = cg_z(body)
      call fun3d_body_to_libmo_body(cgx, cgy, cgz, trans)
      fun3d_to_libmo(:,:,body) = trans(:,:)
      call get_inverse_transform(trans, inv_trans)
      libmo_to_fun3d(:,:,body) = inv_trans(:,:)
    end do

!   Read any external force/moment files and setup associated derived types

    call read_external_force(moving_body)
    call read_external_moment(moving_body)

!   Create file to store external force/moment history for each body

    if (lmpi_master) then
      do body = 1,n_moving_bodies
        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
        if (n_extforce(body) > 0 .or. n_extmoment(body) > 0) then
         call external_force_file_setup(body,moving_body(body),dimensional_time)
        end if
      end do
    end if

!   Set the masking flag arrays for aerodynamic forces and moments; input flags
!   are assumed to be set for FUN3D coordinates

    use_aeroforce(:,:)  = 1.0_dp    ! default to on
    use_aeromoment(:,:) = 1.0_dp

    do body = 1,n_moving_bodies
      if (ignore_x_aeroforce(body))  use_aeroforce(1,body)  = 0.0_dp
      if (ignore_y_aeroforce(body))  use_aeroforce(2,body)  = 0.0_dp
      if (ignore_z_aeroforce(body))  use_aeroforce(3,body)  = 0.0_dp
      if (ignore_x_aeromoment(body)) use_aeromoment(1,body) = 0.0_dp
      if (ignore_y_aeromoment(body)) use_aeromoment(2,body) = 0.0_dp
      if (ignore_z_aeromoment(body)) use_aeromoment(3,body) = 0.0_dp
    end do

!   Initialize 6DOF library

    if (lmpi_master) then

      call sixdof_init(dimensional_time, dimensional_dt, moving_body)

      write(*,*)
      write(*,'(a)')' 6DOF Initialization:'
      write(*,*)
      write(*,'(a)')' Nondimesionalization factors for 6DOF equations:'
      write(*,'(a)')' (6DOF force/moment nondim. differs from aerodynamics)'
      write(*,*)
      write(*,'(a,e15.8)')'   inertia_factor      = ', inertia_factor
      write(*,'(a,e15.8)')'   mass_factor         = ', mass_factor
      write(*,'(a,e15.8)')'   gravity_factor      = ', gravity_factor
      write(*,'(a,e15.8)')'   length_factor       = ', length_factor
      write(*,'(a,e15.8)')'   velocity_factor     = ', velocity_factor
      write(*,'(a,e15.8)')'   time_factor         = ', time_factor
      write(*,'(a,e15.8)')'   force_factor        = ', force_factor
      write(*,'(a,e15.8)')'   moment_factor       = ', moment_factor
      do body = 1,n_moving_bodies
        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
        write(*,'(a,i0)') '   body ',body
        write(*,'(a,e15.8)')'     aero_force_factor   = ',                     &
                                                    aero_force_factor(body)
        write(*,'(a,e15.8)')'     aero_xmoment_factor = ',                     &
                                                    aero_xmoment_factor(body)
        write(*,'(a,e15.8)')'     aero_ymoment_factor = ',                     &
                                                    aero_ymoment_factor(body)
        write(*,'(a,e15.8)')'     aero_zmoment_factor = ',                     &
                                                    aero_zmoment_factor(body)
      end do
      write(*,*)
      write(*,'(a)')' Transform matrix, FUN3D coords to LIBMO coords:'
      do body = 1,n_moving_bodies
        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
        write(*,'(a,i0)') '   body ',body
        do i=1,4
          write(*,'(3x,4e19.10)') (fun3d_to_libmo(i,j,body),j=1,4)
        end do
      end do
      write(*,*)

    end if

!   Setup some more 6DOF data

    if (lmpi_master) then

!     Print out gravity magnitude and direction so we can verify that
!     we know up from down

      def_g_mag     = 1
      def_g_dir     = 2

      call mofGetDefault(def_g_mag, fmag, istat)
      call mofGetVectorDefault(def_g_dir, fm, 3, istat)

      write(*,'(a,4e11.3)')                                                    &
           '  Gravity Magnitude and Direction (LIBMO coords): ', fmag, fm
      write(*,*)

!     Get mass/inertia properties for each body and print out

      do body = 1,n_moving_bodies

        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

        id = body_id(body)

        call mofGetBodyNameFromId(id, 80, body_name, istat)

        call mofGetBodymassProp(id, mmag, iixy(1,1), iixy(2,2), iixy(3,3),     &
                                iixy(1,2), iixy(1,3), iixy(2,3), cgx, cgy, cgz,&
                                istat)
        call mofGetBodyState(id, dx, dy, dz, vx, vy, vz,                       &
                             omegax, omegay, omegaz, istat)

        call mofGetBodyEulerAngles(id, yaw, pitch, roll, istat)

        write(*,'(a,i0,a,e15.7)') '  Dimensional 6DOF data for Body ',         &
                              body, '   Time = ', dimensional_time
        write(*,'(2a)')      '  Note: data below are now in LIBMO coordinates',&
                             ' and dimensional units'

        write(*,'(2a)')      '   Body Name:        ',trim(body_name)
        write(*,'(a,4e14.6)')'   Mass:           ',mmag
        write(*,'(a,4e14.6)')'   CG Location:    ',cgx, cgy, cgz
        write(*,'(a,4e14.6)')'   Ixx,Iyy,Izz:    ',iixy(1,1),iixy(2,2),iixy(3,3)
        write(*,'(a,4e14.6)')'   Ixy,Ixz,Iyz:    ',iixy(1,2),iixy(1,3),iixy(2,3)
        write(*,'(a,4e14.6)')'   Linear Vel:     ',vx,vy,vz
        write(*,'(a,4e14.6)')'   Angular Vel:    ',omegax,omegay,omegaz
        write(*,'(a,4e14.6)')'   Yaw,Pitch,Roll: ',yaw,pitch,roll

        if (n_extforce(body) > 0) then
          write(*,*)
          write(*,'(a,i0,a)') '  External forces for Body ',body,              &
                              ' imposed from the file(s) :'
          do n = 1,n_extforce(body)
            write(*,'(2a)') '   ',trim(file_extforce(n,body))
          end do
        end if

        if (n_extmoment(body) > 0) then
          write(*,*)
          write(*,'(a,i0,a)') '  External moments for Body ',body,             &
                              ' imposed from the file(s) :'
          do n = 1,n_extmoment(body)
            write(*,'(2a)') '   ',trim(file_extmoment(n,body))
          end do
        end if

      end do

      if (n_moving_bodies == 0) then
        write(*,*)
        write(*,*) 'Error in set_up_sixdof: 6DOF...n_moving_bodies = 0'
        write(*,*) 'Stopping'
        ierr = 1
      end if

!     When using the option to use specified aero forces and moments rather
!     than the computed ones (for testing), open up an additional files(s)
!     to output the specified data for comparison

      if (use_specified_aero_data) then
        do body=1,n_moving_bodies
          if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
          call aero_force_file_setup(body, moving_body(body), simulation_time, &
                                     'Specified_AeroForceMomentBody')
        end do
      end if

    end if

    call lmpi_conditional_stop(ierr, 'set_up_sixdof')

    if (lmpi_master .and. print_sixdof_summary) then
      write(*,*)
      write(*,*) 'Note: In Initial Problem Summary Below'
      write(*,*) 'Forces/Moments Are Not Yet Applied'
      call mofPrintSummary(istat)
!     mofPrintSummary doesn't output linear displacements for some reason
!     so do that explicitly
      do body=1,n_moving_bodies
        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
        call mofGetBodyState(id,dx,dy,dz,vx,vy,vz,omegax,omegay,omegaz,istat)
        write(*,'(a)') '    Linear Displacements:'
        write(*,'(a,g13.6,a,g13.6,a,g13.6)')                                   &
                       '      dx = ', dx, ' dy = ', dy, ' dz = ', dz
      end do
      call se_flush()
    end if

  end subroutine set_up_sixdof


!================================= UPDATE_SIXDOF =============================80
!
! Updates body position from 6DOF model, and puts the resulting transform data
! in the moving_body derived type so we can use it to drive the grid motion
!
! Integration of 6DOF equations is done on the master node
!
!=============================================================================80

  subroutine update_sixdof(simulation_time, dt, moving_body)

    use lmpi,                only : lmpi_master, lmpi_conditional_stop,        &
                                    lmpi_bcast
    use system_extensions,   only : se_flush
    use nml_grid_motion,     only : time_factor, length_factor,                &
                                    velocity_factor, time_factor
    use grid_motion_helpers, only : get_inverse_transform
    use info_depr,           only : ntt
    use nml_global,          only : grid_motion_only
    use string_utils,        only : sub_string

    real(dp),        intent(in)    :: simulation_time, dt

    type(moving_body_type), dimension(n_moving_bodies), intent(inout) ::       &
                                                                    moving_body

    integer :: istat, n_sixdof_errors, force_err
    integer :: body, id

    real(dp)    :: dimensional_time, dx, dy, dz, omegax, omegay, omegaz
    real(dp)    :: vx, vy, vz, cgx, cgy, cgz, yaw, pitch, roll
    real(dp)    :: trans(4,4)

  continue

!   Perform set up and intialization operations the first time through

    if (ntt == 1) then
      call set_up_sixdof(simulation_time-dt, dt, moving_body)
    end if

    force_err       = 0
    n_sixdof_errors = 0

!   Convert nondimensional flow solver time to dimensional

    dimensional_time = simulation_time/time_factor

!   Store off the transform from the inertial frame to the FUN3D body frame.
!   note that this transform changes each time step; use the (inverse)
!   transform at the start of the time step (stored in transform_matrixatn)

    do body = 1,n_moving_bodies
     if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
     call get_inverse_transform(moving_body(body)%transform_matrixatn, trans)
     inertial_to_fun3d(:,:,body) = trans(:,:)
    end do

    if (lmpi_master) then

!     Set current body state (position, velocity, orientation); this may reflect
!     any changes from trim or other motions imposed elsewhere

      do body = 1, n_moving_bodies

        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

        body_lin_vel(:,body) = moving_body(body)%body_lin_vel(:)
        body_ang_vel(:,body) = moving_body(body)%body_ang_vel(:)
        euler_ang(:,body)    = moving_body(body)%euler_angles(:)
        displacement(1,body) = moving_body(body)%xcg
        displacement(2,body) = moving_body(body)%ycg
        displacement(3,body) = moving_body(body)%zcg

!       Convert FUN3D data to dimensional; FUN3D Euler angles are in degrees

        displacement(:,body) = displacement(:,body)/length_factor
        body_lin_vel(:,body) = body_lin_vel(:,body)/velocity_factor
        body_ang_vel(:,body) = body_ang_vel(:,body)*time_factor

!       FUN3D body linear velocities are in the inertial frame; LIBMO is
!       expecting linear velocities in the body frame, so first convert
!       from inertial frame to FUN3D body frame

        vx = body_lin_vel(1,body)
        vy = body_lin_vel(2,body)
        vz = body_lin_vel(3,body)

        body_lin_vel(1,body) = vx*inertial_to_fun3d(1,1,body)                  &
                             + vy*inertial_to_fun3d(1,2,body)                  &
                             + vz*inertial_to_fun3d(1,3,body)
        body_lin_vel(2,body) = vx*inertial_to_fun3d(2,1,body)                  &
                             + vy*inertial_to_fun3d(2,2,body)                  &
                             + vz*inertial_to_fun3d(2,3,body)
        body_lin_vel(3,body) = vx*inertial_to_fun3d(3,1,body)                  &
                             + vy*inertial_to_fun3d(3,2,body)                  &
                             + vz*inertial_to_fun3d(3,3,body)

!       Convert from FUN3D coordinate system to LIBMO coordinate system

        trans(:,:) = fun3d_to_libmo(:,:,body)

        cgx = cg_x(body)
        cgy = cg_y(body)
        cgz = cg_z(body)
        cg_x(body) = trans(1,1)*cgx + trans(1,2)*cgy + trans(1,3)*cgz          &
                   + trans(1,4)
        cg_y(body) = trans(2,1)*cgx + trans(2,2)*cgy + trans(2,3)*cgz          &
                   + trans(2,4)
        cg_z(body) = trans(3,1)*cgx + trans(3,2)*cgy + trans(3,3)*cgz          &
                   + trans(3,4)

        dx = displacement(1,body)
        dy = displacement(2,body)
        dz = displacement(3,body)
        displacement(1,body) = trans(1,1)*dx + trans(1,2)*dy + trans(1,3)*dz   &
                             + trans(1,4)
        displacement(2,body) = trans(2,1)*dx + trans(2,2)*dy + trans(2,3)*dz   &
                             + trans(2,4)
        displacement(3,body) = trans(3,1)*dx + trans(3,2)*dy + trans(3,3)*dz   &
                             + trans(3,4)

        vx = body_lin_vel(1,body)
        vy = body_lin_vel(2,body)
        vz = body_lin_vel(3,body)
        body_lin_vel(1,body) = trans(1,1)*vx + trans(1,2)*vy + trans(1,3)*vz
        body_lin_vel(2,body) = trans(2,1)*vx + trans(2,2)*vy + trans(2,3)*vz
        body_lin_vel(3,body) = trans(3,1)*vx + trans(3,2)*vy + trans(3,3)*vz

        omegax = body_ang_vel(1,body)
        omegay = body_ang_vel(2,body)
        omegaz = body_ang_vel(3,body)
        body_ang_vel(1,body) = trans(1,1)*omegax + trans(1,2)*omegay           &
                             + trans(1,3)*omegaz
        body_ang_vel(2,body) = trans(2,1)*omegax + trans(2,2)*omegay           &
                             + trans(2,3)*omegaz
        body_ang_vel(3,body) = trans(3,1)*omegax + trans(3,2)*omegay           &
                             + trans(3,3)*omegaz

        yaw   = euler_ang(1,body)
        pitch = euler_ang(2,body)
        roll  = euler_ang(3,body)
        euler_ang(1,body) = trans(1,1)*yaw + trans(1,2)*pitch + trans(1,3)*roll
        euler_ang(2,body) = trans(2,1)*yaw + trans(2,2)*pitch + trans(2,3)*roll
        euler_ang(3,body) = trans(3,1)*yaw + trans(3,2)*pitch + trans(3,3)*roll

        id = body_id(body)

        call mofSetBodyState(id, displacement(1,body), displacement(2,body),   &
                             displacement(3,body), body_lin_vel(1,body),       &
                             body_lin_vel(2,body), body_lin_vel(3,body),       &
                             body_ang_vel(1,body), body_ang_vel(2,body),       &
                             body_ang_vel(3,body), istat)

        call mofSetBodyEulerAngles(id, euler_ang(1,body), euler_ang(2,body),   &
                                   euler_ang(3,body), istat)

      end do

!     Load the current force and moment data into the 6DOF routine

      call sixdof_apply_force_moment(moving_body, dimensional_time, force_err)

!     Perform the 6DOF integration for all bodies to advance them to time t(n)

      call mofMoveAll(istat)

!     Print out 6DOF summary if desired

      if (print_sixdof_summary) then
        call mofPrintSummary(istat)
!       mofPrintSummary doesn't output linear displacements for some reason
!       so do that explicitly
        do body=1,n_moving_bodies
          if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
          id = body_id(body)
          call mofGetBodyState(id,dx,dy,dz,vx,vy,vz,omegax,omegay,omegaz,istat)
          write(*,'(a)') '    Linear Displacements:'
          write(*,'(a,g13.6,a,g13.6,a,g13.6)')                                 &
                         '      dx = ', dx, ' dy = ', dy, ' dz = ', dz
        end do
        call se_flush()
      end if

    end if

!   Update the data in the moving_body derived type for the current position

    call set_moving_body_from_sixdof(moving_body)

!   Print out any error messages generated by the 6DOF routines;
!   bypass stop if grid_motion_only because 6DOF will generate error
!   message that no external forces/moments exist

    if (lmpi_master .and. .not.(grid_motion_only)) then
      call mofPrintErrors(n_sixdof_errors)
      call se_flush()
      if (n_sixdof_errors /= 0) then
        write(*,*) 'Stopping...6DOF library has reported errors'
      end if
    end if

    n_sixdof_errors = n_sixdof_errors + force_err

    call lmpi_conditional_stop(n_sixdof_errors, 'update_sixdof')

  end subroutine update_sixdof


!================================ CLEANUP_SIXDOF ============================80
!
! Shutdown the 6DOF library and free all of the allocated memory
!
!=============================================================================80

  subroutine cleanup_sixdof()

    use lmpi,              only : lmpi_master

  continue

    if (lmpi_master) call mofCleanup()

  end subroutine cleanup_sixdof


!=========================== SET_MOVING_BODY_FROM_SIXDOF =====================80
!
! Extracts 6DOF data and puts in the moving_body derived type so we can use
! it to drive the grid motion from within FUN3D; also need for restarting
!
!=============================================================================80

  subroutine set_moving_body_from_sixdof(moving_body)

    use lmpi,                only : lmpi_master, lmpi_bcast
    use grid_motion_helpers, only : get_inverse_transform,                     &
                                    transform_matrix_to_euler_angle
    use nml_grid_motion,     only : length_factor, velocity_factor,            &
                                    time_factor, mass_factor, inertia_factor,  &
                                    max_generations
    use string_utils,        only : sub_string

    type(moving_body_type), dimension(n_moving_bodies), intent(inout) ::       &
                                                                    moving_body

    real(dp)    :: dx, dy, dz, vx, vy, vz
    real(dp)    :: omegax, omegay, omegaz
    real(dp)    :: yaw, pitch, roll
    real(dp)    :: trans(4,4), temp(4,4), transfer(22)
    real(dp)    :: trans3(3,3), inertia_tensor(3,3), temp3(3,3)
    real(dp)    :: lib_to_fun(4,4), fun_to_lib(4,4)
    real(dp)    :: radtodeg, x0, y0, z0

    integer     :: parent, child, parent_gen, child_gen, descendant
    integer     :: body, id, istat, i

  continue

    radtodeg = 180.0_dp/acos(-1.0_dp)

    body_loop : do body = 1,n_moving_bodies

      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      lib_to_fun(:,:) = libmo_to_fun3d(:,:,body)
      fun_to_lib(:,:) = fun3d_to_libmo(:,:,body)

      if (lmpi_master) then

        id = body_id(body)

        call mofGetBodyState(id,dx,dy,dz,vx,vy,vz,omegax,omegay,omegaz,istat)
        call mofGetBodyEulerAngles(id, yaw, pitch, roll, istat)
        call mofGetBodyTransformation(id, trans, istat)
        call mofGetBodymassProp(id, mass(body),                                &
                                i_xx(body), i_yy(body), i_zz(body),            &
                                i_xy(body), i_xz(body), i_yz(body),            &
                                cg_x(body), cg_y(body), cg_z(body),            &
                                istat)

!       pack a bunch of scalars for bcasting

        transfer(1)  = dx
        transfer(2)  = dy
        transfer(3)  = dz
        transfer(4)  = vx
        transfer(5)  = vy
        transfer(6)  = vz
        transfer(7)  = omegax
        transfer(8)  = omegay
        transfer(9)  = omegaz
        transfer(10) = yaw
        transfer(11) = pitch
        transfer(12) = roll
        transfer(13) = mass(body)
        transfer(14) = i_xx(body)
        transfer(15) = i_yy(body)
        transfer(16) = i_zz(body)
        transfer(17) = i_xy(body)
        transfer(18) = i_xz(body)
        transfer(19) = i_yz(body)

      end if

      call lmpi_bcast(trans)
      call lmpi_bcast(transfer)

      dx         = transfer(1)
      dy         = transfer(2)
      dz         = transfer(3)
      vx         = transfer(4)
      vy         = transfer(5)
      vz         = transfer(6)
      omegax     = transfer(7)
      omegay     = transfer(8)
      omegaz     = transfer(9)
      yaw        = transfer(10)
      pitch      = transfer(11)
      roll       = transfer(12)
      mass(body) = transfer(13)
      i_xx(body) = transfer(14)
      i_yy(body) = transfer(15)
      i_zz(body) = transfer(16)
      i_xy(body) = transfer(17)
      i_xz(body) = transfer(18)
      i_yz(body) = transfer(19)

!     convert to nondimensional (note 4th column in transform matrix is
!     translation, and hence dimensional; 3x3 submatrix is rotation only)

      dx = dx*length_factor
      dy = dy*length_factor
      dz = dz*length_factor

      vx = vx*velocity_factor
      vy = vy*velocity_factor
      vz = vz*velocity_factor

      omegax = omegax/time_factor
      omegay = omegay/time_factor
      omegaz = omegaz/time_factor

      mass(body)           = mass(body)*mass_factor
      i_xx(body)           = i_xx(body)*inertia_factor
      i_yy(body)           = i_yy(body)*inertia_factor
      i_zz(body)           = i_zz(body)*inertia_factor
      i_xy(body)           = i_xy(body)*inertia_factor
      i_xz(body)           = i_xz(body)*inertia_factor
      i_yz(body)           = i_yz(body)*inertia_factor

      do i=1,3
        trans(i,4) = trans(i,4)*length_factor
      end do

!     save off the relative displacements (needed for parent child)

      displacement(1,body) = dx
      displacement(2,body) = dy
      displacement(3,body) = dz

!     set the mass properties in case they have changed

      moving_body(body)%mass = mass(body)

!     convert inertia tensor from LIBMO coords to FUN3D coords

      inertia_tensor(1,1) =  i_xx(body)
      inertia_tensor(1,2) = -i_xy(body)
      inertia_tensor(1,3) = -i_xz(body)
      inertia_tensor(2,1) = -i_xy(body)
      inertia_tensor(2,2) =  i_yy(body)
      inertia_tensor(2,3) = -i_yz(body)
      inertia_tensor(3,1) = -i_xz(body)
      inertia_tensor(3,2) = -i_yz(body)
      inertia_tensor(3,3) =  i_zz(body)

      trans3(1:3,1:3) = libmo_to_fun3d(1:3,1:3,body)

      temp3 = matmul(trans3,inertia_tensor)

      trans3(1:3,1:3) = fun3d_to_libmo(1:3,1:3,body)

      inertia_tensor = matmul(temp3,trans3(:,:))

      moving_body(body)%i_xx =  inertia_tensor(1,1)
      moving_body(body)%i_yy =  inertia_tensor(2,2)
      moving_body(body)%i_zz =  inertia_tensor(3,3)
      moving_body(body)%i_xy = -inertia_tensor(2,1)
      moving_body(body)%i_xz = -inertia_tensor(1,3)
      moving_body(body)%i_yz = -inertia_tensor(2,3)

!     set the transform matrix and its inverse

      temp = matmul(trans(:,:),fun_to_lib)
      moving_body(body)%transform_matrix(:,:) = matmul(lib_to_fun,temp)

      call get_inverse_transform(moving_body(body)%transform_matrix(:,:),      &
                                 moving_body(body)%inv_transform)

!     set the slice_transform (and inverse) to the moving_body transform

      moving_body(body)%slice_transform     = moving_body(body)%transform_matrix
      moving_body(body)%inv_slice_transform = moving_body(body)%inv_transform

!     set the CG location

      moving_body(body)%xcg = lib_to_fun(1,1)*dx + lib_to_fun(1,2)*dy          &
                            + lib_to_fun(1,3)*dz + lib_to_fun(1,4)
      moving_body(body)%ycg = lib_to_fun(2,1)*dx + lib_to_fun(2,2)*dy          &
                            + lib_to_fun(2,3)*dz + lib_to_fun(2,4)
      moving_body(body)%zcg = lib_to_fun(3,1)*dx + lib_to_fun(3,2)*dy          &
                            + lib_to_fun(3,3)*dz + lib_to_fun(3,4)

!     moment center is CG location

      moving_body(body)%xmc = moving_body(body)%xcg
      moving_body(body)%ymc = moving_body(body)%ycg
      moving_body(body)%zmc = moving_body(body)%zcg

!     set the body linear velocities; note that the velocity components from
!     LIBMO are in the (LIBMO) body frame, so first convert them to FUN3D
!     body frame. But then note that historicaly, FUN3D has stored the body
!     linear velocities in the inertial frame, so also do that conversion
!     Oy!

      moving_body(body)%body_lin_vel(1) = lib_to_fun(1,1)*vx                   &
                                        + lib_to_fun(1,2)*vy                   &
                                        + lib_to_fun(1,3)*vz
      moving_body(body)%body_lin_vel(2) = lib_to_fun(2,1)*vx                   &
                                        + lib_to_fun(2,2)*vy                   &
                                        + lib_to_fun(2,3)*vz
      moving_body(body)%body_lin_vel(3) = lib_to_fun(3,1)*vx                   &
                                        + lib_to_fun(3,2)*vy                   &
                                        + lib_to_fun(3,3)*vz

      vx =  moving_body(body)%body_lin_vel(1)
      vy =  moving_body(body)%body_lin_vel(2)
      vz =  moving_body(body)%body_lin_vel(3)

      moving_body(body)%body_lin_vel(1) =                                      &
                                  + vx*moving_body(body)%transform_matrix(1,1) &
                                  + vy*moving_body(body)%transform_matrix(1,2) &
                                  + vz*moving_body(body)%transform_matrix(1,3)
      moving_body(body)%body_lin_vel(2) =                                      &
                                  + vx*moving_body(body)%transform_matrix(2,1) &
                                  + vy*moving_body(body)%transform_matrix(2,2) &
                                  + vz*moving_body(body)%transform_matrix(2,3)
      moving_body(body)%body_lin_vel(3) =                                      &
                                  + vx*moving_body(body)%transform_matrix(3,1) &
                                  + vy*moving_body(body)%transform_matrix(3,2) &
                                  + vz*moving_body(body)%transform_matrix(3,3)

!     set the body angular velocities; note in both FUN3D and LIBMO angular
!     velocities are in the body frame, so we need only account for the
!     difference in the body frames

      moving_body(body)%body_ang_vel(1) = lib_to_fun(1,1)*omegax               &
                                        + lib_to_fun(1,2)*omegay               &
                                        + lib_to_fun(1,3)*omegaz
      moving_body(body)%body_ang_vel(2) = lib_to_fun(2,1)*omegax               &
                                        + lib_to_fun(2,2)*omegay               &
                                        + lib_to_fun(2,3)*omegaz
      moving_body(body)%body_ang_vel(3) = lib_to_fun(3,1)*omegax               &
                                        + lib_to_fun(3,2)*omegay               &
                                        + lib_to_fun(3,3)*omegaz

!     set the Euler angles (already in degrees)

      moving_body(body)%euler_angles(1) = lib_to_fun(1,1)*yaw                  &
                                        + lib_to_fun(1,2)*pitch                &
                                        + lib_to_fun(1,3)*roll
      moving_body(body)%euler_angles(2) = lib_to_fun(2,1)*yaw                  &
                                        + lib_to_fun(2,2)*pitch                &
                                        + lib_to_fun(2,3)*roll
      moving_body(body)%euler_angles(3) = lib_to_fun(3,1)*yaw                  &
                                        + lib_to_fun(3,2)*pitch                &
                                        + lib_to_fun(3,3)*roll

    end do body_loop

!   take care of parent-child relationships - the 6DOF library keeps the child's
!   transform, state vector, etc. relative to the parent, not relative to the
!   inertial frame; in FUN3D all this is stored relative to the inertial frame

!   traverse down through the generations to build up all the inherited
!   transforms; transforms are multiplicative

    do parent_gen = 0,max_generations-1

      child_gen = parent_gen + 1

      do body = 1,n_moving_bodies

        if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

        if (moving_body(body)%generation(1) == parent_gen) then

          parent = body

!         update the transforms of the parents' immediate children only;
!         grandchildren inherit their grandparents' motion indirectly,
!         via the grandchildrens' own parents

          descendants : do i = 1,moving_body(parent)%n_descendants

            descendant = moving_body(parent)%descendants(i)

            if (moving_body(descendant)%generation(1) == child_gen) then

              child = descendant

!             children of 6DOF bodies that are not themselves 6DOF bodies
!             do not get their inheritance here

              if (.not. sub_string(moving_body(child)%motion_driver,'6dof'))   &
                                                            cycle descendants

              moving_body(child)%transform_matrix =                            &
                               matmul( moving_body(parent)%transform_matrix,   &
                                       moving_body(child)%transform_matrix )

              call get_inverse_transform(moving_body(child)%transform_matrix,  &
                                         moving_body(child)%inv_transform)

              moving_body(child)%slice_transform =                             &
                                         moving_body(child)%transform_matrix
              moving_body(child)%inv_slice_transform =                         &
                                         moving_body(child)%inv_transform

!             Update the CG location of the child based on the current child
!             transform

              x0 = cg_x0(child)
              y0 = cg_y0(child)
              z0 = cg_z0(child)

              moving_body(child)%xcg =                                         &
                         moving_body(child)%transform_matrix(1,1)*x0           &
                       + moving_body(child)%transform_matrix(1,2)*y0           &
                       + moving_body(child)%transform_matrix(1,3)*z0           &
                       + moving_body(child)%transform_matrix(1,4)
              moving_body(child)%ycg =                                         &
                         moving_body(child)%transform_matrix(2,1)*x0           &
                       + moving_body(child)%transform_matrix(2,2)*y0           &
                       + moving_body(child)%transform_matrix(2,3)*z0           &
                       + moving_body(child)%transform_matrix(2,4)
              moving_body(child)%zcg =                                         &
                         moving_body(child)%transform_matrix(3,1)*x0           &
                       + moving_body(child)%transform_matrix(3,2)*y0           &
                       + moving_body(child)%transform_matrix(3,3)*z0           &
                       + moving_body(child)%transform_matrix(3,4)

!             set linear velocities via addition of parent velocity

              moving_body(child)%body_lin_vel(1) =                             &
                                           moving_body(parent)%body_lin_vel(1) &
                                         + moving_body(child)%body_lin_vel(1)
              moving_body(child)%body_lin_vel(2) =                             &
                                           moving_body(parent)%body_lin_vel(2) &
                                         + moving_body(child)%body_lin_vel(2)
              moving_body(child)%body_lin_vel(3) =                             &
                                           moving_body(parent)%body_lin_vel(3) &
                                         + moving_body(child)%body_lin_vel(3)

!             from: http://en.wikipedia.org/wiki/
!                              Rigid_body#Addition_theorem_for_angular_velocity
!             "the angular velocity of a rigid body B in a reference frame N is
!             equal to the sum of the angular velocity of a rigid body D in N
!             and the angular velocity of B with respect to D"

              moving_body(child)%body_ang_vel(1) =                             &
                                          moving_body(parent)%body_ang_vel(1)  &
                                        + moving_body(child)%body_ang_vel(1)
              moving_body(child)%body_ang_vel(2) =                             &
                                          moving_body(parent)%body_ang_vel(2)  &
                                        + moving_body(child)%body_ang_vel(2)
              moving_body(child)%body_ang_vel(3) =                             &
                                          moving_body(parent)%body_ang_vel(3)  &
                                        + moving_body(child)%body_ang_vel(3)

!             Euler angles - get them from the transform matix since I have no
!             clue how or if they can be added between parent and child

              call transform_matrix_to_euler_angle(                            &
                        moving_body(child)%transform_matrix, pitch, roll, yaw)

              moving_body(child)%euler_angles(1) = yaw*radtodeg
              moving_body(child)%euler_angles(2) = pitch*radtodeg
              moving_body(child)%euler_angles(3) = roll*radtodeg

            end if

          end do descendants

        end if

      end do

    end do

  end subroutine set_moving_body_from_sixdof


!=================================== SIXDOF_INIT =============================80
!
! Initializes the 6DOF libraries; lots of up front conversion work though before
! we can actually call the initialization routines
!
!=============================================================================80

  subroutine sixdof_init(time, time_step, moving_body)

    use grid_motion_helpers, only : get_inverse_transform, get_body_id_from_name
    use nml_grid_motion,     only : length_factor, time_factor, velocity_factor
    use string_utils,        only : sub_string

    real(dp),    intent(in) :: time, time_step

    type(moving_body_type), dimension(n_moving_bodies), intent(in) ::          &
                                                                    moving_body

    character(len=80) :: forcename, momentname

    integer           :: body, id, istat, j, coord_sys, ierr
    integer           :: def_g_mag, def_g_dir
    integer           :: def_time, def_time_step
    integer           :: parent, parent_id, child, child_id

    real(dp)          :: fmag, fx, fy, fz, mmag, mx, my, mz, grx, gry, grz
    real(dp)          :: dx, dy, dz, pitch, roll, yaw
    real(dp)          :: cgx, cgy, cgz, vx, vy, vz, omegax, omegay, omegaz
    real(dp)          :: trans(4,4), trans3(3,3), inertia_tensor(3,3), temp(3,3)

  continue

!*******************************************************************************
! Section 1) Get the current body state from FUN3D data (nondimensional)
!*******************************************************************************

    do body = 1,n_moving_bodies

      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      body_lin_vel(:,body) = moving_body(body)%body_lin_vel(:)
      body_ang_vel(:,body) = moving_body(body)%body_ang_vel(:)
      euler_ang(:,body)    = moving_body(body)%euler_angles(:)
      displacement(1,body) = moving_body(body)%xcg
      displacement(2,body) = moving_body(body)%ycg
      displacement(3,body) = moving_body(body)%zcg

!     Convert FUN3D data to dimensional; FUN3D Euler angles are in degrees

      displacement(:,body) = displacement(:,body)/length_factor
      body_lin_vel(:,body) = body_lin_vel(:,body)/velocity_factor
      body_ang_vel(:,body) = body_ang_vel(:,body)*time_factor

    end do

!*******************************************************************************
!   Section 3) data conversions: input and FUN3D data are assumed to be in
!              FUN3D coordinates and need to be converted to LIBMO (6DOF)
!              coordinates
!*******************************************************************************

!   Convert (input) gravity direction from FUN3D coords to LIBMO coords;
!   use the transform of body 1; input gravity magnitude is assumed dimensional

    trans(:,:) = fun3d_to_libmo(:,:,1)

    grx = gravity_dir(1)
    gry = gravity_dir(2)
    grz = gravity_dir(3)
    gravity_dir(1) = trans(1,1)*grx + trans(1,2)*gry + trans(1,3)*grz
    gravity_dir(2) = trans(2,1)*grx + trans(2,2)*gry + trans(2,3)*grz
    gravity_dir(3) = trans(3,1)*grx + trans(3,2)*gry + trans(3,3)*grz

!   Convert the (input) inertia tensor(s) from FUN3D coords to LIBMO coords
!   Inertia tensor I transforms as [T][I][T]^-1 ([T]^-1 = [T]transpose)
!   Where T is the rotation matrix. Assume I is given about body CG,
!   and is assumed dimensional

    do body = 1,n_moving_bodies

     if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      inertia_tensor(1,1) =  i_xx(body)
      inertia_tensor(1,2) = -i_xy(body)
      inertia_tensor(1,3) = -i_xz(body)
      inertia_tensor(2,1) = -i_xy(body)
      inertia_tensor(2,2) =  i_yy(body)
      inertia_tensor(2,3) = -i_yz(body)
      inertia_tensor(3,1) = -i_xz(body)
      inertia_tensor(3,2) = -i_yz(body)
      inertia_tensor(3,3) =  i_zz(body)

      trans3(1:3,1:3) = fun3d_to_libmo(1:3,1:3,body)

      temp = matmul(trans3,inertia_tensor)

      trans3(1:3,1:3) = libmo_to_fun3d(1:3,1:3,body)

      inertia_tensor = matmul(temp,trans3(:,:))

      i_xx(body) =  inertia_tensor(1,1)
      i_xy(body) = -inertia_tensor(1,2)
      i_xz(body) = -inertia_tensor(1,3)
      i_yy(body) =  inertia_tensor(2,2)
      i_yz(body) = -inertia_tensor(2,3)
      i_zz(body) =  inertia_tensor(3,3)

    end do

    do body = 1,n_moving_bodies

      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      trans(:,:) = fun3d_to_libmo(:,:,body)

      cgx = cg_x(body)
      cgy = cg_y(body)
      cgz = cg_z(body)
      cg_x(body) = trans(1,1)*cgx + trans(1,2)*cgy + trans(1,3)*cgz + trans(1,4)
      cg_y(body) = trans(2,1)*cgx + trans(2,2)*cgy + trans(2,3)*cgz + trans(2,4)
      cg_z(body) = trans(3,1)*cgx + trans(3,2)*cgy + trans(3,3)*cgz + trans(3,4)

      dx = displacement(1,body)
      dy = displacement(2,body)
      dz = displacement(3,body)
      displacement(1,body) = trans(1,1)*dx + trans(1,2)*dy + trans(1,3)*dz     &
                           + trans(1,4)
      displacement(2,body) = trans(2,1)*dx + trans(2,2)*dy + trans(2,3)*dz     &
                           + trans(2,4)
      displacement(3,body) = trans(3,1)*dx + trans(3,2)*dy + trans(3,3)*dz     &
                           + trans(3,4)

      vx = body_lin_vel(1,body)
      vy = body_lin_vel(2,body)
      vz = body_lin_vel(3,body)
      body_lin_vel(1,body) = trans(1,1)*vx + trans(1,2)*vy + trans(1,3)*vz
      body_lin_vel(2,body) = trans(2,1)*vx + trans(2,2)*vy + trans(2,3)*vz
      body_lin_vel(3,body) = trans(3,1)*vx + trans(3,2)*vy + trans(3,3)*vz

      omegax = body_ang_vel(1,body)
      omegay = body_ang_vel(2,body)
      omegaz = body_ang_vel(3,body)
      body_ang_vel(1,body) = trans(1,1)*omegax + trans(1,2)*omegay             &
                           + trans(1,3)*omegaz
      body_ang_vel(2,body) = trans(2,1)*omegax + trans(2,2)*omegay             &
                           + trans(2,3)*omegaz
      body_ang_vel(3,body) = trans(3,1)*omegax + trans(3,2)*omegay             &
                           + trans(3,3)*omegaz

      yaw   = euler_ang(1,body)
      pitch = euler_ang(2,body)
      roll  = euler_ang(3,body)
      euler_ang(1,body) = trans(1,1)*yaw + trans(1,2)*pitch + trans(1,3)*roll
      euler_ang(2,body) = trans(2,1)*yaw + trans(2,2)*pitch + trans(2,3)*roll
      euler_ang(3,body) = trans(3,1)*yaw + trans(3,2)*pitch + trans(3,3)*roll

    end do

!*******************************************************************************
!   Section 4) Now that all the data is dimensional and in LIBMO coordinates we
!              can finally make all the LIBMO initializations
!*******************************************************************************

!   Set values for gravity magnitude and direction

    def_g_mag     = 1
    def_g_dir     = 2

    call mofSetDefault(def_g_mag, gravity_mag, istat)
    call mofSetVectorDefault(def_g_dir, gravity_dir, 3, istat)

!   Set values for time and time step

    def_time      = 4
    def_time_step = 5

    call mofSetDefault(def_time, time, istat)
    call mofSetDefault(def_time_step, time_step, istat)

!   Initialize the 6DOF library

    call mofInitialize()

!   Add bodies to the library database

    do body = 1,n_moving_bodies
      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
      call mofNewBody(moving_body(body)%body_name, id, istat)
      body_id(body) = id
    end do

!   Set parent-child relationships

    do body = 1,n_moving_bodies
      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
      call get_body_id_from_name(moving_body(body)%parent_name, parent, ierr)
      if (parent == 0) cycle  ! parent is inertial frame, not a moving body
      parent_id = body_id(parent)
      child = body
      child_id = body_id(child)
      if (sub_string(moving_body(child)%motion_driver,'6dof')) then
        call mofSetParentChild(parent_id, child_id, istat)
      end if
    end do

!   Initialize bodies in the library database

    do body = 1,n_moving_bodies

      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      id = body_id(body)

!     Set the mass properties

      call mofSetBodymassProp(id, mass(body), i_xx(body), i_yy(body),          &
                              i_zz(body), i_xy(body), i_xz(body), i_yz(body),  &
                              cg_x(body), cg_y(body), cg_z(body), istat)

!     Set the initial velocities and position

      call mofSetBodyState(id, displacement(1,body), displacement(2,body),     &
                           displacement(3,body), body_lin_vel(1,body),         &
                           body_lin_vel(2,body), body_lin_vel(3,body),         &
                           body_ang_vel(1,body), body_ang_vel(2,body),         &
                           body_ang_vel(3,body), istat)

      call mofSetBodyEulerAngles(id, euler_ang(1,body), euler_ang(2,body),     &
                                 euler_ang(3,body), istat)

!     Set dummy aerodynamic forces and moments for initialization purposes
!     only - actual values set later

      aeroforce      = 'aero_force'
      aeromoment     = 'aero_moment'

      fmag = 0.0_dp
      fx   = 1.0_dp
      fy   = 0.0_dp
      fz   = 0.0_dp

      mmag = 0.0_dp
      mx   = 1.0_dp
      my   = 0.0_dp
      mz   = 0.0_dp

      coord_sys = id  ! 0 => inertial;  id => body system of body "id"

      call mofNewConstantCGForce(id, aeroforce, fmag, fx, fy, fz,              &
                                 coord_sys, istat)
      call mofNewConstantMoment(id, aeromoment, mmag, mx, my, mz,              &
                                coord_sys, istat)

!     Set dummy external forces and moments

      coord_sys = id  ! 0 => inertial;  id => body system of body "id"

      do j = 1,n_extforce(body)
        forcename = body_external_force(body)%external_force(j)%force_name
        call mofNewConstantForce(id, forcename, fmag, cg_x(body), cg_y(body),  &
                                 cg_z(body), fx, fy, fz, coord_sys, istat)
      end do

      do j = 1,n_extmoment(body)
        momentname = body_external_moment(body)%external_moment(j)%moment_name
        call mofNewConstantMoment(id, momentname, mmag, mx, my, mz,            &
                                  coord_sys, istat)
      end do

    end do

  end subroutine sixdof_init


!============================= SIXDOFAPPLYFORCEMOMENT ========================80
!
! Apply the integrated forces and moments
!
!=============================================================================80

  subroutine sixdof_apply_force_moment(moving_body, dimensional_time, ierr)

    use info_depr,           only : twod
    use nml_global,          only : grid_motion_only
    use nml_grid_motion,     only : aero_force_factor, aero_xmoment_factor,    &
                                    aero_ymoment_factor, aero_zmoment_factor,  &
                                    time_factor
    use grid_motion_helpers, only : write_external_force_file,                 &
                                    write_aero_force_file,                     &
                                    get_inverse_transform
    use string_utils,        only : sub_string

    real(dp),        intent(in)   :: dimensional_time

    integer,         intent(out) :: ierr

    type(moving_body_type), dimension(n_moving_bodies), intent(inout) ::       &
                                                                  moving_body

    character(len=80)        :: forcename, momentname

    integer                  :: body, j, id, istat, coord_sys

    real(dp), dimension(4,4) :: trans
    real(dp), dimension(3)   :: totforce, totmoment
    real(dp), dimension(3)   :: extforce, extmoment
    real(dp)                 :: fmag, mmag, nondim_time
    real(dp)                 :: fx, fy, fz, mx, my, mz, px, py, pz
    real(dp)                 :: external_fx_total, external_fy_total
    real(dp)                 :: external_fz_total
    real(dp)                 :: external_mx_total, external_my_total
    real(dp)                 :: external_mz_total

  continue

   ierr = 0

!  Delete all current forces - aerodynamic and external

   delete_old_loop : do body = 1,n_moving_bodies
     if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle
     id = body_id(body)
     call mofDeleteAllBodyFandM(id,istat)
   end do delete_old_loop

!  Apply new aerodyanamic forces and moments

!  Note: 1) Flow solver computes forces and moments in the inertial frame
!        2) LIBMO requires the aerodynamic F&M in the LIBMO (6DOF) body frame
!        3) The LIBMO body frame and the FUN3D body frame differ; both have
!           y pointing out the pilots right wing, but FUN3D has x pointing
!           aft and z up while the 6DOF wants x pointing fwd and z down

   body_loop_aero : do body = 1,n_moving_bodies

     if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

     id = body_id(body)

     coord_sys = id      ! 0 => inertial;  id => body system of body "id"

!    Grab the aerodynamic forces and moments from the flow solver; these are
!    in the inertial frame

     fx = moving_body(body)%totforce%cx
     fy = moving_body(body)%totforce%cy
     fz = moving_body(body)%totforce%cz

     mx = moving_body(body)%totforce%cmx
     my = moving_body(body)%totforce%cmy
     mz = moving_body(body)%totforce%cmz

!    Apply the force/moment masks (default values are 1.0, i.e. use the
!    particular force/moment; a value of 0.0 turns off that force/moment)
!    These are assumed to be set in the inertial system, to constrain
!    motion in the inertial frame

     fx  = fx*use_aeroforce(1,body)
     fy  = fy*use_aeroforce(2,body)
     fz  = fz*use_aeroforce(3,body)

     mx = mx*use_aeromoment(1,body)
     my = my*use_aeromoment(2,body)
     mz = mz*use_aeromoment(3,body)

!    Convert these from the inertial frame to the (FUN3D) body frame

     trans(:,:) = inertial_to_fun3d(:,:,body)

     totforce(1)  = fx*trans(1,1) + fy*trans(1,2) + fz*trans(1,3)
     totforce(2)  = fx*trans(2,1) + fy*trans(2,2) + fz*trans(2,3)
     totforce(3)  = fx*trans(3,1) + fy*trans(3,2) + fz*trans(3,3)

     totmoment(1) = mx*trans(1,1) + my*trans(1,2) + mz*trans(1,3)
     totmoment(2) = mx*trans(2,1) + my*trans(2,2) + mz*trans(2,3)
     totmoment(3) = mx*trans(3,1) + my*trans(3,2) + mz*trans(3,3)

!    Finally, convert from the FUN3D frame to the LIBMO frame

     trans(:,:) = fun3d_to_libmo(:,:,body)

     fx = totforce(1)
     fy = totforce(2)
     fz = totforce(3)

     mx = totmoment(1)
     my = totmoment(2)
     mz = totmoment(3)

     totforce(1)  = fx*trans(1,1) + fy*trans(1,2) + fz*trans(1,3)
     totforce(2)  = fx*trans(2,1) + fy*trans(2,2) + fz*trans(2,3)
     totforce(3)  = fx*trans(3,1) + fy*trans(3,2) + fz*trans(3,3)

     totmoment(1) = mx*trans(1,1) + my*trans(1,2) + mz*trans(1,3)
     totmoment(2) = mx*trans(2,1) + my*trans(2,2) + mz*trans(2,3)
     totmoment(3) = mx*trans(3,1) + my*trans(3,2) + mz*trans(3,3)

!    Zero all aerodynamic forces and moments if we are running grid_motion_only

     if (grid_motion_only) then
       totforce(:)  = 0._dp
       totmoment(:) = 0._dp
     end if

!    Option to use specified aerodynamic forces and moments instead of computed
!    ones (a hack for for debugging/testing; can be used with grid_motion_only)
!    Note: specified values are assumed to be in FUN3D body frame coordinates

     if (use_specified_aero_data) then

       call get_specified_aero_data(body, dimensional_time, totforce(1),       &
                                    totforce(2), totforce(3), totmoment(1),    &
                                    totmoment(2), totmoment(3), ierr)

       if (ierr /= 0 ) return

!      Dump these into the standard aero_forces file so we can plot these
!      against the computed values if desired

       nondim_time = dimensional_time*time_factor
       call write_aero_force_file(body, nondim_time, totforce(1), totforce(2), &
                                  totforce(3), totmoment(1), totmoment(2),     &
                                  totmoment(3), 'Specified_AeroForceMomentBody')

!      Next convert forces/moments from FUN3D to LIBMO coords
!      (moments assumed applied about CG in both coordinate systems)

       trans(:,:) = fun3d_to_libmo(:,:,body)

       fx = totforce(1)
       fy = totforce(2)
       fz = totforce(3)
       totforce(1)  = fx*trans(1,1) + fy*trans(1,2) + fz*trans(1,3)
       totforce(2)  = fx*trans(2,1) + fy*trans(2,2) + fz*trans(2,3)
       totforce(3)  = fx*trans(3,1) + fy*trans(3,2) + fz*trans(3,3)

       mx = totmoment(1)
       my = totmoment(2)
       mz = totmoment(3)
       totmoment(1) = mx*trans(1,1) + my*trans(1,2) + mz*trans(1,3)
       totmoment(2) = mx*trans(2,1) + my*trans(2,2) + mz*trans(2,3)
       totmoment(3) = mx*trans(3,1) + my*trans(3,2) + mz*trans(3,3)

     end if

!    Convert aerodynamic force and moment coefficients to dimensional forces
!    and moments

     totforce(:)  = totforce(:)/aero_force_factor(body)
     totmoment(1) = totmoment(1)/aero_xmoment_factor(body)
     totmoment(2) = totmoment(2)/aero_ymoment_factor(body)
     totmoment(3) = totmoment(3)/aero_zmoment_factor(body)

!    Explicitly enforce zero side force, yawing and rolling moments in 2D
!    in order to prevent slight drift away from exact 2D alignment over time

     if (twod) then
       totforce(2)  = 0._dp
       totmoment(1) = 0._dp
       totmoment(3) = 0._dp
     end if

!    Apply current aerodynamic forces and moments at the CG

     fmag = sqrt(totforce(1)**2 + totforce(2)**2 + totforce(3)**2)

     if (fmag > 0.0_dp) then
       fx = totforce(1)/fmag
       fy = totforce(2)/fmag
       fz = totforce(3)/fmag
     else
       fx = 1.0_dp
       fy = 0.0_dp
       fz = 0.0_dp
     end if

     call mofNewConstantCGForce(id, aeroforce, fmag, fx, fy, fz,               &
                                coord_sys, istat)

     mmag = sqrt(totmoment(1)**2 + totmoment(2)**2 + totmoment(3)**2)

     if (mmag > 0.0_dp) then
       mx = totmoment(1)/mmag
       my = totmoment(2)/mmag
       mz = totmoment(3)/mmag
     else
       mx = 1.0_dp
       my = 0.0_dp
       mz = 0.0_dp
     end if

     call mofNewConstantMoment(id, aeromoment, mmag, mx, my, mz,               &
                               coord_sys, istat)

   end do body_loop_aero

!  Apply new external forces and moments

   body_loop_ext : do body = 1,n_moving_bodies

     if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

     id = body_id(body)

!    Sum over the applied external forces

     external_fx_total = 0.0_dp
     external_fy_total = 0.0_dp
     external_fz_total = 0.0_dp

     ext_force_loop : do j = 1,n_extforce(body)

!      Inititalize

       extforce(:)  = 0.0_dp

!      Apply external force at the specified point
!      Applied in the user-specified coord system

       call get_external_force(body, j, dimensional_time, extforce(1),         &
                               extforce(2), extforce(3), px, py, pz, coord_sys)

       if (coord_sys == 0) then

!        Convert the forces/moments from the inertial frame to the (FUN3D) body
!        frame; note: the application point is assumes to be specified in
!        FUN3D body coords, so px, py, pz are not converted in this step

         trans(:,:) = inertial_to_fun3d(:,:,body)

         fx = extforce(1)
         fy = extforce(2)
         fz = extforce(3)
         extforce(1)  = fx*trans(1,1) + fy*trans(1,2) + fz*trans(1,3)
         extforce(2)  = fx*trans(2,1) + fy*trans(2,2) + fz*trans(2,3)
         extforce(3)  = fx*trans(3,1) + fy*trans(3,2) + fz*trans(3,3)

         mx = extmoment(1)
         my = extmoment(2)
         mz = extmoment(3)
         extmoment(1) = mx*trans(1,1) + my*trans(1,2) + mz*trans(1,3)
         extmoment(2) = mx*trans(2,1) + my*trans(2,2) + mz*trans(2,3)
         extmoment(3) = mx*trans(3,1) + my*trans(3,2) + mz*trans(3,3)

       end if

!      Convert external forces from fun3d to libmo coords

       trans(:,:) = fun3d_to_libmo(:,:,body)

       fx = extforce(1)
       fy = extforce(2)
       fz = extforce(3)
       extforce(1) = fx*trans(1,1) + fy*trans(1,2) + fz*trans(1,3)
       extforce(2) = fx*trans(2,1) + fy*trans(2,2) + fz*trans(2,3)
       extforce(3) = fx*trans(3,1) + fy*trans(3,2) + fz*trans(3,3)

!      Convert application point of the external force to libmo coords

       fx = px
       fy = py
       fz = pz
       px = fx*trans(1,1) + fy*trans(1,2) + fz*trans(1,3) + trans(1,4)
       py = fx*trans(2,1) + fy*trans(2,2) + fz*trans(2,3) + trans(2,4)
       pz = fx*trans(3,1) + fy*trans(3,2) + fz*trans(3,3) + trans(3,4)

       fmag = sqrt(extforce(1)**2 + extforce(2)**2 + extforce(3)**2)

       if (fmag > 0.0_dp) then
         fx = extforce(1)/fmag
         fy = extforce(2)/fmag
         fz = extforce(3)/fmag
       else
         fx = 1.0_dp
         fy = 0.0_dp
         fz = 0.0_dp
       end if

       forcename = body_external_force(body)%external_force(j)%force_name
       call mofNewConstantForce(id, forcename, fmag, px, py , pz,              &
                                 fx, fy, fz, coord_sys, istat)

       external_fx_total = external_fx_total + extforce(1)
       external_fy_total = external_fy_total + extforce(2)
       external_fz_total = external_fz_total + extforce(3)

     end do ext_force_loop

!    Sum over the applied external moments

     external_mx_total = 0.0_dp
     external_my_total = 0.0_dp
     external_mz_total = 0.0_dp

     ext_moment_loop : do j = 1,n_extmoment(body)

!      Inititalize

       extmoment(:)  = 0.0_dp

!      Apply external moment
!      Applied in the user-specified coord system

       call get_external_moment(body, j, dimensional_time, extmoment(1),       &
                                extmoment(2), extmoment(3), coord_sys)

!      Convert external moments from fun3d to libmo coords
!      (moments assumed applied about CG in both coordinate systems)

       trans(:,:) = fun3d_to_libmo(:,:,body)

       mx = extmoment(1)
       my = extmoment(2)
       mz = extmoment(3)
       extmoment(1) = mx*trans(1,1) + my*trans(1,2) + mz*trans(1,3)
       extmoment(2) = mx*trans(2,1) + my*trans(2,2) + mz*trans(2,3)
       extmoment(3) = mx*trans(3,1) + my*trans(3,2) + mz*trans(3,3)

       mmag = sqrt(extmoment(1)**2 + extmoment(2)**2 + extmoment(3)**2)

       if (mmag > 0.0_dp) then
         mx = extmoment(1)/mmag
         my = extmoment(2)/mmag
         mz = extmoment(3)/mmag
       else
         mx = 1.0_dp
         my = 0.0_dp
         mz = 0.0_dp
       end if

       momentname = body_external_moment(body)%external_moment(j)%moment_name
       call mofNewConstantMoment(id, momentname, mmag, mx, my, mz,             &
                                 coord_sys, istat)

       external_mx_total = external_mx_total + extmoment(1)
       external_my_total = external_my_total + extmoment(2)
       external_mz_total = external_mz_total + extmoment(3)

     end do ext_moment_loop

     if (n_extforce(body) > 0 .or. n_extmoment(body) > 0) then
       call write_external_force_file(body, dimensional_time,                  &
                                      external_fx_total, external_fy_total,    &
                                      external_fz_total, external_mx_total,    &
                                      external_my_total, external_mz_total)
     end if

   end do body_loop_ext

  end subroutine sixdof_apply_force_moment


!============================= READ_EXTERNAL_FORCE ===========================80
!
! Reads data from external force file and sets up derived types
!
!=============================================================================80

  subroutine read_external_force(moving_body)

    use system_extensions,   only : se_open
    use lmpi,                only : lmpi_conditional_stop, lmpi_master
    use allocations,         only : my_alloc_ptr
    use file_utils,          only : available_unit
    use string_utils,        only : sub_string

    type(moving_body_type), dimension(n_moving_bodies), intent(in) ::          &
                                                                   moving_body

    character(len=80)          :: force_name, body_name, body_name_file

    integer                    :: gunit, n_data_pts, repeat_flag
    integer                    :: j, n, iostat, body, array_size, coord_sys

    real(dp)                   :: time_file, fx_file, fy_file, fz_file
    real(dp)                   :: xloc_file, yloc_file, zloc_file

  continue

    array_size = max(1,n_moving_bodies)

    allocate(body_external_force(array_size))

    body_loop : do body = 1,n_moving_bodies

      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      body_external_force(body)%n_extforce = n_extforce(body)

      array_size = max(1,n_extforce(body))

      allocate(body_external_force(body)%external_force(array_size))

      iostat = 0

      force_loop : do j = 1, body_external_force(body)%n_extforce

        gunit = available_unit()

        call se_open(gunit, file=file_extforce(j,body),      &
                     status='old', iostat=iostat)

        rewind(gunit)

        if (iostat /= 0) then
          if (lmpi_master) then
            write(*,'(2a)')'Stopping...error reading external force file ',    &
                            trim(file_extforce(j,body))
          end if
          exit force_loop
        end if

!       read the body name from the file and check against current body name

        read(gunit,*) ! header
        read(gunit,*) body_name_file

        body_name = moving_body(body)%body_name
        if (trim(body_name_file) /= trim(body_name)) then
          iostat = 1
          if (lmpi_master) then
            write(*,'(2a)')'Stopping...incorrect body name in force file ',    &
                            trim(file_extforce(j,body))
            write(*,'(2a)') 'body name in file : ',trim(body_name_file)
            write(*,'(2a)') 'current body name : ',trim(body_name)
          end if
          exit force_loop
        end if

        read(gunit,*) ! header
        read(gunit,*) force_name
        body_external_force(body)%external_force(j)%force_name = force_name

        read(gunit,*) ! header
        read(gunit,*) coord_sys
        body_external_force(body)%external_force(j)%coord_sys = coord_sys

        read(gunit,*) ! header
        read(gunit,*) n_data_pts
        body_external_force(body)%external_force(j)%n_data_pts = n_data_pts

        read(gunit,*) ! header
        read(gunit,*) repeat_flag
        body_external_force(body)%external_force(j)%repeat_flag = repeat_flag

        array_size = max(1,n_data_pts)

        call my_alloc_ptr(body_external_force(body)%external_force(j)%time,    &
                                                                    array_size)
        call my_alloc_ptr(body_external_force(body)%external_force(j)%fx,      &
                                                                    array_size)
        call my_alloc_ptr(body_external_force(body)%external_force(j)%fy,      &
                                                                    array_size)
        call my_alloc_ptr(body_external_force(body)%external_force(j)%fz,      &
                          array_size)
        call my_alloc_ptr(body_external_force(body)%external_force(j)%xloc,    &
                                                                    array_size)
        call my_alloc_ptr(body_external_force(body)%external_force(j)%yloc,    &
                                                                    array_size)
        call my_alloc_ptr(body_external_force(body)%external_force(j)%zloc,    &
                                                                    array_size)

        read(gunit,*) ! header

        do n = 1,n_data_pts

          read(gunit,*) time_file, fx_file, fy_file, fz_file,                  &
                        xloc_file, yloc_file, zloc_file

          body_external_force(body)%external_force(j)%time(n) = time_file

          body_external_force(body)%external_force(j)%fx(n) = fx_file
          body_external_force(body)%external_force(j)%fy(n) = fy_file
          body_external_force(body)%external_force(j)%fz(n) = fz_file

          body_external_force(body)%external_force(j)%xloc(n) = xloc_file
          body_external_force(body)%external_force(j)%yloc(n) = yloc_file
          body_external_force(body)%external_force(j)%zloc(n) = zloc_file

        end do

        close(gunit)

      end do force_loop

      call lmpi_conditional_stop(iostat, 'setup_external_force')

    end do body_loop

  end subroutine read_external_force


!============================= READ_EXTERNAL_MOMENT ==========================80
!
! Reads data from external moment file and sets up derived types
!
!=============================================================================80

  subroutine read_external_moment(moving_body)

    use system_extensions,   only : se_open
    use lmpi,                only : lmpi_conditional_stop, lmpi_master
    use allocations,         only : my_alloc_ptr
    use file_utils,          only : available_unit
    use string_utils,        only : sub_string

    type(moving_body_type), dimension(n_moving_bodies), intent(in) ::          &
                                                                   moving_body

    character(len=80)          :: moment_name, body_name, body_name_file

    integer                    :: gunit, n_data_pts, repeat_flag
    integer                    :: j, n, iostat, body, array_size, coord_sys

    real(dp)                   :: time_file, mx_file, my_file, mz_file

  continue

    array_size = max(1,n_moving_bodies)

    allocate(body_external_moment(array_size))

    body_loop : do body = 1,n_moving_bodies

      if (.not. sub_string(moving_body(body)%motion_driver,'6dof')) cycle

      body_external_moment(body)%n_extmoment = n_extmoment(body)

      array_size = max(1,n_extmoment(body))

      allocate(body_external_moment(body)%external_moment(array_size))

      iostat = 0

      moment_loop : do j = 1, body_external_moment(body)%n_extmoment

        gunit = available_unit()

        call se_open(gunit, file=file_extmoment(j,body),     &
                     status='old', iostat=iostat)

        rewind(gunit)

        if (iostat /= 0) then
          if (lmpi_master) then
            write(*,'(2a)')'Stopping...error reading external moment file ',   &
                            trim(file_extmoment(j,body))
          end if
          exit moment_loop
        end if

!       read the body name from the file and check against current body name

        read(gunit,*) ! header
        read(gunit,*) body_name_file

        body_name = moving_body(body)%body_name
        if (trim(body_name_file) /= trim(body_name)) then
          iostat = 1
          if (lmpi_master) then
            write(*,'(2a)')'Stopping...incorrect body name in moment file ',   &
                            trim(file_extmoment(j,body))
            write(*,'(2a)') 'body name in file : ',trim(body_name_file)
            write(*,'(2a)') 'current body name : ',trim(body_name)
          end if
          exit moment_loop
        end if

        read(gunit,*) ! header
        read(gunit,*) moment_name
        body_external_moment(body)%external_moment(j)%moment_name = moment_name

        read(gunit,*) ! header
        read(gunit,*) coord_sys
        body_external_moment(body)%external_moment(j)%coord_sys = coord_sys

        read(gunit,*) ! header
        read(gunit,*) n_data_pts
        body_external_moment(body)%external_moment(j)%n_data_pts = n_data_pts

        read(gunit,*) ! header
        read(gunit,*) repeat_flag
        body_external_moment(body)%external_moment(j)%repeat_flag = repeat_flag

        array_size = max(1,n_data_pts)

        call my_alloc_ptr(body_external_moment(body)%external_moment(j)%time,  &
                                                                    array_size)
        call my_alloc_ptr(body_external_moment(body)%external_moment(j)%mx,    &
                                                                    array_size)
        call my_alloc_ptr(body_external_moment(body)%external_moment(j)%my,    &
                                                                    array_size)
        call my_alloc_ptr(body_external_moment(body)%external_moment(j)%mz,    &
                                                                    array_size)

        read(gunit,*) ! header

        do n = 1,n_data_pts

          read(gunit,*) time_file, mx_file, my_file, mz_file

          body_external_moment(body)%external_moment(j)%time(n) = time_file
          body_external_moment(body)%external_moment(j)%mx(n)   = mx_file
          body_external_moment(body)%external_moment(j)%my(n)   = my_file
          body_external_moment(body)%external_moment(j)%mz(n)   = mz_file

        end do

        close(gunit)

      end do moment_loop

      call lmpi_conditional_stop(iostat, 'setup_external_moment')

    end do body_loop

  end subroutine read_external_moment


!=============================== GET_EXTERNAL_FORCE ==========================80
!
! Get the specified external force acting on the body
!
!=============================================================================80

  subroutine get_external_force(body, force, dimensional_time, fx, fy, fz,     &
                                x_loc, y_loc, z_loc, coord_sys)

    integer,       intent(in)  :: body, force
    integer,       intent(out) :: coord_sys

    real(dp),      intent(in)  :: dimensional_time
    real(dp),      intent(out) :: fx, fy, fz, x_loc, y_loc, z_loc

    integer                            :: ndata, repeat_flag

    real(dp), dimension(:), pointer    :: datat, datax, datay, dataz

    real(dp)                           :: time, tmax, period

  continue

    time = dimensional_time

    ndata = body_external_force(body)%external_force(force)%n_data_pts

    tmax = body_external_force(body)%external_force(force)%time(ndata)

    repeat_flag = body_external_force(body)%external_force(force)%repeat_flag

!   make sure a non-zero coord sys number matches assigned body id

    coord_sys = body_external_force(body)%external_force(force)%coord_sys
    if (coord_sys /= 0) coord_sys = body_id(body)
    body_external_force(body)%external_force(force)%coord_sys = coord_sys

    if (time <= tmax) then

!     interpolate non-periodic data

      period = 1.0e99_dp

      datat => body_external_force(body)%external_force(force)%time
      datax => body_external_force(body)%external_force(force)%fx
      datay => body_external_force(body)%external_force(force)%fy
      dataz => body_external_force(body)%external_force(force)%fz

      call interp_data_in_time(time, ndata, datat, datax, datay, dataz,        &
                               period, fx, fy, fz)

      datax => body_external_force(body)%external_force(force)%xloc
      datay => body_external_force(body)%external_force(force)%yloc
      dataz => body_external_force(body)%external_force(force)%zloc

      call interp_data_in_time(time, ndata, datat, datax, datay, dataz,        &
                               period, x_loc, y_loc, z_loc)

    else

      select case (repeat_flag)

        case(0)  ! keep final input value for all remaining time

          fx    = body_external_force(body)%external_force(force)%fx(ndata)
          fy    = body_external_force(body)%external_force(force)%fy(ndata)
          fz    = body_external_force(body)%external_force(force)%fz(ndata)

          x_loc = body_external_force(body)%external_force(force)%xloc(ndata)
          y_loc = body_external_force(body)%external_force(force)%yloc(ndata)
          z_loc = body_external_force(body)%external_force(force)%zloc(ndata)

        case(1)  ! interpolate periodic data

          period = tmax

          datat => body_external_force(body)%external_force(force)%time
          datax => body_external_force(body)%external_force(force)%fx
          datay => body_external_force(body)%external_force(force)%fy
          dataz => body_external_force(body)%external_force(force)%fz

          call interp_data_in_time(time, ndata, datat, datax, datay, dataz,    &
                                   period, fx, fy, fz)

          datax => body_external_force(body)%external_force(force)%xloc
          datay => body_external_force(body)%external_force(force)%yloc
          dataz => body_external_force(body)%external_force(force)%zloc

          call interp_data_in_time(time, ndata, datat, datax, datay, dataz,    &
                                   period, x_loc, y_loc, z_loc)

        case default

      end select

    end if

  end subroutine get_external_force


!============================== GET_EXTERNAL_MOMENT ==========================80
!
! Get the specified external moment acting on the body
!
!=============================================================================80

  subroutine get_external_moment(body, moment, dimensional_time, mx, my, mz,   &
                                 coord_sys)

    integer,       intent(in)  :: body, moment
    integer,       intent(out) :: coord_sys

    real(dp),      intent(in)  :: dimensional_time
    real(dp),      intent(out) :: mx, my, mz

    integer                            :: ndata, repeat_flag

    real(dp), dimension(:), pointer    :: datat, datax, datay, dataz

    real(dp)                           :: time, tmax, period

  continue

    time = dimensional_time

    ndata = body_external_moment(body)%external_moment(moment)%n_data_pts

    tmax = body_external_moment(body)%external_moment(moment)%time(ndata)

    repeat_flag = body_external_moment(body)%external_moment(moment)%repeat_flag

!   make sure a non-zero coord sys number matches assigned body id

    coord_sys = body_external_moment(body)%external_moment(moment)%coord_sys
    if (coord_sys /= 0) coord_sys = body_id(body)
    body_external_moment(body)%external_moment(moment)%coord_sys = coord_sys

    if (time <= tmax) then

!     interpolate non-periodic data

      period = 1.0e99_dp

      datat => body_external_moment(body)%external_moment(moment)%time
      datax => body_external_moment(body)%external_moment(moment)%mx
      datay => body_external_moment(body)%external_moment(moment)%my
      dataz => body_external_moment(body)%external_moment(moment)%mz

      call interp_data_in_time(time, ndata, datat, datax, datay, dataz,        &
                               period, mx, my, mz)

    else

      select case (repeat_flag)

        case(0)  ! keep final input value for all remaining time

          mx    = body_external_moment(body)%external_moment(moment)%mx(ndata)
          my    = body_external_moment(body)%external_moment(moment)%my(ndata)
          mz    = body_external_moment(body)%external_moment(moment)%mz(ndata)

        case(1)  ! interpolate periodic data

          period = tmax

          datat => body_external_moment(body)%external_moment(moment)%time
          datax => body_external_moment(body)%external_moment(moment)%mx
          datay => body_external_moment(body)%external_moment(moment)%my
          dataz => body_external_moment(body)%external_moment(moment)%mz

          call interp_data_in_time(time, ndata, datat, datax, datay, dataz,    &
                                   period, mx, my, mz)

        case default

      end select

    end if

  end subroutine get_external_moment


!=========================== INTERP_DATA_IN_TIME =============================80
!
! Interpolates x,y,z data to the current point in time
!
!=============================================================================80

  subroutine interp_data_in_time(time, n_data, datat, datax, datay, dataz,     &
                                 period, interpx, interpy, interpz)

    integer,                        intent(in)  :: n_data

    real(dp),                       intent(in)  :: time, period
    real(dp), dimension(n_data),    intent(in)  :: datat, datax, datay, dataz
    real(dp),                       intent(out) :: interpx, interpy, interpz

    integer                  :: nperiods, time_index, index

    real(dp)                 :: local_time, coeff
    real(dp)                 :: time1, time2, dx, dy, dz

  continue

    interpolation_needed : if (n_data == 1) then

!     just one value...no interpolation needed

      interpx = datax(1)
      interpy = datay(1)
      interpz = dataz(1)

    else interpolation_needed

!     need to interpolate between data points

      nperiods = 0

!     determine where the current time lies in terms of period of data

      do

        local_time = time + epsilon(1.0_dp) - nperiods*period

        if (local_time > 0.0_dp .and. local_time <= period) exit

        nperiods = nperiods + 1

!       make sure we don't go on ad infinitum...

        if (nperiods > 1e6 .or. local_time < 0.0_dp) then
          write(*,*)'stopping...'
          write(*,*)'  could not bracket time within a period of the data'
          stop
        end if

      end do

!     determine linear interpolation coefficients

      index = 0
      coeff = 0.0_dp

      do time_index = 2,n_data

        time1 = datat(time_index-1)
        time2 = datat(time_index)

        if ((local_time >=  time1) .and. (local_time <= time2)) then
          coeff = (local_time - time1)/(time2-time1)
          index = time_index
          exit
        end if

!       next 2 "if...then" blocks are for roundoff safety when first data point
!       or last data point corresponds almost exactly to local time

        if (time_index == 2 .and. (local_time <  time1)) then
          index = 2
          coeff = 0.0_dp
          exit
        end if

        if (time_index == n_data .and. (local_time >  time2)) then
          index = n_data
          coeff = 1.0_dp
          exit
        end if

      end do

      if (index == 0) then
        write(*,*)'stopping...error in interp_data, index = ',index
        write(*,*)'local_time, max_local_time = ',local_time,                  &
                  datat(n_data)
        stop
      end if

      dx = datax(index) - datax(index-1)
      dy = datay(index) - datay(index-1)
      dz = dataz(index) - dataz(index-1)

      interpx = datax(index-1) + coeff*dx
      interpy = datay(index-1) + coeff*dy
      interpz = dataz(index-1) + coeff*dz

    end if interpolation_needed

  end subroutine interp_data_in_time


!=========================== GET_SPECIFIED_AERO_DATA =========================80
!
! Sets aerodynamic force/moment coefficients from a data file (rather than using
! the data computed by th e flow solver); this routine is something of a hack
! and should be used for only for testing/debugging; currently limited to one
! body (or the 1st body if more than 1)
!
!=============================================================================80

  subroutine get_specified_aero_data(body, dimensional_time, fx, fy, fz,       &
                                     mx, my, mz, ierr)

    use file_utils,          only : available_unit
    use grid_motion_helpers, only : get_inverse_transform

    integer,                    intent(in)    :: body
    integer,                    intent(out)   :: ierr

    real(dp),                   intent(in)    :: dimensional_time
    real(dp),                   intent(inout) :: fx, fy, fz, mx, my, mz

    integer                                   :: i, iunit, n, coord_sys_file
    integer,                             save :: ndata
    integer, dimension(6),               save :: use_input_data

    logical,                             save :: init = .true.

    character(len=80)                         :: filename, body_str

    real(dp)                                  :: period
    real(dp)                                  :: fxs, fys, fzs, mxs, mys, mzs

    real(dp), dimension(:), allocatable, save :: cx, cy, cz, cmx, cmy, cmz
    real(dp), dimension(:), allocatable, save :: time

  continue

    ierr = 0

    write(body_str,'(i0)') body
    filename = 'specified_aero_data' // '_body' // trim(body_str) // '.dat'

    read_data : if (init) then

      iunit = available_unit()

      open(unit=iunit,file=filename,status='old')

      read(iunit,*) ! header
      read(iunit,*) coord_sys_file

!     currently the specified data must be in FUN3D body coords
!     (not in the inertial or other frame)

      if (coord_sys_file /= body) then
        ierr = 1
        write(*,'(a)')'Error in get_specified_aero_data:'
        write(*,'(a,i0,2a,i0)') 'for body ', body, ' aero forces specified in',&
                                ' coord system ', coord_sys_file
      end if
      read(iunit,*) ! header
      read(iunit,*) ndata

      read(iunit,*) ! header
      read(iunit,*) (use_input_data(i),i=1,6)

      allocate(cx(ndata))
      allocate(cy(ndata))
      allocate(cz(ndata))
      allocate(cmx(ndata))
      allocate(cmy(ndata))
      allocate(cmz(ndata))
      allocate(time(ndata))

      read(iunit,*) ! header

      do n = 1,ndata
        read(iunit,*) time(n), cx(n), cy(n), cz(n), cmx(n), cmy(n), cmz(n)
      end do

      init = .false.

      close(iunit)

    end if read_data

    period = huge(1.0_dp)

    call interp_data_in_time(dimensional_time, ndata, time, cx, cy, cz,        &
                             period, fxs, fys, fzs)

    call interp_data_in_time(dimensional_time, ndata, time, cmx, cmy, cmz,     &
                             period, mxs, mys, mzs)

    if (use_input_data(1) == 1) fx = fxs
    if (use_input_data(2) == 1) fy = fys
    if (use_input_data(3) == 1) fz = fzs
    if (use_input_data(4) == 1) mx = mxs
    if (use_input_data(5) == 1) my = mys
    if (use_input_data(6) == 1) mz = mzs

     write(*,*)
     write(*,'(2a,i0)') ' Warning: replacing computed aero forces/moments',    &
             ' with user-supplied values in 6-DOF update for body ', body
     write(*,'(a,6i3)') ' use cx cy cz cmx cmy cmz = ',(use_input_data(i),i=1,6)

  end subroutine get_specified_aero_data


!============================= FUN3D_BODY_TO_LIBMO_BODY ======================80
!
! Sets up a transform matrix to go from FUN3D body coords to LIBMO body coords
!
! FUN3D body coords are (from a pilot's point of view):
!   x pointing aft, y pointing right,  z pointing up
!   CG is not necessarily at 0,0,0 in the body coords
!
! LIBMO (and standard AC S&C) coords are (from a pilot's point of view):
!   x pointing fwd, y pointing right,  z pointing down
!   CG is at 0,0,0 in the body coords
!
! The inverse of this goes from LIBMO to FUN3D
!
!=============================================================================80

  subroutine fun3d_body_to_libmo_body(xcg, ycg, zcg, fun3d_to_libmo_transform)

    use grid_motion_helpers, only : setup_transform_matrix
    use moving_body_types,   only : rotation_vector_type,                      &
                                    translation_vector_type

    real(dp),                 intent(in)  :: xcg, ycg, zcg
    real(dp), dimension(4,4), intent(out) :: fun3d_to_libmo_transform

    real(dp), dimension(4,4)              :: rotate_z, rotate_x, shift_cg
    real(dp)                              :: ds, sx, sy, sz

    type(rotation_vector_type)    :: rotation_vector
    type(translation_vector_type) :: translation_vector

  continue

!   first shift cg to origin

    rotation_vector%theta = 0.0_dp  ! no rotation

    ds = sqrt(xcg*xcg + ycg*ycg + zcg*zcg)
    if (ds > 0.0_dp) then
      sx = -xcg/ds
      sy = -ycg/ds
      sz = -zcg/ds
    else
      sx = 1.0_dp
      sy = 0.0_dp
      sz = 0.0_dp
    end if
    translation_vector%ds = ds
    translation_vector%sx = sx
    translation_vector%sy = sy
    translation_vector%sz = sz

    call setup_transform_matrix(shift_cg, rotation_vector, translation_vector)

!   next rotate 180 deg about z (rotation origin is now 0,0,0)

    rotation_vector%theta = acos(-1.0_dp)

    rotation_vector%tx = 0.0_dp
    rotation_vector%ty = 0.0_dp
    rotation_vector%tz = 1.0_dp

    rotation_vector%xorigin = 0.0_dp
    rotation_vector%yorigin = 0.0_dp
    rotation_vector%zorigin = 0.0_dp

    translation_vector%ds = 0.0_dp  ! defer shift of cg to the end
    translation_vector%sx = 1.0_dp
    translation_vector%sy = 0.0_dp
    translation_vector%sz = 0.0_dp

    call setup_transform_matrix(rotate_z, rotation_vector, translation_vector)

!   next, rotate 180 deg about x (rotation origin is now 0,0,0)

    rotation_vector%theta = acos(-1.0_dp)

    rotation_vector%tx = 1.0_dp
    rotation_vector%ty = 0.0_dp
    rotation_vector%tz = 0.0_dp

    rotation_vector%xorigin = 0.0_dp
    rotation_vector%yorigin = 0.0_dp
    rotation_vector%zorigin = 0.0_dp

    call setup_transform_matrix(rotate_x, rotation_vector, translation_vector)

!   the desired transform is the product of these three

!   fun3d_to_libmo_transform = matmul(shift_cg, rotate_z)
!   fun3d_to_libmo_transform = matmul(fun3d_to_libmo_transform, rotate_x)

    fun3d_to_libmo_transform = matmul(rotate_z, shift_cg)
    fun3d_to_libmo_transform = matmul(rotate_x, fun3d_to_libmo_transform)

  end subroutine fun3d_body_to_libmo_body

#else

!================================ CLEANUP_SIXDOF ============================80
!
! Stub version for use when HAVE_SIXDOF is not defined
!
!=============================================================================80

  subroutine cleanup_sixdof()

  continue

  end subroutine cleanup_sixdof


!================================= UPDATE_SIXDOF =============================80
!
! Stub version for use when HAVE_SIXDOF is not defined
!
!=============================================================================80

  subroutine update_sixdof(simulation_time, moving_body)

    real(dp),        intent(in)    :: simulation_time

    type(moving_body_type), dimension(n_moving_bodies), intent(inout) ::       &
                                                                    moving_body

  continue

! artificially use the argument data to avoid compiler warnings

  if (simulation_time > 0.0_dp) then
  end if

  if (moving_body(1)%body_name == '') then
  end if

  end subroutine update_sixdof


!================================== SET_UP_SIXDOF ============================80
!
! Stub version for use when HAVE_SIXDOF is not defined
!
!=============================================================================80

  subroutine set_up_sixdof(simulation_time, dt, moving_body)

    real(dp), intent(in) :: simulation_time, dt

    type(moving_body_type), dimension(n_moving_bodies), intent(inout) ::       &
                                                                   moving_body

  continue

! artificially use the argument data to avoid compiler warnings

  if (simulation_time > 0.0_dp) then
  end if

  if (dt > 0.0_dp) then
  end if

  if (moving_body(1)%body_name == '') then
  end if

  end subroutine set_up_sixdof

#endif

end module sixdof

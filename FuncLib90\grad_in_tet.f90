!=============================== GRAD_IN_TET =================================80
!
! Gradient in tet for var defined at vertices of tetrahedra
!
!=============================================================================80

pure function grad_in_tet( var, x, y, z, cell_volume )

  use kinddefs, only : dp

  real(dp), dimension(4), intent(in) :: var, x, y, z
  real(dp),               intent(in) :: cell_volume

  real(dp), dimension(3) :: grad_in_tet

  real(dp) :: nx1, nx2, nx3, nx4
  real(dp) :: ny1, ny2, ny3, ny4
  real(dp) :: nz1, nz2, nz3, nz4

  real(dp), parameter :: sixth = 1.0_dp/6.0_dp

  continue

! outward normals

  nx1 = sixth*( (y(2)-y(4))*(z(3)-z(4)) - (y(3)-y(4))*(z(2)-z(4)) )
  ny1 = sixth*( (z(2)-z(4))*(x(3)-x(4)) - (z(3)-z(4))*(x(2)-x(4)) )
  nz1 = sixth*( (x(2)-x(4))*(y(3)-y(4)) - (x(3)-x(4))*(y(2)-y(4)) )

  nx2 = sixth*( (y(3)-y(4))*(z(1)-z(4)) - (y(1)-y(4))*(z(3)-z(4)) )
  ny2 = sixth*( (z(3)-z(4))*(x(1)-x(4)) - (z(1)-z(4))*(x(3)-x(4)) )
  nz2 = sixth*( (x(3)-x(4))*(y(1)-y(4)) - (x(1)-x(4))*(y(3)-y(4)) )

  nx3 = sixth*( (y(1)-y(4))*(z(2)-z(4)) - (y(2)-y(4))*(z(1)-z(4)) )
  ny3 = sixth*( (z(1)-z(4))*(x(2)-x(4)) - (z(2)-z(4))*(x(1)-x(4)) )
  nz3 = sixth*( (x(1)-x(4))*(y(2)-y(4)) - (x(2)-x(4))*(y(1)-y(4)) )

  nx4 = -nx1 - nx2 - nx3
  ny4 = -ny1 - ny2 - ny3
  nz4 = -nz1 - nz2 - nz3

  grad_in_tet(1) = -( (var(4)-var(1))*nx4 &
                    + (var(2)-var(1))*nx2 &
                    + (var(3)-var(1))*nx3 &
                    ) / cell_volume
  grad_in_tet(2) = -( (var(4)-var(1))*ny4 &
                    + (var(2)-var(1))*ny2 &
                    + (var(3)-var(1))*ny3 &
                    ) / cell_volume
  grad_in_tet(3) = -( (var(4)-var(1))*nz4 &
                    + (var(2)-var(1))*nz2 &
                    + (var(3)-var(1))*nz3 &
                    ) / cell_volume

end function grad_in_tet

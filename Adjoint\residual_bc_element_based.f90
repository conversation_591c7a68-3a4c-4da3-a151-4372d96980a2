module residual_bc_element_based

  implicit none

  private

  public :: atlam_bc_element_based

contains

  subroutine atlam_bc_element_based( grid, soln, sadj, &
    design, coltag, rlam, crow )

    use kinddefs,       only : dp
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use solution_adj,   only : sadj_type
    use design_types,   only : design_type
    use comprow_types,  only : crow_type

    use bc_names,       only : element_based_bc
    use solution_types, only : incompressible

    type(grid_type),                            intent(in)    :: grid
    type(soln_type),                            intent(in)    :: soln
    type(sadj_type),                            intent(inout) :: sadj

    type(design_type),                 intent(in), optional :: design
    real(dp),        dimension(:,:),   intent(in), optional :: coltag
    real(dp),        dimension(:,:,:), intent(in), optional :: rlam
    type(crow_type),                   intent(in), optional :: crow

    integer :: ib

    continue

    if ( soln%eqn_set == incompressible ) return !FIXME - hack to bypass

    each_bc :do ib = 1, grid%nbound
      has_element : if ( element_based_bc(grid%bc(ib)%ibc) ) then
        call atlam_bc_element_based_ib(ib, grid, soln, sadj, &
          design, coltag, rlam, crow )
      end if has_element
    end do each_bc

  end subroutine atlam_bc_element_based

  subroutine atlam_bc_element_based_ib(ib, grid, soln, sadj, &
    design, coltag, rlam, crow )

    use kinddefs,                 only : dp
    use lmpi,                     only : lmpi_master, lmpi_die
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type
    use solution_adj,             only : sadj_type
    use design_types,             only : design_type
    use comprow_types,            only : crow_type
    use info_depr,                only : twod
    use twod_util,                only : yplane_2d, y_coplanar_tol
    use bc_names,                 only : massflow_out, massflow_in
    use thermo,                   only : dconserved_dprimitive &
                                       , dprimitive_dconserved
    use element_based_bc_util,    only : element_based_metrics
    use bc_element_based,         only : element_based_interp
    use bc_state,                 only : bc_fetch_jacobian,                    &
                                         update_massflow_out_system, sink,     &
                                         update_massflow_in_system, source
    use bc_names,                 only : viscous_solid, tangency

    integer,                                    intent(in)    :: ib
    type(grid_type),                            intent(in)    :: grid
    type(soln_type),                            intent(in)    :: soln
    type(sadj_type),                            intent(inout) :: sadj

    type(design_type),                 intent(in), optional :: design
    real(dp),        dimension(:,:),   intent(in), optional :: coltag
    real(dp),        dimension(:,:,:), intent(in), optional :: rlam
    type(crow_type),                   intent(in), optional :: crow


    real(dp) :: dfp(5,5)
    real(dp) :: dqr_dqi(5,5)
    real(dp) :: df_dq(5,5,2), dfp_dq(5,5)
    real(dp) :: df_dqr(5,5)
    real(dp) :: df_dql(5,5)
    real(dp) :: df_conserved(5,5)
    real(dp) :: dQdq(5,5)

    real(dp), dimension(5) :: qext, qi, qr, ql

    real(dp)    :: xnorm, ynorm, znorm, area, face_speed

    character(len=80) :: q_state

    integer :: nodes_per_face
    integer :: triangle_index
    integer :: triangle_corner
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer :: quad_index
    integer :: quad_corner
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight

    integer :: tri_n, quad_n

    integer :: node1
    integer :: i, j, k
    integer :: row, column

    real(dp)    :: massflow_actual
    real(dp)    :: momentum_actual
    real(dp)    :: pressure_area_actual
    real(dp)    :: area_actual

    logical :: fill_res, fill_a

    continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(rlam) ) then
      if ( .not.present(coltag) .or. .not.present(design)  ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_bc_element_based_ib, ',&
            'but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(crow) ) then
      fill_a = .true.
    endif

    select case ( grid%bc(ib)%ibc )
    case ( massflow_out )
      call update_massflow_out_system(grid,soln,ib,sink)
      momentum_actual      = sink%momentum_actual
      pressure_area_actual = sink%pressure_area_actual
      massflow_actual      = sink%mass_flow_actual
      area_actual          = sink%flow_area
    case ( massflow_in )
      call update_massflow_in_system(grid,soln,ib,source)
      area_actual      = source%flow_area
    end select

!-------------------------Triangular Faces--------------------------------------

    loop_tris : do triangle_index = 1,grid%bc(ib)%nbfacet

      nodes_per_face = 3
      corner_tris_loop : do triangle_corner = 1, nodes_per_face

        call element_based_metrics ( grid%bc, grid%elem                        &
           , grid%x, grid%y, grid%z                                            &
           , grid%dxdt, grid%dydt, grid%dzdt                                   &
           , ib, triangle_index, triangle_corner                               &
           , nodes_per_face, triangle_node, triangle_weight                    &
           , xnorm, ynorm, znorm, area, face_speed )

        call element_based_interp ( grid, soln                                 &
           , ib, triangle_index, triangle_corner                               &
           , nodes_per_face, triangle_node, triangle_weight                    &
           , qi, qext )

! Calcualte d Q_R / d Q_L in primitive
        q_state='primitive'
        call bc_fetch_jacobian  ( ib, grid%bc(ib)%ibc                        &
                                , xnorm, ynorm, znorm, face_speed            &
                                , qi, qext, qr, dqr_dqi, q_state             &
                                , massflow_actual                            &
                                , momentum_actual, pressure_area_actual      &
                                , area_actual, soln%ndim, soln%eqn_set )
        ql = qi

        if ( grid%bc(ib)%ibc /= viscous_solid .and. &
             grid%bc(ib)%ibc /= tangency ) then

! Get the left and right sides to dF/dQ
        df_dq  = dfroe(xnorm, ynorm, znorm, area, face_speed, &
          in_conserved_variables(ql), in_conserved_variables(qr) )

        call dconserved_dprimitive( ql, dqdQ )
        df_dql = matmul( df_dq(:,:,1), dQdq )

        call dconserved_dprimitive( qr, dqdQ )
        df_dqr = matmul( df_dq(:,:,2), dQdq )

        dfp = matmul( df_dqr, dqr_dqi )
! Form dF/dQ|_L = dF/dQ_L + dF/dQ_R dQ_R / dQ_L
        dfp = df_dql + dfp

        elseif ( grid%bc(ib)%ibc == viscous_solid ) then

          !...dFdQL
          dfp_dq = df_unsplit( xnorm, ynorm, znorm, area, face_speed, &
            in_conserved_variables(ql), ubar_null = .false. )

          call dconserved_dprimitive( ql, dQdq )
          dfp = matmul( dfp_dq, dQdq )  !dFdqL

        elseif ( grid%bc(ib)%ibc == tangency ) then

          !...dFdQL
          dfp_dq = df_unsplit( xnorm, ynorm, znorm, area, face_speed, &
            in_conserved_variables(ql), ubar_null = .true. )

          call dconserved_dprimitive( ql, dQdq )
          dfp = matmul( dfp_dq, dQdq )  !dFdqL

        endif

        each_tri_n : do tri_n = 1, nodes_per_face
          local_tri_node : if(triangle_node(tri_n) <= grid%nnodes0) then

            call dprimitive_dconserved(                                     &
              in_primitive_variables(soln%q_dof(:,triangle_node(tri_n))),   &
              dQdq)
            df_conserved = matmul( dfp, dQdq )

            if ( fill_a ) then
              row = triangle_node(1)
              column = triangle_node(tri_n)
! get transposed matrix entry
              k = crow_entry( column, row, crow%iau, crow%ia, crow%ja )

              do i = 1, 5
                do j = 1, 5
                  sadj%aa(i,j,k) = sadj%aa(i,j,k)                              &
                    + triangle_weight(tri_n) * df_conserved(i,j)
                end do
              end do
            end if

            if ( fill_res ) then
              do i = 1, 5
                do j = 1, 5
                  do k = 1, design%nfunctions
                    sadj%res(i,triangle_node(tri_n),k) =            &
                      sadj%res(i,triangle_node(tri_n),k) +          &
                      triangle_weight(tri_n) * df_conserved(j,i) *  &
                      coltag(j,triangle_node(1)) * rlam(j,triangle_node(1),k)
                  end do
                end do
              end do
            end if

          end if local_tri_node
        end do each_tri_n

      end do corner_tris_loop
    end do loop_tris

!-------------------------Quadrilateral Faces-----------------------------------

      loop_quads : do quad_index = 1,grid%bc(ib)%nbfaceq

        nodes_per_face = 4
        corner_quad_loop : do quad_corner = 1, nodes_per_face

        skip_other_twod_plane : if (twod) then
          node1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(quad_index,quad_corner))
          if ( abs(grid%y(node1)-yplane_2d) >= y_coplanar_tol ) &
            cycle corner_quad_loop
        end if skip_other_twod_plane

        call element_based_metrics ( grid%bc, grid%elem                        &
           , grid%x, grid%y, grid%z                                            &
           , grid%dxdt, grid%dydt, grid%dzdt                                   &
           , ib, quad_index, quad_corner                                       &
           , nodes_per_face, quad_node, quad_weight                            &
           , xnorm, ynorm, znorm, area, face_speed )

        call element_based_interp ( grid, soln                                 &
           , ib, triangle_index, triangle_corner                               &
           , nodes_per_face, quad_node, quad_weight                            &
           , qi, qext )

! Calcualte d Q_R / d Q_L in primitive
        q_state='primitive'
        call bc_fetch_jacobian  ( ib, grid%bc(ib)%ibc                        &
                                , xnorm, ynorm, znorm, face_speed            &
                                , qi, qext, qr, dqr_dqi, q_state             &
                                , massflow_actual                            &
                                , momentum_actual, pressure_area_actual      &
                                , area_actual, soln%ndim, soln%eqn_set )
        ql = qi

        if ( grid%bc(ib)%ibc /= viscous_solid .and. &
             grid%bc(ib)%ibc /= tangency ) then

! Get the left and right sides to dF/dQ
        df_dq  = dfroe(xnorm, ynorm, znorm, area, face_speed, &
          in_conserved_variables(ql), in_conserved_variables(qr) )

        call dconserved_dprimitive( ql, dqdQ )
        df_dql = matmul( df_dq(:,:,1), dQdq )

        call dconserved_dprimitive( qr, dqdQ )
        df_dqr = matmul( df_dq(:,:,2), dQdq )

        dfp = matmul( df_dqr, dqr_dqi )
! Form dF/dQ|_L = dF/dQ_L + dF/dQ_R dQ_R / dQ_L
        dfp = df_dql + dfp

        elseif ( grid%bc(ib)%ibc == viscous_solid ) then

          !...dFdQL
          dfp_dq = df_unsplit( xnorm, ynorm, znorm, area, face_speed, &
            in_conserved_variables(ql), ubar_null = .false. )

          call dconserved_dprimitive( ql, dQdq )
          dfp = matmul( dfp_dq, dQdq )  !dFdqL

        elseif ( grid%bc(ib)%ibc == tangency ) then

          !...dFdQL
          dfp_dq = df_unsplit( xnorm, ynorm, znorm, area, face_speed, &
            in_conserved_variables(ql), ubar_null = .true. )

          call dconserved_dprimitive( ql, dQdq )
          dfp = matmul( dfp_dq, dQdq )  !dFdqL

        endif

        each_quad_n : do quad_n = 1, nodes_per_face
          local_quad_node : if(quad_node(quad_n) <= grid%nnodes0) then

            call dprimitive_dconserved(                                       &
              in_primitive_variables(soln%q_dof(:,quad_node(quad_n))),        &
              dQdq)
            df_conserved = matmul( dfp, dQdq )

            if ( fill_a ) then
              row = quad_node(1)
              column = quad_node(quad_n)
! get transposed matrix entry
              k = crow_entry( column, row, crow%iau, crow%ia, crow%ja )
              do i = 1, 5
                do j = 1, 5
                  sadj%aa(i,j,k) = sadj%aa(i,j,k)                              &
                    + quad_weight(quad_n) * df_conserved(i,j)
                end do
              end do
            end if

            if ( fill_res ) then
              do i = 1, 5
                do j = 1, 5
                  do k = 1, design%nfunctions
                    sadj%res(i,quad_node(quad_n),k) =             &
                      sadj%res(i,quad_node(quad_n),k) +           &
                      quad_weight(quad_n) * df_conserved(j,i) *   &
                      coltag(j,quad_node(1)) * rlam(j,quad_node(1),k)
                  end do
                end do
              end do
            end if

          end if local_quad_node
        end do each_quad_n

      end do corner_quad_loop
    end do loop_quads

  end subroutine atlam_bc_element_based_ib

  include 'in_primitive_variables.f90'
  include 'in_conserved_variables.f90'
  include 'crow_entry.f90'
  include 'dfroe.f90'
  include 'df_unsplit.f90'

end module residual_bc_element_based

! Routines needed for articulated rotors

module rotor_motion

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,          only : dp
  use moving_body_types, only : moving_body_type
  use csd_coupling,      only : have_360_data, blade_360, blade_start
  use nml_rotor_data,    only : comprehensive_rotor_coupling
#ifndef HAVE_RCAS_SDX
  use csd_coupling,      only : sdxget, sdxput
#endif

  implicit none

  private

  public :: extract_comprehensive_loads, write_comprehensive_loads,            &
            hub_motion, update_rotor_motion, set_up_rotor_motion,              &
            update_elastic_blade_surface, get_blade_slice_transform,           &
            get_pop_transform, rotor_performance,                              &
            output_comprehensive_loads, ibc

  logical :: output_comprehensive_loads = .false. ! don't output loads; later
                                                  ! set true if coupling

  type(moving_body_type), dimension(:), allocatable :: hub_motion

  integer :: performance_unit   ! unit number for performance data

  logical :: ibc = .false.      ! CLO to allow individual blade control (motion)

  logical, dimension(:), allocatable :: individual_blade_motion

contains

!============================= SET_UP_ROTOR_MOTION ===========================80
!
! Initial set up for rotor motion data
!
!=============================================================================80

  subroutine set_up_rotor_motion(eqn_set, simulation_time, flow_dir)

    use rotors,               only : nrotor, rotor, vinf_ratio
    use info_depr,            only : xmach
    use nml_nonlinear_solves, only : dt
    use grid_motion_helpers,  only : initialize_all_moving_bodies
    use csd_coupling,         only : interpolated_qc_surface
    use lmpi,                 only : lmpi_master, lmpi_conditional_stop,       &
                                     lmpi_synchronize
    use solution_types,       only : compressible, incompressible
    use moving_body_types,    only : moving_body
    use nml_grid_motion,      only : n_moving_bodies
    use system_extensions,    only : se_shell
    use lmpi,                 only : lmpi_bcast
    use solution_globals,     only : slice_group_beg, slice_group_end
    use nml_slice_data,       only : slice_location
    use csd_coupling,         only : ncsdconfig, ncfdconfig, nmisccfg,         &
                                     csdconfig, cfdconfig, miscconfig,         &
                                     sdx_blade, ntsteptcp
    use nml_rcas_sdx,         only : read_nml_rcas_sdx, rcas_license_file,     &
                                     rcas_input_file, rcas_output_file

    integer,      intent(in) :: eqn_set

    real(dp),     intent(in) :: simulation_time

    character(*), intent(in) :: flow_dir

    integer :: tsum, rn, blade, body

    real(dp) :: radtodeg, ref_vel, pi

    integer        :: nblades, islice, icfd, icsd, group, csdnrotor, ierr,     &
                      iacp, ianp, nspan_sdx, nazimuth_sdx, nazimuth_fun3d

    real(dp)       :: tip_radius

    character(256) :: command, err_message, username

    logical      :: echo_csd_data = .false.

  continue

     if (0 == 1) write(*,*) flow_dir ! unclean code to satisfy clean-code nazi

    if (trim(comprehensive_rotor_coupling) == 'rcas'        .or.               &
        trim(comprehensive_rotor_coupling) == 'rcas_tight') then
#ifndef HAVE_RCAS_SDX
      call lmpi_conditional_stop(1,'Stopping: must have SDX library for RCAS')
#endif
    end if

    radtodeg = 180._dp/acos(-1.0_dp)
    pi       = acos(-1.0_dp)

    select case(eqn_set)
      case(compressible)
        ref_vel = xmach
      case(incompressible)
        ref_vel = 1.0_dp
      case default
        ref_vel = 1.0_dp  ! to supress compiler warning
        call lmpi_conditional_stop(1,'set_up_rotor_motion: only in/comprss pg')
    end select

    rcas_init : if (trim(comprehensive_rotor_coupling) == 'rcas'        .or.   &
                    trim(comprehensive_rotor_coupling) == 'rcas_tight') then

      call read_nml_rcas_sdx(flow_dir)

      ierr = 0
      err_message = ''

!     fire up RCAS if doing in-core data exchange

        if (.not. allocated(sdx_blade))  allocate(sdx_blade(nrotor))

      master_gets_data : if (lmpi_master) then

        if (.not. allocated(csdconfig))  allocate(csdconfig(ncsdconfig))
        if (.not. allocated(cfdconfig))  allocate(cfdconfig(ncfdconfig))
        if (.not. allocated(miscconfig)) allocate(miscconfig(nmisccfg))

        if (0==1) username = username  ! "clean" code nonsense
#ifdef HAVE_RCAS_SDX
        call getenv("USER", username)
        command = '/bin/rm -rf /tmp/sdx-' // trim(adjustl(username)) // '/'
        call se_shell(trim(adjustl(command)))
#endif

        command = '/bin/rm -rf ' // trim(adjustl(rcas_output_file))
        call se_shell(trim(adjustl(command)))

        command = '/bin/csh -c ' //                                            &
        '"source ' // trim(adjustl(rcas_license_file)) // '; ' //              &
        'nohup rcasbatchrun ' //  trim(adjustl(rcas_input_file)) // ' ' //     &
        trim(adjustl(rcas_output_file)) // '&"'
        call se_shell(trim(adjustl(command)))

        write(*,*)
        write(*,'(a)') ' Launching RCAS'

        call sdxget('config.data.from.csd'//char(0),csdconfig,ncsdconfig)
        call sdxget('misc.config.from.csd'//char(0),miscconfig,nmisccfg)

        write(*,'(a)') ' Recieved CSD config data from RCAS'

!       pull out the data from the transfer array so we can use it

        icsd = 0

        icsd = icsd + 1
!       unitflag = csdconfig(icsd)
        if (echo_csd_data)                                                     &
        print *, ' unitflag csdconfig(icsd) =',  csdconfig(icsd), icsd

        icsd = icsd + 1
        nazimuth_sdx = csdconfig(icsd) ! number of azmiuths for lcp exchange
        if (echo_csd_data)                                                     &
        print *, ' nazimuth_sdx csdconfig(icsd) =',  csdconfig(icsd), icsd

        icsd = icsd + 1
        ntsteptcp =  csdconfig(icsd)  ! number of time steps for tight coupling
        if (echo_csd_data)                                                     &
        print *, ' ntsteptcp csdconfig(icsd) =',  csdconfig(icsd), icsd

        icsd = icsd + 1
!       cfdfusealflag =  csdconfig(icsd)
        if (echo_csd_data)                                                     &
        print *, ' cfdfusealflag csdconfig(icsd) =',  csdconfig(icsd), icsd

        icsd = icsd + 1
        csdnrotor = csdconfig(icsd)
        if (echo_csd_data)                                                     &
        print *, ' csdnrotor csdconfig(icsd) =',  csdconfig(icsd), icsd
!       consistency check - number of rotors must be the same
        if (csdnrotor /= nrotor) then
          ierr = 1
          err_message = 'Number of rotors in CFD and CSD do not match !'
        end if

!       consistency check - number of azimuthal steps per rev in RCAS must
!       be the same as in FUN3D

        do rn = 1, nrotor
          nazimuth_fun3d =                                                     &
            int(360._dp/(rotor(rn)%vt_ratio*ref_vel/rotor(rn)%rtip*dt*radtodeg))
          if (nazimuth_fun3d /= nazimuth_sdx) then
            ierr = 1
            err_message = 'Number time steps in CFD and CSD do not match !'
          end if
        end do

        rotor_loop_00 : do rn = 1, nrotor

          icsd = icsd + 1
!         rotdir(rn) = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' rotdir csdconfig(icsd) =',  csdconfig(icsd), icsd
!         consistency check - rotors must spin in the same direction
          if ( (int(csdconfig(icsd)) ==  1 .and. rotor(rn)%dirrot /= 0)   .or. &
               (int(csdconfig(icsd)) == -1 .and. rotor(rn)%dirrot /= 1) ) then
            ierr = 1
            err_message = 'Rotation direction in CFD and CSD does not match !'
          end if

          icsd = icsd + 1
!         rrx(rn)    = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' rrx csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         nbladea(rn)= csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' nbladea csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
          nspan_sdx = csdconfig(icsd)   ! nspan
          if (echo_csd_data)                                                   &
          print *, ' csdnacp csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         rotseq(rn) = csdconfig(icsd)  !rn: rotation sequence
          if (echo_csd_data)                                                   &
          print *, ' rotseq csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         lref(rn)   = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' lref csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         ishaft(rn) = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' icsd csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         iradial(rn) = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' iradial csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         ichord(rn) = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' ichord csdconfig(icsd) =',  csdconfig(icsd), icsd

          icsd = icsd + 1
!         itwist(rn) = csdconfig(icsd)
          if (echo_csd_data)                                                   &
          print *, ' itwist csdconfig(icsd) =',  csdconfig(icsd), icsd

          if (.not. allocated(sdx_blade(rn)%xqc))                              &
                                  allocate(sdx_blade(rn)%xqc(nspan_sdx))
          if (.not. allocated(sdx_blade(rn)%yqc))                              &
                                  allocate(sdx_blade(rn)%yqc(nspan_sdx))
          if (.not. allocated(sdx_blade(rn)%zqc))                              &
                                  allocate(sdx_blade(rn)%zqc(nspan_sdx))
          if (.not. allocated(sdx_blade(rn)%xqc_np))                           &
                                  allocate(sdx_blade(rn)%xqc_np(nspan_sdx+1))
          if (.not. allocated(sdx_blade(rn)%yqc_np))                           &
                                  allocate(sdx_blade(rn)%yqc_np(nspan_sdx+1))
          if (.not. allocated(sdx_blade(rn)%zqc_np))                           &
                                  allocate(sdx_blade(rn)%zqc_np(nspan_sdx+1))
          if (.not. allocated(sdx_blade(rn)%rr))                               &
                                  allocate(sdx_blade(rn)%rr(nspan_sdx))
          if (.not. allocated(sdx_blade(rn)%chord))                            &
                                  allocate(sdx_blade(rn)%chord(nspan_sdx))
          if (.not. allocated(sdx_blade(rn)%twist))                            &
                                  allocate(sdx_blade(rn)%twist(nspan_sdx))

!         r/R from csd
          do iacp = 1, nspan_sdx
            icsd = icsd + 1
            sdx_blade(rn)%rr(iacp) = csdconfig(icsd)
          end do

!         chord from csd
          do iacp = 1, nspan_sdx
            icsd = icsd + 1
            sdx_blade(rn)%chord(iacp) = csdconfig(icsd)
          end do

!         twist in deg from csd
          do iacp = 1, nspan_sdx
            icsd = icsd + 1
            sdx_blade(rn)%twist(iacp) = csdconfig(icsd)
          end do

!         panel-center values of xqc/R, yqc/R, zqc/R from csd
          do iacp = 1, nspan_sdx
            icsd = icsd + 1
            sdx_blade(rn)%xqc(iacp) = csdconfig(icsd)
            icsd = icsd + 1
            sdx_blade(rn)%yqc(iacp) = csdconfig(icsd)
            icsd = icsd + 1
            sdx_blade(rn)%zqc(iacp) = csdconfig(icsd)
          end do

!         panel-end point values of xqc/R, yqc/R, zqc/R from csd
          do ianp = 1, nspan_sdx+1
            icsd = icsd + 1
            sdx_blade(rn)%xqc_np(iacp) = csdconfig(icsd)
            icsd = icsd + 1
            sdx_blade(rn)%yqc_np(iacp) = csdconfig(icsd)
            icsd = icsd + 1
            sdx_blade(rn)%zqc_np(iacp) = csdconfig(icsd)
          end do

!         save nspan and nazimuth in the sdx_blade derived type
          sdx_blade(rn)%nazimuth = nazimuth_sdx
          sdx_blade(rn)%nspan    = nspan_sdx

        end do rotor_loop_00

!       load CFD data into the transfer array so RCAS can use it

        group = 0
        icfd  = 0

        rotor_loop_0 : do rn = 1, nrotor

          nblades = rotor(rn)%nblade
          tip_radius = rotor(rn)%rtip

          blade_loop_0 : do blade = 1,nblades

            group = group + 1
            if (blade > 1) cycle blade_loop_0

!           cfd radial stations (aerodynamic control points)

            icfd = icfd + 1
            cfdconfig(icfd) = slice_group_end(group)-slice_group_beg(group)+1

            slice_loop : do islice=slice_group_beg(group),slice_group_end(group)

              icfd = icfd + 1
              cfdconfig(icfd) = slice_location(islice) / tip_radius

            end do slice_loop

          end do blade_loop_0

        end do rotor_loop_0

        call sdxput('config.data.from.cfd'//char(0),cfdconfig,ncfdconfig)

        write(*,'(a)') ' Sent CFD config data to RCAS'
        write(*,*)

      end if master_gets_data

      call lmpi_conditional_stop(ierr,trim(adjustl(err_message)))

!     make sure everyone knows about the data from RCAS

      do rn = 1, nrotor

        call lmpi_bcast(sdx_blade(rn)%nspan)
        call lmpi_bcast(sdx_blade(rn)%nazimuth)

        nspan_sdx = sdx_blade(rn)%nspan

!       these arrays have not been allocated heretofor on non-master processes
        if (.not. allocated(sdx_blade(rn)%xqc))                                &
                                allocate(sdx_blade(rn)%xqc(nspan_sdx))
        if (.not. allocated(sdx_blade(rn)%yqc))                                &
                                allocate(sdx_blade(rn)%yqc(nspan_sdx))
        if (.not. allocated(sdx_blade(rn)%zqc))                                &
                                allocate(sdx_blade(rn)%zqc(nspan_sdx))
        if (.not. allocated(sdx_blade(rn)%xqc_np))                             &
                                allocate(sdx_blade(rn)%xqc_np(nspan_sdx+1))
        if (.not. allocated(sdx_blade(rn)%yqc_np))                             &
                                allocate(sdx_blade(rn)%yqc_np(nspan_sdx+1))
        if (.not. allocated(sdx_blade(rn)%zqc_np))                             &
                                allocate(sdx_blade(rn)%zqc_np(nspan_sdx+1))
        if (.not. allocated(sdx_blade(rn)%rr))                                 &
                                allocate(sdx_blade(rn)%rr(nspan_sdx))
        if (.not. allocated(sdx_blade(rn)%chord))                              &
                                allocate(sdx_blade(rn)%chord(nspan_sdx))
        if (.not. allocated(sdx_blade(rn)%twist))                              &
                                allocate(sdx_blade(rn)%twist(nspan_sdx))

        call lmpi_bcast(sdx_blade(rn)%xqc)
        call lmpi_bcast(sdx_blade(rn)%yqc)
        call lmpi_bcast(sdx_blade(rn)%zqc)
        call lmpi_bcast(sdx_blade(rn)%xqc_np)
        call lmpi_bcast(sdx_blade(rn)%yqc_np)
        call lmpi_bcast(sdx_blade(rn)%zqc_np)
        call lmpi_bcast(sdx_blade(rn)%rr)
        call lmpi_bcast(sdx_blade(rn)%chord)
        call lmpi_bcast(sdx_blade(rn)%twist)

      end do

    end if rcas_init

    tsum = 0
    do rn = 1, nrotor
     tsum = tsum + rotor(rn)%nblade
    end do

    if (n_moving_bodies /= tsum)                                               &
      write(*,*)'Warning: n_moving_bodies is not equal to number of blades'

    allocate(hub_motion(nrotor))
    call initialize_all_moving_bodies(hub_motion)

    allocate(have_360_data(nrotor))
    allocate(blade_360(nrotor))
    allocate(blade_start(nrotor))
    have_360_data(:) = .false.
    blade_360(:)     = 0
    blade_start(:)   = 0

    body = 0

    rotor_loop : do rn = 1, nrotor

!     compute nondimensional rotational velocity

      rotor(rn)%omega = rotor(rn)%vt_ratio*ref_vel/rotor(rn)%rtip

      if (lmpi_master) then
        write(*,*)
        write(*,'(a,i2)') ' Rotor info, rotor ', rn
        write(*,'(a,i4)')    '  Number of blades                   : ',        &
                             rotor(rn)%nblade
        write(*,'(a,f16.8)') '  Nondimensional rotation rate       : ',        &
                             rotor(rn)%omega
        write(*,'(a,f16.8)') '  Azimuth change (deg) per time step : ',        &
                             rotor(rn)%omega*dt*radtodeg
        if ( eqn_set == compressible )                                         &
        write(*,'(a,f16.8)') '  Tip Mach number (hover)            : ',        &
                             rotor(rn)%omega*rotor(rn)%rtip
        write(*,'(a,f16.8)') '  Advance ratio                      : ',        &
                             vinf_ratio/rotor(rn)%vt_ratio
        write(*,'(a,f16.8)') '  Tip radius                         : ',        &
                             rotor(rn)%rtip
      end if


      blade_loop : do blade = 1, rotor(rn)%nblade

        body = body + 1

!       ensure force/moment reference data are consistent with rotor input data
!       (override data in fun3d main input file or moving body input file)

        moving_body(body)%sref = pi*rotor(rn)%rtip**2
        moving_body(body)%bref = rotor(rn)%rtip
        moving_body(body)%cref = rotor(rn)%rtip
        moving_body(body)%xmc  = rotor(rn)%x0
        moving_body(body)%ymc  = rotor(rn)%y0
        moving_body(body)%zmc  = rotor(rn)%z0

        moving_body(body)%move_mc = 0

!       the following should not be set in the moving_body.input file;
!       zero them out just to be safe

        moving_body(body)%translate = 0
        moving_body(body)%rotate    = 0

      end do blade_loop

      if (lmpi_master) then
        write(*,'(a,f16.8)') '  Force/Moment reference area        : ',        &
                             pi*rotor(rn)%rtip**2
        write(*,'(a,f16.8)') '  Force/Moment reference length      : ',        &
                             rotor(rn)%rtip
        write(*,'(a,f16.8)') '  Moment reference x-center          : ',        &
                             rotor(rn)%x0
        write(*,'(a,f16.8)') '  Moment reference y-center          : ',        &
                             rotor(rn)%y0
        write(*,'(a,f16.8)') '  Moment reference z-center          : ',        &
                             rotor(rn)%z0
        write(*,'(a)')       '  Moment reference center motion     : .false.'
        write(*,'(a)')       '  Note: force/moment reference data above'
        write(*,'(a)')       '        supercedes any other input values'
      end if

!     set up the file(s) for rotor performance history

      if (lmpi_master) then
        call performance_file_setup(rn, simulation_time)
      end if

    end do rotor_loop

    setup_for_coupling : if (trim(comprehensive_rotor_coupling) /= 'none') then

!     enable output tailored to comprehensive code

      output_comprehensive_loads = .true.

!     allow for independent elastic blade motion (aka ibc) for each blade
!     (usually in loose coupling, all blades have the same elastic motion)

      allocate(individual_blade_motion(nrotor))

      individual_blade_motion(:) = .false.

!     for now, if ibc is utilized, ALL rotors must use ibc

      if (ibc) then
        individual_blade_motion(:) = .true.
      end if

!     get current coupling cycle motion data

      call get_comprehensive_motion()

      if (trim(comprehensive_rotor_coupling) == 'camrad' .or.                 &
          trim(comprehensive_rotor_coupling) == 'rcas' ) then

!       output interpolated quarter-chord data for checking against computed
!       quarter-chord position and to check blade tracking (loose coupling)

        do rn = 1, nrotor
          call interpolated_qc_surface(rn,ibc)
        end do

      end if

    end if setup_for_coupling

    if (trim(comprehensive_rotor_coupling) == 'rcas'        .or.               &
        trim(comprehensive_rotor_coupling) == 'rcas_tight') then
      call lmpi_synchronize()
    end if

  end subroutine set_up_rotor_motion


!=========================== UPDATE_ROTOR_MOTION =============================80
!
! Sets rotor motion transforms. Assumes that the moving bodies are ordered
! first by rotor then by blade; if this isn't the case then the psi_t0 values
! will be incorrect and the wrong angles will be used. This order is
! explicitly set in the moving_body.input file. For example, if rotor 1 has
! 4 blades and rotor 2 has 3 blades, then bodies 1-4 in the moving_body.input
! correspond to blades 1-4 of rotor 1 and bodies 5-7 correspond to blades 1-3
! of rotor 2.
!
! If rigid blade motion, transform contains hub rotation, collective, and
! cyclic components; if elastic blade motion (coupling to comprehensive code),
! transform contains the hub rotation component, plus approximate flap, and
! lead/lag components derived from a linear fit of the elastic quarter-chord
! motion provided by the comprehensive code
!
! Assumes the only user-set moving bodies are the rotor blades
!
!=============================================================================80

  subroutine update_rotor_motion(simulation_time)

    use rotors,              only : nrotor, rotor
    use grid_motion_helpers, only : get_inverse_transform, get_body_velocity,  &
                                    transform_matrix_to_euler_angle
    use csd_coupling,        only : extract_rigid_from_elastic,                &
                                    interpolated_qc_surface,                   &
                                    exchange_data_with_rcas
    use fsi_coupling,        only : notsosimple_rigid_rotation
    use fsi_coupling_tight,  only : extract_rigid_from_elastic_fsi_tight
    use info_depr,           only : ntt
    use lmpi,                only : lmpi_synchronize

    use moving_body_types,   only : moving_body

    real(dp),      intent(in)   :: simulation_time

    integer                     :: rn, blade, body

    real(dp), dimension(4,4)    :: matrixh, matrixh_slice, matrixh_static

    real(dp)                    :: roll, pitch, yaw, radtodeg, psi_shaft, psi_t0

  continue

    radtodeg = 180._dp/acos(-1.0_dp)

!   Get new blade motion data if coupling with RCAS (note that we can
!   skip the read on the very first step of the current run since the motion
!   data has already been transfered during in setup_rotor_motion

    if (trim(comprehensive_rotor_coupling) == 'rcas'        .or.               &
        trim(comprehensive_rotor_coupling) == 'rcas_tight') then

      if (exchange_data_with_rcas) then

        if (ntt > 1) then

          call get_comprehensive_motion()

          exchange_data_with_rcas = .false.

          if (trim(comprehensive_rotor_coupling) == 'rcas' ) then

!           output interpolated quarter-chord data for checking against computed
!           quarter-chord position and to check blade tracking (loose coupling)

            do rn = 1, nrotor
              call interpolated_qc_surface(rn,ibc)
            end do

          end if

        end if

      end if

    end if

    if (trim(comprehensive_rotor_coupling) == 'rcas'        .or.               &
        trim(comprehensive_rotor_coupling) == 'rcas_tight') then
      call lmpi_synchronize()
    end if

!   Blade motion

    body = 0

    rotor_loop : do rn = 1, nrotor

      rotor(rn)%psi1 = rotor(rn)%omega*simulation_time

      psi_shaft = rotor(rn)%psi1

      blade_loop : do blade = 1, rotor(rn)%nblade

        body = body + 1

        select case(trim(comprehensive_rotor_coupling))

          case('camrad', 'rcas', 'rcas_tight')

            call extract_rigid_from_elastic(blade, rn, psi_shaft)

            call get_elastic_blade_transform(blade, rn, matrixh, psi_shaft,    &
                                             matrixh_static)

          case('fsi')

            call notsosimple_rigid_rotation(blade, rn, matrixh, psi_shaft)

          case('fsi_tight')

            call extract_rigid_from_elastic_fsi_tight(blade, rotor(rn)%dirrot)

            call get_elastic_blade_transform(blade, rn, matrixh, psi_shaft,    &
                                             matrixh_static)

          case default

            call get_rigid_blade_transform(blade, rn, matrixh, psi_shaft,      &
                                           matrixh_static)

        end select

!       set some things in the moving_body derived type

        moving_body(body)%transform_matrix(:,:) = matrixh(:,:)

        call get_inverse_transform(moving_body(body)%transform_matrix,         &
                                   moving_body(body)%inv_transform)

        call get_blade_slice_transform(blade, rn, psi_shaft, matrixh_slice)

        moving_body(body)%slice_transform(:,:) = matrixh_slice(:,:)

        call get_inverse_transform(moving_body(body)%slice_transform,         &
                                   moving_body(body)%inv_slice_transform)

        psi_t0 = 0.0_dp

        call get_blade_slice_transform(blade, rn, psi_t0, matrixh_slice)

        moving_body(body)%slice_transform_0(:,:) = matrixh_slice(:,:)

        call get_inverse_transform(moving_body(body)%slice_transform_0,       &
                                   moving_body(body)%inv_slice_transform_0)

        moving_body(body)%static_transform(:,:) = matrixh_static(:,:)

        call get_inverse_transform(moving_body(body)%static_transform,        &
                                   moving_body(body)%inv_static_transform)

        moving_body(body)%xcg = rotor(rn)%x0
        moving_body(body)%ycg = rotor(rn)%y0
        moving_body(body)%zcg = rotor(rn)%z0

        call transform_matrix_to_euler_angle(                                  &
                          moving_body(body)%transform_matrix, pitch, roll, yaw)

        moving_body(body)%euler_angles(1) = yaw*radtodeg
        moving_body(body)%euler_angles(2) = pitch*radtodeg
        moving_body(body)%euler_angles(3) = roll*radtodeg

        call get_body_velocity(moving_body(body)%transform_matrix,             &
                               moving_body(body)%transform_matrixatn,          &
                               moving_body(body)%transform_matrixatn1,         &
                               moving_body(body)%transform_matrixatn2,         &
                               moving_body(body)%transform_matrixatn3,         &
                               moving_body(body)%inv_transform,                &
                               moving_body(body)%body_lin_vel(1),              &
                               moving_body(body)%body_lin_vel(2),              &
                               moving_body(body)%body_lin_vel(3),              &
                               moving_body(body)%body_ang_vel(1),              &
                               moving_body(body)%body_ang_vel(2),              &
                               moving_body(body)%body_ang_vel(3),              &
                               rotor(rn)%x0, rotor(rn)%y0, rotor(rn)%z0)

      end do blade_loop

    end do rotor_loop

!   Hub motion

    hub_loop : do rn = 1, nrotor

      psi_shaft = rotor(rn)%psi1

      call get_hub_transform(rn, matrixh, psi_shaft)

      hub_motion(rn)%transform_matrix(:,:) = matrixh(:,:)

      call get_inverse_transform(hub_motion(rn)%transform_matrix,              &
                                 hub_motion(rn)%inv_transform)

      hub_motion(rn)%xcg = rotor(rn)%x0
      hub_motion(rn)%ycg = rotor(rn)%y0
      hub_motion(rn)%zcg = rotor(rn)%z0

      call transform_matrix_to_euler_angle(                                    &
                             hub_motion(rn)%transform_matrix, pitch, roll, yaw)

      hub_motion(rn)%euler_angles(1) = yaw*radtodeg
      hub_motion(rn)%euler_angles(2) = pitch*radtodeg
      hub_motion(rn)%euler_angles(3) = roll*radtodeg

      call get_body_velocity(hub_motion(rn)%transform_matrix,                  &
                             hub_motion(rn)%transform_matrixatn,               &
                             hub_motion(rn)%transform_matrixatn1,              &
                             hub_motion(rn)%transform_matrixatn2,              &
                             hub_motion(rn)%transform_matrixatn3,              &
                             hub_motion(rn)%inv_transform,                     &
                             hub_motion(rn)%body_lin_vel(1),                   &
                             hub_motion(rn)%body_lin_vel(2),                   &
                             hub_motion(rn)%body_lin_vel(3),                   &
                             hub_motion(rn)%body_ang_vel(1),                   &
                             hub_motion(rn)%body_ang_vel(2),                   &
                             hub_motion(rn)%body_ang_vel(3),                   &
                             rotor(rn)%x0, rotor(rn)%y0, rotor(rn)%z0)

    end do hub_loop

  end subroutine update_rotor_motion


!=========================== GET_RIGID_BLADE_TRANSFORM =======================80
!
!  Takes the rotor settings in rotor.input and computes a transformation matrix
!  (matrixh) for a rigid rotor blade. Matrixh will take the blade from its
!  position in the grid at t=0 to the current position. This transform is used
!  to move both the blade and the surrounding volume grid.
!
!=============================================================================80

  subroutine get_rigid_blade_transform(blade, rn, matrixh, psi_shaft,          &
                                       matrixh_static)

    use rotors,              only : rotor
    use linear_algebra,      only : identity_matrix
    use grid_motion_helpers, only : matrix_mult,                               &
                                    translate_to_center, get_inverse_transform

    real(dp), dimension(4,4),    intent(out) :: matrixh
    real(dp), dimension(4,4),    intent(out) :: matrixh_static
    real(dp),                    intent(in)  :: psi_shaft
    integer,                     intent(in)  :: blade, rn

    integer                   :: i

    real(dp)                  :: cosalp, sinalp, beta, delta, theta, sn
    real(dp)                  :: ebeta, edelta, etheta, psi_t0, psi, pi
    real(dp)                  :: beta_t0, delta_t0, theta_t0, alpha0

    real(dp), dimension(3)    :: axisbeta,  cenbeta,  axisdelta, cendelta
    real(dp), dimension(3)    :: axistheta, centheta, axisrotor, cenrotor
    real(dp), dimension(3)    :: axistilt,  centilt, neg_cenrotor

  continue

    pi = acos(-1.0_dp)

!   rotor axis, for clockwise axis is -Z, for counter clockwise axis is Z

    sn =  1.0_dp                            ! counterclockwise rotation
    if (rotor(rn)%dirrot == 1) sn = -1.0_dp ! clockwise rotation

!   alpha0 is the tip angle of the TPP with respect to reference plane; assumes
!   that axis tilted in the xz-plane, taken from the phi2 input option; a
!   negative angle in the rotor.input file implies a tilt forward so negate here

    alpha0 = rotor(rn)%alpha0
    cosalp = cos(-1.0_dp*alpha0)
    sinalp = sin(-1.0_dp*alpha0)

!   shaft tilt axis - fore/aft tilt only - no side-to-side tilt

    axistilt(1) = 0.0_dp
    axistilt(2) = 1.0_dp
    axistilt(3) = 0.0_dp
    centilt(1)  = rotor(rn)%x0
    centilt(2)  = rotor(rn)%y0
    centilt(3)  = rotor(rn)%z0

!   rotor hub axis

    axisrotor(1) = -sinalp*sn
    axisrotor(2) = 0.0_dp
    axisrotor(3) = cosalp*sn
    cenrotor(1)  = rotor(rn)%x0
    cenrotor(2)  = rotor(rn)%y0
    cenrotor(3)  = rotor(rn)%z0

    neg_cenrotor(:) = -cenrotor(:)

!   hinge offsets, assumes non-dimensionalized by rotor radius

    ebeta  = rotor(rn)%rfh
    edelta = rotor(rn)%rlh
    etheta = rotor(rn)%rph

!   pitch about xrotor

    axistheta(1) = cosalp*sn
    axistheta(2) = 0.0_dp
    axistheta(3) = sinalp*sn
    centheta(1)  = rotor(rn)%x0 + etheta*rotor(rn)%rtip*cosalp
    centheta(2)  = rotor(rn)%y0
    centheta(3)  = rotor(rn)%z0 + etheta*rotor(rn)%rtip*sinalp

!   lead/lag about zrotor

    axisdelta(1) = -sinalp
    axisdelta(2) = 0.0_dp
    axisdelta(3) = cosalp
    cendelta(1)  = rotor(rn)%x0 + edelta*rotor(rn)%rtip*cosalp
    cendelta(2)  = rotor(rn)%y0
    cendelta(3)  = rotor(rn)%z0 + edelta*rotor(rn)%rtip*sinalp

!   flap about Y

    axisbeta(1) = 0.0_dp
    axisbeta(2) = -1.0_dp
    axisbeta(3) = 0.0_dp
    cenbeta(1)  = rotor(rn)%x0 + ebeta*rotor(rn)%rtip*cosalp
    cenbeta(2)  = rotor(rn)%y0
    cenbeta(3)  = rotor(rn)%z0 + ebeta*rotor(rn)%rtip*sinalp

!   get t=0 contol angles; psi_t0 assumes blade 1 is at psi 0 initially

    psi_t0 = real(blade-1,dp)*(pi*2.0_dp) / real(rotor(rn)%nblade,dp)

!   blades in composite mesh are positioned in a neutral setting, and
!   "popped" to the flight-condition specific settings right before the
!   flow solution commences

    beta_t0  = 0._dp
    theta_t0 = 0._dp
    delta_t0 = 0._dp

!   get current contol angles

    psi = psi_shaft + psi_t0

    beta = rotor(rn)%beta(1)
    do i = 2,rotor(rn)%nbeta*2,2
      beta = beta                                                              &
           + rotor(rn)%beta(i)  *sin((real(i/2,dp))*abs(psi))                  &
           + rotor(rn)%beta(i+1)*cos((real(i/2,dp))*abs(psi))
    end do

    theta = rotor(rn)%theta0
    theta = theta                                                              &
          + rotor(rn)%theta1c*cos(abs(psi))                                    &
          + rotor(rn)%theta1s*sin(abs(psi))

    delta = rotor(rn)%delta(1)
    do i = 2,rotor(rn)%ndelta*2,2
      delta = delta                                                            &
            + rotor(rn)%delta(i)  *sin((real(i/2,dp))*abs(psi))                &
            + rotor(rn)%delta(i+1)*cos((real(i/2,dp))*abs(psi))
    end do

!   create homogeneous transformation matrix
!   [matrixh]=[psi][beta][delta][theta][i]

    matrixh = identity_matrix(4)

!   starting with the blade as it is positioned in the input grid at t=0,
!   get the transform needed to put it back to the reference blade position,
!   i.e.,  aligned with x-axis - this negates any transforms applied in
!   the dci_gen utility.

    call matrix_mult(axisrotor,   cenrotor, -psi_t0,   matrixh)
    call matrix_mult(axisbeta ,   cenbeta,  -beta_t0,  matrixh)
    call matrix_mult(axisdelta,   cendelta, -delta_t0, matrixh)
    call matrix_mult(axistheta,   centheta, -theta_t0, matrixh)
    call matrix_mult(axistilt,    centilt,  -alpha0,   matrixh)
    call translate_to_center(neg_cenrotor,             matrixh)

!   the inverse of the matrix above is our static transform matrix, which
!   may be needed for SUGGAR

    call get_inverse_transform(matrixh, matrixh_static)

!   now get the transform from the reference blade position to the new position
!   note that psi contains both the current hub rotation angle and the t0 offset

!   note: the last 2 transforms above and the first 2 transforms below could be
!   eliminated without any change, but they are cheap and having them allows
!   exact line up between the operations in this routine and in dci_gen, which
!   should aid understanding/debugging; the final matrixh transform will take
!   the blade from its grid position at t=0 to the current position

    call translate_to_center(   cenrotor,         matrixh)
    call matrix_mult(axistilt,  centilt,  alpha0, matrixh)
    call matrix_mult(axistheta, centheta, theta,  matrixh)
    call matrix_mult(axisdelta, cendelta, delta,  matrixh)
    call matrix_mult(axisbeta , cenbeta,  beta,   matrixh)
    call matrix_mult(axisrotor, cenrotor, psi,    matrixh)

  end subroutine get_rigid_blade_transform


!========================== GET_ELASTIC_BLADE_TRANSFORM ======================80
!
!  Takes the rotor settings in rotor.input, plus "mean" pitch, flap, and
!  lead/lag angles extracted from the elastic blade motion data and computes
!  a transformation matrix (matrixh) for the "mean" elastic rotor blade.
!  Matrixh will take the "mean" blade from its position in the grid at t=0
!  to its current position. This transform is used to move the volume grid
!  surrounding the blade in order to keep the volume grid approximately
!  centered on the elastic blade; this transform is not used to move the
!  blade itself.
!
!=============================================================================80

  subroutine get_elastic_blade_transform(blade, rn, matrixh, psi_shaft,        &
                                         matrixh_static)

    use rotors,              only : rotor
    use csd_coupling,        only : elastic_rotor
    use fsi_coupling,        only : fsi_info
    use linear_algebra,      only : identity_matrix
    use grid_motion_helpers, only : matrix_mult,                               &
                                    translate_to_center, get_inverse_transform

    real(dp), dimension(4,4),    intent(out) :: matrixh
    real(dp), dimension(4,4),    intent(out) :: matrixh_static
    real(dp),                    intent(in)  :: psi_shaft
    integer,                     intent(in)  :: blade, rn

    real(dp)                  :: cosalp, sinalp, beta, delta, theta, sn
    real(dp)                  :: ebeta, edelta, etheta, psi_t0, psi, pi
    real(dp)                  :: beta_t0, delta_t0, theta_t0, alpha0

    real(dp), dimension(3)    :: axisbeta,  cenbeta,  axisdelta, cendelta
    real(dp), dimension(3)    :: axistheta, centheta, axisrotor, cenrotor
    real(dp), dimension(3)    :: axistilt,  centilt, neg_cenrotor

  continue

    pi = acos(-1.0_dp)

!   rotor axis, for clockwise axis is -Z, for counter clockwise axis is Z

    sn =  1.0_dp                            ! counterclockwise rotation
    if (rotor(rn)%dirrot == 1) sn = -1.0_dp ! clockwise rotation

!   alpha0 is the tip angle of the TPP with respect to reference plane; assumes
!   that axis tilted in the xz-plane, taken from the phi2 input option; a
!   negative angle in the rotor.input file implies a tilt forward so negate here

    alpha0 = rotor(rn)%alpha0
    cosalp = cos(-1.0_dp*alpha0)
    sinalp = sin(-1.0_dp*alpha0)

!   shaft tilt axis - fore/aft tilt only - no side-to-side tilt

    axistilt(1) = 0.0_dp
    axistilt(2) = 1.0_dp
    axistilt(3) = 0.0_dp
    centilt(1)  = rotor(rn)%x0
    centilt(2)  = rotor(rn)%y0
    centilt(3)  = rotor(rn)%z0

!   rotor hub axis

    axisrotor(1) = -sinalp*sn
    axisrotor(2) = 0.0_dp
    axisrotor(3) = cosalp*sn
    cenrotor(1)  = rotor(rn)%x0
    cenrotor(2)  = rotor(rn)%y0
    cenrotor(3)  = rotor(rn)%z0

    neg_cenrotor(:) = -cenrotor(:)

!   hinge offsets, assumes non-dimensionalized by rotor radius

    ebeta  = rotor(rn)%rfh
    edelta = rotor(rn)%rlh
    etheta = rotor(rn)%rph

!   pitch about xrotor

    axistheta(1) = cosalp*sn
    axistheta(2) = 0.0_dp
    axistheta(3) = sinalp*sn
    centheta(1)  = rotor(rn)%x0 + etheta*rotor(rn)%rtip*cosalp
    centheta(2)  = rotor(rn)%y0
    centheta(3)  = rotor(rn)%z0 + etheta*rotor(rn)%rtip*sinalp

!   lead/lag about zrotor

    axisdelta(1) = -sinalp
    axisdelta(2) = 0.0_dp
    axisdelta(3) = cosalp
    cendelta(1)  = rotor(rn)%x0 + edelta*rotor(rn)%rtip*cosalp
    cendelta(2)  = rotor(rn)%y0
    cendelta(3)  = rotor(rn)%z0 + edelta*rotor(rn)%rtip*sinalp

!   flap about Y

    axisbeta(1) = 0.0_dp
    axisbeta(2) = -1.0_dp
    axisbeta(3) = 0.0_dp
    cenbeta(1)  = rotor(rn)%x0 + ebeta*rotor(rn)%rtip*cosalp
    cenbeta(2)  = rotor(rn)%y0
    cenbeta(3)  = rotor(rn)%z0 + ebeta*rotor(rn)%rtip*sinalp

!   get t=0 contol angles; psi_t0 assumes blade 1 is at psi 0 initially

    psi_t0 = real(blade-1,dp)*(pi*2.0_dp) / real(rotor(rn)%nblade,dp)

!   blades in composite mesh are positioned in a neutral setting, and
!   "popped" to the flight-condition specific settings right before the
!   flow solution commences

    beta_t0  = 0._dp
    theta_t0 = 0._dp
    delta_t0 = 0._dp

!   get current contol angles

    psi   = psi_shaft + psi_t0

    select case (trim(comprehensive_rotor_coupling))
      case ('camrad', 'rcas', 'rcas_tight')
        beta  = elastic_rotor(rn)%blade_motion(blade)%beta
        theta = elastic_rotor(rn)%blade_motion(blade)%theta
        delta = elastic_rotor(rn)%blade_motion(blade)%delta
      case ('fsi_tight')
        beta  = fsi_info%new_ang_tight(2)
        theta = fsi_info%new_ang_tight(3)
        delta = fsi_info%new_ang_tight(1)
    end select

!   create homogeneous transformation matrix
!   [matrixh]=[psi][beta][delta][theta][i]

    matrixh = identity_matrix(4)

!   starting with the blade as it is positioned in the input grid at t=0,
!   get the transform needed to put it back to the reference blade position,
!   i.e.,  aligned with x-axis - this negates any transforms applied in
!   the dci_gen utility.

    call matrix_mult(axisrotor,   cenrotor, -psi_t0,   matrixh)
    call matrix_mult(axisbeta ,   cenbeta,  -beta_t0,  matrixh)
    call matrix_mult(axisdelta,   cendelta, -delta_t0, matrixh)
    call matrix_mult(axistheta,   centheta, -theta_t0, matrixh)
    call matrix_mult(axistilt,    centilt,  -alpha0,   matrixh)
    call translate_to_center(neg_cenrotor,             matrixh)

!   the inverse of the matrix above is our static transform matrix, which
!   may be needed for SUGGAR

    call get_inverse_transform(matrixh, matrixh_static)

!   now get the transform from the reference blade position to the new position
!   note that psi contains both the current hub rotation angle and the t0 offset

!   note: the last 2 transforms above and the first 2 transforms below could be
!   eliminated without any change, but they are cheap and having them allows
!   exact line up between the operations in this routine and in dci_gen, which
!   should aid understanding/debugging; the final matrixh transform will take
!   the (mean) blade from its grid position at t=0 to the current position

    call translate_to_center(   cenrotor,         matrixh)
    call matrix_mult(axistilt,  centilt,  alpha0, matrixh)
    call matrix_mult(axistheta, centheta, theta,  matrixh)
    call matrix_mult(axisdelta, cendelta, delta,  matrixh)
    call matrix_mult(axisbeta , cenbeta,  beta,   matrixh)
    call matrix_mult(axisrotor, cenrotor, psi,    matrixh)

  end subroutine get_elastic_blade_transform


!=============================== GET_HUB_TRANSFORM ===========================80
!
!  Takes the rotor settings in rotor.input and computes the new transformation
!  matrix for a rotor hub (shaft)
!
!=============================================================================80

  subroutine get_hub_transform(rn, matrixh, psi_shaft)

    use rotors,              only : rotor
    use grid_motion_helpers, only : matrix_mult
    use linear_algebra,      only : identity_matrix

    real(dp), dimension(4,4),    intent(out) :: matrixh
    real(dp),                    intent(in)  :: psi_shaft
    integer,                     intent(in)  :: rn

    real(dp)                  :: cosalp, sinalp, sn
    real(dp)                  :: psi1

    real(dp), dimension(3)    :: axisrotor, cenrotor

  continue

!   alpha0 is the tip angle of the TPP with respect to reference plane; assumes
!   that axis tilted in the xz-plane, taken from the phi2 input option; a
!   negative angle in the rotor.input file implies a tilt forward so negate here

    cosalp = cos(-1.0_dp*rotor(rn)%alpha0)
    sinalp = sin(-1.0_dp*rotor(rn)%alpha0)

!   rotor hub location

    cenrotor(1) = rotor(rn)%x0
    cenrotor(2) = rotor(rn)%y0
    cenrotor(3) = rotor(rn)%z0

!   rotor axis, for clockwise axis is -Z, for counter clockwise axis is Z

    sn =  1.0_dp                            ! counterclockwise rotation
    if (rotor(rn)%dirrot == 1) sn = -1.0_dp ! clockwise rotation

    axisrotor(1) = -sinalp*sn
    axisrotor(2) = 0.0_dp
    axisrotor(3) = cosalp*sn

    psi1 = psi_shaft

!   create homogeneous transformation matrix
!   [matrixh]=[psi][i]

    matrixh = identity_matrix(4)

    call matrix_mult(axisrotor, cenrotor, psi1, matrixh)

  end subroutine get_hub_transform


!=========================== DEFORM_ROTOR_SURFACE_POINT ======================80
!
! Moves a surface point x,y,z on a rotor blade, given the location of the local
! quarter chord and the comprehensive code deflections stored in the data()
! array; data(1-6) = dx, dy, dz, dangx, dangy, dangz where dangx, dangy
! The rotations are given in terms of Euler angles about body-fixed axes in
! the order Z-Y-X, which is equivalent to rotation about fixed axes in the
! order x-y-z, which is how the rotation matrix is formed in this routine.
!
!  Also returns the associated transform matrix and inverse transform.
!  Assumes the blade is in the reference orientation:
!
!   +y axis
!   |
!   |     l.e.
!   -----------------
!      blade/wing    | ----- +x axis
!   -----------------
!         t.e.
!
!=============================================================================80

  subroutine deform_rotor_surface_point(x, y, z, data, transform_matrix,       &
                                        inv_transform)

    use grid_motion_helpers, only : get_inverse_transform

    real(dp),                    intent(inout) :: x, y, z
    real(dp), dimension(9),      intent(in)    :: data
    real(dp), dimension(4,4),    intent(out)   :: transform_matrix
    real(dp), dimension(4,4),    intent(out)   :: inv_transform

    real(dp)    :: xqc, yqc, zqc, dx, dy, dz, dangx, dangy, dangz
    real(dp)    :: cx, sx, cy, sy, cz, sz, xold, yold, zold

  continue

    xqc = data(7)
    yqc = data(8)
    zqc = data(9)

    dx = data(1)
    dy = data(2)
    dz = data(3)

    dangx =  data(4)
    dangy =  data(5)
    dangz =  data(6)

    cx  = cos(dangx)
    sx  = sin(dangx)
    cy  = cos(dangy)
    sy  = sin(dangy)
    cz  = cos(dangz)
    sz  = sin(dangz)

!   fill out the rotation part of the matrix

    transform_matrix(1,1) =  cy*cz
    transform_matrix(1,2) =  cz*sy*sx - sz*cx
    transform_matrix(1,3) =  cz*sy*cx + sz*sx
    transform_matrix(2,1) =  cy*sz
    transform_matrix(2,2) =  sz*sy*sx + cx*cz
    transform_matrix(2,3) =  sz*sy*cx - cz*sx
    transform_matrix(3,1) = -sy
    transform_matrix(3,2) =  cy*sx
    transform_matrix(3,3) =  cy*cx

!   fill out the translation part of the matrix

    transform_matrix(1,4) = dx + xqc - (transform_matrix(1,1)*xqc +            &
                                        transform_matrix(1,2)*yqc +            &
                                        transform_matrix(1,3)*zqc)

    transform_matrix(2,4) = dy + yqc - (transform_matrix(2,1)*xqc +            &
                                        transform_matrix(2,2)*yqc +            &
                                        transform_matrix(2,3)*zqc)

    transform_matrix(3,4) = dz + zqc - (transform_matrix(3,1)*xqc +            &
                                        transform_matrix(3,2)*yqc +            &
                                        transform_matrix(3,3)*zqc)

!   fill out the bottom row of the matrix

    transform_matrix(4,1)  = 0._dp
    transform_matrix(4,2)  = 0._dp
    transform_matrix(4,3)  = 0._dp
    transform_matrix(4,4)  = 1._dp

!   get the inverse while we are at it

    call get_inverse_transform(transform_matrix, inv_transform)

!   finally, get the new position for the surface point

    xold = x
    yold = y
    zold = z

    x = transform_matrix(1,1)*xold + transform_matrix(1,2)*yold                &
      + transform_matrix(1,3)*zold + transform_matrix(1,4)
    y = transform_matrix(2,1)*xold + transform_matrix(2,2)*yold                &
      + transform_matrix(2,3)*zold + transform_matrix(2,4)
    z = transform_matrix(3,1)*xold + transform_matrix(3,2)*yold                &
      + transform_matrix(3,3)*zold + transform_matrix(3,4)

  end subroutine deform_rotor_surface_point


!========================= UPDATE_ELASTIC_BLADE_SURFACE ======================80
!
! Gets new positions for each point on an elastic blade surface
! Assumes the blade is in the reference orientation:
!
!   +y axis
!   |
!   |     l.e.
!   -----------------
!      blade/wing    | ----- +x axis
!   -----------------
!         t.e.
!
! Currently supports camrad/rcas style or fsi style
!
!=============================================================================80

  subroutine update_elastic_blade_surface(surface_mesh, body)

    use info_depr,           only : simulation_time
    use grid_types,          only : mass_type
    use lmpi,                only : lmpi_die, lmpi_master, lmpi_id
    use rotors,              only : rotor, get_rotor_id_from_ref_frame
    use grid_motion_helpers, only : matrix_mult,                               &
                                    translate_to_center
    use linear_algebra,      only : identity_matrix
    use allocations,         only : my_alloc_ptr

    integer,         intent(in)    :: body

    type(mass_type), intent(inout) :: surface_mesh

    integer                        :: i, rn , blade, id

    real(dp), dimension(4,4)       :: point_transform, inv_point_transform
    real(dp), dimension(4,4)       :: transform_to_ref, transform_from_ref
    real(dp), dimension(9)         :: data
    real(dp), dimension(3)         :: axisbeta, cenbeta
    real(dp), dimension(3)         :: axisdelta, cendelta
    real(dp), dimension(3)         :: axistilt, centilt, axisrotor, cenrotor
    real(dp), dimension(3)         :: axistheta, centheta, neg_cenrotor

    real(dp)                       :: psi, psi_t0, xmt, ymt, zmt, sim_time
    real(dp)                       :: cosalp, sinalp, sn, alpha0, pi
    real(dp)                       :: theta_t0, beta_t0, delta_t0
    real(dp)                       :: ebeta, edelta, etheta

    type(mass_type)                :: surface_mesh_ref, surface_mesh_deform

    character(len=80)              :: project

    logical                        :: output_surfaces = .false.  ! for debugging

  continue

    if (output_surfaces) then

      surface_mesh_ref%itotal = surface_mesh%itotal

      call my_alloc_ptr(surface_mesh_ref%xmt,     max(1,surface_mesh%itotal))
      call my_alloc_ptr(surface_mesh_ref%ymt,     max(1,surface_mesh%itotal))
      call my_alloc_ptr(surface_mesh_ref%zmt,     max(1,surface_mesh%itotal))
      call my_alloc_ptr(surface_mesh_ref%inodemt, max(1,surface_mesh%itotal))

      surface_mesh_deform%itotal = surface_mesh%itotal

      call my_alloc_ptr(surface_mesh_deform%xmt,     max(1,surface_mesh%itotal))
      call my_alloc_ptr(surface_mesh_deform%ymt,     max(1,surface_mesh%itotal))
      call my_alloc_ptr(surface_mesh_deform%zmt,     max(1,surface_mesh%itotal))
      call my_alloc_ptr(surface_mesh_deform%inodemt, max(1,surface_mesh%itotal))

    end if

    pi = acos(-1.0_dp)

    call get_rotor_id_from_ref_frame(body, rn, blade)

    select case(trim(comprehensive_rotor_coupling))

      case('camrad', 'rcas', 'rcas_tight', 'fsi_tight')

!       rotor axis, for clockwise axis is -Z, for counter clockwise axis is Z

        sn =  1.0_dp                            ! counterclockwise rotation
        if (rotor(rn)%dirrot == 1) sn = -1.0_dp ! clockwise rotation

!       alpha0 is the tip angle of the TPP with respect to reference plane;
!       assumes that axis tilted in the xz-plane, taken from the phi2 input
!       option; a negative angle in the rotor.input file implies a tilt forward
!       so negate here

        alpha0 = rotor(rn)%alpha0
        cosalp = cos(-1.0_dp*alpha0)
        sinalp = sin(-1.0_dp*alpha0)

!       shaft tilt axis - fore/aft tilt only - no side-to-side tilt

        axistilt(1) = 0.0_dp
        axistilt(2) = 1.0_dp
        axistilt(3) = 0.0_dp
        centilt(1)  = rotor(rn)%x0
        centilt(2)  = rotor(rn)%y0
        centilt(3)  = rotor(rn)%z0

!       rotor hub axis

        axisrotor(1) = -sinalp*sn
        axisrotor(2) = 0.0_dp
        axisrotor(3) = cosalp*sn
        cenrotor(1)  = rotor(rn)%x0
        cenrotor(2)  = rotor(rn)%y0
        cenrotor(3)  = rotor(rn)%z0

        neg_cenrotor(:) = -cenrotor(:)

!       hinge offsets, assumes non-dimensionalized by rotor radius

        ebeta  = rotor(rn)%rfh
        edelta = rotor(rn)%rlh
        etheta = rotor(rn)%rph

!       pitch about xrotor

        axistheta(1) = cosalp*sn
        axistheta(2) = 0.0_dp
        axistheta(3) = sinalp*sn
        centheta(1)  = rotor(rn)%x0 + etheta*rotor(rn)%rtip*cosalp
        centheta(2)  = rotor(rn)%y0
        centheta(3)  = rotor(rn)%z0 + etheta*rotor(rn)%rtip*sinalp

!       lead/lag about zrotor

        axisdelta(1) = -sinalp
        axisdelta(2) = 0.0_dp
        axisdelta(3) = cosalp
        cendelta(1)  = rotor(rn)%x0 + edelta*rotor(rn)%rtip*cosalp
        cendelta(2)  = rotor(rn)%y0
        cendelta(3)  = rotor(rn)%z0 + edelta*rotor(rn)%rtip*sinalp

!       flap about Y

        axisbeta(1) = 0.0_dp
        axisbeta(2) = -1.0_dp
        axisbeta(3) = 0.0_dp
        cenbeta(1)  = rotor(rn)%x0 + ebeta*rotor(rn)%rtip*cosalp
        cenbeta(2)  = rotor(rn)%y0
        cenbeta(3)  = rotor(rn)%z0 + ebeta*rotor(rn)%rtip*sinalp

        psi_t0 = real(blade-1,dp)*(pi*2.0_dp) / real(rotor(rn)%nblade,dp)

        psi    = rotor(rn)%omega*simulation_time + psi_t0

!       blades in composite mesh are positioned in a neutral setting, and
!       "popped" to the flight-condition specific settings right before the
!       flow solution commences

        beta_t0  = 0._dp
        theta_t0 = 0._dp
        delta_t0 = 0._dp

        transform_to_ref   = identity_matrix(4)
        transform_from_ref = identity_matrix(4)

!       starting with the blade (in surface_mesh) as it is positioned in the
!       input grid at t=0, get the transform needed to put it back to the
!       reference blade position, i.e., aligned with x-axis

        call matrix_mult(axisrotor,  cenrotor, -psi_t0,   transform_to_ref)
        call matrix_mult(axisbeta ,  cenbeta,  -beta_t0,  transform_to_ref)
        call matrix_mult(axisdelta,  cendelta, -delta_t0, transform_to_ref)
        call matrix_mult(axistheta,  centheta, -theta_t0, transform_to_ref)
        call matrix_mult(axistilt,   centilt,  -alpha0,   transform_to_ref)
        call translate_to_center(neg_cenrotor,            transform_to_ref)

!       get the transform needed to go from the (deformed) reference blade
!       position to the current blade position (note that the defomed blade will
!       implicitly include beta, delta and theta components, so they must
!       not be included in the transform_from_ref matix

        call translate_to_center(  cenrotor,          transform_from_ref)
        call matrix_mult(axistilt,  centilt,  alpha0, transform_from_ref)
        call matrix_mult(axisrotor, cenrotor, psi,    transform_from_ref)

!       deform the blade as it sits in the reference orientation

        do i = 1,surface_mesh%itotal

!         transform to reference blade system

          xmt = transform_to_ref(1,1)*surface_mesh%xmt(i)                      &
              + transform_to_ref(1,2)*surface_mesh%ymt(i)                      &
              + transform_to_ref(1,3)*surface_mesh%zmt(i)                      &
              + transform_to_ref(1,4)

          ymt = transform_to_ref(2,1)*surface_mesh%xmt(i)                      &
              + transform_to_ref(2,2)*surface_mesh%ymt(i)                      &
              + transform_to_ref(2,3)*surface_mesh%zmt(i)                      &
              + transform_to_ref(2,4)

          zmt = transform_to_ref(3,1)*surface_mesh%xmt(i)                      &
              + transform_to_ref(3,2)*surface_mesh%ymt(i)                      &
              + transform_to_ref(3,3)*surface_mesh%zmt(i)                      &
              + transform_to_ref(3,4)

          call evaluate_motion_spline(rn, blade, xmt, psi, data)

          if (output_surfaces) then
            surface_mesh_ref%xmt(i)     = xmt
            surface_mesh_ref%ymt(i)     = ymt
            surface_mesh_ref%zmt(i)     = zmt
            surface_mesh_ref%inodemt(i) = surface_mesh%inodemt(i)
          end if

          call deform_rotor_surface_point(xmt, ymt, zmt, data,                 &
                                          point_transform, inv_point_transform)

          if (output_surfaces) then
            surface_mesh_deform%xmt(i)     = xmt
            surface_mesh_deform%ymt(i)     = ymt
            surface_mesh_deform%zmt(i)     = zmt
            surface_mesh_deform%inodemt(i) = surface_mesh%inodemt(i)
          end if

!         transform new surface point to current position

          surface_mesh%xmt(i) = transform_from_ref(1,1)*xmt                    &
                              + transform_from_ref(1,2)*ymt                    &
                              + transform_from_ref(1,3)*zmt                    &
                              + transform_from_ref(1,4)

          surface_mesh%ymt(i) = transform_from_ref(2,1)*xmt                    &
                              + transform_from_ref(2,2)*ymt                    &
                              + transform_from_ref(2,3)*zmt                    &
                              + transform_from_ref(2,4)

          surface_mesh%zmt(i) = transform_from_ref(3,1)*xmt                    &
                              + transform_from_ref(3,2)*ymt                    &
                              + transform_from_ref(3,3)*zmt                    &
                              + transform_from_ref(3,4)

        end do

      case('fsi')

      case('none')

      case default

        if (lmpi_master) then
          write(*,*)' error in set_up_rotor_motion: unknown string in',        &
                    ' comprehensive_rotor_coupling: ',                         &
                    trim(comprehensive_rotor_coupling)
        end if
        call lmpi_die

    end select

    if (output_surfaces) then

      project = 'blade_t0'
      id = lmpi_id
      sim_time = 0.0_dp
      if (surface_mesh_ref%itotal > 0) then
        call write_surf(surface_mesh_ref, project, body, id, sim_time)
      end if
      deallocate(surface_mesh_ref%xmt)
      deallocate(surface_mesh_ref%ymt)
      deallocate(surface_mesh_ref%zmt)
      deallocate(surface_mesh_ref%inodemt)

      project = 'blade'
      id = lmpi_id
      sim_time = 0.0_dp
      if (surface_mesh_deform%itotal > 0) then
        call write_surf(surface_mesh_deform, project, body, id, sim_time)
      end if
      deallocate(surface_mesh_deform%xmt)
      deallocate(surface_mesh_deform%ymt)
      deallocate(surface_mesh_deform%zmt)
      deallocate(surface_mesh_deform%inodemt)

    end if

  end subroutine update_elastic_blade_surface


!=============================================================================80
!
! Evaluate a spline for either CAMRAD/RCAS or FSI-tight to get new motion at
! given location.
!
!=============================================================================80

  subroutine evaluate_motion_spline(rn, blade, xmt, psi, data)

    use csd_coupling,       only : evaluate_elastic_rotor_spline
    use fsi_coupling_tight, only : evaluate_fsi_motion_spline

    integer,                intent(in)  :: rn, blade
    real(dp),               intent(in)  :: xmt, psi
    real(dp), dimension(9), intent(out) :: data

  continue

    select case(trim(comprehensive_rotor_coupling))
      case ('camrad', 'rcas', 'rcas_tight')
        call evaluate_elastic_rotor_spline(rn, blade, xmt, psi, data)
      case ('fsi_tight')
        call evaluate_fsi_motion_spline(blade, xmt, data)
      case default
    end select

  end subroutine evaluate_motion_spline


!============================ GET_COMPREHENSIVE_MOTION =======================80
!
! Loads motion data generated by the selected comprehensive code; currently
! supports camrad, rcas or fsi motion data
!
!=============================================================================80

  subroutine get_comprehensive_motion()

    use csd_coupling,       only : read_rotor_motion_file
    use fsi_coupling,       only : init_fsi
    use fsi_coupling_tight, only : init_fsi_tight
    use rotors,             only : nrotor, rotor
    use lmpi,               only : lmpi_die, lmpi_master
    use lmpi,               only : lmpi_bcast
    use csd_coupling,       only : cfdcsdstatus

    integer           :: rn, nblades_read
    integer, save     :: counter = 0

    logical, save     :: check_spline = .true.

    real(dp)          :: cfdcsdstatus_last

  continue

    select case (trim(comprehensive_rotor_coupling))

      case ('camrad')

        nblades_read = 1

        do rn = 1,nrotor
          if (individual_blade_motion(rn)) nblades_read = rotor(rn)%nblade
          call read_rotor_motion_file(rn, nblades_read, check_spline,          &
                                       comprehensive_rotor_coupling)
        end do

      case('rcas', 'rcas_tight')

!       get motion data from RCAS
!         check RCAS status to see what it is doing:
!           cfdcsdstatus =  1 means lcp in progress
!                        = 99 means lcp and tcp are complete
!                        >  1 and < 99 means tcp in progress

        if (lmpi_master) then

          write(*,*)
          write(*,'(a)') ' Requesting rotor motion from RCAS'
          cfdcsdstatus_last = cfdcsdstatus

          call sdxget('cfd.csd.status'//char(0), cfdcsdstatus, 1)

          if (int(cfdcsdstatus_last) > 0) then
            if (cfdcsdstatus_last <= 1._dp .and. cfdcsdstatus > 1._dp) then
              write(*,*)
              write(*,'(a)')' Switching from loose coupling to tight coupling'
              write(*,*)
              comprehensive_rotor_coupling = 'rcas_tight'
            end if
          end if

        end if

        call lmpi_bcast(cfdcsdstatus)
        call lmpi_bcast(comprehensive_rotor_coupling)

        if (trim(comprehensive_rotor_coupling) == 'rcas') then

!         loose coupling

          nblades_read = 1

          do rn = 1,nrotor
            call read_rotor_motion_file(rn, nblades_read, check_spline,        &
                                        comprehensive_rotor_coupling)
          end do

        else

!         tight coupling

          do rn = 1,nrotor
            counter = counter + 1
!           only do spline checks the first time step
            if (counter > nrotor) check_spline = .false.
            nblades_read = rotor(rn)%nblade
            call read_rotor_motion_file(rn, nblades_read, check_spline,        &
                                        comprehensive_rotor_coupling)
          end do

        end if

      case ('fsi')

          call init_fsi()

      case ('fsi_tight')

          call init_fsi_tight()

      case('none')

      case default

        if (lmpi_master) then
          write(*,*)' error in set_up_rotor_motion: unknown string in',        &
                    ' comprehensive_rotor_coupling: ',                         &
                    trim(comprehensive_rotor_coupling)
        end if
        call lmpi_die

    end select

  end subroutine get_comprehensive_motion


!=========================== EXTRACT_COMPREHENSIVE_LOADS =====================80
!
! Computes the aero loads needed by the selected comprehensive code; currently
! supports camrad/rcas style or fsi style
!
!=============================================================================80

  subroutine extract_comprehensive_loads(grid, soln)

    use grid_types,        only : grid_type
    use solution_types,    only : soln_type
    use info_depr,         only : ntt
    use nml_global,        only : slice_freq
    use csd_coupling,      only : get_rotor_airloads_data
    use fsi_coupling,      only : extract_fsi_loads
    use fsi_coupling_tight, only : get_fsi_section_data

    type(grid_type),                                    intent(in) :: grid
    type(soln_type),                                    intent(in) :: soln

  continue

    select case (trim(comprehensive_rotor_coupling))

      case ('camrad', 'rcas', 'rcas_tight')

        if (ntt/slice_freq*slice_freq == ntt) then
          call get_rotor_airloads_data(soln%eqn_set)
        end if

      case ('fsi')

        call extract_fsi_loads(grid)

      case ('fsi_tight')

        call get_fsi_section_data(soln%eqn_set)

      case default

        if (ntt/slice_freq*slice_freq == ntt) then
          call get_rotor_airloads_data(soln%eqn_set)
        end if

    end select

  end subroutine extract_comprehensive_loads


!============================ WRITE_COMPREHENSIVE_LOADS ======================80
!
! Outputs the aero loads needed by the selected comprehensive code; currently
! supports camrad/rcas style or fsi style
!
!=============================================================================80

  subroutine write_comprehensive_loads()

    use info_depr,         only : ntt
    use nml_global,        only : slice_freq
    use csd_coupling,      only : write_rotor_airloads_data, airloads_data,    &
                                  azimuth_range_text, exchange_data_with_rcas, &
                                  check_onerev_data_available
    use nml_rcas_sdx,      only : initial_cfd_revs
    use csd_coupling,      only : send_airloads_via_sdx
    use fsi_coupling,      only : write_fsi_loads
    use lmpi,              only : lmpi_master, lmpi_synchronize
    use rotors,            only : rotor, nrotor
    use system_extensions, only : se_shell

    integer            :: rn
    integer,      save :: rcas_intervalometer = 0

    character(len=80)  :: text_rn, text_nrevs, text_azi_beg, text_azi_end
    character(len=80)  :: file1, file2
    character(len=256) :: command

    real(dp)           :: psi, psi_min

  continue

!   determine if we have collected enough data to provide data to the
!   comprehensive code (loose coupling), or for file output (tight coupling)

    call check_onerev_data_available()

    select case (trim(comprehensive_rotor_coupling))

      case ('camrad')

        if (ntt/slice_freq*slice_freq == ntt) then
          call write_rotor_airloads_data(comprehensive_rotor_coupling)
        end if

      case ('rcas')

        if (ntt/slice_freq*slice_freq == ntt) then
          call write_rotor_airloads_data(comprehensive_rotor_coupling)
        end if

!       let RCAS know when the airloads are ready

        do rn  = 1,nrotor

          psi = rotor(rn)%psi1*180.0_dp/acos(-1.0_dp)

          if (have_360_data(rn)) then    ! we have at least 1 rev of data

            rcas_intervalometer = rcas_intervalometer + 1

!           don't couple with RCAS until we have done some minimum number
!           of revs in order to wash out initial transients

            psi_min = 360._dp*real(initial_cfd_revs,dp) - 0.01_dp

            if (psi >= psi_min) then

!             only couple with RCAS when we have collected 2 revs worth of
!             data, in order to wash out previous coupling transients; for
!             a 4-bladed rotor, where we collect data on all 4 blades, this
!             is equivalent to coupling every 180 degress, like we do with
!             CAMRAD

              if (rcas_intervalometer > 1) then

                exchange_data_with_rcas = .true.

                if (lmpi_master) then
                  call send_airloads_via_sdx(rn)
                end if

                call lmpi_synchronize()

                rcas_intervalometer = 0  ! reset

              end if

            end if

          end if

        end do

      case ('rcas_tight')

!       output current loads if appropriate (when a rev is completed)

        call write_rotor_airloads_data(comprehensive_rotor_coupling)

        exchange_data_with_rcas = .true.

        if (lmpi_master) then
          do rn  = 1,nrotor
            call send_airloads_via_sdx(rn)
          end do
        end if

        call lmpi_synchronize()

      case ('fsi')

        call write_fsi_loads()

      case ('fsi_tight')  ! Do nothing for now since we don't need the file.
                          ! Might need to write one later for debugging.
      case default

        if (ntt/slice_freq*slice_freq == ntt) then
          call write_rotor_airloads_data(comprehensive_rotor_coupling)
        end if

    end select

!   loose coupling: archive the "motion.txt" data when current rev is complete
!   note: we don't *have* a file to archive if using rcas' sdx option...

    if (trim(comprehensive_rotor_coupling) == 'camrad') then

      do rn = 1,nrotor
        if (have_360_data(rn)) then
          write(text_rn,*) rn
          text_rn = adjustl(text_rn)
          call azimuth_range_text(rn,text_nrevs,text_azi_beg,text_azi_end)
          file1 = 'camrad_motion_data_rotor_' // trim(text_rn) // '.dat'
          file2 = trim(file1) // '_rev_' // trim(text_nrevs) // '_psi_' // &
                  trim(text_azi_beg) // '-' // trim(text_azi_end)
          command = 'cp '//trim(adjustl(file1))//' '//trim(adjustl(file2))
          if (lmpi_master) call se_shell(trim(adjustl(command)))
        end if
      end do

    end if

!   reset the counter when a complete rev has been output

    do rn = 1,nrotor
      if (have_360_data(rn)) airloads_data(rn)%nazimuth = 0
    end do

  end subroutine write_comprehensive_loads


!=============================== ROTOR_PERFORMANCE ===========================80
!
! Computes rotor performance metrics: thrust coefficient, torque coeficient,
! and figure-of-merit (averages over the number of azimuth stations of the
! current run)
!
!=============================================================================80

  subroutine rotor_performance()

    use rotors,              only : nrotor, rotor, get_rotor_id_from_ref_frame,&
                                    vinf_ratio
    use lmpi,                only : lmpi_master
    use info_depr,           only : alpha, yaw
    use grid_motion_helpers, only : matrix_mult
    use nml_grid_motion,     only : ref_velocity, ref_density, ref_length
    use linear_algebra,      only : identity_matrix
    use moving_body_types,   only : moving_body
    use nml_grid_motion,     only : n_moving_bodies
    use csd_coupling,        only : get_nrevs

    integer       :: body, blade, rn, nrevs

    logical, save :: init = .true.

    real(dp)      :: fx, fy, fz, mx, my, mz
    real(dp)      :: conv, alpha_rad, yaw_rad
    real(dp)      :: pi, area_factor, velocity_factor, force_factor
    real(dp)      :: length_factor_x, length_factor_y, length_factor_z
    real(dp)      :: f_factor, m_factor, p_factor, psi_in, psi_out, revs

    integer,  dimension(:),     allocatable, save :: nazimuth

    real(dp), dimension(:),     allocatable, save :: advance_ratio
    real(dp), dimension(:),     allocatable, save :: cp, ct, cq, fm, solidity
    real(dp), dimension(:),     allocatable, save :: cpi0
    real(dp), dimension(:),     allocatable, save :: dim_force
    real(dp), dimension(:),     allocatable, save :: dim_moment
    real(dp), dimension(:),     allocatable, save :: dim_power
    real(dp), dimension(:),     allocatable, save :: cx, cy, cz, cl, cd
    real(dp), dimension(:),     allocatable, save :: cmx, cmy, cmz
    real(dp), dimension(:),     allocatable, save :: cx_shaft, cy_shaft
    real(dp), dimension(:),     allocatable, save :: cz_shaft
    real(dp), dimension(:),     allocatable, save :: cmx_shaft, cmy_shaft
    real(dp), dimension(:),     allocatable, save :: cmz_shaft
    real(dp), dimension(:),     allocatable, save :: cx_wind, cy_wind
    real(dp), dimension(:),     allocatable, save :: cz_wind
    real(dp), dimension(:),     allocatable, save :: cmx_wind, cmy_wind
    real(dp), dimension(:),     allocatable, save :: cmz_wind
    real(dp), dimension(4,4)                      :: transform
    real(dp), dimension(:,:,:), allocatable, save :: trans_shaft
    real(dp), dimension(4,4)                      :: trans_wind
    real(dp), dimension(3)                        :: axistilt, centilt

  continue

    pi = acos(-1.0_dp)
    conv = pi/180.0_dp

    if (init) then

      allocate(nazimuth(nrotor))

      allocate(advance_ratio(nrotor))
      allocate(cp(nrotor))
      allocate(cpi0(nrotor))
      allocate(ct(nrotor))
      allocate(cq(nrotor))
      allocate(fm(nrotor))
      allocate(cl(nrotor))
      allocate(cd(nrotor))
      allocate(solidity(nrotor))

      allocate(cx(nrotor))
      allocate(cy(nrotor))
      allocate(cz(nrotor))
      allocate(cmx(nrotor))
      allocate(cmy(nrotor))
      allocate(cmz(nrotor))

      allocate(cx_shaft(nrotor))
      allocate(cy_shaft(nrotor))
      allocate(cz_shaft(nrotor))
      allocate(cmx_shaft(nrotor))
      allocate(cmy_shaft(nrotor))
      allocate(cmz_shaft(nrotor))

      allocate(cx_wind(nrotor))
      allocate(cy_wind(nrotor))
      allocate(cz_wind(nrotor))
      allocate(cmx_wind(nrotor))
      allocate(cmy_wind(nrotor))
      allocate(cmz_wind(nrotor))

      allocate(dim_force(nrotor))
      allocate(dim_moment(nrotor))
      allocate(dim_power(nrotor))

      nazimuth(:) = 0

      cp(:)        = 0.0_dp
      cpi0(:)      = 0.0_dp
      ct(:)        = 0.0_dp
      cq(:)        = 0.0_dp
      fm(:)        = 0.0_dp
      cl(:)        = 0.0_dp
      cd(:)        = 0.0_dp
      solidity(:)  = 0.0_dp

      cx(:)        = 0.0_dp
      cy(:)        = 0.0_dp
      cz(:)        = 0.0_dp
      cmx(:)       = 0.0_dp
      cmy(:)       = 0.0_dp
      cmz(:)       = 0.0_dp

      cx_shaft(:)  = 0.0_dp
      cy_shaft(:)  = 0.0_dp
      cz_shaft(:)  = 0.0_dp
      cmx_shaft(:) = 0.0_dp
      cmy_shaft(:) = 0.0_dp
      cmz_shaft(:) = 0.0_dp

      cx_wind(:)   = 0.0_dp
      cy_wind(:)   = 0.0_dp
      cz_wind(:)   = 0.0_dp
      cmx_wind(:)  = 0.0_dp
      cmy_wind(:)  = 0.0_dp
      cmz_wind(:)  = 0.0_dp

      dim_force(:)  = 1.0_dp
      dim_moment(:) = 1.0_dp
      dim_power(:)  = 1.0_dp

      advance_ratio(:) = vinf_ratio / rotor(:)%vt_ratio

!     get transform matrix from inertial axis into non-rotating shaft axis
!     assume shaft has fore/aft tilt (alpha0) only - no side-to-side tilt

      allocate(trans_shaft(4,4,nrotor))

      do rn = 1,nrotor

        axistilt(1) = 0.0_dp
        axistilt(2) = 1.0_dp
        axistilt(3) = 0.0_dp
        centilt(1)  = rotor(rn)%x0
        centilt(2)  = rotor(rn)%y0
        centilt(3)  = rotor(rn)%z0

        transform  = identity_matrix(4)
        call matrix_mult(axistilt, centilt, -rotor(rn)%alpha0, transform)

        trans_shaft(:,:,rn)  = transform(:,:)

      end do

!     set transform matrix from inertial axis into wind axis

      trans_wind = identity_matrix(4)

      alpha_rad = alpha*conv
      yaw_rad   = yaw*conv

      trans_wind(1,1) =  cos(alpha_rad)*cos(yaw_rad)
      trans_wind(1,2) = -sin(yaw_rad)
      trans_wind(1,3) =  sin(alpha_rad)*cos(yaw_rad)
      trans_wind(2,1) =  cos(alpha_rad)*sin(yaw_rad)
      trans_wind(2,2) =  cos(yaw_rad)
      trans_wind(2,3) =  sin(alpha_rad)*sin(yaw_rad)
      trans_wind(3,1) = -sin(alpha_rad)
      trans_wind(3,2) =  0.0_dp
      trans_wind(3,3) =  cos(alpha_rad)

!     set factors for conversion to dimensional output if desired

      do rn = 1,nrotor
        dim_force(rn)  = ref_density                                           &
                       * pi*(ref_length*rotor(rn)%rtip)**2                     &
                       * (ref_velocity*rotor(rn)%omega*rotor(rn)%rtip)**2
        dim_moment(rn) = ref_density                                           &
                       * pi*(ref_length*rotor(rn)%rtip)**3                     &
                       * (ref_velocity*rotor(rn)%omega*rotor(rn)%rtip)**2
        dim_power(rn)  = ref_density                                           &
                       * pi*(ref_length*rotor(rn)%rtip)**2                     &
                       * (ref_velocity*rotor(rn)%omega*rotor(rn)%rtip)**3
      end do

      init = .false.

    end if

!   convert inertial-frame force/moment coefficients from averages
!   back to running totals; zero out solidity

    cx(:)  = cx(:)*real(nazimuth(:), dp)
    cy(:)  = cy(:)*real(nazimuth(:), dp)
    cz(:)  = cz(:)*real(nazimuth(:), dp)
    cmx(:) = cmx(:)*real(nazimuth(:), dp)
    cmy(:) = cmy(:)*real(nazimuth(:), dp)
    cmz(:) = cmz(:)*real(nazimuth(:), dp)

    solidity(:)  = 0.0_dp

    do body = 1,n_moving_bodies

!     forces/moments in inertial frame

      fx = moving_body(body)%totforce%cx
      fy = moving_body(body)%totforce%cy
      fz = moving_body(body)%totforce%cz
      mx = moving_body(body)%totforce%cmx
      my = moving_body(body)%totforce%cmy
      mz = moving_body(body)%totforce%cmz

!     given body number, get rotor number (and blade number)

      call get_rotor_id_from_ref_frame(body, rn, blade)

!     multiplicative factors to ensure that rotorcraft normalizations are used
!     for forces and moments rather than the standard fixed-wing normalizations

      area_factor     = moving_body(body)%sref/(pi*rotor(rn)%rtip**2)
      velocity_factor = (1.0_dp/rotor(rn)%vt_ratio)**2
      length_factor_x = moving_body(body)%bref/rotor(rn)%rtip
      length_factor_y = moving_body(body)%cref/rotor(rn)%rtip
      length_factor_z = moving_body(body)%bref/rotor(rn)%rtip

      force_factor    = 0.5_dp*area_factor*velocity_factor

!     compute running force/moment coefficients in inertial frame

      cx(rn)  = cx(rn)  + fx*force_factor
      cy(rn)  = cy(rn)  + fy*force_factor
      cz(rn)  = cz(rn)  + fz*force_factor
      cmx(rn) = cmx(rn) + mx*force_factor*length_factor_x
      cmy(rn) = cmy(rn) + my*force_factor*length_factor_y
      cmz(rn) = cmz(rn) + mz*force_factor*length_factor_z

!     solidity below based on HART-II definition, using rtip and not
!     rtip-rroot - not sure if that is standard...

      solidity(rn) = solidity(rn) + rotor(rn)%rtip*rotor(rn)%chord

    end do

    do rn = 1,nrotor

      nazimuth(rn) = nazimuth(rn) + 1

!     average inertial-frame thrust and torque over the number of
!     azimuth positions so far

      cx(rn)  = cx(rn) / real(nazimuth(rn), dp)
      cy(rn)  = cy(rn) / real(nazimuth(rn), dp)
      cz(rn)  = cz(rn) / real(nazimuth(rn), dp)
      cmx(rn) = cmx(rn) / real(nazimuth(rn), dp)
      cmy(rn) = cmy(rn) / real(nazimuth(rn), dp)
      cmz(rn) = cmz(rn) / real(nazimuth(rn), dp)

!     convert inertial axes forces and moments to shaft axes

      cx_shaft(rn) = cx(rn)*trans_shaft(1,1,rn) + cy(rn)*trans_shaft(1,2,rn)   &
                   + cz(rn)*trans_shaft(1,3,rn)
      cy_shaft(rn) = cx(rn)*trans_shaft(2,1,rn) + cy(rn)*trans_shaft(2,2,rn)   &
                   + cz(rn)*trans_shaft(2,3,rn)
      cz_shaft(rn) = cx(rn)*trans_shaft(3,1,rn) + cy(rn)*trans_shaft(3,2,rn)   &
                   + cz(rn)*trans_shaft(3,3,rn)

      cmx_shaft(rn) = cmx(rn)*trans_shaft(1,1,rn) + cmy(rn)*trans_shaft(1,2,rn)&
                    + cmz(rn)*trans_shaft(1,3,rn)
      cmy_shaft(rn) = cmx(rn)*trans_shaft(2,1,rn) + cmy(rn)*trans_shaft(2,2,rn)&
                    + cmz(rn)*trans_shaft(2,3,rn)
      cmz_shaft(rn) = cmx(rn)*trans_shaft(3,1,rn) + cmy(rn)*trans_shaft(3,2,rn)&
                    + cmz(rn)*trans_shaft(3,3,rn)

!     convert inertial axes forces and moments to wind axes

      cx_wind(rn) = cx(rn)*trans_wind(1,1) + cy(rn)*trans_wind(1,2)            &
                  + cz(rn)*trans_wind(1,3)
      cy_wind(rn) = cx(rn)*trans_wind(2,1) + cy(rn)*trans_wind(2,2)            &
                  + cz(rn)*trans_wind(2,3)
      cz_wind(rn) = cx(rn)*trans_wind(3,1) + cy(rn)*trans_wind(3,2)            &
                  + cz(rn)*trans_wind(3,3)

      cmx_wind(rn) = cmx(rn)*trans_wind(1,1) + cmy(rn)*trans_wind(1,2)         &
                   + cmz(rn)*trans_wind(1,3)
      cmy_wind(rn) = cmx(rn)*trans_wind(2,1) + cmy(rn)*trans_wind(2,2)         &
                   + cmz(rn)*trans_wind(2,3)
      cmz_wind(rn) = cmx(rn)*trans_wind(3,1) + cmy(rn)*trans_wind(3,2)         &
                   + cmz(rn)*trans_wind(3,3)

!     torque taken opposite to aerodynamic z-moment in shaft axes
!     thrust taken as z-force in shaft axes;
!     Cp = Cq (Gessow & Meyers, p. 54, 3rd Printing), but power = torque*omega
!     Cpi0 = Cpi + Cp0 = induced + profile power = Cp - (-drag)*velocity


      cq(rn)   = -cmz_shaft(rn)
      cp(rn)   = cq(rn)
      cpi0(rn) = cp(rn) + cx_wind(rn)*advance_ratio(rn)
      ct(rn)   = cz_shaft(rn)

!     figure-of-merit (from Gessow & Meyers)

      if (abs(cq(rn)) > 0.0_dp) then
        fm(rn) = ct(rn)*sqrt(abs(ct(rn))/2.0_dp)/cq(rn)
      else
        fm(rn) = -999.0_dp
      end if

!     lift and drag are the z- and x-forces in the wind axes

      cl(rn) = cz_wind(rn)
      cd(rn) = cx_wind(rn)

!     solidity based on disk area

      solidity(rn) = solidity(rn)/(pi*rotor(rn)%rtip**2)

    end do

    if (lmpi_master) then
      do rn=1,nrotor
        f_factor = dim_force(rn)
        m_factor = dim_moment(rn)
        p_factor = dim_power(rn)
        write(*,*)
        write(*,'(a,i2)')    ' Rotor Forces and Moments, Rotor ', rn
        write(*,'(a,i5,a)')  ' Averages over ', nazimuth(rn), ' steps'
        write(*,'(a,f12.8)') ' For dimensional output: reference density  ',   &
                               ref_density
        write(*,'(a,f12.2)') '                         reference velocity ',   &
                               ref_velocity
        write(*,'(a,f12.6)') '                         reference length   ',   &
                               rotor(rn)%rtip
        write(*,'(a,f14.4)') '                         reference area   ',     &
                               pi*rotor(rn)%rtip**2
        write(*,'(a)')       '   Inertial Axes'
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cx  : ', cx(rn),  '     Fx   : ', cx(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cy  : ', cy(rn),  '     Fy   : ', cy(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cz  : ', cz(rn),  '     Fz   : ', cz(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cmx : ', cmx(rn), '     Mx   : ', cmx(rn)*m_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cmy : ', cmy(rn), '     My   : ', cmy(rn)*m_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cmz : ', cmz(rn), '     Mz   : ', cmz(rn)*m_factor
        write(*,'(a)')       '   Nonrotating Shaft Axes'
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cx  : ', cx_shaft(rn),  '     Fx   : ', cx_shaft(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cy  : ', cy_shaft(rn),  '     Fy   : ', cy_shaft(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cz  : ', cz_shaft(rn),  '     Fz   : ', cz_shaft(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cmx : ', cmx_shaft(rn), '     Mx   : ', cmx_shaft(rn)*m_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cmy : ', cmy_shaft(rn), '     My   : ', cmy_shaft(rn)*m_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cmz : ', cmz_shaft(rn), '     Mz   : ', cmz_shaft(rn)*m_factor
        write(*,'(a)')       '   Wind Axes'
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cl  : ', cl(rn), '     Lift : ', cl(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Cd  : ', cd(rn), '     Drag : ', cd(rn)*f_factor
        write(*,'(a)')       '   Performance Parameters'
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Thrust, Ct      : ', ct(rn),'   Thrust : ', ct(rn)*f_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Torque, Cq      : ', cq(rn),'   Torque : ', cq(rn)*m_factor
        write(*,'(a,f12.8,a,f13.2)')                                           &
        '     Power,  Cp      : ', cp(rn),'   Power  : ', cp(rn)*p_factor
        write(*,'(a,f12.8,a,f13.2,a)')                                         &
        '     Power,  Cpi+Cp0 : ', cpi0(rn),'   Power  : ',                    &
        cpi0(rn)*p_factor, ' (induced + profile)'
        write(*,'(a,f12.8)') '     Solidity, S     : ', solidity(rn)
        write(*,'(a,f12.8)') '     Ct/S            : ', ct(rn)/solidity(rn)
        write(*,'(a,f12.8)') '     Cq/S            : ', cq(rn)/solidity(rn)
        write(*,'(a,f12.8)') '     Cp/S            : ', cp(rn)/solidity(rn)
        write(*,'(a,f12.8,a)')                                                 &
                           '     Cpi0/S          : ', cpi0(rn)/solidity(rn),   &
                           ' (induced + profile)'
        write(*,'(a,f12.8)') '     Figure-of-Merit : ', fm(rn)
      end do
    end if

!   write some selected data out to a file for plotting/archiving - only do
!   this when we have collected and averaged data over a complete rotor rev

    do rn=1,nrotor
      if (have_360_data(rn)) then
        psi_in = rotor(rn)%psi1/conv   ! rad to deg
        call get_nrevs(psi_in, psi_out, nrevs)
        revs = real(nrevs,dp) + psi_out/360.0_dp
        if (lmpi_master) then
          call write_performance_file(revs, f_factor, m_factor, p_factor,      &
                                      solidity(rn), ct(rn), cq(rn), cp(rn),    &
                                      cpi0(rn), fm(rn))
        end if
      end if
    end do

!   don't average rotor forces/moments over more than one rev

    do rn=1,nrotor
      if (have_360_data(rn)) then
        nazimuth(rn) = 0
        cx(rn)       = 0.0_dp
        cy(rn)       = 0.0_dp
        cz(rn)       = 0.0_dp
        cmx(rn)      = 0.0_dp
        cmy(rn)      = 0.0_dp
        cmz(rn)      = 0.0_dp
      end if
    end do

  end subroutine rotor_performance


!================================ GET_POP_TRANSFORM ==========================80
!
! Gets the transform matrix needed to "pop" the rotor blades into a reasonable
! starting position, from a composite mesh in which the blades are positioned
! in a "generic" orientation. This allows reuse of the composite mesh (and
! initial dci file) for multiple cases. This is an abreviated version of the
! subroutine update_rotor_motion.
!
!=============================================================================80

  subroutine get_pop_transform()

    use rotors,              only : nrotor, rotor
    use grid_motion_helpers, only : get_inverse_transform
    use csd_coupling,        only : extract_rigid_from_elastic
    use fsi_coupling,        only : notsosimple_rigid_rotation
    use fsi_coupling_tight,  only : extract_rigid_from_elastic_fsi_tight

    use moving_body_types,   only : moving_body

    integer                     :: rn, blade, body

    real(dp), dimension(4,4)    :: matrixh, matrixh_static

    real(dp)                    :: psi_shaft

  continue

!   Blade motion

    body = 0

    rotor_loop : do rn = 1, nrotor

      rotor(rn)%psi1 = 0.0_dp

      psi_shaft = rotor(rn)%psi1

      blade_loop : do blade = 1, rotor(rn)%nblade

        body = body + 1

        select case(trim(comprehensive_rotor_coupling))

          case('camrad','rcas','rcas_tight')

            call extract_rigid_from_elastic(blade, rn, psi_shaft)

            call get_elastic_blade_transform(blade, rn, matrixh, psi_shaft,    &
                                             matrixh_static)

          case('fsi')

            call notsosimple_rigid_rotation(blade, rn, matrixh, psi_shaft)

          case('fsi_tight')

            call extract_rigid_from_elastic_fsi_tight(blade, rotor(rn)%dirrot)

            call get_elastic_blade_transform(blade, rn, matrixh, psi_shaft,    &
                                             matrixh_static)

          case default

            call get_rigid_blade_transform(blade, rn, matrixh, psi_shaft,      &
                                           matrixh_static)

        end select

!       set some things in the moving_body derived type

        moving_body(body)%transform_matrix(:,:) = matrixh(:,:)

        call get_inverse_transform(moving_body(body)%transform_matrix,         &
                                   moving_body(body)%inv_transform)

      end do blade_loop

    end do rotor_loop

  end subroutine get_pop_transform


!=========================== GET_BLADE_SLICE_TRANSFORM =======================80
!
!  Takes the rotor settings in rotor.input, plus the current shaft position
!  computes a transformation matrix (matrixh_slice) which takes the blade from
!  the t=0 frame (where slicing is done) to the current position
!
!=============================================================================80

  subroutine get_blade_slice_transform(blade, rn, psi_shaft, matrixh_slice)

    use rotors,              only : rotor
    use grid_motion_helpers, only : matrix_mult, translate_to_center
    use linear_algebra,      only : identity_matrix

    integer,                  intent(in)  :: blade, rn

    real(dp),                 intent(in)  :: psi_shaft
    real(dp), dimension(4,4), intent(out) :: matrixh_slice

    real(dp)                  :: pi, sn, sinalp, cosalp, psi_t0, psi, alpha0
    real(dp), dimension(3)    :: axisrotor, cenrotor
    real(dp), dimension(3)    :: axistilt,  centilt

  continue

    pi = acos(-1.0_dp)

!   rotor axis, for clockwise axis is -Z, for counter clockwise axis is Z

    sn =  1.0_dp                            ! counterclockwise rotation
    if (rotor(rn)%dirrot == 1) sn = -1.0_dp ! clockwise rotation

!   alpha0 is the tip angle of the TPP with respect to reference plane; assumes
!   that axis tilted in the xz-plane, taken from the phi2 input option; a
!   negative angle in the rotor.input file implies a tilt forward so negate here

    alpha0 = rotor(rn)%alpha0
    cosalp = cos(-1.0_dp*alpha0)
    sinalp = sin(-1.0_dp*alpha0)

!   shaft tilt axis - fore/aft tilt only - no side-to-side tilt

    axistilt(1) = 0.0_dp
    axistilt(2) = 1.0_dp
    axistilt(3) = 0.0_dp
    centilt(1)  = rotor(rn)%x0
    centilt(2)  = rotor(rn)%y0
    centilt(3)  = rotor(rn)%z0

!   rotor hub axis

    axisrotor(1) = -sinalp*sn
    axisrotor(2) = 0.0_dp
    axisrotor(3) = cosalp*sn
    cenrotor(1)  = rotor(rn)%x0
    cenrotor(2)  = rotor(rn)%y0
    cenrotor(3)  = rotor(rn)%z0

    psi_t0 = real(blade-1,dp)*(pi*2.0_dp) / real(rotor(rn)%nblade,dp)
    psi    = psi_shaft + psi_t0

    matrixh_slice = identity_matrix(4)

    call translate_to_center(   cenrotor,         matrixh_slice)
    call matrix_mult(axistilt,  centilt,  alpha0, matrixh_slice)
    call matrix_mult(axisrotor, cenrotor, psi,    matrixh_slice)

  end subroutine get_blade_slice_transform


!========================== PERFORMANCE_FILE_SETUP ==========================80
!
! Open file and write headers for the performance data history of a rotor
!
!=============================================================================80

  subroutine performance_file_setup(rn, simulation_time)

    use file_utils,        only : file_exists, available_unit

    integer,                intent(in)  :: rn

    real(dp),               intent(in)  :: simulation_time

    character(80) :: filename, tempstr

    continue

!   Check to see if rotor performance history file exists; open if needed

    write(tempstr,*) rn
    tempstr  = adjustl(tempstr)
    filename = 'performance_rotor_' // trim(tempstr) //'.dat'

    performance_unit = available_unit()

    if ( simulation_time > 0.0_dp .and. file_exists(filename) ) then
      open(performance_unit, file=filename, status='old', position='append')
      return
    else
      open(performance_unit, file=filename, status='unknown')
      rewind(performance_unit)
    end if

!   Write tecplot headers

    write(performance_unit,'(3a)') 'variables = "Rev", "Ct", "Cq", "Cp", ',    &
                        '"Cpi0", "Ct/S", "Cq/S" "Cp/S", "Cpi0/S", ',           &
                        '"Thrust", "Torque", "Power", "Poweri0", "FM"'
    write(performance_unit,'(a,i0,a)') 'zone t = "performance data for rotor ',&
                                      rn,'"'
    write(performance_unit,'(4a)')                                             &
                      '#  Rev           Ct           Cq           ',           &
                      'Cp         Cpi0         Ct/S         Cq/S         ',    &
                      'Cp/S       Cpi0/S      Thrust      Torque       ',      &
                      'Power     Poweri0           FM'

  end subroutine performance_file_setup


!============================= WRITE_PERFORMANCE_FILE ========================80
!
! Writes the performance data for the rotor to a file
!
!=============================================================================80

  subroutine write_performance_file(revs, f_factor, m_factor, p_factor, &
                                    solidity, ct, cq, cp, cpi0, fm)

    real(dp), intent(in) :: revs, f_factor, m_factor, p_factor,solidity,       &
                            ct, cq, cp, cpi0, fm

    continue

    write(performance_unit,'(f6.2,8(1x,f12.8),4(1x,f11.2),1x,f12.8)')          &
         revs, ct, cq, cp, cpi0, ct/solidity, cq/solidity, cp/solidity,        &
         cpi0/solidity, ct*f_factor, cq*m_factor, cp*p_factor, cpi0*p_factor,  &
         fm

  end subroutine write_performance_file


!==================================== WRITE_SURF =============================80
!
! Writes a Tecplot file for the surface mesh; will append title line with time
! level if requested
!
! Note: connectivity data is not written out, so the resulting mesh will appear
! as a disconnected 'cloud' of points when viewed in tecplot
!
!=============================================================================80

  subroutine write_surf(surface_mesh, project_name, body, ntt, timestep)

    use grid_types,        only : mass_type
    use system_extensions, only : se_open

    character(80),      intent(in)    :: project_name
    type(mass_type),    intent(inout) :: surface_mesh
    integer,  optional, intent(in)    :: body, ntt
    real(dp), optional, intent(in)    :: timestep

    integer ::  i, iunit

    character(80) :: filename, tempname, string1, string2

  continue

    iunit = 80

    filename = trim(project_name)

    if (present(body)) then
      tempname = trim(filename) // '_body'
      write(string1,*) body
      filename = trim(tempname) // trim(adjustl(string1))
    end if

    if (present(ntt)) then
      tempname = trim(filename) // '_timestep'
      write(string2,*) ntt
      filename = trim(tempname)  //  trim(adjustl(string2))
    end if

    filename = trim(filename) // '.tec'

    call se_open(iunit, file=filename)

    rewind(iunit)

    if ( present(timestep) ) then
      write(iunit,'(a,e19.12,a)')'title="' // 'massoud' // &
                                              ' time = ',timestep,'"'
    else
      write(iunit,'(a,e19.12)')'title="' // 'massooud' // '"'
    end if

    write(iunit,'(a)') 'variables = "x" "y" "z" "id"'
    write(iunit,'("zone t = group0, I = ",i6," ")') surface_mesh%itotal

    do i=1,surface_mesh%itotal
      write(iunit,"(3e20.12,i10,3e20.10)") surface_mesh%xmt(i),        &
                                           surface_mesh%ymt(i),        &
                                           surface_mesh%zmt(i),        &
                                           surface_mesh%inodemt(i)
    end do

    close(iunit)

  end subroutine write_surf


end  module rotor_motion




!==================================== MINMODS ================================80
!
! MinMod limiter applied to a scalar
!
!=============================================================================80

  pure function minmods(grad_a, grad_b)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_half, my_1

    real(dp), intent(in) :: grad_a, grad_b
    real(dp)             :: minmods

  continue

    minmods = my_half*(sign(my_1, grad_a) + sign(my_1, grad_b)) *              &
                       min(abs(grad_a),abs(grad_b))

  end function minmods

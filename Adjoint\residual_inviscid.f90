module residual_inviscid

  use kinddefs, only : dp

  implicit none

  private

  public :: atlam_all
  public :: atlam_roe
  public :: atlam_roei
  public :: atlam_vl

  type neighbor_type
    integer :: n
    integer, dimension(:), pointer :: list
  end type neighbor_type

  type(neighbor_type), dimension(:), allocatable :: neighbors

  logical :: neighbors_found = .false.

contains

!================================ ATLAM_ALL ==================================80
!
!  This does A-transpose times lambda at time-level n
!  for part of the residual (dI/dQ is the other part)
!
!  Puts the result in res
!
!  All flux flavors
!
!  Expects qnode in PRIMITIVE
!  Returns qnode in PRIMITIVE
!
!=============================================================================80

  subroutine atlam_all(nnodes0,nnodes01,                                       &
                       x,y,z,                                                  &
                       symmetry,rr11,rr22,rr33,rr12,rr13,rr23,                 &
                       n_tot,n_grd,qnode,gradx,grady,gradz,phi,                &
                       adim,nfunctions,coltag,rlam,res,                        &
                       nedge,nedgeloc,eptr,rarea,xn,yn,zn,facespeed,           &
                       ia,ia_ns,ja)

    use kinddefs,              only : dp
    use inviscid_flux,         only : first_order_iterations
    use debug_defs,            only : symmetry_bcs
    use info_depr,             only : ntt,kappa_umuscl
    use inviscid_flux,         only : flux_construction
    use reconstruction,        only : lstgs_sym
    use thermo,                only : dprimitive_dconserved
    use flux_functions,        only : vanleer_primitive_jacobian,              &
                                      roe_primitive_jacobian
    use grid_motion_helpers,   only : need_grid_velocity

    integer, intent(in) :: nnodes0,nnodes01,nfunctions
    integer, intent(in) :: nedge,nedgeloc, n_tot, adim, n_grd

    real(dp), dimension(nnodes01),                  intent(in)    :: x,y,z

    integer, dimension(nnodes01),                   intent(in)    :: symmetry
    real(dp), dimension(nnodes0),                   intent(in)    :: rr11
    real(dp), dimension(nnodes0),                   intent(in)    :: rr22
    real(dp), dimension(nnodes0),                   intent(in)    :: rr33
    real(dp), dimension(nnodes0),                   intent(in)    :: rr12
    real(dp), dimension(nnodes0),                   intent(in)    :: rr13
    real(dp), dimension(nnodes0),                   intent(in)    :: rr23

    real(dp), dimension(n_tot,nnodes01),            intent(in)    :: qnode
    real(dp), dimension(n_grd,nnodes01),            intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),            intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),            intent(in)    :: gradz
    real(dp), dimension(n_grd,nnodes01),            intent(in)    :: phi

    real(dp), dimension(adim,nnodes01),             intent(in)    :: coltag
    real(dp), dimension(adim,nnodes01, nfunctions), intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01, nfunctions), intent(inout) :: res

    integer,  dimension(2,nedge),                   intent(in)    :: eptr
    real(dp), dimension(nedge),                     intent(in)    :: rarea
    real(dp), dimension(nedge),                     intent(in)    :: xn,yn,zn
    real(dp), dimension(nedgeloc),                  intent(in)    :: facespeed

    integer, dimension(:),             intent(in) :: ia, ia_ns, ja

    integer :: n, node, node1, node2, i, j, k, ii
    integer, parameter :: n_cont = 5

    real(dp)    :: second, face_speed
    real(dp)    :: area, xmean, ymean, zmean
    real(dp)    :: rx, ry, rz
    real(dp)    :: dx, dy, dz

    real(dp), dimension(3)   :: terms, normal
    real(dp), dimension(5)   :: state1, state2
    real(dp), dimension(5,5) :: df_dstate1, df_dstate2
    real(dp), dimension(5,5) :: dQdq, dres
    real(dp), dimension(5,5) :: dfp_primitive_times_weights
    real(dp), dimension(5,5) :: dfm_primitive_times_weights
    real(dp), dimension(5,5) :: M1_inverse, M2_inverse
    real(dp), dimension(5,5) :: dR1_dQ1, dR1_dQ2
    real(dp), dimension(5,5) :: dR2_dQ1, dR2_dQ2
    real(dp), dimension(nfunctions) :: Rlam1, Rlam2, Rlam3, Rlam4, Rlam5

    real(dp), parameter    :: my_0   = 0.0_dp
    real(dp), parameter    :: zero   = 0.0_dp
    real(dp), parameter    :: second_ord_limit = 0.01_dp
    real(dp), parameter    :: my_haf = 0.5_dp
    real(dp), parameter    :: my_1   = 1.0_dp

    real(dp), dimension(5)      :: weight_matrix
    real(dp), dimension(n_cont) :: swx, swy, swz

    logical :: state1_firstorder, state2_firstorder

  continue

    second = my_1
    if ( ntt <= first_order_iterations ) second = my_0

    edge_loop : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      edge_has_local_node : if ( node1 <= nnodes0 .or. node2 <= nnodes0 ) then

! Get unit normals and area

        normal(1) = xn(n)
        normal(2) = yn(n)
        normal(3) = zn(n)
        area  = rarea(n)

        face_speed = my_0
        if ( need_grid_velocity ) face_speed = facespeed(n)

        xmean = my_haf*(x(node1) + x(node2))
        ymean = my_haf*(y(node1) + y(node2))
        zmean = my_haf*(z(node1) + z(node2))

! Get variables on "left" side of face

        rx = second*(xmean - x(node1))
        ry = second*(ymean - y(node1))
        rz = second*(zmean - z(node1))

        state1_firstorder = .false.

        state1 = qnode(:,node1)                                                &
               + phi(:,node1)*((my_1-kappa_umuscl)*gradx(:,node1)*rx           &
               +               (my_1-kappa_umuscl)*grady(:,node1)*ry           &
               +               (my_1-kappa_umuscl)*gradz(:,node1)*rz           &
               + second*kappa_umuscl*my_haf*(qnode(:,node2)-qnode(:,node1)))

! Catastrophic limiter
! Note the derivatives do not change

        left_catastrophe : if (state1(1) <= my_0 .or. state1(5) <= my_0) then
          state1_firstorder = .true.
          state1 = qnode(:,node1)
        end if left_catastrophe

! Get variables on "right" side of face

        rx = second*(xmean - x(node2))
        ry = second*(ymean - y(node2))
        rz = second*(zmean - z(node2))

        state2_firstorder = .false.

        state2 = qnode(:,node2)                                                &
               + phi(:,node2)*((my_1-kappa_umuscl)*gradx(:,node2)*rx           &
               +               (my_1-kappa_umuscl)*grady(:,node2)*ry           &
               +               (my_1-kappa_umuscl)*gradz(:,node2)*rz           &
               + second*kappa_umuscl*my_haf*(qnode(:,node1)-qnode(:,node2)))

        right_catastrophe : if (state2(1) <= my_0 .or. state2(5) <= my_0) then
          state2_firstorder = .true.
          state2 = qnode(:,node2)
        end if right_catastrophe

        select case (flux_construction)
        case ('vanleer')
          call vanleer_primitive_jacobian(state1,state2,normal,area,face_speed,&
            df_dstate1,df_dstate2)
        case ('roe')
          call roe_primitive_jacobian(state1,state2,normal,area,face_speed,&
            df_dstate1,df_dstate2)
        case default
          df_dstate1 = zero
          df_dstate2 = zero
        end select

        if(node1 <= nnodes0) then
          call dprimitive_dconserved( qnode(:,node1), dQdq)
          dres = matmul( df_dstate1, dQdq )

          do i = 1, 5
            do j = 1, 5
              do k = 1, nfunctions
                res(j,node1,k) = res(j,node1,k)               &
                  + dres(i,j)*coltag(i,node1)*rlam(i,node1,k) &
                  - dres(i,j)*coltag(i,node2)*rlam(i,node2,k)
              end do
            end do
          end do
        endif

        if(node2 <= nnodes0) then
          call dprimitive_dconserved( qnode(:,node2), dQdq)
          dres = matmul( df_dstate2, dQdq )

          do i = 1, 5
            do j = 1, 5
              do k = 1, nfunctions
                res(j,node2,k) = res(j,node2,k)               &
                  + dres(i,j)*coltag(i,node1)*rlam(i,node1,k) &
                  - dres(i,j)*coltag(i,node2)*rlam(i,node2,k)
              end do
            end do
          end do
        endif

! At this point, we have first order accuracy.  Now add contribution
! from second order terms.  We have taken into account the constant
! term in the reconstruction.  Now we need to add the pieces from the
! gradient terms.

      second_order_contribs : if( second > second_ord_limit) then

! Take care of the circuit of nodes around node1

        node1_second_order : if( node1 <= nnodes0 .and. &
                                (.not. state1_firstorder) ) then

          rx = second*(xmean - x(node1))
          ry = second*(ymean - y(node1))
          rz = second*(zmean - z(node1))

          node1_orbit : do ii = ia(node1),ia_ns(node1)-1

            node = ja(ii) ! This is the node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

            dx = x(node) - x(node1)
            dy = y(node) - y(node1)
            dz = z(node) - z(node1)

            terms(:) = lstgs_func(dx,          dy,          dz,                &
                                  rr11(node1), rr12(node1), rr13(node1),       &
                                  rr22(node1), rr23(node1), rr33(node1))
            swx(:) = terms(1)
            swy(:) = terms(2)
            swz(:) = terms(3)

            if (symmetry_bcs) then
              call lstgs_sym(symmetry(node1),                                  &
                             dx,          dy,          dz,                     &
                             rr11(node1), rr12(node1), rr13(node1),            &
                             rr22(node1), rr23(node1), rr33(node1),            &
                             n_cont, swx, swy, swz )
            end if

            if( node == node1 )then
              swx = my_0
              swy = my_0
              swz = my_0
            end if

            weight_matrix(1) = phi(1,node1)*(my_1-kappa_umuscl)                &
                                           *(swx(1)*rx+swy(1)*ry+swz(1)*rz)
            weight_matrix(2) = phi(2,node1)*(my_1-kappa_umuscl)                &
                                           *(swx(2)*rx+swy(2)*ry+swz(2)*rz)
            weight_matrix(3) = phi(3,node1)*(my_1-kappa_umuscl)                &
                                           *(swx(3)*rx+swy(3)*ry+swz(3)*rz)
            weight_matrix(4) = phi(4,node1)*(my_1-kappa_umuscl)                &
                                           *(swx(4)*rx+swy(4)*ry+swz(4)*rz)
            weight_matrix(5) = phi(5,node1)*(my_1-kappa_umuscl)                &
                                           *(swx(5)*rx+swy(5)*ry+swz(5)*rz)

            call dprimitive_dconserved( qnode(:,node1), dQdq)
            dres = matmul( df_dstate1, dQdq )

            do i = 1, 5
              do j = 1, 5
                do k = 1, nfunctions
                  res(j,node1,k) = res(j,node1,k)                            &
                - weight_matrix(j)*dres(i,j)*coltag(i,node1)*rlam(i,node1,k) &
                + weight_matrix(j)*dres(i,j)*coltag(i,node2)*rlam(i,node2,k)
                end do
              end do
            end do

            call dprimitive_dconserved( qnode(:,node), dQdq)
            dres = matmul( df_dstate1, dQdq )

            do i = 1, 5
              do j = 1, 5
                do k = 1, nfunctions
                  res(j,node,k) = res(j,node,k)                              &
                + weight_matrix(j)*dres(i,j)*coltag(i,node1)*rlam(i,node1,k) &
                - weight_matrix(j)*dres(i,j)*coltag(i,node2)*rlam(i,node2,k)
                end do
              end do
            end do
          end do node1_orbit

        end if node1_second_order

! Take care of the circuit of nodes around node2

        node2_second_order : if( node2 <= nnodes0 .and. &
                                (.not. state2_firstorder) ) then

          rx = second*(xmean - x(node2))
          ry = second*(ymean - y(node2))
          rz = second*(zmean - z(node2))

          node2_orbit : do ii = ia(node2),ia_ns(node2)-1

            node = ja(ii) ! This is the node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

            dx = x(node) - x(node2)
            dy = y(node) - y(node2)
            dz = z(node) - z(node2)

            terms(:) = lstgs_func(dx,          dy,          dz,                &
                                  rr11(node2), rr12(node2), rr13(node2),       &
                                  rr22(node2), rr23(node2), rr33(node2))
            swx(:) = terms(1)
            swy(:) = terms(2)
            swz(:) = terms(3)

            if (symmetry_bcs) then
              call lstgs_sym(symmetry(node2),                                  &
                             dx,          dy,          dz,                     &
                             rr11(node2), rr12(node2), rr13(node2),            &
                             rr22(node2), rr23(node2), rr33(node2),            &
                             n_cont, swx, swy, swz )
            end if

            if( node == node2 )then
              swx = my_0
              swy = my_0
              swz = my_0
            end if

            weight_matrix(1) = phi(1,node2)*(my_1-kappa_umuscl)                &
                                           *(swx(1)*rx+swy(1)*ry+swz(1)*rz)
            weight_matrix(2) = phi(2,node2)*(my_1-kappa_umuscl)                &
                                           *(swx(2)*rx+swy(2)*ry+swz(2)*rz)
            weight_matrix(3) = phi(3,node2)*(my_1-kappa_umuscl)                &
                                           *(swx(3)*rx+swy(3)*ry+swz(3)*rz)
            weight_matrix(4) = phi(4,node2)*(my_1-kappa_umuscl)                &
                                           *(swx(4)*rx+swy(4)*ry+swz(4)*rz)
            weight_matrix(5) = phi(5,node2)*(my_1-kappa_umuscl)                &
                                           *(swx(5)*rx+swy(5)*ry+swz(5)*rz)

            call dprimitive_dconserved( qnode(:,node2), dQdq)
            dres = matmul( df_dstate2, dQdq )

            do i = 1, 5
              do j = 1, 5
                do k = 1, nfunctions
                  res(j,node2,k) = res(j,node2,k)                            &
                - weight_matrix(j)*dres(i,j)*coltag(i,node1)*rlam(i,node1,k) &
                + weight_matrix(j)*dres(i,j)*coltag(i,node2)*rlam(i,node2,k)
                end do
              end do
            end do

            call dprimitive_dconserved( qnode(:,node), dQdq)
            dres = matmul( df_dstate2, dQdq )

            do i = 1, 5
              do j = 1, 5
                do k = 1, nfunctions
                  res(j,node,k) = res(j,node,k)                              &
                + weight_matrix(j)*dres(i,j)*coltag(i,node1)*rlam(i,node1,k) &
                - weight_matrix(j)*dres(i,j)*coltag(i,node2)*rlam(i,node2,k)
                end do
              end do
            end do
          end do node2_orbit

        end if node2_second_order

! Finally pick up the other piece of the UMUSCL term along the edge
! These could be collapsed a lot, but its nice to see where each piece
! explicitly comes from

! First lets do dR1/dQ1

! contributions from left side

        weight_matrix(1) = phi(1,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(2) = phi(2,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(3) = phi(3,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(4) = phi(4,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(5) = phi(5,node1)*kappa_umuscl*my_haf*(-1.0_dp)

        do i = 1, 5
          do j = 1, 5
            dfp_primitive_times_weights(i,j)=df_dstate1(i,j)*weight_matrix(j)
          end do
        end do

        M1_inverse = setup_t(qnode(1,node1),qnode(2,node1),qnode(3,node1),     &
                             qnode(4,node1))

        dR1_dQ1 = 0.0_dp

        do i = 1, 5
          do j = 1, 5
            do k = 1, 5
              dR1_dQ1(i,j) = dR1_dQ1(i,j)                                      &
                              + dfp_primitive_times_weights(i,k)*M1_inverse(k,j)
            end do
          end do
        end do

! contributions from right side

        weight_matrix(1) = phi(1,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(2) = phi(2,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(3) = phi(3,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(4) = phi(4,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(5) = phi(5,node1)*kappa_umuscl*my_haf*(1.0_dp)

        do i = 1, 5
          do j = 1, 5
            dfm_primitive_times_weights(i,j)=df_dstate2(i,j)*weight_matrix(j)
          end do
        end do

        do i = 1, 5
          do j = 1, 5
            do k = 1, 5
              dR1_dQ1(i,j) = dR1_dQ1(i,j)                                      &
                              + dfm_primitive_times_weights(i,k)*M1_inverse(k,j)
            end do
          end do
        end do

! Note that dR2/dQ1 just has the sign flipped

        dR2_dQ1 = -dR1_dQ1

! Now lets do dR1/dQ2

! contributions from left side

        weight_matrix(1) = phi(1,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(2) = phi(2,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(3) = phi(3,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(4) = phi(4,node1)*kappa_umuscl*my_haf*(1.0_dp)
        weight_matrix(5) = phi(5,node1)*kappa_umuscl*my_haf*(1.0_dp)

        do i = 1, 5
          do j = 1, 5
            dfp_primitive_times_weights(i,j)=df_dstate1(i,j)*weight_matrix(j)
          end do
        end do

        M2_inverse = setup_t(qnode(1,node2),qnode(2,node2),qnode(3,node2),     &
                             qnode(4,node2))

        dR1_dQ2 = 0.0_dp

        do i = 1, 5
          do j = 1, 5
            do k = 1, 5
              dR1_dQ2(i,j) = dR1_dQ2(i,j)                                      &
                              + dfp_primitive_times_weights(i,k)*M2_inverse(k,j)
            end do
          end do
        end do

! contributions from right side

        weight_matrix(1) = phi(1,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(2) = phi(2,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(3) = phi(3,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(4) = phi(4,node1)*kappa_umuscl*my_haf*(-1.0_dp)
        weight_matrix(5) = phi(5,node1)*kappa_umuscl*my_haf*(-1.0_dp)

        do i = 1, 5
          do j = 1, 5
            dfm_primitive_times_weights(i,j)=df_dstate2(i,j)*weight_matrix(j)
          end do
        end do

        do i = 1, 5
          do j = 1, 5
            do k = 1, 5
              dR1_dQ2(i,j) = dR1_dQ2(i,j)                                      &
                              + dfm_primitive_times_weights(i,k)*M2_inverse(k,j)
            end do
          end do
        end do

! Note that dR2/dQ2 just has the sign flipped

        dR2_dQ2 = -dR1_dQ2

! Now that we have the little blocks, we can transpose and multiply by lambda
! and send to the residual

        do i = 1, nfunctions
          rlam1(i) = coltag(1,node1)*Rlam(1,node1,i)
          rlam2(i) = coltag(2,node1)*Rlam(2,node1,i)
          rlam3(i) = coltag(3,node1)*Rlam(3,node1,i)
          rlam4(i) = coltag(4,node1)*Rlam(4,node1,i)
          rlam5(i) = coltag(5,node1)*Rlam(5,node1,i)

          if ( node1 <= nnodes0 ) then
            res(1,node1,i) = res(1,node1,i) + dR1_dQ1(1,1)*Rlam1(i)         &
                                            + dR1_dQ1(2,1)*Rlam2(i)         &
                                            + dR1_dQ1(3,1)*Rlam3(i)         &
                                            + dR1_dQ1(4,1)*Rlam4(i)         &
                                            + dR1_dQ1(5,1)*Rlam5(i)

            res(2,node1,i) = res(2,node1,i) + dR1_dQ1(1,2)*Rlam1(i)         &
                                            + dR1_dQ1(2,2)*Rlam2(i)         &
                                            + dR1_dQ1(3,2)*Rlam3(i)         &
                                            + dR1_dQ1(4,2)*Rlam4(i)         &
                                            + dR1_dQ1(5,2)*Rlam5(i)

            res(3,node1,i) = res(3,node1,i) + dR1_dQ1(1,3)*Rlam1(i)         &
                                            + dR1_dQ1(2,3)*Rlam2(i)         &
                                            + dR1_dQ1(3,3)*Rlam3(i)         &
                                            + dR1_dQ1(4,3)*Rlam4(i)         &
                                            + dR1_dQ1(5,3)*Rlam5(i)

            res(4,node1,i) = res(4,node1,i) + dR1_dQ1(1,4)*Rlam1(i)         &
                                            + dR1_dQ1(2,4)*Rlam2(i)         &
                                            + dR1_dQ1(3,4)*Rlam3(i)         &
                                            + dR1_dQ1(4,4)*Rlam4(i)         &
                                            + dR1_dQ1(5,4)*Rlam5(i)

            res(5,node1,i) = res(5,node1,i) + dR1_dQ1(1,5)*Rlam1(i)         &
                                            + dR1_dQ1(2,5)*Rlam2(i)         &
                                            + dR1_dQ1(3,5)*Rlam3(i)         &
                                            + dR1_dQ1(4,5)*Rlam4(i)         &
                                            + dR1_dQ1(5,5)*Rlam5(i)
          endif

          if ( node2 <= nnodes0 ) then
            res(1,node2,i) = res(1,node2,i) + dR1_dQ2(1,1)*Rlam1(i)         &
                                            + dR1_dQ2(2,1)*Rlam2(i)         &
                                            + dR1_dQ2(3,1)*Rlam3(i)         &
                                            + dR1_dQ2(4,1)*Rlam4(i)         &
                                            + dR1_dQ2(5,1)*Rlam5(i)

            res(2,node2,i) = res(2,node2,i) + dR1_dQ2(1,2)*Rlam1(i)         &
                                            + dR1_dQ2(2,2)*Rlam2(i)         &
                                            + dR1_dQ2(3,2)*Rlam3(i)         &
                                            + dR1_dQ2(4,2)*Rlam4(i)         &
                                            + dR1_dQ2(5,2)*Rlam5(i)

            res(3,node2,i) = res(3,node2,i) + dR1_dQ2(1,3)*Rlam1(i)         &
                                            + dR1_dQ2(2,3)*Rlam2(i)         &
                                            + dR1_dQ2(3,3)*Rlam3(i)         &
                                            + dR1_dQ2(4,3)*Rlam4(i)         &
                                            + dR1_dQ2(5,3)*Rlam5(i)

            res(4,node2,i) = res(4,node2,i) + dR1_dQ2(1,4)*Rlam1(i)         &
                                            + dR1_dQ2(2,4)*Rlam2(i)         &
                                            + dR1_dQ2(3,4)*Rlam3(i)         &
                                            + dR1_dQ2(4,4)*Rlam4(i)         &
                                            + dR1_dQ2(5,4)*Rlam5(i)

            res(5,node2,i) = res(5,node2,i) + dR1_dQ2(1,5)*Rlam1(i)         &
                                            + dR1_dQ2(2,5)*Rlam2(i)         &
                                            + dR1_dQ2(3,5)*Rlam3(i)         &
                                            + dR1_dQ2(4,5)*Rlam4(i)         &
                                            + dR1_dQ2(5,5)*Rlam5(i)
          endif

          rlam1(i) = coltag(1,node2)*Rlam(1,node2,i)
          rlam2(i) = coltag(2,node2)*Rlam(2,node2,i)
          rlam3(i) = coltag(3,node2)*Rlam(3,node2,i)
          rlam4(i) = coltag(4,node2)*Rlam(4,node2,i)
          rlam5(i) = coltag(5,node2)*Rlam(5,node2,i)

          if ( node1 <= nnodes0 ) then
            res(1,node1,i) = res(1,node1,i) + dR2_dQ1(1,1)*Rlam1(i)         &
                                            + dR2_dQ1(2,1)*Rlam2(i)         &
                                            + dR2_dQ1(3,1)*Rlam3(i)         &
                                            + dR2_dQ1(4,1)*Rlam4(i)         &
                                            + dR2_dQ1(5,1)*Rlam5(i)

            res(2,node1,i) = res(2,node1,i) + dR2_dQ1(1,2)*Rlam1(i)         &
                                            + dR2_dQ1(2,2)*Rlam2(i)         &
                                            + dR2_dQ1(3,2)*Rlam3(i)         &
                                            + dR2_dQ1(4,2)*Rlam4(i)         &
                                            + dR2_dQ1(5,2)*Rlam5(i)

            res(3,node1,i) = res(3,node1,i) + dR2_dQ1(1,3)*Rlam1(i)         &
                                            + dR2_dQ1(2,3)*Rlam2(i)         &
                                            + dR2_dQ1(3,3)*Rlam3(i)         &
                                            + dR2_dQ1(4,3)*Rlam4(i)         &
                                            + dR2_dQ1(5,3)*Rlam5(i)

            res(4,node1,i) = res(4,node1,i) + dR2_dQ1(1,4)*Rlam1(i)         &
                                            + dR2_dQ1(2,4)*Rlam2(i)         &
                                            + dR2_dQ1(3,4)*Rlam3(i)         &
                                            + dR2_dQ1(4,4)*Rlam4(i)         &
                                            + dR2_dQ1(5,4)*Rlam5(i)

            res(5,node1,i) = res(5,node1,i) + dR2_dQ1(1,5)*Rlam1(i)         &
                                            + dR2_dQ1(2,5)*Rlam2(i)         &
                                            + dR2_dQ1(3,5)*Rlam3(i)         &
                                            + dR2_dQ1(4,5)*Rlam4(i)         &
                                            + dR2_dQ1(5,5)*Rlam5(i)
          endif

          if ( node2 <= nnodes0 ) then
            res(1,node2,i) = res(1,node2,i) + dR2_dQ2(1,1)*Rlam1(i)         &
                                            + dR2_dQ2(2,1)*Rlam2(i)         &
                                            + dR2_dQ2(3,1)*Rlam3(i)         &
                                            + dR2_dQ2(4,1)*Rlam4(i)         &
                                            + dR2_dQ2(5,1)*Rlam5(i)

            res(2,node2,i) = res(2,node2,i) + dR2_dQ2(1,2)*Rlam1(i)         &
                                            + dR2_dQ2(2,2)*Rlam2(i)         &
                                            + dR2_dQ2(3,2)*Rlam3(i)         &
                                            + dR2_dQ2(4,2)*Rlam4(i)         &
                                            + dR2_dQ2(5,2)*Rlam5(i)

            res(3,node2,i) = res(3,node2,i) + dR2_dQ2(1,3)*Rlam1(i)         &
                                            + dR2_dQ2(2,3)*Rlam2(i)         &
                                            + dR2_dQ2(3,3)*Rlam3(i)         &
                                            + dR2_dQ2(4,3)*Rlam4(i)         &
                                            + dR2_dQ2(5,3)*Rlam5(i)

            res(4,node2,i) = res(4,node2,i) + dR2_dQ2(1,4)*Rlam1(i)         &
                                            + dR2_dQ2(2,4)*Rlam2(i)         &
                                            + dR2_dQ2(3,4)*Rlam3(i)         &
                                            + dR2_dQ2(4,4)*Rlam4(i)         &
                                            + dR2_dQ2(5,4)*Rlam5(i)

            res(5,node2,i) = res(5,node2,i) + dR2_dQ2(1,5)*Rlam1(i)         &
                                            + dR2_dQ2(2,5)*Rlam2(i)         &
                                            + dR2_dQ2(3,5)*Rlam3(i)         &
                                            + dR2_dQ2(4,5)*Rlam4(i)         &
                                            + dR2_dQ2(5,5)*Rlam5(i)
          endif
        end do


      end if second_order_contribs

      end if edge_has_local_node

    enddo edge_loop

  end subroutine atlam_all

!================================ ATLAM_ROE ==================================80
!
!  This does A-transpose times lambda at time-level n
!  for part of the residual (dI/dQ is the other part)
!
!  Puts the result in res
!
!  Roe
!
!  Expects qnode in PRIMITIVE
!  Returns qnode in PRIMITIVE
!
!=============================================================================80
  subroutine atlam_roe(nnodes0,nnodes01,x,y,z,symmetry,rr11,rr22,rr33,rr12,    &
                       rr13,rr23,qnode,gradx,grady,gradz,nedge,nedgeloc,eptr,  &
                       rarea,xn,yn,zn,ia,ja,phi,n_tot,adim,n_grd,facespeed,    &
                       nfunctions,ad,res,rlam,coltag,fhelp,iau,a)

    use kinddefs,            only : dp
    use inviscid_flux,       only : first_order_iterations
    use fluid,               only : gm1
    use debug_defs,          only : symmetry_bcs
    use info_depr,           only : ntt,kappa_umuscl
    use reconstruction,      only : lstgs_sym
    use adjoint_switches,    only : rn, np
    use design_types,        only : max_functions
    use lmpi,                only : lmpi_master, lmpi_die
    use grid_motion_helpers, only : need_grid_velocity

    integer, intent(in) :: nnodes0,nnodes01
    integer, intent(in) :: nedge,nedgeloc, n_tot, adim, n_grd
    integer, intent(in), optional :: nfunctions

    integer, dimension(nnodes01), intent(in) :: symmetry
    integer, dimension(2,nedge),  intent(in) :: eptr
    integer, dimension(:),        intent(in) :: ia, ja
    integer, dimension(:),        intent(in), optional :: iau
    integer, dimension(:,:),      intent(in), optional :: fhelp

    real(dp), dimension(nnodes01),       intent(in) :: x,y,z
    real(dp), dimension(nnodes0),        intent(in) :: rr11
    real(dp), dimension(nnodes0),        intent(in) :: rr22
    real(dp), dimension(nnodes0),        intent(in) :: rr33
    real(dp), dimension(nnodes0),        intent(in) :: rr12
    real(dp), dimension(nnodes0),        intent(in) :: rr13
    real(dp), dimension(nnodes0),        intent(in) :: rr23
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(n_grd,nnodes01), intent(in) :: gradx
    real(dp), dimension(n_grd,nnodes01), intent(in) :: grady
    real(dp), dimension(n_grd,nnodes01), intent(in) :: gradz
    real(dp), dimension(n_grd,nnodes01), intent(in) :: phi
    real(dp), dimension(nedge),          intent(in) :: rarea
    real(dp), dimension(nedge),          intent(in) :: xn,yn,zn
    real(dp), dimension(nedgeloc),       intent(in) :: facespeed
    real(dp), dimension(adim,nnodes01),  intent(in)   , optional::coltag
    real(dp), dimension(5,5),            intent(inout), optional::ad
    real(dp), dimension(:,:,:),          intent(in)   , optional::rlam
    real(dp), dimension(:,:,:),          intent(inout), optional::res
    real(dp), dimension(:,:,:),          intent(inout), optional::a

    real(dp) :: dfp(5,5),dfm(5,5),t(5,5),ti(5,5)
    real(dp) :: dfp_primitive(5,5),dfm_primitive(5,5)
    real(dp) :: B(5,5),CC(5,5),tag(2,5)
    real(dp) :: dR1_dQ1(5,5), dR1_dQ2(5,5)
    real(dp) :: dR2_dQ1(5,5), dR2_dQ2(5,5)

    integer :: n, ii, node, node1, node2, i, j, k, iii
    integer :: idiag1, idiag2, ioff12, ioff21, ioff1k, jstart, jend, neighbor
    integer :: ioff2k, m
    integer, parameter :: n_cont = 5

    real(dp) :: second, face_speed, rx, ry, rz
    real(dp) :: xnorm, ynorm, znorm, area, xmean, ymean, zmean

    real(dp) :: rhol, rholrL, ul, uluL, vl, vlvL, wl, wlwL, q2l, q2luL, q2lvL
    real(dp) :: q2lwL, pressl, presslpL, enrgyl, enrgylrL, enrgyluL, enrgylvL
    real(dp) :: enrgylwL, enrgylpL, Hl, HlrL, HluL, HlvL, HlwL, HlpL, ubarl
    real(dp) :: ubarluL, ubarlvL, ubarlwL, rhor, rhorrR, ur, uruR, vr, vrvR
    real(dp) :: wr, wrwR, q2r, q2ruR, q2rvR, q2rwR, pressr, pressrpR, enrgyr
    real(dp) :: enrgyrrR, enrgyruR, enrgyrvR, enrgyrwR, enrgyrpR, Hr, HrrR
    real(dp) :: HruR, HrvR, HrwR, HrpR, ubarr, ubarruR, ubarrvR, ubarrwR
    real(dp) :: rho, rhorL, rhorR, wat, watrL, watrR, u, urL, uuL, urR, uuR
    real(dp) :: v, vrL, vvL, vrR, vvR, w, wrL, wwL, wrR, wwR, H, HrL, HuL
    real(dp) :: HvL, HwL, HpL, HrR, HuR, HvR, HwR, HpR, q2, q2rL, q2uL, q2vL
    real(dp) :: q2wL, q2rR, q2uR, q2vR, q2wR, c, crL, cuL, cvL, cwL, cpL
    real(dp) :: crR, cuR, cvR, cwR, cpR, ubar, ubarrL, ubaruL, ubarvL, ubarwL
    real(dp) :: ubarrR, ubaruR, ubarvR, ubarwR, eig1, eig1rL, eig1uL, eig1vL
    real(dp) :: eig1wL, eig1pL, eig1rR, eig1uR, eig1vR, eig1wR, eig1pR
    real(dp) :: eig2, eig2rL, eig2uL, eig2vL, eig2wL, eig2pL, eig2rR, eig2uR
    real(dp) :: eig2vR, eig2wR, eig2pR, eig3, eig3rL, eig3uL, eig3vL, eig3wL
    real(dp) :: eig3rR, eig3uR, eig3vR, eig3wR
    real(dp) :: drho, drhorL, drhorR, dpress, dpresspL, dpresspR, du, duuL
    real(dp) :: duuR, dv, dvvL, dvvR, dw, dwwL, dwwR, dubar, dubaruL, dubarvL
    real(dp) :: dubarwL, dubaruR, dubarvR, dubarwR, c2, c2rL, c2uL, c2vL, c2wL
    real(dp) :: c2pL, c2rR, c2uR, c2vR, c2wR, c2pR, ubar_fsl, ubar_fsluL
    real(dp) :: ubar_fslvL, ubar_fslwL, ubar_fsr, ubar_fsruR, ubar_fsrvR
    real(dp) :: ubar_fsrwR, ubar_fs, ubar_fsrL, ubar_fsuL, ubar_fsvL, ubar_fswL
    real(dp) :: ubar_fsrR, ubar_fsuR, ubar_fsvR, ubar_fswR, dv1, dv1rL, dv1uL
    real(dp) :: dv1vL, dv1wL, dv1pL, dv1rR, dv1uR, dv1vR, dv1wR, dv1pR, dv2
    real(dp) :: dv2rL, dv2uL, dv2vL, dv2wL, dv2pL, dv2rR, dv2uR, dv2vR, dv2wR
    real(dp) :: dv2pR, dv3, dv3rL, dv3rR, dv4, dv4rL, dv4uL, dv4vL, dv4wL, dv4pL
    real(dp) :: dv4rR, dv4uR, dv4vR, dv4wR, dv4pR, r21, r21rL, r21uL, r21vL
    real(dp) :: r21wL, r21pL, r21rR, r21uR, r21vR, r21wR, r21pR, r31, r31rL
    real(dp) :: r31uL, r31vL, r31wL, r31pL, r31rR, r31uR, r31vR, r31wR, r31pR
    real(dp) :: r41, r41rL, r41uL, r41vL, r41wL, r41pL, r41rR, r41uR, r41vR
    real(dp) :: r41wR, r41pR, r51, r51rL, r51uL, r51vL, r51wL, r51pL, r51rR
    real(dp) :: r51uR, r51vR, r51wR, r51pR, r22, r22rL, r22uL, r22vL, r22wL
    real(dp) :: r22pL, r22rR, r22uR, r22vR, r22wR, r22pR, r32, r32rL, r32uL
    real(dp) :: r32vL, r32wL, r32pL, r32rR, r32uR, r32vR, r32wR, r32pR, r42
    real(dp) :: r42rL, r42uL, r42vL, r42wL, r42pL, r42rR, r42uR, r42vR, r42wR
    real(dp) :: r42pR, r52, r52rL, r52uL, r52vL, r52wL, r52pL, r52rR, r52uR
    real(dp) :: r52vR, r52wR, r52pR, r23, r23uL, r23vL, r23wL, r23uR, r23vR
    real(dp) :: r23wR, r33, r33uL, r33vL, r33wL, r33uR, r33vR, r33wR, r43
    real(dp) :: r43uL, r43vL, r43wL, r43uR, r43vR, r43wR, r53, r53rL, r53uL
    real(dp) :: r53vL, r53wL, r53rR, r53uR, r53vR, r53wR, r24, r24rL, r24uL
    real(dp) :: r24rR, r24uR, r34, r34rL, r34vL, r34rR, r34vR, r44, r44rL
    real(dp) :: r44wL, r44rR, r44wR, r54, r54rL, r54uL, r54vL, r54wL, r54rR
    real(dp) :: r54uR, r54vR, r54wR, t1rL, t1uL, t1vL, t1wL, t1pL, t1rR, t1uR
    real(dp) :: t1vR, t1wR, t1pR, t2rL, t2uL, t2vL, t2wL, t2pL, t2rR, t2uR
    real(dp) :: t2vR, t2wR, t2pR, t3rL, t3uL, t3vL, t3wL, t3pL, t3rR, t3uR
    real(dp) :: t3vR, t3wR, t3pR, t4rL, t4uL, t4vL, t4wL, t4pL, t4rR, t4uR
    real(dp) :: t4vR, t4wR, t4pR, t5rL, t5uL, t5vL, t5wL, t5pL, t5rR, t5uR
    real(dp) :: t5vR, t5wR, t5pR, dxL, dyL, dzL, dxR, dyR, dzR, dx1, dy1, dz1
    real(dp) :: fluxp1rL, fluxp1uL, fluxp1vL, fluxp1wL
    real(dp) :: fluxp2rL, fluxp2uL, fluxp2vL, fluxp2wL, fluxp2pL
    real(dp) :: fluxp3rL, fluxp3uL, fluxp3vL, fluxp3wL, fluxp3pL
    real(dp) :: fluxp4rL, fluxp4uL, fluxp4vL, fluxp4wL, fluxp4pL
    real(dp) :: fluxp5rL, fluxp5uL, fluxp5vL, fluxp5wL, fluxp5pL
    real(dp) :: fluxm1rR, fluxm1uR, fluxm1vR, fluxm1wR
    real(dp) :: fluxm2rR, fluxm2uR, fluxm2vR, fluxm2wR, fluxm2pR
    real(dp) :: fluxm3rR, fluxm3uR, fluxm3vR, fluxm3wR, fluxm3pR
    real(dp) :: fluxm4rR, fluxm4uR, fluxm4vR, fluxm4wR, fluxm4pR
    real(dp) :: fluxm5rR, fluxm5uR, fluxm5vR, fluxm5wR, fluxm5pR

    real(dp), dimension(5,max_functions) :: rlamb

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: second_ord_limit = 0.01_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp

    real(dp), dimension(3)      :: terms
    real(dp), dimension(5)      :: weight_matrix
    real(dp), dimension(5,5)    :: M1_inverse
    real(dp), dimension(5,5)    :: M2_inverse
    real(dp), dimension(5,5)    :: Mk_inverse
    real(dp), dimension(5,5)    :: dfp_primitive_times_weights
    real(dp), dimension(5,5)    :: dfm_primitive_times_weights
    real(dp), dimension(5,5)    :: dflux_dQnode1
    real(dp), dimension(5,5)    :: dflux_dQnode2
    real(dp), dimension(5,5)    :: dflux_dQnodek
    real(dp), dimension(n_cont) :: swx, swy, swz

    logical :: left_state_firstorder, right_state_firstorder
    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) .or.                   &
           .not.present(ad)     .or. .not.present(nfunctions) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_roe, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if ( .not.present(iau) .or. .not.present(fhelp) ) then
        if ( lmpi_master ) then
          write(*,*) 'a requested in atlam_roe, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    tag=0.0_dp

    if ( .not. neighbors_found ) call find_neighbors(nnodes01,nedgeloc,eptr)

    second = my_1
    if ( ntt <= first_order_iterations ) second = my_0

        edge_loop : do n = 1, nedgeloc

          node1 = eptr(1,n)
          node2 = eptr(2,n)

          local_node : if ( node1 <= nnodes0 .or. node2 <= nnodes0 ) then

! Get unit normals and area

          xnorm = xn(n)
          ynorm = yn(n)
          znorm = zn(n)
          area  = rarea(n)

          face_speed = my_0
          if ( need_grid_velocity ) face_speed = facespeed(n)

          xmean = my_haf*(x(node1) + x(node2))
          ymean = my_haf*(y(node1) + y(node2))
          zmean = my_haf*(z(node1) + z(node2))

          rx = second*(xmean - x(node1))
          ry = second*(ymean - y(node1))
          rz = second*(zmean - z(node1))

! Get variables on "left" side of face

          left_state_firstorder = .false.

            rhol   = qnode(1,node1)                                            &
                   + phi(1,node1)*((my_1-kappa_umuscl)*gradx(1,node1)*rx       &
                   +               (my_1-kappa_umuscl)*grady(1,node1)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(1,node1)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(1,node2)-qnode(1,node1)))
              rholrL = my_1

            ul     = qnode(2,node1)                                            &
                   + phi(2,node1)*((my_1-kappa_umuscl)*gradx(2,node1)*rx       &
                   +               (my_1-kappa_umuscl)*grady(2,node1)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(2,node1)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(2,node2)-qnode(2,node1)))
              uluL = my_1

            vl     = qnode(3,node1)                                            &
                   + phi(3,node1)*((my_1-kappa_umuscl)*gradx(3,node1)*rx       &
                   +               (my_1-kappa_umuscl)*grady(3,node1)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(3,node1)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(3,node2)-qnode(3,node1)))
              vlvL = my_1

            wl     = qnode(4,node1)                                            &
                   + phi(4,node1)*((my_1-kappa_umuscl)*gradx(4,node1)*rx       &
                   +               (my_1-kappa_umuscl)*grady(4,node1)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(4,node1)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(4,node2)-qnode(4,node1)))
              wlwL = my_1

            pressl = qnode(5,node1)                                            &
                   + phi(5,node1)*((my_1-kappa_umuscl)*gradx(5,node1)*rx       &
                   +               (my_1-kappa_umuscl)*grady(5,node1)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(5,node1)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(5,node2)-qnode(5,node1)))
              presslpL = my_1

! Catastrophic limiter
! Note the derivatives do not change

            if (rhol <= my_0 .or. pressl <= my_0) then
              left_state_firstorder = .true.
              rhol   = qnode(1,node1)
              ul     = qnode(2,node1)
              vl     = qnode(3,node1)
              wl     = qnode(4,node1)
              pressl = qnode(5,node1)
            end if

            q2l     = ul*ul + vl*vl + wl*wl
              q2luL = my_2*ul*uluL
              q2lvL = my_2*vl*vlvL
              q2lwL = my_2*wl*wlwL

            enrgyl = pressl/gm1 + my_haf*rhol*q2l

              enrgylrL = my_haf*(q2l*rholrL)
              enrgyluL = my_haf*(rhol*q2luL)
              enrgylvL = my_haf*(rhol*q2lvL)
              enrgylwL = my_haf*(rhol*q2lwL)
              enrgylpL = presslpL/gm1

            Hl = (enrgyl + pressl)/rhol

            HlrL = (rhol*(enrgylrL) - (enrgyl+pressl)*rholrL) / rhol / rhol
            HluL = (rhol*(enrgyluL)) / rhol / rhol
            HlvL = (rhol*(enrgylvL)) / rhol / rhol
            HlwL = (rhol*(enrgylwL)) / rhol / rhol
            HlpL = (rhol*(enrgylpL+presslpL)) / rhol / rhol

            ubarl  = xnorm*ul + ynorm*vl + znorm*wl

            ubarluL = xnorm*uluL
            ubarlvL = ynorm*vlvL
            ubarlwL = znorm*wlwL

! Get variables on "right" side of face

          rx = second*(xmean - x(node2))
          ry = second*(ymean - y(node2))
          rz = second*(zmean - z(node2))

          right_state_firstorder = .false.

            rhor   = qnode(1,node2)                                            &
                   + phi(1,node2)*((my_1-kappa_umuscl)*gradx(1,node2)*rx       &
                   +               (my_1-kappa_umuscl)*grady(1,node2)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(1,node2)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(1,node1)-qnode(1,node2)))
              rhorrR = my_1

            ur     = qnode(2,node2)                                            &
                   + phi(2,node2)*((my_1-kappa_umuscl)*gradx(2,node2)*rx       &
                   +               (my_1-kappa_umuscl)*grady(2,node2)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(2,node2)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(2,node1)-qnode(2,node2)))
              uruR = my_1

            vr     = qnode(3,node2)                                            &
                   + phi(3,node2)*((my_1-kappa_umuscl)*gradx(3,node2)*rx       &
                   +               (my_1-kappa_umuscl)*grady(3,node2)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(3,node2)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(3,node1)-qnode(3,node2)))
              vrvR = my_1

            wr     = qnode(4,node2)                                            &
                   + phi(4,node2)*((my_1-kappa_umuscl)*gradx(4,node2)*rx       &
                   +               (my_1-kappa_umuscl)*grady(4,node2)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(4,node2)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(4,node1)-qnode(4,node2)))
              wrwR = my_1

            pressr = qnode(5,node2)                                            &
                   + phi(5,node2)*((my_1-kappa_umuscl)*gradx(5,node2)*rx       &
                   +               (my_1-kappa_umuscl)*grady(5,node2)*ry       &
                   +               (my_1-kappa_umuscl)*gradz(5,node2)*rz       &
                   + second*kappa_umuscl*my_haf*(qnode(5,node1)-qnode(5,node2)))
              pressrpR = my_1

! Catastrophic limiter
! Note the derivatives do not change

            if (rhor <= my_0 .or. pressr <= my_0) then
              right_state_firstorder = .true.
              rhor   = qnode(1,node2)
              ur     = qnode(2,node2)
              vr     = qnode(3,node2)
              wr     = qnode(4,node2)
              pressr = qnode(5,node2)
            end if

            q2r    = ur*ur + vr*vr + wr*wr
              q2ruR = my_2*ur*uruR
              q2rvR = my_2*vr*vrvR
              q2rwR = my_2*wr*wrwR

            enrgyr = pressr/gm1 + my_haf*rhor*q2r
              enrgyrrR = my_haf*(q2r*rhorrR)
              enrgyruR = my_haf*(rhor*q2ruR)
              enrgyrvR = my_haf*(rhor*q2rvR)
              enrgyrwR = my_haf*(rhor*q2rwR)
              enrgyrpR = pressrpR/gm1

            Hr = (enrgyr + pressr)/rhor

            HrrR = (rhor*(enrgyrrR) - (enrgyr+pressr)*rhorrR) / rhor / rhor
            HruR = (rhor*(enrgyruR)) / rhor / rhor
            HrvR = (rhor*(enrgyrvR)) / rhor / rhor
            HrwR = (rhor*(enrgyrwR)) / rhor / rhor
            HrpR = (rhor*(enrgyrpR+pressrpR)) / rhor / rhor

            ubarr  = xnorm*ur + ynorm*vr + znorm*wr

            ubarruR = xnorm*uruR
            ubarrvR = ynorm*vrvR
            ubarrwR = znorm*wrwR

! Compute rho averages

            rho = sqrt(rhol*rhor)

            rhorL = my_haf / sqrt(rhol*rhor) * (rhor*rholrL)
            rhorR = my_haf / sqrt(rhol*rhor) * (rhol*rhorrR)

            wat = rho/(rho + rhor)

            watrL = ((rho + rhor)*rhorL - rho*(rhorL))                         &
                  / (rho + rhor) / (rho + rhor)
            watrR = ((rho + rhor)*rhorR - rho*(rhorR + rhorrR))                &
                  / (rho + rhor) / (rho + rhor)

            u   = ul*wat + ur*(my_1 - wat)

            urL = ul*watrL + ur*(-watrL)
            uuL = wat*uluL

            urR = ul*watrR + ur*(-watrR)
            uuR = (my_1 - wat)*uruR

            v   = vl*wat + vr*(my_1 - wat)

            vrL = vl*watrL + vr*(-watrL)
            vvL = wat*vlvL

            vrR = vl*watrR + vr*(-watrR)
            vvR = (my_1 - wat)*vrvR

            w   = wl*wat + wr*(my_1 - wat)

            wrL = wl*watrL + wr*(-watrL)
            wwL = wat*wlwL

            wrR = wl*watrR + wr*(-watrR)
            wwR = (my_1 - wat)*wrwR

            H   = Hl*wat + Hr*(my_1 - wat)

            HrL = Hl*watrL + wat*HlrL + Hr*(-watrL)
            HuL = wat*HluL
            HvL = wat*HlvL
            HwL = wat*HlwL
            HpL = wat*HlpL

            HrR = Hl*watrR + Hr*(-watrR) + (my_1 - wat)*HrrR
            HuR = (my_1 - wat)*HruR
            HvR = (my_1 - wat)*HrvR
            HwR = (my_1 - wat)*HrwR
            HpR = (my_1 - wat)*HrpR

            q2  = u*u + v*v + w*w

            q2rL = my_2*u*urL + my_2*v*vrL + my_2*w*wrL
            q2uL = my_2*u*uuL
            q2vL = my_2*v*vvL
            q2wL = my_2*w*wwL

            q2rR = my_2*u*urR + my_2*v*vrR + my_2*w*wrR
            q2uR = my_2*u*uuR
            q2vR = my_2*v*vvR
            q2wR = my_2*w*wwR

            c   = sqrt(gm1*(H - my_haf*q2))

            crL = my_haf / c * gm1*(HrL - my_haf*q2rL)
            cuL = my_haf / c * gm1*(HuL - my_haf*q2uL)
            cvL = my_haf / c * gm1*(HvL - my_haf*q2vL)
            cwL = my_haf / c * gm1*(HwL - my_haf*q2wL)
            cpL = my_haf / c * gm1*(HpL)

            crR = my_haf / c * gm1*(HrR - my_haf*q2rR)
            cuR = my_haf / c * gm1*(HuR - my_haf*q2uR)
            cvR = my_haf / c * gm1*(HvR - my_haf*q2vR)
            cwR = my_haf / c * gm1*(HwR - my_haf*q2wR)
            cpR = my_haf / c * gm1*(HpR)

            ubar = xnorm*u + ynorm*v + znorm*w

            ubarrL = xnorm*urL + ynorm*vrL + znorm*wrL
            ubaruL = xnorm*uuL
            ubarvL = ynorm*vvL
            ubarwL = znorm*wwL

            ubarrR = xnorm*urR + ynorm*vrR + znorm*wrR
            ubaruR = xnorm*uuR
            ubarvR = ynorm*vvR
            ubarwR = znorm*wwR

! Add normal face speed to the contravariant velocity terms
! Derivatives don't change since face_speed does not depend
! on Q

          ubar_fsl = ubarl - face_speed
            ubar_fsluL = ubarluL
            ubar_fslvL = ubarlvL
            ubar_fslwL = ubarlwL

          ubar_fsr = ubarr - face_speed
            ubar_fsruR = ubarruR
            ubar_fsrvR = ubarrvR
            ubar_fsrwR = ubarrwR

          ubar_fs  = ubar  - face_speed

            ubar_fsrL = ubarrL
            ubar_fsuL = ubaruL
            ubar_fsvL = ubarvL
            ubar_fswL = ubarwL

            ubar_fsrR = ubarrR
            ubar_fsuR = ubaruR
            ubar_fsvR = ubarvR
            ubar_fswR = ubarwR

! Now compute eigenvalues, eigenvectors, and strengths

            eig1 = abs(ubar_fs + c)
            eig2 = abs(ubar_fs - c)
            eig3 = abs(ubar_fs)

            if(ubar_fs+c >  my_0) then
              eig1rL = ubar_fsrL + crL
              eig1uL = ubar_fsuL + cuL
              eig1vL = ubar_fsvL + cvL
              eig1wL = ubar_fswL + cwL
              eig1pL = cpL
              eig1rR = ubar_fsrR + crR
              eig1uR = ubar_fsuR + cuR
              eig1vR = ubar_fsvR + cvR
              eig1wR = ubar_fswR + cwR
              eig1pR = cpR
            else
              eig1rL = -(ubar_fsrL + crL)
              eig1uL = -(ubar_fsuL + cuL)
              eig1vL = -(ubar_fsvL + cvL)
              eig1wL = -(ubar_fswL + cwL)
              eig1pL = -(cpL)
              eig1rR = -(ubar_fsrR + crR)
              eig1uR = -(ubar_fsuR + cuR)
              eig1vR = -(ubar_fsvR + cvR)
              eig1wR = -(ubar_fswR + cwR)
              eig1pR = -(cpR)
            endif

            if(ubar_fs-c >  my_0) then
              eig2rL = ubar_fsrL - crL
              eig2uL = ubar_fsuL - cuL
              eig2vL = ubar_fsvL - cvL
              eig2wL = ubar_fswL - cwL
              eig2pL = - cpL
              eig2rR = ubar_fsrR - crR
              eig2uR = ubar_fsuR - cuR
              eig2vR = ubar_fsvR - cvR
              eig2wR = ubar_fswR - cwR
              eig2pR = - cpR
            else
              eig2rL = -(ubar_fsrL - crL)
              eig2uL = -(ubar_fsuL - cuL)
              eig2vL = -(ubar_fsvL - cvL)
              eig2wL = -(ubar_fswL - cwL)
              eig2pL = -(- cpL)
              eig2rR = -(ubar_fsrR - crR)
              eig2uR = -(ubar_fsuR - cuR)
              eig2vR = -(ubar_fsvR - cvR)
              eig2wR = -(ubar_fswR - cwR)
              eig2pR = -(- cpR)
            endif

            if(ubar_fs >  my_0) then
              eig3rL = ubar_fsrL
              eig3uL = ubar_fsuL
              eig3vL = ubar_fsvL
              eig3wL = ubar_fswL
              eig3rR = ubar_fsrR
              eig3uR = ubar_fsuR
              eig3vR = ubar_fsvR
              eig3wR = ubar_fswR
            else
              eig3rL = -(ubar_fsrL)
              eig3uL = -(ubar_fsuL)
              eig3vL = -(ubar_fsvL)
              eig3wL = -(ubar_fswL)
              eig3rR = -(ubar_fsrR)
              eig3uR = -(ubar_fsuR)
              eig3vR = -(ubar_fsvR)
              eig3wR = -(ubar_fswR)
            endif

            drho   = rhor - rhol

            drhorL = - rholrL
            drhorR = rhorrR

            dpress = pressr - pressl

            dpresspL = - presslpL
            dpresspR = pressrpR

            du     = ur - ul

            duuL = - uluL
            duuR = uruR

            dv     = vr - vl

            dvvL = - vlvL
            dvvR = vrvR

            dw     = wr - wl

            dwwL = - wlwL
            dwwR = wrwR

            dubar  = ubarr - ubarl

            dubaruL = - ubarluL
            dubarvL = - ubarlvL
            dubarwL = - ubarlwL

            dubaruR = ubarruR
            dubarvR = ubarrvR
            dubarwR = ubarrwR

            c2 = c*c

            c2rL = my_2 * c * crL
            c2uL = my_2 * c * cuL
            c2vL = my_2 * c * cvL
            c2wL = my_2 * c * cwL
            c2pL = my_2 * c * cpL

            c2rR = my_2 * c * crR
            c2uR = my_2 * c * cuR
            c2vR = my_2 * c * cvR
            c2wR = my_2 * c * cwR
            c2pR = my_2 * c * cpR

! jumps have units of density

            dv1 = my_haf*(dpress + rho*c*dubar)/c2

            dv1rL = my_haf*(c2*(rho*(dubar*crL) + c*dubar*rhorL)               &
                  - (dpress + rho*c*dubar)*c2rL) / c2 / c2
            dv1uL = my_haf*(c2*(rho*(c*dubaruL + dubar*cuL))                   &
                  - (dpress + rho*c*dubar)*c2uL) / c2 / c2
            dv1vL = my_haf*(c2*(rho*(c*dubarvL + dubar*cvL))                   &
                  - (dpress + rho*c*dubar)*c2vL) / c2 / c2
            dv1wL = my_haf*(c2*(rho*(c*dubarwL + dubar*cwL))                   &
                  - (dpress + rho*c*dubar)*c2wL) / c2 / c2
            dv1pL = my_haf*(c2*(dpresspL + rho*(dubar*cpL))                    &
                  - (dpress + rho*c*dubar)*c2pL) / c2 / c2

            dv1rR = my_haf*(c2*(rho*(dubar*crR) + c*dubar*rhorR)               &
                  - (dpress + rho*c*dubar)*c2rR) / c2 / c2
            dv1uR = my_haf*(c2*(rho*(c*dubaruR + dubar*cuR))                   &
                  - (dpress + rho*c*dubar)*c2uR) / c2 / c2
            dv1vR = my_haf*(c2*(rho*(c*dubarvR + dubar*cvR))                   &
                  - (dpress + rho*c*dubar)*c2vR) / c2 / c2
            dv1wR = my_haf*(c2*(rho*(c*dubarwR + dubar*cwR))                   &
                  - (dpress + rho*c*dubar)*c2wR) / c2 / c2
            dv1pR = my_haf*(c2*(dpresspR + rho*(dubar*cpR))                    &
                  - (dpress + rho*c*dubar)*c2pR) / c2 / c2

            dv2 = my_haf*(dpress - rho*c*dubar)/c2

            dv2rL = my_haf*(c2*(- rho*(dubar*crL) - c*dubar*rhorL)             &
                  - (dpress - rho*c*dubar)*c2rL) / c2 / c2
            dv2uL = my_haf*(c2*(- rho*(c*dubaruL + dubar*cuL))                 &
                  - (dpress - rho*c*dubar)*c2uL) / c2 / c2
            dv2vL = my_haf*(c2*(- rho*(c*dubarvL + dubar*cvL))                 &
                  - (dpress - rho*c*dubar)*c2vL) / c2 / c2
            dv2wL = my_haf*(c2*(- rho*(c*dubarwL + dubar*cwL))                 &
                  - (dpress - rho*c*dubar)*c2wL) / c2 / c2
            dv2pL = my_haf*(c2*(dpresspL - rho*(dubar*cpL))                    &
                  - (dpress - rho*c*dubar)*c2pL) / c2 / c2

            dv2rR = my_haf*(c2*(- rho*(dubar*crR) - c*dubar*rhorR)             &
                  - (dpress - rho*c*dubar)*c2rR) / c2 / c2
            dv2uR = my_haf*(c2*(- rho*(c*dubaruR + dubar*cuR))                 &
                  - (dpress - rho*c*dubar)*c2uR) / c2 / c2
            dv2vR = my_haf*(c2*(- rho*(c*dubarvR + dubar*cvR))                 &
                  - (dpress - rho*c*dubar)*c2vR) / c2 / c2
            dv2wR = my_haf*(c2*(- rho*(c*dubarwR + dubar*cwR))                 &
                  - (dpress - rho*c*dubar)*c2wR) / c2 / c2
            dv2pR = my_haf*(c2*(dpresspR - rho*(dubar*cpR))                    &
                  - (dpress - rho*c*dubar)*c2pR) / c2 / c2

            dv3 = rho

            dv3rL = rhorL
            dv3rR = rhorR

            dv4 = (c*c*drho - dpress)/c2

            dv4rL = (c2*((c*(c*drhorL+drho*crL)+c*drho*crL))                   &
                    - (c*c*drho - dpress)*c2rL) / c2 / c2
            dv4uL = (c2*((c*(drho*cuL)+c*drho*cuL))                            &
                    - (c*c*drho - dpress)*c2uL) / c2 / c2
            dv4vL = (c2*((c*(drho*cvL)+c*drho*cvL))                            &
                    - (c*c*drho - dpress)*c2vL) / c2 / c2
            dv4wL = (c2*((c*(drho*cwL)+c*drho*cwL))                            &
                    - (c*c*drho - dpress)*c2wL) / c2 / c2
            dv4pL = (c2*((c*(drho*cpL)+c*drho*cpL) - dpresspL)                 &
                    - (c*c*drho - dpress)*c2pL) / c2 / c2

            dv4rR = (c2*((c*(c*drhorR+drho*crR)+c*drho*crR))                   &
                    - (c*c*drho - dpress)*c2rR) / c2 / c2
            dv4uR = (c2*((c*(drho*cuR)+c*drho*cuR))                            &
                    - (c*c*drho - dpress)*c2uR) / c2 / c2
            dv4vR = (c2*((c*(drho*cvR)+c*drho*cvR))                            &
                    - (c*c*drho - dpress)*c2vR) / c2 / c2
            dv4wR = (c2*((c*(drho*cwR)+c*drho*cwR))                            &
                    - (c*c*drho - dpress)*c2wR) / c2 / c2
            dv4pR = (c2*((c*(drho*cpR)+c*drho*cpR) - dpresspR)                 &
                    - (c*c*drho - dpress)*c2pR) / c2 / c2

            r21 = u + c*xnorm

            r21rL = urL + xnorm*crL
            r21uL = uuL + xnorm*cuL
            r21vL = xnorm*cvL
            r21wL = xnorm*cwL
            r21pL = xnorm*cpL

            r21rR = urR + xnorm*crR
            r21uR = uuR + xnorm*cuR
            r21vR = xnorm*cvR
            r21wR = xnorm*cwR
            r21pR = xnorm*cpR

            r31 = v + c*ynorm

            r31rL = vrL + ynorm*crL
            r31uL = ynorm*cuL
            r31vL = vvL + ynorm*cvL
            r31wL = ynorm*cwL
            r31pL = ynorm*cpL

            r31rR = vrR + ynorm*crR
            r31uR = ynorm*cuR
            r31vR = vvR + ynorm*cvR
            r31wR = ynorm*cwR
            r31pR = ynorm*cpR

            r41 = w + c*znorm

            r41rL = wrL + znorm*crL
            r41uL = znorm*cuL
            r41vL = znorm*cvL
            r41wL = wwL + znorm*cwL
            r41pL = znorm*cpL

            r41rR = wrR + znorm*crR
            r41uR = znorm*cuR
            r41vR = znorm*cvR
            r41wR = wwR + znorm*cwR
            r41pR = znorm*cpR

            r51 = H + c*ubar

            r51rL = HrL + c*ubarrL + ubar*crL
            r51uL = HuL + c*ubaruL + ubar*cuL
            r51vL = HvL + c*ubarvL + ubar*cvL
            r51wL = HwL + c*ubarwL + ubar*cwL
            r51pL = HpL + ubar*cpL

            r51rR = HrR + c*ubarrR + ubar*crR
            r51uR = HuR + c*ubaruR + ubar*cuR
            r51vR = HvR + c*ubarvR + ubar*cvR
            r51wR = HwR + c*ubarwR + ubar*cwR
            r51pR = HpR + ubar*cpR

            r22 = u - c*xnorm

            r22rL = urL - xnorm*crL
            r22uL = uuL - xnorm*cuL
            r22vL = - xnorm*cvL
            r22wL = - xnorm*cwL
            r22pL = - xnorm*cpL

            r22rR = urR - xnorm*crR
            r22uR = uuR - xnorm*cuR
            r22vR = - xnorm*cvR
            r22wR = - xnorm*cwR
            r22pR = - xnorm*cpR

            r32 = v - c*ynorm

            r32rL = vrL - ynorm*crL
            r32uL = - ynorm*cuL
            r32vL = vvL - ynorm*cvL
            r32wL = - ynorm*cwL
            r32pL = - ynorm*cpL

            r32rR = vrR - ynorm*crR
            r32uR = - ynorm*cuR
            r32vR = vvR - ynorm*cvR
            r32wR = - ynorm*cwR
            r32pR = - ynorm*cpR

            r42 = w - c*znorm

            r42rL = wrL - znorm*crL
            r42uL = - znorm*cuL
            r42vL = - znorm*cvL
            r42wL = wwL - znorm*cwL
            r42pL = - znorm*cpL

            r42rR = wrR - znorm*crR
            r42uR = - znorm*cuR
            r42vR = - znorm*cvR
            r42wR = wwR - znorm*cwR
            r42pR = - znorm*cpR

            r52 = H - c*ubar

            r52rL = HrL - c*ubarrL - ubar*crL
            r52uL = HuL - c*ubaruL - ubar*cuL
            r52vL = HvL - c*ubarvL - ubar*cvL
            r52wL = HwL - c*ubarwL - ubar*cwL
            r52pL = HpL - ubar*cpL

            r52rR = HrR - c*ubarrR - ubar*crR
            r52uR = HuR - c*ubaruR - ubar*cuR
            r52vR = HvR - c*ubarvR - ubar*cvR
            r52wR = HwR - c*ubarwR - ubar*cwR
            r52pR = HpR - ubar*cpR

            r23 = du - dubar*xnorm

            r23uL = duuL - xnorm*dubaruL
            r23vL = - xnorm*dubarvL
            r23wL = - xnorm*dubarwL

            r23uR = duuR - xnorm*dubaruR
            r23vR = - xnorm*dubarvR
            r23wR = - xnorm*dubarwR

            r33 = dv - dubar*ynorm

            r33uL = - ynorm*dubaruL
            r33vL = dvvL - ynorm*dubarvL
            r33wL = - ynorm*dubarwL

            r33uR = - ynorm*dubaruR
            r33vR = dvvR - ynorm*dubarvR
            r33wR = - ynorm*dubarwR

            r43 = dw - dubar*znorm

            r43uL = - znorm*dubaruL
            r43vL = - znorm*dubarvL
            r43wL = dwwL - znorm*dubarwL

            r43uR = - znorm*dubaruR
            r43vR = - znorm*dubarvR
            r43wR = dwwR - znorm*dubarwR

            r53 = u*du + v*dv + w*dw - ubar*dubar

            r53rL = du*urL + dv*vrL +dw*wrL - dubar*ubarrL
            r53uL = u*duuL+du*uuL - ubar*dubaruL - dubar*ubaruL
            r53vL = v*dvvL+dv*vvL - ubar*dubarvL - dubar*ubarvL
            r53wL = w*dwwL+dw*wwL - ubar*dubarwL - dubar*ubarwL

            r53rR = du*urR +dv*vrR +dw*wrR - dubar*ubarrR
            r53uR = u*duuR+du*uuR - ubar*dubaruR - dubar*ubaruR
            r53vR = v*dvvR+dv*vvR - ubar*dubarvR - dubar*ubarvR
            r53wR = w*dwwR+dw*wwR - ubar*dubarwR - dubar*ubarwR

            r24 = u

            r24rL = urL
            r24uL = uuL

            r24rR = urR
            r24uR = uuR

            r34 = v

            r34rL = vrL
            r34vL = vvL

            r34rR = vrR
            r34vR = vvR

            r44 = w

            r44rL = wrL
            r44wL = wwL

            r44rR = wrR
            r44wR = wwR

            r54 = my_haf*q2

            r54rL = my_haf*q2rL
            r54uL = my_haf*q2uL
            r54vL = my_haf*q2vL
            r54wL = my_haf*q2wL

            r54rR = my_haf*q2rR
            r54uR = my_haf*q2uR
            r54vR = my_haf*q2vR
            r54wR = my_haf*q2wR

!            t1 = eig1*dv1     + eig2*dv2                                      &
!                              + eig3*dv4

            t1rL = eig1*dv1rL+dv1*eig1rL + eig2*dv2rL+dv2*eig2rL               &
                 + eig3*dv4rL+dv4*eig3rL
            t1uL = eig1*dv1uL+dv1*eig1uL + eig2*dv2uL+dv2*eig2uL               &
                 + eig3*dv4uL+dv4*eig3uL
            t1vL = eig1*dv1vL+dv1*eig1vL + eig2*dv2vL+dv2*eig2vL               &
                 + eig3*dv4vL+dv4*eig3vL
            t1wL = eig1*dv1wL+dv1*eig1wL + eig2*dv2wL+dv2*eig2wL               &
                 + eig3*dv4wL+dv4*eig3wL
            t1pL = eig1*dv1pL+dv1*eig1pL + eig2*dv2pL+dv2*eig2pL               &
                 + eig3*dv4pL

            t1rR = eig1*dv1rR+dv1*eig1rR + eig2*dv2rR+dv2*eig2rR               &
                 + eig3*dv4rR+dv4*eig3rR
            t1uR = eig1*dv1uR+dv1*eig1uR + eig2*dv2uR+dv2*eig2uR               &
                 + eig3*dv4uR+dv4*eig3uR
            t1vR = eig1*dv1vR+dv1*eig1vR + eig2*dv2vR+dv2*eig2vR               &
                 + eig3*dv4vR+dv4*eig3vR
            t1wR = eig1*dv1wR+dv1*eig1wR + eig2*dv2wR+dv2*eig2wR               &
                 + eig3*dv4wR+dv4*eig3wR
            t1pR = eig1*dv1pR+dv1*eig1pR + eig2*dv2pR+dv2*eig2pR               &
                 + eig3*dv4pR

!            t2 = eig1*r21*dv1 + eig2*r22*dv2                                  &
!               + eig3*r23*dv3 + eig3*r24*dv4

            t2rL = eig1*(r21*dv1rL+dv1*r21rL)+r21*dv1*eig1rL                   &
                 + eig2*(r22*dv2rL+dv2*r22rL)+r22*dv2*eig2rL                   &
                 + eig3*(r23*dv3rL)+r23*dv3*eig3rL                             &
                 + eig3*(r24*dv4rL+dv4*r24rL)+r24*dv4*eig3rL

            t2uL = eig1*(r21*dv1uL+dv1*r21uL)+r21*dv1*eig1uL                   &
                 + eig2*(r22*dv2uL+dv2*r22uL)+r22*dv2*eig2uL                   &
                 + eig3*(dv3*r23uL)+r23*dv3*eig3uL                             &
                 + eig3*(r24*dv4uL+dv4*r24uL)+r24*dv4*eig3uL

            t2vL = eig1*(r21*dv1vL+dv1*r21vL)+r21*dv1*eig1vL                   &
                 + eig2*(r22*dv2vL+dv2*r22vL)+r22*dv2*eig2vL                   &
                 + eig3*(dv3*r23vL)+r23*dv3*eig3vL                             &
                 + eig3*(r24*dv4vL)+r24*dv4*eig3vL

            t2wL = eig1*(r21*dv1wL+dv1*r21wL)+r21*dv1*eig1wL                   &
                 + eig2*(r22*dv2wL+dv2*r22wL)+r22*dv2*eig2wL                   &
                 + eig3*(dv3*r23wL)+r23*dv3*eig3wL                             &
                 + eig3*(r24*dv4wL)+r24*dv4*eig3wL

            t2pL = eig1*(r21*dv1pL+dv1*r21pL)+r21*dv1*eig1pL                   &
                 + eig2*(r22*dv2pL+dv2*r22pL)+r22*dv2*eig2pL                   &
                 + eig3*(r24*dv4pL)

            t2rR = eig1*(r21*dv1rR+dv1*r21rR)+r21*dv1*eig1rR                   &
                 + eig2*(r22*dv2rR+dv2*r22rR)+r22*dv2*eig2rR                   &
                 + eig3*(r23*dv3rR)+r23*dv3*eig3rR                             &
                 + eig3*(r24*dv4rR+dv4*r24rR)+r24*dv4*eig3rR

            t2uR = eig1*(r21*dv1uR+dv1*r21uR)+r21*dv1*eig1uR                   &
                 + eig2*(r22*dv2uR+dv2*r22uR)+r22*dv2*eig2uR                   &
                 + eig3*(dv3*r23uR)+r23*dv3*eig3uR                             &
                 + eig3*(r24*dv4uR+dv4*r24uR)+r24*dv4*eig3uR

            t2vR = eig1*(r21*dv1vR+dv1*r21vR)+r21*dv1*eig1vR                   &
                 + eig2*(r22*dv2vR+dv2*r22vR)+r22*dv2*eig2vR                   &
                 + eig3*(dv3*r23vR)+r23*dv3*eig3vR                             &
                 + eig3*(r24*dv4vR)+r24*dv4*eig3vR

            t2wR = eig1*(r21*dv1wR+dv1*r21wR)+r21*dv1*eig1wR                   &
                 + eig2*(r22*dv2wR+dv2*r22wR)+r22*dv2*eig2wR                   &
                 + eig3*(dv3*r23wR)+r23*dv3*eig3wR                             &
                 + eig3*(r24*dv4wR)+r24*dv4*eig3wR

            t2pR = eig1*(r21*dv1pR+dv1*r21pR)+r21*dv1*eig1pR                   &
                 + eig2*(r22*dv2pR+dv2*r22pR)+r22*dv2*eig2pR                   &
                 + eig3*(r24*dv4pR)

!            t3 = eig1*r31*dv1 + eig2*r32*dv2                                  &
!               + eig3*r33*dv3 + eig3*r34*dv4

            t3rL = eig1*(r31*dv1rL+dv1*r31rL)+r31*dv1*eig1rL                   &
                 + eig2*(r32*dv2rL+dv2*r32rL)+r32*dv2*eig2rL                   &
                 + eig3*(r33*dv3rL)+r33*dv3*eig3rL                             &
                 + eig3*(r34*dv4rL+dv4*r34rL)+r34*dv4*eig3rL

            t3uL = eig1*(r31*dv1uL+dv1*r31uL)+r31*dv1*eig1uL                   &
                 + eig2*(r32*dv2uL+dv2*r32uL)+r32*dv2*eig2uL                   &
                 + eig3*(dv3*r33uL)+r33*dv3*eig3uL                             &
                 + eig3*(r34*dv4uL)+r34*dv4*eig3uL

            t3vL = eig1*(r31*dv1vL+dv1*r31vL)+r31*dv1*eig1vL                   &
                 + eig2*(r32*dv2vL+dv2*r32vL)+r32*dv2*eig2vL                   &
                 + eig3*(dv3*r33vL)+r33*dv3*eig3vL                             &
                 + eig3*(r34*dv4vL+dv4*r34vL)+r34*dv4*eig3vL

            t3wL = eig1*(r31*dv1wL+dv1*r31wL)+r31*dv1*eig1wL                   &
                 + eig2*(r32*dv2wL+dv2*r32wL)+r32*dv2*eig2wL                   &
                 + eig3*(dv3*r33wL)+r33*dv3*eig3wL                             &
                 + eig3*(r34*dv4wL)+r34*dv4*eig3wL

            t3pL = eig1*(r31*dv1pL+dv1*r31pL)+r31*dv1*eig1pL                   &
                 + eig2*(r32*dv2pL+dv2*r32pL)+r32*dv2*eig2pL                   &
                 + eig3*(r34*dv4pL)

            t3rR = eig1*(r31*dv1rR+dv1*r31rR)+r31*dv1*eig1rR                   &
                 + eig2*(r32*dv2rR+dv2*r32rR)+r32*dv2*eig2rR                   &
                 + eig3*(r33*dv3rR)+r33*dv3*eig3rR                             &
                 + eig3*(r34*dv4rR+dv4*r34rR)+r34*dv4*eig3rR

            t3uR = eig1*(r31*dv1uR+dv1*r31uR)+r31*dv1*eig1uR                   &
                 + eig2*(r32*dv2uR+dv2*r32uR)+r32*dv2*eig2uR                   &
                 + eig3*(dv3*r33uR)+r33*dv3*eig3uR                             &
                 + eig3*(r34*dv4uR)+r34*dv4*eig3uR

            t3vR = eig1*(r31*dv1vR+dv1*r31vR)+r31*dv1*eig1vR                   &
                 + eig2*(r32*dv2vR+dv2*r32vR)+r32*dv2*eig2vR                   &
                 + eig3*(dv3*r33vR)+r33*dv3*eig3vR                             &
                 + eig3*(r34*dv4vR+dv4*r34vR)+r34*dv4*eig3vR

            t3wR = eig1*(r31*dv1wR+dv1*r31wR)+r31*dv1*eig1wR                   &
                 + eig2*(r32*dv2wR+dv2*r32wR)+r32*dv2*eig2wR                   &
                 + eig3*(dv3*r33wR)+r33*dv3*eig3wR                             &
                 + eig3*(r34*dv4wR)+r34*dv4*eig3wR

            t3pR = eig1*(r31*dv1pR+dv1*r31pR)+r31*dv1*eig1pR                   &
                 + eig2*(r32*dv2pR+dv2*r32pR)+r32*dv2*eig2pR                   &
                 + eig3*(r34*dv4pR)

!            t4 = eig1*r41*dv1 + eig2*r42*dv2                                  &
!               + eig3*r43*dv3 + eig3*r44*dv4

            t4rL = eig1*(r41*dv1rL+dv1*r41rL)+r41*dv1*eig1rL                   &
                 + eig2*(r42*dv2rL+dv2*r42rL)+r42*dv2*eig2rL                   &
                 + eig3*(r43*dv3rL)+r43*dv3*eig3rL                             &
                 + eig3*(r44*dv4rL+dv4*r44rL)+r44*dv4*eig3rL

            t4uL = eig1*(r41*dv1uL+dv1*r41uL)+r41*dv1*eig1uL                   &
                 + eig2*(r42*dv2uL+dv2*r42uL)+r42*dv2*eig2uL                   &
                 + eig3*(dv3*r43uL)+r43*dv3*eig3uL                             &
                 + eig3*(r44*dv4uL)+r44*dv4*eig3uL

            t4vL = eig1*(r41*dv1vL+dv1*r41vL)+r41*dv1*eig1vL                   &
                 + eig2*(r42*dv2vL+dv2*r42vL)+r42*dv2*eig2vL                   &
                 + eig3*(dv3*r43vL)+r43*dv3*eig3vL                             &
                 + eig3*(r44*dv4vL)+r44*dv4*eig3vL

            t4wL = eig1*(r41*dv1wL+dv1*r41wL)+r41*dv1*eig1wL                   &
                 + eig2*(r42*dv2wL+dv2*r42wL)+r42*dv2*eig2wL                   &
                 + eig3*(dv3*r43wL)+r43*dv3*eig3wL                             &
                 + eig3*(r44*dv4wL+dv4*r44wL)+r44*dv4*eig3wL

            t4pL = eig1*(r41*dv1pL+dv1*r41pL)+r41*dv1*eig1pL                   &
                 + eig2*(r42*dv2pL+dv2*r42pL)+r42*dv2*eig2pL                   &
                 + eig3*(r44*dv4pL)

            t4rR = eig1*(r41*dv1rR+dv1*r41rR)+r41*dv1*eig1rR                   &
                 + eig2*(r42*dv2rR+dv2*r42rR)+r42*dv2*eig2rR                   &
                 + eig3*(r43*dv3rR)+r43*dv3*eig3rR                             &
                 + eig3*(r44*dv4rR+dv4*r44rR)+r44*dv4*eig3rR

            t4uR = eig1*(r41*dv1uR+dv1*r41uR)+r41*dv1*eig1uR                   &
                 + eig2*(r42*dv2uR+dv2*r42uR)+r42*dv2*eig2uR                   &
                 + eig3*(dv3*r43uR)+r43*dv3*eig3uR                             &
                 + eig3*(r44*dv4uR)+r44*dv4*eig3uR

            t4vR = eig1*(r41*dv1vR+dv1*r41vR)+r41*dv1*eig1vR                   &
                 + eig2*(r42*dv2vR+dv2*r42vR)+r42*dv2*eig2vR                   &
                 + eig3*(dv3*r43vR)+r43*dv3*eig3vR                             &
                 + eig3*(r44*dv4vR)+r44*dv4*eig3vR

            t4wR = eig1*(r41*dv1wR+dv1*r41wR)+r41*dv1*eig1wR                   &
                 + eig2*(r42*dv2wR+dv2*r42wR)+r42*dv2*eig2wR                   &
                 + eig3*(dv3*r43wR)+r43*dv3*eig3wR                             &
                 + eig3*(r44*dv4wR+dv4*r44wR)+r44*dv4*eig3wR

            t4pR = eig1*(r41*dv1pR+dv1*r41pR)+r41*dv1*eig1pR                   &
                 + eig2*(r42*dv2pR+dv2*r42pR)+r42*dv2*eig2pR                   &
                 + eig3*(r44*dv4pR)

!            t5 = eig1*r51*dv1 + eig2*r52*dv2                                  &
!               + eig3*r53*dv3 + eig3*r54*dv4

            t5rL = eig1*(r51*dv1rL+dv1*r51rL)+r51*dv1*eig1rL                   &
                 + eig2*(r52*dv2rL+dv2*r52rL)+r52*dv2*eig2rL                   &
                 + eig3*(r53*dv3rL+dv3*r53rL)+r53*dv3*eig3rL                   &
                 + eig3*(r54*dv4rL+dv4*r54rL)+r54*dv4*eig3rL

            t5uL = eig1*(r51*dv1uL+dv1*r51uL)+r51*dv1*eig1uL                   &
                 + eig2*(r52*dv2uL+dv2*r52uL)+r52*dv2*eig2uL                   &
                 + eig3*(dv3*r53uL)+r53*dv3*eig3uL                             &
                 + eig3*(r54*dv4uL+dv4*r54uL)+r54*dv4*eig3uL

            t5vL = eig1*(r51*dv1vL+dv1*r51vL)+r51*dv1*eig1vL                   &
                 + eig2*(r52*dv2vL+dv2*r52vL)+r52*dv2*eig2vL                   &
                 + eig3*(dv3*r53vL)+r53*dv3*eig3vL                             &
                 + eig3*(r54*dv4vL+dv4*r54vL)+r54*dv4*eig3vL

            t5wL = eig1*(r51*dv1wL+dv1*r51wL)+r51*dv1*eig1wL                   &
                 + eig2*(r52*dv2wL+dv2*r52wL)+r52*dv2*eig2wL                   &
                 + eig3*(dv3*r53wL)+r53*dv3*eig3wL                             &
                 + eig3*(r54*dv4wL+dv4*r54wL)+r54*dv4*eig3wL

            t5pL = eig1*(r51*dv1pL+dv1*r51pL)+r51*dv1*eig1pL                   &
                 + eig2*(r52*dv2pL+dv2*r52pL)+r52*dv2*eig2pL                   &
                 + eig3*(r54*dv4pL)

            t5rR = eig1*(r51*dv1rR+dv1*r51rR)+r51*dv1*eig1rR                   &
                 + eig2*(r52*dv2rR+dv2*r52rR)+r52*dv2*eig2rR                   &
                 + eig3*(r53*dv3rR+dv3*r53rR)+r53*dv3*eig3rR                   &
                 + eig3*(r54*dv4rR+dv4*r54rR)+r54*dv4*eig3rR

            t5uR = eig1*(r51*dv1uR+dv1*r51uR)+r51*dv1*eig1uR                   &
                 + eig2*(r52*dv2uR+dv2*r52uR)+r52*dv2*eig2uR                   &
                 + eig3*(dv3*r53uR)+r53*dv3*eig3uR                             &
                 + eig3*(r54*dv4uR+dv4*r54uR)+r54*dv4*eig3uR

            t5vR = eig1*(r51*dv1vR+dv1*r51vR)+r51*dv1*eig1vR                   &
                 + eig2*(r52*dv2vR+dv2*r52vR)+r52*dv2*eig2vR                   &
                 + eig3*(dv3*r53vR)+r53*dv3*eig3vR                             &
                 + eig3*(r54*dv4vR+dv4*r54vR)+r54*dv4*eig3vR

            t5wR = eig1*(r51*dv1wR+dv1*r51wR)+r51*dv1*eig1wR                   &
                 + eig2*(r52*dv2wR+dv2*r52wR)+r52*dv2*eig2wR                   &
                 + eig3*(dv3*r53wR)+r53*dv3*eig3wR                             &
                 + eig3*(r54*dv4wR+dv4*r54wR)+r54*dv4*eig3wR

            t5pR = eig1*(r51*dv1pR+dv1*r51pR)+r51*dv1*eig1pR                   &
                 + eig2*(r52*dv2pR+dv2*r52pR)+r52*dv2*eig2pR                   &
                 + eig3*(r54*dv4pR)

! Compute flux using variables from left side of face

!            fluxp1 = area*rhol*ubar_fsl

            fluxp1rL = area*(ubar_fsl*rholrL)
            fluxp1uL = area*(rhol*ubar_fsluL)
            fluxp1vL = area*(rhol*ubar_fslvL)
            fluxp1wL = area*(rhol*ubar_fslwL)

!            fluxp2 = area*(rhol*ul*ubar_fsl + xnorm*pressl)

            fluxp2rL = area*(ul*ubar_fsl*rholrL)
            fluxp2uL = area*(rhol*(ul*ubar_fsluL+ubar_fsl*uluL))
            fluxp2vL = area*(rhol*(ul*ubar_fslvL))
            fluxp2wL = area*(rhol*(ul*ubar_fslwL))
            fluxp2pL = area*(xnorm*presslpL)

!            fluxp3 = area*(rhol*vl*ubar_fsl + ynorm*pressl)

            fluxp3rL = area*(vl*ubar_fsl*rholrL)
            fluxp3uL = area*(rhol*(vl*ubar_fsluL))
            fluxp3vL = area*(rhol*(vl*ubar_fslvL+ubar_fsl*vlvL))
            fluxp3wL = area*(rhol*(vl*ubar_fslwL))
            fluxp3pL = area*(ynorm*presslpL)

!            fluxp4 = area*(rhol*wl*ubar_fsl + znorm*pressl)

            fluxp4rL = area*(wl*ubar_fsl*rholrL)
            fluxp4uL = area*(rhol*(wl*ubar_fsluL))
            fluxp4vL = area*(rhol*(wl*ubar_fslvL))
            fluxp4wL = area*(rhol*(wl*ubar_fslwL+ubar_fsl*wlwL))
            fluxp4pL = area*(znorm*presslpL)

!            fluxp5 = area*(enrgyl*ubar_fsl + pressl*ubarl)

            fluxp5rL = area*(ubar_fsl*enrgylrL)
            fluxp5uL = area*(enrgyl*ubar_fsluL + ubar_fsl*enrgyluL             &
                     + pressl*ubarluL)
            fluxp5vL = area*(enrgyl*ubar_fslvL + ubar_fsl*enrgylvL             &
                     + pressl*ubarlvL)
            fluxp5wL = area*(enrgyl*ubar_fslwL + ubar_fsl*enrgylwL             &
                     + pressl*ubarlwL)
            fluxp5pL = area*(ubar_fsl*enrgylpL + ubarl*presslpL)

! Now the right side

 !           fluxm1 = area*rhor*ubar_fsr

            fluxm1rR = area*(ubar_fsr*rhorrR)
            fluxm1uR = area*(rhor*ubar_fsruR)
            fluxm1vR = area*(rhor*ubar_fsrvR)
            fluxm1wR = area*(rhor*ubar_fsrwR)

!            fluxm2 = area*(rhor*ur*ubar_fsr + xnorm*pressr)

            fluxm2rR = area*(ur*ubar_fsr*rhorrR)
            fluxm2uR = area*(rhor*(ur*ubar_fsruR+ubar_fsr*uruR))
            fluxm2vR = area*(rhor*(ur*ubar_fsrvR))
            fluxm2wR = area*(rhor*(ur*ubar_fsrwR))
            fluxm2pR = area*(xnorm*pressrpR)

!            fluxm3 = area*(rhor*vr*ubar_fsr + ynorm*pressr)

            fluxm3rR = area*(vr*ubar_fsr*rhorrR)
            fluxm3uR = area*(rhor*(vr*ubar_fsruR))
            fluxm3vR = area*(rhor*(vr*ubar_fsrvR+ubar_fsr*vrvR))
            fluxm3wR = area*(rhor*(vr*ubar_fsrwR))
            fluxm3pR = area*(ynorm*pressrpR)

!            fluxm4 = area*(rhor*wr*ubar_fsr + znorm*pressr)

            fluxm4rR = area*(wr*ubar_fsr*rhorrR)
            fluxm4uR = area*(rhor*(wr*ubar_fsruR))
            fluxm4vR = area*(rhor*(wr*ubar_fsrvR))
            fluxm4wR = area*(rhor*(wr*ubar_fsrwR+ubar_fsr*wrwR))
            fluxm4pR = area*(znorm*pressrpR)

!            fluxm5 = area*(enrgyr*ubar_fsr + pressr*ubarr)

            fluxm5rR = area*(ubar_fsr*enrgyrrR)
            fluxm5uR = area*(enrgyr*ubar_fsruR + ubar_fsr*enrgyruR             &
                     + pressr*ubarruR)
            fluxm5vR = area*(enrgyr*ubar_fsrvR + ubar_fsr*enrgyrvR             &
                     + pressr*ubarrvR)
            fluxm5wR = area*(enrgyr*ubar_fsrwR + ubar_fsr*enrgyrwR             &
                     + pressr*ubarrwR)
            fluxm5pR = area*(ubar_fsr*enrgyrpR + ubarr*pressrpR)

!          flux1 = my_haf*(fluxp1 + fluxm1 - area*t1)
!          flux2 = my_haf*(fluxp2 + fluxm2 - area*t2)
!          flux3 = my_haf*(fluxp3 + fluxm3 - area*t3)
!          flux4 = my_haf*(fluxp4 + fluxm4 - area*t4)
!          flux5 = my_haf*(fluxp5 + fluxm5 - area*t5)

          dfp_primitive(1,1) = my_haf*(fluxp1rL - area*t1rL)
          dfp_primitive(1,2) = my_haf*(fluxp1uL - area*t1uL)
          dfp_primitive(1,3) = my_haf*(fluxp1vL - area*t1vL)
          dfp_primitive(1,4) = my_haf*(fluxp1wL - area*t1wL)
          dfp_primitive(1,5) = my_haf*(- area*t1pL)
          dfm_primitive(1,1) = my_haf*(fluxm1rR - area*t1rR)
          dfm_primitive(1,2) = my_haf*(fluxm1uR - area*t1uR)
          dfm_primitive(1,3) = my_haf*(fluxm1vR - area*t1vR)
          dfm_primitive(1,4) = my_haf*(fluxm1wR - area*t1wR)
          dfm_primitive(1,5) = my_haf*(- area*t1pR)

          dfp_primitive(2,1) = my_haf*(fluxp2rL - area*t2rL)
          dfp_primitive(2,2) = my_haf*(fluxp2uL - area*t2uL)
          dfp_primitive(2,3) = my_haf*(fluxp2vL - area*t2vL)
          dfp_primitive(2,4) = my_haf*(fluxp2wL - area*t2wL)
          dfp_primitive(2,5) = my_haf*(fluxp2pL - area*t2pL)
          dfm_primitive(2,1) = my_haf*(fluxm2rR - area*t2rR)
          dfm_primitive(2,2) = my_haf*(fluxm2uR - area*t2uR)
          dfm_primitive(2,3) = my_haf*(fluxm2vR - area*t2vR)
          dfm_primitive(2,4) = my_haf*(fluxm2wR - area*t2wR)
          dfm_primitive(2,5) = my_haf*(fluxm2pR - area*t2pR)

          dfp_primitive(3,1) = my_haf*(fluxp3rL - area*t3rL)
          dfp_primitive(3,2) = my_haf*(fluxp3uL - area*t3uL)
          dfp_primitive(3,3) = my_haf*(fluxp3vL - area*t3vL)
          dfp_primitive(3,4) = my_haf*(fluxp3wL - area*t3wL)
          dfp_primitive(3,5) = my_haf*(fluxp3pL - area*t3pL)
          dfm_primitive(3,1) = my_haf*(fluxm3rR - area*t3rR)
          dfm_primitive(3,2) = my_haf*(fluxm3uR - area*t3uR)
          dfm_primitive(3,3) = my_haf*(fluxm3vR - area*t3vR)
          dfm_primitive(3,4) = my_haf*(fluxm3wR - area*t3wR)
          dfm_primitive(3,5) = my_haf*(fluxm3pR - area*t3pR)

          dfp_primitive(4,1) = my_haf*(fluxp4rL - area*t4rL)
          dfp_primitive(4,2) = my_haf*(fluxp4uL - area*t4uL)
          dfp_primitive(4,3) = my_haf*(fluxp4vL - area*t4vL)
          dfp_primitive(4,4) = my_haf*(fluxp4wL - area*t4wL)
          dfp_primitive(4,5) = my_haf*(fluxp4pL - area*t4pL)
          dfm_primitive(4,1) = my_haf*(fluxm4rR - area*t4rR)
          dfm_primitive(4,2) = my_haf*(fluxm4uR - area*t4uR)
          dfm_primitive(4,3) = my_haf*(fluxm4vR - area*t4vR)
          dfm_primitive(4,4) = my_haf*(fluxm4wR - area*t4wR)
          dfm_primitive(4,5) = my_haf*(fluxm4pR - area*t4pR)

          dfp_primitive(5,1) = my_haf*(fluxp5rL - area*t5rL)
          dfp_primitive(5,2) = my_haf*(fluxp5uL - area*t5uL)
          dfp_primitive(5,3) = my_haf*(fluxp5vL - area*t5vL)
          dfp_primitive(5,4) = my_haf*(fluxp5wL - area*t5wL)
          dfp_primitive(5,5) = my_haf*(fluxp5pL - area*t5pL)
          dfm_primitive(5,1) = my_haf*(fluxm5rR - area*t5rR)
          dfm_primitive(5,2) = my_haf*(fluxm5uR - area*t5uR)
          dfm_primitive(5,3) = my_haf*(fluxm5vR - area*t5vR)
          dfm_primitive(5,4) = my_haf*(fluxm5wR - area*t5wR)
          dfm_primitive(5,5) = my_haf*(fluxm5pR - area*t5pR)

!  We need to convert these last results into conservative
!  variables, using one last chain-rule.

!  q = primitive
!  Q = conservative

        t = setup_t(rhol,ul,vl,wl)
        dfp = matmul(dfp_primitive,t)

        t = setup_t(rhor,ur,vr,wr)
        dfm = matmul(dfm_primitive,t)

! Take care of node1

        if ( fill_res ) then
          tag(1,1:5) = coltag(1:5,node1)
          tag(2,1:5) = coltag(1:5,node2)

          do i = 1, nfunctions
            rlamb(1:5,i) = tag(1,1:5)*rlam(1:5,node1,i)
          end do
        endif

! Compute (M_left)*(M(inverse)_node1)  This is for dQ(left)/dQ_1

        rho = qnode(1,node1)
        u   = qnode(2,node1)
        v   = qnode(3,node1)
        w   = qnode(4,node1)

        ti = setup_t_inverse(rhol,ul,vl,wl)
        t = setup_t(rho,u,v,w)

        t = matmul(ti,t)
        B = matmul(dfp,t)

        if ( fill_res ) then
          if(node1 <= nnodes0) then
            if ( rn == node1 .and. np == node1 ) then
              do iii = 1, 5
                ad(iii,1:5) = ad(iii,1:5) + B(iii,1:5)*coltag(iii,rn)
              end do
            endif
            do i = 1, nfunctions
              do m = 1, 5
                res(1:5,node1,i) = res(1:5,node1,i) + B(m,1:5)*rlamb(m,i)
              end do
            end do
          endif
        endif

! Now grab the offdiagonal

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(1,1:5)*rlam(1:5,node1,i)
          end do
        endif

! Compute (M_right)*(M(inverse)_node2)  This is for dQ(right)/dQ_2

        rho = qnode(1,node2)
        u   = qnode(2,node2)
        v   = qnode(3,node2)
        w   = qnode(4,node2)

        ti = setup_t_inverse(rhor,ur,vr,wr)
        t = setup_t(rho,u,v,w)

        t = matmul(ti,t)
        cc = matmul(dfm,t)

        if ( fill_a ) then
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          idiag2 = iau(node2)   ! where to put dR2/dQ2
          ioff12 = fhelp(2,n)   ! where to put dR1/dQ2
          ioff21 = fhelp(1,n)   ! where to put dR2/dQ1

! dR1/dQ1
          if(node1<=nnodes0) a(1:5,1:5,idiag1) = a(1:5,1:5,idiag1) + B(1:5,1:5)
! dR1/dQ2
          if(node2<=nnodes0) a(1:5,1:5,ioff12) = a(1:5,1:5,ioff12) + CC(1:5,1:5)
! dR2/dQ2
          if(node2<=nnodes0) a(1:5,1:5,idiag2) = a(1:5,1:5,idiag2) - CC(1:5,1:5)
! dR2/dQ1
          if(node1<=nnodes0) a(1:5,1:5,ioff21) = a(1:5,1:5,ioff21) - B(1:5,1:5)
        endif


        res_contribs : if ( fill_res ) then
          if(node2 <= nnodes0) then
            if ( rn == node1 .and. np == node2 ) then
              do iii = 1, 5
                ad(iii,1:5) = ad(iii,1:5) + CC(iii,1:5)*coltag(iii,rn)
              end do
            endif
            do i = 1, nfunctions
              do m = 1, 5
                res(1:5,node2,i) = res(1:5,node2,i) + CC(m,1:5)*rlamb(m,i)
              end do
            end do
          endif

! Now do the second node

          do i = 1, nfunctions
            rlamb(1:5,i) = tag(2,1:5)*rlam(1:5,node2,i)
          end do

          if(node2 <= nnodes0) then
          if ( rn == node2 .and. np == node2 ) then
            do iii = 1, 5
              ad(iii,1:5) = ad(iii,1:5) - CC(iii,1:5)*coltag(iii,rn)
            end do
          endif
          do i = 1, nfunctions
            do m = 1, 5
              res(1:5,node2,i) = res(1:5,node2,i) - CC(m,1:5)*rlamb(m,i)
            end do
          end do
          endif

! Now grab the offdiagonal

          do i = 1, nfunctions
            rlamb(1:5,i) = tag(2,1:5)*rlam(1:5,node2,i)
          end do

          if(node1 <= nnodes0) then
          if ( rn == node2 .and. np == node1 ) then
           do iii = 1, 5
             ad(iii,1:5) = ad(iii,1:5) - B(iii,1:5)*coltag(iii,rn)
           end do
          endif
          do i = 1, nfunctions
            do m = 1, 5
              res(1:5,node1,i) = res(1:5,node1,i) - B(m,1:5)*rlamb(m,i)
            end do
          end do
          endif
        endif res_contribs

! At this point, we have first order accuracy.  Now add contribution
! from second order terms.  We have taken into account the constant
! term in the reconstruction.  Now we need to add the pieces from the
! gradient terms.

      second_order_contribs : if( second > second_ord_limit) then

! First let's take care of all the nodes surrounding node1

        dxL = my_haf*second*(x(node2) - x(node1))
        dyL = my_haf*second*(y(node2) - y(node1))
        dzL = my_haf*second*(z(node2) - z(node1))

! Take care of the circuit of nodes around node 1

      if(node1 <= nnodes0 .and. (.not.left_state_firstorder) ) then
      loop_2000 : do ii = 1, neighbors(node1)%n

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(1,1:5)*Rlam(1:5,node1,i)
          end do
        endif

        node = neighbors(node1)%list(ii) ! This is node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

        dx1 = x(node) - x(node1)
        dy1 = y(node) - y(node1)
        dz1 = z(node) - z(node1)

        terms(:) = lstgs_func(dx1,         dy1,         dz1,                   &
                              rr11(node1), rr12(node1), rr13(node1),           &
                              rr22(node1), rr23(node1), rr33(node1))
        swx(:) = terms(1)
        swy(:) = terms(2)
        swz(:) = terms(3)

        if (symmetry_bcs) then
          call lstgs_sym(symmetry(node1),                                      &
                         dx1,         dy1,         dz1,                        &
                         rr11(node1), rr12(node1), rr13(node1),                &
                         rr22(node1), rr23(node1), rr33(node1),                &
                         n_cont, swx, swy, swz )
        end if

        if ( node == node1 ) then
          swx = 0.0_dp
          swy = 0.0_dp
          swz = 0.0_dp
        end if

        weight_matrix(1:5) = phi(1:5,node1)*(my_1-kappa_umuscl)                &
                                       *(swx(1:5)*dxL+swy(1:5)*dyL+swz(1:5)*dzL)

        do j = 1, 5
        dfp_primitive_times_weights(1:5,j)=dfp_primitive(1:5,j)*weight_matrix(j)
        end do

         M1_inverse = setup_t(qnode(1,node1),qnode(2,node1),qnode(3,node1),    &
                              qnode(4,node1))

         dflux_dQnode1 = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnode1(i,1:5) = dflux_dQnode1(i,1:5)                       &
                       + dfp_primitive_times_weights(i,k)*M1_inverse(k,1:5)
           end do
         end do

         Mk_inverse = setup_t(qnode(1,node),qnode(2,node),qnode(3,node),       &
                              qnode(4,node))

         dflux_dQnodek = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnodek(i,1:5) = dflux_dQnodek(i,1:5)                       &
                       + dfp_primitive_times_weights(i,k)*Mk_inverse(k,1:5)
           end do
         end do

! We now have the linearizations of the residual at node1 with respect
! to Q at node1 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node1 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node1 and nodek).

! Always add to the off-node and subtract from the central node
! Note that if node=node1 then the weight is zero so nothing happens

! dR1/dQ1

         res_contribs2 : if ( fill_res ) then
           if ( rn == node1 .and. np == node1 ) then
             do iii = 1, 5
               ad(iii,1:5)=ad(iii,1:5) - dflux_dQnode1(iii,1:5)*coltag(iii,rn)
             end do
           endif

           do i = 1, nfunctions
             do m = 1, 5
               res(1:5,node1,i)=res(1:5,node1,i)-dflux_dQnode1(m,1:5)*Rlamb(m,i)
             end do

! dR1/dQk with k going around node1

             if ( rn == node1 .and. np == node ) then
               do iii = 1, 5
                 ad(iii,1:5)=ad(iii,1:5)+dflux_dQnodek(iii,1:5)*coltag(iii,rn)
               end do
             endif

             do m = 1, 5
               res(1:5,node,i) = res(1:5,node,i)+dflux_dQnodek(m,1:5)*Rlamb(m,i)
             end do
           end do
         endif res_contribs2

        if ( fill_a ) then

! dR1/dQ1
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          a(1:5,1:5,idiag1) = a(1:5,1:5,idiag1) - dflux_dQnode1(1:5,1:5)

! dR1/dQk with k going around node1

           ioff1k = 0
           jstart = ia(node)
           jend   = ia(node+1)-1
           search1 : do j = jstart, jend
             neighbor = abs(ja(j))
             if ( neighbor == node1 ) then
               ioff1k = j
               exit search1
             endif
           end do search1

           if ( ioff1k == 0 ) then
             write(*,*) '1: Error finding off-diag 1k', node, node1
             call lmpi_die
           endif

           a(1:5,1:5,ioff1k) = a(1:5,1:5,ioff1k) + dflux_dQnodek(1:5,1:5)
        endif

! Contribution from Jacobian for node 2

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(2,1:5)*Rlam(1:5,node2,i)
          end do
        endif

! We now have the linearizations of the residual at node2 with respect
! to Q at node1 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node2 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node1 and nodek).

! Now, subtract the weight for the "diagonal" and add it for the offdiagonal

! dR2/dQ1

       res_contribs3 : if ( fill_res ) then
         if ( rn == node2 .and. np == node1 ) then
           do iii = 1, 5
             ad(iii,1:5) = ad(iii,1:5) + dflux_dQnode1(iii,1:5)*coltag(iii,rn)
           end do
         endif

         do i = 1, nfunctions
           do m = 1, 5
             res(1:5,node1,i) = res(1:5,node1,i)+dflux_dQnode1(m,1:5)*Rlamb(m,i)
           end do

! dR2/dQk with k going around node1

           if ( rn == node2 .and. np == node ) then
             do iii = 1, 5
               ad(iii,1:5) = ad(iii,1:5)-dflux_dQnodek(iii,1:5)*coltag(iii,rn)
             end do
           endif

           do m = 1, 5
             res(1:5,node,i) = res(1:5,node,i) - dflux_dQnodek(m,1:5)*Rlamb(m,i)
           end do
         end do
       endif res_contribs3

       if ( fill_a ) then

! dR2/dQ1

         ioff21 = 0
         jstart = ia(node1)
         jend   = ia(node1+1)-1
         search2 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff21 = j
             exit search2
           endif
         end do search2

         if ( ioff21 == 0 ) then
           write(*,*) '2: Error finding off-diag 21', node1, node2
           call lmpi_die
         endif

         a(1:5,1:5,ioff21) = a(1:5,1:5,ioff21) + dflux_dQnode1(1:5,1:5)

! dR2/dQk with k going around node1

         ioff2k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search3 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff2k = j
             exit search3
           endif
         end do search3

         if ( ioff2k == 0 ) then
           write(*,*) '3: Error finding off-diag 2k', node, node2
           call lmpi_die
         endif

         a(1:5,1:5,ioff2k) = a(1:5,1:5,ioff2k) - dflux_dQnodek(1:5,1:5)
       endif

      enddo loop_2000
      endif

! Now do circuit around node 2

      dxR = my_haf*second*(x(node1) - x(node2))
      dyR = my_haf*second*(y(node1) - y(node2))
      dzR = my_haf*second*(z(node1) - z(node2))

      if(node2 <= nnodes0 .and. (.not.right_state_firstorder) ) then
      loop_2010 : do ii = 1, neighbors(node2)%n

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(2,1:5)*Rlam(1:5,node2,i)
          end do
        endif

        node = neighbors(node2)%list(ii) ! This is node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

        dx1 = x(node) - x(node2)
        dy1 = y(node) - y(node2)
        dz1 = z(node) - z(node2)

! We now have the linearizations of the residual at node2 with respect
! to Q at node2 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node2 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node2 and nodek).

        terms(:) = lstgs_func(dx1,         dy1,         dz1,                   &
                              rr11(node2), rr12(node2), rr13(node2),           &
                              rr22(node2), rr23(node2), rr33(node2))
        swx(:) = terms(1)
        swy(:) = terms(2)
        swz(:) = terms(3)

        if (symmetry_bcs) then
          call lstgs_sym(symmetry(node2),                                      &
                         dx1,         dy1,         dz1,                        &
                         rr11(node2), rr12(node2), rr13(node2),                &
                         rr22(node2), rr23(node2), rr33(node2),                &
                         n_cont, swx, swy, swz )
        end if

        if( node == node2 )then
          swx = my_0
          swy = my_0
          swz = my_0
        end if

        weight_matrix(1:5) = phi(1:5,node2)*(my_1-kappa_umuscl)                &
                                       *(swx(1:5)*dxR+swy(1:5)*dyR+swz(1:5)*dzR)
        do j = 1, 5
        dfm_primitive_times_weights(1:5,j)=dfm_primitive(1:5,j)*weight_matrix(j)
        end do

        M2_inverse = setup_t(qnode(1,node2),qnode(2,node2),qnode(3,node2),     &
                             qnode(4,node2))

         dflux_dQnode2 = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnode2(i,1:5) = dflux_dQnode2(i,1:5)                       &
                       + dfm_primitive_times_weights(i,k)*M2_inverse(k,1:5)
           end do
         end do

         Mk_inverse = setup_t(qnode(1,node),qnode(2,node),qnode(3,node),       &
                              qnode(4,node))

         dflux_dQnodek = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnodek(i,1:5) = dflux_dQnodek(i,1:5)                       &
                       + dfm_primitive_times_weights(i,k)*Mk_inverse(k,1:5)
           end do
         end do

! Always add to the off-node and subtract from the central node
! Note that if node=node2 then the weight is zero so nothing happens

! dR2/dQ2

        res_contribs4 : if ( fill_res ) then
         if ( rn == node2 .and. np == node2 ) then
           do iii = 1, 5
             ad(iii,1:5) = ad(iii,1:5) + dflux_dQnode2(iii,1:5)*coltag(iii,rn)
           end do
         endif

         do i = 1, nfunctions
           do m = 1, 5
             res(1:5,node2,i) = res(1:5,node2,i)+dflux_dQnode2(m,1:5)*Rlamb(m,i)
           end do

! dR2/dQk

           if ( rn == node2 .and. np == node ) then
             do iii = 1, 5
               ad(iii,1:5) = ad(iii,1:5)-dflux_dQnodek(iii,1:5)*coltag(iii,rn)
             end do
           endif

           do m = 1, 5
             res(1:5,node,i) = res(1:5,node,i) - dflux_dQnodek(m,1:5)*Rlamb(m,i)
           end do
         end do

         do i = 1, nfunctions
           rlamb(1:5,i) = tag(1,1:5)*Rlam(1:5,node1,i)
         end do
        endif res_contribs4

        if ( fill_a ) then

! dR2/dQ2
          idiag2 = iau(node2)   ! where to put dR2/dQ2

          a(1:5,1:5,idiag2) = a(1:5,1:5,idiag2) + dflux_dQnode2(1:5,1:5)

! dR2/dQk

           ioff2k = 0
           jstart = ia(node)
           jend   = ia(node+1)-1
           search4 : do j = jstart, jend
             neighbor = abs(ja(j))
             if ( neighbor == node2 ) then
               ioff2k = j
               exit search4
             endif
           end do search4

           if ( ioff2k == 0 ) then
             write(*,*) '4: Error finding off-diag 2k', node, node2
             call lmpi_die
           endif

           a(1:5,1:5,ioff2k) = a(1:5,1:5,ioff2k) - dflux_dQnodek(1:5,1:5)
        endif

! We now have the linearizations of the residual at node1 with respect
! to Q at node2 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node1 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node2 and nodek).

! dR1/dQ2

       res_contribs5 : if ( fill_res ) then
         if ( rn == node1 .and. np == node2 ) then
           do iii = 1, 5
             ad(iii,1:5) = ad(iii,1:5) - dflux_dQnode2(iii,1:5)*coltag(iii,rn)
           end do
         endif

         do i = 1, nfunctions
           do m = 1, 5
             res(1:5,node2,i) = res(1:5,node2,i)-dflux_dQnode2(m,1:5)*Rlamb(m,i)
           end do

! dR1/dQk

           if ( rn == node1 .and. np == node ) then
             do iii = 1, 5
               ad(iii,1:5) = ad(iii,1:5)+dflux_dQnodek(iii,1:5)*coltag(iii,rn)
             end do
           endif

           do m = 1, 5
             res(1:5,node,i) = res(1:5,node,i) + dflux_dQnodek(m,1:5)*Rlamb(m,i)
           end do
         end do
       endif res_contribs5

       if ( fill_a ) then

! dR1/dQ2
         ioff12 = 0
         jstart = ia(node2)
         jend   = ia(node2+1)-1
         search5 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff12 = j
             exit search5
           endif
         end do search5

         if ( ioff12 == 0 ) then
           write(*,*) '5: Error finding off-diag 12', node2, node1
           call lmpi_die
         endif

         a(1:5,1:5,ioff12) = a(1:5,1:5,ioff12) - dflux_dQnode2(1:5,1:5)

! dR1/dQk

         ioff1k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search6 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff1k = j
             exit search6
           endif
         end do search6

         if ( ioff1k == 0 ) then
           write(*,*) '6: Error finding off-diag 1k', node, node1
           call lmpi_die
         endif

         a(1:5,1:5,ioff1k) = a(1:5,1:5,ioff1k) + dflux_dQnodek(1:5,1:5)
       endif

       enddo loop_2010
      endif

! Finally pick up the other piece of the UMUSCL term along the edge
! These could be collapsed a lot, but its nice to see where each piece
! explicitly comes from

! First lets do dR1/dQ1

! contributions from left side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(-1.0_dp)

        do j = 1, 5
        dfp_primitive_times_weights(1:5,j)=dfp_primitive(1:5,j)*weight_matrix(j)
        end do

        M1_inverse = setup_t(qnode(1,node1),qnode(2,node1),qnode(3,node1),     &
                             qnode(4,node1))

        dR1_dQ1 = 0.0_dp

        do i = 1, 5
          do k = 1, 5
            dR1_dQ1(i,1:5) = dR1_dQ1(i,1:5)                                    &
                            + dfp_primitive_times_weights(i,k)*M1_inverse(k,1:5)
          end do
        end do

! contributions from right side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(1.0_dp)

        do j = 1, 5
        dfm_primitive_times_weights(1:5,j)=dfm_primitive(1:5,j)*weight_matrix(j)
        end do

        do i = 1, 5
          do k = 1, 5
            dR1_dQ1(i,1:5) = dR1_dQ1(i,1:5)                                    &
                            + dfm_primitive_times_weights(i,k)*M1_inverse(k,1:5)
          end do
        end do

! Note that dR2/dQ1 just has the sign flipped

        dR2_dQ1 = -dR1_dQ1

! Now lets do dR1/dQ2

! contributions from left side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(1.0_dp)

        do j = 1, 5
        dfp_primitive_times_weights(1:5,j)=dfp_primitive(1:5,j)*weight_matrix(j)
        end do

        M2_inverse = setup_t(qnode(1,node2),qnode(2,node2),qnode(3,node2),     &
                             qnode(4,node2))

        dR1_dQ2 = 0.0_dp

        do i = 1, 5
          do k = 1, 5
            dR1_dQ2(i,1:5) = dR1_dQ2(i,1:5)                                    &
                            + dfp_primitive_times_weights(i,k)*M2_inverse(k,1:5)
          end do
        end do

! contributions from right side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(-1.0_dp)

        do j = 1, 5
        dfm_primitive_times_weights(1:5,j)=dfm_primitive(1:5,j)*weight_matrix(j)
        end do

        do i = 1, 5
          do k = 1, 5
            dR1_dQ2(i,1:5) = dR1_dQ2(i,1:5)                                    &
                            + dfm_primitive_times_weights(i,k)*M2_inverse(k,1:5)
          end do
        end do

! Note that dR2/dQ2 just has the sign flipped

        dR2_dQ2 = -dR1_dQ2

! Now that we have the little blocks, we can transpose and multiply by lambda
! and send to the residual

        res_contribs6 : if ( fill_res ) then
        do i = 1, nfunctions
          rlamb(1:5,i) = tag(1,1:5)*Rlam(1:5,node1,i)

          if ( node1 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node1,i) = res(1:5,node1,i) + dR1_dQ1(m,1:5)*Rlamb(m,i)
            end do
          endif

          if ( node2 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node2,i) = res(1:5,node2,i) + dR1_dQ2(m,1:5)*Rlamb(m,i)
            end do
          endif

          rlamb(1:5,i) = tag(2,1:5)*Rlam(1:5,node2,i)

          if ( node1 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node1,i) = res(1:5,node1,i) + dR2_dQ1(m,1:5)*Rlamb(m,i)
            end do
          endif

          if ( node2 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node2,i) = res(1:5,node2,i) + dR2_dQ2(m,1:5)*Rlamb(m,i)
            end do
          endif
        end do
        endif res_contribs6

        if ( fill_a ) then
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          idiag2 = iau(node2)   ! where to put dR2/dQ2
          ioff12 = fhelp(2,n)   ! where to put dR1/dQ2
          ioff21 = fhelp(1,n)   ! where to put dR2/dQ1

! dR1/dQ1
          if(node1<=nnodes0)a(1:5,1:5,idiag1)=a(1:5,1:5,idiag1)+dR1_dQ1(1:5,1:5)

! dR1/dQ2
          if(node2<=nnodes0)a(1:5,1:5,ioff12)=a(1:5,1:5,ioff12)+dR1_dQ2(1:5,1:5)

! dR2/dQ2
          if(node2<=nnodes0)a(1:5,1:5,idiag2)=a(1:5,1:5,idiag2)+dR2_dQ2(1:5,1:5)

! dR2/dQ1
          if(node1<=nnodes0)a(1:5,1:5,ioff21)=a(1:5,1:5,ioff21)+dR2_dQ1(1:5,1:5)
        endif

      endif second_order_contribs

      endif local_node

    enddo edge_loop

  end subroutine atlam_roe

!================================ ATLAM_VL ===================================80
!
!  This does A-transpose times lambda at time-level n
!  for part of the residual (dI/dQ is the other part)
!
!  Puts the result in res
!
!  van Leer
!
!  Expects qnode in primitive
!  Returns qnode in primitive
!
!=============================================================================80
  subroutine atlam_vl(nnodes0,nnodes01,x,y,z,symmetry,rr11,rr22,rr33,rr12,rr13,&
                      rr23,qnode,gradx,grady,gradz,nedge,nedgeloc,eptr,rarea,  &
                      xn,yn,zn,ia,ja,phi,n_tot,adim,n_grd,nfunctions,res,rlam, &
                      coltag,fhelp,iau,a)

    use inviscid_flux,       only : first_order_iterations
    use fluid,               only : gm1, gamma, ggm1
    use debug_defs,          only : symmetry_bcs
    use info_depr,           only : ntt,kappa_umuscl
    use reconstruction,      only : lstgs_sym
    use lmpi,                only : lmpi_die, lmpi_master
    use design_types,        only : max_functions
    use grid_motion_helpers, only : need_grid_velocity

    integer, intent(in) :: nnodes0,nnodes01,n_tot,adim,n_grd,nedge
    integer, intent(in) :: nedgeloc
    integer, intent(in), optional :: nfunctions

    integer, dimension(nnodes01), intent(in) :: symmetry
    integer, dimension(2,nedge),  intent(in) :: eptr
    integer, dimension(:),        intent(in) :: ia, ja
    integer, dimension(:),        intent(in), optional :: iau
    integer, dimension(:,:),      intent(in), optional :: fhelp

    real(dp), dimension(nnodes01),       intent(in) :: x, y, z
    real(dp), dimension(nnodes0),        intent(in) :: rr11
    real(dp), dimension(nnodes0),        intent(in) :: rr22
    real(dp), dimension(nnodes0),        intent(in) :: rr33
    real(dp), dimension(nnodes0),        intent(in) :: rr12
    real(dp), dimension(nnodes0),        intent(in) :: rr13
    real(dp), dimension(nnodes0),        intent(in) :: rr23
    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(n_grd,nnodes01), intent(in) :: gradx
    real(dp), dimension(n_grd,nnodes01), intent(in) :: grady
    real(dp), dimension(n_grd,nnodes01), intent(in) :: gradz
    real(dp), dimension(n_grd,nnodes01), intent(in) :: phi
    real(dp), dimension(nedge),          intent(in) :: rarea
    real(dp), dimension(nedge),          intent(in) :: xn, yn, zn
    real(dp), dimension(adim,nnodes01),  intent(in),    optional :: coltag
    real(dp), dimension(:,:,:),          intent(in),    optional :: rlam
    real(dp), dimension(:,:,:),          intent(inout), optional :: res
    real(dp), dimension(:,:,:),          intent(inout), optional :: a

    integer :: n, node1, node2, ii, node, i, j, k, idiag1, idiag2, ioff21
    integer :: ioff1k, jstart, jend, neighbor, ioff2k, ioff12, m
    integer, parameter :: n_cont = 5

    real(dp) :: rho,u,v,w,q2,enrgy,c,ubar,fmach,ubp2a
    real(dp) :: fluxp1,fluxp2,fluxp3,fluxp4,fluxp5,pressl,pressr
    real(dp) :: fluxm1,fluxm2,fluxm3,fluxm4,fluxm5
    real(dp) :: dcdr,dcdru,dcdrv,dcdrw,dcde,rx,ry,rz
    real(dp) :: dfdq1,dfdq2,dfdq3,dfdq4,dfdq5,fof,c1,c2
    real(dp) :: phie,ubm2a,second
    real(dp) :: xmean,ymean,zmean,xnorm,ynorm,znorm,area
    real(dp) :: rhol,ul,vl,wl
    real(dp) :: rhor,ur,vr,wr
    real(dp) :: dxL,dyL,dzL,dxR,dyR,dzR
    real(dp) :: dx1,dy1,dz1

    real(dp), dimension(5,5) :: dfp, dfm
    real(dp), dimension(5,5) :: dfp_primitive, dfm_primitive
    real(dp), dimension(5,5) :: B, CC
    real(dp), dimension(2,5) :: tag
    real(dp), dimension(n_cont) :: swx, swy, swz
    real(dp), dimension(5,max_functions) :: rlamb
    real(dp), dimension(3)   :: terms
    real(dp), dimension(5)   :: weight_matrix
    real(dp), dimension(5,5) :: M1_inverse
    real(dp), dimension(5,5) :: M2_inverse
    real(dp), dimension(5,5) :: Mk_inverse
    real(dp), dimension(5,5) :: dfp_primitive_times_weights
    real(dp), dimension(5,5) :: dfm_primitive_times_weights
    real(dp), dimension(5,5) :: dflux_dQnode1
    real(dp), dimension(5,5) :: dflux_dQnode2
    real(dp), dimension(5,5) :: dflux_dQnodek
    real(dp), dimension(5,5) :: T,ti
    real(dp), dimension(5,5) :: dR1_dQ1, dR1_dQ2
    real(dp), dimension(5,5) :: dR2_dQ1, dR2_dQ2

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: second_ord_limit = 0.01_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp

    logical :: left_state_firstorder, right_state_firstorder
    logical :: fill_res, fill_a

  continue

    if ( need_grid_velocity ) then
      write(*,*) 'atlam_vl not set up with facespeeds'
      call lmpi_die
    endif

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) .or.                   &
           .not.present(nfunctions) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_vl, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if ( .not.present(iau) .or. .not.present(fhelp) ) then
        if ( lmpi_master ) then
          write(*,*) 'a requested in atlam_vl, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    tag=0.0_dp

    if ( .not. neighbors_found ) call find_neighbors(nnodes01,nedgeloc,eptr)

    second = 1.0_dp
    if(ntt <= first_order_iterations)second = 0.0_dp

    edge_loop_1030 : do n = 1, nedgeloc

          node1 = eptr(1,n)
          node2 = eptr(2,n)

          if(node1 <= nnodes0 .or. node2 <= nnodes0) then

          if ( fill_res ) then
            tag(1,1:5) = coltag(1:5,node1)
            tag(2,1:5) = coltag(1:5,node2)
          endif

! Get unit normals and area

          xnorm = xn(n)
          ynorm = yn(n)
          znorm = zn(n)
          area  = rarea(n)

          xmean = 0.5_dp*(x(node1) + x(node2))
          ymean = 0.5_dp*(y(node1) + y(node2))
          zmean = 0.5_dp*(z(node1) + z(node2))

          rx = second*(xmean - x(node1))
          ry = second*(ymean - y(node1))
          rz = second*(zmean - z(node1))

! Take care of left side

          left_state_firstorder = .false.

          rhol  = qnode(1,node1)                                               &
                + phi(1,node1)*((my_1-kappa_umuscl)*gradx(1,node1)*rx          &
                +               (my_1-kappa_umuscl)*grady(1,node1)*ry          &
                +               (my_1-kappa_umuscl)*gradz(1,node1)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(1,node2)-qnode(1,node1)))
          ul    = qnode(2,node1)                                               &
                + phi(2,node1)*((my_1-kappa_umuscl)*gradx(2,node1)*rx          &
                +               (my_1-kappa_umuscl)*grady(2,node1)*ry          &
                +               (my_1-kappa_umuscl)*gradz(2,node1)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(2,node2)-qnode(2,node1)))
          vl    = qnode(3,node1)                                               &
                + phi(3,node1)*((my_1-kappa_umuscl)*gradx(3,node1)*rx          &
                +               (my_1-kappa_umuscl)*grady(3,node1)*ry          &
                +               (my_1-kappa_umuscl)*gradz(3,node1)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(3,node2)-qnode(3,node1)))
          wl    = qnode(4,node1)                                               &
                + phi(4,node1)*((my_1-kappa_umuscl)*gradx(4,node1)*rx          &
                +               (my_1-kappa_umuscl)*grady(4,node1)*ry          &
                +               (my_1-kappa_umuscl)*gradz(4,node1)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(4,node2)-qnode(4,node1)))
          pressl = qnode(5,node1)                                              &
                + phi(5,node1)*((my_1-kappa_umuscl)*gradx(5,node1)*rx          &
                +               (my_1-kappa_umuscl)*grady(5,node1)*ry          &
                +               (my_1-kappa_umuscl)*gradz(5,node1)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(5,node2)-qnode(5,node1)))

! Catastrophic limiter
! Note the derivatives do not change

          if (rhol <= my_0 .or. pressl <= my_0) then
            left_state_firstorder = .true.
            rhol   = qnode(1,node1)
            ul    = qnode(2,node1)
            vl    = qnode(3,node1)
            wl    = qnode(4,node1)
            pressl = qnode(5,node1)
          end if

          q2    = ul*ul + vl*vl + wl*wl
          enrgy = pressl/gm1 + 0.5_dp*rhol*q2

          c     = sqrt(gamma*gm1*(enrgy - 0.5_dp*rhol*q2)/rhol)
          ubar  = xnorm*ul + ynorm*vl + znorm*wl
          fmach = ubar/c
          ubp2a = -ubar + my_2*c

! If subsonic calculate df+ and add contribution to A

          if(abs(fmach) <  1.0_dp)then

! First get fluxes

           fluxp1 = area*0.25_dp*rhol*c*(fmach + 1.0_dp)**2
           fluxp2 = fluxp1*(xnorm*ubp2a/gamma + ul)
           fluxp3 = fluxp1*(ynorm*ubp2a/gamma + vl)
           fluxp4 = fluxp1*(znorm*ubp2a/gamma + wl)
           fluxp5 = fluxp1*((-gm1*ubar*ubar + my_2*gm1*ubar*c + my_2*c*c)      &
                           /(gamma*gamma - 1.0_dp) + 0.5_dp*q2)

! Derivatives of speed of sound

           dcdr  =  0.5_dp*ggm1*(q2 - enrgy/rhol)/(rhol*c)
           dcdru = -0.5_dp*ggm1*ul/(rhol*c)
           dcdrv = -0.5_dp*ggm1*vl/(rhol*c)
           dcdrw = -0.5_dp*ggm1*wl/(rhol*c)
           dcde  =  0.5_dp*ggm1/(rhol*c)

! If subsonic calculate df+ and add contribution to A

           dfdq1  = 0.25_dp*area*(1.0_dp - fmach**2)*(c + rhol*dcdr)
           dfdq2  = 0.25_dp*area*(my_2*xnorm*(fmach + 1.0_dp)                  &
                  + rhol*(1.0_dp - fmach**2)*dcdru)
           dfdq3  = 0.25_dp*area*(my_2*ynorm*(fmach + 1.0_dp)                  &
                  + rhol*(1.0_dp - fmach**2)*dcdrv)
           dfdq4  = 0.25_dp*area*(my_2*znorm*(fmach + 1.0_dp)                  &
                  + rhol*(1.0_dp - fmach**2)*dcdrw)
           dfdq5  = 0.25_dp*area*rhol*dcde*(1.0_dp - fmach**2)

           dfp(1,1) = dfdq1
           dfp(1,2) = dfdq2
           dfp(1,3) = dfdq3
           dfp(1,4) = dfdq4
           dfp(1,5) = dfdq5

           fof = fluxp2/fluxp1
           dfp(2,1) = fluxp1*(my_2*xnorm/gamma*dcdr                            &
                      + xnorm*ubar/gamma/rhol - ul/rhol) + fof*dfdq1
           dfp(2,2) = fluxp1*(-xnorm*xnorm/gamma/rhol                          &
                      + my_2*xnorm*dcdru/gamma + 1.0_dp/rhol) + fof*dfdq2
           dfp(2,3) = fluxp1*(-xnorm*ynorm/gamma/rhol                          &
                      + my_2*xnorm*dcdrv/gamma) + fof*dfdq3
           dfp(2,4) = fluxp1*(-xnorm*znorm/gamma/rhol                          &
                      + my_2*xnorm*dcdrw/gamma) + fof*dfdq4
           dfp(2,5) = fluxp1*(2*xnorm*dcde/gamma)                              &
                       + fof*dfdq5

           fof = fluxp3/fluxp1
           dfp(3,1) = fluxp1*(my_2*ynorm/gamma*dcdr                            &
                      + ynorm*ubar/gamma/rhol - vl/rhol) + fof*dfdq1
           dfp(3,2) = fluxp1*(-ynorm*xnorm/gamma/rhol                          &
                      + my_2*ynorm*dcdru/gamma) + fof*dfdq2
           dfp(3,3) = fluxp1*(-ynorm*ynorm/gamma/rhol                          &
                      + my_2*ynorm*dcdrv/gamma + 1.0_dp/rhol) + fof*dfdq3
           dfp(3,4) = fluxp1*(-ynorm*znorm/gamma/rhol                          &
                      + my_2*ynorm*dcdrw/gamma) + fof*dfdq4
           dfp(3,5) = fluxp1*(my_2*ynorm*dcde/gamma)                           &
                      + fof*dfdq5

           fof = fluxp4/fluxp1
           dfp(4,1) = fluxp1*(my_2*znorm/gamma*dcdr                            &
                      + znorm*ubar/gamma/rhol - wl/rhol) + fof*dfdq1
           dfp(4,2) = fluxp1*(-znorm*xnorm/gamma/rhol                          &
                      + my_2*znorm*dcdru/gamma) + fof*dfdq2
           dfp(4,3) = fluxp1*(-znorm*ynorm/gamma/rhol                          &
                      + my_2*znorm*dcdrv/gamma) + fof*dfdq3
           dfp(4,4) = fluxp1*(-znorm*znorm/gamma/rhol                          &
                      + my_2*znorm*dcdrw/gamma + 1.0_dp/rhol) + fof*dfdq4
           dfp(4,5) = fluxp1*(my_2*znorm*dcde/gamma)                           &
                       + fof*dfdq5

           c1 = (my_2*gm1*ubar + 4.0_dp*c)/(gamma*gamma - 1.0_dp)
           c2 = my_2*gm1/(gamma*gamma - 1.0_dp)*(-ubar + c)/rhol
           fof = fluxp5/fluxp1

           dfp(5,1) = fluxp1*(dcdr*c1                                          &
                      - c2*ubar - q2/rhol) + fof*dfdq1
           dfp(5,2) = fluxp1*(dcdru*c1                                         &
                      + c2*xnorm + ul/rhol) + fof*dfdq2
           dfp(5,3) = fluxp1*(dcdrv*c1                                         &
                      + c2*ynorm + vl/rhol) + fof*dfdq3
           dfp(5,4) = fluxp1*(dcdrw*c1                                         &
                      + c2*znorm + wl/rhol) + fof*dfdq4
           dfp(5,5) = fluxp1*dcde*c1 + fof*dfdq5

           else if(fmach >= 1.0_dp)then
            phie = 0.5_dp*gm1*q2
            dfp(1,1) = 0.0_dp
            dfp(1,2) = area*xnorm
            dfp(1,3) = area*ynorm
            dfp(1,4) = area*znorm
            dfp(1,5) = 0.0_dp

            dfp(2,1) = area*(xnorm*phie - ul*ubar)
            dfp(2,2) = area*(xnorm*(my_2 - gamma)*ul + ubar)
            dfp(2,3) = area*(ynorm*ul - xnorm*gm1*vl)
            dfp(2,4) = area*(znorm*ul - xnorm*gm1*wl)
            dfp(2,5) = area*xnorm*gm1

            dfp(3,1) = area*(ynorm*phie - vl*ubar)
            dfp(3,2) = area*(xnorm*vl - ynorm*gm1*ul)
            dfp(3,3) = area*(ynorm*(my_2 - gamma)*vl + ubar)
            dfp(3,4) = area*(znorm*vl - ynorm*gm1*wl)
            dfp(3,5) = area*ynorm*gm1

            dfp(4,1) = area*(znorm*phie - wl*ubar)
            dfp(4,2) = area*(xnorm*wl - znorm*gm1*ul)
            dfp(4,3) = area*(ynorm*wl - znorm*gm1*vl)
            dfp(4,4) = area*(znorm*(my_2 - gamma)*wl + ubar)
            dfp(4,5) = area*znorm*gm1

            dfp(5,1) = area*(my_2*phie - gamma*enrgy/rhol)*ubar
            dfp(5,2) = area*(xnorm*(gamma*enrgy/rhol - phie)                   &
                         - gm1*ul*ubar)
            dfp(5,3) = area*(ynorm*(gamma*enrgy/rhol - phie)                   &
                         - gm1*vl*ubar)
            dfp(5,4) = area*(znorm*(gamma*enrgy/rhol - phie)                   &
                         - gm1*wl*ubar)
            dfp(5,5) = area*gamma*ubar

           else
            dfp(:,:) = 0.0_dp
          end if

! Now do dfm

          rx = second*(xmean - x(node2))
          ry = second*(ymean - y(node2))
          rz = second*(zmean - z(node2))

          right_state_firstorder = .false.

          rhor   = qnode(1,node2)                                              &
                + phi(1,node2)*((my_1-kappa_umuscl)*gradx(1,node2)*rx          &
                +               (my_1-kappa_umuscl)*grady(1,node2)*ry          &
                +               (my_1-kappa_umuscl)*gradz(1,node2)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(1,node1)-qnode(1,node2)))
          ur    = qnode(2,node2)                                               &
                + phi(2,node2)*((my_1-kappa_umuscl)*gradx(2,node2)*rx          &
                +               (my_1-kappa_umuscl)*grady(2,node2)*ry          &
                +               (my_1-kappa_umuscl)*gradz(2,node2)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(2,node1)-qnode(2,node2)))
          vr    = qnode(3,node2)                                               &
                + phi(3,node2)*((my_1-kappa_umuscl)*gradx(3,node2)*rx          &
                +               (my_1-kappa_umuscl)*grady(3,node2)*ry          &
                +               (my_1-kappa_umuscl)*gradz(3,node2)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(3,node1)-qnode(3,node2)))
          wr    = qnode(4,node2)                                               &
                + phi(4,node2)*((my_1-kappa_umuscl)*gradx(4,node2)*rx          &
                +               (my_1-kappa_umuscl)*grady(4,node2)*ry          &
                +               (my_1-kappa_umuscl)*gradz(4,node2)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(4,node1)-qnode(4,node2)))
          pressr= qnode(5,node2)                                               &
                + phi(5,node2)*((my_1-kappa_umuscl)*gradx(5,node2)*rx          &
                +               (my_1-kappa_umuscl)*grady(5,node2)*ry          &
                +               (my_1-kappa_umuscl)*gradz(5,node2)*rz          &
                + second*kappa_umuscl*my_haf*(qnode(5,node1)-qnode(5,node2)))

! Catastrophic limiter
! Note the derivatives do not change

          if (rhor <= my_0 .or. pressr <= my_0) then
            right_state_firstorder = .true.
            rhor   = qnode(1,node2)
            ur    = qnode(2,node2)
            vr    = qnode(3,node2)
            wr    = qnode(4,node2)
            pressr= qnode(5,node2)
          end if

          q2    = ur*ur + vr*vr + wr*wr
          enrgy = pressr/gm1 + .5_dp*rhor*q2

          c     = sqrt(gamma*gm1*(enrgy - 0.5_dp*rhor*q2)/rhor)
          ubar  = xnorm*ur + ynorm*vr + znorm*wr
          fmach = ubar/c
          ubm2a = -ubar - my_2*c

! If subsonic calculate df+ and add contribution to A

          if(abs(fmach) <  1.0_dp)then

! Get fluxes

           fluxm1 = -area*0.25_dp*rhor*c*(fmach - 1.0_dp)**2
           fluxm2 = fluxm1*(xnorm*ubm2a/gamma + ur)
           fluxm3 = fluxm1*(ynorm*ubm2a/gamma + vr)
           fluxm4 = fluxm1*(znorm*ubm2a/gamma + wr)
           fluxm5 = fluxm1*((-gm1*ubar*ubar                                    &
                        - my_2*gm1*ubar*c + my_2*c*c)/(gamma*gamma - 1.0_dp)   &
                        + 0.5_dp*q2)

! Derivatives of speed of sound

           dcdr  =  0.5_dp*ggm1*(q2 - enrgy/rhor)/(rhor*c)
           dcdru = -0.5_dp*ggm1*ur/(rhor*c)
           dcdrv = -0.5_dp*ggm1*vr/(rhor*c)
           dcdrw = -0.5_dp*ggm1*wr/(rhor*c)
           dcde  =  0.5_dp*ggm1/(rhor*c)

! If subsonic calculate df- and subtract contribution to A

            dfdq1 = -0.25_dp*area*(1.0_dp - fmach**2)*(c + rhor*dcdr)
            dfdq2 = -0.25_dp*area*(my_2*xnorm*(fmach - 1.0_dp)                 &
                  + rhor*(1.0_dp - fmach**2)*dcdru)
            dfdq3 = -0.25_dp*area*(my_2*ynorm*(fmach - 1.0_dp)                 &
                  + rhor*(1.0_dp - fmach**2)*dcdrv)
            dfdq4 = -0.25_dp*area*(my_2*znorm*(fmach - 1.0_dp)                 &
                  + rhor*(1.0_dp - fmach**2)*dcdrw)
            dfdq5 = -0.25_dp*area*rhor*dcde*(1.0_dp - fmach**2)

            dfm(1,1) = dfdq1
            dfm(1,2) = dfdq2
            dfm(1,3) = dfdq3
            dfm(1,4) = dfdq4
            dfm(1,5) = dfdq5

            fof = fluxm2/fluxm1
            dfm(2,1) = fluxm1*(-my_2*xnorm/gamma*dcdr                          &
                       + xnorm*ubar/gamma/rhor - ur/rhor) + fof*dfdq1
            dfm(2,2) = fluxm1*(-xnorm*xnorm/gamma/rhor                         &
                       - my_2*xnorm*dcdru/gamma + 1.0_dp/rhor) + fof*dfdq2
            dfm(2,3) = fluxm1*(-xnorm*ynorm/gamma/rhor                         &
                       - my_2*xnorm*dcdrv/gamma) + fof*dfdq3
            dfm(2,4) = fluxm1*(-xnorm*znorm/gamma/rhor                         &
                       - my_2*xnorm*dcdrw/gamma) + fof*dfdq4
            dfm(2,5) = fluxm1*(-2*xnorm*dcde/gamma)                            &
                       + fof*dfdq5

            fof = fluxm3/fluxm1
            dfm(3,1) = fluxm1*(-my_2*ynorm/gamma*dcdr                          &
                       + ynorm*ubar/gamma/rhor - vr/rhor) + fof*dfdq1
            dfm(3,2) = fluxm1*(-ynorm*xnorm/gamma/rhor                         &
                       - my_2*ynorm*dcdru/gamma) + fof*dfdq2
            dfm(3,3) = fluxm1*(-ynorm*ynorm/gamma/rhor                         &
                       - my_2*ynorm*dcdrv/gamma + 1.0_dp/rhor) + fof*dfdq3
            dfm(3,4) = fluxm1*(-ynorm*znorm/gamma/rhor                         &
                       - my_2*ynorm*dcdrw/gamma) + fof*dfdq4
            dfm(3,5) = fluxm1*(-my_2*ynorm*dcde/gamma)                         &
                       + fof*dfdq5

            fof = fluxm4/fluxm1
            dfm(4,1) = fluxm1*(-my_2*znorm/gamma*dcdr                          &
                       + znorm*ubar/gamma/rhor - wr/rhor) + fof*dfdq1
            dfm(4,2) = fluxm1*(-znorm*xnorm/gamma/rhor                         &
                       - my_2*znorm*dcdru/gamma) + fof*dfdq2
            dfm(4,3) = fluxm1*(-znorm*ynorm/gamma/rhor                         &
                       - my_2*znorm*dcdrv/gamma) + fof*dfdq3
            dfm(4,4) = fluxm1*(-znorm*znorm/gamma/rhor                         &
                       - my_2*znorm*dcdrw/gamma + 1.0_dp/rhor) + fof*dfdq4
            dfm(4,5) = fluxm1*(-my_2*znorm*dcde/gamma)                         &
                       + fof*dfdq5

            c1 = (-my_2*gm1*ubar + 4.0_dp*c)/(gamma*gamma - 1.0_dp)
            c2 = my_2*gm1/(gamma*gamma - 1.0_dp)*(-ubar - c)/rhor
            fof = fluxm5/fluxm1

            dfm(5,1) = fluxm1*(dcdr*c1                                         &
                       - c2*ubar - q2/rhor) + fof*dfdq1
            dfm(5,2) = fluxm1*(dcdru*c1                                        &
                       + c2*xnorm + ur/rhor) + fof*dfdq2
            dfm(5,3) = fluxm1*(dcdrv*c1                                        &
                       + c2*ynorm + vr/rhor) + fof*dfdq3
            dfm(5,4) = fluxm1*(dcdrw*c1                                        &
                       + c2*znorm + wr/rhor) + fof*dfdq4
            dfm(5,5) = fluxm1*dcde*c1 + fof*dfdq5

           else if(fmach <= -1.0_dp)then
            phie = 0.5_dp*gm1*q2
            dfm(1,1) = 0.0_dp
            dfm(1,2) = area*xnorm
            dfm(1,3) = area*ynorm
            dfm(1,4) = area*znorm
            dfm(1,5) = 0.0_dp

            dfm(2,1) = area*(xnorm*phie - ur*ubar)
            dfm(2,2) = area*(xnorm*(my_2 - gamma)*ur + ubar)
            dfm(2,3) = area*(ynorm*ur - xnorm*gm1*vr)
            dfm(2,4) = area*(znorm*ur - xnorm*gm1*wr)
            dfm(2,5) = area*xnorm*gm1

            dfm(3,1) = area*(ynorm*phie - vr*ubar)
            dfm(3,2) = area*(xnorm*vr - ynorm*gm1*ur)
            dfm(3,3) = area*(ynorm*(my_2 - gamma)*vr + ubar)
            dfm(3,4) = area*(znorm*vr - ynorm*gm1*wr)
            dfm(3,5) = area*ynorm*gm1

            dfm(4,1) = area*(znorm*phie - wr*ubar)
            dfm(4,2) = area*(xnorm*wr - znorm*gm1*ur)
            dfm(4,3) = area*(ynorm*wr - znorm*gm1*vr)
            dfm(4,4) = area*(znorm*(my_2 - gamma)*wr + ubar)
            dfm(4,5) = area*znorm*gm1

            dfm(5,1) = area*(my_2*phie - gamma*enrgy/rhor)*ubar
            dfm(5,2) = area*(xnorm*(gamma*enrgy/rhor - phie)                   &
                         - gm1*ur*ubar)
            dfm(5,3) = area*(ynorm*(gamma*enrgy/rhor - phie)                   &
                         - gm1*vr*ubar)
            dfm(5,4) = area*(znorm*(gamma*enrgy/rhor - phie)                   &
                         - gm1*wr*ubar)
            dfm(5,5) = area*gamma*ubar

           else
            dfm(:,:) = 0.0_dp
         end if

! Take care of node1

       if ( fill_res ) then
        do i = 1, nfunctions
          rlamb(1:5,i) = tag(1,1:5)*rlam(1:5,node1,i)
        end do
       endif

! Compute (M_left)*(M(inverse)_node1)  This is for dQ(left)/dQ_1

        rho = qnode(1,node1)
        u   = qnode(2,node1)
        v   = qnode(3,node1)
        w   = qnode(4,node1)

        ti = setup_t_inverse(rhol,ul,vl,wl)
        t = setup_t(rho,u,v,w)

        t = matmul(ti,t)
        B = matmul(dfp,t)

       if ( fill_res ) then
         if(node1 <= nnodes0) then
           do i = 1, nfunctions
             do m = 1, 5
               res(1:5,node1,i) = res(1:5,node1,i) + B(m,1:5)*rlamb(m,i)
             end do
           end do
         endif
       endif

! Now grab the offdiagonal

       if ( fill_res ) then
         do i = 1, nfunctions
           rlamb(1:5,i) = tag(1,1:5)*rlam(1:5,node1,i)
         end do
       endif

! Compute (M_right)*(M(inverse)_node2)  This is for dQ(right)/dQ_2

        rho = qnode(1,node2)
        u   = qnode(2,node2)
        v   = qnode(3,node2)
        w   = qnode(4,node2)

        ti = setup_t_inverse(rhor,ur,vr,wr)
        t = setup_t(rho,u,v,w)

        t = matmul(ti,t)
        cc = matmul(dfm,t)

        if ( fill_a ) then
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          idiag2 = iau(node2)   ! where to put dR2/dQ2
          ioff12 = fhelp(2,n)   ! where to put dR1/dQ2
          ioff21 = fhelp(1,n)   ! where to put dR2/dQ1

! dR1/dQ1
          if(node1<=nnodes0) a(1:5,1:5,idiag1) = a(1:5,1:5,idiag1) + B(1:5,1:5)
! dR1/dQ2
          if(node2<=nnodes0) a(1:5,1:5,ioff12) = a(1:5,1:5,ioff12) + CC(1:5,1:5)
! dR2/dQ2
          if(node2<=nnodes0) a(1:5,1:5,idiag2) = a(1:5,1:5,idiag2) - CC(1:5,1:5)
! dR2/dQ1
          if(node1<=nnodes0) a(1:5,1:5,ioff21) = a(1:5,1:5,ioff21) - B(1:5,1:5)
        endif


        res_contribs : if ( fill_res ) then
         if(node2 <= nnodes0) then
           do i = 1, nfunctions
             do m = 1, 5
               res(1:5,node2,i) = res(1:5,node2,i) + CC(m,1:5)*rlamb(m,i)
             end do
           end do
         endif

! Now do the second node

        do i = 1, nfunctions
          rlamb(1:5,i) = tag(2,1:5)*rlam(1:5,node2,i)
        end do

         if(node2 <= nnodes0) then
          do i = 1, nfunctions
            do m = 1, 5
              res(1:5,node2,i) = res(1:5,node2,i) - CC(m,1:5)*rlamb(m,i)
            end do
          end do
         endif

! Now grab the offdiagonal

        do i = 1, nfunctions
          rlamb(1:5,i) = tag(2,1:5)*rlam(1:5,node2,i)
        end do

         if(node1 <= nnodes0) then
          do i = 1, nfunctions
            do m = 1, 5
              res(1:5,node1,i) = res(1:5,node1,i) - B(m,1:5)*rlamb(m,i)
            end do
          end do
         endif
        endif res_contribs

! We have the flux jacobians wrt conservative in dfp and dfm
! Transform a copy to be wrt primitive which we'll need down below

         t = setup_t_inverse(rhol,ul,vl,wl)     ! Left transformation
         dfp_primitive = matmul(dfp,T)

         t = setup_t_inverse(rhor,ur,vr,wr)     ! Right transformation
         dfm_primitive = matmul(dfm,T)

! At this point, we have first order accuracy.  Now add contribution
! from second order terms.  We have taken into account the constant
! term in the reconstruction.  Now we need to add the pieces from the
! gradient terms.

      second_order_contribs : if( second > second_ord_limit) then

! First let's take care of all the nodes surrounding node1

        dxL = my_haf*second*(x(node2) - x(node1))
        dyL = my_haf*second*(y(node2) - y(node1))
        dzL = my_haf*second*(z(node2) - z(node1))

! Take care of the circuit of nodes around node 1

      if(node1 <= nnodes0 .and. (.not.left_state_firstorder) ) then
      loop_2000 : do ii = 1, neighbors(node1)%n

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(1,1:5)*Rlam(1:5,node1,i)
          end do
        endif

        node = neighbors(node1)%list(ii) ! This is node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

        dx1 = x(node) - x(node1)
        dy1 = y(node) - y(node1)
        dz1 = z(node) - z(node1)

        terms(:) = lstgs_func(dx1,         dy1,         dz1,                   &
                              rr11(node1), rr12(node1), rr13(node1),           &
                              rr22(node1), rr23(node1), rr33(node1))
        swx(:) = terms(1)
        swy(:) = terms(2)
        swz(:) = terms(3)

        if (symmetry_bcs) then
          call lstgs_sym(symmetry(node1),                                      &
                         dx1,         dy1,         dz1,                        &
                         rr11(node1), rr12(node1), rr13(node1),                &
                         rr22(node1), rr23(node1), rr33(node1),                &
                         n_cont, swx, swy, swz )
        end if

        if ( node == node1 ) then
          swx = 0.0_dp
          swy = 0.0_dp
          swz = 0.0_dp
        end if

        weight_matrix(1:5) = phi(1:5,node1)*(my_1-kappa_umuscl)                &
                                       *(swx(1:5)*dxL+swy(1:5)*dyL+swz(1:5)*dzL)

        do j = 1, 5
        dfp_primitive_times_weights(1:5,j)=dfp_primitive(1:5,j)*weight_matrix(j)
        end do

         M1_inverse = setup_t(qnode(1,node1),qnode(2,node1),qnode(3,node1),    &
                              qnode(4,node1))

         dflux_dQnode1 = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnode1(i,1:5) = dflux_dQnode1(i,1:5)                       &
                       + dfp_primitive_times_weights(i,k)*M1_inverse(k,1:5)
           end do
         end do

         Mk_inverse = setup_t(qnode(1,node),qnode(2,node),qnode(3,node),       &
                              qnode(4,node))

         dflux_dQnodek = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnodek(i,1:5) = dflux_dQnodek(i,1:5)                       &
                       + dfp_primitive_times_weights(i,k)*Mk_inverse(k,1:5)
           end do
         end do

! We now have the linearizations of the residual at node1 with respect
! to Q at node1 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node1 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node1 and nodek).

! Always add to the off-node and subtract from the central node
! Note that if node=node1 then the weight is zero so nothing happens

! dR1/dQ1

         res_contribs2 : if ( fill_res ) then
           do i = 1, nfunctions
             do m = 1, 5
               res(1:5,node1,i)=res(1:5,node1,i)-dflux_dQnode1(m,1:5)*Rlamb(m,i)
             end do

! dR1/dQk with k going around node1

             do m = 1, 5
               res(1:5,node,i) = res(1:5,node,i)+dflux_dQnodek(m,1:5)*Rlamb(m,i)
             end do
           end do
         endif res_contribs2

        if ( fill_a ) then

! dR1/dQ1
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          a(1:5,1:5,idiag1) = a(1:5,1:5,idiag1) - dflux_dQnode1(1:5,1:5)

! dR1/dQk with k going around node1

           ioff1k = 0
           jstart = ia(node)
           jend   = ia(node+1)-1
           search1 : do j = jstart, jend
             neighbor = abs(ja(j))
             if ( neighbor == node1 ) then
               ioff1k = j
               exit search1
             endif
           end do search1

           if ( ioff1k == 0 ) then
             write(*,*) '1: Error finding off-diag 1k', node, node1
             call lmpi_die
           endif

           a(1:5,1:5,ioff1k) = a(1:5,1:5,ioff1k) + dflux_dQnodek(1:5,1:5)
        endif

! Contribution from Jacobian for node 2

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(2,1:5)*Rlam(1:5,node2,i)
          end do
        endif

! We now have the linearizations of the residual at node2 with respect
! to Q at node1 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node2 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node1 and nodek).

! Now, subtract the weight for the "diagonal" and add it for the offdiagonal

! dR2/dQ1

       res_contribs3 : if ( fill_res ) then
         do i = 1, nfunctions
           do m = 1, 5
             res(1:5,node1,i) = res(1:5,node1,i)+dflux_dQnode1(m,1:5)*Rlamb(m,i)
           end do

! dR2/dQk with k going around node1

           do m = 1, 5
             res(1:5,node,i) = res(1:5,node,i) - dflux_dQnodek(m,1:5)*Rlamb(m,i)
           end do
         end do
       endif res_contribs3

       if ( fill_a ) then

! dR2/dQ1

         ioff21 = 0
         jstart = ia(node1)
         jend   = ia(node1+1)-1
         search2 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff21 = j
             exit search2
           endif
         end do search2

         if ( ioff21 == 0 ) then
           write(*,*) '2: Error finding off-diag 21', node1, node2
           call lmpi_die
         endif

         a(1:5,1:5,ioff21) = a(1:5,1:5,ioff21) + dflux_dQnode1(1:5,1:5)

! dR2/dQk with k going around node1

         ioff2k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search3 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff2k = j
             exit search3
           endif
         end do search3

         if ( ioff2k == 0 ) then
           write(*,*) '3: Error finding off-diag 2k', node, node2
           call lmpi_die
         endif

         a(1:5,1:5,ioff2k) = a(1:5,1:5,ioff2k) - dflux_dQnodek(1:5,1:5)
       endif

      enddo loop_2000
      endif

! Now do circuit around node 2

      dxR = my_haf*second*(x(node1) - x(node2))
      dyR = my_haf*second*(y(node1) - y(node2))
      dzR = my_haf*second*(z(node1) - z(node2))

      if(node2 <= nnodes0 .and. (.not.right_state_firstorder) ) then
      loop_2010 : do ii = 1, neighbors(node2)%n

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:5,i) = tag(2,1:5)*Rlam(1:5,node2,i)
          end do
        endif

        node = neighbors(node2)%list(ii) ! This is node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

        dx1 = x(node) - x(node2)
        dy1 = y(node) - y(node2)
        dz1 = z(node) - z(node2)

! We now have the linearizations of the residual at node2 with respect
! to Q at node2 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node2 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node2 and nodek).

        terms(:) = lstgs_func(dx1,         dy1,         dz1,                   &
                              rr11(node2), rr12(node2), rr13(node2),           &
                              rr22(node2), rr23(node2), rr33(node2))
        swx(:) = terms(1)
        swy(:) = terms(2)
        swz(:) = terms(3)

        if (symmetry_bcs) then
          call lstgs_sym(symmetry(node2),                                      &
                         dx1,         dy1,         dz1,                        &
                         rr11(node2), rr12(node2), rr13(node2),                &
                         rr22(node2), rr23(node2), rr33(node2),                &
                         n_cont, swx, swy, swz )
        end if

        if( node == node2 )then
          swx = my_0
          swy = my_0
          swz = my_0
        end if

        weight_matrix(1:5) = phi(1:5,node2)*(my_1-kappa_umuscl)                &
                                       *(swx(1:5)*dxR+swy(1:5)*dyR+swz(1:5)*dzR)
        do j = 1, 5
        dfm_primitive_times_weights(1:5,j)=dfm_primitive(1:5,j)*weight_matrix(j)
        end do

        M2_inverse = setup_t(qnode(1,node2),qnode(2,node2),qnode(3,node2),     &
                             qnode(4,node2))

         dflux_dQnode2 = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnode2(i,1:5) = dflux_dQnode2(i,1:5)                       &
                       + dfm_primitive_times_weights(i,k)*M2_inverse(k,1:5)
           end do
         end do

         Mk_inverse = setup_t(qnode(1,node),qnode(2,node),qnode(3,node),       &
                              qnode(4,node))

         dflux_dQnodek = 0.0_dp

         do i = 1, 5
           do k = 1, 5
             dflux_dQnodek(i,1:5) = dflux_dQnodek(i,1:5)                       &
                       + dfm_primitive_times_weights(i,k)*Mk_inverse(k,1:5)
           end do
         end do

! Always add to the off-node and subtract from the central node
! Note that if node=node2 then the weight is zero so nothing happens

! dR2/dQ2

        res_contribs4 : if ( fill_res ) then
         do i = 1, nfunctions
           do m = 1, 5
             res(1:5,node2,i) = res(1:5,node2,i)+dflux_dQnode2(m,1:5)*Rlamb(m,i)
           end do

! dR2/dQk

           do m = 1, 5
             res(1:5,node,i) = res(1:5,node,i) - dflux_dQnodek(m,1:5)*Rlamb(m,i)
           end do
         end do

         do i = 1, nfunctions
           rlamb(1:5,i) = tag(1,1:5)*Rlam(1:5,node1,i)
         end do
        endif res_contribs4

        if ( fill_a ) then

! dR2/dQ2
          idiag2 = iau(node2)   ! where to put dR2/dQ2

          a(1:5,1:5,idiag2) = a(1:5,1:5,idiag2) + dflux_dQnode2(1:5,1:5)

! dR2/dQk

           ioff2k = 0
           jstart = ia(node)
           jend   = ia(node+1)-1
           search4 : do j = jstart, jend
             neighbor = abs(ja(j))
             if ( neighbor == node2 ) then
               ioff2k = j
               exit search4
             endif
           end do search4

           if ( ioff2k == 0 ) then
             write(*,*) '4: Error finding off-diag 2k', node, node2
             call lmpi_die
           endif

           a(1:5,1:5,ioff2k) = a(1:5,1:5,ioff2k) - dflux_dQnodek(1:5,1:5)
        endif

! We now have the linearizations of the residual at node1 with respect
! to Q at node2 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node1 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node2 and nodek).

! dR1/dQ2

       res_contribs5 : if ( fill_res ) then
         do i = 1, nfunctions
           do m = 1, 5
             res(1:5,node2,i) = res(1:5,node2,i)-dflux_dQnode2(m,1:5)*Rlamb(m,i)
           end do

! dR1/dQk

           do m = 1, 5
             res(1:5,node,i) = res(1:5,node,i) + dflux_dQnodek(m,1:5)*Rlamb(m,i)
           end do
         end do
       endif res_contribs5

       if ( fill_a ) then

! dR1/dQ2
         ioff12 = 0
         jstart = ia(node2)
         jend   = ia(node2+1)-1
         search5 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff12 = j
             exit search5
           endif
         end do search5

         if ( ioff12 == 0 ) then
           write(*,*) '5: Error finding off-diag 12', node2, node1
           call lmpi_die
         endif

         a(1:5,1:5,ioff12) = a(1:5,1:5,ioff12) - dflux_dQnode2(1:5,1:5)

! dR1/dQk

         ioff1k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search6 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff1k = j
             exit search6
           endif
         end do search6

         if ( ioff1k == 0 ) then
           write(*,*) '6: Error finding off-diag 1k', node, node1
           call lmpi_die
         endif

         a(1:5,1:5,ioff1k) = a(1:5,1:5,ioff1k) + dflux_dQnodek(1:5,1:5)
       endif

       enddo loop_2010
      endif

! Finally pick up the other piece of the UMUSCL term along the edge
! These could be collapsed a lot, but its nice to see where each piece
! explicitly comes from

! First lets do dR1/dQ1

! contributions from left side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(-1.0_dp)

        do j = 1, 5
        dfp_primitive_times_weights(1:5,j)=dfp_primitive(1:5,j)*weight_matrix(j)
        end do

        M1_inverse = setup_t(qnode(1,node1),qnode(2,node1),qnode(3,node1),     &
                             qnode(4,node1))

        dR1_dQ1 = 0.0_dp

        do i = 1, 5
          do k = 1, 5
            dR1_dQ1(i,1:5) = dR1_dQ1(i,1:5)                                    &
                            + dfp_primitive_times_weights(i,k)*M1_inverse(k,1:5)
          end do
        end do

! contributions from right side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(1.0_dp)

        do j = 1, 5
        dfm_primitive_times_weights(1:5,j)=dfm_primitive(1:5,j)*weight_matrix(j)
        end do

        do i = 1, 5
          do k = 1, 5
            dR1_dQ1(i,1:5) = dR1_dQ1(i,1:5)                                    &
                            + dfm_primitive_times_weights(i,k)*M1_inverse(k,1:5)
          end do
        end do

! Note that dR2/dQ1 just has the sign flipped

        dR2_dQ1 = -dR1_dQ1

! Now lets do dR1/dQ2

! contributions from left side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(1.0_dp)

        do j = 1, 5
        dfp_primitive_times_weights(1:5,j)=dfp_primitive(1:5,j)*weight_matrix(j)
        end do

        M2_inverse = setup_t(qnode(1,node2),qnode(2,node2),qnode(3,node2),     &
                             qnode(4,node2))

        dR1_dQ2 = 0.0_dp

        do i = 1, 5
          do k = 1, 5
            dR1_dQ2(i,1:5) = dR1_dQ2(i,1:5)                                    &
                            + dfp_primitive_times_weights(i,k)*M2_inverse(k,1:5)
          end do
        end do

! contributions from right side

        weight_matrix(1:5) = phi(1:5,node1)*kappa_umuscl*my_haf*(-1.0_dp)

        do j = 1, 5
        dfm_primitive_times_weights(1:5,j)=dfm_primitive(1:5,j)*weight_matrix(j)
        end do

        do i = 1, 5
          do k = 1, 5
            dR1_dQ2(i,1:5) = dR1_dQ2(i,1:5)                                    &
                            + dfm_primitive_times_weights(i,k)*M2_inverse(k,1:5)
          end do
        end do

! Note that dR2/dQ2 just has the sign flipped

        dR2_dQ2 = -dR1_dQ2

! Now that we have the little blocks, we can transpose and multiply by lambda
! and send to the residual

        res_contribs6 : if ( fill_res ) then
        do i = 1, nfunctions
          rlamb(1:5,i) = tag(1,1:5)*Rlam(1:5,node1,i)

          if ( node1 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node1,i) = res(1:5,node1,i) + dR1_dQ1(m,1:5)*Rlamb(m,i)
            end do
          endif

          if ( node2 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node2,i) = res(1:5,node2,i) + dR1_dQ2(m,1:5)*Rlamb(m,i)
            end do
          endif

          rlamb(1:5,i) = tag(2,1:5)*Rlam(1:5,node2,i)

          if ( node1 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node1,i) = res(1:5,node1,i) + dR2_dQ1(m,1:5)*Rlamb(m,i)
            end do
          endif

          if ( node2 <= nnodes0 ) then
            do m = 1, 5
              res(1:5,node2,i) = res(1:5,node2,i) + dR2_dQ2(m,1:5)*Rlamb(m,i)
            end do
          endif
        end do
        endif res_contribs6

        if ( fill_a ) then
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          idiag2 = iau(node2)   ! where to put dR2/dQ2
          ioff12 = fhelp(2,n)   ! where to put dR1/dQ2
          ioff21 = fhelp(1,n)   ! where to put dR2/dQ1

! dR1/dQ1
          if(node1<=nnodes0)a(1:5,1:5,idiag1)=a(1:5,1:5,idiag1)+dR1_dQ1(1:5,1:5)

! dR1/dQ2
          if(node2<=nnodes0)a(1:5,1:5,ioff12)=a(1:5,1:5,ioff12)+dR1_dQ2(1:5,1:5)

! dR2/dQ2
          if(node2<=nnodes0)a(1:5,1:5,idiag2)=a(1:5,1:5,idiag2)+dR2_dQ2(1:5,1:5)

! dR2/dQ1
          if(node1<=nnodes0)a(1:5,1:5,ioff21)=a(1:5,1:5,ioff21)+dR2_dQ1(1:5,1:5)
        endif

      endif second_order_contribs

      endif  ! End of if node1 or node2 is less than nnodes0

   enddo edge_loop_1030

  end subroutine atlam_vl


!================================ ATLAM_ROEI =================================80
!
!  This does A-transpose times lambda at time-level n
!  for part of the residual (dI/dQ is the other part)
!
!  Puts the result in res
!
!  Roe
!
!  Incompressible
!
!=============================================================================80
  subroutine atlam_roei(nnodes0,nnodes01,x,y,z,symmetry,rr11,rr22,rr33,        &
                        rr12,rr13,rr23,qnode,gradx,grady,gradz,nedgeloc,eptr,  &
                        rarea,xn,yn,zn,ia,ja,ndim,adim,facespeed,              &
                        nfunctions,ad,res,rlam,coltag,fhelp,iau,a)

    use inviscid_flux,       only : first_order_iterations
    use kinddefs,            only : dp
    use reconstruction,      only : lstgs_sym
    use debug_defs,          only : symmetry_bcs
    use info_depr,           only : beta, ntt, kappa_umuscl
    use grid_motion_helpers, only : need_grid_velocity
    use adjoint_switches,    only : rn, np
    use design_types,        only : max_functions
    use lmpi,                only : lmpi_master, lmpi_die

    integer, intent(in) :: nnodes0,ndim,adim
    integer, intent(in) :: nedgeloc,nnodes01
    integer, intent(in), optional :: nfunctions

    integer, dimension(nnodes01),   intent(in) :: symmetry
    integer, dimension(2,nedgeloc), intent(in) :: eptr
    integer, dimension(:),          intent(in) :: ia, ja
    integer, dimension(:),          intent(in), optional :: iau
    integer, dimension(:,:),        intent(in), optional :: fhelp

    real(dp), dimension(nnodes01),      intent(in) :: x,y,z
    real(dp), dimension(nnodes0),       intent(in) :: rr11
    real(dp), dimension(nnodes0),       intent(in) :: rr22
    real(dp), dimension(nnodes0),       intent(in) :: rr33
    real(dp), dimension(nnodes0),       intent(in) :: rr12
    real(dp), dimension(nnodes0),       intent(in) :: rr13
    real(dp), dimension(nnodes0),       intent(in) :: rr23
    real(dp), dimension(ndim,nnodes01), intent(in) :: qnode
    real(dp), dimension(ndim,nnodes01), intent(in) :: gradx
    real(dp), dimension(ndim,nnodes01), intent(in) :: grady
    real(dp), dimension(ndim,nnodes01), intent(in) :: gradz
    real(dp), dimension(nedgeloc),      intent(in) :: rarea
    real(dp), dimension(nedgeloc),      intent(in) :: xn,yn,zn
    real(dp), dimension(nedgeloc),      intent(in) :: facespeed
    real(dp), dimension(adim,nnodes01), intent(in),    optional :: coltag
    real(dp), dimension(:,:,:),         intent(in),    optional :: rlam
    real(dp), dimension(5,5),           intent(inout), optional :: ad
    real(dp), dimension(:,:,:),         intent(inout), optional :: res
    real(dp), dimension(:,:,:),         intent(inout), optional :: a

    integer :: n,node1,node2,ii,node,i,j,iii,m
    integer :: idiag1, idiag2, ioff12, ioff21
    integer :: ioff1k, jstart, jend, neighbor, ioff2k

    real(dp)    :: second,xnorm,ynorm,znorm,area
    real(dp)    :: xmean,ymean,zmean
    real(dp)    :: rx,ry,rz

    real(dp)    :: pl
    real(dp)    :: ul
    real(dp)    :: vl
    real(dp)    :: wl
    real(dp)    :: ubarl

    real(dp)    :: pr
    real(dp)    :: ur
    real(dp)    :: vr
    real(dp)    :: wr
    real(dp)    :: ubarr

    real(dp)    :: u
    real(dp)    :: upl,uul,uvl,uwl,upr,uur,uvr,uwr
    real(dp)    :: v
    real(dp)    :: vpl,vul,vvl,vwl,vpr,vur,vvr,vwr
    real(dp)    :: w
    real(dp)    :: wpl,wul,wvl,wwl,wpr,wur,wvr,wwr
    real(dp)    :: c
    real(dp)    :: cpl,cul,cvl,cwl,cpr,cur,cvr,cwr
    real(dp)    :: c2
    real(dp)    :: c2pl,c2ul,c2vl,c2wl,c2pr,c2ur,c2vr,c2wr
    real(dp)    :: ubar
    real(dp)    :: ubarpl,ubarul,ubarvl,ubarwl,ubarpr,ubarur,ubarvr,ubarwr
    real(dp)    :: eig1
    real(dp)    :: eig1pl,eig1ul,eig1vl,eig1wl,eig1pr,eig1ur,eig1vr,eig1wr
    real(dp)    :: eig3
    real(dp)    :: eig3pl,eig3ul,eig3vl,eig3wl,eig3pr,eig3ur,eig3vr,eig3wr
    real(dp)    :: eig4
    real(dp)    :: eig4pl,eig4ul,eig4vl,eig4wl,eig4pr,eig4ur,eig4vr,eig4wr

    real(dp)    :: t1pl,t1ul,t1vl,t1wl,t1pr,t1ur,t1vr,t1wr
    real(dp)    :: t2pl,t2ul,t2vl,t2wl,t2pr,t2ur,t2vr,t2wr
    real(dp)    :: t3pl,t3ul,t3vl,t3wl,t3pr,t3ur,t3vr,t3wr
    real(dp)    :: t4pl,t4ul,t4vl,t4wl,t4pr,t4ur,t4vr,t4wr

    real(dp), dimension(4,max_functions) :: rlamb

    real(dp)    :: dxl,dyl,dzl,dxr,dyr,dzr
    real(dp)    :: dx1,dy1,dz1

    real(dp) :: face_speed, fspd_half, fspd2_4th
    real(dp) :: plpL,pluL,plvL,plwL,plpR,pluR,plvR,plwR
    real(dp) :: ulpL,uluL,ulvL,ulwL,ulpR,uluR,ulvR,ulwR
    real(dp) :: vlpL,vluL,vlvL,vlwL,vlpR,vluR,vlvR,vlwR
    real(dp) :: wlpL,wluL,wlvL,wlwL,wlpR,wluR,wlvR,wlwR
    real(dp) :: prpL,pruL,prvL,prwL,prpR,pruR,prvR,prwR
    real(dp) :: urpL,uruL,urvL,urwL,urpR,uruR,urvR,urwR
    real(dp) :: vrpL,vruL,vrvL,vrwL,vrpR,vruR,vrvR,vrwR
    real(dp) :: wrpL,wruL,wrvL,wrwL,wrpR,wruR,wrvR,wrwR
    real(dp) :: ubarrpL,ubarruL,ubarrvL,ubarrwL,ubarrpR,ubarruR,ubarrvR,ubarrwR
    real(dp) :: ubarlpL,ubarluL,ubarlvL,ubarlwL,ubarlpR,ubarluR,ubarlvR,ubarlwR
    real(dp) :: phi_ppL,phi_puL,phi_pvL,phi_pwL,phi_ppR,phi_puR,phi_pvR,phi_pwR
    real(dp) :: phi_mpL,phi_muL,phi_mvL,phi_mwL,phi_mpR,phi_muR,phi_mvR,phi_mwR
    real(dp) :: delppL,delpuL,delpvL,delpwL,delppR,delpuR,delpvR,delpwR
    real(dp) :: delupL,deluuL,deluvL,deluwL,delupR,deluuR,deluvR,deluwR
    real(dp) :: delvpL,delvuL,delvvL,delvwL,delvpR,delvuR,delvvR,delvwR
    real(dp) :: delwpL,delwuL,delwvL,delwwL,delwpR,delwuR,delwvR,delwwR
    real(dp) :: phi_p, phi_m, delp, delu, delv, delw, delubar, term1
    real(dp) :: delubarpL,delubaruL,delubarvL,delubarwL
    real(dp) :: delubarpR,delubaruR,delubarvR,delubarwR
    real(dp) :: term1pL,term1uL,term1vL,term1wL,term1pR,term1uR,term1vR,term1wR
    real(dp) :: f11pL,f11uL,f11vL,f11wL,f11pR,f11uR,f11vR,f11wR
    real(dp) :: f12pL,f12uL,f12vL,f12wL,f12pR,f12uR,f12vR,f12wR
    real(dp) :: f13pL,f13uL,f13vL,f13wL,f13pR,f13uR,f13vR,f13wR
    real(dp) :: f14pL,f14uL,f14vL,f14wL,f14pR,f14uR,f14vR,f14wR
    real(dp) :: piece1, piece2, term2
    real(dp) :: piece1pL,piece1uL,piece1vL,piece1wL
    real(dp) :: piece1pR,piece1uR,piece1vR,piece1wR
    real(dp) :: piece2pL,piece2uL,piece2vL,piece2wL
    real(dp) :: piece2pR,piece2uR,piece2vR,piece2wR
    real(dp) :: term2pL,term2uL,term2vL,term2wL,term2pR,term2uR,term2vR,term2wR
    real(dp) :: f21pL,f21uL,f21vL,f21wL,f21pR,f21uR,f21vR,f21wR
    real(dp) :: f22pL,f22uL,f22vL,f22wL,f22pR,f22uR,f22vR,f22wR
    real(dp) :: f23pL,f23uL,f23vL,f23wL,f23pR,f23uR,f23vR,f23wR
    real(dp) :: f24pL,f24uL,f24vL,f24wL,f24pR,f24uR,f24vR,f24wR
    real(dp) :: term3,term
    real(dp) :: term3pL,term3uL,term3vL,term3wL,term3pR,term3uR,term3vR,term3wR
    real(dp) :: termpL,termuL,termvL,termwL,termpR,termuR,termvR,termwR
    real(dp) :: f31pL,f31uL,f31vL,f31wL,f31pR,f31uR,f31vR,f31wR
    real(dp) :: f32pL,f32uL,f32vL,f32wL,f32pR,f32uR,f32vR,f32wR
    real(dp) :: f33pL,f33uL,f33vL,f33wL,f33pR,f33uR,f33vR,f33wR
    real(dp) :: f34pL,f34uL,f34vL,f34wL,f34pR,f34uR,f34vR,f34wR
    real(dp) :: piece3,term4
    real(dp) :: piece3pL,piece3uL,piece3vL,piece3wL
    real(dp) :: piece3pR,piece3uR,piece3vR,piece3wR
    real(dp) :: f41pL,f41uL,f41vL,f41wL,f41pR,f41uR,f41vR,f41wR
    real(dp) :: f42pL,f42uL,f42vL,f42wL,f42pR,f42uR,f42vR,f42wR
    real(dp) :: f43pL,f43uL,f43vL,f43wL,f43pR,f43uR,f43vR,f43wR
    real(dp) :: f44pL,f44uL,f44vL,f44wL,f44pR,f44uR,f44vR,f44wR
    real(dp) :: term4pL,term4uL,term4vL,term4wL,term4pR,term4uR,term4vR,term4wR
    real(dp) :: fluxp1pL,fluxp1uL,fluxp1vL,fluxp1wL
    real(dp) :: fluxp1pR,fluxp1uR,fluxp1vR,fluxp1wR
    real(dp) :: fluxp2pL,fluxp2uL,fluxp2vL,fluxp2wL
    real(dp) :: fluxp2pR,fluxp2uR,fluxp2vR,fluxp2wR
    real(dp) :: fluxp3pL,fluxp3uL,fluxp3vL,fluxp3wL
    real(dp) :: fluxp3pR,fluxp3uR,fluxp3vR,fluxp3wR
    real(dp) :: fluxp4pL,fluxp4uL,fluxp4vL,fluxp4wL
    real(dp) :: fluxp4pR,fluxp4uR,fluxp4vR,fluxp4wR
    real(dp) :: fluxm1pL,fluxm1uL,fluxm1vL,fluxm1wL
    real(dp) :: fluxm1pR,fluxm1uR,fluxm1vR,fluxm1wR
    real(dp) :: fluxm2pL,fluxm2uL,fluxm2vL,fluxm2wL
    real(dp) :: fluxm2pR,fluxm2uR,fluxm2vR,fluxm2wR
    real(dp) :: fluxm3pL,fluxm3uL,fluxm3vL,fluxm3wL
    real(dp) :: fluxm3pR,fluxm3uR,fluxm3vR,fluxm3wR
    real(dp) :: fluxm4pL,fluxm4uL,fluxm4vL,fluxm4wL
    real(dp) :: fluxm4pR,fluxm4uR,fluxm4vR,fluxm4wR

    integer, parameter :: n_cont = 4

    real(dp), dimension(3)      :: terms
    real(dp), dimension(4)      :: weight_matrix
    real(dp), dimension(4,4)    :: dfp_primitive_times_weights
    real(dp), dimension(4,4)    :: dfm_primitive_times_weights
    real(dp), dimension(4,4)    :: dflux_dQnode1
    real(dp), dimension(4,4)    :: dflux_dQnode2
    real(dp), dimension(4,4)    :: dflux_dQnodek
    real(dp), dimension(4,4)    :: dR1_dQ1, dR1_dQ2
    real(dp), dimension(4,4)    :: dR2_dQ1, dR2_dQ2
    real(dp), dimension(4,4)    :: dfp, dfm
    real(dp), dimension(2,4)    :: tag
    real(dp), dimension(n_cont) :: swx, swy, swz

    real(dp), parameter :: second_ord_limit = 0.01_dp
    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_4th = 0.25_dp

    logical :: fill_res, fill_a

  continue

    fill_res = .false.
    fill_a   = .false.

    if ( present(res) ) then
      if ( .not.present(coltag) .or. .not.present(rlam) .or.                   &
           .not.present(ad)     .or. .not.present(nfunctions) ) then
        if ( lmpi_master ) then
          write(*,*) 'res requested in atlam_roei, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_res = .true.
    endif

    if ( present(a) ) then
      if ( .not.present(iau) .or. .not.present(fhelp) ) then
        if ( lmpi_master ) then
          write(*,*) 'a requested in atlam_roei, but args not present.'
        endif
        call lmpi_die
        stop
      endif
      fill_a = .true.
    endif

    tag=0.0_dp

    if ( .not. neighbors_found ) call find_neighbors(nnodes01,nedgeloc,eptr)

    second = 1.0_dp
    if ( ntt <= first_order_iterations ) second = 0.0_dp

    edge_loop : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      local_node : if ( node1 <= nnodes0 .or. node2 <= nnodes0 ) then

! Get unit normals and area

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = rarea(n)

        face_speed = my_0
        if ( need_grid_velocity ) face_speed = facespeed(n)

        fspd_half = my_haf*face_speed
        fspd2_4th = my_4th*face_speed*face_speed

        xmean = my_haf*(x(node1) + x(node2))
        ymean = my_haf*(y(node1) + y(node2))
        zmean = my_haf*(z(node1) + z(node2))

        rx = second*(xmean - x(node1))
        ry = second*(ymean - y(node1))
        rz = second*(zmean - z(node1))

! Get variables on "left" side of face

        pl   = qnode(1,node1)                                                  &
             + ((my_1-kappa_umuscl)*gradx(1,node1)*rx                          &
             +  (my_1-kappa_umuscl)*grady(1,node1)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(1,node1)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(1,node2)-qnode(1,node1)))
          plpL = my_1
          pluL = my_0
          plvL = my_0
          plwL = my_0

          plpR = my_0
          pluR = my_0
          plvR = my_0
          plwR = my_0

        ul   = qnode(2,node1)                                                  &
             + ((my_1-kappa_umuscl)*gradx(2,node1)*rx                          &
             +  (my_1-kappa_umuscl)*grady(2,node1)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(2,node1)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(2,node2)-qnode(2,node1)))
          ulpL = my_0
          uluL = my_1
          ulvL = my_0
          ulwL = my_0

          ulpR = my_0
          uluR = my_0
          ulvR = my_0
          ulwR = my_0

        vl   = qnode(3,node1)                                                  &
             + ((my_1-kappa_umuscl)*gradx(3,node1)*rx                          &
             +  (my_1-kappa_umuscl)*grady(3,node1)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(3,node1)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(3,node2)-qnode(3,node1)))
          vlpL = my_0
          vluL = my_0
          vlvL = my_1
          vlwL = my_0

          vlpR = my_0
          vluR = my_0
          vlvR = my_0
          vlwR = my_0

        wl   = qnode(4,node1)                                                  &
             + ((my_1-kappa_umuscl)*gradx(4,node1)*rx                          &
             +  (my_1-kappa_umuscl)*grady(4,node1)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(4,node1)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(4,node2)-qnode(4,node1)))
          wlpL = my_0
          wluL = my_0
          wlvL = my_0
          wlwL = my_1

          wlpR = my_0
          wluR = my_0
          wlvR = my_0
          wlwR = my_0

        ubarl  = xnorm*ul + ynorm*vl + znorm*wl
          ubarlpL = xnorm*ulpL + ynorm*vlpL + znorm*wlpL
          ubarluL = xnorm*uluL + ynorm*vluL + znorm*wluL
          ubarlvL = xnorm*ulvL + ynorm*vlvL + znorm*wlvL
          ubarlwL = xnorm*ulwL + ynorm*vlwL + znorm*wlwL

          ubarlpR = xnorm*ulpR + ynorm*vlpR + znorm*wlpR
          ubarluR = xnorm*uluR + ynorm*vluR + znorm*wluR
          ubarlvR = xnorm*ulvR + ynorm*vlvR + znorm*wlvR
          ubarlwR = xnorm*ulwR + ynorm*vlwR + znorm*wlwR

! Get variables on "right" side of face

        rx = second*(xmean - x(node2))
        ry = second*(ymean - y(node2))
        rz = second*(zmean - z(node2))

        pr   = qnode(1,node2)                                                  &
             + ((my_1-kappa_umuscl)*gradx(1,node2)*rx                          &
             +  (my_1-kappa_umuscl)*grady(1,node2)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(1,node2)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(1,node1)-qnode(1,node2)))
          prpL = my_0
          pruL = my_0
          prvL = my_0
          prwL = my_0

          prpR = my_1
          pruR = my_0
          prvR = my_0
          prwR = my_0

        ur   = qnode(2,node2)                                                  &
             + ((my_1-kappa_umuscl)*gradx(2,node2)*rx                          &
             +  (my_1-kappa_umuscl)*grady(2,node2)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(2,node2)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(2,node1)-qnode(2,node2)))
          urpL = my_0
          uruL = my_0
          urvL = my_0
          urwL = my_0

          urpR = my_0
          uruR = my_1
          urvR = my_0
          urwR = my_0

        vr   = qnode(3,node2)                                                  &
             + ((my_1-kappa_umuscl)*gradx(3,node2)*rx                          &
             +  (my_1-kappa_umuscl)*grady(3,node2)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(3,node2)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(3,node1)-qnode(3,node2)))
          vrpL = my_0
          vruL = my_0
          vrvL = my_0
          vrwL = my_0

          vrpR = my_0
          vruR = my_0
          vrvR = my_1
          vrwR = my_0

        wr   = qnode(4,node2)                                                  &
             + ((my_1-kappa_umuscl)*gradx(4,node2)*rx                          &
             +  (my_1-kappa_umuscl)*grady(4,node2)*ry                          &
             +  (my_1-kappa_umuscl)*gradz(4,node2)*rz                          &
             + second*kappa_umuscl*my_haf*(qnode(4,node1)-qnode(4,node2)))
          wrpL = my_0
          wruL = my_0
          wrvL = my_0
          wrwL = my_0

          wrpR = my_0
          wruR = my_0
          wrvR = my_0
          wrwR = my_1

        ubarr  = xnorm*ur + ynorm*vr + znorm*wr
          ubarrpL = xnorm*urpL + ynorm*vrpL + znorm*wrpL
          ubarruL = xnorm*uruL + ynorm*vruL + znorm*wruL
          ubarrvL = xnorm*urvL + ynorm*vrvL + znorm*wrvL
          ubarrwL = xnorm*urwL + ynorm*vrwL + znorm*wrwL

          ubarrpR = xnorm*urpR + ynorm*vrpR + znorm*wrpR
          ubarruR = xnorm*uruR + ynorm*vruR + znorm*wruR
          ubarrvR = xnorm*urvR + ynorm*vrvR + znorm*wrvR
          ubarrwR = xnorm*urwR + ynorm*vrwR + znorm*wrwR

!   Compute averages

        u = 0.5_dp*(uL + uR)
          upL = 0.0_dp
          uuL = 0.5_dp
          uvL = 0.0_dp
          uwL = 0.0_dp
          upR = 0.0_dp
          uuR = 0.5_dp
          uvR = 0.0_dp
          uwR = 0.0_dp
        v = 0.5_dp*(vL + vR)
          vpL = 0.0_dp
          vuL = 0.0_dp
          vvL = 0.5_dp
          vwL = 0.0_dp
          vpR = 0.0_dp
          vuR = 0.0_dp
          vvR = 0.5_dp
          vwR = 0.0_dp
        w = 0.5_dp*(wL + wR)
          wpL = 0.0_dp
          wuL = 0.0_dp
          wvL = 0.0_dp
          wwL = 0.5_dp
          wpR = 0.0_dp
          wuR = 0.0_dp
          wvR = 0.0_dp
          wwR = 0.5_dp

        ubar = my_haf*(ubarl + ubarr)
          ubarpL = my_haf*(ubarlpL + ubarrpL)
          ubaruL = my_haf*(ubarluL + ubarruL)
          ubarvL = my_haf*(ubarlvL + ubarrvL)
          ubarwL = my_haf*(ubarlwL + ubarrwL)
          ubarpR = my_haf*(ubarlpR + ubarrpR)
          ubaruR = my_haf*(ubarluR + ubarruR)
          ubarvR = my_haf*(ubarlvR + ubarrvR)
          ubarwR = my_haf*(ubarlwR + ubarrwR)

        c2    = (ubar-fspd_half)*(ubar-fspd_half) + beta
          c2pL = 2.0_dp*(ubar-fspd_half)*ubarpL
          c2uL = 2.0_dp*(ubar-fspd_half)*ubaruL
          c2vL = 2.0_dp*(ubar-fspd_half)*ubarvL
          c2wL = 2.0_dp*(ubar-fspd_half)*ubarwL
          c2pR = 2.0_dp*(ubar-fspd_half)*ubarpR
          c2uR = 2.0_dp*(ubar-fspd_half)*ubaruR
          c2vR = 2.0_dp*(ubar-fspd_half)*ubarvR
          c2wR = 2.0_dp*(ubar-fspd_half)*ubarwR

        c     = sqrt(c2)
          cpL = 0.5_dp/sqrt(c2)*c2pL
          cuL = 0.5_dp/sqrt(c2)*c2uL
          cvL = 0.5_dp/sqrt(c2)*c2vL
          cwL = 0.5_dp/sqrt(c2)*c2wL
          cpR = 0.5_dp/sqrt(c2)*c2pR
          cuR = 0.5_dp/sqrt(c2)*c2uR
          cvR = 0.5_dp/sqrt(c2)*c2vR
          cwR = 0.5_dp/sqrt(c2)*c2wR

        phi_p = c/(c + fspd_half)
          phi_ppL = ((c+fspd_half)*cpL - c*cpL)/(c+fspd_half)/(c+fspd_half)
          phi_puL = ((c+fspd_half)*cuL - c*cuL)/(c+fspd_half)/(c+fspd_half)
          phi_pvL = ((c+fspd_half)*cvL - c*cvL)/(c+fspd_half)/(c+fspd_half)
          phi_pwL = ((c+fspd_half)*cwL - c*cwL)/(c+fspd_half)/(c+fspd_half)
          phi_ppR = ((c+fspd_half)*cpR - c*cpR)/(c+fspd_half)/(c+fspd_half)
          phi_puR = ((c+fspd_half)*cuR - c*cuR)/(c+fspd_half)/(c+fspd_half)
          phi_pvR = ((c+fspd_half)*cvR - c*cvR)/(c+fspd_half)/(c+fspd_half)
          phi_pwR = ((c+fspd_half)*cwR - c*cwR)/(c+fspd_half)/(c+fspd_half)

        phi_m = c/(c - fspd_half)
          phi_mpL = ((c-fspd_half)*cpL - c*cpL)/(c-fspd_half)/(c-fspd_half)
          phi_muL = ((c-fspd_half)*cuL - c*cuL)/(c-fspd_half)/(c-fspd_half)
          phi_mvL = ((c-fspd_half)*cvL - c*cvL)/(c-fspd_half)/(c-fspd_half)
          phi_mwL = ((c-fspd_half)*cwL - c*cwL)/(c-fspd_half)/(c-fspd_half)
          phi_mpR = ((c-fspd_half)*cpR - c*cpR)/(c-fspd_half)/(c-fspd_half)
          phi_muR = ((c-fspd_half)*cuR - c*cuR)/(c-fspd_half)/(c-fspd_half)
          phi_mvR = ((c-fspd_half)*cvR - c*cvR)/(c-fspd_half)/(c-fspd_half)
          phi_mwR = ((c-fspd_half)*cwR - c*cwR)/(c-fspd_half)/(c-fspd_half)

! Now compute eigenvalues, eigenvectors, and strengths

        eig1 = abs(ubar)
        if(ubar > 0.0_dp)then
          eig1pL = 0.0_dp
          eig1uL = ubaruL
          eig1vL = ubarvL
          eig1wL = ubarwL
          eig1pR = 0.0_dp
          eig1uR = ubaruR
          eig1vR = ubarvR
          eig1wR = ubarwR
        else
          eig1pL = 0.0_dp
          eig1uL = -ubaruL
          eig1vL = -ubarvL
          eig1wL = -ubarwL
          eig1pR = 0.0_dp
          eig1uR = -ubaruR
          eig1vR = -ubarvR
          eig1wR = -ubarwR
        end if

        eig3 = abs(ubar - fspd_half - c)
        if(ubar-fspd_half-c > 0.0_dp)then
          eig3pL = ubarpL - cpL
          eig3uL = ubaruL - cuL
          eig3vL = ubarvL - cvL
          eig3wL = ubarwL - cwL
          eig3pR = ubarpR - cpR
          eig3uR = ubaruR - cuR
          eig3vR = ubarvR - cvR
          eig3wR = ubarwR - cwR
        else
          eig3pL = -(ubarpL - cpL)
          eig3uL = -(ubaruL - cuL)
          eig3vL = -(ubarvL - cvL)
          eig3wL = -(ubarwL - cwL)
          eig3pR = -(ubarpR - cpR)
          eig3uR = -(ubaruR - cuR)
          eig3vR = -(ubarvR - cvR)
          eig3wR = -(ubarwR - cwR)
        end if

        eig4 = abs(ubar - fspd_half + c)
        if(ubar-fspd_half+c > 0.0_dp)then
          eig4pL = ubarpL + cpL
          eig4uL = ubaruL + cuL
          eig4vL = ubarvL + cvL
          eig4wL = ubarwL + cwL
          eig4pR = ubarpR + cpR
          eig4uR = ubaruR + cuR
          eig4vR = ubarvR + cvR
          eig4wR = ubarwR + cwR
        else
          eig4pL = -(ubarpL + cpL)
          eig4uL = -(ubaruL + cuL)
          eig4vL = -(ubarvL + cvL)
          eig4wL = -(ubarwL + cwL)
          eig4pR = -(ubarpR + cpR)
          eig4uR = -(ubaruR + cuR)
          eig4vR = -(ubarvR + cvR)
          eig4wR = -(ubarwR + cwR)
        end if

        delp    = pr - pl
          delppL = -1.0_dp
          delpuL =  0.0_dp
          delpvL =  0.0_dp
          delpwL =  0.0_dp
          delppR =  1.0_dp
          delpuR =  0.0_dp
          delpvR =  0.0_dp
          delpwR =  0.0_dp

        delu    = ur - ul
          delupL =  0.0_dp
          deluuL = -1.0_dp
          deluvL =  0.0_dp
          deluwL =  0.0_dp
          delupR =  0.0_dp
          deluuR =  1.0_dp
          deluvR =  0.0_dp
          deluwR =  0.0_dp

        delv    = vr - vl
          delvpL =  0.0_dp
          delvuL =  0.0_dp
          delvvL = -1.0_dp
          delvwL =  0.0_dp
          delvpR =  0.0_dp
          delvuR =  0.0_dp
          delvvR =  1.0_dp
          delvwR =  0.0_dp

        delw    = wr - wl
          delwpL =  0.0_dp
          delwuL =  0.0_dp
          delwvL =  0.0_dp
          delwwL = -1.0_dp
          delwpR =  0.0_dp
          delwuR =  0.0_dp
          delwvR =  0.0_dp
          delwwR =  1.0_dp

        delubar = ubarr - ubarl
          delubarpL = ubarrpL - ubarlpL
          delubaruL = ubarruL - ubarluL
          delubarvL = ubarrvL - ubarlvL
          delubarwL = ubarrwL - ubarlwL
          delubarpR = ubarrpR - ubarlpR
          delubaruR = ubarruR - ubarluR
          delubarvR = ubarrvR - ubarlvR
          delubarwR = ubarrwR - ubarlwR

        term1 =  eig1
          term1pL = eig1pL
          term1uL = eig1uL
          term1vL = eig1vL
          term1wL = eig1wL
          term1pR = eig1pR
          term1uR = eig1uR
          term1vR = eig1vR
          term1wR = eig1wR

!       f11   =  my_0
          f11pL   =  my_0
          f11uL   =  my_0
          f11vL   =  my_0
          f11wL   =  my_0
          f11pR   =  my_0
          f11uR   =  my_0
          f11vR   =  my_0
          f11wR   =  my_0

!       f12   =  term1*(delu - xnorm*delubar)
          f12pL = term1*(delupL-xnorm*delubarpL)+(delu-xnorm*delubar)*term1pL
          f12uL = term1*(deluuL-xnorm*delubaruL)+(delu-xnorm*delubar)*term1uL
          f12vL = term1*(deluvL-xnorm*delubarvL)+(delu-xnorm*delubar)*term1vL
          f12wL = term1*(deluwL-xnorm*delubarwL)+(delu-xnorm*delubar)*term1wL
          f12pR = term1*(delupR-xnorm*delubarpR)+(delu-xnorm*delubar)*term1pR
          f12uR = term1*(deluuR-xnorm*delubaruR)+(delu-xnorm*delubar)*term1uR
          f12vR = term1*(deluvR-xnorm*delubarvR)+(delu-xnorm*delubar)*term1vR
          f12wR = term1*(deluwR-xnorm*delubarwR)+(delu-xnorm*delubar)*term1wR

!       f13   =  term1*(delv - ynorm*delubar)
          f13pL = term1*(delvpL-ynorm*delubarpL)+(delv-ynorm*delubar)*term1pL
          f13uL = term1*(delvuL-ynorm*delubaruL)+(delv-ynorm*delubar)*term1uL
          f13vL = term1*(delvvL-ynorm*delubarvL)+(delv-ynorm*delubar)*term1vL
          f13wL = term1*(delvwL-ynorm*delubarwL)+(delv-ynorm*delubar)*term1wL
          f13pR = term1*(delvpR-ynorm*delubarpR)+(delv-ynorm*delubar)*term1pR
          f13uR = term1*(delvuR-ynorm*delubaruR)+(delv-ynorm*delubar)*term1uR
          f13vR = term1*(delvvR-ynorm*delubarvR)+(delv-ynorm*delubar)*term1vR
          f13wR = term1*(delvwR-ynorm*delubarwR)+(delv-ynorm*delubar)*term1wR

!       f14   =  term1*(delw - znorm*delubar)
          f14pL = term1*(delwpL-znorm*delubarpL)+(delw-znorm*delubar)*term1pL
          f14uL = term1*(delwuL-znorm*delubaruL)+(delw-znorm*delubar)*term1uL
          f14vL = term1*(delwvL-znorm*delubarvL)+(delw-znorm*delubar)*term1vL
          f14wL = term1*(delwwL-znorm*delubarwL)+(delw-znorm*delubar)*term1wL
          f14pR = term1*(delwpR-znorm*delubarpR)+(delw-znorm*delubar)*term1pR
          f14uR = term1*(delwuR-znorm*delubaruR)+(delw-znorm*delubar)*term1uR
          f14vR = term1*(delwvR-znorm*delubarvR)+(delw-znorm*delubar)*term1vR
          f14wR = term1*(delwwR-znorm*delubarwR)+(delw-znorm*delubar)*term1wR

        piece1 = (ubar - face_speed)*delubar + delp
          piece1pL = (ubar - face_speed)*delubarpL + delubar*ubarpL + delppL
          piece1uL = (ubar - face_speed)*delubaruL + delubar*ubaruL + delpuL
          piece1vL = (ubar - face_speed)*delubarvL + delubar*ubarvL + delpvL
          piece1wL = (ubar - face_speed)*delubarwL + delubar*ubarwL + delpwL
          piece1pR = (ubar - face_speed)*delubarpR + delubar*ubarpR + delppR
          piece1uR = (ubar - face_speed)*delubaruR + delubar*ubaruR + delpuR
          piece1vR = (ubar - face_speed)*delubarvR + delubar*ubarvR + delpvR
          piece1wR = (ubar - face_speed)*delubarwR + delubar*ubarwR + delpwR

        piece2 = (c2 + fspd2_4th)
          piece2pL = c2pL
          piece2uL = c2uL
          piece2vL = c2vL
          piece2wL = c2wL
          piece2pR = c2pR
          piece2uR = c2uR
          piece2vR = c2vR
          piece2wR = c2wR

        term2 = -eig1*piece1/piece2
          term2pL = -eig1*(piece2*piece1pL-piece1*piece2pL)/piece2/piece2      &
                  - piece1/piece2*eig1pL
          term2uL = -eig1*(piece2*piece1uL-piece1*piece2uL)/piece2/piece2      &
                  - piece1/piece2*eig1uL
          term2vL = -eig1*(piece2*piece1vL-piece1*piece2vL)/piece2/piece2      &
                  - piece1/piece2*eig1vL
          term2wL = -eig1*(piece2*piece1wL-piece1*piece2wL)/piece2/piece2      &
                  - piece1/piece2*eig1wL
          term2pR = -eig1*(piece2*piece1pR-piece1*piece2pR)/piece2/piece2      &
                  - piece1/piece2*eig1pR
          term2uR = -eig1*(piece2*piece1uR-piece1*piece2uR)/piece2/piece2      &
                  - piece1/piece2*eig1uR
          term2vR = -eig1*(piece2*piece1vR-piece1*piece2vR)/piece2/piece2      &
                  - piece1/piece2*eig1vR
          term2wR = -eig1*(piece2*piece1wR-piece1*piece2wR)/piece2/piece2      &
                  - piece1/piece2*eig1wR

!       f21   =  my_0
          f21pL   =  my_0
          f21uL   =  my_0
          f21vL   =  my_0
          f21wL   =  my_0
          f21pR   =  my_0
          f21uR   =  my_0
          f21vR   =  my_0
          f21wR   =  my_0

!       f22   =  term2*(u - xnorm*ubar)
          f22pL = term2*(upL - xnorm*ubarpL) + (u - xnorm*ubar)*term2pL
          f22uL = term2*(uuL - xnorm*ubaruL) + (u - xnorm*ubar)*term2uL
          f22vL = term2*(uvL - xnorm*ubarvL) + (u - xnorm*ubar)*term2vL
          f22wL = term2*(uwL - xnorm*ubarwL) + (u - xnorm*ubar)*term2wL
          f22pR = term2*(upR - xnorm*ubarpR) + (u - xnorm*ubar)*term2pR
          f22uR = term2*(uuR - xnorm*ubaruR) + (u - xnorm*ubar)*term2uR
          f22vR = term2*(uvR - xnorm*ubarvR) + (u - xnorm*ubar)*term2vR
          f22wR = term2*(uwR - xnorm*ubarwR) + (u - xnorm*ubar)*term2wR

!       f23   =  term2*(v - ynorm*ubar)
          f23pL = term2*(vpL - ynorm*ubarpL) + (v - ynorm*ubar)*term2pL
          f23uL = term2*(vuL - ynorm*ubaruL) + (v - ynorm*ubar)*term2uL
          f23vL = term2*(vvL - ynorm*ubarvL) + (v - ynorm*ubar)*term2vL
          f23wL = term2*(vwL - ynorm*ubarwL) + (v - ynorm*ubar)*term2wL
          f23pR = term2*(vpR - ynorm*ubarpR) + (v - ynorm*ubar)*term2pR
          f23uR = term2*(vuR - ynorm*ubaruR) + (v - ynorm*ubar)*term2uR
          f23vR = term2*(vvR - ynorm*ubarvR) + (v - ynorm*ubar)*term2vR
          f23wR = term2*(vwR - ynorm*ubarwR) + (v - ynorm*ubar)*term2wR

!       f24   =  term2*(w - znorm*ubar)
          f24pL = term2*(wpL - znorm*ubarpL) + (w - znorm*ubar)*term2pL
          f24uL = term2*(wuL - znorm*ubaruL) + (w - znorm*ubar)*term2uL
          f24vL = term2*(wvL - znorm*ubarvL) + (w - znorm*ubar)*term2vL
          f24wL = term2*(wwL - znorm*ubarwL) + (w - znorm*ubar)*term2wL
          f24pR = term2*(wpR - znorm*ubarpR) + (w - znorm*ubar)*term2pR
          f24uR = term2*(wuR - znorm*ubaruR) + (w - znorm*ubar)*term2uR
          f24vR = term2*(wvR - znorm*ubarvR) + (w - znorm*ubar)*term2vR
          f24wR = term2*(wwR - znorm*ubarwR) + (w - znorm*ubar)*term2wR

        piece1 = (ubar - c - fspd_half)*delubar + delp
          piece1pL = (ubar-c-fspd_half)*delubarpL+delubar*(ubarpL-cpL)+delppL
          piece1uL = (ubar-c-fspd_half)*delubaruL+delubar*(ubaruL-cuL)+delpuL
          piece1vL = (ubar-c-fspd_half)*delubarvL+delubar*(ubarvL-cvL)+delpvL
          piece1wL = (ubar-c-fspd_half)*delubarwL+delubar*(ubarwL-cwL)+delpwL
          piece1pR = (ubar-c-fspd_half)*delubarpR+delubar*(ubarpR-cpR)+delppR
          piece1uR = (ubar-c-fspd_half)*delubaruR+delubar*(ubaruR-cuR)+delpuR
          piece1vR = (ubar-c-fspd_half)*delubarvR+delubar*(ubarvR-cvR)+delpvR
          piece1wR = (ubar-c-fspd_half)*delubarwR+delubar*(ubarwR-cwR)+delpwR

        piece2 = piece1/c2
          piece2pL = (c2*piece1pL - piece1*c2pL)/c2/c2
          piece2uL = (c2*piece1uL - piece1*c2uL)/c2/c2
          piece2vL = (c2*piece1vL - piece1*c2vL)/c2/c2
          piece2wL = (c2*piece1wL - piece1*c2wL)/c2/c2
          piece2pR = (c2*piece1pR - piece1*c2pR)/c2/c2
          piece2uR = (c2*piece1uR - piece1*c2uR)/c2/c2
          piece2vR = (c2*piece1vR - piece1*c2vR)/c2/c2
          piece2wR = (c2*piece1wR - piece1*c2wR)/c2/c2

        term3 =  eig3*my_haf*piece2
          term3pL =  my_haf*(eig3*piece2pL + piece2*eig3pL)
          term3uL =  my_haf*(eig3*piece2uL + piece2*eig3uL)
          term3vL =  my_haf*(eig3*piece2vL + piece2*eig3vL)
          term3wL =  my_haf*(eig3*piece2wL + piece2*eig3wL)
          term3pR =  my_haf*(eig3*piece2pR + piece2*eig3pR)
          term3uR =  my_haf*(eig3*piece2uR + piece2*eig3uR)
          term3vR =  my_haf*(eig3*piece2vR + piece2*eig3vR)
          term3wR =  my_haf*(eig3*piece2wR + piece2*eig3wR)

        term  =  ubar+c-fspd_half
          termpL = ubarpL+cpL
          termuL = ubaruL+cuL
          termvL = ubarvL+cvL
          termwL = ubarwL+cwL
          termpR = ubarpR+cpR
          termuR = ubaruR+cuR
          termvR = ubarvR+cvR
          termwR = ubarwR+cwR

!       f31   =  term3*c*term
          f31pL = term3*(c*termpL + term*cpL) + c*term*term3pL
          f31uL = term3*(c*termuL + term*cuL) + c*term*term3uL
          f31vL = term3*(c*termvL + term*cvL) + c*term*term3vL
          f31wL = term3*(c*termwL + term*cwL) + c*term*term3wL
          f31pR = term3*(c*termpR + term*cpR) + c*term*term3pR
          f31uR = term3*(c*termuR + term*cuR) + c*term*term3uR
          f31vR = term3*(c*termvR + term*cvR) + c*term*term3vR
          f31wR = term3*(c*termwR + term*cwR) + c*term*term3wR

!       f32   =  term3*phi_m*(u - xnorm*term)
          f32pL = term3*(phi_m*(upL - xnorm*termpL)+(u - xnorm*term)*phi_mpL)  &
                + phi_m*(u - xnorm*term)*term3pL
          f32uL = term3*(phi_m*(uuL - xnorm*termuL)+(u - xnorm*term)*phi_muL)  &
                + phi_m*(u - xnorm*term)*term3uL
          f32vL = term3*(phi_m*(uvL - xnorm*termvL)+(u - xnorm*term)*phi_mvL)  &
                + phi_m*(u - xnorm*term)*term3vL
          f32wL = term3*(phi_m*(uwL - xnorm*termwL)+(u - xnorm*term)*phi_mwL)  &
                + phi_m*(u - xnorm*term)*term3wL
          f32pR = term3*(phi_m*(upR - xnorm*termpR)+(u - xnorm*term)*phi_mpR)  &
                + phi_m*(u - xnorm*term)*term3pR
          f32uR = term3*(phi_m*(uuR - xnorm*termuR)+(u - xnorm*term)*phi_muR)  &
                + phi_m*(u - xnorm*term)*term3uR
          f32vR = term3*(phi_m*(uvR - xnorm*termvR)+(u - xnorm*term)*phi_mvR)  &
                + phi_m*(u - xnorm*term)*term3vR
          f32wR = term3*(phi_m*(uwR - xnorm*termwR)+(u - xnorm*term)*phi_mwR)  &
                + phi_m*(u - xnorm*term)*term3wR

!       f33   =  term3*phi_m*(v - ynorm*term)
          f33pL = term3*(phi_m*(vpL - ynorm*termpL)+(v - ynorm*term)*phi_mpL)  &
                + phi_m*(v - ynorm*term)*term3pL
          f33uL = term3*(phi_m*(vuL - ynorm*termuL)+(v - ynorm*term)*phi_muL)  &
                + phi_m*(v - ynorm*term)*term3uL
          f33vL = term3*(phi_m*(vvL - ynorm*termvL)+(v - ynorm*term)*phi_mvL)  &
                + phi_m*(v - ynorm*term)*term3vL
          f33wL = term3*(phi_m*(vwL - ynorm*termwL)+(v - ynorm*term)*phi_mwL)  &
                + phi_m*(v - ynorm*term)*term3wL
          f33pR = term3*(phi_m*(vpR - ynorm*termpR)+(v - ynorm*term)*phi_mpR)  &
                + phi_m*(v - ynorm*term)*term3pR
          f33uR = term3*(phi_m*(vuR - ynorm*termuR)+(v - ynorm*term)*phi_muR)  &
                + phi_m*(v - ynorm*term)*term3uR
          f33vR = term3*(phi_m*(vvR - ynorm*termvR)+(v - ynorm*term)*phi_mvR)  &
                + phi_m*(v - ynorm*term)*term3vR
          f33wR = term3*(phi_m*(vwR - ynorm*termwR)+(v - ynorm*term)*phi_mwR)  &
                + phi_m*(v - ynorm*term)*term3wR

!       f34   =  term3*phi_m*(w - znorm*term)
          f34pL = term3*(phi_m*(wpL - znorm*termpL)+(w - znorm*term)*phi_mpL)  &
                + phi_m*(w - znorm*term)*term3pL
          f34uL = term3*(phi_m*(wuL - znorm*termuL)+(w - znorm*term)*phi_muL)  &
                + phi_m*(w - znorm*term)*term3uL
          f34vL = term3*(phi_m*(wvL - znorm*termvL)+(w - znorm*term)*phi_mvL)  &
                + phi_m*(w - znorm*term)*term3vL
          f34wL = term3*(phi_m*(wwL - znorm*termwL)+(w - znorm*term)*phi_mwL)  &
                + phi_m*(w - znorm*term)*term3wL
          f34pR = term3*(phi_m*(wpR - znorm*termpR)+(w - znorm*term)*phi_mpR)  &
                + phi_m*(w - znorm*term)*term3pR
          f34uR = term3*(phi_m*(wuR - znorm*termuR)+(w - znorm*term)*phi_muR)  &
                + phi_m*(w - znorm*term)*term3uR
          f34vR = term3*(phi_m*(wvR - znorm*termvR)+(w - znorm*term)*phi_mvR)  &
                + phi_m*(w - znorm*term)*term3vR
          f34wR = term3*(phi_m*(wwR - znorm*termwR)+(w - znorm*term)*phi_mwR)  &
                + phi_m*(w - znorm*term)*term3wR

        piece1 = ubar + c - fspd_half
          piece1pL = ubarpL + cpL
          piece1uL = ubaruL + cuL
          piece1vL = ubarvL + cvL
          piece1wL = ubarwL + cwL
          piece1pR = ubarpR + cpR
          piece1uR = ubaruR + cuR
          piece1vR = ubarvR + cvR
          piece1wR = ubarwR + cwR

        piece2 = piece1*delubar + delp
          piece2pL = piece1*delubarpL + delubar*piece1pL + delppL
          piece2uL = piece1*delubaruL + delubar*piece1uL + delpuL
          piece2vL = piece1*delubarvL + delubar*piece1vL + delpvL
          piece2wL = piece1*delubarwL + delubar*piece1wL + delpwL
          piece2pR = piece1*delubarpR + delubar*piece1pR + delppR
          piece2uR = piece1*delubaruR + delubar*piece1uR + delpuR
          piece2vR = piece1*delubarvR + delubar*piece1vR + delpvR
          piece2wR = piece1*delubarwR + delubar*piece1wR + delpwR

        piece3 = piece2/c2
          piece3pL = (c2*piece2pL - piece2*c2pL)/c2/c2
          piece3uL = (c2*piece2uL - piece2*c2uL)/c2/c2
          piece3vL = (c2*piece2vL - piece2*c2vL)/c2/c2
          piece3wL = (c2*piece2wL - piece2*c2wL)/c2/c2
          piece3pR = (c2*piece2pR - piece2*c2pR)/c2/c2
          piece3uR = (c2*piece2uR - piece2*c2uR)/c2/c2
          piece3vR = (c2*piece2vR - piece2*c2vR)/c2/c2
          piece3wR = (c2*piece2wR - piece2*c2wR)/c2/c2

        term4 =  my_haf*eig4*piece3
          term4pL = my_haf*(eig4*piece3pL + piece3*eig4pL)
          term4uL = my_haf*(eig4*piece3uL + piece3*eig4uL)
          term4vL = my_haf*(eig4*piece3vL + piece3*eig4vL)
          term4wL = my_haf*(eig4*piece3wL + piece3*eig4wL)
          term4pR = my_haf*(eig4*piece3pR + piece3*eig4pR)
          term4uR = my_haf*(eig4*piece3uR + piece3*eig4uR)
          term4vR = my_haf*(eig4*piece3vR + piece3*eig4vR)
          term4wR = my_haf*(eig4*piece3wR + piece3*eig4wR)

        term  =  ubar-c-fspd_half
          termpL = ubarpL - cpL
          termuL = ubaruL - cuL
          termvL = ubarvL - cvL
          termwL = ubarwL - cwL
          termpR = ubarpR - cpR
          termuR = ubaruR - cuR
          termvR = ubarvR - cvR
          termwR = ubarwR - cwR

!       f41   = -term4*c*term
          f41pL = -term4*(c*termpL+term*cpL) - c*term*term4pL
          f41uL = -term4*(c*termuL+term*cuL) - c*term*term4uL
          f41vL = -term4*(c*termvL+term*cvL) - c*term*term4vL
          f41wL = -term4*(c*termwL+term*cwL) - c*term*term4wL
          f41pR = -term4*(c*termpR+term*cpR) - c*term*term4pR
          f41uR = -term4*(c*termuR+term*cuR) - c*term*term4uR
          f41vR = -term4*(c*termvR+term*cvR) - c*term*term4vR
          f41wR = -term4*(c*termwR+term*cwR) - c*term*term4wR

!       f42   =  term4*phi_p*(u - xnorm*term)
          f42pL = term4*(phi_p*(upL - xnorm*termpL)+(u - xnorm*term)*phi_ppL)  &
                + phi_p*(u - xnorm*term)*term4pL
          f42uL = term4*(phi_p*(uuL - xnorm*termuL)+(u - xnorm*term)*phi_puL)  &
                + phi_p*(u - xnorm*term)*term4uL
          f42vL = term4*(phi_p*(uvL - xnorm*termvL)+(u - xnorm*term)*phi_pvL)  &
                + phi_p*(u - xnorm*term)*term4vL
          f42wL = term4*(phi_p*(uwL - xnorm*termwL)+(u - xnorm*term)*phi_pwL)  &
                + phi_p*(u - xnorm*term)*term4wL
          f42pR = term4*(phi_p*(upR - xnorm*termpR)+(u - xnorm*term)*phi_ppR)  &
                + phi_p*(u - xnorm*term)*term4pR
          f42uR = term4*(phi_p*(uuR - xnorm*termuR)+(u - xnorm*term)*phi_puR)  &
                + phi_p*(u - xnorm*term)*term4uR
          f42vR = term4*(phi_p*(uvR - xnorm*termvR)+(u - xnorm*term)*phi_pvR)  &
                + phi_p*(u - xnorm*term)*term4vR
          f42wR = term4*(phi_p*(uwR - xnorm*termwR)+(u - xnorm*term)*phi_pwR)  &
                + phi_p*(u - xnorm*term)*term4wR

!       f43   =  term4*phi_p*(v - ynorm*term)
          f43pL = term4*(phi_p*(vpL - ynorm*termpL)+(v - ynorm*term)*phi_ppL)  &
                + phi_p*(v - ynorm*term)*term4pL
          f43uL = term4*(phi_p*(vuL - ynorm*termuL)+(v - ynorm*term)*phi_puL)  &
                + phi_p*(v - ynorm*term)*term4uL
          f43vL = term4*(phi_p*(vvL - ynorm*termvL)+(v - ynorm*term)*phi_pvL)  &
                + phi_p*(v - ynorm*term)*term4vL
          f43wL = term4*(phi_p*(vwL - ynorm*termwL)+(v - ynorm*term)*phi_pwL)  &
                + phi_p*(v - ynorm*term)*term4wL
          f43pR = term4*(phi_p*(vpR - ynorm*termpR)+(v - ynorm*term)*phi_ppR)  &
                + phi_p*(v - ynorm*term)*term4pR
          f43uR = term4*(phi_p*(vuR - ynorm*termuR)+(v - ynorm*term)*phi_puR)  &
                + phi_p*(v - ynorm*term)*term4uR
          f43vR = term4*(phi_p*(vvR - ynorm*termvR)+(v - ynorm*term)*phi_pvR)  &
                + phi_p*(v - ynorm*term)*term4vR
          f43wR = term4*(phi_p*(vwR - ynorm*termwR)+(v - ynorm*term)*phi_pwR)  &
                + phi_p*(v - ynorm*term)*term4wR

!       f44   =  term4*phi_p*(w - znorm*term)
          f44pL = term4*(phi_p*(wpL - znorm*termpL)+(w - znorm*term)*phi_ppL)  &
                + phi_p*(w - znorm*term)*term4pL
          f44uL = term4*(phi_p*(wuL - znorm*termuL)+(w - znorm*term)*phi_puL)  &
                + phi_p*(w - znorm*term)*term4uL
          f44vL = term4*(phi_p*(wvL - znorm*termvL)+(w - znorm*term)*phi_pvL)  &
                + phi_p*(w - znorm*term)*term4vL
          f44wL = term4*(phi_p*(wwL - znorm*termwL)+(w - znorm*term)*phi_pwL)  &
                + phi_p*(w - znorm*term)*term4wL
          f44pR = term4*(phi_p*(wpR - znorm*termpR)+(w - znorm*term)*phi_ppR)  &
                + phi_p*(w - znorm*term)*term4pR
          f44uR = term4*(phi_p*(wuR - znorm*termuR)+(w - znorm*term)*phi_puR)  &
                + phi_p*(w - znorm*term)*term4uR
          f44vR = term4*(phi_p*(wvR - znorm*termvR)+(w - znorm*term)*phi_pvR)  &
                + phi_p*(w - znorm*term)*term4vR
          f44wR = term4*(phi_p*(wwR - znorm*termwR)+(w - znorm*term)*phi_pwR)  &
                + phi_p*(w - znorm*term)*term4wR

!       t1 = f11 + f21 + f31 + f41
          t1pL = f11pL + f21pL + f31pL + f41pL
          t1uL = f11uL + f21uL + f31uL + f41uL
          t1vL = f11vL + f21vL + f31vL + f41vL
          t1wL = f11wL + f21wL + f31wL + f41wL
          t1pR = f11pR + f21pR + f31pR + f41pR
          t1uR = f11uR + f21uR + f31uR + f41uR
          t1vR = f11vR + f21vR + f31vR + f41vR
          t1wR = f11wR + f21wR + f31wR + f41wR

!       t2 = f12 + f22 + f32 + f42
          t2pL = f12pL + f22pL + f32pL + f42pL
          t2uL = f12uL + f22uL + f32uL + f42uL
          t2vL = f12vL + f22vL + f32vL + f42vL
          t2wL = f12wL + f22wL + f32wL + f42wL
          t2pR = f12pR + f22pR + f32pR + f42pR
          t2uR = f12uR + f22uR + f32uR + f42uR
          t2vR = f12vR + f22vR + f32vR + f42vR
          t2wR = f12wR + f22wR + f32wR + f42wR

!       t3 = f13 + f23 + f33 + f43
          t3pL = f13pL + f23pL + f33pL + f43pL
          t3uL = f13uL + f23uL + f33uL + f43uL
          t3vL = f13vL + f23vL + f33vL + f43vL
          t3wL = f13wL + f23wL + f33wL + f43wL
          t3pR = f13pR + f23pR + f33pR + f43pR
          t3uR = f13uR + f23uR + f33uR + f43uR
          t3vR = f13vR + f23vR + f33vR + f43vR
          t3wR = f13wR + f23wR + f33wR + f43wR

!       t4 = f14 + f24 + f34 + f44
          t4pL = f14pL + f24pL + f34pL + f44pL
          t4uL = f14uL + f24uL + f34uL + f44uL
          t4vL = f14vL + f24vL + f34vL + f44vL
          t4wL = f14wL + f24wL + f34wL + f44wL
          t4pR = f14pR + f24pR + f34pR + f44pR
          t4uR = f14uR + f24uR + f34uR + f44uR
          t4vR = f14vR + f24vR + f34vR + f44vR
          t4wR = f14wR + f24wR + f34wR + f44wR

!   Calculate the flux vector on the left side

!       fluxp1 = beta*(ubarl-face_speed)
          fluxp1pL = beta*ubarlpL
          fluxp1uL = beta*ubarluL
          fluxp1vL = beta*ubarlvL
          fluxp1wL = beta*ubarlwL
          fluxp1pR = beta*ubarlpR
          fluxp1uR = beta*ubarluR
          fluxp1vR = beta*ubarlvR
          fluxp1wR = beta*ubarlwR

!       fluxp2 = ul*(ubarl-face_speed) + xnorm*pl
          fluxp2pL = ul*ubarlpL + (ubarl-face_speed)*ulpL + xnorm*plpL
          fluxp2uL = ul*ubarluL + (ubarl-face_speed)*uluL + xnorm*pluL
          fluxp2vL = ul*ubarlvL + (ubarl-face_speed)*ulvL + xnorm*plvL
          fluxp2wL = ul*ubarlwL + (ubarl-face_speed)*ulwL + xnorm*plwL
          fluxp2pR = ul*ubarlpR + (ubarl-face_speed)*ulpR + xnorm*plpR
          fluxp2uR = ul*ubarluR + (ubarl-face_speed)*uluR + xnorm*pluR
          fluxp2vR = ul*ubarlvR + (ubarl-face_speed)*ulvR + xnorm*plvR
          fluxp2wR = ul*ubarlwR + (ubarl-face_speed)*ulwR + xnorm*plwR

!       fluxp3 = vl*(ubarl-face_speed) + ynorm*pl
          fluxp3pL = vl*ubarlpL + (ubarl-face_speed)*vlpL + ynorm*plpL
          fluxp3uL = vl*ubarluL + (ubarl-face_speed)*vluL + ynorm*pluL
          fluxp3vL = vl*ubarlvL + (ubarl-face_speed)*vlvL + ynorm*plvL
          fluxp3wL = vl*ubarlwL + (ubarl-face_speed)*vlwL + ynorm*plwL
          fluxp3pR = vl*ubarlpR + (ubarl-face_speed)*vlpR + ynorm*plpR
          fluxp3uR = vl*ubarluR + (ubarl-face_speed)*vluR + ynorm*pluR
          fluxp3vR = vl*ubarlvR + (ubarl-face_speed)*vlvR + ynorm*plvR
          fluxp3wR = vl*ubarlwR + (ubarl-face_speed)*vlwR + ynorm*plwR

!       fluxp4 = wl*(ubarl-face_speed) + znorm*pl
          fluxp4pL = wl*ubarlpL + (ubarl-face_speed)*wlpL + znorm*plpL
          fluxp4uL = wl*ubarluL + (ubarl-face_speed)*wluL + znorm*pluL
          fluxp4vL = wl*ubarlvL + (ubarl-face_speed)*wlvL + znorm*plvL
          fluxp4wL = wl*ubarlwL + (ubarl-face_speed)*wlwL + znorm*plwL
          fluxp4pR = wl*ubarlpR + (ubarl-face_speed)*wlpR + znorm*plpR
          fluxp4uR = wl*ubarluR + (ubarl-face_speed)*wluR + znorm*pluR
          fluxp4vR = wl*ubarlvR + (ubarl-face_speed)*wlvR + znorm*plvR
          fluxp4wR = wl*ubarlwR + (ubarl-face_speed)*wlwR + znorm*plwR

!   Now the right side

!       fluxm1 = beta*(ubarr-face_speed)
          fluxm1pL = beta*ubarrpL
          fluxm1uL = beta*ubarruL
          fluxm1vL = beta*ubarrvL
          fluxm1wL = beta*ubarrwL
          fluxm1pR = beta*ubarrpR
          fluxm1uR = beta*ubarruR
          fluxm1vR = beta*ubarrvR
          fluxm1wR = beta*ubarrwR

!       fluxm2 = ur*(ubarr-face_speed) + xnorm*pr
          fluxm2pL = ur*ubarrpL + (ubarr-face_speed)*urpL + xnorm*prpL
          fluxm2uL = ur*ubarruL + (ubarr-face_speed)*uruL + xnorm*pruL
          fluxm2vL = ur*ubarrvL + (ubarr-face_speed)*urvL + xnorm*prvL
          fluxm2wL = ur*ubarrwL + (ubarr-face_speed)*urwL + xnorm*prwL
          fluxm2pR = ur*ubarrpR + (ubarr-face_speed)*urpR + xnorm*prpR
          fluxm2uR = ur*ubarruR + (ubarr-face_speed)*uruR + xnorm*pruR
          fluxm2vR = ur*ubarrvR + (ubarr-face_speed)*urvR + xnorm*prvR
          fluxm2wR = ur*ubarrwR + (ubarr-face_speed)*urwR + xnorm*prwR

!       fluxm3 = vr*(ubarr-face_speed) + ynorm*pr
          fluxm3pL = vr*ubarrpL + (ubarr-face_speed)*vrpL + ynorm*prpL
          fluxm3uL = vr*ubarruL + (ubarr-face_speed)*vruL + ynorm*pruL
          fluxm3vL = vr*ubarrvL + (ubarr-face_speed)*vrvL + ynorm*prvL
          fluxm3wL = vr*ubarrwL + (ubarr-face_speed)*vrwL + ynorm*prwL
          fluxm3pR = vr*ubarrpR + (ubarr-face_speed)*vrpR + ynorm*prpR
          fluxm3uR = vr*ubarruR + (ubarr-face_speed)*vruR + ynorm*pruR
          fluxm3vR = vr*ubarrvR + (ubarr-face_speed)*vrvR + ynorm*prvR
          fluxm3wR = vr*ubarrwR + (ubarr-face_speed)*vrwR + ynorm*prwR

!       fluxm4 = wr*(ubarr-face_speed) + znorm*pr
          fluxm4pL = wr*ubarrpL + (ubarr-face_speed)*wrpL + znorm*prpL
          fluxm4uL = wr*ubarruL + (ubarr-face_speed)*wruL + znorm*pruL
          fluxm4vL = wr*ubarrvL + (ubarr-face_speed)*wrvL + znorm*prvL
          fluxm4wL = wr*ubarrwL + (ubarr-face_speed)*wrwL + znorm*prwL
          fluxm4pR = wr*ubarrpR + (ubarr-face_speed)*wrpR + znorm*prpR
          fluxm4uR = wr*ubarruR + (ubarr-face_speed)*wruR + znorm*pruR
          fluxm4vR = wr*ubarrvR + (ubarr-face_speed)*wrvR + znorm*prvR
          fluxm4wR = wr*ubarrwR + (ubarr-face_speed)*wrwR + znorm*prwR

!       flux1 = my_haf*area*(fluxp1 + fluxm1 - t1)
          dfp(1,1) = my_haf*area*(fluxp1pL + fluxm1pL - t1pL)
          dfp(1,2) = my_haf*area*(fluxp1uL + fluxm1uL - t1uL)
          dfp(1,3) = my_haf*area*(fluxp1vL + fluxm1vL - t1vL)
          dfp(1,4) = my_haf*area*(fluxp1wL + fluxm1wL - t1wL)
          dfm(1,1) = my_haf*area*(fluxp1pR + fluxm1pR - t1pR)
          dfm(1,2) = my_haf*area*(fluxp1uR + fluxm1uR - t1uR)
          dfm(1,3) = my_haf*area*(fluxp1vR + fluxm1vR - t1vR)
          dfm(1,4) = my_haf*area*(fluxp1wR + fluxm1wR - t1wR)

!       flux2 = my_haf*area*(fluxp2 + fluxm2 - t2)
          dfp(2,1) = my_haf*area*(fluxp2pL + fluxm2pL - t2pL)
          dfp(2,2) = my_haf*area*(fluxp2uL + fluxm2uL - t2uL)
          dfp(2,3) = my_haf*area*(fluxp2vL + fluxm2vL - t2vL)
          dfp(2,4) = my_haf*area*(fluxp2wL + fluxm2wL - t2wL)
          dfm(2,1) = my_haf*area*(fluxp2pR + fluxm2pR - t2pR)
          dfm(2,2) = my_haf*area*(fluxp2uR + fluxm2uR - t2uR)
          dfm(2,3) = my_haf*area*(fluxp2vR + fluxm2vR - t2vR)
          dfm(2,4) = my_haf*area*(fluxp2wR + fluxm2wR - t2wR)

!       flux3 = my_haf*area*(fluxp3 + fluxm3 - t3)
          dfp(3,1) = my_haf*area*(fluxp3pL + fluxm3pL - t3pL)
          dfp(3,2) = my_haf*area*(fluxp3uL + fluxm3uL - t3uL)
          dfp(3,3) = my_haf*area*(fluxp3vL + fluxm3vL - t3vL)
          dfp(3,4) = my_haf*area*(fluxp3wL + fluxm3wL - t3wL)
          dfm(3,1) = my_haf*area*(fluxp3pR + fluxm3pR - t3pR)
          dfm(3,2) = my_haf*area*(fluxp3uR + fluxm3uR - t3uR)
          dfm(3,3) = my_haf*area*(fluxp3vR + fluxm3vR - t3vR)
          dfm(3,4) = my_haf*area*(fluxp3wR + fluxm3wR - t3wR)

!       flux4 = my_haf*area*(fluxp4 + fluxm4 - t4)
          dfp(4,1) = my_haf*area*(fluxp4pL + fluxm4pL - t4pL)
          dfp(4,2) = my_haf*area*(fluxp4uL + fluxm4uL - t4uL)
          dfp(4,3) = my_haf*area*(fluxp4vL + fluxm4vL - t4vL)
          dfp(4,4) = my_haf*area*(fluxp4wL + fluxm4wL - t4wL)
          dfm(4,1) = my_haf*area*(fluxp4pR + fluxm4pR - t4pR)
          dfm(4,2) = my_haf*area*(fluxp4uR + fluxm4uR - t4uR)
          dfm(4,3) = my_haf*area*(fluxp4vR + fluxm4vR - t4vR)
          dfm(4,4) = my_haf*area*(fluxp4wR + fluxm4wR - t4wR)

        if ( fill_res ) then
          tag(1,1:4) = coltag(1:4,node1)
          tag(2,1:4) = coltag(1:4,node2)
        endif

! Now take care of contribution to node 1

        res_contribs1 : if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:4,i) = tag(1,1:4)*rlam(1:4,node1,i)
          end do

! Diagonal piece

        if(node1 <= nnodes0) then
          do i = 1, nfunctions
            do m = 1, 4
              res(1:4,node1,i) = res(1:4,node1,i) + dfp(m,1:4)*rlamb(m,i)
            end do
          end do
        endif

! Now grab the offdiagonal

        do i = 1, nfunctions
          rlamb(1:4,i) = tag(1,1:4)*rlam(1:4,node1,i)
        end do

        if(node2 <= nnodes0) then
          do i = 1, nfunctions
            do m = 1, 4
              res(1:4,node2,i) = res(1:4,node2,i) + dfm(m,1:4)*rlamb(m,i)
            end do
          end do
        endif

! Now do the second node

        do i = 1, nfunctions
          rlamb(1:4,i) = tag(2,1:4)*rlam(1:4,node2,i)
        end do

        if(node2 <= nnodes0) then
          do i = 1, nfunctions
            do m = 1, 4
              res(1:4,node2,i) = res(1:4,node2,i) - dfm(m,1:4)*rlamb(m,i)
            end do
          end do
        endif

! Now grab the offdiagonal

        do i = 1, nfunctions
          rlamb(1:4,i) = tag(2,1:4)*rlam(1:4,node2,i)
        end do

        if(node1 <= nnodes0) then
          do i = 1, nfunctions
            do m = 1, 4
              res(1:4,node1,i) = res(1:4,node1,i) - dfp(m,1:4)*rlamb(m,i)
            end do
          end do
        endif

        endif res_contribs1

        if ( fill_a ) then
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          idiag2 = iau(node2)   ! where to put dR2/dQ2
          ioff12 = fhelp(2,n)   ! where to put dR1/dQ2
          ioff21 = fhelp(1,n)   ! where to put dR2/dQ1

! dR1/dQ1
          if(node1<=nnodes0)a(1:4,1:4,idiag1) = a(1:4,1:4,idiag1) + dfp(1:4,1:4)

! dR1/dQ2
          if(node2<=nnodes0)a(1:4,1:4,ioff12) = a(1:4,1:4,ioff12) + dfm(1:4,1:4)

! dR2/dQ2
          if(node2<=nnodes0)a(1:4,1:4,idiag2) = a(1:4,1:4,idiag2) - dfm(1:4,1:4)

! dR2/dQ1
          if(node1<=nnodes0)a(1:4,1:4,ioff21) = a(1:4,1:4,ioff21) - dfp(1:4,1:4)

        endif

! At this point, we have first order accuracy.  Now add contribution
! from second order terms.  We have taken into account the constant
! term in the reconstruction.  Now we need to add the pieces from the
! gradient terms.

      second_order_contribs : if( second > second_ord_limit) then

! First let's take care of all the nodes surrounding node1

        dxL = my_haf*second*(x(node2) - x(node1))
        dyL = my_haf*second*(y(node2) - y(node1))
        dzL = my_haf*second*(z(node2) - z(node1))

! Take care of the circuit of nodes around node 1

      if ( node1 <= nnodes0 ) then
      loop_2000 : do ii = 1, neighbors(node1)%n

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:4,i) = tag(1,1:4)*Rlam(1:4,node1,i)
          end do
        endif

        node = neighbors(node1)%list(ii) ! This is node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

        dx1 = x(node) - x(node1)
        dy1 = y(node) - y(node1)
        dz1 = z(node) - z(node1)

        terms(:) = lstgs_func(dx1,         dy1,         dz1,                   &
                              rr11(node1), rr12(node1), rr13(node1),           &
                              rr22(node1), rr23(node1), rr33(node1))
        swx(:) = terms(1)
        swy(:) = terms(2)
        swz(:) = terms(3)

        if (symmetry_bcs) then
          call lstgs_sym(symmetry(node1),                                      &
                         dx1,         dy1,         dz1,                        &
                         rr11(node1), rr12(node1), rr13(node1),                &
                         rr22(node1), rr23(node1), rr33(node1),                &
                         n_cont, swx, swy, swz )
        end if

        if( node == node1 )then
          swx = my_0
          swy = my_0
          swz = my_0
        end if

        weight_matrix(1:4) = (my_1-kappa_umuscl)                               &
                                       *(swx(1:4)*dxL+swy(1:4)*dyL+swz(1:4)*dzL)

         do j = 1, 4
           dfp_primitive_times_weights(1:4,j)=dfp(1:4,j)*weight_matrix(j)
         end do

         dflux_dQnode1 = 0.0_dp

         do i = 1, 4
           do j = 1, 4
             dflux_dQnode1(i,j) = dflux_dQnode1(i,j)                           &
                       + dfp_primitive_times_weights(i,j)
           end do
         end do

         dflux_dQnodek = 0.0_dp

         do i = 1, 4
           do j = 1, 4
             dflux_dQnodek(i,j) = dflux_dQnodek(i,j)                           &
                       + dfp_primitive_times_weights(i,j)
           end do
         end do

! We now have the linearizations of the residual at node1 with respect
! to Q at node1 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node1 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node1 and nodek).

! Always add to the off-node and subtract from the central node
! Note that if node=node1 then the weight is zero so nothing happens

! dR1/dQ1

        res_contribs2 : if ( fill_res ) then
          if ( rn == node1 .and. np == node1 ) then
            do iii = 1, 4
              ad(iii,1:4) = ad(iii,1:4) - dflux_dQnode1(iii,1:4)*coltag(iii,rn)
            end do
          endif

          do i = 1, nfunctions
            do m = 1, 4
              res(1:4,node1,i)=res(1:4,node1,i)-dflux_dQnode1(m,1:4)*rlamb(m,i)
            end do

! dR1/dQk with k going around node1

            if ( rn == node1 .and. np == node ) then
              do iii = 1, 4
                ad(iii,1:4) = ad(iii,1:4)+dflux_dQnodek(iii,1:4)*coltag(iii,rn)
              end do
            endif

            do m = 1, 4
              res(1:4,node,i) = res(1:4,node,i)+dflux_dQnodek(m,1:4)*rlamb(m,i)
            end do
          end do
        endif res_contribs2

! dR1/dQ1

      if ( fill_a ) then
        idiag1 = iau(node1)   ! where to put dR1/dQ1

        a(1:4,1:4,idiag1) = a(1:4,1:4,idiag1) - dflux_dQnode1(1:4,1:4)

! dR1/dQk with k going around node1

         ioff1k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search1 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff1k = j
             exit search1
           endif
         end do search1

         if ( ioff1k == 0 ) then
           write(*,*) 'Error finding off-diag 1k'
           call lmpi_die
         endif

         a(1:4,1:4,ioff1k) = a(1:4,1:4,ioff1k) + dflux_dQnodek(1:4,1:4)
       endif

! Contribution from Jacobian for node 2

       if ( fill_res ) then
         do i = 1, nfunctions
           rlamb(1:4,i) = tag(2,1:4)*Rlam(1:4,node2,i)
         end do
       endif

! We now have the linearizations of the residual at node2 with respect
! to Q at node1 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node2 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node1 and nodek).

! Now, subtract the weight for the "diagonal" and add it for the offdiagonal

! dR2/dQ1

       res_contribs3 : if ( fill_res ) then
         if ( rn == node2 .and. np == node1 ) then
           do iii = 1, 4
             ad(iii,1:4) = ad(iii,1:4) + dflux_dQnode1(iii,1:4)*coltag(iii,rn)
           end do
         endif

         do i = 1, nfunctions
           do m = 1, 4
             res(1:4,node1,i) = res(1:4,node1,i)+dflux_dQnode1(m,1:4)*rlamb(m,i)
           end do

! dR2/dQk with k going around node1

           if ( rn == node2 .and. np == node ) then
             do iii = 1, 4
               ad(iii,1:4) = ad(iii,1:4) - dflux_dQnodek(iii,1:4)*coltag(iii,rn)
             end do
           endif

           do m = 1, 4
             res(1:4,node,i) = res(1:4,node,i)-dflux_dQnodek(m,1:4)*rlamb(m,i)
           end do
         end do
       endif res_contribs3

       if ( fill_a ) then

! dR2/dQ1
         ioff21 = 0
         jstart = ia(node1)
         jend   = ia(node1+1)-1
         search2 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff21 = j
             exit search2
           endif
         end do search2

         if ( ioff21 == 0 ) then
           write(*,*) 'Error finding off-diag 21'
           call lmpi_die
         endif

         a(1:4,1:4,ioff21) = a(1:4,1:4,ioff21) + dflux_dQnode1(1:4,1:4)

! dR2/dQk with k going around node1

         ioff2k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search3 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff2k = j
             exit search3
           endif
         end do search3

         if ( ioff2k == 0 ) then
           write(*,*) 'Error finding off-diag 2k'
           call lmpi_die
         endif

         a(1:4,1:4,ioff2k) = a(1:4,1:4,ioff2k) - dflux_dQnodek(1:4,1:4)
       endif

      enddo loop_2000
      endif

! Now do circuit around node 2

      dxR = my_haf*second*(x(node1) - x(node2))
      dyR = my_haf*second*(y(node1) - y(node2))
      dzR = my_haf*second*(z(node1) - z(node2))

      if ( node2 <= nnodes0 ) then
      loop_2010 : do ii = 1, neighbors(node2)%n

        if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:4,i) = tag(2,1:4)*Rlam(1:4,node2,i)
          end do
        endif

        node = neighbors(node2)%list(ii) ! This is node we are about to update

! Now we need to go to each surrounding node and compute the
! weight from the gradient and add this contribution

        dx1 = x(node) - x(node2)
        dy1 = y(node) - y(node2)
        dz1 = z(node) - z(node2)

! We now have the linearizations of the residual at node2 with respect
! to Q at node2 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node2 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node2 and nodek).

        terms(:) = lstgs_func(dx1,         dy1,         dz1,                   &
                              rr11(node2), rr12(node2), rr13(node2),           &
                              rr22(node2), rr23(node2), rr33(node2))
        swx(:) = terms(1)
        swy(:) = terms(2)
        swz(:) = terms(3)

        if (symmetry_bcs) then
          call lstgs_sym(symmetry(node2),                                      &
                         dx1,         dy1,         dz1,                        &
                         rr11(node2), rr12(node2), rr13(node2),                &
                         rr22(node2), rr23(node2), rr33(node2),                &
                         n_cont, swx, swy, swz )
        end if

        if( node == node2 )then
          swx = my_0
          swy = my_0
          swz = my_0
        end if

        weight_matrix(1:4) = (my_1-kappa_umuscl)                               &
                                       *(swx(1:4)*dxR+swy(1:4)*dyR+swz(1:4)*dzR)

         do i = 1, 4
          do j = 1, 4
           dfm_primitive_times_weights(i,j)=dfm(i,j)*weight_matrix(j)
          end do
         end do

         dflux_dQnode2 = 0.0_dp

         do i = 1, 4
           do j = 1, 4
             dflux_dQnode2(i,j) = dflux_dQnode2(i,j)                           &
                       + dfm_primitive_times_weights(i,j)
           end do
         end do

         dflux_dQnodek = 0.0_dp

         do i = 1, 4
           do j = 1, 4
             dflux_dQnodek(i,j) = dflux_dQnodek(i,j)                           &
                       + dfm_primitive_times_weights(i,j)
           end do
         end do

! Always add to the off-node and subtract from the central node
! Note that if node=node2 then the weight is zero so nothing happens

! dR2/dQ2

        res_contribs4 : if ( fill_res ) then
         if ( rn == node2 .and. np == node2 ) then
           do iii = 1, 4
             ad(iii,1:4) = ad(iii,1:4) + dflux_dQnode2(iii,1:4)*coltag(iii,rn)
           end do
         endif

         do i = 1, nfunctions
           do m = 1, 4
             res(1:4,node2,i) = res(1:4,node2,i)+dflux_dQnode2(m,1:4)*rlamb(m,i)
           end do

! dR2/dQk

           if ( rn == node2 .and. np == node ) then
             do iii = 1, 4
               ad(iii,1:4) = ad(iii,1:4) - dflux_dQnodek(iii,1:4)*coltag(iii,rn)
             end do
           endif

           do m = 1, 4
             res(1:4,node,i) = res(1:4,node,i)-dflux_dQnodek(m,1:4)*rlamb(m,i)
           end do
         end do

        do i = 1, nfunctions
          rlamb(1:4,i) = tag(1,1:4)*Rlam(1:4,node1,i)
        end do
       endif res_contribs4

       if ( fill_a ) then

! dR2/dQ2
        idiag2 = iau(node2)   ! where to put dR2/dQ2

        a(1:4,1:4,idiag2) = a(1:4,1:4,idiag2) + dflux_dQnode2(1:4,1:4)

! dR2/dQk

         ioff2k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search4 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node2 ) then
             ioff2k = j
             exit search4
           endif
         end do search4

         if ( ioff2k == 0 ) then
           write(*,*) 'Error finding off-diag 2k'
           call lmpi_die
         endif

         a(1:4,1:4,ioff2k) = a(1:4,1:4,ioff2k) - dflux_dQnodek(1:4,1:4)
       endif

! We now have the linearizations of the residual at node1 with respect
! to Q at node2 and each of its neighbors.  This would normally be a
! row of the A-matrix.  For the adjoint, however, this forms a column of
! A-transpose.  Each piece will get multiplied by the lambda associated
! with node1 and added to the adjoint residual for the node that the
! linearization is taken with respect to (node2 and nodek).

       res_contribs5 : if ( fill_res ) then
! dR1/dQ2

         if ( rn == node1 .and. np == node2 ) then
           do iii = 1, 4
             ad(iii,1:4) = ad(iii,1:4) - dflux_dQnode2(iii,1:4)*coltag(iii,rn)
           end do
         endif

         do i = 1, nfunctions
           do m = 1, 4
             res(1:4,node2,i) = res(1:4,node2,i)-dflux_dQnode2(m,1:4)*rlamb(m,i)
           end do

! dR1/dQk

           if ( rn == node1 .and. np == node ) then
             do iii = 1, 4
               ad(iii,1:4) = ad(iii,1:4) + dflux_dQnodek(iii,1:4)*coltag(iii,rn)
             end do
           endif

           do m = 1, 4
             res(1:4,node,i) = res(1:4,node,i)+dflux_dQnodek(m,1:4)*rlamb(m,i)
           end do
         end do
       endif res_contribs5

       if ( fill_a ) then

! dR1/dQ2
         ioff12 = 0
         jstart = ia(node2)
         jend   = ia(node2+1)-1
         search5 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff12 = j
             exit search5
           endif
         end do search5

         if ( ioff12 == 0 ) then
           write(*,*) 'Error finding off-diag 12'
           call lmpi_die
         endif

         a(1:4,1:4,ioff12) = a(1:4,1:4,ioff12) - dflux_dQnode2(1:4,1:4)

! dR1/dQk

         ioff1k = 0
         jstart = ia(node)
         jend   = ia(node+1)-1
         search6 : do j = jstart, jend
           neighbor = abs(ja(j))
           if ( neighbor == node1 ) then
             ioff1k = j
             exit search6
           endif
         end do search6

         if ( ioff1k == 0 ) then
           write(*,*) 'Error finding off-diag 1k'
           call lmpi_die
         endif

         a(1:4,1:4,ioff1k) = a(1:4,1:4,ioff1k) + dflux_dQnodek(1:4,1:4)
       endif

       enddo loop_2010
      endif

! Finally pick up the other piece of the UMUSCL term along the edge
! These could be collapsed a lot, but its nice to see where each piece
! explicitly comes from

! First lets do dR1/dQ1

! contributions from left side

        weight_matrix(1:4) = kappa_umuscl*my_haf*(-1.0_dp)

        do i = 1, 4
          do j = 1, 4
            dfp_primitive_times_weights(i,j)=dfp(i,j)*weight_matrix(j)
          end do
        end do

        dR1_dQ1 = 0.0_dp

        do i = 1, 4
          do j = 1, 4
            dR1_dQ1(i,j) = dR1_dQ1(i,j)                                        &
                            + dfp_primitive_times_weights(i,j)
          end do
        end do

! contributions from right side

        weight_matrix(1:4) = kappa_umuscl*my_haf*(1.0_dp)

        do i = 1, 4
          do j = 1, 4
            dfm_primitive_times_weights(i,j)=dfm(i,j)*weight_matrix(j)
          end do
        end do

        do i = 1, 4
          do j = 1, 4
            dR1_dQ1(i,j) = dR1_dQ1(i,j)                                        &
                            + dfm_primitive_times_weights(i,j)
          end do
        end do

! Note that dR2/dQ1 just has the sign flipped

        dR2_dQ1 = -dR1_dQ1

! Now lets do dR1/dQ2

! contributions from left side

        weight_matrix(1:4) = kappa_umuscl*my_haf*(1.0_dp)

        do i = 1, 4
          do j = 1, 4
            dfp_primitive_times_weights(i,j)=dfp(i,j)*weight_matrix(j)
          end do
        end do

        dR1_dQ2 = 0.0_dp

        do i = 1, 4
          do j = 1, 4
            dR1_dQ2(i,j) = dR1_dQ2(i,j)                                        &
                            + dfp_primitive_times_weights(i,j)
          end do
        end do

! contributions from right side

        weight_matrix(1:4) = kappa_umuscl*my_haf*(-1.0_dp)

        do i = 1, 4
          do j = 1, 4
            dfm_primitive_times_weights(i,j)=dfm(i,j)*weight_matrix(j)
          end do
        end do

        do i = 1, 4
          do j = 1, 4
            dR1_dQ2(i,j) = dR1_dQ2(i,j)                                        &
                            + dfm_primitive_times_weights(i,j)
          end do
        end do

! Note that dR2/dQ2 just has the sign flipped

        dR2_dQ2 = -dR1_dQ2

! Now that we have the little blocks, we can transpose and multiply by lambda
! and send to the residual

        res_contribs6 : if ( fill_res ) then
          do i = 1, nfunctions
            rlamb(1:4,i) = tag(1,1:4)*Rlam(1:4,node1,i)

            if ( node1 <= nnodes0 ) then
              do m = 1, 4
                res(1:4,node1,i) = res(1:4,node1,i)+dR1_dQ1(m,1:4)*rlamb(m,i)
              end do
            endif

            if ( node2 <= nnodes0 ) then
              do m = 1, 4
                res(1:4,node2,i) = res(1:4,node2,i)+dR1_dQ2(m,1:4)*rlamb(m,i)
              end do
            endif

            rlamb(1:4,i) = tag(2,1:4)*Rlam(1:4,node2,i)

            if ( node1 <= nnodes0 ) then
              do m = 1, 4
                res(1:4,node1,i) = res(1:4,node1,i)+dR2_dQ1(m,1:4)*rlamb(m,i)
              end do
            endif

            if ( node2 <= nnodes0 ) then
              do m = 1, 4
                res(1:4,node2,i) = res(1:4,node2,i)+dR2_dQ2(m,1:4)*rlamb(m,i)
              end do
            endif
          end do
        endif res_contribs6

        if ( fill_a ) then
          idiag1 = iau(node1)   ! where to put dR1/dQ1
          idiag2 = iau(node2)   ! where to put dR2/dQ2
          ioff12 = fhelp(2,n)   ! where to put dR1/dQ2
          ioff21 = fhelp(1,n)   ! where to put dR2/dQ1

! dR1/dQ1
          if(node1<=nnodes0)a(1:4,1:4,idiag1)=a(1:4,1:4,idiag1)+dR1_dQ1(1:4,1:4)

! dR1/dQ2
          if(node2<=nnodes0)a(1:4,1:4,ioff12)=a(1:4,1:4,ioff12)+dR1_dQ2(1:4,1:4)

! dR2/dQ2
          if(node2<=nnodes0)a(1:4,1:4,idiag2)=a(1:4,1:4,idiag2)+dR2_dQ2(1:4,1:4)

! dR2/dQ1
          if(node1<=nnodes0)a(1:4,1:4,ioff21)=a(1:4,1:4,ioff21)+dR2_dQ1(1:4,1:4)

        endif

        endif second_order_contribs

      endif local_node

    enddo edge_loop

  end subroutine atlam_roei


!================================ FIND_NEIGHBORS =============================80
!
!  Find list of edge-connected neighbors around each node
!
!=============================================================================80
  subroutine find_neighbors(nnodes01,nedgeloc,eptr)

    use allocations, only : my_alloc_ptr

    integer, intent(in) :: nnodes01, nedgeloc

    integer, dimension(:,:), intent(in) :: eptr

    integer :: i, node1, node2, j, min_entry, minsave, ksave, k

  continue

! Count neighbors

    allocate(neighbors(nnodes01))
    do i = 1, nnodes01
      neighbors(i)%n = 1  ! diagonal
    end do

    do i = 1, nedgeloc
      node1 = eptr(1,i)
      node2 = eptr(2,i)
      neighbors(node1)%n = neighbors(node1)%n + 1
      neighbors(node2)%n = neighbors(node2)%n + 1
    end do

! Allocate memory

    do i = 1, nnodes01
      call my_alloc_ptr(neighbors(i)%list, neighbors(i)%n)
      neighbors(i)%n = 1
      neighbors(i)%list(neighbors(i)%n) = i   ! diagonal
    end do

! Store neighbors

    do i = 1, nedgeloc
      node1 = eptr(1,i)
      node2 = eptr(2,i)
      neighbors(node1)%n = neighbors(node1)%n + 1
      neighbors(node1)%list(neighbors(node1)%n) = node2
      neighbors(node2)%n = neighbors(node2)%n + 1
      neighbors(node2)%list(neighbors(node2)%n) = node1
    end do

! Sort neighbors

    do i = 1, nnodes01
      do j = 1, neighbors(i)%n
        min_entry = neighbors(i)%list(j)
        minsave   = neighbors(i)%list(j)
        ksave     = j
        do k = j+1,neighbors(i)%n
          if ( neighbors(i)%list(k) < min_entry ) then
            min_entry = neighbors(i)%list(k)
            ksave = k
          endif
        end do
        neighbors(i)%list(j) = min_entry
        neighbors(i)%list(ksave) = minsave
      end do
    end do

    neighbors_found = .true.

  end subroutine find_neighbors

  include 'lstgs_func.f90'
  include 'setup_t.f90'
  include 'setup_t_inverse.f90'

end module residual_inviscid


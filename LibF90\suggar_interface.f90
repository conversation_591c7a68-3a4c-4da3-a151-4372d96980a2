module suggar_interface

  implicit none

  private

  public :: dcf_compute_dci, dcf_init_no_args, dcf_begin_motion_input
  public :: dcf_add_motion_input, dcf_end_motion_input, dcf_parse_motion
  public :: dcf_release_dci

! explicit declaration of libsuggar routines to allow checking for correctness

  interface
    subroutine dcf_compute_dci(output_dci)
      implicit none
      integer, intent(in) :: output_dci
    end subroutine dcf_compute_dci
  end interface

  interface
    subroutine dcf_init_no_args(inp_name)
      implicit none
      character(len=*), intent(in) :: inp_name
    end subroutine dcf_init_no_args
  end interface

  interface
    subroutine dcf_begin_motion_input
      implicit none
    end subroutine dcf_begin_motion_input
  end interface

  interface
    subroutine dcf_add_motion_input(buf)
      implicit none
      character(len=*), intent(in) :: buf
    end subroutine dcf_add_motion_input
  end interface

  interface
    subroutine dcf_end_motion_input
      implicit none
    end subroutine dcf_end_motion_input
  end interface

  interface
    subroutine dcf_parse_motion
      implicit none
    end subroutine dcf_parse_motion
  end interface

  interface
    subroutine dcf_release_dci
      implicit none
    end subroutine dcf_release_dci
  end interface

end module suggar_interface

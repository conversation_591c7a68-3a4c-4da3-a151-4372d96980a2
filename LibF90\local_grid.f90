!============================== local_grid ===================================80
!
! Local grid data prior to metis/ParMetis.
!
!=============================================================================80

! Defines data structures for constructing a global (unpartitioned) grid

module local_grid

  implicit none

  private

  public :: pp_nhead, pp_ntail, pp_nsize
  public :: pp_mhead, pp_mtail, pp_msize
  public :: pp_chead, pp_ctail, pp_csize
  public :: pp_elem_ct
  public :: cc_chead, cc_ctail, cc_csize

  ! Node (n - current, m - before mirroring)
  integer, dimension(:), allocatable :: pp_nhead, pp_ntail, pp_nsize
  integer, dimension(:), allocatable :: pp_mhead, pp_mtail, pp_msize

  ! Cell (n - current, m - before mirroring)
  integer, dimension(:), allocatable :: pp_chead, pp_ctail, pp_csize
  integer, dimension(:), allocatable :: pp_elem_ct
  integer, dimension(:), allocatable :: cc_chead, cc_ctail, cc_csize

end module local_grid

module timestep

  use kinddefs, only : dp

  implicit none

  private

  public :: deltat2, bc_deltat2, deltat2i, bc_deltat2i, deltat2c_nc

contains

!================================ DELTAT2 ====================================80
!
! Calculate a time step for each cell
! Note that this routine assumes conservative variables
!
!=============================================================================80

  subroutine deltat2( nnodes0, nnodes01, nedgeloc, qnode, cdt, vol,            &
                      xn, yn, zn, ra, eptr, nbound, bc,                        &
                      nedgeloc_2d, nnodes0_2d, node_pairs_2d, facespeed, n_tot )

    use info_depr,            only : twod, lowmach_prec,                       &
                                      prec_mach_star, prec_mach_trans1,        &
                                      prec_mach_trans2, prec_dtau_skip
    use nml_nonlinear_solves, only : use_local_dt
    use grid_motion_helpers,  only : need_grid_velocity
    use fluid,                only : gamma, gm1
    use bc_types,             only : bcgrid_type
    use bc_names,             only : symmetry_x, symmetry_y, symmetry_z,       &
                                     bc_ignore_2d, bc_is_periodic
    use periodics,            only : nperiodic, periodic_data, periodic

    integer,                              intent(in)  :: nnodes0, n_tot
    integer,                              intent(in)  :: nnodes01
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nnodes0_2d
    integer,                              intent(in)  :: nbound
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    real(dp), dimension(nnodes0),         intent(out) :: cdt
    real(dp), dimension(nnodes01),        intent(in)  :: vol
    real(dp), dimension(nedgeloc),        intent(in)  :: ra,xn,yn,zn
    real(dp), dimension(nedgeloc),        intent(in)  :: facespeed
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    type(bcgrid_type), dimension(nbound), intent(in)  :: bc

    integer :: i, ib, n, node1, node2, nedge_dt_eval, set_number, node, j

    integer, dimension(nnodes0) :: periodic_tag

    real(dp) :: area, c, c1, c2, e1, e2, p1, p2, rho1, rho2, term
    real(dp) :: u, u1, u2, v, v1, v2, w, w1, w2, cdt_sum, volume_sum
    real(dp) :: xnorm, ynorm, znorm, face_speed
    real(dp) :: mach, beta, beta_star, mach_rel, ubar_fsa, psia, sigmaa

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_0    = 0.0_dp

    continue

!   set number of edges to evaluate interior time step data

    if (twod) then
      nedge_dt_eval = nedgeloc_2d
    else
      nedge_dt_eval = nedgeloc
    end if

! If local time steping, loop over faces
! and calculate time step as cdt = V/(sum(|u.n| +c.area)
! This is time step for cfl=1. We will multiply by cfl number later

! First loop over nodes and zero out cdt

    local_dt: if ( use_local_dt ) then

      do i = 1,nnodes0
        cdt(i) = my_0
      end do

! Start by getting interior contributions

      if ( lowmach_prec .and. prec_dtau_skip == 0 ) then

        edge_loop_prec: do n = 1,nedge_dt_eval

          node1 = eptr(1,n)
          node2 = eptr(2,n)

!       get normal to dual face

          xnorm = xn(n)
          ynorm = yn(n)
          znorm = zn(n)
          area  = ra(n)

          xnorm = xnorm*area
          ynorm = ynorm*area
          znorm = znorm*area

!       xnorm = x-normal x area of face
!       ynorm = y-normal x area of face
!       znorm = z-normal x area of face

          rho1 = qnode(1,node1)
          u1   = qnode(2,node1)/rho1
          v1   = qnode(3,node1)/rho1
          w1   = qnode(4,node1)/rho1
          e1   = qnode(5,node1)
          p1   = gm1*(e1 - my_half*rho1*(u1*u1 + v1*v1 + w1*w1))
          c1   = sqrt(gamma*p1/rho1)

          rho2 = qnode(1,node2)
          u2   = qnode(2,node2)/rho2
          v2   = qnode(3,node2)/rho2
          w2   = qnode(4,node2)/rho2
          e2   = qnode(5,node2)
          p2   = gm1*(e2 - my_half*rho2*(u2*u2 + v2*v2 + w2*w2))
          c2   = sqrt(gamma*p2/rho2)

!       get average values on dual face

          u    = my_half*(u1 + u2)
          v    = my_half*(v1 + v2)
          w    = my_half*(w1 + w2)
          c    = my_half*(c1 + c2)

!       dual face speed

          face_speed = my_0
          if (need_grid_velocity) then
            face_speed = facespeed(n)
          end if

!       Compute beta based on local Mach number

          beta_star = prec_mach_star**2
          mach      = sqrt( u**2 + v**2 + w**2 )/c

          if(mach >= prec_mach_trans2) then
            beta = my_1
          elseif(mach <= prec_mach_trans1) then
            beta = beta_star
          else
            mach_rel = (mach-prec_mach_trans1)/                          &
                       (prec_mach_trans2-prec_mach_trans1)
            beta = beta_star  + (beta_star-my_1)*(2.0_dp*mach_rel**2) &
                                                *(-1.5_dp + mach_rel)
          endif

!       Compute preconditioning parameters

          ubar_fsa = u*xnorm + v*ynorm + w*znorm - face_speed*area
          psia     = ubar_fsa*( my_1 - beta )*my_half
          sigmaa   = sqrt( psia**2 + beta*c*c*area*area )

          term = abs( ubar_fsa - psia ) + sigmaa
          if (node1 <= nnodes0) cdt(node1) = cdt(node1) + term
          if (node2 <= nnodes0) cdt(node2) = cdt(node2) + term

        end do edge_loop_prec

      else

        edge_loop: do n = 1,nedge_dt_eval

          node1 = eptr(1,n)
          node2 = eptr(2,n)

!       get normal to dual face

          xnorm = xn(n)
          ynorm = yn(n)
          znorm = zn(n)
          area  = ra(n)

          xnorm = xnorm*area
          ynorm = ynorm*area
          znorm = znorm*area

!       xnorm = x-normal x area of face
!       ynorm = y-normal x area of face
!       znorm = z-normal x area of face

          rho1 = qnode(1,node1)
          u1   = qnode(2,node1)/rho1
          v1   = qnode(3,node1)/rho1
          w1   = qnode(4,node1)/rho1
          e1   = qnode(5,node1)
          p1   = gm1*(e1 - my_half*rho1*(u1*u1 + v1*v1 + w1*w1))
          c1   = sqrt(gamma*p1/rho1)

          rho2 = qnode(1,node2)
          u2   = qnode(2,node2)/rho2
          v2   = qnode(3,node2)/rho2
          w2   = qnode(4,node2)/rho2
          e2   = qnode(5,node2)
          p2   = gm1*(e2 - my_half*rho2*(u2*u2 + v2*v2 + w2*w2))
          c2   = sqrt(gamma*p2/rho2)

!       get average values on dual face

          u    = my_half*(u1 + u2)
          v    = my_half*(v1 + v2)
          w    = my_half*(w1 + w2)
          c    = my_half*(c1 + c2)

!       dual face speed

          face_speed = my_0
          if (need_grid_velocity) then
            face_speed = facespeed(n)
          end if

          term = abs(u*xnorm + v*ynorm + w*znorm - face_speed*area) + c*area
          if (node1 <= nnodes0) cdt(node1) = cdt(node1) + term
          if (node2 <= nnodes0) cdt(node2) = cdt(node2) + term

        end do edge_loop
      endif

! Now close off the boundaries of the dual cells

      boundary_loop : do ib = 1, nbound
        if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle boundary_loop

        if((bc(ib)%ibc == symmetry_x) .or.               &
           (bc(ib)%ibc == symmetry_y) .or.               &
           (bc(ib)%ibc == symmetry_z) .or.               &
           (bc_is_periodic(bc(ib)%ibc)) ) cycle boundary_loop

        call bc_deltat2(nnodes0, nnodes01, bc(ib)%nbnode,                      &
                        bc(ib)%ibnode, qnode, cdt,                             &
                        bc(ib)%bxn, bc(ib)%byn, bc(ib)%bzn,                    &
                        bc(ib)%bfacespeed, n_tot)
      end do boundary_loop

! Combine the contributions from nodes on periodic planes
! Also set up a tag we'll need later

      if ( periodic ) then

        periodic_tag = 0

        do i = 1, nperiodic

          node = periodic_data(i)%list(1)
          cdt_sum = cdt(node)
          periodic_tag(node) = i

          do j = 2, periodic_data(i)%n
            node = periodic_data(i)%list(j)
            cdt_sum = cdt_sum + cdt(node)
            periodic_tag(node) = -i
          end do

          do j = 1, periodic_data(i)%n
            node = periodic_data(i)%list(j)
            cdt(node) = cdt_sum
          end do

        end do

      endif

! Now cdt has sum(|u.n| + c*area)

      if (twod) then

        do n = 1,nnodes0_2d
          node1 = node_pairs_2d(1,n)
          node2 = node_pairs_2d(2,n)
          cdt(node1) = vol(node1)/cdt(node1)
          cdt(node2) = cdt(node1)
        end do

      else

        if ( .not. periodic ) then

          do i = 1,nnodes0
            cdt(i) = vol(i)/cdt(i)
          end do

        else

          do i = 1,nnodes0

            if ( periodic_tag(i) == 0 ) then

              cdt(i) = vol(i)/cdt(i)

            else if ( periodic_tag(i) > 0 ) then

! Only do periodic nodes when we hit the primary nodes (don't double bookkeep)

              set_number = periodic_tag(i)
              node1 = periodic_data(set_number)%list(1)
              volume_sum = vol(node1)
              do j = 2, periodic_data(set_number)%n
                node2 = periodic_data(set_number)%list(j)
                volume_sum = volume_sum + vol(node2)
              end do

              do j = 1, periodic_data(set_number)%n
                node = periodic_data(set_number)%list(j)
                cdt(node) = volume_sum / cdt(node)
              end do

            endif
          end do

        endif

      end if

    else

! If not doing local time stepping just set cdt=1

      do i = 1,nnodes0
        cdt(i) = my_1
      end do

    end if local_dt

  end subroutine deltat2


!================================ BC_DELTAT2 =================================80
!
! Computes boundary contribution to dt (node based)
!
!=============================================================================80

  subroutine bc_deltat2( nnodes0, nnodes01, nbnode, ibnode, qnode, cdt,        &
                         bxn, byn, bzn, bfacespeed, n_tot )

    use fluid,               only : gamma, gm1
    use info_depr,           only : lowmach_prec, prec_mach_star,              &
                                    prec_dtau_skip, prec_mach_trans1,          &
                                    prec_mach_trans2
    use grid_motion_helpers, only : need_grid_velocity
    use inviscid_flux,       only : mean_decouple
    use generic_gas_map,     only : n_sonic_k

    integer,                             intent(in)    :: nnodes0, nnodes01
    integer,                             intent(in)    :: n_tot
    integer,                             intent(in)    :: nbnode
    integer,  dimension(nbnode),         intent(in)    :: ibnode
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nnodes0),        intent(inout) :: cdt
    real(dp), dimension(nbnode),         intent(in)    :: bxn,byn,bzn
    real(dp), dimension(nbnode),         intent(in)    :: bfacespeed

    integer :: i, inode

    real(dp) :: vn, area, c, e, p, rho, u, v, w
    real(dp) :: xnorm, ynorm, znorm, face_speed
    real(dp) :: mach, beta, beta_star, mach_rel, ubar_fsa, psia, sigmaa, term

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp

    continue

    if ( lowmach_prec .and. prec_dtau_skip == 0 ) then

      do i = 1,nbnode

        inode = ibnode(i)

        if (inode <= nnodes0) then

          rho = qnode(1,inode)
          u = qnode(2,inode) / rho
          v = qnode(3,inode) / rho
          w = qnode(4,inode) / rho
          e = qnode(5,inode)
          p = gm1 * (e-my_half*rho*(u*u+v*v+w*w))
          c = sqrt(gamma*p/rho)

!       get the normal

          xnorm = bxn(i)
          ynorm = byn(i)
          znorm = bzn(i)
          area  = sqrt(xnorm*xnorm+ynorm*ynorm+znorm*znorm)

!       face speed

          face_speed = 0._dp
          if (need_grid_velocity) then
            face_speed = bfacespeed(i)
          end if

!       Compute beta based on local Mach number

          beta_star = prec_mach_star**2
          mach      = sqrt( u**2 + v**2 + w**2 )/c

          if(mach >= prec_mach_trans2) then
            beta = my_1
          elseif(mach <= prec_mach_trans1) then
            beta = beta_star
          else
            mach_rel = (mach-prec_mach_trans1)/                          &
                       (prec_mach_trans2-prec_mach_trans1)
            beta = beta_star  + (beta_star-my_1)*(2.0_dp*mach_rel**2) &
                                               *( -1.5_dp + mach_rel)
          endif

!       Compute preconditioning parameters

          ubar_fsa = u*xnorm + v*ynorm + w*znorm - face_speed*area
          psia     = ubar_fsa*( my_1 - beta )*my_half
          sigmaa   = sqrt( psia**2 + beta*c*c*area*area )

          term = abs( ubar_fsa - psia ) + sigmaa

          cdt(inode) = cdt(inode) + term

        end if

      end do

    else

      do i = 1,nbnode

        inode = ibnode(i)

        if (inode <= nnodes0) then

          rho = qnode(1,inode)
          u = qnode(2,inode) / rho
          v = qnode(3,inode) / rho
          w = qnode(4,inode) / rho
          e = qnode(5,inode)

          if (mean_decouple) then
            c = qnode(n_sonic_k(1), inode)
          else
            p = gm1 * (e-my_half*rho*(u*u+v*v+w*w))
            c = sqrt(gamma*p/rho)
          end if

!       get the normal

          xnorm = bxn(i)
          ynorm = byn(i)
          znorm = bzn(i)
          area  = sqrt(xnorm*xnorm+ynorm*ynorm+znorm*znorm)

!       face speed

          face_speed = 0._dp
          if (need_grid_velocity) then
            face_speed = bfacespeed(i)
          end if

          vn = abs(xnorm*u+ynorm*v+znorm*w - face_speed*area) + c*area
          cdt(inode) = cdt(inode) + vn

        end if

      end do

    endif

  end subroutine bc_deltat2


!================================ DELTAT2I ===================================80
!
! Calculate a time step for each cell for incompressible flow
! Note that this routine assumes conservative variables
!
!=============================================================================80

  subroutine deltat2i( nnodes0, nnodes01, nedgeloc, qnode, cdt, vol,           &
                       xn, yn, zn, ra, eptr, nbound, bc,                       &
                       nedgeloc_2d, nnodes0_2d, node_pairs_2d, facespeed, n_tot)

    use info_depr,            only : beta, twod
    use nml_nonlinear_solves, only : use_local_dt
    use grid_motion_helpers,  only : need_grid_velocity
    use bc_types,             only : bcgrid_type
    use bc_names,             only : symmetry_x, symmetry_y, symmetry_z,       &
                                     bc_ignore_2d

    integer,                              intent(in)  :: nnodes0, n_tot
    integer,                              intent(in)  :: nnodes01
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nnodes0_2d
    integer,                              intent(in)  :: nbound
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(nnodes0),         intent(out) :: cdt
    real(dp), dimension(nnodes01),        intent(in)  :: vol
    real(dp), dimension(nedgeloc),        intent(in)  :: ra,xn,yn,zn
    real(dp), dimension(nedgeloc),        intent(in)  :: facespeed
    type(bcgrid_type), dimension(nbound), intent(in)  :: bc

    integer :: i, ib, n, node1, node2, nedge_dt_eval

    real(dp) :: area, c, term, u, u1, u2, v, v1, v2, w, w1, w2
    real(dp) :: xnorm, ynorm, znorm, ubar, face_speed, fspd_half

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_0    = 0.0_dp

    continue

!   set number of edges to evaluate interior time step data

    if (twod) then
      nedge_dt_eval = nedgeloc_2d
    else
      nedge_dt_eval = nedgeloc
    end if

! If local time steping, loop over faces
! and calculate time step as cdt = V/(sum(|u.n| +c.area)
! This is time step for cfl=1. We will multiply by cfl number later

! First loop over nodes and zero out cdt

    local_dt: if (use_local_dt) then

      do i = 1,nnodes0
        cdt(i) = my_0
      end do

! Start by getting interior contributions

      edge_loop: do n = 1,nedge_dt_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       get normal to face

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = ra(n)

        xnorm = xnorm*area
        ynorm = ynorm*area
        znorm = znorm*area

!       xnorm = x-normal x area of face
!       ynorm = y-normal x area of face
!       znorm = z-normal x area of face

        u1   = qnode(2,node1)
        v1   = qnode(3,node1)
        w1   = qnode(4,node1)

        u2   = qnode(2,node2)
        v2   = qnode(3,node2)
        w2   = qnode(4,node2)

!       dual face speed

        face_speed = my_0
        if (need_grid_velocity) then
          face_speed = facespeed(n)
        end if

        fspd_half = my_half*face_speed

!       get average values on face

        u    = my_half*(u1 + u2)
        v    = my_half*(v1 + v2)
        w    = my_half*(w1 + w2)
        ubar = xn(n)*u + yn(n)*v + zn(n)*w
        c    = sqrt((ubar-fspd_half)*(ubar-fspd_half) + beta)

        term = abs(u*xnorm + v*ynorm + w*znorm - fspd_half*area) + c*area
        if (node1 <= nnodes0) cdt(node1) = cdt(node1) + term
        if (node2 <= nnodes0) cdt(node2) = cdt(node2) + term

      end do edge_loop

      boundary_loop : do ib = 1, nbound
        if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle boundary_loop

        if((bc(ib)%ibc == symmetry_x) .or.               &
           (bc(ib)%ibc == symmetry_y) .or.               &
           (bc(ib)%ibc == symmetry_z)) cycle boundary_loop

        call bc_deltat2i(nnodes0, nnodes01, bc(ib)%nbnode,                     &
                         bc(ib)%ibnode, qnode, cdt,                            &
                         bc(ib)%bxn, bc(ib)%byn, bc(ib)%bzn, bc(ib)%bfacespeed,&
                         n_tot)
      end do boundary_loop

! Now cdt has sum(|u.n| + c*area)

      if (twod) then

        do n = 1,nnodes0_2d
          node1 = node_pairs_2d(1,n)
          node2 = node_pairs_2d(2,n)
          cdt(node1) = vol(node1)/cdt(node1)
          cdt(node2) = cdt(node1)
        end do

      else

        do i = 1,nnodes0
          cdt(i) = vol(i)/cdt(i)
        end do

      end if

    else

! If not doing local time stepping just set cdt=1

      do i = 1,nnodes0
        cdt(i) = my_1
      end do

    end if local_dt

  end subroutine deltat2i


!================================ BC_DELTAT2I ================================80
!
! Computes boundary contribution to dt (node based) for incompressible flow
!
!=============================================================================80

  subroutine bc_deltat2i( nnodes0, nnodes01, nbnode, ibnode, qnode, cdt,       &
                          bxn, byn, bzn, bfacespeed, n_tot )

    use info_depr,           only : beta
    use grid_motion_helpers, only : need_grid_velocity

    integer,                             intent(in)    :: nnodes0
    integer,                             intent(in)    :: nnodes01
    integer,                             intent(in)    :: n_tot
    integer,                             intent(in)    :: nbnode
    integer,  dimension(nbnode),         intent(in)    :: ibnode
    real(dp), dimension(nnodes0),        intent(inout) :: cdt
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nbnode),         intent(in)    :: bxn, byn, bzn
    real(dp), dimension(nbnode),         intent(in)    :: bfacespeed

    integer :: i, inode

    real(dp) :: vn, area, c, u, ubar, v, w
    real(dp) :: xnorm, ynorm, znorm, face_speed, fspd_half

    real(dp), parameter :: my_half = 0.5_dp
    real(dp), parameter :: my_0    = 0.0_dp

    continue

    do i = 1,nbnode

      inode = ibnode(i)

      if (inode <= nnodes0) then

!       get the normal

        xnorm = bxn(i)
        ynorm = byn(i)
        znorm = bzn(i)
        area  = sqrt(xnorm*xnorm+ynorm*ynorm+znorm*znorm)

!       dual face speed

        face_speed = my_0
        if (need_grid_velocity) then
          face_speed = bfacespeed(i)
        end if

        fspd_half = my_half*face_speed

        u    = qnode(2,inode)
        v    = qnode(3,inode)
        w    = qnode(4,inode)
        ubar = (u*xnorm+v*ynorm+w*znorm) / area
        c    = sqrt((ubar-fspd_half)*(ubar-fspd_half) + beta)

        vn = abs(xnorm*u+ynorm*v+znorm*w - fspd_half*area) + c*area
        cdt(inode) = cdt(inode) + vn

      end if

    end do

  end subroutine bc_deltat2i

!================================ DELTAT2C_NC ================================80
!
! Calculate a time step for each cell for incompressible convection
!
!=============================================================================80

  subroutine deltat2c_nc( nnodes0, nnodes01, nedgeloc, cdt, vol,               &
                          xn, yn, zn, ra, eptr, nbound, bc,                    &
                          nedgeloc_2d, nnodes0_2d, node_pairs_2d )

    use convection_defs,      only : cu, cv, cw
    use info_depr,            only : twod
    use nml_nonlinear_solves, only : use_local_dt
    use bc_types,             only : bcgrid_type
    use bc_names,             only : symmetry_x, symmetry_y, symmetry_z,       &
                                     bc_ignore_2d

    integer,                              intent(in)  :: nnodes0, nnodes01
    integer,                              intent(in)  :: nedgeloc
    integer,                              intent(in)  :: nedgeloc_2d
    integer,                              intent(in)  :: nnodes0_2d
    integer,                              intent(in)  :: nbound
    integer,  dimension(2,nedgeloc),      intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)  :: node_pairs_2d
    real(dp), dimension(nnodes0),         intent(out) :: cdt
    real(dp), dimension(nnodes01),        intent(in)  :: vol
    real(dp), dimension(nedgeloc),        intent(in)  :: ra,xn,yn,zn
    type(bcgrid_type), dimension(nbound), intent(in)  :: bc

    integer :: i, ib, n, node1, node2, nedge_dt_eval

    real(dp) :: area, term, xnorm, ynorm, znorm

    continue

!   set number of edges to evaluate interior time step data

    if (twod) then
      nedge_dt_eval = nedgeloc_2d
    else
      nedge_dt_eval = nedgeloc
    end if

! If local time steping, loop over faces
! and calculate time step as cdt = V/(sum(|u.n|)
! This is time step for cfl=1. We will multiply by cfl number later

! First loop over nodes and zero out cdt

    local_dt: if (use_local_dt) then

      do i = 1,nnodes0
        cdt(i) = 0._dp
      end do

! Start by getting interior contributions

      edge_loop: do n = 1,nedge_dt_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       get normal to face

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = ra(n)

        xnorm = xnorm*area
        ynorm = ynorm*area
        znorm = znorm*area

!       xnorm = x-normal x area of face
!       ynorm = y-normal x area of face
!       znorm = z-normal x area of face

        term = abs( cu*xnorm + cv*ynorm + cw*znorm )
        if (node1 <= nnodes0) cdt(node1) = cdt(node1) + term
        if (node2 <= nnodes0) cdt(node2) = cdt(node2) + term

      end do edge_loop

      boundary_loop : do ib = 1, nbound
        if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle boundary_loop

        if((bc(ib)%ibc == symmetry_x) .or.               &
           (bc(ib)%ibc == symmetry_y) .or.               &
           (bc(ib)%ibc == symmetry_z)) cycle boundary_loop

        call bc_deltat2c_nc(nnodes0, bc(ib)%nbnode,             &
                            bc(ib)%ibnode, cdt,                 &
                            bc(ib)%bxn, bc(ib)%byn, bc(ib)%bzn )
      end do boundary_loop

! Now cdt has sum(|u.n| + c*area)

      if (twod) then

        do n = 1,nnodes0_2d
          node1 = node_pairs_2d(1,n)
          node2 = node_pairs_2d(2,n)
          cdt(node1) = vol(node1)/cdt(node1)
          cdt(node2) = cdt(node1)
        end do

      else

        do i = 1,nnodes0
          cdt(i) = vol(i)/cdt(i)
        end do

      end if

    else

! If not doing local time stepping just set cdt=1

      do i = 1,nnodes0
        cdt(i) = 1._dp
      end do

    end if local_dt

  end subroutine deltat2c_nc

!================================ BC_DELTAT2C_NC =============================80
!
! Boundary contribution to dt (node based) for incompressible convection
!
!=============================================================================80

  subroutine bc_deltat2c_nc( nnodes0, nbnode, ibnode, cdt, bxn, byn, bzn )

    use convection_defs, only : cu, cv, cw

    integer,                      intent(in)    :: nnodes0
    integer,                      intent(in)    :: nbnode
    integer,  dimension(nbnode),  intent(in)    :: ibnode
    real(dp), dimension(nnodes0), intent(inout) :: cdt
    real(dp), dimension(nbnode),  intent(in)    :: bxn, byn, bzn

    integer :: i, inode

    real(dp) :: vn, xnorm, ynorm, znorm

    continue

    do i = 1,nbnode

      inode = ibnode(i)

      if (inode <= nnodes0) then

        xnorm = bxn(i)
        ynorm = byn(i)
        znorm = bzn(i)

        vn = abs( xnorm*cu + ynorm*cv + znorm*cw )
        cdt(inode) = cdt(inode) + vn

      end if

    end do

  end subroutine bc_deltat2c_nc

end module timestep

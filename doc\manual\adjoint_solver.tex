\section{Adjoint Solver, \dual\label{c:adjoint}}

This section describes how to execute the adjoint solver, \dual, directly.
Typically, \dual is executed by scripts that manage the multiple 
steps required for design optimization (\sectionref{s:design})
or grid adaptation (\sectionref{s:grid_adaptation}).
However, it may be necessary to run \dual directly to diagnose
problems or gain experience during setup 
including determining input parameters and termination strategies.
\FunThreeD is configured to compile \dual by default.
While the adjoint method is available for most commonly used
\FunThreeD capabilities,
only a subset of \FunThreeD's full capabilities are implemented
in the adjoint solver.

\subsection{Convergence of the Linear Adjoint Equations}

The adjoint solution is dependent on the 
primal flow solution (and the convergence of the primal flow equations).
While the primal solution may have converged enough to give acceptable
force and moment results, 
the flow residuals might still be large,
which can cause the adjoint solution scheme to diverge.
This divergence issue is most common in turbulent simulations.
A divergent adjoint scheme can be improved in some circumstances 
with the \cmd{--outer_loop_krylov} command line option.
It is critical to run the flow solver and the adjoint solver with the
\emph{same} governing equations and boundary conditions.

The scaling of the adjoint residuals is different from
the flow residuals 
and is dependent on the choice of the adjoint cost functions.
The number of iterations \cmd{steps} and
the residual tolerance \cmd{stopping_tolerance}
will need to be adjusted, 
see \sectionref{s:nml_code_run_control}.
The sensitivities should converge
at the same rate as your functions (i.e., lift), 
but an adjoint with some algebraic error may
still provide reasonable sensitivities for design and grid adaptation.

\subsection{Required Directory Hierarchy and Executing \dual}

The executable \file{dual} can be invoked directly from the 
command line, 
\begin{Verbatim}
  dual [fun3d options]
\end{Verbatim}
but the MPI version \file{dual_mpi} will need to be invoked 
within an MPI environment.
The most common method is via
\begin{Verbatim}
  [MPI run command] [MPI options] dual_mpi [fun3d options]
\end{Verbatim}
Any \file{[fun3d options]} provided to \nodet that control
the flow solver residual will also be required for
the adjoint solver for a consistent adjoint solution and
solution scheme.
See the flow solver execution instructions for more
details, \sectionref{s:flow-exec}.

\dual expects the cost function description \file{../rubber.data}
to be in the parent directory of the directory from which it is invoked.
The input and flow restart files are shared with \nodet in the
directory \file{../Flow/}. 
The flow solver must be run to completion, 
to provide a flow restart file, 
before \dual is invoked.
See \tab{t:adjoint-dir} for the required files and locations.

 \begin{table}[ht]
  \caption{Adjoint solver \dual directory hierarchy.}
  \label{t:adjoint-dir}
  \begin{tabular}{ll}
    \textbf{Relative Path} & \textbf{Description} \\
    \midrule
    \file{../Flow/[project_rootname].flow}  & Primal flow solution (restart) \\
    \file{../Flow/fun3d.nml}       & Main input namelist file \\
    \file{../rubber.data}          & Description of the adjoint cost function
  \end{tabular}
\end{table}

\subsection{\texttt{rubber.data}}\label{rubber.data.min}

The minimum required \file{rubber.data} for running the adjoint
(and grid adaptation)
can be written with the command
\begin{Verbatim}
  f3d function [cost function name]
\end{Verbatim}
Available cost function names are discussed in \sectionref{s:obj-con-fcns}
and listed in \tab{t:obj-con-keys}.
See \sectionref{s:rubber.data.details} for complete details on 
this file format including the information required for design.
The \file{rubber.data} reader requires the \emph{exact} number of header lines.
Be very careful when editing this file.

\subsection{Output Files}

The adjoint solver will export visualization files
in the same manner as the flow solver when
requested, see \sectionref{s:flowvis}.

\subparagraph{\protect\file{[project_rootname].adjoint}}

This file contains the binary restart information and is read by the
and adjoint solver for restart computations.

\subparagraph{\protect\file{[project_rootname]_hist.tec}}

This file contains the convergence history for the RMS residual
of the adjoint equations and CPU time.
The file is in the same \Tecplot format as the flow solver produces.
History information is truncated when the adjoint solver is restarted.




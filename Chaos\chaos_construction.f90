module chaos_construction

  implicit none

  private

  public :: build_adjoint_rhs, atimesx, diagonal_preconditioner
  public :: postprocess_tangent_initial, postprocess_tangent, get_csr
  public :: build_matrix, build_tangent_rhs, verify_check, boris_check
  public :: insert_identity, transfer_matrix, get_overlapped_csr

contains

!===================================== GET_CSR ===============================80
!
!   Establish CSR arrays for "big" matrix
!   This is only the local block in time
!
!=============================================================================80
  subroutine get_csr()

    use chaos_datas, only : time_data, nnodes, ia, ja, iau
    use lmpi,        only : lmpi_die
    use allocations, only : my_realloc_ptr
    use sort,        only : heap_sort

    integer :: i, j, j1, j2, k, neighbor1, neighbor2, maxcols, nnz, counter
    integer :: candidate

    integer, dimension(:), allocatable :: indx

    logical :: found_it

    type neighbor_type
      integer :: n
      integer, dimension(:), pointer :: list
    end type neighbor_type

    type(neighbor_type), dimension(:), allocatable :: neighbors

  continue

! First walk through G*G^t and bookkeep the neighbors per row

    allocate(neighbors(nnodes))
    do i = 1, nnodes
      neighbors(i)%n = 0
      allocate(neighbors(i)%list(1))
    end do

    grid_points : do i = 1, nnodes

      neighbors1 : do j1 = 1, time_data%drdqt(i)%n

        neighbor1 = time_data%drdqt(i)%list(j1)

        neighbors2 : do j2 = 1, time_data%drdqt(i)%n

          neighbor2 = time_data%drdqt(i)%list(j2)

          found_it = .false.
          search : do k = 1, neighbors(neighbor1)%n
            candidate = neighbors(neighbor1)%list(k)
            if ( candidate == neighbor2 ) then
              found_it = .true.
              exit search
            endif
          end do search

          if ( found_it ) cycle neighbors2

          neighbors(neighbor1)%n = neighbors(neighbor1)%n + 1

          if ( neighbors(neighbor1)%n > size(neighbors(neighbor1)%list,1) ) then
            call my_realloc_ptr(neighbors(neighbor1)%list,                     &
                                                       neighbors(neighbor1)%n+5)
          endif

          neighbors(neighbor1)%list(neighbors(neighbor1)%n) = neighbor2

        end do neighbors2

      end do neighbors1

    end do grid_points

! Now that we know the columns per row, allocate and fill the CSR arrays
! Order the entries in every row in ascending order

    maxcols = 0
    nnz     = 0
    do i = 1, nnodes
      maxcols = max(maxcols,neighbors(i)%n)
      nnz = nnz + neighbors(i)%n
    end do

    allocate(ia(nnodes+1))
    allocate(ja(nnz))
    allocate(iau(nnodes))

    allocate(indx(maxcols))

    ia(1) = 1
    counter = 0

    do i = 1, nnodes
      ia(i+1) = ia(i) + neighbors(i)%n
      call heap_sort(neighbors(i)%n, neighbors(i)%list, indx)
      found_it = .false.
      do j = 1, neighbors(i)%n
        counter = counter + 1
        ja(counter) = neighbors(i)%list(indx(j))
        if ( ja(counter) == i ) then
          iau(i) = counter
          found_it = .true.
        endif
      end do
      if ( .not. found_it ) then
        write(*,*) 'Trouble locating iau in get_csr().'
        call lmpi_die
        stop
      endif
      deallocate(neighbors(i)%list)
    end do

    if ( counter /= nnz ) then
      write(*,*) 'Error: counter /= nnz in get_csr().'
      call lmpi_die
      stop
    endif

    deallocate(neighbors,indx)

  end subroutine get_csr


!================================GET_OVERLAPPED_CSR ==========================80
!
!   Establish CSR arrays for "big" matrix
!   This includes the overlapped pieces in time
!
!=============================================================================80
  subroutine get_overlapped_csr()

    use chaos_datas, only : time_data, nnodes, ia, ja, iau, timestep, ntimesteps
    use lmpi,        only : lmpi_die
    use allocations, only : my_realloc_ptr
    use sort,        only : heap_sort

    integer :: i, j, j1, j2, k, neighbor1, neighbor2, maxcols, nnz, counter
    integer :: candidate, cand_time, nentries, offset, row_count, neighbor
    integer :: time_level, loop_index, jstart, jend

    integer, dimension(:), allocatable :: indx, entries

    logical :: found_it

    type neighbor_type
      integer :: n
      integer, dimension(:), pointer :: list
      integer, dimension(:), pointer :: time_level
    end type neighbor_type

    type(neighbor_type), dimension(:), allocatable :: neighbors

  continue

! First walk through G*G^t and bookkeep the neighbors per row
! This will be generic in terms of temporal boundary conditions
! I.e., we will bookkeep fwd and backward time planes even on the
! first and last ranks.  We will throw them out when we build the
! actual CSR arrays later.  But we need to know the general set of
! neighbors on every rank (even the first and last) since we will
! receive jacobians from neighboring ranks that involve them.

    allocate(neighbors(nnodes))
    do i = 1, nnodes
      neighbors(i)%n = 0
      allocate(neighbors(i)%list(1))
      allocate(neighbors(i)%time_level(1))
    end do

    grid_points : do i = 1, nnodes

      neighbors1 : do j1 = 1, time_data%drdqt(i)%n

        neighbor1 = time_data%drdqt(i)%list(j1)

        neighbors2 : do j2 = 1, time_data%drdqt(i)%n

          neighbor2 = time_data%drdqt(i)%list(j2)

          found_it = .false.
          search : do k = 1, neighbors(neighbor1)%n
            candidate = neighbors(neighbor1)%list(k)
            cand_time = neighbors(neighbor1)%time_level(k)
            if ( candidate == neighbor2 .and. cand_time == timestep) then
              found_it = .true.
              exit search
            endif
          end do search

          if ( found_it ) cycle neighbors2

          neighbors(neighbor1)%n = neighbors(neighbor1)%n + 1

          if ( neighbors(neighbor1)%n > size(neighbors(neighbor1)%list,1) ) then
            call my_realloc_ptr(neighbors(neighbor1)%list,                     &
                                                       neighbors(neighbor1)%n+5)
            call my_realloc_ptr(neighbors(neighbor1)%time_level,               &
                                                       neighbors(neighbor1)%n+5)
          endif

          neighbors(neighbor1)%list(neighbors(neighbor1)%n)       = neighbor2
          neighbors(neighbor1)%time_level(neighbors(neighbor1)%n) = timestep

        end do neighbors2

! Fwd plane contribution

        found_it = .false.
        search2 : do k = 1, neighbors(neighbor1)%n
          candidate = neighbors(neighbor1)%list(k)
          cand_time = neighbors(neighbor1)%time_level(k)
          if ( candidate == i .and. cand_time == timestep+1) then
            found_it = .true.
            exit search2
          endif
        end do search2

        add_fwd : if ( .not. found_it ) then

          neighbors(neighbor1)%n = neighbors(neighbor1)%n + 1

          if ( neighbors(neighbor1)%n > size(neighbors(neighbor1)%list,1) ) then
            call my_realloc_ptr(neighbors(neighbor1)%list,                     &
                                                       neighbors(neighbor1)%n+5)
            call my_realloc_ptr(neighbors(neighbor1)%time_level,               &
                                                       neighbors(neighbor1)%n+5)
          endif

          neighbors(neighbor1)%list(neighbors(neighbor1)%n)       = i
          neighbors(neighbor1)%time_level(neighbors(neighbor1)%n) = timestep+1

        endif add_fwd

! Backwards plane contribution

        found_it = .false.
        search3 : do k = 1, neighbors(i)%n
          candidate = neighbors(i)%list(k)
          cand_time = neighbors(i)%time_level(k)
          if ( candidate == neighbor1 .and. cand_time == timestep-1) then
            found_it = .true.
            exit search3
          endif
        end do search3

        add_back : if ( .not. found_it ) then

          neighbors(i)%n = neighbors(i)%n + 1

          if ( neighbors(i)%n > size(neighbors(i)%list,1) ) then
            call my_realloc_ptr(neighbors(i)%list,       neighbors(i)%n+5)
            call my_realloc_ptr(neighbors(i)%time_level, neighbors(i)%n+5)
          endif

          neighbors(i)%list(neighbors(i)%n)       = neighbor1
          neighbors(i)%time_level(neighbors(i)%n) = timestep-1

        endif add_back

      end do neighbors1

    end do grid_points

! Now that we know the columns per row, allocate and fill the CSR arrays
! Order the entries in every row in ascending order

    maxcols = 0
    nnz     = 0
    do i = 1, nnodes
      maxcols = max(maxcols,neighbors(i)%n)
      nnz = nnz + neighbors(i)%n
    end do

! nnz estimate here is bigger than we need since we will lop off fwd/backward
! time planes as needed

    if ( timestep == 1 .or. timestep == ntimesteps ) then
      nnz = 2*nnz          ! 2 macro rows in overlapped system
      maxcols = 2*maxcols  ! 2 macro rows in overlapped system
      allocate(ia(2*nnodes+1))
      allocate(iau(2*nnodes))
      allocate(ja(nnz))
    else
      nnz = 3*nnz          ! 3 macro rows in overlapped system
      maxcols = 3*maxcols  ! 3 macro rows in overlapped system
      allocate(ia(3*nnodes+1))
      allocate(iau(3*nnodes))
      allocate(ja(nnz))
    endif

! First set up the macro row associated with the backwards time plane if
! it exists

    ia(1)   = 1
    counter = 0
    offset  = 0

    if ( timestep /= 1 ) then
      do i = 1, nnodes
        row_count = 0
        do j = 1, neighbors(i)%n

          neighbor   = neighbors(i)%list(j)
          time_level = neighbors(i)%time_level(j)

          if ( time_level == timestep-1 ) cycle ! not in macro block

          row_count = row_count + 1
          counter = counter + 1

          if ( time_level == timestep ) then
            ja(counter) = neighbor
          else if ( time_level == timestep+1 ) then
            ja(counter) = nnodes + neighbor
          else
            write(*,*) 'unexpected event1'
            call lmpi_die
            stop
          endif

        end do
        ia(offset+i+1) = ia(offset+i) + row_count
      end do
      offset = offset + nnodes
    endif

! Now set up the macro row associated with the current time plane

    do i = 1, nnodes
      row_count = 0
      do j = 1, neighbors(i)%n

        neighbor   = neighbors(i)%list(j)
        time_level = neighbors(i)%time_level(j)

        if(timestep==1          .and. time_level==timestep-1)cycle !doesnt exist
        if(timestep==ntimesteps .and. time_level==timestep+1)cycle !doesnt exist

        row_count  = row_count + 1
        counter    = counter + 1

        if ( timestep == 1 .and. time_level == timestep ) then
          ja(counter) = neighbor
        else if ( timestep == 1 .and. time_level == timestep+1 ) then
          ja(counter) = nnodes + neighbor
        else if ( time_level == timestep-1 ) then
          ja(counter) = neighbor
        else if ( time_level == timestep ) then
          ja(counter) = nnodes + neighbor
        else if ( time_level == timestep+1 ) then
          ja(counter) = nnodes + nnodes + neighbor
        else
          write(*,*) 'unexpected event2'
          call lmpi_die
          stop
        endif
      end do
      ia(offset+i+1) = ia(offset+i) + row_count
    end do

    offset = offset + nnodes

! Finally set up the macro row associated with the forwards time plane if
! it exists

    if ( timestep /= ntimesteps ) then
      do i = 1, nnodes
        row_count = 0
        do j = 1, neighbors(i)%n

          neighbor   = neighbors(i)%list(j)
          time_level = neighbors(i)%time_level(j)

          if ( time_level == timestep+1 ) cycle ! not in macro block

          row_count = row_count + 1
          counter = counter + 1

          if ( timestep == 1 .and. time_level == timestep-1 ) then
            ja(counter) = neighbor
          else if ( timestep == 1 .and. time_level == timestep ) then
            ja(counter) = nnodes + neighbor
          else if ( time_level == timestep-1 ) then
            ja(counter) = nnodes + neighbor
          else if ( time_level == timestep ) then
            ja(counter) = nnodes + nnodes + neighbor
          else
            write(*,*) 'unexpected event3'
            call lmpi_die
            stop
          endif
        end do
        ia(offset+i+1) = ia(offset+i) + row_count
      end do
    endif

! Ditch the neighbors info

    do i = 1, nnodes
      deallocate(neighbors(i)%list, neighbors(i)%time_level)
    end do
    deallocate(neighbors)

! Reallocate ja to fit snugly now

    call my_realloc_ptr(ja,counter)

! Now go back through and sort the entries in each row and establish iau

    allocate(indx(maxcols))
    allocate(entries(maxcols))

    loop_index = 3*nnodes
    if ( timestep == 1 .or. timestep == ntimesteps ) loop_index = 2*nnodes

    do i = 1, loop_index

      jstart = ia(i)
      jend   = ia(i+1) - 1

      nentries = 0
      do j = jstart, jend
        nentries = nentries + 1
        entries(nentries) = ja(j)
      end do

      call heap_sort(nentries, entries, indx)

      nentries = 0
      do j = jstart, jend
        nentries = nentries + 1
        ja(j) = entries(indx(nentries))
      end do

      found_it = .false.
      finder : do j = jstart, jend
        if ( ja(j) == i ) then
          iau(i) = j
          found_it = .true.
          exit finder
        endif
      end do finder

      if ( .not. found_it ) then
        write(*,*) 'Trouble locating iau in get_overlapped_csr().'
        call lmpi_die
        stop
      endif

    end do

    deallocate(indx,entries)

  end subroutine get_overlapped_csr


!============================= BUILD_ADJOINT_RHS =============================80
!
!   Builds the adjoint RHS vector
!
!=============================================================================80
  subroutine build_adjoint_rhs(rhs)

    use kinddefs,    only : dp
    use chaos_datas, only : dt, nnodes, time_data, time_data_back, timestep,   &
                            power, m0, m1, obj_target, obj, omega, a2inv

    real(dp), dimension(:), intent(out) :: rhs

    integer :: i, j, neighbor, i1, i2

    real(dp) :: zbar, fbar, h

    real(dp), dimension(5,5) :: f, g, time_matrix, vinv, identity5

  continue

    identity5(:,:) = 0.0_dp
    identity5(1,1) = 1.0_dp
    identity5(2,2) = 1.0_dp
    identity5(3,3) = 1.0_dp
    identity5(4,4) = 1.0_dp
    identity5(5,5) = 1.0_dp

    rhs(:) = 0.0_dp

! Precompute some objective function stuff

    zbar = 0.0_dp
    do i = m0, m1
      zbar = zbar + (obj(i) - obj_target)
    end do
    zbar = zbar / real((m1-m0+1),dp)

    fbar = omega * dt * zbar ** power

!   factor =  1.0_dp/real((m1-m0+1),dp) FIXME: commented code

    time_matrix(:,:) = 0.0_dp
    do i = 1, 5
      time_matrix(i,i) = 1.0_dp / dt
    end do

    grid_points : do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      neighbors : do j = 1, time_data%drdq(i)%n

        neighbor = time_data%drdq(i)%list(j)   ! neighbor node number

        vinv = 1.0_dp / time_data%vol(neighbor) * identity5

        g(:,:) = time_data%drdq(i)%blocks(:,:,j)

        if ( neighbor == i ) then   ! diagonal contribution
          g(:,:) = g(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        rhs(i1:i2) = rhs(i1:i2)                                                &
                   - matmul(g(:,:),matmul(vinv,time_data%dfdq(:,neighbor)))

      end do neighbors

      vinv = 1.0_dp / time_data%vol(i) * identity5

      if ( timestep > 1 ) then
        rhs(i1:i2) = rhs(i1:i2)                                                &
                   - matmul(f(:,:),matmul(vinv,time_data_back%dfdq(:,i)))
      endif

      h = power/real(m1-m0,dp)*(omega*dt*zbar**(power-1)                       &
                              *(obj(timestep)-obj_target)-fbar)

      rhs(i1:i2) = rhs(i1:i2) - a2inv*h*time_data%res(:,i)

    end do grid_points

  end subroutine build_adjoint_rhs


!============================= BUILD_TANGENT_RHS =============================80
!
!   Builds the tangent RHS vector
!
!=============================================================================80
  subroutine build_tangent_rhs(rhs)

    use kinddefs,    only : dp
    use chaos_datas, only : nnodes, time_data

    real(dp), dimension(:), intent(out) :: rhs

    integer :: i, i1, i2

  continue

    rhs(:) = 0.0_dp

    do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      rhs(i1:i2) = time_data%drdd(:,i)

    end do

  end subroutine build_tangent_rhs


!===================================== ATIMESX ===============================80
!
!   Forms matvec
!
!=============================================================================80
  subroutine atimesx(n,x,y)

    use kinddefs,    only : dp
    use chaos_datas, only : timestep, time_data, time_data_back, dt, nnodes,   &
                            ntimesteps, a2inv
    use lmpi,        only : lmpi_recv, lmpi_send, lmpi_success, lmpi_id,       &
                            lmpi_die

    integer, intent(in) :: n

    real(dp), dimension(n), intent(in)  :: x
    real(dp), dimension(n), intent(out) :: y

    integer :: i, i1, i2, ierr, j1, j2, j11, j12, j21, j22
    integer :: neighbor1, neighbor2

    real(dp) :: psi

    real(dp), dimension(5,5) :: f, time_matrix, gt1, gt2, gt, vinv, identity5

    real(dp), dimension(n) :: x_back, x_fwd

  continue

    identity5(:,:) = 0.0_dp
    identity5(1,1) = 1.0_dp
    identity5(2,2) = 1.0_dp
    identity5(3,3) = 1.0_dp
    identity5(4,4) = 1.0_dp
    identity5(5,5) = 1.0_dp

    y(:) = 0.0_dp

! Transfer my portion of the x vector to the processors above and below me
! if they exist

! First let's send data backwards in time, i.e., to the rank "below" us.
! This will become x_fwd on the receiving ranks. The "special" cases here are:
!   1) the last rank will not receive anything
!   2) the first rank will not send anything

    if ( timestep /= ntimesteps ) then

      call lmpi_recv(x_fwd,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for backward comm.', lmpi_id
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= 1 ) then

      call lmpi_send(x,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for backward comm.', lmpi_id
        call lmpi_die
        stop
      endif

    endif

! Now send data forwards in time, i.e., to the rank "above" us.
! This will become x_back on the receiving ranks. The "special" cases here are:
!   1) the first rank will not receive anything
!   2) the last rank will not send anything

    if ( timestep /= 1 ) then

      call lmpi_recv(x_back,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for forward comm.'
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= ntimesteps ) then

      call lmpi_send(x,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for forward comm.'
        call lmpi_die
        stop
      endif

    endif

! Now perform the local contributions to the matvec

    time_matrix = 1.0_dp / dt * identity5

    psi = 0.0_dp
    grid_points : do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      vinv = 1.0_dp / time_data%vol(i) * identity5

      neighbors1 : do j1 = 1, time_data%drdqt(i)%n

        neighbor1 = time_data%drdqt(i)%list(j1)   ! neighbor node number

        j11 = (neighbor1-1)*5 + 1
        j12 = (neighbor1-1)*5 + 5

        gt1(:,:) = time_data%drdqt(i)%blocks(:,:,j1)

        if ( neighbor1 == i ) then   ! diagonal contribution
          gt1(:,:) = gt1(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        neighbors2 : do j2 = 1, time_data%drdqt(i)%n

          neighbor2 = time_data%drdqt(i)%list(j2)   ! neighbor node number

          j21 = (neighbor2-1)*5 + 1
          j22 = (neighbor2-1)*5 + 5

          gt2(:,:) = time_data%drdqt(i)%blocks(:,:,j2)

          if ( neighbor2 == i ) then   ! diagonal contribution
            gt2(:,:) = gt2(:,:) + time_data%vol(i) * time_matrix(:,:)
          endif

! Macro diagonal contribution (in time)

!         y(j11:j12)=y(j11:j12) + matmul(matmul(transpose(gt1),gt2),x(j21:j22))

          y(j11:j12) = y(j11:j12)                                              &
                    + matmul(matmul(transpose(gt1),matmul(vinv,gt2)),x(j21:j22))

        end do neighbors2

! Forward plane contribution

        if ( timestep < ntimesteps ) then
!         y(j11:j12) = y(j11:j12) + matmul(matmul(transpose(gt1),transpose(f)),&
!                                          x_fwd(i1:i2) )
          y(j11:j12) = y(j11:j12)                                              &
                     + matmul(matmul(transpose(gt1),matmul(vinv,transpose(f))),&
                       x_fwd(i1:i2) )
        endif

! Back plane contribution

        if ( timestep > 1 ) then
          gt(:,:) = time_data_back%drdqt(i)%blocks(:,:,j1)

          if ( neighbor1 == i ) then   ! diagonal contribution
            gt(:,:) = gt(:,:) + time_data%vol(i) * time_matrix(:,:)
          endif

!         y(i1:i2) = y(i1:i2) + matmul(matmul(f,gt), x_back(j11:j12) )

          y(i1:i2)= y(i1:i2) + matmul(matmul(f,matmul(vinv,gt)),x_back(j11:j12))
        endif

      end do neighbors1

! Diagonal contributions

!     y(i1:i2) = y(i1:i2) + matmul(matmul(f,transpose(f)), x(i1:i2))

      y(i1:i2) = y(i1:i2) + matmul(matmul(f,matmul(vinv,transpose(f))),x(i1:i2))

      psi = psi + a2inv*dot_product(time_data%res(:,i), x(i1:i2))

    end do grid_points

    do i = 1, nnodes
      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5
      y(i1:i2) = y(i1:i2) + psi*time_data%res(:,i)
    end do

  end subroutine atimesx


!===================================== BORIS_CHECK ===========================80
!
!   Checks implementation of the volume scaling
!
!=============================================================================80
  subroutine boris_check(n,x)

    use kinddefs,    only : dp
    use chaos_datas, only : timestep, time_data, dt, nnodes, ntimesteps, a2inv,&
                            m0, m1, obj, obj_target, omega, power, dt
    use lmpi,        only : lmpi_recv, lmpi_send, lmpi_success, lmpi_id,       &
                            lmpi_die, lmpi_max_and_maxid, lmpi_bcast,          &
                            lmpi_master

    integer, intent(in) :: n

    real(dp), dimension(n), intent(in)  :: x

    integer :: i, i1, i2, ierr, j21, j22, j2, j, j1
    integer :: neighbor2, neighbor, proc_with_max

    real(dp) :: psi, zbar, fbar, h, max_val

    real(dp), dimension(5,5) :: f, time_matrix, gt2, vinv, identity5, g

    real(dp), dimension(n) :: x_back, x_fwd, y_back
    real(dp), dimension(n) :: y, y0, check

  continue

    identity5(:,:) = 0.0_dp
    identity5(1,1) = 1.0_dp
    identity5(2,2) = 1.0_dp
    identity5(3,3) = 1.0_dp
    identity5(4,4) = 1.0_dp
    identity5(5,5) = 1.0_dp

    y(:) = 0.0_dp
    y0(:) = 0.0_dp

! Transfer my portion of the x vector to the processors above and below me
! if they exist

! First let's send data backwards in time, i.e., to the rank "below" us.
! This will become x_fwd on the receiving ranks. The "special" cases here are:
!   1) the last rank will not receive anything
!   2) the first rank will not send anything

    if ( timestep /= ntimesteps ) then

      call lmpi_recv(x_fwd,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for backward comm.', lmpi_id
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= 1 ) then

      call lmpi_send(x,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for backward comm.', lmpi_id
        call lmpi_die
        stop
      endif

    endif

! Now send data forwards in time, i.e., to the rank "above" us.
! This will become x_back on the receiving ranks. The "special" cases here are:
!   1) the first rank will not receive anything
!   2) the last rank will not send anything

    if ( timestep /= 1 ) then

      call lmpi_recv(x_back,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for forward comm.'
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= ntimesteps ) then

      call lmpi_send(x,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for forward comm.'
        call lmpi_die
        stop
      endif

    endif

! First compute B^T * lambda.  The result is a vector defined at m+1 time planes
! (From 0 to m)

    time_matrix = 1.0_dp / dt * identity5

    grid_points : do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      neighbors2 : do j2 = 1, time_data%drdqt(i)%n

        neighbor2 = time_data%drdqt(i)%list(j2)   ! neighbor node number

        j21 = (neighbor2-1)*5 + 1
        j22 = (neighbor2-1)*5 + 5

        gt2(:,:) = time_data%drdqt(i)%blocks(:,:,j2)

        if ( neighbor2 == i ) then   ! diagonal contribution
          gt2(:,:) = gt2(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        y(i1:i2)=y(i1:i2) + matmul(gt2,x(j21:j22))

      end do neighbors2

      if ( timestep /= ntimesteps ) then
        y(i1:i2)=y(i1:i2) + matmul(transpose(f),x_fwd(i1:i2))
      endif

      if ( timestep == 1 ) then
        y0(i1:i2)=y0(i1:i2) + matmul(transpose(f),x(i1:i2))
      endif

    end do grid_points

! Now, let's form -g - B^T*lambda

    do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      vinv = 1.0_dp / time_data%vol(i) * identity5

      y(i1:i2) = matmul(vinv,-time_data%dfdq(:,i) - y(i1:i2))

! Note that df/dQ is zero at level 0

      if ( timestep == 1 ) then
        y0(i1:i2) = matmul(vinv,-y0(i1:i2))
      endif

    end do

! Now we have the tangent solution in y and y0
! Let's compute the time dilation term

! Compute psi = 1/alpha**2 * (R^T * lambda)

    psi = 0.0_dp

    do i = 1, nnodes
      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5
      psi = psi + dot_product(time_data%res(:,i),x(i1:i2))
    end do

    zbar = 0.0_dp
    do i = m0, m1
      zbar = zbar + (obj(i) - obj_target)
    end do
    zbar = zbar / real((m1-m0+1),dp)

    fbar = omega * dt * zbar ** power

    h = power/real(m1-m0,dp)*(omega*dt*zbar**(power-1)                         &
                            *(obj(timestep)-obj_target)-fbar)

    psi = (-h - psi)*a2inv

! Transfer data to get y_back

! Now send data forwards in time, i.e., to the rank "above" us.
! This will become y_back on the receiving ranks. The "special" cases here are:
!   1) the first rank will not receive anything
!   2) the last rank will not send anything

    if ( timestep /= 1 ) then

      call lmpi_recv(y_back,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for forward comm.'
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= ntimesteps ) then

      call lmpi_send(y,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for forward comm.'
        call lmpi_die
        stop
      endif

    endif

! Verify that the residuals of the tangent block in LSS are zero
! Compute B*tangent + R*psi

    check = 0.0_dp

    do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      do j = 1, time_data%drdq(i)%n

        neighbor = time_data%drdq(i)%list(j)   ! neighbor node number

        j1 = (neighbor-1)*5 + 1
        j2 = (neighbor-1)*5 + 5

        g(:,:) = time_data%drdq(i)%blocks(:,:,j)

        if ( neighbor == i ) then   ! diagonal contribution
          g(:,:) = g(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        check(i1:i2) = check(i1:i2) + matmul(g(:,:),y(j1:j2))

      end do

      if ( timestep > 1 ) then
        check(i1:i2) = check(i1:i2) + matmul(f(:,:),y_back(i1:i2))
      else
        check(i1:i2) = check(i1:i2) + matmul(f(:,:),y0(i1:i2))
      endif

      check(i1:i2) = check(i1:i2) + time_data%res(:,i)*psi

    end do

    max_val = maxval(abs(check))

    call lmpi_max_and_maxid(max_val,proc_with_max)
    call lmpi_bcast(max_val,proc_with_max)

    if ( lmpi_master ) then
      write(*,'(a,i0,1x,e15.7)')                                               &
         ' Rank, max discrep btw Schur comp and full system = ', proc_with_max,&
                                                                 max_val
    endif

  end subroutine boris_check


!================================== BUILD_MATRIX =============================80
!
!   Constructs matrix
!   If overlapping with other time planes, be sure
!   to put these pieces in the proper location - this is
!   handled internally by the find_entry lookup function
!
!=============================================================================80
  subroutine build_matrix(a)

    use kinddefs,    only : dp
    use chaos_datas, only : time_data, dt, nnodes, timestep, ntimesteps,       &
                            time_data_back, overlap

    real(dp), dimension(:,:,:), intent(out) :: a

    integer :: i, j1, j2, neighbor1, neighbor2, indx

    real(dp), dimension(5,5) :: time_matrix, f, gt1, gt2, gt, identity5, vinv

  continue

    identity5(:,:) = 0.0_dp
    identity5(1,1) = 1.0_dp
    identity5(2,2) = 1.0_dp
    identity5(3,3) = 1.0_dp
    identity5(4,4) = 1.0_dp
    identity5(5,5) = 1.0_dp

    a = 0.0_dp

    time_matrix(:,:) = 0.0_dp
    do i = 1, 5
      time_matrix(i,i) = 1.0_dp / dt
    end do

    grid_points : do i = 1, nnodes

!     i1 = (i-1)*5 + 1 FIXME: commented code
!     i2 = (i-1)*5 + 5 FIXME: commented code

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      vinv = 1.0_dp / time_data%vol(i) * identity5

      neighbors1 : do j1 = 1, time_data%drdqt(i)%n

        neighbor1 = time_data%drdqt(i)%list(j1)   ! neighbor node number

!       j11 = (neighbor1-1)*5 + 1
!       j12 = (neighbor1-1)*5 + 5

        gt1(:,:) = time_data%drdqt(i)%blocks(:,:,j1)

        if ( neighbor1 == i ) then   ! diagonal contribution
          gt1(:,:) = gt1(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        neighbors2 : do j2 = 1, time_data%drdqt(i)%n

          neighbor2 = time_data%drdqt(i)%list(j2)   ! neighbor node number

!         j21 = (neighbor2-1)*5 + 1
!         j22 = (neighbor2-1)*5 + 5

          gt2(:,:) = time_data%drdqt(i)%blocks(:,:,j2)

          if ( neighbor2 == i ) then   ! diagonal contribution
            gt2(:,:) = gt2(:,:) + time_data%vol(i) * time_matrix(:,:)
          endif

! Macro diagonal contribution (in time)

!         y(j11:j12) = y(j11:j12)                                              &
!                   + matmul(matmul(transpose(gt1),matmul(vinv,gt2)),x(j21:j22))

          indx = find_entry(neighbor1,neighbor2,timestep,.false.)

          a(:,:,indx) = a(:,:,indx) + matmul(transpose(gt1),matmul(vinv,gt2))

        end do neighbors2

! Forward plane contribution

        if ( timestep < ntimesteps .and. overlap ) then

!         y(j11:j12) = y(j11:j12)                                              &
!                    + matmul(matmul(transpose(gt1),matmul(vinv,transpose(f))),&
!                                          x_fwd(i1:i2) )

          indx = find_entry(neighbor1,i,timestep+1,.false.)

          a(:,:,indx) = a(:,:,indx)                                            &
                      + matmul(transpose(gt1),matmul(vinv,transpose(f)))

        endif

! Back plane contribution

        if ( timestep > 1 .and. overlap ) then

          gt(:,:) = time_data_back%drdqt(i)%blocks(:,:,j1)

          if ( neighbor1 == i ) then   ! diagonal contribution
            gt(:,:) = gt(:,:) + time_data%vol(i) * time_matrix(:,:)
          endif

!         y(i1:i2)= y(i1:i2) + matmul(matmul(f,matmul(vinv,gt)),x_back(j11:j12))

          indx = find_entry(i,neighbor1,timestep-1,.false.)

          a(:,:,indx) = a(:,:,indx) + matmul(f,matmul(vinv,gt))

        endif

      end do neighbors1

! Diagonal contributions

!     y(i1:i2) = y(i1:i2) + matmul(matmul(f,matmul(vinv,transpose(f))),x(i1:i2))

      indx = find_entry(i,i,timestep,.false.)

      a(:,:,indx) = a(:,:,indx) + matmul(f,matmul(vinv,transpose(f)))

! Since the following term contributes to the entire matrix, ignore it
! in the preconditioner for now

!     y(i1:i2) = y(i1:i2) + a2inv*dot_product(time_data%res(:,i), x(i1:i2))    &
!                         * time_data%res(:,i)

    end do grid_points

  end subroutine build_matrix


!================================== FIND_ENTRY ===============================80
!
! Finds column block in a specfied row
! Takes care in the event we are overlapping the matrix
!
! If override_offsets is .true., it assumes you know precisely
! the row/col in the matrix you want to find
!
!=============================================================================80
  function find_entry(rowin,colin,time,override_offsets)

    use lmpi,        only : lmpi_die
    use chaos_datas, only : ia, ja, iau, overlap, timestep, nnodes, ntimesteps

    integer, intent(in) :: rowin, colin, time

    logical, intent(in) :: override_offsets

    integer :: find_entry, row, col, row_offset, col_offset

    integer :: j

  continue

! Determine the necessary offsets

    row_offset = 0
    col_offset = 0

    find_offsets : if ( overlap .and. (.not.override_offsets) ) then

      if ( timestep == 1 ) then

        row_offset = 0

        if ( time == 1 ) then
          col_offset = 0
        else if ( time == 2 ) then
          col_offset = nnodes
        else
          write(*,*) 'find_entry: uh oh1.'
          call lmpi_die
          stop
        endif

      else if ( timestep == ntimesteps ) then

        row_offset = nnodes

        if ( time == ntimesteps-1 ) then
          col_offset = 0
        else if ( time == ntimesteps ) then
          col_offset = nnodes
        else
          write(*,*) 'find_entry: uh oh2.'
          call lmpi_die
          stop
        endif

      else

        row_offset = nnodes

        if ( time == timestep-1 ) then
          col_offset = 0
        else if ( time == timestep ) then
          col_offset = nnodes
        else if ( time == timestep+1 ) then
          col_offset = nnodes + nnodes
        else
          write(*,*) 'find_entry: uh oh2.'
          call lmpi_die
          stop
        endif

      endif

    endif find_offsets

! Apply the necessary offsets

    row = rowin + row_offset
    col = colin + col_offset

! Now find the entry

    find_entry = 0

    if ( row == col ) then
      find_entry = iau(row)
      return
    endif

    search : do j = ia(row), ia(row+1)-1
      if ( ja(j) == col ) then
        find_entry = j
        return
      endif
    end do search

    write(*,*) 'Trouble locating entry in find_entry: ', time, rowin, colin,   &
                                                         row, col
    call lmpi_die
    stop

  end function find_entry


!================================ INSERT_IDENTITY ============================80
!
!   Inserts identity matrix into forward and backward portions
!   of overlapped matrix for testing
!
!=============================================================================80
  subroutine insert_identity(a)

    use kinddefs,    only : dp
    use chaos_datas, only : nnodes, iau, timestep, ntimesteps

    real(dp), dimension(:,:,:), intent(inout) :: a

    integer :: indx, i

  continue

    if ( timestep == 1 ) then
      do i = nnodes+1, 2*nnodes
        indx = iau(i)
        a(1,1,indx) = 1.0_dp
        a(2,2,indx) = 1.0_dp
        a(3,3,indx) = 1.0_dp
        a(4,4,indx) = 1.0_dp
        a(5,5,indx) = 1.0_dp
      end do
    else if ( timestep == ntimesteps ) then
      do i = 1, nnodes
        indx = iau(i)
        a(1,1,indx) = 1.0_dp
        a(2,2,indx) = 1.0_dp
        a(3,3,indx) = 1.0_dp
        a(4,4,indx) = 1.0_dp
        a(5,5,indx) = 1.0_dp
      end do
    else
      do i = 1, nnodes
        indx = iau(i)
        a(1,1,indx) = 1.0_dp
        a(2,2,indx) = 1.0_dp
        a(3,3,indx) = 1.0_dp
        a(4,4,indx) = 1.0_dp
        a(5,5,indx) = 1.0_dp
      end do
      do i = 2*nnodes+1, 3*nnodes
        indx = iau(i)
        a(1,1,indx) = 1.0_dp
        a(2,2,indx) = 1.0_dp
        a(3,3,indx) = 1.0_dp
        a(4,4,indx) = 1.0_dp
        a(5,5,indx) = 1.0_dp
      end do
    endif

  end subroutine insert_identity


!================================ TRANSFER_MATRIX ============================80
!
!   Passes local rows of jacobian matrix fwd and backwards in time
!
!=============================================================================80
  subroutine transfer_matrix(a)

    use kinddefs,    only : dp
    use chaos_datas, only : timestep, ntimesteps, ja, ia, nnodes
    use lmpi,        only : lmpi_send, lmpi_recv, lmpi_id, lmpi_die,           &
                            lmpi_success, lmpi_nproc, lmpi_reduce, lmpi_bcast

    real(dp), dimension(:,:,:), intent(inout) :: a

    integer :: i, istart, iend, ierr, counter, max_datasize1, forw_limit
    integer :: max_datasize2, max_datasize, indx, jstart, jend, j, row, col
    integer :: deltai, deltaj

    integer, dimension(lmpi_nproc) :: size_back_data, size_forw_data, tempg

    integer, dimension(:), allocatable :: rowsend, rowrecv
    integer, dimension(:), allocatable :: colsend, colrecv

    real(dp), dimension(:,:,:), allocatable :: datarecv, datasend

  continue

! Determine max number of 5x5 blocks any processor may send or receive
! Do this by looking at the data to be sent out

    if ( timestep == 1 ) then
      istart = 1
      iend   = nnodes
    else
      istart = nnodes+1
      iend   = 2*nnodes
    endif

    size_back_data(1:lmpi_nproc) = 0
    size_forw_data(1:lmpi_nproc) = 0

    forw_limit = nnodes+1
    if ( timestep == 1 ) forw_limit = 1

    local_rows : do i = istart, iend
      jstart = ia(i)
      jend   = ia(i+1)-1
      do j = jstart, jend
        if ( ja(j) <= 2*nnodes .and. timestep > 1 ) then
          size_back_data(lmpi_id+1) = size_back_data(lmpi_id+1) + 1
        endif
        if ( ja(j) >= forw_limit .and. timestep < ntimesteps) then
          size_forw_data(lmpi_id+1) = size_forw_data(lmpi_id+1) + 1
        endif
      end do
    end do local_rows

    call lmpi_reduce(size_back_data,tempg); call lmpi_bcast(tempg)
    size_back_data = tempg

    call lmpi_reduce(size_forw_data,tempg); call lmpi_bcast(tempg)
    size_forw_data = tempg

    max_datasize1 = maxval(size_back_data)
    max_datasize2 = maxval(size_forw_data)

    max_datasize = max(max_datasize1,max_datasize2)

! Now that we know the largest number of blocks needed to be sent,
! allocate some work arrays

    allocate(rowrecv(max_datasize))
    allocate(rowsend(max_datasize))
    allocate(colrecv(max_datasize))
    allocate(colsend(max_datasize))
    allocate(datarecv(5,5,max_datasize))
    allocate(datasend(5,5,max_datasize))

! First let's send our local rows backwards in time, i.e., to the rank
! "below" us.  This will become a_fwd on the receiving ranks.  In general,
! we will pack up the following:
!   - row number between 1 and nnodes
!   - col number between 1 and 2*nnodes
!   - 5x5 block
! Upon reception, the row and column numbers will have to be shifted
! appropriately.  The "special" cases here are:
!   1) the last rank will not receive anything
!   2) the first rank will not send anything

! First gather up the data to be sent

    gather_send_data : if ( timestep /= 1 ) then

      counter = 0

      do i = nnodes+1, 2*nnodes
        jstart = ia(i)
        jend   = ia(i+1)-1
        do j = jstart, jend
          if ( ja(j) <= 2*nnodes ) then
            counter = counter + 1
            rowsend(counter)      = i-nnodes
            colsend(counter)      = ja(j)
            datasend(:,:,counter) = a(:,:,j)
          endif
        end do
      end do

      if ( counter /= size_back_data(lmpi_id+1) ) then
        write(*,*) 'Error counting data to send backwards'
        call lmpi_die
        stop
      endif

    endif gather_send_data

! Post the receives for the row numbers

    if ( timestep /= ntimesteps ) then
      call lmpi_recv(rowrecv,size_back_data(lmpi_id+2),lmpi_id+1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for row backward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the sends for the row numbers

    if ( timestep /= 1 ) then
      call lmpi_send(rowsend,size_back_data(lmpi_id+1),lmpi_id-1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for row backward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the receives for the col numbers

    if ( timestep /= ntimesteps ) then
      call lmpi_recv(colrecv,size_back_data(lmpi_id+2),lmpi_id+1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for col backward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the sends for the col numbers

    if ( timestep /= 1 ) then
      call lmpi_send(colsend,size_back_data(lmpi_id+1),lmpi_id-1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for col backward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the receives for the actual data

    if ( timestep /= ntimesteps ) then
      call lmpi_recv(datarecv,5*5*size_back_data(lmpi_id+2),lmpi_id+1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for mat backward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the sends for the actual data

    if ( timestep /= 1 ) then
      call lmpi_send(datasend,5*5*size_back_data(lmpi_id+1),lmpi_id-1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for mat backward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Now we can unpack the data into the correct matrix locations

    unpack_recv_data : if ( timestep /= ntimesteps ) then

      do i = 1, size_back_data(lmpi_id+2)

        row = rowrecv(i) ! ranges from 1 to nnodes
        col = colrecv(i) ! ranges from 1 to 2*nnodes

        if ( timestep == 1 ) then
          indx = find_entry(row+nnodes,col,timestep,.true.)
        else
          indx = find_entry(row+2*nnodes,col+nnodes,timestep,.true.)
        endif

        a(:,:,indx) = datarecv(:,:,i)

      end do

    endif unpack_recv_data


! Now let's send our local rows forwards in time, i.e., to the rank
! "above" us.  This will become a_back on the receiving ranks.  In general,
! we will pack up the following:
!   - row number between 1 and nnodes
!   - col number between 1 and 2*nnodes
!   - 5x5 block
! Upon reception, the row and column numbers will have to be shifted
! appropriately.  The "special" cases here are:
!   1) the last rank will not send anything
!   2) the first rank will not receive anything

! First gather up the data to be sent

    gather_send_data2 : if ( timestep /= ntimesteps ) then

      counter = 0

      if ( timestep == 1 ) then
        istart = 1
        iend   = nnodes
        deltai = 0
        deltaj = 0
      else
        istart = nnodes+1
        iend   = 2*nnodes
        deltai = nnodes
        deltaj = nnodes
      endif

      do i = istart, iend
        jstart = ia(i)
        jend   = ia(i+1)-1
        do j = jstart, jend
          if ( ja(j) >= forw_limit ) then
            counter = counter + 1
            rowsend(counter)      = i-deltai
            colsend(counter)      = ja(j)-deltaj
            datasend(:,:,counter) = a(:,:,j)
          endif
        end do
      end do

      if ( counter /= size_forw_data(lmpi_id+1) ) then
        write(*,*) 'Error counting data to send forwards'
        call lmpi_die
        stop
      endif

    endif gather_send_data2

! Post the receives for the row numbers

    if ( timestep /= 1 ) then
      call lmpi_recv(rowrecv,size_forw_data(lmpi_id),lmpi_id-1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for row forward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the sends for the row numbers

    if ( timestep /= ntimesteps ) then
      call lmpi_send(rowsend,size_forw_data(lmpi_id+1),lmpi_id+1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for row forward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the receives for the col numbers

    if ( timestep /= 1 ) then
      call lmpi_recv(colrecv,size_forw_data(lmpi_id),lmpi_id-1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for col forward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the sends for the col numbers

    if ( timestep /= ntimesteps ) then
      call lmpi_send(colsend,size_forw_data(lmpi_id+1),lmpi_id+1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for col forward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the receives for the actual data

    if ( timestep /= 1 ) then
      call lmpi_recv(datarecv,5*5*size_forw_data(lmpi_id),lmpi_id-1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for mat forward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Post the sends for the actual data

    if ( timestep /= ntimesteps ) then
      call lmpi_send(datasend,5*5*size_forw_data(lmpi_id+1),lmpi_id+1,1,ierr)
      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for mat forward comm.', lmpi_id
        call lmpi_die
        stop
      endif
    endif

! Now we can unpack the data into the correct matrix locations

    unpack_recv_data2 : if ( timestep /= 1 ) then

      do i = 1, size_forw_data(lmpi_id)

        row = rowrecv(i) ! ranges from 1 to nnodes
        col = colrecv(i) ! ranges from 1 to 2*nnodes

        indx = find_entry(row,col,timestep,.true.)

        a(:,:,indx) = datarecv(:,:,i)

      end do

    endif unpack_recv_data2

    deallocate(rowrecv,rowsend,colrecv,colsend,datarecv,datasend)

  end subroutine transfer_matrix


!============================ DIAGONAL_PRECONDITIONER ========================80
!
!   Basic diagonal preconditioning Px=y
!
!=============================================================================80
  subroutine diagonal_preconditioner(n,x,y)

    use kinddefs,    only : dp
    use chaos_datas, only : nnodes, dt, time_data

    integer, intent(in) :: n

    real(dp), dimension(n), intent(inout) :: x
    real(dp), dimension(n), intent(in)    :: y

    integer :: i, j, neighbor, i1

    real(dp) :: term1, term2, term3, term4, term5

    real(dp), dimension(5,5) :: g, time_matrix

  continue

    time_matrix(:,:) = 0.0_dp
    do i = 1, 5
      time_matrix(i,i) = 1.0_dp / dt
    end do

    grid_points : do i = 1, nnodes

! First let's do F*F^T and R*R^T

      term1 = 1.0_dp/dt**2 + time_data%res(1,i)**2
      term2 = 1.0_dp/dt**2 + time_data%res(2,i)**2
      term3 = 1.0_dp/dt**2 + time_data%res(3,i)**2
      term4 = 1.0_dp/dt**2 + time_data%res(4,i)**2
      term5 = 1.0_dp/dt**2 + time_data%res(5,i)**2

! Now add in G*G^T

      neighbors : do j = 1, time_data%drdq(i)%n

        neighbor = time_data%drdq(i)%list(j)   ! neighbor node number

        g(:,:) = time_data%drdq(i)%blocks(:,:,j)

        if ( neighbor == i ) then   ! diagonal contribution
          g(:,:) = g(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        term1 = term1 + dot_product(g(1,:),g(1,:))
        term2 = term2 + dot_product(g(2,:),g(2,:))
        term3 = term3 + dot_product(g(3,:),g(3,:))
        term4 = term4 + dot_product(g(4,:),g(4,:))
        term5 = term5 + dot_product(g(5,:),g(5,:))

      end do neighbors

! This simple preconditioning will simply solve for x = 1/term * y

      i1 = (i-1)*5 + 1

      x(i1)   = 1.0_dp / term1 * y(i1)
      x(i1+1) = 1.0_dp / term2 * y(i1+1)
      x(i1+2) = 1.0_dp / term3 * y(i1+2)
      x(i1+3) = 1.0_dp / term4 * y(i1+3)
      x(i1+4) = 1.0_dp / term5 * y(i1+4)

    end do grid_points

  end subroutine diagonal_preconditioner


!================================= POSTPROCESS_TANGENT =======================80
!
!   Postprocess the tangent solution
!
!=============================================================================80
  subroutine postprocess_tangent(solution,tangent)

    use kinddefs,    only : dp
    use lmpi,        only : lmpi_send, lmpi_recv, lmpi_success, lmpi_id,       &
                            lmpi_die
    use chaos_datas, only : timestep, ntimesteps, dt, nnodes, time_data

    real(dp), dimension(:), intent(in)  :: solution
    real(dp), dimension(:), intent(out) :: tangent

    integer :: i, i1, i2, j1, j2, n, ierr, j, neighbor

    real(dp), dimension(5,5) :: time_matrix, f, gt

    real(dp), dimension(:), allocatable :: solution_fwd

  continue

    n = size(solution,1)
    allocate(solution_fwd(n))

    tangent(:) = 0.0_dp

! First let's send data backwards in time, i.e., to the rank "below" us.
! This will become x_fwd on the receiving ranks. The "special" cases here are:
!   1) the last rank will not receive anything
!   2) the first rank will not send anything

    if ( timestep /= ntimesteps ) then

      call lmpi_recv(solution_fwd,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for postprocess comm.', lmpi_id
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= 1 ) then

      call lmpi_send(solution,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for postprocess comm.', lmpi_id
        call lmpi_die
        stop
      endif

    endif

    time_matrix(:,:) = 0.0_dp
    do i = 1, 5
      time_matrix(i,i) = 1.0_dp / dt
    end do

    grid_points : do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      neighbors : do j = 1, time_data%drdqt(i)%n

        neighbor = time_data%drdqt(i)%list(j)   ! neighbor node number

        j1 = (neighbor-1)*5 + 1
        j2 = (neighbor-1)*5 + 5

        gt(:,:) = time_data%drdqt(i)%blocks(:,:,j)

        if ( neighbor == i ) then   ! diagonal contribution
          gt(:,:) = gt(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        tangent(i1:i2) = tangent(i1:i2) - matmul(gt,solution(j1:j2))

      end do neighbors

      if ( timestep < ntimesteps ) then
        tangent(i1:i2) = tangent(i1:i2)-matmul(transpose(f),solution_fwd(i1:i2))
      endif

    end do grid_points

    deallocate(solution_fwd)

  end subroutine postprocess_tangent


!=========================== POSTPROCESS_TANGENT_INITIAL =====================80
!
!   Postprocess the initial tangent solution
!
!=============================================================================80
  subroutine postprocess_tangent_initial(solution,tangent)

    use kinddefs,    only : dp
    use chaos_datas, only : dt, nnodes, time_data

    real(dp), dimension(:), intent(in)  :: solution
    real(dp), dimension(:), intent(out) :: tangent

    integer :: i, i1, i2

    real(dp), dimension(5,5) :: time_matrix, f

  continue

    tangent(:) = 0.0_dp

    time_matrix(:,:) = 0.0_dp
    do i = 1, 5
      time_matrix(i,i) = 1.0_dp / dt
    end do

    grid_points : do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      tangent(i1:i2) = - matmul(transpose(f),solution(i1:i2))

    end do grid_points

  end subroutine postprocess_tangent_initial


!================================= VERIFY_CHECK ==============================80
!
!   Checks cookbook test in the notes
!
!=============================================================================80
  subroutine verify_check(tangent,tangent0)

    use kinddefs,    only : dp
    use lmpi,        only : lmpi_send, lmpi_recv, lmpi_success, lmpi_id,       &
                            lmpi_die
    use chaos_datas, only : timestep, ntimesteps, dt, nnodes, time_data

    real(dp), dimension(:), intent(in)  :: tangent, tangent0

    integer :: i, i1, i2, j1, j2, n, ierr, j, neighbor

    real(dp) :: xnorm

    real(dp), dimension(5,5) :: time_matrix, f, g

    real(dp), dimension(:), allocatable :: tangent_back, x

  continue

    n = size(tangent,1)
    allocate(tangent_back(n))
    allocate(x(n))

    tangent_back(:) = 0.0_dp

! Now send data forwards in time, i.e., to the rank "above" us.
! This will become tangent_back on the receiving ranks. The "special" cases
! here are:
!   1) the first rank will not receive anything
!   2) the last rank will not send anything

    if ( timestep /= 1 ) then

      call lmpi_recv(tangent_back,n,lmpi_id-1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_recv() for verify_check comm.'
        call lmpi_die
        stop
      endif

    endif

    if ( timestep /= ntimesteps ) then

      call lmpi_send(tangent,n,lmpi_id+1,1,ierr)

      if ( ierr /= lmpi_success ) then
        write(*,*) 'Error in lmpi_send() for verify_check comm.'
        call lmpi_die
        stop
      endif

    endif

    x(:) = 0.0_dp

    time_matrix(:,:) = 0.0_dp
    do i = 1, 5
      time_matrix(i,i) = 1.0_dp / dt
    end do

    grid_points : do i = 1, nnodes

      i1 = (i-1)*5 + 1
      i2 = (i-1)*5 + 5

      f(:,:) = - time_data%vol(i) * time_matrix(:,:)

      neighbors : do j = 1, time_data%drdq(i)%n

        neighbor = time_data%drdq(i)%list(j)   ! neighbor node number

        j1 = (neighbor-1)*5 + 1
        j2 = (neighbor-1)*5 + 5

        g(:,:) = time_data%drdq(i)%blocks(:,:,j)

        if ( neighbor == i ) then   ! diagonal contribution
          g(:,:) = g(:,:) + time_data%vol(i) * time_matrix(:,:)
        endif

        x(i1:i2) = x(i1:i2) + matmul(g,tangent(j1:j2))

      end do neighbors

! Take special care of data associated with level 0 on the master

      if ( timestep > 1 ) then
        x(i1:i2) = x(i1:i2) + matmul(f,tangent_back(i1:i2))+time_data%drdd(:,i)
      else
        x(i1:i2) = x(i1:i2) + matmul(f,tangent0(i1:i2))+time_data%drdd(:,i)
      endif

    end do grid_points

! Now examine what is in x

    xnorm = 0.0_dp
    do i = 1, n
      xnorm = xnorm + x(i)**2
    end do

    xnorm = sqrt(xnorm/n)

    write(*,*) 'Rank, norm of x = ', lmpi_id, xnorm

    deallocate(tangent_back,x)

  end subroutine verify_check

end module chaos_construction

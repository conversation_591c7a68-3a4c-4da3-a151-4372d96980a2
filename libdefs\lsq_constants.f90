module lsq_constants

  use kinddefs,        only : dp
  use twod_util,       only : q_2d

  implicit none

  private

  public :: set_lsq_constants
  public :: mlsq, wflsq1, wflsq2, tf, cg_tol, flsqn_max, lsqn_max

  integer :: mlsq

  real(dp) :: wflsq1, wflsq2, tf, cg_tol

  integer, save :: flsqn_max = 1, lsqn_max = 1

!...references for least squares.

contains

!================================= SET_LSQ_CONSTANTS =========================80
!
! Set constants needed to evaluate lsqe (cc).
!
!=============================================================================80

  subroutine set_lsq_constants( grid, soln, crow )

    use lsq_defs,              only : cc_mapped_lsq, cylindrical_mapping,      &
                                      cgamma_tolerance, nc_mapped_lsq
    use cc_defs,               only : wlsq_face
    use grid_types,            only : grid_type
    use info_depr,             only : skeleton, ivisc
    use comprow_types,         only : crow_flow
    use solution_types,        only : soln_type
    use lmpi,                  only : lmpi_max

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(in)    :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: n, m, n1, n2, flsqn_maxg, lsqn_maxg

    integer, dimension(grid%nnodes0) :: degree

  continue

    cg_tol = cgamma_tolerance

    mlsq = 0 !ummapped_lsq
    if ( cc_mapped_lsq .or. nc_mapped_lsq ) then
      mlsq = 1 !approximate_mapped_lsq
      if ( cylindrical_mapping ) then
        mlsq = 2 !exact_mapped_lsq
      endif
    endif

    tf = 1.0_dp
    if ( q_2d ) tf = 0.0_dp

    wflsq1 = 1.0_dp
    wflsq2 = 0.0_dp
    if ( wlsq_face ) then
      wflsq1 = 0.0_dp
      wflsq2 = 1.0_dp
    endif

    if ( grid%cc ) then

      if ( ivisc > 0 .and. soln%viscous_method == 3 ) then
        do n = 1, grid%nface
          m =                                          2  & !face-adj
              +     grid%flsq_ia(n+1)-    grid%flsq_ia(n) & !augments
              + grid%bcc%flsq_ib(n+1)-grid%bcc%flsq_ib(n)   !bc-faces
          flsqn_max = max( flsqn_max , m )
        end do
      endif

      do n = 1,grid%ncell0
        m =                                           1 & !fv_sp
          +         crow%ia_ns(n) -          crow%ia(n) & !fptr-faces
          +     grid%rlsq_ia(n+1) -     grid%rlsq_ia(n) & !augments
          + grid%bcc%clsq_ib(n+1) - grid%bcc%clsq_ib(n)   !bc-faces
        lsqn_max = max( lsqn_max , m )
      enddo

    else

      degree = 0
      do n = 1, grid%nedgeloc
        n1 = grid%eptr(1,n)
        n2 = grid%eptr(2,n)
        if ( n1 <= grid%nnodes0 ) degree(n1) = degree(n1) + 1
        if ( n2 <= grid%nnodes0 ) degree(n2) = degree(n2) + 1
      end do
      lsqn_max = max( lsqn_max, maxval(degree) )

    endif

    call lmpi_max( flsqn_max, flsqn_maxg )
    call lmpi_max(  lsqn_max,  lsqn_maxg )


    if ( skeleton > 0 ) then
      if ( grid%cc ) then
        write(*,*) 'LSQ: Constants set via lsq_constants (cc path):'
      else
        write(*,*) 'LSQ: Constants set via lsq_constants (nc path):'
      endif
      write(*,*) 'LSQ: .......igrid=',grid%igrid
      write(*,*) 'LSQ: ......origin=',grid%origin
      if ( grid%cc )                          &
      write(*,*) 'LSQ: ...flsqn_max=',flsqn_maxg
      write(*,*) 'LSQ:.....lsqn_max=', lsqn_maxg
    endif

  end subroutine set_lsq_constants

end module lsq_constants

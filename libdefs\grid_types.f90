module grid_types

  use kinddefs,      only : dp, system_i1, system_i2, system_i8
  use bc_types,      only : bcgrid_type!complex_needs_real_version_too
  use bc_types,      only : bcc_type
  use element_types, only : elem_type

  implicit none

  private
  public :: grid_type
  public :: mass_type
  public :: raw_grid_data_type
  public :: na_type

  type grid_type

    character    (len=80)                :: project  ! Case name

    character    (len=80)                :: grid_motion ! Grid motion type:
                                                        ! static/rigid/deform

    integer :: partid   ! Partition ID

    integer :: nnodes0        ! Number of level 0 nodes
    integer :: nnodes01       ! Number of level 0+1 nodes
    integer :: nnodesg        ! Total nodes in global mesh

    integer :: nedge         ! Total edges for partition
    integer :: nedgeloc      ! Number of edges level 0&1
    integer :: nedgeloc_2d   ! Number of 2D edges
    integer(system_i8) :: nedgeg        ! Number of global edges

    integer :: ncell0   ! Number of level 0 cells        THESE GUYS ARE CELL-
    integer :: ncell01  ! Number of level0+1 cells       CENTERED-SPECIFIC
    integer :: ncellg   ! Total cells in global mesh     VARIABLES, WHERE WE USE
    integer :: nface    ! Number of faces                CONCATENATED LISTS OF
    integer :: nfaceg   ! Number of global faces         CELLS AND FACES.
    integer :: nbface0  ! Level 0 boundary faces
    integer :: nbface01 ! Level01 boundary faces
    integer :: nbfaceg  ! Total # of global boundary faces
    integer :: ncell_augmentors ! Total # of cells used to augment LSQ
    integer :: nface_augmentors ! Total # of cells used to augment face LSQ

    integer :: nbound   ! Number of boundaries

    integer :: nelem    ! Number of element types

    integer :: idistfcn ! is there a distance fcn

    integer :: origin = 1 ! origin of grid
             ! origin  =1, grid generation (with full element definitions)
             ! origin  =2, from grid agglomeration (currently not used)
             ! origin  =3, dual/cell agglomeration
             ! origin  =4, cell agglomeration (second order viscous)
    integer :: igrid = 1  ! grid number ( 1 is also for non-multigrid )
             ! igrid   =1,     finest grid
             ! igrid   =ngrid, coarsest grid
    logical :: cc     !discretization
             ! cc     =.true.  cell-centered
             ! cc     =.false. node-centered

    integer :: symmetry_x_faces
    integer :: symmetry_y_faces
    integer :: symmetry_z_faces
    integer,  dimension(:), pointer :: symmetry_x_cells
    integer,  dimension(:), pointer :: symmetry_y_cells
    integer,  dimension(:), pointer :: symmetry_z_cells

    real(dp),  dimension(:), pointer :: symmetry_x_coord
    real(dp),  dimension(:), pointer :: symmetry_y_coord
    real(dp),  dimension(:), pointer :: symmetry_z_coord

    integer,  dimension(:),   pointer :: l2g         ! Local-to-global (nodes)
    integer(system_i8), dimension(:), pointer :: el2g! Local-to-global (edges)
    integer,  dimension(:),   pointer :: cl2g        ! Local-to-global (cells)
    integer,  dimension(:),   pointer :: fl2g        ! Local-to-global (faces)
    integer,  dimension(:,:), pointer :: eptr        ! Edge pointers
    integer,  dimension(:,:), pointer :: fptr        ! Face pointers
    integer,  dimension(:),   pointer :: symmetry    ! symmetry flag at node
    integer,  dimension(:),   pointer :: jag         ! boundary surface steps
    integer, dimension(:),    pointer :: flsq_ia     ! crs for face-lsq
    integer, dimension(:),    pointer :: flsq_ja     ! crs for face-lsq

    integer(kind=system_i1), dimension(:), pointer :: flsq_flag_rhs
    integer(kind=system_i1), dimension(:), pointer :: flsq_flag_lhs
    real(dp), dimension(:,:,:), pointer :: flsq_lu   ! flsq lu decomposition

    real(dp), dimension(:),   pointer :: xq,yq,zq    ! Generalized pointers
    real(dp), dimension(:),   pointer :: volq        !

    real(dp), dimension(:),   pointer :: x,y,z             ! Coordinates Node
    real(dp), dimension(:),   pointer :: xc,yc,zc          ! Coordinates Cell
    real(dp), dimension(:),   pointer :: xat0 ,yat0 ,zat0  ! x,y,z at t=0
    real(dp), dimension(:),   pointer :: xatn ,yatn ,zatn  ! x,y,z at t(n  )
    real(dp), dimension(:),   pointer :: xatn1,yatn1,zatn1 ! x,y,z at t(n-1)
    real(dp), dimension(:),   pointer :: xatn2,yatn2,zatn2 ! x,y,z at t(n-2)
    real(dp), dimension(:),   pointer :: xatn3,yatn3,zatn3 ! x,y,z at t(n-3)
    real(dp), dimension(:),   pointer :: xatn4,yatn4,zatn4 ! x,y,z at t(n-4)
    real(dp), dimension(:),   pointer :: vol        ! Dual cell volumes
    real(dp), dimension(:),   pointer :: volatn     ! Dual cell vol at t(n)
    real(dp), dimension(:),   pointer :: volatn1    ! Dual cell vol at t(n-1)
    real(dp), dimension(:),   pointer :: volatn2    ! Dual cell vol at t(n-2)
    real(dp), dimension(:),   pointer :: volatn3    ! Dual cell vol at t(n-2)
    real(dp), dimension(:),   pointer :: volatn4    ! Dual cell vol at t(n-3)
    real(dp), dimension(:),   pointer :: volatnp1   ! Dual cell vol at t(n+1)
    real(dp), dimension(:),   pointer :: volatnp2   ! Dual cell vol at t(n+2)
    real(dp), dimension(:),   pointer :: cell_vol   ! Cell-centered volumes
    real(dp), dimension(:),   pointer :: area_face  ! Cell-centered face area
    real(dp), dimension(:),   pointer :: xn_face    ! Cell-centered face xnrm
    real(dp), dimension(:),   pointer :: yn_face    ! Cell-centered face ynrm
    real(dp), dimension(:),   pointer :: zn_face    ! Cell-centered face znrm
    real(dp), dimension(:),   pointer :: x_face     ! Cell-centered face centr.
    real(dp), dimension(:),   pointer :: y_face     ! Cell-centered face centr.
    real(dp), dimension(:),   pointer :: z_face     ! Cell-centered face centr.
    real(dp), dimension(:),   pointer :: s_face     ! Cell-centered face centr.
    real(dp), dimension(:),   pointer :: slen       ! Distance to surface.
    integer,  dimension(:),   pointer :: iflagslen  ! distance fcn help
    integer,  dimension(:,:), pointer :: inodeslen  ! nearest-node(s) (global #)
    real(dp), dimension(:),   pointer :: des_slen   ! Local size for DES
    real(dp), dimension(:),   pointer :: xn         ! X normal to dual faces
    real(dp), dimension(:),   pointer :: yn         ! Y normal to dual faces
    real(dp), dimension(:),   pointer :: zn         ! Z normal to dual faces
    real(dp), dimension(:),   pointer :: dxdt       ! Grid point X-velocity
    real(dp), dimension(:),   pointer :: dydt       ! Grid point Y-velocity
    real(dp), dimension(:),   pointer :: dzdt       ! Grid point Z-velocity
    real(dp), dimension(:),   pointer :: dxdtatn1   ! Grid point X-velocity
    real(dp), dimension(:),   pointer :: dydtatn1   ! Grid point Y-velocity
    real(dp), dimension(:),   pointer :: dzdtatn1   ! Grid point Z-velocity
    real(dp), dimension(:),   pointer :: dxdtatn2   ! Grid point X-velocity
    real(dp), dimension(:),   pointer :: dydtatn2   ! Grid point Y-velocity
    real(dp), dimension(:),   pointer :: dzdtatn2   ! Grid point Z-velocity
    real(dp), dimension(:),   pointer :: facespeed  ! Dual face speed
    real(dp), dimension(:),   pointer :: ra         ! Area normal to faces
    real(dp), dimension(:),   pointer :: r11        ! Stuff for least squares
    real(dp), dimension(:),   pointer :: r12        ! Stuff for least squares
    real(dp), dimension(:),   pointer :: r13        ! Stuff for least squares
    real(dp), dimension(:),   pointer :: r22        ! Stuff for least squares
    real(dp), dimension(:),   pointer :: r23        ! Stuff for least squares
    real(dp), dimension(:),   pointer :: r33        ! Stuff for least squares
    real(dp), dimension(:),   pointer :: wr11       ! weighted-least-squares
    real(dp), dimension(:),   pointer :: wr12       ! weighted-least-squares
    real(dp), dimension(:),   pointer :: wr13       ! weighted-least-squares
    real(dp), dimension(:),   pointer :: wr22       ! weighted-least squares
    real(dp), dimension(:),   pointer :: wr23       ! weighted-least-squares
    real(dp), dimension(:),   pointer :: wr33       ! weighted-least-squares

    !...cgamma...characterizing high-curvature high-aspect-ratio problem areas.
    real(dp), dimension(:), pointer :: cgamma
    !...location of nearest point on surface for distance function.
    real(dp), dimension(:), pointer :: slenxn, slenyn, slenzn

    real(dp), dimension(:,:,:),  pointer :: rlsq ! Stuff for least squares
    real(dp), dimension(:,:,:),  pointer :: nlsq ! Stuff for least squares

    integer, dimension(:), pointer :: rlsq_ia ! crs for least squares
    integer, dimension(:), pointer :: rlsq_ja ! crs for least squares

    real(dp), dimension(:,:), pointer :: res_gcl   ! GCL res; deforming mesh
                                                   ! (rank-2 to match flow res)
    real(dp), dimension(:,:), pointer :: phi       ! radius of insc sphere,
                                                   ! min at node & helper
    real(dp), dimension(:,:), pointer :: weight    ! edge-based tet visc weights

!   data for keeping track of grid position and orientation as functions of time
    real(dp), dimension(:),   pointer :: thetax   ! Grid rotation wrt x-axis
    real(dp), dimension(:),   pointer :: thetay   ! Grid rotation wrt y-axis
    real(dp), dimension(:),   pointer :: thetaz   ! Grid rotation wrt z-axis
    real(dp), dimension(:),   pointer :: xorig    ! Grid x-orign
    real(dp), dimension(:),   pointer :: yorig    ! Grid y-orign
    real(dp), dimension(:),   pointer :: zorig    ! Grid z-orign

    type(elem_type),   dimension(:), pointer :: elem ! Element type data

    type(bcgrid_type), dimension(:), pointer :: bc   ! Boundary data

!   sorted global and local node id pairs for binary search
    integer,  dimension(:),   pointer :: sortedglobal => null()
    integer,  dimension(:),   pointer :: sortedlocal => null()
    integer                           :: nsorted ! number of sorted nodes

    integer,  dimension(:),   pointer :: firstn2c => null() ! first n2c entry
    integer,  dimension(:,:), pointer :: n2c => null()      ! node to cell adj
    integer                           :: firstemptyn2c  ! empty n2c entry

    integer                           :: firstemptyc2n   ! empty c2n entry
    integer                           :: firstemptynode  ! empty node entry
    integer                           :: firstemptynode0 ! empty node0 entry

    integer,  dimension(:),   pointer :: part => null() ! node processor id

    integer,  dimension(:),   pointer :: iblank ! blank nodes for overset
    integer,  dimension(:),   pointer :: imesh  ! node => mesh for overset

    integer :: nnodes0_2d        ! level 0  nodes
    integer :: dof0              ! = nnodes0_2d if twod element-based grid
                                 ! = nnodes0    if twod agglom-grid or 3D
                                 ! = ncell0     if cell-centered.

    integer,  dimension(:,:), pointer :: node_pairs_2d ! node pairs

    type(bcc_type), pointer :: bcc    ! Boundary condition for cc path

    integer(system_i2), dimension(:), pointer :: cell_skewness !cell_skewness

    integer(system_i1), dimension(:), pointer :: br_flag !boundary relaxation
!...these would be great to have as system_i1 if we supported xfer!!
    integer, dimension(:), pointer :: skip_q        !skip_q
    integer, dimension(:), pointer :: boundary_flag !track corner/ridge/valley
                                                    !/interior

    logical :: skip_q_allocated

  end type grid_type

  type mass_type
    integer                         :: itotal         ! Global node number
    integer,  dimension(:), pointer :: inodemt        ! Global node number
    real(dp), dimension(:), pointer :: xmt,ymt,zmt    ! Node coordinates
  end type mass_type

  type raw_grid_data_type
    logical :: read_part_files      = .false. ! Read part files
    logical :: write_part_files     = .false. ! Dump part files and quit
    logical :: twod_mode            = .false. ! Force 2D mode
    logical :: swap_yz_axes         = .false. ! Swap y- and z-axes in raw grid
    logical :: ignore_euler_number  = .false. ! ignore euler number problems
    character(len=6 ) :: patch_lumping = 'none' ! 'none' 'bc' 'family'
    character(len=6 ) ::fieldview_coordinate_precision='single'!'single''double'
    character(len=20) :: grid_format = 'aflr3' ! FAST, VGRID, etc
    character(len=20) :: data_format = 'stream'! 'ASCII', 'unformatted',
                                               ! 'stream', 'stream64'
  end type raw_grid_data_type

  type na_type

    real(dp), dimension(:), pointer :: lambdax, lambday, lambdaz
    real(dp), dimension(:), pointer :: weight_sum, weight_sum_inv_qt
    real(dp), dimension(:), pointer :: slenxn, slenyn, slenzn, cgamma, slen
    integer,  dimension(:), pointer :: n_sum

  end type na_type

end module grid_types

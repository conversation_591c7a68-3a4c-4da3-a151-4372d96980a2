# Makefile.in generated by automake 1.11.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008, 2009  Free Software Foundation,
# Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@


VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
DIST_COMMON = $(srcdir)/../Common.am $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in $(top_srcdir)/make.rules
@BUILD_IRS_SUPPORT_TRUE@am__append_1 = -L@irslibrary@ -lirs
@BUILD_SSDC_SUPPORT_TRUE@am__append_2 = -L@ssdclibrary@ -lssdc
@BUILD_SFE_SUPPORT_TRUE@am__append_3 = -L@sfelibrary@ -lsfe
@BUILD_SPARSKIT_SUPPORT_TRUE@am__append_4 = -L@sparskitlibrary@ -lskit
@BUILD_PARMETIS_SUPPORT_TRUE@am__append_5 = @parmetis_ldadd@
@BUILD_DIRTLIB_SUPPORT_TRUE@@BUILD_MPI_TRUE@am__append_6 = \
@BUILD_DIRTLIB_SUPPORT_TRUE@@BUILD_MPI_TRUE@	-L@dirtlibrary@ -ldirt_mpich -lp3d

@BUILD_DIRTLIB_SUPPORT_TRUE@@BUILD_MPI_FALSE@am__append_7 = \
@BUILD_DIRTLIB_SUPPORT_TRUE@@BUILD_MPI_FALSE@	-L@dirtlibrary@ -ldirt -lp3d

@BUILD_MPI_TRUE@@BUILD_SUGGAR_SUPPORT_TRUE@am__append_8 = \
@BUILD_MPI_TRUE@@BUILD_SUGGAR_SUPPORT_TRUE@	-L@suggarlibrary@ -lsuggar_mpi -lp3d -lexpat -lpthread -lstdc++

@BUILD_MPI_FALSE@@BUILD_SUGGAR_SUPPORT_TRUE@am__append_9 = \
@BUILD_MPI_FALSE@@BUILD_SUGGAR_SUPPORT_TRUE@	-L@suggarlibrary@ -lsuggar -lp3d -lexpat -lpthread -lstdc++

@BUILD_DYMORE_SUPPORT_TRUE@am__append_10 = -L@dymorelibrary@ -ldymore4
@BUILD_RCAS_SDX_SUPPORT_TRUE@am__append_11 = -L@sdxlibrary@ -lsdx
@BUILD_MESHSIM_SUPPORT_TRUE@am__append_12 = @meshsim_ldadd@
@BUILD_REFINE_SUPPORT_TRUE@am__append_13 = @refine_ldadd@
@BUILD_TECIO_SUPPORT_TRUE@am__append_14 = @TECIOLIBS@
@BUILD_CGNS_SUPPORT_TRUE@am__append_15 = -L@CGNSlibrary@ -lcgns
@BUILD_SBOOM_SUPPORT_TRUE@am__append_16 = -L@SBOOMlibrary@ -lsboomadjoint
@BUILD_PUNDIT_SUPPORT_TRUE@am__append_17 = -L@punditlibrary@ -lPUNDIT
@BUILD_SIXDOF_SUPPORT_TRUE@am__append_18 = \
@BUILD_SIXDOF_SUPPORT_TRUE@	-L@SIXDOFLIBS@/Motion/lib -lmo \
@BUILD_SIXDOF_SUPPORT_TRUE@	-L@SIXDOFLIBS@/HT/lib -lht \
@BUILD_SIXDOF_SUPPORT_TRUE@	-L@SIXDOFLIBS@/EXP/lib -lexp

@BUILD_KNIFE_SUPPORT_TRUE@am__append_19 = @knife_ldadd@
@BUILD_VISIT_SUPPORT_TRUE@am__append_20 = -L@VisItlibrary@ -lsimV2f -lsimV2 -ldl
@BUILD_CUDA_SUPPORT_TRUE@am__append_21 = -L@CUDA_LIB_PATH@ -lcudart -lcuda
@BUILD_FORTRAN_C_INTEROP_SUPPORT_TRUE@am__append_22 = -lstdc++
bin_PROGRAMS = complex_nodet@MPI_EXT@$(EXEEXT)
subdir = FUN3D_90/Complex
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps =  \
	$(top_srcdir)/aclocal/ax_f90_module_extension.m4 \
	$(top_srcdir)/aclocal/ax_f90_module_flag.m4 \
	$(top_srcdir)/aclocal/capri.m4 $(top_srcdir)/aclocal/cgns.m4 \
	$(top_srcdir)/aclocal/cuda.m4 \
	$(top_srcdir)/aclocal/dynamic_loading.m4 \
	$(top_srcdir)/aclocal/f90_tuner.m4 \
	$(top_srcdir)/aclocal/f90_unix.m4 \
	$(top_srcdir)/aclocal/fccht.m4 \
	$(top_srcdir)/aclocal/fortran_2003_environment.m4 \
	$(top_srcdir)/aclocal/fortran_asynchronous_io.m4 \
	$(top_srcdir)/aclocal/fortran_c_interoperability.m4 \
	$(top_srcdir)/aclocal/fortran_etime.m4 \
	$(top_srcdir)/aclocal/fortran_open_big_endian.m4 \
	$(top_srcdir)/aclocal/fortran_open_stream.m4 \
	$(top_srcdir)/aclocal/fortran_posix_interface.m4 \
	$(top_srcdir)/aclocal/fun3d.m4 $(top_srcdir)/aclocal/gsi.m4 \
	$(top_srcdir)/aclocal/meshsim.m4 $(top_srcdir)/aclocal/mpi.m4 \
	$(top_srcdir)/aclocal/parmetis.m4 \
	$(top_srcdir)/aclocal/resource_limit.m4 \
	$(top_srcdir)/aclocal/zoltan.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
LIBRARIES = $(noinst_LIBRARIES)
AR = ar
ARFLAGS = cru
libFUN3DFlow_a_AR = $(AR) $(ARFLAGS)
libFUN3DFlow_a_DEPENDENCIES =
am__objects_1 = composite_jacobian.$(OBJEXT) \
	composite_jacobian_util.$(OBJEXT) defect_correction.$(OBJEXT) \
	flow.$(OBJEXT) survey_matrix.$(OBJEXT)
nodist_libFUN3DFlow_a_OBJECTS = $(am__objects_1)
libFUN3DFlow_a_OBJECTS = $(nodist_libFUN3DFlow_a_OBJECTS)
am__installdirs = "$(DESTDIR)$(bindir)"
PROGRAMS = $(bin_PROGRAMS)
am__objects_2 = main.$(OBJEXT)
nodist_complex_nodet@MPI_EXT@_OBJECTS = $(am__objects_2)
complex_nodet@MPI_EXT@_OBJECTS =  \
	$(nodist_complex_nodet@MPI_EXT@_OBJECTS)
am__DEPENDENCIES_1 =
am__DEPENDENCIES_2 = libFUN3DFlow.a \
	$(PHYSICS_DEPS_DIR)/libFUN3DPhysicsDeps.a \
	$(PHYSICS_DIR)/libFUN3DPhysics.a $(LIBF90_DIR)/libsink.a \
	$(LIBINIT_DIR)/libinit.a $(LIBDDFB_DIR)/libDDFB.a \
	$(LIBSMEMRD_DIR)/libsmemrd.a $(LIBTURB_DIR)/libturb.a \
	$(LIBDEFS_DIR)/libdefs.a $(ENGINESIM_DIR)/libenginesim.a \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(LIBCORE_DIR)/libcore.a $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1) $(am__DEPENDENCIES_1) \
	$(am__DEPENDENCIES_1)
complex_nodet@MPI_EXT@_DEPENDENCIES = $(am__DEPENDENCIES_2)
DEFAULT_INCLUDES = -I.@am__isrc@ -I$(top_builddir)
PPFCCOMPILE = $(FC) $(DEFS) $(DEFAULT_INCLUDES) $(INCLUDES) \
	$(AM_CPPFLAGS) $(CPPFLAGS) $(AM_FCFLAGS) $(FCFLAGS)
FCLD = $(FC)
FCLINK = $(FCLD) $(AM_FCFLAGS) $(FCFLAGS) $(AM_LDFLAGS) $(LDFLAGS) -o \
	$@
FCCOMPILE = $(FC) $(AM_FCFLAGS) $(FCFLAGS)
SOURCES = $(nodist_libFUN3DFlow_a_SOURCES) \
	$(nodist_complex_nodet@MPI_EXT@_SOURCES)
DIST_SOURCES =
ETAGS = etags
CTAGS = ctags
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ACLOCAL_AMFLAGS = @ACLOCAL_AMFLAGS@
AMTAR = @AMTAR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BIBTEX = @BIBTEX@
CAPRIheader = @CAPRIheader@
CAPRIlibrary = @CAPRIlibrary@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CGNSinclude = @CGNSinclude@
CGNSlibrary = @CGNSlibrary@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CUDACC = @CUDACC@
CUDAFLAGS = @CUDAFLAGS@
CUDA_LIB_PATH = @CUDA_LIB_PATH@
CXX = @CXX@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F90_EXT_LIB = @F90_EXT_LIB@
FC = @FC@
FCFLAGS = @FCFLAGS@
FCLIBS = @FCLIBS@
FC_MODEXT = @FC_MODEXT@
FC_MODINC = @FC_MODINC@
GREP = @GREP@
HAVE_F2PY = @HAVE_F2PY@
HAVE_RUBY = @HAVE_RUBY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
KNIFE_SUBDIR = @KNIFE_SUBDIR@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
MOD_DEP_COMPILER = @MOD_DEP_COMPILER@
MPIF90 = @MPIF90@
MPIINC = @MPIINC@
MPIRUN = @MPIRUN@
MPI_EXT = @MPI_EXT@
MPI_Prefix = @MPI_Prefix@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PDFLATEX = @PDFLATEX@
PERL5 = @PERL5@
PHYSICS_TYPE = @PHYSICS_TYPE@
PYTHON = @PYTHON@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SUBDIR = @PYTHON_SUBDIR@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
REFINE_SUBDIR = @REFINE_SUBDIR@
SBOOMlibrary = @SBOOMlibrary@
SDKheader = @SDKheader@
SDKlibrary = @SDKlibrary@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SIXDOFLIBS = @SIXDOFLIBS@
STRIP = @STRIP@
TECIOLIBS = @TECIOLIBS@
VERSION = @VERSION@
VisItinclude = @VisItinclude@
VisItlibrary = @VisItlibrary@
XMKMF = @XMKMF@
Xheader = @Xheader@
Xlibrary = @Xlibrary@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_FC = @ac_ct_FC@
ac_empty = @ac_empty@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
dirtlibrary = @dirtlibrary@
docdir = @docdir@
dotlibrary = @dotlibrary@
dvidir = @dvidir@
dymorelibrary = @dymorelibrary@
exec_prefix = @exec_prefix@
fcompiler = @fcompiler@
have_bibtex = @have_bibtex@
have_latex = @have_latex@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
irslibrary = @irslibrary@
knife_deps = @knife_deps@
knife_ldadd = @knife_ldadd@
ksoptlibrary = @ksoptlibrary@
libcore_path = @libcore_path@
libdir = @libdir@
libexecdir = @libexecdir@
libturb_path = @libturb_path@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
meshsim_ldadd = @meshsim_ldadd@
mkdir_p = @mkdir_p@
mpi_ldadd = @mpi_ldadd@
npsollibrary = @npsollibrary@
oldincludedir = @oldincludedir@
parmetis_include = @parmetis_include@
parmetis_ldadd = @parmetis_ldadd@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
portlibrary = @portlibrary@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
punditinclude = @punditinclude@
punditlibrary = @punditlibrary@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
refine_deps = @refine_deps@
refine_ldadd = @refine_ldadd@
sbindir = @sbindir@
sdxlibrary = @sdxlibrary@
sfelibrary = @sfelibrary@
sharedstatedir = @sharedstatedir@
snoptlibrary = @snoptlibrary@
sparskitlibrary = @sparskitlibrary@
srcdir = @srcdir@
ssdclibrary = @ssdclibrary@
subdirs = @subdirs@
suggarlibrary = @suggarlibrary@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
zoltan_include = @zoltan_include@
zoltan_ldadd = @zoltan_ldadd@
LIBCORE_DIR = @libcore_path@/Complex
LIBTURB_DIR = @libturb_path@/Complex
LIBSMEMRD_DIR = @top_builddir@/libsmemrd/Complex
LIBDDFB_DIR = @top_builddir@/libddfb
FUNCLIB_DIR = @top_builddir@/FuncLib90/Complex
LIBDEFS_DIR = @top_builddir@/libdefs/Complex
LIBINIT_DIR = @top_builddir@/libinit/Complex
LIBF90_DIR = @top_builddir@/LibF90/Complex
PHYSICS_DIR = @top_builddir@/@PHYSICS_TYPE@/Complex
PHYSICS_DEPS_DIR = @top_builddir@/PHYSICS_DEPS/Complex
ENGINESIM_DIR = @top_builddir@/enginesim/src
FUN3D_90_DIR = @top_builddir@/FUN3D_90/Complex
libFUN3DFlow_SRCS = \
	composite_jacobian.f90 \
	composite_jacobian_util.f90 \
	defect_correction.f90 \
	flow.F90 \
	survey_matrix.f90

nodet_SRCS = \
	main.f90

nodet_LDSTUFF = libFUN3DFlow.a \
	$(PHYSICS_DEPS_DIR)/libFUN3DPhysicsDeps.a \
	$(PHYSICS_DIR)/libFUN3DPhysics.a $(LIBF90_DIR)/libsink.a \
	$(LIBINIT_DIR)/libinit.a $(LIBDDFB_DIR)/libDDFB.a \
	$(LIBSMEMRD_DIR)/libsmemrd.a $(LIBTURB_DIR)/libturb.a \
	$(LIBDEFS_DIR)/libdefs.a $(ENGINESIM_DIR)/libenginesim.a \
	$(am__append_1) $(am__append_2) $(am__append_3) \
	$(am__append_4) $(LIBCORE_DIR)/libcore.a $(am__append_5) \
	$(am__append_6) $(am__append_7) $(am__append_8) \
	$(am__append_9) $(am__append_10) $(am__append_11) \
	$(am__append_12) $(am__append_13) $(am__append_14) \
	$(am__append_15) $(am__append_16) $(am__append_17) \
	$(am__append_18) $(am__append_19) $(am__append_20) \
	@F90_EXT_LIB@ @zoltan_ldadd@ $(am__append_21) $(am__append_22)
AM_FCFLAGS = \
	$(FC_MODINC)$(LIBCORE_DIR)\
	$(FC_MODINC)$(LIBDEFS_DIR)\
	$(FC_MODINC)$(LIBTURB_DIR) \
	$(FC_MODINC)$(LIBSMEMRD_DIR) \
	$(FC_MODINC)$(LIBINIT_DIR)\
	$(FC_MODINC)$(LIBF90_DIR) \
	$(FC_MODINC)$(PHYSICS_DIR) \
	$(FC_MODINC)$(PHYSICS_DEPS_DIR) \
	$(FC_MODINC)@top_builddir@


# remove *.mod *.fh when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT) mpif.h *.time *.mod *.fh *.d \
	$(BUILT_SOURCES)
libFUN3DFlow_f90s = $(libFUN3DFlow_SRCS:.F90=.f90) \
	$(nodet_SRCS:.F90=.f90)
libFUN3DFlow_deps = $(libFUN3DFlow_f90s:.f90=.d)
DISTCLEANFILES = $(libFUN3DFlow_deps)
SUFFIXES = .d
BUILT_SOURCES = $(libFUN3DFlow_deps) \
	$(nodist_complex_nodet@MPI_EXT@_SOURCES) \
	$(nodist_libFUN3DFlow_a_SOURCES)
CORE_LIBS = libcore.a
DEFS_LIBS = libdefs.a
INIT_LIBS = libinit.a
TURB_LIBS = libturb.a
SMEMRD_LIBS = libsmemrd.a
FUN3D_F90_LIBS = libsink.a
lib_MODULES = $(libFUN3DFlow_f90s:.f90=.$(FC_MODEXT))
noinst_LIBRARIES = libFUN3DFlow.a
libFUN3DFlow_a_LIBADD = 
nodist_libFUN3DFlow_a_SOURCES = $(libFUN3DFlow_SRCS)
libFUN3DFlow_a_LINK = $(F90LINK)
complex_nodet@MPI_EXT@_LDADD = $(nodet_LDSTUFF)
nodist_complex_nodet@MPI_EXT@_SOURCES = $(nodet_SRCS)
all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
.SUFFIXES: .d .F90 .f90 .o .obj
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am $(srcdir)/../Common.am $(top_srcdir)/make.rules $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu FUN3D_90/Complex/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu FUN3D_90/Complex/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):

clean-noinstLIBRARIES:
	-test -z "$(noinst_LIBRARIES)" || rm -f $(noinst_LIBRARIES)
libFUN3DFlow.a: $(libFUN3DFlow_a_OBJECTS) $(libFUN3DFlow_a_DEPENDENCIES) 
	-rm -f libFUN3DFlow.a
	$(libFUN3DFlow_a_AR) libFUN3DFlow.a $(libFUN3DFlow_a_OBJECTS) $(libFUN3DFlow_a_LIBADD)
	$(RANLIB) libFUN3DFlow.a
install-binPROGRAMS: $(bin_PROGRAMS)
	@$(NORMAL_INSTALL)
	test -z "$(bindir)" || $(MKDIR_P) "$(DESTDIR)$(bindir)"
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	for p in $$list; do echo "$$p $$p"; done | \
	sed 's/$(EXEEXT)$$//' | \
	while read p p1; do if test -f $$p; \
	  then echo "$$p"; echo "$$p"; else :; fi; \
	done | \
	sed -e 'p;s,.*/,,;n;h' -e 's|.*|.|' \
	    -e 'p;x;s,.*/,,;s/$(EXEEXT)$$//;$(transform);s/$$/$(EXEEXT)/' | \
	sed 'N;N;N;s,\n, ,g' | \
	$(AWK) 'BEGIN { files["."] = ""; dirs["."] = 1 } \
	  { d=$$3; if (dirs[d] != 1) { print "d", d; dirs[d] = 1 } \
	    if ($$2 == $$4) files[d] = files[d] " " $$1; \
	    else { print "f", $$3 "/" $$4, $$1; } } \
	  END { for (d in files) print "f", d, files[d] }' | \
	while read type dir files; do \
	    if test "$$dir" = .; then dir=; else dir=/$$dir; fi; \
	    test -z "$$files" || { \
	      echo " $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files '$(DESTDIR)$(bindir)$$dir'"; \
	      $(INSTALL_PROGRAM_ENV) $(INSTALL_PROGRAM) $$files "$(DESTDIR)$(bindir)$$dir" || exit $$?; \
	    } \
	; done

uninstall-binPROGRAMS:
	@$(NORMAL_UNINSTALL)
	@list='$(bin_PROGRAMS)'; test -n "$(bindir)" || list=; \
	files=`for p in $$list; do echo "$$p"; done | \
	  sed -e 'h;s,^.*/,,;s/$(EXEEXT)$$//;$(transform)' \
	      -e 's/$$/$(EXEEXT)/' `; \
	test -n "$$list" || exit 0; \
	echo " ( cd '$(DESTDIR)$(bindir)' && rm -f" $$files ")"; \
	cd "$(DESTDIR)$(bindir)" && rm -f $$files

clean-binPROGRAMS:
	-test -z "$(bin_PROGRAMS)" || rm -f $(bin_PROGRAMS)
complex_nodet@MPI_EXT@$(EXEEXT): $(complex_nodet@MPI_EXT@_OBJECTS) $(complex_nodet@MPI_EXT@_DEPENDENCIES) 
	@rm -f complex_nodet@MPI_EXT@$(EXEEXT)
	$(FCLINK) $(complex_nodet@MPI_EXT@_OBJECTS) $(complex_nodet@MPI_EXT@_LDADD) $(LIBS)

mostlyclean-compile:
	-rm -f *.$(OBJEXT)

distclean-compile:
	-rm -f *.tab.c

.F90.o:
	$(PPFCCOMPILE) -c -o $@ $<

.F90.obj:
	$(PPFCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

.f90.o:
	$(FCCOMPILE) -c -o $@ $<

.f90.obj:
	$(FCCOMPILE) -c -o $@ `$(CYGPATH_W) '$<'`

ID: $(HEADERS) $(SOURCES) $(LISP) $(TAGS_FILES)
	list='$(SOURCES) $(HEADERS) $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	mkid -fID $$unique
tags: TAGS

TAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	set x; \
	here=`pwd`; \
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	shift; \
	if test -z "$(ETAGS_ARGS)$$*$$unique"; then :; else \
	  test -n "$$unique" || unique=$$empty_fix; \
	  if test $$# -gt 0; then \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      "$$@" $$unique; \
	  else \
	    $(ETAGS) $(ETAGSFLAGS) $(AM_ETAGSFLAGS) $(ETAGS_ARGS) \
	      $$unique; \
	  fi; \
	fi
ctags: CTAGS
CTAGS:  $(HEADERS) $(SOURCES)  $(TAGS_DEPENDENCIES) \
		$(TAGS_FILES) $(LISP)
	list='$(SOURCES) $(HEADERS)  $(LISP) $(TAGS_FILES)'; \
	unique=`for i in $$list; do \
	    if test -f "$$i"; then echo $$i; else echo $(srcdir)/$$i; fi; \
	  done | \
	  $(AWK) '{ files[$$0] = 1; nonempty = 1; } \
	      END { if (nonempty) { for (i in files) print i; }; }'`; \
	test -z "$(CTAGS_ARGS)$$unique" \
	  || $(CTAGS) $(CTAGSFLAGS) $(AM_CTAGSFLAGS) $(CTAGS_ARGS) \
	     $$unique

GTAGS:
	here=`$(am__cd) $(top_builddir) && pwd` \
	  && $(am__cd) $(top_srcdir) \
	  && gtags -i $(GTAGS_ARGS) "$$here"

distclean-tags:
	-rm -f TAGS ID GTAGS GRTAGS GSYMS GPATH tags

distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile $(LIBRARIES) $(PROGRAMS) all-local
installdirs:
	for dir in "$(DESTDIR)$(bindir)"; do \
	  test -z "$$dir" || $(MKDIR_P) "$$dir"; \
	done
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)
	-test -z "$(DISTCLEANFILES)" || rm -f $(DISTCLEANFILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-am

clean-am: clean-binPROGRAMS clean-generic clean-noinstLIBRARIES \
	mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-compile distclean-generic \
	distclean-tags

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am: install-binPROGRAMS

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-compile mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am: uninstall-binPROGRAMS

.MAKE: all check install install-am install-strip

.PHONY: CTAGS GTAGS all all-am all-local check check-am clean \
	clean-binPROGRAMS clean-generic clean-noinstLIBRARIES ctags \
	distclean distclean-compile distclean-generic distclean-tags \
	distdir dvi dvi-am html html-am info info-am install \
	install-am install-binPROGRAMS install-data install-data-am \
	install-dvi install-dvi-am install-exec install-exec-am \
	install-html install-html-am install-info install-info-am \
	install-man install-pdf install-pdf-am install-ps \
	install-ps-am install-strip installcheck installcheck-am \
	installdirs maintainer-clean maintainer-clean-generic \
	mostlyclean mostlyclean-compile mostlyclean-generic pdf pdf-am \
	ps ps-am tags uninstall uninstall-am uninstall-binPROGRAMS


-include $(libFUN3DFlow_deps)
%.o %.$(FC_MODEXT): %.F90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(PPFCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.obj %.$(FC_MODEXT): %.F90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(PPFCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.o %.$(FC_MODEXT): %.f90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(FCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

%.obj %.$(FC_MODEXT): %.f90
	@$(PERL5) $(top_srcdir)/Perl/compile_mod.pl -fc '$(FCCOMPILE) -c -o $*.o $<' -provides '$*.$(FC_MODEXT) $*.o' -requires '$^' -cmp '$(top_srcdir)/Perl/check_interface.pl $(realpath $<)' -mod_ext '$(FC_MODEXT)' -src $(realpath $<)

all-local:
	@$(top_srcdir)/remove_stalemods.sh $(FC_MODEXT) $(sort $(lib_MODULES))

clean-stalemods:
	@if `/bin/ls *.o > /dev/null 2>&1`; \
	then \
	  for i in *.o; \
	   do \
	     root=`echo "$$i" | sed 's/\.o$$//'`; \
	     if test ! -e "$(srcdir)/$$root.f90" -a ! -e "$(srcdir)/$$root.F90" -a ! -e "$(srcdir)/$$root.c"; \
	     then \
	       echo "Removing $(srcdir)/$$root objects..."; \
	       /bin/rm -f "$$root.o" "$$root.$(FC_MODEXT)" "$$root.fh" "$$root.mod" "$$root.d"; \
	     fi; \
	   done; \
	fi

clean-stalemods-complex:
	@if `/bin/ls *.o > /dev/null 2>&1`; \
	then \
	  for i in *.o; \
	   do \
	     root=`echo "$$i" | sed 's/\.o$$//'`; \
	     if test ! -e "$(srcdir)/../$$root.f90" -a ! -e "$(srcdir)/../$$root.F90" -a ! -e "$(srcdir)/../$$root.c"; \
	     then \
	       echo "Removing $(srcdir)/../$$root objects..."; \
	       /bin/rm -f "$$root.o" "$$root.$(FC_MODEXT)" "$$root.fh" "$$root.mod" "$$root.d" "$$root.f90" "$$root.F90"; \
	     fi; \
	   done; \
	fi

%.f90: $(top_srcdir)/FUN3D_90/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/FUN3D_90/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDDFB_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-I $(PHYSICS_DEPS_DIR) \
	-L $(FUNCLIB_DIR) \
	-L $(LIBTURB_DIR) > $@

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

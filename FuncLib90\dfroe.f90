!============================ DFROE ==========================================80
!
! Roe flux jacobians
!
! Note: ql, qr are conservative variables
!
! All derivatives worked out in detail
!
! The left  jacobian is returned in dfroe(:,:,1)
! The right jacobian is returned in dfroe(:,:,2)
!
!=============================================================================80

  pure function dfroe(xnorm, ynorm, znorm, area, face_speed, ql, qr)

    use kinddefs,  only : dp
    use fun3d_constants, only : my_0, my_1
    use inviscid_flux,     only : lhs_a_eigenvalue_coef, lhs_u_eigenvalue_coef
    use fluid,     only : gm1

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(5), intent(in) :: ql, qr

    real(dp), dimension(5,5,2)         :: dfroe

    real(dp) :: flux1rl,flux1ul,flux1vl,flux1wl
    real(dp) :: flux2rl,flux2ul,flux2vl,flux2wl,flux2pl
    real(dp) :: flux3rl,flux3ul,flux3vl,flux3wl,flux3pl
    real(dp) :: flux4rl,flux4ul,flux4vl,flux4wl,flux4pl
    real(dp) :: flux5rl,flux5ul,flux5vl,flux5wl,flux5pl

    real(dp) :: flux1rr,flux1ur,flux1vr,flux1wr
    real(dp) :: flux2rr,flux2ur,flux2vr,flux2wr,flux2pr
    real(dp) :: flux3rr,flux3ur,flux3vr,flux3wr,flux3pr
    real(dp) :: flux4rr,flux4ur,flux4vr,flux4wr,flux4pr
    real(dp) :: flux5rr,flux5ur,flux5vr,flux5wr,flux5pr

    real(dp) :: t1rl,t1ul,t1vl,t1wl,t1pl
    real(dp) :: t2rl,t2ul,t2vl,t2wl,t2pl
    real(dp) :: t3rl,t3ul,t3vl,t3wl,t3pl
    real(dp) :: t4rl,t4ul,t4vl,t4wl,t4pl
    real(dp) :: t5rl,t5ul,t5vl,t5wl,t5pl

    real(dp) :: t1rr,t1ur,t1vr,t1wr,t1pr
    real(dp) :: t2rr,t2ur,t2vr,t2wr,t2pr
    real(dp) :: t3rr,t3ur,t3vr,t3wr,t3pr
    real(dp) :: t4rr,t4ur,t4vr,t4wr,t4pr
    real(dp) :: t5rr,t5ur,t5vr,t5wr,t5pr

    real(dp) :: r54
    real(dp) :: r54rl,r54ul,r54vl,r54wl
    real(dp) :: r54rr,r54ur,r54vr,r54wr

    real(dp) :: r44
    real(dp) :: r44rl,r44wl
    real(dp) :: r44rr,r44wr

    real(dp) :: r34
    real(dp) :: r34rl,r34vl
    real(dp) :: r34rr,r34vr

    real(dp) :: r24
    real(dp) :: r24rl,r24ul
    real(dp) :: r24rr,r24ur

    real(dp) :: r53
    real(dp) :: r53rl,r53ul,r53vl,r53wl
    real(dp) :: r53rr,r53ur,r53vr,r53wr

    real(dp) :: r43
    real(dp) :: r43rl,r43ul,r43vl,r43wl
    real(dp) :: r43rr,r43ur,r43vr,r43wr

    real(dp) :: r33
    real(dp) :: r33rl,r33ul,r33vl,r33wl
    real(dp) :: r33rr,r33ur,r33vr,r33wr

    real(dp) :: r23
    real(dp) :: r23rl,r23ul,r23vl,r23wl
    real(dp) :: r23rr,r23ur,r23vr,r23wr

    real(dp) :: r52
    real(dp) :: r52rl,r52ul,r52vl,r52wl,r52pl
    real(dp) :: r52rr,r52ur,r52vr,r52wr,r52pr

    real(dp) :: r42
    real(dp) :: r42rl,r42ul,r42vl,r42wl,r42pl
    real(dp) :: r42rr,r42ur,r42vr,r42wr,r42pr

    real(dp) :: r32
    real(dp) :: r32rl,r32ul,r32vl,r32wl,r32pl
    real(dp) :: r32rr,r32ur,r32vr,r32wr,r32pr

    real(dp) :: r22
    real(dp) :: r22rl,r22ul,r22vl,r22wl,r22pl
    real(dp) :: r22rr,r22ur,r22vr,r22wr,r22pr

    real(dp) :: r51
    real(dp) :: r51rl,r51ul,r51vl,r51wl,r51pl
    real(dp) :: r51rr,r51ur,r51vr,r51wr,r51pr

    real(dp) :: r41
    real(dp) :: r41rl,r41ul,r41vl,r41wl,r41pl
    real(dp) :: r41rr,r41ur,r41vr,r41wr,r41pr

    real(dp) :: r31
    real(dp) :: r31rl,r31ul,r31vl,r31wl,r31pl
    real(dp) :: r31rr,r31ur,r31vr,r31wr,r31pr

    real(dp) :: r21
    real(dp) :: r21rl,r21ul,r21vl,r21wl,r21pl
    real(dp) :: r21rr,r21ur,r21vr,r21wr,r21pr

    real(dp) :: dv1
    real(dp) :: dv1rl,dv1ul,dv1vl,dv1wl,dv1pl
    real(dp) :: dv1rr,dv1ur,dv1vr,dv1wr,dv1pr

    real(dp) :: dv2
    real(dp) :: dv2rl,dv2ul,dv2vl,dv2wl,dv2pl
    real(dp) :: dv2rr,dv2ur,dv2vr,dv2wr,dv2pr

    real(dp) :: dv3
    real(dp) :: dv3rl
    real(dp) :: dv3rr

    real(dp) :: dv4
    real(dp) :: dv4rl,dv4ul,dv4vl,dv4wl,dv4pl
    real(dp) :: dv4rr,dv4ur,dv4vr,dv4wr,dv4pr

    real(dp) :: c2
    real(dp) :: c2rl,c2ul,c2vl,c2wl,c2pl
    real(dp) :: c2rr,c2ur,c2vr,c2wr,c2pr

    real(dp) :: dubar
    real(dp) :: dubarrl,dubarul,dubarvl,dubarwl
    real(dp) :: dubarrr,dubarur,dubarvr,dubarwr

    real(dp) :: du
    real(dp) :: durl,duul
    real(dp) :: durr,duur

    real(dp) :: dv
    real(dp) :: dvrl,dvvl
    real(dp) :: dvrr,dvvr

    real(dp) :: dw
    real(dp) :: dwrl,dwwl
    real(dp) :: dwrr,dwwr

    real(dp) :: dpress
    real(dp) :: dpressrl,dpressul,dpressvl,dpresswl,dpresspl
    real(dp) :: dpressrr,dpressur,dpressvr,dpresswr,dpresspr

    real(dp) :: drho
    real(dp) :: drhorl
    real(dp) :: drhorr

    real(dp) :: eig1, abseig1
    real(dp) :: eig1rl,eig1ul,eig1vl,eig1wl,eig1pl
    real(dp) :: eig1rr,eig1ur,eig1vr,eig1wr,eig1pr

    real(dp) :: eig2, abseig2
    real(dp) :: eig2rl,eig2ul,eig2vl,eig2wl,eig2pl
    real(dp) :: eig2rr,eig2ur,eig2vr,eig2wr,eig2pr

    real(dp) :: eig3, abseig3
    real(dp) :: eig3rl,eig3ul,eig3vl,eig3wl,eig3pl
    real(dp) :: eig3rr,eig3ur,eig3vr,eig3wr,eig3pr

    real(dp) :: maxeig
    real(dp) :: maxeigrl,maxeigul,maxeigvl,maxeigwl,maxeigpl
    real(dp) :: maxeigrr,maxeigur,maxeigvr,maxeigwr,maxeigpr

    real(dp) :: ubar
    real(dp) :: ubarrl,ubarul,ubarvl,ubarwl
    real(dp) :: ubarrr,ubarur,ubarvr,ubarwr

    real(dp) :: c
    real(dp) :: crl,cul,cvl,cwl,cpl
    real(dp) :: crr,cur,cvr,cwr,cpr

    real(dp) :: q2
    real(dp) :: q2rl,q2ul,q2vl,q2wl
    real(dp) :: q2rr,q2ur,q2vr,q2wr

    real(dp) :: h
    real(dp) :: hrl,hul,hvl,hwl,hpl
    real(dp) :: hrr,hur,hvr,hwr,hpr

    real(dp) :: u
    real(dp) :: url,uul
    real(dp) :: urr,uur

    real(dp) :: v
    real(dp) :: vrl,vvl
    real(dp) :: vrr,vvr

    real(dp) :: w
    real(dp) :: wrl,wwl
    real(dp) :: wrr,wwr

    real(dp) :: wat
    real(dp) :: watrl
    real(dp) :: watrr

    real(dp) :: rho
    real(dp) :: rhorl
    real(dp) :: rhorr

    real(dp) :: ubarr
    real(dp) :: ubarrrr,ubarrur,ubarrvr,ubarrwr

    real(dp) :: hr
    real(dp) :: hrrr,hrur,hrvr,hrwr,hrpr

    real(dp) :: enrgyr
    real(dp) :: enrgyrpr

    real(dp) :: pressr
    real(dp) :: pressrrr,pressrur,pressrvr,pressrwr,pressrpr

    real(dp) :: q2r
    real(dp) :: q2rrr,q2rur,q2rvr,q2rwr

    real(dp) :: ur
    real(dp) :: urrr,urur

    real(dp) :: vr
    real(dp) :: vrrr,vrvr

    real(dp) :: wr
    real(dp) :: wrrr,wrwr

    real(dp) :: rhor
    real(dp) :: rhorrr

    real(dp) :: ubarl
    real(dp) :: ubarlrl,ubarlul,ubarlvl,ubarlwl

    real(dp) :: hl
    real(dp) :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp) :: enrgyl
    real(dp) :: enrgylpl

    real(dp) :: pressl
    real(dp) :: presslrl,presslul,presslvl,presslwl,presslpl

    real(dp) :: q2l
    real(dp) :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp) :: ul
    real(dp) :: ulrl,ulul

    real(dp) :: vl
    real(dp) :: vlrl,vlvl

    real(dp) :: wl
    real(dp) :: wlrl,wlwl

    real(dp) :: rhol
    real(dp) :: rholrl

    real(dp) :: eigeps1, eigeps2, eigeps3
    real(dp) :: d1, d2, d3

    real(dp) :: fa, faeps, absfa, dm, dterm

  continue

! Primitive variables on "left" side of face

      rhol = ql(1)
        rholrl = 1.0_dp
      ul = ql(2) / rhol
        ulrl = -ul/rhol
        ulul = 1.0_dp / rhol
      vl = ql(3) / rhol
        vlrl = -vl/rhol
        vlvl = 1.0_dp / rhol
      wl = ql(4) / rhol
        wlrl = -wl/rhol
        wlwl = 1.0_dp / rhol

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = ql(5)
        enrgylpl = 1.0_dp

      pressl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
        presslrl = -0.5_dp*gm1*(rhol*q2lrl + q2l*rholrl)
        presslul = -0.5_dp*gm1*rhol*q2lul
        presslvl = -0.5_dp*gm1*rhol*q2lvl
        presslwl = -0.5_dp*gm1*rhol*q2lwl
        presslpl = gm1

      Hl = (enrgyl + pressl)/rhol
        Hlrl = (rhol*(presslrl) - (enrgyl+pressl)*rholrl) / rhol / rhol
        Hlul = (rhol*(presslul)) / rhol / rhol
        Hlvl = (rhol*(presslvl)) / rhol / rhol
        Hlwl = (rhol*(presslwl)) / rhol / rhol
        Hlpl = (rhol*(enrgylpl+presslpl)) / rhol / rhol

      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed
        ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
        ubarlul = xnorm*ulul
        ubarlvl = ynorm*vlvl
        ubarlwl = znorm*wlwl

! Primitive variables on "right" side of face

      rhor = qr(1)
        rhorrr = 1.0_dp

      ur = qr(2) / rhor
        urrr = -ur/rhor
        urur = 1.0_dp / rhor

      vr = qr(3) / rhor
        vrrr = -vr/rhor
        vrvr = 1.0_dp / rhor

      wr = qr(4) / rhor
        wrrr = -wr/rhor
        wrwr = 1.0_dp / rhor

      q2r = ur*ur + vr*vr + wr*wr
        q2rrr = 2.0_dp*ur*urrr + 2.0_dp*vr*vrrr + 2.0_dp*wr*wrrr
        q2rur = 2.0_dp*ur*urur
        q2rvr = 2.0_dp*vr*vrvr
        q2rwr = 2.0_dp*wr*wrwr

      enrgyr = qr(5)
        enrgyrpr = 1.0_dp

      pressr = gm1*(enrgyr - 0.5_dp*rhor*q2r)
        pressrrr = -0.5_dp*gm1*(rhor*q2rrr + q2r*rhorrr)
        pressrur = -0.5_dp*gm1*rhor*q2rur
        pressrvr = -0.5_dp*gm1*rhor*q2rvr
        pressrwr = -0.5_dp*gm1*rhor*q2rwr
        pressrpr = gm1

      Hr = (enrgyr + pressr)/rhor
        Hrrr = (rhor*(pressrrr) - (enrgyr+pressr)*rhorrr) / rhor / rhor
        Hrur = (rhor*(pressrur)) / rhor / rhor
        Hrvr = (rhor*(pressrvr)) / rhor / rhor
        Hrwr = (rhor*(pressrwr)) / rhor / rhor
        Hrpr = (rhor*(enrgyrpr+pressrpr)) / rhor / rhor

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed
        ubarrrr = xnorm*urrr + ynorm*vrrr + znorm*wrrr
        ubarrur = xnorm*urur
        ubarrvr = ynorm*vrvr
        ubarrwr = znorm*wrwr

! Compute Roe averages

      rho = sqrt(rhol*rhor)

      rhorl = 0.5_dp / rho * (rhor*rholrl)
      rhorr = 0.5_dp / rho * (rhol*rhorrr)

      wat = rho/(rho + rhor)
        watrl = ((rho + rhor)*rhorl - rho*(rhorl)) / (rho+rhor) / (rho+rhor)
        watrr = ((rho + rhor)*rhorr-rho*(rhorr + rhorrr))/(rho+rhor)/(rho+rhor)

      u = ul*wat + ur*(1.0_dp - wat)
        url = ul*watrl + wat*ulrl + ur*(-watrl)
        uul = wat*ulul
        urr = ul*watrr + ur*(-watrr) + (1.0_dp - wat)*urrr
        uur = (1.0_dp - wat)*urur

      v = vl*wat + vr*(1.0_dp - wat)
        vrl = vl*watrl + wat*vlrl + vr*(-watrl)
        vvl = wat*vlvl
        vrr = vl*watrr + vr*(-watrr) + (1.0_dp - wat)*vrrr
        vvr = (1.0_dp - wat)*vrvr

      w = wl*wat + wr*(1.0_dp - wat)
        wrl = wl*watrl + wat*wlrl + wr*(-watrl)
        wwl = wat*wlwl
        wrr = wl*watrr + wr*(-watrr) + (1.0_dp - wat)*wrrr
        wwr = (1.0_dp - wat)*wrwr

      H = Hl*wat + Hr*(1.0_dp - wat)
        Hrl = Hl*watrl + wat*Hlrl + Hr*(-watrl)
        Hul = wat*Hlul
        Hvl = wat*Hlvl
        Hwl = wat*Hlwl
        Hpl = wat*Hlpl

        Hrr = Hl*watrr + Hr*(-watrr) + (1.0_dp - wat)*Hrrr
        Hur = (1.0_dp - wat)*Hrur
        Hvr = (1.0_dp - wat)*Hrvr
        Hwr = (1.0_dp - wat)*Hrwr
        Hpr = (1.0_dp - wat)*Hrpr

      q2 = u*u + v*v + w*w
        q2rl = 2.0_dp*u*url + 2.0_dp*v*vrl + 2.0_dp*w*wrl
        q2ul = 2.0_dp*u*uul
        q2vl = 2.0_dp*v*vvl
        q2wl = 2.0_dp*w*wwl

        q2rr = 2.0_dp*u*urr + 2.0_dp*v*vrr + 2.0_dp*w*wrr
        q2ur = 2.0_dp*u*uur
        q2vr = 2.0_dp*v*vvr
        q2wr = 2.0_dp*w*wwr

      c = sqrt(gm1*(H - 0.5_dp*q2))
        crl = 0.5_dp / c * gm1*(Hrl - 0.5_dp*q2rl)
        cul = 0.5_dp / c * gm1*(Hul - 0.5_dp*q2ul)
        cvl = 0.5_dp / c * gm1*(Hvl - 0.5_dp*q2vl)
        cwl = 0.5_dp / c * gm1*(Hwl - 0.5_dp*q2wl)
        cpl = 0.5_dp / c * gm1*(Hpl)

        crr = 0.5_dp / c * gm1*(Hrr - 0.5_dp*q2rr)
        cur = 0.5_dp / c * gm1*(Hur - 0.5_dp*q2ur)
        cvr = 0.5_dp / c * gm1*(Hvr - 0.5_dp*q2vr)
        cwr = 0.5_dp / c * gm1*(Hwr - 0.5_dp*q2wr)
        cpr = 0.5_dp / c * gm1*(Hpr)

      ubar = xnorm*u + ynorm*v + znorm*w - face_speed
        ubarrl = xnorm*url + ynorm*vrl + znorm*wrl
        ubarul = xnorm*uul
        ubarvl = ynorm*vvl
        ubarwl = znorm*wwl

        ubarrr = xnorm*urr + ynorm*vrr + znorm*wrr
        ubarur = xnorm*uur
        ubarvr = ynorm*vvr
        ubarwr = znorm*wwr

! Eigenvalue limiting.  In terms of dimensional equations:
! -limit eigenvalues as fraction of local maximum

      fa    = ubar
      faeps = 0.05_dp*c
      absfa = abs( fa )
      if ( absfa < faeps ) absfa = 0.5_dp*(fa**2/faeps + faeps)

      maxeig = absfa + c

      if(abs(fa) < faeps ) then
        dm = fa/faeps
      elseif(fa > my_0) then
        dm = my_1
      else
        dm =-my_1
      endif

      dterm = my_1 + 0.5_dp*(my_1 - dm**2)*0.05_dp

        maxeigrl = dm*(ubarrl) + crl*dterm
        maxeigul = dm*(ubarul) + cul*dterm
        maxeigvl = dm*(ubarvl) + cvl*dterm
        maxeigwl = dm*(ubarwl) + cwl*dterm
        maxeigpl = cpl*dterm

        maxeigrr = dm*(ubarrr) + crr*dterm
        maxeigur = dm*(ubarur) + cur*dterm
        maxeigvr = dm*(ubarvr) + cvr*dterm
        maxeigwr = dm*(ubarwr) + cwr*dterm
        maxeigpr = cpr*dterm

! Now compute eigenvalues, eigenvectors, and strengths

      eig1 = ubar + c
      eig2 = ubar - c
      eig3 = ubar

! acoustic eigenvalue limiters

      eigeps1 = lhs_a_eigenvalue_coef*maxeig
      eigeps2 = lhs_a_eigenvalue_coef*maxeig

! convective eigenvalue limiter

      eigeps3 = lhs_u_eigenvalue_coef*maxeig

      abseig1 = abs( eig1 )
      abseig2 = abs( eig2 )
      abseig3 = abs( eig3 )

      if(abseig1 < eigeps1) abseig1 = 0.5_dp*(eig1**2/eigeps1 + eigeps1)
      if(abseig2 < eigeps2) abseig2 = 0.5_dp*(eig2**2/eigeps2 + eigeps2)
      if(abseig3 < eigeps3) abseig3 = 0.5_dp*(eig3**2/eigeps3 + eigeps3)

      if(abs(eig1) < eigeps1 ) then
        d1 = eig1/eigeps1
      elseif(eig1 > my_0) then
        d1 = my_1
      else
        d1 =-my_1
      endif

      dterm = 0.5_dp*(my_1 - d1**2)*lhs_a_eigenvalue_coef

        eig1rl = d1*(ubarrl + crl) + dterm*maxeigrl
        eig1ul = d1*(ubarul + cul) + dterm*maxeigul
        eig1vl = d1*(ubarvl + cvl) + dterm*maxeigvl
        eig1wl = d1*(ubarwl + cwl) + dterm*maxeigwl
        eig1pl = d1*(cpl) + dterm*maxeigpl

        eig1rr = d1*(ubarrr + crr) + dterm*maxeigrr
        eig1ur = d1*(ubarur + cur) + dterm*maxeigur
        eig1vr = d1*(ubarvr + cvr) + dterm*maxeigvr
        eig1wr = d1*(ubarwr + cwr) + dterm*maxeigwr
        eig1pr = d1*(cpr) + dterm*maxeigpr

      if(abs(eig2) < eigeps2 ) then
        d2 = eig2/eigeps2
      elseif(eig2 > my_0) then
        d2 = my_1
      else
        d2 =-my_1
      endif

      dterm = 0.5_dp*(my_1 - d2**2)*lhs_a_eigenvalue_coef

        eig2rl = d2*(ubarrl - crl) + dterm*maxeigrl
        eig2ul = d2*(ubarul - cul) + dterm*maxeigul
        eig2vl = d2*(ubarvl - cvl) + dterm*maxeigvl
        eig2wl = d2*(ubarwl - cwl) + dterm*maxeigwl
        eig2pl = d2*(- cpl) + dterm*maxeigpl

        eig2rr = d2*(ubarrr - crr) + dterm*maxeigrr
        eig2ur = d2*(ubarur - cur) + dterm*maxeigur
        eig2vr = d2*(ubarvr - cvr) + dterm*maxeigvr
        eig2wr = d2*(ubarwr - cwr) + dterm*maxeigwr
        eig2pr = d2*(- cpr) + dterm*maxeigpr

      if(abs(eig3) < eigeps3 ) then
        d3 = eig3/eigeps3
      elseif(eig3 > my_0) then
        d3 = my_1
      else
        d3 =-my_1
      endif

      dterm = 0.5_dp*(my_1 - d3**2)*lhs_u_eigenvalue_coef

        eig3rl = d3*ubarrl + dterm*maxeigrl
        eig3ul = d3*ubarul + dterm*maxeigul
        eig3vl = d3*ubarvl + dterm*maxeigvl
        eig3wl = d3*ubarwl + dterm*maxeigwl
        eig3pl = dterm*maxeigpl

        eig3rr = d3*ubarrr + dterm*maxeigrr
        eig3ur = d3*ubarur + dterm*maxeigur
        eig3vr = d3*ubarvr + dterm*maxeigvr
        eig3wr = d3*ubarwr + dterm*maxeigwr
        eig3pr = dterm*maxeigpr

      drho = rhor - rhol
        drhorl = - rholrl
        drhorr = rhorrr

      dpress = pressr - pressl
        dpressrl = - presslrl
        dpressul = - presslul
        dpressvl = - presslvl
        dpresswl = - presslwl
        dpresspl = - presslpl

        dpressrr = pressrrr
        dpressur = pressrur
        dpressvr = pressrvr
        dpresswr = pressrwr
        dpresspr = pressrpr

      du = ur - ul
        durl = - ulrl
        duul = - ulul
        durr = urrr
        duur = urur

      dv = vr - vl
        dvrl = - vlrl
        dvvl = - vlvl
        dvrr = vrrr
        dvvr = vrvr

      dw = wr - wl
        dwrl = - wlrl
        dwwl = - wlwl
        dwrr = wrrr
        dwwr = wrwr

      dubar = ubarr - ubarl
        dubarrl = - ubarlrl
        dubarul = - ubarlul
        dubarvl = - ubarlvl
        dubarwl = - ubarlwl

        dubarrr = ubarrrr
        dubarur = ubarrur
        dubarvr = ubarrvr
        dubarwr = ubarrwr

      c2 = c*c
        c2rl = 2.0_dp * c * crl
        c2ul = 2.0_dp * c * cul
        c2vl = 2.0_dp * c * cvl
        c2wl = 2.0_dp * c * cwl
        c2pl = 2.0_dp * c * cpl

        c2rr = 2.0_dp * c * crr
        c2ur = 2.0_dp * c * cur
        c2vr = 2.0_dp * c * cvr
        c2wr = 2.0_dp * c * cwr
        c2pr = 2.0_dp * c * cpr

! jumps have units of density

      dv1 = 0.5_dp*(dpress + rho*c*dubar)/c2
        dv1rl = 0.5_dp*(c2*(dpressrl + rho*(c*dubarrl + dubar*crl)      &
                + c*dubar*rhorl) - (dpress + rho*c*dubar)*c2rl) / c2 / c2
        dv1ul = 0.5_dp*(c2*(dpressul + rho*(c*dubarul + dubar*cul))     &
                - (dpress + rho*c*dubar)*c2ul) / c2 / c2
        dv1vl = 0.5_dp*(c2*(dpressvl + rho*(c*dubarvl + dubar*cvl))     &
                - (dpress + rho*c*dubar)*c2vl) / c2 / c2
        dv1wl = 0.5_dp*(c2*(dpresswl + rho*(c*dubarwl + dubar*cwl))     &
                - (dpress + rho*c*dubar)*c2wl) / c2 / c2
        dv1pl = 0.5_dp*(c2*(dpresspl + rho*(dubar*cpl))                 &
                - (dpress + rho*c*dubar)*c2pl) / c2 / c2

        dv1rr = 0.5_dp*(c2*(dpressrr + rho*(c*dubarrr + dubar*crr)      &
                + c*dubar*rhorr) - (dpress + rho*c*dubar)*c2rr)         &
                / c2 / c2
        dv1ur = 0.5_dp*(c2*(dpressur + rho*(c*dubarur + dubar*cur))     &
                - (dpress + rho*c*dubar)*c2ur) / c2 / c2
        dv1vr = 0.5_dp*(c2*(dpressvr + rho*(c*dubarvr + dubar*cvr))     &
                - (dpress + rho*c*dubar)*c2vr) / c2 / c2
        dv1wr = 0.5_dp*(c2*(dpresswr + rho*(c*dubarwr + dubar*cwr))     &
                - (dpress + rho*c*dubar)*c2wr) / c2 / c2
        dv1pr = 0.5_dp*(c2*(dpresspr + rho*(dubar*cpr))                 &
                - (dpress + rho*c*dubar)*c2pr) / c2 / c2

      dv2 = 0.5_dp*(dpress - rho*c*dubar)/c2
        dv2rl = 0.5_dp*(c2*(dpressrl - rho*(c*dubarrl + dubar*crl)      &
                - c*dubar*rhorl) - (dpress - rho*c*dubar)*c2rl)         &
                / c2 / c2
        dv2ul = 0.5_dp*(c2*(dpressul - rho*(c*dubarul + dubar*cul))     &
                - (dpress - rho*c*dubar)*c2ul) / c2 / c2
        dv2vl = 0.5_dp*(c2*(dpressvl - rho*(c*dubarvl + dubar*cvl))     &
                - (dpress - rho*c*dubar)*c2vl) / c2 / c2
        dv2wl = 0.5_dp*(c2*(dpresswl - rho*(c*dubarwl + dubar*cwl))     &
                - (dpress - rho*c*dubar)*c2wl) / c2 / c2
        dv2pl = 0.5_dp*(c2*(dpresspl - rho*(dubar*cpl))                 &
                - (dpress - rho*c*dubar)*c2pl) / c2 / c2

        dv2rr = 0.5_dp*(c2*(dpressrr - rho*(c*dubarrr + dubar*crr)      &
                - c*dubar*rhorr) - (dpress - rho*c*dubar)*c2rr)         &
                / c2 / c2
        dv2ur = 0.5_dp*(c2*(dpressur - rho*(c*dubarur + dubar*cur))     &
                - (dpress - rho*c*dubar)*c2ur) / c2 / c2
        dv2vr = 0.5_dp*(c2*(dpressvr - rho*(c*dubarvr + dubar*cvr))     &
                - (dpress - rho*c*dubar)*c2vr) / c2 / c2
        dv2wr = 0.5_dp*(c2*(dpresswr - rho*(c*dubarwr + dubar*cwr))     &
                - (dpress - rho*c*dubar)*c2wr) / c2 / c2
        dv2pr = 0.5_dp*(c2*(dpresspr - rho*(dubar*cpr))                 &
                - (dpress - rho*c*dubar)*c2pr) / c2 / c2

      dv3 = rho
        dv3rl = rhorl
        dv3rr = rhorr

      dv4 = (c*c*drho - dpress)/c2
        dv4rl = (c2*((c*(c*drhorl+drho*crl)+c*drho*crl) - dpressrl)        &
                - (c*c*drho - dpress)*c2rl) / c2 / c2
        dv4ul = (c2*((c*(drho*cul)+c*drho*cul) - dpressul)                 &
                - (c*c*drho - dpress)*c2ul) / c2 / c2
        dv4vl = (c2*((c*(drho*cvl)+c*drho*cvl) - dpressvl)                 &
                - (c*c*drho - dpress)*c2vl) / c2 / c2
        dv4wl = (c2*((c*(drho*cwl)+c*drho*cwl) - dpresswl)                 &
                - (c*c*drho - dpress)*c2wl) / c2 / c2
        dv4pl = (c2*((c*(drho*cpl)+c*drho*cpl) - dpresspl)                 &
                - (c*c*drho - dpress)*c2pl) / c2 / c2

        dv4rr = (c2*((c*(c*drhorr+drho*crr)+c*drho*crr) - dpressrr)        &
                - (c*c*drho - dpress)*c2rr) / c2 / c2
        dv4ur = (c2*((c*(drho*cur)+c*drho*cur) - dpressur)                 &
                - (c*c*drho - dpress)*c2ur) / c2 / c2
        dv4vr = (c2*((c*(drho*cvr)+c*drho*cvr) - dpressvr)                 &
                - (c*c*drho - dpress)*c2vr) / c2 / c2
        dv4wr = (c2*((c*(drho*cwr)+c*drho*cwr) - dpresswr)                 &
                - (c*c*drho - dpress)*c2wr) / c2 / c2
        dv4pr = (c2*((c*(drho*cpr)+c*drho*cpr) - dpresspr)                 &
                - (c*c*drho - dpress)*c2pr) / c2 / c2

      r21 = u + c*xnorm
        r21rl = url + xnorm*crl
        r21ul = uul + xnorm*cul
        r21vl = xnorm*cvl
        r21wl = xnorm*cwl
        r21pl = xnorm*cpl

        r21rr = urr + xnorm*crr
        r21ur = uur + xnorm*cur
        r21vr = xnorm*cvr
        r21wr = xnorm*cwr
        r21pr = xnorm*cpr

      r31 = v + c*ynorm
        r31rl = vrl + ynorm*crl
        r31ul = ynorm*cul
        r31vl = vvl + ynorm*cvl
        r31wl = ynorm*cwl
        r31pl = ynorm*cpl

        r31rr = vrr + ynorm*crr
        r31ur = ynorm*cur
        r31vr = vvr + ynorm*cvr
        r31wr = ynorm*cwr
        r31pr = ynorm*cpr

      r41 = w + c*znorm
        r41rl = wrl + znorm*crl
        r41ul = znorm*cul
        r41vl = znorm*cvl
        r41wl = wwl + znorm*cwl
        r41pl = znorm*cpl

        r41rr = wrr + znorm*crr
        r41ur = znorm*cur
        r41vr = znorm*cvr
        r41wr = wwr + znorm*cwr
        r41pr = znorm*cpr

      r51 = H + c*(ubar+face_speed)
        r51rl = Hrl + c*ubarrl + (ubar+face_speed)*crl
        r51ul = Hul + c*ubarul + (ubar+face_speed)*cul
        r51vl = Hvl + c*ubarvl + (ubar+face_speed)*cvl
        r51wl = Hwl + c*ubarwl + (ubar+face_speed)*cwl
        r51pl = Hpl + (ubar+face_speed)*cpl

        r51rr = Hrr + c*ubarrr + (ubar+face_speed)*crr
        r51ur = Hur + c*ubarur + (ubar+face_speed)*cur
        r51vr = Hvr + c*ubarvr + (ubar+face_speed)*cvr
        r51wr = Hwr + c*ubarwr + (ubar+face_speed)*cwr
        r51pr = Hpr + (ubar+face_speed)*cpr

      r22 = u - c*xnorm
        r22rl = url - xnorm*crl
        r22ul = uul - xnorm*cul
        r22vl = - xnorm*cvl
        r22wl = - xnorm*cwl
        r22pl = - xnorm*cpl

        r22rr = urr - xnorm*crr
        r22ur = uur - xnorm*cur
        r22vr = - xnorm*cvr
        r22wr = - xnorm*cwr
        r22pr = - xnorm*cpr

      r32 = v - c*ynorm
        r32rl = vrl - ynorm*crl
        r32ul = - ynorm*cul
        r32vl = vvl - ynorm*cvl
        r32wl = - ynorm*cwl
        r32pl = - ynorm*cpl

        r32rr = vrr - ynorm*crr
        r32ur = - ynorm*cur
        r32vr = vvr - ynorm*cvr
        r32wr = - ynorm*cwr
        r32pr = - ynorm*cpr

      r42 = w - c*znorm
        r42rl = wrl - znorm*crl
        r42ul = - znorm*cul
        r42vl = - znorm*cvl
        r42wl = wwl - znorm*cwl
        r42pl = - znorm*cpl

        r42rr = wrr - znorm*crr
        r42ur = - znorm*cur
        r42vr = - znorm*cvr
        r42wr = wwr - znorm*cwr
        r42pr = - znorm*cpr

      r52 = H - c*(ubar+face_speed)
        r52rl = Hrl - c*ubarrl - (ubar+face_speed)*crl
        r52ul = Hul - c*ubarul - (ubar+face_speed)*cul
        r52vl = Hvl - c*ubarvl - (ubar+face_speed)*cvl
        r52wl = Hwl - c*ubarwl - (ubar+face_speed)*cwl
        r52pl = Hpl - (ubar+face_speed)*cpl

        r52rr = Hrr - c*ubarrr - (ubar+face_speed)*crr
        r52ur = Hur - c*ubarur - (ubar+face_speed)*cur
        r52vr = Hvr - c*ubarvr - (ubar+face_speed)*cvr
        r52wr = Hwr - c*ubarwr - (ubar+face_speed)*cwr
        r52pr = Hpr - (ubar+face_speed)*cpr

      r23 = du - dubar*xnorm
        r23rl = durl - xnorm*dubarrl
        r23ul = duul - xnorm*dubarul
        r23vl = - xnorm*dubarvl
        r23wl = - xnorm*dubarwl

        r23rr = durr - xnorm*dubarrr
        r23ur = duur - xnorm*dubarur
        r23vr = - xnorm*dubarvr
        r23wr = - xnorm*dubarwr

      r33 = dv - dubar*ynorm
        r33rl = dvrl - ynorm*dubarrl
        r33ul = - ynorm*dubarul
        r33vl = dvvl - ynorm*dubarvl
        r33wl = - ynorm*dubarwl

        r33rr = dvrr - ynorm*dubarrr
        r33ur = - ynorm*dubarur
        r33vr = dvvr - ynorm*dubarvr
        r33wr = - ynorm*dubarwr

      r43 = dw - dubar*znorm
        r43rl = dwrl - znorm*dubarrl
        r43ul = - znorm*dubarul
        r43vl = - znorm*dubarvl
        r43wl = dwwl - znorm*dubarwl

        r43rr = dwrr - znorm*dubarrr
        r43ur = - znorm*dubarur
        r43vr = - znorm*dubarvr
        r43wr = dwwr - znorm*dubarwr

      r53 = u*du + v*dv + w*dw - (ubar+face_speed)*dubar
        r53rl = u*durl+du*url + v*dvrl+dv*vrl + w*dwrl+dw*wrl              &
                - (ubar+face_speed)*dubarrl - dubar*ubarrl
        r53ul = u*duul+du*uul - (ubar+face_speed)*dubarul - dubar*ubarul
        r53vl = v*dvvl+dv*vvl - (ubar+face_speed)*dubarvl - dubar*ubarvl
        r53wl = w*dwwl+dw*wwl - (ubar+face_speed)*dubarwl - dubar*ubarwl

        r53rr = u*durr+du*urr + v*dvrr+dv*vrr + w*dwrr+dw*wrr              &
                - (ubar+face_speed)*dubarrr - dubar*ubarrr
        r53ur = u*duur+du*uur - (ubar+face_speed)*dubarur - dubar*ubarur
        r53vr = v*dvvr+dv*vvr - (ubar+face_speed)*dubarvr - dubar*ubarvr
        r53wr = w*dwwr+dw*wwr - (ubar+face_speed)*dubarwr - dubar*ubarwr

      r24 = u
        r24rl = url
        r24ul = uul
        r24rr = urr
        r24ur = uur

      r34 = v
        r34rl = vrl
        r34vl = vvl
        r34rr = vrr
        r34vr = vvr

      r44 = w
        r44rl = wrl
        r44wl = wwl
        r44rr = wrr
        r44wr = wwr

      r54 = 0.5_dp*q2
        r54rl = 0.5_dp*q2rl
        r54ul = 0.5_dp*q2ul
        r54vl = 0.5_dp*q2vl
        r54wl = 0.5_dp*q2wl

        r54rr = 0.5_dp*q2rr
        r54ur = 0.5_dp*q2ur
        r54vr = 0.5_dp*q2vr
        r54wr = 0.5_dp*q2wr

!           t1 = abseig1*dv1     + abseig2*dv2                                 &
!                                + abseig3*dv4

        t1rl = abseig1*dv1rl+dv1*eig1rl + abseig2*dv2rl+dv2*eig2rl         &
             + abseig3*dv4rl+dv4*eig3rl
        t1ul = abseig1*dv1ul+dv1*eig1ul + abseig2*dv2ul+dv2*eig2ul         &
             + abseig3*dv4ul+dv4*eig3ul
        t1vl = abseig1*dv1vl+dv1*eig1vl + abseig2*dv2vl+dv2*eig2vl         &
             + abseig3*dv4vl+dv4*eig3vl
        t1wl = abseig1*dv1wl+dv1*eig1wl + abseig2*dv2wl+dv2*eig2wl         &
             + abseig3*dv4wl+dv4*eig3wl
        t1pl = abseig1*dv1pl+dv1*eig1pl + abseig2*dv2pl+dv2*eig2pl         &
             + abseig3*dv4pl+dv4*eig3pl

        t1rr = abseig1*dv1rr+dv1*eig1rr + abseig2*dv2rr+dv2*eig2rr         &
             + abseig3*dv4rr+dv4*eig3rr
        t1ur = abseig1*dv1ur+dv1*eig1ur + abseig2*dv2ur+dv2*eig2ur         &
             + abseig3*dv4ur+dv4*eig3ur
        t1vr = abseig1*dv1vr+dv1*eig1vr + abseig2*dv2vr+dv2*eig2vr         &
             + abseig3*dv4vr+dv4*eig3vr
        t1wr = abseig1*dv1wr+dv1*eig1wr + abseig2*dv2wr+dv2*eig2wr         &
             + abseig3*dv4wr+dv4*eig3wr
        t1pr = abseig1*dv1pr+dv1*eig1pr + abseig2*dv2pr+dv2*eig2pr         &
             + abseig3*dv4pr+dv4*eig3pr

!           t2 = abseig1*r21*dv1 + abseig2*r22*dv2                             &
!              + abseig3*r23*dv3 + abseig3*r24*dv4

        t2rl = abseig1*(r21*dv1rl+dv1*r21rl)+r21*dv1*eig1rl                &
             + abseig2*(r22*dv2rl+dv2*r22rl)+r22*dv2*eig2rl                &
             + abseig3*(r23*dv3rl+dv3*r23rl)+r23*dv3*eig3rl                &
             + abseig3*(r24*dv4rl+dv4*r24rl)+r24*dv4*eig3rl

        t2ul = abseig1*(r21*dv1ul+dv1*r21ul)+r21*dv1*eig1ul                &
             + abseig2*(r22*dv2ul+dv2*r22ul)+r22*dv2*eig2ul                &
             + abseig3*(dv3*r23ul)+r23*dv3*eig3ul                          &
             + abseig3*(r24*dv4ul+dv4*r24ul)+r24*dv4*eig3ul

        t2vl = abseig1*(r21*dv1vl+dv1*r21vl)+r21*dv1*eig1vl                &
             + abseig2*(r22*dv2vl+dv2*r22vl)+r22*dv2*eig2vl                &
             + abseig3*(dv3*r23vl)+r23*dv3*eig3vl                          &
             + abseig3*(r24*dv4vl)+r24*dv4*eig3vl

        t2wl = abseig1*(r21*dv1wl+dv1*r21wl)+r21*dv1*eig1wl                &
             + abseig2*(r22*dv2wl+dv2*r22wl)+r22*dv2*eig2wl                &
             + abseig3*(dv3*r23wl)+r23*dv3*eig3wl                          &
             + abseig3*(r24*dv4wl)+r24*dv4*eig3wl

        t2pl = abseig1*(r21*dv1pl+dv1*r21pl)+r21*dv1*eig1pl                &
             + abseig2*(r22*dv2pl+dv2*r22pl)+r22*dv2*eig2pl                &
             +r23*dv3*eig3pl + abseig3*(r24*dv4pl)+r24*dv4*eig3pl

        t2rr = abseig1*(r21*dv1rr+dv1*r21rr)+r21*dv1*eig1rr                &
             + abseig2*(r22*dv2rr+dv2*r22rr)+r22*dv2*eig2rr                &
             + abseig3*(r23*dv3rr+dv3*r23rr)+r23*dv3*eig3rr                &
             + abseig3*(r24*dv4rr+dv4*r24rr)+r24*dv4*eig3rr

        t2ur = abseig1*(r21*dv1ur+dv1*r21ur)+r21*dv1*eig1ur                &
             + abseig2*(r22*dv2ur+dv2*r22ur)+r22*dv2*eig2ur                &
             + abseig3*(dv3*r23ur)+r23*dv3*eig3ur                          &
             + abseig3*(r24*dv4ur+dv4*r24ur)+r24*dv4*eig3ur

        t2vr = abseig1*(r21*dv1vr+dv1*r21vr)+r21*dv1*eig1vr                &
             + abseig2*(r22*dv2vr+dv2*r22vr)+r22*dv2*eig2vr                &
             + abseig3*(dv3*r23vr)+r23*dv3*eig3vr                          &
             + abseig3*(r24*dv4vr)+r24*dv4*eig3vr

        t2wr = abseig1*(r21*dv1wr+dv1*r21wr)+r21*dv1*eig1wr                &
             + abseig2*(r22*dv2wr+dv2*r22wr)+r22*dv2*eig2wr                &
             + abseig3*(dv3*r23wr)+r23*dv3*eig3wr                          &
             + abseig3*(r24*dv4wr)+r24*dv4*eig3wr

        t2pr = abseig1*(r21*dv1pr+dv1*r21pr)+r21*dv1*eig1pr                &
             + abseig2*(r22*dv2pr+dv2*r22pr)+r22*dv2*eig2pr                &
             +r23*dv3*eig3pr + abseig3*(r24*dv4pr)+r24*dv4*eig3pr

!           t3 = abseig1*r31*dv1 + abseig2*r32*dv2                             &
!              + abseig3*r33*dv3 + abseig3*r34*dv4

        t3rl = abseig1*(r31*dv1rl+dv1*r31rl)+r31*dv1*eig1rl                &
             + abseig2*(r32*dv2rl+dv2*r32rl)+r32*dv2*eig2rl                &
             + abseig3*(r33*dv3rl+dv3*r33rl)+r33*dv3*eig3rl                &
             + abseig3*(r34*dv4rl+dv4*r34rl)+r34*dv4*eig3rl

        t3ul = abseig1*(r31*dv1ul+dv1*r31ul)+r31*dv1*eig1ul                &
             + abseig2*(r32*dv2ul+dv2*r32ul)+r32*dv2*eig2ul                &
             + abseig3*(dv3*r33ul)+r33*dv3*eig3ul                          &
             + abseig3*(r34*dv4ul)+r34*dv4*eig3ul

        t3vl = abseig1*(r31*dv1vl+dv1*r31vl)+r31*dv1*eig1vl                &
             + abseig2*(r32*dv2vl+dv2*r32vl)+r32*dv2*eig2vl                &
             + abseig3*(dv3*r33vl)+r33*dv3*eig3vl                          &
             + abseig3*(r34*dv4vl+dv4*r34vl)+r34*dv4*eig3vl

        t3wl = abseig1*(r31*dv1wl+dv1*r31wl)+r31*dv1*eig1wl                &
             + abseig2*(r32*dv2wl+dv2*r32wl)+r32*dv2*eig2wl                &
             + abseig3*(dv3*r33wl)+r33*dv3*eig3wl                          &
             + abseig3*(r34*dv4wl)+r34*dv4*eig3wl

        t3pl = abseig1*(r31*dv1pl+dv1*r31pl)+r31*dv1*eig1pl                &
             + abseig2*(r32*dv2pl+dv2*r32pl)+r32*dv2*eig2pl                &
             +r33*dv3*eig3pl + abseig3*(r34*dv4pl)+r34*dv4*eig3pl

        t3rr = abseig1*(r31*dv1rr+dv1*r31rr)+r31*dv1*eig1rr                &
             + abseig2*(r32*dv2rr+dv2*r32rr)+r32*dv2*eig2rr                &
             + abseig3*(r33*dv3rr+dv3*r33rr)+r33*dv3*eig3rr                &
             + abseig3*(r34*dv4rr+dv4*r34rr)+r34*dv4*eig3rr

        t3ur = abseig1*(r31*dv1ur+dv1*r31ur)+r31*dv1*eig1ur                &
             + abseig2*(r32*dv2ur+dv2*r32ur)+r32*dv2*eig2ur                &
             + abseig3*(dv3*r33ur)+r33*dv3*eig3ur                          &
             + abseig3*(r34*dv4ur)+r34*dv4*eig3ur

        t3vr = abseig1*(r31*dv1vr+dv1*r31vr)+r31*dv1*eig1vr                &
             + abseig2*(r32*dv2vr+dv2*r32vr)+r32*dv2*eig2vr                &
             + abseig3*(dv3*r33vr)+r33*dv3*eig3vr                          &
             + abseig3*(r34*dv4vr+dv4*r34vr)+r34*dv4*eig3vr

        t3wr = abseig1*(r31*dv1wr+dv1*r31wr)+r31*dv1*eig1wr                &
             + abseig2*(r32*dv2wr+dv2*r32wr)+r32*dv2*eig2wr                &
             + abseig3*(dv3*r33wr)+r33*dv3*eig3wr                          &
             + abseig3*(r34*dv4wr)+r34*dv4*eig3wr

        t3pr = abseig1*(r31*dv1pr+dv1*r31pr)+r31*dv1*eig1pr                &
             + abseig2*(r32*dv2pr+dv2*r32pr)+r32*dv2*eig2pr                &
             +r33*dv3*eig3pr + abseig3*(r34*dv4pr)+r34*dv4*eig3pr

!           t4 = abseig1*r41*dv1 + abseig2*r42*dv2                             &
!              + abseig3*r43*dv3 + abseig3*r44*dv4

        t4rl = abseig1*(r41*dv1rl+dv1*r41rl)+r41*dv1*eig1rl                &
             + abseig2*(r42*dv2rl+dv2*r42rl)+r42*dv2*eig2rl                &
             + abseig3*(r43*dv3rl+dv3*r43rl)+r43*dv3*eig3rl                &
             + abseig3*(r44*dv4rl+dv4*r44rl)+r44*dv4*eig3rl

        t4ul = abseig1*(r41*dv1ul+dv1*r41ul)+r41*dv1*eig1ul                &
             + abseig2*(r42*dv2ul+dv2*r42ul)+r42*dv2*eig2ul                &
             + abseig3*(dv3*r43ul)+r43*dv3*eig3ul                          &
             + abseig3*(r44*dv4ul)+r44*dv4*eig3ul

        t4vl = abseig1*(r41*dv1vl+dv1*r41vl)+r41*dv1*eig1vl                &
             + abseig2*(r42*dv2vl+dv2*r42vl)+r42*dv2*eig2vl                &
             + abseig3*(dv3*r43vl)+r43*dv3*eig3vl                          &
             + abseig3*(r44*dv4vl)+r44*dv4*eig3vl

        t4wl = abseig1*(r41*dv1wl+dv1*r41wl)+r41*dv1*eig1wl                &
             + abseig2*(r42*dv2wl+dv2*r42wl)+r42*dv2*eig2wl                &
             + abseig3*(dv3*r43wl)+r43*dv3*eig3wl                          &
             + abseig3*(r44*dv4wl+dv4*r44wl)+r44*dv4*eig3wl

        t4pl = abseig1*(r41*dv1pl+dv1*r41pl)+r41*dv1*eig1pl                &
             + abseig2*(r42*dv2pl+dv2*r42pl)+r42*dv2*eig2pl                &
             +r43*dv3*eig3pl + abseig3*(r44*dv4pl)+r44*dv4*eig3pl

        t4rr = abseig1*(r41*dv1rr+dv1*r41rr)+r41*dv1*eig1rr                &
             + abseig2*(r42*dv2rr+dv2*r42rr)+r42*dv2*eig2rr                &
             + abseig3*(r43*dv3rr+dv3*r43rr)+r43*dv3*eig3rr                &
             + abseig3*(r44*dv4rr+dv4*r44rr)+r44*dv4*eig3rr

        t4ur = abseig1*(r41*dv1ur+dv1*r41ur)+r41*dv1*eig1ur                &
             + abseig2*(r42*dv2ur+dv2*r42ur)+r42*dv2*eig2ur                &
             + abseig3*(dv3*r43ur)+r43*dv3*eig3ur                          &
             + abseig3*(r44*dv4ur)+r44*dv4*eig3ur

        t4vr = abseig1*(r41*dv1vr+dv1*r41vr)+r41*dv1*eig1vr                &
             + abseig2*(r42*dv2vr+dv2*r42vr)+r42*dv2*eig2vr                &
             + abseig3*(dv3*r43vr)+r43*dv3*eig3vr                          &
             + abseig3*(r44*dv4vr)+r44*dv4*eig3vr

        t4wr = abseig1*(r41*dv1wr+dv1*r41wr)+r41*dv1*eig1wr                &
             + abseig2*(r42*dv2wr+dv2*r42wr)+r42*dv2*eig2wr                &
             + abseig3*(dv3*r43wr)+r43*dv3*eig3wr                          &
             + abseig3*(r44*dv4wr+dv4*r44wr)+r44*dv4*eig3wr

        t4pr = abseig1*(r41*dv1pr+dv1*r41pr)+r41*dv1*eig1pr                &
             + abseig2*(r42*dv2pr+dv2*r42pr)+r42*dv2*eig2pr                &
             +r43*dv3*eig3pr + abseig3*(r44*dv4pr)+r44*dv4*eig3pr

!           t5 = abseig1*r51*dv1 + abseig2*r52*dv2                             &
!              + abseig3*r53*dv3 + abseig3*r54*dv4

        t5rl = abseig1*(r51*dv1rl+dv1*r51rl)+r51*dv1*eig1rl                &
             + abseig2*(r52*dv2rl+dv2*r52rl)+r52*dv2*eig2rl                &
             + abseig3*(r53*dv3rl+dv3*r53rl)+r53*dv3*eig3rl                &
             + abseig3*(r54*dv4rl+dv4*r54rl)+r54*dv4*eig3rl

        t5ul = abseig1*(r51*dv1ul+dv1*r51ul)+r51*dv1*eig1ul                &
             + abseig2*(r52*dv2ul+dv2*r52ul)+r52*dv2*eig2ul                &
             + abseig3*(dv3*r53ul)+r53*dv3*eig3ul                          &
             + abseig3*(r54*dv4ul+dv4*r54ul)+r54*dv4*eig3ul

        t5vl = abseig1*(r51*dv1vl+dv1*r51vl)+r51*dv1*eig1vl                &
             + abseig2*(r52*dv2vl+dv2*r52vl)+r52*dv2*eig2vl                &
             + abseig3*(dv3*r53vl)+r53*dv3*eig3vl                          &
             + abseig3*(r54*dv4vl+dv4*r54vl)+r54*dv4*eig3vl

        t5wl = abseig1*(r51*dv1wl+dv1*r51wl)+r51*dv1*eig1wl                &
             + abseig2*(r52*dv2wl+dv2*r52wl)+r52*dv2*eig2wl                &
             + abseig3*(dv3*r53wl)+r53*dv3*eig3wl                          &
             + abseig3*(r54*dv4wl+dv4*r54wl)+r54*dv4*eig3wl

        t5pl = abseig1*(r51*dv1pl+dv1*r51pl)+r51*dv1*eig1pl                &
             + abseig2*(r52*dv2pl+dv2*r52pl)+r52*dv2*eig2pl                &
             +r53*dv3*eig3pl + abseig3*(r54*dv4pl)+r54*dv4*eig3pl
        t5rr = abseig1*(r51*dv1rr+dv1*r51rr)+r51*dv1*eig1rr                &
             + abseig2*(r52*dv2rr+dv2*r52rr)+r52*dv2*eig2rr                &
             + abseig3*(r53*dv3rr+dv3*r53rr)+r53*dv3*eig3rr                &
             + abseig3*(r54*dv4rr+dv4*r54rr)+r54*dv4*eig3rr

        t5ur = abseig1*(r51*dv1ur+dv1*r51ur)+r51*dv1*eig1ur                &
             + abseig2*(r52*dv2ur+dv2*r52ur)+r52*dv2*eig2ur                &
             + abseig3*(dv3*r53ur)+r53*dv3*eig3ur                          &
             + abseig3*(r54*dv4ur+dv4*r54ur)+r54*dv4*eig3ur

        t5vr = abseig1*(r51*dv1vr+dv1*r51vr)+r51*dv1*eig1vr                &
             + abseig2*(r52*dv2vr+dv2*r52vr)+r52*dv2*eig2vr                &
             + abseig3*(dv3*r53vr)+r53*dv3*eig3vr                          &
             + abseig3*(r54*dv4vr+dv4*r54vr)+r54*dv4*eig3vr

        t5wr = abseig1*(r51*dv1wr+dv1*r51wr)+r51*dv1*eig1wr                &
             + abseig2*(r52*dv2wr+dv2*r52wr)+r52*dv2*eig2wr                &
             + abseig3*(dv3*r53wr)+r53*dv3*eig3wr                          &
             + abseig3*(r54*dv4wr+dv4*r54wr)+r54*dv4*eig3wr

        t5pr = abseig1*(r51*dv1pr+dv1*r51pr)+r51*dv1*eig1pr                &
             + abseig2*(r52*dv2pr+dv2*r52pr)+r52*dv2*eig2pr                &
             +r53*dv3*eig3pr + abseig3*(r54*dv4pr)+r54*dv4*eig3pr

! Compute flux using variables from left side of face

!           fluxl1 = area*rhol*ubarl

        flux1rl = rhol*ubarlrl + ubarl*rholrl
        flux1ul = rhol*ubarlul
        flux1vl = rhol*ubarlvl
        flux1wl = rhol*ubarlwl

!           fluxl2 = area*(rhol*ul*ubarl + xnorm*pressl)

        flux2rl = rhol*(ul*ubarlrl+ubarl*ulrl) +                          &
                  ul*ubarl*rholrl + xnorm*presslrl
        flux2ul = rhol*(ul*ubarlul+ubarl*ulul) + xnorm*presslul
        flux2vl = rhol*(ul*ubarlvl) + xnorm*presslvl
        flux2wl = rhol*(ul*ubarlwl) + xnorm*presslwl
        flux2pl = xnorm*presslpl

!           fluxl3 = area*(rhol*vl*ubarl + ynorm*pressl)

        flux3rl = rhol*(vl*ubarlrl+ubarl*vlrl) +                          &
                  vl*ubarl*rholrl + ynorm*presslrl
        flux3ul = rhol*(vl*ubarlul) + ynorm*presslul
        flux3vl = rhol*(vl*ubarlvl+ubarl*vlvl) + ynorm*presslvl
        flux3wl = rhol*(vl*ubarlwl) + ynorm*presslwl
        flux3pl = ynorm*presslpl

!           fluxl4 = area*(rhol*wl*ubarl + znorm*pressl)

        flux4rl = rhol*(wl*ubarlrl+ubarl*wlrl) +                          &
                  wl*ubarl*rholrl + znorm*presslrl
        flux4ul = rhol*(wl*ubarlul) + znorm*presslul
        flux4vl = rhol*(wl*ubarlvl) + znorm*presslvl
        flux4wl = rhol*(wl*ubarlwl+ubarl*wlwl) + znorm*presslwl
        flux4pl = znorm*presslpl

!           fluxl5 = area*(enrgyl + pressl)*ubarl + area*face_speed*pressl

        flux5rl = (enrgyl + pressl)*ubarlrl +                             &
                  ubarl*(presslrl) + face_speed*presslrl
        flux5ul = (enrgyl + pressl)*ubarlul +                             &
                  ubarl*(presslul) + face_speed*presslul
        flux5vl = (enrgyl + pressl)*ubarlvl +                             &
                  ubarl*(presslvl) + face_speed*presslvl
        flux5wl = (enrgyl + pressl)*ubarlwl +                             &
                  ubarl*(presslwl) + face_speed*presslwl
        flux5pl = ubarl*(enrgylpl+presslpl) + face_speed*presslpl

! Now the right side

!           fluxr1 = area*rhor*ubarr

        flux1rr = rhor*ubarrrr + ubarr*rhorrr
        flux1ur = rhor*ubarrur
        flux1vr = rhor*ubarrvr
        flux1wr = rhor*ubarrwr

!           fluxr2 = area*(rhor*ur*ubarr + xnorm*pressr)

        flux2rr = rhor*(ur*ubarrrr+ubarr*urrr) +                          &
                  ur*ubarr*rhorrr + xnorm*pressrrr
        flux2ur = rhor*(ur*ubarrur+ubarr*urur) + xnorm*pressrur
        flux2vr = rhor*(ur*ubarrvr) + xnorm*pressrvr
        flux2wr = rhor*(ur*ubarrwr) + xnorm*pressrwr
        flux2pr = xnorm*pressrpr

!           fluxr3 = area*(rhor*vr*ubarr + ynorm*pressr)

        flux3rr = rhor*(vr*ubarrrr+ubarr*vrrr) +                          &
                  vr*ubarr*rhorrr + ynorm*pressrrr
        flux3ur = rhor*(vr*ubarrur) + ynorm*pressrur
        flux3vr = rhor*(vr*ubarrvr+ubarr*vrvr) + ynorm*pressrvr
        flux3wr = rhor*(vr*ubarrwr) + ynorm*pressrwr
        flux3pr = ynorm*pressrpr

!           fluxr4 = area*(rhor*wr*ubarr + znorm*pressr)

        flux4rr = rhor*(wr*ubarrrr+ubarr*wrrr) +                          &
                  wr*ubarr*rhorrr + znorm*pressrrr
        flux4ur = rhor*(wr*ubarrur) + znorm*pressrur
        flux4vr = rhor*(wr*ubarrvr) + znorm*pressrvr
        flux4wr = rhor*(wr*ubarrwr+ubarr*wrwr) + znorm*pressrwr
        flux4pr = znorm*pressrpr

!           fluxr5 = area*(enrgyr + pressr)*ubarr + area*face_speed*pressr

        flux5rr = (enrgyr + pressr)*ubarrrr +                             &
                  ubarr*(pressrrr) + face_speed*pressrrr
        flux5ur = (enrgyr + pressr)*ubarrur +                             &
                  ubarr*(pressrur) + face_speed*pressrur
        flux5vr = (enrgyr + pressr)*ubarrvr +                             &
                  ubarr*(pressrvr) + face_speed*pressrvr
        flux5wr = (enrgyr + pressr)*ubarrwr +                             &
                  ubarr*(pressrwr) + face_speed*pressrwr
        flux5pr = ubarr*(enrgyrpr+pressrpr) + face_speed*pressrpr

!         flux1 = 0.5_dp*(fluxl1 + fluxr1 - area*t1)
!         flux2 = 0.5_dp*(fluxl2 + fluxr2 - area*t2)
!         flux3 = 0.5_dp*(fluxl3 + fluxr3 - area*t3)
!         flux4 = 0.5_dp*(fluxl4 + fluxr4 - area*t4)
!         flux5 = 0.5_dp*(fluxl5 + fluxr5 - area*t5)

      dfroe(1,1,1) = 0.5_dp*area*(flux1rl - t1rl)
      dfroe(1,2,1) = 0.5_dp*area*(flux1ul - t1ul)
      dfroe(1,3,1) = 0.5_dp*area*(flux1vl - t1vl)
      dfroe(1,4,1) = 0.5_dp*area*(flux1wl - t1wl)
      dfroe(1,5,1) = 0.5_dp*area*(- t1pl         )

      dfroe(1,1,2) = 0.5_dp*area*(flux1rr - t1rr)
      dfroe(1,2,2) = 0.5_dp*area*(flux1ur - t1ur)
      dfroe(1,3,2) = 0.5_dp*area*(flux1vr - t1vr)
      dfroe(1,4,2) = 0.5_dp*area*(flux1wr - t1wr)
      dfroe(1,5,2) = 0.5_dp*area*(- t1pr         )

      dfroe(2,1,1) = 0.5_dp*area*(flux2rl - t2rl)
      dfroe(2,2,1) = 0.5_dp*area*(flux2ul - t2ul)
      dfroe(2,3,1) = 0.5_dp*area*(flux2vl - t2vl)
      dfroe(2,4,1) = 0.5_dp*area*(flux2wl - t2wl)
      dfroe(2,5,1) = 0.5_dp*area*(flux2pl - t2pl)

      dfroe(2,1,2) = 0.5_dp*area*(flux2rr - t2rr)
      dfroe(2,2,2) = 0.5_dp*area*(flux2ur - t2ur)
      dfroe(2,3,2) = 0.5_dp*area*(flux2vr - t2vr)
      dfroe(2,4,2) = 0.5_dp*area*(flux2wr - t2wr)
      dfroe(2,5,2) = 0.5_dp*area*(flux2pr - t2pr)

      dfroe(3,1,1) = 0.5_dp*area*(flux3rl - t3rl)
      dfroe(3,2,1) = 0.5_dp*area*(flux3ul - t3ul)
      dfroe(3,3,1) = 0.5_dp*area*(flux3vl - t3vl)
      dfroe(3,4,1) = 0.5_dp*area*(flux3wl - t3wl)
      dfroe(3,5,1) = 0.5_dp*area*(flux3pl - t3pl)

      dfroe(3,1,2) = 0.5_dp*area*(flux3rr - t3rr)
      dfroe(3,2,2) = 0.5_dp*area*(flux3ur - t3ur)
      dfroe(3,3,2) = 0.5_dp*area*(flux3vr - t3vr)
      dfroe(3,4,2) = 0.5_dp*area*(flux3wr - t3wr)
      dfroe(3,5,2) = 0.5_dp*area*(flux3pr - t3pr)

      dfroe(4,1,1) = 0.5_dp*area*(flux4rl - t4rl)
      dfroe(4,2,1) = 0.5_dp*area*(flux4ul - t4ul)
      dfroe(4,3,1) = 0.5_dp*area*(flux4vl - t4vl)
      dfroe(4,4,1) = 0.5_dp*area*(flux4wl - t4wl)
      dfroe(4,5,1) = 0.5_dp*area*(flux4pl - t4pl)

      dfroe(4,1,2) = 0.5_dp*area*(flux4rr - t4rr)
      dfroe(4,2,2) = 0.5_dp*area*(flux4ur - t4ur)
      dfroe(4,3,2) = 0.5_dp*area*(flux4vr - t4vr)
      dfroe(4,4,2) = 0.5_dp*area*(flux4wr - t4wr)
      dfroe(4,5,2) = 0.5_dp*area*(flux4pr - t4pr)

      dfroe(5,1,1) = 0.5_dp*area*(flux5rl - t5rl)
      dfroe(5,2,1) = 0.5_dp*area*(flux5ul - t5ul)
      dfroe(5,3,1) = 0.5_dp*area*(flux5vl - t5vl)
      dfroe(5,4,1) = 0.5_dp*area*(flux5wl - t5wl)
      dfroe(5,5,1) = 0.5_dp*area*(flux5pl - t5pl)

      dfroe(5,1,2) = 0.5_dp*area*(flux5rr - t5rr)
      dfroe(5,2,2) = 0.5_dp*area*(flux5ur - t5ur)
      dfroe(5,3,2) = 0.5_dp*area*(flux5vr - t5vr)
      dfroe(5,4,2) = 0.5_dp*area*(flux5wr - t5wr)
      dfroe(5,5,2) = 0.5_dp*area*(flux5pr - t5pr)

  end function dfroe

!================================= FLUX_AUFS =================================80
!
! Artificially Upwind Flux-vector Splitting (AUFS) flux function per M.Sun
! and <PERSON><PERSON>, "An Artificially Upwind Flux Vector Splitting Scheme for
! the Euler Equations", Journal of Computational Physics, Volume 189, 2003,
! pages 305-329. (Available online at http://www.sciencedirect.com/.)
!
! Note that this function uses primitive variables
!
!=============================================================================80

  pure function flux_aufs(xnorm, ynorm, znorm, area, ql, qr)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_4th, my_half, my_1

    use fluid,           only : gm1

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp), dimension(5), intent(in) :: ql, qr
    real(dp), dimension(5)             :: flux_aufs

    real(dp) :: rhol, ul, vl, wl, q2l, pressl, energyl, Hl, ubarl, cl
    real(dp) :: rhor, ur, vr, wr, q2r, pressr, energyr, Hr, ubarr, cr
    real(dp) :: cbar
    real(dp) :: ubars, cs
    real(dp) :: S1, S2, M
    real(dp) :: dU1, dU2, dU3, dU4, dU5
    real(dp) :: Fc1, Fc2, Fc3, Fc4, Fc5
    real(dp) :: Fu1, Fu2, Fu3, Fu4, Fu5

  continue

!   Get left and right state primitive variables

    rhol   = ql(1)
    ul     = ql(2)
    vl     = ql(3)
    wl     = ql(4)
    pressl = ql(5)

    rhor   = qr(1)
    ur     = qr(2)
    vr     = qr(3)
    wr     = qr(4)
    pressr = qr(5)

!   Compute the remaining needed left and right state variables:

    q2l     = ul*ul + vl*vl + wl*wl
    energyl = pressl/gm1 + my_half*rhol*q2l
    Hl      = (energyl + pressl)/rhol
    ubarl   = xnorm*ul + ynorm*vl + znorm*wl
    cl      = sqrt(gm1*(Hl-my_half*q2l))

    q2r     = ur*ur + vr*vr + wr*wr
    energyr = pressr/gm1 + my_half*rhor*q2r
    Hr      = (energyr + pressr)/rhor
    ubarr   = xnorm*ur + ynorm*vr + znorm*wr
    cr      = sqrt(gm1*(Hr-my_half*q2r))

!   Acoustic wave speed average (Eqn. 35):

    cbar = my_half*( cl + cr )

!   Artificial viscosity (Eqn.49):

    dU1 = my_half*(pressl-pressr)/cbar
    dU2 = my_half*(pressl*ul-pressr*ur)/cbar
    dU3 = my_half*(pressl*vl-pressr*vr)/cbar
    dU4 = my_half*(pressl*wl-pressr*wr)/cbar
    dU5 = my_half*(cbar*(pressl-pressr)/gm1                                    &
                   + my_half*(pressl*q2l-pressr*q2r)/cbar)

!   First splitting wave speed (Eqn. 36):

    S1 = my_half*( ubarl + ubarr )

!   Speeds between two isentropic waves (Eqn. 17):

    cs    = cbar + my_4th*gm1*(ubarl-ubarr)
    ubars = S1 + (cl-cr)/gm1

!   Second splitting wave speed (Eqn. 37):

    if ( S1 > my_0 ) then
      S2 = min( my_0, ubarl-cl, ubars-cs )
    else
      S2 = max( my_0, ubarr+cr, ubars+cs )
    end if

!   Splitting ratio (Eqn. 10):

    M = S1/(S1-S2)

!   The central difference plus dissipation part of flux:

    Fc1 = (my_1-M)*dU1
    Fc2 = (my_1-M)*(my_half*(pressl+pressr)*xnorm+dU2)
    Fc3 = (my_1-M)*(my_half*(pressl+pressr)*ynorm+dU3)
    Fc4 = (my_1-M)*(my_half*(pressl+pressr)*znorm+dU4)
    Fc5 = (my_1-M)*(my_half*(pressl*ubarl+pressr*ubarr)+dU5)

!   Load the proper upwind portion of flux (Eqn. 48)
!   based on wave speeds (Eqn. 24):

    if ( S1 > my_0 ) then ! use left state

      Fu1 = M*rhol*(ubarl-S2)
      Fu2 = M*(rhol*ul*(ubarl-S2)+pressl*xnorm)
      Fu3 = M*(rhol*vl*(ubarl-S2)+pressl*ynorm)
      Fu4 = M*(rhol*wl*(ubarl-S2)+pressl*znorm)
      Fu5 = M*(energyl*(ubarl-S2)+pressl*ubarl)

    else  ! use right state

      Fu1 = M*rhor*(ubarr-S2)
      Fu2 = M*(rhor*ur*(ubarr-S2)+pressr*xnorm)
      Fu3 = M*(rhor*vr*(ubarr-S2)+pressr*ynorm)
      Fu4 = M*(rhor*wr*(ubarr-S2)+pressr*znorm)
      Fu5 = M*(energyr*(ubarr-S2)+pressr*ubarr)

    end if

!   Compute the contribution to the flux balance

    flux_aufs(1) = area*(Fc1 + Fu1)
    flux_aufs(2) = area*(Fc2 + Fu2)
    flux_aufs(3) = area*(Fc3 + Fu3)
    flux_aufs(4) = area*(Fc4 + Fu4)
    flux_aufs(5) = area*(Fc5 + Fu5)

  end function flux_aufs

module invert_lapack

  use kinddefs,            only : dp
  use av2p0_lapack_util,   only : xerbla, claswp, icamax,      &
                                  cgemm, cgemv, cgeru, ctrtri, &
                                  ilaenv,                      &
                                  cscal, cswap, ctrsm

  implicit none

  private

  public :: invert
  interface invert
    module procedure invert_real
    module procedure invert_complex
  end interface

contains

!===================================== INVERT_REAL ===========================80
!
! Invert a matrix.
!
!=============================================================================80
  subroutine invert_real(neim, n, a, work_size)

    integer,                        intent(in)    :: neim, n, work_size
    real(dp), dimension(neim,neim), intent(inout) :: a

    complex(dp), dimension(neim,neim) :: amat

    integer :: i,icheck,info,j

    integer, dimension(neim) :: ipiv
    complex(dp), dimension(work_size) :: work

  continue

    do i=1,n
      do j=1,n
        amat(i,j) = cmplx( a(i,j), 0._dp, dp )
      enddo
    enddo

    icheck = 0
    if (icheck == 1) then
      do i = 1,n
        write (6,*) " inverting a ->row", i, (a(i,j), j = 1,n)
      end do
    end if
    !  =====================
    !  Arguments to CGETRF
    !  =====================
    !
    !  M       (input) INTEGER
    !          The number of rows of the matrix A.  M >= 0.
    !
    !  N       (input) INTEGER
    !          The number of columns of the matrix A.  N >= 0.
    !
    !  A       (input/output) COMPLEX array, dimension (LDA,N)
    !          On entry, the M-by-N matrix to be factored.
    !          On exit, the factors L and U from the factorization
    !          A = P*L*U; the unit diagonal elements of L are not stored.
    !
    !  LDA     (input) INTEGER
    !          The leading dimension of the array A.  LDA >= max(1,M).
    !
    !  IPIV    (output) INTEGER array, dimension (min(M,N))
    !          The pivot indices; for 1 <= i <= min(M,N), row i of the
    !          matrix was interchanged with row IPIV(i).
    !
    !  INFO    (output) INTEGER
    !          = 0:  successful exit
    !          < 0:  if INFO = -i, the i-th argument had an illegal value
    !          > 0:  if INFO = i, U(i,i) is exactly zero. The factorization
    !                has been completed, but the factor U is exactly
    !                singular, and division by zero will occur if it is used
    !                to solve a system of equations.
    !
    !  =====================================================================
    info = 0
    call CGETRF(n,n,amat,neim,IPIV,INFO)
    if (info > 0) then
      write (6,*) " abnormal exit(CGETRF) singular matrix...stopping"
      stop
    end if
    if (info < 0) then
      write (6,*) " abnormal exit(CGETRF) illegal value at i=", info
      stop
    end if
    !  ===================
    !  Arguments to CGETRI
    !  ===================
    !
    !  N       (input) INTEGER
    !          The order of the matrix A.  N >= 0.
    !
    !  A       (input/output) COMPLEX array, dimension (LDA,N)
    !          On entry, the factors L and U from the factorization
    !          A = P*L*U as computed by CGETRF.
    !          On exit, if INFO = 0, the inverse of the original matrix A.
    !
    !  LDA     (input) INTEGER
    !          The leading dimension of the array A.  LDA >= max(1,N).
    !
    !  IPIV    (input) INTEGER array, dimension (N)
    !          The pivot indices from CGETRF; for 1<=i<=N, row i of the
    !          matrix was interchanged with row IPIV(i).
    !
    !  WORK    (workspace/output) COMPLEX array, dimension (LWORK)
    !          On exit, if INFO=0, then WORK(1) returns the optimal LWORK.
    !
    !  LWORK   (input) INTEGER
    !          The dimension of the array WORK.  LWORK >= max(1,N).
    !          For optimal performance LWORK >= N*NB, where NB is
    !          the optimal blocksize returned by ILAENV.
    !
    !  INFO    (output) INTEGER
    !          = 0:  successful exit
    !          < 0:  if INFO = -i, the i-th argument had an illegal value
    !          > 0:  if INFO = i, U(i,i) is exactly zero; the matrix is
    !                singular and its inverse could not be computed.
    !
    !  =====================================================================
    info = 0
    call CGETRI(N,amat,neim,IPIV,WORK,work_size,INFO)
    if (info > 0) then
      write (6,*) " abnormal exit(CGETRI) singular matrix...stopping"
      stop
    end if
    if (info < 0) then
      write (6,*) " abnormal exit(CGETRI) illegal value at i=", info
      stop
    end if

    do i=1,n
      do j=1,n
        a(i,j) = real( amat(i,j), dp )
      enddo
    enddo
    if (icheck /= 1) return
    do i = 1,n
      write (6,*) "  A(inv) ->row", i, (a(i,j), j = 1,n)
    end do

  end subroutine invert_real

!===================================== INVERT_COMPLEX ========================80
!
! Invert a complex matrix.
!
!=============================================================================80
  subroutine invert_complex(neim, n, a, work_size)

    integer,                           intent(in)    :: neim, n, work_size
    complex(dp), dimension(neim,neim), intent(inout) :: a

    complex(dp), dimension(neim,neim) :: amat

    integer :: i,icheck,info,j

    integer, dimension(neim) :: ipiv
    complex(dp), dimension(work_size) :: work

  continue

    do i=1,n
      do j=1,n
        amat(i,j) = a(i,j)
      enddo
    enddo

    icheck = 0
    if (icheck == 1) then
      do i = 1,n
        write (6,*) " inverting a ->row", i, (a(i,j), j = 1,n)
      end do
    end if
    !  =====================
    !  Arguments to CGETRF
    !  =====================
    !
    !  M       (input) INTEGER
    !          The number of rows of the matrix A.  M >= 0.
    !
    !  N       (input) INTEGER
    !          The number of columns of the matrix A.  N >= 0.
    !
    !  A       (input/output) COMPLEX array, dimension (LDA,N)
    !          On entry, the M-by-N matrix to be factored.
    !          On exit, the factors L and U from the factorization
    !          A = P*L*U; the unit diagonal elements of L are not stored.
    !
    !  LDA     (input) INTEGER
    !          The leading dimension of the array A.  LDA >= max(1,M).
    !
    !  IPIV    (output) INTEGER array, dimension (min(M,N))
    !          The pivot indices; for 1 <= i <= min(M,N), row i of the
    !          matrix was interchanged with row IPIV(i).
    !
    !  INFO    (output) INTEGER
    !          = 0:  successful exit
    !          < 0:  if INFO = -i, the i-th argument had an illegal value
    !          > 0:  if INFO = i, U(i,i) is exactly zero. The factorization
    !                has been completed, but the factor U is exactly
    !                singular, and division by zero will occur if it is used
    !                to solve a system of equations.
    !
    !  =====================================================================
    info = 0
    call CGETRF(n,n,amat,neim,IPIV,INFO)
    if (info > 0) then
      write (6,*) " abnormal exit(CGETRF) singular matrix...stopping"
      stop
    end if
    if (info < 0) then
      write (6,*) " abnormal exit(CGETRF) illegal value at i=", info
      stop
    end if
    !  ===================
    !  Arguments to CGETRI
    !  ===================
    !
    !  N       (input) INTEGER
    !          The order of the matrix A.  N >= 0.
    !
    !  A       (input/output) COMPLEX array, dimension (LDA,N)
    !          On entry, the factors L and U from the factorization
    !          A = P*L*U as computed by CGETRF.
    !          On exit, if INFO = 0, the inverse of the original matrix A.
    !
    !  LDA     (input) INTEGER
    !          The leading dimension of the array A.  LDA >= max(1,N).
    !
    !  IPIV    (input) INTEGER array, dimension (N)
    !          The pivot indices from CGETRF; for 1<=i<=N, row i of the
    !          matrix was interchanged with row IPIV(i).
    !
    !  WORK    (workspace/output) COMPLEX array, dimension (LWORK)
    !          On exit, if INFO=0, then WORK(1) returns the optimal LWORK.
    !
    !  LWORK   (input) INTEGER
    !          The dimension of the array WORK.  LWORK >= max(1,N).
    !          For optimal performance LWORK >= N*NB, where NB is
    !          the optimal blocksize returned by ILAENV.
    !
    !  INFO    (output) INTEGER
    !          = 0:  successful exit
    !          < 0:  if INFO = -i, the i-th argument had an illegal value
    !          > 0:  if INFO = i, U(i,i) is exactly zero; the matrix is
    !                singular and its inverse could not be computed.
    !
    !  =====================================================================
    info = 0
    call CGETRI(N,amat,neim,IPIV,WORK,work_size,INFO)
    if (info > 0) then
      write (6,*) " abnormal exit(CGETRI) singular matrix...stopping"
      stop
    end if
    if (info < 0) then
      write (6,*) " abnormal exit(CGETRI) illegal value at i=", info
      stop
    end if

    do i=1,n
      do j=1,n
        a(i,j) = amat(i,j)
      enddo
    enddo
    if (icheck /= 1) return
    do i = 1,n
      write (6,*) "  A(inv) ->row", i, (a(i,j), j = 1,n)
    end do

  end subroutine invert_complex

subroutine CGETRF(M,N,A,LDA,IPIV,INFO)
  !
  !  -- LAPACK routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994

  complex(dp) :: ONE

  integer, intent(in) :: LDA,M,N
  integer, intent(out) :: INFO
  integer, dimension(*), intent(inout) :: IPIV
  complex(dp), dimension(LDA,*), intent(inout) :: A
  !

  integer :: I,IINFO,J,JB,NB
  !

  !
  !     Test the input parameters.
  !
  ONE = cmplx(1.0_dp,0.0_dp,dp)
  INFO = 0
  if (M < 0) then
    INFO = -1
  elseif (N < 0) then
    INFO = -2
  elseif (LDA < MAX(1,M)) then
    INFO = -4
  end if
  if (INFO /= 0) then
    call XERBLA("CGETRF",-INFO)
    stop
  end if
  !
  !     Quick return if possible
  !
  if (M==0 .or. N==0) return
  !
  !     Determine the block size for this environment.
  !
  NB = ILAENV(1,"CGETRF",M,N,-1)
  if (NB<=1 .or. NB>=MIN(M,N)) then
    !
    !        Use unblocked code.
    !
    call CGETF2(M,N,A,LDA,IPIV,INFO)
    return
  end if
  !
  !        Use blocked code.
  !
  do J = 1,MIN(M,N),NB
    JB = MIN(MIN(M,N)-J+1,NB)
    !
    !           Factor diagonal and subdiagonal blocks and test for exact
    !           singularity.
    !
    call CGETF2(M-J+1,JB,A(J,J),LDA,IPIV(J),IINFO)
    !
    !           Adjust INFO and the pivot indices.
    !
    if (INFO==0 .and. IINFO>0) then
      INFO = IINFO + J - 1
    end if
    do I = J,MIN(M,J+JB-1)
      IPIV(I) = J - 1 + IPIV(I)
    end do
    !
    !           Apply interchanges to columns 1:J-1.
    !
    call CLASWP(J-1,A,LDA,J,J+JB-1,IPIV,1)
    !
    if (J+JB <= N) then
      !
      !              Apply interchanges to columns J+JB:N.
      !
      call CLASWP(N-J-JB+1,A(1,J+JB),LDA,J,J+JB-1,IPIV,1)
      !
      !              Compute block row of U.
      !
      call CTRSM("Left","Lower","No transpose","Unit",JB,N-J-JB+1,ONE,A(J,J), &
                LDA,A(J,J+JB),LDA)
      !
      if (J+JB <= M) then
        !
        !                 Update trailing submatrix.
        !
        call CGEMM("No transpose","No transpose",M-J-JB+1,N-J-JB+1,JB,-ONE, &
                  A(J+JB,J),LDA,A(J,J+JB),LDA,ONE,A(J+JB,J+JB),LDA)
      end if
    end if
  end do
!
!     End of CGETRF
!
end subroutine CGETRF
subroutine CGETF2(M,N,A,LDA,IPIV,INFO)
  !
  !  -- LAPACK routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994
  !
  !     .. Scalar Arguments ..


  complex(dp) :: ONE
  complex(dp) :: ZERO

  integer, intent(in) :: LDA,M,N
  integer, intent(out) :: INFO
  integer, dimension(*), intent(out) :: IPIV
  complex(dp), dimension(LDA,*), intent(inout) :: A
  !

  integer :: J,JP

  !
  !     Test the input parameters.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  INFO = 0
  if (M < 0) then
    INFO = -1
  elseif (N < 0) then
    INFO = -2
  elseif (LDA < MAX(1,M)) then
    INFO = -4
  end if
  if (INFO /= 0) then
    call XERBLA("CGETF2",-INFO)
    stop
  end if
  !
  !     Quick return if possible
  !
  if (M==0 .or. N==0) return
  !
  do J = 1,MIN(M,N)
    !
    !        Find pivot and test for singularity.
    !
    JP = J - 1 + ICAMAX(M-J+1,A(J,J),1)
    IPIV(J) = JP
    if (A(JP,J) /= ZERO) then
      !
      !           Apply the interchange to columns 1:N.
      !
      if (JP /= J) then
        call CSWAP(N,A(J,1),LDA,A(JP,1),LDA)
      end if
      !
      !           Compute elements J+1:M of J-th column.
      !
      if (J < M) then
        call CSCAL(M-J,ONE/A(J,J),A(J+1,J),1)
      end if
    !
    elseif (INFO == 0) then
      !
      INFO = J
    end if
    !
    if (J < MIN(M,N)) then
      !
      !           Update trailing submatrix.
      !
      call CGERU(M-J,N-J,-ONE,A(J+1,J),1,A(J,J+1),LDA,A(J+1,J+1),LDA)
    end if
  end do
!
!     End of CGETF2
!
end subroutine CGETF2

!
subroutine CGETRI(N,A,LDA,IPIV,WORK,LWORK,INFO)
  !
  !  -- LAPACK routine (version 2.0) --
  !     Univ. of Tennessee, Univ. of California Berkeley, NAG Ltd.,
  !     Courant Institute, Argonne National Lab, and Rice University
  !     September 30, 1994
  !
  !     .. Scalar Arguments ..


  complex(dp) :: ZERO
  complex(dp) :: ONE

  integer, intent(in) :: LDA,LWORK,N
  integer, intent(out) :: INFO
  integer, dimension(*), intent(in) :: IPIV
  complex(dp), dimension(LWORK), intent(inout) :: WORK
  complex(dp), dimension(LDA,*), intent(inout) :: A

  integer :: I,IWS,J,JB,JJ,JP,LDWORK,NB,NBMIN,NN
  !

  !     ..
  !     .. Executable Statements ..
  !
  !     Test the input parameters.
  !
  ONE  = cmplx(1.0_dp,0.0_dp,dp)
  ZERO = cmplx(0.0_dp,0.0_dp,dp)
  INFO = 0
  WORK(1) = MAX(N,1)
  if (N < 0) then
    INFO = -1
  elseif (LDA < MAX(1,N)) then
    INFO = -3
  elseif (LWORK < MAX(1,N)) then
    INFO = -6
  end if
  if (INFO /= 0) then
    call XERBLA("CGETRI",-INFO)
    stop
  end if
  !
  !     Quick return if possible
  !
  if (N == 0) return
  !
  !     Form inv(U).  If INFO > 0 from CTRTRI, then U is singular,
  !     and the inverse is not computed.
  !
  call CTRTRI("Upper","Non-unit",N,A,LDA,INFO)
  if (INFO > 0) return
  !
  !     Determine the block size for this environment.
  !
  NB = ILAENV(1,"CGETRI",N,-1,-1)
  NBMIN = 2
  LDWORK = N
  if (NB>1 .and. NB<N) then
    IWS = MAX(LDWORK*NB,1)
    if (LWORK < IWS) then
      NB = LWORK / LDWORK
      NBMIN = MAX(2,ILAENV(2,"CGETRI",N,-1,-1))
    end if
  else
    IWS = N
  end if
  !
  !     Solve the equation inv(A)*L = inv(U) for inv(A).
  !
  if (NB<NBMIN .or. NB>=N) then
    !
    !        Use unblocked code.
    !
    do J = N,1,-1
      !
      !           Copy current column of L to WORK and replace with zeros.
      !
      do I = J+1,N
        WORK(I) = A(I,J)
        A(I,J) = ZERO
      end do
      !
      !           Compute current column of inv(A).
      !
      if (J < N) then
        call CGEMV("No transpose",N,N-J,-ONE,A(1,J+1),LDA,WORK(J+1),1,ONE, &
                  A(1,J),1)
      end if
    end do
  else
    !
    !        Use blocked code.
    !
    NN = ((N-1)/NB)*NB + 1
    do J = NN,1,-NB
      JB = MIN(NB,N-J+1)
      !
      !           Copy current block column of L to WORK and replace with
      !           zeros.
      !
      do JJ = J,J+JB-1
        do I = JJ+1,N
          WORK(I+(JJ-J)*LDWORK) = A(I,JJ)
          A(I,JJ) = ZERO
        end do
      end do
      !
      !           Compute current block column of inv(A).
      !
      if (J+JB <= N) then
        call CGEMM("No transpose","No transpose", &
                  N,JB,N-J-JB+1,-ONE,A(1,J+JB),   &
                  LDA,WORK(J+JB),LDWORK,ONE,A(1,J),LDA)
      end if
      call CTRSM("Right","Lower","No transpose","Unit", &
                N,JB,ONE,WORK(J),                       &
                LDWORK,A(1,J),LDA)
    end do
  end if
  !
  !     Apply column interchanges.
  !
  do J = N-1,1,-1
    JP = IPIV(J)
    if (JP /= J) then
      call CSWAP(N,A(1,J),1,A(1,JP),1)
    end if
  end do
  !
  WORK(1) = IWS
!
!     End of CGETRI
!
end subroutine CGETRI




end module invert_lapack

module ivals

  use kinddefs, only : dp

  implicit none

  private

  public :: c0, et0, h0, s0, p0, pt0, rho0, u0, v0, w0, amut0
  public :: q0, twall_bc, sonic0_k, pressure0_jac
  public :: density0, molecular_weight0
  public :: velocity0, density0_i, dif0_i, enthalpy0_ij, temperature0_j
  public :: energy0_j, entropy0_j, cv0_j, eta0_j, pressure0_k, amu0_k, turb0
  public :: htg0, htg0_min, t_fac_dim

  public :: sonic1_k, pressure1_jac
  public :: density1_i, dif1_i, enthalpy1_ij, temperature1_j
  public :: energy1_j, entropy1_j, cv1_j, eta1_j, pressure1_k, amu1_k, turb1

  real(dp) :: c0   ! Freestream speed of sound
  real(dp) :: et0  ! Freestream total energy
  real(dp) :: h0   ! Freestream enthalpy
  real(dp) :: s0   ! Freestream entropy
  real(dp) :: p0   ! Freestream pressure
  real(dp) :: pt0  ! Freestream total pressure
  real(dp) :: rho0 ! Freestream density
  real(dp) :: u0   ! Freestream u-velocity
  real(dp) :: v0   ! Freestream v-velocity
  real(dp) :: w0   ! Freestream w-velocity

! Flags and scaling parameters for radiative equilibrium in perfect gas path
! q_wall = emissivity*stefan_boltzmann*T_wall_new**4
! T_wall = rad_relax*T_wall_new + (1 - rad_relax)*T_wall_old

  real(dp) :: htg0 ! conversion factor to get dimensional heating in W/m**2
  real(dp) :: htg0_min ! heating corresponding to minimum wall temperature 300 K
  real(dp) :: t_fac_dim ! T, K = (gamma*p/rho)_nondim * t_fac_dim

! Define initial values for eqn_set=2

  real(dp), dimension(:),   pointer :: q0
  real(dp), dimension(:),   pointer :: density0_i, density1_i
  real(dp), dimension(:),   pointer :: temperature0_j, temperature1_j
  real(dp), dimension(:),   pointer :: energy0_j, energy1_j
  real(dp), dimension(:),   pointer :: entropy0_j, entropy1_j
  real(dp), dimension(:),   pointer :: pressure0_k, pressure1_k
  real(dp), dimension(:),   pointer :: cv0_j, cv1_j
  real(dp), dimension(:,:), pointer :: enthalpy0_ij, enthalpy1_ij
  real(dp), dimension(:),   pointer :: sonic0_k, sonic1_k
  real(dp), dimension(:,:), pointer :: pressure0_jac, pressure1_jac
  real(dp), dimension(:),   pointer :: amu0_k, amu1_k
  real(dp), dimension(:),   pointer :: eta0_j, eta1_j
  real(dp), dimension(:),   pointer :: dif0_i, dif1_i
  real(dp), dimension(:),   pointer :: turb0, turb1

  real(dp) :: density0
  real(dp) :: velocity0
  real(dp) :: amut0
  real(dp) :: molecular_weight0
  real(dp) :: twall_bc

end module ivals

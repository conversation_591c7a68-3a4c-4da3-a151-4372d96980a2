module complex_functions

  implicit none

  private

  public :: o
  public :: extract_imaginary_part
  public :: cccpu_time
  public :: ccrandom_number
  public :: ccceiling, ccnint
  public :: ccsign, ccabs, ccdim
  public :: ccmin, ccmax
  public :: ccminval, ccmaxval
  public :: ccminloc, ccmaxloc
  public :: cctan
  public :: ccacos, ccasin, ccatan, ccatan2
  public :: cclog10
  public :: cccosh, ccsinh, cctanh

  public :: operator(>), operator(<), operator(>=), operator(<=)

  interface o
    module procedure cmplx_o
    module procedure real_o
  end interface

  interface extract_imaginary_part
    module procedure cmplx_extract_imaginary_part
    module procedure real_extract_imaginary_part
  end interface

  interface cccpu_time
    module procedure cccpu_time_c8
    module procedure cccpu_time_r4
    module procedure cccpu_time_r8
  end interface cccpu_time

  interface ccrandom_number
    module procedure ccrandom_number_c4
    module procedure ccrandom_number_c8
    module procedure ccrandom_number_r4
    module procedure ccrandom_number_r8
  end interface ccrandom_number

  interface ccceiling
    module procedure ccceiling_c4
    module procedure ccceiling_c8
    module procedure ccceiling_r4
    module procedure ccceiling_r8
  end interface ccceiling

  interface ccnint
    module procedure ccnint_c4
    module procedure ccnint_c8
    module procedure ccnint_r4
    module procedure ccnint_r8
  end interface ccnint

  interface ccsign
    module procedure ccsign_c8c8
    module procedure ccsign_c4c4
    module procedure ccsign_c8c8v
    module procedure ccsign_i4i4
    module procedure ccsign_i8i8
    module procedure ccsign_r8c8
  end interface ccsign

  interface ccabs
    module procedure ccabs_c4
    module procedure ccabs_c8
    module procedure ccabs_i4
    module procedure ccabs_i8
    module procedure ccabs_r4
    module procedure ccabs_r8
  end interface ccabs

  interface ccdim
    module procedure ccdim_c8c8
  end interface ccdim

  interface ccmin
    module procedure ccmin_c8c8
    module procedure ccmin_c8c8c8
    module procedure ccmin_c8c8c8c8
    module procedure ccmin_c8c8c8c8c8c8
    module procedure ccmin_c8r8
    module procedure ccmin_i1i1
    module procedure ccmin_i4i4
    module procedure ccmin_i4i4i4
    module procedure ccmin_i4i4i4i4
    module procedure ccmin_i4i4i4i4i4i4
    module procedure ccmin_i4i4i4i4i4i4i4i4
    module procedure ccmin_i8i8
    module procedure ccmin_i8i8i8
    module procedure ccmin_i8i8i8i8
    module procedure ccmin_i8i8i8i8i8i8
    module procedure ccmin_i8i8i8i8i8i8i8i8
    module procedure ccmin_r8c8
    module procedure ccmin_r8c8c8
    module procedure ccmin_r8r8
  end interface ccmin

  interface ccmax
    module procedure ccmax_c4c4
    module procedure ccmax_c4c8
    module procedure ccmax_c8c8
    module procedure ccmax_c8c8c8
    module procedure ccmax_c8c8c8c8
    module procedure ccmax_c8r8
    module procedure ccmax_i1i1
    module procedure ccmax_i1i4
    module procedure ccmax_i1i8
    module procedure ccmax_i2i2
    module procedure ccmax_i4i1
    module procedure ccmax_i4i4
    module procedure ccmax_i4i4i4
    module procedure ccmax_i4i4i4i4
    module procedure ccmax_i4i4i4i4i4
    module procedure ccmax_i4i4i4i4i4i4
    module procedure ccmax_i8i1
    module procedure ccmax_i8i8
    module procedure ccmax_i8i8i8
    module procedure ccmax_i8i8i8i8
    module procedure ccmax_i8i8i8i8i8
    module procedure ccmax_i8i8i8i8i8i8
    module procedure ccmax_r8c8
    module procedure ccmax_r8c8c8
    module procedure ccmax_r4r4
    module procedure ccmax_r8r8
  end interface ccmax

  interface ccminval
    module procedure ccminval_c8
    module procedure ccminval_c8_2d
    module procedure ccminval_i1
    module procedure ccminval_i1_2d
    module procedure ccminval_i4
    module procedure ccminval_i4_2d
    module procedure ccminval_i8
    module procedure ccminval_i8_2d
    module procedure ccminval_r4
    module procedure ccminval_r8
  end interface ccminval

  interface ccmaxval
    module procedure ccmaxval_i1
    module procedure ccmaxval_i1_2d
    module procedure ccmaxval_i4
    module procedure ccmaxval_i4_2d
    module procedure ccmaxval_i8
    module procedure ccmaxval_i8_2d
    module procedure ccmaxval_c8
    module procedure ccmaxval_c8_2d
    module procedure ccmaxval_r4
    module procedure ccmaxval_r8
  end interface ccmaxval

  interface ccminloc
    module procedure ccminloc_i4
    module procedure ccminloc_i4_dim
    module procedure ccminloc_i8
    module procedure ccminloc_i8_dim
    module procedure ccminloc_c8
    module procedure ccminloc_c8_2d
    module procedure ccminloc_c8_dim
  end interface ccminloc

  interface ccmaxloc
    module procedure ccmaxloc_i4
    module procedure ccmaxloc_i4_dim
    module procedure ccmaxloc_i8
    module procedure ccmaxloc_i8_dim
    module procedure ccmaxloc_c8
    module procedure ccmaxloc_c8_2d
    module procedure ccmaxloc_c8_dim
  end interface ccmaxloc

  interface cctan
    module procedure cctan_c8
    module procedure cctan_r8
  end interface cctan

  interface ccacos
    module procedure ccacos_c8
    module procedure ccacos_r8
  end interface ccacos

  interface ccasin
    module procedure ccasin_c8
    module procedure ccasin_r8
  end interface ccasin

  interface ccatan
    module procedure ccatan_c8c4
    module procedure ccatan_c8c8
    module procedure ccatan_r8r4
    module procedure ccatan_r8r8
  end interface ccatan

  interface ccatan2
    module procedure ccatan2_c8c8
    module procedure ccatan2_r8r8
  end interface ccatan2

  interface cctanh
    module procedure cctanh_c8
    module procedure cctanh_r8
  end interface cctanh

  interface cclog10
    module procedure cclog10_c4
    module procedure cclog10_c8
    module procedure cclog10_r4
    module procedure cclog10_r8
  end interface cclog10

  interface operator(<)
    module procedure lt_c4c4
    module procedure lt_c8c8
    module procedure lt_c8r8
    module procedure lt_c8vc8
    module procedure lt_r4c4
    module procedure lt_r4c8
    module procedure lt_r8c8
  end interface

  interface operator(<=)
    module procedure le_c4c4
    module procedure le_c8c8
    module procedure le_c8r8
    module procedure le_c8vc8
    module procedure le_r4c4
    module procedure le_r8c8
  end interface

  interface operator(>=)
    module procedure ge_c4c4
    module procedure ge_c8c8
    module procedure ge_c8i4
    module procedure ge_c8i8
    module procedure ge_c8r8
    module procedure ge_c8vc8
    module procedure ge_i4c8
    module procedure ge_i8c8
    module procedure ge_r4c8
    module procedure ge_r8c8
  end interface

  interface operator(>)
    module procedure gt_c4c4
    module procedure gt_c4r4
    module procedure gt_c8c8
    module procedure gt_c8vc8
    module procedure gt_c8vr8
    module procedure gt_r4c4
    module procedure gt_r4c8
    module procedure gt_r8c8
    module procedure gt_c8r8
  end interface

contains

!================================= CMPLX_O  ==================================80
!
! Real part of arg_in.
!
!=============================================================================80

  pure function cmplx_o( arg_in )
    use kinddefs, only : dp
    complex(dp), intent(in)  :: arg_in
    real(dp)                 :: cmplx_o
    continue
    cmplx_o = real(arg_in,dp)
  end function cmplx_o

!========================== REAL_O  ==========================================80
!
! Real part of arg_in.
!
!=============================================================================80

  pure function real_o( arg_in )
    use kinddefs, only : dp
    real(dp), intent(in)  :: arg_in
    real(dp)              :: real_o
    continue
    real_o = real(arg_in,dp)
  end function real_o

!================================= CMPLX_EXTRACT_IMAGINARY_PART  =============80
!
! Imaginary part of arg_in returned in real part of arg_out.
!
!=============================================================================80

  subroutine cmplx_extract_imaginary_part(arg_in,arg_out)
    use kinddefs, only : dp
    complex(dp), intent(in)  :: arg_in
    complex(dp), intent(out) :: arg_out
    continue
    arg_out = aimag(arg_in)
  end subroutine cmplx_extract_imaginary_part

!========================== REAL_EXTRACT_IMAGINARY_PART  =====================80
!
! Allow compilation of complex code before complexification.
!
!=============================================================================80

  subroutine real_extract_imaginary_part(arg_in,arg_out)
    use kinddefs, only : dp
    real(dp), intent(in)  :: arg_in
    real(dp), intent(out) :: arg_out
    continue
    arg_out = arg_in
  end subroutine real_extract_imaginary_part

!================================ CCCPU_TIME_* ===============================80
!
! Complex CPU_TIME() for various argument types.
!
!=============================================================================80

  subroutine cccpu_time_c8( time_c )
    use kinddefs, only : r8
    complex(r8), intent(out) :: time_c
    real(r8) :: time_r
   continue
    call cpu_time( time_r )
    time_c = time_r
  end subroutine cccpu_time_c8

  subroutine cccpu_time_r4( time_r )
    use kinddefs, only : r4
    real(r4), intent(out) :: time_r
   continue
    call cpu_time( time_r )
  end subroutine cccpu_time_r4

  subroutine cccpu_time_r8( time_r )
    use kinddefs, only : r8
    real(r8), intent(out) :: time_r
   continue
    call cpu_time( time_r )
  end subroutine cccpu_time_r8

!================================== CCRANDOM_NUMBER_* ========================80
!
! Complex RANDOM_NUMBER() for various argument types.
!
!=============================================================================80

  subroutine ccrandom_number_c4( harvest_c4 )
    use kinddefs, only : r4
    complex(r4), intent(out) :: harvest_c4
    real(r4) :: harvest
   continue
    call random_number(harvest)
    harvest_c4 = harvest
  end subroutine ccrandom_number_c4

  subroutine ccrandom_number_c8( harvest_c )
    use kinddefs, only : r8
    complex(r8), intent(out) :: harvest_c
    real(r8) :: harvest
   continue
    call random_number(harvest)
    harvest_c = harvest
  end subroutine ccrandom_number_c8

  subroutine ccrandom_number_r4( harvest_r4 )
    use kinddefs, only : r4
    real(r4), intent(out) :: harvest_r4
    real(r4) :: harvest
   continue
    call random_number(harvest)
    harvest_r4 = harvest
  end subroutine ccrandom_number_r4

  subroutine ccrandom_number_r8( harvest_r )
    use kinddefs, only : r8
    real(r8), intent(out) :: harvest_r
    real(r8) :: harvest
   continue
    call random_number(harvest)
    harvest_r = harvest
  end subroutine ccrandom_number_r8

!================================ CCCEILING_* ================================80
!
! Complex CEILING() for various argument types.
!
!=============================================================================80

  elemental function ccceiling_c4(a)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a
    integer                 :: ccceiling_c4
   continue
    ccceiling_c4 = ceiling( real(a,r4) )
  end function ccceiling_c4

  elemental function ccceiling_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    integer                 :: ccceiling_c8
   continue
    ccceiling_c8 = ceiling( real(a,r8) )
  end function ccceiling_c8

  elemental function ccceiling_r4(a)
    use kinddefs, only : r4
    real(r4), intent(in) :: a
    integer              :: ccceiling_r4
   continue
    ccceiling_r4 = ceiling( a )
  end function ccceiling_r4

  elemental function ccceiling_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    integer              :: ccceiling_r8
   continue
    ccceiling_r8 = ceiling( a )
  end function ccceiling_r8

!================================= CCNINT_* ==================================80
!
! Complex NINT() for various argument types.
!
!=============================================================================80

  elemental function ccnint_c4(a)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a
    integer                        :: ccnint_c4
   continue
    ccnint_c4 = nint( real(a,r4) )
  end function ccnint_c4

  elemental function ccnint_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    integer                 :: ccnint_c8
   continue
    ccnint_c8 = nint( real(a,r8) )
  end function ccnint_c8

  elemental function ccnint_r4(a)
    use kinddefs, only : r4
    real(r4), intent(in) :: a
    integer                     :: ccnint_r4
   continue
    ccnint_r4 = nint( a )
  end function ccnint_r4

  elemental function ccnint_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    integer              :: ccnint_r8
   continue
    ccnint_r8 = nint( a )
  end function ccnint_r8

!================================== CCSIGN_* =================================80
!
! Complex SIGN() for various argument types.
!
!=============================================================================80

  pure function ccsign_c8c8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a, b
    complex(r8)             :: ccsign_c8c8
    real(r8) :: sgnb
   continue
    sgnb = 1.0_r8
    if (real(b,r8) < 0.0_r8) sgnb = -1.0_r8
    ccsign_c8c8 = ccabs(a)*sgnb
  end function ccsign_c8c8

  pure function ccsign_c4c4(a,b)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a, b
    complex(r4)             :: ccsign_c4c4
    real(r4) :: sgnb
   continue
    sgnb = 1.0_r4
    if (real(b,r4) < 0.0_r4) sgnb = -1.0_r4
    ccsign_c4c4 = ccabs(a)*sgnb
  end function ccsign_c4c4

  pure function ccsign_c8c8v(a,b)
    use kinddefs, only : r8
    complex(r8),               intent(in) :: a
    complex(r8), dimension(:), intent(in) :: b
    complex(r8), dimension(size(b,1))     :: ccsign_c8c8v
    real(r8), dimension(size(b,1)) :: sgnb
    integer :: i
   continue
    sgnb(:) = 1.0_r8
    do i=1,size(b,1)
      if (real(b(i),r8) < 0.0_r8) sgnb(i) = -1.0_r8
    enddo
    ccsign_c8c8v = ccabs(a)*sgnb
  end function ccsign_c8c8v

  pure function ccsign_i4i4(a,b)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a, b
    integer                 :: ccsign_i4i4
    integer :: sgnb
   continue
    sgnb = 1
    if ( b < 0 ) sgnb = -1
    ccsign_i4i4 = abs(a)*sgnb
  end function ccsign_i4i4

  pure function ccsign_i8i8(a,b)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a, b
    integer                 :: ccsign_i8i8
    integer :: sgnb
   continue
    sgnb = 1
    if ( b < 0 ) sgnb = -1
    ccsign_i8i8 = abs(a)*sgnb
  end function ccsign_i8i8

  pure function ccsign_r8c8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a
    complex(r8), intent(in) :: b
    real(r8)                :: ccsign_r8c8
    real(r8) :: sgnb
   continue
    sgnb = 1.0_r8
    if (real(b,r8) < 0.0_r8) sgnb = -1.0_r8
    ccsign_r8c8 = abs(a)*sgnb
  end function ccsign_r8c8

!================================== CCABS_* ==================================80
!
! Complex ABS() for various argument types.
!
!=============================================================================80

  pure elemental function ccabs_c4(a)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a
    complex(r4)             :: ccabs_c4
   continue
    ccabs_c4 = a
    if ( real(a,r4) < 0.0_r4 ) ccabs_c4 = -a
  end function ccabs_c4

  pure elemental function ccabs_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: ccabs_c8
   continue
    ccabs_c8 = a
    if ( real(a,r8) < 0.0_r8 ) ccabs_c8 = -a
  end function ccabs_c8

  pure elemental function ccabs_i4(a)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a
    integer(i4)             :: ccabs_i4
   continue
    ccabs_i4 = abs( a )
  end function ccabs_i4

  pure elemental function ccabs_i8(a)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a
    integer(i8)             :: ccabs_i8
   continue
    ccabs_i8 = abs( a )
  end function ccabs_i8

  pure elemental function ccabs_r4(a)
    use kinddefs, only : r4
    real(r4), intent(in) :: a
    real(r4)             :: ccabs_r4
   continue
    ccabs_r4 = abs( a )
  end function ccabs_r4

  pure elemental function ccabs_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    real(r8)             :: ccabs_r8
   continue
    ccabs_r8 = abs( a )
  end function ccabs_r8

!================================== CCDIM_* ==================================80
!
! Complex DIM() for various argument types.
!
!=============================================================================80

  elemental function ccdim_c8c8(a1,a2)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a1, a2
    complex(r8)             :: ccdim_c8c8
   continue
    ccdim_c8c8 = ccmax(a2-a1, 0.0_r8)
  end function ccdim_c8c8

!================================== CCMIN_* ==================================80
!
! Complex MIN() for various argument types.
!
!=============================================================================80

  elemental function ccmin_c8c8(a1,a2)
    use kinddefs, only : r8
    complex(r8), intent(in)  :: a1, a2
    complex(r8)              :: ccmin_c8c8
   continue
    if ( real(a1,r8) < real(a2,r8) ) then
      ccmin_c8c8 = a1
    else
      ccmin_c8c8 = a2
    endif
  end function ccmin_c8c8

  elemental function ccmin_c8c8c8(a1,a2,a3)
    use kinddefs, only : r8
    complex(r8), intent(in)  :: a1, a2, a3
    complex(r8)              :: ccmin_c8c8c8
   continue
    ccmin_c8c8c8 = ccmin(a1,ccmin(a2,a3))
  end function ccmin_c8c8c8

  elemental function ccmin_c8c8c8c8(a1,a2,a3,a4)
    use kinddefs, only : r8
    complex(r8), intent(in)  :: a1, a2, a3, a4
    complex(r8)              :: ccmin_c8c8c8c8
   continue
    ccmin_c8c8c8c8 = ccmin(ccmin(a1,a2),ccmin(a3,a4))
  end function ccmin_c8c8c8c8

  elemental function ccmin_c8c8c8c8c8c8(a1,a2,a3,a4,a5,a6)
    use kinddefs, only : r8
    complex(r8), intent(in)  :: a1, a2, a3, a4, a5, a6
    complex(r8)              :: ccmin_c8c8c8c8c8c8
   continue
    ccmin_c8c8c8c8c8c8 = ccmin(ccmin(a1,a2),ccmin(a3,a4),ccmin(a5,a6))
  end function ccmin_c8c8c8c8c8c8

  elemental function ccmin_c8r8(a1,a2)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a1
    real(r8),    intent(in) :: a2
    complex(r8)             :: ccmin_c8r8
   continue
    if ( real(a1,r8) < a2 ) then
      ccmin_c8r8 = a1
    else
      ccmin_c8r8 = cmplx(a2, 0.0_r8, r8)
    endif
  end function ccmin_c8r8

  elemental function ccmin_i1i1(a1,a2)
    use kinddefs, only : i1
    integer(i1), intent(in) :: a1, a2
    integer(i1)             :: ccmin_i1i1
   continue
    ccmin_i1i1 = min(a1,a2)
  end function ccmin_i1i1

  elemental function ccmin_i4i4(a1,a2)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2
    integer(i4)             :: ccmin_i4i4
   continue
    ccmin_i4i4 = min(a1,a2)
  end function ccmin_i4i4

  elemental function ccmin_i4i4i4(a1,a2,a3)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3
    integer(i4)             :: ccmin_i4i4i4
   continue
    ccmin_i4i4i4 = min(a1,a2,a3)
  end function ccmin_i4i4i4

  elemental function ccmin_i4i4i4i4(a1,a2,a3,a4)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3, a4
    integer(i4)             :: ccmin_i4i4i4i4
   continue
    ccmin_i4i4i4i4 = min(a1,a2,a3,a4)
  end function ccmin_i4i4i4i4

  elemental function ccmin_i4i4i4i4i4i4(a1,a2,a3,a4,a5,a6)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3, a4, a5, a6
    integer(i4)             :: ccmin_i4i4i4i4i4i4
   continue
    ccmin_i4i4i4i4i4i4 = min(a1,a2,a3,a4,a5,a6)
  end function ccmin_i4i4i4i4i4i4

  elemental function ccmin_i4i4i4i4i4i4i4i4(a1,a2,a3,a4,a5,a6,a7,a8)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3, a4, a5, a6, a7, a8
    integer(i4)             :: ccmin_i4i4i4i4i4i4i4i4
   continue
    ccmin_i4i4i4i4i4i4i4i4 = min(a1,a2,a3,a4,a5,a6,a7,a8)
  end function ccmin_i4i4i4i4i4i4i4i4

  elemental function ccmin_i8i8(a1,a2)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2
    integer(i8)             :: ccmin_i8i8
   continue
    ccmin_i8i8 = min(a1,a2)
  end function ccmin_i8i8

  elemental function ccmin_i8i8i8(a1,a2,a3)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3
    integer(i8)             :: ccmin_i8i8i8
   continue
    ccmin_i8i8i8 = min(a1,a2,a3)
  end function ccmin_i8i8i8

  elemental function ccmin_i8i8i8i8(a1,a2,a3,a4)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3, a4
    integer(i8)             :: ccmin_i8i8i8i8
   continue
    ccmin_i8i8i8i8 = min(a1,a2,a3,a4)
  end function ccmin_i8i8i8i8

  elemental function ccmin_i8i8i8i8i8i8(a1,a2,a3,a4,a5,a6)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3, a4, a5, a6
    integer(i8)             :: ccmin_i8i8i8i8i8i8
   continue
    ccmin_i8i8i8i8i8i8 = min(a1,a2,a3,a4,a5,a6)
  end function ccmin_i8i8i8i8i8i8

  elemental function ccmin_i8i8i8i8i8i8i8i8(a1,a2,a3,a4,a5,a6,a7,a8)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3, a4, a5, a6, a7, a8
    integer(i8)             :: ccmin_i8i8i8i8i8i8i8i8
   continue
    ccmin_i8i8i8i8i8i8i8i8 = min(a1,a2,a3,a4,a5,a6,a7,a8)
  end function ccmin_i8i8i8i8i8i8i8i8

  elemental function ccmin_r8c8(a1,a2)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a1
    complex(r8), intent(in) :: a2
    complex(r8)             :: ccmin_r8c8
   continue
    if ( a1 < real(a2,r8) ) then
      ccmin_r8c8 = cmplx(a1, 0.0_r8, r8)
    else
      ccmin_r8c8 = a2
    endif
  end function ccmin_r8c8

  elemental function ccmin_r8c8c8(a1,a2,a3)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a1
    complex(r8), intent(in) :: a2
    complex(r8), intent(in) :: a3
    complex(r8)             :: ccmin_r8c8c8
   continue
    ccmin_r8c8c8 = ccmin( ccmin( a1, a2 ), a3 )
  end function ccmin_r8c8c8

  elemental function ccmin_r8r8(a1,a2)
    use kinddefs, only : r8
    real(r8), intent(in) :: a1
    real(r8), intent(in) :: a2
    complex(r8)          :: ccmin_r8r8
   continue
    ccmin_r8r8 = min(a1,a2)
  end function ccmin_r8r8

!================================== CCMAX_* ==================================80
!
! Complex MAX() for various argument types.
!
!=============================================================================80

  elemental function ccmax_c4c4(a1,a2)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a1, a2
    complex(r4)             :: ccmax_c4c4
   continue
    if ( real(a1,r4) > real(a2,r4) ) then
      ccmax_c4c4 = a1
    else
      ccmax_c4c4 = a2
    endif
  end function ccmax_c4c4

  elemental function ccmax_c4c8(a1,a2)
    use kinddefs, only : r4, r8
    complex(r4), intent(in) :: a1
    complex(r8), intent(in) :: a2
    complex(r8)             :: ccmax_c4c8
   continue
    if ( real(a1,r4) > real(a2,r4) ) then
      ccmax_c4c8 = a1
    else
      ccmax_c4c8 = cmplx(real(a2,r4),aimag(a2),r4)
    endif
  end function ccmax_c4c8

  elemental function ccmax_c8c8(a1,a2)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a1, a2
    complex(r8)             :: ccmax_c8c8
   continue
    if ( real(a1,r8) > real(a2,r8) ) then
      ccmax_c8c8 = a1
    else
      ccmax_c8c8 = a2
    endif
  end function ccmax_c8c8

  elemental function ccmax_c8c8c8(a1,a2,a3)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a1, a2, a3
    complex(r8)             :: ccmax_c8c8c8
   continue
    if ( real(a1,r8) > real(a2,r8) ) then
      if ( real(a1,r8) > real(a3,r8) ) then
        ccmax_c8c8c8 = a1
      else
        ccmax_c8c8c8 = a3
      endif
    else
      if ( real(a2,r8) > real(a3,r8) ) then
        ccmax_c8c8c8 = a2
      else
        ccmax_c8c8c8 = a3
      endif
    endif
  end function ccmax_c8c8c8

  elemental function ccmax_c8c8c8c8(a1,a2,a3,a4)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a1, a2, a3, a4
    complex(r8)             :: ccmax_c8c8c8c8
   continue
    ccmax_c8c8c8c8 = ccmax( ccmax( a1, a2 ), ccmax( a3, a4 ) )
  end function ccmax_c8c8c8c8

  elemental function ccmax_c8r8(a1,a2)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a1
    real(r8),    intent(in) :: a2
    complex(r8)             :: ccmax_c8r8
   continue
    if ( real(a1,r8) > a2 ) then
      ccmax_c8r8 = a1
    else
      ccmax_c8r8 = cmplx(a2, 0.0_r8, r8)
    endif
  end function ccmax_c8r8

  elemental function ccmax_i1i1(a1,a2)
    use kinddefs, only : i1
    integer(i1), intent(in) :: a1, a2
    integer(i1)             :: ccmax_i1i1
   continue
    if ( a1 > a2 ) then
      ccmax_i1i1 = a1
    else
      ccmax_i1i1 = a2
    endif
  end function ccmax_i1i1

  elemental function ccmax_i1i4(a1,a2)
    use kinddefs, only : i1, i4
    integer(i1), intent(in) :: a1
    integer(i4), intent(in) :: a2
    integer(i4)             :: ccmax_i1i4
   continue
    if ( a1 > a2 ) then
      ccmax_i1i4 = a1
    else
      ccmax_i1i4 = a2
    endif
  end function ccmax_i1i4

  elemental function ccmax_i1i8(a1,a2)
    use kinddefs, only : i1, i8
    integer(i1), intent(in) :: a1
    integer(i8), intent(in) :: a2
    integer(i8)             :: ccmax_i1i8
   continue
    if ( a1 > a2 ) then
      ccmax_i1i8 = a1
    else
      ccmax_i1i8 = a2
    endif
  end function ccmax_i1i8

  elemental function ccmax_i2i2(a1,a2)
    use kinddefs, only : i2
    integer(i2), intent(in) :: a1, a2
    integer(i2)             :: ccmax_i2i2
   continue
    if ( a1 > a2 ) then
      ccmax_i2i2 = a1
    else
      ccmax_i2i2 = a2
    endif
  end function ccmax_i2i2

  elemental function ccmax_i4i1(a1,a2)
    use kinddefs, only : i1, i4
    integer(i4), intent(in) :: a1
    integer(i1), intent(in) :: a2
    integer(i4)             :: ccmax_i4i1
   continue
    if ( a1 > a2 ) then
      ccmax_i4i1 = a1
    else
      ccmax_i4i1 = a2
    endif
  end function ccmax_i4i1

  elemental function ccmax_i4i4(a1,a2)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2
    integer(i4)             :: ccmax_i4i4
   continue
    if ( a1 > a2 ) then
      ccmax_i4i4 = a1
    else
      ccmax_i4i4 = a2
    endif
  end function ccmax_i4i4

  elemental function ccmax_i4i4i4(a1,a2,a3)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3
    integer(i4)             :: ccmax_i4i4i4
   continue
    ccmax_i4i4i4 = ccmax(ccmax(a1,a2),a3)
  end function ccmax_i4i4i4

  elemental function ccmax_i4i4i4i4(a1,a2,a3,a4)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3, a4
    integer(i4)             :: ccmax_i4i4i4i4
   continue
    ccmax_i4i4i4i4 = ccmax(ccmax(a1,a2),ccmax(a3,a4))
  end function ccmax_i4i4i4i4

  elemental function ccmax_i4i4i4i4i4(a1,a2,a3,a4,a5)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3, a4, a5
    integer(i4)             :: ccmax_i4i4i4i4i4
   continue
    ccmax_i4i4i4i4i4 = ccmax(ccmax(a1,a2),ccmax(a3,a4,a5))
  end function ccmax_i4i4i4i4i4

  elemental function ccmax_i4i4i4i4i4i4(a1,a2,a3,a4,a5,a6)
    use kinddefs, only : i4
    integer(i4), intent(in) :: a1, a2, a3, a4, a5, a6
    integer(i4)             :: ccmax_i4i4i4i4i4i4
   continue
    ccmax_i4i4i4i4i4i4 = ccmax(ccmax(a1,a2,a3),ccmax(a4,a5,a6))
  end function ccmax_i4i4i4i4i4i4

  elemental function ccmax_i8i1(a1,a2)
    use kinddefs, only : i8, i1
    integer(i8), intent(in) :: a1
    integer(i1), intent(in) :: a2
    integer(i8)             :: ccmax_i8i1
   continue
    if ( a1 > a2 ) then
      ccmax_i8i1 = a1
    else
      ccmax_i8i1 = a2
    endif
  end function ccmax_i8i1

  elemental function ccmax_i8i8(a1,a2)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2
    integer(i8)             :: ccmax_i8i8
   continue
    if ( a1 > a2 ) then
      ccmax_i8i8 = a1
    else
      ccmax_i8i8 = a2
    endif
  end function ccmax_i8i8

  elemental function ccmax_i8i8i8(a1,a2,a3)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3
    integer(i8)             :: ccmax_i8i8i8
   continue
    ccmax_i8i8i8 = ccmax(ccmax(a1,a2),a3)
  end function ccmax_i8i8i8

  elemental function ccmax_i8i8i8i8(a1,a2,a3,a4)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3, a4
    integer(i8)             :: ccmax_i8i8i8i8
   continue
    ccmax_i8i8i8i8 = ccmax(ccmax(a1,a2),ccmax(a3,a4))
  end function ccmax_i8i8i8i8

  elemental function ccmax_i8i8i8i8i8(a1,a2,a3,a4,a5)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3, a4, a5
    integer(i8)             :: ccmax_i8i8i8i8i8
   continue
    ccmax_i8i8i8i8i8 = ccmax(ccmax(a1,a2),ccmax(a3,a4,a5))
  end function ccmax_i8i8i8i8i8

  elemental function ccmax_i8i8i8i8i8i8(a1,a2,a3,a4,a5,a6)
    use kinddefs, only : i8
    integer(i8), intent(in) :: a1, a2, a3, a4, a5, a6
    integer(i8)             :: ccmax_i8i8i8i8i8i8
   continue
    ccmax_i8i8i8i8i8i8 = ccmax(ccmax(a1,a2,a3),ccmax(a4,a5,a6))
  end function ccmax_i8i8i8i8i8i8

  elemental function ccmax_r4r4(a1,a2)
    use kinddefs, only : r4
    real(r4), intent(in) :: a1, a2
    real(r4)             :: ccmax_r4r4
   continue
    ccmax_r4r4 = max(a1, a2)
  end function ccmax_r4r4

  elemental function ccmax_r8c8(a1,a2)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a1
    complex(r8), intent(in) :: a2
    complex(r8)             :: ccmax_r8c8
   continue
    if ( a1 > real(a2,r8) ) then
      ccmax_r8c8 = cmplx(a1, 0.0_r8, r8)
    else
      ccmax_r8c8 = a2
    endif
  end function ccmax_r8c8

  elemental function ccmax_r8c8c8(a1,a2,a3)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a1
    complex(r8), intent(in) :: a2
    complex(r8), intent(in) :: a3
    complex(r8)             :: ccmax_r8c8c8
   continue
    ccmax_r8c8c8 = ccmax( ccmax( a1, a2), a3)
  end function ccmax_r8c8c8

  elemental function ccmax_r8r8(a1,a2)
    use kinddefs, only : r8
    real(r8), intent(in) :: a1, a2
    real(r8)             :: ccmax_r8r8
   continue
    ccmax_r8r8 = max(a1, a2)
  end function ccmax_r8r8

!================================== CCMINVAL_* ===============================80
!
! Complex MINVAL() for various argument types.
!
!=============================================================================80

  pure function ccminval_c8(a,mask)
    use kinddefs, only : r8
    complex(r8), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    complex(r8)                                           :: ccminval_c8
    integer, dimension(1) :: min_location
   continue
    mask_conditional : if ( present(mask) ) then
      min_location = ccminloc(a, mask)
    else
      min_location = ccminloc(a)
    end if mask_conditional
    ccminval_c8 = a(min_location(1))
  end function ccminval_c8

  pure function ccminval_c8_2d(a)
    use kinddefs, only : r8
    complex(r8), dimension(:,:), intent(in) :: a
    complex(r8)                             :: ccminval_c8_2d
    integer, dimension(2) :: min_location
   continue
    min_location = ccminloc(a)
    ccminval_c8_2d = a(min_location(1),min_location(2))
  end function ccminval_c8_2d

  pure function ccminval_i1(a)
    use kinddefs, only : i1
    integer(i1), dimension(:), intent(in) :: a
    integer(i1)                           :: ccminval_i1
   continue
    ccminval_i1 = minval(a)
  end function ccminval_i1

  pure function ccminval_i1_2d(a)
    use kinddefs, only : i1
    integer(i1), dimension(:,:), intent(in) :: a
    integer(i1)                             :: ccminval_i1_2d
   continue
    ccminval_i1_2d = minval(a)
  end function ccminval_i1_2d

  pure function ccminval_i4(a)
    use kinddefs, only : i4
    integer(i4), dimension(:), intent(in) :: a
    integer(i4)                           :: ccminval_i4
   continue
    ccminval_i4 = minval(a)
  end function ccminval_i4

  pure function ccminval_i4_2d(a)
    use kinddefs, only : i4
    integer(i4), dimension(:,:), intent(in) :: a
    integer(i4)                             :: ccminval_i4_2d
   continue
    ccminval_i4_2d = minval(a)
  end function ccminval_i4_2d

  pure function ccminval_i8(a)
    use kinddefs, only : i8
    integer(i8), dimension(:), intent(in) :: a
    integer(i8)                           :: ccminval_i8
   continue
    ccminval_i8 = minval(a)
  end function ccminval_i8

  pure function ccminval_i8_2d(a)
    use kinddefs, only : i8
    integer(i8), dimension(:,:), intent(in) :: a
    integer(i8)                             :: ccminval_i8_2d
   continue
    ccminval_i8_2d = minval(a)
  end function ccminval_i8_2d

  pure function ccminval_r4(a)
    use kinddefs, only : r4
    real(r4), dimension(:), intent(in) :: a
    real(r4)                           :: ccminval_r4
   continue
    ccminval_r4 = minval(a)
  end function ccminval_r4

  pure function ccminval_r8(a)
    use kinddefs, only : r8
    real(r8), dimension(:), intent(in) :: a
    real(r8)                           :: ccminval_r8
   continue
    ccminval_r8 = minval(a)
  end function ccminval_r8

!================================== CCMAXVAL_* ===============================80
!
! Complex MAXVAL() for various argument types.
!
!=============================================================================80

  function ccmaxval_c8(a)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    complex(r8)                           :: ccmaxval_c8
    integer, dimension(1) :: max_location
   continue
    max_location = ccmaxloc(a)
    ccmaxval_c8 = a(max_location(1))
  end function ccmaxval_c8

  function ccmaxval_c8_2d(a)
    use kinddefs, only : r8
    complex(r8), dimension(:,:), intent(in) :: a
    complex(r8)                             :: ccmaxval_c8_2d
    integer, dimension(2) :: max_location
   continue
    max_location = ccmaxloc(a)
    ccmaxval_c8_2d = a(max_location(1),max_location(2))
  end function ccmaxval_c8_2d

  function ccmaxval_i1(a)
    use kinddefs, only : i1
    integer(i1), dimension(:), intent(in) :: a
    integer(i1)                           :: ccmaxval_i1
   continue
    ccmaxval_i1 = maxval(a)
  end function ccmaxval_i1

  function ccmaxval_i1_2d(a)
    use kinddefs, only : i1
    integer(i1), dimension(:,:), intent(in) :: a
    integer(i1)                             :: ccmaxval_i1_2d
   continue
    ccmaxval_i1_2d = maxval(a)
  end function ccmaxval_i1_2d

  function ccmaxval_i4(a)
    use kinddefs, only : i4
    integer(i4), dimension(:), intent(in) :: a
    integer(i4)                           :: ccmaxval_i4
   continue
    ccmaxval_i4 = maxval(a)
  end function ccmaxval_i4

  function ccmaxval_i4_2d(a)
    use kinddefs, only : i4
    integer(i4), dimension(:,:), intent(in) :: a
    integer(i4)                             :: ccmaxval_i4_2d
   continue
    ccmaxval_i4_2d = maxval(a)
  end function ccmaxval_i4_2d

  function ccmaxval_i8(a)
    use kinddefs, only : i8
    integer(i8), dimension(:), intent(in) :: a
    integer(i8)                           :: ccmaxval_i8
   continue
    ccmaxval_i8 = maxval(a)
  end function ccmaxval_i8

  function ccmaxval_i8_2d(a)
    use kinddefs, only : i8
    integer(i8), dimension(:,:), intent(in) :: a
    integer(i8)                             :: ccmaxval_i8_2d
   continue
    ccmaxval_i8_2d = maxval(a)
  end function ccmaxval_i8_2d

  function ccmaxval_r4(a)
    use kinddefs, only : r4
    real(r4), dimension(:), intent(in) :: a
    real(r4)                           :: ccmaxval_r4
   continue
    ccmaxval_r4 = maxval(a)
  end function ccmaxval_r4

  function ccmaxval_r8(a)
    use kinddefs, only : r8
    real(r8), dimension(:), intent(in) :: a
    real(r8)                           :: ccmaxval_r8
   continue
    ccmaxval_r8 = maxval(a)
  end function ccmaxval_r8

!================================== CCMINLOC_* ===============================80
!
! Complex MINLOC() for various argument types.
!
!=============================================================================80

  pure function ccminloc_c8(a, mask)
    use kinddefs, only : r8
    complex(r8), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    integer,     dimension(1)                             :: ccminloc_c8
   continue
    mask_conditional : if ( present(mask) ) then
      ccminloc_c8 = minloc( real( a, r8 ) , mask )
    else mask_conditional
      ccminloc_c8 = minloc( real( a, r8 ) )
    end if mask_conditional
  end function ccminloc_c8

  pure function ccminloc_c8_2d(a, mask)
    use kinddefs, only : r8
    complex(r8), dimension(:,:),                       intent(in) :: a
    logical, dimension(size(a,1),size(a,2)), optional, intent(in) :: mask
    integer, dimension(2)                                      :: ccminloc_c8_2d
   continue
    mask_conditional : if ( present(mask) ) then
      ccminloc_c8_2d = minloc( real( a, r8 ) , mask )
    else mask_conditional
      ccminloc_c8_2d = minloc( real( a, r8 ) )
    end if mask_conditional
  end function ccminloc_c8_2d

  pure function ccminloc_c8_dim(a, dim)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    integer,                   intent(in) :: dim
    integer                               :: ccminloc_c8_dim
   continue
    ccminloc_c8_dim = minloc( real( a, r8 ), dim )
  end function ccminloc_c8_dim

  pure function ccminloc_i4(a, mask)
    use kinddefs, only : i4
    integer(i4), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    integer,     dimension(1)                             :: ccminloc_i4
   continue
    mask_conditional : if ( present(mask) ) then
      ccminloc_i4 = minloc( a, mask )
    else mask_conditional
      ccminloc_i4 = minloc( a )
    end if mask_conditional
  end function ccminloc_i4

  pure function ccminloc_i4_dim(a, dim)
    use kinddefs, only : i4
    integer(i4), dimension(:), intent(in) :: a
    integer,                   intent(in) :: dim
    integer                               :: ccminloc_i4_dim
   continue
    ccminloc_i4_dim = minloc( a, dim )
  end function ccminloc_i4_dim

  pure function ccminloc_i8(a, mask)
    use kinddefs, only : i8
    integer(i8), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    integer,     dimension(1)                             :: ccminloc_i8
   continue
    mask_conditional : if ( present(mask) ) then
      ccminloc_i8 = minloc( a, mask )
    else mask_conditional
      ccminloc_i8 = minloc( a )
    end if mask_conditional
  end function ccminloc_i8

  pure function ccminloc_i8_dim(a, dim)
    use kinddefs, only : i8
    integer(i8), dimension(:), intent(in) :: a
    integer,                   intent(in) :: dim
    integer                               :: ccminloc_i8_dim
   continue
    ccminloc_i8_dim = minloc( a, dim )
  end function ccminloc_i8_dim

!================================== CCMAXLOC_* ===============================80
!
! Complex MAXLOC() for various argument types.
!
!=============================================================================80

  function ccmaxloc_c8(a, mask)
    use kinddefs, only : r8
    complex(r8), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    integer,     dimension(1)                             :: ccmaxloc_c8
   continue
    mask_conditional : if ( present(mask) ) then
      ccmaxloc_c8 = maxloc( real( a, r8 ) , mask )
    else mask_conditional
      ccmaxloc_c8 = maxloc( real( a, r8 ) )
    end if mask_conditional
  end function ccmaxloc_c8

  function ccmaxloc_c8_2d(a, mask)
    use kinddefs, only : r8
    complex(r8), dimension(:,:),                       intent(in) :: a
    logical, dimension(size(a,1),size(a,2)), optional, intent(in) :: mask
    integer, dimension(2)                                      :: ccmaxloc_c8_2d
   continue
    mask_conditional : if ( present(mask) ) then
      ccmaxloc_c8_2d = maxloc( real( a, r8 ) , mask )
    else mask_conditional
      ccmaxloc_c8_2d = maxloc( real( a, r8 ) )
    end if mask_conditional
  end function ccmaxloc_c8_2d

  function ccmaxloc_c8_dim(a, dim)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    integer,                   intent(in) :: dim
    integer                               :: ccmaxloc_c8_dim
   continue
    ccmaxloc_c8_dim = maxloc( real( a, r8 ), dim )
  end function ccmaxloc_c8_dim

  function ccmaxloc_i4(a, mask)
    use kinddefs, only : i4
    integer(i4), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    integer,     dimension(1)                             :: ccmaxloc_i4
   continue
    mask_conditional : if ( present(mask) ) then
      ccmaxloc_i4 = maxloc( a, mask )
    else mask_conditional
      ccmaxloc_i4 = maxloc( a )
    end if mask_conditional
  end function ccmaxloc_i4

  pure function ccmaxloc_i4_dim(a, dim)
    use kinddefs, only : i4
    integer(i4), dimension(:), intent(in) :: a
    integer,                   intent(in) :: dim
    integer                               :: ccmaxloc_i4_dim
   continue
    ccmaxloc_i4_dim = maxloc( a, dim )
  end function ccmaxloc_i4_dim

  function ccmaxloc_i8(a, mask)
    use kinddefs, only : i8
    integer(i8), dimension(:),                 intent(in) :: a
    logical,     dimension(size(a)), optional, intent(in) :: mask
    integer,     dimension(1)                             :: ccmaxloc_i8
   continue
    mask_conditional : if ( present(mask) ) then
      ccmaxloc_i8 = maxloc( a, mask )
    else mask_conditional
      ccmaxloc_i8 = maxloc( a )
    end if mask_conditional
  end function ccmaxloc_i8

  pure function ccmaxloc_i8_dim(a, dim)
    use kinddefs, only : i8
    integer(i8), dimension(:), intent(in) :: a
    integer,                   intent(in) :: dim
    integer                               :: ccmaxloc_i8_dim
   continue
    ccmaxloc_i8_dim = maxloc( a, dim )
  end function ccmaxloc_i8_dim

!================================== CCTAN_* ==================================80
!
! Complex TAN() for various argument types.
!
!
! *    Ctan(z) = ( sin(2*real) + jsinh(2*imag) )
! *              -------------------------------
! *              ( cos(2*real) + cosh(2*imag) )
! *    where z = real + j imag
!=============================================================================80

  pure function cctan_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: cctan_c8
    real(r8) :: real2, imag2, denom, realpiece, imagpiece
  continue
! from http://paulbourke.net/oldstuff/mathlib/ccl.c
    if ( aimag(a) < epsilon(1.0_r8) ) then
      cctan_c8 = cmplx(tan(real(a,r8)), 0.0_r8, r8)
    else
      real2 = 2.0_r8 * real(a,r8)
      imag2 = 2.0_r8 * aimag(a)
      denom = cos(real2) + cosh(imag2)
      realpiece = sin(real2) / denom
      imagpiece = sinh(imag2) / denom
      cctan_c8 = cmplx( realpiece, imagpiece, r8 )
    end if
  end function cctan_c8

  pure function cctan_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    real(r8)             :: cctan_r8
   continue
    cctan_r8 = tan(a)
  end function cctan_r8

!================================== CCACOS_* =================================80
!
! Complex ACOS() for various argument types.
!
!=============================================================================80

  pure function ccacos_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: ccacos_c8
    real(r8) :: x2, y2, xp1, xm1, part1, part2, apiece, bpiece
   continue
!   from http://astronomy.swin.edu.au/~pbourke/oldstuff/mathlib/ccl.c
    if ( aimag(a) < epsilon(1.0_r8) ) then
      ccacos_c8 = cmplx(acos(real(a,r8)), 0.0_r8, r8)
    else
      x2 = real(a,r8) * real(a,r8)
      y2 = aimag(a) * aimag(a)
      xp1 = x2 + 2.0_r8 * real(a,r8) + 1.0_r8
      xm1 = x2 - 2.0_r8 * real(a,r8) + 1.0_r8
      part1 = 0.5_r8 * sqrt(xp1 + y2)
      part2 = 0.5_r8 * sqrt(xm1 + y2)
      apiece = part1 + part2
      bpiece = part1 - part2
      ccacos_c8 = cmplx( acos(bpiece),                                         &
                         log(apiece+sqrt(apiece*apiece-1.0_r8)),               &
                         r8 )
    endif
  end function ccacos_c8

  pure function ccacos_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    real(r8)             :: ccacos_r8
   continue
    ccacos_r8 = acos(a)
  end function ccacos_r8

!================================== CCASIN_* =================================80
!
! Complex ASIN() for various argument types.
!
!=============================================================================80

! FIXME: not completely general - see ccacos_c8
  pure elemental &
  function ccasin_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: ccasin_c8
   continue
    ccasin_c8 = cmplx( asin(real(a, r8)),                                      &
                       aimag(a)/sqrt(1.0_r8 - real(a, r8)**2),                 &
                       r8 )
  end function ccasin_c8

  pure elemental &
  function ccasin_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    real(r8)             :: ccasin_r8
   continue
    ccasin_r8 = asin(a)
  end function ccasin_r8

!================================== CCATAN_* =================================80
!
! Complex ATAN() for various argument types.
!
!=============================================================================80

! FIXME: not completely general - see ccacos_c8
  function ccatan_c8c4(a)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a
    complex(r4)             :: ccatan_c8c4
   continue
    ccatan_c8c4 = cmplx( atan(real(a)), aimag(a)/(1.+real(a)**2), r4 )
  end function ccatan_c8c4

! FIXME: not completely general - see ccacos_c8
  function ccatan_c8c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: ccatan_c8c8
   continue
    ccatan_c8c8 = cmplx( atan(real(a)), aimag(a)/(1.+real(a)**2), r8 )
  end function ccatan_c8c8

  function ccatan_r8r4(a)
    use kinddefs, only : r4
    real(r4), intent(in) :: a
    real(r4)             :: ccatan_r8r4
   continue
    ccatan_r8r4 = atan(a)
  end function ccatan_r8r4

  function ccatan_r8r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    real(r8)             :: ccatan_r8r8
   continue
    ccatan_r8r8 = atan(a)
  end function ccatan_r8r8

!================================== CCATAN2_* ================================80
!
! Complex ATAN2() for various argument types.
!
!=============================================================================80

! FIXME: not completely general - see ccacos_c8
  pure function ccatan2_c8c8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a,b
    complex(r8) :: ccatan2_c8c8
   continue
    ccatan2_c8c8 = cmplx( atan2(real(a,r8),real(b,r8)),                        &
                          (real(b,r8)*aimag(a)-real(a,r8)*aimag(b))            &
                         /(real(a, r8)**2+real(b,r8)**2), r8)
  end function ccatan2_c8c8

  pure function ccatan2_r8r8(a,b)
    use kinddefs, only : r8
    real(r8), intent(in) :: a, b
    real(r8)             :: ccatan2_r8r8
   continue
    ccatan2_r8r8 = atan2(a,b)
  end function ccatan2_r8r8

!================================== CCCOSH_* =================================80
!
! Complex COSH() for various argument types.
!
!=============================================================================80

  function cccosh(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: cccosh
   continue
    cccosh = cmplx( cosh(real(a, r8)), aimag(a)*sinh(real(a, r8)), r8 )
  end function cccosh

!================================== CCSINH_* =================================80
!
! Complex SINH() for various argument types.
!
!=============================================================================80

  function ccsinh(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: ccsinh
   continue
    ccsinh = cmplx( sinh(real(a, r8)), aimag(a)*sinh(real(a, r8)), r8 )
  end function ccsinh

!================================== CCTANH_* =================================80
!
! Complex TANH() for various argument types.
!
!=============================================================================80

  pure function cctanh_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: cctanh_c8
    complex(r8) :: eplus, eminus
   continue
    if (real(a,r8) > 50.0_r8) then ! magic number
      cctanh_c8 = 1.0_r8
    else
      eplus  = exp(a)
      eminus = exp(-a)
      cctanh_c8 = (eplus - eminus)/(eplus + eminus)
    end if
  end function cctanh_c8

  pure function cctanh_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    complex(r8)          :: cctanh_r8
   continue
    cctanh_r8 = cmplx( tanh(a), 0.0_r8, r8 )
  end function cctanh_r8

!================================== CCLOG10_* ================================80
!
! Complex LOG10() from various argument types.
!
!=============================================================================80

  elemental function cclog10_c4(a)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a
    complex(r4)             :: cclog10_c4
   continue
    cclog10_c4 = log(a) / log((10.0_r4, 0.0_r4))
  end function cclog10_c4

  elemental function cclog10_c8(a)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    complex(r8)             :: cclog10_c8
   continue
    cclog10_c8 = log(a) / log((10.0_r8, 0.0_r8))
  end function cclog10_c8

  elemental function cclog10_r4(a)
    use kinddefs, only : r4
    real(r4), intent(in) :: a
    real(r4)             :: cclog10_r4
   continue
    cclog10_r4 = log10(a)
  end function cclog10_r4

  elemental function cclog10_r8(a)
    use kinddefs, only : r8
    real(r8), intent(in) :: a
    real(r8)             :: cclog10_r8
   continue
    cclog10_r8 = log10(a)
  end function cclog10_r8

!===================================== LT_* ==================================80
!
!  Complex OPERATOR(<) for various combinations.
!
!=============================================================================80

  pure function lt_c4c4(a,b)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a, b
    logical                 :: lt_c4c4
   continue
    lt_c4c4 = ( real(a,r4) < real(b,r4) )
  end function lt_c4c4

  pure function lt_c8c8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a, b
    logical                 :: lt_c8c8
   continue
    lt_c8c8 = ( real(a,r8) < real(b,r8) )
  end function lt_c8c8

  pure function lt_c8r8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: b
    complex(r8), intent(in) :: a
    logical                 :: lt_c8r8
   continue
    lt_c8r8 = ( real(a,r8) < b )
  end function lt_c8r8

  pure function lt_c8vc8(a,b)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    complex(r8),               intent(in) :: b
    logical, dimension(size(a))           :: lt_c8vc8
   continue
    lt_c8vc8(:) = ( real(a(:),r8) < real(b,r8) )
  end function lt_c8vc8

  pure function lt_r4c4(a,b)
    use kinddefs, only : r4
    real(r4),    intent(in) :: a
    complex(r4), intent(in) :: b
    logical                 :: lt_r4c4
   continue
    lt_r4c4 = ( a < real(b,r4) )
  end function lt_r4c4

  pure function lt_r4c8(a,b)
    use kinddefs, only : r4, r8
    real(r4),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: lt_r4c8
   continue
    lt_r4c8 = ( a < real(b,r4) )
  end function lt_r4c8

  pure function lt_r8c8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: lt_r8c8
   continue
    lt_r8c8 = ( a < real(b,r8) )
  end function lt_r8c8

!===================================== LE_* ==================================80
!
!  Complex OPERATOR(<=) for various combinations.
!
!=============================================================================80

  pure elemental function le_c4c4(a,b)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a, b
    logical                 :: le_c4c4
   continue
    le_c4c4 = ( real(a,r4) <= real(b,r4) )
  end function le_c4c4

  pure elemental function le_c8c8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a, b
    logical                 :: le_c8c8
   continue
    le_c8c8 = ( real(a,r8) <= real(b,r8) )
  end function le_c8c8

  pure elemental function le_c8r8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: b
    complex(r8), intent(in) :: a
    logical                 :: le_c8r8
   continue
    le_c8r8 = ( real(a,r8) <= b )
  end function le_c8r8

  pure function le_c8vc8(a,b)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    complex(r8),               intent(in) :: b
    logical, dimension(size(a))           :: le_c8vc8
   continue
    le_c8vc8(:) = ( real(a(:),r8) <= real(b,r8) )
  end function le_c8vc8

  pure elemental function le_r4c4(a,b)
    use kinddefs, only : r4
    real(r4),    intent(in) :: a
    complex(r4), intent(in) :: b
    logical                 :: le_r4c4
   continue
    le_r4c4 = ( a <= real(b,r4) )
  end function le_r4c4

  pure elemental function le_r8c8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: le_r8c8
   continue
    le_r8c8 = ( a <= real(b,r8) )
  end function le_r8c8

!===================================== GE_* ==================================80
!
!  Complex OPERATOR(>=) for various combinations.
!
!=============================================================================80

  pure elemental function ge_c4c4(a,b)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a, b
    logical                 :: ge_c4c4
   continue
    ge_c4c4 = ( real(a,r4) >= real(b,r4) )
  end function ge_c4c4

  pure elemental function ge_c8c8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a, b
    logical                 :: ge_c8c8
   continue
    ge_c8c8 = ( real(a,r8) >= real(b,r8) )
  end function ge_c8c8

  pure elemental function ge_c8i4(a,b)
    use kinddefs, only : r8, i4
    complex(r8), intent(in) :: a
    integer(i4), intent(in) :: b
    logical                 :: ge_c8i4
   continue
    ge_c8i4 = ( real(a,r8) >= b )
  end function ge_c8i4

  pure elemental function ge_c8i8(a,b)
    use kinddefs, only : r8, i8
    complex(r8), intent(in) :: a
    integer(i8), intent(in) :: b
    logical                 :: ge_c8i8
   continue
    ge_c8i8 = ( real(a,r8) >= b )
  end function ge_c8i8

  pure elemental function ge_c8r8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a
    real(r8),    intent(in) :: b
    logical                 :: ge_c8r8
   continue
    ge_c8r8 = ( real(a,r8) >= b )
  end function ge_c8r8

  pure function ge_c8vc8(a,b)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    complex(r8),               intent(in) :: b
    logical, dimension(size(a))           :: ge_c8vc8
   continue
    ge_c8vc8(:) = ( real(a(:),r8) >= real(b,r8) )
  end function ge_c8vc8

  pure elemental function ge_i4c8(a,b)
    use kinddefs, only : i4, r8
    integer(i4), intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: ge_i4c8
   continue
    ge_i4c8 = ( a >= real(b,r8) )
  end function ge_i4c8

  pure elemental function ge_i8c8(a,b)
    use kinddefs, only : i8, r8
    integer(i8), intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: ge_i8c8
   continue
    ge_i8c8 = ( a >= real(b,r8) )
  end function ge_i8c8

  pure elemental function ge_r4c8(a,b)
    use kinddefs, only : r8, r4
    real(r4),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: ge_r4c8
   continue
    ge_r4c8 = ( a >= real(b,r8) )
  end function ge_r4c8

  pure elemental function ge_r8c8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: ge_r8c8
   continue
    ge_r8c8 = ( a >= real(b,r8) )
  end function ge_r8c8

!===================================== GT_* ==================================80
!
!  Complex OPERATOR(>) for various combinations.
!
!=============================================================================80

  pure elemental function gt_c4c4(a,b)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a, b
    logical                 :: gt_c4c4
   continue
    gt_c4c4 = ( real(a,r4) > real(b,r4) )
  end function gt_c4c4

  pure elemental function gt_c4r4(a,b)
    use kinddefs, only : r4
    complex(r4), intent(in) :: a
    real(r4),    intent(in) :: b
    logical                 :: gt_c4r4
   continue
    gt_c4r4 = ( real(a,r4) > b )
  end function gt_c4r4

  pure elemental function gt_c8c8(a,b)
    use kinddefs, only : r8
    complex(r8), intent(in) :: a, b
    logical                 :: gt_c8c8
   continue
    gt_c8c8 = ( real(a,r8) > real(b,r8) )
  end function gt_c8c8

  pure elemental function gt_c8r8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: b
    complex(r8), intent(in) :: a
    logical                 :: gt_c8r8
   continue
    gt_c8r8 = ( real(a,r8) > b )
  end function gt_c8r8

  pure function gt_c8vc8(a,b)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    complex(r8),               intent(in) :: b
    logical, dimension(size(a))           :: gt_c8vc8
   continue
    gt_c8vc8(:) = ( real(a(:),r8) > real(b,r8) )
  end function gt_c8vc8

  pure function gt_c8vr8(a,b)
    use kinddefs, only : r8
    complex(r8), dimension(:), intent(in) :: a
    real(r8),                  intent(in) :: b
    logical, dimension(size(a))           :: gt_c8vr8
   continue
    gt_c8vr8(:) = ( real(a(:),r8) > b )
  end function gt_c8vr8

  pure elemental function gt_r4c4(a,b)
    use kinddefs, only : r4
    real(r4),    intent(in) :: a
    complex(r4), intent(in) :: b
    logical                 :: gt_r4c4
   continue
    gt_r4c4 = ( a > real(b,r4) )
  end function gt_r4c4

  pure elemental function gt_r4c8(a,b)
    use kinddefs, only : r4, r8
    real(r4),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: gt_r4c8
   continue
    gt_r4c8 = ( a > real(b,r4) )
  end function gt_r4c8

  pure elemental function gt_r8c8(a,b)
    use kinddefs, only : r8
    real(r8),    intent(in) :: a
    complex(r8), intent(in) :: b
    logical                 :: gt_r8c8
   continue
    gt_r8c8 = ( a > real(b,r8) )
  end function gt_r8c8

end module complex_functions

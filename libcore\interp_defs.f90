
! Defines some data structures for send/recv.

module interp_defs

  use kinddefs,        only : system_i1, system_r4, system_r8, system_i8

  implicit none

  private

  public :: sendrecv_type, sr, sr_allocated, dump_sr, dump_one_sr
  public :: nullify_sendrecv, basic_sendrecv_type
  public :: deallocate_sendrecv

  type sendrecv_type

  ! list of message lengths to other processors (in comp. row storage)

    integer                          :: size_sendindex, size_recvindex
    integer, dimension(:),   pointer :: sendproc => null()
    integer, dimension(:),   pointer :: recvproc => null()
    integer, dimension(:),   pointer :: sendindex => null()
    integer, dimension(:),   pointer :: recvindex => null()
    integer, dimension(:,:), pointer :: femsendindex => null()

    logical :: sendrecv_allocated = .FALSE.

  ! sendproc, recvproc, sendindex, recvindex are allocated in lmpi_readme
  ! and read from the gridpart files

  ! mpi work arrays for sending and recving data

    integer,         dimension(:,:), pointer :: integr_mat_recvdata => null()
    integer,         dimension(:,:), pointer :: integr_mat_senddata => null()
    logical                                  :: integr_mat_allocated = .FALSE.

    integer,         dimension(:),   pointer :: integr_vec_recvdata => null()
    integer,         dimension(:),   pointer :: integr_vec_senddata => null()
    logical                                  :: integr_vec_allocated = .FALSE.

    integer(system_i1),dimension(:,:), pointer :: integ1_mat_recvdata => null()
    integer(system_i1),dimension(:,:), pointer :: integ1_mat_senddata => null()
    logical                                    :: integ1_mat_allocated = .FALSE.

    integer(system_i8),dimension(:,:), pointer :: integ8_mat_recvdata => null()
    integer(system_i8),dimension(:,:), pointer :: integ8_mat_senddata => null()
    logical                                    :: integ8_mat_allocated = .FALSE.

    integer(system_i1),dimension(:),   pointer :: integ1_vec_recvdata => null()
    integer(system_i1),dimension(:),   pointer :: integ1_vec_senddata => null()
    logical                                  :: integ1_vec_allocated = .FALSE.

    integer(system_i8),dimension(:),   pointer :: integ8_vec_recvdata => null()
    integer(system_i8),dimension(:),   pointer :: integ8_vec_senddata => null()
    logical                                  :: integ8_vec_allocated = .FALSE.

    logical,         dimension(:,:), pointer :: logicl_mat_recvdata => null()
    logical,         dimension(:,:), pointer :: logicl_mat_senddata => null()
    logical                                  :: logicl_mat_allocated = .FALSE.

    logical,         dimension(:),   pointer :: logicl_vec_recvdata => null()
    logical,         dimension(:),   pointer :: logicl_vec_senddata => null()
    logical                                  :: logicl_vec_allocated = .FALSE.

    complex(system_r8), dimension(:,:), pointer :: cmpxr8_mat_recvdata => null()
    complex(system_r8), dimension(:,:), pointer :: cmpxr8_mat_senddata => null()
    logical                                  :: cmpxr8_mat_allocated = .FALSE.

    complex(system_r8), dimension(:),   pointer :: cmpxr8_vec_recvdata => null()
    complex(system_r8), dimension(:),   pointer :: cmpxr8_vec_senddata => null()
    logical                                  :: cmpxr8_vec_allocated = .FALSE.

    complex(system_r4), dimension(:,:), pointer :: cmpxr4_mat_recvdata => null()
    complex(system_r4), dimension(:,:), pointer :: cmpxr4_mat_senddata => null()
    logical                                  :: cmpxr4_mat_allocated = .FALSE.

    complex(system_r4), dimension(:),   pointer :: cmpxr4_vec_recvdata => null()
    complex(system_r4), dimension(:),   pointer :: cmpxr4_vec_senddata => null()
    logical                                  :: cmpxr4_vec_allocated = .FALSE.

    real(system_r8), dimension(:,:), pointer :: double_mat_recvdata => null()
    real(system_r8), dimension(:,:), pointer :: double_mat_senddata => null()
    logical                                  :: double_mat_allocated = .FALSE.

    real(system_r8), dimension(:),   pointer :: double_vec_recvdata => null()
    real(system_r8), dimension(:),   pointer :: double_vec_senddata => null()
    logical                                  :: double_vec_allocated = .FALSE.

    real(system_r4), dimension(:,:), pointer :: single_mat_recvdata => null()
    real(system_r4), dimension(:,:), pointer :: single_mat_senddata => null()
    logical                                  :: single_mat_allocated = .FALSE.

    real(system_r4), dimension(:),   pointer :: single_vec_recvdata => null()
    real(system_r4), dimension(:),   pointer :: single_vec_senddata => null()
    logical                                  :: single_vec_allocated = .FALSE.

  end type sendrecv_type

  logical :: sr_allocated = .FALSE.

  type (sendrecv_type), dimension(:,:), pointer :: sr

  type basic_sendrecv_type

!   list of message lengths to other processors (in comp. row storage)

    integer, dimension(:), pointer :: sendproc => null()
    integer, dimension(:), pointer :: recvproc => null()

!   list of global node indexes to mpi messages; indexed with sendproc,
!   recvproc
!   note: these are converted from global numbers
!         to local number during file write

    integer, dimension(:), pointer :: sendindex => null()
    integer, dimension(:), pointer :: recvindex => null()

  end type basic_sendrecv_type

! sr1 has the send and recv data pairs for level-1 nodes
! sr2 has the send and recv data pairs for level-2 nodes
! sre has the send and recv data pairs for edges
! src has the send and recv data pairs for cells (cell-centered)


contains

  subroutine nullify_sendrecv(sendrecv)

    type(sendrecv_type),  intent(inout) :: sendrecv

    continue

    nullify( sendrecv%integr_mat_recvdata )
    nullify( sendrecv%integr_mat_senddata )
    sendrecv%integr_mat_allocated = .false.

    nullify( sendrecv%integr_vec_recvdata )
    nullify( sendrecv%integr_vec_senddata )
    sendrecv%integr_vec_allocated = .false.

    nullify( sendrecv%logicl_mat_recvdata )
    nullify( sendrecv%logicl_mat_senddata )
    sendrecv%logicl_mat_allocated = .false.

    nullify( sendrecv%logicl_vec_recvdata )
    nullify( sendrecv%logicl_vec_senddata )
    sendrecv%logicl_vec_allocated = .false.

    nullify( sendrecv%cmpxr8_mat_recvdata )
    nullify( sendrecv%cmpxr8_mat_senddata )
    sendrecv%cmpxr8_mat_allocated = .false.

    nullify( sendrecv%cmpxr8_vec_recvdata )
    nullify( sendrecv%cmpxr8_vec_senddata )
    sendrecv%cmpxr8_vec_allocated = .false.

    nullify( sendrecv%cmpxr4_mat_recvdata )
    nullify( sendrecv%cmpxr4_mat_senddata )
    sendrecv%cmpxr4_mat_allocated = .false.

    nullify( sendrecv%cmpxr4_vec_recvdata )
    nullify( sendrecv%cmpxr4_vec_senddata )
    sendrecv%cmpxr4_vec_allocated = .false.

    nullify( sendrecv%double_mat_recvdata )
    nullify( sendrecv%double_mat_senddata )
    sendrecv%double_mat_allocated = .false.

    nullify( sendrecv%double_vec_recvdata )
    nullify( sendrecv%double_vec_senddata )
    sendrecv%double_vec_allocated = .false.

    nullify( sendrecv%single_mat_recvdata )
    nullify( sendrecv%single_mat_senddata )
    sendrecv%single_mat_allocated = .false.

    nullify( sendrecv%single_vec_recvdata )
    nullify( sendrecv%single_vec_senddata )
    sendrecv%single_vec_allocated = .false.

  end subroutine nullify_sendrecv

  subroutine deallocate_sendrecv(sr)

    type(sendrecv_type),  intent(inout) :: sr

    continue

    if ( associated(sr%sendproc) ) deallocate(sr%sendproc)
    sr%sendproc => null()
    if ( associated(sr%recvproc) ) deallocate(sr%recvproc)
    sr%recvproc => null()
    if ( associated(sr%sendindex) ) deallocate(sr%sendindex)
    sr%sendindex => null()
    if ( associated(sr%recvindex) ) deallocate(sr%recvindex)
    sr%recvindex => null()
    sr%sendrecv_allocated=.false.

    if ( associated(sr%integr_mat_recvdata) ) deallocate(sr%integr_mat_recvdata)
    sr%integr_mat_recvdata => null()
    if ( associated(sr%integr_mat_senddata) ) deallocate(sr%integr_mat_senddata)
    sr%integr_mat_senddata => null()
    sr%integr_mat_allocated = .false.

    if ( associated(sr%integr_vec_recvdata) ) deallocate(sr%integr_vec_recvdata)
    sr%integr_vec_recvdata => null()
    if ( associated(sr%integr_vec_senddata) ) deallocate(sr%integr_vec_senddata)
    sr%integr_vec_senddata => null()
    sr%integr_vec_allocated = .false.

    if ( associated(sr%integ8_mat_recvdata) ) deallocate(sr%integ8_mat_recvdata)
    sr%integ8_mat_recvdata => null()
    if ( associated(sr%integ8_mat_senddata) ) deallocate(sr%integ8_mat_senddata)
    sr%integ8_mat_senddata => null()
    sr%integ8_mat_allocated = .false.

    if ( associated(sr%logicl_mat_recvdata) ) deallocate(sr%logicl_mat_recvdata)
    sr%logicl_mat_recvdata => null()
    if ( associated(sr%logicl_mat_senddata) ) deallocate(sr%logicl_mat_senddata)
    sr%logicl_mat_senddata => null()
    sr%logicl_mat_allocated = .false.

    if ( associated(sr%logicl_vec_recvdata) ) deallocate(sr%logicl_vec_recvdata)
    sr%logicl_vec_recvdata => null()
    if ( associated(sr%logicl_vec_senddata) ) deallocate(sr%logicl_vec_senddata)
    sr%logicl_vec_senddata => null()
    sr%logicl_vec_allocated = .false.

    if ( associated(sr%cmpxr8_mat_recvdata) ) deallocate(sr%cmpxr8_mat_recvdata)
    sr%cmpxr8_mat_recvdata => null()
    if ( associated(sr%cmpxr8_mat_senddata) ) deallocate(sr%cmpxr8_mat_senddata)
    sr%cmpxr8_mat_senddata => null()
    sr%cmpxr8_mat_allocated = .false.

    if ( associated(sr%cmpxr8_vec_recvdata) ) deallocate(sr%cmpxr8_vec_recvdata)
    sr%cmpxr8_vec_recvdata => null()
    if ( associated(sr%cmpxr8_vec_senddata) ) deallocate(sr%cmpxr8_vec_senddata)
    sr%cmpxr8_vec_senddata => null()
    sr%cmpxr8_vec_allocated = .false.

    if ( associated(sr%cmpxr4_mat_recvdata) ) deallocate(sr%cmpxr4_mat_recvdata)
    sr%cmpxr4_mat_recvdata => null()
    if ( associated(sr%cmpxr4_mat_senddata) ) deallocate(sr%cmpxr4_mat_senddata)
    sr%cmpxr4_mat_senddata => null()
    sr%cmpxr4_mat_allocated = .false.

    if ( associated(sr%cmpxr4_mat_recvdata) ) deallocate(sr%cmpxr4_mat_recvdata)
    sr%cmpxr4_mat_recvdata => null()
    if ( associated(sr%cmpxr4_mat_senddata) ) deallocate(sr%cmpxr4_mat_senddata)
    sr%cmpxr4_mat_senddata => null()
    sr%cmpxr4_mat_allocated = .false.

    if ( associated(sr%double_mat_recvdata) ) deallocate(sr%double_mat_recvdata)
    sr%double_mat_recvdata => null()
    if ( associated(sr%double_mat_senddata) ) deallocate(sr%double_mat_senddata)
    sr%double_mat_senddata => null()
    sr%double_mat_allocated = .false.

    if ( associated(sr%double_vec_recvdata) ) deallocate(sr%double_vec_recvdata)
    sr%double_vec_recvdata => null()
    if ( associated(sr%double_vec_senddata) ) deallocate(sr%double_vec_senddata)
    sr%double_vec_senddata => null()
    sr%double_vec_allocated = .false.

    if ( associated(sr%single_mat_recvdata) ) deallocate(sr%single_mat_recvdata)
    sr%single_mat_recvdata => null()
    if ( associated(sr%single_mat_senddata) ) deallocate(sr%single_mat_senddata)
    sr%single_mat_senddata => null()
    sr%single_mat_allocated = .false.

    if ( associated(sr%single_vec_recvdata) ) deallocate(sr%single_vec_recvdata)
    sr%single_vec_recvdata => null()
    if ( associated(sr%single_vec_senddata) ) deallocate(sr%single_vec_senddata)
    sr%single_vec_senddata => null()
    sr%single_vec_allocated = .false.

  end subroutine deallocate_sendrecv

!============================== DUMP_SR ======================================80
!
! Driver to dump sr (send_recv)
!
!=============================================================================80

  subroutine dump_sr(io)
    integer, intent(in) :: io
    integer             :: i,j
  continue

    write(io,*) 'size(sr) ',size(sr,1),size(sr,2)

    do i = 1,size(sr,1)
       do j = 1,size(sr,2)
          write(io,*) '-------------- sr ',i,j
          call dump_one_sr(io,sr(i,j))
       end do
    end do
    write(io,*) '------------------'

 end subroutine dump_sr

!============================== DUMP_ONE_SR ==================================80
!
! Dump one sr (send_recv)
!
!=============================================================================80
 subroutine dump_one_sr (io,srx)

    integer,              intent(in) :: io
    type (sendrecv_type), intent(in) :: srx

  continue

    write(io,*) 'size_sendindex ',srx%size_sendindex
    write(io,*) 'size_recvindex ',srx%size_recvindex
    write(io,*) 's(sendproc)    ',size(srx%sendproc)
    write(io,*) 's(recvproc)    ',size(srx%recvproc)
    if (srx%size_sendindex > 0) then
       write(io,*) 's(si)          ',size(srx%sendindex)
       write(io,*) srx%sendindex
    end if
    if (srx%size_recvindex > 0) then
       write(io,*) 's(ri)          ',size(srx%recvindex)
       write(io,*) srx%recvindex
    end if
    write(io,*) 'sendrecv_allocated ',srx%sendrecv_allocated

    write(io,*) 'integr_mat_allocated ',srx%integr_mat_allocated
    if (srx%integr_mat_allocated) then
       write(io,*) 's(im rd) ',size(srx%integr_mat_recvdata,1),                &
                               size(srx%integr_mat_recvdata,2)
       write(io,*) 's(im sd) ',size(srx%integr_mat_senddata,1),                &
                               size(srx%integr_mat_senddata,2)
    end if

    write(io,*) 'integr_vec_allocated ',srx%integr_vec_allocated
    if (srx%integr_vec_allocated) then
       write(io,*) 's(iv rd) ',size(srx%integr_vec_recvdata,1)
       write(io,*) 's(iv sd) ',size(srx%integr_vec_senddata,1)
    end if

    write(io,*) 'integ8_mat_allocated ',srx%integ8_mat_allocated
    if (srx%integ8_mat_allocated) then
       write(io,*) 's(8m rd) ',size(srx%integ8_mat_recvdata,1),                &
                               size(srx%integ8_mat_recvdata,2)
       write(io,*) 's(8m sd) ',size(srx%integ8_mat_senddata,1),                &
                               size(srx%integ8_mat_senddata,2)
    end if

    write(io,*) 'integ8_vec_allocated ',srx%integ8_vec_allocated
    if (srx%integ8_vec_allocated) then
       write(io,*) 's(8v rd) ',size(srx%integ8_vec_recvdata,1)
       write(io,*) 's(8v sd) ',size(srx%integ8_vec_senddata,1)
    end if

    write(io,*) 'logicl_mat_allocated ',srx%logicl_mat_allocated
    if (srx%logicl_mat_allocated) then
       write(io,*) 's(lm rd) ',size(srx%logicl_mat_recvdata,1),                &
                               size(srx%logicl_mat_recvdata,2)
       write(io,*) 's(lm sd) ',size(srx%logicl_mat_senddata,1),                &
                               size(srx%logicl_mat_senddata,2)
    end if

    write(io,*) 'logicl_vec_allocated ',srx%logicl_vec_allocated
    if (srx%logicl_mat_allocated) then
       write(io,*) 's(lv rd) ',size(srx%logicl_vec_recvdata,1)
       write(io,*) 's(lv sd) ',size(srx%logicl_vec_senddata,1)
    end if

    write(io,*) 'complex_allocated ',      srx%cmpxr8_mat_allocated,           &
                 srx%cmpxr8_vec_allocated, srx%cmpxr4_mat_allocated,           &
                 srx%cmpxr4_vec_allocated, srx%double_mat_allocated,           &
                 srx%double_vec_allocated, srx%single_mat_allocated,           &
                 srx%single_vec_allocated

  end subroutine dump_one_sr

end module interp_defs

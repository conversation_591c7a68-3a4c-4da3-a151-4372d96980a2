module turb_2eqn

  use kinddefs,       only : dp

  implicit none

  private

  public :: turb_resid_2eqn
  public :: turb_jacob_2eqn

  public :: bc_2eqn_set_walls

  real(dp), dimension(:), allocatable :: ds11dt
  real(dp), dimension(:), allocatable :: ds12dt
  real(dp), dimension(:), allocatable :: ds13dt
  real(dp), dimension(:), allocatable :: ds22dt
  real(dp), dimension(:), allocatable :: ds23dt
  real(dp), dimension(:), allocatable :: ds33dt
  real(dp), dimension(:), allocatable :: u_double_prime
  logical :: u_double_prime_allocated = .false.

  logical :: sderivs_allocated = .false.

contains

!============================== TURB_RESID_2EQN ==============================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for 2-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid_2eqn ( turbulence_model_int,                           &
                               eqn_set, nnodes0, nnodes01,                     &
                       nedgeloc, eptr, turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,       &
                       nnodes0_2d, node_pairs_2d, iflagslen,            n_turb,&
                       n_tot, n_grd, nelem, elem,                              &
                       nbound, bc, dxdt, dydt, dzdt, rhotauij                  &
                     , sst_f1, sst_f2 )

    use bc_types,           only : bcgrid_type
    use element_types,      only : elem_type
    use kinddefs,           only : dp
    use turb_util,          only : strain_tensor_deriv
    use turbulence_info,    only : wilcox_kw88, sst, sst_kkl,                  &
                                   hellsten, smirnov, easmcc
    use turb_wilcox_kw88,   only : source_resid_wilcox_kw88
    use turb_sst,           only : source_resid_sst
    use turb_sst_kkl,       only : source_resid_sst_kkl
    use turb_kw_const,      only : curvature_model_int

    use turb_2eqn_routines, only : turb_resid_2eqn_routines
    use turb_util,       only : velocity_gradient_derivative

    integer,                              intent(in)    :: turbulence_model_int
    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nelem
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    integer,                              intent(in)    :: n_grd
    integer,                              intent(in)    :: nedgeloc
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nedgeloc_2d
    integer,                              intent(in)    :: nnodes0_2d
    integer,                              intent(in)    :: nbound

    integer,  dimension(nnodes01),        intent(in)    :: iflagslen
    integer,  dimension(2,nedgeloc),      intent(in)    :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d

    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z, vol
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    type(elem_type), dimension(nelem),    intent(in)    :: elem
    type(bcgrid_type),dimension(nbound),  intent(in)    :: bc
    real(dp), dimension(nnodes01),        intent(in)    :: dxdt
    real(dp), dimension(nnodes01),        intent(in)    :: dydt
    real(dp), dimension(nnodes01),        intent(in)    :: dzdt
    real(dp), dimension(6,nnodes01),      intent(out)   :: rhotauij
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f2
!   real(dp), dimension(nnodes0),         intent(inout) :: crossd

    real(dp), parameter                                 :: zero = 0.0_dp

  continue

    if ( .not. sderivs_allocated ) then
      select case ( curvature_model_int )
      case ( hellsten, smirnov, easmcc )
        allocate(ds11dt(nnodes01))
        allocate(ds12dt(nnodes01))
        allocate(ds13dt(nnodes01))
        allocate(ds22dt(nnodes01))
        allocate(ds23dt(nnodes01))
        allocate(ds33dt(nnodes01))
        sderivs_allocated = .true.
      case default
        allocate(ds11dt(1))
        allocate(ds12dt(1))
        allocate(ds13dt(1))
        allocate(ds22dt(1))
        allocate(ds23dt(1))
        allocate(ds33dt(1))
        sderivs_allocated = .true.
      end select
    end if

    ds11dt = zero
    ds12dt = zero
    ds13dt = zero
    ds22dt = zero
    ds23dt = zero
    ds33dt = zero
!-----------------------------------------------------------------------------80
!                        D S_{ij} / D t
!-----------------------------------------------------------------------------80
    select case ( curvature_model_int )
    case ( hellsten, smirnov, easmcc )
      call strain_tensor_deriv(eqn_set,                                        &
                   nnodes0, nnodes01, nedgeloc, eptr, qnode,                   &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,        &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,                 &
                   ds11dt, ds12dt, ds13dt, ds22dt, ds23dt, ds33dt)

    end select
!-----------------------------------------------------------------------------80
!                    turbulence models
!-----------------------------------------------------------------------------80

    if ( .not. u_double_prime_allocated ) then
      if ( turbulence_model_int == sst_kkl) then
        allocate(u_double_prime(nnodes01))
        u_double_prime_allocated = .true.
      else
        allocate(u_double_prime(1))
        u_double_prime_allocated = .true.
      endif
    endif
    u_double_prime = zero
    if ( turbulence_model_int == wilcox_kw88 ) then

      call source_resid_wilcox_kw88  (eqn_set, nnodes0, nnodes01,              &
                       turb, qnode, res, slen, gradx, grady,                   &
                       gradz, vol, x, y, z,                                    &
                       nnodes0_2d, node_pairs_2d, iflagslen, n_turb,           &
                       n_tot, n_grd, rhotauij )

    else if ( turbulence_model_int == sst ) then

      call source_resid_sst  (eqn_set, nnodes0, nnodes01,                      &
                                       turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, nnodes0_2d, node_pairs_2d, iflagslen,       &
                       n_turb, n_tot, n_grd, rhotauij,                         &
                       ds11dt, ds12dt, ds13dt, ds22dt, ds23dt, ds33dt,         &
                       sst_f1 )

    else if ( turbulence_model_int == sst_kkl ) then
      call velocity_gradient_derivative(eqn_set, nnodes0, nnodes01, nedgeloc,  &
                   eptr, x, y, z, gradx, grady, gradz, xn, yn, zn, ra,         &
                   vol, nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,   &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc, u_double_prime )

      call source_resid_sst_kkl (eqn_set, nnodes0, nnodes01,                   &
                                       turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, nnodes0_2d, node_pairs_2d, iflagslen,       &
                       n_turb, n_tot, n_grd, rhotauij,                         &
                       u_double_prime )

    else

    call turb_resid_2eqn_routines  (eqn_set, nnodes0, nnodes01,                &
                       nedgeloc, eptr, turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,       &
                       nnodes0_2d, node_pairs_2d, iflagslen,            n_turb,&
                       n_tot, n_grd, nelem, elem,                              &
                       nbound, bc, dxdt, dydt, dzdt, rhotauij,                 &
                       ds11dt, ds12dt, ds13dt, ds22dt, ds23dt, ds33dt,         &
                       sst_f1, sst_f2 )

    end if

  end subroutine turb_resid_2eqn

!============================== TURB_JACOB_2EQN ==============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for 2-eqn models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob_2eqn (turbulence_model_int, eqn_set, nnodes0, nnodes01,&
                        turb, qnode, iflagslen, slen, gradx, grady, gradz,     &
                        vol, a_diag, nnodes0_2d, node_pairs_2d, n_turb,        &
                        n_tot, n_grd, g2m, sst_f1, sst_f2,                     &
                        crossd, rhotauij )

    use info_depr,          only : twod
    use kinddefs,           only : dp
    use turbulence_info,    only : wilcox_kw88, sst, sst_kkl
    use turb_wilcox_kw88,   only : source_jacob_wilcox_kw88
    use turb_sst,           only : source_jacob_sst
    use turb_sst_kkl,       only : source_jacob_sst_kkl

    use turb_2eqn_routines, only : turb_jacob_2eqn_routines

    integer,                                 intent(in) :: turbulence_model_int
    integer,                                 intent(in) :: eqn_set
    integer,                                 intent(in) :: n_tot, n_grd
    integer,                                 intent(in) :: n_turb
    integer,                                 intent(in) :: nnodes0_2d
    integer,                                 intent(in) :: nnodes0
    integer,                                 intent(in) :: nnodes01

    integer, dimension(2,nnodes0_2d),        intent(in) :: node_pairs_2d
    integer, dimension(:),                   intent(in) :: g2m
    integer, dimension(nnodes01),            intent(in)    :: iflagslen

    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: vol
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f2
    real(dp),  dimension(nnodes0),               intent(inout) :: crossd
    real(dp),  dimension(6,nnodes0),             intent(in)    :: rhotauij

    integer :: node_src_eval

  continue

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    if ( turbulence_model_int == wilcox_kw88 ) then
      call source_jacob_wilcox_kw88( nnodes01, nnodes0, nnodes0_2d,            &
                                      node_src_eval,                           &
                                      node_pairs_2d, n_tot, n_turb, g2m, vol,  &
                                      qnode, turb, iflagslen, a_diag )

    else if ( turbulence_model_int == sst ) then
      call source_jacob_sst (nnodes0, nnodes01, node_src_eval,                 &
                          turb,        iflagslen,                              &
                          vol, a_diag, nnodes0_2d, node_pairs_2d, n_turb,      &
                          g2m, sst_f1 )

    else if ( turbulence_model_int == sst_kkl ) then
      call source_jacob_sst_kkl (nnodes0, nnodes01, node_src_eval,             &
                          turb,        iflagslen,                              &
                          vol, a_diag, nnodes0_2d, node_pairs_2d, n_turb,      &
                          g2m )

    else

    call turb_jacob_2eqn_routines (eqn_set, nnodes0, nnodes01,                 &
                        turb, qnode, iflagslen, slen, gradx, grady, gradz,     &
                        vol, a_diag, nnodes0_2d, node_pairs_2d, n_turb,        &
                        n_tot, n_grd, g2m, sst_f1, sst_f2,                     &
                        crossd, rhotauij )

    end if

  end subroutine turb_jacob_2eqn

!============================ BC_2EQN_SET_WALLS ==============================80
!
!  Sets quantities on viscous walls for kw-SST
!
!=============================================================================80

  subroutine bc_2eqn_set_walls( turbulence_model_int,                          &
                         eqn_set, nnodes0, nnodes01, nbnode, ibnode,           &
                         slen_wall, gradn_sqrtk, turb, qnode,                  &
                         n_turb, n_tot, ibc, k_wf, omega_wf, mu_t_wf )

    use kinddefs,           only : dp
    use solution_types,     only : compressible, incompressible
    use lmpi,               only : lmpi_conditional_stop
    use turb_2eqn_routines, only : wall_turbulence_kw_sst
    use turb_sst,           only : bc_wall_sst
    use turb_wilcox_kw88,   only : bc_wall_wilcox_kw88
!   use turb_sst_kkl,       only : bc_wall_sst_kkl
    use turbulence_info,    only : wilcox_kw88, sst

    integer, intent(in) :: nbnode, n_turb, n_tot, eqn_set
    integer, intent(in) :: nnodes0, nnodes01, ibc

    integer,                        intent(in)    :: turbulence_model_int
    integer,     dimension(nbnode),         intent(in)    :: ibnode
    real(dp),    dimension(nbnode),         intent(in)    :: slen_wall
    real(dp),    dimension(nbnode),         intent(in)    :: gradn_sqrtk
    real(dp),    dimension(nbnode),         intent(in)    :: k_wf
    real(dp),    dimension(nbnode),         intent(in)    :: omega_wf
    real(dp),    dimension(nbnode),         intent(in)    :: mu_t_wf

    real(dp), dimension(n_turb,nnodes01),intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode

    integer :: i,inode

    real(dp), dimension(n_tot)  :: qlocal
    real(dp), dimension(n_turb) :: turb_wall

    continue

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'bc_2eqn_set_walls: only in/comprss pg')
    end if

    if ( turbulence_model_int == sst ) then

      call bc_wall_sst( eqn_set, nnodes0, nnodes01, nbnode, ibnode,            &
                        slen_wall, turb, qnode,                                &
                        n_turb, n_tot, ibc, k_wf, omega_wf, mu_t_wf )

    else if ( turbulence_model_int == wilcox_kw88 ) then

      call bc_wall_wilcox_kw88( eqn_set, nnodes0, nnodes01, nbnode, ibnode,    &
                                slen_wall, turb, qnode,                        &
                                n_turb, n_tot, ibc, k_wf, omega_wf, mu_t_wf )


    else

      do i = 1,nbnode
        inode = ibnode(i)
        if (inode <= nnodes0) then

          qlocal = qnode(1:n_tot,inode)

          call wall_turbulence_kw_sst( eqn_set, ibc, slen_wall(i), qlocal      &
                                     , gradn_sqrtk(i), turb_wall               &
                                     , k_wf(i), omega_wf(i), mu_t_wf(i) )
          turb(:,inode) = turb_wall(:)

        end if
      end do

    end if

  end subroutine bc_2eqn_set_walls

end module turb_2eqn

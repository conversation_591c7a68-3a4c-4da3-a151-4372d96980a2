module dots

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs, only : dp

  implicit none

  private

  public :: dot_driver

  real(dp), dimension(:,:), pointer :: last_flow_dvs

  real(dp), dimension(:), pointer :: c, d  ! Scaling coefficients

contains

!================================= DOT_DRIVER ================================80
!
!  Performs a multipoint optimization using DOT
!
!=============================================================================80
  subroutine dot_driver(max_design_cycles,tau_subproblem,what_to_do,           &
                        restart_optimization,dot_method,feas_tol_val,io,       &
                        ammo_directory,lss_flag)

    use allocations,       only : my_alloc_ptr
    use kinddefs,          only : dp
    use lmpi,              only : lmpi_die
    use analysis,          only : perform_analysis
    use sensitivity,       only : perform_sensitivity_analysis
    use design_types,      only : opt_data_type
    use designs,           only : load_optimization_data, been_there,          &
                                  unload_optimization_data,                    &
                                  free_optimization_data
    use system_extensions, only : se_flush, se_chdir
    use file_utils,        only : available_unit, rm
    use utilities,         only : check_for_stop
    use nml_design,        only : n_design_pts, model_variables

    integer, intent(in) :: max_design_cycles, what_to_do, dot_method, io

    logical, intent(in) :: lss_flag, restart_optimization

    real(dp), intent(in) :: tau_subproblem, feas_tol_val

    character(len=*), intent(in) :: ammo_directory

    integer :: i, f1, f2, dot_info, dot_ndv, dot_ncon
    integer :: dot_maxint, entry
    integer :: counter, j, k, n, istop

#ifdef HAVE_DOT
    integer :: dot_ierr, dot_nstore, dot_iprint, dot_minmax, dot_nriwd
    integer :: dot_ngmax, dot_nriwk, dot_nrwkmn, dot_nrwkmx, dot_nrwk
    integer :: dot_ndscrt
#endif

    integer, dimension(:), pointer :: dot_iprm, dot_iwk, dot_idiscr

    real(dp) :: dot_obj

    real(dp), dimension(:), pointer :: dot_x, dot_xl, dot_xu, dot_g
    real(dp), dimension(:), pointer :: dot_rprm, dot_wk, dot_discrt

    type(opt_data_type), dimension(n_design_pts) :: opt_data

  continue

    if ( .false. ) write(*,*) dot_method  ! silence compiler warning

! Load the optimization data for each model

    do i = 1, n_design_pts
      call se_chdir(model_variables(i)%model_directory)
      opt_data(i)%allocated = .false.
      call load_optimization_data(opt_data(i),io,'DOT: Location 1')
      opt_data(i)%scale = 1.0_dp
    end do

! Set some basic DOT constants

    dot_info = 0
#ifdef HAVE_DOT
    dot_iprint = 7
    dot_minmax = 0   ! find the min of the objective
#endif
    dot_ndv = opt_data(1)%ndv

    dot_ncon = 0
    do i = 1, n_design_pts
      dot_ncon = dot_ncon + opt_data(i)%nconstraints
    end do

! Load the design variable bounds into the PORT-style array
! using the values from the first model.  We assume the
! parameterization, bounds, active DV's, etc are the same
! across all models

    call my_alloc_ptr(dot_x,  dot_ndv)
    call my_alloc_ptr(dot_xl, dot_ndv)
    call my_alloc_ptr(dot_xu, dot_ndv)
    dot_x  = opt_data(1)%design_variables
    dot_xl = opt_data(1)%lower_bounds
    dot_xu = opt_data(1)%upper_bounds

! DOT constraint vector

    call my_alloc_ptr(dot_g, max(1,dot_ncon))

! Allocate DOT work arrays

    call my_alloc_ptr(dot_rprm, 20)
    dot_rprm = 0.0_dp

    call my_alloc_ptr(dot_iprm, 20)
    dot_iprm = 0

! Set feasibility tolerances on constraints

    dot_rprm(1) = -abs(feas_tol_val)
    dot_rprm(2) = 0.0_dp

! Set the relative function convergence criteria for DOT

    dot_rprm(4)  = tau_subproblem
    dot_rprm(13) = tau_subproblem

! we will provide gradients

    dot_iprm(1) = 1

! max number of design cycles

    dot_iprm(3) = max_design_cycles
    dot_iprm(8) = max_design_cycles

! file unit for DOT output

    f1 = available_unit()
#ifdef HAVE_DOT
    call dot700(f1,'dot.output')
#endif
    dot_iprm(5) = f1

! allow all constraint gradients to be calculated

    dot_iprm(7) = dot_ncon

! allow printing from SLP/SQP subproblems

    dot_iprm(10) = 1

! show me the scaling factors

    dot_iprm(11) = 1

! file unit for DOT iteration history

    f2 = available_unit()
#ifdef HAVE_DOT
    call dot700(f2,'dot.history')
#endif
    dot_iprm(13) = f2

! Get storage space requirements for DOT

#ifdef HAVE_DOT
    dot_ndscrt = 0
#endif

    dot_maxint = huge(dot_maxint)

#ifdef HAVE_DOT
    call dotstr(dot_ndv, dot_ncon, dot_method, dot_nrwk, dot_nrwkmn, dot_nriwd,&
                dot_nrwkmx, dot_nriwk, dot_nstore, dot_ngmax, dot_xl, dot_xu,  &
                dot_maxint, dot_ndscrt, dot_ierr)
    if ( dot_ierr == -1 ) then
      write(*,*) 'Error: DOTSTR reports too little storage.'
      write(*,*) 'dot_nrwkmn = ', dot_nrwkmn
      write(*,*) 'dot_nriwd  = ', dot_nriwd
      write(*,*) 'dot_nrwkmx = ', dot_nrwkmx
      write(*,*) 'dot_nriwk  = ', dot_nriwk
      write(*,*) 'dot_ngmax  = ', dot_ngmax
      call lmpi_die
    else if ( dot_ierr == 1 ) then
      write(*,*) 'Error: DOTSTR reports too much storage required.'
      call lmpi_die
    endif
#else
    write(*,*) 'You do not have DOT installed.'
    call lmpi_die
#endif

#ifdef HAVE_DOT
    dot_nrwk = dot_nrwkmx ! take worst-case scenario
#endif

! Allocate some more DOT work arrays

#ifdef HAVE_DOT
    call my_alloc_ptr(dot_wk,  dot_nrwk)
    call my_alloc_ptr(dot_iwk, dot_nriwk)
#else
    call my_alloc_ptr(dot_wk,  1)
    call my_alloc_ptr(dot_iwk, 1)
#endif

    call my_alloc_ptr(dot_idiscr, 2*dot_ndv); dot_idiscr = 0
    call my_alloc_ptr(dot_discrt, 1);         dot_discrt = 0.0_dp

! Initialize the arrays to store the previous set of DV's for each model

    call my_alloc_ptr(last_flow_dvs, n_design_pts, dot_ndv)

! Allocate routine sets things to zero, so we ought
! to set it to something crazy, since this could
! very well be our starting design point

    last_flow_dvs = huge(1.0_dp)

! Call PORT

100 continue

    call se_chdir(ammo_directory)
    call check_for_stop(istop)
    if ( istop > 0 ) then
      call rm('stop.dat')
      write(*,*) 'User requested premature stop...'
      stop
    endif

    call scale_problem(dot_ndv,dot_ncon,dot_xl,dot_xu,dot_x,dot_wk,dot_iprm)
#ifdef HAVE_DOT
    call alldot(dot_info, dot_method, dot_iprint, dot_ndv, dot_ncon, dot_x,    &
                dot_xl, dot_xu, dot_obj, dot_minmax, dot_g, dot_rprm, dot_iprm,&
                dot_wk, dot_nrwk, dot_iwk, dot_nriwk, dot_discrt, dot_idiscr)
#else
    write(*,*) 'You do not have DOT installed.'
    call lmpi_die
#endif

    call unscale_problem(dot_ndv,dot_ncon,dot_xl,dot_xu,dot_x,dot_wk,dot_iprm)

! Flush the DOT output

    call se_flush(f1)
    call se_flush(f2)

! Write latest optimization data - this assumes that the only
! outbound variable from the PORT call is the design variable
! vector

    do i = 1, n_design_pts
      opt_data(i)%design_variables = dot_x
      call se_chdir(model_variables(i)%model_directory)
      call unload_optimization_data(opt_data(i),io,'After returning from DOT')
    end do

! Based on the info returned by DOT, either get function, gradient, or exit

    select case ( dot_info )
    case (0)

      call interpret_dot_output(dot_iprm(18))
      goto 200

    case (1)

      do i = 1, n_design_pts
        if ( .not. been_there(dot_ndv,dot_x,last_flow_dvs(i,:),.false.) ) then
          call se_chdir(model_variables(i)%model_directory)
          call perform_analysis(model_variables(i)%restart_flow,               &
                                i,n_design_pts,what_to_do,restart_optimization,&
                                model_variables(i)%desc_directory)
          call load_optimization_data(opt_data(i),io,'DOT: Location 2')
          last_flow_dvs(i,:) = dot_x
        endif
      end do

! Combine single point functions into a composite function

      call combine_mp_functions(dot_obj,opt_data)

! Set any constraint values before heading back into DOT

      counter = 0
      do i = 1, n_design_pts
        if ( opt_data(i)%nconstraints > 0 ) then
          do j = 1, opt_data(i)%nconstraints
            counter = counter + 1
            dot_g(counter) = opt_data(i)%constraints(j)
          end do
        endif
      end do

    case (2)

      do i = 1, n_design_pts

        call se_chdir(model_variables(i)%model_directory)

        if ( .not. been_there(dot_ndv,dot_x, last_flow_dvs(i,:),.true.) ) then
          write(*,*) 'WARNING: Sensitivity analysis requested at design point'
          write(*,*) 'different from previous function analysis!'
          call perform_analysis(model_variables(i)%restart_flow,               &
                                i,n_design_pts,what_to_do,restart_optimization,&
                                model_variables(i)%desc_directory)
          call load_optimization_data(opt_data(i),io,'DOT: Location 3')
          last_flow_dvs(i,:) = dot_x
        endif

        call perform_sensitivity_analysis(model_variables(i)%restart_dual,     &
                                          i,n_design_pts,what_to_do,lss_flag)
        call load_optimization_data(opt_data(i),io,'DOT: Location 4')
      end do

! Combine single point gradients into a composite gradient

      call combine_mp_gradients(dot_ndv,dot_wk,opt_data)

! Set any constraint gradients before heading back into DOT

      if ( dot_iprm(20) > 0 .and. dot_ncon > 0 ) then

        counter = 0
        entry   = dot_ndv

        do i = 1, n_design_pts
          if ( opt_data(i)%nconstraints > 0 ) then
            do j = 1, opt_data(i)%nconstraints
              counter = counter + 1
              do k = 1, dot_iprm(20)
                if ( dot_iwk(k) == counter ) then
                  do n = 1, opt_data(i)%ndv
                    entry = entry + 1
                    dot_wk(entry) = opt_data(i)%constraint_gradients(j,n)
                  end do
                endif
              end do
            end do
          endif
        end do

      endif

    case default

      write(*,*) 'Unknown flag received from DOT: ', dot_info
      goto 200

    end select

! Return to DOT

    goto 100

200 continue

! Close the DOT output file and deallocate its memory

#ifdef HAVE_DOT
    call dot701(f1)
    call dot701(f2)
#endif

    deallocate(dot_x, dot_xl, dot_xu, dot_g, dot_rprm, dot_iprm)
    deallocate(dot_wk, dot_iwk, last_flow_dvs)

! Free the memory used by the optimization data derived type

    do i = 1, n_design_pts
      call free_optimization_data(opt_data(i))
    end do

  end subroutine dot_driver


!============================== INTERPRET_DOT_OUTPUT =========================80
!
!  Interprets the flag returned from DOT after an optimization
!
!=============================================================================80

  subroutine interpret_dot_output(dot_flag)

    use system_extensions, only : se_flush

    integer, intent(in) :: dot_flag

  continue

    write(*,*) 'DOT has returned the following flag: ',dot_flag

    select case ( dot_flag )
    case (0)
      write(*,*) 'DOT: Normal termination.'
    case (1)
      write(*,*) 'DOT: Insufficient memory for WK or IWK.'
    case (2)
      write(*,*) 'DOT: Some XL(i) > XU(i).'
    case (3:4)
      write(*,*) 'DOT: Storage for constraint gradients too small.'
    case (5)
      write(*,*) 'DOT: Violated constraint has zero gradient: no feasible point'
      write(*,*) 'can be found.'
    case default
      write(*,*) 'Nothing seems to be known about this DOT flag...', dot_flag
    end select

    call se_flush(6)

  end subroutine interpret_dot_output


!============================== COMBINE_MP_FUNCTIONS =========================80
!
!  Combines objective functions from single design points into a
!  weighted multipoint objective function
!
!=============================================================================80
  subroutine combine_mp_functions(dot_objectives,opt_data)

    use kinddefs,     only : dp
    use design_types, only : opt_data_type, ks_function, ks_rho
    use nml_design,   only : model_variables, n_design_pts

    real(dp), intent(out) :: dot_objectives

    type(opt_data_type), dimension(n_design_pts), intent(in) :: opt_data

    integer :: i

    real(dp) :: obj_max, ks_sum

  continue

    dot_objectives = 0.0_dp

    if ( ks_function ) then

! First find the maximum objective function at the current design point

      obj_max = -huge(1.0_dp)
      do i = 1, n_design_pts
        obj_max = max(obj_max,opt_data(i)%objectives(1))
      end do

! Now form the contributions to the composite KS function

      ks_sum = 0.0_dp
      do i = 1, n_design_pts
        ks_sum = ks_sum + exp(ks_rho*(opt_data(i)%objectives(1) - obj_max))
      end do

! Turn this into the final KS function

      dot_objectives = obj_max + 1.0_dp/ks_rho*log(ks_sum)

      write(*,*) 'KS Function: obj_max, ks_sum, dot_objectives = ',            &
                 obj_max, ks_sum, dot_objectives

    else

      do i = 1, n_design_pts
        dot_objectives = dot_objectives +                                      &
                          model_variables(i)%weight * opt_data(i)%objectives(1)
      end do

    endif

  end subroutine combine_mp_functions


!============================== COMBINE_MP_GRADIENTS =========================80
!
!  Combines gradients from single design points into a weighted multipoint
!  gradient
!
!=============================================================================80
  subroutine combine_mp_gradients(ndv,dot_wk,opt_data)

    use kinddefs,     only : dp
    use design_types, only : opt_data_type, ks_function, ks_rho
    use nml_design,   only : model_variables, n_design_pts

    integer, intent(in) :: ndv

    real(dp), dimension(:), intent(inout) :: dot_wk

    type(opt_data_type), dimension(n_design_pts), intent(in) :: opt_data

    integer :: i, j

    real(dp) :: obj_max, num_sum, den_sum

  continue

    dot_wk(1:ndv) = 0.0_dp

    if ( ks_function ) then

! First find the maximum objective function at the current design point

      obj_max = -huge(1.0_dp)
      do i = 1, n_design_pts
        obj_max = max(obj_max,opt_data(i)%objectives(1))
      end do

! Form the sums for the numerator and denominator

      do i = 1, opt_data(1)%ndv
        num_sum = 0.0_dp
        den_sum = 0.0_dp
        do j = 1, n_design_pts
          num_sum = num_sum + opt_data(j)%gradients(1,i) *                     &
                               exp(ks_rho*(opt_data(j)%objectives(1) - obj_max))
          den_sum = den_sum + exp(ks_rho*(opt_data(j)%objectives(1) - obj_max))
        end do
        dot_wk(i) = num_sum / den_sum
      end do

      write(*,*) 'KS Function: obj_max, dot_wk = ', obj_max, dot_wk(1:ndv)

    else

      do i = 1, n_design_pts
        dot_wk(1:ndv) = dot_wk(1:ndv) +                                        &
                        model_variables(i)%weight*opt_data(i)%gradients(1,1:ndv)
      end do

    endif

  end subroutine combine_mp_gradients


!============================== SCALE_PROBLEM ================================80
!
!  Scales problem for PORT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine scale_problem(n,ncon,xl,xu,x,wk,iprm)

    use allocations, only : my_alloc_ptr

    integer, intent(in) :: n, ncon

    integer, dimension(:), intent(in) :: iprm

    real(dp), dimension(n), intent(inout) :: xl, xu
    real(dp), dimension(n), intent(inout) :: x
    real(dp), dimension(:), intent(inout) :: wk

    integer :: k, j, entry

    logical, save :: first_time = .true.

  continue

    if ( first_time ) then

! determine the scaling coefficients

      call my_alloc_ptr(c,n)
      call my_alloc_ptr(d,n)

      c(:) = 0.5_dp*(xl(:)+xu(:))
      d(:) = 0.5_dp*(xu(:)-xl(:))

      first_time = .false.
    endif

! scale the DV's

    x(:) = (x(:)-c(:)) / d(:)

! scale the bounds

    xl(:) = (xl(:)-c(:)) / d(:)
    xu(:) = (xu(:)-c(:)) / d(:)

! scale the gradient

    wk(1:n) = d(1:n)*wk(1:n)

! scale any constraint gradients

    if ( iprm(20) > 0 .and. ncon > 0 ) then
      entry = n
      do k = 1, iprm(20)
        do j = 1, n
          entry = entry + 1
          wk(entry) = d(j)*wk(entry)
        end do
      end do
    endif

  end subroutine scale_problem


!============================== UNSCALE_PROBLEM ==============================80
!
!  Unscales problem for PORT
!  Based on Practical Optimization (Gill, Murray, Wright)
!
!=============================================================================80
  subroutine unscale_problem(n,ncon,xl,xu,x,wk,iprm)

    integer, intent(in) :: n, ncon

    integer, dimension(:), intent(in) :: iprm

    real(dp), dimension(n), intent(inout) :: xl, xu
    real(dp), dimension(n), intent(inout) :: x
    real(dp), dimension(:), intent(inout) :: wk

    integer :: entry, j, k

  continue

! unscale the DV's

    x(:) = d(:)*x(:) + c(:)

! unscale the bounds

    xl(:) = d(:)*xl(:) + c(:)
    xu(:) = d(:)*xu(:) + c(:)

! unscale the gradient

    wk(1:n) = wk(1:n)/d(1:n)

! unscale any constraint gradients

    if ( iprm(20) > 0 .and. ncon > 0 ) then
      entry = n
      do k = 1, iprm(20)
        do j = 1, n
          entry = entry + 1
          wk(entry) = wk(entry)/d(j)
        end do
      end do
    endif

  end subroutine unscale_problem

end module dots

!============================= QTGRAD_NODE_AVG ===============================80
!
! Face gradients of (var1,u,v,w,T) with node averaging.
!
!=============================================================================80

  pure function qtgrad_node_avg(eqn_set, n_tot, n_q, n_mf,                     &
                                cell1, cell2, nodes_to_avg,                    &
                                ncell01, xc, yc, zc,                           &
                                qcell, nnodes01, x, y, z, qtavg )

    use twod_util,      only : q_2d
    use thermo,         only : q_type, conserved_q_type

    integer, intent(in) :: eqn_set, n_q, n_tot, n_mf, cell1, cell2
    integer, intent(in) :: ncell01, nnodes01

    integer,  dimension(5),             intent(in) :: nodes_to_avg
    real(dp), dimension(ncell01),       intent(in) :: xc, yc, zc
    real(dp), dimension(n_tot,ncell01), intent(in) :: qcell
    real(dp), dimension(nnodes01),      intent(in) :: x, y, z
    real(dp), dimension(n_q,nnodes01),  intent(in) :: qtavg

    integer :: n_face_nodes, node1, node2, node3, node4

    real(dp) :: dx, dy, dz

    real(dp), dimension(n_mf)   :: dqc, dq1, dq2
    real(dp), dimension(3,3) :: t, ti

    real(dp), dimension(3,n_mf) :: qtgrad_node_avg

  continue

!   Node-center connection.

    dx = xc(cell2) - xc(cell1)
    dy = yc(cell2) - yc(cell1)
    dz = zc(cell2) - zc(cell1)

    if ( q_type == conserved_q_type ) then
      dqc(1:n_mf) = qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cell2)) &
                  - qt_from_qc( eqn_set, n_mf, qcell(1:n_mf,cell1))
    else
      dqc(1:n_mf) = qt_from_qp( eqn_set, n_mf, qcell(1:n_mf,cell2)) &
                  - qt_from_qp( eqn_set, n_mf, qcell(1:n_mf,cell1))
    endif

    n_face_nodes = nodes_to_avg(5)
    node1 = nodes_to_avg(1)
    node2 = nodes_to_avg(2)
    node3 = nodes_to_avg(3)
    node4 = nodes_to_avg(4)

    if ( .not.q_2d .and. (n_face_nodes == 3) ) then

      t(1,1) = dx
      t(1,2) = dy
      t(1,3) = dz

      t(2,1) = x(node2) - x(node1)
      t(2,2) = y(node2) - y(node1)
      t(2,3) = z(node2) - z(node1)

      t(3,1) = x(node3) - x(node1)
      t(3,2) = y(node3) - y(node1)
      t(3,3) = z(node3) - z(node1)

      dq1(1:n_mf) = qtavg(1:n_mf,node2) - qtavg(1:n_mf,node1)
      dq2(1:n_mf) = qtavg(1:n_mf,node3) - qtavg(1:n_mf,node1)

    elseif ( .not.q_2d ) then

      !...assume a cyclic ordering of the points.

      t(1,1) = dx
      t(1,2) = dy
      t(1,3) = dz

      t(2,1) = 0.5_dp*( x(node1) + x(node2) - x(node3) - x(node4) )
      t(2,2) = 0.5_dp*( y(node1) + y(node2) - y(node3) - y(node4) )
      t(2,3) = 0.5_dp*( z(node1) + z(node2) - z(node3) - z(node4) )

      t(3,1) = 0.5_dp*( x(node2) + x(node3) - x(node1) - x(node4) )
      t(3,2) = 0.5_dp*( y(node2) + y(node3) - y(node1) - y(node4) )
      t(3,3) = 0.5_dp*( z(node2) + z(node3) - z(node1) - z(node4) )

      dq1(1:n_mf) = 0.5_dp*( qtavg(1:n_mf,node1) + qtavg(1:n_mf,node2) &
                           - qtavg(1:n_mf,node3) - qtavg(1:n_mf,node4) )

      dq2(1:n_mf) = 0.5_dp*( qtavg(1:n_mf,node2) + qtavg(1:n_mf,node3) &
                           - qtavg(1:n_mf,node1) - qtavg(1:n_mf,node4) )

    elseif ( q_2d ) then

      t(1,1) = dx
      t(1,2) = 0._dp
      t(1,3) = dz

      t(2,1) = x(node2) - x(node1)
      t(2,2) = 0._dp
      t(2,3) = z(node2) - z(node1)

      t(3,1) = 0._dp
      t(3,2) = 1._dp
      t(3,3) = 0._dp

      dq1(1:n_mf) = qtavg(1:n_mf,node2) - qtavg(1:n_mf,node1)
      dq2(1:n_mf) = 0._dp

    endif

    ti = tinverse( t )

    qtgrad_node_avg(1,:) = ti(1,1)*dqc(:) + ti(1,2)*dq1(:) + ti(1,3)*dq2(:)
    qtgrad_node_avg(2,:) = ti(2,1)*dqc(:) + ti(2,2)*dq1(:) + ti(2,3)*dq2(:)
    qtgrad_node_avg(3,:) = ti(3,1)*dqc(:) + ti(3,2)*dq1(:) + ti(3,3)*dq2(:)

  end function qtgrad_node_avg

!================================== pparty_data =============================80
!
! Provides methods and data structures to call Parmetis and
! store adjacency information.
!
! The final result in is adj, adjncy, and vtxdist as required by ParMetis
!
!----------------------------
!
! Building 64-bit ParMetis(4.0.2)
!
! 1. Change to 64-bit data type
!
!   vi .../parmetis-4.0.2/metis/include/metis.h
!      #define IDXTYPEWIDTH  64 /* was 32 (default) */
!      #define REALTYPEWIDTH 64 /* was 32 (default) */
!
! 2. softlink Parmetis' two created libraries (metis, parmetis) into
!    one directory for FUN3D build.
!
!   cd .../parmetis-4.0.2/build/Linux-x86_64/libparmetis
!   ln -s ../libmetis/libmetis.a
!
!=============================================================================80

module pparty_metis

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,    only : system_i1, dp
  use info_depr,   only : twod
  use local_grid,  only : pp_nhead, pp_ntail, pp_nsize,                        &
                          pp_chead, pp_ctail, pp_csize,                        &
                          cc_chead, cc_ctail, cc_csize
  use lmpi,        only : lmpi_nproc, lmpi_id, lmpi_reduce, lmpi_die,          &
                          lmpi_gatherv, lmpi_master, lmpi_send, lmpi_recv,     &
                          lmpi_conditional_stop, lmpi_finalize, lmpi_bcast,    &
                          lmpi_gather,                                         &
                          lmpi_min, lmpi_max, lmpi_comm_world, lmpi_synchronize
  implicit none

  public :: my_metis, my_metis_in, my_metis_check, convert_to_nc_partitioning
  public :: partition_out_global, partition_in_sequence, verify_in_sequence
  public :: my_metis_out, pparty_write_data_stats, migrate_driver
  public :: compute_adj_list, compute_adj_list2, compute_adj_list_unique
  public :: my_metis_cc, my_metis_out_cc, compute_c2c_cc_driver
  public :: lines_check_islands, check_partition_lines
  public :: lines_check_objects, lines_check_singleton

  integer, public :: adj_count_head, adj_count_tail

  integer, public, dimension(:), allocatable :: adj_count
  integer, public, dimension(:), allocatable :: metis_data
  integer, public, dimension(:), allocatable :: cc_adj_count, cc_adj

  integer, public, dimension(:), pointer :: adj     ! adjncy indecies - ParMetis
  integer, public, dimension(:), pointer :: adjncy  ! adjncy for ParMetis

! Use pp_nhead, pp_ntail to determine the node slice in adj
  integer, public, dimension(:), pointer :: adj_ct_unique  ! adj - cc
  integer, public, dimension(:), pointer :: adjncy_unique  ! adjncy - cc
  integer, public, dimension(:), pointer :: adjncy2        ! adjncy - cc
  integer, public, dimension(:), allocatable :: adj_count2 ! - cc

  private

#ifdef HAVE_PARMETIS
  logical :: stats_opened
#endif
  integer, public, dimension(:), pointer :: vtxdist   ! adjncy ranges - ParMetis

  integer, dimension(:), allocatable :: node_weight, overset_node_weight
  integer, dimension(:), allocatable :: edge_weight
  integer, dimension(:), allocatable :: imesh_global

  integer, parameter :: migration_loop = 4
  real(dp),parameter :: migration_tol  = 0.005_dp

  type elem1_type
    integer :: ct
    integer(system_i1), dimension(:), pointer :: ctag
  end type

  type elem1_c2c_type
    integer, dimension(:,:),          pointer :: c2c
    integer, dimension(:),            pointer :: cl2g
    integer :: ncell
  end type

  type elem2_type
    integer :: sz, ct
    integer, dimension(:),   pointer :: cl2g
    integer, dimension(:,:), pointer :: c2n
  end type elem2_type

  type elem3_type
    integer                          :: ncell
    integer, dimension(:),   pointer :: cl2g
    integer, dimension(:,:), pointer :: c2c
  end type elem3_type

  public :: elem1_c2c_type, elem1_type, elem3_type
  type(elem1_c2c_type), public, dimension(:), allocatable :: elem1_c2c
  type(elem1_type),     public, dimension(:), allocatable :: elem1_tag
  type(elem3_type),     public, dimension(:), allocatable :: elem3_c2c

contains

!============================== MY_METIS =====================================80
!
! Partitions mesh in parallel
!
!=============================================================================80
  subroutine my_metis(grid)

    use grid_types,  only : grid_type

#ifdef HAVE_PARMETIS
    use metis_defs,        only : partitioning_only
    use info_depr,         only : pp_cmd_wgtflag, pp_cmd_ubvec, pp_cmd_use64,  &
                                  pp_cmd_nnparts, pp_cmd_stats,                &
                                  pp_cmd_PM_use_proc, pp_cmd_skip_do_min,      &
                                  serialize_partitioner
    use nml_global,        only : moving_grid
    use nml_overset_data,  only : use_imesh_constraint
    use nml_overset_data,  only : overset_flag, dci_on_the_fly, dci_io
    use pundit,            only : pundit_flag
    use kinddefs,          only : dp, r4, r8, i8
    use suggar_info,       only : fun3d_comm
    use system_extensions, only : se_open, se_wall_time, se_wall_clock
    use nml_governing_equations, only : ssdc_flag, discretization

    type(grid_type), intent(in) :: grid

    integer :: edgeCut,ncon,ihead,itail,offset,gn,imesh
    integer :: isize,ivsize,ki,ioff,nnodesg,nnparts
    integer :: numbering,weightflag,generic_comm

    integer, dimension(:), pointer :: weight
    integer :: ielem, icell, local_node, global_node, index1
    integer, dimension(5)          :: options

    integer :: use_proc, i,j, new_comm1
    integer :: is, ie, ierr, myid, icolor, new_lmpi_id
    logical :: iam_in, do_min

    real(dp) :: wvalue, time1, time2

    integer, dimension(:), allocatable :: temp_metis_data, colors
    integer, dimension(:), pointer :: temp_weight, temp, temp_edge_weight
    integer, dimension(:), pointer :: temp_adj, temp_adjncy, temp_vtxdist

    integer, dimension(:), pointer :: js,je,procs

    integer, parameter :: iunit_stats = 172
    logical, parameter :: dbtime      = .false.

!beginNeverComplex
    real(r4), dimension(:), allocatable :: tpwgts,ubvec
!endNeverComplex

!--------------------- PM_4.0 64-bit ------------------
  integer(i8) :: weightflag_i8, numbering_i8, ncon_i8, nnparts_i8, edgeCut_i8
  integer(i8), dimension(:), allocatable :: weight_i8, edge_weight_i8,vtxdist_i8
  integer(i8), dimension(:), allocatable :: adj_i8, adjncy_i8, options_i8
  integer(i8), dimension(:), allocatable :: metis_data_i8
  real(r8), dimension(:), allocatable :: tpwgts_r8, ubvec_r8
!---------------------

  continue

    if (partitioning_only) then
       nnodesg = 0
       if (.not.partitioning_only) write(*,*) nnodesg, lmpi_master
    end if
    isize = pp_nsize(lmpi_id)
    if ( allocated(metis_data) ) deallocate(metis_data)
    allocate(metis_data(isize))

! if the solver was run on a single processor, set the part vector to
! identity and simply return; no need to call ParMetis

    if ( lmpi_nproc == 1 ) then
      metis_data = 1
      deallocate(adj)
      deallocate(adjncy)
      if ( allocated(imesh_global) ) deallocate(imesh_global)
      return
    endif

! Optionally use serial Metis if requested

    if ( serialize_partitioner ) then

      call serial_metis(grid%nnodesg,1)

      deallocate(adj,adjncy)
      if ( allocated(imesh_global) ) deallocate(imesh_global)
      return

    endif

    stats_opened = .false.

    if (lmpi_master) write(*,*)"    ... PM (64,skip_do_min) : ",               &
       pp_cmd_use64, pp_cmd_skip_do_min

    do_min = .false.
    if (grid%nnodesg <= 100000000) then
       if ((.not.twod).and.(.not.pp_cmd_skip_do_min).and.(pp_cmd_use64 == 0))  &
          call PM_min_setup(grid%nnodesg,do_min,use_proc,js,je,procs,is,ie,    &
                            iam_in,myid)
    else
       if (lmpi_master)                                                        &
          write(*,*)"All cores are used by ParMetis for grids > 100M nodes."
    end if
    numbering  = 1
    options    = 0
    options(1) = 0
    options(2) = 3
    options(3) = 1
    nnparts = lmpi_nproc
    if (pp_cmd_nnparts /= 0) then
       nnparts = pp_cmd_nnparts
       partitioning_only = .true.
    end if

!   Allocate and map partitions to processors

    ! Allocate the weights of the vertices and edges ----------------------
    select case (pp_cmd_wgtflag)
    case (0)
       ncon       = 1
       if ( (overset_flag.or.pundit_flag) .and. lmpi_master ) then
         write(*,*) 'overset/pundit not compatible with pp_cmd_wgtflag=0.'
         call lmpi_finalize
         stop
       endif
       weightflag = 0
       ivsize     = 1
    case(1,2,3)
       ncon       = 2
       if (((overset_flag.and.use_imesh_constraint).or.pundit_flag)            &
             .and.moving_grid )then
         ncon = 3
       endif
       weightflag = 2
       if ( twod ) weightflag = 3
       if ( ssdc_flag ) ncon = 2
       ivsize = pp_nsize(lmpi_id)*ncon
    case default
       ivsize = 1; ncon = 1 ! avoid "may be unused undef"
       write(*,*) 'Invalid pp_cmd_wgtflag'
       call lmpi_die()
     end select

!   Allocate temporary arrays used for parallel processing
    allocate(tpwgts(nnparts*ncon)); tpwgts = 1.0_r4/real(nnparts,r4)
    allocate(ubvec(ncon));          ubvec  = real(pp_cmd_ubvec,r4)

    allocate(weight(ivsize)); weight = 0

    ihead = pp_nhead(lmpi_id)
    itail = pp_ntail(lmpi_id)

    if ((pp_cmd_wgtflag >= 1).and.(pp_cmd_wgtflag <= 3)) then

      ssdc_or_legacy : if ( ssdc_flag ) then

        ioff = 1
        offset = pp_nhead(lmpi_id) - 1

        do ki = 1, pp_nsize(lmpi_id)

          if ( overset_flag .or. pundit_flag ) then

             gn = offset + ki

             if ( .not. (gn >= ihead .and. gn <= itail) ) then
               write(*,*) 'Error: global node mismatch in range.'
               call lmpi_die
               stop
             endif

             imesh = imesh_global(gn) + 1 ! shift to 1-based

             select case(trim(discretization(imesh)))
             case('finite-volume')
               weight(ioff)   = adj(ki+1) - adj(ki)   ! finite-volume constraint
               weight(ioff+1) = 1                     ! FEM constraint
             case('ssdc')
               weight(ioff)   = 1                     ! finite-volume constraint
               weight(ioff+1) = adj(ki+1) - adj(ki)   ! FEM constraint
             case default
               write(*,*) 'Unknown value of discretization: ',                 &
                          trim(discretization(imesh))
               call lmpi_die
               stop
             end select

          else

             weight(ioff)   = 1                     ! finite-volume constraint
             weight(ioff+1) = adj(ki+1) - adj(ki)   ! FEM constraint

          endif

          ioff = ioff + ncon

        end do

      else ssdc_or_legacy

! Use 2 weights per node - cell degree and edge degree.

        ioff = 1
        do ki = 1,pp_nsize(lmpi_id)
          !write(50000+lmpi_id,'(i0,1x,100(i0,1x))') &
          ! pp_nhead(lmpi_id)+ki-1,adj(ki+1)-adj(ki),adjncy(adj(ki):adj(ki+1)-1)
          if (pp_cmd_wgtflag == 1) then
             weight(ioff)   = node_weight(ki)
             weight(ioff+1) = 1
          elseif (pp_cmd_wgtflag == 2) then
             weight(ioff)   = adj(ki+1) - adj(ki)
             weight(ioff+1) = 1
          else
             ! flip the weight index to match party
             weight(ioff+1) = adj(ki+1) - adj(ki)
          end if
          if ( ((overset_flag.and.use_imesh_constraint) .or. pundit_flag)      &
                 .and. moving_grid ) then
            weight(ioff+2) = overset_node_weight(ki)
          endif
          ioff = ioff + ncon
        end do
        if (pp_cmd_wgtflag == 3) then
           do ielem = 1, grid%nelem
             do icell = 1, grid%elem(ielem)%ncell
               do local_node = 1, grid%elem(ielem)%node_per_cell
                 global_node = grid%elem(ielem)%c2n(local_node,icell)
                 if ((global_node >= pp_nhead(lmpi_id)).and.                   &
                     (global_node <= pp_ntail(lmpi_id))) then
                    index1 = global_node-pp_nhead(lmpi_id)
                    index1 = (index1*ncon)+1
                    weight(index1) = weight(index1) + 1
                 end if
               end do
             end do
           end do
          !ioff = 0
          !do ki = 1,pp_nsize(lmpi_id)*2,2
          ! write(3000+lmpi_id,*) pp_nhead(lmpi_id)+ioff,weight(ki),weight(ki+1)
          !   ioff = ioff + 1
          !end do
        end if

      endif ssdc_or_legacy

    end if

      if (do_min) then
         call PM_min_gather(use_proc,je,is,ie,iam_in,myid,ncon,twod,           &
              weight, edge_weight,                                             &
              temp_adj,temp_adjncy,temp_weight,temp_edge_weight,temp_vtxdist)
      end if

    if ( allocated(        node_weight) ) deallocate(        node_weight)
    if ( allocated(overset_node_weight) ) deallocate(overset_node_weight)

    edgeCut = 0

    if (lmpi_master)                                                           &
       write(*,*)'    ... Calling ParMetis (ParMETIS_V3_PartKway) ....',       &
         pp_cmd_use64,pp_cmd_skip_do_min

       if ((pp_cmd_stats==1).or.(pp_cmd_stats==3))                             &
          call pparty_write_metis_stats_open(iunit_stats,grid)

       generic_comm = lmpi_comm_world
       if ( dci_on_the_fly .or. dci_io ) generic_comm = fun3d_comm
       if (.not.do_min) then
         if (pp_cmd_use64 == 0) then
            if (lmpi_master.and.dbtime)                                        &
               call se_wall_time("ParMETIS_V3_PartKway (32)")
            if (lmpi_master) time1 = se_wall_clock()
           call ParMETIS_V3_PartKway(                                          &
             vtxdist, adj, adjncy, weight, edge_weight,                        &
             weightflag, numbering, ncon, nnparts, tpwgts, ubvec,              &
             options, edgeCut, metis_data, generic_comm)
            if (lmpi_master) time2 = se_wall_clock()
            if (lmpi_master.and.dbtime)                                        &
               call se_wall_time("ParMETIS_V3_PartKway (32)")
         else
            allocate(vtxdist_i8(size(vtxdist))); vtxdist_i8 = vtxdist
            allocate(adj_i8(size(adj)));         adj_i8     = adj
            allocate(adjncy_i8(size(adjncy)));   adjncy_i8  = adjncy
            allocate(weight_i8(size(weight)));   weight_i8  = weight
            allocate(edge_weight_i8(size(edge_weight)))
                     edge_weight_i8=edge_weight
            weightflag_i8 = weightflag
            numbering_i8  = numbering
            ncon_i8       = ncon
            nnparts_i8    = nnparts
            allocate(tpwgts_r8(size(tpwgts)));   tpwgts_r8  = tpwgts
            allocate(ubvec_r8(size(ubvec)));     ubvec_r8   = ubvec
            allocate(options_i8(size(options))); options_i8 = options
            edgeCut_i8 = 0
            allocate(metis_data_i8(size(metis_data))); metis_data_i8 = 0
            if (lmpi_master) time1 = se_wall_clock()
            if (lmpi_master.and.dbtime)                                        &
               call se_wall_time("ParMETIS_V3_PartKway (64)")
            call ParMETIS_V3_PartKway(                                         &
                   vtxdist_i8, adj_i8, adjncy_i8,                              &
                   weight_i8, edge_weight_i8,                                  &
                   weightflag_i8, numbering_i8, ncon_i8, nnparts_i8,           &
                   tpwgts_r8, ubvec_r8,                                        &
                   options_i8, edgeCut_i8, metis_data_i8, generic_comm)
            if (lmpi_master.and.dbtime)                                        &
               call se_wall_time("ParMETIS_V3_PartKway (64)")
            if (lmpi_master) time2 = se_wall_clock()
            edgeCut    = edgeCut_i8
            metis_data = metis_data_i8
            deallocate(vtxdist_i8,adj_i8,adjncy_i8,weight_i8,edge_weight_i8)
            deallocate(tpwgts_r8, ubvec_r8, options_i8, metis_data_i8)
         end if
         if (lmpi_master) then
            write(*,*)'    ... edgeCut ',edgeCut
            write(*,'("     ... Time for ParMetis: ",f0.1," s")') &
            real(time2-time1,dp)
         end if

           if ((pp_cmd_stats == 1).or.(pp_cmd_stats == 3)) then
             call pparty_write_metis_stats(                                  &
                    "reg",iunit_stats,nnparts,                               &
                    size(metis_data),metis_data,                             &
                    ncon,size(weight),weight,wvalue)
           end if
          ! write(5000+lmpi_id,*) " size(metis_data)=",size(metis_data)
          ! write(5000+lmpi_id,"(1x,10i8)") (metis_data(i),i=1,size(metis_data))
       else ! do_min
          ! write(*,'("VT2 ",i0,1x,":: ",16(i0,1x))') lmpi_id,temp_vtxdist
          icolor = 1
          if (iam_in) icolor = 0
          allocate(colors(0:lmpi_nproc-1))
          call lmpi_gather(icolor,colors)
          call lmpi_bcast(colors)
          new_lmpi_id = 0
          if (iam_in) then
             if (lmpi_id > 0) new_lmpi_id = sum(colors(0:lmpi_id-1))
          end if
          deallocate(colors)
          call MPI_COMM_SPLIT(generic_comm,icolor,new_lmpi_id,new_comm1,ierr)
          if (iam_in) then
             ! call MPI_COMM_RANK(new_comm1, new_lmpi_id, ierr)
             ! call MPI_COMM_SIZE(new_comm1, new_lmpi_nproc, ierr)
             ! write(*,*)"NR ",new_lmpi_id,new_lmpi_nproc
             ! write(*,*)"PM MIN ",myid,use_proc,nnparts,size(weight)
               i = sum(pp_nsize(is:ie))
             allocate(temp_metis_data(i)); temp_metis_data = 0
             call ParMETIS_V3_PartKway(                                        &
                temp_vtxdist, temp_adj, temp_adjncy,                           &
                temp_weight, temp_edge_weight,                                 &
                weightflag, numbering, ncon, nnparts, tpwgts, ubvec,           &
                options, edgeCut, temp_metis_data, new_comm1)
             if (lmpi_master) write(*,*)'    ... edgeCut ',edgeCut
             if (pp_cmd_stats == 1) then
               call pparty_write_metis_stats(                                  &
                      "do_min",iunit_stats,nnparts,                            &
                      size(temp_metis_data),temp_metis_data,                   &
                      ncon,size(temp_weight),temp_weight,wvalue,new_comm1)
             end if
            !if (pp_cmd_PM_use_proc > 0) then
            !      do i = 1,size(temp_metis_data)
            !        write(6000+myid,*) " ",temp_metis_data(i)," "
            !      end do
            !end if

             deallocate(temp_adj, temp_adjncy, temp_edge_weight, temp_vtxdist)
             deallocate(temp_weight)

             ! distribute partition vector
             ioff = pp_nsize(lmpi_id)
             metis_data(1:ioff) = temp_metis_data(1:ioff)
             if (ie > lmpi_id) then
                do i = is+1,ie
                   allocate(temp(pp_nsize(i))); temp = 0
                   do j = 1,pp_nsize(i)
                      ioff = ioff + 1
                      temp(j) = temp_metis_data(ioff)
                   end do
                   call lmpi_send(temp,pp_nsize(i),i,i+60000,ierr)
                   deallocate(temp)
                end do
             end if
             deallocate(temp_metis_data)
          else
             ioff = pp_nsize(lmpi_id)
             call lmpi_recv(metis_data,ioff,is,lmpi_id+60000,ierr)
          end if
          ! do i = 1,size(metis_data)
          !      write(86000+lmpi_id,*) " ",metis_data(i)," "
          ! end do
          ! write(5000+lmpi_id,*) " size(metis_data)=",size(metis_data)
          ! write(5000+lmpi_id,"(1x,10i8)") (metis_data(i),i=1,size(metis_data))

       end if

    if ( twod ) call check_metis_2d(grid)

    if (partitioning_only) then
       call lmpi_reduce(isize,nnodesg)
       call my_metis_out(nnodesg,grid%project)
       call lmpi_conditional_stop(1,&
       'Stopping after writing project.metisout:my_metis')
    end if

    deallocate(edge_weight,weight,ubvec,tpwgts,adj,adjncy)
#else
    type(grid_type), intent(in) :: grid

  continue

! if the solver was run on a single processor, set the part vector to
! identity and simply return; no need to call ParMetis

    if (lmpi_nproc == 1) then
       if ( allocated(metis_data) ) deallocate(metis_data)
       allocate(metis_data(pp_nsize(lmpi_id)))
       metis_data = 1
       deallocate(adj)
       deallocate(adjncy)
    else
       write(*,*) 'Solver run on more than one processor, but ParMetis has not'
       write(*,*) 'been linked against - exiting.',grid%nnodesg,               &
                   lmpi_comm_world ! variable flagged by compiler
       call lmpi_die
       call check_metis_2d(grid) ! compiler
    endif
#endif

    if ( allocated(imesh_global) ) deallocate(imesh_global)

 end subroutine my_metis


!============================ CONVERT_TO_NC_PARTITIONING =====================80
!
!  Takes a cell-centered partition vector and heuristically turns
!  it into a node-centered partitioning
!
!=============================================================================80
  subroutine convert_to_nc_partitioning(grid)

    use lmpi,           only : lmpi_id, lmpi_bcast, lmpi_die, lmpi_reduce
    use ssdc_interface, only : metis_data_ssdc
    use local_grid,     only : pp_nsize, pp_nhead, pp_ntail, cc_csize,         &
                               cc_chead, cc_ctail
    use grid_types,     only : grid_type

    type(grid_type), intent(in) :: grid

    integer :: i, nsize, elem_rank, node, node_rank, j, nhead, ntail, nproblems
    integer :: min_node, ioff, global_cell, test_cell, ipe, csize, ielem
    integer :: chead, ctail, k, list_size, l, remote_node, rank, arbitrary_rank
    integer :: nresolves, nresolvesg, indx

    integer, dimension(:),   allocatable :: temp1
    integer, dimension(:,:), allocatable :: list

    logical :: found_unassigned, found_on_my_proc, found_desired_rank

    type list_type
      integer, dimension(:), allocatable :: value
    end type list_type

    type node_part_type
      integer, dimension(:,:), allocatable :: value
    end type node_part_type

    type(list_type),      dimension(:), allocatable :: elem_partition
    type(list_type),      dimension(:), allocatable :: problems
    type(node_part_type), dimension(:), allocatable :: node_partition

  continue

! Establish an array that goes along with grid%elem(:)%ncell that contains
! the partition numbers assigned by Metis.  Pass the raw partition vector
! around and mine what we need on the local rank.

    allocate(elem_partition(grid%nelem))
    do ielem = 1, grid%nelem
      allocate(elem_partition(ielem)%value(grid%elem(ielem)%ncell))
      elem_partition(ielem)%value(:) = 0
    end do

    ioff = 0

    do ipe = 0, lmpi_nproc-1

      csize = cc_csize(ipe)
      allocate(temp1(csize))
      if (lmpi_id == ipe) temp1(1:csize) = metis_data(1:csize)
      call lmpi_bcast(temp1,ipe)

      do i = 1, csize
        global_cell = i + ioff
        search_fams : do ielem = 1, grid%nelem
          search_elems : do j = 1, grid%elem(ielem)%ncell
            test_cell = grid%elem(ielem)%cl2g(j)
            if ( test_cell == global_cell ) then
              elem_partition(ielem)%value(j) = temp1(i)
              exit search_fams
            endif
          end do search_elems
        end do search_fams
      end do

      deallocate(temp1)
      ioff = ioff + csize

    end do

! Make sure every element was assigned a rank

    do ielem = 1, grid%nelem
      do j = 1, grid%elem(ielem)%ncell
        if ( elem_partition(ielem)%value(j) == 0 ) then
          write(*,*) 'Error: element not assigned a rank.', ielem, j
          call lmpi_die
          stop
        endif
      end do
    end do

! Make a copy of the element-based partitioning and delete the original
! as we prepare to make a node-based one

    allocate(metis_data_ssdc(size(metis_data,1)))
    metis_data_ssdc = metis_data

    deallocate(metis_data)

    nsize = pp_nsize(lmpi_id)

    allocate(metis_data(nsize))
    metis_data(:) = 0

! Set up a list analogous to grid%elem(:)%c2n that will store partition numbers
! for off-processor nodes.  This is to avoid a globally-numbered array.

    allocate(node_partition(grid%nelem))
    do ielem = 1, grid%nelem
      allocate(node_partition(ielem)%value(grid%elem(ielem)%node_per_cell,     &
               grid%elem(ielem)%ncell))
      node_partition(ielem)%value(:,:) = 0
    end do

    nhead = pp_nhead(lmpi_id)
    ntail = pp_ntail(lmpi_id)

! Go through the elements on my processor.  Look at the partition number
! for the element.  Look through the local points in the element.  If one
! of the local points has already been assigned to the same processor as
! the current element, go on to the next element.  Otherwise, find a local
! point in the element which has not yet been assigned to a processor, and
! assign it to the element's processor.  If no local point is available for
! assigning the element's processor number, then mark that element as a
! problem child to revisit.

    nproblems = 0

    allocate(problems(grid%nelem))
    do ielem = 1, grid%nelem
      allocate(problems(ielem)%value(grid%elem(ielem)%ncell))
      problems(ielem)%value(:) = 0
    end do

    fam_loop : do ielem = 1, grid%nelem
      cell_loop : do j = 1, grid%elem(ielem)%ncell

        elem_rank = elem_partition(ielem)%value(j)

! See if this element already has a node assigned to the desired rank
! If so, skip it, he's good

        found_on_my_proc = .false.
        search1 : do k = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(k,j)
          if ( node >= nhead .and. node <= ntail ) then
            node_rank = metis_data(node-nhead+1)
            if ( node_rank == elem_rank ) then
              found_on_my_proc = .true.
              exit search1
            endif
          endif
        end do search1

        if ( found_on_my_proc ) cycle cell_loop

! See if this element has a local node that has yet to be assigned to
! a rank.  If so, assign the desired rank to that point and move on.

        found_unassigned = .false.
        search2 : do k = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(k,j)
          if ( node >= nhead .and. node <= ntail ) then
            node_rank = metis_data(node-nhead+1)
            if ( node_rank == 0 ) then
              found_unassigned = .true.
              metis_data(node-nhead+1) = elem_rank
              node_partition(ielem)%value(k,j) = elem_rank
              exit search2
            endif
          endif
        end do search2

        if ( found_unassigned ) cycle cell_loop

! If there are no local nodes available to be assigned to the desired rank,
! mark this cell as a potential problem for now.

        nproblems = nproblems + 1
        problems(ielem)%value(j) = 1

      end do cell_loop
    end do fam_loop

! Print out the number of problem elements from above to see how we are doing.
! Also dump out a list of the problem elements' original processor numbers.

! Now send the (local, but in global numbering) nodes that were assigned a
! partition number around to all other ranks, and if they have an element
! who contains one of these global node numbers, then update its copy of
! the partition number that was assigned at these points.

    rank_loop : do ipe = 0, lmpi_nproc-1

      list_size = nsize
      call lmpi_bcast(list_size,ipe)

      allocate(list(2,list_size))

      if ( lmpi_id == ipe ) then
        do i = 1, nsize
          list(1,i) = i + nhead - 1 ! global node number
          list(2,i) = metis_data(i) ! assigned partition (if any)
        end do
      endif

      call lmpi_bcast(list,ipe)

      if ( lmpi_id /= ipe ) then

        do ielem = 1, grid%nelem
          do j = 1, grid%elem(ielem)%ncell

            npc : do k = 1, grid%elem(ielem)%node_per_cell

              node = grid%elem(ielem)%c2n(k,j)

              if ( node >= nhead .and. node <= ntail ) cycle npc

              search_list : do l = 1, list_size
                remote_node = list(1,l)
                if ( remote_node == node ) then
                  node_partition(ielem)%value(k,j) = list(2,l)
                  exit search_list
                endif
              end do search_list

            end do npc

          end do
        end do

      endif

      deallocate(list)

    end do rank_loop

! Go back through the elements on my processor now and update the list of
! cells who are still considered problems.  Also at the same time, check
! to make sure there are no nodes with rank=0 assigned to them - that
! would indicate we have the freedom to resolve this problem cell

    nproblems = 0
    do ielem = 1, grid%nelem
      problems(ielem)%value(:) = 0
    end do

    fam_loop2 : do ielem = 1, grid%nelem
      cell_loop2 : do j = 1, grid%elem(ielem)%ncell

        elem_rank = elem_partition(ielem)%value(j)

        found_desired_rank = .false.
        look_npc : do k = 1, grid%elem(ielem)%node_per_cell

          node = grid%elem(ielem)%c2n(k,j)

          if ( node >= nhead .and. node <= ntail ) then
            rank = metis_data(node-nhead+1)
          else
            rank = node_partition(ielem)%value(k,j)
          endif

          if ( rank == elem_rank ) then
            found_desired_rank = .true.
            exit look_npc
          endif

        end do look_npc

        if ( found_desired_rank ) cycle cell_loop2

! Check for neighboring rank=0 nodes

        look_npc2 : do k = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(k,j)
          if ( node >= nhead .and. node <= ntail ) then
            rank = metis_data(node-nhead+1)
          else
            rank = node_partition(ielem)%value(k,j)
          endif
          if ( rank == 0 ) then
            write(*,*) 'Hey, we could easily resolve a problem element!'
            call lmpi_die
            stop
          endif
        end do look_npc2

        nproblems = nproblems + 1
        problems(ielem)%value(j) = 1

      end do cell_loop2
    end do fam_loop2

! Make sure all local nodes have been assigned a nonzero rank now by simply
! assigning them to the first adjacent element we come across

    fam_loop4 : do ielem = 1, grid%nelem
      cell_loop4 : do j = 1, grid%elem(ielem)%ncell

        finish_locals : do k = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(k,j)
          if ( node >= nhead .and. node <= ntail ) then
            rank = metis_data(node-nhead+1)
            if( rank==0 )metis_data(node-nhead+1)=elem_partition(ielem)%value(j)
          endif
        end do finish_locals

      end do cell_loop4
    end do fam_loop4

! Transfer freshly updated local node ranks to other processors


    rank_loop3 : do ipe = 0, lmpi_nproc-1

      list_size = nsize
      call lmpi_bcast(list_size,ipe)

      allocate(list(2,list_size))

      if ( lmpi_id == ipe ) then
        do i = 1, nsize
          list(1,i) = i + nhead - 1 ! global node number
          list(2,i) = metis_data(i) ! assigned partition (if any)
        end do
      endif

      call lmpi_bcast(list,ipe)

      if ( lmpi_id /= ipe ) then

        do ielem = 1, grid%nelem
          do j = 1, grid%elem(ielem)%ncell

            npc2 : do k = 1, grid%elem(ielem)%node_per_cell

              node = grid%elem(ielem)%c2n(k,j)

              if ( node >= nhead .and. node <= ntail ) cycle npc2

              search_list2 : do l = 1, list_size
                remote_node = list(1,l)
                if ( remote_node == node ) then
                  node_partition(ielem)%value(k,j) = list(2,l)
                  exit search_list2
                endif
              end do search_list2

            end do npc2

          end do
        end do

      endif

      deallocate(list)

    end do rank_loop3

! Now there are many things we could attempt to do to address problem elements
! at this point, but let's try to just simply reassign any for now.

! Other possible things to try perhaps in the future:

! Now go through my problem elements and for those who have non-local nodes
! with unassigned partition numbers, compose a list of ranks that we would
! like to assign that node to.  Note there may be more than one desired rank
! since neighboring elements may both want that node on differing partitions.

! If a problem element belongs to more than one rank, allow a rank to set
! the partition number for non-local nodes.

! But again, for now, just reassign the element to the rank of an arbitrary
! local node in the element.  Make sure we only hit each element once by only
! treating him if the minimum global node number lies on our processor.  Then
! share that updated element partition number with all other ranks.

    nresolves = 0

    fam_loop3 : do ielem = 1, grid%nelem
      cell_loop3 : do j = 1, grid%elem(ielem)%ncell

        if ( problems(ielem)%value(j) == 0 ) cycle cell_loop3

        arbitrary_rank = -1
        min_node = huge(min_node)
        do k = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(k,j)
          min_node = min(min_node,node)
          if ( node >= nhead .and. node <= ntail ) then
            arbitrary_rank = metis_data(node-nhead+1)
          endif
        end do

        found_on_my_proc = .false.
        if ( min_node >= nhead .and. min_node <= ntail ) found_on_my_proc=.true.

        if ( .not. found_on_my_proc ) cycle cell_loop3

        if ( arbitrary_rank == -1 ) then
          write(*,*) 'Error locating arbitrary rank.'
          call lmpi_die
          stop
        endif

        elem_partition(ielem)%value(j) = arbitrary_rank
        nresolves = nresolves + 1

      end do cell_loop3
    end do fam_loop3

    call lmpi_reduce(nresolves, nresolvesg)
    call lmpi_bcast(nresolvesg)

    if ( lmpi_master ) then
      if ( nresolvesg > 0 ) then
        write(*,'(a,i0,a)') '     ... Resolved ', nresolvesg,                  &
                            ' partitioning conflicts heuristically'
      endif
    endif

! Share final element partition data with everyone else and also store
! the local ones

    chead = cc_chead(lmpi_id)
    ctail = cc_ctail(lmpi_id)

    rank_loop2 : do ipe = 0, lmpi_nproc-1

      if ( ipe == lmpi_id ) then

! count em

        list_size = 0
        fam1 : do ielem = 1, grid%nelem
          cells1 : do j = 1, grid%elem(ielem)%ncell

            global_cell = grid%elem(ielem)%cl2g(j)

! If I "own" this cell, it might have been updated, so send it around so
! everyone else can see it

            min_node = huge(min_node)
            do k = 1, grid%elem(ielem)%node_per_cell
              node = grid%elem(ielem)%c2n(k,j)
              min_node = min(min_node,node)
            end do

            found_on_my_proc = .false.
            if ( min_node>=nhead .and. min_node<=ntail ) found_on_my_proc=.true.

            if ( found_on_my_proc ) list_size = list_size + 1

          end do cells1
        end do fam1

! allocate em

        allocate(list(2,list_size))

! store em

        list_size = 0
        fam2 : do ielem = 1, grid%nelem
          cells2 : do j = 1, grid%elem(ielem)%ncell

            global_cell = grid%elem(ielem)%cl2g(j)

! If I "own" this cell, it might have been updated, so send it around so
! everyone else can see it

            min_node = huge(min_node)
            do k = 1, grid%elem(ielem)%node_per_cell
              node = grid%elem(ielem)%c2n(k,j)
              min_node = min(min_node,node)
            end do

            found_on_my_proc = .false.
            if ( min_node>=nhead .and. min_node<=ntail ) found_on_my_proc=.true.

            if ( found_on_my_proc ) then
              list_size = list_size + 1
              list(1,list_size) = global_cell
              list(2,list_size) = elem_partition(ielem)%value(j)
            endif

          end do cells2
        end do fam2

      endif

! broadcast em

      call lmpi_bcast(list_size,ipe)
      if ( lmpi_id /= ipe ) allocate(list(2,list_size))
      call lmpi_bcast(list,ipe)

! mine anything that belongs on my rank

      do i = 1, list_size
        global_cell = list(1,i)
        if ( global_cell >= chead .and. global_cell <= ctail ) then
          indx = global_cell - chead + 1
          metis_data_ssdc(indx) = list(2,i)
        endif
      end do

! clean up for next go-round

      deallocate(list)

    end do rank_loop2

! Update the list of element partitions

    ioff = 0

    do ipe = 0, lmpi_nproc-1

      csize = cc_csize(ipe)
      allocate(temp1(csize))
      if (lmpi_id == ipe) temp1(1:csize) = metis_data_ssdc(1:csize)
      call lmpi_bcast(temp1,ipe)

      do i = 1, csize
        global_cell = i + ioff
        search_fams2 : do ielem = 1, grid%nelem
          search_elems2 : do j = 1, grid%elem(ielem)%ncell
            test_cell = grid%elem(ielem)%cl2g(j)
            if ( test_cell == global_cell ) then
              elem_partition(ielem)%value(j) = temp1(i)
              exit search_fams2
            endif
          end do search_elems2
        end do search_fams2
      end do

      deallocate(temp1)
      ioff = ioff + csize

    end do

! Make one last check to ensure that there is a node in each element that
! lies on the same rank as the element

    do ielem = 1, grid%nelem
      do j = 1, grid%elem(ielem)%ncell
        elem_rank = elem_partition(ielem)%value(j)
        found_desired_rank = .false.
        hunt : do k = 1, grid%elem(ielem)%node_per_cell
          node = grid%elem(ielem)%c2n(k,j)
          if ( node >= nhead .and. node <= ntail ) then
            node_rank = metis_data(node-nhead+1)
          else
            node_rank = node_partition(ielem)%value(k,j)
          endif
          if ( node_rank == elem_rank ) then
            found_desired_rank = .true.
            exit hunt
          endif
        end do hunt
        if ( .not. found_desired_rank ) then
          write(*,*) 'Element/node inconsistency at end of conversion.'
          call lmpi_die
          stop
        endif
      end do
    end do

! Make final check to ensure both the node-based and element-based partition
! vectors have values between 1 and nproc

    do i = 1, pp_nsize(lmpi_id)
      if ( metis_data(i) < 1 .or. metis_data(i) > lmpi_nproc ) then
        write(*,*) 'Bad value of node-based partition vector: ', metis_data(i)
      endif
    end do

    do i = 1, cc_csize(lmpi_id)
      if ( metis_data_ssdc(i) < 1 .or. metis_data_ssdc(i) > lmpi_nproc ) then
        write(*,*)'Bad value of elem-based partition vector:',metis_data_ssdc(i)
      endif
    end do

! Clean up and finally we are done

    do ielem = 1, grid%nelem
      deallocate(elem_partition(ielem)%value)
      deallocate(node_partition(ielem)%value)
      deallocate(problems(ielem)%value)
    end do

    deallocate(elem_partition, node_partition, problems)

 end subroutine convert_to_nc_partitioning


#ifdef HAVE_PARMETIS
!============================== SERIAL_METIS =================================80
!
!  Moves raw grid data to master node and calls Metis to get partitioning
!
!  The input argument adj_key tells us how the adj array is allocated:
!    key = 1: adj(dimension), as done in the node-centered path
!    key = 2: adj(dim1:dim2), as done in the cell-centered path
!
!=============================================================================80
  subroutine serial_metis(global_ndof,adj_key)

    use lmpi,       only : lmpi_nproc, lmpi_reduce, lmpi_bcast, lmpi_master,   &
                           lmpi_id, lmpi_die, lmpi_success
    use kinddefs,   only : r4, r8
    use info_depr,  only : metis_numbering_entry

    integer, intent(in) :: global_ndof, adj_key

    integer :: ipe, total_size, ierr, dim1, counter1, counter2, i, jstart, jend
    integer :: j, objval, nvtxs, ncon, offset, indx1, indx2, my_nnz, dim2

    integer, dimension(0:40)          :: options
    integer, dimension(global_ndof) :: part, vwgt, vsize
    integer, dimension(lmpi_nproc)    :: ndof_proc

    integer, dimension(:), allocatable :: xadj_buffer, global_adjncy, adjwgt
    integer, dimension(:), allocatable :: global_xadj, adjncy_buffer

    real(r4), dimension(1) :: ubvec

    real(r4), dimension(lmpi_nproc) :: tpwgts

  continue

    if ( lmpi_master ) allocate(global_xadj(global_ndof+1))

    select case(adj_key)
    case(1)
      my_nnz = adj(vtxdist(lmpi_id+2) - vtxdist(lmpi_id+1) + 1) - 1
    case(2)
      my_nnz = adj(vtxdist(lmpi_id+2)) - 1
    end select

    call lmpi_reduce(my_nnz, total_size)
    call lmpi_bcast(total_size)

    if ( lmpi_master ) allocate(global_adjncy(total_size))

! First insert master's data
! adj_key does not matter here since this is only on the master so everything
! starts consistently at 1

    if ( lmpi_master ) then

      dim1 = vtxdist(lmpi_id+1)
      dim2 = vtxdist(lmpi_id+2)-1

      do i = dim1, dim2
        global_xadj(i) = adj(i)
      end do

      do i = 1, adj(dim2+1)-1
        global_adjncy(i) = adjncy(i)
      end do

      counter1 = dim2           ! Number of DOFs we have accumulated so far
      counter2 = adj(dim2+1)-1  ! Number of NNZs we have accumulated so far

      ndof_proc(1) = vtxdist(lmpi_id+2) - vtxdist(lmpi_id+1)

    endif

! Now move all other data to the master too

    rank_loop : do ipe = 1, lmpi_nproc-1

! First xadj

      dim1 = vtxdist(lmpi_id+2) - vtxdist(lmpi_id+1) + 1
      call lmpi_bcast(dim1,ipe)

      if ( lmpi_master ) then

        ndof_proc(ipe+1) = dim1-1

        allocate(xadj_buffer(dim1))
        call lmpi_recv(xadj_buffer,dim1,ipe,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_recv() in serial_metis.'
          call lmpi_die
          stop
        endif

      else if ( lmpi_id == ipe ) then

        call lmpi_send(adj,dim1,0,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_send() in serial_metis.'
          call lmpi_die
          stop
        endif

      endif

! Now adjncy

      if ( adj_key == 1 ) then
        dim1 = adj(vtxdist(lmpi_id+2) - vtxdist(lmpi_id+1) + 1) - 1
      else if ( adj_key == 2 ) then
        dim1 = adj(vtxdist(lmpi_id+2)) - 1
      endif

      call lmpi_bcast(dim1,ipe)

      if ( lmpi_master ) then

        allocate(adjncy_buffer(dim1))
        call lmpi_recv(adjncy_buffer,dim1,ipe,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_recv() in serial_metis.'
          call lmpi_die
          stop
        endif

      else if ( lmpi_id == ipe ) then

        call lmpi_send(adjncy,dim1,0,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_send() in serial_metis.'
          call lmpi_die
          stop
        endif

      endif

! Store them - xadj must be offset by total number of nonzeros so far

      if ( lmpi_master ) then

        offset = counter2

        do i = 1, size(xadj_buffer,1)-1

          jstart = xadj_buffer(i)
          jend   = xadj_buffer(i+1)-1

          counter1 = counter1 + 1

          global_xadj(counter1) = xadj_buffer(i) + offset

          do j = jstart, jend
            counter2 = counter2 + 1
            global_adjncy(counter2) = adjncy_buffer(j)
          end do

        end do

      endif

! Clean up for next round

      if ( lmpi_master ) deallocate(xadj_buffer,adjncy_buffer)

    end do rank_loop

! Sanity checks

    if ( lmpi_master ) then

      if ( counter1 /= global_ndof ) then
        write(*,*) 'Error: counter1 /= global_ndof in serial_metis.'
        call lmpi_die
        stop
      endif

      if ( counter2 /= total_size ) then
        write(*,*) 'Error: counter2 /= total_size in serial_metis.', counter2, &
                   total_size
        call lmpi_die
        stop
      endif

    endif

! Add the last entry onto xadj

    if ( lmpi_master ) global_xadj(counter1+1) = counter2 + 1

! Call Metis from master

    if ( lmpi_master ) then

      allocate(adjwgt(size(global_adjncy,1)))

      nvtxs                          = global_ndof
      ncon                           = 1
      vwgt(:)                        = 1
      vsize(:)                       = 1
      adjwgt(:)                      = 1
      tpwgts(:)                      = 1.0_r4 / real(lmpi_nproc,r4)
      ubvec(1)                       = 1.001_r4
      options(:)                     = -1
      options(metis_numbering_entry) = 1  ! Use Fortran-style numbering

      call metis_partgraphkway(nvtxs, ncon, global_xadj, global_adjncy, vwgt,  &
                               vsize, adjwgt, lmpi_nproc, tpwgts, ubvec,       &
                               options, objval, part)

      write(*,'(a,i0,a)')'     ... Total edge cut from serial Metis = ',objval,&
                         '...'

      deallocate(adjwgt)

    endif

! Clean up the Metis-related stuff

    if ( lmpi_master ) deallocate(global_xadj,global_adjncy)

! Distribute the partition vector to the other ranks: set the master's value
! then send out the others

    if ( lmpi_master ) then
      metis_data(1:ndof_proc(1)) = part(1:ndof_proc(1))
      offset = ndof_proc(1)
    endif

    do ipe = 1, lmpi_nproc-1

      if ( lmpi_id == ipe ) then

        call lmpi_recv(metis_data,size(metis_data,1),0,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_recv() in serial_metis.'
          call lmpi_die
          stop
        endif

      else if ( lmpi_master ) then

        indx1 = offset + 1
        indx2 = indx1 + ndof_proc(ipe+1) - 1

        call lmpi_send(part(indx1:indx2),ndof_proc(ipe+1),ipe,1,ierr)

        if ( ierr /= lmpi_success ) then
          write(*,*) 'Error in lmpi_send() in serial_metis.'
          call lmpi_die
          stop
        endif

        offset = offset + ndof_proc(ipe+1)

      endif

    end do

    if ( lmpi_master ) then
      if ( offset /= global_ndof ) then
        write(*,*) 'Error: final offset /= global_ndof in serial_metis.'
        call lmpi_die
        stop
      endif
    endif

  end subroutine serial_metis
#endif


!============================== MIGRATE_DRIVER ===============================80
!
!  Heuristically moves nodes to desired partition for multiblock interfaces
!  and periodic boundaries.  Resulting load imbalance is not considered.
!
!=============================================================================80
  subroutine migrate_driver(grid)

    use grid_types,      only : grid_type
    use multiblocks,     only : multiblock, n_multiblock_pairs, multiblock_pairs
    use periodics,       only : periodic, periodic1a, periodic1b
    use nml_periodicity, only : periodic_tol
    use bc_names,        only : bc_is_internal
    use lmpi,            only : lmpi_master, lmpi_die
    use allocations,     only : my_alloc_ptr
    use nml_multiblock,  only : multiblock_tol

    type(grid_type), intent(in) :: grid

    integer :: ib, jb

    logical :: problem, found_match

    logical, dimension(grid%nbound) :: boundary_matched_up

  continue

    if ( periodic ) then

      call migrate_engine(grid,periodic1a,periodic1b,1,problem,periodic_tol)

      if ( problem ) then
        write(*,*) 'migrate_driver : Problem finding periodic node matches.'
        call lmpi_die
        stop
      else
        if (lmpi_master) then
          write(*,'(a,i0,a,i0)')                                               &
                   '     ... Migrated according to periodic boundaries: ',     &
                   periodic1a, ' and ', periodic1b
        endif
      endif

    endif

    if ( multiblock ) then

      call my_alloc_ptr(multiblock_pairs,2,grid%nbound/2)

      boundary_matched_up(:) = .false.

      bound_loop : do ib = 1, grid%nbound

        if ( .not.bc_is_internal(grid%bc(ib)%ibc) ) cycle bound_loop

        if ( boundary_matched_up(ib) ) cycle bound_loop

        found_match = .false.
        search_others : do jb = 1, grid%nbound
          if ( ib == jb ) cycle search_others
          if ( .not.bc_is_internal(grid%bc(jb)%ibc) ) cycle search_others
          if ( boundary_matched_up(jb) ) cycle search_others
          call migrate_engine(grid,ib,jb,2,problem,multiblock_tol)
          if ( problem ) cycle search_others
          found_match = .true.
          n_multiblock_pairs = n_multiblock_pairs + 1
          multiblock_pairs(1,n_multiblock_pairs) = ib
          multiblock_pairs(2,n_multiblock_pairs) = jb
          boundary_matched_up(ib) = .true.
          boundary_matched_up(jb) = .true.
          exit search_others
        end do search_others

        if ( found_match ) then
          if (lmpi_master) then
            write(*,'(a,i0,a,i0)')                                             &
                     '     ... Migrated according to interface boundaries: ',  &
                     ib, ' and ', jb
          endif
        else
          if (lmpi_master) write(*,*) 'Error matching up interface boundary:',ib
          call lmpi_die
          stop
        endif

      end do bound_loop

    endif

  end subroutine migrate_driver


!============================== MIGRATE_ENGINE ===============================80
!
!  Heuristically moves nodes to desired partition for multiblock interfaces
!  and periodic boundaries.  Resulting load imbalance is not considered.
!
!=============================================================================80
  subroutine migrate_engine(grid,bc1,bc2,move_type,problem,tol)

    use grid_types,  only : grid_type
    use kinddefs,    only : dp
    use lmpi,        only : lmpi_id, lmpi_nproc, lmpi_bcast, lmpi_reduce,      &
                            lmpi_die
    use nml_periodicity, only : periodic_dir

    integer, intent(in) :: bc1, bc2, move_type

    real(dp), intent(in) :: tol

    logical, intent(out) :: problem

    type(grid_type), intent(in) :: grid

    integer :: nhead, ntail, total_nbnodesa, countera, i, j, inode, ind, rank
    integer :: total_nbnodesb, counterb, nsize
    integer :: countera_face, counterb_face
    integer :: total_nbnodes

    integer, dimension(:), allocatable :: nbnodesa, nbnodesb
    integer, dimension(:), allocatable :: alist, blist
    integer, dimension(:), allocatable :: ownera, ownerb
    integer, dimension(:), allocatable :: node_matched
    integer, dimension(:), allocatable :: node_count
    integer, dimension(:), allocatable :: itemp

    integer, dimension(:), allocatable :: bina
    integer, dimension(:), allocatable :: binb
    integer, dimension(:), allocatable :: ibnodea
    integer, dimension(:), allocatable :: ibnodeb

    real(dp) :: xa,ya,za,xb,yb,zb,dist

    real(dp), dimension(:,:), allocatable :: xyza, xyzb
    real(dp), dimension(:,:), allocatable :: rtemp

    logical :: found_match

  continue

    problem = .false.

    if ( lmpi_nproc == 1 ) return

    dist = 0.0_dp

    nhead = pp_nhead(lmpi_id)
    ntail = pp_ntail(lmpi_id)
    nsize = pp_nsize(lmpi_id)

!------------------------------------------------------------------------------
!  Attempt to reduce the n-squared magnitude of the boundary node matchups
!  Loop through all the faces of boundary 1, eliminate duplicates on
!  the local node and load a local ibnode array
    countera_face = 0
    if ( grid%bc(bc1)%nbfacet + grid%bc(bc1)%nbfaceq == 0 ) then
      allocate(ibnodea(1)); ibnodea = 0
    else
      allocate(ibnodea(grid%bc(bc1)%nbfacet*3              &
                     + grid%bc(bc1)%nbfaceq*4)); ibnodea = 0
      allocate(bina(grid%nnodesg));              bina    = 0

      do i = 1, grid%bc(bc1)%nbfacet
        do j = 1, 3
          if ( bina(grid%bc(bc1)%f2ntb(i,j)) == 0 ) then
            bina(grid%bc(bc1)%f2ntb(i,j)) = 1
            countera_face                 = countera_face + 1
                         if ( countera_face > size(ibnodea) ) &
                         write(*,*) 'increase dimension a',lmpi_id
            ibnodea(countera_face)        = grid%bc(bc1)%f2ntb(i,j)
          endif
        end do
      end do

      do i = 1, grid%bc(bc1)%nbfaceq
        do j = 1, 4
          if ( bina(grid%bc(bc1)%f2nqb(i,j)) == 0 ) then
            bina(grid%bc(bc1)%f2nqb(i,j)) = 1
            countera_face                 = countera_face + 1
                         if ( countera_face > size(ibnodea) ) &
                         write(*,*) 'increase dimension a',lmpi_id
            ibnodea(countera_face)        = grid%bc(bc1)%f2nqb(i,j)
          endif
        end do
      end do

      deallocate(bina)
    end if

!-----------------------------------------------------------------------------80
!  Loop through all the faces of boundary 2, eliminate duplicates on the
!  local node and load a local ibnode array

    counterb_face = 0
    if ( grid%bc(bc2)%nbfacet + grid%bc(bc2)%nbfaceq == 0 ) then
      allocate(ibnodeb(1)); ibnodeb = 0
    else
      allocate(ibnodeb(grid%bc(bc2)%nbfacet*3              &
                     + grid%bc(bc2)%nbfaceq*4)); ibnodeb = 0
      allocate(binb(grid%nnodesg));              binb    = 0

      do i = 1, grid%bc(bc2)%nbfacet
        do j = 1, 3
          if ( binb(grid%bc(bc2)%f2ntb(i,j)) == 0 ) then
            binb(grid%bc(bc2)%f2ntb(i,j)) = 1
            counterb_face                  = counterb_face + 1
                          if ( counterb_face > size(ibnodeb) )                 &
                          write(*,*) 'increase dimension b',lmpi_id
            ibnodeb(counterb_face)         = grid%bc(bc2)%f2ntb(i,j)
          endif
        end do
      end do

      do i = 1, grid%bc(bc2)%nbfaceq
        do j = 1, 4
          if ( binb(grid%bc(bc2)%f2nqb(i,j)) == 0 ) then
            binb(grid%bc(bc2)%f2nqb(i,j)) = 1
            counterb_face                  = counterb_face + 1
                          if ( counterb_face > size(ibnodeb) )                 &
                          write(*,*) 'increase dimension b',lmpi_id
            ibnodeb(counterb_face)         = grid%bc(bc2)%f2nqb(i,j)
          endif
        end do
      end do

      deallocate(binb)
    end if

! Allocate an array to hold the number of boundary nodes on each processor
! Nodes will appear multiple times, but thats ok
! UPDATE: There shouldn't be any duplicates now...

    allocate(nbnodesa(lmpi_nproc+1)); nbnodesa(:) = 0
    allocate(nbnodesb(lmpi_nproc+1)); nbnodesb(:) = 0

    nbnodesa(lmpi_id+1) = countera_face
    nbnodesb(lmpi_id+1) = counterb_face

! Reduce so everyone knows about everyone else's value

    allocate(itemp(lmpi_nproc+1))
    call lmpi_reduce(nbnodesa,itemp)
    call lmpi_bcast(itemp)
    nbnodesa(:) = itemp(:)
    deallocate(itemp)

    allocate(itemp(lmpi_nproc+1))
    call lmpi_reduce(nbnodesb,itemp)
    call lmpi_bcast(itemp)
    nbnodesb(:) = itemp(:)
    deallocate(itemp)


! Figure out the starting point for my entries since they will be
! shifted by the number of hits on each processor

    countera = 0
    do i = 1, lmpi_id
      countera = countera + nbnodesa(i)
    end do

    counterb = 0
    do i = 1, lmpi_id
      counterb = counterb + nbnodesb(i)
    end do

    total_nbnodesa = sum(nbnodesa(:))
    total_nbnodesb = sum(nbnodesb(:))

! Allocate an array by this size to hold them

    allocate(alist(total_nbnodesa)); alist(:) = 0
    allocate(blist(total_nbnodesb)); blist(:) = 0

! Store nodes but there still may be duplicates due to how the mesh
! has been parsed out at this point.  Remember we are still preprocessing
! and have not yet hit metis
    do i = 1, countera_face
      countera        = countera + 1
      alist(countera) = ibnodea(i)
    end do

    do i = 1, counterb_face
      counterb        = counterb + 1
      blist(counterb) = ibnodeb(i)
    end do

    allocate(itemp(total_nbnodesa))
    call lmpi_reduce(alist, itemp)
    call lmpi_bcast(itemp)
! now sift for more duplicates now that all the nodes have shared the node info
      allocate(bina(grid%nnodesg)); bina = 0
      countera = 0
      do i = 1, total_nbnodesa
        if ( bina(itemp(i)) == 0 ) then
          bina(itemp(i)) = 1
          countera      = countera + 1
          alist(countera) = itemp(i)
        end if
      end do
      deallocate(bina)
    deallocate(itemp)


    allocate(itemp(total_nbnodesb))
    call lmpi_reduce(blist, itemp)
    call lmpi_bcast(itemp)
! now sift for more duplicates now that all the nodes have shared the node info
      allocate(binb(grid%nnodesg)); binb = 0
      counterb = 0
      do i = 1, total_nbnodesb
        if ( binb(itemp(i)) == 0 ) then
          binb(itemp(i)) = 1
          counterb      = counterb + 1
          blist(counterb) = itemp(i)
        end if
      end do
      deallocate(binb)
    deallocate(itemp)

! At this point there has to be a match to the number of nodes between
! boundary a and boundary b.

    total_nbnodes = countera

    if ( lmpi_master ) then
      write(*,'(a,i12)')                                             &
     '     ... Migrate_engine: number of nodes found on boundary:  ',&
      total_nbnodes
    endif

! Now create a corresponding array to hold the 1-based rank number who owns
! each node according to the current metis partition vector
! Allocate an array by this same size to also hold the xyz's

    allocate(node_matched(total_nbnodes)); node_matched(:) = 0
    allocate(ownera(total_nbnodes)); ownera(:) = 0
    allocate(ownerb(total_nbnodes)); ownerb(:) = 0
    allocate(xyza(3,total_nbnodes)); xyza(:,:) = 0.0_dp
    allocate(xyzb(3,total_nbnodes)); xyzb(:,:) = 0.0_dp

! Go through previously-established list of boundary nodes and if I own one,
! bookkeep myself as the rank who owns it

    do i = 1, total_nbnodes
      inode = alist(i)
      if (inode >= nhead .and. inode <= ntail ) then
        ind = inode - nhead + 1
        ownera(i) = metis_data(ind)  ! 1-based
        xyza(1,i) = grid%x(ind)
        xyza(2,i) = grid%y(ind)
        xyza(3,i) = grid%z(ind)
      endif
    end do

    do i = 1, total_nbnodes
      inode = blist(i)
      if (inode >= nhead .and. inode <= ntail ) then
        ind = inode - nhead + 1
        ownerb(i) = metis_data(ind)  ! 1-based
        xyzb(1,i) = grid%x(ind)
        xyzb(2,i) = grid%y(ind)
        xyzb(3,i) = grid%z(ind)
      endif
    end do


! Now do a reduce on the owner and xyz arrays and that should
! fully populate them

    allocate(itemp(total_nbnodes))
    call lmpi_reduce(ownera, itemp)
    call lmpi_bcast(itemp)
    ownera(:) = itemp(:)
    deallocate(itemp)

    allocate(itemp(total_nbnodes))
    call lmpi_reduce(ownerb, itemp)
    call lmpi_bcast(itemp)
    ownerb(:) = itemp(:)
    deallocate(itemp)

    allocate(rtemp(3,total_nbnodes))
    call lmpi_reduce(xyza, rtemp)
    call lmpi_bcast(rtemp)
    xyza(:,:) = rtemp(:,:)
    deallocate(rtemp)

    allocate(rtemp(3,total_nbnodes))
    call lmpi_reduce(xyzb, rtemp)
    call lmpi_bcast(rtemp)
    xyzb(:,:) = rtemp(:,:)
    deallocate(rtemp)

! Now walk through all of the global nodes in the b list and search for
! a match in the a list based on the appropriate coordinates.
! This is an n-squared search but these boundaries should not
! contain too many points, even though the points are bookkept multiple times.
! If a match is found, change the entry in the ownerb array to match that of
! the ownera array.  If a match is not found, then thats an error

    do i = 1, total_nbnodes

      inode = blist(i)
      xb = xyzb(1,i)
      yb = xyzb(2,i)
      zb = xyzb(3,i)

      found_match = .false.

      search_a_plane : do j = 1, total_nbnodes

        if ( node_matched(j) == 1 ) then
          found_match = .true.
          cycle search_a_plane
        endif

        xa = xyza(1,j)
        ya = xyza(2,j)
        za = xyza(3,j)

        if ( move_type == 1 ) then         ! periodic case

          select case ( periodic_dir(1) )
          case(1)
            dist = sqrt((ya-yb)**2 + (za-zb)**2)
          case(2)
            dist = sqrt((xa-xb)**2 + (za-zb)**2)
          case(3)
            dist = sqrt((xa-xb)**2 + (ya-yb)**2)
          end select

        else if ( move_type == 2 ) then    ! multiblock interface

          dist = sqrt( (xa-xb)**2 + (ya-yb)**2 + (za-zb)**2 )

        else                               ! unknown

          write(*,*) 'Unknown move_type in migrate_engine: ', move_type
          call lmpi_die
          stop

        endif

        if ( dist < tol ) then

          ownerb(i)            = ownera(j)
          found_match          = .true.
          node_matched(j)      = 1
          cycle search_a_plane

        endif

      end do search_a_plane

      if ( .not. found_match ) then
        problem = .true.
        deallocate(ownera,xyza,xyzb,blist,ownerb)
        return
      endif

    end do

    if ( lmpi_master ) then
      write(*,'(a,i12)')                                             &
      '    ... Migrate_engine: number of nodes matched on boundary:',&
      sum(node_matched)
    endif

! Now finally go back through the listb array and if the global
! node is on my processor, update the metis_data value for that point

    do i = 1, total_nbnodes
      inode = blist(i)
      if (inode >= nhead .and. inode <= ntail ) then
        ind = inode - nhead + 1
        metis_data(ind) = ownerb(i)  ! 1-based
      endif
    end do

    deallocate(xyzb)
    deallocate(xyza)
    deallocate(ownerb)
    deallocate(ownera)
    deallocate(node_matched)

    deallocate(blist)
    deallocate(alist)
    deallocate(nbnodesb)
    deallocate(nbnodesa)

! Make sure every processor still has at least one grid point or else that would
! probably be bad

    allocate(node_count(lmpi_nproc)); node_count(:) = 0

    do i = 1, nsize
      rank = metis_data(i)
      node_count(rank) = node_count(rank) + 1
    end do

    allocate(itemp(lmpi_nproc))
    call lmpi_reduce(node_count, itemp)
    call lmpi_bcast(itemp)
    node_count(:) = itemp(:)
    deallocate(itemp)

    do i = 1, lmpi_nproc
      if ( node_count(i) == 0 ) then
        write(*,*) 'migrate_engine: processor ended up with zero nodes:', i
        call lmpi_die
        stop
      endif
    end do

    deallocate(node_count)

    deallocate(ibnodeb)
    deallocate(ibnodea)

  end subroutine migrate_engine


!============================== LINES_CHECK_SINGLETON ========================80
!
! Check isolated regions and move nodes to partition (based on constraints).
! This version uses a swallow approach to detect singletons.
!
! Condense adj (incoming)
!   size of          : s_adj, s_adjncy, s_metis
!   actual values in : n_adj, n_adjncy,! n_metis
!   values           :   adj,   adjncy, metis
!
! TBD:
! 1. Must check for conflicting changes.
!    I.e., PE0 changes 10 to 100, while PEn changes 100 to xxx
! 2. Change tag (to bits), currently uses max(adjncy) which could get large.
!
!=============================================================================80

  subroutine lines_check_singleton(s_adj, s_adjncy, s_metis,                   &
             n_adj, n_adjncy, adj, adjncy, node_weight, metis)

  use sort, only : binary_search

    integer,                      intent(in)    :: s_adj, s_adjncy, s_metis
    integer,                      intent(in)    :: n_adj, n_adjncy!, n_metis
    integer, dimension(s_adj),    intent(in)    :: adj
    integer, dimension(s_adjncy), intent(in)    :: adjncy
    integer, dimension(s_metis),  intent(in)    :: node_weight
    integer, dimension(s_metis),  intent(inout) :: metis

    integer :: i,j,k,m, ict, ipe, nmigrate, iloop, nl2g, max_nadj
    integer :: max_adjncy, met1, met2, maxi, basei

    integer, dimension(:), allocatable :: l2g, metl2g, temp
    integer, dimension(:), allocatable ::     nsize,     nhead,     ntail
    integer, dimension(:), allocatable :: new_nsize, new_nhead, new_ntail
    integer, dimension(:), allocatable :: ct_arr, sz_arr, sz_arr_all
    integer, dimension(:), allocatable :: wt_arr, temp2
    integer, dimension(:), allocatable :: ct_single, tag_met

  ! TBD change
  ! logical :: b1, found
  ! integer :: bsize, tag_size, word, node, ict
  ! real(dp):: bsize_inv

    integer(system_i1), dimension(:), allocatable :: tag

    integer, parameter :: max_migration_loop = 5
    integer, parameter :: debug  = 0
    integer, parameter :: inform = 0

    continue

    allocate(nsize(0:lmpi_nproc-1)); nsize = 0
    call lmpi_gather(s_adj-1,nsize)
    call lmpi_bcast(nsize)

    allocate(nhead(0:lmpi_nproc-1)); nhead = 0
    nhead(0) = 1
    do i = 1,lmpi_nproc-1
       nhead(i) = nhead(i-1) + nsize(i-1)
    end do

    allocate(ntail(0:lmpi_nproc-1)); ntail = 0
    do i = 0,lmpi_nproc-1
       ntail(i) = (nhead(i) + nsize(i))-1
    end do

!   Do NOT remove the debug statements
    if (debug == 1) then
!      write(200+lmpi_id,*) nsize
!      write(200+lmpi_id,*) nhead
!      write(200+lmpi_id,*) ntail
!
!      if (lmpi_master) write(*,*)"DANA lines_check_singleton (2k,3k)"
       do i = 1,n_adj-1
!!        write(2000+lmpi_id,'(i0,1x,i0)') nhead(lmpi_id)-1+i,metis(i)
          write(3000+lmpi_id,'(1x,i0," : ",i0," :: ",100(i0,1x))')             &
             nhead(lmpi_id)-1+i,adj(i+1)-adj(i),adjncy(adj(i):adj(i+1)-1)
       end do
    end if

! Allocate and sum adj and adjncy on each target pe.

    allocate(ct_arr(0:lmpi_nproc-1)); ct_arr = 0
    allocate(sz_arr(0:lmpi_nproc-1)); sz_arr = 0
    allocate(wt_arr(0:lmpi_nproc-1)); wt_arr = 0

    nl2g = 0
    if (nsize(lmpi_id) > 0) then ! has contributions

       max_adjncy =  maxval(adjncy)
       if (ntail(lmpi_id) > max_adjncy) max_adjncy = ntail(lmpi_id)

       ! tag adj and adjncy
       allocate(tag(max_adjncy)); tag = 0
       do i = 1,n_adjncy                    ! tag adjncy
          tag(adjncy(i)) = 1
       end do
       do i = nhead(lmpi_id),ntail(lmpi_id) ! tag adj
          tag(i) = 1
       end do

      ! Count
      do i = 1,max_adjncy
         if (tag(i) == 1) nl2g = nl2g + 1
      end do
      allocate(l2g(nl2g)); l2g = 0
      allocate(metl2g(nl2g)); metl2g = 0

      ! Populate
      nl2g = 0
      do i = 1,max_adjncy
         if (tag(i) == 1) then
            nl2g = nl2g + 1
            l2g(nl2g) = i
         end if
      end do
      deallocate(tag)

    end if

    ! Gather metis data (for adj, adjncy)
    maxi = maxval(nsize)
    allocate(temp(maxi)); temp = 0
    do ipe = 0,lmpi_nproc-1
       if (nsize(ipe) > 0) then
          temp = 0
          if (ipe == lmpi_id) temp(1:nsize(ipe)) = metis(1:nsize(ipe))
          call lmpi_bcast(temp,ipe)
          do i = 1,nl2g
             if (l2g(i) < nhead(ipe)) cycle
             if (l2g(i) > ntail(ipe)) exit
             j = (l2g(i)-nhead(ipe))+1
             metl2g(i) = temp(j)
          end do
       end if
    end do
    deallocate(temp)
    ! Do NOT remove debug statements
    ! if (debug == 1) then
    !    do i = 1,nl2g
    !       write(17000+lmpi_id,*) l2g(i),metl2g(i)
    !    end do
    ! end if

    deallocate(nsize,ntail)

!-------------------------------------------------------------------------------
! Metis and met will change in this loop. Metis is the global metis data;
! while metl2g is the l2g metis data (level0 and level1) on current PE.

    allocate(tag_met(n_adj-1))
    tag_met = -1              ! -1 : none; 0-lmpi_nproc : value

    allocate(new_nsize(0:lmpi_nproc-1)); new_nsize = 0
    allocate(new_nhead(0:lmpi_nproc-1)); new_nhead = 0
    allocate(new_ntail(0:lmpi_nproc-1)); new_ntail = 0
    allocate(sz_arr_all(0:lmpi_nproc-1)); sz_arr_all = 0

    migration_loop_name : do iloop = 1,max_migration_loop

      nmigrate = 0
      tag_met  = -1

      ct_arr = 0
      sz_arr = 0
      wt_arr = 0
      do i = 1,n_adj-1
         ipe = metis(i)-1
         ct_arr(ipe) = ct_arr(ipe) + 1
         sz_arr(ipe) = sz_arr(ipe) + adj(i+1)-adj(i)
         wt_arr(ipe) = wt_arr(ipe) + node_weight(i)
      end do

      ! Sum values over all PEs, and compute post-ParMetis
      ! (i.e., new_nsize, new_nhead, new_ntail).

      new_nsize = 0
      call lmpi_reduce(ct_arr,new_nsize)
      call lmpi_bcast(new_nsize)

      new_nhead = 0
      new_nhead(0) = 1
      do i = 1,lmpi_nproc-1
         new_nhead(i) = new_nhead(i-1) + new_nsize(i-1)
      end do

      new_ntail = 0
      do i = 0,lmpi_nproc-1
         new_ntail(i) = (new_nhead(i) + new_nsize(i))-1
      end do

      ! Do NOT remove
      ! if (debug == 1) then
      !   write(200+lmpi_id,*)'----------- new_nsize,nh,nt ',iloop
      !   write(200+lmpi_id,*) new_nsize
      !   write(200+lmpi_id,*) new_nhead
      !   write(200+lmpi_id,*) new_ntail
      ! end if

      sz_arr_all = 0
      call lmpi_reduce(wt_arr,sz_arr_all)
      if ((inform == 1).and.(lmpi_master))                                     &
         write(*,'("   ...Weighted balance ",i0," : ",100(i0,1x))')            &
           iloop,sz_arr_all

      ict = 0
      if (nl2g > 0) then ! has potential candidates
         ! if (debug == 1) write(16000+lmpi_id,*)'------------- ',iloop

         max_nadj = 0
         do i = 1,n_adj-1
            j = adj(i+1)-adj(i)
            if (j > max_nadj) max_nadj = j
         end do
         allocate(temp2(max_nadj)); temp2 = 0

         ! All level0 values will be contiguous in l2g, so just find the
         ! first value (basei) and off the remander (instead of a search).

         basei = binary_search(nl2g,l2g,nhead(lmpi_id))
         if (basei == 0) call lmpi_conditional_stop(1,                         &
            'Assert failed binary_search 8a')
         basei = basei - 1

         ict = 0
         outer: do i = 1,s_adj-1
            met1 = metl2g(basei + i) ! level0 (adj)
            m = 0
            do j = adj(i),adj(i+1)-1
               k = binary_search(nl2g,l2g,adjncy(j)) ! adjncy
               if (k == 0) call lmpi_conditional_stop(1,                       &
                  'Assert failed binary_search 9a')
               met2 = metl2g(k)
               if (met1 == met2) cycle outer
               m = m + 1
               temp2(m) = met2 ! collect adj metis values
            end do

            ! HERE Use any value in temp2 is repeated more that once.
            !write(*,*)"MIGRATE ",lmpi_id,i,basei+i,tag_met(i),metis(i),temp2(1)

            tag_met(i)      = temp2(1) ! array to count and retrieve pass values
            metis(i)        = temp2(1) ! original metis array
            metl2g(basei+i) = temp2(1) ! order metis data (BS level 01)
            ict = ict + 1

            !if (debug == 1)                                                   &
            ! write(16000+lmpi_id,'(1x,"singleton found ",i0,":",100(i0,1x))') &
            !   i,adjncy(adj(i):adj(i+1)-1)
            ! j = adj(i+1)-adj(i)
            ! write(16000+lmpi_id,'("met ",i0," : ",i0," :: ",100(i0,1x))')    &
            !   node_weight(i),met1,temp2(1:j)

         end do outer
         deallocate(temp2)
      end if
      call lmpi_conditional_stop(0,'Assert failed binary_search')

      call lmpi_reduce(ict,nmigrate)
      call lmpi_bcast(nmigrate)
      if (lmpi_master)                                                         &
         write(*,*)"  ...Total singletons ",nmigrate," loop ",iloop
      if (nmigrate == 0) exit

      allocate(ct_single(0:lmpi_nproc-1)); ct_single = 0
      call lmpi_gather(ict,ct_single)
      call lmpi_bcast(ct_single)
      maxi = maxval(ct_single)

      ! allocate 2 worst case. First half is adj values; second is metis data.
      allocate(temp(maxi*2)); temp = 0
      do ipe = 0,lmpi_nproc-1
         if (ct_single(ipe) > 0) then
            temp = 0
            if (lmpi_id == ipe) then
               ict = 0
               do i = 1,n_adj-1
                  if (tag_met(i) /= -1) then
                     ict = ict + 1
                     temp(ict)      = (nhead(lmpi_id)+i)-1 ! TBD basei+i
                     temp(ict+maxi) = tag_met(i)
                  end if
               end do
               ! Do NOT remove
               !if (debug == 1) then
               !   write(*,*)"PASS ",lmpi_id,ict,temp(1:ict)," : ",
               !     temp(maxi+1:maxi+ict)
               !end if
            end if
            call lmpi_bcast(temp,ipe)
            do i = 1,ct_single(ipe)
               j = binary_search(nl2g,l2g,temp(i))
               !if (debug == 1) then
               !   if (j > 0) write(*,*)"SETTING ",
               !      lmpi_id,temp(i),j,metl2g(j),temp(i+maxi)
               !end if
               if (j > 0) metl2g(j) = temp(i+maxi)

            end do
         end if
      end do
      deallocate(temp, ct_single)

    end do migration_loop_name ! iloop

    if (debug == 1) then ! concat, sort, uniq (csu)
       do i = 1,nl2g
          write(37000+lmpi_id,*) l2g(i),metl2g(i)
       end do
    end if

    deallocate(sz_arr_all, new_ntail, new_nhead, new_nsize)
    deallocate(wt_arr, sz_arr, ct_arr)
    if (allocated(tag_met)) deallocate(tag_met)
    if (allocated(metl2g))  deallocate(metl2g)
    if (allocated(l2g))     deallocate(l2g)

  end subroutine lines_check_singleton

!============================== LINES_CHECK_OBJECTS ==========================80
!
! Check isolated regions.
! This is to be called after ParMetis (with a partition vector), but without
! a condensed adjancency. I.e., all PEs have valid (non_condensed0 adjncy lists.
!
!=============================================================================80

  subroutine lines_check_objects( ideal_objects )

  use sort, only : binary_search

    logical, intent(out) :: ideal_objects

    integer :: i,j,k,m,i2,ict,ipe,ioff !,soff
    integer :: object_number, nleft, ifound, psize, qsize
    integer :: temp_max_nsize, temp_max_adjncy

    integer, dimension(:), allocatable :: ct_arr, sz_arr, sz_arr_all
    integer, dimension(:), allocatable :: temp, object

    integer :: s_new_adj, s_new_adjncy, s_new_l2g
    integer, dimension(:), allocatable :: new_nsize, new_nhead
    integer, dimension(:), allocatable :: new_adj, new_adjncy, new_l2g

    integer, dimension(:), allocatable :: temp_adj, temp_adjncy, temp_metis,   &
                                          n_adjncy_arr

    integer, parameter :: debug  = 0
    integer, parameter :: inform = 0

    continue

    if (lmpi_master) write(*,*)"  ...Start lines_check_objects"

   !if (debug == 1) then
   !   write(200+lmpi_id,*) pp_nsize
   !   write(200+lmpi_id,*) pp_nhead
   !   write(200+lmpi_id,*) pp_ntail
   !   do i = 1,pp_nsize(lmpi_id)
   !      write(3000+lmpi_id,'(1x,i0," : ",i0," :: ",100(i0,1x))')             &
   !        pp_nhead(lmpi_id)-1+i,adj(i+1)-adj(i),adjncy(adj(i):adj(i+1)-1)
   !   end do
   !end if

! Allocate and sum adj and adjncy on each target pe.

    allocate(ct_arr(0:lmpi_nproc-1)); ct_arr = 0
    allocate(sz_arr(0:lmpi_nproc-1)); sz_arr = 0

    do i = 1,pp_nsize(lmpi_id)
       ipe = metis_data(i)-1
       ct_arr(ipe) = ct_arr(ipe) + 1
       sz_arr(ipe) = sz_arr(ipe) + (adj(i+1)-adj(i))
    end do

! Sum the values over all PEs, and compute post-ParMetis nsize,nhead,ntail.

    allocate(new_nsize(0:lmpi_nproc-1)); new_nsize = 0
    allocate(new_nhead(0:lmpi_nproc-1)); new_nhead = 0

    call lmpi_reduce(ct_arr,new_nsize)
    call lmpi_bcast(new_nsize)
    deallocate(ct_arr)

    s_new_l2g = new_nsize(lmpi_id)
    s_new_adj = s_new_l2g + 1

    allocate(sz_arr_all(0:lmpi_nproc-1)); sz_arr_all = 0
    call lmpi_reduce(sz_arr,sz_arr_all)
    deallocate(sz_arr)
    call lmpi_bcast(sz_arr_all)

    s_new_adjncy = sz_arr_all(lmpi_id)+1 ! Yes, +1

   ! new_nhead(0) = 1
   ! do i = 1,lmpi_nproc-1
   !    new_nhead(i) = new_nhead(i-1) + new_nsize(i-1)
   ! end do
   !
   !if (debug == 1) then
   !   write(200+lmpi_id,*)'----------- new_nsize,nh,nt'
   !   write(200+lmpi_id,*) new_nsize
   !   write(200+lmpi_id,*) new_nhead
   !end if

    deallocate(new_nsize, new_nhead)

! Sum the adjncy sz over all PEs, and compute post-ParMetis new_adjncy.

    allocate(new_adj(s_new_adj));       new_adj    = 0
    allocate(new_adjncy(s_new_adjncy)); new_adjncy = 0
    allocate(new_l2g(s_new_l2g));       new_l2g    = 0 ! level0

    temp_max_nsize = maxval(pp_nsize)

    allocate(n_adjncy_arr(0:lmpi_nproc-1)); n_adjncy_arr = 0
    call lmpi_gather(size(adjncy),n_adjncy_arr)
    call lmpi_bcast(n_adjncy_arr)
    temp_max_adjncy = maxval(n_adjncy_arr)

! Compute and allocate maximum size, so allocate max buffer for communication.

    allocate(temp_metis(temp_max_nsize));   temp_metis  = 0
    allocate(temp_adj(temp_max_nsize+1));   temp_adj    = 0
    allocate(temp_adjncy(temp_max_adjncy)); temp_adjncy = 0

! Now pass adj/adjncy to the destination PE based on ParMetis.
! Compute the new l2g.

! *** Validation: cat fort.3??? should match cat/sort fort.13???
!     cat fort.3??? > gort.3
!     cat fort.13??? > gort.13; sort -n gort.13 > gort.13s
!     md5sum gort.3 gort.13s ! should be identical regardless of n_pe

    !write(*,'(" pp_nsize ",100(i0,1x))') pp_nsize
    !write(*,'(" n_adjncy_arr ",100(i0,1x))') n_adjncy_arr

    new_adj(1) = 1
    ioff = 0
   !soff = 0
    ict  = 0
    do ipe = 0,lmpi_nproc-1
       qsize = n_adjncy_arr(ipe)
       psize = pp_nsize(ipe)
       temp_metis  = 0
       temp_adj    = 0
       temp_adjncy = 0
       if (ipe == lmpi_id) then

         !if (debug == 1) write(5000+lmpi_id,*)"psize,qsize ",psize,qsize
         !do i = 1,psize
         !   soff = soff + 1
         !   if (debug == 1) then
         !      write(5000+lmpi_id,'(1x,i0,1x,i0," : ",100(i0,1x))')           &
         !        soff,metis_data(i),adjncy(adj(i):adj(i+1)-1)
         !      write(6000+lmpi_id,'(1x,i0,1x,i0," : ",100(i0,1x))')           &
         !        soff,metis_data(i),adj(i),adj(i+1),adj(i+1)-adj(i)
         !   end if
         !end do

          temp_metis (1:psize)   = metis_data (1:psize)   ! metis
          temp_adj   (1:psize+1) = adj        (1:psize+1) ! adj
          temp_adjncy(1:qsize)   = adjncy     (1:qsize)   ! adjncy
       end if
       call lmpi_bcast(temp_metis,ipe)  ! metis
       call lmpi_bcast(temp_adj,ipe)    ! adj
       call lmpi_bcast(temp_adjncy,ipe) ! adjncy

       do i = 1,psize
          ioff = ioff + 1
          if (temp_metis(i) == lmpi_id+1) then
             ict = ict + 1
             new_l2g(ict) = ioff
             new_adj(ict+1) = new_adj(ict) + (temp_adj(i+1)-temp_adj(i))
             new_adjncy(new_adj(ict):new_adj(ict+1)-1) =                       &
                temp_adjncy(temp_adj(i):temp_adj(i+1)-1)
          endif
       end do
    end do
    ! write(*,*)"END ict ",lmpi_id,ct,new_adj(ict+1)
    deallocate(temp_metis, temp_adj, temp_adjncy, n_adjncy_arr)

   !if (debug == 1) then
   !   do i = 1,s_new_adj-1
   !      write(13000+lmpi_id,'(1x,i0," : ",i0," :: ",100(i0,1x))')            &
   !        new_l2g(i),new_adj(i+1)-new_adj(i),                                &
   !        new_adjncy(new_adj(i):new_adj(i+1)-1)
   !   end do
   !end if

! Now loop over adjncy (knowing new_l2g is level0) and mark level0
! nodes with object number.

    call lmpi_synchronize()

    ! objects :: -1 not processed
    !             0 marked, but have not looked at adjncy
    !             1 to n (object number)

    allocate(object(s_new_l2g)); object = -1

    nleft = s_new_l2g
    object_number = 0
    outer: do i = 1,s_new_l2g ! not really Order(n**2), will exit early
      if (nleft == 0) exit

      ! process all active
      do i2 = 1,s_new_l2g*2 ! will exit earlier
         ict = 0
         do j = 1,s_new_l2g
            if (object(j) == 0) then
               object(j) = object_number
               nleft = nleft - 1
               if (debug == 1)                                                 &
               write(7000+lmpi_id,*) '(0) ',nleft,s_new_l2g,object_number
               do k = new_adj(j),new_adj(j+1)-1
                  m = binary_search(s_new_l2g,new_l2g,new_adjncy(k))
                  if (m > 0) then
                     if (object(m) == -1) then
                        object(m) = 0
                        ict = ict + 1
                     end if
                  end if
               end do
            end if
         end do
         if (ict == 0) exit
      end do
      if (nleft == 0) exit outer
      do j = 1,s_new_l2g
         if (object(j) == 0) write(*,*)"Never happen 1 ",lmpi_id
      end do

      ! process all unprocessed
      object_number = object_number + 1
      ifound = 0
      do j = 1,s_new_l2g
         if (object(j) == -1) then
            object(j) = object_number
            nleft = nleft - 1
            if (debug == 1)                                                    &
            write(7000+lmpi_id,*) '(-1) ',nleft,s_new_l2g,object_number
            do k = new_adj(j),new_adj(j+1)-1
               m = binary_search(s_new_l2g,new_l2g,new_adjncy(k))
               if (m > 0) then
                  if (debug == 1) write(7000+lmpi_id,*) "m ",m
                  if (object(m) == -1) then
                     object(m) = 0
                     ifound = 1
                  end if
               end if
            end do
            ! NOTE: the do i2 loop could/should go here for clarity.
            if (ifound == 1) exit
         end if
      end do
      if (nleft == 0) exit outer
    end do outer
    if (nleft /= 0) write(*,*)"ERROR nleft ",lmpi_id,nleft
    call lmpi_conditional_stop(nleft,"Internal error when counting objects.")

    deallocate(new_adj, new_adjncy, new_l2g)

    call lmpi_max(object_number,i)
    call lmpi_min(object_number,j)
    if (lmpi_master)                                                           &
    write(*,'("   ...Minimum/Maximum objects on any processor: ",2(i0,1x))') j,i

    if (object_number > 1) then
       allocate(temp(object_number)); temp = 0
       do i = 1,s_new_l2g
          temp(object(i)) = temp(object(i)) + 1
       end do
       if (inform == 1) then
          write(*,'(1x,i0," Object count : ",100(i0,1x))')                     &
            lmpi_id,temp(1:object_number)
       end if
       j = minval(temp)
       deallocate(temp)
    else
       j = s_new_l2g
    end if

    call lmpi_min(j,i)
    if (lmpi_master)                                                           &
       write(*,*)"  ...Minimum size of object on any processor: ",i

    deallocate(object)

    j = 0
    if ( object_number == 1 ) j = 1
    call lmpi_reduce(j, i)

    ideal_objects = .false.
    if ( lmpi_master ) then
      if ( i == lmpi_nproc ) ideal_objects = .true.
      write(*,*) ' One object per processor(ideal_objects)=',&
      ideal_objects
    endif

    call lmpi_bcast(ideal_objects)

  end subroutine lines_check_objects

!============================== LINES_CHECK_ISLANDS ==========================80
!
! Check isolated regions and move node to partition (based on constraints).
!
!=============================================================================80

  subroutine lines_check_islands(s_adj,s_adjncy,adj,adjncy,project)

    integer,                      intent(in)    :: s_adj,s_adjncy
    integer, dimension(s_adj),    intent(in)    :: adj
    integer, dimension(s_adjncy), intent(in)    :: adjncy
    character(len=*),             intent(in)    :: project

    integer :: i,j,ct,ipe,ioff,nmigrate
    integer :: s_ia, s_ja, s_l2g, s_metis_new, inode, iloop
    integer, dimension(:), allocatable :: ct_arr, ct_arr_all, sz_arr, sz_arr_all
    integer, dimension(:), allocatable :: t_ia, t_ja, t_l2g
    integer, dimension(:), allocatable :: temp_dt, temp_ia, temp_ja, metis_out
    integer, dimension(:), allocatable :: counts, m_l2g, m_ipe, itemp1, itemp2

    continue

   !if (debug) then
   !   write(*,*)"DANA9 lines_check_islands ",lmpi_id,s_adj-1,pp_nsize(lmpi_id)
   !   do i = 1,s_adj-1
   !      write(2000+lmpi_id,'(i0,1x,i0)') pp_nhead(lmpi_id)-1+i,metis_data(i)
   !      write(3000+lmpi_id,'(i0," : ",i0," :: ",100(i0,1x))')  &
   !            pp_nhead(lmpi_id)-1+i,adj(i+1)-adj(i),adjncy(adj(i):adj(i+1)-1)
   !   end do
   !end if

migration_loop_name : do iloop = 1,migration_loop+1

    nmigrate = 0

    allocate(ct_arr(0:lmpi_nproc-1)); ct_arr = 0
    allocate(sz_arr(0:lmpi_nproc-1)); sz_arr = 0

    do i = 1,s_adj-1
       ipe = metis_data(i)-1
       ct_arr(ipe) = ct_arr(ipe) + 1
       sz_arr(ipe) = sz_arr(ipe) + adj(i+1)-adj(i)
    end do
    allocate(ct_arr_all(0:lmpi_nproc-1)); ct_arr_all = 0
    call lmpi_reduce(ct_arr,ct_arr_all)
    deallocate(ct_arr)
    call lmpi_bcast(ct_arr_all)

    allocate(sz_arr_all(0:lmpi_nproc-1)); sz_arr_all = 0
    call lmpi_reduce(sz_arr,sz_arr_all)
    deallocate(sz_arr)
    call lmpi_bcast(sz_arr_all)

    s_ia  = ct_arr_all(lmpi_id)+1
    s_ja  = sz_arr_all(lmpi_id)
    s_l2g = ct_arr_all(lmpi_id)
    deallocate(ct_arr_all)
    deallocate(sz_arr_all)

    allocate(t_ia(s_ia));   t_ia = 0
    allocate(t_ja(s_ja));   t_ja = 0
    allocate(t_l2g(s_l2g)); t_l2g = 0

    allocate(temp_dt(pp_nsize(0)));   temp_dt = 0
    allocate(temp_ia(pp_nsize(0)+1)); temp_ia = 0
    call lmpi_max(s_adjncy,i)
    call lmpi_bcast(i)
    allocate(temp_ja(i)); temp_ja = 0

    t_ia(1) = 1
    ioff = 0
    ct   = 0
    do ipe = 0,lmpi_nproc-1
       temp_dt = 0
       temp_ia = 0
       temp_ja = 0
       if (ipe == lmpi_id) then
          ! if (pp_nsize(ipe) /= s_adj-1) stop "ERROR1"
          temp_dt(1:s_adj-1)  = metis_data(1:s_adj-1) ! metis_data
          temp_ia(1:s_adj)    = adj(1:s_adj)          ! ia
          temp_ja(1:s_adjncy) = adjncy(1:s_adjncy)    ! ja
       end if
       call lmpi_bcast(temp_dt,ipe) ! metis_data
       call lmpi_bcast(temp_ia,ipe) ! adj
       call lmpi_bcast(temp_ja,ipe) ! adjncy

       do i = 1,pp_nsize(ipe)
          ioff = ioff + 1
          if (temp_dt(i) == lmpi_id+1) then
             ct = ct + 1
             t_l2g(ct) = ioff
             t_ia(ct+1) = t_ia(ct) + (temp_ia(i+1)-temp_ia(i))
             t_ja(t_ia(ct):t_ia(ct+1)-1) = temp_ja(temp_ia(i):temp_ia(i+1)-1)

             !if (debug)                                                       &
             ! write(13000+lmpi_id,'(i0," : ",i0," :: ",100(i0,1x))')          &
             !   ioff,t_ia(ct+1)-t_ia(ct),t_ja(t_ia(ct):t_ia(ct+1)-1)
          endif
       end do
     end do
     deallocate(temp_dt,temp_ia,temp_ja)

     s_metis_new  = s_l2g
     allocate(metis_out(s_l2g)); metis_out = lmpi_nproc

     !if (debug) then
     !   write(*,*)"SZ(metis_data),SZ(t_l2g) ",                                &
     !     size(metis_data),size(t_l2g),s_l2g,s_metis_data,s_metis_new
     !   do i = 1,s_l2g
     !      write(12000+lmpi_id,'(i0,1x,i0)') t_l2g(i),lmpi_id+1
     !   end do
     !end if

    !if (s_l2g > 300000000) then
     call pparty_check_data_stats(s_l2g, s_ia, s_ja, s_metis_new,              &
                                  t_l2g, t_ia, t_ja,   metis_out, project)
    !end if

! Check and propagate the migration data

     ct = 0
     do i = 1,s_l2g
        if (metis_out(i) /= lmpi_nproc) then
           ct = ct + 1
           !if (debug) then
           !   write(60000+lmpi_id,*)"Mig i,t_l2g,ipe ",i,t_l2g(i),metis_out(i)
           !   write(70000+lmpi_id,*) t_l2g(i)
           !end if
        end if
     end do
     if (ct > 0) write(*,*)"Total to migrate ",lmpi_id,ct
     call lmpi_reduce(ct,j)
     call lmpi_bcast(j)
     nmigrate = j
     !if (lmpi_master) write(*,*)"nmigrate ",lmpi_id,nmigrate
     if (nmigrate > 0) then
        allocate(counts(lmpi_nproc)); counts = 0
        call lmpi_gather(ct,counts)
        call lmpi_bcast(counts)
        allocate(m_l2g(nmigrate));  m_l2g = lmpi_nproc
        allocate(m_ipe(nmigrate));  m_ipe = lmpi_nproc
        allocate(itemp1(max(1,ct))); itemp1 = 0
        allocate(itemp2(max(1,ct))); itemp2 = 0
        if (ct > 0) then
           ct = 0
           do i = 1,s_l2g
              if (metis_out(i) /= lmpi_nproc) then
                 ct = ct + 1
                 itemp1(ct) = t_l2g(i)     ! global node to migrate
                 itemp2(ct) = metis_out(i) ! ipe to migrate to
              end if
           end do
        end if
        call lmpi_gatherv(itemp1,counts(lmpi_id+1),m_l2g,counts)
        deallocate(itemp1)
        call lmpi_bcast(m_l2g)
        call lmpi_gatherv(itemp2,counts(lmpi_id+1),m_ipe,counts)
        deallocate(itemp2)
        call lmpi_bcast(m_ipe)
        !if (debug.and.lmpi_master) then
        !   do i = 1,nmigrate
        !      write(70000+lmpi_id,*) i,m_l2g(i),m_ipe(i)
        !   end do
        !end if
        do i = 1,nmigrate
           inode = m_l2g(i)
           if ((inode>=pp_nhead(lmpi_id)).and.(inode<=pp_ntail(lmpi_id))) then
              j = (m_l2g(i)-pp_nhead(lmpi_id))+1
              ! k = metis_data(j)
              metis_data(j) = m_ipe(i)
              !write(80000+lmpi_id,*) "Set ", &
              !  j,(pp_nhead(lmpi_id)+j)-1,m_l2g(i),m_ipe(i),k
              !if (m_l2g(i) == k) write(80000+lmpi_id,*)"ERROR4a ",m_l2g(i),k
           end if
        end do
        deallocate(m_ipe,m_l2g,counts)
     end if

     deallocate(metis_out,t_l2g,t_ja,t_ia)

     call lmpi_conditional_stop(0)
     if (nmigrate == 0) exit migration_loop_name

  end do migration_loop_name ! iloop

  end subroutine lines_check_islands

!============================== MY_METIS_OUT =================================80
!
! This routine writes out the current partition vector.
!
!=============================================================================80

  subroutine my_metis_out(nnodesg,project)

    use system_extensions, only : se_open
    use info_depr,         only : pp_cmd_nnparts

    integer,          intent(in) :: nnodesg
    character(len=*), intent(in) :: project

    integer :: status,iostat,nnparts,i0_size,my_size,isize,ipe, ierr

    integer, dimension(:), allocatable :: temp

    character(len=80)  :: filename
    integer, parameter :: iounit = 2300

    continue

    status  = 0
    nnparts = lmpi_nproc
    if (pp_cmd_nnparts /= 0) nnparts = pp_cmd_nnparts

    if (lmpi_id == 0) then
       filename = trim(project) // '.metisout'
       call se_open (iounit,file=filename,status='unknown',access='stream',    &
                     form='unformatted',iostat=iostat)
       if ( iostat == 0 ) then
          rewind(unit=iounit)
       else
          write(*,*)'error opening ',trim(filename),' stopping...'
          status = 1
       endif
    end if
    call lmpi_conditional_stop(status)

    my_size = pp_nsize(lmpi_id)
    i0_size = pp_nsize(0)
    allocate(temp(i0_size)); temp = 0

    if (lmpi_id == 0) then
       write(iounit) nnparts,nnodesg
       write(iounit) metis_data(1:my_size)
       do ipe = 1,lmpi_nproc-1
          isize = pp_nsize(ipe)
         !write(*,*)'ipe ',lmpi_id,isize,size(metis_data)
          call lmpi_recv(temp,i0_size,ipe,ipe*100,ierr)
          write(iounit) temp(1:isize)
       end do
       close(iounit)
    else
       temp(1:my_size) = metis_data(1:my_size)
       call lmpi_send(temp,i0_size,0,lmpi_id*100,ierr)
    end if
    deallocate(temp)

    if (lmpi_id == 0) write(*,*)'    ... metis data written successfully.'
    if (lmpi_id == 0) write(*,"(1x,2a)") &
    'PAR: Wrote partition vector file=',trim(filename)

  end subroutine my_metis_out

!============================== PARTITION_OUT_GLOBAL =========================80
!
! Write global number associated with the current partition vector.
!
!=============================================================================80

  subroutine partition_out_global( dofg, project, l2g, nproc, pp_nsize )

    use system_extensions, only : se_open

    integer,                       intent(in) :: dofg, nproc
    integer, dimension(0:nproc-1), intent(in) :: pp_nsize
    integer, dimension(:),         intent(in) :: l2g
    character(len=*),              intent(in) :: project

    integer :: iostat, i0_size, my_size, isize, ipe, ierr

    integer, dimension(:), allocatable :: temp

    character(len=80)  :: filename
    integer, parameter :: iounit = 2300

    continue

    ierr  = nproc - lmpi_nproc
    call lmpi_conditional_stop(ierr,"nproc error:partition_out_global")

    iostat = 0
    if (lmpi_id == 0) then
       filename = trim(project) // '.metisout_global'
       call se_open (iounit,file=filename,status='unknown',access='stream',    &
                     form='unformatted',iostat=iostat)
    end if
    call lmpi_conditional_stop(iostat,"open error:partition_out_global")

    my_size = pp_nsize(lmpi_id)
    i0_size = pp_nsize(0)
    allocate(temp(i0_size)); temp = 0

    if (lmpi_id == 0) then
       rewind(unit=iounit)
       write(iounit) nproc,dofg
       write(iounit) pp_nsize(0:nproc-1)
       write(iounit) l2g(1:my_size)
       do ipe = 1,nproc-1
          isize = pp_nsize(ipe)
          call lmpi_recv(temp,i0_size,ipe,ipe*100,ierr)
          write(iounit) temp(1:isize)
       end do
       close(iounit)
    else
       temp(1:my_size) = l2g(1:my_size)
       call lmpi_send(temp,i0_size,0,lmpi_id*100,ierr)
    end if
    deallocate(temp)

    if (lmpi_id == 0) write(*,"(1x,2a)") &
    'PAR: Wrote partition global file=',trim(filename)

  end subroutine partition_out_global

!============================== MY_METIS_IN ==================================80
!
! This routine bypassed mymetis and reads the partition vector.
!
!=============================================================================80

  subroutine my_metis_in(project,dofg,arr_size)

    use system_extensions, only : se_open

    character(len=*),                   intent(in) :: project
    integer,                            intent(in) :: dofg
    integer, dimension(0:lmpi_nproc-1), intent(in) :: arr_size

    integer :: nproc,status,ipe,ierr,iostat
    integer :: tdofg, i0_size, my_size, isize

    integer, dimension(:), allocatable :: temp

    character(len=80)  :: filename
    integer, parameter :: iounit = 2300

    continue

    status = 0

     if (lmpi_id == 0) then
        write(*,*) '    ... reading partition vector (skipping ParMetis).'
        filename = trim(project) // '.metisin'
        call se_open (iounit,file=filename,                                    &
             access='stream',form='unformatted',status='old',iostat=iostat)
        if ( iostat == 0 ) then
           rewind (unit=iounit)
           read(iounit) nproc,tdofg
           if ((nproc /= lmpi_nproc).or.(tdofg /= dofg)) then
              status = 1
              write(*,*)'Error: nproc in Metis data (proc) ',nproc,lmpi_nproc
              write(*,*)'Error: ndof  in Metis data (dotf) ',tdofg,dofg
           end if
        else
           write(*,*)'error opening ',trim(filename),' stopping...'
           status = 1
        endif
    end if
    call lmpi_conditional_stop(status)

    my_size = arr_size(lmpi_id)
    i0_size = arr_size(0)
    if (allocated(metis_data)) deallocate(metis_data)
    allocate(metis_data(my_size)); metis_data = 0
    allocate(temp(i0_size)); temp = 0
    if (lmpi_id == 0 ) then

       read(iounit,end=300,err=400) metis_data(1:my_size)

       do ipe = 1,lmpi_nproc-1
          isize = arr_size(ipe)
          !write(*,*)'IPE ',lmpi_id,isize,size(metis_data)
          temp = 0
          read(iounit,end=300,err=400) temp(1:isize)
          call lmpi_send(temp,i0_size,ipe,ipe*100,ierr)
      end do
      close(iounit)
    else
      !write(*,*)'ipe ',lmpi_id,my_s,my_e,isize,size(metis_data)
      call lmpi_recv(temp,i0_size,0,lmpi_id*100,ierr)
      metis_data(1:my_size) = temp(1:my_size)
    end if
    deallocate(temp)

   if (lmpi_id == 0) write(*,*)'    ... metis data read successfully.'
   call lmpi_conditional_stop(0)

   return

300  write(*,*)'300: EOF reached'
     call lmpi_conditional_stop(1)
400  write(*,*)'400: ERR reached'
     call lmpi_conditional_stop(1)

  end subroutine my_metis_in

!============================== PARTITION_IN_SEQUENCE ========================80
!
! Reads the partition vector and prolongation for a parent grid
! and sets the partition vector for a child grid.
!
!=============================================================================80

  subroutine partition_in_sequence( projectp, dofgp, project,                  &
                                    l2g, nproc, pp_nsize )

    use system_extensions, only : se_open
    use multigrid_defs,    only : prolong_format

    character(len=*),              intent(in) :: projectp, project
    integer,                       intent(in) :: dofgp, nproc
    integer, dimension(0:nproc-1), intent(in) :: pp_nsize
    integer, dimension(:),         intent(in) :: l2g

    integer :: status, ipe, ierr, iostat, i, fine, coarse, global
    integer :: tnproc, tdofg, tdofgp, tdofgc, my_size, isize
    integer :: start_indexp, indexc, indexp, fine_last, coarse_last
    integer :: coarse_max

    character(len=80)  :: filename
    integer, parameter :: iounit1 = 2300, iounit2 = 2400, u = 2500

    integer, dimension(:), allocatable :: metis_datap, l2gp
    integer, dimension(:), allocatable :: parent, ifound

    integer, dimension(0:lmpi_nproc-1) :: arr_size

    logical :: exhaustive, all_ordered

    integer :: iosum

  continue

    coarse_max = 0

    if ( lmpi_id == 0 ) write(*,"(1x,4a)")                       &
    'PAR: Assembling partition vector from parent grid%project=',&
    trim(projectp),' for grid%project=',trim(project)

    status  = nproc - lmpi_nproc
    call lmpi_conditional_stop(status,"nproc error:partition_in_sequence")

    filename = trim(projectp) // ".prolong_nc_seq"
    if ( prolong_format == 0 ) then
      if ( lmpi_master ) write(*,*) ' Opening formatted filename=',&
                         trim(filename)
      call se_open(unit=u, file=filename, form='formatted',&
           status='old',iostat = iostat)
    else
      if ( lmpi_master ) write(*,*) ' Opening unformatted filename=',&
                         trim(filename)
      call se_open(unit=u, file=filename, form='unformatted',&
           status='old',iostat = iostat)
    endif
    call lmpi_conditional_stop(iostat,'opening file:partition_in_sequence')
    rewind(u)
    if ( lmpi_id == 0 ) &
    write(*,"(1x,2a)") 'PAR: Reading file=',trim(filename)

    my_size = pp_nsize(lmpi_id)
    allocate(parent(my_size)) ; parent = 0
    allocate(ifound(my_size)) ; ifound = 0

    if (allocated(metis_data)) deallocate(metis_data)
    allocate(metis_data(my_size)); metis_data(:) = -10

    if ( prolong_format == 0 ) then
      read(u,"(2i10)") tdofgp, tdofgc
    else
      read(u,iostat=iostat) tdofgp, tdofgc
    endif
    call lmpi_reduce(min(1,abs(iostat)),iosum)
    call lmpi_bcast(iosum)
    if ( iostat /= 0 ) then
      write(*,*) ' Error reading 1st line of file=',lmpi_id,iosum
    endif
    call lmpi_conditional_stop(iostat,"read error:partition_in_sequence")

    if ( lmpi_id == 0 ) write(*,*) "  expected entries...dofgp=",dofgp
    if ( lmpi_id == 0 ) write(*,*) "  tdofgp, tdofgc ",tdofgp, tdofgc
    ierr = tdofgp - dofgp
    call lmpi_conditional_stop(ierr,"dofgp error:partition_in_sequence")

    fine_last   = 0
    coarse_last = 0
    indexc      = 0

    all_ordered = .true.
    outer0 : do global = 1, tdofgc

      if ( prolong_format == 0 ) then
        read(u,"(10x,2i10)") fine, coarse
      else
        read(u) fine, coarse
      endif

      coarse_max = max( coarse, coarse_max )

      exhaustive = .false.
      if ( coarse <= coarse_last .or. fine <= fine_last ) then
        exhaustive  = .true.
        all_ordered = .false.
      elseif ( fine <= fine_last ) then
        write(*,"(1x,a,i0,2(a,2i12))") ' Ordering error...global=',global,   &
        ' coarse,coarse_last=',coarse,coarse_last,                           &
        ' fine,fine_last=',fine,fine_last
        ierr = 1 ; exit
      endif

      coarse_last = coarse
      fine_last = fine

      if ( exhaustive ) indexc = 0
      inner0 : do i = indexc+1,my_size
         if ( coarse == l2g(i) ) then
             indexc = i
             parent(indexc) = fine
            !if ( indexc == my_size ) exit outer0
             exit inner0
         end if
      end do inner0

    enddo outer0

    close(u)

    call lmpi_conditional_stop(ierr,"ordering error:partition_in_sequence")
    ierr = my_size - coarse_max
    if ( lmpi_nproc == 1 .and. ierr /= 0 ) then
      write(*,"(1x,a,i0,3(a,i0))") ' dofgp=',dofgp,&
      ' my_size=',my_size,' indexc=',indexc,' lmpi_id=',lmpi_id
    else
      ierr = 0
    endif
    call lmpi_conditional_stop(ierr,"incomplete fill:partition_in_sequence")

    status = 0
    if (lmpi_id == 0) then
      filename = trim(projectp) // '.metisout'
      write(*,"(1x,2a)") 'PAR: Reading file=',trim(filename)
      call se_open (iounit1,file=filename,                                    &
           access='stream',form='unformatted',status='old',iostat=iostat)
      if ( iostat == 0 ) then
        rewind (unit=iounit1)
        read(iounit1) tnproc,tdofg
        if ( (tnproc /= nproc) .or. (tdofg /= dofgp) ) then
          status = 1
          write(*,*)'Error: nproc in Metis data (proc) ',tnproc,nproc
          write(*,*)'Error: dofg  in Metis data (dotf) ',tdofg,dofgp
        end if
      else
        status = 1
      endif
    end if
    call lmpi_conditional_stop(status,"open error1:partition_in_sequence")

    if (lmpi_id == 0) then
      filename = trim(projectp) // '.metisout_global'
      write(*,"(1x,2a)") 'PAR: Reading file=',trim(filename)
      call se_open (iounit2,file=filename,                                    &
           access='stream',form='unformatted',status='old',iostat=iostat)
      if ( iostat == 0 ) then
        rewind (unit=iounit2)
        read(iounit2) tnproc,tdofg
        if ( (tnproc /= nproc).or.(tdofg /= dofgp) ) then
          status = 1
          write(*,*)'Error: nproc in Metis data (proc) ',tnproc,nproc
          write(*,*)'Error: dofg  in Metis data (dotf) ',tdofg,dofgp
        end if
        read(iounit2) arr_size(0:nproc-1)
      else
        status = 1
      endif
    end if
    call lmpi_conditional_stop(status,"open error2:partition_in_sequence")
    call lmpi_bcast(arr_size)

    do ipe = 0,nproc-1

      isize = arr_size(ipe)

      allocate(metis_datap(isize))
      allocate(       l2gp(isize))
      if (lmpi_id == 0 ) then
        read(iounit1,end=300,err=400) metis_datap(1:isize)
        read(iounit2,end=300,err=400) l2gp(1:isize)
      endif

      call lmpi_bcast(metis_datap)
      call lmpi_bcast(l2gp)

      start_indexp = 1
      outer : do indexc=1,my_size

        if ( ifound(indexc) == 1 ) cycle outer

        fine = parent(indexc)

        inner : do indexp=start_indexp,isize

          if ( l2gp(indexp) /= fine ) cycle inner

          if ( all_ordered ) start_indexp = indexp + 1

          metis_data(indexc) = metis_datap(indexp)

          ifound(indexc) = 1

          exit inner

        enddo inner
      enddo outer

      deallocate(metis_datap, l2gp)

    enddo

    call lmpi_conditional_stop(sum(ifound)-my_size,&
    "ifound:partition_in_sequence")

    close(iounit1)
    close(iounit2)

    do indexc=1,my_size
      if ( metis_data(indexc) >=     1 .and. &
           metis_data(indexc) <= nproc ) cycle
      write(*,*) ' lmpi_id=',lmpi_id,' metis_data=',metis_data(indexc)
      ierr = 1 ; exit
    enddo

    call lmpi_conditional_stop(ierr,"metis_data:partition_in_sequence")

    deallocate(parent, ifound)

    return

300 call lmpi_conditional_stop(1,"EOF:partition_in_sequence")
400 call lmpi_conditional_stop(1,"ERR:partition_in_sequence")

  end subroutine partition_in_sequence

!============================== VERIFY_IN_SEQUENCE ===========================80
!
! Verify that parent and child are on the same partition.
!
!=============================================================================80

  subroutine verify_in_sequence( f_project, f_dof0, f_l2g, f_x, f_y, f_z,      &
                                            c_dof0, c_l2g, c_x, c_y, c_z  )

    use system_extensions, only : se_open

    character(len=*),       intent(in) :: f_project
    integer,                intent(in) :: f_dof0, c_dof0
    integer,  dimension(:), intent(in) :: f_l2g, c_l2g
    real(dp), dimension(:), intent(in) :: f_x, f_y, f_z, c_x, c_y, c_z

    integer :: ierr, iostat, dofgp, dofgc, fine, coarse, global, cdof, fdof

    character(len=80)  :: filename
    integer, parameter :: u = 2500

    logical :: found_coarse, found_fine

  continue

    ierr = 0
    filename = trim(f_project) // ".prolong_nc_seq"
    call se_open(unit=u, file=filename, form='unformatted',                    &
         status='old',iostat = iostat)
    call lmpi_conditional_stop(iostat,'opening file:verify_in_sequence')
    rewind(u)

    read(u) dofgp, dofgc

    do global=1, dofgc

       read(u) fine, coarse

       found_coarse = .false.
       do cdof=1,c_dof0
         if ( c_l2g(cdof) /= coarse ) cycle
         found_coarse = .true.
         exit
       enddo

        if ( .not.found_coarse ) cycle

        found_fine = .false.
        do fdof=1,f_dof0
          if ( f_l2g(fdof) /= fine ) cycle
          found_fine = .true.
          exit
        enddo

        if ( found_fine ) cycle

        write(*,*) ' Found coarse but not fine on lmpi_id=',lmpi_id,&
        ' coarse=',coarse,' fine=',fine
        write(*,"(1x,a,3e20.12,a,3e20.12)")              &
        ' x,y,z (fine)=',  f_x(fdof),f_y(fdof),f_z(fdof),&
        ' x,y,z (coarse)=',c_x(cdof),c_y(cdof),c_z(cdof)
        ierr = 1 ; exit

    enddo

    close(u)

    call lmpi_conditional_stop(ierr,"ordering error:verify_in_sequence")
    if (.false.) write(*,*)"dofgp ",dofgp

  end subroutine verify_in_sequence

!============================== MY_METIS_CHECK ===============================80
!
! Consistency check on partition vector
!
!=============================================================================80

  subroutine my_metis_check(my_size,pvector)

    integer,                     intent(in) :: my_size
    integer, dimension(my_size), intent(in) :: pvector

    integer :: i,j
    integer, dimension(:), allocatable :: temp, temp1

    continue

    allocate(temp(lmpi_nproc)); temp = 0
    do i = 1,my_size
       temp(pvector(i)) = 1
    end do
    allocate(temp1(lmpi_nproc)); temp1 = 0
    call lmpi_max(temp,temp1)
    j = 0
    if (lmpi_master) j = sum(temp1)
    deallocate(temp,temp1)
    call lmpi_bcast(j)
    if (j /= lmpi_nproc) then
       if (lmpi_master) then
          write(*,*)
          write(*,*)"Grid not distributed over all processors."
          write(*,*)"Number cores with no grid nodes/cells ",lmpi_nproc-j
          write(*,*)"This could be due to an overly coarse grid with or ",     &
                    "without line partitioning."
          write(*,*)
          write(*,*)"Gracefully terminating."
          write(*,*)
       end if
       call lmpi_conditional_stop(1,                                           &
            'Grid not distributed over all processores.')
    end if

  end subroutine my_metis_check


#ifdef HAVE_PARMETIS
!============================== MY_METIS_OUT_CC ==============================80
!
! This routine writes out the current partition vector.
!
!=============================================================================80

  subroutine my_metis_out_cc(project)

    use system_extensions, only : se_open
    use info_depr,         only : pp_cmd_nnparts

    character(len=*),            intent(in) :: project

    integer :: status,iostat,i0_size,isize,ipe, ierr,ndofg

    integer, dimension(:), allocatable :: temp, all_size

    character(len=80)  :: filename
    integer, parameter :: iounit = 2300

    continue

    status  = 0

    if (lmpi_id == 0) then
       filename = trim(project) // '.metisout'
       call se_open (iounit,file=filename,status='unknown',access='stream',    &
                     form='unformatted',iostat=iostat)
       if ( iostat == 0 ) then
          rewind(unit=iounit)
       else
          write(*,*)'error opening ',trim(filename),' stopping...'
          status = 1
       endif
    end if
    call lmpi_conditional_stop(status)

    allocate(all_size(0:lmpi_nproc-1))
    isize = size(metis_data)
    call lmpi_gather(isize,all_size)
    if (lmpi_master) ndofg = sum(all_size)

    if (lmpi_master) i0_size = maxval(all_size)
    call lmpi_bcast(i0_size)
    allocate(temp(i0_size)); temp = 0

    if (lmpi_id == 0) then
       write(iounit) lmpi_nproc,ndofg
       write(iounit) metis_data(1:isize)
       do ipe = 1,lmpi_nproc-1
          isize = all_size(ipe)
         !write(*,*)'ipe ',lmpi_id,isize,i0_size
          call lmpi_recv(temp,i0_size,ipe,ipe*100,ierr)
          write(iounit) temp(1:isize)
       end do
       close(iounit)
    else
       temp(1:isize) = metis_data(1:isize)
       call lmpi_send(temp,i0_size,0,lmpi_id*100,ierr)
    end if
    deallocate(temp,all_size)

    if (lmpi_id == 0) write(*,*)'    ... metis (cc) data written successfully.'

  end subroutine my_metis_out_cc
#else
  subroutine my_metis_out_cc(project)
    character(len=*),            intent(in) :: project
    continue
    if (.false.) write(*,*)"project ",project
  end subroutine my_metis_out_cc
#endif

!============================== compute_adj_list =============================80
!
! Driver to insert the cell to node (c2n) info into the c2n data structure,
! and exchange them among processors.
!
!=============================================================================80

  subroutine compute_adj_list(grid,flow_dir)

    use nml_overset_data,  only : use_imesh_constraint
    use nml_global,        only : moving_grid
    use twod_util,         only : y_coplanar_tol
    use grid_types,        only : grid_type
    use overset_defs,      only : dci
    use nml_overset_data,  only : overset_flag, dci_dir, dci_io
    use pundit,            only : pundit_flag
    use dirtlib,           only : dci_file
    use sort,              only : binary_search
    use lmpi,              only : lmpi_id, lmpi_reduce, lmpi_bcast, lmpi_die,  &
                                  lmpi_master
    use system_extensions, only : se_flush
    use dcif,              only : load_imesh_from_dcif
#ifdef HAVE_PUNDIT
    use file_utils, only : available_unit
#endif

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(inout) :: grid

    integer :: i,j,k,ihead,itail,ielem,epc,n1,n2,icell,iedge
    integer :: joff,itotal,ifound,gnode,i1,i2,nsize,imesh,nnodes0,nnodes1
    integer :: n1_local, n2_local
    integer :: nodes_with_imesh_ne_0, total_nodes_with_imesh_ne_0

#ifdef HAVE_PUNDIT
    integer :: gn, ios, f, value
#endif

    integer, dimension(:), allocatable :: ia,ioff,temp,ia_weight,temp_l2g_l1

    character(len=80)  :: suffix

#ifdef HAVE_PUNDIT
    character(len=128) :: imesh_filename
#endif

   continue

    ihead = pp_nhead(lmpi_id)
    itail = pp_ntail(lmpi_id)

    if ( (overset_flag.and.use_imesh_constraint) .or. pundit_flag ) then
      allocate(imesh_global(ihead:itail))
      imesh_global(ihead:itail) = -1
    endif

! If overset, will need to interface with dirtlib

    if ( overset_flag.and.use_imesh_constraint ) then

      write(suffix,*) dci
      if (dci == 0) then
        dci_file = trim(flow_dir) // trim(dci_dir) // trim(grid%project) //    &
                   ".dci"
      else
        dci_file = trim(flow_dir) // trim(dci_dir) // trim(grid%project) //    &
                   trim(adjustl(suffix)) // ".dci"
      end if

      if ( dci_io ) then
        call load_imesh_from_dcif(grid,ihead,itail,imesh_global)
      else
        if ( lmpi_master ) then
          write(*,'(2a)') ' pparty_metis:compute_adj_list Reading DCI data: ', &
                            trim(dci_file)
        endif
#ifdef HAVE_DIRT
        call drtf_load_dci_file_header(trim(dci_file) // char(0))
        do i = ihead, itail
          call drtf_get_dy_gr_for_glbl_node(i,imesh_global(i))
        end do
        call drtf_lib_reset()
#else
        write(*,*) 'Not compiled against DiRTLib...stopping.'
        call lmpi_finalize
        stop
#endif
      endif

    endif

! If running with PUNDIT, need to load in imesh data from disk

    with_pundit : if ( pundit_flag ) then
#ifdef HAVE_PUNDIT
      imesh_filename = trim(grid%project) // '.imesh'
      f = available_unit()
      open(unit=f,file=trim(imesh_filename),form='unformatted',                &
           status='unknown',iostat=ios)
      if (ios == 0) then
        reader : do i = 1, grid%nnodesg
          read(f) gn, value
          if ( gn >= ihead .and. gn <= itail ) imesh_global(gn) = value
        end do reader
        close(f)
      endif
#else
      write(*,*) 'Not compiled against PUNDIT...stopping.'
      call lmpi_finalize
      stop
#endif
    endif with_pundit

    nsize = pp_nsize(lmpi_id)

! Allocate array for node degree

    if ( allocated(        node_weight)) deallocate(        node_weight)
    if ( allocated(overset_node_weight)) deallocate(overset_node_weight)

    allocate(node_weight(nsize)); node_weight = 0
    if (((overset_flag.and.use_imesh_constraint) .or. pundit_flag)             &
          .and. moving_grid)then
      allocate(overset_node_weight(nsize)); overset_node_weight = 0
    endif

    if ( allocated(adj_count) ) deallocate(adj_count)
    allocate(adj_count(ihead:itail)); adj_count = 0

    nodes_with_imesh_ne_0 = 0

    j = pp_nhead(lmpi_id)-1
    do ielem = 1,grid%nelem
       epc = grid%elem(ielem)%edge_per_cell
       do icell = 1,grid%elem(ielem)%ncell

          do i = 1,grid%elem(ielem)%node_per_cell
             n1 = grid%elem(ielem)%c2n(i,icell)
             if ((n1>=ihead).and.(n1<=itail)) then
                node_weight(n1-j) = node_weight(n1-j)+1
                if ( ((overset_flag.and.use_imesh_constraint) .or. pundit_flag)&
                      .and. moving_grid ) then
                  imesh = imesh_global(n1)
                  if ( imesh == -1 ) then
                    write(*,*) 'Error: imesh data unavailable: ', lmpi_id, n1
                    call lmpi_die
                    stop
                  endif
                  if (imesh /= 0) then
                    nodes_with_imesh_ne_0 = nodes_with_imesh_ne_0 + 1
                  end if
                  overset_node_weight(n1-j) = max( min(imesh,1), 0 )
                end if
             end if
          end do

          do iedge = 1,epc
             i1 = grid%elem(ielem)%local_e2n(iedge,1)
             i2 = grid%elem(ielem)%local_e2n(iedge,2)
             n1 = grid%elem(ielem)%c2n(i1,icell)
             n2 = grid%elem(ielem)%c2n(i2,icell)
             if ((n1>=ihead).and.(n1<=itail)) adj_count(n1) = adj_count(n1)+1
             if ((n2>=ihead).and.(n2<=itail)) adj_count(n2) = adj_count(n2)+1
          end do

       end do
    end do

    if (((overset_flag.and.use_imesh_constraint) .or. pundit_flag)             &
          .and. moving_grid)then

      call lmpi_reduce(nodes_with_imesh_ne_0, total_nodes_with_imesh_ne_0)
      call lmpi_bcast(total_nodes_with_imesh_ne_0)

      if (total_nodes_with_imesh_ne_0 == 0) then
        if (lmpi_master) then
          write(*,'(a)') ' In subroutine compute_adj_list:'
          write(*,'(a)') '   Error in dci imesh data: all imesh values are zero'
          write(*,'(a)') '   this likely means when you ran SUGGAR to set up'
          write(*,'(a)') '   the composite mesh, you did not include <dynamic/>'
          write(*,'(a)') '   in the <body> element for moving bodies'
          call se_flush()
        end if
        call lmpi_die
      end if

    end if

    itotal = sum(adj_count)
    allocate(ia(itotal))

    if ( twod ) then
      allocate(ia_weight(itotal)); ia_weight = 1
    else
      allocate(ia_weight(1)); ia_weight = 0
    endif

    allocate(ioff(ihead:itail)); ioff = 0
    do i = ihead+1,itail
       ioff(i) = ioff(i-1)+adj_count(i-1)
    end do

! define a couple of local scalars to make our life easier

    nnodes0 = grid%nnodes0
    nnodes1 = size(grid%l2g,1) - nnodes0

! make an array copy once to avoid the compiler doing it many times later on

    if ( twod ) then
      allocate(temp_l2g_l1(nnodes1))
      temp_l2g_l1(:) = grid%l2g(nnodes0+1:)
    endif

! go back now and store all of the adjacencies
! if accessing xyz arrays, recall they got reallocated and therefore shifted
! to start back at 1 when we expanded the grid data to include level-1!

    adj_count = 0
    do ielem = 1,grid%nelem
       epc = grid%elem(ielem)%edge_per_cell
       do icell = 1,grid%elem(ielem)%ncell
          edge_loop: do iedge = 1,epc
             i1 = grid%elem(ielem)%local_e2n(iedge,1)
             i2 = grid%elem(ielem)%local_e2n(iedge,2)
             n1 = grid%elem(ielem)%c2n(i1,icell)
             n2 = grid%elem(ielem)%c2n(i2,icell)
             n1_local = (n1 - ihead)+1
             n2_local = (n2 - ihead)+1

             if ((n1>=ihead).and.(n1<=itail)) then
               ifound = 0
               joff = ioff(n1)
               if (adj_count(n1) > 0) then
                 do j = 1,adj_count(n1)
                   if (ia(joff+j) == n2) then
                      ifound = 1
                      exit
                   end if
                 end do
               end if
               if (ifound == 0) then
                 adj_count(n1) = adj_count(n1) + 1
                 ia(joff+adj_count(n1)) = n2
                 if ( twod ) then
                   if ( (n2<ihead).or.(n2>itail) ) then
                     n2_local = nnodes0 + binary_search(nnodes1,temp_l2g_l1,n2)
                   endif
                   if(abs(grid%y(n1_local)-grid%y(n2_local))>y_coplanar_tol)then
                     ia_weight(joff+adj_count(n1)) = 1000
                   endif
                 endif
               end if
             end if

             if ((n2>=ihead).and.(n2<=itail)) then
               joff = ioff(n2)
               if (adj_count(n2) > 0) then
                 do j = 1,adj_count(n2)
                    if (ia(joff+j) == n1) cycle edge_loop
                 end do
               end if
               adj_count(n2) = adj_count(n2) + 1
               ia(joff+adj_count(n2)) = n1
               if ( twod ) then
                 if ( (n1<ihead).or.(n1>itail) ) then
                   n1_local = nnodes0 + binary_search(nnodes1,temp_l2g_l1,n1)
                 endif
                 if(abs(grid%y(n1_local)-grid%y(n2_local)) > y_coplanar_tol)then
                   ia_weight(joff+adj_count(n2)) = 1000
                 endif
               endif
             end if

          end do edge_loop
       end do
    end do

    if ( twod ) deallocate(temp_l2g_l1)

    allocate(vtxdist(lmpi_nproc+1))
    vtxdist(1:lmpi_nproc) = pp_nhead(0:lmpi_nproc-1)
    vtxdist(lmpi_nproc+1) = pp_ntail(  lmpi_nproc-1)+1

    allocate(adj(nsize+1)); adj = 0

    ! First element
    adj(1) = 1
    gnode = pp_nhead(lmpi_id)
    do i = 2,nsize+1
       adj(i) = adj(i-1)+adj_count(gnode)
       gnode = gnode + 1
    end do

    itotal = sum(adj_count)
    allocate(adjncy(itotal))

    if ( allocated(edge_weight) ) deallocate(edge_weight)

    if ( twod ) then
      allocate(edge_weight(itotal)); edge_weight = 1
    else
      allocate(edge_weight(1)); edge_weight = 0
    endif

    j = 1
    do i = ihead,itail
       do k = 1,adj_count(i)
          adjncy(j) = ia(ioff(i)+k)
          if ( twod ) edge_weight(j) = ia_weight(ioff(i)+k)
          j = j + 1
       end do
    end do

    deallocate(ia,ia_weight)

    allocate(temp(nsize))
    temp = adj_count
    deallocate(adj_count)
    allocate(adj_count(nsize))
    adj_count = temp
    deallocate(temp)

! ASSERT -- provides baseline adj_count and adjncy for validation.
!   is = 1
!   k  = 0
!   do i = pp_nhead(lmpi_id),pp_ntail(lmpi_id)
!      k = k + 1
!      ie = is + (adj_count(k)-1)
!      allocate(temp(is:ie)); temp = adjncy(is:ie)
!      call small_sort(adj_count(k),temp)
!      write(920+lmpi_id,'(1x,i0," : ",i0,1x," :: ",100(i0,1x))')  &
!        i,adj_count(k),temp(is:ie)
!      deallocate(temp)
!      is = ie + 1
!   end do

    deallocate(ioff)

   end subroutine compute_adj_list ! ioff

!============================== compute_adj_list2 ============================80
!
! Driver to insert the cell to node (c2n) info into the c2n data structure,
! and exchange them amoung processors.
!
!=============================================================================80

  subroutine compute_adj_list2(grid)

   !use twod_util,  only : y_coplanar_tol
    use grid_types, only : grid_type
    use sort,       only : binary_search

    type(grid_type), intent(inout) :: grid

    integer :: i,j,k,ihead,itail,ielem,epc,n1,n2,icell,iedge
    integer :: joff,itotal,ifound,gnode,i1,i2,nsize,nnodes0,nnodes1
    integer :: n1_local, n2_local

    integer, dimension(:), allocatable :: ia,ioff,temp_l2g_l1

   continue

    nsize = pp_nsize(lmpi_id)
    ihead = pp_nhead(lmpi_id)
    itail = pp_ntail(lmpi_id)

    if ( allocated(adj_count2) ) deallocate(adj_count2)
    allocate(adj_count2(ihead:itail)); adj_count2 = 0

    j = pp_nhead(lmpi_id)-1
    do ielem = 1,grid%nelem
       epc = grid%elem(ielem)%edge_per_cell
       do icell = 1,grid%elem(ielem)%ncell
          do iedge = 1,epc
             i1 = grid%elem(ielem)%local_e2n(iedge,1)
             i2 = grid%elem(ielem)%local_e2n(iedge,2)
             n1 = grid%elem(ielem)%c2n(i1,icell)
             n2 = grid%elem(ielem)%c2n(i2,icell)
             if ((n1>=ihead).and.(n1<=itail)) adj_count2(n1) = adj_count2(n1)+1
             if ((n2>=ihead).and.(n2<=itail)) adj_count2(n2) = adj_count2(n2)+1
          end do
       end do
    end do

    itotal = sum(adj_count2)
    allocate(ia(itotal))

    allocate(ioff(ihead:itail)); ioff = 0
    do i = ihead+1,itail
       ioff(i) = ioff(i-1)+adj_count2(i-1)
    end do

! define a couple of local scalars to make our life easier

    nnodes0 = grid%nnodes0
    nnodes1 = size(grid%l2g,1) - nnodes0

! make an array copy once to avoid the compiler doing it many times later on

    if ( twod ) then
      allocate(temp_l2g_l1(nnodes1))
      temp_l2g_l1(:) = grid%l2g(nnodes0+1:)
    endif

! go back now and store all of the adjacencies
! if accessing xyz arrays, recall they got reallocated and therefore shifted
! to start back at 1 when we expanded the grid data to include level-1!

    adj_count2 = 0
    do ielem = 1,grid%nelem
       epc = grid%elem(ielem)%edge_per_cell
       do icell = 1,grid%elem(ielem)%ncell
          edge_loop: do iedge = 1,epc
             i1 = grid%elem(ielem)%local_e2n(iedge,1)
             i2 = grid%elem(ielem)%local_e2n(iedge,2)
             n1 = grid%elem(ielem)%c2n(i1,icell)
             n2 = grid%elem(ielem)%c2n(i2,icell)
             n1_local = (n1 - ihead)+1
             n2_local = (n2 - ihead)+1

             if ((n1>=ihead).and.(n1<=itail)) then
               ifound = 0
               joff = ioff(n1)
               if (adj_count2(n1) > 0) then
                 do j = 1,adj_count2(n1)
                   if (ia(joff+j) == n2) then
                      ifound = 1
                      exit
                   end if
                 end do
               end if
               if (ifound == 0) then
                 adj_count2(n1) = adj_count2(n1) + 1
                 ia(joff+adj_count2(n1)) = n2
                 if ( twod ) then
                   if ( (n2<ihead).or.(n2>itail) ) then
                     n2_local = nnodes0 + binary_search(nnodes1,temp_l2g_l1,n2)
                     write(*,*)'n2_local ',n2_local ! not used TBD
                   endif
                 endif
               end if
             end if

             if ((n2>=ihead).and.(n2<=itail)) then
               joff = ioff(n2)
               if (adj_count2(n2) > 0) then
                 do j = 1,adj_count2(n2)
                    if (ia(joff+j) == n1) cycle edge_loop
                 end do
               end if
               adj_count2(n2) = adj_count2(n2) + 1
               ia(joff+adj_count2(n2)) = n1
               if ( twod ) then
                 if ( (n1<ihead).or.(n1>itail) ) then
                   n1_local = nnodes0 + binary_search(nnodes1,temp_l2g_l1,n1)
                   write(*,*)'n1_local ',n1_local ! not used TBD
                 endif
               endif
             end if
          end do edge_loop
       end do
    end do

    if ( twod ) deallocate(temp_l2g_l1)

    allocate(vtxdist(lmpi_nproc+1))
    vtxdist(1:lmpi_nproc) = pp_nhead(0:lmpi_nproc-1)
    vtxdist(lmpi_nproc+1) = pp_ntail(  lmpi_nproc-1)+1

    allocate(adj(nsize+1)); adj = 0

    ! First element
    adj(1) = 1
    gnode = pp_nhead(lmpi_id)
    do i = 2,nsize+1
       adj(i) = adj(i-1)+adj_count2(gnode)
       gnode = gnode + 1
    end do

    itotal = sum(adj_count2)
    allocate(adjncy2(itotal))

    j = 1
    do i = ihead,itail
       do k = 1,adj_count2(i)
          adjncy2(j) = ia(ioff(i)+k)
          j = j + 1
       end do
    end do

    deallocate(ia)

if ( allocated(adj_count) ) then
write(*,*) ' FIXME - runtime deallocate adj_count'
deallocate(adj_count)
endif

    allocate(adj_count(nsize)); adj_count = 0
    adj_count = adj_count2
    deallocate(adj_count2)

   !write(*,*)"EXIT compute_adj_list2 ",lmpi_id,size(adj_count2),size(adjncy2)
   !do i = 1,size(adj_count)
   !   write(90000+lmpi_id,*) adj_count(i)
   !end do

   end subroutine compute_adj_list2 ! adj_count, adjncy2

!============================== compute_adj_list_unique ======================80
!
! For cc, create : nhead_unique, ntail_unique, adj_ct_unique, adjncy_unique
!
! Called by PE having node 1/n and associated cells with these nodes.
! Creates adj, adjncy (ia,ja) such that values in adjncy are larger then the
!   node associated with adj (thereby unique).
!
!=============================================================================80

  subroutine compute_adj_list_unique(grid)

    use grid_types, only : grid_type

    type(grid_type), intent(inout) :: grid

    integer :: i,j,nhead,ntail,epc,ielem,icell,iedge
    integer :: i1,i2,n1,n2,m1,m2,itotal,joff

    integer, dimension(:), allocatable :: ioff, max_ct, temp_adjncy

   continue

    nhead = pp_nhead(lmpi_id)
    ntail = pp_ntail(lmpi_id)

   !write(*,*)"ENTER compute_adj_list_unique ",lmpi_id,nhead,ntail

    allocate(max_ct(nhead:ntail)); max_ct = 0
    do ielem = 1,grid%nelem
       epc = grid%elem(ielem)%edge_per_cell
       do icell = 1,grid%elem(ielem)%ncell
          do iedge = 1,epc
             i1 = grid%elem(ielem)%local_e2n(iedge,1)
             i2 = grid%elem(ielem)%local_e2n(iedge,2)
             n1 = grid%elem(ielem)%c2n(i1,icell)
             n2 = grid%elem(ielem)%c2n(i2,icell)
             m1 = min(n1,n2)
             if ((m1 >= nhead).and.(m1 <= ntail)) max_ct(m1) = max_ct(m1)+1
          end do
       end do
    end do

    itotal = sum(max_ct)
    allocate(temp_adjncy(itotal)); temp_adjncy = 0

    allocate(ioff(nhead:ntail)); ioff = 0
    do i = nhead+1,ntail
       ioff(i) = ioff(i-1) + max_ct(i-1)
    end do
    deallocate(max_ct)

! Gather the unique (versus maximum) values

    allocate(adj_ct_unique(nhead:ntail)); adj_ct_unique = 0
    do ielem = 1,grid%nelem
       epc = grid%elem(ielem)%edge_per_cell
       do icell = 1,grid%elem(ielem)%ncell
          edge_loop: do iedge = 1,epc
             i1 = grid%elem(ielem)%local_e2n(iedge,1)
             i2 = grid%elem(ielem)%local_e2n(iedge,2)
             n1 = grid%elem(ielem)%c2n(i1,icell)
             n2 = grid%elem(ielem)%c2n(i2,icell)
             m1 = min(n1,n2)
             m2 = max(n1,n2)
             if ((m1 >= nhead).and.(m1 <= ntail)) then
                joff = ioff(m1)
                if (adj_ct_unique(m1) > 0) then
                   do j = 1,adj_ct_unique(m1)
                      if (temp_adjncy(joff+j) == m2) cycle edge_loop
                   end do
                end if
                adj_ct_unique(m1) = adj_ct_unique(m1) + 1
                temp_adjncy(joff+adj_ct_unique(m1)) = m2
             end if
          end do edge_loop
       end do
    end do
        !do i = nhead,ntail
        !   if (adj_ct_unique(i) > 0) then
        !      joff = ioff(i)
        !      write(70000+lmpi_id,'(1x,100(i0,1x),1x)')  &
        !        temp_adjncy(joff+1:joff+adj_ct_unique(i))
        !   end if
        !end do

    itotal = sum(adj_ct_unique)
            ! write(*,*)"ADJ_CT_UNIQUE ",lmpi_id,itotal

    allocate(adjncy_unique(itotal)); adjncy_unique = 0

    joff = 0
    do i = nhead,ntail
       if (adj_ct_unique(i) > 0) then
          adjncy_unique(joff+1   :joff   +adj_ct_unique(i)) =                  &
            temp_adjncy(ioff(i)+1:ioff(i)+adj_ct_unique(i))
          joff = joff + adj_ct_unique(i)
       end if
    end do
    deallocate(ioff)

! Verify
!
!   joff = 0
!   do i = nhead,ntail
!      j = adj_ct_unique(i)
!      !write(90000+lmpi_id,*) j
!      if (j > 0) then
!         allocate(temp(j)); temp = 0
!         temp(1:j) = adjncy_unique(joff+1:joff+j)
!         call small_sort(j,temp)
!         !   do k = 1,j
!         !      write(82000+lmpi_id,'(1x,i0,1x,i0,1x)') i,temp(k)
!         !   end do
!         deallocate(temp)
!         joff = joff + adj_ct_unique(i)
!      end if
!      !j = joff + 1
!      !k = j + adj_ct_unique(i) - 1
!      !write(80000+lmpi_id,'(1x,i0,1x,i0,1x,i0,1x,i0,1x,100(i0,1x))')         &
!      !  i,adj_ct_unique(i),j,k,adjncy_unique(j:k)
!   end do

! Deallocate

    deallocate(temp_adjncy)

   end subroutine compute_adj_list_unique

#ifdef HAVE_PARMETIS
!============================== MY_METIS_CC ==================================80
!
! Partitions mesh in parallel (distributed memory) for cc.
!
!=============================================================================80
  subroutine my_metis_cc(grid)

    use grid_types,        only : grid_type
    use metis_defs,        only : partitioning_only, partitioning_in
    use info_depr,         only : pp_cmd_wgtflag, pp_cmd_ubvec,                &
                                  pp_cmd_nnparts
    use kinddefs,          only : dp, r4
    use suggar_info,       only : fun3d_comm
    use nml_overset_data,  only : dci_on_the_fly
    use info_depr,         only : pp_cmd_PM_use_proc, pp_cmd_stats,            &
                                  serialize_partitioner
    use system_extensions, only : se_open

    type(grid_type), intent(in) :: grid

    integer :: edgeCut,ncon
    integer :: isize,ivsize,nnparts
    integer :: numbering,weightflag,generic_comm

    integer, dimension(5)              :: options
    integer, dimension(:), allocatable :: weight, edge_weight

!beginNeverComplex
    real(r4), dimension(:), allocatable :: tpwgts,ubvec
!endNeverComplex

  continue

    call lmpi_conditional_stop(0,'ENTER my_metis_cc')

! if the solver was run on a single processor, set the part vector to
! identity and simply return; no need to call ParMetis

    if (lmpi_nproc == 1) then

      if ( allocated(cc_chead) ) then
        write(*,*) ' FIXME - runtime deallocate cc_chead'
        deallocate(cc_chead)
      endif

      if ( allocated(cc_ctail) ) then
        write(*,*) ' FIXME - runtime deallocate cc_ctail'
        deallocate(cc_ctail)
      endif

      if ( allocated(cc_csize) ) then
        write(*,*) ' FIXME - runtime deallocate cc_csize'
        deallocate(cc_csize)
      endif

      allocate(cc_chead(0:0)); cc_chead = 1
      allocate(cc_ctail(0:0)); cc_ctail = grid%ncellg
      allocate(cc_csize(0:0)); cc_csize = grid%ncellg
      write(*,*)"cc_csize ",cc_csize
      allocate(metis_data(grid%ncellg))
      metis_data = 1

      return

    endif

    call exchange_c2c_cc(grid) ! uses module variables (elem1_c2c,elem1_tag)
                               ! from compute_c2c_cc_driver

! Setup ParMetis

    if (.not.partitioning_in) then

      isize = vtxdist(lmpi_id+2)-vtxdist(lmpi_id+1)

      allocate(metis_data(isize)); metis_data = 0

! Optionally use serial Metis if requested

      if ( serialize_partitioner ) then
        call serial_metis(grid%ncellg,2)
        return
      endif

      numbering  = 1
      options    = 0
      options(1) = 0
      options(2) = 3
      options(3) = 1

      nnparts = lmpi_nproc

      if (pp_cmd_nnparts /= 0) then
         nnparts = pp_cmd_nnparts
         partitioning_only = .true.
      end if

! Match party (partition_for_threed_cc), not face nor cell weight

      ncon       = 1
      weightflag = 0
      ivsize     = 1

! Allocate temporary arrays used for parallel processing

      allocate(tpwgts(nnparts*ncon)); tpwgts = 1.0_r4/real(nnparts,r4)
      allocate(ubvec(ncon));          ubvec  = real(pp_cmd_ubvec,r4)

      allocate(weight(ivsize));      weight = 0
      allocate(edge_weight(ivsize)); edge_weight = 0

      edgeCut = 0

      if (lmpi_id == 0)                                                        &
                write(*,*)'    ... Calling ParMetis (ParMETIS_V3_PartKway) ....'

      generic_comm = lmpi_comm_world
      if ( dci_on_the_fly ) generic_comm = fun3d_comm
      call ParMETIS_V3_PartKway(vtxdist, adj, adjncy, weight, edge_weight,     &
                                weightflag, numbering, ncon, nnparts, tpwgts,  &
                                ubvec, options, edgeCut, metis_data,           &
                                generic_comm)
      if (lmpi_master) write(*,*)'    ... edgeCut ',edgeCut
      deallocate(edge_weight,weight,ubvec,tpwgts)

    else

      allocate(weight(lmpi_nproc)); weight = 0
      call lmpi_gather(cc_csize(lmpi_id),weight)
      call lmpi_bcast(weight)
      call my_metis_in(grid%project,grid%ncellg,weight)
      deallocate(weight)

    end if

    if (partitioning_only) then
      call my_metis_out_cc(grid%project)
      call lmpi_conditional_stop(1,                                            &
      'Stopping after writing project.metisout:my_metis_cc')
    end if

  end subroutine my_metis_cc

#else

  subroutine my_metis_cc(grid)

    use grid_types, only : grid_type

    type(grid_type), intent(in) :: grid

  continue

    if (.false.) write(*,*)"cc_csize ",cc_csize,pp_ctail, pp_csize, pp_chead
    allocate(metis_data(grid%ncellg)); metis_data = 1

    if ( allocated(cc_chead) ) then
      write(*,*) ' FIXME - runtime deallocate cc_chead'
      deallocate(cc_chead)
    endif

    if ( allocated(cc_ctail) ) then
      write(*,*) ' FIXME - runtime deallocate cc_ctail'
      deallocate(cc_ctail)
    endif

    if ( allocated(cc_csize) ) then
      write(*,*) ' FIXME - runtime deallocate cc_csize'
      deallocate(cc_csize)
    endif

    allocate(cc_chead(0:0)); cc_chead = 1
    allocate(cc_ctail(0:0)); cc_ctail = grid%ncellg
    allocate(cc_csize(0:0)); cc_csize = grid%ncellg

    write(*,*)"cc_csize ",cc_csize

  end subroutine my_metis_cc

#endif

#ifdef HAVE_PARMETIS
!============================== exchange_c2c_cc ==============================80
!
! Use the c2c to exchange adj info and set up PM
!
! Exchange the cell c2c info into ParMetis format
! The current cell c2c are based on elem1_tag and grid%elem()%cl2g.
! Rather and map into local values, this approach just exchanges.
! (The call to PM with reduced core, could be implemented here.)
!
! module variables set:  vtxdist, adj, adjncy
! module variables used: elem1_c2c, elem1_tag
!
!=============================================================================80

  subroutine exchange_c2c_cc(grid)

    use grid_types, only : grid_type
    use local_grid, only : cc_chead, cc_ctail, cc_csize

    type(grid_type), intent(in) :: grid

! local

  integer :: i, j, icell, irem, istart, ipe, ielem, gcell, ioff
  integer :: my_cnt, my_sum, max_cnt, max_sum, ksum, fpc
  integer :: is, ie, js, je, my_clo, my_chi, ct, max1

  integer, dimension(:), allocatable :: temp1, temp1a, temp1b

  continue

! Compute distribution

    allocate(cc_chead(0:lmpi_nproc-1)); cc_chead = 0
    allocate(cc_ctail(0:lmpi_nproc-1)); cc_ctail = 0
    allocate(cc_csize(0:lmpi_nproc-1)); cc_csize = 0

    irem = grid%ncellg - ((grid%ncellg/lmpi_nproc) * lmpi_nproc)
    istart = 1
    do i = 0, lmpi_nproc-1
        cc_chead(i) = istart
        cc_csize(i) = grid%ncellg / lmpi_nproc
        if ((irem /= 0).and.(i < irem)) cc_csize(i) = cc_csize(i) + 1
        cc_ctail(i) = (cc_chead(i) + cc_csize(i)) - 1
        istart = cc_ctail(i) + 1
    end do
    my_clo = cc_chead(lmpi_id)
    my_chi = cc_ctail(lmpi_id)
    !write(*,*)"CChts ",cc_chead(lmpi_id),cc_ctail(lmpi_id),cc_csize(lmpi_id)

    allocate(vtxdist(lmpi_nproc+1)); vtxdist = 0
    vtxdist(1) = 1
    do i = 2,lmpi_nproc+1
       vtxdist(i) = vtxdist(i-1)+cc_csize(i-2)
    end do
    !if (lmpi_master) write(*,*)"VTX ",vtxdist

    allocate(adj(my_clo:my_chi+1)); adj = 0
    adj(my_clo) = 1

!--------------------
! Gather and compute adj !HERE rid elem1_tag(ie)%ctag
    my_cnt = 0
    my_sum = 0
    do ielem = 1,grid%nelem
       fpc = grid%elem(ielem)%face_per_cell
       out0: do icell = 1,grid%elem(ielem)%ncell
          if (elem1_tag(ielem)%ctag(icell)==0) then
             ksum = 0
             do i = 1,fpc
                if (elem1_c2c(ielem)%c2c(i,icell) > 0) ksum = ksum + 1
             end do
             if (ksum > 0) then
                my_cnt = my_cnt + 1
                my_sum = my_sum + ksum
             end if
           end if
       end do out0
    end do
    !write(*,*)"my_cnt,my_sum ",lmpi_id,my_cnt,my_sum

    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) max1 = my_cnt
       call lmpi_bcast(max1,ipe)
       allocate(temp1(max1));  temp1  = 0
       allocate(temp1a(max1)); temp1a = 0
       if (ipe == lmpi_id) then
          j = 0
          do ielem = 1,grid%nelem
             fpc = grid%elem(ielem)%face_per_cell
             out1: do icell = 1,grid%elem(ielem)%ncell
                   if (elem1_tag(ielem)%ctag(icell) == 0) then
                      ksum = 0
                      do i = 1,grid%elem(ielem)%face_per_cell
                         if (elem1_c2c(ielem)%c2c(i,icell) > 0) ksum = ksum + 1
                      end do
                      if (ksum > 0) then
                         j = j + 1
                         temp1(j)  = grid%elem(ielem)%cl2g(icell)
                         temp1a(j) = ksum
                      end if
                   end if
             end do out1
          end do
          if (j /= max1) write(*,*)"MISMATCH1 ",lmpi_id,j,max1
             !do i = 1,max1
             !   write(70000+lmpi_id,'(i0,":",i0)') temp1(i),temp1a(i)
             !end do
       end if
       call lmpi_bcast(temp1,ipe)
       call lmpi_bcast(temp1a,ipe)
       do ioff = 1,max1
          gcell = temp1(ioff)
          if ((gcell>=my_clo).and.(gcell<=my_chi)) adj(gcell+1)=temp1a(ioff)
             !write(72000+lmpi_id,*) temp1(ioff),temp1a(ioff)
       end do
       deallocate(temp1)
       deallocate(temp1a)
    end do
        !do i = my_clo+1,my_chi+1 ! ASSERT: SUM(71K) == SUM(70K)
        !   write(71000+lmpi_id,'(i0,":",i0)') i-1,adj(i)
        !end do
    adj(my_clo) = 1
    do i = my_clo+1,my_chi+1 ! ASSERT: SUM(71K) == SUM(70K)
       adj(i) = adj(i)+adj(i-1)
    end do
        !do i = my_clo,my_chi
        !   write(72000+lmpi_id,'(i0,":",i0)') i,adj(i)
        !   write(73000+lmpi_id,*) i,adj(i),adj(i+1)-1
        !end do

!--------------------
! Gather and store the adjncy values

    allocate(adjncy(adj(my_chi+1))); adjncy = 0
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) then
          max_cnt  = my_cnt; max_sum = my_sum
       end if
       call lmpi_bcast(max_cnt,ipe)
       call lmpi_bcast(max_sum,ipe)
       allocate(temp1 (max_cnt)); temp1  = 0
       allocate(temp1a(max_cnt)); temp1a = 0
       allocate(temp1b(max_sum)); temp1b = 0
       if (ipe == lmpi_id) then
          j = 0
          is = 1
          ie = 0
          do ielem = 1,grid%nelem
             fpc = grid%elem(ielem)%face_per_cell
             out3: do icell = 1,grid%elem(ielem)%ncell
                if (elem1_tag(ielem)%ctag(icell) == 0) then
                   ksum = 0
                   do i = 1,grid%elem(ielem)%face_per_cell
                      if (elem1_c2c(ielem)%c2c(i,icell) > 0) ksum = ksum + 1
                   end do
                   if (ksum > 0) then
                      j = j + 1
                      temp1(j) = grid%elem(ielem)%cl2g(icell)
                      temp1a(j)= ksum
                      do i = 1,grid%elem(ielem)%face_per_cell
                         if (elem1_c2c(ielem)%c2c(i,icell) > 0) then
                            ie = ie + 1
                            temp1b(ie:ie) = elem1_c2c(ielem)%c2c(i,icell)
                            !write(80000+lmpi_id,'(i0,":",i0,":",100(i0,1x))') &
                            !  temp1(j),temp1a(j),temp1b(is:ie)
                          end if
                      end do
                      is = ie + 1
                   end if
                end if
             end do out3
          end do
          if (j /= max_cnt)   write(*,*)"MISMATCH2 ",j,max_cnt
          if (ie /= max_sum)  write(*,*)"MISMATCH sum ",ielem,ie,max_sum
       end if
       call lmpi_bcast(temp1,ipe)  ! gcell
       call lmpi_bcast(temp1a,ipe) ! ct
       call lmpi_bcast(temp1b,ipe) ! values
       is = 1
       do ioff = 1,max_cnt
          gcell = temp1(ioff)
          ct = temp1a(ioff)
          ie = is + ct-1
              !write(90000+lmpi_id,'(i0,":",i0,":",100(i0,1x))')               &
              !  temp1(ioff),temp1a(ioff),temp1b(is:ie)
          if ((gcell>=my_clo).and.(gcell<=my_chi)) then
             js = adj(gcell)
             je = adj(gcell+1)-1
             adjncy(js:je) = temp1b(is:ie)
                 !write(81000+lmpi_id,'(i0,":",i0,":",100(i0,1x))')            &
                 !  gcell,ct,temp1b(is:ie)
                 !write(82000+lmpi_id,'(i0,":",i0,1x,i0,":",100(i0,1x))')      &
                 !  gcell,js,je,adjncy(js:je)
          end if
          is = ie + 1
       end do
       deallocate(temp1)
       deallocate(temp1a)
       deallocate(temp1b)
    end do ! ipe

!       do i = my_clo,my_chi ! ASSERT SUM(83K) == P_SUM(31K)
!          ct = adj(i+1)-adj(i)
!          if (ct > 0) then
!             allocate(temp1(ct)); temp1 = adjncy(adj(i):adj(i+1)-1)
!             call small_sort(ct,temp1)
!             write(83000+lmpi_id,'(i0,":",i0,":",100(i0,1x))')i,ct,temp1(1:ct)
!             deallocate(temp1)
!          end if
!            !   write(84000+lmpi_id,'(i0,":",i0,1x,i0,":",100(i0,1x))')       &
!            !     i,adj(i),adj(i+1)-1,adjncy(adj(i):adj(i+1)-1)
!       end do

  end subroutine exchange_c2c_cc
#endif

!============================== COMPUTE_C2C_CC_DRIVER ========================80
!
! Call compute_c2c_cc and retain module variables elem1_c2c, elem1_tag.
!
!=============================================================================80

  subroutine compute_c2c_cc_driver(grid)

    use grid_types, only : grid_type

    type(grid_type), intent(inout) :: grid

    integer :: ct, ielem, icell, fpc

  continue

! Compute the cc adj; allocate c2c storage and call compute_c2c_c

if ( allocated( elem1_c2c ) ) then
write(*,*) ' FIXME - runtime deallocate elem1_c2c'
deallocate(elem1_c2c)
endif

if ( allocated( elem1_tag ) ) then
write(*,*) ' FIXME - runtime deallocate elem1_tag'
deallocate(elem1_tag)
endif

    allocate(elem1_c2c(grid%nelem))
    allocate(elem1_tag(grid%nelem))
    call compute_c2c_cc(grid, elem1_c2c, elem1_tag)

! Compress and store elem1_c2c and elem1_tag into elem3_c2c

if ( allocated( elem3_c2c ) ) then
write(*,*) ' FIXME - runtime deallocate elem3_c2c'
deallocate(elem3_c2c)
endif


  allocate(elem3_c2c(grid%nelem))
  do ielem = 1,grid%nelem
     elem3_c2c(ielem)%ncell = 0
     nullify(elem3_c2c(ielem)%cl2g)
     nullify(elem3_c2c(ielem)%c2c)
  end do
  do ielem = 1,grid%nelem
     ct = 0
     do icell = 1,grid%elem(ielem)%ncell
        if (elem1_tag(ielem)%ctag(icell) == 0) ct = ct + 1
     end do
     if (ct > 0) then
        fpc = grid%elem(ielem)%face_per_cell
        elem3_c2c(ielem)%ncell = ct
        allocate(elem3_c2c(ielem)%cl2g(ct));    elem3_c2c(ielem)%cl2g  = 0
        allocate(elem3_c2c(ielem)%c2c(fpc,ct)); elem3_c2c(ielem)%c2c   = 0
     end if
  end do

  do ielem = 1,grid%nelem
     fpc = grid%elem(ielem)%face_per_cell
     ct = 0
     do icell = 1,grid%elem(ielem)%ncell
        if (elem1_tag(ielem)%ctag(icell) == 0) then
           ct = ct + 1
           elem3_c2c(ielem)%cl2g(ct)      = grid%elem(ielem)%cl2g(icell)
           elem3_c2c(ielem)%c2c(1:fpc,ct) = elem1_c2c(ielem)%c2c(1:fpc,icell)
           !write(101000+lmpi_id,'(1x,i0,1x,8(i0,1x))')                        &
           ! elem3_c2c(ielem)%cl2g(ct),elem3_c2c(ielem)%c2c(1:fpc,ct)
        end if
     end do
     elem3_c2c(ielem)%ncell = ct
  end do

! do ielem = 1,grid%nelem
!    fpc = grid%elem(ielem)%face_per_cell
!    do icell = 1,elem3_c2c(ielem)%ncell
!       write(201000+lmpi_id,'(1x,i0,1x,8(i0,1x))')                            &
!         elem3_c2c(ielem)%cl2g(icell),elem3_c2c(ielem)%c2c(1:fpc,icell)
!    end do
! end do

! Deallocate elem1_tag, elem1_c2c

   !   do ielem = 1,grid%nelem
   !      if (elem1_tag(ielem)%ct > 0) deallocate(elem1_tag(ielem)%ctag)
   !   end do
   !   deallocate(elem1_tag)

   !   do ielem = 1,grid%nelem
   !      if (elem1_c2c(ielem)%ncell > 0) then
   !         deallocate(elem1_c2c(ielem)%c2c);  nullify(elem1_c2c(ielem)%c2c)
   !         deallocate(elem1_c2c(ielem)%cl2g); nullify(elem1_c2c(ielem)%cl2g)
   !      end if
   !   end do
   !   deallocate(elem1_c2c)

  end subroutine compute_c2c_cc_driver

!============================== COMPUTE_C2C_CC ===============================80
!
! Compute module variables: vtxdist, adj, adjncy.
!
!=============================================================================80

  subroutine compute_c2c_cc(grid, elem1_c2c, elem1_tag)

    use grid_types, only : grid_type
    use sort,       only : heap_sort

    type(grid_type),                             intent(inout) :: grid
    type(elem1_c2c_type), dimension(grid%nelem), intent(inout) :: elem1_c2c
    type(elem1_type),     dimension(grid%nelem), intent(inout) :: elem1_tag

    integer :: i,j1,j2,ipos1,ipos2, ielem,icell,fpc,ncell,goff
    integer :: gcell, iface, nface
    integer :: nn1, nn2, nn3, nn4, ind1, ind2, gelem1, gelem2, gcell1, gcell2
    integer :: mm1,mm2,mm3,mm4,onn(4),ioff_arr(4), fpc_arr(4)

    integer(system_i1), dimension(:), allocatable :: face_elem
    integer, dimension(:), allocatable :: face_indx, ind

    real(dp), dimension(:), allocatable :: c2c_hash, face_hash

    type(elem2_type), dimension(:), allocatable :: elem2

  continue

    call compute_cc_adj_count(grid)

! Convert cl2g to truly global numbers. ! TBD why isn't it already

    goff = 0
    do ielem = 1,grid%nelem
       do icell = 1, grid%elem(ielem)%ncell
              grid%elem(ielem)%cl2g(icell) = grid%elem(ielem)%cl2g(icell)+goff
                 !write(26000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')                &
                 !  grid%elem(ielem)%cl2g(icell), grid%elem(ielem)%c2n(:,icell)
       end do
       goff = goff + grid%elem(ielem)%ncellg
    end do

! Allocate elem1 and elem2

    allocate(elem2(grid%nelem))

! gather level1 cell info into elem2

    call gather_level1_cc(grid,elem2,elem1_tag)

! c2c

  nface = 0
  do ielem = 1,grid%nelem
     ncell = grid%elem(ielem)%ncell
     if (ncell > 0) nface = nface + ncell*grid%elem(ielem)%face_per_cell
  end do
  do ielem = 1,grid%nelem
     if (elem2(ielem)%ct > 0) &
        nface = nface + elem2(ielem)%ct*grid%elem(ielem)%face_per_cell
  end do

  allocate(face_hash(nface)); face_hash = 0.0_dp
  allocate(face_indx(nface)); face_indx = 0
  allocate(face_elem(nface)); face_elem = 0

! local faces

  nface = 0
  do ielem = 1,grid%nelem
     ncell = grid%elem(ielem)%ncell
     if (ncell > 0) then
        fpc = grid%elem(ielem)%face_per_cell
        do icell = 1,ncell
          gcell = grid%elem(ielem)%cl2g(icell)
          !thash = 0.0_dp
          do iface = 1,fpc
            nface = nface + 1
            face_indx(nface) = -icell
            face_elem(nface) = ielem*10 + iface
            mm1=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,1),icell)
            mm2=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,2),icell)
            mm3=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,3),icell)
            if (grid%elem(ielem)%local_f2n(iface,1) ==                         &
                grid%elem(ielem)%local_f2n(iface,4)) then
               nn1 = min(mm1,mm2,mm3)
               nn3 = max(mm1,mm2,mm3)
               nn2 = (mm1+mm2+mm3)-(nn1+nn3)
               nn4 = 0
               face_hash(nface) = (nn1+nn2+nn3)*1._dp+                         &
                  sqrt(nn1*1._dp)*sqrt(nn2*1._dp)*sqrt(nn3*1._dp)
             !write(240000+lmpi_id,'(1x,E20.8,1x,i0,1x,i0," : ",4(i0,1x))')    &
             !face_hash(nface),face_indx(nface),face_elem(nface),nn1,nn2,nn3,nn4
              onn(1) = nn1; onn(2) = nn2; onn(3) = nn3; onn(4) = nn4
            else
             mm4=grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,4),icell)
              call order4(mm1,mm2,mm3,mm4,onn)
              face_hash(nface) = sum(onn)*1._dp +                              &
                 sqrt(onn(1)*1._dp)*sqrt(onn(2)*1._dp)*                        &
                 sqrt(onn(3)*1._dp)*sqrt(onn(4)*1._dp)
                !write(240000+lmpi_id,'(1x,E20.8,1x,i0,1x,i0," : ",4(i0,1x))') &
                !  face_hash(nface),face_indx(nface),face_elem(nface),onn(1:4)
            end if
            !thash(iface) = face_hash(nface)
            !write(40000+lmpi_id,'(1x,i0,1x,i0,1x,E20.8,1x,4(i0,1x))')         &
            !  gcell,nface,face_hash(nface),onn(1:4)
            !write(40000+lmpi_id,'(1x,E30.18,1x,4(i0,1x))')         &
            !  face_hash(nface),onn(1:4)
          end do
          !write(141000+lmpi_id,'(1x,i0,1x,8(E20.8,1x))') gcell,thash(1:fpc)
        end do
     end if
  end do

! remote faces

  do ielem = 1,grid%nelem
     ncell = elem2(ielem)%ct
     if (ncell > 0) then
        fpc = grid%elem(ielem)%face_per_cell
        do icell = 1,ncell
          gcell = elem2(ielem)%cl2g(icell)
          !write(41000+lmpi_id,*) gcell
          !thash = 0.0_dp
          do iface = 1,fpc
            nface = nface + 1
            face_indx(nface) = gcell
            face_elem(nface) = ielem*10 + iface
            mm1=elem2(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,1),icell)
            mm2=elem2(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,2),icell)
            mm3=elem2(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,3),icell)
            if (grid%elem(ielem)%local_f2n(iface,1) ==                         &
                grid%elem(ielem)%local_f2n(iface,4)) then
               nn1 = min(mm1,mm2,mm3)
               nn3 = max(mm1,mm2,mm3)
               nn2 = (mm1+mm2+mm3)-(nn1+nn3)
               nn4 = 0
               face_hash(nface) = (nn1+nn2+nn3)+                               &
                 sqrt(nn1*1._dp)*sqrt(nn2*1._dp)*sqrt(nn3*1._dp)
            else
             mm4=elem2(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,4),icell)
             call order4(mm1,mm2,mm3,mm4,onn)
             face_hash(nface) = sum(onn)*1._dp +                               &
                sqrt(onn(1)*1._dp)*sqrt(onn(2)*1._dp)*                         &
                sqrt(onn(3)*1._dp)*sqrt(onn(4)*1._dp)
            end if
            !write(40000+lmpi_id,'(1x,i0,1x,i0,1x,E20.8,1x,4(i0,1x))')         &
            !  gcell,nface,face_hash(nface),onn(1:4)
            !write(40000+lmpi_id,'(1x,E20.8,1x,4(i0,1x))')         &
            !  face_hash(nface),onn(1:4)
            ! write(240000+lmpi_id,'(1x,E20.8,1x,i0,1x,i0," : ",4(i0,1x))')    &
            !  face_hash(nface),face_indx(nface),face_elem(nface),onn(1:4)
            !thash(iface) = face_hash(nface)
          end do
          !write(141000+lmpi_id,'(1x,i0,1x,8(E20.8,1x))') gcell,thash(1:fpc)
        end do
     end if
  end do

! Deallocate elem2

  do ielem = 1,grid%nelem
     if (elem2(ielem)%sz > 0) then
        deallocate(elem2(ielem)%c2n);  nullify(elem2(ielem)%c2n)
        deallocate(elem2(ielem)%cl2g); nullify(elem2(ielem)%cl2g)
     end if
  end do
  deallocate(elem2)

! sort hash table

  allocate(ind(nface)); ind = 0
  call heap_sort(nface,face_hash,ind)
  allocate(c2c_hash(nface)); c2c_hash = 0.0_dp
  c2c_hash = face_hash(ind)

  deallocate(face_hash)
! do i = 1,nface
!    write(36000+lmpi_id,*) i,c2c_hash(i)
!    write(37000+lmpi_id,*) c2c_hash(i)
!    write(38000+lmpi_id,'(E30.15,1x,i0)') c2c_hash(i),face_indx(ind(i))
! end do
! if (lmpi_master) write(*,*)"AF hash sort ",lmpi_id

! allocate c2c storage

  do ielem = 1,grid%nelem
     elem1_c2c(ielem)%ncell  = 0
     nullify(elem1_c2c(ielem)%c2c)
     ncell = grid%elem(ielem)%ncell
     if (ncell > 0) then
        fpc = grid%elem(ielem)%face_per_cell
        elem1_c2c(ielem)%ncell = ncell
        allocate(elem1_c2c(ielem)%c2c(fpc,ncell))
        allocate(elem1_c2c(ielem)%cl2g(ncell))
        elem1_c2c(ielem)%c2c = 0
        elem1_c2c(ielem)%cl2g = 0
     end if
  end do

! Compute ioff (for a given local face, find the face position in c2c)

   fpc_arr(1:grid%nelem) = grid%elem(1:grid%nelem)%face_per_cell

   ioff_arr = 0
   if (grid%nelem > 1) then
      do ielem = 2,grid%nelem
         ioff_arr(ielem) = ioff_arr(ielem-1) +                                 &
           grid%elem(ielem-1)%ncell*fpc_arr(ielem-1)
      end do
   end if

! gather c2c results

  do i = 1,nface-1
     if (c2c_hash(i) == c2c_hash(i+1)) then
        ind1 = face_indx(ind(i))
        ind2 = face_indx(ind(i+1))
        if ((ind1 < 0).or.(ind2 < 0)) then

           j1     = face_elem(ind(i))
           gelem1 = floor(real(j1)/10.0)
           ipos1  = j1 - gelem1*10
           if (ind1 < 0) then
              gcell1 = grid%elem(gelem1)%cl2g(-ind1)
           else
              gcell1 = ind1
           end if
          !write(250000+lmpi_id,'(1x,E20.8,1x,5(i0,1x))')                      &
          !  c2c_hash(i),1,ind1,j1,gelem1,ipos1

           j2     = face_elem(ind(i+1))
           gelem2 = floor(real(j2)/10.0)
           ipos2  = j2 - gelem2*10
           if (ind2 < 0) then
              gcell2 = grid%elem(gelem2)%cl2g(-ind2)
           else
              gcell2 = ind2
           end if
           !write(250000+lmpi_id,'(1x,E20.8,1x,5(i0,1x))')                     &
           !  c2c_hash(i),2,ind2,j2,gelem2,ipos2

           if (ind1 < 0) elem1_c2c(gelem1)%c2c(ipos1,-ind1) = gcell2
           if (ind2 < 0) elem1_c2c(gelem2)%c2c(ipos2,-ind2) = gcell1
        end if
     end if
  end do
  call lmpi_conditional_stop(0)
  deallocate(face_indx)
  deallocate(face_elem)
  deallocate(ind)
  deallocate(c2c_hash)

! ASSERT pp_39K p_3100 (Back/122910sod)
! do ielem = 1,grid%nelem
!    ncell = grid%elem(ielem)%ncell
!    fpc   = grid%elem(ielem)%face_per_cell
!    do icell = 1,ncell
!       elem1_c2c(ielem)%cl2g(icell) = -grid%elem(ielem)%cl2g(icell)
!       if (elem1_tag(ielem)%ctag(icell) == 0) then
!          elem1_c2c(ielem)%cl2g(icell) = -elem1_c2c(ielem)%cl2g(icell)
!          write(39000+lmpi_id,'(1x,i0,1x,100(i0,1x))')                        &
!            grid%elem(ielem)%cl2g(icell),elem1_c2c(ielem)%c2c(:,icell)
!       end if
!    end do
! end do

  end subroutine compute_c2c_cc

!============================== gather_level1_cc =============================80
!
! Compute cc_adj_count (used forward in LibF90/pparty_mixed_element.f90
!   routine edge_pointer_driver_cc.
! Unique edges: only add nodes in short list, if node >.
! Implies some nodes with no edges.
!
!=============================================================================80

  subroutine compute_cc_adj_count(grid)

    use grid_types, only : grid_type
    use sort,       only : small_sort

    type(grid_type), intent(in) :: grid

    integer :: i,j,k,m, is,ie, ielem, icell, node1,node2
    integer, dimension(:), allocatable :: ia,ja,fwa,temp

  continue

! Compute cc_adj_count

  is = pp_nhead(lmpi_id)
  ie = pp_ntail(lmpi_id)
  allocate(ia(is:ie)); ia = 0
  do ielem = 1,grid%nelem
     do icell = 1,grid%elem(ielem)%ncell
        do j = 1,grid%elem(ielem)%edge_per_cell
            node1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(j,1),icell)
            node2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(j,2),icell)
            if (((node1 >= is).and.(node1 <= ie)).and.(node2 > node1)) &
               ia(node1) = ia(node1) + 1
            if (((node2 >= is).and.(node2 <= ie)).and.(node1 > node2)) &
                ia(node2) = ia(node2) + 1
        end do
     end do
  end do

  j = sum(ia)
  allocate(ja(j));        ja  = 0
  allocate(fwa(is:ie+1)); fwa = 0
  fwa(is) = 1
  do i = is,ie
     fwa(i+1) = fwa(i) + ia(i)
  end do

  ia = 0
  do ielem = 1,grid%nelem
     do icell = 1,grid%elem(ielem)%ncell
        do j = 1,grid%elem(ielem)%edge_per_cell
            node1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(j,1),icell)
            node2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(j,2),icell)
            if (((node1 >= is).and.(node1 <= ie)).and.(node2 > node1)) then
               ja(fwa(node1)+ia(node1)) = node2
               ia(node1) = ia(node1) + 1
            end if
            if (((node2 >= is).and.(node2 <= ie)).and.(node1 > node2)) then
               ja(fwa(node2)+ia(node2)) = node1
               ia(node2) = ia(node2) + 1
            end if
        end do
     end do
  end do

if ( allocated( cc_adj_count ) ) then
write(*,*) ' FIXME - runtime deallocate cc_adj_count'
deallocate(cc_adj_count)
endif


  allocate(cc_adj_count(is:ie)); cc_adj_count = 0
  do i = is,ie
     j = fwa(i+1) - fwa(i)
     if (j > 0) then
        allocate(temp(j+1))
        temp(1:j) = ja(fwa(i):fwa(i+1)-1)
        temp(j+1:j+1) = grid%nnodesg+1
        call small_sort(j+1,temp)
        m = 0
        do k = 1,j
           if (temp(k) /= temp(k+1)) then
              cc_adj_count(i) = cc_adj_count(i) + 1
              ja(fwa(i)+m) = temp(k)
              m = m + 1
           end if
        end do
        deallocate(temp)
     end if
  end do
  j = sum(cc_adj_count)

if ( allocated( cc_adj ) ) then
write(*,*) ' FIXME - runtime deallocate cc_adj'
deallocate(cc_adj)
endif

  allocate(cc_adj(j)); cc_adj = 0

  j = 1 ! start fwa
  k = 0 ! end   fwa
  do i = is,ie
     k = k + cc_adj_count(i)
     cc_adj(j:k) = ja(fwa(i):fwa(i)+cc_adj_count(i)-1)
     j = k + 1
  end do

! ASSERT -- validate cc_adj_count, cc_adj
! j = 1
! do i = is,ie
!    write(920+lmpi_id,'(1x,i0," : ",i0,1x," :: ",100(i0,1x))') &
!      i,cc_adj_count(i),cc_adj(j:j+cc_adj_count(i)-1)
!    j = j + cc_adj_count(i)
! end do

! write(*,*)"END compute_cc_adj_count ",lmpi_id,sum(adj_count)

  deallocate(ia,ja,fwa)

  end subroutine compute_cc_adj_count

!============================== gather_level1_cc =============================80
!
! Compute module variables: vtxdist, adj, adjncy
!
!=============================================================================80

  subroutine gather_level1_cc(grid,elem2,elem1_tag)

    use grid_types, only : grid_type
    use sort,       only : binary_search

    type(grid_type),                         intent(in)    :: grid
    type(elem2_type), dimension(grid%nelem), intent(inout) :: elem2
    type(elem1_type), dimension(grid%nelem), intent(inout) :: elem1_tag

    integer :: i,j,k, ielem, icell, npc, ncell, ipe
    integer :: my_nlo, my_nhi, node, inode, nnodes1
    integer :: my_count, my_count2, off_count, gcell, ioff

    integer, dimension(:),   allocatable :: temp1, max_ct
    integer, dimension(:,:), allocatable :: temp2

    integer(system_i1), dimension(:), allocatable :: tag_n1

    type(elem1_type), dimension(:), allocatable :: elem1

  continue

    my_nlo = pp_nhead(lmpi_id)
    my_nhi = pp_ntail(lmpi_id)

! Initialize elem1 and elem1_tag

    allocate(elem1(grid%nelem))
    do ielem = 1,grid%nelem
       ncell = grid%elem(ielem)%ncell
       elem1(ielem)%ct = ncell
       elem1_tag(ielem)%ct = ncell
       if (ncell > 0) then
          allocate(elem1(ielem)%ctag(ncell)); elem1(ielem)%ctag = 0
          allocate(elem1_tag(ielem)%ctag(ncell)); elem1_tag(ielem)%ctag = 0
       end if
    end do

! Initialize elem2

    do ielem = 1,grid%nelem
       j = nint(grid%elem(ielem)%ncellg*1._dp/lmpi_nproc*1._dp)
       if (j < 1000) j = 1000
       elem2(ielem)%sz = j
       elem2(ielem)%ct = 0
       npc = grid%elem(ielem)%node_per_cell
       nullify(elem2(ielem)%c2n)
       allocate(elem2(ielem)%c2n(npc,j)); elem2(ielem)%c2n  = 0
       nullify(elem2(ielem)%cl2g)
       allocate(elem2(ielem)%cl2g(j));    elem2(ielem)%cl2g = 0
    end do

! Create (level1 for nodes present) tag_n1

    allocate(tag_n1(grid%nnodesg));  tag_n1 = 0 ! TBD MEMORY
    j = 0
    do ielem = 1,grid%nelem
       ncell = grid%elem(ielem)%ncell
       if (ncell > 0) then
          npc = grid%elem(ielem)%node_per_cell
          do icell = 1,ncell
             j = j + 1
             do inode = 1,npc
                node = grid%elem(ielem)%c2n(inode,icell)
                if ((node < my_nlo).or.(node > my_nhi)) tag_n1(node) = 1
             end do
          end do
       end if
    end do

! count level1 nodes

    nnodes1 = 0
    do i = 1,grid%nnodesg
       if (tag_n1(i) == 1) nnodes1 = nnodes1 + 1
       !if (tag_n1(i) == 1) write(28000+lmpi_id,*) i
    end do

! Allocate elem2

    do ielem = 1,grid%nelem
       j = nint(grid%elem(ielem)%ncellg*1._dp/lmpi_nproc*1._dp)
       if (j < 1000) j = 1000
       elem2(ielem)%sz = j
       elem2(ielem)%ct = 0
       npc = grid%elem(ielem)%node_per_cell
       nullify(elem2(ielem)%c2n)
       allocate(elem2(ielem)%c2n(npc,j)); elem2(ielem)%c2n  = 0
       nullify(elem2(ielem)%cl2g)
       allocate(elem2(ielem)%cl2g(j));    elem2(ielem)%cl2g = 0
    end do

! Pass (non-passed) cells and gather level1 cells

    allocate(max_ct(grid%nelem))
    do ipe = 0,lmpi_nproc-1
       max_ct = 0
       if (ipe == lmpi_id) then
          do ielem = 1,grid%nelem
             ncell = grid%elem(ielem)%ncell
             if (ncell > 0) then
                do i = 1,ncell
                   if (elem1(ielem)%ctag(i)==0) max_ct(ielem)=max_ct(ielem)+1
                end do
             end if
          end do
         !write(*,'("MAX_CT ",2(i0,1x)," : ",3(i0,1x))') lmpi_id,ipe,max_ct
       end if
       call lmpi_bcast(max_ct,ipe)

       off_elem: do ielem = 1,grid%nelem
          off_count = max_ct(ielem)
          npc = grid%elem(ielem)%node_per_cell
         !write(*,*)"IELEM ",lmpi_id,ipe,ielem,off_count,npc
          if (off_count == 0) cycle off_elem
          allocate(temp1(off_count));     temp1 = 0
          allocate(temp2(npc,off_count)); temp2 = 0
          if (ipe == lmpi_id) then
             j = 0
             do icell = 1,grid%elem(ielem)%ncell
                if (elem1(ielem)%ctag(icell) == 0) then
                   elem1(ielem)%ctag(icell) = 1
                   j = j + 1
                   temp1(j) = grid%elem(ielem)%cl2g(icell)
                   temp2(1:npc,j) = grid%elem(ielem)%c2n(1:npc,icell)
                end if
             end do
             if (j /= off_count) write(*,*)"MISMATCH ",j,off_count
                !do j = 1,off_count
                !write(29000+lmpi_id,'(1x,i0," : ",8(i0,1x))') &
                !  temp1(j),temp2(1:npc,j)
                !end do
          end if
          call lmpi_bcast(temp1,ipe)
          call lmpi_bcast(temp2,ipe)
          if (lmpi_id /= ipe) then
            !do j = 1,off_count
            !  write(29000+lmpi_id,'(1x,i0," : ",8(i0,1x))') temp1(j),temp2(:,j)
            !end do
             my_count = grid%elem(ielem)%ncell
             out1 : do ioff = 1,off_count
              j = 0
              do k = 1,npc
                 j = tag_n1(temp2(k,ioff))
                 if (j == 1) exit
              end do
              if (j == 0) cycle out1
              gcell = temp1(ioff)
              k = 0
              if (my_count > 0) then
                k=binary_search(my_count,grid%elem(ielem)%cl2g,gcell)
                if (k > 0) then
                   elem1(ielem)%ctag(k) = 1
                   elem1_tag(ielem)%ctag(k) = 1
                end if
              end if
              if (k == 0) then
                 my_count2 = elem2(ielem)%ct
                 if (my_count2 > 0) then
                     do j = 1,my_count2
                       if (elem2(ielem)%cl2g(j)==gcell) cycle out1
                    end do
                    if (my_count2 == elem2(ielem)%sz)                          &
                      call extend_elem2(grid%nelem,ielem,elem2)
                 end if ! my_count2 > 0
                 elem2(ielem)%ct = elem2(ielem)%ct + 1
                 elem2(ielem)%cl2g(elem2(ielem)%ct) = gcell
                 elem2(ielem)%c2n(1:npc,elem2(ielem)%ct) = temp2(1:npc,ioff)
              end if ! k == 0
             end do out1
          end if
          deallocate(temp1)
          deallocate(temp2)
       end do off_elem
    end do
    deallocate(tag_n1)
    deallocate(max_ct)

    do ielem = 1,grid%nelem
       if (elem1(ielem)%ct > 0) deallocate(elem1(ielem)%ctag)
    end do
    deallocate(elem1)

! dump elem2
!   do ielem = 1,grid%nelem
!      ncell = elem2(ielem)%ct
!      if (ncell > 0) then
!         npc = grid%elem(ielem)%node_per_cell
!         do icell = 1,ncell
!            write(27000+lmpi_id,'(1x,i0,1x,8(1x,i0,1x))')                     &
!              elem2(ielem)%cl2g(icell), elem2(ielem)%c2n(1:npc,icell)
!         end do
!      end if
!   end do

  end subroutine gather_level1_cc

!============================== extend_elem2 =================================80
!
! Extend elem2
!
!=============================================================================80

  subroutine extend_elem2(g_nelem,ielem,elem2)

    integer,                              intent(in)    :: g_nelem, ielem
    type(elem2_type), dimension(g_nelem), intent(inout) :: elem2

    integer :: npc, old_size, new_size
    integer, dimension(:),   allocatable :: temp1
    integer, dimension(:,:), allocatable :: temp2

    continue

      old_size = elem2(ielem)%sz
      new_size = nint(old_size * 1.10)
      elem2(ielem)%sz = new_size
      ! write(*,*)"EXTEND ",lmpi_id,old_size,new_size

      allocate(temp1(old_size)); temp1 = 0
      temp1(1:old_size) = elem2(ielem)%cl2g(1:old_size)
      deallocate(elem2(ielem)%cl2g); nullify(elem2(ielem)%cl2g)
      allocate(elem2(ielem)%cl2g(new_size)); elem2(ielem)%cl2g = 0
      elem2(ielem)%cl2g(1:old_size) = temp1(1:old_size)
      deallocate(temp1)

      npc = size(elem2(ielem)%c2n,1)
      allocate(temp2(npc,old_size)); temp2 = 0
      temp2(1:npc,1:old_size) = elem2(ielem)%c2n(1:npc,1:old_size)
      deallocate(elem2(ielem)%c2n); nullify(elem2(ielem)%c2n)
      allocate(elem2(ielem)%c2n(npc,new_size)); elem2(ielem)%c2n = 0
      elem2(ielem)%c2n(1:npc,1:old_size) = temp2(1:npc,1:old_size)
      deallocate(temp2)
      ! write(*,*)"EXTEND ",lmpi_id,ielem,elem2(ielem)%ct,elem2(ielem)%sz

  end subroutine extend_elem2

!============================== ORDER4 =======================================80
!
! Order 4 ints.
!
!=============================================================================80
  subroutine order4(n1,n2,n3,n4,onn)
    use sort, only : small_sort
    integer,               intent(in) :: n1,n2,n3,n4
    integer, dimension(4), intent(out):: onn
   !integer, dimension(4) :: nn,inn
   !logical, dimension(4) :: L
  continue
    onn = (/n1,n2,n3,n4/)
    call small_sort(4,onn) ! workaround to complexify
   !inn = (/n1,n2,n3,n4/)
   !nn(1) = minloc(inn,1)
   !nn(4) = maxloc(inn,1)
   !L = .true.
   !L(nn(1)) = .false.
   !L(nn(4)) = .false.
   !nn(2) = minloc(inn,1,L)
   !nn(3) = maxloc(inn,1,L)
   !onn = inn(nn)
  end subroutine order4

!============================== CHECK_METIS_2D ===============================80
!
! Check to make sure that metis has not cut any edges that span the two
! y=constant planes in 2D cases
!
!=============================================================================80
  subroutine check_metis_2d(grid)

    use grid_types, only : grid_type
    use sort,       only : binary_search

    type(grid_type), intent(in) :: grid

    integer :: fixed_edges, max_fixed_edges, nnodes0, nnodes01
    integer :: last, is, ict, ipe, ie, i, n1, n2, j
    integer :: part1, part2, tempint, nnodes1, summer, ihead, itail
    integer :: n1_local, n2_local, k, weight

    integer, dimension(:), allocatable :: part_vector, temp_part
    integer, dimension(:), allocatable :: temp_l2g_l1

  continue

    if (lmpi_id == 0) write(*,*) '    ... checking for spanwise edge cuts.'

!   set an arbitrary limit on the number of spanwise edges that we will fix
!   in the event that metis cuts an edge that it shouldn't have...more than
!   this and something is probably fubar'ed

    max_fixed_edges = grid%nnodesg/2/10   ! nnodesg/2 = no. of spanwise edges
                                          ! let's take 10% of that as a max

    nnodes0  = grid%nnodes0
    nnodes01 = grid%nnodes01
    nnodes1  = nnodes01 - nnodes0

    ihead = pp_nhead(lmpi_id)
    itail = pp_ntail(lmpi_id)

! set up a local copy of the partition vector that will go out through
! level-1 nodes

    allocate(part_vector(nnodes01)); part_vector = 0
    part_vector(1:nnodes0) = metis_data(1:nnodes0)

! expand our part_vector out through level-1 nodes

    last = nnodes0 + 1
    is   = 1
    ict  = nnodes01 - nnodes0

    do ipe = 0, lmpi_nproc-1
      if (lmpi_id == ipe) ie = grid%l2g(nnodes0)
      call lmpi_bcast(ie,ipe)
      allocate(temp_part(is:ie)); temp_part = 0
      if (lmpi_id == ipe) temp_part(is:ie) = part_vector(1:nnodes0)

      call lmpi_bcast(temp_part,ipe)

      if ((ict > 0).and.(last <= nnodes01)) then
        do i = is,ie
          if (i == grid%l2g(last)) then
            part_vector(last) = temp_part(i)
            last = last + 1
            if (last > nnodes01) exit
          end if
        end do
      end if
      deallocate(temp_part)
      is = ie + 1
    end do

! make an array copy once to avoid the compiler doing it many times later on

    allocate(temp_l2g_l1(nnodes1))
    temp_l2g_l1(:) = grid%l2g(nnodes0+1:)

! go through adjacencies and check the edges that should not have been cut

    fixed_edges = 0

    j = 1
    do i = ihead, itail
      n1 = i    ! global node number
      n1_local = (i-ihead) + 1
      search : do k = 1, adj_count(n1_local)
        weight = edge_weight(j)
        if ( weight == 1000 ) then
          n2 = adjncy(j)   ! global node number
          n2_local = (n2-ihead) + 1
          if ( (n2<ihead).or.(n2>itail) ) then
            n2_local = nnodes0 + binary_search(nnodes1,temp_l2g_l1,n2)
          endif
          part1 = part_vector(n1_local)
          part2 = part_vector(n2_local)
          if ( part1 /= part2 ) then
            if ( part1 < part2 ) then
              part_vector(n2_local) = part_vector(n1_local)
              if ( n1 < n2 ) fixed_edges = fixed_edges + 1
            else
              part_vector(n1_local) = part_vector(n2_local)
              if ( n1 < n2 ) fixed_edges = fixed_edges + 1
            endif
          endif
        endif
        j = j + 1
      end do search
    end do

!   check that we don't have too many fixed edges

    call lmpi_reduce(fixed_edges, tempint)
    fixed_edges = tempint
    call lmpi_bcast(fixed_edges)

    if ( fixed_edges > 0 .and. lmpi_master ) then
      write(*,'(a,i7,a)') '     ... fixed ',fixed_edges,                       &
                          ' spanwise edges that metis cut.'
    end if

    if ( fixed_edges > max_fixed_edges ) then
      if ( lmpi_master ) then
        write(*,*)
        write(*,*) 'pp stopping because this number of cuts exceeds the limit.'
        write(*,*) ' ...grid%project=',trim(grid%project)
        write(*,*) ' ...fixed_edges=',fixed_edges,&
                   ' > max_fixed_edges=',max_fixed_edges
        write(*,*) ' ...suggestion: run with fewer processors.'
      endif
      call lmpi_conditional_stop(1,'check_metis_2d')
      stop
    end if

! double check to make sure final partitioning is truly what we want

    summer = 0

    j = 1
    do i = ihead, itail
      n1_local = (i-ihead) + 1
      search2 : do k = 1, adj_count(n1_local)
        weight = edge_weight(j)
        if ( weight == 1000 ) then
          n2 = adjncy(j)
          n2_local = (n2-ihead) + 1
          if ( (n2<ihead).or.(n2>itail) ) then
            n2_local = nnodes0 + binary_search(nnodes1,temp_l2g_l1,n2)
          endif
          part1 = part_vector(n1_local)
          part2 = part_vector(n2_local)
          if ( part1 /= part2 ) then
            summer = summer + 1
          endif
        endif
        j = j + 1
      end do search2
    end do

    call lmpi_reduce(summer,tempint)
    summer = tempint
    call lmpi_bcast(summer)

    if ( summer > 0 ) then
      if ( lmpi_master ) then
        write(*,*)
        write(*,*) 'Stopping: 2D edge cuts still found!'
      endif
      call lmpi_die()
      stop
    end if

! Reconcile the metis_data vector based on what we just did

    metis_data(1:nnodes0) = part_vector(1:nnodes0)

    deallocate(part_vector,temp_l2g_l1)

! Ensure that everyone got assigned somewhere in the end

    do i = 1, nnodes0
      if ( metis_data(i) < 1 .or. metis_data(i) > lmpi_nproc ) then
        write(*,*) 'Invalid part vector in check_metis_2d.'
        call lmpi_die()
        stop
      endif
    end do

  end subroutine check_metis_2d

!============================== CHECK_PARTITION_LINES ========================80
!
! Check that metis has kept all nodes in a line on the same processor.
! The first node in the line is called a "root_node".
!
!=============================================================================80

  subroutine check_partition_lines( n_lines, line, endline)

    integer,                 intent(in) :: n_lines
    integer, dimension(:,:), intent(in) :: line
    integer, dimension(:),   intent(in) :: endline

    integer :: j, k, my_s, my_e, iline, ierr, node, pid, pidg

    integer, dimension(:), allocatable :: root_nodes, root_value, root_value1

  continue

    if (lmpi_master) write(*,*) &
    '    ... checking partition line cuts...n_lines=',n_lines

    my_s = pp_nhead(lmpi_id)
    my_e = pp_ntail(lmpi_id)

    allocate(root_nodes(n_lines)); root_nodes = 0
    do iline = 1,n_lines
       root_nodes(iline) = line(iline,1)
    end do

    allocate(root_value(n_lines)); root_value = -1

    do iline = 1,n_lines
       ierr = 0
       pid  = 0
       node = root_nodes(iline)
       if ((node >= my_s).and.(node <= my_e)) then
          k = (node - my_s)+1
          pid = metis_data(k)
          root_value(iline) = pid
       end if
       call lmpi_reduce(pid,pidg)
       call lmpi_bcast(pidg)
       if ( pid > 0 ) ierr = pid - pidg
       call lmpi_conditional_stop(ierr,&
       'lines span processors:check_partition_lines')
    end do

    deallocate(root_nodes)
    allocate(root_value1(n_lines)); root_value1 = 0
    call lmpi_max(root_value,root_value1)
    deallocate(root_value)
    call lmpi_bcast(root_value1)

    ierr = 0
    outer: do iline = 1,n_lines
       do j = 2,endline(iline)
          node = line(iline,j)
          if ((node >= my_s).and.(node <= my_e)) then
             k = (node - my_s)+1
             if (metis_data(k) /= root_value1(iline)) then
                write(*,*)"Line has different metis value ",lmpi_id,iline
                write(*,*)"root value, j, metis value ",                       &
                           root_value1(iline),j,metis_data(k)
                ierr = 1
                cycle outer
             end if
          end if
       end do
    end do outer
    call lmpi_conditional_stop(ierr)
    if (lmpi_master) write(*,*)'    ... partition line cuts are fine.'

    deallocate(root_value1)

  end subroutine check_partition_lines

#ifdef HAVE_PARMETIS
!============================== PM_min_setup =================================80
!
! Partitions mesh in parallel
!
! ParMetis_min -- run ParMetis with subset of cores
!
! In general, one could use a subset (use_proc) of cores
! to call ParMetis out of the existing current set of cores (lmpi_nproc)
! to create a number of partitions (gen_proc).

!   lmpi_nproc -- number of current cores (from mpiexec -np)
!   use_proc -- number of cores to use to call ParMetis (subset of run_proc)
!   gen_proc -- number of targeted partitions
!
! However, in the FUN3D paradigm, lmpi_nproc == gen_proc, and use_proc
! is computed using a look-up table, thus balancing the number of cores
! used to call ParMetis against memory constraints (gathered data).
! Realizing, that the memory is spread out over the multi-core nodes.
!
!   use_proc -- number of cores to use to call ParMetis (subset of run_proc)
!
!=============================================================================80

  subroutine PM_min_setup(grid_nnodesg,do_min,use_proc,js,je,procs,is,ie,      &
                          iam_in,myid)

    use info_depr, only : pp_cmd_PM_use_proc

    integer, intent(in) :: grid_nnodesg
    logical, intent(out):: do_min, iam_in
    integer, intent(out):: use_proc, is, ie, myid
    integer, dimension(:), pointer :: js, je, procs

    integer :: i,ival,incr,irem

    integer, parameter               :: million = 1000000 ! 2000
    integer, dimension(8), parameter :: sc = (/4, 8,16,32,64,128,256,512/)
                                      !        6,12,24,48,96,192,384,512

  ! if (overriding use_proc) or for many cores

! if ((pp_cmd_PM_use_proc > 0).or.(lmpi_nproc > 16777216))
  if ((pp_cmd_PM_use_proc > 0).or.(lmpi_nproc > 32)) then

     do_min = .true.
     ival = nint(real(grid_nnodesg)/real(million))

     use_proc = 0
     if (ival <= sc(1)) use_proc = sc(1)
     if (ival >= sc(size(sc))) use_proc = sc(size(sc))
     if (pp_cmd_PM_use_proc > 0) then
        use_proc = pp_cmd_PM_use_proc
        if (use_proc==1) use_proc = 2
        if(lmpi_master)write(*,*)'    ... overriding use_proc for PM ',use_proc
     end if

     if (use_proc == 0) then
        do i = 2,size(sc)
           if (ival <= (sc(i)+sc(i)/2)) then ! 1.5
              use_proc = sc(i)
              exit
           end if
        end do
     end if

     if (use_proc >= lmpi_nproc) then
        if (lmpi_master)                                                       &
           write(*,*) "    ... not minimizing ParMetis ",use_proc,lmpi_nproc
        do_min = .false.
        return
     end if

     incr = lmpi_nproc/use_proc
     irem = lmpi_nproc-(incr*use_proc)
     !write(*,*)"USE_proc ",lmpi_id,use_proc,incr,irem
     allocate(js(use_proc)); js = 0
     allocate(je(use_proc)); je = incr
     if (irem /= 0) je(1:irem) = je(1:irem)+1
     js(1) = 0
     je(1) = je(1)-1
     do i = 2,use_proc
        js(i) = je(i-1)+1
        je(i) = js(i)+(je(i)-1)
     end do
     allocate(procs(use_proc)); procs = js
     ! JS:JE -- the range of orig PEs managed by active PE
     !if (lmpi_id == 0) then
     !   write(*,*)"JS ",js; write(*,*)"JE ",je
     !end if

     iam_in = (lmpi_id == 0)
     do i = 2,use_proc
        if (lmpi_id == procs(i)) iam_in = .true.
     end do
     procs = js
     ! write(*,*)'procs ',procs

     if (iam_in) then
        do i = 1,use_proc
           if (lmpi_id == procs(i)) myid = i-1
        end do
        ! write(*,*)'procs ',lmpi_id,':: ',procs,"::",myid
        ! IS,IE -- the start and end of the orig PEs managed by active PE
        is = js(myid+1)
        ie = je(myid+1)
        !write(*,*)"IS,IE ",lmpi_id,is,ie
     else
       ! on non-iam_in, IS is the managing PE
       is = 0
       do i = 1,use_proc
          if (procs(i) < lmpi_id) is = procs(i)
       end do
     end if
  else
     iam_in = .false.
     do_min = .false.
  end if

  if (do_min.and.lmpi_master)                                                 &
      write(*,*) "    ... Use subset of cores to call ParMetis ",use_proc

  end subroutine PM_min_setup


!============================== PM_min_gather ================================80
!
! Partitions mesh in parallel
!
!=============================================================================80

  subroutine PM_min_gather(use_proc,je,is,ie,iam_in,myid,ncon,twod,            &
             weight,edge_weight,                                               &
             temp_adj, temp_adjncy, temp_weight, temp_edge_weight, temp_vtxdist)

    use lmpi, only : lmpi_allgather, lmpi_allgatherv

    logical, intent(in) :: iam_in, twod
    integer, intent(in) :: use_proc, is, ie, myid, ncon

    integer, dimension(:), intent(in) :: je, edge_weight
    integer, dimension(:), pointer    :: weight ! intent(in)

    integer, dimension(:), pointer :: temp_adj,temp_adjncy, temp_weight
    integer, dimension(:), pointer :: temp_edge_weight, temp_vtxdist

    integer :: i, j, isize, jsize, ksize, lsize, sizes(3)
    integer :: ioff, joff, koff, loff, ierr, ivsize_min, total_size

    integer, dimension(:), allocatable :: temp, temp2

  continue

    allocate(temp(0:lmpi_nproc-1)); temp = 0
    call lmpi_allgather(size(adjncy),temp)
    if (iam_in) then

       ! vtxdist
       allocate(temp_vtxdist(use_proc+1)); temp_vtxdist = 0
       temp_vtxdist(1) = 1
       do i = 2,use_proc+1
          temp_vtxdist(i) = pp_ntail(je(i-1))+1
       end do
       temp_vtxdist(use_proc+1) = pp_ntail(lmpi_nproc-1)+1
       ! write(*,'("PPN ",i0,1x,":: ",16(i0,1x))') lmpi_id,pp_ntail
       ! write(*,'("JE  ",i0,1x,":: ",16(i0,1x))') lmpi_id,je
       ! write(*,'("VT  ",i0,1x,":: ",16(i0,1x))') lmpi_id,temp_vtxdist

       ! adj
       jsize = (temp_vtxdist(myid+2)-temp_vtxdist(myid+1))+1
       allocate(temp_adj(jsize)); temp_adj = 0
       joff = size(adj)
       temp_adj(1:joff) = adj(1:joff)
       ! write(*,*)"Assign ADJ ",myid,adj(1:4)

       ! adjncy
       ksize = sum(temp(is:ie))
       allocate(temp_adjncy(ksize)); temp_adjncy = 0
       ! write(*,*)"ALLOC temp_adj ",                                          &
       !   ksize,lmpi_id,myid+1,temp_vtxdist(myid+1),size(adjncy)
       koff = size(adjncy)
       temp_adjncy(1:koff) = adjncy(1:koff)

       ! temp_weight
       ivsize_min = sum(pp_nsize(is:ie))*ncon
       allocate(temp_weight(ivsize_min)); temp_weight = 0
       ioff = size(weight)
       temp_weight(1:ioff) = weight(1:ioff)

       if (twod) then
          lsize = ksize
       else
          lsize = 1
       end if
       allocate(temp_edge_weight(lsize))
       temp_edge_weight = 0
       loff = koff
       if (twod) temp_edge_weight(1:loff) = edge_weight(1:loff)

       if (lmpi_master) then
          total_size = size(temp_vtxdist) + size(temp_adj) +                   &
                       size(temp_adjncy)  + size(temp_weight)
          write(*,*)"    ... Temporary memory usage ",total_size*4," Bytes"
       end if

       do i = is+1,ie

          ! sizes
          ! write(*,*)"RECV ",lmpi_id,i,myid ! ,ioff,joff,koff
          call lmpi_recv(sizes,3,i,i+10000,ierr)

          ! weight
          isize = sizes(1)
          allocate(temp2(isize)); temp2 = 0
          call lmpi_recv(temp2,isize,i,i+20000,ierr)
          do j = 1,isize
             ioff = ioff + 1
             temp_weight(ioff) = temp2(j)
          end do
          deallocate(temp2)

          ! temp_adj
          jsize = sizes(2)
          allocate(temp2(jsize)); temp2 = 0
          call lmpi_recv(temp2,jsize,i,i+30000,ierr)

          do j = 2,jsize ! note, skip 1st value
             joff = joff + 1
             temp_adj(joff) = temp_adj(joff-1)+ &
                              (temp2(j)-temp2(j-1))
          end do
          deallocate(temp2)

          ! temp_adjncy
          ksize = sizes(3)
          allocate(temp2(ksize)); temp2 = 0
          call lmpi_recv(temp2,ksize,i,i+40000,ierr)
          do j = 1,ksize
             koff = koff + 1
             temp_adjncy(koff) = temp2(j)
          end do

          ! temp_edge_weight
          if (twod) then
             call lmpi_recv(temp2,ksize,i,i+50000,ierr)
             do j = 1,ksize
                loff = loff + 1
                temp_edge_weight(loff) = temp2(j)
             end do
          end if
          deallocate(temp2)

      end do ! is+1,ie

    else
       ! write(*,*)"SEND ",lmpi_id,is
       sizes(1) = size(weight)
       sizes(2) = size(adj)
       sizes(3) = size(adjncy)
       call lmpi_send(sizes, 3,       is,lmpi_id+10000,ierr)
       call lmpi_send(weight,sizes(1),is,lmpi_id+20000,ierr)
       ! write(*,*)"SEND ADJ(1:4) ",lmpi_id,adj(1:4)
       call lmpi_send(adj,   sizes(2),is,lmpi_id+30000,ierr)
       call lmpi_send(adjncy,sizes(3),is,lmpi_id+40000,ierr)
       if (twod) call lmpi_send(adjncy,sizes(3),is,lmpi_id+50000,ierr)
    end if
    deallocate(temp)

  end subroutine PM_min_gather

!=============================== pparty_write_metis_stats_open ===============80
!
!=============================================================================80

  subroutine pparty_write_metis_stats_open(iunit,grid)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_nproc, lmpi_master, lmpi_gather

    integer,          intent(in) :: iunit
    type (grid_type), intent(in) :: grid

    character(len=80) :: filename

  continue

    if (lmpi_master) then
       filename = trim(grid%project) // '.load_balance_stats'
       open(iunit,file=filename,status='unknown',form='formatted')
       if (.not.stats_opened) then
          rewind(iunit)
          stats_opened = .true.
       end if
    endif

  end subroutine pparty_write_metis_stats_open

!=============================== pparty_write_metis_stats ====================80
!
!=============================================================================80

  subroutine pparty_write_metis_stats(text,iunit,nproc,size_metis_data,        &
                                      metis_data,ncon,size_weight,weight,      &
                                      wvalue,new_comm1)
    use kinddefs,   only : dp

  include 'mpif.h'

    character(len=*), intent(in) :: text
    integer,          intent(in) :: iunit,nproc
    integer,          intent(in) :: size_metis_data,ncon,size_weight
    integer, dimension(size_metis_data), intent(in) :: metis_data
    integer, dimension(size_weight),     intent(in) :: weight
    real(dp),          intent(out):: wvalue
    integer, optional, intent(in) :: new_comm1

    real(dp) :: ri,rj
    integer :: i,j,k,ioff,comm,ierr
    integer, dimension(:),   allocatable :: temp,temp1
    integer, dimension(:,:), allocatable :: iwgt

  continue

    comm = lmpi_comm_world
    if (present(new_comm1)) comm = new_comm1

    if (lmpi_master) then
       write(*,*)"Writing stats ",nproc,size_metis_data,size_weight,ncon
       write(iunit,*) trim(text)
    end if
    !write(*,*)"WRITING STATS ",lmpi_id,nproc,size_metis_data,size_weight

    allocate(iwgt(nproc,ncon)); iwgt = 0
    ioff = 1
    do i = 1,size_metis_data
      !write(6000+lmpi_id,*) metis_data(i),weight(ioff:ioff+(ncon-1))
       k = metis_data(i)
       do j = 1,ncon
          iwgt(k,j) = iwgt(k,j) + weight(ioff)
          ioff = ioff + 1
       end do
    end do
    allocate(temp (nproc)); temp  = 0
    allocate(temp1(nproc)); temp1 = 0
    wvalue = 0.0_dp
    do i = 1,ncon
       temp = iwgt(:,i)
       call mpi_reduce(temp,temp1,size(temp,1),mpi_integer,mpi_sum,0,comm,ierr)
       !call lmpi_reduce(temp,temp1)
       if (lmpi_master) then
          ri = minval(temp1)*1._dp
          rj = maxval(temp1)*1._dp
          write(iunit,'(a40,3(F15.0,1x),F8.2)')                                &
            'PM weight: min, max, delta :',ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
          wvalue = wvalue + ((rj-ri)/ri)*100.0_dp
       end if
    end do
    wvalue = wvalue/(ncon*1.0_dp)
    deallocate(temp1,temp,iwgt)
    if (lmpi_master) write(iunit,*)

  end subroutine pparty_write_metis_stats

#endif

#ifdef HAVE_PARMETIS
!=============================== pparty_write_data_stats =====================80
!
!=============================================================================80

  subroutine pparty_write_data_stats(grid)

    use grid_types, only : grid_type

    use kinddefs,    only : system_i1
    use sort,        only : heap_sort, binary_search, small_sort

    type (grid_type), intent(in) :: grid
    integer :: i,j,k,ii,m,iedge,node,node1,node2,ipe,nnodes1,pmax,jsize
    integer :: nobjs,n_todo,iobj,nscan

    character (len=1024) :: filename

    integer, dimension(:), allocatable :: adj_count, ia,ja, noff, toscan
    integer, dimension(:), allocatable :: which_pe,pdata,my_pdata,itag,q2,q3,q4
    integer, dimension(:,:), allocatable :: q0, q1
    integer(system_i1), dimension(:), allocatable :: todo

    integer, parameter :: iunit = 70

  continue

! adj list

    allocate(adj_count(grid%nnodes0+1)); adj_count = 0
    do iedge = 1,grid%nedgeloc
       node1 = grid%eptr(1,iedge)
       node2 = grid%eptr(2,iedge)
       if (node1 <= grid%nnodes0) adj_count(node1) = adj_count(node1) + 1
       if (node2 <= grid%nnodes0) adj_count(node2) = adj_count(node2) + 1
    end do
    allocate(ia(grid%nnodes0+1)); ia = 0
    ia(1) = 1
    do i = 1,grid%nnodes0
       ia(i+1) = ia(i) + adj_count(i)
    end do
    allocate(ja(ia(grid%nnodes0+1))); ja = 0

    adj_count = 0
    do iedge = 1,grid%nedgeloc
       node1 = grid%eptr(1,iedge)
       node2 = grid%eptr(2,iedge)
       if (node1 <= grid%nnodes0) then
          ja(ia(node1)+adj_count(node1)) = node2
          adj_count(node1) = adj_count(node1) + 1
       end if
       if (node2 <= grid%nnodes0) then
          ja(ia(node2)+adj_count(node2)) = node1
          adj_count(node2) = adj_count(node2) + 1
       end if
    end do ! iedge

! Determine PE for level1 nodes

    nnodes1 = grid%nnodes01 - grid%nnodes0
    allocate(which_pe(grid%nnodes0+1:grid%nnodes01))
    which_pe = lmpi_nproc

    allocate(my_pdata(grid%nnodes0))
    my_pdata(1:grid%nnodes0) = grid%l2g(1:grid%nnodes0)
    call heap_sort(grid%nnodes0,my_pdata)
    call lmpi_max(grid%nnodes0,pmax)
    call lmpi_bcast(pmax)
    allocate(pdata(pmax))
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) jsize = grid%nnodes0
       call lmpi_bcast(jsize,ipe)
       pdata = 0
       if (lmpi_id == ipe) pdata(1:jsize) = my_pdata(1:jsize)
       call lmpi_bcast(pdata,ipe)
       do i = grid%nnodes0+1,grid%nnodes01
          if (which_pe(i) == lmpi_nproc) then ! *** Could save time here
             j = binary_search(jsize,pdata,grid%l2g(i))
             if (j > 0) which_pe(i) = ipe
          end if
       end do
    end do
    deallocate(pdata)
    deallocate(my_pdata)

! Count objects and unique off PE

    nobjs = 0
    allocate(itag(grid%nnodes01)); itag = 0
    allocate(todo(grid%nnodes0)); todo = 0
    j = grid%nnodes01 - grid%nnodes0
    allocate(toscan(j)); toscan = 0

!   do i = 1,grid%nnodes0
!      j = ia(i+1)-ia(i)
!      allocate(temp(j))
!      temp = grid%l2g(ja(ia(i):ia(i+1)-1))
!      call small_sort(j,temp)
!      write(19000+lmpi_id,'(i0," : ",i0," :: ",100(i0,1x))') &
!        grid%l2g(i),j,temp(1:j)
!      deallocate(temp)
!      if (adj_count(i) /= j) write(*,*)"ERROR3 ",lmpi_id,i,adj_count(i),j
!   end do

    do i = 1,grid%nnodes0
       if (itag(i) == 0) then
          nobjs = nobjs + 1
          itag(i) = nobjs
          n_todo = 0
          do j = ia(i),ia(i+1)-1
             node = ja(j)
             if(itag(node)/=0)write(23000+lmpi_id,*)" ERROR1a",i,node,itag(node)
             itag(node) = nobjs
            !write(23000+lmpi_id,*)"TAG ",node,nobjs
             if (node <= grid%nnodes0) then
                todo(node) = 1
                n_todo = n_todo + 1
               !write(23000+lmpi_id,*)"TODO ",node
             end if
          end do
          nscan = 0
          do
             if (n_todo == 0) exit
             do j = 1,grid%nnodes0
                if (todo(j) == 1) then
                   todo(j) = 0
                   n_todo = n_todo - 1
                   do k = ia(j),ia(j+1)-1
                      node = ja(k)
                      if ((itag(node) /= 0).and.(itag(node) /= nobjs))         &
                         write(23000+lmpi_id,*)"ERROR2a",i,j,node,itag(node)
                      if (itag(node) == 0) then
                         itag(node) = nobjs
                        !write(23000+lmpi_id,*)"TAG ",node,nobjs
                         if (node <= grid%nnodes0) then
                             todo(node) = 1
                             n_todo = n_todo + 1
                            !write(23000+lmpi_id,*)"TODO ",node
                         else
                           nscan = nscan + 1
                           toscan(nscan) = node
                         end if
                      end if
                   end do
                end if ! todo
             end do ! nnodes0
!            Now check for if any tagged level1 nodes are in level0 adj lists.
             if (nscan > 0) then
                out1 : do j = 1,grid%nnodes0
                   if (itag(j) == 0) then
                      do k = ia(j),ia(j+1)-1
                         node = ja(k)
                         if (node > grid%nnodes0) then
                            do m = 1,nscan
                               if (toscan(m) == node) then
                                  n_todo = n_todo + 1
                                  todo(j) = 1
                                  itag(j) = nobjs
                                  cycle out1
                               end if
                            end do
                         end if
                      end do
                   end if
                end do out1
             end if ! nscan > 0
          end do ! outer, exit by n_todo
       end if
    end do
    deallocate(ia,ja)
    deallocate(adj_count)
    deallocate(todo,toscan)

! Stats

    allocate(q0(nobjs,100)); q0 = 0
    allocate(q1(nobjs,100)); q1 = 0
    allocate(q2(nobjs));     q2 = 0
    allocate(q3(nobjs));     q3 = 0
    allocate(q4(nobjs));     q4 = 0
    allocate(noff(0:lmpi_nproc-1)); noff = 0
    do iobj = 1,nobjs
       q3(iobj) = 0
       do j = 1,grid%nnodes0
          if (itag(j) == iobj) q3(iobj) = q3(iobj) + 1
       end do
       q4(iobj) = 0
       noff = 0
       do j = grid%nnodes0+1,grid%nnodes01
          if (itag(j) == iobj) then
             q4(iobj) = q4(iobj) + 1
             k = which_pe(j)
             noff(k) = noff(k) + 1
          end if
       end do
       !write(*,'("STATS: ",3(i0,1x),"::",100(i0,1x))') &
       ! lmpi_id,i,q3(iobj),q4(iobj)
       ii = 0
       do j = 0,lmpi_nproc-1
          if (noff(j) > 0) then
             ii = ii + 1
             q0(iobj,ii) = j
             q1(iobj,ii) = noff(j)
          end if
       end do
       q2(iobj) = ii

    !  if (ii > 0) then
    !     write(9000+lmpi_id,'(2(i0,1x),":",100(1x,"(",i0,1x,i0,")"))')        &
    !       i, ii,(q0(i,j),q1(i,j),j=1,ii)
    !  else
    !     write(9000+lmpi_id,*) nobjs,j
    !  end if
    end do
    deallocate(noff)
    deallocate(which_pe)
    deallocate(itag)

    filename = trim(grid%project) // '.load_balance_stats'
    do ipe = 0,lmpi_nproc-1
       call lmpi_conditional_stop(0)
       if (ipe == lmpi_id) then
       open(iunit,file=filename,status='old',form='formatted',position='append')
          if (ipe == 0) then
             write(iunit,*)
             write(iunit,*) "DISTRIBUTED OBJECTS"
          end if
          write(iunit,'("   IPE ",i0,1x," NOBJS  ",i0,"  Tot_vol ",i0)')       &
            ipe,nobjs,sum(q1)
          do iobj = 1,nobjs
             if (q2(iobj) > 0) then
              write(iunit,'(10x,a,i0,1x,a,i0,1x,":",a,i0,a,i0,1x,i0)')         &
               "Object ",iobj," Neighbors ",q2(iobj)," Vol ",sum(q1(iobj,:)),  &
               " :: Nodes (local,remote) : ",q3(iobj),q4(iobj)
              write(iunit,'(20x,"Neighbors ",i0,": ",100("(",i0,1x,i0,") "))') &
                    q2(iobj),(q0(iobj,j),q1(iobj,j),j=1,q2(iobj))
             else
                write(iunit,*) nobjs,j
             end if
          end do
          write(iunit,*)
          close(iunit)
       end if
    end do
    deallocate(q0,q1,q2,q3,q4)

  end subroutine pparty_write_data_stats
#else
!=============================== pparty_write_data_stats =====================80
!
!=============================================================================80

  subroutine pparty_write_data_stats(grid)
    use grid_types, only : grid_type
    type (grid_type), intent(in) :: grid
  continue
    write(*,*)"pparty_write_data_stats: grid%nnodesg ",grid%nnodesg
  end subroutine pparty_write_data_stats

#endif


#ifdef HAVE_PARMETIS
!=============================== pparty_check_data_stats =====================80
!
!=============================================================================80

  subroutine pparty_check_data_stats(s_l2g, s_ia, s_ja, s_metis_out,           &
                                       l2g,   ia,   ja,   metis_out, project)

    use kinddefs,    only : system_i1
    use sort,        only : heap_sort, binary_search, small_sort

    integer, intent(in) :: s_l2g, s_ia, s_ja, s_metis_out
    integer, dimension(s_l2g),       intent(in)    :: l2g
    integer, dimension(s_ia),        intent(in)    :: ia
    integer, dimension(s_ja),        intent(inout) :: ja
    integer, dimension(s_metis_out), intent(inout) :: metis_out
    character(len=*),                intent(in)    :: project

    integer :: nnodes0, nnodes1, nnodes01, jmax, inode
    integer :: itol, ct1
    integer, dimension(:), allocatable :: l2g1, nodes_to_move
    integer(system_i1), dimension(:), allocatable :: tag0,tag1

    integer :: i,j,k,m,ii,node,ipe,pmax,jsize
    integer :: nobjs,n_todo,iobj

    integer, dimension(:), allocatable :: noff
    integer, dimension(:), allocatable :: which_pe,pdata,my_pdata,itag,q2,q3,q4
    integer, dimension(:,:), allocatable :: q0, q1
    integer(system_i1), dimension(:), allocatable :: todo

    character(len=80)  :: filename
    integer, parameter :: iunit = 70

    logical, parameter :: db = .false.

  continue

! Count unique nodes in ja

    nnodes0 = s_l2g

    jmax = maxval(ja)
    allocate(tag0(jmax)); tag0 = 0
    allocate(tag1(jmax)); tag1 = 0

    do i = 1,nnodes0
       tag0(l2g(i)) = 1
       if (db) write(22000+lmpi_id,'(1x,i0,1x)') l2g(i)
    end do

    do i = 1,s_ja
       inode = ja(i)
       if (tag0(inode)==0) tag1(inode) = 1
    end do
    nnodes1 = 0
    do i = 1,jmax
       if (tag1(i) == 1) nnodes1 = nnodes1 + 1
    end do
    nnodes01 = nnodes0 + nnodes1

    allocate(l2g1(nnodes1)); l2g1 = 0
    nnodes1 = 0
    do i = 1,jmax
       if (tag1(i) == 1) then
          nnodes1 = nnodes1 + 1
          l2g1(nnodes1) = i
          if (db) write(22000+lmpi_id,'(1x,i0,1x)') i
       end if
    end do
    deallocate(tag1)

! Map ja (global) to l2g1

   do i = 1,s_ja
      inode = ja(i)
      if (tag0(inode) == 1) then
         j = binary_search(s_l2g,l2g,inode)
      else
         j = nnodes0 + binary_search(nnodes1,l2g1,inode)
      end if
      if (j == 0) stop "Never happen 1."
      ja(i) = j
   end do
   deallocate(tag0)

   if (db) then
      do i = 1,s_l2g
         write(23000+lmpi_id,'(i0," : ",i0," :: ",100(i0,1x))')  &
                     l2g(i),ia(i+1)-ia(i),ja(ia(i):ia(i+1)-1)
      end do
   end if

! Count objects and unique off PE

    nobjs = 0
    allocate(itag(nnodes01)); itag = 0
    allocate(todo(nnodes0)); todo = 0
    j = nnodes01 - nnodes0

    do i = 1,nnodes0
       if (itag(i) == 0) then
          nobjs = nobjs + 1
          itag(i) = nobjs
          n_todo = 0
          do j = ia(i),ia(i+1)-1
             node = ja(j)
                    !write(123000+lmpi_id,*)"TAG ",node,nobjs
             if (node <= nnodes0) then
                itag(node) = nobjs
                todo(node) = 1
                n_todo = n_todo + 1
                    !write(123000+lmpi_id,*)"TODO ",node
             end if
          end do
          do
             if (n_todo == 0) exit
             do j = 1,nnodes0
                if (todo(j) == 1) then
                   todo(j) = 0
                   n_todo = n_todo - 1
                   do k = ia(j),ia(j+1)-1
                      node = ja(k)
                      if (node <= nnodes0) then
                         if ((itag(node) /= 0).and.(itag(node) /= nobjs))      &
                            write(123000+lmpi_id,*)"ERROR2b",i,j,node,itag(node)
                         if (itag(node) == 0) then
                            itag(node) = nobjs
                                !write(123000+lmpi_id,*)"TAG ",node,nobjs
                             todo(node) = 1
                             n_todo = n_todo + 1
                                !write(123000+lmpi_id,*)"TODO ",node
                         end if
                      end if
                   end do
                end if ! todo
             end do ! nnodes0
          end do ! outer, exit by n_todo
       end if
    end do
    deallocate(todo)

if (db) write(*,*)"NOBJS ",lmpi_id,nobjs

! Determine PE for level1 nodes

    allocate(which_pe(nnodes1)); which_pe = lmpi_nproc

    allocate(my_pdata(nnodes0))
    my_pdata = l2g
    call lmpi_max(nnodes0,pmax)
    call lmpi_bcast(pmax)
    allocate(pdata(pmax))
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) jsize = nnodes0
       call lmpi_bcast(jsize,ipe)
       pdata = 0
       if (lmpi_id == ipe) pdata(1:jsize) = my_pdata(1:jsize)
       call lmpi_bcast(pdata,ipe)
       do i = 1,nnodes1
          if (which_pe(i) == lmpi_nproc) then
             j = binary_search(jsize,pdata,l2g1(i))
             if (j > 0) which_pe(i) = ipe
          end if
       end do
    end do
    deallocate(pdata)
    deallocate(my_pdata)
    if (db) then
    do i = 1,nnodes0
       write(24000+lmpi_id,'(i0,1x,i0)') l2g(i),lmpi_id
    end do
    do i = 1,nnodes1
       write(24000+lmpi_id,'(i0,1x,i0)') l2g1(i),which_pe(i)
    end do
    end if

! Stats

    allocate(q0(nobjs,100)); q0 = 0
    allocate(q1(nobjs,100)); q1 = 0
    allocate(q2(nobjs));     q2 = 0
    allocate(q3(nobjs));     q3 = 0
    allocate(q4(nobjs));     q4 = 0
    allocate(noff(0:lmpi_nproc-1)); noff = 0
    do iobj = 1,nobjs
       q3(iobj) = 0
       do j = 1,nnodes0
          if (itag(j) == iobj) q3(iobj) = q3(iobj) + 1
       end do
!-------------
       itag(nnodes0+1:nnodes01) = 0
       do i = 1,nnodes0
          if (itag(i) == iobj) then
             do j = ia(i),ia(i+1)-1
                if (ja(j) > nnodes0) itag(ja(j)) = iobj
             end do
          end if
       end do
!-------------
       q4(iobj) = 0
       noff = 0
       do j = nnodes0+1,nnodes01
          if (itag(j) == iobj) then
             q4(iobj) = q4(iobj) + 1
             k = which_pe(j-nnodes0)
             noff(k) = noff(k) + 1
          end if
       end do
       !write(*,'("STATS: ",3(i0,1x),"::",100(i0,1x))') &
       ! lmpi_id,i,q3(iobj),q4(iobj)
       ii = 0
       do j = 0,lmpi_nproc-1
          if (noff(j) > 0) then
             ii = ii + 1
             q0(iobj,ii) = j
             q1(iobj,ii) = noff(j)
          end if
       end do
       q2(iobj) = ii

    !  if (ii > 0) then
    !     write(9000+lmpi_id,'(2(i0,1x),":",100(1x,"(",i0,1x,i0,")"))')        &
    !       i, ii,(q0(i,j),q1(i,j),j=1,ii)
    !  else
    !     write(9000+lmpi_id,*) nobjs,j
    !  end if
    end do

    filename = trim(project) // '.load_balance_stats'
    do ipe = 0,lmpi_nproc-1
       call lmpi_conditional_stop(0)
       if (ipe == lmpi_id) then
       open(iunit,file=filename,status='unknown',form='formatted',             &
            position='append')
          if (ipe == 0) then
             write(iunit,*)
             write(iunit,*) "DISTRIBUTED OBJECTS"
          end if
          write(iunit,'("   IPE ",i0,1x," NOBJS  ",i0,"  Tot_vol ",i0)')       &
            ipe,nobjs,sum(q1)
          write(iunit,'("   IPE ",i0,1x," NN0,NN1 ",i0," ",i0)')               &
            ipe,nnodes0,nnodes1
          do iobj = 1,nobjs
             if (q2(iobj) > 0) then
              write(iunit,'(10x,a,i0,1x,a,i0,1x,":",a,i0,a,i0,1x,i0)')         &
               "Object ",iobj," Neighbors ",q2(iobj)," Vol ",sum(q1(iobj,:)),  &
               " :: Nodes (local,remote) : ",q3(iobj),q4(iobj)
              write(iunit,'(20x,"Neighbors ",i0,": ",100("(",i0,1x,i0,") "))') &
                    q2(iobj),(q0(iobj,j),q1(iobj,j),j=1,q2(iobj))
             else
                write(iunit,*) nobjs,j
             end if
          end do
          write(iunit,*)
          close(iunit)
       end if
    end do

! Migrate local nodes in objects with 1 neighbor and 0.1% local nodes

    itol = max(100,nint(nnodes0 * migration_tol))
    if (lmpi_master.and.db) write(*,*)"TOL ",itol
    if (db) write(50000+lmpi_id,*)'itol ',itol
    allocate(nodes_to_move(itol)); nodes_to_move = 0
    do iobj = 1,nobjs
       if (db) write(50000+lmpi_id,*)'nei,local ',q2(iobj),q3(iobj)
       if (q3(iobj) <= itol) then
          nodes_to_move = 0
          ct1 = 0
          if (q2(iobj) == 1) then
             if(db) write(50000+lmpi_id,*)'----------- iobj :: ',q3(iobj)
             do i = 1,nnodes0
                if (itag(i) == iobj) then
                   ct1 = ct1 + 1
                   nodes_to_move(ct1) = i
                   metis_out(i) = q0(iobj,1)+1 ! +1 as metis_data is 1..n
                end if
             end do
             if(db)write(50000+lmpi_id,*)"N move",iobj,"::",nodes_to_move(1:ct1)
          else if (q2(iobj) <= migration_loop) then
             k = q1(iobj,1) ! size
             m = q0(iobj,1) ! which
             do j = 2,q2(iobj)
                if (q1(iobj,j) > k) then
                    k = j
                    m = q0(iobj,j)
                end if
             end do
             do i = 1,nnodes0
                if (itag(i) == iobj) then
                   ct1 = ct1 + 1
                   nodes_to_move(ct1) = i
                   metis_out(i) = m+1
                end if
             end do
          end if
       end if
    end do

    deallocate(nodes_to_move)
    deallocate(noff)
    deallocate(which_pe)
    deallocate(itag)
    deallocate(q0,q1,q2,q3,q4)

  end subroutine pparty_check_data_stats
#else
!=============================== pparty_check_data_stats =====================80
!
!=============================================================================80

  subroutine pparty_check_data_stats(s_l2g, s_ia, s_ja, s_metis_out,           &
                                       l2g,   ia,   ja,   metis_out, project)

    integer, intent(in) :: s_l2g, s_ia, s_ja, s_metis_out
    integer, dimension(s_l2g),       intent(in)    :: l2g
    integer, dimension(s_ia),        intent(in)    :: ia
    integer, dimension(s_ja),        intent(inout) :: ja
    integer, dimension(s_metis_out), intent(inout) :: metis_out
    character(len=*),                intent(in)    :: project

  continue
    write(*,*)"pparty_check_data_stats: ",                                     &
      project,size(l2g),size(ia),size(ja),size(metis_out),                     &
      migration_tol,migration_loop
  end subroutine pparty_check_data_stats

#endif

end module pparty_metis

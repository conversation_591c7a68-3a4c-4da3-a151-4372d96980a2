!
! DO NOT EDIT this file.  It was generated by the ruby script,
!
! Instead,
! 1. Edit line_solver_ddq.template
! 2. Regenerate this file through the command below:
!               ruby LinAlg.rb line_solver_ddq
! 3. Commit line_solver_ddq.f90 and line_solver_ddq.template
!
module line_solver_ddq

  use kinddefs,        only : dp, jp, odp, dqp
  use lmpi,            only : lmpi_conditional_stop, lmpi_id
  use linear_systems,  only : monitor_eqn_group_relax
  use linear_spectral, only : monitor_rms_sr,                                  &
                              check_preconditioner, preconditioner_exit,       &
                              set_preconditioner_target
  use fun3d_maximums,  only : max_dof_in_line
  use interp_defs,     only : sendrecv_type
  use cell_reynolds,   only : omega_cell_re
  use cfl_defs,      only : hanim

  implicit none

  private

  public :: line_solve_ddq, line_solve_target

  logical :: allow_relaxation_factor, allow_exit

  integer :: actual_sweeps = 0

contains

!================================= LINE_SOLVE_TARGET =========================80
!
! Accumulate targets for line solve routines.
!
!=============================================================================80

  subroutine line_solve_target( nb, res_matvec, n_line_neq0, line_neq0, g2m )

    integer, intent(in) :: nb, n_line_neq0

    integer, dimension(n_line_neq0), intent(in) :: line_neq0
    integer, dimension(:),           intent(in) :: g2m

    real(jp), dimension(:,:), intent(in) :: res_matvec

    integer :: n, m, j, equation

    real(dqp), dimension(nb)   :: target

  continue

    target(:) = 0.0_dqp

    do n = 1,n_line_neq0

        equation = line_neq0(n)
        m        = g2m(equation)

        do j = 1, nb
          target(j) = target(j) + res_matvec(j,m)**2
        enddo

    end do

    call set_preconditioner_target( n_line_neq0, nb, target, 0 )

  end subroutine line_solve_target

!================================ LINE_SOLVE_DDQ =============================80
!
! Routes the code into the appropriate line solve routine
!
!=============================================================================80
  subroutine line_solve_ddq( solve_backwards, nb, dq_dim, nr, nm, neqmax,      &
                         neq0, nia, nja, ia, ja,                               &
                         n_lines, n_line_neq0, first_neq0, line_neq0,          &
                         res, dq, a, b, c,                                     &
                         a_diag, a_off,                                        &
                         sweeps_to_do, sweeps_actual, omega, cell_re,          &
                         colored_sweeps,color_indices,max_colored_sweeps,      &
                         lines, lu_offset,                                     &
                         sr, g2m, nzg2m, force_general_path )

    integer, intent(in) :: nb
    integer, intent(in) :: dq_dim
    integer, intent(in) :: nr, nm
    integer, intent(in) :: neq0, nia, nja, neqmax
    integer, intent(in) :: n_lines
    integer, intent(in) :: n_line_neq0
    integer, intent(in) :: sweeps_to_do
    integer, intent(in) :: colored_sweeps, max_colored_sweeps
    integer, intent(inout) :: sweeps_actual

    integer, intent(in), optional :: force_general_path

    integer, dimension(n_lines+1),   intent(in) :: first_neq0
    integer, dimension(n_line_neq0), intent(in) :: line_neq0
    integer, dimension(nia),         intent(in) :: ia
    integer, dimension(nja),         intent(in) :: ja
    integer, dimension(:),           intent(in) :: g2m,nzg2m
    integer, dimension(n_lines),     intent(in) :: lines, lu_offset

    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: ierr

  continue

    allow_exit = .true.
    if ( sweeps_actual < 0 ) allow_exit = .false.

    allow_relaxation_factor = .false.
    if ( solve_backwards < 0 ) then
      allow_relaxation_factor = .false.
    elseif ( abs(omega-1._dp) > 0.001_dp ) then
      allow_relaxation_factor = .true.
    endif

    if(.not. present(force_general_path)) then

      select case (nb)
      case(6)
        call line_solve_6(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      case(5)
        call line_solve_5(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      case(4)
        call line_solve_4(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      case(3)
        call line_solve_3(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      case(2)
        call line_solve_2(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      case(1)
        call line_solve_1(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      case default

        call line_solve_n(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)

      end select

    else

      if(force_general_path == 1) then

!       General block line solve

        call line_solve_n(     solve_backwards,                                &
                        dq_dim, nr, nm,neq0, nia, nja, ia, ja,                 &
                        n_lines,n_line_neq0, first_neq0, line_neq0,            &
                        sweeps_to_do, omega, cell_re,                          &
                        res, dq, a, b, c,                                      &
                        a_diag,                      a_off,                    &
                        neqmax,nb,lines, lu_offset,                            &
                        colored_sweeps,color_indices,max_colored_sweeps,sr,g2m,&
                        nzg2m)
      endif

    endif

    sweeps_actual = actual_sweeps

    ierr = 0
    if ( sweeps_actual <= 0 ) then
      ierr = 1
      write(*,*) ' lmpi_id,sweeps=',lmpi_id,sweeps_actual,sweeps_to_do
    endif
    call lmpi_conditional_stop(ierr,'Sweeps<0:line_solve_ddq')

  end subroutine line_solve_ddq


!================================ LINE_SOLVE_N ===============================80
!
! Performs direct line solve on block tridiagonal nxn
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D.
!
!=============================================================================80
  subroutine line_solve_n(      solve_backwards,                               &
                           dq_dim, nr, nm, neq0, nia, nja, ia, ja,             &
                           n_lines,n_line_neq0, first_neq0, line_neq0,         &
                           n_sweeps, omega, cell_re,                           &
                           res, dq, a, b, c,                                   &
                           a_diag,            a_off,                           &
                           neqmax,nb,lines, lu_offset,                         &
                           colored_sweeps,color_indices,max_colored_sweeps,sr, &
                           g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: i,j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col
    integer :: il,iu,m,l,k,kstart,kend,kcol,offset
    integer :: sweep,nb1,l1,l0,m1,m0,mm,mm1,mequation,ioff
    integer :: loop_colored_sweeps, sweep_start, sweep_end, sweep_stride
    integer :: color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab
    real(jp) :: apv

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    nb1 = nb - 1

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0  ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            f(1:nb,local_eqn) = change_sign * res(1:nb,mequation)

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, nb
                do col = 1, nb
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 6
              do col = 1, 6
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          if( iu>il )then
            do m = il,iu-1
              m1 = m + 1
              l = m + offset
              l1 = l + 1
              do mm = 1,nb1
                mm1 = mm + 1
                apv = 1._jp / b(mm,mm,l)
                do i = mm1,nb
                  f(i,m) = f(i,m) - f(mm,m)*b(i,mm,l)*apv
                end do
                do i = 1,nb
                  f(i,m1) = f(i,m1) - f(mm,m)*a(i,mm,l1)*apv
                end do
              end do
              apv = 1._jp / b(nb,nb,l)
              mm = nb
              do i = 1,nb
                f(i,m1) = f(i,m1) - f(mm,m)*a(i,mm,l1)*apv
              end do
            end do
          end if

          m  = iu
          m0 = m - 1
          l  = iu + offset
          l0 = l - 1

          do mm = 1,nb1
            mm1 = mm + 1
            apv = 1._jp / b(mm,mm,l)
            do i = mm1,nb
              f(i,m) = f(i,m) - f(mm,m)*b(i,mm,l)*apv
            end do
          end do

          f(nb,m) = f(nb,m) / b(nb,nb,l)

          if( iu>il ) then
            do mm = nb1,1,-1
              mm1 = mm + 1
              do i = mm,1,-1
                f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
              end do
              do i = nb,1,-1
                f(i,m0) = f(i,m0) - c(i,mm1,l0)*f(mm1,m)
              end do
              f(mm,m) = f(mm,m) / b(mm,mm,l)
            end do
            do m = iu-1,il+1,-1
              m0 = m - 1
              m1 = m + 1
              l  = m + offset
              l0 = l - 1
              l1 = l + 1
              mm = nb
              do i = nb,1,-1
                f(i,m) = f(i,m) - c(i,1,l)*f(1,m1)
              end do
              f(mm,m) = f(mm,m) / b(mm,mm,l)
              do mm = nb1,1,-1
                mm1 = mm + 1
                do i = mm,1,-1
                  f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
                end do
                do i = nb,1,-1
                  f(i,m0) = f(i,m0) - c(i,mm1,l0)*f(mm1,m)
                end do
                f(mm,m) = f(mm,m) / b(mm,mm,l)
              end do
            end do
            m  = il
            l  = m + offset
            m1 = m + 1
            l1 = l + 1
            mm = nb
            do i = nb,1,-1
              f(i,m) = f(i,m) - c(i,1,l)*f(1,m1)
            end do
          else
            mm = nb
            l  = il + offset
            m  = 1
          end if

          f(mm,m) = f(mm,m) / b(mm,mm,l)

          do mm = nb1,1,-1
            mm1 = mm + 1
            do i = mm,1,-1
              f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
            end do
            f(mm,m) = f(mm,m) / b(mm,mm,l)
          end do

! Now just put the local solution back into the global solution array

          local_eqn = 0

          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = omega_cell_re( omega, cell_re(equation) )
            if ( monitor_eqn_group_relax )                                   &
            sum_ddq(1:nb)      = sum_ddq(1:nb) + (omegab*f(1:nb,local_eqn))**2
            dq(1:nb,mequation) =      dq(1:nb,mequation) &
                               + omegab*f(1:nb,local_eqn)
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line:block-n')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_n

! no comment
!================================ LINE_SOLVE_1 ===============================80
!
! Performs direct line solve on block tridiagonal 1x1
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_1(     solve_backwards, dq_dim,nr,nm,                  &
                          neq0, nia, nja, ia, ja,                              &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          n_sweeps, omega, cell_re,                            &
                          res, dq, a, b, c,                                    &
                          a_diag,                      a_off,                  &
                          neqmax,nb,lines, lu_offset,                          &
                          colored_sweeps,color_indices,max_colored_sweeps,sr,  &
                          g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col,il,iu,il1,iqq
    integer :: ir,it,is,k,kstart,kend,kcol,offset,mequation
    integer :: sweep,loop_colored_sweeps,sweep_start,sweep_end,ioff
    integer :: sweep_stride, color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0   ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            do row = 1, 1
              f(row,local_eqn) = change_sign * res(row,mequation)
            end do

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_aoff_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, 1
                do col = 1, 1
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_aoff_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 1
              do col = 1, 1
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          il1 = il + 1
          is  = il

! f = binv*f

          f(1,is) = b(1,1,offset+is)*(f(1,is))

! Forward sweep

          forward : do is = il1, iu
            ir = is - 1
            it = is + 1

! First row reduction

            do row = 1, 1
              f(row,is) = f(row,is)                                            &
                              - a(row,1,offset+is)*f(1,ir)
            end do

! f = binv*f

            f(1,is) = b(1,1,offset+is)*(f(1,is))

          end do forward

! Back substitution

          backward : do iqq = il1, iu
            is = il + iu - iqq
            it = is + 1
            do row = 1, 1
              f(row,is) =  f(row,is)                                           &
                                - c(row,1,offset+is)*f(1,it)
            end do
          end do backward

! Now just put the local solution back into the global solution array

          local_eqn = 0


          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = 1._dp
            if ( allow_relaxation_factor  ) then
              omegab = omega_cell_re( omega, cell_re(equation) )
            endif
            if ( monitor_eqn_group_relax ) then
              do row = 1, 1
                sum_ddq(row) = sum_ddq(row) + (omegab*f(row,local_eqn))**2
              end do
            endif
            do row = 1, 1
              dq(row,mequation) =       dq(row,mequation) &
                                + omegab*f(row,local_eqn)
            end do
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_1

! no comment
!================================ LINE_SOLVE_2 ===============================80
!
! Performs direct line solve on block tridiagonal 2x2
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_2(     solve_backwards, dq_dim,nr,nm,                  &
                          neq0, nia, nja, ia, ja,                              &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          n_sweeps, omega, cell_re,                            &
                          res, dq, a, b, c,                                    &
                          a_diag,                      a_off,                  &
                          neqmax,nb,lines, lu_offset,                          &
                          colored_sweeps,color_indices,max_colored_sweeps,sr,  &
                          g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col,il,iu,il1,iqq
    integer :: ir,it,is,k,kstart,kend,kcol,offset,mequation
    integer :: sweep,loop_colored_sweeps,sweep_start,sweep_end,ioff
    integer :: sweep_stride, color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0   ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            do row = 1, 2
              f(row,local_eqn) = change_sign * res(row,mequation)
            end do

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_aoff_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, 2
                do col = 1, 2
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_aoff_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 2
              do col = 1, 2
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          il1 = il + 1
          is  = il

! f = binv*f

          f(1,is) = b(1,1,offset+is)*(f(1,is))
          f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
          f(1,is) = f(1,is)                                                    &
                            - b(1,2,offset+is)*f(2,is)

! Forward sweep

          forward : do is = il1, iu
            ir = is - 1
            it = is + 1

! First row reduction

            do row = 1, 2
              f(row,is) = f(row,is)                                            &
                              - a(row,1,offset+is)*f(1,ir)                     &
                              - a(row,2,offset+is)*f(2,ir)
            end do

! f = binv*f

            f(1,is) = b(1,1,offset+is)*(f(1,is))
            f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
            f(1,is) = f(1,is)                                                  &
                              - b(1,2,offset+is)*f(2,is)

          end do forward

! Back substitution

          backward : do iqq = il1, iu
            is = il + iu - iqq
            it = is + 1
            do row = 1, 2
              f(row,is) =  f(row,is)                                           &
                                - c(row,1,offset+is)*f(1,it)                   &
                                - c(row,2,offset+is)*f(2,it)
            end do
          end do backward

! Now just put the local solution back into the global solution array

          local_eqn = 0


          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = 1._dp
            if ( allow_relaxation_factor  ) then
              omegab = omega_cell_re( omega, cell_re(equation) )
            endif
            if ( monitor_eqn_group_relax ) then
              do row = 1, 2
                sum_ddq(row) = sum_ddq(row) + (omegab*f(row,local_eqn))**2
              end do
            endif
            do row = 1, 2
              dq(row,mequation) =       dq(row,mequation) &
                                + omegab*f(row,local_eqn)
            end do
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_2

! no comment
!================================ LINE_SOLVE_3 ===============================80
!
! Performs direct line solve on block tridiagonal 3x3
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_3(     solve_backwards, dq_dim,nr,nm,                  &
                          neq0, nia, nja, ia, ja,                              &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          n_sweeps, omega, cell_re,                            &
                          res, dq, a, b, c,                                    &
                          a_diag,                      a_off,                  &
                          neqmax,nb,lines, lu_offset,                          &
                          colored_sweeps,color_indices,max_colored_sweeps,sr,  &
                          g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col,il,iu,il1,iqq
    integer :: ir,it,is,k,kstart,kend,kcol,offset,mequation
    integer :: sweep,loop_colored_sweeps,sweep_start,sweep_end,ioff
    integer :: sweep_stride, color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0   ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            do row = 1, 3
              f(row,local_eqn) = change_sign * res(row,mequation)
            end do

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_aoff_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, 3
                do col = 1, 3
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_aoff_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 3
              do col = 1, 3
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          il1 = il + 1
          is  = il

! f = binv*f

          f(1,is) = b(1,1,offset+is)*(f(1,is))
          f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
          f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                              -b(3,2,offset+is)*f(2,is))
          f(2,is) = f(2,is)                                                    &
                            - b(2,3,offset+is)*f(3,is)
          f(1,is) = f(1,is)                                                    &
                            - b(1,3,offset+is)*f(3,is)                         &
                            - b(1,2,offset+is)*f(2,is)

! Forward sweep

          forward : do is = il1, iu
            ir = is - 1
            it = is + 1

! First row reduction

            do row = 1, 3
              f(row,is) = f(row,is)                                            &
                              - a(row,1,offset+is)*f(1,ir)                     &
                              - a(row,2,offset+is)*f(2,ir)                     &
                              - a(row,3,offset+is)*f(3,ir)
            end do

! f = binv*f

            f(1,is) = b(1,1,offset+is)*(f(1,is))
            f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
            f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                                -b(3,2,offset+is)*f(2,is))
            f(2,is) = f(2,is)                                                  &
                              - b(2,3,offset+is)*f(3,is)
            f(1,is) = f(1,is)                                                  &
                              - b(1,3,offset+is)*f(3,is)                       &
                              - b(1,2,offset+is)*f(2,is)

          end do forward

! Back substitution

          backward : do iqq = il1, iu
            is = il + iu - iqq
            it = is + 1
            do row = 1, 3
              f(row,is) =  f(row,is)                                           &
                                - c(row,1,offset+is)*f(1,it)                   &
                                - c(row,2,offset+is)*f(2,it)                   &
                                - c(row,3,offset+is)*f(3,it)
            end do
          end do backward

! Now just put the local solution back into the global solution array

          local_eqn = 0


          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = 1._dp
            if ( allow_relaxation_factor  ) then
              omegab = omega_cell_re( omega, cell_re(equation) )
            endif
            if ( monitor_eqn_group_relax ) then
              do row = 1, 3
                sum_ddq(row) = sum_ddq(row) + (omegab*f(row,local_eqn))**2
              end do
            endif
            do row = 1, 3
              dq(row,mequation) =       dq(row,mequation) &
                                + omegab*f(row,local_eqn)
            end do
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_3

! no comment
!================================ LINE_SOLVE_4 ===============================80
!
! Performs direct line solve on block tridiagonal 4x4
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_4(     solve_backwards, dq_dim,nr,nm,                  &
                          neq0, nia, nja, ia, ja,                              &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          n_sweeps, omega, cell_re,                            &
                          res, dq, a, b, c,                                    &
                          a_diag,                      a_off,                  &
                          neqmax,nb,lines, lu_offset,                          &
                          colored_sweeps,color_indices,max_colored_sweeps,sr,  &
                          g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col,il,iu,il1,iqq
    integer :: ir,it,is,k,kstart,kend,kcol,offset,mequation
    integer :: sweep,loop_colored_sweeps,sweep_start,sweep_end,ioff
    integer :: sweep_stride, color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0   ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            do row = 1, 4
              f(row,local_eqn) = change_sign * res(row,mequation)
            end do

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_aoff_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, 4
                do col = 1, 4
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_aoff_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 4
              do col = 1, 4
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          il1 = il + 1
          is  = il

! f = binv*f

          f(1,is) = b(1,1,offset+is)*(f(1,is))
          f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
          f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                              -b(3,2,offset+is)*f(2,is))
          f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                              -b(4,2,offset+is)*f(2,is)        &
                                              -b(4,3,offset+is)*f(3,is))
          f(3,is) = f(3,is)                                                    &
                            - b(3,4,offset+is)*f(4,is)
          f(2,is) = f(2,is)                                                    &
                            - b(2,4,offset+is)*f(4,is)                         &
                            - b(2,3,offset+is)*f(3,is)
          f(1,is) = f(1,is)                                                    &
                            - b(1,4,offset+is)*f(4,is)                         &
                            - b(1,3,offset+is)*f(3,is)                         &
                            - b(1,2,offset+is)*f(2,is)

! Forward sweep

          forward : do is = il1, iu
            ir = is - 1
            it = is + 1

! First row reduction

            do row = 1, 4
              f(row,is) = f(row,is)                                            &
                              - a(row,1,offset+is)*f(1,ir)                     &
                              - a(row,2,offset+is)*f(2,ir)                     &
                              - a(row,3,offset+is)*f(3,ir)                     &
                              - a(row,4,offset+is)*f(4,ir)
            end do

! f = binv*f

            f(1,is) = b(1,1,offset+is)*(f(1,is))
            f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
            f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                                -b(3,2,offset+is)*f(2,is))
            f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                                -b(4,2,offset+is)*f(2,is)      &
                                                -b(4,3,offset+is)*f(3,is))
            f(3,is) = f(3,is)                                                  &
                              - b(3,4,offset+is)*f(4,is)
            f(2,is) = f(2,is)                                                  &
                              - b(2,4,offset+is)*f(4,is)                       &
                              - b(2,3,offset+is)*f(3,is)
            f(1,is) = f(1,is)                                                  &
                              - b(1,4,offset+is)*f(4,is)                       &
                              - b(1,3,offset+is)*f(3,is)                       &
                              - b(1,2,offset+is)*f(2,is)

          end do forward

! Back substitution

          backward : do iqq = il1, iu
            is = il + iu - iqq
            it = is + 1
            do row = 1, 4
              f(row,is) =  f(row,is)                                           &
                                - c(row,1,offset+is)*f(1,it)                   &
                                - c(row,2,offset+is)*f(2,it)                   &
                                - c(row,3,offset+is)*f(3,it)                   &
                                - c(row,4,offset+is)*f(4,it)
            end do
          end do backward

! Now just put the local solution back into the global solution array

          local_eqn = 0


          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = 1._dp
            if ( allow_relaxation_factor  ) then
              omegab = omega_cell_re( omega, cell_re(equation) )
            endif
            if ( monitor_eqn_group_relax ) then
              do row = 1, 4
                sum_ddq(row) = sum_ddq(row) + (omegab*f(row,local_eqn))**2
              end do
            endif
            do row = 1, 4
              dq(row,mequation) =       dq(row,mequation) &
                                + omegab*f(row,local_eqn)
            end do
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_4

! no comment
!================================ LINE_SOLVE_5 ===============================80
!
! Performs direct line solve on block tridiagonal 5x5
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_5(     solve_backwards, dq_dim,nr,nm,                  &
                          neq0, nia, nja, ia, ja,                              &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          n_sweeps, omega, cell_re,                            &
                          res, dq, a, b, c,                                    &
                          a_diag,                      a_off,                  &
                          neqmax,nb,lines, lu_offset,                          &
                          colored_sweeps,color_indices,max_colored_sweeps,sr,  &
                          g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col,il,iu,il1,iqq
    integer :: ir,it,is,k,kstart,kend,kcol,offset,mequation
    integer :: sweep,loop_colored_sweeps,sweep_start,sweep_end,ioff
    integer :: sweep_stride, color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0   ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            do row = 1, 5
              f(row,local_eqn) = change_sign * res(row,mequation)
            end do

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_aoff_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, 5
                do col = 1, 5
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_aoff_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 5
              do col = 1, 5
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          il1 = il + 1
          is  = il

! f = binv*f

          f(1,is) = b(1,1,offset+is)*(f(1,is))
          f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
          f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                              -b(3,2,offset+is)*f(2,is))
          f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                              -b(4,2,offset+is)*f(2,is)        &
                                              -b(4,3,offset+is)*f(3,is))
          f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)        &
                                              -b(5,2,offset+is)*f(2,is)        &
                                              -b(5,3,offset+is)*f(3,is)        &
                                              -b(5,4,offset+is)*f(4,is))
          f(4,is) = f(4,is)                                                    &
                            - b(4,5,offset+is)*f(5,is)
          f(3,is) = f(3,is)                                                    &
                            - b(3,5,offset+is)*f(5,is)                         &
                            - b(3,4,offset+is)*f(4,is)
          f(2,is) = f(2,is)                                                    &
                            - b(2,5,offset+is)*f(5,is)                         &
                            - b(2,4,offset+is)*f(4,is)                         &
                            - b(2,3,offset+is)*f(3,is)
          f(1,is) = f(1,is)                                                    &
                            - b(1,5,offset+is)*f(5,is)                         &
                            - b(1,4,offset+is)*f(4,is)                         &
                            - b(1,3,offset+is)*f(3,is)                         &
                            - b(1,2,offset+is)*f(2,is)

! Forward sweep

          forward : do is = il1, iu
            ir = is - 1
            it = is + 1

! First row reduction

            do row = 1, 5
              f(row,is) = f(row,is)                                            &
                              - a(row,1,offset+is)*f(1,ir)                     &
                              - a(row,2,offset+is)*f(2,ir)                     &
                              - a(row,3,offset+is)*f(3,ir)                     &
                              - a(row,4,offset+is)*f(4,ir)                     &
                              - a(row,5,offset+is)*f(5,ir)
            end do

! f = binv*f

            f(1,is) = b(1,1,offset+is)*(f(1,is))
            f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
            f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                                -b(3,2,offset+is)*f(2,is))
            f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                                -b(4,2,offset+is)*f(2,is)      &
                                                -b(4,3,offset+is)*f(3,is))
            f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)      &
                                                -b(5,2,offset+is)*f(2,is)      &
                                                -b(5,3,offset+is)*f(3,is)      &
                                                -b(5,4,offset+is)*f(4,is))
            f(4,is) = f(4,is)                                                  &
                              - b(4,5,offset+is)*f(5,is)
            f(3,is) = f(3,is)                                                  &
                              - b(3,5,offset+is)*f(5,is)                       &
                              - b(3,4,offset+is)*f(4,is)
            f(2,is) = f(2,is)                                                  &
                              - b(2,5,offset+is)*f(5,is)                       &
                              - b(2,4,offset+is)*f(4,is)                       &
                              - b(2,3,offset+is)*f(3,is)
            f(1,is) = f(1,is)                                                  &
                              - b(1,5,offset+is)*f(5,is)                       &
                              - b(1,4,offset+is)*f(4,is)                       &
                              - b(1,3,offset+is)*f(3,is)                       &
                              - b(1,2,offset+is)*f(2,is)

          end do forward

! Back substitution

          backward : do iqq = il1, iu
            is = il + iu - iqq
            it = is + 1
            do row = 1, 5
              f(row,is) =  f(row,is)                                           &
                                - c(row,1,offset+is)*f(1,it)                   &
                                - c(row,2,offset+is)*f(2,it)                   &
                                - c(row,3,offset+is)*f(3,it)                   &
                                - c(row,4,offset+is)*f(4,it)                   &
                                - c(row,5,offset+is)*f(5,it)
            end do
          end do backward

! Now just put the local solution back into the global solution array

          local_eqn = 0


          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = 1._dp
            if ( allow_relaxation_factor  ) then
              omegab = omega_cell_re( omega, cell_re(equation) )
            endif
            if ( monitor_eqn_group_relax ) then
              do row = 1, 5
                sum_ddq(row) = sum_ddq(row) + (omegab*f(row,local_eqn))**2
              end do
            endif
            do row = 1, 5
              dq(row,mequation) =       dq(row,mequation) &
                                + omegab*f(row,local_eqn)
            end do
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_5

! no comment
!================================ LINE_SOLVE_6 ===============================80
!
! Performs direct line solve on block tridiagonal 6x6
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_6(     solve_backwards, dq_dim,nr,nm,                  &
                          neq0, nia, nja, ia, ja,                              &
                          n_lines,n_line_neq0, first_neq0, line_neq0,          &
                          n_sweeps, omega, cell_re,                            &
                          res, dq, a, b, c,                                    &
                          a_diag,                      a_off,                  &
                          neqmax,nb,lines, lu_offset,                          &
                          colored_sweeps,color_indices,max_colored_sweeps,sr,  &
                          g2m,nzg2m)

    use lmpi_app,        only : lmpi_xfer

    integer, intent(in) :: dq_dim, nr, nm, neq0, nia, nja
    integer, intent(in) :: n_lines, n_line_neq0, n_sweeps, neqmax
    integer, intent(in) :: nb, colored_sweeps, max_colored_sweeps

    integer, dimension(n_lines+1),        intent(in) :: first_neq0
    integer, dimension(n_lines),          intent(in) :: lines, lu_offset
    integer, dimension(n_line_neq0),      intent(in) :: line_neq0
    integer, dimension(nia),              intent(in) :: ia
    integer, dimension(nja),              intent(in) :: ja
    integer, dimension(:),                intent(in) :: g2m,nzg2m
    integer, dimension(2,colored_sweeps), intent(in) :: color_indices

    real(dp), intent(in) :: omega

    real(dp), dimension(:),                 intent(in)    :: cell_re
    real(dp), dimension(nr,neq0),           intent(in)    :: res
    real(dqp), dimension(dq_dim,neqmax),        intent(inout) :: dq
    real(jp), dimension(nb,nb,n_line_neq0), intent(inout) :: a, b, c
    real(dp), dimension(nm,nm,neq0),        intent(in)    :: a_diag
    real(odp), dimension(nm,nm,nja),        intent(in)    :: a_off

    integer, intent(in) :: solve_backwards

    type(sendrecv_type), dimension(:), pointer :: sr

    integer :: j,line,equation, bottom_location
    integer :: top_location,local_eqn,row,col,il,iu,il1,iqq
    integer :: ir,it,is,k,kstart,kend,kcol,offset,mequation
    integer :: sweep,loop_colored_sweeps,sweep_start,sweep_end,ioff
    integer :: sweep_stride, color, start, end, stride, n, csta, cend, cstr

    real(jp) :: change_sign
    real(dp) :: omegab

    real(jp), dimension(nb,max_dof_in_line) :: f

    real(dqp), dimension(nb)    :: sum_ddq, sum_res

  continue

    csta = 1
    cend = 2
    cstr = 1
    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      csta = 2
      cend = 1
      cstr =-1
    endif

    change_sign = -1.0_jp
    if ( solve_backwards < 0 ) then
      change_sign = 1.0_jp
    endif

    loop_colored_sweeps = max_colored_sweeps

    sweep_start  = 1
    sweep_end    = loop_colored_sweeps
    sweep_stride = 1

    if ( solve_backwards > 1 .or. solve_backwards < -1 ) then
      sweep_start  = loop_colored_sweeps
      sweep_end    = 1
      sweep_stride = -1
    endif

    actual_sweeps = 0
    sweeping : do sweep = 1, n_sweeps
      actual_sweeps = actual_sweeps + 1

      sum_res(:) = 0.0_jp
      if ( monitor_eqn_group_relax ) then
        sum_ddq(:) = 0.0_jp
      endif

      offset = 0   ! Offset the indices into a,b,c

      color_loop : do color = sweep_start, sweep_end, sweep_stride

        start  =  1
        end    =  2
        stride = -1
        if(color <= colored_sweeps) then
          start  = color_indices(csta,color)
          end    = color_indices(cend,color)
          stride = cstr
        endif

! Loop over lines and solve local block tridiagonal system for each

        lines_within_color : do n = start, end, stride

          line = lines(n)

          offset = lu_offset(line)

          bottom_location = first_neq0(line)
          top_location    = first_neq0(line+1) - 1

          local_eqn = 0

          loop_within_line : do j = bottom_location, top_location

            local_eqn = local_eqn + 1

            equation = line_neq0(j)
            mequation = g2m(equation)

! Now initialize the local right hand side with the residual vector

            do row = 1, 6
              f(row,local_eqn) = change_sign * res(row,mequation)
            end do

! Account for off-diagonal linearized residual contributions
! from on-processor equations that are not in the current implicit line

            kstart = ia(equation)
            kend   = ia(equation+1) - 1
            move_aoff_to_rhs : do k = kstart, kend
              kcol = ja(k)
              ioff = nzg2m(k)
              kcol = g2m(kcol)
              do row = 1, 6
                do col = 1, 6
                  f(row,local_eqn) = f(row,local_eqn)            &
                                   - a_off(row,col,ioff)*dq(col,kcol)
                end do
              end do
            end do move_aoff_to_rhs

            !..Move diagonal contribution to rhs.

            do row = 1, 6
              do col = 1, 6
                f(row,local_eqn) = f(row,local_eqn)            &
                                 - a_diag(row,col,mequation)*dq(col,mequation)
              end do
            end do

            sum_res(1:nb) = sum_res(1:nb) + f(1:nb,local_eqn)**2

          end do loop_within_line

! Factor the local block tridiagonal system
! il and iu are starting and finishing indices

          il = 1
          iu = local_eqn

! Now solve matrix equation

          il1 = il + 1
          is  = il

! f = binv*f

          f(1,is) = b(1,1,offset+is)*(f(1,is))
          f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
          f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                              -b(3,2,offset+is)*f(2,is))
          f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                              -b(4,2,offset+is)*f(2,is)        &
                                              -b(4,3,offset+is)*f(3,is))
          f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)        &
                                              -b(5,2,offset+is)*f(2,is)        &
                                              -b(5,3,offset+is)*f(3,is)        &
                                              -b(5,4,offset+is)*f(4,is))
          f(6,is) = b(6,6,offset+is)*(f(6,is) -b(6,1,offset+is)*f(1,is)        &
                                              -b(6,2,offset+is)*f(2,is)        &
                                              -b(6,3,offset+is)*f(3,is)        &
                                              -b(6,4,offset+is)*f(4,is)        &
                                              -b(6,5,offset+is)*f(5,is))
          f(5,is) = f(5,is)                                                    &
                             - b(5,6,offset+is)*f(6,is)
          f(4,is) = f(4,is)                                                    &
                            - b(4,6,offset+is)*f(6,is)                         &
                            - b(4,5,offset+is)*f(5,is)
          f(3,is) = f(3,is)                                                    &
                            - b(3,6,offset+is)*f(6,is)                         &
                            - b(3,5,offset+is)*f(5,is)                         &
                            - b(3,4,offset+is)*f(4,is)
          f(2,is) = f(2,is)                                                    &
                            - b(2,6,offset+is)*f(6,is)                         &
                            - b(2,5,offset+is)*f(5,is)                         &
                            - b(2,4,offset+is)*f(4,is)                         &
                            - b(2,3,offset+is)*f(3,is)
          f(1,is) = f(1,is)                                                    &
                            - b(1,6,offset+is)*f(6,is)                         &
                            - b(1,5,offset+is)*f(5,is)                         &
                            - b(1,4,offset+is)*f(4,is)                         &
                            - b(1,3,offset+is)*f(3,is)                         &
                            - b(1,2,offset+is)*f(2,is)

! Forward sweep

          forward : do is = il1, iu
            ir = is - 1
            it = is + 1

! First row reduction

            do row = 1, 6
              f(row,is) = f(row,is)                                            &
                              - a(row,1,offset+is)*f(1,ir)                     &
                              - a(row,2,offset+is)*f(2,ir)                     &
                              - a(row,3,offset+is)*f(3,ir)                     &
                              - a(row,4,offset+is)*f(4,ir)                     &
                              - a(row,5,offset+is)*f(5,ir)                     &
                              - a(row,6,offset+is)*f(6,ir)
            end do

! f = binv*f

            f(1,is) = b(1,1,offset+is)*(f(1,is))
            f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
            f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                                -b(3,2,offset+is)*f(2,is))
            f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                                -b(4,2,offset+is)*f(2,is)      &
                                                -b(4,3,offset+is)*f(3,is))
            f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)      &
                                                -b(5,2,offset+is)*f(2,is)      &
                                                -b(5,3,offset+is)*f(3,is)      &
                                                -b(5,4,offset+is)*f(4,is))
            f(6,is) = b(6,6,offset+is)*(f(6,is) -b(6,1,offset+is)*f(1,is)      &
                                                -b(6,2,offset+is)*f(2,is)      &
                                                -b(6,3,offset+is)*f(3,is)      &
                                                -b(6,4,offset+is)*f(4,is)      &
                                                -b(6,5,offset+is)*f(5,is))
            f(5,is) = f(5,is)                                                  &
                              - b(5,6,offset+is)*f(6,is)
            f(4,is) = f(4,is)                                                  &
                              - b(4,6,offset+is)*f(6,is)                       &
                              - b(4,5,offset+is)*f(5,is)
            f(3,is) = f(3,is)                                                  &
                              - b(3,6,offset+is)*f(6,is)                       &
                              - b(3,5,offset+is)*f(5,is)                       &
                              - b(3,4,offset+is)*f(4,is)
            f(2,is) = f(2,is)                                                  &
                              - b(2,6,offset+is)*f(6,is)                       &
                              - b(2,5,offset+is)*f(5,is)                       &
                              - b(2,4,offset+is)*f(4,is)                       &
                              - b(2,3,offset+is)*f(3,is)
            f(1,is) = f(1,is)                                                  &
                              - b(1,6,offset+is)*f(6,is)                       &
                              - b(1,5,offset+is)*f(5,is)                       &
                              - b(1,4,offset+is)*f(4,is)                       &
                              - b(1,3,offset+is)*f(3,is)                       &
                              - b(1,2,offset+is)*f(2,is)

          end do forward

! Back substitution

          backward : do iqq = il1, iu
            is = il + iu - iqq
            it = is + 1
            do row = 1, 6
              f(row,is) =  f(row,is)                                           &
                                - c(row,1,offset+is)*f(1,it)                   &
                                - c(row,2,offset+is)*f(2,it)                   &
                                - c(row,3,offset+is)*f(3,it)                   &
                                - c(row,4,offset+is)*f(4,it)                   &
                                - c(row,5,offset+is)*f(5,it)                   &
                                - c(row,6,offset+is)*f(6,it)
            end do
          end do backward

! Now just put the local solution back into the global solution array

          local_eqn = 0


          fill_solution_ddq : do j = bottom_location, top_location
            local_eqn = local_eqn + 1
            equation = line_neq0(j)
            mequation = g2m(equation)
            omegab = 1._dp
            if ( allow_relaxation_factor  ) then
              omegab = omega_cell_re( omega, cell_re(equation) )
            endif
            if ( monitor_eqn_group_relax ) then
              do row = 1, 6
                sum_ddq(row) = sum_ddq(row) + (omegab*f(row,local_eqn))**2
              end do
            endif
            do row = 1, 6
              dq(row,mequation) =       dq(row,mequation) &
                                + omegab*f(row,local_eqn)
            end do
          end do fill_solution_ddq

          offset = offset + local_eqn

        end do lines_within_color

        call lmpi_xfer(dq,sr_opt=sr(color))

      end do color_loop

      if ( monitor_eqn_group_relax ) then
        call monitor_rms_sr(n_line_neq0, nb, sum_res,   &
                            'line-res ' )
      endif

      if ( .not.hanim ) cycle

      if ( allow_exit ) then
        call check_preconditioner( n_line_neq0, nb, sum_res, &
                                   'line')

        if ( preconditioner_exit > 0 ) exit sweeping
      endif

    end do sweeping

  end subroutine line_solve_6

end module line_solver_ddq

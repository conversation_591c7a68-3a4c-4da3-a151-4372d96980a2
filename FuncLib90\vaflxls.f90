


!=================================== VAFLXLS =================================80
!
! vanAlbada flux limiter applied to a scalar
!
!=============================================================================80

  pure function vaflxls(grad_a, grad_b, eps2)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_2

    real(dp), intent(in) :: grad_a, grad_b, eps2
    real(dp)             :: vaflxls

  continue

    vaflxls = (grad_a*(grad_b**2 + eps2) + grad_b*(grad_a**2 + eps2))/         &
              (grad_a**2 + grad_b**2 + my_2*eps2)

  end function vaflxls

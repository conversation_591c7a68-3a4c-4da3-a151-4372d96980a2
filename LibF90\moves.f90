module moves

  use kinddefs, only : dp

  implicit none

  private

  public :: elasticity, snap_grid
  public :: write_complex_grid_golden

  integer :: elasticity = 1                ! elasticity variation flag
                                           ! 1 => E=1/slen, 2 => E=1/vol, etc.

  logical :: snap_grid = .false. ! Snap raw grid to new location?

contains

!======================== WRITE_COMPLEX_GRID_GOLDEN ==========================80
!
!  Writes a quick and dirty golden file for checking complexified version
!
!=============================================================================80

  subroutine write_complex_grid_golden(nnodes0,nnodes01,x,y,z)

    use lmpi,              only : lmpi_reduce, lmpi_bcast, lmpi_master
    use system_extensions, only : se_open

    integer,                       intent(in) :: nnodes0, nnodes01
    real(dp), dimension(nnodes01), intent(in) :: x,y,z

    integer :: i

    real(dp) :: sumx, sumy, sumz

    real(dp), dimension(3) :: r, s

  continue

    sumx = 0.0_dp
    sumy = 0.0_dp
    sumz = 0.0_dp

    do i = 1, nnodes0
      sumx = sumx + x(i)
      sumy = sumy + y(i)
      sumz = sumz + z(i)
    end do

    r(1) = sumx
    r(2) = sumy
    r(3) = sumz

    call lmpi_reduce(r,s)

    call lmpi_bcast(s)

    sumx = s(1)
    sumy = s(2)
    sumz = s(3)

    if ( lmpi_master ) then
    call se_open(89,file='debug_complex_grid')
      rewind(89)
      write(89,'(2x,e25.15,2x,e25.15)') sumx
      write(89,'(2x,e25.15,2x,e25.15)') sumy
      write(89,'(2x,e25.15,2x,e25.15)') sumz
      close(89)
    endif

  end subroutine write_complex_grid_golden

end module moves

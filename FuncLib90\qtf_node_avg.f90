!================================= QTF_NODE_AVG ==============================80
!
! Face (var1,u,v,w,T) variables from conservative.
!
!=============================================================================80
  pure function qtf_node_avg( eqn_set, n_mf, ql, qr)

    integer, intent(in) :: eqn_set, n_mf

    real(dp), dimension(:), intent(in) :: ql, qr

    real(dp), dimension(n_mf) :: qtf_node_avg

  continue

    qtf_node_avg(1:n_mf) = 0.5_dp*( qt_from_qc( eqn_set, n_mf, ql(1:n_mf) ) &
                                  + qt_from_qc( eqn_set, n_mf, qr(1:n_mf) ) )

  end function qtf_node_avg

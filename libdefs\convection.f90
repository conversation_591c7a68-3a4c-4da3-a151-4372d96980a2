module convection

  use kinddefs,              only : dp
  use info_depr,             only : skeleton
  use bc_names,              only : farfield_convection
  use convection_defs,       only : cu, cv, cw

  implicit none

  private

  public :: initialize_convection
  public :: exact_convection_2d, exact_convection_3d

  real(dp) :: clx = 0.0_dp, cly = 1.0_dp, clz = 0.0_dp
  real(dp) :: cmx = 0.0_dp, cmy = 0.0_dp, cmz = 1.0_dp

  contains

!===================== INITIALIZE_CONVECTION =================================80
!
! Initialize coefficients for 3-D convection.
! Set bc types to inflow/outflow if not an admissible set.
!
!=============================================================================80
subroutine initialize_convection( grid, eqn_set )

  use utilities,             only : tangents
  use lmpi,                  only : lmpi_master, lmpi_conditional_stop
  use grid_types,            only : grid_type
  use twod_util,             only : q_2d
  use bc_names,              only : bc_allow_exact_cc, bc_allow_exact_nc,      &
                                    bc_name_index, symmetry_y

  type(grid_type), intent(inout) :: grid
  integer,         intent(in)    :: eqn_set

  integer :: ib, ibc

  logical :: initialized = .false., allow
  character(len=80) :: bc_name

  continue

  if ( eqn_set /= 1 ) then
    if ( lmpi_master ) then
      write(*,*) ' Only coded within incompressible equation path.'
      write(*,*) ' Stopping in initialize_convection...eqn_set=',eqn_set
    endif
    call lmpi_conditional_stop(1,'initialize_convection')
  endif


  do ib = 1,grid%nbound
    allow = .false.
    ibc = grid%bc(ib)%ibc
    if (      grid%cc .and. bc_allow_exact_cc(ibc,eqn_set) ) allow = .true.
    if ( .not.grid%cc .and. bc_allow_exact_nc(ibc) ) allow = .true.
    if ( .not.q_2d .and. ibc == symmetry_y ) allow = .false.
    if ( allow ) cycle
    grid%bc(ib)%ibc = farfield_convection
    if ( lmpi_master ) then
      write(*,*)
      write(*,"(1x,a,i5)") ' Solving convection equation within incompressible &
                           &path...resetting bc for ib=',ib
      call bc_name_index(ibc,bc_name,.true.)
      write(*,"(1x,a,i5,2a)") ' ...grid%bc(ib)%ibc(existing)=',ibc,&
                              ' bc_name=',trim(bc_name)
      call bc_name_index(grid%bc(ib)%ibc,bc_name,.true.)
      write(*,"(1x,a,i5,2a)") ' ...grid%bc(ib)%ibc(   reset)=',grid%bc(ib)%ibc,&
                              ' bc_name=',trim(bc_name)
    endif
  enddo

  if ( initialized ) return

  call tangents(cu, cv, cw, clx, cly, clz, cmx, cmy, cmz)

  if ( lmpi_master .and. q_2d ) then
    write(*,*)
    write(*,*) ' Solving 2D convection equations : characteristic info:'
    write(*,*)
    write(*,*) '   ...convection characteristic:'
    write(*,*) '   ......x-direction= ',cu
    write(*,*) '   ......z-direction= ',cw
    write(*,*)
  elseif ( lmpi_master ) then
    write(*,*)
    write(*,*) ' Solving 3D convection equations : characteristic info:'
    write(*,*)
    write(*,*) '   ...convection characteristic:'
    write(*,*) '   ......x-direction= ',cu
    write(*,*) '   ......y-direction= ',cv
    write(*,*) '   ......z-direction= ',cw
    write(*,*)
    write(*,*) '   ...first cross-characteristic:'
    write(*,*) '   ......x-direction= ',clx
    write(*,*) '   ......y-direction= ',cly
    write(*,*) '   ......z-direction= ',clz
    write(*,*)
    write(*,*) '   ...second cross-characteristic:'
    write(*,*) '   ......x-direction= ',cmx
    write(*,*) '   ......y-direction= ',cmy
    write(*,*) '   ......z-direction= ',cmz
    write(*,*)
    write(*,*) ' Orthogonality error1=', cu*clx+ cv*cly+ cw*clz
    write(*,*) ' Orthogonality error2=', cu*cmx+ cv*cmy+ cw*cmz
    write(*,*) ' Orthogonality error3=',cmx*clx+cmy*cly+cmz*clz
    write(*,*)
  endif
  initialized = .true.

end subroutine initialize_convection

!================================ EXACT_CONVECTION_2D ========================80
!
! 2-D convection solution for sine along a characteristic.
!
!=============================================================================80
subroutine exact_convection_2d( x, y, q, forcing )

  use fun3d_constants, only : pi

  real(dp), intent(in)  :: x, y
  real(dp), intent(out) :: q(1:3)
  logical,  intent(in), optional :: forcing

  real(dp) :: qc, xie, eta

  integer :: i_u = 1, i_v = 2, i_p = 3

  logical, parameter :: debug_terms = .false.

  real(dp), parameter :: quadratic = 1.0_dp

  continue

  qc = sqrt( cu**2 + cw**2 ) !characteristic velocity

  xie = ( cu*x + cw*y )/qc   !characteristic coordinate

  eta = (-cw*x + cu*y )/qc

  q(i_p) = sin( 4.0_dp*pi*qc*xie )    !variation along characteristic

  q(i_u) = qc*( xie + quadratic*0.5_dp*xie**2 ) !variation along characteristic

  q(i_v) = sin( pi*qc*eta )           !variation along cross-characteristic

  if(present(forcing)) then
    q(i_p) = qc*cos( 4.0_dp*pi*qc*xie )*4.0_dp*pi*qc
    q(i_u) = qc*qc*( 1.0_dp + quadratic*xie )
    q(i_v) = 0.0_dp
  endif

  if(.not.debug_terms) return

  if(skeleton < 1) return

  if(present(forcing)) then
    write(*,*) ' Debugging forcing terms for convection...'
  else
    write(*,*) ' Debugging q terms for convection...'
  endif
  write(*,*) ' ...x,y,xie,eta,qc=',x,y,xie,eta,qc
  write(*,*) ' ...q(i_p),q(i_u),q(i_v)=',q(i_p),q(i_u),q(i_v)

end subroutine exact_convection_2d

!================================ EXACT_CONVECTION_3D ========================80
!
! 3-D convection solution for sine along a characteristic.
!
!=============================================================================80
subroutine exact_convection_3d( x, y, z, q, forcing )

  use fun3d_constants, only : pi

  real(dp), intent(in)  :: x, y, z
  real(dp), intent(out) :: q(1:4)
  logical,  intent(in), optional :: forcing

  real(dp) :: qc, xie, eta, zie

  logical, parameter :: debug_terms = .false.

  real(dp), parameter :: quadratic = 1.0_dp

  continue

  qc = sqrt( cu**2 + cv**2 + cw**2 )  !characteristic velocity

  xie = ( cu*x + cv*y + cw*z )/qc     !characteristic coordinate

  eta = ( clx*x + cly*y + clz*z )/qc  !cross-characteristic coordinate

  zie = ( cmx*x + cmy*y + cmz*z )/qc  !cross-characteristic coordinate

  !variation along characteristic
  q(1) = sin( 1.0_dp*pi*qc*xie )

  !variation along characteristic
  q(2) = qc*( xie + quadratic*0.5_dp*xie**2 )

  !variation along cross-characteristic
  q(3) = sin( pi*qc*(eta + zie) )

  !linear variation
  q(4) = qc*(xie + eta + zie)

  if(present(forcing)) then
    q(1) = qc*cos( 1.0_dp*pi*qc*xie )*1.0_dp*pi*qc
    q(2) = qc*qc*( 1.0_dp + quadratic*xie )
    q(3) = 0.0_dp
    q(4) = qc*qc*( 1.0_dp )
  endif

  if(.not.debug_terms) return

  if(skeleton < 1) return

  if(present(forcing)) then
    write(*,*) ' Debugging forcing terms for convection...'
  else
    write(*,*) ' Debugging q terms for convection...'
  endif
  write(*,*) ' ...x,y,z=',x,y,z
  write(*,*) ' ...xie,eta,zie,qc=',xie,eta,zie,qc
  write(*,*) ' ...q(1:4)=',q(1:4)

end subroutine exact_convection_3d

end module convection

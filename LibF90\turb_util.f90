module turb_util

  use kinddefs,       only : dp
  use lmpi,           only : lmpi_conditional_stop
  use info_depr,      only : twod
  use bc_types,       only : bcgrid_type
  use element_types,  only : elem_type
  use twod_util,      only : yplane_2d, y_coplanar_tol

  implicit none

  private

  public :: edget
  public :: turbgrad
  public :: turbmultieqn_grad

  public :: kloc, set_slen_wall_nonzero, set_slen_wall_zero
  public :: wloc
  public :: get_tau
  public :: get_wws
  public :: df_mut
  public :: set_gradv
  public :: strain_tensor_deriv
  public :: velocity_gradient_derivative
  public :: trb_kloc, trb_wloc

! locations for turbulent gradients in grad vector

  integer :: kloc ! location of varible k
  integer :: wloc ! location of variable omega

! locations for turbulent variables in turb vector

  integer :: trb_kloc ! location of varible k
  integer :: trb_wloc ! location of variable omega

! set indices where k and w (or eps) gradients are stored
!   kloc = n_grd-n_turb+1 ! depends on turbulence model
!   wloc = n_grd-n_turb+2

contains

!================================= TURBGRAD ==================================80
!
! Gradients for source terms in the turbulence model
! via Green-Gauss over dual volume boundary but only calculates:
! ux,uy,uz, vx,vy,vz, wx,wy,wz <=> gradx(2:4),grady(2:4),gradz(2:4).
!
! Enforces symmetry exactly.
!
! Equivalent to a volume-weighted Green-Gauss integration over
! the node-adjacent primal volumes only in the case of triangles/tetraheda.
!
!=============================================================================80

  subroutine turbgrad(eqn_set,                                                 &
                      nnodes0, nnodes01, nedgeloc, eptr, q_dof,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, gradx, grady, gradz, nbound, bc,    &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

    use bc_names,       only : bc_ignore_2d, bc_is_periodic
    use flux_symmetry,  only : symmetry_gradients
    use solution_types, only : compressible, incompressible, generic_gas
    use thermo,         only : primitive_q_type, q_type
    use generic_gas_map,only : n_etot

    integer, intent(in) :: eqn_set, nnodes0, nnodes01, n_tot, n_grd
    integer, intent(in) :: nedgeloc, nedgeloc_2d, nbound, nelem, nnodes0_2d

    integer,  dimension(2,nedgeloc),   intent(in)  :: eptr
    integer,  dimension(2,nnodes0_2d), intent(in)  :: node_pairs_2d

    real(dp), dimension(:),   intent(in)    :: x, y, z, dxdt, dydt, dzdt, vol
    real(dp), dimension(:),   intent(in)    :: xn, yn, zn, ra
    real(dp), dimension(:,:), intent(in)    :: q_dof
    real(dp), dimension(:,:), intent(inout) :: gradx, grady, gradz

    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc
    type(elem_type),   dimension(nelem),   intent(in)  :: elem

    integer :: ierr, ib, ibc, dof0, n_q, i

 continue

    dof0 = nnodes0
    if ( twod ) dof0 = nnodes0/2

    n_q = size( gradx, 1 )

    ierr      = 0
    if ( eqn_set == compressible .or. eqn_set == generic_gas ) then
      ierr = primitive_q_type - q_type
    else if ( eqn_set == incompressible ) then
      ierr = 0
    else
      ierr = 1
    end if

    call lmpi_conditional_stop(ierr,'eqn_set/q_type failure:turbgrad')

    if (eqn_set == generic_gas ) then
      if ( (size(q_dof,1) > n_tot) ) ierr = 1
    else
      if ( (size(q_dof,1) > n_tot) .and. (size(q_dof,1) > n_grd) ) ierr = 1
    end if

    call lmpi_conditional_stop(ierr,'size q_dof failure:turbgrad')

    if ( eqn_set == compressible .or. eqn_set == generic_gas ) then

      call turbgrad_interior(                           &
                nnodes0,  q_dof,                        &
                gradx,    grady,       gradz,           &
                nedgeloc, nedgeloc_2d, eptr,            &
                xn,       yn,          zn,    ra )

      do ib = 1,nbound

        ibc = bc(ib)%ibc

        if ( twod .and. bc_ignore_2d(ibc) ) cycle
        if ( bc_is_periodic(ibc) ) cycle

        call turbgrad_bc_eb( ib, n_q, dof0, q_dof,     &
                           gradx, grady, gradz,        &
                           x, y, z, dxdt, dydt, dzdt,  &
                           bc, elem )

      enddo

      ! Correct gradients for symmetry conditions

      do ib = 1,nbound

        call symmetry_gradients(                   &
         bc(ib)%ibc, bc(ib)%nbnode, bc(ib)%ibnode, &
         nnodes0, nnodes01, n_grd,                 &
         gradx, grady, gradz, q_dof,               &
         bc(ib)%bxn, bc(ib)%byn, bc(ib)%bzn,       &
         x, y, z, velocity_only = .true. )

      end do

      call turbgrad_closure( nnodes0, nnodes01,       &
                           vol, gradx, grady, gradz )

      if (twod) then
        do i = 1,nnodes0_2d
          gradx(1:n_etot,node_pairs_2d(2,i)) =gradx(1:n_etot,node_pairs_2d(1,i))
          grady(1:n_etot,node_pairs_2d(2,i)) =grady(1:n_etot,node_pairs_2d(1,i))
          gradz(1:n_etot,node_pairs_2d(2,i)) =gradz(1:n_etot,node_pairs_2d(1,i))
        end do
      endif

    else

      call turbgrad_interior_i(                         &
                nnodes0,  q_dof,                        &
                gradx,    grady,       gradz,           &
                nedgeloc, nedgeloc_2d, eptr,            &
                xn,       yn,          zn,    ra )

      do ib = 1,nbound

        ibc = bc(ib)%ibc

        if ( twod .and. bc_ignore_2d(ibc) ) cycle
        if ( bc_is_periodic(ibc) ) cycle

        call turbgrad_bc_eb_i( ib, n_q, dof0, q_dof,   &
                           gradx, grady, gradz,        &
                           x, y, z, dxdt, dydt, dzdt,  &
                           bc, elem )

      enddo

      ! Correct gradients for symmetry conditions

      do ib = 1,nbound

        call symmetry_gradients(                   &
         bc(ib)%ibc, bc(ib)%nbnode, bc(ib)%ibnode, &
         nnodes0, nnodes01, n_grd,                 &
         gradx, grady, gradz, q_dof,               &
         bc(ib)%bxn, bc(ib)%byn, bc(ib)%bzn,       &
         x, y, z, velocity_only = .true. )

      end do

      call turbgrad_closure_i( nnodes0, nnodes01,       &
                           vol, gradx, grady, gradz )

      if (twod) then
        do i = 1,nnodes0_2d
          gradx(1:4,node_pairs_2d(2,i)) = gradx(1:4,node_pairs_2d(1,i))
          grady(1:4,node_pairs_2d(2,i)) = grady(1:4,node_pairs_2d(1,i))
          gradz(1:4,node_pairs_2d(2,i)) = gradz(1:4,node_pairs_2d(1,i))
        end do
      endif

    endif

  end subroutine turbgrad

!================================= TURBGRAD_INTERIOR =========================80
!
! Green-Gauss gradients of velocity - interior edges.
! qnode assumed in primitive variable form
!
!=============================================================================80

  subroutine turbgrad_interior( nnodes0, qnode, gradx, grady, gradz,           &
                                nedgeloc, nedgeloc_2d, eptr, xn, yn, zn, ra )

    use info_depr,       only : twod, grad_x_y_z_contents
    use generic_gas_map, only : n_species, n_momx, n_momy, n_momz, n_etot

    integer, intent(in) :: nnodes0, nedgeloc, nedgeloc_2d

    integer, dimension(2,nedgeloc), intent(in)  :: eptr

    real(dp), dimension(:,:), intent(in)    :: qnode
    real(dp), dimension(:),   intent(in)    :: xn, yn, zn, ra
    real(dp), dimension(:,:), intent(inout) :: gradx, grady, gradz

    integer :: n, node1, node2, nedge_flux_eval

    real(dp) :: area, q1, q2, q3, q4, q5, xnorm, ynorm, znorm
    real(dp), dimension(:), allocatable :: qi
    logical :: multi_species

  continue

    grad_x_y_z_contents = 'turbgrad'

    nedge_flux_eval = nedgeloc
    if ( twod ) then
      nedge_flux_eval = nedgeloc_2d
    endif

    gradx(1:n_etot,:) = 0._dp
    grady(1:n_etot,:) = 0._dp
    gradz(1:n_etot,:) = 0._dp

    if(n_species>1)then
      multi_species = .true.
      allocate(qi(n_species))
    else
      multi_species = .false.
    end if

    do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      area  = ra(n)
      xnorm = area * xn(n)
      ynorm = area * yn(n)
      znorm = area * zn(n)

      if(multi_species)then
        qi = 0.5_dp * (qnode(1:n_species,node2)+qnode(1:n_species,node1))
        q1 = sum(qi)
      else
        q1 = 0.5_dp * (qnode(1,node2)+qnode(1,node1))
      end if
      q5 = 0.5_dp * (qnode(n_etot,node2)+qnode(n_etot,node1))
      q2 = 0.5_dp * (qnode(n_momx,node2)+qnode(n_momx,node1))
      q3 = 0.5_dp * (qnode(n_momy,node2)+qnode(n_momy,node1))
      q4 = 0.5_dp * (qnode(n_momz,node2)+qnode(n_momz,node1))

      if (node1 <= nnodes0) then

        if(multi_species)then
          gradx(1:n_species,node1) = gradx(1:n_species,node1)                  &
                                   + xnorm*qi(1:n_species)
        else
          gradx(1,node1) = gradx(1,node1) + xnorm*q1
        end if
        gradx(n_etot,node1) = gradx(n_etot,node1) + xnorm*q5
        gradx(n_momx,node1) = gradx(n_momx,node1) + xnorm*q2
        gradx(n_momy,node1) = gradx(n_momy,node1) + xnorm*q3
        gradx(n_momz,node1) = gradx(n_momz,node1) + xnorm*q4

        if(multi_species)then
          grady(1:n_species,node1) = grady(1:n_species,node1)                  &
                                   + ynorm*qi(1:n_species)
        else
          grady(1,node1) = grady(1,node1) + ynorm*q1
        end if
        grady(n_etot,node1) = grady(n_etot,node1) + ynorm*q5
        grady(n_momx,node1) = grady(n_momx,node1) + ynorm*q2
        grady(n_momy,node1) = grady(n_momy,node1) + ynorm*q3
        grady(n_momz,node1) = grady(n_momz,node1) + ynorm*q4

        if(multi_species)then
          gradz(1:n_species,node1) = gradz(1:n_species,node1)                  &
                                   + znorm*qi(1:n_species)
        else
          gradz(1,node1) = gradz(1,node1) + znorm*q1
        end if
        gradz(n_etot,node1) = gradz(n_etot,node1) + znorm*q5
        gradz(n_momx,node1) = gradz(n_momx,node1) + znorm*q2
        gradz(n_momy,node1) = gradz(n_momy,node1) + znorm*q3
        gradz(n_momz,node1) = gradz(n_momz,node1) + znorm*q4
      end if

      if (node2 <= nnodes0) then

        if(multi_species)then
          gradx(1:n_species,node2) = gradx(1:n_species,node2)                  &
                                   - xnorm*qi(1:n_species)
        else
          gradx(1,node2) = gradx(1,node2) - xnorm*q1
        end if
        gradx(n_etot,node2) = gradx(n_etot,node2) - xnorm*q5
        gradx(n_momx,node2) = gradx(n_momx,node2) - xnorm*q2
        gradx(n_momy,node2) = gradx(n_momy,node2) - xnorm*q3
        gradx(n_momz,node2) = gradx(n_momz,node2) - xnorm*q4

        if(multi_species)then
          grady(1:n_species,node2) = grady(1:n_species,node2)                  &
                                   - ynorm*qi(1:n_species)
        else
          grady(1,node2) = grady(1,node2) - ynorm*q1
        end if
        grady(n_etot,node2) = grady(n_etot,node2) - ynorm*q5
        grady(n_momx,node2) = grady(n_momx,node2) - ynorm*q2
        grady(n_momy,node2) = grady(n_momy,node2) - ynorm*q3
        grady(n_momz,node2) = grady(n_momz,node2) - ynorm*q4

        if(multi_species)then
          gradz(1:n_species,node2) = gradz(1:n_species,node2)                  &
                                   - znorm*qi(1:n_species)
        else
          gradz(1,node2) = gradz(1,node2) - znorm*q1
        end if
        gradz(n_etot,node2) = gradz(n_etot,node2) - znorm*q5
        gradz(n_momx,node2) = gradz(n_momx,node2) - znorm*q2
        gradz(n_momy,node2) = gradz(n_momy,node2) - znorm*q3
        gradz(n_momz,node2) = gradz(n_momz,node2) - znorm*q4
      end if

      end do
      if(multi_species) deallocate(qi)

  end subroutine turbgrad_interior

!================================= TURBGRAD_INTERIOR_I =======================80
!
! Green-Gauss gradients of velocity - interior edges.
! qnode assumed in primitive variable form incompressible
!
!=============================================================================80

  subroutine turbgrad_interior_i( nnodes0, qnode, gradx, grady, gradz,         &
                                nedgeloc, nedgeloc_2d, eptr, xn, yn, zn, ra )

    use info_depr,       only : twod, grad_x_y_z_contents

    integer, intent(in) :: nnodes0, nedgeloc, nedgeloc_2d

    integer, dimension(2,nedgeloc), intent(in)  :: eptr

    real(dp), dimension(:,:), intent(in)    :: qnode
    real(dp), dimension(:),   intent(in)    :: xn, yn, zn, ra
    real(dp), dimension(:,:), intent(inout) :: gradx, grady, gradz

    integer :: n, node1, node2, nedge_flux_eval

    real(dp) :: area, q1, q2, q3, q4, xnorm, ynorm, znorm

  continue

    grad_x_y_z_contents = 'turbgrad'

    nedge_flux_eval = nedgeloc
    if ( twod ) then
      nedge_flux_eval = nedgeloc_2d
    endif

    gradx(:,:) = 0._dp
    grady(:,:) = 0._dp
    gradz(:,:) = 0._dp

    do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      area  = ra(n)
      xnorm = area * xn(n)
      ynorm = area * yn(n)
      znorm = area * zn(n)

      q1 = 0.5_dp * (qnode(1,node2)+qnode(1,node1))
      q2 = 0.5_dp * (qnode(2,node2)+qnode(2,node1))
      q3 = 0.5_dp * (qnode(3,node2)+qnode(3,node1))
      q4 = 0.5_dp * (qnode(4,node2)+qnode(4,node1))

      if (node1 <= nnodes0) then

        gradx(1,node1) = gradx(1,node1) + xnorm*q1
        gradx(2,node1) = gradx(2,node1) + xnorm*q2
        gradx(3,node1) = gradx(3,node1) + xnorm*q3
        gradx(4,node1) = gradx(4,node1) + xnorm*q4

        grady(1,node1) = grady(1,node1) + ynorm*q1
        grady(2,node1) = grady(2,node1) + ynorm*q2
        grady(3,node1) = grady(3,node1) + ynorm*q3
        grady(4,node1) = grady(4,node1) + ynorm*q4

        gradz(1,node1) = gradz(1,node1) + znorm*q1
        gradz(2,node1) = gradz(2,node1) + znorm*q2
        gradz(3,node1) = gradz(3,node1) + znorm*q3
        gradz(4,node1) = gradz(4,node1) + znorm*q4
      end if

      if (node2 <= nnodes0) then

        gradx(1,node2) = gradx(1,node2) - xnorm*q1
        gradx(2,node2) = gradx(2,node2) - xnorm*q2
        gradx(3,node2) = gradx(3,node2) - xnorm*q3
        gradx(4,node2) = gradx(4,node2) - xnorm*q4

        grady(1,node2) = grady(1,node2) - ynorm*q1
        grady(2,node2) = grady(2,node2) - ynorm*q2
        grady(3,node2) = grady(3,node2) - ynorm*q3
        grady(4,node2) = grady(4,node2) - ynorm*q4

        gradz(1,node2) = gradz(1,node2) - znorm*q1
        gradz(2,node2) = gradz(2,node2) - znorm*q2
        gradz(3,node2) = gradz(3,node2) - znorm*q3
        gradz(4,node2) = gradz(4,node2) - znorm*q4
      end if

      end do

  end subroutine turbgrad_interior_i

!================================= TURBGRAD_CLOSURE ==========================80
!
! Green-Gauss gradients of velocity - closure.
!
!=============================================================================80

  subroutine turbgrad_closure( nnodes0, nnodes01, vol, gradx, grady, gradz )

    use kinddefs,        only : dp
    use info_depr,       only : twod
    use periodics,       only : nperiodic, periodic_data, periodic
    use generic_gas_map, only : n_etot

    integer, intent(in) :: nnodes0, nnodes01

    real(dp), dimension(:),   intent(in)    :: vol
    real(dp), dimension(:,:), intent(inout) :: gradx, grady, gradz

    integer :: i, node1, node2, dof0, j, node, set_number

    integer, dimension(nnodes01) :: periodic_tag

    real(dp) :: xvol, volume_sum

    real(dp), dimension(:), allocatable :: gx,gy,gz

  continue

    dof0 = nnodes0
    if ( twod ) dof0 = nnodes0/2
    allocate(gx(n_etot),gy(n_etot),gz(n_etot))

    if ( periodic ) then

! Combine the contributions from nodes in the periodic sets
! Also set up a tag we'll need later

      periodic_tag = 0

      do i = 1, nperiodic

        gx(1:n_etot) = 0.0_dp
        gy(1:n_etot) = 0.0_dp
        gz(1:n_etot) = 0.0_dp

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          gx(1:n_etot) = gx(1:n_etot) + gradx(1:n_etot,node)
          gy(1:n_etot) = gy(1:n_etot) + grady(1:n_etot,node)
          gz(1:n_etot) = gz(1:n_etot) + gradz(1:n_etot,node)
        end do

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          gradx(1:n_etot,node) = gx(1:n_etot)
          grady(1:n_etot,node) = gy(1:n_etot)
          gradz(1:n_etot,node) = gz(1:n_etot)
          if ( j == 1 ) then
            periodic_tag(node) = i
          else
            periodic_tag(node) = -i
          endif
        end do

      end do

      do i = 1,nnodes01
        if ( periodic_tag(i) == 0 ) then

          xvol = 1._dp/vol(i)

          gradx(1:n_etot,i) = gradx(1:n_etot,i)*xvol
          grady(1:n_etot,i) = grady(1:n_etot,i)*xvol
          gradz(1:n_etot,i) = gradz(1:n_etot,i)*xvol

        else if ( periodic_tag(i) > 0 ) then

! Only do periodic nodes when we hit the primary nodes (don't double bookkeep)

          set_number = periodic_tag(i)
          node1 = periodic_data(set_number)%list(1)
          volume_sum = vol(node1)
          do j = 2, periodic_data(set_number)%n
            node2 = periodic_data(set_number)%list(j)
            volume_sum = volume_sum + vol(node2)
          end do

          xvol = 1._dp/volume_sum

          do j = 1, periodic_data(set_number)%n
            node = periodic_data(set_number)%list(j)
            gradx(1:n_etot,node) = gradx(1:n_etot,node)*xvol
            grady(1:n_etot,node) = grady(1:n_etot,node)*xvol
            gradz(1:n_etot,node) = gradz(1:n_etot,node)*xvol
          end do

        endif
      end do

    else

      do i = 1,dof0

        xvol = 1._dp/vol(i)

        gradx(1:n_etot,i) = gradx(1:n_etot,i)*xvol
        grady(1:n_etot,i) = grady(1:n_etot,i)*xvol
        gradz(1:n_etot,i) = gradz(1:n_etot,i)*xvol

      end do

    end if
    deallocate(gx,gy,gz)

  end subroutine turbgrad_closure

!================================= TURBGRAD_CLOSURE_I ========================80
!
! Green-Gauss gradients of velocity - closure.
!
!=============================================================================80

  subroutine turbgrad_closure_i( nnodes0, nnodes01, vol, gradx, grady, gradz )

    use kinddefs,        only : dp
    use info_depr,       only : twod
    use periodics,       only : nperiodic, periodic_data, periodic

    integer, intent(in) :: nnodes0, nnodes01

    real(dp), dimension(:),   intent(in)    :: vol
    real(dp), dimension(:,:), intent(inout) :: gradx, grady, gradz

    integer :: i, node1, node2, dof0, j, node, set_number

    integer, dimension(nnodes01) :: periodic_tag

    real(dp) :: xvol, volume_sum

    real(dp), dimension(4) :: gx,gy,gz

  continue

    dof0 = nnodes0
    if ( twod ) dof0 = nnodes0/2

    if ( periodic ) then

! Combine the contributions from nodes in the periodic sets
! Also set up a tag we'll need later

      periodic_tag = 0

      do i = 1, nperiodic

        gx(1:4) = 0.0_dp
        gy(1:4) = 0.0_dp
        gz(1:4) = 0.0_dp

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          gx(1:4) = gx(1:4) + gradx(1:4,node)
          gy(1:4) = gy(1:4) + grady(1:4,node)
          gz(1:4) = gz(1:4) + gradz(1:4,node)
        end do

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          gradx(1:4,node) = gx(1:4)
          grady(1:4,node) = gy(1:4)
          gradz(1:4,node) = gz(1:4)
          if ( j == 1 ) then
            periodic_tag(node) = i
          else
            periodic_tag(node) = -i
          endif
        end do

      end do

      do i = 1,nnodes01
        if ( periodic_tag(i) == 0 ) then

          xvol = 1._dp/vol(i)

          gradx(1:4,i) = gradx(1:4,i)*xvol
          grady(1:4,i) = grady(1:4,i)*xvol
          gradz(1:4,i) = gradz(1:4,i)*xvol

        else if ( periodic_tag(i) > 0 ) then

! Only do periodic nodes when we hit the primary nodes (don't double bookkeep)

          set_number = periodic_tag(i)
          node1 = periodic_data(set_number)%list(1)
          volume_sum = vol(node1)
          do j = 2, periodic_data(set_number)%n
            node2 = periodic_data(set_number)%list(j)
            volume_sum = volume_sum + vol(node2)
          end do

          xvol = 1._dp/volume_sum

          do j = 1, periodic_data(set_number)%n
            node = periodic_data(set_number)%list(j)
            gradx(1:4,node) = gradx(1:4,node)*xvol
            grady(1:4,node) = grady(1:4,node)*xvol
            gradz(1:4,node) = gradz(1:4,node)*xvol
          end do

        endif
      end do

    else

      do i = 1,dof0

        xvol = 1._dp/vol(i)

        gradx(1:4,i) = gradx(1:4,i)*xvol
        grady(1:4,i) = grady(1:4,i)*xvol
        gradz(1:4,i) = gradz(1:4,i)*xvol

      end do

    end if

  end subroutine turbgrad_closure_i

!================================== EDGET ====================================80
!
! This routine computes necessary edge data for turbulence model
! It accumulates data necessary for evaluating the dissipation terms
!
! Both compressible (eqn_set = 0) and incompressible (eqn_set = 1)
!
!=============================================================================80

  subroutine edget(eqn_set, nnodes01, ncell, nedgeloc, c2n, c2e, x, y, z,      &
                   qnode, turb, dft1, dft2, n_turb, n_tot, negsa )

    use kinddefs,        only : dp
    use info_depr,       only : tref
    use turb_sa_const,   only : cb2
    use fluid,           only : gamma, sutherland_constant
    use fun3d_constants, only : my_0, my_4th, my_half, my_1, my_6th, my_9
    use turb_parameters, only : t_diff1, t_diff2, t_diff3
    use solution_types,  only : compressible, incompressible

    integer,                              intent(in)  :: eqn_set
    integer,                              intent(in)  :: nnodes01, n_turb, n_tot
    integer,                              intent(in)  :: ncell
    integer,                              intent(in)  :: nedgeloc
    integer,  dimension(4,ncell),         intent(in)  :: c2n
    integer,  dimension(6,ncell),         intent(in)  :: c2e
    real(dp), dimension(n_tot,nnodes01),  intent(in)  :: qnode
    real(dp), dimension(nnodes01),        intent(in)  :: x, y, z
    real(dp), dimension(n_turb,nnodes01), intent(in)  :: turb
    real(dp), dimension(:),               intent(out) :: dft1, dft2
    integer,                              intent(in)  :: negsa

    integer :: i, n
    integer :: edge1, edge2, edge3, edge4, edge5, edge6
    integer :: node1, node2, node3, node4

    real(dp) :: t1, t2, t3, t4, const, cstar
    real(dp) :: nx1, nx2, nx3, nx4, ny1, ny2, ny3, ny4, nz1, nz2, nz3, nz4
    real(dp) :: p1, p2, p3, p4, phi, r1, r2, r3, r4
    real(dp) :: rnu, rnu1, rnu2, rnu3,rnu4
    real(dp) :: trbre, trbre1, trbre2, trbre3, trbre4, vol
    real(dp) :: wcon, wxx12, wxx13, wxx14, wxx23, wxx24, wxx34
    real(dp) :: wyy12, wyy13, wyy14
    real(dp) :: wyy23, wyy24, wyy34, wzz12, wzz13, wzz14, wzz23, wzz24, wzz34
    real(dp) :: x1, x2, x3, x4, y1, y2, y3, y4, z1, z2, z3, z4
    real(dp) :: r1inv,r2inv,r3inv,r4inv,cb20,cb21
    real(dp) :: atrbre, atrbre1, atrbre2, atrbre3, atrbre4

  continue

    physics_check : select case (eqn_set)

    case (compressible)

    case (incompressible)

    case default

      call lmpi_conditional_stop(1,&
      'edget only handles comp. and incomp. gas.')

    end select physics_check

!   The dft1 variable is for del.del(nu)
!   The dft2 variable is for del.(phi x del(nu))

    cstar = sutherland_constant / tref

!   To characterize three types of turbulent diffusion

    cb20 = t_diff3*cb2
    cb21 = t_diff2 + cb20

!   Initialize all the dft's to zero

    do i = 1,nedgeloc
      dft1(i) = my_0
      dft2(i) = my_0
    end do

!   Loop over all the cells and calculate viscous contribution

      cell_loop : do n = 1, ncell

!       First get the four nodes and the six edges
!       For now we'll just use the cell-to-node pointers

        node1 = c2n(1,n)
        node2 = c2n(2,n)
        node3 = c2n(3,n)
        node4 = c2n(4,n)

        edge1 = c2e(1,n) !Between nodes 1-2
        edge2 = c2e(2,n) !Between nodes 1-3
        edge3 = c2e(3,n) !Between nodes 1-4
        edge4 = c2e(4,n) !Between nodes 2-3
        edge5 = c2e(5,n) !Between nodes 2-4
        edge6 = c2e(6,n) !Between nodes 3-4

        x1 = x(node1)
        x2 = x(node2)
        x3 = x(node3)
        x4 = x(node4)

        y1 = y(node1)
        y2 = y(node2)
        y3 = y(node3)
        y4 = y(node4)

        z1 = z(node1)
        z2 = z(node2)
        z3 = z(node3)
        z4 = z(node4)

!       Lets get outward normals (nx_i is for the face opposite node i)

        nx1 = my_half * ((y2-y4)*(z3-z4)-(y3-y4)*(z2-z4))
        ny1 = my_half * ((z2-z4)*(x3-x4)-(z3-z4)*(x2-x4))
        nz1 = my_half * ((x2-x4)*(y3-y4)-(x3-x4)*(y2-y4))

        nx2 = my_half * ((y3-y4)*(z1-z4)-(y1-y4)*(z3-z4))
        ny2 = my_half * ((z3-z4)*(x1-x4)-(z1-z4)*(x3-x4))
        nz2 = my_half * ((x3-x4)*(y1-y4)-(x1-x4)*(y3-y4))

        nx3 = my_half * ((y1-y4)*(z2-z4)-(y2-y4)*(z1-z4))
        ny3 = my_half * ((z1-z4)*(x2-x4)-(z2-z4)*(x1-x4))
        nz3 = my_half * ((x1-x4)*(y2-y4)-(x2-x4)*(y1-y4))

        nx4 = -nx1 - nx2 - nx3
        ny4 = -ny1 - ny2 - ny3
        nz4 = -nz1 - nz2 - nz3

!       Compute cell volume

        vol = (((y2-y1)*(z3-z1)-(y3-y1)*(z2-z1))*(x4-x1)-                      &
               ((x2-x1)*(z3-z1)-(x3-x1)*(z2-z1))*(y4-y1)+                      &
               ((x2-x1)*(y3-y1)-(x3-x1)*(y2-y1))*(z4-z1)) * my_6th

!       Compute cell averaged quantities you need

        physics : select case (eqn_set)

          case (compressible)

            r1 = qnode(1,node1)
            p1 = qnode(5,node1)
            r2 = qnode(1,node2)
            p2 = qnode(5,node2)
            r3 = qnode(1,node3)
            p3 = qnode(5,node3)
            r4 = qnode(1,node4)
            p4 = qnode(5,node4)

            r1inv = my_1/r1
            r2inv = my_1/r2
            r3inv = my_1/r3
            r4inv = my_1/r4

            t1 = gamma * p1 * r1inv
            t2 = gamma * p2 * r2inv
            t3 = gamma * p3 * r3inv
            t4 = gamma * p4 * r4inv

            rnu1 = viscosity_law( cstar, t1) * r1inv
            rnu2 = viscosity_law( cstar, t2) * r2inv
            rnu3 = viscosity_law( cstar, t3) * r3inv
            rnu4 = viscosity_law( cstar, t4) * r4inv

          case default

            rnu1 = my_1
            rnu2 = my_1
            rnu3 = my_1
            rnu4 = my_1

        end select physics

        rnu = my_4th * (rnu1+rnu2+rnu3+rnu4)

        trbre1 = turb(1,node1)
        trbre2 = turb(1,node2)
        trbre3 = turb(1,node3)
        trbre4 = turb(1,node4)

        trbre = my_4th * (trbre1+trbre2+trbre3+trbre4)

        phi = t_diff1*rnu
        if ( negsa == 0 ) then
          phi = phi + cb21*trbre
        else
          atrbre1 = sa0_turb_abs( trbre1, rnu1 )
          atrbre2 = sa0_turb_abs( trbre2, rnu2 )
          atrbre3 = sa0_turb_abs( trbre3, rnu3 )
          atrbre4 = sa0_turb_abs( trbre4, rnu4 )
          atrbre = my_4th * (atrbre1+atrbre2+atrbre3+atrbre4)
          phi = phi + t_diff3*atrbre + cb20*trbre
        endif

!       Now lets compute some weights
!       Only need the symetric ones

        const =  my_1 / (my_9*vol)

        wxx12 = -nx2*nx1*const
        wxx13 = -nx3*nx1*const
        wxx14 = -nx4*nx1*const
        wxx34 = -nx4*nx3*const
        wxx23 = -nx3*nx2*const
        wxx24 = -nx4*nx2*const

        wyy12 = -ny2*ny1*const
        wyy13 = -ny3*ny1*const
        wyy14 = -ny4*ny1*const
        wyy34 = -ny4*ny3*const
        wyy23 = -ny3*ny2*const
        wyy24 = -ny4*ny2*const

        wzz12 = -nz2*nz1*const
        wzz13 = -nz3*nz1*const
        wzz14 = -nz4*nz1*const
        wzz34 = -nz4*nz3*const
        wzz23 = -nz3*nz2*const
        wzz24 = -nz4*nz2*const

!       Now, since all these terms are symmetric this is easier than before
!       because we dont care what "type" of node it is

!       For edge along 1-2

        wcon = wxx12 + wyy12 + wzz12
        if (edge1 <= nedgeloc) then
          dft1(edge1) = dft1(edge1) + wcon
          dft2(edge1) = dft2(edge1) + phi*wcon
        end if

!       For edge along 1-3

        wcon = wxx13 + wyy13 + wzz13
        if (edge2 <= nedgeloc) then
          dft1(edge2) = dft1(edge2) + wcon
          dft2(edge2) = dft2(edge2) + phi*wcon
        end if

!       For edge along 1-4

        wcon = wxx14 + wyy14 + wzz14
        if (edge3 <= nedgeloc) then
          dft1(edge3) = dft1(edge3) + wcon
          dft2(edge3) = dft2(edge3) + phi*wcon
        end if

!       For edge along 2-3

        wcon = wxx23 + wyy23 + wzz23
        if (edge4 <= nedgeloc) then
          dft1(edge4) = dft1(edge4) + wcon
          dft2(edge4) = dft2(edge4) + phi*wcon
        end if

!       For edge along 2-4

        wcon = wxx24 + wyy24 + wzz24
        if (edge5 <= nedgeloc) then
          dft1(edge5) = dft1(edge5) + wcon
          dft2(edge5) = dft2(edge5) + phi*wcon
        end if

!       For edge along 3-4

        wcon = wxx34 + wyy34 + wzz34
        if (edge6 <= nedgeloc) then
          dft1(edge6) = dft1(edge6) + wcon
          dft2(edge6) = dft2(edge6) + phi*wcon
        end if

      end do cell_loop

  end subroutine edget

!============================ TURBMULTIEQN_GRAD ==============================80
!
! Calculates the gradients of turbulent variables in turb() and puts the
! result in the last n_turb locations of gradx (y,z similar)
! n_grd should = n_tot + n_turb
! Forms derivatives of primitive quantities
! (for non-conserved turbulence eqns it finds the gradients of the variables)
! (for conserved turbulence eqns it finds the gradients of the variables/rho)
!
!=============================================================================80
  subroutine turbmultieqn_grad(nnodes0,nnodes01,nedgeloc,nedgeloc_2d,          &
                               nnodes0_2d,node_pairs_2d,eptr,turb,x,y,z,xn,yn, &
                               zn,ra,vol,gradx,grady,gradz,nbound,bc,n_turb,   &
                             n_grd,nelem,elem,n_tot,qnode,turbulence_model_int )

    use bc_types,        only : bcgrid_type
    use kinddefs,        only : dp
    use info_depr,       only : twod
    use bc_names,        only : bc_ignore_2d, bc_is_periodic
    use element_types,   only : elem_type
    use periodics,       only : nperiodic, periodic_data, periodic
    use fun3d_constants, only : my_1, my_half, my_0
    use turbulence_info, only : turbulence_model_is_conservative

    integer, intent(in) :: n_tot, n_turb, n_grd, nedgeloc_2d, nnodes0_2d, nelem
    integer, intent(in) :: nnodes0, nnodes01, nedgeloc, nbound

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(dp),  dimension(n_turb,nnodes01),intent(in)  :: turb
    real(dp),  dimension(nnodes01),       intent(in)  :: x, y, z
    real(dp),  dimension(nedgeloc),       intent(in)  :: xn, yn, zn, ra
    real(dp),  dimension(nnodes01),       intent(in)  :: vol
    real(dp),  dimension(n_grd,nnodes01), intent(out) :: gradx,grady,gradz

    type(bcgrid_type), dimension(nbound), intent(in) :: bc
    type(elem_type),   dimension(nelem),  intent(in) :: elem
    real(dp), dimension(n_tot,nnodes01),  intent(in) :: qnode
    integer,                              intent(in) :: turbulence_model_int

    integer :: i,ib,n,m,node1,node2,j,node,set_number
    integer :: t_eqn

    integer, dimension(nnodes0) :: periodic_tag
    real(dp), dimension(n_turb) :: gx, gy, gz

    real(dp) :: area,q1,xnorm,ynorm,znorm,xvol,volume_sum

  continue

    q1 = my_0

! zero out the gradients

    do n = 1,nnodes01
      do m = 1, n_turb
        t_eqn = n_grd - n_turb + m
        gradx( t_eqn, n ) = my_0
        grady( t_eqn, n ) = my_0
        gradz( t_eqn, n ) = my_0
      end do
    end do

    twod_mode : if (twod) then

      edge_loop_2d : do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area  = ra(n)
        xnorm = area * xn(n)
        znorm = area * zn(n)

        do m = 1, n_turb
          t_eqn = n_grd - n_turb + m
          if ( turbulence_model_is_conservative(turbulence_model_int) ) then
            q1 = my_half * ( turb(m,node2)/qnode(1,node2)                      &
                         +   turb(m,node1)/qnode(1,node1) )
          else
            q1 = my_half * (turb(m,node2)+turb(m,node1))
          end if

          if (node1 <= nnodes0) then
            gradx(t_eqn,node1) = gradx(t_eqn,node1) + xnorm*q1
            gradz(t_eqn,node1) = gradz(t_eqn,node1) + znorm*q1
          end if

          if (node2 <= nnodes0) then
            gradx(t_eqn,node2) = gradx(t_eqn,node2) - xnorm*q1
            gradz(t_eqn,node2) = gradz(t_eqn,node2) - znorm*q1
          end if

        enddo

      end do edge_loop_2d

    else twod_mode

      edge_loop : do n = 1, nedgeloc
        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area = ra(n)
        xnorm = area * xn(n)
        ynorm = area * yn(n)
        znorm = area * zn(n)

        equation:  do m = 1, n_turb
          t_eqn = n_grd-n_turb+m
          if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
            q1 = my_half * ( turb(m,node2)/qnode(1,node2)                    &
                           + turb(m,node1)/qnode(1,node1) )
          else
            q1 = my_half * (turb(m,node2)+turb(m,node1))
          end if

        if (node1 <= nnodes0) then
          gradx(t_eqn,node1) = gradx(t_eqn,node1) + xnorm*q1
          grady(t_eqn,node1) = grady(t_eqn,node1) + ynorm*q1
          gradz(t_eqn,node1) = gradz(t_eqn,node1) + znorm*q1
        end if

        if (node2 <= nnodes0) then
          gradx(t_eqn,node2) = gradx(t_eqn,node2) - xnorm*q1
          grady(t_eqn,node2) = grady(t_eqn,node2) - ynorm*q1
          gradz(t_eqn,node2) = gradz(t_eqn,node2) - znorm*q1
        end if

      end do equation
      end do edge_loop

    endif twod_mode

    do ib = 1,nbound

      if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle
      if (bc_is_periodic(bc(ib)%ibc)) cycle

      call bc_turbmultieqn_grad(nnodes0,nnodes01,qnode,turb,x,y,z,gradx,grady, &
                                gradz,bc(ib)%nbnode,bc(ib)%ibnode,             &
                                bc(ib)%nbfacet,bc(ib)%f2ntb,bc(ib)%nbfaceq,    &
                                bc(ib)%f2nqb, nelem, elem, n_tot, n_turb,      &
                                n_grd, turbulence_model_int)

    end do

! correct gradients for symmetry conditions

    do ib = 1,nbound
      call turbmultieqn_grad_symmetry(bc(ib)%ibc, bc(ib)%nbnode, bc(ib)%ibnode,&
                                      nnodes0, nnodes01, n_grd, n_turb,        &
                                      gradx, grady, gradz)
    end do

    if ( periodic ) then

! Combine the contributions from nodes in the periodic sets
! Also set up a tag we'll need later

      periodic_tag = 0

      do i = 1, nperiodic

        gx(:) = 0.0_dp
        gy(:) = 0.0_dp
        gz(:) = 0.0_dp

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          do m = 1, n_turb
            t_eqn = n_grd - n_turb + m
            gx(m) = gx(m) + gradx(t_eqn,node)
            gy(m) = gy(m) + grady(t_eqn,node)
            gz(m) = gz(m) + gradz(t_eqn,node)
          end do
        end do

        do j = 1, periodic_data(i)%n
          node = periodic_data(i)%list(j)
          do m = 1, n_turb
            t_eqn = n_grd - n_turb + m
            gradx(t_eqn,node) = gx(m)
            grady(t_eqn,node) = gy(m)
            gradz(t_eqn,node) = gz(m)
          end do
          if ( j == 1 ) then
            periodic_tag(node) = i
          else
            periodic_tag(node) = -i
          endif
        end do

      end do

    endif

    if (twod) then

      do i = 1,nnodes0_2d

        node1 = node_pairs_2d(1,i)
        node2 = node_pairs_2d(2,i)

        xvol = my_1/vol(node1)
        do m = 1, n_turb
          t_eqn = n_grd - n_turb + m
        gradx(t_eqn,node1) = gradx(t_eqn,node1)*xvol
        gradz(t_eqn,node1) = gradz(t_eqn,node1)*xvol

        gradx(t_eqn,node2) = gradx(t_eqn,node1)
        gradz(t_eqn,node2) = gradz(t_eqn,node1)

      end do
      end do

    else

      if ( .not. periodic ) then

        do i = 1,nnodes0
          xvol = my_1/vol(i)
          do m = 1, n_turb
            t_eqn = n_grd - n_turb + m
          gradx(t_eqn,i) = gradx(t_eqn,i)*xvol
          grady(t_eqn,i) = grady(t_eqn,i)*xvol
          gradz(t_eqn,i) = gradz(t_eqn,i)*xvol
        end do
        end do

      else

        do i = 1,nnodes0
          if ( periodic_tag(i) == 0 ) then

            xvol = my_1/vol(i)
            do m = 1, n_turb
            t_eqn = n_grd - n_turb + m
            gradx(t_eqn,i) = gradx(t_eqn,i)*xvol
            grady(t_eqn,i) = grady(t_eqn,i)*xvol
            gradz(t_eqn,i) = gradz(t_eqn,i)*xvol
            enddo

          else if ( periodic_tag(i) > 0 ) then

! Only do periodic nodes when we hit the primary nodes (don't double bookkeep)

            set_number = periodic_tag(i)
            node1 = periodic_data(set_number)%list(1)
            volume_sum = vol(node1)
            do j = 2, periodic_data(set_number)%n
              node2 = periodic_data(set_number)%list(j)
              volume_sum = volume_sum + vol(node2)
            end do

            xvol = 1._dp/volume_sum

            do j = 1, periodic_data(set_number)%n
              node = periodic_data(set_number)%list(j)
              do m = 1, n_turb
                t_eqn = n_grd - n_turb + m
              gradx(t_eqn,node) = gradx(t_eqn,node)*xvol
              grady(t_eqn,node) = grady(t_eqn,node)*xvol
              gradz(t_eqn,node) = gradz(t_eqn,node)*xvol
              enddo
            end do

          endif
        end do

      endif

    endif

  end subroutine turbmultieqn_grad


!=========================== BC_TURBMULTIEQN_GRAD ============================80
!
! This routine closes off the boundaries for the turb multi-eqn gradient routine
!
!=============================================================================80
  subroutine bc_turbmultieqn_grad(nnodes0,nnodes01,qnode,turb,x,y,z            &
                                , gradx,grady,gradz                            &
                                , nbnode,ibnode,nbfacet,f2ntb,nbfaceq,f2nqb    &
                                , nelem , elem,n_tot, n_turb,n_grd             &
                                , turbulence_model_int )

    use kinddefs,        only : dp
    use info_depr,       only : twod
    use element_types,   only : elem_type
    use twod_util,       only : yplane_2d, y_coplanar_tol
    use grid_metrics,    only : dual_area_quad
    use lmpi,            only : lmpi_die
    use turbulence_info, only : turbulence_model_is_conservative
    use nml_noninertial_reference_frame, only : noninertial

    integer, intent(in) :: n_tot,nnodes0,nnodes01,nbfacet,nbnode,n_turb,n_grd
    integer, intent(in) :: nbfaceq, nelem

    integer, dimension(nbnode),    intent(in) :: ibnode
    integer, dimension(nbfacet,5), intent(in) :: f2ntb
    integer, dimension(nbfaceq,6), intent(in) :: f2nqb

    real(dp), dimension(nnodes01),        intent(in)    :: x,y,z
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(inout) :: gradx,grady,gradz
    integer,                              intent(in) :: turbulence_model_int

    integer :: n, m, t_eqn, ielem, node1, node2, node3, node4, neighbor

    real(dp) :: ax,ay,az,bx,by,bz,q1,k1,k2,k3,k4
    real(dp) :: x1,x2,x3,xnorm,y1,y2,y3,ynorm,z1,z2,z3,znorm
    real(dp) :: c68, c18, c56, c16

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_5   = 5.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
    real(dp), parameter :: my_8   = 8.0_dp
    real(dp), parameter :: my_haf = 0.5_dp

    type(elem_type), dimension(nelem), intent(in) :: elem

  continue

    q1 = my_0
    k1 = my_0
    k2 = my_0
    k3 = my_0
    k4 = my_0

    equation_loop:  do m = 1, n_turb
      t_eqn = n_grd - n_turb + m

      twod_mode : if (twod) then

        c68 = 1.0_dp
        c18 = 0.0_dp

!     close off gradient evaluation on triangular faces
!    (should never get here in 2D...)

        loop_tris_1 : do n = 1, nbfacet

          write(*,*) 'bc_turbmultieqn_grad: should not have hit triangular'
          write(*,*) 'boundary faces!'
          call lmpi_die

        end do loop_tris_1

!     close off gradient evaluation on quadralateral faces

        loop_quads_1 : do n = 1, nbfaceq

        ielem = f2nqb(n,6)

        if (elem(ielem)%type_cell == 'prz' .and. twod) then
          c56 = my_5/my_6
        else
          c56 = my_1
        endif
        c16 = my_1 - c56

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        if (node1 <= nnodes0) then

          xnorm = xnorm_q(1)
          znorm = znorm_q(1)

!         find neighbor of node 1

          neighbor = node2
          if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = node3
          if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = node4

          if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
            q1 = c56 * turb(m,node1)    / qnode(1,node1)                      &
               + c16 * turb(m,neighbor) / qnode(1,neighbor)
          else
            q1 = c56*turb(m,node1) + c16*turb(m,neighbor)
          end if

          gradx(t_eqn,node1) = gradx(t_eqn,node1) + xnorm*q1
          gradz(t_eqn,node1) = gradz(t_eqn,node1) + znorm*q1
        end if

        if (node2 <= nnodes0) then

          xnorm = xnorm_q(2)
          znorm = znorm_q(2)

!         find neighbor of node 2

          neighbor = node3
          if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = node1
          if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = node4

          if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
            q1 = c56 * turb(m,node2)    / qnode(1,node2)                      &
               + c16 * turb(m,neighbor) / qnode(1,neighbor)
          else
            q1 = c56*turb(m,node2) + c16*turb(m,neighbor)
          end if

          gradx(t_eqn,node2) = gradx(t_eqn,node2) + xnorm*q1
          gradz(t_eqn,node2) = gradz(t_eqn,node2) + znorm*q1
        end if

        if (node3 <= nnodes0) then

          xnorm = xnorm_q(3)
          znorm = znorm_q(3)

!         find neighbor of node 3

          neighbor = node4
          if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = node1
          if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = node2
          if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
            q1 = c56 * turb(m,node3)    / qnode(1,node3)                      &
               + c16 * turb(m,neighbor) / qnode(1,neighbor)
          else
            q1 = c56*turb(m,node3) + c16*turb(m,neighbor)
          end if
          gradx(t_eqn,node3) = gradx(t_eqn,node3) + xnorm*q1
          gradz(t_eqn,node3) = gradz(t_eqn,node3) + znorm*q1
        end if

        if (node4 <= nnodes0) then

          xnorm = xnorm_q(4)
          znorm = znorm_q(4)

!         find neighbor of node 4

          neighbor = node1
          if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = node2
          if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = node3

          if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
            q1 = c56 * turb(m,node4)    / qnode(1,node4)                      &
               + c16 * turb(m,neighbor) / qnode(1,neighbor)
          else
            q1 = c56*turb(m,node4) + c16*turb(m,neighbor)
          end if

          gradx(t_eqn,node4) = gradx(t_eqn,node4) + xnorm*q1
          gradz(t_eqn,node4) = gradz(t_eqn,node4) + znorm*q1
        end if

      end do loop_quads_1

    else twod_mode

!     close off gradient evaluation on triangular faces

      loop_tris: do n = 1, nbfacet

        ielem = f2ntb(n,5)  ! index to type of element attached to this face

        c68 = my_1
        c18 = my_0

        if (elem(ielem)%type_cell == 'tet') then
!         for linear function preservation during flux closure - tets only
          c68 = my_6/my_8
          c18 = my_1/my_8
        end if

        node1 = ibnode(f2ntb(n,1))
        node2 = ibnode(f2ntb(n,2))
        node3 = ibnode(f2ntb(n,3))

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)

        if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
          k1 = turb(m,node1) / qnode(1,node1)
          k2 = turb(m,node2) / qnode(1,node2)
          k3 = turb(m,node3) / qnode(1,node3)
        else
          k1 = turb(m,node1)
          k2 = turb(m,node2)
          k3 = turb(m,node3)
        end if

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

!           norm points away from grid interior
!           norm magnitude is 1/3 of surface triangle area

        xnorm = -my_haf*(ay*bz-by*az)/my_3
        ynorm =  my_haf*(ax*bz-bx*az)/my_3
        znorm = -my_haf*(ax*by-bx*ay)/my_3

        q1 = c68*k1 + c18*(k2+k3)

        if (node1 <= nnodes0) then
          gradx(t_eqn,node1) = gradx(t_eqn,node1) + xnorm*q1
          grady(t_eqn,node1) = grady(t_eqn,node1) + ynorm*q1
          gradz(t_eqn,node1) = gradz(t_eqn,node1) + znorm*q1
        end if

        q1 = c68*k2 + c18*(k1+k3)

        if (node2 <= nnodes0) then
          gradx(t_eqn,node2) = gradx(t_eqn,node2) + xnorm*q1
          grady(t_eqn,node2) = grady(t_eqn,node2) + ynorm*q1
          gradz(t_eqn,node2) = gradz(t_eqn,node2) + znorm*q1
        end if

        q1 = c68*k3 + c18*(k1+k2)

        if (node3 <= nnodes0) then
          gradx(t_eqn,node3) = gradx(t_eqn,node3) + xnorm*q1
          grady(t_eqn,node3) = grady(t_eqn,node3) + ynorm*q1
          gradz(t_eqn,node3) = gradz(t_eqn,node3) + znorm*q1
        end if
      end do loop_tris

!     close off gradient evaluation on quadralateral faces

      c68 = my_1
      c18 = my_0

      loop_quads_2 : do n = 1, nbfaceq

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

        if ( turbulence_model_is_conservative ( turbulence_model_int ) ) then
          k1 = turb(m,node1) / qnode(1,node1)
          k2 = turb(m,node2) / qnode(1,node2)
          k3 = turb(m,node3) / qnode(1,node3)
          k4 = turb(m,node4) / qnode(1,node4)
        else
          k1 = turb(m,node1)
          k2 = turb(m,node2)
          k3 = turb(m,node3)
          k4 = turb(m,node4)
        end if

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        q1 = k1

        xnorm = xnorm_q(1)
        ynorm = ynorm_q(1)
        znorm = znorm_q(1)

        if (node1 <= nnodes0) then
          gradx(t_eqn,node1) = gradx(t_eqn,node1) + xnorm*q1
          grady(t_eqn,node1) = grady(t_eqn,node1) + ynorm*q1
          gradz(t_eqn,node1) = gradz(t_eqn,node1) + znorm*q1
        end if

        q1 = k2

        xnorm = xnorm_q(2)
        ynorm = ynorm_q(2)
        znorm = znorm_q(2)

        if (node2 <= nnodes0) then
          gradx(t_eqn,node2) = gradx(t_eqn,node2) + xnorm*q1
          grady(t_eqn,node2) = grady(t_eqn,node2) + ynorm*q1
          gradz(t_eqn,node2) = gradz(t_eqn,node2) + znorm*q1
        end if

        q1 = k3

        xnorm = xnorm_q(3)
        ynorm = ynorm_q(3)
        znorm = znorm_q(3)

        if (node3 <= nnodes0) then
          gradx(t_eqn,node3) = gradx(t_eqn,node3) + xnorm*q1
          grady(t_eqn,node3) = grady(t_eqn,node3) + ynorm*q1
          gradz(t_eqn,node3) = gradz(t_eqn,node3) + znorm*q1
        end if

        q1 = k4

        xnorm = xnorm_q(4)
        ynorm = ynorm_q(4)
        znorm = znorm_q(4)

        if (node4 <= nnodes0) then
          gradx(t_eqn,node4) = gradx(t_eqn,node4) + xnorm*q1
          grady(t_eqn,node4) = grady(t_eqn,node4) + ynorm*q1
          gradz(t_eqn,node4) = gradz(t_eqn,node4) + znorm*q1
        end if

      end do loop_quads_2

    end if twod_mode

    end do equation_loop

  end subroutine bc_turbmultieqn_grad

!========================= TURBMULTIEQN_GRAD_SYMMETRY ========================80
!
! Set known gradients at symmetry planes.
! For example, at symmetry_y:
!   y-gradients of k,w,E are zero, e.g. k_y = 0.
!
!=============================================================================80
  subroutine turbmultieqn_grad_symmetry(ibc, nbnode, ibnode, nnodes0, nnodes01,&
                                        n_grd, n_turb, gradx, grady, gradz)

    use kinddefs,  only : dp
    use bc_names,  only : symmetry_x, symmetry_y, symmetry_z

    integer, intent(in) :: ibc, nbnode, nnodes0, nnodes01, n_grd, n_turb

    integer, dimension(nbnode), intent(in) :: ibnode

    real(dp), dimension(n_grd,nnodes01),intent(inout) :: gradx, grady, gradz

    integer :: n, m, node, t_eqn

    real(dp), parameter :: my_0 = 0.0_dp

  continue

    ! Enforce zero gradient at symmetry planes for 7-eqn models like
    ! WilcoxRSM_w2006, SSGLRR_RSM_w2012 (note gradient is NOT zero for two
    ! of the Reynolds stresses, depending on which plane of symmetry it is;
    ! those two stresses require dirichlet BCs instead - enforced elsewhere)
    if ( n_turb == 7 ) then

        select case (ibc)

          case (symmetry_x)

            do n = 1,nbnode
              node = ibnode(n)
              if(node <= nnodes0) then
                gradx(n_grd-n_turb+1,node) = my_0 ! t_xx
                gradx(n_grd-n_turb+2,node) = my_0 ! t_yy
                gradx(n_grd-n_turb+3,node) = my_0 ! t_zz
                gradx(n_grd-n_turb+6,node) = my_0 ! t_yz
                gradx(n_grd-n_turb+7,node) = my_0 ! omega
              end if
            end do

          case (symmetry_y)

            do n = 1,nbnode
              node = ibnode(n)
              if(node <= nnodes0) then
                grady(n_grd-n_turb+1,node) = my_0 ! t_xx
                grady(n_grd-n_turb+2,node) = my_0 ! t_yy
                grady(n_grd-n_turb+3,node) = my_0 ! t_zz
                grady(n_grd-n_turb+5,node) = my_0 ! t_xz
                grady(n_grd-n_turb+7,node) = my_0 ! omega
              end if
            end do

          case (symmetry_z)

            do n = 1,nbnode
              node = ibnode(n)
              if(node <= nnodes0) then
                gradz(n_grd-n_turb+1,node) = my_0 ! t_xx
                gradz(n_grd-n_turb+2,node) = my_0 ! t_yy
                gradz(n_grd-n_turb+3,node) = my_0 ! t_zz
                gradz(n_grd-n_turb+4,node) = my_0 ! t_xy
                gradz(n_grd-n_turb+7,node) = my_0 ! omega
              end if
            end do

        end select

    else

      equation:  do m = 1, n_turb
        t_eqn = n_grd - n_turb + m
        select case (ibc)

          case (symmetry_x)

            do n = 1,nbnode
              node = ibnode(n)
              if(node <= nnodes0) then
                gradx(t_eqn,node) = my_0
              end if
            end do

          case (symmetry_y)

            do n = 1,nbnode
              node = ibnode(n)
              if(node <= nnodes0) then
                grady(t_eqn,node) = my_0
              end if
            end do

          case (symmetry_z)

            do n = 1,nbnode
              node = ibnode(n)
              if(node <= nnodes0) then
                gradz(t_eqn,node) = my_0
              end if
            end do

        end select
      end do equation
    end if

  end subroutine turbmultieqn_grad_symmetry

!============================= SET_SLEN_WALL_NONZERO =========================80
!
! Overwrite slen values at wall.
!
!=============================================================================80

  subroutine set_slen_wall_nonzero( nbound, bc, slen )

    use kinddefs, only : dp
    use bc_types, only : bcgrid_type
    use bc_names, only : bc_used_for_distance_function
    use lmpi_app, only : lmpi_xfer

    integer, intent(in) :: nbound
    type(bcgrid_type), dimension(nbound),  intent(in) :: bc
    real(dp), dimension(:), intent(inout) :: slen

    integer :: j, ibound, inode

    continue

    do ibound = 1,nbound
      if ( .not.bc_used_for_distance_function(bc(ibound)%ibc) ) cycle
      do j = 1, bc(ibound)%nbnode
        inode = bc(ibound)%ibnode(j)
        slen(inode) = bc(ibound)%slen_wall( j )
      enddo
    enddo

! Ghost nodes may lie on a viscous surface.  However, these points may
! not be included in the bc%ibnode list, so the above loop will not treat
! such points correctly.  Therefore, we must transfer slen across processors.

    call lmpi_xfer(slen)

  end subroutine set_slen_wall_nonzero

!============================= SET_SLEN_WALL_ZERO ============================80
!
! Overwrite slen values at wall.
!
!=============================================================================80

  subroutine set_slen_wall_zero( nbound, bc, slen )

    use kinddefs, only : dp
    use bc_types, only : bcgrid_type
    use bc_names, only : bc_used_for_distance_function
    use lmpi_app, only : lmpi_xfer

    integer, intent(in) :: nbound
    type(bcgrid_type), dimension(nbound),  intent(in) :: bc
    real(dp), dimension(:), intent(inout) :: slen

    integer :: j, ibound, inode

    continue

    do ibound = 1,nbound
      if ( .not.bc_used_for_distance_function(bc(ibound)%ibc) ) cycle
      do j = 1, bc(ibound)%nbnode
        inode = bc(ibound)%ibnode(j)
        slen(inode) = 0._dp
      enddo
    enddo

! Ghost nodes may lie on a viscous surface.  However, these points may
! not be included in the bc%ibnode list, so the above loop will not treat
! such points correctly.  Therefore, we must transfer slen across processors.

    call lmpi_xfer(slen)

  end subroutine set_slen_wall_zero

!==========================STRAIN_TENSOR_DERIV ===============================80
!
! For SARC only, need to get D(Sij)/Dt terms (ignoring time deriv for now)
! = u_k*d(Sij)/dx_k (summing over k)
!
!=============================================================================80
  subroutine strain_tensor_deriv(eqn_set,                                      &
                   nnodes0, nnodes01, nedgeloc, eptr, qnode,                   &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,        &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,                 &
                   s11deriv, s12deriv, s13deriv, s22deriv, s23deriv, s33deriv)

    use element_types, only : elem_type
    use info_depr,     only : twod
    use bc_types,      only : bcgrid_type

    integer, intent(in) :: nnodes0, nnodes01, n_tot, n_grd
    integer, intent(in) :: eqn_set, nedgeloc, nelem, nedgeloc_2d
    integer, intent(in) :: nnodes0_2d, nbound

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(dp), dimension(n_tot,nnodes01), intent(in) :: qnode
    real(dp), dimension(nnodes01),       intent(in) :: x, y, z, vol
    real(dp), dimension(nedgeloc),       intent(in) :: xn, yn, zn, ra
    real(dp), dimension(n_grd,nnodes01), intent(in) :: gradx,grady,gradz
    real(dp), dimension(nnodes01),       intent(in) :: dxdt, dydt, dzdt

    real(dp), dimension(nnodes01), intent(out) :: s11deriv, s12deriv
    real(dp), dimension(nnodes01), intent(out) :: s13deriv, s22deriv
    real(dp), dimension(nnodes01), intent(out) :: s23deriv, s33deriv

    real(dp), dimension(:,:), allocatable, save :: dxgradx
    real(dp), dimension(:,:), allocatable, save :: dxgrady
    real(dp), dimension(:,:), allocatable, save :: dxgradz
    real(dp), dimension(:,:), allocatable, save :: dygradx
    real(dp), dimension(:,:), allocatable, save :: dygrady
    real(dp), dimension(:,:), allocatable, save :: dygradz
    real(dp), dimension(:,:), allocatable, save :: dzgradx
    real(dp), dimension(:,:), allocatable, save :: dzgrady
    real(dp), dimension(:,:), allocatable, save :: dzgradz

    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc

    integer :: i,node_src_eval,ii

    type(elem_type), dimension(nelem), intent(in) :: elem

    logical, save :: initialized = .false.

  continue

    if ( .not. initialized ) then
      allocate (dxgradx(n_grd,nnodes01))
      allocate (dxgrady(n_grd,nnodes01))
      allocate (dxgradz(n_grd,nnodes01))
      allocate (dygradx(n_grd,nnodes01))
      allocate (dygrady(n_grd,nnodes01))
      allocate (dygradz(n_grd,nnodes01))
      allocate (dzgradx(n_grd,nnodes01))
      allocate (dzgrady(n_grd,nnodes01))
      allocate (dzgradz(n_grd,nnodes01))
      initialized = .true.
    endif

    s11deriv(:)=0._dp
    s22deriv(:)=0._dp
    s33deriv(:)=0._dp
    s12deriv(:)=0._dp
    s13deriv(:)=0._dp
    s23deriv(:)=0._dp

!   compute gradients of the gradients
    call turbgrad(eqn_set,                                                     &
                      nnodes0, nnodes01, nedgeloc, eptr, gradx,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, dxgradx, dygradx, dzgradx,          &
                      nbound, bc,                                              &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

    call turbgrad(eqn_set,                                                     &
                      nnodes0, nnodes01, nedgeloc, eptr, grady,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, dxgrady, dygrady, dzgrady,          &
                      nbound, bc,                                              &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

    call turbgrad(eqn_set,                                                     &
                      nnodes0, nnodes01, nedgeloc, eptr, gradz,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, dxgradz, dygradz, dzgradz,          &
                      nbound, bc,                                              &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

!   assemble everything to get u_k*DSij/Dt (ignoring time derivative for now)

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

        s11deriv(i) = qnode(2,i)*dxgradx(2,i) + qnode(3,i)*dygradx(2,i) +      &
                      qnode(4,i)*dzgradx(2,i)
        s22deriv(i) = qnode(2,i)*dxgrady(3,i) + qnode(3,i)*dygrady(3,i) +      &
                      qnode(4,i)*dzgrady(3,i)
        s33deriv(i) = qnode(2,i)*dxgradz(4,i) + qnode(3,i)*dygradz(4,i) +      &
                      qnode(4,i)*dzgradz(4,i)
        s12deriv(i) = 0.5_dp*(qnode(2,i)*dxgrady(2,i) + qnode(3,i)*dygrady(2,i)&
                             +qnode(4,i)*dzgrady(2,i)) +                       &
                      0.5_dp*(qnode(2,i)*dxgradx(3,i) + qnode(3,i)*dygradx(3,i)&
                             +qnode(4,i)*dzgradx(3,i))
        s13deriv(i) = 0.5_dp*(qnode(2,i)*dxgradz(2,i) + qnode(3,i)*dygradz(2,i)&
                             +qnode(4,i)*dzgradz(2,i)) +                       &
                      0.5_dp*(qnode(2,i)*dxgradx(4,i) + qnode(3,i)*dygradx(4,i)&
                             +qnode(4,i)*dzgradx(4,i))
        s23deriv(i) = 0.5_dp*(qnode(2,i)*dxgradz(3,i) + qnode(3,i)*dygradz(3,i)&
                             +qnode(4,i)*dzgradz(3,i)) +                       &
                      0.5_dp*(qnode(2,i)*dxgrady(4,i) + qnode(3,i)*dygrady(4,i)&
                             +qnode(4,i)*dzgrady(4,i))

    enddo

  end subroutine strain_tensor_deriv

!======================= VELOCITY_GRADIENT_DERIVATIVE ========================80
!
! magnitude of summation of second derivatives of velocity
!
!=============================================================================80
  subroutine velocity_gradient_derivative(eqn_set,                             &
                   nnodes0, nnodes01, nedgeloc, eptr,                          &
                   x, y, z, gradx, grady, gradz,                               &
                   xn, yn, zn, ra, vol,                                        &
                   nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,        &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc,                 &
                   u_double_prime )

    use element_types, only : elem_type
    use info_depr,     only : twod
    use bc_types,      only : bcgrid_type

    integer, intent(in) :: nnodes0, nnodes01, n_tot, n_grd
    integer, intent(in) :: eqn_set, nedgeloc, nelem, nedgeloc_2d
    integer, intent(in) :: nnodes0_2d, nbound

    integer, dimension(2,nedgeloc),   intent(in) :: eptr
    integer, dimension(2,nnodes0_2d), intent(in) :: node_pairs_2d

    real(dp), dimension(nnodes01),       intent(in) :: x, y, z, vol
    real(dp), dimension(nedgeloc),       intent(in) :: xn, yn, zn, ra
    real(dp), dimension(n_grd,nnodes01), intent(in) :: gradx,grady,gradz
    real(dp), dimension(nnodes01),       intent(in) :: dxdt, dydt, dzdt

    real(dp), dimension(nnodes01), intent(out) :: u_double_prime

    real(dp), dimension(:,:), allocatable, save :: dxgradx
    real(dp), dimension(:,:), allocatable, save :: dxgrady
    real(dp), dimension(:,:), allocatable, save :: dxgradz
    real(dp), dimension(:,:), allocatable, save :: dygradx
    real(dp), dimension(:,:), allocatable, save :: dygrady
    real(dp), dimension(:,:), allocatable, save :: dygradz
    real(dp), dimension(:,:), allocatable, save :: dzgradx
    real(dp), dimension(:,:), allocatable, save :: dzgrady
    real(dp), dimension(:,:), allocatable, save :: dzgradz

    type(bcgrid_type), dimension(nbound),  intent(in)  :: bc

    integer :: i,node_src_eval,ii

    type(elem_type), dimension(nelem), intent(in) :: elem

    logical, save :: initialized = .false.

  continue

    if ( .not. initialized ) then
      allocate (dxgradx(n_grd,nnodes01))
      allocate (dxgrady(n_grd,nnodes01))
      allocate (dxgradz(n_grd,nnodes01))
      allocate (dygradx(n_grd,nnodes01))
      allocate (dygrady(n_grd,nnodes01))
      allocate (dygradz(n_grd,nnodes01))
      allocate (dzgradx(n_grd,nnodes01))
      allocate (dzgrady(n_grd,nnodes01))
      allocate (dzgradz(n_grd,nnodes01))
      initialized = .true.
    endif

    u_double_prime(:)=0._dp
! used in flux_turb.f90,
! input: (q(n_tot,nnodes01) -> (u,v,w)
! gradx: d/dx u, d/dx v, d/dx w
! grady: d/dy u, d/dy v, d/dy w
! gradz: d/dz u, d/dz v, d/dz w

! source_kkl (gradx(n_grd,nnodes01)) -> (d/dx u,d/dx v,d/dx w)
! dxgradx: d/dx d/dx u, d/dx d/dx v, d/dx d/dx w
! dygradx: d/dy d/dx u, d/dy d/dx v, d/dy d/dx w
! dzgradx: d/dz d/dx u, d/dz d/dx v, d/dz d/dx w

! source_kkl (grady(n_grd,nnodes01)) -> (d/dy u,d/dy v,d/dy w)
! dxgrady: d/dx d/dy u, d/dx d/dy v, d/dx d/dy w
! dygrady: d/dy d/dy u, d/dy d/dy v, d/dy d/dy w
! dzgrady: d/dz d/dy u, d/dz d/dy v, d/dz d/dy w

! source_kkl (gradz(n_grd,nnodes01)) -> (d/dz u,d/dz v,d/dz w)
! dxgradz: d/dx d/dz u, d/dx d/dz v, d/dx d/dz w
! dygradz: d/dy d/dz u, d/dy d/dz v, d/dy d/dz w
! dzgradz: d/dz d/dz u, d/dz d/dz v, d/dz d/dz w

!   compute gradients of the gradients
    call turbgrad(eqn_set,                                                     &
                      nnodes0, nnodes01, nedgeloc, eptr, gradx,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, dxgradx, dygradx, dzgradx,          &
                      nbound, bc,                                              &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

    call turbgrad(eqn_set,                                                     &
                      nnodes0, nnodes01, nedgeloc, eptr, grady,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, dxgrady, dygrady, dzgrady,          &
                      nbound, bc,                                              &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

    call turbgrad(eqn_set,                                                     &
                      nnodes0, nnodes01, nedgeloc, eptr, gradz,                &
                      x, y, z, dxdt, dydt, dzdt,                               &
                      xn, yn, zn, ra, vol, dxgradz, dygradz, dzgradz,          &
                      nbound, bc,                                              &
                      nedgeloc_2d, node_pairs_2d,nnodes0_2d, nelem, elem,      &
                      n_tot, n_grd)

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    do ii = 1, node_src_eval

        if (twod) then
          i = node_pairs_2d(1,ii)
        else
          i = ii
        end if

        u_double_prime(i) = sqrt( (dxgradx(2,i)*dxgradx(2,i)+                  &
                                   dygrady(2,i)*dygrady(2,i)+                  &
                                   dzgradz(2,i)*dzgradz(2,i))                  &
                                + (dxgradx(3,i)*dxgradx(3,i)+                  &
                                   dygrady(3,i)*dygrady(3,i)+                  &
                                   dzgradz(3,i)*dzgradz(3,i))                  &
                                + (dxgradx(4,i)*dxgradx(4,i)+                  &
                                   dygrady(4,i)*dygrady(4,i)+                  &
                                   dzgradz(4,i)*dzgradz(4,i)) )

    enddo

  end subroutine velocity_gradient_derivative


!=============================================================================80
!
!
!
!=============================================================================80
  pure function get_tau ( sij, mut, rho, tke ) result( tau )

    use kinddefs, only : dp

    real(dp),  dimension(3,3)             :: tau

    real(dp),  dimension(3,3), intent(in) :: sij
    real(dp),                  intent(in) :: mut
    real(dp),                  intent(in) :: rho
    real(dp),                  intent(in) :: tke

    real(dp),  dimension(3,3) :: sijbar
    real(dp),  parameter, dimension(3,3) :: delta = &
               reshape((/1.,0.,0.,0.,1.,0.,0.,0.,1./),(/3,3/))

    real(dp) :: strace
    real(dp), parameter :: onethird    = 1.0_dp/3.0_dp
    real(dp), parameter :: twothird    = 2.0_dp/3.0_dp

  continue

    strace  = sij(1,1) + sij(2,2) + sij(3,3)
    sijbar  = sij - onethird*strace*delta

    tau = ( 2.0_dp * mut * sijbar / rho - twothird*tke*delta )

  end function get_tau

!=============================== GET_WWS =====================================80
!
! quick triple production function
!
!=============================================================================80
  pure function get_wws ( w, s ) result ( wws )

    use kinddefs, only : dp

    real(dp)                             :: wws

    real(dp), dimension(3,3), intent(in) :: w
    real(dp), dimension(3,3), intent(in) :: s

  continue

    wws = w(1,1)*w(1,1)*s(1,1) + w(1,2)*w(2,1)*s(1,1) + w(1,3)*w(3,1)*s(1,1) + &
          w(1,1)*w(1,2)*s(2,1) + w(1,2)*w(2,2)*s(2,1) + w(1,3)*w(3,2)*s(2,1) + &
          w(1,1)*w(1,3)*s(3,1) + w(1,2)*w(2,3)*s(3,1) + w(1,3)*w(3,3)*s(3,1) + &

          w(2,1)*w(1,1)*s(1,2) + w(2,2)*w(2,1)*s(1,2) + w(2,3)*w(3,1)*s(1,2) + &
          w(2,1)*w(1,2)*s(2,2) + w(2,2)*w(2,2)*s(2,2) + w(2,3)*w(3,2)*s(2,2) + &
          w(2,1)*w(1,3)*s(3,2) + w(2,2)*w(2,3)*s(3,2) + w(2,3)*w(3,3)*s(3,2) + &

          w(3,1)*w(1,1)*s(1,3) + w(3,2)*w(2,1)*s(1,3) + w(3,3)*w(3,1)*s(1,3) + &
          w(3,1)*w(1,2)*s(2,3) + w(3,2)*w(2,2)*s(2,3) + w(3,3)*w(3,2)*s(2,3) + &
          w(3,1)*w(1,3)*s(3,3) + w(3,2)*w(2,3)*s(3,3) + w(3,3)*w(3,3)*s(3,3)

  end function get_wws

!================================= DF_MUT ====================================80
!
! Turbulent diffusion term coefficient for Wilcox-kw 2006
!
!=============================================================================80

  pure function df_mut ( i, n_tot, n_turb, nnodes01, eqn_set, qnode, turb ) &
  result ( mut )

    use kinddefs,       only : dp
    use info_depr,      only : tref
    use fluid,          only : gamma, sutherland_constant
    use turb_kw_const,  only : vmaxb
    use solution_types, only : compressible

    real(dp)                                          :: mut

    integer,                               intent(in) :: i
    integer,                               intent(in) :: n_tot
    integer,                               intent(in) :: n_turb
    integer,                               intent(in) :: nnodes01
    integer,                               intent(in) :: eqn_set
    real(dp), dimension(n_tot, nnodes01),  intent(in) :: qnode
    real(dp), dimension(n_turb, nnodes01), intent(in) :: turb

    real(dp) :: cstar, rho, p, temp, omega, mu_lam

    real(dp), parameter :: one     = 1.0_dp
    real(dp), parameter :: my_tiny = tiny(1.0_dp)

  continue

    cstar  = sutherland_constant / tref

    omega = turb(2,i)

    rho   = one
    p     = one
    temp  = gamma

    if ( eqn_set == compressible ) then
      rho  = qnode(1,i)
      p    = qnode(5,i)
      temp = gamma * p / rho
    endif
    if ( abs(omega) < my_tiny ) omega = 1.0e-12_dp

    mut    = rho * turb(1,i) / omega
    mu_lam = viscosity_law(cstar, temp)

    if ( mut > (vmaxb * mu_lam) ) mut = vmaxb * mu_lam

  end function df_mut

!=============================== SET_GRADV ===================================80
!
!  Create 3x3 matrix of velocity gradients for notational convenience
!
!=============================================================================80
  pure function set_gradv ( gradx2i, grady2i, gradz2i                  &
                          , gradx3i, grady3i, gradz3i                  &
                          , gradx4i, grady4i, gradz4i )                &
  result ( gradv )

    use kinddefs, only : dp

    real(dp), dimension(3,3) :: gradv

    real(dp), intent(in) :: gradx2i, grady2i, gradz2i
    real(dp), intent(in) :: gradx3i, grady3i, gradz3i
    real(dp), intent(in) :: gradx4i, grady4i, gradz4i

  continue

    gradv(1,1) = gradx2i
    gradv(1,2) = grady2i
    gradv(1,3) = gradz2i
    gradv(2,1) = gradx3i
    gradv(2,2) = grady3i
    gradv(2,3) = gradz3i
    gradv(3,1) = gradx4i
    gradv(3,2) = grady4i
    gradv(3,3) = gradz4i

  end function set_gradv

!=============================== TURBGRAD_BC_EB ==============================80
!
! Close off gradients of velocity.
!
!=============================================================================80

  subroutine turbgrad_bc_eb( ib, n_q, dof0, q_dof, gradx, grady, gradz,        &
                             x, y, z, dxdt, dydt, dzdt, bc, elem )

    use element_based_bc_util, only : element_based_metrics, element_based_qi
    use generic_gas_map,       only : n_etot

    integer, intent(in) :: ib, n_q, dof0

    real(dp),  dimension(:),   intent(in)    :: x, y, z
    real(dp),  dimension(:),   intent(in)    :: dxdt, dydt, dzdt
    real(dp),  dimension(:,:), intent(in)    :: q_dof
    real(dp),  dimension(:,:), intent(inout) :: gradx, grady, gradz

    type(elem_type),   dimension(:), intent(in) :: elem
    type(bcgrid_type), dimension(:), intent(in) :: bc

    integer                :: triangle_index
    integer                :: triangle_corner
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer                :: quad_index
    integer                :: quad_corner
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight

    integer  :: node1
    real(dp) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(n_q) :: qi

! reference -r52595
!   integer, parameter :: nvalues = 5  !return 1st 5 containing u, v, w
!   n_etot = 5 for compressible gas path

  continue

    !----------Triangular Faces----------

    loop_tris : do triangle_index = 1,bc(ib)%nbfacet

      corner_tris_loop : do triangle_corner = 1,3

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, triangle_index, triangle_corner                               &
           , 3, triangle_node, triangle_weight                                 &
           , xnorm, ynorm, znorm, area, face_speed )

        call element_based_qi( n_etot, qi, q_dof,                              &
                               3, triangle_node, triangle_weight )

        if( triangle_node(1) > dof0 ) cycle

        gradx(1:n_etot,triangle_node(1)) = gradx(1:n_etot,triangle_node(1))    &
                                    + xnorm*area*qi(1:n_etot)
        grady(1:n_etot,triangle_node(1)) = grady(1:n_etot,triangle_node(1))    &
                                    + ynorm*area*qi(1:n_etot)
        gradz(1:n_etot,triangle_node(1)) = gradz(1:n_etot,triangle_node(1))    &
                                    + znorm*area*qi(1:n_etot)
      enddo corner_tris_loop

    enddo loop_tris

    !----------Quadrilateral Faces----------

    loop_quads : do quad_index = 1,bc(ib)%nbfaceq

      corner_quad_loop : do quad_corner = 1, 4

        skip_other_twod_plane : if (twod) then
          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(quad_index,quad_corner))
          if( abs(y(node1)-yplane_2d) >= y_coplanar_tol )                      &
            cycle corner_quad_loop
        end if skip_other_twod_plane

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, quad_index, quad_corner                                       &
           , 4, quad_node, quad_weight                                         &
           , xnorm, ynorm, znorm, area, face_speed )

        call element_based_qi( n_etot, qi, q_dof,                              &
                               4, quad_node, quad_weight )

        if(quad_node(1) > dof0 ) cycle

        gradx(1:n_etot,quad_node(1)) = gradx(1:n_etot,quad_node(1))            &
                                + xnorm*area*qi(1:n_etot)
        grady(1:n_etot,quad_node(1)) = grady(1:n_etot,quad_node(1))            &
                                + ynorm*area*qi(1:n_etot)
        gradz(1:n_etot,quad_node(1)) = gradz(1:n_etot,quad_node(1))            &
                                + znorm*area*qi(1:n_etot)

      enddo corner_quad_loop

    enddo loop_quads

  end subroutine turbgrad_bc_eb

!=============================== TURBGRAD_BC_EB_I ============================80
!
! Close off gradients of velocity.
!
!=============================================================================80

  subroutine turbgrad_bc_eb_i( ib, n_q, dof0, q_dof, gradx, grady, gradz,      &
                             x, y, z, dxdt, dydt, dzdt, bc, elem )

    use element_based_bc_util, only : element_based_metrics, element_based_qi

    integer, intent(in) :: ib, n_q, dof0

    real(dp),  dimension(:),   intent(in)    :: x, y, z
    real(dp),  dimension(:),   intent(in)    :: dxdt, dydt, dzdt
    real(dp),  dimension(:,:), intent(in)    :: q_dof
    real(dp),  dimension(:,:), intent(inout) :: gradx, grady, gradz

    type(elem_type),   dimension(:), intent(in) :: elem
    type(bcgrid_type), dimension(:), intent(in) :: bc

    integer                :: triangle_index
    integer                :: triangle_corner
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer                :: quad_index
    integer                :: quad_corner
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight

    integer  :: node1
    real(dp) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(n_q) :: qi

    integer, parameter :: nvalues = 4  !return 1st 4 containing u, v, w

  continue
    !----------Triangular Faces----------

    loop_tris : do triangle_index = 1,bc(ib)%nbfacet

      corner_tris_loop : do triangle_corner = 1,3

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, triangle_index, triangle_corner                               &
           , 3, triangle_node, triangle_weight                                 &
           , xnorm, ynorm, znorm, area, face_speed )

        call element_based_qi( nvalues, qi, q_dof,               &
                               3, triangle_node, triangle_weight )

        if( triangle_node(1) > dof0 ) cycle

        gradx(1:4,triangle_node(1)) = gradx(1:4,triangle_node(1)) &
                                    + xnorm*area*qi(1:4)
        grady(1:4,triangle_node(1)) = grady(1:4,triangle_node(1)) &
                                    + ynorm*area*qi(1:4)
        gradz(1:4,triangle_node(1)) = gradz(1:4,triangle_node(1)) &
                                    + znorm*area*qi(1:4)
      enddo corner_tris_loop

    enddo loop_tris

    !----------Quadrilateral Faces----------

    loop_quads : do quad_index = 1,bc(ib)%nbfaceq

      corner_quad_loop : do quad_corner = 1, 4

        skip_other_twod_plane : if (twod) then
          node1 = bc(ib)%ibnode(bc(ib)%f2nqb(quad_index,quad_corner))
          if( abs(y(node1)-yplane_2d) >= y_coplanar_tol ) &
            cycle corner_quad_loop
        end if skip_other_twod_plane

        call element_based_metrics ( bc, elem                                  &
           , x, y, z                                                           &
           , dxdt, dydt, dzdt                                                  &
           , ib, quad_index, quad_corner                                       &
           , 4, quad_node, quad_weight                                         &
           , xnorm, ynorm, znorm, area, face_speed )

        call element_based_qi( nvalues, qi, q_dof,  &
                               4, quad_node, quad_weight )

        if(quad_node(1) > dof0 ) cycle

        gradx(1:4,quad_node(1)) = gradx(1:4,quad_node(1)) &
                                + xnorm*area*qi(1:4)
        grady(1:4,quad_node(1)) = grady(1:4,quad_node(1)) &
                                + ynorm*area*qi(1:4)
        gradz(1:4,quad_node(1)) = gradz(1:4,quad_node(1)) &
                                + znorm*area*qi(1:4)

      enddo corner_quad_loop

    enddo loop_quads

  end subroutine turbgrad_bc_eb_i

  include 'viscosity_law.f90'
  include 'sa0_turb_abs.f90'

end module turb_util


! Routines used for driving the partition computations.

module pparty_computes

   use kinddefs, only : dp, system_i1, system_i8
   use lmpi,     only : lmpi_id, lmpi_die
   implicit none

   private

   public :: pparty_color
   public :: pparty_color_cc

   type cell_ind_type
      integer, dimension(:), pointer :: c2n_ind
   end type cell_ind_type

contains

!============================== PPARTY_COLOR =================================80
!
! After partitioning and migrating information, perform the computations
! and write out the partition files.
!
!=============================================================================80

  subroutine pparty_color (grid)

    use info_depr,   only : pp_cmd_color
    use sort,        only : binary_search, heap_sort
    use grid_types,  only : grid_type
    use bc_cache_cc, only : bc_ghost

    type(grid_type), intent(inout) :: grid

    integer :: ibound, i, j, k, i1, i2, ielem, nnodes0, pncell, iold

    integer,  dimension(:),    allocatable :: old_l2g, point_dofs, list, indx
    integer,  dimension(:),    allocatable :: map, itemp
    integer,  dimension(:,:),  allocatable :: temp_c2n
    real(dp), dimension(:),    allocatable :: rtemp

    logical :: use_binary_search

    type(cell_ind_type), dimension(:), allocatable :: cell_ind

  continue

!-------------------------------------------------------------------------------
!
! Complete partitioning (convert global numbers to local numbers).
!   - cl2g, eptr, bc_nodes
!   - These done during sr exchange: sr1, sr2, sre
!
!-------------------------------------------------------------------------------

    if (pp_cmd_color /= 1) then
       write(*,*)'Invalid pp_cmd_color = ',pp_cmd_color
       call lmpi_die
    end if

    nnodes0 = grid%nnodes0

    allocate(old_l2g(size(grid%l2g)))
    old_l2g = grid%l2g

    allocate(point_dofs(nnodes0)); point_dofs = -1

    if (pp_cmd_color == 1) then

       call local_cuthill_mckee_par(grid%nnodes0, grid%nedge, old_l2g,         &
                                    grid%eptr, grid%y, grid%l2g)

       allocate(indx(grid%nnodes0)); indx = 0
       call heap_sort(grid%nnodes0,old_l2g,indx)

       allocate(list(grid%nnodes0)); list = old_l2g(1:grid%nnodes0)
       call heap_sort(grid%nnodes0,list)

       do i = 1,grid%nnodes0
          j = binary_search(grid%nnodes0,list,grid%l2g(i))
          if (j > 0) point_dofs(i) = indx(j)
       end do
       deallocate(list,indx)

    end if

! Remap arrays affected by coloring (l2g, c2n, xyz, vol, sr12e, eptr, ibnode).
! Only level 0 values are mapped.
! The sr12e arrays are mapped during the exchange.

! xyz, vol

    allocate(rtemp(nnodes0)) ! Real arrays

    rtemp = grid%x(1:nnodes0)
    do i = 1,nnodes0
       grid%x(i) = rtemp(point_dofs(i))
    end do

    rtemp = grid%y(1:nnodes0)
    do i = 1,nnodes0
       grid%y(i) = rtemp(point_dofs(i))
    end do

    rtemp = grid%z(1:nnodes0)
    do i = 1,nnodes0
       grid%z(i) = rtemp(point_dofs(i))
    end do

    deallocate(rtemp)
    deallocate(point_dofs)

! Create map from old_local to new_local.

    ! Determine if binary_search can be used.
    use_binary_search = .true.
    do i = 2,nnodes0
       if (old_l2g(i-1) > old_l2g(i)) then
          use_binary_search = .false.
          exit
       end if
    end do

    allocate(map(nnodes0)); map = 0

    if (use_binary_search) then
       do i = 1,nnodes0
          j = binary_search(nnodes0,old_l2g,grid%l2g(i))
          map(j) = i
       end do
    else
       do i = 1,nnodes0
          j = 0
          do k = 1,size(old_l2g)
             if (grid%l2g(i) == old_l2g(k)) then
                j = k; exit
             end if
          end do
          map(j) = i
       end do
   end if
   deallocate(old_l2g)

! c2n

    do ielem = 1,grid%nelem
       i1 = size(grid%elem(ielem)%c2n,1)
       i2 = size(grid%elem(ielem)%c2n,2)
       if (i1 > 0) then
          allocate(temp_c2n(i1,i2)); temp_c2n = grid%elem(ielem)%c2n
          do i = 1,i1
             do j = 1,i2
                k = temp_c2n(i,j)
                ! Only alter level0 nodes
                if ((k >= 1).and.(k <= nnodes0))                               &
                   grid%elem(ielem)%c2n(i,j) = map(k)
            end do
          end do
          deallocate(temp_c2n)
      end if
   end do

! ibnode

   do ibound = 1,grid%nbound
      do j = 1,grid%bc(ibound)%nbnode
         k = grid%bc(ibound)%ibnode(j)
         if ((k >= 1).and.(k <= nnodes0)) grid%bc(ibound)%ibnode(j)  = map(k)
      end do
   end do

! eptr

    do i = 1,grid%nedge
       i1 = grid%eptr(1,i)
       i2 = grid%eptr(2,i)
       if ((i1 >= 1).and.(i1 <= nnodes0)) grid%eptr(1,i)=map(i1)
       if ((i2 >= 1).and.(i2 <= nnodes0)) grid%eptr(2,i)=map(i2)
    end do

   deallocate(map)

   ! convert cells associated with boundaries to cl2g

!if (lmpi_master) write(*,*)"DANA TBD do we need to convert to cl2g"
!     do ibound = 1,grid%nbound
!        if (grid%bc(ibound)%nbfacet > 0) then
!           do i = 1,grid%bc(ibound)%nbfacet
!              j     = grid%bc(ibound)%f2ntb(i,4)
!              ielem = grid%bc(ibound)%f2ntb(i,5)
!              grid%bc(ibound)%f2ntb(i,4) = grid%elem(ielem)%cl2g(j)
!           end do
!        end if
!        if (grid%bc(ibound)%nbfaceq > 0) then
!           do i = 1,grid%bc(ibound)%nbfaceq
!              j     = grid%bc(ibound)%f2nqb(i,5)
!              ielem = grid%bc(ibound)%f2nqb(i,6)
!              grid%bc(ibound)%f2nqb(i,5) = grid%elem(ielem)%cl2g(j)
!           end do
!        end if
!     end do

   ! convert bc_ghost cells associated with boundaries to cl2g

      if (grid%cc) then
      do ibound = 1,grid%nbound
         if (bc_ghost(ibound)%nbfacet > 0) then
            do i = 1,bc_ghost(ibound)%nbfacet
               j     = bc_ghost(ibound)%f2ntb(i,4)
               ielem = bc_ghost(ibound)%f2ntb(i,5)
               bc_ghost(ibound)%f2ntb(i,4) = grid%elem(ielem)%cl2g(j)
            end do
         end if
         if (bc_ghost(ibound)%nbfaceq > 0) then
            do i = 1,bc_ghost(ibound)%nbfaceq
               j     = bc_ghost(ibound)%f2nqb(i,5)
               ielem = bc_ghost(ibound)%f2nqb(i,6)
               bc_ghost(ibound)%f2nqb(i,5) = grid%elem(ielem)%cl2g(j)
            end do
         end if
      end do
      end if


!---------------------------
! Reorder cells for cache efficiency

        allocate(cell_ind(grid%nelem))
        do ielem = 1,grid%nelem
           if (grid%elem(ielem)%ncell > 0) then
              allocate(cell_ind(ielem)%c2n_ind(grid%elem(ielem)%ncell))
              cell_ind(ielem)%c2n_ind = 0
           else
              nullify(cell_ind(ielem)%c2n_ind)
           end if
        end do

        call order_level0_cells_par( grid%nnodes0,       grid%nelem, &
                                     grid%elem,          cell_ind )

        outer_most: do ielem = 1, grid%nelem

          pncell = grid%elem(ielem)%ncell
          if (pncell == 0) cycle

         ! cl2g cell reordering (not sorted)

          allocate(itemp(pncell))
          itemp = grid%elem(ielem)%cl2g(1:pncell)
          do i = 1,pncell
            grid%elem(ielem)%cl2g(i) = itemp(cell_ind(ielem)%c2n_ind(i))
          end do
          deallocate(itemp)

          allocate(itemp(pncell)); itemp = 0
          call heap_sort(pncell, cell_ind(ielem)%c2n_ind, itemp)

          outer: do ibound = 1,grid%nbound
             if (grid%bc(ibound)%nbfacet > 0) then
                do i = 1,grid%bc(ibound)%nbfacet
                   if (grid%bc(ibound)%f2ntb(i,5) == ielem) then
                      iold = grid%bc(ibound)%f2ntb(i,4)
                      grid%bc(ibound)%f2ntb(i,4) = itemp(iold)
                   end if
                end do
             end if
             if (grid%bc(ibound)%nbfaceq > 0) then
                do i = 1,grid%bc(ibound)%nbfaceq
                   if (grid%bc(ibound)%f2nqb(i,6) == ielem) then
                      iold = grid%bc(ibound)%f2nqb(i,5)
                      grid%bc(ibound)%f2nqb(i,5) = itemp(iold)
                   end if
                end do
             end if
          end do outer

         deallocate(itemp)

       ! c2n cell reordering

          i1 = size(grid%elem(ielem)%c2n,1)
          i2 = size(grid%elem(ielem)%c2n,2)
          allocate(temp_c2n(i1,i2))
          temp_c2n = grid%elem(ielem)%c2n

          do i = 1,pncell
             grid%elem(ielem)%c2n(1:i1,i) =                                    &
               temp_c2n(1:i1,cell_ind(ielem)%c2n_ind(i))
          end do

          deallocate(temp_c2n)

       ! c2e cell renumbering

          i1 = size(grid%elem(ielem)%c2e,1)
          i2 = size(grid%elem(ielem)%c2e,2)
          allocate(temp_c2n(i1,i2))
          temp_c2n = grid%elem(ielem)%c2e

          do i = 1,pncell
             grid%elem(ielem)%c2e(1:i1,i) =                                    &
               temp_c2n(1:i1,cell_ind(ielem)%c2n_ind(i))
          end do

         deallocate(temp_c2n)

        ! c2e reordering for caching

          call cache_edges_par(grid%nnodes0, grid%nnodes01, grid%nelem,        &
               grid%elem, grid%el2g, grid%nedgeloc,                            &
               size(grid%eptr,2),grid%eptr)

      end do outer_most ! ielem

      do ielem = 1,grid%nelem
         if (associated(cell_ind(ielem)%c2n_ind)) then
            deallocate (cell_ind(ielem)%c2n_ind)
            nullify    (cell_ind(ielem)%c2n_ind)
         end if
      end do

  end subroutine pparty_color

!============================== PPARTY_COLOR_CC ==============================80
!
! After partitioning and migrating information, perform the computations
! and write out the partition files.
!
!=============================================================================80

  subroutine pparty_color_cc(grid)

    use info_depr,  only : pp_cmd_color
    use sort,       only : binary_search, heap_sort, sort_uniq
    use grid_types, only : grid_type
    use bc_cache_cc, only : bc_ghost

    type(grid_type), intent(inout) :: grid

    integer :: ibound, i, j, k, i1, i2, ielem, nnodes0, ib, node

    integer,  dimension(:),    allocatable :: old_l2g, point_dofs, list, indx
    integer,  dimension(:),    allocatable :: map, temp, temp_ind
    integer,  dimension(:,:),  allocatable :: temp_c2n
    real(dp), dimension(:),    allocatable :: rtemp

    logical :: use_binary_search

  continue

!-------------------------------------------------------------------------------
!
! Complete partitioning (convert global numbers to local numbers).
!   - cl2g, eptr, bc_nodes
!   - These done during sr exchange: sr1, sr2, sre
!
!-------------------------------------------------------------------------------

    if (pp_cmd_color /= 1) then
       write(*,*)'Invalid pp_cmd_color = ',pp_cmd_color
       call lmpi_die
    end if

    nnodes0 = grid%nnodes0

    allocate(old_l2g(size(grid%l2g)))
    old_l2g = grid%l2g

    allocate(point_dofs(nnodes0)); point_dofs = -1

    call local_cuthill_mckee_par(grid%nnodes0, grid%nedge, old_l2g,            &
                                  grid%eptr, grid%y, grid%l2g)

    allocate(indx(grid%nnodes0)); indx = 0
    call heap_sort(grid%nnodes0,old_l2g,indx)

    allocate(list(grid%nnodes0)); list = old_l2g(1:grid%nnodes0)
    call heap_sort(grid%nnodes0,list)

    do i = 1,grid%nnodes0
       j = binary_search(grid%nnodes0,list,grid%l2g(i))
       if (j > 0) point_dofs(i) = indx(j)
    end do
    deallocate(list,indx)

! Remap arrays affected by coloring (l2g, c2n, xyz, vol, sr12e, eptr, ibnode).
! Only level 0 values are mapped.
! The sr12e arrays are mapped during the exchange.

! xyz, vol

    allocate(rtemp(nnodes0)) ! Real arrays

    rtemp = grid%x(1:nnodes0)
    do i = 1,nnodes0
       grid%x(i) = rtemp(point_dofs(i))
    end do

    rtemp = grid%y(1:nnodes0)
    do i = 1,nnodes0
       grid%y(i) = rtemp(point_dofs(i))
    end do

    rtemp = grid%z(1:nnodes0)
    do i = 1,nnodes0
       grid%z(i) = rtemp(point_dofs(i))
    end do

    deallocate(rtemp)
    deallocate(point_dofs)

! Create map from old_local to new_local.

    ! Determine if binary_search can be used.
    use_binary_search = .true.
    do i = 2,nnodes0
       if (old_l2g(i-1) > old_l2g(i)) then
          use_binary_search = .false.
          exit
       end if
    end do

    allocate(map(nnodes0)); map = 0

    if (use_binary_search) then
       do i = 1,nnodes0
          j = binary_search(nnodes0,old_l2g,grid%l2g(i))
          map(j) = i
       end do
    else
       do i = 1,nnodes0
          j = 0
          do k = 1,size(old_l2g)
             if (grid%l2g(i) == old_l2g(k)) then
                j = k; exit
             end if
          end do
          map(j) = i
       end do
   end if
   deallocate(old_l2g)

! c2n

    do ielem = 1,grid%nelem
       i1 = size(grid%elem(ielem)%c2n,1)
       i2 = size(grid%elem(ielem)%c2n,2)
       if (i1 > 0) then
          allocate(temp_c2n(i1,i2)); temp_c2n = grid%elem(ielem)%c2n
          do i = 1,i1
             do j = 1,i2
                k = temp_c2n(i,j)
                ! Only alter level0 nodes
                if ((k >= 1).and.(k <= nnodes0))                               &
                   grid%elem(ielem)%c2n(i,j) = map(k)
            end do
          end do
          deallocate(temp_c2n)
      end if
   end do

! ibnode

   do ibound = 1,grid%nbound
      do j = 1,grid%bc(ibound)%nbnode
         k = grid%bc(ibound)%ibnode(j)
         if ((k >= 1).and.(k <= nnodes0)) grid%bc(ibound)%ibnode(j)  = map(k)
      end do
   end do

! eptr

    do i = 1,grid%nedge
       i1 = grid%eptr(1,i)
       i2 = grid%eptr(2,i)
       if ((i1 >= 1).and.(i1 <= nnodes0)) grid%eptr(1,i)=map(i1)
       if ((i2 >= 1).and.(i2 <= nnodes0)) grid%eptr(2,i)=map(i2)
    end do

   deallocate(map)


!  if (db) then
!      write(*,*)'CC COLOR See 50K *************************************** '
!      do ib = 1,grid%nbound
!          write(5000+lmpi_id,*)'------ ib ',ib,' -------------'
!          write(5000+lmpi_id,*)'g%bc(ib)%nbfacet ',grid%bc(ib)%nbfacet
!          do j = 1,grid%bc(ib)%nbfacet
!             icell = grid%bc(ib)%f2ntb(j,4)
!             ielem = grid%bc(ib)%f2ntb(j,5)
!             npc   = grid%elem(ielem)%node_per_cell
!             write(5000+lmpi_id,'(1x,3(i0,1x)," : ",i0," : ",8(i0,1x))')      &
!               grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(j,1:3))),        &
!              !icell,ielem,npc
!               icell,grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!          end do
!          write(5000+lmpi_id,*)'g%bc(ib)%nbfaceq ',grid%bc(ib)%nbfaceq
!          do j = 1,grid%bc(ib)%nbfaceq
!             icell = grid%bc(ib)%f2nqb(j,5)
!             ielem = grid%bc(ib)%f2nqb(j,6)
!             npc   = grid%elem(ielem)%node_per_cell
!             write(5000+lmpi_id,'(1x,4(i0,1x)," : ", i0," : ",8(i0,1x))')     &
!               grid%l2g(grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(j,1:4))),        &
!              !icell,ielem,npc
!               icell,grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!          end do
!      end do
!   end if ! db

       ! Allocate and populate bc_ghost%ibnode

       do ib = 1,grid%nbound
         if ((bc_ghost(ib)%nbfacet + bc_ghost(ib)%nbfaceq) > 0) then
           k = bc_ghost(ib)%nbfacet*3 + bc_ghost(ib)%nbfaceq*4
           allocate(temp(k)); temp = 0
           k = 1
           do j = 1,bc_ghost(ib)%nbfacet
              temp(k:k+2) = bc_ghost(ib)%f2ntb(j,1:3)
              k = k + 3
           end do
           do j = 1,bc_ghost(ib)%nbfaceq
              temp(k:k+3) = bc_ghost(ib)%f2nqb(j,1:4)
              k = k + 4
           end do
           k = k-1
           call sort_uniq(k,temp,bc_ghost(ib)%nbnode)
           allocate(bc_ghost(ib)%ibnode(bc_ghost(ib)%nbnode))
           bc_ghost(ib)%ibnode(1:bc_ghost(ib)%nbnode) =                        &
              temp(1:bc_ghost(ib)%nbnode)
           deallocate(temp)
           do j = 1,bc_ghost(ib)%nbfacet
              do k = 1,3
                 node = bc_ghost(ib)%f2ntb(j,k)
                 bc_ghost(ib)%f2ntb(j,k) =                                     &
                    binary_search(bc_ghost(ib)%nbnode,bc_ghost(ib)%ibnode,node)
                 if (bc_ghost(ib)%f2ntb(j,k) == 0)                             &
                    write(*,*)"Bad BS(4) bc_ghost",                            &
                      ib,j,k,node,bc_ghost(ib)%f2ntb(j,k)
              end do
           end do
           do j = 1,bc_ghost(ib)%nbfaceq
              do k = 1,4
                 node = bc_ghost(ib)%f2nqb(j,k)
                 bc_ghost(ib)%f2nqb(j,k) =                                     &
                    binary_search(bc_ghost(ib)%nbnode,bc_ghost(ib)%ibnode,node)
                 if (bc_ghost(ib)%f2nqb(j,k) == 0)                             &
                    write(*,*)"Bad BS(4) bc_ghost",                            &
                      ib,j,k,node,bc_ghost(ib)%f2nqb(j,k)
              end do
           end do
          end if ! if any faces
        end do

!      if (db) then
!      do ib = 1,grid%nbound
!          write(6000+lmpi_id,*)'------ ib GHOST ',ib,' -------------'
!          write(6000+lmpi_id,*)'bc_ghost(ib)%nbfacet ',bc_ghost(ib)%nbfacet
!          do j = 1,bc_ghost(ib)%nbfacet
!             icell = bc_ghost(ib)%f2ntb(j,4)
!             ielem = bc_ghost(ib)%f2ntb(j,5)
!             npc   = grid%elem(ielem)%node_per_cell
!             !write(6000+lmpi_id,'(1x,6(i0,1x))') bc_ghost(ib)%f2ntb(j,:)
!              write(6000+lmpi_id,'(1x,3(i0,1x)," : ",i0," : ",8(i0,1x))')     &
!             ! grid%l2g(bc_ghost(ib)%ibnode(bc_ghost(ib)%f2ntb(j,1:3))),      &
!               bc_ghost(ib)%f2ntb(j,1:3),                                     &
!             ! icell,ielem,npc
!               icell,grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!                write(6200+lmpi_id,*) bc_ghost(ib)%f2ntb(j,1)
!                write(6200+lmpi_id,*) bc_ghost(ib)%f2ntb(j,2)
!                write(6200+lmpi_id,*) bc_ghost(ib)%f2ntb(j,3)
!          end do
!          write(6000+lmpi_id,*)'g%bc(ib)%nbfaceq ',bc_ghost(ib)%nbfaceq
!          do j = 1,bc_ghost(ib)%nbfaceq
!             icell = bc_ghost(ib)%f2nqb(j,5)
!             ielem = bc_ghost(ib)%f2nqb(j,6)
!             npc   = grid%elem(ielem)%node_per_cell
!             write(6000+lmpi_id,'(1x,6(i0,1x))') bc_ghost(ib)%f2nqb(j,:)
!             do k = 1,npc
!                write(6200+lmpi_id,*) bc_ghost(ib)%f2nqb(j,k)
!             end do
!             !write(6000+lmpi_id,'(1x,4(i0,1x)," : ", i0," : ",8(i0,1x))')    &
!             !grid%l2g(bc_ghost(ib)%ibnode(bc_ghost(ib)%f2nqb(j,1:4))),       &
!             !icell,ielem,npc
!             !icell,grid%l2g(grid%elem(ielem)%c2n(1:npc,icell))
!          end do
!          write(6100+lmpi_id,*)'------ ib GHOST ',ib,' -------------'
!          write(6100+lmpi_id,*)'bc_ghost(ib)%nbnode ',bc_ghost(ib)%nbnode
!          write(6100+lmpi_id,'(1x,1000(i0,1x))')                              &
!            bc_ghost(ib)%ibnode(1:bc_ghost(ib)%nbnode)
!      end do
!      end if ! db

! Map bc_ghost(ib)%f2ntb to grid%l2g(ibnode

!      write(*,*)"NEED to map bc_ghost(ib)%f2ntb to grid%l2g(ibnode..."
       allocate(temp(grid%nnodes01));     temp     = 0
       allocate(temp_ind(grid%nnodes01)); temp_ind = 0
       call heap_sort(grid%nnodes01,grid%l2g,temp_ind)
       do i = 1,grid%nnodes01
          temp(i) = grid%l2g(temp_ind(i))
       end do
       do ib = 1,grid%nbound
          do i = 1,bc_ghost(ib)%nbnode
             j = binary_search(grid%nnodes01,temp,bc_ghost(ib)%ibnode(i))
             if(j==0)write(*,*)"Bad BS 6 ",lmpi_id,ib,i,bc_ghost(ib)%ibnode(i)
             bc_ghost(ib)%ibnode(i) = temp_ind(j)
          end do
       end do
       deallocate(temp,temp_ind)

!---------------------------
! TBD TBD CC When you enable, then adjust the sendindex/recvindex in
!    LibF90/party_lmpi_setup_mpi_cc_sm
! because the cl2g ordering will have changed.
!
! convert cells associated with boundaries to cl2g
!
!      do ibound = 1,grid%nbound
!         if (grid%bc(ibound)%nbfacet > 0) then
!            do i = 1,grid%bc(ibound)%nbfacet
!               j     = grid%bc(ibound)%f2ntb(i,4)
!               ielem = grid%bc(ibound)%f2ntb(i,5)
!               grid%bc(ibound)%f2ntb(i,4) = grid%elem(ielem)%cl2g(j)
!            end do
!         end if
!         if (grid%bc(ibound)%nbfaceq > 0) then
!            do i = 1,grid%bc(ibound)%nbfaceq
!               j     = grid%bc(ibound)%f2nqb(i,5)
!               ielem = grid%bc(ibound)%f2nqb(i,6)
!               grid%bc(ibound)%f2nqb(i,5) = grid%elem(ielem)%cl2g(j)
!            end do
!         end if
!      end do
!
! Reorder cells for cache efficiency
!
!       allocate(cell_ind(grid%nelem))
!       do ielem = 1,grid%nelem
!          if (grid%elem(ielem)%ncell > 0) then
!             allocate(cell_ind(ielem)%c2n_ind(grid%elem(ielem)%ncell))
!             cell_ind(ielem)%c2n_ind = 0
!          else
!             nullify(cell_ind(ielem)%c2n_ind)
!          end if
!       end do

!       write(*,*)"SKIPPING order_level0_cells_par in CC ",lmpi_id
!      !call order_level0_cells_par( grid%nnodes0,       grid%nelem, &
!      !                             grid%elem,          cell_ind )

!       outer_most: do ielem = 1, grid%nelem

!         pncell = grid%elem(ielem)%ncell
!         if (pncell == 0) cycle

!        ! cl2g cell reordering

!         allocate(itemp(pncell))
!         itemp = grid%elem(ielem)%cl2g(1:pncell)
!         do i = 1,pncell
!            grid%elem(ielem)%cl2g(i) = itemp(cell_ind(ielem)%c2n_ind(i))
!         end do

!         ! find ind of newly order cl2g
!         call heap_sort(pncell,grid%elem(ielem)%cl2g,itemp)
!         allocate(temp2(pncell))
!         temp2 = grid%elem(ielem)%cl2g(itemp)

!         outer: do ibound = 1,grid%nbound
!            if (grid%bc(ibound)%nbfacet > 0) then
!               do i = 1,grid%bc(ibound)%nbfacet
!                  if (grid%bc(ibound)%f2ntb(i,5) == ielem) then
!                     k = binary_search(pncell,temp2,grid%bc(ibound)%f2ntb(i,4))
!                     if (k > 0) then
!                        grid%bc(ibound)%f2ntb(i,4) = itemp(k)
!                     else
!                        write(*,'(a60,4(i0,1x))')'ERROR(t) = ',               &
!                          lmpi_id,ibound,ielem,grid%bc(ibound)%f2ntb(i,4)
!                        exit outer_most
!                     end if
!                  end if
!               end do
!            end if
!            if (grid%bc(ibound)%nbfaceq > 0) then
!               do i = 1,grid%bc(ibound)%nbfaceq
!                  if (grid%bc(ibound)%f2nqb(i,6) == ielem) then
!                     k = binary_search(pncell,temp2,grid%bc(ibound)%f2nqb(i,5))
!                     if (k > 0) then
!                        grid%bc(ibound)%f2nqb(i,5) = itemp(k)
!                     else
!                        write(*,'(a60,4(i0,1x))')'ERROR(q) = ',               &
!                          lmpi_id,ibound,ielem,grid%bc(ibound)%f2ntb(i,5)
!                        exit outer_most
!                     end if
!                  end if
!               end do
!            end if
!         end do outer

!         deallocate(itemp,temp2)

!      ! c2n cell reordering

!         i1 = size(grid%elem(ielem)%c2n,1)
!         i2 = size(grid%elem(ielem)%c2n,2)
!         allocate(temp_c2n(i1,i2))
!         temp_c2n = grid%elem(ielem)%c2n

!         do i = 1,pncell
!            grid%elem(ielem)%c2n(1:i1,i) =                                    &
!              temp_c2n(1:i1,cell_ind(ielem)%c2n_ind(i))
!         end do

!         deallocate(temp_c2n)

!      ! c2e cell renumbering

!         i1 = size(grid%elem(ielem)%c2e,1)
!         i2 = size(grid%elem(ielem)%c2e,2)
!         allocate(temp_c2n(i1,i2))
!         temp_c2n = grid%elem(ielem)%c2e

!         do i = 1,pncell
!            grid%elem(ielem)%c2e(1:i1,i) =                                    &
!              temp_c2n(1:i1,cell_ind(ielem)%c2n_ind(i))
!         end do

!        deallocate(temp_c2n)

!       ! c2e reordering for caching

!         call cache_edges_par(grid%nnodes0, grid%nnodes01, grid%nelem,        &
!              grid%elem, grid%el2g, grid%nedgeloc,                            &
!              size(grid%eptr,2),grid%eptr)

!     end do outer_most ! ielem

!     do ielem = 1,grid%nelem
!        if (associated(cell_ind(ielem)%c2n_ind)) then
!           deallocate (cell_ind(ielem)%c2n_ind)
!           nullify    (cell_ind(ielem)%c2n_ind)
!        end if
!     end do

  end subroutine pparty_color_cc

!============================== ORDER_LEVEL0_NODES_PAR  ======================80
!
!  Return a c2n index with the reordering of the level01 cells to co-locate
!  cells for subsequent cache friendly access.
!
!=============================================================================80

  subroutine order_level0_cells_par(nnodes0,nelem,elem,cell_ind)

    use element_types, only : elem_type

! arguments

    integer,                               intent(in)    :: nnodes0,nelem
    type(elem_type),     dimension(nelem), intent(in)    :: elem
    type(cell_ind_type), dimension(nelem), intent(inout) :: cell_ind

! local varibles

    integer :: i, j, cnt, local_node
    integer :: ielem, cell, cell_type, min_local_node

    integer, dimension(nelem) :: cellsum

    type cell_info_type
      integer :: n
      integer, dimension(:), pointer :: list
      integer, dimension(:), pointer :: type
    end type cell_info_type

    type(cell_info_type), dimension(:), allocatable :: cells

  continue

! First get list of global cell numbers around each local node
! Note: only put them in the list if they haven't been previously hit
! in this partition!  (Associate cell with least-numbered local node)
! Count em

      allocate(cells(nnodes0))
      cells(:)%n = 0
      cellsum = 0

      do ielem = 1, nelem

        if (elem(ielem)%ncell > 0) then
        do i = 1, elem(ielem)%ncell ! cell01

          min_local_node = nnodes0+1

          do j = 1, elem(ielem)%node_per_cell
            local_node = elem(ielem)%c2n(j,i)
            if ( local_node <= nnodes0) then
              if ( local_node < min_local_node ) min_local_node = local_node
            endif
          end do

          if ( min_local_node <= nnodes0 ) then
            cells(min_local_node)%n = cells(min_local_node)%n + 1
            cellsum(ielem) = cellsum(ielem) + 1
            cnt = i
          endif

        end do
        end if
      ! write(*,*)lmpi_id,': max cell = ',cnt,elem(ielem)%ncell
      end do

! Allocate em

      do i = 1, nnodes0
        allocate(cells(i)%list(cells(i)%n))
        allocate(cells(i)%type(cells(i)%n))
      end do

! Gather em

      cells(:)%n = 0

      do ielem = 1, nelem

        if (elem(ielem)%ncell > 0) then
        do i = 1, elem(ielem)%ncell

          min_local_node = nnodes0+1

          do j = 1, elem(ielem)%node_per_cell
            local_node = elem(ielem)%c2n(j,i)
            if ( local_node <= nnodes0 ) then
              if ( local_node < min_local_node ) min_local_node = local_node
            endif
          end do

          if ( min_local_node <= nnodes0 ) then
            cells(min_local_node)%n = cells(min_local_node)%n + 1
            cells(min_local_node)%list(cells(min_local_node)%n) = i
            cells(min_local_node)%type(cells(min_local_node)%n) = ielem
          endif

        end do
        end if

      end do

! Now go through and set the c2n_ind to reflect reordering

      do ielem = 1, nelem
        cnt = 0
        do i = 1, nnodes0
          do j = 1, cells(i)%n
            cell      = cells(i)%list(j)
            cell_type = cells(i)%type(j)
            if ( cell_type == ielem ) then
              cnt = cnt + 1
              if ( cnt > elem(ielem)%ncell ) then
                write(*,*) 'Error: more local cells found than allocated.'
                call lmpi_die
              endif
              cell_ind(ielem)%c2n_ind(cnt) = cell
            endif
          end do
        end do
        if ( cnt /= elem(ielem)%ncell ) then
          write(*,*)'Error: diff number of local cells found than allocated.', &
            cnt,elem(ielem)%ncell
            call lmpi_die
        endif
      end do

      do i = 1, nnodes0
        deallocate(cells(i)%list, cells(i)%type)
      end do
      deallocate(cells)

  end subroutine order_level0_cells_par

!============================= LOCAL_CUTHILL_MCKEE_PAR =======================80
!
! Reorder the local nodes using Cuthill-McKee.  Keep track of L2G and
! watch out for discontinuous parts of the domain. (PARALLEL)
!
!=============================================================================80

  subroutine local_cuthill_mckee_par(nnodes0,nedge,l2g,eptr,y,new_l2g)

    use info_depr, only : twod
    use kinddefs,  only : dp
    use twod_util, only : yplane_2d, y_coplanar_tol

    integer, intent(in) :: nnodes0, nedge

    integer, dimension(:),       intent(in)  :: l2g
    integer, dimension(2,nedge), intent(in)  :: eptr
    integer, dimension(:),       intent(out) :: new_l2g

    real(dp), dimension(:), intent(in) :: y

    integer :: i, j, k, n1, n2, nadj, nadj1, nadj2, nadjm, nloc, counter
    integer :: iedge, node1, node2, node1_value, node2_value
    integer :: old_local_node_number, old_global_node_number
    integer :: rct, rcurrent

    integer, dimension(:), allocatable :: locs, locvc, n2o, temp_l2g

    integer(system_i1), dimension(:), allocatable :: mark

  continue

   if (.not.twod) then ! twod is handled differently after end if

! Set up nloc, locs and locvc arrays for this partition

    allocate(locs(nnodes0+1)); locs = 0

    edge_loop1 : do iedge = 1,nedge
      node1 = eptr(1,iedge)
      node2 = eptr(2,iedge)
      if ( (node1 <= nnodes0) .and. (node2 <= nnodes0)) then
        locs(node1+1) = locs(node1+1) + 1
        locs(node2+1) = locs(node2+1) + 1
      endif
    end do edge_loop1

    do i = 2, nnodes0+1
      locs(i) = locs(i) + locs(i-1)
    end do

    nloc = locs(nnodes0+1)
    allocate(locvc(nloc)); locvc = 0

    edge_loop2 : do iedge = 1,nedge
      node1 = eptr(1,iedge)
      node2 = eptr(2,iedge)
      if ( (node1 <= nnodes0) .and. (node2 <= nnodes0) ) then
        node1_value        = locs(node1) + 1
        locs(node1)        = node1_value
        locvc(node1_value) = node2

        node2_value        = locs(node2) + 1
        locs(node2)        = node2_value
        locvc(node2_value) = node1
      endif
    end do edge_loop2

    do i = nnodes0+1, 2, -1
      locs(i) = locs(i-1)
    end do

    locs(1) = 0

    nloc = locs(nnodes0+1)

! Now do Cuthill-McKee

! re-order locvc so elements surrounding
! each node are ordered by degree fom minimum to maximum

    n1 = 0
    do i = 1, nnodes0
      nadj = locs(i+1) - locs(i)
      do j = 1, nadj-1
        do k = j+1, nadj
          n1    = locvc(locs(i) + j)
          nadj1 = locs(n1+1) - locs(n1)
          n2    = locvc(locs(i) + k)
          nadj2 = locs(n2+1) - locs(n2)
          if (nadj2 < nadj1) then
            locvc(locs(i) + j) = n2
            locvc(locs(i) + k) = n1
          end if
        end do
      end do
    end do

    allocate(mark(nnodes0)); mark = 0
    allocate(n2o(nnodes0));  n2o  = 0

! Now perform the actual CM algorithm
! The way this is written, if the algorithm walks itself into a corner,
! it will restart its search at the next point in the list that has not
! been renumbered yet.  This should also take care of the situation where
! we have discontinuous parts of the domain, and we have to hop over to
! another grid chunk for the same processor.

    rct      = 0 ! number of nodes in n2o (found)
    rcurrent = 1 ! current node in n2o to search

    rct_versus_nodes: do

      if (rct >= nnodes0 ) exit rct_versus_nodes ! if all nodes accounted

      if (rcurrent > rct) then  ! all nodes in n2o searched
         ! Search grid for node with minimum degree
         ! If current node has lesser degree and it has not already
         ! been renumbered, keep it as the new starting point
        nadjm = nnodes0+1
        do i = 1, nnodes0
           if (mark(i) == 0) then
              nadj1 = locs(i+1) - locs(i)
              if (nadj1 < nadjm) then
                 n1    = i
                 nadjm = nadj1
              end if
           end if
        end do
        rct      = rct + 1
        n2o(rct) = n1
        rcurrent = rct
        mark(n1) = 1
      end if

! Go to node that was last renumbered
! Walk around it and renumber any of its neighbors that have
! not already been renumbered

      r_do: do
        if (rcurrent > rct) exit r_do
        n1 = n2o(rcurrent)
        rcurrent = rcurrent + 1
        do i = 1, locs(n1+1) - locs(n1)
          n2 = locvc(locs(n1) + i)
          if (mark(n2) == 0) then
            rct = rct + 1
            n2o(rct) = n2
            mark(n2) = 1
          end if
        end do
      end do r_do

    end do rct_versus_nodes

! Now renumber our L2G vector on this partition based on this CM information

    do i = 1, nnodes0
      old_local_node_number  = n2o(i)
      old_global_node_number = l2g(old_local_node_number)
      new_l2g(i)             = old_global_node_number
    end do

! Free up memory we don't need anymore

    deallocate(mark,locs,locvc,n2o)

  end if ! not twod

! For 2D, we need to ensure that the local nodes on the primary plane come
! first, followed then by the secondary plane local nodes.  Ideally, this would
! be taken into account in the process above, but this is not so
! straightforward.  As a hack, reset new_l2g(:) to conform to this requirement.
! This ruins the Cuthill-McKee result, but we will live with this
! in 2D for now.

    hack_2d : if ( twod ) then

      i = size(l2g,1)
      allocate(temp_l2g(i)); temp_l2g = l2g

      counter = 0

      do i = 1, nnodes0
        if ( abs(y(i) - yplane_2d) <= y_coplanar_tol ) then
          counter = counter + 1
          new_l2g(counter) = temp_l2g(i)
        endif
      end do

      do i = 1, nnodes0
        if ( abs(y(i) - yplane_2d) > y_coplanar_tol ) then
          counter = counter + 1
          new_l2g(counter) = temp_l2g(i)
        endif
      end do

      if ( counter /= nnodes0 ) then
        write(*,*) 'Error in assigning new_l2g for 2D mode.'
        call lmpi_die
        stop
      endif

      deallocate(temp_l2g)

    endif hack_2d

  end subroutine local_cuthill_mckee_par

!============================== CACHE_EDGES_PAR  =============================80
!
!  Reordering edges for cache efficency based on previous Cuthill ordering.
!  Alters: grid%eptr, grid%el2g, grid%elem(ielem)%c2e
!
!=============================================================================80

  subroutine cache_edges_par(nnodes0,nnodes01,nelem,elem,el2g,nedgeloc,        &
                             neptr,eptr)

    use element_types, only : elem_type

! arguments

    integer, intent(in) :: nnodes01,nnodes0,nelem,neptr,nedgeloc

    type(elem_type),    dimension(nelem),   intent(inout) :: elem
    integer(system_i8), dimension(neptr),   intent(inout) :: el2g
    integer,            dimension(2,neptr), intent(inout) :: eptr

! local varibles

    integer :: i, j, n1, n2, node1, node2, ie, ielem, icell, off
    integer, dimension(:),   allocatable :: cnt, ioff
    integer, dimension(:,:), allocatable :: temp_eptr
    integer(system_i8), dimension(:), allocatable :: temp_el2g

  continue

    allocate(cnt(nnodes01)); cnt = 0
    do ie = 1,nedgeloc
       n1 = min(eptr(1,ie),eptr(2,ie))
       if (n1 > nnodes0) write(*,*)"Error 1 ",lmpi_id,n1,nnodes0
       cnt(n1) = cnt(n1) + 1
    end do

    allocate(ioff(nnodes01)); ioff = 0
    ! set offset
    ioff(1) = 1
    do i = 2,nnodes01
       ioff(i) = ioff(i-1)+cnt(i-1)
    end do

    allocate(temp_eptr(2,neptr)); temp_eptr = 0
    temp_eptr = eptr

    allocate(temp_el2g(neptr)); temp_el2g = 0
    temp_el2g = el2g

    ! Move values and increment offsets
    do ie = 1,nedgeloc
       n1 = temp_eptr(1,ie)
       if (temp_eptr(2,ie) < n1) n1 = temp_eptr(2,ie)
       n2 = ioff(n1)
       eptr(1:2,ioff(n1)) = temp_eptr(1:2,ie)
       el2g(ioff(n1))     = temp_el2g(ie)
       ioff(n1) = ioff(n1) + 1
    end do

    deallocate(temp_eptr)
    deallocate(temp_el2g)

    ! Reset offset
    ioff(1) = 1
    do i = 2,nnodes01
       ioff(i) = ioff(i-1)+cnt(i-1)
    end do

    do ielem = 1,nelem
       do icell = 1,elem(ielem)%ncell
          loop_ie: do ie = 1,elem(ielem)%edge_per_cell
            node1 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,1),icell)
            node2 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,2),icell)
            n1 = min(node1,node2)
            n2 = (node1+node2)-n1
            off = ioff(n1)
            do j = 0,cnt(n1)-1
               if ((n2 == eptr(1,off+j)).or.(n2 == eptr(2,off+j))) then
                  elem(ielem)%c2e(ie,icell) = off+j
                  exit
               end if
            end do
          end do loop_ie
       end do
    end do

    deallocate(cnt)
    deallocate(ioff)

  end subroutine cache_edges_par

end module pparty_computes

! A simple library to facilitate logging
!
! Usage:
!
! 1) Require library:
!
!      use logger
!
! 2) Initialise with unit number, log filename, and optionally,
!    whether you want the logging contents echoed to standard output:
!
!      call log_on( 8, 'sample.log', echo=.false. )
!      call log_on( 8, 'sample.log' )
!
! 3) Log values followed by an optional description:
!
!      call log_it( variable, 'description' )
!      call log_it( variable )
!
! TODO:
! - add logging level, i.e., INFO, WARN, DEBUG, or 0-5, etc.?

module logger

  use kinddefs, only: dp

  implicit none

  public :: log_on, log_it

  private

  integer, parameter :: MAX_LOG_LINE_WIDTH = 1024

  character(MAX_LOG_LINE_WIDTH) :: current_line = ''

  integer :: log_unit
  logical :: log_echo = .true.

  interface log_it
    module procedure log_int
    module procedure log_int_ary
    module procedure log_real
    module procedure log_real_ary
    module procedure log_char
  end interface

contains

! Initialize the logger

  subroutine log_on( fileunit, filename, echo )

    integer,           intent(in) :: fileunit
    character(*),      intent(in) :: filename
    logical, optional, intent(in) :: echo

    continue

    open( unit=fileunit, file=filename )

    log_unit = fileunit

    if ( present(echo) ) log_echo = echo

  end subroutine log_on

! Various permutations of log_it arguments

  subroutine log_int( variable, description )
    integer,                intent(in) :: variable
    character(*), optional, intent(in) :: description
    write(current_line,*) variable, ' ', description
    call write_to_log
  end subroutine log_int

  subroutine log_int_ary( variable, description )
    integer, dimension(:),           intent(in) :: variable
    character(*),          optional, intent(in) :: description
    write(current_line,*) variable, ' ', description
    call write_to_log
  end subroutine log_int_ary

  subroutine log_real( variable, description )
    real(dp),               intent(in) :: variable
    character(*), optional, intent(in) :: description
    write(current_line,*) variable, ' ', description
    call write_to_log
  end subroutine log_real

  subroutine log_real_ary( variable, description )
    real(dp), dimension(:),           intent(in) :: variable
    character(*),           optional, intent(in) :: description
    write(current_line,*) variable, ' ', description
    call write_to_log
  end subroutine log_real_ary

  subroutine log_char( variable, description )
    character(*),           intent(in) :: variable
    character(*), optional, intent(in) :: description
    write(current_line,*) variable, ' ', description
    call write_to_log
  end subroutine log_char

! Helper method to write current line

  subroutine write_to_log
    use string_utils,       only: strip
    write(log_unit,'(a)') trim(current_line)
    if (log_echo) write(*,'(a)') strip(current_line)
  end subroutine write_to_log

end module logger

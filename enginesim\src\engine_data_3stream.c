#include <math.h>
#include "engine_data_3stream.h"
/*============================== ENGINE_SIM3 ===================================
!
! A black box for a first-order engine dynamics simulator - 3 streams
!
!=============================================================================*/
  struct bc_data_type engine_data ( long stream,
                                    double gamma,
                                    double pt_inlet,
                                    double tt_inlet,
                                    double simulationTime
                                   )
{

    struct bc_data_type bc_data;

    double fan_pr;
    double fan_eff;
    double hpc_pr;
    double hpc_eff;
    double hpt_pr;
    double hpt_eff;
    double lpt_pr;
    double lpt_eff;
    double combustor_eff;
    double f;
    double cp;
    double hc;
    double mdot_bypass;
    double mdot_core;
    double fan_vce_pr;
    double fan_vce_eff;

    double one;
    double gm1g;
    double br;
    double pt_fan;
    double tt_fan;
    double pt_hpc;
    double tt_hpc;
    double pt_combustor;
    double tt_combustor;
    double pt_hpt;
    double tt_hpt;
    double pt_lpt;
    double tt_lpt;
    double pt_vce_fan;
    double tt_vce_fan;

    one = (double) 1.0;

/* Engine Performance Calculations */
/* Fan */
    fan_pr = (double) 2.324;
    fan_eff = (double) .919;
/* HPC */
    hpc_pr = (double) 11.05;
    hpc_eff = (double) 0.894;
/* HPT */
    hpt_pr = (double) 4.003;
/* Adjusted to Account for cooling flow, added 0.02 */
    hpt_eff = (double) .939;
/* LPT */
    lpt_pr = (double) 2.926;
/* Adjust to account for cooling flow, added 0.02 */
    lpt_eff = (double) .938;
/* Combustor */
    combustor_eff = (double) .959;
    f             = (double) 0.4008/((double)13.925);
    cp            = (double) 1100.0;
    hc            = (double) 42.8e+6;
/* Flow Rates - Core & Bypass */
    mdot_bypass = (double) 31.10;
    mdot_core = (double) 16.72;

/* Fan VCE - Could use if the thrid stream is required */
      fan_vce_pr = 1.675;
      fan_vce_eff = .899;

      gm1g = (gamma - one)/gamma;

      br = mdot_core/mdot_bypass;

/* Calculations */

/* Fan */
      pt_fan = fan_pr*pt_inlet;
      tt_fan = tt_inlet*(one + (pow(fan_pr,gm1g) - one)/fan_eff);

/* VCE Fan */
      pt_vce_fan = fan_vce_pr*pt_inlet;
      tt_vce_fan = tt_inlet*(one + (pow(fan_vce_pr,gm1g) - one)/fan_vce_eff);

/* Compressor */
      pt_hpc = hpc_pr*pt_fan;
      tt_hpc = tt_fan*(one + (pow(hpc_pr,gm1g) - one)/hpc_eff);

/* Combustor */
      pt_combustor = pt_hpc*combustor_eff;
      tt_combustor = (tt_hpc + f*combustor_eff*(hc/cp))/(one + f);

/* HPT */
      pt_hpt = pt_combustor/hpt_pr;
      tt_hpt = tt_combustor*(one + (pow(hpt_pr,-gm1g) - one)/hpt_eff);

/* LPT */
      pt_lpt = pt_hpt/lpt_pr;
      tt_lpt = tt_hpt*(one + (pow(lpt_pr,-gm1g) - one)/lpt_eff);

/* Output */
      switch ( stream ) {
      case 1:
/* VCE Duct - Top Nozzle (from picture) */
        bc_data.pt = pt_vce_fan;
        bc_data.tt = tt_vce_fan;
        break;
      case 2:
/* Core Flow - Middle Nozzle (from picture) */
        bc_data.pt = pt_lpt;
        bc_data.tt = tt_lpt;
        break;
      case 3:
/* Bypass Duct - Low Nozzle (from picture) */
        bc_data.pt = pt_fan;
        bc_data.tt = tt_fan;
        break;
      default:
        bc_data.pt = 0;
        bc_data.tt = 0;
        break;
      }

      return (bc_data);
}

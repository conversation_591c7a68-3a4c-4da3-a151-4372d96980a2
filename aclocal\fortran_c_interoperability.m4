AC_DEFUN([AX_FORTRAN_C_INTEROPERABILITY],
[AC_CACHE_CHECK([fortran Interoperability with C],
 ax_cv_fortran_c_interoperability,
 [AC_LANG_PUSH(Fortran)
  AC_LINK_IFELSE(
  [
       program main
          use iso_c_binding,    only : c_int
          type, bind(c) :: ret_val_type
            integer(c_int) :: j
          end type ret_val_type
       interface 
         function tester( i ) bind(c,name='malloc')
           use iso_c_binding,    only : c_int
           type, bind(c) :: ret_val_type
             integer(c_int) :: j
           end type ret_val_type
           type(ret_val_type) :: tester
           integer(c_int), value, intent(in) :: i
         end function tester
       end interface
          type(ret_val_type) :: output
          integer(c_int) :: input
          input = 10
          output = tester( input )
       end program main
  ],
  [ax_cv_fortran_c_interoperability=yes],
  [ax_cv_fortran_c_interoperability=no]
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_c_interoperability" != 'no'
then
  AC_DEFINE([HAVE_FORTRAN_C_INTEROPERABILITY],[1],[fortran provides interoperability with C])
  AM_CONDITIONAL(BUILD_FORTRAN_C_INTEROP_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_FORTRAN_C_INTEROP_SUPPORT,false)
fi
])


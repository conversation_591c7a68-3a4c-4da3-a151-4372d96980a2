module ammo

  implicit none

  private

  public :: optimization_driver
  public :: design_sanity

contains

!================================= DESIGN_SANITY =============================80
!
!  Checks to make sure input is valid for each optimizer
!
!=============================================================================80
  subroutine design_sanity()

    use lmpi,              only : lmpi_die
    use designs,           only : get_input_data, free_design
    use design_types,      only : design_type, ks_function
    use system_extensions, only : se_chdir
    use design_io_helpers, only : set_up_design
    use nml_design,        only : opt_algorithm, n_design_pts, model_variables,&
                                  body_grouping, base_directory

    integer :: i, itime, ncyc, ivisc, eqn_set, j, nslices

    logical :: obj_found, obj_already_found, found_sculptor

    character(len=80) :: project
    character(len=20) :: grid_format, data_format

    type(design_type) :: design

  continue

! Make sure we have an objective function of some sort

    obj_found = .false.
    do i = 1, n_design_pts
      if ( model_variables(i)%opt_data%nobjectives > 0 ) obj_found = .true.
    end do
    if ( .not. obj_found ) then
      write (*,*) 'Error: No objective function defined.'
      call lmpi_die
    endif

! Error-checking for homegrown KS function switch (NOT KSOPT!)

    if ( ks_function ) then

      if ( opt_algorithm /= 1 .and. opt_algorithm /= 4 ) then
        write (*,*)'Error: KS function switch only available with PORT and DOT.'
        call lmpi_die
      endif

      do i = 1, n_design_pts
        if ( model_variables(i)%opt_data%nconstraints > 0 ) then
          write (*,*) 'Error: KS function switch cannot currently handle'
          write (*,*) 'constraints.'
          call lmpi_die
        endif
      end do

    endif

! Enforce restrictions on each optimizer

    select case(opt_algorithm)
    case(1)           ! Restrictions on DOT

      obj_already_found = .false.
      do i = 1, n_design_pts

        if ( model_variables(i)%opt_data%nobjectives > 1 ) then
          write (*,*) 'Error: DOT cannot handle multiple objectives.'
          call lmpi_die
        endif

        if ( model_variables(i)%opt_data%nobjectives > 0 ) then
          if ( obj_already_found ) then
            write (*,*) 'Error: DOT cannot handle multiple objectives.'
            call lmpi_die
          else
            obj_already_found = .true.
          endif
        endif
      end do

    case(3)           ! Restrictions on KSOPT

! No known restrictions

    case(4)           ! Restrictions on PORT

      do i = 1, n_design_pts

        if ( model_variables(i)%opt_data%nconstraints > 0 ) then
          write (*,*) 'Error: PORT cannot handle constraints.'
          call lmpi_die
        endif

        if( model_variables(i)%opt_data%nobjectives>1 .and. n_design_pts>1 )then
          write (*,*) 'Error: PORT cannot handle multipoint/multiobjective.'
          call lmpi_die
        endif

      end do

    case(5)           ! Restrictions on NPSOL

      obj_already_found = .false.
      do i = 1, n_design_pts

        if ( model_variables(i)%opt_data%nobjectives > 1 ) then
          write (*,*) 'Error: NPSOL cannot handle multiple objectives.'
          call lmpi_die
        endif

        if ( model_variables(i)%opt_data%nobjectives > 0 ) then
          if ( obj_already_found ) then
            write (*,*) 'Error: NPSOL cannot handle multiple objectives.'
            call lmpi_die
          else
            obj_already_found = .true.
          endif
        endif
      end do

    case(6)           ! Restrictions on SNOPT

      do i = 1, n_design_pts

        if( model_variables(i)%opt_data%nobjectives>1 .and. n_design_pts>1 )then
          write (*,*) 'Error: SNOPT cannot handle multipoint/multiobjective.'
          call lmpi_die
        endif

      end do

    case default

      write (*,*) 'Error: unknown opt_algorithm.'
      call lmpi_die

    end select

! Check to be sure no model requires more than one Sculptor "body".
! (multiple bodies can be bookkept within a single
! Sculptor setup and appear as a single "body" to FUN3D)

! Check to be sure that body grouping is being performed if
! trimming is active

    do i = 1, n_design_pts
      call se_chdir(model_variables(i)%model_directory)
      call se_chdir('Flow')
      call get_input_data(project,eqn_set,ivisc,itime,ncyc,grid_format,        &
                          data_format,nslices)
      call set_up_design(eqn_set, itime, design)

      found_sculptor = .false.
      do j = 1, design%nbodies
        if ( design%body_data(j)%parameterization == 4 ) then
          if ( .not. found_sculptor ) then
            found_sculptor = .true.
          else
            write(*,*)'Error: When running with Sculptor parameterizations,'
            write(*,*)'must bookkeep multiple Sculptor parameterizations within'
            write(*,*)'a single set of Sculptor input files so that they appear'
            write(*,*)'as a single "body" to FUN3D.'
            stop
          endif
        endif
      end do

      if ( design%trimming%active ) then
        if ( .not. body_grouping ) then
          write(*,*) 'Error: When using active trim control, you must use'
          write(*,*) 'body grouping!'
            stop
        endif
      endif

      call free_design(design)
      call se_chdir(trim(base_directory))
    end do

  end subroutine design_sanity


!============================ OPTIMIZATION_DRIVER ============================80
!
!  Drive a multipoint optimization with a specific optimization package
!
!=============================================================================80
  subroutine optimization_driver(io)

    use lmpi,       only : lmpi_die
    use port,       only : port_driver
    use npsols,     only : npsol_driver
    use snopt,      only : snopt_driver
    use ksopt,      only : ksopt_driver
    use dots,       only : dot_driver
    use nml_design, only : what_to_do, restart_optimization, tau_subproblem,   &
                           max_design_cycles, max_function_evals, feas_tol_val,&
                           dot_method, lss_flag, opt_algorithm, ammo_directory

    integer, intent(in) :: io

  continue

    select case (opt_algorithm)
    case (1)
      call dot_driver(max_design_cycles,tau_subproblem,what_to_do,             &
                      restart_optimization,dot_method,feas_tol_val,io,         &
                      ammo_directory,lss_flag)
    case (3)
      call ksopt_driver(max_design_cycles,what_to_do,restart_optimization,io,  &
                        ammo_directory,lss_flag)
    case (4)
      call port_driver(max_function_evals,max_design_cycles,tau_subproblem,io, &
                       what_to_do,restart_optimization,ammo_directory,lss_flag)
    case (5)
      call npsol_driver(max_design_cycles,what_to_do,restart_optimization,io,  &
                        ammo_directory,lss_flag)
    case (6)
      call snopt_driver(max_design_cycles,what_to_do,restart_optimization,io,  &
                        ammo_directory,lss_flag)
    case default
      write(*,*) 'Unknown opt_algorithm type.'
      call lmpi_die
    end select

  end subroutine optimization_driver

end module ammo

!================================= EDGE_NGRAD ================================80
!
! This routine computes the normal gradient from the edge contributions
! at the face and the nearby gradients.
!
!=============================================================================80

  pure function edge_ngrad( xnorm, ynorm, znorm, ex, ey, ez, dsi,              &
                            n_turb, turb1, turb2,                              &
                            tgradx1, tgrady1, tgradz1,                         &
                            tgradx2, tgrady2, tgradz2 )

    integer, intent(in) :: n_turb

    real(dp), intent(in) :: xnorm, ynorm, znorm, ex, ey, ez, dsi

    real(dp), dimension(n_turb), intent(in) :: turb1, turb2

    real(dp), dimension(n_turb), intent(in) :: tgradx1, tgrady1, tgradz1
    real(dp), dimension(n_turb), intent(in) :: tgradx2, tgrady2, tgradz2

    real(dp), dimension(n_turb) :: edge_ngrad

    integer :: n

    real(dp) :: txavg, tyavg, tzavg
    real(dp) :: tx, ty, tz, egradt, gradt_xi

  continue

    do n=1,n_turb

!     average gradients : gradx, grady, gradz

      txavg = 0.5_dp*( tgradx1(n) + tgradx2(n) )
      tyavg = 0.5_dp*( tgrady1(n) + tgrady2(n) )
      tzavg = 0.5_dp*( tgradz1(n) + tgradz2(n) )

!     directional gradients along edge

      egradt = ( turb2(n) - turb1(n) )*dsi

!     average gradient in edge direction

      gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

!     resolve gradient contributions from edge and cell-centers.

      tx = txavg + ( egradt - gradt_xi )*ex
      ty = tyavg + ( egradt - gradt_xi )*ey
      tz = tzavg + ( egradt - gradt_xi )*ez

      edge_ngrad(n) = tx*xnorm &
                    + ty*ynorm &
                    + tz*znorm
    enddo

  end function edge_ngrad

!=================================== DQ ======================================80
!
! Extrapolate or interpolate and limit the gradient to the interface
!
!   Extended to the "U-MUSCL" scheme of Burg et al (AIAA 2003-3983) with
!   upwinding parameter kappa_umuscl:
!   kappa_umuscl is analogous to kappa in the usual structured-mesh upwind
!   schemes. kappa_umuscl = 0 gives the standard (baseline fun3d) unstructured
!   scheme; kappa_umuscl = 1 is central difference; kappa_umuscl = 1/2 gives
!   3rd order in one dimension if the gradients are 2nd order.
!
!=============================================================================80

  pure function dq(rx, ry, rz, gradx, grady, gradz, q1, q2, phi, eps, ndim)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_half, my_1

    use info_depr,       only : kappa_umuscl, epscoef
    use inviscid_flux,   only : iflim

    integer,                   intent(in) :: ndim
    real(dp),                  intent(in) :: rx, ry, rz, eps
    real(dp), dimension(ndim), intent(in) :: gradx, grady, gradz
    real(dp), dimension(ndim), intent(in) :: phi
    real(dp), dimension(ndim), intent(in) :: q1, q2
    real(dp), dimension(ndim)             :: dqm,  dqp, dq, dqmb, dqpb

  continue

!   Construct the consecutive dqs

    dqm = gradx*rx + grady*ry + gradz*rz
    dqp = my_half*(q2 - q1)

!   Construct the cell face gradients depending on the limiter type

!   Edge limited or unlimited gradients
!   N.B.: 0<= phi <= 1 is invoked independent of iflim by the pressure limiter

    if (iflim == 6) then

!     Higher order state reconstruction using the
!     smooth(CFl3D) differentiable gradient and [pressure] limiter

      dqmb = smthlmv(dqm, dqp, epscoef*eps, ndim)
      dqpb = smthlmv(dqp, dqm, epscoef*eps, ndim)

    else if (iflim == 5) then

!     Higher order state reconstruction using the
!     smooth(vanAlbada) differentiable gradient and [pressure] limiter

      dqmb = vaflxlv(dqm, dqp, epscoef*eps, ndim)
      dqpb = vaflxlv(dqp, dqm, epscoef*eps, ndim)

    else if (iflim == 4) then

!     Higher order state reconstruction using the
!     vanLeer and [pressure] limiter

      dqmb = vlflxlv(dqm, dqp, ndim)
      dqpb = vlflxlv(dqp, dqm, ndim)

    else if (iflim == 3) then

!     Higher order state reconstruction using the
!     minmod and [pressure] limiter

      dqmb = minmodv(dqm, dqp, ndim)
      dqpb = minmodv(dqp, dqm, ndim)

    else

      dqmb = dqm
      dqpb = dqp

    end if

!   Higher order state reconstruction using UMUSCL
!   (baseline scheme is kappa_umuscl = 0)
!   N.B.: if iflim=0 then phi will = 1
!         if iflim=1 or iflim=2 then phi will be <= 1
!         if iflim=3 or iflim=4 or iflim=5 or iflim=6 then phi will = 1
!         unless the pressure limiter is turned on
!         in which case phi will be <= 1

    dq = phi*((my_1 - kappa_umuscl)*dqmb + kappa_umuscl*dqpb)

  end function dq

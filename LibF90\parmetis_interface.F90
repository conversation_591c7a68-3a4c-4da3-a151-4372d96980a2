module parmetis_interface

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: adaptiveRepart
  public :: partKway

contains

!=========================== adaptiveRepart ==================================80
!=============================================================================80

  subroutine adaptiveRepart( npart, nodeoffset, nnodes0, rowstart, adjacency,  &
                             part )

#ifdef HAVE_MPI
#ifdef HAVE_PARMETIS
    use kinddefs,        only : system_r4
    use lmpi,            only : lmpi_comm
#endif
#endif

    integer,                                   intent(in)    :: npart
    integer, dimension(npart+1),               intent(inout) :: nodeoffset
    integer,                                   intent(in)    :: nnodes0
    integer, dimension(nnodes0+1),             intent(inout) :: rowstart
    integer, dimension(rowstart(nnodes0+1)-1), intent(inout) :: adjacency
    integer, dimension(nnodes0),               intent(inout) :: part

#ifdef HAVE_MPI
#ifdef HAVE_PARMETIS
    integer :: weightflag, vwgt, vsize, adjwgt
    integer :: numflag, edgecut
    integer, dimension(5) :: options
    real(system_r4) :: itr

    integer, parameter :: ncon = 1

    real(system_r4), dimension(ncon*npart) :: tpwgts
    real(system_r4), dimension(ncon)       :: ubvec

    continue

    vwgt = 0
    vsize = 0
    adjwgt = 0
    weightflag = 0
    numflag = 0 ! 0 C, 1 FORTRAN array index
    tpwgts = 1.0_system_r4/real(npart,system_r4)
    ubvec = 1.05_system_r4

    itr = 500000.0_system_r4 ! manual suggests 1000, 500000 gives better parts
    options(1) = 1
    options(2) = 0 ! add in deg info with 1+2+4+8+32
    options(3) = 15
    options(4) = 1

    if (0 == numflag) then
      nodeoffset = nodeoffset - 1
      rowstart = rowstart - 1
      adjacency = adjacency - 1
      part = part - 1
    end if

    call ParMETIS_V3_AdaptiveRepart(                                           &
      nodeoffset, rowstart, adjacency,                                         &
      vwgt, vsize, adjwgt, weightflag,                                         &
      numflag,                                                                 &
      ncon, npart, tpwgts, ubvec,                                              &
      itr,                                                                     &
      options, edgecut, part, lmpi_comm )

    if (0 == numflag) then
      nodeoffset = nodeoffset + 1
      rowstart = rowstart + 1
      adjacency = adjacency + 1
      part = part + 1
    end if

#else
    if (.false.) write(*,*) nodeoffset(1), part(1), adjacency(1)
    write(*,*) 'Warning! adpativeRepart was not compiled with -DHAVE_PARMETIS'
#endif
#else
    if (.false.) write(*,*) nodeoffset(1), part(1), adjacency(1)
    write(*,*) 'Warning! adpativeRepart was not compiled with -DHAVE_MPI ',    &
               'ParMetis disabled'
#endif
  end subroutine adaptiveRepart

!=========================== partKway ========================================80
!=============================================================================80

  subroutine partKway( npart, nodeoffset, nnodes0, rowstart, adjacency, &
                       part, vertex_weight, edge_weight )

#ifdef HAVE_MPI
#ifdef HAVE_PARMETIS
    use kinddefs,        only : system_r4
    use lmpi,            only : lmpi_comm
    use allocations,     only : my_alloc_ptr
#endif
#endif

    integer,                                   intent(in)    :: npart
    integer, dimension(npart+1),               intent(inout) :: nodeoffset
    integer,                                   intent(in)    :: nnodes0
    integer, dimension(nnodes0+1),             intent(inout) :: rowstart
    integer, dimension(rowstart(nnodes0+1)-1), intent(inout) :: adjacency
    integer, dimension(nnodes0),               intent(inout) :: part
    integer, dimension(:,:),         optional, intent(in)    :: vertex_weight
    integer, dimension(:),           optional, intent(in)    :: edge_weight

#ifdef HAVE_MPI
#ifdef HAVE_PARMETIS
    integer :: weightflag, vwgt, vsize, adjwgt
    integer :: numflag, edgecut
    integer, dimension(5) :: options

    integer :: ncon = 1

    real(system_r4), dimension(:,:), pointer :: tpwgts
    real(system_r4), dimension(:),   pointer :: ubvec

    continue

    weightflag = 0
    if ( present( vertex_weight ) ) weightflag = weightflag + 2
    if ( present( edge_weight ) ) weightflag = weightflag + 1

    if ( present( vertex_weight ) ) then
      ncon = size( vertex_weight, 1 )
    else
      ncon = 1
    end if
    call my_alloc_ptr( tpwgts, ncon, npart )
    call my_alloc_ptr( ubvec, npart )

    vwgt = 0
    vsize = 0
    adjwgt = 0
    numflag = 0 ! 0 C, 1 FORTRAN array index
    tpwgts = 1.0_system_r4/real(npart,system_r4)
    ubvec = 1.01_system_r4

    options(1) = 1
    options(2) = 0 ! add in deg info with 1+2+4+8+32
    options(3) = 15
    options(4) = 1

    if (0 == numflag) then
      nodeoffset = nodeoffset - 1
      rowstart = rowstart - 1
      adjacency = adjacency - 1
      part = part - 1
    end if

    if ( present( vertex_weight ) ) then
      if ( present( edge_weight ) ) then
        call ParMETIS_V3_PartKway(                                             &
          nodeoffset, rowstart, adjacency,                                     &
          vertex_weight, edge_weight, weightflag,                              &
          numflag,                                                             &
          ncon, npart, tpwgts, ubvec,                                          &
          options, edgecut, part, lmpi_comm )
      else
        call ParMETIS_V3_PartKway(                                             &
          nodeoffset, rowstart, adjacency,                                     &
          vertex_weight, adjwgt, weightflag,                                   &
          numflag,                                                             &
          ncon, npart, tpwgts, ubvec,                                          &
          options, edgecut, part, lmpi_comm )
      end if
    else
      if ( present( edge_weight ) ) then
        call ParMETIS_V3_PartKway(                                             &
          nodeoffset, rowstart, adjacency,                                     &
          vwgt, edge_weight, weightflag,                                       &
          numflag,                                                             &
          ncon, npart, tpwgts, ubvec,                                          &
          options, edgecut, part, lmpi_comm )
      else
        call ParMETIS_V3_PartKway(                                             &
          nodeoffset, rowstart, adjacency,                                     &
          vwgt, adjwgt, weightflag,                                            &
          numflag,                                                             &
          ncon, npart, tpwgts, ubvec,                                          &
          options, edgecut, part, lmpi_comm )
      end if
    end if

    if (0 == numflag) then
      nodeoffset = nodeoffset + 1
      rowstart = rowstart + 1
      adjacency = adjacency + 1
      part = part + 1
    end if

    deallocate( tpwgts, ubvec )

#else
    if (.false.) write(*,*) nodeoffset(1), part(1), adjacency(1)
    if (.false.) write(*,*) vertex_weight(1,1), present(edge_weight)
    write(*,*) 'Warning! partKway was not compiled with -DHAVE_PARMETIS'
#endif
#else
    if (.false.) write(*,*) nodeoffset(1), part(1), adjacency(1)
    if (.false.) write(*,*) vertex_weight(1,1), present(edge_weight)
    write(*,*) 'Warning! partKway was not compiled with -DHAVE_MPI ',          &
               'ParMetis disabled'
#endif
  end subroutine partKway

end module parmetis_interface

!================================= DFV_EB ====================================80
!
! Edge-based jacobian terms for cell-centered.
!
! Incoming variables conservative.
!
!=============================================================================80
  pure function dfv_eb( xnorm, ynorm, znorm, area, ql, qr, amutl, amutr,       &
                        dx, dy, dz)

    real(dp),               intent(in) :: xnorm, ynorm, znorm, area
    real(dp),               intent(in) :: amutl, amutr, dx, dy, dz

    real(dp), dimension(5), intent(in) :: ql, qr

    real(dp), dimension(5,5,2) :: dfv_eb

    integer :: eq

    real(dp) :: amutf, ds2i

    real(dp), dimension(3)   :: dgrad
    real(dp), dimension(5)   :: qt
    real(dp), dimension(3,5) :: qtgrad
    real(dp), dimension(  2) :: dtgradn
    real(dp), dimension(3,2) :: dugrad, dvgrad, dwgrad
    real(dp), dimension(5,2) :: dqt, qta

    integer, parameter :: eqn_set = 0

  continue

    qta(1:5,1) = qt_from_qc( eqn_set, 5, ql(1:5) )
    qta(1:5,2) = qt_from_qc( eqn_set, 5, qr(1:5) )

    qt(1:5)    = 0.5_dp*( qta(1:5,1) + qta(1:5,2) )
    dqt(1:5,1) = 0.5_dp
    dqt(1:5,2) = 0.5_dp

    amutf = 0.5_dp*( amutl + amutr )

    ds2i = 1.0_dp/( dx*dx + dy*dy + dz*dz )

    dgrad(1) = dx*ds2i
    dgrad(2) = dy*ds2i
    dgrad(3) = dz*ds2i

    dugrad(:,2) = dgrad(:)
    dvgrad(:,2) = dgrad(:)
    dwgrad(:,2) = dgrad(:)
    dtgradn( 2) = xnorm*dgrad(1) + ynorm*dgrad(2) + znorm*dgrad(3)

    dugrad(:,1) = -dugrad(:,2)
    dvgrad(:,1) = -dvgrad(:,2)
    dwgrad(:,1) = -dwgrad(:,2)
    dtgradn( 1) = -dtgradn( 2)

    do eq=1,5
      qtgrad(:,eq) = dgrad(:)*( qta(eq,2) - qta(eq,1) )
    enddo

    dfv_eb(:,:,1:2) = dfvf( xnorm, ynorm, znorm, area, amutf, qt, dqt,      &
                            qtgrad, dugrad, dvgrad, dwgrad, dtgradn, 2, qta )

  end function dfv_eb

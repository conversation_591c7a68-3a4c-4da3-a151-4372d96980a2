!================================= FLUX_STVD =================================80
!
! This routine computes the fluxes using the Yee and Harten symmetric TVD scheme
! and compute the contribution to the flux balance
! This version has been modified from <PERSON> original interpretation to
! allow 2nd order reconstruction of data at virtual points and interfaces to
! better mimic a structured grid implementation of the Yee symmetric TVD scheme
!
! Note that this subroutine uses primitive variables
!
!=============================================================================80

  pure function flux_stvd( rx1, ry1, rz1, rx2, ry2, rz2,                       &
                           xnorm, ynorm, znorm, area, vol1, vol2, mu,          &
                           gradx1, grady1, gradz1, phi1,                       &
                           gradx2, grady2, gradz2, phi2,                       &
                           face_speed, ql, qr, q0, enrgyl, enrgyr,             &
                           density, sonic_k, dpdq,                             &
                           pel,per, eps1, eps2, second, ndim, n_tot, eqn_set )

    use kinddefs,        only : dp
    use solution_types,  only : compressible
    use fun3d_constants, only : my_0, my_half, my_1, my_2, my_4
    use info_depr,       only : adptv_entropy_fix, ivisc
    use inviscid_flux,   only : entropy_fix, iflim, iflim_teq,                 &
                                gen_turb_advectn_ho
    use fluid,           only : gamma
    use generic_gas_map, only : n_species, n_momx, n_momy, n_momz,             &
                                n_etot, n_pjac, n_energy, n_energy_last,       &
                                n_turb_g, n_sonic_k
    use turb_gen,        only : n_turb_ke, n_dis_nutl, betat, coupled_tke
    use shared_gas_variables,  only : i_electron
    integer,                   intent(in) :: ndim, n_tot, eqn_set
    real(dp),                  intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp),                  intent(in) :: xnorm, ynorm, znorm, area
    real(dp),                  intent(in) :: vol1, vol2
    real(dp),                  intent(in) :: face_speed
    real(dp),                  intent(in) :: phi1, phi2
    real(dp),                  intent(in) :: eps1, eps2
    real(dp),                  intent(in) :: enrgyl, enrgyr
    real(dp),                  intent(in) :: density, mu, pel, per
    real(dp), dimension(ndim), intent(in) :: gradx1, grady1, gradz1
    real(dp), dimension(ndim), intent(in) :: gradx2, grady2, gradz2
    real(dp), dimension(ndim), intent(in) :: q0
    real(dp), dimension(n_tot),  intent(in) :: ql, qr
    real(dp),                    intent(in) :: sonic_k
    real(dp), dimension(n_pjac), intent(in) :: dpdq
    logical,                     intent(in) :: second

    integer :: nmean, ntrbb, ntrbe

    real(dp), dimension(ndim+1) :: flux_stvd

    real(dp) :: rho, rhol, rhor, ul, ur, vl, vr, wl, wr, pressl, pressr
    real(dp) :: q2l, q2r, ubarl, ubarr
    real(dp) :: cl, cr
    real(dp) :: wat, u, v, w, q2a, h, ubar, c, c2
    real(dp) :: ubar_fs, ubar_fsl, ubar_fsr
    real(dp) :: switch, switchv
    real(dp) :: ttdx, ttdy, ttdz
    real(dp) :: vbar, wbar
    real(dp) :: dqpp, dqpm, dqp0, dqp0p
    real(dp) :: epsa
    real(dp) :: pressl_tot, pressr_tot

    real(dp), dimension(3)    :: eig, abseig
    real(dp), dimension(ndim) :: fluxl, fluxr, df, dq
    real(dp), dimension(ndim) :: dqp, dqpl, dqpr, dqpfl
    real(dp), dimension(3,2)  :: lm

    integer,  parameter :: behavior = 1
    integer,  parameter :: powerv = 4
    real(dp), parameter :: poweri = my_1
    real(dp), parameter :: laplcc = my_4

    real(dp) :: sfac, sfac1

  continue

!   Compute the number of mean flow eq.s

    nmean = ndim - n_turb_g

!   Compute the beginning and ending number of the turbulence model eq.s

    ntrbb = nmean + 1
    ntrbe = ndim

!   Get left and right state primitive variables

    if(n_species == 1)then
      rhol   = ql(1)
      rhor   = qr(1)
    else
      rhol   = sum(ql(1:n_species))
      rhor   = sum(qr(1:n_species))
    end if

    ul     = ql(n_momx)
    vl     = ql(n_momy)
    wl     = ql(n_momz)
    pressl = ql(n_etot)
    pressl_tot = pressl

    ur     = qr(n_momx)
    vr     = qr(n_momy)
    wr     = qr(n_momz)
    pressr = qr(n_etot)
    pressr_tot = pressr

!   Compute the remaining needed left and right state variables:

    q2l    = ul*ul + vl*vl + wl*wl
    ubarl  = xnorm*ul + ynorm*vl + znorm*wl

    q2r    = ur*ur + vr*vr + wr*wr
    ubarr  = xnorm*ur + ynorm*vr + znorm*wr

!   Compute Roe averages

    rho  = density
    wat  = rho/(rho + rhor)
    u    = q0(n_momx)
    v    = q0(n_momy)
    w    = q0(n_momz)
    h    = q0(n_etot)
    q2a  = u*u + v*v + w*w
    c    = sonic_k
    c2   = c**2
    ubar = xnorm*u + ynorm*v + znorm*w

!   Add normal face speed to the contravariant velocity terms

    ubar_fsl = ubarl - face_speed
    ubar_fsr = ubarr - face_speed
    ubar_fs  = ubar  - face_speed

!   Now compute eigenvalues, eigenvectors, and strengths

    eig(1) = ubar_fs + c
    eig(2) = ubar_fs - c
    eig(3) = ubar_fs

!   Eigenvalue limiting either adaptive or constant

    switch = my_0 ! not switching off limiter

    if (adptv_entropy_fix .or. entropy_fix) then

!     Compute feature detection switch

      switch = iswch_coef(rx1,ry1,rz1,rx2,ry2,rz2,                             &
                          gradx1(n_etot),grady1(n_etot),gradz1(n_etot),        &
                          gradx2(n_etot),grady2(n_etot),gradz2(n_etot),        &
                          pressl,pressr,phi1,phi2,                             &
                          ubarl,ubarr,q2l,q2r,q2a,c2,                          &
                          laplcc,poweri,behavior)

!     Compute the cell face reynolds number to make the extra dissipation vanish
!     on low Reynolds number cells faces

      if (ivisc >= 2) then
        switchv  = vswch_coef(wat,rho,q2l,ubarl,q2r,ubarr,ubar,c,vol1,vol2,    &
                              area, powerv,mu)
      else
        switchv = my_0
      end if

    end if

!   Limit the eigenvalues

    abseig = roe_efix_u(q2l,q2r,ubarl,ubarr,wat,c,ubar_fs,switchv,eig)

!   Compute the left and right state 1st order fluxes

    if(n_species==1)then
      fluxl(1) = ubar_fsl*rhol
      fluxr(1) = ubar_fsr*rhor
    else
      fluxl(1:n_species) = ubar_fsl*ql(1:n_species)
      fluxr(1:n_species) = ubar_fsr*qr(1:n_species)
    end if
    if(n_energy>1)then
      fluxl(n_energy_last) = ubar_fsl*rhol*ql(n_energy_last)
      fluxr(n_energy_last) = ubar_fsr*rhor*qr(n_energy_last)
      if(i_electron > 0)then
        fluxl(n_energy_last) = fluxl(n_energy_last) + ubar_fsl*pel
        fluxr(n_energy_last) = fluxr(n_energy_last) + ubar_fsr*per
      end if
    end if
    if(n_turb_g > 0)then
      fluxl(n_dis_nutl) = ubar_fsl*rhol*ql(n_dis_nutl)
      fluxr(n_dis_nutl) = ubar_fsr*rhor*qr(n_dis_nutl)
      if(n_turb_g > 1)then
        fluxl(n_turb_ke) = ubar_fsl*rhol*ql(n_turb_ke)
        fluxr(n_turb_ke) = ubar_fsr*rhor*qr(n_turb_ke)
        if(betat > my_0)then
          pressl_tot = pressl + betat*rhol*ql(n_turb_ke)
          pressr_tot = pressr + betat*rhor*qr(n_turb_ke)
        end if
      end if
    end if
    fluxl(n_momx) = rhol*ubar_fsl*ul+xnorm*pressl_tot
    fluxl(n_momy) = rhol*ubar_fsl*vl+ynorm*pressl_tot
    fluxl(n_momz) = rhol*ubar_fsl*wl+znorm*pressl_tot
    fluxl(n_etot) = ubar_fsl*enrgyl + ubar_fsl*pressl_tot

    fluxr(n_momx) = rhor*ubar_fsr*ur+xnorm*pressr_tot
    fluxr(n_momy) = rhor*ubar_fsr*vr+ynorm*pressr_tot
    fluxr(n_momz) = rhor*ubar_fsr*wr+znorm*pressr_tot
    fluxr(n_etot) = ubar_fsr*enrgyr + ubar_fsr*pressr_tot

!   Compute the tangency vectors

    lm = tang_vecs(xnorm,ynorm,znorm)

!
!   Arrangement of virtual nodes and interfaces for the STVD2 scheme
!
!          vinterface  interface   vinterface
!            +1/2        +3/2        +5/2
!        #     !     *     |     *     !     #
!     vnode0       node1       node2      vnode3
!

    vbar = u*lm(1,1) + v*lm(2,1) + w*lm(3,1)
    wbar = u*lm(1,2) + v*lm(2,2) + w*lm(3,2)

!   Form the change in char. variables at the interface: alfa(+3/2)

!   Form the jumps in the primitive variables at the interface (+3/2)
!   delta_q(+3/2) = q_2-q_1

    sfac = rho*c2
    dq(1:ndim) = qr(1:ndim) - ql(1:ndim)
    dqp = characteristic_difference(dq, q0, rho, c, xnorm, ynorm, znorm,       &
                                    lm, n_energy, n_species, n_etot,           &
                                    n_momx, n_momy, n_momz, n_turb_g,          &
                                    n_turb_ke, n_dis_nutl, n_energy_last,      &
                                    betat, ndim, sfac)
!   Compute the second order change in char. variables across the interface by:
!   Compute the distance vector from node1 to node2
!   N.B: They are premultiplied by 2 for the stvd2 scheme

    ttdx = my_2*(rx1 - rx2)
    ttdy = my_2*(ry1 - ry2)
    ttdz = my_2*(rz1 - rz2)

    second_order: if(second)then

      ttdx = (rx1 - rx2)
      ttdy = (ry1 - ry2)
      ttdz = (rz1 - rz2)

      if(eqn_set==compressible)then
        cl     = sqrt(gamma*pressl/rhol)
      else
        cl = ql(n_sonic_k(1))
      end if
      dq(1:ndim) = gradx1(1:ndim)*ttdx+grady1(1:ndim)*ttdy+gradz1(1:ndim)*ttdz
      sfac1 = rhol*cl**2
      dqpl = characteristic_difference(dq, ql, rhol, cl, xnorm, ynorm, znorm,  &
                                       lm, n_energy, n_species, n_etot,        &
                                       n_momx, n_momy, n_momz, n_turb_g,       &
                                       n_turb_ke, n_dis_nutl, n_energy_last,   &
                                       betat, ndim, sfac1)
      if(eqn_set==compressible)then
        cr     = sqrt(gamma*pressr/rhor)
      else
        cr = qr(n_sonic_k(1))
      end if
      dq(1:ndim) = gradx2(1:ndim)*ttdx+grady2(1:ndim)*ttdy+gradz2(1:ndim)*ttdz
      sfac1 = rhor*cr**2

      dqpr = characteristic_difference(dq, qr, rhor, cr, xnorm, ynorm, znorm,  &
                                       lm, n_energy, n_species, n_etot,        &
                                       n_momx, n_momy, n_momz, n_turb_g,       &
                                       n_turb_ke, n_dis_nutl, n_energy_last,   &
                                       betat, ndim, sfac1)

!   (5) Compute the limited higher order change
!       in the char. like variables across the interface

      dqpfl = my_half*(dqpl + dqpr)

!     if (iflim == 3) then
!       dqpfl = my_2*minmodv(dqpl, minmodv(dqpr, minmodv(dqp, my_half*dqpfl, &
!               ndim),ndim),ndim)
!     else if (iflim == 4) then
!       dqpfl = vlflxlv(dqpl, vlflxlv(dqpr,vlflxlv(dqp,dqpfl,ndim),ndim),ndim)
!     else if (iflim == 5) then
!       epsa  = my_half*(eps1+eps2)
!       dqpfl = vaflxlv(dqpl, vaflxlv(vaflxlv(dqp,dqpfl,epsa,ndim),dqpr,eps2,&
!               ndim),eps1,ndim)
!     else if (iflim == 6) then
!       epsa  = my_half*(eps1+eps2)
!       dqpfl = smthlmv(dqpl, smthlmv(smthlmv(dqp, dqpfl, epsa, ndim), dqpr, &
!               eps2, ndim), eps1, ndim)
!     else if (iflim == 7) then
!       dqpfl = my_half*minmodv(my_2*dqp, minmodv(dqpl+dqp, dqpr+dqp,ndim),  &
!               ndim)
!     end if

!     Limit the mean flow equation fluxes

      if (iflim == 3) then
        dqpfl(1:nmean) = my_2*minmodv(dqpl(1:nmean),                           &
                                minmodv(dqpr(1:nmean),                         &
                                  minmodv(dqp(1:nmean),                        &
                                          my_half*dqpfl(1:nmean),              &
                                          nmean),                              &
                                        nmean),                                &
                                      nmean)
      else if (iflim == 4) then
        dqpfl(1:nmean) = vlflxlv(dqpl(1:nmean),                                &
                                 vlflxlv(dqpr(1:nmean),                        &
                                         vlflxlv(dqp(1:nmean),                 &
                                                 dqpfl(1:nmean),               &
                                                 nmean),                       &
                                         nmean),                               &
                                 nmean)
      else if (iflim == 5) then
        epsa  = my_half*(eps1+eps2)
        dqpfl(1:nmean) = vaflxlv(dqpl(1:nmean),                                &
                                 vaflxlv(vaflxlv(dqp(1:nmean),                 &
                                                 dqpfl(1:nmean),epsa,          &
                                                 nmean),                       &
                                         dqpr,eps2,nmean),                     &
                                 eps1,nmean)

      else if (iflim == 6) then
        epsa  = my_half*(eps1+eps2)
        dqpfl(1:nmean) = smthlmv(dqpl(1:nmean),                                &
                                 smthlmv(smthlmv(dqp(1:nmean),                 &
                                                 dqpfl(1:nmean),epsa,          &
                                                 nmean),                       &
                                         dqpr(1:nmean),eps2,nmean),            &
                                 eps1,nmean)
      else if (iflim == 7) then
        dqpfl(1:nmean) = my_half*minmodv(my_2*dqp(1:nmean),                    &
                                         minmodv(dqpl(1:nmean)+dqp(1:nmean),   &
                                                 dqpr(1:nmean)+dqp(1:nmean),   &
                                                 nmean),                       &
                                         nmean)
      end if

      dqp(1:nmean) = dqp(1:nmean) - dqpfl(1:nmean)

!     Limit the turbulence model equation(s) fluxes
!     Allow a different limiter for the turbulence equations
!     or allow 1st order advection

      if (n_turb_g > 0 .and. gen_turb_advectn_ho) then

        if (iflim_teq == 3) then
          dqpfl(ntrbb:ntrbe) = my_2*minmodv(dqpl(ntrbb:ntrbe),                 &
                                  minmodv(dqpr(ntrbb:ntrbe),                   &
                                    minmodv(dqp(ntrbb:ntrbe),                  &
                                            my_half*dqpfl(ntrbb:ntrbe),        &
                                            n_turb_g),                         &
                                          n_turb_g),                           &
                                        n_turb_g)
        else if (iflim_teq == 4) then
          dqpfl(ntrbb:ntrbe) = vlflxlv(dqpl(ntrbb:ntrbe),                      &
                                   vlflxlv(dqpr(ntrbb:ntrbe),                  &
                                           vlflxlv(dqp(ntrbb:ntrbe),           &
                                                   dqpfl(ntrbb:ntrbe),         &
                                                   n_turb_g),                  &
                                           n_turb_g),                          &
                                       n_turb_g)
        else if (iflim_teq == 5) then
          epsa  = my_half*(eps1+eps2)
          dqpfl(ntrbb:ntrbe) = vaflxlv(dqpl(ntrbb:ntrbe),                      &
                                   vaflxlv(vaflxlv(dqp(ntrbb:ntrbe),           &
                                                   dqpfl(ntrbb:ntrbe),epsa,    &
                                                   n_turb_g),                  &
                                           dqpr,eps2,n_turb_g),                &
                                       eps1,n_turb_g)
        else if (iflim_teq == 6) then
          epsa  = my_half*(eps1+eps2)
          dqpfl(ntrbb:ntrbe) = smthlmv(dqpl(ntrbb:ntrbe),                      &
                                   smthlmv(smthlmv(dqp(ntrbb:ntrbe),           &
                                                   dqpfl(ntrbb:ntrbe),epsa,    &
                                                   n_turb_g),                  &
                                           dqpr(ntrbb:ntrbe),eps2,n_turb_g),   &
                                       eps1,n_turb_g)
        else if (iflim_teq == 7) then
          dqpfl(ntrbb:ntrbe) = my_half*minmodv(my_2*dqp(ntrbb:ntrbe),          &
                                    minmodv(dqpl(ntrbb:ntrbe)+dqp(ntrbb:ntrbe),&
                                            dqpr(ntrbb:ntrbe)+dqp(ntrbb:ntrbe),&
                                            n_turb_g),                         &
                                               n_turb_g)
        end if

        dqp(ntrbb:ntrbe) = dqp(ntrbb:ntrbe) - dqpfl(ntrbb:ntrbe)

      end if

    end if second_order

!   Compute the eigenvalue*(change in the char. variable)

    dqp = dqp/sfac
    dqp(1:n_species) = abseig(3)*dqp(1:n_species)/c2
    dqp(n_momx)      = abseig(3)*dqp(n_momx)/c
    dqp(n_momy)      = abseig(3)*dqp(n_momy)/c
    dqp(n_momz)      = abseig(1)*dqp(n_momz)/c2
    dqp(n_etot)      = abseig(2)*dqp(n_etot)/c2
    dqp0             = sum(dqp(1:n_species))

    if(n_energy>1)then
      dqp(n_energy_last) = abseig(3)*dqp(n_energy_last)/c2
    end if
    if(n_turb_g > 0)then
      dqp(n_turb_ke:n_dis_nutl) = abseig(3)*dqp(n_turb_ke:n_dis_nutl)/c2
    end if

    dqpp   = my_half*(dqp(n_momz) + dqp(n_etot))
    dqpm   = my_half*(dqp(n_momz) - dqp(n_etot))*c
    dqp0p  = dqp0 + dqpp

    df(1:n_species)  = dqp(1:n_species) + q0(1:n_species)*dqpp/rho
    df(n_momx)  = q0(n_momx)*dqp0p + lm(1,1)*dqp(n_momx)                       &
                + lm(1,2)*dqp(n_momy) + xnorm*dqpm
    df(n_momy)  = q0(n_momy)*dqp0p + lm(2,1)*dqp(n_momx)                       &
                + lm(2,2)*dqp(n_momy) + ynorm*dqpm
    df(n_momz)  = q0(n_momz)*dqp0p + lm(3,1)*dqp(n_momx)                       &
                + lm(3,2)*dqp(n_momy) + znorm*dqpm
    df(n_etot)  = q2a*dqp0                                                     &
                - sum(dpdq(1:n_species)*dqp(1:n_species))/dpdq(n_momx)         &
                + vbar*dqp(n_momx) + wbar*dqp(n_momy) + ubar*dqpm + h*dqpp
    if(n_energy > 1)then
      df(n_energy_last) = q0(n_energy_last)*dqpp + dqp(n_energy_last)
      df(n_etot) = df(n_etot) - dpdq(n_momy)*dqp(n_energy_last)/dpdq(n_momx)
    end if
    if(n_turb_g > 0)then
      if(n_turb_g > 1)then
        df(n_turb_ke) = q0(n_turb_ke)*dqpp + dqp(n_turb_ke)
        if(coupled_tke)                                                        &
        df(n_etot) = df(n_etot) + (my_1 - betat/dpdq(n_momx))*dqp(n_turb_ke)
      end if
      df(n_dis_nutl) = q0(n_dis_nutl)*dqpp + dqp(n_dis_nutl)
    end if

!   Compute the contribution to the flux balance
!   Remember to multiply by appropriate area on return

    flux_stvd(1:ndim) = my_half*(fluxl + fluxr - df)

    flux_stvd(ndim+1) = switch

  end function flux_stvd

module timedep_sensitivities

  use kinddefs,      only : dp
  use solution_getg, only : getg_type
  use comprow_types, only : crow_type

  implicit none

  private

  public :: unsteady_getgrad, set_up_grid_at_timelevel
  public :: update_temporal_adj_backplanes
  public :: update_geometric_backplanes

  real(dp), dimension(:,:), pointer :: res_gcl_save

  logical :: res_gcl_save_allocated = .false.
  logical :: getg_setup             = .false.
  logical :: nn_crow_setup          = .false.

  type(getg_type) :: getg
  type(crow_type), save :: nn_crow

  integer :: unit_73

  logical :: setup_xn = .false.

  real(dp), dimension(:), pointer :: xn, yn, zn

contains

!=============================== TIME_DEPENDENT_GETGRAD ======================80
!
!  Driver routine to call getgrad
!
!=============================================================================80

  subroutine unsteady_getgrad(design,grid,sadj,soln,crowf,flow_dir,nml_path,   &
                              istep,dldx)

    use lmpi,             only : lmpi_master
    use design_types,     only : design_type
    use info_depr,        only : physical_timestep, ngrid
    use fun3d_constants,  only : my_0
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,     only : sadj_type
    use comprow_types,    only : crow_flow
    use file_utils,       only : available_unit
    use adjoint_switches, only : afc_jets, get_dldx

    integer, intent(in) :: istep

    real(dp), dimension(:,:,:), intent(inout) :: dldx

    type(sadj_type),   intent(inout) :: sadj
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(inout) :: design

    type(grid_type), dimension(ngrid), intent(inout) :: grid
    type(crow_flow), dimension(ngrid), intent(inout) :: crowf

    character(*), intent(in) :: flow_dir
    character(*), intent(in) :: nml_path

    integer :: ibody, ifcn, idv

    logical, save :: initialized = .false.

  continue

! Zero out the derivative sums if its the first time through here
! Open golden file

    if ( .not. initialized ) then

      design%alpha_derivative(:) = my_0
      design%mach_derivative(:)  = my_0
      design%yaw_derivative(:)   = my_0

      do ibody = 1, design%nbodies
        do ifcn = 1, design%nfunctions

          do idv = 1, design%rigid_data(ibody)%ndv
            design%rigid_data(ibody)%shape_derivative(ifcn,idv) = my_0
          end do

          do idv = 1, design%body_data(ibody)%ndv
            design%body_data(ibody)%shape_derivative(ifcn,idv) = my_0
          end do

        end do
      end do

      if ( design%auxiliary_data_present ) then
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%auxiliary%body_data(ibody)%ndv
             design%auxiliary%body_data(ibody)%shape_derivative(ifcn,idv) = my_0
            end do
          end do
        end do
      endif

      if ( design%kinematic_data_present ) then
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%kinematic%body_data(ibody)%ndv
             design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv) = my_0
            end do
          end do
        end do
      endif

      if ( design%trimming_data_present ) then
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%trimming%body_data(ibody)%ndv
             design%trimming%body_data(ibody)%shape_derivative(ifcn,idv) = my_0
            end do
          end do
        end do
      endif

      dldx = my_0

      initialized = .true.

      if ( lmpi_master ) then
        unit_73 = available_unit()
        open(unit_73,file='debug_timedepadj')
      endif

    endif

! Pick up alpha, yaw, and Mach derivatives if requested

    if ( design%alpha_active .or. design%yaw_active ) then
      call alpha_driver(design,grid(1),sadj,soln)
    endif
    if ( design%mach_active ) call mach_driver(design,grid(1),sadj,soln)

! Pick up jet derivatives if requested

    if ( afc_jets ) call jet_driver(design,grid(1),sadj,soln)

! Pick up rigid and shape derivatives if requested

    if ( design%shape_active .or. design%rigid_active .or. get_dldx .or.       &
         design%kinematic%active .or. design%trimming%active ) then
      call shape_driver(soln,design,grid,sadj,crowf,flow_dir,nml_path,dldx)
    endif

! Write derivatives to file for plotting

    if ( lmpi_master ) call derivs_to_tecplot(grid(1),design,istep)

! Close golden file

    if ( physical_timestep == 0 .and. lmpi_master ) close(73)

  end subroutine unsteady_getgrad


!=============================== SHAPE_DRIVER ================================80
!
!  Driver routine to compute shape sensitivity stuff
!
!=============================================================================80
  subroutine shape_driver(soln,design,grid,sadj,crowf,flow_dir,nml_path,dldx)

    use lmpi,                 only : lmpi_master, lmpi_synchronize
    use design_types,         only : design_type
    use design_io_helpers,    only : set_up_design
    use info_depr,            only : physical_timestep, ngrid, ncyc,           &
                                     simulation_time
    use nml_nonlinear_solves, only : itime, dt
    use solution_types,       only : soln_type
    use designs,              only : free_design
    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use moving_body_types,    only : moving_body
    use nml_grid_motion,      only : n_moving_bodies
    use fun3d_constants,      only : my_0
    use allocations,          only : my_alloc_ptr
    use comprow_types,        only : crow_flow
    use solution_adj,         only : sadj_type
    use custom_transforms,    only : tilt_rotor_transform, custom_kinematics,  &
                                     tilt_rotor
    use grid_motion,          only : build_up_inherited_motion
    use grid_motion_helpers,  only : get_inverse_transform,                    &
                                     setup_transform_matrix
    use adjoint_switches,     only : get_dldx

    real(dp), dimension(:,:,:), intent(inout) :: dldx

    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(inout) :: design
    type(soln_type),   intent(inout) :: soln

    type(grid_type), dimension(ngrid), intent(inout) :: grid
    type(crow_flow), dimension(ngrid), intent(inout) :: crowf

    character(*), intent(in) :: flow_dir
    character(*), intent(in) :: nml_path

    integer :: ibody, ifcn, idv

    real(dp) :: raw_deriv

    real(dp), dimension(3,grid(1)%nnodes0,design%nfunctions) :: gridadj_rhs
    real(dp), dimension(:,:,:), pointer, save :: tlambdag
    real(dp), dimension(:,:,:), pointer, save :: tot_gridadj_rhs

    real(dp), dimension(4,4,n_moving_bodies) :: t_local

    logical :: multiply_by_transform

    type(design_type) :: temp_design

  continue

! Store off a copy of Xn if we will need it later

    if ( trim(grid(1)%grid_motion) == 'deform' .and.                           &
         (design%rigid_active     .or.                                         &
          design%kinematic%active .or.                                         &
          design%trimming%active) ) then

      if ( .not. setup_xn ) then
        call my_alloc_ptr(xn,grid(1)%nnodes01)
        call my_alloc_ptr(yn,grid(1)%nnodes01)
        call my_alloc_ptr(zn,grid(1)%nnodes01)
        setup_xn = .true.
      endif

      xn(1:grid(1)%nnodes01) = grid(1)%x(1:grid(1)%nnodes01)
      yn(1:grid(1)%nnodes01) = grid(1)%y(1:grid(1)%nnodes01)
      zn(1:grid(1)%nnodes01) = grid(1)%z(1:grid(1)%nnodes01)

    endif

    t_local = 0.0_dp
    t_local(1,1,:) = 1.0_dp
    t_local(2,2,:) = 1.0_dp
    t_local(3,3,:) = 1.0_dp
    t_local(4,4,:) = 1.0_dp

! First evaluate the RHS of the grid adjoint equation

    if ( lmpi_master ) write(*,*)
    if ( lmpi_master ) write(*,*) 'CALLING SENSSHAPE...'
    if ( lmpi_master ) write(*,*)

    call sensshape_driver(grid(1),soln,sadj,design,gridadj_rhs)

! If motion is involved, update the translation/rotation vectors
! and the transform matrices

    if ( trim(grid(1)%grid_motion) == 'rigid' .or.                             &
         trim(grid(1)%grid_motion) == 'deform' ) then

      call update_rigid_vectors(design)

      if ( tilt_rotor ) then
        call tilt_rotor_transform(simulation_time)
      else

        do ibody = 1, n_moving_bodies

          if ( trim(moving_body(ibody)%motion_driver) == 'custom' ) then
            call custom_kinematics(design%kinematic%body_data(ibody)%ndv,      &
                                   simulation_time,                            &
                                   design%kinematic%body_data(ibody)%value,0,  &
                                   t=moving_body(ibody)%transform_matrix,      &
                                   xcg=moving_body(ibody)%xcg,                 &
                                   ycg=moving_body(ibody)%ycg,                 &
                                   zcg=moving_body(ibody)%zcg)
          else
            call setup_transform_matrix(moving_body(ibody)%transform_matrix,   &
                                        moving_body(ibody)%rotation_vector,    &
                                        moving_body(ibody)%translation_vector)
          endif

          t_local(:,:,ibody) = moving_body(ibody)%transform_matrix

        end do

        call build_up_inherited_motion()

        do ibody = 1, n_moving_bodies
          call get_inverse_transform(moving_body(ibody)%transform_matrix,      &
                                     moving_body(ibody)%inv_transform)
        end do

      endif

    endif



! We will need the grid adjoint using the identity K matrix if we are at a
! positive timestep and any rigid, custom kinematic or trim variables are active

    need_identity_adjoint : if ( trim(grid(1)%grid_motion) == 'rigid' .and.    &
                                 physical_timestep > 0                .and.    &
                                 (design%rigid_active     .or.                 &
                                  design%kinematic%active .or.                 &
                                  design%trimming%active) ) then

! Set up a temporary design type to put the sensitivities into

      call set_up_design(soln%eqn_set, itime, temp_design)

      call rigid_mesh_adjoint(temp_design,grid(1),gridadj_rhs,t_local)

! Post-process this timestep's contribution to the rigid sensitivities

      do ibody = 1, temp_design%nbodies
        do ifcn = 1, temp_design%nfunctions
          do idv = 1, temp_design%rigid_data(ibody)%ndv
            raw_deriv=temp_design%rigid_data(ibody)%shape_derivative(ifcn,idv)
            design%rigid_data(ibody)%shape_derivative(ifcn,idv) =              &
            design%rigid_data(ibody)%shape_derivative(ifcn,idv) + dt*raw_deriv
          end do
        end do
      end do

! Post-process this timestep's contribution to custom kinematic sensitivities

      if ( design%kinematic_data_present ) then
        do ibody = 1, temp_design%nbodies
          do ifcn = 1, temp_design%nfunctions
            do idv = 1, temp_design%kinematic%body_data(ibody)%ndv
             raw_deriv =                                                       &
             temp_design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)
             design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv) =    &
             design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)      &
                                                                + dt*raw_deriv
            end do
          end do
        end do
      endif

! Post-process this timestep's contribution to trimming sensitivities

      if ( design%trimming_data_present ) then
        do ibody = 1, temp_design%nbodies
          do ifcn = 1, temp_design%nfunctions
            do idv = 1, temp_design%trimming%body_data(ibody)%ndv
             raw_deriv =                                                       &
              temp_design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)
             design%trimming%body_data(ibody)%shape_derivative(ifcn,idv) =     &
             design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)       &
                                                                + dt*raw_deriv
            end do
          end do
        end do
      endif

      call free_design(temp_design)

    endif need_identity_adjoint




! We will need the grid adjoint using the elasticity equations if any shape
! variables are active or we're doing morphing grids

    need_elasticity_adjoint : if ( (design%shape_active .or. get_dldx) .or.    &
                                  (trim(grid(1)%grid_motion) == 'deform' .and. &
                                   (design%rigid_active                        &
                                    .or.design%kinematic%active                &
                                    .or.design%trimming%active)) ) then

! Keep a running sum of (Tn-transpose)*(lambdag-n) over all positive timesteps
! if rigid motion is present

      if ( physical_timestep == ncyc ) then
        if ( trim(grid(1)%grid_motion) == 'rigid' ) then
          call my_alloc_ptr(tlambdag,3,grid(1)%nnodes0,design%nfunctions)
          tlambdag = my_0
        else if ( trim(grid(1)%grid_motion) == 'static' ) then
          call my_alloc_ptr(tot_gridadj_rhs,3,grid(1)%nnodes0,design%nfunctions)
          tot_gridadj_rhs = my_0
        endif
      endif

      if ( get_dldx ) then
        if ( trim(grid(1)%grid_motion) == 'static' ) then
          dldx = dldx + gridadj_rhs
        else
          dldx = gridadj_rhs
        endif
      endif

      if (trim(grid(1)%grid_motion) == 'rigid' .and. physical_timestep > 0) then
        call add_to_tlambdag(grid(1)%nnodes0,design%nfunctions,tlambdag,       &
                             gridadj_rhs,grid(1)%imesh)
      endif

      if ( trim(grid(1)%grid_motion) == 'static' ) then
        call add_to_tot_gridadj_rhs(grid(1)%nnodes0,design%nfunctions,         &
                                    tot_gridadj_rhs,gridadj_rhs)
      endif

! If we are back to time=0, then augment the grid adjoint RHS computed above
! by getgrad with the tlambdag sum and write it out to disk.  Then call the
! elasticity-based adjoint solver.

      find_lambdag : if ( physical_timestep == 0 .or.                          &
                          trim(grid(1)%grid_motion) == 'deform') then

        if ( physical_timestep == 0 ) then

          if ( trim(grid(1)%grid_motion) == 'rigid' ) then
            call update_gridadj_rhs(grid(1)%nnodes0,design%nfunctions,         &
                                    gridadj_rhs,tlambdag)
          else if ( trim(grid(1)%grid_motion) == 'static' ) then
            gridadj_rhs(:,:,:) = tot_gridadj_rhs(:,:,:)
          endif

        endif

        solve_elasticity : if ( design%shape_active     .or.                   &
                                design%rigid_active     .or.                   &
                                design%kinematic%active .or.                   &
                                design%trimming%active ) then

! Set up a temporary design type to put the sensitivities into

          call set_up_design(soln%eqn_set, itime, temp_design)

          if ( lmpi_master ) write(*,*)
          if ( lmpi_master ) write(*,*) 'CALLING ELASTICITY ADJOINT...'
          if ( lmpi_master ) write(*,*)

          if ( trim(grid(1)%grid_motion) == 'static' .or.                      &
               trim(grid(1)%grid_motion) == 'rigid' ) then
            multiply_by_transform = .false.
            call elasticity_driver(grid,crowf,temp_design,                     &
                                   gridadj_rhs,multiply_by_transform,t_local,  &
                                   flow_dir,nml_path)
          else
            multiply_by_transform = .true.
            if ( physical_timestep == 0 ) multiply_by_transform = .false.
            call elasticity_driver(grid,crowf,temp_design,                     &
                                   gridadj_rhs,multiply_by_transform,t_local,  &
                                   flow_dir,nml_path)
          endif

! Post-process this timestep's contribution to the rigid sensitivities

          if ( trim(grid(1)%grid_motion) == 'deform' .and.                     &
               design%rigid_active .and. physical_timestep > 0 ) then
            do ibody = 1, temp_design%nbodies
              do ifcn = 1, temp_design%nfunctions
                do idv = 1, temp_design%rigid_data(ibody)%ndv
                  if ( temp_design%rigid_data(ibody)%active(idv) ) then
                    raw_deriv =                                                &
                        temp_design%rigid_data(ibody)%shape_derivative(ifcn,idv)
                    design%rigid_data(ibody)%shape_derivative(ifcn,idv) =      &
                    design%rigid_data(ibody)%shape_derivative(ifcn,idv)        &
                                                                  + dt*raw_deriv
                  endif
                end do
              end do
            end do
          endif

! Post-process this timestep's contribution to custom kinematic sensitivities

          if ( design%kinematic_data_present ) then
           if ( trim(grid(1)%grid_motion) == 'deform' .and.                    &
               design%kinematic%active .and. physical_timestep > 0 ) then
            do ibody = 1, temp_design%nbodies
             do ifcn = 1, temp_design%nfunctions
              do idv = 1, temp_design%kinematic%body_data(ibody)%ndv
               if ( temp_design%kinematic%body_data(ibody)%active(idv) == 1)then
               raw_deriv =                                                     &
               temp_design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)
               design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv) =  &
               design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)    &
                                                                  + dt*raw_deriv
               endif
              end do
             end do
            end do
           endif
          endif

! Post-process this timestep's contribution to trimming sensitivities

          if ( design%trimming_data_present ) then
           if ( trim(grid(1)%grid_motion) == 'deform' .and.                    &
               design%trimming%active .and. physical_timestep > 0 ) then
            do ibody = 1, temp_design%nbodies
             do ifcn = 1, temp_design%nfunctions
              do idv = 1, temp_design%trimming%body_data(ibody)%ndv
               if ( temp_design%trimming%body_data(ibody)%active(idv) == 1) then
                raw_deriv =                                                    &
                temp_design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)
                design%trimming%body_data(ibody)%shape_derivative(ifcn,idv) =  &
                design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)    &
                                                                  + dt*raw_deriv
               endif
              end do
             end do
            end do
           endif
          endif

! Post-process this timestep's contribution to the shape sensitivities

          do ibody = 1, temp_design%nbodies
            do ifcn = 1, temp_design%nfunctions
              do idv = 1, temp_design%body_data(ibody)%ndv
               raw_deriv=temp_design%body_data(ibody)%shape_derivative(ifcn,idv)
               design%body_data(ibody)%shape_derivative(ifcn,idv) =            &
               design%body_data(ibody)%shape_derivative(ifcn,idv) + dt*raw_deriv
              end do
            end do
          end do

          call free_design(temp_design)

        endif solve_elasticity

      endif find_lambdag

    endif need_elasticity_adjoint







! Write the results to the screen for debugging

    do ibody = 1, design%nbodies
      do ifcn = 1, design%nfunctions

        if ( design%rigid_active ) then
          do idv = 1, design%rigid_data(ibody)%ndv
            if ( design%rigid_data(ibody)%active(idv) ) then
              if ( lmpi_master ) write(*,*) 'Current rigid derivative = ',     &
                             design%rigid_data(ibody)%shape_derivative(ifcn,idv)
              if ( lmpi_master ) write(unit_73,*)'Current rigid derivative = ',&
                             design%rigid_data(ibody)%shape_derivative(ifcn,idv)
            endif
          end do
        endif

        if ( design%kinematic%active ) then
         do idv = 1, design%kinematic%body_data(ibody)%ndv
          if ( design%kinematic%body_data(ibody)%active(idv) == 1 ) then
           if( lmpi_master ) write(*,*) 'Current kinematic derivative = ',     &
                    design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)
           if( lmpi_master ) write(unit_73,*)'Current kinematic derivative = ',&
                    design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)
          endif
         end do
        endif

        if ( design%trimming%active ) then
         do idv = 1, design%trimming%body_data(ibody)%ndv
          if ( design%trimming%body_data(ibody)%active(idv) == 1 ) then
           if( lmpi_master ) write(*,*) 'Current trimming derivative = ',      &
                    design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)
           if( lmpi_master ) write(unit_73,*)'Current trimming derivative = ', &
                    design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)
          endif
         end do
        endif

        if ( design%shape_active ) then
          do idv = 1, design%body_data(ibody)%ndv
            if ( design%body_data(ibody)%active(idv) == 1 ) then
              if ( lmpi_master ) write(*,*) 'Current shape derivative = ',     &
                              design%body_data(ibody)%shape_derivative(ifcn,idv)
              if ( lmpi_master ) write(unit_73,*)'Current shape derivative = ',&
                              design%body_data(ibody)%shape_derivative(ifcn,idv)
            endif
          end do
        endif

      end do
    end do

    call lmpi_synchronize()

  end subroutine shape_driver


!=============================== ELASTICITY_DRIVER ===========================80
!
!  Routine to interface with elasticity solver (transposed)
!
!=============================================================================80
  subroutine elasticity_driver(grid,crow,design,gridadj_rhs,                   &
                               multiply_by_transform,t_local,flow_dir,nml_path)

    use info_depr,            only : ntt, ivisc, ngrid, physical_timestep,     &
                                     simulation_time
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : moving_grid
    use grid_motion,          only : solver_parameters, update_rigid_grid
    use solution_types,       only : soln_type
    use grid_types,           only : grid_type, mass_type
    use relax_types,          only : relax_type
    use solution,             only : set_up_solution, set_global_scalars
    use comprow_types,        only : crow_flow
    use io,                   only : relax_schedule_gridmove
    use eqn_groups,           only : process_eqn_groups
    use movees_mixed,         only : movee_mixed
    use design_types,         only : design_type
    use designs,              only : compute_shape_gradient,                   &
                                     compute_shape_gradient_lm
    use allocations,          only : my_realloc_ptr
    use kinddefs,             only : dp
    use adjoint_switches,     only : outer_loop_krylov, low_mem_meshsens
    use custom_transforms,    only : compute_shape_gradient_f15, f15_flutter
    use nml_overset_data,     only : overset_flag
    use pundit,               only : pundit_flag
    use moving_body_types,    only : moving_body
    use nml_grid_motion,      only : n_moving_bodies

    type(design_type),                                  intent(inout) :: design
    type(crow_flow),        dimension(ngrid),           intent(inout) :: crow
    type(grid_type),        dimension(ngrid),           intent(inout) :: grid

    real(dp), dimension(3,grid(1)%nnodes0,design%nfunctions),                  &
                                                    intent(inout) :: gridadj_rhs

    real(dp), dimension(4,4,n_moving_bodies), intent(in) :: t_local

    character(*),           intent(in)    :: flow_dir
    character(*),           intent(in)    :: nml_path

    logical, intent(in) :: multiply_by_transform

    integer :: ncycle, body, ibody, sum_real, i, imesh_key, parent

    integer, dimension(n_moving_bodies) :: real_index

    real(dp), dimension(4,4) :: def_motion, identity

    logical :: start_from_previous

    logical, save :: init = .true.

    logical, dimension(design%nbodies) :: fake_body

    type(relax_type),                             save :: relaxation_elasticity
    type(soln_type), dimension(:),   allocatable, save :: soln_elasticity
    type(mass_type), dimension(:,:), allocatable, save :: surface_mesh

  continue

    identity = 0.0_dp
    identity(1,1) = 1.0_dp
    identity(2,2) = 1.0_dp
    identity(3,3) = 1.0_dp
    identity(4,4) = 1.0_dp

    first_time : if ( init ) then
      allocate(soln_elasticity(ngrid))
      allocate(surface_mesh(design%nbodies,ngrid))
    end if first_time

    surface_mesh(:,:)%itotal = 0

! Set up array to store corresponding suggar body number for each real body

    fake_body(:) = .false.

    do i = 1, n_moving_bodies
      fake_body(i) = moving_body(i)%fake_body
    end do

    real_index(:) = 0
    sum_real = 0
    do i = 1, n_moving_bodies
      if ( .not. moving_body(i)%fake_body ) then
        sum_real = sum_real + 1
        real_index(i) = sum_real
      endif
    end do

! Save off the solver parameters used to solve the adjoint equations

    call solver_parameters(save_adjoint=.true.)

    outer_loop_krylov = .false.

! Set solver parameters needed for solution of linear elasticity equations

    itime = 0
    ntt   = 1
    ivisc = 0

    if ( init ) then
      call set_global_scalars(soln_elasticity(1),-1)
      call set_up_solution(1,grid(1),crow(1),soln_elasticity(1))

! Expand the off-diagonals array to hold the ghost rows as well, since
! we will need to transpose the matrix eventually

      call my_realloc_ptr(soln_elasticity(1)%a_off, soln_elasticity(1)%njac,   &
                          soln_elasticity(1)%njac,  crow(1)%nnz01)

      call relax_schedule_gridmove(relaxation_elasticity,flow_dir)

      call process_eqn_groups(grid(1),crow(1),soln_elasticity(1),              &
                              relaxation_elasticity,1,flow_dir,nml_path)

    endif

! Save the parameters set above for use in solving the elasticity equations

    call solver_parameters(save_elasticity=.true.)

! If dynamic mesh, put fresh copy of the initial mesh in the current mesh arrays
! and perform any rigid transforms prior to evaluating K so we are consistent
! with the forward mode

    grab_and_orient_fresh_mesh : if ( moving_grid ) then

      grid(1)%x(:) = grid(1)%xat0(:)
      grid(1)%y(:) = grid(1)%yat0(:)
      grid(1)%z(:) = grid(1)%zat0(:)

      body_loop : do body = 1, n_moving_bodies

        if (trim(moving_body(body)%mesh_movement) == 'rigid') then

          def_motion = moving_body(body)%transform_matrix

        else if (trim(moving_body(body)%mesh_movement) == 'deform') then

          if ( overset_flag .or. pundit_flag ) then
            if ( moving_body(body)%parent_name == '' ) then
              def_motion = identity
            else
              parent = moving_body(body)%ancestors(1)
              def_motion = moving_body(parent)%transform_matrix
            endif
          else
            def_motion = identity
          end if

        end if

! If overset, must establish proper value of imesh to compare to

        imesh_key = real_index(body)

        if ( overset_flag .or. pundit_flag ) then
          if ( moving_body(body)%fake_body ) then
            search_for_real : do i = body+1, n_moving_bodies
              if ( .not. moving_body(i)%fake_body ) then
                imesh_key = real_index(i)
                exit search_for_real
              endif
            end do search_for_real
          endif
        endif

        call update_rigid_grid(grid(1)%nnodes01, grid(1)%xat0, grid(1)%yat0,   &
                               grid(1)%zat0, grid(1)%x, grid(1)%y, grid(1)%z,  &
                               grid(1)%imesh, def_motion, imesh_key,           &
                               moving_body(body)%mesh_movement)

      end do body_loop

    endif grab_and_orient_fresh_mesh

! Set some parameters for movee_mixed

    ncycle              = 1
    start_from_previous = .false.

! Reverse the sign on the RHS vector, since we call dgmres to solve the system,
! and the first thing it does is change the sign

    gridadj_rhs = -gridadj_rhs

! Solve the mesh adjoint problem
! surface_mesh is a dummy argument
! gridadj_rhs contains lambda-g on return

    call movee_mixed(grid, soln_elasticity, crow, surface_mesh,                &
                     relaxation_elasticity, design%nfunctions, design%nbodies, &
                     ncycle, nml_path,                                         &
                     .true., start_from_previous=start_from_previous,          &
                     gridadj_rhs=gridadj_rhs)

! Restore the solver parameters used to solve the adjoint equations

    call solver_parameters(restore_adjoint=.true.)

! Compute rigid gradient vector using adjoint solution just computed
! and write back out to rubber.data

    rigid_derivs : if ( trim(grid(1)%grid_motion) == 'deform' .and.            &
                        (design%rigid_active.or.design%kinematic%active        &
                         .or.design%trimming%active)                           &
                        .and. physical_timestep > 0 ) then
      call rigid_derivs_of_deforming(design,grid(1),gridadj_rhs,t_local,       &
                                     crow(1))
    endif rigid_derivs

! Compute shape gradient vector using adjoint solution just computed
! and write back out to rubber.data

    shape_derivs : if ( design%shape_active ) then

      body_loop2 : do ibody = 1, design%nbodies

        if ( multiply_by_transform ) then

          if ( f15_flutter ) then
            call compute_shape_gradient_f15(ibody,design,grid(1)%nnodes0,      &
                                            grid(1)%l2g,gridadj_rhs,           &
                                            simulation_time,grid(1)%yat0,      &
                                            grid(1)%y,grid(1)%nnodes01,        &
                                            grid(1)%bc)
          else
            if ( low_mem_meshsens ) then
              call compute_shape_gradient_lm(ibody,design,grid(1)%nnodes0,     &
                                             grid(1)%nnodes01,grid(1)%l2g,     &
                                             gridadj_rhs,multiply_by_transform,&
                                             fake_body)
            else
              call compute_shape_gradient(ibody,design,grid(1)%nnodes0,        &
                                          grid(1)%nnodes01,grid(1)%l2g,        &
                                          gridadj_rhs,multiply_by_transform,   &
                                          fake_body)
            endif
          endif

        else
          if ( low_mem_meshsens ) then
            call compute_shape_gradient_lm(ibody,design,grid(1)%nnodes0,       &
                                           grid(1)%nnodes01,grid(1)%l2g,       &
                                           gridadj_rhs,multiply_by_transform,  &
                                           fake_body)
          else
            call compute_shape_gradient(ibody,design,grid(1)%nnodes0,          &
                                        grid(1)%nnodes01,grid(1)%l2g,          &
                                        gridadj_rhs,multiply_by_transform,     &
                                        fake_body)
          endif
        endif

      end do body_loop2

    endif shape_derivs

    init = .false.

  end subroutine elasticity_driver


!========================== RIGID_DERIVS_OF_DEFORMING ========================80
!
!  Compute derivatives wrt rigid variables for deforming mesh cases
!
!=============================================================================80
  subroutine rigid_derivs_of_deforming(design,grid,lambdag,t_local,crow)

    use kinddefs,          only : dp
    use fun3d_constants,   only : my_0
    use design_types,      only : design_type
    use grid_types,        only : grid_type
    use lmpi,              only : lmpi_die, lmpi_reduce, lmpi_bcast
    use lmpi_app,          only : lmpi_xfer
    use info_depr,         only : simulation_time, physical_timestep
    use moving_body_types, only : moving_body
    use nml_grid_motion,   only : n_moving_bodies
    use movees_mixed,      only : Kt_diag, Kt_off
    use nml_overset_data,  only : overset_flag
    use pundit,            only : pundit_flag
    use comprow_types,     only : crow_flow
    use custom_transforms, only : custom_kinematics

    type(grid_type),   intent(in)    :: grid
    type(design_type), intent(inout) :: design
    type(crow_flow), intent(in) :: crow

    real(dp), dimension(3,grid%nnodes0,design%nfunctions), intent(in) :: lambdag
    real(dp), dimension(4,4,n_moving_bodies), intent(in) :: t_local

    integer :: body, k, row, jstart, jend, col, ind, parent_body, inode

    integer :: idv, ifcn, current_body, j, i, parent, nreal, n, ib

    integer, dimension(grid%nnodes0) :: node_tag

    real(dp) :: rotation_time, translation_time, xold, yold, zold
    real(dp) :: termx, termy, termz, tempvar

    real(dp), dimension(4,4) :: dtransform_matrix, identity, bigt, dtdd
    real(dp), dimension(4,4) :: t_ext_bound, t_def_bound, t, trinv, dtrdd
    real(dp), dimension(4,4,n_moving_bodies) :: t_def_stored, t_ext_stored
    real(dp), dimension(3,grid%nnodes01,design%nfunctions) :: lambda
    real(dp), dimension(3) :: vect3
    real(dp), dimension(3) :: ktl
    real(dp), dimension(design%nfunctions) :: deriv

    logical :: been_here_def, been_here_ext

  continue

    parent = -1 ! satisfy compiler complaint that this is otherwise undefined
                ! in some cases

! Set identity matrix

    identity      = 0.0_dp
    identity(1,1) = 1.0_dp
    identity(2,2) = 1.0_dp
    identity(3,3) = 1.0_dp
    identity(4,4) = 1.0_dp

! create temp copy of lambdag that we can xfer to the ghosts

    lambda(1:3,1:grid%nnodes0,1:design%nfunctions) =                           &
                                 lambdag(1:3,1:grid%nnodes0,1:design%nfunctions)
    do ifcn = 1, design%nfunctions
      call lmpi_xfer(lambda(:,:,ifcn))
    end do

! Now we need to linearize the surface mesh coordinates wrt the design variables
! controlling the rigid body movement

    body_loop1 : do body = 1, n_moving_bodies

! Set the current values of time

      rotation_time = simulation_time                                          &
                               - moving_body(body)%rotation_vector%start_time
      translation_time = simulation_time                                       &
                               - moving_body(body)%translation_vector%start_time

      rigid_dv_loop : do idv = 1, design%rigid_data(body)%ndv

        deriv(:) = 0.0_dp

        rigid_active : if ( design%rigid_data(body)%active(idv) ) then

          dtransform_matrix(:,:) = my_0

          select case(design%rigid_data(body)%name(idv))
          case('RotRate')
            if ( moving_body(body)%rotate == 1 ) then
              dtransform_matrix = drotrate(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector)
            endif
          case('RotFreq')
            if ( moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotfreq(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector)
            endif
          case('RotAmpl')
            if ( moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotrate(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector)
            endif
          case('RotOrgx')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotorig(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,1)
            endif
          case('RotOrgy')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotorig(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,2)
            endif
          case('RotOrgz')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotorig(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,3)
            endif
          case('RotVecx')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotvect(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,1)
            endif
          case('RotVecy')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotvect(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,2)
            endif
          case('RotVecz')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotvect(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,3)
            endif
          case('TrnRate')
            if ( moving_body(body)%translate == 1 ) then
              dtransform_matrix =dtrnrate(translation_time,                    &
                                          moving_body(body)%translate,         &
                                          moving_body(body)%rfreq_trans,       &
                                          moving_body(body)%translation_vector)
            endif
          case('TrnFreq')
            if ( moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnfreq(translation_time,                    &
                                          moving_body(body)%translate,         &
                                          moving_body(body)%transrate,         &
                                          moving_body(body)%rfreq_trans,       &
                                          moving_body(body)%translation_vector)
            endif
          case('TrnAmpl')
            if ( moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnrate(translation_time,                    &
                                          moving_body(body)%translate,         &
                                          moving_body(body)%rfreq_trans,       &
                                          moving_body(body)%translation_vector)
            endif
          case('TrnVecx')
            if ( moving_body(body)%translate == 1 .or.                         &
                 moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnvect(moving_body(body)%translation_vector,&
                                          1,body)
            endif
          case('TrnVecy')
            if ( moving_body(body)%translate == 1 .or.                         &
                 moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnvect(moving_body(body)%translation_vector,&
                                          2,body)
            endif
          case('TrnVecz')
            if ( moving_body(body)%translate == 1 .or.                         &
                 moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnvect(moving_body(body)%translation_vector,&
                                          3,body)
            endif
          case default
            write(*,*) 'Unknown rigid DV name...stopping.'
            call lmpi_die
          end select

          t_def_stored(:,:,:) = 0.0_dp
          t_ext_stored(:,:,:) = 0.0_dp

          compute_dtdd_on_a_body : do j = 1, n_moving_bodies

! Start off our chain of transform matrices to be applied to the defining
! body with identity

            t_def_bound = identity
            t_ext_bound = identity

            been_here_def = .false.
            been_here_ext = .false.

! String together all of the parents from oldest to youngest

            current_body = j

            find_parents : do

              if ( trim(moving_body(current_body)%parent_name) == '' ) then
                exit find_parents
              endif

              if ( trim(moving_body(current_body)%parent_name) ==              &
                   trim(moving_body(current_body)%body_name) ) then  ! fake body
                parent = current_body-1
              else
                search5 : do i = 1, n_moving_bodies
                  if ( trim(moving_body(i)%body_name) ==                       &
                       trim(moving_body(current_body)%parent_name) .and.       &
                       .not.moving_body(i)%fake_body ) then
                    parent = i
                    exit search5
                  endif
                end do search5
              endif

              if ( parent == body ) then
                t_def_bound = matmul(dtransform_matrix,t_def_bound)
                been_here_def = .true.
                t_ext_bound = matmul(dtransform_matrix,t_ext_bound)
                been_here_ext = .true.
              else
                t_def_bound = matmul(t_local(:,:,parent),t_def_bound)
                t_ext_bound = matmul(t_local(:,:,parent),t_ext_bound)
              endif

              current_body = parent

            end do find_parents

! Multiply in the current dT/dD

            if ( j == body ) then
              t_def_bound = matmul(t_def_bound,dtransform_matrix)
            else
              if ( been_here_def ) then
                t_def_bound = matmul(t_def_bound,t_local(:,:,j))
              else
                t_def_bound = 0.0_dp
              endif
            endif

            if ( trim(moving_body(j)%mesh_movement) /= 'deform' ) then
              if ( j == body ) then
                t_ext_bound = matmul(t_ext_bound,dtransform_matrix)
              else
                if ( been_here_ext ) then
                  t_ext_bound = matmul(t_ext_bound,t_local(:,:,j))
                else
                  t_ext_bound = 0.0_dp
                endif
              endif
            else
              if ( been_here_ext ) then
                ! do nothing
              else
                t_ext_bound = 0.0_dp
              endif
            endif

! Store off the current body's matrix

            t_def_stored(:,:,j) = t_def_bound(:,:)
            t_ext_stored(:,:,j) = t_ext_bound(:,:)

          end do compute_dtdd_on_a_body

! Now lets do  (Kn^T * lambda)^T * dTr/dD * Tr-inv * Xn

          nreal = 0

          body_loop2 : do k = 1, n_moving_bodies

            if ( moving_body(k)%fake_body ) cycle body_loop2

            nreal = nreal + 1

! Grab T matrices first

            if ( trim(moving_body(k)%mesh_movement) /= 'deform' ) then
              trinv = moving_body(k)%inv_transform
            else
              if ( trim(moving_body(k)%parent_name) == '' ) then
                trinv = identity
              else
                parent_body = moving_body(k)%ancestors(1)
                trinv = moving_body(parent_body)%inv_transform
              endif
            endif

            dtrdd(:,:) = t_ext_stored(:,:,k)   ! dTr/dD for the current body

            bigt = matmul(dtrdd,trinv)

! Loop over entire grid
! Multiply in the X terms

            fcn_loop1 : do ifcn = 1, design%nfunctions
              node_loop1 : do i = 1, grid%nnodes0

                if ( overset_flag .or. pundit_flag ) then
                  if ( grid%imesh(i) /= nreal ) cycle node_loop1
                endif

                xold = grid%xat0(i)
                yold = grid%yat0(i)
                zold = grid%zat0(i)

                vect3(1) = xn(i)*bigt(1,1) + yn(i)*bigt(1,2)                   &
                         + zn(i)*bigt(1,3) + bigt(1,4)
                vect3(2) = xn(i)*bigt(2,1) + yn(i)*bigt(2,2)                   &
                         + zn(i)*bigt(2,3) + bigt(2,4)
                vect3(3) = xn(i)*bigt(3,1) + yn(i)*bigt(3,2)                   &
                         + zn(i)*bigt(3,3) + bigt(3,4)

                row = crow%g2m(i)

                ktl(1) = Kt_diag(1,1,row)*lambda(1,i,ifcn)                     &
                       + Kt_diag(1,2,row)*lambda(2,i,ifcn)                     &
                       + Kt_diag(1,3,row)*lambda(3,i,ifcn)
                ktl(2) = Kt_diag(2,1,row)*lambda(1,i,ifcn)                     &
                       + Kt_diag(2,2,row)*lambda(2,i,ifcn)                     &
                       + Kt_diag(2,3,row)*lambda(3,i,ifcn)
                ktl(3) = Kt_diag(3,1,row)*lambda(1,i,ifcn)                     &
                       + Kt_diag(3,2,row)*lambda(2,i,ifcn)                     &
                       + Kt_diag(3,3,row)*lambda(3,i,ifcn)

                jstart = crow%ia(i)
                jend   = crow%ia(i+1)-1

                do j = jstart, jend
                  col = crow%ja(j)
                  ind = crow%nzg2m(j)
                  ktl(1) = ktl(1) + Kt_off(1,1,ind)*lambda(1,col,ifcn)         &
                                  + Kt_off(1,2,ind)*lambda(2,col,ifcn)         &
                                  + Kt_off(1,3,ind)*lambda(3,col,ifcn)
                  ktl(2) = ktl(2) + Kt_off(2,1,ind)*lambda(1,col,ifcn)         &
                                  + Kt_off(2,2,ind)*lambda(2,col,ifcn)         &
                                  + Kt_off(2,3,ind)*lambda(3,col,ifcn)
                  ktl(3) = ktl(3) + Kt_off(3,1,ind)*lambda(1,col,ifcn)         &
                                  + Kt_off(3,2,ind)*lambda(2,col,ifcn)         &
                                  + Kt_off(3,3,ind)*lambda(3,col,ifcn)
                end do

! Result is a scalar contribution to the current sensitivity derivative

                deriv(ifcn) = deriv(ifcn) + ktl(1)*vect3(1)                    &
                                          + ktl(2)*vect3(2)                    &
                                          + ktl(3)*vect3(3)
              end do node_loop1

            end do fcn_loop1

          end do body_loop2

! Now lets do (X0_surf)^T * (dT/dD-dTr/dD*Tr-inv*T)^T * lambdag
! Note that d(T-Tr)/dD is zero on any boundary that is not deforming.

          node_tag = 0
          nreal = 0

          body_loop3 : do k = 1, n_moving_bodies

            if ( moving_body(k)%fake_body ) cycle body_loop3

            nreal = nreal + 1

! String together T matrices first

            dtdd(:,:)  = t_def_stored(:,:,k)
            dtrdd(:,:) = t_ext_stored(:,:,k)   ! dTr/dD for the current body

            if ( trim(moving_body(k)%mesh_movement) /= 'deform' ) then
              trinv = moving_body(k)%inv_transform
            else
              if ( trim(moving_body(k)%parent_name) == '' ) then
                trinv = identity
              else
                parent_body = moving_body(k)%ancestors(1)
                trinv = moving_body(parent_body)%inv_transform
              endif
            endif

            t = moving_body(k)%transform_matrix

            bigt = matmul(trinv,t)
            bigt = matmul(dtrdd,bigt)
            bigt = transpose(dtdd - bigt)

! Loop over defining boundaries on the current body to get X0_surf and multiply
! by the T's

            do n = 1, moving_body(k)%n_defining_bndry
              ib = moving_body(k)%defining_bndry(n)

              node_loop2 : do j = 1, grid%bc(ib)%nbnode
                inode = grid%bc(ib)%ibnode(j)

                if ( inode > grid%nnodes0 ) cycle node_loop2

                if ( overset_flag .or. pundit_flag ) then
                  if ( grid%imesh(inode) /= nreal ) cycle node_loop2
                endif

                hit_yet1 : if ( node_tag(inode) == 0 ) then
                  xold = grid%xat0(inode)
                  yold = grid%yat0(inode)
                  zold = grid%zat0(inode)
                  termx = bigt(1,1)*xold + bigt(2,1)*yold                      &
                        + bigt(3,1)*zold + bigt(4,1)
                  termy = bigt(1,2)*xold + bigt(2,2)*yold                      &
                        + bigt(3,2)*zold + bigt(4,2)
                  termz = bigt(1,3)*xold + bigt(2,3)*yold                      &
                        + bigt(3,3)*zold + bigt(4,3)

! Finally multiply by lambdas
! Result is a scalar contribution to the current sensitivity derivative

                  do ifcn = 1, design%nfunctions
                    deriv(ifcn) = deriv(ifcn) + lambda(1,inode,ifcn)*termx     &
                                              + lambda(2,inode,ifcn)*termy     &
                                              + lambda(3,inode,ifcn)*termz
                  end do

                  node_tag(inode) = 1

                endif hit_yet1
              end do node_loop2
            end do
          end do body_loop3

        endif rigid_active

! Reduce the results

        do ifcn = 1, design%nfunctions
          call lmpi_reduce(deriv(ifcn),tempvar)
          call lmpi_bcast(tempvar)
          design%rigid_data(body)%shape_derivative(ifcn,idv) = tempvar
        end do

      end do rigid_dv_loop

    end do body_loop1

! Now we need to linearize the surface mesh coordinates wrt the design variables
! controlling any custom kinematic body movement

    body_loop4 : do body = 1, n_moving_bodies

      custom_motion : if( trim(moving_body(body)%motion_driver)=='custom' ) then

        custom_dv_loop : do idv = 1, design%kinematic%body_data(body)%ndv

          deriv(:) = 0.0_dp

          custom_active : if (design%kinematic%body_data(body)%active(idv)==1) &
          then

            dtransform_matrix(:,:) = my_0

            call custom_kinematics(design%kinematic%body_data(body)%ndv,       &
                                   simulation_time,                            &
                                   design%kinematic%body_data(body)%value,1,   &
                                   ind=idv,dtdd=dtransform_matrix)

            t_def_stored(:,:,:) = 0.0_dp
            t_ext_stored(:,:,:) = 0.0_dp

            compute_dtdd_on_a_body2 : do j = 1, n_moving_bodies

! Start off our chain of transform matrices to be applied to the defining
! body with identity

              t_def_bound = identity
              t_ext_bound = identity

              been_here_def = .false.
              been_here_ext = .false.

! String together all of the parents from oldest to youngest

              current_body = j

              find_parents2 : do

                if ( trim(moving_body(current_body)%parent_name) == '' ) then
                  exit find_parents2
                endif

                if ( trim(moving_body(current_body)%parent_name) ==            &
                     trim(moving_body(current_body)%body_name) ) then !fake body
                  parent = current_body-1
                else
                  search1 : do i = 1, n_moving_bodies
                    if ( trim(moving_body(i)%body_name) ==                     &
                         trim(moving_body(current_body)%parent_name) .and.     &
                         .not.moving_body(i)%fake_body ) then
                      parent = i
                      exit search1
                    endif
                  end do search1
                endif

                if ( parent == body ) then
                  t_def_bound = matmul(dtransform_matrix,t_def_bound)
                  been_here_def = .true.
                  t_ext_bound = matmul(dtransform_matrix,t_ext_bound)
                  been_here_ext = .true.
                else
                  t_def_bound = matmul(t_local(:,:,parent),t_def_bound)
                  t_ext_bound = matmul(t_local(:,:,parent),t_ext_bound)
                endif

                current_body = parent

              end do find_parents2

! Multiply in the current dT/dD

              if ( j == body ) then
                t_def_bound = matmul(t_def_bound,dtransform_matrix)
              else
                if ( been_here_def ) then
                  t_def_bound = matmul(t_def_bound,t_local(:,:,j))
                else
                  t_def_bound = 0.0_dp
                endif
              endif

              if ( trim(moving_body(j)%mesh_movement) /= 'deform' ) then
                if ( j == body ) then
                  t_ext_bound = matmul(t_ext_bound,dtransform_matrix)
                else
                  if ( been_here_ext ) then
                    t_ext_bound = matmul(t_ext_bound,t_local(:,:,j))
                  else
                    t_ext_bound = 0.0_dp
                  endif
                endif
              else
                if ( been_here_ext ) then
                  ! do nothing
                else
                  t_ext_bound = 0.0_dp
                endif
              endif

! Store off the current body's matrix

              t_def_stored(:,:,j) = t_def_bound(:,:)
              t_ext_stored(:,:,j) = t_ext_bound(:,:)

            end do compute_dtdd_on_a_body2

! Now lets do  (Kn^T * lambda)^T * dTr/dD * Tr-inv * Xn

            nreal = 0

            body_loop5 : do k = 1, n_moving_bodies

              if ( moving_body(k)%fake_body ) cycle body_loop5

              nreal = nreal + 1

! Grab T matrices first

              if ( trim(moving_body(k)%mesh_movement) /= 'deform' ) then
                trinv = moving_body(k)%inv_transform
              else
                if ( trim(moving_body(k)%parent_name) == '' ) then
                  trinv = identity
                else
                  parent_body = moving_body(k)%ancestors(1)
                  trinv = moving_body(parent_body)%inv_transform
                endif
              endif

              dtrdd(:,:) = t_ext_stored(:,:,k)   ! dTr/dD for the current body

              bigt = matmul(dtrdd,trinv)

! Loop over entire grid
! Multiply in the X terms

              fcn_loop2 : do ifcn = 1, design%nfunctions
                node_loop3 : do i = 1, grid%nnodes0

                  if ( overset_flag .or. pundit_flag ) then
                    if ( grid%imesh(i) /= nreal ) cycle node_loop3
                  endif

                  xold = grid%xat0(i)
                  yold = grid%yat0(i)
                  zold = grid%zat0(i)

                  vect3(1) = xn(i)*bigt(1,1) + yn(i)*bigt(1,2)                 &
                           + zn(i)*bigt(1,3) + bigt(1,4)
                  vect3(2) = xn(i)*bigt(2,1) + yn(i)*bigt(2,2)                 &
                           + zn(i)*bigt(2,3) + bigt(2,4)
                  vect3(3) = xn(i)*bigt(3,1) + yn(i)*bigt(3,2)                 &
                           + zn(i)*bigt(3,3) + bigt(3,4)

                  row = crow%g2m(i)

                  ktl(1) = Kt_diag(1,1,row)*lambda(1,i,ifcn)                   &
                         + Kt_diag(1,2,row)*lambda(2,i,ifcn)                   &
                         + Kt_diag(1,3,row)*lambda(3,i,ifcn)
                  ktl(2) = Kt_diag(2,1,row)*lambda(1,i,ifcn)                   &
                         + Kt_diag(2,2,row)*lambda(2,i,ifcn)                   &
                         + Kt_diag(2,3,row)*lambda(3,i,ifcn)
                  ktl(3) = Kt_diag(3,1,row)*lambda(1,i,ifcn)                   &
                         + Kt_diag(3,2,row)*lambda(2,i,ifcn)                   &
                         + Kt_diag(3,3,row)*lambda(3,i,ifcn)

                  jstart = crow%ia(i)
                  jend   = crow%ia(i+1)-1

                  do j = jstart, jend
                    col = crow%ja(j)
                    ind = crow%nzg2m(j)
                    ktl(1) = ktl(1) + Kt_off(1,1,ind)*lambda(1,col,ifcn)       &
                                    + Kt_off(1,2,ind)*lambda(2,col,ifcn)       &
                                    + Kt_off(1,3,ind)*lambda(3,col,ifcn)
                    ktl(2) = ktl(2) + Kt_off(2,1,ind)*lambda(1,col,ifcn)       &
                                    + Kt_off(2,2,ind)*lambda(2,col,ifcn)       &
                                    + Kt_off(2,3,ind)*lambda(3,col,ifcn)
                    ktl(3) = ktl(3) + Kt_off(3,1,ind)*lambda(1,col,ifcn)       &
                                    + Kt_off(3,2,ind)*lambda(2,col,ifcn)       &
                                    + Kt_off(3,3,ind)*lambda(3,col,ifcn)
                  end do

! Result is a scalar contribution to the current sensitivity derivative

                  deriv(ifcn) = deriv(ifcn) + ktl(1)*vect3(1)                  &
                                            + ktl(2)*vect3(2)                  &
                                            + ktl(3)*vect3(3)
                end do node_loop3

              end do fcn_loop2

            end do body_loop5

! Now lets do (X0_surf)^T * (dT/dD-dTr/dD*Tr-inv*T)^T * lambdag
! Note that d(T-Tr)/dD is zero on any boundary that is not deforming.

            node_tag = 0
            nreal = 0

            body_loop6 : do k = 1, n_moving_bodies

              if ( moving_body(k)%fake_body ) cycle body_loop6

              nreal = nreal + 1

! String together T matrices first

              dtdd(:,:)  = t_def_stored(:,:,k)
              dtrdd(:,:) = t_ext_stored(:,:,k)   ! dTr/dD for the current body

              if ( trim(moving_body(k)%mesh_movement) /= 'deform' ) then
                trinv = moving_body(k)%inv_transform
              else
                if ( trim(moving_body(k)%parent_name) == '' ) then
                  trinv = identity
                else
                  parent_body = moving_body(k)%ancestors(1)
                  trinv = moving_body(parent_body)%inv_transform
                endif
              endif

              t = moving_body(k)%transform_matrix

              bigt = matmul(trinv,t)
              bigt = matmul(dtrdd,bigt)
              bigt = transpose(dtdd - bigt)

! Loop over defining boundaries on the current body to get X0_surf and multiply
! by the T's

              do n = 1, moving_body(k)%n_defining_bndry
                ib = moving_body(k)%defining_bndry(n)

                node_loop4 : do j = 1, grid%bc(ib)%nbnode
                  inode = grid%bc(ib)%ibnode(j)

                  if ( inode > grid%nnodes0 ) cycle node_loop4

                  if ( overset_flag .or. pundit_flag ) then
                    if ( grid%imesh(inode) /= nreal ) cycle node_loop4
                  endif

                  hit_yet2 : if ( node_tag(inode) == 0 ) then
                    xold = grid%xat0(inode)
                    yold = grid%yat0(inode)
                    zold = grid%zat0(inode)
                    termx = bigt(1,1)*xold + bigt(2,1)*yold                    &
                          + bigt(3,1)*zold + bigt(4,1)
                    termy = bigt(1,2)*xold + bigt(2,2)*yold                    &
                          + bigt(3,2)*zold + bigt(4,2)
                    termz = bigt(1,3)*xold + bigt(2,3)*yold                    &
                          + bigt(3,3)*zold + bigt(4,3)

! Finally multiply by lambdas
! Result is a scalar contribution to the current sensitivity derivative

                    do ifcn = 1, design%nfunctions
                      deriv(ifcn) = deriv(ifcn) + lambda(1,inode,ifcn)*termx   &
                                                + lambda(2,inode,ifcn)*termy   &
                                                + lambda(3,inode,ifcn)*termz
                    end do

                    node_tag(inode) = 1

                  endif hit_yet2
                end do node_loop4
              end do
            end do body_loop6

          endif custom_active

! Reduce the results

          do ifcn = 1, design%nfunctions
            call lmpi_reduce(deriv(ifcn),tempvar)
            call lmpi_bcast(tempvar)
            design%kinematic%body_data(body)%shape_derivative(ifcn,idv)= tempvar
          end do

        end do custom_dv_loop

      endif custom_motion

    end do body_loop4

! Now we need to linearize the surface mesh coordinates wrt the design variables
! controlling any trimming body movement

    body_loop7 : do body = 1, n_moving_bodies

      trim_motion : if ( trim(moving_body(body)%trim_control) == 'design' ) then

        trim_dv_loop : do idv = 1, design%trimming%body_data(body)%ndv

          deriv(:) = 0.0_dp

          trim_active : if (design%trimming%body_data(body)%active(idv) == 1 ) &
          then

            dtransform_matrix(:,:) = my_0

            dtransform_matrix = dtrim(moving_body(body)%rotation_vector,       &
                                      moving_body(body)%steps_per_period,      &
                                      physical_timestep,                       &
                                      moving_body(body)%baseline_psi,          &
                                      design%trimming%body_data(body)%value(1),&
                                      design%trimming%body_data(body)%value(2),&
                                      design%trimming%body_data(body)%value(3),&
                                      idv)

            t_def_stored(:,:,:) = 0.0_dp
            t_ext_stored(:,:,:) = 0.0_dp

            compute_dtdd_on_a_body3 : do j = 1, n_moving_bodies

! Start off our chain of transform matrices to be applied to the defining
! body with identity

              t_def_bound = identity
              t_ext_bound = identity

              been_here_def = .false.
              been_here_ext = .false.

! String together all of the parents from oldest to youngest

              current_body = j

              find_parents3 : do

                if ( trim(moving_body(current_body)%parent_name) == '' ) then
                  exit find_parents3
                endif

                if ( trim(moving_body(current_body)%parent_name) ==            &
                     trim(moving_body(current_body)%body_name) ) then !fake body
                  parent = current_body-1
                else
                  search2 : do i = 1, n_moving_bodies
                    if ( trim(moving_body(i)%body_name) ==                     &
                         trim(moving_body(current_body)%parent_name) .and.     &
                         .not.moving_body(i)%fake_body ) then
                      parent = i
                      exit search2
                    endif
                  end do search2
                endif

                if ( parent == body ) then
                  t_def_bound = matmul(dtransform_matrix,t_def_bound)
                  been_here_def = .true.
                  t_ext_bound = matmul(dtransform_matrix,t_ext_bound)
                  been_here_ext = .true.
                else
                  t_def_bound = matmul(t_local(:,:,parent),t_def_bound)
                  t_ext_bound = matmul(t_local(:,:,parent),t_ext_bound)
                endif

                current_body = parent

              end do find_parents3

! Multiply in the current dT/dD

              if ( j == body ) then
                t_def_bound = matmul(t_def_bound,dtransform_matrix)
              else
                if ( been_here_def ) then
                  t_def_bound = matmul(t_def_bound,t_local(:,:,j))
                else
                  t_def_bound = 0.0_dp
                endif
              endif

              if ( trim(moving_body(j)%mesh_movement) /= 'deform' ) then
                if ( j == body ) then
                  t_ext_bound = matmul(t_ext_bound,dtransform_matrix)
                else
                  if ( been_here_ext ) then
                    t_ext_bound = matmul(t_ext_bound,t_local(:,:,j))
                  else
                    t_ext_bound = 0.0_dp
                  endif
                endif
              else
                if ( been_here_ext ) then
                  ! do nothing
                else
                  t_ext_bound = 0.0_dp
                endif
              endif

! Store off the current body's matrix

              t_def_stored(:,:,j) = t_def_bound(:,:)
              t_ext_stored(:,:,j) = t_ext_bound(:,:)

            end do compute_dtdd_on_a_body3

! Now lets do  (Kn^T * lambda)^T * dTr/dD * Tr-inv * Xn

            nreal = 0

            body_loop9 : do k = 1, n_moving_bodies

              if ( moving_body(k)%fake_body ) cycle body_loop9

              nreal = nreal + 1

! Grab T matrices first

              if ( trim(moving_body(k)%mesh_movement) /= 'deform' ) then
                trinv = moving_body(k)%inv_transform
              else
                if ( trim(moving_body(k)%parent_name) == '' ) then
                  trinv = identity
                else
                  parent_body = moving_body(k)%ancestors(1)
                  trinv = moving_body(parent_body)%inv_transform
                endif
              endif

              dtrdd(:,:) = t_ext_stored(:,:,k)   ! dTr/dD for the current body

              bigt = matmul(dtrdd,trinv)

! Loop over entire grid
! Multiply in the X terms

              fcn_loop3 : do ifcn = 1, design%nfunctions
                node_loop6 : do i = 1, grid%nnodes0

                  if ( overset_flag .or. pundit_flag ) then
                    if ( grid%imesh(i) /= nreal ) cycle node_loop6
                  endif

                  xold = grid%xat0(i)
                  yold = grid%yat0(i)
                  zold = grid%zat0(i)

                  vect3(1) = xn(i)*bigt(1,1) + yn(i)*bigt(1,2)                 &
                           + zn(i)*bigt(1,3) + bigt(1,4)
                  vect3(2) = xn(i)*bigt(2,1) + yn(i)*bigt(2,2)                 &
                           + zn(i)*bigt(2,3) + bigt(2,4)
                  vect3(3) = xn(i)*bigt(3,1) + yn(i)*bigt(3,2)                 &
                           + zn(i)*bigt(3,3) + bigt(3,4)

                  row = crow%g2m(i)

                  ktl(1) = Kt_diag(1,1,row)*lambda(1,i,ifcn)                   &
                         + Kt_diag(1,2,row)*lambda(2,i,ifcn)                   &
                         + Kt_diag(1,3,row)*lambda(3,i,ifcn)
                  ktl(2) = Kt_diag(2,1,row)*lambda(1,i,ifcn)                   &
                         + Kt_diag(2,2,row)*lambda(2,i,ifcn)                   &
                         + Kt_diag(2,3,row)*lambda(3,i,ifcn)
                  ktl(3) = Kt_diag(3,1,row)*lambda(1,i,ifcn)                   &
                         + Kt_diag(3,2,row)*lambda(2,i,ifcn)                   &
                         + Kt_diag(3,3,row)*lambda(3,i,ifcn)

                  jstart = crow%ia(i)
                  jend   = crow%ia(i+1)-1

                  do j = jstart, jend
                    col = crow%ja(j)
                    ind = crow%nzg2m(j)
                    ktl(1) = ktl(1) + Kt_off(1,1,ind)*lambda(1,col,ifcn)       &
                                    + Kt_off(1,2,ind)*lambda(2,col,ifcn)       &
                                    + Kt_off(1,3,ind)*lambda(3,col,ifcn)
                    ktl(2) = ktl(2) + Kt_off(2,1,ind)*lambda(1,col,ifcn)       &
                                    + Kt_off(2,2,ind)*lambda(2,col,ifcn)       &
                                    + Kt_off(2,3,ind)*lambda(3,col,ifcn)
                    ktl(3) = ktl(3) + Kt_off(3,1,ind)*lambda(1,col,ifcn)       &
                                    + Kt_off(3,2,ind)*lambda(2,col,ifcn)       &
                                    + Kt_off(3,3,ind)*lambda(3,col,ifcn)
                  end do

! Result is a scalar contribution to the current sensitivity derivative

                  deriv(ifcn) = deriv(ifcn) + ktl(1)*vect3(1)                  &
                                            + ktl(2)*vect3(2)                  &
                                            + ktl(3)*vect3(3)
                end do node_loop6

              end do fcn_loop3

            end do body_loop9

! Now lets do (X0_surf)^T * (dT/dD-dTr/dD*Tr-inv*T)^T * lambdag
! Note that d(T-Tr)/dD is zero on any boundary that is not deforming.

            node_tag = 0
            nreal = 0

            body_loop10 : do k = 1, n_moving_bodies

              if ( moving_body(k)%fake_body ) cycle body_loop10

              nreal = nreal + 1

! String together T matrices first

              dtdd(:,:)  = t_def_stored(:,:,k)
              dtrdd(:,:) = t_ext_stored(:,:,k)   ! dTr/dD for the current body

              if ( trim(moving_body(k)%mesh_movement) /= 'deform' ) then
                trinv = moving_body(k)%inv_transform
              else
                if ( trim(moving_body(k)%parent_name) == '' ) then
                  trinv = identity
                else
                  parent_body = moving_body(k)%ancestors(1)
                  trinv = moving_body(parent_body)%inv_transform
                endif
              endif

              t = moving_body(k)%transform_matrix

              bigt = matmul(trinv,t)
              bigt = matmul(dtrdd,bigt)
              bigt = transpose(dtdd - bigt)

! Loop over defining boundaries on the current body to get X0_surf and multiply
! by the T's

              do n = 1, moving_body(k)%n_defining_bndry
                ib = moving_body(k)%defining_bndry(n)

                node_loop5 : do j = 1, grid%bc(ib)%nbnode
                  inode = grid%bc(ib)%ibnode(j)

                  if ( inode > grid%nnodes0 ) cycle node_loop5

                  if ( overset_flag .or. pundit_flag ) then
                    if ( grid%imesh(inode) /= nreal ) cycle node_loop5
                  endif

                  hit_yet3 : if ( node_tag(inode) == 0 ) then
                    xold = grid%xat0(inode)
                    yold = grid%yat0(inode)
                    zold = grid%zat0(inode)
                    termx = bigt(1,1)*xold + bigt(2,1)*yold                    &
                          + bigt(3,1)*zold + bigt(4,1)
                    termy = bigt(1,2)*xold + bigt(2,2)*yold                    &
                          + bigt(3,2)*zold + bigt(4,2)
                    termz = bigt(1,3)*xold + bigt(2,3)*yold                    &
                          + bigt(3,3)*zold + bigt(4,3)

! Finally multiply by lambdas
! Result is a scalar contribution to the current sensitivity derivative

                    do ifcn = 1, design%nfunctions
                      deriv(ifcn) = deriv(ifcn) + lambda(1,inode,ifcn)*termx   &
                                                + lambda(2,inode,ifcn)*termy   &
                                                + lambda(3,inode,ifcn)*termz
                    end do

                    node_tag(inode) = 1

                  endif hit_yet3
                end do node_loop5
              end do
            end do body_loop10

          endif trim_active

! Reduce the results

          do ifcn = 1, design%nfunctions
            call lmpi_reduce(deriv(ifcn),tempvar)
            call lmpi_bcast(tempvar)
            design%trimming%body_data(body)%shape_derivative(ifcn,idv)= tempvar
          end do

        end do trim_dv_loop

      endif trim_motion

    end do body_loop7

  end subroutine rigid_derivs_of_deforming


!=============================== SENSSHAPE_DRIVER ============================80
!
!  Routine to interface with sensshape for shape linearizations
!
!=============================================================================80
  subroutine sensshape_driver(grid,soln,sadj,design,gridadj_rhs)

    use info_depr,        only : physical_timestep
    use nml_global,       only : moving_grid
    use grid_types,       only : grid_type
    use design_types,     only : design_type
    use solution_types,   only : soln_type
    use solution_adj,     only : sadj_type
    use solution_getg,    only : set_up_getg
    use dshape,           only : sensshape,write_column_sum,dvol_dgrid,        &
                                 write_column_sum_dynamic,                     &
                                 column_sum_initial_state
    use fun3d_constants,  only : my_0
    use comprow,          only : set_up_comprow
    use adjoint_switches, only : store_full_stencil
    use allocations,      only : my_alloc_ptr

    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    type(grid_type),   intent(inout) :: grid
    type(soln_type),   intent(inout) :: soln

    real(dp), dimension(3,grid%nnodes0,design%nfunctions), intent(out)         &
                                                                  :: gridadj_rhs

    integer :: dim1

    integer, dimension(:), pointer, save :: iflagslen_save

    logical :: global_store_full_stencil

    logical, save :: iflagslen_save_setup = .false.

  continue

! Set up a local nearest-neighbor crow array
! (The one used in the solver might have next-nearest neighbors included)
! Be sure to unset store_full_stencil if needed!

    if ( .not. nn_crow_setup ) then
      global_store_full_stencil = store_full_stencil
      store_full_stencil = .false.
      call set_up_comprow(soln%viscous_method, grid, nn_crow)
      nn_crow_setup = .true.
      store_full_stencil = global_store_full_stencil
    endif

! Set up the getg derived type if not already done

    if ( .not. getg_setup ) then
      call set_up_getg(grid,nn_crow,getg,design)
      getg_setup = .true.
    endif

! Initialize variables

    getg%dvdx           = my_0
    getg%dfdx           = my_0
    getg%drdxl          = my_0
    getg%sourceterm_sum = my_0
    getg%overset_terms  = my_0

! In the event that transition is being simulated, save a baseline copy of
! the iflagslen array so we can restore it later

    if ( .not. iflagslen_save_setup ) then
      dim1 = size(grid%iflagslen,1)
      call my_alloc_ptr(iflagslen_save, dim1)
      iflagslen_save = grid%iflagslen
      iflagslen_save_setup = .true.
    endif

! Linearize the appropriate terms

    if ( physical_timestep > 0 ) then
      grid%iflagslen = abs(grid%iflagslen)
      call sensshape(grid,nn_crow,soln,sadj,design,getg,1)
      grid%iflagslen = iflagslen_save
    else
      call dvol_dgrid(nn_crow,design,sadj,soln,getg,grid%nelem,grid%nnodes0,   &
                      grid%nedgeloc,grid%nbound,grid%elem,grid%x,grid%y,grid%z,&
                      .true.,grid%bc)
    endif

! Dump out the RHS for the grid adjoint

    if ( moving_grid ) then
      if ( physical_timestep > 0 ) then
        call write_column_sum_dynamic(grid%nnodes0,grid%nnodes01,              &
                                      design%nfunctions,nn_crow%nnz01,getg%ntp,&
                                      nn_crow%ia,nn_crow%ja,getg%drdxl,        &
                                      getg%dfdx,getg%dvdx,getg%sourceterm_sum, &
                                      getg%overset_terms,gridadj_rhs)
      else
        call column_sum_initial_state(grid%nnodes0,grid%nnodes01,              &
                                      design%nfunctions,nn_crow%nnz01,getg%ntp,&
                                      nn_crow%ia,nn_crow%ja,getg%dvdx,         &
                                      gridadj_rhs)
      endif
    else
      call write_column_sum(grid%nnodes0,grid%nnodes01,design%nfunctions,      &
                            nn_crow%nnz01,getg%ntp,nn_crow%ia,nn_crow%ja,      &
                            getg%drdxl,getg%dfdx,getg%dvdx,getg%sourceterm_sum,&
                            getg%overset_terms,gridadj_rhs)
    endif

  end subroutine sensshape_driver


!=============================== ADD_TO_TLAMBDAG =============================80
!
!  Add current T*lambdag to the running sum
!
!=============================================================================80
  subroutine add_to_tlambdag(nnodes0,nfunctions,tlambdag,lambdag,imesh)

    use moving_body_types, only : moving_body
    use nml_grid_motion,   only : n_moving_bodies
    use kinddefs,          only : dp
    use nml_overset_data,  only : overset_flag

    integer, intent(in) :: nnodes0, nfunctions

    integer, dimension(:), intent(in) :: imesh

    real(dp), dimension(3,nnodes0,nfunctions), intent(in)    :: lambdag
    real(dp), dimension(3,nnodes0,nfunctions), intent(inout) :: tlambdag

    integer :: i, ifcn, k, nreal

    logical, dimension(nnodes0) :: node_hit

  continue

! Now add T*lambdag to running sum
! If the grid point belongs to a mesh that is moving according to some transform
! matrix, then use it.  Otherwise, the transform matrix is identity.

    node_hit = .false.

    nreal = 0

    body_loop : do k = 1, n_moving_bodies

      if ( moving_body(k)%fake_body ) cycle body_loop

      nreal = nreal + 1

      fcn_loop : do ifcn = 1, nfunctions
        node_loop : do i = 1, nnodes0

          if ( overset_flag ) then
            if ( imesh(i) /= nreal ) cycle node_loop
          endif

          tlambdag(1,i,ifcn) = tlambdag(1,i,ifcn)                              &
                 + moving_body(k)%transform_matrix(1,1)*lambdag(1,i,ifcn)      &
                 + moving_body(k)%transform_matrix(2,1)*lambdag(2,i,ifcn)      &
                 + moving_body(k)%transform_matrix(3,1)*lambdag(3,i,ifcn)
          tlambdag(2,i,ifcn) = tlambdag(2,i,ifcn)                              &
                 + moving_body(k)%transform_matrix(1,2)*lambdag(1,i,ifcn)      &
                 + moving_body(k)%transform_matrix(2,2)*lambdag(2,i,ifcn)      &
                 + moving_body(k)%transform_matrix(3,2)*lambdag(3,i,ifcn)
          tlambdag(3,i,ifcn) = tlambdag(3,i,ifcn)                              &
                 + moving_body(k)%transform_matrix(1,3)*lambdag(1,i,ifcn)      &
                 + moving_body(k)%transform_matrix(2,3)*lambdag(2,i,ifcn)      &
                 + moving_body(k)%transform_matrix(3,3)*lambdag(3,i,ifcn)

          node_hit(i) = .true.

        end do node_loop
      end do fcn_loop
    end do body_loop

    fcn_loop2 : do ifcn = 1, nfunctions
      node_loop2 : do i = 1, nnodes0
        if ( node_hit(i) ) cycle node_loop2
        tlambdag(1,i,ifcn) = tlambdag(1,i,ifcn) + lambdag(1,i,ifcn)
        tlambdag(2,i,ifcn) = tlambdag(2,i,ifcn) + lambdag(2,i,ifcn)
        tlambdag(3,i,ifcn) = tlambdag(3,i,ifcn) + lambdag(3,i,ifcn)
      end do node_loop2
    end do fcn_loop2

  end subroutine add_to_tlambdag


!=============================== ADD_TO_TOT_GRIDADJ_RHS ======================80
!
!  Add grid linearizations to total gridadj_rhs vector
!
!=============================================================================80
  subroutine add_to_tot_gridadj_rhs(nnodes0,nfunctions,tot_gridadj_rhs,        &
                                    gridadj_rhs)

    use kinddefs, only : dp

    integer, intent(in) :: nnodes0, nfunctions

    real(dp), dimension(3,nnodes0,nfunctions), intent(in)    :: gridadj_rhs
    real(dp), dimension(3,nnodes0,nfunctions), intent(inout) :: tot_gridadj_rhs

    integer :: i, ifcn

  continue

    do ifcn = 1, nfunctions
     do i = 1, nnodes0
       tot_gridadj_rhs(1,i,ifcn)=tot_gridadj_rhs(1,i,ifcn)+gridadj_rhs(1,i,ifcn)
       tot_gridadj_rhs(2,i,ifcn)=tot_gridadj_rhs(2,i,ifcn)+gridadj_rhs(2,i,ifcn)
       tot_gridadj_rhs(3,i,ifcn)=tot_gridadj_rhs(3,i,ifcn)+gridadj_rhs(3,i,ifcn)
     end do
    end do

  end subroutine add_to_tot_gridadj_rhs


!=============================== ALPHA_DRIVER ================================80
!
!  Driver routine to compute alpha sensitivity stuff
!
!=============================================================================80
  subroutine alpha_driver(design,grid,sadj,soln)

    use lmpi,                 only : lmpi_master
    use design_types,         only : design_type
    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : subiters, dt
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use allocations,          only : my_alloc_ptr
    use dalpha,               only : sensalpha
    use kinddefs,             only : dp

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(inout) :: design

    integer :: subiter_arg, timestep_arg, j

    real(dp), dimension(:,:), pointer ::  dfs_dq_lambda
    real(dp), dimension(:,:), pointer ::  dIdalp

  continue

    away_from_freestream : if ( physical_timestep > 0 ) then
      call my_alloc_ptr(dIdalp, 2, design%nfunctions)
      timestep_arg = physical_timestep
      subiter_arg  = subiters
      if ( physical_timestep == 0 ) then
        timestep_arg = 1
        subiter_arg  = 0
      endif
      call sensalpha(grid,soln,sadj,design,dIdalp,timestep_arg,subiter_arg)
      do j = 1, design%nfunctions
        design%alpha_derivative(j) = design%alpha_derivative(j) + dt*dIdalp(1,j)
        design%yaw_derivative(j)   = design%yaw_derivative(j)   + dt*dIdalp(2,j)
      end do
      deallocate(dIdalp)
    endif away_from_freestream

! Now if we're back to the initial state (physical_timestep = 0), then we
! also have to add in the (dQ_0/dD)*(lambda_0) term

    if ( physical_timestep == 0 ) then
      call my_alloc_ptr(dfs_dq_lambda, 2, design%nfunctions)
      call linearize_initial_statea(dfs_dq_lambda,design%nfunctions,           &
                                    grid%nnodes0,sadj%rlam,soln%adim,          &
                                    grid%nnodes01,grid%vol,grid%nbound,        &
                                    grid%bc,soln%eqn_set)
      design%alpha_derivative(:) = design%alpha_derivative(:)+dfs_dq_lambda(1,:)
      design%yaw_derivative(:)   = design%yaw_derivative(:)  +dfs_dq_lambda(2,:)
      deallocate(dfs_dq_lambda)
    endif

    if ( lmpi_master ) then
      do j = 1, design%nfunctions
        write(*,*)'Current alpha derivative = ',design%alpha_derivative(j)
        write(unit_73,*)'Current alpha derivative = ',design%alpha_derivative(j)
        write(*,*)'Current yaw derivative = ',design%yaw_derivative(j)
        write(unit_73,*)'Current yaw derivative = ',design%yaw_derivative(j)
      end do
    endif

  end subroutine alpha_driver


!=============================== MACH_DRIVER =================================80
!
!  Driver routine to compute Mach sensitivity stuff
!
!=============================================================================80
  subroutine mach_driver(design,grid,sadj,soln)

    use lmpi,                 only : lmpi_master
    use design_types,         only : design_type
    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : subiters, dt
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use allocations,          only : my_alloc_ptr
    use dmach,                only : sensmach
    use kinddefs,             only : dp

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(inout) :: design

    integer :: subiter_arg, timestep_arg, j

    real(dp), dimension(:), pointer ::  dfs_dq_lambda, dIdmach

  continue

    away_from_freestream : if ( physical_timestep > 0 ) then
      call my_alloc_ptr(dIdmach, design%nfunctions)
      timestep_arg = physical_timestep
      subiter_arg  = subiters
      if ( physical_timestep == 0 ) then
        timestep_arg = 1
        subiter_arg  = 0
      endif
      call sensmach(grid,soln,sadj,design,dIdmach,timestep_arg,subiter_arg)
      do j = 1, design%nfunctions
        design%mach_derivative(j) = design%mach_derivative(j) + dt*dIdmach(j)
      end do
      deallocate(dIdmach)
    endif away_from_freestream

! Now if we're back to the initial state (physical_timestep = 0), then we
! also have to add in the (dQ_0/dD)*(lambda_0) term

    if ( physical_timestep == 0 ) then
      call my_alloc_ptr(dfs_dq_lambda, design%nfunctions)
      call linearize_initial_statem(dfs_dq_lambda,design%nfunctions,           &
                                    grid%nnodes0,sadj%rlam,soln%adim,          &
                                    grid%nnodes01,grid%vol,grid%nbound,        &
                                    grid%bc)
      design%mach_derivative(:) = design%mach_derivative(:) + dfs_dq_lambda(:)
      deallocate(dfs_dq_lambda)
    endif

    if ( lmpi_master ) then
      do j = 1, design%nfunctions
        write(*,*) 'Current Mach  derivative = ',design%mach_derivative(j)
        write(unit_73,*) 'Current Mach  derivative = ',design%mach_derivative(j)
      end do
    endif

  end subroutine mach_driver


!=============================== JET_DRIVER ==================================80
!
!  Driver routine to compute jet sensitivity stuff
!
!=============================================================================80
  subroutine jet_driver(design,grid,sadj,soln)

    use lmpi,                 only : lmpi_master
    use design_types,         only : design_type
    use info_depr,            only : physical_timestep
    use nml_nonlinear_solves, only : subiters, dt
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use allocations,          only : my_alloc_ptr
    use djet,                 only : sensjet
    use kinddefs,             only : dp

    type(grid_type),   intent(in)    :: grid
    type(sadj_type),   intent(in)    :: sadj
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(inout) :: design

    integer :: subiter_arg, timestep_arg

    real(dp), dimension(:,:,:), pointer :: dIdjet

  continue

    away_from_freestream : if ( physical_timestep > 0 ) then
      call my_alloc_ptr(dIdjet, design%nfunctions, 10, 3)
      timestep_arg = physical_timestep
      subiter_arg  = subiters
      if ( physical_timestep == 0 ) then
        timestep_arg = 1
        subiter_arg  = 0
      endif

      call sensjet(grid,soln,sadj,design,dIdjet,timestep_arg,subiter_arg)

! slat: jet 1 of 10

      design%auxiliary%body_data(1)%shape_derivative(1,1) =                    &
         design%auxiliary%body_data(1)%shape_derivative(1,1) + dt*dIdjet(1,1,1)
      design%auxiliary%body_data(1)%shape_derivative(1,2) =                    &
         design%auxiliary%body_data(1)%shape_derivative(1,2) + dt*dIdjet(1,1,2)
      design%auxiliary%body_data(1)%shape_derivative(1,3) =                    &
         design%auxiliary%body_data(1)%shape_derivative(1,3) + dt*dIdjet(1,1,3)

! slat: jet 2 of 10

      design%auxiliary%body_data(1)%shape_derivative(1,4) =                    &
         design%auxiliary%body_data(1)%shape_derivative(1,4) + dt*dIdjet(1,2,1)
      design%auxiliary%body_data(1)%shape_derivative(1,5) =                    &
         design%auxiliary%body_data(1)%shape_derivative(1,5) + dt*dIdjet(1,2,2)
      design%auxiliary%body_data(1)%shape_derivative(1,6) =                    &
         design%auxiliary%body_data(1)%shape_derivative(1,6) + dt*dIdjet(1,2,3)

! main: jet 3 of 10

      design%auxiliary%body_data(2)%shape_derivative(1,1) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,1) + dt*dIdjet(1,3,1)
      design%auxiliary%body_data(2)%shape_derivative(1,2) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,2) + dt*dIdjet(1,3,2)
      design%auxiliary%body_data(2)%shape_derivative(1,3) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,3) + dt*dIdjet(1,3,3)

! main: jet 4 of 10

      design%auxiliary%body_data(2)%shape_derivative(1,4) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,4) + dt*dIdjet(1,4,1)
      design%auxiliary%body_data(2)%shape_derivative(1,5) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,5) + dt*dIdjet(1,4,2)
      design%auxiliary%body_data(2)%shape_derivative(1,6) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,6) + dt*dIdjet(1,4,3)

! main: jet 5 of 10

      design%auxiliary%body_data(2)%shape_derivative(1,7) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,7) + dt*dIdjet(1,5,1)
      design%auxiliary%body_data(2)%shape_derivative(1,8) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,8) + dt*dIdjet(1,5,2)
      design%auxiliary%body_data(2)%shape_derivative(1,9) =                    &
         design%auxiliary%body_data(2)%shape_derivative(1,9) + dt*dIdjet(1,5,3)

! flap: jet 6 of 10

      design%auxiliary%body_data(3)%shape_derivative(1,1) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,1) + dt*dIdjet(1,6,1)
      design%auxiliary%body_data(3)%shape_derivative(1,2) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,2) + dt*dIdjet(1,6,2)
      design%auxiliary%body_data(3)%shape_derivative(1,3) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,3) + dt*dIdjet(1,6,3)

! flap: jet 7 of 10

      design%auxiliary%body_data(3)%shape_derivative(1,4) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,4) + dt*dIdjet(1,7,1)
      design%auxiliary%body_data(3)%shape_derivative(1,5) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,5) + dt*dIdjet(1,7,2)
      design%auxiliary%body_data(3)%shape_derivative(1,6) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,6) + dt*dIdjet(1,7,3)

! flap: jet 8 of 10

      design%auxiliary%body_data(3)%shape_derivative(1,7) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,7) + dt*dIdjet(1,8,1)
      design%auxiliary%body_data(3)%shape_derivative(1,8) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,8) + dt*dIdjet(1,8,2)
      design%auxiliary%body_data(3)%shape_derivative(1,9) =                    &
         design%auxiliary%body_data(3)%shape_derivative(1,9) + dt*dIdjet(1,8,3)

! flap: jet 9 of 10

      design%auxiliary%body_data(3)%shape_derivative(1,10) =                   &
         design%auxiliary%body_data(3)%shape_derivative(1,10) + dt*dIdjet(1,9,1)
      design%auxiliary%body_data(3)%shape_derivative(1,11) =                   &
         design%auxiliary%body_data(3)%shape_derivative(1,11) + dt*dIdjet(1,9,2)
      design%auxiliary%body_data(3)%shape_derivative(1,12) =                   &
         design%auxiliary%body_data(3)%shape_derivative(1,12) + dt*dIdjet(1,9,3)

! flap: jet 10 of 10

      design%auxiliary%body_data(3)%shape_derivative(1,13) =                   &
        design%auxiliary%body_data(3)%shape_derivative(1,13) + dt*dIdjet(1,10,1)
      design%auxiliary%body_data(3)%shape_derivative(1,14) =                   &
        design%auxiliary%body_data(3)%shape_derivative(1,14) + dt*dIdjet(1,10,2)
      design%auxiliary%body_data(3)%shape_derivative(1,15) =                   &
        design%auxiliary%body_data(3)%shape_derivative(1,15) + dt*dIdjet(1,10,3)

      deallocate(dIdjet)
    endif away_from_freestream

    if ( lmpi_master ) then

! Screen output
! Slat derivatives

      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(1)%shape_derivative(1,1)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(1)%shape_derivative(1,2)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(1)%shape_derivative(1,3)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(1)%shape_derivative(1,4)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(1)%shape_derivative(1,5)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(1)%shape_derivative(1,6)

! Main derivatives

      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,1)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,2)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,3)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,4)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,5)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,6)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,7)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,8)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(2)%shape_derivative(1,9)

! Flap derivatives

      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,1)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,2)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,3)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,4)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,5)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,6)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,7)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,8)
      write(*,*) 'Current jet derivative = ',                                  &
                             design%auxiliary%body_data(3)%shape_derivative(1,9)
      write(*,*) 'Current jet derivative = ',                                  &
                            design%auxiliary%body_data(3)%shape_derivative(1,10)
      write(*,*) 'Current jet derivative = ',                                  &
                            design%auxiliary%body_data(3)%shape_derivative(1,11)
      write(*,*) 'Current jet derivative = ',                                  &
                            design%auxiliary%body_data(3)%shape_derivative(1,12)
      write(*,*) 'Current jet derivative = ',                                  &
                            design%auxiliary%body_data(3)%shape_derivative(1,13)
      write(*,*) 'Current jet derivative = ',                                  &
                            design%auxiliary%body_data(3)%shape_derivative(1,14)
      write(*,*) 'Current jet derivative = ',                                  &
                            design%auxiliary%body_data(3)%shape_derivative(1,15)

! File output
! Slat derivatives

      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(1)%shape_derivative(1,1)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(1)%shape_derivative(1,2)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(1)%shape_derivative(1,3)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(1)%shape_derivative(1,4)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(1)%shape_derivative(1,5)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(1)%shape_derivative(1,6)

! Main derivatives

      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,1)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,2)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,3)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,4)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,5)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,6)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,7)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,8)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(2)%shape_derivative(1,9)

! Flap derivatives

      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,1)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,2)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,3)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,4)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,5)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,6)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,7)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,8)
      write(unit_73,*) 'Current jet derivative = ',                            &
                             design%auxiliary%body_data(3)%shape_derivative(1,9)
      write(unit_73,*) 'Current jet derivative = ',                            &
                            design%auxiliary%body_data(3)%shape_derivative(1,10)
      write(unit_73,*) 'Current jet derivative = ',                            &
                            design%auxiliary%body_data(3)%shape_derivative(1,11)
      write(unit_73,*) 'Current jet derivative = ',                            &
                            design%auxiliary%body_data(3)%shape_derivative(1,12)
      write(unit_73,*) 'Current jet derivative = ',                            &
                            design%auxiliary%body_data(3)%shape_derivative(1,13)
      write(unit_73,*) 'Current jet derivative = ',                            &
                            design%auxiliary%body_data(3)%shape_derivative(1,14)
      write(unit_73,*) 'Current jet derivative = ',                            &
                            design%auxiliary%body_data(3)%shape_derivative(1,15)
    endif

  end subroutine jet_driver


!=============================== LINEARIZE_INITIAL_STATEA ====================80
!
!  Linearize Q at the initial state wrt alpha
!
!=============================================================================80
  subroutine linearize_initial_statea(dfs_dq_lambda,nfunctions,nnodes0,rlam,   &
                                      adim,nnodes01,vol,nbound,bc,eqn_set)

    use fun3d_constants, only : pi, my_0, my_180
    use info_depr,       only : xmach, alpha, yaw, ivisc
    use ivals,           only : rho0, u0, v0, w0
    use bc_types,        only : bcgrid_type
    use bc_names,        only : bc_initialized_solid
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_reduce, lmpi_bcast
    use solution_types,  only : compressible, incompressible
    use rotors,          only : alternate_freestream, vinf_input_ratio

    integer, intent(in) :: nfunctions, nnodes0, adim, nnodes01, nbound, eqn_set

    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)  :: rlam
    real(dp), dimension(nnodes01),                 intent(in)  :: vol
    real(dp), dimension(2,nfunctions),             intent(out) :: dfs_dq_lambda

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: i, j, ib, k

    real(dp) :: conv, u0a, v0a, w0a, Q2a, Q3a, Q4a, Q5a
    real(dp) :: u0b, v0b, w0b, Q2b, Q3b, Q4b, Q5b

    real(dp), dimension(2) :: tempvar

    logical, dimension(nnodes0) :: node_hit

  continue

! Initialize

    dfs_dq_lambda = my_0

    Q2a = my_0
    Q3a = my_0
    Q4a = my_0
    Q5a = my_0

    Q2b = my_0
    Q3b = my_0
    Q4b = my_0
    Q5b = my_0

    node_hit = .false.

! Take care of no-slip nodes first (they have no dependence on alpha)

    if ( ivisc > 0 ) then
      do ib = 1, nbound
        if ( bc_initialized_solid(bc(ib)%ibc) ) then
          do i = 1, bc(ib)%nbnode
            k = bc(ib)%ibnode(i)
            if ( k <= nnodes0 ) node_hit(k) = .true.
          end do
        endif
      end do
    endif

    conv = my_180/pi

    select case(eqn_set)
    case (compressible)

      u0a  = -xmach*sin(alpha/conv)/conv*cos(yaw/conv)
      v0a  = 0.0_dp
      w0a  =  xmach*cos(alpha/conv)/conv*cos(yaw/conv)

      u0b  = -xmach*cos(alpha/conv)/conv*sin(yaw/conv)
      v0b  = -xmach*cos(yaw/conv)/conv
      w0b  = -xmach*sin(alpha/conv)/conv*sin(yaw/conv)

      if ( alternate_freestream ) then
        u0a = vinf_input_ratio*u0a
        v0a = vinf_input_ratio*v0a
        w0a = vinf_input_ratio*w0a

        u0b = vinf_input_ratio*u0b
        v0b = vinf_input_ratio*v0b
        w0b = vinf_input_ratio*w0b
      endif

      Q2a = rho0*u0a
      Q3a = rho0*v0a
      Q4a = rho0*w0a
      Q5a = rho0*(u0*u0a + w0*w0a)

      Q2b = rho0*u0b
      Q3b = rho0*v0b
      Q4b = rho0*w0b
      Q5b = rho0*(u0*u0b + v0*v0b + w0*w0b)

    case (incompressible)

      u0a  = -sin(alpha/conv)/conv*cos(yaw/conv)
      v0a  = 0.0_dp
      w0a  =  cos(alpha/conv)/conv*cos(yaw/conv)

      u0b  = -cos(alpha/conv)/conv*sin(yaw/conv)
      v0b  = -cos(yaw/conv)/conv
      w0b  = -sin(alpha/conv)/conv*sin(yaw/conv)

      if ( alternate_freestream ) then
        u0a = vinf_input_ratio*u0a
        v0a = vinf_input_ratio*v0a
        w0a = vinf_input_ratio*w0a

        u0b = vinf_input_ratio*u0b
        v0b = vinf_input_ratio*v0b
        w0b = vinf_input_ratio*w0b
      endif

      Q2a = u0a
      Q3a = 0.0_dp
      Q4a = w0a

      Q2b = u0b
      Q3b = v0b
      Q4b = w0b

    end select

! Take care of the rest of the field

    do i = 1, nnodes0
      if ( node_hit(i) ) cycle
      do j = 1, nfunctions
        select case(eqn_set)
        case (compressible)
          dfs_dq_lambda(1,j) = dfs_dq_lambda(1,j) + (Q2a*rlam(2,i,j)           &
                                                  +  Q3a*rlam(3,i,j)           &
                                                  +  Q4a*rlam(4,i,j)           &
                                                  +  Q5a*rlam(5,i,j))*vol(i)
          dfs_dq_lambda(2,j) = dfs_dq_lambda(2,j) + (Q2b*rlam(2,i,j)           &
                                                  +  Q3b*rlam(3,i,j)           &
                                                  +  Q4b*rlam(4,i,j)           &
                                                  +  Q5b*rlam(5,i,j))*vol(i)
        case (incompressible)
          dfs_dq_lambda(1,j) = dfs_dq_lambda(1,j) + (Q2a*rlam(2,i,j)           &
                                                  +  Q3a*rlam(3,i,j)           &
                                                  +  Q4a*rlam(4,i,j))*vol(i)
          dfs_dq_lambda(2,j) = dfs_dq_lambda(2,j) + (Q2b*rlam(2,i,j)           &
                                                  +  Q3b*rlam(3,i,j)           &
                                                  +  Q4b*rlam(4,i,j))*vol(i)
        end select
      end do
    end do

! Reduce the result

    fcn_loop : do j = 1, nfunctions
      call lmpi_reduce(dfs_dq_lambda(:,j),tempvar)
      call lmpi_bcast(tempvar)
      dfs_dq_lambda(:,j) = tempvar
    end do fcn_loop

  end subroutine linearize_initial_statea


!=============================== LINEARIZE_INITIAL_STATEM ====================80
!
!  Linearize Q at the initial state wrt Mach number
!
!=============================================================================80
  subroutine linearize_initial_statem(dfs_dq_lambda,nfunctions,nnodes0,rlam,   &
                                      adim,nnodes01,vol,nbound,bc)

    use fun3d_constants, only : pi, my_0, my_180
    use info_depr,       only : alpha, yaw, ivisc
    use ivals,           only : rho0, u0, v0, w0
    use bc_types,        only : bcgrid_type
    use bc_names,        only : bc_initialized_solid
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_reduce, lmpi_bcast

    integer, intent(in) :: nfunctions, nnodes0, adim, nnodes01, nbound

    real(dp), dimension(adim,nnodes01,nfunctions), intent(in)  :: rlam
    real(dp), dimension(nnodes01),                 intent(in)  :: vol
    real(dp), dimension(nfunctions),               intent(out) :: dfs_dq_lambda

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: i, j, k, ib

    real(dp) :: conv, u0m, v0m, w0m, Q2m, Q3m, Q4m, Q5m, tempvar

    logical, dimension(nnodes0) :: node_hit

  continue

! Initialize

    dfs_dq_lambda = my_0
    node_hit = .false.

! Take care of no-slip nodes first (they have no dependence on Mach)

    if ( ivisc > 0 ) then
      do ib = 1, nbound
        if ( bc_initialized_solid(bc(ib)%ibc) ) then
          do i = 1, bc(ib)%nbnode
            k = bc(ib)%ibnode(i)
            if ( k <= nnodes0 ) node_hit(k) = .true.
          end do
        endif
      end do
    endif

    conv = my_180/pi
    u0m  = cos(alpha/conv) * cos(yaw/conv)
    v0m  = - sin(yaw/conv)
    w0m  = sin(alpha/conv) * cos(yaw/conv)

    Q2m = rho0*u0m
    Q3m = rho0*v0m
    Q4m = rho0*w0m
    Q5m = rho0*(u0*u0m + v0*v0m + w0*w0m)

    do i = 1, nnodes0
     if ( node_hit(i) ) cycle
     do j = 1, nfunctions
      dfs_dq_lambda(j) = dfs_dq_lambda(j) + (Q2m*rlam(2,i,j) + Q3m*rlam(3,i,j) &
                                          +  Q4m*rlam(4,i,j) + Q5m*rlam(5,i,j))&
                                             *vol(i)
     end do
    end do

! Reduce the result

    fcn_loop : do j = 1, nfunctions
      call lmpi_reduce(dfs_dq_lambda(j),tempvar)
      call lmpi_bcast(tempvar)
      dfs_dq_lambda(j) = tempvar
    end do fcn_loop

  end subroutine linearize_initial_statem


!========================== UPDATE_TEMPORAL_ADJ_BACKPLANES ===================80
!
!  Shuffles current adjoint field into backplanes
!
!=============================================================================80
  subroutine update_temporal_adj_backplanes(sadj)

    use nml_nonlinear_solves, only : itime
    use solution_adj,         only : sadj_type
    use lmpi,                 only : lmpi_die

    type(sadj_type), intent(inout) :: sadj

  continue

    select case(itime)
    case(0)  ! Steady-state
    case(1)  ! BDF1
      sadj%rlamatn  = sadj%rlam
    case(2)  ! BDF2
      sadj%rlamatn1 = sadj%rlamatn
      sadj%rlamatn  = sadj%rlam
    case(3)  ! BDF3/BDF2opt
      sadj%rlamatn2 = sadj%rlamatn1
      sadj%rlamatn1 = sadj%rlamatn
      sadj%rlamatn  = sadj%rlam
    case default
      write(*,*) 'Invalid value of itime in update_temporal_adj_backplanes',   &
                 itime
      call lmpi_die
    end select

  end subroutine update_temporal_adj_backplanes


!========================== UPDATE_GEOMETRIC_BACKPLANES ======================80
!
!  Shuffles current geometry into backplanes
!
!=============================================================================80
  subroutine update_geometric_backplanes(grid)

    use nml_nonlinear_solves, only : itime
    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_die
    use nml_overset_data,     only : overset_flag
    use designs,              only : iblankn, iblankn1, iblankn2
    use allocations,          only : my_alloc_ptr

    type(grid_type), intent(inout) :: grid

    logical, save :: first_time_through = .true.

  continue

    select case(itime)
    case(0)  ! Steady-state
    case(1)  ! BDF1
      grid%volatn   = grid%vol
    case(2)  ! BDF2
      grid%volatn1  = grid%volatn
      grid%volatn   = grid%vol
    case(3)  ! BDF3/BDF2opt
      grid%volatn2  = grid%volatn1
      grid%volatn1  = grid%volatn
      grid%volatn   = grid%vol
    case default
      write(*,*) 'Invalid value of itime in update_geometric_backplanes', itime
      call lmpi_die
    end select

! if overset, shuffle the iblank information too

    shuffle_iblank : if ( overset_flag ) then

      if ( first_time_through ) then
        call my_alloc_ptr(iblankn, grid%nnodes01)
        call my_alloc_ptr(iblankn1,grid%nnodes01)
        call my_alloc_ptr(iblankn2,grid%nnodes01)
      endif

      select case(itime)
      case(0)  ! Steady-state
      case(1)  ! BDF1
        iblankn  = grid%iblank
      case(2)  ! BDF2
        iblankn1 = iblankn
        iblankn  = grid%iblank
      case(3)  ! BDF3/BDF2opt
        iblankn2 = iblankn1
        iblankn1 = iblankn
        iblankn  = grid%iblank
      case default
        write(*,*) 'Invalid value of itime in update_geometric_backplanes',itime
        call lmpi_die
      end select

    endif shuffle_iblank

    first_time_through = .false.

  end subroutine update_geometric_backplanes


!========================== SET_UP_GRID_AT_TIMELEVEL =========================80
!
!  Loads the grid coordinates for the specified timelevel and recomputes
!  correct metrics, speeds, etc
!
!=============================================================================80
  subroutine set_up_grid_at_timelevel(grid,use_initial_grid,crowf)

    use grid_types,          only : grid_type
    use moving_body_types,   only : moving_body
    use nml_grid_motion,     only : n_moving_bodies
    use fun3d_constants,     only : my_0, my_1
    use grid_motion,         only : bndry_velocity
    use distance_function,   only : compute_distance_function,                 &
                                    turb_dist_needs_update
    use nml_global,          only : moving_grid
    use grid_metrics,        only : compute_dual_metrics
    use kinddefs,            only : dp
    use allocations,         only : my_alloc_ptr
    use comprow_types,       only : crow_flow
    use gradient_driver,     only : sumgs_variable

    logical, intent(in) :: use_initial_grid

    type(crow_flow), intent(inout) :: crowf
    type(grid_type), intent(inout) :: grid

    integer :: ib, body

    real(dp), dimension(1,grid%nnodes01) :: work_space

    logical :: check_volumes, transfer_data, update_face_speed

  continue

    if ( .not. res_gcl_save_allocated ) then
      call my_alloc_ptr(res_gcl_save, 1, grid%nnodes01)
      res_gcl_save_allocated = .true.
    endif

! First load in the appropriate grid coordinates

    if ( use_initial_grid ) then   ! fix this case (speeds)?
      grid%x = grid%xat0
      grid%y = grid%yat0
      grid%z = grid%zat0
      grid%dxdt = my_0
      grid%dydt = my_0
      grid%dzdt = my_0
      grid%res_gcl = res_gcl_save
      do body = 1, n_moving_bodies
        moving_body(body)%transform_matrix(:,:) = my_0
        moving_body(body)%transform_matrix(1,1) = my_1
        moving_body(body)%transform_matrix(2,2) = my_1
        moving_body(body)%transform_matrix(3,3) = my_1
        moving_body(body)%transform_matrix(4,4) = my_1
      end do
    endif

! Initialize GCL, boundary speeds

    grid%res_gcl(1,:) = my_0

    do ib = 1, grid%nbound
      grid%bc(ib)%bdxdt(:) = my_0
      grid%bc(ib)%bdydt(:) = my_0
      grid%bc(ib)%bdzdt(:) = my_0
    end do

! Set boundary speeds

    do ib = 1, grid%nbound
      call bndry_velocity(grid%dxdt,         grid%dydt,                        &
                          grid%dzdt,         grid%nnodes01,                    &
                          grid%bc(ib)%bdxdt, grid%bc(ib)%bdydt,                &
                          grid%bc(ib)%bdzdt, grid%bc(ib)%ibnode,               &
                          grid%bc(ib)%nbnode)
    end do

! Compute metrics, face speeds, GCL

    check_volumes     = .true.
    transfer_data     = .true.
    update_face_speed = .true.

    call compute_dual_metrics(grid, moving_grid, check_volumes, transfer_data, &
                              update_face_speed)

! For safety in moving grid cases, set the advance volumes to the initial
! volumes if we have requested the initial grid

    if ( moving_grid .and. use_initial_grid ) then
      grid%volatnp1 = grid%vol
      grid%volatnp2 = grid%vol
    endif

! We actually only need the GCL term at the previous timestep (in adjoint time)
! so save a copy of the GCL at the current timestep and insert the value at
! the previous timestep (rather than storing a res_gclatn term)

    work_space(1,1:grid%nnodes01)   = res_gcl_save(1,1:grid%nnodes01)
    res_gcl_save(1,1:grid%nnodes01) = grid%res_gcl(1,1:grid%nnodes01)
    grid%res_gcl(1,1:grid%nnodes01) = work_space(1,1:grid%nnodes01)

! Update least squares weights for reconstruction

    call sumgs_variable(grid,crowf)

! Recompute the minimum distance function for viscous cases
! (for non-overset, rigid mesh cases, there is no need to recompute slen)

    if(turb_dist_needs_update(grid)) then
        call compute_distance_function(grid, .false. )
    end if

  end subroutine set_up_grid_at_timelevel


!========================== UPDATE_GRIDADJ_RHS ===============================80
!
!  Adds T*lambdag sum to the grid adjoint RHS and writes it out to disk
!
!=============================================================================80
  subroutine update_gridadj_rhs(nnodes0,nfunctions,gridadj_rhs,tlambdag)

    use kinddefs, only : dp

    integer, intent(in) :: nnodes0, nfunctions

    real(dp), dimension(3,nnodes0,nfunctions), intent(in)    :: tlambdag
    real(dp), dimension(3,nnodes0,nfunctions), intent(inout) :: gridadj_rhs

    integer :: i, j

  continue

    do j = 1, nfunctions
      do i = 1, nnodes0
        gridadj_rhs(1,i,j) = gridadj_rhs(1,i,j) + tlambdag(1,i,j)
        gridadj_rhs(2,i,j) = gridadj_rhs(2,i,j) + tlambdag(2,i,j)
        gridadj_rhs(3,i,j) = gridadj_rhs(3,i,j) + tlambdag(3,i,j)
      end do
    end do

  end subroutine update_gridadj_rhs


!========================== RIGID_MESH_ADJOINT ===============================80
!
!  For rigidly moving grids, linearizes the transformation matrices
!
!=============================================================================80
  subroutine rigid_mesh_adjoint(design,grid,lambda,t_local)

    use fun3d_constants,   only : my_0
    use design_types,      only : design_type
    use grid_types,        only : grid_type
    use lmpi,              only : lmpi_die
    use info_depr,         only : simulation_time, physical_timestep
    use moving_body_types, only : moving_body
    use nml_grid_motion,   only : n_moving_bodies
    use custom_transforms, only : custom_kinematics

    real(dp), dimension(:,:,:), intent(in) :: lambda
    real(dp), dimension(4,4,n_moving_bodies), intent(in) :: t_local

    type(design_type), intent(inout) :: design
    type(grid_type),   intent(in)    :: grid

    integer :: idv, ifcn, current_body, j, i, parent, nreal, body

    real(dp) :: rotation_time, translation_time

    real(dp), dimension(design%nfunctions) :: deriv

    real(dp), dimension(4,4) :: dtransform_matrix, identity
    real(dp), dimension(4,4) :: t_ext_bound, t_def_bound
    real(dp), dimension(4,4,n_moving_bodies) :: t_def_stored
    real(dp), dimension(design%nfunctions) :: answer

    logical :: been_here_def, been_here_ext

  continue

    parent = -1 ! satisfy compiler complaint that this is otherwise undefined
                ! in some cases

! Set identity matrix

    identity = 0.0_dp
    identity(1,1) = 1.0_dp
    identity(2,2) = 1.0_dp
    identity(3,3) = 1.0_dp
    identity(4,4) = 1.0_dp

! Now we need to linearize the surface mesh coordinates wrt the design variables
! controlling the rigid body movement

    body_loop1 : do body = 1, n_moving_bodies

! Set the current values of time

      rotation_time    = simulation_time                                       &
                               - moving_body(body)%rotation_vector%start_time
      translation_time = simulation_time                                       &
                               - moving_body(body)%translation_vector%start_time

! Go through function data and see which rigid motion design variables
! are active, and linearize wrt them

      rigid_dv_loop : do idv = 1, design%rigid_data(body)%ndv
        rigid_active : if ( design%rigid_data(body)%active(idv) ) then

          dtransform_matrix(:,:) = my_0

          select case(design%rigid_data(body)%name(idv))
          case('RotRate')
            if ( moving_body(body)%rotate == 1 ) then
              dtransform_matrix = drotrate(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector)
            endif
          case('RotFreq')
            if ( moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotfreq(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector)
            endif
          case('RotAmpl')
            if ( moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotrate(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector)
            endif
          case('RotOrgx')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotorig(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,1)
            endif
          case('RotOrgy')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotorig(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,2)
            endif
          case('RotOrgz')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotorig(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,3)
            endif
          case('RotVecx')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotvect(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,1)
            endif
          case('RotVecy')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotvect(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,2)
            endif
          case('RotVecz')
            if ( moving_body(body)%rotate == 1 .or.                            &
                 moving_body(body)%rotate == 2 ) then
              dtransform_matrix = drotvect(rotation_time,                      &
                                           moving_body(body)%rotate,           &
                                           moving_body(body)%rotrate,          &
                                           moving_body(body)%rfreq_rotate,     &
                                           moving_body(body)%rotation_vector,3)
            endif
          case('TrnRate')
            if ( moving_body(body)%translate == 1 ) then
              dtransform_matrix =dtrnrate(translation_time,                    &
                                          moving_body(body)%translate,         &
                                          moving_body(body)%rfreq_trans,       &
                                          moving_body(body)%translation_vector)
            endif
          case('TrnFreq')
            if ( moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnfreq(translation_time,                    &
                                          moving_body(body)%translate,         &
                                          moving_body(body)%transrate,         &
                                          moving_body(body)%rfreq_trans,       &
                                          moving_body(body)%translation_vector)
            endif
          case('TrnAmpl')
            if ( moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnrate(translation_time,                    &
                                          moving_body(body)%translate,         &
                                          moving_body(body)%rfreq_trans,       &
                                          moving_body(body)%translation_vector)
            endif
          case('TrnVecx')
            if ( moving_body(body)%translate == 1 .or.                         &
                 moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnvect(moving_body(body)%translation_vector,&
                                          1,body)
            endif
          case('TrnVecy')
            if ( moving_body(body)%translate == 1 .or.                         &
                 moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnvect(moving_body(body)%translation_vector,&
                                          2,body)
            endif
          case('TrnVecz')
            if ( moving_body(body)%translate == 1 .or.                         &
                 moving_body(body)%translate == 2 ) then
              dtransform_matrix =dtrnvect(moving_body(body)%translation_vector,&
                                          3,body)
            endif
          case default
            write(*,*) 'Unknown rigid DV name...stopping.'
            call lmpi_die
          end select

          t_def_stored(:,:,:) = 0.0_dp

          compute_dtdd_on_a_body : do j = 1, n_moving_bodies

! Start off our chain of transform matrices to be applied to the defining body
! with identity

            t_def_bound = identity
            t_ext_bound = identity

            been_here_def = .false.
            been_here_ext = .false.

! String together all of the parents from oldest to youngest

            current_body = j

            find_parents : do

              if ( trim(moving_body(current_body)%parent_name) == '' ) then
                exit find_parents
              endif

              if ( trim(moving_body(current_body)%parent_name) ==              &
                   trim(moving_body(current_body)%body_name) ) then  ! fake body
                parent = current_body-1
              else
                search5 : do i = 1, n_moving_bodies
                  if ( trim(moving_body(i)%body_name) ==                       &
                       trim(moving_body(current_body)%parent_name) .and.       &
                       .not.moving_body(i)%fake_body ) then
                    parent = i
                    exit search5
                  endif
                end do search5
              endif

              if ( parent == body ) then
                t_def_bound = matmul(dtransform_matrix,t_def_bound)
                been_here_def = .true.
              else
                t_def_bound = matmul(t_local(:,:,parent),t_def_bound)
              endif

              if ( trim(moving_body(parent)%mesh_movement) == 'rigid' ) then
                if ( parent == body ) then
                  been_here_ext = .true.
                  t_ext_bound = matmul(dtransform_matrix,t_ext_bound)
                else
                  t_ext_bound = matmul(t_local(:,:,parent),t_ext_bound)
                endif
              endif

              current_body = parent

            end do find_parents

! Multiply in the current dT/dD

            if ( j == body ) then
              t_def_bound = matmul(t_def_bound,dtransform_matrix)
            else
              if ( been_here_def ) then
                t_def_bound = matmul(t_def_bound,t_local(:,:,j))
              else
                t_def_bound = 0.0_dp
              endif
            endif

            if ( trim(moving_body(j)%mesh_movement) == 'rigid' ) then
              if ( j == body ) then
                t_ext_bound = matmul(t_ext_bound,dtransform_matrix)
              else
                if ( been_here_ext ) then
                  t_ext_bound = matmul(t_ext_bound,t_local(:,:,j))
                else
                  t_ext_bound = 0.0_dp
                endif
              endif
            else
              if ( been_here_ext ) then
                ! do nothing
              else
                t_ext_bound = 0.0_dp
              endif
            endif

! Set stored matrices

            t_def_stored(:,:,j) = t_def_bound(:,:)

          end do compute_dtdd_on_a_body

          answer = 0.0_dp

          nreal = 0

          derivs : do i = 1, n_moving_bodies

            if ( moving_body(i)%fake_body ) cycle derivs

            nreal = nreal + 1

            call linearize_coords(grid,deriv,design%nfunctions,lambda,         &
                                  t_def_stored(:,:,i),nreal)

            do ifcn = 1, design%nfunctions
              answer(ifcn) = answer(ifcn) + deriv(ifcn)
            end do

          end do derivs

          do ifcn = 1, design%nfunctions
            design%rigid_data(body)%shape_derivative(ifcn,idv) = answer(ifcn)
          end do

        endif rigid_active

      end do rigid_dv_loop

! Go through function data and see which kinematic motion design variables
! are active, and linearize wrt them

      custom_motion : if(trim(moving_body(body)%motion_driver) == 'custom') then

        custom_dv_loop : do idv = 1, design%kinematic%body_data(body)%ndv
          custom_active : if (design%kinematic%body_data(body)%active(idv)==1) &
          then

            dtransform_matrix(:,:) = my_0

            call custom_kinematics(design%kinematic%body_data(body)%ndv,       &
                                   simulation_time,                            &
                                   design%kinematic%body_data(body)%value,1,   &
                                   ind=idv,dtdd=dtransform_matrix)

            t_def_stored(:,:,:) = 0.0_dp

            compute_dtdd_on_a_body2 : do j = 1, n_moving_bodies

! Start off our chain of transform matrices to be applied to the defining body
! with identity

              t_def_bound = identity
              t_ext_bound = identity

              been_here_def = .false.
              been_here_ext = .false.

! String together all of the parents from oldest to youngest

              current_body = j

              find_parents2 : do

                if ( trim(moving_body(current_body)%parent_name) == '' ) then
                  exit find_parents2
                endif

                if ( trim(moving_body(current_body)%parent_name) ==            &
                     trim(moving_body(current_body)%body_name) ) then !fake body
                  parent = current_body-1
                else
                  search6 : do i = 1, n_moving_bodies
                    if ( trim(moving_body(i)%body_name) ==                     &
                         trim(moving_body(current_body)%parent_name) .and.     &
                         .not.moving_body(i)%fake_body ) then
                      parent = i
                      exit search6
                    endif
                  end do search6
                endif

                if ( parent == body ) then
                  t_def_bound = matmul(dtransform_matrix,t_def_bound)
                  been_here_def = .true.
                else
                  t_def_bound = matmul(t_local(:,:,parent),t_def_bound)
                endif

                if ( trim(moving_body(parent)%mesh_movement) == 'rigid' ) then
                  if ( parent == body ) then
                    been_here_ext = .true.
                    t_ext_bound = matmul(dtransform_matrix,t_ext_bound)
                  else
                    t_ext_bound = matmul(t_local(:,:,parent),t_ext_bound)
                  endif
                endif

                current_body = parent

              end do find_parents2

! Multiply in the current dT/dD

              if ( j == body ) then
                t_def_bound = matmul(t_def_bound,dtransform_matrix)
              else
                if ( been_here_def ) then
                  t_def_bound = matmul(t_def_bound,t_local(:,:,j))
                else
                  t_def_bound = 0.0_dp
                endif
              endif

              if ( trim(moving_body(j)%mesh_movement) == 'rigid' ) then
                if ( j == body ) then
                  t_ext_bound = matmul(t_ext_bound,dtransform_matrix)
                else
                  if ( been_here_ext ) then
                    t_ext_bound = matmul(t_ext_bound,t_local(:,:,j))
                  else
                    t_ext_bound = 0.0_dp
                  endif
                endif
              else
                if ( been_here_ext ) then
                  ! do nothing
                else
                  t_ext_bound = 0.0_dp
                endif
              endif

! Set stored matrices

              t_def_stored(:,:,j) = t_def_bound(:,:)

            end do compute_dtdd_on_a_body2

            answer = 0.0_dp

            nreal = 0

            derivs2 : do i = 1, n_moving_bodies

              if ( moving_body(i)%fake_body ) cycle derivs2

              nreal = nreal + 1

              call linearize_coords(grid,deriv,design%nfunctions,lambda,       &
                                    t_def_stored(:,:,i),nreal)

              do ifcn = 1, design%nfunctions
                answer(ifcn) = answer(ifcn) + deriv(ifcn)
              end do

            end do derivs2

            do ifcn = 1, design%nfunctions
              design%kinematic%body_data(body)%shape_derivative(ifcn,idv) =    &
                                                                    answer(ifcn)
            end do

          endif custom_active

        end do custom_dv_loop

      endif custom_motion

! Go through function data and see which trimming design variables
! are active, and linearize wrt them

      trim_motion : if ( trim(moving_body(body)%trim_control) == 'design' ) then

        trim_dv_loop : do idv = 1, design%trimming%body_data(body)%ndv
          trim_active : if (design%trimming%body_data(body)%active(idv) == 1)  &
          then

            dtransform_matrix(:,:) = my_0

            dtransform_matrix = dtrim(moving_body(body)%rotation_vector,       &
                                      moving_body(body)%steps_per_period,      &
                                      physical_timestep,                       &
                                      moving_body(body)%baseline_psi,          &
                                      design%trimming%body_data(body)%value(1),&
                                      design%trimming%body_data(body)%value(2),&
                                      design%trimming%body_data(body)%value(3),&
                                      idv)

            t_def_stored(:,:,:) = 0.0_dp

            compute_dtdd_on_a_body3 : do j = 1, n_moving_bodies

! Start off our chain of transform matrices to be applied to the defining body
! with identity

              t_def_bound = identity
              t_ext_bound = identity

              been_here_def = .false.
              been_here_ext = .false.

! String together all of the parents from oldest to youngest

              current_body = j

              find_parents3 : do

                if ( trim(moving_body(current_body)%parent_name) == '' ) then
                  exit find_parents3
                endif

                if ( trim(moving_body(current_body)%parent_name) ==            &
                     trim(moving_body(current_body)%body_name) ) then !fake body
                  parent = current_body-1
                else
                  search7 : do i = 1, n_moving_bodies
                    if ( trim(moving_body(i)%body_name) ==                     &
                         trim(moving_body(current_body)%parent_name) .and.     &
                         .not.moving_body(i)%fake_body ) then
                      parent = i
                      exit search7
                    endif
                  end do search7
                endif

                if ( parent == body ) then
                  t_def_bound = matmul(dtransform_matrix,t_def_bound)
                  been_here_def = .true.
                else
                  t_def_bound = matmul(t_local(:,:,parent),t_def_bound)
                endif

                if ( trim(moving_body(parent)%mesh_movement) == 'rigid' ) then
                  if ( parent == body ) then
                    been_here_ext = .true.
                    t_ext_bound = matmul(dtransform_matrix,t_ext_bound)
                  else
                    t_ext_bound = matmul(t_local(:,:,parent),t_ext_bound)
                  endif
                endif

                current_body = parent

              end do find_parents3

! Multiply in the current dT/dD

              if ( j == body ) then
                t_def_bound = matmul(t_def_bound,dtransform_matrix)
              else
                if ( been_here_def ) then
                  t_def_bound = matmul(t_def_bound,t_local(:,:,j))
                else
                  t_def_bound = 0.0_dp
                endif
              endif

              if ( trim(moving_body(j)%mesh_movement) == 'rigid' ) then
                if ( j == body ) then
                  t_ext_bound = matmul(t_ext_bound,dtransform_matrix)
                else
                  if ( been_here_ext ) then
                    t_ext_bound = matmul(t_ext_bound,t_local(:,:,j))
                  else
                    t_ext_bound = 0.0_dp
                  endif
                endif
              else
                if ( been_here_ext ) then
                  ! do nothing
                else
                  t_ext_bound = 0.0_dp
                endif
              endif

! Set stored matrices

              t_def_stored(:,:,j) = t_def_bound(:,:)

            end do compute_dtdd_on_a_body3

            answer = 0.0_dp

            nreal = 0

            derivs3 : do i = 1, n_moving_bodies

              if ( moving_body(i)%fake_body ) cycle derivs3

              nreal = nreal + 1

              call linearize_coords(grid,deriv,design%nfunctions,lambda,       &
                                    t_def_stored(:,:,i),nreal)

              do ifcn = 1, design%nfunctions
                answer(ifcn) = answer(ifcn) + deriv(ifcn)
              end do

            end do derivs3

            do ifcn = 1, design%nfunctions
              design%trimming%body_data(body)%shape_derivative(ifcn,idv) =     &
                                                                    answer(ifcn)
            end do

          endif trim_active

        end do trim_dv_loop

      endif trim_motion

    end do body_loop1

  end subroutine rigid_mesh_adjoint


!========================== LINEARIZE_COORDS =================================80
!
!  Linearizes the mesh coords wrt the design variables using the linearization
!  of the transformation matrices
!
!=============================================================================80
  subroutine linearize_coords(grid,deriv,nfunctions,lambda,t_def,nreal)

    use fun3d_constants,  only : my_0
    use grid_types,       only : grid_type
    use lmpi,             only : lmpi_reduce, lmpi_bcast
    use nml_overset_data, only : overset_flag

    integer, intent(in) :: nfunctions, nreal

    real(dp), dimension(nfunctions), intent(out) :: deriv

    real(dp), dimension(4,4), intent(in) :: t_def

    type(grid_type), intent(in) :: grid

    real(dp), dimension(3,grid%nnodes0,nfunctions), intent(in) :: lambda

    integer :: n,ifcn

    real(dp) :: xold,yold,zold,termx,termy,termz

    real(dp), dimension(nfunctions) :: tempvar

  continue

    deriv = my_0

    grid_loop : do n = 1, grid%nnodes0

! If overset, only include contributions from the component grids involved
! in the motion

      if ( overset_flag ) then
        if ( grid%imesh(n) /= nreal ) cycle grid_loop
      endif

      xold = grid%xat0(n)
      yold = grid%yat0(n)
      zold = grid%zat0(n)
      termx = t_def(1,1)*xold + t_def(1,2)*yold + t_def(1,3)*zold + t_def(1,4)
      termy = t_def(2,1)*xold + t_def(2,2)*yold + t_def(2,3)*zold + t_def(2,4)
      termz = t_def(3,1)*xold + t_def(3,2)*yold + t_def(3,3)*zold + t_def(3,4)
      do ifcn = 1, nfunctions
        deriv(ifcn) = deriv(ifcn) + lambda(1,n,ifcn)*termx                     &
                                  + lambda(2,n,ifcn)*termy                     &
                                  + lambda(3,n,ifcn)*termz
      end do

    end do grid_loop

! Reduce the results

    call lmpi_reduce(deriv,tempvar)
    call lmpi_bcast(tempvar)
    deriv = tempvar

  end subroutine linearize_coords


!========================== UPDATE_RIGID_VECTORS =============================80
!
!  Updates translation and rotation vectors
!
!=============================================================================80
  subroutine update_rigid_vectors(design)

    use kinddefs,          only : dp
    use moving_body_types, only : moving_body
    use nml_grid_motion,   only : n_moving_bodies
    use info_depr,         only : simulation_time, physical_timestep
    use grid_motion,       only : update_translation_vector,                   &
                                  update_rotation_vector
    use design_types,      only : design_type
    use custom_transforms, only : evaluate_trim

    type(design_type), intent(in) :: design

    integer :: body

    real(dp) :: start_time, end_time

  continue

    body_loop : do body = 1, n_moving_bodies

      if (moving_body(body)%translate > 0) then
        start_time = moving_body(body)%translation_vector%start_time
        end_time   = start_time                                                &
                   + moving_body(body)%translation_vector%duration
        if ((simulation_time>=start_time) .and. (simulation_time<=end_time))   &
        call update_translation_vector(simulation_time-start_time,             &
                                moving_body(body)%translate,                   &
                                moving_body(body)%transrate,                   &
                                moving_body(body)%rfreq_trans,                 &
                                moving_body(body)%translation_vector,          &
                                moving_body(body)%translation_phase,           &
                                moving_body(body)%translation_tphase)
      end if

      if (moving_body(body)%rotate > 0) then
        if ( trim(moving_body(body)%trim_control) == 'design' ) then
          call evaluate_trim(moving_body(body)%steps_per_period,               &
                             moving_body(body)%baseline_psi,physical_timestep, &
                             design%trimming%body_data(body)%value(1),         &
                             design%trimming%body_data(body)%value(2),         &
                             design%trimming%body_data(body)%value(3),         &
                             moving_body(body)%rotation_vector%theta,          &
                             moving_body(body)%rotation_vector%dthetadt)
        else
          start_time = moving_body(body)%rotation_vector%start_time
          end_time   = start_time                                              &
                     + moving_body(body)%rotation_vector%duration
          if ((simulation_time>=start_time) .and. (simulation_time<=end_time)) &
          call update_rotation_vector(simulation_time-start_time,              &
                               moving_body(body)%rotation_vector%duration,     &
                               moving_body(body)%rotate,                       &
                               moving_body(body)%rotrate,                      &
                               moving_body(body)%rfreq_rotate,                 &
                               moving_body(body)%rotation_vector,              &
                               moving_body(body)%rotation_phase,               &
                               moving_body(body)%rotation_tphase)
        endif
      end if

    end do body_loop

  end subroutine update_rigid_vectors


!===================================== DTRNVECT ==============================80
!
!  Linearizes the transformation matrices wrt translation vector
!
!=============================================================================80
  function dtrnvect(translation_vector,xyz,body)

    use fun3d_constants,     only : my_0
    use moving_body_types,   only : translation_vector_type
    use nml_grid_motion,     only : translation_vector_x, translation_vector_y,&
                                    translation_vector_z

    integer, intent(in) :: xyz, body

    real(dp), dimension(4,4) :: dtrnvect

    type(translation_vector_type), intent(in) :: translation_vector

    real(dp) :: vx, vy, vz, vnorm, dxv, dyv, dzv, vnormvx, vnormvy, vnormvz
    real(dp) :: sxvx, sxvy, sxvz, syvx, syvy, syvz, szvx, szvy, szvz, ds

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

    dtrnvect(1,1) = my_0
    dtrnvect(1,2) = my_0
    dtrnvect(1,3) = my_0

    dtrnvect(2,1) = my_0
    dtrnvect(2,2) = my_0
    dtrnvect(2,3) = my_0

    dtrnvect(3,1) = my_0
    dtrnvect(3,2) = my_0
    dtrnvect(3,3) = my_0

! Linearize the translation part of the matrix

! Raw input vector

    vx = translation_vector_x(body)
    vy = translation_vector_y(body)
    vz = translation_vector_z(body)

    vnorm = sqrt(vx*vx + vy*vy + vz*vz)

    vnormvx = vx/vnorm
    vnormvy = vy/vnorm
    vnormvz = vz/vnorm

!   sx = vx/vnorm
!   sy = vy/vnorm
!   sz = vz/vnorm

    sxvx = (vnorm - vx*vnormvx)/vnorm/vnorm
    sxvy = (      - vx*vnormvy)/vnorm/vnorm
    sxvz = (      - vx*vnormvz)/vnorm/vnorm

    syvx = (      - vy*vnormvx)/vnorm/vnorm
    syvy = (vnorm - vy*vnormvy)/vnorm/vnorm
    syvz = (      - vy*vnormvz)/vnorm/vnorm

    szvx = (      - vz*vnormvx)/vnorm/vnorm
    szvy = (      - vz*vnormvy)/vnorm/vnorm
    szvz = (vnorm - vz*vnormvz)/vnorm/vnorm

    ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

    if ( xyz == 1 ) then
      dxv = ds*sxvx
      dyv = ds*syvx
      dzv = ds*szvx
    else if ( xyz == 2 ) then
      dxv = ds*sxvy
      dyv = ds*syvy
      dzv = ds*szvy
    else if ( xyz == 3 ) then
      dxv = ds*sxvz
      dyv = ds*syvz
      dzv = ds*szvz
    else
      dxv = my_0
      dyv = my_0
      dzv = my_0
    endif

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    dtrnvect(1,4) = dxv
    dtrnvect(2,4) = dyv
    dtrnvect(3,4) = dzv

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    dtrnvect(4,1) = my_0
    dtrnvect(4,2) = my_0
    dtrnvect(4,3) = my_0
    dtrnvect(4,4) = my_0

  end function dtrnvect


!===================================== DTRNFREQ ==============================80
!
!  Linearizes the transformation matrices wrt translation frequency
!
!=============================================================================80
  function dtrnfreq(simtime,translate,transrate,rfreq_trans,translation_vector)

    use fun3d_constants,   only : my_0, my_2, pi
    use moving_body_types, only : translation_vector_type

    integer, intent(in) :: translate

    real(dp), intent(in) :: simtime, transrate, rfreq_trans

    real(dp), dimension(4,4) :: dtrnfreq

    type(translation_vector_type), intent(in) :: translation_vector

    real(dp) :: dsrf, dxrf, dyrf, dzrf, rfreq_transrf

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

    dtrnfreq(1,1) = my_0
    dtrnfreq(1,2) = my_0
    dtrnfreq(1,3) = my_0

    dtrnfreq(2,1) = my_0
    dtrnfreq(2,2) = my_0
    dtrnfreq(2,3) = my_0

    dtrnfreq(3,1) = my_0
    dtrnfreq(3,2) = my_0
    dtrnfreq(3,3) = my_0

! Linearize the translation part of the matrix

    if (translate == 2) then
      rfreq_transrf = my_2*pi
!     ds = transrate*sin(rfreq_trans*simtime)
        dsrf = transrate*cos(rfreq_trans*simtime)*simtime*rfreq_transrf
    else
      dsrf = my_0
    endif

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

    dxrf = translation_vector%sx * dsrf
    dyrf = translation_vector%sy * dsrf
    dzrf = translation_vector%sz * dsrf

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    dtrnfreq(1,4) = dxrf
    dtrnfreq(2,4) = dyrf
    dtrnfreq(3,4) = dzrf

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    dtrnfreq(4,1) = my_0
    dtrnfreq(4,2) = my_0
    dtrnfreq(4,3) = my_0
    dtrnfreq(4,4) = my_0

  end function dtrnfreq


!===================================== DTRNRATE ==============================80
!
!  Linearizes the transformation matrices wrt translation rate
!
!=============================================================================80
  function dtrnrate(simtime,translate,rfreq_trans,translation_vector)

    use fun3d_constants,   only : my_0
    use moving_body_types, only : translation_vector_type

    integer, intent(in) :: translate

    real(dp), intent(in) :: simtime, rfreq_trans

    real(dp), dimension(4,4) :: dtrnrate

    type(translation_vector_type), intent(in) :: translation_vector

    real(dp) :: dstr, dxtr, dytr, dztr

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

    dtrnrate(1,1) = my_0
    dtrnrate(1,2) = my_0
    dtrnrate(1,3) = my_0

    dtrnrate(2,1) = my_0
    dtrnrate(2,2) = my_0
    dtrnrate(2,3) = my_0

    dtrnrate(3,1) = my_0
    dtrnrate(3,2) = my_0
    dtrnrate(3,3) = my_0

! Linearize the translation part of the matrix

    dstr = my_0

    if (translate == 1) then
!     ds = transrate*simtime
        dstr = simtime
    endif

    if (translate == 2) then
!     ds = transrate*sin(rfreq_trans*simtime)
        dstr = sin(rfreq_trans*simtime)
    endif

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

    dxtr = translation_vector%sx * dstr
    dytr = translation_vector%sy * dstr
    dztr = translation_vector%sz * dstr

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    dtrnrate(1,4) = dxtr
    dtrnrate(2,4) = dytr
    dtrnrate(3,4) = dztr

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    dtrnrate(4,1) = my_0
    dtrnrate(4,2) = my_0
    dtrnrate(4,3) = my_0
    dtrnrate(4,4) = my_0

  end function dtrnrate


!===================================== DROTVECT ==============================80
!
!  Linearizes the transformation matrices wrt rotation vector
!
!=============================================================================80
  function drotvect(simtime,rotate,rotrate,rfreq_rotate,rotation_vector,xyz)

    use fun3d_constants,   only : my_0, my_2
    use moving_body_types, only : rotation_vector_type

    integer, intent(in) :: rotate, xyz

    real(dp), intent(in) :: simtime, rotrate, rfreq_rotate

    real(dp), dimension(4,4) :: drotvect

    type(rotation_vector_type), intent(in) :: rotation_vector

    real(dp) :: tx, ty, tz, x0, y0, z0
    real(dp) :: theta, cost, sint, term

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

    theta = my_0
    if (rotate == 1) theta = rotrate*simtime
    if (rotate == 2) theta = rotrate*sin(rfreq_rotate*simtime)

    cost = cos(theta)
    sint = sin(theta)

    term = 1._dp - cost

    tx = rotation_vector%tx
    ty = rotation_vector%ty
    tz = rotation_vector%tz

!   transform_matrix(1,1) = term*tx*tx + cost
!   transform_matrix(1,2) = term*tx*ty - tz*sint
!   transform_matrix(1,3) = term*tx*tz + ty*sint

!   transform_matrix(2,1) = term*ty*tx + tz*sint
!   transform_matrix(2,2) = term*ty*ty + cost
!   transform_matrix(2,3) = term*ty*tz - tx*sint

!   transform_matrix(3,1) = term*tz*tx - ty*sint
!   transform_matrix(3,2) = term*tz*ty + tx*sint
!   transform_matrix(3,3) = term*tz*tz + cost

    if ( xyz == 1 ) then
      drotvect(1,1) = my_2*term*tx
      drotvect(1,2) = term*ty
      drotvect(1,3) = term*tz

      drotvect(2,1) = term*ty
      drotvect(2,2) = my_0
      drotvect(2,3) = -sint

      drotvect(3,1) = term*tz
      drotvect(3,2) = sint
      drotvect(3,3) = my_0
    else if ( xyz == 2 ) then
      drotvect(1,1) = my_0
      drotvect(1,2) = term*tx
      drotvect(1,3) = sint

      drotvect(2,1) = term*tx
      drotvect(2,2) = my_2*term*ty
      drotvect(2,3) = term*tz

      drotvect(3,1) = -sint
      drotvect(3,2) = term*tz
      drotvect(3,3) = my_0
    else if ( xyz == 3 ) then
      drotvect(1,1) = my_0
      drotvect(1,2) = -sint
      drotvect(1,3) = term*tx

      drotvect(2,1) = sint
      drotvect(2,2) = my_0
      drotvect(2,3) = term*ty

      drotvect(3,1) = term*tx
      drotvect(3,2) = term*ty
      drotvect(3,3) = my_2*term*tz
    endif

! Linearize the translation part of the matrix

    x0 = rotation_vector%xorigin
    y0 = rotation_vector%yorigin
    z0 = rotation_vector%zorigin

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    drotvect(1,4) = - (drotvect(1,1)*x0 + drotvect(1,2)*y0 + drotvect(1,3)*z0)
    drotvect(2,4) = - (drotvect(2,1)*x0 + drotvect(2,2)*y0 + drotvect(2,3)*z0)
    drotvect(3,4) = - (drotvect(3,1)*x0 + drotvect(3,2)*y0 + drotvect(3,3)*z0)

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    drotvect(4,1) = my_0
    drotvect(4,2) = my_0
    drotvect(4,3) = my_0
    drotvect(4,4) = my_0

  end function drotvect


!===================================== DROTORIG ==============================80
!
!  Linearizes the transformation matrices wrt rotation origin
!
!=============================================================================80
  function drotorig(simtime,rotate,rotrate,rfreq_rotate,rotation_vector,xyz)

    use fun3d_constants,   only : my_0, my_1
    use moving_body_types, only : rotation_vector_type

    integer, intent(in) :: rotate, xyz

    real(dp), intent(in) :: simtime, rotrate, rfreq_rotate

    real(dp), dimension(4,4) :: drotorig

    type(rotation_vector_type), intent(in) :: rotation_vector

    real(dp) :: tx, ty, tz
    real(dp) :: theta, cost, sint, term

    real(dp), dimension(4,4) :: transform_matrix

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

    transform_matrix = my_0

    theta = my_0
    if (rotate == 1) theta = rotrate*simtime
    if (rotate == 2) theta = rotrate*sin(rfreq_rotate*simtime)

    cost = cos(theta)
    sint = sin(theta)

    term = my_1 - cost

    tx = rotation_vector%tx
    ty = rotation_vector%ty
    tz = rotation_vector%tz

    transform_matrix(1,1) = term*tx*tx + cost
    transform_matrix(1,2) = term*tx*ty - tz*sint
    transform_matrix(1,3) = term*tx*tz + ty*sint

    transform_matrix(2,1) = term*ty*tx + tz*sint
    transform_matrix(2,2) = term*ty*ty + cost
    transform_matrix(2,3) = term*ty*tz - tx*sint

    transform_matrix(3,1) = term*tz*tx - ty*sint
    transform_matrix(3,2) = term*tz*ty + tx*sint
    transform_matrix(3,3) = term*tz*tz + cost

    drotorig(1,1) = my_0
    drotorig(1,2) = my_0
    drotorig(1,3) = my_0

    drotorig(2,1) = my_0
    drotorig(2,2) = my_0
    drotorig(2,3) = my_0

    drotorig(3,1) = my_0
    drotorig(3,2) = my_0
    drotorig(3,3) = my_0

! Linearize the translation part of the matrix

!   x0 = rotation_vector%xorigin
!   y0 = rotation_vector%yorigin
!   z0 = rotation_vector%zorigin

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    if ( xyz == 1 ) then
      drotorig(1,4) = my_1 - transform_matrix(1,1)
      drotorig(2,4) =      - transform_matrix(2,1)
      drotorig(3,4) =      - transform_matrix(3,1)
    else if ( xyz == 2 ) then
      drotorig(1,4) =      - transform_matrix(1,2)
      drotorig(2,4) = my_1 - transform_matrix(2,2)
      drotorig(3,4) =      - transform_matrix(3,2)
    else if ( xyz == 3 ) then
      drotorig(1,4) =      - transform_matrix(1,3)
      drotorig(2,4) =      - transform_matrix(2,3)
      drotorig(3,4) = my_1 - transform_matrix(3,3)
    endif

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    drotorig(4,1) = my_0
    drotorig(4,2) = my_0
    drotorig(4,3) = my_0
    drotorig(4,4) = my_0

  end function drotorig


!===================================== DROTRATE ==============================80
!
!  Linearizes the transformation matrices wrt rotation rate
!
!=============================================================================80
  function drotrate(simtime,rotate,rotrate,rfreq_rotate,rotation_vector)

    use fun3d_constants,   only : my_0, rad_from_deg
    use moving_body_types, only : rotation_vector_type

    integer, intent(in) :: rotate

    real(dp), intent(in) :: simtime, rotrate, rfreq_rotate

    real(dp), dimension(4,4) :: drotrate

    type(rotation_vector_type), intent(in) :: rotation_vector

    real(dp) :: tx, ty, tz, x0, y0, z0, rotraterr
    real(dp) :: theta, thetarr, costrr, sintrr, termrr

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

! constant rate

    theta   = my_0
    thetarr = my_0

    if (rotate == 1) then
      theta = rotrate*simtime
        thetarr = simtime
    end if

! sinusoidal variation with time, at reduced frequency rfreq_rotate

    if (rotate == 2) then
      rotraterr = rad_from_deg
      theta = rotrate*sin(rfreq_rotate*simtime)
        thetarr = sin(rfreq_rotate*simtime)*rotraterr
    end if

!   cost = cos(theta)
      costrr = -sin(theta)*thetarr

!   sint = sin(theta)
      sintrr = cos(theta)*thetarr

!   term = 1._dp - cost
      termrr = -costrr

    tx = rotation_vector%tx
    ty = rotation_vector%ty
    tz = rotation_vector%tz

!   transform_matrix(1,1) = term*tx*tx + cost
!   transform_matrix(1,2) = term*tx*ty - tz*sint
!   transform_matrix(1,3) = term*tx*tz + ty*sint

!   transform_matrix(2,1) = term*ty*tx + tz*sint
!   transform_matrix(2,2) = term*ty*ty + cost
!   transform_matrix(2,3) = term*ty*tz - tx*sint

!   transform_matrix(3,1) = term*tz*tx - ty*sint
!   transform_matrix(3,2) = term*tz*ty + tx*sint
!   transform_matrix(3,3) = term*tz*tz + cost

    drotrate(1,1) = termrr*tx*tx + costrr
    drotrate(1,2) = termrr*tx*ty - tz*sintrr
    drotrate(1,3) = termrr*tx*tz + ty*sintrr

    drotrate(2,1) = termrr*ty*tx + tz*sintrr
    drotrate(2,2) = termrr*ty*ty + costrr
    drotrate(2,3) = termrr*ty*tz - tx*sintrr

    drotrate(3,1) = termrr*tz*tx - ty*sintrr
    drotrate(3,2) = termrr*tz*ty + tx*sintrr
    drotrate(3,3) = termrr*tz*tz + costrr

! Linearize the translation part of the matrix

    x0 = rotation_vector%xorigin
    y0 = rotation_vector%yorigin
    z0 = rotation_vector%zorigin

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    drotrate(1,4) = - (drotrate(1,1)*x0 + drotrate(1,2)*y0 + drotrate(1,3)*z0)
    drotrate(2,4) = - (drotrate(2,1)*x0 + drotrate(2,2)*y0 + drotrate(2,3)*z0)
    drotrate(3,4) = - (drotrate(3,1)*x0 + drotrate(3,2)*y0 + drotrate(3,3)*z0)

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    drotrate(4,1) = my_0
    drotrate(4,2) = my_0
    drotrate(4,3) = my_0
    drotrate(4,4) = my_0

  end function drotrate


!===================================== DROTFREQ ==============================80
!
!  Linearizes the transformation matrices wrt rotation frequency
!
!=============================================================================80
  function drotfreq(simtime,rotate,rotrate,rfreq_rotate,rotation_vector)

    use fun3d_constants,   only : my_0, my_2, pi
    use moving_body_types, only : rotation_vector_type

    integer, intent(in) :: rotate

    real(dp), intent(in) :: simtime, rotrate, rfreq_rotate

    real(dp), dimension(4,4) :: drotfreq

    type(rotation_vector_type), intent(in) :: rotation_vector

    real(dp) :: tx, ty, tz, x0, y0, z0, rfreq_rotaterf
    real(dp) :: theta, thetarf, costrf, sintrf, termrf

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

! constant rate

    theta   = my_0
    thetarf = my_0

    if (rotate == 1) then
      theta = rotrate*simtime
    end if

! sinusoidal variation with time, at reduced frequency rfreq_rotate

    if (rotate == 2) then
      rfreq_rotaterf = my_2*pi
      theta = rotrate*sin(rfreq_rotate*simtime)
        thetarf = rotrate*cos(rfreq_rotate*simtime)*simtime*rfreq_rotaterf
    end if

!   cost = cos(theta)
      costrf = -sin(theta)*thetarf

!   sint = sin(theta)
      sintrf = cos(theta)*thetarf

!   term = 1._dp - cost
      termrf = -costrf

    tx = rotation_vector%tx
    ty = rotation_vector%ty
    tz = rotation_vector%tz

!   transform_matrix(1,1) = term*tx*tx + cost
!   transform_matrix(1,2) = term*tx*ty - tz*sint
!   transform_matrix(1,3) = term*tx*tz + ty*sint

!   transform_matrix(2,1) = term*ty*tx + tz*sint
!   transform_matrix(2,2) = term*ty*ty + cost
!   transform_matrix(2,3) = term*ty*tz - tx*sint

!   transform_matrix(3,1) = term*tz*tx - ty*sint
!   transform_matrix(3,2) = term*tz*ty + tx*sint
!   transform_matrix(3,3) = term*tz*tz + cost

    drotfreq(1,1) = termrf*tx*tx + costrf
    drotfreq(1,2) = termrf*tx*ty - tz*sintrf
    drotfreq(1,3) = termrf*tx*tz + ty*sintrf

    drotfreq(2,1) = termrf*ty*tx + tz*sintrf
    drotfreq(2,2) = termrf*ty*ty + costrf
    drotfreq(2,3) = termrf*ty*tz - tx*sintrf

    drotfreq(3,1) = termrf*tz*tx - ty*sintrf
    drotfreq(3,2) = termrf*tz*ty + tx*sintrf
    drotfreq(3,3) = termrf*tz*tz + costrf

! Linearize the translation part of the matrix

    x0 = rotation_vector%xorigin
    y0 = rotation_vector%yorigin
    z0 = rotation_vector%zorigin

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    drotfreq(1,4) = - (drotfreq(1,1)*x0 + drotfreq(1,2)*y0 + drotfreq(1,3)*z0)
    drotfreq(2,4) = - (drotfreq(2,1)*x0 + drotfreq(2,2)*y0 + drotfreq(2,3)*z0)
    drotfreq(3,4) = - (drotfreq(3,1)*x0 + drotfreq(3,2)*y0 + drotfreq(3,3)*z0)

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    drotfreq(4,1) = my_0
    drotfreq(4,2) = my_0
    drotfreq(4,3) = my_0
    drotfreq(4,4) = my_0

  end function drotfreq


!===================================== DTRIM =================================80
!
!  Linearizes the transformation matrices wrt trim controls
!
!=============================================================================80
  function dtrim(rotation_vector,steps_per_period,iteration,baseline_psi,      &
                 theta_coll,theta_1c,theta_1s,key)

    use fun3d_constants,   only : my_0, rad_from_deg
    use moving_body_types, only : rotation_vector_type
    use lmpi,              only : lmpi_die

    integer, intent(in) :: steps_per_period, iteration, key

    real(dp), intent(in) :: baseline_psi, theta_coll, theta_1c, theta_1s

    real(dp), dimension(4,4) :: dtrim

    type(rotation_vector_type), intent(in) :: rotation_vector

    real(dp) :: tx, ty, tz, x0, y0, z0, dpsi, psi
    real(dp) :: theta_coll_r, theta_coll_rc1, theta_coll_rc2, theta_coll_rc3
    real(dp) :: theta_1c_r, theta_1c_rc1, theta_1c_rc2, theta_1c_rc3
    real(dp) :: theta_1s_r, theta_1s_rc1, theta_1s_rc2, theta_1s_rc3
    real(dp) :: theta, thetac1, thetac2, thetac3
    real(dp) :: costc1, costc2, costc3
    real(dp) :: sintc1, sintc2, sintc3
    real(dp) :: termc1, termc2, termc3

  continue

! Linearize the rotation part of the matrix; theta assumed to be in radians

    dpsi = 360.0_dp / steps_per_period
    psi = (iteration*dpsi + baseline_psi)*rad_from_deg

    theta_coll_r = theta_coll * rad_from_deg
      theta_coll_rc1 = rad_from_deg
      theta_coll_rc2 = 0.0_dp
      theta_coll_rc3 = 0.0_dp

    theta_1c_r   = theta_1c * rad_from_deg
      theta_1c_rc1 = 0.0_dp
      theta_1c_rc2 = rad_from_deg
      theta_1c_rc3 = 0.0_dp

    theta_1s_r   = theta_1s * rad_from_deg
      theta_1s_rc1 = 0.0_dp
      theta_1s_rc2 = 0.0_dp
      theta_1s_rc3 = rad_from_deg

    theta = theta_coll_r + theta_1c_r*cos(psi) + theta_1s_r*sin(psi)
      thetac1 = theta_coll_rc1 + theta_1c_rc1*cos(psi) + theta_1s_rc1*sin(psi)
      thetac2 = theta_coll_rc2 + theta_1c_rc2*cos(psi) + theta_1s_rc2*sin(psi)
      thetac3 = theta_coll_rc3 + theta_1c_rc3*cos(psi) + theta_1s_rc3*sin(psi)

!   cost = cos(theta)
      costc1 = -sin(theta)*thetac1
      costc2 = -sin(theta)*thetac2
      costc3 = -sin(theta)*thetac3

!   sint = sin(theta)
      sintc1 = cos(theta)*thetac1
      sintc2 = cos(theta)*thetac2
      sintc3 = cos(theta)*thetac3

!   term = 1._dp - cost
      termc1 = -costc1
      termc2 = -costc2
      termc3 = -costc3

    tx = rotation_vector%tx
    ty = rotation_vector%ty
    tz = rotation_vector%tz

!   transform_matrix(1,1) = term*tx*tx + cost
!   transform_matrix(1,2) = term*tx*ty - tz*sint
!   transform_matrix(1,3) = term*tx*tz + ty*sint

!   transform_matrix(2,1) = term*ty*tx + tz*sint
!   transform_matrix(2,2) = term*ty*ty + cost
!   transform_matrix(2,3) = term*ty*tz - tx*sint

!   transform_matrix(3,1) = term*tz*tx - ty*sint
!   transform_matrix(3,2) = term*tz*ty + tx*sint
!   transform_matrix(3,3) = term*tz*tz + cost

    select case(key)
    case(1)
      dtrim(1,1) = termc1*tx*tx + costc1
      dtrim(1,2) = termc1*tx*ty - tz*sintc1
      dtrim(1,3) = termc1*tx*tz + ty*sintc1

      dtrim(2,1) = termc1*ty*tx + tz*sintc1
      dtrim(2,2) = termc1*ty*ty + costc1
      dtrim(2,3) = termc1*ty*tz - tx*sintc1

      dtrim(3,1) = termc1*tz*tx - ty*sintc1
      dtrim(3,2) = termc1*tz*ty + tx*sintc1
      dtrim(3,3) = termc1*tz*tz + costc1
    case(2)
      dtrim(1,1) = termc2*tx*tx + costc2
      dtrim(1,2) = termc2*tx*ty - tz*sintc2
      dtrim(1,3) = termc2*tx*tz + ty*sintc2

      dtrim(2,1) = termc2*ty*tx + tz*sintc2
      dtrim(2,2) = termc2*ty*ty + costc2
      dtrim(2,3) = termc2*ty*tz - tx*sintc2

      dtrim(3,1) = termc2*tz*tx - ty*sintc2
      dtrim(3,2) = termc2*tz*ty + tx*sintc2
      dtrim(3,3) = termc2*tz*tz + costc2
    case(3)
      dtrim(1,1) = termc3*tx*tx + costc3
      dtrim(1,2) = termc3*tx*ty - tz*sintc3
      dtrim(1,3) = termc3*tx*tz + ty*sintc3

      dtrim(2,1) = termc3*ty*tx + tz*sintc3
      dtrim(2,2) = termc3*ty*ty + costc3
      dtrim(2,3) = termc3*ty*tz - tx*sintc3

      dtrim(3,1) = termc3*tz*tx - ty*sintc3
      dtrim(3,2) = termc3*tz*ty + tx*sintc3
      dtrim(3,3) = termc3*tz*tz + costc3
    case default
      write(*,*) 'Unknown key provided to dtrim()...'
      call lmpi_die
      stop
    end select

! Linearize the translation part of the matrix

    x0 = rotation_vector%xorigin
    y0 = rotation_vector%yorigin
    z0 = rotation_vector%zorigin

!   ds = translation_vector%ds
!   dx = ds*translation_vector%sx
!   dy = ds*translation_vector%sy
!   dz = ds*translation_vector%sz

!   transform_matrix(1,4) = dx + x0 - (transform_matrix(1,1)*x0 +              &
!                                      transform_matrix(1,2)*y0 +              &
!                                      transform_matrix(1,3)*z0)

!   transform_matrix(2,4) = dy + y0 - (transform_matrix(2,1)*x0 +              &
!                                      transform_matrix(2,2)*y0 +              &
!                                      transform_matrix(2,3)*z0)

!   transform_matrix(3,4) = dz + z0 - (transform_matrix(3,1)*x0 +              &
!                                      transform_matrix(3,2)*y0 +              &
!                                      transform_matrix(3,3)*z0)

    dtrim(1,4) = - (dtrim(1,1)*x0 + dtrim(1,2)*y0 + dtrim(1,3)*z0)
    dtrim(2,4) = - (dtrim(2,1)*x0 + dtrim(2,2)*y0 + dtrim(2,3)*z0)
    dtrim(3,4) = - (dtrim(3,1)*x0 + dtrim(3,2)*y0 + dtrim(3,3)*z0)

!   fill out the bottom row of the matrix

!   transform_matrix(4,1)  = 0._dp
!   transform_matrix(4,2)  = 0._dp
!   transform_matrix(4,3)  = 0._dp
!   transform_matrix(4,4)  = 1._dp

    dtrim(4,1) = my_0
    dtrim(4,2) = my_0
    dtrim(4,3) = my_0
    dtrim(4,4) = my_0

  end function dtrim


!=============================== DERIVS_TO_TECPLOT ===========================80
!
!  Write active variable derivatives to Tecplot files for plotting history
!
!=============================================================================80
  subroutine derivs_to_tecplot(grid,design,istep)

    use grid_types,        only : grid_type
    use design_types,      only : design_type, max_string_length
    use file_utils,        only : available_unit
    use allocations,       only : my_alloc_ptr
    use string_utils,      only : int_to_s
    use info_depr,         only : physical_timestep
    use system_extensions, only : se_flush

    integer, intent(in) :: istep

    type(grid_type),   intent(in) :: grid
    type(design_type), intent(in) :: design

    integer :: counter, ifcn, ibody, i, idv

    integer, save :: alpha_unit, mach_unit, rigid_unit, shape_unit, trim_unit
    integer, save :: kinematic_unit, yaw_unit

    real(dp), dimension(:), pointer, save :: rigid_derivs
    real(dp), dimension(:), pointer, save :: shape_derivs
    real(dp), dimension(:), pointer, save :: trim_derivs
    real(dp), dimension(:), pointer, save :: kinematic_derivs

    logical, save :: init = .false.

    character(len=80)                :: filename, f
    character(len=max_string_length) :: line

  continue

! Plot any alpha derivatives

    if ( design%alpha_active ) then
      if ( .not. init ) then
        alpha_unit = available_unit()
        filename = trim(grid%project) // '_alpha_derivs.dat'
        open(alpha_unit,file=trim(filename),form='formatted')
        write(alpha_unit,*) 'TITLE="Alpha derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ifcn = 1, design%nfunctions
          counter = counter + 1
          line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
        end do
        write(alpha_unit,'(a)') trim(line)
      endif
      counter = design%nfunctions
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(alpha_unit,trim(f)) istep, physical_timestep,                      &
                        (design%alpha_derivative(ifcn),ifcn=1,design%nfunctions)
      call se_flush(alpha_unit)
      if ( physical_timestep == 0 ) close(alpha_unit)
    endif

! Plot any yaw derivatives

    if ( design%yaw_active ) then
      if ( .not. init ) then
        yaw_unit = available_unit()
        filename = trim(grid%project) // '_yaw_derivs.dat'
        open(yaw_unit,file=trim(filename),form='formatted')
        write(yaw_unit,*) 'TITLE="Yaw derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ifcn = 1, design%nfunctions
          counter = counter + 1
          line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
        end do
        write(yaw_unit,'(a)') trim(line)
      endif
      counter = design%nfunctions
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(yaw_unit,trim(f)) istep, physical_timestep,                        &
                          (design%yaw_derivative(ifcn),ifcn=1,design%nfunctions)
      call se_flush(yaw_unit)
      if ( physical_timestep == 0 ) close(yaw_unit)
    endif

! Plot any Mach derivatives

    if ( design%mach_active ) then
      if ( .not. init ) then
        mach_unit = available_unit()
        filename = trim(grid%project) // '_mach_derivs.dat'
        open(mach_unit,file=trim(filename),form='formatted')
        write(mach_unit,*) 'TITLE="Mach derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ifcn = 1, design%nfunctions
          counter = counter + 1
          line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
        end do
        write(mach_unit,'(a)') trim(line)
      endif
      counter = design%nfunctions
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(mach_unit,trim(f)) istep, physical_timestep,                       &
                         (design%mach_derivative(ifcn),ifcn=1,design%nfunctions)
      call se_flush(mach_unit)
      if ( physical_timestep == 0 ) close(mach_unit)
    endif

! Plot any rigid derivatives

    if ( design%rigid_active ) then
      if ( .not. init ) then
        rigid_unit = available_unit()
        filename = trim(grid%project) // '_rigid_derivs.dat'
        open(rigid_unit,file=trim(filename),form='formatted')
        write(rigid_unit,*) 'TITLE="Rigid derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%rigid_data(ibody)%ndv
              if ( design%rigid_data(ibody)%active(idv) ) then
                counter = counter + 1
                line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
              endif
            end do
          end do
        end do
        write(rigid_unit,'(a)') trim(line)
        call my_alloc_ptr(rigid_derivs,counter)
      endif
      rigid_derivs(:) = 0.0_dp
      counter = 0
      do ibody = 1, design%nbodies
        do ifcn = 1, design%nfunctions
          do idv = 1, design%rigid_data(ibody)%ndv
            if ( design%rigid_data(ibody)%active(idv) ) then
              counter = counter + 1
              rigid_derivs(counter) =                                          &
                             design%rigid_data(ibody)%shape_derivative(ifcn,idv)
            endif
          end do
        end do
      end do
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(rigid_unit,trim(f)) istep, physical_timestep,                      &
                                                   (rigid_derivs(i),i=1,counter)
      call se_flush(rigid_unit)
      if ( physical_timestep == 0 ) then
        close(rigid_unit)
        deallocate(rigid_derivs)
      endif
    endif

! Plot any shape derivatives

    if ( design%shape_active ) then
      if ( .not. init ) then
        shape_unit = available_unit()
        filename = trim(grid%project) // '_shape_derivs.dat'
        open(shape_unit,file=trim(filename),form='formatted')
        write(shape_unit,*) 'TITLE="Shape derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%body_data(ibody)%ndv
              if ( design%body_data(ibody)%active(idv) == 1 ) then
                counter = counter + 1
                line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
              endif
            end do
          end do
        end do
        write(shape_unit,'(a)') trim(line)
        call my_alloc_ptr(shape_derivs,counter)
      endif
      shape_derivs(:) = 0.0_dp
      counter = 0
      do ibody = 1, design%nbodies
        do ifcn = 1, design%nfunctions
          do idv = 1, design%body_data(ibody)%ndv
            if ( design%body_data(ibody)%active(idv) == 1 ) then
              counter = counter + 1
              shape_derivs(counter) =                                          &
                              design%body_data(ibody)%shape_derivative(ifcn,idv)
            endif
          end do
        end do
      end do
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(shape_unit,trim(f)) istep, physical_timestep,                      &
                                                   (shape_derivs(i),i=1,counter)
      call se_flush(shape_unit)
      if ( physical_timestep == 0 ) then
        close(shape_unit)
        deallocate(shape_derivs)
      endif
    endif

! Plot any trim derivatives

    if ( design%trimming%active ) then
      if ( .not. init ) then
        trim_unit = available_unit()
        filename = trim(grid%project) // '_trim_derivs.dat'
        open(trim_unit,file=trim(filename),form='formatted')
        write(trim_unit,*) 'TITLE="Trim derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%trimming%body_data(ibody)%ndv
              if ( design%trimming%body_data(ibody)%active(idv) == 1 ) then
                counter = counter + 1
                line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
              endif
            end do
          end do
        end do
        write(trim_unit,'(a)') trim(line)
        call my_alloc_ptr(trim_derivs,counter)
      endif
      trim_derivs(:) = 0.0_dp
      counter = 0
      do ibody = 1, design%nbodies
        do ifcn = 1, design%nfunctions
          do idv = 1, design%trimming%body_data(ibody)%ndv
            if ( design%trimming%body_data(ibody)%active(idv) == 1 ) then
              counter = counter + 1
              trim_derivs(counter) =                                           &
                     design%trimming%body_data(ibody)%shape_derivative(ifcn,idv)
            endif
          end do
        end do
      end do
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(trim_unit,trim(f)) istep, physical_timestep,                       &
                                                    (trim_derivs(i),i=1,counter)
      call se_flush(trim_unit)
      if ( physical_timestep == 0 ) then
        close(trim_unit)
        deallocate(trim_derivs)
      endif
    endif

! Plot any kinematic derivatives

    if ( design%kinematic%active ) then
      if ( .not. init ) then
        kinematic_unit = available_unit()
        filename = trim(grid%project) // '_kinematic_derivs.dat'
        open(kinematic_unit,file=trim(filename),form='formatted')
        write(kinematic_unit,*) 'TITLE="Kinematic derivatives convergence"'
        line = 'VARIABLES="Iter" "Time Step"'
        counter = 0
        do ibody = 1, design%nbodies
          do ifcn = 1, design%nfunctions
            do idv = 1, design%kinematic%body_data(ibody)%ndv
              if ( design%kinematic%body_data(ibody)%active(idv) == 1 ) then
                counter = counter + 1
                line = trim(line) // ' "D' // trim(int_to_s(counter)) // '"'
              endif
            end do
          end do
        end do
        write(kinematic_unit,'(a)') trim(line)
        call my_alloc_ptr(kinematic_derivs,counter)
      endif
      kinematic_derivs(:) = 0.0_dp
      counter = 0
      do ibody = 1, design%nbodies
        do ifcn = 1, design%nfunctions
          do idv = 1, design%kinematic%body_data(ibody)%ndv
            if ( design%kinematic%body_data(ibody)%active(idv) == 1 ) then
              counter = counter + 1
              kinematic_derivs(counter) =                                      &
                    design%kinematic%body_data(ibody)%shape_derivative(ifcn,idv)
            endif
          end do
        end do
      end do
      f = '(i0,1x,i0,1x,' // trim(int_to_s(counter)) // '(e23.15,1x))'
      write(kinematic_unit,trim(f)) istep, physical_timestep,                  &
                                               (kinematic_derivs(i),i=1,counter)
      call se_flush(kinematic_unit)
      if ( physical_timestep == 0 ) then
        close(kinematic_unit)
        deallocate(kinematic_derivs)
      endif
    endif

    init = .true.

  end subroutine derivs_to_tecplot

end module timedep_sensitivities

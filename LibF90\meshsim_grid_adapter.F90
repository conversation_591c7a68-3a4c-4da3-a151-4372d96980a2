module meshsim_grid_adapter

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#ifdef HAVE_MESHSIM
  use kinddefs, only : dp
#endif

  implicit none

  private

  public :: meshsim_start
  public :: meshsim_grid_create
  public :: meshsim_grid_write
  public :: meshsim_grid_free
  public :: meshsim_stop

  public :: meshsimInit
  public :: meshsimParallelAdapt
  public :: meshsimGetGrid
  public :: meshsimReadBc
  public :: meshsimProcessPart
  public :: meshsimFree
  public :: meshsimFreezeBL
  public :: meshsimLoadBalance

#ifdef HAVE_MESHSIM
  integer :: nbound
  integer, dimension(:), allocatable :: ibc

  real(dp) :: timeZero = 0.0_dp
#endif

contains

#ifdef HAVE_MESHSIM
  subroutine meshsimResetTimer()
    use system_extensions, only : se_wall_clock
    continue
    timeZero = se_wall_clock()
  end subroutine meshsimResetTimer

  subroutine meshsimReportTimer( string )
    use system_extensions, only : se_wall_clock, se_flush
    use lmpi,              only : lmpi_master
    character(len=*), intent(in) :: string
    real(dp) :: timeNow
    real :: delta
    continue
    timeNow = se_wall_clock()
    delta = timeNow-timeZero
    if (lmpi_master) then
      write(*,'(a,f15.8,a)') string, delta, ' sec. (wallclock)'
      call se_flush()
    end if
  end subroutine meshsimReportTimer
#endif

#ifdef HAVE_MESHSIM
!============================ meshsim_start ==================================80
!=============================================================================80
  subroutine meshsim_start()

    use refine_adaptation_input, only : adapt_meshsim_license_file
    use lmpi, only : lmpi_master

    interface
      subroutine meshsimstart(licensefile)
        character(len=256), intent(in) :: licensefile
      end subroutine meshsimstart
    end interface

    continue

    if (adapt_meshsim_license_file /= '') then
      call meshsimstart(adapt_meshsim_license_file)
    end if
  end subroutine meshsim_start

!============================ meshsim_grid_create ============================80
!=============================================================================80
  subroutine meshsim_grid_create()

    use refine_adaptation_input, only : adapt_meshsim_model_file,              &
                                        adapt_meshsim_smd_file,                &
                                        adapt_meshsim_mesh_file
    interface
      subroutine meshsim_gridcreate(modelfile, smdfile, meshfile)
        character(len=256), intent(in) :: modelfile, smdfile, meshfile
      end subroutine meshsim_gridcreate
    end interface

    continue

    if (adapt_meshsim_model_file /= '' .and. adapt_meshsim_smd_file /= ''      &
        .and. adapt_meshsim_mesh_file /= '') then
      call meshsim_gridcreate(adapt_meshsim_model_file, adapt_meshsim_smd_file,&
                              adapt_meshsim_mesh_file)
    end if
  end subroutine meshsim_grid_create

!============================ meshsim_grid_write =============================80
!=============================================================================80
  subroutine meshsim_grid_write(mesh_file)

    character(len=256), intent(in) :: mesh_file

    interface
      subroutine meshsim_gridwrite(meshfile)
        character(len=256), intent(in) :: meshfile
      end subroutine meshsim_gridwrite
    end interface

    continue

    call meshsim_gridwrite(mesh_file)

  end subroutine meshsim_grid_write

!============================ meshsim_grid_free ==============================80
!=============================================================================80
  subroutine meshsim_grid_free()

    use refine_adaptation_input, only : adapt_meshsim_model_file,              &
                                        adapt_meshsim_smd_file,                &
                                        adapt_meshsim_mesh_file
    interface
      subroutine meshsim_gridfree()
      end subroutine meshsim_gridfree
    end interface

    continue

    if (adapt_meshsim_model_file /= '' .and. adapt_meshsim_smd_file /= ''      &
        .and. adapt_meshsim_mesh_file /= '') then
      call meshsim_gridfree()
    end if
  end subroutine meshsim_grid_free

!============================ meshsim_stop ===================================80
!=============================================================================80
  subroutine meshsim_stop()

    use lmpi, only : lmpi_master
    use refine_adaptation_input, only : adapt_meshsim_license_file

    interface
      subroutine meshsimstop()
      end subroutine meshsimstop
    end interface

    continue

    if (adapt_meshsim_license_file /= '') then
      call meshsimstop()
    end if
  end subroutine meshsim_stop
#else
  subroutine meshsim_start()
  end subroutine meshsim_start
  subroutine meshsim_grid_create()
  end subroutine meshsim_grid_create
  subroutine meshsim_grid_write(mesh_file)
    character(len=256), intent(in) :: mesh_file
    if (.false.) print *, trim(mesh_file)
  end subroutine meshsim_grid_write
  subroutine meshsim_grid_free()
  end subroutine meshsim_grid_free
  subroutine meshsim_stop()
  end subroutine meshsim_stop
#endif

!========================== meshsimFreezeBL ==================================80
!=============================================================================80

  subroutine meshsimFreezeBL( grid, bl_thick )

    use grid_types,          only : grid_type
    use kinddefs,            only : dp

    type(grid_type), intent(inout) :: grid
    real(dp),        intent(in)    :: bl_thick

#ifdef HAVE_MESHSIM
!   Will probably be of the form below but what is bl_thick? - SST
!   meshsim_gridfreezebl(grid, 1)
    write(*,*) 'Error! meshsimFreezeBL not implemented yet'
#else
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM',  &
      bl_thick, grid%nnodes0
#endif
  end subroutine meshsimFreezeBL

!========================== meshsimInit ======================================80
!=============================================================================80

  subroutine meshsimInit( grid, anisotropic_metric, soln, sadj )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use lmpi,                 only : lmpi_conditional_stop
#ifdef HAVE_MESHSIM
    use design_types,         only : design_run
    use nml_nonlinear_solves, only : itime
    use lmpi,                 only : lmpi_id, lmpi_master
    use grid_helper,          only : create_test_g2l
    use grid_helper,          only : create_part, grid_canonic_cell
    use meshsim_interface,    only : meshsim_compsetmap        &
                                   , meshsim_compsetauxvector  &
                                   , meshsim_compsetauxmatrix  &
                                   , meshsim_compsetauxmatrix3
    use overset,              only : overset_flag
    use solution_types,       only : generic_gas
#endif

    type(grid_type),           intent(inout) :: grid
    real(dp), dimension(6,grid%nnodes01), intent(in) :: anisotropic_metric
    type(soln_type), optional, intent(inout) :: soln
    type(sadj_type), optional, intent(inout) :: sadj

#ifdef HAVE_MESHSIM
    integer :: api_version, expected_version
    integer :: ibound, icell, face
    integer, dimension(:,:), allocatable :: canonic_c2n
    integer, dimension(:,:), allocatable :: f2n
    integer :: naux, offset

    integer :: ielem

    interface
      subroutine meshsim_gridapiversion( api_version )
        integer, intent(out) :: api_version
      end subroutine meshsim_gridapiversion
      subroutine meshsim_gridsetnodelocal2global( partid, nnodeg, nnode,       &
                                                  nnode0, local2global )
        integer, intent(in) :: partid, nnodeg, nnode, nnode0
        integer, dimension(nnode), intent(in) :: local2global
      end subroutine meshsim_gridsetnodelocal2global
      subroutine meshsim_gridsetnaux( naux )
        integer, intent(in) :: naux
      end subroutine meshsim_gridsetnaux
    end interface

    continue

    call create_test_g2l(grid)
    allocate(grid%part(grid%nnodes01))
#if 0
! Parallel
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)
#endif

    call meshsim_gridapiversion( api_version )

    expected_version = 80000000
    wrong_version : if ( expected_version > api_version ) then
      if (lmpi_master)                                                         &
        write(*,'(" meshsim: old interface version (",i9,") expected >= ",i9)')&
        api_version, expected_version
      call lmpi_conditional_stop(1,"meshsim interface version")
    end if wrong_version
    call lmpi_conditional_stop(0,"meshsim interface version")

    call meshsim_gridsetnodelocal2global( lmpi_id, grid%nnodesg, grid%nnodes01,&
                                          grid%nnodes0, grid%l2g )

#if 0
! Parallel
    call meshsim_gridsetnodepart( grid%nnodes01, grid%part )
#endif

! to remeber fun3d specific boundary info in module header
    nbound = grid%nbound
    allocate(ibc(nbound))
    do ibound = 1, grid%nbound
      ibc(ibound) = grid%bc(ibound)%ibc
    end do

! Solution
    naux = 0

    naux_soln : if ( present(soln) ) then
      naux_equ : if ( soln%eqn_set == generic_gas ) then
        naux = soln%n_tot
      else
        naux = soln%ndim
        naux = naux + soln%n_turb
        if (abs(itime) > 0) then
          naux = naux + soln%ndim + soln%n_turb     ! atn
          naux = naux + soln%ndim + soln%n_turb     ! atn1
          if (abs(itime) >=3) then
            naux = naux + soln%ndim + soln%n_turb   ! atn2
          end if
        end if
      end if naux_equ
      if (present(sadj) ) naux = naux + soln%adim
    end if naux_soln

    call meshsim_compsetnaux( naux )

    offset = 0
    have_soln : if ( present(soln) ) then
      soln_eqn_set : if ( soln%eqn_set == generic_gas ) then
        call meshsim_compsetauxmatrix( soln%n_tot, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%n_tot
      else
        call meshsim_compsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%ndim
        if (soln%n_turb>0) then
          call meshsim_compsetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
            soln%turb )
          offset = offset + soln%n_turb
        end if
        if (abs(itime) > 0) then
          call meshsim_compsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call meshsim_compsetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn )
            offset = offset + soln%n_turb
          end if
          call meshsim_compsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn1 )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call meshsim_compsetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn1 )
            offset = offset + soln%n_turb
          end if
          if (abs(itime) >=3) then
            call meshsim_compsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
              soln%qatn2 )
            offset = offset + soln%ndim
            if (soln%n_turb>0) then
              call meshsim_compsetauxmatrix( soln%n_turb, grid%nnodes01, &
                offset, soln%turbatn2)
              offset = offset + soln%n_turb
            end if
          end if
        end if
      end if soln_eqn_set
      have_sadj : if (present(sadj)) then
        call meshsim_compsetauxmatrix3( soln%adim, grid%nnodes01, offset, &
          sadj%rlam )
        offset = offset + soln%adim
      end if have_sadj
    end if have_soln
    if ( naux /= offset ) then
      call lmpi_conditional_stop(1,' naux /= offset')
    end if
    call lmpi_conditional_stop(0,' naux /= offset')

!   Calling _gridsetmap after _gridsetaux* so there's a logical point at which
!   to import field data (else need to write a new function). - SST
    call meshsim_compsetmap( grid%nnodes01, anisotropic_metric )

#else
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM',  &
      grid%nnodes0, anisotropic_metric(1,1)
    call lmpi_conditional_stop(1, 'need HAVE_MESHSIM')
    if (present(soln)) write(*,*) soln%eqn_set
    if (present(sadj)) write(*,*) sadj%rlam(1,1,1)
#endif
  end subroutine meshsimInit

!========================== meshsimParallelAdapt =============================80
!=============================================================================80

  subroutine meshsimParallelAdapt( timer, minLength_arg, maxLength_arg )

    use kinddefs,        only : dp
    use lmpi,        only : lmpi_master

    logical,               intent(in) :: timer
    real(dp), optional,    intent(in) :: minLength_arg, maxLength_arg

#ifdef HAVE_MESHSIM
    real(dp)    :: minLength, maxLength

    interface
      subroutine meshsim_gridparalleladapt( processor, minLength, maxLength )
        integer, intent(in) :: processor
        real(selected_real_kind(15,307)), intent(in) :: minLength, maxLength
      end subroutine meshsim_gridparalleladapt
    end interface

    continue

! Different from Refine - these are MeshSim's defaults
    minLength = 0.7_dp
    maxLength = 1.45_dp
    if ( present(minLength_arg) ) minLength = minLength_arg
    if ( present(maxLength_arg) ) maxLength = maxLength_arg
    if (timer) call meshsimResetTimer()
    call meshsim_gridparalleladapt( -1, minLength, maxLength )
    if (timer) call meshsimReportTimer(" adapt")
    if (timer) call meshsimResetTimer()
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM',                      &
      timer
    if (present(minLength_arg) .and. .false.) write(*,*) minLength_arg
    if (present(maxLength_arg) .and. .false.) write(*,*) maxLength_arg
#endif
    end subroutine meshsimParallelAdapt

!========================== meshsimGetGrid ===================================80
!=============================================================================80

  subroutine meshsimGetGrid( grid, soln, sadj )

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
#ifdef HAVE_MESHSIM
    use solution_types,       only : generic_gas
    use kinddefs,             only : system_i8
    use meshsim_interface,    only : meshsim_compgetnodes      &
                                   , meshsim_compgetauxmatrix  &
                                   , meshsim_compgetauxmatrix3
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_cell_unique
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use nml_nonlinear_solves, only : itime
    use lmpi,                 only : lmpi_id, lmpi_master,                     &
                                     lmpi_reduce, lmpi_bcast,                  &
                                     lmpi_conditional_stop
    use system_extensions,    only : se_flush
    use overset,              only : overset_flag
#else
    use lmpi,                 only : lmpi_master
#endif

    type(grid_type), intent(inout)           :: grid
    type(soln_type), intent(inout), optional :: soln
    type(sadj_type), intent(inout), optional :: sadj

#ifdef HAVE_MESHSIM
    integer :: i, ielem
    integer :: cell
    integer(system_i8) :: nunique_i8, ncellg_i8
    integer :: offset

    interface
      subroutine meshsim_gridSortFUN3D( nnodes0, nnodes01, nnodesg )
        integer, intent(out) :: nnodes0, nnodes01, nnodesg
      end subroutine meshsim_gridSortFUN3D
      subroutine meshsim_gridGetNCell( node_per_cell, ncell )
        integer,               intent(in)  :: node_per_cell
        integer,               intent(out) :: ncell
      end subroutine meshsim_gridGetNCell
      subroutine meshsim_gridGetCell( node_per_cell, ncell, c2n, cl2g )
        integer, intent(in)  :: node_per_cell, ncell
        integer, dimension(node_per_cell,ncell), intent(out) :: c2n
        integer, dimension(ncell), intent(out) :: cl2g
      end subroutine meshsim_gridGetCell
    end interface

    continue

    grid%partid = lmpi_id+1
    grid%project = "meshsim"

    if (lmpi_master) write(*,*) 'meshsimGetGrid motion=', trim(grid%grid_motion)

    call meshsim_gridSortFUN3D( grid%nnodes0, grid%nnodes01, grid%nnodesg )
    if (lmpi_master)                                        &
      write(*,'(" The adapted grid has",i10," nodesg")')    &
      grid%nnodesg
    grid%firstemptynode  = 0
    grid%firstemptynode0 = 0
    if (lmpi_master) write(*,*)"reload nodes..."
    call my_alloc_ptr(grid%l2g,      grid%nnodes01)
    call my_alloc_ptr(grid%x,        grid%nnodes01)
    call my_alloc_ptr(grid%y,        grid%nnodes01)
    call my_alloc_ptr(grid%z,        grid%nnodes01)
    call meshsim_compgetnodes( grid%nnodes01, grid%l2g, grid%x, grid%y, grid%z )

    if (lmpi_master) write(*,*)"filling level01 local part vectors..."
    call create_test_g2l(grid)
    allocate(grid%part(grid%nnodes01))
#if 0
! Solution, parallel
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)
#endif

    have_soln : if (present(soln)) then
      if (lmpi_master) write(*,*)"reload solution..."
      offset = 0
! FIXME? sonl%neq* could be set by solution.set_up_neq
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
      call lmpi_reduce(soln%neq0, soln%dofg)
      call lmpi_bcast(soln%dofg)
      soln_eqn_set : if ( soln%eqn_set == generic_gas ) then
        call my_alloc_ptr( soln%q_dof, soln%n_tot, soln%neq01 )
        call meshsim_compgetauxmatrix( soln%n_tot, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%n_tot
      else
        call my_alloc_ptr( soln%q_dof, soln%ndim, grid%nnodes01 )
        call meshsim_compgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%ndim
        if (soln%n_turb>0) then
          call my_alloc_ptr( soln%turb, soln%n_turb, grid%nnodes01 )
          call meshsim_compgetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
            soln%turb )
          offset = offset + soln%n_turb
        else
          call my_alloc_ptr( soln%turb, 1, 1 )
        end if
        if(abs(itime) > 0) then
          call my_alloc_ptr( soln%qatn, soln%ndim, grid%nnodes01 )
          call meshsim_compgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call my_alloc_ptr( soln%turbatn, soln%n_turb, grid%nnodes01 )
            call meshsim_compgetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn )
            offset = offset + soln%n_turb
          else
            call my_alloc_ptr( soln%turbatn, 1, 1 )
          end if
          call my_alloc_ptr( soln%qatn1, soln%ndim, grid%nnodes01 )
          call meshsim_compgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn1 )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call my_alloc_ptr( soln%turbatn1, soln%n_turb, grid%nnodes01 )
            call meshsim_compgetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn1 )
            offset = offset + soln%n_turb
          else
            call my_alloc_ptr( soln%turbatn1, 1, 1 )
          end if
          if(abs(itime) >= 3) then
            call my_alloc_ptr( soln%qatn2, soln%ndim, grid%nnodes01 )
            call meshsim_compgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
              soln%qatn2 )
            offset = offset + soln%ndim
            if (soln%n_turb>0) then
              call my_alloc_ptr( soln%turbatn2, soln%n_turb, grid%nnodes01 )
              call meshsim_compgetauxmatrix( soln%n_turb, grid%nnodes01, &
                offset, soln%turbatn2 )
              offset = offset + soln%n_turb
            else
              call my_alloc_ptr( soln%turbatn2, 1, 1 )
            end if
          end if
        end if
      end if soln_eqn_set
      if (present(sadj)) then
        call my_alloc_ptr( sadj%rlam, soln%adim, grid%nnodes01, 1 )
        call meshsim_compgetauxmatrix3( soln%adim, grid%nnodes01, offset, &
          sadj%rlam )
      end if
    end if have_soln

    !get cells FIXME MAP WORKING
    ! ncell, ncellg

    get_each_elem_group : do ielem = 1, grid%nelem
      call meshsim_gridGetNCell(grid%elem(ielem)%node_per_cell,                &
                                grid%elem(ielem)%ncell)
      call my_realloc_ptr(grid%elem(ielem)%c2n, grid%elem(ielem)%node_per_cell,&
                          grid%elem(ielem)%ncell)
      call my_realloc_ptr(grid%elem(ielem)%cl2g, grid%elem(ielem)%ncell)
      call meshsim_gridGetCell(grid%elem(ielem)%node_per_cell,                 &
                               grid%elem(ielem)%ncell, grid%elem(ielem)%c2n,   &
                               grid%elem(ielem)%cl2g)

      nunique_i8 = 0
      do cell = 1, grid%elem(ielem)%ncell
        if ( grid_cell_unique(grid, grid%elem(ielem)%c2n(:,cell) ) ) &
          nunique_i8 = nunique_i8 + 1
      end do
      call lmpi_reduce(nunique_i8, ncellg_i8)
      grid%elem(ielem)%ncellg = ncellg_i8
      call lmpi_bcast(grid%elem(ielem)%ncellg)

      if (lmpi_master)                                          &
        write(*,'(" element group ",i0," of ",a," has ",i0)')   &
        ielem, grid%elem(ielem)%type_cell, grid%elem(ielem)%ncellg
    end do get_each_elem_group

    if (lmpi_master) write(*,*)"get boundary faces..."
    get_ibc_from_module_header : do i = 1, grid%nbound
      grid%bc(i)%ibc = ibc(i)
    end do get_ibc_from_module_header

    deallocate(ibc)

    call meshsimReadBc(grid, .true.)
    call meshsimProcessPart(grid, .true., soln, sadj)

    if (lmpi_master) then
      write(*,*)"meshsimGetGrid complete."
      call se_flush
    end if

#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM'
    if (.false.) write(*,*) grid%nnodes0
    if (present(soln) .and. .false.) write(*,*) soln%eqn_set
    if (present(sadj) .and. .false.) write(*,*) sadj%rlam(1,1,1)
#endif

  end subroutine meshsimGetGrid

!========================== meshsimReadBc ====================================80
!=============================================================================80

  subroutine meshsimReadBc(grid, isRealloc)

    use grid_types,          only : grid_type
#ifdef HAVE_MESHSIM
    use lmpi,                only : lmpi_id, lmpi_master,                      &
                                    lmpi_reduce, lmpi_bcast,                   &
                                    lmpi_conditional_stop
    use allocations,         only : my_alloc_ptr, my_realloc_ptr
#else
    use lmpi,                only : lmpi_master
#endif

    type(grid_type), intent(inout)           :: grid
    logical, intent(in) :: isRealloc

#ifdef HAVE_MESHSIM
    integer :: i, face, nunique
    integer, dimension(:,:), allocatable :: f2n

    interface
      subroutine meshsim_gridGetBcSize( ibound, node_per_face, nface )
        integer, intent(in)  :: ibound, node_per_face
        integer, intent(out) :: nface
      end subroutine meshsim_gridGetBcSize
      subroutine meshsim_gridGetBC( ibound, node_per_face, nface, f2n )
        integer, intent(in)  :: ibound, node_per_face, nface
        integer, dimension(node_per_face,nface), intent(out) :: f2n
      end subroutine meshsim_gridGetBC
    end interface

    fill_bound : do i = 1, grid%nbound
!triangles
      call meshsim_gridGetBcSize( i, 3, grid%bc(i)%nbfacet )
      if (isRealloc) then
        call my_realloc_ptr(grid%bc(i)%f2ntb,   max(1,grid%bc(i)%nbfacet),5)
        call my_realloc_ptr(grid%bc(i)%face_bit,max(1,grid%bc(i)%nbfacet))
      else
        call my_alloc_ptr(grid%bc(i)%f2ntb,   max(1,grid%bc(i)%nbfacet),5)
        call my_alloc_ptr(grid%bc(i)%face_bit,max(1,grid%bc(i)%nbfacet))
      end if

      bc_has_tris : if ( grid%bc(i)%nbfacet > 0 ) then
        allocate(f2n(3, grid%bc(i)%nbfacet))
        call meshsim_gridGetBc( i, 3, grid%bc(i)%nbfacet, f2n )
        transpose_tri_index : do face = 1, grid%bc(i)%nbfacet
          grid%bc(i)%f2ntb(face,1) = f2n(1,face)
          grid%bc(i)%f2ntb(face,2) = f2n(2,face)
          grid%bc(i)%f2ntb(face,3) = f2n(3,face)
        end do transpose_tri_index
        deallocate( f2n )
      end if bc_has_tris
      nunique = 0
      do face = 1, grid%bc(i)%nbfacet
        if (grid%bc(i)%f2ntb(face,1) <= grid%nnodes0) then
          nunique = nunique + 1
          grid%bc(i)%face_bit(face) = 1
        else
          grid%bc(i)%face_bit(face) = 0
        end if
      end do
      call lmpi_reduce(nunique,grid%bc(i)%nbfacetg)
      call lmpi_bcast(grid%bc(i)%nbfacetg)

!quads
      call meshsim_gridGetBcSize( i, 4, grid%bc(i)%nbfaceq )
      if (isRealloc) then
        call my_realloc_ptr(grid%bc(i)%f2nqb,    max(1,grid%bc(i)%nbfaceq),6)
        call my_realloc_ptr(grid%bc(i)%face_bitq,max(1,grid%bc(i)%nbfaceq))
      else
        call my_alloc_ptr(grid%bc(i)%f2nqb,    max(1,grid%bc(i)%nbfaceq),6)
        call my_alloc_ptr(grid%bc(i)%face_bitq,max(1,grid%bc(i)%nbfaceq))
      end if

      bc_has_quads : if ( grid%bc(i)%nbfaceq > 0 ) then
        allocate(f2n(4, grid%bc(i)%nbfaceq))
        call meshsim_gridGetBc( i, 4, grid%bc(i)%nbfaceq, f2n )
        transpose_quad_index : do face = 1, grid%bc(i)%nbfaceq
          grid%bc(i)%f2nqb(face,1) = f2n(1,face)
          grid%bc(i)%f2nqb(face,2) = f2n(2,face)
          grid%bc(i)%f2nqb(face,3) = f2n(3,face)
          grid%bc(i)%f2nqb(face,4) = f2n(4,face)
        end do transpose_quad_index
        deallocate( f2n )
      end if bc_has_quads
      nunique = 0
      do face = 1, grid%bc(i)%nbfaceq
        if (grid%bc(i)%f2nqb(face,1) <= grid%nnodes0) then
          nunique = nunique + 1
          grid%bc(i)%face_bitq(face) = 1
        else
          grid%bc(i)%face_bitq(face) = 0
        end if
      end do
      call lmpi_reduce(nunique,grid%bc(i)%nbfaceqg)
      call lmpi_bcast(grid%bc(i)%nbfaceqg)
    end do fill_bound

#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM'
    if (.false.) write(*,*) grid%nnodes0, isRealloc
#endif

  end subroutine meshsimReadBc

!========================== meshsimProcessPart ===============================80
!=============================================================================80

  subroutine meshsimProcessPart(grid, isRealloc, soln, sadj)

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
#ifdef HAVE_MESHSIM
    use info_depr,            only : ebv_tets
    use nml_nonlinear_solves, only : itime
    use grid_motion_helpers,  only : need_grid_velocity
    use nml_global,           only : moving_grid
    use distance_function,    only : compute_distance_function
    use parallel_embed,       only : embed_renumber_faces,                     &
                                     embed_find_boundary_cells,                &
                                     embed_add_adjoint_faces,                  &
                                     embed_test_boundary_cells,                &
                                     embed_make_edgeloc, embed_make_edge,      &
                                     embed_make_edgel2g, embed_share_edgel2g
    use grid_metrics,         only : compute_dual_metrics
    use bc_names,             only : need_distance_function
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_reset_lmpi_xfer,                     &
                                     n2c_type, n2c_from_grid, deallocate_n2c
    use lmpi,                 only : lmpi_id, lmpi_master,                     &
                                     lmpi_reduce, lmpi_bcast,                  &
                                     lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer
    use system_extensions,    only : se_flush
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
#else
    use lmpi,                 only : lmpi_master
#endif

    type(grid_type), intent(inout)           :: grid
    logical, intent(in) :: isRealloc
    type(soln_type), intent(inout), optional :: soln
    type(sadj_type), intent(inout), optional :: sadj

#ifdef HAVE_MESHSIM
    integer :: i, ielem
    type(n2c_type) :: n2c

    if (lmpi_master) write(*,*) "resize tight..."
    call my_realloc_ptr(grid%l2g,grid%nnodes01)
    call my_realloc_ptr(grid%x,grid%nnodes01)
    call my_realloc_ptr(grid%y,grid%nnodes01)
    call my_realloc_ptr(grid%z,grid%nnodes01)
    if (isRealloc) then
      deallocate(grid%sortedglobal)
      deallocate(grid%sortedlocal)
    end if
    call create_test_g2l(grid)
    do ielem = 1, grid%nelem
      call my_realloc_ptr(grid%elem(ielem)%c2n,&
        grid%elem(ielem)%node_per_cell,grid%elem(ielem)%ncell)
    end do

    if (lmpi_master) write(*,*)"set up node lmpi_xfer send and rec..."
    call grid_reset_lmpi_xfer (grid)

    if (lmpi_master) write(*,*)"construct node 2 cell (n2c) structure..."
    call n2c_from_grid(grid, n2c)

    if (lmpi_master) write(*,*)"find boundary cells..."
    call embed_find_boundary_cells (grid, n2c)
! Parallel
!   if (lmpi_master) write(*,*)"find phantom adjoint boundary faces..."
!   call embed_add_adjoint_faces(grid, n2c)
    if (lmpi_master) write(*,*)"renumber boundary nodes..."
    call embed_renumber_faces(grid)
    if (lmpi_master) write(*,*)"test boundary cells..."
    call embed_test_boundary_cells(grid)

    if (lmpi_master) write(*,*)"creating loc edges..."
    call embed_make_edgeloc (grid, n2c)   ! grid%nedgeloc computed here
    if (lmpi_master) write(*,*)"creating remaining edges..."
    call embed_make_edge (grid, n2c)      ! grid%nedge computed here

    if (lmpi_master) write(*,*)"freeing node 2 cell (n2c) array..."
    call deallocate_n2c(n2c)

    if (lmpi_master) &
      write(*,*)"creating unique edge local to global numbering..."
    call my_alloc_ptr(grid%el2g,grid%nedge)
    call embed_make_edgel2g(2, grid%nedge, grid%eptr, &
      grid%nnodes0, grid%nnodes01, grid%l2g,          &
      grid%nedgeg, grid%el2g)

    if (lmpi_master) write(*,*)"share ghost edge local to global numbering..."
    call embed_share_edgel2g(2, grid%nedgeloc, grid%eptr, grid%el2g, &
      grid, .true.)

    if (lmpi_master) write(*,*)"deallocate part and g2l helper arrays..."
    deallocate(grid%part)
    deallocate(grid%sortedglobal)
    deallocate(grid%sortedlocal)

    if (lmpi_master) write(*,*)"populate ghost xyzs..."
    call lmpi_xfer(grid%x)
    call lmpi_xfer(grid%y)
    call lmpi_xfer(grid%z)

#if 0
! Solution
    resize_soln : if ( present(soln) ) then
      if (lmpi_master) write(*,*)"populate ghost solution..."
      resize_soln_equ_set : if ( soln%eqn_set== generic_gas ) then
        call my_realloc_ptr( soln%q_dof, soln%n_tot, grid%nnodes01 )
        call lmpi_xfer( soln%q_dof )
      else
        call my_realloc_ptr( soln%q_dof, soln%ndim, grid%nnodes01 )
        call lmpi_xfer( soln%q_dof )
        if (soln%n_turb>0) then
          call my_realloc_ptr( soln%turb, soln%n_turb, grid%nnodes01 )
          call lmpi_xfer( soln%turb )
        end if
        if (abs(itime) > 0) then
          call my_realloc_ptr( soln%qatn, soln%ndim, grid%nnodes01 )
          call lmpi_xfer( soln%qatn)
          if (soln%n_turb>0) then
            call my_realloc_ptr( soln%turbatn, soln%n_turb, grid%nnodes01 )
            call lmpi_xfer( soln%turbatn )
          end if
          call my_realloc_ptr( soln%qatn1, soln%ndim, grid%nnodes01 )
          call lmpi_xfer( soln%qatn1)
          if (soln%n_turb>0) then
            call my_realloc_ptr( soln%turbatn1, soln%n_turb, grid%nnodes01 )
            call lmpi_xfer( soln%turbatn1 )
          end if
          if (abs(itime) >= 3) then
            call my_realloc_ptr( soln%qatn2, soln%ndim, grid%nnodes01 )
            call lmpi_xfer( soln%qatn2)
            if (soln%n_turb>0) then
              call my_realloc_ptr( soln%turbatn2, soln%n_turb, grid%nnodes01 )
              call lmpi_xfer( soln%turbatn2 )
            end if
          end if
        end if
      end if resize_soln_equ_set
      resize_sadj : if (present(sadj)) then
        call my_realloc_ptr( sadj%rlam, soln%adim, grid%nnodes01, 1 )
        call lmpi_xfer( sadj%rlam(:,:,1) )
      end if resize_sadj

      if (lmpi_master) write(*,*)"size soln neq scalars..."
      ! FIXME? sonl%neq* could be set by solution.set_up_neq
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
      call lmpi_reduce(soln%neq0, soln%dofg)
      call lmpi_bcast(soln%dofg)

    end if resize_soln
#endif

    if (lmpi_master) write(*,*)"allocate grid reals..."
    call my_alloc_ptr(grid%vol, grid%nnodes01)

    if (need_grid_velocity) then
      call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
      call my_alloc_ptr(grid%dydt,      grid%nnodes01)
      call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
      call my_alloc_ptr(grid%facespeed, grid%nedge)
    else
      call my_alloc_ptr(grid%dxdt,      1)
      call my_alloc_ptr(grid%dydt,      1)
      call my_alloc_ptr(grid%dzdt,      1)
      call my_alloc_ptr(grid%facespeed, 1)
    end if
    call my_alloc_ptr(grid%res_gcl,   1, 1)
    call my_alloc_ptr(grid%xn, grid%nedge)
    call my_alloc_ptr(grid%yn, grid%nedge)
    call my_alloc_ptr(grid%zn, grid%nedge)
    call my_alloc_ptr(grid%ra, grid%nedge)

    if ( ebv_tets ) then
      call my_alloc_ptr(grid%weight, 10, grid%nedge)
    else
      call my_alloc_ptr(grid%weight, 1, 1)
    endif

    do i = 1, grid%nbound
      call my_alloc_ptr(grid%bc(i)%bxn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%byn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%bzn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%slen_wall, grid%bc(i)%nbnode)
      if (need_grid_velocity) then
        call my_alloc_ptr( grid%bc(i)%bdxdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdydt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, max0(grid%bc(i)%nbnode,1) )
      else
        call my_alloc_ptr( grid%bc(i)%bdxdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdydt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, 1 )
      end if

    end do

! call PHYSICS_DEPS/grid_motion.f90:move_grid to get node/boundary speeds

    back_plane_volume : if ( trim(grid%grid_motion) /= 'static' .and. &
                             abs(itime) > 0 ) then
        call my_alloc_ptr( grid%xat0, grid%nnodes01 )
        call my_alloc_ptr( grid%yat0, grid%nnodes01 )
        call my_alloc_ptr( grid%zat0, grid%nnodes01 )
        grid%xat0 = grid%x
        grid%yat0 = grid%y
        grid%zat0 = grid%z

        call my_alloc_ptr( grid%xatn, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn3, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn3, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn3, grid%nnodes01 )

        call my_alloc_ptr(grid%volatn, grid%nnodes01)
        call my_alloc_ptr(grid%volatn1, grid%nnodes01)
        call my_alloc_ptr(grid%volatn2, grid%nnodes01)
        call my_alloc_ptr(grid%volatn3, grid%nnodes01)

    end if back_plane_volume

    if (lmpi_master) write(*,*)"make inviscid metrics..."
    call compute_dual_metrics(grid, moving_grid)

    call my_alloc_ptr(grid%slen,grid%nnodes01)
    call my_alloc_ptr(grid%iflagslen,grid%nnodes01)
    call my_alloc_ptr(grid%des_slen,grid%nnodes01)

    compute_dist_fcn : if (need_distance_function(grid%bc)) then
      if (lmpi_master) write(*,*)"computing distance function..."
      grid%idistfcn = 1
      call compute_distance_function(grid,.true.)
      call lmpi_xfer(grid%slen)
      call lmpi_xfer(grid%iflagslen)
    else
      if (lmpi_master) write(*,*)"skip distance function..."
      grid%idistfcn = 0
    end if compute_dist_fcn

    if (lmpi_master) then
      write(*,*)"distance function complete."
      call se_flush
    endif

    deallocate(grid%vol)
    deallocate(grid%dxdt)
    deallocate(grid%dydt)
    deallocate(grid%dzdt)
    deallocate(grid%facespeed)
    deallocate(grid%res_gcl)
    deallocate(grid%xn)
    deallocate(grid%yn)
    deallocate(grid%zn)
    deallocate(grid%ra)
    do i = 1, grid%nbound
      deallocate(grid%bc(i)%bxn)
      deallocate(grid%bc(i)%byn)
      deallocate(grid%bc(i)%bzn)
      deallocate(grid%bc(i)%slen_wall)
      deallocate(grid%bc(i)%bdxdt)
      deallocate(grid%bc(i)%bdydt)
      deallocate(grid%bc(i)%bdzdt)
      deallocate(grid%bc(i)%bfacespeed)
    end do
    deallocate(grid%slen)
    deallocate(grid%iflagslen)
    deallocate(grid%des_slen)

#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM'
    if (.false.) write(*,*) grid%nnodes0, isRealloc
    if ( present(soln) .and. .false. ) write(*,*) soln%eqn_set
    if ( present(sadj) .and. .false. ) write(*,*) sadj%rlam(1,1,1)
#endif

  end subroutine meshsimProcessPart

!========================== meshsimFree ======================================80
!=============================================================================80

  subroutine meshsimFree( )
#ifdef HAVE_MESHSIM
    interface
      subroutine meshsim_gridfree()
      end subroutine meshsim_gridfree
    end interface

    continue

    call meshsim_gridfree()
#else
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM'
#endif
  end subroutine meshsimFree

!========================== meshsimLoadBalance ===============================80
!=============================================================================80

  subroutine meshsimLoadBalance( grid, timer )
    use grid_types,          only : grid_type
    use lmpi,                only : lmpi_master

    type(grid_type), intent(inout)           :: grid
    logical,               intent(in) :: timer

#ifdef HAVE_MESHSIM
    continue

!   Load balance is on by default in MeshSim. If it needs to be controlled from
!   here, it should be disabled in meshsimInit and enabled here by implementing
!   this function.
!   call meshsim_gridloadbalance()
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_MESHSIM'
    if (.false.) write(*,*) grid%nnodes0
    if (.false.) write(*,*) timer
#endif

  end subroutine meshsimLoadBalance

end module meshsim_grid_adapter

AC_DEFUN([AX_RESOURCE_LIMIT],
[AC_CACHE_CHECK([for the ability to modify resource limits],
 ax_cv_resource_limits,
 [AC_LANG_PUSH(C)
  AC_COMPILE_IFELSE(
  [AC_LANG_SOURCE([[
#include <sys/resource.h>
#include <stdlib.h>
int main( void )
{
  struct rlimit rlim;
  getrlimit(RLIMIT_STACK, &rlim);
  rlim.rlim_cur = rlim.rlim_max;
  setrlimit(RLIMIT_STACK, &rlim);
  getrlimit(RLIMIT_DATA, &rlim);
  rlim.rlim_cur = rlim.rlim_max;
  setrlimit(RLIMIT_DATA, &rlim);
  return 0;
}
  ]])],
  [ax_cv_resource_limits=yes],
  [ax_cv_resource_limits=no] 
   )
  AC_LANG_POP(C)
 ])
if test "$ax_cv_resource_limits" != 'no'
then
 AC_DEFINE([HAVE_GETRLIMIT],[1],[Resource limit system getrlimit available.])
 AC_DEFINE([HAVE_SETRLIMIT],[1],[Resource limit system getrlimit available.])
fi
])


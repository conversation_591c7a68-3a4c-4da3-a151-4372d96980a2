module survey_matrix

  use kinddefs,          only : dp, odp
  use info_depr,         only : skeleton
  use grid_types,        only : grid_type
  use solution_types,    only : soln_type
  use comprow_types,     only : crow_flow

  use lmpi,              only : lmpi_master, lmpi_id, lmpi_bcast, lmpi_reduce, &
                                lmpi_die, lmpi_synchronize, lmpi_max_and_maxid,&
                                lmpi_nproc
  use lmpi_app,          only : lmpi_collect_res
  use system_extensions, only : se_flush
  use array_check,       only : csize
  use allocations,       only : my_alloc_ptr
  use twod_util,         only : q_2d
  use debug_defs,        only : inspect_mean_jacobian, inspect_turb_jacobian,  &
                                inspect_mean_equation, inspect_turb_equation
  use multigrid_defs,    only : ideal_relaxation
  use multiscale,        only : ngrid_actual

  implicit none

  private

  public :: survey_jacobians

  logical, parameter :: scale_entries = .false.
  integer :: u1 = 61, u2 = 62, u3 = 63

  logical, save :: first_time_through = .true.

  contains

  subroutine survey_jacobians( grid, soln, crow, fl )

    use info_depr,         only : ngrid

    type(grid_type), dimension(ngrid), intent(in) :: grid
    type(soln_type), dimension(ngrid), intent(in) :: soln
    type(crow_flow), dimension(ngrid), intent(in) :: crow

    integer, intent(in) :: fl

    integer :: level

  continue

    if ( ideal_relaxation ) then
      write(*,*) 'Skipping survey_jacobians...some Jacobians are bypassed.'
      return
    endif

    if ( lmpi_nproc /= 1 ) then
      write(*,*) 'Skipping survey_jacobians...single processor only.'
      return
    endif

    do level=fl,ngrid_actual
      call examine_jacobian_row(grid(level), soln(level), crow(level))
      if ( inspect_mean_jacobian ) then
        call matrix_survey( grid(level), soln(level), crow(level), 0 )
      elseif ( inspect_turb_jacobian ) then
        call matrix_survey( grid(level), soln(level), crow(level), 1 )
      endif
    enddo

  end subroutine survey_jacobians

!============================== EXAMINE_JACOBIAN_ROW =========================80
!
!  Dump Jacobians for one equation to file for visual inspection
!  ...use sparingly.
!  ...only single processor.
!
!=============================================================================80

  subroutine examine_jacobian_row(grid, soln, crow)


    use lmpi,                 only : lmpi_die

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln
    type(crow_flow), intent(in) :: crow

    integer :: i, j, k, colg, colm, entry, ns
    integer :: njac, dof, dof_matrix, n_mf, check

    real(dp) :: sfac

    logical :: inspecting = .false.

  continue

    inspecting = .false.
    if ( inspect_mean_jacobian .or.                &
         inspect_turb_jacobian ) inspecting = .true.

    if ( .not.inspecting ) return

    if ( lmpi_nproc > 1 ) return

    n_mf   = soln%n_q - soln%n_turb

    if ( inspect_mean_jacobian ) then
      open(u1,file='jacobians.meanflow',  &
           form='formatted',position='append')
      if ( first_time_through ) rewind u1
      write(u1,"(1x,a,i5)") 'Meanflow Jacobians at every dof &
      &for block equation=',inspect_mean_equation
      write(u1,"(1x,a,i5)") '...grid%origin=',grid%origin
    endif

    if ( inspect_turb_jacobian ) then
      open(u2,file='jacobians.turbulent',  &
           form='formatted',position='append')
      if ( first_time_through ) rewind u2
      write(u2,"(1x,a,i5)") 'Turbulent Jacobians at every dof &
      &for block equation=',inspect_turb_equation
      write(u2,"(1x,a,i5)") '...grid%origin=',grid%origin
    endif

    open(u3,file='jacobians.connectivity',  &
         form='formatted',position='append')
    if ( first_time_through ) rewind u3
    write(u3,"(1x,a)") 'Jacobian (LHS) connectivities at every dof.'
    write(u3,"(1x,a,L1)") '..........q_2d=',q_2d
    write(u3,"(1x,a,i5)") '...grid%origin=',grid%origin

    first_time_through = .false.

    do dof = 1,soln%dof0

      dof_matrix = crow%g2m(dof)

      entry = 0
      ns    = 0
      write(u3,*)
      write(u3,"(1x,a,a,2(5x,a),3(18x,a),16x,a)")       &
      'entry',' ns','dof:g','dof:m','xq','yq','zq','volq'
      write(u3,"(1x,i5,3x,2i10,4f20.10)") entry, dof, dof_matrix, &
      grid%xq(dof), grid%yq(dof), grid%zq(dof), grid%volq(dof)

      check = crow%m2g( dof_matrix ) - dof
      if ( check > 0 ) then
        write(*,*) 'Fatal error...stopping in examine_jacobian_row.'
        call lmpi_die
      endif

      do k = crow%ia(dof), crow%ia(dof+1)-1
        entry  = entry + 1
        if ( k >= crow%ia_ns(dof) ) ns = ns + 1
        colg = crow%ja(k)
        colm = crow%g2m( colg )
        write(u3,"(1x,i5,i3,2i10,4f20.10)") entry, ns, colg, colm, &
        grid%xq(colg), grid%yq(colg), grid%zq(colg), grid%volq(colg)
      enddo

    end do

!   Loop over grid-ordered equations, invoking compressed
!   row storage matrix-ordered entries.

    loop_dofs : do dof = 1, soln%dof0

      dof_matrix = crow%g2m( dof )

      if ( inspect_mean_jacobian ) then

        njac = n_mf

        i = inspect_mean_equation
        sfac = abs( soln%a_diag(i,i,dof_matrix) )
        do j=1,njac
          sfac = max( sfac, abs( soln%a_diag(i,j,dof_matrix) ) )
        enddo
        if ( .not. scale_entries ) sfac = 1.0_dp
        write(u1,*)
        write(u1,'(1x,a,i10,a,10(3x,e13.5))') &
        'dof=',dof,' scale=',sfac
        write(u1,'(1x,a,i10,a,10(3x,e13.5))')            &
        'dof=',dof,' a_diag=',                           &
        (real(soln%a_diag(i,j,dof_matrix)/sfac),j=1,njac)
        do k = crow%iam(dof_matrix), crow%iam(dof_matrix+1)-1
          colm = crow%jam(k)
          colg = crow%m2g(colm)
          write(u1,'(1x,a,i10,a,10x,10(f16.6))')     &
                    'dof=',colg,'  a_off=',          &
                    (soln%a_off(i,j,k)/sfac,j=1,njac)
        enddo
      endif
      if ( inspect_turb_jacobian ) then

        njac = soln%n_turb

        i = inspect_turb_equation
        sfac = abs( soln%a_turb_diag(i,i,dof_matrix) )
        do j=1,njac
          sfac = max( sfac, abs( soln%a_turb_diag(i,j,dof_matrix) ) )
        enddo
        if ( .not. scale_entries ) sfac = 1.0_dp
        write(u2,*)
        write(u2,'(1x,a,i10,a,10(3x,e20.10))') &
        'dof=',dof,'  scale=',sfac
        write(u2,'(1x,a,i10,a,10(3x,e20.10))')         &
        'dof=',dof,' a_diag=',                         &
        (real(soln%a_turb_diag(i,j,dof_matrix)/sfac),j=1,njac)
        do k = crow%iam(dof_matrix), crow%iam(dof_matrix+1)-1
          colm = crow%jam(k)
          colg = crow%m2g(colm)
          write(u2,'(1x,a,i10,a,10x,10(f20.10))')         &
                    'dof=',colg,'  a_off=',               &
                    (soln%a_turb_off(i,j,k)/sfac,j=1,njac)
        enddo

      endif

    end do loop_dofs

    if ( inspect_mean_jacobian ) close(u1)
    if ( inspect_turb_jacobian ) close(u2)
    close(u3)

  end subroutine examine_jacobian_row

!=============================== MATRIX_SURVEY ===============================80
!
! Driver routine to check positivity.
! ...very limited in generality.
! ...assumes a scalar equation.
!
!=============================================================================80

  subroutine matrix_survey(grid, soln, crow, matrix_type)

    use fun3d_constants, only : conv

    type(grid_type),  intent(in) :: grid
    type(soln_type),  intent(in) :: soln
    type(crow_flow),  intent(in) :: crow

    integer, intent(in)    :: matrix_type

    integer :: i, nb, eq, print_flag, npts, n_res
    integer :: imx(1)
    integer :: proc_with_max
    integer :: global_bandwidth_max
    integer :: global_bandwidth_avg
    integer, dimension(1) :: bandwidth_max
    integer, dimension(1) :: bandwidth_avg

    integer :: dim_q, dim_a_diag, dim_a_off, dof
    integer :: dim_ia, dim_ja, n_mf

    logical :: allocate_q
    logical :: allocate_a_diag
    logical :: allocate_a_off
    logical :: allocate_res

    real(dp), dimension(1) :: pmaxsum, pmax, xmax, ymax, zmax

    real(dp) :: r_dum, t_dum, x_dum, y_dum, z_dum
    real(dp) :: l2norm_factor, fact1, fact2, sum_bw
    real(dp) :: gxmax, gymax, gzmax, gpmax
    real(dp) :: rxmax, rymax, rzmax, grmax

    integer, parameter :: n_profile = 21

    real(dp) :: diagonal_max
    real(dp) :: diagonal_min, xavg, yavg, zavg, d, dmin, ravg, tavg

    real(dp), dimension(1) :: diag_max
    real(dp), dimension(1) :: diag_min

    real(dp), dimension(n_profile,1) :: p_profile
    real(dp), dimension(n_profile,1) :: m_profile
    real(dp), dimension(n_profile,1) :: x_profile
    real(dp), dimension(n_profile,1) :: y_profile
    real(dp), dimension(n_profile,1) :: z_profile

    real(dp),  dimension(:,:),   pointer  :: q
    real(dp),  dimension(:,:),   pointer  :: res
    real(dp),  dimension(:,:,:), pointer  :: a_diag
    real(odp), dimension(:,:,:), pointer  :: a_off

    logical :: inspecting = .false.
    logical :: polar_coordinate_avg = .false.

  continue

    inspecting = .false.
    if ( inspect_mean_jacobian .or.                &
         inspect_turb_jacobian ) inspecting = .true.

    if ( .not.inspecting ) return

    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' On master processor:'
      write(6,*) ' grid%origin=',grid%origin
      write(6,*) ' Number of element types=',grid%nelem
      write(6,*)
      do i = 1, grid%nelem
        write(6,*) ' ...Element number=',i
        write(6,*) '             ncell=',grid%elem(i)%ncell
        write(6,*) '         type_cell=',grid%elem(i)%type_cell
      end do
    endif

    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Over all processors:'

!     Set some variables here.

      n_mf   = soln%n_q - soln%n_turb

      write(6,*)
      write(6,*) ' Number of degrees of freedom=',soln%dofg

      write(6,*)
      if ( matrix_type == 0 ) then
        write(6,*) ' Surveying meanflow LHS matrix (of crow_flow type).'
        nb  = size(soln%a_diag,1)
        eq  = inspect_mean_equation
      else
        write(6,*) ' Surveying turbulent LHS matrix (of crow_flow type).'
        nb  = size(soln%a_turb_diag,1)
        eq  = inspect_turb_equation
      endif
      write(6,*) ' .......eq=',eq
      write(6,*) ' .......nb=',nb
      write(6,*) ' ......n_q=',soln%n_q
      write(6,*) ' .....n_mf=',n_mf
      write(6,*) ' ...n_turb=',soln%n_turb
      write(6,*)
    endif

    if ( skeleton > 0 ) write(6,*) ' Checking array sizes in matrix_survey.'

!   Check size : x/y/z/vol

    dim_q = soln%neq01

    call csize( dim_q, size(grid%volq), 'grid%volq')
    call csize( dim_q, size(grid%xq),   'grid%xq')
    call csize( dim_q, size(grid%yq),   'grid%yq')
    call csize( dim_q, size(grid%zq),   'grid%zq')

!   Check size : ia/ja

    dim_ia = soln%neq0+1
    dim_ja = crow%nnz01
    call csize( dim_ia, size(crow%ia), 'ia')
    call csize( dim_ja, size(crow%ja), 'ja')

!   Preliminary reshuffling...from soln% to local

    allocate_q      = .false.
    allocate_res    = .false.
    allocate_a_diag = .false.
    allocate_a_off  = .false.

    dim_a_diag = size(soln%a_diag,3)
    dim_a_off  = size(soln%a_off,3)

    if ( matrix_type == 0 ) then

!     Meanflow equations.

      q       => soln%q_dof(:,:)
      a_diag  => soln%a_diag(:,:,:)
      res     => soln%res(:,:)

!     Allocate temporary storage

      allocate_a_diag = .true.
      call my_alloc_ptr(a_off, soln%njac, soln%njac, dim_a_off)
      a_off(:,:,:) = soln%a_off(:,:,:)

    elseif ( matrix_type == 1 ) then

!     Turbulent equations.

!     Check sizes

      q       => soln%turb(:,:)
      a_diag  => soln%a_turb_diag(:,:,:)
      a_off   => soln%a_turb_off(:,:,:)
      res     => soln%turbres(:,:)

    else

      if ( lmpi_master) write(6,*) ' Not available in matrix_survey.'
      call lmpi_die

    endif

    if ( skeleton > 0 ) write(6,*) ' Completed check of array sizes.'

!   Set a global printing flag here

    call lmpi_reduce(skeleton,print_flag)
    call lmpi_bcast(print_flag)

    !...check for positivity of coefficient matrix
    call survey_p( dim_q, dim_a_diag, dim_a_off, dim_ia,                   &
                   bandwidth_max, bandwidth_avg,                           &
                   diag_max, diag_min,                                     &
                   pmaxsum, pmax, xmax, ymax, zmax,                        &
                   n_profile, p_profile, m_profile,                        &
                   x_profile, y_profile, z_profile,                        &
                   soln%dof0,                                              &
                   grid%xq, grid%yq, grid%zq, grid%volq,                   &
                   a_diag,         a_off,                                  &
                   crow%iam, crow%g2m, nb, eq )

    r_dum = 0._dp
    t_dum = 0._dp
    x_dum = 0._dp
    y_dum = 0._dp
    z_dum = 0._dp

!   Diagonal max/min

    diagonal_max  = diag_max(1)
    call lmpi_max_and_maxid(real(diagonal_max,dp), proc_with_max)
    call lmpi_bcast(diagonal_max,proc_with_max)

    diagonal_min = -diag_min(1)
    call lmpi_max_and_maxid(real(diagonal_min,dp), proc_with_max)
    call lmpi_bcast(diagonal_min,proc_with_max)
    diagonal_min  = -diagonal_min

!   Bandwidth (block) max/avg

    call lmpi_max_and_maxid(real(bandwidth_max(1),dp), proc_with_max)
    call lmpi_bcast(bandwidth_max(1),proc_with_max)
    global_bandwidth_max = bandwidth_max(1)

    sum_bw = real( bandwidth_avg(1) , dp )
    call lmpi_collect_res( r_dum, t_dum, sum_bw, &
                           x_dum, y_dum, z_dum )
    call lmpi_bcast(sum_bw)
    global_bandwidth_avg = nint( real(    sum_bw , dp ) &
                               / real( soln%dofg , dp ) )

!   Positivity statistics

    call lmpi_collect_res( r_dum,     t_dum,   pmax(1), &
                           xmax(1), ymax(1), zmax(1) )

    call lmpi_collect_res( r_dum, t_dum, pmaxsum(1), &
                           x_dum, y_dum, z_dum )

    do i=1,n_profile
      call lmpi_collect_res(                               &
           p_profile(i,1), t_dum,          m_profile(i,1), &
           x_profile(i,1), y_profile(i,1), z_profile(i,1) )
    enddo

    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Positivity statistics for equation',eq
      write(6,*)
      write(6,*) ' Average bandwidth (block)=',global_bandwidth_avg
      write(6,*) ' Maximum bandwidth (block)=',global_bandwidth_max
      write(6,*)
      write(6,*) ' Maximum diagonal term=',diagonal_max
      write(6,*) ' Minimum diagonal term=',diagonal_min
      if ( diagonal_min < 0.0_dp ) then
        write(6,*) ' WARNING - NEGATIVE DIAGONAL ELEMENT'
      endif
      write(6,*)
      write(6,*) ' Maximum sum non-positive off-diagonals to diagonal=', &
                                                            pmaxsum(1)
      write(6,*) '      Maximum non-positive off-diagonal to diagonal=', &
                                                           pmax(1)
      write(6,"(1x,a,3f20.10)") ' ....at x,y,z=',xmax(1),ymax(1),zmax(1)
      write(6,*) ' '

      write(6,*) ' Variation of non-positive off-diagonal terms.'
      write(6,*) ' Values ratioed to diagonal....total number of dof=', &
                                                           soln%dofg
      write(6,*)
      l2norm_factor = real(soln%dofg,dp)
      write(6,"(5x,'bin',4x,'np:min',3x,'<np:max',3x,'percent',&
               &5x,'dofs',5x,'value',12x,'x',12x,'y',12x,'z')")
      profile_loop : do i=n_profile,1,-1
        fact1 = real(2*(i-1),dp)/real(n_profile-1,dp)
        fact2 = real(2*(  i),dp)/real(n_profile-1,dp)
        !...avoid complexification issues with this construct
        npts  = nint( real( p_profile(i,1),dp ) )
        if ( i == n_profile ) then
          fact2 = fact1
          write(6,"(i8,f10.2,2x,'infinity',f10.4,i10)") i,fact1,       &
               100._dp*p_profile(i,1)/l2norm_factor,npts
        elseif ( npts > 0 ) then
          write(6,"(i8,2f10.2,f10.4,i10,f10.4,3e13.5)") i,fact1,fact2,  &
               100._dp*p_profile(i,1)/l2norm_factor,npts,               &
               m_profile(i,1),x_profile(i,1),y_profile(i,1),z_profile(i,1)
        else
          write(6,"(i8,2f10.2,f10.4,i10)") i,fact1,fact2,&
               100._dp*p_profile(i,1)/l2norm_factor,npts
        endif
      enddo profile_loop
    endif

    if ( skeleton > 0 ) then
      write(6,*)
      write(6,*) ' Completed histogram of positivity.'
    endif

!   Find the location of the worst positivity

    gpmax = pmax(1)
    gxmax = xmax(1)
    gymax = ymax(1)
    gzmax = zmax(1)

    call lmpi_bcast(gxmax)
    call lmpi_bcast(gymax)
    call lmpi_bcast(gzmax)

    call se_flush(6)
    call lmpi_synchronize

    !...print some information at  gxmax, gymax, gzmax
    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Elements of linearization for worst positivity location.'
      write(6,*)
    endif

    call survey_c( dim_q, dim_a_diag, dim_a_off, dim_ia, dim_ja,        &
                   gxmax,          gymax,             gzmax,            &
                   soln%dof0,      grid%volq,                           &
                   q,              res,                                 &
                   grid%xq,        grid%yq,           grid%zq,          &
                   a_diag,         a_off,                               &
                   crow%iam,       crow%jam, crow%g2m, crow%m2g, nb, eq )

    call se_flush(6)
    call lmpi_synchronize

    if ( skeleton > 0 ) then
      write(6,*)
      write(6,*) ' Completed inspection of matrix at worst positivity.'
    endif

    grmax = -1.0_dp

    r_dum = 0.0_dp
    t_dum = 0.0_dp

    !...find location of maximum residuals
    n_res = soln%dof0
    imx  = maxloc( abs( res(eq,1:n_res) ) )
    gpmax = abs(  res(eq,imx(1)) )
    gxmax =      grid%xq(imx(1))
    gymax =      grid%yq(imx(1))
    gzmax =      grid%zq(imx(1))

    call lmpi_collect_res( r_dum, t_dum, gpmax, gxmax, gymax, gzmax )

    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' For equation=',eq
      write(6,*) ' Residual maximum =',gpmax
      write(6,"(1x,a,3f20.10)") ' ...at x,y,z=',gxmax,gymax,gzmax
    endif

    !...broadcast these results
    call lmpi_bcast(gpmax)
    call lmpi_bcast(gxmax)
    call lmpi_bcast(gymax)
    call lmpi_bcast(gzmax)

    !...find location of global maximum
    if ( gpmax > grmax ) then
      grmax = gpmax
      rxmax = gxmax
      rymax = gymax
      rzmax = gzmax
    endif

    call se_flush(6)
    call lmpi_synchronize

    !...print some information at rxmax, rymax, rzmax
    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Elements of linearization at highest residual location.'
      write(6,*) ' ....x=',rxmax,rymax,rzmax
      write(6,*)
    endif
    call survey_c( dim_q, dim_a_diag, dim_a_off, dim_ia, dim_ja,        &
                   rxmax,          rymax,             rzmax,            &
                   soln%dof0,      grid%volq,                           &
                   q,              res,                                 &
                   grid%xq,        grid%yq,           grid%zq,          &
                   a_diag,         a_off,                               &
                   crow%iam,       crow%jam, crow%g2m, crow%m2g, nb, eq )

    call se_flush(6)
    call lmpi_synchronize

    if ( skeleton > 0 ) then
      write(6,*)
      write(6,*) ' Completed inspection of matrix at largest residual.'
    endif

    !...print some information at specified location

    polar_coordinate_avg = .false.
    if ( q_2d ) polar_coordinate_avg = .false.
    xavg = 0._dp
    yavg = 0._dp
    zavg = 0._dp
    tavg = 0._dp
    ravg = 0._dp
    do i=1,soln%dof0
      xavg = xavg + grid%xq(i)
      yavg = yavg + grid%yq(i)
      zavg = zavg + grid%zq(i)
      if ( .not. polar_coordinate_avg ) cycle
      ravg = ravg + sqrt( grid%xq(i)**2 + grid%zq(i)**2 )
      tavg = tavg + atan2( real(grid%zq(i),dp),real(grid%xq(i),dp) )*conv
    enddo
    xavg = xavg/real( soln%dof0 , dp )
    yavg = yavg/real( soln%dof0 , dp )
    zavg = zavg/real( soln%dof0 , dp )
    ravg = ravg/real( soln%dof0 , dp )
    tavg = tavg/real( soln%dof0 , dp )

    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Average x/y/z on master processor:'
      write(6,"(1x,a,3f20.10)") ' ........x,y,z=',xavg,yavg,zavg
      if ( polar_coordinate_avg )                         &
      write(6,"(1x,a,3f20.10)") ' .....r,t(deg)=',ravg,tavg
    endif

    if ( polar_coordinate_avg ) then
      xavg = ravg*cos(tavg/conv)
      zavg = ravg*sin(tavg/conv)
      if ( lmpi_master ) then
        write(6,*)
        write(6,*) ' Average x/z on master processor (via polar coordinates):'
        write(6,"(1x,a,3f20.10)") ' ........x,z=',xavg,zavg
      endif
    endif

    dmin = huge(1.0_dp)
    do i=1,soln%dof0
      d = ( xavg - grid%xq(i) )**2 &
        + ( yavg - grid%yq(i) )**2 &
        + ( zavg - grid%zq(i) )**2
      if ( d < dmin ) then
        dmin = d
        dof  = i
        gxmax = grid%xq(i)
        gymax = grid%yq(i)
        gzmax = grid%zq(i)
      endif
    enddo

    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Closest x/y/z to average on master processor:'
      write(6,"(1x,a,3f20.10)") ' .............x,y,z=',gxmax,gymax,gzmax
      write(6,"(1x,a,i10)")     ' ...dof(grid-order)=',dof
      write(6,"(1x,a,3f20.10)") ' ..............dmin=',dmin
    endif

    !gxmax = grid%xq(1)
    !gymax = grid%yq(1)
    !gzmax = grid%zq(1)

    !...broadcast these locations
    call lmpi_bcast(gxmax)
    call lmpi_bcast(gymax)
    call lmpi_bcast(gzmax)

    !...print some information at gxmax, gymax, gzmax
    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Elements of linearization at specified x,y,z.'
      write(6,*) ' ........dim_q=',dim_q
      write(6,*) ' ...dim_a_diag=',dim_a_diag
      write(6,*) ' ....dim_a_off=',dim_a_off
      write(6,*)
    endif

    call survey_c( dim_q, dim_a_diag, dim_a_off, dim_ia, dim_ja,        &
                   gxmax,          gymax,             gzmax,            &
                   soln%dof0,      grid%volq,                           &
                   q,              res,                                 &
                   grid%xq,        grid%yq,           grid%zq,          &
                   a_diag,         a_off,                               &
                   crow%iam,       crow%jam, crow%g2m, crow%m2g, nb, eq )

    call se_flush(6)
    call lmpi_synchronize

    if ( skeleton > 0 ) then
      write(6,*)
      write(6,*) ' Completed inspection of matrix at specified x/y/z.'
    endif

    if ( allocate_q)      deallocate(q)
    if ( allocate_res)    deallocate(res)
    if ( allocate_a_diag) deallocate(a_diag)
    if ( allocate_a_off)  deallocate(a_off)

    if ( skeleton > 0 ) then
      write(6,*)
      write(6,*) ' Completed survey of matrix.'
      write(6,*)
    endif

  end subroutine matrix_survey

!================================ SURVEY_P ===================================80
!
! This routine checks positivity for a general block matrix.
!
!=============================================================================80
  subroutine survey_p(dim_q, dim_a_diag, dim_a_off, dim_ia,                    &
                      bandwidth_max, bandwidth_avg,                            &
                      diag_max, diag_min,                                      &
                      pmaxsum, pmax, xmax,ymax,zmax,                           &
                      n_profile, p_profile, m_profile,                         &
                      x_profile, y_profile, z_profile,                         &
                      dof0,                                                    &
                      x, y, z, vol,                                            &
                      a_diag, a_off, iam, g2m, nb, eq )

    integer, intent(in) :: dim_q, dim_a_diag, dim_a_off, dim_ia
    integer, intent(in) :: dof0, eq, nb
    integer, dimension(dim_ia), intent(in)  :: iam
    integer, dimension(:),      intent(in)  :: g2m
    integer,                    intent(in)  :: n_profile
    integer, dimension(1),  intent(out) :: bandwidth_max
    integer, dimension(1),  intent(out) :: bandwidth_avg

    real(dp), dimension(1), intent(out) :: pmax
    real(dp), dimension(1), intent(out) :: pmaxsum
    real(dp), dimension(1), intent(out) :: xmax
    real(dp), dimension(1), intent(out) :: ymax
    real(dp), dimension(1), intent(out) :: zmax

    real(dp), dimension(dim_q),            intent(in)  :: x, y, z
    real(dp), dimension(dim_q),            intent(in)  :: vol
    real(dp), dimension(nb,nb,dim_a_diag), intent(in)  :: a_diag
    real(odp),dimension(nb,nb,dim_a_off),  intent(in)  :: a_off

    real(dp), dimension(1),           intent(out) :: diag_max
    real(dp), dimension(1),           intent(out) :: diag_min
    real(dp), dimension(n_profile,1), intent(out) :: p_profile
    real(dp), dimension(n_profile,1), intent(out) :: m_profile
    real(dp), dimension(n_profile,1), intent(out) :: x_profile
    real(dp), dimension(n_profile,1), intent(out) :: y_profile
    real(dp), dimension(n_profile,1), intent(out) :: z_profile

    integer :: dof, i, profile_sum, ierr
    integer :: connect_dof, row

    real(dp) :: diag, diag_vol, off, offmax, offsum
    real(dp) :: ptrial

    logical :: profile_set

  continue

    diag_max(1) = -huge(1.0_dp)
    diag_min(1) = +huge(1.0_dp)

    bandwidth_max(1) = 0
    bandwidth_avg(1) = 0

    pmax(1)    = -huge(1.0_dp)
    xmax(1)    = -huge(1.0_dp)
    ymax(1)    = -huge(1.0_dp)
    zmax(1)    = -huge(1.0_dp)
    pmaxsum(1) = -huge(1.0_dp)

    p_profile(1:n_profile,1) =  0._dp
    m_profile(1:n_profile,1) = -huge(1.0_dp)
    x_profile(1:n_profile,1) =  0._dp
    y_profile(1:n_profile,1) =  0._dp
    z_profile(1:n_profile,1) =  0._dp

!   Loop over all the dofs within this partition.

    ierr = 0
    dof_loop : do dof = 1,dof0

      offsum   = 0._dp
      offmax   = 0._dp
      diag     = a_diag(eq,eq,g2m(dof))
      diag_vol = diag/vol(dof)
      diag_max(1) = max( diag_max(1) , diag_vol )
      diag_min(1) = min( diag_min(1) , diag_vol )
      row = g2m(dof)
      bandwidth_max(1) = max( bandwidth_max(1) , iam(row+1)-iam(row)+1 )
      bandwidth_avg(1) =      bandwidth_avg(1) + iam(row+1)-iam(row)+1
      do connect_dof = iam(row),iam(row+1)-1
        off = a_off(eq,eq,connect_dof)
        if ( sign( 1._dp , off )*sign( 1._dp , diag ) > 0._dp ) then
          offsum = offsum + abs( off )
          offmax = max( offmax , abs( off ) )
        endif
      enddo

!     Track maximum off diagonal terms (with location) for each equation

      if ( abs(diag) < 1.0e-12_dp ) then
        ptrial   = huge(1.0_dp)
        pmax(1) = ptrial
        xmax(1) = x(dof)
        ymax(1) = y(dof)
        zmax(1) = z(dof)
        ierr = ierr + 1
      else
       ptrial = abs( offmax / diag )
       if ( ptrial > pmax(1) ) then
         pmax(1) = ptrial
         xmax(1) = x(dof)
         ymax(1) = y(dof)
         zmax(1) = z(dof)
        endif
      endif

!     Track maximum off diagonal terms within brackets

      profile_set = .false.
      profile_loop : do i=n_profile,1,-1
        if ( ptrial >= real(2*(i-1),dp)/real(n_profile-1,dp) ) then
          p_profile(i,1) = p_profile(i,1) + 1._dp
          profile_set = .true.
          if ( ptrial > m_profile(i,1) ) then
            m_profile(i,1) = ptrial
            x_profile(i,1) = x(dof)
            y_profile(i,1) = y(dof)
            z_profile(i,1) = z(dof)
          endif
        endif
        if ( profile_set) exit
      enddo profile_loop

!     Track maximum off diagonal sum terms for each equation

      if ( abs(diag) < 1.0e-12_dp ) then
        ptrial      = huge(1.0_dp)
        pmaxsum(1) = ptrial
      else
        ptrial = abs( offsum / diag )
        if ( ptrial > pmaxsum(1) ) then
          pmaxsum(1) = ptrial
        endif
      endif

    end do dof_loop

    if ( ierr /= 0 ) then
      write(*,"(1x,2(a,i10))") ' WARNING ... small diagonals...number=',ierr,&
                               ' dof0=',dof0
    endif

!   Count number of points accumulated in profile

    profile_sum = nint( real( sum( p_profile(:,1) ) , dp ) ) - dof0
    if ( profile_sum /= 0 ) then
      write(*,*) ' profile_sum error...profile_sum=',        &
                 nint( real( sum( p_profile(:,1) ) , dp ) ), &
                 ' dof0=',dof0,' processor=',lmpi_id
    endif

  end subroutine survey_p

!================================ SURVEY_C ===================================80
!
! This routine prints out info related to general block matrix.
!
!=============================================================================80
  subroutine survey_c(dim_q, dim_a_diag, dim_a_off, dim_ia, dim_ja,            &
                      xp, yp, zp,                                              &
                      dof0, vol, q, res, x, y, z,                              &
                      a_diag, a_off, iam, jam, g2m, m2g, nb, eq )

    integer, intent(in) :: dim_q, dim_a_diag, dim_a_off, dim_ia, dim_ja
    integer, intent(in) :: dof0, nb, eq
    integer, dimension(dim_ia), intent(in) :: iam
    integer, dimension(dim_ja), intent(in) :: jam
    integer, dimension(:), intent(in) :: g2m, m2g

    real(dp), intent(in) :: xp, yp, zp
    real(dp), dimension(dim_q),              intent(in) :: vol
    real(dp), dimension(nb,dim_q),           intent(in) :: q
    real(dp), dimension(nb,dim_q),           intent(in) :: res
    real(dp), dimension(dim_q),              intent(in) :: x, y, z
    real(dp), dimension(nb,nb,dim_a_diag),   intent(in) :: a_diag
    real(odp),   dimension(nb,nb,dim_a_off), intent(in) :: a_off

    integer :: dof, n_count, eq2, row, points
    integer :: connect_dof, dofc, pass, n_offp

    real(dp), dimension(3) :: check

    real(dp) :: diagi
! beginNeverComplex
    real(dp) :: xc, yc, zc, tol
! endNeverComplex

    continue

    tol = 1.0e-11_dp

    points = 0
    if ( lmpi_master ) then
      write(6,*)
      write(6,*) ' Finding points within tolerance of point below:'
      write(6,"(1x,a,3f20.10)") ' ........x,y,z=',xp,yp,zp
    endif

!   Loop over all the dofs within this partition
    dof_loop : do dof = 1,dof0

      xc = xp
      yc = yp
      zc = zp

      check(1) = abs( x(dof)- xc )
      check(2) = abs( y(dof)- yc )
      check(3) = abs( z(dof)- zc )
      if ( sum(check) > tol ) cycle dof_loop

      points = points + 1
      write(6,*)
      write(6,*) ' Point found within tolerance...total_found=',points
      write(6,*) ' Elements of the linearization for point:'
      write(6,"(1x,a,3f20.10)") ' ........x,y,z=',x(dof),y(dof),z(dof)
      write(6,*) ' .....processor=',lmpi_id
      write(6,*) ' ...dof(  grid)=',dof
      write(6,*) ' ...dof(matrix)=',g2m(dof)
      write(6,*) ' ............nb=',nb
      write(6,*)

      write(6,*) ' ......volume=',vol(dof)
      write(6,"(1x,a,3f20.10)") ' ...........q=',q(eq,dof)
      write(6,"(1x,a,3f20.10)") ' ....residual=',res(eq,dof)

      n_offp = 0
      row = g2m(dof)
      do connect_dof = iam(row),iam(row+1)-1
        if ( m2g(jam(connect_dof)) > dof0) n_offp = n_offp + 1
      enddo
      write(6,*)
      write(6,"(3x,'Total number of off-diagonal contributions = ',i10)") &
                                                iam(row+1)-iam(row)

      write(6,"(3x,'     Number of off-processor contributions = ',i10)") &
                                               n_offp

      n_count = 0
      diagi = 1.0_dp/abs( a_diag(eq,eq,g2m(dof)) )
      write(6,*)
      write(6,"(3x,'|diagonal|=',e20.10)") abs( a_diag(eq,eq,g2m(dof)) )
      write(6,*)
      write(6,"(4x,'dofc',16x,'x',16x,'y',16x,'z',2x,'*',&
              &8x,'contribution',4x,'contribution/|diagonal|')")

      write(6,"(i8,3e17.6,2x,'d',2e20.10)") dof,x(dof),y(dof),z(dof),  &
                a_diag(eq,eq,g2m(dof)),a_diag(eq,eq,g2m(dof))*diagi
      write(6,*)

!     Make 2 passes : on-processor
!                   : off-processor

        pass_loop : do pass = 1,2
        row = g2m(dof)
        ia_loop : do connect_dof = iam(row),iam(row+1)-1

        dofc = m2g(jam(connect_dof))
        if ( (pass == 1) .and. (dofc >= dof0)) cycle
        if ( (pass == 2) .and. (dofc <  dof0)) cycle

        if ( (abs( y(dofc)-yc ) <= tol) .and. (x(dofc) < x(dof)) ) then

          do eq2=1,eq-1
            write(6,"(8x,51x,i3,2e20.10)")                                &
                     eq2,a_off(eq,eq2,connect_dof),                       &
                         a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,"(i8,3e17.6,i3,2e20.10)")                               &
                  m2g(jam(connect_dof)), x(dofc),y(dofc),z(dofc),         &
                  eq,a_off(eq,eq,connect_dof),                            &
                     a_off(eq,eq,connect_dof)*diagi
          do eq2=eq+1,nb
            write(6,"(8x,51x,i3,2e20.10)")                                &
                    eq2,a_off(eq,eq2,connect_dof),                        &
                        a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,*)
          n_count = n_count + 1

        elseif ( (abs( y(dofc)-yc ) <= tol) .and.   &
         (abs( x(dofc) - x(dof)) ) <= tol ) then

          do eq2=1,eq-1
            write(6,"(8x,51x,i3,2e20.10)")                                &
                     eq2,a_off(eq,eq2,connect_dof),                       &
                         a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,"(i8,3e17.6,i3,2e20.10)")                               &
                  m2g(jam(connect_dof)), x(dofc),y(dofc),z(dofc),         &
                  eq,a_off(eq,eq,connect_dof),                            &
                     a_off(eq,eq,connect_dof)*diagi
          do eq2=eq+1,nb
            write(6,"(8x,51x,i3,2e20.10)")                                &
                    eq2,a_off(eq,eq2,connect_dof),                        &
                        a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,*)
          n_count = n_count + 1

        elseif ( (abs( y(dofc)-yc ) <= tol) .and. &
          (x(dofc) > x(dof)) ) then

           do eq2=1,eq-1
            write(6,"(8x,51x,i3,2e20.10)")                                &
                     eq2,a_off(eq,eq2,connect_dof),                       &
                         a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,"(i8,3e17.6,i3,2e20.10)")                               &
                  m2g(jam(connect_dof)), x(dofc),y(dofc),z(dofc),         &
                  eq,a_off(eq,eq,connect_dof),                            &
                     a_off(eq,eq,connect_dof)*diagi
          do eq2=eq+1,nb
            write(6,"(8x,51x,i3,2e20.10)")                                &
                     eq2,a_off(eq,eq2,connect_dof),                       &
                         a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,*)
          n_count = n_count + 1

        else

          do eq2=1,eq-1
            write(6,"(8x,51x,i3,2e20.10)")                                &
                     eq2,a_off(eq,eq2,connect_dof),                       &
                         a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,"(i8,3e17.6,i3,2e20.10)")                               &
                  m2g(jam(connect_dof)), x(dofc),y(dofc),z(dofc),         &
                  eq,a_off(eq,eq,connect_dof),                            &
                     a_off(eq,eq,connect_dof)*diagi
          do eq2=eq+1,nb
            write(6,"(8x,51x,i3,2e20.10)")                                &
                    eq2,a_off(eq,eq2,connect_dof),                        &
                        a_off(eq,eq2,connect_dof)*diagi
          enddo
          write(6,*)
          n_count = n_count + 1

        endif

        enddo ia_loop

        write(6,*)

      enddo pass_loop

      row = g2m(dof)
      if ( n_count /= iam(row+1)-iam(row) ) then
        write(6,*) ' Caution...some dofs missed in printout above....'
      endif

    end do dof_loop

    call se_flush()

  end subroutine survey_c

end module survey_matrix

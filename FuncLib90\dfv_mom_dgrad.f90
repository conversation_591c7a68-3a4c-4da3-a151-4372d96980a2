!================================= DFV_MOM_DGRAD ============================80
!
! Jacobians of viscous momentum fluxes w/r to gradients of u,v,w.
!...with constant term t..ordered as eqns 1-4.
!
!=============================================================================80
  pure function dfv_mom_dgrad( t, xn, yn, zn, dux, duy, duz,                   &
                                              dvx, dvy, dvz,                   &
                                              dwx, dwy, dwz )

    use flux_constants,    only : xmr, c43, c23

    real(dp), intent(in) :: t, xn, yn, zn
    real(dp), intent(in) :: dux, duy, duz, &
                            dvx, dvy, dvz, &
                            dwx, dwy, dwz

    real(dp), dimension(3,3) :: dfv_mom_dgrad

  continue

!   xmr   = xmach/re
!   c43   = xmr*4._dp/3._dp
!   c23   = xmr*2._dp/3._dp

    dfv_mom_dgrad(1,1) = -t*(  xn*c43*dux + yn*xmr*duy + zn*xmr*duz )

    dfv_mom_dgrad(2,1) = -t*(- yn*c23*dux + xn*xmr*duy              )

    dfv_mom_dgrad(3,1) = -t*(- zn*c23*dux              + xn*xmr*duz )

    dfv_mom_dgrad(1,2) = -t*(  yn*xmr*dvx - xn*c23*dvy              )

    dfv_mom_dgrad(2,2) = -t*(  xn*xmr*dvx + yn*c43*dvy + zn*xmr*dvz )

    dfv_mom_dgrad(3,2) = -t*(             - zn*c23*dvy + yn*xmr*dvz )

    dfv_mom_dgrad(1,3) = -t*(  zn*xmr*dwx              - xn*c23*dwz )

    dfv_mom_dgrad(2,3) = -t*(               zn*xmr*dwy - yn*c23*dwz )

    dfv_mom_dgrad(3,3) = -t*(  xn*xmr*dwx + yn*xmr*dwy + zn*c43*dwz )

  end function dfv_mom_dgrad

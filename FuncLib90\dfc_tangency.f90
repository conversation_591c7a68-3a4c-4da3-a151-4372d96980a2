!============================ DFC_TANGENCY ===================================80
!
! Correct compressible perfect gas flux jacobians (-) for inviscid
!
! Note: ql, qr are conservative variables (face_speed also!)
!
!=============================================================================80

  pure function dfc_tangency(xnorm, ynorm, znorm, rface_speed, dfm )

    real(dp), intent(in) :: xnorm, ynorm, znorm, rface_speed

    real(dp), dimension(5,5), intent(in) :: dfm

    real(dp), dimension(5,5)             :: dfc_tangency

    integer :: i, j, k

    real(dp), dimension(5,5) :: correction

  continue

    dfc_tangency = dfm

    correction(2,1) = +2.0_dp*xnorm*rface_speed
    correction(2,2) = -2.0_dp*xnorm*xnorm
    correction(2,3) = -2.0_dp*xnorm*ynorm
    correction(2,4) = -2.0_dp*xnorm*znorm

    correction(3,1) = +2.0_dp*ynorm*rface_speed
    correction(3,2) = -2.0_dp*ynorm*xnorm
    correction(3,3) = -2.0_dp*ynorm*ynorm
    correction(3,4) = -2.0_dp*ynorm*znorm

    correction(4,1) = +2.0_dp*znorm*rface_speed
    correction(4,2) = -2.0_dp*znorm*xnorm
    correction(4,3) = -2.0_dp*znorm*ynorm
    correction(4,4) = -2.0_dp*znorm*znorm

    do i=1,5
      do j=1,4
        do k=2,4
          dfc_tangency(i,j) = dfc_tangency(i,j) + dfm(i,k)*correction(k,j)
        enddo
      enddo
    enddo

  end function dfc_tangency

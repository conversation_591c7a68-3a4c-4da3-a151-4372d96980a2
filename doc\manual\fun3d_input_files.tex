
\renewcommand{\namelistsection}[2]{\newpage\subsubsection{#1}\label{#2}}

\section{\FunThreeD Input Files\label{c:namelist}}

There are a variety of input files necessary for the various codes that 
make up the \FunThreeD suite.
\Tab{t:inputfiles} lists frequently used input files
with a short description.
This chapter will describe the basic formats of each of these files
and meaning of the specific inputs they contain. 

\begin{table}[hbtp]
  \centering\tabularfont
  \caption{\FunThreeD input files.}\label{t:inputfiles}
  \begin{threeparttable}
  \begin{tabular}{lp{2.75in}}
   File & Description \\
   \midrule
    \file{stop.dat} 
    & signals a change to the number of iterations / time steps that were requested at
     the beginning of the run \\
    \file{remove_boundaries_from_force_totals} 
    & omits boundary faces from total force integration\\
    \file{[project_rootname].flow}\tnote{*} 
    & flow field solution \\
    \file{fun3d.nml} 
    & primary Fortran namelist (required) \\
    \file{moving_body.input} 
    & body motion Fortran namelist \\
    \file{rotor.data} 
    & describes the rotor actuator disk model \\
    \file{tdata} 
    & specifies the generic gas model \\
    \file{kinetic_data} 
    & specifies the possible chemical reactions in the generic gas model \\
    \file{species_transp_data} 
    & specifies generic gas model species collision cross sections \\
    \file{species_transp_data_0} 
    & specifies a higher-order generic gas model
    species collision cross sections \\
    \file{hara_namelist_data} 
    & controls the radiation models used by the \textsc{Hara} library\\
  \end{tabular}
  \begin{tablenotes}
    \item [*] The \cmd{[project_rootname]} is a \cmd{&project} namelist variable,
              see \sectionref{s:nml_project}.
  \end{tablenotes}
  \end{threeparttable}
\end{table}

\FunThreeD utilizes Fortran namelists for a large portion of input
specification because it is defined in the Fortran 90 standard.
With all Fortran namelists, leaving out or misspelling any namelist 
(defined with an ampersand preceding its name) will 
result in default values being used for all of the parameters within that 
namelist.
For example, if the namelist name \file{linear_solver_parameters} were
to be misspelled as \file{linear_solver_parameter} (missing \file{s}),
then all parameters within that namelist
would be ignored and retain their default values.
Leaving out any parameter within a namelist results in the default 
value for that parameter being used.
Misspelling or misusing any particular parameter will typically cause
\FunThreeD to issue an error and stop.

\subsection{\protect\file{stop.dat}}\label{s:stop.dat}

This optional file either halts or extends the execution of the solver.
The \file{stop.dat} plain text file contains a single integer.
After every iteration, the solver will check to see if this file exists.
If the file is found in the directory that the solver was invoked,
the integer is read and a message is printed to the standard output
stream of the form,
``stop.dat file found, user requested stop at cycle N.''
If the integer is greater than zero and 
less than or equal to the current iteration,
the solver will write the current solution, 
delete the \file{stop.dat} file, 
and halt execution.
If the integer is less than zero,
the solver will \emph{not} write the restart file,
but will delete the \file{stop.dat} file and halt execution.
If the integer is zero, \file{stop.dat} will be ignored.

The integer is with respect to the \var{steps} variable
in the \var{&code_run_control} namelist and not with respect to
a cumulative number of steps that may result from a restarted run.
The value of the integer may be greater than the number of \var{steps}
specified in \file{fun3d.nml}'s \var{&code_run_control} namelist,
so that \file{stop.dat} can be used to extend the execution
of the solver.
Some environments, especially ones with 
network-mounted filesystems (e.g., NFS),
may exhibit a delay in the \file{stop.dat} file being read or
being deleted.

\subsection{\protect\file{[project_rootname].flow}}\label{s:*.flow}

The optional \file{[project_rootname].flow} binary file contains 
flow solution and checkpoint information.
The \cmd{[project_rootname]} is a \cmd{&project} namelist variable,
see \sectionref{s:nml_project}.
This file is read by the solver to restart computations from
a previously computed flow solution.
The contents vary due to the checkpoint requirements of the simulation.
The file contains a minimum of the current solution and convergence
history.
It can also contain working variables for the turbulence model, 
solutions from previous iterations for time accurate cases, or
previous grid positions and velocities for deforming grids.

\subsection{\protect\file{remove_boundaries_from_force_totals}}\label{s:rbfft}

The optional \file{remove_boundaries_from_force_totals} file is for 
specifying boundaries that are \emph{not} to be included in the calculation of 
force and moment totals. 
This file is useful, for example, in situations where there may be a mounting 
sting on a wind tunnel model, but only the forces on the model are actually of 
interest. 
The forces on the specified boundaries are still computed
and appear in the \file{[project_rootname].forces} file.
However, they are not included in the totals.
The position of the text lines in this file is significant.
So, follow this template carefully:
\begin{Verbatim}
Remove selected boundaries from the total forces
Number of boundaries to turn off
2
Boundaries to turn off (boundary lumping changes indexes)
12
15
\end{Verbatim}
The third line is the number of boundaries to exclude.
The fifth and subsequent lines are the patch indexes of the excluded boundaries.

\subsection{\protect\file{fun3d.nml}}\label{s:fun3d.nml}

The main input namelist file, \file{fun3d.nml}, is described in detail below, 
with defaults listed before the descriptions. 
The namelist file contains a large number of input variables. 
In general, it is not necessary to specify them all because they
have suitable default values.
Only those variables that are \emph{different} from the defaults need to be 
specified. 
An overview of tasks and their associated namelists are listed below.


\paragraph{The project name and grid information:}
\begin{itemize}
\item[] \nameandsection{s:nml_project} 
\item[] \nameandsection{s:nml_raw_grid}
\item[] \nameandsection{s:nml_force_moment_integ_properties}
\item[] \nameandsection{s:nml_grid_transform}
\item[] \nameandsection{s:nml_body_transform}
\end{itemize}

\paragraph{The equation set and reference conditions:}
\begin{itemize}
\item[] \nameandsection{s:nml_governing_equations} 
\item[] \nameandsection{s:nml_reference_physical_properties}
\item[] \nameandsection{s:nml_noninertial_reference_frame}
\end{itemize}

\paragraph{The inviscid flux discretization:}
\begin{itemize}
\item[] \nameandsection{s:nml_inviscid_flux_method}
\end{itemize}

\paragraph{Turbulence model:}
\begin{itemize}
\item[] \nameandsection{s:nml_turbulent_diffusion_models}
\item[] \nameandsection{s:nml_spalart}
\item[] \nameandsection{s:nml_gammaretsst}
\end{itemize}

\paragraph{Number of and size of time steps or steady iterations:}
\begin{itemize}
\item[] \nameandsection{s:nml_code_run_control}
\item[] \nameandsection{s:nml_nonlinear_solver_parameters}
\end{itemize}

\paragraph{Linear relaxation controls:}
\begin{itemize}
\item[] \nameandsection{s:nml_linear_solver_parameters}
\item[] \nameandsection{s:nml_flow_gmres}
\item[] \nameandsection{s:nml_elasticity_gmres}
\end{itemize}

\paragraph{Boundary conditions and transition:}
\begin{itemize}
\item[] \nameandsection{s:nml_boundary_conditions}
\item[] \nameandsection{s:nml_periodicity}
\item[] \nameandsection{s:nml_two_d_trans}
\item[] \nameandsection{s:nml_three_d_trans}
\end{itemize}

\paragraph{Flowfield initialization:}
\begin{itemize}
\item[] \nameandsection{s:nml_flow_initialization}
\end{itemize}

\paragraph{Force tracking and visualization:}
\begin{itemize}
\item[] \nameandsection{s:nml_component_parameters}
\item[] \nameandsection{s:nml_time_avg_params}
\item[] \nameandsection{s:nml_global}
\item[] \nameandsection{s:nml_volume_output_variables}
\item[] \nameandsection{s:nml_boundary_output_variables}
\item[] \nameandsection{s:nml_sampling_output_variables}
\item[] \nameandsection{s:nml_sampling_parameters}
\item[] \nameandsection{s:nml_slice_data}
\end{itemize}

\paragraph{Overset grid systems and rotorcraft simulation:}
\begin{itemize}
\item[] \nameandsection{s:nml_overset_data}
\item[] \nameandsection{s:nml_rotor_data}
\end{itemize}

\paragraph{Grid adaptation:}
\begin{itemize}
\item[] \nameandsection{s:nml_adapt_metric_construction}
\item[] \nameandsection{s:nml_adapt_mechanics}
\end{itemize}

\paragraph{Design optimization cost functions:}
\begin{itemize}
\item[] \nameandsection{s:nml_massoud_output}
\item[] \nameandsection{s:nml_sonic_boom}
\item[] \nameandsection{s:nml_sboom}
\item[] \nameandsection{s:nml_equivalent_area}
\item[] \nameandsection{s:nml_press_box_function}
\item[] \nameandsection{s:nml_pstag_function}
\end{itemize}

\paragraph{Other:}
\begin{itemize}
\item[] \nameandsection{s:nml_special_parameters}
\item[] \nameandsection{s:nml_fwh_acoustic_data}
\item[] \nameandsection{s:nml_vortex_generator}
\end{itemize}





\input{nml_project}
\input{nml_raw_grid}
\input{nml_force_moment_integ_properties}
\input{nml_grid_transform}
\input{nml_body_transform}

\input{nml_governing_equations}
\input{nml_reference_physical_properties}
\input{nml_noninertial_reference_frame}

\input{nml_inviscid_flux_method}

\input{nml_turbulent_diffusion_models}
\input{nml_spalart}
\input{nml_gammaretsst}

\input{nml_code_run_control}
\input{nml_nonlinear_solver_parameters}

\input{nml_linear_solver_parameters}
\input{nml_flow_gmres}
\input{nml_elasticity_gmres}

\input{nml_boundary_conditions}
\input{nml_periodicity}
\input{nml_two_d_trans}
\input{nml_three_d_trans}

\input{nml_flow_initialization}

\input{nml_component_parameters}
\input{nml_time_avg_params}
\input{nml_global}
\input{nml_volume_output_variables}
\input{nml_boundary_output_variables}
\input{nml_sampling_output_variables}
\input{nml_sampling_parameters}
\input{nml_slice_data}


\paragraph{Important Considerations for Determination of Leading And Trailing Edges}
\label{s:consider-slices}
Determining the locations of airfoil leading and trailing edges
is especially important for rotorcraft applications where 
airloads are usually examined (and provided to a CSD code, if applicable), 
in an airfoil section-aligned coordinate system. 
The leading and trailing edge points determine the orientation of this
section aligned coordinate system.
In the section-aligned system,
the local $x$ coordinate is aligned with the local chord, positive in the 
direction from the leading edge to the trailing edge. The local span direction
is defined by the moment centers at the \var{slice_location} points, positive
in the direction of increasing  \var{slice_location}. The local normal
direction is defined as the cross product of the local chord vector and local span vector.
When slicing boundary data, the computed forces are computed in both 
the selected frame of reference (see \cmd{slice_frame}) and
in an airfoil section aligned system. 
If the data in the section-aligned system is irrelevant to you, 
then you do not need to worry about choosing the detection
parameters carefully; the default values should be reasonable.
However, if resolution of forces and moments into a section-aligned system
is important to you,
then there are a number of things that should be considered:

\begin{enumerate}
\item Make sure the chord direction \cmd{chord_dir} is correct;
the default is that
going from the leading edge to the trailing edge is the same as traveling in the positive 
``chordwise'' coordinate direction. 
For most applications this is the usual situation;
however, the convention for rotorcraft applications is the opposite,
requiring \cmd{chord_dir = -1}.

\item Since the best option for determining the leading edge uses the trailing edge location 
(\cmd{le_def} \textgreater{} 1), 
care should be taken to get the trailing edge correct. 
For \emph{sharp} trailing edges, this is very simple since the default of 
\cmd{te_def = 1} (i.e., use the aft-most point) is the best option. 
However, smoothly blunted or squared-off trailing edges are more difficult. 
When the boundary surface of an unstructured mesh is sliced, 
the resulting section will be comprised of line segments determined by the 
intersection of the specified plane and the edges of the surface triangles. 
These segments and the points that make up the segments will not usually be the 
same as the surface points; typically there are more segments and points
 arising from intersected triangles, 
as illustrated in \fig{f:in:te_points}. 
This greater point count should influence the selection of \cmd{te_def} and \cmd{le_def} values. 
You will need enough segments (\cmd{te_def} and \cmd{le_def})
to ensure that both corners are detected, 
but not so many that other, non trailing-edge corners (if present) are detected.
Another parameter that may be of use to aid in the detection of corners is the 
\cmd{corner_angle}; corners with angles larger than \cmd{corner_angle} between 
adjacent segments will require a larger value of \cmd{corner_angle} for detection.

\begin{figure}[b!]
  \centering
  \includegraphics[trim=10 0 10 10,clip,width=0.75\linewidth]{te_geom_close_up_2.jpg}
  \caption{View looking upstream from the trailing edge of a rotor blade mesh;
  the light-colored region is the squared-off trailing edge; the red line shows the
  location where an $x$=constant slice will be taken; black circles indicate surface
  grid points on the trailing edge.}
  \label{f:in:te_points}
\end{figure}

\begin{figure}[b!]
  \centering
  \includegraphics[trim=10 0 10 10,clip,width=0.75\linewidth]{te_geom_close_up.jpg}
  \caption{Sliced section corresponding to \fig{f:in:te_points}; 
   zoomed in to the trailing edge region.}
  \label{f:in:te_slice}
\end{figure}

The resulting section corresponding to the slice depicted in \fig{f:in:te_points}
is shown in \fig{f:in:te_slice}, where the view is zoomed in to 
the trailing edge region. The 
aft-most 8 segments (of the approximately 30 segments in this view) are shown in red.
The computed trailing edge locations using two different \cmd{te_def} values are 
shown. The minimum \cmd{te_def} value at this particular station to pick up both 
corners would be 8, but a value of 20 was used in case another slice
required more segments.
If the blade was pitched downward 
rather than upward, then the point chosen by \cmd{te_def = 1} would be the lower 
corner, rather than the upper corner as shown.
Thus, when pitching up and down, \cmd{te_def = 1} 
with squared-off trailing edges can lead to jumps in the trailing edge position 
as the section transitions from pitch up to pitch down. Depending on the 
thickness of the trailing edge, this can lead to jumps in the geometric pitch 
angle of a few tenths of a degree. To avoid this, the option \cmd{slice_initial_coords = .true.} will reuse the leading and trailing edges determined from the
initial grid definition, rather than the current, displaced grid location.

\item Smoothly-blunted (rounded) trailing edges should be done with either 
\cmd{te_def = 1} (aft-most point) or via a parabolic fit of the aft-most 
\linebreak abs(\cmd{te_def}) points; the latter option is probably better in general but will 
require some experimentation for the particular case at hand to choose the 
optimal number of points over which to fit the parabola.

\item The leading edge is typically easier to determine, if a good 
trailing edge position has already been found. The default value of 
\cmd{le_def = 30} (search the 30 forward-most points for
the one with the greatest distance from the 
trailing edge location) should do a decent job for most cases.

\begin{figure}[b!]
  \centering
  \includegraphics[trim=10 0 10 10,clip,width=0.75\linewidth]{le_geom_close_up.jpg}
  \caption{A sliced section, zoomed in to the leading edge region.}
  \label{f:in:le_slice}
\end{figure}

\Fig{f:in:le_slice} shows a sliced section, zoomed in to the leading edge region. 
The forward-most 
20 segments (of the approximately 30 segments in this view) are shown in red. The 
computed leading edge locations using two different \cmd{le_def} values are shown.
In this case, both results are fairly close but \cmd{le_def = 30} has picked out the 
true leading edge (as judged from the leading edge geometry at zero pitch angle).

\item The leading edge and trailing edge detection schemes can be 
somewhat sensitive to the input choices. For cases that rely on accurate 
resolution of forces and 
moments into section-aligned coordinates (e.g., rotorcraft), it is wise to 
spend some time up front to make sure that things are coming out as expected. 
To do this, inspect the \file{[project_rootname].sectional_forces} file for a particular slice 
station; at each station the computed leading and trailing edge coordinates 
will be output. Plot the corresponding station from the 
\file{[project_rootname]_slice.dat}, as done above, and make sure the computed coordinates 
are the correct ones. If many stations are sliced, it is impractical to inspect 
all of them in this manner, but it is good practice to spot check at least a 
few stations. For moving-geometry cases, try first running the case 
with \cmd{body_motion_only = .true.} in the \cmd{&global} namelist.
This will allow output of the \file{[project_rootname].sectional_forces}
and \file{[project_rootname]_slice.dat} files without the expense of a flow solve or mesh 
deformation; for spot checking you may want to have the slicing done 
infrequently, perhaps using fewer stations than ultimately desired, as these 
output files can be huge.

\item While the \file{[project_rootname].sectional_forces} can be useful for spot checking, the 
data in the file is not in a format that is amenable to plotting. 
The \FunThreeD distribution \file{utils/Rotorcraft} directory contains a utility code that will read
in both the files \file{slice.info} and 
\file{[project_rootname].sectional_forces} to output \Tecplot 
files, for each slice group, containing force and moment data in the 
section-aligned coordinate system, as well as geometry data (leading edge, trailing edge, 
quarter-chord coordinates, and pitch angle).

\item After making sure that the leading edge and trailing edge positions are being computed correctly, 
you may want to turn off one or both of the \file{[project_rootname].sectional_forces}
and \file{[project_rootname]_slice.dat} files unless needed. For instance, in rotorcraft 
applications with coupling to external CSD codes, although the blade boundary 
surfaces must be sliced to generate the aerodynamic loads data for the CSD 
code, this information is actually passed to the CSD code by another file; the 
\file{[project_rootname].sectional_forces} and 
\file{[project_rootname]_slice.dat} files are not used.

\item Although the slicing process will work for multi-element airfoils, at this 
time the computation of the leading edge and trailing edge is only done for the entire section, not 
each element individually.
\end{enumerate}

\input{nml_overset_data}
\input{nml_rotor_data}

\input{nml_adapt_metric_construction}
\input{nml_adapt_mechanics}

\input{nml_massoud_output}
\input{nml_sonic_boom}
\input{nml_sboom}
\input{nml_equivalent_area}
\input{nml_press_box_function}
\input{nml_pstag_function}

\input{nml_special_parameters}
\input{nml_fwh_acoustic_data}
\input{nml_vortex_generator}

\paragraph{Additional information on the use of vortex generator source terms}
The implementation of these source terms in \FunThreeD is based on the heuristic
model described in \cite{aiaa-2004-1236} and previous references cited therein.
The approach avoids the need for resolving geometric details of vortex generator
devices during mesh generation.  Sufficient grid resolution may still be required
to convect the simulated effects of the vortex generator downstream as desired.
The user must also provide a calibration constant for each simulated vortex
generator.  This value should be chosen carefully to produce the desired impact
on the local flowfield.  Vortex generator source terms currently may only be
applied to static grid simulations.  The source terms are treated fully implicit
during the solution procedure.

After developing the desired set of namelist inputs, it is useful to run the
solver for a single iteration, requesting boundary output for (at least) the
boundaries on which vortex generators are to be placed.  The geometry for each
vortex generator as determined by \FunThreeD based on the namelist inputs will
be provided in the \Tecplot file \file{[project_rootname]_vg_geometry.dat}.  The
user should visualize the placement of each vortex generator in relation to the
boundary patches of the grid to ensure the desired placement.

The user will also be provided with a \Tecplot file
\file{[project_rootname]_vg_vectors.dat}.  This file contains the unit
vectors $\hat b$, $\hat t$, and $\hat n$ according to the notation
described in \cite{aiaa-2004-1236}.  The user should visualize these
vectors to ensure that they are oriented appropriately.  The vector
$\hat b$ is defined uniquely by the local boundary orientation; however,
\FunThreeD attempts to infer the directions of the vectors $\hat t$ and
$\hat n$ based on the freestream direction.  If the local flow direction
is expected to be substantially different, these vectors may need to be
manually reversed using the appropriate namelist inputs.

If desired, the user may also plot the points at which the actual source terms
will be computed by `scatter plotting' the data contained in the \Tecplot file
\file{[project_rootname]_vg_source_locations.dat}.  These locations are
determined by the intersections of the vortex generator geometries with
edges in the grid.  Source terms are computed at each of these locations then
scattered to the residual values at either end of the intersecting edge.

An example of the geometry features described above and the local flowfield
in the vicinity of simulated trapezoidal and triangular vortex generators
near the leading edge of a wing is shown here.

\begin{figure}[h]
  \centering
  \includegraphics[trim=0 0 0 0,clip,width=0.75\linewidth]{vg.jpg}
  \caption{View of trapezoidal and triangular vortex generators placed
           near the leading edge of a wing geometry.  The unit vectors
           $\hat b$, $\hat t$, and $\hat n$ are shown, as well as the
           points where the actual source terms will be computed.}
\end{figure}

\newpage\subsection{\protect\file{moving_body.input}}\label{s:movingbody}

This namelist file is only used for time-dependent, moving grid cases to 
specify grid motion as a function of time. This file  must be used in 
conjunction with input variable \cmd{moving_grid = .true.} in the \cmd{&global}
namelist of the \cmd{fun3d.nml} input file.
See \sectionref{s:grid_motion_overview} for an overview of 
moving grid capabilities.

The grid-motion options in \FunThreeD are fairly generally in order to handle a
wide variety of applications. The basic approach is for the user to define one
or more boundaries in the grid to be a `body'. Multiple bodies may be defined.
Basic setup for these bodies is established using the \cmd{&body_definitions}
namelist. A
hierarchical relation may be established between multiple bodies to allow the
motion of one body (a `child') to follow the motion of another body 
(the `parent'). The top level of this hierarchy is the inertial reference frame,
and all motion is ultimately referenced back to this inertial frame. 
Each body has its own
reference frame, and the reference frames of all bodies are assumed to be
coincident with the inertial reference frame at t=0.

Having established the basic body definition(s), the user specifies a general
descriptor (\cmd{motion_driver}) for the mechanism that will drive the motion
of the body, and specifies how the mesh is to be moved - by rigid
motion or by deformation (or both) - in response to the body motion. Note
that mesh deformation requires more CPU time than rigid mesh motion, and is
less robust. Mesh deformation may lead to negative cell volumes, at which
point the solution is terminated, while rigid motion will preserve positive
cell volumes. Thus, rigid mesh motion should be favored
over mesh deformation whenever possible. However, there are certain situations
where only a deforming mesh is appropriate. For example if the body is
aeroelastic, then the mesh must be deformed to fit the deformed body surface. 
In some situations the potential for a deforming mesh to encounter
negative cell volumes can be mitigated by combining deformation with rigid
motion. An example of this is the motion of elastic rotor blades, wherein the 
overall rotational motion of the blades is handled via rigid rotation, but the 
relatively smaller elastic deflection of the blades is handled via deformation.


The \cmd{motion_driver} specification simply provides a notional mechanism for
how the body is to be moved; details of this mechanism are then provided
by one or more additional namelists. For example, if 
\cmd{motion_driver = `forced'} then details of how to move the body,
perhaps by rotation with a given frequency and amplitude, are
specified via the \cmd{&forced_motion} namelist. Other options for 
\cmd{motion_driver} - and the required auxiliary namelists to specify
the details - are given in the following sections.

By default, boundary output from \FunThreeD for visualization purposes 
(see \sectionref{s:flowvis}) is provided
in the inertial frame. It is sometimes useful to have this output
in a different reference frame, an `observer' frame. For example, the observer
frame might be one attached to a moving body. Specification of an alternate
observer frame is handled via the \cmd{&observer_motion} namelist.

See the following sections for descriptions of the namelists in this file.

\input{nml_body_definitions}
\input{nml_forced_motion}
\input{nml_observer_motion}
\input{nml_motion_from_file}
\input{nml_surface_motion_from_file}
\input{nml_sixdof_motion}
\input{nml_aeroelastic_modal_data}
\input{nml_composite_overset_mesh}
\input{nml_body_motion_trim}

\newpage\subsection{\protect\file{rotor.input}}\label{s:rotor.input}

\FunThreeD is capable of modeling a rotating blade system
using different levels of approximation. 
In order of increasing complexity/fidelity/cost, 
rotor systems may be analyzed using either a 
time-averaged actuator disk, 
or via first principles modeling of the moving, articulated, rotor blades
using overset, moving grids.
The actuator method utilizes momentum/energy source terms to represent
the influence of the rotating blade system. 
Use of the source terms simplifies grid generation,
since the actuator surfaces do not need to be built
into the computational grid. 
However, the computational grid should have some refinement
in the vicinity of the actuator surfaces to obtain accurate results.
The steady-state actuator disk capability was originally implemented by
Dave O'Brien, at the time a PhD candidate 
at Georgia Tech.\cite{obrien:thesis}
O'Brien also initiated the overset capability in \FunThreeD,
which was later extended and coupled to a rotorcraft comprehensive
code by Biedron et al.\cite{biedron2008}

The rotor.input file is used primarily for specifying
input quantities related to an actuator surface
model for rotor/propeller combinations.
When using overset, moving grids and/or coupling \FunThreeD
to a rotorcraft comprehensive code
for a more detailed simulation,
a limited number of the input fields in the \file{rotor.input} file
are also required.
The fields required for coupled rotorcraft simulations
include
\emph{(required for coupled simulation)} in the variable description.
The command line option \cmd{--rotor} is required for both types of analysis.

\FunThreeD can also use the
actuator disk library developed by Dave O'Brien
for the Department of Defense HI-ARMS/CREATE/HELIOS project,
Software Module for Engineering Methods of Rotor Dynamics (SMEMRD).
The \FunThreeD team is unable to provide technical support
for SMEMRD;
please contact \href{mailto:<EMAIL>}{Dave O'Brien}
directly for assistance 
(<EMAIL>).
SMEMRD adds the ability to trim to thrust values
and use airfoil lookup tables.
The \cmd{--hiarms_rotor} command line option activates the SMEMRD model.

The two parameters used to set
the flight condition and force/moment coefficient normalization
in compressible rotorcraft simulations
are \var{mach_number} in \file{fun3d.nml}
and \var{Vinf_Ratio} in \file{rotor.input}.
To nondimensionalize the forces with the rotor tip velocity, set
\var{mach_number} to the tip mach number and 
\var{Vinf_Ratio} to the ratio of freestream velocity to rotor tip velocity.
When \var{mach_number} is the tip mach number then
\cmd{reynolds_number} should be set to the corresponding 
tip Reynolds number.
To nondimensionalize the forces with the freestream velocity, set
\var{mach_number} to the freestream mach number and \var{Vinf_Ratio} to one.
The \var{Vinf_Ratio} will still affect the
force nondimensionalization as described above,
for incompressible solutions.

A sample \file{rotor.input} file is shown below
for a conventional main rotor and tail rotor helicopter.
\VerbatimInput[fontsize=\scriptsize]{utils/Rotorcraft/rotor.input}

The header line is where the user specifies the number of rotors,
the freestream velocity ratio, and how often to output the plot3d loading file.
The remainder of the file is in a block structure,
where each block represents the inputs for one rotor.
The first line of each block is a text line that can be edited
to keep the rotors organized for the user. The input values do not have
to be in a fixed format (spaces and number of decimal points do not matter),
but the input values do
have to be in the correct order as noted by the header lines
for the individual input parameters.

\subsubsection{Header}

\begin{namelist}
  \item[\cmd{# Rotors} \emph{(required for coupled simulation)}]
    This is the number of actuator surfaces to create.
    The number of rotor input blocks in this file
    must match the number of rotors specified.
    
  \item[\cmd{Vinf_Ratio} \emph{(required for coupled simulation)}]
    This is the ratio of the freestream velocity to the
    the velocity used for force normalization.
    The force normalization velocity
    is typically the tip velocity for rotorcraft applications.
  \item[\cmd{Write Soln}]
    This is the frequency (in iterations) of Plot3D rotor
    loading data output, which is pairs of 
    \file{source_grid_00000.p3d} and \file{source_data_00000.p3d} files.
    To write once, set \var{Write Soln} to \cmd{steps}.
  \item[\cmd{Force_ref}]
    This is the conversion factor to obtain forces in alternate units,
    \begin{description}
    \item[$1.0$] 
      ~ will output the standard \FunThreeD nondimensionalization
    \item[$(L_{ref}^2a_{ref}^2)/(\pi R_{rotor}^2 V_{tip}^2)$]
      ~ will output standard rotorcraft nondimensionalization
    \item[$\rho_{ref}a_{ref}^2L_{ref}^2$] 
      ~ will output dimensional units
    \end{description}
  \item[\cmd{Moment_ref}]
    This is the conversion factor to obtain moments in alternate units.
    \begin{description}
    \item[$1.0$] 
      ~ will output the standard \FunThreeD nondimensionalization
    \item[$(L_{ref}^2a_{ref}^3)/(\pi R_{rotor}^3 V_{tip}^2)$]
      ~ will output standard rotorcraft nondimensionalization
    \item[$\rho_{ref}a_{ref}^2L_{ref}^3$] 
      ~ will output dimensional units
    \end{description}
\end{namelist}

\subsubsection{Actuator Surface Model}

\begin{namelist}
  \item[\cmd{Rotor Type}]
    Type of rotor model to apply,
    \begin{description}
    \item[1] ~ models the rotor as an actuator disk.
    \item[2] ~ models the rotor as actuator blades.
    \end{description}
  \item[\cmd{Load Type}]
    Type of loading to apply to the rotor model.
    \begin{description}
    \item[1]~ is a pressure jump based on \var{ThrustCoeff}
              that is constant over the disk.
    \item[2]~ is a pressure jump based on \var{ThrustCoeff}
              that increases linearly with radius.
    \item[3]~ is blade element based loading based defined by
              the blade element parameters defined in
              \sectionref{s:blade-elem-param} and 
              \sectionref{s:pitch-control}.
    \item[4]~ is user specified source geometry and strength.
              Not recommended unless you have experience in 
              actuator disk modeling. See the subroutine
              \file{read_user_source2} in
              \file{LibF90/rotors.f90} for input format.
    \item[5]~ is user specified thrust and torque radial distributions in the
              file \file{propeller_propertiesN.dat},
              where \file{N} is the rotor index.
              The first line of the file is the number of radial stations.
              The following lines have three numbers per station,
              with $r/R$, $\frac{dC_T}{d(r/R)}$, $\frac{dC_l}{d(r/R)}$.
    \item[6]~ is a body force based on on the optimal distribution
              of Goldstein\cite{goldstein-vortex-theory-prop} implemented
              as described by 
              Stern, Kim, and Patel.\cite{stern-kim-patel-act-disk-prop-hull}
              Use \var{ThrustCoeff} to set the thrust and \var{TorqueCoeff}
              to produce swirl (with \var{Swirl=1}).
    \end{description}
  \item[\cmd{# Radial}]
    This is the number of sources to distribute along the blade radius. 
    This should be set to approximately match the resolution of the volume
    grid, otherwise a suggested value is 100.
  \item[\cmd{# Normal}]
    This is the number of sources to distribute along the circumferential
    direction.
    This should be set to approximately match the resolution of the volume
    grid.
    For \var{Rotor Type=1}, 720 is suggested for a source every 0.5 degrees.
    For \var{Rotor Type=2}, 20 points in the chord direction is suggested.
  \item[\cmd{Tip Weight}]
    This is the hyperbolic weighting factor
    for distributing sources along the blade radius.
    A suggested value is 0.0, which yields uniform distribution.
    A value larger than 2.0 is not advised, because
    this large a value concentrates too many sources at the blade tip.
\end{namelist}

\subsubsection{Rotor Reference System}

\begin{namelist}
  \item[\cmd{X0_rotor} \emph{(required for coupled simulation)}]
    This is the $x$ coordinate of the hub center of rotation,
    in grid units.
  \item[\cmd{Y0_rotor} \emph{(required for coupled simulation)}]
    This is the $y$ coordinate of the hub center of rotation,
    in grid units.
  \item[\cmd{Z0_rotor} \emph{(required for coupled simulation)}]
    This is the $z$ coordinate of the hub center of rotation,
    in grid units.
  \item[\cmd{phi1}]
    This is the first Euler angle describing a rotation about the $x$ axis,
    in degrees. For a propeller oriented in the $x$-positive direction, this
    should be 0.
  \item[\cmd{phi2}]
    This is the second Euler angle describing a rotation about the $a_2$ axis,
    in degrees. For a propeller oriented in the $x$-positive direction, this
    should be $-90$.
  \item[\cmd{phi3}]
    This is the third Euler angle describing a rotation about the $b_3$ axis,
    in degrees. For a propeller oriented in the $x$-positive direction, this
    should be 0.
\end{namelist}

The Euler angles must be input correctly to obtain the correct orientation
of the source based actuator disk.
The following example illustrates how to determine these angles.
\Fig{f:rotor-angles} depicts the rotations 
\var{phi1} = $10$, \var{phi2} = $-15$, and \var{phi3} = $15$.
Initially, the thrust is assumed to be in the $z$ direction
and the disk is located in the $x-y$ plane.
The first rotation of \var{phi1} about the $x$ axis takes the $x-y-z$ system to 
the $a_1-a_2-a_3$ system shown in red.
The second rotation of \var{phi2} about the $a_2$ axis takes the $a_1-a_2-a_3$ 
system to the $b_1-b_2-b_3$ system shown in green.
The final rotation of \var{phi3} about the $b_3$ axis takes the $b_1-b_2-b_3$ 
system to the rotor reference system shown in blue.
The black circle represents the initial disk orientation and 
the blue circle represents the final disk orientation.
In general \var{phi1} and \var{phi2} are sufficient to define 
the thrust orientation.
The variable \var{phi3} only changes the location
of the zero azimuth angle definition for the rotor.

\begin{figure}[h]
  \centering
  \includegraphics[width=0.60\linewidth]{angles.jpg}
  \caption{Rotor disk Euler angles.}
  \label{f:rotor-angles}
\end{figure}

\subsubsection{Rotor Loading}

\begin{namelist}
  \item[\cmd{Vt_Ratio} \emph{(required for coupled simulation)}]
    This is the ratio of the tip speed to
    the velocity used for force normalization, 
    which is \var{mach_number} for compressible simulations.
    For \cmd{Load Type = 3}, \cmd{Load Type = 5}, and \cmd{Load Type = 6} 
    a negative value will reverse the rotation direction.
    The propeller convention is $J=\frac{V_a}{n D}$, 
    where $V_a$ is speed of advance (true airspeed), 
    $n$ is revolutions per unit time, and $D$ is diameter, i.e,
    \cmd{Vt_Ratio}$=\frac{\pi}{J}$.
  \item[\cmd{ThrustCoff}]
    This is the rotor thrust coefficient defined as,
    $C_T$  = Thrust / ($\pi \rho_{ref} R^2 ( \Omega_{Dim}R )^2$),
    when \var{Load Type}=1, \var{Load Type}=2, or \var{Load Type}=6.
    The blade element model does not trim to specified thrust coefficient.
    The  propeller convention is $K_T$ = Thrust / ($\rho_{ref} n^2 D^4$),
    where $n$ is revolutions per unit time, and $D$ is diameter, i.e,
    \cmd{ThrustCoff}$=\frac{4}{\pi^3}K_T$.
  \item[\cmd{TorqueCoff}]
    This is the rotor torque coefficient defined as,
    $C_Q$  = Torque / ($\pi \rho_{ref} R^3 ( \Omega_{Dim}R )^2$),
    when \var{Load Type}=6.
    The blade element model is not effected by specified torque coefficient.
    The  propeller convention is $K_Q$ = Torque / ($\rho_{ref} n^2 D^5$),
    where $n$ is revolutions per unit time, and $D$ is diameter, i.e,
    \cmd{ThrustCoff}$=\frac{8}{\pi^3}K_Q$.
\end{namelist}

\subsubsection{Blade Parameters}

\begin{namelist}
  \item[\cmd{psi0} \emph{(required for coupled simulation)}]
    This is the initial azimuthal position of blade one, in degrees; 
    the azimuth position is defined as zero when the blade is oriented
    along the $x$-axis with the tip at the most positive $x$ location.
  \item[\cmd{PitchHinge} \emph{(required for coupled simulation)}]
    This is the radial position of
    the blade pitch hinge normalized by tip radius.
  \item[\cmd{DirRot} \emph{(required for coupled simulation)}]
    This is the direction of rotor rotation.
    Zero is counter-clockwise rotation and one is clockwise rotation.
    This option only applies to coupled simulation, not actuator models.
  \item[\cmd{# Blades} \emph{(required for coupled simulation)}]
    This is the number of rotor blades.
    It is only used for \var{Load Type}=3 and overset rotor simulations.
  \item[\cmd{TipRadius} \emph{(required for coupled simulation)}]
    This is the radius of the blade, in grid units. 
  \item[\cmd{RootRadius} \emph{(required for coupled simulation)}]
    This is the radius of the blade root, in grid units.
    It accounts for the cutout region immediately surrounding the hub.
  \item[\cmd{BladeChord} \emph{(required for coupled simulation)}]
    This is the chord length of the blade, in grid units.
    It can only handle rectangular blade planforms and is only valid
    for \var{Load Type}=3.
  \item[\cmd{FlapHinge} \emph{(required for coupled simulation)}]
    This is the radial position of
    the blade flap hinge normalized by tip radius.
  \item[\cmd{LagHinge} \emph{(required for coupled simulation)}]
    This is the radial position of 
    the blade lag hinge normalized by tip radius.
    
\end{namelist}

\subsubsection{Blade Element Parameters for \protect\var{Load Type}=3}\label{s:blade-elem-param}
These inputs are used to set the blade element lift and drag curves according to:
\begin{equation}
C_L = \mathtt{LiftSlope}(\alpha-\alpha_{L=0})
\end{equation}
and
\begin{equation}
C_D = \mathtt{cd0} + \mathtt{cd1} \, \alpha + \mathtt{cd2} \, \alpha^2
\end{equation}

\begin{namelist}
  \item[\cmd{LiftSlope}]
    This is the lift curve slope per radian.
  \item[\cmd{alpha, L=0}]
    This is the zero lift angle of attack, in degrees.
  \item[\cmd{cd0}, \cmd{cd1}, and \cmd{cd2}]
    These are the quadratic drag polar coefficients; 
    where \cmd{cd1} is per radian and \cmd{cd2} is per radian squared.
  \item[\cmd{CL_max} and \cmd{CL_min}]
    These limiters to control the lift coefficient beyond the linear region.
  \item[\cmd{CD_max} and \cmd{CD_min}]
    These limiters to control the drag coefficient.
  \item[\cmd{Swirl}]
    \begin{description}
    \item[0]~ neglects the sources terms that create rotor swirl.
    \item[1]~ the swirl inducing source terms.
    \end{description}
\end{namelist}

\subsubsection{Pitch Control Parameters for \protect\var{Load Type}=3}\label{s:pitch-control}
These inputs are used to specify the pitch/flap controls according to:
\begin{equation}
\theta = \mathtt{Theta0} + \mathtt{ThetaTwist} \, (r/R)
       + \mathtt{Theta1c} \, \cos(\psi) + \mathtt{Theta1s}  \, \sin(\psi)
\end{equation}

\begin{namelist}
  \item[\cmd{Theta0}]
    This is the collective pitch defined at $r/R$=0, in degrees.
  \item[\cmd{ThetaTwist}]
    This is the total amount of linear blade twist
    from the origin to the blade tip, in degrees.
  \item[\cmd{Theta1s}]
    This is the longitudinal cyclic pitch input, in degrees.
  \item[\cmd{Theta1c}]
    This is the lateral cyclic pitch input, in degrees.
  \item[\cmd{Pitch-Flap}]
    Pitch-Flap coupling parameter [not implemented].
\end{namelist}

\subsubsection{Prescribed Flap Parameters}
These inputs are used to specify the flap harmonics according to:
\begin{align}
\beta = \mathtt{Beta0} + \mathtt{Beta1s} \, \sin(\psi) 
                       + \mathtt{Beta1c} \, \cos(\psi)  \nonumber \\
                + \mathtt{Beta2s} \, \sin(2\psi) 
                + \mathtt{Beta2c} \, \cos(2\psi)    \\
                + \mathtt{Beta3s} \, \sin(3\psi) 
                + \mathtt{Beta3c} \, \cos(3\psi)\nonumber
\end{align}

\begin{namelist}
  \item[\cmd{# FlapHar}]
    This is the number of flap harmonics to include.
    The valid input range is zero to three.
  \item[\cmd{Beta0}]
    This is the coning angle, in degrees.
  \item[\cmd{Beta1s} and \cmd{Beta1c}]
    This is the first flap harmonics, in degrees.
  \item[\cmd{Beta2s} and \cmd{Beta2c}]
    This is the second flap harmonics, in degrees.
  \item[\cmd{Beta3s} and \cmd{Beta3c}]
    This is the third flap harmonics, in degrees.
\end{namelist}

\subsubsection{Prescribed Lag Parameters}
These inputs are used to specify the lag harmonics according to:
\begin{align}
\delta = \mathtt{Delta0} + \mathtt{Delta1s} \, \sin(\psi) 
                       + \mathtt{Delta1c} \, \cos(\psi)  \nonumber \\
                + \mathtt{Delta2s} \, \sin(2\psi) 
                + \mathtt{Delta2c} \, \cos(2\psi)    \\
                + \mathtt{Delta3s} \, \sin(3\psi) 
                + \mathtt{Delta3c} \, \cos(3\psi)\nonumber
\end{align}

\begin{namelist}
  \item[\cmd{# LagHar}]
    This is the number of lag harmonics to include.
    The valid input range is zero to three.
  \item[\cmd{Delta0}]
    This is the coning angle, in degrees.
  \item[\cmd{Delta1s} and \cmd{Delta1c}]
    This is the first lag harmonics, in degrees.
  \item[\cmd{Delta2s} and \cmd{Delta2c}]
    This is the second lag harmonics, in degrees.
  \item[\cmd{Delta3s} and \cmd{Delta3c}]
    This is the third lag harmonics, in degrees.
\end{namelist}

\newpage\subsection{\protect\file{tdata}}\label{s:tdata}

This file defines the gas model when \var{eqn_type = 'generic'}.
A keyword is required on the first line of \cmd{tdata}.
Many of these models require additional information as
detailed in each keyword section.

Some keywords require a list the species.
For these keywords, additional groups of species can be
specified for boundary conditions after a blank line. 
If new species are introduced in subsequent instances
their mass fractions are automatically initialized to zero
at any previous inflow boundary.
All the species entries in this file are available
as reactants throughout the entire flow field.

\subsubsection{\protect\var{perfect_gas} Keyword}

A perfect gas can be modeled with the \var{perfect_gas} keyword.
The parameters can be explicitly defined in \file{tdata}
by the namelist \cmd{&species_properties}.
The namelist in \file{tdata}
has different variables than the \cmd{&species_properties}
in \file{species_thermo_data}.
Here is an example of the namelist with defaults
that are all given in SI units,
\begin{Verbatim}
perfect_gas
&species_properties
 gamma   =   1.4
 mol_wt  =  28.8
 suther1 =   0.1458205E-05
 suther2 = 110.333333
 prand   =   0.72
/
\end{Verbatim}
Where \var{gamma} is the gas specific heat ratio,
\var{mol_wt} is the gas molecular weight,
\var{prand} is the gas Prandtl number, and
\var{suther1} and \var{suther2} are
the first and second Sutherland's viscosity
coefficients, $s_{1}$ and $s_{2}$, in
\begin{equation}
  \label{eq:sutherland}
  \mu = s_{1} \frac{T^{3/2}}{T +s_2 }
\end{equation}
These defaults are used if the \cmd{&species_properties} namelist
or any of its variables are omitted.

\subsubsection{\protect\var{equilibrium_air_t} Keyword}

The \cmd{equilibrium_air_t} keyword
engages the Tannehill curve fits for thermodynamic and transport properties
of equilibrium air.\cite{Tannehill}
This keyword does not require additional lines.
\begin{Verbatim}
equilibrium_air_t
\end{Verbatim}

\subsubsection{\protect\var{equilibrium_air_r} Keyword}

The \cmd{equilibrium_air_r} keyword
engages the Tannehill curve fits for transport properties and
a table look-up for equilibrium gases \cite{TP2792},
This keyword does not require additional lines.
\begin{Verbatim}
equilibrium_air_r
\end{Verbatim}

\subsubsection{\protect\var{one} Keyword}

This one-temperature (1-T) model assumes that all the species are thermally
in equilibrium state;
the translational temperature $T$ and vibrational temperature $T_v$ are equal.
This is a mixture of thermally perfect gases and
multi-species transport.
In this example, only molecular oxygen and nitrogen are
present on the inflow boundary, 
but atomic nitrogen and oxygen and nitric oxide
may be produced elsewhere in the flow field due to chemical reactions.
The inflow boundary mass fraction of molecular oxygen and nitrogen
is given next to their symbols.
Mass fractions should sum to one.
\begin{Verbatim}
one
N2  .767
N
O2  .233
O
NO
\end{Verbatim}

\subsubsection{\protect\var{two} Keyword}

This two-temperature (2-T) model assumes that energy distribution
in the translational and rotational modes
of heavy particles (not electrons) are equilibrated
at translational temperature $T$ 
and all other energy modes (vibrational, electronic,
electron translational) are equilibrated
at vibrational temperature $T_v$.
In this example, the gas is assumed to be a mixture of
11 thermally perfect gases.
The inflow boundary mass fraction of molecular oxygen and nitrogen
is given next to their symbols. 
Mass fractions should sum to one.
Other products are the results of chemical reactions flow field.
\begin{Verbatim}
two
N2  .767
N
O2  .233
O
NO
O2+
O+
NO+
e-
\end{Verbatim}

\subsubsection{\protect\var{FEM} Keyword}

This Free-Energy Minimization (FEM) model
causes the species continuity equations to be replaced with
elemental continuity equations
and equilibrium relations for remaining species.
In this example, the gas is assumed to be a mixture of
11 thermally perfect gases.
The inflow boundary mass fraction of molecular oxygen and nitrogen
is given next to their symbols. 
Mass fractions should sum to one.
Other products are the results of chemical reactions flow field.
\begin{Verbatim}
FEM
N2  .767
N
O2  .233
O
NO
O2+
O+
NO+
e-
\end{Verbatim}

\subsection{\protect\file{species_thermo_data}}\label{s:thermodata}

The \file{species_thermo_data} file is the master file for species
thermodynamic data.
The majority of simulations do not require changes to this file.
Investigating other sources of thermodynamic data is the
only reason to edit this file.
If the file is not found in the local run directory,
it is assumed to be located in the
\path{[install-prefix]/share/physics_modules} directory.
See \sectionref{s:install-prefix} for \path{[install-prefix]}.

Each species record consists of the species name,
a \cmd{&species_properties} namelist,
the number of thermodynamic property curve fit ranges,
and the curve fit coefficients for each range. \cite{RP1311}
No blank line is allowed in this file. 
This \cmd{&species_properties} namelist
has different variables than the \cmd{&species_properties}
in \file{tdata}.
The elements of the \cmd{&species_properties} namelist are:
\begin{namelist}

  \item[\cmd{mol_wt}]
  
    This sets the molecular weight of the particle.
    It is always required.
  
  \item[\cmd{molecule = .false.}]
  
    This is denotes the the species a molecule
    (composed of more than one atom);

  \item[\cmd{ion = .false.}]
  
    This is denotes the the species a charged particle.
    Do not set it for neutrals and electrons.
    This will initializes electron-neutral energy exchange
    cross section and sum of the charges.
  
  \item[\cmd{charge = 0}]
  
    This is an integer number to determine number of positive charges
    in particle. It should only be used with \cmd{ion = .true.}
  
  \item[\cmd{elec_impct_ion = -1._dp}]
  
    This sets the energy for neutrals \cmd{ion=.false.}
    that is required to liberate an electron
    when the neutral impact a free electron, 
    in units of electron volts (eV).
  
  \item[\cmd{siga(:) = -1._dp}]
  
    This is an array of three real numbers, which correspond to
    the curve fit coefficients for electron-neutron energy exchange.
    The cross section is defined as
    \begin{equation}
      \sigma_{en}=a+bT+cT^2
    \end{equation}
    where $\sigma_{en}$ is the electron-neutron energy exchange
    collision cross section in $m^{2}$. The variables $a$, $b$, and $c$
    are the curve fit coefficients and $T$ is the gas 
    temperature.\cite{gnoffo;89tp, Gupta;1990}
    For example, \cmd{siga=7.5e-20, 0, 0}.

  \item[\cmd{disoc_ener = 0._dp}]

    This is the dissociation energy of molecule in electron volts (eV).
  
  \item[\cmd{alantel = 0._dp}]
  
    This is the Landau-Teller constant
    to compute vibrational relaxation time for 
    molecule.\cite{MillikanWhite, Ali;1986}.
    It is required when \cmd{molecule=.true.}.
  
  \item[\cmd{cprt0 = 0._dp}]
  
    This non-dimensional real number
    defines translational-rotational heat capacity.
    It is normalized by the gas constant and is equal to 
    \begin{equation}
      cprt() = \frac{f+2}{2} 
    \end{equation}
    where $f$ is the number of degrees of freedom in translation and rotation.
    The defaults for atoms and diatomic molecules
    are 2.5 and 3.5, respectively.

\end{namelist}

A portion of the \file{species_thermo_data} that provides
thermodynamic properties of carbon is shown below.
\begin{Verbatim}[numbers=right]
  C
  &species_properties
  molecule = .false.
  ion = .false.
  charge = 0
  elec_impct_ion = 11.264
  siga = 7.5e-20, 5.5e-24, -1.e-28
  mol_wt = 12.01070
  /
  3
   0.64950315E+03 -0.96490109E+00  0.25046755E+01 -0.12814480E-04
   0.19801337E-07 -0.16061440E-10  0.53144834E-14  0.00000000E+00
   0.85457631E+05  0.47479243E+01   200.000  1000.000
  -0.12891365E+06  0.17195286E+03  0.26460444E+01 -0.33530690E-03
   0.17420927E-06 -0.29028178E-10  0.16421824E-14  0.00000000E+00
   0.84105978E+05  0.41300474E+01  1000.000  6000.000
   0.44325280E+09 -0.28860184E+06  0.77371083E+02 -0.97152819E-02
   0.66495953E-06 -0.22300788E-10  0.28993887E-15  0.00000000E+00
   0.23552734E+07 -0.64051232E+03  6000.000 20000.000
\end{Verbatim}
The species name is defined in line \var{1}.
Between lines \var{2} and \var{9} species properties are defined.
Line \var{10} shows that there are three thermodynamic property
curve fits for temperature ranges of 200~K $<T<$ 1,000~K, 1,000~K $<T<$ 6,000~K,
and 6,000~K $<T<$ 20,000~K.
Each data range consists of 12 real numbers.
Four real numbers must be given on each line.
The first 10 real numbers are the thermodynamic curve fit coefficients,
and the last two real numbers identify the temperature range for
the given curve fit coefficients.
These coefficients are used
to calculate the following thermodynamic properties
\begin{equation}
  \label{eq:cp}
  c_p(T)/R = a_1T^{-2} + a_2T^{-1} + a_3+a_4T
         + a_5T^2 + a_6T^3+a_7T^4
\end{equation}
\begin{equation}
  \label{eq:enthalpy}
  h(T)/RT = -a_1T^{-2} + a_2T^{-1}ln~T + a_3+a_4\frac{T}{2}
       + a_5\frac{T^2}{3} + a_6\frac{T^3}{4}
       + a_7\frac{T^4}{5}+\frac{a_9}{T}
\end{equation}
\begin{equation}
  \label{eq:entropy}
  s(T)/R = -a_1\frac{T^{-2}}{2} - a_2T^{-1}+a_3ln~T
       + a_4T+a_5\frac{T^2}{2} + a_6\frac{T^3}{3}
       + a_7\frac{T^4}{4}+a_{10}
\end{equation}
where $T$ is the gas temperature, $R$ is the universal gas constant,
$c_p$, $h$, and $s$ are the species specific heat, enthalpy and entropy,
respectively,
and $a_i$ are the provided curve fit coefficients
in \file{species_thermo_data}.

The following corrections will be applied if the gas temperature
is out of the given range for the given curve fit coefficients:
\begin{equation}
  c_p(T) = c_p(T^*)
\end{equation}
\begin{equation}
  h(T) =h(T^*) + (T -T^*) c_p(T^*)
\end{equation}
\begin{equation}
  s(T) =s(T^*) + ln~\frac{T}{T^*} c_p(T^*)
\end{equation}
where
\begin{equation}
  T^*= \left\{
  \begin{array}{ll}
    T_{lower} &\mbox{for $T < T_{lower}$}\\
    T_{upper} &\mbox{for $T > T_{upper}$}
  \end{array}
  \right.
\end{equation} 

\subsection{\protect\file{kinetic_data}}\label{sec:kineticdata}

The \cmd{kinetic_data} file defines possible chemical reactions and is optional.
If the file is not found in the local run directory,
it is assumed to be located in the
\path{[install-prefix]/share/physics_modules} directory.
See \sectionref{s:install-prefix} for \path{[install-prefix]}.
Reactants and products can be any species defined in the
\file{species_thermo_data} described in \sectionref{s:thermodata}.
A sample entry looks like
\begin{Verbatim}[numbers=right]
  O2 + M    <=>   2O + M
  2.000e+21  -1.50  5.936e+04
  teff1 = 2
  exp1 = 0.7
  t_eff_min = 1000.
  t_eff_max = 50000.
  C = 5.0
  O = 5.0
  N = 5.0
  H = 5.0
  Si = 5.0
  e- = 0.
\end{Verbatim}
The first line specifies the reaction while
line~2 provides three coefficients of an Arrhenius-like equation,
\begin{equation}
  K_f = c_fT_{eff}^\eta e^{-\epsilon_0/kT_{eff}}
  \label{eq:Arrhenius}
\end{equation}
where $c_f$ is the pre-exponential factor, $\eta$ is the power of
temperature dependence on the pre-exponential factor,
$\epsilon_0$ is the Arrhenius activation energy, and
$k$ is the Boltzmann constant.
The arrowheads in line 1 signify the allowed directionality of the reaction.
The symbol \cmd{=>} denotes forward reaction only
while \cmd{<=>} denotes forward and backward rates are computed.
The coefficients in line~2 correspond to $c_f$, $\eta$, and $\epsilon_0/k$,
respectively.
For reactions with a generic collision partner, \var{M},
such as this one,
these coefficients correspond to Argon;
and other collision partners and their efficiencies (multipliers of $c_f$)
are specified on lines following line~5 and~6,
which give the valid temperature range for the reaction.
The effective temperature, $T_{eff}$, is defined
according to a given integer number in line 3.

\begin{namelist}

  \item[\cmd{teff1 = 1},\cmd{teff2 = 1}]
    
    This defines the formula to compute the effective temperature $T_{eff}$
    for the forward rate and backward rate, respectively.
    It is engaged for the case of thermal nonequilibrium.
    Options for \cmd{teff} are:
    \begin{enumlist}
      \item[1] $T_{eff} = T_{tr}$
      \item[2] $T_{eff} = T_{tr}^{exp1}T_v^{1-exp1}$
      \item[3] $T_{eff} = T_v$
    \end{enumlist}
    where $T_{tr}$ and $T_v$ are translational and vibrational temperatures,
    respectively.
    
  \item[\cmd{exp1 = 0.7}]

    This is the exponent used to define the effective temperature
    when \cmd{teff1= 2} (forward rate) or \cmd{teff2 = 2} (backward rate).
    
  \item[\cmd{rf_max = 1.e+20}]
 
    This is the upper limit on forward reaction rate
    in cgs units when \linebreak \cmd{augment_kinetics_limiting = .true.}
    For unlimited rates as function of temperature,
    see the output file \file{kinetic_diagnostics_output}.
        
  \item[\cmd{rf_min = 1.e-30}]
 
    This is the lower limit on forward reaction rate in
    cgs units when \linebreak \cmd{augment_kinetics_limiting= .true.}
    For unlimited rates as function of temperature,
    see the output file \file{kinetic_diagnostics_output}.
     
  \item[\cmd{rb_max = 1.e+30}]
 
    This is the upper limit on backward reaction rate
    in cgs units when \linebreak \cmd{augment_kinetics_limiting = .true.}
    For unlimited rates as function of temperature,
    see the output file \file{kinetic_diagnostics_output}.
     
  \item[\cmd{rb_min = 1.e-30}]
 
    This is the lower limit on backward reaction rate
    in cgs units when \linebreak \cmd{augment_kinetics_limiting = .true.}
    For unlimited rates as function of temperature,
    see the output file \file{kinetic_diagnostics_output}.

  \item[\cmd{t_eff_min = 1000.}]

    This is the minimum temperature for $T_{eff}$.
    This may circumvent stiff source terms when computing reaction rates.

  \item[\cmd{t_eff_max = 50000.}]
    The maximum temperature for $T_{eff}$.
    This may circumvent stiff source terms when computing reaction rates.
  
\end{namelist}
 
\subsection{\protect\file{species_transp_data}}\label{sec:transpdata}

The \file{species_transp_data} file contains
log-linear curve fit coefficients for species collision cross sections
that are defined based on temperature range of 2,000--4,000~K.\cite{Gupta;1990}
This temperature range spans boundary-layer temperatures for
typical hypersonic entry.
The curve fit for the given coefficients is poor
at temperatures below 1,000~K.
Higher order curve fit data is available in \file{species_transp_data_0}.
The file should not be changed by
the user unless there is a need to investigate other
sources of collision cross-section data. 
If the file is not found in the local run directory,
it is assumed to be located in the
\path{[install-prefix]/share/physics_modules} directory.
See \sectionref{s:install-prefix} for \path{[install-prefix]}.
An example of the entries in the file is
\begin{Verbatim}[numbers=right]
   Ar Ar
  -14.6017 -14.6502 -14.5501 -14.6028 ! trr132+kestin et al
  Ar+ Ar+
  -11.48 -12.08 -11.50 -12.10
  Ar N2
  -14.5995 -14.6475 -14.5480 -14.5981 ! kestin et al
  Ar CO
  -14.5975 -14.6455 -14.5459 -14.5964 ! kestin et al
\end{Verbatim}

\subsection{\protect\file{species_transp_data_0}}\label{sec:transpdata0}

The file \file{species_transp_data_0}
provides collision cross section
coefficients\cite{MichaelWright1,MichaelWright2} for
a higher order curve fit data
than those that are in the \file{species_transp_data}. 
The data in \file{species_transp_data_0} supersedes the
data in \file{species_transp_data}
The file should not be changed by the user unless
there is a need to investigate other sources of collision
cross-section data. 
If the file is not found in the local run directory,
it is assumed to be located in the
\path{[install-prefix]/share/physics_modules} directory.
See \sectionref{s:install-prefix} for \path{[install-prefix]}.
An example of the entries in the file is
\begin{Verbatim}
  O2 N      1 1 1      (c)
           -1.1453028E-03  1.2654140E-02 -2.2435218E-01  7.7201588E+01
           -1.0608832E-03  1.1782595E-02 -2.1246301E-01  8.4561598E+01
            1.4943783E-04 -2.0389247E-03  1.8536165E-02  1.0476552E+00

  NO N      1 1 1      (c)
           -1.5770918E-03  1.9578381E-02 -2.7873624E-01  9.9547944E+01
           -1.4719259E-03  1.8446968E-02 -2.6460411E-01  1.0911124E+02
            2.1014557E-04 -3.0420763E-03  2.5736958E-02  1.0359598E+00

  NO O      1 1 1      (c)
           -1.0885815E-03  1.1883688E-02 -2.1844909E-01  7.5512560E+01
           -1.0066279E-03  1.1029264E-02 -2.0671266E-01  8.2644384E+01
            1.4145458E-04 -1.9249271E-03  1.7785767E-02  1.0482162E+00
             
  END END   1 1 0
               0. 0. 0. 0.
               0. 0. 0. 0.
\end{Verbatim}

\subsection{\protect\file{hara_namelist_data}}\label{sec:harain}

The \file{hara_namelist_data} file controls the radiation models used by
the \textsc{Hara} radiation module.\cite{johnston:08jsr1,johnston:08jsr2}
It is optional for coupled radiation simulations.
If it is not present, then the code automatically chooses
the radiative mechanisms associated with species
present in the flowfield that have number densities
greater than 1000 particles/cm$^2$)
Other options are set to the defaults.
For users not experienced in shock-layer radiation,
the recommended default options should be used.
this \file{hara_namelist_data}
A default \file{hara_namelist_data} is available in
the \file{PHYSICS_MODULES} directory of the \FunThreeD distribution.

\paragraph{\bf{specifying radiation mechanisms for atomic species:}}
The treatment of radiation resulting from atomic lines, atomic bound-free,
and free-free photoionization (referred to here as
atomic continuum), and negative ion continuum 
is available for atomic carbon, hydrogen, oxygen, and nitrogen.
These mechanisms are specified through the following binary flags (on=1/off=0).
If any of these flags are not present in \file{hara_namelist_data},
then that flag is set to true only if the number density of the
associated atomic species is greater than 1000 particles/cm$^2$
somewhere in the flowfield.

\begin{namelist}
  \item[\cmd{treat_[?]_lines}]
  A binary flag to enable the treatment of atomic lines for species \cmd{[?]},
  where \cmd{[?]} can be c, h, n, and o, for atomic carbon, hydrogen, nitrogen and oxygen, respectively.

  \item[\cmd{treat_[?]_cont}]
 A binary flag to enable the treatment of atomic bound-free and free-free continuum for species \cmd{[?]},
  where \cmd{[?]} can be c, h, n, and o, for atomic carbon, hydrogen, nitrogen and oxygen, respectively.

  \item[\cmd{treat_[?]_other}]
 A binary flag to enable the treatment of the negative-ion continuum for species  \cmd{[?]},
  where \cmd{[?]} can be c, h, n, and o, for atomic carbon, hydrogen, nitrogen and oxygen, respectively.

\end{namelist}

\paragraph{\bf{specifying radiation mechanisms for molecular species:}}
The treatment of radiation resulting from numerous 
molecular band systems is available
through the following flags (0 = off, 1 = SRB, 2 = LBL).
The smeared rotational band (SRB) approach applies a simplified and
efficient treatment of each molecular band system, which is 
accurate for optically thin conditions. The line-by-line (LBL) approach
is a detailed but highly inefficient treatment of each molecular band system.
The LBL option is not recommended for coupled radiation-flowfield computations,
except for possibly the CO 4+ system, which may be optically thick for Mars entry conditions.
If any of these flags are not present in \file{hara_namelist_data},
then that flag is set to the SRB option only if the number density of the
associated molecular specie is greater than 1000 particles/cm$^2$
somewhere in the flowfield. Additional band systems are
listed in the following paragraph. These additional
band systems are generally considered negligible relative to those
listed in this section.
Therefore, for computational efficiency, they are not engaged by default. 
Definitions of each band system and the modeling data applied are
discussed in Refs. \cite{johnston:08jsr1, johnston:09jsr}.
\begin{namelist}
 \item[\cmd{treat_band_c2_swan}]
 A flag activating the C$_{\textrm{2}}$ Swan band system.
 
 \item[\cmd{treat_band_c2h}]
 A flag activating the C$_{\textrm{2}}$H band system.
   
 \item[\cmd{treat_band_c3}]
 A flag activating the C$_{\textrm{3}}$ and vacuum ultra-violet (VUV) band systems.
  
 \item[\cmd{treat_band_cn_red}]
 A flag activating the CN red band system.
 
 \item[\cmd{treat_band_cn_violet}]
 A flag activating the CN violet band system.
 
 \item[\cmd{treat_band_co4p}]
 A flag activating the CO 4+ band system.
 
 \item[\cmd{treat_band_co_bx}]
 A flag activating the CO B-X band system.
 
 \item[\cmd{treat_band_co_cx}]
 A flag activating the CO C-X band system.
 
 \item[\cmd{treat_band_co_ex}]
 A flag activating the CO E-X band system.

 \item[\cmd{treat_band_co_ir}]
 A flag activating the CO X-X band system.

 \item[\cmd{treat_band_h2_lyman}]
 A flag activating the H$_{\textrm{2}}$ Lyman band system.

 \item[\cmd{treat_band_h2_werner}]
 A flag activating the H$_{\textrm{2}}$ Werner band system.
 
 \item[\cmd{treat_band_n2fp}]
 A flag activating the N$_{\textrm{2}}$ 1+ band system.

 \item[\cmd{treat_band_n2sp}]
 A flag activating the N$_{\textrm{2}}$ 2+ band system.
 
 \item[\cmd{treat_band_n2pfn}]
 A flag activating the N$_{\textrm{2}}^+$ first-negative band system.
 
 \item[\cmd{treat_band_n2_bh1}]
 A flag activating the N$_{\textrm{2}}$ Birge-Hopfield I band system.
 
 \item[\cmd{treat_band_n2_bh2}]
 A flag activating the N$_{\textrm{2}}$ Birge-Hopfield II band system.
 
 \item[\cmd{treat_band_no_beta}]
 A flag activating the NO beta band system.
 
 \item[\cmd{treat_band_no_delta}]
 A flag activating the NO delta band system.
 
 \item[\cmd{treat_band_no_epsilon}]
 A flag activating the NO epsilon band system.
   
\end{namelist}

\paragraph{\bf{additional molecular band systems:}}
This paragraph lists molecular band systems available in addition
to those listed in the paragraph above. The band systems listed here are generally weak emitters and absorbers, and are therefore not engaged as a default. Therefore, for these band systems to be engaged, the following flags (0 = off, 1 = SRB, 2 = LBL) must be present in the \file{hara_namelist_data} file. The LBL treatment of these bands is not recommended. 

\begin{namelist}

 \item[\cmd{treat_band_c2_br}]
 A flag activating the C$_{\textrm{2}}$ Ballik-Ramsay band system.
 
 \item[\cmd{treat_band_c2_da}]
 A flag activating the C$_{\textrm{2}}$ Deslandres-d'Azambuja band system.
 
 \item[\cmd{treat_band_c2_fh}]
 A flag activating the C$_{\textrm{2}}$ Fox-Herzberg band system.
 
 \item[\cmd{treat_band_c2_mulliken}]
 A flag activating the C$_{\textrm{2}}$ Mulliken band system.
 
 \item[\cmd{treat_band_c2_philip}]
 A flag activating the C$_{\textrm{2}}$ Philips band system.
 
 \item[\cmd{treat_band_co3p}]
 A flag activating the CO 3+ band system.
 
 \item[\cmd{treat_band_co_angstrom}]
 A flag activating the CO angstrom band system.
 
 \item[\cmd{treat_band_co_asundi}]
 A flag activating the CO Asundi band system.
 
 \item[\cmd{treat_band_co_triplet}]
 A flag activating the CO triplet band system.
 
 \item[\cmd{treat_band_co2}]
 A flag activating the CO$_{\textrm{2}}$ band system.
A value of two activates an approximate nonequilibrium model for UV emission,
while a value of one assumes Boltzmann emission.
The LBL treatment of this band is not available.
 
 \item[\cmd{treat_band_n2_cy}]
 A flag activating the N$_{\textrm{2}}$ Carrol-Yoshino band system.
 
 \item[\cmd{treat_band_n2_wj}] 
 A flag activating the N$_{\textrm{2}}$ Worley-Jenkins band system.
 
 \item[\cmd{treat_band_n2_worley}]
 A flag activating the N$_{\textrm{2}}$ Worley band system.
 
 \item[\cmd{treat_band_no_gamma}]
 A flag activating the NO gamma band system.
 
 \item[\cmd{treat_band_no_betap}]
 A flag activating the NO beta-prime band system.
 
 \item[\cmd{treat_band_no_gammap}]
 A flag activating the NO gamma-prime band system.
 
 \item[\cmd{treat_band_o2_sr}]
 A flag activating the O$_{\textrm{2}}$ Schumann-Runge band system. 

 \item[\cmd{treat_[?]_photo_dis}]
   A binary flag activating the molecular photo-dissociation mechanism \cite{cunto} for \cmd{[?]} specie,
   where \cmd{[?]} can be O2 or N2. This mechanism is not technically a molecular band system.

 \item[\cmd{treat_[?]_photo_ion}]
   A binary flag activating the molecular photo-ionization mechanism \cite{cunto} for \cmd{[?]} specie,
   where \cmd{[?]} can be O2 or N2. This mechanism is not technically a molecular band system.

 \item[\cmd{treat_no_photo}]
   A binary flag activating the molecular photo-ionization mechanism \cite{cunto} for NO.
   
\end{namelist}

\paragraph{\bf{atomic line models:}}
%\label{sec:line_model}
There are various models available for atomic line radiation, one of which must be chosen
for each specie that engages atomic line radiation (as specified using \cmd{treat_[?]_lines}).
This choice of atomic line model is made using the following flags.  The listed defaults
are applied if the individual flag is not present in  \file{hara_namelist_data}, or if
 \file{hara_namelist_data} is not present in the working directory.
All model types in this category must be surrounded by a quotation marks.

\begin{namelist}
      
   \item[\cmd{c_atomic_line_model}, \cmd{h_atomic_line_model}]
   A character identifier for selecting the atomic line model
   for atomic carbon or hydrogen.
   Presently, the only available option is the model
   compiled in Ref.  \cite{johnston:09jsr}, which 
   is referred to here as the Complete Line Model (CLM).
   Default : \cmd{`clm'}

    \item[\cmd{n_atomic_line_model}, \cmd{o_atomic_line_model}]
   A character identifier for selecting the atomic line model 
   for atomic nitrogen or oxygen. The available models are compiled and
   compared in Ref. \cite{johnston:08jsr1}, which is referred to here
   as the Complete Line Model (CLM).
   Default~: \cmd{`clm'}
   Available models are:

    \begin{optionlist}
      \item[\var{`all multiplets'}]
      This model treats all lines as grouped multiplets. This 
      significantly reduces the number of lines treated 
      as well as the computational expense. However,
      this grouped multiplet approximation will lead to 
      errors for non-optically-thin conditions.
      
      \item[\var{`clm'}]
      This model, which stands for Complete Line Model, applies the individual treatment of
      strong atomic lines while applying multiplet averages for weak lines.  
      This is the recommended model.

    \end{optionlist}
      
\end{namelist}

\paragraph{\bf{electronic state population models:}}
These flags specify the model applied
for predicting the electronic state populations
of atoms and molecules. The listed defaults
are applied if the individual flag is not present in  \linebreak \file{hara_namelist_data}, or if
 \file{hara_namelist_data} is not present in the working directory.
All model types in this category must 
be surrounded by a quotation marks, e.g. `~'.

\subsubsection*{atomic electronic states}
The electronic state populations for atoms are required for
computing atomic line and photoionization emission and
absorption. The compilation and comparison of the available models
are presented in Ref. \cite{johnston:08jsr2}. 
\begin{namelist}

  \item[\cmd{c_electronic_state}, \cmd{h_electronic_state}]
  A character identifier for selecting the electronic state model 
  for atomic carbon and hydrogen. Available models are 
 (default : `boltzmann'):
    \begin{optionlist}
      \item[\var{`boltzmann'}] Applies Boltzmann population of electronic states.
      \item[\var{`Gally_1st_order_LTNE'}] Applies the Gally first-order local
      thermodynamic nonequilibrium method \cite{gally}, which 
      approximately accounts for the non-Boltzmann
      population of atomic states. 
    \end{optionlist}
  
  \item[\cmd{n_electronic_state}, \cmd{o_electronic_state}]
  A character identifier for selecting the electronic state model 
  for atomic nitrogen and oxygen. Available models are 
  (default : `CR'):
    \begin{optionlist}
        \item[\var{`boltzmann'}] Same as for \cmd{c_electronic_state}
      \item[\var{`Gally_1st_order_LTNE'}] Same as for \cmd{c_electronic_state}
      \item[\var{`CR'}] Applies the detailed collisional radiative (CR) model developed in 
                                   Ref. \cite{johnston:08jsr2}.
      \item[\var{`AARC'}] Applies the approximate atomic collisional radiative (AARC) model
                                  developed in Ref. \cite{johnston:08jsr2}. This model is essentially a
                                  curve-fit based approximation of the CR model, which allows for
                                  improved computational efficiency with a slight loss in accuracy.
    \end{optionlist}
  
\end{namelist}

\subsubsection*{molecular electronic states}
The electronic state populations for molecules are required for
computing molecular band emission and
absorption. The compilation and comparison of the available models
are presented in Refs. \cite{johnston:08jsr2, johnston:07jsr}.
\begin{namelist}
  \item[\cmd{molecular_electronic_state}]
  A character identifier for selecting molecular electronic state for all molecular band systems.
  Available models are (default : `CR'):
    \begin{optionlist}
      \item[\var{`boltzmann'}] Applies Boltzmann population of electronic states.
      \item[\var{`CR'}] Applies a detailed collisional radiative model considering
                                  both heavy-particle and electron impact transitions. Some
                                  molecular states are still assumed Boltzmann with this model
                                  because no data is presently available for the CR model.
    \end{optionlist}

\end{namelist}

\paragraph{\bf{other flags:}}
\begin{namelist}
  \item[\cmd{use_triangles}]
  A logical flag specifying whether optically-thin atomic lines are modeled as triangles
  to reduce computational time.
  This option has shown to result in a negligible loss of accuracy
  while greatly reducing the computational time, \cite{johnston:08jsr1}
  and is therefore recommended. Default : .true. Note: This flag is automatically set to \var{.true.}
  when \cmd{n_} or \cmd{o_atomic_line_model=`clm'}
  --- see {\bf atomic line models} earlier in this section.
  
     \item[\cmd{use_edge_shift}]
   A logical flag to engage the photoionization edge shift \cite{johnston:08jsr1}
   for atomic bound-free radiation.
    (on=1/off=0). Default : .true.   
\end{namelist}


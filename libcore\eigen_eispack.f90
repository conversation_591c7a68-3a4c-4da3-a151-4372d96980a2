module eigen_eispack

  use kinddefs, only : dp

  implicit none

  private

  public :: cg

contains

subroutine cg(nm,n,ar,ai,wr,wi,matz,zr,zi,fv1,fv2,fv3,ierr)

  integer,                   intent(in)    :: matz,n,nm
  integer,                   intent(out)   :: ierr
  real(dp), dimension(n),    intent(inout) :: fv1,fv2,fv3,wi,wr
  real(dp), dimension(nm,n), intent(inout) :: ai,ar,zi,zr

  integer :: is1,is2

  !     this subroutine calls the recommended sequence of
  !     subroutines from the eigensystem subroutine package (eispack)
  !     to find the eigenvalues and eigenvectors (if desired)
  !     of a complex general matrix.
  !     on input
  !
  !        nm  must be set to the row dimension of the two-dimensional
  !        array parameters as declared in the calling program
  !        dimension statement.
  !
  !        n  is the order of the matrix  a=(ar,ai).
  !
  !        ar  and  ai  contain the real and imaginary parts,
  !        respectively, of the complex general matrix.
  !
  !        matz  is an integer variable set equal to zero if
  !        only eigenvalues are desired.  otherwise it is set to
  !        any non-zero integer for both eigenvalues and eigenvectors.
  !
  !     on output
  !
  !        wr  and  wi  contain the real and imaginary parts,
  !        respectively, of the eigenvalues.
  !
  !        zr  and  zi  contain the real and imaginary parts,
  !        respectively, of the eigenvectors if matz is not zero.
  !
  !        ierr  is an integer output variable set equal to an error
  !           completion code described in the documentation for comqr
  !           and comqr2.  the normal completion code is zero.
  !
  !        fv1, fv2, and  fv3  are temporary storage arrays.
  !
  !     questions and comments should be directed to burton s. garbow,
  !     mathematics and computer science div, argonne national laboratory
  !
  !     this version dated august 1983.
  !
  !     ------------------------------------------------------------------
  !
  if (n > nm) then
    ierr = 10 * n
    return
  end if
  !
  call cbal(nm,n,ar,ai,is1,is2,fv1)
  call corth(nm,n,is1,is2,ar,ai,fv2,fv3)
  if (matz /= 0) then
    !     .......... find both eigenvalues and eigenvectors ..........
    call comqr2(nm,n,is1,is2,fv2,fv3,ar,ai,wr,wi,zr,zi,ierr)
    if (ierr == 0) then
      call cbabk2(nm,n,is1,is2,fv1,n,zr,zi)
    end if
  else
    !     .......... find eigenvalues only ..........
    call comqr(nm,n,is1,is2,ar,ai,wr,wi,ierr)
  end if
end subroutine cg

subroutine cbabk2(nm,n,low,igh,sfac,m,zr,zi)

  integer, intent(in) :: igh,low,m,n,nm
  real(dp), dimension(n), intent(in) :: sfac
  real(dp), dimension(nm,m), intent(inout) :: zi,zr

  integer :: i,ii,j,k
  real(dp) :: s

  !     this subroutine is a translation of the algol procedure
  !     cbabk2, which is a complex version of balbak,
  !     num. math. 13, 293-304(1969) by parlett and reinsch.
  !     handbook for auto. comp., vol.ii-linear algebra, 315-326(1971).
  !
  !     this subroutine forms the eigenvectors of a complex general
  !     matrix by back transforming those of the corresponding
  !     balanced matrix determined by  cbal.
  !
  !     on input
  !
  !        nm must be set to the row dimension of two-dimensional
  !          array parameters as declared in the calling program
  !          dimension statement.
  !
  !        n is the order of the matrix.
  !
  !        low and igh are integers determined by  cbal.
  !
  !        sfac contains information determining the permutations
  !          and scaling factors used by  cbal.
  !
  !        m is the number of eigenvectors to be back transformed.
  !
  !        zr and zi contain the real and imaginary parts,
  !          respectively, of the eigenvectors to be
  !          back transformed in their first m columns.
  !
  !     on output
  !
  !        zr and zi contain the real and imaginary parts,
  !          respectively, of the transformed eigenvectors
  !          in their first m columns.
  !
  !     questions and comments should be directed to burton s. garbow,
  !     mathematics and computer science div, argonne national laboratory
  !
  !     this version dated august 1983.
  !
  !     ------------------------------------------------------------------
  !
  if (m == 0) return
  if (igh /= low) then
    !
    do i = low,igh
      s = sfac(i)
      !     .......... left hand eigenvectors are back transformed
      !                if the foregoing statement is replaced by
      !                s=1._dp/sfac(i). ..........
      do j = 1,m
        zr(i,j) = zr(i,j) * s
        zi(i,j) = zi(i,j) * s
      end do
    !
    end do
  end if
  !     .......... for i=low-1 step -1 until 1,
  !                igh+1 step 1 until n do -- ..........
  do ii = 1,n
    i = ii
    if (i<low .or. i>igh) then
      if (i < low) then
        i = low - ii
      end if
      k = sfac(i)
      if (k /= i) then
        !
        do j = 1,m
          s = zr(i,j)
          zr(i,j) = zr(k,j)
          zr(k,j) = s
          s = zi(i,j)
          zi(i,j) = zi(k,j)
          zi(k,j) = s
        end do
      end if
    end if
  !
  end do
end subroutine cbabk2
subroutine cbal(nm,n,ar,ai,low,igh,sfac)

  integer, intent(in) :: n,nm
  integer, intent(out) :: igh,low
  real(dp), dimension(n), intent(inout) :: sfac
  real(dp), dimension(nm,n), intent(inout) :: ai,ar

  logical :: noconv
  integer :: i,iexc,j,jj,k,l,m
  real(dp) :: b2,c,f,g,r,radx,s

  !     this subroutine is a translation of the algol procedure
  !     cbalance, which is a complex version of balance,
  !     num. math. 13, 293-304(1969) by parlett and reinsch.
  !     handbook for auto. comp., vol.ii-linear algebra, 315-326(1971).
  !
  !     this subroutine balances a complex matrix and isolates
  !     eigenvalues whenever possible.
  !
  !     on input
  !
  !        nm must be set to the row dimension of two-dimensional
  !          array parameters as declared in the calling program
  !          dimension statement.
  !
  !        n is the order of the matrix.
  !
  !        ar and ai contain the real and imaginary parts,
  !          respectively, of the complex matrix to be balanced.
  !
  !     on output
  !
  !        ar and ai contain the real and imaginary parts,
  !          respectively, of the balanced matrix.
  !
  !        low and igh are two integers such that ar(i,j) and ai(i,j)
  !          are equal to zero if
  !           (1) i is greater than j and
  !           (2) j=1,...,low-1 or i=igh+1,...,n.
  !
  !        sfac contains information determining the
  !           permutations and scaling factors used.
  !
  !     suppose that the principal submatrix in rows low through igh
  !     has been balanced, that p(j) denotes the index interchanged
  !     with j during the permutation step, and that the elements
  !     of the diagonal matrix used are denoted by d(i,j).  then
  !        sfac(j) = p(j),    for j = 1,...,low-1
  !                 = d(j,j)       j = low,...,igh
  !                 = p(j)         j = igh+1,...,n.
  !     the order in which the interchanges are made is n to igh+1,
  !     then 1 to low-1.
  !
  !     note that 1 is returned for igh if igh is zero formally.
  !
  !     the algol procedure exc contained in cbalance appears in
  !     cbal  in line.  (note that the algol roles of identifiers
  !     k,l have been reversed.)
  !
  !     arithmetic is real throughout.
  !
  !     questions and comments should be directed to burton s. garbow,
  !     mathematics and computer science div, argonne national laboratory
  !
  !     this version dated august 1983.
  !
  !     ------------------------------------------------------------------
  !
  radx = 16._dp
  !
  b2 = radx * radx
  k = 1
  l = n
  OUTER_LOOP: do
    !     .......... for j=l step -1 until 1 do -- ..........
    LOOP: do jj = 1,l
      j = l + 1 - jj
      !
      do i = 1,l
        if (i /= j) then
          if (ar(j,i)/=0._dp .or. ai(j,i)/=0._dp) cycle LOOP
        end if
      end do
      goto 1000
    end do LOOP
    1100 do
           !
           LOOP1: do j = k,l
             !
             do i = k,l
               if (i /= j) then
                 if (ar(i,j)/=0._dp .or. ai(i,j)/=0._dp) cycle LOOP1
               end if
             end do
             goto 1200
           end do LOOP1
           exit OUTER_LOOP
           !
           1200 m = k
           iexc = 2
           !     .......... in-line procedure for row and
           !                column exchange ..........
           sfac(m) = j
           if (j /= m) then
             !
             do i = 1,l
               f = ar(i,j)
               ar(i,j) = ar(i,m)
               ar(i,m) = f
               f = ai(i,j)
               ai(i,j) = ai(i,m)
               ai(i,m) = f
             end do
             !
             do i = k,n
               f = ar(j,i)
               ar(j,i) = ar(m,i)
               ar(m,i) = f
               f = ai(j,i)
               ai(j,i) = ai(m,i)
               ai(m,i) = f
             end do
           end if
           !
           if (iexc == 2) then
             !     .......... search for columns isolating an eigenvalue
             !                and push them left ..........
             k = k + 1
           else
             goto 1300
           end if
         end do
    !
    1000 m = l
    iexc = 1
    sfac(m) = j
    if (j /= m) then
      !
      do i = 1,l
        f = ar(i,j)
        ar(i,j) = ar(i,m)
        ar(i,m) = f
        f = ai(i,j)
        ai(i,j) = ai(i,m)
        ai(i,m) = f
      end do
      !
      do i = k,n
        f = ar(j,i)
        ar(j,i) = ar(m,i)
        ar(m,i) = f
        f = ai(j,i)
        ai(j,i) = ai(m,i)
        ai(m,i) = f
      end do
    end if
    if (iexc == 2) then
      k = k + 1
      goto 1100
    end if
    !     .......... search for rows isolating an eigenvalue
    !                and push them down ..........
    1300 if (l == 1) goto 1400
    l = l - 1
  end do OUTER_LOOP
  !     .......... now balance the submatrix in rows k to l ..........
  do i = k,l
    sfac(i) = 1._dp
  end do
  do
    !     .......... iterative loop for norm reduction ..........
    noconv = .false.
    !
    do i = k,l
      c = 0._dp
      r = 0._dp
      !
      do j = k,l
        if (j /= i) then
          c = c + abs(ar(j,i)) + abs(ai(j,i))
          r = r + abs(ar(i,j)) + abs(ai(i,j))
        end if
      end do
      !     .......... guard against zero c or r due to underflow ..........
      if (c/=0._dp .and. r/=0._dp) then
        g = r / radx
        f = 1._dp
        s = c + r
        do while (c < g)
          f = f * radx
          c = c * b2
        end do
        g = r * radx
        do while (c >= g)
          f = f / radx
          c = c / b2
        end do
        !     .......... now balance ..........
        if ((c+r)/f < 0.95_dp*s) then
          g = 1._dp / f
          sfac(i) = sfac(i) * f
          noconv = .true.
          !
          do j = k,n
            ar(i,j) = ar(i,j) * g
            ai(i,j) = ai(i,j) * g
          end do
          !
          do j = 1,l
            ar(j,i) = ar(j,i) * f
            ai(j,i) = ai(j,i) * f
          end do
        end if
      end if
    !
    end do
    !
    if (.not. noconv) exit
  end do
  !
  1400 low = k
  igh = l
end subroutine cbal
subroutine comqr(nm,n,low,igh,hr,hi,wr,wi,ierr)

  integer,                   intent(in)    :: igh,low,n,nm
  integer,                   intent(out)   :: ierr
  real(dp), dimension(n),    intent(inout) :: wi,wr
  real(dp), dimension(nm,n), intent(inout) :: hi,hr

  integer :: en,enm1,i,itn,its,j,l,ll,lp1
  real(dp) :: norm,si,sr,ti,tr,tst1,tst2,xi,xr,yi,yr,zzi,zzr
  real(dp) :: xi_in, xr_in

  !     this subroutine is a translation of a unitary analogue of the
  !     algol procedure  comlr, num. math. 12, 369-376(1968) by martin
  !     and wilkinson.
  !     handbook for auto. comp., vol.ii-linear algebra, 396-403(1971).
  !     the unitary analogue substitutes the qr algorithm of francis
  !     (comp. jour. 4, 332-345(1962)) for the lr algorithm.
  !
  !     this subroutine finds the eigenvalues of a complex
  !     upper hessenberg matrix by the qr method.
  !
  !     on input
  !
  !        nm must be set to the row dimension of two-dimensional
  !          array parameters as declared in the calling program
  !          dimension statement.
  !
  !        n is the order of the matrix.
  !
  !        low and igh are integers determined by the balancing
  !          subroutine  cbal.  if  cbal  has not been used,
  !          set low=1, igh=n.
  !
  !        hr and hi contain the real and imaginary parts,
  !          respectively, of the complex upper hessenberg matrix.
  !          their lower triangles below the subdiagonal contain
  !          information about the unitary transformations used in
  !          the reduction by  corth, if performed.
  !
  !     on output
  !
  !        the upper hessenberg portions of hr and hi have been
  !          destroyed.  therefore, they must be saved before
  !          calling  comqr  if subsequent calculation of
  !          eigenvectors is to be performed.
  !
  !        wr and wi contain the real and imaginary parts,
  !          respectively, of the eigenvalues.  if an error
  !          exit is made, the eigenvalues should be correct
  !          for indices ierr+1,...,n.
  !
  !        ierr is set to
  !          zero       for normal return,
  !          j          if the limit of 30*n iterations is exhausted
  !                     while the j-th eigenvalue is being sought.
  !
  !     calls cdiv for complex division.
  !     calls csroot for complex square root.
  !     calls pythag for  sqrt(a*a + b*b) .
  !
  !     questions and comments should be directed to burton s. garbow,
  !     mathematics and computer science div, argonne national laboratory
  !
  !     this version dated august 1983.
  !
  !     ------------------------------------------------------------------
  !
  ierr = 0
  l = huge(1) !CCI indicates possible use before definition.
  if (low /= igh) then
    !     .......... create real subdiagonal elements ..........
    l = low + 1
    !
    do i = l,igh
      ll = MIN(i+1,igh)
      if (hi(i,i-1) /= 0._dp) then
        norm = pythag(hr(i,i-1),hi(i,i-1))
        yr = hr(i,i-1) / norm
        yi = hi(i,i-1) / norm
        hr(i,i-1) = norm
        hi(i,i-1) = 0._dp
        !
        do j = i,igh
          si = yr*hi(i,j) - yi*hr(i,j)
          hr(i,j) = yr*hr(i,j) + yi*hi(i,j)
          hi(i,j) = si
        end do
        !
        do j = low,ll
          si = yr*hi(j,i) + yi*hr(j,i)
          hr(j,i) = yr*hr(j,i) - yi*hi(j,i)
          hi(j,i) = si
        end do
      end if
    !
    end do
  end if
  !     .......... store roots isolated by cbal ..........
  do i = 1,n
    if (i<low .or. i>igh) then
      wr(i) = hr(i,i)
      wi(i) = hi(i,i)
    end if
  end do
  !
  en = igh
  tr = 0._dp
  ti = 0._dp
  itn = 30 * n
  OUTER_LOOP: do
    !     .......... search for next eigenvalue ..........
    if (en < low) return
    its = 0
    enm1 = en - 1
    do
      !     .......... look for single small sub-diagonal element
      !                for l=en step -1 until low e0 -- ..........
      do ll = low,en
        l = en + low - ll
        if (l == low) exit
        tst1 = abs(hr(l-1,l-1)) + abs(hi(l-1,l-1)) + abs(hr(l,l)) + &
               abs(hi(l,l))
        tst2 = tst1 + abs(hr(l,l-1))
        if (tst2 == tst1) exit
      end do
      !     .......... form shift ..........
      if (l == en) exit
      if (itn == 0) exit OUTER_LOOP
      if (its==10 .or. its==20) then
        !     .......... form exceptional shift ..........
        sr = abs(hr(en,enm1)) + abs(hr(enm1,en-2))
        si = 0._dp
      else
        sr = hr(en,en)
        si = hi(en,en)
        xr = hr(enm1,en) * hr(en,enm1)
        xi = hi(enm1,en) * hr(en,enm1)
        if (xr/=0._dp .or. xi/=0._dp) then
          yr = (hr(enm1,enm1)-sr) / 2._dp
          yi = (hi(enm1,enm1)-si) / 2._dp
          call csroot(yr**2-yi**2+xr,2._dp*yr*yi+xi,zzr,zzi)
          if (yr*zzr+yi*zzi < 0._dp) then
            zzr = -zzr
            zzi = -zzi
          end if
          xr_in = xr
          xi_in = xi
          call cdiv(xr_in,xi_in,yr+zzr,yi+zzi,xr,xi)
          sr = sr - xr
          si = si - xi
        end if
      end if
      !
      do i = low,en
        hr(i,i) = hr(i,i) - sr
        hi(i,i) = hi(i,i) - si
      end do
      !
      tr = tr + sr
      ti = ti + si
      its = its + 1
      itn = itn - 1
      !     .......... reduce to triangle (rows) ..........
      lp1 = l + 1
      !
      do i = lp1,en
        sr = hr(i,i-1)
        hr(i,i-1) = 0._dp
        norm = pythag(pythag(hr(i-1,i-1),hi(i-1,i-1)),sr)
        xr = hr(i-1,i-1) / norm
        wr(i-1) = xr
        xi = hi(i-1,i-1) / norm
        wi(i-1) = xi
        hr(i-1,i-1) = norm
        hi(i-1,i-1) = 0._dp
        hi(i,i-1) = sr / norm
        !
        do j = i,en
          yr = hr(i-1,j)
          yi = hi(i-1,j)
          zzr = hr(i,j)
          zzi = hi(i,j)
          hr(i-1,j) = xr*yr + xi*yi + hi(i,i-1)*zzr
          hi(i-1,j) = xr*yi - xi*yr + hi(i,i-1)*zzi
          hr(i,j) = xr*zzr - xi*zzi - hi(i,i-1)*yr
          hi(i,j) = xr*zzi + xi*zzr - hi(i,i-1)*yi
        end do
      !
      end do
      !
      si = hi(en,en)
      if (si /= 0._dp) then
        norm = pythag(hr(en,en),si)
        sr = hr(en,en) / norm
        si = si / norm
        hr(en,en) = norm
        hi(en,en) = 0._dp
      end if
      !     .......... inverse operation (columns) ..........
      do j = lp1,en
        xr = wr(j-1)
        xi = wi(j-1)
        !
        do i = l,j
          yr = hr(i,j-1)
          yi = 0._dp
          zzr = hr(i,j)
          zzi = hi(i,j)
          if (i /= j) then
            yi = hi(i,j-1)
            hi(i,j-1) = xr*yi + xi*yr + hi(j,j-1)*zzi
          end if
          hr(i,j-1) = xr*yr - xi*yi + hi(j,j-1)*zzr
          hr(i,j) = xr*zzr + xi*zzi - hi(j,j-1)*yr
          hi(i,j) = xr*zzi - xi*zzr - hi(j,j-1)*yi
        end do
      !
      end do
      !
      if (si /= 0._dp) then
        !
        do i = l,en
          yr = hr(i,en)
          yi = hi(i,en)
          hr(i,en) = sr*yr - si*yi
          hi(i,en) = sr*yi + si*yr
        end do
      end if
    end do
    !     .......... a root found ..........
    wr(en) = hr(en,en) + tr
    wi(en) = hi(en,en) + ti
    en = enm1
  end do OUTER_LOOP
  !     .......... set error -- all eigenvalues have not
  !                converged after 30*n iterations ..........
  ierr = en
end subroutine comqr
subroutine comqr2(nm,n,low,igh,ortr,orti,hr,hi,wr,wi,zr,zi,ierr)

  integer, intent(in) :: igh,low,n,nm
  integer, intent(out) :: ierr
  real(dp), dimension(igh), intent(inout) :: orti,ortr
  real(dp), dimension(n), intent(inout) :: wi,wr
  real(dp), dimension(nm,n), intent(inout) :: hi,hr,zi,zr

  integer :: en,enm1,i,iend,ii,ip1,itn,its,j,jj,k,l,ll,lp1,m,nn
  real(dp) :: norm,si,sr,ti,tr,tst1,tst2,xi,xr,yi,yr,zzi,zzr
  real(dp) :: xi_in, xr_in

  !     this subroutine is a translation of a unitary analogue of the
  !     algol procedure  comlr2, num. math. 16, 181-204(1970) by peters
  !     and wilkinson.
  !     handbook for auto. comp., vol.ii-linear algebra, 372-395(1971).
  !     the unitary analogue substitutes the qr algorithm of francis
  !     (comp. jour. 4, 332-345(1962)) for the lr algorithm.
  !
  !     this subroutine finds the eigenvalues and eigenvectors
  !     of a complex upper hessenberg matrix by the qr
  !     method.  the eigenvectors of a complex general matrix
  !     can also be found if  corth  has been used to reduce
  !     this general matrix to hessenberg form.
  !
  !     on input
  !
  !        nm must be set to the row dimension of two-dimensional
  !          array parameters as declared in the calling program
  !          dimension statement.
  !
  !        n is the order of the matrix.
  !
  !        low and igh are integers determined by the balancing
  !          subroutine  cbal.  if  cbal  has not been used,
  !          set low=1, igh=n.
  !
  !        ortr and orti contain information about the unitary trans-
  !          formations used in the reduction by  corth, if performed.
  !          only elements low through igh are used.  if the eigenvectors
  !          of the hessenberg matrix are desired, set ortr(j) and
  !          orti(j) to 0._dp for these elements.
  !
  !        hr and hi contain the real and imaginary parts,
  !          respectively, of the complex upper hessenberg matrix.
  !          their lower triangles below the subdiagonal contain further
  !          information about the transformations which were used in the
  !          reduction by  corth, if performed.  if the eigenvectors of
  !          the hessenberg matrix are desired, these elements may be
  !          arbitrary.
  !
  !     on output
  !
  !        ortr, orti, and the upper hessenberg portions of hr and hi
  !          have been destroyed.
  !
  !        wr and wi contain the real and imaginary parts,
  !          respectively, of the eigenvalues.  if an error
  !          exit is made, the eigenvalues should be correct
  !          for indices ierr+1,...,n.
  !
  !        zr and zi contain the real and imaginary parts,
  !          respectively, of the eigenvectors.  the eigenvectors
  !          are unnormalized.  if an error exit is made, none of
  !          the eigenvectors has been found.
  !
  !        ierr is set to
  !          zero       for normal return,
  !          j          if the limit of 30*n iterations is exhausted
  !                     while the j-th eigenvalue is being sought.
  !
  !     calls cdiv for complex division.
  !     calls csroot for complex square root.
  !     calls pythag for  sqrt(a*a + b*b) .
  !
  !     questions and comments should be directed to burton s. garbow,
  !     mathematics and computer science div, argonne national laboratory
  !
  !     this version dated october 1989.
  !
  !     ------------------------------------------------------------------
  !
  ierr = 0
  !     .......... initialize eigenvector matrix ..........
  l = huge(1) !CCI indicates possible use before definition.
  do j = 1,n
    !
    do i = 1,n
      zr(i,j) = 0._dp
      zi(i,j) = 0._dp
    end do
    zr(j,j) = 1._dp
  end do
  !     .......... form the matrix of accumulated transformations
  !                from the information left by corth ..........
  iend = igh - low - 1
  if (iend < 0) then
    goto 1000
  elseif (iend /= 0) then
    !     .......... for i=igh-1 step -1 until low+1 do -- ..........
    do ii = 1,iend
      i = igh - ii
      if (ortr(i)/=0._dp .or. orti(i)/=0._dp) then
        if (hr(i,i-1)/=0._dp .or. hi(i,i-1)/=0._dp) then
          !...... norm below is negative of h formed in corth..........
          norm = hr(i,i-1)*ortr(i) + hi(i,i-1)*orti(i)
          ip1 = i + 1
          !
          do k = ip1,igh
            ortr(k) = hr(k,i-1)
            orti(k) = hi(k,i-1)
          end do
          !
          do j = i,igh
            sr = 0._dp
            si = 0._dp
            !
            do k = i,igh
              sr = sr + ortr(k)*zr(k,j) + orti(k)*zi(k,j)
              si = si + ortr(k)*zi(k,j) - orti(k)*zr(k,j)
            end do
            !
            sr = sr / norm
            si = si / norm
            !
            do k = i,igh
              zr(k,j) = zr(k,j) + sr*ortr(k) - si*orti(k)
              zi(k,j) = zi(k,j) + sr*orti(k) + si*ortr(k)
            end do
          !
          end do
        end if
      end if
    !
    end do
  end if
  !     .......... create real subdiagonal elements ..........
  l = low + 1
  !
  do i = l,igh
    ll = MIN(i+1,igh)
    if (hi(i,i-1) /= 0._dp) then
      norm = pythag(hr(i,i-1),hi(i,i-1))
      yr = hr(i,i-1) / norm
      yi = hi(i,i-1) / norm
      hr(i,i-1) = norm
      hi(i,i-1) = 0._dp
      !
      do j = i,n
        si = yr*hi(i,j) - yi*hr(i,j)
        hr(i,j) = yr*hr(i,j) + yi*hi(i,j)
        hi(i,j) = si
      end do
      !
      do j = 1,ll
        si = yr*hi(j,i) + yi*hr(j,i)
        hr(j,i) = yr*hr(j,i) - yi*hi(j,i)
        hi(j,i) = si
      end do
      !
      do j = low,igh
        si = yr*zi(j,i) + yi*zr(j,i)
        zr(j,i) = yr*zr(j,i) - yi*zi(j,i)
        zi(j,i) = si
      end do
    end if
  !
  end do
  !     .......... store roots isolated by cbal ..........
  1000 do i = 1,n
         if (i<low .or. i>igh) then
           wr(i) = hr(i,i)
           wi(i) = hi(i,i)
         end if
       end do
  !
  en = igh
  tr = 0._dp
  ti = 0._dp
  itn = 30 * n
  !     .......... search for next eigenvalue ..........
  do while (en >= low)
    its = 0
    enm1 = en - 1
    do
      !     .......... look for single small sub-diagonal element
      !                for l=en step -1 until low do -- ..........
      do ll = low,en
        l = en + low - ll
        if (l == low) exit
        tst1 = abs(hr(l-1,l-1)) + abs(hi(l-1,l-1)) + abs(hr(l,l)) + &
               abs(hi(l,l))
        tst2 = tst1 + abs(hr(l,l-1))
        if (tst2 == tst1) exit
      end do
      !     .......... form shift ..........
      if (l == en) exit
      if (itn == 0) then
        !     .......... set error -- all eigenvalues have not
        !                converged after 30*n iterations ..........
        ierr = en
        return
      end if
      if (its==10 .or. its==20) then
        !     .......... form exceptional shift ..........
        sr = abs(hr(en,enm1)) + abs(hr(enm1,en-2))
        si = 0._dp
      else
        sr = hr(en,en)
        si = hi(en,en)
        xr = hr(enm1,en) * hr(en,enm1)
        xi = hi(enm1,en) * hr(en,enm1)
        if (xr/=0._dp .or. xi/=0._dp) then
          yr = (hr(enm1,enm1)-sr) / 2._dp
          yi = (hi(enm1,enm1)-si) / 2._dp
          call csroot(yr**2-yi**2+xr,2._dp*yr*yi+xi,zzr,zzi)
          if (yr*zzr+yi*zzi < 0._dp) then
            zzr = -zzr
            zzi = -zzi
          end if
          xr_in = xr
          xi_in = xi
          call cdiv(xr_in,xi_in,yr+zzr,yi+zzi,xr,xi)
          sr = sr - xr
          si = si - xi
        end if
      end if
      !
      do i = low,en
        hr(i,i) = hr(i,i) - sr
        hi(i,i) = hi(i,i) - si
      end do
      !
      tr = tr + sr
      ti = ti + si
      its = its + 1
      itn = itn - 1
      !     .......... reduce to triangle (rows) ..........
      lp1 = l + 1
      !
      do i = lp1,en
        sr = hr(i,i-1)
        hr(i,i-1) = 0._dp
        norm = pythag(pythag(hr(i-1,i-1),hi(i-1,i-1)),sr)
        xr = hr(i-1,i-1) / norm
        wr(i-1) = xr
        xi = hi(i-1,i-1) / norm
        wi(i-1) = xi
        hr(i-1,i-1) = norm
        hi(i-1,i-1) = 0._dp
        hi(i,i-1) = sr / norm
        !
        do j = i,n
          yr = hr(i-1,j)
          yi = hi(i-1,j)
          zzr = hr(i,j)
          zzi = hi(i,j)
          hr(i-1,j) = xr*yr + xi*yi + hi(i,i-1)*zzr
          hi(i-1,j) = xr*yi - xi*yr + hi(i,i-1)*zzi
          hr(i,j) = xr*zzr - xi*zzi - hi(i,i-1)*yr
          hi(i,j) = xr*zzi + xi*zzr - hi(i,i-1)*yi
        end do
      !
      end do
      !
      si = hi(en,en)
      if (si /= 0._dp) then
        norm = pythag(hr(en,en),si)
        sr = hr(en,en) / norm
        si = si / norm
        hr(en,en) = norm
        hi(en,en) = 0._dp
        if (en /= n) then
          ip1 = en + 1
          !
          do j = ip1,n
            yr = hr(en,j)
            yi = hi(en,j)
            hr(en,j) = sr*yr + si*yi
            hi(en,j) = sr*yi - si*yr
          end do
        end if
      end if
      !     .......... inverse operation (columns) ..........
      do j = lp1,en
        xr = wr(j-1)
        xi = wi(j-1)
        !
        do i = 1,j
          yr = hr(i,j-1)
          yi = 0._dp
          zzr = hr(i,j)
          zzi = hi(i,j)
          if (i /= j) then
            yi = hi(i,j-1)
            hi(i,j-1) = xr*yi + xi*yr + hi(j,j-1)*zzi
          end if
          hr(i,j-1) = xr*yr - xi*yi + hi(j,j-1)*zzr
          hr(i,j) = xr*zzr + xi*zzi - hi(j,j-1)*yr
          hi(i,j) = xr*zzi - xi*zzr - hi(j,j-1)*yi
        end do
        !
        do i = low,igh
          yr = zr(i,j-1)
          yi = zi(i,j-1)
          zzr = zr(i,j)
          zzi = zi(i,j)
          zr(i,j-1) = xr*yr - xi*yi + hi(j,j-1)*zzr
          zi(i,j-1) = xr*yi + xi*yr + hi(j,j-1)*zzi
          zr(i,j) = xr*zzr + xi*zzi - hi(j,j-1)*yr
          zi(i,j) = xr*zzi - xi*zzr - hi(j,j-1)*yi
        end do
      !
      end do
      !
      if (si /= 0._dp) then
        !
        do i = 1,en
          yr = hr(i,en)
          yi = hi(i,en)
          hr(i,en) = sr*yr - si*yi
          hi(i,en) = sr*yi + si*yr
        end do
        !
        do i = low,igh
          yr = zr(i,en)
          yi = zi(i,en)
          zr(i,en) = sr*yr - si*yi
          zi(i,en) = sr*yi + si*yr
        end do
      end if
    end do
    !     .......... a root found ..........
    hr(en,en) = hr(en,en) + tr
    wr(en) = hr(en,en)
    hi(en,en) = hi(en,en) + ti
    wi(en) = hi(en,en)
    en = enm1
  end do
  !     .......... all roots found.  backsubstitute to find
  !                vectors of upper triangular form ..........
  norm = 0._dp
  !
  do i = 1,n
    !
    do j = i,n
      tr = abs(hr(i,j)) + abs(hi(i,j))
      if (tr > norm) then
        norm = tr
      end if
    end do
  !
  end do
  !
  if (n==1 .or. norm==0._dp) return
  !     .......... for en=n step -1 until 2 do -- ..........
  do nn = 2,n
    en = n + 2 - nn
    xr = wr(en)
    xi = wi(en)
    hr(en,en) = 1._dp
    hi(en,en) = 0._dp
    enm1 = en - 1
    !     .......... for i=en-1 step -1 until 1 do -- ..........
    do ii = 1,enm1
      i = en - ii
      zzr = 0._dp
      zzi = 0._dp
      ip1 = i + 1
      !
      do j = ip1,en
        zzr = zzr + hr(i,j)*hr(j,en) - hi(i,j)*hi(j,en)
        zzi = zzi + hr(i,j)*hi(j,en) + hi(i,j)*hr(j,en)
      end do
      !
      yr = xr - wr(i)
      yi = xi - wi(i)
      if (yr==0._dp .and. yi==0._dp) then
        tst1 = norm
        yr = tst1
        do
          yr = 0.01_dp * yr
          tst2 = norm + yr
          if (tst2 <= tst1) exit
        end do
      end if
      call cdiv(zzr,zzi,yr,yi,hr(i,en),hi(i,en))
      !     .......... overflow control ..........
      tr = abs(hr(i,en)) + abs(hi(i,en))
      if (tr /= 0._dp) then
        tst1 = tr
        tst2 = tst1 + 1._dp/tst1
        if (tst2 <= tst1) then
          do j = i,en
            hr(j,en) = hr(j,en) / tr
            hi(j,en) = hi(j,en) / tr
          end do
        end if
      end if
    !
    end do
  !
  end do
  !     .......... end backsubstitution ..........
  !     .......... vectors of isolated roots ..........
  do i = 1,N
    if (i<low .or. i>igh) then
      !
      do j = I,n
        zr(i,j) = hr(i,j)
        zi(i,j) = hi(i,j)
      end do
    end if
  !
  end do
  !     .......... multiply by transformation matrix to give
  !                vectors of original full matrix.
  !                for j=n step -1 until low do -- ..........
  do jj = low,N
    j = n + low - jj
    m = MIN(j,igh)
    !
    do i = low,igh
      zzr = 0._dp
      zzi = 0._dp
      !
      do k = low,m
        zzr = zzr + zr(i,k)*hr(k,j) - zi(i,k)*hi(k,j)
        zzi = zzi + zr(i,k)*hi(k,j) + zi(i,k)*hr(k,j)
      end do
      !
      zr(i,j) = zzr
      zi(i,j) = zzi
    end do
  !
  end do
end subroutine comqr2
subroutine corth(nm,n,low,igh,ar,ai,ortr,orti)

  integer, intent(in) :: igh,low,n,nm
  real(dp), dimension(igh), intent(inout) :: orti,ortr
  real(dp), dimension(nm,n), intent(inout) :: ai,ar

  integer :: i,ii,j,jj,kp1,la,m,mp
  real(dp) :: f,fi,fr,g,h,sfac

  !     this subroutine is a translation of a complex analogue of
  !     the algol procedure orthes, num. math. 12, 349-368(1968)
  !     by martin and wilkinson.
  !     handbook for auto. comp., vol.ii-linear algebra, 339-358(1971).
  !
  !     given a complex general matrix, this subroutine
  !     reduces a submatrix situated in rows and columns
  !     low through igh to upper hessenberg form by
  !     unitary similarity transformations.
  !
  !     on input
  !
  !        nm must be set to the row dimension of two-dimensional
  !          array parameters as declared in the calling program
  !          dimension statement.
  !
  !        n is the order of the matrix.
  !
  !        low and igh are integers determined by the balancing
  !          subroutine  cbal.  if  cbal  has not been used,
  !          set low=1, igh=n.
  !
  !        ar and ai contain the real and imaginary parts,
  !          respectively, of the complex input matrix.
  !
  !     on output
  !
  !        ar and ai contain the real and imaginary parts,
  !          respectively, of the hessenberg matrix.  information
  !          about the unitary transformations used in the reduction
  !          is stored in the remaining triangles under the
  !          hessenberg matrix.
  !
  !        ortr and orti contain further information about the
  !          transformations.  only elements low through igh are used.
  !
  !     calls pythag for  sqrt(a*a + b*b) .
  !
  !     questions and comments should be directed to burton s. garbow,
  !     mathematics and computer science div, argonne national laboratory
  !
  !     this version dated august 1983.
  !
  !     ------------------------------------------------------------------
  !
  la = igh - 1
  kp1 = low + 1
  if (la < kp1) return
  !
  do m = kp1,la
    h = 0._dp
    ortr(m) = 0._dp
    orti(m) = 0._dp
    sfac = 0._dp
    !     .......... scale column (algol tol then not needed) ..........
    do i = m,igh
      sfac = sfac + abs(ar(i,m-1)) + abs(ai(i,m-1))
    end do
    !
    if (sfac /= 0._dp) then
      mp = m + igh
      !     .......... for i=igh step -1 until m do -- ..........
      do ii = m,igh
        i = mp - ii
        ortr(i) = ar(i,m-1) / sfac
        orti(i) = ai(i,m-1) / sfac
        h = h + ortr(i)*ortr(i) + orti(i)*orti(i)
      end do
      !
      g = sqrt(h)
      f = pythag(ortr(m),orti(m))
      if (f == 0._dp) then
        !
        ortr(m) = g
        ar(m,m-1) = sfac
      else
        h = h + f*g
        g = g / f
        ortr(m) = (1._dp+g) * ortr(m)
        orti(m) = (1._dp+g) * orti(m)
      end if
      !     .......... form (i-(u*ut)/h) * a ..........
      do j = m,n
        fr = 0._dp
        fi = 0._dp
        !     .......... for i=igh step -1 until m do -- ..........
        do ii = m,igh
          i = mp - ii
          fr = fr + ortr(i)*ar(i,j) + orti(i)*ai(i,j)
          fi = fi + ortr(i)*ai(i,j) - orti(i)*ar(i,j)
        end do
        !
        fr = fr / h
        fi = fi / h
        !
        do i = m,igh
          ar(i,j) = ar(i,j) - fr*ortr(i) + fi*orti(i)
          ai(i,j) = ai(i,j) - fr*orti(i) - fi*ortr(i)
        end do
      !
      end do
      !     .......... form (i-(u*ut)/h)*a*(i-(u*ut)/h) ..........
      do i = 1,igh
        fr = 0._dp
        fi = 0._dp
        !     .......... for j=igh step -1 until m do -- ..........
        do jj = m,igh
          j = mp - jj
          fr = fr + ortr(j)*ar(i,j) - orti(j)*ai(i,j)
          fi = fi + ortr(j)*ai(i,j) + orti(j)*ar(i,j)
        end do
        !
        fr = fr / h
        fi = fi / h
        !
        do j = m,igh
          ar(i,j) = ar(i,j) - fr*ortr(j) - fi*orti(j)
          ai(i,j) = ai(i,j) + fr*orti(j) - fi*ortr(j)
        end do
      !
      end do
      !
      ortr(m) = sfac * ortr(m)
      orti(m) = sfac * orti(m)
      ar(m,m-1) = -g*ar(m,m-1)
      ai(m,m-1) = -g*ai(m,m-1)
    end if
  end do
end subroutine corth
subroutine cdiv(ar,ai,br,bi,cr,ci)

  real(dp), intent(in) :: ai,ar,bi,br
  real(dp), intent(out) :: ci,cr

  real(dp) :: ais,ars,bis,brs,s

  s = abs(br) + abs(bi)
  ars = ar / s
  ais = ai / s
  brs = br / s
  bis = bi / s
  s = brs**2 + bis**2
  cr = (ars*brs+ais*bis) / s
  ci = (ais*brs-ars*bis) / s
end subroutine cdiv
subroutine csroot(xr,xi,yr,yi)

  real(dp), intent(in) :: xi,xr
  real(dp), intent(inout) :: yi,yr

  real(dp) :: s,ti,tr

  tr = xr
  ti = xi
  s = sqrt(0.5_dp*(pythag(tr,ti)+abs(tr)))
  if (tr >= 0._dp) then
    yr = s
  end if
  if (ti < 0._dp) then
    s = -s
  end if
  if (tr <= 0._dp) then
    yi = s
  end if
  if (tr < 0._dp) then
    yr = 0.5_dp * (ti/yi)
  end if
  if (tr > 0._dp) then
    yi = 0.5_dp * (ti/yr)
  end if
end subroutine csroot
function pythag(a,b)

  real(dp), intent(in) :: a,b

  real(dp) :: pythag

  real(dp) :: p,r,s,t,u

  p = MAX(abs(a),abs(b))
  if (p /= 0._dp) then
    r = (MIN(abs(a),abs(b))/p)**2
    do
      t = 4._dp + r
      if (t == 4._dp) exit
      s = r / t
      u = 1._dp + 2._dp*s
      p = u * p
      r = (s/u)**2 * r
    end do
  end if
  pythag = p
end function pythag

end module eigen_eispack


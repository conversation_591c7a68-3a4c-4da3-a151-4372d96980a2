!=============================== ELEMENT_EDGE_DA =============================80
!
! Dual area at an edge.
!
!=============================================================================80

  pure function element_edge_da( ie, local_e2n, r, rc )

    integer,                  intent(in)  :: ie
    integer,  dimension(:,:), intent(in)  :: local_e2n
    real(dp), dimension(:,:), intent(in)  :: r
    real(dp), dimension(3),   intent(in)  :: rc
    real(dp), dimension(3)                :: element_edge_da

    real(dp), dimension(3) :: rl, rr, rm

    integer :: n1, n2, n3, n4, n5, n6

    real(dp), parameter :: onethird = 1.0_dp/3.0_dp
    real(dp), parameter :: oneforth = 0.25_dp
    real(dp), parameter :: half     = 0.5_dp

  continue

    n1 = local_e2n(ie,1)
    n2 = local_e2n(ie,2)
    n3 = local_e2n(ie,3)
    n4 = local_e2n(ie,4)
    n5 = local_e2n(ie,5)
    n6 = local_e2n(ie,6)

    ! Left face centroid.

    if ( n4 == 0 ) then

      ! tria cell face.

      rl(1) = (r(n1,1) + r(n2,1) + r(n3,1))*onethird
      rl(2) = (r(n1,2) + r(n2,2) + r(n3,2))*onethird
      rl(3) = (r(n1,3) + r(n2,3) + r(n3,3))*onethird

    else

      ! quad cell face.

      rl(1) = (r(n1,1) + r(n2,1) + r(n3,1) + r(n4,1))*oneforth
      rl(2) = (r(n1,2) + r(n2,2) + r(n3,2) + r(n4,2))*oneforth
      rl(3) = (r(n1,3) + r(n2,3) + r(n3,3) + r(n4,3))*oneforth

    end if

    ! Right face centroid.

    if (n6 == 0) then

      ! tria cell face.

      rr(1) = (r(n1,1) + r(n2,1) + r(n5,1))*onethird
      rr(2) = (r(n1,2) + r(n2,2) + r(n5,2))*onethird
      rr(3) = (r(n1,3) + r(n2,3) + r(n5,3))*onethird

    else

      ! quad cell face.

      rr(1) = (r(n1,1) + r(n2,1) + r(n5,1) + r(n6,1))*oneforth
      rr(2) = (r(n1,2) + r(n2,2) + r(n5,2) + r(n6,2))*oneforth
      rr(3) = (r(n1,3) + r(n2,3) + r(n5,3) + r(n6,3))*oneforth

    end if

    ! Edge midpoint.

    rm(1) = (r(n1,1) + r(n2,1))*half
    rm(2) = (r(n1,2) + r(n2,2))*half
    rm(3) = (r(n1,3) + r(n2,3))*half

! contributions to dual normals from the two triangles
! that form part of the dual-cell surface

! area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

! xm = rm(1) ; ym = rm(2) ; zm = rm(3)
! xc = rc(1) ; yc = rc(2) ; zc = rc(3)
! xl = rl(1) ; yl = rl(2) ; zl = rl(3)
! xr = rr(1) ; yr = rr(2) ; zr = rr(3)

! element_edge_da(1) = (yc-ym)*(zl-zr) - (zc-zm)*(yl-yr)
! element_edge_da(2) = (zc-zm)*(xl-xr) - (xc-xm)*(zl-zr)
! element_edge_da(3) = (xc-xm)*(yl-yr) - (yc-ym)*(xl-xr)

! element_edge_da(:) = element_edge_da(:)*0.5_dp

  element_edge_da(1) = ((rc(2)-rm(2))*(rl(3)-rr(3)) -                          &
                        (rc(3)-rm(3))*(rl(2)-rr(2)))*half
  element_edge_da(2) = ((rc(3)-rm(3))*(rl(1)-rr(1)) -                          &
                        (rc(1)-rm(1))*(rl(3)-rr(3)))*half
  element_edge_da(3) = ((rc(1)-rm(1))*(rl(2)-rr(2)) -                          &
                        (rc(2)-rm(2))*(rl(1)-rr(1)))*half


end function element_edge_da

! derivative derived type (ddt)

module ddt

  use kinddefs, only : dp

  implicit none

  private

  integer, save :: nsave ! number of derivatives in ddtn type

  public :: ddtn
  type ddtn
    real(dp)                 :: f ! function
    integer                  :: n ! scalar less than or equal to 100
    real(dp), dimension(100) :: d ! the derivatives of f  FIXME: magic number
!   real(dp), pointer, dimension(:) :: d ! the derivatives of f
  end type ddtn

  public :: ddt2
  type ddt2
    real(dp)               :: f ! function
    real(dp), dimension(2) :: d ! the derivatives of f
  end type ddt2

  public :: ddt3
  type ddt3
    real(dp)               :: f ! function
    real(dp), dimension(3) :: d ! the derivatives of f
  end type ddt3

  public :: ddt4
  type ddt4
    real(dp)               :: f ! function
    real(dp), dimension(4) :: d ! the derivatives of f
  end type ddt4

  public :: ddt5
  type ddt5
    real(dp)               :: f ! function
    real(dp), dimension(5) :: d ! the derivatives of f
  end type ddt5

  public :: ddt7
  type ddt7
    real(dp)               :: f ! function
    real(dp), dimension(7) :: d ! the derivatives of f
  end type ddt7

  public :: ddtn_new
  public :: ddt2_new
  public :: ddt3_new
  public :: ddt4_new
  public :: ddt5_new
  public :: ddt7_new
  public :: ddtn_identity
  public :: ddt2_identity
  public :: ddt3_identity
  public :: ddt4_identity
  public :: ddt5_identity
  public :: ddt7_identity

  public :: ddt_jacobian
  interface ddt_jacobian
    module procedure ddtn_jacobian
    module procedure ddt2_jacobian
    module procedure ddt3_jacobian
  end interface

  public :: assignment(=)
  interface assignment(=)
    module procedure ddtn_assign_dr
    module procedure ddtn_assign_rd
    module procedure ddt2_assign_dr
    module procedure ddt2_assign_rd
    module procedure ddt3_assign_dr
    module procedure ddt3_assign_rd
    module procedure ddt4_assign_dr
    module procedure ddt4_assign_rd
    module procedure ddt5_assign_dr
    module procedure ddt5_assign_rd
    module procedure ddt7_assign_dr
    module procedure ddt7_assign_rd
  end interface

  public :: operator(+)
  interface operator(+)
    module procedure ddtn_plus
    module procedure ddtn_plus_dr
    module procedure ddtn_plus_rd
    module procedure ddt2_plus
    module procedure ddt2_plus_dr
    module procedure ddt2_plus_rd
    module procedure ddt3_plus
    module procedure ddt3_plus_dr
    module procedure ddt3_plus_rd
    module procedure ddt4_plus
    module procedure ddt4_plus_dr
    module procedure ddt4_plus_rd
    module procedure ddt5_plus
    module procedure ddt5_plus_dr
    module procedure ddt5_plus_rd
    module procedure ddt7_plus
    module procedure ddt7_plus_dr
    module procedure ddt7_plus_rd
  end interface

  public :: operator(-)
  interface operator(-)
    module procedure ddtn_minus
    module procedure ddtn_minus_rd
    module procedure ddtn_minus_dr
    module procedure ddtn_minus_unary
    module procedure ddt2_minus
    module procedure ddt2_minus_rd
    module procedure ddt2_minus_dr
    module procedure ddt2_minus_unary
    module procedure ddt3_minus
    module procedure ddt3_minus_rd
    module procedure ddt3_minus_dr
    module procedure ddt3_minus_unary
    module procedure ddt4_minus
    module procedure ddt4_minus_rd
    module procedure ddt4_minus_dr
    module procedure ddt4_minus_unary
    module procedure ddt5_minus
    module procedure ddt5_minus_rd
    module procedure ddt5_minus_dr
    module procedure ddt5_minus_unary
    module procedure ddt7_minus
    module procedure ddt7_minus_rd
    module procedure ddt7_minus_dr
    module procedure ddt7_minus_unary
  end interface

  public :: operator(*)
  interface operator(*)
    module procedure ddtn_multiply
    module procedure ddtn_multiply_dr
    module procedure ddtn_multiply_rd
    module procedure ddt2_multiply
    module procedure ddt2_multiply_dr
    module procedure ddt2_multiply_rd
    module procedure ddt3_multiply
    module procedure ddt3_multiply_dr
    module procedure ddt3_multiply_rd
    module procedure ddt4_multiply
    module procedure ddt4_multiply_dr
    module procedure ddt4_multiply_rd
    module procedure ddt5_multiply
    module procedure ddt5_multiply_dr
    module procedure ddt5_multiply_rd
    module procedure ddt7_multiply
    module procedure ddt7_multiply_dr
    module procedure ddt7_multiply_rd
  end interface

  public :: operator(/)
  interface operator(/)
    module procedure ddtn_divide
    module procedure ddtn_divide_dr
    module procedure ddtn_divide_rd
    module procedure ddt2_divide
    module procedure ddt2_divide_dr
    module procedure ddt2_divide_rd
    module procedure ddt3_divide
    module procedure ddt3_divide_dr
    module procedure ddt3_divide_rd
    module procedure ddt4_divide
    module procedure ddt4_divide_dr
    module procedure ddt4_divide_rd
    module procedure ddt5_divide
    module procedure ddt5_divide_dr
    module procedure ddt5_divide_rd
    module procedure ddt7_divide
    module procedure ddt7_divide_dr
    module procedure ddt7_divide_rd
  end interface

  public :: operator(**)
  interface operator(**)
    module procedure ddtn_power
    module procedure ddtn_power_dr
    module procedure ddtn_power_di
    module procedure ddt2_power
    module procedure ddt2_power_dr
    module procedure ddt2_power_di
    module procedure ddt3_power
    module procedure ddt3_power_dr
    module procedure ddt3_power_di
    module procedure ddt4_power
    module procedure ddt4_power_dr
    module procedure ddt4_power_di
    module procedure ddt5_power
    module procedure ddt5_power_dr
    module procedure ddt5_power_di
    module procedure ddt7_power
    module procedure ddt7_power_dr
    module procedure ddt7_power_di
  end interface

  public :: operator(>)
  interface operator(>)
    module procedure ddtn_greater_than
    module procedure ddtn_greater_than_dr
    module procedure ddtn_greater_than_rd
    module procedure ddt2_greater_than
    module procedure ddt2_greater_than_dr
    module procedure ddt2_greater_than_rd
    module procedure ddt3_greater_than
    module procedure ddt3_greater_than_dr
    module procedure ddt3_greater_than_rd
    module procedure ddt4_greater_than
    module procedure ddt4_greater_than_dr
    module procedure ddt4_greater_than_rd
    module procedure ddt5_greater_than
    module procedure ddt5_greater_than_dr
    module procedure ddt5_greater_than_rd
    module procedure ddt7_greater_than
    module procedure ddt7_greater_than_dr
    module procedure ddt7_greater_than_rd
  end interface

  public :: operator(>=)
  interface operator(>=)
    module procedure ddtn_greater_than_equal
    module procedure ddtn_greater_than_equal_dr
    module procedure ddt5_greater_than_equal
    module procedure ddt5_greater_than_equal_dr
    module procedure ddt7_greater_than_equal
    module procedure ddt7_greater_than_equal_dr
  end interface

  public :: operator(<)
  interface operator(<)
    module procedure ddtn_less_than
    module procedure ddtn_less_than_dr
    module procedure ddtn_less_than_rd
    module procedure ddt2_less_than
    module procedure ddt2_less_than_dr
    module procedure ddt2_less_than_rd
    module procedure ddt3_less_than
    module procedure ddt3_less_than_dr
    module procedure ddt3_less_than_rd
    module procedure ddt4_less_than
    module procedure ddt4_less_than_dr
    module procedure ddt4_less_than_rd
    module procedure ddt5_less_than
    module procedure ddt5_less_than_dr
    module procedure ddt5_less_than_rd
    module procedure ddt7_less_than
    module procedure ddt7_less_than_dr
    module procedure ddt7_less_than_rd
  end interface

  public :: operator(<=)
  interface operator(<=)
    module procedure ddtn_less_than_equal
    module procedure ddtn_less_than_equal_dr
    module procedure ddt5_less_than_equal
    module procedure ddt5_less_than_equal_dr
  end interface

  public :: operator(==)
  interface operator(==)
    module procedure ddtn_equal
    module procedure ddtn_equal_dr
    module procedure ddt5_equal
    module procedure ddt5_equal_dr
  end interface

  public :: log
  interface log
    module procedure ddtn_log
    module procedure ddt2_log
    module procedure ddt3_log
    module procedure ddt5_log
    module procedure ddt7_log
  end interface

  public :: ddt_exp
  interface ddt_exp
    module procedure ddtn_exp
    module procedure ddt2_exp
    module procedure ddt3_exp
    module procedure ddt4_exp
    module procedure ddt5_exp
    module procedure ddt7_exp
  end interface

  public :: ddt_tanh
  interface ddt_tanh
    module procedure ddtn_tanh
    module procedure ddt5_tanh
    module procedure ddt7_tanh
  end interface

  public :: ddt_sin
  interface ddt_sin
    module procedure ddtn_sin
    module procedure ddt5_sin
  end interface

  public :: ddt_asin
  interface ddt_asin
    module procedure ddt5_asin
    module procedure ddt7_asin
  end interface

  public :: ddt_cos
  interface ddt_cos
    module procedure ddtn_cos
    module procedure ddt5_cos
    module procedure ddt7_cos
  end interface

  public :: ddt_acos
  interface ddt_acos
    module procedure ddt7_acos
  end interface

  public :: ddt_sqrt
  interface ddt_sqrt
    module procedure ddtn_sqrt
    module procedure ddt2_sqrt
    module procedure ddt3_sqrt
    module procedure ddt4_sqrt
    module procedure ddt5_sqrt
    module procedure ddt7_sqrt
  end interface

  public :: ddt_max
  interface ddt_max
    module procedure ddtn_max
    module procedure ddtn_max_rd
    module procedure ddtn_max_dr
    module procedure ddt2_max
    module procedure ddt2_max_rd
    module procedure ddt2_max_dr
    module procedure ddt4_max
    module procedure ddt4_max_rd
    module procedure ddt4_max_dr
    module procedure ddt5_max
    module procedure ddt5_max_rd
    module procedure ddt5_max_dr
    module procedure ddt7_max
    module procedure ddt7_max_rd
    module procedure ddt7_max_dr
  end interface

  public :: ddt_min
  interface ddt_min
    module procedure ddtn_min
    module procedure ddtn_min_rd
    module procedure ddtn_min_dr
    module procedure ddt2_min
    module procedure ddt2_min_rd
    module procedure ddt2_min_dr
    module procedure ddt4_min
    module procedure ddt4_min_rd
    module procedure ddt4_min_dr
    module procedure ddt5_min
    module procedure ddt5_min_rd
    module procedure ddt5_min_dr
    module procedure ddt7_min
    module procedure ddt7_min_rd
    module procedure ddt7_min_dr
  end interface

  public :: ddt_abs
  interface ddt_abs
    module procedure ddtn_abs
    module procedure ddt2_abs
    module procedure ddt3_abs
    module procedure ddt4_abs
    module procedure ddt5_abs
    module procedure ddt7_abs
  end interface

  public :: ddt_sign
  interface ddt_sign
    module procedure ddtn_sign_rd
    module procedure ddt5_sign_rd
    module procedure ddt7_sign
    module procedure ddt7_sign_rd
  end interface

  public :: vdot_ddt
  interface vdot_ddt
    module procedure ddtn_vdot
    module procedure ddtn_vdot_dr
    module procedure ddt5_vdot
    module procedure ddt5_vdot_dr
    module procedure ddt7_vdot
    module procedure ddt7_vdot_dr
  end interface

  public :: ddt_matmul
  interface ddt_matmul
    module procedure ddt5_matmul
  end interface

  real(dp), parameter :: ddt_sqrt_floor = epsilon(1.0_dp)

contains

!================================= ddtn_new ==================================80

  function ddtn_new(n, f, d)
    integer,     intent(in)           :: n
    real(dp),    intent(in), optional :: f
    real(dp),    intent(in), dimension(n), optional :: d
    type(ddtn) :: ddtn_new

    real(dp), parameter    :: zero = 0.0_dp

    continue

    ddtn_new%n = n
    ddtn_new%f = zero
    ddtn_new%d(1:n) = zero
    nsave = n

    if (present(f)) ddtn_new%f = f
    if (present(d)) ddtn_new%d(1:n) = d(1:n)

  end function ddtn_new

!================================= ddt2_new ==================================80

  pure elemental function ddt2_new(f, d1, d2)
    real(dp),    intent(in), optional :: f, d1, d2
    type(ddt2) :: ddt2_new

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if (present(f)) then
      ddt2_new%f = f
    else
      ddt2_new%f = zero
    endif

    if (present(d1)) then
      ddt2_new%d(1) = d1
    else
      ddt2_new%d(1) = zero
    endif

    if (present(d2)) then
      ddt2_new%d(2) = d2
    else
      ddt2_new%d(2) = zero
    endif

  end function ddt2_new

!================================= ddt3_new ==================================80

  pure elemental function ddt3_new(f, d1, d2, d3)
    real(dp),    intent(in), optional :: f, d1, d2, d3
    type(ddt3) :: ddt3_new

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if (present(f)) then
      ddt3_new%f = f
    else
      ddt3_new%f = zero
    endif

    if (present(d1)) then
      ddt3_new%d(1) = d1
    else
      ddt3_new%d(1) = zero
    endif

    if (present(d2)) then
      ddt3_new%d(2) = d2
    else
      ddt3_new%d(2) = zero
    endif

    if (present(d3)) then
      ddt3_new%d(3) = d3
    else
      ddt3_new%d(3) = zero
    endif

  end function ddt3_new

!================================= ddt4_new ==================================80

  pure elemental function ddt4_new(f, d1, d2, d3, d4)
    real(dp),    intent(in), optional :: f, d1, d2, d3, d4
    type(ddt4) :: ddt4_new

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if (present(f)) then
      ddt4_new%f = f
    else
      ddt4_new%f = zero
    endif

    if (present(d1)) then
      ddt4_new%d(1) = d1
    else
      ddt4_new%d(1) = zero
    endif

    if (present(d2)) then
      ddt4_new%d(2) = d2
    else
      ddt4_new%d(2) = zero
    endif

    if (present(d3)) then
      ddt4_new%d(3) = d3
    else
      ddt4_new%d(3) = zero
    endif

    if (present(d4)) then
      ddt4_new%d(4) = d4
    else
      ddt4_new%d(4) = zero
    endif

  end function ddt4_new

!================================= ddt5_new ==================================80

  pure elemental function ddt5_new(f, d1, d2, d3, d4, d5)
    real(dp),    intent(in), optional :: f, d1, d2, d3, d4, d5
    type(ddt5) :: ddt5_new

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if (present(f)) then
      ddt5_new%f = f
    else
      ddt5_new%f = zero
    endif

    if (present(d1)) then
      ddt5_new%d(1) = d1
    else
      ddt5_new%d(1) = zero
    endif

    if (present(d2)) then
      ddt5_new%d(2) = d2
    else
      ddt5_new%d(2) = zero
    endif

    if (present(d3)) then
      ddt5_new%d(3) = d3
    else
      ddt5_new%d(3) = zero
    endif

    if (present(d4)) then
      ddt5_new%d(4) = d4
    else
      ddt5_new%d(4) = zero
    endif

    if (present(d5)) then
      ddt5_new%d(5) = d5
    else
      ddt5_new%d(5) = zero
    endif

  end function ddt5_new

!================================= ddt7_new ==================================80

  pure elemental function ddt7_new(f, d1, d2, d3, d4, d5, d6, d7)
    real(dp),    intent(in), optional :: f, d1, d2, d3, d4, d5, d6, d7
    type(ddt7) :: ddt7_new

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if (present(f)) then
      ddt7_new%f = f
    else
      ddt7_new%f = zero
    endif

    if (present(d1)) then
      ddt7_new%d(1) = d1
    else
      ddt7_new%d(1) = zero
    endif

    if (present(d2)) then
      ddt7_new%d(2) = d2
    else
      ddt7_new%d(2) = zero
    endif

    if (present(d3)) then
      ddt7_new%d(3) = d3
    else
      ddt7_new%d(3) = zero
    endif

    if (present(d4)) then
      ddt7_new%d(4) = d4
    else
      ddt7_new%d(4) = zero
    endif

    if (present(d5)) then
      ddt7_new%d(5) = d5
    else
      ddt7_new%d(5) = zero
    endif

    if (present(d6)) then
      ddt7_new%d(6) = d6
    else
      ddt7_new%d(6) = zero
    endif

    if (present(d7)) then
      ddt7_new%d(7) = d7
    else
      ddt7_new%d(7) = zero
    endif

  end function ddt7_new

!================================= ddtn_identity =============================80

  function ddtn_identity(f,n)

    integer, intent(in)                :: n
    real(dp), dimension(n), intent(in) :: f
    type(ddtn), dimension(n)           :: ddtn_identity
    real(dp), parameter                :: one  = 1.0_dp
    real(dp), parameter                :: zero = 0.0_dp
    integer                            :: i

    continue

    do i = 1, n
      ddtn_identity(i)%d(1:n) = zero
      ddtn_identity(i)%d(i)   = one
      ddtn_identity(i)%n      = n
      ddtn_identity(i)%f      = f(i)
    end do
    nsave = n

  end function ddtn_identity

!================================= ddt2_identity =============================80

  pure function ddt2_identity(f)
    real(dp), dimension(2),    intent(in) :: f
    type(ddt2), dimension(2) :: ddt2_identity

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_identity = f ! this will use ddt2_assign_dr to set the %d's to zero
    ddt2_identity(1)%d(1) = one
    ddt2_identity(2)%d(2) = one

  end function ddt2_identity

!================================= ddt3_identity =============================80

  pure function ddt3_identity(f)
    real(dp), dimension(3),    intent(in) :: f
    type(ddt3), dimension(3) :: ddt3_identity

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_identity = f ! this will use ddt3_assign_dr to set the %d's to zero
    ddt3_identity(1)%d(1) = one
    ddt3_identity(2)%d(2) = one
    ddt3_identity(3)%d(3) = one

  end function ddt3_identity

!================================= ddt4_identity =============================80

  pure function ddt4_identity(f)
    real(dp), dimension(4),    intent(in) :: f
    type(ddt4), dimension(4) :: ddt4_identity

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt4_identity = f ! this will use ddt4_assign_dr to set the %d's to zero
    ddt4_identity(1)%d(1) = one
    ddt4_identity(2)%d(2) = one
    ddt4_identity(3)%d(3) = one
    ddt4_identity(4)%d(4) = one

  end function ddt4_identity

!================================= ddt5_identity =============================80

  pure function ddt5_identity(f)
    real(dp), dimension(5),    intent(in) :: f
    type(ddt5), dimension(5) :: ddt5_identity

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_identity = f ! this will use ddt5_assign_dr to set the %d's to zero
    ddt5_identity(1)%d(1) = one
    ddt5_identity(2)%d(2) = one
    ddt5_identity(3)%d(3) = one
    ddt5_identity(4)%d(4) = one
    ddt5_identity(5)%d(5) = one

  end function ddt5_identity

!================================= ddt7_identity =============================80

  pure function ddt7_identity(f)
    real(dp), dimension(7),    intent(in) :: f
    type(ddt7), dimension(7) :: ddt7_identity

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_identity = f ! this will use ddt5_assign_dr to set the %d's to zero
    ddt7_identity(1)%d(1) = one
    ddt7_identity(2)%d(2) = one
    ddt7_identity(3)%d(3) = one
    ddt7_identity(4)%d(4) = one
    ddt7_identity(5)%d(5) = one
    ddt7_identity(6)%d(6) = one
    ddt7_identity(7)%d(7) = one

  end function ddt7_identity

!================================= ddtn_jacobian =============================80

  pure function ddtn_jacobian(ddt,n)
    type(ddtn), dimension(:), intent(in) :: ddt
    integer                 , intent(in) :: n
    real(dp), dimension(n,n)             :: ddtn_jacobian

    integer :: row

    continue

    do row = 1, n
      ddtn_jacobian(row,1:n) = ddt(row)%d(1:n)
    end do

  end function ddtn_jacobian

!================================= ddt2_jacobian =============================80

  pure function ddt2_jacobian(ddt)
    type(ddt2), dimension(2), intent(in) :: ddt
    real(dp), dimension(2,2) :: ddt2_jacobian

    integer :: row

    continue

    do row = 1, 2
      ddt2_jacobian(row,:) = ddt(row)%d
    end do

  end function ddt2_jacobian

!================================= ddt3_jacobian =============================80

  pure function ddt3_jacobian(ddt)
    type(ddt3), dimension(3), intent(in) :: ddt
    real(dp), dimension(3,3) :: ddt3_jacobian

    integer :: row

    continue

    do row = 1, 3
      ddt3_jacobian(row,:) = ddt(row)%d
    end do

  end function ddt3_jacobian

!================================= ddtn_assign ===============================80

  pure elemental subroutine ddtn_assign_dr(d, r)
    type(ddtn),  intent(inout) :: d ! d%n usually defined on input
                                    ! use nsave in case it has not
    real(dp),    intent(in)    :: r

    real(dp), parameter    :: zero = 0.0_dp

    continue

    d%n = nsave
    d%f = r
    d%d(1:d%n) = zero

  end subroutine ddtn_assign_dr

  pure elemental subroutine ddtn_assign_rd(r, d)
    real(dp),    intent(out)  :: r
    type(ddtn),  intent(in)   :: d

    continue

    r = d%f

  end subroutine ddtn_assign_rd

  pure elemental subroutine ddt2_assign_dr(d, r)
    type(ddt2),  intent(out) :: d
    real(dp),    intent(in)  :: r

    real(dp), parameter    :: zero = 0.0_dp

    continue

    d%f = r
    d%d = zero

  end subroutine ddt2_assign_dr

  pure elemental subroutine ddt2_assign_rd(r, d)
    real(dp),    intent(out)  :: r
    type(ddt2),  intent(in)   :: d

    continue

    r = d%f

  end subroutine ddt2_assign_rd


  pure elemental subroutine ddt3_assign_dr(d, r)
    type(ddt3),  intent(out) :: d
    real(dp),    intent(in)  :: r

    real(dp), parameter    :: zero = 0.0_dp

    continue

    d%f = r
    d%d = zero

  end subroutine ddt3_assign_dr

  pure elemental subroutine ddt3_assign_rd(r, d)
    real(dp),    intent(out)  :: r
    type(ddt3),  intent(in)   :: d

    continue

    r = d%f

  end subroutine ddt3_assign_rd

  pure elemental subroutine ddt4_assign_dr(d, r)
    type(ddt4),  intent(out) :: d
    real(dp),    intent(in)  :: r

    real(dp), parameter    :: zero = 0.0_dp

    continue

    d%f = r
    d%d = zero

  end subroutine ddt4_assign_dr

  pure elemental subroutine ddt4_assign_rd(r, d)
    real(dp),    intent(out)  :: r
    type(ddt4),  intent(in)   :: d

    continue

    r = d%f

  end subroutine ddt4_assign_rd

  pure elemental subroutine ddt5_assign_dr(d, r)
    type(ddt5),  intent(out) :: d
    real(dp),    intent(in)  :: r

    real(dp), parameter    :: zero = 0.0_dp

    continue

    d%f = r
    d%d = zero

  end subroutine ddt5_assign_dr

  pure elemental subroutine ddt5_assign_rd(r, d)
    real(dp),    intent(out)  :: r
    type(ddt5),  intent(in)   :: d

    continue

    r = d%f

  end subroutine ddt5_assign_rd

  pure elemental subroutine ddt7_assign_dr(d, r)
    type(ddt7),  intent(out) :: d
    real(dp),    intent(in)  :: r

    real(dp), parameter    :: zero = 0.0_dp

    continue

    d%f = r
    d%d = zero

  end subroutine ddt7_assign_dr

  pure elemental subroutine ddt7_assign_rd(r, d)
    real(dp),    intent(out)  :: r
    type(ddt7),  intent(in)   :: d

    continue

    r = d%f

  end subroutine ddt7_assign_rd

!================================= ddtn_plus =================================80

  pure elemental function ddtn_plus(a, b)
    type(ddtn), intent(in) :: a, b
    type(ddtn)             :: ddtn_plus

    continue

    ddtn_plus%f = a%f + b%f
    ddtn_plus%d(1:a%n) = a%d(1:a%n) + b%d(1:a%n)
    ddtn_plus%n      = a%n

  end function ddtn_plus

  pure elemental function ddtn_plus_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddtn)             :: ddtn_plus_dr

    continue

    ddtn_plus_dr%f        = d%f + r
    ddtn_plus_dr%d(1:d%n) = d%d(1:d%n)
    ddtn_plus_dr%n        = d%n

  end function ddtn_plus_dr

  pure elemental function ddtn_plus_rd(r, d)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddtn)             :: ddtn_plus_rd

    continue

    ddtn_plus_rd%f = d%f + r
    ddtn_plus_rd%d(1:d%n) = d%d(1:d%n)
    ddtn_plus_rd%n = d%n

  end function ddtn_plus_rd

!================================= ddt2_plus =================================80

  pure elemental function ddt2_plus(a, b)
    type(ddt2), intent(in) :: a, b
    type(ddt2)             :: ddt2_plus

    continue

    ddt2_plus%f = a%f + b%f
    ddt2_plus%d = a%d + b%d

  end function ddt2_plus

  pure elemental function ddt2_plus_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt2)             :: ddt2_plus_dr

    continue

    ddt2_plus_dr%f = d%f + r
    ddt2_plus_dr%d = d%d

  end function ddt2_plus_dr

  pure elemental function ddt2_plus_rd(r, d)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt2)             :: ddt2_plus_rd

    continue

    ddt2_plus_rd%f = d%f + r
    ddt2_plus_rd%d = d%d

  end function ddt2_plus_rd

!================================= ddt3_plus =================================80

  pure elemental function ddt3_plus(a, b)
    type(ddt3), intent(in) :: a, b
    type(ddt3)             :: ddt3_plus

    continue

    ddt3_plus%f = a%f + b%f
    ddt3_plus%d = a%d + b%d

  end function ddt3_plus

  pure elemental function ddt3_plus_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt3)             :: ddt3_plus_dr

    continue

    ddt3_plus_dr%f = d%f + r
    ddt3_plus_dr%d = d%d

  end function ddt3_plus_dr

  pure elemental function ddt3_plus_rd(r, d)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt3)             :: ddt3_plus_rd

    continue

    ddt3_plus_rd%f = d%f + r
    ddt3_plus_rd%d = d%d

  end function ddt3_plus_rd

!================================= ddt4_plus =================================80

  pure elemental function ddt4_plus(a, b)
    type(ddt4), intent(in) :: a, b
    type(ddt4)             :: ddt4_plus

    continue

    ddt4_plus%f = a%f + b%f
    ddt4_plus%d = a%d + b%d

  end function ddt4_plus

  pure elemental function ddt4_plus_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt4)             :: ddt4_plus_dr

    continue

    ddt4_plus_dr%f = d%f + r
    ddt4_plus_dr%d = d%d

  end function ddt4_plus_dr

  pure elemental function ddt4_plus_rd(r, d)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt4)             :: ddt4_plus_rd

    continue

    ddt4_plus_rd%f = d%f + r
    ddt4_plus_rd%d = d%d

  end function ddt4_plus_rd

!================================= ddt5_plus =================================80
  pure elemental function ddt5_plus(a, b)
    type(ddt5), intent(in) :: a, b
    type(ddt5)             :: ddt5_plus

    continue

    ddt5_plus%f = a%f + b%f
    ddt5_plus%d = a%d + b%d

  end function ddt5_plus

  pure elemental function ddt5_plus_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt5)             :: ddt5_plus_dr

    continue

    ddt5_plus_dr%f = d%f + r
    ddt5_plus_dr%d = d%d

  end function ddt5_plus_dr

  pure elemental function ddt5_plus_rd(r, d)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt5)             :: ddt5_plus_rd

    continue

    ddt5_plus_rd%f = d%f + r
    ddt5_plus_rd%d = d%d

  end function ddt5_plus_rd

!================================= ddt7_plus =================================80

  pure elemental function ddt7_plus(a, b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_plus

    continue

    ddt7_plus%f = a%f + b%f
    ddt7_plus%d = a%d + b%d

  end function ddt7_plus

  pure elemental function ddt7_plus_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt7)             :: ddt7_plus_dr

    continue

    ddt7_plus_dr%f = d%f + r
    ddt7_plus_dr%d = d%d

  end function ddt7_plus_dr

  pure elemental function ddt7_plus_rd(r, d)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt7)             :: ddt7_plus_rd

    continue

    ddt7_plus_rd%f = d%f + r
    ddt7_plus_rd%d = d%d

  end function ddt7_plus_rd

!================================= ddtn_minus ================================80

  pure elemental function ddtn_minus(a, b)
    type(ddtn), intent(in) :: a, b
    type(ddtn)             :: ddtn_minus

    continue

    ddtn_minus%f = a%f - b%f
    ddtn_minus%d(1:a%n) = a%d(1:a%n) - b%d(1:a%n)
    ddtn_minus%n = a%n

  end function ddtn_minus

  pure elemental function ddtn_minus_rd(r, d)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddtn)             :: ddtn_minus_rd

    continue

    ddtn_minus_rd%f = r - d%f
    ddtn_minus_rd%d(1:d%n) =   - d%d(1:d%n)
    ddtn_minus_rd%n = d%n

  end function ddtn_minus_rd

  pure elemental function ddtn_minus_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddtn)             :: ddtn_minus_dr

    continue

    ddtn_minus_dr%f = d%f - r
    ddtn_minus_dr%d(1:d%n) = d%d(1:d%n)
    ddtn_minus_dr%n = d%n

  end function ddtn_minus_dr

  pure elemental function ddtn_minus_unary(d)
    type(ddtn), intent(in) :: d
    type(ddtn)             :: ddtn_minus_unary

    continue

    ddtn_minus_unary%f = -d%f
    ddtn_minus_unary%d(1:d%n) = -d%d(1:d%n)
    ddtn_minus_unary%n = d%n

  end function ddtn_minus_unary

!================================= ddt2_minus ================================80

  pure elemental function ddt2_minus(a, b)
    type(ddt2), intent(in) :: a, b
    type(ddt2)             :: ddt2_minus

    continue

    ddt2_minus%f = a%f - b%f
    ddt2_minus%d = a%d - b%d

  end function ddt2_minus

  pure elemental function ddt2_minus_rd(r, d)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt2)             :: ddt2_minus_rd

    continue

    ddt2_minus_rd%f = r - d%f
    ddt2_minus_rd%d =   - d%d

  end function ddt2_minus_rd

  pure elemental function ddt2_minus_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt2)             :: ddt2_minus_dr

    continue

    ddt2_minus_dr%f = d%f - r
    ddt2_minus_dr%d = d%d

  end function ddt2_minus_dr

  pure elemental function ddt2_minus_unary(d)
    type(ddt2), intent(in) :: d
    type(ddt2)             :: ddt2_minus_unary

    continue

    ddt2_minus_unary%f = -d%f
    ddt2_minus_unary%d = -d%d

  end function ddt2_minus_unary

!================================= ddt3_minus ================================80

  pure elemental function ddt3_minus(a, b)
    type(ddt3), intent(in) :: a, b
    type(ddt3)             :: ddt3_minus

    continue

    ddt3_minus%f = a%f - b%f
    ddt3_minus%d = a%d - b%d

  end function ddt3_minus

  pure elemental function ddt3_minus_rd(r, d)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt3)             :: ddt3_minus_rd

    continue

    ddt3_minus_rd%f = r - d%f
    ddt3_minus_rd%d =   - d%d

  end function ddt3_minus_rd

  pure elemental function ddt3_minus_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt3)             :: ddt3_minus_dr

    continue

    ddt3_minus_dr%f = d%f - r
    ddt3_minus_dr%d = d%d

  end function ddt3_minus_dr

  pure elemental function ddt3_minus_unary(d)
    type(ddt3), intent(in) :: d
    type(ddt3)             :: ddt3_minus_unary

    continue

    ddt3_minus_unary%f = -d%f
    ddt3_minus_unary%d = -d%d

  end function ddt3_minus_unary

!================================= ddt4_minus ================================80

  pure elemental function ddt4_minus(a, b)
    type(ddt4), intent(in) :: a, b
    type(ddt4)             :: ddt4_minus

    continue

    ddt4_minus%f = a%f - b%f
    ddt4_minus%d = a%d - b%d

  end function ddt4_minus

  pure elemental function ddt4_minus_rd(r, d)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt4)             :: ddt4_minus_rd

    continue

    ddt4_minus_rd%f = r - d%f
    ddt4_minus_rd%d =   - d%d

  end function ddt4_minus_rd

  pure elemental function ddt4_minus_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt4)             :: ddt4_minus_dr

    continue

    ddt4_minus_dr%f = d%f - r
    ddt4_minus_dr%d = d%d

  end function ddt4_minus_dr

  pure elemental function ddt4_minus_unary(d)
    type(ddt4), intent(in) :: d
    type(ddt4)             :: ddt4_minus_unary

    continue

    ddt4_minus_unary%f = -d%f
    ddt4_minus_unary%d = -d%d

  end function ddt4_minus_unary

!================================= ddt5_minus ================================80

  pure elemental function ddt5_minus(a, b)
    type(ddt5), intent(in) :: a, b
    type(ddt5)             :: ddt5_minus

    continue

    ddt5_minus%f = a%f - b%f
    ddt5_minus%d = a%d - b%d

  end function ddt5_minus

  pure elemental function ddt5_minus_rd(r, d)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt5)             :: ddt5_minus_rd

    continue

    ddt5_minus_rd%f = r - d%f
    ddt5_minus_rd%d =   - d%d

  end function ddt5_minus_rd

  pure elemental function ddt5_minus_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt5)             :: ddt5_minus_dr

    continue

    ddt5_minus_dr%f = d%f - r
    ddt5_minus_dr%d = d%d

  end function ddt5_minus_dr

  pure elemental function ddt5_minus_unary(d)
    type(ddt5), intent(in) :: d
    type(ddt5)             :: ddt5_minus_unary

    continue

    ddt5_minus_unary%f = -d%f
    ddt5_minus_unary%d = -d%d

  end function ddt5_minus_unary

!================================= ddt7_minus ================================80

  pure elemental function ddt7_minus(a, b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_minus

    continue

    ddt7_minus%f = a%f - b%f
    ddt7_minus%d = a%d - b%d

  end function ddt7_minus

  pure elemental function ddt7_minus_rd(r, d)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt7)             :: ddt7_minus_rd

    continue

    ddt7_minus_rd%f = r - d%f
    ddt7_minus_rd%d =   - d%d

  end function ddt7_minus_rd

  pure elemental function ddt7_minus_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    type(ddt7)             :: ddt7_minus_dr

    continue

    ddt7_minus_dr%f = d%f - r
    ddt7_minus_dr%d = d%d

  end function ddt7_minus_dr

  pure elemental function ddt7_minus_unary(d)
    type(ddt7), intent(in) :: d
    type(ddt7)             :: ddt7_minus_unary

    continue

    ddt7_minus_unary%f = -d%f
    ddt7_minus_unary%d = -d%d

  end function ddt7_minus_unary

!================================= ddtn_multiply =============================80

  pure elemental function ddtn_multiply(a, b)
    type(ddtn), intent(in) :: a, b
    type(ddtn)             :: ddtn_multiply

    continue

    ddtn_multiply%f = a%f * b%f
    ddtn_multiply%d(1:a%n) = a%f*b%d(1:b%n) + b%f*a%d(1:a%n)
    ddtn_multiply%n = a%n

  end function ddtn_multiply

  pure elemental function ddtn_multiply_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddtn)             :: ddtn_multiply_dr

    continue

    ddtn_multiply_dr%f = r*d%f
    ddtn_multiply_dr%d(1:d%n) = r*d%d(1:d%n)
    ddtn_multiply_dr%n = d%n

  end function ddtn_multiply_dr

  pure elemental function ddtn_multiply_rd(r, d)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddtn)             :: ddtn_multiply_rd

    continue

    ddtn_multiply_rd%f = r*d%f
    ddtn_multiply_rd%d(1:d%n) = r*d%d(1:d%n)
    ddtn_multiply_rd%n = d%n

  end function ddtn_multiply_rd

!================================= ddt2_multiply =============================80

  pure elemental function ddt2_multiply(a, b)
    type(ddt2), intent(in) :: a, b
    type(ddt2)             :: ddt2_multiply

    continue

    ddt2_multiply%f = a%f * b%f
    ddt2_multiply%d = a%f*b%d + b%f*a%d

  end function ddt2_multiply

  pure elemental function ddt2_multiply_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt2)             :: ddt2_multiply_dr

    continue

    ddt2_multiply_dr%f = r*d%f
    ddt2_multiply_dr%d = r*d%d

  end function ddt2_multiply_dr

  pure elemental function ddt2_multiply_rd(r, d)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt2)             :: ddt2_multiply_rd

    continue

    ddt2_multiply_rd%f = r*d%f
    ddt2_multiply_rd%d = r*d%d

  end function ddt2_multiply_rd

!================================= ddt3_multiply =============================80

  pure elemental function ddt3_multiply(a, b)
    type(ddt3), intent(in) :: a, b
    type(ddt3)             :: ddt3_multiply

    continue

    ddt3_multiply%f = a%f * b%f
    ddt3_multiply%d = a%f*b%d + b%f*a%d

  end function ddt3_multiply

  pure elemental function ddt3_multiply_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt3)             :: ddt3_multiply_dr

    continue

    ddt3_multiply_dr%f = r*d%f
    ddt3_multiply_dr%d = r*d%d

  end function ddt3_multiply_dr

  pure elemental function ddt3_multiply_rd(r, d)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt3)             :: ddt3_multiply_rd

    continue

    ddt3_multiply_rd%f = r*d%f
    ddt3_multiply_rd%d = r*d%d

  end function ddt3_multiply_rd

!================================= ddt4_multiply =============================80

  pure elemental function ddt4_multiply(a, b)
    type(ddt4), intent(in) :: a, b
    type(ddt4)             :: ddt4_multiply

    continue

    ddt4_multiply%f = a%f * b%f
    ddt4_multiply%d = a%f*b%d + b%f*a%d

  end function ddt4_multiply

  pure elemental function ddt4_multiply_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt4)             :: ddt4_multiply_dr

    continue

    ddt4_multiply_dr%f = r*d%f
    ddt4_multiply_dr%d = r*d%d

  end function ddt4_multiply_dr

  pure elemental function ddt4_multiply_rd(r, d)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt4)             :: ddt4_multiply_rd

    continue

    ddt4_multiply_rd%f = r*d%f
    ddt4_multiply_rd%d = r*d%d

  end function ddt4_multiply_rd

!================================= ddt5_multiply =============================80

  pure elemental function ddt5_multiply(a, b)
    type(ddt5), intent(in) :: a, b
    type(ddt5)             :: ddt5_multiply

    continue

    ddt5_multiply%f = a%f * b%f
    ddt5_multiply%d = a%f*b%d + b%f*a%d

  end function ddt5_multiply

  pure elemental function ddt5_multiply_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt5)             :: ddt5_multiply_dr

    continue

    ddt5_multiply_dr%f = r*d%f
    ddt5_multiply_dr%d = r*d%d

  end function ddt5_multiply_dr

  pure elemental function ddt5_multiply_rd(r, d)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt5)             :: ddt5_multiply_rd

    continue

    ddt5_multiply_rd%f = r*d%f
    ddt5_multiply_rd%d = r*d%d

  end function ddt5_multiply_rd

!================================= ddt7_multiply =============================80

  pure elemental function ddt7_multiply(a, b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_multiply

    continue

    ddt7_multiply%f = a%f * b%f
    ddt7_multiply%d = a%f*b%d + b%f*a%d

  end function ddt7_multiply

  pure elemental function ddt7_multiply_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt7)             :: ddt7_multiply_dr

    continue

    ddt7_multiply_dr%f = r*d%f
    ddt7_multiply_dr%d = r*d%d

  end function ddt7_multiply_dr

  pure elemental function ddt7_multiply_rd(r, d)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt7)             :: ddt7_multiply_rd

    continue

    ddt7_multiply_rd%f = r*d%f
    ddt7_multiply_rd%d = r*d%d

  end function ddt7_multiply_rd

!================================= ddtn_divide ===============================80

  pure elemental function ddtn_divide(a, b)
    type(ddtn), intent(in) :: a, b
    type(ddtn)             :: ddtn_divide

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddtn_divide%f = a%f / b%f

    denom = one / b%f / b%f

    ddtn_divide%d(1:a%n) = ( a%d(1:a%n)*b%f - b%d(1:b%n)*a%f ) * denom
    ddtn_divide%n = a%n

  end function ddtn_divide

  pure elemental function ddtn_divide_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddtn)             :: ddtn_divide_dr

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddtn_divide_dr%f = d%f / r

    denom = one / r / r

    ddtn_divide_dr%d(1:d%n) = ( d%d(1:d%n)*r ) * denom
    ddtn_divide_dr%n = d%n

  end function ddtn_divide_dr

  pure elemental function ddtn_divide_rd(r, d)
    real(dp),    intent(in) :: r
    type(ddtn),  intent(in) :: d
    type(ddtn)              :: ddtn_divide_rd

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddtn_divide_rd%f = r / d%f

    denom = one / d%f / d%f

    ddtn_divide_rd%d(1:d%n) = ( - d%d(1:d%n)*r ) * denom
    ddtn_divide_rd%n = d%n

  end function ddtn_divide_rd

!================================= ddt2_divide ===============================80

  pure elemental function ddt2_divide(a, b)
    type(ddt2), intent(in) :: a, b
    type(ddt2)             :: ddt2_divide

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_divide%f = a%f / b%f

    denom = one / b%f / b%f

    ddt2_divide%d = ( a%d*b%f - b%d*a%f ) * denom

  end function ddt2_divide

  pure elemental function ddt2_divide_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt2)             :: ddt2_divide_dr

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_divide_dr%f = d%f / r

    denom = one / r / r

    ddt2_divide_dr%d = ( d%d*r ) * denom

  end function ddt2_divide_dr

  pure elemental function ddt2_divide_rd(r, d)
    real(dp),    intent(in) :: r
    type(ddt2),  intent(in) :: d
    type(ddt2)              :: ddt2_divide_rd

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_divide_rd%f = r / d%f

    denom = one / d%f / d%f

    ddt2_divide_rd%d = ( - d%d*r ) * denom

  end function ddt2_divide_rd

!================================= ddt3_divide ===============================80
  pure elemental function ddt3_divide(a, b)
    type(ddt3), intent(in) :: a, b
    type(ddt3)             :: ddt3_divide

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_divide%f = a%f / b%f

    denom = one / b%f / b%f

    ddt3_divide%d = ( a%d*b%f - b%d*a%f ) * denom

  end function ddt3_divide

  pure elemental function ddt3_divide_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt3)             :: ddt3_divide_dr

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_divide_dr%f = d%f / r

    denom = one / r / r

    ddt3_divide_dr%d = ( d%d*r ) * denom

  end function ddt3_divide_dr

  pure elemental function ddt3_divide_rd(r, d)
    real(dp),    intent(in) :: r
    type(ddt3),  intent(in) :: d
    type(ddt3)              :: ddt3_divide_rd

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_divide_rd%f = r / d%f

    denom = one / d%f / d%f

    ddt3_divide_rd%d = ( - d%d*r ) * denom

  end function ddt3_divide_rd

!================================= ddt4_divide ===============================80
  pure elemental function ddt4_divide(a, b)
    type(ddt4), intent(in) :: a, b
    type(ddt4)             :: ddt4_divide

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt4_divide%f = a%f / b%f

    denom = one / b%f / b%f

    ddt4_divide%d = ( a%d*b%f - b%d*a%f ) * denom

  end function ddt4_divide

  pure elemental function ddt4_divide_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt4)             :: ddt4_divide_dr

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt4_divide_dr%f = d%f / r

    denom = one / r / r

    ddt4_divide_dr%d = ( d%d*r ) * denom

  end function ddt4_divide_dr

  pure elemental function ddt4_divide_rd(r, d)
    real(dp),    intent(in) :: r
    type(ddt4),  intent(in) :: d
    type(ddt4)              :: ddt4_divide_rd

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt4_divide_rd%f = r / d%f

    denom = one / d%f / d%f

    ddt4_divide_rd%d = ( - d%d*r ) * denom

  end function ddt4_divide_rd

!================================= ddt5_divide ===============================80
  pure elemental function ddt5_divide(a, b)
    type(ddt5), intent(in) :: a, b
    type(ddt5)             :: ddt5_divide

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_divide%f = a%f / b%f

    denom = one / b%f / b%f

    ddt5_divide%d = ( a%d*b%f - b%d*a%f ) * denom

  end function ddt5_divide

  pure elemental function ddt5_divide_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt5)             :: ddt5_divide_dr

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_divide_dr%f = d%f / r

    denom = one / r / r

    ddt5_divide_dr%d = ( d%d*r ) * denom

  end function ddt5_divide_dr

  pure elemental function ddt5_divide_rd(r, d)
    real(dp),    intent(in) :: r
    type(ddt5),  intent(in) :: d
    type(ddt5)              :: ddt5_divide_rd

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_divide_rd%f = r / d%f

    denom = one / d%f / d%f

    ddt5_divide_rd%d = ( - d%d*r ) * denom

  end function ddt5_divide_rd

!================================= ddt7_divide ===============================80

  pure elemental function ddt7_divide(a, b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_divide

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_divide%f = a%f / b%f

    denom = one / b%f / b%f

    ddt7_divide%d = ( a%d*b%f - b%d*a%f ) * denom

  end function ddt7_divide

  pure elemental function ddt7_divide_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt7)             :: ddt7_divide_dr

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_divide_dr%f = d%f / r

    denom = one / r / r

    ddt7_divide_dr%d = ( d%d*r ) * denom

  end function ddt7_divide_dr

  pure elemental function ddt7_divide_rd(r, d)
    real(dp),    intent(in) :: r
    type(ddt7),  intent(in) :: d
    type(ddt7)              :: ddt7_divide_rd

    real(dp)    :: denom
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_divide_rd%f = r / d%f

    denom = one / d%f / d%f

    ddt7_divide_rd%d = ( - d%d*r ) * denom

  end function ddt7_divide_rd

!================================= ddtn_power ================================80

  pure elemental function ddtn_power(a, b)
    type(ddtn),  intent(in) :: a, b
    type(ddtn)             :: ddtn_power

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddtn_power%f = a%f**b%f
    ddtn_power%d(1:a%n) = b%f * a%f**(b%f-one) * a%d(1:a%n)                    &
                        + log(a%f) * a%f**b%f * b%d(1:b%n)
    ddtn_power%n = a%n

  end function ddtn_power

  pure elemental function ddtn_power_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddtn)             :: ddtn_power_dr

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddtn_power_dr%f = d%f**r
    ddtn_power_dr%d(1:d%n) = r * d%f**(r-one) * d%d(1:d%n)
    ddtn_power_dr%n = d%n

  end function ddtn_power_dr

  pure elemental function ddtn_power_di(d, i)
    type(ddtn),  intent(in) :: d
    integer,     intent(in) :: i
    type(ddtn)             :: ddtn_power_di

    continue

    ddtn_power_di%f = d%f**i
    ddtn_power_di%d(1:d%n) = real(i,dp) * d%f**(i-1) * d%d(1:d%n)
    ddtn_power_di%n = d%n

  end function ddtn_power_di

!================================= ddt2_power ================================80

  pure elemental function ddt2_power(a, b)
    type(ddt2),  intent(in) :: a, b
    type(ddt2)             :: ddt2_power

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_power%f = a%f**b%f
    ddt2_power%d = b%f * a%f**(b%f-one) * a%d                                  &
                  + log(a%f) * a%f**b%f * b%d

  end function ddt2_power

  pure elemental function ddt2_power_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt2)             :: ddt2_power_dr

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_power_dr%f = d%f**r
    ddt2_power_dr%d = r * d%f**(r-one) * d%d

  end function ddt2_power_dr

  pure elemental function ddt2_power_di(d, i)
    type(ddt2),  intent(in) :: d
    integer,     intent(in) :: i
    type(ddt2)             :: ddt2_power_di

    continue

    ddt2_power_di%f = d%f**i
    ddt2_power_di%d = real(i,dp) * d%f**(i-1) * d%d

  end function ddt2_power_di

!================================= ddt3_power ================================80

  pure elemental function ddt3_power(a, b)
    type(ddt3),  intent(in) :: a, b
    type(ddt3)             :: ddt3_power

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_power%f = a%f**b%f
    ddt3_power%d = b%f * a%f**(b%f-one) * a%d                                  &
                  + log(a%f) * a%f**b%f * b%d

  end function ddt3_power

  pure elemental function ddt3_power_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt3)             :: ddt3_power_dr

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_power_dr%f = d%f**r
    ddt3_power_dr%d = r * d%f**(r-one) * d%d

  end function ddt3_power_dr

  pure elemental function ddt3_power_di(d, i)
    type(ddt3),  intent(in) :: d
    integer,     intent(in) :: i
    type(ddt3)             :: ddt3_power_di

    continue

    ddt3_power_di%f = d%f**i
    ddt3_power_di%d = real(i,dp) * d%f**(i-1) * d%d

  end function ddt3_power_di

!================================= ddt4_power ================================80

  pure elemental function ddt4_power(a, b)
    type(ddt4),  intent(in) :: a, b
    type(ddt4)             :: ddt4_power

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt4_power%f = a%f**b%f
    ddt4_power%d = b%f * a%f**(b%f-one) * a%d                                  &
                  + log(a%f) * a%f**b%f * b%d

  end function ddt4_power

  pure elemental function ddt4_power_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt4)             :: ddt4_power_dr

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt4_power_dr%f = d%f**r
    ddt4_power_dr%d = r * d%f**(r-one) * d%d

  end function ddt4_power_dr

  pure elemental function ddt4_power_di(d, i)
    type(ddt4),  intent(in) :: d
    integer,     intent(in) :: i
    type(ddt4)             :: ddt4_power_di

    continue

    ddt4_power_di%f = d%f**i
    ddt4_power_di%d = real(i,dp) * d%f**(i-1) * d%d

  end function ddt4_power_di

!================================= ddt5_power ================================80

  pure elemental function ddt5_power(a, b)
    type(ddt5),  intent(in) :: a, b
    type(ddt5)             :: ddt5_power

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_power%f = a%f**b%f
    ddt5_power%d = b%f * a%f**(b%f-one) * a%d                                  &
                  + log(a%f) * a%f**b%f * b%d

  end function ddt5_power

  pure elemental function ddt5_power_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt5)             :: ddt5_power_dr

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_power_dr%f = d%f**r
    ddt5_power_dr%d = r * d%f**(r-one) * d%d

  end function ddt5_power_dr

  pure elemental function ddt5_power_di(d, i)
    type(ddt5),  intent(in) :: d
    integer,     intent(in) :: i
    type(ddt5)             :: ddt5_power_di

    continue

    ddt5_power_di%f = d%f**i
    ddt5_power_di%d = real(i,dp) * d%f**(i-1) * d%d

  end function ddt5_power_di


!================================= ddt7_power ================================80

  pure elemental function ddt7_power(a, b)
    type(ddt7),  intent(in) :: a, b
    type(ddt7)             :: ddt7_power

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_power%f = a%f**b%f
    ddt7_power%d = b%f * a%f**(b%f-one) * a%d                                  &
                  + log(a%f) * a%f**b%f * b%d

  end function ddt7_power

  pure elemental function ddt7_power_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r
    type(ddt7)             :: ddt7_power_dr

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_power_dr%f = d%f**r
    ddt7_power_dr%d = r * d%f**(r-one) * d%d

  end function ddt7_power_dr

  pure elemental function ddt7_power_di(d, i)
    type(ddt7),  intent(in) :: d
    integer,     intent(in) :: i
    type(ddt7)             :: ddt7_power_di

    continue

    ddt7_power_di%f = d%f**i
    ddt7_power_di%d = real(i,dp) * d%f**(i-1) * d%d

  end function ddt7_power_di

!================================= ddtn_greater_than =========================80

  pure elemental function ddtn_greater_than(a, b)
    type(ddtn), intent(in) :: a, b
    logical                :: ddtn_greater_than

    continue

    ddtn_greater_than = ( a%f > b%f )

  end function ddtn_greater_than

  pure elemental function ddtn_greater_than_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_greater_than_dr

    continue

    ddtn_greater_than_dr = ( d%f > r )

  end function ddtn_greater_than_dr

  pure elemental function ddtn_greater_than_rd(r, d)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_greater_than_rd

    continue

    ddtn_greater_than_rd = ( r > d%f )

  end function ddtn_greater_than_rd

!================================= ddt2_greater_than =========================80

  pure elemental function ddt2_greater_than(a, b)
    type(ddt2), intent(in) :: a, b
    logical                :: ddt2_greater_than

    continue

    ddt2_greater_than = ( a%f > b%f )

  end function ddt2_greater_than

  pure elemental function ddt2_greater_than_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt2_greater_than_dr

    continue

    ddt2_greater_than_dr = ( d%f > r )

  end function ddt2_greater_than_dr

  pure elemental function ddt2_greater_than_rd(r, d)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt2_greater_than_rd

    continue

    ddt2_greater_than_rd = ( r > d%f )

  end function ddt2_greater_than_rd

!================================= ddt3_greater_than =========================80

  pure elemental function ddt3_greater_than(a, b)
    type(ddt3), intent(in) :: a, b
    logical                :: ddt3_greater_than

    continue

    ddt3_greater_than = ( a%f > b%f )

  end function ddt3_greater_than

  pure elemental function ddt3_greater_than_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt3_greater_than_dr

    continue

    ddt3_greater_than_dr = ( d%f > r )

  end function ddt3_greater_than_dr

  pure elemental function ddt3_greater_than_rd(r, d)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt3_greater_than_rd

    continue

    ddt3_greater_than_rd = ( r > d%f )

  end function ddt3_greater_than_rd

!================================= ddt4_greater_than =========================80

  pure elemental function ddt4_greater_than(a, b)
    type(ddt4), intent(in) :: a, b
    logical                :: ddt4_greater_than

    continue

    ddt4_greater_than = ( a%f > b%f )

  end function ddt4_greater_than

  pure elemental function ddt4_greater_than_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt4_greater_than_dr

    continue

    ddt4_greater_than_dr = ( d%f > r )

  end function ddt4_greater_than_dr

  pure elemental function ddt4_greater_than_rd(r, d)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt4_greater_than_rd

    continue

    ddt4_greater_than_rd = ( r > d%f )

  end function ddt4_greater_than_rd

!================================= ddt5_greater_than =========================80

  pure elemental function ddt5_greater_than(a, b)
    type(ddt5), intent(in) :: a, b
    logical                :: ddt5_greater_than

    continue

    ddt5_greater_than = ( a%f > b%f )

  end function ddt5_greater_than

  pure elemental function ddt5_greater_than_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_greater_than_dr

    continue

    ddt5_greater_than_dr = ( d%f > r )

  end function ddt5_greater_than_dr

  pure elemental function ddt5_greater_than_rd(r, d)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_greater_than_rd

    continue

    ddt5_greater_than_rd = ( r > d%f )

  end function ddt5_greater_than_rd

!================================= ddtn_greater_than_equal ===================80

  pure elemental function ddtn_greater_than_equal(a, b)
    type(ddtn), intent(in) :: a, b
    logical                :: ddtn_greater_than_equal

    continue

    ddtn_greater_than_equal = ( a%f >= b%f )

  end function ddtn_greater_than_equal

  pure elemental function ddtn_greater_than_equal_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_greater_than_equal_dr

    continue

    ddtn_greater_than_equal_dr = ( d%f >= r )

  end function ddtn_greater_than_equal_dr

!================================= ddt5_greater_than_equal ===================80

  pure elemental function ddt5_greater_than_equal(a, b)
    type(ddt5), intent(in) :: a, b
    logical                :: ddt5_greater_than_equal

    continue

    ddt5_greater_than_equal = ( a%f >= b%f )

  end function ddt5_greater_than_equal

  pure elemental function ddt5_greater_than_equal_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_greater_than_equal_dr

    continue

    ddt5_greater_than_equal_dr = ( d%f >= r )

  end function ddt5_greater_than_equal_dr

!================================= ddt7_greater_than_equal ===================80

  pure elemental function ddt7_greater_than_equal(a, b)
    type(ddt7), intent(in) :: a, b
    logical                :: ddt7_greater_than_equal

    continue

    ddt7_greater_than_equal = ( a%f >= b%f )

  end function ddt7_greater_than_equal

  pure elemental function ddt7_greater_than_equal_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt7_greater_than_equal_dr

    continue

    ddt7_greater_than_equal_dr = ( d%f >= r )

  end function ddt7_greater_than_equal_dr

!================================= ddt7_greater_than =========================80

  pure elemental function ddt7_greater_than(a, b)
    type(ddt7), intent(in) :: a, b
    logical                :: ddt7_greater_than

    continue

    ddt7_greater_than = ( a%f > b%f )

  end function ddt7_greater_than

  pure elemental function ddt7_greater_than_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt7_greater_than_dr

    continue

    ddt7_greater_than_dr = ( d%f > r )

  end function ddt7_greater_than_dr

  pure elemental function ddt7_greater_than_rd(r, d)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt7_greater_than_rd

    continue

    ddt7_greater_than_rd = ( r > d%f )

  end function ddt7_greater_than_rd

!================================= ddtn_less_than ============================80

  pure elemental function ddtn_less_than(a, b)
    type(ddtn), intent(in) :: a, b
    logical                :: ddtn_less_than

    continue

    ddtn_less_than = ( a%f < b%f )

  end function ddtn_less_than

  pure elemental function ddtn_less_than_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_less_than_dr

    continue

    ddtn_less_than_dr = ( d%f < r )

  end function ddtn_less_than_dr

  pure elemental function ddtn_less_than_rd(r, d)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_less_than_rd

    continue

    ddtn_less_than_rd = ( r < d%f )

  end function ddtn_less_than_rd

!================================= ddt2_less_than ============================80

  pure elemental function ddt2_less_than(a, b)
    type(ddt2), intent(in) :: a, b
    logical                :: ddt2_less_than

    continue

    ddt2_less_than = ( a%f < b%f )

  end function ddt2_less_than

  pure elemental function ddt2_less_than_dr(d, r)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt2_less_than_dr

    continue

    ddt2_less_than_dr = ( d%f < r )

  end function ddt2_less_than_dr

  pure elemental function ddt2_less_than_rd(r, d)
    type(ddt2),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt2_less_than_rd

    continue

    ddt2_less_than_rd = ( r < d%f )

  end function ddt2_less_than_rd

!================================= ddt3_less_than ============================80

  pure elemental function ddt3_less_than(a, b)
    type(ddt3), intent(in) :: a, b
    logical                :: ddt3_less_than

    continue

    ddt3_less_than = ( a%f < b%f )

  end function ddt3_less_than

  pure elemental function ddt3_less_than_dr(d, r)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt3_less_than_dr

    continue

    ddt3_less_than_dr = ( d%f < r )

  end function ddt3_less_than_dr

  pure elemental function ddt3_less_than_rd(r, d)
    type(ddt3),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt3_less_than_rd

    continue

    ddt3_less_than_rd = ( r < d%f )

  end function ddt3_less_than_rd

!================================= ddt4_less_than ============================80

  pure elemental function ddt4_less_than(a, b)
    type(ddt4), intent(in) :: a, b
    logical                :: ddt4_less_than

    continue

    ddt4_less_than = ( a%f < b%f )

  end function ddt4_less_than

  pure elemental function ddt4_less_than_dr(d, r)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt4_less_than_dr

    continue

    ddt4_less_than_dr = ( d%f < r )

  end function ddt4_less_than_dr

  pure elemental function ddt4_less_than_rd(r, d)
    type(ddt4),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt4_less_than_rd

    continue

    ddt4_less_than_rd = ( r < d%f )

  end function ddt4_less_than_rd

!================================= ddt5_less_than ============================80

  pure elemental function ddt5_less_than(a, b)
    type(ddt5), intent(in) :: a, b
    logical                :: ddt5_less_than

    continue

    ddt5_less_than = ( a%f < b%f )

  end function ddt5_less_than

  pure elemental function ddt5_less_than_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_less_than_dr

    continue

    ddt5_less_than_dr = ( d%f < r )

  end function ddt5_less_than_dr

  pure elemental function ddt5_less_than_rd(r, d)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_less_than_rd

    continue

    ddt5_less_than_rd = ( r < d%f )

  end function ddt5_less_than_rd

!================================= ddtn_less_than_equal ======================80

  pure elemental function ddtn_less_than_equal(a, b)
    type(ddtn), intent(in) :: a, b
    logical                :: ddtn_less_than_equal

    continue

    ddtn_less_than_equal = ( a%f <= b%f )

  end function ddtn_less_than_equal

!================================= ddt5_less_than_equal ======================80

  pure elemental function ddt5_less_than_equal(a, b)
    type(ddt5), intent(in) :: a, b
    logical                :: ddt5_less_than_equal

    continue

    ddt5_less_than_equal = ( a%f <= b%f )

  end function ddt5_less_than_equal

  pure elemental function ddtn_less_than_equal_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_less_than_equal_dr

    continue

    ddtn_less_than_equal_dr = ( d%f <= r )

  end function ddtn_less_than_equal_dr

  pure elemental function ddt5_less_than_equal_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_less_than_equal_dr

    continue

    ddt5_less_than_equal_dr = ( d%f <= r )

  end function ddt5_less_than_equal_dr

!================================= ddtn_equal ================================80

  pure elemental function ddtn_equal(a, b)
    type(ddtn), intent(in) :: a, b
    logical                :: ddtn_equal

    continue

    ddtn_equal = ( a%f == b%f )

  end function ddtn_equal

  pure elemental function ddtn_equal_dr(d, r)
    type(ddtn),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddtn_equal_dr

    continue

    ddtn_equal_dr = ( d%f == r )

  end function ddtn_equal_dr

!================================= ddt5_equal ================================80

  pure elemental function ddt5_equal(a, b)
    type(ddt5), intent(in) :: a, b
    logical                :: ddt5_equal

    continue

    ddt5_equal = ( a%f == b%f )

  end function ddt5_equal

  pure elemental function ddt5_equal_dr(d, r)
    type(ddt5),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt5_equal_dr

    continue

    ddt5_equal_dr = ( d%f == r )

  end function ddt5_equal_dr

!================================= ddtn_exp ==================================80

  pure elemental function ddtn_exp(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_exp

    continue

    ddtn_exp%f = exp(a%f)
    ddtn_exp%d(1:a%n) = ddtn_exp%f * a%d(1:a%n)
    ddtn_exp%n = a%n

  end function ddtn_exp

!================================= ddt2_exp ==================================80

  pure elemental function ddt2_exp(a)
    type(ddt2), intent(in) :: a
    type(ddt2)             :: ddt2_exp

    continue

    ddt2_exp%f = exp(a%f)

    ddt2_exp%d = ddt2_exp%f * a%d

  end function ddt2_exp

!================================= ddt3_exp ==================================80

  pure elemental function ddt3_exp(a)
    type(ddt3), intent(in) :: a
    type(ddt3)             :: ddt3_exp

    continue

    ddt3_exp%f = exp(a%f)

    ddt3_exp%d = ddt3_exp%f * a%d

  end function ddt3_exp

!================================= ddt4_exp ==================================80

  pure elemental function ddt4_exp(a)
    type(ddt4), intent(in) :: a
    type(ddt4)             :: ddt4_exp

    continue

    ddt4_exp%f = exp(a%f)

    ddt4_exp%d = ddt4_exp%f * a%d

  end function ddt4_exp

!================================= ddt5_exp ==================================80

  pure elemental function ddt5_exp(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_exp

    continue

    ddt5_exp%f = exp(a%f)

    ddt5_exp%d = ddt5_exp%f * a%d

  end function ddt5_exp

!================================= ddt7_exp ==================================80

  pure elemental function ddt7_exp(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_exp

    continue

    ddt7_exp%f = exp(a%f)

    ddt7_exp%d = ddt7_exp%f * a%d

  end function ddt7_exp

!================================= ddtn_log ==================================80

  pure elemental function ddtn_log(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_log

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddtn_log%f = log(a%f)
    ddtn_log%d(1:a%n) = ( one / a%f ) * a%d(1:a%n)
    ddtn_log%n = a%n

  end function ddtn_log

!================================= ddt2_log ==================================80

  pure elemental function ddt2_log(a)
    type(ddt2), intent(in) :: a
    type(ddt2)             :: ddt2_log

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt2_log%f = log(a%f)

    ddt2_log%d = ( one / a%f ) * a%d

  end function ddt2_log

!================================= ddt3_log ==================================80

  pure elemental function ddt3_log(a)
    type(ddt3), intent(in) :: a
    type(ddt3)             :: ddt3_log

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt3_log%f = log(a%f)

    ddt3_log%d = ( one / a%f ) * a%d

  end function ddt3_log

!================================= ddt5_log ==================================80

  pure elemental function ddt5_log(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_log

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_log%f = log(a%f)

    ddt5_log%d = ( one / a%f ) * a%d

  end function ddt5_log

!================================= ddt7_log ==================================80

  pure elemental function ddt7_log(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_log

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_log%f = log(a%f)

    ddt7_log%d = ( one / a%f ) * a%d

  end function ddt7_log

!================================= ddtn_tanh =================================80

  pure elemental &
  function ddtn_tanh(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_tanh

    real(dp)    :: tanh_f
    real(dp), parameter    :: one = 1.0_dp

    continue

    tanh_f = tanh(a%f)
    ddtn_tanh%f = tanh_f
    ddtn_tanh%d(1:a%n) = ( one - tanh_f**2 ) * a%d(1:a%n)
    ddtn_tanh%n = a%n

  end function ddtn_tanh

!================================= ddt5_tanh =================================80

  pure elemental &
  function ddt5_tanh(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_tanh

    real(dp)    :: tanh_f
    real(dp), parameter    :: one = 1.0_dp

    continue

    tanh_f = tanh(a%f)
    ddt5_tanh%f = tanh_f

    ddt5_tanh%d = ( one - tanh_f**2 ) * a%d

  end function ddt5_tanh

!================================= ddt7_tanh =================================80

  pure elemental &
  function ddt7_tanh(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_tanh

    real(dp)    :: tanh_f
    real(dp), parameter    :: one = 1.0_dp

    continue

    tanh_f = tanh(a%f)
    ddt7_tanh%f = tanh_f

    ddt7_tanh%d = ( one - tanh_f**2 ) * a%d

  end function ddt7_tanh

!================================= ddtn_cos ==================================80

  pure elemental &
  function ddtn_cos(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_cos

    continue

    ddtn_cos%f = cos(a%f)
    ddtn_cos%d(1:a%n) = -sin(a%f) * a%d(1:a%n)
    ddtn_cos%n = a%n

  end function ddtn_cos

!================================= ddt5_cos ==================================80

  pure elemental &
  function ddt5_cos(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_cos

    continue

    ddt5_cos%f = cos(a%f)

    ddt5_cos%d = -sin(a%f) * a%d

  end function ddt5_cos

!================================= ddt7_cos ==================================80

  pure elemental &
  function ddt7_cos(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_cos

    continue

    ddt7_cos%f = cos(a%f)

    ddt7_cos%d = -sin(a%f) * a%d

  end function ddt7_cos

!================================= ddt7_acos =================================80

  pure elemental &
  function ddt7_acos(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_acos

    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_acos%f = acos(a%f)

    ddt7_acos%d = -( one / sqrt( one - a%f**2 ) ) * a%d

  end function ddt7_acos

!================================= ddtn_sin ==================================80

  pure elemental &
  function ddtn_sin(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_sin

    continue

    ddtn_sin%f = sin(a%f)
    ddtn_sin%d(1:a%n) = cos(a%f) * a%d(1:a%n)
    ddtn_sin%n = a%n

  end function ddtn_sin

!================================= ddt5_sin ==================================80

  pure elemental &
  function ddt5_sin(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_sin

    continue

    ddt5_sin%f = sin(a%f)

    ddt5_sin%d = cos(a%f) * a%d

  end function ddt5_sin

!================================= ddt5_asin =================================80

  pure elemental &
  function ddt5_asin(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_asin
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt5_asin%f = asin(a%f)

    ddt5_asin%d = ( one / sqrt(one-a%f*a%f) ) * a%d

  end function ddt5_asin

!================================= ddt7_asin =================================80

  pure elemental &
  function ddt7_asin(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_asin
    real(dp), parameter    :: one = 1.0_dp

    continue

    ddt7_asin%f = asin(a%f)

    ddt7_asin%d = ( one / sqrt(one-a%f*a%f) ) * a%d

  end function ddt7_asin

!================================= ddt7_less_than ============================80

  pure elemental &
  function ddt7_less_than(a, b)
    type(ddt7), intent(in) :: a, b
    logical                :: ddt7_less_than

    continue

    ddt7_less_than = ( a%f < b%f )

  end function ddt7_less_than

  pure elemental &
  function ddt7_less_than_dr(d, r)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt7_less_than_dr

    continue

    ddt7_less_than_dr = ( d%f < r )

  end function ddt7_less_than_dr

  pure elemental &
  function ddt7_less_than_rd(r, d)
    type(ddt7),  intent(in) :: d
    real(dp),    intent(in) :: r

    logical                :: ddt7_less_than_rd

    continue

    ddt7_less_than_rd = ( r < d%f )

  end function ddt7_less_than_rd

!================================= ddtn_sqrt =================================80

  pure elemental &
  function ddtn_sqrt(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_sqrt

    real(dp)    :: sqrt_f
    real(dp), parameter    :: half = 0.5_dp

    continue

    sqrt_f = sqrt(a%f)
    ddtn_sqrt%f = sqrt_f

    prevent_inf_derivative : if ( sqrt_f > ddt_sqrt_floor ) then
      ddtn_sqrt%d(1:a%n) = ( half / sqrt_f ) * a%d(1:a%n)
    else
      ddtn_sqrt%d(1:a%n) = ( half / ddt_sqrt_floor ) * a%d(1:a%n)
    end if prevent_inf_derivative
    ddtn_sqrt%n = a%n

  end function ddtn_sqrt

!================================= ddt2_sqrt =================================80

  pure elemental function ddt2_sqrt(a)
    type(ddt2), intent(in) :: a
    type(ddt2)             :: ddt2_sqrt

    real(dp)    :: sqrt_f
    real(dp), parameter    :: half = 0.5_dp

    continue

    sqrt_f = sqrt(a%f)

    ddt2_sqrt%f = sqrt_f

    prevent_inf_derivative : if ( sqrt_f > ddt_sqrt_floor ) then
      ddt2_sqrt%d = ( half / sqrt_f ) * a%d
    else
      ddt2_sqrt%d = ( half / ddt_sqrt_floor ) * a%d
    end if prevent_inf_derivative

  end function ddt2_sqrt

!================================= ddt3_sqrt =================================80

  pure elemental function ddt3_sqrt(a)
    type(ddt3), intent(in) :: a
    type(ddt3)             :: ddt3_sqrt

    real(dp)    :: sqrt_f
    real(dp), parameter    :: half = 0.5_dp

    continue

    sqrt_f = sqrt(a%f)

    ddt3_sqrt%f = sqrt_f

    prevent_inf_derivative : if ( sqrt_f > ddt_sqrt_floor ) then
      ddt3_sqrt%d = ( half / sqrt_f ) * a%d
    else
      ddt3_sqrt%d = ( half / ddt_sqrt_floor ) * a%d
    end if prevent_inf_derivative

  end function ddt3_sqrt

!================================= ddt4_sqrt =================================80
  pure elemental function ddt4_sqrt(a)
    type(ddt4), intent(in) :: a
    type(ddt4)             :: ddt4_sqrt

    real(dp)    :: sqrt_f
    real(dp), parameter    :: half = 0.5_dp

    continue

    sqrt_f = sqrt(a%f)

    ddt4_sqrt%f = sqrt_f

    prevent_inf_derivative : if ( sqrt_f > ddt_sqrt_floor ) then
      ddt4_sqrt%d = ( half / sqrt_f ) * a%d
    else
      ddt4_sqrt%d = ( half / ddt_sqrt_floor ) * a%d
    end if prevent_inf_derivative

  end function ddt4_sqrt

!================================= ddt5_sqrt =================================80
  pure elemental function ddt5_sqrt(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_sqrt

    real(dp)    :: sqrt_f
    real(dp), parameter    :: half = 0.5_dp

    continue

    sqrt_f = sqrt(a%f)

    ddt5_sqrt%f = sqrt_f

    prevent_inf_derivative : if ( sqrt_f > ddt_sqrt_floor ) then
      ddt5_sqrt%d = ( half / sqrt_f ) * a%d
    else
      ddt5_sqrt%d = ( half / ddt_sqrt_floor ) * a%d
    end if prevent_inf_derivative

  end function ddt5_sqrt


!================================= ddt7_sqrt =================================80
  pure elemental function ddt7_sqrt(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_sqrt

    real(dp)    :: sqrt_f
    real(dp), parameter    :: half = 0.5_dp

    continue

    sqrt_f = sqrt(a%f)

    ddt7_sqrt%f = sqrt_f

    prevent_inf_derivative : if ( sqrt_f > ddt_sqrt_floor ) then
      ddt7_sqrt%d = ( half / sqrt_f ) * a%d
    else
      ddt7_sqrt%d = ( half / ddt_sqrt_floor ) * a%d
    end if prevent_inf_derivative

  end function ddt7_sqrt

!================================= ddtn_max ==================================80

  pure elemental function ddtn_max(a,b)
    type(ddtn), intent(in) :: a, b
    type(ddtn)             :: ddtn_max

    continue

    if ( a > b ) then
      ddtn_max = a
    else
      ddtn_max = b
    endif

  end function ddtn_max

  pure elemental function ddtn_max_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddtn),  intent(in) :: b
    type(ddtn)             :: ddtn_max_rd

    continue

    ddtn_max_rd%n = b%n
    if ( r > b ) then
      ddtn_max_rd = r
    else
      ddtn_max_rd = b
    endif

  end function ddtn_max_rd

  pure elemental function ddtn_max_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddtn),  intent(in) :: d
    type(ddtn)              :: ddtn_max_dr

    continue

    ddtn_max_dr%n = d%n
    if ( r > d ) then
      ddtn_max_dr = r
    else
      ddtn_max_dr = d
    endif

  end function ddtn_max_dr

!================================= ddt2_max ==================================80

  pure elemental function ddt2_max(a,b)
    type(ddt2), intent(in) :: a, b
    type(ddt2)             :: ddt2_max

    continue

    if ( a > b ) then
      ddt2_max = a
    else
      ddt2_max = b
    endif

  end function ddt2_max

  pure elemental function ddt2_max_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt2),  intent(in) :: b
    type(ddt2)             :: ddt2_max_rd

    continue

    if ( r > b ) then
      ddt2_max_rd = r
    else
      ddt2_max_rd = b
    endif

  end function ddt2_max_rd

  pure elemental function ddt2_max_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt2),  intent(in) :: d
    type(ddt2)              :: ddt2_max_dr

    continue

    if ( r > d ) then
      ddt2_max_dr = r
    else
      ddt2_max_dr = d
    endif

  end function ddt2_max_dr

!================================= ddt4_max ==================================80

  pure elemental function ddt4_max(a,b)
    type(ddt4), intent(in) :: a, b
    type(ddt4)             :: ddt4_max

    continue

    if ( a > b ) then
      ddt4_max = a
    else
      ddt4_max = b
    endif

  end function ddt4_max

  pure elemental function ddt4_max_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt4),  intent(in) :: b
    type(ddt4)             :: ddt4_max_rd

    continue

    if ( r > b ) then
      ddt4_max_rd = r
    else
      ddt4_max_rd = b
    endif

  end function ddt4_max_rd

  pure elemental function ddt4_max_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt4),  intent(in) :: d
    type(ddt4)              :: ddt4_max_dr

    continue

    if ( r > d ) then
      ddt4_max_dr = r
    else
      ddt4_max_dr = d
    endif

  end function ddt4_max_dr

!================================= ddt5_max ==================================80

  pure elemental function ddt5_max(a,b)
    type(ddt5), intent(in) :: a, b
    type(ddt5)             :: ddt5_max

    continue

    if ( a > b ) then
      ddt5_max = a
    else
      ddt5_max = b
    endif

  end function ddt5_max

  pure elemental function ddt5_max_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt5),  intent(in) :: b
    type(ddt5)             :: ddt5_max_rd

    continue

    if ( r > b ) then
      ddt5_max_rd = r
    else
      ddt5_max_rd = b
    endif

  end function ddt5_max_rd

  pure elemental function ddt5_max_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt5),  intent(in) :: d
    type(ddt5)              :: ddt5_max_dr

    continue

    if ( r > d ) then
      ddt5_max_dr = r
    else
      ddt5_max_dr = d
    endif

  end function ddt5_max_dr

!================================= ddt7_max ==================================80

  pure elemental function ddt7_max(a,b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_max

    continue

    if ( a > b ) then
      ddt7_max = a
    else
      ddt7_max = b
    endif

  end function ddt7_max

  pure elemental function ddt7_max_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt7),  intent(in) :: b
    type(ddt7)              :: ddt7_max_rd

    continue

    if ( r > b ) then
      ddt7_max_rd = r
    else
      ddt7_max_rd = b
    endif

  end function ddt7_max_rd

  pure elemental function ddt7_max_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt7),  intent(in) :: d
    type(ddt7)              :: ddt7_max_dr

    continue

    if ( r > d ) then
      ddt7_max_dr = r
    else
      ddt7_max_dr = d
    endif

  end function ddt7_max_dr

!================================= ddtn_min ==================================80

  pure elemental function ddtn_min(a,b)
    type(ddtn), intent(in) :: a, b
    type(ddtn)             :: ddtn_min

    continue

    if ( a < b ) then
      ddtn_min = a
    else
      ddtn_min = b
    endif

  end function ddtn_min

  pure elemental function ddtn_min_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddtn),  intent(in) :: b
    type(ddtn)              :: ddtn_min_rd

    continue

    ddtn_min_rd%n = b%n
    if ( r < b ) then
      ddtn_min_rd = r
    else
      ddtn_min_rd = b
    endif

  end function ddtn_min_rd

  pure elemental function ddtn_min_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddtn),  intent(in) :: d
    type(ddtn)              :: ddtn_min_dr

    continue

    ddtn_min_dr%n = d%n
    if ( r < d ) then
      ddtn_min_dr = r
    else
      ddtn_min_dr = d
    endif

  end function ddtn_min_dr

!================================= ddt2_min ==================================80

  pure elemental function ddt2_min(a,b)
    type(ddt2), intent(in) :: a, b
    type(ddt2)             :: ddt2_min

    continue

    if ( a < b ) then
      ddt2_min = a
    else
      ddt2_min = b
    endif

  end function ddt2_min

  pure elemental function ddt2_min_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt2),  intent(in) :: b
    type(ddt2)              :: ddt2_min_rd

    continue

    if ( r < b ) then
      ddt2_min_rd = r
    else
      ddt2_min_rd = b
    endif

  end function ddt2_min_rd

  pure elemental function ddt2_min_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt2),  intent(in) :: d
    type(ddt2)              :: ddt2_min_dr

    continue

    if ( r < d ) then
      ddt2_min_dr = r
    else
      ddt2_min_dr = d
    endif

  end function ddt2_min_dr

!================================= ddt4_min ==================================80

  pure elemental function ddt4_min(a,b)
    type(ddt4), intent(in) :: a, b
    type(ddt4)             :: ddt4_min

    continue

    if ( a < b ) then
      ddt4_min = a
    else
      ddt4_min = b
    endif

  end function ddt4_min

  pure elemental function ddt4_min_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt4),  intent(in) :: b
    type(ddt4)              :: ddt4_min_rd

    continue

    if ( r < b ) then
      ddt4_min_rd = r
    else
      ddt4_min_rd = b
    endif

  end function ddt4_min_rd

  pure elemental function ddt4_min_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt4),  intent(in) :: d
    type(ddt4)              :: ddt4_min_dr

    continue

    if ( r < d ) then
      ddt4_min_dr = r
    else
      ddt4_min_dr = d
    endif

  end function ddt4_min_dr

!================================= ddt5_min ==================================80

  pure elemental function ddt5_min(a,b)
    type(ddt5), intent(in) :: a, b
    type(ddt5)             :: ddt5_min

    continue

    if ( a < b ) then
      ddt5_min = a
    else
      ddt5_min = b
    endif

  end function ddt5_min

  pure elemental function ddt5_min_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt5),  intent(in) :: b
    type(ddt5)              :: ddt5_min_rd

    continue

    if ( r < b ) then
      ddt5_min_rd = r
    else
      ddt5_min_rd = b
    endif

  end function ddt5_min_rd

  pure elemental function ddt5_min_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt5),  intent(in) :: d
    type(ddt5)              :: ddt5_min_dr

    continue

    if ( r < d ) then
      ddt5_min_dr = r
    else
      ddt5_min_dr = d
    endif

  end function ddt5_min_dr

!================================= ddt7_min ==================================80

  pure elemental function ddt7_min(a,b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_min

    continue

    if ( a < b ) then
      ddt7_min = a
    else
      ddt7_min = b
    endif

  end function ddt7_min

  pure elemental function ddt7_min_rd(r,b)
    real(dp),    intent(in) :: r
    type(ddt7),  intent(in) :: b
    type(ddt7)              :: ddt7_min_rd

    continue

    if ( r < b ) then
      ddt7_min_rd = r
    else
      ddt7_min_rd = b
    endif

  end function ddt7_min_rd

  pure elemental function ddt7_min_dr(d,r)
    real(dp),    intent(in) :: r
    type(ddt7),  intent(in) :: d
    type(ddt7)              :: ddt7_min_dr

    continue

    if ( r < d ) then
      ddt7_min_dr = r
    else
      ddt7_min_dr = d
    endif

  end function ddt7_min_dr

!================================= ddtn_abs ==================================80

  pure elemental function ddtn_abs(a)
    type(ddtn), intent(in) :: a
    type(ddtn)             :: ddtn_abs

    real(dp), parameter    :: zero = 0.0_dp

    continue

    ddtn_abs%n = a%n
    if ( a < zero ) then
      ddtn_abs = -a
    else
      ddtn_abs = a
    endif

  end function ddtn_abs

!================================= ddt2_abs ==================================80

  pure elemental function ddt2_abs(a)
    type(ddt2), intent(in) :: a
    type(ddt2)             :: ddt2_abs

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( a < zero ) then
      ddt2_abs = -a
    else
      ddt2_abs = a
    endif

  end function ddt2_abs

!================================= ddt3_abs ==================================80

  pure elemental function ddt3_abs(a)
    type(ddt3), intent(in) :: a
    type(ddt3)             :: ddt3_abs

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( a < zero ) then
      ddt3_abs = -a
    else
      ddt3_abs = a
    endif

  end function ddt3_abs

!================================= ddt4_abs ==================================80

  pure elemental function ddt4_abs(a)
    type(ddt4), intent(in) :: a
    type(ddt4)             :: ddt4_abs

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( a < zero ) then
      ddt4_abs = -a
    else
      ddt4_abs = a
    endif

  end function ddt4_abs

!================================= ddt5_abs ==================================80
  pure elemental function ddt5_abs(a)
    type(ddt5), intent(in) :: a
    type(ddt5)             :: ddt5_abs

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( a < zero ) then
      ddt5_abs = -a
    else
      ddt5_abs = a
    endif

  end function ddt5_abs

!================================= ddt7_abs ==================================80
  pure elemental function ddt7_abs(a)
    type(ddt7), intent(in) :: a
    type(ddt7)             :: ddt7_abs

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( a < zero ) then
      ddt7_abs = -a
    else
      ddt7_abs = a
    endif

  end function ddt7_abs

!================================= ddtn_sign_rd ==============================80

  pure elemental &
  function ddtn_sign_rd(a,b)
    real(dp),   intent(in) :: a
    type(ddtn), intent(in) :: b
    type(ddtn)             :: ddtn_sign_rd

    real(dp), parameter    :: zero = 0.0_dp

    continue

    ddtn_sign_rd%n = b%n
    if ( b >= zero) then
      ddtn_sign_rd =  abs(a)
    else
      ddtn_sign_rd = -abs(a)
    end if

  end function ddtn_sign_rd

!================================= ddt5_sign_rd ==============================80

  pure elemental &
  function ddt5_sign_rd(a,b)
    real(dp),   intent(in) :: a
    type(ddt5), intent(in) :: b
    type(ddt5)             :: ddt5_sign_rd

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( b >= zero) then
      ddt5_sign_rd =  abs(a)
    else
      ddt5_sign_rd = -abs(a)
    end if

  end function ddt5_sign_rd

!================================= ddt7_sign =================================80

  pure elemental &
  function ddt7_sign(a,b)
    type(ddt7), intent(in) :: a, b
    type(ddt7)             :: ddt7_sign

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( b >= zero) then
      ddt7_sign =  ddt_abs(a)
    else
      ddt7_sign = -ddt_abs(a)
    end if

  end function ddt7_sign

  pure elemental &
  function ddt7_sign_rd(a,b)
    real(dp),   intent(in) :: a
    type(ddt7), intent(in) :: b
    type(ddt7)             :: ddt7_sign_rd

    real(dp), parameter    :: zero = 0.0_dp

    continue

    if ( b >= zero) then
      ddt7_sign_rd =  abs(a)
    else
      ddt7_sign_rd = -abs(a)
    end if

  end function ddt7_sign_rd

!================================ ddt5_vdot ==================================80
!
!  Computes the dot product
!
!=============================================================================80

  pure function ddt5_vdot( a, b )

    type(ddt5)                           :: ddt5_vdot
    type(ddt5), dimension(3), intent(in) :: a
    type(ddt5), dimension(3), intent(in) :: b

  continue

    ddt5_vdot = a(1)*b(1) + a(2)*b(2) + a(3)*b(3)

  end function ddt5_vdot

  pure function ddt5_vdot_dr( d, r )

    use kinddefs, only : dp

    type(ddt5)                           :: ddt5_vdot_dr
    real(dp),   dimension(3), intent(in) :: r
    type(ddt5), dimension(3), intent(in) :: d

  continue

    ddt5_vdot_dr = r(1)*d(1) + r(2)*d(2) + r(3)*d(3)

  end function ddt5_vdot_dr

!================================ ddt7_vdot ==================================80
!
!  Computes the dot product
!
!=============================================================================80

  pure function ddt7_vdot( a, b )

    type(ddt7)                           :: ddt7_vdot
    type(ddt7), dimension(3), intent(in) :: a
    type(ddt7), dimension(3), intent(in) :: b

  continue

    ddt7_vdot = a(1)*b(1) + a(2)*b(2) + a(3)*b(3)

  end function ddt7_vdot

  pure function ddt7_vdot_dr( d, r )

    use kinddefs, only : dp

    type(ddt7)                           :: ddt7_vdot_dr
    real(dp),   dimension(3), intent(in) :: r
    type(ddt7), dimension(3), intent(in) :: d

  continue

    ddt7_vdot_dr = r(1)*d(1) + r(2)*d(2) + r(3)*d(3)

  end function ddt7_vdot_dr

!================================ ddtn_vdot ==================================80
!
!  Computes the dot product
!
!=============================================================================80

  pure function ddtn_vdot( a, b )

    type(ddtn)                           :: ddtn_vdot
    type(ddtn), dimension(3), intent(in) :: a
    type(ddtn), dimension(3), intent(in) :: b

  continue

    ddtn_vdot = a(1)*b(1) + a(2)*b(2) + a(3)*b(3)

  end function ddtn_vdot

  pure function ddtn_vdot_dr( d, r )

    use kinddefs, only : dp

    type(ddtn)                           :: ddtn_vdot_dr
    real(dp),   dimension(3), intent(in) :: r
    type(ddtn), dimension(3), intent(in) :: d

  continue

    ddtn_vdot_dr = r(1)*d(1) + r(2)*d(2) + r(3)*d(3)

  end function ddtn_vdot_dr


!================================ ddt5_matmul ================================80
!
!  Does matrix multiplication a*b
!   a is nxm
!   b is mxp
!   result is nxp
!
!=============================================================================80

  function ddt5_matmul( a, b, n, p )

    integer, intent(in) :: n, p

    type(ddt5), dimension(:,:), intent(in) :: a
    type(ddt5), dimension(:,:), intent(in) :: b

    type(ddt5), dimension(n,p) :: ddt5_matmul

    integer :: m, i, j, k

  continue

    m = size(a,2)

    if ( m /= size(b,1) ) then
      write(*,*) 'ddt5_matmul: inconsistent matrix dimensions'
      stop
    endif

    do i = 1, n
      do j = 1, p
        ddt5_matmul(i,j) = ddt5_new()
        do k = 1, m
          ddt5_matmul(i,j) = ddt5_matmul(i,j) + a(i,k)*b(k,j)
        end do
      end do
    end do

  end function ddt5_matmul

end module ddt

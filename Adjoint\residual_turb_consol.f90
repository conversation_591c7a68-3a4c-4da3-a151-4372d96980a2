
module residual_turb_consol

  implicit none

  private

  public :: turb_consol_adjoint
  public :: dres_damut_lambda
  public :: dturbgrad_dq

contains

  subroutine turb_consol_adjoint( grid, soln, sadj, design, coltag, rlam )

    use kinddefs,         only : dp
    use info_depr,        only : twod, xmach, re
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,     only : sadj_type
    use design_types,     only : design_type
    use turb_util,        only : kloc, wloc
    use thermo,           only : dprimitive_dconserved
    use ddt,              only : ddt7, assignment(=)
    use lmpi,             only : lmpi_conditional_stop
    use turbulence_info,  only : turbulence_model_int
    use turb_kw_const,    only : realizability_int, mut_off

    type(grid_type),                              intent(in)    :: grid
    type(soln_type),                              intent(inout) :: soln
    type(sadj_type),                              intent(inout) :: sadj
    type(design_type),                            intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01), intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
                                                  intent(in) :: rlam

    type(ddt7), dimension(soln%adim) :: res_node1, res_node2
    integer :: nedge_flux_eval

    integer :: i, j, k
    integer :: n, node1, node2
    integer :: node

    real(dp) :: xmrinv

    real(dp), dimension(soln%adim, design%nfunctions) :: dres_dprim
    real(dp), dimension(7,7)    :: dQdq
    real(dp), dimension(5)      :: state

    real(dp), dimension(:,:), allocatable :: res_lam

    type(ddt7), dimension(soln%adim) :: q_ddt
    type(ddt7)                       :: mut_ddt

    real(dp), parameter    :: zero   = 0.0_dp
    real(dp), parameter    :: one    = 1.0_dp
    type(ddt7), parameter  :: one_ddt = ddt7(1.0_dp,0.0_dp)

    continue

      xmrinv = re / xmach
! initialize to identity
    dQdq            = zero
    do i = 1, soln%adim
      dQdq(i,i) = one
    end do

    nedge_flux_eval = grid%nedgeloc
    if (twod) then
      nedge_flux_eval = grid%nedgeloc_2d
    endif

      edge_conv : do n = 1, nedge_flux_eval
        node1 = grid%eptr(1,n)
        node2 = grid%eptr(2,n)

        node1_conv : if ( node1 <= grid%nnodes0 )  then

! require (turbulent_convection == 0)
! inline turb_convection:edge_assembly_jacob_conv here
          call lmpi_conditional_stop(1,'implement 2-eq adjoint conv')
          res_node1 = 0.0_dp
          res_node2 = 0.0_dp

          dres_dprim = zero
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k)                              &
                  + coltag(i,node1) * rlam(i,node1,k) * res_node1(i)%d(j)
              end do
            end do
          end do
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k)                              &
                  + coltag(i,node2) * rlam(i,node2,k) * res_node2(i)%d(j)
              end do
            end do
          end do
          state = in_primitive_variables( soln%q_dof(:,node1) )
          call dprimitive_dconserved( state, dQdq(1:5,1:5) )
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                sadj%res(j,node1,k) = &
                  sadj%res(j,node1,k)+dQdq(i,j)*dres_dprim(i,k)
              end do
            end do
          end do
        end if node1_conv

        node2_conv : if ( node2 <= grid%nnodes0 )  then

! require (turbulent_convection == 0)
! inline turb_convection:edge_assembly_jacob_conv here
          res_node1 = 0.0_dp
          res_node2 = 0.0_dp

          dres_dprim = zero
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k) &
                  + coltag(i,node1) * rlam(i,node1,k) * res_node1(i)%d(j)
              end do
            end do
          end do
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k) &
                  + coltag(i,node2) * rlam(i,node2,k) * res_node2(i)%d(j)
              end do
            end do
          end do
          state = in_primitive_variables( soln%q_dof(:,node2) )
          call dprimitive_dconserved( state, dQdq(1:5,1:5) )
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                sadj%res(j,node2,k)= &
                  sadj%res(j,node2,k)+dQdq(i,j)*dres_dprim(i,k)
              end do
            end do
          end do
        end if node2_conv
      end do edge_conv


    if ( grid%nelem > 1 .or. grid%elem(1)%type_cell /= 'tet' ) &
      call lmpi_conditional_stop(1,'kw_adjoint dres_damut_lambda tet only')

    skip_mut : if ( .not. mut_off ) then

      allocate( res_lam( grid%nnodes01, design%nfunctions ) )

      res_lam = zero

      call dres_damut_lambda(                              &
        grid%nnodes0, grid%nnodes01,                       &
        grid%elem(1)%ncell, grid%elem(1)%c2n,              &
        grid%x, grid%y, grid%z, soln%q_dof, soln%n_tot,    &
        res_lam, coltag, rlam, soln%adim, design%nfunctions)

      do k = 1, design%nfunctions
        do node = 1, grid%nnodes0
          q_ddt = get_q_ddt( soln%eqn_set, soln%n_tot, soln%n_turb,  &
                             kloc, wloc,                             &
                             soln%q_dof(:,node), soln%turb(:,node) )
          mut_ddt = mut_kw_ddt( kloc, wloc, soln%n_tot, soln%n_turb, &
                                q_ddt, xmrinv,                       &
                                one_ddt, one, turbulence_model_int,  &
                                realizability_int )
          do i = 1,soln%adim
            sadj%res(i,node,k) = sadj%res(i,node,k) &
              + mut_ddt%d(i) * res_lam(node,k)
          end do
        end do
      end do

      deallocate( res_lam )

    end if skip_mut

      edge_diff : do n = 1, nedge_flux_eval
        node1 = grid%eptr(1,n)
        node2 = grid%eptr(2,n)

        node1_diff : if ( node1 <= grid%nnodes0 )  then

! inline turb_diffusion:turb_jacob_diff_element
            res_node1 = 0.0_dp
            res_node2 = 0.0_dp

          dres_dprim = zero
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k)                              &
                  + coltag(i,node1) * rlam(i,node1,k) * res_node1(i)%d(j)
              end do
            end do
          end do
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k)                              &
                  + coltag(i,node2) * rlam(i,node2,k) * res_node2(i)%d(j)
              end do
            end do
          end do
          state = in_primitive_variables( soln%q_dof(:,node1) )
          call dprimitive_dconserved( state, dQdq(1:5,1:5) )
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                sadj%res(j,node1,k) = &
                  sadj%res(j,node1,k)+dQdq(i,j)*dres_dprim(i,k)
              end do
            end do
          end do
        end if node1_diff

        node2_diff : if ( node2 <= grid%nnodes0 )  then

! inline turb_diffusion:turb_jacob_diff_element
            res_node1 = 0.0_dp
            res_node2 = 0.0_dp

          dres_dprim = zero
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k) &
                  + coltag(i,node1) * rlam(i,node1,k) * res_node1(i)%d(j)
              end do
            end do
          end do
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                dres_dprim(j,k) = dres_dprim(j,k) &
                  + coltag(i,node2) * rlam(i,node2,k) * res_node2(i)%d(j)
              end do
            end do
          end do
          state = in_primitive_variables( soln%q_dof(:,node2) )
          call dprimitive_dconserved( state, dQdq(1:5,1:5) )
          do i = 1, soln%adim
            do j = 1, soln%adim
              do k = 1, design%nfunctions
                sadj%res(j,node2,k)= &
                  sadj%res(j,node2,k)+dQdq(i,j)*dres_dprim(i,k)
              end do
            end do
          end do
        end if node2_diff
      end do edge_diff

  end subroutine turb_consol_adjoint

!=============================================================================80
!
! This routine computes the full viscous fluxes on tets
! expects conservative
! computes d(flow residual)/d(amut)
!
!=============================================================================80

  subroutine dres_damut_lambda(nnodes0, nnodes01, ncell, c2n,                  &
                        x, y, z, qnode, n_tot,                                 &
                        res_lam, coltag, rlam, adim, nfunc)

    use kinddefs,         only : dp
!    use info_depr,            only : tref
    use info_depr,            only : xmach, re, ivgrd
    use fluid,                only : gamma, gm1
!    use fluid,                only : sutherland_constant, prandtl
    use turb_parameters,      only : turbulent_prandtl
    use generic_gas_map,      only : n_etot, n_momx, n_momy, n_momz

    integer,                                    intent(in)    :: n_tot
    integer,                                    intent(in)    :: nnodes0
    integer,                                    intent(in)    :: nnodes01
    integer,                                    intent(in)    :: ncell

    integer,      dimension(4,ncell),           intent(in)    :: c2n

    real(dp),  dimension(nnodes01),             intent(in)    :: x, y, z
    real(dp),  dimension(n_tot,nnodes01),       intent(in)    :: qnode
    integer,                                    intent(in)    :: adim,nfunc
    real(dp),  dimension(nnodes01,nfunc),       intent(inout) :: res_lam
    real(dp),  dimension(adim,nnodes01),        intent(in)    :: coltag
    real(dp),  dimension(adim,nnodes01,nfunc),  intent(in)    :: rlam

    integer     :: cell
    integer     :: node1, node2, node3, node4
    integer,dimension(4) :: n
    integer     :: k,i

    real(dp)    :: t1,t2,t3,t4
!    real(dp)    :: eta
    real(dp)    :: const,dot
    real(dp)    :: nx1,nx2,nx3,nx4
    real(dp)    :: ny1,ny2,ny3,ny4,nz1,nz2,nz3,nz4
!    real(dp)    :: rmu,rmu1,rmu2,rmu3,rmu4
    real(dp)    :: u1,u2,u3,u4,usize1,usize2,usize3
    real(dp)    :: usize4,ux,ux1,ux2,ux3,ux4,uy,uy1,uy2,uy3,uy4
    real(dp)    :: uz,uz1,uz2,uz3,uz4,v1,v2,v3,v4,vol,vx
    real(dp)    :: vy,vz,w1,w2,w3,w4,wx,wy,wz,x1,x2,x3,x4
    real(dp)    :: y1,y2,y3,y4,z1,z2,z3,z4
    real(dp)    :: tx, ty, tz
!    real(dp)    :: tqx, tqy, tqz
!    real(dp)    :: txx, txy, txz, tyx, tyy, tyz, tzx, tzy, tzz

    real(dp), dimension(4) :: umu_amut, vmu_amut, wmu_amut, eta_amut
    real(dp), dimension(4) :: rmu_amut
    real(dp), dimension(4) :: txx_amut, txy_amut, txz_amut
    real(dp), dimension(4) :: tyx_amut, tyy_amut, tyz_amut
    real(dp), dimension(4) :: tzx_amut, tzy_amut, tzz_amut
    real(dp), dimension(4) :: tqx_amut, tqy_amut, tqz_amut

    real(dp), dimension(4) :: resx1_amut, resy1_amut, resz1_amut, rese1_amut
    real(dp), dimension(4) :: resx2_amut, resy2_amut, resz2_amut, rese2_amut
    real(dp), dimension(4) :: resx3_amut, resy3_amut, resz3_amut, rese3_amut
    real(dp), dimension(4) :: resx4_amut, resy4_amut, resz4_amut, rese4_amut

    real(dp), dimension(5) :: qnode1, qnode2, qnode3, qnode4

    real(dp), parameter    :: my_4th = 0.25_dp
    real(dp), parameter    :: my_mxd = 0.99939_dp
    real(dp), parameter    :: my_1   = 1.0_dp
    real(dp), parameter    :: my_2   = 2.0_dp
    real(dp), parameter    :: my_3   = 3.0_dp
    real(dp), parameter    :: my_4   = 4.0_dp
    real(dp), parameter    :: my_6   = 6.0_dp
    real(dp), parameter    :: my_6th = my_1/my_6
    real(dp), parameter    :: c43    = my_4/my_3
    real(dp), parameter    :: c23    = my_2/my_3

!    real(dp)                        :: cstar, cgp
    real(dp)                        :: xmr, cgpt
!    real(dp)                        :: umu, vmu, wmu

    continue

!      cstar = sutherland_constant/tref
      xmr   = xmach/re
!      cgp   = my_1/(gm1*prandtl)
      cgpt  = my_1/(gm1*turbulent_prandtl)

!   Loop over all the cells and calculate viscous flux

      cell_loop: do cell = 1, ncell

        n = c2n(1:4,cell)

        node1 = c2n(1,cell)
        node2 = c2n(2,cell)
        node3 = c2n(3,cell)
        node4 = c2n(4,cell)

        x1 = x(node1)
        x2 = x(node2)
        x3 = x(node3)
        x4 = x(node4)

        y1 = y(node1)
        y2 = y(node2)
        y3 = y(node3)
        y4 = y(node4)

        z1 = z(node1)
        z2 = z(node2)
        z3 = z(node3)
        z4 = z(node4)

!       Lets get outward normals

        nx1 = my_6th*((y2 - y4)*(z3 - z4) - (y3 - y4)*(z2 - z4))
        ny1 = my_6th*((z2 - z4)*(x3 - x4) - (z3 - z4)*(x2 - x4))
        nz1 = my_6th*((x2 - x4)*(y3 - y4) - (x3 - x4)*(y2 - y4))

        nx2 = my_6th*((y3 - y4)*(z1 - z4) - (y1 - y4)*(z3 - z4))
        ny2 = my_6th*((z3 - z4)*(x1 - x4) - (z1 - z4)*(x3 - x4))
        nz2 = my_6th*((x3 - x4)*(y1 - y4) - (x1 - x4)*(y3 - y4))

        nx3 = my_6th*((y1 - y4)*(z2 - z4) - (y2 - y4)*(z1 - z4))
        ny3 = my_6th*((z1 - z4)*(x2 - x4) - (z2 - z4)*(x1 - x4))
        nz3 = my_6th*((x1 - x4)*(y2 - y4) - (x2 - x4)*(y1 - y4))

        nx4 = -nx1 -nx2 -nx3
        ny4 = -ny1 -ny2 -ny3
        nz4 = -nz1 -nz2 -nz3

        if (ivgrd == 1) then

!         Normalize the normals and compute dot products. If an angle
!         is detected that is bigger than 178 degrees, try ignoring
!         the contribution from this cell

          usize1 = my_1/sqrt(nx1*nx1 + ny1*ny1 + nz1*nz1)
          ux1 = nx1*usize1
          uy1 = ny1*usize1
          uz1 = nz1*usize1
          usize2 = my_1/sqrt(nx2*nx2 + ny2*ny2 + nz2*nz2)
          ux2 = nx2*usize2
          uy2 = ny2*usize2
          uz2 = nz2*usize2
          usize3 = my_1/sqrt(nx3*nx3 + ny3*ny3 + nz3*nz3)
          ux3 = nx3*usize3
          uy3 = ny3*usize3
          uz3 = nz3*usize3
          usize4 = my_1/sqrt(nx4*nx4 + ny4*ny4 + nz4*nz4)
          ux4 = nx4*usize4
          uy4 = ny4*usize4
          uz4 = nz4*usize4

          dot = ux1*ux2 + uy1*uy2 + uz1*uz2
          if (dot >= my_mxd) cycle
          dot = ux1*ux3 + uy1*uy3 + uz1*uz3
          if (dot >= my_mxd) cycle
          dot = ux1*ux4 + uy1*uy4 + uz1*uz4
          if (dot >= my_mxd) cycle
          dot = ux2*ux3 + uy2*uy3 + uz2*uz3
          if (dot >= my_mxd) cycle
          dot = ux2*ux4 + uy2*uy4 + uz2*uz4
          if (dot >= my_mxd) cycle
          dot = ux3*ux4 + uy3*uy4 + uz3*uz4
          if (dot >= my_mxd) cycle

        end if

!       Compute cell volume

        vol = (((y2-y1)*(z3-z1) - (y3-y1)*(z2-z1))*(x4-x1)                     &
              -((x2-x1)*(z3-z1) - (x3-x1)*(z2-z1))*(y4-y1)                     &
              +((x2-x1)*(y3-y1) - (x3-x1)*(y2-y1))*(z4-z1))*my_6th

!       Compute cell averaged quantities

        qnode1 = in_primitive_variables(qnode(:,node1))
        qnode2 = in_primitive_variables(qnode(:,node2))
        qnode3 = in_primitive_variables(qnode(:,node3))
        qnode4 = in_primitive_variables(qnode(:,node4))

        u1 = qnode1(n_momx)
        v1 = qnode1(n_momy)
        w1 = qnode1(n_momz)
        u2 = qnode2(n_momx)
        v2 = qnode2(n_momy)
        w2 = qnode2(n_momz)
        u3 = qnode3(n_momx)
        v3 = qnode3(n_momy)
        w3 = qnode3(n_momz)
        u4 = qnode4(n_momx)
        v4 = qnode4(n_momy)
        w4 = qnode4(n_momz)

          t1 = gamma*qnode1(5)/qnode1(1)
          t2 = gamma*qnode2(5)/qnode2(1)
          t3 = gamma*qnode3(5)/qnode3(1)
          t4 = gamma*qnode4(5)/qnode4(1)
!          rmu1 = viscosity_law( cstar, t1 )
!          rmu2 = viscosity_law( cstar, t2 )
!          rmu3 = viscosity_law( cstar, t3 )
!          rmu4 = viscosity_law( cstar, t4 )
!          eta  = my_4th*( cgp *(rmu1 + rmu2                                   &
!                              + rmu3 + rmu4)                                  &
!                        + cgpt *(amut(node1) + amut(node2)                    &
!                               + amut(node3) + amut(node4)) )

          eta_amut = my_4th*cgpt

!          rmu1 = rmu1 + amut(node1)
!          rmu2 = rmu2 + amut(node2)
!          rmu3 = rmu3 + amut(node3)
!          rmu4 = rmu4 + amut(node4)

!         rmu1_amut = (/ 1,0,0,0 /)
!         rmu2_amut = (/ 0,1,0,0 /)
!         rmu3_amut = (/ 0,0,1,0 /)
!         rmu4_amut = (/ 0,0,0,1 /)

!          rmu  = my_4th*(rmu1 + rmu2 + rmu3 + rmu4)
!          umu  = my_4th*(u1*rmu1 + u2*rmu2 + u3*rmu3 + u4*rmu4)
!          vmu  = my_4th*(v1*rmu1 + v2*rmu2 + v3*rmu3 + v4*rmu4)
!          wmu  = my_4th*(w1*rmu1 + w2*rmu2 + w3*rmu3 + w4*rmu4)

          rmu_amut = my_4th
          umu_amut(1) = my_4th*u1
          umu_amut(2) = my_4th*u2
          umu_amut(3) = my_4th*u3
          umu_amut(4) = my_4th*u4
          vmu_amut(1) = my_4th*v1
          vmu_amut(2) = my_4th*v2
          vmu_amut(3) = my_4th*v3
          vmu_amut(4) = my_4th*v4
          wmu_amut(1) = my_4th*w1
          wmu_amut(2) = my_4th*w2
          wmu_amut(3) = my_4th*w3
          wmu_amut(4) = my_4th*w4

!       Compute Gradients

        const = xmr/vol
        ux = -((u4 - u1)*nx4 + (u2 - u1)*nx2 + (u3 - u1)*nx3)*const
        vx = -((v4 - v1)*nx4 + (v2 - v1)*nx2 + (v3 - v1)*nx3)*const
        wx = -((w4 - w1)*nx4 + (w2 - w1)*nx2 + (w3 - w1)*nx3)*const
        tx = -((t4 - t1)*nx4 + (t2 - t1)*nx2 + (t3 - t1)*nx3)*const

        uy = -((u4 - u1)*ny4 + (u2 - u1)*ny2 + (u3 - u1)*ny3)*const
        vy = -((v4 - v1)*ny4 + (v2 - v1)*ny2 + (v3 - v1)*ny3)*const
        wy = -((w4 - w1)*ny4 + (w2 - w1)*ny2 + (w3 - w1)*ny3)*const
        ty = -((t4 - t1)*ny4 + (t2 - t1)*ny2 + (t3 - t1)*ny3)*const

        uz = -((u4 - u1)*nz4 + (u2 - u1)*nz2 + (u3 - u1)*nz3)*const
        vz = -((v4 - v1)*nz4 + (v2 - v1)*nz2 + (v3 - v1)*nz3)*const
        wz = -((w4 - w1)*nz4 + (w2 - w1)*nz2 + (w3 - w1)*nz3)*const
        tz = -((t4 - t1)*nz4 + (t2 - t1)*nz2 + (t3 - t1)*nz3)*const

!       Update residual

!        txx = rmu*(c43*ux - c23*vy - c23*wz)
!        txy = rmu*(uy + vx)
!        txz = rmu*(uz + wx)

        txx_amut = rmu_amut*(c43*ux - c23*vy - c23*wz)
        txy_amut = rmu_amut*(uy + vx)
        txz_amut = rmu_amut*(uz + wx)

!        tyx = rmu*(uy + vx)
!        tyy = rmu*(c43*vy - c23*ux - c23*wz)
!        tyz = rmu*(vz + wy)

        tyx_amut = rmu_amut*(uy + vx)
        tyy_amut = rmu_amut*(c43*vy - c23*ux - c23*wz)
        tyz_amut = rmu_amut*(vz + wy)

!        tzx = rmu*(uz + wx)
!        tzy = rmu*(vz + wy)
!        tzz = rmu*(c43*wz - c23*ux - c23*vy)

        tzx_amut = rmu_amut*(uz + wx)
        tzy_amut = rmu_amut*(vz + wy)
        tzz_amut = rmu_amut*(c43*wz - c23*ux - c23*vy)

!        tqx = eta*tx
!        tqy = eta*ty
!        tqz = eta*tz

        tqx_amut = eta_amut*tx
        tqy_amut = eta_amut*ty
        tqz_amut = eta_amut*tz

!        tqx = tqx                          &
!          + umu*(c43*ux - c23*vy - c23*wz) &
!          + vmu*(uy + vx)                  &
!          + wmu*(uz + wx)
!        tqy = tqy                          &
!          + umu*(uy + vx)                  &
!          + vmu*(c43*vy - c23*ux - c23*wz) &
!          + wmu*(vz + wy)
!        tqz = tqz                          &
!          + umu*(uz + wx)                  &
!          + vmu*(vz + wy)                  &
!          + wmu*(c43*wz - c23*ux - c23*vy)

        tqx_amut = tqx_amut                     &
          + umu_amut*(c43*ux - c23*vy - c23*wz) &
          + vmu_amut*(uy + vx)                  &
          + wmu_amut*(uz + wx)
        tqy_amut = tqy_amut                     &
          + umu_amut*(uy + vx)                  &
          + vmu_amut*(c43*vy - c23*ux - c23*wz) &
          + wmu_amut*(vz + wy)
        tqz_amut = tqz_amut                     &
          + umu_amut*(uz + wx)                  &
          + vmu_amut*(vz + wy)                  &
          + wmu_amut*(c43*wz - c23*ux - c23*vy)


        k_function : do k = 1, nfunc
        cell_node : do i = 1,4
        if ( n(i) > nnodes0 ) cycle cell_node
!        if (node1 <= nnodes0) then
!          res(n_momx,node1) = res(n_momx,node1) - (nx1*txx + ny1*txy + nz1*txz)
!          res(n_momy,node1) = res(n_momy,node1) - (nx1*tyx + ny1*tyy + nz1*tyz)
!          res(n_momz,node1) = res(n_momz,node1) - (nx1*tzx + ny1*tzy + nz1*tzz)
!          res(n_etot,node1) = res(n_etot,node1) - (nx1*tqx + ny1*tqy + nz1*tqz)
!        end if
        resx1_amut = - (nx1*txx_amut + ny1*txy_amut + nz1*txz_amut)
        resy1_amut = - (nx1*tyx_amut + ny1*tyy_amut + nz1*tyz_amut)
        resz1_amut = - (nx1*tzx_amut + ny1*tzy_amut + nz1*tzz_amut)
        rese1_amut = - (nx1*tqx_amut + ny1*tqy_amut + nz1*tqz_amut)

        res_lam(n(i),k) = res_lam(n(i),k) &
          + resx1_amut(i)*coltag(n_momx,node1)*rlam(n_momx,node1,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resy1_amut(i)*coltag(n_momy,node1)*rlam(n_momy,node1,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resz1_amut(i)*coltag(n_momz,node1)*rlam(n_momz,node1,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + rese1_amut(i)*coltag(n_etot,node1)*rlam(n_etot,node1,k)

!        if (node2 <= nnodes0) then
!          res(n_momx,node2) = res(n_momx,node2) - (nx2*txx + ny2*txy + nz2*txz)
!          res(n_momy,node2) = res(n_momy,node2) - (nx2*tyx + ny2*tyy + nz2*tyz)
!          res(n_momz,node2) = res(n_momz,node2) - (nx2*tzx + ny2*tzy + nz2*tzz)
!          res(n_etot,node2) = res(n_etot,node2) - (nx2*tqx + ny2*tqy + nz2*tqz)
!        end if
        resx2_amut = - (nx2*txx_amut + ny2*txy_amut + nz2*txz_amut)
        resy2_amut = - (nx2*tyx_amut + ny2*tyy_amut + nz2*tyz_amut)
        resz2_amut = - (nx2*tzx_amut + ny2*tzy_amut + nz2*tzz_amut)
        rese2_amut = - (nx2*tqx_amut + ny2*tqy_amut + nz2*tqz_amut)

        res_lam(n(i),k) = res_lam(n(i),k) &
          + resx2_amut(i)*coltag(n_momx,node2)*rlam(n_momx,node2,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resy2_amut(i)*coltag(n_momy,node2)*rlam(n_momy,node2,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resz2_amut(i)*coltag(n_momz,node2)*rlam(n_momz,node2,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + rese2_amut(i)*coltag(n_etot,node2)*rlam(n_etot,node2,k)

!        if (node3 <= nnodes0) then
!          res(n_momx,node3) = res(n_momx,node3) - (nx3*txx + ny3*txy + nz3*txz)
!          res(n_momy,node3) = res(n_momy,node3) - (nx3*tyx + ny3*tyy + nz3*tyz)
!          res(n_momz,node3) = res(n_momz,node3) - (nx3*tzx + ny3*tzy + nz3*tzz)
!          res(n_etot,node3) = res(n_etot,node3) - (nx3*tqx + ny3*tqy + nz3*tqz)
!        end if
        resx3_amut = - (nx3*txx_amut + ny3*txy_amut + nz3*txz_amut)
        resy3_amut = - (nx3*tyx_amut + ny3*tyy_amut + nz3*tyz_amut)
        resz3_amut = - (nx3*tzx_amut + ny3*tzy_amut + nz3*tzz_amut)
        rese3_amut = - (nx3*tqx_amut + ny3*tqy_amut + nz3*tqz_amut)

        res_lam(n(i),k) = res_lam(n(i),k) &
          + resx3_amut(i)*coltag(n_momx,node3)*rlam(n_momx,node3,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resy3_amut(i)*coltag(n_momy,node3)*rlam(n_momy,node3,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resz3_amut(i)*coltag(n_momz,node3)*rlam(n_momz,node3,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + rese3_amut(i)*coltag(n_etot,node3)*rlam(n_etot,node3,k)

!        if (node4 <= nnodes0) then
!          res(n_momx,node4) = res(n_momx,node4) - (nx4*txx + ny4*txy + nz4*txz)
!          res(n_momy,node4) = res(n_momy,node4) - (nx4*tyx + ny4*tyy + nz4*tyz)
!          res(n_momz,node4) = res(n_momz,node4) - (nx4*tzx + ny4*tzy + nz4*tzz)
!          res(n_etot,node4) = res(n_etot,node4) - (nx4*tqx + ny4*tqy + nz4*tqz)
!        end if
        resx4_amut = - (nx4*txx_amut + ny4*txy_amut + nz4*txz_amut)
        resy4_amut = - (nx4*tyx_amut + ny4*tyy_amut + nz4*tyz_amut)
        resz4_amut = - (nx4*tzx_amut + ny4*tzy_amut + nz4*tzz_amut)
        rese4_amut = - (nx4*tqx_amut + ny4*tqy_amut + nz4*tqz_amut)

        res_lam(n(i),k) = res_lam(n(i),k) &
          + resx4_amut(i)*coltag(n_momx,node4)*rlam(n_momx,node4,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resy4_amut(i)*coltag(n_momy,node4)*rlam(n_momy,node4,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + resz4_amut(i)*coltag(n_momz,node4)*rlam(n_momz,node4,k)
        res_lam(n(i),k) = res_lam(n(i),k) &
          + rese4_amut(i)*coltag(n_etot,node4)*rlam(n_etot,node4,k)

      end do cell_node
      end do k_function
      end do cell_loop

  end subroutine dres_damut_lambda

!=============================================================================80
!
! This routine computes gradients used in the turb model
! expects conservative
! adds terms to the adjoint residual from grad pieces
! res_gradx, res_grady, res_gradz need lmpi_xfer before call
!
!=============================================================================80

  subroutine dturbgrad_dq( grid, soln, sadj, nfunc, &
    res_gradx, res_grady, res_gradz)

    use kinddefs,              only : dp
    use grid_types,            only : grid_type
    use solution_types,        only : soln_type
    use solution_adj,          only : sadj_type
    use periodics,             only : periodic
    use info_depr,             only : twod
    use element_based_bc_util, only : element_based_metrics
    use bc_names,              only : bc_ignore_2d, bc_is_periodic
    use twod_util,             only : yplane_2d, y_coplanar_tol
    use lmpi,                  only : lmpi_conditional_stop
    use thermo,                only : dprimitive_dconserved

    type(grid_type),                              intent(in)    :: grid
    type(soln_type),                              intent(in)    :: soln
    type(sadj_type),                              intent(inout) :: sadj
    integer,                                      intent(in)    :: nfunc
    real(dp),  dimension(5,grid%nnodes01,nfunc),  intent(in)    :: res_gradx
    real(dp),  dimension(5,grid%nnodes01,nfunc),  intent(in)    :: res_grady
    real(dp),  dimension(5,grid%nnodes01,nfunc),  intent(in)    :: res_gradz

    integer :: nedge_flux_eval, n
    integer :: node1, node2
    real(dp) :: xnorm, ynorm, znorm
    integer :: ib
    real(dp) :: area

    integer                :: triangle_index
    integer,  dimension(3) :: triangle_node
    real(dp), dimension(3) :: triangle_weight

    integer                :: quad_index
    integer,  dimension(4) :: quad_node
    real(dp), dimension(4) :: quad_weight
    real(dp)               :: face_speed

    real(dp), dimension(5) :: state
    real(dp), dimension(5,5) :: dQdq

    integer :: i, j, k
    integer :: qn, gn

    real(dp), parameter    :: half = 0.5_dp

    continue

!      gradx(2:4,:) = 0._dp
!      grady(2:4,:) = 0._dp
!      gradz(2:4,:) = 0._dp

    nedge_flux_eval = grid%nedgeloc
    if ( twod ) nedge_flux_eval = grid%nedgeloc_2d

    do n = 1, nedge_flux_eval

      node1 = grid%eptr(1,n)
      node2 = grid%eptr(2,n)

      xnorm = grid%ra(n) * grid%xn(n)
      ynorm = grid%ra(n) * grid%yn(n)
      znorm = grid%ra(n) * grid%zn(n)

!        q2 = 0.5_dp * (soln%qnode(2,node2)+soln%qnode(2,node1))
!        q3 = 0.5_dp * (soln%qnode(3,node2)+soln%qnode(3,node1))
!        q4 = 0.5_dp * (soln%qnode(4,node2)+soln%qnode(4,node1))

!          gradx(2,node1) = gradx(2,node1) + xnorm*q2 / vol(node1)
!          gradx(3,node1) = gradx(3,node1) + xnorm*q3 / vol(node1)
!          gradx(4,node1) = gradx(4,node1) + xnorm*q4 / vol(node1)

!          grady(2,node1) = grady(2,node1) + ynorm*q2 / vol(node1)
!          grady(3,node1) = grady(3,node1) + ynorm*q3 / vol(node1)
!          grady(4,node1) = grady(4,node1) + ynorm*q4 / vol(node1)

!          gradz(2,node1) = gradz(2,node1) + znorm*q2 / vol(node1)
!          gradz(3,node1) = gradz(3,node1) + znorm*q3 / vol(node1)
!          gradz(4,node1) = gradz(4,node1) + znorm*q4 / vol(node1)

!          gradx(2,node2) = gradx(2,node2) - xnorm*q2 / vol(node2)
!          gradx(3,node2) = gradx(3,node2) - xnorm*q3 / vol(node2)
!          gradx(4,node2) = gradx(4,node2) - xnorm*q4 / vol(node2)

!          grady(2,node2) = grady(2,node2) - ynorm*q2 / vol(node2)
!          grady(3,node2) = grady(3,node2) - ynorm*q3 / vol(node2)
!          grady(4,node2) = grady(4,node2) - ynorm*q4 / vol(node2)

!          gradz(2,node2) = gradz(2,node2) - znorm*q2 / vol(node2)
!          gradz(3,node2) = gradz(3,node2) - znorm*q3 / vol(node2)
!          gradz(4,node2) = gradz(4,node2) - znorm*q4 / vol(node2)

! refactored and grouped by q node

! gradx(:,node1) = gradx(:,node1) + xnorm*half*q(:,node1) / vol(node1)
! gradx(:,node2) = gradx(:,node2) - xnorm*half*q(:,node1) / vol(node2)

! gradx(:,node1) = gradx(:,node1) + xnorm*half*q(:,node2) / vol(node1)
! gradx(:,node2) = gradx(:,node2) - xnorm*half*q(:,node2) / vol(node2)

! vol node is the grad node
! sign is the grad node
! q node is the adjoint residual node


      adj_res_node1 : if (node1 <= grid%nnodes0) then

        state = in_primitive_variables( soln%q_dof(1:5,node1) )
        call dprimitive_dconserved( state, dQdq )

        node1_k : do k = 1, nfunc
          do i = 1, 5
            do j = 1, 5
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + xnorm*half*dQdq(j,i) / grid%vol(node1)*res_gradx(j,node1,k)
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + xnorm*half*dQdq(j,i) / grid%vol(node2)*res_gradx(j,node2,k)

              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + ynorm*half*dQdq(j,i) / grid%vol(node1)*res_grady(j,node1,k)
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + ynorm*half*dQdq(j,i) / grid%vol(node2)*res_grady(j,node2,k)

              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + znorm*half*dQdq(j,i) / grid%vol(node1)*res_gradz(j,node1,k)
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + znorm*half*dQdq(j,i) / grid%vol(node2)*res_gradz(j,node2,k)
            end do
          end do
        end do node1_k

      end if adj_res_node1

      adj_res_node2 : if (node2 <= grid%nnodes0) then

        state = in_primitive_variables( soln%q_dof(1:5,node2) )
        call dprimitive_dconserved( state, dQdq )

        node2_k : do k = 1, nfunc
          do i = 1, 5
            do j = 1, 5
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                - xnorm*half*dQdq(j,i) / grid%vol(node1)*res_gradx(j,node1,k)
              sadj%res(i,node2,k) = sadj%res(i,node1,k) &
                - xnorm*half*dQdq(j,i) / grid%vol(node2)*res_gradx(j,node2,k)

              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                - ynorm*half*dQdq(j,i) / grid%vol(node1)*res_grady(j,node1,k)
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                - ynorm*half*dQdq(j,i) / grid%vol(node2)*res_grady(j,node2,k)

              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                - znorm*half*dQdq(j,i) / grid%vol(node1)*res_gradz(j,node1,k)
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                - znorm*half*dQdq(j,i) / grid%vol(node2)*res_gradz(j,node2,k)
            end do
          end do
        end do node2_k

      end if adj_res_node2

    end do

    do ib = 1,grid%nbound
      if ( twod .and. bc_ignore_2d(grid%bc(ib)%ibc) ) cycle
      if ( bc_is_periodic(grid%bc(ib)%ibc) ) cycle

      loop_tris : do triangle_index = 1,grid%bc(ib)%nbfacet

        corner_tris_loop : do gn = 1, 3

          call element_based_metrics ( grid%bc, grid%elem                      &
            , grid%x, grid%y, grid%z                                           &
            , grid%dxdt, grid%dydt, grid%dzdt                                  &
            , ib, triangle_index, gn                                           &
            , 3, triangle_node, triangle_weight                                &
            , xnorm, ynorm, znorm, area, face_speed )

!            call element_based_qi( nvalues, qi, q_dof,               &
!              3, triangle_node, triangle_weight )
!            qi(:) = 0._dp
!            do face_node = 1, 3
!              qi(1:n_q) = qi(1:n_q) &
!                + triangle_weight(face_node) &
!                * q_dof(1:n_q,triangle_node(face_node))
!            end do

!            gradx(2:4,triangle_node(1)) = gradx(2:4,triangle_node(1)) &
!                                        + xnorm*area*qi(2:4)          &
!                                        / vol(triangle_node(1))
!            grady(2:4,triangle_node(1)) = grady(2:4,triangle_node(1)) &
!                                        + ynorm*area*qi(2:4)          &
!                                        / vol(triangle_node(1))
!            gradz(2:4,triangle_node(1)) = gradz(2:4,triangle_node(1)) &
!                                        + znorm*area*qi(2:4)          &
!                                        / vol(triangle_node(1))
!          if( triangle_node(1) > soln%dof0 ) cycle

!            gradx(:,triangle_node(gn)) = gradx(:,triangle_node(gn))    &
!              + xnorm*area*triangle_weight(qn)* q(:,triangle_node(qn)) &
!                                        / vol(triangle_node(gn))

          do qn = 1, 3
            tri_qnode_local : if ( triangle_node(qn) <= grid%nnodes0 ) then

              state= in_primitive_variables( soln%q_dof(1:5,triangle_node(qn)) )
              call dprimitive_dconserved( state, dQdq )

              do k = 1, nfunc
                do i = 1, 5
                  do j = 1, 5
                    sadj%res(i,triangle_node(qn),k)              &
                      = sadj%res(i,triangle_node(qn),k)          &
                      + xnorm*area*triangle_weight(qn)*dQdq(j,i) &
                      / grid%vol(triangle_node(gn))              &
                      * res_gradx(j,triangle_node(gn),k)
                    sadj%res(i,triangle_node(qn),k)              &
                      = sadj%res(i,triangle_node(qn),k)          &
                      + ynorm*area*triangle_weight(qn)*dQdq(j,i) &
                      / grid%vol(triangle_node(gn))              &
                      * res_grady(j,triangle_node(gn),k)
                    sadj%res(i,triangle_node(qn),k)              &
                      = sadj%res(i,triangle_node(qn),k)          &
                      + znorm*area*triangle_weight(qn)*dQdq(j,i) &
                      / grid%vol(triangle_node(gn))              &
                      * res_gradz(j,triangle_node(gn),k)
                  end do
                end do
              end do
            end if tri_qnode_local
          end do

        enddo corner_tris_loop

      enddo loop_tris

      loop_quads : do quad_index = 1,grid%bc(ib)%nbfaceq

        corner_quad_loop : do gn = 1, 4

          skip_other_twod_plane : if (twod) then
            node1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(quad_index,&
                                                         gn))
            if( abs(grid%y(node1)-yplane_2d) >= y_coplanar_tol ) &
              cycle corner_quad_loop
          end if skip_other_twod_plane

          call element_based_metrics ( grid%bc, grid%elem                      &
            , grid%x, grid%y, grid%z                                           &
            , grid%dxdt, grid%dydt, grid%dzdt                                  &
            , ib, quad_index, gn                                               &
            , 4, quad_node, quad_weight                                        &
            , xnorm, ynorm, znorm, area, face_speed )

!            call element_based_qi( nvalues, qi, q_dof,  &
!                                   4, quad_node, quad_weight )
!            qi(:) = 0._dp
!            do face_node = 1, 4
!              qi(1:n_q) = qi(1:n_q) &
!                + quad_weight(face_node)  &
!                * q_dof(1:n_q,quad_node(face_node))
!            end do

!          if(quad_node(1) > soln%dof0 ) cycle

!            gradx(2:4,quad_node(1)) = gradx(2:4,quad_node(1)) &
!                                    + xnorm*area*qi(2:4)      &
!                                    / vol(quad_node(1))
!            grady(2:4,quad_node(1)) = grady(2:4,quad_node(1)) &
!                                    + ynorm*area*qi(2:4)      &
!                                    / vol(quad_node(1))
!            gradz(2:4,quad_node(1)) = gradz(2:4,quad_node(1)) &
!                                    + znorm*area*qi(2:4)      &
!                                    / vol(quad_node(1))

          do qn = 1, 4
            quad_qnode_local : if ( quad_node(qn) <= grid%nnodes0 ) then

              state= in_primitive_variables( soln%q_dof(1:5,quad_node(qn)) )
              call dprimitive_dconserved( state, dQdq )

              do k = 1, nfunc
                do i = 1, 5
                  do j = 1, 5
                    sadj%res(i,quad_node(qn),k)              &
                      = sadj%res(i,quad_node(qn),k)          &
                      + xnorm*area*quad_weight(qn)*dQdq(j,i) &
                      / grid%vol(quad_node(gn))              &
                      * res_gradx(j,quad_node(gn),k)
                    sadj%res(i,quad_node(qn),k)              &
                      = sadj%res(i,quad_node(qn),k)          &
                      + ynorm*area*quad_weight(qn)*dQdq(j,i) &
                      / grid%vol(quad_node(gn))              &
                      * res_grady(j,quad_node(gn),k)
                    sadj%res(i,quad_node(qn),k)              &
                      = sadj%res(i,quad_node(qn),k)          &
                      + znorm*area*quad_weight(qn)*dQdq(j,i) &
                      / grid%vol(quad_node(gn))              &
                      * res_gradz(j,quad_node(gn),k)
                  end do
                end do
              end do
            end if quad_qnode_local
          end do

        enddo corner_quad_loop
      end do loop_quads
    enddo

    if (twod) then
      call lmpi_conditional_stop(1,'implement twod in dturbgrad_dq')
!        do i = 1,nnodes0_2d
!          gradx(2:4,node_pairs_2d(2,i)) = gradx(2:4,node_pairs_2d(1,i))
!          gradx(2:4,node_pairs_2d(2,i)) = gradx(2:4,node_pairs_2d(1,i))
!          gradx(2:4,node_pairs_2d(2,i)) = gradx(2:4,node_pairs_2d(1,i))
!        end do
    end if

    if ( periodic ) then
      call lmpi_conditional_stop(1,'implement periodic in dturbgrad_dq')
    end if

! is this right on solid walls with specified velocity?

  end subroutine dturbgrad_dq

include 'in_primitive_variables.f90'
include 'get_q_ddt.f90'
include 'get_p.f90'
include 'mut_kw_ddt.f90'

end module residual_turb_consol

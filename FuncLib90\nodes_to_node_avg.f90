!============================= NODES_TO_NODE_AVG =============================80
!
! Set nodes to average array.
!
!=============================================================================80

  pure function nodes_to_node_avg( face, nface, fptr, y )

    use twod_util,      only : q_2d

    integer,                     intent(in) :: face, nface
    integer, dimension(6,nface), intent(in) :: fptr
    real(dp), dimension(:),      intent(in) :: y

    integer :: n_face_nodes, node, ii, ii_loop

    integer, dimension(5) :: nodes_to_node_avg

  continue

    nodes_to_node_avg(3:4) = 0

    !skip nonvalid entries in fptr(3:6,face)
    ii_loop = 6
    if ( fptr(3,face) == fptr(6,face) ) ii_loop = 5

    n_face_nodes = 0
    do ii=3,ii_loop
      node=fptr(ii,face)
      if ( q_2d .and. abs( y(node) ) > 1.0e-05_dp ) cycle !skip off-plane 2D
      n_face_nodes = n_face_nodes + 1
      nodes_to_node_avg(n_face_nodes) = node
    enddo

    nodes_to_node_avg(5) = n_face_nodes

  end function nodes_to_node_avg

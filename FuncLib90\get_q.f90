!====================== GET_Q ================================================80
!
! Both compressible (eqn_set = 0) and incompressible (eqn_set = 1)
! Return primitives for either q_type
!
!=============================================================================80
  pure function get_q ( eqn_set, n_tot, n_turb, qnode, qturb )     &
           result ( q )

    use kinddefs,       only : dp
    use solution_types, only : compressible, incompressible
    use thermo,         only : conserved_q_type, q_type

    integer,                     intent(in)  :: eqn_set
    integer,                     intent(in)  :: n_tot
    integer,                     intent(in)  :: n_turb
    real(dp), dimension(n_tot),  intent(in)  :: qnode
    real(dp), dimension(n_turb), intent(in)  :: qturb

    real(dp), dimension(n_tot+n_turb)        :: q

    real(dp), parameter                      :: one = 1.0_dp

    continue

    q(1:n_tot) = qnode(1:n_tot)

    if ( eqn_set == compressible .and. q_type == conserved_q_type ) then
      q(1:5) = in_primitive_variables( qnode(1:5) )
    end if

    if ( eqn_set == incompressible ) then
      q(1) = one
      q(5) = qnode(1)
    endif

    q(n_tot+1:n_tot+n_turb) = qturb(1:n_turb)

  end function get_q

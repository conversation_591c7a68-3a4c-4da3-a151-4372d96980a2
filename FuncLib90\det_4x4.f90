!================================== DET_4x4 ==================================80
!
! Function to find the determinant of a 4x4 matrix whose coefficients are
! a, b, c and d
!
!           | a1 b1 c1 d1 |
!           | a2 b2 c2 d2 |      | b2 c2 d2 |      | a2 c2 d2 |
!           | a3 b3 c3 d3 |   a1*| b3 c3 d3 | - b1*| a3 c3 d3 | +
! det_4x4 = | a4 b4 c4 d4 | =    | b4 c4 d4 |      | a4 c4 d4 |
!
!                                | a2 b2 d2 |      | a2 b2 c2 |
!                             c1*| a3 b3 d3 | - d1*| a3 b3 c3 |
!                                | a4 b4 d4 |      | a4 b4 c4 |
!
!=============================================================================80

  pure function det_4x4(a1, a2, a3, a4, b1, b2, b3, b4,                        &
                        c1, c2, c3, c4, d1, d2, d3, d4)

    use kinddefs, only : dp

    real(dp), intent(in) :: a1, a2, a3, a4, b1, b2, b3, b4
    real(dp), intent(in) :: c1, c2, c3, c4, d1, d2, d3, d4
    real(dp)             :: det_4x4

  continue

    det_4x4 = + a1*det_3x3(b2,b3,b4,c2,c3,c4,d2,d3,d4)                         &
              - b1*det_3x3(a2,a3,a4,c2,c3,c4,d2,d3,d4)                         &
              + c1*det_3x3(a2,a3,a4,b2,b3,b4,d2,d3,d4)                         &
              - d1*det_3x3(a2,a3,a4,b2,b3,b4,c2,c3,c4)

  end function det_4x4

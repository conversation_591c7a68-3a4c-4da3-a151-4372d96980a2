module init_adjs

  implicit none

  private

  public :: init_adj

contains

!==================================== INIT_ADJ ===============================80
!
! Initializes the adjoint variable lambda
!
!=============================================================================80

  subroutine init_adj(maxnodes,nfunctions,rlam,adim,eqn_set,n_turb,x,y,z)

    use exact_defs,     only : ic_y_symmetry
    use kinddefs,       only : dp
    use lmpi,           only : lmpi_master
    use solution_types, only : compressible

    integer, intent(in) :: maxnodes, adim, nfunctions, eqn_set, n_turb

    real(dp), dimension(:),                        intent(in)  :: x,y,z
    real(dp), dimension(adim,maxnodes,nfunctions), intent(out) :: rlam

    integer :: i, j, k

    real(dp) :: term_xz

  continue

    do k = 1, nfunctions
      do j = 1, maxnodes
        do i = 1, adim
          rlam(i,j,k) = 0.0_dp
        end do
       end do
    end do

! Optionally initialize lambda differently

    if ( ic_y_symmetry ) then

      if( lmpi_master ) write(*,*) '...Re-initializing lambda (symmetry).'

      do i = 1, maxnodes
        !...symmetric terms
        term_xz = 0.001_dp*(x(i)**1)*(z(i)**2)
        rlam(1,i,1) = 1.0_dp + (y(i)**2)*term_xz
        term_xz = 0.001_dp*(x(i)**2)*(z(i)**3)
        rlam(2,i,1) = 1.0_dp + (y(i)**4)*term_xz
        term_xz = 0.001_dp*(x(i)**3)*(z(i)**4)
        rlam(4,i,1) = 1.0_dp + (y(i)**2)*term_xz
        if ( eqn_set == compressible ) then
          term_xz = 0.001_dp*(x(i)**4)*(z(i)**1)
          rlam(5,i,1) = 1.0_dp + (y(i)**6)*term_xz
        endif
        !...antisymmetric terms
        rlam(3,i,1) = 0.001_dp*y(i)
      end do

      if ( n_turb > 0 ) then
        if ( lmpi_master ) write(*,*) 'Also re-initializing turbulent lambda.'
        k = 5
        if( eqn_set == compressible ) k = 6
        do j = k, adim
          do i = 1, maxnodes
            !...symmetric terms
            term_xz = 0.0001_dp*(x(i)**2)*(z(i)**2)
            rlam(j,i,1) = 1.0_dp + (y(i)**2)*term_xz
          enddo
        enddo
      endif

    endif

  end subroutine init_adj

end module init_adjs

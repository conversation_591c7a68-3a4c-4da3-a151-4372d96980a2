module exact_defs

  use kinddefs,          only : dp

  implicit none

  private

  public :: exact_solution
  public :: truncation_error, discretization_error
  public :: dirichlet_bc_override
  public :: lisbon_ms1, lisbon_ms2, lisbon_ms4, lisbon_backstep
  public :: lisbon_profile_at_x, lisbon_profile_at_z
  public :: u_scale, v_scale, w_scale, p_scale, t_scale, turb_scale
  public :: u_polyf, v_polyf, w_polyf,                   &
            p_polyf, t_polyf, turb_polyf,                &
            u_sinef, v_sinef, w_sinef,                   &
            p_sinef, t_sinef, turb_sinef,                &
            u_cosinef, v_cosinef, w_cosinef,             &
            p_cosinef, t_cosinef, turb_cosinef
  public :: u_expblf, v_expblf, w_expblf,                   &
            p_expblf, t_expblf, turb_expblf
  public :: null_mms_forcing_terms, value_faults, mpi_error
  public :: stagnation_type, debug_forcing, exact_freestream
  public :: exact_polyf, exact_sinef, exact_cosinef
  public :: exact_expblf
  public :: simple_sine, simple_sine_radial, simple_radial
  public :: farfield_cl, farfield_point_vortex

  public :: ic_exact
  public :: ic_inv_stag_le, ic_inv_stag_te
  public :: ic_y_symmetry, ic_z_symmetry, output_symmetry_residuals

  public :: exact_turb_exprsq, turb_exprsq, turb_exprsq_damping
  public :: exact_turb_reentrant, turb_reentrant, turb_reentrant_exterior_angle
  public :: turb_reentrant_alpha, turb_reentrant_right_corner
  public :: x_te, z_te

  logical :: ic_exact        = .false.  ! An exact solution exists - use it!
  logical :: ic_inv_stag_le  = .false.  ! Use initial conditions of 2D
                                        ! incompressible plane LE stagnation
  logical :: ic_inv_stag_te  = .false.  ! Use initial conditions of 2D
                                        ! incompressible plane TE stagnation
  logical :: ic_y_symmetry   = .false.  ! Use initial conditions to test
                                        ! y-symmetry
  logical :: ic_z_symmetry   = .false.  ! Use initial conditions to test
                                        ! z-symmetry
  logical :: output_symmetry_residuals  ! Output norms of residuals on
                                        ! planes x/y/z=0

  logical :: exact_solution       = .false.
  logical :: truncation_error     = .false.
  logical :: discretization_error = .false.

  logical :: exact_freestream = .false.
  logical :: exact_polyf      = .false.
  logical :: exact_sinef      = .false.
  logical :: exact_cosinef    = .false.
  logical :: exact_expblf     = .false.

  logical :: simple_sine        = .false.
  logical :: simple_sine_radial = .false.
  logical :: simple_radial      = .false.

  real(dp) :: u_scale, v_scale, w_scale, p_scale, t_scale, turb_scale

  integer :: value_faults = 0, mpi_error = 0

  logical :: debug_forcing = .false.

  !To override all boundary conditions in node-centered path with Dirichlet.
  logical :: dirichlet_bc_override = .false.

  real(dp), dimension(13) :: u_polyf = 0._dp
  real(dp), dimension(13) :: v_polyf = 0._dp
  real(dp), dimension(13) :: w_polyf = 0._dp
  real(dp), dimension(13) :: p_polyf = 0._dp
  real(dp), dimension(13) :: t_polyf = 0._dp
  real(dp), dimension(13) :: turb_polyf = 0._dp

  real(dp), dimension(3) :: u_sinef = 0._dp
  real(dp), dimension(3) :: v_sinef = 0._dp
  real(dp), dimension(3) :: w_sinef = 0._dp
  real(dp), dimension(3) :: p_sinef = 0._dp
  real(dp), dimension(3) :: t_sinef = 0._dp
  real(dp), dimension(3) :: turb_sinef = 0._dp

  real(dp), dimension(3) :: u_cosinef = 0._dp
  real(dp), dimension(3) :: v_cosinef = 0._dp
  real(dp), dimension(3) :: w_cosinef = 0._dp
  real(dp), dimension(3) :: p_cosinef = 0._dp
  real(dp), dimension(3) :: t_cosinef = 0._dp
  real(dp), dimension(3) :: turb_cosinef = 0._dp

  real(dp) :: u_expblf = 0._dp
  real(dp) :: v_expblf = 0._dp
  real(dp) :: w_expblf = 0._dp
  real(dp) :: p_expblf = 0._dp
  real(dp) :: t_expblf = 0._dp
  real(dp) :: turb_expblf = 0._dp

  real(dp) :: farfield_cl = 0._dp
  integer  :: farfield_point_vortex = 0

  integer :: stagnation_type = 0

  real(dp) :: lisbon_profile_at_x = 0.875_dp
  real(dp) :: lisbon_profile_at_z = 0.100_dp
  logical :: lisbon_ms1 = .false.
  logical :: lisbon_ms2 = .false.
  logical :: lisbon_ms4 = .false.
  logical :: lisbon_backstep = .false.

  !...To allow exact (or reference) solution but zero the forcing terms.
  !...(For instance, to specify inflow and outflow quantities)
  logical :: null_mms_forcing_terms = .false.

  logical :: exact_turb_exprsq    = .false.

  real(dp) :: turb_exprsq         = 0._dp
  real(dp) :: turb_exprsq_damping = 1._dp

  logical :: exact_turb_reentrant    = .false.
  logical :: turb_reentrant_right_corner = .false.

  real(dp) :: turb_reentrant                =   0._dp
  real(dp) :: turb_reentrant_exterior_angle = 360._dp
  real(dp) :: turb_reentrant_alpha          = -10._dp
  real(dp) :: x_te                          =   0._dp
  real(dp) :: z_te                          =   0._dp


end module exact_defs


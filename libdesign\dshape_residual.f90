module dshape_residual

  implicit none

  private

  public :: drdge<PERSON>, d<PERSON><PERSON><PERSON>, drdnoninrates

contains

!================================= DRDGEOM ===================================80
!
!  This routine computes the matrix-vector product
!
!                               t
!                        {dR/dX}  * {Lambda}
!
!=============================================================================80
      subroutine drdgeom(eqn_set,ntp,nnodes0,nnodes01,nedge,nedgeLoc,gradx,    &
                         grady,gradz,eptr,qnode,x,y,z,xn,yn,zn,ra,rlam,amut,ia,&
                         ia_ns,ja,coltag,slen,turb,volume,dft1,dft2,nbound,bc, &
                         nfunctions,grid,iflagslen,sourceterm_sum,l2g,         &
                         nedgeloc_2d,nnodes0_2d,node_pairs_2d,nelem,elem,phi,  &
                         ndim,adim,n_turb,n_tot,n_grd,facespeed,dxdt,dydt,dzdt,&
                         drdxl,pointsum,rn,np,debug,n_q,symmetry,r11,r12,r13,  &
                         r22,r23,r33)

      use info_depr,           only : ivisc
      use grid_motion_helpers, only : need_grid_velocity
      use nml_global,          only : moving_grid
      use kinddefs,            only : dp
      use bc_types,            only : bcgrid_type
      use lmpi,                only : lmpi_die
      use dshape_inviscid,     only : drdgeominv, drdgeominvi, bc_drdgeominv,  &
                                      bc_drdgeominvi, dwhat_dxyz, edge_stencil
      use dshape_laminar,      only : drdgeomv_mix, bc_viscous_wall,           &
                                      bc_drdgeomv_mix, bc_viscous_walli,       &
                                      drdgeomv_mix_i, bc_drdgeomv_mix_i
      use dshape_turbulent,    only : turbresxyz
      use allocations,         only : my_alloc_ptr
      use grid_types,          only : grid_type
      use element_types,       only : elem_type
      use bc_names,            only : bc_has_visc_flux_closure
      use solution_types,      only : compressible, incompressible, generic_gas
      use nml_noninertial_reference_frame, only : noninertial

      integer, intent(in) :: eqn_set,ntp
      integer, intent(in) :: ndim, adim, n_turb, n_tot, n_grd
      integer, intent(in) :: nnodes0,nnodes01,nedge,nedgeloc
      integer, intent(in) :: nedgeloc_2d, nnodes0_2d
      integer, intent(in) :: nbound,nfunctions
      integer, intent(in) :: nelem,n_q

      integer, intent(inout) :: rn, np

      real(dp), dimension(6,3), intent(inout) :: pointsum

      real(dp), dimension(ndim,nnodes01),            intent(in)    :: qnode
      real(dp), dimension(ndim,nnodes01),            intent(inout) :: gradx
      real(dp), dimension(ndim,nnodes01),            intent(inout) :: grady
      real(dp), dimension(ndim,nnodes01),            intent(inout) :: gradz
      real(dp), dimension(ndim,nnodes01),            intent(in)    :: phi
      real(dp), dimension(nnodes01),                 intent(in)    :: x,y,z
      real(dp), dimension(nnodes0),                  intent(in)    :: r11,r12
      real(dp), dimension(nnodes0),                  intent(in)    :: r13,r22
      real(dp), dimension(nnodes0),                  intent(in)    :: r23,r33
      real(dp), dimension(nedge),                    intent(inout) :: xn,yn,zn
      real(dp), dimension(nedgeloc),                 intent(inout) :: facespeed
      real(dp), dimension(nedge),                    intent(inout) :: ra
      real(dp), dimension(adim,nnodes01,nfunctions), intent(in)    :: rlam
      real(dp), dimension(nnodes01),                 intent(in)    :: amut
      real(dp), dimension(nnodes01),                 intent(in)    :: dxdt
      real(dp), dimension(nnodes01),                 intent(in)    :: dydt
      real(dp), dimension(nnodes01),                 intent(in)    :: dzdt
      real(dp), dimension(adim,nnodes01),            intent(in)    :: coltag
      real(dp), dimension(n_turb,nnodes01),          intent(in)    :: turb
      real(dp), dimension(nnodes01),                 intent(in)    :: volume
      real(dp), dimension(nedge),                    intent(inout) :: dft1,dft2
      real(dp), dimension(nnodes01),                 intent(in)    :: slen
      real(dp), dimension(3,nnodes01,nfunctions),intent(out)   :: sourceterm_sum
      real(dp), dimension(3,nnodes01,nfunctions,ntp),intent(inout) :: drdxl

      integer,     dimension(2,nedge),           intent(in) :: eptr
      integer,     dimension(:),                 intent(in) :: ia,ja,ia_ns
      integer,     dimension(nnodes01),          intent(in) :: iflagslen
      integer,     dimension(nnodes01),          intent(in) :: symmetry
      integer,     dimension(nnodes01),          intent(in) :: l2g
      integer,     dimension(2,nnodes0_2d),      intent(in) :: node_pairs_2d

      type(grid_type), intent(in) :: grid

      type(bcgrid_type), dimension(nbound), intent(in) :: bc

      type(elem_type),   dimension(nelem),  intent(in) :: elem

      logical, intent(in) :: debug

      integer :: ib,ielem,i

      real(dp), dimension(:,:),     pointer, save :: dfdkx,dfdky,dfdkz

  continue

      if ( moving_grid .or. noninertial ) then
        call dwhat_dxyz(ntp,nedgeloc,nelem,elem,x,y,z,eptr,xn,yn,zn,ra,        &
                        facespeed,dxdt,dydt,dzdt)
      else
        allocate(edge_stencil(1))
      endif

      if ( debug ) then
        do i = 1, 6
          write(*,'(a,3(e22.15,1x))') 'Before inv interior = ', pointsum(i,1:3)
        end do
      endif

      call my_alloc_ptr(dfdkx, ndim, nedgeloc)
      call my_alloc_ptr(dfdky, ndim, nedgeloc)
      call my_alloc_ptr(dfdkz, ndim, nedgeloc)

      select case (eqn_set)
      case (compressible)
          call drdgeominv(nnodes0,nedgeLoc,x,y,z,eptr,coltag,rlam,dfdkx,dfdky, &
                          dfdkz,ia,ia_ns,ja,qnode,gradx,grady,gradz,xn,yn,zn,  &
                          ra,nfunctions,phi,ndim,adim,facespeed,pointsum,rn,np,&
                          ntp,nnodes01,drdxl,nelem,elem,symmetry,r11,r12,r13,  &
                          r22,r23,r33,n_grd)
      case (incompressible)
          call drdgeominvi(nnodes0,nedgeLoc,x,y,z,eptr,coltag,rlam,dfdkx,dfdky,&
                           dfdkz,ia,ja,qnode,gradx,grady,gradz,xn,yn,zn,ra,    &
                           nfunctions,ndim,adim,ntp,nnodes01,drdxl,facespeed,  &
                           rn,np,pointsum,ia_ns,nelem,elem,symmetry,r11,r12,   &
                           r13,r22,r23,r33,n_grd)
      case (generic_gas)
          call drdgeominv(nnodes0,nedgeLoc,x,y,z,eptr,coltag,rlam,dfdkx,dfdky, &
                          dfdkz,ia,ia_ns,ja,qnode,gradx,grady,gradz,xn,yn,zn,  &
                          ra,nfunctions,phi,ndim,adim,facespeed,pointsum,rn,np,&
                          ntp,nnodes01,drdxl,nelem,elem,symmetry,r11,r12,r13,  &
                          r22,r23,r33,n_grd)
      case default
        write(*,*) 'sensshape cannot handle eqn_set /= 0,1'
        call lmpi_die
      end select

      if ( debug ) then
        do i = 1, 6
          write(*,'(a,3(e22.15,1x))') 'After inv interior = ', pointsum(i,1:3)
        end do
      endif

      do ib = 1, nbound
        select case (eqn_set)
        case (compressible)
          call bc_drdgeominv(bc(ib)%nbnode,bc(ib)%nbfacet,                     &
                             bc(ib)%face_bit,bc(ib)%face_bitq,bc(ib)%ibnode,   &
                             bc(ib)%f2ntb,bc(ib)%f2nqb,x,y,z,qnode,coltag,rlam,&
                             nfunctions,bc(ib)%ibc, ndim, adim,                &
                             nnodes01,dxdt,dydt,dzdt,bc(ib)%bxn,bc(ib)%byn,    &
                             bc(ib)%bzn,bc(ib)%bfacespeed,bc(ib)%bdxdt,        &
                             bc(ib)%bdydt,bc(ib)%bdzdt,nnodes0,pointsum,rn,np, &
                             ntp,drdxl,bc(ib)%nbfaceq,ib,nelem,elem)
        case (incompressible)
          call bc_drdgeominvi(bc(ib)%nbnode,bc(ib)%nbfacet,                    &
                              bc(ib)%face_bit,bc(ib)%ibnode,                   &
                              bc(ib)%f2ntb,x,y,z,qnode,coltag,rlam,            &
                              nfunctions,bc(ib)%ibc,ndim,adim,ntp,             &
                              nnodes01,drdxl,pointsum,rn,np,                   &
                              bc(ib)%bdxdt,bc(ib)%bdydt,bc(ib)%bdzdt,nnodes0,  &
                              bc(ib)%bxn,bc(ib)%byn,bc(ib)%bzn,                &
                              bc(ib)%bfacespeed,bc(ib)%nbfaceq,bc(ib)%f2nqb,   &
                              bc(ib)%face_bitq,dxdt,dydt,dzdt,nelem,elem)
        case (generic_gas)
          call bc_drdgeominv(bc(ib)%nbnode,bc(ib)%nbfacet,                     &
                             bc(ib)%face_bit,bc(ib)%face_bitq,bc(ib)%ibnode,   &
                             bc(ib)%f2ntb,bc(ib)%f2nqb,x,y,z,qnode,coltag,rlam,&
                             nfunctions,bc(ib)%ibc, ndim, adim,                &
                             nnodes01,dxdt,dydt,dzdt,bc(ib)%bxn,bc(ib)%byn,    &
                             bc(ib)%bzn,bc(ib)%bfacespeed,bc(ib)%bdxdt,        &
                             bc(ib)%bdydt,bc(ib)%bdzdt,nnodes0,pointsum,rn,np, &
                             ntp,drdxl,bc(ib)%nbfaceq,ib,nelem,elem)
        case default
          write(*,*) 'sensshape cannot handle eqn_set /= 0,1'
          call lmpi_die
        end select
      end do

      if ( debug ) then
        do i = 1, 6
          write(*,'(a,3(e22.15,1x))') 'After inv BCs = ', pointsum(i,1:3)
        end do
      endif

      if(ivisc > 0) then
        select case (eqn_set)
        case (compressible)
          do ielem = 1, nelem
            call drdgeomv_mix(nnodes0,nnodes01,elem(ielem)%ncell,              &
                              elem(ielem)%c2n,x,y,z,qnode,amut,                &
                              elem(ielem)%local_f2n,elem(ielem)%local_e2n,     &
                              elem(ielem)%chk_norm,elem(ielem)%face_per_cell,  &
                              elem(ielem)%node_per_cell,                       &
                              elem(ielem)%edge_per_cell,n_tot,ndim,adim,       &
                              nfunctions,ntp,rlam,drdxl,coltag,                &
                              elem(ielem)%type_cell,pointsum,rn,np)
          end do

          if ( debug ) then
            do i = 1, 6
              write(*,'(a,3(e22.15,1x))') 'After laminar interior = ',         &
                                          pointsum(i,1:3)
            end do
          endif

          do ib = 1, nbound
            if( bc_has_visc_flux_closure(bc(ib)%ibc) ) then
              call bc_drdgeomv_mix(nnodes0,nnodes01,bc(ib)%nbnode,bc(ib)%ibc,  &
                           bc(ib)%ibnode, bc(ib)%nbfacet, bc(ib)%f2ntb,        &
                           bc(ib)%nbfaceq, bc(ib)%f2nqb, nelem, elem, x, y, z, &
                           qnode, amut, n_tot, drdxl, coltag, rlam,            &
                           nfunctions, ntp, adim, pointsum, rn, np)
            endif
          end do

          call bc_viscous_wall(ntp,nnodes0,nbound,nfunctions,bc,rlam,adim,     &
                               nnodes01,rn,np,pointsum,drdxl,qnode,ndim)

        case (incompressible)
          do ielem = 1, nelem
            call drdgeomv_mix_i(nnodes0,nnodes01,elem(ielem)%ncell,            &
                                elem(ielem)%c2n,x,y,z,qnode,amut,              &
                                elem(ielem)%local_f2n,elem(ielem)%local_e2n,   &
                                elem(ielem)%chk_norm,elem(ielem)%face_per_cell,&
                                elem(ielem)%node_per_cell,                     &
                                elem(ielem)%edge_per_cell,n_tot,ndim,adim,     &
                                nfunctions,ntp,rlam,drdxl,coltag,              &
                                elem(ielem)%type_cell)
          end do
          do ib = 1, nbound
            if( bc_has_visc_flux_closure(bc(ib)%ibc) ) then
              call bc_drdgeomv_mix_i(nnodes0,nnodes01,bc(ib)%nbnode,bc(ib)%ibc,&
                           bc(ib)%ibnode, bc(ib)%nbfacet, bc(ib)%f2ntb,        &
                           bc(ib)%nbfaceq, bc(ib)%f2nqb, nelem, elem, x, y, z, &
                           qnode, amut, n_tot, drdxl, coltag, rlam,            &
                           nfunctions, ntp, adim)
            endif
          end do
          call bc_viscous_walli(ntp,nnodes0,nbound,nfunctions,bc,rlam,adim,    &
                                nnodes01,rn,np,pointsum,drdxl)
        case default
          write(*,*) 'sensshape cannot handle eqn_set /= 0,1'
          call lmpi_die
        end select
      endif

      if ( debug ) then
        do i = 1, 6
          write(*,'(a,3(e22.15,1x))') 'After laminar BCs = ', pointsum(i,1:3)
        end do
      endif

      turbulent : if ( ivisc == 6 ) then

        call turbresxyz(eqn_set,nedge,eptr,turb,qnode,x,y,z,slen,gradx,grady,  &
                        gradz,volume,xn,yn,zn,ra,dft1,dft2,coltag,rlam,dfdkx,  &
                        dfdky,dfdkz,nnodes0,nnodes01,nedgeLoc,nbound,bc,       &
                        drdxl,nfunctions,grid,iflagslen,sourceterm_sum,l2g,    &
                        nedgeloc_2d,nnodes0_2d,node_pairs_2d,nelem,elem,ndim,  &
                        adim,n_turb,n_tot,n_grd,facespeed,edge_stencil,        &
                        pointsum(6,1),rn,np,ntp,dxdt,dydt,dzdt,n_q,symmetry)

        if ( debug ) then
          do i = 1, 6
            write(*,'(a,3(e22.15,1x))') 'After turb pieces = ', pointsum(i,1:3)
          end do
        endif

      endif turbulent

      deallocate(dfdkx,dfdky,dfdkz)

      if ( need_grid_velocity ) then
        do i = 1, nedgeloc
          deallocate(edge_stencil(i)%dwhatdx, edge_stencil(i)%dwhatdy)
          deallocate(edge_stencil(i)%dwhatdz, edge_stencil(i)%nodes)
        end do
      endif
      deallocate(edge_stencil)

      if ( debug ) then
        do i = 1, 6
          write(*,'(a,3(e22.15,1x))') 'After all flux pieces = ',pointsum(i,1:3)
        end do
      endif

   end subroutine drdgeom


!================================ DCORIOLIS ==================================80
!
!  Include linearization of Coriolis effects for noninertial cases
!
!=============================================================================80
  subroutine dcoriolis(design,sadj,soln,getg,nelem,nnodes0,nedgeloc,nbound,    &
                       elem,x,y,z,bc,eqn_set)

    use solution_types, only : soln_type
    use solution_adj,   only : sadj_type
    use solution_getg,  only : getg_type
    use design_types,   only : design_type
    use kinddefs,       only : dp
    use noninertials,   only : xrotrate_ni, yrotrate_ni, zrotrate_ni
    use bc_types,       only : bcgrid_type
    use element_types,  only : elem_type
    use element_defs,   only : max_node_per_cell
    use solution_types, only : compressible

    integer, intent(in) :: nelem, nnodes0, nedgeloc, nbound, eqn_set

    real(dp), dimension(:), intent(in) :: x, y, z

    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(in)    :: design
    type(soln_type),   intent(in)    :: soln
    type(getg_type),   intent(inout) :: getg

    type(elem_type),   dimension(:), intent(in) :: elem
    type(bcgrid_type), dimension(:), intent(in) :: bc

    integer :: ielem, n, nn, node, k, j, ie, edge, n1, n2
    integer :: nn1, nn2, n3, nn3, n4, nn4, n5, nn5, n6, nn6
    integer :: inode

    real(dp) :: denom,rho,u,v,w,factor2,factor3,factor4
    real(dp) :: xorig1,xorig2,yorig1,yorig2,zorig1,zorig2
    real(dp) :: xl1,xr1,xl2,xr2,xm1,xm2,xavg1,yavg1,zavg1,xavg2,yavg2,zavg2
    real(dp) :: yl1,yr1,yl2,yr2,ym1,ym2,areax1,areay1,areaz1
    real(dp) :: zl1,zr1,zl2,zr2,zm1,zm2,areax2,areay2,areaz2

    real(dp), dimension(max_node_per_cell) :: xc,yc,zc
    real(dp), dimension(max_node_per_cell) :: dareax1dy, dareax1dz
    real(dp), dimension(max_node_per_cell) :: dareay1dx, dareay1dz
    real(dp), dimension(max_node_per_cell) :: dareaz1dx, dareaz1dy
    real(dp), dimension(max_node_per_cell) :: dareax2dy, dareax2dz
    real(dp), dimension(max_node_per_cell) :: dareay2dx, dareay2dz
    real(dp), dimension(max_node_per_cell) :: dareaz2dx, dareaz2dy
    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dxcdx
    real(dp), dimension(max_node_per_cell) :: dxm1dx
    real(dp), dimension(max_node_per_cell) :: dxl1dx
    real(dp), dimension(max_node_per_cell) :: dxr1dx
    real(dp), dimension(max_node_per_cell) :: dxm2dx
    real(dp), dimension(max_node_per_cell) :: dxl2dx
    real(dp), dimension(max_node_per_cell) :: dxr2dx
    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dycdy
    real(dp), dimension(max_node_per_cell) :: dym1dy
    real(dp), dimension(max_node_per_cell) :: dyl1dy
    real(dp), dimension(max_node_per_cell) :: dyr1dy
    real(dp), dimension(max_node_per_cell) :: dym2dy
    real(dp), dimension(max_node_per_cell) :: dyl2dy
    real(dp), dimension(max_node_per_cell) :: dyr2dy
    real(dp), dimension(max_node_per_cell,max_node_per_cell) :: dzcdz
    real(dp), dimension(max_node_per_cell) :: dzm1dz
    real(dp), dimension(max_node_per_cell) :: dzl1dz
    real(dp), dimension(max_node_per_cell) :: dzr1dz
    real(dp), dimension(max_node_per_cell) :: dzm2dz
    real(dp), dimension(max_node_per_cell) :: dzl2dz
    real(dp), dimension(max_node_per_cell) :: dzr2dz
    real(dp), dimension(max_node_per_cell) :: dxorig1dx
    real(dp), dimension(max_node_per_cell) :: dyorig1dy
    real(dp), dimension(max_node_per_cell) :: dzorig1dz
    real(dp), dimension(max_node_per_cell) :: dxorig2dx
    real(dp), dimension(max_node_per_cell) :: dyorig2dy
    real(dp), dimension(max_node_per_cell) :: dzorig2dz
    real(dp), dimension(max_node_per_cell) :: xavg1dx,yavg1dy,zavg1dz
    real(dp), dimension(max_node_per_cell) :: xavg2dx,yavg2dy,zavg2dz
    real(dp), dimension(max_node_per_cell) :: term1dx,term1dy,term1dz
    real(dp), dimension(max_node_per_cell) :: term2dx,term2dy,term2dz
    real(dp), dimension(max_node_per_cell) :: vol1dx,vol1dy,vol1dz
    real(dp), dimension(max_node_per_cell) :: vol2dx,vol2dy,vol2dz

  continue

    rho = 0.0_dp

! Linearize dual volume and compute the terms we want

    elem_loop1 : do ielem = 1, nelem

     cell_loop : do n = 1, elem(ielem)%ncell

!     what follows are the d(normal)/d(coord) pieces

      denom = real(elem(ielem)%node_per_cell,dp)

!     cell center (avg of all points in cell)

      xc(:) = 0.0_dp
      yc(:) = 0.0_dp
      zc(:) = 0.0_dp

      do nn = 1,elem(ielem)%node_per_cell
        node  = elem(ielem)%c2n(nn,n)
        do k = 1, elem(ielem)%node_per_cell
          xc(k) = xc(k) + (x(node) - x(elem(ielem)%c2n(k,n)))
          yc(k) = yc(k) + (y(node) - y(elem(ielem)%c2n(k,n)))
          zc(k) = zc(k) + (z(node) - z(elem(ielem)%c2n(k,n)))
        end do
      end do

      do k = 1, elem(ielem)%node_per_cell
        xc(k) = xc(k)/denom
        yc(k) = yc(k)/denom
        zc(k) = zc(k)/denom
      end do

      do j = 1, elem(ielem)%node_per_cell
        do k = 1, elem(ielem)%node_per_cell
          if ( j == k ) then
            dxcdx(j,k) = 1.0_dp/denom - 1.0_dp
            dycdy(j,k) = 1.0_dp/denom - 1.0_dp
            dzcdz(j,k) = 1.0_dp/denom - 1.0_dp
          else
            dxcdx(j,k) = 1.0_dp/denom
            dycdy(j,k) = 1.0_dp/denom
            dzcdz(j,k) = 1.0_dp/denom
          endif
        end do
      end do

!     loop over edges of the cell

      edge_loop_cell : do ie = 1,elem(ielem)%edge_per_cell

!       initialize local sensitivity arrays

        dxorig1dx(:) = 0.0_dp
        dyorig1dy(:) = 0.0_dp
        dzorig1dz(:) = 0.0_dp

        dxorig2dx(:) = 0.0_dp
        dyorig2dy(:) = 0.0_dp
        dzorig2dz(:) = 0.0_dp

        dxm1dx(:) = 0.0_dp
        dym1dy(:) = 0.0_dp
        dzm1dz(:) = 0.0_dp

        dxm2dx(:) = 0.0_dp
        dym2dy(:) = 0.0_dp
        dzm2dz(:) = 0.0_dp

        dxl1dx(:) = 0.0_dp
        dyl1dy(:) = 0.0_dp
        dzl1dz(:) = 0.0_dp

        dxl2dx(:) = 0.0_dp
        dyl2dy(:) = 0.0_dp
        dzl2dz(:) = 0.0_dp

        dxr1dx(:) = 0.0_dp
        dyr1dy(:) = 0.0_dp
        dzr1dz(:) = 0.0_dp

        dxr2dx(:) = 0.0_dp
        dyr2dy(:) = 0.0_dp
        dzr2dz(:) = 0.0_dp

        vol1dx(:) = 0.0_dp
        vol1dy(:) = 0.0_dp
        vol1dz(:) = 0.0_dp

        vol2dx(:) = 0.0_dp
        vol2dy(:) = 0.0_dp
        vol2dz(:) = 0.0_dp

!       global edge number

        edge = elem(ielem)%c2e(ie,n)

        if (edge > nedgeloc) cycle edge_loop_cell

!       global node numbers of edge endpoints

        n1 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,1),n)
        n2 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,2),n)

!       cell node numbers of edge endpoints

        nn1 = elem(ielem)%local_e2n(ie,1)
        nn2 = elem(ielem)%local_e2n(ie,2)

!       Establish reference origins at each edge endpoint to avoid roundoff

        xorig1 = x(n1)
        yorig1 = y(n1)
        zorig1 = z(n1)

        xorig2 = x(n2)
        yorig2 = y(n2)
        zorig2 = z(n2)

        dxorig1dx(nn1) = 1.0_dp
        dyorig1dy(nn1) = 1.0_dp
        dzorig1dz(nn1) = 1.0_dp

        dxorig2dx(nn2) = 1.0_dp
        dyorig2dy(nn2) = 1.0_dp
        dzorig2dz(nn2) = 1.0_dp

!       edge midpoint referenced to local origins

        xm1 = ((x(n1)-xorig1) + (x(n2)-xorig1))/2._dp
        ym1 = ((y(n1)-yorig1) + (y(n2)-yorig1))/2._dp
        zm1 = ((z(n1)-zorig1) + (z(n2)-zorig1))/2._dp

        dxm1dx(nn1)=1.0_dp/2._dp - dxorig1dx(nn1)/2.0_dp - dxorig1dx(nn1)/2.0_dp
        dym1dy(nn1)=1.0_dp/2._dp - dyorig1dy(nn1)/2.0_dp - dyorig1dy(nn1)/2.0_dp
        dzm1dz(nn1)=1.0_dp/2._dp - dzorig1dz(nn1)/2.0_dp - dzorig1dz(nn1)/2.0_dp

        dxm1dx(nn2)=1.0_dp/2._dp - dxorig1dx(nn2)/2.0_dp - dxorig1dx(nn2)/2.0_dp
        dym1dy(nn2)=1.0_dp/2._dp - dyorig1dy(nn2)/2.0_dp - dyorig1dy(nn2)/2.0_dp
        dzm1dz(nn2)=1.0_dp/2._dp - dzorig1dz(nn2)/2.0_dp - dzorig1dz(nn2)/2.0_dp

        xm2 = ((x(n1)-xorig2) + (x(n2)-xorig2))/2._dp
        ym2 = ((y(n1)-yorig2) + (y(n2)-yorig2))/2._dp
        zm2 = ((z(n1)-zorig2) + (z(n2)-zorig2))/2._dp

        dxm2dx(nn1)=1.0_dp/2._dp - dxorig2dx(nn1)/2.0_dp - dxorig2dx(nn1)/2.0_dp
        dym2dy(nn1)=1.0_dp/2._dp - dyorig2dy(nn1)/2.0_dp - dyorig2dy(nn1)/2.0_dp
        dzm2dz(nn1)=1.0_dp/2._dp - dzorig2dz(nn1)/2.0_dp - dzorig2dz(nn1)/2.0_dp

        dxm2dx(nn2)=1.0_dp/2._dp - dxorig2dx(nn2)/2.0_dp - dxorig2dx(nn2)/2.0_dp
        dym2dy(nn2)=1.0_dp/2._dp - dyorig2dy(nn2)/2.0_dp - dyorig2dy(nn2)/2.0_dp
        dzm2dz(nn2)=1.0_dp/2._dp - dzorig2dz(nn2)/2.0_dp - dzorig2dz(nn2)/2.0_dp

!       compute left face centroid referenced to local origins

        n3 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,3),n)

        nn3 = elem(ielem)%local_e2n(ie,3)

        if (elem(ielem)%local_e2n(ie,4) /= 0) then

!         quad face

          n4 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,4),n)

          nn4 = elem(ielem)%local_e2n(ie,4)

          xl1 = ((x(n1)-xorig1) + (x(n2)-xorig1) +                             &
                 (x(n3)-xorig1) + (x(n4)-xorig1))/4._dp
          yl1 = ((y(n1)-yorig1) + (y(n2)-yorig1) +                             &
                 (y(n3)-yorig1) + (y(n4)-yorig1))/4._dp
          zl1 = ((z(n1)-zorig1) + (z(n2)-zorig1) +                             &
                 (z(n3)-zorig1) + (z(n4)-zorig1))/4._dp

          dxl1dx(nn1) = 1.0_dp/4._dp - dxorig1dx(nn1)
          dyl1dy(nn1) = 1.0_dp/4._dp - dyorig1dy(nn1)
          dzl1dz(nn1) = 1.0_dp/4._dp - dzorig1dz(nn1)

          dxl1dx(nn2) = 1.0_dp/4._dp - dxorig1dx(nn2)
          dyl1dy(nn2) = 1.0_dp/4._dp - dyorig1dy(nn2)
          dzl1dz(nn2) = 1.0_dp/4._dp - dzorig1dz(nn2)

          dxl1dx(nn3) = 1.0_dp/4._dp - dxorig1dx(nn3)
          dyl1dy(nn3) = 1.0_dp/4._dp - dyorig1dy(nn3)
          dzl1dz(nn3) = 1.0_dp/4._dp - dzorig1dz(nn3)

          dxl1dx(nn4) = 1.0_dp/4._dp - dxorig1dx(nn4)
          dyl1dy(nn4) = 1.0_dp/4._dp - dyorig1dy(nn4)
          dzl1dz(nn4) = 1.0_dp/4._dp - dzorig1dz(nn4)

          xl2 = ((x(n1)-xorig2) + (x(n2)-xorig2) +                             &
                 (x(n3)-xorig2) + (x(n4)-xorig2))/4._dp
          yl2 = ((y(n1)-yorig2) + (y(n2)-yorig2) +                             &
                 (y(n3)-yorig2) + (y(n4)-yorig2))/4._dp
          zl2 = ((z(n1)-zorig2) + (z(n2)-zorig2) +                             &
                 (z(n3)-zorig2) + (z(n4)-zorig2))/4._dp

          dxl2dx(nn1) = 1.0_dp/4._dp - dxorig2dx(nn1)
          dyl2dy(nn1) = 1.0_dp/4._dp - dyorig2dy(nn1)
          dzl2dz(nn1) = 1.0_dp/4._dp - dzorig2dz(nn1)

          dxl2dx(nn2) = 1.0_dp/4._dp - dxorig2dx(nn2)
          dyl2dy(nn2) = 1.0_dp/4._dp - dyorig2dy(nn2)
          dzl2dz(nn2) = 1.0_dp/4._dp - dzorig2dz(nn2)

          dxl2dx(nn3) = 1.0_dp/4._dp - dxorig2dx(nn3)
          dyl2dy(nn3) = 1.0_dp/4._dp - dyorig2dy(nn3)
          dzl2dz(nn3) = 1.0_dp/4._dp - dzorig2dz(nn3)

          dxl2dx(nn4) = 1.0_dp/4._dp - dxorig2dx(nn4)
          dyl2dy(nn4) = 1.0_dp/4._dp - dyorig2dy(nn4)
          dzl2dz(nn4) = 1.0_dp/4._dp - dzorig2dz(nn4)

        else

!         tria face

          xl1 = ((x(n1)-xorig1) + (x(n2)-xorig1) + (x(n3)-xorig1))/3._dp
          yl1 = ((y(n1)-yorig1) + (y(n2)-yorig1) + (y(n3)-yorig1))/3._dp
          zl1 = ((z(n1)-zorig1) + (z(n2)-zorig1) + (z(n3)-zorig1))/3._dp

          dxl1dx(nn1) = 1.0_dp/3._dp - dxorig1dx(nn1)
          dyl1dy(nn1) = 1.0_dp/3._dp - dyorig1dy(nn1)
          dzl1dz(nn1) = 1.0_dp/3._dp - dzorig1dz(nn1)

          dxl1dx(nn2) = 1.0_dp/3._dp - dxorig1dx(nn2)
          dyl1dy(nn2) = 1.0_dp/3._dp - dyorig1dy(nn2)
          dzl1dz(nn2) = 1.0_dp/3._dp - dzorig1dz(nn2)

          dxl1dx(nn3) = 1.0_dp/3._dp - dxorig1dx(nn3)
          dyl1dy(nn3) = 1.0_dp/3._dp - dyorig1dy(nn3)
          dzl1dz(nn3) = 1.0_dp/3._dp - dzorig1dz(nn3)

          xl2 = ((x(n1)-xorig2) + (x(n2)-xorig2) + (x(n3)-xorig2))/3._dp
          yl2 = ((y(n1)-yorig2) + (y(n2)-yorig2) + (y(n3)-yorig2))/3._dp
          zl2 = ((z(n1)-zorig2) + (z(n2)-zorig2) + (z(n3)-zorig2))/3._dp

          dxl2dx(nn1) = 1.0_dp/3._dp - dxorig2dx(nn1)
          dyl2dy(nn1) = 1.0_dp/3._dp - dyorig2dy(nn1)
          dzl2dz(nn1) = 1.0_dp/3._dp - dzorig2dz(nn1)

          dxl2dx(nn2) = 1.0_dp/3._dp - dxorig2dx(nn2)
          dyl2dy(nn2) = 1.0_dp/3._dp - dyorig2dy(nn2)
          dzl2dz(nn2) = 1.0_dp/3._dp - dzorig2dz(nn2)

          dxl2dx(nn3) = 1.0_dp/3._dp - dxorig2dx(nn3)
          dyl2dy(nn3) = 1.0_dp/3._dp - dyorig2dy(nn3)
          dzl2dz(nn3) = 1.0_dp/3._dp - dzorig2dz(nn3)

        end if

!       compute right face centroid

        n5 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,5),n)

        nn5 = elem(ielem)%local_e2n(ie,5)

        if (elem(ielem)%local_e2n(ie,6) /= 0) then

!         quad face

          n6 = elem(ielem)%c2n(elem(ielem)%local_e2n(ie,6),n)

          nn6 = elem(ielem)%local_e2n(ie,6)

          xr1 = ((x(n1)-xorig1) + (x(n2)-xorig1) +                             &
                 (x(n5)-xorig1) + (x(n6)-xorig1))/4._dp
          yr1 = ((y(n1)-yorig1) + (y(n2)-yorig1) +                             &
                 (y(n5)-yorig1) + (y(n6)-yorig1))/4._dp
          zr1 = ((z(n1)-zorig1) + (z(n2)-zorig1) +                             &
                 (z(n5)-zorig1) + (z(n6)-zorig1))/4._dp

          dxr1dx(nn1) = 1.0_dp/4._dp - dxorig1dx(nn1)
          dyr1dy(nn1) = 1.0_dp/4._dp - dyorig1dy(nn1)
          dzr1dz(nn1) = 1.0_dp/4._dp - dzorig1dz(nn1)

          dxr1dx(nn2) = 1.0_dp/4._dp - dxorig1dx(nn2)
          dyr1dy(nn2) = 1.0_dp/4._dp - dyorig1dy(nn2)
          dzr1dz(nn2) = 1.0_dp/4._dp - dzorig1dz(nn2)

          dxr1dx(nn5) = 1.0_dp/4._dp - dxorig1dx(nn5)
          dyr1dy(nn5) = 1.0_dp/4._dp - dyorig1dy(nn5)
          dzr1dz(nn5) = 1.0_dp/4._dp - dzorig1dz(nn5)

          dxr1dx(nn6) = 1.0_dp/4._dp - dxorig1dx(nn6)
          dyr1dy(nn6) = 1.0_dp/4._dp - dyorig1dy(nn6)
          dzr1dz(nn6) = 1.0_dp/4._dp - dzorig1dz(nn6)

          xr2 = ((x(n1)-xorig2) + (x(n2)-xorig2) +                             &
                 (x(n5)-xorig2) + (x(n6)-xorig2))/4._dp
          yr2 = ((y(n1)-yorig2) + (y(n2)-yorig2) +                             &
                 (y(n5)-yorig2) + (y(n6)-yorig2))/4._dp
          zr2 = ((z(n1)-zorig2) + (z(n2)-zorig2) +                             &
                 (z(n5)-zorig2) + (z(n6)-zorig2))/4._dp

          dxr2dx(nn1) = 1.0_dp/4._dp - dxorig2dx(nn1)
          dyr2dy(nn1) = 1.0_dp/4._dp - dyorig2dy(nn1)
          dzr2dz(nn1) = 1.0_dp/4._dp - dzorig2dz(nn1)

          dxr2dx(nn2) = 1.0_dp/4._dp - dxorig2dx(nn2)
          dyr2dy(nn2) = 1.0_dp/4._dp - dyorig2dy(nn2)
          dzr2dz(nn2) = 1.0_dp/4._dp - dzorig2dz(nn2)

          dxr2dx(nn5) = 1.0_dp/4._dp - dxorig2dx(nn5)
          dyr2dy(nn5) = 1.0_dp/4._dp - dyorig2dy(nn5)
          dzr2dz(nn5) = 1.0_dp/4._dp - dzorig2dz(nn5)

          dxr2dx(nn6) = 1.0_dp/4._dp - dxorig2dx(nn6)
          dyr2dy(nn6) = 1.0_dp/4._dp - dyorig2dy(nn6)
          dzr2dz(nn6) = 1.0_dp/4._dp - dzorig2dz(nn6)

        else

!         tria face

          xr1 = ((x(n1)-xorig1) + (x(n2)-xorig1) + (x(n5)-xorig1))/3._dp
          yr1 = ((y(n1)-yorig1) + (y(n2)-yorig1) + (y(n5)-yorig1))/3._dp
          zr1 = ((z(n1)-zorig1) + (z(n2)-zorig1) + (z(n5)-zorig1))/3._dp

          dxr1dx(nn1) = 1.0_dp/3._dp - dxorig1dx(nn1)
          dyr1dy(nn1) = 1.0_dp/3._dp - dyorig1dy(nn1)
          dzr1dz(nn1) = 1.0_dp/3._dp - dzorig1dz(nn1)

          dxr1dx(nn2) = 1.0_dp/3._dp - dxorig1dx(nn2)
          dyr1dy(nn2) = 1.0_dp/3._dp - dyorig1dy(nn2)
          dzr1dz(nn2) = 1.0_dp/3._dp - dzorig1dz(nn2)

          dxr1dx(nn5) = 1.0_dp/3._dp - dxorig1dx(nn5)
          dyr1dy(nn5) = 1.0_dp/3._dp - dyorig1dy(nn5)
          dzr1dz(nn5) = 1.0_dp/3._dp - dzorig1dz(nn5)

          xr2 = ((x(n1)-xorig2) + (x(n2)-xorig2) + (x(n5)-xorig2))/3._dp
          yr2 = ((y(n1)-yorig2) + (y(n2)-yorig2) + (y(n5)-yorig2))/3._dp
          zr2 = ((z(n1)-zorig2) + (z(n2)-zorig2) + (z(n5)-zorig2))/3._dp

          dxr2dx(nn1) = 1.0_dp/3._dp - dxorig2dx(nn1)
          dyr2dy(nn1) = 1.0_dp/3._dp - dyorig2dy(nn1)
          dzr2dz(nn1) = 1.0_dp/3._dp - dzorig2dz(nn1)

          dxr2dx(nn2) = 1.0_dp/3._dp - dxorig2dx(nn2)
          dyr2dy(nn2) = 1.0_dp/3._dp - dyorig2dy(nn2)
          dzr2dz(nn2) = 1.0_dp/3._dp - dzorig2dz(nn2)

          dxr2dx(nn5) = 1.0_dp/3._dp - dxorig2dx(nn5)
          dyr2dy(nn5) = 1.0_dp/3._dp - dyorig2dy(nn5)
          dzr2dz(nn5) = 1.0_dp/3._dp - dzorig2dz(nn5)

        end if

!       (dual) triangle xm-xr-xc

        areax1 = 0.5_dp*( (yr1-ym1)*(zc(elem(ielem)%local_e2n(ie,1))-zm1)      &
                        - (zr1-zm1)*(yc(elem(ielem)%local_e2n(ie,1))-ym1) )
!         dareax1dx(:)=0.0_dp
          dareax1dy(:)=0.5_dp*( (dyr1dy(:)-dym1dy(:))*                         &
                       (zc(elem(ielem)%local_e2n(ie,1))-zm1)                   &
                       - (zr1-zm1)*(dycdy(elem(ielem)%local_e2n(ie,1),:)       &
                       -dym1dy(:)) )
          dareax1dz(:)=0.5_dp*( (yr1-ym1)*(dzcdz(elem(ielem)%local_e2n(ie,1),:)&
                      -dzm1dz(:)) - (dzr1dz(:)-dzm1dz(:))                      &
                      *(yc(elem(ielem)%local_e2n(ie,1))-ym1) )

        areay1 = 0.5_dp*( (zr1-zm1)*(xc(elem(ielem)%local_e2n(ie,1))-xm1)      &
                        - (xr1-xm1)*(zc(elem(ielem)%local_e2n(ie,1))-zm1) )
          dareay1dx(:)=0.5_dp*( (zr1-zm1)*(dxcdx(elem(ielem)%local_e2n(ie,1),:)&
                      -dxm1dx(:)) - (dxr1dx(:)-dxm1dx(:))                      &
                      *(zc(elem(ielem)%local_e2n(ie,1))-zm1) )
!         dareay1dy(:)=0.0_dp
          dareay1dz(:)=0.5_dp*( (dzr1dz(:)-dzm1dz(:))                          &
                      *(xc(elem(ielem)%local_e2n(ie,1))-xm1)                   &
                    -(xr1-xm1)*(dzcdz(elem(ielem)%local_e2n(ie,1),:)-dzm1dz(:)))

        areaz1 = 0.5_dp*( (xr1-xm1)*(yc(elem(ielem)%local_e2n(ie,1))-ym1)      &
                        - (yr1-ym1)*(xc(elem(ielem)%local_e2n(ie,1))-xm1) )
          dareaz1dx(:)=0.5_dp*( (dxr1dx(:)-dxm1dx(:))                          &
                      *(yc(elem(ielem)%local_e2n(ie,1))-ym1)                   &
                      - (yr1-ym1)*(dxcdx(elem(ielem)%local_e2n(ie,1),:)-       &
                      dxm1dx(:)) )
          dareaz1dy(:)=0.5_dp*( (xr1-xm1)*(dycdy(elem(ielem)%local_e2n(ie,1),:)&
                      -dym1dy(:)) - (dyr1dy(:)-dym1dy(:))                      &
                      *(xc(elem(ielem)%local_e2n(ie,1))-xm1) )
!         dareaz1dz(:)=0.0_dp


        areax2 = 0.5_dp*( (yr2-ym2)*(zc(elem(ielem)%local_e2n(ie,2))-zm2)      &
                        - (zr2-zm2)*(yc(elem(ielem)%local_e2n(ie,2))-ym2) )
!         dareax2dx(:)=0.0_dp
          dareax2dy(:)=0.5_dp*( (dyr2dy(:)-dym2dy(:))                          &
                      *(zc(elem(ielem)%local_e2n(ie,2))-zm2)                   &
                    -(zr2-zm2)*(dycdy(elem(ielem)%local_e2n(ie,2),:)-dym2dy(:)))
          dareax2dz(:)=0.5_dp*( (yr2-ym2)*(dzcdz(elem(ielem)%local_e2n(ie,2),:)&
                      -dzm2dz(:)) - (dzr2dz(:)-dzm2dz(:))                      &
                      *(yc(elem(ielem)%local_e2n(ie,2))-ym2) )

        areay2 = 0.5_dp*( (zr2-zm2)*(xc(elem(ielem)%local_e2n(ie,2))-xm2)      &
                        - (xr2-xm2)*(zc(elem(ielem)%local_e2n(ie,2))-zm2) )
          dareay2dx(:)=0.5_dp*( (zr2-zm2)*(dxcdx(elem(ielem)%local_e2n(ie,2),:)&
                      -dxm2dx(:)) - (dxr2dx(:)-dxm2dx(:))                      &
                      *(zc(elem(ielem)%local_e2n(ie,2))-zm2) )
!         dareay2dy(:)=0.0_dp
          dareay2dz(:)=0.5_dp*( (dzr2dz(:)-dzm2dz(:))                          &
                      *(xc(elem(ielem)%local_e2n(ie,2))-xm2)                   &
                  - (xr2-xm2)*(dzcdz(elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:)) )

        areaz2 = 0.5_dp*( (xr2-xm2)*(yc(elem(ielem)%local_e2n(ie,2))-ym2)      &
                        - (yr2-ym2)*(xc(elem(ielem)%local_e2n(ie,2))-xm2) )
          dareaz2dx(:)=0.5_dp*( (dxr2dx(:)-dxm2dx(:))                          &
                      *(yc(elem(ielem)%local_e2n(ie,2))-ym2)                   &
                  - (yr2-ym2)*(dxcdx(elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:)) )
          dareaz2dy(:)=0.5_dp*( (xr2-xm2)*(dycdy(elem(ielem)%local_e2n(ie,2),:)&
                      -dym2dy(:)) - (dyr2dy(:)-dym2dy(:))                      &
                      *(xc(elem(ielem)%local_e2n(ie,2))-xm2) )
!         dareaz2dz(:)=0.0_dp

        xavg1 = (xm1 + xr1 + xc(elem(ielem)%local_e2n(ie,1)))/3._dp
          xavg1dx(:) = (dxm1dx(:) + dxr1dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,1),:))/3._dp

        yavg1 = (ym1 + yr1 + yc(elem(ielem)%local_e2n(ie,1)))/3._dp
          yavg1dy(:) = (dym1dy(:) + dyr1dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,1),:))/3._dp

        zavg1 = (zm1 + zr1 + zc(elem(ielem)%local_e2n(ie,1)))/3._dp
          zavg1dz(:) = (dzm1dz(:) + dzr1dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,1),:))/3._dp

        xavg2 = (xm2 + xr2 + xc(elem(ielem)%local_e2n(ie,2)))/3._dp
          xavg2dx(:) = (dxm2dx(:) + dxr2dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,2),:))/3._dp

        yavg2 = (ym2 + yr2 + yc(elem(ielem)%local_e2n(ie,2)))/3._dp
          yavg2dy(:) = (dym2dy(:) + dyr2dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,2),:))/3._dp

        zavg2 = (zm2 + zr2 + zc(elem(ielem)%local_e2n(ie,2)))/3._dp
          zavg2dz(:) = (dzm2dz(:) + dzr2dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,2),:))/3._dp

!       term1 = (xavg1*areax1 + yavg1*areay1 + zavg1*areaz1)/3._dp
         term1dx(:)=(yavg1*dareay1dx(:)+zavg1*dareaz1dx(:) + xavg1dx(:)*areax1)&
                   /3._dp
         term1dy(:)=(xavg1*dareax1dy(:)+zavg1*dareaz1dy(:) + yavg1dy(:)*areay1)&
                   /3._dp
         term1dz(:)=(xavg1*dareax1dz(:)+yavg1*dareay1dz(:) + zavg1dz(:)*areaz1)&
                   /3._dp

!       term2 = (xavg2*areax2 + yavg2*areay2 + zavg2*areaz2)/3._dp
         term2dx(:)=(yavg2*dareay2dx(:)+zavg2*dareaz2dx(:) + xavg2dx(:)*areax2)&
                   /3._dp
         term2dy(:)=(xavg2*dareax2dy(:)+zavg2*dareaz2dy(:) + yavg2dy(:)*areay2)&
                   /3._dp
         term2dz(:)=(xavg2*dareax2dz(:)+yavg2*dareay2dz(:) + zavg2dz(:)*areaz2)&
                   /3._dp

!       vol1 = vol1 + term1
!       vol2 = vol2 - term2

        vol1dx(:) = vol1dx(:) + term1dx(:)
        vol1dy(:) = vol1dy(:) + term1dy(:)
        vol1dz(:) = vol1dz(:) + term1dz(:)

        vol2dx(:) = vol2dx(:) - term2dx(:)
        vol2dy(:) = vol2dy(:) - term2dy(:)
        vol2dz(:) = vol2dz(:) - term2dz(:)

!       (dual) triangle xm-xc-xl

        areax1 = 0.5_dp*( (yc(elem(ielem)%local_e2n(ie,1))-ym1)*(zl1-zm1)      &
                        - (zc(elem(ielem)%local_e2n(ie,1))-zm1)*(yl1-ym1) )
!         dareax1dx(:)=0.0_dp
          dareax1dy(:)=0.5_dp*( (dycdy(elem(ielem)%local_e2n(ie,1),:)          &
                      -dym1dy(:))*(zl1-zm1)                                    &
                 - (zc(elem(ielem)%local_e2n(ie,1))-zm1)*(dyl1dy(:)-dym1dy(:)) )
          dareax1dz(:)=0.5_dp*( (yc(elem(ielem)%local_e2n(ie,1))-ym1)          &
                      *(dzl1dz(:)-dzm1dz(:))                                   &
                  - (dzcdz(elem(ielem)%local_e2n(ie,1),:)-dzm1dz(:))*(yl1-ym1) )

        areay1 = 0.5_dp*( (zc(elem(ielem)%local_e2n(ie,1))-zm1)*(xl1-xm1)      &
                        - (xc(elem(ielem)%local_e2n(ie,1))-xm1)*(zl1-zm1) )
          dareay1dx(:)=0.5_dp*( (zc(elem(ielem)%local_e2n(ie,1))-zm1)          &
                      *(dxl1dx(:)-dxm1dx(:))                                   &
                  - (dxcdx(elem(ielem)%local_e2n(ie,1),:)-dxm1dx(:))*(zl1-zm1) )
!         dareay1dy(:)=0.0_dp
          dareay1dz(:)=0.5_dp*( (dzcdz(elem(ielem)%local_e2n(ie,1),:)          &
                      -dzm1dz(:))*(xl1-xm1)                                    &
                 - (xc(elem(ielem)%local_e2n(ie,1))-xm1)*(dzl1dz(:)-dzm1dz(:)) )

        areaz1 = 0.5_dp*( (xc(elem(ielem)%local_e2n(ie,1))-xm1)*(yl1-ym1)      &
                        - (yc(elem(ielem)%local_e2n(ie,1))-ym1)*(xl1-xm1) )
          dareaz1dx(:)=0.5_dp*( (dxcdx(elem(ielem)%local_e2n(ie,1),:)          &
                      -dxm1dx(:))*(yl1-ym1)                                    &
                 - (yc(elem(ielem)%local_e2n(ie,1))-ym1)*(dxl1dx(:)-dxm1dx(:)) )
          dareaz1dy(:)=0.5_dp*( (xc(elem(ielem)%local_e2n(ie,1))-xm1)          &
                      *(dyl1dy(:)-dym1dy(:))                                   &
                  - (dycdy(elem(ielem)%local_e2n(ie,1),:)-dym1dy(:))*(xl1-xm1) )
!         dareaz1dz(:)=0.0_dp

        areax2 = 0.5_dp*( (yc(elem(ielem)%local_e2n(ie,2))-ym2)*(zl2-zm2)      &
                        - (zc(elem(ielem)%local_e2n(ie,2))-zm2)*(yl2-ym2) )
!         dareax2dx(:)=0.0_dp
          dareax2dy(:)=0.5_dp*( (dycdy(elem(ielem)%local_e2n(ie,2),:)          &
                      -dym2dy(:))*(zl2-zm2)                                    &
                 - (zc(elem(ielem)%local_e2n(ie,2))-zm2)*(dyl2dy(:)-dym2dy(:)) )
          dareax2dz(:)=0.5_dp*( (yc(elem(ielem)%local_e2n(ie,2))-ym2)          &
                      *(dzl2dz(:)-dzm2dz(:))                                   &
                  - (dzcdz(elem(ielem)%local_e2n(ie,2),:)-dzm2dz(:))*(yl2-ym2) )

        areay2 = 0.5_dp*( (zc(elem(ielem)%local_e2n(ie,2))-zm2)*(xl2-xm2)      &
                        - (xc(elem(ielem)%local_e2n(ie,2))-xm2)*(zl2-zm2) )
          dareay2dx(:)=0.5_dp*( (zc(elem(ielem)%local_e2n(ie,2))-zm2)          &
                      *(dxl2dx(:)-dxm2dx(:))                                   &
                  - (dxcdx(elem(ielem)%local_e2n(ie,2),:)-dxm2dx(:))*(zl2-zm2) )
!         dareay2dy(:)=0.0_dp
          dareay2dz(:)=0.5_dp*( (dzcdz(elem(ielem)%local_e2n(ie,2),:)          &
                      -dzm2dz(:))*(xl2-xm2)                                    &
                 - (xc(elem(ielem)%local_e2n(ie,2))-xm2)*(dzl2dz(:)-dzm2dz(:)) )

        areaz2 = 0.5_dp*( (xc(elem(ielem)%local_e2n(ie,2))-xm2)*(yl2-ym2)      &
                        - (yc(elem(ielem)%local_e2n(ie,2))-ym2)*(xl2-xm2) )
          dareaz2dx(:)=0.5_dp*( (dxcdx(elem(ielem)%local_e2n(ie,2),:)          &
                      -dxm2dx(:))*(yl2-ym2)                                    &
                 - (yc(elem(ielem)%local_e2n(ie,2))-ym2)*(dxl2dx(:)-dxm2dx(:)) )
          dareaz2dy(:)=0.5_dp*( (xc(elem(ielem)%local_e2n(ie,2))-xm2)          &
                      *(dyl2dy(:)-dym2dy(:))                                   &
                  - (dycdy(elem(ielem)%local_e2n(ie,2),:)-dym2dy(:))*(xl2-xm2) )
!         dareaz2dz(:)=0.0_dp

        xavg1 = (xm1 + xl1 + xc(elem(ielem)%local_e2n(ie,1)))/3._dp
          xavg1dx(:) = (dxm1dx(:) + dxl1dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,1),:))/3._dp

        yavg1 = (ym1 + yl1 + yc(elem(ielem)%local_e2n(ie,1)))/3._dp
          yavg1dy(:) = (dym1dy(:) + dyl1dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,1),:))/3._dp

        zavg1 = (zm1 + zl1 + zc(elem(ielem)%local_e2n(ie,1)))/3._dp
          zavg1dz(:) = (dzm1dz(:) + dzl1dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,1),:))/3._dp

        xavg2 = (xm2 + xl2 + xc(elem(ielem)%local_e2n(ie,2)))/3._dp
          xavg2dx(:) = (dxm2dx(:) + dxl2dx(:)                                  &
                     + dxcdx(elem(ielem)%local_e2n(ie,2),:))/3._dp

        yavg2 = (ym2 + yl2 + yc(elem(ielem)%local_e2n(ie,2)))/3._dp
          yavg2dy(:) = (dym2dy(:) + dyl2dy(:)                                  &
                     + dycdy(elem(ielem)%local_e2n(ie,2),:))/3._dp

        zavg2 = (zm2 + zl2 + zc(elem(ielem)%local_e2n(ie,2)))/3._dp
          zavg2dz(:) = (dzm2dz(:) + dzl2dz(:)                                  &
                     + dzcdz(elem(ielem)%local_e2n(ie,2),:))/3._dp

!       term1 = (xavg1*areax1 + yavg1*areay1 + zavg1*areaz1)/3._dp
         term1dx(:)=(yavg1*dareay1dx(:)+zavg1*dareaz1dx(:) + xavg1dx(:)*areax1)&
                   /3._dp
         term1dy(:)=(xavg1*dareax1dy(:)+zavg1*dareaz1dy(:) + yavg1dy(:)*areay1)&
                   /3._dp
         term1dz(:)=(xavg1*dareax1dz(:)+yavg1*dareay1dz(:) + zavg1dz(:)*areaz1)&
                   /3._dp

!       term2 = (xavg2*areax2 + yavg2*areay2 + zavg2*areaz2)/3._dp
         term2dx(:)=(yavg2*dareay2dx(:)+zavg2*dareaz2dx(:) + xavg2dx(:)*areax2)&
                   /3._dp
         term2dy(:)=(xavg2*dareax2dy(:)+zavg2*dareaz2dy(:) + yavg2dy(:)*areay2)&
                   /3._dp
         term2dz(:)=(xavg2*dareax2dz(:)+yavg2*dareay2dz(:) + zavg2dz(:)*areaz2)&
                   /3._dp

!       vol1 = vol1 + term1
!       vol2 = vol2 - term2

        vol1dx(:) = vol1dx(:) + term1dx(:)
        vol1dy(:) = vol1dy(:) + term1dy(:)
        vol1dz(:) = vol1dz(:) + term1dz(:)

        vol2dx(:) = vol2dx(:) - term2dx(:)
        vol2dy(:) = vol2dy(:) - term2dy(:)
        vol2dz(:) = vol2dz(:) - term2dz(:)

        n1_local : if ( n1 <= nnodes0 ) then
          if ( eqn_set == compressible ) rho = soln%q_dof(1,n1)
          u = soln%q_dof(2,n1)
          v = soln%q_dof(3,n1)
          w = soln%q_dof(4,n1)
          do k = 1, elem(ielem)%node_per_cell
            inode = elem(ielem)%c2n(k,n)
            do j = 1, design%nfunctions
              factor2 = (yrotrate_ni*w - zrotrate_ni*v)                        &
                                            *sadj%rlam(2,n1,j)*sadj%coltag(2,n1)
              factor3 = (zrotrate_ni*u - xrotrate_ni*w)                        &
                                            *sadj%rlam(3,n1,j)*sadj%coltag(3,n1)
              factor4 = (xrotrate_ni*v - yrotrate_ni*u)                        &
                                            *sadj%rlam(4,n1,j)*sadj%coltag(4,n1)
              if ( eqn_set == compressible ) then
                factor2 = rho*factor2
                factor3 = rho*factor3
                factor4 = rho*factor4
              endif
              getg%drdxl(1,inode,j,1)=getg%drdxl(1,inode,j,1)+vol1dx(k)*factor2
              getg%drdxl(1,inode,j,1)=getg%drdxl(1,inode,j,1)+vol1dx(k)*factor3
              getg%drdxl(1,inode,j,1)=getg%drdxl(1,inode,j,1)+vol1dx(k)*factor4

              getg%drdxl(2,inode,j,1)=getg%drdxl(2,inode,j,1)+vol1dy(k)*factor2
              getg%drdxl(2,inode,j,1)=getg%drdxl(2,inode,j,1)+vol1dy(k)*factor3
              getg%drdxl(2,inode,j,1)=getg%drdxl(2,inode,j,1)+vol1dy(k)*factor4

              getg%drdxl(3,inode,j,1)=getg%drdxl(3,inode,j,1)+vol1dz(k)*factor2
              getg%drdxl(3,inode,j,1)=getg%drdxl(3,inode,j,1)+vol1dz(k)*factor3
              getg%drdxl(3,inode,j,1)=getg%drdxl(3,inode,j,1)+vol1dz(k)*factor4
            end do
          end do
        endif n1_local

        n2_local : if ( n2 <= nnodes0 ) then
          if ( eqn_set == compressible ) rho = soln%q_dof(1,n2)
          u   = soln%q_dof(2,n2)
          v   = soln%q_dof(3,n2)
          w   = soln%q_dof(4,n2)
          do k = 1, elem(ielem)%node_per_cell
            inode = elem(ielem)%c2n(k,n)
            do j = 1, design%nfunctions
              factor2 = (yrotrate_ni*w - zrotrate_ni*v)                        &
                                            *sadj%rlam(2,n2,j)*sadj%coltag(2,n2)
              factor3 = (zrotrate_ni*u - xrotrate_ni*w)                        &
                                            *sadj%rlam(3,n2,j)*sadj%coltag(3,n2)
              factor4 = (xrotrate_ni*v - yrotrate_ni*u)                        &
                                            *sadj%rlam(4,n2,j)*sadj%coltag(4,n2)
              if ( eqn_set == compressible ) then
                factor2 = rho*factor2
                factor3 = rho*factor3
                factor4 = rho*factor4
              endif
              getg%drdxl(1,inode,j,1)=getg%drdxl(1,inode,j,1)+vol2dx(k)*factor2
              getg%drdxl(1,inode,j,1)=getg%drdxl(1,inode,j,1)+vol2dx(k)*factor3
              getg%drdxl(1,inode,j,1)=getg%drdxl(1,inode,j,1)+vol2dx(k)*factor4

              getg%drdxl(2,inode,j,1)=getg%drdxl(2,inode,j,1)+vol2dy(k)*factor2
              getg%drdxl(2,inode,j,1)=getg%drdxl(2,inode,j,1)+vol2dy(k)*factor3
              getg%drdxl(2,inode,j,1)=getg%drdxl(2,inode,j,1)+vol2dy(k)*factor4

              getg%drdxl(3,inode,j,1)=getg%drdxl(3,inode,j,1)+vol2dz(k)*factor2
              getg%drdxl(3,inode,j,1)=getg%drdxl(3,inode,j,1)+vol2dz(k)*factor3
              getg%drdxl(3,inode,j,1)=getg%drdxl(3,inode,j,1)+vol2dz(k)*factor4
            end do
          end do
        endif n2_local

      end do edge_loop_cell

     end do cell_loop

    end do elem_loop1

! close off the boundaries

    call dcoriolisb(nnodes0,nbound,x,y,z,bc,soln,sadj,design,getg,  &
                    eqn_set)

  end subroutine dcoriolis


!================================= DCORIOLISB ================================80
!
! Linearizes the Coriolis terms on the boundaries
!
!=============================================================================80
  subroutine dcoriolisb(nnodes0,nbound,x,y,z,bc,soln,sadj,design,getg,eqn_set)

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_half, my_1, my_2, my_3, my_4
    use solution_types,  only : soln_type
    use solution_adj,    only : sadj_type
    use design_types,    only : design_type
    use solution_getg,   only : getg_type
    use bc_types,        only : bcgrid_type
    use solution_types,  only : compressible
    use noninertials,    only : xrotrate_ni, yrotrate_ni, zrotrate_ni

    integer, intent(in) :: nnodes0, nbound, eqn_set

    real(dp), dimension(:), intent(in) :: x, y, z

    type(sadj_type),                     intent(in)    :: sadj
    type(design_type),                   intent(in)    :: design
    type(soln_type),                     intent(in)    :: soln
    type(bcgrid_type), dimension(:),     intent(in)    :: bc
    type(getg_type),                     intent(inout) :: getg

    integer :: face, nn, node1, node2, node3, node4, ib, j
    integer :: n1, n2, n3, n4, nn1, nn2, nn3, nn4
    integer :: cnode1, cnode2, cnode3, cnode4

    real(dp) :: areax, areay, areaz, rho, u, v, w, factor2, factor3, factor4
    real(dp) :: x0, y0, z0
    real(dp) :: xr, yr, zr
    real(dp) :: xl, yl, zl
    real(dp) :: xorig1,yorig1,zorig1
    real(dp) :: xorig2,yorig2,zorig2
    real(dp) :: xorig3,yorig3,zorig3
    real(dp) :: xorig4,yorig4,zorig4
    real(dp) :: xorig,yorig,zorig
    real(dp) :: xc_ref,yc_ref,zc_ref
    real(dp) :: xorig1x1,xorig1x2,xorig1x3,xorig1x4
    real(dp) :: yorig1y1,yorig1y2,yorig1y3,yorig1y4
    real(dp) :: zorig1z1,zorig1z2,zorig1z3,zorig1z4
    real(dp) :: xorig2x1,xorig2x2,xorig2x3,xorig2x4
    real(dp) :: yorig2y1,yorig2y2,yorig2y3,yorig2y4
    real(dp) :: zorig2z1,zorig2z2,zorig2z3,zorig2z4
    real(dp) :: xorig3x1,xorig3x2,xorig3x3,xorig3x4
    real(dp) :: yorig3y1,yorig3y2,yorig3y3,yorig3y4
    real(dp) :: zorig3z1,zorig3z2,zorig3z3,zorig3z4
    real(dp) :: xorig4x1,xorig4x2,xorig4x3,xorig4x4
    real(dp) :: yorig4y1,yorig4y2,yorig4y3,yorig4y4
    real(dp) :: zorig4z1,zorig4z2,zorig4z3,zorig4z4
    real(dp) :: xorigx1,xorigx2,xorigx3,xorigx4
    real(dp) :: yorigy1,yorigy2,yorigy3,yorigy4
    real(dp) :: zorigz1,zorigz2,zorigz3,zorigz4
    real(dp) :: xc_refx1,xc_refx2,xc_refx3,xc_refx4
    real(dp) :: yc_refy1,yc_refy2,yc_refy3,yc_refy4
    real(dp) :: zc_refz1,zc_refz2,zc_refz3,zc_refz4
    real(dp) :: x1,x2,x3,x4,y1,y2,y3,y4,z1,z2,z3,z4
    real(dp) :: x0x1,x0x2,x0x3,x0x4,y0y1,y0y2,y0y3,y0y4,z0z1,z0z2,z0z3,z0z4
    real(dp) :: x1x1,x1x2,x1x3,x1x4,x2x1,x2x2,x2x3,x2x4,x3x1,x3x2,x3x3
    real(dp) :: y1y1,y1y2,y1y3,y1y4,y2y1,y2y2,y2y3,y2y4,y3y1,y3y2,y3y3
    real(dp) :: z1z1,z1z2,z1z3,z1z4,z2z1,z2z2,z2z3,z2z4,z3z1,z3z2,z3z3
    real(dp) :: x4x1,x4x2,x4x3,x4x4,y4y1,y4y2,y4y3,y4y4,z4z1,z4z2,z4z3,z4z4
    real(dp) :: xlx1,xlx2,xlx3,xlx4,yly1,yly2,yly3,yly4,zlz1,zlz2,zlz3,zlz4
    real(dp) :: xrx1,xrx2,xrx3,xrx4,yry1,yry2,yry3,yry4,zrz1,zrz2,zrz3,zrz4
    real(dp) :: areaxx1,areaxx2,areaxx3,areaxx4
    real(dp) :: areaxy1,areaxy2,areaxy3,areaxy4
    real(dp) :: areaxz1,areaxz2,areaxz3,areaxz4
    real(dp) :: areayx1,areayx2,areayx3,areayx4
    real(dp) :: areayy1,areayy2,areayy3,areayy4
    real(dp) :: areayz1,areayz2,areayz3,areayz4
    real(dp) :: areazx1,areazx2,areazx3,areazx4
    real(dp) :: areazy1,areazy2,areazy3,areazy4
    real(dp) :: areazz1,areazz2,areazz3,areazz4
    real(dp) :: volx1,volx2,volx3,volx4
    real(dp) :: voly1,voly2,voly3,voly4
    real(dp) :: volz1,volz2,volz3,volz4
    real(dp) :: xavg,yavg,zavg
    real(dp) :: xavgx1,xavgx2,xavgx3,xavgx4
    real(dp) :: yavgy1,yavgy2,yavgy3,yavgy4
    real(dp) :: zavgz1,zavgz2,zavgz3,zavgz4
    real(dp) :: termx1,termx2,termx3,termx4
    real(dp) :: termy1,termy2,termy3,termy4
    real(dp) :: termz1,termz2,termz3,termz4

    real(dp), dimension(4) :: xc, yc, zc
    real(dp), dimension(4) :: xcx1,xcx2,xcx3,ycy1,ycy2,ycy3,zcz1,zcz2,zcz3
    real(dp), dimension(4) :: xcx4,ycy4,zcz4

  continue

! avoid used uninitialized warnings

    nn1 = 0; nn2 = 0; nn3 = 0; nn4 = 0

    x1x1=my_0; x1x2=my_0; x1x3=my_0; x1x4=my_0
    x2x1=my_0; x2x2=my_0; x2x3=my_0; x2x4=my_0
    x3x1=my_0; x3x2=my_0; x3x3=my_0
    x4x1=my_0; x4x2=my_0; x4x3=my_0; x4x4=my_0
    y1y1=my_0; y1y2=my_0; y1y3=my_0; y1y4=my_0
    y2y1=my_0; y2y2=my_0; y2y3=my_0; y2y4=my_0
    y3y1=my_0; y3y2=my_0; y3y3=my_0
    y4y1=my_0; y4y2=my_0; y4y3=my_0; y4y4=my_0
    z1z1=my_0; z1z2=my_0; z1z3=my_0; z1z4=my_0
    z2z1=my_0; z2z2=my_0; z2z3=my_0; z2z4=my_0
    z3z1=my_0; z3z2=my_0; z3z3=my_0
    z4z1=my_0; z4z2=my_0; z4z3=my_0; z4z4=my_0

    xorig=my_0; xorigx1=my_0; xorigx2=my_0; xorigx3=my_0; xorigx4=my_0
    yorig=my_0; yorigy1=my_0; yorigy2=my_0; yorigy3=my_0; yorigy4=my_0
    zorig=my_0; zorigz1=my_0; zorigz2=my_0; zorigz3=my_0; zorigz4=my_0

    xc_ref=my_0; xc_refx1=my_0; xc_refx2=my_0; xc_refx3=my_0; xc_refx4=my_0
    yc_ref=my_0; yc_refy1=my_0; yc_refy2=my_0; yc_refy3=my_0; yc_refy4=my_0
    zc_ref=my_0; zc_refz1=my_0; zc_refz2=my_0; zc_refz3=my_0; zc_refz4=my_0

    rho = my_0

    bound_loop : do ib = 1, nbound

      tria_face_loop : do face = 1, bc(ib)%nbfacet

        n1 = bc(ib)%f2ntb(face,1)
        n2 = bc(ib)%f2ntb(face,2)
        n3 = bc(ib)%f2ntb(face,3)

        node1 = bc(ib)%ibnode(n1)
        node2 = bc(ib)%ibnode(n2)
        node3 = bc(ib)%ibnode(n3)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3

        xorig1 = x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0

        yorig1 = y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0

        zorig1 = z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0

        xorig2 = x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0

        yorig2 = y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0

        zorig2 = z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0

        xorig3 = x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1

        yorig3 = y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1

        zorig3 = z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1

        xc(1)=((x(node2)-xorig1) + (x(node3)-xorig1))/my_3
          xcx1(1)=(-xorig1x1-xorig1x1)/my_3
          xcx2(1)=my_1/my_3
          xcx3(1)=my_1/my_3

        yc(1)=((y(node2)-yorig1) + (y(node3)-yorig1))/my_3
          ycy1(1)=(-yorig1y1-yorig1y1)/my_3
          ycy2(1)=my_1/my_3
          ycy3(1)=my_1/my_3

        zc(1)=((z(node2)-zorig1) + (z(node3)-zorig1))/my_3
          zcz1(1)=(-zorig1z1-zorig1z1)/my_3
          zcz2(1)=my_1/my_3
          zcz3(1)=my_1/my_3

        xc(2)=((x(node1)-xorig2) + (x(node3)-xorig2))/my_3
          xcx1(2)=my_1/my_3
          xcx2(2)=(-xorig2x2-xorig2x2)/my_3
          xcx3(2)=my_1/my_3

        yc(2)=((y(node1)-yorig2) + (y(node3)-yorig2))/my_3
          ycy1(2)=my_1/my_3
          ycy2(2)=(-yorig2y2-yorig2y2)/my_3
          ycy3(2)=my_1/my_3

        zc(2)=((z(node1)-zorig2) + (z(node3)-zorig2))/my_3
          zcz1(2)=my_1/my_3
          zcz2(2)=(-zorig2z2-zorig2z2)/my_3
          zcz3(2)=my_1/my_3

        xc(3)=((x(node1)-xorig3) + (x(node2)-xorig3))/my_3
          xcx1(3)=my_1/my_3
          xcx2(3)=my_1/my_3
          xcx3(3)=(-xorig3x3-xorig3x3)/my_3

        yc(3)=((y(node1)-yorig3) + (y(node2)-yorig3))/my_3
          ycy1(3)=my_1/my_3
          ycy2(3)=my_1/my_3
          ycy3(3)=(-yorig3y3-yorig3y3)/my_3

        zc(3)=((z(node1)-zorig3) + (z(node2)-zorig3))/my_3
          zcz1(3)=my_1/my_3
          zcz2(3)=my_1/my_3
          zcz3(3)=(-zorig3z3-zorig3z3)/my_3

        tria_node_loop : do nn = 1, 3

          volx1 = 0.0_dp
          volx2 = 0.0_dp
          volx3 = 0.0_dp

          voly1 = 0.0_dp
          voly2 = 0.0_dp
          voly3 = 0.0_dp

          volz1 = 0.0_dp
          volz2 = 0.0_dp
          volz3 = 0.0_dp

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
            case(3)
              nn1 = n3
              nn2 = n1
              nn3 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
            case default
          end select

          node1 = bc(ib)%ibnode(nn1)
          node2 = bc(ib)%ibnode(nn2)
          node3 = bc(ib)%ibnode(nn3)

          x1 = x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
            endif

          x2 = x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
            endif

          x3 = x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
            endif

          y1 = y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
            endif

          y2 = y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
            endif

          y3 = y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
            endif

          z1 = z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
            endif

          z2 = z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
            endif

          z3 = z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3

          xl = ((x1-xorig) + (x3-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x3x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x3x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x3x3-xorigx3))/my_2

          yl = ((y1-yorig) + (y3-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y3y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y3y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y3y3-yorigy3))/my_2

          zl = ((z1-zorig) + (z3-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z3z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z3z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z3z3-zorigz3))/my_2

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

        xavg = (x0 + xr + xc_ref)/3._dp
          xavgx1 = (x0x1 + xrx1 + xc_refx1)/3._dp
          xavgx2 = (x0x2 + xrx2 + xc_refx2)/3._dp
          xavgx3 = (x0x3 + xrx3 + xc_refx3)/3._dp

        yavg = (y0 + yr + yc_ref)/3._dp
          yavgy1 = (y0y1 + yry1 + yc_refy1)/3._dp
          yavgy2 = (y0y2 + yry2 + yc_refy2)/3._dp
          yavgy3 = (y0y3 + yry3 + yc_refy3)/3._dp

        zavg = (z0 + zr + zc_ref)/3._dp
          zavgz1 = (z0z1 + zrz1 + zc_refz1)/3._dp
          zavgz2 = (z0z2 + zrz2 + zc_refz2)/3._dp
          zavgz3 = (z0z3 + zrz3 + zc_refz3)/3._dp

!       term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
          termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
          termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
          termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp

          termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
          termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
          termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp

          termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
          termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
          termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp

!       vol(node1) = vol(node1) - term

          volx1 = volx1 - termx1
          volx2 = volx2 - termx2
          volx3 = volx3 - termx3

          voly1 = voly1 - termy1
          voly2 = voly2 - termy2
          voly3 = voly3 - termy3

          volz1 = volz1 - termz1
          volz2 = volz2 - termz2
          volz3 = volz3 - termz3

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0

          xavg = (x0 + xc_ref + xl)/3._dp
            xavgx1 = (x0x1 + xlx1 + xc_refx1)/3._dp
            xavgx2 = (x0x2 + xlx2 + xc_refx2)/3._dp
            xavgx3 = (x0x3 + xlx3 + xc_refx3)/3._dp

          yavg = (y0 + yc_ref + yl)/3._dp
            yavgy1 = (y0y1 + yly1 + yc_refy1)/3._dp
            yavgy2 = (y0y2 + yly2 + yc_refy2)/3._dp
            yavgy3 = (y0y3 + yly3 + yc_refy3)/3._dp

          zavg = (z0 + zc_ref + zl)/3._dp
            zavgz1 = (z0z1 + zlz1 + zc_refz1)/3._dp
            zavgz2 = (z0z2 + zlz2 + zc_refz2)/3._dp
            zavgz3 = (z0z3 + zlz3 + zc_refz3)/3._dp

!         term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
            termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
            termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
            termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp

            termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
            termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
            termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp

            termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
            termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
            termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp

!         vol(node1) = vol(node1) - term

            volx1 = volx1 - termx1
            volx2 = volx2 - termx2
            volx3 = volx3 - termx3

            voly1 = voly1 - termy1
            voly2 = voly2 - termy2
            voly3 = voly3 - termy3

            volz1 = volz1 - termz1
            volz2 = volz2 - termz2
            volz3 = volz3 - termz3

          node1_locala : if ( node1 <= nnodes0 ) then
            if ( eqn_set == compressible ) rho = soln%q_dof(1,node1)
            u = soln%q_dof(2,node1)
            v = soln%q_dof(3,node1)
            w = soln%q_dof(4,node1)

            do j = 1, design%nfunctions
              factor2 = (yrotrate_ni*w - zrotrate_ni*v)                        &
                                      *sadj%rlam(2,node1,j)*sadj%coltag(2,node1)
              factor3 = (zrotrate_ni*u - xrotrate_ni*w)                        &
                                      *sadj%rlam(3,node1,j)*sadj%coltag(3,node1)
              factor4 = (xrotrate_ni*v - yrotrate_ni*u)                        &
                                      *sadj%rlam(4,node1,j)*sadj%coltag(4,node1)
              if ( eqn_set == compressible ) then
                factor2 = rho*factor2
                factor3 = rho*factor3
                factor4 = rho*factor4
              endif

              getg%drdxl(1,cnode1,j,1)=getg%drdxl(1,cnode1,j,1)+volx1*factor2
              getg%drdxl(1,cnode1,j,1)=getg%drdxl(1,cnode1,j,1)+volx1*factor3
              getg%drdxl(1,cnode1,j,1)=getg%drdxl(1,cnode1,j,1)+volx1*factor4

              getg%drdxl(2,cnode1,j,1)=getg%drdxl(2,cnode1,j,1)+voly1*factor2
              getg%drdxl(2,cnode1,j,1)=getg%drdxl(2,cnode1,j,1)+voly1*factor3
              getg%drdxl(2,cnode1,j,1)=getg%drdxl(2,cnode1,j,1)+voly1*factor4

              getg%drdxl(3,cnode1,j,1)=getg%drdxl(3,cnode1,j,1)+volz1*factor2
              getg%drdxl(3,cnode1,j,1)=getg%drdxl(3,cnode1,j,1)+volz1*factor3
              getg%drdxl(3,cnode1,j,1)=getg%drdxl(3,cnode1,j,1)+volz1*factor4

              getg%drdxl(1,cnode2,j,1)=getg%drdxl(1,cnode2,j,1)+volx2*factor2
              getg%drdxl(1,cnode2,j,1)=getg%drdxl(1,cnode2,j,1)+volx2*factor3
              getg%drdxl(1,cnode2,j,1)=getg%drdxl(1,cnode2,j,1)+volx2*factor4

              getg%drdxl(2,cnode2,j,1)=getg%drdxl(2,cnode2,j,1)+voly2*factor2
              getg%drdxl(2,cnode2,j,1)=getg%drdxl(2,cnode2,j,1)+voly2*factor3
              getg%drdxl(2,cnode2,j,1)=getg%drdxl(2,cnode2,j,1)+voly2*factor4

              getg%drdxl(3,cnode2,j,1)=getg%drdxl(3,cnode2,j,1)+volz2*factor2
              getg%drdxl(3,cnode2,j,1)=getg%drdxl(3,cnode2,j,1)+volz2*factor3
              getg%drdxl(3,cnode2,j,1)=getg%drdxl(3,cnode2,j,1)+volz2*factor4

              getg%drdxl(1,cnode3,j,1)=getg%drdxl(1,cnode3,j,1)+volx3*factor2
              getg%drdxl(1,cnode3,j,1)=getg%drdxl(1,cnode3,j,1)+volx3*factor3
              getg%drdxl(1,cnode3,j,1)=getg%drdxl(1,cnode3,j,1)+volx3*factor4

              getg%drdxl(2,cnode3,j,1)=getg%drdxl(2,cnode3,j,1)+voly3*factor2
              getg%drdxl(2,cnode3,j,1)=getg%drdxl(2,cnode3,j,1)+voly3*factor3
              getg%drdxl(2,cnode3,j,1)=getg%drdxl(2,cnode3,j,1)+voly3*factor4

              getg%drdxl(3,cnode3,j,1)=getg%drdxl(3,cnode3,j,1)+volz3*factor2
              getg%drdxl(3,cnode3,j,1)=getg%drdxl(3,cnode3,j,1)+volz3*factor3
              getg%drdxl(3,cnode3,j,1)=getg%drdxl(3,cnode3,j,1)+volz3*factor4

            end do
          endif node1_locala

        end do tria_node_loop

      end do tria_face_loop

      quad_face_loop : do face = 1, bc(ib)%nbfaceq

        n1 = bc(ib)%f2nqb(face,1)
        n2 = bc(ib)%f2nqb(face,2)
        n3 = bc(ib)%f2nqb(face,3)
        n4 = bc(ib)%f2nqb(face,4)

        node1 = bc(ib)%ibnode(n1)
        node2 = bc(ib)%ibnode(n2)
        node3 = bc(ib)%ibnode(n3)
        node4 = bc(ib)%ibnode(n4)

        cnode1 = node1
        cnode2 = node2
        cnode3 = node3
        cnode4 = node4

        xorig1 = x(node1)
          xorig1x1 = my_1
          xorig1x2 = my_0
          xorig1x3 = my_0
          xorig1x4 = my_0

        yorig1 = y(node1)
          yorig1y1 = my_1
          yorig1y2 = my_0
          yorig1y3 = my_0
          yorig1y4 = my_0

        zorig1 = z(node1)
          zorig1z1 = my_1
          zorig1z2 = my_0
          zorig1z3 = my_0
          zorig1z4 = my_0

        xorig2 = x(node2)
          xorig2x1 = my_0
          xorig2x2 = my_1
          xorig2x3 = my_0
          xorig2x4 = my_0

        yorig2 = y(node2)
          yorig2y1 = my_0
          yorig2y2 = my_1
          yorig2y3 = my_0
          yorig2y4 = my_0

        zorig2 = z(node2)
          zorig2z1 = my_0
          zorig2z2 = my_1
          zorig2z3 = my_0
          zorig2z4 = my_0

        xorig3 = x(node3)
          xorig3x1 = my_0
          xorig3x2 = my_0
          xorig3x3 = my_1
          xorig3x4 = my_0

        yorig3 = y(node3)
          yorig3y1 = my_0
          yorig3y2 = my_0
          yorig3y3 = my_1
          yorig3y4 = my_0

        zorig3 = z(node3)
          zorig3z1 = my_0
          zorig3z2 = my_0
          zorig3z3 = my_1
          zorig3z4 = my_0

        xorig4 = x(node4)
          xorig4x1 = my_0
          xorig4x2 = my_0
          xorig4x3 = my_0
          xorig4x4 = my_1

        yorig4 = y(node4)
          yorig4y1 = my_0
          yorig4y2 = my_0
          yorig4y3 = my_0
          yorig4y4 = my_1

        zorig4 = z(node4)
          zorig4z1 = my_0
          zorig4z2 = my_0
          zorig4z3 = my_0
          zorig4z4 = my_1

        xc(1)=((x(node2)-xorig1) + (x(node3)-xorig1) + (x(node4)-xorig1))/my_4
          xcx1(1)=(-xorig1x1-xorig1x1-xorig1x1)/my_4
          xcx2(1)=my_1/my_4
          xcx3(1)=my_1/my_4
          xcx4(1)=my_1/my_4

        yc(1)=((y(node2)-yorig1) + (y(node3)-yorig1) + (y(node4)-yorig1))/my_4
          ycy1(1)=(-yorig1y1-yorig1y1-yorig1y1)/my_4
          ycy2(1)=my_1/my_4
          ycy3(1)=my_1/my_4
          ycy4(1)=my_1/my_4

        zc(1)=((z(node2)-zorig1) + (z(node3)-zorig1) + (z(node4)-zorig1))/my_4
          zcz1(1)=(-zorig1z1-zorig1z1-zorig1z1)/my_4
          zcz2(1)=my_1/my_4
          zcz3(1)=my_1/my_4
          zcz4(1)=my_1/my_4

        xc(2)=((x(node1)-xorig2) + (x(node3)-xorig2) + (x(node4)-xorig2))/my_4
          xcx1(2)=my_1/my_4
          xcx2(2)=(-xorig2x2-xorig2x2-xorig2x2)/my_4
          xcx3(2)=my_1/my_4
          xcx4(2)=my_1/my_4

        yc(2)=((y(node1)-yorig2) + (y(node3)-yorig2) + (y(node4)-yorig2))/my_4
          ycy1(2)=my_1/my_4
          ycy2(2)=(-yorig2y2-yorig2y2-yorig2y2)/my_4
          ycy3(2)=my_1/my_4
          ycy4(2)=my_1/my_4

        zc(2)=((z(node1)-zorig2) + (z(node3)-zorig2) + (z(node4)-zorig2))/my_4
          zcz1(2)=my_1/my_4
          zcz2(2)=(-zorig2z2-zorig2z2-zorig2z2)/my_4
          zcz3(2)=my_1/my_4
          zcz4(2)=my_1/my_4

        xc(3)=((x(node1)-xorig3) + (x(node2)-xorig3) + (x(node4)-xorig3))/my_4
          xcx1(3)=my_1/my_4
          xcx2(3)=my_1/my_4
          xcx3(3)=(-xorig3x3-xorig3x3-xorig3x3)/my_4
          xcx4(3)=my_1/my_4

        yc(3)=((y(node1)-yorig3) + (y(node2)-yorig3) + (y(node4)-yorig3))/my_4
          ycy1(3)=my_1/my_4
          ycy2(3)=my_1/my_4
          ycy3(3)=(-yorig3y3-yorig3y3-yorig3y3)/my_4
          ycy4(3)=my_1/my_4

        zc(3)=((z(node1)-zorig3) + (z(node2)-zorig3) + (z(node4)-zorig3))/my_4
          zcz1(3)=my_1/my_4
          zcz2(3)=my_1/my_4
          zcz3(3)=(-zorig3z3-zorig3z3-zorig3z3)/my_4
          zcz4(3)=my_1/my_4



        xc(4)=((x(node1)-xorig4) + (x(node2)-xorig4) + (x(node3)-xorig4))/my_4
          xcx1(4)=my_1/my_4
          xcx2(4)=my_1/my_4
          xcx3(4)=my_1/my_4
          xcx4(4)=(-xorig4x4-xorig4x4-xorig4x4)/my_4

        yc(4)=((y(node1)-yorig4) + (y(node2)-yorig4) + (y(node3)-yorig4))/my_4
          ycy1(4)=my_1/my_4
          ycy2(4)=my_1/my_4
          ycy3(4)=my_1/my_4
          ycy4(4)=(-yorig4y4-yorig4y4-yorig4y4)/my_4

        zc(4)=((z(node1)-zorig4) + (z(node2)-zorig4) + (z(node3)-zorig4))/my_4
          zcz1(4)=my_1/my_4
          zcz2(4)=my_1/my_4
          zcz3(4)=my_1/my_4
          zcz4(4)=(-zorig4z4-zorig4z4-zorig4z4)/my_4

        quad_node_loop : do nn = 1, 4

          volx1 = 0.0_dp
          volx2 = 0.0_dp
          volx3 = 0.0_dp
          volx4 = 0.0_dp

          voly1 = 0.0_dp
          voly2 = 0.0_dp
          voly3 = 0.0_dp
          voly4 = 0.0_dp

          volz1 = 0.0_dp
          volz2 = 0.0_dp
          volz3 = 0.0_dp
          volz4 = 0.0_dp

          select case(nn)
            case(1)
              nn1 = n1
              nn2 = n2
              nn3 = n3
              nn4 = n4
              xorig = xorig1
                xorigx1 = xorig1x1
                xorigx2 = xorig1x2
                xorigx3 = xorig1x3
                xorigx4 = xorig1x4
              yorig = yorig1
                yorigy1 = yorig1y1
                yorigy2 = yorig1y2
                yorigy3 = yorig1y3
                yorigy4 = yorig1y4
              zorig = zorig1
                zorigz1 = zorig1z1
                zorigz2 = zorig1z2
                zorigz3 = zorig1z3
                zorigz4 = zorig1z4
              xc_ref = xc(1)
                xc_refx1 = xcx1(1)
                xc_refx2 = xcx2(1)
                xc_refx3 = xcx3(1)
                xc_refx4 = xcx4(1)
              yc_ref = yc(1)
                yc_refy1 = ycy1(1)
                yc_refy2 = ycy2(1)
                yc_refy3 = ycy3(1)
                yc_refy4 = ycy4(1)
              zc_ref = zc(1)
                zc_refz1 = zcz1(1)
                zc_refz2 = zcz2(1)
                zc_refz3 = zcz3(1)
                zc_refz4 = zcz4(1)
            case(2)
              nn1 = n2
              nn2 = n3
              nn3 = n4
              nn4 = n1
              xorig = xorig2
                xorigx1 = xorig2x1
                xorigx2 = xorig2x2
                xorigx3 = xorig2x3
                xorigx4 = xorig2x4
              yorig = yorig2
                yorigy1 = yorig2y1
                yorigy2 = yorig2y2
                yorigy3 = yorig2y3
                yorigy4 = yorig2y4
              zorig = zorig2
                zorigz1 = zorig2z1
                zorigz2 = zorig2z2
                zorigz3 = zorig2z3
                zorigz4 = zorig2z4
              xc_ref = xc(2)
                xc_refx1 = xcx1(2)
                xc_refx2 = xcx2(2)
                xc_refx3 = xcx3(2)
                xc_refx4 = xcx4(2)
              yc_ref = yc(2)
                yc_refy1 = ycy1(2)
                yc_refy2 = ycy2(2)
                yc_refy3 = ycy3(2)
                yc_refy4 = ycy4(2)
              zc_ref = zc(2)
                zc_refz1 = zcz1(2)
                zc_refz2 = zcz2(2)
                zc_refz3 = zcz3(2)
                zc_refz4 = zcz4(2)
            case(3)
              nn1 = n3
              nn2 = n4
              nn3 = n1
              nn4 = n2
              xorig = xorig3
                xorigx1 = xorig3x1
                xorigx2 = xorig3x2
                xorigx3 = xorig3x3
                xorigx4 = xorig3x4
              yorig = yorig3
                yorigy1 = yorig3y1
                yorigy2 = yorig3y2
                yorigy3 = yorig3y3
                yorigy4 = yorig3y4
              zorig = zorig3
                zorigz1 = zorig3z1
                zorigz2 = zorig3z2
                zorigz3 = zorig3z3
                zorigz4 = zorig3z4
              xc_ref = xc(3)
                xc_refx1 = xcx1(3)
                xc_refx2 = xcx2(3)
                xc_refx3 = xcx3(3)
                xc_refx4 = xcx4(3)
              yc_ref = yc(3)
                yc_refy1 = ycy1(3)
                yc_refy2 = ycy2(3)
                yc_refy3 = ycy3(3)
                yc_refy4 = ycy4(3)
              zc_ref = zc(3)
                zc_refz1 = zcz1(3)
                zc_refz2 = zcz2(3)
                zc_refz3 = zcz3(3)
                zc_refz4 = zcz4(3)
            case(4)
              nn1 = n4
              nn2 = n1
              nn3 = n2
              nn4 = n3
              xorig = xorig4
                xorigx1 = xorig4x1
                xorigx2 = xorig4x2
                xorigx3 = xorig4x3
                xorigx4 = xorig4x4
              yorig = yorig4
                yorigy1 = yorig4y1
                yorigy2 = yorig4y2
                yorigy3 = yorig4y3
                yorigy4 = yorig4y4
              zorig = zorig4
                zorigz1 = zorig4z1
                zorigz2 = zorig4z2
                zorigz3 = zorig4z3
                zorigz4 = zorig4z4
              xc_ref = xc(4)
                xc_refx1 = xcx1(4)
                xc_refx2 = xcx2(4)
                xc_refx3 = xcx3(4)
                xc_refx4 = xcx4(4)
              yc_ref = yc(4)
                yc_refy1 = ycy1(4)
                yc_refy2 = ycy2(4)
                yc_refy3 = ycy3(4)
                yc_refy4 = ycy4(4)
              zc_ref = zc(4)
                zc_refz1 = zcz1(4)
                zc_refz2 = zcz2(4)
                zc_refz3 = zcz3(4)
                zc_refz4 = zcz4(4)
            case default
          end select

          node1 = bc(ib)%ibnode(nn1)
          node2 = bc(ib)%ibnode(nn2)
          node3 = bc(ib)%ibnode(nn3)
          node4 = bc(ib)%ibnode(nn4)

          x1 = x(node1)
            if ( node1 == cnode1 ) then
              x1x1 = my_1
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode2 ) then
              x1x1 = my_0
              x1x2 = my_1
              x1x3 = my_0
              x1x4 = my_0
            else if ( node1 == cnode3 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_1
              x1x4 = my_0
            else if ( node1 == cnode4 ) then
              x1x1 = my_0
              x1x2 = my_0
              x1x3 = my_0
              x1x4 = my_1
            endif

          x2 = x(node2)
            if ( node2 == cnode1 ) then
              x2x1 = my_1
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode2 ) then
              x2x1 = my_0
              x2x2 = my_1
              x2x3 = my_0
              x2x4 = my_0
            else if ( node2 == cnode3 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_1
              x2x4 = my_0
            else if ( node2 == cnode4 ) then
              x2x1 = my_0
              x2x2 = my_0
              x2x3 = my_0
              x2x4 = my_1
            endif

          x3 = x(node3)
            if ( node3 == cnode1 ) then
              x3x1 = my_1
              x3x2 = my_0
              x3x3 = my_0
!             x3x4 = my_0
            else if ( node3 == cnode2 ) then
              x3x1 = my_0
              x3x2 = my_1
              x3x3 = my_0
!             x3x4 = my_0
            else if ( node3 == cnode3 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_1
!             x3x4 = my_0
            else if ( node3 == cnode4 ) then
              x3x1 = my_0
              x3x2 = my_0
              x3x3 = my_0
!             x3x4 = my_1
            endif

          x4 = x(node4)
            if ( node4 == cnode1 ) then
              x4x1 = my_1
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode2 ) then
              x4x1 = my_0
              x4x2 = my_1
              x4x3 = my_0
              x4x4 = my_0
            else if ( node4 == cnode3 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_1
              x4x4 = my_0
            else if ( node4 == cnode4 ) then
              x4x1 = my_0
              x4x2 = my_0
              x4x3 = my_0
              x4x4 = my_1
            endif

          y1 = y(node1)
            if ( node1 == cnode1 ) then
              y1y1 = my_1
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode2 ) then
              y1y1 = my_0
              y1y2 = my_1
              y1y3 = my_0
              y1y4 = my_0
            else if ( node1 == cnode3 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_1
              y1y4 = my_0
            else if ( node1 == cnode4 ) then
              y1y1 = my_0
              y1y2 = my_0
              y1y3 = my_0
              y1y4 = my_1
            endif

          y2 = y(node2)
            if ( node2 == cnode1 ) then
              y2y1 = my_1
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode2 ) then
              y2y1 = my_0
              y2y2 = my_1
              y2y3 = my_0
              y2y4 = my_0
            else if ( node2 == cnode3 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_1
              y2y4 = my_0
            else if ( node2 == cnode4 ) then
              y2y1 = my_0
              y2y2 = my_0
              y2y3 = my_0
              y2y4 = my_1
            endif

          y3 = y(node3)
            if ( node3 == cnode1 ) then
              y3y1 = my_1
              y3y2 = my_0
              y3y3 = my_0
!             y3y4 = my_0
            else if ( node3 == cnode2 ) then
              y3y1 = my_0
              y3y2 = my_1
              y3y3 = my_0
!             y3y4 = my_0
            else if ( node3 == cnode3 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_1
!             y3y4 = my_0
            else if ( node3 == cnode4 ) then
              y3y1 = my_0
              y3y2 = my_0
              y3y3 = my_0
!             y3y4 = my_1
            endif

          y4 = y(node4)
            if ( node4 == cnode1 ) then
              y4y1 = my_1
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode2 ) then
              y4y1 = my_0
              y4y2 = my_1
              y4y3 = my_0
              y4y4 = my_0
            else if ( node4 == cnode3 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_1
              y4y4 = my_0
            else if ( node4 == cnode4 ) then
              y4y1 = my_0
              y4y2 = my_0
              y4y3 = my_0
              y4y4 = my_1
            endif

          z1 = z(node1)
            if ( node1 == cnode1 ) then
              z1z1 = my_1
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode2 ) then
              z1z1 = my_0
              z1z2 = my_1
              z1z3 = my_0
              z1z4 = my_0
            else if ( node1 == cnode3 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_1
              z1z4 = my_0
            else if ( node1 == cnode4 ) then
              z1z1 = my_0
              z1z2 = my_0
              z1z3 = my_0
              z1z4 = my_1
            endif

          z2 = z(node2)
            if ( node2 == cnode1 ) then
              z2z1 = my_1
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode2 ) then
              z2z1 = my_0
              z2z2 = my_1
              z2z3 = my_0
              z2z4 = my_0
            else if ( node2 == cnode3 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_1
              z2z4 = my_0
            else if ( node2 == cnode4 ) then
              z2z1 = my_0
              z2z2 = my_0
              z2z3 = my_0
              z2z4 = my_1
            endif

          z3 = z(node3)
            if ( node3 == cnode1 ) then
              z3z1 = my_1
              z3z2 = my_0
              z3z3 = my_0
!             z3z4 = my_0
            else if ( node3 == cnode2 ) then
              z3z1 = my_0
              z3z2 = my_1
              z3z3 = my_0
!             z3z4 = my_0
            else if ( node3 == cnode3 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_1
!             z3z4 = my_0
            else if ( node3 == cnode4 ) then
              z3z1 = my_0
              z3z2 = my_0
              z3z3 = my_0
!             z3z4 = my_1
            endif

          z4 = z(node4)
            if ( node4 == cnode1 ) then
              z4z1 = my_1
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode2 ) then
              z4z1 = my_0
              z4z2 = my_1
              z4z3 = my_0
              z4z4 = my_0
            else if ( node4 == cnode3 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_1
              z4z4 = my_0
            else if ( node4 == cnode4 ) then
              z4z1 = my_0
              z4z2 = my_0
              z4z3 = my_0
              z4z4 = my_1
            endif

          x0 = x1 - xorig
            x0x1 = x1x1 - xorigx1
            x0x2 = x1x2 - xorigx2
            x0x3 = x1x3 - xorigx3
            x0x4 = x1x4 - xorigx4
          y0 = y1 - yorig
            y0y1 = y1y1 - yorigy1
            y0y2 = y1y2 - yorigy2
            y0y3 = y1y3 - yorigy3
            y0y4 = y1y4 - yorigy4
          z0 = z1 - zorig
            z0z1 = z1z1 - zorigz1
            z0z2 = z1z2 - zorigz2
            z0z3 = z1z3 - zorigz3
            z0z4 = z1z4 - zorigz4

          xl = ((x1-xorig) + (x4-xorig))/my_2
            xlx1 = ((x1x1-xorigx1) + (x4x1-xorigx1))/my_2
            xlx2 = ((x1x2-xorigx2) + (x4x2-xorigx2))/my_2
            xlx3 = ((x1x3-xorigx3) + (x4x3-xorigx3))/my_2
            xlx4 = ((x1x4-xorigx4) + (x4x4-xorigx4))/my_2

          yl = ((y1-yorig) + (y4-yorig))/my_2
            yly1 = ((y1y1-yorigy1) + (y4y1-yorigy1))/my_2
            yly2 = ((y1y2-yorigy2) + (y4y2-yorigy2))/my_2
            yly3 = ((y1y3-yorigy3) + (y4y3-yorigy3))/my_2
            yly4 = ((y1y4-yorigy4) + (y4y4-yorigy4))/my_2

          zl = ((z1-zorig) + (z4-zorig))/my_2
            zlz1 = ((z1z1-zorigz1) + (z4z1-zorigz1))/my_2
            zlz2 = ((z1z2-zorigz2) + (z4z2-zorigz2))/my_2
            zlz3 = ((z1z3-zorigz3) + (z4z3-zorigz3))/my_2
            zlz4 = ((z1z4-zorigz4) + (z4z4-zorigz4))/my_2

          xr = ((x1-xorig) + (x2-xorig))/my_2
            xrx1 = ((x1x1-xorigx1) + (x2x1-xorigx1))/my_2
            xrx2 = ((x1x2-xorigx2) + (x2x2-xorigx2))/my_2
            xrx3 = ((x1x3-xorigx3) + (x2x3-xorigx3))/my_2
            xrx4 = ((x1x4-xorigx4) + (x2x4-xorigx4))/my_2

          yr = ((y1-yorig) + (y2-yorig))/my_2
            yry1 = ((y1y1-yorigy1) + (y2y1-yorigy1))/my_2
            yry2 = ((y1y2-yorigy2) + (y2y2-yorigy2))/my_2
            yry3 = ((y1y3-yorigy3) + (y2y3-yorigy3))/my_2
            yry4 = ((y1y4-yorigy4) + (y2y4-yorigy4))/my_2

          zr = ((z1-zorig) + (z2-zorig))/my_2
            zrz1 = ((z1z1-zorigz1) + (z2z1-zorigz1))/my_2
            zrz2 = ((z1z2-zorigz2) + (z2z2-zorigz2))/my_2
            zrz3 = ((z1z3-zorigz3) + (z2z3-zorigz3))/my_2
            zrz4 = ((z1z4-zorigz4) + (z2z4-zorigz4))/my_2

!       triangle x0-xr-xc

          areax = my_half*( (yr-y0)*(zc_ref-z0) - (zr-z0)*(yc_ref-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yry1-y0y1)*(zc_ref-z0)- (zr-z0)*(yc_refy1-y0y1))
            areaxy2 = my_half*((yry2-y0y2)*(zc_ref-z0)- (zr-z0)*(yc_refy2-y0y2))
            areaxy3 = my_half*((yry3-y0y3)*(zc_ref-z0)- (zr-z0)*(yc_refy3-y0y3))
            areaxy4 = my_half*((yry4-y0y4)*(zc_ref-z0)- (zr-z0)*(yc_refy4-y0y4))

            areaxz1 = my_half*((yr-y0)*(zc_refz1-z0z1)- (zrz1-z0z1)*(yc_ref-y0))
            areaxz2 = my_half*((yr-y0)*(zc_refz2-z0z2)- (zrz2-z0z2)*(yc_ref-y0))
            areaxz3 = my_half*((yr-y0)*(zc_refz3-z0z3)- (zrz3-z0z3)*(yc_ref-y0))
            areaxz4 = my_half*((yr-y0)*(zc_refz4-z0z4)- (zrz4-z0z4)*(yc_ref-y0))

          areay = my_half*( (zr-z0)*(xc_ref-x0) - (xr-x0)*(zc_ref-z0) )
            areayx1 = my_half*((zr-z0)*(xc_refx1-x0x1)- (xrx1-x0x1)*(zc_ref-z0))
            areayx2 = my_half*((zr-z0)*(xc_refx2-x0x2)- (xrx2-x0x2)*(zc_ref-z0))
            areayx3 = my_half*((zr-z0)*(xc_refx3-x0x3)- (xrx3-x0x3)*(zc_ref-z0))
            areayx4 = my_half*((zr-z0)*(xc_refx4-x0x4)- (xrx4-x0x4)*(zc_ref-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zrz1-z0z1)*(xc_ref-x0)- (xr-x0)*(zc_refz1-z0z1))
            areayz2 = my_half*((zrz2-z0z2)*(xc_ref-x0)- (xr-x0)*(zc_refz2-z0z2))
            areayz3 = my_half*((zrz3-z0z3)*(xc_ref-x0)- (xr-x0)*(zc_refz3-z0z3))
            areayz4 = my_half*((zrz4-z0z4)*(xc_ref-x0)- (xr-x0)*(zc_refz4-z0z4))

          areaz = my_half*( (xr-x0)*(yc_ref-y0) - (yr-y0)*(xc_ref-x0) )
            areazx1 = my_half*((xrx1-x0x1)*(yc_ref-y0)- (yr-y0)*(xc_refx1-x0x1))
            areazx2 = my_half*((xrx2-x0x2)*(yc_ref-y0)- (yr-y0)*(xc_refx2-x0x2))
            areazx3 = my_half*((xrx3-x0x3)*(yc_ref-y0)- (yr-y0)*(xc_refx3-x0x3))
            areazx4 = my_half*((xrx4-x0x4)*(yc_ref-y0)- (yr-y0)*(xc_refx4-x0x4))

            areazy1 = my_half*((xr-x0)*(yc_refy1-y0y1)- (yry1-y0y1)*(xc_ref-x0))
            areazy2 = my_half*((xr-x0)*(yc_refy2-y0y2)- (yry2-y0y2)*(xc_ref-x0))
            areazy3 = my_half*((xr-x0)*(yc_refy3-y0y3)- (yry3-y0y3)*(xc_ref-x0))
            areazy4 = my_half*((xr-x0)*(yc_refy4-y0y4)- (yry4-y0y4)*(xc_ref-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

        xavg = (x0 + xr + xc_ref)/3._dp
          xavgx1 = (x0x1 + xrx1 + xc_refx1)/3._dp
          xavgx2 = (x0x2 + xrx2 + xc_refx2)/3._dp
          xavgx3 = (x0x3 + xrx3 + xc_refx3)/3._dp
          xavgx4 = (x0x4 + xrx4 + xc_refx4)/3._dp

        yavg = (y0 + yr + yc_ref)/3._dp
          yavgy1 = (y0y1 + yry1 + yc_refy1)/3._dp
          yavgy2 = (y0y2 + yry2 + yc_refy2)/3._dp
          yavgy3 = (y0y3 + yry3 + yc_refy3)/3._dp
          yavgy4 = (y0y4 + yry4 + yc_refy4)/3._dp

        zavg = (z0 + zr + zc_ref)/3._dp
          zavgz1 = (z0z1 + zrz1 + zc_refz1)/3._dp
          zavgz2 = (z0z2 + zrz2 + zc_refz2)/3._dp
          zavgz3 = (z0z3 + zrz3 + zc_refz3)/3._dp
          zavgz4 = (z0z4 + zrz4 + zc_refz4)/3._dp

!       term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
          termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
          termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
          termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp
          termx4 = (xavg*areaxx4+yavg*areayx4+zavg*areazx4+xavgx4*areax)/3._dp

          termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
          termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
          termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp
          termy4 = (xavg*areaxy4+yavg*areayy4+zavg*areazy4+yavgy4*areay)/3._dp

          termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
          termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
          termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp
          termz4 = (xavg*areaxz4+yavg*areayz4+zavg*areazz4+zavgz4*areaz)/3._dp

!       vol(node1) = vol(node1) - term

          volx1 = volx1 - termx1
          volx2 = volx2 - termx2
          volx3 = volx3 - termx3
          volx4 = volx4 - termx4

          voly1 = voly1 - termy1
          voly2 = voly2 - termy2
          voly3 = voly3 - termy3
          voly4 = voly4 - termy4

          volz1 = volz1 - termz1
          volz2 = volz2 - termz2
          volz3 = volz3 - termz3
          volz4 = volz4 - termz4

!       triangle x0-xc-xl

          areax = my_half*( (yc_ref-y0)*(zl-z0) - (zc_ref-z0)*(yl-y0) )
            areaxx1 = my_0
            areaxx2 = my_0
            areaxx3 = my_0
            areaxx4 = my_0

            areaxy1 = my_half*((yc_refy1-y0y1)*(zl-z0)- (zc_ref-z0)*(yly1-y0y1))
            areaxy2 = my_half*((yc_refy2-y0y2)*(zl-z0)- (zc_ref-z0)*(yly2-y0y2))
            areaxy3 = my_half*((yc_refy3-y0y3)*(zl-z0)- (zc_ref-z0)*(yly3-y0y3))
            areaxy4 = my_half*((yc_refy4-y0y4)*(zl-z0)- (zc_ref-z0)*(yly4-y0y4))

            areaxz1 = my_half*((yc_ref-y0)*(zlz1-z0z1)- (zc_refz1-z0z1)*(yl-y0))
            areaxz2 = my_half*((yc_ref-y0)*(zlz2-z0z2)- (zc_refz2-z0z2)*(yl-y0))
            areaxz3 = my_half*((yc_ref-y0)*(zlz3-z0z3)- (zc_refz3-z0z3)*(yl-y0))
            areaxz4 = my_half*((yc_ref-y0)*(zlz4-z0z4)- (zc_refz4-z0z4)*(yl-y0))

          areay = my_half*( (zc_ref-z0)*(xl-x0) - (xc_ref-x0)*(zl-z0) )
            areayx1 = my_half*((zc_ref-z0)*(xlx1-x0x1)- (xc_refx1-x0x1)*(zl-z0))
            areayx2 = my_half*((zc_ref-z0)*(xlx2-x0x2)- (xc_refx2-x0x2)*(zl-z0))
            areayx3 = my_half*((zc_ref-z0)*(xlx3-x0x3)- (xc_refx3-x0x3)*(zl-z0))
            areayx4 = my_half*((zc_ref-z0)*(xlx4-x0x4)- (xc_refx4-x0x4)*(zl-z0))

            areayy1 = my_0
            areayy2 = my_0
            areayy3 = my_0
            areayy4 = my_0

            areayz1 = my_half*((zc_refz1-z0z1)*(xl-x0)- (xc_ref-x0)*(zlz1-z0z1))
            areayz2 = my_half*((zc_refz2-z0z2)*(xl-x0)- (xc_ref-x0)*(zlz2-z0z2))
            areayz3 = my_half*((zc_refz3-z0z3)*(xl-x0)- (xc_ref-x0)*(zlz3-z0z3))
            areayz4 = my_half*((zc_refz4-z0z4)*(xl-x0)- (xc_ref-x0)*(zlz4-z0z4))

          areaz = my_half*( (xc_ref-x0)*(yl-y0) - (yc_ref-y0)*(xl-x0) )
            areazx1 = my_half*((xc_refx1-x0x1)*(yl-y0)- (yc_ref-y0)*(xlx1-x0x1))
            areazx2 = my_half*((xc_refx2-x0x2)*(yl-y0)- (yc_ref-y0)*(xlx2-x0x2))
            areazx3 = my_half*((xc_refx3-x0x3)*(yl-y0)- (yc_ref-y0)*(xlx3-x0x3))
            areazx4 = my_half*((xc_refx4-x0x4)*(yl-y0)- (yc_ref-y0)*(xlx4-x0x4))

            areazy1 = my_half*((xc_ref-x0)*(yly1-y0y1)- (yc_refy1-y0y1)*(xl-x0))
            areazy2 = my_half*((xc_ref-x0)*(yly2-y0y2)- (yc_refy2-y0y2)*(xl-x0))
            areazy3 = my_half*((xc_ref-x0)*(yly3-y0y3)- (yc_refy3-y0y3)*(xl-x0))
            areazy4 = my_half*((xc_ref-x0)*(yly4-y0y4)- (yc_refy4-y0y4)*(xl-x0))

            areazz1 = my_0
            areazz2 = my_0
            areazz3 = my_0
            areazz4 = my_0

          xavg = (x0 + xc_ref + xl)/3._dp
            xavgx1 = (x0x1 + xlx1 + xc_refx1)/3._dp
            xavgx2 = (x0x2 + xlx2 + xc_refx2)/3._dp
            xavgx3 = (x0x3 + xlx3 + xc_refx3)/3._dp
            xavgx4 = (x0x4 + xlx4 + xc_refx4)/3._dp

          yavg = (y0 + yc_ref + yl)/3._dp
            yavgy1 = (y0y1 + yly1 + yc_refy1)/3._dp
            yavgy2 = (y0y2 + yly2 + yc_refy2)/3._dp
            yavgy3 = (y0y3 + yly3 + yc_refy3)/3._dp
            yavgy4 = (y0y4 + yly4 + yc_refy4)/3._dp

          zavg = (z0 + zc_ref + zl)/3._dp
            zavgz1 = (z0z1 + zlz1 + zc_refz1)/3._dp
            zavgz2 = (z0z2 + zlz2 + zc_refz2)/3._dp
            zavgz3 = (z0z3 + zlz3 + zc_refz3)/3._dp
            zavgz4 = (z0z4 + zlz4 + zc_refz4)/3._dp

!         term = (xavg*areax + yavg*areay + zavg*areaz)/3._dp
            termx1 = (xavg*areaxx1+yavg*areayx1+zavg*areazx1+xavgx1*areax)/3._dp
            termx2 = (xavg*areaxx2+yavg*areayx2+zavg*areazx2+xavgx2*areax)/3._dp
            termx3 = (xavg*areaxx3+yavg*areayx3+zavg*areazx3+xavgx3*areax)/3._dp
            termx4 = (xavg*areaxx4+yavg*areayx4+zavg*areazx4+xavgx4*areax)/3._dp

            termy1 = (xavg*areaxy1+yavg*areayy1+zavg*areazy1+yavgy1*areay)/3._dp
            termy2 = (xavg*areaxy2+yavg*areayy2+zavg*areazy2+yavgy2*areay)/3._dp
            termy3 = (xavg*areaxy3+yavg*areayy3+zavg*areazy3+yavgy3*areay)/3._dp
            termy4 = (xavg*areaxy4+yavg*areayy4+zavg*areazy4+yavgy4*areay)/3._dp

            termz1 = (xavg*areaxz1+yavg*areayz1+zavg*areazz1+zavgz1*areaz)/3._dp
            termz2 = (xavg*areaxz2+yavg*areayz2+zavg*areazz2+zavgz2*areaz)/3._dp
            termz3 = (xavg*areaxz3+yavg*areayz3+zavg*areazz3+zavgz3*areaz)/3._dp
            termz4 = (xavg*areaxz4+yavg*areayz4+zavg*areazz4+zavgz4*areaz)/3._dp

!         vol(node1) = vol(node1) - term

            volx1 = volx1 - termx1
            volx2 = volx2 - termx2
            volx3 = volx3 - termx3
            volx4 = volx4 - termx4

            voly1 = voly1 - termy1
            voly2 = voly2 - termy2
            voly3 = voly3 - termy3
            voly4 = voly4 - termy4

            volz1 = volz1 - termz1
            volz2 = volz2 - termz2
            volz3 = volz3 - termz3
            volz4 = volz4 - termz4

          node1_localb : if ( node1 <= nnodes0 ) then
            if ( eqn_set == compressible ) rho = soln%q_dof(1,node1)
            u = soln%q_dof(2,node1)
            v = soln%q_dof(3,node1)
            w = soln%q_dof(4,node1)

            do j = 1, design%nfunctions
              factor2 = (yrotrate_ni*w - zrotrate_ni*v)                        &
                                      *sadj%rlam(2,node1,j)*sadj%coltag(2,node1)
              factor3 = (zrotrate_ni*u - xrotrate_ni*w)                        &
                                      *sadj%rlam(3,node1,j)*sadj%coltag(3,node1)
              factor4 = (xrotrate_ni*v - yrotrate_ni*u)                        &
                                      *sadj%rlam(4,node1,j)*sadj%coltag(4,node1)
              if ( eqn_set == compressible ) then
                factor2 = rho*factor2
                factor3 = rho*factor3
                factor4 = rho*factor4
              endif

              getg%drdxl(1,cnode1,j,1)=getg%drdxl(1,cnode1,j,1)+volx1*factor2
              getg%drdxl(1,cnode1,j,1)=getg%drdxl(1,cnode1,j,1)+volx1*factor3
              getg%drdxl(1,cnode1,j,1)=getg%drdxl(1,cnode1,j,1)+volx1*factor4

              getg%drdxl(2,cnode1,j,1)=getg%drdxl(2,cnode1,j,1)+voly1*factor2
              getg%drdxl(2,cnode1,j,1)=getg%drdxl(2,cnode1,j,1)+voly1*factor3
              getg%drdxl(2,cnode1,j,1)=getg%drdxl(2,cnode1,j,1)+voly1*factor4

              getg%drdxl(3,cnode1,j,1)=getg%drdxl(3,cnode1,j,1)+volz1*factor2
              getg%drdxl(3,cnode1,j,1)=getg%drdxl(3,cnode1,j,1)+volz1*factor3
              getg%drdxl(3,cnode1,j,1)=getg%drdxl(3,cnode1,j,1)+volz1*factor4

              getg%drdxl(1,cnode2,j,1)=getg%drdxl(1,cnode2,j,1)+volx2*factor2
              getg%drdxl(1,cnode2,j,1)=getg%drdxl(1,cnode2,j,1)+volx2*factor3
              getg%drdxl(1,cnode2,j,1)=getg%drdxl(1,cnode2,j,1)+volx2*factor4

              getg%drdxl(2,cnode2,j,1)=getg%drdxl(2,cnode2,j,1)+voly2*factor2
              getg%drdxl(2,cnode2,j,1)=getg%drdxl(2,cnode2,j,1)+voly2*factor3
              getg%drdxl(2,cnode2,j,1)=getg%drdxl(2,cnode2,j,1)+voly2*factor4

              getg%drdxl(3,cnode2,j,1)=getg%drdxl(3,cnode2,j,1)+volz2*factor2
              getg%drdxl(3,cnode2,j,1)=getg%drdxl(3,cnode2,j,1)+volz2*factor3
              getg%drdxl(3,cnode2,j,1)=getg%drdxl(3,cnode2,j,1)+volz2*factor4

              getg%drdxl(1,cnode3,j,1)=getg%drdxl(1,cnode3,j,1)+volx3*factor2
              getg%drdxl(1,cnode3,j,1)=getg%drdxl(1,cnode3,j,1)+volx3*factor3
              getg%drdxl(1,cnode3,j,1)=getg%drdxl(1,cnode3,j,1)+volx3*factor4

              getg%drdxl(2,cnode3,j,1)=getg%drdxl(2,cnode3,j,1)+voly3*factor2
              getg%drdxl(2,cnode3,j,1)=getg%drdxl(2,cnode3,j,1)+voly3*factor3
              getg%drdxl(2,cnode3,j,1)=getg%drdxl(2,cnode3,j,1)+voly3*factor4

              getg%drdxl(3,cnode3,j,1)=getg%drdxl(3,cnode3,j,1)+volz3*factor2
              getg%drdxl(3,cnode3,j,1)=getg%drdxl(3,cnode3,j,1)+volz3*factor3
              getg%drdxl(3,cnode3,j,1)=getg%drdxl(3,cnode3,j,1)+volz3*factor4

              getg%drdxl(1,cnode4,j,1)=getg%drdxl(1,cnode4,j,1)+volx4*factor2
              getg%drdxl(1,cnode4,j,1)=getg%drdxl(1,cnode4,j,1)+volx4*factor3
              getg%drdxl(1,cnode4,j,1)=getg%drdxl(1,cnode4,j,1)+volx4*factor4

              getg%drdxl(2,cnode4,j,1)=getg%drdxl(2,cnode4,j,1)+voly4*factor2
              getg%drdxl(2,cnode4,j,1)=getg%drdxl(2,cnode4,j,1)+voly4*factor3
              getg%drdxl(2,cnode4,j,1)=getg%drdxl(2,cnode4,j,1)+voly4*factor4

              getg%drdxl(3,cnode4,j,1)=getg%drdxl(3,cnode4,j,1)+volz4*factor2
              getg%drdxl(3,cnode4,j,1)=getg%drdxl(3,cnode4,j,1)+volz4*factor3
              getg%drdxl(3,cnode4,j,1)=getg%drdxl(3,cnode4,j,1)+volz4*factor4

            end do
          endif node1_localb

        end do quad_node_loop

      end do quad_face_loop

    end do bound_loop

  end subroutine dcoriolisb


!================================= DRDNONINRATES =============================80
!
!  Derivatives wrt noninertial rotation rates
!
!=============================================================================80
  subroutine drdnoninrates(grid,soln,sadj,design,drdxlg)

    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,     only : sadj_type
    use design_types,     only : design_type
    use dshape_inviscid,  only : dwhat_drates, drdratesinv, edge_stencil,      &
                                 bc_drdratesinv, bc_drdratesinvi
    use dshape_laminar,   only : bc_viscous_wall_rates
    use kinddefs,         only : dp
    use solution_types,   only : compressible, incompressible
    use lmpi,             only : lmpi_die, lmpi_reduce, lmpi_bcast, lmpi_master
    use lmpi_app,         only : lmpi_xfer
    use reconstruction,   only : lstgs
    use thermo,           only : etop, ptoe
    use info_depr,        only : ivisc
    use dshape_turbulent, only : turbresrates

    real(dp), dimension(:,:), intent(out) :: drdxlg

    type(grid_type),   intent(in)    :: grid
    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(in)    :: design
    type(soln_type),   intent(inout) :: soln

    integer :: i, ib

    real(dp), dimension(3,design%nfunctions) :: drdxl

  continue

    drdxl(:,:) = 0.0_dp

    if ( soln%eqn_set == compressible ) then
      call etop( size(soln%q_dof,2),soln%q_dof,soln%n_tot, soln%eqn_set )
    endif

    call lstgs(soln%viscous_method,grid%nnodes0,grid%nnodes01,grid%nedgeloc,   &
               grid%eptr,grid%symmetry,soln%q_dof,soln%gradx,soln%grady,       &
               soln%gradz,grid%x,grid%y,grid%z,grid%r11,grid%r12,grid%r13,     &
               grid%r22,grid%r23,grid%r33,soln%n_tot,soln%n_grd,soln%turb,     &
               soln%n_turb,soln%eqn_set,soln%ndim)

    call lmpi_xfer(soln%gradx)
    call lmpi_xfer(soln%grady)
    call lmpi_xfer(soln%gradz)

! Linearize facespeed wrt noninertial rates

    call dwhat_drates(grid%nedgeloc,grid%nelem,grid%elem,grid%x,grid%y,grid%z, &
                      grid%eptr,grid%ra)

! Linearize inviscid flux wrt noninertial rates

    call drdratesinv(grid%nnodes0,grid%nedgeloc,grid%x,grid%y,grid%z,          &
                     grid%eptr,sadj%coltag,sadj%rlam,soln%q_dof,soln%gradx,    &
                     soln%grady,soln%gradz,grid%xn,grid%yn,grid%zn,grid%ra,    &
                     design%nfunctions,soln%phi,soln%ndim,soln%adim,           &
                     grid%facespeed,grid%nnodes01,drdxl,soln%eqn_set)

! Linearize inviscid boundary fluxes wrt noninertial rates

    do ib = 1, grid%nbound
      select case(soln%eqn_set)
      case (compressible)
        call bc_drdratesinv(grid%bc(ib)%nbnode,grid%bc(ib)%nbfacet,            &
                            grid%bc(ib)%face_bit,grid%bc(ib)%face_bitq,        &
                            grid%bc(ib)%ibnode,grid%bc(ib)%f2ntb,              &
                            grid%bc(ib)%f2nqb,grid%x,grid%y,grid%z,soln%q_dof, &
                            sadj%coltag,sadj%rlam,design%nfunctions,           &
                            grid%bc(ib)%ibc,soln%ndim,soln%adim,grid%nnodes01, &
                            grid%dxdt,grid%dydt,grid%dzdt,grid%bc(ib)%bxn,     &
                            grid%bc(ib)%byn,grid%bc(ib)%bzn,                   &
                            grid%bc(ib)%bfacespeed,grid%nnodes0,               &
                            drdxl,grid%bc(ib)%nbfaceq,grid%nelem,grid%elem)
      case (incompressible)
        call bc_drdratesinvi(grid%bc(ib)%nbnode,grid%bc(ib)%nbfacet,           &
                             grid%bc(ib)%ibnode,grid%bc(ib)%f2ntb,             &
                             grid%bc(ib)%f2nqb,grid%x,grid%y,grid%z,soln%q_dof,&
                             sadj%coltag,sadj%rlam,design%nfunctions,          &
                             grid%bc(ib)%ibc,soln%ndim,soln%adim,grid%nnodes01,&
                             grid%bc(ib)%bxn,grid%bc(ib)%byn,grid%bc(ib)%bzn,  &
                             grid%bc(ib)%bfacespeed,grid%nnodes0,              &
                             drdxl,grid%bc(ib)%nbfaceq)
      case default
        write(*,*) 'sensshape cannot handle eqn_set /= 0,1'
        call lmpi_die
      end select
    end do

    viscous_wall_bc : if ( ivisc > 0 ) then
      call bc_viscous_wall_rates(grid%nnodes0,grid%nbound,design%nfunctions,   &
                                 grid%bc,sadj%rlam,soln%adim,grid%nnodes01,    &
                                 drdxl,soln%q_dof,soln%ndim,grid%x,grid%y,     &
                                 grid%z,grid%symmetry,soln%eqn_set)

    endif viscous_wall_bc

    turbulent : if ( ivisc == 6 ) then
      call turbresrates(soln%eqn_set,grid%nedge,grid%eptr,soln%turb,soln%q_dof,&
                        grid%x,grid%y,grid%z,grid%xn,grid%yn,grid%zn,grid%ra,  &
                        sadj%coltag,sadj%rlam,grid%nnodes0,grid%nnodes01,      &
                        grid%nedgeLoc,grid%nbound,grid%bc,drdxl,               &
                        design%nfunctions,soln%ndim,soln%adim,soln%n_turb,     &
                        grid%facespeed,edge_stencil,grid%dxdt,grid%dydt,       &
                        grid%dzdt,soln%n_q)
    endif turbulent

    do i = 1, grid%nedgeloc
      deallocate(edge_stencil(i)%dwhatdx, edge_stencil(i)%dwhatdy)
      deallocate(edge_stencil(i)%dwhatdz)
    end do
    deallocate(edge_stencil)

! Also need derivatives of Coriolis terms

    call dcoriolis_rates(soln%eqn_set,grid%vol,grid%nnodes0,grid%nnodes01,     &
                         soln%q_dof,soln%n_tot,design%nfunctions,sadj%rlam,    &
                         sadj%coltag,drdxl)

    if ( soln%eqn_set == compressible ) then
      call ptoe( size(soln%q_dof,2),soln%q_dof,soln%n_tot, soln%eqn_set )
    endif

    call lmpi_reduce(drdxl, drdxlg)
    call lmpi_bcast(drdxlg)

    if ( lmpi_master ) then
      do i = 1, design%nfunctions
        write(*,'(a,i0,a,e25.15)') 'Derivative of function ', i,               &
                                   ' wrt xrotrate_ni = ', drdxlg(1,i)
        write(*,'(a,i0,a,e25.15)') 'Derivative of function ', i,               &
                                   ' wrt yrotrate_ni = ', drdxlg(2,i)
        write(*,'(a,i0,a,e25.15)') 'Derivative of function ', i,               &
                                   ' wrt zrotrate_ni = ', drdxlg(3,i)
      end do
    endif

  end subroutine drdnoninrates


!================================= DCORIOLIS_RATES ===========================80
!
! Linearizes coriolis terms wrt noninertial rates
!
!=============================================================================80
  subroutine dcoriolis_rates(eqn_set,vol,nnodes0,nnodes01,qnode,n_tot,         &
                             nfunctions,rlam,coltag,drdxl)

    use solution_types, only : compressible, incompressible
    use kinddefs,       only : dp

    integer, intent(in) :: eqn_set, nfunctions
    integer, intent(in) :: nnodes0, nnodes01, n_tot

    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(nnodes01),       intent(in)    :: vol
    real(dp), dimension(:,:),            intent(in)    :: coltag
    real(dp), dimension(:,:,:),          intent(in)    :: rlam
    real(dp), dimension(3,nfunctions),   intent(inout) :: drdxl

    integer :: n, ifcn

    real(dp) :: u,v,w,rho,rlam2,rlam3,rlam4
    real(dp) :: res2x,res2y,res2z,res3x,res3y,res3z,res4x,res4y,res4z

  continue

    select case (eqn_set)

      case (compressible)

        do n = 1, nnodes0

          rho = qnode(1,n)
          u   = qnode(2,n)
          v   = qnode(3,n)
          w   = qnode(4,n)

!         res2 = rho*vol(n)*(yrotrate_ni*w - zrotrate_ni*v)
            res2x =  0.0_dp
            res2y =  rho*vol(n)*w
            res2z = -rho*vol(n)*v

!         res3 = rho*vol(n)*(zrotrate_ni*u - xrotrate_ni*w)
            res3x = -rho*vol(n)*w
            res3y =  0.0_dp
            res3z =  rho*vol(n)*u

!         res4 = rho*vol(n)*(xrotrate_ni*v - yrotrate_ni*u)
            res4x =  rho*vol(n)*v
            res4y = -rho*vol(n)*u
            res4z =  0.0_dp

          do ifcn = 1, nfunctions
            rlam2 = coltag(2,n)*rlam(2,n,ifcn)
            rlam3 = coltag(3,n)*rlam(3,n,ifcn)
            rlam4 = coltag(4,n)*rlam(4,n,ifcn)
            drdxl(1,ifcn)=drdxl(1,ifcn)+ res2x*rlam2 + res3x*rlam3 + res4x*rlam4
            drdxl(2,ifcn)=drdxl(2,ifcn)+ res2y*rlam2 + res3y*rlam3 + res4y*rlam4
            drdxl(3,ifcn)=drdxl(3,ifcn)+ res2z*rlam2 + res3z*rlam3 + res4z*rlam4
          end do

        end do

      case (incompressible)

        do n = 1, nnodes0

          u = qnode(2,n)
          v = qnode(3,n)
          w = qnode(4,n)

!         res2 = vol(n)*(yrotrate_ni*w - zrotrate_ni*v)
            res2x =  0.0_dp
            res2y =  vol(n)*w
            res2z = -vol(n)*v

!         res3 = vol(n)*(zrotrate_ni*u - xrotrate_ni*w)
            res3x = -vol(n)*w
            res3y =  0.0_dp
            res3z =  vol(n)*u

!         res4 = vol(n)*(xrotrate_ni*v - yrotrate_ni*u)
            res4x =  vol(n)*v
            res4y = -vol(n)*u
            res4z =  0.0_dp

          do ifcn = 1, nfunctions
            rlam2 = coltag(2,n)*rlam(2,n,ifcn)
            rlam3 = coltag(3,n)*rlam(3,n,ifcn)
            rlam4 = coltag(4,n)*rlam(4,n,ifcn)
            drdxl(1,ifcn)=drdxl(1,ifcn)+ res2x*rlam2 + res3x*rlam3 + res4x*rlam4
            drdxl(2,ifcn)=drdxl(2,ifcn)+ res2y*rlam2 + res3y*rlam3 + res4y*rlam4
            drdxl(3,ifcn)=drdxl(3,ifcn)+ res2z*rlam2 + res3z*rlam3 + res4z*rlam4
          end do

        end do

      case default

      end select

  end subroutine dcoriolis_rates

end module dshape_residual

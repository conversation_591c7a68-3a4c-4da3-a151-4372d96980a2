!================================== DFLROE_I =================================80
!
! Roe incompressible flux jacobians (+)
!
! All derivatives worked out in detail
!
! Newer version that handles moving grid and noninertial rotating reference
! frame cases as well as stationary grid/inertial frame cases.
!
! Reference: Neel, R. E., Godfrey, A. G., and McGrory, W. D.:"Low-Speed,
!            Time-Accurate Validation of GASP Version 4"; AIAA 2005-686
!            43rd AIAA Aerospace Sciences Meeting, Jan. 2005.
!
!=============================================================================80

  pure function dflroe_i(xnorm, ynorm, znorm, area, face_speed, beta, ql, qr)

    use kinddefs, only : dp

    real(dp),                 intent(in)  :: xnorm, ynorm, znorm, area
    real(dp),                 intent(in)  :: face_speed, beta
    real(dp), dimension(4),   intent(in)  :: ql, qr

    real(dp), dimension(4,4)              :: dflroe_i

    real(dp) :: fspd_half,fspd2_4th

    real(dp) :: flux1ul,flux1vl,flux1wl,flux1pl
    real(dp) :: flux2ul,flux2vl,flux2wl,flux2pl
    real(dp) :: flux3ul,flux3vl,flux3wl,flux3pl
    real(dp) :: flux4ul,flux4vl,flux4wl,flux4pl

    real(dp) :: fluxm1ul,fluxm1vl,fluxm1wl,fluxm1pl
    real(dp) :: fluxm2ul,fluxm2vl,fluxm2wl,fluxm2pl
    real(dp) :: fluxm3ul,fluxm3vl,fluxm3wl,fluxm3pl
    real(dp) :: fluxm4ul,fluxm4vl,fluxm4wl,fluxm4pl

    real(dp) :: fluxp1ul,fluxp1vl,fluxp1wl,fluxp1pl
    real(dp) :: fluxp2ul,fluxp2vl,fluxp2wl,fluxp2pl
    real(dp) :: fluxp3ul,fluxp3vl,fluxp3wl,fluxp3pl
    real(dp) :: fluxp4ul,fluxp4vl,fluxp4wl,fluxp4pl

    real(dp) :: termpl, termul, termvl, termwl

    real(dp) :: term2
    real(dp) :: term2pl, term2ul, term2vl, term2wl

    real(dp) :: term3
    real(dp) :: term3pl, term3ul, term3vl, term3wl

    real(dp) :: term4
    real(dp) :: term4pl, term4ul, term4vl, term4wl

    real(dp) :: phi_m
    real(dp) :: phi_mpl, phi_mul, phi_mvl, phi_mwl

    real(dp) :: phi_p
    real(dp) :: phi_ppl, phi_pul, phi_pvl, phi_pwl

    real(dp) :: f11pl, f11ul, f11vl, f11wl
    real(dp) :: f12pl, f12ul, f12vl, f12wl
    real(dp) :: f13pl, f13ul, f13vl, f13wl
    real(dp) :: f14pl, f14ul, f14vl, f14wl

    real(dp) :: f21pl, f21ul, f21vl, f21wl
    real(dp) :: f22pl, f22ul, f22vl, f22wl
    real(dp) :: f23pl, f23ul, f23vl, f23wl
    real(dp) :: f24pl, f24ul, f24vl, f24wl

    real(dp) :: f31pl, f31ul, f31vl, f31wl
    real(dp) :: f32pl, f32ul, f32vl, f32wl
    real(dp) :: f33pl, f33ul, f33vl, f33wl
    real(dp) :: f34pl, f34ul, f34vl, f34wl

    real(dp) :: f41pl, f41ul, f41vl, f41wl
    real(dp) :: f42pl, f42ul, f42vl, f42wl
    real(dp) :: f43pl, f43ul, f43vl, f43wl
    real(dp) :: f44pl, f44ul, f44vl, f44wl

    real(dp) :: t1ul,t1vl,t1wl,t1pl
    real(dp) :: t2ul,t2vl,t2wl,t2pl
    real(dp) :: t3ul,t3vl,t3wl,t3pl
    real(dp) :: t4ul,t4vl,t4wl,t4pl

    real(dp) :: c2
    real(dp) :: c2ul,c2vl,c2wl,c2pl

    real(dp) :: du
    real(dp) :: duul,duvl,duwl,dupl

    real(dp) :: dv
    real(dp) :: dvul,dvvl,dvwl,dvpl

    real(dp) :: dw
    real(dp) :: dwul,dwvl,dwwl,dwpl

    real(dp) :: eig1
    real(dp) :: eig1ul,eig1vl,eig1wl,eig1pl

    real(dp) :: eig2
    real(dp) :: eig2ul,eig2vl,eig2wl,eig2pl

    real(dp) :: eig3
    real(dp) :: eig3ul,eig3vl,eig3wl,eig3pl

    real(dp) :: eig4
    real(dp) :: eig4ul,eig4vl,eig4wl,eig4pl

    real(dp) :: ubar
    real(dp) :: ubarul,ubarvl,ubarwl,ubarpl

    real(dp) :: dubar
    real(dp) :: dubarul,dubarvl,dubarwl,dubarpl

    real(dp) :: c
    real(dp) :: cul,cvl,cwl,cpl

    real(dp) :: u
    real(dp) :: uul,uvl,uwl,upl

    real(dp) :: v
    real(dp) :: vul,vvl,vwl,vpl

    real(dp) :: w
    real(dp) :: wul,wvl,wwl,wpl

    real(dp) :: ubarr
    real(dp) :: ubarrul,ubarrvl,ubarrwl,ubarrpl

    real(dp) :: ur
    real(dp) :: urul,urvl,urwl,urpl

    real(dp) :: vr
    real(dp) :: vrul,vrvl,vrwl,vrpl

    real(dp) :: wr
    real(dp) :: wrul,wrvl,wrwl,wrpl

    real(dp) :: ubarl
    real(dp) :: ubarlul,ubarlvl,ubarlwl,ubarlpl

    real(dp) :: ul
    real(dp) :: ulul,ulvl,ulwl,ulpl

    real(dp) :: vl
    real(dp) :: vlul,vlvl,vlwl,vlpl

    real(dp) :: wl
    real(dp) :: wlul,wlvl,wlwl,wlpl

    real(dp) :: dpress
    real(dp) :: dpressul,dpressvl,dpresswl,dpresspl

    real(dp) :: pl
    real(dp) :: plul,plvl,plwl,plpl

    real(dp) :: pr
    real(dp) :: prul,prvl,prwl,prpl

    real(dp) :: term, half_area

    real(dp), parameter    :: my_0   = 0.0_dp
    real(dp), parameter    :: my_haf = 0.5_dp
    real(dp), parameter    :: my_4th = 0.25_dp

  continue

      fspd_half = my_haf*face_speed
      fspd2_4th = my_4th*face_speed*face_speed

      half_area = my_haf*area

! Get variables on "left" side of face

      pl     = ql(1)

        plpl = 1.0_dp
        plul = 0.0_dp
        plvl = 0.0_dp
        plwl = 0.0_dp

      ul     = ql(2)

        ulpl = 0.0_dp
        ulul = 1.0_dp
        ulvl = 0.0_dp
        ulwl = 0.0_dp

      vl     = ql(3)

        vlpl = 0.0_dp
        vlul = 0.0_dp
        vlvl = 1.0_dp
        vlwl = 0.0_dp

      wl     = ql(4)

        wlpl = 0.0_dp
        wlul = 0.0_dp
        wlvl = 0.0_dp
        wlwl = 1.0_dp

      ubarl  = xnorm*ul + ynorm*vl + znorm*wl

      ubarlpl = xnorm*ulpl + ynorm*vlpl + znorm*wlpl
      ubarlul = xnorm*ulul + ynorm*vlul + znorm*wlul
      ubarlvl = xnorm*ulvl + ynorm*vlvl + znorm*wlvl
      ubarlwl = xnorm*ulwl + ynorm*vlwl + znorm*wlwl

! Get variables on "right" side of face

      pr     = qr(1)

        prpl = 0.0_dp
        prul = 0.0_dp
        prvl = 0.0_dp
        prwl = 0.0_dp

      ur     = qr(2)

        urpl = 0.0_dp
        urul = 0.0_dp
        urvl = 0.0_dp
        urwl = 0.0_dp

      vr     = qr(3)

        vrpl = 0.0_dp
        vrul = 0.0_dp
        vrvl = 0.0_dp
        vrwl = 0.0_dp

      wr     = qr(4)

        wrpl = 0.0_dp
        wrul = 0.0_dp
        wrvl = 0.0_dp
        wrwl = 0.0_dp

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr

      ubarrpl = 0.0_dp
      ubarrul = 0.0_dp
      ubarrvl = 0.0_dp
      ubarrwl = 0.0_dp

!     Compute averages

      u     = 0.5_dp*(ul + ur)

        upl = 0.0_dp
        uul = 0.5_dp
        uvl = 0.0_dp
        uwl = 0.0_dp

      v     = 0.5_dp*(vl + vr)

        vpl = 0.0_dp
        vul = 0.0_dp
        vvl = 0.5_dp
        vwl = 0.0_dp

      w     = 0.5_dp*(wl + wr)

        wpl = 0.0_dp
        wul = 0.0_dp
        wvl = 0.0_dp
        wwl = 0.5_dp

      ubar  = 0.5_dp*(ubarl + ubarr)

        ubarpl = 0.0_dp
        ubarul = 0.5_dp*ubarlul
        ubarvl = 0.5_dp*ubarlvl
        ubarwl = 0.5_dp*ubarlwl

      c2     = (ubar-fspd_half)*(ubar-fspd_half) + beta

        term = 2._dp*(ubar-fspd_half)

        c2pl = term*ubarpl
        c2ul = term*ubarul
        c2vl = term*ubarvl
        c2wl = term*ubarwl

      c      = sqrt(c2)

        term = 0.5_dp/sqrt(c2)

        cpl  = term*c2pl
        cul  = term*c2ul
        cvl  = term*c2vl
        cwl  = term*c2wl

      phi_p  = c/(c + fspd_half)

        term    = fspd_half/(c + fspd_half)/(c + fspd_half)

        phi_ppl = term*cpl
        phi_pul = term*cul
        phi_pvl = term*cvl
        phi_pwl = term*cwl

      phi_m  = c/(c - fspd_half)

        term    = -fspd_half/(c - fspd_half)/(c - fspd_half)

        phi_mpl = term*cpl
        phi_mul = term*cul
        phi_mvl = term*cvl
        phi_mwl = term*cwl

! Now compute eigenvalues, eigenvectors, and strengths

      eig1 = ubar

        eig1pl = ubarpl
        eig1ul = ubarul
        eig1vl = ubarvl
        eig1wl = ubarwl

      if (eig1 < 0.0_dp) then
        eig1pl = -eig1pl
        eig1ul = -eig1ul
        eig1vl = -eig1vl
        eig1wl = -eig1wl
      end if

      eig1 = abs(eig1)

      eig2 = ubar

        eig2pl = ubarpl
        eig2ul = ubarul
        eig2vl = ubarvl
        eig2wl = ubarwl

      if (eig2 < 0.0_dp) then
        eig2pl = -eig2pl
        eig2ul = -eig2ul
        eig2vl = -eig2vl
        eig2wl = -eig2wl
      end if

      eig2 = abs(eig2)

      eig3 = ubar - fspd_half - c

        eig3ul = ubarul - cul
        eig3vl = ubarvl - cvl
        eig3wl = ubarwl - cwl
        eig3pl = ubarpl - cpl

      if (eig3 < 0.0_dp) then
        eig3pl = -eig3pl
        eig3ul = -eig3ul
        eig3vl = -eig3vl
        eig3wl = -eig3wl
      end if

      eig3 = abs(eig3)

      eig4 = ubar - fspd_half + c

        eig4ul = ubarul + cul
        eig4vl = ubarvl + cvl
        eig4wl = ubarwl + cwl
        eig4pl = ubarpl + cpl

      if (eig4 < 0._dp) then
        eig4pl = -eig4pl
        eig4ul = -eig4ul
        eig4vl = -eig4vl
        eig4wl = -eig4wl
      end if

      eig4 = abs(eig4)

! Jumps

      dpress = pr - pl

      dpresspl = prpl - plpl
      dpressul = prul - plul
      dpressvl = prvl - plvl
      dpresswl = prwl - plwl

      du     = ur - ul

      dupl = urpl - ulpl
      duul = urul - ulul
      duvl = urvl - ulvl
      duwl = urwl - ulwl

      dv     = vr - vl

      dvpl = vrpl - vlpl
      dvul = vrul - vlul
      dvvl = vrvl - vlvl
      dvwl = vrwl - vlwl

      dw     = wr - wl

      dwpl = wrpl - wlpl
      dwul = wrul - wlul
      dwvl = wrvl - wlvl
      dwwl = wrwl - wlwl

      dubar = ubarr - ubarl

      dubarpl = ubarrpl - ubarlpl
      dubarul = ubarrul - ubarlul
      dubarvl = ubarrvl - ubarlvl
      dubarwl = ubarrwl - ubarlwl

!     term1 =  eig1
!     f11   =  my_0
!     f12   =  term1*(du - xnorm*dubar)
!     f13   =  term1*(dv - ynorm*dubar)
!     f14   =  term1*(dw - znorm*dubar)

      f11pl =  my_0
      f11ul =  my_0
      f11vl =  my_0
      f11wl =  my_0

      f12pl =  eig1pl*(du - xnorm*dubar) + eig1*(dupl - xnorm*dubarpl)
      f12ul =  eig1ul*(du - xnorm*dubar) + eig1*(duul - xnorm*dubarul)
      f12vl =  eig1vl*(du - xnorm*dubar) + eig1*(duvl - xnorm*dubarvl)
      f12wl =  eig1wl*(du - xnorm*dubar) + eig1*(duwl - xnorm*dubarwl)

      f13pl =  eig1pl*(dv - ynorm*dubar) + eig1*(dvpl - ynorm*dubarpl)
      f13ul =  eig1ul*(dv - ynorm*dubar) + eig1*(dvul - ynorm*dubarul)
      f13vl =  eig1vl*(dv - ynorm*dubar) + eig1*(dvvl - ynorm*dubarvl)
      f13wl =  eig1wl*(dv - ynorm*dubar) + eig1*(dvwl - ynorm*dubarwl)

      f14pl =  eig1pl*(dw - znorm*dubar) + eig1*(dwpl - znorm*dubarpl)
      f14ul =  eig1ul*(dw - znorm*dubar) + eig1*(dwul - znorm*dubarul)
      f14vl =  eig1vl*(dw - znorm*dubar) + eig1*(dwvl - znorm*dubarvl)
      f14wl =  eig1wl*(dw - znorm*dubar) + eig1*(dwwl - znorm*dubarwl)

      term2 = -eig2*((ubar - face_speed)*dubar + dpress)/(c2 + fspd2_4th)
!     f21   =  my_0
!     f22   =  term2*(u - xnorm*ubar)
!     f23   =  term2*(v - ynorm*ubar)
!     f24   =  term2*(w - znorm*ubar)

      term2pl =                                                                &
      ( -eig2pl*((ubar - face_speed)*dubar   + dpress)                         &
        -eig2*  ((ubar - face_speed)*dubarpl + dpresspl + dubar*ubarpl)        &
        +eig2*  ((ubar - face_speed)*dubar   + dpress)/(c2 + fspd2_4th)*c2pl ) &
                                                             / (c2 + fspd2_4th)
      term2ul =                                                                &
      ( -eig2ul*((ubar - face_speed)*dubar   + dpress)                         &
        -eig2*  ((ubar - face_speed)*dubarul + dpressul + dubar*ubarul)        &
        +eig2*  ((ubar - face_speed)*dubar   + dpress)/(c2 + fspd2_4th)*c2ul ) &
                                                             / (c2 + fspd2_4th)
      term2vl =                                                                &
      ( -eig2vl*((ubar - face_speed)*dubar   + dpress)                         &
        -eig2*  ((ubar - face_speed)*dubarvl + dpressvl + dubar*ubarvl)        &
        +eig2*  ((ubar - face_speed)*dubar   + dpress)/(c2 + fspd2_4th)*c2vl ) &
                                                             / (c2 + fspd2_4th)
      term2wl =                                                                &
      ( -eig2wl*((ubar - face_speed)*dubar   + dpress)                         &
        -eig2*  ((ubar - face_speed)*dubarwl + dpresswl + dubar*ubarwl)        &
        +eig2*  ((ubar - face_speed)*dubar   + dpress)/(c2 + fspd2_4th)*c2wl ) &
                                                             / (c2 + fspd2_4th)

      f21pl =  my_0
      f21ul =  my_0
      f21vl =  my_0
      f21wl =  my_0

      f22pl =  term2pl*(u - xnorm*ubar) + term2*(upl - xnorm*ubarpl)
      f22ul =  term2ul*(u - xnorm*ubar) + term2*(uul - xnorm*ubarul)
      f22vl =  term2vl*(u - xnorm*ubar) + term2*(uvl - xnorm*ubarvl)
      f22wl =  term2wl*(u - xnorm*ubar) + term2*(uwl - xnorm*ubarwl)

      f23pl =  term2pl*(v - ynorm*ubar) + term2*(vpl - ynorm*ubarpl)
      f23ul =  term2ul*(v - ynorm*ubar) + term2*(vul - ynorm*ubarul)
      f23vl =  term2vl*(v - ynorm*ubar) + term2*(vvl - ynorm*ubarvl)
      f23wl =  term2wl*(v - ynorm*ubar) + term2*(vwl - ynorm*ubarwl)

      f24pl =  term2pl*(w - znorm*ubar) + term2*(wpl - znorm*ubarpl)
      f24ul =  term2ul*(w - znorm*ubar) + term2*(wul - znorm*ubarul)
      f24vl =  term2vl*(w - znorm*ubar) + term2*(wvl - znorm*ubarvl)
      f24wl =  term2wl*(w - znorm*ubar) + term2*(wwl - znorm*ubarwl)

      term3 =  eig3*my_haf*((ubar - c - fspd_half)*dubar + dpress)/c2
      term  =  ubar+c-fspd_half
!     f31   =  term3*c*term
!     f32   =  term3*phi_m*(u - xnorm*term)
!     f33   =  term3*phi_m*(v - ynorm*term)
!     f34   =  term3*phi_m*(w - znorm*term)

      term3pl =                                                            &
      ( eig3pl*my_haf*((ubar-c-fspd_half)*dubar   + dpress)                &
       +eig3  *my_haf*((ubar-c-fspd_half)*dubarpl + dpresspl               &
                       + (ubarpl-cpl)*dubar)                               &
       -eig3  *my_haf*((ubar-c-fspd_half)*dubar   + dpress)/c2*c2pl )/c2
      term3ul =                                                            &
      ( eig3ul*my_haf*((ubar-c-fspd_half)*dubar   + dpress)                &
       +eig3  *my_haf*((ubar-c-fspd_half)*dubarul + dpressul               &
                      + (ubarul-cul)*dubar)                                &
       -eig3  *my_haf*((ubar-c-fspd_half)*dubar   + dpress)/c2*c2ul )/c2
      term3vl =                                                            &
      ( eig3vl*my_haf*((ubar-c-fspd_half)*dubar   + dpress)                &
       +eig3  *my_haf*((ubar-c-fspd_half)*dubarvl + dpressvl               &
                       + (ubarvl-cvl)*dubar)                               &
       -eig3  *my_haf*((ubar-c-fspd_half)*dubar   + dpress)/c2*c2vl )/c2
      term3wl =                                                            &
      ( eig3wl*my_haf*((ubar-c-fspd_half)*dubar   + dpress)                &
       +eig3  *my_haf*((ubar-c-fspd_half)*dubarwl + dpresswl               &
                       + (ubarwl-cwl)*dubar)                               &
       -eig3  *my_haf*((ubar-c-fspd_half)*dubar   + dpress)/c2*c2wl )/c2

      termpl  =  ubarpl+cpl
      termul  =  ubarul+cul
      termvl  =  ubarvl+cvl
      termwl  =  ubarwl+cwl

      f31pl =  term3pl*c*term + term3*cpl*term + term3*c*termpl
      f31ul =  term3ul*c*term + term3*cul*term + term3*c*termul
      f31vl =  term3vl*c*term + term3*cvl*term + term3*c*termvl
      f31wl =  term3wl*c*term + term3*cwl*term + term3*c*termwl

      f32pl =  term3pl*phi_m*(u - xnorm*term) + term3*phi_mpl*(u - xnorm*term) &
            +  term3*phi_m*(upl - xnorm*termpl)
      f32ul =  term3ul*phi_m*(u - xnorm*term) + term3*phi_mul*(u - xnorm*term) &
            +  term3*phi_m*(uul - xnorm*termul)
      f32vl =  term3vl*phi_m*(u - xnorm*term) + term3*phi_mvl*(u - xnorm*term) &
            +  term3*phi_m*(uvl - xnorm*termvl)
      f32wl =  term3wl*phi_m*(u - xnorm*term) + term3*phi_mwl*(u - xnorm*term) &
            +  term3*phi_m*(uwl - xnorm*termwl)

      f33pl =  term3pl*phi_m*(v - ynorm*term) + term3*phi_mpl*(v - ynorm*term) &
            +  term3*phi_m*(vpl - ynorm*termpl)
      f33ul =  term3ul*phi_m*(v - ynorm*term) + term3*phi_mul*(v - ynorm*term) &
            +  term3*phi_m*(vul - ynorm*termul)
      f33vl =  term3vl*phi_m*(v - ynorm*term) + term3*phi_mvl*(v - ynorm*term) &
            +  term3*phi_m*(vvl - ynorm*termvl)
      f33wl =  term3wl*phi_m*(v - ynorm*term) + term3*phi_mwl*(v - ynorm*term) &
            +  term3*phi_m*(vwl - ynorm*termwl)

      f34pl =  term3pl*phi_m*(w - znorm*term) + term3*phi_mpl*(w - znorm*term) &
            +  term3*phi_m*(wpl - znorm*termpl)
      f34ul =  term3ul*phi_m*(w - znorm*term) + term3*phi_mul*(w - znorm*term) &
            +  term3*phi_m*(wul - znorm*termul)
      f34vl =  term3vl*phi_m*(w - znorm*term) + term3*phi_mvl*(w - znorm*term) &
            +  term3*phi_m*(wvl - znorm*termvl)
      f34wl =  term3wl*phi_m*(w - znorm*term) + term3*phi_mwl*(w - znorm*term) &
            +  term3*phi_m*(wwl - znorm*termwl)

      term4 =  eig4*my_haf*((ubar + c - fspd_half)*dubar + dpress)/c2
      term  =  ubar-c-fspd_half
!     f41   = -term4*c*term
!     f42   =  term4*phi_p*(u - xnorm*term)
!     f43   =  term4*phi_p*(v - ynorm*term)
!     f44   =  term4*phi_p*(w - znorm*term)

      term4pl =                                                                &
      ( eig4pl*my_haf*((ubar+c-fspd_half)*dubar   + dpress)                    &
       +eig4  *my_haf*((ubar+c-fspd_half)*dubarpl + dpresspl                   &
                      + (ubarpl+cpl)*dubar)                                    &
       -eig4  *my_haf*((ubar+c-fspd_half)*dubar   + dpress)/c2*c2pl )/c2
      term4ul =                                                                &
      ( eig4ul*my_haf*((ubar+c-fspd_half)*dubar   + dpress)                    &
       +eig4  *my_haf*((ubar+c-fspd_half)*dubarul + dpressul                   &
                      + (ubarul+cul)*dubar)                                    &
       -eig4  *my_haf*((ubar+c-fspd_half)*dubar   + dpress)/c2*c2ul )/c2
      term4vl =                                                                &
      ( eig4vl*my_haf*((ubar+c-fspd_half)*dubar   + dpress)                    &
       +eig4  *my_haf*((ubar+c-fspd_half)*dubarvl + dpressvl                   &
                      + (ubarvl+cvl)*dubar)                                    &
       -eig4  *my_haf*((ubar+c-fspd_half)*dubar   + dpress)/c2*c2vl )/c2
      term4wl =                                                                &
      ( eig4wl*my_haf*((ubar+c-fspd_half)*dubar   + dpress)                    &
       +eig4  *my_haf*((ubar+c-fspd_half)*dubarwl + dpresswl                   &
                      + (ubarwl+cwl)*dubar)                                    &
       -eig4  *my_haf*((ubar+c-fspd_half)*dubar   + dpress)/c2*c2wl )/c2

      termpl  = ubarpl-cpl
      termul  = ubarul-cul
      termvl  = ubarvl-cvl
      termwl  = ubarwl-cwl

      f41pl = -term4pl*c*term - term4*cpl*term - term4*c*termpl
      f41ul = -term4ul*c*term - term4*cul*term - term4*c*termul
      f41vl = -term4vl*c*term - term4*cvl*term - term4*c*termvl
      f41wl = -term4wl*c*term - term4*cwl*term - term4*c*termwl

      f42pl =  term4pl*phi_p*(u - xnorm*term) + term4*phi_ppl*(u - xnorm*term) &
            +  term4*phi_p*(upl - xnorm*termpl)
      f42ul =  term4ul*phi_p*(u - xnorm*term) + term4*phi_pul*(u - xnorm*term) &
            +  term4*phi_p*(uul - xnorm*termul)
      f42vl =  term4vl*phi_p*(u - xnorm*term) + term4*phi_pvl*(u - xnorm*term) &
            +  term4*phi_p*(uvl - xnorm*termvl)
      f42wl =  term4wl*phi_p*(u - xnorm*term) + term4*phi_pwl*(u - xnorm*term) &
            +  term4*phi_p*(uwl - xnorm*termwl)

      f43pl =  term4pl*phi_p*(v - ynorm*term) + term4*phi_ppl*(v - ynorm*term) &
            +  term4*phi_p*(vpl - ynorm*termpl)
      f43ul =  term4ul*phi_p*(v - ynorm*term) + term4*phi_pul*(v - ynorm*term) &
            +  term4*phi_p*(vul - ynorm*termul)
      f43vl =  term4vl*phi_p*(v - ynorm*term) + term4*phi_pvl*(v - ynorm*term) &
            +  term4*phi_p*(vvl - ynorm*termvl)
      f43wl =  term4wl*phi_p*(v - ynorm*term) + term4*phi_pwl*(v - ynorm*term) &
            +  term4*phi_p*(vwl - ynorm*termwl)

      f44pl =  term4pl*phi_p*(w - znorm*term) + term4*phi_ppl*(w - znorm*term) &
            +  term4*phi_p*(wpl - znorm*termpl)
      f44ul =  term4ul*phi_p*(w - znorm*term) + term4*phi_pul*(w - znorm*term) &
            +  term4*phi_p*(wul - znorm*termul)
      f44vl =  term4vl*phi_p*(w - znorm*term) + term4*phi_pvl*(w - znorm*term) &
            +  term4*phi_p*(wvl - znorm*termvl)
      f44wl =  term4wl*phi_p*(w - znorm*term) + term4*phi_pwl*(w - znorm*term) &
            +  term4*phi_p*(wwl - znorm*termwl)

!     t1 = f11 + f21 + f31 + f41
!     t2 = f12 + f22 + f32 + f42
!     t3 = f13 + f23 + f33 + f43
!     t4 = f14 + f24 + f34 + f44

      t1pl = f11pl + f21pl + f31pl + f41pl
      t1ul = f11ul + f21ul + f31ul + f41ul
      t1vl = f11vl + f21vl + f31vl + f41vl
      t1wl = f11wl + f21wl + f31wl + f41wl

      t2pl = f12pl + f22pl + f32pl + f42pl
      t2ul = f12ul + f22ul + f32ul + f42ul
      t2vl = f12vl + f22vl + f32vl + f42vl
      t2wl = f12wl + f22wl + f32wl + f42wl

      t3pl = f13pl + f23pl + f33pl + f43pl
      t3ul = f13ul + f23ul + f33ul + f43ul
      t3vl = f13vl + f23vl + f33vl + f43vl
      t3wl = f13wl + f23wl + f33wl + f43wl

      t4pl = f14pl + f24pl + f34pl + f44pl
      t4ul = f14ul + f24ul + f34ul + f44ul
      t4vl = f14vl + f24vl + f34vl + f44vl
      t4wl = f14wl + f24wl + f34wl + f44wl

! Calculate the flux vector on the left side
! Note: area term deferrred until the end

!     fluxp1 = area*beta*(ubarl-face_speed)

        fluxp1pl = beta*ubarlpl
        fluxp1ul = beta*ubarlul
        fluxp1vl = beta*ubarlvl
        fluxp1wl = beta*ubarlwl

!     fluxp2 = area*(ul*(ubarl-face_speed) + xnorm*pl)

        fluxp2pl = ulpl*(ubarl-face_speed) + ul*ubarlpl + xnorm*plpl
        fluxp2ul = ulul*(ubarl-face_speed) + ul*ubarlul + xnorm*plul
        fluxp2vl = ulvl*(ubarl-face_speed) + ul*ubarlvl + xnorm*plvl
        fluxp2wl = ulwl*(ubarl-face_speed) + ul*ubarlwl + xnorm*plwl

!     fluxp3 = area*(vl*(ubarl-face_speed) + ynorm*pl)

        fluxp3pl = vlpl*(ubarl-face_speed) + vl*ubarlpl + ynorm*plpl
        fluxp3ul = vlul*(ubarl-face_speed) + vl*ubarlul + ynorm*plul
        fluxp3vl = vlvl*(ubarl-face_speed) + vl*ubarlvl + ynorm*plvl
        fluxp3wl = vlwl*(ubarl-face_speed) + vl*ubarlwl + ynorm*plwl

!     fluxp4 = area*(wl*(ubarl-face_speed) + znorm*pl)

        fluxp4pl = wlpl*(ubarl-face_speed) + wl*ubarlpl + znorm*plpl
        fluxp4ul = wlul*(ubarl-face_speed) + wl*ubarlul + znorm*plul
        fluxp4vl = wlvl*(ubarl-face_speed) + wl*ubarlvl + znorm*plvl
        fluxp4wl = wlwl*(ubarl-face_speed) + wl*ubarlwl + znorm*plwl

! Now the right side
! Note: area term deferrred until the end

!     fluxm1 = area*beta*(ubarr-face_speed)

        fluxm1pl = beta*ubarrpl
        fluxm1ul = beta*ubarrul
        fluxm1vl = beta*ubarrvl
        fluxm1wl = beta*ubarrwl

!     fluxm2 = area*(ur*(ubarr-face_speed) + xnorm*pr)

        fluxm2pl = urpl*(ubarr-face_speed) + ur*ubarrpl + xnorm*prpl
        fluxm2ul = urul*(ubarr-face_speed) + ur*ubarrul + xnorm*prul
        fluxm2vl = urvl*(ubarr-face_speed) + ur*ubarrvl + xnorm*prvl
        fluxm2wl = urwl*(ubarr-face_speed) + ur*ubarrwl + xnorm*prwl

!     fluxm3 = area*(vr*(ubarr-face_speed) + ynorm*pr)

        fluxm3pl = vrpl*(ubarr-face_speed) + vr*ubarrpl + ynorm*prpl
        fluxm3ul = vrul*(ubarr-face_speed) + vr*ubarrul + ynorm*prul
        fluxm3vl = vrvl*(ubarr-face_speed) + vr*ubarrvl + ynorm*prvl
        fluxm3wl = vrwl*(ubarr-face_speed) + vr*ubarrwl + ynorm*prwl

!     fluxm4 = area*(wr*(ubarr-face_speed) + znorm*pr)

        fluxm4pl = wrpl*(ubarr-face_speed) + wr*ubarrpl + znorm*prpl
        fluxm4ul = wrul*(ubarr-face_speed) + wr*ubarrul + znorm*prul
        fluxm4vl = wrvl*(ubarr-face_speed) + wr*ubarrvl + znorm*prvl
        fluxm4wl = wrwl*(ubarr-face_speed) + wr*ubarrwl + znorm*prwl

! Finally, form the numerical flux
! Area terms now added back in

!     res1      = 0.5_dp*(fluxp1 + fluxm1 - area*t1)

        flux1pl = half_area*(fluxp1pl + fluxm1pl - t1pl)
        flux1ul = half_area*(fluxp1ul + fluxm1ul - t1ul)
        flux1vl = half_area*(fluxp1vl + fluxm1vl - t1vl)
        flux1wl = half_area*(fluxp1wl + fluxm1wl - t1wl)

        dflroe_i(1,1) = flux1pl
        dflroe_i(1,2) = flux1ul
        dflroe_i(1,3) = flux1vl
        dflroe_i(1,4) = flux1wl

!     res2      = 0.5_dp*(fluxp2 + fluxm2 - area*t2)

        flux2pl = half_area*(fluxp2pl + fluxm2pl - t2pl)
        flux2ul = half_area*(fluxp2ul + fluxm2ul - t2ul)
        flux2vl = half_area*(fluxp2vl + fluxm2vl - t2vl)
        flux2wl = half_area*(fluxp2wl + fluxm2wl - t2wl)

        dflroe_i(2,1) = flux2pl
        dflroe_i(2,2) = flux2ul
        dflroe_i(2,3) = flux2vl
        dflroe_i(2,4) = flux2wl

!     res3      = 0.5_dp*(fluxp3 + fluxm3 - area*t3)

        flux3pl = half_area*(fluxp3pl + fluxm3pl - t3pl)
        flux3ul = half_area*(fluxp3ul + fluxm3ul - t3ul)
        flux3vl = half_area*(fluxp3vl + fluxm3vl - t3vl)
        flux3wl = half_area*(fluxp3wl + fluxm3wl - t3wl)

        dflroe_i(3,1) = flux3pl
        dflroe_i(3,2) = flux3ul
        dflroe_i(3,3) = flux3vl
        dflroe_i(3,4) = flux3wl

!     res4   = 0.5_dp*(fluxp4 + fluxm4 - area*t4)

        flux4pl = half_area*(fluxp4pl + fluxm4pl - t4pl)
        flux4ul = half_area*(fluxp4ul + fluxm4ul - t4ul)
        flux4vl = half_area*(fluxp4vl + fluxm4vl - t4vl)
        flux4wl = half_area*(fluxp4wl + fluxm4wl - t4wl)

        dflroe_i(4,1) = flux4pl
        dflroe_i(4,2) = flux4ul
        dflroe_i(4,3) = flux4vl
        dflroe_i(4,4) = flux4wl

  end function dflroe_i

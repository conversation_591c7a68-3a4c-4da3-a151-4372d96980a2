module unravel_interface

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: refine_unravel_start
  public :: refine_unravel_tet
  public :: refine_unravel_thaw
  public :: refine_unravel_it
  public :: refine_unravel_xyz
  public :: refine_unravel_cleanup

  interface refine_unravel_tet
    module procedure refine_unravel_tet_r
    module procedure refine_unravel_tet_c
  end interface

  interface refine_unravel_xyz
    module procedure refine_unravel_xyz_r
    module procedure refine_unravel_xyz_c
  end interface

  contains

  subroutine refine_unravel_start( version, status )
    integer, intent(out) :: version, status
#ifdef HAVE_REFINE
    interface
      subroutine unravel_start( version, status )
        integer, intent(out) :: version, status
      end subroutine unravel_start
    end interface
    continue
    call unravel_start(version,status)
#else
    version = 0
    status = 1
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refine_unravel_start

  subroutine refine_unravel_tet_r( c2n, x, y, z, status )
    use kinddefs,          only : dp
    integer,  dimension(4), intent(in) :: c2n
    real(dp), dimension(4), intent(in) :: x, y, z
    integer,                intent(out) :: status
#ifdef HAVE_REFINE
    interface
      subroutine unravel_tet( c2n, x, y, z, status )
        integer,  dimension(4), intent(in) :: c2n
        real(selected_real_kind(15,307)), dimension(4), intent(in) :: x, y, z
        integer,            intent(out) :: status
      end subroutine unravel_tet
    end interface
    call unravel_tet( c2n, x, y, z, status )
#else
    status = 1
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) c2n, x, y, z
#endif
  end subroutine refine_unravel_tet_r

  subroutine refine_unravel_tet_c( c2n, cx, cy, cz, status )
    use kinddefs, only : dp
    integer,     dimension(4), intent(in)  :: c2n
    complex(dp), dimension(4), intent(in)  :: cx, cy, cz
    integer,                   intent(out) :: status
    real(dp), dimension(4) :: x, y, z
    continue
    x = real(cx,dp)
    y = real(cy,dp)
    z = real(cz,dp)
    call refine_unravel_tet_r( c2n, x, y, z, status )
  end subroutine refine_unravel_tet_c

  subroutine refine_unravel_thaw( node, status )
    integer, intent(in)  :: node
    integer, intent(out) :: status
#ifdef HAVE_REFINE
    interface
      subroutine unravel_thaw( node, status )
        integer, intent(in)  :: node
        integer, intent(out) :: status
      end subroutine unravel_thaw
    end interface
    continue
    call unravel_thaw(node,status)
#else
    status = 1
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) node
#endif
  end subroutine refine_unravel_thaw

  subroutine refine_unravel_it( status )
    integer, intent(out) :: status
#ifdef HAVE_REFINE
    interface
      subroutine unravel_it( status )
        integer, intent(out) :: status
      end subroutine unravel_it
    end interface
    continue
    call unravel_it(status)
#else
    status = 1
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refine_unravel_it

  subroutine refine_unravel_xyz_r( node, x, y, z, status )
    use kinddefs,          only : dp
    integer,                intent(in)  :: node
    real(dp),               intent(out) :: x, y, z
    integer,                intent(out) :: status
#ifdef HAVE_REFINE
    interface
      subroutine unravel_xyz( node, x, y, z, status )
        integer,            intent(in)  :: node
        real(selected_real_kind(15,307)), intent(out)  :: x, y, z
        integer,            intent(out) :: status
      end subroutine unravel_xyz
    end interface
    call unravel_xyz( node, x, y, z, status )
#else
    x=0;y=0;z=0;
    status = 1
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) node
#endif
  end subroutine refine_unravel_xyz_r

  subroutine refine_unravel_xyz_c( node, cx, cy, cz, status )
    use kinddefs, only : dp
    integer,                intent(in)  :: node
    complex(dp),            intent(out) :: cx, cy, cz
    integer,                intent(out) :: status
    real(dp) :: x, y, z
    continue
    call refine_unravel_xyz_r( node, x, y, z, status )
    cx = cmplx( x, 0.0_dp, dp )
    cy = cmplx( y, 0.0_dp, dp )
    cz = cmplx( z, 0.0_dp, dp )
  end subroutine refine_unravel_xyz_c

  subroutine refine_unravel_cleanup( status )
    integer, intent(out) :: status
#ifdef HAVE_REFINE
    interface
      subroutine unravel_cleanup( status )
        integer, intent(out) :: status
      end subroutine unravel_cleanup
    end interface
    continue
    call unravel_cleanup(status)
#else
    status = 1
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refine_unravel_cleanup

end module unravel_interface

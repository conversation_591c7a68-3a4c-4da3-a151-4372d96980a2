module tecplot_io_helpers

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs, only : dp
  use complex_functions, only : o

  implicit none

  private

  !beginNoComplexInterface
  public :: write_volume_tec
  public :: write_boundary_tec
  public :: write_sampling_tec
  public :: write_schlieren_tec
  public :: write_negative_volume_tec
  public :: set_boundaries_to_animate
  public :: set_boundaries_to_output
  public :: set_strand_usage
  !endNoComplexInterface

  public :: tecplot_connectivity, get_tec_file_extension
  public :: bypass_solver

  public :: tecplot_has_binary, ascii_tecplot_output

! tecplot output variables common to volume, sampling and boundary data output

  logical         :: boundary_strands, volume_strands
  logical         :: bypass_solver
  logical         :: ascii_tecplot_output = .false.  ! when .true., this will
                                                     ! force ascii tecplot
                                                     ! output (when code is
                                                     ! linked with tecio.a)
#ifdef HAVE_TECIO

  integer            :: tecfile_id
  integer, parameter :: debug    = 0 ! debug tracing (0/1 off/on)
  integer, parameter :: isdouble = 0 ! 0/1 single/double precision
                                     ! driver routines assme single

  character(1), parameter :: nullchar = char(0) ! for C string termination

#endif

contains


!================================ GET_TEC_EXTENSION ==========================80
!
! Sets the proper extension for tecplot output files depending if they are
! ascii or binary
!
!=============================================================================80

  subroutine get_tec_file_extension(tec_file_extension, always_output_ascii)

    character(3),           intent(out) :: tec_file_extension
    logical,      optional, intent(in)  :: always_output_ascii

  continue

#ifdef HAVE_TECIO
    if (ascii_tecplot_output .or. present(always_output_ascii)) then
      tec_file_extension = "dat"
    else
      tec_file_extension = "plt"
    end if
#else
    tec_file_extension = "dat"
    if (present(always_output_ascii)) then  ! suppress compiler warning
      if (.false.) write(*,*) always_output_ascii
    end if
#endif

  end subroutine get_tec_file_extension


!================================ SET_STRAND_USAGE ===========================80
!
! Turns off strand usage if frequency of animation is -1
!
!=============================================================================80

  subroutine set_strand_usage(bypass_solver, bndry_strands, vol_strands,       &
                              sampl_strands, report_usage)

    use nml_global,       only : boundary_animation_freq, volume_animation_freq
    use lmpi,             only : lmpi_master
    use sampling_headers, only : sampling_strands, number_of_geometries,       &
                                 sampling_frequency

    logical,           intent(in) :: bypass_solver
    logical, optional, intent(in) :: bndry_strands, vol_strands, sampl_strands
    logical, optional, intent(in) :: report_usage

    logical :: report

    integer :: igeom

  continue

!   default to use strands

    boundary_strands = .true.
    volume_strands   = .true.
    sampling_strands(:) = .true.

    if (boundary_animation_freq(1) <= 0) boundary_strands    = .false.
    if (volume_animation_freq(1) <= 0)   volume_strands      = .false.
    do igeom = 1,number_of_geometries
      if (sampling_frequency(igeom) <= 0) sampling_strands(igeom) = .false.
    end do

    if (bypass_solver) then
      boundary_strands    = .false.
      volume_strands      = .false.
      sampling_strands(:) = .false.
    end if

!   override the default behavior(s) if requested

    if (present(bndry_strands)) boundary_strands    = bndry_strands
    if (present(vol_strands))   volume_strands      = vol_strands
    if (present(sampl_strands)) sampling_strands(:) = sampl_strands

    report = .false.
    if (present(report_usage)) report = report_usage

    if (report .and. lmpi_master) then
      write(*,*)
      write(*,*) ' boundary_strands = ', boundary_strands
      write(*,*) ' volume_strands   = ', volume_strands
      write(*,*) ' sampling_strands = ',                                       &
                                       sampling_strands(1:number_of_geometries)
      write(*,*)
    end if

  end subroutine set_strand_usage


!=============================== WRITE_VOLUME_TEC ============================80
!
! Driver to choose between writing an ascii or binary tecplot volume file
! Optional argument always_output_ascii forces ascii output
!
!=============================================================================80

  subroutine write_volume_tec(nvars, nnodes, ncells, output_data, file_title,  &
                              zone_title, variable_list, filename, nelem,      &
                              elem, tets_only, valuelocation,                  &
                              time_val, always_output_ascii)

    use element_types, only : elem_type

    integer,                                   intent(in) :: nvars
    integer,                                   intent(in) :: nnodes
    integer,                                   intent(in) :: ncells
    integer,                                   intent(in) :: nelem
    integer,         dimension(nvars),         intent(in) :: valuelocation
    real(dp),        dimension(:,:),           intent(in) :: output_data
    real(dp),                                  intent(in) :: time_val
    type(elem_type), dimension(nelem),         intent(in) :: elem
    logical,                                   intent(in) :: tets_only
    logical,                         optional, intent(in) :: always_output_ascii
    character(256),                            intent(in) :: filename
    character(256),                            intent(in) :: variable_list
    character(256),                            intent(in) :: file_title
    character(256),                            intent(in) :: zone_title

  continue

#ifdef HAVE_TECIO
    if (ascii_tecplot_output .or. present(always_output_ascii)) then
      call write_volume_tec_ascii(nvars, nnodes, ncells, output_data,          &
                                  file_title, zone_title, variable_list,       &
                                  filename, nelem, elem, tets_only,            &
                                  valuelocation, time_val)
    else
      call write_volume_tec_binary(nvars, nnodes, ncells, output_data,         &
                                   file_title, zone_title, variable_list,      &
                                   filename, nelem, elem, tets_only,           &
                                   valuelocation, time_val)
    end if
#else
    call write_volume_tec_ascii(nvars, nnodes, ncells, output_data,            &
                                file_title, zone_title, variable_list,         &
                                filename, nelem, elem, tets_only,              &
                                valuelocation, time_val)
    if (present(always_output_ascii)) then  ! suppress compiler warning
      if (.false.) write(*,*) always_output_ascii
    end if
#endif

  end subroutine write_volume_tec


!============================ WRITE_VOLUME_TEC_ASCII =========================80
!
! Writes an ascii volume tecplot file in "FEBLOCK" style
!
!=============================================================================80

  subroutine write_volume_tec_ascii(nvars, nnodes, ncells, output_data,        &
                                    file_title, zone_title, variable_list,     &
                                    filename, nelem, elem, tets_only,          &
                                    valuelocation, time_val)

    use element_types, only : elem_type
    use info_depr,     only : cc_primal
    use file_utils,    only : available_unit

    integer,                           intent(in) :: nvars, nnodes, ncells
    integer,                           intent(in) :: nelem
    integer,         dimension(nvars), intent(in) :: valuelocation
    real(dp),        dimension(:,:),   intent(in) :: output_data
    real(dp),                          intent(in) :: time_val
    type(elem_type), dimension(nelem), intent(in) :: elem
    logical,                           intent(in) :: tets_only
    character(256),                    intent(in) :: filename
    character(256),                    intent(in) :: variable_list
    character(256),                    intent(in) :: file_title, zone_title

    integer :: iunit, i, ielem, n, n_vars_are_cc, loop_index, strandid

    integer, dimension(nvars) :: var_is_cc

    character(256) :: format_cc, format_node

    character(15) :: cell_type

  continue

    iunit = available_unit()
    open(iunit,file=filename)

    strandid = 10000       ! all volume zones get the same strandid

    if (.not. volume_strands) strandid = 0

    write(iunit,'(a)') 'title="'//trim(adjustl(file_title))//'"'
    write(iunit,'(a)') 'variables='//trim(adjustl(variable_list))

!   write the zone header data

    if (tets_only) then
      cell_type = 'tetrahedron'
    else
      cell_type = 'brick'
    end if

    cc_or_nc : if (cc_primal) then ! check for cell-centered variables

      n_vars_are_cc = 0 ; var_is_cc(:)  = 0

      do n = 1,nvars
        if ( valuelocation(n) == 0 ) then
          n_vars_are_cc = n_vars_are_cc + 1
          var_is_cc(n_vars_are_cc) = n
        end if
      end do

      if ( n_vars_are_cc > 0 ) then ! set Tecplot VARLOCATION parameter

        write(format_cc,1) n_vars_are_cc
 1      format( "('zone t=',a,', n=',i0,', e=',i0,', f=feblock, et=',a,&
                 &', varlocation=([',",i0,"(1x,i0),' ]=cellcentered)')" )

        write(iunit,format_cc) trim(adjustl(zone_title)), nnodes, ncells,      &
                               trim(cell_type), (var_is_cc(n),n=1,n_vars_are_cc)

      end if

    else ! nodal

      format_node = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                    &', n=',i0,', e=',i0,', f=feblock, et=',a)"

      write(iunit,format_node) trim(adjustl(zone_title)), o(time_val), &
                               strandid, nnodes, ncells,trim(cell_type)

    end if cc_or_nc

!   write volume data

    do n = 1,nvars
      loop_index = nnodes
      if (valuelocation(n) == 0) loop_index = ncells
      write(iunit,'(7e20.12)') (o(output_data(n,i)), i=1,loop_index)
    end do

!   write volume connectiviy

    do ielem = 1, nelem
      call tecplot_connectivity( elem(ielem)%type_cell,     elem(ielem)%ncell, &
                                 elem(ielem)%node_per_cell, elem(ielem)%c2n,   &
                                 tets_only,                 iunit )
    end do

    close(iunit)

  end subroutine write_volume_tec_ascii


#ifdef HAVE_TECIO
!============================ WRITE_VOLUME_TEC_BINARY ========================80
!
! Writes an binary volume tecplot file in "FEBLOCK" style...requires linking
! to tecplot's "tecio" library
!
!=============================================================================80

  subroutine write_volume_tec_binary(nvars, nnodes, ncells, output_data,       &
                                     file_title, zone_title, variable_list,    &
                                     filename, nelem, elem, tets_only,         &
                                     valuelocation, time_val)

    use info_depr,     only : cc_primal
    use element_types, only : elem_type
    use kinddefs,      only : r4

    integer,                           intent(in) :: nvars, nnodes, ncells
    integer,                           intent(in) :: nelem
    integer,         dimension(nvars), intent(in) :: valuelocation
    real(dp),        dimension(:,:),   intent(in) :: output_data
    real(dp),                          intent(in) :: time_val
    type(elem_type), dimension(nelem), intent(in) :: elem
    logical,                           intent(in) :: tets_only
    character(256),                    intent(in) :: filename
    character(256),                    intent(in) :: variable_list
    character(256),                    intent(in) :: file_title, zone_title

    integer :: ielem, j, n, c2n_temp_nnode, loop_index
    integer :: zone_type, tecfile_id, file_type, isblock, strandid
    integer :: itec, tecnod, tecdat, tecend, tecfil

    integer,  dimension(:,:), allocatable :: c2n_temp
    real(r4), dimension(:),   allocatable :: data_temp

    character(256) :: scratch_dir

  continue

!   allocate temp array for cell-to-node data

    if (tets_only) then
      c2n_temp_nnode = 4
    else
      c2n_temp_nnode = 8
    end if

    if (.not. allocated(c2n_temp)) allocate(c2n_temp(c2n_temp_nnode,ncells))

!   set some tecplot info

    tecfile_id  = 1        ! id tag for tecplot file
    file_type   = 0        ! 0=grid+soln, 1=grid, 2=soln
    scratch_dir = '.'      ! where to write temp files
    isblock     = 1        ! write data in block format
    strandid    = 10000    ! all volume zones get the same strandid

    if (.not. volume_strands) strandid = 0

    if (tets_only) then
      zone_type = 4        ! tetrahedron
    else
      zone_type = 5        ! brick
    end if

!   write file title and  variable list

    itec = my_tecini(trim(adjustl(file_title)),                                &
                     trim(adjustl(variable_list)),                             &
                     trim(adjustl(filename)),                                  &
                     trim(adjustl(scratch_dir)),                               &
                     file_type)

!   set id tag for tecplot file

    itec = tecfil(tecfile_id)

!   write zone info

    itec = my_teczne(trim(adjustl(zone_title)),                                &
                     nnodes,                                                   &
                     ncells,                                                   &
                     zone_type,                                                &
                     isblock,                                                  &
                     nvars,                                                    &
                     valuelocation,                                            &
                     strandid,                                                 &
                     time_val)

!   write volume data

    loop_index = nnodes
    do n = 1,nvars
      if (valuelocation(n) == 0) loop_index = max( loop_index, ncells )
    end do

    allocate(data_temp(loop_index))

    do n = 1,nvars
      loop_index = nnodes
      if (valuelocation(n) == 0) loop_index = ncells
      do j = 1,loop_index
        data_temp(j) = real(output_data(n,j),r4)
      end do
      itec = tecdat(loop_index, data_temp, isdouble)
    end do

    deallocate(data_temp)

!   output volume connectiviy

    do ielem = 1, nelem
      call tecplot_connectivity_binary                                         &
                   ( elem(ielem)%type_cell,     elem(ielem)%ncell,             &
                     elem(ielem)%node_per_cell, elem(ielem)%c2n,               &
                     tets_only,                 c2n_temp,                      &
                     c2n_temp_nnode,            ncells,                        &
                     ielem )
    end do

    itec = tecnod(c2n_temp)

!   finalize

    itec = tecfil(tecfile_id)
    itec = tecend()

  end subroutine write_volume_tec_binary
#endif


!============================== WRITE_BOUNDARY_TEC ===========================80
!
! Driver to choose between writing an ascii or binary tecplot boundary file
! Optional argument always_output_ascii forces ascii output
!
!=============================================================================80

  subroutine write_boundary_tec(nvars, nfacenodes, nface, f2n, output_data,    &
                                file_title, zone_title, variable_list,         &
                                filename, ib, first_bndry, last_bndry, status, &
                                time_val, always_output_ascii)

    integer,                                intent(in) :: nvars, nfacenodes
    integer,                                intent(in) :: nface, ib, status
    integer,                                intent(in) :: first_bndry
    integer,                                intent(in) :: last_bndry
    integer,  dimension(4,nface),           intent(in) :: f2n
    real(dp), dimension(nvars, nfacenodes), intent(in) :: output_data
    real(dp),                               intent(in) :: time_val
    character(256),                         intent(in) :: filename
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: file_title, zone_title
    logical,                      optional, intent(in) :: always_output_ascii

  continue

#ifdef HAVE_TECIO
    if (ascii_tecplot_output .or. present(always_output_ascii)) then
      call write_boundary_tec_ascii(nvars, nfacenodes, nface, f2n,             &
                                    output_data, file_title, zone_title,       &
                                    variable_list, filename,                   &
                                    ib, first_bndry, last_bndry, status,       &
                                    time_val)
    else
      call write_boundary_tec_binary(nvars, nfacenodes, nface, f2n,            &
                                     output_data, file_title, zone_title,      &
                                     variable_list, filename,                  &
                                     ib, first_bndry, last_bndry, status,      &
                                     time_val)
    end if
#else
    call write_boundary_tec_ascii(nvars, nfacenodes, nface,f2n,                &
                                  output_data, file_title, zone_title,         &
                                  variable_list, filename,                     &
                                  ib, first_bndry, last_bndry, status,         &
                                  time_val)
    if (present(always_output_ascii)) then  ! suppress compiler warning
      if (.false.) write(*,*) always_output_ascii
    end if
#endif

  end subroutine write_boundary_tec


!=========================== WRITE_BOUNDARY_TEC_ASCII ========================80
!
! Writes an ascii boundary tecplot file in "FEBLOCK" style
!
! Note: status indicates whether sufficient memory was available for allocation
! of the output_data array; if ok (status=0), proceeded with output; if not
! (status /= 0) then don't write out data for this boundary, but do open/close
! the file as needed
!
!=============================================================================80

  subroutine write_boundary_tec_ascii(nvars, nfacenodes, nface, f2n,           &
                                      output_data, file_title, zone_title,     &
                                      variable_list, filename, ib,             &
                                      first_bndry, last_bndry, status,         &
                                      time_val)

    use file_utils, only : available_unit

    integer,                                intent(in) :: nvars, nfacenodes
    integer,                                intent(in) :: nface, ib, status
    integer,                                intent(in) :: first_bndry
    integer,                                intent(in) :: last_bndry
    integer,  dimension(4,nface),           intent(in) :: f2n
    real(dp), dimension(nvars, nfacenodes), intent(in) :: output_data
    real(dp),                               intent(in) :: time_val
    character(256),                         intent(in) :: filename
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: file_title, zone_title

    integer       :: i, j, n, strandid
    integer, save :: iunit

    character(256) :: format_block, format_block2, format_point, format_point2

    logical, parameter :: block_format = .true.

  continue

    strandid = ib + 1000 ! each bndry has a unique id; add 1000 to keep
                         ! distinct from sampling geometries

    if (.not. boundary_strands) strandid = 0

    format_block  = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', j=',i0,', f=feblock')"
    format_block2 = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', f=block')"
    format_point  = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', j=',i0,', f=fepoint')"
    format_point2 = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', f=point')"

!   open file, write file title, variable list and zone info first time through

    if (ib == first_bndry) then

      iunit = available_unit()
      open(unit=iunit,file=filename)
      rewind(iunit)

      write(iunit,'(3a)') 'title="',trim(adjustl(file_title)),'"'

      write(iunit,'(2a)') 'variables = ', trim(adjustl(variable_list))

    end if

!   write the data to the file only if the output_data array has been
!   successfully allocated

    write_if_mem_avail : if (status == 0) then

!     write zone title

      if ( block_format ) then
        if (nface > 0) then
          write(iunit,format_block) trim(adjustl(zone_title)),                 &
                                    o(time_val), strandid, nfacenodes, nface
        else
          write(iunit,format_block2) trim(adjustl(zone_title)),                &
                                     o(time_val), strandid, nfacenodes
        end if
      else
        if (nface > 0) then
          write(iunit,format_point) trim(adjustl(zone_title)),                 &
                                    o(time_val), strandid, nfacenodes, nface
        else
          write(iunit,format_point2) trim(adjustl(zone_title)),                &
                                     o(time_val), strandid, nfacenodes
        end if
      end if

!     write boundary data

      if ( block_format ) then
        do n = 1,nvars
          write(iunit,'(7e20.12)') (o(output_data(n,i)), i=1,nfacenodes)
        end do
      else
        do i = 1,nfacenodes
          write(iunit,'(7e20.12)') (o(output_data(n,i)), n=1,nvars)
        end do
      end if

!     write face connectiviy

      tria_face_loop : do j=1,nface
        if (f2n(4,j) == 0) then
          write(iunit,'(4i10)') f2n(1,j), f2n(2,j), f2n(3,j), f2n(3,j)
        end if
      end do tria_face_loop

      quad_face_loop : do j=1,nface
        if (f2n(4,j) /= 0) then
          write(iunit,'(4i10)') f2n(1,j), f2n(2,j), f2n(3,j), f2n(4,j)
        end if
      end do quad_face_loop

    end if write_if_mem_avail

!   close output file after last boundary is written

    if (ib == last_bndry) then
      close(iunit)
    end if

  end subroutine write_boundary_tec_ascii


#ifdef HAVE_TECIO
!=========================== WRITE_BOUNDARY_TEC_BINARY =======================80
!
! Writes an binary boundary tecplot file in "FEBLOCK" style...requires linking
! to tecplot's "tecio" library
!
! Note: status indicates whether sufficient memory was available for allocation
! of the output_data array; if ok (status=0), proceeded with output; if not
! (status /= 0) then don't write out data for this boundary, but do open/close
! the file as needed
!
!=============================================================================80

  subroutine write_boundary_tec_binary(nvars, nfacenodes, nface, f2n,          &
                                       output_data, file_title, zone_title,    &
                                       variable_list, filename, ib,            &
                                       first_bndry, last_bndry, status,        &
                                       time_val)

    use kinddefs, only : r4

    integer,                                intent(in) :: nvars, nfacenodes
    integer,                                intent(in) :: nface, ib, status
    integer,                                intent(in) :: first_bndry
    integer,                                intent(in) :: last_bndry
    integer,  dimension(4,nface),           intent(in) :: f2n
    real(dp), dimension(nvars, nfacenodes), intent(in) :: output_data
    real(dp),                               intent(in) :: time_val
    character(256),                         intent(in) :: filename
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: file_title, zone_title

    character(256) :: scratch_dir

    integer :: j, n, zone_type, itec, tecnod, tecdat, tecend, tecfil
    integer :: file_type, isblock, strandid

    integer, dimension(4,nface) :: f2n_temp
    integer, dimension(nvars)   :: valuelocation

    real(r4), dimension(:), allocatable :: data_temp

  continue

!   set some tecplot info

    tecfile_id  = 1         ! id tag for tecplot file
    file_type   = 0         ! 0=grid+soln, 1=grid, 2=soln
    scratch_dir = '.'       ! where to write temp files
    isblock     = 1         ! write data in block format
    zone_type   = 3         ! quad faces (node4 = 0 for triangle)
    strandid    = ib + 1000 ! each bndry has a unique id; add 1000 to keep
                            ! distinct from sampling geometries

    if (.not. boundary_strands) strandid = 0

    valuelocation(:)  = 1   ! for now all boundary data is node-centered

!   write file title and  variable list first time through

    if (ib == first_bndry) then

      itec = my_tecini(trim(adjustl(file_title)),                              &
                       trim(adjustl(variable_list)),                           &
                       trim(adjustl(filename)),                                &
                       trim(adjustl(scratch_dir)),                             &
                       file_type)

      itec = tecfil(tecfile_id)   ! set id tag for tecplot file

    end if

!   write the data to the file only if the output_data array has been
!   successfully allocated

    write_if_mem_avail : if (status == 0) then

      f2n_temp(:,:) = f2n

      do j=1,nface
        if (f2n_temp(4,j) == 0) then
          f2n_temp(4,j) = f2n_temp(3,j)
        end if
      end do

!     write zone info

      itec = my_teczne(trim(adjustl(zone_title)),                              &
                       nfacenodes,                                             &
                       nface,                                                  &
                       zone_type,                                              &
                       isblock,                                                &
                       nvars,                                                  &
                       valuelocation,                                          &
                       strandid,                                               &
                       time_val)

!     write boundary data

      allocate(data_temp(nfacenodes))

      do n = 1,nvars
        do j = 1,nfacenodes
          data_temp(j) = real(output_data(n,j),r4)
        end do
        itec = tecdat(nfacenodes, data_temp, isdouble)
      end do

      deallocate(data_temp)

!     output face connectiviy

      itec = tecnod(f2n_temp)

    end if write_if_mem_avail

!   finalize after last boundary is written

    if (ib == last_bndry) then
      itec = tecfil(tecfile_id)
      itec = tecend()
    end if

  end subroutine write_boundary_tec_binary
#endif

function tecplot_has_binary()
  logical :: tecplot_has_binary
  continue
#ifdef HAVE_TECIO
  tecplot_has_binary = .true.
  if (ascii_tecplot_output) tecplot_has_binary = .false.
#else
  tecplot_has_binary = .false.
#endif
end function tecplot_has_binary

!============================== WRITE_SAMPLING_TEC ===========================80
!
! Driver to choose between writing an ascii or binary tecplot sampling file
! Optional argument always_output_ascii forces ascii output
!
!=============================================================================80

  subroutine write_sampling_tec(nvars, nfacenodes, nface, f2n, output_data,    &
                                file_title, zone_title, variable_list,         &
                                filename, geom_number, proc_id, time_val,      &
                                append_history, always_output_ascii )

    integer,                                intent(in) :: nvars, nfacenodes
    integer,                                intent(in) :: nface, geom_number
    integer,                                intent(in) :: proc_id
    integer,  dimension(4,nface),           intent(in) :: f2n
    real(dp), dimension(nvars, nfacenodes), intent(in) :: output_data
    real(dp),                               intent(in) :: time_val
    character(256),                         intent(in) :: filename
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: file_title, zone_title
    logical,                                intent(in) :: append_history
    logical,                      optional, intent(in) :: always_output_ascii

  continue

#ifdef HAVE_TECIO
    if (ascii_tecplot_output .or. present(always_output_ascii)) then
      call write_sampling_tec_ascii(nvars, nfacenodes, nface, f2n,             &
                                    output_data, file_title, zone_title,       &
                                    variable_list, filename, geom_number,      &
                                    proc_id, time_val, append_history )
    else
      call write_sampling_tec_binary(nvars, nfacenodes, nface, f2n,            &
                                     output_data, file_title, zone_title,      &
                                     variable_list, filename, geom_number,     &
                                     proc_id, time_val, append_history )
    end if
#else
    call write_sampling_tec_ascii(nvars, nfacenodes, nface,f2n,                &
                                  output_data, file_title, zone_title,         &
                                  variable_list, filename, geom_number,        &
                                  proc_id, time_val, append_history )
    if (present(always_output_ascii)) then  ! suppress compiler warning
      if (.false.) write(*,*) always_output_ascii
    end if
#endif

  end subroutine write_sampling_tec


!=========================== WRITE_SAMPLING_TEC_ASCII ========================80
!
! Writes an ascii sampling tecplot file in "FEBLOCK" style
!
!=============================================================================80

  subroutine write_sampling_tec_ascii(nvars, nfacenodes, nface, f2n,           &
                                      output_data, file_title, zone_title,     &
                                      variable_list, filename, geom_number,    &
                                      proc_id, time_val, append_history )

    use lmpi,             only : lmpi_nproc
    use file_utils,       only : file_exists, available_unit
    use sampling_headers, only : sampling_strands

    integer,                                intent(in) :: nvars, nfacenodes
    integer,                                intent(in) :: nface, geom_number
    integer,                                intent(in) :: proc_id
    integer,  dimension(4,nface),           intent(in) :: f2n
    real(dp), dimension(nvars, nfacenodes), intent(in) :: output_data
    real(dp),                               intent(in) :: time_val
    character(256),                         intent(in) :: filename
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: file_title, zone_title
    logical,                                intent(in) :: append_history

    integer :: iunit, i, j, n, strandid

    character(256) :: format_block, format_point

    logical, parameter :: block_format = .true.

  continue

    iunit = available_unit()

    strandid = geom_number ! each sampling geometry has a unique id

    if (.not. sampling_strands(geom_number)) strandid = 0

    format_block = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', j=',i0,', f=feblock')"
    format_point = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', j=',i0,', f=fepoint')"

!   first processor (master), writes file title and variable list

    if (proc_id == 0) then

      if ( file_exists(filename) ) then
        if ( append_history ) then
          open(iunit,file=filename,position='append')
        else
          open(iunit,file=filename)
        endif
      else
        open(iunit,file=filename)
        rewind(iunit)
      endif

      write(iunit,'(3a)') 'title="',trim(adjustl(file_title)),'"'

      write(iunit,'(2a)') 'variables = ', trim(adjustl(variable_list))

    end if

!   write the data to the file only if this proc actiually has some data

    proc_has_data : if (nfacenodes > 0) then

!     write zone title

      if ( block_format ) then
        write(iunit,format_block) trim(adjustl(zone_title)),                   &
                                  o(time_val), strandid, nfacenodes, nface
      else
        write(iunit,format_point) trim(adjustl(zone_title)),                   &
                                  o(time_val), strandid, nfacenodes, nface
      end if

!     write sampling data

      if ( block_format ) then
        do n = 1,nvars
          write(iunit,'(7e20.12)') (o(output_data(n,i)), i=1,nfacenodes)
        end do
      else
        do i = 1,nfacenodes
          write(iunit,'(7e20.12)') (o(output_data(n,i)), n=1,nvars)
        end do
      end if

!     write face connectiviy

      tria_face_loop : do j=1,nface
        if (f2n(4,j) == 0) then
          write(iunit,'(4i10)') f2n(1,j), f2n(2,j), f2n(3,j), f2n(3,j)
        end if
      end do tria_face_loop

      quad_face_loop : do j=1,nface
        if (f2n(4,j) /= 0) then
          write(iunit,'(4i10)') f2n(1,j), f2n(2,j), f2n(3,j), f2n(4,j)
        end if
      end do quad_face_loop

    end if proc_has_data

!   close output file after last processor has done its' thing

    if (proc_id == lmpi_nproc-1) then
      close(iunit)
    end if

  end subroutine write_sampling_tec_ascii


#ifdef HAVE_TECIO
!=========================== WRITE_SAMPLING_TEC_BINARY =======================80
!
! Writes an binary sampling tecplot file in "FEBLOCK" style...requires linking
! to tecplot's "tecio" library
!
!=============================================================================80

  subroutine write_sampling_tec_binary(nvars, nfacenodes, nface, f2n,          &
                                       output_data, file_title, zone_title,    &
                                       variable_list, filename, geom_number,   &
                                       proc_id, time_val, append_history )

    use kinddefs,         only : r4
    use lmpi,             only : lmpi_master, lmpi_id, lmpi_nproc
    use sampling_headers, only : sampling_strands

    integer,                                intent(in) :: nvars, nfacenodes
    integer,                                intent(in) :: nface, geom_number
    integer,                                intent(in) :: proc_id
    integer,  dimension(4,nface),           intent(in) :: f2n
    real(dp), dimension(nvars, nfacenodes), intent(in) :: output_data
    real(dp),                               intent(in) :: time_val
    character(256),                         intent(in) :: filename
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: file_title, zone_title
    logical,                                intent(in) :: append_history

    character(256) :: scratch_dir

    integer :: j, n, zone_type, itec, tecnod, tecdat, tecend, tecfil
    integer :: file_type, isblock, strandid

    integer, dimension(4,nface) :: f2n_temp
    integer, dimension(nvars)   :: valuelocation

    real(r4), dimension(:), allocatable :: data_temp

  continue

!   set some tecplot info

    if ( append_history ) then
      tecfile_id  = geom_number  ! id tag for tecplot file
    else
      tecfile_id  = 1            ! id tag for tecplot file
    endif
    tecfile_id  = 1

    file_type   = 0            ! 0=grid+soln, 1=grid, 2=soln
    scratch_dir = '.'          ! where to write temp files
    isblock     = 1            ! write data in block format
    zone_type   = 3            ! quad faces (node4 = 0 for triangle)
    strandid    = geom_number  ! each sampling geometry has a unique id

    if (.not. sampling_strands(geom_number)) strandid = 0

    valuelocation(:)  = 1   ! for now all boundary data is node-centered

!   first processor (master), writes file title and variable list

    if (proc_id == 0) then

      itec = my_tecini(trim(adjustl(file_title)),                              &
                       trim(adjustl(variable_list)),                           &
                       trim(adjustl(filename)),                                &
                       trim(adjustl(scratch_dir)),                             &
                       file_type)

      itec = tecfil(tecfile_id)   ! set id tag for tecplot file

    end if

!   write the data to the file only if this proc actiually has some data

    proc_has_data : if (nfacenodes > 0) then

      f2n_temp(:,:) = f2n

      do j=1,nface
        if (f2n_temp(4,j) == 0) then
          f2n_temp(4,j) = f2n_temp(3,j)
        end if
      end do

!     write zone info

      itec = my_teczne(trim(adjustl(zone_title)),                              &
                       nfacenodes,                                             &
                       nface,                                                  &
                       zone_type,                                              &
                       isblock,                                                &
                       nvars,                                                  &
                       valuelocation,                                          &
                       strandid,                                               &
                       time_val)

!     write sampling data

      allocate(data_temp(nfacenodes))

      do n = 1,nvars
        do j = 1,nfacenodes
          data_temp(j) = real(output_data(n,j),r4)
        end do
        itec = tecdat(nfacenodes, data_temp, isdouble)
      end do

      deallocate(data_temp)

!     output face connectiviy

      itec = tecnod(f2n_temp)

    end if proc_has_data

!   close output file after last processor has done its' thing

    if (proc_id == lmpi_nproc-1) then
!     if ( .not.append_history ) then
        itec = tecfil(tecfile_id)
        itec = tecend()
!     endif
    end if

  end subroutine write_sampling_tec_binary
#endif

!============================= WRITE_SCHLIEREN_TEC ===========================80
!
! Driver to choose between writing an ascii or binary tecplot schlieren file
! Optional argument always_output_ascii forces ascii output
!
!=============================================================================80

  subroutine write_schlieren_tec(nvars, nlines, nrow, ncol, output_data,       &
                               file_title, zone_title, variable_list,          &
                               filename, geom_number, proc_id, time_val,       &
                               frequency, always_output_ascii )

    integer,                                intent(in) :: nvars
    integer,                                intent(in) :: nlines, nrow, ncol
    real(dp), dimension(nvars,nlines),      intent(in) :: output_data
    character(256),                         intent(in) :: file_title
    character(256),                         intent(in) :: zone_title
    character(256),                         intent(in) :: variable_list
    character(256),                         intent(in) :: filename
    integer,                                intent(in) :: geom_number
    integer,                                intent(in) :: proc_id
    real(dp),                               intent(in) :: time_val
    integer,                                intent(in) :: frequency
    logical,                      optional, intent(in) :: always_output_ascii

  continue

#ifdef HAVE_TECIO
     if (ascii_tecplot_output .or. present(always_output_ascii)) then
       call write_schlieren_tec_ascii(nvars, nlines, nrow, ncol, output_data,  &
                                     file_title, zone_title, variable_list,    &
                                     filename, geom_number, proc_id, time_val, &
                                     frequency )
     else
       call write_schlieren_tec_binary(nvars, nlines, nrow, ncol, output_data, &
                                      file_title, zone_title, variable_list,   &
                                      filename, geom_number, proc_id, time_val,&
                                      frequency )
     end if
#else
     call write_schlieren_tec_ascii(nvars, nlines, nrow, ncol, output_data,    &
                                   file_title, zone_title, variable_list,      &
                                   filename, geom_number, proc_id, time_val,   &
                                   frequency )
     if (present(always_output_ascii)) then  ! suppress compiler warning
       if (.false.) write(*,*) always_output_ascii
     end if
#endif

  end subroutine write_schlieren_tec

!=========================== WRITE_SCHLIEREN_TEC_ASCII =======================80
!
! Writes an ascii Schlieren tecplot file
!
!=============================================================================80

  subroutine write_schlieren_tec_ascii(nvars, nlines, nrow, ncol, output_data, &
                                                   file_title, zone_title,     &
                                      variable_list, filename, geom_number,    &
                                      proc_id, time_val, frequency )

    use file_utils,       only : available_unit
    use sampling_headers, only : sampling_strands

    integer,                            intent(in) :: nvars
    integer,                            intent(in) :: nlines, nrow, ncol
    integer,                            intent(in) :: geom_number
    real(dp), dimension(nvars, nlines), intent(in) :: output_data
    real(dp),                           intent(in) :: time_val
    character(256),                     intent(in) :: filename
    character(256),                     intent(in) :: variable_list
    character(256),                     intent(in) :: file_title, zone_title
    integer,                            intent(in) :: proc_id
    integer,                            intent(in) :: frequency

    integer :: iunit, i, n, strandid

    character(256) :: format_block, format_point

    logical, parameter :: block_format = .true.

  continue

    iunit = available_unit()

    strandid = geom_number ! each sampling geometry has a unique id

    if (.not. sampling_strands(geom_number)) strandid = 0
    if ( frequency > 0 ) strandid = geom_number

    format_block = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', j=',i0,', f=block')"
    format_point = "('zone t=',a,', solutiontime=',e14.7,', strandid=',i0,&
                  &', i=',i0,', j=',i0,', f=point')"

!   first processor (master), writes file title and variable list

    if (proc_id == 0) then

      open(unit=iunit,file=filename)
      rewind(iunit)

      write(iunit,'(3a)') 'title="',trim(adjustl(file_title)),'"'

      write(iunit,'(2a)') 'variables = ', trim(adjustl(variable_list))

    end if

!   write the data to the file only if this proc actiually has some data

    proc_has_data : if (nlines > 0) then

!     write zone title

      if ( block_format ) then
        write(iunit,format_block) trim(adjustl(zone_title)),                   &
                                  o(time_val), strandid, nrow, ncol
      else
        write(iunit,format_point) trim(adjustl(zone_title)),                   &
                                  o(time_val), strandid, nrow, ncol
      end if

!     write sampling data

      if ( block_format ) then
        do n = 1,nvars
          write(iunit,'(7e20.12)') (o(output_data(n,i)), i=1,nlines)

        end do
      else
        do i = 1,nlines
          write(iunit,'(7e20.12)') (o(output_data(n,i)), n=1,nvars)
        end do
      end if

    end if proc_has_data

!   close output file after last processor has done its' thing

!   if (proc_id == lmpi_nproc-1) then
      close(iunit)
!   end if

  end subroutine write_schlieren_tec_ascii


#ifdef HAVE_TECIO
!=========================== WRITE_SCHLIEREN_TEC_BINARY ======================80
!
! Writes an binary schlieren tecplot file in "FEBLOCK" style...requires linking
! to tecplot's "tecio" library
!
!=============================================================================80

  subroutine write_schlieren_tec_binary(nvars, nlines, nrow, ncol, output_data,&
                                                    file_title, zone_title,    &
                                       variable_list, filename, geom_number,   &
                                       proc_id, time_val, frequency)

    use kinddefs,         only : r4
    use lmpi,             only : lmpi_master, lmpi_id, lmpi_nproc
    use sampling_headers, only : sampling_strands

    integer,                            intent(in) :: nvars
    integer,                            intent(in) :: nlines, nrow, ncol
    integer,                            intent(in) :: geom_number
    integer,                            intent(in) :: proc_id
    real(dp), dimension(nvars, nlines), intent(in) :: output_data
    real(dp),                           intent(in) :: time_val
    character(256),                     intent(in) :: filename
    character(256),                     intent(in) :: variable_list
    character(256),                     intent(in) :: file_title, zone_title
    integer,                            intent(in) :: frequency

    character(256) :: scratch_dir

    integer :: j, n, zone_type, itec, tecdat, tecend, tecfil
    integer :: file_type, isblock, strandid

    integer, dimension(nvars)   :: valuelocation

    real(r4), dimension(:), allocatable :: data_temp

  continue

!   set some tecplot info

    tecfile_id  = 1            ! id tag for tecplot file
    file_type   = 0            ! 0=grid+soln, 1=grid, 2=soln
    scratch_dir = '.'          ! where to write temp files
    isblock     = 1            ! write data in block format
    zone_type   = 0            ! ordered data
    strandid    = geom_number  ! each sampling geometry has a unique id

    strandid = 0
    if ( sampling_strands(geom_number) ) strandid = geom_number

    valuelocation(:)  = 1   ! for now all boundary data is node-centered

!   first processor (master), writes file title and variable list

    if (proc_id == 0) then

      itec = my_tecini(trim(adjustl(file_title)),                              &
                       trim(adjustl(variable_list)),                           &
                       trim(adjustl(filename)),                                &
                       trim(adjustl(scratch_dir)),                             &
                       file_type)

      itec = tecfil(tecfile_id)   ! set id tag for tecplot file

    end if

!   write the data to the file only if this proc actiually has some data

    proc_has_data : if (nlines > 0) then

!     write zone info

      itec = my_teczne(trim(adjustl(zone_title)),                              &
                       nrow,                                                   &
                       ncol,                                                   &
                       zone_type,                                              &
                       isblock,                                                &
                       nvars,                                                  &
                       valuelocation,                                          &
                       strandid,                                               &
                       time_val)

!     write sampling data

      allocate(data_temp(nlines))

      do n = 1,nvars
        do j = 1,nlines
          data_temp(j) = real(output_data(n,j),r4)
        end do
        itec = tecdat(nlines, data_temp, isdouble)
      end do

      deallocate(data_temp)

    end if proc_has_data

!   close output file after last processor has done its' thing

!   if (proc_id == lmpi_nproc-1) then
      itec = tecfil(tecfile_id)
      itec = tecend()
!   end if

  end subroutine write_schlieren_tec_binary
#endif


!============================ WRITE_NEGATIVE_VOLUMES =========================80
!
! Dump out some diagnostic tecplot files if negative volumes are generated,
! then bail out
!
!=============================================================================80

  subroutine write_negative_volume_tec(grid, soln)

    use grid_metrics,     only : gridmetric
    use lmpi,             only : lmpi_master, lmpi_conditional_stop
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_globals, only : set_up_global_bndry_data, get_global_bndry_data
    use bc_names,         only : bc_used_for_force_calculation,                &
                                 bc_is_flow_through

    type(grid_type),           intent(inout) :: grid
    type(soln_type), optional, intent(inout) :: soln

    integer                             :: i, ib, negative_volume_cells
    integer                             :: nnegcell, ierr

    real(dp)                            :: cellvol, min_cellvol, max_cellvol
    real(dp)                            :: max_cellangle
    real(dp), dimension(:), allocatable :: negvol_nodes

    logical                             :: dump_geometry
    logical                             :: output_all_boundaries = .false.

  continue

!   first write out volume files from any processor with negative volumes

    allocate(negvol_nodes(size(grid%x)))

    negvol_nodes = 0.0_dp

    negative_volume_cells = 0

    ierr = 0

    do i = 1,grid%nelem

      call gridmetric( size(grid%x),                                           &
                       grid%x,                                                 &
                       grid%y,                                                 &
                       grid%z,                                                 &
                       cellvol, min_cellvol, max_cellvol,                      &
                       max_cellangle, nnegcell,                                &
                       grid%project,                                           &
                       size(grid%elem(i)%c2n,2),                               &
                       grid%elem(i)%ncell,                                     &
                       grid%elem(i)%type_cell,                                 &
                       grid%elem(i)%node_per_cell,                             &
                       grid%elem(i)%face_per_cell,                             &
                       grid%elem(i)%c2n,                                       &
                       grid%elem(i)%local_f2n,                                 &
                       grid%elem(i)%chk_norm,                                  &
                       negvol_nodes=negvol_nodes)

      negative_volume_cells = negative_volume_cells + nnegcell

    end do

    if (negative_volume_cells > 0) then
      call write_negvol_tec( grid, size(grid%x), negvol_nodes )
    end if

!   next dump out a file containing the geometry in the current location
!   to use as a reference when plotting the volumetric data output above

    dump_geometry = .true.

    if ( .not. present(soln) ) then
      if ( lmpi_master ) then
        write(*,*) 'soln not provided to write_negative_volume_tec():'
        write(*,*) 'skipping dump of reference geometry...'
      endif
      dump_geometry = .false.
    endif

    ref_geom : if ( dump_geometry ) then

      call set_up_global_bndry_data(grid, soln%global_bndry_data, soln%n_tot,  &
                                    soln%n_turb, soln%n_grd, soln%njac,        &
                                    soln%adim)

      soln%global_bndry_data(:)%used = .false.

      if (output_all_boundaries) then
        do ib = 1,grid%nbound
          soln%global_bndry_data(ib)%used = .true.
        end do
      else
        do ib = 1,grid%nbound
          if ( bc_is_flow_through(grid%bc(ib)%ibc) ) cycle
          if (bc_used_for_force_calculation(grid%bc(ib)%ibc)) then
            soln%global_bndry_data(ib)%used = .true.
          end if
        end do
      end if

      call get_global_bndry_data(grid%nbound, grid, soln,                      &
                                 soln%global_bndry_data, 0)

      call output_global_surface(grid, soln)

    endif ref_geom

    if (lmpi_master) then
      write(*,*)
      write(*,*) 'NEGATIVE PRIMAL CELL VOLUME - STOPPING'
      write(*,*)
      ierr = 1
    end if

    call lmpi_conditional_stop(ierr)

  end subroutine write_negative_volume_tec


!============================== WRITE_NEG_VOL_TEC ============================80
!
!  Driver routine to dump a volume tecplot file contains grid coordinates and
!  a negative volume indicator in either ascii or binary format (binary
!  output requires link to tecplot libraries)
!
!  Intended as a diagnostic dump for mesh deformation cases that go south
!
!  Each process that has negative volumes writes a file for its zone; files
!  can be "cat"'ed together or read by individually by tecplot
!
!=============================================================================80

  subroutine write_negvol_tec( grid, nnodes, negvol_nodes )

    use lmpi,                 only : lmpi_id
    use info_depr,            only : simulation_time, ntt
    use nml_nonlinear_solves, only : itime
    use grid_types,           only : grid_type
    use io,                   only : prior_iters

    type(grid_type),             intent(in) :: grid

    integer,                     intent(in) :: nnodes

    real(dp), dimension(nnodes), intent(in) :: negvol_nodes

    integer                                 :: i, ielem, n_output_variables
    integer                                 :: totalcells, status
    integer,  dimension(4)                  :: valuelocation

    real(dp), dimension(:,:),   allocatable :: output_data
    real(dp)                                :: time_val

    character(len=256)                      :: variable_list
    character(len=256)                      :: file_title
    character(len=256)                      :: zone_title
    character(len=256)                      :: filename
    character(len=80)                       :: restart_file
    character(len=80)                       :: proc_id
    character(len=80)                       :: step
    character(len=3)                        :: tec_file_extension

    logical                                 :: tets_only

  continue

    n_output_variables = 4    ! x y z neg_vol_node

    allocate(output_data(n_output_variables, grid%nnodes01), stat=status)

    check_memory : if (status /= 0) then
      write(*,'(2a)')                                                      &
       ' WARNING: memory allocation failed when attempting to allocate',   &
       ' output_data array in write_negvol_tec'
      write(*,'(a,i0)')                                                    &
       ' ...skipping requested volume data output on processor ', lmpi_id
!      allocate tiny amount of memory for safety
       allocate(output_data(1,1))
    end if check_memory

    tets_only  = .true.
    totalcells = 0
    do ielem = 1, grid%nelem
      totalcells = totalcells + grid%elem(ielem)%ncell
      if (grid%elem(ielem)%type_cell /= 'tet') tets_only  = .false.
    end do

!   only do output if we had enough memory to allocate the output_data array

    have_memory_for_output : if (status == 0) then

      output_data = 0._dp

      do i = 1, grid%nnodes01
        output_data(1,i) = grid%x(i)
        output_data(2,i) = grid%y(i)
        output_data(3,i) = grid%z(i)
        output_data(4,i) = negvol_nodes(i)
      end do

      valuelocation(1) = 1  ! all values at node locations
      valuelocation(2) = 1
      valuelocation(3) = 1
      valuelocation(4) = 1

!     set the file name

      write(proc_id,"(i0)") lmpi_id+1
      step = ''

      if (itime == 0) then
        time_val = real(ntt+prior_iters, dp)
      else
        time_val = simulation_time
      end if

      restart_file = trim(adjustl(grid%project)) // '_part'

      call get_tec_file_extension(tec_file_extension)

      filename = trim(restart_file) // trim(adjustl(proc_id)) //               &
                 "_tec_negative_volumes" // trim(adjustl(step)) //             &
                 "." // trim(adjustl(tec_file_extension))

!     set the file title

      file_title = 'tecplot geometry and solution file'

!     set the zone title

      write(zone_title,*) '"processor ' // trim(adjustl(proc_id)) // '",'

!     create the list of variables

      variable_list = 'x y z neg_vol_node'

!     now output the data in either ascii or binary form

      write(*,'(2a)') ' Writing diagnostic Tecplot file: ', trim(filename)

      call write_volume_tec(n_output_variables, grid%nnodes01, totalcells,     &
                            output_data, file_title, zone_title, variable_list,&
                            filename, grid%nelem, grid%elem, tets_only,        &
                            valuelocation, time_val)

    end if have_memory_for_output

  end subroutine write_negvol_tec


!============================= OUTPUT_GLOBAL_SURFACE =========================80
!
!  Driver routine for output of a tecplot file on boundaries for visualizing
!  negative-volume diagnostics; selects between formatted and binary tecplot
!  files (binary files require proprietry tecplot libraries)
!
!=============================================================================80

  subroutine output_global_surface(grid, soln)

    use info_depr,          only : simulation_time
    use lmpi,               only : lmpi_master
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type

    type(grid_type),        intent(in)    :: grid
    type(soln_type),        intent(inout) :: soln

    character(len=80)                          :: step, time_val, bndry
    character(len=80)                          :: restart_file
    character(len=3)                           :: tec_file_extension
    character(len=256)                         :: variable_list
    character(len=256)                         :: file_title
    character(len=256)                         :: zone_title
    character(len=256)                         :: filename

    integer,                              save :: n_output_variables
    integer                                    :: first_bndry, last_bndry
    integer                                    :: ib, nface, nfacenodes, status

    real(dp),      dimension(:,:), allocatable :: output_data

  continue

    if ( grid%cc ) return  ! this routine not cc aware

    master_writes_file : if (lmpi_master) then

!     set the file name

      step = ''

      if (simulation_time < 0._dp) then
        write(time_val,"(e14.7)") 0._dp
      else
        write(time_val,"(e14.7)") simulation_time
      end if

      restart_file = trim(adjustl(grid%project))

      call get_tec_file_extension(tec_file_extension)

      filename = trim(adjustl(restart_file)) // '_tec_boundary' //             &
                 trim(adjustl(step)) // '.' // trim(adjustl(tec_file_extension))

      write(*,'(2a)') ' Writing diagnostic Tecplot file: ', trim(filename)
      write(*,'(2a)') ' Tecplot files contain variables x, y, z, neg_vol_node'
      write(*,'(2a)') '   where neg_vol_node = 1 for nodes in cells with ',    &
                      'volume < 0'
      write(*,'(2a)') '         neg_vol_node = 0 for nodes in cells with ',    &
                      'volume > 0'
      write(*,'(2a)') '         neg_vol_node = -1 for boundary nodes in ',     &
                      trim(filename)
      write(*,'(2a)') ' In Tecplot, use value blanking on neg_vol_node = 0 ',  &
                      'to display only the cells with negative volume'

!     set the file title

      file_title = 'tecplot geometry and solution file'

!     determine the id number of the first and last boundaries to be written

      first_bndry = -1
      first_bnd : do ib = 1,grid%nbound
        if (.not. soln%global_bndry_data(ib)%used) cycle first_bnd
        first_bndry = ib
        exit first_bnd
      end do first_bnd

      last_bndry = -1
      last_bnd : do ib = 1,grid%nbound
        if (.not. soln%global_bndry_data(ib)%used) cycle last_bnd
        last_bndry = ib
      end do last_bnd

!     create the list of variables

      variable_list = 'x y z neg_vol_node'
      n_output_variables = 4

      boundary_loop : do ib=1,grid%nbound

        if (.not. soln%global_bndry_data(ib)%used) cycle

!       set the zone title

        write(bndry,"(i0)") ib

        zone_title = '"time '     // trim(adjustl(time_val)) //                &
                     ' boundary ' // trim(adjustl(bndry))    // '"'

!       set some local scalars for convenience

        nface           =  soln%global_bndry_data(ib)%nface
        nfacenodes      =  soln%global_bndry_data(ib)%nfacenodes

        have_data_to_output : if (nface > 0) then

!         store the data to be output

          allocate(output_data(n_output_variables, nfacenodes), stat=status)

          check_memory : if (status /= 0) then
            write(*,'(2a)')                                                    &
             ' WARNING: memory allocation failed when attempting to allocate', &
             ' output_data array in output_global_surface'
            write(*,'(a,i0)')                                                  &
             ' ...skipping requested boundary data output for boundary ', ib
!            allocate tiny amount of memory for safety
             allocate(output_data(1,1))
          end if check_memory

          if (status == 0) then
            output_data(1,:) = soln%global_bndry_data(ib)%xglobal_bndry(:)
            output_data(2,:) = soln%global_bndry_data(ib)%yglobal_bndry(:)
            output_data(3,:) = soln%global_bndry_data(ib)%zglobal_bndry(:)
            output_data(4,:) = -1.0_dp
          end if

!         now output the data on current boundary in either ascii or binary form

          call write_boundary_tec(n_output_variables, nfacenodes, nface,       &
                                  soln%global_bndry_data(ib)%f2n, output_data, &
                                  file_title, zone_title, variable_list,       &
                                  filename, ib, first_bndry, last_bndry,       &
                                  status, simulation_time)

          if (allocated(output_data)) deallocate(output_data)

        end if have_data_to_output

      end do boundary_loop

    end if master_writes_file

  end subroutine output_global_surface


!============================== TECPLOT_CONNECTIVITY =========================80
!
! Writes out cell-to-node connectivity info for tecplot volume data
!
!=============================================================================80

  subroutine tecplot_connectivity( type_cell, ncell, node_per_cell, c2n,       &
                                   tets_only, unit )

    logical,                                 intent(in) :: tets_only
    character(3),                            intent(in) :: type_cell
    integer,                                 intent(in) :: ncell, node_per_cell
    integer,                                 intent(in) :: unit
    integer, dimension(node_per_cell,ncell), intent(in) :: c2n

    integer :: i, j

    continue

!   Uses tecplot "tetrahedron" style for pure tet grids, and the more general
!   tecplot "brick" style for mixed-elements or non-tet elements (note that if
!   a mixed element grid has tets in it, the tets are written out in the
!   less-compact "brick" style)

    if (tets_only) then

!     only tets, can use tecplot tetrahedron element format
!     default nodal order is correct for tecplot

      do i =1, ncell
        write(unit,'(4i10)') (c2n(j,i),j=1,4)
      end do

    else

!     use tecplot brick element fomat, with repeated nodes in
!     tecplot-specific ordering for the general case

      select case (type_cell)

        case('tet')

          do i =1, ncell
            write(unit,'(4i10)') c2n(1,i), c2n(2,i), c2n(3,i), c2n(3,i),     &
                                 c2n(4,i), c2n(4,i), c2n(4,i), c2n(4,i)
          end do

        case('hex')

          do i =1, ncell
            write(unit,'(4i10)') c2n(1,i), c2n(2,i), c2n(4,i), c2n(3,i),     &
                                 c2n(5,i), c2n(6,i), c2n(8,i), c2n(7,i)
          end do

        case('prz')

          do i =1, ncell
            write(unit,'(4i10)') c2n(3,i), c2n(5,i), c2n(6,i), c2n(4,i),     &
                                 c2n(2,i), c2n(2,i), c2n(1,i), c2n(1,i)
          end do

        case('pyr')

          do i =1, ncell
            write(unit,'(4i10)') c2n(1,i), c2n(2,i), c2n(3,i), c2n(4,i),     &
                                 c2n(5,i), c2n(5,i), c2n(5,i), c2n(5,i)
          end do

        case default

          write(*,'(3a)') ' Element type ', type_cell, ' not yet supported ',&
                          ' in tecplot_connectivity  routine'

        end select

    end if

  end subroutine tecplot_connectivity


#ifdef HAVE_TECIO
!========================== TECPLOT_CONNECTIVITY_BINARY ======================80
!
! Sets up a cell-to-node connectivity array for binary tecplot volume data
!
!=============================================================================80

  subroutine tecplot_connectivity_binary( type_cell, ncell, node_per_cell, c2n,&
                                          tets_only, c2n_temp, node_dim,       &
                                          ncell_tot, ielem )

    logical,                                 intent(in)    :: tets_only
    character(3),                            intent(in)    :: type_cell
    integer,                                 intent(in)    :: ncell
    integer,                                 intent(in)    :: node_per_cell
    integer,                                 intent(in)    :: node_dim
    integer,                                 intent(in)    :: ncell_tot
    integer,                                 intent(in)    :: ielem
    integer, dimension(node_per_cell,ncell), intent(in)    :: c2n
    integer, dimension(node_dim,ncell_tot),  intent(inout) :: c2n_temp

    integer       :: i
    integer, save :: count_cell

  continue

!   Uses tecplot "tetrahedron" style for pure tet grids, and the more general
!   tecplot "brick" style for mixed-elements or non-tet elements (note that if
!   a mixed element grid has tets in it, the tets are written out in the
!   less-compact "brick" style)

    if (tets_only) then

!     only tets, can use tecplot tetrahedron element format
!     default nodal order is correct for tecplot

      do i = 1,ncell
        c2n_temp(1:4,i) = c2n(1:4,i)
      end do

    else

      if (ielem == 1) then    ! first element type of mixed-element mesh
        count_cell = 0
      end if

      select case (type_cell)

        case('tet')

        do i = 1,ncell
          count_cell = count_cell + 1
          c2n_temp(1,count_cell) = c2n(1,i)
          c2n_temp(2,count_cell) = c2n(2,i)
          c2n_temp(3,count_cell) = c2n(3,i)
          c2n_temp(4,count_cell) = c2n(3,i)
          c2n_temp(5,count_cell) = c2n(4,i)
          c2n_temp(6,count_cell) = c2n(4,i)
          c2n_temp(7,count_cell) = c2n(4,i)
          c2n_temp(8,count_cell) = c2n(4,i)
        end do

        case('hex')

        do i = 1,ncell
          count_cell = count_cell + 1
          c2n_temp(1,count_cell) = c2n(1,i)
          c2n_temp(2,count_cell) = c2n(2,i)
          c2n_temp(3,count_cell) = c2n(4,i)
          c2n_temp(4,count_cell) = c2n(3,i)
          c2n_temp(5,count_cell) = c2n(5,i)
          c2n_temp(6,count_cell) = c2n(6,i)
          c2n_temp(7,count_cell) = c2n(8,i)
          c2n_temp(8,count_cell) = c2n(7,i)
        end do

        case('prz')

        do i = 1,ncell
          count_cell = count_cell + 1
          c2n_temp(1,count_cell) = c2n(5,i)
          c2n_temp(2,count_cell) = c2n(2,i)
          c2n_temp(3,count_cell) = c2n(1,i)
          c2n_temp(4,count_cell) = c2n(6,i)
          c2n_temp(5,count_cell) = c2n(3,i)
          c2n_temp(6,count_cell) = c2n(3,i)
          c2n_temp(7,count_cell) = c2n(4,i)
          c2n_temp(8,count_cell) = c2n(4,i)
        end do

        case('pyr')

        do i = 1,ncell
          count_cell = count_cell + 1
          c2n_temp(1,count_cell) = c2n(1,i)
          c2n_temp(2,count_cell) = c2n(2,i)
          c2n_temp(3,count_cell) = c2n(3,i)
          c2n_temp(4,count_cell) = c2n(4,i)
          c2n_temp(5,count_cell) = c2n(5,i)
          c2n_temp(6,count_cell) = c2n(5,i)
          c2n_temp(7,count_cell) = c2n(5,i)
          c2n_temp(8,count_cell) = c2n(5,i)
        end do

        case default

          write(*,'(3a)') ' Element type ', type_cell, ' not yet supported ',  &
                          ' in tecplot_connectivity_binary  routine'

      end select

    end if

  end subroutine tecplot_connectivity_binary
#endif


!========================== SET_BOUNDARIES_TO_ANIMATE ========================80
!
!  Determine which boundaries we will output for animation
!
!=============================================================================80

  subroutine set_boundaries_to_animate( echo, nbound, output_bndry,            &
                                        bc, nnodes01, y, ignore_twod_option )

    use bc_types,            only : bcgrid_type
    use lmpi,                only : lmpi_master, lmpi_conditional_stop, lmpi_die
    use file_utils,          only : file_exists
    use string_utils,        only : list_to_array
    use io_helpers,          only : set_default_boundaries
    use nml_boundary_output, only : number_of_boundaries, boundary_list

    integer,                                intent(in)    :: nbound
    logical,           dimension(nbound),   intent(inout) :: output_bndry
    type(bcgrid_type), dimension(nbound),   intent(inout) :: bc
    integer,                                intent(in)    :: nnodes01
    real(dp),          dimension(nnodes01), intent(in)    :: y
    logical,           optional,            intent(in)    :: ignore_twod_option
    logical,                                intent(in)    :: echo

    integer, dimension(:), pointer :: boundaries_found

    integer :: ib, istop, j, nbndry_to_read

  continue

!   check for no-longer-supported magic file input; now via namelist

    if ( file_exists('boundaries_to_animate') ) then
      if(lmpi_master) write(*,*) "File boundaries_to_animate found--stopping"
      if(lmpi_master) write(*,*) "Use &boundary_output namelist in fun3d.nml"
      call lmpi_die
    end if

!   set the defaults

    call set_default_boundaries(nbound, output_bndry, bc, nnodes01, y,         &
                                ignore_twod_option)

!   get the users choice of boundaries, if set

    user_set_boundaries : if ( number_of_boundaries > 0 .or. &
                               number_of_boundaries == -1 ) then

!     the user has set some boundaries, so wipe the defaults

      output_bndry(:) = .false.

      call list_to_array( boundary_list, boundaries_found )

      if ( number_of_boundaries == -1 ) then
        number_of_boundaries = size(boundaries_found)
      end if

      nbndry_to_read = number_of_boundaries

      if ( lmpi_master .and. size(boundaries_found) /= nbndry_to_read ) then
        write(*,*) 'Troubles parsing boundary_list...'
        echo_boundaries_found : do j = 1, size(boundaries_found)
          write(*,*) j, ':',  boundaries_found(j)
        end do echo_boundaries_found
        write(*,*) trim(boundary_list)
        write(*,*) 'number_of_boundaries = ', number_of_boundaries
        write(*,*) 'boundary_list size = ', size(boundaries_found)
        write(*,'(2a)') ' Character string boundary_list too short',           &
                'in namelist file read for requested number of boundaries.'
        call lmpi_conditional_stop(1,'set_boundaries_to_animate')
      end if

      if ( lmpi_master .and. echo ) then
        write(*,*) 'Number_of_boundaries = ', number_of_boundaries
        write(*,*) 'Specified from parsing character array boundary_list:'
        write(*,*) trim(boundary_list)
        write(*,"(1x,a,a)") ' Boundary-to-Animate','     Boundary Number'
        !                    12345678901234567890   12345678901234567890
        do j = 1, size(boundaries_found)
          write(*,"(1x,2i10)") j,boundaries_found(j)
        end do
      endif

      istop = 0

      boundaries: do j = 1, size(boundaries_found)

        ib = boundaries_found(j)

        if ( ib < 1 .or. ib > nbound ) then
          if (lmpi_master) then
            istop = istop + 1
            write(*,'(2a,i0)') ' Out-of-bounds boundary number specified ',    &
                     'in namelist boundary_output_variables: ', ib
            if (istop == 1) then
              write(*,'(a,i0)') ' Note: valid range is 1 to ', nbound
              write(*,'(2a)') ' Perhaps you lumped boundaries and',            &
                              ' did not account for the change in numbering'
              write(*,'(2a)') ' Or perhaps you are using mirroring and ',      &
                                ' did not account for the change in numbering.'
            end if
          end if
        else
          output_bndry(ib) = .true.
        end if

      end do boundaries

      deallocate(boundaries_found)

      call lmpi_conditional_stop(istop)

    end if user_set_boundaries

  end subroutine set_boundaries_to_animate


!========================== SET_BOUNDARIES_TO_OUTPUT =========================80
!
!  Determine which boundaries we will output [general purpose]
!
!  This is intended to be interim code to support old style user overrides via
!  the existance of a magic file. Some things still use the magic file approach,
!  but all eventually should migrate to the namelist style way of specifying
!  such things (as exemplified by subroutine set_boundaries_to_animate)
!
!=============================================================================80

  subroutine set_boundaries_to_output(nbound, output_bndry, bc, nnodes01, y,   &
                                      filename, ignore_twod_option)

    use bc_types,   only : bcgrid_type
    use lmpi,       only : lmpi_master, lmpi_conditional_stop
    use file_utils, only : file_exists
    use io_helpers, only : set_default_boundaries

    integer,                                intent(in)    :: nbound
    logical,           dimension(nbound),   intent(inout) :: output_bndry
    type(bcgrid_type), dimension(nbound),   intent(inout) :: bc
    integer,                                intent(in)    :: nnodes01
    real(dp),          dimension(nnodes01), intent(in)    :: y
    character(*),                           intent(in)    :: filename
    logical,           optional,            intent(in)    :: ignore_twod_option

    integer :: ib, istop, j, iu, nbndry_to_read

  continue

!   set the defaults

    call set_default_boundaries(nbound, output_bndry, bc, nnodes01, y,         &
                                ignore_twod_option)

!   if the user has chosen to specify boundaries explicitly, use them

    user_file_exists : if ( file_exists(filename) ) then

!     user file exists, so reset the default list

      output_bndry(:) = .false.

!     read the file to get the users choice

      iu = 90

      open(unit=iu,file=filename,status='old')

      rewind(iu)

      read(iu,*)
      read(iu,*)
      read(iu,*) nbndry_to_read
      read(iu,*)

      istop = 0

      do j=1,nbndry_to_read

        read(iu,*) ib

!       check for illegal boundary number

        if (ib < 1 .or. ib > nbound) then
          if (lmpi_master) then
            istop = istop + 1
            write(*,'(4a,i4)') 'Out-of-bounds boundary number specified ',     &
                       'in ', trim(filename), ': ', ib
            if (istop == 1) then
              write(*,'(a,i4)') 'Note: valid range is 1 to', nbound
              write(*,'(2a)') 'Perhaps you lumped boundaries and ',            &
                              'did not account for the change in numbering'
              write(*,'(2a)') 'Or perhaps you are using mirroring and ',       &
                              'did not account for the change in numbering.'
            end if
          end if
        else
          output_bndry(ib) = .true.
        end if

      end do

      close(iu)

      call lmpi_conditional_stop(istop)

    end if user_file_exists

  end subroutine set_boundaries_to_output


#ifdef HAVE_TECIO
!================================= MY_TECINI =================================80
!
! Interface function to deal with newer tecio.a versions
!
!=============================================================================80

  integer function my_tecini(file_title,                                       &
                             variable_list,                                    &
                             filename,                                         &
                             scratchdir,                                       &
                             filetype)

    character(*), intent(in) :: file_title, filename
    character(*), intent(in) :: scratchdir, variable_list
    integer,      intent(in) :: filetype

#ifdef HAVE_TECIO_2008
    integer :: tecini111
#else
    integer :: tecini
#endif

  continue

#ifdef HAVE_TECIO_2008

    my_tecini = tecini111(trim(adjustl(file_title))//nullchar,               &
                          trim(adjustl(variable_list))//nullchar,            &
                          trim(adjustl(filename))//nullchar,                 &
                          trim(adjustl(scratchdir))//nullchar,               &
                          filetype,                                          &
                          debug,                                             &
                          isdouble)

#else

    if (.false.) write(*,*) filetype ! to suppress compiler warning

    my_tecini = tecini(trim(adjustl(file_title))//nullchar,                  &
                       trim(adjustl(variable_list))//nullchar,               &
                       trim(adjustl(filename))//nullchar,                    &
                       trim(adjustl(scratchdir))//nullchar,                  &
                       debug,                                                &
                       isdouble)

#endif

  end function my_tecini


!================================= MY_TECZNE =================================80
!
! Interface function to deal with newer tecio.a versions
!
!=============================================================================80

  integer function my_teczne(zone_title,                                       &
                             nnodes,                                           &
                             ncells,                                           &
                             zone_type,                                        &
                             isblock,                                          &
                             nvars,                                            &
                             valuelocation,                                    &
                             strandid,                                         &
                             time_val)

    character(*),              intent(in) :: zone_title
    integer,                   intent(in) :: nnodes, ncells, zone_type
    integer,                   intent(in) :: isblock, nvars, strandid
    integer, dimension(nvars), intent(in) :: valuelocation
    real(dp),                  intent(in) :: time_val

#ifdef HAVE_TECIO_2008
    integer :: numfaces, ICellMax, JCellMax, KCellMax, parentzone
    integer :: numfaceconnections, faceneighbormode, totalnumfacenodes
    integer :: numconnectedboundaryfaces, shareconnectivityfromzone
    integer :: totalnumboundaryconnections, teczne111
    integer :: IMax, JMax, KMax

    integer, dimension(nvars) :: passivevarlist
    integer, dimension(nvars) :: sharvarfromzone
#else
    integer :: my_zone_type
    integer :: teczne
#endif

  continue

#ifdef HAVE_TECIO_2008

!   set some parameters that are either reserved for future use or are
!   not apropo for fun3d
    if (zone_type /= 0) then          ! ordered

    numfaces                    = 0
    icellmax                    = 0
    jcellmax                    = 0
    kcellmax                    = 0
    parentzone                  = 0
    faceneighbormode            = 0
    numfaceconnections          = 0
    totalnumfacenodes           = 0
    numconnectedboundaryfaces   = 0
    totalnumboundaryconnections = 0
    passivevarlist(:)           = 0
    shareconnectivityfromzone   = 0
    sharvarfromzone(:)          = 0


    my_teczne = teczne111(trim(adjustl(zone_title))//nullchar,                 &
                          zone_type,                                           &
                          nnodes,                                              &
                          ncells,                                              &
                          numfaces,                                            &
                          icellmax,                                            &
                          jcellmax,                                            &
                          kcellmax,                                            &
                          time_val,                                            &
                          strandid,                                            &
                          parentzone,                                          &
                          isblock,                                             &
                          numfaceconnections,                                  &
                          faceneighbormode,                                    &
                          totalnumfacenodes,                                   &
                          numconnectedboundaryfaces,                           &
                          totalnumboundaryconnections,                         &
                          passivevarlist,                                      &
                          valuelocation,                                       &
                          sharvarfromzone,                                     &
                          shareconnectivityfromzone)

    elseif (zone_type == 0) then          ! ordered

      IMax                        = nnodes ! number of rows
      JMax                        = ncells ! number of columns
      KMax                        = 1
      ICellMax                    = 0
      JCellMax                    = 0
      KCellMax                    = 0
      parentzone                  = 0
      faceneighbormode            = 0
      numfaceconnections          = 0
      TotalNumFaceNodes           = 0
      numconnectedboundaryfaces   = 0
      TotalNumBoundaryConnections = 0
      passivevarlist(:)           = 0
      shareconnectivityfromzone   = 0
      sharvarfromzone(:)          = 0


      my_teczne = teczne111(trim(adjustl(zone_title))//nullchar,               &
                            zone_type,                                         &
                            IMax,                                              &
                            JMax,                                              &
                            KMax,                                              &
                            ICellMax,                                          &
                            JCellMax,                                          &
                            KCellMax,                                          &
                            time_val,                                          &
                            strandid,                                          &
                            parentzone,                                        &
                            isblock,                                           &
                            numfaceconnections,                                &
                            faceneighbormode,                                  &
                            TotalNumFaceNodes,                                 &
                            numconnectedboundaryfaces,                         &
                            TotalNumBoundaryConnections,                       &
                            passivevarlist,                                    &
                            valuelocation,                                     &
                            sharvarfromzone,                                   &
                            shareconnectivityfromzone)

      endif
#else

    if (.false.) write(*,*) nvars, isblock  ! to suppress compiler warning

    if (zone_type == 0) then          ! ordered
      my_zone_type = 0                   ! old-style ordered data
    else if (zone_type == 2) then     ! fetriangle
      my_zone_type = 1                   ! old-style quad flag (node4=0 in tri)
    else if (zone_type == 3) then     ! fequadrilateral
      my_zone_type = 1                   ! old-style quad flag
    else if (zone_type == 4) then     ! fetetrahedron
      my_zone_type = 2                   ! old-style tet flag
    else if (zone_type == 5) then     ! febrick
      my_zone_type = 3                   ! old-style brick flag
    else
      my_zone_type = 3
    end if

    if ( zone_type == 0 ) then
      my_teczne = teczne(trim(adjustl(zone_title))//nullchar,                  &
                         nnodes,                                               &
                         ncells,                                               &
                         my_zone_type,                                         &
                         'BLOCK'//nullchar,                                    &
                         nullchar)
    else
    my_teczne = teczne(trim(adjustl(zone_title))//nullchar,                    &
                       nnodes,                                                 &
                       ncells,                                                 &
                       my_zone_type,                                           &
                       'FEBLOCK'//nullchar,                                    &
                       nullchar)
    endif

#endif

  end function my_teczne

#endif


end module tecplot_io_helpers

!============================== CROSS_PRODUCT ================================80
!
! Returns a vector cross product, given two vectors
!
!=============================================================================80

  pure function cross_product( v1, v2 )

    use kinddefs, only : dp

    real(dp), dimension(3), intent(in) :: v1, v2
    real(dp), dimension(3)             :: cross_product

    continue

    cross_product(1) = v1(2)*v2(3) - v1(3)*v2(2)
    cross_product(2) = v1(3)*v2(1) - v1(1)*v2(3)
    cross_product(3) = v1(1)*v2(2) - v1(2)*v2(1)

  end function cross_product

module djet

  implicit none

  private

  public :: sensjet

contains

!=================================== SENSJET =================================80
!
! This routine linearizes the jet BC wrt the jet frequency, velocity, and phase
!
!=============================================================================80
  subroutine sensjet(grid,soln,sadj,design,dLdx,physical_timestep,subiteration)

    use info_depr,                only : mixed, simulation_time
    use nml_nonlinear_solves,     only : itime
    use grid_motion_helpers,      only : need_grid_velocity
    use kinddefs,                 only : dp
    use bc_names,                 only : pulsed_subsonic_inflow_rho
    use fluid,                    only : gm1
    use fun3d_constants,          only : my_0, my_4th, my_half, my_1, my_6,    &
                                         my_3rd, pi, my_8
    use lmpi,                     only : lmpi_die, lmpi_master, lmpi_reduce,   &
                                         lmpi_bcast
    use nml_boundary_conditions,  only : q_set, freq, phase
    use allocations,              only : my_alloc_ptr
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type
    use solution_adj,             only : sadj_type
    use design_types,             only : design_type, max_functions
    use thermo,                   only : etop, ptoe

    integer, intent(in) :: physical_timestep, subiteration

    type(grid_type),   intent(in)    :: grid
    type(sadj_type),   intent(in)    :: sadj
    type(design_type), intent(in)    :: design
    type(soln_type),   intent(inout) :: soln

    real(dp), dimension(design%nfunctions,10,3), intent(out) :: dLdX

    integer :: triangle_index,triangle_corner
    integer :: j,k,face_node,qi,n1,ib,ijet,n

    integer, dimension(3) :: nde

    real(dp) :: area,rho
    real(dp) :: xnorm,ynorm,znorm
    real(dp) :: xc, yc, zc, xm, ym, zm, xl, yl, zl, xr, yr, zr
    real(dp) :: axn, ayn, azn, umag, face_speed, rhol, rhor, c, ubar
    real(dp) :: ul, vl, wl, ur, vr, wr, pressl, pressr, u, v, w, H, q2
    real(dp) :: q2l, q2r, enrgyl, enrgyr, Hl, Hr, ubarl, ubarr, wat
    real(dp) :: areaf, areav, areap, ubar_fsl, ubar_fsr, ubar_fs
    real(dp) :: eig1, eig2, eig3, drho, du, dv, dw, dpress, dubar, c2
    real(dp) :: dv1, dv2, dv3, dv4, t, fluxp, fluxm
    real(dp) :: r21,r31,r41,r51
    real(dp) :: r22,r32,r42,r52
    real(dp) :: r23,r33,r43,r53
    real(dp) :: r24,r34,r44,r54
    real(dp) :: xnormf, xnormv, xnormp
    real(dp) :: ynormf, ynormv, ynormp
    real(dp) :: znormf, znormv, znormp
    real(dp) :: umagf, umagv, umagp
    real(dp) :: qr1f,qr1v,qr1p
    real(dp) :: qr2f,qr2v,qr2p
    real(dp) :: qr3f,qr3v,qr3p
    real(dp) :: qr4f,qr4v,qr4p
    real(dp) :: qr5f,qr5v,qr5p
    real(dp) :: rholf,rholv,rholp
    real(dp) :: ulf,ulv,ulp
    real(dp) :: vlf,vlv,vlp
    real(dp) :: wlf,wlv,wlp
    real(dp) :: presslf,presslv,presslp
    real(dp) :: enrgylf,enrgylv,enrgylp
    real(dp) :: Hlf,Hlv,Hlp
    real(dp) :: ubarlf,ubarlv,ubarlp
    real(dp) :: rhorf,rhorv,rhorp
    real(dp) :: urf,urv,urp
    real(dp) :: vrf,vrv,vrp
    real(dp) :: wrf,wrv,wrp
    real(dp) :: pressrf,pressrv,pressrp
    real(dp) :: enrgyrf,enrgyrv,enrgyrp
    real(dp) :: Hrf,Hrv,Hrp
    real(dp) :: ubarrf,ubarrv,ubarrp
    real(dp) :: q2rf,q2rv,q2rp
    real(dp) :: rhof,rhov,rhop
    real(dp) :: watf,watv,watp
    real(dp) :: uf,uv,up
    real(dp) :: vvf,vvv,vvp
    real(dp) :: wf,wv,wp
    real(dp) :: Hf,Hv,Hp
    real(dp) :: q2f,q2v,q2p
    real(dp) :: cf,cv,cp
    real(dp) :: ubarf,ubarv,ubarp
    real(dp) :: ubar_fslf,ubar_fslv,ubar_fslp
    real(dp) :: ubar_fsrf,ubar_fsrv,ubar_fsrp
    real(dp) :: ubar_fsf,ubar_fsv,ubar_fsp
    real(dp) :: eig1f,eig1v,eig1p
    real(dp) :: eig2f,eig2v,eig2p
    real(dp) :: eig3f,eig3v,eig3p
    real(dp) :: drhof,drhov,drhop
    real(dp) :: dpressf,dpressv,dpressp
    real(dp) :: duf,duv,dup
    real(dp) :: dvf,dvv,dvp
    real(dp) :: dwf,dwv,dwp
    real(dp) :: dubarf,dubarv,dubarp
    real(dp) :: c2f,c2v,c2p
    real(dp) :: dv1f,dv1v,dv1p
    real(dp) :: dv2f,dv2v,dv2p
    real(dp) :: dv3f,dv3v,dv3p
    real(dp) :: dv4f,dv4v,dv4p
    real(dp) :: r21f,r21v,r21p
    real(dp) :: r31f,r31v,r31p
    real(dp) :: r41f,r41v,r41p
    real(dp) :: r51f,r51v,r51p
    real(dp) :: r22f,r22v,r22p
    real(dp) :: r32f,r32v,r32p
    real(dp) :: r42f,r42v,r42p
    real(dp) :: r52f,r52v,r52p
    real(dp) :: r23f,r23v,r23p
    real(dp) :: r33f,r33v,r33p
    real(dp) :: r43f,r43v,r43p
    real(dp) :: r53f,r53v,r53p
    real(dp) :: r24f,r24v,r24p
    real(dp) :: r34f,r34v,r34p
    real(dp) :: r44f,r44v,r44p
    real(dp) :: r54f,r54v,r54p
    real(dp) :: tf,tv,tp
    real(dp) :: fluxpf,fluxpv,fluxpp
    real(dp) :: fluxmf,fluxmv,fluxmp
    real(dp) :: resf,resv,resp
    real(dp) :: tempvar

    real(dp), dimension(max_functions)  :: lambda
    real(dp), dimension(3)              :: weight
    real(dp), dimension(5)              :: ql,qr
    real(dp), dimension(:,:,:), pointer :: productD

  continue

    call my_alloc_ptr(productD,design%nfunctions,10,3)

    dLdX     = 0.0_dp
    productD = 0.0_dp

    call etop(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)

    ijet = 0

    bound_loop : do ib = 1, grid%nbound

     bc_7030 : if ( grid%bc(ib)%ibc == pulsed_subsonic_inflow_rho ) then

      ijet = ijet + 1

      if ( grid%bc(ib)%nbfaceq > 0 ) then
       write(*,*)'djet_quantities not coded for BC 7030 with quads'
       call lmpi_die; stop
      endif

      if ( need_grid_velocity ) then
       write(*,*)'djet_quantities not coded for BC 7030 with moving grid/nonin'
       call lmpi_die; stop
      endif

      if ( mixed ) then
       write(*,*)'djet_quantities not coded for BC 7030 w/elems other than tets'
       call lmpi_die; stop
      endif

      loop_tris : do triangle_index = 1, grid%bc(ib)%nbfacet

        if ( grid%bc(ib)%face_bit(triangle_index) /= 1 ) cycle loop_tris

        corner_tris_loop : do triangle_corner = 1,3

          nde(1) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(triangle_index,        &
                                                  mod(triangle_corner+0-1,3)+1))
          nde(2) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(triangle_index,        &
                                                  mod(triangle_corner+1-1,3)+1))
          nde(3) = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(triangle_index,        &
                                                  mod(triangle_corner+2-1,3)+1))

          xc = (grid%x(nde(1)) + grid%x(nde(2)) + grid%x(nde(3)))*my_3rd
          yc = (grid%y(nde(1)) + grid%y(nde(2)) + grid%y(nde(3)))*my_3rd
          zc = (grid%z(nde(1)) + grid%z(nde(2)) + grid%z(nde(3)))*my_3rd

          xm = grid%x(nde(1))
          ym = grid%y(nde(1))
          zm = grid%z(nde(1))

          xl = (grid%x(nde(1)) + grid%x(nde(3)))*my_half
          yl = (grid%y(nde(1)) + grid%y(nde(3)))*my_half
          zl = (grid%z(nde(1)) + grid%z(nde(3)))*my_half

          xr = (grid%x(nde(1)) + grid%x(nde(2)))*my_half
          yr = (grid%y(nde(1)) + grid%y(nde(2)))*my_half
          zr = (grid%z(nde(1)) + grid%z(nde(2)))*my_half

          axn = -my_4th*( (yc-ym)*(zl-zr) - (zc-zm)*(yl-yr)                    &
                        + (yr-yl)*(zc-zm) - (zr-zl)*(yc-ym) )
          ayn = -my_4th*( (zc-zm)*(xl-xr) - (xc-xm)*(zl-zr)                    &
                        + (zr-zl)*(xc-xm) - (xr-xl)*(zc-zm) )
          azn = -my_4th*( (xc-xm)*(yl-yr) - (yc-ym)*(xl-xr)                    &
                        + (xr-xl)*(yc-ym) - (yr-yl)*(xc-xm) )

          area = sqrt(axn*axn+ayn*ayn+azn*azn)
            areaf = 0.0_dp
            areav = 0.0_dp
            areap = 0.0_dp

          xnorm = axn/area
            xnormf = 0.0_dp
            xnormv = 0.0_dp
            xnormp = 0.0_dp
          ynorm = ayn/area
            ynormf = 0.0_dp
            ynormv = 0.0_dp
            ynormp = 0.0_dp
          znorm = azn/area
            znormf = 0.0_dp
            znormv = 0.0_dp
            znormp = 0.0_dp

          weight(1) = my_6/my_8
          weight(2) = my_1/my_8
          weight(3) = my_1/my_8

! compute left (internal) state

          ql(1:5) = my_0
          do face_node = 1, 3
            ql(:) = ql(:) + weight(face_node)*soln%q_dof(:,nde(face_node))
          end do

! compute right (external) state: specified rho/velocities, energy from inside

          if ( abs(q_set(ib,3))>=epsilon(umag) .or.                            &
               abs(q_set(ib,4))>=epsilon(umag) ) then
            write(*,*) 'Error: all of jet velocity expected in u-component!'
            call lmpi_die; stop
          endif

! start taking partials wrt frequency, velocity, and phase ("f", "v", and "p")

          umag = q_set(ib,2)*sin(freq(ib)*simulation_time+phase(ib)*pi/180.0_dp)
            umagf = q_set(ib,2)*cos(freq(ib)*simulation_time                   &
                                        + phase(ib)*pi/180.0_dp)*simulation_time
            umagv = sin(freq(ib)*simulation_time + phase(ib)*pi/180.0_dp)
            umagp = q_set(ib,2)*cos(freq(ib)*simulation_time                   &
                                            + phase(ib)*pi/180.0_dp)*pi/180.0_dp

          if ( umag <= 0.0_dp ) then
            umag = 0.0_dp
              umagf = 0.0_dp
              umagv = 0.0_dp
              umagp = 0.0_dp
          endif

          qr(1) = q_set(ib,1)
            qr1f = 0.0_dp
            qr1v = 0.0_dp
            qr1p = 0.0_dp
          qr(2) = -xnorm * umag
            qr2f = -xnorm*umagf
            qr2v = -xnorm*umagv
            qr2p = -xnorm*umagp
          qr(3) = -ynorm * umag
            qr3f = -ynorm*umagf
            qr3v = -ynorm*umagv
            qr3p = -ynorm*umagp
          qr(4) = -znorm * umag
            qr4f = -znorm*umagf
            qr4v = -znorm*umagv
            qr4p = -znorm*umagp
          qr(5) = ql(5)
            qr5f = 0.0_dp
            qr5v = 0.0_dp
            qr5p = 0.0_dp

! linearize the flux

          face_speed = my_0

! Get variables on "left" side of face

          rhol   = ql(1)
            rholf = 0.0_dp
            rholv = 0.0_dp
            rholp = 0.0_dp

          ul     = ql(2)
            ulf = 0.0_dp
            ulv = 0.0_dp
            ulp = 0.0_dp

          vl     = ql(3)
            vlf = 0.0_dp
            vlv = 0.0_dp
            vlp = 0.0_dp

          wl     = ql(4)
            wlf = 0.0_dp
            wlv = 0.0_dp
            wlp = 0.0_dp

          pressl = ql(5)
            presslf = 0.0_dp
            presslv = 0.0_dp
            presslp = 0.0_dp

          q2l    = ul*ul + vl*vl + wl*wl

          enrgyl = pressl/gm1 + .5_dp*rhol*q2l
            enrgylf = 0.0_dp
            enrgylv = 0.0_dp
            enrgylp = 0.0_dp

          Hl     = (enrgyl + pressl)/rhol
            Hlf = 0.0_dp
            Hlv = 0.0_dp
            Hlp = 0.0_dp

          ubarl  = xnorm*ul + ynorm*vl + znorm*wl
           ubarlf = 0.0_dp
           ubarlv = 0.0_dp
           ubarlp = 0.0_dp

! Get variables on "right" side of face

          rhor   = qr(1)
            rhorf = qr1f
            rhorv = qr1v
            rhorp = qr1p

          ur     = qr(2)
            urf = qr2f
            urv = qr2v
            urp = qr2p

          vr     = qr(3)
            vrf = qr3f
            vrv = qr3v
            vrp = qr3p

          wr     = qr(4)
            wrf = qr4f
            wrv = qr4v
            wrp = qr4p

          pressr = qr(5)
            pressrf = qr5f
            pressrv = qr5v
            pressrp = qr5p

          q2r    = ur*ur + vr*vr + wr*wr
            q2rf = 2.0_dp*ur*urf + 2.0_dp*vr*vrf + 2.0_dp*wr*wrf
            q2rv = 2.0_dp*ur*urv + 2.0_dp*vr*vrv + 2.0_dp*wr*wrv
            q2rp = 2.0_dp*ur*urp + 2.0_dp*vr*vrp + 2.0_dp*wr*wrp

          enrgyr = pressr/gm1 + .5_dp*rhor*q2r
            enrgyrf = pressrf/gm1 + 0.5_dp*(rhor*q2rf + q2r*rhorf)
            enrgyrv = pressrv/gm1 + 0.5_dp*(rhor*q2rv + q2r*rhorv)
            enrgyrp = pressrp/gm1 + 0.5_dp*(rhor*q2rp + q2r*rhorp)

          Hr     = (enrgyr + pressr)/rhor
            Hrf = (rhor*(enrgyrf+pressrf) - (enrgyr+pressr)*rhorf) / rhor / rhor
            Hrv = (rhor*(enrgyrv+pressrv) - (enrgyr+pressr)*rhorv) / rhor / rhor
            Hrp = (rhor*(enrgyrp+pressrp) - (enrgyr+pressr)*rhorp) / rhor / rhor

          ubarr  = xnorm*ur + ynorm*vr + znorm*wr
           ubarrf = xnorm*urf + ur*xnormf + ynorm*vrf + vr*ynormf              &
                  + znorm*wrf + wr*znormf
           ubarrv = xnorm*urv + ur*xnormv + ynorm*vrv + vr*ynormv              &
                  + znorm*wrv + wr*znormv
           ubarrp = xnorm*urp + ur*xnormp + ynorm*vrp + vr*ynormp              &
                  + znorm*wrp + wr*znormp

! Compute rho averages

          rho = sqrt(rhol*rhor)
           rhof = 0.5_dp / sqrt(rhol*rhor) * (rhol*rhorf + rhor*rholf)
           rhov = 0.5_dp / sqrt(rhol*rhor) * (rhol*rhorv + rhor*rholv)
           rhop = 0.5_dp / sqrt(rhol*rhor) * (rhol*rhorp + rhor*rholp)

          wat = rho/(rho + rhor)
            watf = ((rho + rhor)*rhof - rho*(rhof + rhorf))                    &
                  / (rho + rhor) / (rho + rhor)
            watv = ((rho + rhor)*rhov - rho*(rhov + rhorv))                    &
                  / (rho + rhor) / (rho + rhor)
            watp = ((rho + rhor)*rhop - rho*(rhop + rhorp))                    &
                  / (rho + rhor) / (rho + rhor)

          u   = ul*wat + ur*(1.0_dp - wat)
            uf = ul*watf + wat*ulf + ur*(-watf) + (1.0_dp-wat)*urf
            uv = ul*watv + wat*ulv + ur*(-watv) + (1.0_dp-wat)*urv
            up = ul*watp + wat*ulp + ur*(-watp) + (1.0_dp-wat)*urp

          v   = vl*wat + vr*(1.0_dp - wat)
            vvf = vl*watf + wat*vlf + vr*(-watf) + (1.0_dp-wat)*vrf
            vvv = vl*watv + wat*vlv + vr*(-watv) + (1.0_dp-wat)*vrv
            vvp = vl*watp + wat*vlp + vr*(-watp) + (1.0_dp-wat)*vrp

          w   = wl*wat + wr*(1.0_dp - wat)
            wf = wl*watf + wat*wlf + wr*(-watf) + (1.0_dp-wat)*wrf
            wv = wl*watv + wat*wlv + wr*(-watv) + (1.0_dp-wat)*wrv
            wp = wl*watp + wat*wlp + wr*(-watp) + (1.0_dp-wat)*wrp

          H   = Hl*wat + Hr*(1.0_dp - wat)
            Hf = Hl*watf + wat*Hlf + Hr*(-watf) + (1.0_dp-wat)*Hrf
            Hv = Hl*watv + wat*Hlv + Hr*(-watv) + (1.0_dp-wat)*Hrv
            Hp = Hl*watp + wat*Hlp + Hr*(-watp) + (1.0_dp-wat)*Hrp

          q2  = u*u + v*v + w*w
            q2f = 2.0_dp*u*uf + 2.0_dp*v*vvf + 2.0_dp*w*wf
            q2v = 2.0_dp*u*uv + 2.0_dp*v*vvv + 2.0_dp*w*wv
            q2p = 2.0_dp*u*up + 2.0_dp*v*vvp + 2.0_dp*w*wp

          c   = sqrt(gm1*(H - 0.5_dp*q2))
            cf = 0.5_dp / sqrt(gm1*(H - 0.5_dp*q2)) * gm1*(Hf - 0.5_dp*q2f)
            cv = 0.5_dp / sqrt(gm1*(H - 0.5_dp*q2)) * gm1*(Hv - 0.5_dp*q2v)
            cp = 0.5_dp / sqrt(gm1*(H - 0.5_dp*q2)) * gm1*(Hp - 0.5_dp*q2p)

          ubar = xnorm*u + ynorm*v + znorm*w
            ubarf = xnorm*uf + u*xnormf + ynorm*vvf + v*ynormf            &
                  + znorm*wf + w*znormf
            ubarv = xnorm*uv + u*xnormv + ynorm*vvv + v*ynormv            &
                  + znorm*wv + w*znormv
            ubarp = xnorm*up + u*xnormp + ynorm*vvp + v*ynormp            &
                  + znorm*wp + w*znormp

!   Add normal face speed to the contravariant velocity terms

          ubar_fsl = ubarl - face_speed
            ubar_fslf = ubarlf
            ubar_fslv = ubarlv
            ubar_fslp = ubarlp

          ubar_fsr = ubarr - face_speed
            ubar_fsrf = ubarrf
            ubar_fsrv = ubarrv
            ubar_fsrp = ubarrp

          ubar_fs = ubar - face_speed
            ubar_fsf = ubarf
            ubar_fsv = ubarv
            ubar_fsp = ubarp

! Now compute eigenvalues, eigenvectors, and strengths

          eig1 = abs(ubar_fs + c)
            if((ubar_fs+c)>0.0_dp) then
              eig1f = ubar_fsf + cf
              eig1v = ubar_fsv + cv
              eig1p = ubar_fsp + cp
            else
              eig1f = - (ubar_fsf + cf)
              eig1v = - (ubar_fsv + cv)
              eig1p = - (ubar_fsp + cp)
            endif

          eig2 = abs(ubar_fs - c)
            if((ubar_fs-c)>0.0_dp) then
              eig2f = ubar_fsf - cf
              eig2v = ubar_fsv - cv
              eig2p = ubar_fsp - cp
            else
              eig2f = - (ubar_fsf - cf)
              eig2v = - (ubar_fsv - cv)
              eig2p = - (ubar_fsp - cp)
            endif

          eig3 = abs(ubar_fs)
            if(ubar_fs>0.0_dp) then
              eig3f = ubar_fsf
              eig3v = ubar_fsv
              eig3p = ubar_fsp
            else
              eig3f = - ubar_fsf
              eig3v = - ubar_fsv
              eig3p = - ubar_fsp
            endif

          drho   = rhor - rhol
            drhof = rhorf - rholf
            drhov = rhorv - rholv
            drhop = rhorp - rholp

          dpress = pressr - pressl
            dpressf = pressrf - presslf
            dpressv = pressrv - presslv
            dpressp = pressrp - presslp

          du     = ur - ul
            duf = urf - ulf
            duv = urv - ulv
            dup = urp - ulp

          dv     = vr - vl
            dvf = vrf - vlf
            dvv = vrv - vlv
            dvp = vrp - vlp

          dw     = wr - wl
            dwf = wrf - wlf
            dwv = wrv - wlv
            dwp = wrp - wlp

          dubar  = ubarr - ubarl
            dubarf = ubarrf - ubarlf
            dubarv = ubarrv - ubarlv
            dubarp = ubarrp - ubarlp

          c2 = c*c
            c2f = 2.0_dp*c*cf
            c2v = 2.0_dp*c*cv
            c2p = 2.0_dp*c*cp

! jumps have units of density

          dv1 = 0.5_dp*(dpress + rho*c*dubar)/c2
            dv1f = 0.5_dp*(c2*(dpressf + rho*(c*dubarf+dubar*cf) +             &
                    c*dubar*rhof) - (dpress + rho*c*dubar) * c2f) / c2 / c2
            dv1v = 0.5_dp*(c2*(dpressv + rho*(c*dubarv+dubar*cv) +             &
                    c*dubar*rhov) - (dpress + rho*c*dubar) * c2v) / c2 / c2
            dv1p = 0.5_dp*(c2*(dpressp + rho*(c*dubarp+dubar*cp) +             &
                    c*dubar*rhop) - (dpress + rho*c*dubar) * c2p) / c2 / c2

          dv2 = 0.5_dp*(dpress - rho*c*dubar)/c2
            dv2f = 0.5_dp*(c2*(dpressf - rho*(c*dubarf+dubar*cf) -             &
                    c*dubar*rhof) - (dpress - rho*c*dubar) * c2f) / c2 / c2
            dv2v = 0.5_dp*(c2*(dpressv - rho*(c*dubarv+dubar*cv) -             &
                    c*dubar*rhov) - (dpress - rho*c*dubar) * c2v) / c2 / c2
            dv2p = 0.5_dp*(c2*(dpressp - rho*(c*dubarp+dubar*cp) -             &
                    c*dubar*rhop) - (dpress - rho*c*dubar) * c2p) / c2 / c2

          dv3 = rho
            dv3f = rhof
            dv3v = rhov
            dv3p = rhop

          dv4 = (c*c*drho - dpress)/c2
            dv4f = (c2*(c*(c*drhof + drho*cf) + c*drho*cf) -                   &
                    (c*c*drho - dpress) * c2f) / c2 / c2
            dv4v = (c2*(c*(c*drhov + drho*cv) + c*drho*cv) -                   &
                    (c*c*drho - dpress) * c2v) / c2 / c2
            dv4p = (c2*(c*(c*drhop + drho*cp) + c*drho*cp) -                   &
                    (c*c*drho - dpress) * c2p) / c2 / c2

          r21 = u + c*xnorm
            r21f = uf + c*xnormf + xnorm*cf
            r21v = uv + c*xnormv + xnorm*cv
            r21p = up + c*xnormp + xnorm*cp

          r31 = v + c*ynorm
            r31f = vvf + c*ynormf + ynorm*cf
            r31v = vvv + c*ynormv + ynorm*cv
            r31p = vvp + c*ynormp + ynorm*cp

          r41 = w + c*znorm
            r41f = wf + c*znormf + znorm*cf
            r41v = wv + c*znormv + znorm*cv
            r41p = wp + c*znormp + znorm*cp

          r51 = H + c*ubar
            r51f = Hf + c*ubarf + ubar*cf
            r51v = Hv + c*ubarv + ubar*cv
            r51p = Hp + c*ubarp + ubar*cp

          r22 = u - c*xnorm
            r22f = uf - c*xnormf - xnorm*cf
            r22v = uv - c*xnormv - xnorm*cv
            r22p = up - c*xnormp - xnorm*cp

          r32 = v - c*ynorm
            r32f = vvf - c*ynormf - ynorm*cf
            r32v = vvv - c*ynormv - ynorm*cv
            r32p = vvp - c*ynormp - ynorm*cp

          r42 = w - c*znorm
            r42f = wf - c*znormf - znorm*cf
            r42v = wv - c*znormv - znorm*cv
            r42p = wp - c*znormp - znorm*cp

          r52 = H - c*ubar
            r52f = Hf - c*ubarf - ubar*cf
            r52v = Hv - c*ubarv - ubar*cv
            r52p = Hp - c*ubarp - ubar*cp

          r23 = du - dubar*xnorm
            r23f = duf - dubar*xnormf - xnorm*dubarf
            r23v = duv - dubar*xnormv - xnorm*dubarv
            r23p = dup - dubar*xnormp - xnorm*dubarp

          r33 = dv - dubar*ynorm
            r33f = dvf - dubar*ynormf - ynorm*dubarf
            r33v = dvv - dubar*ynormv - ynorm*dubarv
            r33p = dvp - dubar*ynormp - ynorm*dubarp

          r43 = dw - dubar*znorm
            r43f = dwf - dubar*znormf - znorm*dubarf
            r43v = dwv - dubar*znormv - znorm*dubarv
            r43p = dwp - dubar*znormp - znorm*dubarp

          r53 = u*du + v*dv + w*dw - ubar*dubar
           r53f = u*duf + du*uf + v*dvf + dv*vvf+ w*dwf + dw*wf                &
                 - ubar*dubarf - dubar*ubarf
           r53v = u*duv + du*uv + v*dvv + dv*vvv+ w*dwv + dw*wv                &
                 - ubar*dubarv - dubar*ubarv
           r53p = u*dup + du*up + v*dvp + dv*vvp+ w*dwp + dw*wp                &
                 - ubar*dubarp - dubar*ubarp

          r24 = u
            r24f = uf
            r24v = uv
            r24p = up

          r34 = v
            r34f = vvf
            r34v = vvv
            r34p = vvp

          r44 = w
            r44f = wf
            r44v = wv
            r44p = wp

          r54 = 0.5_dp*q2
            r54f = 0.5_dp*q2f
            r54v = 0.5_dp*q2v
            r54p = 0.5_dp*q2p

          q_loop : do qi = 1, soln%ndim

            if ( qi == 1 ) then

              t = eig1*dv1     + eig2*dv2                                      &
                               + eig3*dv4
                tf = eig1*dv1f + dv1*eig1f                                     &
                   + eig2*dv2f + dv2*eig2f                                     &
                   + eig3*dv4f + dv4*eig3f
                tv = eig1*dv1v + dv1*eig1v                                     &
                   + eig2*dv2v + dv2*eig2v                                     &
                   + eig3*dv4v + dv4*eig3v
                tp = eig1*dv1p + dv1*eig1p                                     &
                   + eig2*dv2p + dv2*eig2p                                     &
                   + eig3*dv4p + dv4*eig3p

            else if ( qi == 2 ) then

              t = eig1*r21*dv1 + eig2*r22*dv2                                  &
                + eig3*r23*dv3 + eig3*r24*dv4
                tf = eig1*(r21*dv1f + dv1*r21f) + r21*dv1*eig1f                &
                   + eig2*(r22*dv2f + dv2*r22f) + r22*dv2*eig2f                &
                   + eig3*(r23*dv3f + dv3*r23f) + r23*dv3*eig3f                &
                   + eig3*(r24*dv4f + dv4*r24f) + r24*dv4*eig3f
                tv = eig1*(r21*dv1v + dv1*r21v) + r21*dv1*eig1v                &
                   + eig2*(r22*dv2v + dv2*r22v) + r22*dv2*eig2v                &
                   + eig3*(r23*dv3v + dv3*r23v) + r23*dv3*eig3v                &
                   + eig3*(r24*dv4v + dv4*r24v) + r24*dv4*eig3v
                tp = eig1*(r21*dv1p + dv1*r21p) + r21*dv1*eig1p                &
                   + eig2*(r22*dv2p + dv2*r22p) + r22*dv2*eig2p                &
                   + eig3*(r23*dv3p + dv3*r23p) + r23*dv3*eig3p                &
                   + eig3*(r24*dv4p + dv4*r24p) + r24*dv4*eig3p

            else if ( qi == 3 ) then

              t = eig1*r31*dv1 + eig2*r32*dv2                                  &
                + eig3*r33*dv3 + eig3*r34*dv4
                tf = eig1*(r31*dv1f + dv1*r31f) + r31*dv1*eig1f                &
                   + eig2*(r32*dv2f + dv2*r32f) + r32*dv2*eig2f                &
                   + eig3*(r33*dv3f + dv3*r33f) + r33*dv3*eig3f                &
                   + eig3*(r34*dv4f + dv4*r34f) + r34*dv4*eig3f
                tv = eig1*(r31*dv1v + dv1*r31v) + r31*dv1*eig1v                &
                   + eig2*(r32*dv2v + dv2*r32v) + r32*dv2*eig2v                &
                   + eig3*(r33*dv3v + dv3*r33v) + r33*dv3*eig3v                &
                   + eig3*(r34*dv4v + dv4*r34v) + r34*dv4*eig3v
                tp = eig1*(r31*dv1p + dv1*r31p) + r31*dv1*eig1p                &
                   + eig2*(r32*dv2p + dv2*r32p) + r32*dv2*eig2p                &
                   + eig3*(r33*dv3p + dv3*r33p) + r33*dv3*eig3p                &
                   + eig3*(r34*dv4p + dv4*r34p) + r34*dv4*eig3p

            else if ( qi == 4 ) then

              t = eig1*r41*dv1 + eig2*r42*dv2                                  &
                + eig3*r43*dv3 + eig3*r44*dv4
                tf = eig1*(r41*dv1f + dv1*r41f) + r41*dv1*eig1f                &
                   + eig2*(r42*dv2f + dv2*r42f) + r42*dv2*eig2f                &
                   + eig3*(r43*dv3f + dv3*r43f) + r43*dv3*eig3f                &
                   + eig3*(r44*dv4f + dv4*r44f) + r44*dv4*eig3f
                tv = eig1*(r41*dv1v + dv1*r41v) + r41*dv1*eig1v                &
                   + eig2*(r42*dv2v + dv2*r42v) + r42*dv2*eig2v                &
                   + eig3*(r43*dv3v + dv3*r43v) + r43*dv3*eig3v                &
                   + eig3*(r44*dv4v + dv4*r44v) + r44*dv4*eig3v
                tp = eig1*(r41*dv1p + dv1*r41p) + r41*dv1*eig1p                &
                   + eig2*(r42*dv2p + dv2*r42p) + r42*dv2*eig2p                &
                   + eig3*(r43*dv3p + dv3*r43p) + r43*dv3*eig3p                &
                   + eig3*(r44*dv4p + dv4*r44p) + r44*dv4*eig3p

            else if ( qi == 5 ) then

              t = eig1*r51*dv1 + eig2*r52*dv2                                  &
                + eig3*r53*dv3 + eig3*r54*dv4
                tf = eig1*(r51*dv1f + dv1*r51f) + r51*dv1*eig1f                &
                   + eig2*(r52*dv2f + dv2*r52f) + r52*dv2*eig2f                &
                   + eig3*(r53*dv3f + dv3*r53f) + r53*dv3*eig3f                &
                   + eig3*(r54*dv4f + dv4*r54f) + r54*dv4*eig3f
                tv = eig1*(r51*dv1v + dv1*r51v) + r51*dv1*eig1v                &
                   + eig2*(r52*dv2v + dv2*r52v) + r52*dv2*eig2v                &
                   + eig3*(r53*dv3v + dv3*r53v) + r53*dv3*eig3v                &
                   + eig3*(r54*dv4v + dv4*r54v) + r54*dv4*eig3v
                tp = eig1*(r51*dv1p + dv1*r51p) + r51*dv1*eig1p                &
                   + eig2*(r52*dv2p + dv2*r52p) + r52*dv2*eig2p                &
                   + eig3*(r53*dv3p + dv3*r53p) + r53*dv3*eig3p                &
                   + eig3*(r54*dv4p + dv4*r54p) + r54*dv4*eig3p

            endif

! Compute flux using variables from left side of face

            if ( qi == 1 ) then

              fluxp = rhol*ubar_fsl
                fluxpf = (rhol*ubar_fslf + ubar_fsl*rholf)
                fluxpv = (rhol*ubar_fslv + ubar_fsl*rholv)
                fluxpp = (rhol*ubar_fslp + ubar_fsl*rholp)

            else if ( qi == 2 ) then

              fluxp = (rhol*ul*ubar_fsl + xnorm*pressl)
                fluxpf = (rhol*(ul*ubar_fslf + ubar_fsl*ulf) +                 &
                           ul*ubar_fsl*rholf + xnorm*presslf +                 &
                           pressl*xnormf)
                fluxpv = (rhol*(ul*ubar_fslv + ubar_fsl*ulv) +                 &
                           ul*ubar_fsl*rholv + xnorm*presslv +                 &
                           pressl*xnormv)
                fluxpp = (rhol*(ul*ubar_fslp + ubar_fsl*ulp) +                 &
                           ul*ubar_fsl*rholp + xnorm*presslp +                 &
                           pressl*xnormp)

            else if ( qi == 3 ) then

              fluxp = (rhol*vl*ubar_fsl + ynorm*pressl)
                fluxpf = (rhol*(vl*ubar_fslf + ubar_fsl*vlf) +                 &
                           vl*ubar_fsl*rholf + ynorm*presslf +                 &
                           pressl*ynormf)
                fluxpv = (rhol*(vl*ubar_fslv + ubar_fsl*vlv) +                 &
                           vl*ubar_fsl*rholv + ynorm*presslv +                 &
                           pressl*ynormv)
                fluxpp = (rhol*(vl*ubar_fslp + ubar_fsl*vlp) +                 &
                           vl*ubar_fsl*rholp + ynorm*presslp +                 &
                           pressl*ynormp)

            else if ( qi == 4 ) then

              fluxp = (rhol*wl*ubar_fsl + znorm*pressl)
                fluxpf = (rhol*(wl*ubar_fslf + ubar_fsl*wlf) +                 &
                           wl*ubar_fsl*rholf + znorm*presslf +                 &
                           pressl*znormf)
                fluxpv = (rhol*(wl*ubar_fslv + ubar_fsl*wlv) +                 &
                           wl*ubar_fsl*rholv + znorm*presslv +                 &
                           pressl*znormv)
                fluxpp = (rhol*(wl*ubar_fslp + ubar_fsl*wlp) +                 &
                           wl*ubar_fsl*rholp + znorm*presslp +                 &
                           pressl*znormp)

            else if ( qi == 5 ) then

              fluxp = (enrgyl*ubar_fsl + pressl*ubarl)
                fluxpf = (enrgyl*ubar_fslf + ubar_fsl*enrgylf                  &
                        + pressl*ubarlf + ubarl*presslf)
                fluxpv = (enrgyl*ubar_fslv + ubar_fsl*enrgylv                  &
                        + pressl*ubarlv + ubarl*presslv)
                fluxpp = (enrgyl*ubar_fslp + ubar_fsl*enrgylp                  &
                        + pressl*ubarlp + ubarl*presslp)

            endif

! Now the right side

            if ( qi == 1 ) then

              fluxm = rhor*ubar_fsr
                fluxmf = (rhor*ubar_fsrf + ubar_fsr*rhorf)
                fluxmv = (rhor*ubar_fsrv + ubar_fsr*rhorv)
                fluxmp = (rhor*ubar_fsrp + ubar_fsr*rhorp)

            else if ( qi == 2 ) then

              fluxm = (rhor*ur*ubar_fsr + xnorm*pressr)
                fluxmf = (rhor*(ur*ubar_fsrf + ubar_fsr*urf) +                 &
                           ur*ubar_fsr*rhorf + xnorm*pressrf +                 &
                           pressr*xnormf)
                fluxmv = (rhor*(ur*ubar_fsrv + ubar_fsr*urv) +                 &
                           ur*ubar_fsr*rhorv + xnorm*pressrv +                 &
                           pressr*xnormv)
                fluxmp = (rhor*(ur*ubar_fsrp + ubar_fsr*urp) +                 &
                           ur*ubar_fsr*rhorp + xnorm*pressrp +                 &
                           pressr*xnormp)

            else if ( qi == 3 ) then

              fluxm = (rhor*vr*ubar_fsr + ynorm*pressr)
                fluxmf = (rhor*(vr*ubar_fsrf + ubar_fsr*vrf) +                 &
                           vr*ubar_fsr*rhorf + ynorm*pressrf +                 &
                           pressr*ynormf)
                fluxmv = (rhor*(vr*ubar_fsrv + ubar_fsr*vrv) +                 &
                           vr*ubar_fsr*rhorv + ynorm*pressrv +                 &
                           pressr*ynormv)
                fluxmp = (rhor*(vr*ubar_fsrp + ubar_fsr*vrp) +                 &
                           vr*ubar_fsr*rhorp + ynorm*pressrp +                 &
                           pressr*ynormp)

            else if ( qi == 4 ) then

              fluxm = (rhor*wr*ubar_fsr + znorm*pressr)
                fluxmf = (rhor*(wr*ubar_fsrf + ubar_fsr*wrf) +                 &
                           wr*ubar_fsr*rhorf + znorm*pressrf +                 &
                           pressr*znormf)
                fluxmv = (rhor*(wr*ubar_fsrv + ubar_fsr*wrv) +                 &
                           wr*ubar_fsr*rhorv + znorm*pressrv +                 &
                           pressr*znormv)
                fluxmp = (rhor*(wr*ubar_fsrp + ubar_fsr*wrp) +                 &
                           wr*ubar_fsr*rhorp + znorm*pressrp +                 &
                           pressr*znormp)

            else if ( qi == 5 ) then

              fluxm = (ubar_fsr*enrgyr + pressr*ubarr)
                fluxmf = (ubar_fsr*enrgyrf + enrgyr*ubar_fsrf                  &
                        + pressr*ubarrf + ubarr*pressrf)
                fluxmv = (ubar_fsr*enrgyrv + enrgyr*ubar_fsrv                  &
                        + pressr*ubarrv + ubarr*pressrv)
                fluxmp = (ubar_fsr*enrgyrp + enrgyr*ubar_fsrp                  &
                        + pressr*ubarrp + ubarr*pressrp)

            endif

!           res = 0.5_dp*area*(fluxp + fluxm - t)
              resf = 0.5_dp*(area*(fluxpf + fluxmf - tf)                       &
                    + (fluxp + fluxm - t)*areaf)
              resv = 0.5_dp*(area*(fluxpv + fluxmv - tv)                       &
                    + (fluxp + fluxm - t)*areav)
              resp = 0.5_dp*(area*(fluxpp + fluxmp - tp)                       &
                    + (fluxp + fluxm - t)*areap)

!           if(nde(1) <= grid%nnodes0) then
!             soln%res(qi,nde(1)) = soln%res(qi,nde(1)) + res
!           endif

            n1 = nde(1)  ! node at which residual is being linearized

            do j = 1, design%nfunctions
              lambda(j) = sadj%coltag(qi,n1)*sadj%rlam(qi,n1,j)
            end do

            fcn_loop2 : do j = 1, design%nfunctions
              productD(j,ijet,1) = productD(j,ijet,1) + resf*lambda(j)
              productD(j,ijet,2) = productD(j,ijet,2) + resv*lambda(j)
              productD(j,ijet,3) = productD(j,ijet,3) + resp*lambda(j)
            end do fcn_loop2

          end do q_loop

        enddo corner_tris_loop
      enddo loop_tris

    endif bc_7030

   end do bound_loop

   call ptoe(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)

! If time-dependent, we do not want the residual contribution at the initial
! state - zero it out

    if ( itime > 0 .and. physical_timestep == 1 .and. subiteration == 0 ) then
      do j = 1, design%nfunctions
        productD(j,:,:) = my_0
      end do
    endif

! Add up the contributions - cost function pieces are identically zero!

    do j = 1, design%nfunctions
      dLdX(j,:,:) = productD(j,:,:)
    end do

    deallocate(productD)

! Reduce the results

    fcn_loop : do j = 1, design%nfunctions
     jet_loop : do n = 1, 10
      var_loop : do k = 1, 3
        call lmpi_reduce(dLdX(j,n,k),tempvar)
        call lmpi_bcast(tempvar)
        dLdX(j,n,k) = tempvar
        if ( lmpi_master ) then
          write(*,'(a,e23.14)') 'Reduced jet derivative         = ',dLdX(j,n,k)
          write(67,'(a,e23.14)') 'Reduced jet derivative         = ',dLdX(j,n,k)
        endif
      end do var_loop
     end do jet_loop
    end do fcn_loop

  end subroutine sensjet

end module djet

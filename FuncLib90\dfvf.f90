!================================= DFVF ======================================80
!
! Linearization of the viscous fluxes.
! Note: tx,ty,tz : unit normal to cell face.
!       qt       : T-vector (var1,u,v,w,T) at face.
!       dqt      : d(qt) w/r to qt at cell-centers.
!       qtgrad   : gradient of T-vector (var1,u,v,w,T) at face.
!       dugrad   : d(u) w/r u at cell.
!       dvgrad   : d(v) w/r v at cell.
!       dwgrad   : d(w) w/r w at cell.
!       dtgradn  : d(Tgradn) w/r T at cell.
! Note : qt(1), dqt(1,:), qtgrad(:,1) unused by intent.
!
!=============================================================================80

  pure function dfvf(tx, ty, tz, area, amutf, qt, dqt,                         &
                     qtgrad, dugrad, dvgrad, dwgrad, dtgradn,                  &
                     n_j, qta )

    use flux_constants, only : cgp, cgpt, cstar

    integer, intent(in) :: n_j

    real(dp),                   intent(in) :: tx, ty, tz, area, amutf
    real(dp), dimension(5),     intent(in) :: qt
    real(dp), dimension(3,5),   intent(in) :: qtgrad
    real(dp), dimension(  n_j), intent(in) :: dtgradn
    real(dp), dimension(3,n_j), intent(in) :: dugrad, dvgrad, dwgrad
    real(dp), dimension(5,n_j), intent(in) :: dqt, qta

    integer :: ii, qeq, eq

    real(dp) :: muf, mucgpa, muta, mucgp, mut
    real(dp) :: dvtm, dvte, fvet, ria

    real(dp), dimension(5) :: fv
    real(dp), dimension(5,5) :: dfv, dfvt

    real(dp), dimension(5,5,n_j) :: dfvf

  continue

    muf   = viscosity_law( cstar, qt(5) )
    fv(1:5) = tau_v( tx, ty, tz, area,                       &
                     qtgrad, qt(2), qt(3), qt(4), muf, amutf )

    mut    = (     muf +      amutf )
    mucgp  = ( cgp*muf + cgpt*amutf )
    muta   =   mut*area
    mucgpa = mucgp*area

    !...accounting for variation of viscosity

    dvtm = dviscosity_law( cstar, qt(5) )/mut
    dvte = cgp*dvtm*mut/mucgp

    fvet = fv(5) - qt(2)*fv(2) - qt(3)*fv(3) - qt(4)*fv(4)

    dfvt(1,:) = 0._dp
    dfvt(:,1) = 0._dp
    df_loop : do ii=1,n_j

      dfvt(2:4,2:4) = dfv_mom_dgrad( muta, tx, ty, tz,                        &
                                     dugrad(1,ii), dugrad(2,ii), dugrad(3,ii),&
                                     dvgrad(1,ii), dvgrad(2,ii), dvgrad(3,ii),&
                                     dwgrad(1,ii), dwgrad(2,ii), dwgrad(3,ii) )

      !...variation of viscosity at face with T.
      do eq=2,4
        dfvt(eq,5) = fv(eq)*dvtm*dqt(5,ii)
      enddo

      do qeq=2,5
        dfvt(5,qeq) = qt(2)*dfvt(2,qeq) &
                    + qt(3)*dfvt(3,qeq) &
                    + qt(4)*dfvt(4,qeq)
      enddo

      dfvt(5,2) = dfvt(5,2) + fv(2)*dqt(2,ii)
      dfvt(5,3) = dfvt(5,3) + fv(3)*dqt(3,ii)
      dfvt(5,4) = dfvt(5,4) + fv(4)*dqt(4,ii)

      dfvt(5,5) = dfvt(5,5) + fvet*dvte*dqt(5,ii) - mucgpa*dtgradn(ii)

      !...convert to conserved from u,v,w,T variables.
      ria = 1._dp/qta(1,ii)
      dfv = trv_from_t( ria, qta(2,ii), qta(3,ii),      &
                             qta(4,ii), qta(5,ii), dfvt )

      !...now update the Jacobian entries from cella
      dfvf(:,:,ii) = dfv(:,:)

    enddo df_loop

  end function dfvf

module turb_2eqn_routines

  use lmpi,           only : lmpi_id
  use lmpi,           only : lmpi_conditional_stop
  use kinddefs,       only : dp
  use turb_kw_const,  only : verbose
  use turb_util,      only : kloc, wloc
  use info_depr,      only : skeleton
  use turb_source_kw, only : source_kw

  use turb_parameters, only : t_prod, t_dest

  use turbulence_info, only :      bsl,           menter_sst,      &
      kw_sst,       kw_sst2003,    sst_v,         sst,             &
      sst_2003,                    abid_linear,   wilcox1988,      &
      wilcox1988_v,                wilcox_kw88p,  wilcox_kw98,     &
      wilcox_asm,   easm_ddes,     EASMko2003_S,  wilcox_kw06,     &
      kw_des,       chien,         wilcox_kw06p,  wilcox2006,      &
      wilcox2006_v, k_kL_MEAH2013, hrle<PERSON>,         wilco<PERSON>_<PERSON>,      &
      hell<PERSON>,     smirnov,       easmcc,                         &
      sstrc, sst_kkl

  implicit none

  private

  public :: turb_resid_2eqn_routines
  public :: turb_jacob_2eqn_routines

  public :: approximate_jacobians_2eqn

  public :: bc_kw_set_walls
  public :: bc_ke_set_walls
  public :: flux_turb
  public :: n_val
  public :: wall_turbulence_kw_sst
  public :: wall_turbulence_ke

  public :: turb_source_kkl

  real(dp), parameter :: zero    = 0.0_dp
  real(dp), parameter :: half    = 0.5_dp
  real(dp), parameter :: one     = 1.0_dp
  integer,  parameter :: n_val   = 7
  real(dp), parameter :: a_0     = -0.72_dp

  real(dp), dimension(:), allocatable :: u_double_prime
  real(dp), dimension(:), allocatable :: mu_t_les
  real(dp), dimension(:), allocatable :: gradx_sqrtk
  real(dp), dimension(:), allocatable :: grady_sqrtk
  real(dp), dimension(:), allocatable :: gradz_sqrtk

  logical :: u_double_prime_allocated = .false.
  logical :: blend_setup              = .false.
  logical :: grad_sqrtk_setup         = .false.

  integer  :: des_points

contains

!============================== TURB_RESID_2EQN ==============================80
!
! Residual for mixed element formulation.
!
! Calculates the residual for 2-eqn models on mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
!=============================================================================80
  subroutine turb_resid_2eqn_routines  (eqn_set, nnodes0, nnodes01,            &
                       nedgeloc, eptr, turb, qnode, res, slen, gradx, grady,   &
                       gradz, vol, xn, yn, zn, ra, x, y, z, nedgeloc_2d,       &
                       nnodes0_2d, node_pairs_2d, iflagslen,            n_turb,&
                       n_tot, n_grd, nelem, elem,                              &
                       nbound, bc, dxdt, dydt, dzdt, rhotauij,                 &
                       ds11dt, ds12dt, ds13dt, ds22dt, ds23dt, ds33dt,         &
                       sst_f1, sst_f2 )

    use bc_types,        only : bcgrid_type
    use ddt,             only : ddt7, assignment(=)
    use fluid,           only : gamma, sutherland_constant
    use kinddefs,        only : dp
    use info_depr,       only : xmre, twod, tref
    use turbulence_info, only : turbulence_model_int
    use lmpi_app,        only : lmpi_xfer
    use element_types,   only : elem_type
    use turb_ke,         only : ke_source
    use turb_util,       only : velocity_gradient_derivative
    use turb_functions,  only : get_eddy_viscosity, get_timescale
    use turb_kw_const,   only : curvature_model_int, realizability_int
    use nml_two_d_trans, only : turb_transition

    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nelem
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    integer,                              intent(in)    :: n_grd
    integer,                              intent(in)    :: nedgeloc
    integer,                              intent(in)    :: nnodes0
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: nedgeloc_2d
    integer,                              intent(in)    :: nnodes0_2d
    integer,                              intent(in)    :: nbound

    integer,  dimension(nnodes01),        intent(in)    :: iflagslen
    integer,  dimension(2,nedgeloc),      intent(in)    :: eptr
    integer,  dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d

    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: x, y, z, vol
    real(dp), dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp), dimension(nedgeloc),        intent(in)    :: ra
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(inout) :: res

    type(elem_type), dimension(nelem),    intent(in)    :: elem
    type(bcgrid_type),dimension(nbound),  intent(in)    :: bc
    real(dp), dimension(nnodes01),        intent(in)    :: dxdt
    real(dp), dimension(nnodes01),        intent(in)    :: dydt
    real(dp), dimension(nnodes01),        intent(in)    :: dzdt
    real(dp), dimension(6,nnodes01),      intent(out)   :: rhotauij
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f1
    real(dp), dimension(nnodes01),        intent(in)    :: sst_f2
    real(dp), dimension(:),               intent(inout) :: ds11dt
    real(dp), dimension(:),               intent(inout) :: ds12dt
    real(dp), dimension(:),               intent(inout) :: ds13dt
    real(dp), dimension(:),               intent(inout) :: ds22dt
    real(dp), dimension(:),               intent(inout) :: ds23dt
    real(dp), dimension(:),               intent(inout) :: ds33dt
!   real(dp), dimension(nnodes0),         intent(inout) :: crossd

    integer :: node_src_eval
    integer :: i, ii
    integer :: neg_k

    real(dp)                  :: cstar
    real(dp)                  :: rhoinv
    real(dp)                  :: pressure
    real(dp)                  :: temperature
    real(dp)                  :: nu
    real(dp)                  :: xmr

    real(dp), parameter :: zero     = 0.0_dp
    real(dp), parameter :: one      = 1.0_dp

    real(dp), dimension(       n_turb )   :: source_ke

    real(dp), dimension( n_tot+n_turb )   :: trbsrc
    type(ddt7), dimension( n_tot+n_turb ) :: src_ddt

    real(dp),   dimension(n_tot+n_turb)   :: q
    type(ddt7), dimension(n_tot+n_turb)   :: q_ddt

    real(dp), dimension(3,3)              :: dsdt
    real(dp), dimension(3,3)              :: gradv
    real(dp), dimension(3,3)              :: sij
    real(dp), dimension(3,3)              :: wij
    real(dp)                              :: sijsij
    real(dp)                              :: s_mod
    real(dp)                              :: vort
    real(dp)                              :: timescale

  continue

    neg_k    = 0

!   When using edge-based diffusion terms, we only need to visit this routine
!   one time
    dsdt     = zero

    xmr   = xmre
    cstar = sutherland_constant / tref

    node_src_eval   = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    if ( .not. blend_setup ) then
      if ( allocated(mu_t_les) ) deallocate(mu_t_les)
      allocate(mu_t_les(nnodes01))
      blend_setup = .true.
    endif

    if ( .not. grad_sqrtk_setup ) then
      if ( allocated(gradx_sqrtk) ) deallocate(gradx_sqrtk)
      allocate(gradx_sqrtk(nnodes01))
      if ( allocated(grady_sqrtk) ) deallocate(grady_sqrtk)
      allocate(grady_sqrtk(nnodes01))
      if ( allocated(gradz_sqrtk) ) deallocate(gradz_sqrtk)
      allocate(gradz_sqrtk(nnodes01))
      grad_sqrtk_setup = .true.
    endif

!   crossd   = zero
    mu_t_les = zero

!--------------- derivative of strain rate tensor ----------------------------80
!   For SARC only, need to get D(Sij)/Dt terms (ignoring time deriv for now)
!   = u_k*d(Sij)/dx_k (summing over k)

    if ( .not. u_double_prime_allocated ) then
      if ( turbulence_model_int == k_kL_MEAH2013 ) then
        allocate(u_double_prime(nnodes01))
        u_double_prime_allocated = .true.
      else
        allocate(u_double_prime(1))
        u_double_prime_allocated = .true.
      endif
    endif

    u_double_prime = zero

!-----------------------------------------------------------------------------80
!                     Reynolds stress tensor
!-----------------------------------------------------------------------------80
      dsdt   = zero
      do ii = 1, node_src_eval
        i        = get_index( twod, node_pairs_2d, ii )
        select case ( curvature_model_int )
        case ( hellsten, smirnov, easmcc )
          dsdt     = make_dsdt ( ds11dt(i), ds22dt(i), ds33dt(i)               &
                               , ds12dt(i), ds13dt(i), ds23dt(i) )
        case default
          dsdt = zero
        end select
        rhotauij(:,i) = turb_2eqn_rhotauij ( i, eqn_set                        &
                       , nnodes01, n_tot, n_turb, n_grd                        &
                       , qnode, turb, gradx, grady, gradz, slen, xmr, dsdt )
      enddo
!-----------------------------------------------------------------------------80
!                    source terms for all 2-equation models
!-----------------------------------------------------------------------------80
    select case ( turbulence_model_int )
    case ( abid_linear )

    if ( skeleton > 20 ) write(*,*) 'Calling ke_grad_sqrt_k in turb_resid_2eqn'

    call ke_grad_sqrt_k( nnodes0, nnodes01, nedgeloc, nedgeloc_2d, nnodes0_2d  &
                       , node_pairs_2d, eptr, turb, x, y, z, xn, yn, zn, ra    &
                       , vol, nbound, bc, n_turb, elem, nelem                  &
                       , gradx_sqrtk, grady_sqrtk, gradz_sqrtk )

!   These terms are node-based and thus are grid transparent, and
!   hence only need be computed while processing the first element type

      do ii = 1, node_src_eval
        i        = get_index( twod, node_pairs_2d, ii )
        source_ke= ke_source  (             turb(1,i), turb(2,i)               &
                              , qnode(1,i), qnode(2,i), qnode(5,i), slen(i)    &
                              , gradx(2,i), gradx(3,i), gradx(4,i)             &
                              , grady(2,i), grady(3,i), grady(4,i)             &
                              , gradz(2,i), gradz(3,i), gradz(4,i)             &
                              , vol(i), iflagslen(i), x(i) )

        res(1,i) = res(1,i) - source_ke(1)
        res(2,i) = res(2,i) - source_ke(2)

      end do

!-----------------------------------------------------------------------------80
    case ( kw_sst, kw_sst2003, sst, sst_v, sst_2003, bsl,                      &
           wilcox1988, wilcox1988_v,                                           &
           wilcox_kw98, wilcox_asm, easm_ddes, EASMko2003_S,                   &
           wilcox_kw06, kw_des, chien, wilcox_kw06p, wilcox2006,               &
           wilcox2006_v, wilcox_kw88p         )

      if ( skeleton>5 ) write(*,*) &
      'Calculating source_kw in turb_resid_2eqn- ', turbulence_model_int

      des_points = 0
      do ii = 1, node_src_eval

        i         = get_index( twod, node_pairs_2d, ii )
        select case ( curvature_model_int )
          case ( hellsten, easmcc )
            dsdt      = make_dsdt ( ds11dt(i), ds22dt(i), ds33dt(i)            &
                                  , ds12dt(i), ds13dt(i), ds23dt(i) )
          case default
            dsdt      = zero
        end select

        q           = get_q( eqn_set, n_tot, n_turb                            &
                           , qnode(1:n_tot,i), turb(1:n_turb,i) )
        q_ddt       = get_q_ddt( eqn_set, n_tot, n_turb, kloc, wloc            &
                          , qnode(1:n_tot,i), turb(1:n_turb,i) )

        rhoinv      = 1.0_dp/q(1)
        pressure    = q(5)
        temperature = gamma * pressure * rhoinv
        nu          = viscosity_law( cstar, temperature ) * rhoinv

        call source_kw ( n_tot, n_turb, n_grd, kloc, wloc,                     &
                         q, q_ddt, vol(i), temperature, nu,                    &
                         gradx(:,i), grady(:,i), gradz(:,i),                   &
                         iflagslen(i), slen(i), xmr, x(i), y(i), z(i),         &
                         dsdt, rhotauij(:,i), sst_f1(i), sst_f2(i),            &
                         turb_transition, trbsrc)

        res(1,i) = res(1,i) - trbsrc(kloc)
        res(2,i) = res(2,i) - trbsrc(wloc)

        if ( turb(1,i) < zero ) neg_k = neg_k + 1
      end do
      if ( neg_k > 0 ) write(*,*) neg_k,' tke negative valued nodes'
      if ( verbose .and. ( des_points > 0 ) )                                  &
      write(*,*) 'Number of des points = ', des_points

!-----------------------------------------------------------------------------80
    case ( k_kL_MEAH2013 )

    if ( skeleton>5 ) write(*,*) &
    'Calculating source_kw in turb_resid_2eqn- ', turbulence_model_int
      call velocity_gradient_derivative(eqn_set, nnodes0, nnodes01, nedgeloc,  &
                   eptr, x, y, z, gradx, grady, gradz, xn, yn, zn, ra,         &
                   vol, nedgeloc_2d, node_pairs_2d, nnodes0_2d, nelem, elem,   &
                   n_tot, n_grd, dxdt, dydt, dzdt, nbound, bc, u_double_prime )

      do ii = 1, node_src_eval
        src_ddt  = zero

        i        = get_index( twod, node_pairs_2d, ii )
        src_ddt  = turb_source_kkl( i, eqn_set, nnodes01, n_tot, n_turb, n_grd &
                             , vol, qnode, turb, gradx, grady, gradz           &
                             , iflagslen, slen, xmr, u_double_prime, rhotauij )

        res(1,i) = res(1,i) - src_ddt(kloc)%f
        res(2,i) = res(2,i) - src_ddt(wloc)%f

      end do
!-----------------------------------------------------------------------------80
    case ( wilcox_les )

      do ii = 1, node_src_eval

        i           = get_index( twod, node_pairs_2d, ii )
        q           = get_q( eqn_set, n_tot, n_turb                           &
                    , qnode(1:n_tot,i), turb(1:n_turb,i) )

        rhoinv      = 1.0_dp/q(1)
        pressure    = q(5)
        temperature = gamma * pressure * rhoinv
        nu          = viscosity_law( cstar, temperature ) * rhoinv

        gradv  = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                 &
                          , gradx(3,i), grady(3,i), gradz(3,i)                 &
                          , gradx(4,i), grady(4,i), gradz(4,i) )
        sij     = get_sij    ( gradv )
        wij     = get_wij    ( gradv )
        sijsij  = get_sijsij ( sij )
        s_mod   = sqrt ( 2.0_dp * sijsij )
        vort    = get_vort ( gradv )
        timescale = get_timescale     ( n_tot, n_turb, kloc, wloc              &
                                      , q, s_mod, vort, nu                     &
                                      , slen(i), xmr, turbulence_model_int     &
                                      , realizability_int                      &
                                      )

        mu_t_les(i) = get_eddy_viscosity ( n_tot, n_turb, kloc, wloc, q        &
                                          , timescale, sij, wij, xmr, nu       &
                                          , one, iflagslen(i), slen(i)         &
                                          , vol(i), turbulence_model_int )
      enddo

      call lmpi_xfer( mu_t_les )

      do ii = 1, node_src_eval

        i        = get_index( twod, node_pairs_2d, ii )
        q        = get_q( eqn_set, n_tot, n_turb                               &
                        , qnode(1:n_tot,i), turb(1:n_turb,i) )
        q_ddt    = get_q_ddt( eqn_set, n_tot, n_turb, kloc, wloc               &
                       , qnode(1:n_tot,i), turb(1:n_turb,i) )

        rhoinv      = 1.0_dp/q(1)
        pressure    = q(5)
        temperature = gamma * pressure * rhoinv
        nu          = viscosity_law( cstar, temperature ) * rhoinv

        call source_kw ( n_tot, n_turb, n_grd, kloc, wloc,                     &
                         q, q_ddt, vol(i), temperature, nu,                    &
                         gradx(:,i), grady(:,i), gradz(:,i),                   &
                         iflagslen(i), slen(i), xmr, x(i), y(i), z(i),         &
                         dsdt, rhotauij(:,i), sst_f1(i), sst_f2(i),            &
                         turb_transition, trbsrc)

        res(1,i) = res(1,i) - trbsrc(kloc)
        res(2,i) = res(2,i) - trbsrc(wloc)

      end do

    end select

  end subroutine turb_resid_2eqn_routines

!============================== TURB_JACOB_2EQN ==============================80
!
! LHS for mixed element formulation
!
! Calculates the jacobians for 2-eqn models (loosely coupled) on
! mixed-element grids.
!
! Diffusion terms are computed using one of the following:
! (1) complete terms via cell-based integration
! (2) complete terms via edge-based integration
!
! Both compressible and incompressible perfect gas
!
! Note: Incoming qnode contains the primitive variables, unlike the
! case for the laminar viscous Jacobians
!
!=============================================================================80
  subroutine turb_jacob_2eqn_routines (eqn_set, nnodes0, nnodes01,             &
                        turb, qnode, iflagslen, slen, gradx, grady, gradz,     &
                        vol, a_diag, nnodes0_2d, node_pairs_2d, n_turb,        &
                        n_tot, n_grd, g2m, sst_f1, sst_f2,                     &
                        crossd, rhotauij )

    use kinddefs,      only : dp
    use info_depr,     only : twod, xmach, re
    use turbulence_info, only : turbulence_model_int
    use ddt,             only : ddt7, assignment(=)

    integer,                                     intent(in) :: eqn_set
    integer,                                     intent(in) :: n_tot, n_grd
    integer,                                     intent(in) :: n_turb
    integer,                                     intent(in) :: nnodes0_2d
    integer,                                     intent(in) :: nnodes0
    integer,                                     intent(in) :: nnodes01

    integer, dimension(2,nnodes0_2d),            intent(in) :: node_pairs_2d
    integer, dimension(:),                       intent(in) :: g2m

    integer,   dimension(nnodes01),              intent(in)    :: iflagslen
    real(dp),  dimension(nnodes01),              intent(in)    :: slen
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),        intent(in)    :: gradz
    real(dp),  dimension(nnodes01),              intent(in)    :: vol
    real(dp),  dimension(n_turb,nnodes01),       intent(in)    :: turb
    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(n_turb,n_turb,nnodes0), intent(inout) :: a_diag
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f1
    real(dp),  dimension(nnodes01),              intent(in)    :: sst_f2
    real(dp),  dimension(nnodes0),               intent(inout) :: crossd
    real(dp),  dimension(6,nnodes0),             intent(in)    :: rhotauij

    integer  :: node_src_eval
    integer  :: i, ii, n
    integer,  dimension(n_turb) :: a_diag_neg
    real(dp), dimension(n_turb) :: a_diag_increment

    real(dp),   dimension(n_turb, n_turb) :: d_source_kw
    type(ddt7), dimension(n_tot+n_turb)   :: d_source_ddt
    integer                               :: row
    real(dp)                              :: xmr

  continue

!   When using the edge-based terms, we only need to visit this routine
!   one time

    node_src_eval  = nnodes0
    if (twod) then
      node_src_eval   = nnodes0_2d
    endif

    xmr   = xmach / re
!-----------------------------------------------------------------------------80
!                             source terms
!-----------------------------------------------------------------------------80
!   Next compute the source (production/destruction) terms

    if ( turbulence_model_int == k_kL_MEAH2013 ) then

        source1a: do ii = 1, node_src_eval
        i         = get_index( twod, node_pairs_2d, ii )
        d_source_ddt = turb_source_kkl( i, eqn_set, nnodes01, n_tot, n_turb,   &
                                        n_grd, vol, qnode, turb,               &
                                        gradx, grady, gradz, iflagslen, slen,  &
                                        xmr, u_double_prime, rhotauij )

        row             = g2m(i)
        a_diag(1,1,row) = a_diag(1,1,row) - d_source_ddt(kloc)%d(kloc)
        a_diag(1,2,row) = a_diag(1,2,row) - d_source_ddt(kloc)%d(wloc)
        a_diag(2,1,row) = a_diag(2,1,row) - d_source_ddt(wloc)%d(kloc)
        a_diag(2,2,row) = a_diag(2,2,row) - d_source_ddt(wloc)%d(wloc)
!        write(6,'(a,i10,4(1x,es15.5))') 'a_diag:.............', row,&
!        a_diag(1:2,1:2,row)
      end do source1a

    else
        source2a: do ii = 1, node_src_eval
        i         = get_index( twod, node_pairs_2d, ii )
        d_source_kw = turb_jacob_source_2eqn( i, eqn_set, nnodes01, nnodes0    &
                          ,  turb, qnode, vol, gradx, grady, gradz             &
                          , n_turb, n_tot, n_grd, iflagslen, slen              &
                          , sst_f1, sst_f2, crossd )

          row             = g2m(i)
          a_diag(1,1,row) = a_diag(1,1,row) + vol(i)*d_source_kw(1,1)! Dkdk
          a_diag(1,2,row) = a_diag(1,2,row) + vol(i)*d_source_kw(1,2)! Dkdw
          a_diag(2,1,row) = a_diag(2,1,row) + vol(i)*d_source_kw(2,1)! Dwdk
          a_diag(2,2,row) = a_diag(2,2,row) + vol(i)*d_source_kw(2,2)! Dwdw

        end do source2a
    endif

!                             positivity
!-----------------------------------------------------------------------------80
!
        a_diag_neg       = 0
        a_diag_increment = 0.0_dp

        check_pos: do ii = 1, node_src_eval

          i         = get_index( twod, node_pairs_2d, ii )
          row       = g2m(i)

          do n = 1, n_turb
            if ( a_diag(n,n,row) < zero ) then
              a_diag_increment(n) =  a_diag_increment(n) + a_diag(n,n,row)
              a_diag_neg(n)       =  a_diag_neg(n) + 1
              a_diag(n,n,row)     = 0.0_dp
            endif
          end do

        end do check_pos

!       if ( ( a_diag_neg(1) > 0 ) .or. ( a_diag_neg(2) > 0 ) ) then
!         write(6,'(a,10i10)') 'turbulent a_diag negative', &
!         a_diag_neg(1:n_turb)
!       endif
!       if ( a_diag22_neg > 0 ) then
!         write(6,'(a,i10,es15.5)') 'a_diag(2,2) negative', &
!         a_diag22_neg, a_diag22_increment/real(a_diag22_neg,dp)
!       endif

  end subroutine turb_jacob_2eqn_routines

!========================= TURB_SOURCE_KKL ===================================80
!
! Compressible k-kL MEAH model
!
!=============================================================================80
       function turb_source_kkl  ( i, eqn_set, nnodes01, n_tot, n_turb, n_grd  &
                       , vol, qnode, turb, gradx, grady, gradz                 &
                       , iflagslen, slen, xmr, u_double_prime, rhotauij )

    use ddt,      only : ddt7, assignment(=), operator(*), operator(+)         &
                       , operator(/), ddt_min, operator(-), operator(**)       &
                       , ddt_sqrt, ddt_max, ddt_min

    use fluid,           only : gamma, sutherland_constant
    use info_depr,       only : tref
    use nml_two_d_trans, only : turb_transition
    use debug_defs,      only : test_freestream
    use solution_types,  only : compressible, incompressible

    use turb_kw_const,   only : kappa, slen_kdes, cmu_0

    use turb_functions,  only : get_prodk

!   use turbulence_info, only : turbulence_model_int
    use turb_parameters, only : t_prod, t_dest

    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot
    type(ddt7), dimension( n_tot+n_turb )               :: turb_source_kkl

    integer,                              intent(in)    :: i
    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: n_grd
    real(dp), dimension(nnodes01),        intent(in)    :: vol
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    integer,  dimension(nnodes01),        intent(in)    :: iflagslen
    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp),                             intent(in)    :: xmr
    real(dp), dimension(:),               intent(in)    :: u_double_prime
    real(dp), dimension(6,nnodes01),      intent(in)    :: rhotauij


    real(dp)                 :: xmrinv
    real(dp)                 :: cstar
    real(dp)                 :: rho, rhoinv, pressure
    real(dp)                 :: temperature
    real(dp)                 :: s_mod
!   real(dp)                 :: vort
    real(dp)                 :: sijsij

    real(dp), dimension(3,3) :: sij
!   real(dp), dimension(3,3) :: wij
    real(dp), dimension(3,3) :: gradv

!   real(dp),   dimension(n_tot+n_turb)  :: q
    type(ddt7), dimension(n_tot+n_turb)  :: q_ddt

    type(ddt7)                 :: production_keqn_ddt
    type(ddt7)                 :: production_eqn2_ddt
    type(ddt7)                 :: destruction_keqn_ddt
    type(ddt7)                 :: destruction_eqn2_ddt
    type(ddt7)                 :: vsm_k
    type(ddt7)                 :: vsm_phi
!   type(ddt7)                 :: length
    real(dp)                   :: length_vonkarman
    type(ddt7)                 :: zeta
    type(ddt7)                 :: f_phi
    type(ddt7)                 :: tke
    type(ddt7)                 :: phi
    type(ddt7)                 :: c_phi_1
    type(ddt7)                 :: c_phi_2
    type(ddt7)                 :: f_p_k_phi

    real(dp),   dimension(3,3) :: tau

    real(dp)                   :: mu
    real(dp)                   :: length
    real(dp)                   :: lower_length
    real(dp)                   :: upper_length
    real(dp)                   :: prod_k
    real(dp)                   :: mu_t
    real(dp)                   :: dist

    real(dp), parameter        :: zero       = 0.0_dp
    real(dp), parameter        :: zeropt3    = 0.3_dp
    real(dp), parameter        :: one        = 1.0_dp
    real(dp), parameter        :: onept5     = 1.5_dp
    real(dp), parameter        :: two        = 2.0_dp
    real(dp), parameter        :: twopt5     = 2.5_dp
    real(dp), parameter        :: four       = 4.0_dp
    real(dp), parameter        :: six        = 6.0_dp
    real(dp), parameter        :: twenty     = 20.0_dp
    real(dp), parameter        :: smidgen    = 1.0e-12_dp

    real(dp), parameter :: c_d1  = 4.7_dp
    real(dp), parameter :: zeta1 = 1.2_dp
    real(dp), parameter :: zeta2 = 0.97_dp
    real(dp), parameter :: zeta3 = 0.13_dp
!   real(dp), parameter :: c_11  = 10.0_dp
    real(dp), parameter :: c_11  = 10.0_dp
    real(dp), parameter :: c_12  = 1.3_dp

  continue

    cstar    = sutherland_constant / tref
!-------------------- to satisfy the nag -------------------------------------80
    tke                  = zero
    phi                  = zero
    mu                   = zero
    length               = zero
    length_vonkarman     = zero
    lower_length         = huge(1._dp)
    upper_length         = tiny(0._dp)
    vsm_k                = zero
    vsm_phi              = zero
    production_keqn_ddt  = zero
    production_eqn2_ddt  = zero
    destruction_keqn_ddt = zero
    destruction_eqn2_ddt = zero

!--------------------------------- flow variables ----------------------------80
!   q           = get_q( eqn_set, n_tot, n_turb                                &
!                      , qnode(1:n_tot,i), turb(1:n_turb,i) )
    q_ddt        = get_q_ddt( eqn_set, n_tot, n_turb, kloc, wloc               &
                      , qnode(1:n_tot,i), turb(1:n_turb,i) )

    rho          = q_ddt(1)
    rhoinv       = one / rho
    pressure     = q_ddt(5)

    tke          = q_ddt(kloc)
    phi          = q_ddt(wloc)

    dist         = slen(i)
    if ( tke%f   < tiny(1.0_dp) ) tke%f = tiny(1.0_dp)
    if ( slen(i) < tiny(1.0_dp) ) dist  = tiny(1.0_dp)

    length        = phi%f / tke%f
    lower_length  = xmr * length / c_11

!---------------------------------  equation set -----------------------------80
    select case ( eqn_set )
      case ( compressible )
        temperature    = gamma * pressure * rhoinv
        mu      = viscosity_law( cstar, temperature )
      case ( incompressible )
        temperature = one
        mu          = one
    end select

    xmrinv   = one / xmr
!--------------------------------- gradients ---------------------------------80
    gradv  = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                     &
                      , gradx(3,i), grady(3,i), gradz(3,i)                     &
                      , gradx(4,i), grady(4,i), gradz(4,i) )
    sij     = get_sij( gradv )
    sijsij  = get_sijsij( sij )
    s_mod   = sqrt ( two * sijsij )

!-------------------------- Reynolds stresses --------------------------------80
    ! rhotau_ij is calculated before calling the source term routine
    tau(1,1) = rhotauij(1,i)
    tau(2,2) = rhotauij(2,i)
    tau(3,3) = rhotauij(3,i)
    tau(1,2) = rhotauij(4,i)
    tau(1,3) = rhotauij(5,i)
    tau(2,3) = rhotauij(6,i)
    tau(2,1) = tau(1,2)
    tau(3,1) = tau(1,3)
    tau(3,2) = tau(2,3)

    ! in order to preserve the readable form of the source term equations
    ! tau must be divided by (Mach/Re) here to be subsequently multiplied
    ! by (Mach/Re) in the production term
    prod_k           = zero

!-----------------------------------------------------------------------------80
    tau = tau*xmrinv
!-------------------------- strain production --------------------------------80
    if ( abs(s_mod) > tiny(1.0_dp) ) then
      prod_k        = get_prodk ( tau, gradv )
      mu_t        = (cmu_0**0.25_dp) * rho * phi / ddt_sqrt( tke )
      prod_k        = mu_t * s_mod * s_mod
    endif

     if ( phi%f > tiny(1.0_dp) ) then
      destruction_keqn_ddt = t_dest * xmrinv                                   &
                           * ( (cmu_0**0.75_dp) * ( rho * tke**twopt5 ) / phi )
    endif
      prod_k  = ddt_min( abs(prod_k) , twenty*destruction_keqn_ddt / xmr )

    if ( tke%f > tiny(1.0_dp) ) then
      f_p_k_phi    = ( abs(prod_k) * xmr * xmr )           &
                   / ( ( cmu_0**0.75 * rho * tke**twopt5 ) &
                   / ( ddt_max( phi , smidgen) ) )
      f_p_k_phi    = ddt_max ( half, f_p_k_phi )
      f_p_k_phi    = ddt_min (  one, f_p_k_phi )
      upper_length = c_12 * kappa * dist * f_p_k_phi
    else
      upper_length = 1.0e+6_dp
    endif

      if ( dist > tiny(1.0_dp) ) then
    vsm_k                  = -two * mu * ( tke / dist**2 )
     end if

      if ( abs(vsm_k%f) < tiny(1.0_dp) ) vsm_k = zero
!FIXME
!   vsm_k = zero
    production_keqn_ddt    = t_prod * xmr * ( abs(prod_k) + vsm_k )

!        write(*,'(a,12(1x,es15.5))') 'phi      ', phi
!        write(*,'(a,12(1x,es15.5))') 'tke      ', tke
!-----------------------------------------------------------------------------80

    if ( u_double_prime(i) > tiny(1.0_dp) ) then
      length_vonkarman     = kappa * abs( s_mod / u_double_prime(i) )
    else
      length_vonkarman     = zero
    endif

      if( length_vonkarman > upper_length ) length_vonkarman = upper_length
      if( length_vonkarman < lower_length ) length_vonkarman = lower_length

    c_phi_1              = zeta1 &
                         - zeta2 * ( length * xmr / length_vonkarman )**2
!FIXME
!   c_phi_1              = zeta1

    zeta                 = rho * dist * ddt_sqrt(zeropt3*tke)                  &
                         / ( twenty * mu * xmr )
    f_phi                = ( one + c_d1 * zeta ) / ( one + zeta**four )
      if ( dist > tiny(1.0_dp) ) then
    vsm_phi              = -six * mu * ( phi / dist**2 ) * f_phi
      end if
!FIXME
!   vsm_phi = zero !------------------------------------------------------------
      if ( abs(vsm_phi%f) < tiny(1.0_dp) ) vsm_phi = zero
      if ( tke%f > tiny(0.0_dp) ) then
    production_eqn2_ddt  = t_prod * xmr &
                         * ( c_phi_1 * phi * prod_k / tke + vsm_phi )
      else
    production_eqn2_ddt  = zero
      end if

    c_phi_2              = zeta3
    destruction_eqn2_ddt = t_dest * xmrinv * ( c_phi_2 * rho * tke**onept5 )

!     if ( abs(u_double_prime(i)) > 1.0e+20 ) then
!if ( skeleton > 20 ) then
!        write(*,'(a,12(1x,es15.5))') 'vsm_k:...', s_mod, prod_k,tke%f,dist,&
!        u_double_prime(i), lower_length, length_vonkarman , upper_length
!        write(*,'(a,12(1x,es15.5))') 'src      ', dist       &
!       , c_phi_1%f, c_phi_2%f, f_phi%f, vsm_phi%f            &
!       , production_keqn_ddt%f                               &
!       , destruction_keqn_ddt%f                              &
!       , production_eqn2_ddt%f                               &
!       , destruction_eqn2_ddt%f
!        write(*,'(a,12(1x,es15.5))') 'prod k   ', production_keqn_ddt
!        write(*,'(a,12(1x,es15.5))') 'dest k   ', destruction_keqn_ddt
!        write(*,'(a,12(1x,es15.5))') 'prod phi ', production_eqn2_ddt
!        write(*,'(a,12(1x,es15.5))') 'dest phi ', destruction_eqn2_ddt
!     endif

    if (turb_transition) then
      if (iflagslen(i) < 0) then
        production_keqn_ddt   = zero
        production_eqn2_ddt   = zero
        destruction_keqn_ddt  = zero
        destruction_eqn2_ddt  = zero
      end if
    end if

!     set laminar flow outside distance = slen_kdes
!     this mimicks the suggestion by Khorami ala CFL3D
!     default for slen_kdes is set to huge - should not affect standard cases
    if( dist > slen_kdes) then
      production_keqn_ddt = zero
      production_eqn2_ddt = zero
    end if


    turb_source_kkl(kloc) = vol(i)*(production_keqn_ddt &
                                  - destruction_keqn_ddt)/rho
    turb_source_kkl(wloc) = vol(i)*(production_eqn2_ddt &
                                  - destruction_eqn2_ddt)/rho

    if (test_freestream) then
      turb_source_kkl     = zero
    end if

  end function turb_source_kkl

!================================ JACOB_SOURCE_KW ============================80
!
!
!=============================================================================80
  function turb_jacob_source_2eqn( i, eqn_set, nnodes01, nnodes0               &
                          , turb, qnode, vol, gradx, grady, gradz              &
                          , n_turb, n_tot, n_grd, iflagslen, slen              &
                          , sst_f1, sst_f2, crossd )                           &
    result ( d_source_kw )

    use fluid,          only : gamma, sutherland_constant
    use kinddefs,       only : dp
    use info_depr,      only : xmach, re, tref
!   use info_depr,      only : xmach, re, tref, xmre
    use nml_two_d_trans, only : turb_transition
    use debug_defs,     only : test_freestream
    use solution_types, only : compressible, incompressible
    use turbulence_info, only : turbulence_model_int,                          &
                                use_approximate_jacobians,                     &
                                model_strain_form_int, prodk_form_int,         &
                                vorticity_based, strain_rate_based,            &
                                stress_based, des_edge

    use turb_kw_const,  only : kappa                                           &
                             , sstrc_crc, cmu_temp                             &
                             , turb_compress_model                             &
                             , beta1, beta2, sig_w1, sig_w2, cmu_0             &
                             ,        realizability_int

    use turb_kw_const,  only : beta_w88, betastar_w88, sigma_w_w88, gamma_w88  &
                         , beta_0_w98, betastar_0_w98, sigma_w_w98, gamma_w98  &
                         , beta_0_w06, betastar_w06, sigma_w_w06, gamma_w06    &
                         , beta_0_asm, sigma_w_asm, betastar_0_asm, gamma_asm  &
                         , betastar_sst,        angular_velocity               &
                         , gamma1_sst_2003, gamma2_sst_2003                    &
                         , curvature_model_int

    use compute_stress,   only : get_rhotauij_ddt
    use turb_functions,   only : get_timescale_ddt                             &
                               , get_eddy_viscosity_ddt                        &
                               , smirnov_cc_term                               &
                               ,            get_prodk_ddt                      &
                               , get_f_beta_ddt                                &
                               , get_f_betastar_ddt                            &
                               , cross_diffusion_ddt                           &
                               , get_wilcox_compressibility                    &
                               , get_production_k_ddt                          &
                               , get_destruction_k_ddt                         &
                               , get_production_eqn2_ddt                       &
                               , get_destruction_eqn2_ddt

    use ddt,              only : ddt7, assignment(=), operator(/)              &
                               , operator(*), operator(+)
    use turb_parameters, only : t_prod, t_dest

!   use debug_defs,        only : composite_jacobian_lhs

    integer,                               intent(in) :: n_turb
    real(dp), dimension(n_turb, n_turb)               :: d_source_kw

    integer,                               intent(in) :: i
    integer,                               intent(in) :: n_tot
    integer,                               intent(in) :: eqn_set
    integer,                               intent(in) :: n_grd
    integer,                               intent(in) :: nnodes01
    integer,                               intent(in) :: nnodes0
    real(dp),  dimension(n_grd,nnodes01),  intent(in) :: gradx
    real(dp),  dimension(n_grd,nnodes01),  intent(in) :: grady
    real(dp),  dimension(n_grd,nnodes01),  intent(in) :: gradz
    real(dp),  dimension(n_turb,nnodes01), intent(in) :: turb
    real(dp),  dimension(n_tot,nnodes01),  intent(in) :: qnode
    real(dp),  dimension(nnodes01),        intent(in) :: vol
    integer,   dimension(nnodes01),        intent(in) :: iflagslen
    real(dp),  dimension(nnodes01),        intent(in) :: slen
    real(dp),  dimension(nnodes01),        intent(in)    :: sst_f1
    real(dp),  dimension(nnodes01),        intent(in)    :: sst_f2
    real(dp),  dimension(nnodes0),         intent(inout) :: crossd
!   real(dp),  dimension(6,nnodes01),      intent(in) :: rhotauij

    real(dp) :: vort, ri, f4
    real(dp) :: f1
    real(dp) :: rho, rhoinv
    real(dp) :: pressure
    real(dp) :: temperature
    real(dp) :: tke
    real(dp) :: omega
    real(dp) :: phi
    real(dp) :: xmr
    real(dp) :: mu
    real(dp) :: xmrinv
    real(dp) :: ctg

    real(dp) :: my_xmach
    real(dp) :: wws_sum
    real(dp) :: sonic2
    real(dp) :: betastar_0, beta_0
    real(dp) :: nu
    real(dp) :: f2
    real(dp) :: cstar
!   real(dp) :: dissp_c
    real(dp) :: sigma_w
    real(dp) :: alpha_inner, alpha_outer
    real(dp) :: lambda, re_k

    real(dp), parameter :: zero       = 0.0_dp
    real(dp), parameter :: one        = 1.0_dp
    real(dp), parameter :: two        = 2.0_dp
    real(dp), parameter :: three      = 3.0_dp
    real(dp), parameter :: ten        = 10.0_dp
    real(dp), parameter :: twenty     = 20.0_dp
!   real(dp), parameter :: smidgen    = 1.0e-6_dp

    real(dp), dimension(n_tot+n_turb) :: q
    real(dp), dimension(3,3)          :: gradv, sij, wij
    real(dp), dimension(3,3)          :: sijhat
    real(dp), dimension(3,3)          :: tau
    real(dp), dimension(3,3)          :: wij_r
    real(dp), dimension(3,3)          :: dsdt
    real(dp), dimension(3)            :: gradk, gradw
    real(dp)                          :: sijsij
    real(dp)                          :: wijwij
    real(dp)                          :: tracepart
    real(dp)                          :: wwshat_sum
    real(dp)                          :: s_mod
    real(dp)                          :: w_mod
    real(dp)                          :: f_r1
    real(dp)                          :: gamma_w

    real(dp)                          :: f_betastar, f_beta
    real(dp)                          :: betastar, beta
!   real(dp)                          :: mu_t


    type(ddt7), dimension(n_tot+n_turb) :: q_ddt
    type(ddt7)                          :: p_keqn_ddt
    type(ddt7)                          :: d_keqn_ddt
    type(ddt7)                          :: p_eqn2_ddt
    type(ddt7)                          :: d_eqn2_ddt
    type(ddt7), dimension(3,3) :: tau_ddt
    type(ddt7) :: rho_ddt
    type(ddt7) :: tke_ddt
    type(ddt7) :: omega_ddt
    type(ddt7) :: mu_t_ddt
    type(ddt7) :: nu_ddt

    type(ddt7) :: f_betastar_ddt, f_beta_ddt
    type(ddt7) :: betastar_ddt, beta_ddt

    type(ddt7) :: prod_k_ddt, prod_k_eqn2_ddt
    type(ddt7) :: sst_f1_ddt
    type(ddt7) :: sst_f2_ddt
    type(ddt7) :: xdterm_ddt
    type(ddt7) :: f_r1_ddt
!   type(ddt7) :: dissp_c_ddt
    type(ddt7) :: timescale_ddt

    continue

    d_source_kw = zero

    cstar = sutherland_constant / tref
!-------------------- to satisfy the nag -------------------------------------80
    beta            = zero
    mu              = zero
    nu              = zero
    f_beta          = one
    dsdt            = zero
!   dissp_c         = zero
    prod_k_ddt      = zero
    prod_k_eqn2_ddt = zero
!   mu_t            = zero

    q           = get_q( eqn_set, n_tot, n_turb                                &
                      , qnode(1:n_tot,i), turb(1:n_turb,i) )
    q_ddt       = get_q_ddt( eqn_set, n_tot, n_turb, kloc, wloc                &
                      , qnode(1:n_tot,i), turb(1:n_turb,i) )

    rho      = q(1)
    rhoinv   = one / rho
    pressure = q(5)
    phi      = zero

    select case (turbulence_model_int)
    case( abid_linear, wilcox1988, wilcox1988_v,                               &
          wilcox_kw88p, wilcox_kw06p, wilcox2006, wilcox2006_v                 &
         )
      tke      = q(kloc)
      omega    = q(wloc)

    case( menter_sst, kw_sst, sst, sst_v, hrles, sst_2003, bsl , kw_sst2003    &
          )
      tke      = q(kloc)
      omega    = q(wloc)
!     f1       = sst_f1(i)
!     beta_0   = f1*beta1       + (one-f1)*beta2
!     gamma_w  = f1*alpha_inner + (one-f1)*alpha_outer
    case( kw_des )
      tke      = q(kloc)
      omega    = q(wloc)

    case( wilcox_kw98, wilcox_kw06,                                            &
          wilcox_asm, easm_ddes, EASMko2003_S, wilcox_les,                     &
          chien )
      tke      = q(kloc) * rhoinv
      omega    = q(wloc) * rhoinv

    case( k_kL_MEAH2013 )
      tke      = q(kloc)
      phi      = q(wloc)

    case default
      tke      = zero
      omega    = zero

    end select

    select case (turbulence_model_int)
    case( abid_linear)
    case( wilcox1988, wilcox1988_v, wilcox_kw88p )
      beta_0     = beta_w88
      betastar_0 = betastar_w88
      sigma_w    = sigma_w_w88
      gamma_w    = gamma_w88
      gamma_w    = beta_0 / betastar_0 - kappa*kappa/(sigma_w*sqrt(betastar_0))
      crossd(i)  = zero
    case( wilcox_kw98 )
      beta_0     = beta_0_w98
      sigma_w    = sigma_w_w98
      betastar_0 = betastar_0_w98
      gamma_w    = gamma_w98
      crossd(i)  = zero
    case( wilcox_asm, easm_ddes, EASMko2003_S)
      beta_0     = beta_0_asm
      sigma_w    = sigma_w_asm
      betastar_0 = betastar_0_asm
      gamma_w    = gamma_asm
      crossd(i)  = zero
    case ( wilcox_kw06, chien )
      beta_0     = beta_0_w06
      betastar_0 = betastar_w06
      sigma_w    = sigma_w_w06
      gamma_w    = gamma_w06
    case ( wilcox_kw06p, wilcox2006, wilcox2006_v )
      beta_0     = beta_0_w06
      betastar_0 = betastar_w06
      sigma_w    = sigma_w_w06
      gamma_w    = gamma_w06
    case ( k_kL_MEAH2013 )
      beta_0     = beta_0_w06
      betastar_0 = betastar_w06
      sigma_w    = sigma_w_w06
      gamma_w    = gamma_w06
    case ( wilcox_les)
      beta_0     = beta_0_w06
      betastar_0 = betastar_w06
      sigma_w    = sigma_w_w06
      gamma_w    = gamma_w06
    case ( menter_sst, kw_sst, sst_v, sst, hrles, bsl, kw_des )
      betastar_0  = betastar_sst
      alpha_inner = beta1 / betastar_0 - kappa*kappa/(sqrt(betastar_0)*sig_w1)
      alpha_outer = beta2 / betastar_0 - kappa*kappa/(sqrt(betastar_0)*sig_w2)
      f1          = sst_f1(i)
      beta_0      = f1*beta1       + (one-f1)*beta2
      gamma_w     = f1*alpha_inner + (one-f1)*alpha_outer
    case ( sst_2003, kw_sst2003 )
      betastar_0  = betastar_sst
      alpha_inner = beta1 / betastar_0 - kappa*kappa/(sqrt(betastar_0)*sig_w1)
      alpha_outer = beta2 / betastar_0 - kappa*kappa/(sqrt(betastar_0)*sig_w2)
      f1          = sst_f1(i)
      beta_0      = f1*beta1       + (one-f1)*beta2
      gamma_w     = f1*gamma1_sst_2003 + (one-f1)*gamma2_sst_2003
    case default
      write(*,*)'Error in routine sst jacob...unknown turbulence_model: ', &
      turbulence_model_int
      call lmpi_conditional_stop(1,&
      'unknown turbulence_model:turb_jacob_source_2eqn')
    end select

!   When using the edge-based terms, we only need to visit this routine
!   one time

!---------------------------------  equation set -----------------------------80
    my_xmach = 0.0_dp
    select case ( eqn_set )
    case ( compressible )
      my_xmach    = xmach
      temperature = gamma * q(5) * rhoinv
      mu          = viscosity_law( cstar, temperature )
      nu          = mu / rho
    case ( incompressible )
      my_xmach    = one
      temperature = one
      mu          = one
      nu          = one
    case default
      call lmpi_conditional_stop(1,'turb_jacob: only for in/compress pg')
    end select

    nu_ddt = nu

    xmr    = my_xmach / re
!   xmr    = xmre
    xmrinv = one /xmr

    re_k      = ( sqrt(tke) * slen(i) / nu ) / xmr
    if ( re_k < des_edge ) then
      lambda    = -two*(re_k/des_edge)**3  + three*(re_k/des_edge)**2
    else
      lambda    = one
    end if

!   Next compute the source (production/destruction) terms
!   This assumes that the blending and cross terms have been previously computed
!   for the residual computation!

    gradv  = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                     &
                      , gradx(3,i), grady(3,i), gradz(3,i)                     &
                      , gradx(4,i), grady(4,i), gradz(4,i) )
    sij     = get_sij    ( gradv )
    wij     = get_wij    ( gradv )
    vort    = get_vort   ( gradv )
    sijsij  = get_sijsij ( sij   )
    wijwij  = get_wijwij ( wij   )
    s_mod   = sqrt( two * sijsij )
    w_mod   = sqrt( abs( two * wijwij ) )

    wws_sum = get_wws( wij, sij )
    sijhat      = sij
    tracepart   = half*(sij(1,1)+sij(2,2)+sij(3,3))
    sijhat(1,1) = sij(1,1) - tracepart
    sijhat(2,2) = sij(2,2) - tracepart
    sijhat(3,3) = sij(3,3) - tracepart
    wwshat_sum  = get_wws( wij, sijhat )

!---------------------- time scales ------------------------------------------80
    timescale_ddt = get_timescale_ddt ( n_tot, n_turb, kloc, wloc              &
                                  , q_ddt, s_mod, vort, nu                     &
                                  , slen(i), xmr, turbulence_model_int         &
                                  , realizability_int                          &
                                  )

!-------------------------- temperature correction ---------------------------80
    ctg = one
    if ( cmu_temp ) then
      ctg = compute_ctg     ( n_grd, n_tot, n_turb, kloc, wloc, q              &
                      , gradx(1:n_grd,i)                                       &
                      , grady(1:n_grd,i)                                       &
                      , gradz(1:n_grd,i), 'cons')
    endif

!---------------------- curvature corrections --------------------------------80
    f2    = one
    f4    = one
    f_r1  = one
    wij_r = zero
    dsdt  = zero

    select case ( curvature_model_int )
    case ( sstrc )
      ri = (vort/s_mod)*(vort/s_mod - one)
      f4 = one / ( one + sstrc_crc*ri )
    case ( smirnov )
     ! This will modify the production terms of the k and omega equations
      f_r1 = smirnov_cc_term ( omega, sij, wij, angular_velocity, dsdt )
    case default
      wij_r = zero
      f_r1  = one
    end select
    wij   = wij + wij_r / a_0

!---------------------- turbulent eddy viscosity -----------------------------80
    mu_t_ddt = get_eddy_viscosity_ddt ( n_tot, n_turb, kloc, wloc, q_ddt       &
                                      , timescale_ddt, sij, wij, xmr, nu       &
                                     , ctg, iflagslen(i), slen(i)              &
                                     , vol(i), turbulence_model_int )

!---------------------------- Reynolds stresses ------------------------------80

    tau_ddt = get_rhotauij_ddt( turbulence_model_int, model_strain_form_int    &
                            , sij, wij, angular_velocity, dsdt, vort, slen(i)  &
                            , q_ddt(1), q_ddt(kloc), timescale_ddt             &
                            , nu_ddt, xmr, cmu_0, turb(1:n_turb,i) )

    ! rhotau_ij is calculated before calling the source term routine
    tau = tau_ddt%f
!   tau(1,1) = rhotauij(1,i)
!   tau(2,2) = rhotauij(2,i)
!   tau(3,3) = rhotauij(3,i)
!   tau(1,2) = rhotauij(4,i)
!   tau(1,3) = rhotauij(5,i)
!   tau(2,3) = rhotauij(6,i)
!   tau(2,1) = tau(1,2)
!   tau(3,1) = tau(1,3)
!   tau(3,2) = tau(2,3)

    ! in order to preserve the readable form of the source term equations
    ! tau must be divided by (Mach/Re) here to be subsequently multiplied
    ! by (Mach/Re) in the production term
    tau    = tau*xmrinv

!--------------------------- Pope correction ---------------------------------80

    rho_ddt    = q_ddt(1)
    tke_ddt    = q_ddt(kloc)
    omega_ddt  = q_ddt(wloc)
    f_beta_ddt = get_f_beta_ddt ( turbulence_model_int, omega_ddt              &
                              , wws_sum, wwshat_sum, xmrinv, betastar_0 )

    beta_ddt        = beta_0 * f_beta_ddt

    f_beta          = f_beta_ddt%f
    beta            = beta_ddt%f
!---------------------------- cross-diffusion --------------------------------80

    gradk  = (/gradx(kloc,i),grady(kloc,i),gradz(kloc,i)/)
    gradw  = (/gradx(wloc,i),grady(wloc,i),gradz(wloc,i)/)

    f_betastar_ddt = get_f_betastar_ddt ( turbulence_model_int, omega_ddt      &
                                        , gradk, gradw, xmr, betastar_0 )

    f_betastar = f_betastar_ddt%f

    select case ( turbulence_model_int )
    case ( wilcox_asm, easm_ddes, EASMko2003_S )

      betastar_ddt    = f_betastar_ddt

    case default

      betastar_ddt    = betastar_0 * f_betastar_ddt

    end select

    betastar        = betastar_ddt%f
!---------------------------------  compressibility --------------------------80
!---------------------------------  compressibility --------------------------80
!   if ( turb_compress_model /= 'none' ) then
      sonic2 = gamma*pressure*rho
      call get_wilcox_compressibility ( turb_compress_model                    &
                                      , sonic2,tke, sst_f1(i)                  &
                                      , betastar_0, f_betastar                 &
                                      , beta_0, f_beta                         &
                                      , betastar, beta                         &
                                      )
!   endif

!-----------------------------------------------------------------------------80
!-------------------------- strain production --------------------------------80
    tau_ddt    = tau
    select case ( prodk_form_int )
      case ( stress_based )
        prod_k_ddt = get_prodk_ddt ( tau_ddt, gradv )

      case ( strain_rate_based )
!       prod_k_ddt = two * ( mu_t_ddt + smidgen ) * sijsij
        prod_k_ddt = mu_t_ddt * s_mod**2

      case ( vorticity_based )
        prod_k_ddt = mu_t_ddt * w_mod * w_mod

    end select
!------------------------------ source terms ---------------------------------80
!-----------------------------------------------------------------------------80
    approx_jac:  if ( use_approximate_jacobians ) then

      d_source_kw = approximate_jacobians_2eqn ( n_turb, turbulence_model_int  &
                   , betastar, beta, ctg, f4, crossd(i), sst_f1(i)             &
                   , rho, tke, omega, phi, vol(i), xmr, lambda )

    else approx_jac

      f_r1_ddt     = f_r1
!     dissp_c_ddt  = dissp_c
      sst_f1_ddt   = sst_f1(i)
      sst_f2_ddt   = sst_f2(i)
      tau_ddt      = tau_ddt * xmrinv

      xdterm_ddt = cross_diffusion_ddt ( rho_ddt, omega_ddt, gradk, gradw      &
                                  , xmr, betastar_0                            &
                                  , turbulence_model_int )

      if (turb_transition) then
        if (iflagslen(i) < 0) then
          mu_t_ddt   = zero
        end if
      end if

      d_keqn_ddt = get_destruction_k_ddt ( turbulence_model_int, betastar_ddt, &
                                    rho_ddt, tke_ddt, omega_ddt,               &
                                    mu_t_ddt, sst_f2_ddt, vol(i), xmr, ctg,    &
                                    lambda )

!     Limit on k-production term
! For sst-2003, *both* k and omega equations are prod_k limited
      select case ( turbulence_model_int )
        case ( k_kL_MEAH2013 )
          stop
        case ( sst_2003, kw_sst2003 )
          prod_k_ddt%f    = min( prod_k_ddt%f , ten*d_keqn_ddt%f/xmr )
          prod_k_eqn2_ddt = prod_k_ddt
        case default
          prod_k_eqn2_ddt = prod_k_ddt
          prod_k_ddt%f    = min( prod_k_ddt%f , twenty*d_keqn_ddt%f/xmr )
      end select

      p_keqn_ddt = get_production_k_ddt ( rho_ddt, tke_ddt                     &
                                  , turbulence_model_int, prod_k_ddt, mu_t_ddt &
                                  , sst_f2_ddt, vol(i), xmr, f_r1_ddt )

      p_eqn2_ddt = get_production_eqn2_ddt ( turbulence_model_int              &
                                  , rho_ddt, timescale_ddt, tke_ddt, omega_ddt &
                                  , prod_k_eqn2_ddt, mu_t_ddt, sst_f2_ddt      &
                                  , gamma_w, f_r1, xmr )

      d_eqn2_ddt = get_destruction_eqn2_ddt ( turbulence_model_int             &
                                  , rho_ddt, timescale_ddt,          omega_ddt &
                                  , beta_ddt, f2, f4, sst_f1_ddt               &
                                  , xdterm_ddt, xmr )

      p_keqn_ddt = t_prod * p_keqn_ddt
      d_keqn_ddt = t_dest * d_keqn_ddt
      p_eqn2_ddt = t_prod * p_eqn2_ddt
      d_eqn2_ddt = t_dest * d_eqn2_ddt

!       if ( composite_jacobian_lhs ) then
        d_source_kw(1,1) = - p_keqn_ddt%d(kloc) + d_keqn_ddt%d(kloc)
        d_source_kw(1,2) = - p_keqn_ddt%d(wloc) + d_keqn_ddt%d(wloc)
        d_source_kw(2,1) = - p_eqn2_ddt%d(kloc) + d_eqn2_ddt%d(kloc)
        d_source_kw(2,2) = - p_eqn2_ddt%d(wloc) + d_eqn2_ddt%d(wloc)

!       else
!         d_source_kw(1,1) = d_keqn_ddt%d(kloc)
!         d_source_kw(1,2) = d_keqn_ddt%d(wloc)
!         d_source_kw(2,1) = d_eqn2_ddt%d(kloc)
!         d_source_kw(2,2) = d_eqn2_ddt%d(wloc)
!       end if

    endif approx_jac

      if (turb_transition) then
        if (iflagslen(i) < 0) then
          d_source_kw = zero
        end if
      end if

!     Add linearizations from just the destruction term since they will
!     add a positive contribution to the diagonal

    if (test_freestream) then
      d_source_kw = zero
    end if

  end function turb_jacob_source_2eqn

!============================ BC_KW_SET_WALLS ================================80
!
!  Sets quantities on viscous walls for Wilcox-19xx,2006 models
!
!=============================================================================80

  subroutine bc_kw_set_walls(eqn_set, n_tot, n_turb, nnodes0, nnodes01         &
                          , nbnode, ibnode, slen_wall                          &
                          , qnode, qturb, ibc, k_wf, omega_wf )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref, xmach, Re
    use bc_names,       only : viscous_wall_rough, viscous_wall_function       &
                             , viscous_wf_trs
!                            , viscous_weak_wall, viscous_weak_trs             &
    use solution_types, only : compressible, incompressible
    use turbulence_info,  only : turbulence_model_int
    use turb_kw_const,  only : beta_w88, beta_0_w98, beta_0_w06                &
                             , beta1, beta_0_asm
    use turb_util,      only : kloc, wloc
    use ddt,            only : ddt7, assignment(=),                            &
                               operator(+), operator(*),                       &
                               operator(-), operator(/)

    integer,                             intent(in)    :: eqn_set
    integer,                             intent(in)    :: n_tot
    integer,                             intent(in)    :: n_turb
    integer,                             intent(in)    :: nnodes0
    integer,                             intent(in)    :: nnodes01
    integer,                             intent(in)    :: nbnode
    integer,  dimension(nbnode),         intent(in)    :: ibnode
    real(dp), dimension(nbnode),         intent(in)    :: slen_wall
!   real(dp), dimension(nbnode),         intent(in)    :: gradn_sqrtk
    real(dp), dimension(n_tot,nnodes01), intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01),intent(inout) :: qturb
    integer,                             intent(in)    :: ibc
    real(dp), dimension(nbnode),         intent(in)    :: k_wf
    real(dp), dimension(nbnode),         intent(in)    :: omega_wf

    integer :: i
    integer :: inode

    real(dp) :: dymin
    real(dp) :: cstar
    real(dp) :: my_xmach
    real(dp) :: xmr
    real(dp) :: beta
!   real(dp) :: tke_off

    real(dp), parameter :: zero    = 0.0_dp
    real(dp), parameter :: sixty   = 60.0_dp
    real(dp), parameter :: hundred = 100.0_dp

    type(ddt7), dimension(n_tot+n_turb)  :: q_ddt
    type(ddt7) :: temp
    type(ddt7) :: rnu

    continue

    beta = zero
    select case (turbulence_model_int)
    case( wilcox1988, wilcox1988_v, wilcox_kw88p )
      beta = beta_w88
    case( wilcox_kw98 )
      beta = beta_0_w98
    case( wilcox_asm, easm_ddes, EASMko2003_S )
      beta = beta_0_asm
    case ( wilcox_kw06, wilcox_kw06p                                    &
         , wilcox2006, wilcox2006_v, chien )
      beta = beta_0_w06
    case ( k_kL_MEAH2013 )
! all quantities zero at a solid wall
    case ( wilcox_les )
      beta = beta_0_w06
    case ( menter_sst, kw_sst, sst_v, sst, sst_2003, kw_sst2003,  &
           bsl, kw_des )
      beta = beta1
    case( abid_linear )
      call lmpi_conditional_stop(1,'bc_kw_set_walls: not for set model')
    end select

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'ebc_kw_set_walls: only in/compress pg')
    end if

    cstar = sutherland_constant / tref

    my_xmach = one
    if ( eqn_set == compressible ) my_xmach = xmach

    xmr = my_xmach/Re
    rnu = one

    do i = 1,nbnode

      inode = ibnode(i)
      if (inode <= nnodes0) then

        dymin = slen_wall(i)
        q_ddt = get_q_ddt( eqn_set, n_tot, n_turb, kloc, wloc &
                   , qnode(1:n_tot,inode), qturb(1:n_turb,inode) )
        temp = gamma * q_ddt(5) / q_ddt(1)
        if ( eqn_set == compressible ) then
          rnu  = viscosity_law_ddt( cstar, temp ) / q_ddt(1)
        endif

        qturb(1,inode) = zero
!       tke_off = gradn_sqrtk(inode) * gradn_sqrtk(inode)
!       if ( turbulence_model_int == 'kw-lag' ) qturb(3,inode) = zero

        select case ( turbulence_model_int )
          case ( wilcox_kw98                                                &
               , wilcox_kw06                                                &
               , wilcox_asm                                                 &
               , EASMko2003_S                                               &
               , wilcox_les                                                 &
               , easm_ddes                                                  &
               , chien                                                      &
               )
            qturb(2,inode) = sixty * q_ddt(1) * rnu%f / beta * (xmr/dymin)**2
          case ( k_kL_MEAH2013, sst_kkl )
!FIXME - figure out correct bc for this model, numbers should be small, but
! not zero
            qturb(1,inode) = zero
            qturb(2,inode) = zero
          case default
            qturb(2,inode) = sixty * rnu%f / beta * (xmr/dymin)**2
        end select

        if ( ibc == viscous_wall_function &
        .or. ibc == viscous_wf_trs        &
!       .or. ibc == viscous_weak_trs      &
!       .or. ibc == viscous_weak_wall     &
             ) then
          qturb(1,inode) = k_wf(i)
          qturb(2,inode) = omega_wf(i)
write(6,'(a,i8,10(1x,f15.5))') 'bc_kw_set_walls', inode                        &
          , qturb(1,inode), qturb(2,inode)
       endif
!  rough wall
        if ( ibc==viscous_wall_rough ) qturb(2,inode) = qturb(2,inode)/hundred

      end if
    end do

  end subroutine bc_kw_set_walls


!========================== FLUX_TURB ========================================80
!
! Flux function for kw-sst bc residual calculation
!
!=============================================================================80
  pure function flux_turb ( ubar, area, ql, qr, n_turb )

    integer,                     intent(in) :: n_turb
    real(dp),                    intent(in) :: ubar
    real(dp),                    intent(in) :: area
    real(dp), dimension(n_turb), intent(in) :: ql
    real(dp), dimension(n_turb), intent(in) :: qr

    real(dp), dimension(n_turb)        :: flux_turb
    real(dp), dimension(n_turb)        :: fluxl
    real(dp), dimension(n_turb)        :: fluxr
    real(dp)                           :: uplus
    real(dp)                           :: uminus

    integer :: i
! inside to outside

    uplus  = half * ( ubar + abs(ubar) )
    uminus = half * ( ubar - abs(ubar) )

    do i = 1, n_turb
      fluxl(i)     = uplus  * ql(i)
      fluxr(i)     = uminus * qr(i)
      flux_turb(i) = ( fluxl(i) + fluxr(i) ) * area
    enddo

! node 2 to node 1
!   ubarm  = -ubar
!   uplus  = half * ( ubarm + abs(ubarm) )
!   uminus = half * ( ubarm - abs(ubarm) )

!   fluxl(1) = uminus * ql(1)
!   fluxl(2) = uminus * ql(2)

!   fluxr(1) = uplus  * qr(1)
!   fluxr(2) = uplus  * qr(2)

!   flux_turb(1) = flux_turb(1) + ( fluxl(1) + fluxr(1) ) * area
!   flux_turb(2) = flux_turb(1) + ( fluxl(2) + fluxr(2) ) * area

  end function flux_turb

!============================= GET_INDEX =====================================80
!
! Switch index if 2D
!
!=============================================================================80
  pure function get_index ( twod, node_pairs_2d, ii ) result ( i )

    integer                             :: i
    logical,                 intent(in) :: twod
    integer, dimension(:,:), intent(in) :: node_pairs_2d
    integer,                 intent(in) :: ii

  continue

    if (twod) then
      i = node_pairs_2d(1,ii)
    else
      i = ii
    end if

  end function get_index

!============================ BC_KEPS_SET_WALLS ==============================80
!
!  Sets quantities on viscous walls for ke
!
!=============================================================================80

  subroutine bc_ke_set_walls( eqn_set,                                        &
                                nnodes0, nnodes01, nbnode, ibnode,            &
                                turb, qnode, n_turb, n_tot,                   &
                                nedgeloc, nedgeloc_2d, nnodes0_2d,            &
                                node_pairs_2d, eptr,                          &
                                x, y, z, xn, yn, zn, ra, vol,                 &
                                nbound, bc, bxn, byn, bzn, elem, nelem,       &
                                gradn_sqrtk )

!   use fluid,          only : gamma, sutherland_constant
!   use info_depr,      only : tref, xmach, Re, skeleton
!   use solution_types, only : compressible, incompressible
!   use turb_ke_const,  only : ke_k_inf, ke_mut_inf, cmu_ke
    use bc_types,       only : bcgrid_type
    use element_types,  only : elem_type

    integer,                                      intent(in)    :: nbnode
    integer,                                      intent(in)    :: n_turb
    integer,                                      intent(in)    :: n_tot
    integer,                                      intent(in)    :: eqn_set
    integer,                                      intent(in)    :: nelem
    integer,                                      intent(in)    :: nnodes0
    integer,                                      intent(in)    :: nnodes01
    integer,                                      intent(in)    :: nedgeloc_2d
    integer,                                      intent(in)    :: nnodes0_2d
    integer,          dimension(nbnode),          intent(in)    :: ibnode
    real(dp),         dimension(n_turb,nnodes01), intent(inout) :: turb
    real(dp),         dimension(n_tot,nnodes01),  intent(in)    :: qnode
    integer,                                      intent(in)    :: nedgeloc
    integer,          dimension(2,nedgeloc),      intent(in)    :: eptr
    integer,          dimension(2,nnodes0_2d),    intent(in)    :: node_pairs_2d
!   real(dp),         dimension(nnodes01)                       :: gradx_sqrtk
!   real(dp),         dimension(nnodes01)                       :: grady_sqrtk
!   real(dp),         dimension(nnodes01)                       :: gradz_sqrtk
    real(dp),         dimension(nnodes01),        intent(in)    :: x, y, z
    real(dp),         dimension(nedgeloc),        intent(in)    :: xn, yn, zn
    real(dp),         dimension(nedgeloc),        intent(in)    :: ra
    real(dp),         dimension(nnodes01),        intent(in)    :: vol
    real(dp),         dimension(nbnode),          intent(in)    :: bxn,byn,bzn
    integer,                                      intent(in)    :: nbound
    type(bcgrid_type),dimension(nbound),          intent(in)    :: bc
    type(elem_type),  dimension(nelem),           intent(in)    :: elem
    real(dp),         dimension(:),               intent(inout) :: gradn_sqrtk
!
    integer :: i,inode

!   real(dp)    :: rho, p
!   real(dp)    :: temp, rnu, cstar
!   real(dp)    :: my_xmach, xmr

    real(dp)    :: xnorm, ynorm, znorm, area
!   real(dp)    :: gradn_sqrtk
!   real(dp)    :: varepsilon_bc, varepsilon_inf

    real(dp), dimension(n_tot)  :: qlocal
    real(dp), dimension(n_turb) :: turb_wall

    real(dp), parameter         :: zero = 0.0_dp

    continue

    if ( .not. grad_sqrtk_setup ) then
      if ( allocated(gradx_sqrtk) ) deallocate(gradx_sqrtk)
      allocate(gradx_sqrtk(nnodes01))
      if ( allocated(grady_sqrtk) ) deallocate(grady_sqrtk)
      allocate(grady_sqrtk(nnodes01))
      if ( allocated(gradz_sqrtk) ) deallocate(gradz_sqrtk)
      allocate(gradz_sqrtk(nnodes01))
      grad_sqrtk_setup = .true.
    endif

!   varepsilon_inf = cmu_ke * ke_k_inf * ke_k_inf / ke_mut_inf

    gradx_sqrtk = zero
    grady_sqrtk = zero
    gradz_sqrtk = zero

    call ke_grad_sqrt_k(nnodes0,nnodes01,nedgeloc,nedgeloc_2d,nnodes0_2d,      &
                              node_pairs_2d,eptr,turb,x,y,z,                   &
                              xn,yn,zn,ra,vol,nbound,bc, n_turb, elem, nelem,  &
                              gradx_sqrtk,grady_sqrtk,gradz_sqrtk)

    do i = 1, nbnode
      inode = ibnode(i)
      if ( inode > nnodes0) cycle

        xnorm  = bxn(i)
        ynorm  = byn(i)
        znorm  = bzn(i)
        area   = sqrt(xnorm*xnorm+ynorm*ynorm+znorm*znorm)
        xnorm  = xnorm / area
        ynorm  = ynorm / area
        znorm  = znorm / area

        gradn_sqrtk(i) =     gradx_sqrtk(inode) * xnorm +                      &
                             grady_sqrtk(inode) * ynorm +                      &
                             gradz_sqrtk(inode) * znorm

      qlocal = qnode(1:n_tot, inode)

      call wall_turbulence_ke( eqn_set, qlocal, gradn_sqrtk(i), turb_wall )

      turb(:,inode) = turb_wall(:)

    end do

  end subroutine bc_ke_set_walls

!============================ WALL_TURBULENCE_KE =============================80
!
!  Sets quantities on viscous walls for ke
!
!=============================================================================80

  subroutine wall_turbulence_ke( eqn_set, qnode, gradn_sqrtk, turb_wall )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref, xmach, Re
    use solution_types, only : compressible

    integer, intent(in) :: eqn_set

    real(dp), dimension(:), intent(in)  :: qnode
    real(dp),               intent(in)  :: gradn_sqrtk
    real(dp), dimension(:), intent(out) :: turb_wall

    real(dp) :: rho,p
    real(dp) :: temp,rnu,cstar
    real(dp) :: my_xmach, xmr

    continue

    cstar = sutherland_constant / tref
    my_xmach = zero

    my_xmach = one
    if (eqn_set == compressible ) my_xmach = xmach

    xmr = my_xmach/Re

    if (eqn_set == compressible) then
      rho  = qnode(1)
      p    = qnode(5)
      temp = gamma * p / rho
      rnu  = viscosity_law( cstar, temp ) / rho
    else
      rnu  = one
    end if

    turb_wall(1) = 0.0_dp
    turb_wall(2) = 2.0_dp*rnu*gradn_sqrtk*gradn_sqrtk*xmr*xmr

      if ( skeleton > 15 )                                                     &
      write(*,'(a,5(1x,f20.10))') 'kewallkewall-1', turb_wall(2), rnu          &
     , gradn_sqrtk

  end subroutine wall_turbulence_ke

!============================== KEPS_GRAD_SQRT_K =============================80
!
! Calculates the gradients of k,e for turbulence model using Green-Gauss
! Note : this routine can definitely be renamed and generalized for turbulence
!
!=============================================================================80

  subroutine ke_grad_sqrt_k(nnodes0,nnodes01,nedgeloc,nedgeloc_2d,nnodes0_2d,  &
                              node_pairs_2d,eptr,turb,x,y,z,                   &
                              xn,yn,zn,ra,vol,nbound,bc, n_turb, elem, nelem,  &
                              gradx_sqrtk,grady_sqrtk,gradz_sqrtk)

    use bc_types,      only : bcgrid_type
    use info_depr,     only : twod
    use bc_names,      only : bc_ignore_2d
    use element_types, only : elem_type

    integer, intent(in) :: n_turb, nedgeloc_2d, nnodes0_2d, nelem
    integer,                                 intent(in)    :: nnodes0, nnodes01
    integer,                                 intent(in)    :: nedgeloc
    integer,      dimension(2,nedgeloc),     intent(in)    :: eptr
    integer,      dimension(2,nnodes0_2d),   intent(in)    :: node_pairs_2d

    real(dp),     dimension(n_turb,nnodes01),intent(inout) :: turb

    real(dp),     dimension(:),              intent(inout) :: gradx_sqrtk
    real(dp),     dimension(:),              intent(inout) :: grady_sqrtk
    real(dp),     dimension(:),              intent(inout) :: gradz_sqrtk

    real(dp),     dimension(nnodes01),       intent(in)    :: x, y, z
    real(dp),     dimension(nedgeloc),       intent(in)    :: xn, yn, zn, ra
    real(dp),     dimension(nnodes01),       intent(in)    :: vol

    integer,                                 intent(in)    :: nbound
    type(bcgrid_type),dimension(nbound),     intent(in)    :: bc
    type(elem_type),  dimension(nelem),      intent(in)    :: elem

    integer     :: i,ib,n,node1,node2

    real(dp) :: area,q3,xnorm,ynorm,znorm

    real(dp), parameter :: zero    = 0.0_dp
    real(dp), parameter :: my_half = 0.5_dp

    continue

! zero out the gradients
! zero out wall values
!   do ib = 1, nbound
!     if ( bc(ib)%ibc == viscous_solid ) then
!       do i = 1, bc(ib)%nbnode
!         turb(1,bc(ib)%ibnode(i)) = zero
!       enddo
!     endif
!   enddo

    do n = 1,nnodes01

      gradx_sqrtk(n) = zero
      grady_sqrtk(n) = zero
      gradz_sqrtk(n) = zero

    end do

    twod_mode : if (twod) then

      edge_loop_2d : do n = 1, nedgeloc_2d

        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area  = ra(n)
        xnorm = area * xn(n)
        znorm = area * zn(n)

        q3 = my_half * (turb(1,node2)**my_half+turb(1,node1)**my_half)

        if (node1 <= nnodes0) then
          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3
        end if

        if (node2 <= nnodes0) then
          gradx_sqrtk(node2) = gradx_sqrtk(node2) - xnorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) - znorm*q3
        end if

      end do edge_loop_2d

    else twod_mode

      edge_loop : do n = 1, nedgeloc
        node1 = eptr(1,n)
        node2 = eptr(2,n)

        area = ra(n)
        xnorm = area * xn(n)
        ynorm = area * yn(n)
        znorm = area * zn(n)

        q3 = my_half * (turb(1,node2)**my_half+turb(1,node1)**my_half)

        if (node1 <= nnodes0) then

          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          grady_sqrtk(node1) = grady_sqrtk(node1) + ynorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3

        end if

        if (node2 <= nnodes0) then

          gradx_sqrtk(node2) = gradx_sqrtk(node2) - xnorm*q3
          grady_sqrtk(node2) = grady_sqrtk(node2) - ynorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) - znorm*q3

        end if

      end do edge_loop

    endif twod_mode

    do ib = 1,nbound

      if (twod .and. bc_ignore_2d(bc(ib)%ibc)) cycle

      call bc_ke_grad_sqrt_k(nnodes0,nnodes01,turb,x,y,z,                      &
                      bc(ib)%nbnode,bc(ib)%ibnode,bc(ib)%nbfacet,              &
                      bc(ib)%nbfaceq,                                          &
                      bc(ib)%f2ntb, n_turb, bc(ib)%f2nqb, elem, nelem,         &
                      bc(ib)%ibc, gradx_sqrtk,grady_sqrtk,gradz_sqrtk)
    end do

! correct gradients for symmetry conditions

    do ib = 1,nbound
      call sqrtk_grad_symmetry(bc(ib)%ibc, bc(ib)%nbnode, bc(ib)%ibnode,       &
                                nnodes0, nnodes01, gradx_sqrtk,                &
                                grady_sqrtk, gradz_sqrtk)
    end do

    if (twod) then

      do i = 1,nnodes0_2d

        node1 = node_pairs_2d(1,i)
        node2 = node_pairs_2d(2,i)

        gradx_sqrtk(node1) = gradx_sqrtk(node1) / vol(node1)
        gradz_sqrtk(node1) = gradz_sqrtk(node1) / vol(node1)

        gradx_sqrtk(node2) = gradx_sqrtk(node1)
        gradz_sqrtk(node2) = gradz_sqrtk(node1)

      end do

    else

      do i = 1,nnodes0

        gradx_sqrtk(i) = gradx_sqrtk(i) / vol(i)
        grady_sqrtk(i) = grady_sqrtk(i) / vol(i)
        gradz_sqrtk(i) = gradz_sqrtk(i) / vol(i)

      end do

    end if


  end subroutine ke_grad_sqrt_k

!=============================== BC_KEPS_GRAD_SQRT_K =========================80
!
! This routine closes off the boundaries for the Green-Gauss gradients
!
!=============================================================================80

  subroutine bc_ke_grad_sqrt_k(nnodes0,nnodes01,turb,x,y,z,nbnode,             &
                                 ibnode,nbfacet,nbfaceq,                       &
                                 f2ntb,n_turb,f2nqb,elem,nelem,ibc,            &
                                 gradx_sqrtk,grady_sqrtk,gradz_sqrtk)

    use info_depr,     only : twod, skeleton
    use element_types, only : elem_type
    use twod_util,     only : yplane_2d, y_coplanar_tol
    use grid_metrics,  only : dual_area_quad
    use lmpi,          only : lmpi_conditional_stop
    use nml_noninertial_reference_frame, only : noninertial

    integer,                                intent(in)    :: nnodes0,nnodes01
    integer,                                intent(in)    :: nbfacet,nbnode
    integer,                                intent(in)    :: nbfaceq
    integer,                                intent(in)    :: n_turb,nelem

    integer,     dimension(nbnode),         intent(in)    :: ibnode
    integer,     dimension(nbfacet,5),      intent(in)    :: f2ntb
    integer,     dimension(nbfaceq,6),      intent(in)    :: f2nqb

    real(dp),    dimension(nnodes01),       intent(in)    :: x,y,z
    real(dp),    dimension(n_turb,nnodes01),intent(in)    :: turb

    real(dp),    dimension(nnodes01),       intent(inout) :: gradx_sqrtk
    real(dp),    dimension(nnodes01),       intent(inout) :: grady_sqrtk
    real(dp),    dimension(nnodes01),       intent(inout) :: gradz_sqrtk
    integer,                                intent(in)    :: ibc

    integer                                               :: n, ielem
    integer                                               :: node1,node2,node3
    integer                                               :: node4,neighbor

    real(dp)                                           :: ax,ay,az
    real(dp)                                           :: bx,by,bz
    real(dp)                                           :: x1,x2,x3,xnorm
    real(dp)                                           :: y1,y2,y3,ynorm
    real(dp)                                           :: z1,z2,z3,znorm
    real(dp)                                           :: c68, c18, c56, c16

    real(dp)                                           :: q3
    real(dp)                                           :: sqrtk1
    real(dp)                                           :: sqrtk2
    real(dp)                                           :: sqrtk3
    real(dp)                                           :: sqrtk4

    real(dp), dimension(4) :: xnorm_q, ynorm_q, znorm_q

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_3   = 3.0_dp
    real(dp), parameter :: my_5   = 5.0_dp
    real(dp), parameter :: my_6   = 6.0_dp
    real(dp), parameter :: my_8   = 8.0_dp
    real(dp), parameter :: my_half = 0.5_dp

    type(elem_type), dimension(nelem), intent(in) :: elem

    continue

    if ( skeleton > 5 ) write(*,*) 'bc_ke_grad_sqrt_k: ibc = ', ibc

    twod_mode : if (twod) then

      c68 = 1.0_dp
      c18 = 0.0_dp

!     close off gradient evaluation on triangular faces
!    (should never get here in 2D...)

      loop_tris_1 : do n = 1, nbfacet

        write(*,*) 'bc_ke_grad_sqrt_k: should not have hit triangular'
        write(*,*) 'boundary faces!'
        call lmpi_conditional_stop(1,&
        'triangular boundary faces:bc_ke_grad_sqrt_k')

      end do loop_tris_1

!     close off gradient evaluation on quadralateral faces

      loop_quads_1 : do n = 1, nbfaceq

        ielem = f2nqb(n,6)

        if (elem(ielem)%type_cell == 'prz' .and. twod) then
          c56 = my_5/my_6
        else
          c56 = my_1
        endif
        c16 = my_1 - c56

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        if (node1 <= nnodes0) then

          xnorm = xnorm_q(1)
          znorm = znorm_q(1)

!         find neighbor of node 1

          neighbor = node2
          if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = node3
          if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = node4

          q3 = c56*turb(1,node1)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3

        end if

        if (node2 <= nnodes0) then

          xnorm = xnorm_q(2)
          znorm = znorm_q(2)

!         find neighbor of node 2

          neighbor = node3
          if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = node1
          if(abs(y(node4)-yplane_2d) < y_coplanar_tol) neighbor = node4

          q3 = c56*turb(1,node2)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node2) = gradx_sqrtk(node2) + xnorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) + znorm*q3

        end if

        if (node3 <= nnodes0) then

          xnorm = xnorm_q(3)
          znorm = znorm_q(3)

!         find neighbor of node 3

          neighbor = node4
          if(abs(y(node1)-yplane_2d) < y_coplanar_tol) neighbor = node1
          if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = node2

          q3 = c56*turb(1,node3)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node3) = gradx_sqrtk(node3) + xnorm*q3
          gradz_sqrtk(node3) = gradz_sqrtk(node3) + znorm*q3

        end if

        if (node4 <= nnodes0) then

          xnorm = xnorm_q(4)
          znorm = znorm_q(4)

!         find neighbor of node 4

          neighbor = node1
          if(abs(y(node2)-yplane_2d) < y_coplanar_tol) neighbor = node2
          if(abs(y(node3)-yplane_2d) < y_coplanar_tol) neighbor = node3

          q3 = c56*turb(1,node4)**my_half + c16*turb(1,neighbor)**my_half

          gradx_sqrtk(node4) = gradx_sqrtk(node4) + xnorm*q3
          gradz_sqrtk(node4) = gradz_sqrtk(node4) + znorm*q3
        end if

      end do loop_quads_1

    else twod_mode

!     close off gradient evaluation on triangular faces

      loop_tris: do n = 1, nbfacet

        ielem = f2ntb(n,5)  ! index to type of element attached to this face

        c68 = my_1
        c18 = my_0

        if (elem(ielem)%type_cell == 'tet') then
!         for linear function preservation during flux closure - tets only
          c68 = my_6/my_8
          c18 = my_1/my_8
        end if

        node1 = ibnode(f2ntb(n,1))
        node2 = ibnode(f2ntb(n,2))
        node3 = ibnode(f2ntb(n,3))

        x1 = x(node1)
        y1 = y(node1)
        z1 = z(node1)
        sqrtk1 = turb(1,node1)**my_half

        x2 = x(node2)
        y2 = y(node2)
        z2 = z(node2)
        sqrtk2 = turb(1,node2)**my_half

        x3 = x(node3)
        y3 = y(node3)
        z3 = z(node3)
        sqrtk3 = turb(1,node3)**my_half

        ax = x2 - x1
        ay = y2 - y1
        az = z2 - z1

        bx = x3 - x1
        by = y3 - y1
        bz = z3 - z1

!           norm points away from grid interior
!           norm magnitude is 1/3 of surface triangle area

        xnorm = -my_half*(ay*bz-by*az)/my_3
        ynorm =  my_half*(ax*bz-bx*az)/my_3
        znorm = -my_half*(ax*by-bx*ay)/my_3

        q3 = c68*sqrtk1 + c18*(sqrtk2+sqrtk3)

        if (node1 <= nnodes0) then

          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          grady_sqrtk(node1) = grady_sqrtk(node1) + ynorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3

        end if

        q3 = c68*sqrtk2 + c18*(sqrtk1+sqrtk3)

        if (node2 <= nnodes0) then

          gradx_sqrtk(node2) = gradx_sqrtk(node2) + xnorm*q3
          grady_sqrtk(node2) = grady_sqrtk(node2) + ynorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) + znorm*q3

        end if

        q3 = c68*sqrtk3 + c18*(sqrtk1+sqrtk2)

        if (node3 <= nnodes0) then

          gradx_sqrtk(node3) = gradx_sqrtk(node3) + xnorm*q3
          grady_sqrtk(node3) = grady_sqrtk(node3) + ynorm*q3
          gradz_sqrtk(node3) = gradz_sqrtk(node3) + znorm*q3

        end if
      end do loop_tris

!     close off gradient evaluation on quadralateral faces

      c68 = my_1
      c18 = my_0

      loop_quads_2 : do n = 1, nbfaceq

        node1 = ibnode(f2nqb(n,1))
        node2 = ibnode(f2nqb(n,2))
        node3 = ibnode(f2nqb(n,3))
        node4 = ibnode(f2nqb(n,4))

        sqrtk1 = turb(1,node1)**my_half
        sqrtk2 = turb(1,node2)**my_half
        sqrtk3 = turb(1,node3)**my_half
        sqrtk4 = turb(1,node4)**my_half

!       get dual norm contributions at each node of the quad face

        call dual_area_quad(nnodes01,x,y,z,node1,node2,node3,node4,noninertial,&
                            xnorm_q,ynorm_q,znorm_q)

        q3 = sqrtk1

        xnorm = xnorm_q(1)
        ynorm = ynorm_q(1)
        znorm = znorm_q(1)

        if (node1 <= nnodes0) then
          gradx_sqrtk(node1) = gradx_sqrtk(node1) + xnorm*q3
          grady_sqrtk(node1) = grady_sqrtk(node1) + ynorm*q3
          gradz_sqrtk(node1) = gradz_sqrtk(node1) + znorm*q3
        end if

        q3 = sqrtk2

        xnorm = xnorm_q(2)
        ynorm = ynorm_q(2)
        znorm = znorm_q(2)

        if (node2 <= nnodes0) then
          gradx_sqrtk(node2) = gradx_sqrtk(node2) + xnorm*q3
          grady_sqrtk(node2) = grady_sqrtk(node2) + ynorm*q3
          gradz_sqrtk(node2) = gradz_sqrtk(node2) + znorm*q3
        end if

        q3 = sqrtk3

        xnorm = xnorm_q(3)
        ynorm = ynorm_q(3)
        znorm = znorm_q(3)

        if (node3 <= nnodes0) then
          gradx_sqrtk(node3) = gradx_sqrtk(node3) + xnorm*q3
          grady_sqrtk(node3) = grady_sqrtk(node3) + ynorm*q3
          gradz_sqrtk(node3) = gradz_sqrtk(node3) + znorm*q3
        end if

        q3 = sqrtk4

        xnorm = xnorm_q(4)
        ynorm = ynorm_q(4)
        znorm = znorm_q(4)

        if (node4 <= nnodes0) then
          gradx_sqrtk(node4) = gradx_sqrtk(node4) + xnorm*q3
          grady_sqrtk(node4) = grady_sqrtk(node4) + ynorm*q3
          gradz_sqrtk(node4) = gradz_sqrtk(node4) + znorm*q3
        end if

      end do loop_quads_2

    end if twod_mode

  end subroutine bc_ke_grad_sqrt_k

!=============================== SQRTK_GRAD_SYMMETRY =========================80
!
! Set known gradients at symmetry planes.
! For example, at symmetry_y:
!   y-gradients of sqrt(k) is zero, e.g. sqrtk_y = 0.
!
!=============================================================================80
  subroutine sqrtk_grad_symmetry(ibc, nbnode, ibnode, nnodes0, nnodes01,       &
                                  gradx_sqrtk, grady_sqrtk, gradz_sqrtk)

    use bc_names,  only : symmetry_x       , symmetry_1_strong,                &
                          symmetry_y       , symmetry_2_strong,                &
                          symmetry_z       , symmetry_3_strong
    use lmpi,      only : lmpi_conditional_stop

    integer, intent(in) :: ibc, nbnode, nnodes0, nnodes01

    integer, dimension(nbnode), intent(in) :: ibnode

    real(dp), dimension(nnodes01),   intent(inout) :: gradx_sqrtk,             &
                                                      grady_sqrtk,             &
                                                      gradz_sqrtk

    integer :: n, node

    real(dp), parameter    :: my_0 = 0.0_dp

  continue

    select case (ibc)

      case (symmetry_x)

        do n = 1,nbnode
          node = ibnode(n)
          if(node <= nnodes0) then
            gradx_sqrtk(node) = my_0
          end if
        end do

      case (symmetry_y)

        do n = 1,nbnode
          node = ibnode(n)
          if(node <= nnodes0) then
            grady_sqrtk(node) = my_0
          end if
        end do

      case (symmetry_z)

        do n = 1,nbnode
          node = ibnode(n)
          if(node <= nnodes0) then
            gradz_sqrtk(node) = my_0
          end if
        end do

      case (symmetry_1_strong, symmetry_2_strong, symmetry_3_strong)
        write(*,*) 'symmetry_1_strong, symmetry_2_strong, symmetry_3_strong '
        write(*,*) 'not taken into account in sqrtk_grad_symmetry.'
        call lmpi_conditional_stop(1,'unknown ibc:sqrtk_grad_symmetry')

    end select

  end subroutine sqrtk_grad_symmetry

!============================ WALL_TURBULENCE_KW_SST =========================80
!
!  Sets quantities on viscous walls for SST, Wilcox-19xx,2006 models
!
!=============================================================================80

  subroutine wall_turbulence_kw_sst( eqn_set, ibc, slen_wall, qnode            &
             , tke_off, turb_wall, k_wf, omega_wf, mu_t_wf )

    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref, xmach, Re
    use bc_names,       only : viscous_wall_rough, viscous_wall_function       &
                             , viscous_wf_trs

    use solution_types, only : compressible, incompressible

    use turb_kw_const,  only : beta_w88, beta_0_w98, beta_0_w06                &
                             , beta1, beta_0_asm

    use turbulence_info, only : turbulence_model_int

    integer,                intent(in)  :: eqn_set
    integer,                intent(in)  :: ibc
    real(dp),               intent(in)  :: slen_wall
    real(dp), dimension(:), intent(in)  :: qnode
    real(dp),               intent(in)  :: tke_off
    real(dp),               intent(in)  :: k_wf
    real(dp),               intent(in)  :: omega_wf
    real(dp),               intent(in)  :: mu_t_wf
    real(dp), dimension(:), intent(out) :: turb_wall

    real(dp) :: rho, p, temp
    real(dp) :: rnu, cstar
    real(dp) :: my_xmach, xmr
    real(dp) :: beta
    real(dp) :: dymin

    real(dp), parameter :: one     = 1.0_dp
    real(dp), parameter :: sixty   = 60.0_dp

    continue

    if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
      call lmpi_conditional_stop(1,'ebc_kw_set_walls: only in/compress pg')
    end if

    cstar       = sutherland_constant / tref
    my_xmach    = one
    rnu         = one
    dymin       = slen_wall

    if (eqn_set == compressible ) my_xmach = xmach
    xmr         = my_xmach/Re

    if (eqn_set == compressible) then
      rho  = qnode(1)
      p    = qnode(5)
      temp = gamma * p / rho
      rnu  = viscosity_law( cstar, temp ) / rho
    end if

    beta = zero
    select case (turbulence_model_int)
    case( wilcox1988, wilcox1988_v, wilcox_kw88p )
      beta = beta_w88
    case( wilcox_kw98 )
      beta = beta_0_w98
    case( wilcox_asm, easm_ddes, EASMko2003_S )
      beta = beta_0_asm
    case ( wilcox_kw06, wilcox_kw06p                                     &
         , wilcox2006, wilcox2006_v, chien )
      beta = beta_0_w06
    case ( k_kL_MEAH2013 )
! all quantities zero at a solid wall
    case ( wilcox_les )
      beta = beta_0_w06
    case ( menter_sst, kw_sst, sst_v, sst, sst_2003, kw_sst2003    &
         , hrles, bsl, kw_des )
      beta = beta1
    case( abid_linear )
!     call lmpi_conditional_stop(1,'bc_kw_set_walls: not for set model')
    end select

    select case ( ibc )

      case ( viscous_wall_rough )
        turb_wall(1) = 0.0_dp
        turb_wall(2) = 60.0_dp*rnu/beta1*(xmr/dymin)**2
        turb_wall(2) = turb_wall(2)/100.0_dp !  rough wall

      case ( viscous_wall_function, viscous_wf_trs )
        turb_wall(1) = k_wf
        turb_wall(2) = omega_wf

      case default

        turb_wall(1) = 0.0_dp

        select case ( turbulence_model_int )
          case ( wilcox_kw98                                                &
               , wilcox_kw06                                                &
               , wilcox_asm                                                 &
               , EASMko2003_S                                               &
               , wilcox_les                                                 &
               , easm_ddes                                                  &
               , chien                                                      &
               )
            turb_wall(2) = sixty * rho * rnu / beta * (xmr/dymin)**2
          case ( k_kL_MEAH2013, sst_kkl )
            turb_wall(2) = zero
          case ( abid_linear )
            turb_wall(2) = 2.0_dp*rnu*tke_off*tke_off*xmr*xmr
          case default
            turb_wall(2) = sixty * rnu / beta * (xmr/dymin)**2
        end select

    end select

    if ( verbose .and. skeleton > 20 )then
    write(6,'(i5,a,15(1x,es20.10))') lmpi_id,'kw_sst:  wallwall', &
      rho, p, turb_wall(1), turb_wall(2), mu_t_wf
    endif

  end subroutine wall_turbulence_kw_sst

!============================== TURB_2EQN_RHOTAUIJ ===========================80
!
! Both compressible and incompressible perfect gas Reynolds stress
!
!=============================================================================80
       function turb_2eqn_rhotauij ( i, eqn_set                                &
                       , nnodes01, n_tot, n_turb, n_grd                        &
                       , qnode, turb, gradx, grady, gradz                      &
                       , slen, xmr, dsdt ) result ( rhotauij )


    use fluid,          only : gamma, sutherland_constant
    use info_depr,      only : tref
    use solution_types, only : compressible
    use turbulence_info, only : turbulence_model_int, model_strain_form_int
    use turb_kw_const,  only : cmu_0, realizability_int

    use turb_kw_const,  only : angular_velocity

    use compute_stress,   only : get_rhotauij
    use turb_functions,   only : get_timescale

    real(dp),   dimension( 6 )                          :: rhotauij
    integer,                              intent(in)    :: n_turb
    integer,                              intent(in)    :: n_tot

    integer,                              intent(in)    :: i
    integer,                              intent(in)    :: eqn_set
    integer,                              intent(in)    :: nnodes01
    integer,                              intent(in)    :: n_grd
    real(dp), dimension(n_tot,nnodes01),  intent(in)    :: qnode
    real(dp), dimension(n_turb,nnodes01), intent(in)    :: turb
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradx
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: grady
    real(dp), dimension(n_grd,nnodes01),  intent(in)    :: gradz
    real(dp), dimension(nnodes01),        intent(in)    :: slen
    real(dp),                             intent(in)    :: xmr
    real(dp), dimension(3,3),             intent(in)    :: dsdt

    real(dp)                 :: rho, rhoinv, temp

    real(dp), dimension(3,3) :: gradv
    real(dp), dimension(3,3) :: sij
    real(dp), dimension(3,3) :: wij

    real(dp), parameter      :: zero     = 0.0_dp
    real(dp), parameter      :: one      = 1.0_dp

    real(dp),   dimension(n_tot+n_turb)  :: q
    real(dp),   dimension(3,3) :: tau
    real(dp)                   :: tke
    real(dp)                   :: mu
    real(dp)                   :: nu
    real(dp)                   :: cstar
    real(dp)                   :: vort
    real(dp)                   :: sijsij
    real(dp)                   :: s_mod
    real(dp)                   :: timescale

  continue

    !---------------- to satisfy the nag -------------------------------------80
    temp            = one
    mu              = one
    nu              = one
    timescale       = zero
    tke             = zero
    !-----------------------------  constants --------------------------------80
    q           = get_q( eqn_set, n_tot, n_turb                                &
                       , qnode(1:n_tot,i), turb(1:n_turb,i) )
    rho         = q(1)
    rhoinv      = one / rho
    !-----------------------------  equation set -----------------------------80
    cstar    = sutherland_constant / tref
    if ( eqn_set == compressible ) then
      temp    = gamma * q(5) * rhoinv
      mu      = viscosity_law( cstar, temp )
      nu      = mu * rhoinv
    end if

    !----------------------------- gradients ---------------------------------80
        gradv  = set_gradv( gradx(2,i), grady(2,i), gradz(2,i)                 &
                          , gradx(3,i), grady(3,i), gradz(3,i)                 &
                          , gradx(4,i), grady(4,i), gradz(4,i) )
    sij     = get_sij( gradv )
    wij     = get_wij( gradv )
    vort    = get_vort( gradv )
    sijsij  = get_sijsij ( sij )
    s_mod   = sqrt( 2.0_dp * sijsij )

    !------------------ time scales ------------------------------------------80
    timescale = get_timescale     ( n_tot, n_turb, kloc, wloc                  &
                                  , q, s_mod, vort, nu                         &
                                  , slen(i), xmr, turbulence_model_int         &
                                  , realizability_int                          &
                                  )

    !------------------ Reynold's stresses -----------------------------------80
    select case (turbulence_model_int)
      case(              wilcox_kw98                                           &
          , wilcox_asm, easm_ddes, EASMko2003_S, wilcox_kw06                   &
          , wilcox_les, chien )
        tke        = q(kloc) * rhoinv
      case default
        tke        = q(kloc)
    end select

    tau = get_rhotauij( turbulence_model_int, model_strain_form_int            &
                      , sij, wij, angular_velocity, dsdt, vort                 &
                      , slen(i), rho, tke, timescale                           &
                      , nu, xmr, cmu_0, turb(1:n_turb,i) )

!   tau    = tau*xmrinv
    rhotauij(1) = tau(1,1)
    rhotauij(2) = tau(2,2)
    rhotauij(3) = tau(3,3)
    rhotauij(4) = tau(1,2)
    rhotauij(5) = tau(1,3)
    rhotauij(6) = tau(2,3)

  end function turb_2eqn_rhotauij


!===================== APPROXIMATE_JACOBIANS_2EQN ============================80
!
!
!=============================================================================80
  function approximate_jacobians_2eqn ( n_turb, turbulence_model_int,          &
                                        betastar, beta, ctg, f4, crossd,       &
                                        sst_f1,                                &
                                        rho, tke, omega, phi, vol, xmr,        &
                                        lambda                                 &
                                      )  result ( d_source_kw )

    use turb_hrles_const, only : c_ep
    use turb_kw_const,  only : beta1, beta2, zeta3_kkl, cmu

!   use turb_kw_const,  only : betastar_w88       &
!                            , betastar_0_w98     &
!                            , betastar_w06       &
!                            , betastar_0_asm     &
!                            , betastar_sst       &
!                            , beta_w88           &
!                            , beta_0_w98         &
!                            , beta_0_w06         &
!                            , beta_0_asm

    use turb_kw_const,    only : c_des

    integer,                intent(in) :: n_turb
    real(dp), dimension(n_turb,n_turb) :: d_source_kw

    integer,                intent(in) :: turbulence_model_int
    real(dp),               intent(in) :: betastar
    real(dp),               intent(in) :: beta
    real(dp),               intent(in) :: ctg
    real(dp),               intent(in) :: f4
    real(dp),               intent(in) :: crossd
    real(dp),               intent(in) :: sst_f1
    real(dp),               intent(in) :: rho
    real(dp),               intent(in) :: tke
    real(dp),               intent(in) :: omega
    real(dp),               intent(in) :: phi
    real(dp),               intent(in) :: vol
    real(dp),               intent(in) :: xmr
    real(dp),               intent(in) :: lambda

    real(dp), parameter :: zero     = 0.0_dp
    real(dp), parameter :: onethird = 1.0_dp/3.0_dp
    real(dp), parameter :: onept5   = 1.5_dp
    real(dp), parameter :: two      = 2.0_dp

    real(dp) :: dkdk
    real(dp) :: dkdk_rans
    real(dp) :: dkdk_sgs
    real(dp) :: dkdw
    real(dp) :: dwdw
    real(dp) :: dwdk
    real(dp) :: xmrinv
    real(dp) :: w1, w2, beta_blend
    real(dp) :: length_sgs

    continue

    d_source_kw = zero
    dkdk        = zero
    dkdw        = zero
    dwdw        = zero
    dwdk        = zero
    xmrinv      = one / xmr
    w1          = sst_f1
    w2          = one - sst_f1

    select case ( turbulence_model_int )

      case ( wilcox1988, wilcox1988_v, wilcox_kw88p,                    &
             wilcox2006, wilcox2006_v, wilcox_kw06p )

        dkdk = ctg * betastar  * omega * xmrinv
        dkdw = ctg * betastar  * tke   * xmrinv
        dwdw = f4 * two * beta * omega * xmrinv

      case (              wilcox_kw06, wilcox_asm,  easm_ddes          &
           , EASMko2003_S, chien )

        dkdk = ctg * betastar  * rho * omega * xmrinv
        dkdw = ctg * betastar  * rho * tke   * xmrinv
        dwdw = f4 * two * beta * rho * omega * xmrinv

      case ( wilcox_kw98, wilcox_les )

        dkdk = ctg * betastar  * rho * omega * xmrinv
        dkdw = ctg * betastar  * rho * tke   * xmrinv
        dwdw = f4 * two * beta * rho * omega * xmrinv                          &
             + rho * abs(crossd) / omega

      case ( k_kl_MEAH2013 )

        dkdk       = ( cmu**0.75 * ( 5.0_dp/2.0_dp) &
                     * tke**1.5_dp / phi ) * xmrinv
        dkdw       = (-cmu**0.75 * tke**2.5_dp / (phi**2) ) * xmrinv
        dwdw       = zero
        dwdw       = zeta3_kkl *  tke**1.5_dp * xmrinv / phi
        dwdk       = zeta3_kkl * 1.5_dp * sqrt(tke) * xmrinv

      case ( menter_sst, kw_sst, sst, sst_v, sst_2003, kw_sst2003, &
             bsl )

        beta_blend = w1*beta1 + w2*beta2
        dkdk       = ctg * betastar            * omega * xmrinv
        dkdw       = ctg * betastar            * tke   * xmrinv
        dwdw       = f4  * two   * beta_blend  * omega * xmrinv

      case ( kw_des )

        length_sgs = c_des * vol**onethird
        dkdk_rans  = betastar                * rho * omega * xmrinv
        dkdk_sgs   = onept5 * rho * sqrt(tke) / length_sgs

        dkdk       = (one-lambda) * dkdk_rans + lambda * dkdk_sgs
        dkdw       = lambda * betastar * rho * tke * xmrinv
        dwdw       = f4 * two * beta * rho * omega * xmrinv

      case ( hrles )

        length_sgs = vol**onethird
        dkdk_rans  = betastar                             * omega * xmrinv
        dkdk_sgs   = c_ep * onept5 * sqrt(tke) / length_sgs

        beta_blend = w1*beta1 + w2*beta2
        dkdk       = w1 * dkdk_rans + w2 * dkdk_sgs
        dkdw       = w1 * betastar                         * tke   * xmrinv
        dwdw       = f4 * two * beta_blend                 * omega * xmrinv    &
                   + abs(crossd) / omega

      case default

        dkdk      = zero
        dkdw      = zero
        dwdw      = zero

      end select

      d_source_kw(1,1) = dkdk
      d_source_kw(1,2) = dkdw
      d_source_kw(2,1) = dwdk
      d_source_kw(2,2) = dwdw

      if ( abs( t_prod) < epsilon(1._dp) .and. &
           abs( t_dest) < epsilon(1._dp)       ) d_source_kw(:,:) = 0._dp

  end function approximate_jacobians_2eqn

  include 'viscosity_law.f90'
  include 'viscosity_law_ddt.f90'
  include 'get_sij.f90'
  include 'get_wij.f90'
  include 'get_wws.f90'
  include 'get_sijsij.f90'
  include 'get_wijwij.f90'
  include 'set_gradv.f90'
  include 'compute_ctg.f90'
    include 'get_vort.f90'
  include 'get_p.f90'
  include 'in_primitive_variables.f90'
  include 'get_q.f90'
  include 'get_q_ddt.f90'
  include 'make_dsdt.f90'

end module turb_2eqn_routines

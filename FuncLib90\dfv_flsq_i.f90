!================================= DFVI_FLSQD ================================80
!
! Linearization of the incompressible viscous fluxes using face-based-lsq.
! (<PERSON>lls Jacobians (4,4)...skipping continuity).
! Note: tx, ty, tz is unit normal to cell face.
!
!=============================================================================80

  pure function dfv_flsq_i( lsq_mref, cell1, cell2,                            &
                            tx, ty, tz, area,                                  &
                            ncell01, xc, yc, zc, amut,                         &
                            flsq_lu, flsq_ja,                                  &
                            slen,                                              &
                            n_q, face, flsq_n, flsq_ni, flsq_nb, bcc  )

    use lsq_constants,  only : wflsq1, flsqn_max, tf
    use bc_types,       only : bcc_type
    use kinddefs,       only : system_i1
    use lsq_types,      only : lsq_ref_type

    type(lsq_ref_type), intent(in) :: lsq_mref

    type(bcc_type), intent(in) :: bcc

    integer, intent(in) :: cell1, cell2, ncell01
    integer, intent(in) :: n_q, face, flsq_n, flsq_ni, flsq_nb

    real(dp), intent(in) :: tx, ty, tz, area

    real(dp), dimension(:), intent(in) :: xc, yc, zc, slen
    real(dp), dimension(ncell01),       intent(in) :: amut

    real(dp), dimension(4,4),     intent(in) :: flsq_lu
    integer,  dimension(flsq_ni), intent(in) :: flsq_ja

    integer :: ii, jj, cella, fb, ibn, ibc

    integer(system_i1), dimension(n_q) :: dqt

    real(dp) :: xiel, etal, ziel
    real(dp) :: ex, ey, ez
    real(dp) :: scalei, a11, deti
    real(dp) :: amutf, wsq, dlgrad, dmgrad, degrad

    real(dp), dimension(3) :: dgrad, dfgrad

    real(dp), dimension(4,flsqn_max) :: lc
    real(dp), dimension(flsqn_max) :: face_wsq

    real(dp), dimension(4) :: f, lc_max
    real(dp), dimension(flsq_n+2) :: dc0, dc1, dc2, dc3
    !...linearization of velocity w/r to cell-centers
    real(dp), dimension(3,flsq_ni+2) :: dugrad, dvgrad, dwgrad

    real(dp), dimension(4,4,flsq_ni+2) :: dfv_flsq_i

    real(dp), dimension(3,3) :: tef, trf

    !integer, parameter :: eqn_set = 1

  continue

    !...Set coordinate transformation.
    trf = mapping_system( tx, ty, tz)

    scalei = flsq_lu(1,1)

    !...ex, ey, ez is unit vector along edge direction
    ex  = 0.5_dp*( xc(cell2) - xc(cell1) )*scalei
    ey  = 0.5_dp*( yc(cell2) - yc(cell1) )*scalei
    ez  = 0.5_dp*( zc(cell2) - zc(cell1) )*scalei

    deti =  1._dp / ( ex*tx + ey*ty + ez*tz )

    tef = tinv_3d( ex, ey, ez, trf(2,1), trf(2,2), trf(2,3),      &
                               trf(3,1), trf(3,2), trf(3,3), deti )

    !...full least squares face

    face_wsq(1:flsq_n+2) = 1._dp
    if( abs(wflsq1-1._dp) > 1.0e-06_dp .and. ( flsq_nb == 0 ) ) then
      face_wsq(1:flsq_n+2) = flsq_wsq( flsq_n, cell1, cell2, ncell01, &
                                       xc, yc, zc, flsq_ja )
    endif

    dc0 = 0._dp
    dc1 = 0._dp
    dc2 = 0._dp
    dc3 = 0._dp
    a11 = 0._dp

    jj  = 0
    do ii=1,flsq_ni
      cella = flsq_ja(ii)
      jj = jj + 1
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             xc(cella), yc(cella), zc(cella), slen(cella) )
    enddo
    jj = jj + 1
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell1), yc(cell1), zc(cell1), slen(cell1) )
    jj = jj + 1
    lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                           xc(cell2), yc(cell2), zc(cell2), slen(cell2) )
    do ii=bcc%flsq_ib(face),bcc%flsq_ib(face+1)-1
      fb = bcc%flsq_jb(ii)
      jj = jj + 1
      lc(:,jj)= lsq_coords( lsq_mref, tf,                                 &
                             bcc%xface(fb), bcc%yface(fb), bcc%zface(fb), &
                             bcc%slenface(fb) )
    enddo

    lc_max = lsq_lc_max( jj, lc, face_wsq )

    do ii=1,jj

      wsq  = face_wsq(ii)
      xiel = lc(1,ii)/lc_max(1)
      etal = lc(2,ii)/lc_max(2)
      ziel = lc(3,ii)/lc_max(3)

      !...d(u,v,w) variables.
      dc0(ii) =       wsq
      dc1(ii) =   xiel*wsq
      dc2(ii) =   etal*wsq
      dc3(ii) =   ziel*wsq

      a11 = a11 + wsq

    enddo

    a11 = 1._dp/a11

! Forward...sequential access to flsq_lu.

    dc_loop : do ii=1,flsq_n+2

      f(1) = dc0(ii)
      f(2) = dc1(ii) - flsq_lu(2,1)*f(1)
      f(3) = dc2(ii) - flsq_lu(3,1)*f(1)
      f(4) = dc3(ii) - flsq_lu(4,1)*f(1)

      f(3) = f(3) - flsq_lu(3,2)*f(2)
      f(4) = f(4) - flsq_lu(4,2)*f(2)

      f(4) = f(4) - flsq_lu(4,3)*f(3)

! Backward...sequential access to flsq_lu.

      f(4) = f(4) * flsq_lu(4,4)
      f(1) = f(1) - flsq_lu(1,4)*f(4)
      f(2) = f(2) - flsq_lu(2,4)*f(4)
      f(3) = f(3) - flsq_lu(3,4)*f(4)

      f(3) = f(3) * flsq_lu(3,3)
      f(1) = f(1) - flsq_lu(1,3)*f(3)
      f(2) = f(2) - flsq_lu(2,3)*f(3)

      f(2) = f(2) * flsq_lu(2,2)
      f(1) = f(1) - flsq_lu(1,2)*f(2)

      f(1) = f(1) * a11 !flsq_lu(1,1)

      !dc3(ii) = f(4)
      !dc2(ii) = f(3)
      !dc1(ii) = f(2)
      dc0(ii) = f(1)

        !...Cartesian gradients at the face.
      dfgrad = lsq_gradc( lsq_mref, f(2:4), lc_max )

      !...gradients in face directions.
      dlgrad = dfgrad(1)*trf(2,1) + dfgrad(2)*trf(2,2) + dfgrad(3)*trf(2,3)
      dmgrad = dfgrad(1)*trf(3,1) + dfgrad(2)*trf(3,2) + dfgrad(3)*trf(3,3)

      !...resolve gradients from face.
      dgrad(1) = tef(1,2)*dlgrad + tef(1,3)*dmgrad
      dgrad(2) = tef(2,2)*dlgrad + tef(2,3)*dmgrad
      dgrad(3) = tef(3,2)*dlgrad + tef(3,3)*dmgrad
      if ( ii <= flsq_ni + 2 ) then
        dugrad(:,ii) = dgrad(:)
        dvgrad(:,ii) = dgrad(:)
        dwgrad(:,ii) = dgrad(:)
      else
        ibn   = bcc%flsq_ib(face) + ii - flsq_ni - 3
        fb    = bcc%flsq_jb(ibn)
        cella = bcc%cell(fb)
        ibc   = bcc%ibc(fb)
        dqt   = dqt_flsq( ibc, bcc%n_qt )
        ibn   = 0
        if ( cella == cell1 ) ibn = flsq_ni+1
        if ( cella == cell2 ) ibn = flsq_ni+2
        dugrad(:,ibn) = dugrad(:,ibn) + dgrad(:)*real( dqt(2), dp )
        dvgrad(:,ibn) = dvgrad(:,ibn) + dgrad(:)*real( dqt(3), dp )
        dwgrad(:,ibn) = dwgrad(:,ibn) + dgrad(:)*real( dqt(4), dp )
      endif

    enddo dc_loop

    !...directional gradients along edge.
    degrad = 0.5_dp*scalei

    !...resolve gradients from edge.
    dugrad(1:3,flsq_ni+1) = dugrad(1:3,flsq_ni+1) - tef(1:3,1)*degrad
    dvgrad(1:3,flsq_ni+1) = dvgrad(1:3,flsq_ni+1) - tef(1:3,1)*degrad
    dwgrad(1:3,flsq_ni+1) = dwgrad(1:3,flsq_ni+1) - tef(1:3,1)*degrad

    dugrad(1:3,flsq_ni+2) = dugrad(1:3,flsq_ni+2) + tef(1:3,1)*degrad
    dvgrad(1:3,flsq_ni+2) = dvgrad(1:3,flsq_ni+2) + tef(1:3,1)*degrad
    dwgrad(1:3,flsq_ni+2) = dwgrad(1:3,flsq_ni+2) + tef(1:3,1)*degrad

    amutf = 0.5_dp*( amut(cell1) + amut(cell2) )

    !...now update the Jacobian entries.
    dfv_flsq_i(:,:,1:flsq_ni+2) = dfvf_i( tx, ty, tz, area, amutf,  &
                                  dugrad, dvgrad, dwgrad, flsq_ni+2 )

  end function dfv_flsq_i

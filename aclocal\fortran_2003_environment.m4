AC_DEFUN([AX_FORTRAN_2003_ENVIRONMENT],
[AC_CACHE_CHECK([fortran 2003 environment available],
 ax_cv_fortran_2003_environment,
 [AC_LANG_PUSH(Fortran)
  AC_LINK_IFELSE(
  [
       program main
       integer :: length, status
       character(len=265) :: value
       print *, command_argument_count()
       call get_command_argument( 1, value, length, status )
       print *, length, status, trim(value)
       end
  ],
  [ax_cv_fortran_2003_environment=yes],
  [ax_cv_fortran_2003_environment=no]
   )
  AC_LANG_POP(Fortran)
 ])
if test "$ax_cv_fortran_2003_environment" != 'no'
then
 AC_DEFINE([HAVE_FORTRAN_2003_ENVIRONMENT],[1],[fortran provides 2003 envronment])
fi
])



! Routines used for parallel mesh embed

module parallel_embed

  use kinddefs, only : dp, i4, i8

  implicit none

  private

  public :: get_embed_grid, embed_the_grid
  public :: embed_renumber_faces
  public :: embed_find_boundary_cells
  public :: embed_add_adjoint_faces
  public :: embed_test_boundary_cells
  public :: embed_make_edgeloc, embed_make_edge
  public :: embed_make_edgel2g, embed_share_edgel2g

  public :: n2e_make_index, n2e_make, n2e_test, n2e_find

  public :: verify_l2g
  public :: clean_lambda_parallel

  logical :: verbose = .false.

contains

!============================== GET_EMBED_GRID ===============================80
!
! Description goes here.
!
!=============================================================================80

  subroutine get_embed_grid( orig, embed, origsoln, embedsoln, origsadj,       &
                             embedsadj, rads, design, verbose_arg )

    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use info_depr,            only : ivisc, ebv_tets
    use nml_noninertial_reference_frame, only : noninertial
    use nml_global,           only : moving_grid
    use lmpi,                 only : lmpi_master, lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer
    use grid_types,           only : grid_type
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_reset_lmpi_xfer, faces_type,         &
                                     faces_from_grid, deallocate_faces
    use bc_names,             only : bc_strong_viscous_adjoint
    use element_defs,         only : initialize_elem, nullify_elem
    use grid_metrics,         only : compute_dual_metrics
    use solution_types,       only : soln_type
    use design_types,         only : design_type
    use solution_adj,         only : sadj_type
    use reconstruction,       only : sumgs
    use rad_defs,             only : rads_type
    use parallel_edge_swap,   only : report_max_dot, embed_edge_swap,          &
                                     gridregisterallcells, gridpack
    use bc_names,             only : need_distance_function
    use distance_function,    only : compute_distance_function
    use noninertials,         only : setup_nonin

    use grid_helper,          only : n2c_type, n2c_from_grid, deallocate_n2c

    type(grid_type),   intent(inout) :: orig
    type(grid_type),   intent(out)   :: embed
    type(soln_type),   intent(inout) :: origsoln
    type(soln_type),   intent(out)   :: embedsoln
    type(sadj_type),   intent(inout) :: origsadj
    type(sadj_type),   intent(out)   :: embedsadj
    type(design_type), intent(in)    :: design
    type(rads_type),   intent(inout) :: rads
    logical, optional, intent(in)    :: verbose_arg

    logical     :: have_strong_boundaries
    integer     :: ielem, ibc, func, iswap, ipyr, itet, ihex
    integer(i8) :: nhexg
    integer(i8), dimension(:), pointer :: hexl2g

    type(faces_type) :: faces
    type(n2c_type) :: n2c

    real(dp)    :: dotfloor

    continue

    if (present(verbose_arg)) verbose = verbose_arg

    call lmpi_xfer(origsoln%q_dof)
    if (ivisc>0) call lmpi_xfer(origsoln%amut)
    if (origsoln%n_turb>0) call lmpi_xfer(origsoln%turb)

    do func = 1, design%nfunctions
      call lmpi_xfer(origsadj%rlam(:,:,func))
    end do

!FIXME modify for symmetry bcs too!

    have_strong_boundaries = .FALSE.
    do ibc = 1, orig%nbound
      have_strong_boundaries = ( have_strong_boundaries                        &
        .or. bc_strong_viscous_adjoint(orig%bc(ibc)%ibc) )
    end do

    if ( have_strong_boundaries ) then
      do func = 1, design%nfunctions
        if (lmpi_master) write(*,*)                                          &
          "fixing the adjoint variable on strong boundaries..."
        if (verbose.and.lmpi_master)                                         &
          write(*,*) "orig rlam...",sum(origsadj%rlam(:,:,func))
        call clean_lambda_parallel(origsoln%viscous_method,                  &
                                   orig, origsadj%rlam(:,:,func),            &
          origsoln%adim, origsoln%turb, origsoln%n_turb)
        if (verbose.and.lmpi_master)                                         &
          write(*,*) "clean rlam...",sum(origsadj%rlam(:,:,func))
      end do
    end if

    embed%project = trim(orig%project) // '_embed'
    embed%partid  = orig%partid
    embed%grid_motion = orig%grid_motion
    embed%origin  = orig%origin
    embed%igrid  = orig%igrid

    embed%nelem = orig%nelem
    allocate(embed%elem(embed%nelem))
    do ielem = 1, embed%nelem
      call nullify_elem(embed%elem(ielem))
      call initialize_elem(embed%elem(ielem), orig%elem(ielem)%type_cell)
    end do

    call faces_from_grid( orig, faces )

    itet = 0
    ipyr = 0
    ihex = 0
    do ielem = 1, orig%nelem
      if ('tet' == orig%elem(ielem)%type_cell) itet = ielem
      if ('pyr' == orig%elem(ielem)%type_cell) ipyr = ielem
      if ('hex' == orig%elem(ielem)%type_cell) ihex = ielem
    end do

    if ( ihex == 0 ) then
       call my_alloc_ptr(hexl2g, 1)
    else
      if (lmpi_master) &
        write(*,*)"creating unique hex local to global numbering..."
      call my_alloc_ptr(hexl2g,orig%elem(ihex)%ncell)
      call embed_make_edgel2g(orig%elem(ihex)%node_per_cell, &
        orig%elem(ihex)%ncell, orig%elem(ihex)%c2n,          &
        orig%nnodes0, orig%nnodes01, orig%l2g,               &
        nhexg, hexl2g)
      if (lmpi_master) write(*,*)"g2l and part..."
      call create_test_g2l(orig)
      if ( associated(orig%part) ) deallocate(orig%part)
      allocate(orig%part(orig%nnodes01))
      call create_part(orig%nnodes0, orig%nnodes01, orig%l2g, orig%part)
      if (lmpi_master) write(*,*)"share ghost hex local to global numbering..."
      call embed_share_edgel2g(orig%elem(ihex)%node_per_cell,  &
        orig%elem(ihex)%ncell, orig%elem(ihex)%c2n, hexl2g,    &
        orig, .false.)
      deallocate(orig%part) ; orig%part => null()
      deallocate(orig%sortedglobal) ; orig%sortedglobal => null()
      deallocate(orig%sortedlocal)  ; orig%sortedlocal  => null()
    end if

    if (lmpi_master) write(*,*)"marking level 0 l2g nodes..."
    call embed_level0(orig, faces, ihex, hexl2g, embed)

    do ielem = 1, embed%nelem
      if (lmpi_master) write(*,*)"creating cell levels 0 and 1 for elem(",&
        ielem, ") : ", trim(orig%elem(ielem)%type_cell)
      call embed_cell(orig, faces, embed, ielem, hexl2g )
    end do

    if ( 0 == itet .and. 0 /= ipyr ) &
      call lmpi_conditional_stop(1,'error, has pyrimids but no tets!')
    if ( 0 /= ipyr ) call embed_pyr_to_tet(orig, faces, embed, ipyr, itet)

    if (1 == embed%nelem .and. 'tet'==embed%elem(1)%type_cell) then
      call gridregisterallcells(embed)
      do iswap = 1, 5
        call report_max_dot( embed )
        dotfloor = 1.0_dp-0.1_dp**iswap
        if (lmpi_master) write(*,'(a,f9.6)')"  swap with floor of", dotfloor
        call embed_edge_swap( embed, dotfloor, orig%nnodesg )
      end do
      call gridpack(embed)
      call report_max_dot( embed )
    end if

    if (lmpi_master) write(*,*) " resize tight..."
    call my_realloc_ptr(embed%l2g,embed%nnodes01)
    call my_realloc_ptr(embed%x,embed%nnodes01)
    call my_realloc_ptr(embed%y,embed%nnodes01)
    call my_realloc_ptr(embed%z,embed%nnodes01)
    do ielem = 1, embed%nelem
      call my_realloc_ptr( embed%elem(ielem)%c2n,                              &
                           embed%elem(ielem)%node_per_cell,                    &
                           embed%elem(ielem)%ncell )
    end do

    if (lmpi_master) write(*,*)"filling level01 local part vectors..."
    deallocate(embed%sortedglobal)
    deallocate(embed%sortedlocal)
    call create_test_g2l(embed)

    if ( associated(embed%part) ) deallocate(embed%part)
    allocate(embed%part(embed%nnodes01))
    call create_part(embed%nnodes0, embed%nnodes01, embed%l2g, embed%part)

    call sumgs(orig%nnodes0, orig%nnodes01, orig%nedgeloc, orig%eptr,         &
               orig%symmetry,     orig%x, orig%y, orig%z,                     &
               orig%r11, orig%r12, orig%r13, orig%r22, orig%r23, orig%r33)

    embedsoln%eqn_set = origsoln%eqn_set

    embedsoln%ndim    = origsoln%ndim
    embedsoln%n_q     = origsoln%n_q
    embedsoln%n_turb  = origsoln%n_turb
    embedsoln%n_tot   = origsoln%n_tot
    embedsoln%n_grd   = origsoln%n_grd
    embedsoln%adim    = origsoln%adim
    embedsoln%njac    = origsoln%njac

    embedsoln%neq0  = embed%nnodes0
    embedsoln%neq01 = embed%nnodes01
    embedsoln%dofg  = embed%nnodesg

    if (lmpi_master) write(*,*)"embed flow and adjoint solution..."
    call embed_solution(                                                       &
      orig,origsoln,origsadj,faces,                                            &
      embed,embedsoln,embedsadj,design,                                        &
      rads, ihex, hexl2g )

    deallocate(hexl2g)

    if (lmpi_master) write(*,*)"set up node lmpi_xfer send and rec..."
    call grid_reset_lmpi_xfer (embed)

    call lmpi_xfer(embed%x)
    call lmpi_xfer(embed%y)
    call lmpi_xfer(embed%z)

    if (lmpi_master) write(*,*)"embed boundary faces..."
    call embed_bc_faces (orig, faces, embed )

    call deallocate_faces(faces)

    call n2c_from_grid(embed, n2c)

    if (lmpi_master) write(*,*)"find boundary cells..."
    call embed_find_boundary_cells( embed, n2c)
    if (lmpi_master) write(*,*)"find phantom adjoint boundary faces..."
    call embed_add_adjoint_faces( embed, n2c )
    if (lmpi_master) write(*,*)"renumber boundary nodes..."
    call embed_renumber_faces (embed)

    call embed_test_boundary_cells(embed)

    if (lmpi_master) write(*,*)"creating loc edges..."
    call embed_make_edgeloc (embed, n2c)

    if (lmpi_master) write(*,*)"creating remaining edges..."
    call embed_make_edge (embed, n2c)

    call deallocate_n2c(n2c)

    if (lmpi_master) &
      write(*,*)"creating unique edge local to global numbering..."
    call my_alloc_ptr(embed%el2g,embed%nedge)
    call embed_make_edgel2g(2, embed%nedge, embed%eptr, &
      embed%nnodes0, embed%nnodes01, embed%l2g,         &
      embed%nedgeg, embed%el2g)

    if (lmpi_master) write(*,*)"share ghost edge local to global numbering..."
    call embed_share_edgel2g(2, embed%nedge, embed%eptr, embed%el2g, &
      embed, .true.)

    if (lmpi_master) write(*,*)"deallocate part and g2l helper arrays..."
    deallocate(embed%part) ; embed%part => null()
    deallocate(embed%sortedglobal) ; embed%sortedglobal => null()
    deallocate(embed%sortedlocal)  ; embed%sortedlocal  => null()

    if (lmpi_master) write(*,*)"allocate grid reals..."
    call my_alloc_ptr(embed%vol, embed%nnodes01)

    call my_alloc_ptr(embed%symmetry, embed%nnodes01)
    call my_alloc_ptr(embed%jag, embed%nnodes01)
    call my_alloc_ptr(embed%xn, embed%nedge)
    call my_alloc_ptr(embed%yn, embed%nedge)
    call my_alloc_ptr(embed%zn, embed%nedge)
    call my_alloc_ptr(embed%ra, embed%nedge)

    if ( ebv_tets ) then
      call my_alloc_ptr(embed%weight,10,embed%nedge)
    else
      call my_alloc_ptr(embed%weight,1,1)
    endif

    call my_alloc_ptr(embed%fptr,      1,1)
    call my_alloc_ptr(embed%cell_vol,  1)
    call my_alloc_ptr(embed%area_face, 1)
    call my_alloc_ptr(embed%xn_face,   1)
    call my_alloc_ptr(embed%yn_face,   1)
    call my_alloc_ptr(embed%zn_face,   1)

    noninertial_grid_speeds : if (noninertial) then
      call my_alloc_ptr(embed%dxdt,      embed%nnodes01)
      call my_alloc_ptr(embed%dydt,      embed%nnodes01)
      call my_alloc_ptr(embed%dzdt,      embed%nnodes01)
      call my_alloc_ptr(embed%facespeed, embed%nedge)
    else
      call my_alloc_ptr(embed%dxdt,      1)
      call my_alloc_ptr(embed%dydt,      1)
      call my_alloc_ptr(embed%dzdt,      1)
      call my_alloc_ptr(embed%facespeed, 1)
    end if noninertial_grid_speeds
    call my_alloc_ptr(embed%res_gcl,   1, 1)

    call my_alloc_ptr(embed%thetax, 1)
    call my_alloc_ptr(embed%thetay, 1)
    call my_alloc_ptr(embed%thetaz, 1)
    call my_alloc_ptr(embed%xorig,  1)
    call my_alloc_ptr(embed%yorig,  1)
    call my_alloc_ptr(embed%zorig,  1)

    do ibc = 1, embed%nbound
      call my_alloc_ptr(embed%bc(ibc)%bxn, embed%bc(ibc)%nbnode)
      call my_alloc_ptr(embed%bc(ibc)%byn, embed%bc(ibc)%nbnode)
      call my_alloc_ptr(embed%bc(ibc)%bzn, embed%bc(ibc)%nbnode)
      call my_alloc_ptr(embed%bc(ibc)%slen_wall, embed%bc(ibc)%nbnode)
      if (moving_grid .or. noninertial) then
        call my_alloc_ptr( embed%bc(ibc)%bdxdt,      embed%bc(ibc)%nbnode )
        call my_alloc_ptr( embed%bc(ibc)%bdydt,      embed%bc(ibc)%nbnode )
        call my_alloc_ptr( embed%bc(ibc)%bdzdt,      embed%bc(ibc)%nbnode )
        call my_alloc_ptr( embed%bc(ibc)%bfacespeed, embed%bc(ibc)%nbnode )
      else
        call my_alloc_ptr( embed%bc(ibc)%bdxdt,      1 )
        call my_alloc_ptr( embed%bc(ibc)%bdydt,      1 )
        call my_alloc_ptr( embed%bc(ibc)%bdzdt,      1 )
        call my_alloc_ptr( embed%bc(ibc)%bfacespeed, 1 )
      end if
    end do

    if (lmpi_master) write(*,*)"make inviscid metrics..."
    call compute_dual_metrics(embed,moving_grid, check_volumes=.FALSE.)

    call my_alloc_ptr(embed%slen,embed%nnodes01)
    call my_alloc_ptr(embed%iflagslen,embed%nnodes01)
    call my_alloc_ptr(embed%des_slen,embed%nnodes01)

   call my_alloc_ptr(embed%cgamma,embed%nnodes01)
   call my_alloc_ptr(embed%slenxn,embed%nnodes01)
   call my_alloc_ptr(embed%slenyn,embed%nnodes01)
   call my_alloc_ptr(embed%slenzn,embed%nnodes01)

    compute_dist_fcn : if (need_distance_function(embed%bc)) then
      if (lmpi_master) write(*,*)"computing distance function..."
      embed%idistfcn = 1
      call compute_distance_function(embed,.false.)
      call lmpi_xfer(embed%slen)
      call lmpi_xfer(embed%iflagslen)
    else
      if (lmpi_master) write(*,*)"skip distance function..."
      embed%idistfcn = 0
    end if compute_dist_fcn

    if ( noninertial ) then
      if (lmpi_master) write(*,*)"Initialize noninertial rotation speeds..."
      call setup_nonin(embed)
    end if

    if (lmpi_master) write(*,*)"get_embed_orig_grid complete."

  end subroutine get_embed_grid


!============================== EMBED_THE_GRID ===============================80
!
! Just embed the grid and write it out, nothing else
! Only tet grids for now, hopefully extend to mixed elements later
!
!=============================================================================80
  subroutine embed_the_grid( orig, verbose_arg )

    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use lmpi,                 only : lmpi_master, lmpi_die,lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer
    use grid_types,           only : grid_type
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_reset_lmpi_xfer, faces_type,         &
                                     faces_from_grid, deallocate_faces
    use element_defs,         only : initialize_elem, nullify_elem
    use parallel_edge_swap,   only : report_max_dot, embed_edge_swap,          &
                                     gridregisterallcells, gridpack
    use global_image,         only : global_image_ugrid
    use bc_util,              only : write_aflr3_mapbc
    use grid_helper,          only : n2c_type, n2c_from_grid, deallocate_n2c
    use file_utils,           only : big_endian_io

    type(grid_type),           intent(inout) :: orig
    logical,         optional, intent(in   ) :: verbose_arg

    integer :: ielem, iswap, ipyr, itet, ihex
    integer(i8) :: nhexg
    integer(i8), dimension(:), pointer :: hexl2g

    type(faces_type) :: faces
    type(n2c_type) :: n2c

    real(dp) :: dotfloor

    type(grid_type) :: embed

    character(256) :: filename

  continue

    if (present(verbose_arg)) verbose = verbose_arg

    embed%project = trim(orig%project) // '_embed'
    embed%nelem = orig%nelem

    allocate(embed%elem(embed%nelem))
    do ielem = 1, embed%nelem
      call nullify_elem(embed%elem(ielem))
      call initialize_elem(embed%elem(ielem), orig%elem(ielem)%type_cell)
    end do

    call faces_from_grid( orig, faces )

    itet = 0
    ipyr = 0
    ihex = 0
    do ielem = 1, orig%nelem
      if ('tet' == orig%elem(ielem)%type_cell) itet = ielem
      if ('pyr' == orig%elem(ielem)%type_cell) ipyr = ielem
      if ('hex' == orig%elem(ielem)%type_cell) ihex = ielem
    end do

    if ( ihex == 0 ) then
       call my_alloc_ptr(hexl2g, 1)
    else
      if (lmpi_master) &
        write(*,*)"creating unique hex local to global numbering..."
      call my_alloc_ptr(hexl2g,orig%elem(ihex)%ncell)
      call embed_make_edgel2g(orig%elem(ihex)%node_per_cell, &
        orig%elem(ihex)%ncell, orig%elem(ihex)%c2n,          &
        orig%nnodes0, orig%nnodes01, orig%l2g,               &
        nhexg, hexl2g)
      if (lmpi_master) write(*,*)"g2l and part..."
      call create_test_g2l(orig)
      if ( associated(orig%part) ) deallocate(orig%part)
      allocate(orig%part(orig%nnodes01))
      call create_part(orig%nnodes0, orig%nnodes01, orig%l2g, orig%part)
      if (lmpi_master) write(*,*)"share ghost hex local to global numbering..."
      call embed_share_edgel2g(orig%elem(ihex)%node_per_cell,  &
        orig%elem(ihex)%ncell, orig%elem(ihex)%c2n, hexl2g,    &
        orig, .false.)
      deallocate(orig%part)         ; orig%part         => null()
      deallocate(orig%sortedglobal) ; orig%sortedglobal => null()
      deallocate(orig%sortedlocal)  ; orig%sortedlocal  => null()
    end if

    if (lmpi_master) write(*,*)"marking level 0 l2g nodes..."
    call embed_level0(orig, faces, ihex, hexl2g, embed)

! Embed the cells

    do ielem = 1, embed%nelem
      if (lmpi_master) write(*,*)"creating cell levels 0 and 1 for elem(",&
        ielem, ") : ", trim(orig%elem(ielem)%type_cell)
      call embed_cell(orig, faces, embed, ielem, hexl2g )
    end do

    deallocate(hexl2g)

    if ( 0 == itet .and. 0 /= ipyr ) &
      call lmpi_conditional_stop(1,'error, has pyrimids but no tets!')
    if ( 0 /= ipyr ) call embed_pyr_to_tet(orig, faces, embed, ipyr, itet)

    if (1 == embed%nelem .and. 'tet'==embed%elem(1)%type_cell) then
      call gridregisterallcells(embed)
      do iswap = 1, 5
        call report_max_dot( embed )
        dotfloor = 1.0_dp-0.1_dp**iswap
        if (lmpi_master) write(*,'(a,f9.6)')"  swap with floor of", dotfloor
        call embed_edge_swap( embed, dotfloor, orig%nnodesg )
      end do
      call gridpack(embed)
      call report_max_dot( embed )
    end if

    if (lmpi_master) write(*,*) " resize tight..."
    call my_realloc_ptr(embed%l2g,embed%nnodes01)
    call my_realloc_ptr(embed%x,embed%nnodes01)
    call my_realloc_ptr(embed%y,embed%nnodes01)
    call my_realloc_ptr(embed%z,embed%nnodes01)
    do ielem = 1, embed%nelem
      call my_realloc_ptr( embed%elem(ielem)%c2n,                              &
                           embed%elem(ielem)%node_per_cell,                    &
                           embed%elem(ielem)%ncell )
    end do

    if (lmpi_master) write(*,*)"filling level01 local part vectors..."
    deallocate(embed%sortedglobal)
    deallocate(embed%sortedlocal)
    call create_test_g2l(embed)

    if ( associated(embed%part) ) deallocate(embed%part)
    allocate(embed%part(embed%nnodes01))
    call create_part(embed%nnodes0, embed%nnodes01, embed%l2g, embed%part)

    if (lmpi_master) write(*,*)"set up node lmpi_xfer send and rec..."
    call grid_reset_lmpi_xfer (embed)

    call lmpi_xfer(embed%x)
    call lmpi_xfer(embed%y)
    call lmpi_xfer(embed%z)

    if (lmpi_master) write(*,*)"embed boundary faces..."
    call embed_bc_faces (orig, faces, embed )

    call deallocate_faces(faces)

    if (lmpi_master) write(*,*)"construct node 2 cell (n2c) structure..."
    call n2c_from_grid(embed, n2c)

!    if (lmpi_master) write(*,*)"find boundary cells..."
!    call embed_find_boundary_cells( embed, n2c)
!   if (lmpi_master) write(*,*)"find phantom adjoint boundary faces..."
!   call embed_add_adjoint_faces( embed, n2c )
    if (lmpi_master) write(*,*)"renumber boundary nodes..."
    call embed_renumber_faces (embed)
!    if (lmpi_master) write(*,*)"test boundary cells..."
!    call embed_test_boundary_cells(embed)

!   if (lmpi_master) write(*,*)"creating loc edges..."
!   call embed_make_edgeloc (embed, n2c)

!   if (lmpi_master) write(*,*)"creating remaining edges..."
!   call embed_make_edge (embed, n2c)

    call deallocate_n2c(n2c)

    if (lmpi_master) write(*,*)"deallocate part and g2l helper arrays..."
    deallocate(embed%part)         ; embed%part         => null()
    deallocate(embed%sortedglobal) ; embed%sortedglobal => null()
    deallocate(embed%sortedlocal)  ; embed%sortedlocal  => null()

    if (lmpi_master) write(*,*)"embed_the_grid complete."

    call write_aflr3_mapbc(  embed, trim(embed%project)//'.mapbc' )
    if ( big_endian_io() ) then
      filename = trim(embed%project)//'.b8.ugrid'
    else
      filename = trim(embed%project)//'.lb8.ugrid'
    end if
    call global_image_ugrid( embed, filename, cl2g_available_arg=.false. )

    if (lmpi_master) write(*,*)"done writing embedded grid."

    call lmpi_die
    stop

  end subroutine embed_the_grid


!=============================== embed_level0 ================================80
!
!=============================================================================80

  subroutine embed_level0 (orig, faces, ihex, hexl2g, embed)

    use allocations,          only : my_alloc_ptr
    use grid_types,           only : grid_type
    use grid_helper,          only : create_test_g2l, faces_type,              &
                                     grid_cell_unique
    use lmpi,                 only : lmpi_master, lmpi_reduce, lmpi_bcast

    type(grid_type),                             intent(in)    :: orig
    type(faces_type),                            intent(in)    :: faces
    integer,                                     intent(in)    :: ihex
    integer(i8), dimension(:),                   intent(in)    :: hexl2g
    type(grid_type),                             intent(inout) :: embed

    integer :: node, edge, node1, node2, face
    integer :: globaledge, globalface, globalnode

    integer :: cell

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: eighth  = 0.125_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: quarter = 0.25_dp

    continue

    embed%nnodes0 = orig%nnodes0

    do edge = 1, orig%nedgeloc
      node1 = orig%eptr(1,edge)
      node2 = orig%eptr(2,edge)
      count_edge_orientation : if ( orig%l2g(node1)                            &
                                  < orig%l2g(node2) ) then
        count_node1_local :if (node1<=orig%nnodes0) then
          embed%nnodes0 = embed%nnodes0 + 1
        end if count_node1_local
      else
        count_node2_local :if (node2<=orig%nnodes0) then
          embed%nnodes0 = embed%nnodes0 + 1
        end if count_node2_local
      end if count_edge_orientation
    end do

    do face = 1, faces%nface
      if ( grid_cell_unique(orig,faces%f2n(:,face)) ) then
        embed%nnodes0 = embed%nnodes0 + 1
      end if
    end do

    count_hex_centers : if ( 0 /= ihex ) then
      do cell = 1, orig%elem(ihex)%ncell
        if ( grid_cell_unique(orig,orig%elem(ihex)%c2n(:,cell)) ) then
          embed%nnodes0 = embed%nnodes0 + 1
        end if
      end do
    end if count_hex_centers

    embed%nnodes01 = embed%nnodes0
    embed%firstemptynode  = 0
    embed%firstemptynode0 = 0
    call my_alloc_ptr(embed%l2g,      embed%nnodes01)
    call my_alloc_ptr(embed%firstn2c, embed%nnodes01)
    call my_alloc_ptr(embed%x,        embed%nnodes01)
    call my_alloc_ptr(embed%y,        embed%nnodes01)
    call my_alloc_ptr(embed%z,        embed%nnodes01)

! copy existing nodes
    do node = 1, orig%nnodes0
      globalnode = orig%l2g(node)
      embed%l2g(node) = globalnode
      embed%x(node) = orig%x(node)
      embed%y(node) = orig%y(node)
      embed%z(node) = orig%z(node)
    end do

    embed%nnodes0 = orig%nnodes0

    create_node_on_edges : do edge = 1, orig%nedgeloc
      node1 = orig%eptr(1,edge)
      node2 = orig%eptr(2,edge)
      edge_orientation : if ( orig%l2g(node1)                                  &
                            < orig%l2g(node2) ) then
        node1_local :if (node1<=orig%nnodes0) then
          globaledge = orig%el2g(edge)
          globalnode = orig%nnodesg+globaledge
          embed%nnodes0 = embed%nnodes0 + 1
          embed%l2g(embed%nnodes0) = globalnode
          embed%x(embed%nnodes0) = my_half * (orig%x(node1)+orig%x(node2))
          embed%y(embed%nnodes0) = my_half * (orig%y(node1)+orig%y(node2))
          embed%z(embed%nnodes0) = my_half * (orig%z(node1)+orig%z(node2))
        end if node1_local
      else
        node2_local :if (node2<=orig%nnodes0) then
          globaledge = orig%el2g(edge)
          globalnode = orig%nnodesg+globaledge
          embed%nnodes0 = embed%nnodes0 + 1
          embed%l2g(embed%nnodes0) = globalnode
          embed%x(embed%nnodes0) = my_half * (orig%x(node1)+orig%x(node2))
          embed%y(embed%nnodes0) = my_half * (orig%y(node1)+orig%y(node2))
          embed%z(embed%nnodes0) = my_half * (orig%z(node1)+orig%z(node2))
        end if node2_local
      end if edge_orientation
    end do create_node_on_edges

    create_node_for_face : do face = 1, faces%nface
      if ( grid_cell_unique(orig,faces%f2n(:,face)) ) then
        globalface = faces%l2g(face)
        globalnode = orig%nnodesg+orig%nedgeg+globalface
        embed%nnodes0 = embed%nnodes0 + 1
        embed%l2g(embed%nnodes0) = globalnode
        embed%x(embed%nnodes0) = quarter * ( orig%x(faces%f2n(1,face)) &
                                           + orig%x(faces%f2n(2,face)) &
                                           + orig%x(faces%f2n(3,face)) &
                                           + orig%x(faces%f2n(4,face)) )
        embed%y(embed%nnodes0) = quarter * ( orig%y(faces%f2n(1,face)) &
                                           + orig%y(faces%f2n(2,face)) &
                                           + orig%y(faces%f2n(3,face)) &
                                           + orig%y(faces%f2n(4,face)) )
        embed%z(embed%nnodes0) = quarter * ( orig%z(faces%f2n(1,face)) &
                                           + orig%z(faces%f2n(2,face)) &
                                           + orig%z(faces%f2n(3,face)) &
                                           + orig%z(faces%f2n(4,face)) )
      end if
    end do create_node_for_face

    create_hex_centers : if ( 0 /= ihex ) then
      create_unique_hex_center : do cell = 1, orig%elem(ihex)%ncell
        if ( grid_cell_unique(orig,orig%elem(ihex)%c2n(:,cell)) ) then
          globalnode = orig%nnodesg+orig%nedgeg+faces%nfaceg+hexl2g(cell)
          embed%nnodes0 = embed%nnodes0 + 1
          embed%l2g(embed%nnodes0) = globalnode
          embed%x(embed%nnodes0) = zero
          embed%y(embed%nnodes0) = zero
          embed%z(embed%nnodes0) = zero
          hex_node : do node = 1, orig%elem(ihex)%node_per_cell
            embed%x(embed%nnodes0) = embed%x(embed%nnodes0) + &
              eighth *  orig%x( orig%elem(ihex)%c2n(node,cell) )
            embed%y(embed%nnodes0) = embed%y(embed%nnodes0) + &
              eighth *  orig%x( orig%elem(ihex)%c2n(node,cell) )
            embed%z(embed%nnodes0) = embed%z(embed%nnodes0) + &
              eighth *  orig%x( orig%elem(ihex)%c2n(node,cell) )
          end do hex_node
        end if
      end do create_unique_hex_center

    end if create_hex_centers

    call create_test_g2l(embed)

    call lmpi_reduce(embed%nnodes0, embed%nnodesg)
    call lmpi_bcast(embed%nnodesg)

    if (lmpi_master) write(*,'(a,i10,a)')" embedded mesh has ",        &
      embed%nnodesg, " nodes"

  end subroutine embed_level0

!=============================== EMBED_CELL ==================================80
!
! embeds the cells in the orig grid to create embed grid cells
! level 0 and level 1 cells, unique cells counted
! c2n and cl2g allocated to embeded guess size
! c2n has global numbering, cl2g is populated
!
!=============================================================================80

  subroutine embed_cell( orig, faces, embed, ielem, hexl2g )

    use grid_types,           only : grid_type
    use grid_helper,          only : level0global, uniqueg2l, &
      grid_canonic_cell, faces_type, faces_find
    use allocations,          only : my_alloc_ptr
    use lmpi,                 only : lmpi_master, lmpi_reduce, lmpi_bcast,     &
                                     lmpi_conditional_stop

    type(grid_type),                             intent(in)    :: orig
    type(faces_type),                            intent(in)    :: faces
    type(grid_type),                             intent(inout) :: embed
    integer,                                     intent(in)    :: ielem
    integer(i8), dimension(:),                   intent(in)    :: hexl2g

    integer, dimension(orig%elem(ielem)%node_per_cell) :: orig_local

    integer,  dimension(:), allocatable :: macro_global
    real(dp), dimension(:), allocatable :: macro_x
    real(dp), dimension(:), allocatable :: macro_y
    real(dp), dimension(:), allocatable :: macro_z

    integer :: macro_nnode, macro_ncell
    integer,  dimension(orig%elem(ielem)%node_per_cell) :: embed_global
    integer,  dimension(orig%elem(ielem)%node_per_cell) :: embed_local
    real(dp), dimension(orig%elem(ielem)%node_per_cell) :: embed_x
    real(dp), dimension(orig%elem(ielem)%node_per_cell) :: embed_y
    real(dp), dimension(orig%elem(ielem)%node_per_cell) :: embed_z

    integer, dimension(:), allocatable    :: n2eindex, n2e
    integer                               :: nn2e
    integer, dimension(4) :: edge_nodes

    integer :: orig_cell, orig_node, orig_edge, orig_face
    integer :: edge, face, node1, node2, node3, node4
    integer, dimension(4) :: face_nodes
    integer :: n_local
    integer :: macro_cell, macro_node
    integer :: embed_node
    integer(i8) :: i8_unique, i8_total

    integer :: node

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: eighth  = 0.125_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_025  = 0.25_dp

 ! make sub-tet 1 near the old node 1 vertex
 ! make sub-tet 2 near the old node 2 vertex
 ! make sub-tet 3 near the old node 3 vertex
 ! make sub-tet 4 near the old node 4 vertex
 ! make sub-tet 5 (sub-tet sharing a face with 1)
 ! make sub-tet 6 (sub-tet sharing a face with 2)
 ! make sub-tet 7 (sub-tet sharing a face with 3)
 ! make sub-tet 8 (sub-tet sharing a face with 4)

  integer, parameter :: macro_tet_nnode = 10
  integer, parameter :: macro_tet_ncell = 8
  integer, parameter, dimension ( 4, macro_tet_ncell ) :: macro_tet =          &
    reshape((/                                                                 &
    1,  5,  6,  7,                                                             &
    2,  9,  8,  5,                                                             &
    3,  8, 10,  6,                                                             &
    4,  7, 10,  9,                                                             &
    5,  7, 10,  6,                                                             &
    8,  9, 10,  5,                                                             &
    8, 10,  6,  5,                                                             &
    9,  7, 10,  5                                                              &
    /),(/4,macro_tet_ncell/))

!                                          n3         edge #    connects nodes
!                                        . /|         ------    --------------
!                                     .   / |           1             e7
!                                  .     /  |           2             e8
!                               .       /   |           3             e9
!                         e13.         /    |           4             e10
!                         .           /e10  |e12        5             e11
!                      .             /      |           6             e12
!                   .               /       |           7             e13
!                .             m18 /        |           8             e14
!               n5----------------n2  m16   n4          9             e15
!               |        e11      |      . /
!               |                 |   .   /
!               |                 |.     /
!               |      m17      . |     /e8
!            e15|         e14.    |e7  /
!               |         .       |   /
!               |      .          |  /
!               |   .             | /
!               |.                |/
!               n6--------------- n1
!                        e9
!
! as tets 6, 1, 4, 3,  6, 1, 3, 2,  6, 2, 3, 5

  integer, parameter :: macro_pri_nnode = 18
  integer, parameter :: macro_pri_ncell = 8
  integer, parameter, dimension ( 6, macro_pri_ncell) :: macro_pri =           &
    reshape((/                                                                 &
     1,  7, 16,  8, 17,  9,                                                    &
     7,  2, 10, 16, 11, 17,                                                    &
    16, 10,  3, 12, 13, 18,                                                    &
     8, 16, 12,  4, 18, 14,                                                    &
    17, 11, 13, 18,  5, 15,                                                    &
     9, 17, 18, 14, 15,  6,                                                    &
     9, 17, 16,  8, 18, 14,                                                    &
    17, 11, 10, 16, 13, 18                                                     &
    /),(/6,macro_pri_ncell/))

!                        e10                          edge #    connects nodes
!               n1----------------n2                  ------    --------------
!               |    .            | \                   1             e6
!               |       .         |  \                  2             e7
!               |          .      |   \                 3             e8
!               |         e6  .   |    \ e7             4             e9
!               |                .|     \               5             e10
!               |                 | .    \              6             e11
!               |                 |    .  \             7             e12
!               |                 |       .n5           8             e13
!             e13      m14      e11      . /
!               |                 |   .   /
!               |                 |.     /
!               |               . |     /
!               |            .    |    /e8
!               |         .       |   /
!               |      .  e9      |  /
!               |   .             | /
!               |.                |/
!               n4----------------n3
!                        e12

  integer, parameter :: macro_pyr_nnode = 14
  integer, parameter :: macro_pyr_ncell = 6
  integer, parameter, dimension ( 5, macro_pyr_ncell) :: macro_pyr =           &
    reshape((/                                                                 &
     6,  7,  8,  9,  5,                                                        &
     6,  9,  8,  7, 14,                                                        &
     1, 10, 14, 13,  6,                                                        &
     2, 11, 14, 10,  7,                                                        &
     3, 12, 14, 11,  8,                                                        &
     4, 13, 14, 12,  9                                                         &
    /),(/5,macro_pyr_ncell/))


!                                e19                  edge #    connects nodes
!                        n6----------------n8         ------    --------------
!                       /.                /|            1             e9
!                      / .               / |            2             e10
!                     /  .              /  |            3             e11
!                    /   .             /   |            4             e12
!                   /    .e17         /    |            5             e13
!                  /e13  .           /e16  |e20         6             e14
!                 /      .          /      |            7             e15
!                /       .  e12    /       |            8             e16
!               n2----------------n4       |            9             e17
!               |        n5.......|........n7          10             e18
!               |       .    e18  |       /            11             e19
!               |      .          |      /             12             e20
!               |     .           |     /
!             e9|    .e11       e14    /e15           face #    contains nodes
!               |   .             |   /               ------    --------------
!               |  .              |  /                  1  m21 front   1-3-4-2
!               | .               | /                   2  m22 back    5-6-8-7
!               |.                |/                    3  m23 bottom  5-7-3-1
!               n1----------------n3                    4  m24 top     6-2-4-8
!                        e10                            5  m25 left    5-1-2-6
!                                          27 center    6  m26 right   7-8-4-3

  integer, parameter :: macro_hex_nnode = 27
  integer, parameter :: macro_hex_ncell = 8

  integer, parameter, dimension ( 8, macro_hex_ncell) :: macro_hex =           &
    reshape((/                                                                 &
     1,  9, 10, 21, 11, 25, 23, 27,                                            &
     9,  2, 21, 12, 25, 13, 27, 24,                                            &
    10, 21,  3, 14, 23, 27, 15, 26,                                            &
    21, 12, 14,  4, 27, 24, 26, 16,                                            &
    11, 25, 23, 27,  5, 17, 18, 22,                                            &
    25, 13, 27, 24, 17,  6, 22, 19,                                            &
    23, 27, 15, 26, 18, 22,  7, 20,                                            &
    27, 24, 26, 16, 22, 19, 20,  8                                             &
    /),(/8,macro_hex_ncell/))

    continue

    macro_nnode = 0
    macro_ncell = 0
    select case (orig%elem(ielem)%type_cell)
    case('tet')
      macro_nnode = macro_tet_nnode
      macro_ncell = macro_tet_ncell
    case('pyr')
      macro_nnode = macro_pyr_nnode
      macro_ncell = macro_pyr_ncell
    case('prz')
      macro_nnode = macro_pri_nnode
      macro_ncell = macro_pri_ncell
    case('hex')
      macro_nnode = macro_hex_nnode
      macro_ncell = macro_hex_ncell
    end select

    if ( 0 == macro_nnode ) then
      call lmpi_conditional_stop(1,'implement parallel_embed:embed_cell')
    end if

    allocate(macro_global(macro_nnode))
    allocate(macro_x(macro_nnode))
    allocate(macro_y(macro_nnode))
    allocate(macro_z(macro_nnode))

    allocate(n2eindex(orig%nnodes01+1))
    n2eindex = 0
    call n2e_make_index(orig%nnodes01, n2eindex, 2, orig%nedge, orig%eptr)
    nn2e = n2eindex(orig%nnodes01+1)-1
    allocate(n2e(nn2e))
    n2e = 0
    call n2e_make(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)
    call n2e_test(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)

    embed%firstemptyc2n = 0

! has zero cells now, but guess it can be the maximum size, realloc later
    embed%elem(ielem)%ncell = 0
    call my_alloc_ptr( embed%elem(ielem)%c2n,               &
                       embed%elem(ielem)%node_per_cell,     &
                       orig%elem(ielem)%ncell * macro_ncell )

    i8_unique = 0
    each_original_cell : do orig_cell = 1, orig%elem(ielem)%ncell

      call grid_canonic_cell(orig,orig%elem(ielem)%c2n(:,orig_cell),orig_local)

      macro_node = 0
      copy_orig_node : do orig_node = 1, orig%elem(ielem)%node_per_cell
        macro_node = macro_node + 1
        macro_x(macro_node) = orig%x(orig_local(orig_node))
        macro_y(macro_node) = orig%y(orig_local(orig_node))
        macro_z(macro_node) = orig%z(orig_local(orig_node))
        macro_global(macro_node) = orig%l2g(orig_local(orig_node))
      end do copy_orig_node

      copy_orig_edge : do orig_edge = 1, orig%elem(ielem)%edge_per_cell
        node1 = orig_local(orig%elem(ielem)%local_e2n(orig_edge,1))
        node2 = orig_local(orig%elem(ielem)%local_e2n(orig_edge,2))
        macro_node = macro_node + 1
        macro_x(macro_node) = my_half * ( orig%x(node1)+ orig%x(node2) )
        macro_y(macro_node) = my_half * ( orig%y(node1)+ orig%y(node2) )
        macro_z(macro_node) = my_half * ( orig%z(node1)+ orig%z(node2) )

        edge_nodes(1) = node1 ; edge_nodes(2) = node2
        edge = n2e_find( edge_nodes, orig%nnodes01, n2eindex, nn2e, n2e,      &
                         2, orig%nedge, orig%eptr)
        if ( edge <= 0 ) then
          write(*,*) 'edge not found'
          call lmpi_conditional_stop(1,'parallel_embed:embed_cell')
        end if
        macro_global(macro_node) = orig%el2g(edge) + orig%nnodesg
      end do copy_orig_edge

      copy_orig_quad : do orig_face = 1, orig%elem(ielem)%face_per_cell
        node1 = orig_local(orig%elem(ielem)%local_f2n(orig_face,1))
        node2 = orig_local(orig%elem(ielem)%local_f2n(orig_face,2))
        node3 = orig_local(orig%elem(ielem)%local_f2n(orig_face,3))
        node4 = orig_local(orig%elem(ielem)%local_f2n(orig_face,4))
        if ( node1 /= node4 ) then
          macro_node = macro_node + 1
          macro_x(macro_node) = my_025 * ( orig%x(node1)+ orig%x(node2) &
                                         + orig%x(node3)+ orig%x(node4) )
          macro_y(macro_node) = my_025 * ( orig%y(node1)+ orig%y(node2) &
                                         + orig%y(node3)+ orig%y(node4) )
          macro_z(macro_node) = my_025 * ( orig%z(node1)+ orig%z(node2) &
                                         + orig%z(node3)+ orig%z(node4) )
          face_nodes = (/node1,node2,node3,node4/)
          face = faces_find(faces, face_nodes)
          if ( face <= 0 ) then
            write(*,*) 'face not found'
            call lmpi_conditional_stop(1,'parallel_embed:embed_cell')
          end if
          macro_global(macro_node) = faces%l2g(face)+ orig%nedgeg + orig%nnodesg
        end if
      end do copy_orig_quad

      copy_orig_hex_center : if (  'hex' == orig%elem(ielem)%type_cell ) then
        macro_node = 27
        macro_x(macro_node) = zero
        macro_y(macro_node) = zero
        macro_z(macro_node) = zero
        hex_node : do node = 1, 8
          macro_x(macro_node) =  macro_x(macro_node) + &
            eighth * orig%x( orig_local(node) )
          macro_y(macro_node) =  macro_y(macro_node) + &
            eighth * orig%y( orig_local(node) )
          macro_z(macro_node) =  macro_z(macro_node) + &
            eighth * orig%z( orig_local(node) )
        end do hex_node
        macro_global(macro_node) = &
          orig%nnodesg+orig%nedgeg+faces%nfaceg+hexl2g(orig_cell)
      end if copy_orig_hex_center

      each_embed_cell_in_macro : do macro_cell = 1, macro_ncell
        n_local = 0
        localize_macro_cell : do embed_node = 1, embed%elem(ielem)%node_per_cell
          select case (orig%elem(ielem)%type_cell)
          case('tet')
            macro_node = macro_tet(embed_node,macro_cell)
          case('pyr')
            macro_node = macro_pyr(embed_node,macro_cell)
          case('prz')
            macro_node = macro_pri(embed_node,macro_cell)
          case('hex')
            macro_node = macro_hex(embed_node,macro_cell)
          end select
          embed_global(embed_node) = macro_global(macro_node)
          embed_x(embed_node) = macro_x(macro_node)
          embed_y(embed_node) = macro_y(macro_node)
          embed_z(embed_node) = macro_z(macro_node)
          if ( level0global(embed,embed_global(embed_node)) ) &
            n_local = n_local + 1
        end do localize_macro_cell

        if ( level0global(embed,embed_global(1)) ) i8_unique = i8_unique + 1

        add_embed_cell : if ( n_local > 0 ) then
          do embed_node = 1, embed%elem(ielem)%node_per_cell
            embed_local(embed_node) = uniqueg2l(embed,embed_global(embed_node))
          end do
          embed%elem(ielem)%ncell = embed%elem(ielem)%ncell + 1
          embed%elem(ielem)%c2n(:,embed%elem(ielem)%ncell) = embed_local
          do embed_node = 1, embed%elem(ielem)%node_per_cell
            embed%x(embed_local(embed_node)) = embed_x(embed_node)
            embed%y(embed_local(embed_node)) = embed_y(embed_node)
            embed%z(embed_local(embed_node)) = embed_z(embed_node)
          end do
        end if add_embed_cell
      end do each_embed_cell_in_macro
    end do each_original_cell
    call lmpi_conditional_stop(0,'parallel_embed:embed_cell')

    deallocate(n2eindex, n2e)

    deallocate(macro_global)
    deallocate(macro_x)
    deallocate(macro_y)
    deallocate(macro_z)

    call lmpi_reduce(i8_unique, i8_total)
    embed%elem(ielem)%ncellg = i8_total
    call lmpi_bcast(embed%elem(ielem)%ncellg)

    if (lmpi_master) write(*,'(a,i1,a,i10,a,a)')&
      " embedded elem(",ielem,") has ",         &
      embed%elem(ielem)%ncellg, " ", embed%elem(ielem)%type_cell

  end subroutine embed_cell

!=============================== EMBED_CELL ==================================80
!
! embeds the cells in the orig grid to create embed grid cells
! level 0 and level 1 cells, unique cells counted
! c2n and cl2g allocated to embeded guess size
! c2n has global numbering, cl2g is populated
!
!=============================================================================80

  subroutine embed_pyr_to_tet( orig, faces, embed, ipyr, itet )

    use grid_types,           only : grid_type
    use grid_helper,          only : level0global, uniqueg2l, &
      grid_canonic_cell, faces_type, faces_find
    use allocations,          only : my_realloc_ptr
    use lmpi,                 only : lmpi_master, lmpi_reduce, lmpi_bcast,     &
                                     lmpi_conditional_stop

    type(grid_type),                             intent(in)    :: orig
    type(faces_type),                            intent(in)    :: faces
    type(grid_type),                             intent(inout) :: embed
    integer,                                     intent(in)    :: ipyr, itet

    integer, dimension(orig%elem(ipyr)%node_per_cell) :: orig_local

    integer,  dimension(:), allocatable :: macro_global
    real(dp), dimension(:), allocatable :: macro_x
    real(dp), dimension(:), allocatable :: macro_y
    real(dp), dimension(:), allocatable :: macro_z

    integer,  dimension(orig%elem(itet)%node_per_cell) :: embed_global
    integer,  dimension(orig%elem(itet)%node_per_cell) :: embed_local
    real(dp), dimension(orig%elem(itet)%node_per_cell) :: embed_x
    real(dp), dimension(orig%elem(itet)%node_per_cell) :: embed_y
    real(dp), dimension(orig%elem(itet)%node_per_cell) :: embed_z

    integer, dimension(:), allocatable    :: n2eindex, n2e
    integer                               :: nn2e
    integer, dimension(2) :: edge_nodes

    integer :: orig_cell, orig_node, orig_edge, orig_face
    integer :: edge, face, node1, node2, node3, node4
    integer, dimension(4) :: face_nodes
    integer :: n_local
    integer :: macro_cell, macro_node
    integer :: embed_node
    integer(i8) :: i8_unique, i8_total

    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_025 = 0.25_dp

!                        e10                          edge #    connects nodes
!               n1----------------n2                  ------    --------------
!               |    .            | \                   1             e6
!               |       .         |  \                  2             e7
!               |          .      |   \                 3             e8
!               |         e6  .   |    \ e7             4             e9
!               |                .|     \               5             e10
!               |                 | .    \              6             e11
!               |                 |    .  \             7             e12
!               |                 |       .n5           8             e13
!             e13      m14      e11      . /
!               |                 |   .   /
!               |                 |.     /
!               |               . |     /
!               |            .    |    /e8
!               |         .       |   /
!               |      .  e9      |  /
!               |   .             | /
!               |.                |/
!               n4----------------n3
!                        e12

  integer, parameter :: macro_pyr_nnode = 14
  integer, parameter :: macro_pyr_ncell = 4
  integer, parameter, dimension ( 4, macro_pyr_ncell) :: macro_pyr =           &
    reshape((/                                                                 &
    14, 10,  7,  6,                                                            &
    14, 11,  8,  7,                                                            &
    14, 12,  9,  8,                                                            &
    14, 13,  6,  9                                                             &
    /),(/4,macro_pyr_ncell/))

    continue

    allocate(macro_global(macro_pyr_nnode))
    allocate(macro_x(macro_pyr_nnode))
    allocate(macro_y(macro_pyr_nnode))
    allocate(macro_z(macro_pyr_nnode))

    allocate(n2eindex(orig%nnodes01+1))
    n2eindex = 0
    call n2e_make_index(orig%nnodes01, n2eindex, 2, orig%nedge, orig%eptr)
    nn2e = n2eindex(orig%nnodes01+1)-1
    allocate(n2e(nn2e))
    n2e = 0
    call n2e_make(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)
    call n2e_test(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)

    embed%firstemptyc2n = 0

! has zero cells now, but guess it can be the maximum size, realloc later
    call my_realloc_ptr( embed%elem(itet)%c2n,              &
                         orig%elem(itet)%node_per_cell,     &
                         embed%elem(itet)%ncell +           &
                         orig%elem(ipyr)%ncell * macro_pyr_ncell )

    i8_unique = 0
    each_original_cell : do orig_cell = 1, orig%elem(ipyr)%ncell

      call grid_canonic_cell(orig,orig%elem(ipyr)%c2n(:,orig_cell),orig_local)

      macro_node = 0
      copy_orig_node : do orig_node = 1, orig%elem(ipyr)%node_per_cell
        macro_node = macro_node + 1
        macro_x(macro_node) = orig%x(orig_local(orig_node))
        macro_y(macro_node) = orig%y(orig_local(orig_node))
        macro_z(macro_node) = orig%z(orig_local(orig_node))
        macro_global(macro_node) = orig%l2g(orig_local(orig_node))
      end do copy_orig_node

      copy_orig_edge : do orig_edge = 1, orig%elem(ipyr)%edge_per_cell
        node1 = orig_local(orig%elem(ipyr)%local_e2n(orig_edge,1))
        node2 = orig_local(orig%elem(ipyr)%local_e2n(orig_edge,2))
        macro_node = macro_node + 1
        macro_x(macro_node) = my_half * ( orig%x(node1)+ orig%x(node2) )
        macro_y(macro_node) = my_half * ( orig%y(node1)+ orig%y(node2) )
        macro_z(macro_node) = my_half * ( orig%z(node1)+ orig%z(node2) )

        edge_nodes(1) = node1 ; edge_nodes(2) = node2
        edge = n2e_find( edge_nodes, orig%nnodes01, n2eindex, nn2e, n2e,      &
                         2, orig%nedge, orig%eptr)
        if ( edge <= 0 ) then
          write(*,*) 'edge not found'
          call lmpi_conditional_stop(1,'parallel_embed:embed_cell')
        end if
        macro_global(macro_node) = orig%el2g(edge) + orig%nnodesg
      end do copy_orig_edge

      copy_orig_quad : do orig_face = 1, orig%elem(ipyr)%face_per_cell
        node1 = orig_local(orig%elem(ipyr)%local_f2n(orig_face,1))
        node2 = orig_local(orig%elem(ipyr)%local_f2n(orig_face,2))
        node3 = orig_local(orig%elem(ipyr)%local_f2n(orig_face,3))
        node4 = orig_local(orig%elem(ipyr)%local_f2n(orig_face,4))
        if ( node1 /= node4 ) then
          macro_node = macro_node + 1
          macro_x(macro_node) = my_025 * ( orig%x(node1)+ orig%x(node2) &
                                         + orig%x(node3)+ orig%x(node4) )
          macro_y(macro_node) = my_025 * ( orig%y(node1)+ orig%y(node2) &
                                         + orig%y(node3)+ orig%y(node4) )
          macro_z(macro_node) = my_025 * ( orig%z(node1)+ orig%z(node2) &
                                         + orig%z(node3)+ orig%z(node4) )
          face_nodes = (/node1,node2,node3,node4/)
          face = faces_find(faces, face_nodes)
          if ( face <= 0 ) then
            write(*,*) 'face not found'
            call lmpi_conditional_stop(1,'parallel_embed:embed_cell')
          end if
          macro_global(macro_node) = faces%l2g(face)+ orig%nedgeg + orig%nnodesg
        end if
      end do copy_orig_quad

      each_embed_cell_in_macro : do macro_cell = 1, macro_pyr_ncell
        n_local = 0
        localize_macro_cell : do embed_node = 1, embed%elem(itet)%node_per_cell
          macro_node = macro_pyr(embed_node,macro_cell)

          embed_global(embed_node) = macro_global(macro_node)
          embed_x(embed_node) = macro_x(macro_node)
          embed_y(embed_node) = macro_y(macro_node)
          embed_z(embed_node) = macro_z(macro_node)
          if ( level0global(embed,embed_global(embed_node)) ) &
            n_local = n_local + 1
        end do localize_macro_cell

        if ( level0global(embed,embed_global(1)) ) i8_unique = i8_unique + 1

        add_embed_cell : if ( n_local > 0 ) then
          do embed_node = 1, embed%elem(itet)%node_per_cell
            embed_local(embed_node) = uniqueg2l(embed,embed_global(embed_node))
          end do
          embed%elem(itet)%ncell = embed%elem(itet)%ncell + 1
          embed%elem(itet)%c2n(:,embed%elem(itet)%ncell) = embed_local
          do embed_node = 1, embed%elem(itet)%node_per_cell
            embed%x(embed_local(embed_node)) = embed_x(embed_node)
            embed%y(embed_local(embed_node)) = embed_y(embed_node)
            embed%z(embed_local(embed_node)) = embed_z(embed_node)
          end do
        end if add_embed_cell
      end do each_embed_cell_in_macro
    end do each_original_cell
    call lmpi_conditional_stop(0,'parallel_embed:embed_cell')

    deallocate(n2eindex, n2e)

    deallocate(macro_global)
    deallocate(macro_x)
    deallocate(macro_y)
    deallocate(macro_z)

    call lmpi_reduce(i8_unique, i8_total)
    embed%elem(itet)%ncellg = embed%elem(itet)%ncellg + i8_total
    call lmpi_bcast(embed%elem(itet)%ncellg)

    if (lmpi_master) write(*,'(a,i1,a,i10,a,a)')&
      " embedded elem(",itet,") has ",          &
      embed%elem(itet)%ncellg, " ", embed%elem(itet)%type_cell

  end subroutine embed_pyr_to_tet

!=============================== EMBED_MAKE_EDGELOC ==========================80
!
! calculated uniques edges out to nedgeloc (c2e and eptr)
! c2n is expected in local numbering.
!
!=============================================================================80

  subroutine embed_make_edgeloc (embed, n2c )

    use grid_types,           only : grid_type
    use allocations,          only : my_alloc_ptr
    use grid_helper,          only : n2c_type

    type(grid_type),                             intent(inout) :: embed
    type(n2c_type),                              intent(in)    :: n2c

    integer :: edge, elem, cell, iedge
    integer :: nodeindex1, nodeindex2
    integer :: localnode1, localnode2

    continue

    edge = 0

    elem_allocation : do elem = 1, embed%nelem
      call my_alloc_ptr(embed%elem(elem)%c2e,&
        embed%elem(elem)%edge_per_cell,embed%elem(elem)%ncell)
    end do elem_allocation

    elem_loop : do elem = 1, embed%nelem
      cell_loop : do cell = 1, embed%elem(elem)%ncell
        cell_edge : do iedge = 1, embed%elem(elem)%edge_per_cell
          nodeindex1 = embed%elem(elem)%local_e2n(iedge,1)
          nodeindex2 = embed%elem(elem)%local_e2n(iedge,2)
          localnode1 = embed%elem(elem)%c2n(nodeindex1,cell)
          localnode2 = embed%elem(elem)%c2n(nodeindex2,cell)
          loc_edge : if (   localnode1 <= embed%nnodes0                        &
                       .or. localnode2 <= embed%nnodes0  ) then
            add_new_edge : if (embed%elem(elem)%c2e(iedge,cell) == 0 ) then
              edge = edge + 1
              call add_edge_to_all_cells( localnode1, localnode2, edge,        &
                                          embed, n2c)
            end if add_new_edge
          end if loc_edge
        end do cell_edge
      end do cell_loop
    end do elem_loop

    embed%nedgeloc = edge

  end subroutine embed_make_edgeloc

!=============================== EMBED_MAKE_EDGE =============================80
!
! calculated uniques edges out to nedge (c2e and eptr)
! c2n is expected in local numbering.
!
!=============================================================================80

  subroutine embed_make_edge (embed, n2c )

    use grid_types,           only : grid_type
    use allocations,          only : my_alloc_ptr
    use lmpi,                 only : lmpi_conditional_stop
    use grid_helper,          only : n2c_type

    type(grid_type),                             intent(inout) :: embed
    type(n2c_type),                              intent(in)    :: n2c

    integer :: edge, elem, cell, iedge
    integer :: nodeindex1, nodeindex2
    integer :: localnode1, localnode2
    integer :: globalnode1, globalnode2

    continue

    edge = embed%nedgeloc

    elem_loop : do elem = 1, embed%nelem
      cell_loop : do cell = 1, embed%elem(elem)%ncell
        cell_edge : do iedge = 1, embed%elem(elem)%edge_per_cell
          add_new_edge : if (embed%elem(elem)%c2e(iedge,cell) == 0 ) then
            nodeindex1 = embed%elem(elem)%local_e2n(iedge,1)
            nodeindex2 = embed%elem(elem)%local_e2n(iedge,2)
            localnode1 = embed%elem(elem)%c2n(nodeindex1,cell)
            localnode2 = embed%elem(elem)%c2n(nodeindex2,cell)
            edge = edge + 1
            call add_edge_to_all_cells( localnode1, localnode2, edge,        &
                                        embed, n2c)
          end if add_new_edge
        end do cell_edge
      end do cell_loop
    end do elem_loop

    check_elem : do elem = 1, embed%nelem
      check_cell : do cell = 1, embed%elem(elem)%ncell
        check_cell_edge : do iedge = 1, embed%elem(elem)%edge_per_cell
          zero_edge : if (embed%elem(elem)%c2e(iedge,cell) == 0) then
            write(*,*) "c2e zero ",elem,cell,iedge
            call lmpi_conditional_stop(1,'c2e zero in make_edge')
          end if zero_edge
        end do check_cell_edge
      end do check_cell
    end do check_elem
    call lmpi_conditional_stop(0,'c2e zero in make_edge')

    embed%nedge = edge

    call my_alloc_ptr(embed%eptr,2,embed%nedge)

    fill_elem : do elem = 1, embed%nelem
      fill_cell : do cell = 1, embed%elem(elem)%ncell
        fill_cell_edge : do iedge = 1, embed%elem(elem)%edge_per_cell
          nodeindex1 = embed%elem(elem)%local_e2n(iedge,1)
          nodeindex2 = embed%elem(elem)%local_e2n(iedge,2)
          localnode1 = embed%elem(elem)%c2n(nodeindex1,cell)
          localnode2 = embed%elem(elem)%c2n(nodeindex2,cell)
          edge = embed%elem(elem)%c2e(iedge,cell)
          globalnode1 = embed%l2g(localnode1)
          globalnode2 = embed%l2g(localnode2)
          orient_edge_inc_nodes : if ( globalnode1 < globalnode2) then
            embed%eptr(1,edge) = localnode1
            embed%eptr(2,edge) = localnode2
          else
            embed%eptr(1,edge) = localnode2
            embed%eptr(2,edge) = localnode1
          end if orient_edge_inc_nodes
        end do fill_cell_edge
      end do fill_cell
    end do fill_elem

    do edge = 1, embed%nedge
      if (embed%eptr(1,edge) == 0 .or. embed%eptr(2,edge) == 0) then
        write(*,*) "eptr zero ",edge,embed%eptr(1,edge),embed%eptr(2,edge)
        call lmpi_conditional_stop(1,'eptr zero in make_edge')
      end if
    end do
    call lmpi_conditional_stop(0,'eptr zero in make_edge')

  end subroutine embed_make_edge

!============================ add_edge_to_all_cells ==========================80
!
!=============================================================================80

  subroutine add_edge_to_all_cells ( node1, node2, edge,                       &
                                     embed, n2c )

    use grid_types,           only : grid_type
    use grid_helper,          only : n2c_type

    integer,                              intent(in)    :: node1, node2, edge
    type(grid_type),                      intent(inout) :: embed
    type(n2c_type),                       intent(in)    :: n2c

    integer :: comprow_index, elem, cell, iedge
    integer :: nodeindex1, nodeindex2
    integer :: localnode1, localnode2

    continue

    cells_surrounding_node1 :do comprow_index = n2c%first_for(node1), &
                                                n2c%first_for(node1+1)-1
      elem = n2c%elem_and_cell(1,comprow_index)
      cell = n2c%elem_and_cell(2,comprow_index)
      cell_edge : do iedge = 1, embed%elem(elem)%edge_per_cell
        nodeindex1 = embed%elem(elem)%local_e2n(iedge,1)
        nodeindex2 = embed%elem(elem)%local_e2n(iedge,2)
        localnode1 = embed%elem(elem)%c2n(nodeindex1,cell)
        localnode2 = embed%elem(elem)%c2n(nodeindex2,cell)
        if (    min(localnode1,localnode2) == min(node1, node2)                &
          .and. max(localnode1,localnode2) == max(node1, node2) ) then
          embed%elem(elem)%c2e(iedge,cell) = edge
        end if
      end do cell_edge
    end do cells_surrounding_node1

  end subroutine add_edge_to_all_cells

!=============================================================================80
!
!=============================================================================80

  subroutine embed_make_edgel2g (ldim, ne, e2n, nnodes0, nnodes01, l2g, &
    nglobal, el2g )

    use kinddefs,             only : i8
    use lmpi,                 only : lmpi_id, lmpi_nproc, lmpi_master,         &
                                     lmpi_gather, lmpi_bcast

    integer,                          intent(in)    :: ldim, ne
    integer,     dimension(ldim, ne), intent(in)    :: e2n
    integer,                          intent(in)    :: nnodes0, nnodes01
    integer,     dimension(nnodes01), intent(in)    :: l2g
    integer(i8),                      intent(out)   :: nglobal
    integer(i8), dimension(ne),       intent(out)   :: el2g

    integer :: ie, node
    integer, dimension(ldim) :: local_nodes, global_nodes
    integer :: smallest_global, smallest_node
    integer :: unique

    integer,     dimension(:), allocatable :: unique_on_proc
    integer(i8), dimension(:), allocatable :: first_on_proc
    integer(i8) :: global

    integer :: processor

    continue

    unique = 0

    do ie = 1, ne
      do node = 1, ldim
        local_nodes(node) = e2n(node,ie)
        global_nodes(node) = l2g(local_nodes(node))
      end do
      smallest_node = 1
      smallest_global = global_nodes(smallest_node)
      do node = 2, ldim
        if ( global_nodes(node) < smallest_global ) then
          smallest_node = node
          smallest_global = global_nodes(smallest_node)
        end if
      end do
      if (local_nodes(smallest_node) <= nnodes0 ) then
        unique = unique + 1
      end if
    end do

    allocate( unique_on_proc( lmpi_nproc ) )
    allocate( first_on_proc( lmpi_nproc ) )

    call lmpi_gather(unique,unique_on_proc)

    if (lmpi_master) then
      nglobal = 0
      sum_global_total : do processor = 1, lmpi_nproc
        nglobal = nglobal + unique_on_proc(processor)
      end do sum_global_total
      first_on_proc(1) = 0
      do processor = 2, lmpi_nproc
        first_on_proc(processor)=&
          first_on_proc(processor-1)+unique_on_proc(processor-1)
      end do
    end if
    call lmpi_bcast(nglobal)
    call lmpi_bcast(first_on_proc)

    global = first_on_proc(lmpi_id+1)

    deallocate( unique_on_proc )
    deallocate( first_on_proc )

    do ie = 1, ne
      do node = 1, ldim
        local_nodes(node) = e2n(node,ie)
        global_nodes(node) = l2g(local_nodes(node))
      end do
      smallest_node = 1
      smallest_global = global_nodes(smallest_node)
      do node = 2, ldim
        if ( global_nodes(node) < smallest_global ) then
          smallest_node = node
          smallest_global = global_nodes(smallest_node)
        end if
      end do
      if (local_nodes(smallest_node) <= nnodes0 ) then
        global = global + 1
        el2g(ie) = global
      end if
    end do

  end subroutine embed_make_edgel2g

!============================ embed_share_edgel2g ============================80
!
!=============================================================================80

  subroutine embed_share_edgel2g (ldim, ne, e2n, el2g, embed, reset_edge_xfer)

    use kinddefs,             only : i8
    use grid_types,           only : grid_type
    use grid_helper,          only : lookupg2l
    use lmpi,                 only : lmpi_nproc, lmpi_id, lmpi_master, &
                                     lmpi_alltoall, lmpi_alltoallv,    &
                                     lmpi_conditional_stop
    use lmpi_app,             only : lmpi_reset_sendrecv

    integer,                          intent(in)    :: ldim, ne
    integer,     dimension(ldim,ne),  intent(in)    :: e2n
    integer(i8), dimension(ne),       intent(inout) :: el2g
    type(grid_type),                  intent(in)    :: embed
    logical,                          intent(in)    :: reset_edge_xfer

    integer :: ie
    integer :: node
    integer :: smallest_node, smallest_global
    integer, dimension(ldim) :: local_nodes, global_nodes
    integer :: serverid, processor

    integer, dimension(:), allocatable :: message_size_1, message_size_2
    integer :: message_total_1, message_total_2

    integer, dimension(:), allocatable :: next_position

    integer, dimension(:), allocatable :: local_edge_1, local_edge_2
    integer, dimension(:,:), allocatable :: edge_nodes_1, edge_nodes_2

    integer, dimension(:), allocatable    :: n2eindex, n2e
    integer                               :: nn2e

    integer :: found

    integer, dimension(:), allocatable :: global_edge_1, global_edge_2
    integer, dimension(:), allocatable :: index_1, index_2

    continue

    allocate(message_size_1(lmpi_nproc))
    allocate(message_size_2(lmpi_nproc))
    message_size_1 = 0
    message_size_2 = 0

    count_empty_edges : do ie = 1, ne
      is_empty1 : if (el2g(ie) == 0 ) then
        do node = 1, ldim
          local_nodes(node) = e2n(node,ie)
          global_nodes(node) = embed%l2g(local_nodes(node))
        end do
        smallest_node = 1
        smallest_global = global_nodes(smallest_node)
        do node = 2, ldim
          if ( global_nodes(node) < smallest_global ) then
            smallest_node = node
            smallest_global = global_nodes(smallest_node)
          end if
        end do
        serverid = embed%part(local_nodes(smallest_node))
        bad_node_server : if ( 0 >= serverid ) then
          write(*,*) lmpi_id, 'share_edgel2g part problem', ie, serverid
          call lmpi_conditional_stop(1,'share_edgel2g')
        end if bad_node_server
        message_size_1(serverid)=message_size_1(serverid)+1
      end if is_empty1
    end do count_empty_edges
    call lmpi_conditional_stop(0,'share_edgel2g')

    call lmpi_alltoall( message_size_1, message_size_2 )

    message_total_1 = sum( message_size_1 )
    message_total_2 = sum( message_size_2 )

    allocate(next_position(lmpi_nproc+1))

    next_position = 0
    next_position(1) = 1
    calc_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + message_size_1(processor)
    end do calc_next_position

    allocate(local_edge_1(message_total_1))
    allocate(local_edge_2(message_total_2))
    local_edge_1 = 0
    local_edge_2 = 0

    allocate(edge_nodes_1(ldim, message_total_1))
    allocate(edge_nodes_2(ldim, message_total_2))
    edge_nodes_1 = 0
    edge_nodes_2 = 0


    request_empty_edges : do ie = 1, ne
      is_empty2 : if (el2g(ie) == 0 ) then
        do node = 1, ldim
          local_nodes(node) = e2n(node,ie)
          global_nodes(node) = embed%l2g(local_nodes(node))
        end do
        smallest_node = 1
        smallest_global = global_nodes(smallest_node)
        do node = 2, ldim
          if ( global_nodes(node) < smallest_global ) then
            smallest_node = node
            smallest_global = global_nodes(smallest_node)
          end if
        end do
        serverid = embed%part(local_nodes(smallest_node))
        local_edge_1(next_position(serverid)) = ie
        do node = 1, ldim
          edge_nodes_1(node,next_position(serverid)) = global_nodes(node)
        end do
        next_position(serverid) = next_position(serverid) +1
      end if is_empty2
    end do request_empty_edges

    deallocate( next_position )

    call lmpi_alltoallv( edge_nodes_1, message_size_1, &
                         edge_nodes_2, message_size_2 )

    allocate(n2eindex(embed%nnodes01+1))
    n2eindex = 0
    call n2e_make_index(embed%nnodes01, n2eindex, ldim, ne, e2n)
    nn2e = n2eindex(embed%nnodes01+1)-1
    allocate(n2e(nn2e))
    n2e = 0
    call n2e_make(embed%nnodes01, n2eindex, nn2e, n2e, &
                  ldim, ne, e2n)
    call n2e_test(embed%nnodes01, n2eindex, nn2e, n2e, &
                  ldim, ne, e2n)

    do ie = 1, message_total_2
      do node = 1, ldim
        local_nodes(node) = lookupg2l(edge_nodes_2(node,ie),embed)
        if ( local_nodes(node) <= 0 .or. &
             local_nodes(node) > embed%nnodes01 ) then
          write(*,*) lmpi_id, 'edge nodes not localized', ie, &
          node, edge_nodes_2(node,ie), local_nodes(node)
          call lmpi_conditional_stop(1,'share_edgel2g')
        end if
      end do
      found = n2e_find( local_nodes, embed%nnodes01, n2eindex, nn2e, n2e, &
        ldim, ne, e2n )
      if ( found <= 0 .or. found > ne ) then
        write(*,*) lmpi_id, 'edge not found local on server', ie, found, &
          edge_nodes_2(1:ldim,ie), local_nodes
        call lmpi_conditional_stop(1,'share_edgel2g')
      end if
      local_edge_2(ie) = found ! used to establish xfer
    end do
    call lmpi_conditional_stop(0,'share_edgel2g')

    deallocate(n2eindex, n2e)

    deallocate( edge_nodes_1, edge_nodes_2 )

    allocate(global_edge_1(message_total_1))
    allocate(global_edge_2(message_total_2))
    global_edge_1 = 0
    global_edge_2 = 0

    do ie = 1, message_total_2
      global_edge_2(ie) = el2g(local_edge_2(ie))
    end do

    call lmpi_alltoallv( global_edge_2, message_size_2, &
                         global_edge_1, message_size_1 )

    do ie = 1,  message_total_1
      if (global_edge_1(ie) == 0) then
        write(*,*)" proc ",lmpi_id, "global_edge_1 zero"
        call lmpi_conditional_stop(1,'share_edgel2g')
      end if
      el2g(local_edge_1(ie)) = global_edge_1(ie)
    end do

    deallocate( global_edge_1, global_edge_2 )

    do ie = 1, ne
      if (el2g(ie) == 0) then
        write(*,*)" proc ",lmpi_id, " el2g not set ", ie
        call lmpi_conditional_stop(1,'share_edgel2g')
      end if
    end do
    call lmpi_conditional_stop(0,'share_edgel2g')

    reset_edge_xfer_requested : if ( reset_edge_xfer ) then
      if (lmpi_master) write(*,*)" reset edge lmpi_xfer send, receive pairs..."
      allocate(index_1(lmpi_nproc+1))
      allocate(index_2(lmpi_nproc+1))

      index_1 = 0
      index_1(1) = 1
      do processor = 1, lmpi_nproc
        index_1(processor+1) = index_1(processor) + message_size_1(processor)
      end do

      index_2 = 0
      index_2(1) = 1
      do processor = 1, lmpi_nproc
        index_2(processor+1) = index_2(processor) + message_size_2(processor)
      end do

      call lmpi_reset_sendrecv( 3, index_2, local_edge_2, index_1, local_edge_1)

      deallocate( index_1, index_2 )
    end if reset_edge_xfer_requested

    deallocate( local_edge_1, local_edge_2 )

    deallocate( message_size_1, message_size_2 )

  end subroutine embed_share_edgel2g

!=============================== EMBED_BC_FACES ==============================80
! embeds the bc in the orig grid to create embed grid bc
!=============================================================================80

  subroutine embed_bc_faces (orig, faces, embed )

    use grid_types,           only : grid_type
    use grid_helper,          only : lookupg2l, faces_type, faces_find
    use bc_types,             only : nullify_bc
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use lmpi,                 only : lmpi_master, lmpi_reduce,                 &
                                     lmpi_conditional_stop

    type(grid_type),                              intent(in)    :: orig
    type(faces_type),                             intent(in)    :: faces
    type(grid_type),                              intent(inout) :: embed

    integer :: ibc, origface, embedface, uniqueface, totalface

    integer, dimension(9) :: orignode, localnode, globalnode ! node work array
    logical, dimension(9) :: local
    integer, dimension(2) :: nodes
    integer :: origedge2, origedge4, origedge6
    integer :: node, edge, face

    integer, dimension(:), allocatable    :: n2eindex, n2e
    integer                               :: nn2e

! node work array, looking at the boundary face
!
!       orig            refined
!
!         1                1
!        / \              / \
!       /   \            6---2
!      /     \          / \ / \
!     3-------2        5---4---3
!

    integer, parameter, dimension ( 4, 3) :: embed_tri_map =                   &
      reshape((/ 1, 2, 6, 2,                                                   &
                 2, 3, 4, 4,                                                   &
                 6, 4, 5, 6 /),(/4,3/))

! node work array, looking at the boundary face
!
!       orig            refined
!
!     4-------3        4---7---3
!     |       |        |   |   |
!     |       |        8---9---6
!     |       |        |   |   |
!     1-------2        1---5---2
!

    integer, parameter, dimension ( 4, 4) :: embed_quad_map =                  &
      reshape((/ 1, 2, 3, 4,                                                   &
                 5, 6, 7, 8,                                                   &
                 9, 9, 9, 9,                                                   &
                 8, 5, 6, 7 /),(/4,4/))
    continue

    allocate(n2eindex(orig%nnodes01+1))
    n2eindex = 0
    call n2e_make_index(orig%nnodes01, n2eindex, 2, orig%nedge, orig%eptr)
    nn2e = n2eindex(orig%nnodes01+1)-1
    allocate(n2e(nn2e))
    n2e = 0
    call n2e_make(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)
    call n2e_test(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)

    embed%nbound = orig%nbound

    allocate(embed%bc(embed%nbound))

    loop_over_boundaries : do ibc = 1, orig%nbound

      call nullify_bc(embed%bc(ibc))
      embed%bc(ibc)%ibc           = orig%bc(ibc)%ibc

      embed%bc(ibc)%nbfacetg = 4*orig%bc(ibc)%nbfacetg

      ! allocate max size then realloc correct smaller size
      call my_alloc_ptr(embed%bc(ibc)%f2ntb,    4*orig%bc(ibc)%nbfacet,5)
      call my_alloc_ptr(embed%bc(ibc)%face_bit, 4*orig%bc(ibc)%nbfacet)

      embedface = 0
      uniqueface = 0
      loop_over_original_tri : do origface = 1, orig%bc(ibc)%nbfacet

        orignode(1) = orig%bc(ibc)%ibnode( orig%bc(ibc)%f2ntb(origface,1) )
        orignode(3) = orig%bc(ibc)%ibnode( orig%bc(ibc)%f2ntb(origface,2) )
        orignode(5) = orig%bc(ibc)%ibnode( orig%bc(ibc)%f2ntb(origface,3) )
        globalnode(1) = orig%l2g( orignode(1) )
        globalnode(3) = orig%l2g( orignode(3) )
        globalnode(5) = orig%l2g( orignode(5) )

        nodes(1) = orignode(1) ;  nodes(2) = orignode(3)
        origedge2 = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,       &
                              2, orig%nedge, orig%eptr)
        nodes(1) = orignode(3) ;  nodes(2) = orignode(5)
        origedge4 = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,       &
                              2, orig%nedge, orig%eptr)
        nodes(1) = orignode(5) ;  nodes(2) = orignode(1)
        origedge6 = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,       &
                              2, orig%nedge, orig%eptr)

        globalnode(2) = orig%el2g(origedge2) + orig%nnodesg
        globalnode(4) = orig%el2g(origedge4) + orig%nnodesg
        globalnode(6) = orig%el2g(origedge6) + orig%nnodesg
        do node = 1, 6
          localnode(node) = lookupg2l(globalnode(node),embed)
          local(node) = ( localnode(node) >  0 .and.                           &
                          localnode(node) <= embed%nnodes0 )
        end do

        add_embed_tri : do face = 1, 4
          local_tri : if (                                                     &
                   local(embed_tri_map(face,1))                                &
              .or. local(embed_tri_map(face,2))                                &
              .or. local(embed_tri_map(face,3)) ) then
            embedface = embedface + 1
            do node = 1, 3
              embed%bc(ibc)%f2ntb(embedface,node) = &
                localnode(embed_tri_map(face,node))
            end do
            if ( local(embed_tri_map(face,1)) ) then
              embed%bc(ibc)%face_bit(embedface) = 1
              uniqueface = uniqueface + 1
            else
              embed%bc(ibc)%face_bit(embedface) = 0
            end if

          end if local_tri
        end do add_embed_tri

      end do loop_over_original_tri

      embed%bc(ibc)%nbfacet = embedface
      call my_realloc_ptr(embed%bc(ibc)%f2ntb,   embed%bc(ibc)%nbfacet,5)
      call my_realloc_ptr(embed%bc(ibc)%face_bit,embed%bc(ibc)%nbfacet)

      call lmpi_reduce(uniqueface, totalface)
      if (lmpi_master) then
        if (embed%bc(ibc)%nbfacetg /= totalface) then
          write(*,'(a,3(i10,a))') " ibc: ",ibc," nbfacetg: ",                  &
            embed%bc(ibc)%nbfacetg, " guess ", totalface, " computed"
          write(*,*)"stopping... embed%bc(ibc)%nbfacetg /= totalface)"
          call lmpi_conditional_stop(1,'embedded tri global count')
        end if
      end if
      call lmpi_conditional_stop(0,'embedded tri global count')

      embed%bc(ibc)%nbfaceqg = 4*orig%bc(ibc)%nbfaceqg

      ! allocate max size then realloc correct smaller size
      call my_alloc_ptr(embed%bc(ibc)%f2nqb,     4*orig%bc(ibc)%nbfaceq, 6)
      call my_alloc_ptr(embed%bc(ibc)%face_bitq, 4*orig%bc(ibc)%nbfaceq)

      embedface = 0
      uniqueface = 0
      loop_over_original_quad : do origface = 1, orig%bc(ibc)%nbfaceq

        orignode(1:4) = orig%bc(ibc)%ibnode( orig%bc(ibc)%f2nqb(origface,1:4) )
        globalnode(1:4) = orig%l2g( orignode(1:4) )

        nodes(1) = orignode(1) ;  nodes(2) = orignode(2)
        edge = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,            &
                         2, orig%nedge, orig%eptr)
        if (edge <= 0) call lmpi_conditional_stop(1,'quad edge or face missing')
        globalnode(5) = orig%el2g(edge) + orig%nnodesg

        nodes(1) = orignode(2) ;  nodes(2) = orignode(3)
        edge = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,            &
                         2, orig%nedge, orig%eptr)
        if (edge <= 0) call lmpi_conditional_stop(1,'quad edge or face missing')
        globalnode(6) = orig%el2g(edge) + orig%nnodesg

        nodes(1) = orignode(3) ;  nodes(2) = orignode(4)
        edge = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,            &
                         2, orig%nedge, orig%eptr)
        if (edge <= 0) call lmpi_conditional_stop(1,'quad edge or face missing')
        globalnode(7) = orig%el2g(edge) + orig%nnodesg

        nodes(1) = orignode(4) ;  nodes(2) = orignode(1)
        edge = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,            &
                         2, orig%nedge, orig%eptr)
       if (edge <= 0) call lmpi_conditional_stop(1,'quad edge or face missing')
        globalnode(8) = orig%el2g(edge) + orig%nnodesg

        face = faces_find(faces, orignode(1:4) )
        if (face <= 0) call lmpi_conditional_stop(1,'quad edge or face missing')
        globalnode(9) = faces%l2g(face) + orig%nedgeg + orig%nnodesg

        do node = 1, 9
          localnode(node) = lookupg2l(globalnode(node),embed)
          local(node) = ( localnode(node) >  0 .and.                           &
                          localnode(node) <= embed%nnodes0 )
        end do

        add_embed_quad : do face = 1, 4
          local_quad : if (                                                    &
                   local(embed_quad_map(face,1))                               &
              .or. local(embed_quad_map(face,2))                               &
              .or. local(embed_quad_map(face,3))                               &
              .or. local(embed_quad_map(face,4)) ) then
            embedface = embedface + 1
            do node = 1, 4
              embed%bc(ibc)%f2nqb(embedface,node) = &
                localnode(embed_quad_map(face,node))
            end do
            if ( local(embed_quad_map(face,1)) ) then
              embed%bc(ibc)%face_bitq(embedface) = 1
              uniqueface = uniqueface + 1
            else
              embed%bc(ibc)%face_bitq(embedface) = 0
            end if

          end if local_quad
        end do add_embed_quad

      end do loop_over_original_quad
      call lmpi_conditional_stop(0,'quad edge or face missing')

      embed%bc(ibc)%nbfaceq = embedface
      call my_realloc_ptr(embed%bc(ibc)%f2nqb,    embed%bc(ibc)%nbfaceq,6)
      call my_realloc_ptr(embed%bc(ibc)%face_bitq,embed%bc(ibc)%nbfaceq)

      call lmpi_reduce(uniqueface, totalface)
      if (lmpi_master) then
        if (embed%bc(ibc)%nbfaceqg /= totalface) then
          write(*,'(a,3(i10,a))') " ibc: ",ibc," nbfacetg: ",                  &
            embed%bc(ibc)%nbfacetg, " guess ", totalface, " computed"
          write(*,*)"stopping... embed%bc(ibc)%nbfaceqg /= totalface)"
          call lmpi_conditional_stop(1,'embedded quad global count')
        end if
      end if
      call lmpi_conditional_stop(0,'embedded quad global count')

    end do loop_over_boundaries

    deallocate(n2eindex, n2e)

  end subroutine embed_bc_faces

!=========================== EMBED_RENUMBER_FACES ============================80
! embeds the bc in the orig grid to create embed grid bc
!=============================================================================80

  subroutine embed_renumber_faces (embed)

    use grid_types,           only : grid_type
    use allocations,          only : my_alloc_ptr
    use lmpi,                 only : lmpi_reduce, lmpi_bcast

    type(grid_type),                             intent(inout) :: embed

    integer :: ibc
    integer :: node, face

    integer, dimension(embed%nnodes01) :: local2face
    integer :: embednode, uniquenode
    integer :: localindex

    continue

    loop_over_boundaries : do ibc = 1, embed%nbound

      embednode = 0
      uniquenode = 0
      local2face = 0

      renumber_triangles : do face = 1, embed%bc(ibc)%nbfacet
        do node = 1, 3
          localindex = embed%bc(ibc)%f2ntb(face,node)
          if (local2face(localindex) == 0) then
            embednode = embednode + 1
            local2face(localindex) = embednode
            if (localindex <= embed%nnodes0) uniquenode = uniquenode +1
          end if
          embed%bc(ibc)%f2ntb(face,node) = local2face(localindex)
        end do
      end do renumber_triangles

      renumber_quads : do face = 1, embed%bc(ibc)%nbfaceq
        do node = 1, 4
          localindex = embed%bc(ibc)%f2nqb(face,node)
          if (local2face(localindex) == 0) then
            embednode = embednode + 1
            local2face(localindex) = embednode
            if (localindex <= embed%nnodes0) uniquenode = uniquenode +1
          end if
          embed%bc(ibc)%f2nqb(face,node) = local2face(localindex)
        end do
      end do renumber_quads

      embed%bc(ibc)%nbnode = embednode

      call lmpi_reduce(uniquenode, embed%bc(ibc)%nbnodeg)
      call lmpi_bcast(embed%bc(ibc)%nbnodeg)

      call my_alloc_ptr(embed%bc(ibc)%ibnode,max(1,embed%bc(ibc)%nbnode))

      do localindex = 1, embed%nnodes01
        node = local2face(localindex)
        if (node>0)embed%bc(ibc)%ibnode(node) = localindex
      enddo

    end do loop_over_boundaries

  end subroutine embed_renumber_faces

!============================ embed_find_boundary_cells ======================80
! finds cells that share face with boundary
!=============================================================================80

  subroutine embed_find_boundary_cells (embed, n2c )

    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_id, lmpi_conditional_stop
    use grid_helper,          only : n2c_type, n2c_cell_with_face

    type(grid_type),                             intent(inout) :: embed
    type(n2c_type),                              intent(in)    :: n2c

    integer :: ibc, face, elem, cell
    integer, dimension(3) :: tri_nodes
    integer, dimension(4) :: quad_nodes

    continue

    loop_over_boundaries : do ibc = 1, embed%nbound
      find_tri_cell : do face = 1, embed%bc(ibc)%nbfacet
        tri_nodes = embed%bc(ibc)%f2ntb(face,1:3)
        call n2c_cell_with_face( embed, n2c, tri_nodes, elem, cell )
        tri_cell_missing : if ( 0 >= elem .or. 0 >= cell ) then
          write(*,*)lmpi_id,"tri cell not found",ibc,face,embed%bc(ibc)%nbfacet
          write(*,*)lmpi_id,"x", embed%x(tri_nodes)
          write(*,*)lmpi_id,"y", embed%y(tri_nodes)
          write(*,*)lmpi_id,"z", embed%x(tri_nodes)
          call lmpi_conditional_stop(1,"bc tri cell not found")
        end if tri_cell_missing
        embed%bc(ibc)%f2ntb(face,4) = cell
        embed%bc(ibc)%f2ntb(face,5) = elem
      end do find_tri_cell
      find_quad_cell : do face = 1, embed%bc(ibc)%nbfaceq
        quad_nodes = embed%bc(ibc)%f2nqb(face,1:4)
        call n2c_cell_with_face( embed, n2c, quad_nodes, elem, cell )
        quad_cell_missing : if ( 0 >= elem .or. 0 >= cell ) then
          write(*,*)lmpi_id,"quad cell not found",ibc,face,embed%bc(ibc)%nbfacet
          write(*,*)lmpi_id,"x", embed%x(quad_nodes)
          write(*,*)lmpi_id,"y", embed%y(quad_nodes)
          write(*,*)lmpi_id,"z", embed%x(quad_nodes)
          call lmpi_conditional_stop(1,"bc quad cell not found")
        end if quad_cell_missing
        embed%bc(ibc)%f2nqb(face,5) = cell
        embed%bc(ibc)%f2nqb(face,6) = elem
      end do find_quad_cell
    end do loop_over_boundaries

    call lmpi_conditional_stop(0,'bc face cell not found')

  end subroutine embed_find_boundary_cells

!============================ embed_add_adjoint_faces ========================80
! add phantom faces needed by the adjoint
!=============================================================================80

  subroutine embed_add_adjoint_faces( embed, n2c )

    use grid_types,           only : grid_type
    use grid_helper,          only : lookupg2l
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use lmpi,                 only : lmpi_id, lmpi_nproc,                      &
                                     lmpi_bcast, lmpi_conditional_stop
    use grid_helper,          only : n2c_type, n2c_cell_with_face

    type(grid_type),                             intent(inout) :: embed
    type(n2c_type),                              intent(in)    :: n2c

    integer :: ibc, face, elem, cell, inode
    integer, dimension(4) :: node, part
    integer :: nfound
    integer, dimension(:,:), pointer :: sendphantom, recvphantom
    integer :: processor, nphantom, iphantom

    continue

    nfound = 0
    loop_over_boundaries : do ibc = 1, embed%nbound
      count_phantom_tri : do face = 1, embed%bc(ibc)%nbfacet
        do inode = 1, 3
          node(inode) = embed%bc(ibc)%f2ntb(face,inode)
        end do
        cell    = embed%bc(ibc)%f2ntb(face,4)
        elem   = embed%bc(ibc)%f2ntb(face,5)
        if ( 4 /= embed%elem(elem)%node_per_cell ) cycle count_phantom_tri
        node(4) = embed%elem(elem)%c2n(1,cell) &
                + embed%elem(elem)%c2n(2,cell) &
                + embed%elem(elem)%c2n(3,cell) &
                + embed%elem(elem)%c2n(4,cell) &
                - node(1) - node(2) - node(3)
        do inode = 1, 4
          part(inode) = embed%part(node(inode))
        end do
        if ( ( part(4) /= part(1) ) .and.                                      &
             ( part(4) /= part(2) ) .and.                                      &
             ( part(4) /= part(3) ) .and.                                      &
             ( part(1) == (lmpi_id+1) ) ) then
          nfound = nfound + 1
          if (nfound == 1) then
            call my_alloc_ptr( sendphantom, 5, nfound )
          else
            call my_realloc_ptr( sendphantom, 5, nfound )
          end if
          sendphantom(1,nfound) = part(4)-1
          sendphantom(2,nfound) = ibc
          do inode = 1, 3
            sendphantom(inode+2,nfound) = embed%l2g(node(inode))
          end do
        endif
      end do count_phantom_tri
    end do loop_over_boundaries

    proc_send_loop : do processor = 0, lmpi_nproc-1
      nphantom = nfound
      call lmpi_bcast(nphantom,processor)
      this_proc_needs_to_send : if (nphantom>0) then
        call my_alloc_ptr(recvphantom, 5, nphantom )
        if (lmpi_id==processor) then
          call lmpi_bcast(sendphantom,processor)
          recvphantom = sendphantom
        else
          call lmpi_bcast(recvphantom,processor)
        end if
        unpack_phantoms : do iphantom = 1, nphantom
          i_need_this_phantom : if (lmpi_id == recvphantom(1,iphantom)) then
            ibc = recvphantom(2,iphantom)
            embed%bc(ibc)%nbfacet = embed%bc(ibc)%nbfacet + 1
            call my_realloc_ptr(embed%bc(ibc)%f2ntb, embed%bc(ibc)%nbfacet, 5)
            call my_realloc_ptr(embed%bc(ibc)%face_bit, embed%bc(ibc)%nbfacet)
            face = embed%bc(ibc)%nbfacet
            localize_face : do inode = 1, 3
              node(inode) = lookupg2l( recvphantom(2+inode,iphantom), embed )
              if ( node(inode) <= 0 ) then
                write(*,*) "ERROR: embed_add_adjoint_faces, local node failure"
                call lmpi_conditional_stop(1, &
                  'phantom face cell missing in adjoint_faces')
              end if
              embed%bc(ibc)%f2ntb(face,inode) = node(inode)
            end do localize_face
            call n2c_cell_with_face( embed, n2c, node(1:3), elem, cell )
            find_cell_error : if ( 0 >= elem .or. 0 >= cell ) then
              write(*,*)lmpi_id,"phantom face bc cell not found",ibc,face
              call lmpi_conditional_stop(1, &
                'phantom face cell missing in adjoint_faces')
            end if find_cell_error
            embed%bc(ibc)%f2ntb(face,4) = cell
            embed%bc(ibc)%f2ntb(face,5) = elem
            embed%bc(ibc)%face_bit(face) = 0
          end if i_need_this_phantom
        end do unpack_phantoms
        deallocate(recvphantom)
      end if this_proc_needs_to_send
    end do proc_send_loop

    if ( nfound > 0 ) deallocate(sendphantom)

    call lmpi_conditional_stop(0, &
      'phantom face cell missing in adjoint_faces')

  end subroutine embed_add_adjoint_faces

!============================ embed_test_boundary_cells ======================80
!
!=============================================================================80

  subroutine embed_test_boundary_cells (embed)

    use grid_types,           only : grid_type
    use lmpi,                 only : lmpi_id, lmpi_conditional_stop


    type(grid_type),                             intent(inout) :: embed

    integer :: ibc, face
    integer :: node1, node2, node3, elem, cell, cell_node, node
    logical :: found1, found2, found3

    continue

    loop_over_boundaries : do ibc = 1, embed%nbound
      find_tri_cell : do face = 1, embed%bc(ibc)%nbfacet
        node1 = embed%bc(ibc)%ibnode( embed%bc(ibc)%f2ntb(face,1) )
        node2 = embed%bc(ibc)%ibnode( embed%bc(ibc)%f2ntb(face,2) )
        node3 = embed%bc(ibc)%ibnode( embed%bc(ibc)%f2ntb(face,3) )
        cell  = embed%bc(ibc)%f2ntb(face,4)
        elem  = embed%bc(ibc)%f2ntb(face,5)
        found1 = .false.
        found2 = .false.
        found3 = .false.
        do cell_node = 1, embed%elem(elem)%node_per_cell
          node = embed%elem(elem)%c2n(cell_node,cell)
          found1 = found1 .or. (node1 == node)
          found2 = found2 .or. (node2 == node)
          found3 = found3 .or. (node3 == node)
        end do
        if ( .not. (found1 .and. found2 .and. found3) ) then
          write(*,*)"face cell not found"
          write(*,*)lmpi_id,ibc,face,node1, node2,node3
          write(*,*)lmpi_id,ibc,face,embed%elem(1)%c2n(:,cell)
          call lmpi_conditional_stop(1,'test_bounady_cell_faces')
        end if
      end do find_tri_cell
    end do loop_over_boundaries

    call lmpi_conditional_stop(0,'test_bounady_cell_faces')

  end subroutine embed_test_boundary_cells

!============================ EMBED_SOLUTION =================================80
! interpolates solution form orig to embed mesh
!=============================================================================80

  subroutine embed_solution(                                                   &
    orig,origsoln,origsadj,faces,                                              &
    embed,embedsoln,embedsadj,design,                                          &
    rads, ihex, hexl2g )

    use grid_helper,          only : faces_type
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use allocations,          only : my_alloc_ptr
    use rad_defs,             only : rads_type
    use design_types,         only : design_type
    use lmpi,                 only : lmpi_master, lmpi_conditional_stop
    use global_image,         only : global_image_export_to

    type(grid_type),                    intent(in)    :: orig
    type(grid_type),                    intent(in)    :: embed
    type(soln_type),                    intent(inout) :: origsoln
    type(soln_type),                    intent(inout) :: embedsoln
    type(sadj_type),                    intent(inout) :: origsadj
    type(sadj_type),                    intent(inout) :: embedsadj
    type(design_type),                  intent(in)    :: design
    type(rads_type),                    intent(inout) :: rads
    type(faces_type),                   intent(in)    :: faces
    integer,                            intent(in)    :: ihex
    integer(i8), dimension(:),          intent(in)    :: hexl2g

    integer :: func

    integer, dimension(:), allocatable    :: n2eindex, n2e
    integer                               :: nn2e

    logical, parameter :: test_embed_soln = .false.

    integer :: node
    integer, parameter :: n_output_variables = 12
    character(80), dimension(n_output_variables) :: output_variables
    real(dp), dimension(:,:), allocatable :: output_data

    continue

    if (test_embed_soln) then
      do node = 1, orig%nnodes01
        origsoln%q_dof(1,node) = 1.0_dp
        origsoln%q_dof(2,node) = orig%x(node)
        origsoln%q_dof(3,node) = orig%y(node)
        origsoln%q_dof(4,node) = orig%z(node)
        origsadj%rlam(1,node,1) = 1.0_dp
        origsadj%rlam(2,node,1) = orig%x(node)*orig%x(node)
        origsadj%rlam(3,node,1) = orig%y(node)*orig%y(node)
        origsadj%rlam(4,node,1) = orig%z(node)*orig%z(node)
      end do
    end if

    allocate(n2eindex(orig%nnodes01+1))
    n2eindex = 0
    call n2e_make_index(orig%nnodes01, n2eindex, 2, orig%nedge, orig%eptr)
    nn2e = n2eindex(orig%nnodes01+1)-1
    allocate(n2e(nn2e))
    n2e = 0
    call n2e_make(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)
    call n2e_test(orig%nnodes01, n2eindex, nn2e, n2e, 2, orig%nedge, orig%eptr)

    resize_grad_for_rlam : if (origsoln%ndim /= origsoln%adim) then
      deallocate(origsoln%gradx)
      deallocate(origsoln%grady)
      deallocate(origsoln%gradz)
      call my_alloc_ptr(origsoln%gradx, origsoln%adim, orig%nnodes01)
      call my_alloc_ptr(origsoln%grady, origsoln%adim, orig%nnodes01)
      call my_alloc_ptr(origsoln%gradz, origsoln%adim, orig%nnodes01)
    end if resize_grad_for_rlam

    call my_alloc_ptr(embedsadj%rlam, embedsoln%adim, embed%nnodes01, &
                                      design%nfunctions)
    call my_alloc_ptr(rads%dual_linear, embedsoln%adim, embed%nnodes0)

    do func = 1, design%nfunctions
      if (lmpi_master) write (*,*)' embed adjoint solution dimensioned ', &
                                    embedsoln%adim
      call parallel_fe_reconstruct(                                            &
        embedsoln%adim,                                                        &
        orig%nnodes01, embed%nnodes0,                                          &
        origsadj%rlam(:,:,func), embedsadj%rlam, rads%dual_linear,             &
        faces, orig, embed,                                                    &
        n2eindex, nn2e, n2e, origsoln, ihex, hexl2g )
    enddo

    resize_grad_for_qnode : if (origsoln%ndim /= origsoln%adim) then
      deallocate(origsoln%gradx)
      deallocate(origsoln%grady)
      deallocate(origsoln%gradz)
      call my_alloc_ptr(origsoln%gradx, origsoln%ndim, orig%nnodes01)
      call my_alloc_ptr(origsoln%grady, origsoln%ndim, orig%nnodes01)
      call my_alloc_ptr(origsoln%gradz, origsoln%ndim, orig%nnodes01)
    end if resize_grad_for_qnode

    call my_alloc_ptr(embedsoln%q_dof, embedsoln%ndim, embed%nnodes01)
    call my_alloc_ptr(rads%prim_linear, embedsoln%ndim, embed%nnodes0)

    if (lmpi_master) write (*,*)' embed flow solution dimensioned ', &
                                  embedsoln%ndim
    call parallel_fe_reconstruct(                                              &
      embedsoln%ndim,                                                          &
      orig%nnodes01, embed%nnodes0,                                            &
      origsoln%q_dof, embedsoln%q_dof, rads%prim_linear,                       &
      faces, orig, embed,                                                      &
      n2eindex, nn2e, n2e, origsoln, ihex, hexl2g )

    reconstruct_turb : if (origsoln%n_turb > 0 ) then

      resize_grad_for_turb : if (origsoln%n_turb /= origsoln%ndim) then
        deallocate(origsoln%gradx)
        deallocate(origsoln%grady)
        deallocate(origsoln%gradz)
        call my_alloc_ptr(origsoln%gradx, origsoln%n_turb, orig%nnodes01)
        call my_alloc_ptr(origsoln%grady, origsoln%n_turb, orig%nnodes01)
        call my_alloc_ptr(origsoln%gradz, origsoln%n_turb, orig%nnodes01)
      end if resize_grad_for_turb

      call my_alloc_ptr(embedsoln%turb, embedsoln%n_turb, embed%nnodes01)
      call my_alloc_ptr(rads%turb_linear, embedsoln%n_turb, embed%nnodes0)

      if (lmpi_master) write (*,*)' embed turb solution dimensioned ',       &
                                    embedsoln%n_turb
      call parallel_fe_reconstruct(                                            &
           embedsoln%n_turb,                                                   &
           orig%nnodes01, embed%nnodes0,                                       &
           origsoln%turb, embedsoln%turb, rads%turb_linear,                    &
           faces, orig, embed,                                                 &
           n2eindex, nn2e, n2e, origsoln, ihex, hexl2g )

    else
      call my_alloc_ptr(embedsoln%turb, 1, 1)
      call my_alloc_ptr(rads%turb_linear, 1, 1)
    end if reconstruct_turb

! reset n_tot for later rad calls

    embedsoln%n_tot = embedsoln%ndim
    embedsoln%n_grd = embedsoln%ndim

    deallocate(origsoln%gradx, origsoln%grady, origsoln%gradz)

    deallocate(n2eindex, n2e)

    if (test_embed_soln) then
      allocate(output_data(n_output_variables,embed%nnodes0))
      output_variables(1) = 'ec1'
      output_variables(2) = 'ex1'
      output_variables(3) = 'ey1'
      output_variables(4) = 'ez1'
      output_variables(5) = 'ec2'
      output_variables(6) = 'ex2'
      output_variables(7) = 'ey2'
      output_variables(8) = 'ez2'
      output_variables(9) = 'ecc'
      output_variables(10) = 'exx'
      output_variables(11) = 'eyy'
      output_variables(12) = 'ezz'
      do node = 1, embed%nnodes0
        output_data(1,node) = rads%prim_linear(1,node) - 1.0_dp
        output_data(2,node) = rads%prim_linear(2,node) - embed%x(node)
        output_data(3,node) = rads%prim_linear(3,node) - embed%y(node)
        output_data(4,node) = rads%prim_linear(4,node) - embed%z(node)
        output_data(5,node) = embedsoln%q_dof(1,node) - 1.0_dp
        output_data(6,node) = embedsoln%q_dof(2,node) - embed%x(node)
        output_data(7,node) = embedsoln%q_dof(3,node) - embed%y(node)
        output_data(8,node) = embedsoln%q_dof(4,node) - embed%z(node)
      output_data( 9,node)= embedsadj%rlam(1,node,1)-1.0_dp
      output_data(10,node)= embedsadj%rlam(2,node,1)-embed%x(node)*embed%x(node)
      output_data(11,node)= embedsadj%rlam(3,node,1)-embed%y(node)*embed%y(node)
      output_data(12,node)= embedsadj%rlam(4,node,1)-embed%z(node)*embed%z(node)
      end do

      call global_image_export_to( 'tec       ', trim(embed%project)//'_et', &
        embed, n_output_variables, output_variables, orig%nnodes0, output_data )

      deallocate(output_data)

      call lmpi_conditional_stop(1,'embed_solution:test_embed_soln true')
    end if

  end subroutine embed_solution

!====================== PARALLEL_FE_RECONSTRUCT ==============================80
!
!=============================================================================80

  subroutine parallel_fe_reconstruct(                                          &
    ndim, nnodes, embed_nnodes,                                                &
    q,           new_q,  linear_q,                                             &
    faces, orig, embed,                                                        &
    n2eindex, nn2e, n2e,                                                       &
    origsoln, ihex, hexl2g )

    use finite_element_interp, only : eval_finite_elem_a, eval_finite_elem_b,  &
                                      square_a_matrix, atransposeb
    use linear_algebra,        only : singleLU, backsolveLU
    use reconstruction,        only : lstgs
    use inviscid_flux,         only : first_order_iterations
    use info_depr,             only : ntt
    use nml_nonlinear_solves,  only : itime
    use timeacc,               only : pseudo_sub
    use lmpi,                  only : lmpi_conditional_stop
    use lmpi_app,              only : lmpi_xfer
    use grid_types,            only : grid_type
    use grid_helper,           only : lookupg2l, faces_type, faces_find
    use solution_types,        only : soln_type

    integer,                                  intent(in) :: ndim
    integer,                                  intent(in)    :: nnodes
    integer,                                  intent(in)    :: embed_nnodes
    real(dp), dimension(ndim,nnodes),         intent(in)    :: q
    real(dp), dimension(ndim,embed_nnodes),   intent(out)   :: new_q
    real(dp), dimension(ndim,embed_nnodes),   intent(out)   :: linear_q
    type(faces_type),                         intent(in)    :: faces
    type(grid_type),                          intent(in)    :: orig
    type(grid_type),                          intent(in)    :: embed

    integer, dimension(orig%nnodes01+1),      intent(in)    :: n2eindex
    integer,                                  intent(in)    :: nn2e
    integer, dimension(nn2e),                 intent(in)    :: n2e
    type(soln_type),                          intent(inout) :: origsoln
    integer,                                  intent(in)    :: ihex
    integer(i8), dimension(:),                intent(in)    :: hexl2g

    integer     :: orig_node, orig_edge, orig_face
    integer     :: edge, face, node
    integer, dimension(2) :: nodes
    integer, dimension(4) :: face_nodes

    integer     :: ivar, iedge, iface, icell
    integer     :: newnode, orignode, globalnode
    integer     :: node1, node2, node3, node4
    real(dp)    :: newx, newy, newz
    real(dp)    :: x1, y1, z1
    real(dp)    :: oneoverhits

    integer     :: ielem
    real(dp), dimension(:),   allocatable :: cellx, celly, cellz
    real(dp), dimension(:),   allocatable :: cellq, cellqx, cellqy, cellqz
    real(dp), dimension(:,:), allocatable :: a
    real(dp), dimension(:,:), allocatable :: lu
    real(dp), dimension(:),   allocatable :: b
    real(dp), dimension(:),   allocatable :: c, atb

    integer     :: macro_nnode
    integer,  dimension(:), allocatable :: macro_global
    integer,  dimension(:), allocatable :: macro_local

    integer, dimension(embed%nnodes0)  :: hits

    real(dp), parameter            :: my_zero  = 0.00_dp
    real(dp), parameter            :: my_0125  = 0.125_dp
    real(dp), parameter            :: my_025   = 0.25_dp
    real(dp), parameter            :: my_half  = 0.50_dp
    real(dp), parameter            :: my_1     = 1.00_dp

    continue

!   this is to make sure that the gradients are computed
    if (itime == 0) then
      ntt = 1
    else
      pseudo_sub = 1
    end if
    first_order_iterations = 0

    call lstgs(origsoln%viscous_method,                                        &
               orig%nnodes0, orig%nnodes01, orig%nedgeloc, orig%eptr,          &
               orig%symmetry, q,                                               &
               origsoln%gradx,   origsoln%grady,         origsoln%gradz,       &
               orig%x,       orig%y,             orig%z,                       &
               orig%r11,     orig%r12,           orig%r13,                     &
               orig%r22,     orig%r23,           orig%r33,                     &
               ndim,      ndim,                                                &
               origsoln%turb,      origsoln%n_turb,  0,  ndim)

    call lmpi_xfer(origsoln%gradx)
    call lmpi_xfer(origsoln%grady)
    call lmpi_xfer(origsoln%gradz)

    new_q = my_zero
    linear_q  = my_zero

!   copy existing grid

    do orignode = 1, orig%nnodes0
      newnode = lookupg2l(orig%l2g(orignode),embed)
      do ivar = 1, ndim
        linear_q(ivar,newnode)=q(ivar,orignode)
      end do
    end do

!   reconstruct linear data on new nodes

    do iedge = 1, orig%nedgeloc
      newnode = lookupg2l(orig%nnodesg + int(orig%el2g(iedge),i4),embed)
      if ( newnode > 0 .and. newnode <= embed%nnodes0 ) then
        node1 = orig%eptr(1,iedge)
        node2 = orig%eptr(2,iedge)
! linear_q is linear spline elements
        do ivar = 1, ndim
          linear_q(ivar,newnode)  = my_half * (q(ivar,node1)+q(ivar,node2))
        end do
      end if
    end do

    do iface = 1, faces%nface
      globalnode = orig%nnodesg + int(orig%nedgeg,i4) + faces%l2g(iface)
      newnode = lookupg2l(globalnode,embed)
      if ( newnode > 0 .and. newnode <= embed%nnodes0 ) then
        node1 = faces%f2n(1,iface)
        node2 = faces%f2n(2,iface)
        node3 = faces%f2n(3,iface)
        node4 = faces%f2n(4,iface)
! linear_q is linear spline elements
        do ivar = 1, ndim
          linear_q(ivar,newnode)  = my_025 * (q(ivar,node1)+q(ivar,node2)&
                                             +q(ivar,node3)+q(ivar,node4))
        end do
      end if
    end do

    lin_orig_hex_center : if ( 0 /= ihex) then
      do icell = 1, orig%elem(ihex)%ncell
        globalnode = orig%nnodesg+orig%nedgeg+faces%nfaceg+hexl2g(icell)
        newnode = lookupg2l(globalnode,embed)
        if ( newnode > 0 .and. newnode <= embed%nnodes0 ) then
! linear_q is linear spline elements
          do ivar = 1, ndim
            linear_q(ivar,newnode)  = my_0125 * (    &
              q(ivar,orig%elem(ihex)%c2n(1,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(2,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(3,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(4,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(5,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(6,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(7,icell)) + &
              q(ivar,orig%elem(ihex)%c2n(8,icell)) )
          end do
        end if
      end do
    end if lin_orig_hex_center

!   reconstruct data on new nodes

    hits = 0

    each_elem_group : do ielem = 1, orig%nelem

      allocate( cellx(orig%elem(ielem)%node_per_cell) )
      allocate( celly(orig%elem(ielem)%node_per_cell) )
      allocate( cellz(orig%elem(ielem)%node_per_cell) )

      allocate( cellq(orig%elem(ielem)%node_per_cell) )
      allocate( cellqx(orig%elem(ielem)%node_per_cell) )
      allocate( cellqy(orig%elem(ielem)%node_per_cell) )
      allocate( cellqz(orig%elem(ielem)%node_per_cell) )

      allocate( a(4*orig%elem(ielem)%node_per_cell,10) )
      allocate( lu(10,10) )
      allocate( b(4*orig%elem(ielem)%node_per_cell) )
      allocate( c(10) )
      allocate( atb(10) )

      macro_nnode = 0
      select case (orig%elem(ielem)%type_cell)
      case('tet')
        macro_nnode = 10
      case('pyr')
        macro_nnode = 14
      case('prz')
        macro_nnode = 18
      case('hex')
        macro_nnode = 27
      case default
        call lmpi_conditional_stop(1,&
          'parallel_embed:parallel_fe_reconstruct implement')
      end select
      allocate( macro_global(macro_nnode) )
      allocate( macro_local(macro_nnode) )

      high_order_cell : do icell = 1, orig%elem(ielem)%ncell

        x1 = orig%x(orig%elem(ielem)%c2n(1,icell))
        y1 = orig%y(orig%elem(ielem)%c2n(1,icell))
        z1 = orig%z(orig%elem(ielem)%c2n(1,icell))
        cellx = orig%x(orig%elem(ielem)%c2n(:,icell))-x1
        celly = orig%y(orig%elem(ielem)%c2n(:,icell))-y1
        cellz = orig%z(orig%elem(ielem)%c2n(:,icell))-z1

        call eval_finite_elem_a(cellx,celly,cellz,a)
        call square_a_matrix(a,lu)
        call singleLU(lu)

        macro_nnode = 0
        copy_orig_node : do orig_node = 1, orig%elem(ielem)%node_per_cell
          macro_nnode = macro_nnode + 1
          macro_global(macro_nnode) = &
            orig%l2g(orig%elem(ielem)%c2n(orig_node,icell))
        end do copy_orig_node
        copy_orig_edge : do orig_edge = 1, orig%elem(ielem)%edge_per_cell
          node1 = &
            orig%elem(ielem)%c2n(orig%elem(ielem)%local_e2n(orig_edge,1),icell)
          node2 = &
            orig%elem(ielem)%c2n(orig%elem(ielem)%local_e2n(orig_edge,2),icell)
          nodes(1) = node1 ; nodes(2) = node2
          edge = n2e_find( nodes, orig%nnodes01, n2eindex, nn2e, n2e,          &
                           2, orig%nedge, orig%eptr)
          if ( edge <= 0 ) then
            write(*,*) 'edge not found'
            call lmpi_conditional_stop(1,&
              'parallel_embed:parallel_fe_reconstruct')
          end if
          macro_nnode = macro_nnode + 1
          macro_global(macro_nnode) = orig%el2g(edge) + orig%nnodesg
        end do copy_orig_edge

        copy_orig_quad : do orig_face = 1, orig%elem(ielem)%face_per_cell
          node1 = &
            orig%elem(ielem)%c2n(orig%elem(ielem)%local_f2n(orig_face,1),icell)
          node2 = &
            orig%elem(ielem)%c2n(orig%elem(ielem)%local_f2n(orig_face,2),icell)
          node3 = &
            orig%elem(ielem)%c2n(orig%elem(ielem)%local_f2n(orig_face,3),icell)
          node4 = &
            orig%elem(ielem)%c2n(orig%elem(ielem)%local_f2n(orig_face,4),icell)
          if ( node1 /= node4 ) then
            face_nodes = (/node1,node2,node3,node4/)
            face = faces_find(faces, face_nodes)
            if ( face <= 0 ) then
              write(*,*) 'face not found'
              call lmpi_conditional_stop(1,&
                'parallel_embed:parallel_fe_reconstruct')
            end if
            macro_nnode = macro_nnode + 1
            macro_global(macro_nnode)=faces%l2g(face)+orig%nedgeg + orig%nnodesg
          end if
        end do copy_orig_quad

        copy_orig_hex_center : if (  'hex' == orig%elem(ielem)%type_cell ) then
          macro_nnode = 27
          macro_global(macro_nnode) = &
            orig%nnodesg+orig%nedgeg+faces%nfaceg+hexl2g(icell)
        end if copy_orig_hex_center

        do node = 1, macro_nnode
          macro_local(node) = lookupg2l(macro_global(node),embed)
          newnode = macro_local(node)
          if (newnode > 0 .and. newnode <= embed%nnodes0 ) then
            hits(newnode) = hits(newnode) + 1
          end if
        end do

        varible : do ivar = 1, ndim

          cellq = q(ivar,orig%elem(ielem)%c2n(:,icell))
          cellqx = origsoln%gradx(ivar,orig%elem(ielem)%c2n(:,icell))
          cellqy = origsoln%grady(ivar,orig%elem(ielem)%c2n(:,icell))
          cellqz = origsoln%gradz(ivar,orig%elem(ielem)%c2n(:,icell))

          call eval_finite_elem_b(cellq,cellqx,cellqy,cellqz,b)

          call atransposeb(a,b,atb)
          call backsolveLU(lu,c,atb)

          do node = 1, macro_nnode
            newnode = macro_local(node)
            if (newnode > 0 .and. newnode <= embed%nnodes0 ) then
              newx = embed%x(newnode)-x1
              newy = embed%y(newnode)-y1
              newz = embed%z(newnode)-z1
              new_q(ivar,newnode) = new_q(ivar,newnode)                    &
                + c(1)                                                     &
                + newx*c(2) + newy*c(3) + newz*c(4)                        &
                + newx*newx*c(5) + newy*newy*c(6) + newz*newz*c(7)         &
                + newx*newy*c(8) + newx*newz*c(9) + newy*newz*c(10)
            end if
          end do

        end do varible
      end do high_order_cell

      deallocate( cellx )
      deallocate( celly )
      deallocate( cellz )

      deallocate( cellq )
      deallocate( cellqx )
      deallocate( cellqy )
      deallocate( cellqz )

      deallocate( a )
      deallocate( lu )
      deallocate( b )
      deallocate( c )
      deallocate( atb )

      deallocate( macro_global )
      deallocate( macro_local )

    end do each_elem_group

    do node = 1, embed%nnodes0
      oneoverhits = my_1 / real(hits(node),dp)
      do ivar = 1, ndim
        new_q(ivar,node) = new_q(ivar,node) * oneoverhits
      end do
    end do

    call lmpi_conditional_stop(0,&
      'parallel_embed:parallel_fe_reconstruct')

  end subroutine parallel_fe_reconstruct

!============================ clean_lambda_parallel ==========================80
!
!=============================================================================80

  subroutine clean_lambda_parallel(viscous_method, grid, rlam,                 &
                                    adim, turb, n_turb )

    use grid_types,            only : grid_type
    use info_depr,             only : ntt
    use nml_nonlinear_solves,  only : itime
    use timeacc,               only : pseudo_sub
    use inviscid_flux,         only : first_order_iterations
    use reconstruction,        only : sumgs_nobound, lstgs_nobound
    use lmpi,                  only : lmpi_die, lmpi_master
    use lmpi_app,              only : lmpi_xfer
    use designs,               only : gettag

    integer, intent(in) :: viscous_method
    integer, intent(inout) :: adim, n_turb

    type(grid_type),                            intent(inout) :: grid
    real(dp), dimension(adim,grid%nnodes01),    intent(inout) :: rlam
    real(dp), dimension(n_turb,grid%nnodes01),     intent(in) :: turb

    integer :: ib, i, inode
    integer :: ivar, iedge, node1, node2
    real(dp)    :: dx, dy, dz, dq, diff

    real(dp)    :: gradmax, gradlimit

    integer,     dimension(grid%nnodes01) :: strong_tag, volume_node_edges
    integer,     dimension(grid%nnodes01) :: bad_grad, contributions

    real(dp), dimension(adim,grid%nnodes01)    :: gradx, grady, gradz
    real(dp), dimension(grid%nnodes0) :: r11, r12, r13, r22, r23, r33
!JOE FIXME use sadj%coltag, not local version
! make sure  sadj%coltag is set correctl upstream
    real(dp), dimension(adim,grid%nnodes01)    :: coltag

    real(dp), parameter            :: half    = 0.5_dp
    real(dp), parameter            :: one     = 1.0_dp

    real(dp), parameter            :: my_biggrad = 1.1_dp

    logical,     parameter :: test_cleanup = .false.

    continue

! Set up column tag array

    call gettag(grid,adim,coltag)

    test_init : if ( test_cleanup ) then
      write(*,*) "test_cleanup is true in clean_lambda_boundary, initializing"
      do i = 1, grid%nnodes01
        rlam(2,i) = grid%x(i)
        rlam(3,i) = grid%y(i)
        rlam(4,i) = grid%z(i)
      enddo
    end if test_init

! compute the bc tag to flag boundary nodes

    strong_tag = 0
    flag_bound_nodes : do ib = 1, grid%nbound
      do i = 1, grid%bc(ib)%nbnode
        inode = grid%bc(ib)%ibnode(i)
        node_has_strong_eq : if (sum(coltag(:,inode)) < real(adim,dp)-half) then
          strong_tag(inode) = 1
        end if node_has_strong_eq
      end do
    end do flag_bound_nodes
    call lmpi_xfer(strong_tag)

! compute the wierd sumgs that skips edges touching boundries

    call sumgs_nobound(                                                        &
         grid%nnodes0, grid%nnodes01,    grid%nedgeloc,   grid%eptr,           &
         grid%x,           grid%y,         grid%z,                             &
         r11,              r12,            r13,                                &
         r22,              r23,            r33,                                &
         strong_tag)

    if (verbose .and. lmpi_master) &
      write(*,*) "r11-r23...", sum(r11), sum(r12), sum(r13), sum(r23), sum(r33)

! compute the wierd lstgs that only uses volume edges

!   this is to make sure that the gradients are computed
    if (itime == 0) then
      ntt = 1
    else
      pseudo_sub = 1
    end if
    first_order_iterations = 0

    call lstgs_nobound(viscous_method,                                         &
                       grid%nnodes0, grid%nnodes01, grid%nedgeloc,             &
                       grid%eptr,    rlam,                                     &
                       gradx,        grady,         gradz,                     &
                       grid%x,       grid%y,        grid%z,                    &
                       r11,          r12,           r13,                       &
                       r22,          r23,           r33,                       &
                       strong_tag,   adim,          adim,      turb,     n_turb)

    call lmpi_xfer(gradx)
    call lmpi_xfer(grady)
    call lmpi_xfer(gradz)

    if (verbose .and. lmpi_master) &
      write(*,*) "grads...", sum(gradx), sum(grady), sum(gradz)

    test_grad : if ( test_cleanup ) then
      write(*,*) "test_cleanup is true in clean_lambda_boundary, test grad"
      do i = 1, grid%nnodes0
        do ivar = 2, 4
          if (abs(gradx(ivar,i)) > my_biggrad)                                 &
               write(*,'(i9,a,i3,e25.16)')i, ' gradx', ivar, gradx(ivar,i)
          if (abs(grady(ivar,i)) > my_biggrad)                                 &
               write(*,'(i9,a,i3,e25.16)')i, ' grady', ivar, grady(ivar,i)
          if (abs(gradx(ivar,i)) > my_biggrad)                                 &
               write(*,'(i9,a,i3,e25.16)')i, ' gradz', ivar, gradz(ivar,i)
        end do
      enddo
    end if test_grad

    do inode = 1, grid%nnodes0
      rlam(:,inode) = coltag(:,inode) * rlam(:,inode)
    end do

! count the number of edges that contribute to volume nodes
!   less than three edges is ill-conditioned

    volume_node_edges = 0
    do iedge = 1, grid%nedgeloc
      node1 = grid%eptr(1,iedge)
      node2 = grid%eptr(2,iedge)
      ! skip edges that have a node that is bctag'ed
      lstgs_edge : if ( strong_tag(node1) == 0 .and.                           &
                        strong_tag(node2) == 0 ) then
        volume_node_edges(node1) = volume_node_edges(node1) + 1
        volume_node_edges(node2) = volume_node_edges(node2) + 1
      end if lstgs_edge
    end do
    call lmpi_xfer(volume_node_edges)

    bad_grad = 0
    do inode = 1, grid%nnodes0
      need_3_edges : if ( strong_tag(inode) == 0 .and. &
                          volume_node_edges(inode) < 3 ) then
        bad_grad(inode) = 1
        if (verbose) write(*,'(a,i9,a,i2,a)')                                  &
          '  WARNING: interior node',grid%l2g(inode),                          &
          ' has',volume_node_edges(inode),' active edges and will be skipped'
        if (verbose) write(*,*) ' at ',grid%x(inode),grid%y(inode),grid%z(inode)
      end if need_3_edges
    end do

! do not use excessively large gradients for extrapolation

    gradlimit = 1.0e8_dp

    do inode = 1, grid%nnodes0
      gradmax =                                                                &
        max( abs(gradx(1,inode)),max(abs(grady(1,inode)), abs(gradz(1,inode))))
      reasonable_limit_check : if (gradmax >= gradlimit) then
        bad_grad(inode) = 1
        if (verbose) write(*,'(a,i9,a,e15.7,a)')                               &
             '  WARNING: interior node',grid%l2g(inode),                       &
             ' has large gradient',gradmax,' and will not be used'
        if (verbose) write(*,*) ' at ',grid%x(inode),grid%y(inode),grid%z(inode)
      end if reasonable_limit_check
    end do

    call lmpi_xfer(bad_grad)

    contributions = 0

    do iedge = 1, grid%nedgeloc

      node1 = grid%eptr(1,iedge)
      node2 = grid%eptr(2,iedge)

      add_node1_contribution : if (node1 <= grid%nnodes0) then
        if ( strong_tag(node1) == 1 .and. strong_tag(node2) == 0 .and.         &
             bad_grad(node2) == 0 ) then

          dx = grid%x(node1) - grid%x(node2)
          dy = grid%y(node1) - grid%y(node2)
          dz = grid%z(node1) - grid%z(node2)

          do ivar = 1, adim
            dq = ( gradx(ivar,node2)*dx                                        &
                 + grady(ivar,node2)*dy                                        &
                 + gradz(ivar,node2)*dz )
            rlam(ivar,node1) = rlam(ivar,node1) &
              + (one-coltag(ivar,node1))*(rlam(ivar,node2) + dq)
          end do
          contributions(node1) = contributions(node1) + 1
        end if
      end if add_node1_contribution

      add_node2_contribution : if (node2 <= grid%nnodes0) then
        if ( strong_tag(node1) == 0 .and. strong_tag(node2) == 1 .and.         &
             bad_grad(node1) == 0 ) then

          dx = grid%x(node2) - grid%x(node1)
          dy = grid%y(node2) - grid%y(node1)
          dz = grid%z(node2) - grid%z(node1)

          do ivar = 1, adim
            dq = ( gradx(ivar,node1)*dx                                        &
                 + grady(ivar,node1)*dy                                        &
                 + gradz(ivar,node1)*dz )
            rlam(ivar,node2) = rlam(ivar,node2) &
              + (one-coltag(ivar,node2))*(rlam(ivar,node1) + dq)
          end do
          contributions(node2) = contributions(node2) + 1
        end if
      end if add_node2_contribution

    end do

    do i = 1, grid%nnodes0
      if (strong_tag(i) == 1 ) then
        if (contributions(i) == 0 ) then
          if (verbose) write(*,'(a,i9)')                                       &
            "  WARNING: boundary node with no edge to volume node, zeroed ",   &
            grid%l2g(i)
          if (verbose) write(*,*) ' at ', grid%x(i), grid%y(i), grid%z(i)
        else
          do ivar = 1, adim
            multiple_donors : if ( coltag(ivar,i) < half ) then
              rlam(ivar,i) = rlam(ivar,i) / real(contributions(i),dp)
            end if multiple_donors
          end do
        end if
      end if
    end do
    call lmpi_xfer(rlam)

    test_results : if ( test_cleanup ) then
      do i = 1, grid%nnodes0
        diff = (                                                               &
             abs( rlam(2,i) - grid%x(i) ) +                                    &
             abs( rlam(3,i) - grid%y(i) ) +                                    &
             abs( rlam(4,i) - grid%z(i) )  )
        if ( diff > 1.0e-14_dp ) then
          write(*,'(a,i9,a,e25.16)')'diff ', i,' : ',diff
          write(*,'(a,2e25.16)') 'x ',rlam(2,i),grid%x(i)
          write(*,'(a,2e25.16)') 'y ',rlam(3,i),grid%y(i)
          write(*,'(a,2e25.16)') 'z ',rlam(4,i),grid%z(i)
        endif
      enddo

      write(*,*) "test_cleanup is true in clean_lambda_boundary, stopping"
      call lmpi_die

    end if test_results

  end subroutine clean_lambda_parallel


!=============================================================================80
!
! determine the size of the node to elem adjacency info
!
!=============================================================================80

  subroutine n2e_make_index(                                                   &
    nnodes, n2eindex,                                                          &
    ldim, ne, e2n)

    integer,                            intent(in)    :: nnodes
    integer,     dimension(nnodes+1),   intent(inout) :: n2eindex
    integer,                            intent(in)    :: ldim, ne
    integer,     dimension(ldim,ne),    intent(in)    :: e2n

    integer :: ie, i

    continue

    do i = 1, nnodes
      n2eindex(i) = 0
    end do

    count_node_degree : do ie = 1, ne
      each_e_node : do i = 1, ldim
        if (e2n(i,ie) <= nnodes)  n2eindex(e2n(i,ie)) = n2eindex(e2n(i,ie)) + 1
      end do each_e_node
    end do count_node_degree

    n2eindex(1) = n2eindex(1) + 1
    overfill_comprow : do i = 1, nnodes
      n2eindex(i+1) = n2eindex(i+1) + n2eindex(i)
    end do overfill_comprow

  end subroutine n2e_make_index

!=============================================================================80
!
! create node to elem adjacency info
!
!=============================================================================80

  subroutine n2e_make(                                                         &
    nnodes, n2eindex,                                                          &
    nn2e, n2e,                                                                 &
    ldim, ne, e2n )

    integer,                            intent(in)    :: nnodes
    integer,     dimension(nnodes+1),   intent(inout) :: n2eindex
    integer,                            intent(in)    :: nn2e
    integer,     dimension(nn2e),       intent(inout) :: n2e
    integer,                            intent(in)    :: ldim, ne
    integer,     dimension(ldim,ne),    intent(in)    :: e2n

    integer :: ie, i, comprow_index

    continue

    insert_elem : do ie = 1, ne
      each_e_node : do i = 1, ldim
        if (e2n(i,ie) <= nnodes) then
          n2eindex(e2n(i,ie)) = n2eindex(e2n(i,ie)) - 1
          comprow_index = n2eindex(e2n(i,ie))
          n2e(comprow_index) = ie
        end if
      end do each_e_node
    end do insert_elem

  end subroutine n2e_make

!=============================================================================80
!
! test the node to edge adjacency info
!
!=============================================================================80

  subroutine n2e_test(                                                         &
    nnodes, n2eindex,                                                          &
    nn2e, n2e,                                                                 &
    ldim, ne, e2n )

    integer,                            intent(in)    :: nnodes
    integer,     dimension(nnodes+1),   intent(in)    :: n2eindex
    integer,                            intent(in)    :: nn2e
    integer,     dimension(nn2e),       intent(in)    :: n2e
    integer,                            intent(in)    :: ldim, ne
    integer,     dimension(ldim,ne),    intent(in)    :: e2n

    integer :: ie, found

  continue

    if (n2eindex(1) /= 1 ) write(*,*)"n2e_test n2eindex error"

    test_e : do ie = 1, ne
      found = n2e_find(e2n(:,ie), nnodes, n2eindex, nn2e, n2e, ldim, ne, e2n)
      if (found /= ie) write(*,*)"test_n2e wrong edge error"
    end do test_e

  end subroutine n2e_test

!=============================================================================80
!
! find an edge with two nodes using the node to edge adjacency info
!
!=============================================================================80

  function n2e_find(nodes, nnodes, n2eindex, nn2e, n2e, ldim, ne, e2n)

    use sort, only : small_sort

    integer                                           :: n2e_find
    integer,                            intent(in)    :: ldim
    integer,     dimension(ldim),       intent(in)    :: nodes
    integer,                            intent(in)    :: nnodes
    integer,     dimension(nnodes+1),   intent(in)    :: n2eindex
    integer,                            intent(in)    :: nn2e
    integer,     dimension(nn2e),       intent(in)    :: n2e
    integer,                            intent(in)    :: ne
    integer,     dimension(ldim,ne),    intent(in)    :: e2n

    integer :: comprow_index, same, i
    integer, dimension(ldim) :: targ, canidate

    continue

    n2e_find = 0

    not_around_node1 : if ( nodes(1) > nnodes ) then
      write(*,*) "n2e_find ghost element, inconstant nnodes"
      return
    end if not_around_node1

    targ = nodes
    call small_sort( ldim, targ )

    search_node1: do comprow_index= n2eindex(nodes(1)), n2eindex(nodes(1)+1)-1
      canidate = e2n( :, n2e(comprow_index) )
      call small_sort( ldim, canidate )
      same = 0
      do i = 1, ldim
        if ( targ(i) == canidate(i) ) same = same + 1
      end do
      if ( ldim == same ) then
        n2e_find =  n2e(comprow_index)
        exit search_node1
      end if
    end do search_node1

  end function n2e_find

!=============================================================================80

  subroutine verify_l2g(grid)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_id, lmpi_nproc, lmpi_master, &
      lmpi_bcast, lmpi_send, lmpi_recv, lmpi_conditional_stop

    type(grid_type),         intent(in) :: grid

    integer, dimension(:), allocatable :: all_globals

    integer :: nnodes0
    integer, dimension(:), allocatable :: part_globals

    integer :: node, proc, error

    continue

    skip_verify_if_too_big : if ( grid%nnodesg > 0 ) then
      if (lmpi_master) write(*,*) &
        "parallel_embed:verify_l2g SKIPPED nnodesg", grid%nnodesg
      return
    end if skip_verify_if_too_big

    error = 0
    master_init_globals : if (lmpi_master) then
      write(*,*) "parallel_embed:verify_l2g nnodesg", grid%nnodesg
      allocate(all_globals(grid%nnodesg))
      do node = 1, grid%nnodesg
        all_globals(node) = 0
      end do
      register_master_l2g : do node = 1, grid%nnodes0
        if ( 0 < grid%l2g(node) .and. grid%l2g(node) <= grid%nnodesg ) then
          all_globals(grid%l2g(node)) = all_globals(grid%l2g(node)) + 1
        else
          error = 1
          write(*,*) "parallel_embed:verify_l2g bad global", &
            lmpi_id, node, grid%l2g(node)
        end if
      end do register_master_l2g
    end if master_init_globals

    stop
    call lmpi_conditional_stop( error )

    error = 0
    each_non_master : do proc = 1, lmpi_nproc-1
      active_part : if ( lmpi_id == proc ) then
        nnodes0 = grid%nnodes0
        allocate(part_globals(nnodes0))
        do node = 1, nnodes0
          part_globals(node) = grid%l2g(node)
        end do
        print *, proc, lmpi_id ; stop
        call lmpi_bcast(nnodes0, proc )
        call lmpi_send(part_globals, nnodes0, 0, proc, error)
        deallocate( part_globals )
      else
        print *, proc, lmpi_id ; stop
        call lmpi_bcast(nnodes0, proc )
      end if active_part
      master_receive : if ( lmpi_master ) then
        allocate(part_globals(nnodes0))
        call lmpi_recv(part_globals, nnodes0, proc, proc, error)
        register_proc_l2g : do node = 1, nnodes0
          if ( 0 < part_globals(node) .and. &
              part_globals(node) <= grid%nnodesg ) then
            all_globals(part_globals(node))= all_globals(part_globals(node)) + 1
          else
            error = 1
            write(*,*) "parallel_embed:verify_l2g bad global", &
              proc, node, part_globals(node)
          end if
        end do register_proc_l2g
        deallocate( part_globals )
      end if master_receive

      call lmpi_conditional_stop( error )
    end do each_non_master

    error = 0
    master_check_globals : if (lmpi_master) then
      do node = 1, grid%nnodesg
        expect_one_per_global_index : if ( 1 /= all_globals(node)) then
          write(*,*) "global",node,"count",all_globals(node)
          error = 1
        end if expect_one_per_global_index
      end do
      deallocate( all_globals )
    end if master_check_globals

    call lmpi_conditional_stop( error )

  end subroutine verify_l2g

end module parallel_embed

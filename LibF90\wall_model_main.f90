module wall_model_main

    use kinddefs,        only : dp
    use info_depr,       only : skeleton
    use lmpi,            only : lmpi_id

  implicit none

  private

    public :: weak_wall_flux,          test_weak_wall_flux
    public :: viscous_wall_function_flux

    public :: residual_viscous_weak_tauij, residual_viscous_weak_pressure
    public :: jacobian_viscous_weak_tauij, jacobian_viscous_weak_pressure

  contains

!============================ WEAK_WALL_FLUX =================================80
!
!  Call appropriate functions for the residual or Jacobian contributions
!  for viscous weak vall or wall function boundary conditions
!
!=============================================================================80

       function weak_wall_flux ( grid, soln, ib, face_index, face_corner,      &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 sink, qi, qext, side,                         &
                                 u_tau_init,                                   &
                                 area_local, yplus_local, uplus_local,         &
                                 u_tau_local, nu_local, rho_local,             &
                                 v_corr_local, phi_local,                      &
                                 f1_nonconv_local,                             &
                                 f2_nonconv_local,                             &
                                 f3_nonconv_local,                             &
                                 f4_nonconv_local )                            &
    result ( flux )

    use kinddefs,                 only : dp
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type

    use fluid,                    only : gamma
    use info_depr,                only : xmach
    use bc_names,                 only : viscous_weak_wall, viscous_weak_trs,  &
                                         viscous_wall_function, viscous_wf_trs,&
                                         inviscid_porous, viscous_porous,      &
                                         block_interface
    use propulsion_types,         only : propulsion_system
    use nml_boundary_conditions,  only : unorm_bc,                             &
                                         passive_boundary, porous_coefficient

    real(dp), dimension(5,5)                         :: flux

    type(grid_type),                     intent(in)  :: grid
    type(soln_type),                     intent(in)  :: soln
    integer,                             intent(in)  :: ib
    integer,                             intent(in)  :: face_index
    integer,                             intent(in)  :: face_corner
    integer,                             intent(in)  :: nodes_per_face
    real(dp),                            intent(in)  :: xnorm
    real(dp),                            intent(in)  :: ynorm
    real(dp),                            intent(in)  :: znorm
    real(dp),                            intent(in)  :: area
    type(propulsion_system),             intent(in)  :: sink
    real(dp), dimension(:),              intent(in)  :: qi
    real(dp), dimension(:),              intent(in)  :: qext
    character(len=3),                    intent(in)  :: side
    real(dp),                            intent(in)  :: u_tau_init
    real(dp), dimension(:),              intent(inout)  :: area_local
    real(dp), dimension(:),              intent(inout)  :: yplus_local
    real(dp), dimension(:),              intent(inout)  :: uplus_local
    real(dp), dimension(:),              intent(inout)  :: u_tau_local
    real(dp), dimension(:),              intent(inout)  :: nu_local
    real(dp), dimension(:),              intent(inout)  :: rho_local
    real(dp), dimension(:),              intent(inout)  :: v_corr_local
    real(dp), dimension(:),              intent(inout)  :: phi_local
    real(dp),                            intent(inout)  :: f1_nonconv_local
    real(dp),                            intent(inout)  :: f2_nonconv_local
    real(dp),                            intent(inout)  :: f3_nonconv_local
    real(dp),                            intent(inout)  :: f4_nonconv_local

!   real(dp), save     :: plenum_pressure
!   real(dp), save     :: massflow
!   integer, parameter :: plenum_eval_freq = 10
!   logical            :: plenum_needs_update

    continue

    flux            = real(0.0,dp)
!   plenum_needs_update = .false.
!   plenum_needs_update = (  ( ntt == 1                          ) .or.        &
!             ( (ntt/plenum_eval_freq)*plenum_eval_freq == ntt ) )

!   unorm_bc = 0.0_dp

    select case ( grid%bc(ib)%ibc )

    case ( viscous_weak_wall, viscous_weak_trs, block_interface )

      if ( passive_boundary(ib) == 1 ) then
        unorm_bc(ib) =  porous_coefficient(ib)*(qi(5)-qext(5)) &
                     / (sqrt(gamma)*xmach)
      endif

      if ( skeleton > 20 ) then
        write(6,'(a,2i5,15(1x,f15.5))') 'unorm_bc:.......', lmpi_id, ib, &
        qi(1), qi(4), qi(5), qext(1), qext(4), qext(5), unorm_bc(ib)
      end if


       flux = viscous_weak_flux ( grid, soln, ib, face_index, face_corner,     &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 side, unorm_bc )


    case ( viscous_wall_function, viscous_wf_trs )

       flux = viscous_wall_function_flux (                                     &
                                 grid, soln, ib, face_index, face_corner,      &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 side, unorm_bc,                               &
                                 u_tau_init,                                   &
                                 area_local, yplus_local, uplus_local,         &
                                 u_tau_local, nu_local, rho_local,             &
                                 v_corr_local, phi_local,                      &
                                 f1_nonconv_local, f2_nonconv_local,           &
                                 f3_nonconv_local, f4_nonconv_local )




    case ( inviscid_porous )

!     flux = porous_wall_flux ( grid, soln%q_dof,                             &
!                                      ib, face_index, face_corner,           &
!                                      nodes_per_face,                        &
!                                      max_node_per_cell,                     &
!                                      xnorm, ynorm, znorm, area,             &
!                                      soln%eqn_set, plenum_pressure,         &
!                                      massflow, side )

    case ( viscous_porous )

      flux = viscous_porous_flux ( grid, soln, ib, face_index, face_corner,    &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 side, unorm_bc, sink )
!     flux = viscous_porous_flux ( grid, soln%q_dof,                          &
!                                      ib, face_index, face_corner,           &
!                                      nodes_per_face,                        &
!                                      max_node_per_cell,                     &
!                                      xnorm, ynorm, znorm, area,             &
!                                      soln%eqn_set, plenum_pressure,         &
!                                      massflow, side )

    end select

    if ( skeleton > 20 ) then
      write(6,'(a,a            )') 'side:.......... ', side
      write(6,'(a,2i6          )') 'ib:............ ', ib
!     write(6,'(a,l6           )') 'plenum update.. ', plenum_needs_update
!     write(6,'(a,15(1x,es15.5))') 'p_plenum....... ', plenum_pressure
      write(6,'(a,2i6          )') 'face/corner:... ',face_index,face_corner
      write(6,'(a,15(1x,es15.5))') 'flux(1,1:5):... ', flux(1,1:5)
      write(6,'(a,15(1x,es15.5))') 'flux(2,1:5):... ', flux(2,1:5)
      write(6,'(a,15(1x,es15.5))') 'flux(3,1:5):... ', flux(3,1:5)
      write(6,'(a,15(1x,es15.5))') 'flux(4,1:5):... ', flux(4,1:5)
      write(6,'(a,15(1x,es15.5))') 'flux(5,1:5):... ', flux(5,1:5)
      write(*,*)
    endif

  end function weak_wall_flux

  include 'viscosity_law.f90'
  include 'get_p.f90'

!============================ VISCOUS_WEAK_FLUX ==============================80
!
!
!
!
!=============================================================================80

    function viscous_weak_flux ( grid, soln, ib, face_index, face_corner,      &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 side, unorm_bc                                &
                                 )                                             &
    result ( flux )

    use kinddefs,                 only : dp
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type

    use element_defs,             only : max_node_per_cell
    use info_depr,                only : xmach, re
    use turbulence_info,          only : model_strain_form_int

    real(dp), dimension(5,5)                         :: flux

    type(grid_type),                     intent(in)  :: grid
    type(soln_type),                     intent(in)  :: soln
    integer,                             intent(in)  :: ib
    integer,                             intent(in)  :: face_index
    integer,                             intent(in)  :: face_corner
    integer,                             intent(in)  :: nodes_per_face
    real(dp),                            intent(in)  :: xnorm
    real(dp),                            intent(in)  :: ynorm
    real(dp),                            intent(in)  :: znorm
    real(dp),                            intent(in)  :: area
    character(len=3),                    intent(in)  :: side
    real(dp), dimension(:),              intent(in)  :: unorm_bc

    real(dp) :: xmr
    real(dp), dimension(5)                           :: stresses
    real(dp), dimension(5)                           :: pressure
    real(dp), dimension(5,5)                         :: stresses_jacobian
    real(dp), dimension(5,5)                         :: pressure_jacobian

    continue

   xmr             = xmach / re

    if ( side == 'rhs' ) then

      stresses = residual_viscous_weak_tauij ( grid, soln%q_dof, soln%amut, &
                                     ib, face_index, nodes_per_face,        &
                                     xmr, xnorm, ynorm, znorm, area,        &
                                     soln%eqn_set, unorm_bc                 &
                                     )

      pressure = residual_viscous_weak_pressure ( grid, soln%q_dof,         &
                                     ib, face_index, face_corner,           &
                                     nodes_per_face,                        &
                                     max_node_per_cell,                     &
                                     xnorm, ynorm, znorm, area,             &
                                     soln%eqn_set )

      flux(1:5,1) = stresses(1:5) - pressure(1:5)


    else if ( side == 'lhs' ) then

      stresses_jacobian = jacobian_viscous_weak_tauij (                      &
                                   grid, soln%q_dof, soln%amut,              &
                                   ib , face_index, face_corner,             &
                                   xnorm, ynorm, znorm, area,                &
                                   nodes_per_face, xmr,                      &
                                   model_strain_form_int, soln%eqn_set,      &
                                   unorm_bc )

      pressure_jacobian = jacobian_viscous_weak_pressure (                   &
                          xnorm, ynorm, znorm, area )

      flux        = stresses_jacobian - pressure_jacobian

    endif

  end function viscous_weak_flux

!========================= VISCOUS_WALL_FUNCTION_FLUX ========================80
!
!
!
!
!=============================================================================80

    function viscous_wall_function_flux (                                      &
                                 grid, soln, ib, face_index, face_corner,      &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 side, unorm_bc,                               &
                                 u_tau_init,                                   &
                                 area_local, yplus_local, uplus_local,         &
                                 u_tau_local, nu_local, rho_local,             &
                                 v_corr_local, phi_local,                      &
                                 f1_nonconv_local, f2_nonconv_local,           &
                                 f3_nonconv_local, f4_nonconv_local )          &
    result ( flux )

    use kinddefs,                 only : dp
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type

    use element_defs,             only : max_node_per_cell
    use turbulence_info,          only : model_strain_form_int
    use fluid,                    only : gamma, sutherland_constant
    use info_depr,                only : xmach, re, tref
    use turb_parameters,          only : turbulent_prandtl

    real(dp), dimension(5,5)                         :: flux

    type(grid_type),                     intent(in)  :: grid
    type(soln_type),                     intent(in)  :: soln
    integer,                             intent(in)  :: ib
    integer,                             intent(in)  :: face_index
    integer,                             intent(in)  :: face_corner
    integer,                             intent(in)  :: nodes_per_face
    real(dp),                            intent(in)  :: xnorm
    real(dp),                            intent(in)  :: ynorm
    real(dp),                            intent(in)  :: znorm
    real(dp),                            intent(in)  :: area
    character(len=3),                    intent(in)  :: side
    real(dp), dimension(:),              intent(in)  :: unorm_bc
    real(dp),                            intent(in)  :: u_tau_init
    real(dp), dimension(:),            intent(inout) :: area_local
    real(dp), dimension(:),            intent(inout) :: yplus_local
    real(dp), dimension(:),            intent(inout) :: uplus_local
    real(dp), dimension(:),            intent(inout) :: u_tau_local
    real(dp), dimension(:),            intent(inout) :: nu_local
    real(dp), dimension(:),            intent(inout) :: rho_local
    real(dp), dimension(:),            intent(inout) :: v_corr_local
    real(dp), dimension(:),            intent(inout) :: phi_local
    real(dp),                          intent(inout) :: f1_nonconv_local
    real(dp),                          intent(inout) :: f2_nonconv_local
    real(dp),                          intent(inout) :: f3_nonconv_local
    real(dp),                          intent(inout) :: f4_nonconv_local

    real(dp) :: xmr
    real(dp), dimension(5)                           :: stresses
    real(dp), dimension(5)                           :: pressure
    real(dp), dimension(5,5)                         :: stresses_jacobian
    real(dp), dimension(5,5)                         :: pressure_jacobian

    continue

    xmr             = xmach / re

    if ( side == 'rhs' ) then

      stresses = residual_wall_function_tauij ( grid, soln%q_dof, soln%amut,   &
                                   soln%gradx, soln%grady, soln%gradz,         &
                                   ib, face_index, face_corner ,               &
                                   nodes_per_face,                             &
                                   max_node_per_cell, xmr, gamma,              &
                                   sutherland_constant, tref,                  &
                                   turbulent_prandtl,                          &
                                   xnorm, ynorm, znorm, area,                  &
                                   soln%eqn_set, unorm_bc,                     &
                                   u_tau_init,                                 &
                                   area_local, yplus_local, uplus_local,       &
                                   u_tau_local, nu_local, rho_local,           &
                                   v_corr_local, phi_local,                    &
                                   f1_nonconv_local,                           &
                                   f2_nonconv_local,                           &
                                   f3_nonconv_local,                           &
                                   f4_nonconv_local )

      pressure = residual_viscous_weak_pressure ( grid, soln%q_dof,            &
                                     ib, face_index, face_corner,              &
                                     nodes_per_face,                           &
                                     max_node_per_cell,                        &
                                     xnorm, ynorm, znorm, area,                &
                                     soln%eqn_set )

      flux(1:5,1) = stresses(1:5) - pressure(1:5)


    else if ( side == 'lhs' ) then

      stresses_jacobian = jacobian_viscous_weak_tauij (                        &
                                   grid, soln%q_dof, soln%amut,                &
                                   ib , face_index, face_corner,               &
                                   xnorm, ynorm, znorm, area,                  &
                                   nodes_per_face, xmr,                        &
                                   model_strain_form_int, soln%eqn_set,        &
                                   unorm_bc )

      pressure_jacobian = jacobian_viscous_weak_pressure (                     &
                          xnorm, ynorm, znorm, area )

      flux        = stresses_jacobian - pressure_jacobian

    endif

  end function viscous_wall_function_flux

!======================= RESIDUAL_VISCOUS_WEAK_TAUIJ =========================80
!
!  Determines a weak wall boundary condition flux
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function residual_viscous_weak_tauij ( grid, q_dof, amut, ib,           &
                                  face_index, nodes_per_face,                  &
                                  xmr, xnorm, ynorm, znorm, area,              &
                                  eqn_set, unorm_bc                            &
                                  ) result ( flux_bc )

    use kinddefs,       only : dp
    use ddt,            only : ddt5
    use grid_types,     only : grid_type
    use turbulence_info,only : model_strain_form_int
    use wall_model,     only : wall_reynolds_stress

    real(dp),   dimension(5)                         :: flux_bc

    type(grid_type),                   intent(in)    :: grid
    real(dp),          dimension(:,:), intent(in)    :: q_dof
    real(dp),          dimension(:  ), intent(in)    :: amut
    integer,                           intent(in)    :: ib
    integer,                           intent(in)    :: face_index
    integer,                           intent(in)    :: nodes_per_face
    real(dp),                          intent(in)    :: xmr
    integer,                           intent(in)    :: eqn_set
    real(dp),                          intent(in)    :: xnorm
    real(dp),                          intent(in)    :: ynorm
    real(dp),                          intent(in)    :: znorm
    real(dp),                          intent(in)    :: area
    real(dp),          dimension(:  ), intent(in)    :: unorm_bc

    type(ddt5), dimension(3,3)               :: rhotauij_ddt
    real(dp),   dimension(3,3)               :: rhotauij
    real(dp)                                 :: vf2, vf3, vf4
    real(dp)                                 :: areax, areay, areaz

    real(dp), parameter                      :: zero = 0.0_dp

  continue

!-----------------------------------------------------------------------------80
!   viscous stresses on the element boundary face
    areax        = -xnorm*area
    areay        = -ynorm*area
    areaz        = -znorm*area

    rhotauij_ddt = wall_reynolds_stress ( grid, q_dof, amut, ib,               &
                                      face_index, nodes_per_face, xmr,         &
                                     model_strain_form_int, eqn_set, unorm_bc )

    rhotauij     = rhotauij_ddt%f

    vf2   = areax*rhotauij(1,1) + areay*rhotauij(1,2) + areaz*rhotauij(1,3)
    vf3   = areax*rhotauij(2,1) + areay*rhotauij(2,2) + areaz*rhotauij(2,3)
    vf4   = areax*rhotauij(3,1) + areay*rhotauij(3,2) + areaz*rhotauij(3,3)

!-----------------------------------------------------------------------------80
    flux_bc(1)  = zero
    flux_bc(2)  = vf2
    flux_bc(3)  = vf3
    flux_bc(4)  = vf4
    flux_bc(5)  = zero

    if ( skeleton > 21 ) then
    write(6,'(a,15(1x,f20.10))') 'weak wall '
    write(6,'(a,12(es15.5))') 'tau(1,:)     ',  &
      rhotauij(1,1), rhotauij(1,2), rhotauij(1,3)
    write(6,'(a,12(es15.5))') 'tau(2,:)     ',  &
      rhotauij(2,1), rhotauij(2,2), rhotauij(2,3)
    write(6,'(a,12(es15.5))') 'tau(3,:)     ',  &
      rhotauij(3,1), rhotauij(3,2), rhotauij(3,3)
    write(6,'(a,12(es15.5))') 'mom flux     ', vf2,vf3,vf4
    endif

  end function residual_viscous_weak_tauij

!====================== RESIDUAL_WALL_FUNCTION_TAUIJ =========================80
!
!  Determines a wall function boundary condition flux
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function residual_wall_function_tauij ( grid, q_dof, amut,              &
                                  gradx, grady, gradz, ib,                     &
                                  face_index, face_corner,                     &
                                  nodes_per_face,                              &
                                  max_node_per_cell, xmr,                      &
                                  gamma, sutherland_constant, tref,            &
                                  turbulent_prandtl,                           &
                                  xnorm, ynorm, znorm, area,                   &
                                  eqn_set, unorm_bc,                           &
                                  u_tau_init,                                  &
                                  area_local, yplus_local, uplus_local,        &
                                  u_tau_local, nu_local, rho_local,            &
                                  v_corr_local, phi_local,                     &
                                  f1_nonconv_local,                            &
                                  f2_nonconv_local,                            &
                                  f3_nonconv_local,                            &
                                  f4_nonconv_local                             &
                                ) result ( flux_bc )


    use kinddefs,       only : dp
    use grid_types,     only : grid_type
    use wall_model,     only : wall_function_reynolds_stress

    real(dp),   dimension(5)                       :: flux_bc

    type(grid_type),                   intent(in)  :: grid
    real(dp),          dimension(:,:), intent(in)  :: q_dof
    real(dp),          dimension(:  ), intent(in)  :: amut
    real(dp),          dimension(:,:), intent(in)  :: gradx
    real(dp),          dimension(:,:), intent(in)  :: grady
    real(dp),          dimension(:,:), intent(in)  :: gradz
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: face_index
    integer,                           intent(in)  :: face_corner
    integer,                           intent(in)  :: nodes_per_face
    integer,                           intent(in)  :: max_node_per_cell
    real(dp),                          intent(in)  :: xmr
    real(dp),                          intent(in)  :: gamma
    real(dp),                          intent(in)  :: turbulent_prandtl
    real(dp),                          intent(in)  :: sutherland_constant
    real(dp),                          intent(in)  :: tref
    integer,                           intent(in)  :: eqn_set
    real(dp),                          intent(in)  :: xnorm
    real(dp),                          intent(in)  :: ynorm
    real(dp),                          intent(in)  :: znorm
    real(dp),                          intent(in)  :: area
    real(dp),          dimension(:  ), intent(in)  :: unorm_bc
    real(dp),                          intent(in)  :: u_tau_init
    real(dp), dimension(:),            intent(inout) :: area_local
    real(dp), dimension(:),            intent(inout) :: yplus_local
    real(dp), dimension(:),            intent(inout) :: uplus_local
    real(dp), dimension(:),            intent(inout) :: u_tau_local
    real(dp), dimension(:),            intent(inout) :: nu_local
    real(dp), dimension(:),            intent(inout) :: rho_local
    real(dp), dimension(:),            intent(inout) :: v_corr_local
    real(dp), dimension(:),            intent(inout) :: phi_local
    real(dp),                          intent(inout) :: f1_nonconv_local
    real(dp),                          intent(inout) :: f2_nonconv_local
    real(dp),                          intent(inout) :: f3_nonconv_local
    real(dp),                          intent(inout) :: f4_nonconv_local

!   type(ddt5), dimension(3,3)               :: rhotauij_ddt
!   real(dp),   dimension(3,3)               :: rhotauij
!   real(dp),   dimension(5)                 :: momentum_flux
!   real(dp)                                 :: vf2, vf3, vf4
!   real(dp)                                 :: areax, areay, areaz

!   real(dp), parameter                      :: zero = 0.0_dp

  continue

!-----------------------------------------------------------------------------80
!   viscous stresses on the element boundary face
!   areax        = -xnorm*area
!   areay        = -ynorm*area
!   areaz        = -znorm*area

    flux_bc = wall_function_reynolds_stress ( grid, q_dof, amut,               &
                                   gradx, grady, gradz,                        &
                                   ib, face_index, face_corner ,               &
                                   nodes_per_face,                             &
                                   max_node_per_cell, xmr, gamma,              &
                                   sutherland_constant, tref,                  &
                                   turbulent_prandtl,                          &
                                   xnorm, ynorm, znorm, area,                  &
                                   eqn_set, unorm_bc,                          &
                                   u_tau_init,                                 &
                                   area_local, yplus_local, uplus_local,       &
                                   u_tau_local, nu_local, rho_local,           &
                                   v_corr_local, phi_local,                    &
                                   f1_nonconv_local,                           &
                                   f2_nonconv_local,                           &
                                   f3_nonconv_local,                           &
                                   f4_nonconv_local )

!   rhotauij_ddt = wall_reynolds_stress ( grid, q_dof, amut, ib                &
!                                   , face_index, nodes_per_face, xmr          &
!                                   , model_strain_form, eqn_set, unorm_bc )

!   rhotauij     = rhotauij_ddt%f

!   vf2   = areax*rhotauij(1,1) + areay*rhotauij(1,2) + areaz*rhotauij(1,3)
!   vf3   = areax*rhotauij(2,1) + areay*rhotauij(2,2) + areaz*rhotauij(2,3)
!   vf4   = areax*rhotauij(3,1) + areay*rhotauij(3,2) + areaz*rhotauij(3,3)
!-----------------------------------------------------------------------------80
  end function residual_wall_function_tauij

!===================== RESIDUAL_VISCOUS_WEAK_PRESSURE ========================80
!
!  Determines a weak wall boundary condition flux
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function residual_viscous_weak_pressure ( grid, q_dof, ib               &
                                , face_index, face_corner                      &
                                , nodes_per_face                               &
                                , max_node_per_cell                            &
                                , xnorm, ynorm, znorm, area                    &
                                , eqn_set                                      &
                                ) result ( flux_bc )

    use kinddefs,       only : dp
    use grid_types,     only : grid_type
    use geometry_utils, only : return_median_points, return_median_center      &
                             , element_stats, fetch_element_nodes
    use wall_model,     only : get_element_qp

    real(dp),   dimension(5)                       :: flux_bc

    type(grid_type),                   intent(in)  :: grid
    real(dp),          dimension(:,:), intent(in)  :: q_dof
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: face_index
    integer,                           intent(in)  :: face_corner
    integer,                           intent(in)  :: nodes_per_face
    integer,                           intent(in)  :: max_node_per_cell
    integer,                           intent(in)  :: eqn_set
    real(dp),                          intent(in)  :: xnorm
    real(dp),                          intent(in)  :: ynorm
    real(dp),                          intent(in)  :: znorm
    real(dp),                          intent(in)  :: area

    integer, dimension(max_node_per_cell)    :: c2n_cell
    integer, dimension(max_node_per_cell)    :: node_map

    real(dp), dimension(0:max_node_per_cell) :: px, py, pz
    real(dp), dimension(5,max_node_per_cell) :: qp
    real(dp), dimension(4,3)                 :: p_bc_dual
    real(dp), dimension(5)                   :: q_bcface_dual

    real(dp)                                 :: p
    real(dp), dimension(3)                   :: pos_bcface_dual
    real(dp)                                 :: areax, areay, areaz

    real(dp), parameter                      :: zero = 0.0_dp

    integer                                  :: icell, ielem
    integer                                  :: nodes_per_cell
    integer                                  :: cell_node

  continue

    px            = zero
    py            = zero
    pz            = zero
!-----------------------------------------------------------------------------80
!   get the element type and cell index associated
!   with the boundary face from the face-to-node array
!   and fill local volume element information
    icell = 0
    ielem = 0
    select case( nodes_per_face )
      case( 3 )
        icell         = grid%bc(ib)%f2ntb(face_index,4)
        ielem         = grid%bc(ib)%f2ntb(face_index,5)
      case( 4 )
        icell         = grid%bc(ib)%f2nqb(face_index,5)
        ielem         = grid%bc(ib)%f2nqb(face_index,6)
    end select

    node_map(:)    = 0
    nodes_per_cell = grid%elem(ielem)%node_per_cell

    do cell_node = 1, nodes_per_cell
      node_map(cell_node) = cell_node
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

    qp = get_element_qp     ( q_dof, c2n_cell, node_map, eqn_set               &
                            , nodes_per_cell, 5 )

!-----------------------------------------------------------------------------80
!   viscous stresses on the element boundary face
    areax        = -xnorm*area
    areay        = -ynorm*area
    areaz        = -znorm*area

!-----------------------------------------------------------------------------80
! Place element geometry information in the local arrays (px,py,pz)
    call fetch_element_nodes( grid, ib, face_index, nodes_per_face, px, py, pz )

!-----------------------------------------------------------------------------80
! Extract the four corners of the dual,
! calculate boundary face dual location
! and then the flow field at the wall
    p_bc_dual     = return_median_points ( grid%x, grid%y, grid%z, grid%bc     &
                                , ib, face_index, face_corner, nodes_per_face )

    pos_bcface_dual = return_median_center ( p_bc_dual )

!
    px(0)           = pos_bcface_dual(1)
    py(0)           = pos_bcface_dual(2)
    pz(0)           = pos_bcface_dual(3)
    q_bcface_dual   = element_stats (5, nodes_per_cell, px, py, pz, qp )
    p               = q_bcface_dual(5)
!-----------------------------------------------------------------------------80
    flux_bc(1)  = zero
    flux_bc(2)  = p * areax
    flux_bc(3)  = p * areay
    flux_bc(4)  = p * areaz
    flux_bc(5)  = zero

    if ( skeleton > 21 ) then
    write(6,'(a,15(1x,f20.10))') 'weak wall pressure '
    write(6,'(a,12(es15.5))') 'pres area    ', p * areax,p*areay,p*areaz
    endif

  end function residual_viscous_weak_pressure

!============================ GET_WALL_FLUX ==================================80
!
! Closes off viscous flux on boundaries for the general (mixed) element case
! for incompressible. It is the generalization of the original tetrahedral
! formulation to more general elements.
!
! Note: qnode assumed to contain primitive variables
!
!=============================================================================80

  subroutine test_weak_wall_flux  ( grid, q_dof, amut, res, ib, eqn_set )

    use grid_types,              only : grid_type
    use info_depr,               only : re, xmach, twod
    use twod_util,               only : yplane_2d, y_coplanar_tol
    use grid_metrics,            only : dual_area_quad
    use turbulence_info,         only : model_strain_form_int
    use nml_boundary_conditions, only : unorm_bc
    use ddt,                     only : ddt5, assignment(=), operator(-)       &
                                      , operator(*), operator(+), operator(/)
    use wall_model,              only : wall_reynolds_stress
    use nml_noninertial_reference_frame, only : noninertial

    type(grid_type),                       intent(in) :: grid
    real(dp), dimension(:,:),              intent(in) :: q_dof
    real(dp), dimension(:),                intent(in) :: amut
    real(dp), dimension(:,:),              intent(inout) :: res
    integer,                               intent(in) :: ib
    integer,                               intent(in) :: eqn_set

    integer                                :: node
    integer                                :: bnode1, bnode2, bnode3, bnode4
    integer                                :: face_index
    integer                                :: corner_index
    integer                                :: nodes_per_face

    real(dp)                               :: xmr
    real(dp)                               :: x1, x2, x3
    real(dp)                               :: y1, y2, y3
    real(dp)                               :: z1, z2, z3
    real(dp)                               :: areax, areay, areaz

    real(dp),   dimension(4)               :: xnorm_q, ynorm_q, znorm_q

    type(ddt5)                             :: vf2, vf3, vf4
    type(ddt5), dimension(3,3)             :: rhotauij

    real(dp),  parameter                   :: zero   = 0.0_dp
    real(dp),  parameter                   :: my_6th = 1.0_dp/6.0_dp

  continue

    xmr   = xmach / re
    unorm_bc = zero
!   cgp   = xmr / (gm1*prandtl)
!   cgpt  = xmr / (gm1*turbulent_prandtl)

!=============================================================================80
! Loop over all the triangle boundary faces
!=============================================================================80
    tria_faces:  if ( grid%bc(ib)%nbfacet > 0 ) then

      nodes_per_face = 3

      loop_tria_faces: do face_index = 1, grid%bc(ib)%nbfacet

        rhotauij  = zero

! Viscous contributions at dual face [ full Navier-Stokes terms ]
        rhotauij = wall_reynolds_stress ( grid, q_dof, amut, ib                &
                                        , face_index, nodes_per_face, xmr      &
                                 , model_strain_form_int, eqn_set, unorm_bc )

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a triangle, this is just 1/3 the face normal, and is
! the same at each node

      bnode1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,1))
      bnode2 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,2))
      bnode3 = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,3))

      x1 = grid%x(bnode1)
      x2 = grid%x(bnode2)
      x3 = grid%x(bnode3)

      y1 = grid%y(bnode1)
      y2 = grid%y(bnode2)
      y3 = grid%y(bnode3)

      z1 = grid%z(bnode1)
      z2 = grid%z(bnode2)
      z3 = grid%z(bnode3)

!       - sign on normals because Green-Gauss needs outward pointing normals

      areax = -my_6th*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
      areay = -my_6th*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
      areaz = -my_6th*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

      vf2 = areax*rhotauij(1,1) + areay*rhotauij(1,2) + areaz*rhotauij(1,3)
      vf3 = areax*rhotauij(2,1) + areay*rhotauij(2,2) + areaz*rhotauij(2,3)
      vf4 = areax*rhotauij(3,1) + areay*rhotauij(3,2) + areaz*rhotauij(3,3)

      loop_tri_nodes:  do corner_index = 1, nodes_per_face

        node = grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,corner_index))

        if (twod) then
           if (abs(grid%y(node)-yplane_2d) > y_coplanar_tol) cycle
        end if

        if (node <= grid%nnodes0) then

          res(2,node) = res(2,node) - vf2%f
          res(3,node) = res(3,node) - vf3%f
          res(4,node) = res(4,node) - vf4%f

        end if

      end do loop_tri_nodes

    end do loop_tria_faces

  endif tria_faces
!=============================================================================80
! Now loop over all the quad boundary faces
!=============================================================================80
    quad_faces:  if ( grid%bc(ib)%nbfaceq > 0 ) then

      nodes_per_face = 4

      loop_quad_faces: do face_index = 1, grid%bc(ib)%nbfaceq

        rhotauij  = zero

! Viscous contributions at dual face [ full Navier-Stokes terms ]
        rhotauij = wall_reynolds_stress ( grid, q_dof, amut, ib                &
                                        , face_index, nodes_per_face, xmr      &
                                 , model_strain_form_int, eqn_set, unorm_bc )

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a quad, the dual normal is in general different at
! at each node on the face
        bnode1 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,1))
        bnode2 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,2))
        bnode3 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,3))
        bnode4 = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,4))

        call dual_area_quad ( grid%nnodes01,grid%x,grid%y,grid%z               &
                            , bnode1,bnode2,bnode3,bnode4,noninertial          &
                            , xnorm_q,ynorm_q,znorm_q)

        loop_quad_nodes: do corner_index = 1, nodes_per_face

          node = grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,corner_index))

          if (twod) then
            if (abs(grid%y(node)-yplane_2d) > y_coplanar_tol) cycle
          end if

          areax = xnorm_q(corner_index)
          areay = ynorm_q(corner_index)
          areaz = znorm_q(corner_index)

          vf2 = areax*rhotauij(1,1) + areay*rhotauij(1,2) + areaz*rhotauij(1,3)
          vf3 = areax*rhotauij(2,1) + areay*rhotauij(2,2) + areaz*rhotauij(2,3)
          vf4 = areax*rhotauij(3,1) + areay*rhotauij(3,2) + areaz*rhotauij(3,3)

          if (node <= grid%nnodes0) then

            res(2,node) = res(2,node) - vf2%f
            res(3,node) = res(3,node) - vf3%f
            res(4,node) = res(4,node) - vf4%f

          end if

        end do loop_quad_nodes

      end do loop_quad_faces

    endif quad_faces

  end subroutine test_weak_wall_flux

!============================ WEAK_WALL_JACOBIANS_STRESSES ===================80
!
!  Calculates the viscous stress Jacobian contribution for the
!  weak viscous wall boundary condition
!
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!  Jacobians are in conserved variables
!=============================================================================80

  function jacobian_viscous_weak_tauij ( grid, q_dof, amut, ib                 &
                                , face_index, face_corner                      &
                                , xnorm, ynorm, znorm, area                    &
                                , nodes_per_face, xmr                          &
                                , model_strain_form_int, eqn_set               &
                                , unorm_bc                                     &
                                    )                                          &
  result ( deqn_dq )

    use grid_types,      only : grid_type
    use info_depr,       only : twod, tref
    use fluid,           only : gamma, sutherland_constant, gm1, prandtl
    use generic_gas_map, only : n_momx, n_momy, n_momz
    use turb_parameters, only : turbulent_prandtl
    use turbulence_info, only : traceless
    use element_defs,    only : max_face_per_cell, max_node_per_cell
    use utilities,       only : cell_gradients, cell_jacobians
    use thermo,          only : q_type, primitive_q_type, conserved_q_type
    use solution_types,  only : compressible
    use bc_strong_nc,    only : wall_properties


    real(dp), dimension(5,5)               :: deqn_dq

    type(grid_type),            intent(in) :: grid
    real(dp), dimension(:,:),   intent(in) :: q_dof
    real(dp), dimension(:),     intent(in) :: amut
    integer,                    intent(in) :: ib
    integer,                    intent(in) :: face_index
    integer,                    intent(in) :: face_corner
    integer,                    intent(in) :: nodes_per_face
    real(dp),                   intent(in) :: xmr
    real(dp),                   intent(in) :: xnorm
    real(dp),                   intent(in) :: ynorm
    real(dp),                   intent(in) :: znorm
    real(dp),                   intent(in) :: area
    integer,                    intent(in) :: model_strain_form_int
    integer,                    intent(in) :: eqn_set
    real(dp), dimension(:),     intent(in) :: unorm_bc


    integer, dimension(max_node_per_cell)  :: c2n_cell
    integer, dimension(max_node_per_cell)  :: wall
    integer, dimension(max_node_per_cell)  :: node_map

    integer                                    :: i, icell, ielem, face_2d
    integer                                    :: i_local
    integer                                    :: node
    integer                                    :: edges_local, nodes_local
    integer                                    :: corner_node
    integer                                    :: cell_node
    integer                                    :: face_corner_index
    integer                                    :: boundary_node

    real(dp)                                   :: cell_vol
    real(dp)                                   :: cell_avg_factor
    real(dp)                                   :: cstar
    real(dp)                                   :: cgp, cgpt

    real(dp), dimension(max_face_per_cell)      :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node

    real(dp),   dimension(max_node_per_cell)    :: rho_node
    real(dp),   dimension(max_node_per_cell)    :: ri_node
    real(dp),   dimension(max_node_per_cell)    :: u_node, v_node, w_node
    real(dp),   dimension(max_node_per_cell)    :: mu_node
    real(dp),   dimension(max_node_per_cell)    :: mus_node
    real(dp),   dimension(max_node_per_cell)    :: T_node
    real(dp),   dimension(max_node_per_cell)    :: p_node
    real(dp),   dimension(max_node_per_cell)    :: h_node
    real(dp),   dimension(max_node_per_cell)    :: e_node
    real(dp),   dimension(5,max_node_per_cell)  :: q_node
    real(dp),   dimension(5)                    :: gradx_cell, grady_cell
    real(dp),   dimension(5)                    :: gradz_cell
    real(dp),   dimension(3,3)                  :: gradv
    real(dp),   dimension(3,3)                  :: sij
    real(dp),   dimension(3,3)                  :: sijbar
    real(dp),   dimension(3,3)                  :: rhotauij
    real(dp)                                    :: rmu
    real(dp)                                    :: rho_avg
    real(dp)                                    :: p_avg
    real(dp)                                    :: h_avg
    real(dp)                                    :: wall_temp
    real(dp)                                    :: mus, umus, vmus, wmus

    real(dp), dimension(5)                      :: qp_exact
    real(dp)                                    :: heating
    real(dp)                                    :: t_old
    real(dp)                                    :: fact
    real(dp), dimension(max_node_per_cell)      :: wall_dxdt
    real(dp), dimension(max_node_per_cell)      :: wall_dydt
    real(dp), dimension(max_node_per_cell)      :: wall_dzdt
    real(dp), dimension(max_node_per_cell)      :: dgradx_celldq
    real(dp), dimension(max_node_per_cell)      :: dgrady_celldq
    real(dp), dimension(max_node_per_cell)      :: dgradz_celldq

    real(dp), dimension(max_node_per_cell)      :: duxavgdu
    real(dp), dimension(max_node_per_cell)      :: dvxavgdv
    real(dp), dimension(max_node_per_cell)      :: dwxavgdw
    real(dp), dimension(max_node_per_cell)      :: dTxavgdT
    real(dp), dimension(max_node_per_cell)      :: duyavgdu
    real(dp), dimension(max_node_per_cell)      :: dvyavgdv
    real(dp), dimension(max_node_per_cell)      :: dwyavgdw
    real(dp), dimension(max_node_per_cell)      :: dTyavgdT
    real(dp), dimension(max_node_per_cell)      :: duzavgdu
    real(dp), dimension(max_node_per_cell)      :: dvzavgdv
    real(dp), dimension(max_node_per_cell)      :: dwzavgdw
    real(dp), dimension(max_node_per_cell)      :: dTzavgdT

    real(dp), dimension(max_node_per_cell)      :: duxdu
    real(dp), dimension(max_node_per_cell)      :: duydu
    real(dp), dimension(max_node_per_cell)      :: duzdu
    real(dp), dimension(max_node_per_cell)      :: dvxdv
    real(dp), dimension(max_node_per_cell)      :: dvydv
    real(dp), dimension(max_node_per_cell)      :: dvzdv
    real(dp), dimension(max_node_per_cell)      :: dwxdw
    real(dp), dimension(max_node_per_cell)      :: dwydw
    real(dp), dimension(max_node_per_cell)      :: dwzdw
    real(dp), dimension(max_node_per_cell)      :: dTxdT
    real(dp), dimension(max_node_per_cell)      :: dTydT
    real(dp), dimension(max_node_per_cell)      :: dTzdT


    real(dp), dimension(max_node_per_cell)      :: dvf2du, dvf2dv, dvf2dw
    real(dp), dimension(max_node_per_cell)      :: dvf3du, dvf3dv, dvf3dw
    real(dp), dimension(max_node_per_cell)      :: dvf4du, dvf4dv, dvf4dw
    real(dp), dimension(max_node_per_cell)      :: dvf5du, dvf5dv, dvf5dw
    real(dp), dimension(max_node_per_cell)      :: dvf2dT
    real(dp), dimension(max_node_per_cell)      :: dvf3dT
    real(dp), dimension(max_node_per_cell)      :: dvf4dT
    real(dp), dimension(max_node_per_cell)      :: dvf5dT

    real(dp)                                    :: dmudT
    real(dp), dimension(max_node_per_cell)      :: dmusdT
    real(dp), dimension(max_node_per_cell)      :: dumusdu
    real(dp), dimension(max_node_per_cell)      :: dumusdT
    real(dp), dimension(max_node_per_cell)      :: dvmusdv
    real(dp), dimension(max_node_per_cell)      :: dvmusdT
    real(dp), dimension(max_node_per_cell)      :: dwmusdw
    real(dp), dimension(max_node_per_cell)      :: dwmusdT

!   real(dp)                               :: ubar
    real(dp)                               :: tx, ty, tz
    real(dp)                               :: txavg, tyavg, tzavg
    real(dp)                               :: cgpu, rmucgp
    real(dp)                               :: txx, txy, txz, tyy, tyz, tzz
    real(dp)                               :: vf2, vf3, vf4
    real(dp)                               :: areax, areay, areaz
    real(dp)                               :: dtxxdu, dtxxdv, dtxxdw
    real(dp)                               :: dtxydu, dtxydv
    real(dp)                               :: dtxzdu,         dtxzdw
    real(dp)                               :: dtyydu, dtyydv, dtyydw
    real(dp)                               ::         dtyzdv, dtyzdw
    real(dp)                               :: dtzzdu, dtzzdv, dtzzdw
    real(dp)                               :: rax, ray, raz
    real(dp)                               :: tax, tay, taz
    real(dp)                               :: draxdT, draydT, drazdT
    real(dp)                               :: dtaxdT, dtaydT, dtazdT

    real(dp)    :: piece, dpiecedu, dpiecedv, dpiecedw, dpiecedT

!   real(dp)    :: u, v, w
!   real(dp)    :: q2

    real(dp),  parameter                   :: zero   = 0.0_dp
    real(dp),  parameter                   :: one    = 1.0_dp
    real(dp),  parameter                   :: two    = 2.0_dp
    real(dp),  parameter                   :: third  = 1.0_dp/3.0_dp
    real(dp),  parameter                   :: c23    = 2.0_dp/3.0_dp
    real(dp),  parameter                   :: c43    = 4.0_dp/3.0_dp

  continue

    cstar      = sutherland_constant / tref
    cgp        = xmr / (gm1*prandtl)
    cgpt       = xmr / (gm1*turbulent_prandtl)
    cgpu       = one / (gm1*prandtl)

    heating    = zero
    areax      = xnorm * area
    areay      = ynorm * area
    areaz      = znorm * area


! Zero out some derivatives of viscosity stuff; we may
! or may not fill these in with values later

    gradx_cell    = 0.0_dp
    grady_cell    = 0.0_dp
    gradz_cell    = 0.0_dp
    dgradx_celldq = 0.0_dp
    dgrady_celldq = 0.0_dp
    dgradz_celldq = 0.0_dp

    deqn_dq       = zero

    icell             = 0
    ielem             = 0
    node              = 0
    face_corner_index = 0

!-----------------------------------------------------------------------------80
!  global node numbers of the cell/face nodes on the boundary
    select case ( nodes_per_face )
      case ( 3 )
        icell         = grid%bc(ib)%f2ntb(face_index,4)
        ielem         = grid%bc(ib)%f2ntb(face_index,5)
!       boundary_node = grid%bc(ib)%f2ntb(face_index,face_corner)

      case ( 4 )
        icell         = grid%bc(ib)%f2nqb(face_index,5)
        ielem         = grid%bc(ib)%f2nqb(face_index,6)
!       boundary_node = grid%bc(ib)%f2nqb(face_index,face_corner)

    end select

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

    do cell_node = 1, grid%elem(ielem)%node_per_cell
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    node_map(:) = 0

    if (twod) then

          face_2d = grid%elem(ielem)%face_2d

          nodes_local = 3
          if (grid%elem(ielem)%local_f2n(face_2d,1) /=                         &
              grid%elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = grid%elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (grid%elem(ielem)%local_f2e(face_2d,1) /=                         &
              grid%elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

    else

          nodes_local = grid%elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = grid%elem(ielem)%edge_per_cell

    end if

!-----------------------------------------------------------------------------80
! Set up the parts that are only dependent on the geometry.

    x_node    = zero
    y_node    = zero
    z_node    = zero

    node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          cell_node = c2n_cell(i)

          x_node(i) = grid%x(cell_node)
          y_node(i) = grid%y(cell_node)
          z_node(i) = grid%z(cell_node)

    end do node_loop1

!-----------------------------------------------------------------------------80

! Set flag to indicate which cell nodes correspond to wall nodes

    wall      = 0         ! default to no nodes in cell on a solid wall
    wall_dxdt = 0._dp  ! zero unless moving wall
    wall_dydt = 0._dp
    wall_dzdt = 0._dp

    select case ( nodes_per_face )
    case ( 3 )

      do corner_node = 1, nodes_per_face
        boundary_node = grid%bc(ib)%f2ntb(face_index,corner_node)
        node          = grid%bc(ib)%ibnode(boundary_node)
        do cell_node = 1, grid%elem(ielem)%node_per_cell
          if (c2n_cell(cell_node) == node) then
            wall(cell_node) = 1    ! node on a solid wall
            call wall_properties( ib, boundary_node, grid, eqn_set, 5          &
                                , qp_exact, heating, t_old, unorm_bc)
            wall_dxdt(cell_node) = qp_exact(2)
            wall_dydt(cell_node) = qp_exact(3)
            wall_dzdt(cell_node) = qp_exact(4)
          end if
          if ( c2n_cell(cell_node) ==                                          &
            grid%bc(ib)%ibnode(grid%bc(ib)%f2ntb(face_index,face_corner)) )    &
            face_corner_index = cell_node
        end do
      end do

!     wall_temp = qp_exact(5)

    case ( 4 )

      do corner_node = 1, nodes_per_face
        boundary_node = grid%bc(ib)%f2nqb(face_index,corner_node)
        node          = grid%bc(ib)%ibnode(boundary_node)
        do cell_node = 1, grid%elem(ielem)%node_per_cell
          if (c2n_cell(cell_node) == node) then
            wall(cell_node) = 1    ! node on a solid wall
            call wall_properties( ib, boundary_node, grid, eqn_set, 5          &
                                , qp_exact, heating, t_old, unorm_bc)
            wall_dxdt(cell_node) = qp_exact(2)
            wall_dydt(cell_node) = qp_exact(3)
            wall_dzdt(cell_node) = qp_exact(4)
          end if
          if ( c2n_cell(cell_node) ==                                          &
            grid%bc(ib)%ibnode(grid%bc(ib)%f2nqb(face_index,face_corner)) )    &
            face_corner_index = cell_node
        end do
      end do

!      wall_temp = qp_exact(5)

    end select

!-----------------------------------------------------------------------------80
! Compute cell averages, cell center, and set up some local solution arrays

    u_node    = zero
    v_node    = zero
    w_node    = zero
    T_node    = zero
    p_node    = zero
    h_node    = zero
    mu_node   = zero
    mus_node  = zero
    q_node    = zero

    rmu       = zero
    rho_avg   = zero
    p_avg     = zero
    h_avg     = zero
    mus       = zero
    umus      = zero
    vmus      = zero
    wmus      = zero
    rmucgp    = zero

!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80
    node_loop2 : do i_local = 1, nodes_local

!         local node number

      i = node_map(i_local)

      cell_node = c2n_cell(i)

      if ( eqn_set == compressible .and. q_type == primitive_q_type ) then

        rho_node(i) = q_dof(1,cell_node)
        u_node(i)   = q_dof(n_momx,cell_node)
        v_node(i)   = q_dof(n_momy,cell_node)
        w_node(i)   = q_dof(n_momz,cell_node)
        p_node(i)   = q_dof(5,cell_node)
        e_node(i)   = ( p_node(i) / gm1 ) + 0.5_dp * rho_node(i)               &
                    * ( u_node(i)*u_node(i)                                    &
                    +   v_node(i)*v_node(i)                                    &
                    +   w_node(i)*w_node(i) )

      else                                                                     &
      if ( eqn_set == compressible .and. q_type == conserved_q_type ) then

        rho_node(i) = q_dof(1,cell_node)
        ri_node(i)  = one / rho_node(i)
        u_node(i)   = q_dof(n_momx,cell_node) * ri_node(i)
        v_node(i)   = q_dof(n_momy,cell_node) * ri_node(i)
        w_node(i)   = q_dof(n_momz,cell_node) * ri_node(i)
        e_node(i)   = q_dof(5,cell_node)
        p_node(i)   = get_p(q_dof(1:5,cell_node))

      endif

      fact = real(wall(i), dp)    ! 1 if WEAK wall point, 0 otherwise

      u_node(i) = u_node(i)*(1._dp - fact) + fact*wall_dxdt(i)
      v_node(i) = v_node(i)*(1._dp - fact) + fact*wall_dydt(i)
      w_node(i) = w_node(i)*(1._dp - fact) + fact*wall_dzdt(i)

      wall_temp   = gamma*p_node(i)/rho_node(i)
      T_node(i)   = gamma*p_node(i)/rho_node(i)*(1._dp - fact)             &
                  + fact*wall_temp
      h_node(i)   = ( e_node(i) - p_node(i) ) / rho_node(i)
      if ( skeleton > 21 ) then
        write(6,'(a,i5,15(1x,f20.10))') 'loop         ', i, fact, rho_node(i)  &
        , p_node(i), wall_temp, T_node(i)                                      &
        , viscosity_law ( cstar, T_node(i) ), amut(cell_node)
      endif
      mu_node(i)  = amut(cell_node)
! use laminar viscosity instead
      mu_node(i)  = viscosity_law ( cstar, T_node(i) )

      mus_node(i) = mu_node(i) ! + amut(node)

      rmu         = rmu     + mu_node(i)
      rmucgp      = rmucgp + mu_node(i)*cgp + amut(node)*cgpt
      rho_avg     = rho_avg + rho_node(i)
      p_avg       = p_avg   + p_node(i)
      h_avg       = h_avg   + h_node(i)
      mus         = mus  +             mus_node(i)
      umus        = umus + u_node(i) * mus_node(i)
      vmus        = vmus + v_node(i) * mus_node(i)
      wmus        = wmus + w_node(i) * mus_node(i)

    end do node_loop2

!       now compute cell averages by dividing by the number of nodes
!       that contributed

    cell_avg_factor = 1._dp / real(nodes_local, dp)

    rmu     = rmu     * cell_avg_factor
    rmucgp  = rmucgp  * cell_avg_factor
    rho_avg = rho_avg * cell_avg_factor
    p_avg   = p_avg   * cell_avg_factor
    h_avg   = h_avg   * cell_avg_factor
    umus    = umus    * cell_avg_factor
    vmus    = vmus    * cell_avg_factor
    wmus    = wmus    * cell_avg_factor
!-----------------------------------------------------------------------------80
!-----------------------------------------------------------------------------80


!-----------------------------------------------------------------------------80

    if ( skeleton > 21 ) then
      write(6,'(a,15(1x,f15.5))') 'rho_node     ', rho_node
      write(6,'(a,15(1x,f15.5))') 'u_node       ', u_node
      write(6,'(a,15(1x,f15.5))') 'v_node       ', v_node
      write(6,'(a,15(1x,f15.5))') 'w_node       ', w_node
      write(6,'(a,15(1x,f15.5))') 'p_node       ', p_node
      write(6,'(a,15(1x,f15.5))') 'T_node       ', T_node
      write(6,'(a,15(1x,f15.5))') 'mu_node      ', mu_node
    endif

    do i_local = 1, nodes_local

      i = node_map(i_local)

      dmudT      = dviscosity_law( cstar, T_node(i) )

      dmusdT(i)  = cell_avg_factor*dmudT

      dumusdu(i) = mus_node(i)*cell_avg_factor
      dumusdT(i) =   u_node(i)*cell_avg_factor*dmudT

      dvmusdv(i) = mus_node(i)*cell_avg_factor
      dvmusdT(i) =   v_node(i)*cell_avg_factor*dmudT

      dwmusdw(i) = mus_node(i)*cell_avg_factor
      dwmusdT(i) =   w_node(i)*cell_avg_factor*dmudT

    enddo

! Get the gradients in the primal cell via Green-Gauss

    q_node(1,:) = rho_node(:)
    q_node(2,:) = u_node(:)
    q_node(3,:) = v_node(:)
    q_node(4,:) = w_node(:)
    q_node(5,:) = T_node(:)

        call cell_gradients( edges_local, max_node_per_cell,                   &
                            grid%elem(ielem)%face_per_cell,                    &
                            x_node, y_node, z_node,                            &
                            5, q_node, grid%elem(ielem)%local_f2n,             &
                            grid%elem(ielem)%e2n_2d, gradx_cell, grady_cell,   &
                            gradz_cell, cell_vol, nx, ny, nz)

    gradv(1,1) = gradx_cell(2)
    gradv(2,1) = gradx_cell(3)
    gradv(3,1) = gradx_cell(4)

    gradv(1,2) = grady_cell(2)
    gradv(2,2) = grady_cell(3)
    gradv(3,2) = grady_cell(4)

    gradv(1,3) = gradz_cell(2)
    gradv(2,3) = gradz_cell(3)
    gradv(3,3) = gradz_cell(4)

    txavg      = gradx_cell(5)
    tyavg      = grady_cell(5)
    tzavg      = gradz_cell(5)

    ! Jacobians of the gradients in the primal cell via Green-Gauss
    ! Note: these are with respect to the primitive variables

    call cell_jacobians( edges_local, max_node_per_cell,                       &
                        grid%elem(ielem)%face_per_cell,                        &
                        x_node, y_node, z_node,                                &
                        grid%elem(ielem)%local_f2n,                            &
                        grid%elem(ielem)%e2n_2d, dgradx_celldq,                &
                        dgrady_celldq, dgradz_celldq, cell_vol, nx, ny, nz)

      ! Jacobians with respect to conservative variables

      do i_local = 1, nodes_local

        i = node_map(i_local) !         local node number

        duxavgdu(i) = dgradx_celldq(i)
        dvxavgdv(i) = dgradx_celldq(i)
        dwxavgdw(i) = dgradx_celldq(i)
        dTxavgdT(i) = dgradx_celldq(i)

        duyavgdu(i) = dgrady_celldq(i)
        dvyavgdv(i) = dgrady_celldq(i)
        dwyavgdw(i) = dgrady_celldq(i)
        dTyavgdT(i) = dgrady_celldq(i)

        duzavgdu(i) = dgradz_celldq(i)
        dvzavgdv(i) = dgradz_celldq(i)
        dwzavgdw(i) = dgradz_celldq(i)
        dTzavgdT(i) = dgradz_celldq(i)

      end do
!----------------------------------------------------------------------------80
! components of symmetric stress tensor - traceless S_ij
    sij    = get_sij ( gradv )
    sijbar = sij
    if ( model_strain_form_int == traceless ) then
      sijbar(1,1) = sij(1,1) - third*(sij(1,1)+sij(2,2)+sij(3,3))
      sijbar(2,2) = sij(2,2) - third*(sij(1,1)+sij(2,2)+sij(3,3))
      sijbar(3,3) = sij(3,3) - third*(sij(1,1)+sij(2,2)+sij(3,3))
    endif

    rhotauij      = two * rmu * rho_avg * sijbar * xmr

!       [nondimensionalization factor xmr ] * [ viscosity ]
!       [ unit normal and area ] at dual face

    rax = xmr*rmu*areax
    ray = xmr*rmu*areay
    raz = xmr*rmu*areaz

    tax = rmucgp*areax
    tay = rmucgp*areay
    taz = rmucgp*areaz

    txx = rhotauij(1,1) / ( rmu*xmr ) ! c43*ux - c23*vy - c23*wz
    txy = rhotauij(1,2) / ( rmu*xmr ) ! uy + vx
    txz = rhotauij(1,3) / ( rmu*xmr ) ! uz + wx

    tyy = rhotauij(2,2) / ( rmu*xmr ) ! c43*vy - c23*ux - c23*wz
    tyz = rhotauij(2,3) / ( rmu*xmr ) ! vz + wy

    tzz = rhotauij(3,3) / ( rmu*xmr ) ! c43*wz - c23*ux - c23*vy

    vf2 = rax*txx + ray*txy + raz*txz
    vf3 = rax*txy + ray*tyy + raz*tyz
    vf4 = rax*txz + ray*tyz + raz*tzz

    if ( skeleton > 21 ) then
      write(6,'(a,15(1x,f15.5))') 'ux,vx,wx     ', gradv(:,1)
      write(6,'(a,15(1x,f15.5))') 'uy,vy,wy     ', gradv(:,2)
      write(6,'(a,15(1x,f15.5))') 'uz,vz,wz     ', gradv(:,3)
      write(6,'(a,15(1x,f15.5))') 'rmu          ', rmu
      write(6,'(a,15(1x,f15.5))') 'dgradx_celldq', dgradx_celldq
      write(6,'(a,15(1x,f15.5))') 'dgrady_celldq', dgrady_celldq
      write(6,'(a,15(1x,f15.5))') 'dgradz_celldq', dgradz_celldq
      write(6,'(a,15(1x,f15.5))') 'rhotauij     '
      write(6,'(3(1x,f15.5))') rhotauij
      write(*,*)
      write(6,'(3(1x,f15.5))') rmu*xmr*txx, rmu*xmr*txy, rmu*xmr*txz
      write(6,'(3(1x,f15.5))') rmu*xmr*txy, rmu*xmr*tyy, rmu*xmr*tyz
      write(6,'(3(1x,f15.5))') rmu*xmr*txz, rmu*xmr*tyz, rmu*xmr*tzz
    endif

!-----------------------------------------------------------------------------80

    tx = txavg
    ty = tyavg
    tz = tzavg

! Green-Gauss cell-average gradients (this
! is what the baseline code does for tets)

    do i_local = 1, nodes_local

      i = node_map(i_local) !           local node number

      ! u-gradient avg terms

      duxdu(i) = duxavgdu(i)
      duydu(i) = duyavgdu(i)
      duzdu(i) = duzavgdu(i)

      ! v-gradient avg terms


      dvxdv(i) = dvxavgdv(i)
      dvydv(i) = dvyavgdv(i)
      dvzdv(i) = dvzavgdv(i)

      ! w-gradient avg terms

      dwxdw(i) = dwxavgdw(i)
      dwydw(i) = dwyavgdw(i)
      dwzdw(i) = dwzavgdw(i)

      ! t-gradient avg terms

      dTxdT(i) = dTxavgdT(i)
      dTydT(i) = dTyavgdT(i)
      dTzdT(i) = dTzavgdT(i)

    end do


    i = face_corner_index

! Stress pieces

    dtxxdu =   c43*duxdu(i)
    dtxxdv = - c23*dvydv(i)
    dtxxdw = - c23*dwzdw(i)

    dtxydu = duydu(i)
    dtxydv = dvxdv(i)

    dtxzdu = duzdu(i)
    dtxzdw = dwxdw(i)

    dtyydu = - c23*duxdu(i)
    dtyydv =   c43*dvydv(i)
    dtyydw = - c23*dwzdw(i)

    dtyzdv = dvzdv(i)
    dtyzdw = dwydw(i)

    dtzzdu = - c23*duxdu(i)
    dtzzdv = - c23*dvydv(i)
    dtzzdw =   c43*dwzdw(i)

! Add stress contributions

    dvf2du(i) = rax*dtxxdu + ray*dtxydu + raz*dtxzdu
    dvf2dv(i) = rax*dtxxdv + ray*dtxydv
    dvf2dw(i) = rax*dtxxdw              + raz*dtxzdw
    dvf2dT(i) = 0._dp

    dvf3du(i) = rax*dtxydu + ray*dtyydu
    dvf3dv(i) = rax*dtxydv + ray*dtyydv + raz*dtyzdv
    dvf3dw(i) =              ray*dtyydw + raz*dtyzdw
    dvf3dT(i) = 0._dp

    dvf4du(i) = rax*dtxzdu              + raz*dtzzdu
    dvf4dv(i) =              ray*dtyzdv + raz*dtzzdv
    dvf4dw(i) = rax*dtxzdw + ray*dtyzdw + raz*dtzzdw
    dvf4dT(i) = 0._dp

! mu_laminar + mu_turbulent pieces

    draxdT = xmr*areax*dmusdT(i)
    draydT = xmr*areay*dmusdT(i)
    drazdT = xmr*areaz*dmusdT(i)
    dtaxdT =  cgpu*draxdT
    dtaydT =  cgpu*draydT
    dtazdT =  cgpu*drazdT


! Add mu_laminar + mu_turbulent contributions

    dvf2dT(i) = dvf2dT(i) + txx*draxdT + txy*draydT + txz*drazdT
    dvf3dT(i) = dvf3dT(i) + txy*draxdT + tyy*draydT + tyz*drazdT
    dvf4dT(i) = dvf4dT(i) + txz*draxdT + tyz*draydT + tzz*drazdT


! Stress and viscosity pieces for vf5 all at once

!  vf5 = tax*tx   + tay*ty  + taz*tz
!      + (umus*vf2 + vmus*vf3 + wmus*vf4)/mus

    piece = umus*vf2 + vmus*vf3 + wmus*vf4

    dpiecedu = umus*dvf2du(i) + vmus*dvf3du(i) + wmus*dvf4du(i) + &
               vf2*dumusdu(i)
    dpiecedv = umus*dvf2dv(i) + vmus*dvf3dv(i) + wmus*dvf4dv(i) + &
                                vf3*dvmusdv(i)
    dpiecedw = umus*dvf2dw(i) + vmus*dvf3dw(i) + wmus*dvf4dw(i) + &
                                                 vf4*dwmusdw(i)
    dpiecedT = umus*dvf2dT(i) + vmus*dvf3dT(i) + wmus*dvf4dT(i) + &
               vf2*dumusdT(i) + vf3*dvmusdT(i) + vf4*dwmusdT(i)

    dvf5du(i) = dpiecedu/mus
    dvf5dv(i) = dpiecedv/mus
    dvf5dw(i) = dpiecedw/mus
    dvf5dT(i) = tax    *dTxdT(i) + tay   *dTydT(i) + taz*dTzdT(i) &
              + tx    *dtaxdT    + ty   *dtaydT    + tz*dtazdT    &
              + (mus*dpiecedT    - piece*dmusdT(i))/mus/mus

    if ( skeleton > 21 ) then
      write(6,'(a,15(1x,f15.5))') 'dvf2         ', &
      dvf2du(i), dvf2dv(i), dvf2dw(i), dvf2dT(i)
      write(6,'(a,15(1x,f15.5))') 'dvf3         ', &
      dvf3du(i), dvf3dv(i), dvf3dw(i), dvf3dT(i)
      write(6,'(a,15(1x,f15.5))') 'dvf4         ', &
      dvf4du(i), dvf4dv(i), dvf4dw(i), dvf4dT(i)
      write(6,'(a,15(1x,f15.5))') 'draxdT       ', &
      draxdT, draydT, drazdT
      write(6,'(a,15(1x,f15.5))') 'dvfxdT       ', &
      dvf2dT(i), dvf3dT(i), dvf4dT(i)
      write(6,'(a,15(1x,f15.5))') 'dvf5         ', &
      dvf5du(i), dvf5dv(i), dvf5dw(i)
    endif


! viscous flux Jacobian contribution

    deqn_dq(2,1) = zero
    deqn_dq(2,2) = dvf2du(i)
    deqn_dq(2,3) = dvf2dv(i)
    deqn_dq(2,4) = dvf2dw(i)
    deqn_dq(2,5) = dvf2dT(i)

    deqn_dq(3,1) = zero
    deqn_dq(3,2) = dvf3du(i)
    deqn_dq(3,3) = dvf3dv(i)
    deqn_dq(3,4) = dvf3dw(i)
    deqn_dq(3,5) = dvf3dT(i)

    deqn_dq(4,1) = zero
    deqn_dq(4,2) = dvf4du(i)
    deqn_dq(4,3) = dvf4dv(i)
    deqn_dq(4,4) = dvf4dw(i)
    deqn_dq(4,5) = dvf4dT(i)

    deqn_dq(5,1) = zero
    deqn_dq(5,2) = dvf5du(i)
    deqn_dq(5,3) = dvf5dv(i)
    deqn_dq(5,4) = dvf5dw(i)
    deqn_dq(5,5) = dvf5dT(i)

! Now the energy/pressure contribution

!   u     = wall_dxdt(i)
!   v     = wall_dydt(i)
!   w     = wall_dzdt(i)
!   ubar  = xnorm*u + ynorm*v + znorm*w
!   q2    = u*u + v*v + w*w

!   deqn_dq(2,1) = deqn_dq(2,1)! + area*(xnorm*p_avg-u*ubar)
!   deqn_dq(2,2) = deqn_dq(2,2)
!   deqn_dq(2,3) = deqn_dq(2,3)
!   deqn_dq(2,4) = deqn_dq(2,4)
!   deqn_dq(2,5) = deqn_dq(2,5) + areax

!   deqn_dq(3,1) = deqn_dq(3,1)! + area*(ynorm*p_avg-v*ubar)
!   deqn_dq(3,2) = deqn_dq(3,2)
!   deqn_dq(3,3) = deqn_dq(3,3)
!   deqn_dq(3,4) = deqn_dq(3,4)
!   deqn_dq(3,5) = deqn_dq(3,5) + areay

!   deqn_dq(4,1) = deqn_dq(4,1)! + area*(znorm*p_avg-w*ubar)
!   deqn_dq(4,2) = deqn_dq(4,2)
!   deqn_dq(4,3) = deqn_dq(4,3)
!   deqn_dq(4,4) = deqn_dq(4,4)
!   deqn_dq(4,5) = deqn_dq(4,5) + areaz

!   deqn_dq(5,1) = deqn_dq(5,1) ! - area * gm1*half*q2
!   deqn_dq(5,2) = deqn_dq(5,2) - areax * p_avg
!   deqn_dq(5,3) = deqn_dq(5,3) - areay * p_avg
!   deqn_dq(5,4) = deqn_dq(5,4) - areaz * p_avg
!   deqn_dq(5,5) = deqn_dq(5,5) - area  * ubar

  end function jacobian_viscous_weak_tauij

!============================ WEAK_WALL_JACOBIANS ============================80
!
!  Calculates the pressure Jacobian contribution for the
!  weak viscous wall boundary condition
!
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!  Jacobians are in conserved variables
!=============================================================================80

  function jacobian_viscous_weak_pressure ( xnorm, ynorm, znorm, area )        &
  result ( deqn_dq )

    use generic_gas_map, only : n_momx, n_momy, n_momz

    real(dp), dimension(5,5)               :: deqn_dq

    real(dp),                   intent(in) :: xnorm
    real(dp),                   intent(in) :: ynorm
    real(dp),                   intent(in) :: znorm
    real(dp),                   intent(in) :: area

    real(dp)                               :: areax, areay, areaz
    real(dp), parameter                    :: zero = 0.0_dp

  continue

    deqn_dq = zero

    areax      = xnorm * area
    areay      = ynorm * area
    areaz      = znorm * area

!-----------------------------------------------------------------------------80

!   v     = wall_dydt(i)
!   w     = wall_dzdt(i)
!   ubar  = xnorm*u + ynorm*v + znorm*w
!   q2    = u*u + v*v + w*w

!   deqn_dq(2,1) = deqn_dq(2,1)! + area*(xnorm*p_avg-u*ubar)
    deqn_dq(n_momx,5) = - areax

!   deqn_dq(3,1) = deqn_dq(3,1)! + area*(ynorm*p_avg-v*ubar)
    deqn_dq(n_momy,5) = - areay

!   deqn_dq(4,1) = deqn_dq(4,1)! + area*(znorm*p_avg-w*ubar)
    deqn_dq(n_momz,5) = - areaz

  end function jacobian_viscous_weak_pressure

!============================ VISCOUS_POROUS_FLUX ============================80
!
!
!
!
!=============================================================================80

    function viscous_porous_flux ( grid, soln, ib, face_index, face_corner,    &
                                 nodes_per_face, xnorm, ynorm, znorm, area,    &
                                 side, unorm_bc, sink                          &
                                 )                                             &
    result ( flux )

    use kinddefs,                 only : dp
    use grid_types,               only : grid_type
    use solution_types,           only : soln_type

    use element_defs,             only : max_node_per_cell
    use info_depr,                only : xmach, re
    use turbulence_info,          only : model_strain_form_int
    use porous_model,             only : residual_porous_tauij,                &
                                         residual_porous_pressure
    use propulsion_types,         only : propulsion_system

    real(dp), dimension(5,5)                         :: flux

    type(grid_type),                     intent(in)    :: grid
    type(soln_type),                     intent(in)    :: soln
    integer,                             intent(in)    :: ib
    integer,                             intent(in)    :: face_index
    integer,                             intent(in)    :: face_corner
    integer,                             intent(in)    :: nodes_per_face
    real(dp),                            intent(in)    :: xnorm
    real(dp),                            intent(in)    :: ynorm
    real(dp),                            intent(in)    :: znorm
    real(dp),                            intent(in)    :: area
    character(len=3),                    intent(in)    :: side
    type(propulsion_system),             intent(in)    :: sink
    real(dp), dimension(:),              intent(inout) :: unorm_bc

    real(dp) :: xmr
    real(dp), dimension(5)                           :: stresses
    real(dp), dimension(5)                           :: pressure
    real(dp), dimension(5,5)                         :: stresses_jacobian
    real(dp), dimension(5,5)                         :: pressure_jacobian

    continue

   xmr             = xmach / re

    if ( side == 'rhs' ) then


      stresses = residual_porous_tauij ( grid, soln%q_dof, soln%amut,       &
                                     ib, face_index, face_corner,           &
                                     xnorm, ynorm, znorm, area,             &
                                     nodes_per_face, xmr,                   &
                                     soln%eqn_set, unorm_bc, sink           &
                                     )

      pressure = residual_porous_pressure ( grid, soln%q_dof,               &
                                     ib, face_index, face_corner,           &
                                     nodes_per_face,                        &
                                     max_node_per_cell,                     &
                                     xnorm, ynorm, znorm, area,             &
                                     soln%eqn_set )

      stresses = residual_viscous_weak_tauij ( grid, soln%q_dof, soln%amut, &
                                     ib, face_index, nodes_per_face,        &
                                     xmr, xnorm, ynorm, znorm, area,        &
                                     soln%eqn_set, unorm_bc                 &
                                     )

!     pressure = residual_viscous_weak_pressure ( grid, soln%q_dof,         &
!                                    ib, face_index, face_corner,           &
!                                    nodes_per_face,                        &
!                                    max_node_per_cell,                     &
!                                    xnorm, ynorm, znorm, area,             &
!                                    soln%eqn_set )

      flux(1:5,1) = stresses(1:5) - pressure(1:5)


    else if ( side == 'lhs' ) then

      stresses_jacobian = jacobian_viscous_weak_tauij (                      &
                                   grid, soln%q_dof, soln%amut,              &
                                   ib , face_index, face_corner,             &
                                   xnorm, ynorm, znorm, area,                &
                                   nodes_per_face, xmr,                      &
                                   model_strain_form_int, soln%eqn_set,      &
                                   unorm_bc )

      pressure_jacobian = jacobian_viscous_weak_pressure (                   &
                          xnorm, ynorm, znorm, area )

      flux        = stresses_jacobian - pressure_jacobian

    endif

  end function viscous_porous_flux


include 'get_sij.f90'
include 'dviscosity_law.f90'

end module wall_model_main

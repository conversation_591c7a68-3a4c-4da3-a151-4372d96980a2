module linear_spectral

  use kinddefs,          only : dqp, dp
  use fun3d_maximums,    only : relaxation_steps
  use complex_functions, only : o
  use debug_defs,        only : debug_lm

  implicit none

  private

  public :: set_field_points
  public :: monitor_rms_sr, block_rms
  public :: check_preconditioner, set_preconditioner_target
  public :: preconditioner_exit
  public :: set_initialized, set_pc, initialized_pc

  integer, save :: field_points = 0

  logical,   dimension(relaxation_steps), save :: initialized_pc = .false.
  real(dqp), dimension(relaxation_steps), save :: scalar_rms_global1
  real(dqp), dimension(relaxation_steps), save :: scalar_rms_global2
  integer,   dimension(relaxation_steps), save :: tolerance_reached
  logical,   dimension(relaxation_steps), save :: minimum_reached

  integer, save :: preconditioner_sweep
  integer, save :: global_dof_sweep, global_dof

  integer, save :: preconditioner_exit  = 0  !early exit flag

  integer,       save :: step_type_pc  = 0
  character(22), save :: solve_type_pc = ''

  integer,  save :: subiterations_min
  real(dp), save :: target_minimum
  real(dp), save :: target_maximum

contains

!============================ SET_INITIALIZED ================================80
!
! Set flag to indicate residual norm for spectral radius not initialized.
!
!=============================================================================80

  subroutine set_initialized(qset, dofg )

    use cfl_defs, only : preconditioner_target_m,                              &
                         preconditioner_target_t,                              &
                         preconditioner_target_m_max,                          &
                         preconditioner_target_t_max,                          &
                         subiterations_min_m, subiterations_min_t

    integer, intent(in) :: qset, dofg

  continue

    preconditioner_exit   = 0
    initialized_pc(:)     = .false.
    tolerance_reached(:)  = 0
    preconditioner_sweep  = 0
    global_dof_sweep      = 0
    minimum_reached(:)    = .false.
    scalar_rms_global2(:) = 0._dp

    if ( qset == 1 ) then
      target_minimum    = preconditioner_target_m
      target_maximum    = preconditioner_target_m_max
      subiterations_min = subiterations_min_m

    elseif ( qset == 2 ) then
      target_minimum    = preconditioner_target_t
      target_maximum    = preconditioner_target_t_max
      subiterations_min = subiterations_min_t
    endif

    global_dof = dofg

  end subroutine set_initialized

!============================ SET_PC =========================================80
!
! Set step_type and solve_type of preconditioner.
!
!=============================================================================80

  subroutine set_pc( step_type, solve_type )

    use lmpi, only : lmpi_master, lmpi_conditional_stop

    integer,       intent(in) :: step_type
    character(22), intent(in) :: solve_type

    integer :: ierr

  continue

    step_type_pc  = step_type
    solve_type_pc = solve_type

    ! Check that arrays containing information for evaluating
    ! convergence of linear system are sufficient
    ! (note that step_type of line2 relaxation is 10).

    ierr = 0
    if ( step_type_pc > relaxation_steps ) then
      if ( lmpi_master ) write(*,*) ' step_type_pc=',step_type_pc, &
                                    ' relaxation_steps=',relaxation_steps
      ierr = 1
    endif

    call lmpi_conditional_stop(ierr,' insufficient dimension:relaxation_steps')

  end subroutine set_pc

!============================ MONITOR_RMS_SR =================================80
!
! Print norm of linear system residual.
!
!=============================================================================80

  subroutine monitor_rms_sr(n_eqns, nb, sum_sq, region)

    use lmpi,            only : lmpi_reduce, lmpi_bcast, lmpi_master

    integer, intent(in) :: n_eqns, nb

    real(dqp), dimension(nb), intent(in) :: sum_sq

    character(len=*), intent(in) :: region

    integer :: eq, total_n_eqns

    real(dqp), dimension(nb) :: rms

! beginNeverComplex
    real(dqp):: ratio
! endNeverComplex

  continue

    call lmpi_reduce(sum_sq,rms)
    call lmpi_bcast(rms)

    call lmpi_reduce(n_eqns,total_n_eqns)
    call lmpi_bcast(total_n_eqns)

    ratio = real(total_n_eqns,dqp) / real(field_points,dqp)

    rms(:) = sqrt(rms(:)/real(total_n_eqns,dqp))

    if(.not.lmpi_master) return

    if ( nb <= 5 ) then
      write(*,'(1x,f5.2,1x,a22,a10,1x,5(e17.10,2x))') ratio,                   &
            solve_type_pc,region,(rms(eq),eq=1,nb)
    elseif ( nb <= 7 ) then
      write(*,'(1x,f5.2,1x,a22,a10,1x,5(e12.4),2x,2(e12.4))') ratio,           &
            solve_type_pc,region,(rms(eq),eq=1,nb)
    else
      write(*,'(1x,f5.2,1x,a22,a10,1x,5(e17.10,2x),/,17x,5(e17.10,2x))') ratio,&
            solve_type_pc,region,(rms(eq),eq=1,nb)
    endif

  end subroutine monitor_rms_sr

!============================ SET_FIELD_POINTS ===============================80
!
! Set the global number of solved points.
!
!=============================================================================80

  subroutine set_field_points(dof0)

    use lmpi,     only : lmpi_reduce, lmpi_bcast

    integer, intent(in) :: dof0

  continue

    call lmpi_reduce(dof0,field_points)
    call lmpi_bcast(field_points)

  end subroutine set_field_points

!============================ CHECK_PRECONDITIONER ===========================80
!
! Check convergence of preconditioner.
! Note: the residual correspond to the residual before the last update sweep,
! i.e., if there are n colors, the residual corresponds to the n-1 color.
!
!=============================================================================80

  subroutine check_preconditioner(n_eqns, nb, sum_res, region)

    use lmpi,     only : lmpi_reduce, lmpi_bcast, lmpi_master
    use cfl_defs, only : preconditioner_cr_to_disallow

    integer,                  intent(in) :: n_eqns, nb
    real(dqp), dimension(nb), intent(in) :: sum_res
    character(len=*),         intent(in) :: region

    integer :: total_n_eqns

    real(dqp) :: divergence_threshold
    real(dqp) :: scalar_target

    real(dqp), dimension(nb) :: rms_dq
    real(dp),  dimension(nb) :: rms

    real(dp) :: scalar_rms, cr1, cr2, global1, global2

    character(80)       :: fmt1

    logical :: tolerance_exceeded

    logical, parameter :: check_second_target = .false.

  continue

    preconditioner_sweep = preconditioner_sweep + 1

    tolerance_exceeded   = .false.

    ! Allow more divergence in the dynamic evaluation of the residual
    ! than in the final static evaluation...legacy value was 100.

    divergence_threshold = preconditioner_cr_to_disallow*20._dp !100._dp

    call lmpi_reduce(sum_res,rms_dq)

    call lmpi_reduce(n_eqns,total_n_eqns)
    call lmpi_bcast(total_n_eqns)

    ! Only count relaxations that encompass entire domain.

    if ( total_n_eqns >= global_dof ) global_dof_sweep = &
                                      global_dof_sweep + 1

    if ( lmpi_master ) then ! Process on master processor.

      rms(:) = sqrt(rms_dq(:)/real(total_n_eqns,dqp))

      global1 = scalar_rms_global1(step_type_pc)
      global2 = scalar_rms_global2(step_type_pc)

      scalar_rms = block_rms( nb, rms )

      cr1 = scalar_rms/global1
      cr2 = 1._dp
      if ( minimum_reached(step_type_pc) ) then
        cr2 = scalar_rms/global2
      endif

      if ( debug_lm ) then
        fmt1 = "(1x,2a,i1,a,f12.4,a,2i6,a,e12.2,1x,3a)"
        if ( real(cr1,dp) > 9999.99_dp ) &
        fmt1 = "(1x,2a,i1,a,e12.4,a,2i6,a,e12.2,1x,3a)"
        write(*,fmt1)                                      &
        ' PRECONDITIONER-STATUS',                          &
        ' nb=',nb,' cr:rms=',o(cr1),                       &
        ' preconditioner_sweep=',preconditioner_sweep,     &
                                    global_dof_sweep,      &
        ' rms=',o(scalar_rms),                             &
        trim(solve_type_pc),':',trim(region)
      endif
      if ( debug_lm .and. minimum_reached(step_type_pc) ) then
        fmt1 = "(1x,a,5x,a,f15.7,a,2i6,a,e12.2,1x,3a)"
        if ( real(cr2,dp) > 9999.99_dp ) &
        fmt1 = "(1x,a,5x,a,e15.7,a,2i6,a,e12.2,1x,3a)"
        write(*,fmt1)                            &
        ' SECONDARY_CONVERGENCE',' cr:rms=',o(cr2)
      endif
      if ( real(scalar_rms,dp) > &
           real(divergence_threshold*global1,dp) ) then
        fmt1 = "(1x,2a,i1,a,f10.2,a,2i6,a,e12.2,1x,3a)"
        if ( real(cr1,dp) > 9999.99_dp ) &
        fmt1 = "(1x,2a,i1,a,e10.2,a,2i6,a,e12.2,1x,3a)"
        write(*,fmt1)                                      &
        ' PRECONDITIONER-DIVERGENCE',                      &
        ' nb=',nb,' cr:rms=',o(cr1),                       &
        ' preconditioner_sweep=',preconditioner_sweep,     &
                                     global_dof_sweep,     &
        ' rms=',o(scalar_rms),                             &
        trim(solve_type_pc),':',trim(region)
      endif

      ! Check for divergence for any sweep.

      if ( real(scalar_rms,dp) >                   &
           real(divergence_threshold*global1,dp) ) &
           tolerance_exceeded = .true.

      if ( global_dof_sweep >= subiterations_min ) then

        ! Check convergence if relaxations span entire domain
        ! reach a minimum number (subiterations_min).

        ! Check convergence against first target.

        scalar_target = target_minimum*global1
        if ( real(scalar_rms,   dp) <=  real(scalar_target,dp) ) then
          if ( total_n_eqns >= global_dof ) then
            tolerance_reached(:) = 0            !reached : global relaxation.
          else
            tolerance_reached(step_type_pc) = 0 !reached : local relaxation.
          endif
        else
          tolerance_reached(step_type_pc) = 1   !not reached : local relaxation.
        endif

        if ( check_second_target ) then

          ! Check convergence against second target.

          scalar_target = target_minimum*global2
          if ( real(scalar_rms,   dp) <=  real(scalar_target,dp) ) then
            if ( total_n_eqns >= global_dof ) then
              tolerance_reached(:) = 0            !reached:global relaxation.
            else
              tolerance_reached(step_type_pc) = 0 !reached:local relaxation.
            endif
          else
            tolerance_reached(step_type_pc) = 1   !not reached:local relaxation.
          endif

        endif

      else

        ! Check maximum convergence at any point.

        scalar_target = target_maximum*global1
        if ( real(scalar_rms,   dp) <=  real(scalar_target,dp) ) then
          if ( total_n_eqns >= global_dof ) then
            tolerance_reached(:) = 0            !reached : global relaxation.
          else
            tolerance_reached(step_type_pc) = 0 !reached : local relaxation.
          endif
        else
          tolerance_reached(step_type_pc) = 1   !locally not reached.
        endif

      endif

      ! Exit if any divergent or all reached tolerance.

      if ( tolerance_exceeded ) then
        preconditioner_exit = 2                     !failure - early exit
      elseif ( sum(tolerance_reached) == 0 ) then
        preconditioner_exit = 1                     !success - early exit
      endif

    endif

    call lmpi_bcast(preconditioner_exit)

    ! Second preconditioner target to accomodate checking for
    ! large amplifications in residual that many times occur
    ! at outset, especially for red-black iterations with
    ! smooth algebraic errors.

    if ( preconditioner_sweep >= subiterations_min .and. &
             global_dof_sweep >= subiterations_min .and. &
         .not.minimum_reached(step_type_pc)              ) then
      call set_preconditioner_target( n_eqns, nb, sum_res, -1 )
    endif

  end subroutine check_preconditioner

!============================ SET_PRECONDITIONER_TARGET=======================80
!
! Set target for preconditioner.
!
!=============================================================================80

  subroutine set_preconditioner_target( n_eqns, nb, sum_res, which_global )

    use lmpi,     only : lmpi_reduce, lmpi_master, lmpi_conditional_stop

    integer,                  intent(in) :: n_eqns, nb, which_global
    real(dqp), dimension(nb), intent(in) :: sum_res

    integer :: total_n_eqns, ierr

    real(dqp), dimension(nb) :: rms_dq
    real(dp),  dimension(nb) :: rms

    real(dp) :: global

    real(dp), parameter :: one = 1._dp

    character(80)       :: fmt1

  continue

    rms_dq(:) = 0._dp
    call lmpi_reduce(sum_res,rms_dq)

    rms(:) = rms_dq(:)

    call lmpi_reduce(n_eqns,total_n_eqns)

    ierr = 0
    if ( lmpi_master .and. total_n_eqns <= 0 ) then

      ierr = 1
      write(*,"(1x,2a,i0,2a)") 'Preconditioner invoked with zero DOF!',&
      ' preconditioner_sweep=',preconditioner_sweep,                   &
      ' solve_type_pc=',trim(solve_type_pc)

    elseif ( lmpi_master ) then ! Process on master processor.

      rms(:) = sqrt(rms(:)/real(total_n_eqns,dqp))

      global = block_rms( nb, rms )

      if ( which_global == 0 ) then
        scalar_rms_global1(step_type_pc) = global
      else
        scalar_rms_global2(step_type_pc) = global
      endif

      if ( debug_lm ) then
          fmt1 = "(1x,2a,i1,a,f12.4,a,i6,6x,a,e12.2,1x,3a)"
          write(*,fmt1)                                      &
          ' PRECONDITIONER-TARGET',                          &
          ' nb=',nb,' cr:rms=',o(one),                       &
          ' preconditioner_sweep=',which_global,             &
          ' rms=',o(global),                                 &
          trim(solve_type_pc)
        endif

    endif

    call lmpi_conditional_stop(ierr,'DOF=0:set_preconditioner_target')

    if ( which_global == 0 ) then
      tolerance_reached( step_type_pc) = 1   !tolerance not reached
    else
      minimum_reached(   step_type_pc ) = .true.
    endif

  end subroutine set_preconditioner_target

!============================= BLOCK_RMS =====================================80
!
! Block norm of rms array.
!
!=============================================================================80
  pure function block_rms( nb, rms_array )

    integer,                  intent(in)  :: nb
    real(dp), dimension(:),   intent(in)  :: rms_array
    real(dp)                              :: block_rms

    integer :: j

  continue

    block_rms = real(rms_array(1),dp)**2
    do j=2,nb
      block_rms = block_rms + real(rms_array(j),dp)**2
    enddo
    block_rms = sqrt( block_rms / real(nb,dp) )

  end function block_rms

end module linear_spectral

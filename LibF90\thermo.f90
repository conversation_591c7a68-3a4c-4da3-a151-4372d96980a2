module thermo

  use kinddefs,        only : dp
  use lmpi,            only : lmpi_die, lmpi_master

  implicit none

  private

  public :: etop, ptoe
  public :: dprimitive_dconserved
  public :: dconserved_dprimitive
  public :: rescale_qnode

  integer, public, parameter :: conserved_q_type = 0
  integer, public, parameter :: primitive_q_type = 1

! Denotes current type of variable in q
  integer, public :: q_type = conserved_q_type

contains

!================================ ETOP =======================================80
!
!  Converts total energy (q_type = 0) to pressure (q_type = 1)
!  Also converts rhou and rhov to u and v
!
!=============================================================================80

  subroutine etop(maxnodes, qnode, n_tot, eqn_set, ignore )

    use fluid,                only : gm1
    use fun3d_constants,      only : my_half, my_1
    use generic_gas_map,      only : n_density, n_etot, n_momx,   &
                                     n_pressure_k
    use solution_types,       only : compressible, generic_gas

    integer, intent(in) :: maxnodes, n_tot, eqn_set

    real(dp), dimension(n_tot,maxnodes), intent(inout) :: qnode

    logical, intent(in), optional :: ignore

    integer :: i, ndim

    real(dp) :: rho,u,v,w,q2,et,p,rhoinv

  continue

    if ( .not.present(ignore) ) then
      if(q_type /= conserved_q_type) then
        if(lmpi_master) write(*,*) ' Error in etop...q_type=',q_type
        call lmpi_die
      endif
      q_type = primitive_q_type
    endif

    choice_eqn_set : select case(eqn_set)
    case (compressible) choice_eqn_set
      do i = 1,maxnodes
        rho = qnode(1,i)
        rhoinv = my_1/rho
        u   = qnode(2,i)*rhoinv
        v   = qnode(3,i)*rhoinv
        w   = qnode(4,i)*rhoinv
        q2  = u*u + v*v + w*w
        et  = qnode(5,i)
        p   = gm1*(et - my_half*rho*q2)

        qnode(2,i) = u
        qnode(3,i) = v
        qnode(4,i) = w
        qnode(5,i) = p
      end do
    case (generic_gas) choice_eqn_set
      ndim = n_density - 1
      do i = 1,maxnodes
        p   = qnode(n_pressure_k(1),i)
        et  = qnode(n_etot,i)
        rhoinv = my_1/qnode(n_density,i)
        qnode(n_momx:ndim,i) = qnode(n_momx:ndim,i)*rhoinv
        qnode(n_etot,i) = p
        qnode(n_pressure_k(1),i) = et
      end do
    case default
      write(*,*) 'eqn_set selection is not a valid choice, eqn_set= ',eqn_set
      write(*,*) 'stopping...'
      call lmpi_die
    end select choice_eqn_set

  end subroutine etop


!================================ PTOE =======================================80
!
!  Converts pressure (q_type=1) to total energy (q_type=0)
!  Also converts u and v back to rhou and rhov
!
!=============================================================================80

  subroutine ptoe(maxnodes, qnode, n_tot, eqn_set, ignore)

    use fluid,           only : xgm1
    use fun3d_constants, only : my_half
    use generic_gas_map, only : n_density, n_momx, n_etot, n_pressure_k
    use solution_types,  only : compressible, generic_gas

    integer, intent(in) :: maxnodes, n_tot, eqn_set

    real(dp), dimension(n_tot,maxnodes), intent(inout) :: qnode

    logical, intent(in), optional :: ignore

    integer :: i, ndim

    real(dp) :: rho,u,v,w,q2,p,et

  continue

    if ( .not.present(ignore) ) then
      if(q_type /= primitive_q_type) then
        if(lmpi_master) write(*,*) ' Error in ptoe...q_type=',q_type
        call lmpi_die
      endif
      q_type = conserved_q_type
    endif

    choice_eqn_set : select case(eqn_set)
    case (compressible) choice_eqn_set
      do i = 1,maxnodes
        rho = qnode(1,i)
        u   = qnode(2,i)
        v   = qnode(3,i)
        w   = qnode(4,i)
        q2  = u*u + v*v + w*w
        p   = qnode(5,i)

        qnode(2,i) = rho*u
        qnode(3,i) = rho*v
        qnode(4,i) = rho*w
        qnode(5,i) = p*xgm1 + my_half*rho*q2
      end do
    case (generic_gas) choice_eqn_set
      ndim = n_density - 1
      do i = 1,maxnodes
        et  = qnode(n_pressure_k(1),i)
        p   = qnode(n_etot,i)
        rho = qnode(n_density,i)
        qnode(n_momx:ndim,i) = qnode(n_momx:ndim,i)*rho
        qnode(n_etot,i) = et
        qnode(n_pressure_k(1),i) = p
      end do
    case default
      write(*,*) 'eqn_set selection is not a valid choice, eqn_set= ',eqn_set
      write(*,*) 'stopping...'
      call lmpi_die
    end select choice_eqn_set

  end subroutine ptoe

!================================ DPRIMITIVE_DCONSERVED ======================80
!
! Jacobian matrix dq/dQ ; state expected in primitive variables.
!
!=============================================================================80
  subroutine dprimitive_dconserved(state,dqdQ)

    use kinddefs,        only : dp
    use fluid,           only : gm1

    real(dp), dimension(5), intent(in) :: state

    real(dp), dimension(5,5), intent(out) :: dqdQ

    real(dp) :: rho, u, v, w

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp
    real(dp), parameter :: my_2 = 2.0_dp

    continue

    rho = state(1)
    u   = state(2)
    v   = state(3)
    w   = state(4)
   !p   = state(5)

    dqdQ(1,1) = my_1
    dqdQ(1,2) = my_0
    dqdQ(1,3) = my_0
    dqdQ(1,4) = my_0
    dqdQ(1,5) = my_0

    dqdQ(2,1) = -u/rho
    dqdQ(2,2) = my_1/rho
    dqdQ(2,3) = my_0
    dqdQ(2,4) = my_0
    dqdQ(2,5) = my_0

    dqdQ(3,1) = -v/rho
    dqdQ(3,2) = my_0
    dqdQ(3,3) = my_1/rho
    dqdQ(3,4) = my_0
    dqdQ(3,5) = my_0

    dqdQ(4,1) = -w/rho
    dqdQ(4,2) = my_0
    dqdQ(4,3) = my_0
    dqdQ(4,4) = my_1/rho
    dqdQ(4,5) = my_0

    dqdQ(5,1) = gm1/my_2*(u*u + v*v + w*w)
    dqdQ(5,2) = -gm1*u
    dqdQ(5,3) = -gm1*v
    dqdQ(5,4) = -gm1*w
    dqdQ(5,5) = gm1

  end subroutine dprimitive_dconserved

!================================ DCONSERVED_DPRIMITIVE ======================80
!
!  Jacobian matrix dQ/dq ; state expected in primitive variables.
!
!=============================================================================80
  subroutine dconserved_dprimitive(state,dQdq)

    use kinddefs,        only : dp
    use fluid,           only : gm1

    real(dp), dimension(5), intent(in) :: state

    real(dp), dimension(5,5), intent(out) :: dQdq

    real(dp) :: rho, u, v, w

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp
    real(dp), parameter :: half = 0.5_dp

    continue

    rho = state(1)
    u   = state(2)
    v   = state(3)
    w   = state(4)
   !p   = state(5)

    dQdq(1,1) = my_1
    dQdq(1,2) = my_0
    dQdq(1,3) = my_0
    dQdq(1,4) = my_0
    dQdq(1,5) = my_0

    dQdq(2,1) = u
    dQdq(2,2) = rho
    dQdq(2,3) = my_0
    dQdq(2,4) = my_0
    dQdq(2,5) = my_0

    dQdq(3,1) = v
    dQdq(3,2) = my_0
    dQdq(3,3) = rho
    dQdq(3,4) = my_0
    dQdq(3,5) = my_0

    dQdq(4,1) = w
    dQdq(4,2) = my_0
    dQdq(4,3) = my_0
    dQdq(4,4) = rho
    dQdq(4,5) = my_0

    dQdq(5,1) = half*(u*u + v*v + w*w)
    dQdq(5,2) = rho*u
    dQdq(5,3) = rho*v
    dQdq(5,4) = rho*w
    dQdq(5,5) = my_1/gm1

  end subroutine dconserved_dprimitive

!================================ RESCALE_QNODE ==============================80
!
!  Rescales compressible, perfect gas solution to allow better restarts when
!  the mach number is changed at restart: scale u, v, w by xmach/xmach_old,
!  with pressure coefficient held fixed (same as the way CFL3D does it)
!
!=============================================================================80

  subroutine rescale_qnode(xmach, xmach_old, neq0, qdim1, qdim2, qnode)

    use lmpi_app,              only : lmpi_xfer
    use lmpi,                  only : lmpi_master, lmpi_die
    use fluid,                 only : gamma
    use ivals,                 only : p0
    use code_status,           only : code_id, flow_code_id

    integer,                             intent(in) :: neq0, qdim1, qdim2

    real(dp),                            intent(in)    :: xmach, xmach_old
    real(dp), dimension(qdim1,qdim2),    intent(inout) :: qnode

    integer                                            :: node

    real(dp)                                           :: factor

  continue

!   only do this for the flow solver

    if (code_id /= flow_code_id) return

    if (lmpi_master) then
      write(*,*)
      write(*,*) 'Mach number changed at restart...rescaling solution to match'
      write(*,*) '  Old Mach, New Mach:', xmach_old, xmach
      write(*,*)
    end if

!   make sure old Mach number is not zero

    if (xmach_old == 0.0_dp) then
      write (*,*) 'error in rescale_qnode: xmach_old = 0 !!!'
      call lmpi_die
    end if

    factor = xmach/xmach_old

!   convert from conserved to primitive

    call etop(qdim2, qnode, qdim1, 0)

    do node = 1,neq0

!     scale velocities

      qnode(2,node) = qnode(2,node)*factor
      qnode(3,node) = qnode(3,node)*factor
      qnode(4,node) = qnode(4,node)*factor

!     cp = constant

      qnode(5,node) = qnode(5,node)*gamma
      qnode(5,node) = (qnode(5,node) - 1.0_dp)*factor*factor
      qnode(5,node) = (qnode(5,node) + 1.0_dp)/gamma

!     insure pressure does not go negative due to rescaling
!     use same cutoff as supersonic_floors option

      if ( qnode(5,node) <= 0.01*p0 ) then
        qnode(5,node) = 0.01*p0
      end if

    end do

!   convert back to conserved variables and tranfer rind data

    call ptoe(qdim2, qnode, qdim1, 0)

    call lmpi_xfer(qnode)

  end subroutine rescale_qnode

end module thermo

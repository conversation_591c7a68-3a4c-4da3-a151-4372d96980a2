!============================= RECONSTRUCT_WEIGHT ============================80
!
! Length weighting in reconstruction routines
!
!=============================================================================80

  pure function reconstruct_weight( dx, dy, dz, weight_factor, &
                                    wls_inv_dist_scale )

    real(dp), intent(in) :: dx, dy, dz, weight_factor, wls_inv_dist_scale
    real(dp)             :: reconstruct_weight
    real(dp)             :: dist

    real(dp), parameter  :: my_1 = 1.0_dp

    continue

    if ( wls_inv_dist_scale < epsilon( 1.0_dp ) ) then
      dist = my_1
    else
      dist = sqrt(dx*dx + dy*dy + dz*dz)**wls_inv_dist_scale
    endif
    reconstruct_weight = weight_factor/dist + (my_1 - weight_factor)

  end function reconstruct_weight

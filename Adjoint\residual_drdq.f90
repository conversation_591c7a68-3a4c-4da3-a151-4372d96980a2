module residual_drdq

  implicit none

  private

  public :: adjoint_drdq, store_aa, write_drdqt

  interface adjoint_drdq
    module procedure adjoint_drdq_sadj
    module procedure adjoint_drdq_args
  end interface

contains

!=============================== ADJOINT_DRDQ ================================80
!
! Adds the (dR/dQ)^T * Lambda piece of the adjoint residual to sadj%res
! Stub routine
!
!=============================================================================80

  subroutine adjoint_drdq_sadj(grid,soln,sadj,crow,crowf,design)

    use grid_types,     only : grid_type
    use solution_adj,   only : sadj_type
    use comprow_types,  only : crow_type, crow_flow
    use design_types,   only : design_type
    use solution_types, only : soln_type

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(crow_type),   intent(in)    :: crow
    type(crow_flow),   intent(in)    :: crowf
    type(design_type), intent(in)    :: design

  continue

    call adjoint_drdq(grid,soln,sadj,crow,crowf,design,sadj%coltag,sadj%rlam)

  end subroutine adjoint_drdq_sadj

!=============================== ADJOINT_DRDQ ================================80
!
! Adds the (dR/dQ)^T * Lambda piece of the adjoint residual to sadj%res
!
!=============================================================================80

  subroutine adjoint_drdq_args(grid,soln,sadj,crow,crowf,design,coltag,rlam)

    use grid_types,                only : grid_type
    use solution_adj,              only : sadj_type
    use comprow_types,             only : crow_type, crow_flow
    use design_types,              only : design_type
    use inviscid_flux,             only : flux_construction
    use info_depr,                 only : ivisc
    use thermo,                    only : etop, ptoe
    use lmpi,                      only : lmpi_die, lmpi_master,               &
                                          lmpi_conditional_stop
    use lmpi_app,                  only : lmpi_xfer, lmpi_sumnode
    use kinddefs,                  only : dp
    use reconstruction,            only : lstgs
    use turb_util,                 only : turbgrad
    use adjoint_switches,          only : always_recompute, store_full_stencil,&
                                          debug_linearizations
    use residual_bc,               only : atlam_bc
    use residual_bc_visc,          only : dvisrhs_bc_mix6, dvisrhs_bc_mix5
    use residual_bci,              only : atlam_bci
    use residual_inviscid,         only : atlam_roe, atlam_vl, atlam_all,      &
                                          atlam_roei
    use residual_laminar,          only : dvisrhs_mix6
    use residual_laminari,         only : dvisrhs_mix5
    use residual_turbparti,        only : turbparti
    use residual_turb_consol,      only : turb_consol_adjoint
    use gmres_matvec,              only : matvecaa_4, matvecaa_5, matvecaa_6
    use solution_types,            only : soln_type,                           &
                                          compressible, incompressible,        &
                                          generic_gas
    use cut_types,                 only : cut_cell_activated
    use residual_cut,              only : cut_cell_atlam, ldfss_atlam
    use residual_bc_element_based, only : atlam_bc_element_based
    use flux_symmetry,             only : symmetry_rhs_adj
    use residual_turbpart,         only : turbpart
    use gradient_driver,           only : update_grads_and_lim

    type(grid_type),                            intent(in)    :: grid
    type(soln_type),                            intent(inout) :: soln
    type(sadj_type),                            intent(inout) :: sadj
    type(crow_type),                            intent(in)    :: crow
    type(crow_flow),                            intent(in)    :: crowf
    type(design_type),                          intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01), intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),           &
                                                intent(in)   :: rlam
    integer :: i

    real(dp), dimension(5,5) :: ad ! Pointwise matrix of interest for debugging

  continue

    ad = 0.0_dp

    explicit_matrix : if ( store_full_stencil ) then

      select case (soln%adim)
      case (6)
        call matvecaa_6(grid%nnodes01,crow%nnz01,crow%ia,crow%ja,sadj%aa,rlam, &
                        sadj%res,soln%adim,design%nfunctions,grid%nnodes01,    &
                        .true.)
      case (5)
        call matvecaa_5(grid%nnodes01,crow%nnz01,crow%ia,crow%ja,sadj%aa,rlam, &
                        sadj%res,soln%adim,design%nfunctions,grid%nnodes01,    &
                        .true.)
      case (4)
        call matvecaa_4(grid%nnodes01,crow%nnz01,crow%ia,crow%ja,sadj%aa,rlam, &
                        sadj%res,soln%adim,design%nfunctions,grid%nnodes01,    &
                        .true.)
      case default
        write(*,*)'ERROR: adim= ',soln%adim,' not known in adjoint_drdq'
        call lmpi_die
      end select

    else explicit_matrix

! update the gradients and limiters related to the inviscid reconstruction

      call update_grads_and_lim(grid,soln)

      call atlam_bc_element_based( grid, soln, sadj, design, coltag, rlam )

      if ( compressible == soln%eqn_set) then
        call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      end if

      !CHECK
      if ( generic_gas == soln%eqn_set) then
        call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      end if

      cut_cell_interior_and_bc : if( cut_cell_activated ) then
        call cut_cell_atlam( grid, soln, sadj, design, coltag, rlam )
      else

        if ( debug_linearizations ) then
          do i = 1, 5
            write(*,'(a,5(e22.15,1x))') 'Before inv interior flux = ',ad(i,1:5)
          end do
        endif
        inviscid_flux_construction : select case (flux_construction)
        case ('roe')
          roe_eqn_set : select case (soln%eqn_set)
          case (compressible)
            call atlam_roe(                                                    &
              grid%nnodes0, grid%nnodes01,                                     &
              grid%x,       grid%y,        grid%z,        grid%symmetry,       &
              grid%r11,     grid%r22,      grid%r33,                           &
              grid%r12,     grid%r13,      grid%r23,                           &
              soln%q_dof,   soln%gradx,    soln%grady,    soln%gradz,          &
              grid%nedge,   grid%nedgeloc, grid%eptr,                          &
              grid%ra,      grid%xn,       grid%yn,       grid%zn,             &
              crow%ia,      crow%ja,       soln%phi,                           &
              soln%ndim,    soln%adim,     soln%n_grd,                         &
              grid%facespeed, nfunctions=design%nfunctions, ad=ad,             &
              res=sadj%res, rlam=rlam, coltag=coltag )
          case (incompressible)
            call atlam_roei(                                                   &
              grid%nnodes0, grid%nnodes01,                                     &
              grid%x,       grid%y,        grid%z,        grid%symmetry,       &
              grid%r11,     grid%r22,      grid%r33,                           &
              grid%r12,     grid%r13,      grid%r23,                           &
              soln%q_dof,   soln%gradx,    soln%grady,    soln%gradz,          &
              grid%nedgeloc, grid%eptr,                                        &
              grid%ra,      grid%xn,       grid%yn,       grid%zn,             &
              crow%ia,      crow%ja,       soln%ndim,     soln%adim,           &
              grid%facespeed, nfunctions=design%nfunctions, ad=ad,             &
              res=sadj%res, rlam=rlam, coltag=coltag)
          case (generic_gas) !CHECK
            call atlam_roe(                                                    &
              grid%nnodes0, grid%nnodes01,                                     &
              grid%x,       grid%y,        grid%z,        grid%symmetry,       &
              grid%r11,     grid%r22,      grid%r33,                           &
              grid%r12,     grid%r13,      grid%r23,                           &
              soln%q_dof,   soln%gradx,    soln%grady,    soln%gradz,          &
              grid%nedge,   grid%nedgeloc, grid%eptr,                          &
              grid%ra,      grid%xn,       grid%yn,       grid%zn,             &
              crow%ia,      crow%ja,       soln%phi,                           &
              soln%n_tot,    soln%adim,     soln%n_grd,                        &
              grid%facespeed, nfunctions=design%nfunctions, ad=ad,             &
              res=sadj%res, rlam=rlam, coltag=coltag)
          case default
            call lmpi_conditional_stop(1,'adjoint_drdq: only in/comprss pg')
          end select roe_eqn_set
        case ('vanleer')
          call atlam_vl(                                                       &
            grid%nnodes0, grid%nnodes01,                                       &
            grid%x,       grid%y,        grid%z,        grid%symmetry,         &
            grid%r11,     grid%r22,      grid%r33,                             &
            grid%r12,     grid%r13,      grid%r23,                             &
            soln%q_dof,   soln%gradx,    soln%grady,    soln%gradz,            &
            grid%nedge,   grid%nedgeloc, grid%eptr,                            &
            grid%ra,      grid%xn,       grid%yn,       grid%zn,               &
            crow%ia,      crow%ja,       soln%phi,                             &
            soln%n_tot,   soln%adim,     soln%n_grd,                           &
            nfunctions=design%nfunctions, res=sadj%res, rlam=rlam,coltag=coltag)
        case ('ldfss','dldfss')
          call ldfss_atlam( grid, soln, sadj, design, crow, coltag, rlam )
        case default
          if (lmpi_master) write(*,*) 'stop. inviscid flux not supported',&
            flux_construction
          call lmpi_conditional_stop(1)
          call atlam_all(                                                      &
            grid%nnodes0, grid%nnodes01,                                       &
            grid%x,       grid%y,        grid%z,        grid%symmetry,         &
            grid%r11,     grid%r22,      grid%r33,                             &
            grid%r12,     grid%r13,      grid%r23,                             &
            soln%n_tot,   soln%n_grd,                                          &
            soln%q_dof,soln%gradx,    soln%grady,    soln%gradz,  soln%phi,    &
            soln%adim, design%nfunctions,                                      &
            coltag,       rlam,          sadj%res,                             &
            grid%nedge,   grid%nedgeloc, grid%eptr,                            &
            grid%ra,      grid%xn,       grid%yn,       grid%zn,               &
            grid%facespeed,                                                    &
            crow%ia,      crow%ia_ns,    crow%ja)
        end select inviscid_flux_construction

      end if cut_cell_interior_and_bc

      do i = 1, design%nfunctions
        call lmpi_sumnode(sadj%res(:,:,i)) ! sum the ghost values from off-proc
      end do

      if ( debug_linearizations ) then
        do i = 1, 5
          write(*,'(a,5(e22.15,1x))') 'After inv interior flux = ',ad(i,1:5)
        end do
      endif

      select case (soln%eqn_set)
      case (compressible)
        call atlam_bc(grid%nnodes0, grid%nnodes01, grid%x, grid%y, grid%z,     &
                      soln%q_dof, grid%nbound, grid%bc, grid%nelem, grid%elem, &
                      soln%adim, soln%n_tot, grid%dxdt, grid%dydt, grid%dzdt,  &
                      ad=ad, nfunctions=design%nfunctions, res=sadj%res,       &
                      rlam=rlam, coltag=coltag)
      case (incompressible)
        call atlam_bci(grid%nnodes0, grid%nnodes01, grid%x, grid%y, grid%z,    &
                       soln%q_dof, grid%nbound, grid%bc, grid%nelem, grid%elem,&
                       soln%ndim, soln%adim, grid%dxdt, grid%dydt, grid%dzdt,  &
                       coltag=coltag, rlam=rlam, res=sadj%res,                 &
                       nfunctions=design%nfunctions)
      case (generic_gas) !CHECK
        call atlam_bc(grid%nnodes0, grid%nnodes01, grid%x, grid%y, grid%z,     &
                      soln%q_dof, grid%nbound, grid%bc, grid%nelem, grid%elem, &
                      soln%adim, soln%n_tot, grid%dxdt, grid%dydt, grid%dzdt,  &
                      ad=ad, nfunctions=design%nfunctions, res=sadj%res,       &
                      rlam=rlam, coltag=coltag, eqn_set=soln%eqn_set)

      case default
        call lmpi_conditional_stop(1,'adjoint_drdq: only in/compress pg')
      end select

      if ( debug_linearizations ) then
        do i = 1, 5
          write(*,'(a,5(e22.15,1x))') 'After inv BC flux = ',ad(i,1:5)
        end do
      endif

! Pick up any source term contributions
! These are always recomputed, just like the inviscid terms

      call adjoint_source_terms(grid,soln,crowf,sadj,design,rlam,coltag,ad)

      if ( debug_linearizations ) then
        do i = 1, 5
          write(*,'(a,5(e22.15,1x))') 'After source flux = ',ad(i,1:5)
        end do
      endif

      add_visc_terms : if(ivisc > 0) then

        recompute_viscous_method : if (always_recompute) then

          if ( soln%eqn_set == compressible ) then
            call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
          endif

          how_viscous : select case (ivisc)
          case( 2, 8 ) ! laminar or consolidated 2-equation
            laminar_eqn_set : select case (soln%eqn_set)
            case (compressible)

                call ptoe(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

                laminar_comp_elem : do i = 1, grid%nelem
                    if ( .not. cut_cell_activated )                            &
                    call dvisrhs_mix6(grid%nnodes0, grid%nnodes01,             &
                                      grid%elem(i)%ncell, grid%elem(i)%c2n,    &
                                      grid%x, grid%y, grid%z, soln%q_dof,      &
                                      soln%amut, grid%elem(i)%local_f2n,       &
                                      grid%elem(i)%local_e2n,                  &
                                      grid%elem(i)%chk_norm,                   &
                                      grid%elem(i)%local_f2e,                  &
                                      grid%elem(i)%e2n_2d,                     &
                                      grid%elem(i)%face_per_cell,              &
                                      grid%elem(i)%node_per_cell,              &
                                      grid%elem(i)%edge_per_cell,              &
                                      grid%elem(i)%type_cell, soln%n_tot,      &
                                      soln%adim, grid%elem(i)%face_2d,         &
                                      soln%n_turb, soln%turb,                  &
                                      nfunctions=design%nfunctions, rlam=rlam, &
                                      coltag=coltag, res=sadj%res)
                end do laminar_comp_elem

                if ( debug_linearizations ) then
                  do i = 1, 5
                    write(*,'(a,5(e22.15,1x))')'After laminar flux = ',ad(i,1:5)
                  end do
                endif

                call dvisrhs_bc_mix6(grid%nnodes0, grid%nnodes01, crow%nnz0,   &
                                     grid%nbound, grid%bc, crow%ia, crow%ja,   &
                                     crow%iau, grid%nelem, grid%elem, grid%x,  &
                                     grid%y, grid%z, soln%q_dof, soln%amut,    &
                                     soln%n_tot, soln%adim, design%nfunctions, &
                                     coltag, rlam, soln%n_turb, soln%turb,     &
                                     grid%symmetry, res=sadj%res)

                if ( debug_linearizations ) then
                  do i = 1, 5
                    write(*,'(a,5(e22.15,1x))') 'After laminar BC flux = ',  &
                                                 ad(i,1:5)
                  end do
                endif

                if ( ivisc == 8 ) then
                  call turb_consol_adjoint( grid, soln, sadj, design,&
                    coltag, rlam )
                end if

            case (incompressible)

              if ( .not. cut_cell_activated ) then
                do i = 1, grid%nelem
                  call dvisrhs_mix5(grid%nnodes0, grid%nnodes01,               &
                                    grid%elem(i)%ncell,grid%elem(i)%c2n,grid%x,&
                                    grid%y, grid%z, soln%q_dof, soln%amut,     &
                                    grid%elem(i)%local_f2n,                    &
                                    grid%elem(i)%local_e2n,                    &
                                    grid%elem(i)%chk_norm,                     &
                                    grid%elem(i)%local_f2e,                    &
                                    grid%elem(i)%e2n_2d,                       &
                                    grid%elem(i)%face_per_cell,                &
                                    grid%elem(i)%node_per_cell,                &
                                    grid%elem(i)%edge_per_cell,                &
                                    grid%elem(i)%type_cell, soln%n_tot,        &
                                    soln%adim,grid%elem(i)%face_2d,soln%n_turb,&
                                    soln%turb, nfunctions=design%nfunctions,   &
                                    rlam=rlam, coltag=coltag, res=sadj%res)
                end do

              endif
              call dvisrhs_bc_mix5(grid%nnodes0, grid%nnodes01, crow%nnz0,     &
                                   grid%nbound, grid%bc, crow%ia, crow%ja,     &
                                   crow%iau, grid%nelem, grid%elem, grid%x,    &
                                   grid%y, grid%z, soln%q_dof, soln%amut,      &
                                   soln%n_tot, soln%adim, design%nfunctions,   &
                                   coltag, rlam, soln%n_turb, soln%turb,       &
                                   res=sadj%res)

            case default

              call lmpi_conditional_stop(1, 'adjoint_drdq: only in/compress pg')

            end select laminar_eqn_set

          case( 6 ) ! sa
            sa_eqn_set : select case (soln%eqn_set)
            case (compressible)

              call turbgrad(   soln%eqn_set,                                   &
                grid%nnodes0,  grid%nnodes01,  grid%nedgeloc,  grid%eptr,      &
                soln%q_dof,    grid%x,         grid%y,         grid%z,         &
                grid%dxdt,     grid%dydt,      grid%dzdt,                      &
                grid%xn,       grid%yn,        grid%zn,        grid%ra,        &
                grid%vol,      soln%gradx,     soln%grady,     soln%gradz,     &
                grid%nbound,   grid%bc,        grid%nedgeloc_2d,               &
                grid%node_pairs_2d,            grid%nnodes0_2d,                &
                grid%nelem,   grid%elem, soln%n_tot, soln%n_grd)

              call lmpi_xfer(soln%gradx)
              call lmpi_xfer(soln%grady)
              call lmpi_xfer(soln%gradz)

              call ptoe(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

              do i = 1, grid%nelem
                call dvisrhs_mix6(grid%nnodes0, grid%nnodes01,                 &
                                  grid%elem(i)%ncell, grid%elem(i)%c2n, grid%x,&
                                  grid%y, grid%z, soln%q_dof, soln%amut,       &
                                  grid%elem(i)%local_f2n,                      &
                                  grid%elem(i)%local_e2n,                      &
                                  grid%elem(i)%chk_norm,                       &
                                  grid%elem(i)%local_f2e,                      &
                                  grid%elem(i)%e2n_2d,                         &
                                  grid%elem(i)%face_per_cell,                  &
                                  grid%elem(i)%node_per_cell,                  &
                                  grid%elem(i)%edge_per_cell,                  &
                                  grid%elem(i)%type_cell, soln%n_tot,          &
                                  soln%adim, grid%elem(i)%face_2d, soln%n_turb,&
                                  soln%turb, nfunctions=design%nfunctions,     &
                                  rlam=rlam, coltag=coltag, res=sadj%res)
              end do

              call dvisrhs_bc_mix6(grid%nnodes0, grid%nnodes01, crow%nnz0,     &
                                   grid%nbound, grid%bc, crow%ia, crow%ja,     &
                                   crow%iau, grid%nelem, grid%elem, grid%x,    &
                                   grid%y, grid%z, soln%q_dof, soln%amut,      &
                                   soln%n_tot, soln%adim, design%nfunctions,   &
                                   coltag, rlam, soln%n_turb, soln%turb,       &
                                   grid%symmetry, res=sadj%res)

              call etop(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

!  Now get contribution from turbulence model

              call turbpart(                                                   &
                  grid%nnodes0,grid%nnodes01,grid%x,grid%y,grid%z,             &
                  soln%q_dof,soln%gradx,soln%grady,soln%gradz,soln%turb,       &
                  grid%slen,grid%vol,grid%nedge,                               &
                  grid%nedgeLoc,grid%eptr,grid%ra,grid%xn,grid%yn,grid%zn,     &
                  soln%dft1,soln%dft2,grid%nbound,grid%bc,design%nfunctions,   &
                  soln%ndim,soln%adim,soln%n_turb,grid%facespeed,soln%n_q,     &
                  soln%eqn_set,grid%dxdt,grid%dydt,grid%dzdt,grid%elem,        &
                  sadj%coltag,grid%nelem,soln%n_tot,sadj%rlam,sadj%res,        &
                  grid%symmetry)

              call lstgs(      soln%viscous_method,                            &
                grid%nnodes0,  grid%nnodes01,  grid%nedgeloc, grid%eptr,       &
                grid%symmetry, soln%q_dof,                                     &
                soln%gradx,    soln%grady,     soln%gradz,                     &
                grid%x,        grid%y,         grid%z,                         &
                grid%r11,      grid%r12,       grid%r13,                       &
                grid%r22,      grid%r23,       grid%r33,                       &
                soln%n_tot,    soln%n_grd,     soln%turb,     soln%n_turb,     &
                soln%eqn_set,  soln%ndim)

              call lmpi_xfer(soln%gradx)
              call lmpi_xfer(soln%grady)
              call lmpi_xfer(soln%gradz)

            case (incompressible)

              call turbgrad(   soln%eqn_set,                                   &
                grid%nnodes0,  grid%nnodes01,  grid%nedgeloc,  grid%eptr,      &
                soln%q_dof,    grid%x,         grid%y,         grid%z,         &
                grid%dxdt,     grid%dydt,      grid%dzdt,                      &
                grid%xn,       grid%yn,        grid%zn,        grid%ra,        &
                grid%vol,      soln%gradx,     soln%grady,     soln%gradz,     &
                grid%nbound,   grid%bc,        grid%nedgeloc_2d,               &
                grid%node_pairs_2d,            grid%nnodes0_2d,                &
                grid%nelem,   grid%elem, soln%n_tot, soln%n_grd)

              call lmpi_xfer(soln%gradx)
              call lmpi_xfer(soln%grady)
              call lmpi_xfer(soln%gradz)

              do i = 1, grid%nelem
                call dvisrhs_mix5(grid%nnodes0, grid%nnodes01,                 &
                                  grid%elem(i)%ncell, grid%elem(i)%c2n, grid%x,&
                                  grid%y, grid%z, soln%q_dof, soln%amut,       &
                                  grid%elem(i)%local_f2n,                      &
                                  grid%elem(i)%local_e2n,                      &
                                  grid%elem(i)%chk_norm,                       &
                                  grid%elem(i)%local_f2e,                      &
                                  grid%elem(i)%e2n_2d,                         &
                                  grid%elem(i)%face_per_cell,                  &
                                  grid%elem(i)%node_per_cell,                  &
                                  grid%elem(i)%edge_per_cell,                  &
                                  grid%elem(i)%type_cell, soln%n_tot,          &
                                  soln%adim, grid%elem(i)%face_2d, soln%n_turb,&
                                  soln%turb, nfunctions=design%nfunctions,     &
                                  rlam=rlam, coltag=coltag, res=sadj%res)
              end do

              call dvisrhs_bc_mix5(grid%nnodes0, grid%nnodes01, crow%nnz0,     &
                                   grid%nbound, grid%bc, crow%ia, crow%ja,     &
                                   crow%iau, grid%nelem, grid%elem, grid%x,    &
                                   grid%y, grid%z, soln%q_dof, soln%amut,      &
                                   soln%n_tot, soln%adim, design%nfunctions,   &
                                   coltag, rlam, soln%n_turb, soln%turb,       &
                                   res=sadj%res)

              call turbparti(grid%nnodes0,grid%nnodes01,grid%x,grid%y,grid%z,  &
                             soln%q_dof,soln%gradx,soln%grady,soln%gradz,      &
                             soln%turb,grid%slen,grid%vol,sadj%rlam,           &
                             sadj%coltag,sadj%res,grid%nedge,grid%nedgeloc,    &
                             grid%eptr,grid%ra,grid%xn,grid%yn,grid%zn,        &
                             soln%dft1,soln%dft2,grid%nbound,grid%bc,          &
                             design%nfunctions,soln%ndim,soln%n_turb,soln%adim,&
                             grid%facespeed,soln%eqn_set,soln%n_q,grid%dxdt,   &
                             grid%dydt,grid%dzdt,grid%elem,grid%nelem,         &
                             grid%symmetry)

              call lstgs(      soln%viscous_method,                            &
                grid%nnodes0,  grid%nnodes01,  grid%nedgeloc, grid%eptr,       &
                grid%symmetry, soln%q_dof,                                     &
                soln%gradx,    soln%grady,     soln%gradz,                     &
                grid%x,        grid%y,         grid%z,                         &
                grid%r11,      grid%r12,       grid%r13,                       &
                grid%r22,      grid%r23,       grid%r33,                       &
                soln%n_tot,    soln%n_grd,     soln%turb,     soln%n_turb,     &
                soln%eqn_set,  soln%ndim)

              call lmpi_xfer(soln%gradx)
              call lmpi_xfer(soln%grady)
              call lmpi_xfer(soln%gradz)

            case default
              call lmpi_conditional_stop(1,'adjoint_drdq: only in/compress pg')
            end select sa_eqn_set

            if ( soln%eqn_set == compressible ) then
              call ptoe( grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set )
            endif

          case default
            call lmpi_conditional_stop(1,'adjoint_drdq: ivisc bad, how_viscous')
          end select how_viscous

        else recompute_viscous_method

          select case (soln%adim)
          case (6)
            call matvecaa_6(grid%nnodes01,crow%nnz01,crow%ia,crow%ja,sadj%aa,  &
                            rlam,sadj%res,soln%adim,design%nfunctions,         &
                            grid%nnodes0,.false.)
          case (5)
            call matvecaa_5(grid%nnodes01,crow%nnz01,crow%ia,crow%ja,sadj%aa,  &
                            rlam,sadj%res,soln%adim,design%nfunctions,         &
                            grid%nnodes0,.false.)
          case (4)
            call matvecaa_4(grid%nnodes01,crow%nnz01,crow%ia,crow%ja,sadj%aa,  &
                            rlam,sadj%res,soln%adim,design%nfunctions,         &
                            grid%nnodes0,.false.)
          case default
            write(*,*)'ERROR: adim= ',soln%adim,' not known in adjoint_drdq'
            call lmpi_die
          end select

        end if recompute_viscous_method

      else add_visc_terms

        call symmetry_rhs_adj(grid, sadj)

      endif add_visc_terms

    endif explicit_matrix

    if ( debug_linearizations ) stop ! FIXME: should be lmpi_finalize?

  end subroutine adjoint_drdq_args

!=============================== ADJOINT_SOURCE_TERMS ========================80
!
! Adds any source terms to the adjoint residual (noninertial, rotor, etc)
!
!=============================================================================80

  subroutine adjoint_source_terms(grid,soln,crow,sadj,design,rlam,coltag,ad)

    use nml_noninertial_reference_frame, only : noninertial
    use kinddefs,       only : dp
    use residual_nonin, only : atlam_noninertial
    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use design_types,   only : design_type
    use solution_adj,   only : sadj_type
    use rotors,         only : rotor_source_lhs,  rotor_flag
    use comprow_types,  only : crow_flow

    type(grid_type),                            intent(in)    :: grid
    type(soln_type),                            intent(in)    :: soln
    type(crow_flow),                            intent(in)    :: crow
    type(design_type),                          intent(in)    :: design
    type(sadj_type),                            intent(inout) :: sadj
    real(dp), dimension(soln%adim,grid%nnodes01),intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),           &
                                                intent(in)    :: rlam
    real(dp), dimension(5,5),                   intent(inout) :: ad

  continue

! Noninertial terms

    if ( noninertial ) then
      call atlam_noninertial( grid%nnodes0, grid%nnodes01, grid%vol, soln%adim,&
                              soln%ndim, ad=ad, coltag=coltag, rlam=rlam,      &
                              res=sadj%res, nfunctions=design%nfunctions )
    end if

! Rotor terms

    if ( rotor_flag ) then
      call rotor_source_lhs(soln%eqn_set, grid%nnodes0, grid%nnodes01,         &
                            soln%adim,                                         &
                            design%nfunctions, soln%q_dof, soln%n_tot,         &
                            soln%njac, fill_a_diag=.false.,                    &
                            fill_adjoint_res=.true., g2m=crow%g2m, rlam=rlam,  &
                            coltag=coltag, res=sadj%res)
    end if

  end subroutine adjoint_source_terms


!================================= STORE_AA ==================================80
!
! Store some or all of the jacobians to speed up matrix-vector products
!
!=============================================================================80
  subroutine store_aa(grid, soln, sadj, crow, design)

    use kinddefs,                 only: dp
    use exact_defs,               only: ic_y_symmetry
    use info_depr,                only: ivisc
    use nml_noninertial_reference_frame, only : noninertial
    use lmpi,                     only: lmpi_die
    use lmpi_app,                 only: lmpi_xfer
    use thermo,                   only: etop, ptoe
    use turb_util,                only: turbgrad
    use reconstruction,           only: lstgs
    use gradient_driver,          only: update_grads_and_lim
    use residual_nonin,           only: atlam_noninertial
    use flux_symmetry,            only: enforce_symmetry_bc
    use residual_bc_element_based,only: atlam_bc_element_based
    use grid_types,               only: grid_type
    use solution_types,           only: soln_type, compressible, incompressible
    use solution_adj,             only: sadj_type
    use comprow_types,            only: crow_type
    use adjoint_switches,         only: always_recompute, store_full_stencil
    use bc_names,                 only: bc_strong_viscous_adjoint
    use allocations,              only: my_alloc_ptr
    use residual_inviscid,        only: atlam_roe, atlam_roei, atlam_vl
    use residual_laminar,         only: dvisrhs_mix6
    use residual_laminari,        only: dvisrhs_mix5
    use residual_bc_visc,         only: dvisrhs_bc_mix6, dvisrhs_bc_mix5
    use residual_bc,              only: atlam_bc, enforce_lam_strong_bc,       &
                                        enforce_turb_strong_bc
    use residual_bci,             only: atlam_bci, enforce_lam_strong_bci,     &
                                        enforce_turb_strong_bci
    use residual_turbpart,        only: fillturb
    use residual_turbparti,       only: fillturbi
    use design_types,             only: design_type
    use inviscid_flux,            only: flux_construction

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(crow_type),   intent(in)    :: crow
    type(design_type), intent(in)    :: design

    integer :: i, ib, node, diag, matrix_extent, nnz

    logical, pointer, dimension(:) :: visited

  continue

    matrix_extent = grid%nnodes0
    if ( store_full_stencil ) matrix_extent = grid%nnodes01

! evaluate the gradients and limiter for inviscid reconstruction

    call update_grads_and_lim(grid,soln)

! Construct the matrix

    store_matrix : if ( .not. always_recompute ) then

      sadj%AA = 0.0_dp

      if ( soln%eqn_set == compressible ) then
        call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      end if

      if ( store_full_stencil ) then
        select case(soln%eqn_set)
        case (compressible)
          select case(flux_construction)
          case('roe')
            call atlam_roe(grid%nnodes0, grid%nnodes01, grid%x,                &
                           grid%y, grid%z, grid%symmetry, grid%r11, grid%r22,  &
                           grid%r33, grid%r12, grid%r13, grid%r23, soln%q_dof, &
                           soln%gradx, soln%grady, soln%gradz, grid%nedge,     &
                           grid%nedgeloc, grid%eptr, grid%ra, grid%xn, grid%yn,&
                           grid%zn, crow%ia, crow%ja, soln%phi,                &
                           soln%ndim, soln%adim, soln%n_grd, grid%facespeed,   &
                           fhelp=crow%fhelp, iau=crow%iau, a=sadj%aa)
          case('vanleer')
            call atlam_vl(grid%nnodes0, grid%nnodes01, grid%x,                 &
                          grid%y, grid%z, grid%symmetry, grid%r11, grid%r22,   &
                          grid%r33, grid%r12, grid%r13, grid%r23, soln%q_dof,  &
                          soln%gradx, soln%grady, soln%gradz, grid%nedge,      &
                          grid%nedgeloc, grid%eptr, grid%ra, grid%xn, grid%yn, &
                          grid%zn, crow%ia, crow%ja, soln%phi,                 &
                          soln%ndim, soln%adim, soln%n_grd, fhelp=crow%fhelp,  &
                          iau=crow%iau, a=sadj%aa)
          case default
            write(*,*)'Unknown flux_construction in store_aa:',flux_construction
            call lmpi_die
            stop
          end select
          call atlam_bc(grid%nnodes0, grid%nnodes01, grid%x, grid%y,           &
                        grid%z, soln%q_dof, grid%nbound, grid%bc, grid%nelem,  &
                        grid%elem, soln%adim, soln%n_tot, grid%dxdt, grid%dydt,&
                        grid%dzdt, aa=sadj%aa, ia=crow%ia, ja=crow%ja,         &
                        iau=crow%iau)
        case (incompressible)
          call atlam_roei(grid%nnodes0, grid%nnodes01,             grid%x,     &
                          grid%y, grid%z, grid%symmetry, grid%r11, grid%r22,   &
                          grid%r33, grid%r12, grid%r13, grid%r23, soln%q_dof,  &
                          soln%gradx, soln%grady, soln%gradz,                  &
                          grid%nedgeloc, grid%eptr, grid%ra, grid%xn, grid%yn, &
                          grid%zn, crow%ia, crow%ja, soln%ndim,                &
                          soln%adim, grid%facespeed, fhelp=crow%fhelp,         &
                          iau=crow%iau, a=sadj%aa)
          call atlam_bci(grid%nnodes0, grid%nnodes01, grid%x, grid%y,          &
                        grid%z, soln%q_dof, grid%nbound, grid%bc, grid%nelem,  &
                        grid%elem, soln%ndim, soln%adim, grid%dxdt, grid%dydt, &
                        grid%dzdt, ia=crow%ia, ja=crow%ja, iau=crow%iau,       &
                        aa=sadj%aa)
        end select

        call atlam_bc_element_based(grid, soln, sadj, crow=crow)

        if ( soln%eqn_set == compressible ) then
          call etop( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
        endif

        if ( noninertial ) then
          call atlam_noninertial(grid%nnodes0, grid%nnodes01, grid%vol,        &
                                 soln%adim, soln%ndim, iau=crow%iau, aa=sadj%aa)
        end if
      endif

      precompute_ivisc : select case (ivisc)
      case (0) precompute_ivisc
! Euler terms already done above
        call zero_rows_via_coltag(grid%nnodes01, soln%adim, sadj%coltag,       &
                                  sadj%aa, crow%ia, crow%ja, matrix_extent)
      case (2) precompute_ivisc
        select case (soln%eqn_set)
        case (compressible)

            call ptoe(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

            do i = 1, grid%nelem
                call dvisrhs_mix6(grid%nnodes0, grid%nnodes01,                 &
                                  grid%elem(i)%ncell, grid%elem(i)%c2n, grid%x,&
                                  grid%y, grid%z, soln%q_dof, soln%amut,       &
                                  grid%elem(i)%local_f2n,                      &
                                  grid%elem(i)%local_e2n,                      &
                                  grid%elem(i)%chk_norm,                       &
                                  grid%elem(i)%local_f2e, grid%elem(i)%e2n_2d, &
                                  grid%elem(i)%face_per_cell,                  &
                                  grid%elem(i)%node_per_cell,                  &
                                  grid%elem(i)%edge_per_cell,                  &
                                  grid%elem(i)%type_cell, soln%n_tot,          &
                                  soln%adim, grid%elem(i)%face_2d, soln%n_turb,&
                                  soln%turb, ia=crow%ia,ja=crow%ja,            &
                                  iau=crow%iau,aa=sadj%aa)
            end do
            call dvisrhs_bc_mix6(grid%nnodes0, grid%nnodes01,                  &
                                 crow%nnz0, grid%nbound, grid%bc,              &
                                 crow%ia, crow%ja, crow%iau, grid%nelem,       &
                                 grid%elem, grid%x, grid%y, grid%z, soln%q_dof,&
                                 soln%amut, soln%n_tot, soln%adim,             &
                                 design%nfunctions, sadj%coltag, sadj%rlam,    &
                                 soln%n_turb, soln%turb, grid%symmetry,        &
                                 aa=sadj%aa)

            call etop(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

          call zero_rows_via_coltag(grid%nnodes01, soln%adim, sadj%coltag,     &
                                    sadj%aa, crow%ia, crow%ja, matrix_extent)

          call enforce_lam_strong_bc(grid%nnodes01, grid%nnodes0, crow%nnz0,   &
                                     soln%adim, sadj%aa, crow%iau, grid%nbound,&
                                     grid%bc, grid%symmetry)

        case (incompressible)

          do i = 1, grid%nelem
            call dvisrhs_mix5(grid%nnodes0, grid%nnodes01, grid%elem(i)%ncell, &
                              grid%elem(i)%c2n, grid%x, grid%y, grid%z,        &
                              soln%q_dof, soln%amut, grid%elem(i)%local_f2n,   &
                              grid%elem(i)%local_e2n, grid%elem(i)%chk_norm,   &
                              grid%elem(i)%local_f2e, grid%elem(i)%e2n_2d,     &
                              grid%elem(i)%face_per_cell,                      &
                              grid%elem(i)%node_per_cell,                      &
                              grid%elem(i)%edge_per_cell,                      &
                              grid%elem(i)%type_cell, soln%n_tot, soln%adim,   &
                              grid%elem(i)%face_2d, soln%n_turb, soln%turb,    &
                              ia=crow%ia,ja=crow%ja,iau=crow%iau,aa=sadj%aa)
          end do

          call dvisrhs_bc_mix5(grid%nnodes0, grid%nnodes01,                    &
                               crow%nnz0, grid%nbound, grid%bc,                &
                               crow%ia, crow%ja, crow%iau, grid%nelem,         &
                               grid%elem, grid%x, grid%y, grid%z, soln%q_dof,  &
                               soln%amut, soln%n_tot, soln%adim,               &
                               design%nfunctions, sadj%coltag, sadj%rlam,      &
                               soln%n_turb, soln%turb, aa=sadj%aa)

          call zero_rows_via_coltag(grid%nnodes01, soln%adim, sadj%coltag,     &
                                    sadj%aa, crow%ia, crow%ja, matrix_extent)

          call enforce_lam_strong_bci(grid%nnodes01,grid%nnodes0,crow%nnz0,    &
                                      soln%adim,sadj%aa,crow%iau,grid%nbound,  &
                                      grid%bc )

        case default
          write(*,*)'ERROR: eqn_set=',soln%eqn_set,' not known in fcnadt stop.'
          call lmpi_die
        end select

      case (6) precompute_ivisc


        select case (soln%eqn_set)
        case (compressible)

          call turbgrad(soln%eqn_set,  grid%nnodes0,  grid%nnodes01,           &
                        grid%nedgeloc, grid%eptr,                              &
                        soln%q_dof,    grid%x,       grid%y,      grid%z,      &
                        grid%dxdt,     grid%dydt,    grid%dzdt,                &
                        grid%xn,       grid%yn,      grid%zn,     grid%ra,     &
                        grid%vol,      soln%gradx,   soln%grady,  soln%gradz,  &
                        grid%nbound,   grid%bc,      grid%nedgeloc_2d,         &
                        grid%node_pairs_2d,          grid%nnodes0_2d,          &
                        grid%nelem,    grid%elem, soln%n_tot, soln%n_grd)

          call lmpi_xfer(soln%gradx)
          call lmpi_xfer(soln%grady)
          call lmpi_xfer(soln%gradz)

          call ptoe(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

          do i = 1, grid%nelem
            call dvisrhs_mix6(grid%nnodes0, grid%nnodes01, grid%elem(i)%ncell, &
                              grid%elem(i)%c2n, grid%x, grid%y, grid%z,        &
                              soln%q_dof, soln%amut, grid%elem(i)%local_f2n,   &
                              grid%elem(i)%local_e2n, grid%elem(i)%chk_norm,   &
                              grid%elem(i)%local_f2e, grid%elem(i)%e2n_2d,     &
                              grid%elem(i)%face_per_cell,                      &
                              grid%elem(i)%node_per_cell,                      &
                              grid%elem(i)%edge_per_cell,                      &
                              grid%elem(i)%type_cell, soln%n_tot, soln%adim,   &
                              grid%elem(i)%face_2d, soln%n_turb, soln%turb,    &
                              ia=crow%ia,ja=crow%ja,iau=crow%iau,aa=sadj%aa)
          end do

          call dvisrhs_bc_mix6(grid%nnodes0, grid%nnodes01,                    &
                               crow%nnz0, grid%nbound, grid%bc,                &
                               crow%ia, crow%ja, crow%iau, grid%nelem,         &
                               grid%elem, grid%x, grid%y, grid%z, soln%q_dof,  &
                               soln%amut, soln%n_tot, soln%adim,               &
                               design%nfunctions, sadj%coltag, sadj%rlam,      &
                               soln%n_turb, soln%turb, grid%symmetry,          &
                               aa=sadj%aa)

          call etop(grid%nnodes01,soln%q_dof, soln%n_tot, soln%eqn_set)

          call fillturb(grid%nnodes0,grid%nnodes01,grid%x,grid%y,grid%z,       &
                        grid%vol,soln%q_dof,soln%gradx,soln%grady,soln%gradz,  &
                        grid%slen,sadj%coltag,soln%turb,grid%nedge,            &
                        grid%nedgeLoc,grid%eptr,grid%xn,grid%yn,grid%zn,       &
                        grid%ra,soln%dft1,soln%dft2,                           &
                        sadj%aa,crow%iau,crow%fhelp,grid%nbound,grid%bc,       &
                        soln%ndim,soln%adim,soln%n_turb,grid%facespeed,        &
                        soln%eqn_set,soln%n_q,design%nfunctions,grid%dxdt,     &
                        grid%dydt,grid%dzdt,grid%elem,grid%nelem,soln%n_tot,   &
                        crow%ia,crow%ja,grid%symmetry)


          call zero_rows_via_coltag(grid%nnodes01, soln%adim, sadj%coltag,     &
                                    sadj%aa, crow%ia, crow%ja, matrix_extent)

          call enforce_turb_strong_bc(grid%nnodes01, grid%nnodes0, crow%nnz0,  &
                                      soln%adim, sadj%aa, crow%iau,            &
                                      grid%nbound, grid%bc, grid%symmetry)

          call lstgs(soln%viscous_method,                                      &
                     grid%nnodes0,  grid%nnodes01,                             &
                     grid%nedgeloc, grid%eptr,   grid%symmetry,                &
                     soln%q_dof,    soln%gradx,  soln%grady,    soln%gradz,    &
                     grid%x,        grid%y,      grid%z,                       &
                     grid%r11,      grid%r12,    grid%r13,                     &
                     grid%r22,      grid%r23,    grid%r33,                     &
                     soln%n_tot,    soln%n_grd,  soln%turb,                    &
                     soln%n_turb,   soln%eqn_set,soln%ndim)

          call lmpi_xfer(soln%gradx)
          call lmpi_xfer(soln%grady)
          call lmpi_xfer(soln%gradz)

        case (incompressible)

          call turbgrad(soln%eqn_set,  grid%nnodes0,  grid%nnodes01,           &
                        grid%nedgeloc, grid%eptr,                              &
                        soln%q_dof,    grid%x,     grid%y,        grid%z,      &
                        grid%dxdt,     grid%dydt,  grid%dzdt,                  &
                        grid%xn,       grid%yn,    grid%zn,       grid%ra,     &
                        grid%vol,      soln%gradx, soln%grady,    soln%gradz,  &
                        grid%nbound,   grid%bc,    grid%nedgeloc_2d,           &
                        grid%node_pairs_2d,        grid%nnodes0_2d,            &
                        grid%nelem,   grid%elem, soln%n_tot, soln%n_grd)

          call lmpi_xfer(soln%gradx)
          call lmpi_xfer(soln%grady)
          call lmpi_xfer(soln%gradz)

          do i = 1, grid%nelem
            call dvisrhs_mix5(grid%nnodes0, grid%nnodes01, grid%elem(i)%ncell, &
                              grid%elem(i)%c2n, grid%x, grid%y, grid%z,        &
                              soln%q_dof, soln%amut, grid%elem(i)%local_f2n,   &
                              grid%elem(i)%local_e2n, grid%elem(i)%chk_norm,   &
                              grid%elem(i)%local_f2e, grid%elem(i)%e2n_2d,     &
                              grid%elem(i)%face_per_cell,                      &
                              grid%elem(i)%node_per_cell,                      &
                              grid%elem(i)%edge_per_cell,                      &
                              grid%elem(i)%type_cell, soln%n_tot, soln%adim,   &
                              grid%elem(i)%face_2d, soln%n_turb, soln%turb,    &
                              ia=crow%ia,ja=crow%ja,iau=crow%iau,aa=sadj%aa)
          end do

          call dvisrhs_bc_mix5(grid%nnodes0, grid%nnodes01,                    &
                               crow%nnz0, grid%nbound, grid%bc,                &
                               crow%ia, crow%ja, crow%iau, grid%nelem,         &
                               grid%elem, grid%x, grid%y, grid%z, soln%q_dof,  &
                               soln%amut, soln%n_tot, soln%adim,               &
                               design%nfunctions, sadj%coltag, sadj%rlam,      &
                               soln%n_turb, soln%turb, aa=sadj%aa)

          call fillturbi(grid%nnodes01,grid%nnodes0,grid%x,grid%y,grid%z,      &
                         grid%vol,soln%q_dof,soln%gradx,soln%grady,soln%gradz, &
                         grid%slen,sadj%coltag,soln%turb,grid%nedge,           &
                         grid%nedgeloc,grid%eptr,grid%xn,grid%yn,grid%zn,      &
                         grid%ra,soln%dft1,soln%dft2,crow%nnz0,sadj%aa,        &
                         crow%iau,crow%fhelp,grid%nbound,grid%bc,soln%ndim,    &
                         soln%adim,soln%n_turb,grid%facespeed,soln%eqn_set,    &
                         soln%n_q,design%nfunctions,grid%dxdt,grid%dydt,       &
                         grid%dzdt,grid%elem,crow%ia,crow%ja,grid%nelem,       &
                         grid%symmetry)

          call zero_rows_via_coltag(grid%nnodes01, soln%adim, sadj%coltag,     &
                                    sadj%aa, crow%ia, crow%ja, matrix_extent)

          call enforce_turb_strong_bci(grid%nnodes01, grid%nnodes0, crow%nnz0, &
                                       soln%adim, sadj%aa, crow%iau,           &
                                       grid%nbound, grid%bc )

          call lstgs(soln%viscous_method,                                      &
                     grid%nnodes0,  grid%nnodes01,                             &
                     grid%nedgeloc, grid%eptr,   grid%symmetry,                &
                     soln%q_dof,    soln%gradx,  soln%grady,    soln%gradz,    &
                     grid%x,        grid%y,      grid%z,                       &
                     grid%r11,      grid%r12,    grid%r13,                     &
                     grid%r22,      grid%r23,    grid%r33,                     &
                     soln%n_tot,    soln%n_grd,  soln%turb,                    &
                     soln%n_turb,   soln%eqn_set,soln%ndim)

          call lmpi_xfer(soln%gradx)
          call lmpi_xfer(soln%grady)
          call lmpi_xfer(soln%gradz)

        case default
          write(*,*)'ERROR: eqn_set=',soln%eqn_set,' not known in fcnadt stop.'
          call lmpi_die
        end select

      end select precompute_ivisc

      call enforce_symmetry_bc( grid%nnodes01, grid%nnodes0,                   &
                                crow%nnz0, soln%adim,                          &
                                sadj%aa,   crow%iau,                           &
                                grid%nbound,   grid%bc)

      if ( soln%eqn_set == compressible ) then
        call ptoe( grid%nnodes01, soln%q_dof, soln%n_tot, soln%eqn_set )
      endif

!     Transpose each block in the matrix

      nnz = crow%nnz0
      if ( store_full_stencil ) nnz = crow%nnz01

      select case (soln%adim)
      case (4)
        call transpose_4(sadj%aa,nnz,soln%adim)
      case (5)
        call transpose_5(sadj%aa,nnz,soln%adim)
      case (6)
        call transpose_6(sadj%aa,nnz,soln%adim)
      case default
        write(*,*)'ERROR: soln%adim= ',soln%adim,' unknown in store_aa : stop.'
        call lmpi_die
      end select

      if(ic_y_symmetry) then

!       Scale strong viscous dR/dQ by volume - only if checking residuals

        call my_alloc_ptr(visited, grid%nnodes01)
        visited = .false.
        do ib = 1,grid%nbound
          if( .not.bc_strong_viscous_adjoint(grid%bc(ib)%ibc) ) cycle
          do i = 1,grid%bc(ib)%nbnode
            node = grid%bc(ib)%ibnode(i)
            if(node > grid%nnodes0) cycle
            if(visited(node)) cycle
            diag = crow%iau(node)
            sadj%aa(:,:,diag) = sadj%aa(:,:,diag)*grid%vol(node)
            visited(node) = .true.
          end do
        enddo
      endif

    endif store_matrix

  end subroutine store_aa


!================================= WRITE_DRDQT ===============================80
!
! Writes dR/dQ transpose for testing
!
!=============================================================================80
  subroutine write_drdqt(nnodes01,nnodesg,l2g,aa,ia,ja)

    use info_depr,         only : physical_timestep, ncyc
    use system_extensions, only : se_open
    use string_utils,      only : int_to_s
    use file_utils,        only : available_unit
    use lmpi,              only : lmpi_id
    use kinddefs,          only : dp, i8

    integer, intent(in) :: nnodes01, nnodesg

    integer, dimension(:), intent(in) :: l2g, ia, ja

    real(dp), dimension(:,:,:), intent(in) :: aa

    integer :: i, j, jstart, jend, jcol

    integer,     save :: iu1, iu2
    integer(i8), save :: current_position

    character(len=1000) :: filename

    logical, save :: first_time = .true.

  continue

! I would like to use an inquire statement to get the current file position,
! but it doesnt seem to always work correctly.  Therefore I will count bytes
! manually.  Bytes are not portable file pointer units, but it seems to
! be what the Intel compiler uses.

    if ( first_time ) then

      iu1 = available_unit()
      filename = 'drdqt.data.' // trim(int_to_s(lmpi_id+1))
      call se_open(iu1,file=trim(filename),form='unformatted',status='replace',&
                   access='stream')

      current_position = 1

      write(iu1) ncyc, nnodes01, nnodesg

      current_position = current_position + 12

      do i = 1, nnodes01
        write(iu1) l2g(i)
      end do

      current_position = current_position + 4*nnodes01

      iu2 = available_unit()
      filename = 'drdqt.metadata.' // trim(int_to_s(lmpi_id+1))
      call se_open(iu2,file=trim(filename),form='formatted',status='replace')

      write(iu2,*) ncyc, nnodes01, nnodesg

      first_time = .false.

    endif

    write(iu2,*) current_position

    do i = 1, nnodes01

      jstart = ia(i)
      jend   = ia(i+1) - 1

      write(iu1) jend-jstart+1

      current_position = current_position + 4

      do j = jstart, jend
        jcol = ja(j)
        write(iu1) l2g(jcol)
        current_position = current_position + 4
        write(iu1) aa(:,:,j)
        current_position = current_position + 25*8
      end do

    end do

    if ( physical_timestep == 1 ) then
      close(iu1)
      close(iu2)
    endif

  end subroutine write_drdqt


!================================= TRANSPOSE_4 ===============================80
!
! Take lhs and form the transpose of each 4x4 sub-block
!
!=============================================================================80

  subroutine transpose_4(lhs,nnz, adim)

    use kinddefs, only : dp

    integer, intent(in) :: nnz, adim

    integer             :: i

    real(dp), dimension(adim,adim,nnz), intent(inout) :: lhs

    real(dp)    :: temp11, temp21, temp31, temp41
    real(dp)    :: temp12, temp22, temp32, temp42
    real(dp)    :: temp13, temp23, temp33, temp43
    real(dp)    :: temp14, temp24, temp34, temp44

    continue

    do i = 1, nnz
      temp11 = lhs(1,1,i)
      temp21 = lhs(2,1,i)
      temp31 = lhs(3,1,i)
      temp41 = lhs(4,1,i)

      temp12 = lhs(1,2,i)
      temp22 = lhs(2,2,i)
      temp32 = lhs(3,2,i)
      temp42 = lhs(4,2,i)

      temp13 = lhs(1,3,i)
      temp23 = lhs(2,3,i)
      temp33 = lhs(3,3,i)
      temp43 = lhs(4,3,i)

      temp14 = lhs(1,4,i)
      temp24 = lhs(2,4,i)
      temp34 = lhs(3,4,i)
      temp44 = lhs(4,4,i)

      lhs(1,1,i) = temp11
      lhs(2,1,i) = temp12
      lhs(3,1,i) = temp13
      lhs(4,1,i) = temp14

      lhs(1,2,i) = temp21
      lhs(2,2,i) = temp22
      lhs(3,2,i) = temp23
      lhs(4,2,i) = temp24

      lhs(1,3,i) = temp31
      lhs(2,3,i) = temp32
      lhs(3,3,i) = temp33
      lhs(4,3,i) = temp34

      lhs(1,4,i) = temp41
      lhs(2,4,i) = temp42
      lhs(3,4,i) = temp43
      lhs(4,4,i) = temp44

    end do

  end subroutine transpose_4


!================================= TRANSPOSE_5 ===============================80
!
! Take lhs and form the transpose of each 5x5 sub-block
!
!=============================================================================80

  subroutine transpose_5(lhs,nnz, adim)

    use kinddefs, only : dp

    integer, intent(in) :: nnz, adim

    integer             :: i

    real(dp), dimension(adim,adim,nnz), intent(inout) :: lhs

    real(dp)    :: temp11, temp21, temp31, temp41, temp51
    real(dp)    :: temp12, temp22, temp32, temp42, temp52
    real(dp)    :: temp13, temp23, temp33, temp43, temp53
    real(dp)    :: temp14, temp24, temp34, temp44, temp54
    real(dp)    :: temp15, temp25, temp35, temp45, temp55

    continue

    do i = 1, nnz
      temp11 = lhs(1,1,i)
      temp21 = lhs(2,1,i)
      temp31 = lhs(3,1,i)
      temp41 = lhs(4,1,i)
      temp51 = lhs(5,1,i)

      temp12 = lhs(1,2,i)
      temp22 = lhs(2,2,i)
      temp32 = lhs(3,2,i)
      temp42 = lhs(4,2,i)
      temp52 = lhs(5,2,i)

      temp13 = lhs(1,3,i)
      temp23 = lhs(2,3,i)
      temp33 = lhs(3,3,i)
      temp43 = lhs(4,3,i)
      temp53 = lhs(5,3,i)

      temp14 = lhs(1,4,i)
      temp24 = lhs(2,4,i)
      temp34 = lhs(3,4,i)
      temp44 = lhs(4,4,i)
      temp54 = lhs(5,4,i)

      temp15 = lhs(1,5,i)
      temp25 = lhs(2,5,i)
      temp35 = lhs(3,5,i)
      temp45 = lhs(4,5,i)
      temp55 = lhs(5,5,i)

      lhs(1,1,i) = temp11
      lhs(2,1,i) = temp12
      lhs(3,1,i) = temp13
      lhs(4,1,i) = temp14
      lhs(5,1,i) = temp15

      lhs(1,2,i) = temp21
      lhs(2,2,i) = temp22
      lhs(3,2,i) = temp23
      lhs(4,2,i) = temp24
      lhs(5,2,i) = temp25

      lhs(1,3,i) = temp31
      lhs(2,3,i) = temp32
      lhs(3,3,i) = temp33
      lhs(4,3,i) = temp34
      lhs(5,3,i) = temp35

      lhs(1,4,i) = temp41
      lhs(2,4,i) = temp42
      lhs(3,4,i) = temp43
      lhs(4,4,i) = temp44
      lhs(5,4,i) = temp45

      lhs(1,5,i) = temp51
      lhs(2,5,i) = temp52
      lhs(3,5,i) = temp53
      lhs(4,5,i) = temp54
      lhs(5,5,i) = temp55

    end do

  end subroutine transpose_5


!================================= TRANSPOSE_6 ===============================80
!
! Take lhs and form the transpose of each 6x6 sub-block
!
!=============================================================================80

  subroutine transpose_6(lhs,nnz, adim)

    use kinddefs, only : dp

    integer, intent(in) :: nnz, adim

    integer             :: i

    real(dp), dimension(adim,adim,nnz), intent(inout) :: lhs

    real(dp)    :: temp11, temp21, temp31, temp41, temp51, temp61
    real(dp)    :: temp12, temp22, temp32, temp42, temp52, temp62
    real(dp)    :: temp13, temp23, temp33, temp43, temp53, temp63
    real(dp)    :: temp14, temp24, temp34, temp44, temp54, temp64
    real(dp)    :: temp15, temp25, temp35, temp45, temp55, temp65
    real(dp)    :: temp16, temp26, temp36, temp46, temp56, temp66

    continue

    do i = 1, nnz
      temp11 = lhs(1,1,i)
      temp21 = lhs(2,1,i)
      temp31 = lhs(3,1,i)
      temp41 = lhs(4,1,i)
      temp51 = lhs(5,1,i)
      temp61 = lhs(6,1,i)

      temp12 = lhs(1,2,i)
      temp22 = lhs(2,2,i)
      temp32 = lhs(3,2,i)
      temp42 = lhs(4,2,i)
      temp52 = lhs(5,2,i)
      temp62 = lhs(6,2,i)

      temp13 = lhs(1,3,i)
      temp23 = lhs(2,3,i)
      temp33 = lhs(3,3,i)
      temp43 = lhs(4,3,i)
      temp53 = lhs(5,3,i)
      temp63 = lhs(6,3,i)

      temp14 = lhs(1,4,i)
      temp24 = lhs(2,4,i)
      temp34 = lhs(3,4,i)
      temp44 = lhs(4,4,i)
      temp54 = lhs(5,4,i)
      temp64 = lhs(6,4,i)

      temp15 = lhs(1,5,i)
      temp25 = lhs(2,5,i)
      temp35 = lhs(3,5,i)
      temp45 = lhs(4,5,i)
      temp55 = lhs(5,5,i)
      temp65 = lhs(6,5,i)

      temp16 = lhs(1,6,i)
      temp26 = lhs(2,6,i)
      temp36 = lhs(3,6,i)
      temp46 = lhs(4,6,i)
      temp56 = lhs(5,6,i)
      temp66 = lhs(6,6,i)

      lhs(1,1,i) = temp11
      lhs(2,1,i) = temp12
      lhs(3,1,i) = temp13
      lhs(4,1,i) = temp14
      lhs(5,1,i) = temp15
      lhs(6,1,i) = temp16

      lhs(1,2,i) = temp21
      lhs(2,2,i) = temp22
      lhs(3,2,i) = temp23
      lhs(4,2,i) = temp24
      lhs(5,2,i) = temp25
      lhs(6,2,i) = temp26

      lhs(1,3,i) = temp31
      lhs(2,3,i) = temp32
      lhs(3,3,i) = temp33
      lhs(4,3,i) = temp34
      lhs(5,3,i) = temp35
      lhs(6,3,i) = temp36

      lhs(1,4,i) = temp41
      lhs(2,4,i) = temp42
      lhs(3,4,i) = temp43
      lhs(4,4,i) = temp44
      lhs(5,4,i) = temp45
      lhs(6,4,i) = temp46

      lhs(1,5,i) = temp51
      lhs(2,5,i) = temp52
      lhs(3,5,i) = temp53
      lhs(4,5,i) = temp54
      lhs(5,5,i) = temp55
      lhs(6,5,i) = temp56

      lhs(1,6,i) = temp61
      lhs(2,6,i) = temp62
      lhs(3,6,i) = temp63
      lhs(4,6,i) = temp64
      lhs(5,6,i) = temp65
      lhs(6,6,i) = temp66

    end do

  end subroutine transpose_6

!================================ ZERO_ROWS_VIA_COLTAG =======================80
!
! Zero rows of A matrix based on coltag.
!
! Note: The block jacobian matrices are not yet transposed.
!
!=============================================================================80

  subroutine zero_rows_via_coltag(nnodes01,adim,coltag,A,ia,ja,extent)

    use kinddefs, only : dp

    integer, intent(in) :: nnodes01, adim, extent

    real(dp), dimension(adim,nnodes01),    intent(in) :: coltag

    real(dp), dimension(:,:,:), intent(inout) :: A

    integer, dimension(:), intent(in) :: ia,ja

    integer :: i, row
    integer :: jstart, jend, j
    integer :: column

    real(dp), parameter    :: my_0     = 0.0_dp

    continue

! Loop through simple and non-simple entries.

    eqn_loop : do i = 1, extent

      jstart = ia(i)
      jend   = ia(i+1)-1

      col_loop : do j = jstart, jend

        column = ja(j)

        do row=1,adim

          if ( coltag(row,column) > 0.5_dp ) cycle

          A(row,:,j) = my_0

        enddo
      end do col_loop

    end do eqn_loop

  end subroutine zero_rows_via_coltag

end module residual_drdq

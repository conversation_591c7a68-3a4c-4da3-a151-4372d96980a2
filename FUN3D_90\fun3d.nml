&project
  project_rootname = 'projectname'
/
&raw_grid
  grid_format = 'aflr3' 
  data_format = 'stream' ! .b8.ugrid or .lb8.ugrid
/
&reference_physical_properties
  mach_number       = 0.3
  reynolds_number   = 1.0e+6
  angle_of_attack   = 2.0
  temperature       = 460.0
  temperature_units = 'Rankine'
/
&force_moment_integ_properties
  x_moment_center = 0.25
/
&nonlinear_solver_parameters
  schedule_cfl       = 10.0  200.0
  schedule_cflturb   =  1.0   50.0
/
&code_run_control
  steps              = 100
  stopping_tolerance = 1.0e-9
  restart_read       = 'off'
/
&flow_initialization
  number_of_volumes = 4
  type_of_volume(1) = 'box'
   pmin(:,1) = -1.0, -1.0, -1.0
   pmax(:,1) = -0.5, -0.5, -0.5
      rho(1) = 1.0
        c(1) = 0.9
        u(1) = 0.4
  type_of_volume(2)='sphere'
   center(:,2) = 1.0, 1.0, 1.0
     radius(2) = 0.5
        rho(2) = 1.1
          c(2) = 1.1
          u(2) = 0.5
  type_of_volume(3) = 'cylinder'
   point1(:,3) = -1.0, 0.0, 0.0
   point2(:,3) =  0.0, 0.0, 0.0
     radius(3) = 0.5
        rho(3) = 1.2
          c(3) = 1.1
          u(3) = 0.6
  type_of_volume(4) = 'cone'
   point1(:,4) = 0.0, 0.0, 0.0
   point2(:,4) = 1.0, 0.0, 0.0
    radius1(4) = 0.1
    radius2(4) = 1.0
       rho(4) = 1.3
         c(4) = 1.2
         u(4) = 0.7
/
&boundary_output_variables
  number_of_boundaries = -1 ! compute from following list
  boundary_list        = '1,2,4,5,8,10,15,20-25,30'
/
&sonic_boom
  x_lower_bound =  -1.0  ! req'd for 'boom_targ'
  x_upper_bound =   1.0  ! req'd for 'boom_targ'
  npoints       =  1000  ! req'd for 'boom_targ'
  nsignals      =     2  ! req'd for 'boom_targ' and 'sboom'; max is 1000
  y_ray(1)      = 0.001  ! req'd for 'boom_targ' and 'sboom'; max is 1000
  z_ray(1)      =  -2.0  ! req'd for 'boom_targ' and 'sboom'; max is 1000
  y_ray(2)      = 0.001  ! req'd for 'boom_targ' and 'sboom'; max is 1000
  z_ray(2)      =  -5.0  ! req'd for 'boom_targ' and 'sboom'; max is 1000
  x_cor         =   0.0  ! req'd for 'boom_targ' and 'sboom'
  z_cor         =   0.0  ! req'd for 'boom_targ' and 'sboom'
/

!================================= QP_FROM_QT ================================80
!
! Primitive variables from variables (var1,u,v,w,T).
!
!=============================================================================80
  pure function qp_from_qt( eqn_set, n_q, qt)

    use flux_constants, only : gamma_inv
    use solution_types, only : compressible

    integer,                intent(in) :: eqn_set, n_q
    real(dp), dimension(:), intent(in) :: qt

    real(dp), dimension(n_q)           :: qp_from_qt

  continue

    qp_from_qt(1:n_q) = qt(1:n_q)

    if ( eqn_set == compressible ) qp_from_qt(5) = qt(1)*qt(5)*gamma_inv

  end function qp_from_qt

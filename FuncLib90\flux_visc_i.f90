!================================= FLUX_VISC_I ===============================80
!
! Full viscous fluxes for cell-centered given l,r state and gradient.
!
!=============================================================================80
  pure function flux_visc_i(xnorm, ynorm, znorm, area, ql, qr,                 &
                            gradxl, gradyl, gradzl, gradxr, gradyr, gradzr,    &
                            amutl, amutr, dx, dy, dz)

    use info_depr,             only : re

    real(dp), intent(in) :: xnorm, ynorm, znorm, area
    real(dp), intent(in) :: amutl, amutr, dx, dy, dz

    real(dp), dimension(4), intent(in)  :: ql, qr
    real(dp), dimension(4), intent(in)  :: gradxl, gradyl, gradzl
    real(dp), dimension(4), intent(in)  :: gradxr, gradyr, gradzr
    real(dp), dimension(4) :: flux_visc_i

    real(dp), dimension(4):: lgradx, lgrady, lgradz, dots

    real(dp) :: rei, rmu, ds2i, termx, termy, termz
    real(dp) :: ux, uy, uz
    real(dp) :: vx, vy, vz
    real(dp) :: wx, wy, wz
    real(dp) :: txx, txy, txz, tyy, tyz, tzz, rax, ray, raz

  continue

    rei = 1.0_dp/re

    ds2i = 1.0_dp/(dx*dx + dy*dy + dz*dz)

    termx = dx*ds2i
    termy = dy*ds2i
    termz = dz*ds2i

    lgradx(2:4) = 0.5_dp*(gradxl(2:4) + gradxr(2:4))
    lgrady(2:4) = 0.5_dp*(gradyl(2:4) + gradyr(2:4))
    lgradz(2:4) = 0.5_dp*(gradzl(2:4) + gradzr(2:4))

    dots(2:4) = lgradx(2:4)*dx + lgrady(2:4)*dy + lgradz(2:4)*dz

    lgradx(2:4) = lgradx(2:4) + ( qr(2:4) - ql(2:4) - dots(2:4) )*termx
    lgrady(2:4) = lgrady(2:4) + ( qr(2:4) - ql(2:4) - dots(2:4) )*termy
    lgradz(2:4) = lgradz(2:4) + ( qr(2:4) - ql(2:4) - dots(2:4) )*termz

    ux = lgradx(2)
    vx = lgradx(3)
    wx = lgradx(4)

    uy = lgrady(2)
    vy = lgrady(3)
    wy = lgrady(4)

    uz = lgradz(2)
    vz = lgradz(3)
    wz = lgradz(4)

!   Viscous contributions at face [ full Navier-Stokes terms ]
!   average viscosity [ kinematic + turbulent ]

    rmu = 1.0_dp + 0.5_dp*( amutl + amutr )

!   Viscous contributions [ full Navier-Stokes terms ]

!   components of symmetric stress tensor

    txx = 2._dp*ux
    txy = uy + vx
    txz = uz + wx

    tyy = 2._dp*vy
    tyz = vz + wy

    tzz = 2._dp*wz

!   -[ viscosity ] * [ unit normal and area ] *
!   [nondimensionalization factor rei ] at face

    rax = -rei*rmu*area*xnorm
    ray = -rei*rmu*area*ynorm
    raz = -rei*rmu*area*znorm

    flux_visc_i(2) = rax*txx + ray*txy + raz*txz
    flux_visc_i(3) = rax*txy + ray*tyy + raz*tyz
    flux_visc_i(4) = rax*txz + ray*tyz + raz*tzz

  end function flux_visc_i

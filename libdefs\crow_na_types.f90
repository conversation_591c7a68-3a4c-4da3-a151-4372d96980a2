module crow_na_types

  use kinddefs,        only : system_i1, dp

  implicit none

  private

  public :: crow_na

  type crow_na

    !...interior faces
    integer, dimension(:), pointer :: ia
    integer, dimension(:), pointer :: cell

    !...boundary_faces
    integer,            dimension(:), pointer :: iab
    integer,            dimension(:), pointer :: ibc
    integer,            dimension(:), pointer :: iqt
    integer(system_i1), dimension(:), pointer :: ibr
    real(dp),           dimension(:), pointer :: xf, yf, zf, sf

  end type crow_na

end module crow_na_types

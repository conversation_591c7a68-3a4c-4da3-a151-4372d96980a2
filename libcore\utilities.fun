!   vim: set filetype=fortran:
! emacs: -*- f90 -*-


test_suite utilities

test tinverse_identity
  use kinddefs, only: dp
  real(dp), dimension(3,3) :: t, tinv
  integer :: i, j
  t = 0_dp ; t(1,1) = 1_dp ; t(2,2) = 1_dp ; t(3,3) = 1_dp
  call tinverse( t, tinv )
  do i = 1, 3
    do j = 1, 3
      if (i==j) then
        assert_equal( 1_dp, tinv(i,j) )
      else
        assert_equal( 0_dp, tinv(i,j) )
      end if
    end do
  end do
end test

test tinverse_known
  use kinddefs, only: dp
  real(dp), dimension(3,3) :: t, tinv
  t(1,1) = 1_dp ; t(1,2) = 2_dp ; t(1,3) = 3_dp
  t(2,1) = 0_dp ; t(2,2) = 1_dp ; t(2,3) = 4_dp
  t(3,1) = 5_dp ; t(3,2) = 6_dp ; t(3,3) = 0_dp
  call tinverse( t, tinv )
  assert_equal( -24_dp, tinv(1,1) )
  assert_equal(  18_dp, tinv(1,2) )
  assert_equal(   5_dp, tinv(1,3) )
  assert_equal(  20_dp, tinv(2,1) )
  assert_equal( -15_dp, tinv(2,2) )
  assert_equal(  -4_dp, tinv(2,3) )
  assert_equal(  -5_dp, tinv(3,1) )
  assert_equal(   4_dp, tinv(3,2) )
  assert_equal(   1_dp, tinv(3,3) )
end test

test cell_gradients_ddt_tet

  use kinddefs, only : dp
  use info_depr,       only : twod
  use debug_defs,      only : best_practice
  use fun3d_constants, only : my_1, my_0, my_half, my_4th, my_6th
  use lmpi,            only : lmpi_conditional_stop
  use ddt,             only : ddt5, assignment(=), operator(-)               &
                            , operator(*), operator(+), operator(/)          &
                            , ddt5_identity

    integer                                 :: edges_local
    integer, parameter                      :: qdim          = 5
    integer, parameter                      :: node_per_cell = 4
    integer, parameter                      :: face_per_cell = 4
    integer,  dimension(4,2)                :: e2n_2d

    real(dp), dimension(node_per_cell)         :: x_node
    real(dp), dimension(node_per_cell)         :: y_node
    real(dp), dimension(node_per_cell)         :: z_node
    type(ddt5), dimension(qdim              )  :: qnode
    type(ddt5), dimension(qdim,node_per_cell)  :: q_node
    type(ddt5), dimension(     node_per_cell)  :: rho_node, u_node, v_node, w_node, p_node

! resutl

    type(ddt5), dimension(qdim)                :: gradx_cell
    type(ddt5), dimension(qdim)                :: grady_cell
    type(ddt5), dimension(qdim)                :: gradz_cell
    real(dp), dimension(face_per_cell)         :: nxf
    real(dp), dimension(face_per_cell)         :: nyf
    real(dp), dimension(face_per_cell)         :: nzf
    real(dp)                                   :: cell_vol

    real(dp), dimension(node_per_cell)         :: dgradx_celldq
    real(dp), dimension(node_per_cell)         :: dgrady_celldq
    real(dp), dimension(node_per_cell)         :: dgradz_celldq

    real(dp), dimension(qdim)         :: q 

    integer, parameter, dimension (4,4) :: local_f2n_tet =                       &
    reshape((/  1, 2, 1, 1,                                                      &
                3, 3, 4, 2,                                                      &
                2, 4, 3, 4,                                                      &
                1, 2, 1, 1 /), (/ 4, 4/))

    best_practice = .true.
    twod          = .false.
    edges_local   = 6

  x_node(1) = 0.0_dp; y_node(1) = 0.0_dp; z_node(1) = 0.0_dp
  x_node(2) = 1.0_dp; y_node(2) = 0.1_dp; z_node(2) = 0.1_dp
  x_node(3) = 0.5_dp; y_node(3) = 1.0_dp; z_node(3) = 0.2_dp
  x_node(4) = 0.3_dp; y_node(4) = 0.5_dp; z_node(4) = 1.0_dp
! x_node(1) = 0.0_dp; y_node(1) = 0.0_dp; z_node(1) = 0.0_dp
! x_node(2) = 1.0_dp; y_node(2) = 0.0_dp; z_node(2) = 0.0_dp
! x_node(3) = 1.0_dp; y_node(3) = 1.0_dp; z_node(3) = 0.0_dp
! x_node(4) = 0.0_dp; y_node(4) = 1.0_dp; z_node(4) = 0.0_dp
! x_node(5) = 1.0_dp; y_node(5) = 0.0_dp; z_node(5) = 5.0e-2_dp
! x_node(6) = 1.0_dp; y_node(6) = 1.0_dp; z_node(6) = 5.0e-2_dp
! x_node(7) = 0.0_dp; y_node(7) = 0.0_dp; z_node(7) = 5.0e-2_dp
! x_node(8) = 0.0_dp; y_node(8) = 1.0_dp; z_node(8) = 5.0e-2_dp
! x_node(9) =-0.5_dp; y_node(9) = 0.5_dp; z_node(9) = 2.5e-2_dp
! node 1
! q(1:5) = (/1.0_dp,0.000_dp,0.000_dp,0.000_dp,0.714_dp/)
  q(1:5) = 1.0_dp
  qnode = ddt5_identity(q)
  rho_node(1) = qnode(1)
    u_node(1) = qnode(2)
    v_node(1) = qnode(3)
    w_node(1) = qnode(4)
    p_node(1) = qnode(5)
! node 1
! q(1:5) = (/1.0_dp,0.0001_dp,0.0001_dp,0.0001_dp,0.714_dp/)
  q(1:5) = 1.0_dp
  qnode = ddt5_identity(q)
  rho_node(2) = qnode(1)
    u_node(2) = qnode(2)
    v_node(2) = qnode(3)
    w_node(2) = qnode(4)
    p_node(2) = qnode(5)
! node 3
! q(1:5) = (/1.5_dp,0.0002_dp,0.0002_dp,0.0002_dp,0.9_dp/)
  q(1:5) = 1.0_dp
  qnode = ddt5_identity(q)
  rho_node(3) = qnode(1)
    u_node(3) = qnode(2)
    v_node(3) = qnode(3)
    w_node(3) = qnode(4)
    p_node(3) = qnode(5)
! node 4
! q(1:5) = (/2.0_dp,0.01_dp,0.01_dp,0.01_dp,1.000_dp/)
  q(1:5) = 1.0_dp
  qnode = ddt5_identity(q)
  rho_node(4) = qnode(1)
    u_node(4) = qnode(2)
    v_node(4) = qnode(3)
    w_node(4) = qnode(4)
    p_node(4) = qnode(5)

  q_node(1,:) = rho_node(:)
  q_node(2,:) =   u_node(:)
  q_node(3,:) =   v_node(:)
  q_node(4,:) =   w_node(:)
  q_node(5,:) =   p_node(:)
    write(6,'(a,10(1x,es15.5))') 'q_node(1,1)   : ', q_node(1,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(2,1)   : ', q_node(2,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(3,1)   : ', q_node(3,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(4,1)   : ', q_node(4,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(5,1)   : ', q_node(5,1)

    call cell_gradients_ddt(edges_local, node_per_cell, face_per_cell,      &
                     x_node, y_node, z_node, qdim, q_node, local_f2n_tet,   &
                     e2n_2d, gradx_cell, grady_cell, gradz_cell,            &
                     cell_vol, nxf, nyf, nzf)

!   write(6,'(a,10(1x,es15.5))') 'gradx_cell    : ', gradx_cell%f
!   write(6,'(a,10(1x,es15.5))') 'grady_cell    : ', grady_cell%f
!   write(6,'(a,10(1x,es15.5))') 'gradz_cell    : ', gradz_cell%f
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(1) : ', gradx_cell(1)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(2) : ', gradx_cell(2)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(3) : ', gradx_cell(3)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(4) : ', gradx_cell(4)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(5) : ', gradx_cell(5)
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(1) : ', grady_cell(1)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(2) : ', grady_cell(2)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(3) : ', grady_cell(3)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(4) : ', grady_cell(4)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(5) : ', grady_cell(5)
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(1) : ', gradz_cell(1)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(2) : ', gradz_cell(2)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(3) : ', gradz_cell(3)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(4) : ', gradz_cell(4)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(5) : ', gradz_cell(5)
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'nxf           : ', nxf
    write(6,'(a,10(1x,es15.5))') 'nyf           : ', nyf
    write(6,'(a,10(1x,es15.5))') 'nzf           : ', nzf
write(*,*)

   call       cell_jacobians(edges_local, node_per_cell, face_per_cell, x_node,     &
                             y_node, z_node, local_f2n_tet, e2n_2d, dgradx_celldq,  &
                             dgrady_celldq, dgradz_celldq, cell_vol, nxf, nyf,      &
                             nzf)
 
    write(6,'(a,10(1x,es15.5))') 'dgradx_celldq : ', dgradx_celldq
    write(6,'(a,10(1x,es15.5))') 'dgrady_celldq : ', dgrady_celldq
    write(6,'(a,10(1x,es15.5))') 'dgradz_celldq : ', dgradz_celldq
 


end test

test cell_gradients_ddt_hex

  use kinddefs, only : dp
  use info_depr,       only : twod
  use debug_defs,      only : best_practice
  use fun3d_constants, only : my_1, my_0, my_half, my_4th, my_6th
  use lmpi,            only : lmpi_conditional_stop
  use ddt,             only : ddt5, assignment(=), operator(-)               &
                            , operator(*), operator(+), operator(/)          &
                            , ddt5_identity

    integer                                 :: edges_local
    integer, parameter                      :: qdim          = 5
    integer, parameter                      :: node_per_cell = 8
    integer, parameter                      :: face_per_cell = 6
    integer,  dimension(4,2)                :: e2n_2d

    real(dp), dimension(node_per_cell)         :: x_node
    real(dp), dimension(node_per_cell)         :: y_node
    real(dp), dimension(node_per_cell)         :: z_node
    type(ddt5), dimension(qdim              )  :: qnode
    type(ddt5), dimension(qdim,node_per_cell)  :: q_node
    type(ddt5), dimension(     node_per_cell)  :: rho_node, u_node, v_node, w_node, p_node

! resutl

    type(ddt5), dimension(qdim)                :: gradx_cell
    type(ddt5), dimension(qdim)                :: grady_cell
    type(ddt5), dimension(qdim)                :: gradz_cell
    real(dp), dimension(face_per_cell)         :: nxf
    real(dp), dimension(face_per_cell)         :: nyf
    real(dp), dimension(face_per_cell)         :: nzf
    real(dp)                                   :: cell_vol

    real(dp), dimension(node_per_cell)         :: dgradx_celldq
    real(dp), dimension(node_per_cell)         :: dgrady_celldq
    real(dp), dimension(node_per_cell)         :: dgradz_celldq

    real(dp), dimension(qdim)         :: q 

    integer, parameter, dimension (6,4) :: local_f2n_hex =                       &
    reshape((/ 1, 5, 5, 6, 5, 7,                                                 &
               3, 6, 7, 2, 1, 8,                                                 &
               4, 8, 3, 4, 2, 4,                                                 &
               2, 7, 1, 8, 6, 3 /), (/ 6, 4/))

    best_practice = .true.
    twod          = .false.
    edges_local   = 12

  x_node(1) = 0.0_dp; y_node(1) = 0.0_dp; z_node(1) = 0.0_dp
  x_node(2) = 0.0_dp; y_node(2) = 0.0_dp; z_node(2) = 1.0_dp
  x_node(3) = 1.0_dp; y_node(3) = 0.0_dp; z_node(3) = 0.0_dp
  x_node(4) = 1.0_dp; y_node(4) = 0.0_dp; z_node(4) = 1.0_dp
  x_node(5) = 0.0_dp; y_node(5) = 1.0_dp; z_node(5) = 0.0_dp
  x_node(6) = 0.0_dp; y_node(6) = 1.0_dp; z_node(6) = 1.0_dp
  x_node(7) = 1.0_dp; y_node(7) = 1.0_dp; z_node(7) = 0.0_dp
  x_node(8) = 1.0_dp; y_node(8) = 1.0_dp; z_node(8) = 1.0_dp

!
  q(1:5) = (/1.0_dp,0.000_dp,0.000_dp,0.000_dp,0.714_dp/)
  qnode = ddt5_identity(q)
  rho_node(1:4) = qnode(1)
    u_node(1:4) = qnode(2)
    v_node(1:4) = qnode(3)
    w_node(1:4) = qnode(4)
    p_node(1:4) = qnode(5)
!
  q(1:5) = (/1.0_dp,0.100_dp,0.100_dp,0.100_dp,0.714_dp/)
  qnode = ddt5_identity(q)
  rho_node(5:8) = qnode(1)
    u_node(5:8) = qnode(2)
    v_node(5:8) = qnode(3)
    w_node(5:8) = qnode(4)
    p_node(5:8) = qnode(5)

    q_node(1,:) = rho_node(:)
    q_node(2,:) =   u_node(:)
    q_node(3,:) =   v_node(:)
    q_node(4,:) =   w_node(:)
    q_node(5,:) =   p_node(:)
write(*,*)
write(*,*) 'hex '
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'q_node(1,1)   : ', q_node(1,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(2,1)   : ', q_node(2,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(3,1)   : ', q_node(3,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(4,1)   : ', q_node(4,1)
    write(6,'(a,10(1x,es15.5))') 'q_node(5,1)   : ', q_node(5,1)

    call cell_gradients_ddt(edges_local, node_per_cell, face_per_cell,      &
                     x_node, y_node, z_node, qdim, q_node, local_f2n_hex,   &
                     e2n_2d, gradx_cell, grady_cell, gradz_cell,            &
                     cell_vol, nxf, nyf, nzf)

!   write(6,'(a,10(1x,es15.5))') 'gradx_cell    : ', gradx_cell%f
!   write(6,'(a,10(1x,es15.5))') 'grady_cell    : ', grady_cell%f
!   write(6,'(a,10(1x,es15.5))') 'gradz_cell    : ', gradz_cell%f
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(1) : ', gradx_cell(1)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(2) : ', gradx_cell(2)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(3) : ', gradx_cell(3)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(4) : ', gradx_cell(4)
    write(6,'(a,10(1x,es15.5))') 'gradx_cell(5) : ', gradx_cell(5)
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(1) : ', grady_cell(1)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(2) : ', grady_cell(2)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(3) : ', grady_cell(3)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(4) : ', grady_cell(4)
    write(6,'(a,10(1x,es15.5))') 'grady_cell(5) : ', grady_cell(5)
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(1) : ', gradz_cell(1)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(2) : ', gradz_cell(2)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(3) : ', gradz_cell(3)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(4) : ', gradz_cell(4)
    write(6,'(a,10(1x,es15.5))') 'gradz_cell(5) : ', gradz_cell(5)
write(*,*)
    write(6,'(a,10(1x,es15.5))') 'nxf           : ', nxf
    write(6,'(a,10(1x,es15.5))') 'nyf           : ', nyf
    write(6,'(a,10(1x,es15.5))') 'nzf           : ', nzf
write(*,*)

   call       cell_jacobians(edges_local, node_per_cell, face_per_cell, x_node,     &
                             y_node, z_node, local_f2n_hex, e2n_2d, dgradx_celldq,  &
                             dgrady_celldq, dgradz_celldq, cell_vol, nxf, nyf,      &
                             nzf)
 
    write(6,'(a,10(1x,es15.5))') 'dgradx_celldq : ', dgradx_celldq
    write(6,'(a,10(1x,es15.5))') 'dgrady_celldq : ', dgrady_celldq
    write(6,'(a,10(1x,es15.5))') 'dgradz_celldq : ', dgradz_celldq
 

end test

end test_suite

module composite_jacobian

  use kinddefs,             only : dp
  use info_depr,            only : complex_mode, skeleton,                    &
                                   tightly_couple, twod
  use lmpi,                 only : lmpi_master, lmpi_nproc, lmpi_id,          &
                                   lmpi_conditional_stop, lmpi_bcast,         &
                                   lmpi_reduce


  use system_extensions,    only : se_open
  use grid_types,           only : grid_type, mass_type
  use solution_types,       only : soln_type
  use comprow_types,        only : crow_flow
  use generic_gas_map,      only : n_momy

  use comprow_util,         only : max_adj_off_diag

  use composite_jacobian_util,  only :                                         &
      write_gold_dof_info,                                                     &
      set_likely_flags, reorder_lhs_m2g,                                       &
      header_composite_jacobian,  header_set, check_crow_entries,              &
      get_cmp_residual, frechet_setup, check_frechet, corrupt_jacobian,        &
      complex_epsilon, real_epsilon, gfile1_header

  use complex_functions, only : extract_imaginary_part
  use inviscid_flux,     only : mean_decouple
  use debug_defs,        only : debug_dc

  implicit none

  private
  public :: check_cmp_jacobians, lhs_rhs_crs_setup

  integer :: gfile1 = 60, gfile2 = 61 !golden files
  integer :: gold_dof

contains

!============================== CHECK_CMP_JACOBIANS ==========================80
!
! Driver routine for checking jacobians by comparing lhs jacobians
! with either finite difference or complex-mode derivatives of the residual.
!
!=============================================================================80

  subroutine check_cmp_jacobians(grid, soln, crow, mass)

    use comprow,            only : set_comprow_flow_rhs
    use stability_defs,     only : d_lhs, a_lhs, d_rhs, a_rhs, skip_jac, r00,  &
                                   adj_rhs
    use debug_defs,         only : composite_jacobian_rhs,                     &
                                   composite_jacobian_seed,                    &
                                   composite_jacobian_fraction,                &
                                   debug_q_loc, debug_q_proc, debug_q_global
    use inviscid_flux,      only : first_order_iterations
    use debug_defs,         only : composite_jacobian_lhs, debug_dc
    use generic_gas_map,    only : n_species

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(inout) :: crow
    type(mass_type), dimension(:), intent(in) :: mass

    character(len=80) :: jacobian_type ! e.g. inviscid, viscous,
                                       ! source, sa_turb

    integer :: set, set_start, sets, max_adj_lhs, it
    integer :: njac, ierr, dof_mp, dof01_mp, dof, nnz_mp
    integer :: ip, size1, size2, itemp1, itemp2, proc, assessments, total

    logical :: check_crow = .false.

    real(dp), dimension(:,:), allocatable :: base_res
    real(dp), dimension(:,:), allocatable :: base_turbres

    logical :: local_request, reset_q

! beginNeverComplex
    real(dp) :: random, default_tolerance = 1.0e-13_dp, fraction_dof_mp
! endNeverComplex

    integer,dimension(:), allocatable :: seed

  continue

    if ( composite_jacobian_seed /= 1 ) then
      call random_seed(size=size1)  !finding required size of seed
      allocate(seed(size1))
      seed(1) = composite_jacobian_seed
      call random_seed(put=seed)
    endif
    total = 0

    if ( soln%dofg <= 125 ) then
      composite_jacobian_fraction = 1.01_dp
      composite_jacobian_seed     = 0
    elseif ( composite_jacobian_fraction < 0._dp ) then
    elseif ( composite_jacobian_fraction < default_tolerance ) then
      composite_jacobian_fraction = 125._dp/soln%dofg
    endif

    if ( lmpi_master ) write(*,*) ' Jacobians examined randomly if random&
    &  number < composite_jacobian_fraction=',composite_jacobian_fraction

    !...reset some values to accomodate complex side
    !   which doesn't allow restarts (May 2011)
    first_order_iterations = 1000
    composite_jacobian_lhs = .true.

    skeleton = 0 !turn off skeleton

    ierr = 0
    !if ( soln%eqn_set < 0 .or. soln%eqn_set > 1 ) then
    !  call lmpi_conditional_stop(1,'eqn_set:check_cmp_jacobians')
    !endif

    if ( lmpi_master ) then
      if ( complex_mode ) then
        call se_open( gfile1,file='jacobian.summary.complex',  &
                      form='formatted')
      else
        call se_open( gfile1,file='jacobian.summary',  &
                      form='formatted')
      endif
      rewind(gfile1)
    endif

    local_request = .false.
    dof           = 0
    gold_dof      = 0
    proc          = 0
    if ( lmpi_id == debug_q_proc ) then
      gold_dof  = debug_q_loc
      dof       = debug_q_loc
      proc      = debug_q_proc
    else
      debug_q_proc = -1
      debug_q_loc  =  0
    endif

    call lmpi_reduce(dof,it) ; call lmpi_bcast(it)

    if ( it > 0 ) then

      local_request = .true.
      call lmpi_reduce(dof, itemp1) ; call lmpi_bcast(itemp1)
      call lmpi_reduce(proc,itemp2) ; call lmpi_bcast(itemp2)

    elseif( lmpi_master ) then

      gold_dof     = 1
      debug_q_proc = lmpi_id
      debug_q_loc  = gold_dof

    endif

    if ( lmpi_id == debug_q_proc ) then
      if ( complex_mode ) then
        call se_open( gfile2,file='jacobian.local.complex',  &
                      form='formatted')
      else
        call se_open( gfile2,file='jacobian.local',  &
                      form='formatted')
      endif
      rewind(gfile2)
    endif

    call set_comprow_flow_rhs(grid, crow, 1)

    size1 = size(soln%res,1)
    size2 = size(soln%res,2)
    allocate(    base_res(size1,size2) )

    size1 = size(soln%turbres,1)
    size2 = size(soln%turbres,2)
    allocate(    base_turbres(size1,size2) )

    call frechet_setup(grid, soln, crow, mass, reset_q )

    if ( 0 == 1 ) call corrupt_jacobian( grid, soln, crow, gfile1 )

    base_res     = soln%res
    base_turbres = soln%turbres

    call header_composite_jacobian

    ! Upper bound for off-diagonals associated with lhs.

    call max_adj_off_diag(soln%dof0, crow, max_adj_lhs)

    if ( lmpi_master .and. check_crow ) &
    call check_crow_entries(grid, soln, crow)

    sets = 1
    set_start = 1
    if ( .not.tightly_couple ) then
      if ( soln%n_turb > 0) sets = 2
    endif

    if ( lmpi_master ) then
      write(*,*)
      write(*,*) ' Starting numerical Jacobians...starting set=',set_start
      write(*,*) '                             .....ending set=',sets
      if ( local_request ) then
        write(*,*) '   jacobian detail at requested dof location:'
        write(*,*) '     ...debug_q_proc=',itemp2
        write(*,*) '     ....debug_q_loc=',itemp1
        write(*,*) '   ...debug_q_global=',debug_q_global
      else
        write(*,*) '   jacobian detail at dof selected arbitrarily:'
        write(*,*) '     ...debug_q_proc=',debug_q_proc
        write(*,*) '     ....debug_q_loc=',debug_q_loc
      endif
    endif

    meanflow_turbulence : do set = set_start,sets

      call set_likely_flags()

      ! Set the type and number of jacobians to check
      if ( set == 1 .and. tightly_couple ) then
        jacobian_type='Meanflow+Turbulence'
        if (debug_dc) then
          njac = n_species
        else
          njac = soln%n_q
        end if
      elseif ( set == 1 ) then
        jacobian_type='Meanflow'
        if (debug_dc) then
          njac = n_species
        else
          njac = soln%n_q - soln%n_turb
        end if
      elseif ( set == 2 ) then
        njac = soln%n_turb
        jacobian_type = 'Turbulence'
      else
        ierr = 1
      endif

      call lmpi_conditional_stop(ierr,'set issue:check_cmp_jacobians')

      call header_set( soln%eqn_set, jacobian_type )

      loop_ip : do ip=0,lmpi_nproc-1

        ! Limit the number of jacobians checked.

        dof_mp   = soln%dof0  ; call lmpi_bcast( dof_mp,   ip )

        dof01_mp = size(soln%q_dof,2) ; call lmpi_bcast( dof01_mp, ip )

        !...allocate work arrays.
        allocate( skip_jac(dof_mp) )  !skip jacobian

        skip_jac    = .false.
        assessments = dof_mp
        if ( grid%origin > 2 ) then
          skip_jac = .true.
          if ( ip == lmpi_id .and. lmpi_id == debug_q_proc .and. &
               gold_dof /= 0 ) then
            skip_jac(gold_dof) = .false.
          endif
          call lmpi_bcast( skip_jac, ip )
        else
          if ( lmpi_id == ip ) then
            do dof=1,dof_mp
              if ( dof == gold_dof ) cycle
              call random_number(random)
              if ( random < composite_jacobian_fraction ) cycle
              skip_jac(dof) = .true.
              assessments   = assessments - 1
            enddo
          endif
          call lmpi_bcast( skip_jac, ip )
        endif

        call lmpi_bcast( assessments, ip )
        fraction_dof_mp = real( assessments, dp ) / real( dof_mp, dp )
        total = total + assessments

        nnz_mp = adj_rhs%nnz ; call lmpi_bcast( nnz_mp, ip )

        !...allocate work arrays.
        allocate( d_lhs(njac,njac,dof_mp) )  !global lhs:diag
        allocate( d_rhs(njac,njac,dof_mp) )  !global rhs:diag

        allocate(   r00(njac,     dof01_mp) ) !first residual
        allocate( a_lhs(njac,njac,  nnz_mp) ) !global lhs:off-diag
        allocate( a_rhs(njac,njac,  nnz_mp) ) !global rhs:off-diag

        if ( ip == lmpi_id ) then
          if ( set == 1 .and. tightly_couple ) then
            r00(:,:) = base_res(:,:)
          elseif ( set == 1 ) then
            r00(:,:) = base_res(:,:)
          else
            r00(1:njac,:) = base_turbres(1:njac,:)
          endif
        endif

        call lmpi_bcast( r00, ip )

        if ( ip == lmpi_id .and. lmpi_id == debug_q_proc .and. &
             gold_dof /= 0 ) then
          call write_gold_dof_info( grid, soln, crow, gfile2, gold_dof, &
                                    set, njac, base_res, base_turbres )
        endif

        call gfile1_header( gfile1, ip, set, sets,        &
                            assessments, fraction_dof_mp, total, reset_q )

        if ( composite_jacobian_rhs ) skip_jac = .true.

        ! Assemble matrices for rhs.

        if ( lmpi_id == ip ) &
        call reorder_lhs_m2g(soln, crow, set, njac, ierr )

        call lmpi_conditional_stop(ierr,'aj_sums zero:check_cmp_jacobians')

        ! Assemble matrices for rhs across all processors.

        call assemble_rhs( grid, soln, crow, mass, ip, &
                           set, njac, r00, skip_jac )

        ! Check Jacobians on master processor.

        call check_frechet( grid, soln, crow, ip, max_adj_lhs, njac,  &
                            gfile1, gfile2, gold_dof, jacobian_type )

        ! deallocate work arrays.
        deallocate(skip_jac) ; deallocate(r00)
        deallocate(d_lhs)    ; deallocate(d_rhs)
        deallocate(a_lhs)    ; deallocate(a_rhs)

      enddo loop_ip

    enddo meanflow_turbulence

    deallocate( base_res, base_turbres )

    if ( lmpi_master )             close(gfile1)
    if ( lmpi_id == debug_q_proc ) close(gfile2)

    if ( lmpi_master ) write(*,*) 'DONE!:check_cmp_jacobians'
    if ( complex_mode .and. lmpi_master ) then
      write(*,*) ' output file -> jacobian.summary.complex'
      write(*,*) ' output file -> jacobian.local.complex'
    elseif ( lmpi_master ) then
      write(*,*) ' output file -> jacobian.summary'
      write(*,*) ' output file -> jacobian.local'
    endif
  end subroutine check_cmp_jacobians

!============================== ASSEMBLE_RHS =================================80
!
! Assemble Jacobian matrices by divided differences.
!
!=============================================================================80

  subroutine assemble_rhs( grid, soln, crow, mass, ip,                         &
                           set, njac, r00, skip_jac )

    use stability_defs,  only : d_rhs, a_rhs, adj_rhs
    use generic_gas_map, only : n_species

    integer, intent(in) :: ip, set, njac

    type(grid_type),               intent(inout) :: grid
    type(soln_type),               intent(inout) :: soln
    type(crow_flow),               intent(in)    :: crow
    type(mass_type), dimension(:), intent(in)    :: mass
    real(dp), dimension(:,:),      intent(in)    :: r00
    logical,  dimension(:),        intent(in)    :: skip_jac

    integer :: i, j, k, dof, dof_off, dof_mp, ia1_mp, ia2_mp, ierr, jacobians

    real(dp), dimension(:), allocatable :: r1

    logical :: zero_twod_je

    real(dp) :: term

    integer :: debug_dof = 0, debug_dof_off = 0 !dof coupling

    integer :: debug_i   = 0, debug_j = 0       !equation coupling

    integer :: p_shift

    logical :: skip_mp

! beginNeverComplex
    real(dp) :: rj_sums
! endNeverComplex

  continue

    ierr = 0 ; jacobians = 0

    !debug_dof = 14   ; debug_dof_off = 0
      !debug_i = soln%n_q    ;       debug_j = soln%n_q

    if ( debug_dof > 0 ) then
      write(*,*)
      write(*,"(1x,3(a,i10))") 'Frechet.......debug_dof=',debug_dof
      write(*,"(1x,3(a,i10))") 'Frechet...debug_dof_off=',debug_dof_off
      write(*,"(1x,3(a,i10))") 'Frechet.........debug_i=',debug_i
      write(*,"(1x,3(a,i10))") 'Frechet.........debug_j=',debug_j
    endif

    ! Zero twod (node-centered 2D) meanflow Jacobians
    ! from variations in v because we neglect endcap fluxes.

    zero_twod_je = .false.
    if ( twod .and. set == 1 ) zero_twod_je = .true.

    rj_sums = 0.0_dp

    allocate(r1(njac))

    !shift perturbation for decoupled method
    p_shift = 0
    if(mean_decouple .and. .not. debug_dc) p_shift = n_species - 1

    dof_mp= soln%dof0; call lmpi_bcast( dof_mp, ip )

    loop_dofs : do dof = 1, dof_mp

      skip_mp = skip_jac(dof); call lmpi_bcast( skip_mp, ip )

      if ( skip_mp ) cycle

      jacobians = jacobians + 1

      if ( dof == debug_dof ) then
        write(*,*)
        write(*,"(1x,a,7e20.10)") 'Frechet.....r00(:)=',r00(:,dof)
      endif

      ! add a delta to each variable

      loop_eq : do j = 1, njac


        ! Perturb the jth component of the solution at dof n by delta.
        ! Compute residual and store in r1.

        if ( dof == debug_dof ) then
          write(*,*)
          write(*,"(1x,a,i0,1x,i0)")'Frechet perturbed Q at (j,dof)=',j,dof
        endif

        call get_cmp_residual( grid, soln, mass, crow, ip, set, &
                               dof, njac, r1, dof, j+p_shift)

        if ( lmpi_id == ip ) then

          ! finite difference or complex derivatives

          if ( complex_mode ) then
            do i = 1, njac
              call extract_imaginary_part(r1(i),term)
              d_rhs(i,j,dof) = term/complex_epsilon
            end do
          else
            do i = 1, njac
              term = r1(i) - r00(i,dof)
              d_rhs(i,j,dof) = term/real_epsilon
            end do

            if ( dof == debug_dof ) then
              write(*,*)
              write(*,"(1x,a,7e20.10)") 'Frechet    r1(:,j)=',r1(:)
              write(*,"(1x,a,7e20.10)") 'Frechet d_rhs(:,j)=',d_rhs(:,j,dof)
            endif
          end if

          if ( zero_twod_je ) then
            do i = 1, njac
              if ( i == n_momy ) cycle
              if ( j == n_momy ) d_rhs(i,j,dof) = 0._dp
            end do
          endif

          ! Accumulate residual Jacobian sums

          rj_sums = rj_sums + sum( abs( real( d_rhs(:,j,dof), dp ) ) )

        endif

        if ( j == debug_i ) call lmpi_conditional_stop(1,&
                                  'j=debug_i:assemble_rhs')

        ! Perturb off diagonal contributions.

        ia1_mp = adj_rhs%ia(dof)
        ia2_mp = adj_rhs%ia(dof+1)-1

        call lmpi_bcast( ia1_mp, ip )
        call lmpi_bcast( ia2_mp, ip )

        ia_loop : do k = ia1_mp, ia2_mp

          dof_off = adj_rhs%ja(k)
          call lmpi_bcast( dof_off,ip )

          if ( dof == debug_dof ) then
            write(*,*) 'Frechet..dof=',dof,' dof_off=',dof_off
          endif

          ! Perturb soln at dof_off and store residual in r1.

          call get_cmp_residual( grid, soln, mass, crow, ip, set, &
                                 dof, njac, r1, dof_off, j+p_shift )

          if ( lmpi_id == ip ) then

            ! finite difference or complex derivatives

            if ( complex_mode ) then
              do i = 1, njac
                call extract_imaginary_part(r1(i),term)
                a_rhs(i,j,k) = term/complex_epsilon
              end do
            else

              if ( dof == debug_dof .and. dof_off == debug_dof_off ) then
                write(*,*) 'Frechet=',r1(1),r00(1,dof)
              endif

              do i = 1, njac
                term = r1(i) - r00(i,dof)
                a_rhs(i,j,k) = term/real_epsilon
              end do
              if ( dof == debug_dof .and. dof_off == debug_dof_off ) then
                write(*,*) 'Frechet=',a_rhs(1,1,k)
              endif

            end if


            if ( zero_twod_je ) then
              do i = 1, njac
                if ( i == n_momy ) cycle
                if ( j == n_momy ) a_rhs(i,j,k) = 0._dp
              end do
            endif

            ! Accumulate residual Jacobian sums

            rj_sums = rj_sums + sum( abs( real( a_rhs(:,j,k), dp ) ) )

          endif

        end do ia_loop

      end do loop_eq

    end do loop_dofs

    deallocate(r1)

    if ( jacobians > 0 ) then
      if( ip == lmpi_id .and. rj_sums < 1.0e-11_dp ) then
        ierr = 1
        write(*,*) ' Assembling RHS...lmpi_id=',lmpi_id
        write(*,*) ' ...set=',set,' njac=',njac,' rj_sums=',rj_sums
        write(*,*) ' ...jacobians=',jacobians
      endif
    endif

    call lmpi_conditional_stop(ierr,'rj_sums zero:assemble_rhs')

  end subroutine assemble_rhs

!============================== LHS_RHS_CRS_SETUP ============================80
!
! Driver routine for setting up lhs and rhs jacobians.
!
!=============================================================================80

  subroutine lhs_rhs_crs_setup(grid, soln, crow, mass, set, r00, skip_jac)

    use stability_defs,   only : d_lhs, a_lhs, d_rhs, a_rhs
    use comprow,          only : set_comprow_flow_rhs
    use stability_defs,   only : adj_rhs

    integer,                       intent(in)    :: set
    type(grid_type),               intent(inout) :: grid
    type(soln_type),               intent(inout) :: soln
    type(crow_flow),               intent(inout) :: crow
    type(mass_type), dimension(:), intent(in)    :: mass
    real(dp), dimension(:,:),      intent(inout) :: r00
    logical,  dimension(:),        intent(in)    :: skip_jac

    integer :: njac, ierr

    logical :: reset_q

  continue

    ierr = 0

    call lmpi_conditional_stop(lmpi_nproc-1,'MPI in lhs_rhs_crs_setup')

    call set_comprow_flow_rhs(grid, crow)

    call check_crow_entries(grid, soln, crow)

    call frechet_setup(grid, soln, crow, mass, reset_q)

    skeleton = 0 !turn off skeleton

    ! Set the type and number of jacobians to check

    if ( set == 1 ) then
      njac = soln%n_tot
      if ( grid%cc ) njac = soln%n_q - soln%n_turb
    elseif ( set == 2 ) then
      njac = soln%n_turb
    else
      write(*,*) ' ...set=',set
      call lmpi_conditional_stop(1,'set issue:lhs_rhs_crs_setup')
    endif

    !...global lhs and rhs jacobians
    allocate(a_lhs(njac,njac,adj_rhs%nnz))
    allocate(a_rhs(njac,njac,adj_rhs%nnz))
    allocate(d_lhs(njac,njac,soln%dof0))
    allocate(d_rhs(njac,njac,soln%dof0))

    if ( set == 1 ) then
      r00(:,:) = soln%res(:,:)
    else
      r00(:,:) = soln%turbres(:,:)
    endif

    ! Assemble matrices for rhs.

    call reorder_lhs_m2g(soln, crow, set, njac, ierr )

    call lmpi_conditional_stop(ierr,'aj_sums zero:check_cmp_jacobians')

    ! Assemble matrices for rhs.

    call assemble_rhs( grid, soln, crow, mass,        &
                       0, set, njac, r00, skip_jac )

  end subroutine lhs_rhs_crs_setup

end module composite_jacobian

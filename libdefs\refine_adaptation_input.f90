module refine_adaptation_input

  use kinddefs,        only : dp

  implicit none

  private

  public :: adapt_project
  public :: adapt_cycles
  public :: adapt_post_quality
  public :: adapt_freezebl
  public :: adapt_freeze_outside_x_cylinder
  public :: adapt_load_balance
  public :: adapt_interpolate
  public :: adapt_library
  public :: adapt_bamg_geometry_format
  public :: adapt_bamg_command
  public :: adapt_fixed_fraction

  public :: ladapt_re_cell
  public :: ladapt_beta_grd
  public :: ladapt_ep0_grd
  public :: ladapt_fsh
  public :: ladapt_fstr
  public :: ladapt_max_distance
  public :: ladapt_fctrjmp
  public :: ladapt_jumpflag
  public :: ladapt_freq
  public :: ladapt_max
  public :: ladapt_g_limiter

  public :: sfadapt_fsbuffr
  public :: sfadapt_ceqinc
  public :: sfadapt_shkdtct
  public :: sfadapt_fsfrac0
  public :: sfadapt_fsfraci
  public :: sfadapt_threshold
  public :: sfadapt_grdspd

  public :: adapt_hessian_key
  public :: adapt_hessian_method
  public :: adapt_hessian_average_on_bound
  public :: adapt_max_anisotropy
  public :: adapt_max_edge_growth
  public :: adapt_max_edge_length
  public :: adapt_min_edge_length
  public :: adapt_output_tolerance, adapt_exponent
  public :: adapt_error_estimation
  public :: adapt_statistics
  public :: adapt_twod
  public :: adapt_feature_scalar_key
  public :: adapt_feature_scalar_form
  public :: adapt_export_feature_scalar_key
  public :: adapt_feature_length_exp

  public :: adapt_visualize_metric

  public :: adapt_complexity
  public :: adapt_gradation

  public :: adapt_smooth_surface

  public :: adapt_surface_movie
  public :: capri_model, capri_modeler, capri_URL
  public :: adapt_felisa_gri
  public :: adapt_felisa_unk
  public :: adapt_felisa_unkX
  public :: adapt_felisa_flow
  public :: adapt_verbose
  public :: adapt_debug

  public :: adapt_intersect_metric_in_time

  public :: adapt_metric_from_file
  public :: adapt_export_metric
  public :: adapt_current_h_method
  public :: adapt_current_h_gradation

  public :: adapt_node_dist_file

! CL options

! allow the refine adaptation mechanics to smooth surface node locations
  logical :: adapt_smooth_surface = .true.

! output project for adaptation
  character(len=256) :: capri_model = ""
  character(len=256) :: capri_modeler = ""
  character(len=256) :: capri_URL = ""

! flow file for feature adaption is from felisa solution.  qnode(5,:) is Mach
  logical :: adapt_felisa_gri  = .false.
  logical :: adapt_felisa_unk  = .false.
  logical :: adapt_felisa_unkX = .false.
  logical :: adapt_felisa_flow = .false.

! Options nml_adapt_mechanics

! output project for adaptation
  character(len=256) :: adapt_project

! distance to freeze nodes away from no-slip faces (-1.0 deactivates)
  real(dp) :: adapt_freezebl

! number of adaptation cycles
  integer :: adapt_cycles

! number of post adaptation quality improvement cycles
  integer :: adapt_post_quality

! to test new solution interpolation
! once implemented, this option will be removed and true will be default
  logical :: adapt_interpolate

! distance to freeze nodes away from x-axis (-1.0 deactivates)
  real(dp) :: adapt_freeze_outside_x_cylinder

! to test new load balancing option
! once implemented, this option will be removed and true will be default
  logical :: adapt_load_balance

! write periodic tecplot surface snapshots durring adaptation
  logical :: adapt_surface_movie

! adaptation library to call
  character(len=20) :: adapt_library = 'refine/one'

! BAMG geometry format
  character(len=20) :: adapt_bamg_geometry_format = 'msh'

! BAMG command, may include full path or command line arguments
  character(len=256) :: adapt_bamg_command = 'bamg'

! distance to freeze nodes away from x-axis (-1.0 deactivates)
  real(dp) :: adapt_fixed_fraction = -1.0_dp

! Options for nml_adapt_metric_construction

! output and feature based Hessian variable
!   allowed variables:  density, pressure, temp, mach, manufact
  character(len=20) :: adapt_hessian_key

! method used to construct Hessian, lsq, green, grad, kexact
  character(len=15) :: adapt_hessian_method

! reconstruct hessian on boundary from average of interior
  logical :: adapt_hessian_average_on_bound = .false.

! maxium adaptation anisotropic aspect ratio
  real(dp) :: adapt_max_anisotropy = 1.0e6_dp

! used to limit coarsening/refining.
!   will define absolute max value of edge growth
!   adapt_max_edge_growth = 1.0_dp will allow no coarsening
!   adapt_max_edge_length = -1.0_dp will allow any coarsening
!   adapt_min_edge_length = -1.0_dp will allow any refining
  real(dp) :: adapt_max_edge_growth
  real(dp) :: adapt_max_edge_length
  real(dp) :: adapt_min_edge_length

! adaptation error tolerance level
  real(dp) :: adapt_output_tolerance

! adaptation grid to have a target complexity
  real(dp) :: adapt_complexity

! Size gradation control parameter
  real(dp) :: adapt_gradation

! adaptation error tolerance level
  character(len=20) :: adapt_error_estimation

! adaptation error statistics method
  character(len=8) :: adapt_statistics

! treat the grid as twod to invoke 2d grid adaptation
  logical :: adapt_twod = .false.

! underrelaxation exponent for the adaptation grid metric
  real(dp) :: adapt_exponent

! flow quantity used for scalar difference term
!   allowed variables:  density, pressure, temp, mach, manufact
  character(len=20) :: adapt_feature_scalar_key

! method for computing scalar term.
!   none     : do not use scalar term -- use Hessian only
!   delta    : max delta across edges at each node
!   delta-l  : max (delta * edgeLength^exp)
!   ratio    : max p1/p2 across an edge
!   max      : max of values across an edge
  character(len=20) :: adapt_feature_scalar_form

! to plot it...
  character(len=20) :: adapt_export_feature_scalar_key
  character(len=20) :: adapt_visualize_metric

! how to estimate the current min h size
  character(len=20) :: adapt_current_h_method = 'edge'

! limit gradation in current min h size estimate
  real(dp) :: adapt_current_h_gradation = 1.5_dp

! exponent to use with delta-l, typically 0.5 or 1.0, 0.0 just gives delta
  real(dp) :: adapt_feature_length_exp

! echo norms to hunt NaNs
  logical :: adapt_verbose = .false.

! detailed error estimation debug info
  logical :: adapt_debug = .false.

  logical :: adapt_intersect_metric_in_time = .false.

! read metric from this file
  character(len=256) :: adapt_metric_from_file

! export metric for external grid adaptation tools
  logical :: adapt_export_metric = .false.

! read r-refinement total node distance moved from this file
  character(len=256) :: adapt_node_dist_file

! target surface cell reynolds number
  real(dp) :: ladapt_re_cell

! exponential grid stretching factor
  real(dp) :: ladapt_beta_grd

! shock clustering parameter
  real(dp) :: ladapt_ep0_grd

! location of captured shock as fraction of distance between body
! and opposing boundary
  real(dp) :: ladapt_fsh

! approximate fraction of nodes in boundary layer
  real(dp) :: ladapt_fstr

! maximum distance outer boundary can be moved
  real(dp) :: ladapt_max_distance

! property ratio used to detect shock
  real(dp) :: ladapt_fctrjmp

! flag to define how shock is detected and/or grid is adapted
  integer  :: ladapt_jumpflag

! number of relaxation steps between line adaptations
  integer  :: ladapt_freq

! maximum number of calls to line adaptation
  integer  :: ladapt_max

! parameter to insure minimum mesh size does not get too big on a line to
! cause local skewing
  real(dp) :: ladapt_g_limiter
! shock fitting number of buffer nodes between shock and freestream boundary
  integer  :: sfadapt_fsbuffr

! shock fitting compatibility eq. influence coefficient.
  real(dp) :: sfadapt_ceqinc

! shock fitting shock-boundary interaction detector coefficient.
  real(dp) :: sfadapt_shkdtct

! shock fitting initial freestream boundary propagation velocity fraction.
  real(dp) :: sfadapt_fsfrac0

! shock fitting interaction freestream boundary propagation velocity fraction.
  real(dp) :: sfadapt_fsfraci

! shock fitting interaction threshold
  real(dp) :: sfadapt_threshold

! shock fitting grid motion treatment
  logical  :: sfadapt_grdspd

end module refine_adaptation_input

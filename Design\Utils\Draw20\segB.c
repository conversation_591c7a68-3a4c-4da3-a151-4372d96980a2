#include <stdio.h>
#include <math.h>
#include <stdlib.h>

#define maxcurve 100
#define maxSegments 200
#define maxElements 50


double fitsegmentLS (
         long            *order,
         long            *ncontrol,
         long             param,
         long             npts,
         double          *xcoord,
         double          *ycoord,
         long            *nknot,
         double         **knots,
         double         **xcontrol,
         double         **ycontrol,
         double          *segtval);

typedef struct seg{
   int numberOfPoints;    /* Number of points on each segment */
   int noControl;         /* Number of control points for this segment */
   int maxcontrol;        /* Max number of control points for segment  */
   int type;              /* 0=inviscid 1=viscous 2=far-field          */
   double *x;             /* x-coordinate for each point      */
   double *y;             /* y-coordinate for each point      */
   double *tval;          /* t-parameter (local for each segment)      */
}SEGMENT;
extern SEGMENT segment[maxSegments];

typedef struct elems{
   int noKnots;           /* Number of knots in the fitted knot sequence          */
   int noControl;         /* Number of control points for the fitted geometry     */
   int noSegments;        /* Number of segments for each element                  */
   int firstSegment;      /* The number of the first segment defining the element */
   int lastSegment;       /* The number of the last segment defining the element  */
   int order;             /* Order of the B-spline                                */
   int numEval;           /* Number of points on B-spline surface                 */
   int fitted;            /* If zero this has not been previously fit             */
   int maxdegree;         /* Maximum degree of polynomial for element min(npts-1) */
   int itranx, itrany;    /* If we are translating the element                    */
   int irotate;           /* If we are rotating the element                       */
   int parameterization;  /* Type 1=uniform 2=chord 3=centripital                 */
   int initialNum;        /* If reading design.data we need to know number of pts */
   double xrot, yrot;     /* Point of rotation for each element                   */
   double *knots;         /* knots for the element                                */
   double *xcontrol;      /* Position of x-control points                         */
   double *ycontrol;      /* Position of y-control points                         */
   double *xupper;        /* Maximum x-position for control points                */
   double *xlower;        /* Minimum x-position for control points                */
   double *yupper;        /* Maximum y-position for control points                */
   double *ylower;        /* Minimum y-position for control points                */
   double *xfitted;       /* x-position of points on fitted surface               */
   double *yfitted;       /* y-position of points on fitted surface               */
   double *xcontrolO;     /* Saved position of x-control points                   */
   double *ycontrolO;     /* Saved Position of y-control points                   */
   double *uvalue;        /* parameter for evaluating spline                      */
   double *uRead;         /* Parameter read in from existing design.data file     */
}ELEMENT;

extern ELEMENT element[maxElements];
extern int whichelement;

void segB(void);

void segB()
  {
  int ncurve=1;        /* Number of curves                  */
  int nseg;            /* Number of segments                */
  int icurve;          /* Curve counter                     */
  int iseg;            /* Segment counter                   */
  int firstsegment;    /* First segment on element          */
  int i;
  int istart, jstart;
  int totnknot;        /* Number of knots in sequence for the curve */
  int totncontrol;     /* Number of control points for curve */
  int igo;             /* Counter used to skip over points in a knot sequence */
  long npts;
  long order,ncontrol,param,nknot;
  double *xcoord,*ycoord;
  double *knots,*xcontrol,*ycontrol;
  double *finalKnots;         /* Final knots sequence for each curve */
  double *finalxc, *finalyc;  /* Final control points for each curve */
  double chi2;
  double tlast;

   printf ("Debug here\n");
   order = element[whichelement].order;
   nseg = element[whichelement].noSegments;
   firstsegment = element[whichelement].firstSegment;
/* Allocate some small initial size for storing the knot vector */
   finalKnots = (double *)calloc(1,sizeof(double));
   finalxc = (double *)calloc(1,sizeof(double));
   finalyc = (double *)calloc(1,sizeof(double));
/*  fscanf(IN," %d",&ncurve); kyle */
   ncurve = 1;
  printf(" There are %d curves\n",ncurve);
  for(icurve=0; icurve <ncurve; icurve++)
  {
    totnknot = 0; /* Keeps track of how many knots are in the knot sequence */
    totncontrol = 0; 
    istart = 0;   /* Keeps track of where in the knot sequence we are       */
    jstart = 0;   /* Keeps track of which control point                     */

/*    fscanf(IN," %d",&nseg); kyle this is brought in */

    printf(" For curve number %d there are %d segments\n",icurve,nseg);
/*    printf(" Enter the order for this curve\n"); kyle brought in */
/*    scanf(" %ld",&order); kyle brought in */

    tlast = 0.;
    for(iseg=0; iseg<nseg; iseg++)
    {
      npts = segment[firstsegment + iseg].numberOfPoints;

/*      fscanf(IN," %ld",&npts); kyle brought in */

      printf(" On Segment %d there are %ld points\n",iseg,npts);
      xcoord = (double *) calloc(npts, sizeof(double));
      ycoord = (double *) calloc(npts, sizeof(double));

      /* Fill in the coordinates of the points */
      for (i=0; i < npts; i++)
      {
         xcoord[i] = segment[firstsegment + iseg].x[i];
         ycoord[i] = segment[firstsegment + iseg].y[i];
/*         fscanf(IN,"%lg %lg",&xcoord[i],&ycoord[i]); kyle */
      }

/*      printf("Number of control points for this segment?\n"); kyle */
/*      scanf(" %ld",&ncontrol); kyle */

      ncontrol = segment[firstsegment + iseg].noControl;

      param = 3; /* Uniform parameterization */
      param = element[whichelement].parameterization + 1;
      chi2 = fitsegmentLS(&order,&ncontrol,param,npts,xcoord,ycoord,
                         &nknot,&knots,&xcontrol,&ycontrol,segment[firstsegment + iseg].tval);

      segment[firstsegment + iseg].noControl = ncontrol; /* Reset the number of control points in case */
                                                         /* fitsegementLS made some changes            */

      printf("chi2 = %lg\n",chi2);
      if(iseg == 0)
       {
         totnknot += nknot ;
         totncontrol += ncontrol;
       }
       else
       {
         totnknot += nknot -order ;
         totncontrol += ncontrol - 1;
       }

/* Realloc the size of the final knots and copy the current knots into them */
      if((finalKnots = (double *)realloc((double *)finalKnots,totnknot*sizeof(double))) == (double *)NULL)
      {
        printf("Reallocating knot sequence fails ");
        exit(3);
      }

/* Realloc the size of the control points */
      if((finalxc = (double *)realloc((double *)finalxc,totncontrol*sizeof(double))) == (double *)NULL)
      {
        printf("Reallocating finalxc sequence fails ");
        exit(3);
      }

      if((finalyc = (double *)realloc((double *)finalyc,totncontrol*sizeof(double))) == (double *)NULL)
      {
        printf("Reallocating finalyc sequence fails ");
        exit(3);
      }

      igo = 0;
      if( iseg != 0)igo = order;
      for(i=igo; i < nknot; i++)
      {
/*        finalKnots[istart++] = knots[i] + totnknot - igo; */
        finalKnots[istart++] = knots[i] + tlast;
      }
      tlast +=  knots[nknot-1];

      igo = 0;
      if( iseg != 0)igo = 1;
      for(i=igo; i < ncontrol; i++)
      {
        finalxc[jstart]   = xcontrol[i];
        finalyc[jstart++] = ycontrol[i];
      }

    } /* End of segment loop */

  /* Free memory for coordinates on segment */
     free(xcoord);
     free(ycoord);

/* Check to see if memory has already been alocated for the knots   */
/* and the splined coordinates. If not, allocate it. If so, free it */
/* first and then allocate it in case the size has changed.         */
    if(element[whichelement].fitted)
    {
      free(element[whichelement].knots);
      free(element[whichelement].xcontrol);
      free(element[whichelement].ycontrol);
      free(element[whichelement].xcontrolO);
      free(element[whichelement].ycontrolO);
      free(element[whichelement].xupper);
      free(element[whichelement].xlower);
      free(element[whichelement].yupper);
      free(element[whichelement].ylower);
      free(element[whichelement].xfitted);
      free(element[whichelement].yfitted);
    }
      fcalloc(totnknot, &(element[whichelement].knots));
      fcalloc(totncontrol, &(element[whichelement].xcontrol));
      fcalloc(totncontrol, &(element[whichelement].ycontrol));
      fcalloc(totncontrol, &(element[whichelement].xcontrolO));
      fcalloc(totncontrol, &(element[whichelement].ycontrolO));
      fcalloc(totncontrol, &(element[whichelement].xupper));
      fcalloc(totncontrol, &(element[whichelement].xlower));
      fcalloc(totncontrol, &(element[whichelement].yupper));
      fcalloc(totncontrol, &(element[whichelement].ylower));
      fcalloc(element[whichelement].numEval, &(element[whichelement].xfitted));
      fcalloc(element[whichelement].numEval, &(element[whichelement].yfitted));
      element[whichelement].fitted = 1;
   
   /* Write out information for curve */
   /* Scale the knot values by the last one so we go from 0 to 1 */
     element[whichelement].noKnots = totnknot;
     element[whichelement].noControl = totncontrol;
/*     fprintf(OUT,"%ld\n",totnknot); */
     for(i=0; i<totnknot; i++)
     {
/*       fprintf(OUT,"%lg\n",finalKnots[i]/finalKnots[totnknot-1]); kyle */
       element[whichelement].knots[i] = finalKnots[i]/finalKnots[totnknot-1];
     }
/*     fprintf(OUT,"%ld\n",totncontrol); */
     for (i=0; i < totncontrol; i++)
     {
/*       fprintf(OUT,"%lg %lg\n",finalxc[i],finalyc[i]); kyle */
        element[whichelement].xcontrol[i] = finalxc[i];
        element[whichelement].ycontrol[i] = finalyc[i];
        element[whichelement].xcontrolO[i] = finalxc[i];
        element[whichelement].ycontrolO[i] = finalyc[i];
        element[whichelement].xupper[i] = -990.0; /* Make the upper limit lower  */
        element[whichelement].xlower[i] =  990.0; /* than the lower limit. If we */
        element[whichelement].yupper[i] = -990.0; /* want "real" limits we will  */
        element[whichelement].ylower[i] =  990.0; /* set them by hand            */
     }

  } /* End of loop over curves */

  }

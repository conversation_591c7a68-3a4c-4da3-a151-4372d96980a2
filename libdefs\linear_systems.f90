module linear_systems

  use kinddefs,             only : dp
  use fun3d_maximums,       only : ngrid_max

  implicit none

  private

  public :: meanflow_sweeps, turbulence_sweeps, decoupled_sweeps
  public :: line_implicit, number_implicit_line_sets
  public :: linear_projection
  public :: monitor_eqn_group_relax, monitor_gcr
  public :: eqn_group_passes, eqn_group_work
  public :: residuals_mean, residuals_turb  !SP Note : hacked organization.
  public :: cfl_inf_meanflow, cfl_inf_turbulence
  public :: adaptive_cfl
  public :: current_mean_residual, maximum_mean_residual
  public :: current_turb_residual, maximum_turb_residual
  public :: dgmres_frechet

  integer :: meanflow_sweeps
  integer :: turbulence_sweeps
  integer :: decoupled_sweeps
  integer :: number_implicit_line_sets
  logical :: linear_projection
  logical :: monitor_eqn_group_relax
  integer :: monitor_gcr
  character(3) :: line_implicit

  integer, dimension(ngrid_max,2) :: eqn_group_passes = 0
  integer, dimension(ngrid_max,2) :: eqn_group_work   = 0
  integer, dimension(ngrid_max)   :: residuals_mean   = 0
  integer, dimension(ngrid_max)   :: residuals_turb   = 0

  real(dp) :: current_mean_residual = 1.0_dp     ! Current meanflow residual
  real(dp) :: maximum_mean_residual = 1.0e-12_dp ! Maximum meanflow residual
  real(dp) :: current_turb_residual = 1.0_dp     ! Current turb residual
  real(dp) :: maximum_turb_residual = 1.0e-12_dp ! Maximum turb residual

  logical :: cfl_inf_meanflow   = .false. ! Infinite CFL (meanflow)
  logical :: cfl_inf_turbulence = .false. ! Infiinte CFL (turbulence)

  logical :: adaptive_cfl  = .false.   ! Acct for pressure jumps in CFL comp.

  logical :: dgmres_frechet = .false. ! =T residuals computed via Frechet
                                      ! derivatives within dgmres

end module linear_systems

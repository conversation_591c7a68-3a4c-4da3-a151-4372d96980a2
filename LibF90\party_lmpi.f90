! Routines to set up data for message passing via mpi

module party_lmpi

  use kinddefs,        only : dp, system_i8
  use interp_defs,     only : basic_sendrecv_type

  implicit none

  private

  public :: party_lmpi_setup_mpi_sm
  public :: party_lmpi_setup_mpi_cc_sm

  public :: party_lmpi_writeme
  public :: party_lmpi_writeme_sm

  public :: pparty_write_stats, src

! sr1 has the send and recv data pairs for level-1 nodes
! sre has the send and recv data pairs for edges
! src has the send and recv data pairs for cells (cell-centered)

  type (basic_sendrecv_type), dimension(:), pointer :: sr1, sre, src

  logical, public :: ppdb_part = .true.  ! debug: partition file output comments

contains

!============================== PARTY_LMPI_WRITEME ===========================80
!
! Setup mpi send and recv arrays
!
!=============================================================================80

  subroutine party_lmpi_writeme(cc, iunit, ipart, nparts,                      &
                                nnodesg, g2l,                                  &
                                nedgeg, eg2l, iform,                           &
                                globalnnodes, concat_cell_g2l, g2l_map)


    logical,                     intent(in) :: iform, cc
    integer,                     intent(in) :: iunit, ipart, nparts
    integer,                     intent(in) :: nnodesg
    integer, dimension(nnodesg), intent(in) :: g2l
    integer,                     intent(in) :: nedgeg
    integer, dimension(nedgeg),  intent(in) :: eg2l
    integer,                     intent(in) :: globalnnodes
    integer, dimension(:),       intent(in) :: concat_cell_g2l
    integer, optional, dimension(globalnnodes), intent(in) :: g2l_map

    integer :: i,j1,j2

    logical :: present1

    integer, dimension(:), allocatable :: rtemp, stemp

  continue

    present1 = present(g2l_map)

    cc_or_not : if ( cc ) then

! Dummies for level-2 nodes, edges

      if ( iform ) then
        if (ppdb_part) write(iunit,*)'beg party_lmpi_writeme (sr1,src)1'
        write(iunit,*) nparts, sr1(ipart)%sendproc(nparts+1)-1,                &
                               sr1(ipart)%recvproc(nparts+1)-1,                &
                               1, 1,                                           &
                               1, 1,                                           &
                               src(ipart)%sendproc(nparts+1)-1,                &
                               src(ipart)%recvproc(nparts+1)-1
      else
        write(iunit) nparts, sr1(ipart)%sendproc(nparts+1)-1,                  &
                             sr1(ipart)%recvproc(nparts+1)-1,                  &
                             1, 1,                                             &
                             1, 1,                                             &
                             src(ipart)%sendproc(nparts+1)-1,                  &
                             src(ipart)%recvproc(nparts+1)-1
      endif

!     write out indicies for mpi info, note that cell numbers are
!     converted from global to local numbers here, while node numbers have
!     already been converted to local, since they were multiply-defined as
!     different local numbers on more than one processor - see hack in the
!     routine that sets the sr1 array

      if ( iform ) then
        if (ppdb_part) write(iunit,*)'beg party_lmpi_writeme (sr1,src)2'
        if (nparts > 1 ) then
          write(iunit,*)                                                       &
              (sr1(ipart)%sendproc(i),i=1,nparts+1),                           &
              (sr1(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit,*)                                                       &
              (sr1(ipart)%sendindex(i),i=1,sr1(ipart)%sendproc(nparts+1)-1),   &
              (sr1(ipart)%recvindex(i),i=1,sr1(ipart)%recvproc(nparts+1)-1)
          write(iunit,*)'beg dummy sr2'
          write(iunit,*) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sr2
          write(iunit,*) 1, 1                                 ! Dummy sr2
          write(iunit,*)'beg dummy sre'
          write(iunit,*) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sre
          write(iunit,*) 1, 1                                 ! Dummy sre
          write(iunit,*)'src%sp(1:np+1),src%rc(1:np+1)'
          write(iunit,*)                                                       &
            (src(ipart)%sendproc(i),i=1,nparts+1),                             &
            (src(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit,*)'beg concat ',size(concat_cell_g2l)
          write(iunit,*) (concat_cell_g2l(i),i=1,size(concat_cell_g2l))
          write(iunit,*)'beg concat_celg2l(src%sp,src%rp)',size(concat_cell_g2l)
          if (cc) then ! DANA ************* present(concat_cell_g2l)) then
             write(iunit,*)                                                    &
            (concat_cell_g2l(src(ipart)%sendindex(i)),                         &
                                          i=1,src(ipart)%sendproc(nparts+1)-1),&
            (concat_cell_g2l(src(ipart)%recvindex(i)),                         &
                                          i=1,src(ipart)%recvproc(nparts+1)-1)
          else ! CC
            write(iunit,*)"INFO nparts ",nparts,ipart
            write(iunit,*)"INFO sendproc src(ipart)%sendproc(nparts+1)-1",     &
                                         src(ipart)%sendproc(nparts+1)-1
            write(iunit,*)"INFO recvproc src(ipart)%recvproc(nparts+1)-1",     &
                                         src(ipart)%recvproc(nparts+1)-1
            write(iunit,*)"CC src(ipart)%si ",src(ipart)%sendproc(nparts+1)-1
            write(iunit,*)                                                     &
             (src(ipart)%sendindex(i), i=1,src(ipart)%sendproc(nparts+1)-1)
            write(iunit,*)"CC src(ipart)%ri ",src(ipart)%recvproc(nparts+1)-1
            write(iunit,*)                                                     &
             (src(ipart)%recvindex(i), i=1,src(ipart)%recvproc(nparts+1)-1)
          end if
        else
          write(iunit,*) -999
          write(iunit,*) -999
          write(iunit,*) -999
          write(iunit,*) -999
          write(iunit,*) -999
          write(iunit,*) -999
          write(iunit,*) -999
          write(iunit,*) -999
        end if
        if (ppdb_part) write(iunit,*)'End party_lmpi_writeme (sr1,sr2,sre) 2'
      else
        if (nparts > 1 ) then
          write(iunit)                                                         &
              (sr1(ipart)%sendproc(i),i=1,nparts+1),                           &
              (sr1(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit)                                                         &
              (sr1(ipart)%sendindex(i),i=1,sr1(ipart)%sendproc(nparts+1)-1),   &
              (sr1(ipart)%recvindex(i),i=1,sr1(ipart)%recvproc(nparts+1)-1)
          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sr2
          write(iunit) 1, 1                                 ! Dummy sr2
          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sre
          write(iunit) 1, 1                                 ! Dummy sre
          write(iunit)                                                         &
            (src(ipart)%sendproc(i),i=1,nparts+1),                             &
            (src(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit)                                                         &
            (concat_cell_g2l(src(ipart)%sendindex(i)),                         &
                                          i=1,src(ipart)%sendproc(nparts+1)-1),&
            (concat_cell_g2l(src(ipart)%recvindex(i)),                         &
                                          i=1,src(ipart)%recvproc(nparts+1)-1)
        else
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
        end if
      endif

    else cc_or_not

      if (iform) then
        if (ppdb_part) write(iunit,*)'beg party_lmpi_writeme (sr1,sr2,sre)'

        write(iunit,'(i10)') nparts, sr1(ipart)%sendproc(nparts+1)-1,          &
                               sr1(ipart)%recvproc(nparts+1)-1,                &
                               1,1,                                            &
                              !sr2(ipart)%sendproc(nparts+1)-1,                &
                              !sr2(ipart)%recvproc(nparts+1)-1,                &
                               sre(ipart)%sendproc(nparts+1)-1,                &
                               sre(ipart)%recvproc(nparts+1)-1,                &
                               1,1     ! dummies for cells
      else
        write(iunit) nparts, sr1(ipart)%sendproc(nparts+1)-1,                  &
                             sr1(ipart)%recvproc(nparts+1)-1,                  &
                             1,1,                                              &
                            !sr2(ipart)%sendproc(nparts+1)-1,                  &
                            !sr2(ipart)%recvproc(nparts+1)-1,                  &
                             sre(ipart)%sendproc(nparts+1)-1,                  &
                             sre(ipart)%recvproc(nparts+1)-1,                  &
                             1,1     ! dummies for cells
      end if

      if (nparts > 1 ) then

!     write out indicies for mpi info, note that node and edge number are
!     converted from global to local numbers here

        if (iform) then

          if(ppdb_part)write(iunit,*)'beg sr1'
          write(iunit,'(i10)')                                                 &
            (sr1(ipart)%sendproc(i),i=1,nparts+1),                             &
            (sr1(ipart)%recvproc(i),i=1,nparts+1)

           if (present1) then

             j1 = sr1(ipart)%sendproc(nparts+1)-1
             allocate(stemp(j1))
             stemp(1:j1) = g2l_map(sr1(ipart)%sendindex(1:j1))

             j2 = sr1(ipart)%recvproc(nparts+1)-1
             allocate(rtemp(j2))
             rtemp(1:j2) = g2l_map(sr1(ipart)%recvindex(1:j2))

             write(iunit,'(i10)') (stemp(i),i=1,j1), (rtemp(i),i=1,j2)
             deallocate(stemp, rtemp)

           else
            write(iunit,'(i10)')                                               &
            (g2l(sr1(ipart)%sendindex(i)),i=1,sr1(ipart)%sendproc(nparts+1)-1),&
            (g2l(sr1(ipart)%recvindex(i)),i=1,sr1(ipart)%recvproc(nparts+1)-1)
           end if
          if(ppdb_part)write(iunit,*)'end sr1'

          if(ppdb_part)write(iunit,*)'beg sr2'
          write(iunit,'(i10)')                                                 &
            (1,i=1,nparts+1), (1,i=1,nparts+1)
           !(sr2(ipart)%sendproc(i),i=1,nparts+1),                             &
           !(sr2(ipart)%recvproc(i),i=1,nparts+1)

           if (present1) then

             !j1 = sr2(ipart)%sendproc(nparts+1)-1
             !allocate(stemp(j1))
             !stemp(1:j1) = g2l_map(sr2(ipart)%sendindex(1:j1))

             !j2 = sr2(ipart)%recvproc(nparts+1)-1
             !allocate(rtemp(j2))
             !rtemp(1:j2) = g2l_map(sr2(ipart)%recvindex(1:j2))

             write(iunit,'(i10)') 1,1 ! (stemp(i),i=1,j1), (rtemp(i),i=1,j2)
             !deallocate(stemp, rtemp)
           else
            write(iunit,'(i10)')                                               &
              1,1
           !(g2l(sr2(ipart)%sendindex(i)),i=1,sr2(ipart)%sendproc(nparts+1)-1),&
           !(g2l(sr2(ipart)%recvindex(i)),i=1,sr2(ipart)%recvproc(nparts+1)-1)
           end if
          if(ppdb_part)write(iunit,*)'end sr2'

          if(ppdb_part)write(iunit,*)'beg sre'
          write(iunit,'(i10)')                                                 &
            (sre(ipart)%sendproc(i),i=1,nparts+1),                             &
            (sre(ipart)%recvproc(i),i=1,nparts+1)

          write(iunit,'(i10)')                                                 &
           (eg2l(sre(ipart)%sendindex(i)),i=1,sre(ipart)%sendproc(nparts+1)-1),&
           (eg2l(sre(ipart)%recvindex(i)),i=1,sre(ipart)%recvproc(nparts+1)-1)
          if(ppdb_part)write(iunit,*)'end sre'

          if(ppdb_part)write(iunit,*)'beg src'    ! Dummy cell stuff
          write(iunit,'(i10)') (1, i=1,nparts+1), (1, i=1,nparts+1)
          write(iunit,'(i10)') 1, 1
          if(ppdb_part)write(iunit,*)'end src'

        else

          write(iunit)                                                         &
            (sr1(ipart)%sendproc(i),i=1,nparts+1),                             &
            (sr1(ipart)%recvproc(i),i=1,nparts+1)

           if (present1) then

             j1 = sr1(ipart)%sendproc(nparts+1)-1
             allocate(stemp(j1))
             stemp(1:j1) = g2l_map(sr1(ipart)%sendindex(1:j1))

             j2 = sr1(ipart)%recvproc(nparts+1)-1
             allocate(rtemp(j2))
             rtemp(1:j2) = g2l_map(sr1(ipart)%recvindex(1:j2))

             write(iunit) (stemp(i),i=1,j1), (rtemp(i),i=1,j2)
             deallocate(stemp, rtemp)

           else
            write(iunit)                                                       &
            (g2l(sr1(ipart)%sendindex(i)),i=1,sr1(ipart)%sendproc(nparts+1)-1),&
            (g2l(sr1(ipart)%recvindex(i)),i=1,sr1(ipart)%recvproc(nparts+1)-1)
           end if

          write(iunit)                                                         &
            1,1
           !(sr2(ipart)%sendproc(i),i=1,nparts+1),                             &
           !(sr2(ipart)%recvproc(i),i=1,nparts+1)

           if (present1) then

             !j1 = sr2(ipart)%sendproc(nparts+1)-1
             !allocate(stemp(j1))
             !stemp(1:j1) = g2l_map(sr2(ipart)%sendindex(1:j1))

             !j2 = sr2(ipart)%recvproc(nparts+1)-1
             !allocate(rtemp(j2))
             !rtemp(1:j2) = g2l_map(sr2(ipart)%recvindex(1:j2))

             write(iunit) 1,1 ! (stemp(i),i=1,j1), (rtemp(i),i=1,j2)
             deallocate(stemp, rtemp)

           else
            write(iunit)                                                       &
            1,1
           !(g2l(sr2(ipart)%sendindex(i)),i=1,sr2(ipart)%sendproc(nparts+1)-1),&
           !(g2l(sr2(ipart)%recvindex(i)),i=1,sr2(ipart)%recvproc(nparts+1)-1)
           end if

          write(iunit)                                                         &
            (sre(ipart)%sendproc(i),i=1,nparts+1),                             &
            (sre(ipart)%recvproc(i),i=1,nparts+1)

          write(iunit)                                                         &
           (eg2l(sre(ipart)%sendindex(i)),i=1,sre(ipart)%sendproc(nparts+1)-1),&
           (eg2l(sre(ipart)%recvindex(i)),i=1,sre(ipart)%recvproc(nparts+1)-1)

          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy cell stuff
          write(iunit) 1, 1           ! Dummy cell stuff

        end if

      else

!     write out dummy info

        if (iform) then
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
        else
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
        end if

      end if

    endif cc_or_not

    if (ppdb_part.and.iform) write(iunit,*)'end party_lmpi_writeme'

  end subroutine party_lmpi_writeme

!============================== PARTY_LMPI_WRITEME_SM ========================80
!
! Setup mpi send and recv arrays
!
!=============================================================================80

  subroutine party_lmpi_writeme_sm(iunit, grid, iform, nparts, concat_cell_g2l)

    use info_depr,  only : cc_primal
    use grid_types, only : grid_type

    integer,                         intent(in) :: iunit, nparts
    logical,                         intent(in) :: iform
    type(grid_type),                 intent(in) :: grid
    integer, dimension(:), optional, intent(in) :: concat_cell_g2l

! local

    integer :: ipart, i

  continue

    ipart = grid%partid

    cc_or_not : if ( cc_primal ) then

! Dummies for level-2 nodes, edges

      if ( iform ) then
        if (ppdb_part) write(iunit,*)'Beg party_lmpi_writeme (sr1,src) 1'
        write(iunit,*)'Beg party_lmpi_writeme (sr1,src) 1 ',associated(sr1)
        if (associated(sr1).and.associated(src)) then
           write(iunit,*) nparts, sr1(ipart)%sendproc(nparts+1)-1,             &
                               sr1(ipart)%recvproc(nparts+1)-1,                &
                               1, 1,                                           &
                               1, 1,                                           &
                               src(ipart)%sendproc(nparts+1)-1,                &
                               src(ipart)%recvproc(nparts+1)-1
        else
           write(iunit,*) "Associated sr1,src ",associated(sr1),associated(src)
           return
        end if
        if (ppdb_part) write(iunit,*)'End party_lmpi_writeme (sr1,sr2,sre) 1'
      else
        if (associated(sr1).and.associated(src))                               &
        write(iunit) nparts, sr1(ipart)%sendproc(nparts+1)-1,                  &
                             sr1(ipart)%recvproc(nparts+1)-1,                  &
                             1, 1,                                             &
                             1, 1,                                             &
                             src(ipart)%sendproc(nparts+1)-1,                  &
                             src(ipart)%recvproc(nparts+1)-1
      endif

!     write out indicies for mpi info, note that cell numbers are
!     converted from global to local numbers here, while node numbers have
!     already been converted to local, since they were multiply-defined as
!     different local numbers on more than one processor - see hack in the
!     routine that sets the sr1 array

      if ( iform ) then
        if (ppdb_part) write(iunit,*)'beg party_lmpi_writeme (sr1,sr2,sre) 2a'
        if (nparts > 1 ) then
          write(iunit,*)                                                       &
              (sr1(ipart)%sendproc(i),i=1,nparts+1),                           &
              (sr1(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit,*)                                                       &
              (sr1(ipart)%sendindex(i),i=1,sr1(ipart)%sendproc(nparts+1)-1),   &
              (sr1(ipart)%recvindex(i),i=1,sr1(ipart)%recvproc(nparts+1)-1)
          write(iunit,*) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sr2
          write(iunit,*) 1, 1                                 ! Dummy sr2
          write(iunit,*) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sre
          write(iunit,*) 1, 1                                 ! Dummy sre
          write(iunit,*) 'Src%sp(1:np+1),src%rc(1:np+1)'
          write(iunit,*)                                                       &
            (src(ipart)%sendproc(i),i=1,nparts+1),                             &
            (src(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit,*)"present(concat_cell_cg2l ",present(concat_cell_g2l)
          if (present(concat_cell_g2l)) then
             write(iunit,*)                                                    &
             (concat_cell_g2l(src(ipart)%sendindex(i)),                        &
                                          i=1,src(ipart)%sendproc(nparts+1)-1),&
             (concat_cell_g2l(src(ipart)%recvindex(i)),                        &
                                          i=1,src(ipart)%recvproc(nparts+1)-1)
          else ! CC
            write(iunit,*)"INFO nparts ",nparts,ipart
            write(iunit,*)"INFO sendproc src(ipart)%sendproc(nparts+1)-1",     &
                                         src(ipart)%sendproc(nparts+1)-1
            write(iunit,*)"INFO recvproc src(ipart)%recvproc(nparts+1)-1",     &
                                         src(ipart)%recvproc(nparts+1)-1
            write(iunit,*)"CC src(ipart)%si ",src(ipart)%sendproc(nparts+1)-1
            write(iunit,*)                                                     &
             (src(ipart)%sendindex(i), i=1,src(ipart)%sendproc(nparts+1)-1)
            write(iunit,*)"CC src(ipart)%ri ",src(ipart)%recvproc(nparts+1)-1
            write(iunit,*)                                                     &
             (src(ipart)%recvindex(i), i=1,src(ipart)%recvproc(nparts+1)-1)
          end if
        else
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
        end if
        if (ppdb_part) write(iunit,*)'End party_lmpi_writeme (sr1,sr2,sre) 2'
      else
        if (nparts > 1 ) then
          write(iunit)                                                         &
              (sr1(ipart)%sendproc(i),i=1,nparts+1),                           &
              (sr1(ipart)%recvproc(i),i=1,nparts+1)
          write(iunit)                                                         &
              (sr1(ipart)%sendindex(i),i=1,sr1(ipart)%sendproc(nparts+1)-1),   &
              (sr1(ipart)%recvindex(i),i=1,sr1(ipart)%recvproc(nparts+1)-1)
          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sr2
          write(iunit) 1, 1                                 ! Dummy sr2
          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sre
          write(iunit) 1, 1                                 ! Dummy sre
          write(iunit)                                                         &
            (src(ipart)%sendproc(i),i=1,nparts+1),                             &
            (src(ipart)%recvproc(i),i=1,nparts+1)
          if (present(concat_cell_g2l)) then
             write(iunit)                                                      &
               (concat_cell_g2l(src(ipart)%sendindex(i)),                      &
                                          i=1,src(ipart)%sendproc(nparts+1)-1),&
               (concat_cell_g2l(src(ipart)%recvindex(i)),                      &
                                          i=1,src(ipart)%recvproc(nparts+1)-1)
          else ! CC
            write(iunit)                                                       &
             (src(ipart)%sendindex(i), i=1,src(ipart)%sendproc(nparts+1)-1),   &
             (src(ipart)%recvindex(i), i=1,src(ipart)%recvproc(nparts+1)-1)
          end if
        else
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
        end if
      endif

    else cc_or_not

      if (iform) then
        if (ppdb_part) write(iunit,*)'beg party_lmpi_writeme'

        write(iunit,'(i10)') nparts, sr1(ipart)%sendproc(nparts+1)-1,          &
                               sr1(ipart)%recvproc(nparts+1)-1,                &
                               1,1,1,1,1,1 ! sr2, sre, cells for cells
      else
        write(iunit) nparts, sr1(ipart)%sendproc(nparts+1)-1,                  &
                             sr1(ipart)%recvproc(nparts+1)-1,                  &
                             1,1,1,1,1,1 ! sr2, sre, cells
      end if

      if (nparts > 1 ) then

!     write out indicies for mpi info, note that node and edge number are
!     converted from global to local numbers here

        if (iform) then

          if(ppdb_part)write(iunit,*)'beg sr1'
          write(iunit,'(i10)')                                                 &
            (sr1(ipart)%sendproc(i),i=1,nparts+1),                             &
            (sr1(ipart)%recvproc(i),i=1,nparts+1)
            write(iunit,'(i10)')                                               &
            (sr1(ipart)%sendindex(i),i=1,sr1(ipart)%sendproc(nparts+1)-1),     &
            (sr1(ipart)%recvindex(i),i=1,sr1(ipart)%recvproc(nparts+1)-1)
          if(ppdb_part)write(iunit,*)'end sr1'

          if(ppdb_part)write(iunit,*)'beg sr2'
          write(iunit,'(i10)') (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sr2
          write(iunit,'(i10)') 1, 1                                 ! Dummy sr2
          if(ppdb_part)write(iunit,*)'end sr2'

          if(ppdb_part)write(iunit,*)'beg sre'
          write(iunit,'(i10)') (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sre
          write(iunit,'(i10)') 1, 1                                 ! Dummy sre
          if(ppdb_part)write(iunit,*)'end sre'

          if(ppdb_part)write(iunit,*)'beg src'    ! Dummy cell stuff
          write(iunit,'(i10)') (1, i=1,nparts+1), (1, i=1,nparts+1)
          write(iunit,'(i10)') 1, 1
          if(ppdb_part)write(iunit,*)'end src'

        else

          write(iunit)                                                         &
            (sr1(ipart)%sendproc(i),i=1,nparts+1),                             &
            (sr1(ipart)%recvproc(i),i=1,nparts+1)
            write(iunit)                                                       &
            (sr1(ipart)%sendindex(i),i=1,sr1(ipart)%sendproc(nparts+1)-1),     &
            (sr1(ipart)%recvindex(i),i=1,sr1(ipart)%recvproc(nparts+1)-1)

          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sr2
          write(iunit) 1, 1                                 ! Dummy sr2

          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy sre
          write(iunit) 1, 1                                 ! Dummy sre

          write(iunit) (1, i=1,nparts+1), (1, i=1,nparts+1) ! Dummy cell stuff
          write(iunit) 1, 1           ! Dummy cell stuff

        end if

      else

!     write out dummy info

        if (iform) then
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
          write(iunit,'(i10)') -999
        else
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
          write(iunit) -999
        end if

      end if

    endif cc_or_not

    if (ppdb_part.and.iform) write(iunit,*)'end party_lmpi_writeme_sm'

  end subroutine party_lmpi_writeme_sm

!================================ PARTY_LMPI_SETUP_MPI_SM ====================80
!
! Setup mpi send and recv arrays.  This version calls MPI to exchange info.
!
!=============================================================================80

  subroutine party_lmpi_setup_mpi_sm(grid)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_id, lmpi_conditional_stop
    use lmpi_app,   only : lmpi_copy, lmpi_xfer, lmpi_xferedge, lmpi_grid_level

    type(grid_type), intent(inout) :: grid

    integer :: i,ierr
    integer,            dimension(:), allocatable :: l2g_ipe, el2g_ipe, temp
    integer(system_i8) :: i8
    integer(system_i8), dimension(:), allocatable :: tempi8

    logical, parameter :: test_xfer = .true.

    continue

    if (grid%cc) call lmpi_conditional_stop(10,'party_lmpi_setup_mpi_sm')

    allocate(l2g_ipe(grid%nnodes01),stat=ierr)
    call lmpi_conditional_stop(ierr, "Allocate error l2g_ipe")
    l2g_ipe = 0

    allocate(el2g_ipe(grid%nedge),stat=ierr)
    call lmpi_conditional_stop(ierr,"Allocate erro el2g_ipe")
    el2g_ipe = 0
    call set_l2g_ipe2(grid, l2g_ipe, el2g_ipe)
    call party_lmpi_setup_level_mpi_sm2(grid,sr1,1,l2g_ipe,sre,el2g_ipe)
    deallocate(el2g_ipe)

    deallocate(l2g_ipe)
    call lmpi_copy(lmpi_grid_level(),sr1,sre)

! Test the xfers

    if (test_xfer) then

! First the nodes

      allocate(temp(grid%nnodes01))
      temp = -grid%l2g
      call lmpi_xfer(temp)
      ierr = 0
      do i = 1,grid%nnodes01
        if (iabs(temp(i)) /= grid%l2g(i)) then
          write(*,*)lmpi_id,i,grid%l2g(i),temp(i)
          ierr = 1
          exit
        end if
      end do
      deallocate(temp)
      call lmpi_conditional_stop(ierr,"xfer1")

! Now the edges

      allocate(tempi8(grid%nedge))
      tempi8 = -grid%el2g
      call lmpi_xferedge(tempi8)
      ierr = 0
      check : do i = 1,grid%nedge
        i8 = tempi8(i); if (i8 < 0) i8 = -i8
        if (i8 /= grid%el2g(i)) then
          write(*,*)lmpi_id,i,grid%el2g(i),i8
          ierr = 1
          exit
        end if
      end do check
      call lmpi_conditional_stop(ierr,'xfer edges')

      deallocate(tempi8)

    end if

  end subroutine party_lmpi_setup_mpi_sm

!================================ PARTY_LMPI_SETUP_MPI_CC_SM =================80
!
! Setup mpi send and recv arrays. This version calls MPI to exchange info.
!
!=============================================================================80

  subroutine party_lmpi_setup_mpi_cc_sm(grid)

    use grid_types,  only : grid_type
    use lmpi,        only : lmpi_id, lmpi_conditional_stop, lmpi_master,       &
                            lmpi_nproc, lmpi_bcast, lmpi_allgather
    use lmpi_app,    only : lmpi_copy, lmpi_xfer, lmpi_xferedge, lmpi_grid_level
    use sort,        only : binary_search
    use interp_defs, only : sr

    type(grid_type), intent(inout) :: grid

    integer :: i,j,k, ierr, ipe, lmpi_idp1, irun
    integer :: ncell0, ncell1, ncells0, ncells1

    integer, dimension(:), allocatable :: l2g_ipe, el2g_ipe, temp, ncell1_arr
    integer, dimension(:), allocatable :: cl2g_ipe, icount, sp, rp, ct
    integer, dimension(:), allocatable :: temp0, temp1

    logical, parameter :: test_xfer = .true.

    continue

    if (.not.grid%cc) &
      call lmpi_conditional_stop(10,'party_lmpi_setup_mpi_cc_sm')

    !write(*,*)"ENTER party_lmpi_setup_mpi_cc_sm"

    lmpi_idp1 = lmpi_id + 1

    allocate(l2g_ipe(grid%nnodes01),stat=ierr)
    if (ierr /= 0) write(*,*)"Allocate error l2g_ipe ",lmpi_id,ierr
    call lmpi_conditional_stop(ierr)
    l2g_ipe = 0

    allocate(el2g_ipe(grid%nedge),stat=ierr)
    if (ierr /= 0) write(*,*)"Allocate error el2g_ipe ",lmpi_id,ierr
    call lmpi_conditional_stop(ierr)
    el2g_ipe = 0

    call set_l2g_ipe2(grid, l2g_ipe, el2g_ipe)
    call party_lmpi_setup_lvl_mpi_sm2_cc(grid,sr1,1,l2g_ipe,sre,el2g_ipe)

    deallocate(el2g_ipe)

    deallocate(l2g_ipe)

    call lmpi_copy(lmpi_grid_level(),sr1,sre) ! DANA CC

! Test the xfers

    if (.false..and.test_xfer) then

! First the nodes

      allocate(temp(grid%nnodes01))
      temp = -grid%l2g
      call lmpi_xfer(temp)
      ierr = 0
      do i = 1,grid%nnodes0
        if (iabs(temp(i)) /= grid%l2g(i)) then
          write(*,*)lmpi_id,i,grid%l2g(i),iabs(temp(i))
          ierr = 1
          exit
        end if
      end do
      deallocate(temp)
      call lmpi_conditional_stop(ierr,"xfer TESTING failed")

! Now the edges

      allocate(temp(grid%nedge))
      temp = -grid%el2g
      call lmpi_xferedge(temp)
      ierr = 0
      check : do i = 1,grid%nedge
        if (iabs(temp(i)) /= grid%el2g(i)) then
          write(*,*)lmpi_id,i,grid%el2g(i),temp(i)
          ierr = 1
          exit
        end if
      end do check
      call lmpi_conditional_stop(ierr)
      deallocate(temp)

    end if

! Compute CC SRC HERE

    if (lmpi_master) write(*,*)" ............. Computing src ............."

    ncells0 = sum(grid%elem(1:grid%nelem)%ncell0)
    ncells1 = sum(grid%elem(1:grid%nelem)%ncell) - ncells0
    allocate(cl2g_ipe(ncells1)); cl2g_ipe = 0
    call set_cl2g_ipe2(grid,ncells1,cl2g_ipe)
    allocate(icount(lmpi_nproc)); icount = 0
    do i = 1,ncells1
       ipe = cl2g_ipe(i)+1
       icount(ipe) = icount(ipe) + 1
    end do

    allocate(rp(lmpi_nproc+1)); rp = 0
    rp(1) = 1
    do i = 2,lmpi_nproc+1
       rp(i) = rp(i-1) + icount(i-1)
    end do

    allocate(sp(lmpi_nproc+1)); sp = 0
    sp(1) = 1
    allocate(temp(lmpi_nproc)); temp = 0
    do ipe = 0,lmpi_nproc-1
       if (ipe == lmpi_id) temp = icount
       call lmpi_bcast(temp,ipe)
       sp(ipe+2) = temp(lmpi_idp1)
    end do
    deallocate(icount)
    deallocate(temp)
    do i = 2,lmpi_nproc+1
       sp(i) = sp(i) + sp(i-1)
    end do

    allocate(src(lmpi_idp1:lmpi_idp1))
    allocate(src(lmpi_idp1)%sendproc(lmpi_nproc+1))
    src(lmpi_idp1)%sendproc = sp
    allocate(src(lmpi_idp1)%recvproc(lmpi_nproc+1))
    src(lmpi_idp1)%recvproc = rp
    i = sp(lmpi_nproc+1)-1; allocate(src(lmpi_idp1)%sendindex(i))
    src(lmpi_idp1)%sendindex = 0
    i = rp(lmpi_nproc+1)-1; allocate(src(lmpi_idp1)%recvindex(i))
    src(lmpi_idp1)%recvindex = 0
    deallocate(sp, rp)

    !write(*,'(" SP ",i0," : ",1000(i0,1x))')lmpi_id,src(lmpi_idp1)%sendproc
    !write(*,'(" RP ",i0," : ",1000(i0,1x))')lmpi_id,src(lmpi_idp1)%recvproc

! fill recvindex cc (order by PE, sorted within the PE subset)

    k = 0
    do j = 0,lmpi_nproc-1
       !write(4000+lmpi_id,*)'------------ BEG ',j,k,ncells1
       if (lmpi_id /= j) then
          do i = 1,ncells1
             if (cl2g_ipe(i) == j) then
                k = k + 1
                src(lmpi_idp1)%recvindex(k) = ncells0+i
                !write(4000+lmpi_id,*) src(lmpi_idp1)%recvindex(k),            &
                !                      grid%cl2g(src(lmpi_idp1)%recvindex(k))
             end if
          end do
       end if
       !write(4000+lmpi_id,*)'--------------- END ',j,k,ncells1
    end do

! fill sendindex cc

    allocate(ncell1_arr(0:lmpi_nproc-1)); ncell1_arr = 0
    call lmpi_allgather(ncells1,ncell1_arr)

    allocate(temp(maxval(ncell1_arr))); temp = 0
    allocate(ct(0:lmpi_nproc));      ct   = 0

    ncell0 = grid%ncell0
    allocate(temp0(ncell0)); temp0 = grid%cl2g(1:ncell0)

    ncell1 = grid%ncell01-grid%ncell0
    allocate(temp1(ncell1))
    temp1 = grid%cl2g(grid%ncell0+1:grid%ncell01)

   !write(*,*)"NC0,NC1 ",lmpi_id,ncell0,ncell1
   !do i = 1,ncell1
   !   write(3000+lmpi_id,*) temp1(i)
   !end do
   !do i = 1,ncell0
   !   write(3100+lmpi_id,*) temp0(i)
   !end do

    irun = 0
    do ipe = 0,lmpi_nproc-1
        temp = 0
        if (lmpi_id == ipe) then
           ct   = src(lmpi_idp1)%recvproc
           i    = ncell1_arr(ipe)
           temp(1:i) = grid%cl2g(src(lmpi_idp1)%recvindex(1:i))
       end if
        call lmpi_bcast(ct,ipe)
        call lmpi_bcast(temp,ipe)
        if (ct(lmpi_id) /= ct(lmpi_id+1)) then
           do i = ct(lmpi_id),ct(lmpi_id+1)-1
                j = binary_search(ncell0,temp0,temp(i))
                if (j == 0) then
                   j = binary_search(ncell1,temp1,temp(i))
                   if (j == 0) then
                      write(*,*)"NOT FOUND ",temp(i),j,ipe,lmpi_id
                      call lmpi_conditional_stop(1,"BAD BS 1a")
                   end if
                   j = j + ncell0
                end if
                irun = irun + 1
                src(lmpi_idp1)%sendindex(irun) = j
           end do
        end if
        call lmpi_conditional_stop(0,'BAD BS 1a')
    end do
    deallocate(ct,temp,ncell1_arr,temp0,temp1)

! Test the cc xfers

    if ((.false.).and.(test_xfer)) then
      allocate(temp(grid%ncell01)); temp = -grid%cl2g
      call lmpi_xfer(temp) ! TBD ,ghostlevel_arg=1)
      ierr = 0
      do i = 1,grid%ncell01
        if (iabs(temp(i)) /= grid%cl2g(i)) then
          write(2000+lmpi_id,*)" BAD xfer ",lmpi_id,i,grid%cl2g(i),iabs(temp(i))
          ierr = ierr + 1
        ! exit
        end if
      end do
      if (ierr /= 0) then
         write(*,*)"BAD xfer ",lmpi_id, ierr
         ierr = 1
      end if
      deallocate(temp)
     !call lmpi_conditional_stop(ierr,'BAD xfer')
    end if

    if (lmpi_nproc > 1) then

    ! src to sr (TBD replace setup to write directly to src)

       i = size(src(lmpi_idp1)%sendproc)
       allocate(sr(4,1)%sendproc(i))
       sr(4,1)%sendproc = src(lmpi_idp1)%sendproc

       i = size(src(lmpi_idp1)%recvproc)
       allocate(sr(4,1)%recvproc(i))
       sr(4,1)%recvproc = src(lmpi_idp1)%recvproc

       sr(4,1)%size_sendindex = size(src(lmpi_idp1)%sendindex)
       allocate(sr(4,1)%sendindex(sr(4,1)%size_sendindex))
       sr(4,1)%sendindex = src(lmpi_idp1)%sendindex

       sr(4,1)%size_recvindex = size(src(lmpi_idp1)%recvindex)
       allocate(sr(4,1)%recvindex(sr(4,1)%size_recvindex))
       sr(4,1)%recvindex = src(lmpi_idp1)%recvindex

    end if

  end subroutine party_lmpi_setup_mpi_cc_sm

!============================== SET_CL2G_IPE2 ================================80
!
! Set the processor for all nodes in cl2g. cl2g is unordered (i.e., cuthill).
!
!=============================================================================80

  subroutine set_cl2g_ipe2(grid,incells1,cl2g_ipe)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_nproc,lmpi_id,lmpi_bcast,lmpi_conditional_stop
    use sort,       only : binary_search, heap_sort

    type(grid_type),              intent(in)  :: grid
    integer,                      intent(in)  :: incells1
    integer, dimension(incells1), intent(out) :: cl2g_ipe

    integer :: i,j, ncells0, ncells1, ipe, jsize
    integer, dimension(:), allocatable :: temp0,temp1,pdata

  continue

! Determine the pe for level1 cells ()

    ncells0 = sum(grid%elem(1:grid%nelem)%ncell0)
    ncells1 = sum(grid%elem(1:grid%nelem)%ncell) - ncells0
    if (incells1 /= ncells1) call lmpi_conditional_stop(1,"ncells1 mismatch.")

!   allocate(temp0(ncells0)); temp0 = 0
!   allocate(temp1(ncells1)); temp1 = 0
!   ncells0 = 0
!   ncells1 = 0
!   do ielem = 1,grid%nelem
!      if (grid%elem(ielem)%ncell0 > 0) then
!         do icell = 1,grid%elem(ielem)%ncell0
!            ncells0 = ncells0 + 1
!            temp0(ncells0) = grid%elem(ielem)%cl2g(icell)
!         end do
!         do icell = grid%elem(ielem)%ncell0+1,grid%elem(ielem)%ncell
!            ncells1 = ncells1 + 1
!            temp1(ncells1) = grid%elem(ielem)%cl2g(icell)
!         end do
!      end if
!   end do

    allocate(temp0(ncells0)); temp0 = grid%cl2g(1:ncells0)
    allocate(temp1(ncells1)); temp1 = grid%cl2g(ncells0+1:size(grid%cl2g))

    call heap_sort(ncells0,temp0)

    cl2g_ipe = lmpi_nproc
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) jsize = ncells0
       call lmpi_bcast(jsize,ipe)
       if (jsize > 0) then
          allocate(pdata(jsize)); pdata = 0
          if (lmpi_id == ipe) pdata = temp0
          call lmpi_bcast(pdata,ipe)
          do i = 1,ncells1
             if (cl2g_ipe(i) == lmpi_nproc) then
                j = binary_search(jsize,pdata,temp1(i))
                if (j > 0) cl2g_ipe(i) = ipe
             end if
          end do
          deallocate(pdata)
       end if
    end do

    deallocate(temp0,temp1)

  end subroutine set_cl2g_ipe2

!============================== PARTY_LMPI_SETUP_LEVEL_MPI_SM2 ===============80
!
! Setup mpi send and recv arrays for each level of ghost nodes
!
!=============================================================================80

  subroutine party_lmpi_setup_level_mpi_sm2(grid,sr,level,l2g_ipe,sre,el2g_ipe)

    use lmpi,        only : lmpi_bcast, lmpi_id, lmpi_nproc, lmpi_die,         &
                            lmpi_gatherv, lmpi_gather, lmpi_conditional_stop
    use grid_types,  only : grid_type
    use sort,        only : binary_search, heap_sort

    type(grid_type),                         intent(in) :: grid
    type(basic_sendrecv_type), dimension(:), pointer    :: sr,sre
    integer,                                 intent(in) :: level
    integer, dimension(grid%nnodes01),       intent(in) :: l2g_ipe
    integer, dimension(grid%nedge),          intent(in) :: el2g_ipe

    integer :: myid, nparts, n0, n01
    integer :: i,j,k,node, is, ie, ipe, total_count, icount_out, my_count_out
    integer(system_i8) :: edge

    integer,            dimension(lmpi_nproc)     :: new_counts
    integer,            dimension(:), allocatable :: fwa,counts,l2g_ind,t1
    integer,            dimension(:), allocatable :: el2g_ind
    integer(system_i8), dimension(:), allocatable :: t8, temp, temp_out

    continue

    myid   = lmpi_id
    nparts = lmpi_nproc

! First the node stuff

    n0     = grid%nnodes0
    n01    = grid%nnodes01

    allocate(fwa(lmpi_nproc+1))

    allocate(sr(nparts))
    allocate(sr(lmpi_id+1)%recvproc(nparts+1)); sr(lmpi_id+1)%recvproc = 0
    allocate(sr(lmpi_id+1)%sendproc(nparts+1)); sr(lmpi_id+1)%sendproc = 0

    is = n0+1
    ie = n01
    if (level == 2) ie = grid%nnodes01

!-------------------------------------
! compute recvproc and recvindex

    allocate(counts(lmpi_nproc)); counts = 0
    do i = is,ie
       counts(l2g_ipe(i)+1) = counts(l2g_ipe(i)+1) + 1
    end do
    total_count = sum(counts)
    allocate(sr(myid+1)%recvindex(total_count))
    sr(myid+1)%recvindex = 0

    fwa = 0
    fwa(1) = 1
    do i = 2,lmpi_nproc+1
       fwa(i) = fwa(i-1) + counts(i-1)
    end do
    sr(myid+1)%recvproc = fwa

    do i = is,ie
       ipe = l2g_ipe(i)+1
       sr(myid+1)%recvindex(fwa(ipe)) = i
       fwa(ipe) = fwa(ipe) + 1
    end do

!---------------------------
! Create a sorted index into unsorted l2g

  allocate(l2g_ind(grid%nnodes01))
  call heap_sort(grid%nnodes01,grid%l2g,l2g_ind)
  allocate(t1(grid%nnodes01))
  t1 = grid%l2g(1:grid%nnodes01)
  call heap_sort(grid%nnodes01,t1)

! --------------------------
! compute sendproc and sendindex

    do ipe = 0,lmpi_nproc-1

       my_count_out = counts(ipe+1)
       new_counts   = 0
       call lmpi_gather(my_count_out,new_counts)
       call lmpi_bcast(new_counts)

       if (ipe == lmpi_id) then
          fwa = 0
          fwa(1) = 1
          do i = 2,lmpi_nproc+1
             fwa(i) = fwa(i-1) + new_counts(i-1)
          end do
          sr(myid+1)%sendproc = fwa
       end if

       total_count = sum(new_counts)
       allocate(temp(total_count))

       icount_out   = max(1,my_count_out)
       allocate(temp_out(icount_out)); temp_out = 0
       if (my_count_out > 0) then
          is = 1
          if (ipe > 0) is = sum(counts(1:ipe))+1
          ie = (is + my_count_out)-1
          temp_out = grid%l2g(sr(myid+1)%recvindex(is:ie))
       end if

       call lmpi_gatherv(temp_out, new_counts(lmpi_id+1), temp, new_counts)
       call lmpi_bcast(temp)

       if (lmpi_id == ipe) then
          allocate(sr(myid+1)%sendindex(total_count))
          do i = 1,total_count
             node = temp(i)
             j = binary_search(grid%nnodes01,t1,node)
            !j = 0
            !do k = 1,grid%nnodes0
            !   if (grid%l2g(k) == node) then
            !      j = k
            !      exit
            !   end if
            !end do
             if (j == 0) then
                write(*,*)'Bad BS(2) = ',lmpi_id,node,ipe,grid%nnodes0
                call lmpi_die
             end if
             sr(myid+1)%sendindex(i) = l2g_ind(j)
          end do
       end if
       deallocate(temp,temp_out)

   end do
   deallocate(l2g_ind,t1,fwa,counts)

!===========================================================================
! Now the edge stuff

    n0     = grid%nedgeloc
    n01    = grid%nedge

    allocate(fwa(lmpi_nproc+1)); fwa = 0

    allocate(sre(lmpi_id+1))
    allocate(sre(lmpi_id+1)%recvproc(nparts+1)); sre(lmpi_id+1)%recvproc = 0
    allocate(sre(lmpi_id+1)%sendproc(nparts+1)); sre(lmpi_id+1)%sendproc = 0

    is = n0+1
    ie = n01

!-------------------------------------
! compute recvproc and recvindex

    allocate(counts(lmpi_nproc)); counts = 0
    do i = is,ie
       counts(el2g_ipe(i)+1) = counts(el2g_ipe(i)+1) + 1
    !  write(500000+lmpi_id,*) grid%el2g(i),el2g_ipe(i)
    end do
    total_count = sum(counts)
    allocate(sre(myid+1)%recvindex(total_count))
    sre(myid+1)%recvproc = 0

    fwa = 0
    fwa(1) = 1
    do i = 2,lmpi_nproc+1
       fwa(i) = fwa(i-1) + counts(i-1)
    end do
    sre(myid+1)%recvproc = fwa

    do i = is,ie
       ipe = el2g_ipe(i)+1
       sre(myid+1)%recvindex(fwa(ipe)) = i
       fwa(ipe) = fwa(ipe) + 1
    end do

!---------------------------
! Create a sorted index into unsorted l2g

  allocate(el2g_ind(grid%nedgeloc))
  call heap_sort(grid%nedgeloc,grid%el2g,el2g_ind)
  allocate(t8(grid%nedge)); t8 = 0
  do i = 1,grid%nedgeloc
     t8(i) = grid%el2g(el2g_ind(i))
  end do

! --------------------------
! compute sendproc and sendindex

    k = 0
    do ipe = 0,lmpi_nproc-1
      !if ((lmpi_id == 0).and.(mod(ipe,500)==0))  &
      !   write(*,*)"party_lmpi_setup_level_mpi_sm2 S4",ipe
       my_count_out = counts(ipe+1)
       new_counts   = 0
       call lmpi_gather(my_count_out,new_counts)
       call lmpi_bcast(new_counts)

       if (ipe == lmpi_id) then
          fwa = 0
          fwa(1) = 1
          do i = 2,lmpi_nproc+1
             fwa(i) = fwa(i-1) + new_counts(i-1)
          end do
          sre(myid+1)%sendproc = fwa
       end if

       total_count = sum(new_counts)
       allocate(temp(total_count))

       icount_out   = max(1,my_count_out)
       allocate(temp_out(icount_out)); temp_out = 0
       if (my_count_out > 0) then
          is = 1
          if (ipe > 0) is = sum(counts(1:ipe))+1
          ie = (is + my_count_out)-1
          temp_out = grid%el2g(sre(myid+1)%recvindex(is:ie))
       end if

       call lmpi_gatherv(temp_out, new_counts(lmpi_id+1), temp, new_counts)
       call lmpi_bcast(temp)

       if (lmpi_id == ipe) then
          allocate(sre(myid+1)%sendindex(total_count))
          do i = 1,total_count
             edge = temp(i)
             j = binary_search(grid%nedgeloc,t8,edge)
             if (j == 0) then
                write(*,*)'Bad BS(3) = ',lmpi_id,edge,ipe,grid%nedgeloc
                k = 1
             end if
             sre(myid+1)%sendindex(i) = el2g_ind(j)
          end do
       end if
       deallocate(temp,temp_out)
   end do
   call lmpi_conditional_stop(k,'Bad BS(3)')
   deallocate(el2g_ind,t8,fwa,counts)

  end subroutine party_lmpi_setup_level_mpi_sm2

!============================== PARTY_LMPI_SETUP_LEVEL_MPI_SM2_CC ============80
!
! Setup mpi send and recv arrays for each level of ghost nodes
!
!=============================================================================80

  subroutine party_lmpi_setup_lvl_mpi_sm2_cc                                   &
    (grid,sr,level,l2g_ipe,sre,el2g_ipe)

    use lmpi,        only : lmpi_bcast, lmpi_id, lmpi_nproc, lmpi_die,         &
                            lmpi_gatherv, lmpi_gather
    use grid_types,  only : grid_type
    use sort,        only : binary_search, heap_sort

    type(grid_type),                         intent(in) :: grid
    type(basic_sendrecv_type), dimension(:), pointer    :: sr,sre
    integer,                                 intent(in) :: level
    integer, dimension(grid%nnodes01),       intent(in) :: l2g_ipe
    integer, dimension(grid%nedge),          intent(in) :: el2g_ipe

    integer :: myid, nparts, n0, n01, edge
    integer :: i,j,node, is, ie, ipe, total_count, icount_out, my_count_out

    integer, dimension(lmpi_nproc)     :: new_counts
    integer, dimension(:), allocatable :: fwa,counts,temp,temp_out,l2g_ind,t1
    integer, dimension(:), allocatable :: el2g_ind

    continue

    myid   = lmpi_id
    nparts = lmpi_nproc

! First the node stuff

    n0     = grid%nnodes0
    n01    = grid%nnodes01

    allocate(fwa(lmpi_nproc+1))

    allocate(sr(nparts))
    allocate(sr(lmpi_id+1)%recvproc(nparts+1)); sr(lmpi_id+1)%recvproc = 0
    allocate(sr(lmpi_id+1)%sendproc(nparts+1)); sr(lmpi_id+1)%sendproc = 0

    is = n0+1
    ie = n01
    if (level == 2) ie = grid%nnodes01

!-------------------------------------
! compute recvproc and recvindex

    allocate(counts(lmpi_nproc)); counts = 0
    do i = is,ie
       if (l2g_ipe(i) /= lmpi_id) &
       counts(l2g_ipe(i)+1) = counts(l2g_ipe(i)+1) + 1
    end do
    total_count = sum(counts)
    allocate(sr(myid+1)%recvindex(max(total_count,1)))
    sr(myid+1)%recvindex = 0

    fwa = 0
    fwa(1) = 1
    do i = 2,lmpi_nproc+1
       fwa(i) = fwa(i-1) + counts(i-1)
    end do
    sr(myid+1)%recvproc = fwa

    do i = is,ie
       ipe = l2g_ipe(i)+1
       if (ipe /= lmpi_id+1) then
          sr(myid+1)%recvindex(fwa(ipe)) = i
          fwa(ipe) = fwa(ipe) + 1
       end if
    end do

!---------------------------
! Create a sorted index into unsorted l2g

  allocate(l2g_ind(grid%nnodes01))
  call heap_sort(grid%nnodes01,grid%l2g,l2g_ind)
  allocate(t1(grid%nnodes01))
  t1 = grid%l2g(1:grid%nnodes01)
  call heap_sort(grid%nnodes01,t1)

! --------------------------
! compute sendproc and sendindex

    do ipe = 0,lmpi_nproc-1

       my_count_out = counts(ipe+1)
       new_counts   = 0
       call lmpi_gather(my_count_out,new_counts)
       call lmpi_bcast(new_counts)

       if (ipe == lmpi_id) then
          fwa = 0
          fwa(1) = 1
          do i = 2,lmpi_nproc+1
             fwa(i) = fwa(i-1) + new_counts(i-1)
          end do
          sr(myid+1)%sendproc = fwa
       end if

       total_count = sum(new_counts)
       allocate(temp(max(1,total_count)))

       icount_out   = max(1,my_count_out)
       allocate(temp_out(icount_out)); temp_out = 0
       if (my_count_out > 0) then
          is = 1
          if (ipe > 0) is = sum(counts(1:ipe))+1
          ie = (is + my_count_out)-1
          temp_out = grid%l2g(sr(myid+1)%recvindex(is:ie))
       end if

       call lmpi_gatherv(temp_out, new_counts(lmpi_id+1), temp, new_counts)
       call lmpi_bcast(temp)

       if (lmpi_id == ipe) then
          allocate(sr(myid+1)%sendindex(total_count))
          do i = 1,total_count
             node = temp(i)
             j = binary_search(grid%nnodes01,t1,node)
            !j = 0
            !do k = 1,grid%nnodes0
            !   if (grid%l2g(k) == node) then
            !      j = k
            !      exit
            !   end if
            !end do
             if (j == 0) then
                write(*,*)'Bad BS(2) = ',lmpi_id,node,ipe,grid%nnodes0
                call lmpi_die
             end if
             !write(9300+lmpi_id,*) "sr(",myid+1,"%sendindex(i) ",i,l2g_ind(j)
             sr(myid+1)%sendindex(i) = l2g_ind(j)
          end do
       end if
       deallocate(temp,temp_out)

   end do
   deallocate(l2g_ind,t1,fwa,counts)

! Now the edge stuff

    n0     = grid%nedgeloc
    n01    = grid%nedge

    allocate(fwa(lmpi_nproc+1))

    allocate(sre(lmpi_id+1))
    allocate(sre(lmpi_id+1)%recvproc(nparts+1)); sre(lmpi_id+1)%recvproc = 0
    allocate(sre(lmpi_id+1)%sendproc(nparts+1)); sre(lmpi_id+1)%sendproc = 0

    is = n0+1
    ie = n01

!-------------------------------------
! compute recvproc and recvindex

    allocate(counts(lmpi_nproc)); counts = 0
    do i = is,ie
       counts(el2g_ipe(i)+1) = counts(el2g_ipe(i)+1) + 1
    end do
    total_count = sum(counts)
    allocate(sre(myid+1)%recvindex(total_count))
    sre(myid+1)%recvproc = 0

    fwa = 0
    fwa(1) = 1
    do i = 2,lmpi_nproc+1
       fwa(i) = fwa(i-1) + counts(i-1)
    end do
    sre(myid+1)%recvproc = fwa

    do i = is,ie
       ipe = el2g_ipe(i)+1
       sre(myid+1)%recvindex(fwa(ipe)) = i
       fwa(ipe) = fwa(ipe) + 1
    end do

!---------------------------
! Create a sorted index into unsorted l2g

  allocate(el2g_ind(grid%nedgeloc))
  call heap_sort(grid%nedgeloc,grid%el2g,el2g_ind)
  allocate(t1(grid%nedge))
  t1 = grid%el2g(1:grid%nedgeloc)
  call heap_sort(grid%nedgeloc,t1)

! --------------------------
! compute sendproc and sendindex

    do ipe = 0,lmpi_nproc-1

       my_count_out = counts(ipe+1)
       new_counts   = 0
       call lmpi_gather(my_count_out,new_counts)
       call lmpi_bcast(new_counts)

       if (ipe == lmpi_id) then
          fwa = 0
          fwa(1) = 1
          do i = 2,lmpi_nproc+1
             fwa(i) = fwa(i-1) + new_counts(i-1)
          end do
          sre(myid+1)%sendproc = fwa
       end if

       total_count = sum(new_counts)
       allocate(temp(total_count))

       icount_out   = max(1,my_count_out)
       allocate(temp_out(icount_out)); temp_out = 0
       if (my_count_out > 0) then
          is = 1
          if (ipe > 0) is = sum(counts(1:ipe))+1
          ie = (is + my_count_out)-1
          temp_out = grid%el2g(sre(myid+1)%recvindex(is:ie))
       end if

       call lmpi_gatherv(temp_out, new_counts(lmpi_id+1), temp, new_counts)
       call lmpi_bcast(temp)

       if (lmpi_id == ipe) then
          allocate(sre(myid+1)%sendindex(total_count))
          do i = 1,total_count
             edge = temp(i)
             j = binary_search(grid%nedgeloc,t1,edge)
             if (j == 0) then
                write(*,*)'Bad BS(3) = ',lmpi_id,edge,ipe,grid%nedgeloc
                call lmpi_die
             end if
             sre(myid+1)%sendindex(i) = el2g_ind(j)
          end do
       end if
       deallocate(temp,temp_out)

   end do
   deallocate(el2g_ind,t1,fwa,counts)

  end subroutine party_lmpi_setup_lvl_mpi_sm2_cc


!============================== SET_L2G_IPE2 =================================80
!
! Set the processor for all nodes in l2g. l2g is unordered (i.e., cuthill).
! Also do the ghost edges.
!
!=============================================================================80

  subroutine set_l2g_ipe2(grid,l2g_ipe,el2g_ipe)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_nproc, lmpi_id, lmpi_bcast,                    &
                           lmpi_conditional_stop
    use sort,       only : heap_sort

    type(grid_type), intent(in) :: grid

    integer, dimension(grid%nnodes01), intent(out) :: l2g_ipe
    integer, dimension(grid%nedge),    intent(out) :: el2g_ipe

    integer :: i,n0,n12,n0_off,ipe,nleft,last,node,pe1,pe2

    integer, dimension(:), allocatable :: list,my_list,t1,t1_ind

  continue

    if (grid%cc) then
       call set_l2g_ipe2_cc(grid,l2g_ipe,el2g_ipe)
       return
    end if

! First do the node stuff

    n0 = grid%nnodes0
    n12 = grid%nnodes01-n0

    l2g_ipe = -1
    l2g_ipe(1:n0) = lmpi_id

    allocate(my_list(n0))
    my_list = grid%l2g(1:n0)
    call heap_sort(n0,my_list)

    allocate(t1(n12)); t1 = grid%l2g(grid%nnodes0+1:grid%nnodes01)
    allocate(t1_ind(n12))
    call heap_sort(n12,t1,t1_ind)
    call heap_sort(n12,t1)

    nleft = n12

    do ipe = 0,lmpi_nproc-1

       if (lmpi_id == ipe) n0_off = n0
       call lmpi_bcast(n0_off,ipe)

       allocate(list(n0_off)); list = 0
       if (lmpi_id == ipe) then
          list = my_list
          deallocate(my_list)
       end if
       call lmpi_bcast(list,ipe)

       if (nleft > 0) then
          last = 1
          out1: do i = 1,n0_off
             node = list(i)
    1        continue
             if (node < t1(last)) cycle
             if (node > t1(last)) then
                last = last + 1
                if (last > n12) exit out1
                goto 1
             end if
             l2g_ipe(n0+t1_ind(last)) = ipe
             nleft = nleft - 1
             last = last + 1
             if ((last > n12).or.(nleft == 0)) exit out1
          end do out1
       end if

       deallocate(list)

    end do

    deallocate(t1,t1_ind)

    if (nleft > 0) then
       write(*,*)"set_l2g_ipe2: missing nodes ",nleft
       call lmpi_conditional_stop(nleft)
    end if

    nleft = 0
    do i = n0+1,n12
       if (l2g_ipe(i) == -1) nleft = nleft + 1
    end do
    if (nleft > 0) then
       write(*,*)"set_l2g_ipe2: Missing nodes ",nleft
       call lmpi_conditional_stop(nleft)
    end if

!==========================================================================
! Now do the edge stuff

     do i = 1, grid%nedge
       pe1 = l2g_ipe(grid%eptr(1,i))
       pe2 = l2g_ipe(grid%eptr(2,i))
       el2g_ipe(i) = min(pe1,pe2)
     end do

  end subroutine set_l2g_ipe2

!============================== SET_L2G_IPE2_CC ==============================80
!
! Set the processor for all nodes in l2g. In cc, the nodes are really owned
! by any pe, so assign consistenly to one. (l2g is unordered.)
! Also do the ghost edges.
!
!=============================================================================80

  subroutine set_l2g_ipe2_cc(grid,l2g_ipe,el2g_ipe)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_nproc, lmpi_id, lmpi_bcast, lmpi_min,          &
                           lmpi_conditional_stop
    use sort,       only : heap_sort
    use kinddefs,   only : system_i1

    type(grid_type), intent(in) :: grid

    integer, dimension(grid%nnodes01), intent(out) :: l2g_ipe
    integer, dimension(grid%nedge),    intent(out) :: el2g_ipe

    integer :: i,j, n01,node,pe1,pe2, nleft, is, ie

    integer, dimension(:), allocatable :: t1,t1_ind, slice_in, slice_out
    integer(system_i1), dimension(:), allocatable :: tag

    integer, parameter :: nslice = 250000

  continue

! First do the node stuff

    n01 = grid%nnodes01
    allocate(t1_ind(n01)); t1_ind = 0
    allocate(t1    (n01)); t1     = grid%l2g
    call heap_sort(n01,t1,t1_ind)
    t1 = grid%l2g(t1_ind)
    allocate(tag(n01)); tag = 0

    do i = 1,grid%nnodesg,nslice
       is = i
       ie = (is+nslice)-1
       if (ie > grid%nnodesg) ie = grid%nnodesg
       allocate(slice_in(is:ie)); slice_in = lmpi_nproc+1
       tag = 0
       do j = 1,n01
          node = t1(j)
          if (node > ie) exit
          if ((node >= is).and.(node <= ie)) then
             slice_in(node) = lmpi_id
             tag(j) = 1
          end if
       end do
       allocate(slice_out(is:ie)); slice_out = lmpi_nproc+1
       call lmpi_min(slice_in, slice_out)
       deallocate(slice_in)
       call lmpi_bcast(slice_out)
       !if (lmpi_master) then
       !   do j = is,ie
       !      write(97000+lmpi_id,*) j,slice_out(j)
       !   end do
       !end if
       do j = 1,n01
          if (tag(j)==1) l2g_ipe(t1_ind(j)) = slice_out(t1(j))
       end do
       deallocate(slice_out)
    end do

    nleft = 0
    do i = 1,n01
       if (l2g_ipe(i) == -1) nleft = nleft + 1
    end do
    if (nleft > 0) then
       write(*,*)"set_l2g_ipe2: Missing nodes ",nleft
       call lmpi_conditional_stop(nleft)
    end if

    deallocate(tag,t1,t1_ind)

   !do i = 1,n01
   !   write(99000+lmpi_id,*) grid%l2g(i),l2g_ipe(i)
   !end do
   !call lmpi_conditional_stop(50,'Test l2g_ipe')

! Now do the edge stuff

    el2g_ipe = -1
    do i = 1, grid%nedge
      pe1 = l2g_ipe(grid%eptr(1,i))
      pe2 = l2g_ipe(grid%eptr(2,i))
      el2g_ipe(i) = min(pe1,pe2)
     !write(99000+lmpi_id,*) grid%l2g(grid%eptr(1:2,i)),el2g_ipe(i)
    end do

    nleft = 0
    do i = 1,grid%nedge
       if (el2g_ipe(i) == -1) nleft = nleft + 1
    end do
    if (nleft > 0) then
       write(*,*)"set_l2g_ipe2: Missing edges ",nleft
       call lmpi_conditional_stop(nleft)
    end if

!   call lmpi_conditional_stop(60,'Test el2g_ipe')

  end subroutine set_l2g_ipe2_cc

!=============================== pparty_write_stats ==========================80
!
! Calculate and write load balance
! Overset not implemented
!
!=============================================================================80

  subroutine pparty_write_stats(grid)

    use grid_types, only : grid_type
    use lmpi,       only : lmpi_nproc, lmpi_master, lmpi_gather, lmpi_id,      &
                           lmpi_synchronize

    type (grid_type), intent(in) :: grid

    integer :: j,k,nsends,nrecvs,ipe,nrecv,i_other_proc,length
    logical :: in_use, existent
    integer, dimension(:), allocatable :: q0,q1

    real(dp) :: ri, rj, svol,rvol

    integer,  dimension(:), allocatable :: iwork_arr
    real(dp), dimension(:), allocatable :: work_arr

    character(len=256) :: filename
    integer, parameter :: iunit = 172

  continue

    allocate(iwork_arr(lmpi_nproc)); iwork_arr = 0

    if ( lmpi_master ) then
      filename = trim(grid%project) // '.load_balance_stats'
      inquire( iunit, exist=existent,opened=in_use )
      if (existent.and..not.in_use)                                            &
      open(iunit,file=filename,status='old',form='formatted',position='append')
      write(iunit,*)
      write(iunit,*) "WORK"
    endif

    call lmpi_gather(grid%nnodes0,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Lev0: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(grid%nnodes01-grid%nnodes0,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Lev1: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(grid%nnodes01,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "  Lev01: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

! Communication

    if (lmpi_master) write(iunit,*)

    call party_lmpi_sr_stats(nsends,nrecvs,svol,rvol)
    call lmpi_gather(nsends,iwork_arr)
    if (lmpi_master) then
      write(iunit,*)"COMM"
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Send: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(nrecvs,iwork_arr)
    if (lmpi_master) then
      ri = minval(iwork_arr)*1._dp
      rj = maxval(iwork_arr)*1._dp
      write(iunit,'(a40,3(F15.0,1x),F8.2)')                                    &
        "   Recv: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if
    deallocate(iwork_arr)

    allocate(work_arr(lmpi_nproc)); work_arr = 0
    call lmpi_gather(svol,work_arr)
    if (lmpi_master) then
      ri = minval(work_arr)
      rj = maxval(work_arr)
      write(iunit,'(a40,3(F15.0,1x),F8.2,1x)')                                 &
        "   Svol: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if

    call lmpi_gather(rvol,work_arr)
    if (lmpi_master) then
      ri = minval(work_arr)
      rj = maxval(work_arr)
      write(iunit,'(a40,3(F15.0,1x),F8.2,1x)')                                 &
        "   Rvol: min, max, delta : ",ri,rj,rj-ri,((rj-ri)/ri)*100.0_dp
    end if
    deallocate(work_arr)

    if (lmpi_master) close(iunit)

!   if (pp_cmd_stats == 1) then
       filename = trim(grid%project) // '.load_balance_stats'
       allocate(q0(lmpi_nproc)); q0 = 0
       allocate(q1(lmpi_nproc)); q1 = 0
       do ipe = 0,lmpi_nproc-1
          call lmpi_synchronize
          if (ipe == lmpi_id) then
             k = ipe+1
             open(iunit,file=filename,status='old',form='formatted',           &
               position='append')
             if (ipe == 0) then
                write(iunit,*)
                write(iunit,*) "SR Information"
             end if
             write(iunit,'("    IPE ",i0,"  Rvol,Svol :: ",i0,1x,i0)')         &
               ipe,sr1(k)%recvproc(lmpi_nproc+1)-1,                            &
                   sr1(k)%sendproc(lmpi_nproc+1)-1
            !write(iunit,'("        RECV ",100(i0,1x))') sr1(k)%recvproc
            !write(iunit,'("        SEND ",100(i0,1x))') sr1(k)%sendproc

             nrecv = 0
             do i_other_proc = 1,lmpi_nproc
                if (i_other_proc /= k) then
                   length = sr1(k)%recvproc(i_other_proc+1)                    &
                          - sr1(k)%recvproc(i_other_proc)
                   if (length>0) then
                      nrecv  = nrecv + 1
                      q0(nrecv) = i_other_proc-1
                      q1(nrecv) = length
                   end if
                end if
             end do
             if (nrecv > 0) then
               write(iunit,'(11x,"Recv ",i0,1x,100(1x,"(",i0,1x,i0,")"))')&
                     nrecv,(q0(j),q1(j),j=1,nrecv)
             else
               write(iunit,'(11x,"Recv ",i0)') nrecv
             end if

             nrecv = 0
             do i_other_proc = 1, lmpi_nproc
                if (i_other_proc /= k) then
                   length = sr1(k)%sendproc(i_other_proc+1)                    &
                          - sr1(k)%sendproc(i_other_proc)
                   if (length>0) then
                      nrecv  = nrecv + 1
                      q0(nrecv) = i_other_proc-1
                      q1(nrecv) = length
                   end if
                end if
             end do
             if (nrecv > 0) then
               write(iunit,'(11x,"Send ",i0,1x,100(1x,"(",i0,1x,i0,")"))')     &
                     nrecv,(q0(j),q1(j),j=1,nrecv)
             else
               write(iunit,'(11x,"Send ",i0)') nrecv
             end if
             deallocate(q0,q1)

             write(iunit,'(11x,"Nodes (0,1,01) :: ",3(i0,1x))')                &
               grid%nnodes0,grid%nnodes01-grid%nnodes0,grid%nnodes01
             write(iunit,'(11x,"Edges          :: ",2(i0,1x))')                &
               grid%nedgeloc,size(grid%eptr,2)
             write(iunit,*)

             close(iunit)
          end if
       end do
!   end if

  end subroutine pparty_write_stats

!============================== PARTY_LMPI_SR_STATS ==========================80
!
! Return stats about the SR array
!
!=============================================================================80

  subroutine party_lmpi_sr_stats(nsends,nrecvs,svol,rvol)

    use lmpi, only : lmpi_id, lmpi_nproc

    integer,  intent(out) :: nsends,nrecvs
    real(dp), intent(out) :: svol,rvol

    integer  :: ipart, i, d1, d2

  continue

    ipart =  lmpi_id+1

    nsends  = 0
    svol    = 0._dp
    do i = 1,lmpi_nproc
       d1 = sr1(ipart)%sendproc(i+1)-sr1(ipart)%sendproc(i)
       svol = svol + d1*1._dp
       if (d1 > 0) nsends = nsends + 1
    end do

    nrecvs = 0
    rvol   = 0._dp
    do i = 1,lmpi_nproc
       d2 = sr1(ipart)%recvproc(i+1)-sr1(ipart)%recvproc(i)
       rvol = rvol + d2*1._dp
       if (d2 > 0) nrecvs = nrecvs + 1
    end do

  end subroutine party_lmpi_sr_stats

end module party_lmpi

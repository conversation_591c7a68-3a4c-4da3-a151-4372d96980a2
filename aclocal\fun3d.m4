# -*- Autoconf -*-
# autoconf macro for defining FUN3D configuration options
#
# Assigned Shell Variables:
#   $enable_hefss     Build with High Energy Physics
#   $enable_full_precision     Full precision real kind
#   $enable_complex   Build Complex Variable Tools
#
# Assigned Output Variables:
#   @PHYSICS_TYPE@    "PHYSICS_MODULES" or "PHYSICS_DUMMY"
#   @PERL5@           Absolute path to Perl 5
#
# Assigned AM_CONDITIONALS:
#   BUILD_ALL_TESTS
#   BUILD_PHYSICS
#   BUILD_COMPLEX
#
AC_DEFUN([AX_FUN3D_OPTIONS],[

# Check for High Energy support
AX_CHECK_HEFSS

# Check for Real kind precision
AX_CHECK_PRECISION

# Check for Complex Variable tools
AX_CHECK_COMPLEX

# Check for Perl5 path
AX_CHECK_PERL5

])


#
# Assigned Shell Variables:
#   $enable_hefss     Build with High Energy Physics
#
# Assigned Output Variables:
#   @PHYSICS_TYPE@    "PHYSICS_MODULES" or "PHYSICS_DUMMY"
#
# Assigned AM_CONDITIONALS:
#   BUILD_PHYSICS
#
AC_DEFUN([AX_CHECK_HEFSS],[

dnl Check for High Energy source

AC_ARG_ENABLE(hefss,
        [[  --enable-hefss          build with High Energy Physics [no]]],
        [enable_hefss=$enableval],  [enable_hefss="no"])

if test "$enable_hefss" != 'no'
then
  AC_CHECK_FILE([${srcdir}/PHYSICS_MODULES/chemical_kinetics.f90],
                [have_hefss='yes'],[have_hefss='no'])

  if test "$have_hefss" != 'no'
  then
    PHYSICS_TYPE="PHYSICS_MODULES"
    AM_CONDITIONAL(BUILD_PHYSICS,true)
    AC_DEFINE([HAVE_PHYSICS],[1],[Real gas physics build requested])
  else
    AC_MSG_ERROR([High Energy Physics requested, but source not available])
  fi
else
  PHYSICS_TYPE="PHYSICS_DUMMY"
  AM_CONDITIONAL(BUILD_PHYSICS,false)
fi
AC_SUBST([PHYSICS_TYPE])

])


#
# Assigned Shell Variables:
#   $enable_full_precision     Full precision real kind
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_CHECK_PRECISION],[

AC_ARG_ENABLE(full-precision,
        [[  --enable-full-precision build with full-precision real kind [no]]],
        [enable_full_precision=$enableval],[enable_full_precision="no"])

if test "$enable_full_precision" != 'no'
then
  AC_DEFINE([FULL_PRECISION],[1],[Full precision real kind build requested])
fi

])


#
# Assigned Shell Variables:
#   $enable_complex   Build Complex Variable Tools
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_COMPLEX
#
AC_DEFUN([AX_CHECK_COMPLEX],[

AC_ARG_ENABLE(complex,
        [[  --enable-complex        build Complex variable version of tools [no]]],
        [enable_complex=$enableval],  [enable_complex="no"])

if test "$enable_complex" != 'no'
then
  AC_CHECK_FILE([$srcdir/Complex/Makefile.am],
                [have_complex='yes'],[have_complex='no'])

  if test "$have_complex" != 'no'
  then

    AC_CHECK_FILE([$srcdir/Complex/complexifile.rb],
                  [have_complex='yes'],[have_complex='no'])

    if test "$have_complex" != 'no'
    then
      AC_ARG_VAR([HAVE_RUBY],[Ruby is available])
      AC_CHECK_PROG([HAVE_RUBY],[ruby],[yes],[no],[])

      if test "$HAVE_RUBY" == 'no'
      then
        AC_MSG_ERROR([Ruby >= 1.8 needed to generate Complex Variable source])
      fi
    else
      AC_MSG_ERROR([Complex Variable source generator not found])
    fi
  else
    AC_MSG_ERROR([This distribution does not support a Complex Variable build])
  fi

  AM_CONDITIONAL(BUILD_COMPLEX,true)
  AC_DEFINE([HAVE_COMPLEX],[1],[Complex variable build enabled])
else
  AM_CONDITIONAL(BUILD_COMPLEX,false)
fi

])


#
# Assigned Shell Variables:
#
# Assigned Output Variables:
#   @PERL5@           Absolute path to Perl 5
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_CHECK_PERL5],[

AC_ARG_WITH(perl5,
        [[  --with-perl5[=ARG]      Perl 5.6 installation [/usr/bin/perl]]],
        [with_perl5=$withval],      [with_perl5="/usr/bin/perl"])

PERL5="$with_perl5 -w"
AC_SUBST([PERL5])

])


#
# to provide a warning upon use of the old option
#
AC_DEFUN([AX_PARMETIS_OLD],[

AC_ARG_WITH(ParMetis,
        [[    the --with-ParMetis option has been removed, use --with-parmetis]],
        [with_ParMetis=$withval],   [with_ParMetis="no"])

if test "$with_ParMetis" != 'no'
then
  AC_MSG_ERROR([the --with-ParMetis option has been replaced with --with-parmetis])
fi

])


#
# Assigned Shell Variables:
#   $with_libcore     Build with external libcore
#
# Assigned Output Variables:
#   @libcore_ldadd@   Link information for libcore library
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_LIBCORE],[

AC_ARG_WITH(libcore,
        [[  --with-libcore[=ARG]    use external libcore [ARG=no]]],
        [with_libcore=${withval}],  [with_libcore="no"])

if test "${with_libcore}" != 'no'
then
  AC_CHECK_FILE([${with_libcore}/libcore.a],
                [libcore_path="${with_libcore}"],
                [AC_MSG_ERROR([libcore required but not found])])
  AM_CONDITIONAL(BUILD_LIBCORE,false)
else
  libcore_path='$(top_builddir)/libcore'
  AM_CONDITIONAL(BUILD_LIBCORE,true)
fi
AC_SUBST([libcore_path])
])


#
# Assigned Shell Variables:
#   $with_libturb     Build with external libturb
#
# Assigned Output Variables:
#   @libturb_ldadd@   Link information for libturb library
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_LIBTURB],[

AC_ARG_WITH(libturb,
        [[  --with-libturb[=ARG]    use external libturb [ARG=no]]],
        [with_libturb=${withval}],  [with_libturb="no"])

if test "${with_libturb}" != 'no'
then
  AC_CHECK_FILE([${with_libturb}/libturb.a],
                [libturb_path="${with_libturb}"],
                [AC_MSG_ERROR([libturb required but not found])])
  AM_CONDITIONAL(BUILD_LIBTURB,false)
else
  libturb_path='$(top_builddir)/libturb'
  AM_CONDITIONAL(BUILD_LIBTURB,true)
fi
AC_SUBST([libturb_path])
])


#
# Assigned Shell Variables:
#   $with_refine      Build with refine adaptation support
#
# Assigned Output Variables:
#   @refine_deps@   List of refine libraries for dependencies
#   @refine_ldadd@   Path to refine library
#
# Assigned AM_CONDITIONALS:
#   BUILD_REFINE_SUPPORT
#
AC_DEFUN([AX_REFINE],[

AC_ARG_WITH(refine,
        [[  --with-refine[=ARG]     use refine adaptation package [ARG=no]]],
        [with_refine=${withval}],   [with_refine="subpackage"])

# to subpackage refine
AM_CONDITIONAL(BUILD_EXECUTABLES,false)

if test "${with_refine}" == 'subpackage'
then
  AC_DEFINE([HAVE_REFINE],[1],[refine is available])
  AM_CONDITIONAL(BUILD_REFINE_SUPPORT,true)
  AM_CONDITIONAL(BUILD_REFINE,true)
  refine_deps=' $(top_builddir)/refine/src/librefine.a $(top_builddir)/refine/src/libFAUXGeom.a $(top_builddir)/refine/two/libref2.a '
  refine_ldadd=' -L$(top_builddir)/refine/src -lrefine -lFAUXGeom -L$(top_builddir)/refine/two -lref2 '
else
  if test "${with_refine}" != 'no'
  then
    AC_CHECK_FILE([${with_refine}/lib/librefine.a],
                  [have_refine='yes'],[have_refine='no'])

    if test "${have_refine}" != 'no'
    then
      AC_DEFINE([HAVE_REFINE],[1],[refine is available])
      AM_CONDITIONAL(BUILD_REFINE_SUPPORT,true)
      refine_ldadd="  -L${with_refine}/lib -lrefine -lFAUXGeom -lref2 "
    else
      AC_MSG_ERROR([refine requested but not found])
    fi
  else
    AM_CONDITIONAL(BUILD_REFINE_SUPPORT,false)
  fi
  AM_CONDITIONAL(BUILD_REFINE,false)
fi
AC_SUBST([refine_deps])
AC_SUBST([refine_ldadd])

# Check for CAPRI and set flags
CAPRI_LIB_PATH

])


#
# Assigned Shell Variables:
#   $with_knife      Build with knife cut cell support
#
# Assigned Output Variables:
#   @knife_deps@    List of knife libraries for dependencies
#   @knife_ldadd@    Stuff to link knife library
#
# Assigned AM_CONDITIONALS:
#   BUILD_KNIFE_SUPPORT
#
AC_DEFUN([AX_KNIFE],[

AC_ARG_WITH(knife,
        [[  --with-knife[=ARG]      use Knife cut cell package [ARG=subpackage]]],
        [with_knife=$withval],      [with_knife="subpackage"])

if test "$with_knife" == 'subpackage'
then
  AC_DEFINE([HAVE_KNIFE],[1],[knife is available])
  AM_CONDITIONAL(BUILD_KNIFE_SUPPORT,true)
  knife_deps=' $(top_builddir)/knife/src/libknife.a '
  knife_ldadd=' -L$(top_builddir)/knife/src -lknife '
  AC_SUBST([knife_deps])
  AC_SUBST([knife_ldadd])
  AM_CONDITIONAL(BUILD_KNIFE,true)
else
  if test "$with_knife" != 'no'
  then
    AC_CHECK_FILE([$with_knife/lib/libknife.a],
                  [have_knife='yes'],[have_knife='no'])

    if test "$have_knife" != 'no'
    then
      AC_DEFINE([HAVE_KNIFE],[1],[knife is available])
      knife_ldadd=" -L$with_knife/lib -lknife "
      AM_CONDITIONAL(BUILD_KNIFE_SUPPORT,true)
    else
      AC_MSG_ERROR([knife requested but not found])
    fi
    AC_SUBST([knife_ldadd])
  else
    AM_CONDITIONAL(BUILD_KNIFE_SUPPORT,false)
  fi
  AM_CONDITIONAL(BUILD_KNIFE,false)
fi

])


#
# Version of AC_CHECK_LIB() that links static library w/o "-l"
# and attempts to run the test program.
#
# Assigned Shell Variables:
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_CHECK_TECLIB],
[
AS_LITERAL_IF([$1],
              [AS_VAR_PUSHDEF([ac_Lib], [ac_cv_lib_$1_$2])],
              [AS_VAR_PUSHDEF([ac_Lib], [ac_cv_lib_$1''_$2])])dnl
AC_CACHE_CHECK([for $2 in $1], ac_Lib,
[ax_check_teclib_save_LIBS=$LIBS
 LIBS="$1 $5 $LIBS"
 AC_LANG_ASSERT(Fortran)
 AC_RUN_IFELSE([AC_LANG_PROGRAM([],
                                [[
      call tecini(char(0),'x'//char(0),'/dev/null'//char(0),
     +             '.'//char(0),0,0)
      call tecend
                                ]])],
               [AS_VAR_SET(ac_Lib, yes)],
               [AS_VAR_SET(ac_Lib, no)])
 LIBS=$ax_check_teclib_save_LIBS])
AS_IF([test AS_VAR_GET(ac_Lib) = yes],
      [m4_default([$3], [AC_DEFINE_UNQUOTED(AS_TR_CPP(HAVE_LIB$1))
  LIBS="$1 $LIBS"
])],
      [$4])dnl
AS_VAR_POPDEF([ac_Lib])dnl
])# AX_CHECK_TECLIB


#
# Version of AC_CHECK_LIB() that links static library w/o "-l"
# and attempts to run the test program.
#
# Assigned Shell Variables:
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_CHECK_TECLIB2008],
[
AS_LITERAL_IF([$1],
              [AS_VAR_PUSHDEF([ac_Lib], [ac_cv_lib_$1_$2])],
              [AS_VAR_PUSHDEF([ac_Lib], [ac_cv_lib_$1''_$2])])dnl
AC_CACHE_CHECK([for $2 in $1], ac_Lib,
[ax_check_teclib_save_LIBS=$LIBS
 LIBS="$1 $5 $LIBS"
 AC_LANG_ASSERT(Fortran)
 AC_RUN_IFELSE([AC_LANG_PROGRAM([],
                                [[
      call tecini111(char(0),'x'//char(0),'/dev/null'//char(0),
     +             '.'//char(0),0,0,0)
      call tecend111
                                ]])],
               [AS_VAR_SET(ac_Lib, yes)],
               [AS_VAR_SET(ac_Lib, no)])
 LIBS=$ax_check_teclib_save_LIBS])
AS_IF([test AS_VAR_GET(ac_Lib) = yes],
      [m4_default([$3], [AC_DEFINE_UNQUOTED(AS_TR_CPP(HAVE_LIB$1))
  LIBS="$1 $LIBS"
])],
      [$4])dnl
AS_VAR_POPDEF([ac_Lib])dnl
])# AX_CHECK_TECLIB2008


#
# Assigned Shell Variables:
#   $with_tecio       Build with Tecplot I/O support
#
# Assigned Output Variables:
#   @TECIOLIBS@       Path to Tecplot I/O libraries
#
# Assigned AM_CONDITIONALS:
#   BUILD_TECIO_SUPPORT
#
AC_DEFUN([AX_TECIO],[

AC_ARG_WITH(tecio,
        [[  --with-tecio[=ARG]      Tecplot I/O library install path [ARG=no]]],
        [with_tecio=$withval],      [with_tecio="no"])

# *Note:* Does not handle tecio.dll or tecio64.a cases.

if test "$with_tecio" != 'no'
then

  AC_CHECK_LIB([tecio],
               [tecini111],
               [TECIOLIBS="-L$with_tecio -ltecio -lstdc++"
                   have_tecio2008='yes'
                   have_tecio='yes'],
               [AX_CHECK_TECLIB2008(
                 [$with_tecio/tecio.a],
                 [tecini111],
                 [TECIOLIBS="$with_tecio/tecio.a -lstdc++"
                  have_tecio2008='yes'
                  have_tecio='yes'],
                 [AX_CHECK_TECLIB2008(
                   [$with_tecio/tecio64.a],
                   [tecini111],
                   [TECIOLIBS="$with_tecio/tecio64.a -lstdc++"
                    have_tecio2008='yes'
                    have_tecio='yes'],
                   [AX_CHECK_TECLIB(
                     [$with_tecio/tecio.a],
                     [tecini],
                     [TECIOLIBS="$with_tecio/tecio.a -lstdc++"
                      have_tecio2008='no'
                      have_tecio='yes'],
                     [AX_CHECK_TECLIB(
                       [$with_tecio/tecio64.a],
                       [tecini],
                       [TECIOLIBS="$with_tecio/tecio64.a -lstdc++"
                        have_tecio2008='no'
                        have_tecio='yes'],
                       [have_tecio='no'],
                       [-lstdc++]
                      )],
                     [-lstdc++]
                    )],
                   [-lstdc++]
                  )],
                 [-lstdc++]
                )],
               [-L$with_tecio -lstdc++]
              )

  if test "$have_tecio" != 'no'
  then
    AC_DEFINE([HAVE_TECIO],[1],[Tecio is available])
  else
    AC_MSG_ERROR([Tecio requested but not found])
  fi
  if test "$have_tecio2008" != 'no'
  then
    AC_DEFINE([HAVE_TECIO_2008],[1],[Tecio 2008 is available])
  fi
  AC_SUBST([TECIOLIBS])
  AM_CONDITIONAL(BUILD_TECIO_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_TECIO_SUPPORT,false)
fi

])


# -*- Autoconf -*-
# autoconf macro for defining DiRTLib support options
#
# Assigned Shell Variables:
#   $with_dirtlib     Build with DiRTLib support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_DIRTLIB_SUPPORT
#
AC_DEFUN([AX_DIRTLIB],[

AC_ARG_WITH(dirtlib,
        [[  --with-dirtlib[=ARG]    use DiRTlib overset library [ARG=no]]],
        [with_dirtlib=$withval],    [with_dirtlib="no"])

# Check for dirtlib library

if test "$with_dirtlib" != 'no'
then
  if test "$with_mpi" != 'no'
  then
    AC_CHECK_FILE([$with_dirtlib/libdirt_mpich.a],
                  [have_dirtlib='yes'],[have_dirtlib='no'])
  else
    AC_CHECK_FILE([$with_dirtlib/libdirt.a],
                  [have_dirtlib='yes'],[have_dirtlib='no'])
  fi

  if test "$have_dirtlib" != 'no'
  then
    AC_DEFINE([HAVE_DIRT],[1],[DiRTlib is available])
    dirtlibrary=$with_dirtlib
    AM_CONDITIONAL(BUILD_DIRTLIB_SUPPORT,true)
  else
    AC_MSG_ERROR([DiRTlib requested but not found])
  fi
  AC_SUBST([dirtlibrary])
else
  AM_CONDITIONAL(BUILD_DIRTLIB_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining SUGGAR support options
#
# Assigned Shell Variables:
#   $with_suggar     Build with SUGGAR support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_SUGGAR_SUPPORT
#
AC_DEFUN([AX_SUGGAR],[

AC_ARG_WITH(suggar,
        [[  --with-suggar[=ARG]     use SUGGAR overset library [ARG=no]]],
        [with_suggar=$withval],     [with_suggar="no"])

# Check for suggar library

if test "$with_suggar" != 'no'
then
  if test "$with_mpi" != 'no'
  then
    AC_CHECK_FILE([$with_suggar/libsuggar_mpi.a],
                  [have_suggar='yes'],[have_suggar='no'])
  else
    AC_CHECK_FILE([$with_suggar/libsuggar.a],
                  [have_suggar='yes'],[have_suggar='no'])
  fi

  if test "$have_suggar" != 'no'
  then
    AC_DEFINE([HAVE_SUGGAR],[1],[suggar is available])
    suggarlibrary=$with_suggar
    AM_CONDITIONAL(BUILD_SUGGAR_SUPPORT,true)
  else
    AC_MSG_ERROR([SUGGAR requested but not found])
  fi
  AC_SUBST([suggarlibrary])
else
  AM_CONDITIONAL(BUILD_SUGGAR_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining DYMORE support options
#
# Assigned Shell Variables:
#   $with_dymore     Build with DYMORE coupling support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_DYMORE_SUPPORT
#
AC_DEFUN([AX_DYMORE],[

AC_ARG_WITH(dymore,
        [[  --with-dymore[=ARG]     use DYMORE library [ARG=no]]],
        [with_dymore=$withval],     [with_dymore="no"])

# Check for dymore library

if test "$with_dymore" != 'no'
then
  AC_CHECK_FILE([$with_dymore/libdymore4.a],
                [have_dymore='yes'],[have_dymore='no'])

  if test "$have_dymore" != 'no'
  then
    AC_DEFINE([HAVE_DYMORE],[1],[Dymore is available])
    dymorelibrary=$with_dymore
    AM_CONDITIONAL(BUILD_DYMORE_SUPPORT,true)
  else
    AC_MSG_ERROR([DYMORE requested but not found])
  fi
  AC_SUBST([dymorelibrary])
else
  AM_CONDITIONAL(BUILD_DYMORE_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining IRS support options
#
# Assigned Shell Variables:
#   $with_irs      Build with IRS coupling support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_IRS_SUPPORT
#
AC_DEFUN([AX_IRS],[

AC_ARG_WITH(IRS,
        [[  --with-IRS[=ARG]        use IRS library [ARG=no]]],
        [with_irs=$withval],        [with_irs="no"])

# Check for irs library

if test "$with_irs" != 'no'
then
  AC_CHECK_FILE([$with_irs/libirs.a],
                [have_irs='yes'],[have_irs='no'])

  if test "$have_irs" != 'no'
  then
    AC_DEFINE([HAVE_IRS],[1],[IRS is available])
    irslibrary=$with_irs
    AM_CONDITIONAL(BUILD_IRS_SUPPORT,true)
  else
    AC_MSG_ERROR([IRS requested but not found])
  fi
  AC_SUBST([irslibrary])
else
  AM_CONDITIONAL(BUILD_IRS_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining SSDC support options
#
# Assigned Shell Variables:
#   $with_ssdc     Build with SSDC coupling support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_SSDC_SUPPORT
#
AC_DEFUN([AX_SSDC],[

AC_ARG_WITH(SSDC,
        [[  --with-SSDC[=ARG]       use SSDC library [ARG=no]]],
        [with_ssdc=$withval],       [with_ssdc="no"])

# Check for ssdc library

if test "$with_ssdc" != 'no'
then
  AC_CHECK_FILE([$with_ssdc/libssdc.a],
                [have_ssdc='yes'],[have_ssdc='no'])

  if test "$have_ssdc" != 'no'
  then
    AC_DEFINE([HAVE_SSDC],[1],[SSDC is available])
    ssdclibrary=$with_ssdc
    AM_CONDITIONAL(BUILD_SSDC_SUPPORT,true)
  else
    AC_MSG_ERROR([SSDC requested but not found])
  fi
  AC_SUBST([ssdclibrary])
else
  AM_CONDITIONAL(BUILD_SSDC_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining SFE support options
#
# Assigned Shell Variables:
#   $with_sfe     Build with SFE coupling support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_SFE_SUPPORT
#
AC_DEFUN([AX_SFE],[

AC_ARG_WITH(SFE,
        [[  --with-SFE[=ARG]       use SFE library [ARG=no]]],
        [with_sfe=$withval],       [with_sfe="no"])

# Check for sfe library

if test "$with_sfe" != 'no'
then
  AC_CHECK_FILE([$with_sfe/libsfe.a],
                [have_sfe='yes'],[have_sfe='no'])

  if test "$have_sfe" != 'no'
  then
    AC_DEFINE([HAVE_SFE],[1],[SFE is available])
    sfelibrary=$with_sfe
    AM_CONDITIONAL(BUILD_SFE_SUPPORT,true)
  else
    AC_MSG_ERROR([SFE requested but not found])
  fi
  AC_SUBST([sfelibrary])
else
  AM_CONDITIONAL(BUILD_SFE_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining SPARSKIT support options
#
# Assigned Shell Variables:
#   $with_sparskit   Build with SPARSKIT coupling support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_SPARSKIT_SUPPORT
#
AC_DEFUN([AX_SPARSKIT],[

AC_ARG_WITH(SPARSKIT,
        [[  --with-SPARSKIT[=ARG]   use SPARSKIT library [ARG=no]]],
        [with_sparskit=$withval],   [with_sparskit="no"])

# Check for sparskit library

if test "$with_sparskit" != 'no'
then
  AC_CHECK_FILE([$with_sparskit/libskit.a],
                [have_sparskit='yes'],[have_sparskit='no'])

  if test "$have_sparskit" != 'no'
  then
    AC_DEFINE([HAVE_SPARSKIT],[1],[SPARSKIT is available])
    sparskitlibrary=$with_sparskit
    AM_CONDITIONAL(BUILD_SPARSKIT_SUPPORT,true)
  else
    AC_MSG_ERROR([SPARSKIT requested but not found])
  fi
  AC_SUBST([sparskitlibrary])
else
  AM_CONDITIONAL(BUILD_SPARSKIT_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining RCAS_SDX support options
#
# Assigned Shell Variables:
#   $with_sdx     Build with RCAS_SDX coupling support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_RCAS_SDX_SUPPORT
#
AC_DEFUN([AX_RCAS_SDX],[

AC_ARG_WITH(sdx,
        [[  --with-sdx[=ARG]        use RCAS_SDX library [ARG=no]]],
        [with_sdx=$withval],        [with_sdx="no"])

# Check for sdx library

if test "$with_sdx" != 'no'
then
  AC_CHECK_FILE([$with_sdx/libsdx.a],
                [have_sdx='yes'],[have_sdx='no'])

  if test "$have_sdx" != 'no'
  then
    AC_DEFINE([HAVE_RCAS_SDX],[1],[Rcas_sdx is available])
    sdxlibrary=$with_sdx
    AM_CONDITIONAL(BUILD_RCAS_SDX_SUPPORT,true)
  else
    AC_MSG_ERROR([RCAS_SDX requested but not found])
  fi
  AC_SUBST([sdxlibrary])
else
  AM_CONDITIONAL(BUILD_RCAS_SDX_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining 6DOF support options
#
# Assigned Shell Variables:
#   $with_sixdof      Build with 6DOF support
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_SIXDOF_SUPPORT
#
AC_DEFUN([AX_UAB_6DOF],[

AC_ARG_WITH(sixdof,
        [[  --with-sixdof[=ARG]     build with 6DOF libraries from UAB [ARG=no]]],
        [with_sixdof=$withval],      [with_sixdof="no"])

# Check for 6DOF library

if test "$with_sixdof" != 'no'
then
# need 3 library files: libmo.a, libht.a, libexp.a
# check for libmo.a
  AC_CHECK_FILE([$with_sixdof/Motion/lib/libmo.a],
                [have_sixdof='yes'],[have_sixdof='no'])
  if test "$have_sixdof" != 'no'
  then
    AC_DEFINE([HAVE_SIXDOF],[1],[6DOF is available])
    SIXDOFLIBS=$with_sixdof
  else
    AC_MSG_ERROR([6DOF requested but Motion/lib/libmo.a not found])
  fi
# check for libht.a
  AC_CHECK_FILE([$with_sixdof/HT/lib/libht.a],
                [have_sixdof='yes'],[have_sixdof='no'])
  if test "$have_sixdof" != 'no'
  then
    AC_DEFINE([HAVE_SIXDOF],[1],[6DOF is available])
    SIXDOFLIBS=$with_sixdof
  else
    AC_MSG_ERROR([6DOF requested but HT/lib/libht.a not found])
  fi
# check for libexp.a
  AC_CHECK_FILE([$with_sixdof/EXP/lib/libexp.a],
                [have_sixdof='yes'],[have_sixdof='no'])
  if test "$have_sixdof" != 'no'
  then
    AC_DEFINE([HAVE_SIXDOF],[1],[6DOF is available])
    SIXDOFLIBS=$with_sixdof
  else
    AC_MSG_ERROR([6DOF requested but EXP/lib/libexp.a not found])
  fi
  AC_SUBST([SIXDOFLIBS])
  AM_CONDITIONAL(BUILD_SIXDOF_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_SIXDOF_SUPPORT,false)
fi

])


#
# Assigned Shell Variables:
#   $with_SBOOM             Build with SBOOM support
#
# Assigned Output Variables:
#   @SBOOMlibrary@           Path to SBOOM library
#
# Assigned AM_CONDITIONALS:
#   BUILD_SBOOM_SUPPORT
#
AC_DEFUN([AX_SBOOM],[

AC_ARG_WITH(SBOOM,
        [[  --with-SBOOM[=ARG]      SBOOM library path [ARG=no]]],
        [with_SBOOM=$withval],      [with_SBOOM="no"])

if test "$with_SBOOM" != 'no'
then
  AC_CHECK_FILE([$with_SBOOM/libsboomadjoint.a],
                [have_SBOOM='yes'],[have_SBOOM='no'])

  if test "$have_SBOOM" != 'no'
  then
    AC_DEFINE([HAVE_SBOOM],[1],[SBOOM is available])
    SBOOMlibrary="$with_SBOOM"
  else
    AC_MSG_ERROR([SBOOM requested but not found])
  fi
  AC_SUBST([SBOOMlibrary])
  AM_CONDITIONAL(BUILD_SBOOM_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_SBOOM_SUPPORT,false)
fi

])

# -*- Autoconf -*-
# autoconf macro for defining PUNDIT support options
#
# Assigned Shell Variables:
#   $with_pundit     Build with PUNDIT support
#
# Assigned Output Variables:
#   $HAVE_PUNDIT
#   @punditinclude@
#   @punditlibrary@
#
# Assigned AM_CONDITIONALS:
#   BUILD_PUNDIT_SUPPORT
#
AC_DEFUN([AX_PUNDIT],[

AC_ARG_WITH(pundit,
        [[  --with-pundit[=ARG]     use PUNDIT overset library [ARG=no]]],
        [with_pundit=$withval],     [with_pundit="no"])

# Check for pundit library

if test "$with_pundit" != 'no'
then
  AC_CHECK_FILE([$with_pundit/lib/libPUNDIT.a],
                [have_pundit='yes'],[have_pundit='no'])

  if test "$have_pundit" != 'no'
  then
    AC_DEFINE([HAVE_PUNDIT],[1],[PUNDIT is available])
    punditinclude="$with_pundit/interface"
    punditlibrary="$with_pundit/lib"
    AM_CONDITIONAL(BUILD_PUNDIT_SUPPORT,true)
  else
    AC_MSG_ERROR([PUNDIT requested but not found])
  fi
  AC_SUBST([punditinclude])
  AC_SUBST([punditlibrary])
else
  AM_CONDITIONAL(BUILD_PUNDIT_SUPPORT,false)
fi

])


# -*- Autoconf -*-
# autoconf macro for defining VisIt libsim support options
#
# Assigned Shell Variables:
#   $with_visit     Build with VisIt libsim support
#
# Assigned Output Variables:
#   @VisItinclude@  Include search path
#   @VisItlibrary@  Library search path
#
# Assigned AM_CONDITIONALS:
#   BUILD_VISIT_SUPPORT
#
AC_DEFUN([AX_VISIT],[

AC_ARG_WITH(visit,
        [[  --with-visit[=ARG]      use VisIt libsim library [ARG=no]]],
        [with_visit=$withval],      [with_visit="no"])

# Check for VisIt libsim library

if test "$with_visit" != 'no'
then
  AC_CHECK_FILE([$with_visit/lib/libsimV2f.a],
                [have_visit='yes'],[have_visit='no'])

  if test "$have_visit" != 'no'
  then
    AC_DEFINE([HAVE_VISIT],[1],[VisIt libsim is available])
    VisItinclude=$with_visit/include
    VisItlibrary=$with_visit/lib
    AM_CONDITIONAL(BUILD_VISIT_SUPPORT,true)
  else
    AC_MSG_ERROR([VisIt libsim requested but not found])
  fi
  AC_SUBST([VisItinclude])
  AC_SUBST([VisItlibrary])
else
  AM_CONDITIONAL(BUILD_VISIT_SUPPORT,false)
fi

])

AC_DEFUN([AX_DESIGN_LIBRARIES],[

AC_ARG_WITH(PORT,
        [[  --with-PORT[=ARG]       use PORT optimization library [ARG=no]]],
        [with_port=$withval],       [with_port="no"])

AC_ARG_WITH(NPSOL,
        [[  --with-NPSOL[=ARG]      use NPSOL optimization library [ARG=no]]],
        [with_npsol=$withval],       [with_npsol="no"])

AC_ARG_WITH(SNOPT,
        [[  --with-SNOPT[=ARG]      use SNOPT optimization library [ARG=no]]],
        [with_snopt=$withval],       [with_snopt="no"])

AC_ARG_WITH(DOT,
        [[  --with-DOT[=ARG]        use DOT optimization library [ARG=no]]],
        [with_dot=$withval],        [with_dot="no"])

AC_ARG_WITH(KSOPT,
        [[  --with-KSOPT[=ARG]      use KSOPT optimization library [ARG=no]]],
        [with_ksopt=$withval],       [with_ksopt="no"])

if test "$with_port" != 'no'
then
  AC_CHECK_FILE([$with_port/libport.a],
                [have_port='yes'],[have_port='no'])

  if test "$have_port" != 'no'
  then
    AC_DEFINE([HAVE_PORT3],[1],[PORT is available])
    portlibrary=$with_port
    AM_CONDITIONAL(BUILD_PORT_SUPPORT,true)
  else
    AC_MSG_ERROR([PORT requested but not found])
  fi
  AC_SUBST([portlibrary])
else
  AM_CONDITIONAL(BUILD_PORT_SUPPORT,false)
fi

if test "$with_npsol" != 'no'
then
  AC_CHECK_FILE([$with_npsol/libopt.a],
                [have_npsol='yes'],[have_npsol='no'])

  if test "$have_npsol" != 'no'
  then
    AC_DEFINE([HAVE_NPSOL],[1],[NPSOL is available])
    npsollibrary=$with_npsol
    AM_CONDITIONAL(BUILD_NPSOL_SUPPORT,true)
  else
    AC_MSG_ERROR([NPSOL requested but not found])
  fi
  AC_SUBST([npsollibrary])
else
  AM_CONDITIONAL(BUILD_NPSOL_SUPPORT,false)
fi

if test "$with_snopt" != 'no'
then
  AC_CHECK_FILE([$with_snopt/libsnopt.a],
                [have_snopt='yes'],[have_snopt='no'])

  if test "$have_snopt" != 'no'
  then
    AC_DEFINE([HAVE_SNOPT],[1],[SNOPT is available])
    snoptlibrary=$with_snopt
    AM_CONDITIONAL(BUILD_SNOPT_SUPPORT,true)
  else
    AC_MSG_ERROR([SNOPT requested but not found])
  fi
  AC_SUBST([snoptlibrary])
else
  AM_CONDITIONAL(BUILD_SNOPT_SUPPORT,false)
fi

if test "$with_dot" != 'no'
then
  AC_CHECK_FILE([$with_dot/libDOT2.a],
                [have_dot='yes'],[have_dot='no'])

  if test "$have_dot" != 'no'
  then
    AC_DEFINE([HAVE_DOT],[1],[DOT is available])
    dotlibrary=$with_dot
    AM_CONDITIONAL(BUILD_DOT_SUPPORT,true)
  else
    AC_MSG_ERROR([DOT requested but not found])
  fi
  AC_SUBST([dotlibrary])
else
  AM_CONDITIONAL(BUILD_DOT_SUPPORT,false)
fi

if test "$with_ksopt" != 'no'
then
  AC_CHECK_FILE([$with_ksopt/libksopt.a],
                [have_ksopt='yes'],[have_ksopt='no'])

  if test "$have_ksopt" != 'no'
  then
    AC_DEFINE([HAVE_KSOPT],[1],[KSOPT is available])
    ksoptlibrary=$with_ksopt
    AM_CONDITIONAL(BUILD_KSOPT_SUPPORT,true)
  else
    AC_MSG_ERROR([KSOPT requested but not found])
  fi
  AC_SUBST([ksoptlibrary])
else
  AM_CONDITIONAL(BUILD_KSOPT_SUPPORT,false)
fi

])


# -*- Autoconf -*-
# autoconf macro for defining Python wrapper support options
#
# Assigned Shell Variables:
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_PYTHON_SUPPORT
#
AC_DEFUN([AX_PYTHON],[

AC_ARG_ENABLE(python,
        [[  --enable-python         build Python extension module [no]]],
        [enable_python=$enableval], [enable_python="no"])

if test "$enable_python" != 'no'
then
  AM_PATH_PYTHON([2.6],
                 [AC_ARG_VAR([HAVE_F2PY],[f2py is available])
                  AC_CHECK_PROG([HAVE_F2PY],[f2py],[yes],[no],[])
                  if test "$HAVE_F2PY" == 'no'
                  then
                    AC_MSG_ERROR([f2py is required to generate Python wrappers for FUN3D])
                  fi
                  CFLAGS="$CFLAGS -fPIC"
                  AC_SUBST([CFLAGS])
                  CXXFLAGS="$CXXFLAGS -fPIC"
                  AC_SUBST([CXXFLAGS])
                  FCFLAGS="$FCFLAGS -fPIC"
                  AC_SUBST([FCFLAGS])
                  AM_CONDITIONAL(BUILD_PYTHON_SUPPORT,true)],
                 [AC_MSG_ERROR([Python not found])]
                )
else
  AM_CONDITIONAL(BUILD_PYTHON_SUPPORT,false)
fi

])


# -*- Autoconf -*-
# autoconf macro for finding pdf2latex and bibtex tools
#
# Assigned Shell Variables:
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#   BUILD_PDFLATEX_SUPPORT
#
AC_DEFUN([AX_PDFLATEX],[

PDFLATEX="pdflatex"
AC_SUBST([PDFLATEX])
BIBTEX="bibtex"
AC_SUBST([BIBTEX])

# Check for presence of pdfLaTeX
AC_CHECK_PROG([have_latex], [$PDFLATEX], [yes], [no])

if test "$have_latex" != 'no'; then
  # Check for presence of bibtex
  AC_CHECK_PROG([have_bibtex], [$BIBTEX], [yes], [no])
  if test "$have_bibtex" != 'no'; then
    enable_docs=yes
    AM_CONDITIONAL([BUILD_PDFLATEX_SUPPORT], true)
  fi
fi

if test "$have_latex" == 'no' -o "$have_bibtex" == 'no'; then
  AC_MSG_WARN([Unable to create PDF version of the user manual.])
  enable_docs=no
  AM_CONDITIONAL([BUILD_PDFLATEX_SUPPORT], false)
fi
])

!======================= HRLES_BLEND =========================================80
!
! Computes the HRLES blending function.  Since we recompute a lot of constants,
! this is probably slower than the inline version in turb_hrles, but this is
! easier to inject into the Tecplot output routines without circular deps.
!
! Returns 1 if not running HRLES
!
!=============================================================================80

  pure function hrles_blend(eqn_set, n_tot, n_grd, slen,                       &
                            q_dof, turb, gradx, grady, gradz)

    use kinddefs,        only: dp
    use fun3d_constants, only: my_1, my_1p5
    use info_depr,       only: tref, xmach, re, ivisc
    use fluid,           only: gamma, sutherland_constant
    use turb_kw_const,   only: sig_w2, betastar
    use turb_util,       only: kloc, wloc
    use solution_types,  only: incompressible

    integer,                    intent(in) :: eqn_set, n_tot, n_grd
    real(dp),                   intent(in) :: slen
    real(dp), dimension(n_tot), intent(in) :: q_dof
    real(dp), dimension(2),     intent(in) :: turb
    real(dp), dimension(n_grd), intent(in) :: gradx, grady, gradz
    real(dp)                               :: hrles_blend

    real(dp) :: cstar, xmr, rnu, rhoinv, p, temp, tke, omega, dist
    real(dp) :: rkx, rky, rkz, rwx, rwy, rwz, term, crossterm
    real(dp) :: arg1, arg2, argt, arg3, arga, arg

!   If we're not actually running HRLES, just set this to a value
!   indicating pure RANS
    if (ivisc /= 10) then
      hrles_blend = 1.0_dp
      return
    end if

    if ( eqn_set == incompressible ) then
      xmr = my_1 / re
      rnu = my_1
    else
      xmr    = xmach / re
      rhoinv = my_1 / q_dof(1)
      p      = q_dof(5)
      temp   = gamma * p * rhoinv
      cstar  = sutherland_constant / tref
      rnu    = ((my_1+cstar)/(temp+cstar)*temp**my_1p5) * rhoinv
    end if

    rkx = gradx(kloc)
    rky = grady(kloc)
    rkz = gradz(kloc)
    rwx = gradx(wloc)
    rwy = grady(wloc)
    rwz = gradz(wloc)

    tke   = turb(1)
    omega = turb(2)

    dist = slen
    if(abs(dist) <= tiny(1.0_dp)) dist = 1.0e-12_dp

    term = rkx*rwx + rky*rwy + rkz*rwz
    crossterm = 2.0_dp*xmr/sig_w2*term/omega

    arg1 = xmr*sqrt(tke)/(betastar*omega*dist)
    arg2 = 500.0_dp*xmr*xmr*rnu/(dist*dist*omega)
    argt = max(crossterm/xmr,1.0e-20_dp)
    arg3 = 4.0_dp/sig_w2*tke/(argt*dist*dist)
    arga = max(arg1,arg2)
    arg  = min(arga,arg3)

    hrles_blend = tanh(arg*arg*arg*arg)

  end function hrles_blend

\section{Boundary Conditions}\label{s:boundaryconditions}

This chapter discusses the boundary conditions available in \FunThreeD.
\Tab{t:bcs} lists the integers used to specify
\FunThreeD boundary conditions with a short description.
Each grid description subsection in
\sectionref{s:grids} indicates how these integers are specified.
Details of the boundary condition implementation are provided
by <PERSON>.\cite{carlson-fun3d-bc-tm}
Details of symmetry boundary conditions are provided in \sectionref{s:symmbc}.
Some boundary conditions have required or optionally specified 
parameters defined in the \texttt{\&boundary\_conditions} namelist,
see \sectionref{s:nml_boundary_conditions} for further boundary
condition details.

\begin{table}[hbtp]
  \centering\tabularfont
  \caption{\FunThreeD boundary conditions.}
  \label{t:bcs}
  \begin{threeparttable}
  \begin{tabular}{rlp{3.5in}}
    BC & Description  & Notes  \\
    \midrule
    $-$1 & Overlap      & overset grid boundary \\
    3000 & Tangency     & zero normal velocity, specified via fluxes \\
    4000\tnote{*}       & Viscous  & explicitly set the no-slip condition \\
    5000 & Farfield     &  Riemann invariants  \\
    5026 & Extrapolate  &  supersonic outflow, 
                           variables extrapolated from the interior   \\
    5050 & Freestream   & external freestream, specified via fluxes \\
    5051\tnote{*} & Back pressure  
                        &  specified static pressure 
                           (switches to extrapolation boundary condition 
                            in the presence of supersonic flow)  \\
    5052\tnote{*} & Mach outflow  
                        &  static pressure outflow boundary condition set
                           via a specified subsonic Mach number 
                           (not for boundary layer ingestion) \\
    6021 & Symmetry plane 1 
                        & symmetry enforced by replacing $x$-momentum with
                          zero velocity normal to arbitrary boundary plane. \\
    6022 & Symmetry plane 2
                        & symmetry enforced by replacing $y$-momentum with
                          zero velocity normal to arbitrary boundary plane. \\
    6023 & Symmetry plane 3
                        & symmetry enforced by replacing $z$-momentum with
                          zero velocity normal to arbitrary boundary plane. \\
    6100 & Periodicity  &  discrete periodicity, 
                           limited to nominally 2D grids extruded
                           across n planes in a third dimension  \\
    6661 & $X$-symmetry plane  
                        &  enforces symmetry for $x$ Cartesian plane\\
    6662 & $Y$-symmetry plane  
                        &  enforces symmetry for $y$ Cartesian plane\\
    6663 & $Z$-symmetry plane  
                        &  enforces symmetry for $z$ Cartesian plane\\
    7011\tnote{*} & Subsonic inflow   &  
    subsonic inflow ($p_{t,bc}=p_{total,plenum}/p_{static,freestream}$,
    $T_{t,bc}=T_{total,plenum}/T_{static,freestream}$) for nozzle or tunnel plenum ( M$_{inflow} <$ 1 )   \\
    7012\tnote{*} & Subsonic outflow  &  subsonic outflow ($p_{bc}=p_{static,inlet}/p_{static,freestream}$ 
    for inlet flow (does not allow for reverse or supersonic flow at the outflow boundary face)  \\
    7021\tnote{*} & Reaction control jet plenum  & models the plenum of a reaction control system (RCS) jet  \\
    7031\tnote{*} & Mass flow out  &  specification of massflow out of the control volume  \\
    7036\tnote{*} & Mass flow in  &  specification of massflow in to the control volume  \\
    7100\tnote{*} & Fixed inflow  &  fixed primitive variables in to control volume   \\
    7101\tnote{*} & Fixed inflow profile &  specified profile   \\
    7103\tnote{*} & Pulsed supersonic inflow  & pulsing supersonic flow    \\
    7104\tnote{*} & Ramped supersonic inflow  & ramping supersonic flow    \\
    7105\tnote{*} & Fixed outflow  & specified primitive outflow conditions    \\
    \bottomrule
  \end{tabular}
  \begin{tablenotes}
    \item [*] See \texttt{\&boundary\_conditions} namelist in 
              \sectionref{s:nml_boundary_conditions} 
              to specify auxiliary information and for further descriptions.
  \end{tablenotes}
  \end{threeparttable}
\end{table}

\clearpage

\subsection{$X$-Symmetry, $Y$-Symmetry, and $Z$-Symmetry}\label{s:symmbc}

The symmetry condition in FUN3D enforces discrete symmetry.  
That is, if the mesh were mirrored about the symmetry plane and run as
a full-span simulation, 
the residuals (and therefore the outputs, such as lift, drag, etc.) 
will be identical.  
The FUN3D automated tests include a case were a mirrored grid
is compared to the symmetry boundary condition.
Discrete symmetry is also enforced where multiple symmetry condition
intersect (i.e., a corner of $Y$-symmetry and $Z$-symmetry).

Specifically, the condition enforces:
\begin{itemize}
\item Any points on a symmetry plane are first ``snapped'' to the average coordinate for that plane.  Many grid generators will have some small amount of ``slop'' in their y-coordinates for points on a y-symmetry plane; FUN3D immediately pops all of the points onto the exact same plane, at least to double precision.
\item The residual equation corresponding to the momentum normal to the symmetry plane is modified to reflect zero crossflow (i.e., $\rho v = 0$ on a y-symmetry plane).

\item The least squares system used to compute gradients for inviscid reconstruction to the cell faces is augmented to include symmetry contributions across the symmetry plane(s).

\item All gradients of the velocity normal to the symmetry plane ($v$ for a y-symmetry plane) in the source terms for the turbulence models are zeroed out.  Gradients of the tangential velocities are zeroed out normal to the symmetry plane (du/dy, dw/dy).

\item No convective flux of turbulence normal to the symmetry plane.

\item Grid metrics (e.g., areas, normals) are forced to be symmetric at symmetry planes.

\end{itemize}

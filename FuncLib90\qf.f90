!================================== QF =======================================80
!
!  Reconstruction of the primitive state variables at an interface
!
!=============================================================================80

  pure function qf( rx, ry, rz, gradx, grady, gradz, q1, q2,                   &
                    central, phi, eps, second, ndim )

    use kinddefs,        only : dp
    use fun3d_constants, only : my_0, my_1

    use info_depr,       only : new_umuscl
    use generic_gas_map, only : n_species, n_etot
    use cfl_defs,      only : hanim
    use inviscid_flux,   only : average_off_wall


    integer,                   intent(in) :: central, ndim
    real(dp),                  intent(in) :: rx, ry, rz
    real(dp),                  intent(in) :: eps
    real(dp), dimension(ndim), intent(in) :: gradx, grady, gradz
    real(dp), dimension(ndim), intent(in) :: q1, q2
    real(dp), dimension(ndim), intent(in) :: phi

    logical,                   intent(in) :: second

    real(dp), dimension(ndim+1)           :: qf
    real(dp)                              :: rhoi_min


  continue

!   Initialize reconstruction realizability flag to default state

    qf(ndim+1)   = my_0

!   Higher order reconstructed state primitive variables:

    if ( hanim .and. second ) then
      qf(1:ndim) = q1 + dqumuscl( rx, ry, rz, gradx, grady, gradz, q1, q2,     &
                                  phi, eps, ndim )
    elseif (.not. new_umuscl .and. second) then
      qf(1:ndim) = q1 + dq(rx,ry,rz,gradx,grady,gradz,q1,q2,phi,eps,ndim)
    else if (new_umuscl .and. second) then
      qf(1:ndim) = q1 + dqumuscl(rx,ry,rz,gradx,grady,gradz,q1,q2,             &
                                 phi,eps,ndim)
    else
      qf(1:ndim) = q1
    end if

!   Central differencing.

    if ( average_off_wall .and. hanim .and.            &
         central /= 0 .and. second ) then

      qf(1:ndim) = 0.5_dp*( q1(1:ndim) + q2(1:ndim) )

    endif

!   Detect unrealizable higher order reconstruction of density and/or pressure
!   then force a first order reconstruction of the state variables and then
!   tag the reconstruction as unrealizable

    if ( ndim == 4 ) return !skip the incompressible path.

    rhoi_min = minval(qf(1:n_species))
    if ((rhoi_min <= my_0) .or. (qf(n_etot) <= my_0)) then
      qf(1:ndim) = q1(1:ndim)
      qf(ndim+1)   = my_1
    end if

  end function qf

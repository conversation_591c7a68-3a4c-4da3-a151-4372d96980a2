#! /usr/bin/env ruby
#
# This program reads a Fortran template file (e.g., something.template)
# and produces Fortran code (e.g., something.F90).

require 'English'

template_rootname = ''

if ARGV.size == 1 then
  template_rootname = ARGV.first
else
  puts "Usage: #{File.basename $PROGRAM_NAME} template_rootname"
  exit 1
end

template_file = template_rootname + '.template'

types = [ [ "integr", "integer(system_i4)", "mpi_integer",          "0",        "-huge(1_system_i4)"],
          [ "integ8", "integer(system_i8)", "mpi_integer8",         "0",        "-huge(1_system_i8)"],
          [ "integ1", "integer(system_i1)", "mpi_byte",             "0",        "-huge(1_system_i1)"],
          [ "logicl", "logical",            "mpi_logical",          ".false.",  ".false." ],
          [ "single", "real(system_r4)",    "mpi_real",             "0.0",      "-huge(1.0_system_r4)"],
          [ "double", "real(system_r8)",    "mpi_double_precision", "0.0",      "-huge(1.0_system_r8)"],
          [ "cmpxr4", "complex(system_r4)", "mpi_complex",          "0.0",      "-huge(1.0_system_r4)"],
          [ "cmpxr8", "complex(system_r8)", "mpi_double_complex",   "0.0",      "-huge(1.0_system_r8)"] ]

begin
  input = IO.readlines template_file
rescue
  puts $!
  exit 1
end

WARNING = <<EOS
!
! *Do Not edit* this file.  It was generated by the ruby script,
!
! Instead, edit the template file, '#{template_file}', regenerate
! this file (by running #{__FILE__}) and commit both
! this generated file and your new template file.
!
EOS

lines, interface, buffer = [], [], []

mode = :norm

input.each do |line|

  if line.match(/^!tempStartExpand\((.*)\)/) then
    mode = :temp
    interface.push "  public :: #$1"
    interface.push "  interface #$1"
    buffer = []
  end

  if mode == :norm then
    lines.push line.gsub(/\s*!tempProtectPrivate/,"")
    if !line.match(/!tempProtectPrivate/) &&
        line.match(/^\s*(?:subroutine|function)\s*(.*)/i) then
      interface.push "  public:: #{$1.sub(/\(.*$/,'')}\n\n"
    end
  end

  if line.match(/^!tempEndExpand/) then
    mode = :norm
    subname  = ''
    types.each do |type|
      buffer.each do |bufferline|
        bufferline = bufferline.gsub( /tempName/,     type[0] ).
                                gsub( /tempType/,     type[1] ).
                                gsub( /tempMPIType/,  type[2] ).
                                gsub( /tempConstant/, type[3] ).
                                gsub( /tempHuge/,     type[4] )
        if bufferline.match(/^\s*subroutine\s*(.*)/i) then
          subname = $1.sub(/\(.*$/,'')
          interface.push "    module procedure #{subname}"
        end
        lines.push bufferline.sub( /tempSub/, subname )
      end
    end
    interface.push "  end interface\n\n"
  end

  if line.match(/^\s*subroutine\s*(.*)\s*.*!tempProtectOnce/i)
    mode = :once
    interface.push "    module procedure #{$1.sub(/\(.*$/,'')}"
  end

  buffer.push line if mode == :temp
  lines.push  line if mode == :once

  mode = :temp if mode == :once && line.match(/^\s*end subroutine/i)

end

lines.collect! do |line|
  unless line.match(/^!tempInsertInterface/) then
    if line.index('&') and line.index('&')+1 != 80 then
      line.match(/&/)
      line = line.chomp.sub(/\s*&(.*)$/,'').ljust(79) + "&#$1\n" unless $`.match(/!/)
    end
    line
  else
    interface
  end
end

begin
  File.open( "#{template_rootname}.F90", "w" ) { |f| f.puts(WARNING,lines) }
rescue
  $stderr.puts "File #{template_rootname}.F90 not opened: #$!"
end

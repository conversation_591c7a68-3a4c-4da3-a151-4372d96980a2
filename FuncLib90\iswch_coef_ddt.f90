!============================== ISWCH_COEF ===================================80
!
!  Computes the feature detection coefficient which is then used to compute
!  a flux switching and limiting coefficient depending on the value of behavior:
!
!  ------------------------------------------------------------------
!  | behavior | flux switching and limiting coefficient behavior    |
!  ------------------------------------------------------------------
!  |  = 10    | binary (on/off) flux switching                      |
!  ------------------------------------------------------------------
!  |  =  1    | continuous flux switching and limiting              |
!  ------------------------------------------------------------------
!
!=============================================================================80

  pure function iswch_coef_ddt(rx1, ry1, rz1, rx2, ry2, rz2,                   &
                               gradpx1, gradpy1, gradpz1,                      &
                               gradpx2, gradpy2, gradpz2,                      &
                               pressl, pressr, phip1, phip2,                   &
                               ubarl, ubarr, q2l, q2r, q2, a2,                 &
                               laplcc, power, behavior)

    use kinddefs,        only : dp
    use ddt,             only : ddt5, assignment(=),                           &
      ddt_min, ddt_max, ddt_abs, ddt_sqrt, ddt_tanh, ddt_cos,                  &
      operator(+), operator(-), operator(*), operator(/), operator(**),        &
      operator(<=)
    use fun3d_constants, only : my_0, my_half, my_1, pi

    use inviscid_flux,                only : lhs_u_eigenvalue_coef

    integer,  intent(in) :: behavior

    real(dp),   intent(in) :: rx1, ry1, rz1, rx2, ry2, rz2
    type(ddt5), intent(in) :: gradpx1, gradpy1, gradpz1,                       &
                              gradpx2, gradpy2, gradpz2
    real(dp),   intent(in) :: phip1, phip2
    type(ddt5), intent(in) :: pressl, pressr
    type(ddt5), intent(in) :: ubarl, ubarr, q2l, q2r
    type(ddt5), intent(in) :: q2, a2
    real(dp),   intent(in) :: laplcc, power

    type(ddt5)             :: iswch_coef_ddt

    type(ddt5) :: gradpl, gradpr, pgrad
    type(ddt5) :: dpx2, dpy2, dpz2, plapc, pcoef, prato
    type(ddt5) :: utngl, utngr, utot, dutng
    type(ddt5) :: machno, machf, iswch

    real(dp), parameter :: min_mach = 0.35_dp
    real(dp), parameter :: dmach    = 0.35_dp
    real(dp), parameter :: one_e_negative_6 = 0.000001_dp

  continue

!   Construct the normalized undivided pressure ratio

    prato  = ddt_abs(pressr - pressl) / ddt_min(pressl, pressr)

!   Construct the normalized magnitude of the undivided pressure gradient

    gradpl = ddt_sqrt( gradpx1**2 + gradpy1**2 + gradpz1**2 )                  &
           * sqrt( rx1*rx1 + ry1*ry1 + rz1*rz1 )

    gradpr = ddt_sqrt( gradpx2**2 + gradpy2**2 + gradpz2**2 )                  &
           * sqrt( rx2*rx2 + ry2*ry2 + rz2*rz2 )

    pgrad  = ddt_max(gradpl, gradpr) / ddt_min(pressl, pressr)

!   Construct a crude estimate of the magnitude of the
!   normalized undivided pressure Laplacian

    dpx2 = gradpx2*rx2 - gradpx1*rx1
    dpy2 = gradpy2*ry2 - gradpy1*ry1
    dpz2 = gradpz2*rz2 - gradpz1*rz1

    plapc = laplcc*ddt_sqrt( dpx2*dpx2 + dpy2*dpy2 + dpz2*dpz2 )               &
          / ddt_min(pressl, pressr)

    pcoef = ddt_sqrt( ddt_max(pgrad, prato)**2 + plapc**2 )

!   Compute deltaU(tangent) and U(normal) to compute the inviscid coeff.

    utngl  = ddt_sqrt(ddt_max(my_0, q2l-ubarl*ubarl))
    utngr  = ddt_sqrt(ddt_max(my_0, q2r-ubarr*ubarr))
    utot   = ddt_sqrt(q2)
    dutng  = ddt_min(my_1, ddt_abs(utngr-utngl)/(utot+one_e_negative_6))

    iswch_coef_ddt = ddt_min(my_1-dutng, ddt_tanh(pcoef)**power)
    iswch_coef_ddt = &
      ddt_min(my_1,  &
              ddt_max(my_0, ddt_max(iswch_coef_ddt,my_1-min(phip1,phip2))))

!   Force the switch to approach 0 in sub-sonic flow, thereby forcing
!   the anti-diffusion term to be active in regions of sub-sonic flow

    machno = ddt_sqrt(q2/a2)
    machf  = ddt_max(my_0, ddt_min(my_1, (machno-min_mach)/dmach))
    iswch  = my_1 - my_half*(ddt_cos(machf*pi)+my_1)
    iswch_coef_ddt = ddt_min(iswch, iswch_coef_ddt)

!   Compute the switching/dissipation/limiting coefficient
!   A) when behavior = 10 : compute the binary switch used to switch between the
!      dissipative and non-dissipative fluxes where:
!      iswch_coef = 0 : dissipative flux
!      iswch_coef = 1 : non-dissipative flux
!   or
!   B) when behavior =  1 : compute the continuous switching/limiting function

    if (behavior == 10) then
      if (iswch_coef_ddt <= my_1-lhs_u_eigenvalue_coef) then
        iswch_coef_ddt = my_1
      else
        iswch_coef_ddt = my_0
      end if
    else
      iswch_coef_ddt = my_1 - iswch_coef_ddt
    end if

  end function iswch_coef_ddt

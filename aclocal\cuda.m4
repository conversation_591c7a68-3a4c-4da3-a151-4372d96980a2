# -*- Autoconf -*-
# autoconf macro for defining CUDA configuration options
#
# Assigned Shell Variables:
#   $with_cuda        Build with Cuda partitioning support
#
# Assigned Output Variables:
#   @CUDA_LIB_PATH@   Path to Cuda library
#
# Assigned AM_CONDITIONALS:
#   BUILD_CUDA_SUPPORT
#
AC_DEFUN([AX_PROG_CUDA],[

AC_ARG_WITH(cuda,
        [[  --with-cuda[=ARG]       CUDA install path [ARG=no]]],
        [with_cuda=$withval],       [with_cuda="no"])

AC_ARG_VAR([CUDAFLAGS], [CUDA compiler flags])

# Check for CUDA Compiler
if test "$with_cuda" != 'no'
then
  dnl We need OBJEXT and EXEEXT, but Autoconf doesn't offer any public
  dnl macro to compute them.  Use AC_PROG_CC instead.
  AC_REQUIRE([AC_PROG_CC])
  AC_PATH_PROG([CUDACC], [nvcc], [], [$with_cuda/bin $PATH])

  AS_IF([test -z "$CUDACC"], [AC_MSG_ERROR([No CUDA compiler found.])],
        [AC_MSG_NOTICE([determine CUDA library ABI])
         AC_LANG_PUSH([C])
          AC_CHECK_LIB([cudart],
                       [cudaFree],
                       [CUDA_LIB_PATH="$with_cuda/lib"
                        have_cuda='yes'],
                       [AX_RESET_CUDA_LIB_CACHE([cudart],[cudaFree])
                        AC_CHECK_LIB([cudart],
                                     [cudaFree],
                                     [CUDA_LIB_PATH="$with_cuda/lib64"
                                      have_cuda='yes'],
                                     [AC_MSG_ERROR([CUDA library but not found])
                                      have_cuda='no'],
                                     [-L$with_cuda/lib64])],
                       [-L$with_cuda/lib])
         AC_LANG_POP([C])
        ])

  AC_MSG_NOTICE([Using CUDA library in $CUDA_LIB_PATH])
  AC_SUBST([CUDA_LIB_PATH])
  AC_DEFINE([HAVE_CUDA],[1],[CUDA is available])
  AM_CONDITIONAL(BUILD_CUDA_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_CUDA_SUPPORT,false)
fi
  
])

#
# Assigned Shell Variables:
#
# Assigned Output Variables:
#
# Assigned AM_CONDITIONALS:
#
AC_DEFUN([AX_RESET_CUDA_LIB_CACHE], [
  AS_LITERAL_IF([$1],
                [AS_VAR_PUSHDEF([ax_Lib], [ac_cv_lib_$1_$2])],
                [AS_VAR_PUSHDEF([ax_Lib], [ac_cv_lib_$1''_$2])])dnl
  AS_UNSET([ax_Lib])
  AS_VAR_POPDEF([ax_Lib])
])

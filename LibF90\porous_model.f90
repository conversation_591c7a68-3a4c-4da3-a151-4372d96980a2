module porous_model

    use kinddefs,       only : dp
    use info_depr,      only : skeleton

  implicit none

  private

    public :: porous_wall_flux

    public :: residual_porous_tauij
    public :: residual_porous_pressure

    public :: residual_porous_wall
    public :: porous_wall_inflow_state,      porous_wall_outflow_state

    public :: newton_inflow_mach_1,    newton_outflow_mach_1
    public :: newton_inflow_mach_2,    newton_outflow_mach_2
    public :: newton_inflow_mach_3,    newton_outflow_mach_3
    public :: get_porous_plenum_pressure

    public :: newton_inflow_mach_1_ddt, newton_outflow_mach_1_ddt
    public :: newton_inflow_mach_2_ddt, newton_outflow_mach_2_ddt
    public :: newton_inflow_mach_3_ddt, newton_outflow_mach_3_ddt

    real(dp), save                           :: plenum_pressure

  contains

!========================== POROUS_WALL_FLUX ===============================80
!
!  Determines a flux for the porous wall boundary condition
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function porous_wall_flux ( grid, q_dof, ib, face_index, face_corner    &
                                , nodes_per_face                               &
                                , max_node_per_cell, xnorm, ynorm, znorm, area &
                                , eqn_set, plenum_pressure, massflow, side     &
                                ) result ( flux )

    use kinddefs,                only : dp
    use fluid,                   only : gm1
    use nml_boundary_conditions, only : solidity, set_porous_pressure          &
                                      , porous_pressure
    use ivals,                   only : pt0
    use grid_types,              only : grid_type
    use geometry_utils,          only : element_stats                          &
                                      , fetch_element_nodes                    &
                                      , return_median_center                   &
                                      , return_median_points

    use ddt,                     only : ddt5, assignment(=), operator(-),      &
                                        operator(*), operator(/), operator(+)
    use wall_model,              only : get_element_qp

    real(dp), dimension(5,5)                       :: flux

    type(grid_type),                   intent(in)  :: grid
    real(dp),          dimension(:,:), intent(in)  :: q_dof
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: face_index
    integer,                           intent(in)  :: face_corner
    integer,                           intent(in)  :: nodes_per_face
    integer,                           intent(in)  :: max_node_per_cell
    integer,                           intent(in)  :: eqn_set
    real(dp),                          intent(in)  :: xnorm
    real(dp),                          intent(in)  :: ynorm
    real(dp),                          intent(in)  :: znorm
    real(dp),                          intent(in)  :: area
    real(dp),                          intent(in)  :: plenum_pressure
    real(dp),                          intent(in)  :: massflow
    character(len=3),                  intent(in)  :: side

    integer, dimension(max_node_per_cell)    :: c2n_cell
    integer, dimension(max_node_per_cell)    :: node_map

    real(dp), dimension(0:max_node_per_cell) :: px, py, pz
    real(dp), dimension(4,3)                 :: p_bc_dual
    real(dp), dimension(3)                   :: pos_bcface_dual

    type(ddt5), dimension(5)                 :: flux_ddt
    type(ddt5)                               :: rho, u, v, w, p, e
    type(ddt5), dimension(5)                 :: bc_state
    real(dp), dimension(5,max_node_per_cell) :: qp
    real(dp), dimension(5)                   :: q_cons
    real(dp), dimension(5)                   :: q_bcface_dual
    real(dp)                                 :: p_avg, p_min

    real(dp)                                 :: s
    real(dp)                                 :: ubar
    real(dp)                                 :: p_3

    real(dp), parameter                      :: zero = 0.0_dp
    real(dp), parameter                      :: half = 0.5_dp

    integer                                  :: icell, ielem
    integer                                  :: nodes_per_cell

    integer                                  :: cell_node

  continue

    flux_ddt(1) = zero
    flux_ddt(2) = zero
    flux_ddt(3) = zero
    flux_ddt(4) = zero
    flux_ddt(5) = zero

    px            = zero
    py            = zero
    pz            = zero
!-----------------------------------------------------------------------------80
!   get the element type and cell index associated
!   with the boundary face from the face-to-node array
!   and fill local volume element information
    icell = 0
    ielem = 0
    select case( nodes_per_face )
      case( 3 )
        icell         = grid%bc(ib)%f2ntb(face_index,4)
        ielem         = grid%bc(ib)%f2ntb(face_index,5)
      case( 4 )
        icell         = grid%bc(ib)%f2nqb(face_index,5)
        ielem         = grid%bc(ib)%f2nqb(face_index,6)
    end select

    node_map(:)    = 0
    nodes_per_cell = grid%elem(ielem)%node_per_cell

    do cell_node = 1, nodes_per_cell
      node_map(cell_node) = cell_node
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

    qp = get_element_qp     ( q_dof, c2n_cell, node_map, eqn_set               &
                            , nodes_per_cell, 5 )
!-----------------------------------------------------------------------------80
! Place element geometry information in the local arrays (px,py,pz)
    call fetch_element_nodes( grid, ib, face_index, nodes_per_face, px, py, pz )

!-----------------------------------------------------------------------------80
! Extract the four corners of the dual,
! calculate boundary face dual location
! and then the flow field at the wall
    p_bc_dual     = return_median_points ( grid%x, grid%y, grid%z, grid%bc     &
                                , ib, face_index, face_corner, nodes_per_face )

    pos_bcface_dual = return_median_center ( p_bc_dual )

    px(0)           = pos_bcface_dual(1)
    py(0)           = pos_bcface_dual(2)
    pz(0)           = pos_bcface_dual(3)
    q_bcface_dual   = element_stats (5, nodes_per_cell, px, py, pz, qp )
! write(6,'(a,15(1x,f15.5))') 'bc_face:....... ', q_bcface_dual
! variables are returned primitive when get_element_qp is called, but
! the porous wall functions will be expecting conserved variables.
    q_cons          = in_conserved_variables(q_bcface_dual)
    ubar            = q_bcface_dual(2) * xnorm &
                    + q_bcface_dual(3) * ynorm &
                    + q_bcface_dual(4) * znorm

    p_avg    = plenum_pressure ! average pressure of porous boundary
    p_min    = 0.90_dp * p_avg
    if ( set_porous_pressure(ib) ) then
      p_3 = porous_pressure(ib)
    else
      p_3 = get_porous_plenum_pressure( massflow, p_avg, pt0, p_min )
    end if
!     write(6,'(a,15(1x,es15.5))') 'mdot_avg:...... ', mdot_avg
!     write(6,'(a,15(1x,es15.5))') 'p_avg:......... ', p_avg
!     write(6,'(a,15(1x,es15.5))') 'pt0............ ', pt0
!     write(6,'(a,15(1x,es15.5))') 'p_min.......... ', p_min
!     write(6,'(a,15(1x,es15.5))') 'p_3............ ', p_3
    s        = solidity(ib)
    bc_state = q_bcface_dual
!     write(6,'(a,15(1x,es15.5))') 'bc_state(1).... ', bc_state(1)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(2).... ', bc_state(2)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(3).... ', bc_state(3)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(4).... ', bc_state(4)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(5).... ', bc_state(5)

    if ( ubar > zero ) then
!   if ( p_3 < p_avg ) then
! Use porous_wall_inflow_state for flow into the plenum out of the domain

      bc_state = porous_wall_inflow_state ( q_cons, xnorm, ynorm, znorm, &
                 s, p_3 )

      rho   = bc_state(1)
      u     = bc_state(2)
      v     = bc_state(3)
      w     = bc_state(4)
      p     = bc_state(5)
      e     = p / gm1 + half*rho*(u*u+v*v+w*w)
!     ubar  = u * xnorm + v * ynorm + w * znorm

      flux_ddt(1)   = area *   rho     * ubar
      flux_ddt(2)   = area * ( rho * u * ubar + p * xnorm )
      flux_ddt(3)   = area * ( rho * v * ubar + p * ynorm )
      flux_ddt(4)   = area * ( rho * w * ubar + p * znorm )
      flux_ddt(5)   = area * ( e + p ) * ubar

    else if ( ubar <= zero ) then
!   else if ( p_3 > p_avg ) then
! Use porous_wall_outflow_state for flow into the domain out of the plenum

      bc_state = porous_wall_outflow_state ( q_cons, xnorm, ynorm, znorm, &
                 s, p_3 )
      rho   = bc_state(1)
      u     = bc_state(2)
      v     = bc_state(3)
      w     = bc_state(4)
      p     = bc_state(5)
      e     = p / gm1 + half*rho*(u*u+v*v+w*w)
!     ubar  = u * xnorm + v * ynorm + w * znorm

      flux_ddt(1)   = area *   rho * ubar
      flux_ddt(2)   = area * ( rho * u * ubar + p * xnorm )
      flux_ddt(3)   = area * ( rho * v * ubar + p * ynorm )
      flux_ddt(4)   = area * ( rho * w * ubar + p * znorm )
      flux_ddt(5)   = area * ( e + p ) * ubar

    endif

    if ( side == 'rhs' ) then
      flux(1,1) = flux_ddt(1)%f
      flux(2,1) = flux_ddt(2)%f
      flux(3,1) = flux_ddt(3)%f
      flux(4,1) = flux_ddt(4)%f
      flux(5,1) = flux_ddt(5)%f
    else if ( side == 'lhs' ) then
      flux(1,1:5) = flux_ddt(1)%d(1:5)
      flux(2,1:5) = flux_ddt(2)%d(1:5)
      flux(3,1:5) = flux_ddt(3)%d(1:5)
      flux(4,1:5) = flux_ddt(4)%d(1:5)
      flux(5,1:5) = flux_ddt(5)%d(1:5)
    endif

  end function porous_wall_flux

!======================= RESIDUAL_POROUS_TAUIJ ===============================80
!
!  Determines a weak wall boundary condition flux
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function residual_porous_tauij ( grid, q_dof, amut, ib,                 &
                                        face_index, face_corner,               &
                                        xnorm, ynorm, znorm, area,             &
                                        nodes_per_face, xmr,                   &
                                        eqn_set, unorm_bc, sink                &
                                  ) result ( flux_bc )

    use kinddefs,         only : dp
    use ddt,              only : ddt5
    use element_defs,     only : max_node_per_cell
    use grid_types,       only : grid_type
    use info_depr,        only : ntt
    use propulsion_types, only : propulsion_system
    use turbulence_info,  only : model_strain_form_int
    use wall_model,       only : wall_reynolds_stress

    real(dp),   dimension(5)                         :: flux_bc

    type(grid_type),                   intent(in)    :: grid
    real(dp),          dimension(:,:), intent(in)    :: q_dof
    real(dp),          dimension(:  ), intent(in)    :: amut
    integer,                           intent(in)    :: ib
    integer,                           intent(in)    :: face_index
    integer,                           intent(in)    :: face_corner
    real(dp),                          intent(in)    :: xnorm
    real(dp),                          intent(in)    :: ynorm
    real(dp),                          intent(in)    :: znorm
    real(dp),                          intent(in)    :: area
    integer,                           intent(in)    :: nodes_per_face
    real(dp),                          intent(in)    :: xmr
    integer,                           intent(in)    :: eqn_set
    type(propulsion_system),           intent(in)    :: sink
    real(dp),          dimension(:  ), intent(inout) :: unorm_bc

    real(dp), dimension(5)                   :: q_bc

    type(ddt5), dimension(3,3)               :: rhotauij_ddt
    real(dp),   dimension(3,3)               :: rhotauij
    real(dp)                                 :: vf2, vf3, vf4
    real(dp)                                 :: areax, areay, areaz

    real(dp), save                           :: massflow
    integer, parameter                       :: plenum_eval_freq = 10
    logical                                  :: plenum_needs_update

    real(dp), parameter                      :: zero = 0.0_dp

  continue

    plenum_needs_update = .false.
    plenum_needs_update = (  ( ntt == 1                          ) .or.        &
                          ( (ntt/plenum_eval_freq)*plenum_eval_freq == ntt ) )

    if ( plenum_needs_update ) then
       plenum_pressure = sink%pressure_area_actual / sink%flow_area
       massflow        = sink%mass_flow_actual / sink%flow_area
    end if

    q_bc = porous_wall_state ( grid, q_dof, ib,                                &
                               face_index, face_corner, nodes_per_face,        &
                               max_node_per_cell,                              &
                               xnorm, ynorm, znorm, eqn_set,                   &
                               plenum_pressure, massflow )
    write(6,'(a,12(es15.5))') 'q_bc:.....   ',q_bc

    unorm_bc(ib) = q_bc(4)
    unorm_bc(ib) = zero
!-----------------------------------------------------------------------------80
!   viscous stresses on the element boundary face
    areax        = -xnorm*area
    areay        = -ynorm*area
    areaz        = -znorm*area

    rhotauij_ddt = wall_reynolds_stress ( grid, q_dof, amut, ib,               &
                                      face_index, nodes_per_face, xmr,         &
                                  model_strain_form_int, eqn_set, unorm_bc )

    rhotauij     = rhotauij_ddt%f

    vf2   = areax*rhotauij(1,1) + areay*rhotauij(1,2) + areaz*rhotauij(1,3)
    vf3   = areax*rhotauij(2,1) + areay*rhotauij(2,2) + areaz*rhotauij(2,3)
    vf4   = areax*rhotauij(3,1) + areay*rhotauij(3,2) + areaz*rhotauij(3,3)

!-----------------------------------------------------------------------------80
    flux_bc(1)  = zero
    flux_bc(2)  = vf2
    flux_bc(3)  = vf3
    flux_bc(4)  = vf4
    flux_bc(5)  = zero

    if ( skeleton > 21 ) then
    write(6,'(a,15(1x,f20.10))') 'weak wall '
    write(6,'(a,12(es15.5))') 'tau(1,:)     ',  &
      rhotauij(1,1), rhotauij(1,2), rhotauij(1,3)
    write(6,'(a,12(es15.5))') 'tau(2,:)     ',  &
      rhotauij(2,1), rhotauij(2,2), rhotauij(2,3)
    write(6,'(a,12(es15.5))') 'tau(3,:)     ',  &
      rhotauij(3,1), rhotauij(3,2), rhotauij(3,3)
    write(6,'(a,12(es15.5))') 'mom flux     ', vf2,vf3,vf4
    endif

  end function residual_porous_tauij


!===================== RESIDUAL_POROUS_PRESSURE ==============================80
!
!  Determines a weak wall boundary condition flux
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function residual_porous_pressure ( grid, q_dof, ib                     &
                                , face_index, face_corner                      &
                                , nodes_per_face                               &
                                , max_node_per_cell                            &
                                , xnorm, ynorm, znorm, area                    &
                                , eqn_set                                      &
                                ) result ( flux_bc )

    use kinddefs,       only : dp
    use grid_types,     only : grid_type
    use nml_boundary_conditions, only : set_porous_pressure, porous_pressure

    use geometry_utils, only : return_median_points, return_median_center,     &
                               element_stats,                                  &
                               fetch_element_nodes
    use wall_model,     only : get_element_qp

    real(dp),   dimension(5)                       :: flux_bc

    type(grid_type),                   intent(in)  :: grid
    real(dp),          dimension(:,:), intent(in)  :: q_dof
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: face_index
    integer,                           intent(in)  :: face_corner
    integer,                           intent(in)  :: nodes_per_face
    integer,                           intent(in)  :: max_node_per_cell
    integer,                           intent(in)  :: eqn_set
    real(dp),                          intent(in)  :: xnorm
    real(dp),                          intent(in)  :: ynorm
    real(dp),                          intent(in)  :: znorm
    real(dp),                          intent(in)  :: area

    integer, dimension(max_node_per_cell)    :: c2n_cell
    integer, dimension(max_node_per_cell)    :: node_map

    real(dp), dimension(0:max_node_per_cell) :: px, py, pz
    real(dp), dimension(5,max_node_per_cell) :: qp
    real(dp), dimension(4,3)                 :: p_bc_dual
    real(dp), dimension(5)                   :: q_bcface_dual
    real(dp), dimension(5)                   :: state

    real(dp)                                 :: p
    real(dp)                                 :: plenum_pressure
    real(dp)                                 :: massflow
    real(dp), dimension(3)                   :: pos_bcface_dual
    real(dp)                                 :: areax, areay, areaz

    real(dp), parameter                      :: zero = 0.0_dp

    integer                                  :: icell, ielem
    integer                                  :: nodes_per_cell
    integer                                  :: cell_node

  continue

    px            = zero
    py            = zero
    pz            = zero
!-----------------------------------------------------------------------------80
!   get the element type and cell index associated
!   with the boundary face from the face-to-node array
!   and fill local volume element information
    icell = 0
    ielem = 0
    select case( nodes_per_face )
      case( 3 )
        icell         = grid%bc(ib)%f2ntb(face_index,4)
        ielem         = grid%bc(ib)%f2ntb(face_index,5)
      case( 4 )
        icell         = grid%bc(ib)%f2nqb(face_index,5)
        ielem         = grid%bc(ib)%f2nqb(face_index,6)
    end select

    node_map(:)    = 0
    nodes_per_cell = grid%elem(ielem)%node_per_cell

    do cell_node = 1, nodes_per_cell
      node_map(cell_node) = cell_node
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

    qp = get_element_qp     ( q_dof, c2n_cell, node_map, eqn_set               &
                            , nodes_per_cell, 5 )

!-----------------------------------------------------------------------------80
!   viscous stresses on the element boundary face
    areax        = -xnorm*area
    areay        = -ynorm*area
    areaz        = -znorm*area

!-----------------------------------------------------------------------------80
! Place element geometry information in the local arrays (px,py,pz)
    call fetch_element_nodes( grid, ib, face_index, nodes_per_face, px, py, pz )

!-----------------------------------------------------------------------------80
! Extract the four corners of the dual,
! calculate boundary face dual location
! and then the flow field at the wall
    p_bc_dual     = return_median_points ( grid%x, grid%y, grid%z, grid%bc     &
                                , ib, face_index, face_corner, nodes_per_face )

    pos_bcface_dual = return_median_center ( p_bc_dual )

!
    px(0)           = pos_bcface_dual(1)
    py(0)           = pos_bcface_dual(2)
    pz(0)           = pos_bcface_dual(3)
    q_bcface_dual   = element_stats (5, nodes_per_cell, px, py, pz, qp )
    if ( set_porous_pressure(ib) ) then
      p = porous_pressure(ib)
    end if
!   else
    plenum_pressure = q_bcface_dual(5)
    massflow        = 0.001_dp
      state = porous_wall_state ( grid, q_dof, ib,                             &
                                  face_index, face_corner, nodes_per_face,     &
                                  max_node_per_cell,                           &
                                  xnorm, ynorm, znorm, eqn_set,                &
                                  plenum_pressure, massflow )
!   end if
    p = state(5)
!   p = q_bcface_dual(5)
!   p =  plenum_pressure
!-----------------------------------------------------------------------------80
    flux_bc(1)  = zero
    flux_bc(2)  = p * areax
    flux_bc(3)  = p * areay
    flux_bc(4)  = p * areaz
    flux_bc(5)  = zero

    if ( skeleton > 21 ) then
    write(6,'(a,12(es15.5))') 'pres area    ', flux_bc(2:4)
    endif

  end function residual_porous_pressure

!============================ RESIDUAL_POROUS_WALL ===========================80
!
!  Yet another attempt at implementing the porous wall boundary condition
!  Assuming q in primitive variables
!=============================================================================80

       function residual_porous_wall ( q, xnorm, ynorm, znorm, area, s, p_3    &
                                     )                                         &
    result ( flux )

    use kinddefs,                 only : dp

    use fluid,                    only : gamma, gm1
!   use info_depr,                only : xmach, re, tref

    real(dp), dimension(5)                           :: flux

    real(dp), dimension(5),              intent(in)  :: q
    real(dp),                            intent(in)  :: xnorm
    real(dp),                            intent(in)  :: ynorm
    real(dp),                            intent(in)  :: znorm
    real(dp),                            intent(in)  :: area
    real(dp),                            intent(in)  :: s
    real(dp),                            intent(in)  :: p_3

    real(dp) :: rho, u, v, w, p, e, ubar, u_sq
    real(dp) :: ut, vt, wt

    real(dp) :: mach_1_sq, mach_2_sq, mach_3_sq
    real(dp) :: cf, rho_unorm, rho_unorm_sq, rho_unorm_sq_m
    real(dp) :: phi, phi0, mach_factor
    real(dp) :: pt_1, tt_1, a_1, unorm
    real(dp) :: p_1, p_2
    real(dp) :: temperature

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: half = 0.5_dp
    real(dp), parameter :: one  = 1.0_dp
    real(dp), parameter :: two  = 2.0_dp

  continue

    flux = real(0.0,dp)

    ut      = zero
    vt      = zero
    wt      = zero
    rho     = q(1)
    u       = q(2) / rho
    v       = q(3) / rho
    w       = q(4) / rho
!   p       = q(5)
    unorm       = u * xnorm + v * ynorm + w * znorm
    u_sq        = u*u + v*v + w*w
    p           = gm1 * ( q(5) - 0.5_dp*rho*u_sq )
    a_1         = sqrt( gamma * p / rho )
    mach_1_sq   = ( unorm / a_1 )**2
    mach_3_sq   = ( unorm / a_1 )**2 ! BANDAID
    tt_1        = a_1**2 * ( one + half * gm1 * mach_1_sq )
    phi0        = 0.04137_dp / (0.0982_dp+s)+ 0.57323_dp + 0.005786_dp*(one-s)

    rho_unorm_sq  = (rho*unorm)**2

  !----------------------------------------------------------------
    inflow:  if ( unorm > 0.0_dp ) then

      mach_3_sq   = mach_1_sq
      mach_factor = one + half * gm1
      phi      = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-one)

      mach_3_sq   = newton_inflow_mach_3( s, mach_1_sq, phi )

  !----------------------------------------------------------------
      mach_factor = one + half * gm1 * mach_3_sq
      rho_unorm_sq_m = gamma*gamma/tt_1*p_3*p_3*mach_3_sq*mach_factor

      if ( rho_unorm_sq > rho_unorm_sq_m ) then
        rho_unorm_sq = rho_unorm_sq_m
        mach_2_sq    = one
      else
        mach_3_sq = (sqrt( one &
        + two * gm1/gamma/gamma*tt_1*rho_unorm_sq/p_3/p_3)-one) / gm1
        cf            = rho_unorm_sq * tt_1 / ( gamma*gamma*p_3*p_3)
        mach_2_sq     = newton_inflow_mach_2( s, mach_3_sq, cf )

      endif

  !----------------------------------------------------------------
      mach_factor = one + half * gm1 * mach_2_sq
      phi         = phi0 &
                  + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-one)
      pt_1        = p_3 * (( one+gamma*mach_3_sq)                             &
                  /  ( one + gamma * mach_2_sq * phi * (one-s) ))             &
                  * mach_factor**(gamma/gm1)

      mach_1_sq   = mach_3_sq
      cf          = rho_unorm_sq * tt_1 / ( gamma*gamma * pt_1*pt_1 )
      mach_1_sq   = newton_inflow_mach_1( cf )

      rho_unorm = sqrt(rho_unorm_sq)

!=============================================================================80
    else

      mach_1_sq   = mach_3_sq
      mach_factor = one + half * gm1
      phi         = phi0 &
               + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-one)

      mach_1_sq   = newton_outflow_mach_1( s, mach_3_sq, phi )

    !----------------------------------------------------------------
      mach_factor = one + half * gm1 * mach_1_sq
      rho_unorm_sq_m = gamma*gamma/tt_1*p_3*p_3*mach_1_sq*mach_factor

      if ( rho_unorm_sq > rho_unorm_sq_m ) then
        rho_unorm_sq = rho_unorm_sq_m
        mach_2_sq    = one
      else
        mach_1_sq = (sqrt( one &
           + two * gm1/gamma/gamma*tt_1*rho_unorm_sq/p_3/p_3)-one) / gm1
        cf            = rho_unorm_sq * tt_1 / ( gamma*gamma*p_3*p_3)
        mach_2_sq     = newton_outflow_mach_2( s, mach_1_sq, cf )

    endif

    !----------------------------------------------------------------
      mach_factor = one + half * gm1 * mach_2_sq
      phi         = phi0  &
                  + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-one)
      p_2 = p_3 *((one+half*gm1*mach_3_sq)/mach_factor)**(gamma/gm1)
      mach_3_sq   = mach_1_sq
      cf          = rho_unorm_sq * tt_1 / ( gamma*gamma * p_2*p_2 )

      mach_3_sq   = newton_outflow_mach_3( s, mach_2_sq, phi, cf )


      p_1  = p_2 * ( one+gamma*mach_2_sq*phi*(one-s))&
           / (one+gamma*mach_3_sq)
      pt_1 = p_1 * ( one + half * gm1 * mach_3_sq )**(gamma/gm1)
      rho_unorm = -sqrt(rho_unorm_sq)

    endif inflow

    mach_factor = one + half * gm1 * mach_3_sq
    p           = pt_1 * mach_factor**(-gamma/gm1)
    temperature = tt_1 / mach_factor
    rho         = gamma * p / temperature
    e           = p / gm1
    ubar        = rho_unorm / rho
    u           = ut + ubar * xnorm
    v           = vt + ubar * ynorm
    w           = wt + ubar * znorm

    flux(1) = area * rho * ubar
    flux(2) = area * rho * u * ubar + xnorm * area * p
    flux(3) = area * rho * v * ubar + ynorm * area * p
    flux(4) = area * rho * w * ubar + znorm * area * p
    flux(5) = area * ( e + p ) * ubar

  end function residual_porous_wall

!============================ NEWTON_INFLOW_MACH_1 ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_inflow_mach_1 ( cf ) result ( mach_1_sq )

    use fluid,       only : gp1, gm1

    real(dp)             :: mach_1_sq

    real(dp), intent(in) :: cf

    real(dp)             :: ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: zero     = 0.0_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_1_sq = zero

    do while ( (abs(ff/fp) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      ff          = mach_1_sq * ( one + half * gm1 * mach_1_sq )**(-gp1/gm1)   &
                  - cf

      fp          = ( one + half * gm1 * mach_1_sq )**(-gp1/gm1)               &
                  * ( one - half * gp1 * mach_1_sq                             &
                  / ( one + half * gm1 * mach_1_sq ) )

      mach_1_sq = mach_1_sq - ff / fp

    end do

  end function newton_inflow_mach_1

!============================ NEWTON_INFLOW_MACH_1_DDT =======================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_inflow_mach_1_ddt ( mach_3_sq, cf ) result ( mach_1_sq )

    use fluid,       only : gp1, gm1
    use ddt,         only : ddt5, assignment(=), operator(+), operator(-)      &
                          , operator(**), operator(*), operator(/)

    type(ddt5)           :: mach_1_sq

    type(ddt5), intent(in) :: mach_3_sq
    type(ddt5), intent(in) :: cf

    type(ddt5)           :: ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_1_sq = mach_3_sq

    do while ( (abs(ff%f/fp%f) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      ff          = mach_1_sq * ( one + half * gm1 * mach_1_sq )**(-gp1/gm1)   &
                  - cf

      fp          = ( one + half * gm1 * mach_1_sq )**(-gp1/gm1)               &
                  * ( one - half * gp1 * mach_1_sq                             &
                  / ( one + half * gm1 * mach_1_sq ) )

      mach_1_sq = mach_1_sq - ff / fp
!write(6,'(a,i5,10(1x,es15.5))') 'mach_1_ddt:..', iter,ff%f,fp%f,mach_1_sq%f
    end do

  end function newton_inflow_mach_1_ddt

!============================ NEWTON_INFLOW_MACH_2 ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_inflow_mach_2 ( s, mach_3_sq, cf ) result ( mach_2_sq )

    use fluid,       only : gamma, gm1

    real(dp)             :: mach_2_sq

    real(dp), intent(in) :: s
    real(dp), intent(in) :: mach_3_sq
    real(dp), intent(in) :: cf

    real(dp)             :: phi0, phi, dphi, mach_factor, ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: zero     = 0.0_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp

    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_2_sq = zero
    phi0      = 0.04137_dp / (0.0982+s)+ 0.57323_dp + 0.005786_dp*(one-s)

    do while ( (abs(ff/fp) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      mach_factor = one + half * gm1 * mach_2_sq
      phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-one)
      dphi        = gamma * 0.0925_dp*(s**0.25)*mach_factor**(one/gm1)

      ff          = mach_2_sq * mach_factor * phi * phi * (one-s)**2           &
                  * ( ( one+gamma*mach_3_sq )                                  &
                  / (one + gamma*mach_2_sq * phi * (one-s)))**2                &
                  - cf

      fp          = ( ( one+gamma*mach_3_sq )                                  &
                  / ( one + gamma*mach_2_sq * phi * (one-s)))**2               &
                  * phi * phi * (one-s)**2 * (half*gm1*mach_2_sq + mach_factor &
                  * ( one - two * gamma * (one-s) * mach_2_sq                  &
                  * ( phi + mach_2_sq * dphi )                                 &
                  / ( one + gamma * mach_2_sq * phi * (one-s))                 &
                  + two * mach_2_sq / phi * dphi ) )

      mach_2_sq = mach_2_sq - ff / fp

    end do

  end function newton_inflow_mach_2

!============================ NEWTON_INFLOW_MACH_2_DDT =======================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_inflow_mach_2_ddt ( s, mach_3_sq, cf ) result ( mach_2_sq )

    use fluid,       only : gamma, gm1
    use ddt,         only : ddt5, assignment(=), operator(+), operator(-)      &
                          , operator(**), operator(*), operator(/)

    type(ddt5)           :: mach_2_sq

    real(dp),   intent(in) :: s
    type(ddt5), intent(in) :: mach_3_sq
    type(ddt5), intent(in) :: cf

    type(ddt5)           :: phi0, phi, dphi, mach_factor, ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: zero     = 0.0_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp

    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_2_sq = zero
    phi0      = 0.04137_dp / (0.0982+s)+ 0.57323_dp + 0.005786_dp*(one-s)

    do while ( (abs(ff%f/fp%f) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      mach_factor = one + half * gm1 * mach_2_sq
      phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_factor**(gamma/gm1)-one)
      dphi        = gamma * 0.0925_dp*(s**0.25)*mach_factor**(one/gm1)

      ff          = mach_2_sq * mach_factor * phi * phi * (one-s)**2           &
                  * ( ( one+gamma*mach_3_sq )                                  &
                  / (one + gamma*mach_2_sq * phi * (one-s)))**2                &
                  - cf

      fp          = ( ( one+gamma*mach_3_sq )                                  &
                  / ( one + gamma*mach_2_sq * phi * (one-s)))**2               &
                  * phi * phi * (one-s)**2 * (half*gm1*mach_2_sq + mach_factor &
                  * ( one - two * gamma * (one-s) * mach_2_sq                  &
                  * ( phi + mach_2_sq * dphi )                                 &
                  / ( one + gamma * mach_2_sq * phi * (one-s))                 &
                  + two * mach_2_sq / phi * dphi ) )

      mach_2_sq = mach_2_sq - ff / fp

    end do

  end function newton_inflow_mach_2_ddt

!============================ NEWTON_INFLOW_MACH_3 ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_inflow_mach_3 ( s, mach_1_sq, phi ) result ( mach_3_sq )

    use fluid,       only : gamma, gp1, gm1

    real(dp)             :: mach_3_sq

    real(dp), intent(in) :: s
    real(dp), intent(in) :: mach_1_sq
    real(dp), intent(in) :: phi

    real(dp)             :: mach_factor, ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp
    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_3_sq = mach_1_sq

    do while ( (abs(ff/fp) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1
      mach_factor = one + half * gm1 * mach_3_sq
      ff          = half * gp1 * phi * phi * ( one - s )**2                   &
                  * ( ( one+gamma*mach_3_sq ) /(one + gamma*phi*(one-s)))**2  &
                  - mach_3_sq*mach_factor
      fp          = gamma*gp1*phi*phi*((one-s)**2)                            &
                  * ( ( one+gamma*mach_3_sq ) /(one + gamma*phi*(one-s))**2)  &
                  - two*mach_factor + one

      mach_3_sq = mach_3_sq - ff / fp
    end do

  end function newton_inflow_mach_3

!============================ NEWTON_INFLOW_MACH_3_DDT =======================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_inflow_mach_3_ddt ( s, mach_1_sq, phi ) result ( mach_3_sq )

    use fluid,       only : gamma, gp1, gm1
    use ddt,         only : ddt5, assignment(=), operator(+), operator(-)      &
                          , operator(**), operator(*), operator(/)

    type(ddt5)           :: mach_3_sq

    real(dp),   intent(in) :: s
    type(ddt5), intent(in) :: mach_1_sq
    real(dp),   intent(in) :: phi

    type(ddt5)             :: mach_factor, ff, fp
    integer                :: iter

    real(dp), parameter    :: tol      = 1.0e-12_dp
    real(dp), parameter    :: half     = 0.5_dp
    real(dp), parameter    :: one      = 1.0_dp
    real(dp), parameter    :: two      = 2.0_dp
    integer, parameter     :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_3_sq = mach_1_sq

    do while ( (abs(ff%f/fp%f) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1
      mach_factor = one + half * gm1 * mach_3_sq
      ff          = half * gp1 * phi * phi * ( one - s )**2                   &
                  * ( ( one+gamma*mach_3_sq ) /(one + gamma*phi*(one-s)))**2  &
                  - mach_3_sq*mach_factor
      fp          = gamma*gp1*phi*phi*((one-s)**2)                            &
                  * ( ( one+gamma*mach_3_sq ) /(one + gamma*phi*(one-s))**2)  &
                  - two*mach_factor + one

      mach_3_sq = mach_3_sq - ff / fp
    end do

  end function newton_inflow_mach_3_ddt

!=========================== NEWTON_OUTFLOW_MACH_3 ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_outflow_mach_3 ( s, mach_2_sq, phi, cf ) result ( mach_3_sq )

    use fluid,       only : gamma, gm1

    real(dp)             :: mach_3_sq

    real(dp), intent(in) :: s
    real(dp), intent(in) :: mach_2_sq
    real(dp), intent(in) :: phi
    real(dp), intent(in) :: cf

    real(dp)             :: ff, fp, mach_factor
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp
    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_3_sq = mach_2_sq

    do while ( (abs(ff/fp) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      mach_factor = one + half*gm1*mach_3_sq
      ff          = ( ( one + gamma * mach_2_sq * phi * (one-s) )              &
                  /   ( one + gamma * mach_3_sq ) )**2                         &
                  * mach_3_sq * mach_factor - cf

      fp          = ( ( one + gamma * mach_2_sq * phi * (one-s) )              &
                  /   ( one + gamma * mach_3_sq ) )**2                         &
                  * ( mach_factor * ( one - two * gamma * mach_3_sq            &
                  / ( one + gamma * mach_3_sq ) )                              &
                  + half * gm1 * mach_3_sq )

      mach_3_sq = mach_3_sq - ff / fp

    end do

  end function newton_outflow_mach_3

!=========================== NEWTON_OUTFLOW_MACH_3_DDT =======================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_outflow_mach_3_ddt ( s, mach_2_sq, phi, cf )                 &
    result ( mach_3_sq )

    use fluid,       only : gamma, gm1
    use ddt,         only : ddt5, assignment(=), operator(+), operator(-)      &
                          , operator(**), operator(*), operator(/)

    type(ddt5)           :: mach_3_sq

    real(dp),   intent(in) :: s
    type(ddt5), intent(in) :: mach_2_sq
    type(ddt5), intent(in) :: phi
    type(ddt5), intent(in) :: cf

    type(ddt5)           :: ff, fp, mach_factor
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: zero     = 0.0_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp
    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_3_sq = zero

    do while ( (abs(ff%f/fp%f) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      mach_factor = one + half*gm1*mach_3_sq
      ff          = ( ( one + gamma * mach_2_sq * phi * (one-s) )              &
                  /   ( one + gamma * mach_3_sq ) )**2                         &
                  * mach_3_sq * mach_factor - cf

      fp          = ( ( one + gamma * mach_2_sq * phi * (one-s) )              &
                  /   ( one + gamma * mach_3_sq ) )**2                         &
                  * ( mach_factor * ( one - two * gamma * mach_3_sq            &
                  / ( one + gamma * mach_3_sq ) )                              &
                  + half * gm1 * mach_3_sq )

      mach_3_sq = mach_3_sq - ff / fp

    end do

  end function newton_outflow_mach_3_ddt

!=========================== NEWTON_OUTFLOW_MACH_2 ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_outflow_mach_2 ( s, mach_3_sq, cf ) result ( mach_2_sq )

    use fluid,       only : gamma, gm1, gp1

    real(dp)             :: mach_2_sq

    real(dp), intent(in) :: s
    real(dp), intent(in) :: mach_3_sq
    real(dp), intent(in) :: cf

    real(dp)             :: mach_2_fact, mach_3_fact
    real(dp)             :: phi0, phi, dphi, ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: zero     = 0.0_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp

    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_2_sq   = zero
    mach_3_fact = one + half * gm1 * mach_3_sq
    phi0        = 0.04137_dp / (0.0982+s)+ 0.57323_dp + 0.005786_dp*(one-s)

    do while ( (abs(ff/fp) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      mach_2_fact = one + half * gm1 * mach_2_sq
      phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_2_fact**(gamma/gm1)-one)
      dphi        = gamma * 0.0925_dp*(s**0.25)*mach_2_fact**(one/gm1)

      ff          = mach_2_sq * mach_3_fact**(two*gamma/gm1) &
                  * mach_2_fact**(-gp1/gm1)*phi*phi*(one-s)**2 - cf

      fp          = mach_3_fact**(two*gamma/gm1)                               &
                  * mach_2_fact**(-gp1/gm1)*phi*phi*(one-s)**2                 &
                  * (one-half*gp1*mach_2_sq/mach_2_fact                        &
                  + two*mach_2_sq/phi * dphi)

      mach_2_sq = mach_2_sq - ff / fp

    end do

  end function newton_outflow_mach_2

!======================= NEWTON_OUTFLOW_MACH_2_DDT ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_outflow_mach_2_ddt ( s, mach_3_sq, cf ) result ( mach_2_sq )

    use fluid,       only : gamma, gm1, gp1
    use ddt,         only : ddt5, assignment(=), operator(+), operator(-)      &
                          , operator(**), operator(*), operator(/)

    type(ddt5)             :: mach_2_sq

    real(dp),   intent(in) :: s
    type(ddt5), intent(in) :: mach_3_sq
    type(ddt5), intent(in) :: cf

    type(ddt5)             :: mach_2_fact, mach_3_fact
    type(ddt5)             :: phi0, phi, dphi, ff, fp
    integer                :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: zero     = 0.0_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp

    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_2_sq   = zero
    mach_3_fact = one + half * gm1 * mach_3_sq
    phi0        = 0.04137_dp / (0.0982+s)+ 0.57323_dp + 0.005786_dp*(one-s)

    do while ( (abs(ff%f/fp%f) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1

      mach_2_fact = one + half * gm1 * mach_2_sq
      phi         = phi0 + 0.185_dp*(s**0.25_dp)*(mach_2_fact**(gamma/gm1)-one)
      dphi        = gamma * 0.0925_dp*(s**0.25)*mach_2_fact**(one/gm1)

      ff          = mach_2_sq * mach_3_fact**(two*gamma/gm1) &
                  * mach_2_fact**(-gp1/gm1)*phi*phi*(one-s)**2 - cf

      fp          = mach_3_fact**(two*gamma/gm1)                               &
                  * mach_2_fact**(-gp1/gm1)*phi*phi*(one-s)**2                 &
                  * (one-half*gp1*mach_2_sq/mach_2_fact                        &
                  + two*mach_2_sq/phi * dphi)

      mach_2_sq = mach_2_sq - ff / fp

    end do

  end function newton_outflow_mach_2_ddt

!=========================== NEWTON_OUTFLOW_MACH_1 ===========================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_outflow_mach_1( s, mach_3_sq, phi ) result ( mach_1_sq )

    use fluid,       only : gp1, gm1

    real(dp)             :: mach_1_sq

    real(dp), intent(in) :: s
    real(dp), intent(in) :: mach_3_sq
    real(dp), intent(in) :: phi

    real(dp)             :: mach_factor, ff, fp
    integer              :: iter

    real(dp), parameter  :: tol      = 1.0e-12_dp
    real(dp), parameter  :: half     = 0.5_dp
    real(dp), parameter  :: one      = 1.0_dp
    real(dp), parameter  :: two      = 2.0_dp
    integer, parameter   :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_1_sq = mach_3_sq

    do while ( (abs(ff/fp) > tol) .and. ( iter < max_iter ) )
      iter        = iter + 1
      mach_factor = one + half * gm1 * mach_1_sq
      ff          = (two/gp1*mach_factor)**(gp1/gm1)*phi*phi*(one-s)**2       &
                  - mach_1_sq

      fp          = (two/gp1*mach_factor)**(two/gm1)*phi*phi*(one-s)**2 - one

      mach_1_sq = mach_1_sq - ff / fp

    end do

  end function newton_outflow_mach_1

!=========================== NEWTON_OUTFLOW_MACH_1_DDT =======================80
!
!  Newton solve for the Mach number at a porous boundary
!
!=============================================================================80
  function newton_outflow_mach_1_ddt( s, mach_3_sq, phi ) result ( mach_1_sq )

    use fluid,       only : gp1, gm1
    use ddt,         only : ddt5, assignment(=), operator(+), operator(-)      &
                          , operator(**), operator(*), operator(/)

    type(ddt5)           :: mach_1_sq

    real(dp),   intent(in) :: s
    type(ddt5), intent(in) :: mach_3_sq
    real(dp),   intent(in) :: phi

    type(ddt5)             :: mach_factor, ff, fp
    integer                :: iter

    real(dp), parameter    :: tol      = 1.0e-12_dp
    real(dp), parameter    :: half     = 0.5_dp
    real(dp), parameter    :: one      = 1.0_dp
    real(dp), parameter    :: two      = 2.0_dp
    integer, parameter     :: max_iter = 25

  continue

    ff   = one
    fp   = one
    iter = 0

    mach_1_sq = mach_3_sq

    do while ( (abs(ff%f/fp%f) > tol) .and. ( iter < max_iter ) )

      iter        = iter + 1
      mach_factor = one + half * gm1 * mach_1_sq
      ff          = (two/gp1*mach_factor)**(gp1/gm1)*phi*phi*(one-s)**2       &
                  - mach_1_sq

      fp          = (two/gp1*mach_factor)**(two/gm1)*phi*phi*(one-s)**2 - one

      mach_1_sq = mach_1_sq - ff / fp

    end do

  end function newton_outflow_mach_1_ddt

!============================ GET_POROUS_PLENUM_PRESSURE =====================80
!
!  Solve for the plenum pressure that gives zero net mass flux through
!  the porous bc patch
!
!=============================================================================80
  function get_porous_plenum_pressure( momdot_avg, p_avg, pt0, p_min )         &
    result ( p_plenum )

    real(dp)             :: p_plenum

    real(dp), intent(in) :: momdot_avg
    real(dp), intent(in) :: p_avg
    real(dp), intent(in) :: pt0
    real(dp), intent(in) :: p_min

    real(dp)             :: p_start, p_update

    real(dp), parameter  :: one      =  1.0_dp
    real(dp), parameter  :: c_1      = 10.0_dp
    real(dp), parameter  :: c_2      = 50.0_dp

  continue

    p_start  = p_avg

    p_update = min( ( one + c_1 * momdot_avg ) * p_start, pt0 )
    p_update = max( p_update, 1.005_dp*p_min )
    p_plenum = (c_2*p_start + p_update ) / (c_2+one)

    p_start = p_plenum

  end function get_porous_plenum_pressure

!========================= POROUS_WALL_INFLOW_STATE ==========================80
!
!  Create state vector describing conditions at a porous boundary when there
!  in an inflow condition.
!  Flow into the plenum: (p_{plenum} < p_{domain})
!  Assume q in conserved variables
!=============================================================================80

  function porous_wall_inflow_state ( q, xnorm, ynorm, znorm, s, p_3 )


    use fluid, only : gamma, gm1
    use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
                    , operator(-), operator(*), operator(/)           &
                    , operator(**), ddt_sqrt

    type(ddt5), dimension(5)              :: porous_wall_inflow_state

    real(dp), dimension(5),   intent(in)  :: q
    real(dp),                 intent(in)  :: xnorm
    real(dp),                 intent(in)  :: ynorm
    real(dp),                 intent(in)  :: znorm
    real(dp),                 intent(in)  :: s
    real(dp),                 intent(in)  :: p_3


    real(dp)                 :: phi, phi0
    real(dp), dimension(3)   :: normal

    type(ddt5), dimension(5) :: q_ddt
    type(ddt5)               :: rho_ddt, u_ddt, v_ddt, w_ddt, p_ddt, pt_ddt
    type(ddt5)               :: u_sq_ddt, tt_ddt, rho_unorm_sq_ddt
    type(ddt5)               :: unorm_ddt, asq_ddt
    type(ddt5)               :: t_ddt
    type(ddt5)               :: mach_1_sq, mach_2_sq, mach_3_sq
    type(ddt5)               :: mach_1_factor, mach_2_factor, mach_3_factor
    type(ddt5)               :: rho_unorm_sq_m_ddt
    type(ddt5)               :: phi_ddt
    type(ddt5)               :: cf_ddt

    real(dp), parameter :: half = 0.5_dp
    real(dp), parameter :: one  = 1.0_dp
    real(dp), parameter :: two  = 2.0_dp

  continue

    phi0   = 0.04137_dp/(0.0982+s) + 0.57323 + 0.005786*(one-s)

    normal = (/xnorm,ynorm,znorm/)
    q_ddt  = ddt5_identity(q)

    rho_ddt           = q_ddt(1)
    u_ddt             = q_ddt(2) / rho_ddt
    v_ddt             = q_ddt(3) / rho_ddt
    w_ddt             = q_ddt(4) / rho_ddt
    unorm_ddt         = u_ddt*normal(1) + v_ddt*normal(2) + w_ddt*normal(3)
    u_sq_ddt          = u_ddt*u_ddt + v_ddt*v_ddt + w_ddt*w_ddt
    p_ddt             = gm1 * ( q_ddt(5) - half*rho_ddt*u_sq_ddt )
    asq_ddt           = gamma * p_ddt / rho_ddt
    mach_1_sq         = unorm_ddt**2/asq_ddt
    tt_ddt            = asq_ddt * ( one + half * gm1 * mach_1_sq )
    rho_unorm_sq_ddt  = ( rho_ddt * unorm_ddt )**2

    !----------------------------------------------------------------
    ! inflow_outflow:  if ( unorm1 > 0.0_dp ) then

    mach_3_sq     = mach_1_sq
    mach_3_factor = one + half * gm1
    phi           = phi0 + 0.185*s**0.25_dp*(mach_3_factor**(gamma/gm1)-one)
    mach_3_sq     = newton_inflow_mach_3_ddt ( s, mach_1_sq, phi )

!write(6,'(a,10(1x,es15.5))') 'inflow:mach_3_sq:.........',mach_3_sq
    !----------------------------------------------------------------
    mach_3_factor      = one + half * gm1 * mach_3_sq
    rho_unorm_sq_m_ddt = gamma*gamma/tt_ddt*p_3*p_3*mach_3_sq*mach_3_factor

    if ( rho_unorm_sq_ddt%f > rho_unorm_sq_m_ddt%f ) then
      rho_unorm_sq_ddt  = rho_unorm_sq_m_ddt
      mach_2_sq         = one
    else
      mach_3_sq  = (ddt_sqrt( one + two * gm1 * tt_ddt * rho_unorm_sq_ddt &
                  /gamma/gamma/p_3/p_3)-one)/gm1
      cf_ddt     = rho_unorm_sq_ddt*tt_ddt/(gamma*gamma*p_3*p_3)
      mach_2_sq  = newton_inflow_mach_2_ddt( s, mach_3_sq, cf_ddt )

    endif

!write(6,'(a,10(1x,es15.5))') 'inflow:mach_2_sq:.........',mach_2_sq
    !----------------------------------------------------------------
    mach_2_factor = one + half * gm1 * mach_2_sq
    phi_ddt       = phi0 + 0.185_dp*s**0.25_dp                               &
                  *( mach_2_factor**(gamma/gm1)-one)
    pt_ddt        = p_3 * ( ( one+gamma * mach_3_sq )                        &
                  / ( one + gamma * mach_2_sq * phi_ddt * (one-s) ) )        &
                  * mach_2_factor**(gamma/gm1)
!write(6,'(a,10(1x,es15.5))') 'mach_2_factor:............',mach_2_factor
!write(6,'(a,10(1x,es15.5))') 'phi_ddt:..................',phi_ddt
!write(6,'(a,10(1x,es15.5))') 'pt_ddt:...................',pt_ddt
!write(6,'(a,10(1x,es15.5))') 'p_3:......................',p_3

    cf_ddt        = rho_unorm_sq_ddt*tt_ddt/(gamma*gamma*pt_ddt*pt_ddt)
!write(6,'(a,10(1x,es15.5))') 'cf_ddt:...................',cf_ddt
    mach_1_sq     = newton_inflow_mach_1_ddt( mach_3_sq, cf_ddt )

!write(6,'(a,10(1x,es15.5))') 'inflow:mach_1_sq:.........',mach_1_sq
!=============================================================================80

    mach_1_factor = one + half * gm1 * mach_1_sq
    p_ddt         = pt_ddt * mach_1_factor**(-gamma/gm1)
    t_ddt         = tt_ddt / mach_1_factor
    rho_ddt       = gamma * p_ddt / t_ddt
!   e_ddt         = p_ddt / gm1
    unorm_ddt     = ddt_sqrt(rho_unorm_sq_ddt)/rho_ddt
    u_ddt         = unorm_ddt * normal(1)
    v_ddt         = unorm_ddt * normal(2)
    w_ddt         = unorm_ddt * normal(3)

    porous_wall_inflow_state(1) = rho_ddt
    porous_wall_inflow_state(2) = u_ddt
    porous_wall_inflow_state(3) = v_ddt
    porous_wall_inflow_state(4) = w_ddt
    porous_wall_inflow_state(5) = p_ddt

  end function porous_wall_inflow_state

!========================= POROUS_WALL_OUTFLOW_STATE =========================80
!
!  Yet another attempt at implementing the porous wall boundary condition
!  Assume q in conserved variables
!  Flow out of the plenum: (p_{plenum} > p_{domain})
!=============================================================================80
  function porous_wall_outflow_state ( q, xnorm, ynorm, znorm, s, p_1 )

    use fluid, only : gamma, gm1
    use ddt,   only : ddt5, ddt5_identity, assignment(=), operator(+) &
                    , operator(-), operator(*), operator(/)           &
                    , operator(**), ddt_sqrt

    type(ddt5), dimension(5)            :: porous_wall_outflow_state

    real(dp), dimension(5), intent(in)  :: q
    real(dp),               intent(in)  :: xnorm
    real(dp),               intent(in)  :: ynorm
    real(dp),               intent(in)  :: znorm
    real(dp),               intent(in)  :: s
    real(dp),               intent(in)  :: p_1

    real(dp)                 :: phi, phi0
    real(dp)                 :: mach_factor
    real(dp), dimension(3)   :: normal

    type(ddt5), dimension(5) :: q_ddt
    type(ddt5)               :: rho_ddt, u_ddt, v_ddt, w_ddt, p_ddt
    type(ddt5)               :: u_sq_ddt, tt_ddt, rho_unorm_sq_ddt
    type(ddt5)               :: unorm_ddt, asq_ddt
    type(ddt5)               :: t_ddt
    type(ddt5)               :: mach_1_sq,     mach_2_sq,     mach_3_sq
    type(ddt5)               :: mach_1_factor, mach_2_factor, mach_3_factor
    type(ddt5)               :: rho_unorm_sq_m_ddt
    type(ddt5)               :: phi_ddt
    type(ddt5)               :: cf_ddt
    type(ddt5)               :: p_2
    type(ddt5)               :: pt_3

    real(dp), parameter :: half = 0.5_dp
    real(dp), parameter :: one  = 1.0_dp
    real(dp), parameter :: two  = 2.0_dp

  continue

    phi0   = 0.04137_dp/(0.0982+s) + 0.57323 + 0.005786*(one-s)

    normal = (/xnorm,ynorm,znorm/)
    q_ddt  = ddt5_identity(q)

    rho_ddt           = q_ddt(1)
    u_ddt             = q_ddt(2) / rho_ddt
    v_ddt             = q_ddt(3) / rho_ddt
    w_ddt             = q_ddt(4) / rho_ddt
    unorm_ddt         = u_ddt*normal(1) + v_ddt*normal(2) + w_ddt*normal(3)
    u_sq_ddt          = u_ddt * u_ddt + v_ddt * v_ddt + w_ddt * w_ddt
    p_ddt             = gm1 * ( q_ddt(5) - half*rho_ddt*u_sq_ddt )
    asq_ddt           = gamma * p_ddt / rho_ddt
    mach_3_sq         = unorm_ddt**2/asq_ddt
    tt_ddt            = asq_ddt * ( one + half * gm1 * mach_3_sq )
    rho_unorm_sq_ddt  = ( rho_ddt * unorm_ddt )**2

    !----------------------------------------------------------------
    ! else if ( unorm1 < 0.0_dp ) then

    mach_1_sq   = mach_3_sq
    mach_factor = one + half * gm1
    phi         = phi0 + 0.185*s**0.25_dp*(mach_factor**(gamma/gm1)-one)
    mach_1_sq   = newton_outflow_mach_1_ddt ( s, mach_3_sq, phi )

    !----------------------------------------------------------------
    mach_1_factor      = one + half * gm1 * mach_1_sq
    rho_unorm_sq_m_ddt = gamma*gamma/tt_ddt*p_1*p_1*mach_1_sq*mach_1_factor

    if ( rho_unorm_sq_ddt%f > rho_unorm_sq_m_ddt%f ) then
      rho_unorm_sq_ddt  = rho_unorm_sq_m_ddt
      mach_2_sq         = one
    else
      mach_1_sq  = (ddt_sqrt( one + two * gm1 * tt_ddt * rho_unorm_sq_ddt &
                  /gamma/gamma/p_1/p_1)-one)/gm1
      cf_ddt     = rho_unorm_sq_ddt*tt_ddt/(gamma*gamma*p_1*p_1)
      mach_2_sq  = newton_outflow_mach_2_ddt( s, mach_3_sq, cf_ddt )

    endif

    !----------------------------------------------------------------
    mach_1_factor = one + half * gm1 * mach_1_sq
    mach_2_factor = one + half * gm1 * mach_2_sq
    phi_ddt       = phi0 + 0.185_dp*s**0.25_dp                                 &
                  *( mach_2_factor**(gamma/gm1)-one)
    p_2           = p_1 * ( mach_1_factor / mach_2_factor )**(gamma/gm1)

    cf_ddt        = rho_unorm_sq_ddt*tt_ddt/(gamma*gamma*p_2*p_2)
    mach_3_sq     = newton_outflow_mach_3_ddt( s, mach_2_sq, phi_ddt, cf_ddt )
    mach_3_factor = one + half * gm1 * mach_3_sq

    pt_3          = p_2 * ( (one+gamma*mach_2_sq*phi_ddt*(one-s))              &
                  / ( one + gamma*mach_3_sq ) )                                &
                  * mach_3_factor**(gamma/gm1)
!=============================================================================80

    p_ddt         = pt_3 * mach_3_factor**(-gamma/gm1)
    t_ddt         = tt_ddt / mach_3_factor
    rho_ddt       = gamma * p_ddt / t_ddt
    unorm_ddt     = ddt_sqrt(rho_unorm_sq_ddt)/rho_ddt
    u_ddt         = unorm_ddt * normal(1)
    v_ddt         = unorm_ddt * normal(2)
    w_ddt         = unorm_ddt * normal(3)

    porous_wall_outflow_state(1) = rho_ddt
    porous_wall_outflow_state(2) = u_ddt
    porous_wall_outflow_state(3) = v_ddt
    porous_wall_outflow_state(4) = w_ddt
    porous_wall_outflow_state(5) = p_ddt

  end function porous_wall_outflow_state

!========================== POROUS_WALL_PRESSURE ===========================80
!
!  Determines the pressure for a porous wall boundary condition
!  This function is called for each dual ( face_corner )
!  of every boundary element ( face_index )
!
!=============================================================================80
       function porous_wall_state ( grid, q_dof, ib,                           &
                                  face_index, face_corner, nodes_per_face,     &
                                  max_node_per_cell,                           &
                                  xnorm, ynorm, znorm, eqn_set,                &
                                  plenum_pressure, massflow                    &
                                ) result ( bc_state )

    use kinddefs,                only : dp
    use nml_boundary_conditions, only : solidity, set_porous_pressure          &
                                      , porous_pressure
    use ivals,                   only : pt0
    use grid_types,              only : grid_type
    use geometry_utils,          only : element_stats                          &
                                      , fetch_element_nodes                    &
                                      , return_median_center                   &
                                      , return_median_points

    use ddt,                     only : ddt5, assignment(=), operator(-),      &
                                        operator(*), operator(/), operator(+)
    use wall_model,              only : get_element_qp

    real(dp),   dimension(5)                 :: bc_state

    type(grid_type),                   intent(in)  :: grid
    real(dp),          dimension(:,:), intent(in)  :: q_dof
    integer,                           intent(in)  :: ib
    integer,                           intent(in)  :: face_index
    integer,                           intent(in)  :: face_corner
    integer,                           intent(in)  :: nodes_per_face
    integer,                           intent(in)  :: max_node_per_cell
    integer,                           intent(in)  :: eqn_set
    real(dp),                          intent(in)  :: xnorm
    real(dp),                          intent(in)  :: ynorm
    real(dp),                          intent(in)  :: znorm
    real(dp),                          intent(in)  :: plenum_pressure
    real(dp),                          intent(in)  :: massflow

    integer, dimension(max_node_per_cell)    :: c2n_cell
    integer, dimension(max_node_per_cell)    :: node_map

    real(dp), dimension(0:max_node_per_cell) :: px, py, pz
    real(dp), dimension(4,3)                 :: p_bc_dual
    real(dp), dimension(3)                   :: pos_bcface_dual

    type(ddt5), dimension(5)                 :: bc_state_ddt

    real(dp), dimension(5,max_node_per_cell) :: qp
    real(dp), dimension(5)                   :: q_cons
    real(dp), dimension(5)                   :: q_bcface_dual
    real(dp)                                 :: p_avg, p_min

    real(dp)                                 :: s
    real(dp)                                 :: ubar
    real(dp)                                 :: p_3

    real(dp), parameter                      :: zero = 0.0_dp

    integer                                  :: icell, ielem
    integer                                  :: nodes_per_cell

    integer                                  :: cell_node

  continue

    px            = zero
    py            = zero
    pz            = zero
!-----------------------------------------------------------------------------80
!   get the element type and cell index associated
!   with the boundary face from the face-to-node array
!   and fill local volume element information
    icell = 0
    ielem = 0
    select case( nodes_per_face )
      case( 3 )
        icell         = grid%bc(ib)%f2ntb(face_index,4)
        ielem         = grid%bc(ib)%f2ntb(face_index,5)
      case( 4 )
        icell         = grid%bc(ib)%f2nqb(face_index,5)
        ielem         = grid%bc(ib)%f2nqb(face_index,6)
    end select

    node_map(:)    = 0
    nodes_per_cell = grid%elem(ielem)%node_per_cell

    do cell_node = 1, nodes_per_cell
      node_map(cell_node) = cell_node
      c2n_cell(cell_node) = grid%elem(ielem)%c2n(cell_node,icell)
    end do

    qp = get_element_qp     ( q_dof, c2n_cell, node_map, eqn_set               &
                            , nodes_per_cell, 5 )

!-----------------------------------------------------------------------------80
! Place element geometry information in the local arrays (px,py,pz)
    call fetch_element_nodes( grid, ib, face_index, nodes_per_face, px, py, pz )

!-----------------------------------------------------------------------------80
! Extract the four corners of the dual,
! calculate boundary face dual location
! and then the flow field at the wall
    p_bc_dual     = return_median_points ( grid%x, grid%y, grid%z, grid%bc     &
                                , ib, face_index, face_corner, nodes_per_face )

    pos_bcface_dual = return_median_center ( p_bc_dual )

    px(0)           = pos_bcface_dual(1)
    py(0)           = pos_bcface_dual(2)
    pz(0)           = pos_bcface_dual(3)
    q_bcface_dual   = element_stats (5, nodes_per_cell, px, py, pz, qp )
! write(6,'(a,15(1x,f15.5))') 'bc_face:....... ', q_bcface_dual
! variables are returned primitive when get_element_qp is called, but
! the porous wall functions will be expecting conserved variables.
    q_cons          = in_conserved_variables(q_bcface_dual)
    ubar            = q_bcface_dual(2) * xnorm &
                    + q_bcface_dual(3) * ynorm &
                    + q_bcface_dual(4) * znorm

    p_avg    = plenum_pressure ! average pressure of porous boundary
    p_min    = 0.90_dp * p_avg
      write(6,'(a,l2           )') 'set pressure:.. ', set_porous_pressure(ib)
      write(6,'(a,15(1x,es15.5))') 'porous_pressure ', porous_pressure(ib)
    if ( set_porous_pressure(ib) ) then
      p_3 = porous_pressure(ib)
    else
      p_3 = get_porous_plenum_pressure( massflow, p_avg, pt0, p_min )
    end if
      write(6,'(a,15(1x,es15.5))') 'massflow:...... ', massflow
      write(6,'(a,15(1x,es15.5))') 'p_avg:......... ', p_avg
      write(6,'(a,15(1x,es15.5))') 'pt0............ ', pt0
      write(6,'(a,15(1x,es15.5))') 'p_min.......... ', p_min
      write(6,'(a,15(1x,es15.5))') 'p_3............ ', p_3
      write(6,'(a,15(1x,es15.5))') 'ubar:.......... ', ubar
    s        = solidity(ib)
    bc_state = q_bcface_dual
!     write(6,'(a,15(1x,es15.5))') 'bc_state(1).... ', bc_state(1)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(2).... ', bc_state(2)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(3).... ', bc_state(3)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(4).... ', bc_state(4)
!     write(6,'(a,15(1x,es15.5))') 'bc_state(5).... ', bc_state(5)

    if ( ubar > zero ) then
!   if ( p_3 < p_avg ) then
! Use porous_wall_inflow_state for flow into the plenum out of the domain

      bc_state_ddt = porous_wall_inflow_state ( q_cons, xnorm, ynorm, znorm, &
                 s, p_3 )

    else if ( ubar <= zero ) then
!   else if ( p_3 > p_avg ) then
! Use porous_wall_outflow_state for flow into the domain out of the plenum

      bc_state_ddt = porous_wall_outflow_state ( q_cons, xnorm, ynorm, znorm, &
                 s, p_3 )
    endif

    bc_state(1:5) = bc_state_ddt(1:5)%f

  end function porous_wall_state

include 'in_conserved_variables.f90'

end module porous_model

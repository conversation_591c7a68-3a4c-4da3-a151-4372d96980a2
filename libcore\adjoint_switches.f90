module adjoint_switches

  use kinddefs, only : dp

  implicit none

  private

  public :: debug_linearizations, low_mem_meshsens, windowing, get_dldx
  public :: always_recompute, store_full_stencil, timedep_adj_frozen
  public :: outer_loop_krylov, getgrad, new_krylov, use_window_indices
  public :: rad, sboom_active, afc_jets, use_bp_model, ae_cost, n_ae_fcns
  public :: sonic_boom_cost, press_box_cost, rn, np, pstag_cost
  public :: locally_optimal, locally_optimal_freq, show_surface_sensitivity
  public :: slice_orientation, disk_radius, x_disk_origin, y_disk_origin
  public :: z_disk_origin, integrand_type, gid, xmin, xmax, ymin, ymax, zmin
  public :: zmax, override_bc_limitation, write_for_chaos, read_for_chaos
  public :: read_initial_field, write_initial_field, write_final_field

! true monitors linearizations at a specified point and perturbation
  logical, parameter :: debug_linearizations = .false.

! true engages locally optimal scheme for time-dependent adjoint
  logical :: locally_optimal = .false.

! true stores off dL/dX
  logical :: get_dldx = .false.

! controls frequency of locally optimal scheme
  integer :: locally_optimal_freq = 1

! true recomputes fcnadt residual, false uses a stored matrix for speed
  logical :: always_recompute = .false.

! store full A in adjoint
  logical :: store_full_stencil = .false.

! true wraps the relax routine with krylov
  logical :: outer_loop_krylov = .false.
  logical :: new_krylov = .false.

! make the adjoint solver compute RAD (residual adjoint dot-product) for
! error correction and adaptation parameter
  logical :: rad = .false.

! Pressure integration adjoint cost function for sonic boom
  logical :: sonic_boom_cost = .false.

! SBOOM is in play
  logical :: sboom_active = .false.

! Pressure norm adjoint cost function
  logical :: press_box_cost = .false.

! RMS of stagnation pressure adjoint cost function
  logical :: pstag_cost = .false.

! Equivalent area adjoint cost function
  logical :: ae_cost = .false.

! Number of equivalent area adjoint cost functions present in rubber.data
  integer :: n_ae_fcns

! Run getgrad after steady adjoint
  logical :: getgrad = .false.

! If true, avoids storing the surface mesh sensitivities; sacrifices speed
! for reduced memory
  logical :: low_mem_meshsens = .false.

  integer  :: rn, np  ! debugging: residual node, node to perturb

  logical  :: afc_jets = .false. ! Hack to simulate AFC jets
  logical  :: windowing = .false. ! Apply windowing to cost function
  logical :: timedep_adj_frozen = .false. ! Time-dep adjoint, frozen jacs

! use the user-supplied time integration window indices in applying the
! cost function
  logical :: use_window_indices = .true.

! ignore any geometric parameterization and simply compute df/dx, df/dy,
! and df/dz as if every surface mesh point coordinate were a design variable
  logical :: show_surface_sensitivity = .false.

! Hack to provide adjoint for Jim's separate implementation of SA model
  logical :: use_bp_model  = .false.

! pstag cost function

  integer :: slice_orientation

  real(dp) :: x_disk_origin
  real(dp) :: y_disk_origin
  real(dp) :: z_disk_origin
  real(dp) :: disk_radius

! pressure box cost function

  integer :: integrand_type, gid

  real(dp) :: xmin, xmax, ymin, ymax, zmin, zmax

! Allow sensitivity analysis for cases involving element-based BC's

  logical :: override_bc_limitation = .false.

! write out stuff for chaos testing

  logical :: write_for_chaos = .false.

! read in chaotic adjoint solution

  logical :: read_for_chaos = .false.

! write out initial field for chaos testing

  logical :: write_initial_field = .false.

! write out final field for chaos testing

  logical :: write_final_field = .false.

! read initial field for chaos testing

  logical :: read_initial_field = .false.

end module adjoint_switches

!================================= FLUX_CONV =================================80
!
! Flux difference split convection fluxes(within incompressible path).
!
!=============================================================================80

  pure function flux_conv( xnorm, ynorm, znorm, area, ql, qr )

    use kinddefs,        only : dp
    use convection_defs, only : cu, cv, cw

    real(dp), intent(in) :: xnorm, ynorm, znorm, area

    real(dp), dimension(4), intent(in) :: ql, qr
    real(dp), dimension(4)             :: flux_conv

    real(dp), dimension(4) :: fluxp, fluxm
    real(dp) :: ubar, eig1

  continue

    ubar  = xnorm*cu + ynorm*cv + znorm*cw

    eig1 = abs(ubar)

    fluxp(:) = ubar*ql(:)

    fluxm(:) = ubar*qr(:)

    flux_conv(:) = 0.5_dp*area*( fluxp(:) + fluxm(:) - eig1*(qr(:)-ql(:)) )

  end function flux_conv

!================================= QT_FROM_QC ================================80
!
! Variables (var1,u,v,w,T) from conserved variables.
!
!=============================================================================80

  pure function qt_from_qc( eqn_set, n_q, qc )

    use flux_constants, only : ggm1
    use solution_types, only : compressible

    integer,                intent(in) :: eqn_set, n_q
    real(dp), dimension(:), intent(in) :: qc

    real(dp), dimension(n_q)           :: qt_from_qc

    real(dp) :: ri

  continue

    qt_from_qc(1:n_q) = qc(1:n_q)

    if ( eqn_set == compressible ) then
      ri = 1.0_dp/qt_from_qc(1)

      qt_from_qc(2:4) = qt_from_qc(2:4)*ri

      qt_from_qc(5)   =   ggm1*( qt_from_qc(5)*ri              &
                      - 0.5_dp*( qt_from_qc(2)*qt_from_qc(2)   &
                               + qt_from_qc(3)*qt_from_qc(3)   &
                               + qt_from_qc(4)*qt_from_qc(4) ) )
    endif

  end function qt_from_qc

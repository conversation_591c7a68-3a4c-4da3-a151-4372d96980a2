!================================  TURB_FLUCTUATIONS =========================80
!
!  Computes turbulent fluctuation data such as u'v' at a point
!
!=============================================================================80

  pure function turb_fluctuations( n_grd, n_tot, n_turb, eqn_set, qnode, amut, &
                                   gradx, grady, gradz, turb )

    use kinddefs,       only : dp
    use info_depr,      only : re, xmach
    use solution_types, only : compressible
    use turbulence_info,only : turbulence_model_int, alg_re_stress_model,      &
                               WilcoxRSM_w2006c, WilcoxRSM_w2006,              &
                               SSGLRR_RSM_w2012_SD, SSGLRR_RSM_w2012
    use compute_stress, only : get_rhotau_qcr

    integer,                    intent(in) :: n_grd, n_tot, n_turb, eqn_set
    real(dp), dimension(n_tot), intent(in) :: qnode
    real(dp), dimension(n_turb),intent(in) :: turb
    real(dp), dimension(n_grd), intent(in) :: gradx, grady, gradz
    real(dp),                   intent(in) :: amut

    real(dp), dimension(6)   :: turb_fluctuations
    real(dp), dimension(3,3) :: tau, tau_turb, gradv

    real(dp) :: s11, s22, s33, s12, s13, s23
    real(dp) :: xis, zk, re_xm, u_inf

  continue

    tau   = 0.0_dp
    gradv = 0.0_dp

    if ( eqn_set == compressible ) then
      re_xm     = re/xmach
      u_inf     = xmach
    else
      re_xm     = re
      u_inf     = 1._dp
    end if

    if (n_turb == 7) then
!     if 7-eqn model, then turb already has desired info; just need to scale it
      if (turbulence_model_int == WilcoxRSM_w2006c) then
!       u'u'/(u_inf)**2:
        turb_fluctuations(1) = -turb(1)/qnode(1)/u_inf**2
!       v'v'/(u_inf)**2:
        turb_fluctuations(2) = -turb(2)/qnode(1)/u_inf**2
!       w'w'/(u_inf)**2:
        turb_fluctuations(3) = -turb(3)/qnode(1)/u_inf**2
!       u'v'/(u_inf)**2:
        turb_fluctuations(4) = -turb(4)/qnode(1)/u_inf**2
!       u'w'/(u_inf)**2:
        turb_fluctuations(5) = -turb(5)/qnode(1)/u_inf**2
!       v'w'/(u_inf)**2:
        turb_fluctuations(6) = -turb(6)/qnode(1)/u_inf**2
      else if (turbulence_model_int == WilcoxRSM_w2006 .or.     &
               turbulence_model_int == SSGLRR_RSM_w2012_SD .or. &
               turbulence_model_int == SSGLRR_RSM_w2012) then
!       u'u'/(u_inf)**2:
        turb_fluctuations(1) = -turb(1)/u_inf**2
!       v'v'/(u_inf)**2:
        turb_fluctuations(2) = -turb(2)/u_inf**2
!       w'w'/(u_inf)**2:
        turb_fluctuations(3) = -turb(3)/u_inf**2
!       u'v'/(u_inf)**2:
        turb_fluctuations(4) = -turb(4)/u_inf**2
!       u'w'/(u_inf)**2:
        turb_fluctuations(5) = -turb(5)/u_inf**2
!       v'w'/(u_inf)**2:
        turb_fluctuations(6) = -turb(6)/u_inf**2
      end if


    else

      gradv(1,1) = gradx(2)
      gradv(1,2) = grady(2)
      gradv(1,3) = gradz(2)
      gradv(2,1) = gradx(3)
      gradv(2,2) = grady(3)
      gradv(2,3) = gradz(3)
      gradv(3,1) = gradx(4)
      gradv(3,2) = grady(4)
      gradv(3,3) = gradz(4)

!     do for non-RSM models (valid for linear eddy-viscosity models & QCR only!)
!     FIXME - other nonlinear models will need to bring in tau_turb via
!     non-Boussinesq relationship
      s11 = gradx(2)
      s22 = grady(3)
      s33 = gradz(4)

      s12 = 0.5*(grady(2) + gradx(3))
      s13 = 0.5*(gradz(2) + gradx(4))
      s23 = 0.5*(gradz(3) + grady(4))

      xis = s11*s11       + s22*s22       + s33*s33                            &
        + 2._dp*s12*s12 + 2._dp*s13*s13 + 2._dp*s23*s23

      tau(1,1) = -2._dp*amut*(s11 - (s11+s22+s33)/3._dp)
      tau(2,2) = -2._dp*amut*(s22 - (s11+s22+s33)/3._dp)
      tau(3,3) = -2._dp*amut*(s33 - (s11+s22+s33)/3._dp)

      tau(1,2) = -2._dp*amut*s12
      tau(1,3) = -2._dp*amut*s13
      tau(2,3) = -2._dp*amut*s23
      tau(2,1) = tau(1,2)
      tau(3,1) = tau(1,3)
      tau(3,2) = tau(2,3)

      tau_turb = tau

      if (alg_re_stress_model  == 2 ) then
        tau_turb = get_rhotau_qcr( gradv, tau )
      end if

!   to get better postprocessed approx to normal stresses,
!   need to add 2/3 rho k term; SA model does not have k available,
!   so it is approximated

      if (n_turb == 1) then
        zk = amut/qnode(1)*sqrt(2._dp*xis)/(re_xm*.31_dp)
      else
        zk = turb(1)
      end if

      tau_turb(1,1) = tau_turb(1,1) + 2._dp*qnode(1)*zk*re_xm/3._dp
      tau_turb(2,2) = tau_turb(2,2) + 2._dp*qnode(1)*zk*re_xm/3._dp
      tau_turb(3,3) = tau_turb(3,3) + 2._dp*qnode(1)*zk*re_xm/3._dp

!     u'u'/(u_inf)**2:
      turb_fluctuations(1) = tau_turb(1,1)/(qnode(1)*re_xm)/u_inf**2

!     v'v'/(u_inf)**2:
      turb_fluctuations(2) = tau_turb(2,2)/(qnode(1)*re_xm)/u_inf**2

!     w'w'/(u_inf)**2:
      turb_fluctuations(3) = tau_turb(3,3)/(qnode(1)*re_xm)/u_inf**2

!     u'v'/(u_inf)**2:
      turb_fluctuations(4) = tau_turb(1,2)/(qnode(1)*re_xm)/u_inf**2

!     u'w'/(u_inf)**2:
      turb_fluctuations(5) = tau_turb(1,3)/(qnode(1)*re_xm)/u_inf**2

!     v'w'/(u_inf)**2:
      turb_fluctuations(6) = tau_turb(2,3)/(qnode(1)*re_xm)/u_inf**2

    end if

  end function turb_fluctuations

!
! DO NOT EDIT this file.  It was generated by the ruby script,
!
! Instead,
! 1. Edit line_nsolver.template
! 2. Regenerate this file through the command below:
!               ruby LinAlg.rb line_nsolver
! 3. Commit line_nsolver.f90 and line_nsolver.template
!
module line_nsolver

  use kinddefs,        only : jp, dqp

  implicit none

  private

  public :: line_nsolve

contains

!================================ LINE_NSOLVE ================================80
!
! Routes the code into the appropriate line solve routine
!
!=============================================================================80
  subroutine line_nsolve( nb, dof_in_line, f, a, b, c, lu_offset,              &
                          force_general_path)

    integer, intent(in)           :: nb, dof_in_line, lu_offset
    integer, intent(in), optional :: force_general_path

    real(dqp), dimension(:,:),   intent(inout) :: f
    real(jp),  dimension(:,:,:), intent(in)    :: a, b, c

  continue

    if(.not. present(force_general_path)) then

      select case (nb)
      case(6)
        call line_solve_6( dof_in_line, f, a, b, c, lu_offset )
      case(5)
        call line_solve_5( dof_in_line, f, a, b, c, lu_offset )
      case(4)
        call line_solve_4( dof_in_line, f, a, b, c, lu_offset )
      case(3)
        call line_solve_3( dof_in_line, f, a, b, c, lu_offset )
      case(2)
        call line_solve_2( dof_in_line, f, a, b, c, lu_offset )
      case(1)
        call line_solve_1( dof_in_line, f, a, b, c, lu_offset )
      case default

        call line_solve_n( nb, dof_in_line, f, a, b, c, lu_offset )

      end select

    else

      if(force_general_path == 1) then

!       General block line solve

        call line_solve_n( nb, dof_in_line, f, a, b, c, lu_offset )
      endif

    endif

  end subroutine line_nsolve


!================================ LINE_SOLVE_N ===============================80
!
! Performs direct line solve on block tridiagonal nxn
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D.
!
!=============================================================================80
  subroutine line_solve_n( nb, dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: nb, dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in)    :: a, b, c

    integer :: i, il, iu, m, l, offset
    integer :: nb1, l1, l0, m1, m0, mm, mm1

    real(jp) :: apv

  continue

    nb1 = nb - 1

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      if( iu>il )then
        do m = il,iu-1
          m1 = m + 1
          l = m + offset
          l1 = l + 1
          do mm = 1,nb1
            mm1 = mm + 1
            apv = 1._jp / b(mm,mm,l)
            do i = mm1,nb
              f(i,m) = f(i,m) - f(mm,m)*b(i,mm,l)*apv
            end do
            do i = 1,nb
              f(i,m1) = f(i,m1) - f(mm,m)*a(i,mm,l1)*apv
            end do
          end do
          apv = 1._jp / b(nb,nb,l)
          mm = nb
          do i = 1,nb
            f(i,m1) = f(i,m1) - f(mm,m)*a(i,mm,l1)*apv
          end do
        end do
      end if

      m  = iu
      m0 = m - 1
      l  = iu + offset
      l0 = l - 1

      do mm = 1,nb1
        mm1 = mm + 1
        apv = 1._jp / b(mm,mm,l)
        do i = mm1,nb
          f(i,m) = f(i,m) - f(mm,m)*b(i,mm,l)*apv
        end do
      end do

      f(nb,m) = f(nb,m) / b(nb,nb,l)

      if( iu>il ) then
        do mm = nb1,1,-1
          mm1 = mm + 1
          do i = mm,1,-1
            f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
          end do
          do i = nb,1,-1
            f(i,m0) = f(i,m0) - c(i,mm1,l0)*f(mm1,m)
          end do
          f(mm,m) = f(mm,m) / b(mm,mm,l)
        end do
        do m = iu-1,il+1,-1
          m0 = m - 1
          m1 = m + 1
          l  = m + offset
          l0 = l - 1
          l1 = l + 1
          mm = nb
          do i = nb,1,-1
            f(i,m) = f(i,m) - c(i,1,l)*f(1,m1)
          end do
          f(mm,m) = f(mm,m) / b(mm,mm,l)
          do mm = nb1,1,-1
            mm1 = mm + 1
            do i = mm,1,-1
              f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
            end do
            do i = nb,1,-1
              f(i,m0) = f(i,m0) - c(i,mm1,l0)*f(mm1,m)
            end do
            f(mm,m) = f(mm,m) / b(mm,mm,l)
          end do
        end do
        m  = il
        l  = m + offset
        m1 = m + 1
        l1 = l + 1
        mm = nb
        do i = nb,1,-1
          f(i,m) = f(i,m) - c(i,1,l)*f(1,m1)
        end do
      else
        mm = nb
        l  = il + offset
        m  = 1
      end if

      f(mm,m) = f(mm,m) / b(mm,mm,l)

      do mm = nb1,1,-1
        mm1 = mm + 1
        do i = mm,1,-1
          f(i,m) = f(i,m) - b(i,mm1,l)*f(mm1,m)
        end do
        f(mm,m) = f(mm,m) / b(mm,mm,l)
      end do

  end subroutine line_solve_n

! no comment
!================================ LINE_SOLVE_1 ===============================80
!
! Performs direct line solve on block tridiagonal 1x1
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_1( dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in) :: a, b, c

    integer :: row, il, iu, il1, iqq
    integer :: ir, it, is, offset

  continue

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 1
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 1
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)
        end do
      end do backward

  end subroutine line_solve_1

! no comment
!================================ LINE_SOLVE_2 ===============================80
!
! Performs direct line solve on block tridiagonal 2x2
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_2( dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in) :: a, b, c

    integer :: row, il, iu, il1, iqq
    integer :: ir, it, is, offset

  continue

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(1,is) = f(1,is)                                                    &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 2
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(1,is) = f(1,is)                                                  &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 2
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)
        end do
      end do backward

  end subroutine line_solve_2

! no comment
!================================ LINE_SOLVE_3 ===============================80
!
! Performs direct line solve on block tridiagonal 3x3
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_3( dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in) :: a, b, c

    integer :: row, il, iu, il1, iqq
    integer :: ir, it, is, offset

  continue

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(2,is) = f(2,is)                                                    &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 3
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(2,is) = f(2,is)                                                  &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 3
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)
        end do
      end do backward

  end subroutine line_solve_3

! no comment
!================================ LINE_SOLVE_4 ===============================80
!
! Performs direct line solve on block tridiagonal 4x4
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_4( dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in) :: a, b, c

    integer :: row, il, iu, il1, iqq
    integer :: ir, it, is, offset

  continue

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                          -b(4,2,offset+is)*f(2,is)        &
                                          -b(4,3,offset+is)*f(3,is))
      f(3,is) = f(3,is)                                                    &
                        - b(3,4,offset+is)*f(4,is)
      f(2,is) = f(2,is)                                                    &
                        - b(2,4,offset+is)*f(4,is)                         &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,4,offset+is)*f(4,is)                         &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 4
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)                     &
                          - a(row,4,offset+is)*f(4,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                            -b(4,2,offset+is)*f(2,is)      &
                                            -b(4,3,offset+is)*f(3,is))
        f(3,is) = f(3,is)                                                  &
                          - b(3,4,offset+is)*f(4,is)
        f(2,is) = f(2,is)                                                  &
                          - b(2,4,offset+is)*f(4,is)                       &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,4,offset+is)*f(4,is)                       &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 4
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)                   &
                            - c(row,4,offset+is)*f(4,it)
        end do
      end do backward

  end subroutine line_solve_4

! no comment
!================================ LINE_SOLVE_5 ===============================80
!
! Performs direct line solve on block tridiagonal 5x5
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_5( dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in) :: a, b, c

    integer :: row, il, iu, il1, iqq
    integer :: ir, it, is, offset

  continue

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                          -b(4,2,offset+is)*f(2,is)        &
                                          -b(4,3,offset+is)*f(3,is))
      f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)        &
                                          -b(5,2,offset+is)*f(2,is)        &
                                          -b(5,3,offset+is)*f(3,is)        &
                                          -b(5,4,offset+is)*f(4,is))
      f(4,is) = f(4,is)                                                    &
                        - b(4,5,offset+is)*f(5,is)
      f(3,is) = f(3,is)                                                    &
                        - b(3,5,offset+is)*f(5,is)                         &
                        - b(3,4,offset+is)*f(4,is)
      f(2,is) = f(2,is)                                                    &
                        - b(2,5,offset+is)*f(5,is)                         &
                        - b(2,4,offset+is)*f(4,is)                         &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,5,offset+is)*f(5,is)                         &
                        - b(1,4,offset+is)*f(4,is)                         &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 5
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)                     &
                          - a(row,4,offset+is)*f(4,ir)                     &
                          - a(row,5,offset+is)*f(5,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                            -b(4,2,offset+is)*f(2,is)      &
                                            -b(4,3,offset+is)*f(3,is))
        f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)      &
                                            -b(5,2,offset+is)*f(2,is)      &
                                            -b(5,3,offset+is)*f(3,is)      &
                                            -b(5,4,offset+is)*f(4,is))
        f(4,is) = f(4,is)                                                  &
                          - b(4,5,offset+is)*f(5,is)
        f(3,is) = f(3,is)                                                  &
                          - b(3,5,offset+is)*f(5,is)                       &
                          - b(3,4,offset+is)*f(4,is)
        f(2,is) = f(2,is)                                                  &
                          - b(2,5,offset+is)*f(5,is)                       &
                          - b(2,4,offset+is)*f(4,is)                       &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,5,offset+is)*f(5,is)                       &
                          - b(1,4,offset+is)*f(4,is)                       &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 5
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)                   &
                            - c(row,4,offset+is)*f(4,it)                   &
                            - c(row,5,offset+is)*f(5,it)
        end do
      end do backward

  end subroutine line_solve_5

! no comment
!================================ LINE_SOLVE_6 ===============================80
!
! Performs direct line solve on block tridiagonal 6x6
! system of specified equation set
!
! The linear algebra is based on the solve from CFL3D
! with diagonal entries of b containing 1/entry.
!
!=============================================================================80
  subroutine line_solve_6( dof_in_line, f, a, b, c, lu_offset )

    integer, intent(in) :: dof_in_line, lu_offset

    real(dqp), dimension(:,:),  intent(inout) :: f
    real(jp), dimension(:,:,:), intent(in) :: a, b, c

    integer :: row, il, iu, il1, iqq
    integer :: ir, it, is, offset

  continue

      offset = lu_offset ! Offset the indices into a,b,c

      il = 1
      iu = dof_in_line

      il1 = il + 1
      is  = il

      ! f = binv*f.

      f(1,is) = b(1,1,offset+is)*(f(1,is))
      f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
      f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)        &
                                          -b(3,2,offset+is)*f(2,is))
      f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)        &
                                          -b(4,2,offset+is)*f(2,is)        &
                                          -b(4,3,offset+is)*f(3,is))
      f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)        &
                                          -b(5,2,offset+is)*f(2,is)        &
                                          -b(5,3,offset+is)*f(3,is)        &
                                          -b(5,4,offset+is)*f(4,is))
      f(6,is) = b(6,6,offset+is)*(f(6,is) -b(6,1,offset+is)*f(1,is)        &
                                          -b(6,2,offset+is)*f(2,is)        &
                                          -b(6,3,offset+is)*f(3,is)        &
                                          -b(6,4,offset+is)*f(4,is)        &
                                          -b(6,5,offset+is)*f(5,is))
      f(5,is) = f(5,is)                                                    &
                         - b(5,6,offset+is)*f(6,is)
      f(4,is) = f(4,is)                                                    &
                        - b(4,6,offset+is)*f(6,is)                         &
                        - b(4,5,offset+is)*f(5,is)
      f(3,is) = f(3,is)                                                    &
                        - b(3,6,offset+is)*f(6,is)                         &
                        - b(3,5,offset+is)*f(5,is)                         &
                        - b(3,4,offset+is)*f(4,is)
      f(2,is) = f(2,is)                                                    &
                        - b(2,6,offset+is)*f(6,is)                         &
                        - b(2,5,offset+is)*f(5,is)                         &
                        - b(2,4,offset+is)*f(4,is)                         &
                        - b(2,3,offset+is)*f(3,is)
      f(1,is) = f(1,is)                                                    &
                        - b(1,6,offset+is)*f(6,is)                         &
                        - b(1,5,offset+is)*f(5,is)                         &
                        - b(1,4,offset+is)*f(4,is)                         &
                        - b(1,3,offset+is)*f(3,is)                         &
                        - b(1,2,offset+is)*f(2,is)

      ! Forward sweep.

      forward : do is = il1, iu
        ir = is - 1
        it = is + 1

        ! First row reduction.

        do row = 1, 6
          f(row,is) = f(row,is)                                            &
                          - a(row,1,offset+is)*f(1,ir)                     &
                          - a(row,2,offset+is)*f(2,ir)                     &
                          - a(row,3,offset+is)*f(3,ir)                     &
                          - a(row,4,offset+is)*f(4,ir)                     &
                          - a(row,5,offset+is)*f(5,ir)                     &
                          - a(row,6,offset+is)*f(6,ir)
        end do

        ! f = binv*f.

        f(1,is) = b(1,1,offset+is)*(f(1,is))
        f(2,is) = b(2,2,offset+is)*(f(2,is) -b(2,1,offset+is)*f(1,is))
        f(3,is) = b(3,3,offset+is)*(f(3,is) -b(3,1,offset+is)*f(1,is)      &
                                            -b(3,2,offset+is)*f(2,is))
        f(4,is) = b(4,4,offset+is)*(f(4,is) -b(4,1,offset+is)*f(1,is)      &
                                            -b(4,2,offset+is)*f(2,is)      &
                                            -b(4,3,offset+is)*f(3,is))
        f(5,is) = b(5,5,offset+is)*(f(5,is) -b(5,1,offset+is)*f(1,is)      &
                                            -b(5,2,offset+is)*f(2,is)      &
                                            -b(5,3,offset+is)*f(3,is)      &
                                            -b(5,4,offset+is)*f(4,is))
        f(6,is) = b(6,6,offset+is)*(f(6,is) -b(6,1,offset+is)*f(1,is)      &
                                            -b(6,2,offset+is)*f(2,is)      &
                                            -b(6,3,offset+is)*f(3,is)      &
                                            -b(6,4,offset+is)*f(4,is)      &
                                            -b(6,5,offset+is)*f(5,is))
        f(5,is) = f(5,is)                                                  &
                          - b(5,6,offset+is)*f(6,is)
        f(4,is) = f(4,is)                                                  &
                          - b(4,6,offset+is)*f(6,is)                       &
                          - b(4,5,offset+is)*f(5,is)
        f(3,is) = f(3,is)                                                  &
                          - b(3,6,offset+is)*f(6,is)                       &
                          - b(3,5,offset+is)*f(5,is)                       &
                          - b(3,4,offset+is)*f(4,is)
        f(2,is) = f(2,is)                                                  &
                          - b(2,6,offset+is)*f(6,is)                       &
                          - b(2,5,offset+is)*f(5,is)                       &
                          - b(2,4,offset+is)*f(4,is)                       &
                          - b(2,3,offset+is)*f(3,is)
        f(1,is) = f(1,is)                                                  &
                          - b(1,6,offset+is)*f(6,is)                       &
                          - b(1,5,offset+is)*f(5,is)                       &
                          - b(1,4,offset+is)*f(4,is)                       &
                          - b(1,3,offset+is)*f(3,is)                       &
                          - b(1,2,offset+is)*f(2,is)

      end do forward

      ! Back substitution.

      backward : do iqq = il1, iu
        is = il + iu - iqq
        it = is + 1
        do row = 1, 6
          f(row,is) =  f(row,is)                                           &
                            - c(row,1,offset+is)*f(1,it)                   &
                            - c(row,2,offset+is)*f(2,it)                   &
                            - c(row,3,offset+is)*f(3,it)                   &
                            - c(row,4,offset+is)*f(4,it)                   &
                            - c(row,5,offset+is)*f(5,it)                   &
                            - c(row,6,offset+is)*f(6,it)
        end do
      end do backward

  end subroutine line_solve_6

end module line_nsolver

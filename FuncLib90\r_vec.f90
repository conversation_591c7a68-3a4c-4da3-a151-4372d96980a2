!=================================== R_VEC ===================================80
!
! Compute the vectors from the nodes to the interface
! Using either the existing geometry of the dual or a
! wall proximity and curvature dependent mapping approach
!
!=============================================================================80
  pure function r_vec(nnodes01, node1, node2, q_2d, mlsq, tf, cg_tol, x, y, z, &
                      cgamma, slen, slenxn, slenyn, slenzn,                    &
                      mapped, second)

    use kinddefs,        only : dp
    use lsq_types,       only : lsq_ref_type

    integer,                             intent(in) :: nnodes01
    integer,                             intent(in) :: node1, node2
    integer,                             intent(in) :: mlsq

    real(dp),                            intent(in) :: tf, cg_tol
    real(dp), dimension(nnodes01),       intent(in) :: x, y, z
    real(dp), dimension(nnodes01),       intent(in) :: slen
    real(dp), dimension(nnodes01),       intent(in) :: cgamma
    real(dp), dimension(nnodes01),       intent(in) :: slenxn, slenyn, slenzn

    logical,                             intent(in) :: q_2d, mapped
    logical,                             intent(in) :: second

    real(dp), dimension(3,2)                        :: r_vec

    type(lsq_ref_type)       :: lsq_refs

    real(dp)                 :: x1, y1, z1
    real(dp)                 :: x2, y2, z2
    real(dp)                 :: xmean, ymean, zmean
    real(dp)                 :: xleft, yleft, zleft
    real(dp)                 :: xrght, yrght, zrght
    real(dp)                 :: s1, s2, smean
    real(dp)                 :: drx, dry, drz

    real(dp), dimension(3,3) :: ti
    real(dp), dimension(4)   :: lc

    real(dp), parameter :: my_1 = 1.0_dp

  continue

! Compute the unmapped interface location of the dual interface

    x1 = x(node1)
    y1 = y(node1)
    z1 = z(node1)

    x2 = x(node2)
    y2 = y(node2)
    z2 = z(node2)

    xmean = 0.5_dp*(x1 + x2)
    ymean = 0.5_dp*(y1 + y2)
    zmean = 0.5_dp*(z1 + z2)

    if (.not.second .or. .not.mapped) then

      r_vec(1,1) = xmean - x1
      r_vec(2,1) = ymean - y1
      r_vec(3,1) = zmean - z1

      r_vec(1,2) = xmean - x2
      r_vec(2,2) = ymean - y2
      r_vec(3,2) = zmean - z2

    else

! Get the average value of slen

      s1 = slen(node1)

      s2 = slen(node2)

      smean = 0.5_dp*(s1 + s2)

! Determine the mapped system at node1

      lsq_refs = lsq_map_ref(q_2d, mlsq, cg_tol, x1, y1, z1, my_1,          &
                             cgamma(node1), s1, slenxn(node1),              &
                             slenyn(node1), slenzn(node1) )

! Determine the mapped location [xie-eta-zie] of the interface

      lc(:) = lsq_coords(lsq_refs, tf, xmean, ymean, zmean, smean)

! Invert the transform matrix

      ti = tinverse(lsq_refs%tr)

! Now dr = [tr inverse]*[xie-eta-zie]

      drx = sum(ti(1,:)*lc(1:3))
      dry = sum(ti(2,:)*lc(1:3))
      drz = sum(ti(3,:)*lc(1:3))

! Recall that dr = xc-xr
! These coords form our new interface location to extrapolate to on the left

      xleft = x(node1) + drx
      yleft = y(node1) + dry
      zleft = z(node1) + drz

! Determine the mapped system at node2

      lsq_refs = lsq_map_ref(q_2d, mlsq, cg_tol, x2, y2, z2, my_1,           &
                             cgamma(node2), s2, slenxn(node2),               &
                             slenyn(node2), slenzn(node2))

! Determine the mapped location [xie-eta-zie] of the interface

      lc(:) = lsq_coords(lsq_refs, tf, xmean, ymean, zmean, smean)

! Invert the transform matrix

      ti = tinverse(lsq_refs%tr)

! Now dr = [tr inverse]*[xie-eta-zie]

      drx = sum(ti(1,:)*lc(1:3))
      dry = sum(ti(2,:)*lc(1:3))
      drz = sum(ti(3,:)*lc(1:3))

! Recall that dr = xc-xr
! These coords form our new interface location to extrapolate to on the right

      xrght = x(node2) + drx
      yrght = y(node2) + dry
      zrght = z(node2) + drz

!     Compute the vectors from the nodes to the interface

      r_vec(1,1) = xleft - x(node1)
      r_vec(2,1) = yleft - y(node1)
      r_vec(3,1) = zleft - z(node1)

      r_vec(1,2) = xrght - x(node2)
      r_vec(2,2) = yrght - y(node2)
      r_vec(3,2) = zrght - z(node2)

    end if

end function r_vec

module source

  implicit none
  private
  public :: source_flux
  public :: source_jacobian

contains

!============================== SOURCE_FLUX ==================================80
!
! Driver routine to add source term contributions to the residual
!
!=============================================================================80

  subroutine source_flux(grid, soln)

    use grid_types,              only : grid_type
    use solution_types,          only : soln_type
    use noninertials,            only : noninrhs
    use pressure_gradient_src,   only : pressure_gradient_rhs
    use nml_noninertial_reference_frame, only : noninertial
    use rotors,                  only : rotor_source_rhs, rotor_flag
    use hiarms_act_disk,         only : hiarms_flag, hiarms_source_rhs
    use nml_boundary_conditions, only : impose_pressure_gradient,              &
                                        pressure_gradient,                     &
                                        number_of_porous_boundaries
    use nml_vortex_generator,    only : nvg
    use porous2,                 only : porous_rhs
    use vortex_generators,       only : vgs_rhs

    type(grid_type),  intent(in)    :: grid
    type(soln_type),  intent(inout) :: soln

    continue

! Source terms for noninertial refernce frame

    if (noninertial) then

      call noninrhs(soln%eqn_set,                                      &
                    grid%vol, grid%nnodes0, grid%nnodes01, soln%q_dof, &
                    soln%res, soln%n_tot,   soln%njac)

    end if

! Adds rotor source terms to the RHS of the equations

    if (rotor_flag) then
      call rotor_source_rhs(soln%eqn_set,                                      &
                            grid%nnodes0, grid%nnodes01, soln%q_dof, soln%res, &
                            soln%n_tot,   soln%njac)
    end if

! Adds HI-ARMS rotor source terms to the RHS of the equations

    if (hiarms_flag) then
      call hiarms_source_rhs(soln%eqn_set, grid%nnodes0, grid%nnodes01,        &
                             soln%q_dof, soln%res, soln%n_tot, soln%njac)
    end if

! Source terms to impoase a global pressure gradient

    if (impose_pressure_gradient) then

      call pressure_gradient_rhs(soln%eqn_set,                                 &
                    grid%vol, grid%nnodes0, grid%nnodes01, soln%q_dof,         &
                    soln%res, soln%n_tot,   soln%njac, pressure_gradient )

    end if

! Source terms to impoase a poroous bc

    if (number_of_porous_boundaries>0) then

      call porous_rhs( grid%nnodes0 )

    end if

! Source terms to simulate vortex generators

    if ( nvg > 0 ) call vgs_rhs(grid,soln)

  end subroutine source_flux


!============================== SOURCE_JACOBIAN ==============================80
!
! Driver routine to add source term contributions to the jabonians
!
!=============================================================================80

  subroutine source_jacobian( grid, soln, crow )

    use grid_types,         only : grid_type
    use solution_types,     only : soln_type, generic_gas
    use comprow_types,      only : crow_flow
    use noninertials,       only : noninlhs
    use info_depr,          only : skeleton
    use nml_noninertial_reference_frame, only : noninertial
    use rotors,             only : rotor_source_lhs, rotor_flag
    use hiarms_act_disk,    only : hiarms_flag, hiarms_source_lhs
    use nml_vortex_generator,  only : nvg
    use vortex_generators,  only : vgs_lhs
    use generic_gas_map,    only : therm_on, chem_on
    use source_gen,         only : source_eqn2
    use jacobian_mean_util, only : jacobian_need_update
    use inviscid_flux,      only : mean_decouple, dc_part

    type(grid_type),  intent(in)    :: grid
    type(crow_flow),  intent(in)    :: crow
    type(soln_type),  intent(inout) :: soln

    logical                        :: update_jacobian

    continue

! Jacobians of source terms for noninertial refernce frame

    if (noninertial) then

      call noninlhs(soln%eqn_set, grid%vol, grid%nnodes0, grid%nnodes01,       &
                    soln%a_diag,  soln%njac, crow%g2m)

    end if

! Add the Jacobians of the rotor source terms to the LHS of the equations

    if (rotor_flag) then
      call rotor_source_lhs(soln%eqn_set, grid%nnodes0, grid%nnodes01,         &
                            soln%adim,                                         &
                            1, soln%q_dof, soln%n_tot, soln%njac,              &
                            fill_a_diag=.true., fill_adjoint_res=.false.,      &
                            g2m=crow%g2m,a_diag=soln%a_diag)
    end if

! Adds the Jacobians of the HI-ARMS source terms to the LHS of the equations
    if (hiarms_flag) then
      call hiarms_source_lhs(soln%eqn_set, grid%nnodes0, grid%nnodes01,        &
                             soln%q_dof, soln%a_diag, soln%n_tot, soln%njac)
    end if

! Source terms to simulate vortex generators

    if ( nvg > 0 ) call vgs_lhs(grid,soln,crow)

! Source terms to for generic gas path

    if ( soln%eqn_set == generic_gas ) then

      if ( mean_decouple .and. .not. dc_part ) then
        if (skeleton > 15) write(*,*) "Decoupled: skipping source terms..."
        return
      end if

      if (chem_on .or. therm_on) then
        update_jacobian = jacobian_need_update(grid%igrid, soln%eqn_set, .true.)
        call source_eqn2( grid%nnodes0, grid%nnodes01, update_jacobian,        &
                          grid%x,       grid%y,        grid%z,                 &
                          soln%q_dof, soln%res, grid%vol, soln%a_diag,         &
                          soln%enthalpy_ij, grid%slen, soln%gradx, soln%grady, &
                          soln%gradz, soln%ndim, soln%njac, soln%n_grd,        &
                          crow%g2m, grid%nelem, grid%elem(:), crow%ia, crow%ja,&
                          crow%nzg2m, soln%a_off)
      end if
    end if

  end subroutine source_jacobian

end module source

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite linear_algebra

integer, parameter :: dp = selected_real_kind(P=15)

test TriDiagFull
  real(dp) :: a(3,3),d(3),e(3),q(3,3)
  a(1,1) = 1
  a(1,2) = 3
  a(1,3) = 4
  a(2,1) = a(1,2)
  a(2,2) = 2
  a(2,3) = 5
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 6
  !call display3(a)
  call tridiag(a,d,e,q)
  assert_real_equal(1,d(1))
  assert_real_equal(2+0.8*(2*0.6*5+0.8*(6-2)),d(2))
  assert_real_equal(6-0.8*(2*0.6*5+0.8*(6-2)),d(3))
  assert_real_equal(5,e(1))
  assert_real_equal(5-0.6*(2*0.6*5+0.8*(6-2)),e(2))
  assert_real_equal(0,e(3))
  assert_real_equal(1,q(1,1))
  assert_real_equal(0,q(1,2))
  assert_real_equal(0,q(1,3))
  assert_real_equal(0,q(2,1))
  assert_real_equal(0.6,q(2,2))
  assert_real_equal(0.8,q(2,3))
  assert_real_equal(0,q(3,1))
  assert_real_equal(0.8,q(3,2))
  assert_real_equal(-0.6,q(3,3))
end test

test TriDiagAlreadyTriDiag
  real(dp) :: a(3,3),d(3),e(3),q(3,3)
  a(1,1) = 1
  a(1,2) = 2
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 3
  a(2,3) = 4
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 5
  !call display3(a)
  call tridiag(a,d,e,q)
  assert_real_equal(1,d(1))
  assert_real_equal(3,d(2))
  assert_real_equal(5,d(3))
  assert_real_equal(2,e(1))
  assert_real_equal(4,e(2))
  assert_real_equal(0,e(3))
  assert_real_equal(1,q(1,1))
  assert_real_equal(0,q(1,2))
  assert_real_equal(0,q(1,3))
  assert_real_equal(0,q(2,1))
  assert_real_equal(1,q(2,2))
  assert_real_equal(0,q(2,3))
  assert_real_equal(0,q(3,1))
  assert_real_equal(0,q(3,2))
  assert_real_equal(1,q(3,3))
end test

test TriDiagAlreadyDiag
  real(dp) :: a(3,3),d(3),e(3),q(3,3)
  a(1,1) = 1
  a(1,2) = 0
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 2
  a(2,3) = 0
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 3
  call tridiag(a,d,e,q)
  assert_real_equal(1,d(1))
  assert_real_equal(2,d(2))
  assert_real_equal(3,d(3))
  assert_real_equal(0,e(1))
  assert_real_equal(0,e(2))
  assert_real_equal(0,e(3))
  assert_real_equal(1,q(1,1))
  assert_real_equal(0,q(1,2))
  assert_real_equal(0,q(1,3))
  assert_real_equal(0,q(2,1))
  assert_real_equal(1,q(2,2))
  assert_real_equal(0,q(2,3))
  assert_real_equal(0,q(3,1))
  assert_real_equal(0,q(3,2))
  assert_real_equal(1,q(3,3))
end test

test SignIntrinsic
  assert_real_equal( 1,sign( 1, 1))
  assert_real_equal( 1,sign(-1, 1))
  assert_real_equal(-1,sign( 1,-1))
  assert_real_equal(-1,sign(-1,-1))
  assert_real_equal( 1.0,sign( 1.0, 1.0))
  assert_real_equal( 1.0,sign(-1.0, 1.0))
  assert_real_equal(-1.0,sign( 1.0,-1.0))
  assert_real_equal(-1.0,sign(-1.0,-1.0))
end test

test TriDiagEig2x2plus1
  real(dp) :: a(3,3),d(3),e(3),z(3,3)
  real(dp), parameter :: invsqrt2 = 0.707106781186547
  integer :: ierr
  a(1,1) = 0.5
  a(1,2) = 0.5
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 3.5
  a(2,3) = 0
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 1
  !call display3(a)
  call tridiag(a,d,e,z)
  call poseigtridiag(d,e,z,ierr)
  assert_real_equal(3.58113883,d(1))
  assert_real_equal(1,d(2))
  assert_real_equal(0.41886117,d(3))
end test

test TriDiagEigFull1554
  real(dp) :: a(3,3),d(3),e(3),z(3,3)
  real(dp), parameter :: invsqrt2 = 0.707106781186547
  integer :: ierr
  a(1,1) = 4
  a(1,2) = 0
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 13
  a(2,3) = -7
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 7
  !call display3(a)
  call tridiag(a,d,e,z)
  call poseigtridiag(d,e,z,ierr)
  assert_real_equal(17.6157731,d(1))
  assert_real_equal(4,d(2))
  assert_real_equal(2.3842269,d(3))
  assert_real_equal(0,z(1,1))
  assert_real_equal(-0.83484109,z(2,1))
  assert_real_equal(0.550491000,z(3,1))
  assert_real_equal(-1,z(1,2))
  assert_real_equal(0,z(2,2))
  assert_real_equal(0,z(3,2))
  assert_real_equal(0,z(1,3))
  assert_real_equal(-0.550491008,z(2,3))
  assert_real_equal(-0.834841092,z(3,3))
end test

test TriDiagEig211212
  real(dp) :: a(3,3),d(3),e(3),z(3,3)
  real(dp), parameter :: invsqrt2 = 0.707106781186547
  integer :: ierr
  a(1,1) = 2
  a(1,2) = 1
  a(1,3) = 1
  a(2,1) = a(1,2)
  a(2,2) = 2
  a(2,3) = 1
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 2
  !call display3(a)
  call tridiag(a,d,e,z)
  assert_real_equal(invsqrt2,z(2,2))
  assert_real_equal(invsqrt2,z(2,3))
  assert_real_equal(invsqrt2,z(3,2))
  assert_real_equal(-invsqrt2,z(3,3))
  call poseigtridiag(d,e,z,ierr)
  assert_real_equal(4,d(1))
  assert_real_equal(1,d(2))
  assert_real_equal(1,d(3))
  assert_real_equal(0.57735027,z(1,1))
  assert_real_equal(0.57735027,z(2,1))
  assert_real_equal(0.57735027,z(3,1))
  assert_real_equal(0,z(1,2))
  assert_real_equal(invsqrt2,z(2,2))
  assert_real_equal(-invsqrt2,z(3,2))
  assert_real_equal( 0.81649658,z(1,3))
  assert_real_equal(-0.40824829,z(2,3))
  assert_real_equal(-0.40824829,z(3,3))
end test

test TriDiagEig111
  real(dp) :: a(3,3),d(3),e(3),z(3,3)
  integer :: ierr
  a(1,1) = 1
  a(1,2) = 0
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 1
  a(2,3) = 0
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 1
  call tridiag(a,d,e,z)
  call poseigtridiag(d,e,z,ierr)
  assert_real_equal(1,d(1))
  assert_real_equal(1,d(2))
  assert_real_equal(1,d(3))
end test

test TriDiagEig123
  real(dp) :: a(3,3),d(3),e(3),z(3,3)
  integer :: ierr
  a(1,1) = 2
  a(1,2) = 0
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 3
  a(2,3) = 0
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 1
  call tridiag(a,d,e,z)
  call poseigtridiag(d,e,z,ierr)
  assert_real_equal(3,d(1))
  assert_real_equal(2,d(2))
  assert_real_equal(1,d(3))
  assert_real_equal(0,z(1,1))
  assert_real_equal(-1,z(1,2))
  assert_real_equal(0,z(1,3))
  assert_real_equal(1,z(2,1))
  assert_real_equal(0,z(2,2))
  assert_real_equal(0,z(2,3))
  assert_real_equal(0,z(3,1))
  assert_real_equal(0,z(3,2))
  assert_real_equal(1,z(3,3))
end test

test TriDiagEigReconstruct211212
  real(dp), dimension(3)   :: d,e
  real(dP), dimension(3,3) :: a, eig, eigt, diag, eigd, r
  integer :: ierr
  a(1,1) = 2
  a(1,2) = 1
  a(1,3) = 1
  a(2,1) = a(1,2)
  a(2,2) = 2
  a(2,3) = 1
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 2
  call tridiag(a,d,e,eig)
  call poseigtridiag(d,e,eig,ierr)
  diag = 0
  diag(1,1) = d(1)
  diag(2,2) = d(2)
  diag(3,3) = d(3)
  eigt = transpose(eig)
  eigd = matmul(eig,diag)
  r = matmul(eigd,eigt)
  assert_real_equal(2,r(1,1))
  assert_real_equal(1,r(1,2))
  assert_real_equal(1,r(1,3))
  assert_real_equal(1,r(2,1))
  assert_real_equal(2,r(2,2))
  assert_real_equal(1,r(2,3))
  assert_real_equal(1,r(3,1))
  assert_real_equal(1,r(3,2))
  assert_real_equal(2,r(3,3))
end test

test TriDiagEigReconstruct1515
  real(dp), dimension(3)   :: d,e
  real(dP), dimension(3,3) :: a, eig, eigt, diag, eigd, r
  integer :: ierr
  a(1,1) = 13
  a(1,2) = -4
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 7
  a(2,3) = 0
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 1
  call tridiag(a,d,e,eig)
  call poseigtridiag(d,e,eig,ierr)
  assert_real_equal(15,d(1))
  assert_real_equal(5,d(2))
  assert_real_equal(1,d(3))
  diag = 0
  diag(1,1) = d(1)
  diag(2,2) = d(2)
  diag(3,3) = d(3)
  eigt = transpose(eig)
  eigd = matmul(eig,diag)
  r = matmul(eigd,eigt)
  assert_real_equal(13,r(1,1))
  assert_real_equal(-4,r(1,2))
  assert_real_equal(0,r(1,3))
  assert_real_equal(-4,r(2,1))
  assert_real_equal(7,r(2,2))
  assert_real_equal(0,r(2,3))
  assert_real_equal(0,r(3,1))
  assert_real_equal(0,r(3,2))
  assert_real_equal(1,r(3,3))
end test

test tql3Reconstruct5154tight
  real(dp), dimension(3)   :: d,e
  real(dP), dimension(3,3) :: a, eig, eigt, diag, eigd, r
  integer :: ierr
  a(1,1) = 4
  a(1,2) = 0
  a(1,3) = 0
  a(2,1) = a(1,2)
  a(2,2) = 13
  a(2,3) = -4
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 7
  call tridiag(a,d,e,eig)
  call poseigtridiag(d,e,eig,ierr)
  diag = 0
  diag(1,1) = d(1)
  diag(2,2) = d(2)
  diag(3,3) = d(3)
  eigt = transpose(eig)
  eigd = matmul(eig,diag)
  r = matmul(eigd,eigt)
 
  assert_real_equal(4,r(1,1))
  assert_real_equal(0,r(1,2))
  assert_real_equal(0,r(1,3))
  assert_real_equal(0,r(2,1))
  assert_real_equal(13,r(2,2))
  assert_real_equal(-4,r(2,3))
  assert_real_equal(0,r(3,1))
  assert_real_equal(-4,r(3,2))
  assert_real_equal(7,r(3,3))
 
  assert_real_equal(15,d(1))
  assert_real_equal(5,d(2))
  assert_real_equal(4,d(3))
 
end test

test tql3Reconstruct5154split
  real(dp), dimension(3)   :: d,e
  real(dP), dimension(3,3) :: a, eig, eigt, diag, eigd, r
  integer :: ierr
  a(1,1) = 13
  a(1,2) = 0
  a(1,3) = -4
  a(2,1) = a(1,2)
  a(2,2) = 4
  a(2,3) = 0
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 7
  call tridiag(a,d,e,eig)
  call poseigtridiag(d,e,eig,ierr)
  diag = 0
  diag(1,1) = d(1)
  diag(2,2) = d(2)
  diag(3,3) = d(3)
  eigt = transpose(eig)
  eigd =matmul(eig,diag)
  r = matmul(eigd,eigt)
  assert_real_equal(13,r(1,1))
  assert_real_equal(0,r(1,2))
  assert_real_equal(-4,r(1,3))
  assert_real_equal(0,r(2,1))
  assert_real_equal(4,r(2,2))
  assert_real_equal(0,r(2,3))
  assert_real_equal(-4,r(3,1))
  assert_real_equal(0,r(3,2))
  assert_real_equal(7,r(3,3))

  assert_real_equal(15,d(1))
  assert_real_equal(5,d(2))
  assert_real_equal(4,d(3))

end test

test TriDiagEigRandom
  real(dp) :: a(3,3),d(3),e(3),z(3,3)
  integer :: ierr
  a(1,1) = 0.22461
  a(1,2) = 0.43558
  a(1,3) = 0.12848
  a(2,1) = a(1,2)
  a(2,2) = 0.40385
  a(2,3) = 0.65227
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 0.75951
  !call display3(a)
  call tridiag(a,d,e,z)
  assert_equal_within(  0.22461,d(1),1.0e-5)
  assert_equal_within(  0.78631,d(2),1.0e-5)
  assert_equal_within(  0.37705,d(3),1.0e-5)
  call poseigtridiag(d,e,z,ierr)
  assert_equal_within(  1.37923,d(1),1.0e-5)
  assert_equal_within(  0.27957,d(2),1.0e-5)
  assert_equal_within(  0.27083,d(3),1.0e-5)
end test

test TriDiagEigMessyDiag
  real(dp) :: a(3,3),d(3),e(3),z(3,3), ans
  real(dP), dimension(3,3) :: eig, eigt, diag, eigd, r
  integer :: ierr
  a(1,1) = 5669.182266666660325_dp
  a(1,2) = 0.000000000379356_dp
  a(1,3) = 0.000000000497356_dp
  a(2,1) = a(1,2)
  a(2,2) = 5669.182266666660325_dp
  a(2,3) = 0.000000000436356_dp
  a(3,1) = a(1,3)
  a(3,2) = a(2,3)
  a(3,3) = 5669.182266666660325_dp
  call tridiag(a,d,e,z)
   ans = 5669.18227_dp
  call poseigtridiag(d,e,z,ierr)
  assert_equal_within( ans,d(1),1.0e-5)
  assert_equal_within( ans,d(2),1.0e-5)
  assert_equal_within( ans,d(3),1.0e-5)

  call tridiag(a,d,e,eig)
  call poseigtridiag(d,e,eig,ierr)
  diag = 0
  diag(1,1) = d(1)
  diag(2,2) = d(2)
  diag(3,3) = d(3)
  eigt = transpose(eig)
  eigd = matmul(eig,diag)
  r = matmul(eigd,eigt)
  r = abs(r-a)
  !call display3(r)

end test

test TriDiagEig000
  real(dp) :: a(3,3),d(3),e(3),eig(3,3)
  integer :: ierr
  a = 0.0_dp
  call tridiag(a,d,e,eig)
  call poseigtridiag(d,e,eig,ierr)

  assert_real_equal(0,d(1))
  assert_real_equal(0,d(2))
  assert_real_equal(0,d(3))

  assert_real_equal(1,eig(1,1))
  assert_real_equal(0,eig(2,1))
  assert_real_equal(0,eig(3,1))

  assert_real_equal(0,eig(1,2))
  assert_real_equal(1,eig(2,2))
  assert_real_equal(0,eig(3,2))

  assert_real_equal(0,eig(1,3))
  assert_real_equal(0,eig(2,3))
  assert_real_equal(1,eig(3,3))
end test

test LUaSquareMatrix

  real(dp) :: ar(1,2)
  real(dp) :: a2(2,2)
  real(dp) :: a3(3,3)

  ar(1,1) = 10
  ar(1,2) = 20

  call singleLU(ar)

  assert_real_equal(10, ar(1,1))
  assert_real_equal(20, ar(1,2))

  a2 = 0
  a2(1,1) = 1
  a2(2,2) = 2

  call singleLU(a2)

  assert_real_equal(1, a2(1,1))
  assert_real_equal(0, a2(2,1))
  assert_real_equal(0, a2(1,2))
  assert_real_equal(2, a2(2,2))

! LU tests from Adv Eng Math Kreyszig p981-982

  a2 = 0
  a2(1,1) = 2
  a2(1,2) = 3
  a2(2,1) = 8
  a2(2,2) = 5

  call singleLU(a2)

  assert_real_equal(2, a2(1,1))
  assert_real_equal(3, a2(1,2))
  assert_real_equal(4, a2(2,1))
  assert_real_equal(-7, a2(2,2))

  a3 = 0
  a3(1,1) = 3
  a3(1,2) = 5
  a3(1,3) = 2
  a3(2,1) = 0
  a3(2,2) = 8
  a3(2,3) = 2
  a3(3,1) = 6
  a3(3,2) = 2
  a3(3,3) = 8

  call singleLU(a3)

  assert_real_equal(3, a3(1,1))
  assert_real_equal(5, a3(1,2))
  assert_real_equal(2, a3(1,3))

  assert_real_equal(0, a3(2,1))
  assert_real_equal(8, a3(2,2))
  assert_real_equal(2, a3(2,3))

  assert_real_equal(2, a3(3,1))
  assert_real_equal(-1, a3(3,2))
  assert_real_equal(6, a3(3,3))

end test

test PivotAndLUaDiagonalMatrix

  real(dp) :: a2(2,2), p2(2,2)

  a2 = 0
  a2(1,1) = 1
  a2(2,2) = 2

  call singlePivotLU(a2,p2)

  assert_real_equal(1, a2(1,1))
  assert_real_equal(0, a2(2,1))
  assert_real_equal(0, a2(1,2))
  assert_real_equal(2, a2(2,2))

  assert_real_equal(1, p2(1,1))
  assert_real_equal(0, p2(2,1))
  assert_real_equal(0, p2(1,2))
  assert_real_equal(1, p2(2,2))

end test

test PivotAndLUaOctave2x2ExampleMatrix

  real(dp) :: a2(2,2), p2(2,2)

  a2 = 0
  a2(1,1) = 1
  a2(1,2) = 2
  a2(2,1) = 3
  a2(2,2) = 4

  call singlePivotLU(a2,p2)

  assert_real_equal(0, p2(1,1))
  assert_real_equal(1, p2(1,2))
  assert_real_equal(1, p2(2,1))
  assert_real_equal(0, p2(2,2))

  assert_real_equal(3, a2(1,1))
  assert_real_equal(4, a2(1,2))
  assert_real_equal(1.0/3.0, a2(2,1))
  assert_real_equal(2.0/3.0, a2(2,2))

end test

test PivotAndLUaNLA4x4ExampleMatrix

  real(dp) :: a4(4,4), p4(4,4)

  a4 = 0

  a4(1,1) = 2
  a4(1,2) = 1
  a4(1,3) = 1
  a4(1,4) = 0

  a4(2,1) = 4
  a4(2,2) = 3
  a4(2,3) = 3
  a4(2,4) = 1

  a4(3,1) = 8
  a4(3,2) = 7
  a4(3,3) = 9
  a4(3,4) = 5

  a4(4,1) = 6
  a4(4,2) = 7
  a4(4,3) = 9
  a4(4,4) = 8

  call singlePivotLU(a4,p4)

  assert_real_equal(0, p4(1,1))
  assert_real_equal(0, p4(1,2))
  assert_real_equal(1, p4(1,3))
  assert_real_equal(0, p4(1,4))

  assert_real_equal(0, p4(2,1))
  assert_real_equal(0, p4(2,2))
  assert_real_equal(0, p4(2,3))
  assert_real_equal(1, p4(2,4))

  assert_real_equal(0, p4(3,1))
  assert_real_equal(1, p4(3,2))
  assert_real_equal(0, p4(3,3))
  assert_real_equal(0, p4(3,4))

  assert_real_equal(1, p4(4,1))
  assert_real_equal(0, p4(4,2))
  assert_real_equal(0, p4(4,3))
  assert_real_equal(0, p4(4,4))

  assert_real_equal(8, a4(1,1))
  assert_real_equal(7, a4(1,2))
  assert_real_equal(9, a4(1,3))
  assert_real_equal(5, a4(1,4))

  assert_real_equal( 3.0/4.0, a4(2,1))
  assert_real_equal( 7.0/4.0, a4(2,2))
  assert_real_equal( 9.0/4.0, a4(2,3))
  assert_real_equal(17.0/4.0, a4(2,4))

  assert_real_equal( 1.0/2.0, a4(3,1))
  assert_real_equal(-2.0/7.0, a4(3,2))
  assert_real_equal(-6.0/7.0, a4(3,3))
  assert_real_equal(-2.0/7.0, a4(3,4))

  assert_real_equal( 1.0/4.0, a4(4,1))
  assert_real_equal(-3.0/7.0, a4(4,2))
  assert_real_equal( 1.0/3.0, a4(4,3))
  assert_real_equal( 2.0/3.0, a4(4,4))

end test

Xtest svd3x2

  integer, parameter :: m=3,n=2
  real(dp) :: A(m,n), u(m,m), d(n), v(n,n), tempwork(n)
  real(dp) :: dd(m,n), udd(m,n), aa(m,n)
  real(dp) :: val

  A(1,1) = 1;  A(1,2) = 1
  A(2,1) = 0;  A(2,2) = 1
  A(3,1) = 1;  A(3,2) = 0

  call svd(a,d,u,v,tempwork)

  assert_real_equal(sqrt(3.0), d(1))
  assert_real_equal(     1.0,  d(2))

  assert_real_equal( sqrt(6.0)/3.0, u(1,1))
  assert_real_equal( sqrt(6.0)/6.0, u(2,1))
  assert_real_equal( sqrt(6.0)/6.0, u(3,1))

  val = u(1,2)
  assert_equal_within(0, val,1.0e-8)
  assert_real_equal(-sqrt(2.0)/2.0, u(2,2))
  assert_real_equal( sqrt(2.0)/2.0, u(3,2))

  assert_real_equal( sqrt(3.0)/3.0, u(1,3))
  assert_real_equal(-sqrt(3.0)/3.0, u(2,3))
  assert_real_equal(-sqrt(3.0)/3.0, u(3,3))

  assert_real_equal(-sqrt(2.0)/2.0, v(1,1))
  assert_real_equal(-sqrt(2.0)/2.0, v(2,1))

  assert_real_equal( sqrt(2.0)/2.0, v(1,2))
  assert_real_equal(-sqrt(2.0)/2.0, v(2,2))

  dd=0
  dd(1,1) = d(1)
  dd(2,2) = d(2)
  udd = matmul(u,dd)
  aa = matmul(udd,v)

  !write(*,*) 'u', u
  !write(*,*) 'v', v
  !write(*,*) 'aa', aa

  assert_real_equal(1.0, aa(1,1))
  assert_real_equal(0.0, aa(2,1))
  assert_real_equal(1.0, aa(3,1))

  assert_real_equal(1.0, aa(1,2))
  assert_real_equal(1.0, aa(2,2))
  assert_real_equal(0.0, aa(3,2))

end test

Xtest svd2x1_10

  integer, parameter :: m=2,n=1
  real(dp) :: A(m,n), u(m,m), d(n), v(n,n), tempwork(n)
  real(dp) :: dd(m,n), udd(m,n), aa(m,n)

  A(1,1) = 1.0
  A(2,1) = 0.0

  call svd(a,d,u,v,tempwork)

  assert_real_equal( 1.0, d(1))

  assert_real_equal(-1.0, u(1,1))
  assert_real_equal( 0.0, u(2,1))
  assert_real_equal( 0.0, u(1,2))
  assert_real_equal( 0.0, u(2,2))

  assert_real_equal(-1.0, v(1,1))

  dd=0
  dd(1,1) = d(1)
  udd = matmul(u,dd)
  aa = matmul(udd,v)

  assert_real_equal(1.0, aa(1,1))
  assert_real_equal(0.0, aa(2,1))
end test

Xtest svd2x1_01

  integer, parameter :: m=2,n=1
  real(dp) :: A(m,n), u(m,m), d(n), v(n,n), tempwork(n)
  real(dp) :: dd(m,n), udd(m,n), aa(m,n)

  A(1,1) = 0.0
  A(2,1) = 1.0

  call svd(a,d,u,v,tempwork)

  assert_real_equal( 1.0, d(1))

  assert_real_equal( 0.0, u(1,1))
  assert_real_equal(-1.0, u(2,1))
  assert_real_equal( 0.0, u(1,2))
  assert_real_equal( 0.0, u(2,2))

  assert_real_equal(-1.0, v(1,1))

  dd=0
  dd(1,1) = d(1)
  udd = matmul(u,dd)
  aa = matmul(udd,v)

  assert_real_equal(0.0, aa(1,1))
  assert_real_equal(1.0, aa(2,1))
end test

Xtest svd2x2Strang

  integer, parameter :: m=2,n=2
  real(dp) :: A(m,n), u(m,m), d(n), v(n,n), tempwork(n)
  real(dp) :: dd(m,n), udd(m,n), aa(m,n)

  A(1,1) = 2.0; A(1,2) = 2.0
  A(2,2) =-1.0; A(2,2) = 1.0

  call svd(a,d,u,v,tempwork)

  assert_real_equal( 2.0*sqrt(2.0), d(1))
  assert_real_equal(     sqrt(2.0), d(2))

  assert_real_equal(-1.0/sqrt(2.0), u(1,1))
  assert_real_equal(-1.0/sqrt(2.0), u(2,1))
  assert_real_equal( 1.0/sqrt(2.0), u(1,2))
  assert_real_equal(-1.0/sqrt(2.0), u(2,2))

  assert_real_equal(-1.0, v(1,1))
  assert_real_equal( 0.0, v(2,1))
  assert_real_equal( 0.0, v(1,2))
  assert_real_equal(-1.0, v(2,2))

  dd=0
  dd(1,1) = d(1)
  dd(2,2) = d(2)
  udd = matmul(u,dd)
  aa = matmul(udd,v)

  assert_real_equal( 2.0, aa(1,1))
  assert_real_equal(-1.0, aa(2,1))
  assert_real_equal( 2.0, aa(1,2))
  assert_real_equal( 1.0, aa(2,2))

end test

test test_gaussian_elimination_2_eye

  integer, parameter :: n=2
  real(dp) :: a(n,n), b(n), x(n)
  integer :: bad_col

  a = 0.0
  a(1,1) = 1.0
  a(2,2) = 1.0
  b(1) = 2
  b(2) = 3

  x = 0
  call gaussian_elimination(a,x,b,bad_col)

  assert_equal( 0, bad_col )
  assert_real_equal( 2.0, x(1))
  assert_real_equal( 3.0, x(2))

end test

test test_gaussian_elimination_3_wiki

  integer, parameter :: n=3
  real(dp) :: a(n,n), b(n), x(n)
  integer :: bad_col

  a = 0.0
  a(1,:)   =  (/2,1,-1/)
  a(2,:)   =  (/-3,-1,2/)
  a(3,:)   =  (/-2,1,2/)

  b(:) = (/8,-11,-3/)

  x = 0
  call gaussian_elimination(a,x,b,bad_col)

  assert_equal( 0, bad_col )
  assert_real_equal( 2.0, x(1))
  assert_real_equal( 3.0, x(2))
  assert_real_equal(-1.0, x(3))

end test

test test_gaussian_elimination_3_wolf

  integer, parameter :: n=3
  real(dp) :: a(n,n), b(n), x(n)
  integer :: bad_col

  a = 0.0
  a(1,:)   =  (/9,3,4/)
  a(2,:)   =  (/4,3,4/)
  a(3,:)   =  (/1,1,1/)

  b(:) = (/7,8,3/)

  x = 0
  call gaussian_elimination(a,x,b,bad_col)

  assert_equal( 0, bad_col )
  assert_real_equal( -1.0/5.0, x(1))
  assert_real_equal(      4.0, x(2))
  assert_real_equal( -4.0/5.0, x(3))

end test

end test_suite

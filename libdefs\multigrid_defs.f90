
! Multigrid restriction/prolongation operators.

module multigrid_defs

  use interp_defs,     only : sendrecv_type
  use kinddefs,        only : dp
  use fun3d_maximums,  only : ngrid_max

  implicit none

  private

  public :: fmg_levels_request, fmg_cycles_request
  public :: fmg_prolong, fmg_prolong_order, prolong_format
  public :: ideal_coarse_grid, ideal_coarse_grid_fas, ideal_coarse_grid_cs
  public :: ideal_relaxation, ideal_relaxation_fas, ideal_relaxation_cs
  public :: viscous_method_elements
  public :: fas_depth_relaxation, fas_depth_correction
  public :: refresh_meanflow, refresh_turbulence
  public :: fl_last_relaxation
  public :: edge_averaging_tet_m
  public :: restriction_via_nsequence

  logical  :: restriction_via_nsequence = .false.
  integer :: viscous_method_elements = 0

! Multigrid parameters...global input/output parameters
! Defauls in &global nml

  integer :: fmg_levels_request ! FMG levels requested
  integer :: fmg_prolong
  integer :: prolong_format = 1
  !Note (1) is the finest level:
  integer, dimension(ngrid_max-1) :: fmg_prolong_order
  integer, dimension(ngrid_max)   :: fmg_cycles_request

  ! Ideal coarse grid flag in multigrid
  ! ..requires exact discrete solution.
  logical :: ideal_coarse_grid

  ! Ideal relaxation in multigrid
  ! ..although not strictly required, uses exact discrete solution.
  logical :: ideal_relaxation

  logical :: ideal_coarse_grid_fas
  logical :: ideal_coarse_grid_cs
  logical :: ideal_relaxation_fas
  logical :: ideal_relaxation_cs

  logical :: edge_averaging_tet_m = .false.

  public :: interp_f2c_type, restrict_p0
  public :: interp_crs_type, prolong_p1, prolong_fmg
  public :: heap_sort_type, sorted

  type interp_crs_type
    logical                         :: sr_needed
    integer,  dimension(:), pointer :: ia
    integer,  dimension(:), pointer :: ja
    integer,  dimension(:), pointer :: proc
    real(dp), dimension(:), pointer :: coeff
    real(dp), dimension(:), pointer :: volume_restricted
    type(sendrecv_type)             :: sr_f2c
    type(sendrecv_type)             :: sr_c2f
    type(sendrecv_type)             :: sr_f2c_matrix_order
    type(sendrecv_type)             :: sr_c2f_matrix_order
  end type interp_crs_type

  type interp_f2c_type
    logical                         :: sr_needed
    integer,  dimension(:), pointer :: f2c_local
    integer,  dimension(:), pointer :: proc
    real(dp), dimension(:), pointer :: volume_restricted
    type(sendrecv_type)             :: sr_f2c
    type(sendrecv_type)             :: sr_c2f
    type(sendrecv_type)             :: sr_f2c_matrix_order
    type(sendrecv_type)             :: sr_c2f_matrix_order
  end type interp_f2c_type

  type(interp_f2c_type), dimension(:), allocatable :: restrict_p0
  type(interp_crs_type), dimension(:), allocatable :: prolong_p1
  type(interp_crs_type), dimension(:), allocatable :: prolong_fmg

  type heap_sort_type
    integer,  dimension(:), pointer :: data
    integer,  dimension(:), pointer :: order
  end type heap_sort_type

  type(heap_sort_type),  dimension(:), allocatable :: sorted

  integer :: fas_depth_relaxation = 0
  integer :: fas_depth_correction = 0

  integer :: fl_last_relaxation = 0

  integer,  dimension(ngrid_max) :: refresh_meanflow = 0
  integer,  dimension(ngrid_max) :: refresh_turbulence = 0

end module multigrid_defs

!================================= LSQ_MAP_REF ===============================80
!
! Set references for least squares.
!
!=============================================================================80

  pure function lsq_map_ref(q_2d, mlsq, cg_tol, x, y, z, scalei, cgamma,       &
                            slen, slenxn, slenyn, slenzn)

    use lsq_types, only : lsq_ref_type

    integer, intent(in) :: mlsq

    real(dp), intent(in) :: cg_tol
    real(dp), intent(in) :: x, y, z
    real(dp), intent(in) :: scalei, cgamma, slen
    real(dp), intent(in) :: slenxn, slenyn, slenzn

    logical, intent(in) :: q_2d

    type(lsq_ref_type) :: lsq_map_ref

    real(dp) :: tx, ty, tz
    real(dp) :: radiusr, thetar

  continue

    lsq_map_ref%mapr = mlsq + 1
!   if ( ( lsq_map_ref%mapr == 2 ) .and.                                       &
!        ( cgamma <= cg_tol .or. slen >= 0.15 ) ) lsq_map_ref%mapr = 1
!   if ( ( lsq_map_ref%mapr == 2 ) .and.                                       &
!        ( slen >= 7.000 ) ) lsq_map_ref%mapr = 1
    if ( ( lsq_map_ref%mapr == 2 ) .and.                                       &
         ( cgamma <= cg_tol ) ) lsq_map_ref%mapr = 1
    if ( lsq_map_ref%mapr == 1 .and. .not.q_2d ) lsq_map_ref%mapr = 0

    lsq_map_ref%xr = x
    lsq_map_ref%yr = y
    lsq_map_ref%zr = z
    lsq_map_ref%slenr = slen

    lsq_map_ref%scaleir = scalei

    lsq_map_ref%slenxnr = slenxn
    lsq_map_ref%slenynr = slenyn
    lsq_map_ref%slenznr = slenzn

    lsq_map_ref%tr(:,:) = 0._dp
    lsq_map_ref%tr(1,1) = 1._dp
    lsq_map_ref%tr(2,2) = 1._dp
    lsq_map_ref%tr(3,3) = 1._dp

    if ( lsq_map_ref%mapr == 1 ) then

      tx = 1._dp ; ty = 0._dp ; tz = 0._dp

      lsq_map_ref%tr = mapping_system( tx, ty, tz )

    else if ( lsq_map_ref%mapr == 2 ) then

      !...tx, ty, tz is unit vector along distance normal direction.

      tx  = slenxn
      ty  = slenyn
      tz  = slenzn

      lsq_map_ref%tr = mapping_system( tx, ty, tz )

    else if ( lsq_map_ref%mapr == 3 ) then

      radiusr = sqrt( x**2 + z**2 )
      thetar = atan2( real(z,dp) , real(x,dp) )

      lsq_map_ref%xr = radiusr*cos(thetar)
      lsq_map_ref%zr = radiusr*sin(thetar)

    end if

  end function lsq_map_ref

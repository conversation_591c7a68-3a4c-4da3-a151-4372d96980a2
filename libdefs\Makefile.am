DIST_SUBDIRS =

include Common.am

LIBCORE_DIR=@libcore_path@

noinst_LIBRARIES = libdefs.a

libdefs_a_LIBADD =
libdefs_a_SOURCES = $(libdefs_SRCS)
libdefs_a_LINK = $(F90LINK)

EXTRA_DIST =

if BUILD_COMPLEX
DIST_SUBDIRS += Complex
endif

#Build Fortran dependencies
%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) > $@


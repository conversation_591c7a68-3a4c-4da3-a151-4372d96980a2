!================================= LSQ_SCOORDS_SY ============================80
!
! Coordinates in least square - symmetry_y
!
!=============================================================================80

  pure function lsq_scoords_sy( lsq_mrefs, tf, lc_max, dy, xc, yc, zc, slenc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type),     intent(in) :: lsq_mrefs
    real(dp), dimension(4), intent(in) :: lc_max
    real(dp),               intent(in) :: tf, dy, xc, yc, zc, slenc

    real(dp), dimension(4) :: lsq_scoords_sy, lc

    real(dp) :: xs, ys, zs, slens

  continue

    xs = xc
    ys = yc + dy
    zs = zc
    slens = slenc

    lc = lsq_coords( lsq_mrefs, tf, xs, ys, zs, slens )

    lsq_scoords_sy = lsq_scoords( lc, lc_max)

  end function lsq_scoords_sy

% This is the main LaTeX file for the FUN3D user manual.
% This file includes all the chapter files.
% see README.txt

% An 'authors' command lets us write the author list once
% instead of twice, but assumes all authors are Langley civil
% servants.
% If we ever have authors who are not Langley civil servants,
% then we have to write 'author' and 'AuthorAffiliation' separately.

\newcommand{\authors}{
  <PERSON>,
  <PERSON><PERSON><PERSON>\'<PERSON>,
  <PERSON>~<PERSON>~<PERSON>,
  <PERSON>~<PERSON>,
  <PERSON>~<PERSON><PERSON>,
  <PERSON>~<PERSON>~<PERSON>,
  <PERSON><PERSON>,
  <PERSON>~<PERSON>~<PERSON>,
  <PERSON>~<PERSON>~<PERSON>,
  <PERSON>~<PERSON>~<PERSON>,
  <PERSON>~<PERSON>,
  <PERSON>~<PERSON>~<PERSON>,
  and
  <PERSON>~<PERSON><PERSON>
}

\newcommand{\version}{M.m}% filled in by build

\documentclass[12pt]{NASA}

  \title{FUN3D Manual: \version}
  \author{\authors}
  \AuthorAffiliation{
    \authors\\
    Langley Research Center\\
    Hampton, Virginia 23681
  }
  \input{abstract}
  \NasaCenter{Langley Research Center\\
    Hampton, Virginia 23681}
  \Year{\the\year}
  \Month{\the\month}
  \Type{TM}
  \Number{tmnumb}% FIXME
  \LNumber{lnumb}% FIXME
  \WorkUnitNumber{794072.02.07.02.02}
  \SubjectCategory{61}% Computer Programming and Software, computerized simulation (general)
  \Distribution{Standard}
  \SubjectTerms{Aerodynamics; 
                Computer Programming and Software; 
                Fluid Mechanics;
                Mathematics;
                Propulsion Systems}
  \usepackage{lastpage} % get the number of pages for report documentation page
  \Pages{\pageref{LastPage}}

  \usepackage{graphicx} % images
    \graphicspath{{images/}}
    \setkeys{Gin}{width=\linewidth,totalheight=\textheight,keepaspectratio}

  \usepackage{bold-extra} % bold small caps and tt fonts
  \usepackage{amsmath}    % extended math
  \usepackage{fixltx2e}   % to provide \textsubscript under CentOS 6.4
  \usepackage{varioref}   % variable \refs
  \usepackage{subfig}     % subfloats
  \usepackage{booktabs}   % book-quality tables
  \usepackage{textcomp}   % typeset \texttrademark correctly
  \usepackage{xspace}     % put space after commands intelligently
  \usepackage{upquote}    % to get upright quotation marks in verbatim mode

  \usepackage{rotating}   % to rotate boxes without width
  \usepackage{threeparttable} % to allow table footnotes

  \usepackage[table]{xcolor} % to allow alternating table row colors
    \definecolor{lightgray}{gray}{0.95}

  \usepackage{enumitem}   % extend standard lists
    \setitemize{nolistsep,itemsep=1ex,topsep=1ex}

  \usepackage{fancyvrb}  % extended verbatim environments 
    \fvset{fontsize=\small,xleftmargin=2ex}

  \usepackage{fancyhdr}  % fancy headers/footers
    \pagestyle{fancyplain}
    \fancyhf{} % clear
    \fancyfoot[C]{\thepage}

  \usepackage[obeyspaces,spaces]{url} % verbatim-style commands with line breaks
    \def\UrlBreaks{\do\@\do\\\do\/\do\!\do\|\do\;\do\>\do\]%
                   \do\)\do\,\do\?\do\'\do+\do\=\do\#}% remove period

  \usepackage{overpic} % overlay images with picture environment

  \usepackage[hyperfootnotes=false]{hyperref} % hyperlinks (load last)
    \hypersetup{breaklinks,colorlinks,
                citecolor=darkred,linkcolor=darkblue,urlcolor=darkgreen}
                
  \newenvironment{optionlist}%
    {\begin{list}{}%
                 {\setlength{\labelwidth}{0.7em}%
                  \setlength{\labelsep}{0pt}%
                  \setlength{\itemsep}{\bigskipamount}%
                  \renewcommand{\makelabel}{\optionlistlabel}}}%
    {\end{list}}
  \newcommand{\optionlistlabel}[1]%
    {\parbox[b]{0pt}% box gymnastics due to label set in horizontal mode
      {\makebox[0pt][l]{\normalfont#1}\\[\smallskipamount]\mbox{}}}
 
   \newenvironment{enumlist}%
    {\begin{list}{}%
                 {\setlength{\labelwidth}{\linewidth}%
                  \setlength{\labelsep}{0.5em}%
                  \renewcommand{\makelabel}{\enumlistlabel}}}%
    {\end{list}}
  \newcommand{\enumlistlabel}[1]{\hfil\normalfont#1:}             

% a description-style list for typesetting namelist variables

  \newenvironment{namelist}%
    {\begin{list}{}%
                 {\setlength{\labelwidth}{0pt}%
                  \setlength{\labelsep}{0pt}%
                  \setlength{\itemsep}{\medskipamount}%
                  \renewcommand{\makelabel}{\namelistlabel}}}%
    {\end{list}}
  \newcommand{\namelistlabel}[1]%
    {\parbox[b]{0pt}% box gymnastics due to label set in horizontal mode
      {\makebox[0pt][l]{\normalfont\underline{#1}}\\[\medskipamount]\mbox{}}}

  \newcommand{\namelistsection}[2]{\newpage\subsubsection{#1}\label{#2}}

  \newcommand{\tabularfont}{\footnotesize\sffamily}

% shorthand commands for consistent treatment and line breaks

  \DeclareUrlCommand{\path}{\urlstyle{tt}}
  \DeclareUrlCommand{\file}{\urlstyle{tt}}
  \DeclareUrlCommand{\code}{\urlstyle{tt}}
  \DeclareUrlCommand{\cmd}{\urlstyle{tt}}
  \DeclareUrlCommand{\var}{\urlstyle{tt}}

  \newcommand{\FieldView}{FieldView\texttrademark\xspace}
  \newcommand{\Tecplot  }{Tecplot\texttrademark\xspace}
  \newcommand{\Sculptor }{Sculptor\texttrademark\xspace}
  \newcommand{\DOT      }{DOT/BIGDOT\texttrademark\xspace}
  \newcommand{\NPSOL    }{NPSOL\texttrademark\xspace}
  \newcommand{\SNOPT    }{SNOPT\texttrademark\xspace}
  \newcommand{\Intel    }{Intel\textsuperscript{\textregistered}\xspace}
  \newcommand{\Cray     }{Cray\textsuperscript{\textregistered}\xspace}
  \newcommand{\PGI      }{Portland Group\textsuperscript{\textregistered}\xspace}
  \newcommand{\NAG      }{NAG\textsuperscript{\textregistered}\xspace}
  \newcommand{\Lahey    }{Lahey/Fujitsu\textsuperscript{\textregistered}\xspace}
  \newcommand{\Absoft   }{Absoft\textsuperscript{\textregistered}\xspace}
  \newcommand{\IBM      }{IBM\textsuperscript{\textregistered}\xspace}
  \newcommand{\Mac      }{Mac\textsuperscript{\textregistered}\xspace}
  \newcommand{\MacOSX   }{Mac OS X\texttrademark\xspace}
  \newcommand{\Windows  }{Microsoft Windows\texttrademark\xspace}

  \newcommand{\FunThreeD}{\textsc{Fun3D}\xspace}
  \newcommand{\FunTwoD}{\textsc{Fun2D}\xspace}
  \newcommand{\nodet}{\textsc{nodet}\xspace}
  \newcommand{\dual}{\textsc{dual}\xspace}
  \newcommand{\adapt}{\textsc{adapt}\xspace}
  \newcommand{\optDriver}{\textsc{opt_driver}\xspace}
  \newcommand{\refine}{\textsc{refine}\xspace}
  \newcommand{\knife }{\textsc{knife}\xspace}
  \newcommand{\VGRID}{\textsc{VGRID}\xspace}
  \newcommand{\sboom}{\textsc{sBOOM}\xspace}
  \newcommand{\funsupport}{\texttt{<EMAIL>}\xspace}

\newcommand{\Fig}[1]{Figure \ref{#1}}
\newcommand{\fig}[1]{Fig.~\ref{#1}}
\newcommand{\Tab}[1]{Table~\ref{#1}}
\newcommand{\tab}[1]{Table~\ref{#1}}
\newcommand{\Sectionref}[1]{Section~\ref{#1}}
\newcommand{\sectionref}[1]{section~\ref{#1}}
\newcommand{\nameandsection}[1]{\nameref{#1} (\ref{#1})}

  \setcounter{tocdepth}{3}

% To typeset only specified sections; *.aux files will be used if available.
% \includeonly{introduction}

\begin{document}

\maketitle

\cleardoublepage

\tableofcontents

\cleardoublepage

\phantomsection% for hyperref

\addcontentsline{toc}{section}{About this Document}
\include{about}

\addcontentsline{toc}{section}{Acknowledgments}
\include{acknowledgments}

\addcontentsline{toc}{section}{Quick Start}
\include{quick_start}

\include{introduction}
\include{conventions} % coord and non-dim
\include{boundary_conditions}
\include{grids}
\include{flow_solver}
\include{adjoint_solver}
\include{grid_motion}
\include{grid_adaptation}
\include{design}

\cleardoublepage

\phantomsection% for hyperref
\addcontentsline{toc}{section}{References}
\bibliography{manual}
\bibliographystyle{nasa}

\appendix

%\include{hypersonics}
\include{installation}
\include{fun3d_input_files}
\include{troubleshooting}

\end{document}

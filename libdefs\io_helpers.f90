module io_helpers

  use kinddefs,  only : dp

  implicit none

  private

  public :: set_default_boundaries, set_kappa_umuscl

contains

!=========================== SET_DEFAULT_BOUNDARIES ==========================80
!
!  Sets a default list of boundaries that are used for special purposes -
!  animation output, aero loads extraction, massoud file creation, etc.
!  The default list is set as all solid surfaces in 3D and one y=const. plane
!  in 2D. The optional ignore_twod_option argument sets the list as as all
!  solid surfaces even for 2D. In general, after this routine is called, the
!  code should call a second routine that allows the user to override this
!  default list if they desire.
!
!=============================================================================80

  subroutine set_default_boundaries(nbound, output_bndry, bc, nnodes01, y,     &
                                    ignore_twod_option)

    use info_depr,       only : twod
    use bc_types,        only : bcgrid_type
    use bc_names,        only : bc_used_for_force_calculation, bc_name_index  &
                              , bc_is_flow_through
    use lmpi,            only : lmpi_bcast, lmpi_reduce
    use twod_util,       only : yplane_2d, y_coplanar_tol, q_2d

    integer,                                intent(in)    :: nbound
    logical,           dimension(nbound),   intent(out)   :: output_bndry
    type(bcgrid_type), dimension(nbound),   intent(inout) :: bc
    integer,                                intent(in)    :: nnodes01
    real(dp),          dimension(nnodes01), intent(in)    :: y
    logical,           optional,            intent(in)    :: ignore_twod_option

    integer  :: ib, node, outputbnd, outputbnd_sum

    logical  :: ignore_twod

    real(dp) :: delta_y

    character(len=80) :: bc_name

  continue

    if ( present(ignore_twod_option) ) then
      ignore_twod = ignore_twod_option
    else
      ignore_twod = .false.
    end if

!   start with no boundaries

    output_bndry(:) = .false.

    if ((twod .or. q_2d) .and. (.not. ignore_twod)) then

!     output primary symmetry plane(s) for 2D cases

      bound_loop : do ib=1,nbound
        outputbnd = 0
        call bc_name_index( bc(ib)%ibc, bc_name, .true. )
        if (trim(bc_name) /= 'symmetry_y') cycle bound_loop
        do node=1,bc(ib)%nbnode
          outputbnd = 0
          delta_y = y(bc(ib)%ibnode(node)) - yplane_2d
          if ( abs(real(delta_y)) > y_coplanar_tol ) exit
          outputbnd = 1
        end do
        call lmpi_reduce(outputbnd, outputbnd_sum)
        call lmpi_bcast(outputbnd_sum)
        if (outputbnd_sum /= 0) output_bndry(ib) = .true.
      end do bound_loop

    else

!     output all solid boundaries in 3D

      do ib=1,nbound
        if (.not.(bc_used_for_force_calculation(bc(ib)%ibc))) cycle
        if (     (bc_is_flow_through(bc(ib)%ibc))) cycle
        output_bndry(ib) = .true.
      end do

    end if

  end subroutine set_default_boundaries

!============================== SET_KAPPA_UMUSCL =============================80
!
! Set kappa_umuscl depending on grid topology.
! Overwrite this if kappa_umuscl has been specified by the user.
!
!=============================================================================80

  subroutine set_kappa_umuscl( elem )

    use info_depr,         only : twod, cc_primal, kappa_umuscl
    use lmpi,              only : lmpi_master
    use element_types,     only : elem_type
    use element_defs,      only : type_tet
    use complex_functions, only : o

    type(elem_type), dimension(:), intent(in) :: elem

    integer :: i

    continue

    kappa_umuscl_not_set: if (kappa_umuscl < 0.0_dp) then
      kappa_umuscl = 0.0_dp
      threed_node_centered: if (.not.twod.and..not.cc_primal) then
        detect_non_tet_elements: do i = 1, size(elem)
          if ( elem(i)%type_cell /= type_tet ) then
            kappa_umuscl = 0.5_dp
            exit detect_non_tet_elements
          end if
        end do detect_non_tet_elements
      end if threed_node_centered
      if (lmpi_master) then
        write(*,1) 'NOTE: kappa_umuscl set by grid:', o(kappa_umuscl)
      end if
    else
      if (lmpi_master) then
        write(*,1) 'NOTE: kappa_umuscl set by user:', o(kappa_umuscl)
      end if
    end if kappa_umuscl_not_set
 1 format(2x,a,1x,f0.2)

  end subroutine set_kappa_umuscl

end module io_helpers

module sfe_interface

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use kinddefs,                only : dp
#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)
  use nml_governing_equations, only : discretization
#endif

  implicit none

  private

  public :: sfe_init, sfe_timestep, sfe_soln_dump, all_sfe
  public :: metis_data_sfe


  integer, dimension(:), allocatable :: metis_data_sfe

  real(dp), dimension(:,:),   pointer     :: dummy_turb

  logical :: all_sfe  = .false.  ! SFE is solving entire problem

contains

!=============================== SFE_INIT ====================================80
!
!  Load SFE with its data
!
!=============================================================================80
  subroutine sfe_init(filename,grid,soln,crow)

#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)
    use fluid,                   only : gamma, prandtl
    use info_depr,               only : xmach, alpha, ivisc, tref, re
    use lmpi,                    only : lmpi_max, lmpi_bcast, lmpi_master,     &
                                        lmpi_die
    use allocations,             only : my_alloc_ptr
    use ivals,                   only : rho0, u0, v0, w0, p0
    use kinddefs,                only : dp
    use turb_parameters,         only : turbulence_exterior
    use nml_global,              only : irest, moving_grid
    use nml_periodicity,         only : periodic_dir, periodic_tol
    use refgeom,                 only : sref
    use nml_governing_equations, only : max_component_grids
#else
    use lmpi,            only : lmpi_master, lmpi_die
#endif

    use grid_types,      only : grid_type
    use solution_types,  only : soln_type
    use comprow_types,   only : crow_flow

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln
    type(crow_flow), intent(in) :: crow

    character(len=80), intent(in) :: filename

#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)

    real(dp), dimension(5) :: freestream

    logical :: viscous_flag, found_sfe, found_finvol
    logical :: turbulent_flag, read_restart

#endif

  continue

#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)

! some basic sanity checks and problem setup, then initialize SFE

    viscous_flag   = ( ivisc > 0 )
    turbulent_flag = ( ivisc > 2 )
    read_restart   = ( irest == 1 )

    all_sfe = .true.

    freestream(1) = rho0
    freestream(2) = u0
    freestream(3) = v0
    freestream(4) = w0
    freestream(5) = p0


    discretization(1) = 'sfe'

!     call sfe_initiate(filename, grid%nedge, grid%nelem, grid%elem,           &
!                       grid%nnodes0, grid%nnodes01, grid%nbound, grid%bc,     &
!                       grid%x, grid%y, grid%z, gamma, prandtl, xmach, alpha,  &
!                       viscous_flag, max_component_grids, discretization,     &
!                       tref, re, freestream,                                  &
!                       grid%l2g, grid%project, turbulent_flag, soln%n_turb,   &
!                       turbulence_exterior(1:soln%n_turb), read_restart,      &
!                       moving_grid, soln%n_tot, soln%neq01, soln%q_dof,       &
!                       periodic_dir, periodic_tol, sref,grid%nnodesg,         &
!                       size(metis_data_sfe), metis_data_sfe, all_sfe,         &
!                       crow%ia, crow%ja, crow%nnz0)

      call sfe_initiate(filename, grid%nnodes0, grid%nnodes01, grid%nelem,     &
                        grid%elem, grid%nbound, grid%bc,                       &
                        grid%x, grid%y, grid%z, grid%vol,                      &
                        gamma, prandtl, xmach, alpha,                          &
                        re, viscous_flag, tref, freestream,                    &
                        grid%l2g, turbulent_flag, read_restart,                &
                        soln%n_tot, soln%neq01, soln%q_dof, sref,              &
                        crow%ia, crow%ja, crow%nnz0)

#else

    if ( lmpi_master ) then
      write(*,*) 'This FUN3D executable not linked to SFE library.'
    endif
    call lmpi_die
    stop
    if ( trim(filename) == '' ) then
    endif
    if ( grid%nnodes0 == 0 ) then
    endif
    if ( soln%eqn_set == 0 ) then
    endif
    if ( crow%nnz0 == 0)then
    end if

#endif

  end subroutine sfe_init


!=============================== SFE_TIMESTEP ================================80
!
!  Execute one timestep of SFE scheme
!
!=============================================================================80
  subroutine sfe_timestep(soln,grid,iterations)

#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)
    use info_depr,               only : simulation_time
    use nml_nonlinear_solves,    only : dt
    use kinddefs,                only : dp
    use lmpi,                    only : lmpi_die
    use nml_governing_equations, only : max_component_grids
#else
    use lmpi,                    only : lmpi_master, lmpi_die
#endif
    use solution_types,          only : soln_type
    use grid_types,              only : grid_type
    use allocations,             only : my_alloc_ptr

    integer, intent(in) :: iterations

    type(grid_type), intent(in) :: grid

    type(soln_type), intent(inout) :: soln

    logical, save :: init = .true.

  continue

! Initialization

    if ( init ) then

      if ( soln%n_turb == 0 ) then
        call my_alloc_ptr(dummy_turb, 1, grid%nnodes01)
        dummy_turb(:,:) = 0.0_dp
      endif

      init = .false.

    endif

#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)
! SFE time starts at zero

!       call sfe_advance_timestep(simulation_time-dt, dt, soln%n_tot,         &
!                                 soln%neq01, soln%q_dof, soln%qatn1,         &
!                                 max_component_grids, discretization,        &
!                                 soln%n_turb,soln%turb,                      &
!                                 iterations,grid%nelem,grid%elem)

! Pass solution into SFE so I can copy solution
! back into FUN3D for post-processing
!
        call sfe_advance_timestep(soln%n_tot, soln%neq01, &
                                  soln%q_dof,soln%rmshist,iterations)
#else
    if ( lmpi_master ) then
      write(*,*) 'Executable has not been compiled against the SFE library.'
    endif
    call lmpi_die
    stop
    if ( soln%dof0 == 0 ) then
    endif
    if ( iterations == 0 ) then
    endif

#endif

  end subroutine sfe_timestep


!================================ SFE_SOLUTION_DUMP ==========================80
!
! Tell SFE to dump its solution
!
!=============================================================================80
  subroutine sfe_soln_dump(iterations)

#if !defined(HAVE_SFE) || !defined(HAVE_SPARSKIT)
    use lmpi, only : lmpi_master, lmpi_die
#endif

    integer, intent(in) :: iterations

  continue

#if defined(HAVE_SFE) && defined(HAVE_SPARSKIT)
!   call sfe_write_soln(iterations)
#else
    if ( lmpi_master ) then
      write(*,*) 'Executable has not been compiled against the SFE library.'
    endif
    if ( .false. ) write(*,*) iterations
    call lmpi_die
    stop
#endif

  end subroutine sfe_soln_dump


end module sfe_interface

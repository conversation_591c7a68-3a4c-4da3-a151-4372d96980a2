!============================= SOLVE_BOX =====================================80
!
!  Define and scale a box within the grid for computation.
!
!=============================================================================80
module solve_box

  use info_depr,         only : skeleton, twod, tightly_couple
  use twod_util,         only : q_2d
  use kinddefs,          only : dp, odp
  use grid_types,        only : grid_type
  use solution_types,    only : soln_type
  use comprow_types,     only : crow_flow
  use lmpi,              only : lmpi_nproc, lmpi_reduce, lmpi_max, lmpi_master,&
                                lmpi_conditional_stop, lmpi_bcast,             &
                                lmpi_max_and_maxid, lmpi_min, lmpi_id
  use flux_util,         only : mark_nearboundaries_cc,                        &
                                mark_boundaries, mark_nearboundaries
  use bcc_util,          only : mark_boundaries_bcc,                           &
                                mark_select_boundaries_bcc
  use lmpi_app,          only : lmpi_xfer
  use allocations,       only : my_alloc_ptr
  use fun3d_maximums,    only : ngrid_max
  use exact,             only : exact_q, check_value_faults
  use debug_defs,        only : debug_q_proc, debug_q_loc, debug_q_global
  use nml_global,        only : document_namelist

  implicit none

  private

  public :: read_nml_box, echo_nml_box
  public :: set_box_q_dof
  public :: set_solve_box, set_box_solve_levels
  public :: set_solve_box_meanflow
  public :: set_solve_box_turb
  public :: set_implicit_box_meanflow
  public :: set_implicit_box_turb
  public :: grid_box_scaling
  public :: box_solve_grid_level, box_solve_fas_finest
  public :: box_x_min, box_x_max
  public :: box_y_min, box_y_max
  public :: box_z_min, box_z_max
  public :: box_s_min, box_s_max
  public :: n_box
  public :: reference_q_read, reference_q_write
  public :: reference_meanflow, reference_turbulence

  integer :: n_box = 1
  logical                       :: box_solve             = .false.
  logical                       :: box_solve_fas_finest  = .false.
  logical                       :: box_solve_fas_coarser = .false.
  logical, dimension(ngrid_max) :: box_solve_grid_level  = .false.

  logical :: box_cylindrical = .false.
  logical :: box_spherical = .false.
  logical :: scale_about_nearest_grid_point  = .false.
  logical :: skip_boundary_q                 = .false.
  logical :: skip_interior_q                 = .false.

  logical :: skip_boundaries_selectively = .false.
  logical, dimension(20) :: select_boundaries = .false.
  logical :: infinite_box                    = .false.

  integer :: geometry_scale_power = 0
  integer :: boundary_rind_expansions = 0

  logical :: box_seed            = .false.
  integer :: box_seed_expansions = 0

  real(dp) :: geometry_scale = 1.0_dp
  real(dp) :: x_scale_center = 999.0_dp,    &
              y_scale_center = 999.0_dp,    &
              z_scale_center = 999.0_dp

  real(dp), dimension(5) :: box_x_min = -99999.0_dp, box_x_max = 99999.0_dp
  real(dp), dimension(5) :: box_y_min = -99999.0_dp, box_y_max = 99999.0_dp
  real(dp), dimension(5) :: box_z_min = -99999.0_dp, box_z_max = 99999.0_dp
  real(dp), dimension(5) :: box_s_min = -99999.0_dp, box_s_max = 99999.0_dp

  integer :: actions

  logical :: discrete_q_outside_box = .false.
  logical :: reference_q_read = .false., reference_q_write = .false.
  logical :: reference_meanflow   = .false. !=T =>set dQ=0
  logical :: reference_turbulence = .false. !=T =>set dturb = 0

  logical :: show = .false., echo = .false.

  namelist / box /                                                             &
                                                                   show, echo, &
      box_solve, box_solve_fas_finest, box_solve_fas_coarser,                  &
      geometry_scale_power,                                                    &
      box_spherical, box_cylindrical,                                          &
      scale_about_nearest_grid_point,                                          &
      x_scale_center, y_scale_center, z_scale_center, n_box,                   &
      box_x_min, box_x_max, box_y_min, box_y_max,                              &
      box_z_min, box_z_max, box_s_min, box_s_max,                              &
      skip_boundary_q, boundary_rind_expansions,                               &
      box_seed, box_seed_expansions,                                           &
      skip_boundaries_selectively, select_boundaries,                          &
      skip_interior_q, infinite_box, discrete_q_outside_box,                   &
      reference_q_write, reference_q_read,                                     &
      reference_meanflow, reference_turbulence

contains

!================================= GRID_BOX_SCALING ==========================80
!
! Scale x,y,z coordinates.
!
!=============================================================================80

subroutine grid_box_scaling( grid )

  use grid_metrics,        only : compute_dual_metrics
  use array_check,         only : csize
  use grid_types,          only : grid_type
  use nml_global,          only : moving_grid

  type(grid_type), intent(inout) :: grid

  integer :: i, b

  logical, save :: first_pass = .true.

  continue

  if ( first_pass ) then

    first_pass = .false.

    !...set q_2d here to be on safe side.
    if ( twod )  q_2d = .true.

    if ( box_solve ) call find_enclosing_box(grid%nnodes0, grid%nnodes01, &
                                             grid%x, grid%y, grid%z )
  endif

! Set up scaling of geometry

  if(geometry_scale_power <= 0) return

  if(lmpi_master) write(*,*) ' SCALE...geometry_scale_power=',&
                                  real(geometry_scale_power,dp)
! Set geometry scale factor

  geometry_scale = 1._dp/real( 2**(geometry_scale_power-1) , dp )
  if(lmpi_master) write(*,*) ' SCALE...geometry_scale=',&
                                  real(geometry_scale,dp)

  call set_xyz_scale_center( grid%nnodes0, grid%nnodes01,                  &
                               grid%x, grid%y, grid%z )
  grid%x = (grid%x - x_scale_center )*geometry_scale + x_scale_center
  if(.not.q_2d)                                                     &
  grid%y = (grid%y - y_scale_center )*geometry_scale + y_scale_center
  grid%z = (grid%z - z_scale_center )*geometry_scale + z_scale_center

  if(lmpi_master) write(*,*) ' SCALE...grid%x/y/z scaled.'

  if ( box_spherical .or. box_cylindrical ) then

    write(*,*) ' Scaling must use cartesian box....stopping'
    call lmpi_conditional_stop(1,'non-cartesian:grid_box_scaling')

  elseif ( grid%cc ) then

    write(*,*) ' Not programmed for cc....stopping'
    call lmpi_conditional_stop(1,'cc:grid_box_scaling')

  else

    if(lmpi_master) write(*,*) ' SCALE...Recomputing metric terms.'

    !...check leading dimension of c2n/c2e
    do i = 1,grid%nelem
      call csize(grid%elem(i)%ncell, size(grid%elem(i)%c2n,2), &
                                        'c2n..grid_box_scaling')
      call csize(grid%elem(i)%ncell, size(grid%elem(i)%c2e,2), &
                                        'c2e..grid_box_scaling')
    end do

    call compute_dual_metrics(grid, moving_grid)

!   Scale distance function (possibly)

    if(lmpi_master) write(*,*) ' SCALE...distance function.'
    grid%slen = grid%slen*geometry_scale
  endif

! Rescale box if needed

  if ( box_solve ) then
    if(lmpi_master) write(*,*) ' BOX/SCALE:...rescaling.'
    box_x_min = (box_x_min - x_scale_center )*geometry_scale+x_scale_center
    box_x_max = (box_x_max - x_scale_center )*geometry_scale+x_scale_center
    if(.not.q_2d)                                                         &
    box_y_min = (box_y_min - y_scale_center )*geometry_scale+y_scale_center
    if(.not.q_2d)                                                         &
    box_y_max = (box_y_max - y_scale_center )*geometry_scale+y_scale_center
    box_z_min = (box_z_min - z_scale_center )*geometry_scale+z_scale_center
    box_z_max = (box_z_max - z_scale_center )*geometry_scale+z_scale_center
    if ( lmpi_master ) then
      do b=1,n_box
        write(*,*) ' BOX/SCALE:...box=',b,' of n_box=',n_box
        write(*,*) ' BOX/SCALE:...box_x_min=',real(box_x_min(b),dp)
        write(*,*) ' BOX/SCALE:...box_x_max=',real(box_x_max(b),dp)
        write(*,*) ' BOX/SCALE:...box_y_min=',real(box_y_min(b),dp)
        write(*,*) ' BOX/SCALE:...box_y_max=',real(box_y_max(b),dp)
        write(*,*) ' BOX/SCALE:...box_z_min=',real(box_z_min(b),dp)
        write(*,*) ' BOX/SCALE:...box_z_max=',real(box_z_max(b),dp)
      enddo
    endif
  endif

  end subroutine grid_box_scaling

!=============================== READ_NML_BOX ================================80
!
! Read namelist for scaling box.
!
!=============================================================================80

  subroutine read_nml_box(nml_path)

    use info_depr,         only : print_conditional, cc_primal
    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use namelist_util,     only : nml_error

    character(len=*), intent(in) :: nml_path

    integer :: sunit, iostat1, iostat2

    integer :: ierr, b

    continue

    iostat1 = 0
    iostat2 = 0
    master_read_nml : if ( lmpi_master ) then

      sunit = available_unit()
      call se_open(sunit, file=nml_path, status='old', iostat=iostat1)
      if (iostat1 == 0) then
        show = print_conditional
        read(sunit,iostat=iostat2,nml=box)
        if ( show ) write(*,nml=box)
      else
        write(*,*) 'unable to open ', trim(nml_path)
      endif
      close(sunit)

    endif master_read_nml

    call nml_error( iostat1, iostat2, nml_path, 'box' )

    if ( lmpi_master ) then

      if ( box_solve_fas_finest .or. box_solve_fas_coarser ) then
        if ( print_conditional .and. .not.box_solve ) then
          write(6,*) ' BOX:...overriding value : box_solve = .true.'
        endif
        box_solve = .true.
      elseif( box_solve ) then
        box_solve_fas_finest  = box_solve
        box_solve_fas_coarser = box_solve
        if ( print_conditional ) then
          write(6,*) ' BOX:...overriding values:'
          write(6,*) ' BOX:...box_solve_fas_finest=', box_solve_fas_finest
          write(6,*) ' BOX:...box_solve_fas_coarser=',box_solve_fas_coarser
        endif
      endif

      !...override some values if not solving using box.
      if ( .not.box_solve ) then
        box_solve_grid_level(:)     = .false.
        skip_boundary_q             = .false.
        skip_boundaries_selectively = .false.
        skip_interior_q             = .false.
        boundary_rind_expansions    = 0
        if ( print_conditional ) then
          write(6,*) ' BOX:...box_solve=',box_solve
          write(6,*) ' BOX:...overriding values:'
          write(6,*) ' BOX:...............box_solve_grid_level(:)=',&
                                          box_solve_grid_level(:)
          write(6,*) ' BOX:...............skip_boundary_q=',&
                                          skip_boundary_q
          write(6,*) ' BOX:...skip_boundaries_selectively=',&
                              skip_boundaries_selectively
          write(6,*) ' BOX:...............skip_interior_q=',&
                                          skip_interior_q
          write(6,*) ' BOX:......boundary_rind_expansions=',&
                                 boundary_rind_expansions
        endif
      else
        box_solve_grid_level(:) = box_solve_fas_coarser
        box_solve_grid_level(1) = box_solve_fas_finest
      endif

    endif

    call lmpi_bcast(box_solve)
    call lmpi_bcast(box_solve_fas_finest)
    call lmpi_bcast(box_solve_fas_coarser)
    call lmpi_bcast(box_solve_grid_level)
    call lmpi_bcast(geometry_scale_power)
    call lmpi_bcast(scale_about_nearest_grid_point)

    call lmpi_bcast(x_scale_center)
    call lmpi_bcast(y_scale_center)
    call lmpi_bcast(z_scale_center)

    call lmpi_bcast(n_box)

    call lmpi_bcast(box_x_min)
    call lmpi_bcast(box_x_max)

    call lmpi_bcast(box_y_min)
    call lmpi_bcast(box_y_max)

    call lmpi_bcast(box_z_min)
    call lmpi_bcast(box_z_max)

    call lmpi_bcast(box_s_min)
    call lmpi_bcast(box_s_max)

    call lmpi_bcast(box_spherical)
    call lmpi_bcast(box_cylindrical)

    call lmpi_bcast(skip_boundary_q)
    call lmpi_bcast(skip_boundaries_selectively)
    call lmpi_bcast(select_boundaries)
    call lmpi_bcast(skip_interior_q)
    call lmpi_bcast(boundary_rind_expansions)
    call lmpi_bcast(box_seed)
    call lmpi_bcast(box_seed_expansions)
    call lmpi_bcast(infinite_box)

    call lmpi_bcast(discrete_q_outside_box)
    call lmpi_bcast(reference_q_read)
    call lmpi_bcast(reference_q_write)

    call lmpi_bcast(reference_meanflow)
    call lmpi_bcast(reference_turbulence)

    ierr = 0
    if ( .not.infinite_box ) then
      do b=1,n_box
      if((box_x_min(b) > box_x_max(b)) .or. &
         (box_y_min(b) > box_y_max(b)) .or. &
         (box_z_min(b) > box_z_max(b))) then
        ierr = 1
        if(lmpi_master) then
          write(6,*) ' BOX:...box=',b,' of n_box=',n_box
          write(6,*) ' BOX:...min exceeds max on box specification..stopping.'
          write(6,*) ' BOX:...box_x_min/max=',real(box_x_min(b),dp),&
                                              real(box_x_max(b),dp)
          write(6,*) ' BOX:...box_y_min/max=',real(box_y_min(b),dp),&
                                              real(box_y_max(b),dp)
          write(6,*) ' BOX:...box_z_min/max=',real(box_z_min(b),dp),&
                                              real(box_z_max(b),dp)
        endif
      endif
      enddo
    endif
    call lmpi_conditional_stop(ierr,'1:read_nml_box')

    if ( skip_interior_q .and. skip_boundary_q ) then
      if(lmpi_master) then
        write(6,*) ' BOX:...skip_interior_q and skip_boundary_q &
                   &cannot both be true...stopping.'
      endif
      call lmpi_conditional_stop(1,'2:read_nml_box')
    elseif ( skip_interior_q .and. skip_boundaries_selectively ) then
      if(lmpi_master) then
        write(6,*) ' BOX:...skip_interior_q and skip_boundaries_selectively &
                   &cannot both be true...stopping.'
      endif
      call lmpi_conditional_stop(1,'3:read_nml_box')
    elseif ( skip_boundary_q .and. skip_boundaries_selectively ) then
      if(lmpi_master) then
        write(6,*) ' BOX:...skip_boundary_q and skip_boundaries_selectively &
                   &cannot both be true...stopping.'
      endif
      call lmpi_conditional_stop(1,'4:read_nml_box')
    endif

    if ( skip_boundaries_selectively .and. .not. cc_primal ) then
      if ( lmpi_master ) then
        write(6,*)
        write(6,*) ' BOX:...node-centered : skip_boundaries_selectively',&
                   ' not programmed.'
        write(6,*) ' BOX:...stopping.'
      endif
      call lmpi_conditional_stop(1,'5:read_nml_box')
    endif

  end subroutine read_nml_box

!=========================== SET_XYZ_SCALE_CENTER ============================80
!
! This routine computes the x/y/z of node closest to average value.
!
!=============================================================================80

  subroutine set_xyz_scale_center(nnodes0, nnodes01, x, y, z )

    integer,                          intent(in) :: nnodes0, nnodes01
    real(dp), dimension(nnodes01),    intent(in) :: x,y,z

    integer :: n, proc_with_max
    integer :: node_sum, node_sum_global

    real(dp)    :: r_min, r
    real(dp)    :: x_sum, y_sum, z_sum
    real(dp)    :: x_sum_global, y_sum_global, z_sum_global
    real(dp)    :: x_avg, y_avg, z_avg

  continue

    x_sum    = 0._dp
    y_sum    = 0._dp
    z_sum    = 0._dp
    node_sum = 0

!   Loop over the nodes and compute an approximate average
!   if input value different from default

    if(abs(x_scale_center - 999.0_dp) < 1.0e-09_dp .and.                       &
       abs(y_scale_center - 999.0_dp) < 1.0e-09_dp .and.                       &
       abs(z_scale_center - 999.0_dp) < 1.0e-09_dp) then

      node_loop : do n = 1, nnodes0

        if(q_2d .and. abs(y(n)) > 1._dp) cycle node_loop

        x_sum   = x_sum + x(n)
        y_sum   = y_sum + y(n)
        z_sum   = z_sum + z(n)
        node_sum = node_sum + 1

      end do node_loop

      call lmpi_reduce(x_sum,       x_sum_global)
      call lmpi_reduce(y_sum,       y_sum_global)
      call lmpi_reduce(z_sum,       z_sum_global)
      call lmpi_reduce(node_sum, node_sum_global)

      if(lmpi_master) then
        x_scale_center = x_sum_global/real(node_sum_global,dp)
        y_scale_center = y_sum_global/real(node_sum_global,dp)
        z_scale_center = z_sum_global/real(node_sum_global,dp)
        write(*,*) ' SCALE:Scale center (average node position) :'
        write(*,*) ' SCALE...x=',x_scale_center
        write(*,*) ' SCALE...y=',y_scale_center
        write(*,*) ' SCALE...z=',z_scale_center
        write(*,*) ' SCALE...summation of nodes in average=',node_sum_global
      endif
      call lmpi_bcast(x_scale_center)
      call lmpi_bcast(y_scale_center)
      call lmpi_bcast(z_scale_center)
    else
      if(lmpi_master) then
        write(*,*) ' SCALE:Scale center (specified via namelist) :'
        write(*,*) ' SCALE...x=',x_scale_center
        write(*,*) ' SCALE...y=',y_scale_center
        write(*,*) ' SCALE...z=',z_scale_center
      endif
    endif

    if(.not.scale_about_nearest_grid_point) return

    x_avg = x_scale_center
    y_avg = y_scale_center
    z_avg = z_scale_center

    r_min = huge(1.0_dp)

!   Loop over the nodes and find the closest node

    do n = 1, nnodes0

      r = abs( x(n) - x_avg ) + &
          abs( y(n) - y_avg ) + &
          abs( z(n) - z_avg )

      if(r < r_min) then
        r_min          = r
        x_scale_center = x(n)
        y_scale_center = y(n)
        z_scale_center = z(n)
      endif

    end do

!   Find and broadcast minimum

    call lmpi_max_and_maxid(real(-r_min,dp),proc_with_max)
    call lmpi_bcast(r_min,proc_with_max)
    call lmpi_bcast(x_scale_center,proc_with_max)
    call lmpi_bcast(y_scale_center,proc_with_max)
    call lmpi_bcast(z_scale_center,proc_with_max)
    if(lmpi_master) then
      write(*,*) ' SCALE:Scale center redefined &
                 &(nearest node to specified point) :'
      write(*,*) ' SCALE...x=',x_scale_center
      write(*,*) ' SCALE...y=',y_scale_center
      write(*,*) ' SCALE...z=',z_scale_center
      write(*,*) ' SCALE...with minimum distance from specified point =',r_min
    endif

  end subroutine set_xyz_scale_center

!=================================== SET_SOLVE_BOX_MEANFLOW ==================80
!
! Set the residuals outside the solve box.
!
!=============================================================================80

  subroutine set_solve_box_meanflow( grid, soln, dof )

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    integer, intent(in) :: dof

    integer :: qeq, ib, dof_sta, dof_end, n_q, n_turb, n_mf, t, b

    real(dp), dimension(soln%n_q) :: q_exact

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = n_q - n_turb

    dof_sta = 1 ; dof_end = soln%dof0
    if ( dof > 0 ) then
      dof_sta = dof ; dof_end = dof
    endif

    if ( soln%eqn_set /= 1 .and. &
         soln%eqn_set /= 0 ) then
      call lmpi_conditional_stop(1,'eqn_set:set_solve_box_meanflow')
    endif

    if ( (grid%nbound > 20) .and.  skip_boundaries_selectively ) then
      if ( lmpi_master ) then
        write(*,*) ' BOX:...insufficient select_boundaries..stopping.'
      endif
      call lmpi_conditional_stop(1,'2:set_solve_box_meanflow')
    endif

    !...set residual outside box (solution recovered in <= 1 step)
    if ( skeleton > 0 ) then
      write(*,*)
      if ( skip_boundary_q ) then
        write(*,*) ' BOX:Box_solve.....set soln%res for boundary q.'
      elseif ( skip_boundaries_selectively ) then
        write(*,*) ' BOX:Box_solve.....set soln%res for select boundaries.'
        write(*,*) ' BOX:  bc_index   select_boundaries (=T, install exact.)'
        do ib=1,grid%nbound
          write(*,'(1x,a,i10,l20)') ' BOX:',grid%bc(ib)%ibc,   &
                                           select_boundaries(ib)
        enddo
      elseif ( skip_interior_q ) then
        write(*,*) ' BOX:Box_solve.....set soln%res for interior q.'
      endif
      if ( .not.infinite_box ) then
        write(*,*) ' BOX:Box_solve.....solving (R:MF) inside boxes:'
        do b=1,n_box
        write(*,*) ' BOX:...box=',b,' of n_box=',n_box
        write(*,*) ' BOX:...box_x_min=',real(box_x_min(b),dp)
        write(*,*) ' BOX:...box_x_max=',real(box_x_max(b),dp)
        write(*,*) ' BOX:...box_y_min=',real(box_y_min(b),dp)
        write(*,*) ' BOX:...box_y_max=',real(box_y_max(b),dp)
        write(*,*) ' BOX:...box_z_min=',real(box_z_min(b),dp)
        write(*,*) ' BOX:...box_z_max=',real(box_z_max(b),dp)
        write(*,*) ' BOX:...box_z_min=',real(box_s_min(b),dp)
        write(*,*) ' BOX:...box_z_max=',real(box_s_max(b),dp)
        enddo
      endif
      write(*,*) ' BOX:...residual block-size=',n_mf
    endif

    actions = 0
    if ( discrete_q_outside_box .or. box_seed ) then

      do qeq=dof_sta,dof_end
        if ( grid%skip_q(qeq) == 0 ) cycle
        !...override best practices...hardwire residual to zero.
        if ( .not.tightly_couple ) then
          soln%res(1:n_mf,qeq)       = 0._dp
        else
          soln%res(1:n_q,qeq)        = 0._dp
        endif
        actions = actions + 1
      enddo

    else

      do qeq=dof_sta,dof_end
        if ( grid%skip_q(qeq) == 0 ) cycle

        !...Skipped points should be exact conservative solution.
        !...set the residual to be of type res = vol*(current - desired).
        call exact_q( soln%eqn_set,  soln%n_q,                        &
                     grid%xq(qeq), grid%yq(qeq), grid%zq(qeq), q_exact)

        if ( .not.tightly_couple ) then
          soln%res(1:n_mf,qeq) =      grid%volq(qeq)*(                 &
                              soln%q_dof(1:n_mf,qeq) - q_exact(1:n_mf) )
        elseif ( grid%cc ) then
          soln%res(1:n_q,qeq) =       grid%volq(qeq)*(                 &
                               soln%q_dof(1:n_q,qeq) - q_exact(1:n_q) )
        else
          soln%res(1:n_mf,qeq) =      grid%volq(qeq)*(                 &
                              soln%q_dof(1:n_mf,qeq) - q_exact(1:n_mf) )
          do t=1,n_turb
            soln%turbres(t,qeq) =     grid%volq(qeq)*(                 &
                                    soln%turb(t,qeq) - q_exact(n_mf+t) )
          enddo
        endif

        actions = actions + 1

      enddo
      call check_value_faults('set_solve_box_meanflow')

    endif

    qeq = actions
    call lmpi_reduce(qeq,actions)
    if ( skeleton > 0 ) write(*,*) ' BOX:...actions=',actions

  end subroutine set_solve_box_meanflow


!============================== SET_SOLVE_BOX_TURB ===========================80
!
! Set the residuals outside the solve box.
!
!=============================================================================80

  subroutine set_solve_box_turb( grid, soln, dof )

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    integer, intent(in) :: dof

    integer :: qeq, n_s, dof_sta, dof_end, n_q, n_turb, n_mf, t, b

    real(dp), dimension(soln%n_q) :: q_exact

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = n_q - n_turb


    dof_sta = 1 ; dof_end = soln%dof0
    if ( dof > 0 ) then
      dof_sta = dof ; dof_end = dof
    endif

    if(soln%eqn_set /= 1 .and. soln%eqn_set /= 0) then
      call lmpi_conditional_stop(1,'eqn_set:set_solve_box_turb')
    endif

    !...set residual outside box (solution recovered in <= 1 update).
    if ( skeleton > 0 ) then
      write(*,*)
      if ( skip_boundary_q ) then
        write(*,*) ' BOX:Box_solve.....set soln%turbres at boundary q.'
      elseif ( skip_boundaries_selectively ) then
        write(*,*) ' BOX:Box_solve.....set soln%turbres for select boundaries.'
      elseif ( skip_interior_q ) then
        write(*,*) ' BOX:Box_solve.....set soln%turbres at interior q.'
      endif
      if ( .not.infinite_box ) then
        write(*,*) ' BOX:Box_solve.....solving (R:T) inside boxes:'
        do b=1,n_box
        write(*,*) ' BOX:...box=',b,' of n_box=',n_box
        write(*,*) ' BOX:...box_x_min=',real(box_x_min(b),dp)
        write(*,*) ' BOX:...box_x_max=',real(box_x_max(b),dp)
        write(*,*) ' BOX:...box_y_min=',real(box_y_min(b),dp)
        write(*,*) ' BOX:...box_y_max=',real(box_y_max(b),dp)
        write(*,*) ' BOX:...box_z_min=',real(box_z_min(b),dp)
        write(*,*) ' BOX:...box_z_max=',real(box_z_max(b),dp)
        write(*,*) ' BOX:...box_s_min=',real(box_s_min(b),dp)
        write(*,*) ' BOX:...box_s_max=',real(box_s_max(b),dp)
        enddo
      endif
      write(*,*) ' BOX:...residual block-size=',soln%n_turb
    endif

    actions = 0
    if ( discrete_q_outside_box .or. box_seed ) then

      do qeq=dof_sta,dof_end
        if ( grid%skip_q(qeq) == 0 ) cycle
        !...override best practices...hardwire residual to zero.
        if ( .not.tightly_couple ) then
          soln%turbres(1:n_turb,qeq) = 0._dp
        else
          soln%res(n_mf+1:n_q,qeq) = 0._dp
        endif
        actions = actions + 1
      enddo

    else

      do qeq=dof_sta,dof_end
        if ( grid%skip_q(qeq) == 0 ) cycle

        !...Skipped points should be exact conservative solution
        !...set the residual to be of type res = vol*(current - desired).
        call exact_q( soln%eqn_set, soln%n_q,                         &
                     grid%xq(qeq), grid%yq(qeq), grid%zq(qeq), q_exact)
        if ( .not.tightly_couple ) then
          if ( grid%cc ) then
            n_s = soln%n_q - soln%n_turb + 1
            soln%turbres(1:n_turb,qeq) = grid%volq(qeq)*(                  &
                                soln%q_dof(n_s:n_q,qeq) - q_exact(n_s:n_q) )
          else
            do t=1,n_turb
              soln%turbres(t,qeq) =      grid%volq(qeq)*(                 &
                                       soln%turb(t,qeq) - q_exact(n_mf+t) )
            enddo
          endif
        else
          if ( grid%cc ) then
            n_s = soln%n_q - soln%n_turb + 1
            soln%res(n_s:n_q,qeq)  =  grid%volq(qeq)*(                  &
                             soln%q_dof(n_s:n_q,qeq) - q_exact(n_s:n_q) )
          else
            do t=1,n_turb
              soln%res(n_mf+t,qeq) = grid%volq(qeq)*(                 &
                                   soln%turb(t,qeq) - q_exact(n_mf+t) )
            enddo
          endif
        endif
        actions = actions + 1

      enddo
      call check_value_faults('set_solve_box_turb')

    endif

    qeq = actions
    call lmpi_reduce(qeq,actions)
    if ( skeleton > 0 ) write(*,*) ' BOX:...actions=',actions

  end subroutine set_solve_box_turb

!=========================== FIND_ENCLOSING_BOX ==============================80
!
! Find enclosing box.
!
!=============================================================================80

  subroutine find_enclosing_box(nnodes0, nnodes01, x, y, z )

    integer, intent(in) :: nnodes0, nnodes01

    real(dp), dimension(nnodes01), intent(in) :: x,y,z

    integer :: n

    real(dp) :: x_min, x_max, y_min, y_max, z_min, z_max
    real(dp) :: xmin, xmax, ymin, ymax, zmin, zmax

    real(dp) :: xc, yc, zc

  continue

    x_min = huge(1.0_dp)
    y_min = huge(1.0_dp)
    z_min = huge(1.0_dp)

    x_max =-huge(1.0_dp)
    y_max =-huge(1.0_dp)
    z_max =-huge(1.0_dp)

!   Loop over the nodes and find the minimum and maximums.

    do n = 1, nnodes0

      call xyz_transform( x(n), y(n), z(n), xc, yc, zc )

      x_min = min ( x_min , xc )
      y_min = min ( y_min , yc )
      z_min = min ( z_min , zc )

      x_max = max ( x_max , xc )
      y_max = max ( y_max , yc )
      z_max = max ( z_max , zc )

    end do

    call lmpi_min(x_min, xmin)
    call lmpi_min(y_min, ymin)
    call lmpi_min(z_min, zmin)

    call lmpi_max(x_max, xmax)
    call lmpi_max(y_max, ymax)
    call lmpi_max(z_max, zmax)

    if ( lmpi_master ) then
      write(*,*)
      write(*,*) ' BOX:Coordinate limits of current grid:'
      write(*,"(1x,a,2f20.12)") ' BOX...x_min/max=',real(xmin,dp),&
                                                    real(xmax,dp)
      write(*,"(1x,a,2f20.12)") ' BOX...y_min/max=',real(ymin,dp),&
                                                    real(ymax,dp)
      write(*,"(1x,a,2f20.12)") ' BOX...z_min/max=',real(zmin,dp),&
                                                    real(zmax,dp)
    endif

  end subroutine find_enclosing_box

!================================= SET_IMPLICIT_BOX_MEANFLOW =================80
!
! Set Jacobians outside solve box.
!
!=============================================================================80

  subroutine set_implicit_box_meanflow(grid, soln, crow )

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: eq, ioff, jeq, row, remaining, npts, nadj, i, j, b

    logical :: tecplot_dumped = .false.

    character (len=256) :: filename

  continue

    if ( soln%eqn_set /= 1 .and. &
         soln%eqn_set /= 0 ) then
      call lmpi_conditional_stop(1,'eqn_set:set_implicit_box_meanflow')
    endif

    if ( skeleton > 0 ) then
      write(*,*)
      write(*,*) ' BOX:Box_solve...modifying soln%a_diag/a_off.'
      if ( skip_boundary_q ) then
        write(*,*) ' BOX:...diagonal Jacobians for boundary q.'
      elseif ( skip_boundaries_selectively ) then
        write(*,*) ' BOX:...diagonal Jacobians for select boundaries.'
      elseif ( skip_interior_q ) then
        write(*,*) ' BOX:...diagonal Jacobians for interior q.'
      endif
      if ( .not.infinite_box ) then
        write(*,*) ' BOX:...Solving (J:MF) inside boxes:'
        do b=1,n_box
        write(*,*) ' BOX:...box_x_min=',real(box_x_min(b),dp)
        write(*,*) ' BOX:...box_x_max=',real(box_x_max(b),dp)
        write(*,*) ' BOX:...box_y_min=',real(box_y_min(b),dp)
        write(*,*) ' BOX:...box_y_max=',real(box_y_max(b),dp)
        write(*,*) ' BOX:...box_z_min=',real(box_z_min(b),dp)
        write(*,*) ' BOX:...box_z_max=',real(box_z_max(b),dp)
        write(*,*) ' BOX:...box_s_min=',real(box_s_min(b),dp)
        write(*,*) ' BOX:...box_s_max=',real(box_s_max(b),dp)
        enddo
      endif
      write(*,*) ' BOX:...Jacobian block-size=',size(soln%a_diag,1)
    endif

    actions = 0
    do eq=1,soln%dof0
      if ( grid%skip_q(eq) <= 0 ) cycle
      row = crow%g2m(eq)
      soln%a_diag(:,:,row) = 0._dp

!     Set diagonal J terms to volume (unity for J-terms/volume).

      do jeq=1,size(soln%a_diag,1)
        soln%a_diag(jeq,jeq,row) = grid%volq(eq)*( 1._dp )
      enddo
      do ioff = crow%iam(row), crow%iam(row+1) - 1
        soln%a_off(:,:,ioff) = 0.0_odp
      end do
      actions = actions + 1
    enddo

    eq = actions
    call lmpi_reduce(eq,actions)
    if ( skeleton > 0 ) write(*,*) ' BOX:...actions=',actions

!   Dump some information on a single processor run.

    if ( tecplot_dumped .or. (lmpi_nproc > 1) ) return
    if ( skip_boundary_q .or. skip_interior_q ) return

    filename = 'box_solve_adjacencies'
    filename = trim(filename) // '_tec.dat'

    open(unit=56, file=filename,&
         status='unknown', position='append')

    rewind(56)

    write(56,'(a)') 'title="Adjacency at box solve points"'
    write(56,'(2a)') 'variables="x", "y", "z", "node"'

    remaining = 0
    do eq=1,soln%dof0
      if ( grid%skip_q(eq) > 0 ) cycle
      remaining = remaining + 1

      nadj = crow%ia(eq+1) - crow%ia(eq)
      npts = 2*(nadj) + 1
      write(56,*)'ZONE T="Adjacency Points" I=',npts

      i = eq
      write(56,'(3e23.15,i7)') real(grid%xq(i),dp),       &
                               real(grid%yq(i),dp),       &
                               real(grid%zq(i),dp), i
      do j=crow%ia(eq), crow%ia(eq+1) - 1
        i = crow%ja(j)
        write(56,'(3e23.15,i7)') real(grid%xq(i),dp),     &
                                 real(grid%yq(i),dp),     &
                                 real(grid%zq(i),dp), i
        i = eq
        write(56,'(3e23.15,i7)') real(grid%xq(i),dp),     &
                                 real(grid%yq(i),dp),     &
                                 real(grid%zq(i),dp), i
      enddo

    enddo

    tecplot_dumped = .true.
    close(56)

  end subroutine set_implicit_box_meanflow

!================================= SET_IMPLICIT_BOX_TURB =====================80
!
! Set Jacobians outside solve box.
!
!=============================================================================80

  subroutine set_implicit_box_turb(grid, soln, crow )

    type(grid_type), intent(in)    :: grid
    type(crow_flow), intent(in)    :: crow
    type(soln_type), intent(inout) :: soln

    integer :: eq, ioff, jeq, row, b

  continue

    if ( soln%eqn_set /= 1 .and. &
         soln%eqn_set /= 0 ) then
      call lmpi_conditional_stop(1,'eqn_set:set_implicit_box_turb')
    endif

    if ( skeleton > 0 ) then
      write(*,*)
      write(*,*) ' BOX:Box_solve...modifying soln%a_turb_diag/a_turb_off.'
      if ( skip_boundary_q ) then
        write(*,*) ' BOX:...diagonal Jacobians for boundary q.'
      elseif ( skip_boundaries_selectively ) then
        write(*,*) ' BOX:...diagonal Jacobians for select boundaries.'
      elseif ( skip_interior_q ) then
        write(*,*) ' BOX:...diagonal Jacobians for interior q.'
      endif
      if ( .not.infinite_box ) then
        write(*,*) ' BOX:...Solving (J:T) inside boxes:'
        do b=1,n_box
        write(*,*) ' BOX:...box=',b,' of n_box=',n_box
        write(*,*) ' BOX:...box_x_min=',real(box_x_min(b),dp)
        write(*,*) ' BOX:...box_x_max=',real(box_x_max(b),dp)
        write(*,*) ' BOX:...box_y_min=',real(box_y_min(b),dp)
        write(*,*) ' BOX:...box_y_max=',real(box_y_max(b),dp)
        write(*,*) ' BOX:...box_z_min=',real(box_z_min(b),dp)
        write(*,*) ' BOX:...box_z_max=',real(box_z_max(b),dp)
        write(*,*) ' BOX:...box_s_min=',real(box_s_min(b),dp)
        write(*,*) ' BOX:...box_s_max=',real(box_s_max(b),dp)
        enddo
      endif
      write(*,*) ' BOX:...Jacobian block-size=',size(soln%a_turb_diag,1)
    endif

    actions = 0
    do eq=1,soln%dof0
      if ( grid%skip_q(eq) <= 0 ) cycle
      row = crow%g2m(eq)
      soln%a_turb_diag(:,:,row) = 0._dp

!     Set diagonal J terms to volume (unity for J-terms/volume).

      do jeq=1,size(soln%a_turb_diag,1)
        soln%a_turb_diag(jeq,jeq,row) = grid%volq(eq)*( 1._dp )
      enddo
      do ioff = crow%iam(row), crow%iam(row+1) - 1
        soln%a_turb_off(:,:,ioff) = 0._dp
      end do
      actions = actions + 1
    enddo

    eq = actions
    call lmpi_reduce(eq,actions)
    if ( skeleton > 0 ) write(*,*) ' BOX:...actions=',actions

  end subroutine set_implicit_box_turb

!=================================== SET_SOLVE_BOX ==========================80
!
! Set or update the solving box.
!
!=============================================================================80

  subroutine set_solve_box( grid, soln, crow, level )

    use twod_util,             only : yplane_2d, y_coplanar_tol

    integer,         intent(in)    :: level
    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(in)    :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: qeq, expansions, ib, boundary_rinds, dof
    integer :: step, node, ierr
    integer :: cell_rinds_f, cell_rinds_c, b

    logical, dimension(soln%dof0) :: solve_union_boxes

    real(dp) :: xc, yc, zc, sc

  continue

    if( .not.box_solve ) return

!   First allocate skip_q, if needed.

    if ( .not.grid%skip_q_allocated ) then

      call my_alloc_ptr(grid%skip_q,soln%neq01)
      grid%skip_q(:) = 0
      grid%skip_q_allocated = .true.

    endif

    ! Set grid%skip_q (= 1 to skip).

    if ( lmpi_master ) then
      write(*,*) ' BOX:Box_solve.....level=',level
      write(*,*) ' BOX:Box_solve.....twod=',twod,' q_2d=',q_2d

      write(*,*)
      write(*,*)   ' BOX:Step 1...skipping boundary/interior values.'
      if ( skip_boundary_q ) then
        write(*,*) ' BOX:......Using all boundaries.'
        write(*,*) ' Box:......boundary_rind_expansions=',&
                               boundary_rind_expansions
      elseif ( skip_boundaries_selectively ) then
        write(*,*) ' BOX:......Using boundaries selectively.'
        write(*,*) ' Box:......boundary_rind_expansions=',&
                               boundary_rind_expansions
      endif
      if ( skip_interior_q ) then
        write(*,*) ' BOX:......skipping interior values.'
      endif

      write(*,*)
      write(*,*) ' BOX:Step 2...skipping off-plane (twod):'

      write(*,*)
      write(*,*) ' BOX:Step 3...skip outside union of x/y/z boxes:'
      do b=1,n_box
      write(*,*) ' BOX:...box=',b,' of n_box=',n_box
      write(*,*) ' BOX:...box_x_min=',real(box_x_min(b),dp)
      write(*,*) ' BOX:...box_x_max=',real(box_x_max(b),dp)
      write(*,*) ' BOX:...box_y_min=',real(box_y_min(b),dp)
      write(*,*) ' BOX:...box_y_max=',real(box_y_max(b),dp)
      write(*,*) ' BOX:...box_z_min=',real(box_z_min(b),dp)
      write(*,*) ' BOX:...box_z_max=',real(box_z_max(b),dp)
      enddo

      write(*,*)
      write(*,*) ' BOX:Step 4...skip outside union of distance function boxes:'
      do b=1,n_box
      write(*,*) ' BOX:...box=',b,' of n_box=',n_box
      write(*,*) ' BOX:...box_s_min=',real(box_s_min(b),dp)
      write(*,*) ' BOX:...box_s_max=',real(box_s_max(b),dp)
      enddo

      write(*,*)
      write(*,*) ' BOX:Step 5...skipping dof via seed:'
      write(*,*) ' BOX:..............box_seed=',box_seed
      write(*,*) ' BOX:...box_seed_expansions=',box_seed_expansions
      write(*,*) ' BOX:..........debug_q_proc=',debug_q_proc
      write(*,*) ' BOX:...........debug_q_loc=',debug_q_loc
      write(*,*) ' BOX:........debug_q_global=',debug_q_global

    endif

    grid%skip_q(:) = 0
    step = 0 ; call count_remaining(step, grid, soln) ; step = 1
    if ( skip_boundary_q .or. skip_interior_q .or. &
         skip_boundaries_selectively ) then

!     Flag q adjacent to the boundary.

      if ( skip_boundaries_selectively ) then
        call mark_select_boundaries_bcc(grid%bcc,                       &
                            grid%ncell01, grid%skip_q, select_boundaries)
      elseif ( grid%cc ) then
        call mark_boundaries_bcc(grid%bcc, grid%skip_q)
      else
        do ib = 1,grid%nbound
          call mark_boundaries(grid%skip_q,             grid%nnodes01,  &
                               grid%bc(ib)%ibc,    grid%bc(ib)%nbnode,  &
                               grid%bc(ib)%ibnode, twod )
        end do
      endif

      do qeq=1,soln%neq0
        if ( grid%skip_q(qeq) > 1 ) grid%skip_q(qeq) = 1
      enddo

      ierr = 0
      boundary_rinds = boundary_rind_expansions
      if ( boundary_rind_expansions < 0 .and. grid%cc ) then
        cell_rinds_f = abs( boundary_rind_expansions )
        cell_rinds_c = cell_rinds_f/ ( 2**( grid%igrid - 1 ) )
        if ( lmpi_master ) then
          write(*,*) ' BOX cell_rinds ( finest grid)=',cell_rinds_f
          write(*,*) ' BOX cell_rinds (current grid)=',cell_rinds_c
        endif
        if ( cell_rinds_c < 1 ) then
          ierr = 1
          write(*,*) ' cell_rinds_c < 0...stopping on grid%igrid=',grid%igrid
        endif
        boundary_rinds = cell_rinds_c - 1
      elseif ( boundary_rind_expansions < 0 ) then
        ierr = 1
        write(*,*) ' boundary_rind_expansions < 0...disallowed nc path.'
      endif
      call lmpi_conditional_stop(ierr,'boundary rinds:set_solve_box')
      do expansions=1,boundary_rinds

        !...mark nearboundaries with negative entry.
        if ( grid%cc ) then
          call lmpi_xfer( grid%skip_q )
          call mark_nearboundaries_cc(grid%nface, grid%fptr,           &
                                      grid%ncell0, grid%ncell01, grid%skip_q)
        else
          call mark_nearboundaries(grid%nnodes0,     grid%nnodes01,     &
                                   grid%nedgeloc,    grid%nedgeloc_2d,  &
                                   grid%eptr,        grid%skip_q)
        endif

        do qeq=1,soln%neq0
          if ( grid%skip_q(qeq) < 0 ) then
            grid%skip_q(qeq) = 1
          endif
        enddo

      enddo

      if ( skip_interior_q ) then
        do qeq=1,soln%neq0
         if ( grid%skip_q(qeq) > 0 ) then
           grid%skip_q(qeq) = 0
         else
           grid%skip_q(qeq) = 1
         endif
        enddo
      endif

      step = 1; call count_remaining(step, grid, soln)

    endif

    ! Off-solve-plane points (node-centered 2D path).

    if ( twod ) then
      do node=1,grid%nnodes01
        if(abs(grid%y(node)-yplane_2d) < y_coplanar_tol) cycle
        grid%skip_q(node) = 1
      enddo
      step = 2; call count_remaining(step, grid, soln)
    endif

    if ( .not.infinite_box ) then
      !Set box based on position.
      if ( n_box == 1 ) then
      do dof=1,soln%dof0
        if ( grid%skip_q(dof) == 1 ) cycle
        call xyz_transform( grid%xq(dof), grid%yq(dof), grid%zq(dof), &
                            xc, yc, zc )
        do b=1,n_box
        if ( xc < box_x_min(b) .or. &
             xc > box_x_max(b) .or. &
             yc < box_y_min(b) .or. &
             yc > box_y_max(b) .or. &
             zc < box_z_min(b) .or. &
             zc > box_z_max(b) ) then
          grid%skip_q(dof) = 1
        endif
        enddo
      enddo
      else
      solve_union_boxes(:) = .false.
      do dof=1,soln%dof0
        call xyz_transform( grid%xq(dof), grid%yq(dof), grid%zq(dof), &
                            xc, yc, zc )
        do b=1,n_box
        if ( xc >= box_x_min(b) .and. &
             xc <= box_x_max(b) .and. &
             yc >= box_y_min(b) .and. &
             yc <= box_y_max(b) .and. &
             zc >= box_z_min(b) .and. &
             zc <= box_z_max(b) ) then
          solve_union_boxes(dof) = .true.
        endif
        enddo
      enddo
      do dof=1,soln%dof0
        if ( grid%skip_q(dof) == 1 ) cycle
        if ( solve_union_boxes(dof) ) cycle
        grid%skip_q(dof) = 1
      enddo
      endif
      step = 3 ; call count_remaining(step, grid, soln)
    endif

    if ( .not.infinite_box .and. grid%idistfcn > 0 ) then
      !Set box based on distance function.
      if ( n_box == 1 )then
        do dof=1,soln%dof0
          if ( grid%skip_q(dof) == 1 ) cycle
          sc = grid%slen(dof)
          do b=1,n_box
          if ( sc < box_s_min(b) .or. sc > box_s_max(b) ) then
            grid%skip_q(dof) = 1
          endif
          enddo
        enddo
      else
      solve_union_boxes(:) = .false.
      do dof=1,soln%dof0
        sc = grid%slen(dof)
        do b=1,n_box
        if ( sc >= box_s_min(b) .and. &
             sc <= box_s_max(b) ) then
          solve_union_boxes(dof) = .true.
        endif
        enddo
      enddo
      do dof=1,soln%dof0
        if ( grid%skip_q(dof) == 1 ) cycle
        if ( solve_union_boxes(dof) ) cycle
        grid%skip_q(dof) = 1
      enddo
      endif
      step = 4; call count_remaining(step, grid, soln)
    endif

    ! Skipping specified dof.

    if ( box_seed ) then
      call set_box_seed( grid, soln, crow )
      step = 5; call count_remaining(step, grid, soln)
    endif

    if ( lmpi_nproc <= 2 ) &
    call box_tecplot( grid, soln, level )

  end subroutine set_solve_box

!=================================== BOX_TECPLOT =============================80
!
! Tecplot file.
!
!=============================================================================80

  subroutine box_tecplot( grid, soln, level )

    use string_utils,          only : int_to_s

    integer,         intent(in) :: level
    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln

    integer :: dof, file, file_start, pts, i, ii

    real(dp) :: xc, yc, zc, sc

    character (len=256) :: filename

  continue

    ! file = 0 skipped_points skip_q = 1
    ! file = 1  solved_points skip_q = 0

    file_start = 1 ; if ( lmpi_nproc == 1 ) file_start = 0

    do file=file_start,1

      pts = 0
      do dof=1,soln%dof0
        if ( grid%skip_q(dof) == file ) cycle
        pts = pts + 1
      enddo

      filename = 'box_skip_xyz'
      if ( file == 1 ) filename = 'box_solve_xyz'
      filename = trim(filename)                                 &
               // '_grid' // trim(int_to_s(level))              &
               // '_pid' // trim(int_to_s(lmpi_id)) // '_tec.dat'
      write(*,"(1x,a,i12,2a)") ' BOX:...pts=',pts,&
      ' written to file=',trim(filename)

      open(unit=56, file=filename,&
           status='unknown', position='append')

      rewind(56)

      write(56,'(a)') 'title="Points for solve box"'
      write(56,'(2a)') 'variables="pt", "node", "x", "y", "z", "slen", "wall" '
      write(56,*)'ZONE T="Points" I=',pts

      i = 0
      do dof=1,soln%dof0
        if ( grid%skip_q(dof) == file ) cycle
        i = i + 1
        sc = -999._dp ; ii = -1
        if ( grid%idistfcn > 0 ) then
          sc = grid%slen(dof) ; ii = 0
          if ( sc < epsilon(1._dp) ) ii = 1
        endif
        write(56,'(2i6,4e23.15,i3)') i, dof,          &
        real(grid%xq(dof),dp), real(grid%yq(dof),dp), &
        real(grid%zq(dof),dp), real(sc,dp), ii
      enddo

      close(56)

      if ( .not.box_cylindrical .and. .not.box_spherical ) cycle

      filename = 'box_skip_xyzc'
      if ( file == 1 ) filename = 'box_solve_xyzc'
      filename = trim(filename)                                 &
               // '_level' // trim(int_to_s(level))             &
               // '_oid' // trim(int_to_s(lmpi_id)) // '_tec.dat'
      write(*,"(1x,a,i12,2a)") ' BOX:...pts=',pts,&
      ' written to file=',trim(filename)

      open(unit=56, file=filename,&
           status='unknown', position='append')

      rewind(56)

      write(56,'(a)') 'title="Points for solve box"'
      write(56,'(2a)') 'variables="pt", "node", "x", "y", "z"'
      write(56,*)'ZONE T="Points" I=',pts

      i = 0
      do dof=1,soln%dof0
        if ( grid%skip_q(dof) == file ) cycle
        i = i + 1
        call xyz_transform( grid%xq(dof), grid%yq(dof), grid%zq(dof), &
                            xc, yc, zc )
        write(56,"(1x,2i6,4e20.12)") i, dof, real(xc,dp), real(yc,dp), &
                                             real(zc,dp)
      enddo

      close(56)

    enddo

  end subroutine box_tecplot

!=========================== XYZ_TRANSFORM ===================================80
!
! Transform coordinates.
!
!=============================================================================80

  subroutine xyz_transform(x, y, z, xc, yc, zc )
    use trig_utils,      only : skew_radians, skew_degrees

    real(dp), intent(in)  :: x, y, z
    real(dp), intent(out) :: xc, yc, zc

    real(dp) :: rc, tc, pc, tr, xt, yt, zt

  continue

    xc = x
    yc = y
    zc = z

    if ( box_spherical ) then

      !xg = ru*cos( theta )
      !yg = ru*sin( theta )*sin( phi )
      !zg = ru*sin( theta )*cos( phi )
      !x =  xg
      !z =  yg
      !y = -zg

      xt =  x
      zt =  y
      yt = -z

      rc = sqrt( xt**2 + yt**2 + zt**2 )

      tr = skew_radians( xt / rc )
      tc = skew_degrees( xt / rc )

      pc = skew_degrees( zt / ( rc * sin(tr) ) )

      xc = rc
      yc = tc
      zc = pc

    elseif ( box_cylindrical ) then

      !x = -ru * cos(theta)
      !y =  ru * sin(theta)
      !z =  zmin + real(k-1,dp)*delta_z

      rc = sqrt( x**2 + z**2 )

      tc = skew_degrees( -x / rc )

      xc = rc
      yc = tc
      zc = z

    endif

  end subroutine xyz_transform

!=========================== COUNT_REMAINING =================================80
!
! Count remaining points in solve region.
!
!=============================================================================80

  subroutine count_remaining(step, grid, soln)

    integer,         intent(in) :: step
    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln

    integer :: remaining, skipped, i

  continue

    skipped = sum( grid%skip_q(1:soln%dof0) )

    i = skipped ; call lmpi_reduce(i,skipped) ; call lmpi_bcast(skipped)
    remaining = soln%dofg - skipped
    if ( lmpi_master ) then
      if ( step == 0 )                                           &
      write(*,"(1x,a,a,4(3x,a),5x,a)") ' BOX:...',               &
      '      ','soln%dofg',' selected','remaining','      %-remaining'
      write(*,"(1x,a,a,i1,3i12,f20.10)") ' BOX:...','Step ', &
      step,soln%dofg,skipped,remaining,                      &
      100._dp*real(remaining,dp)/real(soln%dofg,dp)
    endif

    if ( remaining == 0 ) then
      call lmpi_conditional_stop(1,'no remaining dof:count_remaining')
    endif

  end subroutine count_remaining

!================================= SET_BOX_SOLVE_LEVELS ======================80
!
! Set box solve for levels within FAS.
!
!=============================================================================80

  subroutine set_box_solve_levels( fl_start, fl_end )

    integer, intent(in) :: fl_start, fl_end

    integer :: fl

  continue

    box_solve_grid_level(fl_start) = box_solve_fas_finest
    do fl=fl_start+1,fl_end
      box_solve_grid_level(fl) = box_solve_fas_coarser
    enddo

    if ( .not. box_solve ) return

    if ( .not. lmpi_master ) return

    write(*,"(1x,a,a,a)") ' BOX:...',' level',' box_solve_grid_level'
    do fl=fl_start,fl_end
      write(*,"(1x,a,i6,L21)")                                        &
      ' BOX:...',fl,box_solve_grid_level(fl)
    enddo

  end subroutine set_box_solve_levels

!=================================== SET_BOX_Q_DOF ===========================80
!
! Set Q for overspecified solution.
!
!=============================================================================80

  subroutine set_box_q_dof( grid, soln, turbulence_only )

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln

    logical, intent(in), optional :: turbulence_only

    integer :: dof, n_q, n_turb, n_mf, n_s

    real(dp), dimension(soln%n_q) :: q_exact

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = n_q - n_turb
    n_s    = n_mf + 1

    if ( discrete_q_outside_box .and. grid%igrid /= 1 ) then

      write(*,*) 'discrete_q_outside_box not set up for multigrid.'
      call lmpi_conditional_stop(1,'set_box_q_dof')

    elseif ( tightly_couple ) then

      call lmpi_conditional_stop(1,'tightly_couple:set_box_q_dof')

    elseif ( present(turbulence_only) .and. n_turb == 0 ) then

      call lmpi_conditional_stop(1,'n_turb issue:set_box_q_dof')

    endif

    do dof=1,size(soln%q_dof,2)
      if ( grid%skip_q(dof) == 0 ) cycle

      call exact_q( soln%eqn_set,  n_q,                     &
                   grid%xq(dof), grid%yq(dof), grid%zq(dof), q_exact)

      if ( present(turbulence_only) ) then

        if ( grid%cc ) then
          soln%q_dof( n_s:n_q,dof) = q_exact(n_s:n_q)
        else
          soln%turb( 1:n_turb,dof) = q_exact(n_s:n_q)
        endif

      elseif ( n_turb == 0 .or. grid%cc ) then
        soln%q_dof(1:n_q,dof)    = q_exact(1:n_q)
      else
        soln%q_dof(1:n_q,   dof) = q_exact(  1:n_mf)
        soln%turb( 1:n_turb,dof) = q_exact(n_s:n_q )
      endif

    enddo
    call check_value_faults('set_box_q_dof')

  end subroutine set_box_q_dof

!=================================== SET_BOX_SEED ============================80
!
! Set skip_q according to seed and expansions.
!
!=============================================================================80

  subroutine set_box_seed( grid, soln, crow )

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(in)    :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: dof, dof_expansions, ia, dofa, ipe, off_proc_n, dof01
    integer :: ierr, local

    integer, dimension(0:lmpi_nproc-1) :: proc1, proc2

    logical :: off_processor_expansion = .true.

    integer, dimension(:), allocatable ::       off_proc_list
    integer, dimension(:), allocatable :: local_off_proc_list

  continue

    ierr = 0

    dof01 =         size( grid%skip_q, 1 )
    ierr  = dof01 - size(  soln%q_dof, 2 )
    call lmpi_conditional_stop(ierr,'Off-processor:box_seed')

    if ( box_seed_expansions >= 1 ) &
    allocate( off_proc_list( dof01 - soln%dof0 ) )

    if ( lmpi_id == debug_q_proc ) then
      grid%skip_q(debug_q_loc) = 2
    endif

    do dof_expansions = 1,box_seed_expansions

      off_proc_list = 0
      off_proc_n    = 0

      do dof=1,soln%dof0
        if ( grid%skip_q(dof) /= 2 ) cycle
        do ia=crow%ia(dof),crow%ia_ns(dof)-1
          dofa = crow%ja(ia)
          if ( grid%skip_q(dofa) > 0 ) cycle
          grid%skip_q(dofa) = 3
          if ( dofa > soln%dof0 ) then
            off_proc_n = off_proc_n + 1
            if ( grid%cc ) then
              off_proc_list(off_proc_n) = grid%cl2g(dofa)
            else
              off_proc_list(off_proc_n) =  grid%l2g(dofa)
            endif
          endif
        enddo
      enddo

      proc1 = 0
      if (  off_processor_expansion ) then

        proc2 = 0
        proc2(lmpi_id) = off_proc_n ; call lmpi_reduce(proc2, proc1)
        call lmpi_bcast(proc1)

      endif

      if ( sum(proc1) > 0 ) then

        do ipe=0,lmpi_nproc-1

          if ( proc1(ipe) == 0 ) cycle

          allocate( local_off_proc_list( proc1(ipe) ) )

          if ( lmpi_id == ipe ) local_off_proc_list(1:proc1(ipe)) = &
                                      off_proc_list(1:proc1(ipe))

          call lmpi_bcast( local_off_proc_list, ipe )

          do dof=1,soln%dof0
            do local = 1, proc1(ipe)
              if ( grid%cc ) then
                if ( grid%cl2g(dof) == local_off_proc_list(local) ) then
                  grid%skip_q(dof) = 3
                endif
              else
                if ( grid%l2g(dof) == local_off_proc_list(local) ) then
                  grid%skip_q(dof) = 3
                endif
              endif
            enddo
          enddo

          deallocate( local_off_proc_list )

        enddo

      endif

      call lmpi_xfer( grid%skip_q )

      do dof=1,dof01
        if ( grid%skip_q(dof) >= 2 ) grid%skip_q(dof) = grid%skip_q(dof) - 1
      enddo

    enddo

    do dof=1,soln%dof0
      grid%skip_q(dof) = min( 1, grid%skip_q(dof) )
    enddo

    call lmpi_xfer( grid%skip_q )

    if ( box_seed_expansions >= 1 ) &
    deallocate(off_proc_list)

    do dof=1,dof01
      if ( grid%skip_q(dof) > 0 ) then
        grid%skip_q(dof) = 0
      else
        grid%skip_q(dof) = 1
      endif
    enddo

  end subroutine set_box_seed

!=============================== ECHO_NML_BOX ================================80
!
! Echo namelist for box solution.
!
!=============================================================================80

  subroutine echo_nml_box( f )

    integer          , intent(in) :: f

    integer :: u, j, ib, b

  continue

    if ( .not. lmpi_master ) return

    if ( f /= 6 ) then
      u = f
      write(u,*)
      write(u,nml=box)
      if ( .not.document_namelist ) return
    else
      u = 6
      if ( .not.echo ) return
    endif

    !..echo quantities of interest

    write(u,*)
    write(u,"(1x,a)")     ' ..Solving within windows:'
    write(u,"(1x,a,L12)") ' ....box_solve=',box_solve
    write(u,"(1x,a)")     ' ..Solving within windows on finest FAS level:'
    write(u,"(1x,a,L12)") ' ....box_solve_fas_finest=',box_solve_fas_finest
    write(u,"(1x,a)")     ' ..Solving within windows on coarser FAS levels:'
    write(u,"(1x,a,L12)") ' ....box_solve_fas_coarser=',box_solve_fas_coarser
    write(u,*)
    write(u,"(1x,a)")     ' ..The windowing regions can be set via boxes &
                          &or interior/bc flags.'
    write(u,"(1x,a)")     ' ..A simple window is a Cartesian box.'
    write(u,"(1x,a)")     ' ..The windows can be viewed via grid%skip_q:'
    write(u,"(1x,a)")     ' .....=0, point is solved.'
    write(u,"(1x,a)")     ' .....=1, point is skipped (set by other means).'
    write(u,*)
    write(u,"(1x,a)")     ' ..Related to shape/type of a box-type window:'
    write(u,"(1x,a)")     ' ..(Typically a Cartesian box)'
    write(u,"(1x,a,L12)") ' ......box_cylindrical=',box_cylindrical
    write(u,"(1x,a,L12)") ' ........box_spherical=',box_spherical
    write(u,"(1x,a,L12)") ' .........infinite_box=',infinite_box
    write(u,*)
    write(u,"(1x,a)")     ' ..A maximum of 5 boxes can be specified:'
    do b=1,n_box
      write(u,"(1x,2(a,i2))")  ' ....box=',b,' of n_box=',n_box
      write(u,"(1x,a,e20.10)") ' ....box_x_min=',real(box_x_min(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_x_max=',real(box_x_max(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_y_min=',real(box_y_min(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_y_max=',real(box_y_max(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_z_min=',real(box_z_min(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_z_max=',real(box_z_max(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_s_min=',real(box_s_min(b),dp)
      write(u,"(1x,a,e20.10)") ' ....box_s_max=',real(box_s_max(b),dp)
    enddo
    write(u,*)
    write(u,"(1x,a)")     ' ..Related to q outside of solve region:'
    write(u,"(1x,a,L1)") ' .....discrete_q_outside_box=',&
                                discrete_q_outside_box
    write(u,"(1x,a)")     ' .....If discrete_q_outside_box = T'
    write(u,"(1x,a)")     ' .....==> Q set via a reference discrete solution.'
    write(u,"(1x,a)")     ' .....If discrete_q_outside_box = F'
    write(u,"(1x,a)")     ' .....==> Q set via q_exact.'

    write(u,*)
    write(u,"(1x,a)")     ' ..Related to read/write of reference solution:'
    write(u,"(1x,a,L1)") ' .....reference_q_write=',&
                                reference_q_write
    write(u,"(1x,a,L1)") ' .....reference_q_read=',&
                                reference_q_read
    write(u,"(1x,a,L1)") ' .....reference_meanflow=',&
                                reference_meanflow
    write(u,"(1x,a)")    ' .......(=T => set dQ=0)'
    write(u,"(1x,a,L1)") ' .....reference_turbulence=',&
                                reference_turbulence
    write(u,"(1x,a)")    ' .......(=T => set dturb=0)'

    write(u,*)
    write(u,"(1x,a)")     ' ..Related to interior/bc flags:'
    write(u,"(1x,a,L12)") ' ................skip_boundary_q=',skip_boundary_q
    write(u,"(1x,a,L12)") ' ................skip_interior_q=',skip_interior_q
    write(u,"(1x,a,L12)")       ' ....skip_boundaries_selectively=',&
                                      skip_boundaries_selectively
    write(u,"(1x,a)")           ' .....If skip_boundaries_selectively = T&
                                & then up to 20 boundaries can be selected.'
    write(u,"(1x,a,10(1x,L1))") ' .......select_boundaries( 1:10)=',&
                                         select_boundaries( 1:10)
    write(u,"(1x,a,10(1x,L1))") ' .......select_boundaries(11:20)=',&
                                         select_boundaries(11:20)
    write(u,"(1x,a)")     ' ..Notes:'
    write(u,"(1x,a)")     ' ..(1) For cell-centered, cell-centers face-adjacent'
    write(u,"(1x,a)")     '       to the boundaries selected are skipped.'
    write(u,"(1x,a)")     ' ..(2) 2D sidewall boundaries should be marked F.'

    if ( skip_boundaries_selectively ) then
      write(u,*)
      write(u,*) ' BOX:...skip_boundaries_selectively=',&
                          skip_boundaries_selectively
      write(u,*) ' BOX:...select_boundary=T to set connecting cell center',&
                 ' value to exact solution.'
      write(u,*) ' BOX: boundary   select_boundaries'
      j=0
      do ib=1,20
        if ( select_boundaries(ib) ) then
          j=j+1
          write(u,'(1x,a,i8,l20,a)') ' BOX: ',ib,select_boundaries(ib),      &
                                     ' (corresponding cell centers are exact)'
        else
          write(u,'(1x,a,i8,l20)') ' BOX: ',ib,select_boundaries(ib)
        endif
      enddo
      write(u,*) ' BOX:...Number of selected boundaries =',j
      write(u,*) ' BOX:...Note: 2D sidewall boundaries should be marked F.'
    endif

    write(u,*)
    write(u,"(1x,a)") ' ..Related to solving at seeded dof and expansions &
    &based on edge connectivity.'
    write(u,*)
    write(u,*) ' BOX:.............box_seed=',box_seed
    write(u,*) ' BOX:..box_seed_expansions=',box_seed_expansions

  end subroutine echo_nml_box

end module solve_box

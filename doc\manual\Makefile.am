DIST_SUBDIRS = images

TEXSRC = \
	NASA.cls \
	about.tex \
	abstract.tex \
	acknowledgments.tex \
	adjoint_solver.tex \
	boundary_conditions.tex \
	conventions.tex \
	design.tex \
	extract_namelists.rb \
	fixltx2e.sty \
	flow_solver.tex \
	fun3d_input_files.tex \
	grid_adaptation.tex \
	grids.tex \
	installation.tex \
	introduction.tex \
	manual.bib \
	manual.tex \
	grid_motion.tex \
	nasa.bst \
	nasalogo.pdf \
	personal_dictionary.en.pws \
	quick_start.tex \
	troubleshooting.tex
	
EXTRA_DIST = \
	$(TEXSRC)

CLEANFILES = $(MANPDF) $(MANNAME).log $(MANNAME).idx $(MANNAME).out \
	$(MANNAME).toc $(MANAUX) *.aux nml_*.tex *.bbl *.blg

docdir = ${prefix}/doc
doc_DATA = $(docfiles)
docfiles =

if BUILD_PDFLATEX_SUPPORT
docfiles += $(MANPDF)

MANNAME = manual

MANTEX = $(MANNAME).tex
MANAUX = $(MANNAME).aux
MANPDF = $(MANNAME).pdf

TEXPATH=./:$(srcdir)/:$(top_srcdir)/doc/:$(top_srcdir)//:

$(MANPDF): $(srcdir)/$(MANTEX) $(TEXSRC)
	$(srcdir)/extract_namelists.rb --path $(top_srcdir)
	TEXINPUTS=$(TEXPATH): $(PDFLATEX) $(srcdir)/$(MANTEX)
	BSTINPUTS=$(TEXPATH): BIBINPUTS=$(TEXPATH): $(BIBTEX) $(MANNAME)
	TEXINPUTS=$(TEXPATH): $(PDFLATEX) $(srcdir)/$(MANTEX)
	TEXINPUTS=$(TEXPATH): $(PDFLATEX) $(srcdir)/$(MANTEX)
endif

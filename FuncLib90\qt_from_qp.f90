!================================= QT_FROM_QP ================================80
!
! Variables (rho,u,v,w,T) from primitive variables.
!
!=============================================================================80

  pure function qt_from_qp( eqn_set, n_q, qp )

    use flux_constants, only : gamma
    use solution_types, only : compressible

    integer,                 intent(in) :: eqn_set, n_q
    real(dp), dimension(:),  intent(in) :: qp
    real(dp), dimension(n_q)            :: qt_from_qp

  continue

    qt_from_qp(1:n_q) = qp(1:n_q)

    if ( eqn_set == compressible ) qt_from_qp(5) = gamma*qp(5)/qp(1)

  end function qt_from_qp

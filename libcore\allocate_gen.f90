module allocate_gen

  implicit none

  private

  public :: my_gen_alloc_ptr
  public :: inc_gen_alloc ! increment allocation by 1

  interface my_gen_alloc_ptr
    module procedure spec_prop_my_alloc
    module procedure kin_3_my_alloc
    module procedure kin_2_my_alloc
    module procedure kin_1_my_alloc
  end interface

  interface inc_gen_alloc
    module procedure spec_prop_inc_alloc
    module procedure kin_2_inc_alloc
    module procedure kin_1_inc_alloc
  end interface

contains

!============================= SPEC_PROP_MY_ALLOC ============================80
!
! Allocate space for type spec_prop arrays
!
!=============================================================================80

  subroutine spec_prop_my_alloc(x,dim1)

    use kinddefs,      only : dp
    use physics_types, only : spec_prop, max_ranges

    type(spec_prop), dimension(:), pointer    :: x
    integer,                       intent(in) :: dim1

    integer :: status, i, j

    continue

    allocate (x(dim1),stat=status)
    if (status /= 0) stop "Allocation failed for spec_prop"

    if (dim1 > 0) then
      x(:)%species              = "                        "
      x(:)%mol_wt               = 0._dp
      x(:)%cprt0                = 0._dp
      x(:)%heat_for             = 0._dp
      x(:)%elec_impct_ion       = 0._dp
      x(:)%alantel              = 0._dp
      x(:)%disoc_ener           = 0._dp
      do i = 1,5
        x(:)%elements(i)        = "  "
        x(:)%number(i)          = 0
      end do
      x(:)%n_elements           = 0
      do i = 1,3
        x(:)%siga(i)            = 0._dp
      end do
      do i = 1,max_ranges

        do j = 1,10
            x(:)%coef(j,i)      = 0._dp
        end do

        x(:)%t_lower(i)         = 0._dp
        x(:)%t_upper(i)         = 0._dp
      end do

      x(:)%number_thermo_ranges = 0
      x(:)%molecule = .false.
      x(:)%ion = .false.
      x(:)%electron = .false.
      x(:)%charge = 0

    end if

    return

  end subroutine spec_prop_my_alloc

!============================= KIN_3_MY_ALLOC ================================80
!
! Allocate space for type kin_3 arrays
!
!=============================================================================80

  subroutine kin_3_my_alloc(x,dim1,dim2)

    use allocations,   only : my_alloc_ptr
    use physics_types, only : kin_3

    type(kin_3), dimension(:), pointer    :: x
    integer,                   intent(in) :: dim1, dim2

    integer :: i, status

    continue

    allocate (x(dim1),stat=status)
    if (status /= 0) stop "Allocation failed for type kin_3"

    do i = 1, dim1
      call my_alloc_ptr(x(i)%j,       dim2)
      call my_alloc_ptr(x(i)%int_exp, dim2)
      call my_alloc_ptr(x(i)%m_i,     dim2)
      call my_alloc_ptr(x(i)%m_r,     dim2)
      call my_alloc_ptr(x(i)%n2m,     dim2)
    end do

    return

  end subroutine kin_3_my_alloc

!=============================== KIN_2_MY_ALLOC ==============================80
!
! Allocate space for type kin_2 arrays
!
!=============================================================================80

  subroutine kin_2_my_alloc(x,dim1,dim2)

    use kinddefs,      only : dp
    use physics_types, only : kin_2

    type(kin_2), dimension(:,:), pointer    :: x
    integer,                     intent(in) :: dim1, dim2

    integer :: status

    continue

    allocate (x(dim1,dim2),stat=status)
    if (status /= 0) stop "Allocation failed for kin_2"

    if (dim1>0 .and. dim2>0) then
      x%jr    = 0
      x%rexp0 = .false.
      x%reac0 = 0
      x%reac1 = 0._dp
      x%jp    = 0
      x%pexp0 = .false.
      x%prod0 = 0
      x%prod1 = 0._dp
    end if

    return

  end subroutine kin_2_my_alloc

!=============================== KIN_1_MY_ALLOC ==============================80
!
! Allocate space for type kin_1 arrays
!
!=============================================================================80

  subroutine kin_1_my_alloc(x,dim1)

    use kinddefs,      only : dp
    use physics_types, only : kin_1

    type(kin_1), dimension(:), pointer    :: x
    integer,                   intent(in) :: dim1

    integer :: status

    continue

    allocate (x(dim1),stat=status)
    if (status /= 0) stop "Allocation failed for kin_1"

    if (dim1>0 ) then
      x%j_reac             = 0
      x%j_prod             = 0
      x%forward            = .false.
      x%backward           = .false.
      x%gen_col_ptnr       = .false.
      x%electron_impact    = .false.
      x%a                  = 0._dp
      x%b                  = 0._dp
      x%c                  = 0._dp
      x%ake                = 0._dp
      x%bke                = 0._dp
      x%cke                = 0._dp
      x%dke                = 0._dp
      x%eke                = 0._dp
      x%exp1               = 0.7_dp
      x%exp2               = 0.3_dp
      x%t_eff_min1         = 1000._dp
      x%t_eff_max1         = 50000._dp
      x%t_eff_min2         = 1000._dp
      x%t_eff_max2         = 50000._dp
      x%indx_forward_teff  = 1
      x%indx_backward_teff = 1
      x%elec_impact_target = 0
    end if
    return

  end subroutine kin_1_my_alloc

!=============================== SPEC_PROP_INC_ALLOC =========================80
!
! Increment allocation by 1 for rank 1 type spec_prop arrays
!
!=============================================================================80

  subroutine spec_prop_inc_alloc(x,length)

    use kinddefs,      only : dp
    use physics_types, only : spec_prop, max_ranges

    type(spec_prop), dimension(:), pointer    :: x
    integer,                       intent(in) :: length

    type(spec_prop), dimension(:), pointer :: dum

    integer :: i, j, status

    continue

    call my_gen_alloc_ptr(dum,length)
    if (length> 0) dum(:) = x(:)
    status = 0
    if (length > 0) deallocate (x, STAT=status)
    if (status /= 0) stop "Incremental allocation failed for type spec_prop"

    call my_gen_alloc_ptr(x,length+1)

    if (length > 0) x(1:length)      = dum(1:length)
    deallocate (dum)
    x(length+1)%species              = "                        "
    x(length+1)%mol_wt               = 0._dp
    x(length+1)%cprt0                = 0._dp
    x(length+1)%heat_for             = 0._dp
    x(length+1)%elec_impct_ion       = 0._dp
    x(length+1)%alantel              = 0._dp
    x(length+1)%disoc_ener           = 0._dp
    x(length+1)%elements             = "  "
    x(length+1)%number               = 0
    x(length+1)%n_elements           = 0

    do i = 1,3
      x(length+1)%siga(i)            = 0._dp
    end do

    do i = 1,max_ranges
      do j = 1,10
        x(length+1)%coef(j,i)        = 0._dp
      end do
      x(length+1)%t_lower(i)         = 0._dp
      x(length+1)%t_upper(i)         = 0._dp
    end do

    x(length+1)%number_thermo_ranges = 0
    x(length+1)%molecule             = .false.
    x(length+1)%ion                  = .false.
    x(length+1)%electron             = .false.
    x(length+1)%charge               = 0

    return

  end subroutine spec_prop_inc_alloc

!================================ KIN_2_INC_ALLOC ============================80
!
! Increment allocation by 1 for type kin_2 arrays
!
!=============================================================================80

  subroutine kin_2_inc_alloc(x,dim1,dim2,extent)

    use physics_types, only : kin_2

    type(kin_2), dimension(:,:), pointer    :: x
    integer,                     intent(in) :: dim1   ! first extent
    integer,                     intent(in) :: dim2   ! second extent
    integer,                     intent(in) :: extent ! extent to increment

    type(kin_2), dimension(:,:), pointer :: dum

    integer :: status

    continue

    call my_gen_alloc_ptr(dum,dim1,dim2)

    dum = x

    deallocate (x, stat = status)

    if (status /= 0) stop "Incremental allocation failed for type kin_2"

    select case (extent)
      case (1)
        call my_gen_alloc_ptr(x,dim1+1,dim2)
      case (2)
        call my_gen_alloc_ptr(x,dim1,dim2+1)
      case default
        write (6,*) "Illegal value for extent in call to inc_alloc"
        write (6,*) "extent = ", extent, " whereas value must be 1 or 2"
        stop
    end select

    if (dim1>0 .and. dim2>0) x(1:dim1,1:dim2) = dum(1:dim1,1:dim2)

    deallocate (dum, stat = status)

    return

  end subroutine kin_2_inc_alloc

!================================ KIN_1_INC_ALLOC ============================80
!
! Increment allocation by 1 for type kin_1 arrays
!
!=============================================================================80

  subroutine kin_1_inc_alloc(x,dim1)

    use physics_types, only : kin_1

    type(kin_1), dimension(:), pointer    :: x
    integer,                   intent(in) :: dim1               ! first extent

    type(kin_1), dimension(:), pointer :: dum

    integer :: status

    continue

    call my_gen_alloc_ptr(dum,dim1)

    dum = x
    deallocate (x, stat = status)

    if (status /= 0) stop "Incremental allocation failed for type kin_1"

    call my_gen_alloc_ptr(x,dim1+1)

    if (dim1>0 ) x(1:dim1) = dum(1:dim1)

    deallocate (dum, stat = status)

    return

  end subroutine kin_1_inc_alloc

end module allocate_gen

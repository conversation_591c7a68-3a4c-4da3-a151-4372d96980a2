module search

  use kinddefs, only : dp

  implicit none

  private

  public :: disdr_wigton_seperate

  integer :: iptr,imax      ! Wigton distance stuff
  integer :: ifptr,ifmax    ! Wigton distance stuff
  integer :: totbbv,nbbv    ! Wigton distance stuff
  integer :: minpbb,maxlev  ! Wigton distance stuff
  integer :: nwv,nbb

  real(dp), dimension(3) :: bbmin, bbmax   ! Wigton distance stuff

  logical, parameter :: watch_alloc = .false.

contains

!========================== DISDR_WIGTON_SEPERATE ============================80
!
! Serves as a driver routine for computing distance to closest solid surface
!
! Note: search region involves information across all the blocks
! for all solid segments)
!
! Completely new version of disdr introduced by <PERSON> May 1995.
! Uses routines written by <PERSON> based on a recursive-box algorithm.
! This speeds up nearest point calculation.
!
!=============================================================================80

  subroutine disdr_wigton_seperate(nnodes,x,y,z,                               &
                                   nsurfnodes, xsurf, ysurf, zsurf,            &
                                   smin,wk3d5,iwrk,indexx )

    use kinddefs, only : dp

    integer,                         intent(in)    :: nsurfnodes
    real(dp), dimension(nsurfnodes), intent(in)    :: xsurf, ysurf, zsurf
    integer,                         intent(in)    :: nnodes
    real(dp), dimension(:),          intent(in)    :: x, y, z
    real(dp), dimension(:),          intent(inout) :: smin
    integer,  dimension(:),          intent(out)   :: indexx
    real(dp), dimension(*),          intent(out)   :: wk3d5
    integer,  dimension(*),          intent(inout) :: iwrk

!   [..variable ivnode gets changed by call to collect_surf.]
!   [..variable indexx gets changed by call to calc_dist.]
!   [..variable wk3d5 gets changed by call to collect surf, sort_x, and
!    ..calc_dist.]

    integer :: nsurf
!   dimension wk3d5(5*mx3dwk), iwrk(2*mx3dwk)
    integer :: mxszf, mxszi
    integer :: vlist, ipv, bbdef, surf, ntri, ntotv
    integer :: minbox, nbb

    continue

!   initialize the distance function

    smin = huge(1.0_dp)

!   nsurf will count the number of viscous surface points.

    nsurf = nsurfnodes
!   print *,"nsurf=",nsurf

!   now collect all the viscous surface points into an array
!   xsurf(nsurf,3)

    mxszf = 4*nnodes + 14*nsurf
    mxszi = nnodes + 12*nsurf
!   mx3dwk = 4*nnodes + 14*nsurf
    call initi(mxszi)
    call initf(mxszf)

!   surf(i)  == wk3d5(surf+i-1)
!   ntri(i)  == iwrk( ntri+i-1)

    surf = ifalloc(3*nsurf)
    ntri = iialloc(nsurf)

    call collect_surf_seperate (nsurfnodes, xsurf, ysurf, zsurf,               &
         wk3d5(surf), iwrk(ntri) )

!   to help locate identical points on boundaries, we sort with
!   respect to x coordinate.  better to use a stable sort on
!   x,y,z and ntri consecutively, but this is good enough
!   for time being.

    call sort_x(nsurf,wk3d5(surf),iwrk(ntri), wk3d5,iwrk)

!   now run mac ice routines for making boxes that surround
!   groups of viscous surface points

!   time1=second()
    minbox=sqrt(float(nsurf))
    minbox=max(minbox,50)
    minbox=min(minbox,nsurf) ! account for small cases
    nbb = 3*nsurf/minbox

!   bbdef(1..6,i) == xmin,xmax,ymin,ymax,zmin,zmax of i-th
!                    bounding box.
!   ipv(1,i) == number of points in i-th bounding box.
!   ipv(2,i) == location in vlist of points in i-th bounding box
!   vlist(ipv(2,i),...,vlist(ipv(2,i)+ipv(1,i)-1) ==
!         location in array surf of points in i-th bounding box

    bbdef = ifalloc(6*nbb)
    ipv = iialloc(2*nbb)
    vlist = iialloc(nsurf)
    call makebb(minbox,10, nsurf,                                              &
                wk3d5(surf),wk3d5(surf+nsurf),                                 &
                wk3d5(surf+2*nsurf),                                           &
                nbb, ntotv, wk3d5(bbdef),                                      &
                iwrk(ipv),iwrk(vlist),iwrk)
!   time2=second()
!   print *,"time to set up boxes=",time2-time1

!   now calculate distance from each point in the field to
!   points on the viscous surfaces
!   and then look at neighboring triangles

!   the nearest point on any surface to a field point is found
!   as follows:

!   1. the distance from the field point to each bounding
!        box is calculated.
!   2. the nearest bounding box is found, and the the nearest
!        point in that box is found (by exhaustive search.)
!   3. go to the next nearest bounding box. if it is farther
!        away than the nearest point found so far, then we have
!        the nearest point.
!   4. find the nearest point in this box, and compare with the
!        previous nearest point. return to step 3.

    call calc_dist (nnodes,x,y,z, smin, indexx,nsurf,wk3d5(surf),nbb,          &
                    wk3d5(bbdef), iwrk(ipv),iwrk(vlist),iwrk(ntri),            &
                    wk3d5)

!   computation of distance to node points is now complete
!   on the finest grid

    if (watch_alloc) print *,'alloc cleanup disdr_wigton'

!   call ifree(nsurf)
!   call ifree(2*nbb)
!   call ffree(6*nbb)
!   call ffree(3*nsurf)
!   call ifree(nsurf)

! free all -- at end of disdr_wigton

    call ifree(-1)
    call ffree(-1)

  end subroutine disdr_wigton_seperate

!============================= COLLECT_SURF_SEPERATE =========================80
!
! Description goes here.
!
!=============================================================================80

  subroutine collect_surf_seperate (nsurfnodes, xsurf, ysurf, zsurf, surf, ntri)

    use kinddefs, only : dp

    integer,                            intent(in)  :: nsurfnodes
    real(dp), dimension(nsurfnodes),    intent(in)  :: xsurf,ysurf,zsurf
    real(dp), dimension(nsurfnodes, 3), intent(out) :: surf
    integer,  dimension(nsurfnodes),    intent(out) :: ntri

    integer :: i

    continue

!   collect surface points.

    do i=1,nsurfnodes
      surf(i,1) = xsurf(i)
      surf(i,2) = ysurf(i)
      surf(i,3) = zsurf(i)
      ntri(i)   = i
    end do ! i

  end subroutine collect_surf_seperate

!================================== SORT_X ===================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine sort_x(nsurf,surf,ntri,wk3d5,iwrk)

    use kinddefs, only : dp
    use sort,     only : heap_sort

    integer,                      intent(in)    :: nsurf
    real(dp), dimension(nsurf,3), intent(inout) :: surf
    integer,  dimension(nsurf),   intent(inout) :: ntri
    real(dp), dimension(*),       intent(inout) :: wk3d5
    integer,  dimension(*),       intent(inout) :: iwrk

!   [..variables surf and  wk3d5 get changed by call to move_real.]

    integer, dimension(:), allocatable :: sorted_index

    integer :: iperm, temp, itemp

    continue

    iperm = iialloc(nsurf) ! Beware side effect: increments module variable iptr
    if (.false.) write(*,*) iperm ! avoid compiler warnings

    temp  = ifalloc(nsurf)

!----------
! Revised code to work with g95.  Tested with lahey and Intel.

   allocate(sorted_index(nsurf)); sorted_index = 0
   call heap_sort(nsurf,surf(:,1),sorted_index)
   call move_real(nsurf,surf(1,1),sorted_index,wk3d5(temp))
   call move_real(nsurf,surf(1,2),sorted_index,wk3d5(temp))
   call move_real(nsurf,surf(1,3),sorted_index,wk3d5(temp))

   call ffree(nsurf)

   itemp = iialloc(nsurf)
   call move_integer(nsurf,ntri,sorted_index,iwrk(itemp))
   deallocate(sorted_index)
!-----------
! Original wigton code
!
!   call heap_sort(nsurf,surf(:,1),iwrk(iperm:iperm+nsurf-1))
!   call move_real(nsurf,surf(1,1),iwrk(iperm),wk3d5(temp))
!   call move_real(nsurf,surf(1,2),iwrk(iperm),wk3d5(temp))
!   call move_real(nsurf,surf(1,3),iwrk(iperm),wk3d5(temp))
!
!   call ffree(nsurf)
!
!   itemp = iialloc(nsurf)
!   call move_integer(nsurf,ntri,iwrk(iperm),iwrk(itemp))
!----------

    call ifree(nsurf)

    call ifree(nsurf)

  end subroutine sort_x

!================================== MAKEBB ===================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine makebb(minpb0,maxlv0,nwv0,wx,wy,wz,nbb0,bblen,bbdef,iv,vlist,iwrk)

    use kinddefs, only : dp

    integer,                  intent(in)  :: minpb0, maxlv0, nwv0
    integer,                  intent(out) :: nbb0
    real(dp), dimension(*),   intent(in)  :: wx, wy, wz
    integer,  dimension(*),   intent(out) :: iwrk
    integer,                  intent(out) :: bblen
    real(dp), dimension(6,*), intent(out) :: bbdef
    integer,  dimension(2,*), intent(out) :: iv
    integer,  dimension(*),   intent(out) :: vlist

!   [..the variables bbdef, iv and vlist get changed by call to sdpltbb]

    integer :: bbi, level,i

!   integer :: totbbv,nbbv,minpbb,maxlev,nwv,nbb
!   real(dp) :: bbmin(3), bbmax(3)
!   the above two lines of declaration have been moved to module level

!   [..there is no need for common blocks declaration in fortran 90]
!   common /bbcom/ nbbv,minpbb,maxlev,nwv,nbb,totbbv, bbmin(3), bbmax(3)

    continue

    minpbb = minpb0
    maxlev = maxlv0
    nwv    = nwv0
    nbb    = 0
    totbbv = 0

!   the array bbi is a list of the surface points in wx,wy,wz.

    bbi = iialloc(nwv)
    do i=1,nwv
      iwrk(bbi+i-1) = i
    end do

!   create a bounding box containing all the surface points.

    call calcbb(bbmin,bbmax,nwv,iwrk(bbi),wx,wy,wz)

!   now subdivide the bounding box. subdivision occurs maxlev
!   times, or until there are less than minpbb points in the
!   bounding box, whichever comes first.

    level = 0
    call spltbb(level,bbdef,iv,vlist,iwrk(bbi),wx,wy,wz)
    call ifree(nwv)
    nbb0  = nbb
    bblen = totbbv
!   print *,'nbb0,bblen: ',nbb0,bblen

  end subroutine makebb

!==================================== CALCBB =================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine calcbb(bbmin,bbmax,nvi,vi,wx,wy,wz)

    use kinddefs, only : dp

    real(dp), dimension(3), intent(out):: bbmin,bbmax
    integer,  dimension(*), intent(in) :: vi
    integer,                intent(in) :: nvi
    real(dp), dimension(*), intent(in) :: wx,wy,wz

    real(dp) :: bval, sval

    integer :: i, j

  continue

!   vi is an array of nvi indices into the wx,wy,wz arrays.
!   these indices represent nvi points. this subroutine
!   creates a bounding box that contains these nvi points.

    bval =  huge(1.0_dp) !  [..it is not clear where the values of
    sval = -huge(1.0_dp) !  bval and sval are obtained for the first time]

    do i=1,3
      bbmin(i) = bval
      bbmax(i) = sval
    end do

    do j=1,nvi
       i = vi(j)
       if(wx(i) < bbmin(1)) bbmin(1) = wx(i)
       if(wy(i) < bbmin(2)) bbmin(2) = wy(i)
       if(wz(i) < bbmin(3)) bbmin(3) = wz(i)
       if(wx(i) > bbmax(1)) bbmax(1) = wx(i)
       if(wy(i) > bbmax(2)) bbmax(2) = wy(i)
       if(wz(i) > bbmax(3)) bbmax(3) = wz(i)
    end do

  end subroutine calcbb

!=================================== GETVRT ==================================80
!
! Searches the wx,wy,wz arrays to find all points that fall
! within a bounding box.
!
!=============================================================================80

  subroutine getvrt(bbmin,bbmax,nwv,nbbv,bbi,wx,wy,wz)

    use kinddefs, only : dp

    real(dp), dimension(3), intent(in)  :: bbmin, bbmax
    integer,  dimension(*), intent(out) :: bbi
    real(dp), dimension(*), intent(in)  :: wx, wy, wz
    integer,                intent(out) :: nbbv
    integer,                intent(in)  :: nwv

    integer :: i

    continue

!   this subroutine searches the wx,wy,wz arrays to find
!   all points that fall within a bounding box.

    nbbv = 0
    do i=1,nwv
      if( (wx(i) >= bbmin(1)).and.(wx(i) <= bbmax(1)).and.                     &
          (wy(i) >= bbmin(2)).and.(wy(i) <= bbmax(2)).and.                     &
          (wz(i) >= bbmin(3)).and.(wz(i) <= bbmax(3)))then
           nbbv = nbbv + 1
           bbi(nbbv) = i
      end if
    end do

  end subroutine getvrt

!================================== SHELLS ===================================80
!
! Shell sort (see Knuth)
!
!=============================================================================80

  subroutine shells(n,is,s)

    use kinddefs, only : dp

    integer,                intent(in)    :: n
    integer,  dimension(*), intent(inout) :: is
    real(dp), dimension(*), intent(in)    :: s

    integer :: i, j, gap,temp

    continue

    gap = n / 2

    positive_gap : do
      if (gap <= 0) exit positive_gap
      half_gap_loop : do i = gap+1, n
        j = i - gap
        move_is_elements : do
          if (j <= 0) cycle half_gap_loop
          if (s(is(j)) <= s(is(j+gap))) cycle half_gap_loop

          temp = is(j)
          is(j) = is(j+gap)
          is(j+gap) = temp
          j = j - gap
        enddo move_is_elements
      enddo half_gap_loop
      gap = gap / 2
    enddo positive_gap

  end subroutine shells

!=================================== SPLTBB ==================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine spltbb(level0,bbdef,iv,vlist,bbi,wx,wy,wz)

    use kinddefs, only : dp

    integer,                  intent(in)  :: level0
    real(dp), dimension(6,*), intent(out) :: bbdef
    integer,  dimension(*),   intent(out) :: bbi
    integer,  dimension(2,*), intent(out) :: iv
    integer,  dimension(*),   intent(out) :: vlist
    real(dp), dimension(*),   intent(in)  :: wx, wy, wz

    integer :: isptr, k, i, i1, i2, iright
    integer :: level

    integer, parameter :: maxbb=200

    real(dp) :: stack(7,maxbb)
    real(dp) :: parmin(3), parmax(3)
    real(dp) :: plmin(3), plmax(3)
    real(dp) :: prmin(3), prmax(3)
    real(dp) :: bx, by, bz

    continue

    isptr = 1
    call push(stack,isptr,bbmin,bbmax,level0)
    k = 1
    do
      if (isptr <= 1) exit
      call pop(stack,isptr,parmin,parmax,level)
      call getvrt(parmin,parmax,nwv,nbbv,bbi,wx,wy,wz)
      if ((nbbv >= minpbb) .and. level < maxlev) then
        bx = parmax(1) - parmin(1)
        by = parmax(2) - parmin(2)
        bz = parmax(3) - parmin(3)
        if (bx >= by.and.bx >= bz) then
          call shells(nbbv,bbi,wx)
          iright = nbbv/2
          i1     = bbi(iright)
          i2     = bbi(iright+1)
          do
            if (iright >= nbbv)   exit
            if (real(wx(i1),dp) /= real(wx(i2),dp)) exit
            iright = iright + 1
            i1     = bbi(iright)
            i2     = bbi(iright+1)
          end do
        elseif (by >= bx.and.by >= bz) then
          call shells(nbbv,bbi,wy)
          iright = nbbv/2
          i1     = bbi(iright)
          i2     = bbi(iright+1)
          do
            if (iright >= nbbv)   exit
            if (real(wy(i1),dp) /= real(wy(i2),dp)) exit
            iright = iright + 1
            i1     = bbi(iright)
            i2     = bbi(iright+1)
          end do
        else
          call shells(nbbv,bbi,wz)
          iright = nbbv/2
          i1 = bbi(iright)
          i2 = bbi(iright+1)
          do
            if (iright >= nbbv)   exit
            if (real(wz(i1),dp) /= real(wz(i2),dp)) exit
            iright = iright + 1
            i1     = bbi(iright)
            i2     = bbi(iright+1)
          end do
        end if
        call calcbb(plmin,plmax,iright,bbi,wx,wy,wz)
        call calcbb(prmin,prmax,nbbv-iright,bbi(iright+1),                   &
                    wx,wy,wz)
        call push(stack,isptr,prmin,prmax,level+1)
        call push(stack,isptr,plmin,plmax,level+1)
      else
        if (nbbv > 0) then
          nbb = nbb + 1
          bbdef(1,nbb) = parmin(1)
          bbdef(2,nbb) = parmax(1)
          bbdef(3,nbb) = parmin(2)
          bbdef(4,nbb) = parmax(2)
          bbdef(5,nbb) = parmin(3)
          bbdef(6,nbb) = parmax(3)
          iv(1,nbb)    = nbbv
          iv(2,nbb)    = k
          do i=1,nbbv
            vlist(k) = bbi(i)
            k        = k + 1
            totbbv   = totbbv + 1
          end do
        end if
      end if
    end do

  end subroutine spltbb

!=================================== PUSH ====================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine push(st,is,bbmin,bbmax,lev)

    use kinddefs, only : dp

    real(dp), dimension(3),   intent(in)    :: bbmin,bbmax
    real(dp), dimension(7,*), intent(out)   :: st
    integer,                  intent(inout) :: is
    integer,                  intent(in)    :: lev

    integer :: i, ii

    continue

    do i=1,3
       ii = i+3
       st(i,is) = bbmin(i)
       st(ii,is) = bbmax(i)
    end do
    st(7,is) = lev + 0.10_dp
    is = is + 1

  end subroutine push

!====================================== POP ==================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine pop(st,is,bbmin,bbmax,lev)

    use kinddefs, only : dp

    real(dp), dimension(7,*), intent(in)    :: st
    real(dp), dimension(3),   intent(out)   :: bbmin, bbmax
    integer,                  intent(inout) :: is
    integer,                  intent(out)   :: lev

    integer :: i, ii

    continue

    is = is - 1
    do i=1,3
       ii = i+3
       bbmin(i) = st(i,is)
       bbmax(i) = st(ii,is)
    end do
    lev = st(7,is)

  end subroutine pop

!=================================== CALC_DIST ===============================80
!
! Description goes here.
!
!=============================================================================80

  subroutine calc_dist (ng, xg, yg, zg, smin,indexx, nsurf, surf, nbb, bbdef,  &
                        ipv, vlist, ntri, wk3d5)

    use kinddefs, only : dp

    integer,                      intent(in)  :: ng, nsurf, nbb
    real(dp), dimension(ng),      intent(in)  :: xg, yg, zg
    integer,  dimension(ng),      intent(out) :: indexx
    real(dp), dimension(ng),      intent(out) :: smin
    real(dp), dimension(*),       intent(out) :: wk3d5
    integer,  dimension(nsurf),   intent(in)  :: vlist
    integer,  dimension(2,nbb),   intent(in)  :: ipv
    real(dp), dimension(nsurf,3), intent(in)  :: surf
    real(dp), dimension(6,nbb),   intent(in)  :: bbdef
    integer,  dimension(nsurf),   intent(in)  :: ntri

!   [..smin gets changed by call to bbdist.]
!   [..wk3d5 gets changed by call to bbdist.]

    integer :: i
    integer :: ncalc

    continue

!   print *,"ng=",ng

!   time1=second()
    call bbdist(ng,xg,yg,zg,nsurf,surf, nbb,bbdef,ipv,vlist,smin,indexx,       &
                ncalc,wk3d5)
!   time2=second()
!   print *,"time for finding nearest points=",time2-time1

!   put in calculation to triangles

    do i = 1,ng
       indexx(i) = ntri(indexx(i))
    end do

  end subroutine calc_dist

!=================================== MOVE_REAL ===============================80
!
! Description goes here.
!
!=============================================================================80

  subroutine move_real(n,x,iperm,temp)

    use kinddefs, only : dp

    integer,                intent(in)    :: n
    real(dp), dimension(n), intent(inout) :: x
    integer,  dimension(n), intent(in)    :: iperm
    real(dp), dimension(n), intent(inout) :: temp

    integer :: i

    continue

    do i=1,n
      temp(i)=x(iperm(i))
    end do

    do i=1,n
      x(i)=temp(i)
    end do

  end subroutine move_real

!=============================== MOVE_INTEGER ================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine move_integer(n,ix,iperm,itemp)

    integer,               intent(in)    :: n
    integer, dimension(n), intent(inout) :: ix
    integer, dimension(n), intent(in)    :: iperm
    integer, dimension(n), intent(inout) :: itemp

    integer :: i

    continue

    do i=1,n
       itemp(i)=ix(iperm(i))
    end do

    do i=1,n
      ix(i)=itemp(i)
    end do

  end subroutine move_integer

!==================================== BBDIST =================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine bbdist(ng,xg,yg,zg,nsurf,surf,                                    &
                    nbb,bbdef,ipv,vlist,dist,idist,ncalc,wk3d5)

    use kinddefs, only : dp

    integer,                      intent(in)  :: ng, nbb, nsurf
    integer,                      intent(out) :: ncalc
    real(dp), dimension(nsurf,3), intent(in)  :: surf
    real(dp), dimension(ng),      intent(out) :: dist
    integer,  dimension(ng),      intent(out) :: idist
    real(dp), dimension(ng),      intent(in)  :: xg,yg,zg
    real(dp), dimension(6,nbb),   intent(in)  :: bbdef
    integer,  dimension(2,nbb),   intent(in)  :: ipv
    real(dp), dimension(*),       intent(out) :: wk3d5
    integer,  dimension(nsurf),   intent(in)  :: vlist

!   [..ncalc, dist, idist and wk3d5 get changed by call to bbdst1.]

    integer :: wrk, tsurf, test

    continue

    wrk   = ifalloc(nbb)
    tsurf = ifalloc(3*nsurf)
    test  = ifalloc(nsurf)
    call bbdst1(ng,xg,yg,zg,nsurf,surf,nbb,bbdef,ipv,vlist,dist, idist, ncalc, &
                wk3d5(wrk),wk3d5(tsurf),wk3d5(test))
    call ffree(nsurf)
    call ffree(3*nsurf)
    call ffree(nbb)

  end subroutine bbdist

!=================================== BBDST1 ==================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine bbdst1(ng,xg,yg,zg,nsurf,surf,nbb,bbdef,ipv,vlist,dist,idist,     &
                    ncalc,wrk,tsurf,test)

    use kinddefs, only : dp

    integer,                      intent(in)  :: ng, nbb, nsurf
    integer,                      intent(out) :: ncalc
    real(dp), dimension(nsurf,3), intent(in)  :: surf
    real(dp), dimension(ng),      intent(out) :: dist
    integer,  dimension(ng),      intent(out) :: idist
    real(dp), dimension(ng),      intent(in)  :: xg,yg,zg
    real(dp), dimension(6,nbb),   intent(in)  :: bbdef
    integer,  dimension(2,nbb),   intent(in)  :: ipv
    real(dp), dimension(nbb),     intent(out) :: wrk
    real(dp), dimension(nsurf,3), intent(out) :: tsurf
    real(dp), dimension(nsurf),   intent(out) :: test
    integer,  dimension(nsurf),   intent(in)  :: vlist

    integer :: i, k, kk, l, jj
    integer :: n, ii, kmin, j, jp

    real(dp) :: x, y, z, xs, ys, zs, px, py, pz
    real(dp) :: bbmin, testmin, smin

    continue

    do i=1,nsurf
      kk=vlist(i)
      tsurf(i,1)=surf(kk,1)
      tsurf(i,2)=surf(kk,2)
      tsurf(i,3)=surf(kk,3)
    end do

    ncalc=0
    do i=1,ng
      x=xg(i)
      y=yg(i)
      z=zg(i)
      smin=1.0e34_dp
      jp=0
!     build table of distances from grid to bounding boxes
      do j=1,nbb
        px=x
        if (px  <=  bbdef(1,j)) px=bbdef(1,j)
        if (px  >=  bbdef(2,j)) px=bbdef(2,j)
        py=y
        if (py  <=  bbdef(3,j)) py=bbdef(3,j)
        if (py  >=  bbdef(4,j)) py=bbdef(4,j)
        pz=z
        if (pz  <=  bbdef(5,j)) pz=bbdef(5,j)
        if (pz  >=  bbdef(6,j)) pz=bbdef(6,j)
        wrk(j)=(x-px)**2+(y-py)**2+(z-pz)**2
      end do
      do j=1,nbb
!       find nearest bounding box that has not been searched
        bbmin=wrk(1)

        do ii=2,nbb
          bbmin=min(bbmin,wrk(ii))
        end do

        jj=isrcheq(nbb,wrk,1,bbmin)
        wrk(jj)=2.0e34_dp
!       stop searching when nearest bounding box is too far away
        if (bbmin  >  smin) exit
        n=ipv(1,jj)
        l=ipv(2,jj)
        testmin=1.0e34_dp

        do k=1,n
          xs=tsurf(l,1)
          ys=tsurf(l,2)
          zs=tsurf(l,3)
          l=l+1
          test(k)=(x-xs)**2+(y-ys)**2+(z-zs)**2
          testmin=min(testmin,test(k))
        end do

        ncalc=ncalc+n
        if (testmin  <  smin) then
          kmin=isrcheq(n,test,1,testmin)
          smin=testmin
          jp=vlist(ipv(2,jj)+kmin-1)
        end if
      end do
!     dist(i)=sqrt(smin)
      dist(i)=smin
      idist(i)=jp
    end do

  end subroutine bbdst1

!===================================== INITF =================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine initf(isize)

    integer, intent(in) :: isize

  continue

    ifptr = 1
    ifmax = isize

    if (watch_alloc) print '(a,2i10)', 'initf',ifptr,ifmax

  end subroutine initf

!===================================== INITI =================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine initi(isize)

    integer, intent(in) :: isize

    continue

    iptr = 1
    imax = isize

    if (watch_alloc) print '(a,2i10)', 'initi',iptr,imax

  end subroutine initi

!====================================== IFREE ================================80
!
! Description goes here.
!
!=============================================================================80

  subroutine ifree(isize)

    integer, intent(in) :: isize

    continue

    if (watch_alloc) print '(a,2i10)', 'ifree',iptr,isize

    if (isize < iptr) then
       iptr = iptr - isize
       if (isize < 0) iptr = 0 ! free all
    else
       print *,'freeing too much: ',isize,iptr,' FUN3D suggestion: --near_tree'
       stop
    end if

  end subroutine ifree

!======================================= FFREE ===============================80
!
! Description goes here.
!
!=============================================================================80

  subroutine ffree(isize)

    integer, intent(in) :: isize

    continue

    if (watch_alloc) print '(a,2i10)', 'ffree',ifptr,isize

    if (isize < ifptr) then
       ifptr = ifptr - isize
       if (isize < 0) ifptr = 0 ! free all
    else
       print *,'freeing too much: ',isize,ifptr,' FUN3D suggestion: --near_tree'
       stop
    end if

  end subroutine ffree

!==================================== IIALLOC ================================80
!
! Increments the global variable 'iptr' by the argument, and returns the
! value that iptr had prior to incrementing.
!
! Prints an error message if the value to be returned is greater than
! another global variable 'imax' and stops without returning.
!
!=============================================================================80

  integer function iialloc(isize)

    integer, intent(in) :: isize

    continue

    iialloc = iptr
    iptr = iptr + isize
    if (iptr > (imax+1)) then
       print *,'iialloc failed: ',isize, iptr-isize
       stop
    end if

    if (watch_alloc) print '(a,2i10)', 'iialloc',iptr,isize

  end function iialloc

!=============================================================================80
!
! Increments the global variable 'ifptr' by the argument, and returns the
! value that ifptr had prior to incrementing.
!
! Prints an error message if the value to be returned is greater than
! another global variable 'imax' and stops without returning.
!
!=============================================================================80

  integer function ifalloc(isize)

    integer, intent(in) :: isize

    continue

    ifalloc = ifptr
    ifptr = ifptr + isize
    if (ifptr > (ifmax+1)) then
       print *,'ifalloc failed: ',isize,ifptr-isize
       stop
    end if

    if (watch_alloc) print '(a,2i10)', 'ifalloc',ifptr,isize

  end function ifalloc

!=============================================================================80
!
! Compares the elements in an array x with a target.
!
! The search is carried out a maximum of 'n' number of times, with the index
! incrementing each time by incx. on locating an element with value
! equal to the target, it returns the number of attempts made in
! locating the element.
!
!=============================================================================80

  integer function isrcheq(n,x,incx,target)

    use kinddefs, only : dp

    integer,                intent(in) :: n, incx
    real(dp), dimension(*), intent(in) :: x
    real(dp),               intent(in) :: target

    integer :: j, i

    continue

    j = 1
    isrcheq = 0
    if (n <= 0) return
    if (incx < 0) j = 1 - (n-1)*incx
    do i=1,n
      if (x(j) == target) exit
      j = j + incx
    end do
    isrcheq = i

  end function isrcheq

end module search

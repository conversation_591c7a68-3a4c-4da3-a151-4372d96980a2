
pure function in_conserved_variables(prim)

  use kinddefs,        only : dp
  use fluid,           only : gm1

  real(dp), dimension(5), intent(in) :: prim

  real(dp), dimension(5)             :: in_conserved_variables

  real(dp), parameter :: half = 0.5_dp

  continue

  in_conserved_variables(1)   = prim(1)
  in_conserved_variables(2:4) = prim(1) * prim(2:4)
  in_conserved_variables(5)   = prim(5)/gm1 + &
    half*prim(1)*(prim(2)*prim(2)+prim(3)*prim(3)+prim(4)*prim(4))

end function in_conserved_variables

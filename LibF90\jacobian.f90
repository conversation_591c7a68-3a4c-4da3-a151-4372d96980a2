module jacobian

  use kinddefs,            only : dp, odp
  use rtiming,             only : rtime
  use lmpi,                only : lmpi_id
  use inviscid_flux,       only : flux_construction_lhs
  use grid_motion_helpers, only : need_grid_velocity
  use info_depr,           only : skeleton

  implicit none

  private

  public :: inviscid_jacobian
  public :: inviscid_jacobian_i
  public :: dfldfsscp !, cp_dfdc

  logical, parameter :: timing_jac = .false.

contains

!=============================== INVISCID_JACOBIAN ===========================80
!
! Driver routine to compute inviscid jacobians - compressible
!
!=============================================================================80

  subroutine inviscid_jacobian(grid, soln, crow)

    use grid_types,     only : grid_type
    use solution_types, only : soln_type
    use comprow_types,  only : crow_flow
    use info_depr,      only : lowmach_prec
    use flux_fds_aj,    only : roe_approx_jacobians
    use inviscid_flux,  only : mean_decouple, dc_part, gen_use_perf_jac

    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

  continue

    if ( timing_jac ) call rtime('start:inviscid_jacobian:interior')

    if ( lowmach_prec ) then

!     jacobians for Roe's fluxes with low Mach preconditioning

      call lowmach_prec_roe_jacobians(grid%nnodes0, grid%nnodes01,             &
                         grid%nedgeloc, grid%nedgeloc_2d, soln%max_nnz,        &
                         grid%eptr, soln%q_dof, soln%a_diag, soln%a_off,       &
                         grid%xn, grid%yn, grid%zn, grid%ra, crow%fhelp,       &
                         grid%facespeed, grid%ncell01, .true., soln%n_tot,     &
                         soln%njac, crow%g2m)

    elseif ( flux_construction_lhs == 'roe-approximate' ) then

!     approximate jacobians for Roe's fluxes

      call roe_approx_jacobians(                                               &
                      grid%nedgeloc,      grid%nedgeloc_2d,                    &
                      grid%nnodes0,       grid%nnodes01,    soln%max_nnz,      &
                      soln%q_dof,         soln%a_diag,      soln%a_off,        &
                      grid%eptr,          grid%xn,          grid%yn,           &
                      grid%zn,            grid%ra,          crow%fhelp,        &
                      grid%facespeed,     soln%n_tot,       soln%njac,         &
                      crow%g2m)

    elseif ( flux_construction_lhs == 'roe' ) then
      decouple_generic_gas_switch: if ( mean_decouple .or.                     &
                                        gen_use_perf_jac ) then
        if(dc_part) then

          if (skeleton > 0) write(*,*) "Calling roe_jacobians_dc"

!         decoupled species eqn. jacobians
          call roe_jacobians_dc(grid%nnodes0, grid%nnodes01, grid%nedgeloc,    &
                         grid%nedgeloc_2d, soln%max_nnz, grid%eptr, soln%q_dof,&
                         soln%a_diag, soln%a_off_dc, grid%xn, grid%yn, grid%zn,&
                         grid%ra, crow%fhelp, grid%facespeed, soln%n_tot,      &
                         soln%njac, crow%g2m, soln%pressure_jac)
        else

          if (skeleton > 0) write(*,*) "Calling roe_jacobians_gen"

!         exact jacobians for Roe's fluxes - generic gas path version
          call roe_jacobians_gen(grid%nnodes0, grid%nnodes01, grid%nedgeloc,   &
                         grid%nedgeloc_2d, soln%max_nnz, grid%eptr, soln%q_dof,&
                         soln%a_diag, soln%a_off, grid%xn, grid%yn, grid%zn,   &
                         grid%ra, crow%fhelp, grid%facespeed, soln%n_tot,      &
                         soln%njac, crow%g2m, soln%eqn_set, soln%pressure_jac)
        end if
      else

!     exact jacobians for Roe's fluxes
      call roe_jacobians(grid%nnodes0, grid%nnodes01, grid%nedgeloc,           &
                         grid%nedgeloc_2d, soln%max_nnz, grid%eptr, soln%q_dof,&
                         soln%a_diag, soln%a_off, grid%xn, grid%yn, grid%zn,   &
                         grid%ra, crow%fhelp, grid%facespeed, soln%n_tot,      &
                         soln%njac, crow%g2m)
      end if decouple_generic_gas_switch
    elseif ( flux_construction_lhs == 'central_diss' ) then

!     jacobians for central-difference

      call cd_jacobians(grid%nnodes0, grid%nnodes01, grid%nedgeloc,            &
                        grid%nedgeloc_2d, soln%max_nnz,                        &
                        grid%eptr, soln%q_dof,                                 &
                        soln%a_diag, soln%a_off, grid%xn, grid%yn, grid%zn,    &
                        grid%ra, crow%fhelp, grid%facespeed,                   &
                        soln%n_tot, soln%njac, crow%g2m)

    else if ( flux_construction_lhs == 'hllc' ) then

!     jacobians for hllc fluxes

      call flux_jacobian_hllcs( grid%nnodes0, grid%nnodes01,                   &
                                grid%nedgeloc, grid%nedgeloc_2d,               &
                                grid%eptr, soln%q_dof,                         &
                                grid%vol, grid%x, grid%y, grid%z,              &
                                soln%gradx, soln%gradx, soln%gradx, soln%phi,  &
                                grid%xn, grid%yn, grid%zn, grid%ra,            &
                                grid%facespeed, soln%ndim, soln%eqn_set,       &
                                soln%n_tot, soln%n_grd, soln%njac,             &
                                crow%g2m, crow%fhelp,                          &
                                soln%a_off, soln%a_diag,                       &
                                soln%max_nnz, grid%ncell01)

    else if ( flux_construction_lhs == 'aufs' ) then

!     jacobians for aufs fluxes

      call flux_jacobian_aufs( grid%nnodes0,grid%nnodes01,                     &
                               grid%nedgeloc, grid%nedgeloc_2d,                &
                               soln%max_nnz,                                   &
                               grid%eptr, soln%q_dof,                          &
                               soln%a_diag, soln%a_off,                        &
                               grid%xn, grid%yn, grid%zn,                      &
                               grid%ra, crow%fhelp, grid%ncell01,              &
                               soln%n_tot, soln%njac, crow%g2m)

    else if ( flux_construction_lhs == 'ldfss'          .or. &
              flux_construction_lhs == 'dldfss'         ) then

!     jacobians for LDFSS fluxes

      call flux_jacobian_ldfss( grid%nnodes0, grid%nnodes01,                   &
                                grid%nedgeloc, grid%nedgeloc_2d,               &
                                grid%eptr, soln%q_dof,                         &
                                grid%vol, grid%x, grid%y, grid%z,              &
                                soln%gradx, soln%gradx, soln%gradx, soln%phi,  &
                                grid%xn, grid%yn, grid%zn, grid%ra,            &
                                grid%facespeed, soln%ndim, soln%eqn_set,       &
                                soln%n_tot, soln%n_grd, soln%njac,             &
                                crow%g2m, crow%fhelp,                          &
                                soln%a_off, soln%a_diag,                       &
                                soln%max_nnz, grid%ncell01)

    else if ( flux_construction_lhs == 'ldfss-analytic' ) then

!     jacobians for LDFSS fluxes

      call flux_jacobian_ldfss_analytic( grid%nnodes0, grid%nnodes01,          &
                                grid%nedgeloc, grid%nedgeloc_2d,               &
                                grid%eptr, soln%q_dof,                         &
                                grid%xn, grid%yn, grid%zn, grid%ra,            &
                                soln%ndim, soln%eqn_set,                       &
                                soln%n_tot, soln%njac,                         &
                                crow%g2m, crow%fhelp,                          &
                                soln%a_off, soln%a_diag,                       &
                                soln%max_nnz, grid%ncell01)

    else

!     jacobians for van Leer's fluxes

      call dfdup(grid%nnodes0,     grid%nnodes01,  grid%nedgeloc,              &
                 grid%nedgeloc_2d, soln%max_nnz,                               &
                 grid%eptr,        soln%q_dof,     soln%a_diag,                &
                 soln%a_off,       grid%xn,        grid%yn,                    &
                 grid%zn,          grid%ra,        crow%fhelp,                 &
                 grid%facespeed,   soln%n_tot,     soln%njac,                  &
                 crow%g2m, soln%amut, grid%weight)
    end if

    if ( timing_jac ) call rtime('  end:inviscid_jacobian:interior')

  end subroutine inviscid_jacobian


!=================================== DFDUP ===================================80
!
! van Leer flux jacobians
!
! Note: this subroutine uses conservative variables
!
!=============================================================================80

  subroutine dfdup(nnodes0, nnodes01, nedgeloc, nedgeloc_2d, max_nnz,          &
                   baseline_eptr, qnode, a_diag, a_off, baseline_xn,           &
                   baseline_yn, baseline_zn, baseline_ra, baseline_fhelp,      &
                   baseline_facespeed, n_tot, njac, g2m, amut, baseline_weight)

    use info_depr,               only : twod, ebv_tets, xmach, re, tref
    use fluid,                   only : gamma, gm1, ggm1, xgm, xg2m1, prandtl, &
                                        sutherland_constant
    use turb_parameters,         only : turbulent_prandtl
    use nml_governing_equations, only : viscous_terms
    use openacc,                 only : use_openacc
    use openacc_vars,            only : openacc_eptr, openacc_fhelp,           &
                                        openacc_xn, openacc_yn, openacc_zn,    &
                                        openacc_ra, openacc_weight,            &
                                        openacc_facespeed,                     &
                                        openacc_nedge_colors,                  &
                                        openacc_nedge_in_color

    integer, intent(in) :: n_tot, njac
    integer, intent(in) :: nnodes0
    integer, intent(in) :: nnodes01
    integer, intent(in) :: max_nnz

    integer, intent(in)    :: nedgeloc
    integer, intent(in)    :: nedgeloc_2d

    integer, dimension(:,:), pointer    :: baseline_eptr
    integer, dimension(:,:), pointer    :: baseline_fhelp
    integer, dimension(:),   intent(in) :: g2m

    real(dp),  dimension(nnodes01),          intent(in)    :: amut
    real(dp),  dimension(n_tot,nnodes01),    intent(in)    :: qnode
    real(dp),  dimension(:,:),               pointer       :: baseline_weight
    real(dp),  dimension(:),                 pointer       :: baseline_xn
    real(dp),  dimension(:),                 pointer       :: baseline_yn
    real(dp),  dimension(:),                 pointer       :: baseline_zn
    real(dp),  dimension(:),                 pointer       :: baseline_ra
    real(dp),  dimension(:),                 pointer       :: baseline_facespeed
    real(dp),  dimension(njac,njac,nnodes0), intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz), intent(inout) :: a_off

    integer :: n,node1,node2,idiag,k,j,ioff,nedge_jac_eval,nedge_colors,nstart
    integer :: color,amount

    integer, dimension(:,:), pointer     :: eptr  => null()
    integer, dimension(:,:), pointer     :: fhelp => null()
    integer, dimension(:),   allocatable :: nedge_in_color

    real(dp) :: area,c,c1,c2,dcde,dcdr,dcdru,dcdrv,dcdrw,dfdq1,dfdq2,dfdq3,dfdq4
    real(dp) :: dfdq5,fluxm1,fluxm2,fluxm3,fluxm4,fluxm5,fluxp1,fluxp2,fluxp3
    real(dp) :: fluxp4,fluxp5,fmach,fof,phi,q2,fluxp1inv,fluxm1inv,ubar,ubm2a
    real(dp) :: ubp2a,xnorm,ynorm,znorm,face_speed,rhoc_inv,rho1_inv,cgpt
    real(dp) :: rho2_inv,rho1,rho2,u1,u2,v1,v2,w1,w2,enrgy1,enrgy2,cstar,xmr,cgp
    real(dp) :: mut1,mut2

    real(dp), parameter :: my_0   = 0.0_dp
    real(dp), parameter :: my_4th = 0.25_dp
    real(dp), parameter :: my_haf = 0.5_dp
    real(dp), parameter :: my_1   = 1.0_dp
    real(dp), parameter :: my_2   = 2.0_dp
    real(dp), parameter :: my_4   = 4.0_dp

    real(dp), dimension(10)    :: w
    real(dp), dimension(5,5)   :: dfm,dfp
    real(dp), dimension(5,5,2) :: a_visc

    real(dp), dimension(:),   pointer :: xn        => null()
    real(dp), dimension(:),   pointer :: yn        => null()
    real(dp), dimension(:),   pointer :: zn        => null()
    real(dp), dimension(:),   pointer :: ra        => null()
    real(dp), dimension(:),   pointer :: facespeed => null()
    real(dp), dimension(:,:), pointer :: weight    => null()

    logical :: include_visc_terms

  continue

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

    include_visc_terms = .false.
    if ( ebv_tets ) then
      cstar = sutherland_constant/tref
      xmr   = xmach/re
      cgp   = 1.0_dp/(gm1*prandtl)
      cgpt  = 1.0_dp/(gm1*turbulent_prandtl)
      if ( viscous_terms /= 'inviscid' ) include_visc_terms = .true.
    endif

    if ( use_openacc ) then
      nedge_colors = openacc_nedge_colors
      allocate(nedge_in_color(nedge_colors))
      nedge_in_color(:) = openacc_nedge_in_color(:)
      eptr      => openacc_eptr
      fhelp     => openacc_fhelp
      xn        => openacc_xn
      yn        => openacc_yn
      zn        => openacc_zn
      ra        => openacc_ra
      weight    => openacc_weight
      facespeed => openacc_facespeed
    else
      nedge_colors = 1
      allocate(nedge_in_color(nedge_colors))
      nedge_in_color(1) = nedge_jac_eval
      eptr      => baseline_eptr
      fhelp     => baseline_fhelp
      xn        => baseline_xn
      yn        => baseline_yn
      zn        => baseline_zn
      ra        => baseline_ra
      weight    => baseline_weight
      facespeed => baseline_facespeed
    endif

!   loop over the edges and calculate the jacobians
!   (in the 2D case, this loop contains edges on only one y=constant plane)

    nstart = 1
    edge_colors : do color = 1, nedge_colors
      amount = nedge_in_color(color)
!$acc parallel loop gang worker num_workers(128)                               &
!$acc          present(eptr,xn,yn,zn,ra,weight,facespeed,qnode,amut,g2m,fhelp, &
!$acc                  a_diag,a_off)                                           &
!$acc          private(dfp,dfm,a_visc,w)
      scanedges: do n = nstart, nstart + amount - 1

        node1 = eptr(1,n)
        node2 = eptr(2,n)

!       get unit normals to and area

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = ra(n)

!       face speed

        face_speed = my_0

        if (need_grid_velocity) then
          face_speed = facespeed(n)
        end if

!       take care of left side

        rho1  = qnode(1,node1)
        rho1_inv = my_1/rho1
        u1    = qnode(2,node1) * rho1_inv
        v1    = qnode(3,node1) * rho1_inv
        w1    = qnode(4,node1) * rho1_inv
        q2    = u1*u1 + v1*v1 + w1*w1
        enrgy1= qnode(5,node1)
        c     = sqrt(gamma*gm1*(enrgy1-my_haf*rho1*q2)*rho1_inv)
        rhoc_inv = my_1/(rho1*c)
        ubar  = xnorm*u1 + ynorm*v1 + znorm*w1  - face_speed
        fmach = ubar / c
        ubp2a = -ubar + my_2*c

!       if subsonic calculate df+ and add contribution to A

        first_machvalue_check: if (abs(fmach) < my_1) then

!         first get fluxes

          fluxp1 = area * my_4th * rho1 * c * (fmach+1)**2
          fluxp2 = fluxp1 * (xnorm*ubp2a*xgm+u1)
          fluxp3 = fluxp1 * (ynorm*ubp2a*xgm+v1)
          fluxp4 = fluxp1 * (znorm*ubp2a*xgm+w1)
          fluxp5 = fluxp1*((-gm1*ubar*ubar                                     &
                       + my_2*gm1*ubar*c + my_2*c*c)*xg2m1                     &
                       + my_haf*q2 + face_speed*(-ubar + my_2*c)*xgm)

!         derivatives of speed of sound

          dcdr  =  my_haf*ggm1 * (q2-enrgy1*rho1_inv) * rhoc_inv
          dcdru = -my_haf*ggm1*u1*rhoc_inv
          dcdrv = -my_haf*ggm1*v1*rhoc_inv
          dcdrw = -my_haf*ggm1*w1*rhoc_inv
          dcde  =  my_haf*ggm1 * rhoc_inv

!         if subsonic calculate df+ and add contribution to Jacobian

          dfdq1 = my_4th*area*(my_1 - fmach**2)*(c + rho1*dcdr)                &
                - my_haf*area*face_speed*(fmach + my_1)
          dfdq2 = my_4th*area*(my_2*xnorm*(fmach + my_1)                       &
                + rho1*(my_1 - fmach**2)*dcdru)
          dfdq3 = my_4th*area*(my_2*ynorm*(fmach + my_1)                       &
                + rho1*(my_1 - fmach**2)*dcdrv)
          dfdq4 = my_4th*area*(my_2*znorm*(fmach + my_1)                       &
                + rho1*(my_1 - fmach**2)*dcdrw)
          dfdq5 = my_4th*area*rho1*dcde*(my_1 - fmach**2)

          dfp(1,1) = dfdq1
          dfp(1,2) = dfdq2
          dfp(1,3) = dfdq3
          dfp(1,4) = dfdq4
          dfp(1,5) = dfdq5

          fluxp1inv = my_1/fluxp1

          fof = fluxp2*fluxp1inv
          dfp(2,1) = fluxp1*(my_2*xnorm*xgm*dcdr                               &
                   + xnorm*(ubar+face_speed)*xgm*rho1_inv - u1*rho1_inv)       &
                   + fof*dfdq1
          dfp(2,2) = fluxp1*(-xnorm*xnorm*xgm*rho1_inv                         &
                   + my_2*xnorm*dcdru*xgm + rho1_inv) + fof*dfdq2

          dfp(2,3) = fluxp1*(-xnorm*ynorm*xgm*rho1_inv                         &
                   + my_2*xnorm*dcdrv*xgm) + fof*dfdq3
          dfp(2,4) = fluxp1*(-xnorm*znorm*xgm*rho1_inv                         &
                   + my_2*xnorm*dcdrw*xgm) + fof*dfdq4
          dfp(2,5) = fluxp1*(my_2*xnorm*dcde*xgm)                              &
                    + fof*dfdq5

          fof = fluxp3*fluxp1inv
          dfp(3,1) = fluxp1*(my_2*ynorm*xgm*dcdr                               &
                   + ynorm*(ubar+face_speed)*xgm*rho1_inv - v1*rho1_inv)       &
                   + fof*dfdq1
          dfp(3,2) = fluxp1*(-ynorm*xnorm*xgm*rho1_inv                         &
                   + my_2*ynorm*dcdru*xgm) + fof*dfdq2
          dfp(3,3) = fluxp1*(-ynorm*ynorm*xgm*rho1_inv                         &
                   + my_2*ynorm*dcdrv*xgm + rho1_inv) + fof*dfdq3
          dfp(3,4) = fluxp1*(-ynorm*znorm*xgm*rho1_inv                         &
                   + my_2*ynorm*dcdrw*xgm) + fof*dfdq4
          dfp(3,5) = fluxp1*(my_2*ynorm*dcde*xgm)                              &
                   + fof*dfdq5

          fof = fluxp4*fluxp1inv
          dfp(4,1) = fluxp1*(my_2*znorm*xgm*dcdr                               &
                   + znorm*(ubar+face_speed)*xgm*rho1_inv - w1*rho1_inv)       &
                   + fof*dfdq1
          dfp(4,2) = fluxp1*(-znorm*xnorm*xgm*rho1_inv                         &
                   + my_2*znorm*dcdru*xgm) + fof*dfdq2
          dfp(4,3) = fluxp1*(-znorm*ynorm*xgm*rho1_inv                         &
                   + my_2*znorm*dcdrv*xgm) + fof*dfdq3
          dfp(4,4) = fluxp1*(-znorm*znorm*xgm*rho1_inv                         &
                   + my_2*znorm*dcdrw*xgm + rho1_inv) + fof*dfdq4
          dfp(4,5) = fluxp1*(my_2*znorm*dcde*xgm)                              &
                     + fof*dfdq5

          c1  = (my_2*gm1*ubar+my_4*c)*xg2m1 + my_2*xgm*face_speed
          c2  = my_2 * gm1*xg2m1 * (-ubar+c) * rho1_inv
          fof = fluxp5*fluxp1inv

          dfp(5,1) = fluxp1*(dcdr*c1 - c2*(ubar+face_speed) - q2*rho1_inv      &
                   + face_speed*(ubar+face_speed)*xgm*rho1_inv)                &
                   + fof*dfdq1
          dfp(5,2) = fluxp1*(dcdru*c1 + c2*xnorm + u1*rho1_inv                 &
                   - face_speed*xnorm*xgm*rho1_inv)                            &
                   + fof*dfdq2
          dfp(5,3) = fluxp1*(dcdrv*c1 + c2*ynorm + v1*rho1_inv                 &
                   - face_speed*ynorm*xgm*rho1_inv)                            &
                   + fof*dfdq3
          dfp(5,4) = fluxp1*(dcdrw*c1 + c2*znorm + w1*rho1_inv                 &
                   - face_speed*znorm*xgm*rho1_inv)                            &
                   + fof*dfdq4
          dfp(5,5) = fluxp1*dcde*c1 + fof*dfdq5

        else if (fmach >= my_1) then

          phi = my_haf * gm1 * q2
          dfp(1,1) = -area*face_speed
          dfp(1,2) = area*xnorm
          dfp(1,3) = area*ynorm
          dfp(1,4) = area*znorm
          dfp(1,5) = my_0

          dfp(2,1) = area*(xnorm*phi-u1*(ubar+face_speed))
          dfp(2,2) = area*(xnorm*(my_2-gamma)*u1+ubar)
          dfp(2,3) = area*(ynorm*u1-xnorm*gm1*v1)
          dfp(2,4) = area*(znorm*u1-xnorm*gm1*w1)
          dfp(2,5) = area*xnorm*gm1

          dfp(3,1) = area*(ynorm*phi-v1*(ubar+face_speed))
          dfp(3,2) = area*(xnorm*v1-ynorm*gm1*u1)
          dfp(3,3) = area*(ynorm*(my_2-gamma)*v1+ubar)
          dfp(3,4) = area*(znorm*v1-ynorm*gm1*w1)
          dfp(3,5) = area*ynorm*gm1

          dfp(4,1) = area*(znorm*phi-w1*(ubar+face_speed))
          dfp(4,2) = area*(xnorm*w1-znorm*gm1*u1)
          dfp(4,3) = area*(ynorm*w1-znorm*gm1*v1)
          dfp(4,4) = area*(znorm*(my_2-gamma)*w1+ubar)
          dfp(4,5) = area*znorm*gm1

          dfp(5,1) = area*(my_2*phi-gamma*enrgy1*rho1_inv)*(ubar+face_speed)
          dfp(5,2) = area*(xnorm*(gamma*enrgy1*rho1_inv-phi)-gm1*u1*           &
                     (ubar+face_speed))
          dfp(5,3) = area*(ynorm*(gamma*enrgy1*rho1_inv-phi)-gm1*v1*           &
                     (ubar+face_speed))
          dfp(5,4) = area*(znorm*(gamma*enrgy1*rho1_inv-phi)-gm1*w1*           &
                     (ubar+face_speed))
          dfp(5,5) = area*(gamma*ubar + gm1*face_speed)

        else

          dfp(1,1) = my_0
          dfp(1,2) = my_0
          dfp(1,3) = my_0
          dfp(1,4) = my_0
          dfp(1,5) = my_0

          dfp(2,1) = my_0
          dfp(2,2) = my_0
          dfp(2,3) = my_0
          dfp(2,4) = my_0
          dfp(2,5) = my_0

          dfp(3,1) = my_0
          dfp(3,2) = my_0
          dfp(3,3) = my_0
          dfp(3,4) = my_0
          dfp(3,5) = my_0

          dfp(4,1) = my_0
          dfp(4,2) = my_0
          dfp(4,3) = my_0
          dfp(4,4) = my_0
          dfp(4,5) = my_0

          dfp(5,1) = my_0
          dfp(5,2) = my_0
          dfp(5,3) = my_0
          dfp(5,4) = my_0
          dfp(5,5) = my_0

        end if first_machvalue_check

!       now do dfm
        rho2  = qnode(1,node2)
        rho2_inv = my_1/rho2
        u2    = qnode(2,node2)*rho2_inv
        v2    = qnode(3,node2)*rho2_inv
        w2    = qnode(4,node2)*rho2_inv
        q2    = u2*u2 + v2*v2 + w2*w2
        enrgy2= qnode(5,node2)
        c     = sqrt(gamma*gm1*(enrgy2 - my_haf*rho2*q2)*rho2_inv)
        rhoc_inv = my_1/(rho2*c)
        ubar  = xnorm*u2 + ynorm*v2 + znorm*w2 - face_speed
        fmach = ubar/c
        ubm2a = -ubar - my_2*c


!       if subsonic calculate df+ and add contribution to A

        second_machvalue_check: if (abs(fmach) < my_1) then

!         get fluxes

          fluxm1 = -area*my_4th*rho2*c*(fmach-1)**2
          fluxm2 = fluxm1 * (xnorm*ubm2a*xgm+u2)
          fluxm3 = fluxm1 * (ynorm*ubm2a*xgm+v2)
          fluxm4 = fluxm1 * (znorm*ubm2a*xgm+w2)
          fluxm5 = fluxm1*((-gm1*ubar*ubar                                     &
                       - my_2*gm1*ubar*c + my_2*c*c)*xg2m1                     &
                       + my_haf*q2 + face_speed*(-ubar - my_2*c)*xgm)

!         derivatives of speed of sound

          dcdr  =  my_haf*ggm1*(q2 - enrgy2*rho2_inv)*rhoc_inv
          dcdru = -my_haf*ggm1*u2*rhoc_inv
          dcdrv = -my_haf*ggm1*v2*rhoc_inv
          dcdrw = -my_haf*ggm1*w2*rhoc_inv
          dcde  =  my_haf*ggm1  *rhoc_inv

!         if subsonic calculate df- and subtract contribution to A

          dfdq1 = -my_4th*area*(my_1 - fmach**2)*(c + rho2*dcdr)               &
                +  my_haf*area*face_speed*(fmach - my_1)
          dfdq2 = -my_4th*area*(my_2*xnorm*(fmach - my_1)                      &
                + rho2*(my_1 - fmach**2)*dcdru)
          dfdq3 = -my_4th*area*(my_2*ynorm*(fmach - my_1)                      &
                + rho2*(my_1 - fmach**2)*dcdrv)
          dfdq4 = -my_4th*area*(my_2*znorm*(fmach - my_1)                      &
                + rho2*(my_1 - fmach**2)*dcdrw)
          dfdq5 = -my_4th*area*rho2*dcde*(my_1 - fmach**2)

          dfm(1,1) = dfdq1
          dfm(1,2) = dfdq2
          dfm(1,3) = dfdq3
          dfm(1,4) = dfdq4
          dfm(1,5) = dfdq5

          fluxm1inv = my_1/fluxm1
          fof = fluxm2*fluxm1inv
          dfm(2,1) = fluxm1*(-my_2*xnorm*xgm*dcdr                              &
                   + xnorm*(ubar+face_speed)*xgm*rho2_inv - u2*rho2_inv)       &
                   + fof*dfdq1
          dfm(2,2) = fluxm1*(-xnorm*xnorm*xgm*rho2_inv                         &
                   - my_2*xnorm*dcdru*xgm + rho2_inv) + fof*dfdq2
          dfm(2,3) = fluxm1*(-xnorm*ynorm*xgm*rho2_inv                         &
                   - my_2*xnorm*dcdrv*xgm) + fof*dfdq3
          dfm(2,4) = fluxm1*(-xnorm*znorm*xgm*rho2_inv                         &
                   - my_2*xnorm*dcdrw*xgm) + fof*dfdq4
          dfm(2,5) = fluxm1*(-my_2*xnorm*dcde*xgm)                             &
                   + fof*dfdq5

          fof = fluxm3*fluxm1inv
          dfm(3,1) = fluxm1*(-my_2*ynorm*xgm*dcdr                              &
                   + ynorm*(ubar+face_speed)*xgm*rho2_inv - v2*rho2_inv)       &
                   + fof*dfdq1
          dfm(3,2) = fluxm1*(-ynorm*xnorm*xgm*rho2_inv                         &
                   - my_2*ynorm*dcdru*xgm) + fof*dfdq2
          dfm(3,3) = fluxm1*(-ynorm*ynorm*xgm*rho2_inv                         &
                   - my_2*ynorm*dcdrv*xgm + rho2_inv) + fof*dfdq3
          dfm(3,4) = fluxm1*(-ynorm*znorm*xgm*rho2_inv                         &
                   - my_2*ynorm*dcdrw*xgm) + fof*dfdq4
          dfm(3,5) = fluxm1*(-my_2*ynorm*dcde*xgm)                             &
                   + fof*dfdq5

          fof = fluxm4*fluxm1inv
          dfm(4,1) = fluxm1*(-my_2*znorm*xgm*dcdr                              &
                   + znorm*(ubar+face_speed)*xgm*rho2_inv - w2*rho2_inv)       &
                   + fof*dfdq1
          dfm(4,2) = fluxm1*(-znorm*xnorm*xgm*rho2_inv                         &
                   - my_2*znorm*dcdru*xgm) + fof*dfdq2
          dfm(4,3) = fluxm1*(-znorm*ynorm*xgm*rho2_inv                         &
                   - my_2*znorm*dcdrv*xgm) + fof*dfdq3
          dfm(4,4) = fluxm1*(-znorm*znorm*xgm*rho2_inv                         &
                   - my_2*znorm*dcdrw*xgm + rho2_inv) + fof*dfdq4
          dfm(4,5) = fluxm1*(-my_2*znorm*dcde*xgm)                             &
                   + fof*dfdq5

          c1  = (-my_2*gm1*ubar + my_4*c)*xg2m1 - my_2*xgm*face_speed
          c2  = my_2*gm1*xg2m1*(-ubar - c)*rho2_inv
          fof = fluxm5*fluxm1inv

          dfm(5,1) = fluxm1*(dcdr*c1 - c2*(ubar+face_speed) - q2*rho2_inv      &
                   + face_speed*(ubar+face_speed)*xgm*rho2_inv)                &
                   + fof*dfdq1
          dfm(5,2) = fluxm1*(dcdru*c1 + c2*xnorm + u2*rho2_inv                 &
                   - face_speed*xnorm*xgm*rho2_inv)                            &
                   + fof*dfdq2
          dfm(5,3) = fluxm1*(dcdrv*c1 + c2*ynorm + v2*rho2_inv                 &
                   - face_speed*ynorm*xgm*rho2_inv)                            &
                   + fof*dfdq3
          dfm(5,4) = fluxm1*(dcdrw*c1 + c2*znorm + w2*rho2_inv                 &
                   - face_speed*znorm*xgm*rho2_inv)                            &
                   + fof*dfdq4
          dfm(5,5) = fluxm1*dcde*c1 + fof*dfdq5

        else if (fmach <= -my_1) then

          phi = my_haf * gm1 * q2
          dfm(1,1) = -area*face_speed
          dfm(1,2) = area*xnorm
          dfm(1,3) = area*ynorm
          dfm(1,4) = area*znorm
          dfm(1,5) = my_0

          dfm(2,1) = area*(xnorm*phi-u2*(ubar+face_speed))
          dfm(2,2) = area*(xnorm*(my_2-gamma)*u2+ubar)
          dfm(2,3) = area*(ynorm*u2-xnorm*gm1*v2)
          dfm(2,4) = area*(znorm*u2-xnorm*gm1*w2)
          dfm(2,5) = area*xnorm*gm1

          dfm(3,1) = area*(ynorm*phi-v2*(ubar+face_speed))
          dfm(3,2) = area*(xnorm*v2-ynorm*gm1*u2)
          dfm(3,3) = area*(ynorm*(my_2-gamma)*v2+ubar)
          dfm(3,4) = area*(znorm*v2-ynorm*gm1*w2)
          dfm(3,5) = area*ynorm*gm1

          dfm(4,1) = area*(znorm*phi-w2*(ubar+face_speed))
          dfm(4,2) = area*(xnorm*w2-znorm*gm1*u2)
          dfm(4,3) = area*(ynorm*w2-znorm*gm1*v2)
          dfm(4,4) = area*(znorm*(my_2-gamma)*w2+ubar)
          dfm(4,5) = area*znorm*gm1

          dfm(5,1) = area*(my_2*phi-gamma*enrgy2*rho2_inv)*(ubar+face_speed)
          dfm(5,2) = area*(xnorm*(gamma*enrgy2*rho2_inv-phi)-gm1*u2*           &
                     (ubar+face_speed))
          dfm(5,3) = area*(ynorm*(gamma*enrgy2*rho2_inv-phi)-gm1*v2*           &
                     (ubar+face_speed))
          dfm(5,4) = area*(znorm*(gamma*enrgy2*rho2_inv-phi)-gm1*w2*           &
                     (ubar+face_speed))
          dfm(5,5) = area*(gamma*ubar + gm1*face_speed)

        else

          dfm(1,1) = my_0
          dfm(1,2) = my_0
          dfm(1,3) = my_0
          dfm(1,4) = my_0
          dfm(1,5) = my_0

          dfm(2,1) = my_0
          dfm(2,2) = my_0
          dfm(2,3) = my_0
          dfm(2,4) = my_0
          dfm(2,5) = my_0

          dfm(3,1) = my_0
          dfm(3,2) = my_0
          dfm(3,3) = my_0
          dfm(3,4) = my_0
          dfm(3,5) = my_0

          dfm(4,1) = my_0
          dfm(4,2) = my_0
          dfm(4,3) = my_0
          dfm(4,4) = my_0
          dfm(4,5) = my_0

          dfm(5,1) = my_0
          dfm(5,2) = my_0
          dfm(5,3) = my_0
          dfm(5,4) = my_0
          dfm(5,5) = my_0

        end if second_machvalue_check

        if ( include_visc_terms ) then
          mut1 = amut(node1)
          mut2 = amut(node2)
          w(:) = weight(:,n)
          a_visc = ebv_tet_jac(rho1,u1,v1,w1,enrgy1,rho2,u2,v2,w2,enrgy2,cstar,&
                               cgp,cgpt,mut1,mut2,w,xmr,rho1_inv,rho2_inv)
          dfp(:,:) = dfp(:,:) + a_visc(:,:,1)
          dfm(:,:) = dfm(:,:) + a_visc(:,:,2)
        endif

        if(node1 <= nnodes0) then
          idiag = g2m(node1)
          do k = 1, 5
            do j = 1, 5
              a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfp(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          ioff  = fhelp(2,n)
          do k = 1, 5
            do j = 1, 5
              a_off(j,k,ioff)  = a_off(j,k,ioff) - real(dfp(j,k),odp)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          idiag = g2m(node2)
          do k = 1, 5
            do j = 1, 5
              a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfm(j,k)
            end do
          end do
        end if

        if(node1 <= nnodes0) then
          ioff  = fhelp(1,n)
          do k = 1, 5
            do j = 1, 5
              a_off(j,k,ioff)  = a_off(j,k,ioff) + real(dfm(j,k),odp)
            end do
          end do
        end if

      end do scanedges
!$acc end parallel
      nstart = nstart + amount
    end do edge_colors

    deallocate(nedge_in_color)

    fhelp     => null()
    eptr      => null()
    xn        => null()
    yn        => null()
    zn        => null()
    ra        => null()
    facespeed => null()

  end subroutine dfdup


!================================ ROE_JACOBIANS ==============================80
!
! Roe flux jacobian
!
! All derivatives worked out in detail
!
! Watch out for the notation here.  Originally this routine linearized wrt
! primitive variables and then transformed to conserved.  The seed
! derivatives at the top were later changed to be wrt conserved to avoid the
! transformation (and the need for ptoe/etop).  However, the notation for the
! derivatives still reads like primitive.
!
!=============================================================================80

  subroutine roe_jacobians(nnodes0, nnodes01, nedgeloc, nedgeloc_2d, max_nnz,  &
                           eptr, qnode, a_diag, a_off, xn, yn, zn,             &
                           ra, fhelp, facespeed, n_tot, njac, g2m)

    use info_depr,     only : twod, adptv_entropy_fix
    use inviscid_flux, only : rhs_a_eigenvalue_coef, rhs_u_eigenvalue_coef,    &
                              lhs_a_eigenvalue_coef, lhs_u_eigenvalue_coef
    use fluid,         only : gm1

    integer,                                    intent(in) :: nnodes0,nnodes01
    integer,                                    intent(in) :: nedgeloc
    integer,                                    intent(in) :: nedgeloc_2d
    integer,                                    intent(in) :: max_nnz
    integer,                                    intent(in) :: n_tot,njac

    integer,      dimension(2,nedgeloc),        intent(in) :: eptr
    integer,      dimension(2,nedgeloc),        intent(in) :: fhelp
    integer,      dimension(:),                 intent(in) :: g2m

    real(dp),  dimension(n_tot,nnodes01),       intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),             intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),             intent(in)    :: facespeed
    real(dp),  dimension(njac,njac,nnodes0),    intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz),    intent(inout) :: a_off

    integer :: n,node1,node2,nedge_jac_eval,idiag,k,ioff

    real(dp)    :: xnorm,ynorm,znorm,area,face_speed

    real(dp)    :: flux1rl,flux1ul,flux1vl,flux1wl,flux1pl
    real(dp)    :: flux2rl,flux2ul,flux2vl,flux2wl,flux2pl
    real(dp)    :: flux3rl,flux3ul,flux3vl,flux3wl,flux3pl
    real(dp)    :: flux4rl,flux4ul,flux4vl,flux4wl,flux4pl
    real(dp)    :: flux5rl,flux5ul,flux5vl,flux5wl,flux5pl

    real(dp)    :: fluxp1rl,fluxp1ul,fluxp1vl,fluxp1wl
    real(dp)    :: fluxp2rl,fluxp2ul,fluxp2vl,fluxp2wl,fluxp2pl
    real(dp)    :: fluxp3rl,fluxp3ul,fluxp3vl,fluxp3wl,fluxp3pl
    real(dp)    :: fluxp4rl,fluxp4ul,fluxp4vl,fluxp4wl,fluxp4pl
    real(dp)    :: fluxp5rl,fluxp5ul,fluxp5vl,fluxp5wl,fluxp5pl

    real(dp)    :: flux1rr,flux1ur,flux1vr,flux1wr,flux1pr
    real(dp)    :: flux2rr,flux2ur,flux2vr,flux2wr,flux2pr
    real(dp)    :: flux3rr,flux3ur,flux3vr,flux3wr,flux3pr
    real(dp)    :: flux4rr,flux4ur,flux4vr,flux4wr,flux4pr
    real(dp)    :: flux5rr,flux5ur,flux5vr,flux5wr,flux5pr

    real(dp)    :: fluxm1rr,fluxm1ur,fluxm1vr,fluxm1wr
    real(dp)    :: fluxm2rr,fluxm2ur,fluxm2vr,fluxm2wr,fluxm2pr
    real(dp)    :: fluxm3rr,fluxm3ur,fluxm3vr,fluxm3wr,fluxm3pr
    real(dp)    :: fluxm4rr,fluxm4ur,fluxm4vr,fluxm4wr,fluxm4pr
    real(dp)    :: fluxm5rr,fluxm5ur,fluxm5vr,fluxm5wr,fluxm5pr

    real(dp)    :: t1rl,t1ul,t1vl,t1wl,t1pl
    real(dp)    :: t2rl,t2ul,t2vl,t2wl,t2pl
    real(dp)    :: t3rl,t3ul,t3vl,t3wl,t3pl
    real(dp)    :: t4rl,t4ul,t4vl,t4wl,t4pl
    real(dp)    :: t5rl,t5ul,t5vl,t5wl,t5pl

    real(dp)    :: t1rr,t1ur,t1vr,t1wr,t1pr
    real(dp)    :: t2rr,t2ur,t2vr,t2wr,t2pr
    real(dp)    :: t3rr,t3ur,t3vr,t3wr,t3pr
    real(dp)    :: t4rr,t4ur,t4vr,t4wr,t4pr
    real(dp)    :: t5rr,t5ur,t5vr,t5wr,t5pr

    real(dp)    :: r54
    real(dp)    :: r54rl,r54ul,r54vl,r54wl
    real(dp)    :: r54rr,r54ur,r54vr,r54wr

    real(dp)    :: r44
    real(dp)    :: r44rl,r44wl
    real(dp)    :: r44rr,r44wr

    real(dp)    :: r34
    real(dp)    :: r34rl,r34vl
    real(dp)    :: r34rr,r34vr

    real(dp)    :: r24
    real(dp)    :: r24rl,r24ul
    real(dp)    :: r24rr,r24ur

    real(dp)    :: r53
    real(dp)    :: r53rl,r53ul,r53vl,r53wl
    real(dp)    :: r53rr,r53ur,r53vr,r53wr

    real(dp)    :: r43
    real(dp)    :: r43rl,r43ul,r43vl,r43wl
    real(dp)    :: r43rr,r43ur,r43vr,r43wr

    real(dp)    :: r33
    real(dp)    :: r33rl,r33ul,r33vl,r33wl
    real(dp)    :: r33rr,r33ur,r33vr,r33wr

    real(dp)    :: r23
    real(dp)    :: r23rl,r23ul,r23vl,r23wl
    real(dp)    :: r23rr,r23ur,r23vr,r23wr

    real(dp)    :: r52
    real(dp)    :: r52rl,r52ul,r52vl,r52wl,r52pl
    real(dp)    :: r52rr,r52ur,r52vr,r52wr,r52pr

    real(dp)    :: r42
    real(dp)    :: r42rl,r42ul,r42vl,r42wl,r42pl
    real(dp)    :: r42rr,r42ur,r42vr,r42wr,r42pr

    real(dp)    :: r32
    real(dp)    :: r32rl,r32ul,r32vl,r32wl,r32pl
    real(dp)    :: r32rr,r32ur,r32vr,r32wr,r32pr

    real(dp)    :: r22
    real(dp)    :: r22rl,r22ul,r22vl,r22wl,r22pl
    real(dp)    :: r22rr,r22ur,r22vr,r22wr,r22pr

    real(dp)    :: r51
    real(dp)    :: r51rl,r51ul,r51vl,r51wl,r51pl
    real(dp)    :: r51rr,r51ur,r51vr,r51wr,r51pr

    real(dp)    :: r41
    real(dp)    :: r41rl,r41ul,r41vl,r41wl,r41pl
    real(dp)    :: r41rr,r41ur,r41vr,r41wr,r41pr

    real(dp)    :: r31
    real(dp)    :: r31rl,r31ul,r31vl,r31wl,r31pl
    real(dp)    :: r31rr,r31ur,r31vr,r31wr,r31pr

    real(dp)    :: r21
    real(dp)    :: r21rl,r21ul,r21vl,r21wl,r21pl
    real(dp)    :: r21rr,r21ur,r21vr,r21wr,r21pr

    real(dp)    :: dv1
    real(dp)    :: dv1rl,dv1ul,dv1vl,dv1wl,dv1pl
    real(dp)    :: dv1rr,dv1ur,dv1vr,dv1wr,dv1pr

    real(dp)    :: dv2
    real(dp)    :: dv2rl,dv2ul,dv2vl,dv2wl,dv2pl
    real(dp)    :: dv2rr,dv2ur,dv2vr,dv2wr,dv2pr

    real(dp)    :: dv3
    real(dp)    :: dv3rl
    real(dp)    :: dv3rr

    real(dp)    :: dv4
    real(dp)    :: dv4rl,dv4ul,dv4vl,dv4wl,dv4pl
    real(dp)    :: dv4rr,dv4ur,dv4vr,dv4wr,dv4pr

    real(dp)    :: c2
    real(dp)    :: c2rl,c2ul,c2vl,c2wl,c2pl
    real(dp)    :: c2rr,c2ur,c2vr,c2wr,c2pr

    real(dp)    :: dubar
    real(dp)    :: dubarrl,dubarul,dubarvl,dubarwl
    real(dp)    :: dubarrr,dubarur,dubarvr,dubarwr

    real(dp)    :: du
    real(dp)    :: durl,duul
    real(dp)    :: durr,duur

    real(dp)    :: dv
    real(dp)    :: dvrl,dvvl
    real(dp)    :: dvrr,dvvr

    real(dp)    :: dw
    real(dp)    :: dwrl,dwwl
    real(dp)    :: dwrr,dwwr

    real(dp)    :: dpress
    real(dp)    :: dpressrl,dpressul,dpressvl,dpresswl,dpresspl
    real(dp)    :: dpressrr,dpressur,dpressvr,dpresswr,dpresspr

    real(dp)    :: drho
    real(dp)    :: drhorl
    real(dp)    :: drhorr

    real(dp)    :: eig1, abseig1
    real(dp)    :: eig1rl,eig1ul,eig1vl,eig1wl,eig1pl
    real(dp)    :: eig1rr,eig1ur,eig1vr,eig1wr,eig1pr

    real(dp)    :: eig2, abseig2
    real(dp)    :: eig2rl,eig2ul,eig2vl,eig2wl,eig2pl
    real(dp)    :: eig2rr,eig2ur,eig2vr,eig2wr,eig2pr

    real(dp)    :: eig3, abseig3
    real(dp)    :: eig3rl,eig3ul,eig3vl,eig3wl,eig3pl
    real(dp)    :: eig3rr,eig3ur,eig3vr,eig3wr,eig3pr

    real(dp)    :: maxeig
    real(dp)    :: maxeigrl,maxeigul,maxeigvl,maxeigwl,maxeigpl
    real(dp)    :: maxeigrr,maxeigur,maxeigvr,maxeigwr,maxeigpr

    real(dp)    :: ubar
    real(dp)    :: ubarrl,ubarul,ubarvl,ubarwl
    real(dp)    :: ubarrr,ubarur,ubarvr,ubarwr

    real(dp)    :: c
    real(dp)    :: crl,cul,cvl,cwl,cpl
    real(dp)    :: crr,cur,cvr,cwr,cpr

    real(dp)    :: q2
    real(dp)    :: q2rl,q2ul,q2vl,q2wl
    real(dp)    :: q2rr,q2ur,q2vr,q2wr

    real(dp)    :: h
    real(dp)    :: hrl,hul,hvl,hwl,hpl
    real(dp)    :: hrr,hur,hvr,hwr,hpr

    real(dp)    :: u
    real(dp)    :: url,uul
    real(dp)    :: urr,uur

    real(dp)    :: v
    real(dp)    :: vrl,vvl
    real(dp)    :: vrr,vvr

    real(dp)    :: w
    real(dp)    :: wrl,wwl
    real(dp)    :: wrr,wwr

    real(dp)    :: wat
    real(dp)    :: watrl
    real(dp)    :: watrr

    real(dp)    :: rho
    real(dp)    :: rhorl
    real(dp)    :: rhorr

    real(dp)    :: ubarr
    real(dp)    :: ubarrrr,ubarrur,ubarrvr,ubarrwr

    real(dp)    :: hr
    real(dp)    :: hrrr,hrur,hrvr,hrwr,hrpr

    real(dp)    :: enrgyr
    real(dp)    :: enrgyrpr

    real(dp)    :: pressr
    real(dp)    :: pressrrr,pressrur,pressrvr,pressrwr,pressrpr

    real(dp)    :: q2r
    real(dp)    :: q2rrr,q2rur,q2rvr,q2rwr

    real(dp)    :: ur
    real(dp)    :: urrr,urur

    real(dp)    :: vr
    real(dp)    :: vrrr,vrvr

    real(dp)    :: wr
    real(dp)    :: wrrr,wrwr

    real(dp)    :: rhor
    real(dp)    :: rhorrr

    real(dp)    :: ubarl
    real(dp)    :: ubarlrl,ubarlul,ubarlvl,ubarlwl

    real(dp)    :: hl
    real(dp)    :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp)    :: enrgyl
    real(dp)    :: enrgylpl

    real(dp)    :: pressl
    real(dp)    :: presslrl,presslul,presslvl,presslwl,presslpl

    real(dp)    :: q2l
    real(dp)    :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp)    :: ul
    real(dp)    :: ulrl,ulul

    real(dp)    :: vl
    real(dp)    :: vlrl,vlvl

    real(dp)    :: wl
    real(dp)    :: wlrl,wlwl

    real(dp)    :: rhol
    real(dp)    :: rholrl

    real(dp)    :: eigeps1, eigeps2, eigeps3
    real(dp)    :: d1, d2, d3

    real(dp), dimension(5,5)    :: dfm,dfp

    real(dp)    :: fa, faeps, absfa, dm, dterm
    real(dp)    :: utngl, utngr, utang, scoef, aecoef, cecoef
    real(dp)    :: rhol_inv, rhor_inv, rho_inv
    real(dp)    :: c_inv, c2_inv, c22_inv

    real(dp), parameter    :: my_0    = 0.0_dp
    real(dp), parameter    :: my_4th  = 0.25_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_1    = 1.0_dp

  continue

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

! Loop over the edges and calculate the Jacobians

    scanedges: do n = 1, nedge_jac_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Get unit normals and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

! Face speed

      face_speed = 0.0_dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

! Get variables on "left" side of face

      rhol = qnode(1,node1)
        rhol_inv = my_1 / rhol
        rholrl = 1.0_dp

      ul = qnode(2,node1) * rhol_inv
        ulrl = -ul*rhol_inv
        ulul = 1.0_dp * rhol_inv

      vl = qnode(3,node1) * rhol_inv
        vlrl = -vl*rhol_inv
        vlvl = 1.0_dp * rhol_inv

      wl = qnode(4,node1) * rhol_inv
        wlrl = -wl*rhol_inv
        wlwl = 1.0_dp * rhol_inv

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = qnode(5,node1)
        enrgylpl = 1.0_dp

      pressl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
        presslrl = -0.5_dp*gm1*(rhol*q2lrl + q2l*rholrl)
        presslul = -0.5_dp*gm1*rhol*q2lul
        presslvl = -0.5_dp*gm1*rhol*q2lvl
        presslwl = -0.5_dp*gm1*rhol*q2lwl
        presslpl = gm1

      Hl = (enrgyl + pressl)*rhol_inv
        Hlrl = (rhol*(presslrl) - (enrgyl+pressl)*rholrl) * rhol_inv * rhol_inv
        Hlul = (rhol*(presslul)) * rhol_inv * rhol_inv
        Hlvl = (rhol*(presslvl)) * rhol_inv * rhol_inv
        Hlwl = (rhol*(presslwl)) * rhol_inv * rhol_inv
        Hlpl = (rhol*(enrgylpl+presslpl)) * rhol_inv * rhol_inv

      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed
        ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
        ubarlul = xnorm*ulul
        ubarlvl = ynorm*vlvl
        ubarlwl = znorm*wlwl

! Get variables on "right" side of face

      rhor = qnode(1,node2)
        rhor_inv = my_1 / rhor
        rhorrr = 1.0_dp

      ur = qnode(2,node2) * rhor_inv
        urrr = -ur*rhor_inv
        urur = 1.0_dp * rhor_inv

      vr = qnode(3,node2) * rhor_inv
        vrrr = -vr*rhor_inv
        vrvr = 1.0_dp * rhor_inv

      wr = qnode(4,node2) * rhor_inv
        wrrr = -wr*rhor_inv
        wrwr = 1.0_dp * rhor_inv

      q2r = ur*ur + vr*vr + wr*wr
        q2rrr = 2.0_dp*ur*urrr + 2.0_dp*vr*vrrr + 2.0_dp*wr*wrrr
        q2rur = 2.0_dp*ur*urur
        q2rvr = 2.0_dp*vr*vrvr
        q2rwr = 2.0_dp*wr*wrwr

      enrgyr = qnode(5,node2)
        enrgyrpr = 1.0_dp

      pressr = gm1*(enrgyr - 0.5_dp*rhor*q2r)
        pressrrr = -0.5_dp*gm1*(rhor*q2rrr + q2r*rhorrr)
        pressrur = -0.5_dp*gm1*rhor*q2rur
        pressrvr = -0.5_dp*gm1*rhor*q2rvr
        pressrwr = -0.5_dp*gm1*rhor*q2rwr
        pressrpr = gm1

      Hr = (enrgyr + pressr)*rhor_inv
        Hrrr = (rhor*(pressrrr) - (enrgyr+pressr)*rhorrr) * rhor_inv * rhor_inv
        Hrur = (rhor*(pressrur)) * rhor_inv * rhor_inv
        Hrvr = (rhor*(pressrvr)) * rhor_inv * rhor_inv
        Hrwr = (rhor*(pressrwr)) * rhor_inv * rhor_inv
        Hrpr = (rhor*(enrgyrpr+pressrpr)) * rhor_inv * rhor_inv

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed
        ubarrrr = xnorm*urrr + ynorm*vrrr + znorm*wrrr
        ubarrur = xnorm*urur
        ubarrvr = ynorm*vrvr
        ubarrwr = znorm*wrwr

! Compute Roe averages

      rho = sqrt(rhol*rhor)
      rho_inv = my_1/rho

      rhorl = 0.5_dp * rho_inv * (rhor*rholrl)
      rhorr = 0.5_dp * rho_inv * (rhol*rhorrr)

      wat = rho/(rho + rhor)
        watrl = ((rho + rhor)*rhorl - rho*(rhorl)) / (rho+rhor) / (rho+rhor)
        watrr = ((rho + rhor)*rhorr-rho*(rhorr + rhorrr))/(rho+rhor)/(rho+rhor)

      u = ul*wat + ur*(1.0_dp - wat)
        url = ul*watrl + wat*ulrl + ur*(-watrl)
        uul = wat*ulul
        urr = ul*watrr + ur*(-watrr) + (1.0_dp - wat)*urrr
        uur = (1.0_dp - wat)*urur

      v = vl*wat + vr*(1.0_dp - wat)
        vrl = vl*watrl + wat*vlrl + vr*(-watrl)
        vvl = wat*vlvl
        vrr = vl*watrr + vr*(-watrr) + (1.0_dp - wat)*vrrr
        vvr = (1.0_dp - wat)*vrvr

      w = wl*wat + wr*(1.0_dp - wat)
        wrl = wl*watrl + wat*wlrl + wr*(-watrl)
        wwl = wat*wlwl
        wrr = wl*watrr + wr*(-watrr) + (1.0_dp - wat)*wrrr
        wwr = (1.0_dp - wat)*wrwr

      H = Hl*wat + Hr*(1.0_dp - wat)
        Hrl = Hl*watrl + wat*Hlrl + Hr*(-watrl)
        Hul = wat*Hlul
        Hvl = wat*Hlvl
        Hwl = wat*Hlwl
        Hpl = wat*Hlpl

        Hrr = Hl*watrr + Hr*(-watrr) + (1.0_dp - wat)*Hrrr
        Hur = (1.0_dp - wat)*Hrur
        Hvr = (1.0_dp - wat)*Hrvr
        Hwr = (1.0_dp - wat)*Hrwr
        Hpr = (1.0_dp - wat)*Hrpr

      q2 = u*u + v*v + w*w
        q2rl = 2.0_dp*u*url + 2.0_dp*v*vrl + 2.0_dp*w*wrl
        q2ul = 2.0_dp*u*uul
        q2vl = 2.0_dp*v*vvl
        q2wl = 2.0_dp*w*wwl

        q2rr = 2.0_dp*u*urr + 2.0_dp*v*vrr + 2.0_dp*w*wrr
        q2ur = 2.0_dp*u*uur
        q2vr = 2.0_dp*v*vvr
        q2wr = 2.0_dp*w*wwr

      c = sqrt(gm1*(H - 0.5_dp*q2))
        c_inv = my_1/c
        crl = 0.5_dp * c_inv * gm1*(Hrl - 0.5_dp*q2rl)
        cul = 0.5_dp * c_inv * gm1*(Hul - 0.5_dp*q2ul)
        cvl = 0.5_dp * c_inv * gm1*(Hvl - 0.5_dp*q2vl)
        cwl = 0.5_dp * c_inv * gm1*(Hwl - 0.5_dp*q2wl)
        cpl = 0.5_dp * c_inv * gm1*(Hpl)

        crr = 0.5_dp * c_inv * gm1*(Hrr - 0.5_dp*q2rr)
        cur = 0.5_dp * c_inv * gm1*(Hur - 0.5_dp*q2ur)
        cvr = 0.5_dp * c_inv * gm1*(Hvr - 0.5_dp*q2vr)
        cwr = 0.5_dp * c_inv * gm1*(Hwr - 0.5_dp*q2wr)
        cpr = 0.5_dp * c_inv * gm1*(Hpr)

      ubar = xnorm*u + ynorm*v + znorm*w - face_speed
        ubarrl = xnorm*url + ynorm*vrl + znorm*wrl
        ubarul = xnorm*uul
        ubarvl = ynorm*vvl
        ubarwl = znorm*wwl

        ubarrr = xnorm*urr + ynorm*vrr + znorm*wrr
        ubarur = xnorm*uur
        ubarvr = ynorm*vvr
        ubarwr = znorm*wwr

! Eigenvalue limiting.  In terms of dimensional equations:
! -limit eigenvalues as fraction of local maximum

      if (adptv_entropy_fix) then

        if (ubar > my_0) then
          dm = my_1
        else
          dm =-my_1
        endif

        dterm = my_1

        scoef   = my_0
!       scoef   = min(my_4th, flux_efixc(n))

!       maximum eigenvalue

        q2l  = ul*ul + vl*vl + wl*wl
        q2r  = ur*ur + vr*vr + wr*wr
        utngl  = sqrt(max(my_0, q2l-ubarl*ubarl))
        utngr  = sqrt(max(my_0, q2r-ubarr*ubarr))
        utang  = utngl*wat + utngr*(my_1 - wat)
        maxeig = max(abs( ubar ), abs( utang )) + c

!       acoustic eigenvalue limiter coefficient

!       aecoef  = min(my_4th, lhs_a_eigenvalue_coef*(my_1-scoef))
        aecoef  = 0.90_dp*min(my_4th, rhs_a_eigenvalue_coef*(my_1-scoef))
        aecoef  = max(0.009_dp, min(my_1, aecoef))

!       convective eigenvalue limiter coefficient

!       cecoef  = min(my_half, lhs_u_eigenvalue_coef*(my_1-scoef))
        cecoef  = 1.10_dp*min(my_half, rhs_u_eigenvalue_coef*(my_1-scoef))
        cecoef  = max(0.000011_dp, min(my_1, cecoef))

      else

        fa    = ubar
        faeps = 0.05_dp*c
        absfa = abs( fa )
        if (absfa < faeps) absfa = my_half*(fa**2/faeps + faeps)

!       maximum eigenvalue

        maxeig = absfa + c

        if (abs(fa) < faeps) then
          dm = fa/faeps
        else if (fa > my_0) then
          dm = my_1
        else
          dm =-my_1
        endif

        dterm = my_1 + my_half*(my_1 - dm**2)*0.05_dp

!       acoustic eigenvalue limiter coefficient

        aecoef  = lhs_a_eigenvalue_coef

!       convective eigenvalue limite coefficient

        cecoef  = lhs_u_eigenvalue_coef

      end if

      maxeigrl = dm*(ubarrl) + crl*dterm
      maxeigul = dm*(ubarul) + cul*dterm
      maxeigvl = dm*(ubarvl) + cvl*dterm
      maxeigwl = dm*(ubarwl) + cwl*dterm
      maxeigpl = cpl*dterm

      maxeigrr = dm*(ubarrr) + crr*dterm
      maxeigur = dm*(ubarur) + cur*dterm
      maxeigvr = dm*(ubarvr) + cvr*dterm
      maxeigwr = dm*(ubarwr) + cwr*dterm
      maxeigpr = cpr*dterm

! Now compute eigenvalues, eigenvectors, and strengths

      eig1 = ubar + c
      eig2 = ubar - c
      eig3 = ubar

! acoustic eigenvalue limiters

      eigeps1 = aecoef*maxeig
      eigeps2 = aecoef*maxeig

! convective eigenvalue limiter

      eigeps3 = cecoef*maxeig

      abseig1 = abs( eig1 )
      abseig2 = abs( eig2 )
      abseig3 = abs( eig3 )

      if(abseig1 < eigeps1) abseig1 = my_half*(eig1**2/eigeps1 + eigeps1)
      if(abseig2 < eigeps2) abseig2 = my_half*(eig2**2/eigeps2 + eigeps2)
      if(abseig3 < eigeps3) abseig3 = my_half*(eig3**2/eigeps3 + eigeps3)

      if(abs(eig1) < eigeps1 ) then
        d1 = eig1/eigeps1
      elseif(eig1 > my_0) then
        d1 = my_1
      else
        d1 =-my_1
      endif

      dterm = my_half*(my_1 - d1**2)*aecoef

        eig1rl = d1*(ubarrl + crl) + dterm*maxeigrl
        eig1ul = d1*(ubarul + cul) + dterm*maxeigul
        eig1vl = d1*(ubarvl + cvl) + dterm*maxeigvl
        eig1wl = d1*(ubarwl + cwl) + dterm*maxeigwl
        eig1pl = d1*(cpl) + dterm*maxeigpl

        eig1rr = d1*(ubarrr + crr) + dterm*maxeigrr
        eig1ur = d1*(ubarur + cur) + dterm*maxeigur
        eig1vr = d1*(ubarvr + cvr) + dterm*maxeigvr
        eig1wr = d1*(ubarwr + cwr) + dterm*maxeigwr
        eig1pr = d1*(cpr) + dterm*maxeigpr

      if(abs(eig2) < eigeps2 ) then
        d2 = eig2/eigeps2
      elseif(eig2 > my_0) then
        d2 = my_1
      else
        d2 =-my_1
      endif

      dterm = my_half*(my_1 - d2**2)*aecoef

        eig2rl = d2*(ubarrl - crl) + dterm*maxeigrl
        eig2ul = d2*(ubarul - cul) + dterm*maxeigul
        eig2vl = d2*(ubarvl - cvl) + dterm*maxeigvl
        eig2wl = d2*(ubarwl - cwl) + dterm*maxeigwl
        eig2pl = d2*(- cpl) + dterm*maxeigpl

        eig2rr = d2*(ubarrr - crr) + dterm*maxeigrr
        eig2ur = d2*(ubarur - cur) + dterm*maxeigur
        eig2vr = d2*(ubarvr - cvr) + dterm*maxeigvr
        eig2wr = d2*(ubarwr - cwr) + dterm*maxeigwr
        eig2pr = d2*(- cpr) + dterm*maxeigpr

      if(abs(eig3) < eigeps3 ) then
        d3 = eig3/eigeps3
      elseif(eig3 > my_0) then
        d3 = my_1
      else
        d3 =-my_1
      endif

      dterm = my_half*(my_1 - d3**2)*cecoef

        eig3rl = d3*ubarrl + dterm*maxeigrl
        eig3ul = d3*ubarul + dterm*maxeigul
        eig3vl = d3*ubarvl + dterm*maxeigvl
        eig3wl = d3*ubarwl + dterm*maxeigwl
        eig3pl = dterm*maxeigpl

        eig3rr = d3*ubarrr + dterm*maxeigrr
        eig3ur = d3*ubarur + dterm*maxeigur
        eig3vr = d3*ubarvr + dterm*maxeigvr
        eig3wr = d3*ubarwr + dterm*maxeigwr
        eig3pr = dterm*maxeigpr

      drho = rhor - rhol
        drhorl = - rholrl
        drhorr = rhorrr

      dpress = pressr - pressl
        dpressrl = - presslrl
        dpressul = - presslul
        dpressvl = - presslvl
        dpresswl = - presslwl
        dpresspl = - presslpl

        dpressrr = pressrrr
        dpressur = pressrur
        dpressvr = pressrvr
        dpresswr = pressrwr
        dpresspr = pressrpr

      du = ur - ul
        durl = - ulrl
        duul = - ulul
        durr = urrr
        duur = urur

      dv = vr - vl
        dvrl = - vlrl
        dvvl = - vlvl
        dvrr = vrrr
        dvvr = vrvr

      dw = wr - wl
        dwrl = - wlrl
        dwwl = - wlwl
        dwrr = wrrr
        dwwr = wrwr

      dubar = ubarr - ubarl
        dubarrl = - ubarlrl
        dubarul = - ubarlul
        dubarvl = - ubarlvl
        dubarwl = - ubarlwl

        dubarrr = ubarrrr
        dubarur = ubarrur
        dubarvr = ubarrvr
        dubarwr = ubarrwr

      c2 = c*c
        c2_inv = my_1/c2
        c22_inv = c2_inv*c2_inv
        c2rl = 2.0_dp * c * crl
        c2ul = 2.0_dp * c * cul
        c2vl = 2.0_dp * c * cvl
        c2wl = 2.0_dp * c * cwl
        c2pl = 2.0_dp * c * cpl

        c2rr = 2.0_dp * c * crr
        c2ur = 2.0_dp * c * cur
        c2vr = 2.0_dp * c * cvr
        c2wr = 2.0_dp * c * cwr
        c2pr = 2.0_dp * c * cpr

! jumps have units of density

      dv1 = 0.5_dp*(dpress + rho*c*dubar)*c2_inv
        dv1rl = 0.5_dp*(c2*(dpressrl + rho*(c*dubarrl + dubar*crl)         &
                + c*dubar*rhorl) - (dpress + rho*c*dubar)*c2rl) * c22_inv
        dv1ul = 0.5_dp*(c2*(dpressul + rho*(c*dubarul + dubar*cul))        &
                - (dpress + rho*c*dubar)*c2ul) * c22_inv
        dv1vl = 0.5_dp*(c2*(dpressvl + rho*(c*dubarvl + dubar*cvl))        &
                - (dpress + rho*c*dubar)*c2vl) * c22_inv
        dv1wl = 0.5_dp*(c2*(dpresswl + rho*(c*dubarwl + dubar*cwl))        &
                - (dpress + rho*c*dubar)*c2wl) * c22_inv
        dv1pl = 0.5_dp*(c2*(dpresspl + rho*(dubar*cpl))                    &
                - (dpress + rho*c*dubar)*c2pl) * c22_inv

        dv1rr = 0.5_dp*(c2*(dpressrr + rho*(c*dubarrr + dubar*crr)         &
                + c*dubar*rhorr) - (dpress + rho*c*dubar)*c2rr)            &
                * c22_inv
        dv1ur = 0.5_dp*(c2*(dpressur + rho*(c*dubarur + dubar*cur))        &
                - (dpress + rho*c*dubar)*c2ur) * c22_inv
        dv1vr = 0.5_dp*(c2*(dpressvr + rho*(c*dubarvr + dubar*cvr))        &
                - (dpress + rho*c*dubar)*c2vr) * c22_inv
        dv1wr = 0.5_dp*(c2*(dpresswr + rho*(c*dubarwr + dubar*cwr))        &
                - (dpress + rho*c*dubar)*c2wr) * c22_inv
        dv1pr = 0.5_dp*(c2*(dpresspr + rho*(dubar*cpr))                    &
                - (dpress + rho*c*dubar)*c2pr) * c22_inv

      dv2 = 0.5_dp*(dpress - rho*c*dubar)/c2
        dv2rl = 0.5_dp*(c2*(dpressrl - rho*(c*dubarrl + dubar*crl)         &
                - c*dubar*rhorl) - (dpress - rho*c*dubar)*c2rl)            &
                * c22_inv
        dv2ul = 0.5_dp*(c2*(dpressul - rho*(c*dubarul + dubar*cul))        &
                - (dpress - rho*c*dubar)*c2ul) * c22_inv
        dv2vl = 0.5_dp*(c2*(dpressvl - rho*(c*dubarvl + dubar*cvl))        &
                - (dpress - rho*c*dubar)*c2vl) * c22_inv
        dv2wl = 0.5_dp*(c2*(dpresswl - rho*(c*dubarwl + dubar*cwl))        &
                - (dpress - rho*c*dubar)*c2wl) * c22_inv
        dv2pl = 0.5_dp*(c2*(dpresspl - rho*(dubar*cpl))                    &
                - (dpress - rho*c*dubar)*c2pl) * c22_inv

        dv2rr = 0.5_dp*(c2*(dpressrr - rho*(c*dubarrr + dubar*crr)         &
                - c*dubar*rhorr) - (dpress - rho*c*dubar)*c2rr)            &
                * c22_inv
        dv2ur = 0.5_dp*(c2*(dpressur - rho*(c*dubarur + dubar*cur))        &
                - (dpress - rho*c*dubar)*c2ur) * c22_inv
        dv2vr = 0.5_dp*(c2*(dpressvr - rho*(c*dubarvr + dubar*cvr))        &
                - (dpress - rho*c*dubar)*c2vr) * c22_inv
        dv2wr = 0.5_dp*(c2*(dpresswr - rho*(c*dubarwr + dubar*cwr))        &
                - (dpress - rho*c*dubar)*c2wr) * c22_inv
        dv2pr = 0.5_dp*(c2*(dpresspr - rho*(dubar*cpr))                    &
                - (dpress - rho*c*dubar)*c2pr) * c22_inv

      dv3 = rho
        dv3rl = rhorl
        dv3rr = rhorr

      dv4 = (c*c*drho - dpress)/c2
        dv4rl = (c2*((c*(c*drhorl+drho*crl)+c*drho*crl) - dpressrl)        &
                - (c*c*drho - dpress)*c2rl) * c22_inv
        dv4ul = (c2*((c*(drho*cul)+c*drho*cul) - dpressul)                 &
                - (c*c*drho - dpress)*c2ul) * c22_inv
        dv4vl = (c2*((c*(drho*cvl)+c*drho*cvl) - dpressvl)                 &
                - (c*c*drho - dpress)*c2vl) * c22_inv
        dv4wl = (c2*((c*(drho*cwl)+c*drho*cwl) - dpresswl)                 &
                - (c*c*drho - dpress)*c2wl) * c22_inv
        dv4pl = (c2*((c*(drho*cpl)+c*drho*cpl) - dpresspl)                 &
                - (c*c*drho - dpress)*c2pl) * c22_inv

        dv4rr = (c2*((c*(c*drhorr+drho*crr)+c*drho*crr) - dpressrr)        &
                - (c*c*drho - dpress)*c2rr) * c22_inv
        dv4ur = (c2*((c*(drho*cur)+c*drho*cur) - dpressur)                 &
                - (c*c*drho - dpress)*c2ur) * c22_inv
        dv4vr = (c2*((c*(drho*cvr)+c*drho*cvr) - dpressvr)                 &
                - (c*c*drho - dpress)*c2vr) * c22_inv
        dv4wr = (c2*((c*(drho*cwr)+c*drho*cwr) - dpresswr)                 &
                - (c*c*drho - dpress)*c2wr) * c22_inv
        dv4pr = (c2*((c*(drho*cpr)+c*drho*cpr) - dpresspr)                 &
                - (c*c*drho - dpress)*c2pr) * c22_inv

      r21 = u + c*xnorm
        r21rl = url + xnorm*crl
        r21ul = uul + xnorm*cul
        r21vl = xnorm*cvl
        r21wl = xnorm*cwl
        r21pl = xnorm*cpl

        r21rr = urr + xnorm*crr
        r21ur = uur + xnorm*cur
        r21vr = xnorm*cvr
        r21wr = xnorm*cwr
        r21pr = xnorm*cpr

      r31 = v + c*ynorm
        r31rl = vrl + ynorm*crl
        r31ul = ynorm*cul
        r31vl = vvl + ynorm*cvl
        r31wl = ynorm*cwl
        r31pl = ynorm*cpl

        r31rr = vrr + ynorm*crr
        r31ur = ynorm*cur
        r31vr = vvr + ynorm*cvr
        r31wr = ynorm*cwr
        r31pr = ynorm*cpr

      r41 = w + c*znorm
        r41rl = wrl + znorm*crl
        r41ul = znorm*cul
        r41vl = znorm*cvl
        r41wl = wwl + znorm*cwl
        r41pl = znorm*cpl

        r41rr = wrr + znorm*crr
        r41ur = znorm*cur
        r41vr = znorm*cvr
        r41wr = wwr + znorm*cwr
        r41pr = znorm*cpr

      r51 = H + c*(ubar+face_speed)
        r51rl = Hrl + c*ubarrl + (ubar+face_speed)*crl
        r51ul = Hul + c*ubarul + (ubar+face_speed)*cul
        r51vl = Hvl + c*ubarvl + (ubar+face_speed)*cvl
        r51wl = Hwl + c*ubarwl + (ubar+face_speed)*cwl
        r51pl = Hpl + (ubar+face_speed)*cpl

        r51rr = Hrr + c*ubarrr + (ubar+face_speed)*crr
        r51ur = Hur + c*ubarur + (ubar+face_speed)*cur
        r51vr = Hvr + c*ubarvr + (ubar+face_speed)*cvr
        r51wr = Hwr + c*ubarwr + (ubar+face_speed)*cwr
        r51pr = Hpr + (ubar+face_speed)*cpr

      r22 = u - c*xnorm
        r22rl = url - xnorm*crl
        r22ul = uul - xnorm*cul
        r22vl = - xnorm*cvl
        r22wl = - xnorm*cwl
        r22pl = - xnorm*cpl

        r22rr = urr - xnorm*crr
        r22ur = uur - xnorm*cur
        r22vr = - xnorm*cvr
        r22wr = - xnorm*cwr
        r22pr = - xnorm*cpr

      r32 = v - c*ynorm
        r32rl = vrl - ynorm*crl
        r32ul = - ynorm*cul
        r32vl = vvl - ynorm*cvl
        r32wl = - ynorm*cwl
        r32pl = - ynorm*cpl

        r32rr = vrr - ynorm*crr
        r32ur = - ynorm*cur
        r32vr = vvr - ynorm*cvr
        r32wr = - ynorm*cwr
        r32pr = - ynorm*cpr

      r42 = w - c*znorm
        r42rl = wrl - znorm*crl
        r42ul = - znorm*cul
        r42vl = - znorm*cvl
        r42wl = wwl - znorm*cwl
        r42pl = - znorm*cpl

        r42rr = wrr - znorm*crr
        r42ur = - znorm*cur
        r42vr = - znorm*cvr
        r42wr = wwr - znorm*cwr
        r42pr = - znorm*cpr

      r52 = H - c*(ubar+face_speed)
        r52rl = Hrl - c*ubarrl - (ubar+face_speed)*crl
        r52ul = Hul - c*ubarul - (ubar+face_speed)*cul
        r52vl = Hvl - c*ubarvl - (ubar+face_speed)*cvl
        r52wl = Hwl - c*ubarwl - (ubar+face_speed)*cwl
        r52pl = Hpl - (ubar+face_speed)*cpl

        r52rr = Hrr - c*ubarrr - (ubar+face_speed)*crr
        r52ur = Hur - c*ubarur - (ubar+face_speed)*cur
        r52vr = Hvr - c*ubarvr - (ubar+face_speed)*cvr
        r52wr = Hwr - c*ubarwr - (ubar+face_speed)*cwr
        r52pr = Hpr - (ubar+face_speed)*cpr

      r23 = du - dubar*xnorm
        r23rl = durl - xnorm*dubarrl
        r23ul = duul - xnorm*dubarul
        r23vl = - xnorm*dubarvl
        r23wl = - xnorm*dubarwl

        r23rr = durr - xnorm*dubarrr
        r23ur = duur - xnorm*dubarur
        r23vr = - xnorm*dubarvr
        r23wr = - xnorm*dubarwr

      r33 = dv - dubar*ynorm
        r33rl = dvrl - ynorm*dubarrl
        r33ul = - ynorm*dubarul
        r33vl = dvvl - ynorm*dubarvl
        r33wl = - ynorm*dubarwl

        r33rr = dvrr - ynorm*dubarrr
        r33ur = - ynorm*dubarur
        r33vr = dvvr - ynorm*dubarvr
        r33wr = - ynorm*dubarwr

      r43 = dw - dubar*znorm
        r43rl = dwrl - znorm*dubarrl
        r43ul = - znorm*dubarul
        r43vl = - znorm*dubarvl
        r43wl = dwwl - znorm*dubarwl

        r43rr = dwrr - znorm*dubarrr
        r43ur = - znorm*dubarur
        r43vr = - znorm*dubarvr
        r43wr = dwwr - znorm*dubarwr

      r53 = u*du + v*dv + w*dw - (ubar+face_speed)*dubar
        r53rl = u*durl+du*url + v*dvrl+dv*vrl + w*dwrl+dw*wrl              &
                - (ubar+face_speed)*dubarrl - dubar*ubarrl
        r53ul = u*duul+du*uul - (ubar+face_speed)*dubarul - dubar*ubarul
        r53vl = v*dvvl+dv*vvl - (ubar+face_speed)*dubarvl - dubar*ubarvl
        r53wl = w*dwwl+dw*wwl - (ubar+face_speed)*dubarwl - dubar*ubarwl

        r53rr = u*durr+du*urr + v*dvrr+dv*vrr + w*dwrr+dw*wrr              &
                - (ubar+face_speed)*dubarrr - dubar*ubarrr
        r53ur = u*duur+du*uur - (ubar+face_speed)*dubarur - dubar*ubarur
        r53vr = v*dvvr+dv*vvr - (ubar+face_speed)*dubarvr - dubar*ubarvr
        r53wr = w*dwwr+dw*wwr - (ubar+face_speed)*dubarwr - dubar*ubarwr

      r24 = u
        r24rl = url
        r24ul = uul
        r24rr = urr
        r24ur = uur

      r34 = v
        r34rl = vrl
        r34vl = vvl
        r34rr = vrr
        r34vr = vvr

      r44 = w
        r44rl = wrl
        r44wl = wwl
        r44rr = wrr
        r44wr = wwr

      r54 = 0.5_dp*q2
        r54rl = 0.5_dp*q2rl
        r54ul = 0.5_dp*q2ul
        r54vl = 0.5_dp*q2vl
        r54wl = 0.5_dp*q2wl

        r54rr = 0.5_dp*q2rr
        r54ur = 0.5_dp*q2ur
        r54vr = 0.5_dp*q2vr
        r54wr = 0.5_dp*q2wr

!           t1 = abseig1*dv1     + abseig2*dv2                                 &
!                                + abseig3*dv4

        t1rl = abseig1*dv1rl+dv1*eig1rl + abseig2*dv2rl+dv2*eig2rl         &
             + abseig3*dv4rl+dv4*eig3rl
        t1ul = abseig1*dv1ul+dv1*eig1ul + abseig2*dv2ul+dv2*eig2ul         &
             + abseig3*dv4ul+dv4*eig3ul
        t1vl = abseig1*dv1vl+dv1*eig1vl + abseig2*dv2vl+dv2*eig2vl         &
             + abseig3*dv4vl+dv4*eig3vl
        t1wl = abseig1*dv1wl+dv1*eig1wl + abseig2*dv2wl+dv2*eig2wl         &
             + abseig3*dv4wl+dv4*eig3wl
        t1pl = abseig1*dv1pl+dv1*eig1pl + abseig2*dv2pl+dv2*eig2pl         &
             + abseig3*dv4pl+dv4*eig3pl

        t1rr = abseig1*dv1rr+dv1*eig1rr + abseig2*dv2rr+dv2*eig2rr         &
             + abseig3*dv4rr+dv4*eig3rr
        t1ur = abseig1*dv1ur+dv1*eig1ur + abseig2*dv2ur+dv2*eig2ur         &
             + abseig3*dv4ur+dv4*eig3ur
        t1vr = abseig1*dv1vr+dv1*eig1vr + abseig2*dv2vr+dv2*eig2vr         &
             + abseig3*dv4vr+dv4*eig3vr
        t1wr = abseig1*dv1wr+dv1*eig1wr + abseig2*dv2wr+dv2*eig2wr         &
             + abseig3*dv4wr+dv4*eig3wr
        t1pr = abseig1*dv1pr+dv1*eig1pr + abseig2*dv2pr+dv2*eig2pr         &
             + abseig3*dv4pr+dv4*eig3pr

!           t2 = abseig1*r21*dv1 + abseig2*r22*dv2                             &
!              + abseig3*r23*dv3 + abseig3*r24*dv4

        t2rl = abseig1*(r21*dv1rl+dv1*r21rl)+r21*dv1*eig1rl                &
             + abseig2*(r22*dv2rl+dv2*r22rl)+r22*dv2*eig2rl                &
             + abseig3*(r23*dv3rl+dv3*r23rl)+r23*dv3*eig3rl                &
             + abseig3*(r24*dv4rl+dv4*r24rl)+r24*dv4*eig3rl

        t2ul = abseig1*(r21*dv1ul+dv1*r21ul)+r21*dv1*eig1ul                &
             + abseig2*(r22*dv2ul+dv2*r22ul)+r22*dv2*eig2ul                &
             + abseig3*(dv3*r23ul)+r23*dv3*eig3ul                          &
             + abseig3*(r24*dv4ul+dv4*r24ul)+r24*dv4*eig3ul

        t2vl = abseig1*(r21*dv1vl+dv1*r21vl)+r21*dv1*eig1vl                &
             + abseig2*(r22*dv2vl+dv2*r22vl)+r22*dv2*eig2vl                &
             + abseig3*(dv3*r23vl)+r23*dv3*eig3vl                          &
             + abseig3*(r24*dv4vl)+r24*dv4*eig3vl

        t2wl = abseig1*(r21*dv1wl+dv1*r21wl)+r21*dv1*eig1wl                &
             + abseig2*(r22*dv2wl+dv2*r22wl)+r22*dv2*eig2wl                &
             + abseig3*(dv3*r23wl)+r23*dv3*eig3wl                          &
             + abseig3*(r24*dv4wl)+r24*dv4*eig3wl

        t2pl = abseig1*(r21*dv1pl+dv1*r21pl)+r21*dv1*eig1pl                &
             + abseig2*(r22*dv2pl+dv2*r22pl)+r22*dv2*eig2pl                &
             +r23*dv3*eig3pl + abseig3*(r24*dv4pl)+r24*dv4*eig3pl

        t2rr = abseig1*(r21*dv1rr+dv1*r21rr)+r21*dv1*eig1rr                &
             + abseig2*(r22*dv2rr+dv2*r22rr)+r22*dv2*eig2rr                &
             + abseig3*(r23*dv3rr+dv3*r23rr)+r23*dv3*eig3rr                &
             + abseig3*(r24*dv4rr+dv4*r24rr)+r24*dv4*eig3rr

        t2ur = abseig1*(r21*dv1ur+dv1*r21ur)+r21*dv1*eig1ur                &
             + abseig2*(r22*dv2ur+dv2*r22ur)+r22*dv2*eig2ur                &
             + abseig3*(dv3*r23ur)+r23*dv3*eig3ur                          &
             + abseig3*(r24*dv4ur+dv4*r24ur)+r24*dv4*eig3ur

        t2vr = abseig1*(r21*dv1vr+dv1*r21vr)+r21*dv1*eig1vr                &
             + abseig2*(r22*dv2vr+dv2*r22vr)+r22*dv2*eig2vr                &
             + abseig3*(dv3*r23vr)+r23*dv3*eig3vr                          &
             + abseig3*(r24*dv4vr)+r24*dv4*eig3vr

        t2wr = abseig1*(r21*dv1wr+dv1*r21wr)+r21*dv1*eig1wr                &
             + abseig2*(r22*dv2wr+dv2*r22wr)+r22*dv2*eig2wr                &
             + abseig3*(dv3*r23wr)+r23*dv3*eig3wr                          &
             + abseig3*(r24*dv4wr)+r24*dv4*eig3wr

        t2pr = abseig1*(r21*dv1pr+dv1*r21pr)+r21*dv1*eig1pr                &
             + abseig2*(r22*dv2pr+dv2*r22pr)+r22*dv2*eig2pr                &
             +r23*dv3*eig3pr + abseig3*(r24*dv4pr)+r24*dv4*eig3pr

!           t3 = abseig1*r31*dv1 + abseig2*r32*dv2                             &
!              + abseig3*r33*dv3 + abseig3*r34*dv4

        t3rl = abseig1*(r31*dv1rl+dv1*r31rl)+r31*dv1*eig1rl                &
             + abseig2*(r32*dv2rl+dv2*r32rl)+r32*dv2*eig2rl                &
             + abseig3*(r33*dv3rl+dv3*r33rl)+r33*dv3*eig3rl                &
             + abseig3*(r34*dv4rl+dv4*r34rl)+r34*dv4*eig3rl

        t3ul = abseig1*(r31*dv1ul+dv1*r31ul)+r31*dv1*eig1ul                &
             + abseig2*(r32*dv2ul+dv2*r32ul)+r32*dv2*eig2ul                &
             + abseig3*(dv3*r33ul)+r33*dv3*eig3ul                          &
             + abseig3*(r34*dv4ul)+r34*dv4*eig3ul

        t3vl = abseig1*(r31*dv1vl+dv1*r31vl)+r31*dv1*eig1vl                &
             + abseig2*(r32*dv2vl+dv2*r32vl)+r32*dv2*eig2vl                &
             + abseig3*(dv3*r33vl)+r33*dv3*eig3vl                          &
             + abseig3*(r34*dv4vl+dv4*r34vl)+r34*dv4*eig3vl

        t3wl = abseig1*(r31*dv1wl+dv1*r31wl)+r31*dv1*eig1wl                &
             + abseig2*(r32*dv2wl+dv2*r32wl)+r32*dv2*eig2wl                &
             + abseig3*(dv3*r33wl)+r33*dv3*eig3wl                          &
             + abseig3*(r34*dv4wl)+r34*dv4*eig3wl

        t3pl = abseig1*(r31*dv1pl+dv1*r31pl)+r31*dv1*eig1pl                &
             + abseig2*(r32*dv2pl+dv2*r32pl)+r32*dv2*eig2pl                &
             +r33*dv3*eig3pl + abseig3*(r34*dv4pl)+r34*dv4*eig3pl

        t3rr = abseig1*(r31*dv1rr+dv1*r31rr)+r31*dv1*eig1rr                &
             + abseig2*(r32*dv2rr+dv2*r32rr)+r32*dv2*eig2rr                &
             + abseig3*(r33*dv3rr+dv3*r33rr)+r33*dv3*eig3rr                &
             + abseig3*(r34*dv4rr+dv4*r34rr)+r34*dv4*eig3rr

        t3ur = abseig1*(r31*dv1ur+dv1*r31ur)+r31*dv1*eig1ur                &
             + abseig2*(r32*dv2ur+dv2*r32ur)+r32*dv2*eig2ur                &
             + abseig3*(dv3*r33ur)+r33*dv3*eig3ur                          &
             + abseig3*(r34*dv4ur)+r34*dv4*eig3ur

        t3vr = abseig1*(r31*dv1vr+dv1*r31vr)+r31*dv1*eig1vr                &
             + abseig2*(r32*dv2vr+dv2*r32vr)+r32*dv2*eig2vr                &
             + abseig3*(dv3*r33vr)+r33*dv3*eig3vr                          &
             + abseig3*(r34*dv4vr+dv4*r34vr)+r34*dv4*eig3vr

        t3wr = abseig1*(r31*dv1wr+dv1*r31wr)+r31*dv1*eig1wr                &
             + abseig2*(r32*dv2wr+dv2*r32wr)+r32*dv2*eig2wr                &
             + abseig3*(dv3*r33wr)+r33*dv3*eig3wr                          &
             + abseig3*(r34*dv4wr)+r34*dv4*eig3wr

        t3pr = abseig1*(r31*dv1pr+dv1*r31pr)+r31*dv1*eig1pr                &
             + abseig2*(r32*dv2pr+dv2*r32pr)+r32*dv2*eig2pr                &
             +r33*dv3*eig3pr + abseig3*(r34*dv4pr)+r34*dv4*eig3pr

!           t4 = abseig1*r41*dv1 + abseig2*r42*dv2                             &
!              + abseig3*r43*dv3 + abseig3*r44*dv4

        t4rl = abseig1*(r41*dv1rl+dv1*r41rl)+r41*dv1*eig1rl                &
             + abseig2*(r42*dv2rl+dv2*r42rl)+r42*dv2*eig2rl                &
             + abseig3*(r43*dv3rl+dv3*r43rl)+r43*dv3*eig3rl                &
             + abseig3*(r44*dv4rl+dv4*r44rl)+r44*dv4*eig3rl

        t4ul = abseig1*(r41*dv1ul+dv1*r41ul)+r41*dv1*eig1ul                &
             + abseig2*(r42*dv2ul+dv2*r42ul)+r42*dv2*eig2ul                &
             + abseig3*(dv3*r43ul)+r43*dv3*eig3ul                          &
             + abseig3*(r44*dv4ul)+r44*dv4*eig3ul

        t4vl = abseig1*(r41*dv1vl+dv1*r41vl)+r41*dv1*eig1vl                &
             + abseig2*(r42*dv2vl+dv2*r42vl)+r42*dv2*eig2vl                &
             + abseig3*(dv3*r43vl)+r43*dv3*eig3vl                          &
             + abseig3*(r44*dv4vl)+r44*dv4*eig3vl

        t4wl = abseig1*(r41*dv1wl+dv1*r41wl)+r41*dv1*eig1wl                &
             + abseig2*(r42*dv2wl+dv2*r42wl)+r42*dv2*eig2wl                &
             + abseig3*(dv3*r43wl)+r43*dv3*eig3wl                          &
             + abseig3*(r44*dv4wl+dv4*r44wl)+r44*dv4*eig3wl

        t4pl = abseig1*(r41*dv1pl+dv1*r41pl)+r41*dv1*eig1pl                &
             + abseig2*(r42*dv2pl+dv2*r42pl)+r42*dv2*eig2pl                &
             +r43*dv3*eig3pl + abseig3*(r44*dv4pl)+r44*dv4*eig3pl

        t4rr = abseig1*(r41*dv1rr+dv1*r41rr)+r41*dv1*eig1rr                &
             + abseig2*(r42*dv2rr+dv2*r42rr)+r42*dv2*eig2rr                &
             + abseig3*(r43*dv3rr+dv3*r43rr)+r43*dv3*eig3rr                &
             + abseig3*(r44*dv4rr+dv4*r44rr)+r44*dv4*eig3rr

        t4ur = abseig1*(r41*dv1ur+dv1*r41ur)+r41*dv1*eig1ur                &
             + abseig2*(r42*dv2ur+dv2*r42ur)+r42*dv2*eig2ur                &
             + abseig3*(dv3*r43ur)+r43*dv3*eig3ur                          &
             + abseig3*(r44*dv4ur)+r44*dv4*eig3ur

        t4vr = abseig1*(r41*dv1vr+dv1*r41vr)+r41*dv1*eig1vr                &
             + abseig2*(r42*dv2vr+dv2*r42vr)+r42*dv2*eig2vr                &
             + abseig3*(dv3*r43vr)+r43*dv3*eig3vr                          &
             + abseig3*(r44*dv4vr)+r44*dv4*eig3vr

        t4wr = abseig1*(r41*dv1wr+dv1*r41wr)+r41*dv1*eig1wr                &
             + abseig2*(r42*dv2wr+dv2*r42wr)+r42*dv2*eig2wr                &
             + abseig3*(dv3*r43wr)+r43*dv3*eig3wr                          &
             + abseig3*(r44*dv4wr+dv4*r44wr)+r44*dv4*eig3wr

        t4pr = abseig1*(r41*dv1pr+dv1*r41pr)+r41*dv1*eig1pr                &
             + abseig2*(r42*dv2pr+dv2*r42pr)+r42*dv2*eig2pr                &
             +r43*dv3*eig3pr + abseig3*(r44*dv4pr)+r44*dv4*eig3pr

!           t5 = abseig1*r51*dv1 + abseig2*r52*dv2                             &
!              + abseig3*r53*dv3 + abseig3*r54*dv4

        t5rl = abseig1*(r51*dv1rl+dv1*r51rl)+r51*dv1*eig1rl                &
             + abseig2*(r52*dv2rl+dv2*r52rl)+r52*dv2*eig2rl                &
             + abseig3*(r53*dv3rl+dv3*r53rl)+r53*dv3*eig3rl                &
             + abseig3*(r54*dv4rl+dv4*r54rl)+r54*dv4*eig3rl

        t5ul = abseig1*(r51*dv1ul+dv1*r51ul)+r51*dv1*eig1ul                &
             + abseig2*(r52*dv2ul+dv2*r52ul)+r52*dv2*eig2ul                &
             + abseig3*(dv3*r53ul)+r53*dv3*eig3ul                          &
             + abseig3*(r54*dv4ul+dv4*r54ul)+r54*dv4*eig3ul

        t5vl = abseig1*(r51*dv1vl+dv1*r51vl)+r51*dv1*eig1vl                &
             + abseig2*(r52*dv2vl+dv2*r52vl)+r52*dv2*eig2vl                &
             + abseig3*(dv3*r53vl)+r53*dv3*eig3vl                          &
             + abseig3*(r54*dv4vl+dv4*r54vl)+r54*dv4*eig3vl

        t5wl = abseig1*(r51*dv1wl+dv1*r51wl)+r51*dv1*eig1wl                &
             + abseig2*(r52*dv2wl+dv2*r52wl)+r52*dv2*eig2wl                &
             + abseig3*(dv3*r53wl)+r53*dv3*eig3wl                          &
             + abseig3*(r54*dv4wl+dv4*r54wl)+r54*dv4*eig3wl

        t5pl = abseig1*(r51*dv1pl+dv1*r51pl)+r51*dv1*eig1pl                &
             + abseig2*(r52*dv2pl+dv2*r52pl)+r52*dv2*eig2pl                &
             +r53*dv3*eig3pl + abseig3*(r54*dv4pl)+r54*dv4*eig3pl
        t5rr = abseig1*(r51*dv1rr+dv1*r51rr)+r51*dv1*eig1rr                &
             + abseig2*(r52*dv2rr+dv2*r52rr)+r52*dv2*eig2rr                &
             + abseig3*(r53*dv3rr+dv3*r53rr)+r53*dv3*eig3rr                &
             + abseig3*(r54*dv4rr+dv4*r54rr)+r54*dv4*eig3rr

        t5ur = abseig1*(r51*dv1ur+dv1*r51ur)+r51*dv1*eig1ur                &
             + abseig2*(r52*dv2ur+dv2*r52ur)+r52*dv2*eig2ur                &
             + abseig3*(dv3*r53ur)+r53*dv3*eig3ur                          &
             + abseig3*(r54*dv4ur+dv4*r54ur)+r54*dv4*eig3ur

        t5vr = abseig1*(r51*dv1vr+dv1*r51vr)+r51*dv1*eig1vr                &
             + abseig2*(r52*dv2vr+dv2*r52vr)+r52*dv2*eig2vr                &
             + abseig3*(dv3*r53vr)+r53*dv3*eig3vr                          &
             + abseig3*(r54*dv4vr+dv4*r54vr)+r54*dv4*eig3vr

        t5wr = abseig1*(r51*dv1wr+dv1*r51wr)+r51*dv1*eig1wr                &
             + abseig2*(r52*dv2wr+dv2*r52wr)+r52*dv2*eig2wr                &
             + abseig3*(dv3*r53wr)+r53*dv3*eig3wr                          &
             + abseig3*(r54*dv4wr+dv4*r54wr)+r54*dv4*eig3wr

        t5pr = abseig1*(r51*dv1pr+dv1*r51pr)+r51*dv1*eig1pr                &
             + abseig2*(r52*dv2pr+dv2*r52pr)+r52*dv2*eig2pr                &
             +r53*dv3*eig3pr + abseig3*(r54*dv4pr)+r54*dv4*eig3pr

! Compute flux using variables from left side of face

!           fluxp1 = area*rhol*ubarl

        fluxp1rl = area*(rhol*ubarlrl + ubarl*rholrl)
        fluxp1ul = area*(rhol*ubarlul)
        fluxp1vl = area*(rhol*ubarlvl)
        fluxp1wl = area*(rhol*ubarlwl)

!           fluxp2 = area*(rhol*ul*ubarl + xnorm*pressl)

        fluxp2rl = area*(rhol*(ul*ubarlrl+ubarl*ulrl) +                    &
                   ul*ubarl*rholrl + xnorm*presslrl)
        fluxp2ul = area*(rhol*(ul*ubarlul+ubarl*ulul) + xnorm*presslul)
        fluxp2vl = area*(rhol*(ul*ubarlvl) + xnorm*presslvl)
        fluxp2wl = area*(rhol*(ul*ubarlwl) + xnorm*presslwl)
        fluxp2pl = area*(xnorm*presslpl)

!           fluxp3 = area*(rhol*vl*ubarl + ynorm*pressl)

        fluxp3rl = area*(rhol*(vl*ubarlrl+ubarl*vlrl) +                    &
                   vl*ubarl*rholrl + ynorm*presslrl)
        fluxp3ul = area*(rhol*(vl*ubarlul) + ynorm*presslul)
        fluxp3vl = area*(rhol*(vl*ubarlvl+ubarl*vlvl) + ynorm*presslvl)
        fluxp3wl = area*(rhol*(vl*ubarlwl) + ynorm*presslwl)
        fluxp3pl = area*(ynorm*presslpl)

!           fluxp4 = area*(rhol*wl*ubarl + znorm*pressl)

        fluxp4rl = area*(rhol*(wl*ubarlrl+ubarl*wlrl) +                    &
                   wl*ubarl*rholrl + znorm*presslrl)
        fluxp4ul = area*(rhol*(wl*ubarlul) + znorm*presslul)
        fluxp4vl = area*(rhol*(wl*ubarlvl) + znorm*presslvl)
        fluxp4wl = area*(rhol*(wl*ubarlwl+ubarl*wlwl) + znorm*presslwl)
        fluxp4pl = area*(znorm*presslpl)

!           fluxp5 = area*(enrgyl + pressl)*ubarl + area*face_speed*pressl

        fluxp5rl = area*((enrgyl + pressl)*ubarlrl +                       &
                   ubarl*(presslrl)) + area*face_speed*presslrl
        fluxp5ul = area*((enrgyl + pressl)*ubarlul +                       &
                   ubarl*(presslul)) + area*face_speed*presslul
        fluxp5vl = area*((enrgyl + pressl)*ubarlvl +                       &
                   ubarl*(presslvl)) + area*face_speed*presslvl
        fluxp5wl = area*((enrgyl + pressl)*ubarlwl +                       &
                   ubarl*(presslwl)) + area*face_speed*presslwl
        fluxp5pl = area*(ubarl*(enrgylpl+presslpl))+area*face_speed*presslpl

! Now the right side

!           fluxm1 = area*rhor*ubarr

        fluxm1rr = area*(rhor*ubarrrr + ubarr*rhorrr)
        fluxm1ur = area*(rhor*ubarrur)
        fluxm1vr = area*(rhor*ubarrvr)
        fluxm1wr = area*(rhor*ubarrwr)

!           fluxm2 = area*(rhor*ur*ubarr + xnorm*pressr)

        fluxm2rr = area*(rhor*(ur*ubarrrr+ubarr*urrr) +                    &
                   ur*ubarr*rhorrr + xnorm*pressrrr)
        fluxm2ur = area*(rhor*(ur*ubarrur+ubarr*urur) + xnorm*pressrur)
        fluxm2vr = area*(rhor*(ur*ubarrvr) + xnorm*pressrvr)
        fluxm2wr = area*(rhor*(ur*ubarrwr) + xnorm*pressrwr)
        fluxm2pr = area*(xnorm*pressrpr)

!           fluxm3 = area*(rhor*vr*ubarr + ynorm*pressr)

        fluxm3rr = area*(rhor*(vr*ubarrrr+ubarr*vrrr) +                    &
                   vr*ubarr*rhorrr + ynorm*pressrrr)
        fluxm3ur = area*(rhor*(vr*ubarrur) + ynorm*pressrur)
        fluxm3vr = area*(rhor*(vr*ubarrvr+ubarr*vrvr) + ynorm*pressrvr)
        fluxm3wr = area*(rhor*(vr*ubarrwr) + ynorm*pressrwr)
        fluxm3pr = area*(ynorm*pressrpr)

!           fluxm4 = area*(rhor*wr*ubarr + znorm*pressr)

        fluxm4rr = area*(rhor*(wr*ubarrrr+ubarr*wrrr) +                    &
                   wr*ubarr*rhorrr + znorm*pressrrr)
        fluxm4ur = area*(rhor*(wr*ubarrur) + znorm*pressrur)
        fluxm4vr = area*(rhor*(wr*ubarrvr) + znorm*pressrvr)
        fluxm4wr = area*(rhor*(wr*ubarrwr+ubarr*wrwr) + znorm*pressrwr)
        fluxm4pr = area*(znorm*pressrpr)

!           fluxm5 = area*(enrgyr + pressr)*ubarr + area*face_speed*pressr

        fluxm5rr = area*((enrgyr + pressr)*ubarrrr +                       &
                   ubarr*(pressrrr)) + area*face_speed*pressrrr
        fluxm5ur = area*((enrgyr + pressr)*ubarrur +                       &
                   ubarr*(pressrur)) + area*face_speed*pressrur
        fluxm5vr = area*((enrgyr + pressr)*ubarrvr +                       &
                   ubarr*(pressrvr)) + area*face_speed*pressrvr
        fluxm5wr = area*((enrgyr + pressr)*ubarrwr +                       &
                   ubarr*(pressrwr)) + area*face_speed*pressrwr
        fluxm5pr = area*(ubarr*(enrgyrpr+pressrpr))+area*face_speed*pressrpr

!         flux1 = 0.5_dp*(fluxp1 + fluxm1 - area*t1)
!         flux2 = 0.5_dp*(fluxp2 + fluxm2 - area*t2)
!         flux3 = 0.5_dp*(fluxp3 + fluxm3 - area*t3)
!         flux4 = 0.5_dp*(fluxp4 + fluxm4 - area*t4)
!         flux5 = 0.5_dp*(fluxp5 + fluxm5 - area*t5)

        flux1rl = 0.5_dp*(fluxp1rl - area*t1rl)
        flux1ul = 0.5_dp*(fluxp1ul - area*t1ul)
        flux1vl = 0.5_dp*(fluxp1vl - area*t1vl)
        flux1wl = 0.5_dp*(fluxp1wl - area*t1wl)
        flux1pl = 0.5_dp*(- area*t1pl)
        flux1rr = 0.5_dp*(fluxm1rr - area*t1rr)
        flux1ur = 0.5_dp*(fluxm1ur - area*t1ur)
        flux1vr = 0.5_dp*(fluxm1vr - area*t1vr)
        flux1wr = 0.5_dp*(fluxm1wr - area*t1wr)
        flux1pr = 0.5_dp*(- area*t1pr)

        flux2rl = 0.5_dp*(fluxp2rl - area*t2rl)
        flux2ul = 0.5_dp*(fluxp2ul - area*t2ul)
        flux2vl = 0.5_dp*(fluxp2vl - area*t2vl)
        flux2wl = 0.5_dp*(fluxp2wl - area*t2wl)
        flux2pl = 0.5_dp*(fluxp2pl - area*t2pl)
        flux2rr = 0.5_dp*(fluxm2rr - area*t2rr)
        flux2ur = 0.5_dp*(fluxm2ur - area*t2ur)
        flux2vr = 0.5_dp*(fluxm2vr - area*t2vr)
        flux2wr = 0.5_dp*(fluxm2wr - area*t2wr)
        flux2pr = 0.5_dp*(fluxm2pr - area*t2pr)

        flux3rl = 0.5_dp*(fluxp3rl - area*t3rl)
        flux3ul = 0.5_dp*(fluxp3ul - area*t3ul)
        flux3vl = 0.5_dp*(fluxp3vl - area*t3vl)
        flux3wl = 0.5_dp*(fluxp3wl - area*t3wl)
        flux3pl = 0.5_dp*(fluxp3pl - area*t3pl)
        flux3rr = 0.5_dp*(fluxm3rr - area*t3rr)
        flux3ur = 0.5_dp*(fluxm3ur - area*t3ur)
        flux3vr = 0.5_dp*(fluxm3vr - area*t3vr)
        flux3wr = 0.5_dp*(fluxm3wr - area*t3wr)
        flux3pr = 0.5_dp*(fluxm3pr - area*t3pr)

        flux4rl = 0.5_dp*(fluxp4rl - area*t4rl)
        flux4ul = 0.5_dp*(fluxp4ul - area*t4ul)
        flux4vl = 0.5_dp*(fluxp4vl - area*t4vl)
        flux4wl = 0.5_dp*(fluxp4wl - area*t4wl)
        flux4pl = 0.5_dp*(fluxp4pl - area*t4pl)
        flux4rr = 0.5_dp*(fluxm4rr - area*t4rr)
        flux4ur = 0.5_dp*(fluxm4ur - area*t4ur)
        flux4vr = 0.5_dp*(fluxm4vr - area*t4vr)
        flux4wr = 0.5_dp*(fluxm4wr - area*t4wr)
        flux4pr = 0.5_dp*(fluxm4pr - area*t4pr)

        flux5rl = 0.5_dp*(fluxp5rl - area*t5rl)
        flux5ul = 0.5_dp*(fluxp5ul - area*t5ul)
        flux5vl = 0.5_dp*(fluxp5vl - area*t5vl)
        flux5wl = 0.5_dp*(fluxp5wl - area*t5wl)
        flux5pl = 0.5_dp*(fluxp5pl - area*t5pl)
        flux5rr = 0.5_dp*(fluxm5rr - area*t5rr)
        flux5ur = 0.5_dp*(fluxm5ur - area*t5ur)
        flux5vr = 0.5_dp*(fluxm5vr - area*t5vr)
        flux5wr = 0.5_dp*(fluxm5wr - area*t5wr)
        flux5pr = 0.5_dp*(fluxm5pr - area*t5pr)

      dfp(1,1) = flux1rl
      dfp(1,2) = flux1ul
      dfp(1,3) = flux1vl
      dfp(1,4) = flux1wl
      dfp(1,5) = flux1pl

      dfp(2,1) = flux2rl
      dfp(2,2) = flux2ul
      dfp(2,3) = flux2vl
      dfp(2,4) = flux2wl
      dfp(2,5) = flux2pl

      dfp(3,1) = flux3rl
      dfp(3,2) = flux3ul
      dfp(3,3) = flux3vl
      dfp(3,4) = flux3wl
      dfp(3,5) = flux3pl

      dfp(4,1) = flux4rl
      dfp(4,2) = flux4ul
      dfp(4,3) = flux4vl
      dfp(4,4) = flux4wl
      dfp(4,5) = flux4pl

      dfp(5,1) = flux5rl
      dfp(5,2) = flux5ul
      dfp(5,3) = flux5vl
      dfp(5,4) = flux5wl
      dfp(5,5) = flux5pl

      dfm(1,1) = flux1rr
      dfm(1,2) = flux1ur
      dfm(1,3) = flux1vr
      dfm(1,4) = flux1wr
      dfm(1,5) = flux1pr

      dfm(2,1) = flux2rr
      dfm(2,2) = flux2ur
      dfm(2,3) = flux2vr
      dfm(2,4) = flux2wr
      dfm(2,5) = flux2pr

      dfm(3,1) = flux3rr
      dfm(3,2) = flux3ur
      dfm(3,3) = flux3vr
      dfm(3,4) = flux3wr
      dfm(3,5) = flux3pr

      dfm(4,1) = flux4rr
      dfm(4,2) = flux4ur
      dfm(4,3) = flux4vr
      dfm(4,4) = flux4wr
      dfm(4,5) = flux4pr

      dfm(5,1) = flux5rr
      dfm(5,2) = flux5ur
      dfm(5,3) = flux5vr
      dfm(5,4) = flux5wr
      dfm(5,5) = flux5pr

      if(node1 <= nnodes0) then
        idiag = g2m(node1)
        do k = 1, 5
            a_diag(1,k,idiag) = a_diag(1,k,idiag) + dfp(1,k)
            a_diag(2,k,idiag) = a_diag(2,k,idiag) + dfp(2,k)
            a_diag(3,k,idiag) = a_diag(3,k,idiag) + dfp(3,k)
            a_diag(4,k,idiag) = a_diag(4,k,idiag) + dfp(4,k)
            a_diag(5,k,idiag) = a_diag(5,k,idiag) + dfp(5,k)
        end do

        ioff  = fhelp(1,n)
        do k = 1, 5
            a_off(1,k,ioff)  = a_off(1,k,ioff) + real(dfm(1,k),odp)
            a_off(2,k,ioff)  = a_off(2,k,ioff) + real(dfm(2,k),odp)
            a_off(3,k,ioff)  = a_off(3,k,ioff) + real(dfm(3,k),odp)
            a_off(4,k,ioff)  = a_off(4,k,ioff) + real(dfm(4,k),odp)
            a_off(5,k,ioff)  = a_off(5,k,ioff) + real(dfm(5,k),odp)
        end do
      end if

      if(node2 <= nnodes0) then
        idiag = g2m(node2)
        do k = 1, 5
            a_diag(1,k,idiag) = a_diag(1,k,idiag) - dfm(1,k)
            a_diag(2,k,idiag) = a_diag(2,k,idiag) - dfm(2,k)
            a_diag(3,k,idiag) = a_diag(3,k,idiag) - dfm(3,k)
            a_diag(4,k,idiag) = a_diag(4,k,idiag) - dfm(4,k)
            a_diag(5,k,idiag) = a_diag(5,k,idiag) - dfm(5,k)
        end do

        ioff  = fhelp(2,n)
        do k = 1, 5
            a_off(1,k,ioff)  = a_off(1,k,ioff) - real(dfp(1,k),odp)
            a_off(2,k,ioff)  = a_off(2,k,ioff) - real(dfp(2,k),odp)
            a_off(3,k,ioff)  = a_off(3,k,ioff) - real(dfp(3,k),odp)
            a_off(4,k,ioff)  = a_off(4,k,ioff) - real(dfp(4,k),odp)
            a_off(5,k,ioff)  = a_off(5,k,ioff) - real(dfp(5,k),odp)
        end do
      end if

    end do scanedges

  end subroutine roe_jacobians

!================================ ROE_JACOBIANS ==============================80
!
! Roe flux jacobian for generic gas path (part 1)
!
! This routine is usable by both the compressible and generic gas paths;
! however, for the generic gas path, this is the routine for solving the
! first part of the decoupled problem (solves for rho, momentum, total energy)
!
! All derivatives worked out in detail
!
! Watch out for the notation here.  Originally this routine linearized wrt
! primitive variables and then transformed to conserved.  The seed
! derivatives at the top were later changed to be wrt conserved to avoid the
! transformation (and the need for ptoe/etop).  However, the notation for the
! derivatives still reads like primitive.
!
!=============================================================================80

subroutine roe_jacobians_gen(nnodes0, nnodes01, nedgeloc, nedgeloc_2d,         &
                           max_nnz, eptr, qnode, a_diag, a_off, xn, yn, zn,    &
                           ra, fhelp, facespeed, n_tot, njac, g2m, eqn_set,    &
                           pressure_jac)

    use info_depr,       only : twod, adptv_entropy_fix
    use inviscid_flux,   only : rhs_a_eigenvalue_coef, rhs_u_eigenvalue_coef,  &
                                lhs_a_eigenvalue_coef, lhs_u_eigenvalue_coef
    use fluid,           only : gm1
    use generic_gas_map, only : n_pjac, n_mom, n_species, n_etot, n_density,   &
                                n_momx, n_momy, n_momz, n_pressure_k
    use solution_types,  only : generic_gas
    !use turb_gen,        only : n_turb_ke, n_dis_nutl, betat_jac

    integer,                                    intent(in) :: nnodes0,nnodes01
    integer,                                    intent(in) :: nedgeloc
    integer,                                    intent(in) :: nedgeloc_2d
    integer,                                    intent(in) :: max_nnz
    integer,                                    intent(in) :: n_tot,njac
    integer,                                    intent(in) :: eqn_set

    integer,      dimension(2,nedgeloc),        intent(in) :: eptr
    integer,      dimension(2,nedgeloc),        intent(in) :: fhelp
    integer,      dimension(:),                 intent(in) :: g2m

    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(njac,njac,nnodes0),     intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz),     intent(inout) :: a_off
    real(dp),  dimension(n_pjac,n_mom,nnodes01), intent(in)    :: pressure_jac

    integer :: n,node1,node2,nedge_jac_eval,idiag,k,ioff
    integer :: ns,ns1

    real(dp)    :: xnorm,ynorm,znorm,area,face_speed

    real(dp)    :: flux1rl,flux1ul,flux1vl,flux1wl,flux1pl
    real(dp)    :: flux2rl,flux2ul,flux2vl,flux2wl,flux2pl
    real(dp)    :: flux3rl,flux3ul,flux3vl,flux3wl,flux3pl
    real(dp)    :: flux4rl,flux4ul,flux4vl,flux4wl,flux4pl
    real(dp)    :: flux5rl,flux5ul,flux5vl,flux5wl,flux5pl

    real(dp)    :: fluxp1rl,fluxp1ul,fluxp1vl,fluxp1wl
    real(dp)    :: fluxp2rl,fluxp2ul,fluxp2vl,fluxp2wl,fluxp2pl
    real(dp)    :: fluxp3rl,fluxp3ul,fluxp3vl,fluxp3wl,fluxp3pl
    real(dp)    :: fluxp4rl,fluxp4ul,fluxp4vl,fluxp4wl,fluxp4pl
    real(dp)    :: fluxp5rl,fluxp5ul,fluxp5vl,fluxp5wl,fluxp5pl

    real(dp)    :: flux1rr,flux1ur,flux1vr,flux1wr,flux1pr
    real(dp)    :: flux2rr,flux2ur,flux2vr,flux2wr,flux2pr
    real(dp)    :: flux3rr,flux3ur,flux3vr,flux3wr,flux3pr
    real(dp)    :: flux4rr,flux4ur,flux4vr,flux4wr,flux4pr
    real(dp)    :: flux5rr,flux5ur,flux5vr,flux5wr,flux5pr

    real(dp)    :: fluxm1rr,fluxm1ur,fluxm1vr,fluxm1wr
    real(dp)    :: fluxm2rr,fluxm2ur,fluxm2vr,fluxm2wr,fluxm2pr
    real(dp)    :: fluxm3rr,fluxm3ur,fluxm3vr,fluxm3wr,fluxm3pr
    real(dp)    :: fluxm4rr,fluxm4ur,fluxm4vr,fluxm4wr,fluxm4pr
    real(dp)    :: fluxm5rr,fluxm5ur,fluxm5vr,fluxm5wr,fluxm5pr

    real(dp)    :: t1rl,t1ul,t1vl,t1wl,t1pl
    real(dp)    :: t2rl,t2ul,t2vl,t2wl,t2pl
    real(dp)    :: t3rl,t3ul,t3vl,t3wl,t3pl
    real(dp)    :: t4rl,t4ul,t4vl,t4wl,t4pl
    real(dp)    :: t5rl,t5ul,t5vl,t5wl,t5pl

    real(dp)    :: t1rr,t1ur,t1vr,t1wr,t1pr
    real(dp)    :: t2rr,t2ur,t2vr,t2wr,t2pr
    real(dp)    :: t3rr,t3ur,t3vr,t3wr,t3pr
    real(dp)    :: t4rr,t4ur,t4vr,t4wr,t4pr
    real(dp)    :: t5rr,t5ur,t5vr,t5wr,t5pr

    real(dp)    :: r54
    real(dp)    :: r54rl,r54ul,r54vl,r54wl
    real(dp)    :: r54rr,r54ur,r54vr,r54wr

    real(dp)    :: r44
    real(dp)    :: r44rl,r44wl
    real(dp)    :: r44rr,r44wr

    real(dp)    :: r34
    real(dp)    :: r34rl,r34vl
    real(dp)    :: r34rr,r34vr

    real(dp)    :: r24
    real(dp)    :: r24rl,r24ul
    real(dp)    :: r24rr,r24ur

    real(dp)    :: r53
    real(dp)    :: r53rl,r53ul,r53vl,r53wl
    real(dp)    :: r53rr,r53ur,r53vr,r53wr

    real(dp)    :: r43
    real(dp)    :: r43rl,r43ul,r43vl,r43wl
    real(dp)    :: r43rr,r43ur,r43vr,r43wr

    real(dp)    :: r33
    real(dp)    :: r33rl,r33ul,r33vl,r33wl
    real(dp)    :: r33rr,r33ur,r33vr,r33wr

    real(dp)    :: r23
    real(dp)    :: r23rl,r23ul,r23vl,r23wl
    real(dp)    :: r23rr,r23ur,r23vr,r23wr

    real(dp)    :: r52
    real(dp)    :: r52rl,r52ul,r52vl,r52wl,r52pl
    real(dp)    :: r52rr,r52ur,r52vr,r52wr,r52pr

    real(dp)    :: r42
    real(dp)    :: r42rl,r42ul,r42vl,r42wl,r42pl
    real(dp)    :: r42rr,r42ur,r42vr,r42wr,r42pr

    real(dp)    :: r32
    real(dp)    :: r32rl,r32ul,r32vl,r32wl,r32pl
    real(dp)    :: r32rr,r32ur,r32vr,r32wr,r32pr

    real(dp)    :: r22
    real(dp)    :: r22rl,r22ul,r22vl,r22wl,r22pl
    real(dp)    :: r22rr,r22ur,r22vr,r22wr,r22pr

    real(dp)    :: r51
    real(dp)    :: r51rl,r51ul,r51vl,r51wl,r51pl
    real(dp)    :: r51rr,r51ur,r51vr,r51wr,r51pr

    real(dp)    :: r41
    real(dp)    :: r41rl,r41ul,r41vl,r41wl,r41pl
    real(dp)    :: r41rr,r41ur,r41vr,r41wr,r41pr

    real(dp)    :: r31
    real(dp)    :: r31rl,r31ul,r31vl,r31wl,r31pl
    real(dp)    :: r31rr,r31ur,r31vr,r31wr,r31pr

    real(dp)    :: r21
    real(dp)    :: r21rl,r21ul,r21vl,r21wl,r21pl
    real(dp)    :: r21rr,r21ur,r21vr,r21wr,r21pr

    real(dp)    :: dv1
    real(dp)    :: dv1rl,dv1ul,dv1vl,dv1wl,dv1pl
    real(dp)    :: dv1rr,dv1ur,dv1vr,dv1wr,dv1pr

    real(dp)    :: dv2
    real(dp)    :: dv2rl,dv2ul,dv2vl,dv2wl,dv2pl
    real(dp)    :: dv2rr,dv2ur,dv2vr,dv2wr,dv2pr

    real(dp)    :: dv3
    real(dp)    :: dv3rl
    real(dp)    :: dv3rr

    real(dp)    :: dv4
    real(dp)    :: dv4rl,dv4ul,dv4vl,dv4wl,dv4pl
    real(dp)    :: dv4rr,dv4ur,dv4vr,dv4wr,dv4pr

    real(dp)    :: c2
    real(dp)    :: c2rl,c2ul,c2vl,c2wl,c2pl
    real(dp)    :: c2rr,c2ur,c2vr,c2wr,c2pr

    real(dp)    :: dubar
    real(dp)    :: dubarrl,dubarul,dubarvl,dubarwl
    real(dp)    :: dubarrr,dubarur,dubarvr,dubarwr

    real(dp)    :: du
    real(dp)    :: durl,duul
    real(dp)    :: durr,duur

    real(dp)    :: dv
    real(dp)    :: dvrl,dvvl
    real(dp)    :: dvrr,dvvr

    real(dp)    :: dw
    real(dp)    :: dwrl,dwwl
    real(dp)    :: dwrr,dwwr

    real(dp)    :: dpress
    real(dp)    :: dpressrl,dpressul,dpressvl,dpresswl,dpresspl
    real(dp)    :: dpressrr,dpressur,dpressvr,dpresswr,dpresspr

    real(dp)    :: drho
    real(dp)    :: drhorl
    real(dp)    :: drhorr

    real(dp)    :: eig1, abseig1
    real(dp)    :: eig1rl,eig1ul,eig1vl,eig1wl,eig1pl
    real(dp)    :: eig1rr,eig1ur,eig1vr,eig1wr,eig1pr

    real(dp)    :: eig2, abseig2
    real(dp)    :: eig2rl,eig2ul,eig2vl,eig2wl,eig2pl
    real(dp)    :: eig2rr,eig2ur,eig2vr,eig2wr,eig2pr

    real(dp)    :: eig3, abseig3
    real(dp)    :: eig3rl,eig3ul,eig3vl,eig3wl,eig3pl
    real(dp)    :: eig3rr,eig3ur,eig3vr,eig3wr,eig3pr

    real(dp)    :: maxeig
    real(dp)    :: maxeigrl,maxeigul,maxeigvl,maxeigwl,maxeigpl
    real(dp)    :: maxeigrr,maxeigur,maxeigvr,maxeigwr,maxeigpr

    real(dp)    :: ubar
    real(dp)    :: ubarrl,ubarul,ubarvl,ubarwl
    real(dp)    :: ubarrr,ubarur,ubarvr,ubarwr

    real(dp)    :: c
    real(dp)    :: crl,cul,cvl,cwl,cpl
    real(dp)    :: crr,cur,cvr,cwr,cpr

    real(dp)    :: q2
    real(dp)    :: q2rl,q2ul,q2vl,q2wl
    real(dp)    :: q2rr,q2ur,q2vr,q2wr

    real(dp)    :: h
    real(dp)    :: hrl,hul,hvl,hwl,hpl
    real(dp)    :: hrr,hur,hvr,hwr,hpr

    real(dp)    :: u
    real(dp)    :: url,uul
    real(dp)    :: urr,uur

    real(dp)    :: v
    real(dp)    :: vrl,vvl
    real(dp)    :: vrr,vvr

    real(dp)    :: w
    real(dp)    :: wrl,wwl
    real(dp)    :: wrr,wwr

    real(dp)    :: wat, dwat
    real(dp)    :: watrl
    real(dp)    :: watrr

    real(dp)    :: rho
    real(dp)    :: rhorl
    real(dp)    :: rhorr

    real(dp)    :: ubarr
    real(dp)    :: ubarrrr,ubarrur,ubarrvr,ubarrwr

    real(dp)    :: hr
    real(dp)    :: hrrr,hrur,hrvr,hrwr,hrpr

    real(dp)    :: enrgyr
    real(dp)    :: enrgyrpr

    real(dp)    :: pressr
    real(dp)    :: pressrrr,pressrur,pressrvr,pressrwr,pressrpr

    real(dp)    :: q2r
    real(dp)    :: q2rrr,q2rur,q2rvr,q2rwr

    real(dp)    :: ur
    real(dp)    :: urrr,urur

    real(dp)    :: vr
    real(dp)    :: vrrr,vrvr

    real(dp)    :: wr
    real(dp)    :: wrrr,wrwr

    real(dp)    :: rhor
    real(dp)    :: rhorrr

    real(dp)    :: ubarl
    real(dp)    :: ubarlrl,ubarlul,ubarlvl,ubarlwl

    real(dp)    :: hl
    real(dp)    :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp)    :: enrgyl
    real(dp)    :: enrgylpl

    real(dp)    :: pressl,dp_drho
    real(dp)    :: presslrl,presslul,presslvl,presslwl,presslpl

    real(dp)    :: q2l
    real(dp)    :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp)    :: ul
    real(dp)    :: ulrl,ulul

    real(dp)    :: vl
    real(dp)    :: vlrl,vlvl

    real(dp)    :: wl
    real(dp)    :: wlrl,wlwl

    real(dp)    :: rhol
    real(dp)    :: rholrl

    real(dp)    :: eigeps1, eigeps2, eigeps3
    real(dp)    :: d1, d2, d3

    real(dp), dimension(5,5)       :: dfm,dfp

    real(dp), dimension(n_species) :: fracl_i,fracr_i,frac_i
    !real(dp), dimension(n_energy)  :: energyl_j,energyr_j
    real(dp), dimension(n_pjac)    :: betal,betar,beta

    real(dp)    :: fa, faeps, absfa, dm, dterm
    real(dp)    :: utngl, utngr, utang, scoef, aecoef, cecoef
    real(dp)    :: rhol_inv, rhor_inv, rho_inv
    real(dp)    :: c_inv, c2_inv, c22_inv
    real(dp)    :: wt1,wt2,ratio, dp_de

    logical     :: generic

    real(dp), parameter    :: my_0    = 0.0_dp
    real(dp), parameter    :: my_4th  = 0.25_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_1    = 1.0_dp

  continue

!--- test to see if generic or compressible gas path

  if ( eqn_set == generic_gas ) then
    generic = .true.

    !alias long names
    ns      = n_species
    ns1     = n_species + 1
  else
    generic = .false.
  end if

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

! Loop over the edges and calculate the Jacobians

    scanedges: do n = 1, nedge_jac_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Get unit normals and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

! Face speed

      face_speed = 0.0_dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

! Get variables on "left" side of face

      rhol = qnode(n_density,node1)
        rhol_inv = my_1 / rhol
        rholrl = 1.0_dp

      ul = qnode(n_momx,node1) * rhol_inv
        ulrl = -ul*rhol_inv
        ulul = 1.0_dp * rhol_inv

      vl = qnode(n_momy,node1) * rhol_inv
        vlrl = -vl*rhol_inv
        vlvl = 1.0_dp * rhol_inv

      wl = qnode(n_momz,node1) * rhol_inv
        wlrl = -wl*rhol_inv
        wlwl = 1.0_dp * rhol_inv

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = qnode(n_etot,node1)
        enrgylpl = 1.0_dp

      !use pressure jacobian for generic gas path (NOT pg EOS)
      if ( generic ) then
        !mass fractions
        fracl_i(1:ns) = qnode(1:ns,node1)*rhol_inv

        !pressure jacobian
        betal(:) = pressure_jac(:,1,node1)

        pressl = qnode(n_pressure_k(1),node1)
          presslrl = sum(fracl_i(1:ns)*(betal(1:ns) + 0.5_dp*(betal(ns1)*q2l)))
          presslul = -ul*betal(ns1)
          presslvl = -vl*betal(ns1)
          presslwl = -wl*betal(ns1)
          presslpl = betal(ns1)
      else !compressible path
        pressl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
          presslrl = -0.5_dp*gm1*(rhol*q2lrl + q2l*rholrl)
          presslul = -0.5_dp*gm1*rhol*q2lul
          presslvl = -0.5_dp*gm1*rhol*q2lvl
          presslwl = -0.5_dp*gm1*rhol*q2lwl
          presslpl = gm1
      end if

      Hl = (enrgyl + pressl)*rhol_inv
        Hlrl = (rhol*(presslrl) - (enrgyl+pressl)*rholrl) * rhol_inv * rhol_inv
        Hlul = (rhol*(presslul)) * rhol_inv * rhol_inv
        Hlvl = (rhol*(presslvl)) * rhol_inv * rhol_inv
        Hlwl = (rhol*(presslwl)) * rhol_inv * rhol_inv
        Hlpl = (rhol*(enrgylpl+presslpl)) * rhol_inv * rhol_inv

      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed
        ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
        ubarlul = xnorm*ulul
        ubarlvl = ynorm*vlvl
        ubarlwl = znorm*wlwl

! Get variables on "right" side of face

      rhor = qnode(n_density,node2)
        rhor_inv = my_1 / rhor
        rhorrr = 1.0_dp

      ur = qnode(n_momx,node2) * rhor_inv
        urrr = -ur*rhor_inv
        urur = 1.0_dp * rhor_inv

      vr = qnode(n_momy,node2) * rhor_inv
        vrrr = -vr*rhor_inv
        vrvr = 1.0_dp * rhor_inv

      wr = qnode(n_momz,node2) * rhor_inv
        wrrr = -wr*rhor_inv
        wrwr = 1.0_dp * rhor_inv

      q2r = ur*ur + vr*vr + wr*wr
        q2rrr = 2.0_dp*ur*urrr + 2.0_dp*vr*vrrr + 2.0_dp*wr*wrrr
        q2rur = 2.0_dp*ur*urur
        q2rvr = 2.0_dp*vr*vrvr
        q2rwr = 2.0_dp*wr*wrwr

      enrgyr = qnode(n_etot,node2)
        enrgyrpr = 1.0_dp

      !use pressure jacobian for generic gas path (NOT pg EOS)
      if ( generic ) then
        !mass fractions
        fracr_i(1:ns) = qnode(1:ns,node2)*rhor_inv

        !pressure jacobian
        betar(:) = pressure_jac(:,1,node2)

        pressr = qnode(n_pressure_k(1),node2)
          pressrrr = sum(fracr_i(1:ns)*(betar(1:ns) + 0.5_dp*(betar(ns1)*q2r)))
          pressrur = -ur*betar(ns1)
          pressrvr = -vr*betar(ns1)
          pressrwr = -wr*betar(ns1)
          pressrpr = betar(ns1)
      else !compressible path
        pressr = gm1*(enrgyr - 0.5_dp*rhor*q2r)
          pressrrr = -0.5_dp*gm1*(rhor*q2rrr + q2r*rhorrr)
          pressrur = -0.5_dp*gm1*rhor*q2rur
          pressrvr = -0.5_dp*gm1*rhor*q2rvr
          pressrwr = -0.5_dp*gm1*rhor*q2rwr
          pressrpr = gm1
      end if

      Hr = (enrgyr + pressr)*rhor_inv
        Hrrr = (rhor*(pressrrr) - (enrgyr+pressr)*rhorrr) * rhor_inv * rhor_inv
        Hrur = (rhor*(pressrur)) * rhor_inv * rhor_inv
        Hrvr = (rhor*(pressrvr)) * rhor_inv * rhor_inv
        Hrwr = (rhor*(pressrwr)) * rhor_inv * rhor_inv
        Hrpr = (rhor*(enrgyrpr+pressrpr)) * rhor_inv * rhor_inv

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed
        ubarrrr = xnorm*urrr + ynorm*vrrr + znorm*wrrr
        ubarrur = xnorm*urur
        ubarrvr = ynorm*vrvr
        ubarrwr = znorm*wrwr

! Compute Roe averages

      rho = sqrt(rhol*rhor)
      rho_inv = my_1/rho

      rhorl = 0.5_dp * rho_inv * (rhor*rholrl)
      rhorr = 0.5_dp * rho_inv * (rhol*rhorrr)

      wat = rho/(rho + rhor)
        dwat  = wat*wat*rho_inv*rho_inv
        watrl = rhor*rhorl*dwat
        watrr = (rhor*rhorr-rho*rhorrr)*dwat

      u = ul*wat + ur*(1.0_dp - wat)
        url = ul*watrl + wat*ulrl + ur*(-watrl)
        uul = wat*ulul
        urr = ul*watrr + ur*(-watrr) + (1.0_dp - wat)*urrr
        uur = (1.0_dp - wat)*urur

      v = vl*wat + vr*(1.0_dp - wat)
        vrl = vl*watrl + wat*vlrl + vr*(-watrl)
        vvl = wat*vlvl
        vrr = vl*watrr + vr*(-watrr) + (1.0_dp - wat)*vrrr
        vvr = (1.0_dp - wat)*vrvr

      w = wl*wat + wr*(1.0_dp - wat)
        wrl = wl*watrl + wat*wlrl + wr*(-watrl)
        wwl = wat*wlwl
        wrr = wl*watrr + wr*(-watrr) + (1.0_dp - wat)*wrrr
        wwr = (1.0_dp - wat)*wrwr

      H = Hl*wat + Hr*(1.0_dp - wat)
        Hrl = Hl*watrl + wat*Hlrl + Hr*(-watrl)
        Hul = wat*Hlul
        Hvl = wat*Hlvl
        Hwl = wat*Hlwl
        Hpl = wat*Hlpl

        Hrr = Hl*watrr + Hr*(-watrr) + (1.0_dp - wat)*Hrrr
        Hur = (1.0_dp - wat)*Hrur
        Hvr = (1.0_dp - wat)*Hrvr
        Hwr = (1.0_dp - wat)*Hrwr
        Hpr = (1.0_dp - wat)*Hrpr

      q2 = u*u + v*v + w*w
        q2rl = 2.0_dp*u*url + 2.0_dp*v*vrl + 2.0_dp*w*wrl
        q2ul = 2.0_dp*u*uul
        q2vl = 2.0_dp*v*vvl
        q2wl = 2.0_dp*w*wwl

        q2rr = 2.0_dp*u*urr + 2.0_dp*v*vrr + 2.0_dp*w*wrr
        q2ur = 2.0_dp*u*uur
        q2vr = 2.0_dp*v*vvr
        q2wr = 2.0_dp*w*wwr

      !need to define roe-average pressure derivatives and speed of sound
      if ( generic ) then

!       Pressure weight the pressure jacobian factors
!       This procedure will exactly match perfect gas treatment

        ratio = pressr/pressl
        if(ratio>my_1)then
          wt1 = (my_1/ratio)**8
          wt2 = my_1 - wt1
        else
          wt2 = (ratio)**8
          wt1 = my_1 - wt2
        end if

        !roe_averaged mass fractions
        frac_i(1:ns) = wat*fracl_i(1:ns) + (my_1 - wat)*fracr_i(1:ns)

        !roe-averaged pressure jacobian
        beta(1:n_pjac) = wt1*betal(1:n_pjac) + wt2*betar(1:n_pjac)
        beta(1:ns)     = beta(1:ns) + beta(ns1)*0.5_dp*q2


        !NOT USED (uncomment if desired later)
        !!For the speed of sound derivatives, the pressure derivatives at the
        !!roe-averaged state are needed. Since these are not unique, a scheme
        !!devised by Shuen, Liou, and Van Leer (JCP 90, 371-395 1995) is used.

        !dpress  = pressr - pressl

        !!species energy
        !energyl_j(:) = qnode(n_energy_j(:),node1)
        !energyr_j(:) = qnode(n_energy_j(:),node2)

        !denergy_j = energyr_j(1)-energyl_j(1)
        !drho      = rhor-rhol

        dp_de   = rho*beta(ns1)
        dp_drho = sum(frac_i(1:ns)*beta(1:ns))

        !dp_denom = my_1/max((dp_de*denergy_j)**2 + (dp_drho*drho**2),    &
        !                      1.e-20_dp)
        !p_res    = dpress - dp_de*denergy_j - dp_drho*drho

        !overwrite values with newly weighted values
        !dp_de   = dp_de*(my_1+dp_de*denergy_j*dp_denom*p_res)
        !dp_drho = dp_drho*(my_1+dp_drho*drho*dp_denom*p_res)

        !calculated roe-averaged speed of sound

        c2 =  dp_drho + rho_inv*dp_de*(H-q2)

        !if(n_energy > 1)then
        !  c2 = c2 + sum(e(2:n_energy)*beta(n_species+2:n_pjac))
        !end if
        !if(n_turb_g > 1) c2 = c2 - (beta(ns1)-betat)*turb(1)

        !c2 = max(c2,0.0001_dp*sonic0_k(1)**2)
        !c2 = max(c2,sonic0_k(1)**2)

        c = sqrt(c2)
          c_inv = my_1/c
          crl = 0.5_dp * c_inv * beta(ns1)*(Hrl - 0.5_dp*q2rl)
          cul = 0.5_dp * c_inv * beta(ns1)*(Hul - 0.5_dp*q2ul)
          cvl = 0.5_dp * c_inv * beta(ns1)*(Hvl - 0.5_dp*q2vl)
          cwl = 0.5_dp * c_inv * beta(ns1)*(Hwl - 0.5_dp*q2wl)
          cpl = 0.5_dp * c_inv * beta(ns1)*(Hpl)

          crr = 0.5_dp * c_inv * beta(ns1)*(Hrr - 0.5_dp*q2rr)
          cur = 0.5_dp * c_inv * beta(ns1)*(Hur - 0.5_dp*q2ur)
          cvr = 0.5_dp * c_inv * beta(ns1)*(Hvr - 0.5_dp*q2vr)
          cwr = 0.5_dp * c_inv * beta(ns1)*(Hwr - 0.5_dp*q2wr)
          cpr = 0.5_dp * c_inv * beta(ns1)*(Hpr)

      else
        c = sqrt(gm1*(H - 0.5_dp*q2))
          c_inv = my_1/c
          crl = 0.5_dp * c_inv * gm1*(Hrl - 0.5_dp*q2rl)
          cul = 0.5_dp * c_inv * gm1*(Hul - 0.5_dp*q2ul)
          cvl = 0.5_dp * c_inv * gm1*(Hvl - 0.5_dp*q2vl)
          cwl = 0.5_dp * c_inv * gm1*(Hwl - 0.5_dp*q2wl)
          cpl = 0.5_dp * c_inv * gm1*(Hpl)

          crr = 0.5_dp * c_inv * gm1*(Hrr - 0.5_dp*q2rr)
          cur = 0.5_dp * c_inv * gm1*(Hur - 0.5_dp*q2ur)
          cvr = 0.5_dp * c_inv * gm1*(Hvr - 0.5_dp*q2vr)
          cwr = 0.5_dp * c_inv * gm1*(Hwr - 0.5_dp*q2wr)
          cpr = 0.5_dp * c_inv * gm1*(Hpr)
      end if

      ubar = xnorm*u + ynorm*v + znorm*w - face_speed
        ubarrl = xnorm*url + ynorm*vrl + znorm*wrl
        ubarul = xnorm*uul
        ubarvl = ynorm*vvl
        ubarwl = znorm*wwl

        ubarrr = xnorm*urr + ynorm*vrr + znorm*wrr
        ubarur = xnorm*uur
        ubarvr = ynorm*vvr
        ubarwr = znorm*wwr

! Eigenvalue limiting.  In terms of dimensional equations:
! -limit eigenvalues as fraction of local maximum

      if (adptv_entropy_fix) then

        if (ubar > my_0) then
          dm = my_1
        else
          dm =-my_1
        endif

        dterm = my_1

        scoef   = my_0
!       scoef   = min(my_4th, flux_efixc(n))

!       maximum eigenvalue

        q2l  = ul*ul + vl*vl + wl*wl
        q2r  = ur*ur + vr*vr + wr*wr
        utngl  = sqrt(max(my_0, q2l-ubarl*ubarl))
        utngr  = sqrt(max(my_0, q2r-ubarr*ubarr))
        utang  = utngl*wat + utngr*(my_1 - wat)
        maxeig = max(abs( ubar ), abs( utang )) + c

!       acoustic eigenvalue limiter coefficient

!       aecoef  = min(my_4th, lhs_a_eigenvalue_coef*(my_1-scoef))
        aecoef  = 0.90_dp*min(my_4th, rhs_a_eigenvalue_coef*(my_1-scoef))
        aecoef  = max(0.009_dp, min(my_1, aecoef))

!       convective eigenvalue limiter coefficient

!       cecoef  = min(my_half, lhs_u_eigenvalue_coef*(my_1-scoef))
        cecoef  = 1.10_dp*min(my_half, rhs_u_eigenvalue_coef*(my_1-scoef))
        cecoef  = max(0.000011_dp, min(my_1, cecoef))

      else

        fa    = ubar
        faeps = 0.05_dp*c
        absfa = abs( fa )
        if (absfa < faeps) absfa = my_half*(fa**2/faeps + faeps)

!       maximum eigenvalue

        maxeig = absfa + c

        if (abs(fa) < faeps) then
          dm = fa/faeps
        else if (fa > my_0) then
          dm = my_1
        else
          dm =-my_1
        endif

        dterm = my_1 + my_half*(my_1 - dm**2)*0.05_dp

!       acoustic eigenvalue limiter coefficient

        aecoef  = lhs_a_eigenvalue_coef

!       convective eigenvalue limite coefficient

        cecoef  = lhs_u_eigenvalue_coef

      end if

      maxeigrl = dm*(ubarrl) + crl*dterm
      maxeigul = dm*(ubarul) + cul*dterm
      maxeigvl = dm*(ubarvl) + cvl*dterm
      maxeigwl = dm*(ubarwl) + cwl*dterm
      maxeigpl = cpl*dterm

      maxeigrr = dm*(ubarrr) + crr*dterm
      maxeigur = dm*(ubarur) + cur*dterm
      maxeigvr = dm*(ubarvr) + cvr*dterm
      maxeigwr = dm*(ubarwr) + cwr*dterm
      maxeigpr = cpr*dterm

! Now compute eigenvalues, eigenvectors, and strengths

      eig1 = ubar + c
      eig2 = ubar - c
      eig3 = ubar

! acoustic eigenvalue limiters

      eigeps1 = aecoef*maxeig
      eigeps2 = aecoef*maxeig

! convective eigenvalue limiter

      eigeps3 = cecoef*maxeig

      abseig1 = abs( eig1 )
      abseig2 = abs( eig2 )
      abseig3 = abs( eig3 )

      if(abseig1 < eigeps1) abseig1 = my_half*(eig1**2/eigeps1 + eigeps1)
      if(abseig2 < eigeps2) abseig2 = my_half*(eig2**2/eigeps2 + eigeps2)
      if(abseig3 < eigeps3) abseig3 = my_half*(eig3**2/eigeps3 + eigeps3)

      if(abs(eig1) < eigeps1 ) then
        d1 = eig1/eigeps1
      elseif(eig1 > my_0) then
        d1 = my_1
      else
        d1 =-my_1
      endif

      dterm = my_half*(my_1 - d1**2)*aecoef

        eig1rl = d1*(ubarrl + crl) + dterm*maxeigrl
        eig1ul = d1*(ubarul + cul) + dterm*maxeigul
        eig1vl = d1*(ubarvl + cvl) + dterm*maxeigvl
        eig1wl = d1*(ubarwl + cwl) + dterm*maxeigwl
        eig1pl = d1*(cpl) + dterm*maxeigpl

        eig1rr = d1*(ubarrr + crr) + dterm*maxeigrr
        eig1ur = d1*(ubarur + cur) + dterm*maxeigur
        eig1vr = d1*(ubarvr + cvr) + dterm*maxeigvr
        eig1wr = d1*(ubarwr + cwr) + dterm*maxeigwr
        eig1pr = d1*(cpr) + dterm*maxeigpr

      if(abs(eig2) < eigeps2 ) then
        d2 = eig2/eigeps2
      elseif(eig2 > my_0) then
        d2 = my_1
      else
        d2 =-my_1
      endif

      dterm = my_half*(my_1 - d2**2)*aecoef

        eig2rl = d2*(ubarrl - crl) + dterm*maxeigrl
        eig2ul = d2*(ubarul - cul) + dterm*maxeigul
        eig2vl = d2*(ubarvl - cvl) + dterm*maxeigvl
        eig2wl = d2*(ubarwl - cwl) + dterm*maxeigwl
        eig2pl = d2*(- cpl) + dterm*maxeigpl

        eig2rr = d2*(ubarrr - crr) + dterm*maxeigrr
        eig2ur = d2*(ubarur - cur) + dterm*maxeigur
        eig2vr = d2*(ubarvr - cvr) + dterm*maxeigvr
        eig2wr = d2*(ubarwr - cwr) + dterm*maxeigwr
        eig2pr = d2*(- cpr) + dterm*maxeigpr

      if(abs(eig3) < eigeps3 ) then
        d3 = eig3/eigeps3
      elseif(eig3 > my_0) then
        d3 = my_1
      else
        d3 =-my_1
      endif

      dterm = my_half*(my_1 - d3**2)*cecoef

        eig3rl = d3*ubarrl + dterm*maxeigrl
        eig3ul = d3*ubarul + dterm*maxeigul
        eig3vl = d3*ubarvl + dterm*maxeigvl
        eig3wl = d3*ubarwl + dterm*maxeigwl
        eig3pl = dterm*maxeigpl

        eig3rr = d3*ubarrr + dterm*maxeigrr
        eig3ur = d3*ubarur + dterm*maxeigur
        eig3vr = d3*ubarvr + dterm*maxeigvr
        eig3wr = d3*ubarwr + dterm*maxeigwr
        eig3pr = dterm*maxeigpr

      drho = rhor - rhol
        drhorl = - rholrl
        drhorr = rhorrr

      dpress = pressr - pressl
        dpressrl = - presslrl
        dpressul = - presslul
        dpressvl = - presslvl
        dpresswl = - presslwl
        dpresspl = - presslpl

        dpressrr = pressrrr
        dpressur = pressrur
        dpressvr = pressrvr
        dpresswr = pressrwr
        dpresspr = pressrpr

      du = ur - ul
        durl = - ulrl
        duul = - ulul
        durr = urrr
        duur = urur

      dv = vr - vl
        dvrl = - vlrl
        dvvl = - vlvl
        dvrr = vrrr
        dvvr = vrvr

      dw = wr - wl
        dwrl = - wlrl
        dwwl = - wlwl
        dwrr = wrrr
        dwwr = wrwr

      dubar = ubarr - ubarl
        dubarrl = - ubarlrl
        dubarul = - ubarlul
        dubarvl = - ubarlvl
        dubarwl = - ubarlwl

        dubarrr = ubarrrr
        dubarur = ubarrur
        dubarvr = ubarrvr
        dubarwr = ubarrwr

      c2 = c*c
        c2_inv = my_1/c2
        c22_inv = c2_inv*c2_inv
        c2rl = 2.0_dp * c * crl
        c2ul = 2.0_dp * c * cul
        c2vl = 2.0_dp * c * cvl
        c2wl = 2.0_dp * c * cwl
        c2pl = 2.0_dp * c * cpl

        c2rr = 2.0_dp * c * crr
        c2ur = 2.0_dp * c * cur
        c2vr = 2.0_dp * c * cvr
        c2wr = 2.0_dp * c * cwr
        c2pr = 2.0_dp * c * cpr

! jumps have units of density

      dv1 = 0.5_dp*(dpress + rho*c*dubar)*c2_inv
        dv1rl = 0.5_dp*(c2*(dpressrl + rho*(c*dubarrl + dubar*crl)         &
                + c*dubar*rhorl) - (dpress + rho*c*dubar)*c2rl) * c22_inv
        dv1ul = 0.5_dp*(c2*(dpressul + rho*(c*dubarul + dubar*cul))        &
                - (dpress + rho*c*dubar)*c2ul) * c22_inv
        dv1vl = 0.5_dp*(c2*(dpressvl + rho*(c*dubarvl + dubar*cvl))        &
                - (dpress + rho*c*dubar)*c2vl) * c22_inv
        dv1wl = 0.5_dp*(c2*(dpresswl + rho*(c*dubarwl + dubar*cwl))        &
                - (dpress + rho*c*dubar)*c2wl) * c22_inv
        dv1pl = 0.5_dp*(c2*(dpresspl + rho*(dubar*cpl))                    &
                - (dpress + rho*c*dubar)*c2pl) * c22_inv

        dv1rr = 0.5_dp*(c2*(dpressrr + rho*(c*dubarrr + dubar*crr)         &
                + c*dubar*rhorr) - (dpress + rho*c*dubar)*c2rr)            &
                * c22_inv
        dv1ur = 0.5_dp*(c2*(dpressur + rho*(c*dubarur + dubar*cur))        &
                - (dpress + rho*c*dubar)*c2ur) * c22_inv
        dv1vr = 0.5_dp*(c2*(dpressvr + rho*(c*dubarvr + dubar*cvr))        &
                - (dpress + rho*c*dubar)*c2vr) * c22_inv
        dv1wr = 0.5_dp*(c2*(dpresswr + rho*(c*dubarwr + dubar*cwr))        &
                - (dpress + rho*c*dubar)*c2wr) * c22_inv
        dv1pr = 0.5_dp*(c2*(dpresspr + rho*(dubar*cpr))                    &
                - (dpress + rho*c*dubar)*c2pr) * c22_inv

      dv2 = 0.5_dp*(dpress - rho*c*dubar)/c2
        dv2rl = 0.5_dp*(c2*(dpressrl - rho*(c*dubarrl + dubar*crl)         &
                - c*dubar*rhorl) - (dpress - rho*c*dubar)*c2rl)            &
                * c22_inv
        dv2ul = 0.5_dp*(c2*(dpressul - rho*(c*dubarul + dubar*cul))        &
                - (dpress - rho*c*dubar)*c2ul) * c22_inv
        dv2vl = 0.5_dp*(c2*(dpressvl - rho*(c*dubarvl + dubar*cvl))        &
                - (dpress - rho*c*dubar)*c2vl) * c22_inv
        dv2wl = 0.5_dp*(c2*(dpresswl - rho*(c*dubarwl + dubar*cwl))        &
                - (dpress - rho*c*dubar)*c2wl) * c22_inv
        dv2pl = 0.5_dp*(c2*(dpresspl - rho*(dubar*cpl))                    &
                - (dpress - rho*c*dubar)*c2pl) * c22_inv

        dv2rr = 0.5_dp*(c2*(dpressrr - rho*(c*dubarrr + dubar*crr)         &
                - c*dubar*rhorr) - (dpress - rho*c*dubar)*c2rr)            &
                * c22_inv
        dv2ur = 0.5_dp*(c2*(dpressur - rho*(c*dubarur + dubar*cur))        &
                - (dpress - rho*c*dubar)*c2ur) * c22_inv
        dv2vr = 0.5_dp*(c2*(dpressvr - rho*(c*dubarvr + dubar*cvr))        &
                - (dpress - rho*c*dubar)*c2vr) * c22_inv
        dv2wr = 0.5_dp*(c2*(dpresswr - rho*(c*dubarwr + dubar*cwr))        &
                - (dpress - rho*c*dubar)*c2wr) * c22_inv
        dv2pr = 0.5_dp*(c2*(dpresspr - rho*(dubar*cpr))                    &
                - (dpress - rho*c*dubar)*c2pr) * c22_inv

      dv3 = rho
        dv3rl = rhorl
        dv3rr = rhorr

      dv4 = (c*c*drho - dpress)/c2
        dv4rl = (c2*((c*(c*drhorl+drho*crl)+c*drho*crl) - dpressrl)        &
                - (c*c*drho - dpress)*c2rl) * c22_inv
        dv4ul = (c2*((c*(drho*cul)+c*drho*cul) - dpressul)                 &
                - (c*c*drho - dpress)*c2ul) * c22_inv
        dv4vl = (c2*((c*(drho*cvl)+c*drho*cvl) - dpressvl)                 &
                - (c*c*drho - dpress)*c2vl) * c22_inv
        dv4wl = (c2*((c*(drho*cwl)+c*drho*cwl) - dpresswl)                 &
                - (c*c*drho - dpress)*c2wl) * c22_inv
        dv4pl = (c2*((c*(drho*cpl)+c*drho*cpl) - dpresspl)                 &
                - (c*c*drho - dpress)*c2pl) * c22_inv

        dv4rr = (c2*((c*(c*drhorr+drho*crr)+c*drho*crr) - dpressrr)        &
                - (c*c*drho - dpress)*c2rr) * c22_inv
        dv4ur = (c2*((c*(drho*cur)+c*drho*cur) - dpressur)                 &
                - (c*c*drho - dpress)*c2ur) * c22_inv
        dv4vr = (c2*((c*(drho*cvr)+c*drho*cvr) - dpressvr)                 &
                - (c*c*drho - dpress)*c2vr) * c22_inv
        dv4wr = (c2*((c*(drho*cwr)+c*drho*cwr) - dpresswr)                 &
                - (c*c*drho - dpress)*c2wr) * c22_inv
        dv4pr = (c2*((c*(drho*cpr)+c*drho*cpr) - dpresspr)                 &
                - (c*c*drho - dpress)*c2pr) * c22_inv

      r21 = u + c*xnorm
        r21rl = url + xnorm*crl
        r21ul = uul + xnorm*cul
        r21vl = xnorm*cvl
        r21wl = xnorm*cwl
        r21pl = xnorm*cpl

        r21rr = urr + xnorm*crr
        r21ur = uur + xnorm*cur
        r21vr = xnorm*cvr
        r21wr = xnorm*cwr
        r21pr = xnorm*cpr

      r31 = v + c*ynorm
        r31rl = vrl + ynorm*crl
        r31ul = ynorm*cul
        r31vl = vvl + ynorm*cvl
        r31wl = ynorm*cwl
        r31pl = ynorm*cpl

        r31rr = vrr + ynorm*crr
        r31ur = ynorm*cur
        r31vr = vvr + ynorm*cvr
        r31wr = ynorm*cwr
        r31pr = ynorm*cpr

      r41 = w + c*znorm
        r41rl = wrl + znorm*crl
        r41ul = znorm*cul
        r41vl = znorm*cvl
        r41wl = wwl + znorm*cwl
        r41pl = znorm*cpl

        r41rr = wrr + znorm*crr
        r41ur = znorm*cur
        r41vr = znorm*cvr
        r41wr = wwr + znorm*cwr
        r41pr = znorm*cpr

      r51 = H + c*(ubar+face_speed)
        r51rl = Hrl + c*ubarrl + (ubar+face_speed)*crl
        r51ul = Hul + c*ubarul + (ubar+face_speed)*cul
        r51vl = Hvl + c*ubarvl + (ubar+face_speed)*cvl
        r51wl = Hwl + c*ubarwl + (ubar+face_speed)*cwl
        r51pl = Hpl + (ubar+face_speed)*cpl

        r51rr = Hrr + c*ubarrr + (ubar+face_speed)*crr
        r51ur = Hur + c*ubarur + (ubar+face_speed)*cur
        r51vr = Hvr + c*ubarvr + (ubar+face_speed)*cvr
        r51wr = Hwr + c*ubarwr + (ubar+face_speed)*cwr
        r51pr = Hpr + (ubar+face_speed)*cpr

      r22 = u - c*xnorm
        r22rl = url - xnorm*crl
        r22ul = uul - xnorm*cul
        r22vl = - xnorm*cvl
        r22wl = - xnorm*cwl
        r22pl = - xnorm*cpl

        r22rr = urr - xnorm*crr
        r22ur = uur - xnorm*cur
        r22vr = - xnorm*cvr
        r22wr = - xnorm*cwr
        r22pr = - xnorm*cpr

      r32 = v - c*ynorm
        r32rl = vrl - ynorm*crl
        r32ul = - ynorm*cul
        r32vl = vvl - ynorm*cvl
        r32wl = - ynorm*cwl
        r32pl = - ynorm*cpl

        r32rr = vrr - ynorm*crr
        r32ur = - ynorm*cur
        r32vr = vvr - ynorm*cvr
        r32wr = - ynorm*cwr
        r32pr = - ynorm*cpr

      r42 = w - c*znorm
        r42rl = wrl - znorm*crl
        r42ul = - znorm*cul
        r42vl = - znorm*cvl
        r42wl = wwl - znorm*cwl
        r42pl = - znorm*cpl

        r42rr = wrr - znorm*crr
        r42ur = - znorm*cur
        r42vr = - znorm*cvr
        r42wr = wwr - znorm*cwr
        r42pr = - znorm*cpr

      r52 = H - c*(ubar+face_speed)
        r52rl = Hrl - c*ubarrl - (ubar+face_speed)*crl
        r52ul = Hul - c*ubarul - (ubar+face_speed)*cul
        r52vl = Hvl - c*ubarvl - (ubar+face_speed)*cvl
        r52wl = Hwl - c*ubarwl - (ubar+face_speed)*cwl
        r52pl = Hpl - (ubar+face_speed)*cpl

        r52rr = Hrr - c*ubarrr - (ubar+face_speed)*crr
        r52ur = Hur - c*ubarur - (ubar+face_speed)*cur
        r52vr = Hvr - c*ubarvr - (ubar+face_speed)*cvr
        r52wr = Hwr - c*ubarwr - (ubar+face_speed)*cwr
        r52pr = Hpr - (ubar+face_speed)*cpr

      r23 = du - dubar*xnorm
        r23rl = durl - xnorm*dubarrl
        r23ul = duul - xnorm*dubarul
        r23vl = - xnorm*dubarvl
        r23wl = - xnorm*dubarwl

        r23rr = durr - xnorm*dubarrr
        r23ur = duur - xnorm*dubarur
        r23vr = - xnorm*dubarvr
        r23wr = - xnorm*dubarwr

      r33 = dv - dubar*ynorm
        r33rl = dvrl - ynorm*dubarrl
        r33ul = - ynorm*dubarul
        r33vl = dvvl - ynorm*dubarvl
        r33wl = - ynorm*dubarwl

        r33rr = dvrr - ynorm*dubarrr
        r33ur = - ynorm*dubarur
        r33vr = dvvr - ynorm*dubarvr
        r33wr = - ynorm*dubarwr

      r43 = dw - dubar*znorm
        r43rl = dwrl - znorm*dubarrl
        r43ul = - znorm*dubarul
        r43vl = - znorm*dubarvl
        r43wl = dwwl - znorm*dubarwl

        r43rr = dwrr - znorm*dubarrr
        r43ur = - znorm*dubarur
        r43vr = - znorm*dubarvr
        r43wr = dwwr - znorm*dubarwr

      r53 = u*du + v*dv + w*dw - (ubar+face_speed)*dubar
        r53rl = u*durl+du*url + v*dvrl+dv*vrl + w*dwrl+dw*wrl              &
                - (ubar+face_speed)*dubarrl - dubar*ubarrl
        r53ul = u*duul+du*uul - (ubar+face_speed)*dubarul - dubar*ubarul
        r53vl = v*dvvl+dv*vvl - (ubar+face_speed)*dubarvl - dubar*ubarvl
        r53wl = w*dwwl+dw*wwl - (ubar+face_speed)*dubarwl - dubar*ubarwl

        r53rr = u*durr+du*urr + v*dvrr+dv*vrr + w*dwrr+dw*wrr              &
                - (ubar+face_speed)*dubarrr - dubar*ubarrr
        r53ur = u*duur+du*uur - (ubar+face_speed)*dubarur - dubar*ubarur
        r53vr = v*dvvr+dv*vvr - (ubar+face_speed)*dubarvr - dubar*ubarvr
        r53wr = w*dwwr+dw*wwr - (ubar+face_speed)*dubarwr - dubar*ubarwr

      r24 = u
        r24rl = url
        r24ul = uul
        r24rr = urr
        r24ur = uur

      r34 = v
        r34rl = vrl
        r34vl = vvl
        r34rr = vrr
        r34vr = vvr

      r44 = w
        r44rl = wrl
        r44wl = wwl
        r44rr = wrr
        r44wr = wwr

      r54 = q2 - sum(frac_i(1:ns)*beta(1:ns))/beta(ns1)

      !r54 = 0.5_dp*q2
        r54rl = 0.5_dp*q2rl
        r54ul = 0.5_dp*q2ul
        r54vl = 0.5_dp*q2vl
        r54wl = 0.5_dp*q2wl

        r54rr = 0.5_dp*q2rr
        r54ur = 0.5_dp*q2ur
        r54vr = 0.5_dp*q2vr
        r54wr = 0.5_dp*q2wr

!           t1 = abseig1*dv1     + abseig2*dv2                                 &
!                                + abseig3*dv4

        t1rl = abseig1*dv1rl+dv1*eig1rl + abseig2*dv2rl+dv2*eig2rl         &
             + abseig3*dv4rl+dv4*eig3rl
        t1ul = abseig1*dv1ul+dv1*eig1ul + abseig2*dv2ul+dv2*eig2ul         &
             + abseig3*dv4ul+dv4*eig3ul
        t1vl = abseig1*dv1vl+dv1*eig1vl + abseig2*dv2vl+dv2*eig2vl         &
             + abseig3*dv4vl+dv4*eig3vl
        t1wl = abseig1*dv1wl+dv1*eig1wl + abseig2*dv2wl+dv2*eig2wl         &
             + abseig3*dv4wl+dv4*eig3wl
        t1pl = abseig1*dv1pl+dv1*eig1pl + abseig2*dv2pl+dv2*eig2pl         &
             + abseig3*dv4pl+dv4*eig3pl

        t1rr = abseig1*dv1rr+dv1*eig1rr + abseig2*dv2rr+dv2*eig2rr         &
             + abseig3*dv4rr+dv4*eig3rr
        t1ur = abseig1*dv1ur+dv1*eig1ur + abseig2*dv2ur+dv2*eig2ur         &
             + abseig3*dv4ur+dv4*eig3ur
        t1vr = abseig1*dv1vr+dv1*eig1vr + abseig2*dv2vr+dv2*eig2vr         &
             + abseig3*dv4vr+dv4*eig3vr
        t1wr = abseig1*dv1wr+dv1*eig1wr + abseig2*dv2wr+dv2*eig2wr         &
             + abseig3*dv4wr+dv4*eig3wr
        t1pr = abseig1*dv1pr+dv1*eig1pr + abseig2*dv2pr+dv2*eig2pr         &
             + abseig3*dv4pr+dv4*eig3pr

!           t2 = abseig1*r21*dv1 + abseig2*r22*dv2                             &
!              + abseig3*r23*dv3 + abseig3*r24*dv4

        t2rl = abseig1*(r21*dv1rl+dv1*r21rl)+r21*dv1*eig1rl                &
             + abseig2*(r22*dv2rl+dv2*r22rl)+r22*dv2*eig2rl                &
             + abseig3*(r23*dv3rl+dv3*r23rl)+r23*dv3*eig3rl                &
             + abseig3*(r24*dv4rl+dv4*r24rl)+r24*dv4*eig3rl

        t2ul = abseig1*(r21*dv1ul+dv1*r21ul)+r21*dv1*eig1ul                &
             + abseig2*(r22*dv2ul+dv2*r22ul)+r22*dv2*eig2ul                &
             + abseig3*(dv3*r23ul)+r23*dv3*eig3ul                          &
             + abseig3*(r24*dv4ul+dv4*r24ul)+r24*dv4*eig3ul

        t2vl = abseig1*(r21*dv1vl+dv1*r21vl)+r21*dv1*eig1vl                &
             + abseig2*(r22*dv2vl+dv2*r22vl)+r22*dv2*eig2vl                &
             + abseig3*(dv3*r23vl)+r23*dv3*eig3vl                          &
             + abseig3*(r24*dv4vl)+r24*dv4*eig3vl

        t2wl = abseig1*(r21*dv1wl+dv1*r21wl)+r21*dv1*eig1wl                &
             + abseig2*(r22*dv2wl+dv2*r22wl)+r22*dv2*eig2wl                &
             + abseig3*(dv3*r23wl)+r23*dv3*eig3wl                          &
             + abseig3*(r24*dv4wl)+r24*dv4*eig3wl

        t2pl = abseig1*(r21*dv1pl+dv1*r21pl)+r21*dv1*eig1pl                &
             + abseig2*(r22*dv2pl+dv2*r22pl)+r22*dv2*eig2pl                &
             +r23*dv3*eig3pl + abseig3*(r24*dv4pl)+r24*dv4*eig3pl

        t2rr = abseig1*(r21*dv1rr+dv1*r21rr)+r21*dv1*eig1rr                &
             + abseig2*(r22*dv2rr+dv2*r22rr)+r22*dv2*eig2rr                &
             + abseig3*(r23*dv3rr+dv3*r23rr)+r23*dv3*eig3rr                &
             + abseig3*(r24*dv4rr+dv4*r24rr)+r24*dv4*eig3rr

        t2ur = abseig1*(r21*dv1ur+dv1*r21ur)+r21*dv1*eig1ur                &
             + abseig2*(r22*dv2ur+dv2*r22ur)+r22*dv2*eig2ur                &
             + abseig3*(dv3*r23ur)+r23*dv3*eig3ur                          &
             + abseig3*(r24*dv4ur+dv4*r24ur)+r24*dv4*eig3ur

        t2vr = abseig1*(r21*dv1vr+dv1*r21vr)+r21*dv1*eig1vr                &
             + abseig2*(r22*dv2vr+dv2*r22vr)+r22*dv2*eig2vr                &
             + abseig3*(dv3*r23vr)+r23*dv3*eig3vr                          &
             + abseig3*(r24*dv4vr)+r24*dv4*eig3vr

        t2wr = abseig1*(r21*dv1wr+dv1*r21wr)+r21*dv1*eig1wr                &
             + abseig2*(r22*dv2wr+dv2*r22wr)+r22*dv2*eig2wr                &
             + abseig3*(dv3*r23wr)+r23*dv3*eig3wr                          &
             + abseig3*(r24*dv4wr)+r24*dv4*eig3wr

        t2pr = abseig1*(r21*dv1pr+dv1*r21pr)+r21*dv1*eig1pr                &
             + abseig2*(r22*dv2pr+dv2*r22pr)+r22*dv2*eig2pr                &
             +r23*dv3*eig3pr + abseig3*(r24*dv4pr)+r24*dv4*eig3pr

!           t3 = abseig1*r31*dv1 + abseig2*r32*dv2                             &
!              + abseig3*r33*dv3 + abseig3*r34*dv4

        t3rl = abseig1*(r31*dv1rl+dv1*r31rl)+r31*dv1*eig1rl                &
             + abseig2*(r32*dv2rl+dv2*r32rl)+r32*dv2*eig2rl                &
             + abseig3*(r33*dv3rl+dv3*r33rl)+r33*dv3*eig3rl                &
             + abseig3*(r34*dv4rl+dv4*r34rl)+r34*dv4*eig3rl

        t3ul = abseig1*(r31*dv1ul+dv1*r31ul)+r31*dv1*eig1ul                &
             + abseig2*(r32*dv2ul+dv2*r32ul)+r32*dv2*eig2ul                &
             + abseig3*(dv3*r33ul)+r33*dv3*eig3ul                          &
             + abseig3*(r34*dv4ul)+r34*dv4*eig3ul

        t3vl = abseig1*(r31*dv1vl+dv1*r31vl)+r31*dv1*eig1vl                &
             + abseig2*(r32*dv2vl+dv2*r32vl)+r32*dv2*eig2vl                &
             + abseig3*(dv3*r33vl)+r33*dv3*eig3vl                          &
             + abseig3*(r34*dv4vl+dv4*r34vl)+r34*dv4*eig3vl

        t3wl = abseig1*(r31*dv1wl+dv1*r31wl)+r31*dv1*eig1wl                &
             + abseig2*(r32*dv2wl+dv2*r32wl)+r32*dv2*eig2wl                &
             + abseig3*(dv3*r33wl)+r33*dv3*eig3wl                          &
             + abseig3*(r34*dv4wl)+r34*dv4*eig3wl

        t3pl = abseig1*(r31*dv1pl+dv1*r31pl)+r31*dv1*eig1pl                &
             + abseig2*(r32*dv2pl+dv2*r32pl)+r32*dv2*eig2pl                &
             +r33*dv3*eig3pl + abseig3*(r34*dv4pl)+r34*dv4*eig3pl

        t3rr = abseig1*(r31*dv1rr+dv1*r31rr)+r31*dv1*eig1rr                &
             + abseig2*(r32*dv2rr+dv2*r32rr)+r32*dv2*eig2rr                &
             + abseig3*(r33*dv3rr+dv3*r33rr)+r33*dv3*eig3rr                &
             + abseig3*(r34*dv4rr+dv4*r34rr)+r34*dv4*eig3rr

        t3ur = abseig1*(r31*dv1ur+dv1*r31ur)+r31*dv1*eig1ur                &
             + abseig2*(r32*dv2ur+dv2*r32ur)+r32*dv2*eig2ur                &
             + abseig3*(dv3*r33ur)+r33*dv3*eig3ur                          &
             + abseig3*(r34*dv4ur)+r34*dv4*eig3ur

        t3vr = abseig1*(r31*dv1vr+dv1*r31vr)+r31*dv1*eig1vr                &
             + abseig2*(r32*dv2vr+dv2*r32vr)+r32*dv2*eig2vr                &
             + abseig3*(dv3*r33vr)+r33*dv3*eig3vr                          &
             + abseig3*(r34*dv4vr+dv4*r34vr)+r34*dv4*eig3vr

        t3wr = abseig1*(r31*dv1wr+dv1*r31wr)+r31*dv1*eig1wr                &
             + abseig2*(r32*dv2wr+dv2*r32wr)+r32*dv2*eig2wr                &
             + abseig3*(dv3*r33wr)+r33*dv3*eig3wr                          &
             + abseig3*(r34*dv4wr)+r34*dv4*eig3wr

        t3pr = abseig1*(r31*dv1pr+dv1*r31pr)+r31*dv1*eig1pr                &
             + abseig2*(r32*dv2pr+dv2*r32pr)+r32*dv2*eig2pr                &
             +r33*dv3*eig3pr + abseig3*(r34*dv4pr)+r34*dv4*eig3pr

!           t4 = abseig1*r41*dv1 + abseig2*r42*dv2                             &
!              + abseig3*r43*dv3 + abseig3*r44*dv4

        t4rl = abseig1*(r41*dv1rl+dv1*r41rl)+r41*dv1*eig1rl                &
             + abseig2*(r42*dv2rl+dv2*r42rl)+r42*dv2*eig2rl                &
             + abseig3*(r43*dv3rl+dv3*r43rl)+r43*dv3*eig3rl                &
             + abseig3*(r44*dv4rl+dv4*r44rl)+r44*dv4*eig3rl

        t4ul = abseig1*(r41*dv1ul+dv1*r41ul)+r41*dv1*eig1ul                &
             + abseig2*(r42*dv2ul+dv2*r42ul)+r42*dv2*eig2ul                &
             + abseig3*(dv3*r43ul)+r43*dv3*eig3ul                          &
             + abseig3*(r44*dv4ul)+r44*dv4*eig3ul

        t4vl = abseig1*(r41*dv1vl+dv1*r41vl)+r41*dv1*eig1vl                &
             + abseig2*(r42*dv2vl+dv2*r42vl)+r42*dv2*eig2vl                &
             + abseig3*(dv3*r43vl)+r43*dv3*eig3vl                          &
             + abseig3*(r44*dv4vl)+r44*dv4*eig3vl

        t4wl = abseig1*(r41*dv1wl+dv1*r41wl)+r41*dv1*eig1wl                &
             + abseig2*(r42*dv2wl+dv2*r42wl)+r42*dv2*eig2wl                &
             + abseig3*(dv3*r43wl)+r43*dv3*eig3wl                          &
             + abseig3*(r44*dv4wl+dv4*r44wl)+r44*dv4*eig3wl

        t4pl = abseig1*(r41*dv1pl+dv1*r41pl)+r41*dv1*eig1pl                &
             + abseig2*(r42*dv2pl+dv2*r42pl)+r42*dv2*eig2pl                &
             +r43*dv3*eig3pl + abseig3*(r44*dv4pl)+r44*dv4*eig3pl

        t4rr = abseig1*(r41*dv1rr+dv1*r41rr)+r41*dv1*eig1rr                &
             + abseig2*(r42*dv2rr+dv2*r42rr)+r42*dv2*eig2rr                &
             + abseig3*(r43*dv3rr+dv3*r43rr)+r43*dv3*eig3rr                &
             + abseig3*(r44*dv4rr+dv4*r44rr)+r44*dv4*eig3rr

        t4ur = abseig1*(r41*dv1ur+dv1*r41ur)+r41*dv1*eig1ur                &
             + abseig2*(r42*dv2ur+dv2*r42ur)+r42*dv2*eig2ur                &
             + abseig3*(dv3*r43ur)+r43*dv3*eig3ur                          &
             + abseig3*(r44*dv4ur)+r44*dv4*eig3ur

        t4vr = abseig1*(r41*dv1vr+dv1*r41vr)+r41*dv1*eig1vr                &
             + abseig2*(r42*dv2vr+dv2*r42vr)+r42*dv2*eig2vr                &
             + abseig3*(dv3*r43vr)+r43*dv3*eig3vr                          &
             + abseig3*(r44*dv4vr)+r44*dv4*eig3vr

        t4wr = abseig1*(r41*dv1wr+dv1*r41wr)+r41*dv1*eig1wr                &
             + abseig2*(r42*dv2wr+dv2*r42wr)+r42*dv2*eig2wr                &
             + abseig3*(dv3*r43wr)+r43*dv3*eig3wr                          &
             + abseig3*(r44*dv4wr+dv4*r44wr)+r44*dv4*eig3wr

        t4pr = abseig1*(r41*dv1pr+dv1*r41pr)+r41*dv1*eig1pr                &
             + abseig2*(r42*dv2pr+dv2*r42pr)+r42*dv2*eig2pr                &
             +r43*dv3*eig3pr + abseig3*(r44*dv4pr)+r44*dv4*eig3pr

!           t5 = abseig1*r51*dv1 + abseig2*r52*dv2                             &
!              + abseig3*r53*dv3 + abseig3*r54*dv4

        t5rl = abseig1*(r51*dv1rl+dv1*r51rl)+r51*dv1*eig1rl                &
             + abseig2*(r52*dv2rl+dv2*r52rl)+r52*dv2*eig2rl                &
             + abseig3*(r53*dv3rl+dv3*r53rl)+r53*dv3*eig3rl                &
             + abseig3*(r54*dv4rl+dv4*r54rl)+r54*dv4*eig3rl

        t5ul = abseig1*(r51*dv1ul+dv1*r51ul)+r51*dv1*eig1ul                &
             + abseig2*(r52*dv2ul+dv2*r52ul)+r52*dv2*eig2ul                &
             + abseig3*(dv3*r53ul)+r53*dv3*eig3ul                          &
             + abseig3*(r54*dv4ul+dv4*r54ul)+r54*dv4*eig3ul

        t5vl = abseig1*(r51*dv1vl+dv1*r51vl)+r51*dv1*eig1vl                &
             + abseig2*(r52*dv2vl+dv2*r52vl)+r52*dv2*eig2vl                &
             + abseig3*(dv3*r53vl)+r53*dv3*eig3vl                          &
             + abseig3*(r54*dv4vl+dv4*r54vl)+r54*dv4*eig3vl

        t5wl = abseig1*(r51*dv1wl+dv1*r51wl)+r51*dv1*eig1wl                &
             + abseig2*(r52*dv2wl+dv2*r52wl)+r52*dv2*eig2wl                &
             + abseig3*(dv3*r53wl)+r53*dv3*eig3wl                          &
             + abseig3*(r54*dv4wl+dv4*r54wl)+r54*dv4*eig3wl

        t5pl = abseig1*(r51*dv1pl+dv1*r51pl)+r51*dv1*eig1pl                &
             + abseig2*(r52*dv2pl+dv2*r52pl)+r52*dv2*eig2pl                &
             +r53*dv3*eig3pl + abseig3*(r54*dv4pl)+r54*dv4*eig3pl
        t5rr = abseig1*(r51*dv1rr+dv1*r51rr)+r51*dv1*eig1rr                &
             + abseig2*(r52*dv2rr+dv2*r52rr)+r52*dv2*eig2rr                &
             + abseig3*(r53*dv3rr+dv3*r53rr)+r53*dv3*eig3rr                &
             + abseig3*(r54*dv4rr+dv4*r54rr)+r54*dv4*eig3rr

        t5ur = abseig1*(r51*dv1ur+dv1*r51ur)+r51*dv1*eig1ur                &
             + abseig2*(r52*dv2ur+dv2*r52ur)+r52*dv2*eig2ur                &
             + abseig3*(dv3*r53ur)+r53*dv3*eig3ur                          &
             + abseig3*(r54*dv4ur+dv4*r54ur)+r54*dv4*eig3ur

        t5vr = abseig1*(r51*dv1vr+dv1*r51vr)+r51*dv1*eig1vr                &
             + abseig2*(r52*dv2vr+dv2*r52vr)+r52*dv2*eig2vr                &
             + abseig3*(dv3*r53vr)+r53*dv3*eig3vr                          &
             + abseig3*(r54*dv4vr+dv4*r54vr)+r54*dv4*eig3vr

        t5wr = abseig1*(r51*dv1wr+dv1*r51wr)+r51*dv1*eig1wr                &
             + abseig2*(r52*dv2wr+dv2*r52wr)+r52*dv2*eig2wr                &
             + abseig3*(dv3*r53wr)+r53*dv3*eig3wr                          &
             + abseig3*(r54*dv4wr+dv4*r54wr)+r54*dv4*eig3wr

        t5pr = abseig1*(r51*dv1pr+dv1*r51pr)+r51*dv1*eig1pr                &
             + abseig2*(r52*dv2pr+dv2*r52pr)+r52*dv2*eig2pr                &
             +r53*dv3*eig3pr + abseig3*(r54*dv4pr)+r54*dv4*eig3pr

! Compute flux using variables from left side of face

!           fluxp1 = area*rhol*ubarl

        fluxp1rl = area*(rhol*ubarlrl + ubarl*rholrl)
        fluxp1ul = area*(rhol*ubarlul)
        fluxp1vl = area*(rhol*ubarlvl)
        fluxp1wl = area*(rhol*ubarlwl)

!           fluxp2 = area*(rhol*ul*ubarl + xnorm*pressl)

        fluxp2rl = area*(rhol*(ul*ubarlrl+ubarl*ulrl) +                    &
                   ul*ubarl*rholrl + xnorm*presslrl)
        fluxp2ul = area*(rhol*(ul*ubarlul+ubarl*ulul) + xnorm*presslul)
        fluxp2vl = area*(rhol*(ul*ubarlvl) + xnorm*presslvl)
        fluxp2wl = area*(rhol*(ul*ubarlwl) + xnorm*presslwl)
        fluxp2pl = area*(xnorm*presslpl)

!           fluxp3 = area*(rhol*vl*ubarl + ynorm*pressl)

        fluxp3rl = area*(rhol*(vl*ubarlrl+ubarl*vlrl) +                    &
                   vl*ubarl*rholrl + ynorm*presslrl)
        fluxp3ul = area*(rhol*(vl*ubarlul) + ynorm*presslul)
        fluxp3vl = area*(rhol*(vl*ubarlvl+ubarl*vlvl) + ynorm*presslvl)
        fluxp3wl = area*(rhol*(vl*ubarlwl) + ynorm*presslwl)
        fluxp3pl = area*(ynorm*presslpl)

!           fluxp4 = area*(rhol*wl*ubarl + znorm*pressl)

        fluxp4rl = area*(rhol*(wl*ubarlrl+ubarl*wlrl) +                    &
                   wl*ubarl*rholrl + znorm*presslrl)
        fluxp4ul = area*(rhol*(wl*ubarlul) + znorm*presslul)
        fluxp4vl = area*(rhol*(wl*ubarlvl) + znorm*presslvl)
        fluxp4wl = area*(rhol*(wl*ubarlwl+ubarl*wlwl) + znorm*presslwl)
        fluxp4pl = area*(znorm*presslpl)

!           fluxp5 = area*(enrgyl + pressl)*ubarl + area*face_speed*pressl

        fluxp5rl = area*((enrgyl + pressl)*ubarlrl +                       &
                   ubarl*(presslrl)) + area*face_speed*presslrl
        fluxp5ul = area*((enrgyl + pressl)*ubarlul +                       &
                   ubarl*(presslul)) + area*face_speed*presslul
        fluxp5vl = area*((enrgyl + pressl)*ubarlvl +                       &
                   ubarl*(presslvl)) + area*face_speed*presslvl
        fluxp5wl = area*((enrgyl + pressl)*ubarlwl +                       &
                   ubarl*(presslwl)) + area*face_speed*presslwl
        fluxp5pl = area*(ubarl*(enrgylpl+presslpl))+area*face_speed*presslpl

! Now the right side

!           fluxm1 = area*rhor*ubarr

        fluxm1rr = area*(rhor*ubarrrr + ubarr*rhorrr)
        fluxm1ur = area*(rhor*ubarrur)
        fluxm1vr = area*(rhor*ubarrvr)
        fluxm1wr = area*(rhor*ubarrwr)

!           fluxm2 = area*(rhor*ur*ubarr + xnorm*pressr)

        fluxm2rr = area*(rhor*(ur*ubarrrr+ubarr*urrr) +                    &
                   ur*ubarr*rhorrr + xnorm*pressrrr)
        fluxm2ur = area*(rhor*(ur*ubarrur+ubarr*urur) + xnorm*pressrur)
        fluxm2vr = area*(rhor*(ur*ubarrvr) + xnorm*pressrvr)
        fluxm2wr = area*(rhor*(ur*ubarrwr) + xnorm*pressrwr)
        fluxm2pr = area*(xnorm*pressrpr)

!           fluxm3 = area*(rhor*vr*ubarr + ynorm*pressr)

        fluxm3rr = area*(rhor*(vr*ubarrrr+ubarr*vrrr) +                    &
                   vr*ubarr*rhorrr + ynorm*pressrrr)
        fluxm3ur = area*(rhor*(vr*ubarrur) + ynorm*pressrur)
        fluxm3vr = area*(rhor*(vr*ubarrvr+ubarr*vrvr) + ynorm*pressrvr)
        fluxm3wr = area*(rhor*(vr*ubarrwr) + ynorm*pressrwr)
        fluxm3pr = area*(ynorm*pressrpr)

!           fluxm4 = area*(rhor*wr*ubarr + znorm*pressr)

        fluxm4rr = area*(rhor*(wr*ubarrrr+ubarr*wrrr) +                    &
                   wr*ubarr*rhorrr + znorm*pressrrr)
        fluxm4ur = area*(rhor*(wr*ubarrur) + znorm*pressrur)
        fluxm4vr = area*(rhor*(wr*ubarrvr) + znorm*pressrvr)
        fluxm4wr = area*(rhor*(wr*ubarrwr+ubarr*wrwr) + znorm*pressrwr)
        fluxm4pr = area*(znorm*pressrpr)

!           fluxm5 = area*(enrgyr + pressr)*ubarr + area*face_speed*pressr

        fluxm5rr = area*((enrgyr + pressr)*ubarrrr +                       &
                   ubarr*(pressrrr)) + area*face_speed*pressrrr
        fluxm5ur = area*((enrgyr + pressr)*ubarrur +                       &
                   ubarr*(pressrur)) + area*face_speed*pressrur
        fluxm5vr = area*((enrgyr + pressr)*ubarrvr +                       &
                   ubarr*(pressrvr)) + area*face_speed*pressrvr
        fluxm5wr = area*((enrgyr + pressr)*ubarrwr +                       &
                   ubarr*(pressrwr)) + area*face_speed*pressrwr
        fluxm5pr = area*(ubarr*(enrgyrpr+pressrpr))+area*face_speed*pressrpr

!         flux1 = 0.5_dp*(fluxp1 + fluxm1 - area*t1)
!         flux2 = 0.5_dp*(fluxp2 + fluxm2 - area*t2)
!         flux3 = 0.5_dp*(fluxp3 + fluxm3 - area*t3)
!         flux4 = 0.5_dp*(fluxp4 + fluxm4 - area*t4)
!         flux5 = 0.5_dp*(fluxp5 + fluxm5 - area*t5)

        flux1rl = 0.5_dp*(fluxp1rl - area*t1rl)
        flux1ul = 0.5_dp*(fluxp1ul - area*t1ul)
        flux1vl = 0.5_dp*(fluxp1vl - area*t1vl)
        flux1wl = 0.5_dp*(fluxp1wl - area*t1wl)
        flux1pl = 0.5_dp*(- area*t1pl)
        flux1rr = 0.5_dp*(fluxm1rr - area*t1rr)
        flux1ur = 0.5_dp*(fluxm1ur - area*t1ur)
        flux1vr = 0.5_dp*(fluxm1vr - area*t1vr)
        flux1wr = 0.5_dp*(fluxm1wr - area*t1wr)
        flux1pr = 0.5_dp*(- area*t1pr)

        flux2rl = 0.5_dp*(fluxp2rl - area*t2rl)
        flux2ul = 0.5_dp*(fluxp2ul - area*t2ul)
        flux2vl = 0.5_dp*(fluxp2vl - area*t2vl)
        flux2wl = 0.5_dp*(fluxp2wl - area*t2wl)
        flux2pl = 0.5_dp*(fluxp2pl - area*t2pl)
        flux2rr = 0.5_dp*(fluxm2rr - area*t2rr)
        flux2ur = 0.5_dp*(fluxm2ur - area*t2ur)
        flux2vr = 0.5_dp*(fluxm2vr - area*t2vr)
        flux2wr = 0.5_dp*(fluxm2wr - area*t2wr)
        flux2pr = 0.5_dp*(fluxm2pr - area*t2pr)

        flux3rl = 0.5_dp*(fluxp3rl - area*t3rl)
        flux3ul = 0.5_dp*(fluxp3ul - area*t3ul)
        flux3vl = 0.5_dp*(fluxp3vl - area*t3vl)
        flux3wl = 0.5_dp*(fluxp3wl - area*t3wl)
        flux3pl = 0.5_dp*(fluxp3pl - area*t3pl)
        flux3rr = 0.5_dp*(fluxm3rr - area*t3rr)
        flux3ur = 0.5_dp*(fluxm3ur - area*t3ur)
        flux3vr = 0.5_dp*(fluxm3vr - area*t3vr)
        flux3wr = 0.5_dp*(fluxm3wr - area*t3wr)
        flux3pr = 0.5_dp*(fluxm3pr - area*t3pr)

        flux4rl = 0.5_dp*(fluxp4rl - area*t4rl)
        flux4ul = 0.5_dp*(fluxp4ul - area*t4ul)
        flux4vl = 0.5_dp*(fluxp4vl - area*t4vl)
        flux4wl = 0.5_dp*(fluxp4wl - area*t4wl)
        flux4pl = 0.5_dp*(fluxp4pl - area*t4pl)
        flux4rr = 0.5_dp*(fluxm4rr - area*t4rr)
        flux4ur = 0.5_dp*(fluxm4ur - area*t4ur)
        flux4vr = 0.5_dp*(fluxm4vr - area*t4vr)
        flux4wr = 0.5_dp*(fluxm4wr - area*t4wr)
        flux4pr = 0.5_dp*(fluxm4pr - area*t4pr)

        flux5rl = 0.5_dp*(fluxp5rl - area*t5rl)
        flux5ul = 0.5_dp*(fluxp5ul - area*t5ul)
        flux5vl = 0.5_dp*(fluxp5vl - area*t5vl)
        flux5wl = 0.5_dp*(fluxp5wl - area*t5wl)
        flux5pl = 0.5_dp*(fluxp5pl - area*t5pl)
        flux5rr = 0.5_dp*(fluxm5rr - area*t5rr)
        flux5ur = 0.5_dp*(fluxm5ur - area*t5ur)
        flux5vr = 0.5_dp*(fluxm5vr - area*t5vr)
        flux5wr = 0.5_dp*(fluxm5wr - area*t5wr)
        flux5pr = 0.5_dp*(fluxm5pr - area*t5pr)

      dfp(1,1) = flux1rl
      dfp(1,2) = flux1ul
      dfp(1,3) = flux1vl
      dfp(1,4) = flux1wl
      dfp(1,5) = flux1pl

      dfp(2,1) = flux2rl
      dfp(2,2) = flux2ul
      dfp(2,3) = flux2vl
      dfp(2,4) = flux2wl
      dfp(2,5) = flux2pl

      dfp(3,1) = flux3rl
      dfp(3,2) = flux3ul
      dfp(3,3) = flux3vl
      dfp(3,4) = flux3wl
      dfp(3,5) = flux3pl

      dfp(4,1) = flux4rl
      dfp(4,2) = flux4ul
      dfp(4,3) = flux4vl
      dfp(4,4) = flux4wl
      dfp(4,5) = flux4pl

      dfp(5,1) = flux5rl
      dfp(5,2) = flux5ul
      dfp(5,3) = flux5vl
      dfp(5,4) = flux5wl
      dfp(5,5) = flux5pl

      dfm(1,1) = flux1rr
      dfm(1,2) = flux1ur
      dfm(1,3) = flux1vr
      dfm(1,4) = flux1wr
      dfm(1,5) = flux1pr

      dfm(2,1) = flux2rr
      dfm(2,2) = flux2ur
      dfm(2,3) = flux2vr
      dfm(2,4) = flux2wr
      dfm(2,5) = flux2pr

      dfm(3,1) = flux3rr
      dfm(3,2) = flux3ur
      dfm(3,3) = flux3vr
      dfm(3,4) = flux3wr
      dfm(3,5) = flux3pr

      dfm(4,1) = flux4rr
      dfm(4,2) = flux4ur
      dfm(4,3) = flux4vr
      dfm(4,4) = flux4wr
      dfm(4,5) = flux4pr

      dfm(5,1) = flux5rr
      dfm(5,2) = flux5ur
      dfm(5,3) = flux5vr
      dfm(5,4) = flux5wr
      dfm(5,5) = flux5pr

      if(node1 <= nnodes0) then
        idiag = g2m(node1)
        do k = 1, 5
            a_diag(1,k,idiag) = a_diag(1,k,idiag) + dfp(1,k)
            a_diag(2,k,idiag) = a_diag(2,k,idiag) + dfp(2,k)
            a_diag(3,k,idiag) = a_diag(3,k,idiag) + dfp(3,k)
            a_diag(4,k,idiag) = a_diag(4,k,idiag) + dfp(4,k)
            a_diag(5,k,idiag) = a_diag(5,k,idiag) + dfp(5,k)
        end do

        ioff  = fhelp(1,n)
        do k = 1, 5
            a_off(1,k,ioff)  = a_off(1,k,ioff) + real(dfm(1,k),odp)
            a_off(2,k,ioff)  = a_off(2,k,ioff) + real(dfm(2,k),odp)
            a_off(3,k,ioff)  = a_off(3,k,ioff) + real(dfm(3,k),odp)
            a_off(4,k,ioff)  = a_off(4,k,ioff) + real(dfm(4,k),odp)
            a_off(5,k,ioff)  = a_off(5,k,ioff) + real(dfm(5,k),odp)
        end do
      end if

      if(node2 <= nnodes0) then
        idiag = g2m(node2)
        do k = 1, 5
            a_diag(1,k,idiag) = a_diag(1,k,idiag) - dfm(1,k)
            a_diag(2,k,idiag) = a_diag(2,k,idiag) - dfm(2,k)
            a_diag(3,k,idiag) = a_diag(3,k,idiag) - dfm(3,k)
            a_diag(4,k,idiag) = a_diag(4,k,idiag) - dfm(4,k)
            a_diag(5,k,idiag) = a_diag(5,k,idiag) - dfm(5,k)
        end do

        ioff  = fhelp(2,n)
        do k = 1, 5
            a_off(1,k,ioff)  = a_off(1,k,ioff) - real(dfp(1,k),odp)
            a_off(2,k,ioff)  = a_off(2,k,ioff) - real(dfp(2,k),odp)
            a_off(3,k,ioff)  = a_off(3,k,ioff) - real(dfp(3,k),odp)
            a_off(4,k,ioff)  = a_off(4,k,ioff) - real(dfp(4,k),odp)
            a_off(5,k,ioff)  = a_off(5,k,ioff) - real(dfp(5,k),odp)
        end do
      end if

    end do scanedges

  end subroutine roe_jacobians_gen

!================================ ROE_JACOBIANS ==============================80
!
! Roe flux jacobian for generic gas path (part 2)
!
! This routine solves generic gas path decoupled problem for species and vib.
! energy. The other conserved variables have already been calculated (including
! total mass) for a previously constant thermodynamic state.  This updates the
! thermodynamic state as well as ensures that species continuity exactly
! matches the total continuity.
!
!=============================================================================80

  subroutine roe_jacobians_dc(nnodes0, nnodes01, nedgeloc, nedgeloc_2d,        &
                              max_nnz, eptr, qnode, a_diag, a_off, xn, yn, zn, &
                              ra, fhelp, facespeed, n_tot, njac, g2m,          &
                              pressure_jac)

    use info_depr,       only : twod, adptv_entropy_fix
    use inviscid_flux,   only : rhs_u_eigenvalue_coef, lhs_u_eigenvalue_coef
    use generic_gas_map, only : n_density, n_momx, n_momy, n_momz, n_mom,      &
                                n_pjac, n_species, n_pressure_k, n_etot
    !use turb_gen,        only : n_turb_ke, n_dis_nutl, betat_jac

    integer,                                    intent(in) :: nnodes0,nnodes01
    integer,                                    intent(in) :: nedgeloc
    integer,                                    intent(in) :: nedgeloc_2d
    integer,                                    intent(in) :: max_nnz
    integer,                                    intent(in) :: n_tot,njac

    integer,      dimension(2,nedgeloc),        intent(in) :: eptr
    integer,      dimension(2,nedgeloc),        intent(in) :: fhelp
    integer,      dimension(:),                 intent(in) :: g2m

    real(dp),  dimension(n_tot,nnodes01),        intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed
    real(dp),  dimension(njac,njac,nnodes0),     intent(inout) :: a_diag
    real(odp), dimension(njac,max_nnz),          intent(inout) :: a_off
    real(dp),  dimension(n_pjac,n_mom,nnodes01), intent(in)    :: pressure_jac

    real(dp),  dimension(n_species) :: fracl_i, fracr_i, frac_i
    real(dp),  dimension(n_pjac)    :: betal, betar, beta


    integer :: n,node1,node2,nedge_jac_eval,idiag,k,ioff,ns,ns1

    real(dp)    :: xnorm,ynorm,znorm,area,face_speed

    real(dp)    :: rhol,rhol_inv,rhor,rhor_inv
    real(dp)    :: ul,vl,wl,ubarl,pressl,enrgyl,Hl
    real(dp)    :: ur,vr,wr,ubarr,pressr,enrgyr,Hr
    real(dp)    :: rho,wat,rho_inv
    real(dp)    :: wt1,wt2,ratio
    real(dp)    :: u,v,w,ubar,H
    real(dp)    :: q2l,q2r,q2,c,c2
    real(dp)    :: dp_de, dp_drho
    real(dp)    :: scoef,utngl,utngr,utang
    real(dp)    :: eig,abseig,eigeps
    real(dp)    :: cecoef,fa,faeps,absfa,maxeig
    real(dp)    :: dfm,dfp

    real(dp), parameter    :: my_0    = 0.0_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_1    = 1.0_dp

  continue

    ns  = n_species
    ns1 = n_species+1

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

! Loop over the edges and calculate the Jacobians

    scanedges: do n = 1, nedge_jac_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Face speed

      face_speed = 0.0_dp

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

! Get unit normals and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

! Get variables on "left" side of face

      rhol = qnode(n_density,node1)
        rhol_inv = my_1/rhol

      ul = qnode(n_momx,node1) * rhol_inv

      vl = qnode(n_momy,node1) * rhol_inv

      wl = qnode(n_momz,node1) * rhol_inv

      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed

      enrgyl = qnode(n_etot,node1)

      pressl = qnode(n_pressure_k(1),node1)

      Hl = (enrgyl + pressl)*rhol_inv

      !pressure jacobian
      betal(:) = pressure_jac(:,1,node1)

! Get variables on "right" side of face

      rhor = qnode(n_density,node2)
        rhor_inv = my_1/rhor

      ur = qnode(n_momx,node2) * rhor_inv

      vr = qnode(n_momy,node2) * rhor_inv

      wr = qnode(n_momz,node2) * rhor_inv

      ubarr = xnorm*ur + ynorm*vr + znorm*wr - face_speed

      enrgyr = qnode(n_etot,node2)

      pressr = qnode(n_pressure_k(1),node2)

      Hr = (enrgyr + pressr)*rhor_inv

      !pressure jacobian
      betar(:) = pressure_jac(:,1,node2)

! Compute Roe averages

      rho = sqrt(rhol*rhor)
        rho_inv = my_1/rho

      wat = rho/(rho + rhor)

      u = ul*wat + ur*(1.0_dp - wat)

      v = vl*wat + vr*(1.0_dp - wat)

      w = wl*wat + wr*(1.0_dp - wat)

      ubar = xnorm*u + ynorm*v + znorm*w - face_speed

      q2 = u*u + v*v + w*w

      H = Hl*wat + Hr*(1.0_dp - wat)

!     Pressure weight the pressure jacobian factors
!     This procedure will exactly match perfect gas treatment

      ratio = pressr/pressl
      if(ratio>my_1)then
        wt1 = (my_1/ratio)**8
        wt2 = my_1 - wt1
      else
        wt2 = (ratio)**8
        wt1 = my_1 - wt2
      end if

!     Get Roe averaged speed of sound
      !roe_averaged mass fractions
      fracl_i(1:ns) = qnode(1:ns,node1)*rhol_inv
      fracr_i(1:ns) = qnode(1:ns,node2)*rhor_inv
      frac_i(1:ns) = wat*fracl_i(1:ns) + (my_1 - wat)*fracr_i(1:ns)

      !roe-averaged pressure jacobian
      beta(1:n_pjac) = wt1*betal(1:n_pjac) + wt2*betar(1:n_pjac)
      beta(1:ns)     = beta(1:ns) + beta(ns1)*0.5_dp*q2

      dp_de   = rho*beta(ns1)
      dp_drho = sum(frac_i(1:ns)*beta(1:ns))

      c2 =  dp_drho + rho_inv*dp_de*(H-q2)
      c  = sqrt(c2)

!    Eigenvalue limiting.  In terms of dimensional equations:
!    -limit eigenvalues as fraction of local maximum

      if (adptv_entropy_fix) then

        scoef   = my_0
!       scoef   = min(my_4th, flux_efixc(n))

!       maximum eigenvalue

        q2l  = ul*ul + vl*vl + wl*wl
        q2r  = ur*ur + vr*vr + wr*wr
        utngl  = sqrt(max(my_0, q2l-ubarl*ubarl))
        utngr  = sqrt(max(my_0, q2r-ubarr*ubarr))
        utang  = utngl*wat + utngr*(my_1 - wat)
        maxeig = max(abs( ubar ), abs( utang )) + c

!       convective eigenvalue limiter coefficient

!       cecoef  = min(my_half, lhs_u_eigenvalue_coef*(my_1-scoef))
        cecoef  = 1.10_dp*min(my_half, rhs_u_eigenvalue_coef*(my_1-scoef))
        cecoef  = max(0.000011_dp, min(my_1, cecoef))

      else

        fa    = ubar
        faeps = 0.05_dp*c
        absfa = abs( fa )
        if (absfa < faeps) absfa = my_half*(fa**2/faeps + faeps)

!       maximum eigenvalue

        maxeig = absfa + c

!       convective eigenvalue limite coefficient

        cecoef  = lhs_u_eigenvalue_coef

      end if

! Now compute eigenvalues, eigenvectors, and strengths

      eig = ubar

! convective eigenvalue limiter

      eigeps = cecoef*maxeig

      abseig = abs( eig )

      if(abseig < eigeps) abseig = my_half*(eig**2/eigeps + eigeps)

      eig = sign(abseig,eig)

! Jacobians are derived from the proportionality to the total mass flux
! and the total energy flux

      dfp = area*rhol*my_half*(ubarl + abseig)
      dfm = area*rhor*my_half*(ubarr - abseig)

      if(node1 <= nnodes0) then
        idiag = g2m(node1)

        do k = 1, njac
            a_diag(k,k,idiag) = a_diag(k,k,idiag) + dfp
        end do

        ioff  = fhelp(1,n)
        do k = 1, njac
            a_off(k,ioff) = a_off(k,ioff) + real(dfm,odp)
        end do
      end if

      if(node2 <= nnodes0) then
        idiag = g2m(node2)

        do k = 1, njac
            a_diag(k,k,idiag) = a_diag(k,k,idiag) - dfm
        end do

        ioff  = fhelp(2,n)
        do k = 1, njac
            a_off(k,ioff)  = a_off(k,ioff) - real(dfp,odp)
        end do
      end if

    end do scanedges

end subroutine roe_jacobians_dc

!================================ CD_JACOBIANS ===============================80
!
! Central difference flux jacobian
!
! All derivatives worked out in detail
!
!=============================================================================80

  subroutine cd_jacobians(nnodes0, nnodes01, nedgeloc, nedgeloc_2d, max_nnz,   &
                          eptr, qnode, a_diag, a_off, xn, yn, zn,              &
                          ra, fhelp, facespeed, n_tot, njac, g2m)

    use info_depr, only : twod
    use fluid,     only : gm1, gamma
    use thermo,    only : etop, ptoe

    integer, intent(in) :: nnodes0,nnodes01,nedgeloc,nedgeloc_2d,max_nnz
    integer, intent(in) :: n_tot,njac

    integer, dimension(2,nedgeloc), intent(in) :: eptr
    integer, dimension(2,nedgeloc), intent(in) :: fhelp
    integer, dimension(:),          intent(in) :: g2m

    real(dp),  dimension(n_tot,nnodes01),       intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),             intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),             intent(in)    :: facespeed
    real(dp),  dimension(njac,njac,nnodes0),    intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz), intent(inout) :: a_off

    integer :: n,node1,node2,nedge_jac_eval,idiag,j,k,ioff,qdim

    real(dp)    :: xnorm,ynorm,znorm,area,face_speed

    real(dp)    :: q1q1l,q1q2l,q1q3l,q1q4l,q1q5l
    real(dp)    :: q2q1l,q2q2l,q2q3l,q2q4l,q2q5l
    real(dp)    :: q3q1l,q3q2l,q3q3l,q3q4l,q3q5l
    real(dp)    :: q4q1l,q4q2l,q4q3l,q4q4l,q4q5l
    real(dp)    :: q5q1l,q5q2l,q5q3l,q5q4l,q5q5l

    real(dp)    :: q1q1r,q1q2r,q1q3r,q1q4r,q1q5r
    real(dp)    :: q2q1r,q2q2r,q2q3r,q2q4r,q2q5r
    real(dp)    :: q3q1r,q3q2r,q3q3r,q3q4r,q3q5r
    real(dp)    :: q4q1r,q4q2r,q4q3r,q4q4r,q4q5r
    real(dp)    :: q5q1r,q5q2r,q5q3r,q5q4r,q5q5r

    real(dp)    :: flux1rl,flux1ul,flux1vl,flux1wl,flux1pl
    real(dp)    :: flux2rl,flux2ul,flux2vl,flux2wl,flux2pl
    real(dp)    :: flux3rl,flux3ul,flux3vl,flux3wl,flux3pl
    real(dp)    :: flux4rl,flux4ul,flux4vl,flux4wl,flux4pl
    real(dp)    :: flux5rl,flux5ul,flux5vl,flux5wl,flux5pl

    real(dp)    :: fluxm1rl,fluxm1ul,fluxm1vl,fluxm1wl,fluxm1pl
    real(dp)    :: fluxm2rl,fluxm2ul,fluxm2vl,fluxm2wl,fluxm2pl
    real(dp)    :: fluxm3rl,fluxm3ul,fluxm3vl,fluxm3wl,fluxm3pl
    real(dp)    :: fluxm4rl,fluxm4ul,fluxm4vl,fluxm4wl,fluxm4pl
    real(dp)    :: fluxm5rl,fluxm5ul,fluxm5vl,fluxm5wl,fluxm5pl

    real(dp)    :: fluxp1rl,fluxp1ul,fluxp1vl,fluxp1wl,fluxp1pl
    real(dp)    :: fluxp2rl,fluxp2ul,fluxp2vl,fluxp2wl,fluxp2pl
    real(dp)    :: fluxp3rl,fluxp3ul,fluxp3vl,fluxp3wl,fluxp3pl
    real(dp)    :: fluxp4rl,fluxp4ul,fluxp4vl,fluxp4wl,fluxp4pl
    real(dp)    :: fluxp5rl,fluxp5ul,fluxp5vl,fluxp5wl,fluxp5pl

    real(dp)    :: flux1rr,flux1ur,flux1vr,flux1wr,flux1pr
    real(dp)    :: flux2rr,flux2ur,flux2vr,flux2wr,flux2pr
    real(dp)    :: flux3rr,flux3ur,flux3vr,flux3wr,flux3pr
    real(dp)    :: flux4rr,flux4ur,flux4vr,flux4wr,flux4pr
    real(dp)    :: flux5rr,flux5ur,flux5vr,flux5wr,flux5pr

    real(dp)    :: fluxm1rr,fluxm1ur,fluxm1vr,fluxm1wr,fluxm1pr
    real(dp)    :: fluxm2rr,fluxm2ur,fluxm2vr,fluxm2wr,fluxm2pr
    real(dp)    :: fluxm3rr,fluxm3ur,fluxm3vr,fluxm3wr,fluxm3pr
    real(dp)    :: fluxm4rr,fluxm4ur,fluxm4vr,fluxm4wr,fluxm4pr
    real(dp)    :: fluxm5rr,fluxm5ur,fluxm5vr,fluxm5wr,fluxm5pr

    real(dp)    :: fluxp1rr,fluxp1ur,fluxp1vr,fluxp1wr,fluxp1pr
    real(dp)    :: fluxp2rr,fluxp2ur,fluxp2vr,fluxp2wr,fluxp2pr
    real(dp)    :: fluxp3rr,fluxp3ur,fluxp3vr,fluxp3wr,fluxp3pr
    real(dp)    :: fluxp4rr,fluxp4ur,fluxp4vr,fluxp4wr,fluxp4pr
    real(dp)    :: fluxp5rr,fluxp5ur,fluxp5vr,fluxp5wr,fluxp5pr

    real(dp)    :: ubarr
    real(dp)    :: ubarrrl,ubarrul,ubarrvl,ubarrwl,ubarrpl
    real(dp)    :: ubarrrr,ubarrur,ubarrvr,ubarrwr,ubarrpr

    real(dp)    :: enrgyr
    real(dp)    :: enrgyrrl,enrgyrul,enrgyrvl,enrgyrwl,enrgyrpl
    real(dp)    :: enrgyrrr,enrgyrur,enrgyrvr,enrgyrwr,enrgyrpr

    real(dp)    :: pressr
    real(dp)    :: pressrrl,pressrul,pressrvl,pressrwl,pressrpl
    real(dp)    :: pressrrr,pressrur,pressrvr,pressrwr,pressrpr

    real(dp)    :: q2r
    real(dp)    :: q2rrl,q2rul,q2rvl,q2rwl,q2rpl
    real(dp)    :: q2rrr,q2rur,q2rvr,q2rwr,q2rpr

    real(dp)    :: ur
    real(dp)    :: urrl,urul,urvl,urwl,urpl
    real(dp)    :: urrr,urur,urvr,urwr,urpr

    real(dp)    :: vr
    real(dp)    :: vrrl,vrul,vrvl,vrwl,vrpl
    real(dp)    :: vrrr,vrur,vrvr,vrwr,vrpr

    real(dp)    :: wr
    real(dp)    :: wrrl,wrul,wrvl,wrwl,wrpl
    real(dp)    :: wrrr,wrur,wrvr,wrwr,wrpr

    real(dp)    :: rhor
    real(dp)    :: rhorrl,rhorul,rhorvl,rhorwl,rhorpl
    real(dp)    :: rhorrr,rhorur,rhorvr,rhorwr,rhorpr

    real(dp)    :: ubarl
    real(dp)    :: ubarlrl,ubarlul,ubarlvl,ubarlwl,ubarlpl
    real(dp)    :: ubarlrr,ubarlur,ubarlvr,ubarlwr,ubarlpr

    real(dp)    :: enrgyl
    real(dp)    :: enrgylrl,enrgylul,enrgylvl,enrgylwl,enrgylpl
    real(dp)    :: enrgylrr,enrgylur,enrgylvr,enrgylwr,enrgylpr

    real(dp)    :: pressl
    real(dp)    :: presslrl,presslul,presslvl,presslwl,presslpl
    real(dp)    :: presslrr,presslur,presslvr,presslwr,presslpr

    real(dp)    :: q2l
    real(dp)    :: q2lrl,q2lul,q2lvl,q2lwl,q2lpl
    real(dp)    :: q2lrr,q2lur,q2lvr,q2lwr,q2lpr

    real(dp)    :: ul
    real(dp)    :: ulrl,ulul,ulvl,ulwl,ulpl
    real(dp)    :: ulrr,ulur,ulvr,ulwr,ulpr

    real(dp)    :: vl
    real(dp)    :: vlrl,vlul,vlvl,vlwl,vlpl
    real(dp)    :: vlrr,vlur,vlvr,vlwr,vlpr

    real(dp)    :: wl
    real(dp)    :: wlrl,wlul,wlvl,wlwl,wlpl
    real(dp)    :: wlrr,wlur,wlvr,wlwr,wlpr

    real(dp)    :: rhol
    real(dp)    :: rholrl,rholul,rholvl,rholwl,rholpl
    real(dp)    :: rholrr,rholur,rholvr,rholwr,rholpr

    real(dp)    :: rho1, u1, v1, w1, q21, press1
    real(dp)    :: rho2, u2, v2, w2, q22, press2
    real(dp)    :: qs10, qs20, qs, rhoa, pa
    real(dp)    :: epsilon2, cc, lambda

    real(dp)    :: rho1q11, rho1q12, rho1q13, rho1q14, rho1q15
    real(dp)    :: rho1q21, rho1q22, rho1q23, rho1q24, rho1q25
    real(dp)    :: rho2q11, rho2q12, rho2q13, rho2q14, rho2q15
    real(dp)    :: rho2q21, rho2q22, rho2q23, rho2q24, rho2q25

    real(dp)    :: u1q11, u1q12, u1q13, u1q14, u1q15
    real(dp)    :: u1q21, u1q22, u1q23, u1q24, u1q25

    real(dp)    :: u2q11, u2q12, u2q13, u2q14, u2q15
    real(dp)    :: u2q21, u2q22, u2q23, u2q24, u2q25

    real(dp)    :: v1q11, v1q12, v1q13, v1q14, v1q15
    real(dp)    :: v1q21, v1q22, v1q23, v1q24, v1q25

    real(dp)    :: v2q11, v2q12, v2q13, v2q14, v2q15
    real(dp)    :: v2q21, v2q22, v2q23, v2q24, v2q25

    real(dp)    :: w1q11, w1q12, w1q13, w1q14, w1q15
    real(dp)    :: w1q21, w1q22, w1q23, w1q24, w1q25

    real(dp)    :: w2q11, w2q12, w2q13, w2q14, w2q15
    real(dp)    :: w2q21, w2q22, w2q23, w2q24, w2q25

    real(dp)    :: q21q11, q21q12, q21q13, q21q14, q21q15
    real(dp)    :: q21q21, q21q22, q21q23, q21q24, q21q25

    real(dp)    :: q22q11, q22q12, q22q13, q22q14, q22q15
    real(dp)    :: q22q21, q22q22, q22q23, q22q24, q22q25

    real(dp)    :: press1q11, press1q12, press1q13, press1q14, press1q15
    real(dp)    :: press1q21, press1q22, press1q23, press1q24, press1q25

    real(dp)    :: press2q11, press2q12, press2q13, press2q14, press2q15
    real(dp)    :: press2q21, press2q22, press2q23, press2q24, press2q25

    real(dp)    :: qs10q11, qs10q12, qs10q13, qs10q14, qs10q15
    real(dp)    :: qs10q21, qs10q22, qs10q23, qs10q24, qs10q25

    real(dp)    :: qs20q11, qs20q12, qs20q13, qs20q14, qs20q15
    real(dp)    :: qs20q21, qs20q22, qs20q23, qs20q24, qs20q25

    real(dp)    :: qsq11, qsq12, qsq13, qsq14, qsq15
    real(dp)    :: qsq21, qsq22, qsq23, qsq24, qsq25

    real(dp)    :: rhoaq11, rhoaq12, rhoaq13, rhoaq14, rhoaq15
    real(dp)    :: rhoaq21, rhoaq22, rhoaq23, rhoaq24, rhoaq25

    real(dp)    :: paq11, paq12, paq13, paq14, paq15
    real(dp)    :: paq21, paq22, paq23, paq24, paq25

    real(dp)    :: epsilon2q11, epsilon2q12, epsilon2q13, epsilon2q14,         &
                   epsilon2q15
    real(dp)    :: epsilon2q21, epsilon2q22, epsilon2q23, epsilon2q24,         &
                   epsilon2q25

    real(dp)    :: ccq11, ccq12, ccq13, ccq14, ccq15
    real(dp)    :: ccq21, ccq22, ccq23, ccq24, ccq25

    real(dp)    :: lambdaq11, lambdaq12, lambdaq13, lambdaq14, lambdaq15
    real(dp)    :: lambdaq21, lambdaq22, lambdaq23, lambdaq24, lambdaq25

    real(dp)    :: dq1q11, dq1q12, dq1q13, dq1q14, dq1q15
    real(dp)    :: dq1q21, dq1q22, dq1q23, dq1q24, dq1q25

    real(dp)    :: dq2q11, dq2q12, dq2q13, dq2q14, dq2q15
    real(dp)    :: dq2q21, dq2q22, dq2q23, dq2q24, dq2q25

    real(dp)    :: dq3q11, dq3q12, dq3q13, dq3q14, dq3q15
    real(dp)    :: dq3q21, dq3q22, dq3q23, dq3q24, dq3q25

    real(dp)    :: dq4q11, dq4q12, dq4q13, dq4q14, dq4q15
    real(dp)    :: dq4q21, dq4q22, dq4q23, dq4q24, dq4q25

    real(dp)    :: dq5q11, dq5q12, dq5q13, dq5q14, dq5q15
    real(dp)    :: dq5q21, dq5q22, dq5q23, dq5q24, dq5q25

!   p1 and p2 terms - not needed when forcing 1st order LHS
!   real(dp)    :: p1, p2

    real(dp), dimension(5,5)    :: dfm,dfp

!   pw term - not needed when forcing 1st order LHS
!   real(dp), dimension(2,nnodes01)    :: pw

    real(dp), parameter    :: my_0    = 0.0_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_1    = 1.0_dp
    real(dp), parameter    :: my_2    = 2.0_dp

  continue

! Set dimension for etop, ptoe

    qdim = nnodes01

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

!  Convert to primitive variables

      call etop(qdim,qnode,n_tot,0) ! Note eqn_set hardwired to 0 here

! Loop over the edges and calculate the Jacobians

        scanedges: do n = 1, nedge_jac_eval

          node1 = eptr(1,n)
          node2 = eptr(2,n)

! Get unit normals and area

          xnorm = xn(n)
          ynorm = yn(n)
          znorm = zn(n)
          area  = ra(n)

! Face speed

          face_speed = 0.0_dp

          if (need_grid_velocity) then
            face_speed = facespeed(n)
          end if

! Get variables on "left" side of face

            rhol   = qnode(1,node1)
              rholrl = 1.0_dp
              rholul = 0.0_dp
              rholvl = 0.0_dp
              rholwl = 0.0_dp
              rholpl = 0.0_dp

              rholrr = 0.0_dp
              rholur = 0.0_dp
              rholvr = 0.0_dp
              rholwr = 0.0_dp
              rholpr = 0.0_dp
            ul     = qnode(2,node1)
              ulrl = 0.0_dp
              ulul = 1.0_dp
              ulvl = 0.0_dp
              ulwl = 0.0_dp
              ulpl = 0.0_dp

              ulrr = 0.0_dp
              ulur = 0.0_dp
              ulvr = 0.0_dp
              ulwr = 0.0_dp
              ulpr = 0.0_dp
            vl     = qnode(3,node1)
              vlrl = 0.0_dp
              vlul = 0.0_dp
              vlvl = 1.0_dp
              vlwl = 0.0_dp
              vlpl = 0.0_dp

              vlrr = 0.0_dp
              vlur = 0.0_dp
              vlvr = 0.0_dp
              vlwr = 0.0_dp
              vlpr = 0.0_dp
            wl     = qnode(4,node1)
              wlrl = 0.0_dp
              wlul = 0.0_dp
              wlvl = 0.0_dp
              wlwl = 1.0_dp
              wlpl = 0.0_dp

              wlrr = 0.0_dp
              wlur = 0.0_dp
              wlvr = 0.0_dp
              wlwr = 0.0_dp
              wlpr = 0.0_dp

            q2l     = ul*ul + vl*vl + wl*wl
              q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
              q2lul = 2.0_dp*ul*ulul + 2.0_dp*vl*vlul + 2.0_dp*wl*wlul
              q2lvl = 2.0_dp*ul*ulvl + 2.0_dp*vl*vlvl + 2.0_dp*wl*wlvl
              q2lwl = 2.0_dp*ul*ulwl + 2.0_dp*vl*vlwl + 2.0_dp*wl*wlwl
              q2lpl = 2.0_dp*ul*ulpl + 2.0_dp*vl*vlpl + 2.0_dp*wl*wlpl

              q2lrr = 2.0_dp*ul*ulrr + 2.0_dp*vl*vlrr + 2.0_dp*wl*wlrr
              q2lur = 2.0_dp*ul*ulur + 2.0_dp*vl*vlur + 2.0_dp*wl*wlur
              q2lvr = 2.0_dp*ul*ulvr + 2.0_dp*vl*vlvr + 2.0_dp*wl*wlvr
              q2lwr = 2.0_dp*ul*ulwr + 2.0_dp*vl*vlwr + 2.0_dp*wl*wlwr
              q2lpr = 2.0_dp*ul*ulpr + 2.0_dp*vl*vlpr + 2.0_dp*wl*wlpr

            pressl = qnode(5,node1)
              presslrl = 0.0_dp
              presslul = 0.0_dp
              presslvl = 0.0_dp
              presslwl = 0.0_dp
              presslpl = 1.0_dp

              presslrr = 0.0_dp
              presslur = 0.0_dp
              presslvr = 0.0_dp
              presslwr = 0.0_dp
              presslpr = 0.0_dp

            enrgyl = pressl/gm1 + 0.5_dp*rhol*q2l

              enrgylrl = presslrl/gm1 + 0.5_dp*(rhol*q2lrl + q2l*rholrl)
              enrgylul = presslul/gm1 + 0.5_dp*(rhol*q2lul + q2l*rholul)
              enrgylvl = presslvl/gm1 + 0.5_dp*(rhol*q2lvl + q2l*rholvl)
              enrgylwl = presslwl/gm1 + 0.5_dp*(rhol*q2lwl + q2l*rholwl)
              enrgylpl = presslpl/gm1 + 0.5_dp*(rhol*q2lpl + q2l*rholpl)

              enrgylrr = presslrr/gm1 + 0.5_dp*(rhol*q2lrr + q2l*rholrr)
              enrgylur = presslur/gm1 + 0.5_dp*(rhol*q2lur + q2l*rholur)
              enrgylvr = presslvr/gm1 + 0.5_dp*(rhol*q2lvr + q2l*rholvr)
              enrgylwr = presslwr/gm1 + 0.5_dp*(rhol*q2lwr + q2l*rholwr)
              enrgylpr = presslpr/gm1 + 0.5_dp*(rhol*q2lpr + q2l*rholpr)

!           Hl     = (enrgyl + pressl)/rhol

!           Hlrl = (rhol*(enrgylrl+presslrl) - (enrgyl+pressl)*rholrl)         &
!                  / rhol / rhol
!           Hlul = (rhol*(enrgylul+presslul) - (enrgyl+pressl)*rholul)         &
!                  / rhol / rhol
!           Hlvl = (rhol*(enrgylvl+presslvl) - (enrgyl+pressl)*rholvl)         &
!                  / rhol / rhol
!           Hlwl = (rhol*(enrgylwl+presslwl) - (enrgyl+pressl)*rholwl)         &
!                  / rhol / rhol
!           Hlpl = (rhol*(enrgylpl+presslpl) - (enrgyl+pressl)*rholpl)         &
!                  / rhol / rhol

!           Hlrr = (rhol*(enrgylrr+presslrr) - (enrgyl+pressl)*rholrr)         &
!                  / rhol / rhol
!           Hlur = (rhol*(enrgylur+presslur) - (enrgyl+pressl)*rholur)         &
!                  / rhol / rhol
!           Hlvr = (rhol*(enrgylvr+presslvr) - (enrgyl+pressl)*rholvr)         &
!                  / rhol / rhol
!           Hlwr = (rhol*(enrgylwr+presslwr) - (enrgyl+pressl)*rholwr)         &
!                  / rhol / rhol
!           Hlpr = (rhol*(enrgylpr+presslpr) - (enrgyl+pressl)*rholpr)         &
!                  / rhol / rhol

            ubarl  = xnorm*ul + ynorm*vl + znorm*wl - face_speed

            ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
            ubarlul = xnorm*ulul + ynorm*vlul + znorm*wlul
            ubarlvl = xnorm*ulvl + ynorm*vlvl + znorm*wlvl
            ubarlwl = xnorm*ulwl + ynorm*vlwl + znorm*wlwl
            ubarlpl = xnorm*ulpl + ynorm*vlpl + znorm*wlpl

            ubarlrr = xnorm*ulrr + ynorm*vlrr + znorm*wlrr
            ubarlur = xnorm*ulur + ynorm*vlur + znorm*wlur
            ubarlvr = xnorm*ulvr + ynorm*vlvr + znorm*wlvr
            ubarlwr = xnorm*ulwr + ynorm*vlwr + znorm*wlwr
            ubarlpr = xnorm*ulpr + ynorm*vlpr + znorm*wlpr

! Get variables on "right" side of face

            rhor   = qnode(1,node2)
              rhorrl = 0.0_dp
              rhorul = 0.0_dp
              rhorvl = 0.0_dp
              rhorwl = 0.0_dp
              rhorpl = 0.0_dp

              rhorrr = 1.0_dp
              rhorur = 0.0_dp
              rhorvr = 0.0_dp
              rhorwr = 0.0_dp
              rhorpr = 0.0_dp

            ur     = qnode(2,node2)
              urrl = 0.0_dp
              urul = 0.0_dp
              urvl = 0.0_dp
              urwl = 0.0_dp
              urpl = 0.0_dp

              urrr = 0.0_dp
              urur = 1.0_dp
              urvr = 0.0_dp
              urwr = 0.0_dp
              urpr = 0.0_dp

            vr     = qnode(3,node2)
              vrrl = 0.0_dp
              vrul = 0.0_dp
              vrvl = 0.0_dp
              vrwl = 0.0_dp
              vrpl = 0.0_dp

              vrrr = 0.0_dp
              vrur = 0.0_dp
              vrvr = 1.0_dp
              vrwr = 0.0_dp
              vrpr = 0.0_dp

            wr     = qnode(4,node2)
              wrrl = 0.0_dp
              wrul = 0.0_dp
              wrvl = 0.0_dp
              wrwl = 0.0_dp
              wrpl = 0.0_dp

              wrrr = 0.0_dp
              wrur = 0.0_dp
              wrvr = 0.0_dp
              wrwr = 1.0_dp
              wrpr = 0.0_dp

            q2r    = ur*ur + vr*vr + wr*wr
              q2rrl = 2.0_dp*ur*urrl + 2.0_dp*vr*vrrl + 2.0_dp*wr*wrrl
              q2rul = 2.0_dp*ur*urul + 2.0_dp*vr*vrul + 2.0_dp*wr*wrul
              q2rvl = 2.0_dp*ur*urvl + 2.0_dp*vr*vrvl + 2.0_dp*wr*wrvl
              q2rwl = 2.0_dp*ur*urwl + 2.0_dp*vr*vrwl + 2.0_dp*wr*wrwl
              q2rpl = 2.0_dp*ur*urpl + 2.0_dp*vr*vrpl + 2.0_dp*wr*wrpl

              q2rrr = 2.0_dp*ur*urrr + 2.0_dp*vr*vrrr + 2.0_dp*wr*wrrr
              q2rur = 2.0_dp*ur*urur + 2.0_dp*vr*vrur + 2.0_dp*wr*wrur
              q2rvr = 2.0_dp*ur*urvr + 2.0_dp*vr*vrvr + 2.0_dp*wr*wrvr
              q2rwr = 2.0_dp*ur*urwr + 2.0_dp*vr*vrwr + 2.0_dp*wr*wrwr
              q2rpr = 2.0_dp*ur*urpr + 2.0_dp*vr*vrpr + 2.0_dp*wr*wrpr

            pressr = qnode(5,node2)
              pressrrl = 0.0_dp
              pressrul = 0.0_dp
              pressrvl = 0.0_dp
              pressrwl = 0.0_dp
              pressrpl = 0.0_dp

              pressrrr = 0.0_dp
              pressrur = 0.0_dp
              pressrvr = 0.0_dp
              pressrwr = 0.0_dp
              pressrpr = 1.0_dp

            enrgyr = pressr/gm1 + 0.5_dp*rhor*q2r

              enrgyrrl = pressrrl/gm1 + 0.5_dp*(rhor*q2rrl + q2r*rhorrl)
              enrgyrul = pressrul/gm1 + 0.5_dp*(rhor*q2rul + q2r*rhorul)
              enrgyrvl = pressrvl/gm1 + 0.5_dp*(rhor*q2rvl + q2r*rhorvl)
              enrgyrwl = pressrwl/gm1 + 0.5_dp*(rhor*q2rwl + q2r*rhorwl)
              enrgyrpl = pressrpl/gm1 + 0.5_dp*(rhor*q2rpl + q2r*rhorpl)

              enrgyrrr = pressrrr/gm1 + 0.5_dp*(rhor*q2rrr + q2r*rhorrr)
              enrgyrur = pressrur/gm1 + 0.5_dp*(rhor*q2rur + q2r*rhorur)
              enrgyrvr = pressrvr/gm1 + 0.5_dp*(rhor*q2rvr + q2r*rhorvr)
              enrgyrwr = pressrwr/gm1 + 0.5_dp*(rhor*q2rwr + q2r*rhorwr)
              enrgyrpr = pressrpr/gm1 + 0.5_dp*(rhor*q2rpr + q2r*rhorpr)

!           Hr     = (enrgyr + pressr)/rhor

!           Hrrl = (rhor*(enrgyrrl+pressrrl) - (enrgyr+pressr)*rhorrl)         &
!                  / rhor / rhor
!           Hrul = (rhor*(enrgyrul+pressrul) - (enrgyr+pressr)*rhorul)         &
!                  / rhor / rhor
!           Hrvl = (rhor*(enrgyrvl+pressrvl) - (enrgyr+pressr)*rhorvl)         &
!                  / rhor / rhor
!           Hrwl = (rhor*(enrgyrwl+pressrwl) - (enrgyr+pressr)*rhorwl)         &
!                  / rhor / rhor
!           Hrpl = (rhor*(enrgyrpl+pressrpl) - (enrgyr+pressr)*rhorpl)         &
!                  / rhor / rhor

!           Hrrr = (rhor*(enrgyrrr+pressrrr) - (enrgyr+pressr)*rhorrr)         &
!                  / rhor / rhor
!           Hrur = (rhor*(enrgyrur+pressrur) - (enrgyr+pressr)*rhorur)         &
!                  / rhor / rhor
!           Hrvr = (rhor*(enrgyrvr+pressrvr) - (enrgyr+pressr)*rhorvr)         &
!                  / rhor / rhor
!           Hrwr = (rhor*(enrgyrwr+pressrwr) - (enrgyr+pressr)*rhorwr)         &
!                  / rhor / rhor
!           Hrpr = (rhor*(enrgyrpr+pressrpr) - (enrgyr+pressr)*rhorpr)         &
!                  / rhor / rhor

            ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed

            ubarrrl = xnorm*urrl + ynorm*vrrl + znorm*wrrl
            ubarrul = xnorm*urul + ynorm*vrul + znorm*wrul
            ubarrvl = xnorm*urvl + ynorm*vrvl + znorm*wrvl
            ubarrwl = xnorm*urwl + ynorm*vrwl + znorm*wrwl
            ubarrpl = xnorm*urpl + ynorm*vrpl + znorm*wrpl

            ubarrrr = xnorm*urrr + ynorm*vrrr + znorm*wrrr
            ubarrur = xnorm*urur + ynorm*vrur + znorm*wrur
            ubarrvr = xnorm*urvr + ynorm*vrvr + znorm*wrvr
            ubarrwr = xnorm*urwr + ynorm*vrwr + znorm*wrwr
            ubarrpr = xnorm*urpr + ynorm*vrpr + znorm*wrpr

! Compute flux using variables from left side of face

!           fluxp1 = area*rhol*ubarl

            fluxp1rl = area*(rhol*ubarlrl + ubarl*rholrl)
            fluxp1ul = area*(rhol*ubarlul + ubarl*rholul)
            fluxp1vl = area*(rhol*ubarlvl + ubarl*rholvl)
            fluxp1wl = area*(rhol*ubarlwl + ubarl*rholwl)
            fluxp1pl = area*(rhol*ubarlpl + ubarl*rholpl)

            fluxp1rr = area*(rhol*ubarlrr + ubarl*rholrr)
            fluxp1ur = area*(rhol*ubarlur + ubarl*rholur)
            fluxp1vr = area*(rhol*ubarlvr + ubarl*rholvr)
            fluxp1wr = area*(rhol*ubarlwr + ubarl*rholwr)
            fluxp1pr = area*(rhol*ubarlpr + ubarl*rholpr)

!           fluxp2 = area*(rhol*ul*ubarl + xnorm*pressl)

            fluxp2rl = area*(rhol*(ul*ubarlrl+ubarl*ulrl) +                    &
                       ul*ubarl*rholrl + xnorm*presslrl)
            fluxp2ul = area*(rhol*(ul*ubarlul+ubarl*ulul) +                    &
                       ul*ubarl*rholul + xnorm*presslul)
            fluxp2vl = area*(rhol*(ul*ubarlvl+ubarl*ulvl) +                    &
                       ul*ubarl*rholvl + xnorm*presslvl)
            fluxp2wl = area*(rhol*(ul*ubarlwl+ubarl*ulwl) +                    &
                       ul*ubarl*rholwl + xnorm*presslwl)
            fluxp2pl = area*(rhol*(ul*ubarlpl+ubarl*ulpl) +                    &
                       ul*ubarl*rholpl + xnorm*presslpl)

            fluxp2rr = area*(rhol*(ul*ubarlrr+ubarl*ulrr) +                    &
                       ul*ubarl*rholrr + xnorm*presslrr)
            fluxp2ur = area*(rhol*(ul*ubarlur+ubarl*ulur) +                    &
                       ul*ubarl*rholur + xnorm*presslur)
            fluxp2vr = area*(rhol*(ul*ubarlvr+ubarl*ulvr) +                    &
                       ul*ubarl*rholvr + xnorm*presslvr)
            fluxp2wr = area*(rhol*(ul*ubarlwr+ubarl*ulwr) +                    &
                       ul*ubarl*rholwr + xnorm*presslwr)
            fluxp2pr = area*(rhol*(ul*ubarlpr+ubarl*ulpr) +                    &
                       ul*ubarl*rholpr + xnorm*presslpr)

!           fluxp3 = area*(rhol*vl*ubarl + ynorm*pressl)

            fluxp3rl = area*(rhol*(vl*ubarlrl+ubarl*vlrl) +                    &
                       vl*ubarl*rholrl + ynorm*presslrl)
            fluxp3ul = area*(rhol*(vl*ubarlul+ubarl*vlul) +                    &
                       vl*ubarl*rholul + ynorm*presslul)
            fluxp3vl = area*(rhol*(vl*ubarlvl+ubarl*vlvl) +                    &
                       vl*ubarl*rholvl + ynorm*presslvl)
            fluxp3wl = area*(rhol*(vl*ubarlwl+ubarl*vlwl) +                    &
                       vl*ubarl*rholwl + ynorm*presslwl)
            fluxp3pl = area*(rhol*(vl*ubarlpl+ubarl*vlpl) +                    &
                       vl*ubarl*rholpl + ynorm*presslpl)
            fluxp3rr = area*(rhol*(vl*ubarlrr+ubarl*vlrr) +                    &
                       vl*ubarl*rholrr + ynorm*presslrr)
            fluxp3ur = area*(rhol*(vl*ubarlur+ubarl*vlur) +                    &
                       vl*ubarl*rholur + ynorm*presslur)
            fluxp3vr = area*(rhol*(vl*ubarlvr+ubarl*vlvr) +                    &
                       vl*ubarl*rholvr + ynorm*presslvr)
            fluxp3wr = area*(rhol*(vl*ubarlwr+ubarl*vlwr) +                    &
                       vl*ubarl*rholwr + ynorm*presslwr)
            fluxp3pr = area*(rhol*(vl*ubarlpr+ubarl*vlpr) +                    &
                       vl*ubarl*rholpr + ynorm*presslpr)

!           fluxp4 = area*(rhol*wl*ubarl + znorm*pressl)

            fluxp4rl = area*(rhol*(wl*ubarlrl+ubarl*wlrl) +                    &
                       wl*ubarl*rholrl + znorm*presslrl)
            fluxp4ul = area*(rhol*(wl*ubarlul+ubarl*wlul) +                    &
                       wl*ubarl*rholul + znorm*presslul)
            fluxp4vl = area*(rhol*(wl*ubarlvl+ubarl*wlvl) +                    &
                       wl*ubarl*rholvl + znorm*presslvl)
            fluxp4wl = area*(rhol*(wl*ubarlwl+ubarl*wlwl) +                    &
                       wl*ubarl*rholwl + znorm*presslwl)
            fluxp4pl = area*(rhol*(wl*ubarlpl+ubarl*wlpl) +                    &
                       wl*ubarl*rholpl + znorm*presslpl)

            fluxp4rr = area*(rhol*(wl*ubarlrr+ubarl*wlrr) +                    &
                       wl*ubarl*rholrr + znorm*presslrr)
            fluxp4ur = area*(rhol*(wl*ubarlur+ubarl*wlur) +                    &
                       wl*ubarl*rholur + znorm*presslur)
            fluxp4vr = area*(rhol*(wl*ubarlvr+ubarl*wlvr) +                    &
                       wl*ubarl*rholvr + znorm*presslvr)
            fluxp4wr = area*(rhol*(wl*ubarlwr+ubarl*wlwr) +                    &
                       wl*ubarl*rholwr + znorm*presslwr)
            fluxp4pr = area*(rhol*(wl*ubarlpr+ubarl*wlpr) +                    &
                       wl*ubarl*rholpr + znorm*presslpr)

!           fluxp5 = area*(enrgyl + pressl)*ubarl + area*face_speed*pressl

            fluxp5rl = area*((enrgyl + pressl)*ubarlrl +                       &
                       ubarl*(enrgylrl + presslrl)) + area*face_speed*presslrl
            fluxp5ul = area*((enrgyl + pressl)*ubarlul +                       &
                       ubarl*(enrgylul + presslul)) + area*face_speed*presslul
            fluxp5vl = area*((enrgyl + pressl)*ubarlvl +                       &
                       ubarl*(enrgylvl + presslvl)) + area*face_speed*presslvl
            fluxp5wl = area*((enrgyl + pressl)*ubarlwl +                       &
                       ubarl*(enrgylwl + presslwl)) + area*face_speed*presslwl
            fluxp5pl = area*((enrgyl + pressl)*ubarlpl +                       &
                       ubarl*(enrgylpl + presslpl)) + area*face_speed*presslpl

            fluxp5rr = area*((enrgyl + pressl)*ubarlrr +                       &
                       ubarl*(enrgylrr + presslrr)) + area*face_speed*presslrr
            fluxp5ur = area*((enrgyl + pressl)*ubarlur +                       &
                       ubarl*(enrgylur + presslur)) + area*face_speed*presslur
            fluxp5vr = area*((enrgyl + pressl)*ubarlvr +                       &
                       ubarl*(enrgylvr + presslvr)) + area*face_speed*presslvr
            fluxp5wr = area*((enrgyl + pressl)*ubarlwr +                       &
                       ubarl*(enrgylwr + presslwr)) + area*face_speed*presslwr
            fluxp5pr = area*((enrgyl + pressl)*ubarlpr +                       &
                       ubarl*(enrgylpr + presslpr)) + area*face_speed*presslpr

! Now the right side

!           fluxm1 = area*rhor*ubarr

            fluxm1rl = area*(rhor*ubarrrl + ubarr*rhorrl)
            fluxm1ul = area*(rhor*ubarrul + ubarr*rhorul)
            fluxm1vl = area*(rhor*ubarrvl + ubarr*rhorvl)
            fluxm1wl = area*(rhor*ubarrwl + ubarr*rhorwl)
            fluxm1pl = area*(rhor*ubarrpl + ubarr*rhorpl)

            fluxm1rr = area*(rhor*ubarrrr + ubarr*rhorrr)
            fluxm1ur = area*(rhor*ubarrur + ubarr*rhorur)
            fluxm1vr = area*(rhor*ubarrvr + ubarr*rhorvr)
            fluxm1wr = area*(rhor*ubarrwr + ubarr*rhorwr)
            fluxm1pr = area*(rhor*ubarrpr + ubarr*rhorpr)

!           fluxm2 = area*(rhor*ur*ubarr + xnorm*pressr)

            fluxm2rl = area*(rhor*(ur*ubarrrl+ubarr*urrl) +                    &
                       ur*ubarr*rhorrl + xnorm*pressrrl)
            fluxm2ul = area*(rhor*(ur*ubarrul+ubarr*urul) +                    &
                       ur*ubarr*rhorul + xnorm*pressrul)
            fluxm2vl = area*(rhor*(ur*ubarrvl+ubarr*urvl) +                    &
                       ur*ubarr*rhorvl + xnorm*pressrvl)
            fluxm2wl = area*(rhor*(ur*ubarrwl+ubarr*urwl) +                    &
                       ur*ubarr*rhorwl + xnorm*pressrwl)
            fluxm2pl = area*(rhor*(ur*ubarrpl+ubarr*urpl) +                    &
                       ur*ubarr*rhorpl + xnorm*pressrpl)

            fluxm2rr = area*(rhor*(ur*ubarrrr+ubarr*urrr) +                    &
                       ur*ubarr*rhorrr + xnorm*pressrrr)
            fluxm2ur = area*(rhor*(ur*ubarrur+ubarr*urur) +                    &
                       ur*ubarr*rhorur + xnorm*pressrur)
            fluxm2vr = area*(rhor*(ur*ubarrvr+ubarr*urvr) +                    &
                       ur*ubarr*rhorvr + xnorm*pressrvr)
            fluxm2wr = area*(rhor*(ur*ubarrwr+ubarr*urwr) +                    &
                       ur*ubarr*rhorwr + xnorm*pressrwr)
            fluxm2pr = area*(rhor*(ur*ubarrpr+ubarr*urpr) +                    &
                       ur*ubarr*rhorpr + xnorm*pressrpr)

!           fluxm3 = area*(rhor*vr*ubarr + ynorm*pressr)

            fluxm3rl = area*(rhor*(vr*ubarrrl+ubarr*vrrl) +                    &
                       vr*ubarr*rhorrl + ynorm*pressrrl)
            fluxm3ul = area*(rhor*(vr*ubarrul+ubarr*vrul) +                    &
                       vr*ubarr*rhorul + ynorm*pressrul)
            fluxm3vl = area*(rhor*(vr*ubarrvl+ubarr*vrvl) +                    &
                       vr*ubarr*rhorvl + ynorm*pressrvl)
            fluxm3wl = area*(rhor*(vr*ubarrwl+ubarr*vrwl) +                    &
                       vr*ubarr*rhorwl + ynorm*pressrwl)
            fluxm3pl = area*(rhor*(vr*ubarrpl+ubarr*vrpl) +                    &
                       vr*ubarr*rhorpl + ynorm*pressrpl)

            fluxm3rr = area*(rhor*(vr*ubarrrr+ubarr*vrrr) +                    &
                       vr*ubarr*rhorrr + ynorm*pressrrr)
            fluxm3ur = area*(rhor*(vr*ubarrur+ubarr*vrur) +                    &
                       vr*ubarr*rhorur + ynorm*pressrur)
            fluxm3vr = area*(rhor*(vr*ubarrvr+ubarr*vrvr) +                    &
                       vr*ubarr*rhorvr + ynorm*pressrvr)
            fluxm3wr = area*(rhor*(vr*ubarrwr+ubarr*vrwr) +                    &
                       vr*ubarr*rhorwr + ynorm*pressrwr)
            fluxm3pr = area*(rhor*(vr*ubarrpr+ubarr*vrpr) +                    &
                       vr*ubarr*rhorpr + ynorm*pressrpr)

!           fluxm4 = area*(rhor*wr*ubarr + znorm*pressr)

            fluxm4rl = area*(rhor*(wr*ubarrrl+ubarr*wrrl) +                    &
                       wr*ubarr*rhorrl + znorm*pressrrl)
            fluxm4ul = area*(rhor*(wr*ubarrul+ubarr*wrul) +                    &
                       wr*ubarr*rhorul + znorm*pressrul)
            fluxm4vl = area*(rhor*(wr*ubarrvl+ubarr*wrvl) +                    &
                       wr*ubarr*rhorvl + znorm*pressrvl)
            fluxm4wl = area*(rhor*(wr*ubarrwl+ubarr*wrwl) +                    &
                       wr*ubarr*rhorwl + znorm*pressrwl)
            fluxm4pl = area*(rhor*(wr*ubarrpl+ubarr*wrpl) +                    &
                       wr*ubarr*rhorpl + znorm*pressrpl)

            fluxm4rr = area*(rhor*(wr*ubarrrr+ubarr*wrrr) +                    &
                       wr*ubarr*rhorrr + znorm*pressrrr)
            fluxm4ur = area*(rhor*(wr*ubarrur+ubarr*wrur) +                    &
                       wr*ubarr*rhorur + znorm*pressrur)
            fluxm4vr = area*(rhor*(wr*ubarrvr+ubarr*wrvr) +                    &
                       wr*ubarr*rhorvr + znorm*pressrvr)
            fluxm4wr = area*(rhor*(wr*ubarrwr+ubarr*wrwr) +                    &
                       wr*ubarr*rhorwr + znorm*pressrwr)
            fluxm4pr = area*(rhor*(wr*ubarrpr+ubarr*wrpr) +                    &
                       wr*ubarr*rhorpr + znorm*pressrpr)

!           fluxm5 = area*(enrgyr + pressr)*ubarr + area*face_speed*pressr

            fluxm5rl = area*((enrgyr + pressr)*ubarrrl +                       &
                       ubarr*(enrgyrrl + pressrrl)) + area*face_speed*pressrrl
            fluxm5ul = area*((enrgyr + pressr)*ubarrul +                       &
                       ubarr*(enrgyrul + pressrul)) + area*face_speed*pressrul
            fluxm5vl = area*((enrgyr + pressr)*ubarrvl +                       &
                       ubarr*(enrgyrvl + pressrvl)) + area*face_speed*pressrvl
            fluxm5wl = area*((enrgyr + pressr)*ubarrwl +                       &
                       ubarr*(enrgyrwl + pressrwl)) + area*face_speed*pressrwl
            fluxm5pl = area*((enrgyr + pressr)*ubarrpl +                       &
                       ubarr*(enrgyrpl + pressrpl)) + area*face_speed*pressrpl

            fluxm5rr = area*((enrgyr + pressr)*ubarrrr +                       &
                       ubarr*(enrgyrrr + pressrrr)) + area*face_speed*pressrrr
            fluxm5ur = area*((enrgyr + pressr)*ubarrur +                       &
                       ubarr*(enrgyrur + pressrur)) + area*face_speed*pressrur
            fluxm5vr = area*((enrgyr + pressr)*ubarrvr +                       &
                       ubarr*(enrgyrvr + pressrvr)) + area*face_speed*pressrvr
            fluxm5wr = area*((enrgyr + pressr)*ubarrwr +                       &
                       ubarr*(enrgyrwr + pressrwr)) + area*face_speed*pressrwr
            fluxm5pr = area*((enrgyr + pressr)*ubarrpr +                       &
                       ubarr*(enrgyrpr + pressrpr)) + area*face_speed*pressrpr

!         flux1 = 0.5_dp*(fluxp1 + fluxm1 - area*t1)
!         flux2 = 0.5_dp*(fluxp2 + fluxm2 - area*t2)
!         flux3 = 0.5_dp*(fluxp3 + fluxm3 - area*t3)
!         flux4 = 0.5_dp*(fluxp4 + fluxm4 - area*t4)
!         flux5 = 0.5_dp*(fluxp5 + fluxm5 - area*t5)

          flux1rl = 0.5_dp*(fluxp1rl + fluxm1rl)
          flux1ul = 0.5_dp*(fluxp1ul + fluxm1ul)
          flux1vl = 0.5_dp*(fluxp1vl + fluxm1vl)
          flux1wl = 0.5_dp*(fluxp1wl + fluxm1wl)
          flux1pl = 0.5_dp*(fluxp1pl + fluxm1pl)
          flux1rr = 0.5_dp*(fluxp1rr + fluxm1rr)
          flux1ur = 0.5_dp*(fluxp1ur + fluxm1ur)
          flux1vr = 0.5_dp*(fluxp1vr + fluxm1vr)
          flux1wr = 0.5_dp*(fluxp1wr + fluxm1wr)
          flux1pr = 0.5_dp*(fluxp1pr + fluxm1pr)

          flux2rl = 0.5_dp*(fluxp2rl + fluxm2rl)
          flux2ul = 0.5_dp*(fluxp2ul + fluxm2ul)
          flux2vl = 0.5_dp*(fluxp2vl + fluxm2vl)
          flux2wl = 0.5_dp*(fluxp2wl + fluxm2wl)
          flux2pl = 0.5_dp*(fluxp2pl + fluxm2pl)
          flux2rr = 0.5_dp*(fluxp2rr + fluxm2rr)
          flux2ur = 0.5_dp*(fluxp2ur + fluxm2ur)
          flux2vr = 0.5_dp*(fluxp2vr + fluxm2vr)
          flux2wr = 0.5_dp*(fluxp2wr + fluxm2wr)
          flux2pr = 0.5_dp*(fluxp2pr + fluxm2pr)

          flux3rl = 0.5_dp*(fluxp3rl + fluxm3rl)
          flux3ul = 0.5_dp*(fluxp3ul + fluxm3ul)
          flux3vl = 0.5_dp*(fluxp3vl + fluxm3vl)
          flux3wl = 0.5_dp*(fluxp3wl + fluxm3wl)
          flux3pl = 0.5_dp*(fluxp3pl + fluxm3pl)
          flux3rr = 0.5_dp*(fluxp3rr + fluxm3rr)
          flux3ur = 0.5_dp*(fluxp3ur + fluxm3ur)
          flux3vr = 0.5_dp*(fluxp3vr + fluxm3vr)
          flux3wr = 0.5_dp*(fluxp3wr + fluxm3wr)
          flux3pr = 0.5_dp*(fluxp3pr + fluxm3pr)

          flux4rl = 0.5_dp*(fluxp4rl + fluxm4rl)
          flux4ul = 0.5_dp*(fluxp4ul + fluxm4ul)
          flux4vl = 0.5_dp*(fluxp4vl + fluxm4vl)
          flux4wl = 0.5_dp*(fluxp4wl + fluxm4wl)
          flux4pl = 0.5_dp*(fluxp4pl + fluxm4pl)
          flux4rr = 0.5_dp*(fluxp4rr + fluxm4rr)
          flux4ur = 0.5_dp*(fluxp4ur + fluxm4ur)
          flux4vr = 0.5_dp*(fluxp4vr + fluxm4vr)
          flux4wr = 0.5_dp*(fluxp4wr + fluxm4wr)
          flux4pr = 0.5_dp*(fluxp4pr + fluxm4pr)

          flux5rl = 0.5_dp*(fluxp5rl + fluxm5rl)
          flux5ul = 0.5_dp*(fluxp5ul + fluxm5ul)
          flux5vl = 0.5_dp*(fluxp5vl + fluxm5vl)
          flux5wl = 0.5_dp*(fluxp5wl + fluxm5wl)
          flux5pl = 0.5_dp*(fluxp5pl + fluxm5pl)
          flux5rr = 0.5_dp*(fluxp5rr + fluxm5rr)
          flux5ur = 0.5_dp*(fluxp5ur + fluxm5ur)
          flux5vr = 0.5_dp*(fluxp5vr + fluxm5vr)
          flux5wr = 0.5_dp*(fluxp5wr + fluxm5wr)
          flux5pr = 0.5_dp*(fluxp5pr + fluxm5pr)

!  These last 50 equations are the dfp's and dfm's we're
!  looking for, but they're w.r.t. primitive variables.

!  We need to convert these last results into conservative
!  variables, using one last chain-rule.

!  q = primitive
!  Q = conservative

!  First the left transformation

        q1Q1l = 1.0_dp
        q1Q2l = 0.0_dp
        q1Q3l = 0.0_dp
        q1Q4l = 0.0_dp
        q1Q5l = 0.0_dp

        q2Q1l = - ul / rhol
        q2Q2l = 1.0_dp / rhol
        q2Q3l = 0.0_dp
        q2Q4l = 0.0_dp
        q2Q5l = 0.0_dp

        q3Q1l = - vl / rhol
        q3Q2l = 0.0_dp
        q3Q3l = 1.0_dp / rhol
        q3Q4l = 0.0_dp
        q3Q5l = 0.0_dp

        q4Q1l = - wl / rhol
        q4Q2l = 0.0_dp
        q4Q3l = 0.0_dp
        q4Q4l = 1.0_dp / rhol
        q4Q5l = 0.0_dp

        q5Q1l = gm1 / 2.0_dp * (ul*ul + vl*vl + wl*wl)
        q5Q2l = - gm1 * ul
        q5Q3l = - gm1 * vl
        q5Q4l = - gm1 * wl
        q5Q5l = gm1

        dfp(1,1) = flux1rl*q1Q1l + flux1ul*q2Q1l + flux1vl*q3Q1l               &
                 + flux1wl*q4Q1l + flux1pl*q5Q1l

        dfp(1,2) = flux1rl*q1Q2l + flux1ul*q2Q2l + flux1vl*q3Q2l               &
                 + flux1wl*q4Q2l + flux1pl*q5Q2l

        dfp(1,3) = flux1rl*q1Q3l + flux1ul*q2Q3l + flux1vl*q3Q3l               &
                 + flux1wl*q4Q3l + flux1pl*q5Q3l

        dfp(1,4) = flux1rl*q1Q4l + flux1ul*q2Q4l + flux1vl*q3Q4l               &
                 + flux1wl*q4Q4l + flux1pl*q5Q4l

        dfp(1,5) = flux1rl*q1Q5l + flux1ul*q2Q5l + flux1vl*q3Q5l               &
                 + flux1wl*q4Q5l + flux1pl*q5Q5l

        dfp(2,1) = flux2rl*q1Q1l + flux2ul*q2Q1l + flux2vl*q3Q1l               &
                 + flux2wl*q4Q1l + flux2pl*q5Q1l

        dfp(2,2) = flux2rl*q1Q2l + flux2ul*q2Q2l + flux2vl*q3Q2l               &
                 + flux2wl*q4Q2l + flux2pl*q5Q2l

        dfp(2,3) = flux2rl*q1Q3l + flux2ul*q2Q3l + flux2vl*q3Q3l               &
                 + flux2wl*q4Q3l + flux2pl*q5Q3l

        dfp(2,4) = flux2rl*q1Q4l + flux2ul*q2Q4l + flux2vl*q3Q4l               &
                 + flux2wl*q4Q4l + flux2pl*q5Q4l

        dfp(2,5) = flux2rl*q1Q5l + flux2ul*q2Q5l + flux2vl*q3Q5l               &
                 + flux2wl*q4Q5l + flux2pl*q5Q5l

        dfp(3,1) = flux3rl*q1Q1l + flux3ul*q2Q1l + flux3vl*q3Q1l               &
                 + flux3wl*q4Q1l + flux3pl*q5Q1l

        dfp(3,2) = flux3rl*q1Q2l + flux3ul*q2Q2l + flux3vl*q3Q2l               &
                 + flux3wl*q4Q2l + flux3pl*q5Q2l

        dfp(3,3) = flux3rl*q1Q3l + flux3ul*q2Q3l + flux3vl*q3Q3l               &
                 + flux3wl*q4Q3l + flux3pl*q5Q3l

        dfp(3,4) = flux3rl*q1Q4l + flux3ul*q2Q4l + flux3vl*q3Q4l               &
                 + flux3wl*q4Q4l + flux3pl*q5Q4l
        dfp(3,5) = flux3rl*q1Q5l + flux3ul*q2Q5l + flux3vl*q3Q5l               &
                 + flux3wl*q4Q5l + flux3pl*q5Q5l

        dfp(4,1) = flux4rl*q1Q1l + flux4ul*q2Q1l + flux4vl*q3Q1l               &
                 + flux4wl*q4Q1l + flux4pl*q5Q1l

        dfp(4,2) = flux4rl*q1Q2l + flux4ul*q2Q2l + flux4vl*q3Q2l               &
                 + flux4wl*q4Q2l + flux4pl*q5Q2l
        dfp(4,3) = flux4rl*q1Q3l + flux4ul*q2Q3l + flux4vl*q3Q3l               &
                 + flux4wl*q4Q3l + flux4pl*q5Q3l

        dfp(4,4) = flux4rl*q1Q4l + flux4ul*q2Q4l + flux4vl*q3Q4l               &
                 + flux4wl*q4Q4l + flux4pl*q5Q4l

        dfp(4,5) = flux4rl*q1Q5l + flux4ul*q2Q5l + flux4vl*q3Q5l               &
                 + flux4wl*q4Q5l + flux4pl*q5Q5l
        dfp(5,1) = flux5rl*q1Q1l + flux5ul*q2Q1l + flux5vl*q3Q1l               &
                 + flux5wl*q4Q1l + flux5pl*q5Q1l

        dfp(5,2) = flux5rl*q1Q2l + flux5ul*q2Q2l + flux5vl*q3Q2l               &
                 + flux5wl*q4Q2l + flux5pl*q5Q2l

        dfp(5,3) = flux5rl*q1Q3l + flux5ul*q2Q3l + flux5vl*q3Q3l               &
                 + flux5wl*q4Q3l + flux5pl*q5Q3l

        dfp(5,4) = flux5rl*q1Q4l + flux5ul*q2Q4l + flux5vl*q3Q4l               &
                 + flux5wl*q4Q4l + flux5pl*q5Q4l

        dfp(5,5) = flux5rl*q1Q5l + flux5ul*q2Q5l + flux5vl*q3Q5l               &
                 + flux5wl*q4Q5l + flux5pl*q5Q5l

!  Now the right transformation

        q1Q1r = 1.0_dp
        q1Q2r = 0.0_dp
        q1Q3r = 0.0_dp
        q1Q4r = 0.0_dp
        q1Q5r = 0.0_dp

        q2Q1r = - ur / rhor
        q2Q2r = 1.0_dp / rhor
        q2Q3r = 0.0_dp
        q2Q4r = 0.0_dp
        q2Q5r = 0.0_dp

        q3Q1r = - vr / rhor
        q3Q2r = 0.0_dp
        q3Q3r = 1.0_dp / rhor
        q3Q4r = 0.0_dp
        q3Q5r = 0.0_dp

        q4Q1r = - wr / rhor
        q4Q2r = 0.0_dp
        q4Q3r = 0.0_dp
        q4Q4r = 1.0_dp / rhor
        q4Q5r = 0.0_dp

        q5Q1r = gm1 / 2.0_dp * (ur*ur + vr*vr + wr*wr)
        q5Q2r = - gm1 * ur
        q5Q3r = - gm1 * vr
        q5Q4r = - gm1 * wr
        q5Q5r = gm1

        dfm(1,1) = flux1rr*q1Q1r + flux1ur*q2Q1r + flux1vr*q3Q1r               &
                 + flux1wr*q4Q1r + flux1pr*q5Q1r

        dfm(1,2) = flux1rr*q1Q2r + flux1ur*q2Q2r + flux1vr*q3Q2r               &
                 + flux1wr*q4Q2r + flux1pr*q5Q2r

        dfm(1,3) = flux1rr*q1Q3r + flux1ur*q2Q3r + flux1vr*q3Q3r               &
                 + flux1wr*q4Q3r + flux1pr*q5Q3r

        dfm(1,4) = flux1rr*q1Q4r + flux1ur*q2Q4r + flux1vr*q3Q4r               &
                 + flux1wr*q4Q4r + flux1pr*q5Q4r

        dfm(1,5) = flux1rr*q1Q5r + flux1ur*q2Q5r + flux1vr*q3Q5r               &
                 + flux1wr*q4Q5r + flux1pr*q5Q5r

        dfm(2,1) = flux2rr*q1Q1r + flux2ur*q2Q1r + flux2vr*q3Q1r               &
                 + flux2wr*q4Q1r + flux2pr*q5Q1r

        dfm(2,2) = flux2rr*q1Q2r + flux2ur*q2Q2r + flux2vr*q3Q2r               &
                 + flux2wr*q4Q2r + flux2pr*q5Q2r

        dfm(2,3) = flux2rr*q1Q3r + flux2ur*q2Q3r + flux2vr*q3Q3r               &
                 + flux2wr*q4Q3r + flux2pr*q5Q3r

        dfm(2,4) = flux2rr*q1Q4r + flux2ur*q2Q4r + flux2vr*q3Q4r               &
                 + flux2wr*q4Q4r + flux2pr*q5Q4r

        dfm(2,5) = flux2rr*q1Q5r + flux2ur*q2Q5r + flux2vr*q3Q5r               &
                 + flux2wr*q4Q5r + flux2pr*q5Q5r

        dfm(3,1) = flux3rr*q1Q1r + flux3ur*q2Q1r + flux3vr*q3Q1r               &
                 + flux3wr*q4Q1r + flux3pr*q5Q1r

        dfm(3,2) = flux3rr*q1Q2r + flux3ur*q2Q2r + flux3vr*q3Q2r               &
                 + flux3wr*q4Q2r + flux3pr*q5Q2r

        dfm(3,3) = flux3rr*q1Q3r + flux3ur*q2Q3r + flux3vr*q3Q3r               &
                 + flux3wr*q4Q3r + flux3pr*q5Q3r

        dfm(3,4) = flux3rr*q1Q4r + flux3ur*q2Q4r + flux3vr*q3Q4r               &
                 + flux3wr*q4Q4r + flux3pr*q5Q4r

        dfm(3,5) = flux3rr*q1Q5r + flux3ur*q2Q5r + flux3vr*q3Q5r               &
                 + flux3wr*q4Q5r + flux3pr*q5Q5r

        dfm(4,1) = flux4rr*q1Q1r + flux4ur*q2Q1r + flux4vr*q3Q1r               &
                 + flux4wr*q4Q1r + flux4pr*q5Q1r

        dfm(4,2) = flux4rr*q1Q2r + flux4ur*q2Q2r + flux4vr*q3Q2r               &
                 + flux4wr*q4Q2r + flux4pr*q5Q2r

        dfm(4,3) = flux4rr*q1Q3r + flux4ur*q2Q3r + flux4vr*q3Q3r               &
                 + flux4wr*q4Q3r + flux4pr*q5Q3r

        dfm(4,4) = flux4rr*q1Q4r + flux4ur*q2Q4r + flux4vr*q3Q4r               &
                 + flux4wr*q4Q4r + flux4pr*q5Q4r

        dfm(4,5) = flux4rr*q1Q5r + flux4ur*q2Q5r + flux4vr*q3Q5r               &
                 + flux4wr*q4Q5r + flux4pr*q5Q5r

        dfm(5,1) = flux5rr*q1Q1r + flux5ur*q2Q1r + flux5vr*q3Q1r               &
                 + flux5wr*q4Q1r + flux5pr*q5Q1r

        dfm(5,2) = flux5rr*q1Q2r + flux5ur*q2Q2r + flux5vr*q3Q2r               &
                 + flux5wr*q4Q2r + flux5pr*q5Q2r

        dfm(5,3) = flux5rr*q1Q3r + flux5ur*q2Q3r + flux5vr*q3Q3r               &
                 + flux5wr*q4Q3r + flux5pr*q5Q3r

        dfm(5,4) = flux5rr*q1Q4r + flux5ur*q2Q4r + flux5vr*q3Q4r               &
                 + flux5wr*q4Q4r + flux5pr*q5Q4r

        dfm(5,5) = flux5rr*q1Q5r + flux5ur*q2Q5r + flux5vr*q3Q5r               &
                 + flux5wr*q4Q5r + flux5pr*q5Q5r

        if(node1 <= nnodes0) then
          idiag = g2m(node1)
          do k = 1, 5
            do j = 1, 5
              a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfp(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          ioff  = fhelp(2,n)
          do k = 1, 5
            do j = 1, 5
              a_off(j,k,ioff)  = a_off(j,k,ioff) - dfp(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          idiag = g2m(node2)
          do k = 1, 5
            do j = 1, 5
              a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfm(j,k)
            end do
          end do
        end if

        if(node1 <= nnodes0) then
          ioff  = fhelp(1,n)
          do k = 1, 5
            do j = 1, 5
              a_off(j,k,ioff)  = a_off(j,k,ioff) + dfm(j,k)
            end do
          end do
        end if

      end do scanedges

!  Convert back to conserved variables

      call ptoe(qdim,qnode,n_tot,0)

! That does it for the central difference piece, now do the dissipation

! Initialize the second diff to zero - not needed when forcing 1st order LHS

!   pw = my_0

! Form the second differences - not needed when forcing 1st order LHS
! These are not linearized but merely used to run the pressure switch

!   form_second_diff : do n = 1, nedgeloc

!     node1 = eptr(1,n)
!     node2 = eptr(2,n)

!     rho1   = qnode(1,node1)
!     u1     = qnode(2,node1) / rho1
!     v1     = qnode(3,node1) / rho1
!     w1     = qnode(4,node1) / rho1
!     q21    = u1*u1 + v1*v1 + w1*w1
!     press1 = gm1*(qnode(5,node1) - my_half*rho1*q21)

!     rho2   = qnode(1,node2)
!     u2     = qnode(2,node2) / rho2
!     v2     = qnode(3,node2) / rho2
!     w2     = qnode(4,node2) / rho2
!     q22    = u2*u2 + v2*v2 + w2*w2
!     press2 = gm1*(qnode(5,node2) - my_half*rho2*q22)

!     p1 = press1 - press2
!     p2 = press1 + press2

!     if ( node1 <= nnodes0 ) then
!       pw(1,node1) = pw(1,node1) - p1
!       pw(2,node1) = pw(2,node1) + p2
!     endif

!     if ( node2 <= nnodes0 ) then
!       pw(1,node2) = pw(1,node2) + p1
!       pw(2,node2) = pw(2,node2) + p2
!     endif

!   end do form_second_diff

! Exchange across processors - not needed when forcing 1st order LHS
!   call lmpi_xfer(pw)

! Form the pressure quotient - not needed when forcing 1st order LHS
!   jst_switch(1,:) = abs( pw(1,:) / pw(2,:) )

! Now linearize the nearest neighbor pieces

    dissipation_contribs : do n = 1, nedgeloc

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      xnorm  = xn(n)
      ynorm  = yn(n)
      znorm  = zn(n)
      area   = ra(n)

      rho1   = qnode(1,node1)
        rho1q11 = my_1
        rho1q12 = my_0
        rho1q13 = my_0
        rho1q14 = my_0
        rho1q15 = my_0

        rho1q21 = my_0
        rho1q22 = my_0
        rho1q23 = my_0
        rho1q24 = my_0
        rho1q25 = my_0

      u1     = qnode(2,node1) / rho1
        u1q11 = (-qnode(2,node1)*rho1q11) / rho1 / rho1
        u1q12 = (rho1 - qnode(2,node1)*rho1q12) / rho1 / rho1
        u1q13 = my_0
        u1q14 = my_0
        u1q15 = my_0

        u1q21 = my_0
        u1q22 = my_0
        u1q23 = my_0
        u1q24 = my_0
        u1q25 = my_0

      v1     = qnode(3,node1) / rho1
        v1q11 = (-qnode(3,node1)*rho1q11) / rho1 / rho1
        v1q12 = my_0
        v1q13 = (rho1 - qnode(3,node1)*rho1q13) / rho1 / rho1
        v1q14 = my_0
        v1q15 = my_0

        v1q21 = my_0
        v1q22 = my_0
        v1q23 = my_0
        v1q24 = my_0
        v1q25 = my_0

      w1     = qnode(4,node1) / rho1
        w1q11 = (-qnode(4,node1)*rho1q11) / rho1 / rho1
        w1q12 = my_0
        w1q13 = my_0
        w1q14 = (rho1 - qnode(4,node1)*rho1q14) / rho1 / rho1
        w1q15 = my_0

        w1q21 = my_0
        w1q22 = my_0
        w1q23 = my_0
        w1q24 = my_0
        w1q25 = my_0

      q21    = u1*u1 + v1*v1 + w1*w1
        q21q11 = my_2*u1*u1q11 + my_2*v1*v1q11 + my_2*w1*w1q11
        q21q12 = my_2*u1*u1q12 + my_2*v1*v1q12 + my_2*w1*w1q12
        q21q13 = my_2*u1*u1q13 + my_2*v1*v1q13 + my_2*w1*w1q13
        q21q14 = my_2*u1*u1q14 + my_2*v1*v1q14 + my_2*w1*w1q14
        q21q15 = my_2*u1*u1q15 + my_2*v1*v1q15 + my_2*w1*w1q15

        q21q21 = my_2*u1*u1q21 + my_2*v1*v1q21 + my_2*w1*w1q21
        q21q22 = my_2*u1*u1q22 + my_2*v1*v1q22 + my_2*w1*w1q22
        q21q23 = my_2*u1*u1q23 + my_2*v1*v1q23 + my_2*w1*w1q23
        q21q24 = my_2*u1*u1q24 + my_2*v1*v1q24 + my_2*w1*w1q24
        q21q25 = my_2*u1*u1q25 + my_2*v1*v1q25 + my_2*w1*w1q25

      press1 = gm1*(qnode(5,node1) - my_half*rho1*q21)
        press1q11 = gm1*(     -my_half*(rho1*q21q11 + q21*rho1q11))
        press1q12 = gm1*(     -my_half*(rho1*q21q12 + q21*rho1q12))
        press1q13 = gm1*(     -my_half*(rho1*q21q13 + q21*rho1q13))
        press1q14 = gm1*(     -my_half*(rho1*q21q14 + q21*rho1q14))
        press1q15 = gm1*(my_1 -my_half*(rho1*q21q15 + q21*rho1q15))

        press1q21 = gm1*(     -my_half*(rho1*q21q21 + q21*rho1q21))
        press1q22 = gm1*(     -my_half*(rho1*q21q22 + q21*rho1q22))
        press1q23 = gm1*(     -my_half*(rho1*q21q23 + q21*rho1q23))
        press1q24 = gm1*(     -my_half*(rho1*q21q24 + q21*rho1q24))
        press1q25 = gm1*(     -my_half*(rho1*q21q25 + q21*rho1q25))

      rho2   = qnode(1,node2)
        rho2q11 = my_0
        rho2q12 = my_0
        rho2q13 = my_0
        rho2q14 = my_0
        rho2q15 = my_0

        rho2q21 = my_1
        rho2q22 = my_0
        rho2q23 = my_0
        rho2q24 = my_0
        rho2q25 = my_0

      u2     = qnode(2,node2) / rho2
        u2q11 = my_0
        u2q12 = my_0
        u2q13 = my_0
        u2q14 = my_0
        u2q15 = my_0

        u2q21 = (-qnode(2,node2)*rho2q21) / rho2 / rho2
        u2q22 = (rho2 - qnode(2,node2)*rho2q22) / rho2 / rho2
        u2q23 = my_0
        u2q24 = my_0
        u2q25 = my_0

      v2     = qnode(3,node2) / rho2
        v2q11 = my_0
        v2q12 = my_0
        v2q13 = my_0
        v2q14 = my_0
        v2q15 = my_0

        v2q21 = (-qnode(3,node2)*rho2q21) / rho2 / rho2
        v2q22 = my_0
        v2q23 = (rho2 - qnode(3,node2)*rho2q23) / rho2 / rho2
        v2q24 = my_0
        v2q25 = my_0

      w2     = qnode(4,node2) / rho2
        w2q11 = my_0
        w2q12 = my_0
        w2q13 = my_0
        w2q14 = my_0
        w2q15 = my_0

        w2q21 = (-qnode(4,node2)*rho2q21) / rho2 / rho2
        w2q22 = my_0
        w2q23 = my_0
        w2q24 = (rho2 - qnode(4,node2)*rho2q24) / rho2 / rho2
        w2q25 = my_0

      q22    = u2*u2 + v2*v2 + w2*w2
        q22q11 = my_2*u2*u2q11 + my_2*v2*v2q11 + my_2*w2*w2q11
        q22q12 = my_2*u2*u2q12 + my_2*v2*v2q12 + my_2*w2*w2q12
        q22q13 = my_2*u2*u2q13 + my_2*v2*v2q13 + my_2*w2*w2q13
        q22q14 = my_2*u2*u2q14 + my_2*v2*v2q14 + my_2*w2*w2q14
        q22q15 = my_2*u2*u2q15 + my_2*v2*v2q15 + my_2*w2*w2q15

        q22q21 = my_2*u2*u2q21 + my_2*v2*v2q21 + my_2*w2*w2q21
        q22q22 = my_2*u2*u2q22 + my_2*v2*v2q22 + my_2*w2*w2q22
        q22q23 = my_2*u2*u2q23 + my_2*v2*v2q23 + my_2*w2*w2q23
        q22q24 = my_2*u2*u2q24 + my_2*v2*v2q24 + my_2*w2*w2q24
        q22q25 = my_2*u2*u2q25 + my_2*v2*v2q25 + my_2*w2*w2q25

      press2 = gm1*(qnode(5,node2) - my_half*rho2*q22)
        press2q11 = gm1*(     -my_half*(rho2*q22q11 + q22*rho2q11))
        press2q12 = gm1*(     -my_half*(rho2*q22q12 + q22*rho2q12))
        press2q13 = gm1*(     -my_half*(rho2*q22q13 + q22*rho2q13))
        press2q14 = gm1*(     -my_half*(rho2*q22q14 + q22*rho2q14))
        press2q15 = gm1*(     -my_half*(rho2*q22q15 + q22*rho2q15))

        press2q21 = gm1*(     -my_half*(rho2*q22q21 + q22*rho2q21))
        press2q22 = gm1*(     -my_half*(rho2*q22q22 + q22*rho2q22))
        press2q23 = gm1*(     -my_half*(rho2*q22q23 + q22*rho2q23))
        press2q24 = gm1*(     -my_half*(rho2*q22q24 + q22*rho2q24))
        press2q25 = gm1*(my_1 -my_half*(rho2*q22q25 + q22*rho2q25))

      qs10 = xnorm*u1 + ynorm*v1 + znorm*w1
        qs10q11 = xnorm*u1q11 + ynorm*v1q11 + znorm*w1q11
        qs10q12 = xnorm*u1q12 + ynorm*v1q12 + znorm*w1q12
        qs10q13 = xnorm*u1q13 + ynorm*v1q13 + znorm*w1q13
        qs10q14 = xnorm*u1q14 + ynorm*v1q14 + znorm*w1q14
        qs10q15 = xnorm*u1q15 + ynorm*v1q15 + znorm*w1q15

        qs10q21 = xnorm*u1q21 + ynorm*v1q21 + znorm*w1q21
        qs10q22 = xnorm*u1q22 + ynorm*v1q22 + znorm*w1q22
        qs10q23 = xnorm*u1q23 + ynorm*v1q23 + znorm*w1q23
        qs10q24 = xnorm*u1q24 + ynorm*v1q24 + znorm*w1q24
        qs10q25 = xnorm*u1q25 + ynorm*v1q25 + znorm*w1q25

      qs20 = xnorm*u2 + ynorm*v2 + znorm*w2
        qs20q11 = xnorm*u2q11 + ynorm*v2q11 + znorm*w2q11
        qs20q12 = xnorm*u2q12 + ynorm*v2q12 + znorm*w2q12
        qs20q13 = xnorm*u2q13 + ynorm*v2q13 + znorm*w2q13
        qs20q14 = xnorm*u2q14 + ynorm*v2q14 + znorm*w2q14
        qs20q15 = xnorm*u2q15 + ynorm*v2q15 + znorm*w2q15

        qs20q21 = xnorm*u2q21 + ynorm*v2q21 + znorm*w2q21
        qs20q22 = xnorm*u2q22 + ynorm*v2q22 + znorm*w2q22
        qs20q23 = xnorm*u2q23 + ynorm*v2q23 + znorm*w2q23
        qs20q24 = xnorm*u2q24 + ynorm*v2q24 + znorm*w2q24
        qs20q25 = xnorm*u2q25 + ynorm*v2q25 + znorm*w2q25

      qs = abs(qs10 + qs20) / my_2
        if ( (qs10 + qs20) >= my_0 ) then
          qsq11 = (qs10q11 + qs20q11) / my_2
          qsq12 = (qs10q12 + qs20q12) / my_2
          qsq13 = (qs10q13 + qs20q13) / my_2
          qsq14 = (qs10q14 + qs20q14) / my_2
          qsq15 = (qs10q15 + qs20q15) / my_2

          qsq21 = (qs10q21 + qs20q21) / my_2
          qsq22 = (qs10q22 + qs20q22) / my_2
          qsq23 = (qs10q23 + qs20q23) / my_2
          qsq24 = (qs10q24 + qs20q24) / my_2
          qsq25 = (qs10q25 + qs20q25) / my_2
        else
          qsq11 = -(qs10q11 + qs20q11) / my_2
          qsq12 = -(qs10q12 + qs20q12) / my_2
          qsq13 = -(qs10q13 + qs20q13) / my_2
          qsq14 = -(qs10q14 + qs20q14) / my_2
          qsq15 = -(qs10q15 + qs20q15) / my_2

          qsq21 = -(qs10q21 + qs20q21) / my_2
          qsq22 = -(qs10q22 + qs20q22) / my_2
          qsq23 = -(qs10q23 + qs20q23) / my_2
          qsq24 = -(qs10q24 + qs20q24) / my_2
          qsq25 = -(qs10q25 + qs20q25) / my_2
        endif

      rhoa = (rho1 + rho2) / my_2
        rhoaq11 = (rho1q11 + rho2q11) / my_2
        rhoaq12 = (rho1q12 + rho2q12) / my_2
        rhoaq13 = (rho1q13 + rho2q13) / my_2
        rhoaq14 = (rho1q14 + rho2q14) / my_2
        rhoaq15 = (rho1q15 + rho2q15) / my_2

        rhoaq21 = (rho1q21 + rho2q21) / my_2
        rhoaq22 = (rho1q22 + rho2q22) / my_2
        rhoaq23 = (rho1q23 + rho2q23) / my_2
        rhoaq24 = (rho1q24 + rho2q24) / my_2
        rhoaq25 = (rho1q25 + rho2q25) / my_2

      pa   = (press1 + press2) / my_2
        paq11 = (press1q11 + press2q11) / my_2
        paq12 = (press1q12 + press2q12) / my_2
        paq13 = (press1q13 + press2q13) / my_2
        paq14 = (press1q14 + press2q14) / my_2
        paq15 = (press1q15 + press2q15) / my_2

        paq21 = (press1q21 + press2q21) / my_2
        paq22 = (press1q22 + press2q22) / my_2
        paq23 = (press1q23 + press2q23) / my_2
        paq24 = (press1q24 + press2q24) / my_2
        paq25 = (press1q25 + press2q25) / my_2

      cc   = gamma*pa/rhoa
        ccq11 = gamma * (rhoa*paq11 - pa*rhoaq11) / rhoa / rhoa
        ccq12 = gamma * (rhoa*paq12 - pa*rhoaq12) / rhoa / rhoa
        ccq13 = gamma * (rhoa*paq13 - pa*rhoaq13) / rhoa / rhoa
        ccq14 = gamma * (rhoa*paq14 - pa*rhoaq14) / rhoa / rhoa
        ccq15 = gamma * (rhoa*paq15 - pa*rhoaq15) / rhoa / rhoa

        ccq21 = gamma * (rhoa*paq21 - pa*rhoaq21) / rhoa / rhoa
        ccq22 = gamma * (rhoa*paq22 - pa*rhoaq22) / rhoa / rhoa
        ccq23 = gamma * (rhoa*paq23 - pa*rhoaq23) / rhoa / rhoa
        ccq24 = gamma * (rhoa*paq24 - pa*rhoaq24) / rhoa / rhoa
        ccq25 = gamma * (rhoa*paq25 - pa*rhoaq25) / rhoa / rhoa

! Eigenvalue

      lambda = qs + sqrt(cc)
        lambdaq11 = qsq11 + my_half/sqrt(cc)*ccq11
        lambdaq12 = qsq12 + my_half/sqrt(cc)*ccq12
        lambdaq13 = qsq13 + my_half/sqrt(cc)*ccq13
        lambdaq14 = qsq14 + my_half/sqrt(cc)*ccq14
        lambdaq15 = qsq15 + my_half/sqrt(cc)*ccq15

        lambdaq21 = qsq21 + my_half/sqrt(cc)*ccq21
        lambdaq22 = qsq22 + my_half/sqrt(cc)*ccq22
        lambdaq23 = qsq23 + my_half/sqrt(cc)*ccq23
        lambdaq24 = qsq24 + my_half/sqrt(cc)*ccq24
        lambdaq25 = qsq25 + my_half/sqrt(cc)*ccq25

! The LHS is always done 1st order, epsilon2=lambda/2, epsilon4 = 0 (not used)

      epsilon2 = my_half * lambda
        epsilon2q11 = my_half * lambdaq11
        epsilon2q12 = my_half * lambdaq12
        epsilon2q13 = my_half * lambdaq13
        epsilon2q14 = my_half * lambdaq14
        epsilon2q15 = my_half * lambdaq15

        epsilon2q21 = my_half * lambdaq21
        epsilon2q22 = my_half * lambdaq22
        epsilon2q23 = my_half * lambdaq23
        epsilon2q24 = my_half * lambdaq24
        epsilon2q25 = my_half * lambdaq25

! Assume terms like dq are fixed (do not linearize bigger stencil)

!     dq1=epsilon2*(qnode(1,node1)-qnode(1,node2))
        dq1q11 =  epsilon2 + (qnode(1,node1)-qnode(1,node2))*epsilon2q11
        dq1q12 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q12
        dq1q13 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q13
        dq1q14 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q14
        dq1q15 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q15

        dq1q21 = -epsilon2 + (qnode(1,node1)-qnode(1,node2))*epsilon2q21
        dq1q22 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q22
        dq1q23 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q23
        dq1q24 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q24
        dq1q25 =           + (qnode(1,node1)-qnode(1,node2))*epsilon2q25

!     dq2=epsilon2*(qnode(2,node1)-qnode(2,node2))
        dq2q11 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q11
        dq2q12 =  epsilon2 + (qnode(2,node1)-qnode(2,node2))*epsilon2q12
        dq2q13 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q13
        dq2q14 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q14
        dq2q15 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q15

        dq2q21 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q21
        dq2q22 = -epsilon2 + (qnode(2,node1)-qnode(2,node2))*epsilon2q22
        dq2q23 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q23
        dq2q24 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q24
        dq2q25 =           + (qnode(2,node1)-qnode(2,node2))*epsilon2q25

!     dq3=epsilon2*(qnode(3,node1)-qnode(3,node2))
        dq3q11 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q11
        dq3q12 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q12
        dq3q13 =  epsilon2 + (qnode(3,node1)-qnode(3,node2))*epsilon2q13
        dq3q14 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q14
        dq3q15 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q15

        dq3q21 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q21
        dq3q22 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q22
        dq3q23 = -epsilon2 + (qnode(3,node1)-qnode(3,node2))*epsilon2q23
        dq3q24 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q24
        dq3q25 =           + (qnode(3,node1)-qnode(3,node2))*epsilon2q25

!     dq4=epsilon2*(qnode(4,node1)-qnode(4,node2))
        dq4q11 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q11
        dq4q12 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q12
        dq4q13 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q13
        dq4q14 =  epsilon2 + (qnode(4,node1)-qnode(4,node2))*epsilon2q14
        dq4q15 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q15

        dq4q21 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q21
        dq4q22 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q22
        dq4q23 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q23
        dq4q24 = -epsilon2 + (qnode(4,node1)-qnode(4,node2))*epsilon2q24
        dq4q25 =           + (qnode(4,node1)-qnode(4,node2))*epsilon2q25

!     dq5=epsilon2*(qnode(5,node1)-qnode(5,node2))
        dq5q11 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q11
        dq5q12 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q12
        dq5q13 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q13
        dq5q14 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q14
        dq5q15 =  epsilon2 + (qnode(5,node1)-qnode(5,node2))*epsilon2q15

        dq5q21 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q21
        dq5q22 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q22
        dq5q23 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q23
        dq5q24 =           + (qnode(5,node1)-qnode(5,node2))*epsilon2q24
        dq5q25 = -epsilon2 + (qnode(5,node1)-qnode(5,node2))*epsilon2q25

! Add contributions to residuals

      if ( node1 <= nnodes0 ) then
!       res(1,node1) = res(1,node1) + dq1*area
!       res(2,node1) = res(2,node1) + dq2*area
!       res(3,node1) = res(3,node1) + dq3*area
!       res(4,node1) = res(4,node1) + dq4*area
!       res(5,node1) = res(5,node1) + dq5*area

! Diag contribution for equation at node1

        idiag = g2m(node1)
        a_diag(1,1,idiag) = a_diag(1,1,idiag) + area*dq1q11
        a_diag(1,2,idiag) = a_diag(1,2,idiag) + area*dq1q12
        a_diag(1,3,idiag) = a_diag(1,3,idiag) + area*dq1q13
        a_diag(1,4,idiag) = a_diag(1,4,idiag) + area*dq1q14
        a_diag(1,5,idiag) = a_diag(1,5,idiag) + area*dq1q15

        a_diag(2,1,idiag) = a_diag(2,1,idiag) + area*dq2q11
        a_diag(2,2,idiag) = a_diag(2,2,idiag) + area*dq2q12
        a_diag(2,3,idiag) = a_diag(2,3,idiag) + area*dq2q13
        a_diag(2,4,idiag) = a_diag(2,4,idiag) + area*dq2q14
        a_diag(2,5,idiag) = a_diag(2,5,idiag) + area*dq2q15

        a_diag(3,1,idiag) = a_diag(3,1,idiag) + area*dq3q11
        a_diag(3,2,idiag) = a_diag(3,2,idiag) + area*dq3q12
        a_diag(3,3,idiag) = a_diag(3,3,idiag) + area*dq3q13
        a_diag(3,4,idiag) = a_diag(3,4,idiag) + area*dq3q14
        a_diag(3,5,idiag) = a_diag(3,5,idiag) + area*dq3q15

        a_diag(4,1,idiag) = a_diag(4,1,idiag) + area*dq4q11
        a_diag(4,2,idiag) = a_diag(4,2,idiag) + area*dq4q12
        a_diag(4,3,idiag) = a_diag(4,3,idiag) + area*dq4q13
        a_diag(4,4,idiag) = a_diag(4,4,idiag) + area*dq4q14
        a_diag(4,5,idiag) = a_diag(4,5,idiag) + area*dq4q15

        a_diag(5,1,idiag) = a_diag(5,1,idiag) + area*dq5q11
        a_diag(5,2,idiag) = a_diag(5,2,idiag) + area*dq5q12
        a_diag(5,3,idiag) = a_diag(5,3,idiag) + area*dq5q13
        a_diag(5,4,idiag) = a_diag(5,4,idiag) + area*dq5q14
        a_diag(5,5,idiag) = a_diag(5,5,idiag) + area*dq5q15

! Off-diag contribution for equation at node1
        ioff  = fhelp(1,n)

        a_off(1,1,ioff) = a_off(1,1,ioff) + area*dq1q21
        a_off(1,2,ioff) = a_off(1,2,ioff) + area*dq1q22
        a_off(1,3,ioff) = a_off(1,3,ioff) + area*dq1q23
        a_off(1,4,ioff) = a_off(1,4,ioff) + area*dq1q24
        a_off(1,5,ioff) = a_off(1,5,ioff) + area*dq1q25

        a_off(2,1,ioff) = a_off(2,1,ioff) + area*dq2q21
        a_off(2,2,ioff) = a_off(2,2,ioff) + area*dq2q22
        a_off(2,3,ioff) = a_off(2,3,ioff) + area*dq2q23
        a_off(2,4,ioff) = a_off(2,4,ioff) + area*dq2q24
        a_off(2,5,ioff) = a_off(2,5,ioff) + area*dq2q25

        a_off(3,1,ioff) = a_off(3,1,ioff) + area*dq3q21
        a_off(3,2,ioff) = a_off(3,2,ioff) + area*dq3q22
        a_off(3,3,ioff) = a_off(3,3,ioff) + area*dq3q23
        a_off(3,4,ioff) = a_off(3,4,ioff) + area*dq3q24
        a_off(3,5,ioff) = a_off(3,5,ioff) + area*dq3q25

        a_off(4,1,ioff) = a_off(4,1,ioff) + area*dq4q21
        a_off(4,2,ioff) = a_off(4,2,ioff) + area*dq4q22
        a_off(4,3,ioff) = a_off(4,3,ioff) + area*dq4q23
        a_off(4,4,ioff) = a_off(4,4,ioff) + area*dq4q24
        a_off(4,5,ioff) = a_off(4,5,ioff) + area*dq4q25

        a_off(5,1,ioff) = a_off(5,1,ioff) + area*dq5q21
        a_off(5,2,ioff) = a_off(5,2,ioff) + area*dq5q22
        a_off(5,3,ioff) = a_off(5,3,ioff) + area*dq5q23
        a_off(5,4,ioff) = a_off(5,4,ioff) + area*dq5q24
        a_off(5,5,ioff) = a_off(5,5,ioff) + area*dq5q25
      endif

      if ( node2 <= nnodes0 ) then
!       res(1,node2) = res(1,node2) - dq1*area
!       res(2,node2) = res(2,node2) - dq2*area
!       res(3,node2) = res(3,node2) - dq3*area
!       res(4,node2) = res(4,node2) - dq4*area
!       res(5,node2) = res(5,node2) - dq5*area

! Diag contribution for equation at node2

        idiag = g2m(node2)
        a_diag(1,1,idiag) = a_diag(1,1,idiag) - area*dq1q21
        a_diag(1,2,idiag) = a_diag(1,2,idiag) - area*dq1q22
        a_diag(1,3,idiag) = a_diag(1,3,idiag) - area*dq1q23
        a_diag(1,4,idiag) = a_diag(1,4,idiag) - area*dq1q24
        a_diag(1,5,idiag) = a_diag(1,5,idiag) - area*dq1q25

        a_diag(2,1,idiag) = a_diag(2,1,idiag) - area*dq2q21
        a_diag(2,2,idiag) = a_diag(2,2,idiag) - area*dq2q22
        a_diag(2,3,idiag) = a_diag(2,3,idiag) - area*dq2q23
        a_diag(2,4,idiag) = a_diag(2,4,idiag) - area*dq2q24
        a_diag(2,5,idiag) = a_diag(2,5,idiag) - area*dq2q25

        a_diag(3,1,idiag) = a_diag(3,1,idiag) - area*dq3q21
        a_diag(3,2,idiag) = a_diag(3,2,idiag) - area*dq3q22
        a_diag(3,3,idiag) = a_diag(3,3,idiag) - area*dq3q23
        a_diag(3,4,idiag) = a_diag(3,4,idiag) - area*dq3q24
        a_diag(3,5,idiag) = a_diag(3,5,idiag) - area*dq3q25

        a_diag(4,1,idiag) = a_diag(4,1,idiag) - area*dq4q21
        a_diag(4,2,idiag) = a_diag(4,2,idiag) - area*dq4q22
        a_diag(4,3,idiag) = a_diag(4,3,idiag) - area*dq4q23
        a_diag(4,4,idiag) = a_diag(4,4,idiag) - area*dq4q24
        a_diag(4,5,idiag) = a_diag(4,5,idiag) - area*dq4q25

        a_diag(5,1,idiag) = a_diag(5,1,idiag) - area*dq5q21
        a_diag(5,2,idiag) = a_diag(5,2,idiag) - area*dq5q22
        a_diag(5,3,idiag) = a_diag(5,3,idiag) - area*dq5q23
        a_diag(5,4,idiag) = a_diag(5,4,idiag) - area*dq5q24
        a_diag(5,5,idiag) = a_diag(5,5,idiag) - area*dq5q25

! Off-diag contribution for equation at node1
        ioff  = fhelp(2,n)

        a_off(1,1,ioff) = a_off(1,1,ioff) - area*dq1q11
        a_off(1,2,ioff) = a_off(1,2,ioff) - area*dq1q12
        a_off(1,3,ioff) = a_off(1,3,ioff) - area*dq1q13
        a_off(1,4,ioff) = a_off(1,4,ioff) - area*dq1q14
        a_off(1,5,ioff) = a_off(1,5,ioff) - area*dq1q15

        a_off(2,1,ioff) = a_off(2,1,ioff) - area*dq2q11
        a_off(2,2,ioff) = a_off(2,2,ioff) - area*dq2q12
        a_off(2,3,ioff) = a_off(2,3,ioff) - area*dq2q13
        a_off(2,4,ioff) = a_off(2,4,ioff) - area*dq2q14
        a_off(2,5,ioff) = a_off(2,5,ioff) - area*dq2q15

        a_off(3,1,ioff) = a_off(3,1,ioff) - area*dq3q11
        a_off(3,2,ioff) = a_off(3,2,ioff) - area*dq3q12
        a_off(3,3,ioff) = a_off(3,3,ioff) - area*dq3q13
        a_off(3,4,ioff) = a_off(3,4,ioff) - area*dq3q14
        a_off(3,5,ioff) = a_off(3,5,ioff) - area*dq3q15

        a_off(4,1,ioff) = a_off(4,1,ioff) - area*dq4q11
        a_off(4,2,ioff) = a_off(4,2,ioff) - area*dq4q12
        a_off(4,3,ioff) = a_off(4,3,ioff) - area*dq4q13
        a_off(4,4,ioff) = a_off(4,4,ioff) - area*dq4q14
        a_off(4,5,ioff) = a_off(4,5,ioff) - area*dq4q15

        a_off(5,1,ioff) = a_off(5,1,ioff) - area*dq5q11
        a_off(5,2,ioff) = a_off(5,2,ioff) - area*dq5q12
        a_off(5,3,ioff) = a_off(5,3,ioff) - area*dq5q13
        a_off(5,4,ioff) = a_off(5,4,ioff) - area*dq5q14
        a_off(5,5,ioff) = a_off(5,5,ioff) - area*dq5q15
      endif

    end do dissipation_contribs

  end subroutine cd_jacobians

!============================== FLUX_JACOBIAN_AUFS ===========================80
!
! AUFS flux Jacobian
!
!=============================================================================80

  subroutine flux_jacobian_aufs( nnodes0, nnodes01, nedgeloc, nedgeloc_2d,     &
                                 max_nnz, eptr,                                &
                                 qnode, a_diag, a_off, xn, yn, zn,             &
                                 ra, fhelp, ncell01, n_tot, njac, g2m)

    use info_depr, only : twod, cc_primal
    use fluid,     only : gm1
    use thermo,    only : etop, ptoe

    integer,                                    intent(in) :: nnodes0,nnodes01
    integer,                                    intent(in) :: nedgeloc
    integer,                                    intent(in) :: nedgeloc_2d
    integer,                                    intent(in) :: max_nnz
    integer,                                    intent(in) :: ncell01,n_tot,njac

    integer,      dimension(2,nedgeloc),        intent(in) :: eptr
    integer,      dimension(2,nedgeloc),        intent(in) :: fhelp
    integer,      dimension(:),                 intent(in) :: g2m

    real(dp),  dimension(n_tot,nnodes01),       intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),             intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(njac,njac,nnodes0),    intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz),    intent(inout) :: a_off

    integer :: n,node1,node2,nedge_jac_eval,idiag,j,k,ioff,qdim

    real(dp)    :: xnorm,ynorm,znorm,area

    real(dp)    :: cr
    real(dp)    :: crrl,crul,crvl,crwl,crpl
    real(dp)    :: crrr,crur,crvr,crwr,crpr

    real(dp)    :: ubarr
    real(dp)    :: ubarrrl,ubarrul,ubarrvl,ubarrwl,ubarrpl
    real(dp)    :: ubarrrr,ubarrur,ubarrvr,ubarrwr,ubarrpr

    real(dp)    :: hr
    real(dp)    :: hrrl,hrul,hrvl,hrwl,hrpl
    real(dp)    :: hrrr,hrur,hrvr,hrwr,hrpr

    real(dp)    :: energyr
    real(dp)    :: energyrrl,energyrul,energyrvl,energyrwl,energyrpl
    real(dp)    :: energyrrr,energyrur,energyrvr,energyrwr,energyrpr

    real(dp)    :: pressr
    real(dp)    :: pressrrl,pressrul,pressrvl,pressrwl,pressrpl
    real(dp)    :: pressrrr,pressrur,pressrvr,pressrwr,pressrpr

    real(dp)    :: q2r
    real(dp)    :: q2rrl,q2rul,q2rvl,q2rwl,q2rpl
    real(dp)    :: q2rrr,q2rur,q2rvr,q2rwr,q2rpr

    real(dp)    :: ur
    real(dp)    :: urrl,urul,urvl,urwl,urpl
    real(dp)    :: urrr,urur,urvr,urwr,urpr

    real(dp)    :: vr
    real(dp)    :: vrrl,vrul,vrvl,vrwl,vrpl
    real(dp)    :: vrrr,vrur,vrvr,vrwr,vrpr

    real(dp)    :: wr
    real(dp)    :: wrrl,wrul,wrvl,wrwl,wrpl
    real(dp)    :: wrrr,wrur,wrvr,wrwr,wrpr

    real(dp)    :: rhor
    real(dp)    :: rhorrl,rhorul,rhorvl,rhorwl,rhorpl
    real(dp)    :: rhorrr,rhorur,rhorvr,rhorwr,rhorpr

    real(dp)    :: cl
    real(dp)    :: clrl,clul,clvl,clwl,clpl
    real(dp)    :: clrr,clur,clvr,clwr,clpr

    real(dp)    :: ubarl
    real(dp)    :: ubarlrl,ubarlul,ubarlvl,ubarlwl,ubarlpl
    real(dp)    :: ubarlrr,ubarlur,ubarlvr,ubarlwr,ubarlpr

    real(dp)    :: hl
    real(dp)    :: hlrl,hlul,hlvl,hlwl,hlpl
    real(dp)    :: hlrr,hlur,hlvr,hlwr,hlpr

    real(dp)    :: energyl
    real(dp)    :: energylrl,energylul,energylvl,energylwl,energylpl
    real(dp)    :: energylrr,energylur,energylvr,energylwr,energylpr

    real(dp)    :: pressl
    real(dp)    :: presslrl,presslul,presslvl,presslwl,presslpl
    real(dp)    :: presslrr,presslur,presslvr,presslwr,presslpr

    real(dp)    :: q2l
    real(dp)    :: q2lrl,q2lul,q2lvl,q2lwl,q2lpl
    real(dp)    :: q2lrr,q2lur,q2lvr,q2lwr,q2lpr

    real(dp)    :: ul
    real(dp)    :: ulrl,ulul,ulvl,ulwl,ulpl
    real(dp)    :: ulrr,ulur,ulvr,ulwr,ulpr

    real(dp)    :: vl
    real(dp)    :: vlrl,vlul,vlvl,vlwl,vlpl
    real(dp)    :: vlrr,vlur,vlvr,vlwr,vlpr

    real(dp)    :: wl
    real(dp)    :: wlrl,wlul,wlvl,wlwl,wlpl
    real(dp)    :: wlrr,wlur,wlvr,wlwr,wlpr

    real(dp)    :: rhol
    real(dp)    :: rholrl,rholul,rholvl,rholwl,rholpl
    real(dp)    :: rholrr,rholur,rholvr,rholwr,rholpr

    real(dp)    :: S1
    real(dp)    :: S1rl,S1ul,S1vl,S1wl,S1pl
    real(dp)    :: S1rr,S1ur,S1vr,S1wr,S1pr
    real(dp)    :: S2
    real(dp)    :: S2rl,S2ul,S2vl,S2wl,S2pl
    real(dp)    :: S2rr,S2ur,S2vr,S2wr,S2pr
    real(dp)    :: M
    real(dp)    :: Mrl,Mul,Mvl,Mwl,Mpl
    real(dp)    :: Mrr,Mur,Mvr,Mwr,Mpr

    real(dp)    :: cs
    real(dp)    :: csrl,csul,csvl,cswl,cspl
    real(dp)    :: csrr,csur,csvr,cswr,cspr

    real(dp)    :: cbar
    real(dp)    :: cbarrl,cbarul,cbarvl,cbarwl,cbarpl
    real(dp)    :: cbarrr,cbarur,cbarvr,cbarwr,cbarpr

    real(dp)    :: ubars
    real(dp)    :: ubarsrl,ubarsul,ubarsvl,ubarswl,ubarspl
    real(dp)    :: ubarsrr,ubarsur,ubarsvr,ubarswr,ubarspr

    real(dp)    :: dU1
    real(dp)    :: dU1rl,dU1ul,dU1vl,dU1wl,dU1pl
    real(dp)    :: dU1rr,dU1ur,dU1vr,dU1wr,dU1pr
    real(dp)    :: dU2
    real(dp)    :: dU2rl,dU2ul,dU2vl,dU2wl,dU2pl
    real(dp)    :: dU2rr,dU2ur,dU2vr,dU2wr,dU2pr
    real(dp)    :: dU3
    real(dp)    :: dU3rl,dU3ul,dU3vl,dU3wl,dU3pl
    real(dp)    :: dU3rr,dU3ur,dU3vr,dU3wr,dU3pr
    real(dp)    :: dU4
    real(dp)    :: dU4rl,dU4ul,dU4vl,dU4wl,dU4pl
    real(dp)    :: dU4rr,dU4ur,dU4vr,dU4wr,dU4pr
    real(dp)    :: dU5
    real(dp)    :: dU5rl,dU5ul,dU5vl,dU5wl,dU5pl
    real(dp)    :: dU5rr,dU5ur,dU5vr,dU5wr,dU5pr

!   real(dp)    :: Fc1
    real(dp)    :: Fc1rl,Fc1ul,Fc1vl,Fc1wl,Fc1pl
    real(dp)    :: Fc1rr,Fc1ur,Fc1vr,Fc1wr,Fc1pr
!   real(dp)    :: Fc2
    real(dp)    :: Fc2rl,Fc2ul,Fc2vl,Fc2wl,Fc2pl
    real(dp)    :: Fc2rr,Fc2ur,Fc2vr,Fc2wr,Fc2pr
!   real(dp)    :: Fc3
    real(dp)    :: Fc3rl,Fc3ul,Fc3vl,Fc3wl,Fc3pl
    real(dp)    :: Fc3rr,Fc3ur,Fc3vr,Fc3wr,Fc3pr
!   real(dp)    :: Fc4
    real(dp)    :: Fc4rl,Fc4ul,Fc4vl,Fc4wl,Fc4pl
    real(dp)    :: Fc4rr,Fc4ur,Fc4vr,Fc4wr,Fc4pr
!   real(dp)    :: Fc5
    real(dp)    :: Fc5rl,Fc5ul,Fc5vl,Fc5wl,Fc5pl
    real(dp)    :: Fc5rr,Fc5ur,Fc5vr,Fc5wr,Fc5pr

!   real(dp)    :: Fu1
    real(dp)    :: Fu1rl,Fu1ul,Fu1vl,Fu1wl,Fu1pl
    real(dp)    :: Fu1rr,Fu1ur,Fu1vr,Fu1wr,Fu1pr
!   real(dp)    :: Fu2
    real(dp)    :: Fu2rl,Fu2ul,Fu2vl,Fu2wl,Fu2pl
    real(dp)    :: Fu2rr,Fu2ur,Fu2vr,Fu2wr,Fu2pr
!   real(dp)    :: Fu3
    real(dp)    :: Fu3rl,Fu3ul,Fu3vl,Fu3wl,Fu3pl
    real(dp)    :: Fu3rr,Fu3ur,Fu3vr,Fu3wr,Fu3pr
!   real(dp)    :: Fu4
    real(dp)    :: Fu4rl,Fu4ul,Fu4vl,Fu4wl,Fu4pl
    real(dp)    :: Fu4rr,Fu4ur,Fu4vr,Fu4wr,Fu4pr
!   real(dp)    :: Fu5
    real(dp)    :: Fu5rl,Fu5ul,Fu5vl,Fu5wl,Fu5pl
    real(dp)    :: Fu5rr,Fu5ur,Fu5vr,Fu5wr,Fu5pr

!   real(dp)    :: F1
    real(dp)    :: F1rl,F1ul,F1vl,F1wl,F1pl
    real(dp)    :: F1rr,F1ur,F1vr,F1wr,F1pr
!   real(dp)    :: F2
    real(dp)    :: F2rl,F2ul,F2vl,F2wl,F2pl
    real(dp)    :: F2rr,F2ur,F2vr,F2wr,F2pr
!   real(dp)    :: F3
    real(dp)    :: F3rl,F3ul,F3vl,F3wl,F3pl
    real(dp)    :: F3rr,F3ur,F3vr,F3wr,F3pr
!   real(dp)    :: F4
    real(dp)    :: F4rl,F4ul,F4vl,F4wl,F4pl
    real(dp)    :: F4rr,F4ur,F4vr,F4wr,F4pr
!   real(dp)    :: F5
    real(dp)    :: F5rl,F5ul,F5vl,F5wl,F5pl
    real(dp)    :: F5rr,F5ur,F5vr,F5wr,F5pr

    real(dp)    :: q1q1l,q1q2l,q1q3l,q1q4l,q1q5l
    real(dp)    :: q2q1l,q2q2l,q2q3l,q2q4l,q2q5l
    real(dp)    :: q3q1l,q3q2l,q3q3l,q3q4l,q3q5l
    real(dp)    :: q4q1l,q4q2l,q4q3l,q4q4l,q4q5l
    real(dp)    :: q5q1l,q5q2l,q5q3l,q5q4l,q5q5l

    real(dp)    :: q1q1r,q1q2r,q1q3r,q1q4r,q1q5r
    real(dp)    :: q2q1r,q2q2r,q2q3r,q2q4r,q2q5r
    real(dp)    :: q3q1r,q3q2r,q3q3r,q3q4r,q3q5r
    real(dp)    :: q4q1r,q4q2r,q4q3r,q4q4r,q4q5r
    real(dp)    :: q5q1r,q5q2r,q5q3r,q5q4r,q5q5r

    real(dp), dimension(5,5)   :: dFm, dFp

  continue

! Set dimension for etop, ptoe

    qdim = nnodes01
    if ( cc_primal ) qdim = ncell01

    nedge_jac_eval = nedgeloc
    if (twod) nedge_jac_eval = nedgeloc_2d

!   Convert to primitive variables

    call etop(qdim,qnode,n_tot,0) ! Note eqn_set hardwired to zero here

!   Loop over the edges and calculate the Jacobians

    jacobian_for_each_edge: do n = 1, nedge_jac_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Get unit normals and area

      xnorm = xn(n)
      ynorm = yn(n)
      znorm = zn(n)
      area  = ra(n)

! Get variables on "left" side of face

      rhol    = qnode(1,node1)

       rholrl = 1.0_dp
       rholul = 0.0_dp
       rholvl = 0.0_dp
       rholwl = 0.0_dp
       rholpl = 0.0_dp

       rholrr = 0.0_dp
       rholur = 0.0_dp
       rholvr = 0.0_dp
       rholwr = 0.0_dp
       rholpr = 0.0_dp

      ul    = qnode(2,node1)

       ulrl = 0.0_dp
       ulul = 1.0_dp
       ulvl = 0.0_dp
       ulwl = 0.0_dp
       ulpl = 0.0_dp

       ulrr = 0.0_dp
       ulur = 0.0_dp
       ulvr = 0.0_dp
       ulwr = 0.0_dp
       ulpr = 0.0_dp

      vl    = qnode(3,node1)

       vlrl = 0.0_dp
       vlul = 0.0_dp
       vlvl = 1.0_dp
       vlwl = 0.0_dp
       vlpl = 0.0_dp

       vlrr = 0.0_dp
       vlur = 0.0_dp
       vlvr = 0.0_dp
       vlwr = 0.0_dp
       vlpr = 0.0_dp

      wl    = qnode(4,node1)

       wlrl = 0.0_dp
       wlul = 0.0_dp
       wlvl = 0.0_dp
       wlwl = 1.0_dp
       wlpl = 0.0_dp

       wlrr = 0.0_dp
       wlur = 0.0_dp
       wlvr = 0.0_dp
       wlwr = 0.0_dp
       wlpr = 0.0_dp

      q2l   = ul*ul + vl*vl + wl*wl

       q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
       q2lul = 2.0_dp*ul*ulul + 2.0_dp*vl*vlul + 2.0_dp*wl*wlul
       q2lvl = 2.0_dp*ul*ulvl + 2.0_dp*vl*vlvl + 2.0_dp*wl*wlvl
       q2lwl = 2.0_dp*ul*ulwl + 2.0_dp*vl*vlwl + 2.0_dp*wl*wlwl
       q2lpl = 2.0_dp*ul*ulpl + 2.0_dp*vl*vlpl + 2.0_dp*wl*wlpl

       q2lrr = 2.0_dp*ul*ulrr + 2.0_dp*vl*vlrr + 2.0_dp*wl*wlrr
       q2lur = 2.0_dp*ul*ulur + 2.0_dp*vl*vlur + 2.0_dp*wl*wlur
       q2lvr = 2.0_dp*ul*ulvr + 2.0_dp*vl*vlvr + 2.0_dp*wl*wlvr
       q2lwr = 2.0_dp*ul*ulwr + 2.0_dp*vl*vlwr + 2.0_dp*wl*wlwr
       q2lpr = 2.0_dp*ul*ulpr + 2.0_dp*vl*vlpr + 2.0_dp*wl*wlpr

      pressl    = qnode(5,node1)

       presslrl = 0.0_dp
       presslul = 0.0_dp
       presslvl = 0.0_dp
       presslwl = 0.0_dp
       presslpl = 1.0_dp

       presslrr = 0.0_dp
       presslur = 0.0_dp
       presslvr = 0.0_dp
       presslwr = 0.0_dp
       presslpr = 0.0_dp

      energyl    = pressl/gm1 + 0.5_dp*rhol*q2l

       energylrl = presslrl/gm1 + 0.5_dp*(rhol*q2lrl + q2l*rholrl)
       energylul = presslul/gm1 + 0.5_dp*(rhol*q2lul + q2l*rholul)
       energylvl = presslvl/gm1 + 0.5_dp*(rhol*q2lvl + q2l*rholvl)
       energylwl = presslwl/gm1 + 0.5_dp*(rhol*q2lwl + q2l*rholwl)
       energylpl = presslpl/gm1 + 0.5_dp*(rhol*q2lpl + q2l*rholpl)

       energylrr = presslrr/gm1 + 0.5_dp*(rhol*q2lrr + q2l*rholrr)
       energylur = presslur/gm1 + 0.5_dp*(rhol*q2lur + q2l*rholur)
       energylvr = presslvr/gm1 + 0.5_dp*(rhol*q2lvr + q2l*rholvr)
       energylwr = presslwr/gm1 + 0.5_dp*(rhol*q2lwr + q2l*rholwr)
       energylpr = presslpr/gm1 + 0.5_dp*(rhol*q2lpr + q2l*rholpr)

      Hl    = (energyl + pressl)/rhol

       Hlrl = (rhol*(energylrl+presslrl) - (energyl+pressl)*rholrl)/rhol/rhol
       Hlul = (rhol*(energylul+presslul) - (energyl+pressl)*rholul)/rhol/rhol
       Hlvl = (rhol*(energylvl+presslvl) - (energyl+pressl)*rholvl)/rhol/rhol
       Hlwl = (rhol*(energylwl+presslwl) - (energyl+pressl)*rholwl)/rhol/rhol
       Hlpl = (rhol*(energylpl+presslpl) - (energyl+pressl)*rholpl)/rhol/rhol

       Hlrr = (rhol*(energylrr+presslrr) - (energyl+pressl)*rholrr)/rhol/rhol
       Hlur = (rhol*(energylur+presslur) - (energyl+pressl)*rholur)/rhol/rhol
       Hlvr = (rhol*(energylvr+presslvr) - (energyl+pressl)*rholvr)/rhol/rhol
       Hlwr = (rhol*(energylwr+presslwr) - (energyl+pressl)*rholwr)/rhol/rhol
       Hlpr = (rhol*(energylpr+presslpr) - (energyl+pressl)*rholpr)/rhol/rhol

      ubarl    = xnorm*ul + ynorm*vl + znorm*wl

       ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
       ubarlul = xnorm*ulul + ynorm*vlul + znorm*wlul
       ubarlvl = xnorm*ulvl + ynorm*vlvl + znorm*wlvl
       ubarlwl = xnorm*ulwl + ynorm*vlwl + znorm*wlwl
       ubarlpl = xnorm*ulpl + ynorm*vlpl + znorm*wlpl

       ubarlrr = xnorm*ulrr + ynorm*vlrr + znorm*wlrr
       ubarlur = xnorm*ulur + ynorm*vlur + znorm*wlur
       ubarlvr = xnorm*ulvr + ynorm*vlvr + znorm*wlvr
       ubarlwr = xnorm*ulwr + ynorm*vlwr + znorm*wlwr
       ubarlpr = xnorm*ulpr + ynorm*vlpr + znorm*wlpr

      cl = sqrt(gm1*(Hl-0.5_dp*q2l))

       clrl = 0.5_dp / cl * gm1*(Hlrl - 0.5_dp*q2lrl)
       clul = 0.5_dp / cl * gm1*(Hlul - 0.5_dp*q2lul)
       clvl = 0.5_dp / cl * gm1*(Hlvl - 0.5_dp*q2lvl)
       clwl = 0.5_dp / cl * gm1*(Hlwl - 0.5_dp*q2lwl)
       clpl = 0.5_dp / cl * gm1*(Hlpl - 0.5_dp*q2lpl)

       clrr = 0.5_dp / cl * gm1*(Hlrr - 0.5_dp*q2lrr)
       clur = 0.5_dp / cl * gm1*(Hlur - 0.5_dp*q2lur)
       clvr = 0.5_dp / cl * gm1*(Hlvr - 0.5_dp*q2lvr)
       clwr = 0.5_dp / cl * gm1*(Hlwr - 0.5_dp*q2lwr)
       clpr = 0.5_dp / cl * gm1*(Hlpr - 0.5_dp*q2lpr)

! Get variables on "right" side of face

      rhor   = qnode(1,node2)

       rhorrl = 0.0_dp
       rhorul = 0.0_dp
       rhorvl = 0.0_dp
       rhorwl = 0.0_dp
       rhorpl = 0.0_dp

       rhorrr = 1.0_dp
       rhorur = 0.0_dp
       rhorvr = 0.0_dp
       rhorwr = 0.0_dp
       rhorpr = 0.0_dp

      ur     = qnode(2,node2)

       urrl = 0.0_dp
       urul = 0.0_dp
       urvl = 0.0_dp
       urwl = 0.0_dp
       urpl = 0.0_dp

       urrr = 0.0_dp
       urur = 1.0_dp
       urvr = 0.0_dp
       urwr = 0.0_dp
       urpr = 0.0_dp

      vr     = qnode(3,node2)

       vrrl = 0.0_dp
       vrul = 0.0_dp
       vrvl = 0.0_dp
       vrwl = 0.0_dp
       vrpl = 0.0_dp

       vrrr = 0.0_dp
       vrur = 0.0_dp
       vrvr = 1.0_dp
       vrwr = 0.0_dp
       vrpr = 0.0_dp

      wr     = qnode(4,node2)

       wrrl = 0.0_dp
       wrul = 0.0_dp
       wrvl = 0.0_dp
       wrwl = 0.0_dp
       wrpl = 0.0_dp

       wrrr = 0.0_dp
       wrur = 0.0_dp
       wrvr = 0.0_dp
       wrwr = 1.0_dp
       wrpr = 0.0_dp

      q2r    = ur*ur + vr*vr + wr*wr

       q2rrl = 2.0_dp*ur*urrl + 2.0_dp*vr*vrrl + 2.0_dp*wr*wrrl
       q2rul = 2.0_dp*ur*urul + 2.0_dp*vr*vrul + 2.0_dp*wr*wrul
       q2rvl = 2.0_dp*ur*urvl + 2.0_dp*vr*vrvl + 2.0_dp*wr*wrvl
       q2rwl = 2.0_dp*ur*urwl + 2.0_dp*vr*vrwl + 2.0_dp*wr*wrwl
       q2rpl = 2.0_dp*ur*urpl + 2.0_dp*vr*vrpl + 2.0_dp*wr*wrpl

       q2rrr = 2.0_dp*ur*urrr + 2.0_dp*vr*vrrr + 2.0_dp*wr*wrrr
       q2rur = 2.0_dp*ur*urur + 2.0_dp*vr*vrur + 2.0_dp*wr*wrur
       q2rvr = 2.0_dp*ur*urvr + 2.0_dp*vr*vrvr + 2.0_dp*wr*wrvr
       q2rwr = 2.0_dp*ur*urwr + 2.0_dp*vr*vrwr + 2.0_dp*wr*wrwr
       q2rpr = 2.0_dp*ur*urpr + 2.0_dp*vr*vrpr + 2.0_dp*wr*wrpr

      pressr = qnode(5,node2)

       pressrrl = 0.0_dp
       pressrul = 0.0_dp
       pressrvl = 0.0_dp
       pressrwl = 0.0_dp
       pressrpl = 0.0_dp

       pressrrr = 0.0_dp
       pressrur = 0.0_dp
       pressrvr = 0.0_dp
       pressrwr = 0.0_dp
       pressrpr = 1.0_dp

      energyr = pressr/gm1 + 0.5_dp*rhor*q2r

       energyrrl = pressrrl/gm1 + 0.5_dp*(rhor*q2rrl + q2r*rhorrl)
       energyrul = pressrul/gm1 + 0.5_dp*(rhor*q2rul + q2r*rhorul)
       energyrvl = pressrvl/gm1 + 0.5_dp*(rhor*q2rvl + q2r*rhorvl)
       energyrwl = pressrwl/gm1 + 0.5_dp*(rhor*q2rwl + q2r*rhorwl)
       energyrpl = pressrpl/gm1 + 0.5_dp*(rhor*q2rpl + q2r*rhorpl)

       energyrrr = pressrrr/gm1 + 0.5_dp*(rhor*q2rrr + q2r*rhorrr)
       energyrur = pressrur/gm1 + 0.5_dp*(rhor*q2rur + q2r*rhorur)
       energyrvr = pressrvr/gm1 + 0.5_dp*(rhor*q2rvr + q2r*rhorvr)
       energyrwr = pressrwr/gm1 + 0.5_dp*(rhor*q2rwr + q2r*rhorwr)
       energyrpr = pressrpr/gm1 + 0.5_dp*(rhor*q2rpr + q2r*rhorpr)

      Hr    = (energyr + pressr)/rhor

       Hrrl = ( rhor*(energyrrl+pressrrl) - (energyr+pressr)*rhorrl ) / rhor**2
       Hrul = ( rhor*(energyrul+pressrul) - (energyr+pressr)*rhorul ) / rhor**2
       Hrvl = ( rhor*(energyrvl+pressrvl) - (energyr+pressr)*rhorvl ) / rhor**2
       Hrwl = ( rhor*(energyrwl+pressrwl) - (energyr+pressr)*rhorwl ) / rhor**2
       Hrpl = ( rhor*(energyrpl+pressrpl) - (energyr+pressr)*rhorpl ) / rhor**2

       Hrrr = ( rhor*(energyrrr+pressrrr) - (energyr+pressr)*rhorrr ) / rhor**2
       Hrur = ( rhor*(energyrur+pressrur) - (energyr+pressr)*rhorur ) / rhor**2
       Hrvr = ( rhor*(energyrvr+pressrvr) - (energyr+pressr)*rhorvr ) / rhor**2
       Hrwr = ( rhor*(energyrwr+pressrwr) - (energyr+pressr)*rhorwr ) / rhor**2
       Hrpr = ( rhor*(energyrpr+pressrpr) - (energyr+pressr)*rhorpr ) / rhor**2

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr

       ubarrrl = xnorm*urrl + ynorm*vrrl + znorm*wrrl
       ubarrul = xnorm*urul + ynorm*vrul + znorm*wrul
       ubarrvl = xnorm*urvl + ynorm*vrvl + znorm*wrvl
       ubarrwl = xnorm*urwl + ynorm*vrwl + znorm*wrwl
       ubarrpl = xnorm*urpl + ynorm*vrpl + znorm*wrpl

       ubarrrr = xnorm*urrr + ynorm*vrrr + znorm*wrrr
       ubarrur = xnorm*urur + ynorm*vrur + znorm*wrur
       ubarrvr = xnorm*urvr + ynorm*vrvr + znorm*wrvr
       ubarrwr = xnorm*urwr + ynorm*vrwr + znorm*wrwr
       ubarrpr = xnorm*urpr + ynorm*vrpr + znorm*wrpr

      cr = sqrt(gm1*(Hr-0.5_dp*q2r))

       crrl = 0.5_dp / cr * gm1*(Hrrl - 0.5_dp*q2rrl)
       crul = 0.5_dp / cr * gm1*(Hrul - 0.5_dp*q2rul)
       crvl = 0.5_dp / cr * gm1*(Hrvl - 0.5_dp*q2rvl)
       crwl = 0.5_dp / cr * gm1*(Hrwl - 0.5_dp*q2rwl)
       crpl = 0.5_dp / cr * gm1*(Hrpl - 0.5_dp*q2rpl)

       crrr = 0.5_dp / cr * gm1*(Hrrr - 0.5_dp*q2rrr)
       crur = 0.5_dp / cr * gm1*(Hrur - 0.5_dp*q2rur)
       crvr = 0.5_dp / cr * gm1*(Hrvr - 0.5_dp*q2rvr)
       crwr = 0.5_dp / cr * gm1*(Hrwr - 0.5_dp*q2rwr)
       crpr = 0.5_dp / cr * gm1*(Hrpr - 0.5_dp*q2rpr)

!     Acoustic wave speed average (Eqn. 35):

      cbar = 0.5_dp*( cl + cr )

       cbarrl = 0.5_dp*( clrl + crrl )
       cbarul = 0.5_dp*( clul + crul )
       cbarvl = 0.5_dp*( clvl + crvl )
       cbarwl = 0.5_dp*( clwl + crwl )
       cbarpl = 0.5_dp*( clpl + crpl )

       cbarrr = 0.5_dp*( clrr + crrr )
       cbarur = 0.5_dp*( clur + crur )
       cbarvr = 0.5_dp*( clvr + crvr )
       cbarwr = 0.5_dp*( clwr + crwr )
       cbarpr = 0.5_dp*( clpr + crpr )

!     Artificial viscosity (Eqn.47):

      dU1 = 0.5_dp*(pressl-pressr)/cbar

       dU1rl = ( cbar*0.5_dp*(presslrl-pressrrl)                               &
               - 0.5_dp*(pressl-pressr)*cbarrl ) / cbar**2
       dU1ul = ( cbar*0.5_dp*(presslul-pressrul)                               &
               - 0.5_dp*(pressl-pressr)*cbarul ) / cbar**2
       dU1vl = ( cbar*0.5_dp*(presslvl-pressrvl)                               &
               - 0.5_dp*(pressl-pressr)*cbarvl ) / cbar**2
       dU1wl = ( cbar*0.5_dp*(presslwl-pressrwl)                               &
               - 0.5_dp*(pressl-pressr)*cbarwl ) / cbar**2
       dU1pl = ( cbar*0.5_dp*(presslpl-pressrpl)                               &
               - 0.5_dp*(pressl-pressr)*cbarpl ) / cbar**2

       dU1rr = ( cbar*0.5_dp*(presslrr-pressrrr)                               &
               - 0.5_dp*(pressl-pressr)*cbarrr ) / cbar**2
       dU1ur = ( cbar*0.5_dp*(presslur-pressrur)                               &
               - 0.5_dp*(pressl-pressr)*cbarur ) / cbar**2
       dU1vr = ( cbar*0.5_dp*(presslvr-pressrvr)                               &
               - 0.5_dp*(pressl-pressr)*cbarvr ) / cbar**2
       dU1wr = ( cbar*0.5_dp*(presslwr-pressrwr)                               &
               - 0.5_dp*(pressl-pressr)*cbarwr ) / cbar**2
       dU1pr = ( cbar*0.5_dp*(presslpr-pressrpr)                               &
               - 0.5_dp*(pressl-pressr)*cbarpr ) / cbar**2

      dU2 = 0.5_dp*(pressl*ul-pressr*ur)/cbar

       dU2rl = ( cbar*0.5_dp*(presslrl*ul-pressrrl*ur                          &
                                +pressl*ulrl-pressr*urrl)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarrl ) / cbar**2
       dU2ul = ( cbar*0.5_dp*(presslul*ul-pressrul*ur                          &
                                +pressl*ulul-pressr*urul)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarul ) / cbar**2
       dU2vl = ( cbar*0.5_dp*(presslvl*ul-pressrvl*ur                          &
                                +pressl*ulvl-pressr*urvl)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarvl ) / cbar**2
       dU2wl = ( cbar*0.5_dp*(presslwl*ul-pressrwl*ur                          &
                                +pressl*ulwl-pressr*urwl)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarwl ) / cbar**2
       dU2pl = ( cbar*0.5_dp*(presslpl*ul-pressrpl*ur                          &
                                +pressl*ulpl-pressr*urpl)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarpl ) / cbar**2

       dU2rr = ( cbar*0.5_dp*(presslrr*ul-pressrrr*ur                          &
                                +pressl*ulrr-pressr*urrr)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarrr ) / cbar**2
       dU2ur = ( cbar*0.5_dp*(presslur*ul-pressrur*ur                          &
                                +pressl*ulur-pressr*urur)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarur ) / cbar**2
       dU2vr = ( cbar*0.5_dp*(presslvr*ul-pressrvr*ur                          &
                                +pressl*ulvr-pressr*urvr)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarvr ) / cbar**2
       dU2wr = ( cbar*0.5_dp*(presslwr*ul-pressrwr*ur                          &
                                +pressl*ulwr-pressr*urwr)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarwr ) / cbar**2
       dU2pr = ( cbar*0.5_dp*(presslpr*ul-pressrpr*ur                          &
                                +pressl*ulpr-pressr*urpr)                      &
               - 0.5_dp*(pressl*ul-pressr*ur)*cbarpr ) / cbar**2

      dU3 = 0.5_dp*(pressl*vl-pressr*vr)/cbar

       dU3rl = ( cbar*0.5_dp*(presslrl*vl-pressrrl*vr                          &
                                +pressl*vlrl-pressr*vrrl)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarrl ) / cbar**2
       dU3ul = ( cbar*0.5_dp*(presslul*vl-pressrul*vr                          &
                                +pressl*vlul-pressr*vrul)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarul ) / cbar**2
       dU3vl = ( cbar*0.5_dp*(presslvl*vl-pressrvl*vr                          &
                                +pressl*vlvl-pressr*vrvl)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarvl ) / cbar**2
       dU3wl = ( cbar*0.5_dp*(presslwl*vl-pressrwl*vr                          &
                                +pressl*vlwl-pressr*vrwl)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarwl ) / cbar**2
       dU3pl = ( cbar*0.5_dp*(presslpl*vl-pressrpl*vr                          &
                                +pressl*vlpl-pressr*vrpl)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarpl ) / cbar**2

       dU3rr = ( cbar*0.5_dp*(presslrr*vl-pressrrr*vr                          &
                                +pressl*vlrr-pressr*vrrr)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarrr ) / cbar**2
       dU3ur = ( cbar*0.5_dp*(presslur*vl-pressrur*vr                          &
                                +pressl*vlur-pressr*vrur)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarur ) / cbar**2
       dU3vr = ( cbar*0.5_dp*(presslvr*vl-pressrvr*vr                          &
                                +pressl*vlvr-pressr*vrvr)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarvr ) / cbar**2
       dU3wr = ( cbar*0.5_dp*(presslwr*vl-pressrwr*vr                          &
                                +pressl*vlwr-pressr*vrwr)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarwr ) / cbar**2
       dU3pr = ( cbar*0.5_dp*(presslpr*vl-pressrpr*vr                          &
                                +pressl*vlpr-pressr*vrpr)                      &
               - 0.5_dp*(pressl*vl-pressr*vr)*cbarpr ) / cbar**2

      dU4 = 0.5_dp*(pressl*wl-pressr*wr)/cbar

       dU4rl = ( cbar*0.5_dp*(presslrl*wl-pressrrl*wr                          &
                                +pressl*wlrl-pressr*wrrl)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarrl ) / cbar**2
       dU4ul = ( cbar*0.5_dp*(presslul*wl-pressrul*wr                          &
                                +pressl*wlul-pressr*wrul)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarul ) / cbar**2
       dU4vl = ( cbar*0.5_dp*(presslvl*wl-pressrvl*wr                          &
                                +pressl*wlvl-pressr*wrvl)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarvl ) / cbar**2
       dU4wl = ( cbar*0.5_dp*(presslwl*wl-pressrwl*wr                          &
                                +pressl*wlwl-pressr*wrwl)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarwl ) / cbar**2
       dU4pl = ( cbar*0.5_dp*(presslpl*wl-pressrpl*wr                          &
                                +pressl*wlpl-pressr*wrpl)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarpl ) / cbar**2

       dU4rr = ( cbar*0.5_dp*(presslrr*wl-pressrrr*wr                          &
                                +pressl*wlrr-pressr*wrrr)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarrr ) / cbar**2
       dU4ur = ( cbar*0.5_dp*(presslur*wl-pressrur*wr                          &
                                +pressl*wlur-pressr*wrur)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarur ) / cbar**2
       dU4vr = ( cbar*0.5_dp*(presslvr*wl-pressrvr*wr                          &
                                +pressl*wlvr-pressr*wrvr)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarvr ) / cbar**2
       dU4wr = ( cbar*0.5_dp*(presslwr*wl-pressrwr*wr                          &
                                +pressl*wlwr-pressr*wrwr)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarwr ) / cbar**2
       dU4pr = ( cbar*0.5_dp*(presslpr*wl-pressrpr*wr                          &
                                +pressl*wlpr-pressr*wrpr)                      &
               - 0.5_dp*(pressl*wl-pressr*wr)*cbarpr ) / cbar**2

      dU5 = 0.5_dp*(cbar*(pressl-pressr)/gm1                                   &
                      +0.5_dp*(pressl*q2l-pressr*q2r)/cbar)

       dU5rl = 0.5_dp*(cbarrl*(pressl-pressr)/gm1                              &
                         +cbar*(presslrl-pressrrl)/gm1                         &
             + ( cbar*0.5_dp*(presslrl*q2l-pressrrl*q2r                        &
                                +pressl*q2lrl-pressr*q2rrl)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarrl ) / cbar**2 )
       dU5ul = 0.5_dp*(cbarul*(pressl-pressr)/gm1                              &
                         +cbar*(presslul-pressrul)/gm1                         &
             + ( cbar*0.5_dp*(presslul*q2l-pressrul*q2r                        &
                                +pressl*q2lul-pressr*q2rul)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarul ) / cbar**2 )
       dU5vl = 0.5_dp*(cbarvl*(pressl-pressr)/gm1                              &
                         +cbar*(presslvl-pressrvl)/gm1                         &
             + ( cbar*0.5_dp*(presslvl*q2l-pressrvl*q2r                        &
                                +pressl*q2lvl-pressr*q2rvl)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarvl ) / cbar**2 )
       dU5wl = 0.5_dp*(cbarwl*(pressl-pressr)/gm1                              &
                         +cbar*(presslwl-pressrwl)/gm1                         &
             + ( cbar*0.5_dp*(presslwl*q2l-pressrwl*q2r                        &
                                +pressl*q2lwl-pressr*q2rwl)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarwl ) / cbar**2 )
       dU5pl = 0.5_dp*(cbarpl*(pressl-pressr)/gm1                              &
                         +cbar*(presslpl-pressrpl)/gm1                         &
             + ( cbar*0.5_dp*(presslpl*q2l-pressrpl*q2r                        &
                                +pressl*q2lpl-pressr*q2rpl)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarpl ) / cbar**2 )

       dU5rr = 0.5_dp*(cbarrr*(pressl-pressr)/gm1                              &
                         +cbar*(presslrr-pressrrr)/gm1                         &
             + ( cbar*0.5_dp*(presslrr*q2l-pressrrr*q2r                        &
                                +pressl*q2lrr-pressr*q2rrr)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarrr ) / cbar**2 )
       dU5ur = 0.5_dp*(cbarur*(pressl-pressr)/gm1                              &
                         +cbar*(presslur-pressrur)/gm1                         &
             + ( cbar*0.5_dp*(presslur*q2l-pressrur*q2r                        &
                                +pressl*q2lur-pressr*q2rur)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarur ) / cbar**2 )
       dU5vr = 0.5_dp*(cbarvr*(pressl-pressr)/gm1                              &
                         +cbar*(presslvr-pressrvr)/gm1                         &
             + ( cbar*0.5_dp*(presslvr*q2l-pressrvr*q2r                        &
                                +pressl*q2lvr-pressr*q2rvr)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarvr ) / cbar**2 )
       dU5wr = 0.5_dp*(cbarwr*(pressl-pressr)/gm1                              &
                         +cbar*(presslwr-pressrwr)/gm1                         &
             + ( cbar*0.5_dp*(presslwr*q2l-pressrwr*q2r                        &
                                +pressl*q2lwr-pressr*q2rwr)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarwr ) / cbar**2 )
       dU5pr = 0.5_dp*(cbarpr*(pressl-pressr)/gm1                              &
                         +cbar*(presslpr-pressrpr)/gm1                         &
             + ( cbar*0.5_dp*(presslpr*q2l-pressrpr*q2r                        &
                                +pressl*q2lpr-pressr*q2rpr)                    &
                -0.5_dp*(pressl*q2l-pressr*q2r)*cbarpr ) / cbar**2 )

!     First splitting wave speed (Eqn. 36):

      S1 = 0.5_dp*( ubarl + ubarr )

       S1rl = 0.5_dp*( ubarlrl + ubarrrl )
       S1ul = 0.5_dp*( ubarlul + ubarrul )
       S1vl = 0.5_dp*( ubarlvl + ubarrvl )
       S1wl = 0.5_dp*( ubarlwl + ubarrwl )
       S1pl = 0.5_dp*( ubarlpl + ubarrpl )

       S1rr = 0.5_dp*( ubarlrr + ubarrrr )
       S1ur = 0.5_dp*( ubarlur + ubarrur )
       S1vr = 0.5_dp*( ubarlvr + ubarrvr )
       S1wr = 0.5_dp*( ubarlwr + ubarrwr )
       S1pr = 0.5_dp*( ubarlpr + ubarrpr )

!     Speeds between two isentropic waves (Eqn. 17):

      cs    = cbar + 0.25_dp*gm1*(ubarl-ubarr)

       csrl = cbarrl + 0.25_dp*gm1*( ubarlrl - ubarrrl )
       csul = cbarul + 0.25_dp*gm1*( ubarlul - ubarrul )
       csvl = cbarvl + 0.25_dp*gm1*( ubarlvl - ubarrvl )
       cswl = cbarwl + 0.25_dp*gm1*( ubarlwl - ubarrwl )
       cspl = cbarpl + 0.25_dp*gm1*( ubarlpl - ubarrpl )

       csrr = cbarrr + 0.25_dp*gm1*( ubarlrr - ubarrrr )
       csur = cbarur + 0.25_dp*gm1*( ubarlur - ubarrur )
       csvr = cbarvr + 0.25_dp*gm1*( ubarlvr - ubarrvr )
       cswr = cbarwr + 0.25_dp*gm1*( ubarlwr - ubarrwr )
       cspr = cbarpr + 0.25_dp*gm1*( ubarlpr - ubarrpr )

      ubars = S1 + (cl-cr)/gm1

       ubarsrl = S1rl + ( clrl - crrl )/gm1
       ubarsul = S1ul + ( clul - crul )/gm1
       ubarsvl = S1vl + ( clvl - crvl )/gm1
       ubarswl = S1wl + ( clwl - crwl )/gm1
       ubarspl = S1pl + ( clpl - crpl )/gm1

       ubarsrr = S1rr + ( clrr - crrr )/gm1
       ubarsur = S1ur + ( clur - crur )/gm1
       ubarsvr = S1vr + ( clvr - crvr )/gm1
       ubarswr = S1wr + ( clwr - crwr )/gm1
       ubarspr = S1pr + ( clpr - crpr )/gm1

!     Second splitting wave speed (Eqn. 37):

      if ( S1 > 0.0_dp ) then

        S2 = min( 0.0_dp, ubarl-cl, ubars-cs )

        if ( (0.0_dp < ubarl-cl) .and. (0.0_dp < ubars-cs) ) then ! 0 min
          S2rl = 0.0_dp
          S2ul = 0.0_dp
          S2vl = 0.0_dp
          S2wl = 0.0_dp
          S2pl = 0.0_dp
          S2rr = 0.0_dp
          S2ur = 0.0_dp
          S2vr = 0.0_dp
          S2wr = 0.0_dp
          S2pr = 0.0_dp
        else if ( ubarl-cl < ubars-cs ) then ! ubarl-cl minimum
          S2rl = ubarlrl-clrl
          S2ul = ubarlul-clul
          S2vl = ubarlvl-clvl
          S2wl = ubarlwl-clwl
          S2pl = ubarlpl-clpl
          S2rr = ubarlrr-clrr
          S2ur = ubarlur-clur
          S2vr = ubarlvr-clvr
          S2wr = ubarlwr-clwr
          S2pr = ubarlpr-clpr
        else ! ubars-cs minimum
          S2rl = ubarsrl-csrl
          S2ul = ubarsul-csul
          S2vl = ubarsvl-csvl
          S2wl = ubarswl-cswl
          S2pl = ubarspl-cspl
          S2rr = ubarsrr-csrr
          S2ur = ubarsur-csur
          S2vr = ubarsvr-csvr
          S2wr = ubarswr-cswr
          S2pr = ubarspr-cspr
        end if

      else

        S2 = max( 0.0_dp, ubarr+cr, ubars+cs )

        if ( (0.0_dp > ubarr+cr) .and. (0.0_dp > ubars+cs) ) then ! 0 max
          S2rl = 0.0_dp
          S2ul = 0.0_dp
          S2vl = 0.0_dp
          S2wl = 0.0_dp
          S2pl = 0.0_dp
          S2rr = 0.0_dp
          S2ur = 0.0_dp
          S2vr = 0.0_dp
          S2wr = 0.0_dp
          S2pr = 0.0_dp
        else if ( ubarr+cr > ubars+cs ) then ! ubarr+cr maximum
          S2rl = ubarrrl+crrl
          S2ul = ubarrul+crul
          S2vl = ubarrvl+crvl
          S2wl = ubarrwl+crwl
          S2pl = ubarrpl+crpl
          S2rr = ubarrrr+crrr
          S2ur = ubarrur+crur
          S2vr = ubarrvr+crvr
          S2wr = ubarrwr+crwr
          S2pr = ubarrpr+crpr
        else ! ubars+cs maximum
          S2rl = ubarsrl+csrl
          S2ul = ubarsul+csul
          S2vl = ubarsvl+csvl
          S2wl = ubarswl+cswl
          S2pl = ubarspl+cspl
          S2rr = ubarsrr+csrr
          S2ur = ubarsur+csur
          S2vr = ubarsvr+csvr
          S2wr = ubarswr+cswr
          S2pr = ubarspr+cspr
        end if

      end if

!     Splitting ratio (Eqn. 10):

      M = S1/(S1-S2)

       Mrl = ( (S1-S2)*S1rl - S1*(S1rl-S2rl) ) / (S1-S2)**2
       Mul = ( (S1-S2)*S1ul - S1*(S1ul-S2ul) ) / (S1-S2)**2
       Mvl = ( (S1-S2)*S1vl - S1*(S1vl-S2vl) ) / (S1-S2)**2
       Mwl = ( (S1-S2)*S1wl - S1*(S1wl-S2wl) ) / (S1-S2)**2
       Mpl = ( (S1-S2)*S1pl - S1*(S1pl-S2pl) ) / (S1-S2)**2

       Mrr = ( (S1-S2)*S1rr - S1*(S1rr-S2rr) ) / (S1-S2)**2
       Mur = ( (S1-S2)*S1ur - S1*(S1ur-S2ur) ) / (S1-S2)**2
       Mvr = ( (S1-S2)*S1vr - S1*(S1vr-S2vr) ) / (S1-S2)**2
       Mwr = ( (S1-S2)*S1wr - S1*(S1wr-S2wr) ) / (S1-S2)**2
       Mpr = ( (S1-S2)*S1pr - S1*(S1pr-S2pr) ) / (S1-S2)**2

!     The central difference plus dissipation part of flux:

!     Fc1 = (1.0_dp-M)*dU1

       Fc1rl = -Mrl*dU1 + (1.0_dp-M)*dU1rl
       Fc1ul = -Mul*dU1 + (1.0_dp-M)*dU1ul
       Fc1vl = -Mvl*dU1 + (1.0_dp-M)*dU1vl
       Fc1wl = -Mwl*dU1 + (1.0_dp-M)*dU1wl
       Fc1pl = -Mpl*dU1 + (1.0_dp-M)*dU1pl

       Fc1rr = -Mrr*dU1 + (1.0_dp-M)*dU1rr
       Fc1ur = -Mur*dU1 + (1.0_dp-M)*dU1ur
       Fc1vr = -Mvr*dU1 + (1.0_dp-M)*dU1vr
       Fc1wr = -Mwr*dU1 + (1.0_dp-M)*dU1wr
       Fc1pr = -Mpr*dU1 + (1.0_dp-M)*dU1pr

!     Fc2 = (1.0_dp-M)*(0.5_dp*(pressl+pressr)*xnorm+dU2)

       Fc2rl = -Mrl*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslrl+pressrrl)*xnorm+dU2rl)
       Fc2ul = -Mul*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslul+pressrul)*xnorm+dU2ul)
       Fc2vl = -Mvl*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslvl+pressrvl)*xnorm+dU2vl)
       Fc2wl = -Mwl*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslwl+pressrwl)*xnorm+dU2wl)
       Fc2pl = -Mpl*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslpl+pressrpl)*xnorm+dU2pl)

       Fc2rr = -Mrr*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslrr+pressrrr)*xnorm+dU2rr)
       Fc2ur = -Mur*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslur+pressrur)*xnorm+dU2ur)
       Fc2vr = -Mvr*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslvr+pressrvr)*xnorm+dU2vr)
       Fc2wr = -Mwr*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslwr+pressrwr)*xnorm+dU2wr)
       Fc2pr = -Mpr*(0.5_dp*(pressl+pressr)*xnorm+dU2)                         &
             + (1.0_dp-M)*(0.5_dp*(presslpr+pressrpr)*xnorm+dU2pr)

!     Fc3 = (1.0_dp-M)*(0.5_dp*(pressl+pressr)*ynorm+dU3)

       Fc3rl = -Mrl*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslrl+pressrrl)*ynorm+dU3rl)
       Fc3ul = -Mul*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslul+pressrul)*ynorm+dU3ul)
       Fc3vl = -Mvl*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslvl+pressrvl)*ynorm+dU3vl)
       Fc3wl = -Mwl*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslwl+pressrwl)*ynorm+dU3wl)
       Fc3pl = -Mpl*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslpl+pressrpl)*ynorm+dU3pl)

       Fc3rr = -Mrr*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslrr+pressrrr)*ynorm+dU3rr)
       Fc3ur = -Mur*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslur+pressrur)*ynorm+dU3ur)
       Fc3vr = -Mvr*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslvr+pressrvr)*ynorm+dU3vr)
       Fc3wr = -Mwr*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslwr+pressrwr)*ynorm+dU3wr)
       Fc3pr = -Mpr*(0.5_dp*(pressl+pressr)*ynorm+dU3)                         &
             + (1.0_dp-M)*(0.5_dp*(presslpr+pressrpr)*ynorm+dU3pr)

!     Fc4 = (1.0_dp-M)*(0.5_dp*(pressl+pressr)*znorm+dU4)

       Fc4rl = -Mrl*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslrl+pressrrl)*znorm+dU4rl)
       Fc4ul = -Mul*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslul+pressrul)*znorm+dU4ul)
       Fc4vl = -Mvl*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslvl+pressrvl)*znorm+dU4vl)
       Fc4wl = -Mwl*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslwl+pressrwl)*znorm+dU4wl)
       Fc4pl = -Mpl*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslpl+pressrpl)*znorm+dU4pl)

       Fc4rr = -Mrr*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslrr+pressrrr)*znorm+dU4rr)
       Fc4ur = -Mur*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslur+pressrur)*znorm+dU4ur)
       Fc4vr = -Mvr*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslvr+pressrvr)*znorm+dU4vr)
       Fc4wr = -Mwr*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslwr+pressrwr)*znorm+dU4wr)
       Fc4pr = -Mpr*(0.5_dp*(pressl+pressr)*znorm+dU4)                         &
             + (1.0_dp-M)*(0.5_dp*(presslpr+pressrpr)*znorm+dU4pr)

!     Fc5 = (1.0_dp-M)*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)

       Fc5rl = -Mrl*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslrl*ubarl+pressl*ubarlrl               &
                                        +pressrrl*ubarr+pressr*ubarrrl)+dU5rl)
       Fc5ul = -Mul*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslul*ubarl+pressl*ubarlul               &
                                        +pressrul*ubarr+pressr*ubarrul)+dU5ul)
       Fc5vl = -Mvl*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslvl*ubarl+pressl*ubarlvl               &
                                        +pressrvl*ubarr+pressr*ubarrvl)+dU5vl)
       Fc5wl = -Mwl*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslwl*ubarl+pressl*ubarlwl               &
                                        +pressrwl*ubarr+pressr*ubarrwl)+dU5wl)
       Fc5pl = -Mpl*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslpl*ubarl+pressl*ubarlpl               &
                                        +pressrpl*ubarr+pressr*ubarrpl)+dU5pl)

       Fc5rr = -Mrr*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslrr*ubarl+pressl*ubarlrr               &
                                        +pressrrr*ubarr+pressr*ubarrrr)+dU5rr)
       Fc5ur = -Mur*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslur*ubarl+pressl*ubarlur               &
                                        +pressrur*ubarr+pressr*ubarrur)+dU5ur)
       Fc5vr = -Mvr*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslvr*ubarl+pressl*ubarlvr               &
                                        +pressrvr*ubarr+pressr*ubarrvr)+dU5vr)
       Fc5wr = -Mwr*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslwr*ubarl+pressl*ubarlwr               &
                                        +pressrwr*ubarr+pressr*ubarrwr)+dU5wr)
       Fc5pr = -Mpr*(0.5_dp*(pressl*ubarl+pressr*ubarr)+dU5)                   &
             + (1.0_dp-M)*(0.5_dp*(presslpr*ubarl+pressl*ubarlpr               &
                                        +pressrpr*ubarr+pressr*ubarrpr)+dU5pr)

!     Load the proper upwind portion of flux (Eqn. 48)
!     based on wave speeds (Eqn. 24):

      pick_flux: if ( S1 > 0.0_dp ) then ! use left state

!       Fu1 = M*rhol*(ubarl-S2)

         Fu1rl=Mrl*rhol*(ubarl-S2) + M*rholrl*(ubarl-S2) + M*rhol*(ubarlrl-S2rl)
         Fu1ul=Mul*rhol*(ubarl-S2) + M*rholul*(ubarl-S2) + M*rhol*(ubarlul-S2ul)
         Fu1vl=Mvl*rhol*(ubarl-S2) + M*rholvl*(ubarl-S2) + M*rhol*(ubarlvl-S2vl)
         Fu1wl=Mwl*rhol*(ubarl-S2) + M*rholwl*(ubarl-S2) + M*rhol*(ubarlwl-S2wl)
         Fu1pl=Mpl*rhol*(ubarl-S2) + M*rholpl*(ubarl-S2) + M*rhol*(ubarlpl-S2pl)

         Fu1rr=Mrr*rhol*(ubarl-S2) + M*rholrr*(ubarl-S2) + M*rhol*(ubarlrr-S2rr)
         Fu1ur=Mur*rhol*(ubarl-S2) + M*rholur*(ubarl-S2) + M*rhol*(ubarlur-S2ur)
         Fu1vr=Mvr*rhol*(ubarl-S2) + M*rholvr*(ubarl-S2) + M*rhol*(ubarlvr-S2vr)
         Fu1wr=Mwr*rhol*(ubarl-S2) + M*rholwr*(ubarl-S2) + M*rhol*(ubarlwr-S2wr)
         Fu1pr=Mpr*rhol*(ubarl-S2) + M*rholpr*(ubarl-S2) + M*rhol*(ubarlpr-S2pr)

!       Fu2 = M*(rhol*ul*(ubarl-S2)+pressl*xnorm)

         Fu2rl = Mrl*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholrl*ul*(ubarl-S2)+rhol*ulrl*(ubarl-S2)                  &
                   +rhol*ul*(ubarlrl-S2rl)+presslrl*xnorm)
         Fu2ul = Mul*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholul*ul*(ubarl-S2)+rhol*ulul*(ubarl-S2)                  &
                   +rhol*ul*(ubarlul-S2ul)+presslul*xnorm)
         Fu2vl =  Mvl*(rhol*ul*(ubarl-S2)+pressl*xnorm)                        &
               + M*(rholvl*ul*(ubarl-S2)+rhol*ulvl*(ubarl-S2)                  &
                   +rhol*ul*(ubarlvl-S2vl)+presslvl*xnorm)
         Fu2wl = Mwl*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholwl*ul*(ubarl-S2)+rhol*ulwl*(ubarl-S2)                  &
                   +rhol*ul*(ubarlwl-S2wl)+presslwl*xnorm)
         Fu2pl =  Mpl*(rhol*ul*(ubarl-S2)+pressl*xnorm)                        &
               + M*(rholpl*ul*(ubarl-S2)+rhol*ulpl*(ubarl-S2)                  &
                   +rhol*ul*(ubarlpl-S2pl)+presslpl*xnorm)

         Fu2rr = Mrr*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholrr*ul*(ubarl-S2)+rhol*ulrr*(ubarl-S2)                  &
                   +rhol*ul*(ubarlrr-S2rr)+presslrr*xnorm)
         Fu2ur = Mur*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholur*ul*(ubarl-S2)+rhol*ulur*(ubarl-S2)                  &
                   +rhol*ul*(ubarlur-S2ur)+presslur*xnorm)
         Fu2vr = Mvr*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholvr*ul*(ubarl-S2)+rhol*ulvr*(ubarl-S2)                  &
                   +rhol*ul*(ubarlvr-S2vr)+presslvr*xnorm)
         Fu2wr = Mwr*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholwr*ul*(ubarl-S2)+rhol*ulwr*(ubarl-S2)                  &
                   +rhol*ul*(ubarlwr-S2wr)+presslwr*xnorm)
         Fu2pr = Mpr*(rhol*ul*(ubarl-S2)+pressl*xnorm)                         &
               + M*(rholpr*ul*(ubarl-S2)+rhol*ulpr*(ubarl-S2)                  &
                   +rhol*ul*(ubarlpr-S2pr)+presslpr*xnorm)

!       Fu3 = M*(rhol*vl*(ubarl-S2)+pressl*ynorm)


         Fu3rl = Mrl*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholrl*vl*(ubarl-S2)+rhol*vlrl*(ubarl-S2)                  &
                   +rhol*vl*(ubarlrl-S2rl)+presslrl*ynorm)
         Fu3ul = Mul*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholul*vl*(ubarl-S2)+rhol*vlul*(ubarl-S2)                  &
                   +rhol*vl*(ubarlul-S2ul)+presslul*ynorm)
         Fu3vl = Mvl*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholvl*vl*(ubarl-S2)+rhol*vlvl*(ubarl-S2)                  &
                   +rhol*vl*(ubarlvl-S2vl)+presslvl*ynorm)
         Fu3wl = Mwl*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholwl*vl*(ubarl-S2)+rhol*vlwl*(ubarl-S2)                  &
                   +rhol*vl*(ubarlwl-S2wl)+presslwl*ynorm)
         Fu3pl = Mpl*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholpl*vl*(ubarl-S2)+rhol*vlpl*(ubarl-S2)                  &
                   +rhol*vl*(ubarlpl-S2pl)+presslpl*ynorm)

         Fu3rr = Mrr*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholrr*vl*(ubarl-S2)+rhol*vlrr*(ubarl-S2)                  &
                   +rhol*vl*(ubarlrr-S2rr)+presslrr*ynorm)
         Fu3ur = Mur*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholur*vl*(ubarl-S2)+rhol*vlur*(ubarl-S2)                  &
                   +rhol*vl*(ubarlur-S2ur)+presslur*ynorm)
         Fu3vr = Mvr*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholvr*vl*(ubarl-S2)+rhol*vlvr*(ubarl-S2)                  &
                   +rhol*vl*(ubarlvr-S2vr)+presslvr*ynorm)
         Fu3wr = Mwr*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholwr*vl*(ubarl-S2)+rhol*vlwr*(ubarl-S2)                  &
                   +rhol*vl*(ubarlwr-S2wr)+presslwr*ynorm)
         Fu3pr = Mpr*(rhol*vl*(ubarl-S2)+pressl*ynorm)                         &
               + M*(rholpr*vl*(ubarl-S2)+rhol*vlpr*(ubarl-S2)                  &
                   +rhol*vl*(ubarlpr-S2pr)+presslpr*ynorm)

!       Fu4 = M*(rhol*wl*(ubarl-S2)+pressl*znorm)

         Fu4rl = Mrl*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholrl*wl*(ubarl-S2)+rhol*wlrl*(ubarl-S2)                  &
                   +rhol*wl*(ubarlrl-S2rl)+presslrl*znorm)
         Fu4ul = Mul*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholul*wl*(ubarl-S2)+rhol*wlul*(ubarl-S2)                  &
                   +rhol*wl*(ubarlul-S2ul)+presslul*znorm)
         Fu4vl = Mvl*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholvl*wl*(ubarl-S2)+rhol*wlvl*(ubarl-S2)                  &
                   +rhol*wl*(ubarlvl-S2vl)+presslvl*znorm)
         Fu4wl = Mwl*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholwl*wl*(ubarl-S2)+rhol*wlwl*(ubarl-S2)                  &
                   +rhol*wl*(ubarlwl-S2wl)+presslwl*znorm)
         Fu4pl = Mpl*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholpl*wl*(ubarl-S2)+rhol*wlpl*(ubarl-S2)                  &
                   +rhol*wl*(ubarlpl-S2pl)+presslpl*znorm)

         Fu4rr = Mrr*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholrr*wl*(ubarl-S2)+rhol*wlrr*(ubarl-S2)                  &
                   +rhol*wl*(ubarlrr-S2rr)+presslrr*znorm)
         Fu4ur = Mur*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholur*wl*(ubarl-S2)+rhol*wlur*(ubarl-S2)                  &
                   +rhol*wl*(ubarlur-S2ur)+presslur*znorm)
         Fu4vr = Mvr*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholvr*wl*(ubarl-S2)+rhol*wlvr*(ubarl-S2)                  &
                   +rhol*wl*(ubarlvr-S2vr)+presslvr*znorm)
         Fu4wr = Mwr*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholwr*wl*(ubarl-S2)+rhol*wlwr*(ubarl-S2)                  &
                   +rhol*wl*(ubarlwr-S2wr)+presslwr*znorm)
         Fu4pr = Mpr*(rhol*wl*(ubarl-S2)+pressl*znorm)                         &
               + M*(rholpr*wl*(ubarl-S2)+rhol*wlpr*(ubarl-S2)                  &
                   +rhol*wl*(ubarlpr-S2pr)+presslpr*znorm)

!       Fu5 = M*(energyl*(ubarl-S2)+pressl*ubarl)

         Fu5rl = Mrl*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylrl*(ubarl-S2)+energyl*(ubarlrl-S2rl)                &
                   +presslrl*ubarl+pressl*ubarlrl)
         Fu5ul = Mul*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylul*(ubarl-S2)+energyl*(ubarlul-S2ul)                &
                   +presslul*ubarl+pressl*ubarlul)
         Fu5vl = Mvl*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylvl*(ubarl-S2)+energyl*(ubarlvl-S2vl)                &
                   +presslvl*ubarl+pressl*ubarlvl)
         Fu5wl = Mwl*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylwl*(ubarl-S2)+energyl*(ubarlwl-S2wl)                &
                   +presslwl*ubarl+pressl*ubarlwl)
         Fu5pl = Mpl*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylpl*(ubarl-S2)+energyl*(ubarlpl-S2pl)                &
                   +presslpl*ubarl+pressl*ubarlpl)

         Fu5rr = Mrr*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylrr*(ubarl-S2)+energyl*(ubarlrr-S2rr)                &
                   +presslrr*ubarl+pressl*ubarlrr)
         Fu5ur = Mur*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylur*(ubarl-S2)+energyl*(ubarlur-S2ur)                &
                   +presslur*ubarl+pressl*ubarlur)
         Fu5vr = Mvr*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylvr*(ubarl-S2)+energyl*(ubarlvr-S2vr)                &
                   +presslvr*ubarl+pressl*ubarlvr)
         Fu5wr = Mwr*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylwr*(ubarl-S2)+energyl*(ubarlwr-S2wr)                &
                   +presslwr*ubarl+pressl*ubarlwr)
         Fu5pr = Mpr*(energyl*(ubarl-S2)+pressl*ubarl)                         &
               + M*(energylpr*(ubarl-S2)+energyl*(ubarlpr-S2pr)                &
                   +presslpr*ubarl+pressl*ubarlpr)

      else  ! use right state

!       Fu1 = M*rhor*(ubarr-S2)

         Fu1rl=Mrl*rhor*(ubarr-S2) + M*rhorrl*(ubarr-S2) + M*rhor*(ubarrrl-S2rl)
         Fu1ul=Mul*rhor*(ubarr-S2) + M*rhorul*(ubarr-S2) + M*rhor*(ubarrul-S2ul)
         Fu1vl=Mvl*rhor*(ubarr-S2) + M*rhorvl*(ubarr-S2) + M*rhor*(ubarrvl-S2vl)
         Fu1wl=Mwl*rhor*(ubarr-S2) + M*rhorwl*(ubarr-S2) + M*rhor*(ubarrwl-S2wl)
         Fu1pl=Mpl*rhor*(ubarr-S2) + M*rhorpl*(ubarr-S2) + M*rhor*(ubarrpl-S2pl)

         Fu1rr=Mrr*rhor*(ubarr-S2) + M*rhorrr*(ubarr-S2) + M*rhor*(ubarrrr-S2rr)
         Fu1ur=Mur*rhor*(ubarr-S2) + M*rhorur*(ubarr-S2) + M*rhor*(ubarrur-S2ur)
         Fu1vr=Mvr*rhor*(ubarr-S2) + M*rhorvr*(ubarr-S2) + M*rhor*(ubarrvr-S2vr)
         Fu1wr=Mwr*rhor*(ubarr-S2) + M*rhorwr*(ubarr-S2) + M*rhor*(ubarrwr-S2wr)
         Fu1pr=Mpr*rhor*(ubarr-S2) + M*rhorpr*(ubarr-S2) + M*rhor*(ubarrpr-S2pr)

!       Fu2 = M*(rhor*ur*(ubarr-S2)+pressr*xnorm)

         Fu2rl = Mrl*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorrl*ur*(ubarr-S2)+rhor*urrl*(ubarr-S2)                  &
                   +rhor*ur*(ubarrrl-S2rl)+pressrrl*xnorm)
         Fu2ul = Mul*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorul*ur*(ubarr-S2)+rhor*urul*(ubarr-S2)                  &
                   +rhor*ur*(ubarrul-S2ul)+pressrul*xnorm)
         Fu2vl = Mvl*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorvl*ur*(ubarr-S2)+rhor*urvl*(ubarr-S2)                  &
                   +rhor*ur*(ubarrvl-S2vl)+pressrvl*xnorm)
         Fu2wl = Mwl*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorwl*ur*(ubarr-S2)+rhor*urwl*(ubarr-S2)                  &
                   +rhor*ur*(ubarrwl-S2wl)+pressrwl*xnorm)
         Fu2pl = Mpl*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorpl*ur*(ubarr-S2)+rhor*urpl*(ubarr-S2)                  &
                   +rhor*ur*(ubarrpl-S2pl)+pressrpl*xnorm)

         Fu2rr = Mrr*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorrr*ur*(ubarr-S2)+rhor*urrr*(ubarr-S2)                  &
                   +rhor*ur*(ubarrrr-S2rr)+pressrrr*xnorm)
         Fu2ur = Mur*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorur*ur*(ubarr-S2)+rhor*urur*(ubarr-S2)                  &
                   +rhor*ur*(ubarrur-S2ur)+pressrur*xnorm)
         Fu2vr = Mvr*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorvr*ur*(ubarr-S2)+rhor*urvr*(ubarr-S2)                  &
                   +rhor*ur*(ubarrvr-S2vr)+pressrvr*xnorm)
         Fu2wr = Mwr*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorwr*ur*(ubarr-S2)+rhor*urwr*(ubarr-S2)                  &
                   +rhor*ur*(ubarrwr-S2wr)+pressrwr*xnorm)
         Fu2pr = Mpr*(rhor*ur*(ubarr-S2)+pressr*xnorm)                         &
               + M*(rhorpr*ur*(ubarr-S2)+rhor*urpr*(ubarr-S2)                  &
                   +rhor*ur*(ubarrpr-S2pr)+pressrpr*xnorm)

!       Fu3 = M*(rhor*vr*(ubarr-S2)+pressr*ynorm)

         Fu3rl = Mrl*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorrl*vr*(ubarr-S2)+rhor*vrrl*(ubarr-S2)                  &
                   +rhor*vr*(ubarrrl-S2rl)+pressrrl*ynorm)
         Fu3ul = Mul*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorul*vr*(ubarr-S2)+rhor*vrul*(ubarr-S2)                  &
                   +rhor*vr*(ubarrul-S2ul)+pressrul*ynorm)
         Fu3vl = Mvl*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorvl*vr*(ubarr-S2)+rhor*vrvl*(ubarr-S2)                  &
                   +rhor*vr*(ubarrvl-S2vl)+pressrvl*ynorm)
         Fu3wl = Mwl*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorwl*vr*(ubarr-S2)+rhor*vrwl*(ubarr-S2)                  &
                   +rhor*vr*(ubarrwl-S2wl)+pressrwl*ynorm)
         Fu3pl = Mpl*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorpl*vr*(ubarr-S2)+rhor*vrpl*(ubarr-S2)                  &
                   +rhor*vr*(ubarrpl-S2pl)+pressrpl*ynorm)

         Fu3rr = Mrr*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorrr*vr*(ubarr-S2)+rhor*vrrr*(ubarr-S2)                  &
                   +rhor*vr*(ubarrrr-S2rr)+pressrrr*ynorm)
         Fu3ur = Mur*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorur*vr*(ubarr-S2)+rhor*vrur*(ubarr-S2)                  &
                   +rhor*vr*(ubarrur-S2ur)+pressrur*ynorm)
         Fu3vr = Mvr*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorvr*vr*(ubarr-S2)+rhor*vrvr*(ubarr-S2)                  &
                   +rhor*vr*(ubarrvr-S2vr)+pressrvr*ynorm)
         Fu3wr = Mwr*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorwr*vr*(ubarr-S2)+rhor*vrwr*(ubarr-S2)                  &
                   +rhor*vr*(ubarrwr-S2wr)+pressrwr*ynorm)
         Fu3pr = Mpr*(rhor*vr*(ubarr-S2)+pressr*ynorm)                         &
               + M*(rhorpr*vr*(ubarr-S2)+rhor*vrpr*(ubarr-S2)                  &
                   +rhor*vr*(ubarrpr-S2pr)+pressrpr*ynorm)

!       Fu4 = M*(rhor*wr*(ubarr-S2)+pressr*znorm)

         Fu4rl = Mrl*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorrl*wr*(ubarr-S2)+rhor*wrrl*(ubarr-S2)                  &
                   +rhor*wr*(ubarrrl-S2rl)+pressrrl*znorm)
         Fu4ul = Mul*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorul*wr*(ubarr-S2)+rhor*wrul*(ubarr-S2)                  &
                   +rhor*wr*(ubarrul-S2ul)+pressrul*znorm)
         Fu4vl = Mvl*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorvl*wr*(ubarr-S2)+rhor*wrvl*(ubarr-S2)                  &
                   +rhor*wr*(ubarrvl-S2vl)+pressrvl*znorm)
         Fu4wl = Mwl*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorwl*wr*(ubarr-S2)+rhor*wrwl*(ubarr-S2)                  &
                   +rhor*wr*(ubarrwl-S2wl)+pressrwl*znorm)
         Fu4pl = Mpl*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorpl*wr*(ubarr-S2)+rhor*wrpl*(ubarr-S2)                  &
                   +rhor*wr*(ubarrpl-S2pl)+pressrpl*znorm)

         Fu4rr = Mrr*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorrr*wr*(ubarr-S2)+rhor*wrrr*(ubarr-S2)                  &
                   +rhor*wr*(ubarrrr-S2rr)+pressrrr*znorm)
         Fu4ur = Mur*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorur*wr*(ubarr-S2)+rhor*wrur*(ubarr-S2)                  &
                   +rhor*wr*(ubarrur-S2ur)+pressrur*znorm)
         Fu4vr = Mvr*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorvr*wr*(ubarr-S2)+rhor*wrvr*(ubarr-S2)                  &
                   +rhor*wr*(ubarrvr-S2vr)+pressrvr*znorm)
         Fu4wr = Mwr*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorwr*wr*(ubarr-S2)+rhor*wrwr*(ubarr-S2)                  &
                   +rhor*wr*(ubarrwr-S2wr)+pressrwr*znorm)
         Fu4pr = Mpr*(rhor*wr*(ubarr-S2)+pressr*znorm)                         &
               + M*(rhorpr*wr*(ubarr-S2)+rhor*wrpr*(ubarr-S2)                  &
                   +rhor*wr*(ubarrpr-S2pr)+pressrpr*znorm)

!       Fu5 = M*(energyr*(ubarr-S2)+pressr*ubarr)

         Fu5rl = Mrl*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrrl*(ubarr-S2)+energyr*(ubarrrl-S2rl)                &
                   +pressrrl*ubarr+pressr*ubarrrl)
         Fu5ul = Mul*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrul*(ubarr-S2)+energyr*(ubarrul-S2ul)                &
                   +pressrul*ubarr+pressr*ubarrul)
         Fu5vl = Mvl*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrvl*(ubarr-S2)+energyr*(ubarrvl-S2vl)                &
                   +pressrvl*ubarr+pressr*ubarrvl)
         Fu5wl = Mwl*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrwl*(ubarr-S2)+energyr*(ubarrwl-S2wl)                &
                   +pressrwl*ubarr+pressr*ubarrwl)
         Fu5pl = Mpl*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrpl*(ubarr-S2)+energyr*(ubarrpl-S2pl)                &
                   +pressrpl*ubarr+pressr*ubarrpl)

         Fu5rr = Mrr*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrrr*(ubarr-S2)+energyr*(ubarrrr-S2rr)                &
                   +pressrrr*ubarr+pressr*ubarrrr)
         Fu5ur = Mur*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrur*(ubarr-S2)+energyr*(ubarrur-S2ur)                &
                   +pressrur*ubarr+pressr*ubarrur)
         Fu5vr = Mvr*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrvr*(ubarr-S2)+energyr*(ubarrvr-S2vr)                &
                   +pressrvr*ubarr+pressr*ubarrvr)
         Fu5wr = Mwr*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrwr*(ubarr-S2)+energyr*(ubarrwr-S2wr)                &
                   +pressrwr*ubarr+pressr*ubarrwr)
         Fu5pr = Mpr*(energyr*(ubarr-S2)+pressr*ubarr)                         &
               + M*(energyrpr*(ubarr-S2)+energyr*(ubarrpr-S2pr)                &
                   +pressrpr*ubarr+pressr*ubarrpr)

      end if pick_flux

!     Sum the two flux components to get total flux:

!     F1 = Fc1 + Fu1

       F1rl = Fc1rl + Fu1rl
       F1ul = Fc1ul + Fu1ul
       F1vl = Fc1vl + Fu1vl
       F1wl = Fc1wl + Fu1wl
       F1pl = Fc1pl + Fu1pl

       F1rr = Fc1rr + Fu1rr
       F1ur = Fc1ur + Fu1ur
       F1vr = Fc1vr + Fu1vr
       F1wr = Fc1wr + Fu1wr
       F1pr = Fc1pr + Fu1pr

!     F2 = Fc2 + Fu2

       F2rl = Fc2rl + Fu2rl
       F2ul = Fc2ul + Fu2ul
       F2vl = Fc2vl + Fu2vl
       F2wl = Fc2wl + Fu2wl
       F2pl = Fc2pl + Fu2pl

       F2rr = Fc2rr + Fu2rr
       F2ur = Fc2ur + Fu2ur
       F2vr = Fc2vr + Fu2vr
       F2wr = Fc2wr + Fu2wr
       F2pr = Fc2pr + Fu2pr

!     F3 = Fc3 + Fu3

       F3rl = Fc3rl + Fu3rl
       F3ul = Fc3ul + Fu3ul
       F3vl = Fc3vl + Fu3vl
       F3wl = Fc3wl + Fu3wl
       F3pl = Fc3pl + Fu3pl

       F3rr = Fc3rr + Fu3rr
       F3ur = Fc3ur + Fu3ur
       F3vr = Fc3vr + Fu3vr
       F3wr = Fc3wr + Fu3wr
       F3pr = Fc3pr + Fu3pr

!     F4 = Fc4 + Fu4

       F4rl = Fc4rl + Fu4rl
       F4ul = Fc4ul + Fu4ul
       F4vl = Fc4vl + Fu4vl
       F4wl = Fc4wl + Fu4wl
       F4pl = Fc4pl + Fu4pl

       F4rr = Fc4rr + Fu4rr
       F4ur = Fc4ur + Fu4ur
       F4vr = Fc4vr + Fu4vr
       F4wr = Fc4wr + Fu4wr
       F4pr = Fc4pr + Fu4pr

!     F5 = Fc5 + Fu5

       F5rl = Fc5rl + Fu5rl
       F5ul = Fc5ul + Fu5ul
       F5vl = Fc5vl + Fu5vl
       F5wl = Fc5wl + Fu5wl
       F5pl = Fc5pl + Fu5pl

       F5rr = Fc5rr + Fu5rr
       F5ur = Fc5ur + Fu5ur
       F5vr = Fc5vr + Fu5vr
       F5wr = Fc5wr + Fu5wr
       F5pr = Fc5pr + Fu5pr

!  Convert from primitive to conservative derivatives

!  q = primitive
!  Q = conservative

!     Left:

      q1Q1l = 1.0_dp
      q1Q2l = 0.0_dp
      q1Q3l = 0.0_dp
      q1Q4l = 0.0_dp
      q1Q5l = 0.0_dp

      q2Q1l = - ul / rhol
      q2Q2l = 1.0_dp / rhol
      q2Q3l = 0.0_dp
      q2Q4l = 0.0_dp
      q2Q5l = 0.0_dp

      q3Q1l = - vl / rhol
      q3Q2l = 0.0_dp
      q3Q3l = 1.0_dp / rhol
      q3Q4l = 0.0_dp
      q3Q5l = 0.0_dp

      q4Q1l = - wl / rhol
      q4Q2l = 0.0_dp
      q4Q3l = 0.0_dp
      q4Q4l = 1.0_dp / rhol
      q4Q5l = 0.0_dp

      q5Q1l = gm1 / 2.0_dp * (ul*ul + vl*vl + wl*wl)
      q5Q2l = - gm1 * ul
      q5Q3l = - gm1 * vl
      q5Q4l = - gm1 * wl
      q5Q5l = gm1

      dfp(1,1) = F1rl*q1Q1l + F1ul*q2Q1l + F1vl*q3Q1l + F1wl*q4Q1l + F1pl*q5Q1l
      dfp(1,2) = F1rl*q1Q2l + F1ul*q2Q2l + F1vl*q3Q2l + F1wl*q4Q2l + F1pl*q5Q2l
      dfp(1,3) = F1rl*q1Q3l + F1ul*q2Q3l + F1vl*q3Q3l + F1wl*q4Q3l + F1pl*q5Q3l
      dfp(1,4) = F1rl*q1Q4l + F1ul*q2Q4l + F1vl*q3Q4l + F1wl*q4Q4l + F1pl*q5Q4l
      dfp(1,5) = F1rl*q1Q5l + F1ul*q2Q5l + F1vl*q3Q5l + F1wl*q4Q5l + F1pl*q5Q5l

      dfp(2,1) = F2rl*q1Q1l + F2ul*q2Q1l + F2vl*q3Q1l + F2wl*q4Q1l + F2pl*q5Q1l
      dfp(2,2) = F2rl*q1Q2l + F2ul*q2Q2l + F2vl*q3Q2l + F2wl*q4Q2l + F2pl*q5Q2l
      dfp(2,3) = F2rl*q1Q3l + F2ul*q2Q3l + F2vl*q3Q3l + F2wl*q4Q3l + F2pl*q5Q3l
      dfp(2,4) = F2rl*q1Q4l + F2ul*q2Q4l + F2vl*q3Q4l + F2wl*q4Q4l + F2pl*q5Q4l
      dfp(2,5) = F2rl*q1Q5l + F2ul*q2Q5l + F2vl*q3Q5l + F2wl*q4Q5l + F2pl*q5Q5l

      dfp(3,1) = F3rl*q1Q1l + F3ul*q2Q1l + F3vl*q3Q1l + F3wl*q4Q1l + F3pl*q5Q1l
      dfp(3,2) = F3rl*q1Q2l + F3ul*q2Q2l + F3vl*q3Q2l + F3wl*q4Q2l + F3pl*q5Q2l
      dfp(3,3) = F3rl*q1Q3l + F3ul*q2Q3l + F3vl*q3Q3l + F3wl*q4Q3l + F3pl*q5Q3l
      dfp(3,4) = F3rl*q1Q4l + F3ul*q2Q4l + F3vl*q3Q4l + F3wl*q4Q4l + F3pl*q5Q4l
      dfp(3,5) = F3rl*q1Q5l + F3ul*q2Q5l + F3vl*q3Q5l + F3wl*q4Q5l + F3pl*q5Q5l

      dfp(4,1) = F4rl*q1Q1l + F4ul*q2Q1l + F4vl*q3Q1l + F4wl*q4Q1l + F4pl*q5Q1l
      dfp(4,2) = F4rl*q1Q2l + F4ul*q2Q2l + F4vl*q3Q2l + F4wl*q4Q2l + F4pl*q5Q2l
      dfp(4,3) = F4rl*q1Q3l + F4ul*q2Q3l + F4vl*q3Q3l + F4wl*q4Q3l + F4pl*q5Q3l
      dfp(4,4) = F4rl*q1Q4l + F4ul*q2Q4l + F4vl*q3Q4l + F4wl*q4Q4l + F4pl*q5Q4l
      dfp(4,5) = F4rl*q1Q5l + F4ul*q2Q5l + F4vl*q3Q5l + F4wl*q4Q5l + F4pl*q5Q5l

      dfp(5,1) = F5rl*q1Q1l + F5ul*q2Q1l + F5vl*q3Q1l + F5wl*q4Q1l + F5pl*q5Q1l
      dfp(5,2) = F5rl*q1Q2l + F5ul*q2Q2l + F5vl*q3Q2l + F5wl*q4Q2l + F5pl*q5Q2l
      dfp(5,3) = F5rl*q1Q3l + F5ul*q2Q3l + F5vl*q3Q3l + F5wl*q4Q3l + F5pl*q5Q3l
      dfp(5,4) = F5rl*q1Q4l + F5ul*q2Q4l + F5vl*q3Q4l + F5wl*q4Q4l + F5pl*q5Q4l
      dfp(5,5) = F5rl*q1Q5l + F5ul*q2Q5l + F5vl*q3Q5l + F5wl*q4Q5l + F5pl*q5Q5l

!     Right:

      q1Q1r = 1.0_dp
      q1Q2r = 0.0_dp
      q1Q3r = 0.0_dp
      q1Q4r = 0.0_dp
      q1Q5r = 0.0_dp

      q2Q1r = - ur / rhor
      q2Q2r = 1.0_dp / rhor
      q2Q3r = 0.0_dp
      q2Q4r = 0.0_dp
      q2Q5r = 0.0_dp

      q3Q1r = - vr / rhor
      q3Q2r = 0.0_dp
      q3Q3r = 1.0_dp / rhor
      q3Q4r = 0.0_dp
      q3Q5r = 0.0_dp

      q4Q1r = - wr / rhor
      q4Q2r = 0.0_dp
      q4Q3r = 0.0_dp
      q4Q4r = 1.0_dp / rhor
      q4Q5r = 0.0_dp

      q5Q1r = gm1 / 2.0_dp * (ur*ur + vr*vr + wr*wr)
      q5Q2r = - gm1 * ur
      q5Q3r = - gm1 * vr
      q5Q4r = - gm1 * wr
      q5Q5r = gm1

      dfm(1,1) = F1rr*q1Q1r + F1ur*q2Q1r + F1vr*q3Q1r + F1wr*q4Q1r + F1pr*q5Q1r
      dfm(1,2) = F1rr*q1Q2r + F1ur*q2Q2r + F1vr*q3Q2r + F1wr*q4Q2r + F1pr*q5Q2r
      dfm(1,3) = F1rr*q1Q3r + F1ur*q2Q3r + F1vr*q3Q3r + F1wr*q4Q3r + F1pr*q5Q3r
      dfm(1,4) = F1rr*q1Q4r + F1ur*q2Q4r + F1vr*q3Q4r + F1wr*q4Q4r + F1pr*q5Q4r
      dfm(1,5) = F1rr*q1Q5r + F1ur*q2Q5r + F1vr*q3Q5r + F1wr*q4Q5r + F1pr*q5Q5r

      dfm(2,1) = F2rr*q1Q1r + F2ur*q2Q1r + F2vr*q3Q1r + F2wr*q4Q1r + F2pr*q5Q1r
      dfm(2,2) = F2rr*q1Q2r + F2ur*q2Q2r + F2vr*q3Q2r + F2wr*q4Q2r + F2pr*q5Q2r
      dfm(2,3) = F2rr*q1Q3r + F2ur*q2Q3r + F2vr*q3Q3r + F2wr*q4Q3r + F2pr*q5Q3r
      dfm(2,4) = F2rr*q1Q4r + F2ur*q2Q4r + F2vr*q3Q4r + F2wr*q4Q4r + F2pr*q5Q4r
      dfm(2,5) = F2rr*q1Q5r + F2ur*q2Q5r + F2vr*q3Q5r + F2wr*q4Q5r + F2pr*q5Q5r

      dfm(3,1) = F3rr*q1Q1r + F3ur*q2Q1r + F3vr*q3Q1r + F3wr*q4Q1r + F3pr*q5Q1r
      dfm(3,2) = F3rr*q1Q2r + F3ur*q2Q2r + F3vr*q3Q2r + F3wr*q4Q2r + F3pr*q5Q2r
      dfm(3,3) = F3rr*q1Q3r + F3ur*q2Q3r + F3vr*q3Q3r + F3wr*q4Q3r + F3pr*q5Q3r
      dfm(3,4) = F3rr*q1Q4r + F3ur*q2Q4r + F3vr*q3Q4r + F3wr*q4Q4r + F3pr*q5Q4r
      dfm(3,5) = F3rr*q1Q5r + F3ur*q2Q5r + F3vr*q3Q5r + F3wr*q4Q5r + F3pr*q5Q5r

      dfm(4,1) = F4rr*q1Q1r + F4ur*q2Q1r + F4vr*q3Q1r + F4wr*q4Q1r + F4pr*q5Q1r
      dfm(4,2) = F4rr*q1Q2r + F4ur*q2Q2r + F4vr*q3Q2r + F4wr*q4Q2r + F4pr*q5Q2r
      dfm(4,3) = F4rr*q1Q3r + F4ur*q2Q3r + F4vr*q3Q3r + F4wr*q4Q3r + F4pr*q5Q3r
      dfm(4,4) = F4rr*q1Q4r + F4ur*q2Q4r + F4vr*q3Q4r + F4wr*q4Q4r + F4pr*q5Q4r
      dfm(4,5) = F4rr*q1Q5r + F4ur*q2Q5r + F4vr*q3Q5r + F4wr*q4Q5r + F4pr*q5Q5r

      dfm(5,1) = F5rr*q1Q1r + F5ur*q2Q1r + F5vr*q3Q1r + F5wr*q4Q1r + F5pr*q5Q1r
      dfm(5,2) = F5rr*q1Q2r + F5ur*q2Q2r + F5vr*q3Q2r + F5wr*q4Q2r + F5pr*q5Q2r
      dfm(5,3) = F5rr*q1Q3r + F5ur*q2Q3r + F5vr*q3Q3r + F5wr*q4Q3r + F5pr*q5Q3r
      dfm(5,4) = F5rr*q1Q4r + F5ur*q2Q4r + F5vr*q3Q4r + F5wr*q4Q4r + F5pr*q5Q4r
      dfm(5,5) = F5rr*q1Q5r + F5ur*q2Q5r + F5vr*q3Q5r + F5wr*q4Q5r + F5pr*q5Q5r

      if(node1 <= nnodes0) then
        idiag = g2m(node1)
        do k = 1, 5
          do j = 1, 5
            a_diag(j,k,idiag) = a_diag(j,k,idiag) + area*dfp(j,k)
          end do
        end do
      end if

      if(node2 <= nnodes0) then
        ioff  = fhelp(2,n)
        do k = 1, 5
          do j = 1, 5
            a_off(j,k,ioff)  = a_off(j,k,ioff) - area*dfp(j,k)
          end do
        end do
      end if

      if(node2 <= nnodes0) then
        idiag = g2m(node2)
        do k = 1, 5
          do j = 1, 5
            a_diag(j,k,idiag) = a_diag(j,k,idiag) - area*dfm(j,k)
          end do
        end do
      end if

      if(node1 <= nnodes0) then
        ioff  = fhelp(1,n)
        do k = 1, 5
          do j = 1, 5
            a_off(j,k,ioff)  = a_off(j,k,ioff) + area*dfm(j,k)
          end do
        end do
      end if

    end do jacobian_for_each_edge

!  Convert back to conserved variables

    call ptoe(qdim,qnode,n_tot,0)

  end subroutine flux_jacobian_aufs

!============================= FLUX_JACOBIAN_HLLCS ===========================80
!
! hllc Jacobian
!
! Qnode expected in conserved vars.
!
!=============================================================================80

  subroutine flux_jacobian_hllcs(nnodes0, nnodes01, nedgeloc, nedgeloc_2d,     &
                                 eptr, qnode, vol, x, y, z,                    &
                                 gradx, grady, gradz, phi,                     &
                                 xn, yn, zn, ra,                               &
                                 facespeed, ndim, eqn_set,                     &
                                 n_tot, n_grd, njac, g2m, fhelp,               &
                                 a_off, a_diag, max_nnz, ncell01)

    use fun3d_constants,         only : my_0, my_half, my_1, my_2, my_4
    use info_depr,               only : twod, cc_primal, xmach, re, tref, ntt, &
                                        ivisc, pr_limiter, pr_limiter_coeff
    use nml_nonlinear_solves,    only : itime
    use timeacc,                 only : pseudo_sub
    use fluid,                   only : gm1, gamma, sutherland_constant
    use inviscid_flux,           only : first_order_iterations, iflim
    use thermo,                  only : etop, ptoe
    use reconstruction,          only : pressure_limiter_coeff
    use flux_functions,          only : hllcs_conserved_jac_w_ddt
    use generic_gas_map,         only : n_etot, n_sonic_k, n_amu_k
    use solution_types,          only : generic_gas
    use nml_governing_equations, only : viscous_terms

    integer,                                 intent(in)    :: n_tot
    integer,                                 intent(in)    :: ndim
    integer,                                 intent(in)    :: eqn_set
    integer,                                 intent(in)    :: njac
    integer,                                 intent(in)    :: n_grd
    integer,                                 intent(in)    :: nnodes0
    integer,                                 intent(in)    :: nnodes01
    integer,                                 intent(in)    :: nedgeloc
    integer,                                 intent(in)    :: nedgeloc_2d
    integer,                                 intent(in)    :: max_nnz
    integer,                                 intent(in)    :: ncell01
    integer,   dimension(2,nedgeloc),        intent(in)    :: eptr
    integer,   dimension(2,nedgeloc),        intent(in)    :: fhelp
    integer,   dimension(:),                 intent(in)    :: g2m
    real(dp),  dimension(n_tot,nnodes01),    intent(inout) :: qnode
    real(dp),  dimension(nnodes01),          intent(in)    :: vol
    real(dp),  dimension(nnodes01),          intent(in)    :: x, y, z
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradz
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: phi
    real(dp),  dimension(nedgeloc),          intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),          intent(in)    :: facespeed
    real(odp), dimension(njac,njac,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(njac,njac,nnodes0), intent(inout) :: a_diag

    integer  :: n, node1, node2
    integer  :: nedge_flux_eval, my_ntt
    integer  :: idiag, k, j, ioff
    integer  :: qdim

    logical  :: second
    real(dp) :: xmean, ymean, zmean
    real(dp) :: xnorm, ynorm, znorm
    real(dp) :: area

    real(dp) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp) :: face_speed
    real(dp) :: pcoef, pr_limiter_coeff_g
    real(dp) :: power, shft, gradcc, laplcc
    real(dp) :: cstar, xmr
    real(dp) :: c_gen, mu1, mu2, mu

    real(dp), dimension(ndim+1) :: ql, qr
    real(dp), dimension(ndim)   :: phi1, phi2
    real(dp), dimension(ndim)   :: dflux_dgradx1
    real(dp), dimension(ndim)   :: dflux_dgrady1
    real(dp), dimension(ndim)   :: dflux_dgradz1
    real(dp), dimension(ndim)   :: dflux_drho1, dflux_dpress1

    real(dp), dimension(ndim)   :: dflux_dgradx2
    real(dp), dimension(ndim)   :: dflux_dgrady2
    real(dp), dimension(ndim)   :: dflux_dgradz2
    real(dp), dimension(ndim)   :: dflux_drho2, dflux_dpress2

    real(dp), dimension(ndim,ndim)   :: dflux_dql_prim, dflux_dqr_prim
    real(dp), dimension(ndim,ndim)   :: dflux_dq_cons_p, dflux_dq_cons_m
    real(dp), dimension(ndim,ndim)   :: dfm, dfp

    real(dp), dimension(nedgeloc)    :: fl_coeff

    integer,  parameter :: powerv = 4

    logical,  parameter :: hot = .false.
    logical,  parameter :: left_jacobian = .true.
    logical,  parameter :: rght_jacobian = .true.

    continue

    qdim = nnodes01
    if ( cc_primal ) qdim = ncell01

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    second = ( my_ntt > first_order_iterations )
!   second = .false.

    nedge_flux_eval = nedgeloc
!   my_exp   = my_3rd
!   my_fact1 = my_6
!   my_fact2 = my_3
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
!     my_exp   = my_half
!     my_fact1 = my_4
!     my_fact2 = my_1
    end if

    gradcc = my_1
    if (iflim == 0) then
      power  = my_1
      laplcc = my_4
      shft  = my_0
    else if (iflim == 3) then
      power  = my_1
      laplcc = my_4
      shft  = my_0
    else if (iflim == 4) then
      power  = my_1
      laplcc = my_4
      shft  = my_0
    else if (iflim == 5) then
      power  = my_1
      laplcc = my_2
      shft  = my_0
    else if (iflim == 6) then
      power  = my_1
      laplcc = my_2
      shft  = my_0
    else
      power  = my_1
      laplcc = my_2
      shft  = my_0
    end if

    cstar  = sutherland_constant/tref

!   Convert to primitive variables

    call etop(qdim,qnode,n_tot,eqn_set)

!   Call routines that control the use of limiters during the higher order
!   reconstruction so as to increase robustness at hign Mach numbers and
!   reduce dissipation at low Mach numbers

    if (pr_limiter .and. second .and. (iflim > 2) .and. (iflim < 13)) then
      call pressure_limiter_coeff(n_tot, nnodes01, nnodes0, nedgeloc, eptr,    &
                                  qnode, fl_coeff, eqn_set)
    end if

!   Loop over all the edges and calculate the inviscid fluxes
!   (in the 2D case, this loop contains edges on only one y=constant plane)

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      xmean = my_half*(x(node1) + x(node2))
      ymean = my_half*(y(node1) + y(node2))
      zmean = my_half*(z(node1) + z(node2))

      xnorm  = xn(n)
      ynorm  = yn(n)
      znorm  = zn(n)
      area   = ra(n)

!     Compute the normalized undivided pressure ratio and
!     pressure Laplacian based limiter

      pcoef = my_1
      if ( eqn_set == generic_gas ) then
        c_gen = my_half*(qnode(n_sonic_k(1),node1)+qnode(n_sonic_k(1),node2))
        if(ivisc >= 2)then
          mu1 = qnode(n_amu_k(1),node1)
          mu2 = qnode(n_amu_k(1),node2)
        else
          mu1 = my_0
          mu2 = my_0
        end if
      else
        c_gen = my_0
        if(ivisc >= 2)then
          xmr = xmach/re
          mu1 = viscosity_law(cstar,gamma*qnode(5,node1)/qnode(1,node1))*xmr
          mu2 = viscosity_law(cstar,gamma*qnode(5,node2)/qnode(1,node2))*xmr
        else
          mu1 = my_0
          mu2 = my_0
        end if
      end if
      mu = my_half*(mu1 + mu2)
      if (pr_limiter .and. second .and. (iflim > 2) .and. (iflim < 13)) then
        pr_limiter_coeff_g = pr_limiter_coeff
        pr_limiter_coeff = fl_coeff(n)
        pcoef=pressure_limiter(x(node1),y(node1),z(node1),                     &
                               x(node2),y(node2),z(node2),                     &
                               xn(n),yn(n),zn(n),ra(n),vol(node1),vol(node2),  &
                               qnode(1:ndim,node1),qnode(1:ndim,node2),        &
                               gradx(n_etot,node1),grady(n_etot,node1),        &
                               gradz(n_etot,node1),gradx(n_etot,node2),        &
                               grady(n_etot,node2),gradz(n_etot,node2),        &
                               ndim,  eqn_set, mu, gm1, viscous_terms, powerv, &
                               shft,gradcc,laplcc,power,0,c_gen)
        pr_limiter_coeff = pr_limiter_coeff_g
      end if

!     Combine the limiters

      if (iflim == 0) then
        phi1(1:ndim) = pcoef
        phi2(1:ndim) = pcoef
      else if (iflim <= 2) then
        phi1(1:ndim) = phi(1:ndim,node1)*pcoef
        phi2(1:ndim) = phi(1:ndim,node2)*pcoef
      else if ((iflim >= 13) .and. (iflim <= 17)) then
        phi1(1:ndim) = phi(1:ndim,node1)
        phi2(1:ndim) = phi(1:ndim,node2)
      else if ((iflim >= 3) .and. (iflim <= 6)) then
        phi1(1:ndim) = pcoef
        phi2(1:ndim) = pcoef
      end if

!     face speed

      face_speed = my_0
      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

      rx1 = xmean - x(node1)
      ry1 = ymean - y(node1)
      rz1 = zmean - z(node1)

      rx2 = xmean - x(node2)
      ry2 = ymean - y(node2)
      rz2 = zmean - z(node2)

!     eps1 = my_1
!     eps2 = my_1
!     if ((iflim ==  5) .or. (iflim ==  6) .or.                                &
!         (iflim == 15) .or. (iflim == 16) .or. (iflim == 17)) then
!       diam1 = (my_fact1*vol(node1)/pi)**my_exp
!       diam2 = (my_fact1*vol(node2)/pi)**my_exp
!       eps1  = (my_fact2*diam1)**3
!       eps2  = (my_fact2*diam2)**3
!     end if

      ql(1:ndim) = qnode(1:ndim,node1)
      qr(1:ndim) = qnode(1:ndim,node2)

!     Compute the reconstructed left and right state primitive variables
!     at the interface using grdients from the least squares method

!     ql = qf(rx1,ry1,rz1,gradx(1:5,node1),grady(1:5,node1),gradz(1:5,node1),  &
!             qnode(1:5,node1),qnode(1:5,node2),phi1(1:5),my_1,eps1,second)
!
!     qr = qf(rx2,ry2,rz2,gradx(1:5,node2),grady(1:5,node2),gradz(1:5,node2),  &
!             qnode(1:5,node2),qnode(1:5,node1),phi2(1:5),my_1,eps2,second)

      call hllcs_conserved_jac_w_ddt(rx1, ry1, rz1, rx2, ry2, rz2,             &
                                     xnorm, ynorm, znorm, area,                &
                                     vol(node1), vol(node2),                   &
                                     gradx(5,node1), grady(5,node1),           &
                                     gradz(5,node1),                           &
                                     phi1(5),                                  &
                                     gradx(5,node2), grady(5,node2),           &
                                     gradz(5,node2),                           &
                                     phi2(5),                                  &
                                     face_speed, mu,                           &
                                     ql(1:5), qr(1:5), second, hot,            &
                                     left_jacobian, rght_jacobian,             &
                                     dflux_dgradx1, dflux_dgrady1,             &
                                     dflux_dgradz1,                            &
                                     dflux_drho1, dflux_dpress1,               &
                                     dflux_dgradx2, dflux_dgrady2,             &
                                     dflux_dgradz2,                            &
                                     dflux_drho2, dflux_dpress2,               &
                                     dflux_dql_prim, dflux_dqr_prim,           &
                                     dflux_dq_cons_p, dflux_dq_cons_m)

      dfp = dflux_dq_cons_p
      dfm = dflux_dq_cons_m

      if(node1 <= nnodes0) then
        idiag = g2m(node1)
        do k = 1, 5
          do j = 1, 5
             a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfp(j,k)
          end do
        end do
      end if

      if(node2 <= nnodes0) then
        ioff  = fhelp(2,n)
        do k = 1, 5
          do j = 1, 5
             a_off(j,k,ioff)  = a_off(j,k,ioff) - dfp(j,k)
          end do
        end do
      end if

     if(node2 <= nnodes0) then
        idiag = g2m(node2)
        do k = 1, 5
          do j = 1, 5
             a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfm(j,k)
          end do
        end do
      end if

     if(node1 <= nnodes0) then
       ioff  = fhelp(1,n)
        do k = 1, 5
          do j = 1, 5
             a_off(j,k,ioff)  = a_off(j,k,ioff) + dfm(j,k)
          end do
        end do
      end if

    end do edge_loop

!  Convert back to conserved variables

    call ptoe(qdim,qnode,n_tot,0)

  end subroutine flux_jacobian_hllcs


!============================= FLUX_JACOBIAN_LDFSS ===========================80
!
! Low Dissipation Flux Split Scheme (LDFSS) Jacobian
!
! Qnode expected in conserved vars.
!
!=============================================================================80
  subroutine flux_jacobian_ldfss(nnodes0, nnodes01, nedgeloc, nedgeloc_2d,     &
                                 eptr, qnode, vol, x, y, z,                    &
                                 gradx, grady, gradz, phi,                     &
                                 xn, yn, zn, ra,                               &
                                 facespeed, ndim, eqn_set,                     &
                                 n_tot, n_grd, njac, g2m, fhelp,               &
                                 a_off, a_diag, max_nnz, ncell01)

    use fun3d_constants,         only : my_0, my_half, my_1, my_2, my_4
    use info_depr,               only : twod, cc_primal, xmach, re, tref, ntt, &
                                        ivisc, pr_limiter, pr_limiter_coeff
    use nml_nonlinear_solves,    only : itime
    use timeacc,                 only : pseudo_sub
    use fluid,                   only : gm1, gamma, sutherland_constant
    use inviscid_flux,           only : first_order_iterations, iflim
    use thermo,                  only : etop, ptoe
    use reconstruction,          only : pressure_limiter_coeff
    use flux_functions,          only : ldfss_conserved_jac_w_ddt
    use generic_gas_map,         only : n_etot, n_sonic_k, n_amu_k
    use solution_types,          only : generic_gas
    use nml_governing_equations, only : viscous_terms

    integer,                                 intent(in)    :: n_tot
    integer,                                 intent(in)    :: ndim
    integer,                                 intent(in)    :: eqn_set
    integer,                                 intent(in)    :: njac
    integer,                                 intent(in)    :: n_grd
    integer,                                 intent(in)    :: nnodes0
    integer,                                 intent(in)    :: nnodes01
    integer,                                 intent(in)    :: nedgeloc
    integer,                                 intent(in)    :: nedgeloc_2d
    integer,                                 intent(in)    :: max_nnz
    integer,                                 intent(in)    :: ncell01
    integer,   dimension(2,nedgeloc),        intent(in)    :: eptr
    integer,   dimension(2,nedgeloc),        intent(in)    :: fhelp
    integer,   dimension(:),                 intent(in)    :: g2m
    real(dp),  dimension(n_tot,nnodes01),    intent(inout) :: qnode
    real(dp),  dimension(nnodes01),          intent(in)    :: vol
    real(dp),  dimension(nnodes01),          intent(in)    :: x, y, z
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradx
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: grady
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: gradz
    real(dp),  dimension(n_grd,nnodes01),    intent(in)    :: phi
    real(dp),  dimension(nedgeloc),          intent(in)    :: xn, yn, zn, ra
    real(dp),  dimension(nedgeloc),          intent(in)    :: facespeed
    real(odp), dimension(njac,njac,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(njac,njac,nnodes0), intent(inout) :: a_diag

    integer  :: n, node1, node2
    integer  :: nedge_flux_eval, my_ntt
    integer  :: idiag, k, j, ioff
    integer  :: qdim

    logical  :: second
    real(dp) :: xmean, ymean, zmean
    real(dp) :: xnorm, ynorm, znorm
    real(dp) :: area

    real(dp) :: rx1, ry1, rz1, rx2, ry2, rz2
    real(dp) :: face_speed
    real(dp) :: pcoef, pr_limiter_coeff_g
    real(dp) :: power, shft, gradcc, laplcc
    real(dp) :: cstar, xmr
    real(dp) :: c_gen, mu1, mu2, mu

    real(dp), dimension(ndim+1)   :: ql, qr
    real(dp), dimension(ndim)     :: phi1, phi2
    real(dp), dimension(ndim)     :: dflux_dgradx1
    real(dp), dimension(ndim)     :: dflux_dgrady1
    real(dp), dimension(ndim)     :: dflux_dgradz1
    real(dp), dimension(ndim)     :: dflux_drho1, dflux_dpress1

    real(dp), dimension(ndim)     :: dflux_dgradx2
    real(dp), dimension(ndim)     :: dflux_dgrady2
    real(dp), dimension(ndim)     :: dflux_dgradz2
    real(dp), dimension(ndim)     :: dflux_drho2, dflux_dpress2

    real(dp), dimension(ndim,ndim)   :: dflux_dql_prim, dflux_dqr_prim
    real(dp), dimension(ndim,ndim)   :: dflux_dq_cons_p, dflux_dq_cons_m
    real(dp), dimension(ndim,ndim)   :: dfm, dfp

    real(dp), dimension(nedgeloc) :: fl_coeff

    integer,  parameter :: powerv = 4

    logical,  parameter :: hot = .false.
    logical,  parameter :: left_jacobian = .true.
    logical,  parameter :: rght_jacobian = .true.

    continue

    qdim = nnodes01
    if ( cc_primal ) qdim = ncell01

    if (itime /= 0) then
      my_ntt = pseudo_sub
    else
      my_ntt = ntt
    end if

    second = .false.
    if ( my_ntt > first_order_iterations ) second = .true.
!   second = ( my_ntt > first_order_iterations )

    nedge_flux_eval = nedgeloc
!   my_exp   = my_3rd
!   my_fact1 = my_6
!   my_fact2 = my_3
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
!     my_exp   = my_half
!     my_fact1 = my_4
!     my_fact2 = my_1
    end if

    gradcc = my_1
    if (iflim == 0) then
      power  = my_1
      laplcc = my_4
      shft  = my_0
    else if (iflim == 3) then
      power  = my_1
      laplcc = my_4
      shft  = my_0
    else if (iflim == 4) then
      power  = my_1
      laplcc = my_4
      shft  = my_0
    else if (iflim == 5) then
      power  = my_1
      laplcc = my_2
      shft  = my_0
    else if (iflim == 6) then
      power  = my_1
      laplcc = my_2
      shft  = my_0
    else
      power  = my_1
      laplcc = my_2
      shft  = my_0
    end if

    cstar  = sutherland_constant/tref

!   Convert to primitive variables

    call etop(qdim,qnode,n_tot,eqn_set)

!   Call routines that control the use of limiters during the higher order
!   reconstruction so as to increase robustness at hign Mach numbers and
!   reduce dissipation at low Mach numbers

    if (pr_limiter .and. second .and. (iflim > 2) .and. (iflim < 13)) then
      call pressure_limiter_coeff(n_tot, nnodes01, nnodes0, nedgeloc, eptr,    &
                                  qnode, fl_coeff, eqn_set)
    end if

!   Loop over all the edges and calculate the inviscid fluxes
!   (in the 2D case, this loop contains edges on only one y=constant plane)

!   rewind(600+lmpi_id)
    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      xmean = my_half*(x(node1) + x(node2))
      ymean = my_half*(y(node1) + y(node2))
      zmean = my_half*(z(node1) + z(node2))

      xnorm  = xn(n)
      ynorm  = yn(n)
      znorm  = zn(n)
      area   = ra(n)

!     Compute the normalized undivided pressure ratio and
!     pressure Laplacian based limiter

      pcoef = my_1
      if ( eqn_set == generic_gas ) then
        c_gen = my_half*(qnode(n_sonic_k(1),node1)+qnode(n_sonic_k(1),node2))
        if(ivisc >= 2)then
          mu1 = qnode(n_amu_k(1),node1)
          mu2 = qnode(n_amu_k(1),node2)
        else
          mu1 = my_0
          mu2 = my_0
        end if
      else
        c_gen = my_0
        if(ivisc >= 2)then
          xmr = xmach/re
          mu1 = viscosity_law(cstar,gamma*qnode(5,node1)/qnode(1,node1))*xmr
          mu2 = viscosity_law(cstar,gamma*qnode(5,node2)/qnode(1,node2))*xmr
        else
          mu1 = my_0
          mu2 = my_0
        end if
      end if
      mu = my_half*(mu1 + mu2)

      if (pr_limiter .and. second .and. (iflim > 2) .and. (iflim < 13)) then
        pr_limiter_coeff_g = pr_limiter_coeff
        pr_limiter_coeff = fl_coeff(n)
        pcoef=pressure_limiter(x(node1),y(node1),z(node1),                     &
                               x(node2),y(node2),z(node2),                     &
                               xn(n),yn(n),zn(n),ra(n),vol(node1),vol(node2),  &
                               qnode(1:ndim,node1),qnode(1:ndim,node2),        &
                               gradx(n_etot,node1),grady(n_etot,node1),        &
                               gradz(n_etot,node1),gradx(n_etot,node2),        &
                               grady(n_etot,node2),gradz(n_etot,node2),        &
                               ndim,  eqn_set, mu, gm1, viscous_terms, powerv, &
                               shft,gradcc,laplcc,power,0,c_gen)
        pr_limiter_coeff = pr_limiter_coeff_g
      end if

!     Combine the limiters

      if (iflim == 0) then
        phi1(1:ndim) = pcoef
        phi2(1:ndim) = pcoef
      else if (iflim <= 2) then
        phi1(1:ndim) = phi(1:ndim,node1)*pcoef
        phi2(1:ndim) = phi(1:ndim,node2)*pcoef
      else if ((iflim >= 13) .and. (iflim <= 17)) then
        phi1(1:ndim) = phi(1:ndim,node1)
        phi2(1:ndim) = phi(1:ndim,node2)
      else if ((iflim >= 3) .and. (iflim <= 6)) then
        phi1(1:ndim) = pcoef
        phi2(1:ndim) = pcoef
      end if

!     face speed

      face_speed = my_0
      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

      rx1 = xmean - x(node1)
      ry1 = ymean - y(node1)
      rz1 = zmean - z(node1)

      rx2 = xmean - x(node2)
      ry2 = ymean - y(node2)
      rz2 = zmean - z(node2)

!     eps1 = my_1
!     eps2 = my_1
!     if ((iflim ==  5) .or. (iflim ==  6) .or.                                &
!         (iflim == 15) .or. (iflim == 16) .or. (iflim == 17)) then
!       diam1 = (my_fact1*vol(node1)/pi)**my_exp
!       diam2 = (my_fact1*vol(node2)/pi)**my_exp
!       eps1  = (my_fact2*diam1)**3
!       eps2  = (my_fact2*diam2)**3
!     end if

      ql(1:ndim) = qnode(1:ndim,node1)
      qr(1:ndim) = qnode(1:ndim,node2)

!     Compute the reconstructed left and right state primitive variables
!     at the interface using gradients from the least squares method

!     ql = qf(rx1,ry1,rz1,gradx(1:5,node1),grady(1:5,node1),gradz(1:5,node1),  &
!             qnode(1:5,node1),qnode(1:5,node2),phi1(1:5),my_1,eps1,second)
!
!     qr = qf(rx2,ry2,rz2,gradx(1:5,node2),grady(1:5,node2),gradz(1:5,node2),  &
!             qnode(1:5,node2),qnode(1:5,node1),phi2(1:5),my_1,eps2,second)

      call ldfss_conserved_jac_w_ddt(rx1, ry1, rz1, rx2, ry2, rz2,             &
                                     xnorm, ynorm, znorm, area,                &
                                     vol(node1), vol(node2),                   &
                                     gradx(5,node1), grady(5,node1),           &
                                     gradz(5,node1),                           &
                                     phi1(5),                                  &
                                     gradx(5,node2), grady(5,node2),           &
                                     gradz(5,node2),                           &
                                     phi2(5),                                  &
                                     face_speed, mu,                           &
                                     ql(1:5), qr(1:5), second, hot,            &
                                     left_jacobian, rght_jacobian,             &
                                     dflux_dgradx1, dflux_dgrady1,             &
                                     dflux_dgradz1,                            &
                                     dflux_drho1, dflux_dpress1,               &
                                     dflux_dgradx2, dflux_dgrady2,             &
                                     dflux_dgradz2,                            &
                                     dflux_drho2, dflux_dpress2,               &
                                     dflux_dql_prim, dflux_dqr_prim,           &
                                     dflux_dq_cons_p, dflux_dq_cons_m)

      dfp = dflux_dq_cons_p
      dfm = dflux_dq_cons_m

      if(node1 <= nnodes0) then
        idiag = g2m(node1)
        do k = 1, 5
          do j = 1, 5
             a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfp(j,k)
          end do
        end do
      end if

      if(node2 <= nnodes0) then
        ioff  = fhelp(2,n)
        do k = 1, 5
          do j = 1, 5
             a_off(j,k,ioff)  = a_off(j,k,ioff) - dfp(j,k)
          end do
        end do
      end if

     if(node2 <= nnodes0) then
        idiag = g2m(node2)
        do k = 1, 5
          do j = 1, 5
             a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfm(j,k)
          end do
        end do
      end if

     if(node1 <= nnodes0) then
       ioff  = fhelp(1,n)
        do k = 1, 5
          do j = 1, 5
             a_off(j,k,ioff)  = a_off(j,k,ioff) + dfm(j,k)
          end do
        end do
      end if

    end do edge_loop

!  Convert back to conserved variables

    call ptoe(qdim,qnode,n_tot,0)

  end subroutine flux_jacobian_ldfss

!============================= FLUX_JACOBIAN_LDFSS_ANALYTIC ==================80
!
! Low Dissipation Flux Split Scheme (LDFSS) Jacobian - Analytic.
!
! Qnode expected in conserved vars.
!
!=============================================================================80
  subroutine flux_jacobian_ldfss_analytic(                                     &
                                 nnodes0, nnodes01, nedgeloc, nedgeloc_2d,     &
                                 eptr, qnode,                                  &
                                 xn, yn, zn, ra,                               &
                                 ndim, eqn_set,                                &
                                 n_tot, njac, g2m, fhelp,                      &
                                 a_off, a_diag, max_nnz, ncell01)

    use lmpi,           only : lmpi_conditional_stop
    use info_depr,      only : twod, cc_primal
    use thermo,         only : etop, ptoe
    use solution_types, only : generic_gas

    integer,                                 intent(in)    :: n_tot
    integer,                                 intent(in)    :: ndim
    integer,                                 intent(in)    :: eqn_set
    integer,                                 intent(in)    :: njac
    integer,                                 intent(in)    :: nnodes0
    integer,                                 intent(in)    :: nnodes01
    integer,                                 intent(in)    :: nedgeloc
    integer,                                 intent(in)    :: nedgeloc_2d
    integer,                                 intent(in)    :: max_nnz
    integer,                                 intent(in)    :: ncell01
    integer,   dimension(2,nedgeloc),        intent(in)    :: eptr
    integer,   dimension(2,nedgeloc),        intent(in)    :: fhelp
    integer,   dimension(:),                 intent(in)    :: g2m
    real(dp),  dimension(n_tot,nnodes01),    intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),          intent(in)    :: xn, yn, zn, ra
    real(odp), dimension(njac,njac,max_nnz), intent(inout) :: a_off
    real(dp),  dimension(njac,njac,nnodes0), intent(inout) :: a_diag

    integer  :: n, node1, node2
    integer  :: nedge_flux_eval
    integer  :: idiag, k, j, ioff
    integer  :: qdim

    real(dp) :: xnorm, ynorm, znorm
    real(dp) :: area

    real(dp), dimension(ndim+1)   :: ql, qr

    real(dp), dimension(ndim,ndim)   :: dfm, dfp

    integer, parameter  :: nq_call = 5, nm_call = 3

    real(dp), parameter :: wldfss_call = 0._dp

    real(dp), dimension(1,nm_call)         :: s_call
    real(dp), dimension(1,nq_call)         :: qm_call
    real(dp), dimension(1,nq_call)         :: qp_call
    real(dp), dimension(1,nq_call,nq_call) :: dfdqm_call
    real(dp), dimension(1,nq_call,nq_call) :: dfdqp_call

    continue

    if ( need_grid_velocity ) call lmpi_conditional_stop(1,                    &
    'Non-zero face_speed not programmed:flux_jacobian_ldfss_analytic')

    if (  eqn_set == generic_gas ) call lmpi_conditional_stop(1,               &
    'Generic gas not programmed:flux_jacobian_ldfss_analytic')

    qdim = nnodes01
    if ( cc_primal ) qdim = ncell01

    nedge_flux_eval = nedgeloc
    if (twod) then
      nedge_flux_eval = nedgeloc_2d
    end if

!   Convert to primitive variables

    call etop(qdim,qnode,n_tot,eqn_set)

!   Loop over all the edges and calculate the inviscid fluxes
!   (in the 2D case, this loop contains edges on only one y=constant plane)

    edge_loop: do n = 1, nedge_flux_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

      xnorm  = xn(n)
      ynorm  = yn(n)
      znorm  = zn(n)
      area   = ra(n)

      ql(1:ndim) = qnode(1:ndim,node1)
      qr(1:ndim) = qnode(1:ndim,node2)

      s_call(1,1) = xnorm*area
      s_call(1,2) = ynorm*area
      s_call(1,3) = znorm*area
      qm_call(1,1:nq_call) = ql(1:nq_call)
      qp_call(1,1:nq_call) = qr(1:nq_call)
      call dfldfsscp( nq_call,       nm_call,           1, &
                      qm_call,       qp_call,      s_call, &
                      dfdqm_call, dfdqp_call, wldfss_call)

      dfm(:,:) = dfdqp_call(1,:,:)
      dfp(:,:) = dfdqm_call(1,:,:)

      if(node1 <= nnodes0) then
        idiag = g2m(node1)
        do k = 1, 5
          do j = 1, 5
             a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfp(j,k)
          end do
        end do
      end if

      if(node2 <= nnodes0) then
        ioff  = fhelp(2,n)
        do k = 1, 5
          do j = 1, 5
             a_off(j,k,ioff)  = a_off(j,k,ioff) - dfp(j,k)
          end do
        end do
      end if

     if(node2 <= nnodes0) then
        idiag = g2m(node2)
        do k = 1, 5
          do j = 1, 5
             a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfm(j,k)
          end do
        end do
      end if

     if(node1 <= nnodes0) then
       ioff  = fhelp(1,n)
        do k = 1, 5
          do j = 1, 5
             a_off(j,k,ioff)  = a_off(j,k,ioff) + dfm(j,k)
          end do
        end do
      end if

    end do edge_loop

!  Convert back to conserved variables

    call ptoe(qdim,qnode,n_tot,0)

  end subroutine flux_jacobian_ldfss_analytic


!=============================== INVISCID_JACOBIAN_I =========================80
!
! Driver routine to compute inviscid jacobians - incompressible
!
!=============================================================================80

  subroutine inviscid_jacobian_i(grid, soln, crow)

    use grid_types,             only : grid_type
    use solution_types,         only : soln_type
    use comprow_types,          only : crow_flow
    use convection_defs,        only : incompressible_convection
    use convection_flux,        only : convection_lhs_nc
    use nml_code_run_control,   only : dfduc3_jacobians

    type(grid_type),  intent(in)    :: grid
    type(soln_type),  intent(inout) :: soln
    type(crow_flow),  intent(in)    :: crow

    continue

    if ( timing_jac ) call rtime('start:inviscid_jacobian:interior')

    if ( incompressible_convection ) then

      call convection_lhs_nc(                                                  &
                          grid%nnodes0,     grid%nedgeloc,                     &
                          grid%nedgeloc_2d, soln%max_nnz,                      &
                          grid%eptr,        soln%a_diag,                       &
                          soln%a_off,       grid%xn,       grid%yn,            &
                          grid%zn,          grid%ra,       crow%fhelp,         &
                          soln%njac, crow%g2m )

    else

      if (dfduc3_jacobians) then
!       old style jacobians that may be more robust in some situations
        call dfduc3(grid%nnodes0,     grid%nnodes01, grid%nedgeloc,            &
                    grid%nedgeloc_2d, soln%max_nnz,                            &
                    grid%eptr,        soln%q_dof,    soln%a_diag,              &
                    soln%a_off,       grid%xn,       grid%yn,                  &
                    grid%zn,          grid%ra,       crow%fhelp,               &
                    soln%n_tot,       soln%njac,     crow%g2m,                 &
                    grid%facespeed )

      else
        call roe_i_jacobians(                                                  &
                          grid%nnodes0,     grid%nnodes01, grid%nedgeloc,      &
                          grid%nedgeloc_2d, soln%max_nnz,                      &
                          grid%eptr,        soln%q_dof,    soln%a_diag,        &
                          soln%a_off,       grid%xn,       grid%yn,            &
                          grid%zn,          grid%ra,       crow%fhelp,         &
                          grid%facespeed,   soln%n_tot,    soln%njac,          &
                          crow%g2m )
      end if

    end if

    if ( timing_jac ) call rtime('  end:inviscid_jacobian_i:interior')

  end subroutine inviscid_jacobian_i


!=================================== ROE_I_JACOBIANS =========================80
!
! Roe flux jacobian (incompressible)
!
! All derivatives worked out in detail
!
! Newer version that handles moving grid and noninertial rotating reference
! frame cases as well as stationary grid/inertial frame cases.
!
! Reference: Neel, R. E., Godfrey, A. G., and McGrory, W. D.:"Low-Speed,
!            Time-Accurate Validation of GASP Version 4"; AIAA 2005-686
!            43rd AIAA Aerospace Sciences Meeting, Jan. 2005.
!
!=============================================================================80

  subroutine roe_i_jacobians(nnodes0, nnodes01, nedgeloc, nedgeloc_2d,         &
                            max_nnz, eptr, qnode, a_diag, a_off,               &
                            xn, yn, zn, ra, fhelp, facespeed, n_tot, njac, g2m)

    use info_depr,       only : twod, beta

    integer,                                     intent(in)    :: n_tot, njac
    integer,                                     intent(in)    :: nnodes0
    integer,                                     intent(in)    :: nnodes01
    integer,                                     intent(in)    :: max_nnz
    integer,                                     intent(in)    :: nedgeloc
    integer,                                     intent(in)    :: nedgeloc_2d

    integer,      dimension(2,nedgeloc),         intent(in)    :: eptr
    integer,      dimension(2,nedgeloc),         intent(in)    :: fhelp
    integer,      dimension(:),                  intent(in)    :: g2m

    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(njac,njac,nnodes0),     intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz),     intent(inout) :: a_off
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed

    integer                    :: n,node1,node2,nedge_jac_eval,idiag,j,k,ioff

    real(dp)                   :: xnorm,ynorm,znorm,area,face_speed

    real(dp), dimension(n_tot) :: ql, qr

    real(dp), dimension(4,4)    :: dfr,dfl

    real(dp), parameter    :: my_0   = 0.0_dp

    continue

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

! Loop over the edges and calculate the Jacobians
! (in the 2D case, this loop contains edges on only one y=constant plane)

    scanedges: do n = 1, nedge_jac_eval

      node1 = eptr(1,n)
      node2 = eptr(2,n)

! Calculate unit normal to face and length of face

      xnorm  = xn(n)
      ynorm  = yn(n)
      znorm  = zn(n)
      area   = ra(n)

! Face speed

      face_speed = my_0

      if (need_grid_velocity) then
        face_speed = facespeed(n)
      end if

! State vector at left and right nodes

      ql(:) = qnode(:,node1)
      qr(:) = qnode(:,node2)

! Get the left and right sides to dF/dQ

      call dfroe_i(xnorm, ynorm, znorm, area, face_speed, beta, ql, qr,        &
                   dfl, dfr)

! All that's left is to put things in the right place

        if (node1 <= nnodes0) then
          idiag = g2m(node1)
          do k = 1, 4
            do j = 1, 4
              a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfl(j,k)
            end do
          end do
        end if

        if (node2 <= nnodes0) then
          ioff  = fhelp(2,n)
          do k = 1, 4
            do j = 1, 4
              a_off(j,k,ioff)  = a_off(j,k,ioff) - dfl(j,k)
            end do
          end do
        end if

        if (node2 <= nnodes0) then
          idiag = g2m(node2)
          do k = 1, 4
            do j = 1, 4
              a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfr(j,k)
            end do
          end do
        end if

        if (node1 <= nnodes0) then
          ioff  = fhelp(1,n)
          do k = 1, 4
            do j = 1, 4
              a_off(j,k,ioff)  = a_off(j,k,ioff) + dfr(j,k)
            end do
          end do
        end if

    end do scanedges

  end subroutine roe_i_jacobians


!=================================== DFDUC3 ==================================80
!
!  Calculates the flux jacobians as a+|a|
!  Here |a| is averaged.
!  Also, there is a cutoff for eigenvalues but it is turned off
!  If you want to form dfp strictly from left and dfm strictly from right
!  then need to modify
!
!  This is an older jacobian routine for incompressible flow that may be more
!  robust in some situations; however, roe_i_jacobians is the default since
!  it is discretely consistent (for 1st order) with "flux_roe_i"
!
!=============================================================================80

  subroutine dfduc3(nnodes0,nnodes01,nedgeloc,nedgeloc_2d,max_nnz,            &
                    eptr,qnode,a_diag,a_off,xn,yn,zn,ra,fhelp,n_tot,njac,g2m, &
                    facespeed)

    use info_depr,  only : twod, beta
    use nml_global, only : moving_grid
    use nml_noninertial_reference_frame, only : noninertial

    integer,                                     intent(in)    :: n_tot,njac
    integer,                                     intent(in)    :: nnodes0
    integer,                                     intent(in)    :: nnodes01
    integer,                                     intent(in)    :: max_nnz
    integer,                                     intent(in)    :: nedgeloc
    integer,                                     intent(in)    :: nedgeloc_2d

    integer,      dimension(2,nedgeloc),         intent(in)    :: eptr
    integer,      dimension(2,nedgeloc),         intent(in)    :: fhelp
    integer,      dimension(:),                  intent(in)    :: g2m

    real(dp),  dimension(n_tot,nnodes01),        intent(in)    :: qnode
    real(dp),  dimension(njac,njac,nnodes0),     intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz),     intent(inout) :: a_off
    real(dp),  dimension(nedgeloc),              intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),              intent(in)    :: facespeed

    integer                                                    :: n,node1,node2
    integer                                                    :: idiag,k,j,ioff
    integer                                                    :: nedge_jac_eval

    real(dp)    :: area, xnorm, ynorm, znorm, face_speed

    real(dp), dimension(n_tot) :: ql, qr
    real(dp), dimension(4,4)   :: dfr,dfl

    real(dp), parameter    :: my_0   =  0._dp

    continue

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

! loop over the edges and calculate the jacobians
! (in the 2D case, this loop contains edges on only one y=constant plane)

      scanedges: do n = 1, nedge_jac_eval

        node1 = eptr(1,n)
        node2 = eptr(2,n)

! calculate unit normal to face and length of face

        xnorm = xn(n)
        ynorm = yn(n)
        znorm = zn(n)
        area  = ra(n)

! Face speed

      face_speed = my_0

      if (moving_grid .or. noninertial) then
        face_speed = facespeed(n)
      end if

! State vector at left and right nodes

      ql(:) = qnode(:,node1)
      qr(:) = qnode(:,node2)

! Get the left and right sides to dF/dQ

      call dfduc3_i(xnorm, ynorm, znorm, area, face_speed, beta, ql, qr,       &
                   dfl, dfr)

        if(node1 <= nnodes0) then
          idiag = g2m(node1)
          do k = 1, 4
            do j = 1, 4
              a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfl(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          ioff  = fhelp(2,n)
          do k = 1, 4
            do j = 1, 4
              a_off(j,k,ioff)  = a_off(j,k,ioff) - dfl(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          idiag = g2m(node2)
          do k = 1, 4
            do j = 1, 4
              a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfr(j,k)
            end do
          end do
        end if

        if(node1 <= nnodes0) then
          ioff  = fhelp(1,n)
          do k = 1, 4
            do j = 1, 4
              a_off(j,k,ioff)  = a_off(j,k,ioff) + dfr(j,k)
            end do
          end do
        end if

      end do scanedges

  end subroutine dfduc3


!============================== LOWMACH_PREC_ROE_JACOBIANS ===================80
!
! Roe flux jacobians with preconditioning
!
! All derivatives worked out in detail
!
! Note: Incoming and outgoing qnode variables are conservation variables
!
!=============================================================================80
  subroutine lowmach_prec_roe_jacobians(nnodes0, nnodes01, nedgeloc,           &
                           nedgeloc_2d,                                        &
                           max_nnz, eptr, qnode, a_diag, a_off,                &
                           xn, yn, zn,                                         &
                           ra, fhelp, facespeed, ncell01,                      &
                           conservative_linearization, n_tot, njac, g2m)

    use info_depr,     only : twod, cc_primal, prec_mach_star,                 &
                              prec_mach_trans1, prec_mach_trans2, prec_alt,    &
                              adptv_entropy_fix
    use inviscid_flux, only : rhs_a_eigenvalue_coef, rhs_u_eigenvalue_coef,    &
                              lhs_a_eigenvalue_coef, lhs_u_eigenvalue_coef
    use fluid,         only : gm1
    use thermo,        only : etop, ptoe

    integer,                                    intent(in) :: nnodes0,nnodes01
    integer,                                    intent(in) :: nedgeloc
    integer,                                    intent(in) :: nedgeloc_2d
    integer,                                    intent(in) :: max_nnz
    integer,                                    intent(in) :: ncell01,n_tot,njac

    integer,      dimension(2,nedgeloc),        intent(in) :: eptr
    integer,      dimension(2,nedgeloc),        intent(in) :: fhelp
    integer,      dimension(:),                 intent(in) :: g2m

    real(dp),  dimension(n_tot,nnodes01),       intent(inout) :: qnode
    real(dp),  dimension(nedgeloc),             intent(in)    :: xn,yn,zn,ra
    real(dp),  dimension(nedgeloc),             intent(in)    :: facespeed
    real(dp),  dimension(njac,njac,nnodes0),    intent(inout) :: a_diag
    real(odp), dimension(njac,njac,max_nnz), intent(inout) :: a_off

    logical, intent(in) :: conservative_linearization

    integer :: n,node1,node2,nedge_jac_eval,idiag,j,k,ioff,qdim

    real(dp)    :: xnorm,ynorm,znorm,face_speed

    real(dp)    :: q1q1l,q1q2l,q1q3l,q1q4l,q1q5l
    real(dp)    :: q2q1l,q2q2l,q2q3l,q2q4l,q2q5l
    real(dp)    :: q3q1l,q3q2l,q3q3l,q3q4l,q3q5l
    real(dp)    :: q4q1l,q4q2l,q4q3l,q4q4l,q4q5l
    real(dp)    :: q5q1l,q5q2l,q5q3l,q5q4l,q5q5l

    real(dp)    :: q1q1r,q1q2r,q1q3r,q1q4r,q1q5r
    real(dp)    :: q2q1r,q2q2r,q2q3r,q2q4r,q2q5r
    real(dp)    :: q3q1r,q3q2r,q3q3r,q3q4r,q3q5r
    real(dp)    :: q4q1r,q4q2r,q4q3r,q4q4r,q4q5r
    real(dp)    :: q5q1r,q5q2r,q5q3r,q5q4r,q5q5r

!   Unsplit flux derivatives [d(Fl)/d(ql) or d(Fr)/d(qr) ]

    real(dp)    :: fluxl1rl,fluxl1ul,fluxl1vl,fluxl1wl,fluxl1pl
    real(dp)    :: fluxl2rl,fluxl2ul,fluxl2vl,fluxl2wl,fluxl2pl
    real(dp)    :: fluxl3rl,fluxl3ul,fluxl3vl,fluxl3wl,fluxl3pl
    real(dp)    :: fluxl4rl,fluxl4ul,fluxl4vl,fluxl4wl,fluxl4pl
    real(dp)    :: fluxl5rl,fluxl5ul,fluxl5vl,fluxl5wl,fluxl5pl

    real(dp)    :: fluxr1rr,fluxr1ur,fluxr1vr,fluxr1wr,fluxr1pr
    real(dp)    :: fluxr2rr,fluxr2ur,fluxr2vr,fluxr2wr,fluxr2pr
    real(dp)    :: fluxr3rr,fluxr3ur,fluxr3vr,fluxr3wr,fluxr3pr
    real(dp)    :: fluxr4rr,fluxr4ur,fluxr4vr,fluxr4wr,fluxr4pr
    real(dp)    :: fluxr5rr,fluxr5ur,fluxr5vr,fluxr5wr,fluxr5pr

!   Total flux derivatives [d(F)/d(ql) or d(F)/d(qr) ]

    real(dp)    :: flux1rl,flux1ul,flux1vl,flux1wl,flux1pl
    real(dp)    :: flux2rl,flux2ul,flux2vl,flux2wl,flux2pl
    real(dp)    :: flux3rl,flux3ul,flux3vl,flux3wl,flux3pl
    real(dp)    :: flux4rl,flux4ul,flux4vl,flux4wl,flux4pl
    real(dp)    :: flux5rl,flux5ul,flux5vl,flux5wl,flux5pl

    real(dp)    :: flux1rr,flux1ur,flux1vr,flux1wr,flux1pr
    real(dp)    :: flux2rr,flux2ur,flux2vr,flux2wr,flux2pr
    real(dp)    :: flux3rr,flux3ur,flux3vr,flux3wr,flux3pr
    real(dp)    :: flux4rr,flux4ur,flux4vr,flux4wr,flux4pr
    real(dp)    :: flux5rr,flux5ur,flux5vr,flux5wr,flux5pr

!   |A| derivatives [d(|A|)/d(ql) or d(|A|)/d(qr) ]

    real(dp)    :: t1rl,t1ul,t1vl,t1wl,t1pl
    real(dp)    :: t2rl,t2ul,t2vl,t2wl,t2pl
    real(dp)    :: t3rl,t3ul,t3vl,t3wl,t3pl
    real(dp)    :: t4rl,t4ul,t4vl,t4wl,t4pl
    real(dp)    :: t5rl,t5ul,t5vl,t5wl,t5pl

    real(dp)    :: t1rr,t1ur,t1vr,t1wr,t1pr
    real(dp)    :: t2rr,t2ur,t2vr,t2wr,t2pr
    real(dp)    :: t3rr,t3ur,t3vr,t3wr,t3pr
    real(dp)    :: t4rr,t4ur,t4vr,t4wr,t4pr
    real(dp)    :: t5rr,t5ur,t5vr,t5wr,t5pr

    real(dp)    :: at1rl,at1ul,at1vl,at1wl,at1pl
    real(dp)    :: at1rr,at1ur,at1vr,at1wr,at1pr

!   Derivatives of jump quantities [ ()r - ()l ]

    real(dp)    :: dv1
    real(dp)    :: dv1rl,dv1ul,dv1vl,dv1wl,dv1pl
    real(dp)    :: dv1rr,dv1ur,dv1vr,dv1wr,dv1pr

    real(dp)    :: dv2
    real(dp)    :: dv2rl,dv2ul,dv2vl,dv2wl,dv2pl
    real(dp)    :: dv2rr,dv2ur,dv2vr,dv2wr,dv2pr

    real(dp)    :: dv3
    real(dp)    :: dv3rl,dv3ul,dv3vl,dv3wl,dv3pl
    real(dp)    :: dv3rr,dv3ur,dv3vr,dv3wr,dv3pr

    real(dp)    :: dv4
    real(dp)    :: dv4rl,dv4ul,dv4vl,dv4wl,dv4pl
    real(dp)    :: dv4rr,dv4ur,dv4vr,dv4wr,dv4pr

    real(dp)    :: dv5
    real(dp)    :: dv5rl,dv5ul,dv5vl,dv5wl,dv5pl
    real(dp)    :: dv5rr,dv5ur,dv5vr,dv5wr,dv5pr

    real(dp)    :: dv6
    real(dp)    :: dv6rl,dv6ul,dv6vl,dv6wl,dv6pl
    real(dp)    :: dv6rr,dv6ur,dv6vr,dv6wr,dv6pr

    real(dp)    :: dv7
    real(dp)    :: dv7rl,dv7ul,dv7vl,dv7wl,dv7pl
    real(dp)    :: dv7rr,dv7ur,dv7vr,dv7wr,dv7pr

    real(dp)    :: dv8
    real(dp)    :: dv8rl,dv8ul,dv8vl,dv8wl,dv8pl
    real(dp)    :: dv8rr,dv8ur,dv8vr,dv8wr,dv8pr

    real(dp)    :: c2
    real(dp)    :: c2rl,c2ul,c2vl,c2wl,c2pl
    real(dp)    :: c2rr,c2ur,c2vr,c2wr,c2pr

    real(dp)    :: beta
    real(dp)    :: betarl,betaul,betavl,betawl,betapl
    real(dp)    :: betarr,betaur,betavr,betawr,betapr

    real(dp)    :: psi
    real(dp)    :: psirl,psiul,psivl,psiwl,psipl
    real(dp)    :: psirr,psiur,psivr,psiwr,psipr

    real(dp)    :: sigma
    real(dp)    :: sigmarl,sigmaul,sigmavl,sigmawl,sigmapl
    real(dp)    :: sigmarr,sigmaur,sigmavr,sigmawr,sigmapr

    real(dp)    :: c_sigma
    real(dp)    :: c_sigmarl,c_sigmaul,c_sigmavl,c_sigmawl,c_sigmapl
    real(dp)    :: c_sigmarr,c_sigmaur,c_sigmavr,c_sigmawr,c_sigmapr

    real(dp)    :: g_m
    real(dp)    :: g_mrl,g_mul,g_mvl,g_mwl,g_mpl
    real(dp)    :: g_mrr,g_mur,g_mvr,g_mwr,g_mpr

    real(dp)    :: g_p
    real(dp)    :: g_prl,g_pul,g_pvl,g_pwl,g_ppl
    real(dp)    :: g_prr,g_pur,g_pvr,g_pwr,g_ppr

    real(dp)    :: f_m
    real(dp)    :: f_mrl,f_mul,f_mvl,f_mwl,f_mpl
    real(dp)    :: f_mrr,f_mur,f_mvr,f_mwr,f_mpr

    real(dp)    :: f_p
    real(dp)    :: f_prl,f_pul,f_pvl,f_pwl,f_ppl
    real(dp)    :: f_prr,f_pur,f_pvr,f_pwr,f_ppr

!   Derivatives of primitive variable jump quantities [ ()r - ()l ]

    real(dp)    :: du
    real(dp)    :: duul
    real(dp)    :: duur

    real(dp)    :: dv
    real(dp)    :: dvvl
    real(dp)    :: dvvr

    real(dp)    :: dw
    real(dp)    :: dwwl
    real(dp)    :: dwwr

    real(dp)    :: dubar
    real(dp)    :: dubarul,dubarvl,dubarwl
    real(dp)    :: dubarur,dubarvr,dubarwr

    real(dp)    :: dpress
    real(dp)    :: dpresspl
    real(dp)    :: dpresspr

    real(dp)    :: drho
    real(dp)    :: drhorl
    real(dp)    :: drhorr

    real(dp)    :: eig1
    real(dp)    :: eig1rl,eig1ul,eig1vl,eig1wl,eig1pl
    real(dp)    :: eig1rr,eig1ur,eig1vr,eig1wr,eig1pr

    real(dp)    :: eig2
    real(dp)    :: eig2rl,eig2ul,eig2vl,eig2wl,eig2pl
    real(dp)    :: eig2rr,eig2ur,eig2vr,eig2wr,eig2pr

    real(dp)    :: eig3
    real(dp)    :: eig3rl,eig3ul,eig3vl,eig3wl,eig3pl
    real(dp)    :: eig3rr,eig3ur,eig3vr,eig3wr,eig3pr

    real(dp)    :: maxeig
    real(dp)    :: maxeigrl,maxeigul,maxeigvl,maxeigwl,maxeigpl
    real(dp)    :: maxeigrr,maxeigur,maxeigvr,maxeigwr,maxeigpr

!   Derivatives of Roe-averaged quantities

    real(dp)    :: rho
    real(dp)    :: rhorl
    real(dp)    :: rhorr

    real(dp)    :: wat
    real(dp)    :: watrl
    real(dp)    :: watrr

    real(dp)    :: c
    real(dp)    :: crl,cul,cvl,cwl,cpl
    real(dp)    :: crr,cur,cvr,cwr,cpr

    real(dp)    :: h
    real(dp)    :: hrl,hul,hvl,hwl,hpl
    real(dp)    :: hrr,hur,hvr,hwr,hpr

    real(dp)    :: u
    real(dp)    :: url,uul,uvl,uwl,upl
    real(dp)    :: urr,uur,uvr,uwr,upr

    real(dp)    :: v
    real(dp)    :: vrl,vul,vvl,vwl,vpl
    real(dp)    :: vrr,vur,vvr,vwr,vpr

    real(dp)    :: w
    real(dp)    :: wrl,wul,wvl,wwl,wpl
    real(dp)    :: wrr,wur,wvr,wwr,wpr

    real(dp)    :: ubar
    real(dp)    :: ubarrl,ubarul,ubarvl,ubarwl,ubarpl
    real(dp)    :: ubarrr,ubarur,ubarvr,ubarwr,ubarpr

    real(dp)    :: q2
    real(dp)    :: q2rl,q2ul,q2vl,q2wl,q2pl
    real(dp)    :: q2rr,q2ur,q2vr,q2wr,q2pr

!   Elemental derivatives

    real(dp)    :: ubarrur,ubarrvr,ubarrwr

    real(dp)    :: hr
    real(dp)    :: hrrr,hrur,hrvr,hrwr,hrpr

    real(dp)    :: energyr
    real(dp)    :: energyrrr,energyrur,energyrvr,energyrwr,energyrpr

    real(dp)    :: pressr
    real(dp)    :: pressrpr

    real(dp)    :: q2r
    real(dp)    :: q2rur,q2rvr,q2rwr

    real(dp)    :: ur
    real(dp)    :: urur

    real(dp)    :: vr
    real(dp)    :: vrvr

    real(dp)    :: wr
    real(dp)    :: wrwr

    real(dp)    :: rhor
    real(dp)    :: rhorrr

    real(dp)    :: ubarlul,ubarlvl,ubarlwl

    real(dp)    :: hl
    real(dp)    :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp)    :: energyl
    real(dp)    :: energylrl,energylul,energylvl,energylwl,energylpl

    real(dp)    :: pressl
    real(dp)    :: presslpl

    real(dp)    :: q2l
    real(dp)    :: q2lul,q2lvl,q2lwl

    real(dp)    :: ul
    real(dp)    :: ulul

    real(dp)    :: vl
    real(dp)    :: vlvl

    real(dp)    :: wl
    real(dp)    :: wlwl

    real(dp)    :: rhol
    real(dp)    :: rholrl

    real(dp), dimension(5,5)    :: dfm,dfp

    real(dp)    :: at1, ta, half_area
    real(dp)    :: ubar_fs, ubar_fsl, ubar_fsr
    real(dp)    :: eigeps1, eigeps2, eigeps3
    real(dp)    :: abseig1, abseig2, abseig3
    real(dp)    :: t1, d1, d2, d3
    real(dp)    :: c2g, q2h, twice_c, c4, e1, e2, e3, e4, e5, e6
    real(dp)    :: c_du, c_dv, c_dw, c_dubar, c_drho, c_dpress

    real(dp)    :: mach, beta_star, mach_rel, dbetadm

    real(dp)    :: fa, faeps, absfa, dm, dterm
    real(dp)    :: utngl, utngr, utang, scoef, aecoef, cecoef

    real(dp), parameter    :: my_0    = 0.0_dp
    real(dp), parameter    :: my_4th  = 0.25_dp
    real(dp), parameter    :: my_half = 0.5_dp
    real(dp), parameter    :: my_1    = 1.0_dp

    continue

    ta = my_0
    if(prec_alt == 1) ta = my_1

! Set dimension for etop, ptoe

    qdim = nnodes01
    if ( cc_primal ) qdim = ncell01

    if (twod) then
      nedge_jac_eval = nedgeloc_2d
    else
      nedge_jac_eval = nedgeloc
    end if

!  Convert to primitive variables

    call etop(qdim,qnode,n_tot,0) ! Note eqn_set hardwired to 0 here

! Loop over the edges and calculate the Jacobians

    scanedges: do n = 1, nedge_jac_eval

       node1 = eptr(1,n)
       node2 = eptr(2,n)

! Get unit normals - defer area

       xnorm = xn(n)
       ynorm = yn(n)
       znorm = zn(n)

! Face speed

       face_speed = my_0

       if (need_grid_velocity) then
         face_speed = facespeed(n)
       end if

! Get variables on "left" side of face
! Delete obvious zero contributions

         rhol   = qnode(1,node1)
         rholrl = my_1

         ul     = qnode(2,node1)
         ulul   = my_1

         vl     = qnode(3,node1)
         vlvl   = my_1

         wl     = qnode(4,node1)
         wlwl   = my_1

         q2l    = ul*ul + vl*vl + wl*wl
         q2lul  = 2.0_dp*( ul*ulul )
         q2lvl  = 2.0_dp*( vl*vlvl )
         q2lwl  = 2.0_dp*( wl*wlwl )

         pressl   = qnode(5,node1)
         presslpl = my_1

         energyl = pressl/gm1 + my_half*rhol*q2l

         energylrl =                                 my_half*q2l*rholrl
         energylul =              my_half*rhol*q2lul
         energylvl =              my_half*rhol*q2lvl
         energylwl =              my_half*rhol*q2lwl
         energylpl = presslpl/gm1

         hl     = (energyl + pressl)/rhol

         hlrl = ((energylrl) - hl*rholrl)/rhol
         hlul = ((energylul)            )/rhol
         hlvl = ((energylvl)            )/rhol
         hlwl = ((energylwl)            )/rhol
         hlpl = ((energylpl+presslpl)   )/rhol

!        ubarl  = xnorm*ul + ynorm*vl + znorm*wl

         ubarlul = xnorm*ulul
         ubarlvl = ynorm*vlvl
         ubarlwl = znorm*wlwl

! Get variables on "right" side of face

         rhor   = qnode(1,node2)
         rhorrr = my_1

         ur     = qnode(2,node2)
         urur   = my_1

         vr     = qnode(3,node2)
         vrvr   = my_1

         wr     = qnode(4,node2)
         wrwr   = my_1

         q2r    = ur*ur + vr*vr + wr*wr
         q2rur  = 2.0_dp*(ur*urur)
         q2rvr  = 2.0_dp*(vr*vrvr)
         q2rwr  = 2.0_dp*(wr*wrwr)

         pressr   = qnode(5,node2)
         pressrpr = my_1

         energyr = pressr/gm1 + my_half*rhor*q2r

         energyrrr =                                 my_half*q2r*rhorrr
         energyrur =              my_half*rhor*q2rur
         energyrvr =              my_half*rhor*q2rvr
         energyrwr =              my_half*rhor*q2rwr
         energyrpr = pressrpr/gm1

         hr     = (energyr + pressr)/rhor

         hrrr = ((energyrrr) - hr*rhorrr)/rhor
         hrur = ((energyrur)            )/rhor
         hrvr = ((energyrvr)            )/rhor
         hrwr = ((energyrwr)            )/rhor
         hrpr = ((energyrpr+pressrpr)   )/rhor

!        ubarr  = xnorm*ur + ynorm*vr + znorm*wr

         ubarrur = xnorm*urur
         ubarrvr = ynorm*vrvr
         ubarrwr = znorm*wrwr

! Compute Roe averages

         rho = sqrt(rhol*rhor)

         rhorl = my_half*(rhor*rholrl)/rho
         rhorr = my_half*(rhol*rhorrr)/rho

         wat = rho/(rho + rhor)

         watrl = (rhorl - wat*(rhorl         ))/(rho + rhor)
         watrr = (rhorr - wat*(rhorr + rhorrr))/(rho + rhor)

         u   = ul*wat + ur*(my_1 - wat)

         url = ul*watrl + ur*(-watrl)
         uul = wat*ulul
         uvl = my_0
         uwl = my_0
         upl = my_0

         urr = ul*watrr + ur*(-watrr)
         uur = (my_1 - wat)*urur
         uvr = my_0
         uwr = my_0
         upr = my_0

         v   = vl*wat + vr*(my_1 - wat)

         vrl = vl*watrl + vr*(-watrl)
         vul = my_0
         vvl = wat*vlvl
         vwl = my_0
         vpl = my_0

         vrr = vl*watrr + vr*(-watrr)
         vur = my_0
         vvr = (my_1 - wat)*vrvr
         vwr = my_0
         vpr = my_0

         w   = wl*wat + wr*(my_1 - wat)

         wrl = wl*watrl + wr*(-watrl)
         wul = my_0
         wvl = my_0
         wwl = wat*wlwl
         wpl = my_0

         wrr = wl*watrr + wr*(-watrr)
         wur = my_0
         wvr = my_0
         wwr = (my_1 - wat)*wrwr
         wpr = my_0

         h   = hl*wat + hr*(my_1 - wat)

         hrl = hl*watrl + wat*hlrl + hr*(-watrl)
         hul =          + wat*hlul
         hvl =          + wat*hlvl
         hwl =          + wat*hlwl
         hpl =          + wat*hlpl

         hrr = hl*watrr + hr*(-watrr) + (my_1 - wat)*hrrr
         hur =                        + (my_1 - wat)*hrur
         hvr =                        + (my_1 - wat)*hrvr
         hwr =                        + (my_1 - wat)*hrwr
         hpr =                        + (my_1 - wat)*hrpr

         q2  = u*u + v*v + w*w

         q2rl = 2.0_dp*(u*url + v*vrl + w*wrl)
         q2ul = 2.0_dp*(u*uul + v*vul + w*wul)
         q2vl = 2.0_dp*(u*uvl + v*vvl + w*wvl)
         q2wl = 2.0_dp*(u*uwl + v*vwl + w*wwl)
         q2pl = 2.0_dp*(u*upl + v*vpl + w*wpl)

         q2rr = 2.0_dp*(u*urr + v*vrr + w*wrr)
         q2ur = 2.0_dp*(u*uur + v*vur + w*wur)
         q2vr = 2.0_dp*(u*uvr + v*vvr + w*wvr)
         q2wr = 2.0_dp*(u*uwr + v*vwr + w*wwr)
         q2pr = 2.0_dp*(u*upr + v*vpr + w*wpr)

         c   = sqrt(gm1*(h - my_half*q2))

         crl = my_half*gm1*(hrl - my_half*q2rl)/c
         cul = my_half*gm1*(hul - my_half*q2ul)/c
         cvl = my_half*gm1*(hvl - my_half*q2vl)/c
         cwl = my_half*gm1*(hwl - my_half*q2wl)/c
         cpl = my_half*gm1*(hpl - my_half*q2pl)/c

         crr = my_half*gm1*(hrr - my_half*q2rr)/c
         cur = my_half*gm1*(hur - my_half*q2ur)/c
         cvr = my_half*gm1*(hvr - my_half*q2vr)/c
         cwr = my_half*gm1*(hwr - my_half*q2wr)/c
         cpr = my_half*gm1*(hpr - my_half*q2pr)/c

         c2 = c*c
         c4 = c2*c2

         twice_c = c + c

         c2rl = twice_c*crl
         c2ul = twice_c*cul
         c2vl = twice_c*cvl
         c2wl = twice_c*cwl
         c2pl = twice_c*cpl

         c2rr = twice_c*crr
         c2ur = twice_c*cur
         c2vr = twice_c*cvr
         c2wr = twice_c*cwr
         c2pr = twice_c*cpr

         ubar    = xnorm*u + ynorm*v + znorm*w

!     Compute normal velocity - face speed used in eigenvalue
!     and unsplit flux contributions

         ubar_fs  = ubar - face_speed
         ubar_fsl = xnorm*ul + ynorm*vl + znorm*wl - face_speed
         ubar_fsr = xnorm*ur + ynorm*vr + znorm*wr - face_speed

         ubarrl = xnorm*url + ynorm*vrl + znorm*wrl
         ubarul = xnorm*uul + ynorm*vul + znorm*wul
         ubarvl = xnorm*uvl + ynorm*vvl + znorm*wvl
         ubarwl = xnorm*uwl + ynorm*vwl + znorm*wwl
         ubarpl = xnorm*upl + ynorm*vpl + znorm*wpl

         ubarrr = xnorm*urr + ynorm*vrr + znorm*wrr
         ubarur = xnorm*uur + ynorm*vur + znorm*wur
         ubarvr = xnorm*uvr + ynorm*vvr + znorm*wvr
         ubarwr = xnorm*uwr + ynorm*vwr + znorm*wwr
         ubarpr = xnorm*upr + ynorm*vpr + znorm*wpr

!        Compute beta based on local Mach number
!        beta = 1        M >= M_t2
!             = (M*)^2   M <= M_t1
!             = F(M)     otherwise [ i.e., M_t1 < M < M_t2 ]

         beta_star = prec_mach_star**2
         mach      = sqrt( q2/c2 )

!        Initialize beta derivatives to zero

         betarl = my_0
         betaul = my_0
         betavl = my_0
         betawl = my_0
         betapl = my_0

         betarr = my_0
         betaur = my_0
         betavr = my_0
         betawr = my_0
         betapr = my_0

         if(mach >= prec_mach_trans2) then
           beta = my_1
         elseif(mach <= prec_mach_trans1) then
           beta = beta_star
         else
           mach_rel = (mach-prec_mach_trans1)                                  &
                    / (prec_mach_trans2-prec_mach_trans1)
           beta     = beta_star  + (beta_star-my_1)*(2.0_dp*mach_rel**2)       &
                                                   *( -1.5_dp + mach_rel)

           dbetadm  = 6.0_dp*(beta_star-my_1)*mach_rel*( -my_1 + mach_rel)     &
                    / (prec_mach_trans2-prec_mach_trans1)
           e1 = -mach*dbetadm/c
           e2 = my_half*dbetadm/(mach*c2)

           betarl = e1*crl + e2*q2rl
           betaul = e1*cul + e2*q2ul
           betavl = e1*cvl + e2*q2vl
           betawl = e1*cwl + e2*q2wl
           betapl = e1*cpl + e2*q2pl

           betarr = e1*crr + e2*q2rr
           betaur = e1*cur + e2*q2ur
           betavr = e1*cvr + e2*q2vr
           betawr = e1*cwr + e2*q2wr
           betapr = e1*cpr + e2*q2pr
         endif

!     Compute preconditioning parameters

         psi   = ubar_fs*( my_1 - beta )*my_half

         e1 = ( my_1 - beta )*my_half
         e2 = -ubar_fs*my_half

         psirl = e1*ubarrl + e2*betarl
         psiul = e1*ubarul + e2*betaul
         psivl = e1*ubarvl + e2*betavl
         psiwl = e1*ubarwl + e2*betawl
         psipl = e1*ubarpl + e2*betapl

         psirr = e1*ubarrr + e2*betarr
         psiur = e1*ubarur + e2*betaur
         psivr = e1*ubarvr + e2*betavr
         psiwr = e1*ubarwr + e2*betawr
         psipr = e1*ubarpr + e2*betapr

         sigma = sqrt( psi**2 + beta*c2 )

         e1 = psi/sigma
         e2 = my_half*c2/sigma
         e3 = my_half*beta/sigma

         sigmarl = e1*psirl + e2*betarl + e3*c2rl
         sigmaul = e1*psiul + e2*betaul + e3*c2ul
         sigmavl = e1*psivl + e2*betavl + e3*c2vl
         sigmawl = e1*psiwl + e2*betawl + e3*c2wl
         sigmapl = e1*psipl + e2*betapl + e3*c2pl

         sigmarr = e1*psirr + e2*betarr + e3*c2rr
         sigmaur = e1*psiur + e2*betaur + e3*c2ur
         sigmavr = e1*psivr + e2*betavr + e3*c2vr
         sigmawr = e1*psiwr + e2*betawr + e3*c2wr
         sigmapr = e1*psipr + e2*betapr + e3*c2pr

         c_sigma = c/sigma

         e1 = -c_sigma/sigma
         e2 = my_1/sigma

         c_sigmarl = e1*sigmarl + e2*crl
         c_sigmaul = e1*sigmaul + e2*cul
         c_sigmavl = e1*sigmavl + e2*cvl
         c_sigmawl = e1*sigmawl + e2*cwl
         c_sigmapl = e1*sigmapl + e2*cpl

         c_sigmarr = e1*sigmarr + e2*crr
         c_sigmaur = e1*sigmaur + e2*cur
         c_sigmavr = e1*sigmavr + e2*cvr
         c_sigmawr = e1*sigmawr + e2*cwr
         c_sigmapr = e1*sigmapr + e2*cpr

         g_p   = -(psi-sigma)

         g_prl = -psirl + sigmarl
         g_pul = -psiul + sigmaul
         g_pvl = -psivl + sigmavl
         g_pwl = -psiwl + sigmawl
         g_ppl = -psipl + sigmapl

         g_prr = -psirr + sigmarr
         g_pur = -psiur + sigmaur
         g_pvr = -psivr + sigmavr
         g_pwr = -psiwr + sigmawr
         g_ppr = -psipr + sigmapr

         g_m   = +(psi+sigma)

         g_mrl = psirl + sigmarl
         g_mul = psiul + sigmaul
         g_mvl = psivl + sigmavl
         g_mwl = psiwl + sigmawl
         g_mpl = psipl + sigmapl

         g_mrr = psirr + sigmarr
         g_mur = psiur + sigmaur
         g_mvr = psivr + sigmavr
         g_mwr = psiwr + sigmawr
         g_mpr = psipr + sigmapr

         f_p     = g_p/(beta*c)

         e1 = my_1/(beta*c)
         e2 = -f_p/c
         e3 = -f_p/beta

         f_prl = e1*g_prl + e2*crl + e3*betarl
         f_pul = e1*g_pul + e2*cul + e3*betaul
         f_pvl = e1*g_pvl + e2*cvl + e3*betavl
         f_pwl = e1*g_pwl + e2*cwl + e3*betawl
         f_ppl = e1*g_ppl + e2*cpl + e3*betapl

         f_prr = e1*g_prr + e2*crr + e3*betarr
         f_pur = e1*g_pur + e2*cur + e3*betaur
         f_pvr = e1*g_pvr + e2*cvr + e3*betavr
         f_pwr = e1*g_pwr + e2*cwr + e3*betawr
         f_ppr = e1*g_ppr + e2*cpr + e3*betapr

         f_m     = g_m/(beta*c)

         e1 = my_1/(beta*c)
         e2 = -f_m/c
         e3 = -f_m/beta

         f_mrl = e1*g_mrl + e2*crl + e3*betarl
         f_mul = e1*g_mul + e2*cul + e3*betaul
         f_mvl = e1*g_mvl + e2*cvl + e3*betavl
         f_mwl = e1*g_mwl + e2*cwl + e3*betawl
         f_mpl = e1*g_mpl + e2*cpl + e3*betapl

         f_mrr = e1*g_mrr + e2*crr + e3*betarr
         f_mur = e1*g_mur + e2*cur + e3*betaur
         f_mvr = e1*g_mvr + e2*cvr + e3*betavr
         f_mwr = e1*g_mwr + e2*cwr + e3*betawr
         f_mpr = e1*g_mpr + e2*cpr + e3*betapr

!     Now compute eigenvalues, eigenvectors, and strengths
!     including preconditioning parameters

         eig1 = ubar_fs - psi + sigma
         eig2 = ubar_fs - psi - sigma
         eig3 = ubar_fs

!     Eigenvalue limiting.  In terms of dimensional equations:
!     -limit eigenvalues as fraction of local maximum

         if (adptv_entropy_fix) then

           if (ubar_fs-psi > my_0) then
             dm = my_1
           else
             dm =-my_1
           endif

           dterm = my_1

           scoef   = my_0
!          scoef   = flux_efixc(n)

!          maximum eigenvalue

           q2l  = ul*ul + vl*vl + wl*wl
           q2r  = ur*ur + vr*vr + wr*wr
           utngl  = sqrt(max(my_0, q2l-ubar_fsl*ubar_fsl))
           utngr  = sqrt(max(my_0, q2r-ubar_fsr*ubar_fsr))
           utang  = utngl*wat + utngr*(my_1 - wat)
           maxeig = max(abs( ubar_fs-psi ), abs( utang )) + sigma

!          acoustic eigenvalue limiter coefficient

!          aecoef  = min(my_4th, lhs_a_eigenvalue_coef*(my_1-scoef))
           aecoef  = 0.90_dp*min(my_4th, rhs_a_eigenvalue_coef*(my_1-scoef))
           aecoef  = max(0.009_dp, min(my_1, aecoef))

!          convective eigenvalue limiter coefficient

           cecoef  = min(my_half, lhs_u_eigenvalue_coef*(my_1-scoef))
           cecoef  = 1.10_dp*min(my_half, rhs_u_eigenvalue_coef*(my_1-scoef))
           cecoef  = max(0.000011_dp, min(my_1, cecoef))

         else

           fa    = ubar_fs-psi
           faeps = 0.05_dp*sigma
           absfa = abs( fa )
           if (absfa < faeps) absfa = my_half*(fa**2/faeps + faeps)

!          maximum eigenvalue

           maxeig = absfa + sigma

           if (abs(fa) < faeps) then
             dm = fa/faeps
           else if (fa > my_0) then
             dm = my_1
           else
             dm =-my_1
           endif

           dterm = my_1 + my_half*(my_1 - dm**2)*0.05_dp

!          acoustic eigenvalue limiter coefficient

           aecoef  = lhs_a_eigenvalue_coef

!          convective eigenvalue limite coefficient

           cecoef  = lhs_u_eigenvalue_coef

         end if

!     acoustic eigenvalue limiters

         eigeps1 = aecoef*maxeig
         eigeps2 = aecoef*maxeig

!     convective eigenvalue limiter

         eigeps3 = cecoef*maxeig

         maxeigrl = dm*(ubarrl - psirl ) + sigmarl*dterm
         maxeigul = dm*(ubarul - psiul ) + sigmaul*dterm
         maxeigvl = dm*(ubarvl - psivl ) + sigmavl*dterm
         maxeigwl = dm*(ubarwl - psiwl ) + sigmawl*dterm
         maxeigpl = dm*(ubarpl - psipl ) + sigmapl*dterm

         maxeigrr = dm*(ubarrr - psirr ) + sigmarr*dterm
         maxeigur = dm*(ubarur - psiur ) + sigmaur*dterm
         maxeigvr = dm*(ubarvr - psivr ) + sigmavr*dterm
         maxeigwr = dm*(ubarwr - psiwr ) + sigmawr*dterm
         maxeigpr = dm*(ubarpr - psipr ) + sigmapr*dterm

         abseig1 = abs( eig1 )
         abseig2 = abs( eig2 )
         abseig3 = abs( eig3 )

         if(abseig1 < eigeps1) abseig1 = my_half*(eig1**2/eigeps1 + eigeps1)
         if(abseig2 < eigeps2) abseig2 = my_half*(eig2**2/eigeps2 + eigeps2)
         if(abseig3 < eigeps3) abseig3 = my_half*(eig3**2/eigeps3 + eigeps3)

         if(abs(eig1) < eigeps1 ) then
           d1 = eig1/eigeps1
         elseif(eig1 > my_0) then
           d1 = my_1
         else
           d1 =-my_1
         endif

         dterm = my_half*(my_1 - d1**2)*aecoef
         eig1rl = d1*(ubarrl - psirl + sigmarl) + dterm*maxeigrl
         eig1ul = d1*(ubarul - psiul + sigmaul) + dterm*maxeigul
         eig1vl = d1*(ubarvl - psivl + sigmavl) + dterm*maxeigvl
         eig1wl = d1*(ubarwl - psiwl + sigmawl) + dterm*maxeigwl
         eig1pl = d1*(ubarpl - psipl + sigmapl) + dterm*maxeigpl

         eig1rr = d1*(ubarrr - psirr + sigmarr) + dterm*maxeigrr
         eig1ur = d1*(ubarur - psiur + sigmaur) + dterm*maxeigur
         eig1vr = d1*(ubarvr - psivr + sigmavr) + dterm*maxeigvr
         eig1wr = d1*(ubarwr - psiwr + sigmawr) + dterm*maxeigwr
         eig1pr = d1*(ubarpr - psipr + sigmapr) + dterm*maxeigpr

         if(abs(eig2) < eigeps2 ) then
           d2 = eig2/eigeps2
         elseif(eig2 > my_0) then
           d2 = my_1
         else
           d2 =-my_1
         endif

         dterm = my_half*(my_1 - d2**2)*aecoef
         eig2rl = d2*(ubarrl - psirl - sigmarl) + dterm*maxeigrl
         eig2ul = d2*(ubarul - psiul - sigmaul) + dterm*maxeigul
         eig2vl = d2*(ubarvl - psivl - sigmavl) + dterm*maxeigvl
         eig2wl = d2*(ubarwl - psiwl - sigmawl) + dterm*maxeigwl
         eig2pl = d2*(ubarpl - psipl - sigmapl) + dterm*maxeigpl

         eig2rr = d2*(ubarrr - psirr - sigmarr) + dterm*maxeigrr
         eig2ur = d2*(ubarur - psiur - sigmaur) + dterm*maxeigur
         eig2vr = d2*(ubarvr - psivr - sigmavr) + dterm*maxeigvr
         eig2wr = d2*(ubarwr - psiwr - sigmawr) + dterm*maxeigwr
         eig2pr = d2*(ubarpr - psipr - sigmapr) + dterm*maxeigpr

         if(abs(eig3) < eigeps3 ) then
           d3 = eig3/eigeps3
         elseif(eig3 > my_0) then
           d3 = my_1
         else
           d3 =-my_1
         endif

         dterm = my_half*(my_1 - d3**2)*cecoef
         eig3rl = d3*ubarrl + dterm*maxeigrl
         eig3ul = d3*ubarul + dterm*maxeigul
         eig3vl = d3*ubarvl + dterm*maxeigvl
         eig3wl = d3*ubarwl + dterm*maxeigwl
         eig3pl = d3*ubarpl + dterm*maxeigpl

         eig3rr = d3*ubarrr + dterm*maxeigrr
         eig3ur = d3*ubarur + dterm*maxeigur
         eig3vr = d3*ubarvr + dterm*maxeigvr
         eig3wr = d3*ubarwr + dterm*maxeigwr
         eig3pr = d3*ubarpr + dterm*maxeigpr

         drho   = rhor - rhol

         drhorl = - rholrl
         drhorr =   rhorrr

         dpress   = pressr - pressl

         dpresspl = - presslpl
         dpresspr =   pressrpr

         du   = ur - ul

         duul = - ulul
         duur =   urur

         dv   = vr - vl

         dvvl = - vlvl
         dvvr =   vrvr

         dw   = wr - wl

         dwwl = - wlwl
         dwwr =   wrwr

         dubar  = xnorm*du + ynorm*dv + znorm*dw

         dubarul = xnorm*duul
         dubarvl = ynorm*dvvl
         dubarwl = znorm*dwwl

         dubarur = xnorm*duur
         dubarvr = ynorm*dvvr
         dubarwr = znorm*dwwr

         dv1 = abseig1*my_half*(f_p*dpress + rho*c*dubar)/c2

         c_dpress = abseig1*my_half*(f_p  )/c2
         c_dubar  = abseig1*my_half*(rho*c)/c2

         e1 = my_half*(f_p*dpress + rho*c*dubar)/c2
         e2 = abseig1*my_half*(dpress)/c2
         e3 = -dv1/c2
         e4 = abseig1*my_half*(  c*dubar)/c2
         e5 = abseig1*my_half*(rho*dubar)/c2

         dv1rl =                   + e1*eig1rl + e2*f_prl + e3*c2rl + e5*crl
         dv1ul = c_dubar*dubarul   + e1*eig1ul + e2*f_pul + e3*c2ul + e5*cul
         dv1vl = c_dubar*dubarvl   + e1*eig1vl + e2*f_pvl + e3*c2vl + e5*cvl
         dv1wl = c_dubar*dubarwl   + e1*eig1wl + e2*f_pwl + e3*c2wl + e5*cwl
         dv1pl = c_dpress*dpresspl + e1*eig1pl + e2*f_ppl + e3*c2pl + e5*cpl

         dv1rr =                   + e1*eig1rr + e2*f_prr + e3*c2rr + e5*crr
         dv1ur = c_dubar*dubarur   + e1*eig1ur + e2*f_pur + e3*c2ur + e5*cur
         dv1vr = c_dubar*dubarvr   + e1*eig1vr + e2*f_pvr + e3*c2vr + e5*cvr
         dv1wr = c_dubar*dubarwr   + e1*eig1wr + e2*f_pwr + e3*c2wr + e5*cwr
         dv1pr = c_dpress*dpresspr + e1*eig1pr + e2*f_ppr + e3*c2pr + e5*cpr

         dv1rl = dv1rl + e4*rhorl
         dv1rr = dv1rr + e4*rhorr

         dv2 = abseig2*my_half*(f_m*dpress - rho*c*dubar)/c2

         c_dpress = abseig2*my_half*( f_m  )/c2
         c_dubar  = abseig2*my_half*(-rho*c)/c2

         e1 = my_half*(f_m*dpress - rho*c*dubar)/c2
         e2 = abseig2*my_half*(dpress)/c2
         e3 = -dv2/c2
         e4 = abseig2*my_half*(  -c*dubar)/c2
         e5 = abseig2*my_half*(-rho*dubar)/c2

         dv2rl =                   + e1*eig2rl + e2*f_mrl + e3*c2rl + e5*crl
         dv2ul = c_dubar*dubarul   + e1*eig2ul + e2*f_mul + e3*c2ul + e5*cul
         dv2vl = c_dubar*dubarvl   + e1*eig2vl + e2*f_mvl + e3*c2vl + e5*cvl
         dv2wl = c_dubar*dubarwl   + e1*eig2wl + e2*f_mwl + e3*c2wl + e5*cwl
         dv2pl = c_dpress*dpresspl + e1*eig2pl + e2*f_mpl + e3*c2pl + e5*cpl

         dv2rr =                   + e1*eig2rr + e2*f_mrr + e3*c2rr + e5*crr
         dv2ur = c_dubar*dubarur   + e1*eig2ur + e2*f_mur + e3*c2ur + e5*cur
         dv2vr = c_dubar*dubarvr   + e1*eig2vr + e2*f_mvr + e3*c2vr + e5*cvr
         dv2wr = c_dubar*dubarwr   + e1*eig2wr + e2*f_mwr + e3*c2wr + e5*cwr
         dv2pr = c_dpress*dpresspr + e1*eig2pr + e2*f_mpr + e3*c2pr + e5*cpr

         dv2rl = dv2rl + e4*rhorl
         dv2rr = dv2rr + e4*rhorr

         dv3 = abseig3*(drho - dpress/c2)

         c_drho   = abseig3
         c_dpress =-abseig3/c2
         e1       = (drho - dpress/c2)
         e2       = abseig3*dpress/c4

         dv3rl = c_drho*drhorl     + e1*eig3rl + e2*c2rl
         dv3ul =                   + e1*eig3ul + e2*c2ul
         dv3vl =                   + e1*eig3vl + e2*c2vl
         dv3wl =                   + e1*eig3wl + e2*c2wl
         dv3pl = c_dpress*dpresspl + e1*eig3pl + e2*c2pl

         dv3rr = c_drho*drhorr     + e1*eig3rr + e2*c2rr
         dv3ur =                   + e1*eig3ur + e2*c2ur
         dv3vr =                   + e1*eig3vr + e2*c2vr
         dv3wr =                   + e1*eig3wr + e2*c2wr
         dv3pr = c_dpress*dpresspr + e1*eig3pr + e2*c2pr

         dv4 = abseig3*rho*(du - dubar*xnorm)

         c_du    = abseig3*rho
         c_dubar =-abseig3*rho*xnorm
         e1      =     rho*(du - dubar*xnorm)
         e2      = abseig3*(du - dubar*xnorm)

         dv4rl =                             + e1*eig3rl + e2*rhorl
         dv4ul = c_du*duul + c_dubar*dubarul + e1*eig3ul
         dv4vl =             c_dubar*dubarvl + e1*eig3vl
         dv4wl =             c_dubar*dubarwl + e1*eig3wl
         dv4pl =                             + e1*eig3pl

         dv4rr =                             + e1*eig3rr + e2*rhorr
         dv4ur = c_du*duur + c_dubar*dubarur + e1*eig3ur
         dv4vr =             c_dubar*dubarvr + e1*eig3vr
         dv4wr =             c_dubar*dubarwr + e1*eig3wr
         dv4pr =                             + e1*eig3pr

         dv5 = abseig3*rho*(dv - dubar*ynorm)

         c_dv    = abseig3*rho
         c_dubar =-abseig3*rho*ynorm
         e1      =     rho*(dv - dubar*ynorm)
         e2      = abseig3*(dv - dubar*ynorm)

         dv5rl =                             + e1*eig3rl + e2*rhorl
         dv5ul =             c_dubar*dubarul + e1*eig3ul
         dv5vl = c_dv*dvvl + c_dubar*dubarvl + e1*eig3vl
         dv5wl =             c_dubar*dubarwl + e1*eig3wl
         dv5pl =                             + e1*eig3pl

         dv5rr =                             + e1*eig3rr + e2*rhorr
         dv5ur =             c_dubar*dubarur + e1*eig3ur
         dv5vr = c_dv*dvvr + c_dubar*dubarvr + e1*eig3vr
         dv5wr =             c_dubar*dubarwr + e1*eig3wr
         dv5pr =                             + e1*eig3pr

         dv6 = abseig3*rho*(dw - dubar*znorm)

         c_dw    = abseig3*rho
         c_dubar =-abseig3*rho*znorm
         e1      =     rho*(dw - dubar*znorm)
         e2      = abseig3*(dw - dubar*znorm)

         dv6rl =                             + e1*eig3rl + e2*rhorl
         dv6ul =             c_dubar*dubarul + e1*eig3ul
         dv6vl =             c_dubar*dubarvl + e1*eig3vl
         dv6wl = c_dw*dwwl + c_dubar*dubarwl + e1*eig3wl
         dv6pl =                             + e1*eig3pl

         dv6rr =                             + e1*eig3rr + e2*rhorr
         dv6ur =             c_dubar*dubarur + e1*eig3ur
         dv6vr =             c_dubar*dubarvr + e1*eig3vr
         dv6wr = c_dw*dwwr + c_dubar*dubarwr + e1*eig3wr
         dv6pr =                             + e1*eig3pr

         dv7 = c_sigma*( dv1 + dv2 )

         e1  = ( dv1 + dv2 )

         dv7rl = c_sigma*( dv1rl + dv2rl ) + e1*c_sigmarl
         dv7ul = c_sigma*( dv1ul + dv2ul ) + e1*c_sigmaul
         dv7vl = c_sigma*( dv1vl + dv2vl ) + e1*c_sigmavl
         dv7wl = c_sigma*( dv1wl + dv2wl ) + e1*c_sigmawl
         dv7pl = c_sigma*( dv1pl + dv2pl ) + e1*c_sigmapl

         dv7rr = c_sigma*( dv1rr + dv2rr ) + e1*c_sigmarr
         dv7ur = c_sigma*( dv1ur + dv2ur ) + e1*c_sigmaur
         dv7vr = c_sigma*( dv1vr + dv2vr ) + e1*c_sigmavr
         dv7wr = c_sigma*( dv1wr + dv2wr ) + e1*c_sigmawr
         dv7pr = c_sigma*( dv1pr + dv2pr ) + e1*c_sigmapr

         dv8 = c_sigma*( g_m*dv1 - g_p*dv2 )

         e1 = ( g_m*dv1 - g_p*dv2 )
         e2 = c_sigma*(   dv1 )
         e3 = c_sigma*( - dv2 )

         dv8rl = c_sigma*( g_m*dv1rl - g_p*dv2rl ) + e1*c_sigmarl
         dv8ul = c_sigma*( g_m*dv1ul - g_p*dv2ul ) + e1*c_sigmaul
         dv8vl = c_sigma*( g_m*dv1vl - g_p*dv2vl ) + e1*c_sigmavl
         dv8wl = c_sigma*( g_m*dv1wl - g_p*dv2wl ) + e1*c_sigmawl
         dv8pl = c_sigma*( g_m*dv1pl - g_p*dv2pl ) + e1*c_sigmapl

         dv8rr = c_sigma*( g_m*dv1rr - g_p*dv2rr ) + e1*c_sigmarr
         dv8ur = c_sigma*( g_m*dv1ur - g_p*dv2ur ) + e1*c_sigmaur
         dv8vr = c_sigma*( g_m*dv1vr - g_p*dv2vr ) + e1*c_sigmavr
         dv8wr = c_sigma*( g_m*dv1wr - g_p*dv2wr ) + e1*c_sigmawr
         dv8pr = c_sigma*( g_m*dv1pr - g_p*dv2pr ) + e1*c_sigmapr

         dv8rl = dv8rl + e2*g_mrl + e3*g_prl
         dv8ul = dv8ul + e2*g_mul + e3*g_pul
         dv8vl = dv8vl + e2*g_mvl + e3*g_pvl
         dv8wl = dv8wl + e2*g_mwl + e3*g_pwl
         dv8pl = dv8pl + e2*g_mpl + e3*g_ppl

         dv8rr = dv8rr + e2*g_mrr + e3*g_prr
         dv8ur = dv8ur + e2*g_mur + e3*g_pur
         dv8vr = dv8vr + e2*g_mvr + e3*g_pvr
         dv8wr = dv8wr + e2*g_mwr + e3*g_pwr
         dv8pr = dv8pr + e2*g_mpr + e3*g_ppr

         t1 = dv3 + dv7

         t1rl = dv3rl + dv7rl
         t1ul = dv3ul + dv7ul
         t1vl = dv3vl + dv7vl
         t1wl = dv3wl + dv7wl
         t1pl = dv3pl + dv7pl

         t1rr = dv3rr + dv7rr
         t1ur = dv3ur + dv7ur
         t1vr = dv3vr + dv7vr
         t1wr = dv3wr + dv7wr
         t1pr = dv3pr + dv7pr

!        t2 = u*t1 + dv4 + xnorm*dv8

         t2rl = u*t1rl + dv4rl + xnorm*dv8rl + t1*url
         t2ul = u*t1ul + dv4ul + xnorm*dv8ul + t1*uul
         t2vl = u*t1vl + dv4vl + xnorm*dv8vl + t1*uvl
         t2wl = u*t1wl + dv4wl + xnorm*dv8wl + t1*uwl
         t2pl = u*t1pl + dv4pl + xnorm*dv8pl + t1*upl

         t2rr = u*t1rr + dv4rr + xnorm*dv8rr + t1*urr
         t2ur = u*t1ur + dv4ur + xnorm*dv8ur + t1*uur
         t2vr = u*t1vr + dv4vr + xnorm*dv8vr + t1*uvr
         t2wr = u*t1wr + dv4wr + xnorm*dv8wr + t1*uwr
         t2pr = u*t1pr + dv4pr + xnorm*dv8pr + t1*upr

!        t3 = v*t1 + dv5 + ynorm*dv8

         t3rl = v*t1rl + dv5rl + ynorm*dv8rl + t1*vrl
         t3ul = v*t1ul + dv5ul + ynorm*dv8ul + t1*vul
         t3vl = v*t1vl + dv5vl + ynorm*dv8vl + t1*vvl
         t3wl = v*t1wl + dv5wl + ynorm*dv8wl + t1*vwl
         t3pl = v*t1pl + dv5pl + ynorm*dv8pl + t1*vpl

         t3rr = v*t1rr + dv5rr + ynorm*dv8rr + t1*vrr
         t3ur = v*t1ur + dv5ur + ynorm*dv8ur + t1*vur
         t3vr = v*t1vr + dv5vr + ynorm*dv8vr + t1*vvr
         t3wr = v*t1wr + dv5wr + ynorm*dv8wr + t1*vwr
         t3pr = v*t1pr + dv5pr + ynorm*dv8pr + t1*vpr

!        t4 = w*t1 + dv6 + znorm*dv8

         t4rl = w*t1rl + dv6rl + znorm*dv8rl + t1*wrl
         t4ul = w*t1ul + dv6ul + znorm*dv8ul + t1*wul
         t4vl = w*t1vl + dv6vl + znorm*dv8vl + t1*wvl
         t4wl = w*t1wl + dv6wl + znorm*dv8wl + t1*wwl
         t4pl = w*t1pl + dv6pl + znorm*dv8pl + t1*wpl

         t4rr = w*t1rr + dv6rr + znorm*dv8rr + t1*wrr
         t4ur = w*t1ur + dv6ur + znorm*dv8ur + t1*wur
         t4vr = w*t1vr + dv6vr + znorm*dv8vr + t1*wvr
         t4wr = w*t1wr + dv6wr + znorm*dv8wr + t1*wwr
         t4pr = w*t1pr + dv6pr + znorm*dv8pr + t1*wpr

!        t5 = my_half*q2*t1 + u*dv4 + v*dv5 + w*dv6 + ubar*dv8 + c2*dv7/gm1

         c2g = c2/gm1
         q2h = my_half*q2
         e1  = my_half*t1
         e2  = dv4
         e3  = dv5
         e4  = dv6
         e5  = dv8
         e6  = dv7/gm1

         t5rl = q2h*t1rl + u*dv4rl + v*dv5rl + w*dv6rl + ubar*dv8rl + c2g*dv7rl
         t5ul = q2h*t1ul + u*dv4ul + v*dv5ul + w*dv6ul + ubar*dv8ul + c2g*dv7ul
         t5vl = q2h*t1vl + u*dv4vl + v*dv5vl + w*dv6vl + ubar*dv8vl + c2g*dv7vl
         t5wl = q2h*t1wl + u*dv4wl + v*dv5wl + w*dv6wl + ubar*dv8wl + c2g*dv7wl
         t5pl = q2h*t1pl + u*dv4pl + v*dv5pl + w*dv6pl + ubar*dv8pl + c2g*dv7pl

         t5rr = q2h*t1rr + u*dv4rr + v*dv5rr + w*dv6rr + ubar*dv8rr + c2g*dv7rr
         t5ur = q2h*t1ur + u*dv4ur + v*dv5ur + w*dv6ur + ubar*dv8ur + c2g*dv7ur
         t5vr = q2h*t1vr + u*dv4vr + v*dv5vr + w*dv6vr + ubar*dv8vr + c2g*dv7vr
         t5wr = q2h*t1wr + u*dv4wr + v*dv5wr + w*dv6wr + ubar*dv8wr + c2g*dv7wr
         t5pr = q2h*t1pr + u*dv4pr + v*dv5pr + w*dv6pr + ubar*dv8pr + c2g*dv7pr

         t5rl = t5rl + e1*q2rl + e2*url + e3*vrl + e4*wrl + e5*ubarrl + e6*c2rl
         t5ul = t5ul + e1*q2ul + e2*uul + e3*vul + e4*wul + e5*ubarul + e6*c2ul
         t5vl = t5vl + e1*q2vl + e2*uvl + e3*vvl + e4*wvl + e5*ubarvl + e6*c2vl
         t5wl = t5wl + e1*q2wl + e2*uwl + e3*vwl + e4*wwl + e5*ubarwl + e6*c2wl
         t5pl = t5pl + e1*q2pl + e2*upl + e3*vpl + e4*wpl + e5*ubarpl + e6*c2pl

         t5rr = t5rr + e1*q2rr + e2*urr + e3*vrr + e4*wrr + e5*ubarrr + e6*c2rr
         t5ur = t5ur + e1*q2ur + e2*uur + e3*vur + e4*wur + e5*ubarur + e6*c2ur
         t5vr = t5vr + e1*q2vr + e2*uvr + e3*vvr + e4*wvr + e5*ubarvr + e6*c2vr
         t5wr = t5wr + e1*q2wr + e2*uwr + e3*vwr + e4*wwr + e5*ubarwr + e6*c2wr
         t5pr = t5pr + e1*q2pr + e2*upr + e3*vpr + e4*wpr + e5*ubarpr + e6*c2pr


!     Add in the alternate preconditioning form contributions

!        at1 = dv3 + dv7

!     ...original entropy only term

         at1 = ta*abseig3*( dpress*(+my_1 - my_1/beta)       &
                          - dubar*rho*2.0_dp*psi/beta )/c2

         c_dubar  = ta*abseig3*(-rho*2.0_dp*psi/beta )/c2
         c_dpress = ta*abseig3*(+my_1 - my_1/beta)/c2

         e1 = ta*( dpress*(+my_1 - my_1/beta)       &
                 - dubar*rho*2.0_dp*psi/beta )/c2
         e2 = ta*abseig3*( dpress*(+my_1/beta)                     &
                         + dubar*rho*2.0_dp*psi/beta )/(c2*beta)
         e3 = -at1/c2
         e4 = ta*abseig3*( - dubar*2.0_dp*rho/beta )/c2
         e5 = ta*abseig3*( - dubar*2.0_dp*psi/beta )/c2

         at1rl =                   + e1*eig3rl + e2*betarl + e3*c2rl + e4*psirl
         at1ul = c_dubar*dubarul   + e1*eig3ul + e2*betaul + e3*c2ul + e4*psiul
         at1vl = c_dubar*dubarvl   + e1*eig3vl + e2*betavl + e3*c2vl + e4*psivl
         at1wl = c_dubar*dubarwl   + e1*eig3wl + e2*betawl + e3*c2wl + e4*psiwl
         at1pl = c_dpress*dpresspl + e1*eig3pl + e2*betapl + e3*c2pl + e4*psipl

         at1rr =                   + e1*eig3rr + e2*betarr + e3*c2rr + e4*psirr
         at1ur = c_dubar*dubarur   + e1*eig3ur + e2*betaur + e3*c2ur + e4*psiur
         at1vr = c_dubar*dubarvr   + e1*eig3vr + e2*betavr + e3*c2vr + e4*psivr
         at1wr = c_dubar*dubarwr   + e1*eig3wr + e2*betawr + e3*c2wr + e4*psiwr
         at1pr = c_dpress*dpresspr + e1*eig3pr + e2*betapr + e3*c2pr + e4*psipr

         at1rl = at1rl + e5*rhorl
         at1rr = at1rr + e5*rhorr

         at1 = at1 + ta*c_sigma*( dv1*( -my_1 + my_1/(beta*f_p**2) )  &
                                + dv2*( -my_1 + my_1/(beta*f_m**2) ) )

         e1 = ta*c_sigma*( -my_1 + my_1/(beta*f_p**2) )
         e2 = ta*c_sigma*( -my_1 + my_1/(beta*f_m**2) )
         e3 = ta*( dv1*( -my_1 + my_1/(beta*f_p**2) )  &
                 + dv2*( -my_1 + my_1/(beta*f_m**2) ) )

         e4 = ta*c_sigma*dv1*( -2.0_dp/(beta*f_p**3) )
         e5 = ta*c_sigma*dv2*( -2.0_dp/(beta*f_m**3) )
         e6 = ta*c_sigma*( dv1*( -my_1/(beta*f_p**2) )  &
                         + dv2*( -my_1/(beta*f_m**2) ) )/beta
         at1rl = at1rl + e1*dv1rl + e2*dv2rl + e3*c_sigmarl
         at1ul = at1ul + e1*dv1ul + e2*dv2ul + e3*c_sigmaul
         at1vl = at1vl + e1*dv1vl + e2*dv2vl + e3*c_sigmavl
         at1wl = at1wl + e1*dv1wl + e2*dv2wl + e3*c_sigmawl
         at1pl = at1pl + e1*dv1pl + e2*dv2pl + e3*c_sigmapl

         at1rr = at1rr + e1*dv1rr + e2*dv2rr + e3*c_sigmarr
         at1ur = at1ur + e1*dv1ur + e2*dv2ur + e3*c_sigmaur
         at1vr = at1vr + e1*dv1vr + e2*dv2vr + e3*c_sigmavr
         at1wr = at1wr + e1*dv1wr + e2*dv2wr + e3*c_sigmawr
         at1pr = at1pr + e1*dv1pr + e2*dv2pr + e3*c_sigmapr

         at1rl = at1rl + e4*f_prl + e5*f_mrl + e6*betarl
         at1ul = at1ul + e4*f_pul + e5*f_mul + e6*betaul
         at1vl = at1vl + e4*f_pvl + e5*f_mvl + e6*betavl
         at1wl = at1wl + e4*f_pwl + e5*f_mwl + e6*betawl
         at1pl = at1pl + e4*f_ppl + e5*f_mpl + e6*betapl

         at1rr = at1rr + e4*f_prr + e5*f_mrr + e6*betarr
         at1ur = at1ur + e4*f_pur + e5*f_mur + e6*betaur
         at1vr = at1vr + e4*f_pvr + e5*f_mvr + e6*betavr
         at1wr = at1wr + e4*f_pwr + e5*f_mwr + e6*betawr
         at1pr = at1pr + e4*f_ppr + e5*f_mpr + e6*betapr

!        Now add in alternate preconditioning terms

         t1rl = t1rl + at1rl
         t1ul = t1ul + at1ul
         t1vl = t1vl + at1vl
         t1wl = t1wl + at1wl
         t1pl = t1pl + at1pl

         t1rr = t1rr + at1rr
         t1ur = t1ur + at1ur
         t1vr = t1vr + at1vr
         t1wr = t1wr + at1wr
         t1pr = t1pr + at1pr

!        t2 = u*at1

         t2rl = t2rl + u*at1rl + at1*url
         t2ul = t2ul + u*at1ul + at1*uul
         t2vl = t2vl + u*at1vl + at1*uvl
         t2wl = t2wl + u*at1wl + at1*uwl
         t2pl = t2pl + u*at1pl + at1*upl

         t2rr = t2rr + u*at1rr + at1*urr
         t2ur = t2ur + u*at1ur + at1*uur
         t2vr = t2vr + u*at1vr + at1*uvr
         t2wr = t2wr + u*at1wr + at1*uwr
         t2pr = t2pr + u*at1pr + at1*upr

!        t3 = v*at1

         t3rl = t3rl + v*at1rl + at1*vrl
         t3ul = t3ul + v*at1ul + at1*vul
         t3vl = t3vl + v*at1vl + at1*vvl
         t3wl = t3wl + v*at1wl + at1*vwl
         t3pl = t3pl + v*at1pl + at1*vpl

         t3rr = t3rr + v*at1rr + at1*vrr
         t3ur = t3ur + v*at1ur + at1*vur
         t3vr = t3vr + v*at1vr + at1*vvr
         t3wr = t3wr + v*at1wr + at1*vwr
         t3pr = t3pr + v*at1pr + at1*vpr

!        t4 = w*at1

         t4rl = t4rl + w*at1rl + at1*wrl
         t4ul = t4ul + w*at1ul + at1*wul
         t4vl = t4vl + w*at1vl + at1*wvl
         t4wl = t4wl + w*at1wl + at1*wwl
         t4pl = t4pl + w*at1pl + at1*wpl

         t4rr = t4rr + w*at1rr + at1*wrr
         t4ur = t4ur + w*at1ur + at1*wur
         t4vr = t4vr + w*at1vr + at1*wvr
         t4wr = t4wr + w*at1wr + at1*wwr
         t4pr = t4pr + w*at1pr + at1*wpr
!        t5 = my_half*q2*at1

         q2h = my_half*q2
         e1  = my_half*at1

         t5rl = t5rl + q2h*at1rl + e1*q2rl
         t5ul = t5ul + q2h*at1ul + e1*q2ul
         t5vl = t5vl + q2h*at1vl + e1*q2vl
         t5wl = t5wl + q2h*at1wl + e1*q2wl
         t5pl = t5pl + q2h*at1pl + e1*q2pl

         t5rr = t5rr + q2h*at1rr + e1*q2rr
         t5ur = t5ur + q2h*at1ur + e1*q2ur
         t5vr = t5vr + q2h*at1vr + e1*q2vr
         t5wr = t5wr + q2h*at1wr + e1*q2wr
         t5pr = t5pr + q2h*at1pr + e1*q2pr

! Compute flux using variables from left side of face

!        fluxl1 = ubar_fsl*rhol

         fluxl1rl =              ubar_fsl*rholrl
         fluxl1ul = rhol*ubarlul
         fluxl1vl = rhol*ubarlvl
         fluxl1wl = rhol*ubarlwl
         fluxl1pl = my_0
!        fluxl2 = ubar_fsl*rhol*ul + xnorm*pressl

         fluxl2rl =                                 ul*ubar_fsl*rholrl
         fluxl2ul = rhol*(ul*ubarlul+ubar_fsl*ulul)
         fluxl2vl = rhol*(ul*ubarlvl)
         fluxl2wl = rhol*(ul*ubarlwl)
         fluxl2pl =                                 xnorm*presslpl

!        fluxl3 = ubar_fsl*rhol*vl + ynorm*pressl

         fluxl3rl =                                 vl*ubar_fsl*rholrl
         fluxl3ul = rhol*(vl*ubarlul)
         fluxl3vl = rhol*(vl*ubarlvl+ubar_fsl*vlvl)
         fluxl3wl = rhol*(vl*ubarlwl)
         fluxl3pl =                                 ynorm*presslpl

!        fluxl4 = ubar_fsl*rhol*wl + znorm*pressl

         fluxl4rl =                                 wl*ubar_fsl*rholrl
         fluxl4ul = rhol*(wl*ubarlul)
         fluxl4vl = rhol*(wl*ubarlvl)
         fluxl4wl = rhol*(wl*ubarlwl+ubar_fsl*wlwl)
         fluxl4pl =                                 znorm*presslpl

!        fluxl5 = ubar_fsl*(energyl + pressl) + face_speed*pressl

         fluxl5rl =                              ubar_fsl*(energylrl)
         fluxl5ul = (energyl + pressl)*ubarlul + ubar_fsl*(energylul)
         fluxl5vl = (energyl + pressl)*ubarlvl + ubar_fsl*(energylvl)
         fluxl5wl = (energyl + pressl)*ubarlwl + ubar_fsl*(energylwl)
         fluxl5pl =                              ubar_fsl*(energylpl+presslpl) &
                                               + face_speed*presslpl


! Now the right side

!      fluxr1 = ubar_fsr*rhor

         fluxr1rr =              ubar_fsr*rhorrr
         fluxr1ur = rhor*ubarrur
         fluxr1vr = rhor*ubarrvr
         fluxr1wr = rhor*ubarrwr
         fluxr1pr = my_0

!      fluxr2 = ubar_fsr*rhor*ur + xnorm*pressr

         fluxr2rr =                                 ur*ubar_fsr*rhorrr
         fluxr2ur = rhor*(ur*ubarrur+ubar_fsr*urur)
         fluxr2vr = rhor*(ur*ubarrvr)
         fluxr2wr = rhor*(ur*ubarrwr)
         fluxr2pr =                                 xnorm*pressrpr

!      fluxr3 = ubar_fsr*rhor*vr + ynorm*pressr

         fluxr3rr =                                 vr*ubar_fsr*rhorrr
         fluxr3ur = rhor*(vr*ubarrur)
         fluxr3vr = rhor*(vr*ubarrvr+ubar_fsr*vrvr)
         fluxr3wr = rhor*(vr*ubarrwr)
         fluxr3pr =                                 ynorm*pressrpr

!      fluxr4 = ubar_fsr*rhor*wr + znorm*pressr

         fluxr4rr =                                 wr*ubar_fsr*rhorrr
         fluxr4ur = rhor*(wr*ubarrur)
         fluxr4vr = rhor*(wr*ubarrvr)
         fluxr4wr = rhor*(wr*ubarrwr+ubar_fsr*wrwr)
         fluxr4pr =                                 znorm*pressrpr

!      fluxr5 = ubar_fsr*(energyr + pressr) + face_speed*pressr

         fluxr5rr =                              ubar_fsr*(energyrrr)
         fluxr5ur = (energyr + pressr)*ubarrur + ubar_fsr*(energyrur)
         fluxr5vr = (energyr + pressr)*ubarrvr + ubar_fsr*(energyrvr)
         fluxr5wr = (energyr + pressr)*ubarrwr + ubar_fsr*(energyrwr)
         fluxr5pr =                              ubar_fsr*(energyrpr+pressrpr) &
                                               + face_speed*pressrpr

       half_area = ra(n)*my_half

!         flux1 = half_area*(fluxl1 + fluxr1 - t1)
!         flux2 = half_area*(fluxl2 + fluxr2 - t2)
!         flux3 = half_area*(fluxl3 + fluxr3 - t3)
!         flux4 = half_area*(fluxl4 + fluxr4 - t4)
!         flux5 = half_area*(fluxl5 + fluxr5 - t5)

       flux1rl = half_area*(fluxl1rl - t1rl)
       flux1ul = half_area*(fluxl1ul - t1ul)
       flux1vl = half_area*(fluxl1vl - t1vl)
       flux1wl = half_area*(fluxl1wl - t1wl)
       flux1pl = half_area*(fluxl1pl - t1pl)

       flux1rr = half_area*(fluxr1rr - t1rr)
       flux1ur = half_area*(fluxr1ur - t1ur)
       flux1vr = half_area*(fluxr1vr - t1vr)
       flux1wr = half_area*(fluxr1wr - t1wr)
       flux1pr = half_area*(fluxr1pr - t1pr)

       flux2rl = half_area*(fluxl2rl - t2rl)
       flux2ul = half_area*(fluxl2ul - t2ul)
       flux2vl = half_area*(fluxl2vl - t2vl)
       flux2wl = half_area*(fluxl2wl - t2wl)
       flux2pl = half_area*(fluxl2pl - t2pl)

       flux2rr = half_area*(fluxr2rr - t2rr)
       flux2ur = half_area*(fluxr2ur - t2ur)
       flux2vr = half_area*(fluxr2vr - t2vr)
       flux2wr = half_area*(fluxr2wr - t2wr)
       flux2pr = half_area*(fluxr2pr - t2pr)

       flux3rl = half_area*(fluxl3rl - t3rl)
       flux3ul = half_area*(fluxl3ul - t3ul)
       flux3vl = half_area*(fluxl3vl - t3vl)
       flux3wl = half_area*(fluxl3wl - t3wl)
       flux3pl = half_area*(fluxl3pl - t3pl)

       flux3rr = half_area*(fluxr3rr - t3rr)
       flux3ur = half_area*(fluxr3ur - t3ur)
       flux3vr = half_area*(fluxr3vr - t3vr)
       flux3wr = half_area*(fluxr3wr - t3wr)
       flux3pr = half_area*(fluxr3pr - t3pr)

       flux4rl = half_area*(fluxl4rl - t4rl)
       flux4ul = half_area*(fluxl4ul - t4ul)
       flux4vl = half_area*(fluxl4vl - t4vl)
       flux4wl = half_area*(fluxl4wl - t4wl)
       flux4pl = half_area*(fluxl4pl - t4pl)

       flux4rr = half_area*(fluxr4rr - t4rr)
       flux4ur = half_area*(fluxr4ur - t4ur)
       flux4vr = half_area*(fluxr4vr - t4vr)
       flux4wr = half_area*(fluxr4wr - t4wr)
       flux4pr = half_area*(fluxr4pr - t4pr)

       flux5rl = half_area*(fluxl5rl - t5rl)
       flux5ul = half_area*(fluxl5ul - t5ul)
       flux5vl = half_area*(fluxl5vl - t5vl)
       flux5wl = half_area*(fluxl5wl - t5wl)
       flux5pl = half_area*(fluxl5pl - t5pl)

       flux5rr = half_area*(fluxr5rr - t5rr)
       flux5ur = half_area*(fluxr5ur - t5ur)
       flux5vr = half_area*(fluxr5vr - t5vr)
       flux5wr = half_area*(fluxr5wr - t5wr)
       flux5pr = half_area*(fluxr5pr - t5pr)

!  These last 50 equations are the dfp's and dfm's we're
!  looking for, but they're w.r.t. primitive variables.

!  We need to convert these last results into conservative
!  variables, using one last chain-rule.

!  q = primitive
!  Q = conservative

!  First the left transformation

        if(conservative_linearization) then
        q1Q1l = my_1
        q1Q2l = my_0
        q1Q3l = my_0
        q1Q4l = my_0
        q1Q5l = my_0

        q2Q1l = - ul / rhol
        q2Q2l = my_1 / rhol
        q2Q3l = my_0
        q2Q4l = my_0
        q2Q5l = my_0

        q3Q1l = - vl / rhol
        q3Q2l = my_0
        q3Q3l = my_1 / rhol
        q3Q4l = my_0
        q3Q5l = my_0

        q4Q1l = - wl / rhol
        q4Q2l = my_0
        q4Q3l = my_0
        q4Q4l = my_1 / rhol
        q4Q5l = my_0

        q5Q1l = gm1 / 2.0_dp * (ul*ul + vl*vl + wl*wl)
        q5Q2l = - gm1 * ul
        q5Q3l = - gm1 * vl
        q5Q4l = - gm1 * wl
        q5Q5l = gm1
        else
        q1Q1l = my_1
        q1Q2l = my_0
        q1Q3l = my_0
        q1Q4l = my_0
        q1Q5l = my_0

        q2Q1l = my_0
        q2Q2l = my_1
        q2Q3l = my_0
        q2Q4l = my_0
        q2Q5l = my_0

        q3Q1l = my_0
        q3Q2l = my_0
        q3Q3l = my_1
        q3Q4l = my_0
        q3Q5l = my_0

        q4Q1l = my_0
        q4Q2l = my_0
        q4Q3l = my_0
        q4Q4l = my_1
        q4Q5l = my_0

        q5Q1l = my_0
        q5Q2l = my_0
        q5Q3l = my_0
        q5Q4l = my_0
        q5Q5l = my_1
        endif

        dfp(1,1) = flux1rl*q1Q1l + flux1ul*q2Q1l + flux1vl*q3Q1l              &
              + flux1wl*q4Q1l + flux1pl*q5Q1l

        dfp(1,2) = flux1rl*q1Q2l + flux1ul*q2Q2l + flux1vl*q3Q2l              &
              + flux1wl*q4Q2l + flux1pl*q5Q2l

        dfp(1,3) = flux1rl*q1Q3l + flux1ul*q2Q3l + flux1vl*q3Q3l              &
              + flux1wl*q4Q3l + flux1pl*q5Q3l

        dfp(1,4) = flux1rl*q1Q4l + flux1ul*q2Q4l + flux1vl*q3Q4l              &
              + flux1wl*q4Q4l + flux1pl*q5Q4l

        dfp(1,5) = flux1rl*q1Q5l + flux1ul*q2Q5l + flux1vl*q3Q5l              &
              + flux1wl*q4Q5l + flux1pl*q5Q5l

        dfp(2,1) = flux2rl*q1Q1l + flux2ul*q2Q1l + flux2vl*q3Q1l              &
              + flux2wl*q4Q1l + flux2pl*q5Q1l

        dfp(2,2) = flux2rl*q1Q2l + flux2ul*q2Q2l + flux2vl*q3Q2l              &
              + flux2wl*q4Q2l + flux2pl*q5Q2l

        dfp(2,3) = flux2rl*q1Q3l + flux2ul*q2Q3l + flux2vl*q3Q3l              &
              + flux2wl*q4Q3l + flux2pl*q5Q3l

        dfp(2,4) = flux2rl*q1Q4l + flux2ul*q2Q4l + flux2vl*q3Q4l              &
              + flux2wl*q4Q4l + flux2pl*q5Q4l

        dfp(2,5) = flux2rl*q1Q5l + flux2ul*q2Q5l + flux2vl*q3Q5l              &
              + flux2wl*q4Q5l + flux2pl*q5Q5l

        dfp(3,1) = flux3rl*q1Q1l + flux3ul*q2Q1l + flux3vl*q3Q1l              &
              + flux3wl*q4Q1l + flux3pl*q5Q1l

        dfp(3,2) = flux3rl*q1Q2l + flux3ul*q2Q2l + flux3vl*q3Q2l              &
              + flux3wl*q4Q2l + flux3pl*q5Q2l

        dfp(3,3) = flux3rl*q1Q3l + flux3ul*q2Q3l + flux3vl*q3Q3l              &
              + flux3wl*q4Q3l + flux3pl*q5Q3l

        dfp(3,4) = flux3rl*q1Q4l + flux3ul*q2Q4l + flux3vl*q3Q4l              &
              + flux3wl*q4Q4l + flux3pl*q5Q4l

        dfp(3,5) = flux3rl*q1Q5l + flux3ul*q2Q5l + flux3vl*q3Q5l              &
              + flux3wl*q4Q5l + flux3pl*q5Q5l

        dfp(4,1) = flux4rl*q1Q1l + flux4ul*q2Q1l + flux4vl*q3Q1l              &
              + flux4wl*q4Q1l + flux4pl*q5Q1l

        dfp(4,2) = flux4rl*q1Q2l + flux4ul*q2Q2l + flux4vl*q3Q2l              &
              + flux4wl*q4Q2l + flux4pl*q5Q2l

        dfp(4,3) = flux4rl*q1Q3l + flux4ul*q2Q3l + flux4vl*q3Q3l              &
              + flux4wl*q4Q3l + flux4pl*q5Q3l

        dfp(4,4) = flux4rl*q1Q4l + flux4ul*q2Q4l + flux4vl*q3Q4l              &
              + flux4wl*q4Q4l + flux4pl*q5Q4l

        dfp(4,5) = flux4rl*q1Q5l + flux4ul*q2Q5l + flux4vl*q3Q5l              &
              + flux4wl*q4Q5l + flux4pl*q5Q5l

        dfp(5,1) = flux5rl*q1Q1l + flux5ul*q2Q1l + flux5vl*q3Q1l              &
              + flux5wl*q4Q1l + flux5pl*q5Q1l

        dfp(5,2) = flux5rl*q1Q2l + flux5ul*q2Q2l + flux5vl*q3Q2l              &
              + flux5wl*q4Q2l + flux5pl*q5Q2l

        dfp(5,3) = flux5rl*q1Q3l + flux5ul*q2Q3l + flux5vl*q3Q3l              &
              + flux5wl*q4Q3l + flux5pl*q5Q3l

        dfp(5,4) = flux5rl*q1Q4l + flux5ul*q2Q4l + flux5vl*q3Q4l              &
              + flux5wl*q4Q4l + flux5pl*q5Q4l

        dfp(5,5) = flux5rl*q1Q5l + flux5ul*q2Q5l + flux5vl*q3Q5l              &
              + flux5wl*q4Q5l + flux5pl*q5Q5l

!  Now the right transformation

        if(conservative_linearization) then
        q1Q1r = my_1
        q1Q2r = my_0
        q1Q3r = my_0
        q1Q4r = my_0
        q1Q5r = my_0

        q2Q1r = - ur / rhor
        q2Q2r = my_1 / rhor
        q2Q3r = my_0
        q2Q4r = my_0
        q2Q5r = my_0

        q3Q1r = - vr / rhor
        q3Q2r = my_0
        q3Q3r = my_1 / rhor
        q3Q4r = my_0
        q3Q5r = my_0

        q4Q1r = - wr / rhor
        q4Q2r = my_0
        q4Q3r = my_0
        q4Q4r = my_1 / rhor
        q4Q5r = my_0

        q5Q1r = gm1 / 2.0_dp * (ur*ur + vr*vr + wr*wr)
        q5Q2r = - gm1 * ur
        q5Q3r = - gm1 * vr
        q5Q4r = - gm1 * wr
        q5Q5r = gm1
        else
        q1Q1r = my_1
        q1Q2r = my_0
        q1Q3r = my_0
        q1Q4r = my_0
        q1Q5r = my_0

        q2Q1r = my_0
        q2Q2r = my_1
        q2Q3r = my_0
        q2Q4r = my_0
        q2Q5r = my_0

        q3Q1r = my_0
        q3Q2r = my_0
        q3Q3r = my_1
        q3Q4r = my_0
        q3Q5r = my_0

        q4Q1r = my_0
        q4Q2r = my_0
        q4Q3r = my_0
        q4Q4r = my_1
        q4Q5r = my_0

        q5Q1r = my_0
        q5Q2r = my_0
        q5Q3r = my_0
        q5Q4r = my_0
        q5Q5r = my_1
        endif

        dfm(1,1) = flux1rr*q1Q1r + flux1ur*q2Q1r + flux1vr*q3Q1r              &
              + flux1wr*q4Q1r + flux1pr*q5Q1r

        dfm(1,2) = flux1rr*q1Q2r + flux1ur*q2Q2r + flux1vr*q3Q2r              &
              + flux1wr*q4Q2r + flux1pr*q5Q2r

        dfm(1,3) = flux1rr*q1Q3r + flux1ur*q2Q3r + flux1vr*q3Q3r              &
              + flux1wr*q4Q3r + flux1pr*q5Q3r

        dfm(1,4) = flux1rr*q1Q4r + flux1ur*q2Q4r + flux1vr*q3Q4r              &
              + flux1wr*q4Q4r + flux1pr*q5Q4r

        dfm(1,5) = flux1rr*q1Q5r + flux1ur*q2Q5r + flux1vr*q3Q5r              &
              + flux1wr*q4Q5r + flux1pr*q5Q5r

        dfm(2,1) = flux2rr*q1Q1r + flux2ur*q2Q1r + flux2vr*q3Q1r              &
              + flux2wr*q4Q1r + flux2pr*q5Q1r

        dfm(2,2) = flux2rr*q1Q2r + flux2ur*q2Q2r + flux2vr*q3Q2r              &
              + flux2wr*q4Q2r + flux2pr*q5Q2r

        dfm(2,3) = flux2rr*q1Q3r + flux2ur*q2Q3r + flux2vr*q3Q3r              &
              + flux2wr*q4Q3r + flux2pr*q5Q3r

        dfm(2,4) = flux2rr*q1Q4r + flux2ur*q2Q4r + flux2vr*q3Q4r              &
              + flux2wr*q4Q4r + flux2pr*q5Q4r

        dfm(2,5) = flux2rr*q1Q5r + flux2ur*q2Q5r + flux2vr*q3Q5r              &
              + flux2wr*q4Q5r + flux2pr*q5Q5r

        dfm(3,1) = flux3rr*q1Q1r + flux3ur*q2Q1r + flux3vr*q3Q1r              &
              + flux3wr*q4Q1r + flux3pr*q5Q1r

        dfm(3,2) = flux3rr*q1Q2r + flux3ur*q2Q2r + flux3vr*q3Q2r              &
              + flux3wr*q4Q2r + flux3pr*q5Q2r

        dfm(3,3) = flux3rr*q1Q3r + flux3ur*q2Q3r + flux3vr*q3Q3r              &
              + flux3wr*q4Q3r + flux3pr*q5Q3r

        dfm(3,4) = flux3rr*q1Q4r + flux3ur*q2Q4r + flux3vr*q3Q4r              &
              + flux3wr*q4Q4r + flux3pr*q5Q4r

        dfm(3,5) = flux3rr*q1Q5r + flux3ur*q2Q5r + flux3vr*q3Q5r              &
              + flux3wr*q4Q5r + flux3pr*q5Q5r

        dfm(4,1) = flux4rr*q1Q1r + flux4ur*q2Q1r + flux4vr*q3Q1r              &
              + flux4wr*q4Q1r + flux4pr*q5Q1r

        dfm(4,2) = flux4rr*q1Q2r + flux4ur*q2Q2r + flux4vr*q3Q2r              &
              + flux4wr*q4Q2r + flux4pr*q5Q2r

        dfm(4,3) = flux4rr*q1Q3r + flux4ur*q2Q3r + flux4vr*q3Q3r              &
              + flux4wr*q4Q3r + flux4pr*q5Q3r

        dfm(4,4) = flux4rr*q1Q4r + flux4ur*q2Q4r + flux4vr*q3Q4r              &
              + flux4wr*q4Q4r + flux4pr*q5Q4r

        dfm(4,5) = flux4rr*q1Q5r + flux4ur*q2Q5r + flux4vr*q3Q5r              &
              + flux4wr*q4Q5r + flux4pr*q5Q5r

        dfm(5,1) = flux5rr*q1Q1r + flux5ur*q2Q1r + flux5vr*q3Q1r              &
              + flux5wr*q4Q1r + flux5pr*q5Q1r

        dfm(5,2) = flux5rr*q1Q2r + flux5ur*q2Q2r + flux5vr*q3Q2r              &
              + flux5wr*q4Q2r + flux5pr*q5Q2r

        dfm(5,3) = flux5rr*q1Q3r + flux5ur*q2Q3r + flux5vr*q3Q3r              &
              + flux5wr*q4Q3r + flux5pr*q5Q3r

        dfm(5,4) = flux5rr*q1Q4r + flux5ur*q2Q4r + flux5vr*q3Q4r              &
              + flux5wr*q4Q4r + flux5pr*q5Q4r

        dfm(5,5) = flux5rr*q1Q5r + flux5ur*q2Q5r + flux5vr*q3Q5r              &
              + flux5wr*q4Q5r + flux5pr*q5Q5r

!       Accumulate contributions into a_diag and a_off

        if(node1 <= nnodes0) then
          idiag = g2m(node1)
          do k = 1, 5
            do j = 1, 5
              a_diag(j,k,idiag) = a_diag(j,k,idiag) + dfp(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          ioff  = fhelp(2,n)
          do k = 1, 5
            do j = 1, 5
              a_off(j,k,ioff)  = a_off(j,k,ioff) - dfp(j,k)
            end do
          end do
        end if

        if(node2 <= nnodes0) then
          idiag = g2m(node2)
          do k = 1, 5
            do j = 1, 5
              a_diag(j,k,idiag) = a_diag(j,k,idiag) - dfm(j,k)
            end do
          end do
        end if

        if(node1 <= nnodes0) then
          ioff  = fhelp(1,n)
          do k = 1, 5
            do j = 1, 5
              a_off(j,k,ioff)  = a_off(j,k,ioff) + dfm(j,k)
            end do
          end do
        end if

      end do scanedges

!  Convert back to conserved variables

    call ptoe(qdim,qnode,n_tot,0)

  end subroutine lowmach_prec_roe_jacobians

!==============================  DFLDFSSCP ===================================80
!
! Routine to compute the inviscid flux Jacobian of a calorically
! perfect gas using the LDFSS scheme.
!
!=============================================================================80

subroutine dfldfsscp(nq, nm, nfaces, qm, qp, s, dfdqm, dfdqp, wldfss)

  use fun3d_constants, only : pi
  use fluid,           only : gamma
  !
  ! Routine to compute the inviscid flux Jacobian of a calorically
  ! perfect gas using the LDFSS scheme
  !
  ! NQ     : leading dimension of q
  ! NM     : leading dimension of s
  ! S      : metrics on cell faces
  ! QM     : left/minus state primitive flow variables at cell faces
  ! QP     : right/plus state primitive flow variables at cell faces
  ! DFDQM  : flux Jacobian at the interface wrt q-left/minus (conserved)
  ! DFDQP  : flux Jacobian at the interface wrt q-right/plus (conserved)
  ! WLDFSS : coeff. to force LDFss(0) scheme
  !
  implicit none
  !
  !.. Formal Arguments ..
  integer, intent(in) :: nq, nm, nfaces
  !
  real(dp), intent(in) :: wldfss
  !
  real(dp), dimension(nfaces,1:nm),      intent(in)  :: s
  real(dp), dimension(nfaces,1:nq),      intent(in)  :: qm,       qp
  real(dp), dimension(nfaces,1:nq,1:nq), intent(out) :: dfdqm, dfdqp
  !
  !.. Local Scalars ..
  integer :: l,m1,m2,m3,nc,ne,nf,nk,nl,istat_alloc
  !
  real(dp) :: ahalf, al, alh, all, ar, arh, alr, ubm, ubp, ubmmf, ubpmf
  real(dp) :: btfunct, btl, btr, delp, htotl, htotr, fact, btlrxmc
  real(dp) :: grads, grdsnz, grdsrt, ppl, ppr, psum !pnet
  real(dp) :: rmbl, rmbr, ubml, ubpr
  real(dp) :: xmc, xmcl, xmcr, xmfunct, xmhalf, xmml, xmmr, xmclm, xmcrp
  real(dp) :: tkesw, gamogm1
  !
  !.. Local Arrays ..
  real(dp), dimension(1:nq) :: q
  real(dp), dimension(1:3)  :: snorm
  real(dp), dimension(1:nq) :: dubmld, dald, dalhd, dhtotld
  real(dp), dimension(1:nq) :: drmbld, dalld, dbtld
  real(dp), dimension(1:nq) :: dxmmld, dxmcld, dubmd, dppld
  real(dp), dimension(1:nq) :: dahalfd, dxmhalfd, dxmcd, ddelpd, dpsumd
  real(dp), dimension(1:nq) :: dxmfunctd, dbtfunctd, dfactd, dpnetd
  real(dp), dimension(1:nq) :: dubprd, dard, darhd, dhtotrd
  real(dp), dimension(1:nq) :: drmbrd, dalrd, dbtrd
  real(dp), dimension(1:nq) :: dxmmrd, dxmcrd, dubpd, dpprd
  real(dp), dimension(1:nq) :: dbtlrxmcd, dxmclmd, dxmcrpd
  !
  !.. Local Allocatable Arrays ..
  !
  real(dp), allocatable, dimension(:,:) :: dqd
  real(dp), allocatable, dimension(:,:) :: dfdp
  !
  !.. Intrinsic Functions ..
  intrinsic abs, int, max, min, sign, sin, sqrt

  !...Data set by Common.h
  integer, dimension(7,5) :: ngov

  real(dp) :: gamref

  integer, parameter :: iout = 6
  integer, parameter :: itrbmd = 0, itlmnr = 0  !turbulence model type
  real(dp), parameter :: rndoff = 1.0e-12_dp
  real(dp), parameter :: small = 1.0e-12_dp

!interface
!  pure function cp_dfdc(ngovf,nqf,gamreff,q,dfdp)
!
!    use kinddefs, only : dp
!
!    !
!    implicit none
!    !
!    !.. Formal Integer Arguments ..
!    integer,                           intent(in) :: nqf
!    integer, dimension(7,5),           intent(in) :: ngovf
!    !
!    !.. Formal Real Arguments ..
!    real(dp),                         intent(in) :: gamreff
!    real(dp), dimension(1:nqf),       intent(in) :: q
!    real(dp), dimension(1:nqf,1:nqf), intent(in) :: dfdp
!    !
!    ! .. Function returned argument ..
!    real(dp), dimension(1:nqf,1:nqf)             :: cp_dfdc
!    !
!  end function cp_dfdc
!end interface

continue

  gamref = gamma
  ngov = 0
  ngov(2,1) = 1
  ngov(1,2) = 2
  ngov(2,3) = 5
  ngov(1,4) = 6
  ngov(2,4) = 7

  !
  ! Continuity equation and momentum equations numbers
  !
  ngov(2,1) = 1
  ngov(1,2) = 2
  ngov(2,3) = 5

  nc = ngov(2,1)
  m1 = ngov(1,2)
  m2 = ngov(1,2) + 1
  m3 = ngov(1,2) + 2
  ne = ngov(2,3)

  !
  allocate( dqd(1:nq,1:nq), stat=istat_alloc )
  if (istat_alloc /= 0) then
    write (iout,*)
    write (iout,*) "*************************************** "
    write (iout,*) "UNABLE TO ALLOCATE MEMORY FOR dqd ARRAY "
    write (iout,*) "IN SUBROUTINE dfldfsscp "
    write (iout,*) "ON PROCESSOR NO. ", lmpi_id
    write (iout,*) "*************************************** "
  end if
  !
  allocate( dfdp(1:nq,1:nq), stat=istat_alloc )
  if (istat_alloc /= 0) then
    write (iout,*)
    write (iout,*) "**************************************** "
    write (iout,*) "UNABLE TO ALLOCATE MEMORY FOR dfdp ARRAY "
    write (iout,*) "IN SUBROUTINE dfldfsscp "
    write (iout,*) "ON PROCESSOR NO. ", lmpi_id
    write (iout,*) "**************************************** "
  end if

  !
  if (itrbmd /= itlmnr) then
    nk = ngov(1,4)
    nl = ngov(2,4)
    if (ngov(3,4) >= 2) then
      tkesw = 1.0_dp
    else
      tkesw = 0.0_dp
    end if
  else
    nk = ngov(2,1)
    nl = ngov(2,1)
    tkesw = 0.0_dp
  end if
  !
  gamogm1 = gamref/(gamref-1.0_dp)
  !
  dqd        = 0.0_dp
  do l=1,nq
    dqd(l,l) = 1.0_dp
  end do

  btfunct   = 0.0_dp
  xmfunct   = 0.0_dp
  btlrxmc   = 0.0_dp
  dubmld    = 0.0_dp ; dald      = 0.0_dp
  dhtotld   = 0.0_dp ; drmbld    = 0.0_dp
  dalld     = 0.0_dp ; dbtld     = 0.0_dp
  dxmmld    = 0.0_dp ; dxmcld    = 0.0_dp
  dubmd     = 0.0_dp ; dppld     = 0.0_dp
  dahalfd   = 0.0_dp ; dxmhalfd  = 0.0_dp
  dxmcd     = 0.0_dp ; ddelpd    = 0.0_dp
  dpsumd    = 0.0_dp ; dxmfunctd = 0.0_dp
  dbtfunctd = 0.0_dp ; dfactd    = 0.0_dp
  dpnetd    = 0.0_dp ; dubprd    = 0.0_dp
  dard      = 0.0_dp ; dhtotrd   = 0.0_dp
  drmbrd    = 0.0_dp ; dalrd     = 0.0_dp
  dbtrd     = 0.0_dp ; dxmmrd    = 0.0_dp
  dxmcrd    = 0.0_dp ; dubpd     = 0.0_dp
  dpprd     = 0.0_dp ; dbtlrxmcd = 0.0_dp
  dxmclmd   = 0.0_dp ; dxmcrpd   = 0.0_dp
  !
  ! Compute the continuity, momentum and energy flux derivatives
  ! w.r.t. rho,v,w,u,P,k,omega.....
  !
  do nf = 1,nfaces
    !
    ! Cell face area
    !
    grads  = sqrt(s(nf,1)*s(nf,1)+s(nf,2)*s(nf,2)+s(nf,3)*s(nf,3))
    grdsnz = max(grads,rndoff)
    grdsrt = grads / grdsnz
    !
    snorm(1:3) = s(nf,1:3)*grdsrt/grdsnz
    !
    ! Contravariant Mach numbers, speeds of sound, enthalpy
    !
    ! Variables dependant on the Left(-) state
    !
    ubml  = qm(nf,m1)*snorm(1) + qm(nf,m2)*snorm(2) + qm(nf,m3)*snorm(3)
    al    = sqrt(gamref*qm(nf,ne)/qm(nf,nc))
    htotl = gamogm1*qm(nf,ne)/qm(nf,nc) + tkesw*qm(nf,nk) + &
       0.5_dp*(qm(nf,m1)*qm(nf,m1) + qm(nf,m2)*qm(nf,m2) + qm(nf,m3)*qm(nf,m3))
    !
    ! Right(+) state dependant variables
    ! Variables dependant on the Right(+) state
    !
    ubpr  = qp(nf,m1)*snorm(1) + qp(nf,m2)*snorm(2) + qp(nf,m3)*snorm(3)
    ar    = sqrt(gamref*qp(nf,ne)/qp(nf,nc))
    htotr = gamogm1*qp(nf,ne)/qp(nf,nc) + tkesw*qp(nf,nk) + &
       0.5_dp*(qp(nf,m1)*qp(nf,m1) + qp(nf,m2)*qp(nf,m2) + qp(nf,m3)*qp(nf,m3))
    !
    ahalf = 0.5_dp*(al+ar)
    !
    rmbl = ubml / ahalf
    rmbr = ubpr / ahalf
    !
    ! Split Mach numbers
    !
    all = 0.5_dp*(1.0_dp + sign(1.0_dp, rmbl))
    alr = 0.5_dp*(1.0_dp - sign(1.0_dp, rmbr))
    !
    btl = -max(0.0_dp, 1.0_dp - real(int(abs(rmbl)), kind=dp))
    btr = -max(0.0_dp, 1.0_dp - real(int(abs(rmbr)), kind=dp))
    !
    xmml =  0.25_dp*(rmbl + 1.0_dp)**2
    xmmr = -0.25_dp*(rmbr - 1.0_dp)**2
    !
    ! Variables dependent on both states
    !
    delp = qm(nf,ne) - qp(nf,ne)
    psum = qm(nf,ne) + qp(nf,ne)
    !
    xmcl = 1.0_dp - (delp/psum + 2.0_dp*abs(delp)/qm(nf,ne))
    xmcr = 1.0_dp + (delp/psum - 2.0_dp*abs(delp)/qp(nf,ne))
    !
    xmhalf = sqrt(0.5_dp*(rmbl**2 + rmbr**2))
    !
    xmc = 0.25_dp*btl*btr*(xmhalf - 1.0_dp)**2
    !
    ! Allow vanLeer like or LDFSS to be used
    !
    if (abs(wldfss) > small) then
      !
      ! vanLeer like scheme
      !
      alh = al
      arh = ar
      !
      xmc = (1.0_dp - abs(wldfss)) * xmc
      !
      xmclm = xmc * xmcl
      xmcrp = xmc * xmcr
      !
      fact = 0.0_dp
      !
    else
      !
      ! LDFSS scheme
      !
      alh = ahalf
      arh = ahalf
      !
      btlrxmc = btl*btr*xmc
      !
      xmclm = btlrxmc * xmcl
      xmcrp = btlrxmc * xmcr
      !
      ! ---- newer sonic rarefaction fix
      ! ---- for older way, set XMFUNCT = 1.0_dp
      !
      xmfunct = sin(0.5_dp*pi*min(xmhalf, 1.0_dp))
      btfunct = 0.25_dp*(rmbl - rmbr - abs(rmbl - rmbr))
      fact    = max(-0.5_dp, btfunct) * xmfunct
      !
    end if
    !
    ubm = all*(1.0_dp + btl)*rmbl - btl*xmml - xmclm - fact
    ubp = alr*(1.0_dp + btr)*rmbr - btr*xmmr + xmcrp + fact
    !
    ! Pressure splitting for the momentum eq.s
    !
    ppl = 0.25_dp * (rmbl + 1.0_dp)**2 * (2.0_dp - rmbl)
    ppr = 0.25_dp * (rmbr - 1.0_dp)**2 * (2.0_dp + rmbr)
    !pnet = (all*(1.0_dp + btl) - btl*ppl)*qm(nf,ne) + &
    !       (alr*(1.0_dp + btr) - btr*ppr)*qp(nf,ne)
    !
    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    !
    ! Linearization of F with respect to qm(primitive)
    !
    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    !
    ! Initialize the left state variables
    !
    dfdp = 0.0_dp
    q(:) = qm(nf,:)
    !
    ! Flux Jacobian based on derivatives wrt the left state
    !
    ! Derivatives wrt q(prim), where q(prim) = rho,v,w,u,P and &
    ! possibly mu_tilde or k & omega
    !
    dubmld(nc:nq) = dqd(m1,nc:nq)*snorm(1) + dqd(m2,nc:nq)*snorm(2) + &
                    dqd(m3,nc:nq)*snorm(3)
    dubprd(nc:nq) = 0.0_dp
    !
    dald(nc:nq) = 0.5_dp/al * &
                  gamref*(dqd(ne,nc:nq)*q(nc) - q(ne)*dqd(nc,nc:nq))/q(nc)**2
    dard(nc:nq) = 0.0_dp
    !
    dhtotld(nc:nq) =                                                    &
      gamogm1*(dqd(ne,nc:nq)*q(nc) - q(ne)*dqd(nc,nc:nq))/q(nc)**2 +    &
      q(m1)*dqd(m1,nc:nq) + q(m2)*dqd(m2,nc:nq) + q(m3)*dqd(m3,nc:nq) + &
      tkesw*dqd(nk,nc:nq)
    !
    dhtotrd(nc:nq) = 0.0_dp
    !
    dahalfd(nc:nq) = 0.5_dp*(dald(nc:nq) + dard(nc:nq))
    !
    drmbld(nc:nq) = (dubmld(nc:nq)*ahalf - ubml*dahalfd(nc:nq))/ahalf**2
    drmbrd(nc:nq) = (dubprd(nc:nq)*ahalf - ubpr*dahalfd(nc:nq))/ahalf**2
    !
    dalld(nc:nq)  = 0.0_dp
    dalrd(nc:nq)  = 0.0_dp
    !
    dbtld(nc:nq)  = 0.0_dp
    dbtrd(nc:nq)  = 0.0_dp
    !
    dxmmld(nc:nq) =  0.5_dp*(rmbl + 1.0_dp)*drmbld(nc:nq)
    dxmmrd(nc:nq) = -0.5_dp*(rmbr - 1.0_dp)*drmbrd(nc:nq)
    !
    ddelpd(nc:nq) = dqd(ne,nc:nq)
    dpsumd(nc:nq) = dqd(ne,nc:nq)
    !
    ! Derivatives of xmcl and xmcr
    !
    if (delp >= 0.0_dp) then
      dxmcld(nc:nq) = -((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 + &
                    2.0_dp*(ddelpd(nc:nq)*q(ne) - delp*dqd(ne,nc:nq))/q(ne)**2)
      dxmcrd(nc:nq) = ((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 - &
                    2.0_dp*ddelpd(nc:nq)/qp(nf,ne))
    else
      dxmcld(nc:nq) = -((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 - &
                    2.0_dp*(ddelpd(nc:nq)*q(ne) - delp*dqd(ne,nc:nq))/q(ne)**2)
      dxmcrd(nc:nq) = ((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 + &
                    2.0_dp*ddelpd(nc:nq)/qp(nf,ne))
    end if
    !
    ! Prevent divide by zero in quotient rule when xmhalf is zero
    ! and use lhopitals rule to get correct derivative
    !
    if (xmhalf > 0.0_dp) then
      dxmhalfd(nc:nq) = 0.5_dp*(rmbl*drmbld(nc:nq) + rmbr*drmbrd(nc:nq))/xmhalf
    else
      dxmhalfd(nc:nq) = 0.0_dp
    end if
    !
    dxmcd(nc:nq) = 0.25_dp*(xmhalf - 1.0_dp)**2 *                   &
                            (dbtld(nc:nq)*btr + btl*dbtrd(nc:nq)) + &
                   0.5_dp*btl*btr*(xmhalf - 1.0_dp)*dxmhalfd(nc:nq)
    !
    if (abs(wldfss) > small) then
      !
      ! Derivative terms for the vanLeer like scheme
      !
      dalhd(nc:nq) = dald(nc:nq)
      darhd(nc:nq) = dard(nc:nq)
      !
      dxmcd(nc:nq) = (1.0_dp-abs(wldfss)) * dxmcd(nc:nq)
      !
      dxmclmd(nc:nq) = dxmcd(nc:nq)*xmcl + xmc*dxmcld(nc:nq)
      dxmcrpd(nc:nq) = dxmcd(nc:nq)*xmcr + xmc*dxmcrd(nc:nq)
      !
      dfactd(nc:nq) = 0.0_dp
      !
    else
      !
      ! Derivative terms for the LDFSS scheme
      !
      dalhd(nc:nq) = dahalfd(nc:nq)
      darhd(nc:nq) = dahalfd(nc:nq)
      !
      dbtlrxmcd(nc:nq) = (dbtld(nc:nq)*btr + btl*dbtrd(nc:nq))*xmc + &
                         btl*btr*dxmcd(nc:nq)
      !
      dxmclmd(nc:nq) = dbtlrxmcd(nc:nq)*xmcl + btlrxmc*dxmcld(nc:nq)
      dxmcrpd(nc:nq) = dbtlrxmcd(nc:nq)*xmcr + btlrxmc*dxmcrd(nc:nq)
      !
      if (xmhalf < 1.0_dp) then
        dxmfunctd(nc:nq) = cos(0.5_dp*pi*xmhalf) * 0.5_dp*pi*dxmhalfd(nc:nq)
      else
        dxmfunctd(nc:nq) = 0.0_dp
      end if
      !
      if ((rmbl-rmbr) >= 0.0_dp) then
        dbtfunctd(nc:nq) = 0.0_dp
      else
        dbtfunctd(nc:nq) = 0.5_dp * (drmbld(nc:nq) - drmbrd(nc:nq))
      end if
      !
      if (btfunct > -0.5_dp) then
        dfactd(nc:nq) = dbtfunctd(nc:nq)*xmfunct + btfunct*dxmfunctd(nc:nq)
      else
        dfactd(nc:nq) = -0.5_dp*dxmfunctd(nc:nq)
      end if
      !
    end if
    !
    ! Derivatives of the interface mass flux
    !
    dubmd(nc:nq) = dbtld(nc:nq)*all*rmbl +                                  &
                   (1.0_dp + btl)*(dalld(nc:nq)*rmbl + all*drmbld(nc:nq)) - &
                   (dbtld(nc:nq)*xmml + btl*dxmmld(nc:nq)) -                &
                   dxmclmd(nc:nq) - dfactd(nc:nq)
    !dubmd(nc:nq) = (dubmd(nc:nq)*q(nc)*ahalf + &
    !                ubm*(dqd(nc,nc:nq)*ahalf + q(nc)*dahalfd(nc:nq)))*grads
    dubmd(nc:nq) = (dubmd(nc:nq)*q(nc)*alh + &
                    ubm*(dqd(nc,nc:nq)*alh + q(nc)*dalhd(nc:nq)))*grads
    !
    dubpd(nc:nq) = dbtrd(nc:nq)*alr*rmbr +                                  &
                   (1.0_dp + btr)*(dalrd(nc:nq)*rmbr + alr*drmbrd(nc:nq)) - &
                   (dbtrd(nc:nq)*xmmr + btr*dxmmrd(nc:nq)) +                &
                   dxmcrpd(nc:nq) + dfactd(nc:nq)
    !dubpd(nc:nq) = (dubpd(nc:nq)*qp(nf,nc)*ahalf + &
    !                ubp*(qp(nf,nc)*dahalfd(nc:nq)))*grads
    dubpd(nc:nq) = (dubpd(nc:nq)*qp(nf,nc)*arh + &
                    ubp*(qp(nf,nc)*darhd(nc:nq)))*grads
    !
    ! Derivatives of the pressure splitting for the momentum eq.s
    !
    dppld(nc:nq) = 0.50_dp*(rmbl + 1.0_dp)*drmbld(nc:nq)*(2.0_dp - rmbl) - &
                   0.25_dp*(rmbl + 1.0_dp)**2*drmbld(nc:nq)
    !
    dpprd(nc:nq) = 0.50_dp*(rmbr - 1.0_dp)*drmbrd(nc:nq)*(2.0_dp + rmbr) + &
                   0.25_dp*(rmbr - 1.0_dp)**2*drmbrd(nc:nq)
    !
    dpnetd(nc:nq) = (all*(1.0_dp + btl) - btl*ppl)*dqd(ne,nc:nq) +    &
                    (dalld(nc:nq)*(1.0_dp + btl) + all*dbtld(nc:nq) - &
                     (dbtld(nc:nq)*ppl + btl*dppld(nc:nq)))*q(ne) +   &
                    (dalrd(nc:nq)*(1.0_dp + btr) + alr*dbtrd(nc:nq) - &
                     (dbtrd(nc:nq)*ppr + btr*dpprd(nc:nq)))*qp(nf,ne)
    !
    ! Continuity, momentum, and energy equation flux Jacobians
    !
    ubmmf = ubm * q(nc)*ahalf * grads
    !
    dfdp(nc,nc:nq) = dubmd(nc:nq) + dubpd(nc:nq)
    !
    dfdp(m1,nc:nq) = dubmd(nc:nq)*q(m1) + ubmmf*dqd(m1,nc:nq) + &
                     dubpd(nc:nq)*qp(nf,m1) + dpnetd(nc:nq)*s(nf,1)
    !
    dfdp(m2,nc:nq) = dubmd(nc:nq)*q(m2) + ubmmf*dqd(m2,nc:nq) + &
                     dubpd(nc:nq)*qp(nf,m2) + dpnetd(nc:nq)*s(nf,2)
    !
    dfdp(m3,nc:nq) = dubmd(nc:nq)*q(m3) + ubmmf*dqd(m3,nc:nq) + &
                     dubpd(nc:nq)*qp(nf,m3) + dpnetd(nc:nq)*s(nf,3)
    !
    dfdp(ne,nc:nq) = dubmd(nc:nq)*htotl + ubmmf*dhtotld(nc:nq) + &
                     dubpd(nc:nq)*htotr
    !
    ! Turbulence flux Jacobians
    !
    if (ngov(3,4) > 0) then
      dfdp(nk,nc:nq) = dubmd(nc:nq)*q(nk) + ubmmf*dqd(nk,nc:nq) + &
                       dubpd(nc:nq)*qp(nf,nk)
      if (ngov(3,4) == 2) then
        dfdp(nl,nc:nq) = dubmd(nc:nq)*q(nl) + ubmmf*dqd(nl,nc:nq) + &
                         dubpd(nc:nq)*qp(nf,nl)
      end if
    end if
    !
    ! Transform the flux Jacobian wrt primitive variables to the
    ! the flux Jacobian wrt conserved variables and
    ! multiply it by the cell face area
    !
    dfdqm(nf,:,:) = cp_dfdc(ngov,nq,gamref,q(:),dfdp(:,:))
    !
    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    !
    ! Linearization of F with respect to qp(primitive)
    !
    !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!
    !
    ! Initialize the right state variables
    !
    dfdp = 0.0_dp
    q(:) = qp(nf,:)
    !
    ! Flux Jacobian based on derivatives wrt the right state
    !
    ! Derivatives wrt q(prim), where q(prim) = rho,v,w,u,P and &
    ! possibly mu_tilde or k & omega
    !
    dubmld(nc:nq) = 0.0_dp
    dubprd(nc:nq) = dqd(m1,nc:nq)*snorm(1) + dqd(m2,nc:nq)*snorm(2) + &
                    dqd(m3,nc:nq)*snorm(3)
    !
    dald(nc:nq) = 0.0_dp
    dard(nc:nq) = 0.5_dp/ar * &
                  gamref*(dqd(ne,nc:nq)*q(nc) - q(ne)*dqd(nc,nc:nq))/q(nc)**2
    !
    dhtotld(nc:nq) = 0.0_dp
    !
    dhtotrd(nc:nq) =                                                    &
      gamogm1*(dqd(ne,nc:nq)*q(nc) - q(ne)*dqd(nc,nc:nq))/q(nc)**2 +    &
      q(m1)*dqd(m1,nc:nq) + q(m2)*dqd(m2,nc:nq) + q(m3)*dqd(m3,nc:nq) + &
      tkesw*dqd(nk,nc:nq)
    !
    dahalfd(nc:nq) = 0.5_dp*(dald(nc:nq) + dard(nc:nq))
    !
    drmbld(nc:nq) = (dubmld(nc:nq)*ahalf - ubml*dahalfd(nc:nq))/ahalf**2
    drmbrd(nc:nq) = (dubprd(nc:nq)*ahalf - ubpr*dahalfd(nc:nq))/ahalf**2
    !
    dalld(nc:nq)  = 0.0_dp
    dalrd(nc:nq)  = 0.0_dp
    !
    dbtld(nc:nq)  = 0.0_dp
    dbtrd(nc:nq)  = 0.0_dp
    !
    dxmmld(nc:nq) =  0.5_dp*(rmbl + 1.0_dp)*drmbld(nc:nq)
    dxmmrd(nc:nq) = -0.5_dp*(rmbr - 1.0_dp)*drmbrd(nc:nq)
    !
    ddelpd(nc:nq) = -dqd(ne,nc:nq)
    dpsumd(nc:nq) =  dqd(ne,nc:nq)
    !
    ! Derivatives of xmcl and xmcr
    !
    if (delp >= 0.0_dp) then
      dxmcld(nc:nq) = -((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 + &
                    2.0_dp*ddelpd(nc:nq)/qm(nf,ne))
      dxmcrd(nc:nq) = ((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 - &
                    2.0_dp*(ddelpd(nc:nq)*q(ne) - delp*dqd(ne,nc:nq))/q(ne)**2)
    else
      dxmcld(nc:nq) = -((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 - &
                    2.0_dp*ddelpd(nc:nq)/qm(nf,ne))
      dxmcrd(nc:nq) = ((ddelpd(nc:nq)*psum - delp*dpsumd(nc:nq))/psum**2 + &
                    2.0_dp*(ddelpd(nc:nq)*q(ne) - delp*dqd(ne,nc:nq))/q(ne)**2)
    end if
    !
    ! Prevent divide by zero in quotient rule when xmhalf is zero
    ! and use lhopitals rule to get correct derivative
    !
    if (xmhalf > 0.0_dp) then
      dxmhalfd(nc:nq) = 0.5_dp*(rmbl*drmbld(nc:nq) + rmbr*drmbrd(nc:nq))/xmhalf
    else
      dxmhalfd(nc:nq) = 0.0_dp
    end if
    !
    dxmcd(nc:nq) = 0.25_dp*(xmhalf - 1.0_dp)**2 *                   &
                            (dbtld(nc:nq)*btr + btl*dbtrd(nc:nq)) + &
                   0.5_dp*btl*btr*(xmhalf - 1.0_dp)*dxmhalfd(nc:nq)
    !
    if (abs(wldfss) > small) then
      !
      ! Derivative terms for the vanLeer like scheme
      !
      dalhd(nc:nq) = dald(nc:nq)
      darhd(nc:nq) = dard(nc:nq)
      !
      dxmcd(nc:nq) = (1.0_dp-abs(wldfss)) * dxmcd(nc:nq)
      !
      dxmclmd(nc:nq) = dxmcd(nc:nq)*xmcl + xmc*dxmcld(nc:nq)
      dxmcrpd(nc:nq) = dxmcd(nc:nq)*xmcr + xmc*dxmcrd(nc:nq)
      !
      dfactd(nc:nq) = 0.0_dp
      !
    else
      !
      ! Derivative terms for the LDFSS scheme
      !
      dalhd(nc:nq) = dahalfd(nc:nq)
      darhd(nc:nq) = dahalfd(nc:nq)
      !
      dbtlrxmcd(nc:nq) = (dbtld(nc:nq)*btr + btl*dbtrd(nc:nq))*xmc + &
                         btl*btr*dxmcd(nc:nq)
      !
      dxmclmd(nc:nq) = dbtlrxmcd(nc:nq)*xmcl + btlrxmc*dxmcld(nc:nq)
      dxmcrpd(nc:nq) = dbtlrxmcd(nc:nq)*xmcr + btlrxmc*dxmcrd(nc:nq)
      !
      if (xmhalf < 1.0_dp) then
        dxmfunctd(nc:nq) = cos(0.5_dp*pi*xmhalf) * 0.5_dp*pi*dxmhalfd(nc:nq)
      else
        dxmfunctd(nc:nq) = 0.0_dp
      end if
      !
      if ((rmbl-rmbr) >= 0.0_dp) then
        dbtfunctd(nc:nq) = 0.0_dp
      else
        dbtfunctd(nc:nq) = 0.5_dp * (drmbld(nc:nq) - drmbrd(nc:nq))
      end if
      !
      if (btfunct > -0.5_dp) then
        dfactd(nc:nq) = dbtfunctd(nc:nq)*xmfunct + btfunct*dxmfunctd(nc:nq)
      else
        dfactd(nc:nq) = -0.5_dp*dxmfunctd(nc:nq)
      end if
      !
    end if
    !
    ! Derivatives of the interface mass flux
    !
    dubmd(nc:nq) = dbtld(nc:nq)*all*rmbl +                                  &
                   (1.0_dp + btl)*(dalld(nc:nq)*rmbl + all*drmbld(nc:nq)) - &
                   (dbtld(nc:nq)*xmml + btl*dxmmld(nc:nq)) -                &
                   dxmclmd(nc:nq) - dfactd(nc:nq)
    !dubmd(nc:nq) = (dubmd(nc:nq)*qm(nf,nc)*ahalf + &
    !                ubm*(qm(nf,nc)*dahalfd(nc:nq)))*grads
    dubmd(nc:nq) = (dubmd(nc:nq)*qm(nf,nc)*alh + &
                    ubm*(qm(nf,nc)*dalhd(nc:nq)))*grads
    !
    dubpd(nc:nq) = dbtrd(nc:nq)*alr*rmbr +                                  &
                   (1.0_dp + btr)*(dalrd(nc:nq)*rmbr + alr*drmbrd(nc:nq)) - &
                   (dbtrd(nc:nq)*xmmr + btr*dxmmrd(nc:nq)) +                &
                   dxmcrpd(nc:nq) + dfactd(nc:nq)
    !dubpd(nc:nq) = (dubpd(nc:nq)*q(nc)*ahalf + &
    !                ubp*(dqd(nc,nc:nq)*ahalf + q(nc)*dahalfd(nc:nq)))*grads
    dubpd(nc:nq) = (dubpd(nc:nq)*q(nc)*arh + &
                    ubp*(dqd(nc,nc:nq)*arh + q(nc)*darhd(nc:nq)))*grads
    !
    ! Derivatives of the pressure splitting for the momentum eq.s
    !
    dppld(nc:nq) = 0.50_dp*(rmbl + 1.0_dp)*drmbld(nc:nq)*(2.0_dp - rmbl) - &
                   0.25_dp*(rmbl + 1.0_dp)**2*drmbld(nc:nq)
    !
    dpprd(nc:nq) = 0.50_dp*(rmbr - 1.0_dp)*drmbrd(nc:nq)*(2.0_dp + rmbr) + &
                   0.25_dp*(rmbr - 1.0_dp)**2*drmbrd(nc:nq)
    !
    dpnetd(nc:nq) = (dalld(nc:nq)*(1.0_dp + btl) + all*dbtld(nc:nq) -   &
                     (dbtld(nc:nq)*ppl + btl*dppld(nc:nq)))*qm(nf,ne) + &
                    (alr*(1.0_dp + btr) - btr*ppr)*dqd(ne,nc:nq) +      &
                    (dalrd(nc:nq)*(1.0_dp + btr) + alr*dbtrd(nc:nq) -   &
                     (dbtrd(nc:nq)*ppr + btr*dpprd(nc:nq)))*q(ne)
    !
    ! Continuity, momentum, and energy equation flux Jacobians
    !
    ubpmf = ubp * q(nc)*ahalf * grads
    !
    dfdp(nc,nc:nq) = dubmd(nc:nq) + dubpd(nc:nq)
    !
    dfdp(m1,nc:nq) = dubmd(nc:nq)*qm(nf,m1) + dubpd(nc:nq)*q(m1) + &
                     ubpmf*dqd(m1,nc:nq) + dpnetd(nc:nq)*s(nf,1)
    !
    dfdp(m2,nc:nq) = dubmd(nc:nq)*qm(nf,m2) + dubpd(nc:nq)*q(m2) + &
                     ubpmf*dqd(m2,nc:nq) + dpnetd(nc:nq)*s(nf,2)
    !
    dfdp(m3,nc:nq) = dubmd(nc:nq)*qm(nf,m3) + dubpd(nc:nq)*q(m3) + &
                     ubpmf*dqd(m3,nc:nq) + dpnetd(nc:nq)*s(nf,3)
    !
    dfdp(ne,nc:nq) = dubmd(nc:nq)*htotl + dubpd(nc:nq)*htotr + &
                     ubpmf*dhtotrd(nc:nq)
    !
    ! Turbulence flux Jacobians
    !
    if (ngov(3,4) > 0) then
      dfdp(nk,nc:nq) = dubmd(nc:nq)*qm(nf,nk) + dubpd(nc:nq)*q(nk) + &
                       ubpmf*dqd(nk,nc:nq)
      if (ngov(3,4) == 2) then
        dfdp(nl,nc:nq) = dubmd(nc:nq)*qm(nf,nl) + dubpd(nc:nq)*q(nl) + &
                         ubpmf*dqd(nl,nc:nq)
      end if
    end if
    !
    ! Transform the flux Jacobian wrt primitive variables to the
    ! the flux Jacobian wrt conserved variables and
    ! multiply it by the cell face area
    !
    dfdqp(nf,:,:) = cp_dfdc(ngov,nq,gamref,q(:),dfdp(:,:))
    !
  end do
  !
  deallocate (dqd, dfdp)
  !
  ! Finished computing the inviscid flux Jacobian of a calorically
  ! perfect gas using the LDFSS scheme
  !
end subroutine dfldfsscp

pure function cp_dfdc(ngovf,nqf,gamreff,q,dfdp)
  !
  ! Routine to transform the flux Jacobian matrix wrt primitive variables
  ! to the flux Jacobian matrix wrt conserved variables
  ! for a calorically perfect gas
  !
  ! Conserved variables [rho_1 -> rho_ncs, rho,rhov,rhow,rhou,rhoE,rhok,rhol]
  ! Primitive variables [  f_1 ->   f_ncs, rho,   v,   w,   u,   p,   k,   l]
  !
  ! NGOVF  : equation number control parameter
  ! NQF    : number of dependent variables
  ! GAMREFF: reference gamma
  ! Q      : flow variables (conserved)
  ! DPDC   : Jacobian matrix of conserved w.r.t. primitive
  !
  implicit none
  !
  !.. Formal Integer Arguments ..
  integer,                           intent(in) :: nqf
  integer, dimension(7,5),           intent(in) :: ngovf
  !
  !.. Formal Real Arguments ..
  real(dp),                         intent(in) :: gamreff
  real(dp), dimension(1:nqf),       intent(in) :: q
  real(dp), dimension(1:nqf,1:nqf), intent(in) :: dfdp
  !
  ! .. Function returned argument ..
  real(dp), dimension(1:nqf,1:nqf)             :: cp_dfdc
  !
  !.. Local Arrays ..
  real, dimension(1:nqf,1:nqf) :: dpdc
  !
  !.. Local Scalars ..
  integer   :: l,nc,m1,m2,m3,ne,nk,nl
  !
  real(dp) :: rhoi,u,v,w,gamm1
  !
  !
  ! Eq. numbers
  !
  nc = ngovf(2,1)
  m1 = ngovf(1,2)
  m2 = ngovf(1,2) + 1
  m3 = ngovf(1,2) + 2
  ne = ngovf(2,3)
  !
  nk = ngovf(1,4)
  nl = ngovf(2,4)
  !
  ! Initialize the transformation Jacobian
  !
  dpdc = 0.0_dp
  !
  rhoi = 1.0_dp/q(nc)
  !
  ! Initialize all the diagonal elements to the reciprocal of density
  !
  do l = 1,nqf
    dpdc(l,l) = rhoi
  end do
  !
  gamm1 = gamreff-1.0_dp
  !
  u = q(m3)
  v = q(m1)
  w = q(m2)
  !
  ! Set the density wrt density term
  !
  dpdc(nc,nc) = 1.0_dp
  !
  ! Compute the velocity wrt density terms
  !
  dpdc(m1,nc) = -v*rhoi
  dpdc(m2,nc) = -w*rhoi
  dpdc(m3,nc) = -u*rhoi
  !
  ! Compute the pressure wrt density term
  !
  dpdc(ne,nc) = 0.5_dp*(u*u+v*v+w*w)*gamm1
  !
  ! Compute the pressure wrt momentum terms
  !
  dpdc(ne,m1) = -v*gamm1
  dpdc(ne,m2) = -w*gamm1
  dpdc(ne,m3) = -u*gamm1
  !
  ! Compute the pressure wrt energy term
  !
  dpdc(ne,ne) = gamm1
  !
  ! Compute all the terms involving turbulence quantities
  !
  if (ngovf(3,4) == 1) then
    dpdc(nl,nc) = -q(nl)*rhoi
  else if (ngovf(3,4) == 2) then
    dpdc(ne,nk) = -gamm1
    dpdc(nk,nc) = -q(nk)*rhoi
    dpdc(nl,nc) = -q(nl)*rhoi
  end if
  !
  ! Perform the matrix matrix multiply to transform dfdp to dfdc
  !
  cp_dfdc = MATMUL(dfdp,dpdc)
  !
  ! End of routine to transform a flux Jacobian matrix wrt primitive variables
  ! to a flux Jacobian matrix wrt conserved variables
  ! for a calorically perfect gas
  !
end function cp_dfdc

  include 'dfroe_i.f90'
  include 'dfduc3_i.f90'
  include 'viscosity_law.f90'
  include 'pressure_limiter.f90'
  include 'pswitch.f90'
  include 'vswch_coef.f90'
  include 'vswch_coef_orig.f90'
  include 'ebv_tet_jac.f90'

end module jacobian

!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite allocations

test SmallMemoryRequestReturnsZeroStatus
  integer :: n, ierr
  real, dimension(:,:,:,:), allocatable :: x
  n = 10
  allocate(x(n,n,n,n),stat=ierr)
  assert_equal( 0, ierr )
  x = 0.0
end test

!!$test RidiculousMemoryRequestProducesErrorMessage
!!$  integer :: n, ierr
!!$  real, dimension(:,:,:,:), allocatable :: x
!!$  n = huge(n)
!!$  allocate(x(n,n,n,n),stat=ierr)
!!$  assert_equal( 1, ierr )
!!$  x = 0.0
!!$end test

end test_suite

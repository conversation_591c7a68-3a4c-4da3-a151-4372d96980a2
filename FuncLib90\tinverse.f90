!=================================== TINVERSE ================================80
!
! Compute inverse of 3x3 transformation matrix (also available in utilities)
!
!=============================================================================80

  pure function tinverse( t )

    real(dp), dimension(3,3), intent(in)  :: t
    real(dp), dimension(3,3)  :: tinverse

    real(dp) :: deti

    continue

    !...find inverse elements of transformation matrix
    deti =  1.0_dp / ( t(1,1)*( t(2,2)*t(3,3) - t(3,2)*t(2,3) )             &
                     + t(2,1)*( t(3,2)*t(1,3) - t(1,2)*t(3,3) )             &
                     + t(3,1)*( t(1,2)*t(2,3) - t(2,2)*t(1,3) ) )

    tinverse(1,1) =  deti*( t(2,2)*t(3,3) - t(3,2)*t(2,3) )
    tinverse(1,2) =  deti*( t(3,2)*t(1,3) - t(1,2)*t(3,3) )
    tinverse(1,3) =  deti*( t(1,2)*t(2,3) - t(2,2)*t(1,3) )

    tinverse(2,1) = -deti*( t(2,1)*t(3,3) - t(3,1)*t(2,3) )
    tinverse(2,2) = -deti*( t(3,1)*t(1,3) - t(1,1)*t(3,3) )
    tinverse(2,3) = -deti*( t(1,1)*t(2,3) - t(2,1)*t(1,3) )

    tinverse(3,1) =  deti*( t(2,1)*t(3,2) - t(3,1)*t(2,2) )
    tinverse(3,2) =  deti*( t(3,1)*t(1,2) - t(1,1)*t(3,2) )
    tinverse(3,3) =  deti*( t(1,1)*t(2,2) - t(2,1)*t(1,2) )

  end function tinverse

module bc_names

  use kinddefs, only : dp

  implicit none

  private

  public :: bc_dirichlet
  public :: element_based_bc
  public :: element_based_bci
  public :: bc_used_for_distance_function
  public :: need_distance_function
  public :: bc_laminar_transition
  public :: need_transition
  public :: bc_used_for_force_calculation
  public :: bc_is_flow_through
  public :: bc_is_internal
  public :: bc_is_periodic
  public :: bc_is_far_field
  public :: bc_has_skin_friction
  public :: bc_massflux_explicitly_init
  public :: bc_explicitly_initialized
  public :: bc_initialized_solid
  public :: bc_unsteady
  public :: bc_viscous_strong_velocity
  public :: bc_strong_turb
  public :: bc_allowed_for_adjoint
  public :: bc_allowed_for_getgrad
  public :: bc_allowed_for_testing_sa
  public :: bc_not_allowed_for_non_inertial
  public :: bc_not_allowed_for_moving_grid
  public :: bc_is_not_incompressible
  public :: bc_is_not_perfectgas
  public :: bc_is_not_gengas
  public :: bc_is_symmetry
  public :: bc_strong_viscous_adjoint
  public :: bc_has_pressure_closure
  public :: bc_has_visc_flux_closure
  public :: bc_ignore_2d
  public :: backwards_compatible_bc
  public :: usm3d_to_fun3d_bc

  !...best practices symmetry boundary conditiions (nc and cc)
  public :: symmetry_x, symmetry_y, symmetry_z, bc_null
  !...periodicity bcs only nc
  public :: periodicity1, periodicity2, periodicity3
  public :: periodicity1_ssdc, periodicity2_ssdc, periodicity3_ssdc
  public :: block_interface
  public :: filter

  public :: symmetry_1_strong, symmetry_2_strong, symmetry_3_strong

  public :: tangency, inviscid_pseudo, inviscid_strong
  public :: viscous_solid
  public :: viscous_generic_0
  public :: viscous_generic_1, viscous_generic_2, viscous_generic_3
  public :: viscous_generic_4, viscous_generic_5, viscous_generic_6
  public :: viscous_generic_7, viscous_generic_8, viscous_generic_9
  public :: viscous_solid_trs
  public :: viscous_adiabatic_ssdc
  public :: viscous_wall_rough
  public :: viscous_wall_function
  public :: viscous_weak_wall
  public :: viscous_weak_trs
  public :: viscous_wf_trs
  public :: inviscid_porous, viscous_porous
  public :: viscous_pseudo
  public :: pback, twall
  public :: farfield_pbck, farfield_convection
  public :: farfield_shkfit
  public :: farfield_extr
  public :: farfield_riem, farfield_roe, riemann, extrapolate, tangency_weak
  public :: hover_ss
  public :: farfield_strong
  public :: subsonic_inflow_vel
  public :: subsonic_inflow_pint
  public :: subsonic_inflow_qsqint
  public :: pulsed_subsonic_inflow_rho
  public :: subsonic_inflow_pt
  public :: subsonic_inflow_pt_alt
  public :: subsonic_outflow_p0
  public :: subsonic_outflow_p1
  public :: vacuum
  public :: subsonic_outflow_surge
  public :: subsonic_inflow_pt_surge
  public :: back_pressure
  public :: subsonic_outflow_mach
  public :: totalp_totalt_version3
  public :: rcs_jet_plenum
  public :: massflow_out
  public :: viscous_bleed
  public :: bleed
  public :: massflow_in
  public :: dirichlet, dirichlet_viscous, dirichlet_discrete
  public :: dirichlet_lisbon
  public :: fixed_inflow
  public :: fixed_inflow_profile
  public :: lisbon_inflow_profile
  public :: fixed_inflow_patch
  public :: pulsed_supersonic_inflow
  public :: ramped_supersonic_inflow
  public :: fixed_outflow
  public :: solution_functional
  public :: overset_interp

  public :: bc_allow_exact_cc, bc_allow_exact_nc

  public :: bc_name_index

! WARNING FOR BC 3010:
! this is a new bc that uses the bc_gen calls to enforce a strong condition
! it will break if you have a mesh that has a node that gets called twice
! for the 3010 boundary i.e. two adjacent 3010 patches. Also should not
! be used for sharp trailing edges. It is now turned off in all paths via
! the bc_is_not_XXX logical functions

! If adding a new bc, please update function bc_name_index

! Dummy bc for overset boundaries
  integer, parameter :: overset_interp    = -1

  integer, parameter :: tangency          = 3000
  integer, parameter :: inviscid_strong   = 3010
  integer, parameter :: inviscid_porous   = 3040
  integer, parameter :: inviscid_pseudo   = 3060

  integer, parameter :: viscous_solid         =  4000

! 4001-4009 are reserved for generic gas, viscous wall boundary conditions.
! It is implicitly assumed that no single simulation would require more than
! nine different surfaces. Parameters for each surface type will be input
! through its respective namelist (generic_surface_1_nml, ...).
! Surfaces are typically described by a temperature boundary condition, and a
! species mole fraction boundary condition which in turn are functions of
! emissivity(T), catalytic efficiency(T), and ablation(p,T). Other surface
! characteristics may be added through namelist as needed.

  integer, parameter :: viscous_generic_1     =  4001
  integer, parameter :: viscous_generic_2     =  4002
  integer, parameter :: viscous_generic_3     =  4003
  integer, parameter :: viscous_generic_4     =  4004
  integer, parameter :: viscous_generic_5     =  4005
  integer, parameter :: viscous_generic_6     =  4006
  integer, parameter :: viscous_generic_7     =  4007
  integer, parameter :: viscous_generic_8     =  4008
  integer, parameter :: viscous_generic_9     =  4009
  integer, parameter :: viscous_generic_0     =  4010

  integer, parameter :: viscous_solid_trs     = -4000
  integer, parameter :: viscous_wf_trs        = -4100
  integer, parameter :: viscous_weak_trs      = -4110

  integer, parameter :: viscous_porous        =  4040
  integer, parameter :: viscous_adiabatic_ssdc = 4050
  integer, parameter :: viscous_pseudo        =  4060
  integer, parameter :: viscous_wall_rough    =  4075
  integer, parameter :: viscous_wall_function =  4100
  integer, parameter :: viscous_weak_wall     =  4110

! some of these will migrate to element based
  integer, parameter :: farfield_strong         = 5015
  integer, parameter :: farfield_convection     = 5020

! Positive Diagonal Initiative.
! Farfield boundary condition.
! For compressible flow, based on constant entropy Riemann invariants.
! For incompressible flow, based on discrete fds eigenvectors.
! Counterpart in element-based implementation is riemann.
  integer, parameter :: farfield_riem           = 5000

! Shock fitting boundary conditions
  integer, parameter :: farfield_shkfit         = 5500

! These are designed for axisymmetric use.

  integer, parameter :: symmetry_1_strong = 6021
  integer, parameter :: symmetry_2_strong = 6022
  integer, parameter :: symmetry_3_strong = 6023

! Symmetry conditions for node-centered and cell-centered paths.

  integer, parameter :: bc_null    = 6660
  integer, parameter :: symmetry_x = 6661
  integer, parameter :: symmetry_y = 6662
  integer, parameter :: symmetry_z = 6663

! Periodic boundary conditions

  integer, parameter :: periodicity1 = 6100
  integer, parameter :: periodicity2 = 6101
  integer, parameter :: periodicity3 = 6102

  integer, parameter :: periodicity1_ssdc = 6150
  integer, parameter :: periodicity2_ssdc = 6151
  integer, parameter :: periodicity3_ssdc = 6152

! Boundary between blocks

  integer, parameter :: block_interface = 6200
  integer, parameter :: filter          = 6210

  !...the first two need an exact solution.
  integer, parameter :: dirichlet          = 9999
  integer, parameter :: dirichlet_viscous  = 9998 !To allow distance function
  integer, parameter :: dirichlet_discrete = 9996 !Overspecified discrete.
  integer, parameter :: dirichlet_lisbon   = 9997 !Lisbon backstep inflow.

! slated for eventual removal once migrated

  integer, parameter :: farfield_extr           = 5005
  integer, parameter :: farfield_pbck           = 5010
  integer, parameter :: totalp_totalt_version3  = 7020

! Gather element based bcs down here
  integer, parameter :: tangency_weak              = 3005
  integer, parameter :: riemann                    = 5025
  integer, parameter :: extrapolate                = 5026
  integer, parameter :: farfield_roe               = 5050
  integer, parameter :: back_pressure              = 5051
  integer, parameter :: subsonic_outflow_mach      = 5052
  integer, parameter :: subsonic_outflow_p1        = 5053
  integer, parameter :: vacuum                     = 5054
  integer, parameter :: hover_ss                   = 5100
  integer, parameter :: subsonic_inflow_vel        = 7010
  integer, parameter :: subsonic_inflow_pt         = 7011
  integer, parameter :: subsonic_outflow_p0        = 7012
  integer, parameter :: subsonic_inflow_pint       = 7013
  integer, parameter :: subsonic_inflow_qsqint     = 7014
  integer, parameter :: subsonic_inflow_pt_alt     = 7015
  integer, parameter :: rcs_jet_plenum             = 7021


  integer, parameter :: massflow_out               = 7031
  integer, parameter :: viscous_bleed              = 7032
  integer, parameter :: bleed                      = 7033
  integer, parameter :: massflow_in                = 7036
  integer, parameter :: fixed_inflow               = 7100
  integer, parameter :: fixed_inflow_profile       = 7101
  integer, parameter :: fixed_inflow_patch         = 7102
  integer, parameter :: pulsed_supersonic_inflow   = 7103
  integer, parameter :: ramped_supersonic_inflow   = 7104
  integer, parameter :: fixed_outflow              = 7105

  integer, parameter :: pulsed_subsonic_inflow_rho = 7130
  integer, parameter :: subsonic_inflow_pt_surge   = 7131
  integer, parameter :: subsonic_outflow_surge     = 7132

  integer, parameter :: lisbon_inflow_profile      = 7150
  integer, parameter :: solution_functional        = 7200

! Extra physics info for special BCs
! This will eventually be read into the solver separately

  real(dp) :: pback           =  1.0_dp  ! P_back/P_infty

  real(dp) :: twall           = -1.0_dp  ! < 0 to get adiabatic wall temp
                                         !   (see init routine)

contains

!==================================== BC_NAME_INDEX ==========================80
!
! Two modes, based on input logical:
! Generates a character name for the various bc types
!   or
! Generates a bc_type for the various character names
!
!   Note: this must be kept up to date with the bcs defined at the top
!         of this module
!
!=============================================================================80

  subroutine bc_name_index(ibc,bc_name,integer_to_character,optionally_quiet)

    use string_utils, only : downcase

    integer,       intent(inout)           :: ibc
    character(80), intent(inout)           :: bc_name
    logical,       intent(in   )           :: integer_to_character
    logical,       intent(in   ), optional :: optionally_quiet

    logical :: verbose_error

    continue

    verbose_error = .true.
    if ( present(optionally_quiet) ) then
      if ( optionally_quiet ) then
        verbose_error = .false.
      end if
    end if

    conversion : if ( integer_to_character ) then

      select case (ibc)

!--------------------
!   Exact bc
!--------------------

        case (dirichlet)
          bc_name = 'dirichlet'
        case (dirichlet_viscous)
          bc_name = 'dirichlet_viscous'
        case (dirichlet_lisbon)
          bc_name = 'dirichlet_lisbon'
        case (dirichlet_discrete)
          bc_name = 'dirichlet_discrete'

!--------------------
!   tangency wall bcs
!--------------------

        case (tangency)
          bc_name = 'tangency'
        case (inviscid_strong)
          bc_name = 'inviscid_strong'
        case (inviscid_porous)
          bc_name = 'inviscid_porous'
        case (inviscid_pseudo)
          bc_name = 'inviscid_pseudo'

!-------------------
!   viscous wall bcs
!-------------------

        case (viscous_solid)
          bc_name = 'viscous_solid'
        case (viscous_generic_0)
          bc_name = 'viscous_generic_0'
        case (viscous_generic_1)
          bc_name = 'viscous_generic_1'
        case (viscous_generic_2)
          bc_name = 'viscous_generic_2'
        case (viscous_generic_3)
          bc_name = 'viscous_generic_3'
        case (viscous_generic_4)
          bc_name = 'viscous_generic_4'
        case (viscous_generic_5)
          bc_name = 'viscous_generic_5'
        case (viscous_generic_6)
          bc_name = 'viscous_generic_6'
        case (viscous_generic_7)
          bc_name = 'viscous_generic_7'
        case (viscous_generic_8)
          bc_name = 'viscous_generic_8'
        case (viscous_generic_9)
          bc_name = 'viscous_generic_9'
        case (viscous_solid_trs)
          bc_name = 'viscous_solid_trs'
        case (viscous_porous)
          bc_name = 'viscous_porous'
        case (viscous_adiabatic_ssdc)
          bc_name = 'viscous_adiabatic_ssdc'
        case (viscous_pseudo)
          bc_name = 'viscous_pseudo'
        case (viscous_wall_rough)
          bc_name = 'viscous_wall_rough'
        case (viscous_wall_function)
          bc_name = 'viscous_wall_function'
        case (viscous_weak_wall)
          bc_name = 'viscous_weak_wall'
        case (viscous_weak_trs)
          bc_name = 'viscous_weak_trs'
        case (viscous_wf_trs)
          bc_name = 'viscous_wf_trs'

!----------------
!   far-field bcs
!----------------

        case (farfield_riem)
          bc_name = 'farfield_riem'
        case (farfield_roe)
          bc_name = 'farfield_roe'
        case (farfield_shkfit)
          bc_name = 'farfield_shock_fit'
        case (tangency_weak)
          bc_name = 'tangency_weak'
        case (riemann)
          bc_name = 'riemann'
        case (extrapolate)
          bc_name = 'extrapolate'
        case (farfield_extr)
          bc_name = 'farfield_extr'
        case (farfield_pbck)
          bc_name = 'farfield_pbck'
        case (farfield_strong)
          bc_name = 'farfield_strong'
        case (farfield_convection)
          bc_name = 'farfield_convection'
        case (back_pressure)
          bc_name = 'back_pressure'
        case (subsonic_outflow_p1)
          bc_name = 'subsonic_outflow_p1'
        case (vacuum)
          bc_name = 'vacuum'
        case (subsonic_outflow_surge)
          bc_name = 'subsonic_outflow_surge'
        case (subsonic_inflow_pt_surge)
          bc_name = 'subsonic_inflow_pt_surge'
        case (subsonic_outflow_mach)
          bc_name = 'subsonic_outflow_mach'
        case (hover_ss)
          bc_name = 'hover_ss'

!---------------
!   symmetry bcs - node-centered
!---------------

        case (symmetry_1_strong)
          bc_name = 'symmetry_1_strong'
        case (symmetry_2_strong)
          bc_name = 'symmetry_2_strong'
        case (symmetry_3_strong)
          bc_name = 'symmetry_3_strong'

!---------------
!   symmetry bcs - best practices
!---------------

        case (symmetry_x)
          bc_name = 'symmetry_x'
        case (symmetry_y)
          bc_name = 'symmetry_y'
        case (symmetry_z)
          bc_name = 'symmetry_z'
        case (bc_null)
          bc_name = 'bc_null'

!---------------
!   periodic bcs
!---------------

        case (periodicity1)
          bc_name = 'periodicity1'
        case (periodicity2)
          bc_name = 'periodicity2'
        case (periodicity3)
          bc_name = 'periodicity3'
        case (periodicity1_ssdc)
          bc_name = 'periodicity1_ssdc'
        case (periodicity2_ssdc)
          bc_name = 'periodicity2_ssdc'
        case (periodicity3_ssdc)
          bc_name = 'periodicity3_ssdc'

!---------------
!   block interface bcs
!---------------

        case (block_interface)
          bc_name = 'block_interface'
        case (filter)
          bc_name = 'filter'

!--------------------
!   internal flow bcs
!--------------------

        case (subsonic_inflow_vel)
          bc_name = 'subsonic_inflow_vel'
        case (subsonic_inflow_pint)
          bc_name = 'subsonic_inflow_pint'
        case (subsonic_inflow_qsqint)
          bc_name = 'subsonic_inflow_qsqint'
        case (pulsed_subsonic_inflow_rho)
          bc_name = 'pulsed_subsonic_inflow_rho'
        case (subsonic_inflow_pt_alt)
          bc_name = 'subsonic_inflow_pt_alt'
        case (subsonic_inflow_pt)
          bc_name = 'subsonic_inflow_pt'
        case (subsonic_outflow_p0)
          bc_name = 'subsonic_outflow_p0'
        case (totalp_totalt_version3)
          bc_name = 'totalp_totalt_version3'
        case (rcs_jet_plenum)
          bc_name = 'rcs_jet_plenum'
        case (massflow_out)
          bc_name = 'massflow_out'
        case (viscous_bleed)
          bc_name = 'viscous_bleed'
        case (bleed)
          bc_name = 'bleed'
        case (massflow_in)
          bc_name = 'massflow_in'
        case (fixed_inflow)
          bc_name = 'fixed_inflow'
        case (fixed_inflow_profile)
          bc_name = 'fixed_inflow_profile'
        case (lisbon_inflow_profile)
          bc_name = 'lisbon_inflow_profile'
        case (fixed_inflow_patch)
          bc_name = 'fixed_inflow_patch'
        case (pulsed_supersonic_inflow)
          bc_name = 'pulsed_supersonic_inflow'
        case (ramped_supersonic_inflow)
          bc_name = 'ramped_supersonic_inflow'
        case (fixed_outflow)
          bc_name = 'fixed_outflow'
        case (solution_functional)
          bc_name = 'solution_functionl'

!---------------------------
!   overset interpolation bc
!---------------------------

        case (overset_interp)
          bc_name = 'overset_interp'

!-------------
!   unknown bc
!-------------

        case default

          bc_name = 'unknown'

          if ( verbose_error ) then
            write(*,*) 'Your data has a BC index unknown to function bc_name.'
            write(*,*) '...ibc=',ibc
            write(*,*) 'This may be an invalid BC index.'
            write(*,*) 'At a minimum, it is unknown within the module bc_names.'
          end if

      end select

    else conversion

      select case ( downcase(trim(bc_name)) )

!--------------------
!   Exact bc
!--------------------

        case ('dirichlet')
          ibc = dirichlet
        case ('dirichlet_viscous')
          ibc = dirichlet_viscous
        case ('dirichlet_lisbon')
          ibc = dirichlet_lisbon
        case ('dirichlet_discrete')
          ibc = dirichlet_discrete

!--------------------
!   tangency wall bcs
!--------------------

        case ('tangency')
          ibc = tangency
        case ('inviscid_strong')
          ibc = inviscid_strong
        case ('inviscid_porous')
          ibc = inviscid_porous
        case ('inviscid_pseudo')
          ibc = inviscid_pseudo

!-------------------
!   viscous wall bcs
!-------------------

        case ('viscous_solid')
          ibc = viscous_solid
        case ('viscous_generic_0')
          ibc = viscous_generic_0
        case ('viscous_generic_1')
          ibc = viscous_generic_1
        case ('viscous_generic_2')
          ibc = viscous_generic_2
        case ('viscous_generic_3')
          ibc = viscous_generic_3
        case ('viscous_generic_4')
          ibc = viscous_generic_4
        case ('viscous_generic_5')
          ibc = viscous_generic_5
        case ('viscous_generic_6')
          ibc = viscous_generic_6
        case ('viscous_generic_7')
          ibc = viscous_generic_7
        case ('viscous_generic_8')
          ibc = viscous_generic_8
        case ('viscous_generic_9')
          ibc = viscous_generic_9
        case ('viscous_solid_trs')
          ibc = viscous_solid_trs
        case ('viscous_porous')
          ibc = viscous_porous
        case ('viscous_adiabatic_ssdc')
          ibc = viscous_adiabatic_ssdc
        case ('viscous_pseudo')
          ibc = viscous_pseudo
        case ('viscous_wall_rough')
          ibc = viscous_wall_rough
        case ('viscous_wall_function')
          ibc = viscous_wall_function
        case ('viscous_weak_wall')
          ibc = viscous_weak_wall
        case ('viscous_weak_trs')
          ibc = viscous_weak_trs
        case ('viscous_wf_trs')
          ibc = viscous_wf_trs

!----------------
!   far-field bcs
!----------------

        case ('farfield_riem')
          ibc = farfield_riem
        case ('tangency_weak')
          ibc = tangency_weak
        case ('riemann')
          ibc = riemann
        case ('extrapolate')
          ibc = extrapolate
        case ('farfield_roe')
          ibc = farfield_roe
        case ('farfield_extr')
          ibc = farfield_extr
        case ('farfield_pbck')
          ibc = farfield_pbck
        case ('farfield_strong')
          ibc = farfield_strong
        case ('farfield_convection')
          ibc = farfield_convection
        case ('back_pressure')
          ibc = back_pressure
        case ('subsonic_outflow_p1')
          ibc = subsonic_outflow_p1
        case ('vacuum')
          ibc = vacuum
        case ('subsonic_outflow_surge')
          ibc = subsonic_outflow_surge
        case ('subsonic_inflow_pt_surge')
          ibc = subsonic_inflow_pt_surge
        case ('subsonic_outflow_mach')
          ibc = subsonic_outflow_mach
        case ('hover_ss')
          ibc = hover_ss

!---------------
!   symmetry bcs
!---------------
        case ('symmetry_1_strong')
          ibc = symmetry_1_strong
        case ('symmetry_2_strong')
          ibc = symmetry_2_strong
        case ('symmetry_3_strong')
          ibc = symmetry_3_strong
        case ('symmetry_x')
          ibc = symmetry_x
        case ('symmetry_y')
          ibc = symmetry_y
        case ('symmetry_z')
          ibc = symmetry_z
        case ('bc_null')
          ibc = bc_null

!---------------
!   periodic bcs
!---------------

        case ('periodicity1')
          ibc = periodicity1
        case ('periodicity2')
          ibc = periodicity2
        case ('periodicity3')
          ibc = periodicity3
        case ('periodicity1_ssdc')
          ibc = periodicity1_ssdc
        case ('periodicity2_ssdc')
          ibc = periodicity2_ssdc
        case ('periodicity3_ssdc')
          ibc = periodicity3_ssdc

!---------------
!   block interface bcs
!---------------

        case ('block_interface')
          ibc = block_interface
        case ('filter')
          ibc = filter

!--------------------
!   internal flow bcs
!--------------------

        case ('subsonic_inflow_vel')
          ibc = subsonic_inflow_vel
        case ('subsonic_inflow_pint')
          ibc = subsonic_inflow_pint
        case ('subsonic_inflow_qsqint')
          ibc = subsonic_inflow_qsqint
        case ('pulsed_subsonic_inflow_rho')
          ibc = pulsed_subsonic_inflow_rho
        case ('subsonic_inflow_pt_alt')
          ibc = subsonic_inflow_pt_alt
        case ('subsonic_inflow_pt')
          ibc = subsonic_inflow_pt
        case ('subsonic_outflow_p0')
          ibc = subsonic_outflow_p0
        case ('totalp_totalt_version3')
          ibc = totalp_totalt_version3
        case ('rcs_jet_plenum')
          ibc = rcs_jet_plenum
        case ('massflow_out')
          ibc = massflow_out
        case ('viscous_bleed')
          ibc = viscous_bleed
        case ('bleed')
          ibc = bleed
        case ('massflow_in')
          ibc = massflow_in
        case ('fixed_inflow')
          ibc = fixed_inflow
        case ('fixed_inflow_profile')
          ibc = fixed_inflow_profile
        case ('lisbon_inflow_profile')
          ibc = lisbon_inflow_profile
        case ('fixed_inflow_patch')
          ibc = fixed_inflow_patch
        case ('pulsed_supersonic_inflow')
          ibc = pulsed_supersonic_inflow
        case ('ramped_supersonic_inflow')
          ibc = ramped_supersonic_inflow
        case ('fixed_outflow')
          ibc = fixed_outflow
        case ('solution_functional')
          ibc = solution_functional

!---------------------------
!   overset interpolation bc
!---------------------------

        case ('overset_interp', 'OVERSET_INTERP')
          ibc = overset_interp

!-------------
!   unknown bc
!-------------

        case default

          ibc = 0000

          if ( verbose_error ) then
            write(*,*)'Your data has a BC name unknown to bc_name_index.'
            write(*,*)'...bc name=', trim(bc_name)
            write(*,*)'This may be an invalid BC name.'
            write(*,*)'At a minimum, it is unknown within the module bc_names.'
          end if

      end select

    endif conversion

  end subroutine bc_name_index


!===================== BC_USED_FOR_DISTANCE_FUNCTION =========================80
!
! logical function bc_used_for_distance_function
!
!=============================================================================80

  logical function bc_used_for_distance_function(ibc, use_all_boundaries_arg,  &
                                                 use_inviscid_boundaries_arg)

    use debug_defs, only : bp_solution
    use info_depr,  only : ivisc

    integer,           intent(in) :: ibc
    logical, optional, intent(in) :: use_all_boundaries_arg
    logical, optional, intent(in) :: use_inviscid_boundaries_arg

    logical :: use_all_boundaries, use_inviscid_boundaries

    continue

    bc_used_for_distance_function = .false. ! to provide a default for CCI

    if ( present(use_all_boundaries_arg) ) then
      use_all_boundaries = use_all_boundaries_arg
    else
      use_all_boundaries = .false.
    endif

    if ( present(use_inviscid_boundaries_arg) ) then
      use_inviscid_boundaries = use_inviscid_boundaries_arg
    else
      use_inviscid_boundaries = .false.
    endif

    if ( use_all_boundaries ) then
      bc_used_for_distance_function = .true.
    else if ( use_inviscid_boundaries ) then

      bc_used_for_distance_function = (     ibc == viscous_solid               &
                                       .or. ibc == viscous_solid_trs           &
                                       .or.(ibc >= viscous_generic_1 .and.     &
                                            ibc <= viscous_generic_0)          &
                                       .or. ibc == viscous_wall_rough          &
                                       .or. ibc == viscous_wall_function       &
                                       .or. ibc == viscous_weak_wall           &
                                       .or. ibc == viscous_weak_trs            &
                                       .or. ibc == viscous_wf_trs              &
                                       .or. ibc == dirichlet_viscous           &
                                       .or. ibc == viscous_porous              &
                                       .or. ibc == viscous_pseudo              &
                                       .or. ibc == tangency                    &
                                       .or. ibc == filter                      &
                                       .or. ibc == inviscid_strong             &
                                       .or. ibc == inviscid_porous             &
                                       .or. ibc == inviscid_pseudo )
    elseif ( .not.bp_solution ) then

      bc_used_for_distance_function = (     ibc == viscous_solid               &
                                       .or. ibc == viscous_solid_trs           &
                                       .or.(ibc >= viscous_generic_1 .and.     &
                                            ibc <= viscous_generic_0)          &
                                       .or. ibc == viscous_wall_rough          &
                                       .or. ibc == viscous_wall_function       &
                                       .or. ibc == viscous_weak_wall           &
                                       .or. ibc == viscous_weak_trs            &
                                       .or. ibc == viscous_wf_trs              &
                                       .or. ibc == dirichlet_viscous           &
                                       .or. ibc == viscous_porous              &
                                       .or. ibc == viscous_pseudo              &
                                       .or. ibc == block_interface             &
                                       .or. ibc == filter                      &
                                      )

    elseif ( ivisc > 0 ) then

      bc_used_for_distance_function = (     ibc == viscous_solid               &
                                       .or. ibc == viscous_solid_trs           &
                                       .or.(ibc >= viscous_generic_1 .and.     &
                                            ibc <= viscous_generic_0)          &
                                       .or. ibc == viscous_wall_rough          &
                                       .or. ibc == viscous_wall_function       &
                                       .or. ibc == viscous_weak_wall           &
                                       .or. ibc == viscous_weak_trs            &
                                       .or. ibc == viscous_wf_trs              &
                                       .or. ibc == dirichlet_viscous           &
                                       .or. ibc == viscous_porous              &
                                       .or. ibc == viscous_pseudo              &
                                       .or. ibc == block_interface             &
                                       .or. ibc == filter                      &
                                      )

    elseif ( ivisc == 0 ) then

      bc_used_for_distance_function = (     ibc == viscous_solid               &
                                       .or. ibc == viscous_solid_trs           &
                                       .or.(ibc >= viscous_generic_1 .and.     &
                                            ibc <= viscous_generic_0)          &
                                       .or. ibc == viscous_wall_rough          &
                                       .or. ibc == viscous_wall_function       &
                                       .or. ibc == viscous_weak_wall           &
                                       .or. ibc == viscous_weak_trs            &
                                       .or. ibc == viscous_wf_trs              &
                                       .or. ibc == dirichlet_viscous           &
                                       .or. ibc == viscous_porous              &
                                       .or. ibc == viscous_pseudo              &
                                       .or. ibc == tangency                    &
                                       .or. ibc == filter                      &
                                       .or. ibc == inviscid_strong             &
                                       .or. ibc == inviscid_porous             &
                                       .or. ibc == inviscid_pseudo )


    endif

  end function bc_used_for_distance_function

!===================== NEED_DISTANCE_FUNCTION ================================80
!
! logical function need_distance_function
!
!=============================================================================80

  logical function need_distance_function(bc)

    use bc_types, only : bcgrid_type

    type(bcgrid_type), dimension(:), intent(in) :: bc

    integer :: i

    continue

    need_distance_function = .false.

    do i = 1, size(bc)
      if (bc_used_for_distance_function(bc(i)%ibc)) then
        need_distance_function = .true.
        return
      end if
    end do

  end function need_distance_function

!===================== BC_LAMINAR_TRANSITION =================================80
!
! logical function bc_laminar_transition
!
!=============================================================================80

  logical function bc_laminar_transition(ibc,specified_transition)

    integer, intent(in) :: ibc
    logical, intent(in) :: specified_transition

    continue

    bc_laminar_transition  = ( (ibc < -1) .or. specified_transition )


  end function bc_laminar_transition

!===================== NEED_TRANSITION =======================================80
!
! logical function bc_used_for_distance_function
!
!=============================================================================80

  logical function need_transition(bc,specified_transition)

    use bc_types, only : bcgrid_type

    type(bcgrid_type), dimension(:), intent(in) :: bc
    logical,           dimension(:), intent(in) :: specified_transition

    integer :: i

    continue

    need_transition = .false.

    do i = 1, size(bc)
      if (bc_laminar_transition(bc(i)%ibc,specified_transition(i)) &
     .or.  specified_transition(i) ) then
        need_transition = .true.
        return
      end if
    end do

  end function need_transition

!===================== BC_USED_FOR_FORCE_CALCULATION =========================80
!
! logical function bc_used_for_force_calculation
!
!=============================================================================80

  logical function bc_used_for_force_calculation(ibc)

    integer, intent(in) :: ibc

    continue

    bc_used_for_force_calculation = (     ibc == tangency                      &
                                     .or. ibc == inviscid_strong               &
                                     .or. ibc == inviscid_porous               &
                                     .or. ibc == inviscid_pseudo               &
                                     .or. ibc == viscous_porous                &
                                     .or. ibc == viscous_pseudo                &
                                     .or. ibc == viscous_solid                 &
                                     .or. ibc == viscous_adiabatic_ssdc        &
                                     .or. ibc == viscous_solid_trs             &
                                     .or. ibc == dirichlet_viscous             &
                                     .or. ibc == viscous_wall_rough            &
                                     .or. ibc == viscous_wall_function         &
                                     .or. ibc == viscous_weak_wall             &
                                     .or. ibc == viscous_weak_trs              &
                                     .or. ibc == viscous_wf_trs                &
                                     .or. bc_is_flow_through(ibc)              &
                                     .or.(ibc >= viscous_generic_1 .and.       &
                                          ibc <= viscous_generic_0) )

  end function bc_used_for_force_calculation

!============================= BC_IS_FLOW_THROUGH ============================80
!
! logical function bc_is_flow_through used in force calculation
!
!=============================================================================80

  logical function bc_is_flow_through(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_flow_through            = (                                          &
                                          ibc == extrapolate                   &
                                     .or. ibc == back_pressure                 &
                                     .or. ibc == subsonic_outflow_p1           &
                                     .or. ibc == vacuum                        &
                                     .or. ibc == subsonic_outflow_surge        &
                                     .or. ibc == subsonic_inflow_pt_surge      &
                                     .or. ibc == subsonic_outflow_mach         &
                                     .or. ibc == subsonic_outflow_p0           &
                                     .or. ibc == subsonic_inflow_pt            &
                                     .or. ibc == subsonic_inflow_pt_alt        &
                                     .or. ibc == subsonic_inflow_vel           &
                                     .or. ibc == subsonic_inflow_pint          &
                                     .or. ibc == subsonic_inflow_qsqint        &
                                     .or. ibc == rcs_jet_plenum                &
                                     .or. ibc == massflow_out                  &
                                     .or. ibc == massflow_in                   &
                                     .or. ibc == fixed_inflow                  &
                                     .or. ibc == fixed_inflow_profile          &
                                     .or. ibc == lisbon_inflow_profile         &
                                     .or. ibc == fixed_inflow_patch            &
                                     .or. ibc == pulsed_supersonic_inflow      &
                                     .or. ibc == ramped_supersonic_inflow      &
                                     .or. ibc == fixed_outflow                 &
                                     .or. ibc == viscous_porous                &
                                     .or. ibc == inviscid_porous               &
                                     .or. ibc == viscous_bleed                 &
                                     .or. ibc == block_interface               &
                                     .or. ibc == filter                        &
                                                                    )

  end function bc_is_flow_through

!============================= BC_IS_INTERNAL ================================80
!
! logical function bc_is_internal used for patch boundary interfaces
!
!=============================================================================80

  logical function bc_is_internal(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_internal                = (                                          &
                                          ibc == block_interface               &
                                     .or. ibc == filter                        &
                                                                    )

  end function bc_is_internal

!============================= BC_IS_PERIODIC ================================80
!
! logical function bc_is_periodic
!
!=============================================================================80

  logical function bc_is_periodic(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_periodic = ( ibc == periodicity1 .or.                                &
                       ibc == periodicity2 .or.                                &
                       ibc == periodicity3 )

  end function bc_is_periodic

!============================= BC_IS_FAR_FIELD ===============================80
!
! logical function bc_is_flow_through used in force calculation
!
!=============================================================================80

  logical function bc_is_far_field(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_far_field               = (                                          &
                                          ibc == farfield_strong               &
                                     .or. ibc == farfield_convection           &
                                     .or. ibc == farfield_extr                 &
                                     .or. ibc == farfield_pbck                 &
                                     .or. ibc == farfield_roe                  &
                                     .or. ibc == farfield_riem                 &
                                     .or. ibc == farfield_shkfit               &
                                     .or. ibc == extrapolate                   &
                                     .or. ibc == back_pressure                 &
                                     .or. ibc == vacuum                        &
                                     )

  end function bc_is_far_field

!========================= BC_HAS_SKIN_FRICTION ==============================80
!
! logical function bc_has_skin_friction
!
!=============================================================================80

  logical function bc_has_skin_friction(ibc)

    integer, intent(in) :: ibc

    continue

    bc_has_skin_friction = (     ibc == viscous_solid                          &
                            .or. ibc == viscous_solid_trs                      &
                            .or. ibc == viscous_porous                         &
                            .or. ibc == viscous_pseudo                         &
                            .or. ibc == viscous_wall_rough                     &
                            .or. ibc == viscous_wall_function                  &
                            .or. ibc == viscous_weak_wall                      &
                            .or. ibc == viscous_weak_trs                       &
                            .or. ibc == viscous_wf_trs                         &
                            .or. ibc == block_interface                        &
                            .or.(ibc >= viscous_generic_1 .and.                &
                                 ibc <= viscous_generic_0) )

  end function bc_has_skin_friction


!========================= BC_EXPLICITLY_INITIALIZED =========================80
!
! logical function bc_explicitly_initialized
!
!=============================================================================80

  logical function bc_explicitly_initialized(ibc,itime)

    integer, intent(in) :: ibc, itime

    continue

    bc_explicitly_initialized = (                                              &
                                       bc_initialized_solid(ibc)               &
                                  .or. bc_massflux_explicitly_init(ibc)        &
                                  .or. bc_unsteady(ibc, itime)                 &
                                  .or. ibc == symmetry_x                       &
                                  .or. ibc == symmetry_y                       &
                                  .or. ibc == symmetry_z                       &
                                )

  end function bc_explicitly_initialized

!========================= BC_Unsteady =======================================80
!
! logical function bc_unsteady
!
!=============================================================================80

  logical function bc_unsteady(ibc, itime)

    integer, intent(in) :: ibc, itime

    continue

    bc_unsteady = ( itime /= 0 .and. ibc == farfield_riem )

  end function bc_unsteady

!========================= BC_MASSFLUX_EXPLICITLY_INITIALIZED ================80
!
! logical function bc_massflux_explicitly_init
!
!=============================================================================80

  logical function bc_massflux_explicitly_init(ibc)

    integer, intent(in) :: ibc

    continue

    bc_massflux_explicitly_init = (   ibc == massflow_out                      &
                                 .or. ibc == viscous_bleed                     &
                                 .or. ibc == bleed                             &
                                 .or. ibc == massflow_in )

  end function bc_massflux_explicitly_init


!========================== BC_INITIALIZED_SOLID =============================80
!
! logical function bc_initialized_solid
!
!=============================================================================80

  logical function bc_initialized_solid(ibc)

    integer, intent(in) :: ibc

    continue

    bc_initialized_solid = (          ibc == viscous_solid                     &
                                 .or.(ibc >= viscous_generic_1 .and.           &
                                      ibc <= viscous_generic_0)                &
                                 .or. ibc == viscous_solid_trs                 &
!                                .or. ibc == viscous_porous                    &
                                 .or. ibc == viscous_pseudo                    &
                                 .or. ibc == viscous_wall_rough                &
!                                .or. ibc == viscous_weak_wall                 &
!                                .or. ibc == viscous_weak_trs                  &
!                                .or. ibc == viscous_wall_function             &
!                                .or. ibc == viscous_wf_trs                    &
                           )

  end function bc_initialized_solid


!========================= BC_VISCOUS_STRONG_VELOCITY ========================80
!
! logical function bc_viscous_strong_velocity
!
!=============================================================================80

  logical function bc_viscous_strong_velocity(ibc)

    integer, intent(in) :: ibc

    continue

    bc_viscous_strong_velocity = (    ibc == viscous_solid                     &
                                 .or.(ibc >= viscous_generic_1 .and.           &
                                      ibc <= viscous_generic_0)                &
                                 .or. ibc == viscous_solid_trs )

  end function bc_viscous_strong_velocity

!======================== BC_STRONG_TURB =====================================80
!
! logical function bc_strong_turb
!
!=============================================================================80

  logical function bc_strong_turb(ibc)

    integer, intent(in) :: ibc

    continue

    bc_strong_turb = (    bc_viscous_strong_velocity(ibc)                      &
                     .or. ibc == viscous_wall_function                         &
                     .or. ibc == viscous_wf_trs                                &
                     .or. ibc == viscous_weak_wall                             &
                     .or. ibc == viscous_weak_trs                              &
                     .or. ibc == viscous_porous                                &
                     .or. ibc == block_interface                               &
                     .or. ibc == filter                                        &
                     .or. ibc == viscous_pseudo )

  end function bc_strong_turb


!========================= BC_ALLOWED_FOR_ADJOINT ============================80
!
! logical function bc_allowed_for_adjoint
!
!=============================================================================80

  logical function bc_allowed_for_adjoint(eqn_set, ibc)

    use solution_types, only : generic_gas

    integer, intent(in) :: eqn_set
    integer, intent(in) :: ibc

    continue

    bc_allowed_for_adjoint = (     ibc == tangency                             &
                              .or. ibc == viscous_solid                        &
                              .or. ibc == symmetry_x                           &
                              .or. ibc == symmetry_y                           &
                              .or. ibc == symmetry_z                           &
                              .or. ibc == farfield_riem                        &
                              .or. ibc == farfield_extr                        &
                              .or. ibc == farfield_pbck                        &
                              .or. ibc == overset_interp                       &
                              .or. element_based_bc(ibc) )

    if ( eqn_set == generic_gas ) bc_allowed_for_adjoint = .true.

  end function bc_allowed_for_adjoint


!========================= BC_ALLOWED_FOR_GETGRAD ============================80
!
! logical function bc_allowed_for_getgrad
!
!=============================================================================80

  logical function bc_allowed_for_getgrad(ibc,alpha_active,moving_grid,        &
                                          noninertial)

    use adjoint_switches, only : override_bc_limitation
    use lmpi,             only : lmpi_master

    integer, intent(in) :: ibc

    logical, intent(in) :: alpha_active, moving_grid, noninertial

  continue

! No BCs allowed by default

    bc_allowed_for_getgrad = .false.

! First check non-element-based BCs

    if ( ibc == tangency )       bc_allowed_for_getgrad = .true.
    if ( ibc == viscous_solid )  bc_allowed_for_getgrad = .true.
    if ( ibc == farfield_riem )  bc_allowed_for_getgrad = .true.
    if ( ibc == symmetry_x )     bc_allowed_for_getgrad = .true.
    if ( ibc == symmetry_y )     bc_allowed_for_getgrad = .true.
    if ( ibc == symmetry_z )     bc_allowed_for_getgrad = .true.
    if ( ibc == overset_interp ) bc_allowed_for_getgrad = .true.

    if ( ibc == farfield_extr .or. ibc == farfield_pbck ) then

      bc_allowed_for_getgrad = .true.

      if ( noninertial .or. moving_grid ) then
        if ( lmpi_master ) then
          write(*,*) 'Noninertial/moving grid sims not compatible with this BC.'
        endif
        bc_allowed_for_getgrad = .false.
      endif

      if ( alpha_active ) then
        if ( lmpi_master ) then
          write(*,*) 'Angle of attack derivatives not available for this BC.'
        endif
        bc_allowed_for_getgrad = .false.
      endif

    endif

! Now check element-based BCs

    if ( element_based_bc(ibc) ) then
      bc_allowed_for_getgrad = .false.
      if ( lmpi_master ) then
        write(*,*) 'This BC not formally treated for sensitivity analysis:',ibc
        write(*,*) 'However, there are cases in which it is safe to override'
        write(*,*) 'this limitation by specifying override_bc_limitation=.true.'
        write(*,*) 'in the &special_parameters namelist in fun3d.nml.  Users'
        write(*,*) '<NAME_EMAIL> for guidance in'
        write(*,*) 'using this override; please provide your list of active'
        write(*,*) 'design variables with your inquiry.'
      endif
      if ( override_bc_limitation ) then
        if ( lmpi_master ) write(*,*) '===== OVERRIDE ENABLED ===== '
        bc_allowed_for_getgrad = .true.
      endif
    endif

    if ( ibc == riemann .or. ibc == farfield_roe ) then
      if ( alpha_active ) then
        if ( lmpi_master ) then
          write(*,*) 'Angle of attack derivatives not available for this BC.'
        endif
      endif
    endif

  end function bc_allowed_for_getgrad


!===================== BC_NOT_ALLOWED_FOR_NON_INERTIAL =======================80
!
! logical function bc_not_allowed_for_non_inertial
!
!=============================================================================80

  logical function bc_not_allowed_for_non_inertial(ibc)

    integer, intent(in) :: ibc

    continue

    bc_not_allowed_for_non_inertial = (     ibc == dirichlet_viscous           &
                                       .or. ibc == dirichlet_lisbon            &
                                       .or. ibc == dirichlet_discrete          &
                                       .or. ibc == dirichlet )

  end function bc_not_allowed_for_non_inertial

!===================== ELEMENT_BASED_BC======================================80
!
! logical function bc uses element based convention and set the entire flux
!
!=============================================================================80

  logical function element_based_bc(ibc)

    integer, intent(in) :: ibc

    continue

    element_based_bc = (     ibc == farfield_roe                              &
!  FIXME: commented     .or. ibc == farfield_extr                             &
                        .or. ibc == tangency_weak                             &
                        .or. ibc == riemann                                   &
                        .or. ibc == extrapolate                               &
                        .or. ibc == subsonic_inflow_vel                       &
                        .or. ibc == subsonic_inflow_pint                      &
                        .or. ibc == subsonic_inflow_qsqint                    &
                        .or. ibc == pulsed_subsonic_inflow_rho                &
                        .or. ibc == subsonic_inflow_pt                        &
                        .or. ibc == subsonic_inflow_pt_alt                    &
                        .or. ibc == rcs_jet_plenum                            &
                        .or. ibc == subsonic_outflow_p0                       &
                        .or. ibc == back_pressure                             &
                        .or. ibc == subsonic_outflow_p1                       &
                        .or. ibc == vacuum                                    &
                        .or. ibc == subsonic_outflow_surge                    &
                        .or. ibc == subsonic_inflow_pt_surge                  &
                        .or. ibc == subsonic_outflow_mach                     &
                        .or. ibc == hover_ss                                  &
                        .or. ibc == massflow_out                              &
                        .or. ibc == viscous_bleed                             &
                        .or. ibc == bleed                                     &
                        .or. ibc == massflow_in                               &
                        .or. ibc == fixed_inflow                              &
                        .or. ibc == fixed_inflow_profile                      &
                        .or. ibc == lisbon_inflow_profile                     &
                        .or. ibc == fixed_inflow_patch                        &
                        .or. ibc == pulsed_supersonic_inflow                  &
                        .or. ibc == ramped_supersonic_inflow                  &
                        .or. ibc == fixed_outflow                             &
                        .or. ibc == viscous_wall_function                     &
                        .or. ibc == viscous_wf_trs                            &
                        .or. ibc == viscous_weak_wall                         &
                        .or. ibc == viscous_weak_trs                          &
                        .or. ibc == solution_functional                       &
                        .or. ibc == viscous_porous                            &
                        .or. ibc == inviscid_porous                           &
                        .or. ibc == block_interface                           &
                        .or. ibc == filter                                    &
!FIXME NOTEA            .or. ibc == viscous_solid                             &
                        )

  end function element_based_bc

!===================== ELEMENT_BASED_BCI======================================80
!
! logical function bc uses element based convention and set the entire flux
!
!=============================================================================80

  logical function element_based_bci(ibc)

    integer, intent(in) :: ibc

    continue

    element_based_bci = (     ibc == farfield_roe                              &
                         .or. ibc == tangency_weak                             &
                         .or. ibc == riemann                                   &
                         .or. ibc == extrapolate                               &
                         .or. ibc == farfield_convection                       &
                         .or. ibc == subsonic_inflow_vel                       &
                         .or. ibc == back_pressure                             &
                         )

  end function element_based_bci


!===================== BC_NOT_ALLOWED_FOR_MOVING_GRID ========================80
!
! logical function bc_not_allowed_for_moving_grid
!
!=============================================================================80

  logical function bc_not_allowed_for_moving_grid(ibc)

    integer, intent(in) :: ibc

    continue

    bc_not_allowed_for_moving_grid = (     ibc == inviscid_strong              &
                                      .or. ibc == inviscid_porous              &
                                      .or. ibc == massflow_out                 &
                                      .or. ibc == massflow_in                  &
                                      .or. ibc == dirichlet_viscous            &
                                      .or. ibc == dirichlet_lisbon             &
                                      .or. ibc == farfield_convection          &
                                      .or. ibc == dirichlet_discrete           &
                                      .or. ibc == dirichlet )

  end function bc_not_allowed_for_moving_grid


!========================== BC_IS_NOT_INCOMPRESSIBLE =========================80
!
! logical function bc_is_not_incompressible
!
!=============================================================================80

  logical function bc_is_not_incompressible(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_not_incompressible = (   ibc == viscous_solid_trs                    &
                              .or. ibc == viscous_wall_rough                   &
                              .or. ibc == viscous_wall_function                &
                              .or. ibc == viscous_weak_wall                    &
                              .or. ibc == viscous_weak_trs                     &
                              .or. ibc == viscous_wf_trs                       &
                              .or. ibc == viscous_porous                       &
                              .or. ibc == viscous_pseudo                       &
                              .or. ibc == inviscid_strong                      &
                              .or. ibc == inviscid_porous                      &
                              .or. ibc == inviscid_pseudo                      &
                              .or. ibc == totalp_totalt_version3               &
                              .or. ibc == rcs_jet_plenum                       &
                              .or. ibc == subsonic_inflow_pt                   &
                              .or. ibc == subsonic_inflow_pt_alt               &
                              .or. ibc == massflow_out                         &
                              .or. ibc == viscous_bleed                        &
                              .or. ibc == bleed                                &
                              .or. ibc == massflow_in                          &
                               )

  end function bc_is_not_incompressible


!========================== BC_IS_NOT_PERFECTGAS =============================80
!
! logical function bc_is_not_perfectgas
!
!=============================================================================80

  logical function bc_is_not_perfectgas(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_not_perfectgas = (   ibc == viscous_pseudo                           &
                          .or. ibc == inviscid_pseudo                          &
                          .or. ibc == totalp_totalt_version3                   &
                          .or. ibc == rcs_jet_plenum                           &
                          .or. ibc == inviscid_strong                          &
                          .or. ibc == farfield_strong )

  end function bc_is_not_perfectgas


!============================== BC_IS_NOT_GENGAS =============================80
!
! logical function bc_is_not_gengas
!
!=============================================================================80

  logical function bc_is_not_gengas(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_not_gengas = (                                                       &
                           ibc == viscous_solid_trs                            &
                      .or. ibc == viscous_wall_rough                           &
                      .or. ibc == viscous_wall_function                        &
                      .or. ibc == viscous_weak_wall                            &
                      .or. ibc == viscous_weak_trs                             &
                      .or. ibc == viscous_wf_trs                               &
                      .or. ibc == massflow_out                                 &
                      .or. ibc == viscous_bleed                                &
                      .or. ibc == bleed                                        &
                      .or. ibc == massflow_in                                  &
!                     .or. ibc == fixed_inflow                                 &
                      .or. ibc == fixed_inflow_profile                         &
                      .or. ibc == lisbon_inflow_profile                        &
                      .or. ibc == fixed_inflow_patch                           &
                      .or. ibc == pulsed_supersonic_inflow                     &
                      .or. ibc == ramped_supersonic_inflow                     &
                      .or. ibc == fixed_outflow                                &
                      .or. ibc == solution_functional                          &
                      .or. ibc == dirichlet_viscous                            &
                      .or. ibc == dirichlet_lisbon                             &
                      .or. ibc == dirichlet_discrete                           &
                      .or. ibc == dirichlet                                    &
                      .or. ibc == viscous_porous                               &
                      .or. ibc == block_interface                              &
                      .or. ibc == filter                                       &
                       )

  end function bc_is_not_gengas


!========================== BC_STRONG_VISCOUS_ADJOINT ========================80
!
! logical function bc_strong_visc_adjoint
!
!=============================================================================80

  logical function bc_strong_viscous_adjoint(ibc)

    integer, intent(in) :: ibc

    continue

    bc_strong_viscous_adjoint = (  ibc == viscous_solid                        &
                              .or. ibc == viscous_solid_trs                    &
                              .or. ibc == viscous_pseudo                       &
                              .or. ibc == viscous_wall_rough                   &
                                 )

  end function bc_strong_viscous_adjoint


!======================== BC_HAS_PRESSURE_CLOSURE ============================80
!
! logical function bc_has_pressure_closure
!
!=============================================================================80

  logical function bc_has_pressure_closure(ibc)

    use info_depr,  only : twod
    use debug_defs, only : forces_via_fluxes

    integer, intent(in) :: ibc

    continue

!FIXME NOTEA
!FIXME NOTEA The element_based closure in bc_has_pressure_closure path
!FIXME NOTEA has errors in the LHS Jacobian for simplicial meshes.
!FIXME NOTEA Discovered by RTB and verifed.   Several composite Jacobian
!FIXME NOTEA check builds now in place which display the error.
!FIXME NOTEA They are repaired by moving this closure to another path
!FIXME NOTEA (i.e., delete boundary condition below and add to
!FIXME NOTEA element_based_bc above).   The element_based_bc path is
!FIXME NOTEA more general (applicable to manufactured solutions) and along
!FIXME NOTEA the way an error in the generic gas path for moving grids
!FIXME NOTEA was found and corrected.  For viscous_solid, the differences only
!FIXME NOTEA show up for adiabatic walls with moving grid terms because
!FIXME NOTEA the momentum and energy energy residuals are overwritten for
!FIXME NOTEA viscous specified-wall-temperature and there is no continuity
!FIXME NOTEA contribution from pressure.  The adjoint path uses
!FIXME NOTEA the element_based_bc path for compressible flow but not for
!FIXME NOTEA incompressible flow currently.  The impact of the switch on
!FIXME NOETA adjoint side needs to be reviewed before proceeding further.
!FIXME NOTEA The modififications enabling this switch are made by series
!FIXME NOTEA of commits: r62432 (thanks Bil!) 62395 62369 62365 62347 62341
!FIXME NOTEA 62339-62334 62331-62329 62310 62306 62305 62302 62299
!FIXME NOTEA 62285 62280 62279 (the last 3 was a false start)
!FIXME NOTEA The basic approach is to define an unsplit flux and a tangency
!FIXME NOTEA flux and use all of the infrastructure of the element_based_bc
!FIXME NOETA path.
!FIXME NOTEA

    ! All of the element based closures associated with bc_has_pressure_closure
    ! can potentially be moved to the element_based_bc path.

    if (twod) then
      bc_has_pressure_closure = (                                              &
                                  ibc == tangency              .or.            &
                                  ibc == inviscid_strong       .or.            &
!FIXME COMPUTE BOUNDARY INVISCID FLUXES (PRESSURE TERMS)
  ( forces_via_fluxes <= 1 .and. ibc == viscous_solid )        .or.            &
!FIXME COMPUTE BOUNDARY INVISCID FLUXES (PRESSURE TERMS)
                                 (ibc >= viscous_generic_1 .and.               &
                                  ibc <= viscous_generic_0).or.                &
                                  ibc == viscous_solid_trs     .or.            &
                                  ibc == viscous_wall_rough                    &
                                 )

    else

      bc_has_pressure_closure = (                                              &
                                  ibc == tangency              .or.            &
                                  ibc == inviscid_strong       .or.            &
!FIXME NOTEA
                                  ibc == viscous_solid         .or.            &
!FIXME NOTEA
                                  ibc == viscous_solid_trs     .or.            &
                                 (ibc >= viscous_generic_1 .and.               &
                                  ibc <= viscous_generic_0).or.                &
                                  ibc == symmetry_1_strong     .or.            &
                                  ibc == symmetry_2_strong     .or.            &
                                  ibc == symmetry_3_strong     .or.            &
!                                 ibc == viscous_wall_function .or.            &
!                                 ibc == viscous_weak_wall     .or.            &
!                                 ibc == viscous_weak_trs      .or.            &
!                                 ibc == viscous_wf_trs        .or.            &
                                  ibc == viscous_wall_rough                    &
                                )

    end if

  end function bc_has_pressure_closure


!======================= BC_HAS_VISC_FLUX_CLOSURE ============================80
!
! logical function bc_has_visc_flux_closure
!
!=============================================================================80

  logical function bc_has_visc_flux_closure(ibc)

    integer, intent(in) :: ibc

    continue

    bc_has_visc_flux_closure = (                                               &
                         ibc == viscous_wall_rough )

    if ( bc_viscous_strong_velocity(ibc) ) bc_has_visc_flux_closure = .false.

    bc_has_visc_flux_closure = ( (ibc >= viscous_generic_1 .and.               &
                                  ibc <= viscous_generic_0))
  end function bc_has_visc_flux_closure

!================================ BC_IGNORE_2D ===============================80
!
! Filters BCs that we don't need to set for 2D cases
!
!=============================================================================80

  logical function bc_ignore_2d(ibc)

    integer, intent(in) :: ibc

    continue

    bc_ignore_2d = ibc == symmetry_y

  end function bc_ignore_2d

!========================= BC_ALLOWED_FOR_TESTING_SA =========================80
!
! logical function bc_allowed_for_testing_sa (special path within sa turbulence)
!
!=============================================================================80

  logical function bc_allowed_for_testing_sa(eqn_set, ibc)

    use solution_types, only : generic_gas

    integer, intent(in) :: eqn_set
    integer, intent(in) :: ibc

    continue

    bc_allowed_for_testing_sa = (   ibc == viscous_solid                       &
                               .or. ibc == dirichlet                           &
                               .or. ibc == dirichlet_viscous                   &
                               .or. ibc == dirichlet_discrete                  &
                               .or. ibc == symmetry_x                          &
                               .or. ibc == symmetry_y                          &
                               .or. ibc == symmetry_z                          &
! FIXME: BROKEN UNTIL FURTHER NOTICE - POSITIVE DIAGONAL INITIATIVE            &
!                              .or. ibc == farfield_roe                        &
                               .or. ibc == tangency_weak                       &
                               .or. ibc == riemann                             &
                               .or. ibc == farfield_roe )

    if ( eqn_set == generic_gas ) bc_allowed_for_testing_sa = .false.
    if ( eqn_set < 0  ) bc_allowed_for_testing_sa = .false.

  end function bc_allowed_for_testing_sa

!========================= BACKWARDS_COMPATIBLE_BC ===========================80
!
! FIXME: description
!
!=============================================================================80

  subroutine backwards_compatible_bc(ibc, bc_name)

    use string_utils, only : downcase

    integer,                 intent(inout) :: ibc
    character(80), optional, intent(inout) :: bc_name

    integer, parameter :: symmetry_x_strong = 6011
    integer, parameter :: symmetry_y_strong = 6010
    integer, parameter :: symmetry_z_strong = 6012
    !integer, parameter :: farfield_extr     = 5005
    !integer, parameter :: farfield_pbck     = 5010

    continue

    if ( .not. present(bc_name) ) then

      if ( ibc == symmetry_y_strong ) ibc = symmetry_y
      if ( ibc == symmetry_x_strong ) ibc = symmetry_x
      if ( ibc == symmetry_z_strong ) ibc = symmetry_z
      if ( ibc == farfield_extr )     ibc = extrapolate
      if ( ibc == farfield_pbck )     ibc = back_pressure

    else

       select case( downcase(trim(bc_name)) )
       case('farfield_pbck')
         ibc     =  back_pressure
         bc_name = 'back_pressure'
       case('farfield_extr')
         ibc     =  extrapolate
         bc_name = 'extrapolate'
       case('symmetry_x_strong')
         ibc     =  symmetry_x
         bc_name = 'symmetry_x'
       case('symmetry_y_strong')
         ibc     =  symmetry_y
         bc_name = 'symmetry_y'
       case('symmetry_z_strong')
         ibc     =  symmetry_z
         bc_name = 'symmetry_z'
       end select

    endif

  end subroutine backwards_compatible_bc

!=============================================================================80
!
! based on http://tetruss.larc.nasa.gov/usm3d/bc.html
!  BOUNDARY CONDITIONS FOR USM3Dns
!
!=============================================================================80

  integer function usm3d_to_fun3d_bc(ibc,farfield_roe_selection)

    integer, intent(in) :: ibc
    logical, intent(in) :: farfield_roe_selection

    continue

    select case(ibc)
      case(-1)                                 ! overset-grid interpolation
        usm3d_to_fun3d_bc = overset_interp
      case(0)                                  ! Freestream, Supersonic Inflow
        if ( farfield_roe_selection ) then
          usm3d_to_fun3d_bc = farfield_roe
        else
          usm3d_to_fun3d_bc = farfield_riem
        endif
      case(1)                                  ! old y-symmetry plane
        usm3d_to_fun3d_bc = symmetry_y
      case(2)                                  ! old Extrapolation
        usm3d_to_fun3d_bc = farfield_extr
      case(3)                                  ! old Far-field
        if ( farfield_roe_selection ) then
          usm3d_to_fun3d_bc = farfield_roe
        else
          usm3d_to_fun3d_bc = farfield_riem
        endif
      case(4)                                  ! old Viscous surface
        usm3d_to_fun3d_bc = viscous_solid
      case(44)                                 ! old Viscous surface
        usm3d_to_fun3d_bc = viscous_solid
      case(-4)                         ! old Viscous surface lam in turb flow
        usm3d_to_fun3d_bc = viscous_solid_trs
      case(5)                                  ! old Inviscid surface
        usm3d_to_fun3d_bc = tangency
      case(55)                                 ! old Inviscid surface
        usm3d_to_fun3d_bc = tangency
      case(101:)
        usm3d_to_fun3d_bc = ibc
      case(-4000)
        usm3d_to_fun3d_bc = viscous_solid_trs ! Viscous surface lam in turb flow
      case(-4100)
        usm3d_to_fun3d_bc = viscous_wf_trs    ! Viscous surface lam in turb flow
      case(-4110)
        usm3d_to_fun3d_bc = viscous_weak_trs  ! Viscous surface lam in turb flow
      case default
        usm3d_to_fun3d_bc = -9999
    end select

  end function usm3d_to_fun3d_bc

!========================= BC_DIRICHLET ======================================80
!
! logical function bc_dirichlet
!
!=============================================================================80

  logical function bc_dirichlet(ibc)

    integer, intent(in) :: ibc

    continue

    bc_dirichlet = (                  ibc == dirichlet_viscous                 &
                                 .or. ibc == dirichlet_discrete                &
                                 .or. ibc == dirichlet  )

  end function bc_dirichlet

!===================== BC_IS_SYMMETRY ========================================80
!
!=============================================================================80

  logical function bc_is_symmetry(ibc)

    integer, intent(in) :: ibc

    continue

    bc_is_symmetry = (   ibc == symmetry_x                                     &
                    .or. ibc == symmetry_y                                     &
                    .or. ibc == symmetry_z )

  end function bc_is_symmetry

!================================ BC_ALLOW_EXACT_NC ==========================80
!
! Filter for allowable bc.
!
!=============================================================================80

  logical function bc_allow_exact_nc(ibc)

    use convection_defs, only : incompressible_convection

    integer, intent(in) :: ibc

  continue

    if ( incompressible_convection ) then

      bc_allow_exact_nc = ( ibc == symmetry_y                             .or. &
                            ibc == dirichlet                              .or. &
                            ibc == dirichlet_viscous                      .or. &
                            ibc == farfield_convection )

    else

      bc_allow_exact_nc = (                                                    &
                          ibc == symmetry_x                               .or. &
                          ibc == symmetry_y                               .or. &
                          ibc == symmetry_z                               .or. &
                          ibc == dirichlet                                .or. &
                          ibc == dirichlet_viscous                        .or. &
                          ibc == dirichlet_lisbon                         .or. &
                          ibc == farfield_roe                             .or. &
                          ibc == farfield_riem                            .or. &
                          ibc == tangency                                 .or. &
                          ibc == viscous_solid )
    endif

  end function bc_allow_exact_nc

!================================ BC_ALLOW_EXACT_CC ==========================80
!
! Filter for allowable bc.
!
!=============================================================================80

  logical function bc_allow_exact_cc(ibc, eqn_set)

    use solution_types,  only : compressible, incompressible
    use convection_defs, only : incompressible_convection

    integer, intent(in) :: ibc, eqn_set

  continue

    if ( incompressible_convection ) then

      bc_allow_exact_cc = ( ibc == symmetry_y                             .or. &
                            ibc == farfield_convection )

    elseif ( eqn_set == compressible ) then

      bc_allow_exact_cc = ( ibc == symmetry_x                             .or. &
                            ibc == symmetry_y                             .or. &
                            ibc == symmetry_z                             .or. &
                            ibc == dirichlet                              .or. &
                            ibc == dirichlet_viscous                      .or. &
                            ibc == farfield_roe                           .or. &
                            ibc == farfield_riem                          .or. &
                            ibc == extrapolate                            .or. &
                            ibc == farfield_riem                          .or. &
                            ibc == back_pressure                          .or. &
                            ibc == subsonic_outflow_p1                    .or. &
                            ibc == tangency                               .or. &
                            ibc == viscous_solid )

    elseif ( eqn_set == incompressible ) then

      bc_allow_exact_cc = ( ibc == symmetry_x                             .or. &
                            ibc == symmetry_y                             .or. &
                            ibc == symmetry_z                             .or. &
                            ibc == dirichlet                              .or. &
                            ibc == dirichlet_viscous                      .or. &
                            ibc == farfield_roe                           .or. &
                            ibc == farfield_riem                          .or. &
                            ibc == extrapolate                            .or. &
                            ibc == farfield_riem                          .or. &
                            ibc == tangency                               .or. &
                            ibc == viscous_solid )

    else

     bc_allow_exact_cc = .false.

    endif

  end function bc_allow_exact_cc

end module bc_names

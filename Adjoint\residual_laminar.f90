module residual_laminar

  implicit none

  private

  public :: dvisrhs_mix6

contains

!================================= DVISRHS_MIX6 ==============================80
!
! This routine computes the compressible viscous flux jacobians in general
! (mixed) elements and constructs the adjoint residual (dR/dQ)^T * lambda
! Routine can either store the matrix explicitly or carry out the matrix-vector
! product itself, based on which optional arguments are present in the call
!
! Note: qnode is assumed to contain conserved variables
!
! Linearizes wrt turbulence quantity as well
!
!=============================================================================80
  subroutine dvisrhs_mix6(nnodes0, nnodes01, ncell, c2n, x, y, z, qnode, amut, &
                          local_f2n, local_e2n, chk_norm, local_f2e, e2n_2d,   &
                          face_per_cell, node_per_cell, edge_per_cell,         &
                          type_cell, n_tot, adim, face_2d, n_turb, turb,       &
                          nfunctions, rlam, coltag, res, ia, ja, iau, aa)

    use kinddefs,         only : dp
    use info_depr,        only : tref, xmach, re, ivgrd, twod, ivisc,          &
                                 use_edge_gradients
    use fluid,            only : gamma, gm1, ggm1, sutherland_constant, prandtl
    use element_defs,     only : max_node_per_cell, max_face_per_cell,         &
                                 max_edge_per_cell
    use utilities,        only : cell_jacobians, cell_gradients
    use turb_parameters,  only : turbulent_prandtl
    use turb_sa_const,    only : cv1
    use lmpi,             only : lmpi_die
    use debug_defs,       only : gradient_construction_rhs
    use adjoint_switches, only : use_bp_model

    integer, intent(in) :: n_tot, adim, nnodes0, nnodes01, ncell, face_per_cell
    integer, intent(in) :: node_per_cell, edge_per_cell
    integer, intent(in) :: face_2d, n_turb
    integer, intent(in), optional :: nfunctions

    integer, dimension(node_per_cell,ncell),         intent(in) :: c2n
    integer, dimension(face_per_cell,4),             intent(in) :: local_f2n
    integer, dimension(edge_per_cell,6),             intent(in) :: local_e2n
    integer, dimension(face_per_cell,face_per_cell), intent(in) :: chk_norm
    integer, dimension(face_per_cell,4),             intent(in) :: local_f2e
    integer, dimension(4,2),                         intent(in) :: e2n_2d
    integer, dimension(:),                 optional, intent(in) :: ia, ja, iau

    real(dp), dimension(nnodes01),                    intent(in)    :: x,y,z
    real(dp), dimension(n_tot,nnodes01),              intent(in)    :: qnode
    real(dp), dimension(nnodes01),                    intent(in)    :: amut
    real(dp), dimension(n_turb,nnodes01),             intent(in)    :: turb
    real(dp), dimension(:,:,:),             optional, intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01),     optional, intent(in)    :: coltag
    real(dp), dimension(:,:,:),             optional, intent(inout) :: aa
    real(dp), dimension(:,:,:),             optional, intent(inout) :: res

    character(len=3), intent(in) :: type_cell

    logical :: edge_gradients

    integer :: n, nodec, j, idiag, ioff, k, column
    integer :: ie, i, ii, ie_local, i_local
    integer :: nodes_local, edges_local
    integer :: n1_loc, n2_loc, node
    integer :: n1, n2, n3, n4, n5, n6
    integer :: big_angle, ifcn

    integer, dimension(max_node_per_cell)   :: node_map
    integer, dimension(max_edge_per_cell)   :: edge_map

    real(dp) :: c23, c43, cgp, xmr
    real(dp) :: cstar, dot, disi
    real(dp) :: ex, ey, ez, factor
    real(dp) :: vf2, vf3, vf4
    real(dp) :: xr, yr, zr, xl, yl, zl, xm, ym, zm
    real(dp) :: areax, areay, areaz
    real(dp) :: u1, u2, v1, e1, p1, v2, w1, w2, t1, t2, e2, p2
    real(dp) :: egradu, egradv, egradw, egradt
    real(dp) :: gradu_xi, gradv_xi, gradw_xi, gradt_xi
    real(dp) :: xc, yc, zc, cell_vol, fact
    real(dp) :: ux, uy, uz, vx, vy, vz, wx, wy, wz, tx, ty, tz
    real(dp) :: uxavg, uyavg, uzavg, vxavg, vyavg, vzavg
    real(dp) :: wxavg, wyavg, wzavg, txavg, tyavg, tzavg
    real(dp) :: rmu, umu, vmu, wmu
    real(dp) :: dtxxdr, dtxxdm, dtxxdn, dtxxdl, dtxxde, dtxxdt
    real(dp) :: dtxydr, dtxydm, dtxydn, dtxydl, dtxyde, dtxydt
    real(dp) :: dtxzdr, dtxzdm, dtxzdn, dtxzdl, dtxzde, dtxzdt
    real(dp) :: dtyydr, dtyydm, dtyydn, dtyydl, dtyyde, dtyydt
    real(dp) :: dtyzdr, dtyzdm, dtyzdn, dtyzdl, dtyzde, dtyzdt
    real(dp) :: dtzzdr, dtzzdm, dtzzdn, dtzzdl, dtzzde, dtzzdt
    real(dp) :: txx, txy, txz, tyy, tyz, tzz
    real(dp) :: rax, ray, raz
    real(dp) :: dpiecedr, dpiecedm, dpiecedn, dpiecedl, dpiecede
    real(dp) :: dpiecedt
    real(dp) :: dtqxdr, dtqxdm, dtqxdn, dtqxdl, dtqxde, dtqxdt
    real(dp) :: dtqydr, dtqydm, dtqydn, dtqydl, dtqyde, dtqydt
    real(dp) :: dtqzdr, dtqzdm, dtqzdn, dtqzdl, dtqzde, dtqzdt
    real(dp) :: rmucgp, cgpt, usum, vsum, wsum
    real(dp) :: dgradu_xidr, dgradu_xidm
    real(dp) :: dgradv_xidr, dgradv_xidn
    real(dp) :: dgradw_xidr, dgradw_xidl
    real(dp) :: dnudr,dnudm,dnudn,dnudl,dnude
    real(dp) :: chi,dchidr,dchidm,dchidn,dchidl,dchide,dchidt
    real(dp) :: psi,dpsidr,dpsidm,dpsidn,dpsidl,dpside,dpsidt
    real(dp) :: base,dbasedr,dbasedm,dbasedn,dbasedl,dbasede,dbasedt
    real(dp) :: fv1,fv1dr,fv1dm,fv1dn,fv1dl,fv1de,fv1dt
    real(dp) :: dgradt_xidr, dgradt_xidm
    real(dp) :: dgradt_xidn, dgradt_xidl
    real(dp) :: dgradt_xide
    real(dp) :: areai, xnf, ynf, znf

    real(dp), dimension(max_node_per_cell) :: dmudr,dmudm,dmudn,dmudl,dmude
    real(dp), dimension(max_node_per_cell) :: dmutdr,dmutdm,dmutdn,dmutdl,dmutde
    real(dp), dimension(max_node_per_cell) :: dmutdt
    real(dp), dimension(max_node_per_cell) :: duxavgdr
    real(dp), dimension(max_node_per_cell) :: duxavgdm
    real(dp), dimension(max_node_per_cell) :: duyavgdr
    real(dp), dimension(max_node_per_cell) :: duyavgdm
    real(dp), dimension(max_node_per_cell) :: duzavgdr
    real(dp), dimension(max_node_per_cell) :: duzavgdm
    real(dp), dimension(max_node_per_cell) :: dvxavgdr
    real(dp), dimension(max_node_per_cell) :: dvxavgdn
    real(dp), dimension(max_node_per_cell) :: dvyavgdr
    real(dp), dimension(max_node_per_cell) :: dvyavgdn
    real(dp), dimension(max_node_per_cell) :: dvzavgdr
    real(dp), dimension(max_node_per_cell) :: dvzavgdn
    real(dp), dimension(max_node_per_cell) :: dwxavgdr
    real(dp), dimension(max_node_per_cell) :: dwxavgdl
    real(dp), dimension(max_node_per_cell) :: dwyavgdr
    real(dp), dimension(max_node_per_cell) :: dwyavgdl
    real(dp), dimension(max_node_per_cell) :: dwzavgdr
    real(dp), dimension(max_node_per_cell) :: dwzavgdl
    real(dp), dimension(max_node_per_cell) :: dtxavgdr
    real(dp), dimension(max_node_per_cell) :: dtxavgdm
    real(dp), dimension(max_node_per_cell) :: dtxavgdn
    real(dp), dimension(max_node_per_cell) :: dtxavgdl
    real(dp), dimension(max_node_per_cell) :: dtxavgde
    real(dp), dimension(max_node_per_cell) :: dtyavgdr
    real(dp), dimension(max_node_per_cell) :: dtyavgdm
    real(dp), dimension(max_node_per_cell) :: dtyavgdn
    real(dp), dimension(max_node_per_cell) :: dtyavgdl
    real(dp), dimension(max_node_per_cell) :: dtyavgde
    real(dp), dimension(max_node_per_cell) :: dtzavgdr
    real(dp), dimension(max_node_per_cell) :: dtzavgdm
    real(dp), dimension(max_node_per_cell) :: dtzavgdn
    real(dp), dimension(max_node_per_cell) :: dtzavgdl
    real(dp), dimension(max_node_per_cell) :: dtzavgde
    real(dp), dimension(max_node_per_cell) :: dtdr,dtdm,dtdn,dtdl,dtde
    real(dp), dimension(max_node_per_cell) :: duxdr, duxdm
    real(dp), dimension(max_node_per_cell) :: duydr, duydm
    real(dp), dimension(max_node_per_cell) :: duzdr, duzdm
    real(dp), dimension(max_node_per_cell) :: dvxdr, dvxdn
    real(dp), dimension(max_node_per_cell) :: dvydr, dvydn
    real(dp), dimension(max_node_per_cell) :: dvzdr, dvzdn
    real(dp), dimension(max_node_per_cell) :: dwxdr, dwxdl
    real(dp), dimension(max_node_per_cell) :: dwydr, dwydl
    real(dp), dimension(max_node_per_cell) :: dwzdr, dwzdl
    real(dp), dimension(max_node_per_cell) :: dtxdr, dtxdm, dtxdn
    real(dp), dimension(max_node_per_cell) :: dtxdl, dtxde
    real(dp), dimension(max_node_per_cell) :: dtydr, dtydm, dtydn
    real(dp), dimension(max_node_per_cell) :: dtydl, dtyde
    real(dp), dimension(max_node_per_cell) :: dtzdr, dtzdm, dtzdn
    real(dp), dimension(max_node_per_cell) :: dtzdl, dtzde
    real(dp), dimension(max_node_per_cell) :: dvf2dr, dvf2dm, dvf2dt
    real(dp), dimension(max_node_per_cell) :: dvf2dn, dvf2dl, dvf2de
    real(dp), dimension(max_node_per_cell) :: dvf3dr, dvf3dm, dvf3dt
    real(dp), dimension(max_node_per_cell) :: dvf3dn, dvf3dl, dvf3de
    real(dp), dimension(max_node_per_cell) :: dvf4dr, dvf4dm, dvf4dt
    real(dp), dimension(max_node_per_cell) :: dvf4dn, dvf4dl, dvf4de
    real(dp), dimension(max_node_per_cell) :: dvf5dr, dvf5dm, dvf5dt
    real(dp), dimension(max_node_per_cell) :: dvf5dn, dvf5dl, dvf5de
    real(dp), dimension(max_node_per_cell) :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell) :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell) :: dgradz_celldq
    real(dp), dimension(max_face_per_cell) :: nx, ny, nz
    real(dp), dimension(max_node_per_cell) :: rho_node, e_node, mu_node
    real(dp), dimension(max_node_per_cell) :: nu_node, turb_node
    real(dp), dimension(max_node_per_cell) :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell) :: t_node, p_node
    real(dp), dimension(4,max_node_per_cell) :: q_node
    real(dp), dimension(4)                 :: gradx_cell, grady_cell
    real(dp), dimension(4)                 :: gradz_cell
    real(dp), dimension(max_node_per_cell) :: drmudr,dumudr,dvmudr,dwmudr
    real(dp), dimension(max_node_per_cell) :: drmudm,dumudm,dvmudm,dwmudm
    real(dp), dimension(max_node_per_cell) :: drmudn,dumudn,dvmudn,dwmudn
    real(dp), dimension(max_node_per_cell) :: drmudl,dumudl,dvmudl,dwmudl
    real(dp), dimension(max_node_per_cell) :: drmude,dumude,dvmude,dwmude
    real(dp), dimension(max_node_per_cell) :: drmudt,dumudt,dvmudt,dwmudt
    real(dp), dimension(max_node_per_cell) :: dudr, dudm, dudn, dudl, dude
    real(dp), dimension(max_node_per_cell) :: dvdr, dvdm, dvdn, dvdl, dvde
    real(dp), dimension(max_node_per_cell) :: dwdr, dwdm, dwdn, dwdl, dwde
    real(dp), dimension(5,6)               :: a
    real(dp), dimension(3)                 :: augment_weight

    real(dp), parameter :: my_haf  = 0.50_dp
    real(dp), parameter :: my_mxd  = 0.99939_dp
    real(dp), parameter :: my_1    = 1.0_dp
    real(dp), parameter :: my_1p5  = 1.5_dp
    real(dp), parameter :: my_2    = 2.0_dp
    real(dp), parameter :: my_3    = 3.0_dp
    real(dp), parameter :: my_4    = 4.0_dp

    logical :: form_matvec = .false.
    logical :: form_matrix = .false.

  continue

    if ( present(res) ) form_matvec = .true.
    if ( present(aa) )  form_matrix = .true.

    if ( (.not.form_matvec) .and. (.not.form_matrix)) then
      write(*,*) 'WARNING:  dvisrhs_mix6 called but did not request output...'
      return
    endif

    cstar = sutherland_constant/tref
    xmr   = xmach/re
    c43   = my_4/my_3
    c23   = my_2/my_3
    cgp   = my_1/(gm1*prandtl)
    cgpt  = my_1/(gm1*turbulent_prandtl)

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

    edge_map(:) = 0
    node_map(:) = 0

    if (twod) then

      nodes_local = 3
      if (local_f2n(face_2d,1) /= local_f2n(face_2d,4)) nodes_local = 4

      do i=1,nodes_local
        node_map(i) = local_f2n(face_2d,i)
      end do

      edges_local = 3
      if (local_f2e(face_2d,1) /= local_f2e(face_2d,4)) edges_local = 4

      do i = 1,edges_local
        edge_map(i) = local_f2e(face_2d,i)
      end do

    else

      nodes_local = node_per_cell

      do i=1,nodes_local
        node_map(i) = i
      end do

      edges_local = edge_per_cell

      do i=1,edges_local
        edge_map(i)  = i
      end do

    end if

! Loop over the cells

    cell_loop: do n = 1, ncell

! Initialization

      cell_vol = 0.0_dp

      ux = 0._dp
      vx = 0._dp
      wx = 0._dp
      tx = 0._dp

      uy = 0._dp
      vy = 0._dp
      wy = 0._dp
      ty = 0._dp

      uz = 0._dp
      vz = 0._dp
      wz = 0._dp
      tz = 0._dp

      rmu = 0.0_dp
      umu = 0.0_dp
      vmu = 0.0_dp
      wmu = 0.0_dp
      rmucgp = 0.0_dp

      xc = 0._dp
      yc = 0._dp
      zc = 0._dp

      rho_node(:) = 0._dp
      e_node(:)   = 0._dp
      mu_node(:)  = 0._dp
      nu_node(:)  = 0._dp
      turb_node(:)= 0._dp
      u_node(:)   = 0._dp
      v_node(:)   = 0._dp
      w_node(:)   = 0._dp
      t_node(:)   = 0._dp
      p_node(:)   = 0._dp
      x_node(:)   = 0._dp
      y_node(:)   = 0._dp
      z_node(:)   = 0._dp
      q_node(:,:) = 0._dp

      duxdr(:) = 0._dp
      duxdm(:) = 0._dp
      duydr(:) = 0._dp
      duydm(:) = 0._dp
      duzdr(:) = 0._dp
      duzdm(:) = 0._dp

      dvxdr(:) = 0._dp
      dvxdn(:) = 0._dp
      dvydr(:) = 0._dp
      dvydn(:) = 0._dp
      dvzdr(:) = 0._dp
      dvzdn(:) = 0._dp

      dwxdr(:) = 0._dp
      dwxdl(:) = 0._dp
      dwydr(:) = 0._dp
      dwydl(:) = 0._dp
      dwzdr(:) = 0._dp
      dwzdl(:) = 0._dp

      dtxdr(:) = 0._dp
      dtxdm(:) = 0._dp
      dtxdn(:) = 0._dp
      dtxdl(:) = 0._dp
      dtxde(:) = 0._dp
      dtydr(:) = 0._dp
      dtydm(:) = 0._dp
      dtydn(:) = 0._dp
      dtydl(:) = 0._dp
      dtyde(:) = 0._dp
      dtzdr(:) = 0._dp
      dtzdm(:) = 0._dp
      dtzdn(:) = 0._dp
      dtzdl(:) = 0._dp
      dtzde(:) = 0._dp

      duxavgdr(:) = 0._dp
      duxavgdm(:) = 0._dp
      dvxavgdr(:) = 0._dp
      dvxavgdn(:) = 0._dp
      dwxavgdr(:) = 0._dp
      dwxavgdl(:) = 0._dp
      dtxavgdr(:) = 0._dp
      dtxavgdm(:) = 0._dp
      dtxavgdn(:) = 0._dp
      dtxavgdl(:) = 0._dp
      dtxavgde(:) = 0._dp

      duyavgdr(:) = 0._dp
      duyavgdm(:) = 0._dp
      dvyavgdr(:) = 0._dp
      dvyavgdn(:) = 0._dp
      dwyavgdr(:) = 0._dp
      dwyavgdl(:) = 0._dp
      dtyavgdr(:) = 0._dp
      dtyavgdm(:) = 0._dp
      dtyavgdn(:) = 0._dp
      dtyavgdl(:) = 0._dp
      dtyavgde(:) = 0._dp

      duzavgdr(:) = 0._dp
      duzavgdm(:) = 0._dp
      dvzavgdr(:) = 0._dp
      dvzavgdn(:) = 0._dp
      dwzavgdr(:) = 0._dp
      dwzavgdl(:) = 0._dp
      dtzavgdr(:) = 0._dp
      dtzavgdm(:) = 0._dp
      dtzavgdn(:) = 0._dp
      dtzavgdl(:) = 0._dp
      dtzavgde(:) = 0._dp

!     zero out some derivatives of viscosity stuff; we may
!     or may not fill these in with values later

      drmudr(:) = 0.0_dp
      drmudm(:) = 0.0_dp
      drmudn(:) = 0.0_dp
      drmudl(:) = 0.0_dp
      drmude(:) = 0.0_dp
      drmudt(:) = 0.0_dp

      dumudr(:) = 0.0_dp
      dumudm(:) = 0.0_dp
      dumudn(:) = 0.0_dp
      dumudl(:) = 0.0_dp
      dumude(:) = 0.0_dp
      dumudt(:) = 0.0_dp

      dvmudr(:) = 0.0_dp
      dvmudm(:) = 0.0_dp
      dvmudn(:) = 0.0_dp
      dvmudl(:) = 0.0_dp
      dvmude(:) = 0.0_dp
      dvmudt(:) = 0.0_dp

      dwmudr(:) = 0.0_dp
      dwmudm(:) = 0.0_dp
      dwmudn(:) = 0.0_dp
      dwmudl(:) = 0.0_dp
      dwmude(:) = 0.0_dp
      dwmudt(:) = 0.0_dp

! Compute cell averages, cell center, and set up some local solution arrays

      node_loop_1 : do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

!       global node number

        node = c2n(i,n)

        x_node(i) = x(node)
        y_node(i) = y(node)
        z_node(i) = z(node)

        rho_node(i) = qnode(1,node)
        u_node(i) = qnode(2,node)/qnode(1,node)
        v_node(i) = qnode(3,node)/qnode(1,node)
        w_node(i) = qnode(4,node)/qnode(1,node)
        e_node(i) = qnode(5,node)
        p_node(i) = gm1*(e_node(i) - my_haf*rho_node(i)*(u_node(i)*u_node(i) + &
                                                         v_node(i)*v_node(i) + &
                                                         w_node(i)*w_node(i)))
        t_node(i)  = gamma*p_node(i)/rho_node(i)
        if ( ivisc == 6 ) turb_node(i) = turb(1,node)
        mu_node(i) = (my_1 + cstar)/(t_node(i) + cstar)*t_node(i)**my_1p5
        nu_node(i) = mu_node(i)/rho_node(i)
        rmucgp     = rmucgp + mu_node(i)*cgp
        mu_node(i) = mu_node(i) + amut(node)
        rmucgp     = rmucgp + amut(node)*cgpt

        rmu = rmu + mu_node(i)
        umu = umu + u_node(i) * mu_node(i)
        vmu = vmu + v_node(i) * mu_node(i)
        wmu = wmu + w_node(i) * mu_node(i)

      end do node_loop_1

! save off some sums we will need later for derivatives

      usum = umu
      vsum = vmu
      wsum = wmu

!     get cell averages by dividing by the number of nodes that contributed

      fact = 1._dp / real(nodes_local, dp)

      rmu = rmu*fact
      umu = umu*fact/rmu
      vmu = vmu*fact/rmu
      wmu = wmu*fact/rmu
      rmucgp = rmucgp*fact

!     compute the cell center (must loop over node_per_cell even in 2D)

      do i = 1, node_per_cell

!       global node number

        node = c2n(i,n)

        xc  =  xc + x(node)
        yc  =  yc + y(node)
        zc  =  zc + z(node)

      end do

      fact = 1._dp / real(node_per_cell, dp)

      xc  =  xc*fact
      yc  =  yc*fact
      zc  =  zc*fact

!     derivatives of (primitive) variables wrt conserved variables
!     for each node in the cell

!     nomenclature:
!       dudr = d(u)/d(rho)
!       dudm = d(u)/d(rho*u)
!       dvdr = d(v)/d(rho)
!       dvdn = d(v)/d(rho*v)
!       dwdr = d(w)/d(rho)
!       dwdl = d(w)/d(rho*w)
!       dtdr = d(t)/d(rho)
!       dtdm = d(t)/d(rho*u)
!       dtdn = d(t)/d(rho*v)
!       dtdl = d(t)/d(rho*w)
!       dtde = d(t)/d(e)

      node_loop_2 : do i_local = 1, nodes_local

!       local node number

        i = node_map(i_local)

        dudr(i) = - u_node(i) / rho_node(i)
        dudm(i) =        my_1 / rho_node(i)
        dudn(i) = 0.0_dp
        dudl(i) = 0.0_dp
        dude(i) = 0.0_dp

        dvdr(i) = - v_node(i) / rho_node(i)
        dvdm(i) = 0.0_dp
        dvdn(i) =        my_1 / rho_node(i)
        dvdl(i) = 0.0_dp
        dvde(i) = 0.0_dp

        dwdr(i) = - w_node(i) / rho_node(i)
        dwdm(i) = 0.0_dp
        dwdn(i) = 0.0_dp
        dwdl(i) =        my_1 / rho_node(i)
        dwde(i) = 0.0_dp

        dtdr(i) =   ggm1 * ( u_node(i)*u_node(i) +                             &
                             v_node(i)*v_node(i) +                             &
                             w_node(i)*w_node(i) -                             &
                             e_node(i)/rho_node(i) ) / rho_node(i)
        dtdm(i) = - ggm1 * u_node(i) / rho_node(i)
        dtdn(i) = - ggm1 * v_node(i) / rho_node(i)
        dtdl(i) = - ggm1 * w_node(i) / rho_node(i)
        dtde(i) =   ggm1 / rho_node(i)

        dmudr(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *            &
                   (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdr(i)
        dmudm(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *            &
                   (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdm(i)
        dmudn(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *            &
                   (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdn(i)
        dmudl(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *            &
                   (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdl(i)
        dmude(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *            &
                   (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtde(i)

        select case ( ivisc )
        case (2,8)
          dmutdr(i) = 0.0_dp
          dmutdm(i) = 0.0_dp
          dmutdn(i) = 0.0_dp
          dmutdl(i) = 0.0_dp
          dmutde(i) = 0.0_dp
          dmutdt(i) = 0.0_dp
        case (6)

          bp_model : if ( use_bp_model ) then

            dnudr = dmudr(i)/rho_node(i) - nu_node(i)/rho_node(i)
            dnudm = dmudm(i)/rho_node(i)
            dnudn = dmudn(i)/rho_node(i)
            dnudl = dmudl(i)/rho_node(i)
            dnude = dmude(i)/rho_node(i)

            chi = turb_node(i) / nu_node(i)
              dchidr = -turb_node(i)*dnudr/nu_node(i)/nu_node(i)
              dchidm = -turb_node(i)*dnudm/nu_node(i)/nu_node(i)
              dchidn = -turb_node(i)*dnudn/nu_node(i)/nu_node(i)
              dchidl = -turb_node(i)*dnudl/nu_node(i)/nu_node(i)
              dchide = -turb_node(i)*dnude/nu_node(i)/nu_node(i)
              dchidt = 1.0_dp / nu_node(i)

            psi = chi
              dpsidr = dchidr
              dpsidm = dchidm
              dpsidn = dchidn
              dpsidl = dchidl
              dpside = dchide
              dpsidt = dchidt

            if ( chi < 10._dp ) then
              base = 1.0_dp + exp(20.0_dp*chi)
                dbasedr = exp(20.0_dp*chi)*20.0_dp*dchidr
                dbasedm = exp(20.0_dp*chi)*20.0_dp*dchidm
                dbasedn = exp(20.0_dp*chi)*20.0_dp*dchidn
                dbasedl = exp(20.0_dp*chi)*20.0_dp*dchidl
                dbasede = exp(20.0_dp*chi)*20.0_dp*dchide
                dbasedt = exp(20.0_dp*chi)*20.0_dp*dchidt
              psi = 0.05_dp * log(base)
                dpsidr = 0.05_dp/base*dbasedr
                dpsidm = 0.05_dp/base*dbasedm
                dpsidn = 0.05_dp/base*dbasedn
                dpsidl = 0.05_dp/base*dbasedl
                dpside = 0.05_dp/base*dbasede
                dpsidt = 0.05_dp/base*dbasedt
            endif

            fv1 = psi**3 / (psi**3 + cv1**3)
              fv1dr = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidr                 &
                       - psi**3*3.0_dp*psi**2*dpsidr ) / (psi**3 + cv1**3)**2
              fv1dm = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidm                 &
                       - psi**3*3.0_dp*psi**2*dpsidm ) / (psi**3 + cv1**3)**2
              fv1dn = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidn                 &
                       - psi**3*3.0_dp*psi**2*dpsidn ) / (psi**3 + cv1**3)**2
              fv1dl = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidl                 &
                       - psi**3*3.0_dp*psi**2*dpsidl ) / (psi**3 + cv1**3)**2
              fv1de = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpside                 &
                       - psi**3*3.0_dp*psi**2*dpside ) / (psi**3 + cv1**3)**2
              fv1dt = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidt                 &
                       - psi**3*3.0_dp*psi**2*dpsidt ) / (psi**3 + cv1**3)**2

!           mut = max( 0._dp , rho_node(i)*turb_node(i)*fv1 )

            if ( rho_node(i)*turb_node(i)*fv1 > 0.0_dp ) then
              dmutdr(i) = turb_node(i)*(rho_node(i)*fv1dr + fv1)
              dmutdm(i) = turb_node(i)*rho_node(i)*fv1dm
              dmutdn(i) = turb_node(i)*rho_node(i)*fv1dn
              dmutdl(i) = turb_node(i)*rho_node(i)*fv1dl
              dmutde(i) = turb_node(i)*rho_node(i)*fv1de
              dmutdt(i) = rho_node(i)*(turb_node(i)*fv1dt + fv1)
            else
              dmutdr(i) = 0.0_dp
              dmutdm(i) = 0.0_dp
              dmutdn(i) = 0.0_dp
              dmutdl(i) = 0.0_dp
              dmutde(i) = 0.0_dp
              dmutdt(i) = 0.0_dp
            endif

          else bp_model

            dnudr = dmudr(i)/rho_node(i) - nu_node(i)/rho_node(i)
            dnudm = dmudm(i)/rho_node(i)
            dnudn = dmudn(i)/rho_node(i)
            dnudl = dmudl(i)/rho_node(i)
            dnude = dmude(i)/rho_node(i)

            chi = turb_node(i) / nu_node(i)
            dchidr = -turb_node(i)*dnudr/nu_node(i)/nu_node(i)
            dchidm = -turb_node(i)*dnudm/nu_node(i)/nu_node(i)
            dchidn = -turb_node(i)*dnudn/nu_node(i)/nu_node(i)
            dchidl = -turb_node(i)*dnudl/nu_node(i)/nu_node(i)
            dchide = -turb_node(i)*dnude/nu_node(i)/nu_node(i)
            dchidt = 1.0_dp / nu_node(i)

            fv1 = chi**3/(chi**3 + cv1**3)

            fv1dr = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidr                    &
                  - chi**3*3.0_dp*chi**2*dchidr)/(chi**3+cv1**3)/(chi**3+cv1**3)
            fv1dm = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidm                    &
                  - chi**3*3.0_dp*chi**2*dchidm)/(chi**3+cv1**3)/(chi**3+cv1**3)
            fv1dn = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidn                    &
                  - chi**3*3.0_dp*chi**2*dchidn)/(chi**3+cv1**3)/(chi**3+cv1**3)
            fv1dl = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidl                    &
                  - chi**3*3.0_dp*chi**2*dchidl)/(chi**3+cv1**3)/(chi**3+cv1**3)
            fv1de = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchide                    &
                  - chi**3*3.0_dp*chi**2*dchide)/(chi**3+cv1**3)/(chi**3+cv1**3)
            fv1dt = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidt                    &
                  - chi**3*3.0_dp*chi**2*dchidt)/(chi**3+cv1**3)/(chi**3+cv1**3)

!           mut = rho_node(i)*turb_node(i)*fv1

            dmutdr(i) = turb_node(i)*(rho_node(i)*fv1dr + fv1)
            dmutdm(i) = turb_node(i)*rho_node(i)*fv1dm
            dmutdn(i) = turb_node(i)*rho_node(i)*fv1dn
            dmutdl(i) = turb_node(i)*rho_node(i)*fv1dl
            dmutde(i) = turb_node(i)*rho_node(i)*fv1de
            dmutdt(i) = rho_node(i)*(turb_node(i)*fv1dt + fv1)

          endif bp_model

        case default
          write(*,*) 'ivisc case not supported in dvisrhs_mix6: ', ivisc
          call lmpi_die
          stop
        end select

        drmudr(i) = dmudr(i) + dmutdr(i)
        drmudm(i) = dmudm(i) + dmutdm(i)
        drmudn(i) = dmudn(i) + dmutdn(i)
        drmudl(i) = dmudl(i) + dmutdl(i)
        drmude(i) = dmude(i) + dmutde(i)
        drmudt(i) =            dmutdt(i)

      end do node_loop_2

!     divide the viscosity sum derivatives by the averaging factor

      fact = 1._dp / real(nodes_local, dp)

      drmudr = drmudr*fact
      drmudm = drmudm*fact
      drmudn = drmudn*fact
      drmudl = drmudl*fact
      drmude = drmude*fact
      drmudt = drmudt*fact

! derivatives of umu, vmu, wmu

      node_loop_3 : do i_local = 1, nodes_local

        i = node_map(i_local)

        dumudr(i) = fact/rmu*(u_node(i)*drmudr(i)/fact + mu_node(i)*dudr(i))   &
                  - fact/rmu/rmu*usum*drmudr(i)
        dumudm(i) = fact/rmu*(u_node(i)*drmudm(i)/fact + mu_node(i)*dudm(i))   &
                  - fact/rmu/rmu*usum*drmudm(i)
        dumudn(i) = fact/rmu*(u_node(i)*drmudn(i)/fact + mu_node(i)*dudn(i))   &
                  - fact/rmu/rmu*usum*drmudn(i)
        dumudl(i) = fact/rmu*(u_node(i)*drmudl(i)/fact + mu_node(i)*dudl(i))   &
                  - fact/rmu/rmu*usum*drmudl(i)
        dumude(i) = fact/rmu*(u_node(i)*drmude(i)/fact + mu_node(i)*dude(i))   &
                  - fact/rmu/rmu*usum*drmude(i)
        dumudt(i) = fact/rmu*(u_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*usum*drmudt(i)

        dvmudr(i) = fact/rmu*(v_node(i)*drmudr(i)/fact + mu_node(i)*dvdr(i))   &
                  - fact/rmu/rmu*vsum*drmudr(i)
        dvmudm(i) = fact/rmu*(v_node(i)*drmudm(i)/fact + mu_node(i)*dvdm(i))   &
                  - fact/rmu/rmu*vsum*drmudm(i)
        dvmudn(i) = fact/rmu*(v_node(i)*drmudn(i)/fact + mu_node(i)*dvdn(i))   &
                  - fact/rmu/rmu*vsum*drmudn(i)
        dvmudl(i) = fact/rmu*(v_node(i)*drmudl(i)/fact + mu_node(i)*dvdl(i))   &
                  - fact/rmu/rmu*vsum*drmudl(i)
        dvmude(i) = fact/rmu*(v_node(i)*drmude(i)/fact + mu_node(i)*dvde(i))   &
                  - fact/rmu/rmu*vsum*drmude(i)
        dvmudt(i) = fact/rmu*(v_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*vsum*drmudt(i)

        dwmudr(i) = fact/rmu*(w_node(i)*drmudr(i)/fact + mu_node(i)*dwdr(i))   &
                  - fact/rmu/rmu*wsum*drmudr(i)
        dwmudm(i) = fact/rmu*(w_node(i)*drmudm(i)/fact + mu_node(i)*dwdm(i))   &
                  - fact/rmu/rmu*wsum*drmudm(i)
        dwmudn(i) = fact/rmu*(w_node(i)*drmudn(i)/fact + mu_node(i)*dwdn(i))   &
                  - fact/rmu/rmu*wsum*drmudn(i)
        dwmudl(i) = fact/rmu*(w_node(i)*drmudl(i)/fact + mu_node(i)*dwdl(i))   &
                  - fact/rmu/rmu*wsum*drmudl(i)
        dwmude(i) = fact/rmu*(w_node(i)*drmude(i)/fact + mu_node(i)*dwde(i))   &
                  - fact/rmu/rmu*wsum*drmude(i)
        dwmudt(i) = fact/rmu*(w_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*wsum*drmudt(i)

      end do node_loop_3

! Get the gradients in the primal cell via Green-Gauss

      q_node(1,:) = u_node(:)
      q_node(2,:) = v_node(:)
      q_node(3,:) = w_node(:)
      q_node(4,:) = t_node(:)

      call cell_gradients(edges_local, max_node_per_cell,face_per_cell,        &
                          x_node, y_node, z_node, 4, q_node, local_f2n,        &
                          e2n_2d, gradx_cell, grady_cell, gradz_cell,          &
                          cell_vol, nx, ny, nz)

      uxavg = gradx_cell(1)
      vxavg = gradx_cell(2)
      wxavg = gradx_cell(3)
      txavg = gradx_cell(4)

      uyavg = grady_cell(1)
      vyavg = grady_cell(2)
      wyavg = grady_cell(3)
      tyavg = grady_cell(4)

      uzavg = gradz_cell(1)
      vzavg = gradz_cell(2)
      wzavg = gradz_cell(3)
      tzavg = gradz_cell(4)

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note: these are with respect to the primitive variables

      call cell_jacobians(edges_local, max_node_per_cell, face_per_cell,       &
                          x_node, y_node, z_node, local_f2n, e2n_2d,           &
                          dgradx_celldq, dgrady_celldq, dgradz_celldq,         &
                          cell_vol, nx, ny, nz)

!     convert to jacobians with respect to conservative variables

      if (twod) then

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          duxavgdr(i) = dudr(i)*dgradx_celldq(i)
          duxavgdm(i) = dudm(i)*dgradx_celldq(i)
          dwxavgdr(i) = dwdr(i)*dgradx_celldq(i)
          dwxavgdl(i) = dwdl(i)*dgradx_celldq(i)
          dtxavgdr(i) = dtdr(i)*dgradx_celldq(i)
          dtxavgdm(i) = dtdm(i)*dgradx_celldq(i)
          dtxavgdl(i) = dtdl(i)*dgradx_celldq(i)
          dtxavgde(i) = dtde(i)*dgradx_celldq(i)

          duzavgdr(i) = dudr(i)*dgradz_celldq(i)
          duzavgdm(i) = dudm(i)*dgradz_celldq(i)
          dwzavgdr(i) = dwdr(i)*dgradz_celldq(i)
          dwzavgdl(i) = dwdl(i)*dgradz_celldq(i)
          dtzavgdr(i) = dtdr(i)*dgradz_celldq(i)
          dtzavgdm(i) = dtdm(i)*dgradz_celldq(i)
          dtzavgdl(i) = dtdl(i)*dgradz_celldq(i)
          dtzavgde(i) = dtde(i)*dgradz_celldq(i)

        end do

      else

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          duxavgdr(i) = dudr(i)*dgradx_celldq(i)
          duxavgdm(i) = dudm(i)*dgradx_celldq(i)
          dvxavgdr(i) = dvdr(i)*dgradx_celldq(i)
          dvxavgdn(i) = dvdn(i)*dgradx_celldq(i)
          dwxavgdr(i) = dwdr(i)*dgradx_celldq(i)
          dwxavgdl(i) = dwdl(i)*dgradx_celldq(i)
          dtxavgdr(i) = dtdr(i)*dgradx_celldq(i)
          dtxavgdm(i) = dtdm(i)*dgradx_celldq(i)
          dtxavgdn(i) = dtdn(i)*dgradx_celldq(i)
          dtxavgdl(i) = dtdl(i)*dgradx_celldq(i)
          dtxavgde(i) = dtde(i)*dgradx_celldq(i)

          duyavgdr(i) = dudr(i)*dgrady_celldq(i)
          duyavgdm(i) = dudm(i)*dgrady_celldq(i)
          dvyavgdr(i) = dvdr(i)*dgrady_celldq(i)
          dvyavgdn(i) = dvdn(i)*dgrady_celldq(i)
          dwyavgdr(i) = dwdr(i)*dgrady_celldq(i)
          dwyavgdl(i) = dwdl(i)*dgrady_celldq(i)
          dtyavgdr(i) = dtdr(i)*dgrady_celldq(i)
          dtyavgdm(i) = dtdm(i)*dgrady_celldq(i)
          dtyavgdn(i) = dtdn(i)*dgrady_celldq(i)
          dtyavgdl(i) = dtdl(i)*dgrady_celldq(i)
          dtyavgde(i) = dtde(i)*dgrady_celldq(i)

          duzavgdr(i) = dudr(i)*dgradz_celldq(i)
          duzavgdm(i) = dudm(i)*dgradz_celldq(i)
          dvzavgdr(i) = dvdr(i)*dgradz_celldq(i)
          dvzavgdn(i) = dvdn(i)*dgradz_celldq(i)
          dwzavgdr(i) = dwdr(i)*dgradz_celldq(i)
          dwzavgdl(i) = dwdl(i)*dgradz_celldq(i)
          dtzavgdr(i) = dtdr(i)*dgradz_celldq(i)
          dtzavgdm(i) = dtdm(i)*dgradz_celldq(i)
          dtzavgdn(i) = dtdn(i)*dgradz_celldq(i)
          dtzavgdl(i) = dtdl(i)*dgradz_celldq(i)
          dtzavgde(i) = dtde(i)*dgradz_celldq(i)

        end do

      end if

! Check cell-face angles (dot product) - if greater that a specified value,
! we skip the contribution from this cell (currently for 3D cases only)

!     note: only need to check upper part of matrix chk_norm since
!     dot products commute (A*B = B*A) and the diagonal indicates
!     the dot product of a face with itself; also note that nx, ny, nz
!     are not unit normals so we must scale the dot product accordingly

      if (ivgrd == 1 .and. .not.(twod)) then

        big_angle = 0

        do i=1,face_per_cell

          do j=i+1,face_per_cell

            if (chk_norm(i,j) > 0) then
              dot = nx(i)*nx(j) + ny(i)*ny(j) + nz(i)*nz(j)
!             scale to unit normals
              dot = dot / sqrt(nx(i)*nx(i) + ny(i)*ny(i) + nz(i)*nz(i))
              dot = dot / sqrt(nx(j)*nx(j) + ny(j)*ny(j) + nz(j)*nz(j))
              if (dot >= my_mxd) big_angle = big_angle + 1
            end if

          end do

        end do

        if ( big_angle > 0 )  cycle cell_loop

      end if

! Next loop over the edges in the cell and get each ones'
! contribution to the jacobian

      edge_loop : do ie_local = 1,edges_local

!       local edge number

        ie = edge_map(ie_local)

!       local node numbers of edge endpoints

        n1_loc = local_e2n(ie,1)
        n2_loc = local_e2n(ie,2)

!       global node numbers of edge endpoints

        n1 = c2n(n1_loc,n)
        n2 = c2n(n2_loc,n)

! Get this edges' contributiuon to the dual normal and area

!       edge midpoint

        xm = (x(n1) + x(n2))/2._dp
        ym = (y(n1) + y(n2))/2._dp
        zm = (z(n1) + z(n2))/2._dp

!       compute left face centroid

        n3 = c2n(local_e2n(ie,3),n)

        if (local_e2n(ie,4) /= 0) then

!         quad cell face

          n4 = c2n(local_e2n(ie,4),n)

          xl = (x(n1) + x(n2) + x(n3) + x(n4))/4._dp
          yl = (y(n1) + y(n2) + y(n3) + y(n4))/4._dp
          zl = (z(n1) + z(n2) + z(n3) + z(n4))/4._dp

        else

!         tria cell face

          xl = (x(n1) + x(n2) + x(n3))/3._dp
          yl = (y(n1) + y(n2) + y(n3))/3._dp
          zl = (z(n1) + z(n2) + z(n3))/3._dp

        end if

!       compute right face centroid

        n5 = c2n(local_e2n(ie,5),n)

        if (local_e2n(ie,6) /= 0) then

!         quad cell face

          n6 = c2n(local_e2n(ie,6),n)

          xr = (x(n1) + x(n2) + x(n5) + x(n6))/4._dp
          yr = (y(n1) + y(n2) + y(n5) + y(n6))/4._dp
          zr = (z(n1) + z(n2) + z(n5) + z(n6))/4._dp

        else

!         tria cell face

          xr = (x(n1) + x(n2) + x(n5))/3._dp
          yr = (y(n1) + y(n2) + y(n5))/3._dp
          zr = (z(n1) + z(n2) + z(n5))/3._dp

        end if

!       get the contributions to dual normals from the two triangles
!       that form part of the dual-cell surface

!       area of dual triangle xm-xr-xc + area of dual triangle xm-xc-xl

        areax = ((yc-ym)*(zl-zr) - (zc-zm)*(yl-yr))*my_haf
        areay = ((zc-zm)*(xl-xr) - (xc-xm)*(zl-zr))*my_haf
        areaz = ((xc-xm)*(yl-yr) - (yc-ym)*(xl-xr))*my_haf

! Get (jacobians of) gradients at the dual face; either take gradients for
! this piece of the dual face to be the same as the cell-average gradient
! computed above  (which is what the legacy FUN3D solver does for tets),
! or combine with the edge-gradient to increase h-ellipticity on
! non-simplicial meshes.

! For tets in 3D or prisms in 2D, edge gradients add no new info
! so there is no need to do the extra work

        edge_gradients = use_edge_gradients

        if (type_cell == 'tet') edge_gradients = .false.
        if (twod .and. type_cell == 'prz') edge_gradients = .false.

        include_edge_gradients : if (edge_gradients) then

!         ex, ey, ez is unit vector along edge direction

          ex   = x(n2) - x(n1)
          ey   = y(n2) - y(n1)
          ez   = z(n2) - z(n1)
          disi = my_1/sqrt( ex**2 + ey**2 + ez**2 )

          ex   = ex*disi
          ey   = ey*disi
          ez   = ez*disi

          areai = 1._dp/sqrt( areax**2 + areay**2 + areaz**2 )
          xnf   = areax*areai
          ynf   = areay*areai
          znf   = areaz*areai

          augment_weight = edge_augment_weight( gradient_construction_rhs, &
                                                ex, ey, ez, xnf, ynf, znf )

!         primitive variables at nodes 1 and 2

          u1 = qnode(2,n1)/qnode(1,n1)
          v1 = qnode(3,n1)/qnode(1,n1)
          w1 = qnode(4,n1)/qnode(1,n1)
          e1 = qnode(5,n1)
          p1 = gm1*(e1 - my_haf*qnode(1,n1)*(u1*u1 + v1*v1 + w1*w1))
          t1 = gamma*p1/qnode(1,n1)

          u2 = qnode(2,n2)/qnode(1,n2)
          v2 = qnode(3,n2)/qnode(1,n2)
          w2 = qnode(4,n2)/qnode(1,n2)
          e2 = qnode(5,n2)
          p2 = gm1*(e2 - my_haf*qnode(1,n2)*(u2*u2 + v2*v2 + w2*w2))
          t2 = gamma*p2/qnode(1,n2)

!           directional gradients along edge

            egradu = ( u2 - u1 )*disi
            egradv = ( v2 - v1 )*disi
            egradw = ( w2 - w1 )*disi
            egradt = ( t2 - t1 )*disi

!           average Green-Gauss gradient in edge direction

            gradu_xi = uxavg*ex + uyavg*ey + uzavg*ez
            gradv_xi = vxavg*ex + vyavg*ey + vzavg*ez
            gradw_xi = wxavg*ex + wyavg*ey + wzavg*ez
            gradt_xi = txavg*ex + tyavg*ey + tzavg*ez

!           combine gradient contributions from edge and primal cell

            ux = uxavg + ( egradu - gradu_xi )*augment_weight(1)
            uy = uyavg + ( egradu - gradu_xi )*augment_weight(2)
            uz = uzavg + ( egradu - gradu_xi )*augment_weight(3)

            vx = vxavg + ( egradv - gradv_xi )*augment_weight(1)
            vy = vyavg + ( egradv - gradv_xi )*augment_weight(2)
            vz = vzavg + ( egradv - gradv_xi )*augment_weight(3)

            wx = wxavg + ( egradw - gradw_xi )*augment_weight(1)
            wy = wyavg + ( egradw - gradw_xi )*augment_weight(2)
            wz = wzavg + ( egradw - gradw_xi )*augment_weight(3)

            tx = txavg + ( egradt - gradt_xi )*augment_weight(1)
            ty = tyavg + ( egradt - gradt_xi )*augment_weight(2)
            tz = tzavg + ( egradt - gradt_xi )*augment_weight(3)

!           write the above gradients as
!           ux = uxavg - (uxavg*ex + uyavg*ey + uzavg*ez)*ex + (u2-u1)*disi*ex
!                <-----------------avg_term---------------->   <--edge_term-->
!           etc.

!           first get the avg_term pieces; all active cell nodes contribute

            do i_local = 1, nodes_local

!             local node number

              i = node_map(i_local)

!             u-gradient avg terms

              dgradu_xidr = duxavgdr(i)*ex + duyavgdr(i)*ey +  duzavgdr(i)*ez
              dgradu_xidm = duxavgdm(i)*ex + duyavgdm(i)*ey +  duzavgdm(i)*ez

              duxdr(i) = duxavgdr(i) - dgradu_xidr*augment_weight(1)
              duxdm(i) = duxavgdm(i) - dgradu_xidm*augment_weight(1)
              duydr(i) = duyavgdr(i) - dgradu_xidr*augment_weight(2)
              duydm(i) = duyavgdm(i) - dgradu_xidm*augment_weight(2)
              duzdr(i) = duzavgdr(i) - dgradu_xidr*augment_weight(3)
              duzdm(i) = duzavgdm(i) - dgradu_xidm*augment_weight(3)

!             v-gradient avg terms

              dgradv_xidr = dvxavgdr(i)*ex + dvyavgdr(i)*ey +  dvzavgdr(i)*ez
              dgradv_xidn = dvxavgdn(i)*ex + dvyavgdn(i)*ey +  dvzavgdn(i)*ez

              dvxdr(i) = dvxavgdr(i) - dgradv_xidr*augment_weight(1)
              dvxdn(i) = dvxavgdn(i) - dgradv_xidn*augment_weight(1)
              dvydr(i) = dvyavgdr(i) - dgradv_xidr*augment_weight(2)
              dvydn(i) = dvyavgdn(i) - dgradv_xidn*augment_weight(2)
              dvzdr(i) = dvzavgdr(i) - dgradv_xidr*augment_weight(3)
              dvzdn(i) = dvzavgdn(i) - dgradv_xidn*augment_weight(3)

!             w-gradient avg terms

              dgradw_xidr = dwxavgdr(i)*ex + dwyavgdr(i)*ey +  dwzavgdr(i)*ez
              dgradw_xidl = dwxavgdl(i)*ex + dwyavgdl(i)*ey +  dwzavgdl(i)*ez

              dwxdr(i) = dwxavgdr(i) - dgradw_xidr*augment_weight(1)
              dwxdl(i) = dwxavgdl(i) - dgradw_xidl*augment_weight(1)
              dwydr(i) = dwyavgdr(i) - dgradw_xidr*augment_weight(2)
              dwydl(i) = dwyavgdl(i) - dgradw_xidl*augment_weight(2)
              dwzdr(i) = dwzavgdr(i) - dgradw_xidr*augment_weight(3)
              dwzdl(i) = dwzavgdl(i) - dgradw_xidl*augment_weight(3)

!             t-gradient avg terms

              dgradt_xidr = dtxavgdr(i)*ex + dtyavgdr(i)*ey +  dtzavgdr(i)*ez
              dgradt_xidm = dtxavgdm(i)*ex + dtyavgdm(i)*ey +  dtzavgdm(i)*ez
              dgradt_xidn = dtxavgdn(i)*ex + dtyavgdn(i)*ey +  dtzavgdn(i)*ez
              dgradt_xidl = dtxavgdl(i)*ex + dtyavgdl(i)*ey +  dtzavgdl(i)*ez
              dgradt_xide = dtxavgde(i)*ex + dtyavgde(i)*ey +  dtzavgde(i)*ez

              dtxdr(i) = dtxavgdr(i) - dgradt_xidr*augment_weight(1)
              dtxdm(i) = dtxavgdm(i) - dgradt_xidm*augment_weight(1)
              dtxdn(i) = dtxavgdn(i) - dgradt_xidn*augment_weight(1)
              dtxdl(i) = dtxavgdl(i) - dgradt_xidl*augment_weight(1)
              dtxde(i) = dtxavgde(i) - dgradt_xide*augment_weight(1)

              dtydr(i) = dtyavgdr(i) - dgradt_xidr*augment_weight(2)
              dtydm(i) = dtyavgdm(i) - dgradt_xidm*augment_weight(2)
              dtydn(i) = dtyavgdn(i) - dgradt_xidn*augment_weight(2)
              dtydl(i) = dtyavgdl(i) - dgradt_xidl*augment_weight(2)
              dtyde(i) = dtyavgde(i) - dgradt_xide*augment_weight(2)

              dtzdr(i) = dtzavgdr(i) - dgradt_xidr*augment_weight(3)
              dtzdm(i) = dtzavgdm(i) - dgradt_xidm*augment_weight(3)
              dtzdn(i) = dtzavgdn(i) - dgradt_xidn*augment_weight(3)
              dtzdl(i) = dtzavgdl(i) - dgradt_xidl*augment_weight(3)
              dtzde(i) = dtzavgde(i) - dgradt_xide*augment_weight(3)

            end do

!           next get the edge_term pieces; only the two edge nodes contribute
!           u-gradient edge terms

            duxdr(n1_loc) = duxdr(n1_loc) - dudr(n1_loc)*disi*augment_weight(1)
            duxdr(n2_loc) = duxdr(n2_loc) + dudr(n2_loc)*disi*augment_weight(1)
            duxdm(n1_loc) = duxdm(n1_loc) - dudm(n1_loc)*disi*augment_weight(1)
            duxdm(n2_loc) = duxdm(n2_loc) + dudm(n2_loc)*disi*augment_weight(1)
            duydr(n1_loc) = duydr(n1_loc) - dudr(n1_loc)*disi*augment_weight(2)
            duydr(n2_loc) = duydr(n2_loc) + dudr(n2_loc)*disi*augment_weight(2)
            duydm(n1_loc) = duydm(n1_loc) - dudm(n1_loc)*disi*augment_weight(2)
            duydm(n2_loc) = duydm(n2_loc) + dudm(n2_loc)*disi*augment_weight(2)
            duzdr(n1_loc) = duzdr(n1_loc) - dudr(n1_loc)*disi*augment_weight(3)
            duzdr(n2_loc) = duzdr(n2_loc) + dudr(n2_loc)*disi*augment_weight(3)
            duzdm(n1_loc) = duzdm(n1_loc) - dudm(n1_loc)*disi*augment_weight(3)
            duzdm(n2_loc) = duzdm(n2_loc) + dudm(n2_loc)*disi*augment_weight(3)

!           v-gradient edge terms

            dvxdr(n1_loc) = dvxdr(n1_loc) - dvdr(n1_loc)*disi*augment_weight(1)
            dvxdr(n2_loc) = dvxdr(n2_loc) + dvdr(n2_loc)*disi*augment_weight(1)
            dvxdn(n1_loc) = dvxdn(n1_loc) - dvdn(n1_loc)*disi*augment_weight(1)
            dvxdn(n2_loc) = dvxdn(n2_loc) + dvdn(n2_loc)*disi*augment_weight(1)
            dvydr(n1_loc) = dvydr(n1_loc) - dvdr(n1_loc)*disi*augment_weight(2)
            dvydr(n2_loc) = dvydr(n2_loc) + dvdr(n2_loc)*disi*augment_weight(2)
            dvydn(n1_loc) = dvydn(n1_loc) - dvdn(n1_loc)*disi*augment_weight(2)
            dvydn(n2_loc) = dvydn(n2_loc) + dvdn(n2_loc)*disi*augment_weight(2)
            dvzdr(n1_loc) = dvzdr(n1_loc) - dvdr(n1_loc)*disi*augment_weight(3)
            dvzdr(n2_loc) = dvzdr(n2_loc) + dvdr(n2_loc)*disi*augment_weight(3)
            dvzdn(n1_loc) = dvzdn(n1_loc) - dvdn(n1_loc)*disi*augment_weight(3)
            dvzdn(n2_loc) = dvzdn(n2_loc) + dvdn(n2_loc)*disi*augment_weight(3)

!           w-gradient edge terms

            dwxdr(n1_loc) = dwxdr(n1_loc) - dwdr(n1_loc)*disi*augment_weight(1)
            dwxdr(n2_loc) = dwxdr(n2_loc) + dwdr(n2_loc)*disi*augment_weight(1)
            dwxdl(n1_loc) = dwxdl(n1_loc) - dwdl(n1_loc)*disi*augment_weight(1)
            dwxdl(n2_loc) = dwxdl(n2_loc) + dwdl(n2_loc)*disi*augment_weight(1)
            dwydr(n1_loc) = dwydr(n1_loc) - dwdr(n1_loc)*disi*augment_weight(2)
            dwydr(n2_loc) = dwydr(n2_loc) + dwdr(n2_loc)*disi*augment_weight(2)
            dwydl(n1_loc) = dwydl(n1_loc) - dwdl(n1_loc)*disi*augment_weight(2)
            dwydl(n2_loc) = dwydl(n2_loc) + dwdl(n2_loc)*disi*augment_weight(2)
            dwzdr(n1_loc) = dwzdr(n1_loc) - dwdr(n1_loc)*disi*augment_weight(3)
            dwzdr(n2_loc) = dwzdr(n2_loc) + dwdr(n2_loc)*disi*augment_weight(3)
            dwzdl(n1_loc) = dwzdl(n1_loc) - dwdl(n1_loc)*disi*augment_weight(3)
            dwzdl(n2_loc) = dwzdl(n2_loc) + dwdl(n2_loc)*disi*augment_weight(3)

!           t-gradient edge terms

            dtxdr(n1_loc) = dtxdr(n1_loc) - dtdr(n1_loc)*disi*augment_weight(1)
            dtxdr(n2_loc) = dtxdr(n2_loc) + dtdr(n2_loc)*disi*augment_weight(1)
            dtxdm(n1_loc) = dtxdm(n1_loc) - dtdm(n1_loc)*disi*augment_weight(1)
            dtxdm(n2_loc) = dtxdm(n2_loc) + dtdm(n2_loc)*disi*augment_weight(1)
            dtxdn(n1_loc) = dtxdn(n1_loc) - dtdn(n1_loc)*disi*augment_weight(1)
            dtxdn(n2_loc) = dtxdn(n2_loc) + dtdn(n2_loc)*disi*augment_weight(1)
            dtxdl(n1_loc) = dtxdl(n1_loc) - dtdl(n1_loc)*disi*augment_weight(1)
            dtxdl(n2_loc) = dtxdl(n2_loc) + dtdl(n2_loc)*disi*augment_weight(1)
            dtxde(n1_loc) = dtxde(n1_loc) - dtde(n1_loc)*disi*augment_weight(1)
            dtxde(n2_loc) = dtxde(n2_loc) + dtde(n2_loc)*disi*augment_weight(1)
            dtydr(n1_loc) = dtydr(n1_loc) - dtdr(n1_loc)*disi*augment_weight(2)
            dtydr(n2_loc) = dtydr(n2_loc) + dtdr(n2_loc)*disi*augment_weight(2)
            dtydm(n1_loc) = dtydm(n1_loc) - dtdm(n1_loc)*disi*augment_weight(2)
            dtydm(n2_loc) = dtydm(n2_loc) + dtdm(n2_loc)*disi*augment_weight(2)
            dtydn(n1_loc) = dtydn(n1_loc) - dtdn(n1_loc)*disi*augment_weight(2)
            dtydn(n2_loc) = dtydn(n2_loc) + dtdn(n2_loc)*disi*augment_weight(2)
            dtydl(n1_loc) = dtydl(n1_loc) - dtdl(n1_loc)*disi*augment_weight(2)
            dtydl(n2_loc) = dtydl(n2_loc) + dtdl(n2_loc)*disi*augment_weight(2)
            dtyde(n1_loc) = dtyde(n1_loc) - dtde(n1_loc)*disi*augment_weight(2)
            dtyde(n2_loc) = dtyde(n2_loc) + dtde(n2_loc)*disi*augment_weight(2)
            dtzdr(n1_loc) = dtzdr(n1_loc) - dtdr(n1_loc)*disi*augment_weight(3)
            dtzdr(n2_loc) = dtzdr(n2_loc) + dtdr(n2_loc)*disi*augment_weight(3)
            dtzdm(n1_loc) = dtzdm(n1_loc) - dtdm(n1_loc)*disi*augment_weight(3)
            dtzdm(n2_loc) = dtzdm(n2_loc) + dtdm(n2_loc)*disi*augment_weight(3)
            dtzdn(n1_loc) = dtzdn(n1_loc) - dtdn(n1_loc)*disi*augment_weight(3)
            dtzdn(n2_loc) = dtzdn(n2_loc) + dtdn(n2_loc)*disi*augment_weight(3)
            dtzdl(n1_loc) = dtzdl(n1_loc) - dtdl(n1_loc)*disi*augment_weight(3)
            dtzdl(n2_loc) = dtzdl(n2_loc) + dtdl(n2_loc)*disi*augment_weight(3)
            dtzde(n1_loc) = dtzde(n1_loc) - dtde(n1_loc)*disi*augment_weight(3)
            dtzde(n2_loc) = dtzde(n2_loc) + dtde(n2_loc)*disi*augment_weight(3)

        else include_edge_gradients

!         just use Green-Gauss cell-average gradients (this
!         is what the baseline code does for tets)

          ux = uxavg
          uy = uyavg
          uz = uzavg

          vx = vxavg
          vy = vyavg
          vz = vzavg

          wx = wxavg
          wy = wyavg
          wz = wzavg

          tx = txavg
          ty = tyavg
          tz = tzavg

!         only have the unaltered, average green-gauss contributions;
!         all active cell nodes contribute

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           u-gradient avg terms

            duxdr(i) = duxavgdr(i)
            duxdm(i) = duxavgdm(i)
            duydr(i) = duyavgdr(i)
            duydm(i) = duyavgdm(i)
            duzdr(i) = duzavgdr(i)
            duzdm(i) = duzavgdm(i)

!           v-gradient avg terms

            dvxdr(i) = dvxavgdr(i)
            dvxdn(i) = dvxavgdn(i)
            dvydr(i) = dvyavgdr(i)
            dvydn(i) = dvyavgdn(i)
            dvzdr(i) = dvzavgdr(i)
            dvzdn(i) = dvzavgdn(i)

!           w-gradient avg terms

            dwxdr(i) = dwxavgdr(i)
            dwxdl(i) = dwxavgdl(i)
            dwydr(i) = dwyavgdr(i)
            dwydl(i) = dwyavgdl(i)
            dwzdr(i) = dwzavgdr(i)
            dwzdl(i) = dwzavgdl(i)

!           t-gradient avg terms

            dtxdr(i) = dtxavgdr(i)
            dtxdm(i) = dtxavgdm(i)
            dtxdn(i) = dtxavgdn(i)
            dtxdl(i) = dtxavgdl(i)
            dtxde(i) = dtxavgde(i)

            dtydr(i) = dtyavgdr(i)
            dtydm(i) = dtyavgdm(i)
            dtydn(i) = dtyavgdn(i)
            dtydl(i) = dtyavgdl(i)
            dtyde(i) = dtyavgde(i)

            dtzdr(i) = dtzavgdr(i)
            dtzdm(i) = dtzavgdm(i)
            dtzdn(i) = dtzavgdn(i)
            dtzdl(i) = dtzavgdl(i)
            dtzde(i) = dtzavgde(i)

          end do

        end if include_edge_gradients

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

        txx = rmu*(c43*ux - c23*vy - c23*wz)
        txy = rmu*(uy + vx)
        txz = rmu*(uz + wx)

        tyy = rmu*(c43*vy - c23*ux - c23*wz)
        tyz = rmu*(vz + wy)

        tzz = rmu*(c43*wz - c23*ux - c23*vy)

!       [nondimensionalization factor xmr ] * [ viscosity ]
!       [ unit normal and area ] at dual face

        rax = xmr*areax
        ray = xmr*areay
        raz = xmr*areaz

        vf2 = rax*txx + ray*txy + raz*txz
        vf3 = rax*txy + ray*tyy + raz*tyz
        vf4 = rax*txz + ray*tyz + raz*tzz

!       form some intermediate Jacobians at all nodes

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

! Stress pieces

          dtxxdr = rmu*(  c43*duxdr(i) - c23*dvydr(i) - c23*dwzdr(i))
          dtxxdm = rmu*(  c43*duxdm(i))
          dtxxdn = rmu*(- c23*dvydn(i))
          dtxxdl = rmu*(- c23*dwzdl(i))

          dtxydr = rmu*(duydr(i) + dvxdr(i))
          dtxydm = rmu*(duydm(i))
          dtxydn = rmu*(dvxdn(i))

          dtxzdr = rmu*(duzdr(i) + dwxdr(i))
          dtxzdm = rmu*(duzdm(i))
          dtxzdl = rmu*(dwxdl(i))

          dtyydr = rmu*(  c43*dvydr(i) - c23*duxdr(i) - c23*dwzdr(i))
          dtyydm = rmu*(- c23*duxdm(i))
          dtyydn = rmu*(  c43*dvydn(i))
          dtyydl = rmu*(- c23*dwzdl(i))

          dtyzdr = rmu*(dvzdr(i) + dwydr(i))
          dtyzdn = rmu*(dvzdn(i))
          dtyzdl = rmu*(dwydl(i))

          dtzzdr = rmu*(  c43*dwzdr(i) - c23*duxdr(i) - c23*dvydr(i))
          dtzzdm = rmu*(- c23*duxdm(i))
          dtzzdn = rmu*(- c23*dvydn(i))
          dtzzdl = rmu*(  c43*dwzdl(i))

! Add stress contributions

          dvf2dr(i) = rax*dtxxdr + ray*dtxydr + raz*dtxzdr
          dvf2dm(i) = rax*dtxxdm + ray*dtxydm + raz*dtxzdm
          dvf2dn(i) = rax*dtxxdn + ray*dtxydn
          dvf2dl(i) = rax*dtxxdl              + raz*dtxzdl
          dvf2de(i) = 0.0_dp
          dvf2dt(i) = 0.0_dp

          dvf3dr(i) = rax*dtxydr + ray*dtyydr + raz*dtyzdr
          dvf3dm(i) = rax*dtxydm + ray*dtyydm
          dvf3dn(i) = rax*dtxydn + ray*dtyydn + raz*dtyzdn
          dvf3dl(i) =              ray*dtyydl + raz*dtyzdl
          dvf3de(i) = 0.0_dp
          dvf3dt(i) = 0.0_dp

          dvf4dr(i) = rax*dtxzdr + ray*dtyzdr + raz*dtzzdr
          dvf4dm(i) = rax*dtxzdm              + raz*dtzzdm
          dvf4dn(i) =              ray*dtyzdn + raz*dtzzdn
          dvf4dl(i) = rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
          dvf4de(i) = 0.0_dp
          dvf4dt(i) = 0.0_dp

! Viscosity pieces

          dtxxdr = drmudr(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdm = drmudm(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdn = drmudn(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdl = drmudl(i)*(c43*ux - c23*vy - c23*wz)
          dtxxde = drmude(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdt = drmudt(i)*(c43*ux - c23*vy - c23*wz)

          dtxydr = drmudr(i)*(uy + vx)
          dtxydm = drmudm(i)*(uy + vx)
          dtxydn = drmudn(i)*(uy + vx)
          dtxydl = drmudl(i)*(uy + vx)
          dtxyde = drmude(i)*(uy + vx)
          dtxydt = drmudt(i)*(uy + vx)

          dtxzdr = drmudr(i)*(uz + wx)
          dtxzdm = drmudm(i)*(uz + wx)
          dtxzdn = drmudn(i)*(uz + wx)
          dtxzdl = drmudl(i)*(uz + wx)
          dtxzde = drmude(i)*(uz + wx)
          dtxzdt = drmudt(i)*(uz + wx)

          dtyydr = drmudr(i)*(c43*vy - c23*ux - c23*wz)
          dtyydm = drmudm(i)*(c43*vy - c23*ux - c23*wz)
          dtyydn = drmudn(i)*(c43*vy - c23*ux - c23*wz)
          dtyydl = drmudl(i)*(c43*vy - c23*ux - c23*wz)
          dtyyde = drmude(i)*(c43*vy - c23*ux - c23*wz)
          dtyydt = drmudt(i)*(c43*vy - c23*ux - c23*wz)

          dtyzdr = drmudr(i)*(vz + wy)
          dtyzdm = drmudm(i)*(vz + wy)
          dtyzdn = drmudn(i)*(vz + wy)
          dtyzdl = drmudl(i)*(vz + wy)
          dtyzde = drmude(i)*(vz + wy)
          dtyzdt = drmudt(i)*(vz + wy)

          dtzzdr = drmudr(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdm = drmudm(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdn = drmudn(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdl = drmudl(i)*(c43*wz - c23*ux - c23*vy)
          dtzzde = drmude(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdt = drmudt(i)*(c43*wz - c23*ux - c23*vy)

! Add viscosity contributions

          dvf2dr(i) = dvf2dr(i) + rax*dtxxdr + ray*dtxydr + raz*dtxzdr
          dvf2dm(i) = dvf2dm(i) + rax*dtxxdm + ray*dtxydm + raz*dtxzdm
          dvf2dn(i) = dvf2dn(i) + rax*dtxxdn + ray*dtxydn + raz*dtxzdn
          dvf2dl(i) = dvf2dl(i) + rax*dtxxdl + ray*dtxydl + raz*dtxzdl
          dvf2de(i) = dvf2de(i) + rax*dtxxde + ray*dtxyde + raz*dtxzde
          dvf2dt(i) = dvf2dt(i) + rax*dtxxdt + ray*dtxydt + raz*dtxzdt

          dvf3dr(i) = dvf3dr(i) + rax*dtxydr + ray*dtyydr + raz*dtyzdr
          dvf3dm(i) = dvf3dm(i) + rax*dtxydm + ray*dtyydm + raz*dtyzdm
          dvf3dn(i) = dvf3dn(i) + rax*dtxydn + ray*dtyydn + raz*dtyzdn
          dvf3dl(i) = dvf3dl(i) + rax*dtxydl + ray*dtyydl + raz*dtyzdl
          dvf3de(i) = dvf3de(i) + rax*dtxyde + ray*dtyyde + raz*dtyzde
          dvf3dt(i) = dvf3dt(i) + rax*dtxydt + ray*dtyydt + raz*dtyzdt

          dvf4dr(i) = dvf4dr(i) + rax*dtxzdr + ray*dtyzdr + raz*dtzzdr
          dvf4dm(i) = dvf4dm(i) + rax*dtxzdm + ray*dtyzdm + raz*dtzzdm
          dvf4dn(i) = dvf4dn(i) + rax*dtxzdn + ray*dtyzdn + raz*dtzzdn
          dvf4dl(i) = dvf4dl(i) + rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
          dvf4de(i) = dvf4de(i) + rax*dtxzde + ray*dtyzde + raz*dtzzde
          dvf4dt(i) = dvf4dt(i) + rax*dtxzdt + ray*dtyzdt + raz*dtzzdt

! Now do stress and viscosity pieces for vf5 all at once

!    vf5 = rax*tqx + ray*tqy + raz*tqz + (umu*vf2 + vmu*vf3 + wmu*vf4)

!         tqx = rmucgp*tx
            dtqxdr = rmucgp*dtxdr(i) + tx*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqxdm = rmucgp*dtxdm(i) + tx*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqxdn = rmucgp*dtxdn(i) + tx*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqxdl = rmucgp*dtxdl(i) + tx*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqxde = rmucgp*dtxde(i) + tx*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqxdt =                   tx*fact*(               cgpt*dmutdt(i))

!         tqy = rmucgp*ty
            dtqydr = rmucgp*dtydr(i) + ty*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqydm = rmucgp*dtydm(i) + ty*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqydn = rmucgp*dtydn(i) + ty*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqydl = rmucgp*dtydl(i) + ty*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqyde = rmucgp*dtyde(i) + ty*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqydt =                   ty*fact*(               cgpt*dmutdt(i))

!         tqz = rmucgp*tz
            dtqzdr = rmucgp*dtzdr(i) + tz*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqzdm = rmucgp*dtzdm(i) + tz*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqzdn = rmucgp*dtzdn(i) + tz*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqzdl = rmucgp*dtzdl(i) + tz*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqzde = rmucgp*dtzde(i) + tz*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqzdt =                   tz*fact*(               cgpt*dmutdt(i))

!         piece = umu*vf2 + vmu*vf3 + wmu*vf4
            dpiecedr = umu*dvf2dr(i) + vmu*dvf3dr(i) + wmu*dvf4dr(i) +         &
                       vf2*dumudr(i) + vf3*dvmudr(i) + vf4*dwmudr(i)
            dpiecedm = umu*dvf2dm(i) + vmu*dvf3dm(i) + wmu*dvf4dm(i) +         &
                       vf2*dumudm(i) + vf3*dvmudm(i) + vf4*dwmudm(i)
            dpiecedn = umu*dvf2dn(i) + vmu*dvf3dn(i) + wmu*dvf4dn(i) +         &
                       vf2*dumudn(i) + vf3*dvmudn(i) + vf4*dwmudn(i)
            dpiecedl = umu*dvf2dl(i) + vmu*dvf3dl(i) + wmu*dvf4dl(i) +         &
                       vf2*dumudl(i) + vf3*dvmudl(i) + vf4*dwmudl(i)
            dpiecede = umu*dvf2de(i) + vmu*dvf3de(i) + wmu*dvf4de(i) +         &
                       vf2*dumude(i) + vf3*dvmude(i) + vf4*dwmude(i)
            dpiecedt = umu*dvf2dt(i) + vmu*dvf3dt(i) + wmu*dvf4dt(i) +         &
                       vf2*dumudt(i) + vf3*dvmudt(i) + vf4*dwmudt(i)

          dvf5dr(i) = rax*dtqxdr + ray*dtqydr + raz*dtqzdr + dpiecedr
          dvf5dm(i) = rax*dtqxdm + ray*dtqydm + raz*dtqzdm + dpiecedm
          dvf5dn(i) = rax*dtqxdn + ray*dtqydn + raz*dtqzdn + dpiecedn
          dvf5dl(i) = rax*dtqxdl + ray*dtqydl + raz*dtqzdl + dpiecedl
          dvf5de(i) = rax*dtqxde + ray*dtqyde + raz*dtqzde + dpiecede
          dvf5dt(i) = rax*dtqxdt + ray*dtqydt + raz*dtqzdt + dpiecedt

        end do

! Assemble final Jacobian matrices into sparse matrix form

        factor = +my_1

        edge_node_loop : do ii = 1,2

!         diagonal contributions

!         local (i) and global (node) numbers

          if (ii == 1) then
            i = n1_loc
            node = c2n(n1_loc,n)
          else
            i = n2_loc
            node = c2n(n2_loc,n)
          end if

          factor = -my_1*factor

          local_contrib1 : if ( node <= nnodes0 ) then

            a(2,1) = factor*dvf2dr(i)
            a(2,2) = factor*dvf2dm(i)
            a(2,3) = factor*dvf2dn(i)
            a(2,4) = factor*dvf2dl(i)
            a(2,5) = factor*dvf2de(i)
            a(2,6) = factor*dvf2dt(i)

            a(3,1) = factor*dvf3dr(i)
            a(3,2) = factor*dvf3dm(i)
            a(3,3) = factor*dvf3dn(i)
            a(3,4) = factor*dvf3dl(i)
            a(3,5) = factor*dvf3de(i)
            a(3,6) = factor*dvf3dt(i)

            a(4,1) = factor*dvf4dr(i)
            a(4,2) = factor*dvf4dm(i)
            a(4,3) = factor*dvf4dn(i)
            a(4,4) = factor*dvf4dl(i)
            a(4,5) = factor*dvf4de(i)
            a(4,6) = factor*dvf4dt(i)

            a(5,1) = factor*dvf5dr(i)
            a(5,2) = factor*dvf5dm(i)
            a(5,3) = factor*dvf5dn(i)
            a(5,4) = factor*dvf5dl(i)
            a(5,5) = factor*dvf5de(i)
            a(5,6) = factor*dvf5dt(i)

            if ( form_matrix ) then

! Add contributions to stored adjoint matrix

              idiag = iau(node)

              aa(2,1,idiag) = aa(2,1,idiag) + a(2,1)
              aa(2,2,idiag) = aa(2,2,idiag) + a(2,2)
              aa(2,3,idiag) = aa(2,3,idiag) + a(2,3)
              aa(2,4,idiag) = aa(2,4,idiag) + a(2,4)
              aa(2,5,idiag) = aa(2,5,idiag) + a(2,5)
              if ( ivisc > 2 ) aa(2,6,idiag) = aa(2,6,idiag) + a(2,6)

              aa(3,1,idiag) = aa(3,1,idiag) + a(3,1)
              aa(3,2,idiag) = aa(3,2,idiag) + a(3,2)
              aa(3,3,idiag) = aa(3,3,idiag) + a(3,3)
              aa(3,4,idiag) = aa(3,4,idiag) + a(3,4)
              aa(3,5,idiag) = aa(3,5,idiag) + a(3,5)
              if ( ivisc > 2 ) aa(3,6,idiag) = aa(3,6,idiag) + a(3,6)

              aa(4,1,idiag) = aa(4,1,idiag) + a(4,1)
              aa(4,2,idiag) = aa(4,2,idiag) + a(4,2)
              aa(4,3,idiag) = aa(4,3,idiag) + a(4,3)
              aa(4,4,idiag) = aa(4,4,idiag) + a(4,4)
              aa(4,5,idiag) = aa(4,5,idiag) + a(4,5)
              if ( ivisc > 2 ) aa(4,6,idiag) = aa(4,6,idiag) + a(4,6)

              aa(5,1,idiag) = aa(5,1,idiag) + a(5,1)
              aa(5,2,idiag) = aa(5,2,idiag) + a(5,2)
              aa(5,3,idiag) = aa(5,3,idiag) + a(5,3)
              aa(5,4,idiag) = aa(5,4,idiag) + a(5,4)
              aa(5,5,idiag) = aa(5,5,idiag) + a(5,5)
              if ( ivisc > 2 ) aa(5,6,idiag) = aa(5,6,idiag) + a(5,6)

            endif

            if ( form_matvec ) then

! Add contributions to adjoint residual

              do ifcn = 1, nfunctions
                res(1,node,ifcn) = res(1,node,ifcn)                            &
                                 + a(2,1)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,1)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,1)*coltag(4,node)*rlam(4,node,ifcn)     &
                                 + a(5,1)*coltag(5,node)*rlam(5,node,ifcn)

                res(2,node,ifcn) = res(2,node,ifcn)                            &
                                 + a(2,2)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,2)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,2)*coltag(4,node)*rlam(4,node,ifcn)     &
                                 + a(5,2)*coltag(5,node)*rlam(5,node,ifcn)

                res(3,node,ifcn) = res(3,node,ifcn)                            &
                                 + a(2,3)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,3)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,3)*coltag(4,node)*rlam(4,node,ifcn)     &
                                 + a(5,3)*coltag(5,node)*rlam(5,node,ifcn)

                res(4,node,ifcn) = res(4,node,ifcn)                            &
                                 + a(2,4)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,4)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,4)*coltag(4,node)*rlam(4,node,ifcn)     &
                                 + a(5,4)*coltag(5,node)*rlam(5,node,ifcn)

                res(5,node,ifcn) = res(5,node,ifcn)                            &
                                 + a(2,5)*coltag(2,node)*rlam(2,node,ifcn)     &
                                 + a(3,5)*coltag(3,node)*rlam(3,node,ifcn)     &
                                 + a(4,5)*coltag(4,node)*rlam(4,node,ifcn)     &
                                 + a(5,5)*coltag(5,node)*rlam(5,node,ifcn)

                if ( ivisc > 2 ) then
                  res(6,node,ifcn) = res(6,node,ifcn)                          &
                                   + a(2,6)*coltag(2,node)*rlam(2,node,ifcn)   &
                                   + a(3,6)*coltag(3,node)*rlam(3,node,ifcn)   &
                                   + a(4,6)*coltag(4,node)*rlam(4,node,ifcn)   &
                                   + a(5,6)*coltag(5,node)*rlam(5,node,ifcn)
                endif
              end do
            endif

          end if local_contrib1

!         off-diagonal contributions
!         (place into transposed block locations)

          node_loop_4 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n(i,n)

            if (nodec == node) cycle node_loop_4   ! already did diagonal

            if ( nodec <= nnodes0 ) then

              a(2,1) = factor*dvf2dr(i)
              a(2,2) = factor*dvf2dm(i)
              a(2,3) = factor*dvf2dn(i)
              a(2,4) = factor*dvf2dl(i)
              a(2,5) = factor*dvf2de(i)
              a(2,6) = factor*dvf2dt(i)

              a(3,1) = factor*dvf3dr(i)
              a(3,2) = factor*dvf3dm(i)
              a(3,3) = factor*dvf3dn(i)
              a(3,4) = factor*dvf3dl(i)
              a(3,5) = factor*dvf3de(i)
              a(3,6) = factor*dvf3dt(i)

              a(4,1) = factor*dvf4dr(i)
              a(4,2) = factor*dvf4dm(i)
              a(4,3) = factor*dvf4dn(i)
              a(4,4) = factor*dvf4dl(i)
              a(4,5) = factor*dvf4de(i)
              a(4,6) = factor*dvf4dt(i)

              a(5,1) = factor*dvf5dr(i)
              a(5,2) = factor*dvf5dm(i)
              a(5,3) = factor*dvf5dn(i)
              a(5,4) = factor*dvf5dl(i)
              a(5,5) = factor*dvf5de(i)
              a(5,6) = factor*dvf5dt(i)

              if ( form_matrix ) then

! Determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == node) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                             node,' to the off diagonal of node ',nodec
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                aa(2,1,ioff) = aa(2,1,ioff) + a(2,1)
                aa(2,2,ioff) = aa(2,2,ioff) + a(2,2)
                aa(2,3,ioff) = aa(2,3,ioff) + a(2,3)
                aa(2,4,ioff) = aa(2,4,ioff) + a(2,4)
                aa(2,5,ioff) = aa(2,5,ioff) + a(2,5)
                if ( ivisc > 2 ) aa(2,6,ioff) = aa(2,6,ioff) + a(2,6)

                aa(3,1,ioff) = aa(3,1,ioff) + a(3,1)
                aa(3,2,ioff) = aa(3,2,ioff) + a(3,2)
                aa(3,3,ioff) = aa(3,3,ioff) + a(3,3)
                aa(3,4,ioff) = aa(3,4,ioff) + a(3,4)
                aa(3,5,ioff) = aa(3,5,ioff) + a(3,5)
                if ( ivisc > 2 ) aa(3,6,ioff) = aa(3,6,ioff) + a(3,6)

                aa(4,1,ioff) = aa(4,1,ioff) + a(4,1)
                aa(4,2,ioff) = aa(4,2,ioff) + a(4,2)
                aa(4,3,ioff) = aa(4,3,ioff) + a(4,3)
                aa(4,4,ioff) = aa(4,4,ioff) + a(4,4)
                aa(4,5,ioff) = aa(4,5,ioff) + a(4,5)
                if ( ivisc > 2 ) aa(4,6,ioff) = aa(4,6,ioff) + a(4,6)

                aa(5,1,ioff) = aa(5,1,ioff) + a(5,1)
                aa(5,2,ioff) = aa(5,2,ioff) + a(5,2)
                aa(5,3,ioff) = aa(5,3,ioff) + a(5,3)
                aa(5,4,ioff) = aa(5,4,ioff) + a(5,4)
                aa(5,5,ioff) = aa(5,5,ioff) + a(5,5)
                if ( ivisc > 2 ) aa(5,6,ioff) = aa(5,6,ioff) + a(5,6)

              endif

              if ( form_matvec ) then

! Add contributions to adjoint residual

                do ifcn = 1, nfunctions
                  res(1,nodec,ifcn) = res(1,nodec,ifcn)                        &
                                    + a(2,1)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,1)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,1)*coltag(4,node)*rlam(4,node,ifcn)  &
                                    + a(5,1)*coltag(5,node)*rlam(5,node,ifcn)

                  res(2,nodec,ifcn) = res(2,nodec,ifcn)                        &
                                    + a(2,2)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,2)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,2)*coltag(4,node)*rlam(4,node,ifcn)  &
                                    + a(5,2)*coltag(5,node)*rlam(5,node,ifcn)

                  res(3,nodec,ifcn) = res(3,nodec,ifcn)                        &
                                    + a(2,3)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,3)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,3)*coltag(4,node)*rlam(4,node,ifcn)  &
                                    + a(5,3)*coltag(5,node)*rlam(5,node,ifcn)

                  res(4,nodec,ifcn) = res(4,nodec,ifcn)                        &
                                    + a(2,4)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,4)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,4)*coltag(4,node)*rlam(4,node,ifcn)  &
                                    + a(5,4)*coltag(5,node)*rlam(5,node,ifcn)

                  res(5,nodec,ifcn) = res(5,nodec,ifcn)                        &
                                    + a(2,5)*coltag(2,node)*rlam(2,node,ifcn)  &
                                    + a(3,5)*coltag(3,node)*rlam(3,node,ifcn)  &
                                    + a(4,5)*coltag(4,node)*rlam(4,node,ifcn)  &
                                    + a(5,5)*coltag(5,node)*rlam(5,node,ifcn)

                  if ( ivisc > 2 ) then
                    res(6,nodec,ifcn) = res(6,nodec,ifcn)                      &
                                      + a(2,6)*coltag(2,node)*rlam(2,node,ifcn)&
                                      + a(3,6)*coltag(3,node)*rlam(3,node,ifcn)&
                                      + a(4,6)*coltag(4,node)*rlam(4,node,ifcn)&
                                      + a(5,6)*coltag(5,node)*rlam(5,node,ifcn)
                  endif
                end do
              endif

            end if

          end do node_loop_4

        end do edge_node_loop

      end do edge_loop

    end do cell_loop

  end subroutine dvisrhs_mix6

  include 'edge_augment_weight.f90'

end module residual_laminar

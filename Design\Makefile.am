DIST_SUBDIRS = Utils

bin_PROGRAMS = opt_driver rutro
bin_SCRIPTS = setupDesign cleanupDesign

LIBCORE_DIR=@libcore_path@
LIBTURB_DIR=@libturb_path@
PHYSICS_DIR=@top_builddir@/@PHYSICS_TYPE@
LIBSMEMRD_DIR=@top_builddir@/libsmemrd
LIBDDFB_DIR=@top_builddir@/libddfb
FUNCLIB_DIR=@top_builddir@/FuncLib90
LIBF90_DIR=@top_builddir@/LibF90
LIBDEFS_DIR=@top_builddir@/libdefs
LIBINIT_DIR=@top_builddir@/libinit
ENGINESIM_DIR=@top_builddir@/enginesim/src

AM_FCFLAGS = \
	$(FC_MODINC)$(LIBCORE_DIR) \
	$(FC_MODINC)$(LIBDEFS_DIR) \
	$(FC_MODINC)$(LIBTURB_DIR) \
	$(FC_MODINC)$(PHYSICS_DIR) \
	$(FC_MODINC)$(LIBSMEMRD_DIR) \
	$(FC_MODINC)$(LIBINIT_DIR) \
	$(FC_MODINC)$(LIBF90_DIR) \
	$(FC_MODINC)@top_builddir@

opt_driver_LDADD = \
	$(LIBF90_DIR)/libsink.a \
	$(LIBINIT_DIR)/libinit.a \
	$(LIBDDFB_DIR)/libDDFB.a \
	$(LIBSMEMRD_DIR)/libsmemrd.a \
	$(LIBTURB_DIR)/libturb.a \
	$(LIBDEFS_DIR)/libdefs.a \
	$(PHYSICS_DIR)/libFUN3DPhysics.a \
	$(ENGINESIM_DIR)/libenginesim.a


if BUILD_SSDC_SUPPORT
opt_driver_LDADD += -L@ssdclibrary@ -lssdc
endif

if BUILD_SPARSKIT_SUPPORT
opt_driver_LDADD += -L@sparskitlibrary@ -lskit
endif

opt_driver_LDADD += $(LIBCORE_DIR)/libcore.a

if BUILD_SBOOM_SUPPORT
opt_driver_LDADD += -L@SBOOMlibrary@ -lsboomadjoint
endif

if BUILD_PORT_SUPPORT
opt_driver_LDADD += -L@portlibrary@ -lport
endif

if BUILD_NPSOL_SUPPORT
opt_driver_LDADD += -L@npsollibrary@ -lopt
endif

if BUILD_SNOPT_SUPPORT
opt_driver_LDADD += -L@snoptlibrary@ -lsnopt -lsnprint -lblas
endif

if BUILD_DOT_SUPPORT
opt_driver_LDADD += -L@dotlibrary@ -lDOT2 -lgfortran
endif

if BUILD_KSOPT_SUPPORT
opt_driver_LDADD += -L@ksoptlibrary@ -lksopt
endif

if BUILD_DIRTLIB_SUPPORT
opt_driver_LDADD += \
	-L@dirtlibrary@ -ldirt -lp3d
endif

if BUILD_TECIO_SUPPORT
opt_driver_LDADD += @TECIOLIBS@
endif

if BUILD_SIXDOF_SUPPORT
opt_driver_LDADD += \
	-L@SIXDOFLIBS@/Motion/lib -lmo \
	-L@SIXDOFLIBS@/HT/lib -lht \
	-L@SIXDOFLIBS@/EXP/lib -lexp
endif

if BUILD_DYMORE_SUPPORT
opt_driver_LDADD += \
	-L@dymorelibrary@ -ldymore4
endif

if BUILD_RCAS_SDX_SUPPORT
opt_driver_LDADD += -L@sdxlibrary@ -lsdx
endif

if BUILD_CGNS_SUPPORT
opt_driver_LDADD += -L@CGNSlibrary@ -lcgns
endif

if BUILD_FORTRAN_C_INTEROP_SUPPORT
opt_driver_LDADD += -lstdc++
endif

opt_driver_LDADD += @F90_EXT_LIB@

opt_driver_SOURCES = \
	ammo.f90 \
	dots.F90 \
	ksopt.F90 \
	main.f90 \
	npsol_data.f90 \
	npsols.F90 \
	snopt.F90 \
	port.F90

rutro_SOURCES = rutro.c
rutro_LDADD =

setupDesign:
	@echo '#! /bin/sh' > $@
	@echo '' >> $@
	@echo 'points=1' >> $@
	@echo 'if test -n "$$1"' >> $@
	@echo 'then' >> $@
	@echo '  points=$$1' >> $@
	@echo '  shift' >> $@
	@echo 'fi' >> $@
	@echo '' >> $@
	@echo 'casedir=`pwd`' >> $@
	@echo 'optdir=`dirname $$0`' >> $@
	@echo 'configdir=`dirname $$optdir`' >> $@
	@echo 'fundir=`dirname $$configdir`' >> $@
	@echo '' >> $@
	@echo 'cat > opt_driver.$$$$ << EOF' >> $@
	@echo '"$$casedir"' >> $@
	@echo '"$$fundir"' >> $@
	@echo '"$$configdir"' >> $@
	@echo 'EOF' >> $@
	@echo '' >> $@
	@echo '$$optdir/opt_driver --setup_design $$points < opt_driver.$$$$ | tee README' >> $@
	@echo '' >> $@
	@echo '/bin/rm opt_driver.$$$$' >> $@
	@/bin/chmod ugo+x $@

cleanupDesign:
	@echo '#! /bin/sh' > $@
	@echo '' >> $@
	@echo 'for i in model.*' >> $@
	@echo 'do' >> $@
	@echo '  rm -f $$i/command_line.options \' >> $@
	@echo '        $$i/body_grouping.data \' >> $@
	@echo '        $$i/machinefile \' >> $@
	@echo '        $$i/port.output \' >> $@
	@echo '        $$i/rubber.data' >> $@
	@echo '  rm -f $$i/Flow/*_part.* \' >> $@
	@echo '        $$i/Flow/debug_verify \' >> $@
	@echo '        $$i/Flow/fun3d.nml \' >> $@
	@echo '        $$i/Flow/move_gmres.input \' >> $@
	@echo '        $$i/Flow/namelist.input \' >> $@
	@echo '        $$i/Flow/relaxation_log_linear \' >> $@
	@echo '        $$i/Flow/forces.dat \' >> $@
	@echo '        $$i/Flow/info_gridmoves \' >> $@
	@echo '        $$i/Flow/debug_gridmovesens \' >> $@
	@echo '        $$i/Flow/debug_gridmove \' >> $@
	@echo '        $$i/Flow/*.grid_info \' >> $@
	@echo '        $$i/Flow/*.cogsg \' >> $@
	@echo '        $$i/Flow/*.bc \' >> $@
	@echo '        $$i/Flow/*.ugrid \' >> $@
	@echo '        $$i/Flow/*.mapbc \' >> $@
	@echo '        $$i/Flow/*.mapbc.override \' >> $@
	@echo '        $$i/Flow/*.pr \' >> $@
	@echo '        $$i/Flow/*.mesh_info \' >> $@
	@echo '        $$i/Flow/*.tec \' >> $@
	@echo '        $$i/Flow/bc_lookup.dat \' >> $@
	@echo '        $$i/Flow/*.part_info \' >> $@
	@echo '        $$i/Flow/temp.commands \' >> $@
	@echo '        $$i/Flow/h_measures \' >> $@
	@echo '        $$i/Flow/*_flow.* \' >> $@
	@echo '        $$i/Flow/*.flow \' >> $@
	@echo '        $$i/Flow/*_hist.dat.iteration.* \' >> $@
	@echo '        $$i/Flow/*_tec_boundary.* \' >> $@
	@echo '        $$i/Flow/*_hist.dat \' >> $@
	@echo '        $$i/Flow/*_timing.tec \' >> $@
	@echo '        $$i/Flow/*.forces \' >> $@
	@echo '        $$i/Flow/fort.* \' >> $@
	@echo '        $$i/Flow/*tec_negative_volumes.plt \' >> $@
	@echo '        $$i/Flow/linear_systems_sr_flow \' >> $@
	@echo '        $$i/Flow/debug_verify \' >> $@
	@echo '        $$i/Flow/forces.tec \' >> $@
	@echo '        $$i/Flow/movie.tec \' >> $@
	@echo '        $$i/Flow/baseline.*' >> $@
	@echo '  rm -f $$i/Flow/datafiles/*' >> $@
	@echo '  rm -f $$i/Flow/SaveFiles/*' >> $@
	@echo '  rm -f $$i/Grid/linear_systems_sr_gridmove \' >> $@
	@echo '        $$i/Grid/debug_gridmove \' >> $@
	@echo '        $$i/Grid/debug_gridmovesens \' >> $@
	@echo '        $$i/Grid/fort.68 \' >> $@
	@echo '        $$i/Grid/fort.69 \' >> $@
	@echo '        $$i/Grid/fort.70 \' >> $@
	@echo '        $$i/Grid/info_gridmoves \' >> $@
	@echo '        $$i/Grid/GridMove_mixed_timing.tec \' >> $@
	@echo '        $$i/Grid/GridMove_mixed_hist.dat \' >> $@
	@echo '        $$i/Grid/temp.commands \' >> $@
	@echo '        $$i/Grid/move_gmres.input \' >> $@
	@echo '        $$i/Grid/*_rhs.*' >> $@
	@echo '  rm -f $$i/Rubberize/GP_Log \' >> $@
	@echo '        $$i/Rubberize/model.tec.*.sd1 \' >> $@
	@echo '        $$i/Rubberize/model.tec.* \' >> $@
	@echo '        $$i/Rubberize/after.plt \' >> $@
	@echo '        $$i/Rubberize/before.plt \' >> $@
	@echo '        $$i/Rubberize/new*.plt \' >> $@
	@echo '        $$i/Rubberize/customDV.* \' >> $@
	@echo '        $$i/Rubberize/design.usd.* \' >> $@
	@echo '        $$i/Rubberize/massoud.* \' >> $@
	@echo '        $$i/Rubberize/design.* \' >> $@
	@echo '        $$i/Rubberize/design.gp.* \' >> $@
	@echo '        $$i/Rubberize/surface_history/* \' >> $@
	@echo '        $$i/Rubberize/transforms.*' >> $@
	@echo '  rm -f $$i/Adjoint/debug_getgrad \' >> $@
	@echo '        $$i/Adjoint/debug_gridmove \' >> $@
	@echo '        $$i/Adjoint/debug_gridmovesens \' >> $@
	@echo '        $$i/Adjoint/linear_systems_sr_adjoint \' >> $@
	@echo '        $$i/Adjoint/info_gridmoves \' >> $@
	@echo '        $$i/Adjoint/nonlinadjoint.tec \' >> $@
	@echo '        $$i/Adjoint/nonlinturbadjoint.tec \' >> $@
	@echo '        $$i/Adjoint/relaxation_log_linear \' >> $@
	@echo '        $$i/Adjoint/temp.commands \' >> $@
	@echo '        $$i/Adjoint/*.adjoint \' >> $@
	@echo '        $$i/Adjoint/*.grid_info \' >> $@
	@echo '        $$i/Adjoint/*.mapbc.override \' >> $@
	@echo '        $$i/Adjoint/*_timing.tec \' >> $@
	@echo '        $$i/Adjoint/*_hist.dat \' >> $@
	@echo '        $$i/Adjoint/*_adj.*' >> $@
	@echo 'done' >> $@
	@/bin/chmod ugo+x $@

EXTRA_DIST = \
	design.nml \
	command_line.options \
	PORT.Makefile

# remove *.mod *.interface when mod_suffix is repaired for OS X
CLEANFILES = *.$(FC_MODEXT)  mpif.h *.time *.mod *.interface *.d *.fh

opt_driver_f90s=$(opt_driver_SOURCES:.F90=.f90)
opt_driver_deps=$(opt_driver_f90s:.f90=.d)

lib_MODULES = $(opt_driver_f90s:.f90=.$(FC_MODEXT))

DISTCLEANFILES = $(opt_driver_deps)

SUFFIXES = .d

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(PHYSICS_DIR) \
	-I $(LIBSMEMRD_DIR) \
	-I $(LIBDEFS_DIR) \
	-I $(LIBINIT_DIR) \
	-I $(LIBTURB_DIR) \
	-I $(LIBF90_DIR) \
	-L $(top_srcdir)/FuncLib90 \
	-L $(LIBTURB_DIR) > $@

BUILT_SOURCES = $(opt_driver_deps)

-include $(opt_driver_deps)
include $(top_srcdir)/make.rules

DISTCLEANFILES += setupDesign cleanupDesign

CORE_LIBS = libcore.a
INIT_LIBS = libinit.a
FUN3D_F90_LIBS = libsink.a


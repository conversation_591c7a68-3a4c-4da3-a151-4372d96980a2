module puns3d_io_c2n

  use local_grid,   only : pp_elem_ct,                                         &
                           pp_nhead, pp_ntail, pp_nsize,                       &
                           pp_chead, pp_ctail, pp_csize,                       &
                           pp_mhead, pp_mtail, pp_msize
  use bc_names,     only : backwards_compatible_bc,                            &
                           symmetry_x, symmetry_y, symmetry_z
  use info_depr,    only : twod, mirror_x, mirror_y, mirror_z, skeleton
  use kinddefs,     only : dp, system_i1, system_i8, r4, r8
  use lmpi,         only : lmpi_id,        lmpi_nproc,  lmpi_die,              &
                           lmpi_master,    lmpi_send,   lmpi_recv,             &
                           lmpi_allgather, lmpi_gather, lmpi_bcast,            &
                           lmpi_gatherv,   lmpi_reduce, lmpi_max,              &
                           lmpi_conditional_stop
  use timings,           only : timing, timer
  use system_extensions, only : se_open, se_wall_time

  implicit none

  public :: verbose
  logical :: verbose = .false.

  integer :: ntface, nqface

  public :: read_mapbc
  public :: process_grid_bc

  public :: puns3d_read_fast_c2n
  public :: puns3d_read_fast_bc           ! read and return bc

  public :: puns3d_read_vgrid_c2n
  public :: puns3d_read_vgrid_c2n_sio     ! vgrid stream IO
  public :: puns3d_read_vgrid_bc          ! read and return bc

  public :: puns3d_read_fun2d_c2n

  public :: puns3d_read_felisa_c2n
  public :: puns3d_read_felisa_bc         ! read and return the bctag

  public :: puns3d_read_aflr3_c2n
  public :: puns3d_read_aflr3_c2n_sio
  public :: puns3d_read_aflr3_bc          ! read and return bc

  public :: puns3d_read_fieldview_c2n     ! read and return xyz and c2n
                                          ! bc uses the same call
  public :: mirror_grid_par_driver
  public :: process_grid_bc_mirror_allocate
  public :: process_grid_bc_mirror_fill

  public :: lump_boundaries

  public :: distribute_fast_c2n
  public :: puns3d_expand_xyz_level1
  public :: puns3d_send_xyz_slice, puns3d_send_c2n_slice

  public :: delete_faceptr

private

  character(len=80)  :: gridfilename
  character(len=80)  :: mapbcfilename

  integer            :: unit_grid  = 16000 ! Units for files
  integer            :: unit_bc    = 62
  integer            :: unit_mapbc = 63
 !integer, parameter :: buffer_limit      = 200000000 ! 200MB
  integer, parameter :: buffer_limit      =  20000000 !  20MB (less mem)
  integer, parameter :: bcast_buffer_size =   8000000 !   8MB
  logical            :: verbose2 = .false.

! General boundary face variables

  integer, dimension(:,:), allocatable :: faceptr
  integer, dimension(:),   allocatable :: facetag
  integer, dimension(:,:), allocatable :: tagmap,  tagmap_org
  integer                              :: nbctags, nbctags_org

  character(len=120), dimension(:), allocatable, public :: family_org
  character(len=120), dimension(:), allocatable, public :: family_map

! For mirroring

  integer :: sym_num  ! number of nodes on symmetry plane
  integer :: sym_nnodesg_org
  integer, dimension(:), allocatable :: sym_val, sym_off   ! value and offset

  type t_bc_type
    integer, dimension(:,:), pointer :: f2ntb,f2nqb
  end type t_bc_type

  type elem_type
       integer :: act_size, max_size
       integer, dimension(:,:), pointer :: c2n
       integer, dimension(:),   pointer :: cl2g
  end type elem_type
  type(elem_type), dimension(:), allocatable :: elem

  integer, dimension(4) :: ncount
  logical, parameter :: dbtime = .false.

contains

!============================= PROCESS_GRID_BC ===============================80
!
! Processes grids read by READVGRID or READFAST or READNEAL
!
!=============================================================================80

  subroutine process_grid_bc( grid, patch_lumping )

    use grid_types,   only : grid_type
    use string_utils, only : strip
    use info_depr,    only : skeleton
    use bc_names,     only : usm3d_to_fun3d_bc
    use cfl_defs, only : hanim
    use sort,         only : binary_search, small_sort, heap_sort
    use bc_cache_cc,  only : bc_ghost

    type(grid_type),  intent(inout) :: grid
    character(len=*), intent(in)    :: patch_lumping

    integer  :: itag, ntags, ibound, ielem, ichk, iface, j, k
    integer  :: usm3d_bc
    integer  :: b_faces, fpc, icell, onn(4),n1,n2,n3,n4
    real(dp) :: hash1
    integer,  dimension(:), allocatable :: ind, face_elem, face_cell
    real(dp), dimension(:), allocatable :: face_hash, sorted_hash
    integer(system_i8) :: i8_totalcells

    integer,         dimension(:), allocatable :: iflag
    type(t_bc_type), dimension(:), allocatable :: t_bc

    character(len=4)                           :: bc_number

    continue

    if (lmpi_master.and.dbtime) call se_wall_time("process_grid_bc")

    if (lmpi_id == 0 .and. skeleton > 0 ) then
      write(*,*)
      write(*,*)        ' Processing grid input...'
      write(*,'(a,i0)') '   Number of nodes          : ', grid%nnodesg
    end if

    i8_totalcells = 0
    do ielem = 1,grid%nelem
      if (lmpi_id == 0 .and. skeleton > 0 )                                    &
      write(*,'(a,a3,a,i0)') '   Number of ',grid%elem(ielem)%type_cell,       &
                             ' cells      : ', grid%elem(ielem)%ncellg
      i8_totalcells = i8_totalcells + grid%elem(ielem)%ncellg
    end do

    if (lmpi_id == 0 .and. skeleton > 0 ) then
       write(*,'(a,i10)') '   Total number of cells    : ', i8_totalcells
       write(*,'(a,i10)') '   Number of tria bc faces  : ', ntface
       write(*,'(a,i10)') '   Number of quad bc faces  : ', nqface
       write(*,'(a,i10)') '   Total number of bc faces : ', ntface+nqface
    end if

    call lmpi_bcast(nbctags)

!   check if there are multiple bc tag specifcations

    if (lmpi_master) then
       do ichk = 1, nbctags-1
         do itag = ichk, nbctags
           if ((tagmap(itag,1) == tagmap(ichk,1)) .and.                        &
               (tagmap(itag,2) /= tagmap(ichk,2))) then
               print *,                                                        &
               '  WARNING: Multiple BC specification for surface tag, bc = ',  &
                 tagmap(itag,1),tagmap(itag,2)
           end if
         end do
       end do
    end if

!   Figure out how many tags there are
!   count faces on each boundary instance with iflag

    allocate(iflag(nbctags)); iflag = 0
    do iface = 1, ntface + nqface
      itag = itagindex(facetag(iface))
      iflag(itag) = iflag(itag) + 1
    end do

!   count bc instances

    ntags = 0

    do itag = 1, nbctags
      if (iflag(itag) > 0) ntags = ntags + 1
    end do

    if (ntags /= nbctags) then
      if (lmpi_master)                                                         &
        write (*,*) '  WARNING (ntags: ',ntags,'  /= nbctags: ',nbctags,' )'
       if (ntags > nbctags) then
          if (lmpi_master) then
             write (*,*) 'ERROR (ntags > nbctags)'
             write (*,*) 'ERROR stopping due to a memory check'
          end if
          call lmpi_conditional_stop(1)
       end if
    end if

!   Allocate the bc instances

    grid%nbound = ntags

    allocate(grid%bc(grid%nbound))

!   allocate the boundary face to node pointer arrays
!   iflag holds the number of faces for each boundary
!   iflag will be set to a bc instance index

    ntags = 0

    alloc_bc_f2n : do itag = 1, nbctags

      if (iflag(itag)==0) cycle alloc_bc_f2n ! skip bc's with no faces

      ntags = ntags + 1

!     NOTE skip the translation of boundary numbers if a new number (e.g. >100)

      usm3d_bc = tagmap(itag,2)
      tagmap(itag,2)     = usm3d_to_fun3d_bc(tagmap(itag,2),hanim)
      bad_bc_translation : if ( tagmap(itag,2) == -9999 ) then
        if (lmpi_master)  then
          write(*,*)"boundary face ",itag,tagmap(itag,1)," bc translation error"
          write(*,*)" originally ", usm3d_bc
        end if
        call lmpi_conditional_stop(1,'usm3d bc translation')
      end if bad_bc_translation
      grid%bc(ntags)%ibc = tagmap(itag,2)

      select case(trim(patch_lumping))
      case('bc')
         write(bc_number,'(i4)') grid%bc(ntags)%ibc
         grid%bc(ntags)%bc_family_org = 'BC_' // trim(adjustl(bc_number))
         grid%bc(ntags)%bc_family     = 'BC_' // trim(adjustl(bc_number))
      case default
         grid%bc(ntags)%bc_family_org = strip(family_org(ntags))
         grid%bc(ntags)%bc_family     = strip(family_map(ntags))
      end select

      iflag(itag) = ntags                  ! set iflag to index the bc instance

    end do alloc_bc_f2n

!    ntags = 0
!
!    symmetry_quarantine : do itag = 1, nbctags
!      if (iflag(itag)==0) cycle symmetry_quarantine ! skip bc's with no faces
!      ntags = ntags + 1
!      if ( .not. cc ) cycle symmetry_quarantine
!      ibcc = bc(ntags)%ibc
!      if (ibcc == symmetry_x_weak .or. ibcc == symmetry_x)                    &
!          bc(ntags)%ibc = symmetry_x
!      if (ibcc == symmetry_y_weak .or. ibcc == symmetry_y)                    &
!          bc(ntags)%ibc = symmetry_y
!      if (ibcc == symmetry_z_weak .or. ibcc == symmetry_z)                    &
!          bc(ntags)%ibc = symmetry_z
!    enddo symmetry_quarantine
!----------------------------

    if (.not.grid%cc) then

       call process_grid_bc_faces_nc(nbctags,iflag,grid)
       deallocate(iflag)

    else

    ! TBD The CC data structures (bc_ghost) is just level1 and
    !     should be handled as an extension of existing data structures.
    !     bc_ghost(ib)%nbfacet --> bc(ib)%nbfacet
    !     bc_ghost(ib)%ibnode  --> bc(ib)%ibnode
    !     qcell_ptr_t, qcell_ptr_q should be removed
    !     qcell_ptr_t is already available as grid%bc(ib)%f2ntb(i,4)
    !     qcell_ptr_q is already available as grid%bc(ib)%f2nqb(i,5)
    !     etc. [But for the initial spike, they are retained here.]

    ! Level0 (non-bc_ghost)

       b_faces  = 0
       do ielem = 1,grid%nelem
          fpc = grid%elem(ielem)%face_per_cell
          do icell = 1,grid%elem(ielem)%ncell0
             do iface = 1,fpc
                if (grid%elem(ielem)%c2c(iface,icell) <= 0) b_faces=b_faces+1
             end do
          end do
       end do
       allocate(face_hash(b_faces)); face_hash = 0

       b_faces = 0
       do ielem = 1,grid%nelem
          fpc = grid%elem(ielem)%face_per_cell
          do icell = 1,grid%elem(ielem)%ncell0
             do iface = 1,fpc
                if (grid%elem(ielem)%c2c(iface,icell) <= 0) then
                   b_faces = b_faces + 1
                   n1 = grid%elem(ielem)%local_f2n(iface,1)
                   n2 = grid%elem(ielem)%local_f2n(iface,2)
                   n3 = grid%elem(ielem)%local_f2n(iface,3)
                   n4 = grid%elem(ielem)%local_f2n(iface,4)
                   onn(1) = grid%elem(ielem)%c2n(n1,icell)
                   onn(2) = grid%elem(ielem)%c2n(n2,icell)
                   onn(3) = grid%elem(ielem)%c2n(n3,icell)
                   if (n1 == n4) then
                      onn(4) = 1
                   else
                      onn(4) = grid%elem(ielem)%c2n(n4,icell)
                   end if
                   call small_sort(4,onn)
                   face_hash(b_faces) = sum(onn)*1._dp +                       &
                        sqrt(onn(1)*1._dp)*sqrt(onn(2)*1._dp)*                 &
                        sqrt(onn(3)*1._dp)*sqrt(onn(4)*1._dp)
                end if
             end do
          end do
       end do
       !write(*,*)"b_faces = ",b_faces

       ! sort hash table

       allocate(ind(b_faces)); ind = 0
       call heap_sort(b_faces,face_hash,ind)
       allocate(sorted_hash(b_faces)); sorted_hash = 0.0_dp
       sorted_hash = face_hash(ind)
       deallocate(face_hash)

       allocate(t_bc(grid%nbound))
       k = max(b_faces,1)
       do ibound = 1,grid%nbound
          allocate(t_bc(ibound)%f2ntb(k,3)); t_bc(ibound)%f2ntb = 0
          allocate(t_bc(ibound)%f2nqb(k,4)); t_bc(ibound)%f2nqb = 0
       end do

       grid%bc(:)%nbfacet = 0
       grid%bc(:)%nbfaceq = 0
       do iface = 1, ntface + nqface
         ibound = iflag(itagindex(facetag(iface)))
         onn = 1
         if (faceptr(iface,4)==0) then ! tri-faces
            onn(1:3) = faceptr(iface,1:3)
         else
            onn(1:4) = faceptr(iface,1:4)
         end if
         call small_sort(4,onn)
         hash1 = sum(onn)*1._dp +                                              &
                 sqrt(onn(1)*1._dp)*sqrt(onn(2)*1._dp)*                        &
                 sqrt(onn(3)*1._dp)*sqrt(onn(4)*1._dp)
         k = binary_search(b_faces,sorted_hash,hash1)
         if (k > 0) then
            if (faceptr(iface,4)==0) then
               grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
               t_bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,1:3) =               &
                 faceptr(iface,1:3)
            else
               grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
               t_bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,1:4) =               &
                 faceptr(iface,1:4)
            end if
         end if
       end do
       deallocate(ind,sorted_hash)

       ! Now reallocate based on actual face counts

       do ibound = 1,grid%nbound
          k = max(1,grid%bc(ibound)%nbfacet)
          allocate(grid%bc(ibound)%f2ntb(k,5)); grid%bc(ibound)%f2ntb = 0
          if (grid%bc(ibound)%nbfacet > 0) then
             do j = 1,grid%bc(ibound)%nbfacet
                grid%bc(ibound)%f2ntb(j,1:3) = t_bc(ibound)%f2ntb(j,1:3)
             end do
          end if
          k = max(1,grid%bc(ibound)%nbfaceq)
          allocate(grid%bc(ibound)%f2nqb(k,6)); grid%bc(ibound)%f2nqb = 0
          if (grid%bc(ibound)%nbfaceq > 0) then
             do j = 1,grid%bc(ibound)%nbfaceq
                grid%bc(ibound)%f2nqb(j,1:4) = t_bc(ibound)%f2nqb(j,1:4)
             end do
          end if
       end do
       do ibound = 1,grid%nbound
         deallocate(t_bc(ibound)%f2ntb)
         deallocate(t_bc(ibound)%f2nqb)
       end do
       deallocate(t_bc)

    ! Level 1 (bc_ghost)

       allocate(bc_ghost(grid%nbound))
       bc_ghost(:)%nbfacet = 0
       bc_ghost(:)%nbfaceq = 0
       bc_ghost(:)%nbnode  = 0

       b_faces = 0
       do ielem = 1,grid%nelem
          fpc = grid%elem(ielem)%face_per_cell
          do icell = grid%elem(ielem)%ncell0+1,grid%elem(ielem)%ncell
             do iface = 1,fpc
                if (grid%elem(ielem)%c2c(iface,icell) <= 0) b_faces = b_faces+1
             end do
          end do
       end do

       allocate(face_hash(b_faces)); face_hash = 0
       allocate(face_elem(b_faces)); face_elem = 0
       allocate(face_cell(b_faces)); face_cell = 0

       b_faces = 0
       do ielem = 1,grid%nelem
          fpc = grid%elem(ielem)%face_per_cell
          do icell = grid%elem(ielem)%ncell0+1,grid%elem(ielem)%ncell
             do iface = 1,fpc
                if (grid%elem(ielem)%c2c(iface,icell) <= 0) then
                   b_faces = b_faces + 1
                   n1 = grid%elem(ielem)%local_f2n(iface,1)
                   n2 = grid%elem(ielem)%local_f2n(iface,2)
                   n3 = grid%elem(ielem)%local_f2n(iface,3)
                   n4 = grid%elem(ielem)%local_f2n(iface,4)
                   onn(1) = grid%elem(ielem)%c2n(n1,icell)
                   onn(2) = grid%elem(ielem)%c2n(n2,icell)
                   onn(3) = grid%elem(ielem)%c2n(n3,icell)
                   if (n1 == n4) then
                      onn(4) = 1
                   else
                      onn(4) = grid%elem(ielem)%c2n(n4,icell)
                   end if
                   call small_sort(4,onn)
                   face_hash(b_faces) = sum(onn)*1._dp +                       &
                        sqrt(onn(1)*1._dp)*sqrt(onn(2)*1._dp)*                 &
                        sqrt(onn(3)*1._dp)*sqrt(onn(4)*1._dp)
                   face_elem(b_faces) = ielem
                   face_cell(b_faces) = icell
                end if
             end do
          end do
       end do

       ! sort hash table

       allocate(ind(b_faces)); ind = 0
       call heap_sort(b_faces,face_hash,ind)
       allocate(sorted_hash(b_faces)); sorted_hash = 0.0_dp
       sorted_hash = face_hash(ind)
       deallocate(face_hash)

       allocate(t_bc(grid%nbound))
       k = max(b_faces,1)
       do ibound = 1,grid%nbound
          allocate(t_bc(ibound)%f2ntb(k,5)); t_bc(ibound)%f2ntb = 0
          allocate(t_bc(ibound)%f2nqb(k,6)); t_bc(ibound)%f2nqb = 0
       end do

       bc_ghost(:)%nbfacet = 0
       bc_ghost(:)%nbfaceq = 0
       do iface = 1, ntface + nqface
         ibound = iflag(itagindex(facetag(iface)))
         onn = 1
         if (faceptr(iface,4)==0) then ! tri-faces
            onn(1:3) = faceptr(iface,1:3)
         else
            onn(1:4) = faceptr(iface,1:4)
         end if
         call small_sort(4,onn)
         hash1 = sum(onn)*1._dp +                                              &
                 sqrt(onn(1)*1._dp)*sqrt(onn(2)*1._dp)*                        &
                 sqrt(onn(3)*1._dp)*sqrt(onn(4)*1._dp)
         k = binary_search(b_faces,sorted_hash,hash1)
         if (k > 0) then
            if (faceptr(iface,4)==0) then
               bc_ghost(ibound)%nbfacet = bc_ghost(ibound)%nbfacet + 1
               t_bc(ibound)%f2ntb(bc_ghost(ibound)%nbfacet,1:3) =              &
                 faceptr(iface,1:3)
               t_bc(ibound)%f2ntb(bc_ghost(ibound)%nbfacet,4)=face_cell(ind(k))
               t_bc(ibound)%f2ntb(bc_ghost(ibound)%nbfacet,5)=face_elem(ind(k))
            else
               bc_ghost(ibound)%nbfaceq = bc_ghost(ibound)%nbfaceq + 1
               t_bc(ibound)%f2nqb(bc_ghost(ibound)%nbfaceq,1:4) =              &
                 faceptr(iface,1:4)
               t_bc(ibound)%f2nqb(bc_ghost(ibound)%nbfaceq,5)=face_cell(ind(k))
               t_bc(ibound)%f2nqb(bc_ghost(ibound)%nbfaceq,6)=face_elem(ind(k))
            end if
         end if
       end do
       deallocate(ind,sorted_hash)

       deallocate(iflag)

       ! Now reallocate based on actual face counts

       do ibound = 1,grid%nbound
          k = max(1,bc_ghost(ibound)%nbfacet)
          allocate(bc_ghost(ibound)%f2ntb(k,5))
          bc_ghost(ibound)%f2ntb = 0
          if (bc_ghost(ibound)%nbfacet > 0) then
             do j = 1,bc_ghost(ibound)%nbfacet
                bc_ghost(ibound)%f2ntb(j,1:5) = t_bc(ibound)%f2ntb(j,1:5)
             end do
          end if
          k = max(1,bc_ghost(ibound)%nbfaceq)
          allocate(bc_ghost(ibound)%f2nqb(k,6)); bc_ghost(ibound)%f2nqb = 0
          if (bc_ghost(ibound)%nbfaceq > 0) then
             do j = 1,bc_ghost(ibound)%nbfaceq
                bc_ghost(ibound)%f2nqb(j,1:6) = t_bc(ibound)%f2nqb(j,1:6)
             end do
          end if
       end do
       do ibound = 1,grid%nbound
         deallocate(t_bc(ibound)%f2ntb)
         deallocate(t_bc(ibound)%f2nqb)
       end do
       deallocate(t_bc)

       if (lmpi_id == 0.and.timing) call timer('... find bc faces')

    end if ! nc/cc

    if (lmpi_master.and.dbtime) call se_wall_time("END process_grid_bc")

  end subroutine process_grid_bc

!============================= PUN3D_PROCESS_GRID_BC_FACES_NC ================80
!
! Process boundaries sorted in t_bc.
! The grid data structure is partially allocated
!
!=============================================================================80

  subroutine process_grid_bc_faces_nc(niflag,iflag,grid)

    use grid_types,  only : grid_type

    integer,                    intent(in)    :: niflag
    integer, dimension(niflag), intent(in)    :: iflag
    type(grid_type),            intent(inout) :: grid

    integer :: ibound, ielem, iface, jface
    integer :: i,j,i1,i2,i3,i4,i5,i6,i7,i8
    integer :: bsize, tag_size
    integer :: node1,node2,node3,node4
    integer :: word1,word2,word3,word4

    logical  :: b1,b2,b3,b4

    real(dp) :: bsize_inv

    character(len=3)  :: type_cell

    integer(system_i1), dimension(:), allocatable :: tag01, btag, tag
    integer, dimension(:), allocatable :: faceb

    continue

    if (lmpi_master.and.dbtime) call se_wall_time("process_grid_bc_faces_nc")

    bsize     = bit_size(tag01)
    bsize_inv = 1./bit_size(tag01)
    tag_size  = ceiling(grid%nnodesg*bsize_inv)

    allocate(tag01(tag_size)); tag01 = 0

    do i = 1,grid%nnodes01
!      write(1300+lmpi_id,*) grid%l2g(i)
       word1 = ceiling(grid%l2g(i)*bsize_inv)
       tag01(word1)  = ibset(tag01(word1), mod(grid%l2g(i),bsize))
    end do

    tag_size = ceiling((ntface+nqface)*bsize_inv)
    allocate(btag(tag_size)); btag = 0

    do ibound = 1, grid%nbound
       grid%bc(ibound)%nbfacetg = 0
       grid%bc(ibound)%nbfaceqg = 0
    end do

    allocate(faceb(ntface + nqface)); faceb = 0

    do iface = 1, ntface + nqface

      ibound = iflag(itagindex(facetag(iface)))
      faceb(iface) = ibound

      if (faceptr(iface,4)==0) then ! tri-faces
         grid%bc(ibound)%nbfacetg = grid%bc(ibound)%nbfacetg + 1
      else
         grid%bc(ibound)%nbfaceqg = grid%bc(ibound)%nbfaceqg + 1
      end if

      node1 = faceptr(iface,1)
      word1 = ceiling(node1*bsize_inv)
      b1    = btest(tag01(word1), mod(node1,bsize))
      if (.not.b1) cycle

      node2 = faceptr(iface,2)
      word2 = ceiling(node2*bsize_inv)
      b2    = btest(tag01(word2), mod(node2,bsize))
      if (.not.b2) cycle

      node3 = faceptr(iface,3)
      word3 = ceiling(node3*bsize_inv)
      b3    = btest(tag01(word3), mod(node3,bsize))
      if (.not.b3) cycle

      node4 = faceptr(iface,4)
      if (node4 /= 0) then
         word4 = ceiling(node4*bsize_inv)
         b4    = btest(tag01(word4), mod(node4,bsize))
         if (.not.b4) cycle
      end if

      word1 = ceiling(iface*bsize_inv)
      btag(word1) = ibset(btag(word1), mod(iface,bsize))

    end do

!----------------------------

    tag01 = 0
    do i = 1,grid%nnodes0
       word1 = ceiling(grid%l2g(i)*bsize_inv)
       tag01(word1)  = ibset(tag01(word1), mod(grid%l2g(i),bsize))
    end do

    allocate(tag(ntface+nqface)); tag = 0

    jface = 1

    loop_face: do iface = 1,ntface+nqface

      if (iface == ntface+1) jface = 2

      ! Active face
      word1 = ceiling(iface*bsize_inv)
      b1    = btest(btag(word1), mod(iface,bsize))
      if (.not.b1) cycle

      node1 = faceptr(iface,1)
      word1 = ceiling(node1*bsize_inv)
      b1    = btest(tag01(word1), mod(node1,bsize))
      if (b1) then
         tag(iface) = jface; cycle
      end if

      node2 = faceptr(iface,2)
      word2 = ceiling(node2*bsize_inv)
      b2    = btest(tag01(word2), mod(node2,bsize))
      if (b2) then
         tag(iface) = jface; cycle
      end if

      node3 = faceptr(iface,3)
      word3 = ceiling(node3*bsize_inv)
      b3    = btest(tag01(word3), mod(node3,bsize))
      if (b3) then
         tag(iface) = jface; cycle
      end if

      node4 = faceptr(iface,4)
      if (jface == 2) then
         word4 = ceiling(node4*bsize_inv)
         b4    = btest(tag01(word4), mod(node4,bsize))
         if (b4) then
            tag(iface) = jface; cycle
         end if
      end if

      if (node4 == 0) then
         do ielem = 1,grid%nelem
            type_cell = grid%elem(ielem)%type_cell

            if (type_cell == 'tet') then
              do i = 1,size(grid%elem(ielem)%c2n,2)
                i1 = grid%elem(ielem)%c2n(1,i)
                i2 = grid%elem(ielem)%c2n(2,i)
                i3 = grid%elem(ielem)%c2n(3,i)
                i4 = grid%elem(ielem)%c2n(4,i)
                if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.        &
                    (node1/=i4)) cycle
                if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.        &
                    (node2/=i4)) cycle
                if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.        &
                    (node3/=i4)) cycle
                tag(iface) = jface
                cycle loop_face
             end do

            else if (type_cell == 'prz') then
             do i = 1,size(grid%elem(ielem)%c2n,2)
               i1 = grid%elem(ielem)%c2n(1,i)
               i2 = grid%elem(ielem)%c2n(2,i)
               i3 = grid%elem(ielem)%c2n(3,i)
               i4 = grid%elem(ielem)%c2n(4,i)
               i5 = grid%elem(ielem)%c2n(5,i)
               i6 = grid%elem(ielem)%c2n(6,i)
               if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.         &
                   (node1/=i4).and.(node1/=i5).and.(node1/=i6)) cycle
               if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.         &
                   (node2/=i4).and.(node2/=i5).and.(node2/=i6)) cycle
               if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.         &
                   (node3/=i4).and.(node3/=i5).and.(node3/=i6)) cycle
               tag(iface) = jface
               cycle loop_face
             end do

            else if (type_cell == 'pyr') then
              do i = 1,size(grid%elem(ielem)%c2n,2)
               i1 = grid%elem(ielem)%c2n(1,i)
               i2 = grid%elem(ielem)%c2n(2,i)
               i3 = grid%elem(ielem)%c2n(3,i)
               i4 = grid%elem(ielem)%c2n(4,i)
               i5 = grid%elem(ielem)%c2n(5,i)
               if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.         &
                   (node1/=i4).and.(node1/=i5)) cycle
               if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.         &
                   (node2/=i4).and.(node2/=i5)) cycle
               if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.         &
                   (node3/=i4).and.(node3/=i5)) cycle
               tag(iface) = jface
               cycle loop_face
             end do ! i
            end if
          end do ! ielem
        else ! quad
          do ielem = 1,grid%nelem
             type_cell = grid%elem(ielem)%type_cell

            if (type_cell == 'hex') then
             do i = 1,size(grid%elem(ielem)%c2n,2)
              i1 = grid%elem(ielem)%c2n(1,i)
              i2 = grid%elem(ielem)%c2n(2,i)
              i3 = grid%elem(ielem)%c2n(3,i)
              i4 = grid%elem(ielem)%c2n(4,i)
              i5 = grid%elem(ielem)%c2n(5,i)
              i6 = grid%elem(ielem)%c2n(6,i)
              i7 = grid%elem(ielem)%c2n(7,i)
              i8 = grid%elem(ielem)%c2n(8,i)
              if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                  (node1/=i4).and.(node1/=i5).and.(node1/=i6).and.          &
                  (node1/=i7).and.(node1/=i8)) cycle
              if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                  (node2/=i4).and.(node2/=i5).and.(node2/=i6).and.          &
                  (node2/=i7).and.(node2/=i8)) cycle
              if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                  (node3/=i4).and.(node3/=i5).and.(node3/=i6).and.          &
                  (node3/=i7).and.(node3/=i8)) cycle
              if ((node4/=i1).and.(node4/=i2).and.(node4/=i3).and.          &
                  (node4/=i4).and.(node4/=i5).and.(node4/=i6).and.          &
                  (node4/=i7).and.(node4/=i8)) cycle
              tag(iface) = jface
              cycle loop_face
            end do ! i

          else if (type_cell == 'prz') then
            do i = 1,size(grid%elem(ielem)%c2n,2)
              i1 = grid%elem(ielem)%c2n(1,i)
              i2 = grid%elem(ielem)%c2n(2,i)
              i3 = grid%elem(ielem)%c2n(3,i)
              i4 = grid%elem(ielem)%c2n(4,i)
              i5 = grid%elem(ielem)%c2n(5,i)
              i6 = grid%elem(ielem)%c2n(6,i)
              if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                  (node1/=i4).and.(node1/=i5).and.(node1/=i6)) cycle
              if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                  (node2/=i4).and.(node2/=i5).and.(node2/=i6)) cycle
              if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                  (node3/=i4).and.(node3/=i5).and.(node3/=i6)) cycle
              if ((node4/=i1).and.(node4/=i2).and.(node4/=i3).and.          &
                  (node4/=i4).and.(node4/=i5).and.(node4/=i6)) cycle
              tag(iface) = jface
              cycle loop_face
             end do ! i

          else if (type_cell == 'pyr') then
            do i = 1,size(grid%elem(ielem)%c2n,2)
              i1 = grid%elem(ielem)%c2n(1,i)
              i2 = grid%elem(ielem)%c2n(2,i)
              i3 = grid%elem(ielem)%c2n(3,i)
              i4 = grid%elem(ielem)%c2n(4,i)
              i5 = grid%elem(ielem)%c2n(5,i)
              if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                  (node1/=i4).and.(node1/=i5)) cycle
              if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                  (node2/=i4).and.(node2/=i5)) cycle
              if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                  (node3/=i4).and.(node3/=i5)) cycle
              if ((node4/=i1).and.(node4/=i2).and.(node4/=i3).and.          &
                  (node4/=i4).and.(node4/=i5)) cycle
              tag(iface) = jface
              cycle loop_face
             end do ! i
          end if ! type_cell
        end do ! ielem
      end if ! quad
    end do loop_face  ! iface (quad)

    do ibound = 1, grid%nbound
      grid%bc(ibound)%nbfacet = 0
      grid%bc(ibound)%nbfaceq = 0
    end do

    do iface = 1,ntface+nqface
       ibound = faceb(iface)
       if (tag(iface) == 1) then
          grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
       elseif (tag(iface) == 2) then
          grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
       end if
    end do

!  if ((grid%bc(ibound)%nbfacet > 0).or.(grid%bc(ibound)%nbfaceq > 0)) then
!    write(1600+lmpi_id,*)ibound,grid%bc(ibound)%nbfacet,grid%bc(ibound)%nbfaceq
!  end if
    do ibound = 1,grid%nbound
        j = max(1,grid%bc(ibound)%nbfacet)
        allocate(grid%bc(ibound)%f2ntb(j,5)); grid%bc(ibound)%f2ntb = 0
        j = max(1,grid%bc(ibound)%nbfaceq)
        allocate(grid%bc(ibound)%f2nqb(j,6)); grid%bc(ibound)%f2nqb = 0
        grid%bc(ibound)%nbfacet = 0
        grid%bc(ibound)%nbfaceq = 0
    end do

    do iface = 1,ntface+nqface
       ibound = faceb(iface)
       if (tag(iface) == 1) then
          grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
          grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,1:3) =              &
             faceptr(iface,1:3)
       elseif (tag(iface) == 2) then
          grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
          grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,1:4) =              &
             faceptr(iface,1:4)
       end if
    end do ! iface

    deallocate(btag,tag,tag01)
    deallocate(faceb)
!----------------------------

!   do ibound = 1,grid%nbound
!     if (grid%bc(ibound)%nbfacet > 0) then
!        write(5000+lmpi_id,*) 'ib,nbt ',ibound,grid%bc(ibound)%nbfacet
!        do i = 1,grid%bc(ibound)%nbfacet
!           write(5000+lmpi_id,'(6(i0,1x))') grid%bc(ibound)%f2ntb(i,:)
!        end do
!     end if
!     if (grid%bc(ibound)%nbfaceq > 0) then
!        write(5000+lmpi_id,*) 'ib,nbq ',ibound,grid%bc(ibound)%nbfaceq
!        do i = 1,grid%bc(ibound)%nbfaceq
!           write(5000+lmpi_id,'(6(i0,1x))') grid%bc(ibound)%f2nqb(i,:)
!        end do
!    end if
!   end do

    if (lmpi_master.and.dbtime) call se_wall_time("process_grid_bc_faces_nc")

  end subroutine process_grid_bc_faces_nc

!============================= PUN3D_PROCESS_GRID_BC_FACES_MIRROR ============80
!
! Process boundaries sorted in t_bc.
! The grid data structure is partially allocated
!
!=============================================================================80

  subroutine process_grid_bc_faces_mirror(nbound,t_nbfacet,t_nbfaceq,t_bc,grid)

    use allocations, only : my_realloc_ptr
    use grid_types,  only : grid_type

    integer,                            intent(in) :: nbound
    integer,         dimension(nbound), intent(in) :: t_nbfacet, t_nbfaceq
    type(t_bc_type), dimension(nbound), intent(in) :: t_bc
    type(grid_type),                    intent(inout) :: grid

    integer :: ibound, ielem, iface, ifound
    integer :: i,j,i1,i2,i3,i4,i5,i6,i7,i8
    integer :: bsize, tag_size
    integer :: node1,node2,node3,node4,word
    integer :: word1,word2,word3,word4
    integer :: word1a, word1b, word2a, word2b, word3a, word3b, word4a, word4b

    logical  :: b1,b2,b3,b4,c1,c2,c3,c4

    real(dp) :: bsize_inv

    character(len=3)  :: type_cell

    integer(system_i1), dimension(:), allocatable :: tag012, tag0

    continue

    if (lmpi_id == 0.and.timing)                                               &
       call timer('... START process_grid_bc_faces')

    bsize     = bit_size(tag0)
    bsize_inv = 1./bit_size(tag0)
    tag_size = ceiling(grid%nnodesg*bsize_inv)

    allocate(tag0  (tag_size)); tag0   = 0
    allocate(tag012(tag_size)); tag012 = 0

    do i = 1,grid%nnodes01
       word1 = ceiling(grid%l2g(i)*bsize_inv)
       if (i <= grid%nnodes0)                                                  &
          tag0(word1) = ibset(tag0(word1),   mod(grid%l2g(i),bsize))
       tag012(word1)  = ibset(tag012(word1), mod(grid%l2g(i),bsize))
    end do

!----------------------------

    grid%bc(:)%nbfacet = 0
    grid%bc(:)%nbfaceq = 0
    do ibound = 1,grid%nbound

      if (t_nbfacet(ibound) > 0) then
       loop1t: do iface = 1,t_nbfacet(ibound)

         node1  = t_bc(ibound)%f2ntb(iface,1)
         word1  = ceiling(node1*bsize_inv)
         word1a = tag012(word1)
         b1     = btest(word1a, mod(node1,bsize))
         if (.not.b1) cycle
         word1b = tag0(word1)
         c1     = btest(word1b, mod(node1,bsize))

         node2  = t_bc(ibound)%f2ntb(iface,2)
         word2  = ceiling(node2*bsize_inv)
         word2a = tag012(word2)
         b2 = btest(word2a, mod(node2,bsize))
         if (.not.b2) cycle
         word2b = tag0(word2)
         c2     = btest(word2b, mod(node2,bsize))

         node3  = t_bc(ibound)%f2ntb(iface,3)
         word3  = ceiling(node3*bsize_inv)
         word3a = tag012(word3)
         b3 = btest(word3a, mod(node3,bsize))
         if (.not.b3) cycle
         word3b = tag0(word3)
         c3     = btest(word3b, mod(node3,bsize))

         if (c1.or.c2.or.c3) then

            grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
            grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,1) = node1
            grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,2) = node2
            grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,3) = node3
           !write(4000+lmpi_id,'(i0,":",i0,"::",100(i0,1x))') ibound,          &
           !  grid%bc(ibound)%nbfacet,node1,node2,node3

         else ! (not.(c1.or.c2.or.c3))

           do ielem = 1,grid%nelem
             type_cell = grid%elem(ielem)%type_cell

             if (type_cell == 'tet') then

                loop2: do i = 1,size(grid%elem(ielem)%c2n,2)

                 i1 = grid%elem(ielem)%c2n(1,i)
                 i2 = grid%elem(ielem)%c2n(2,i)
                 i3 = grid%elem(ielem)%c2n(3,i)
                 i4 = grid%elem(ielem)%c2n(4,i)

                 if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                     (node1/=i4)) cycle
                 if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                     (node2/=i4)) cycle
                 if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                     (node3/=i4)) cycle

                 ! Find other node by subtraction

                 ifound = (i1+i2+i3+i4) - (node1+node2+node3)
                 word   = tag0(ceiling(ifound*bsize_inv))
                 c1     = btest(word, mod(ifound,bsize))
                 if (c1) then
                    grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
                    grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,1) = node1
                    grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,2) = node2
                    grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,3) = node3
                   !write(4000+lmpi_id,'(i0,":",i0,":",100(i0,1x))') ibound,   &
                   !  grid%bc(ibound)%nbfacet,node1,node2,node3
                    cycle loop1t
                 end if
                end do loop2

             else if (type_cell == 'prz') then
                loop2a: do i = 1,size(grid%elem(ielem)%c2n,2)
                 i1 = grid%elem(ielem)%c2n(1,i)
                 i2 = grid%elem(ielem)%c2n(2,i)
                 i3 = grid%elem(ielem)%c2n(3,i)
                 i4 = grid%elem(ielem)%c2n(4,i)
                 i5 = grid%elem(ielem)%c2n(5,i)
                 i6 = grid%elem(ielem)%c2n(6,i)

                 if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                     (node1/=i4).and.(node1/=i5).and.(node1/=i6)) cycle
                 if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                     (node2/=i4).and.(node2/=i5).and.(node2/=i6)) cycle
                 if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                     (node3/=i4).and.(node3/=i5).and.(node3/=i6)) cycle
                 do j = 1,6
                    ifound = grid%elem(ielem)%c2n(j,i)
                    word   = tag0(ceiling(ifound*bsize_inv))
                    c1     = btest(word, mod(ifound,bsize))
                    if (c1) then
                       grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
                       grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,1) = node1
                       grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,2) = node2
                       grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,3) = node3
                      !write(4000+lmpi_id,'(i0,":",i0,":",100(i0,1x))')        &
                      !  ibound,grid%bc(ibound)%nbfacet,node1,node2,node3
                       cycle loop1t
                    end if
                 end do
                end do loop2a

             else if (type_cell == 'pyr') then
                loop2b:  do i = 1,size(grid%elem(ielem)%c2n,2)
                 i1 = grid%elem(ielem)%c2n(1,i)
                 i2 = grid%elem(ielem)%c2n(2,i)
                 i3 = grid%elem(ielem)%c2n(3,i)
                 i4 = grid%elem(ielem)%c2n(4,i)
                 i5 = grid%elem(ielem)%c2n(5,i)

                 if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                     (node1/=i4).and.(node1/=i5)) cycle
                 if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                     (node2/=i4).and.(node2/=i5)) cycle
                 if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                     (node3/=i4).and.(node3/=i5)) cycle

                 do j = 1,5
                    ifound = grid%elem(ielem)%c2n(j,i)
                    word   = tag0(ceiling(ifound*bsize_inv))
                    c1     = btest(word, mod(ifound,bsize))
                    if (c1) then
                       grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
                       grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,1) = node1
                       grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,2) = node2
                       grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet,3) = node3
                      !write(4000+lmpi_id,'(i0,":",i0,":",100(i0,1x))')        &
                      !  ibound,grid%bc(ibound)%nbfacet,node1,node2,node3
                       cycle loop1t
                    end if
                 end do
                end do loop2b

             end if
           end do ! ielem

         end if
       end do loop1t  ! tri
      end if    ! if (t_nbfacet > 0)

      if (t_nbfaceq(ibound) > 0) then
       loop1q: do iface = 1,t_nbfaceq(ibound)

         node1  = t_bc(ibound)%f2nqb(iface,1)
         word1  = ceiling(node1*bsize_inv)
         word1a = tag012(word1)
         b1     = btest(word1a, mod(node1,bsize))
         if (.not.b1) cycle
         word1b = tag0(word1)
         c1     = btest(word1b, mod(node1,bsize))

         node2  = t_bc(ibound)%f2nqb(iface,2)
         word2  = ceiling(node2*bsize_inv)
         word2a = tag012(word2)
         b2     = btest(word2a, mod(node2,bsize))
         if (.not.b2) cycle
         word2b = tag0(word2)
         c2     = btest(word2b, mod(node2,bsize))

         node3  = t_bc(ibound)%f2nqb(iface,3)
         word3  = ceiling(node3*bsize_inv)
         word3a = tag012(word3)
         b3     = btest(word3a, mod(node3,bsize))
         if (.not.b3) cycle
         word3b = tag0(word3)
         c3     = btest(word3b, mod(node3,bsize))

         node4  = t_bc(ibound)%f2nqb(iface,4)
         word4  = ceiling(node4*bsize_inv)
         word4a = tag012(word4)
         b4     = btest(word4a, mod(node4,bsize))
         if (.not.b4) cycle
         word4b = tag0(word4)
         c4     = btest(word4b, mod(node4,bsize))

         if (c1.or.c2.or.c3.or.c4) then ! in face

            grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
            grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,1) = node1
            grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,2) = node2
            grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,3) = node3
            grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,4) = node4
          ! write(4000+lmpi_id,'(i0,":",i0,"::",100(i0,1x))') ibound,          &
          !   grid%bc(ibound)%nbfacet,node1,node2,node3,node4

         else ! (.not.(c1.or.c2.or.c3.or.c4)

           do ielem = 1,grid%nelem
             type_cell = grid%elem(ielem)%type_cell

             if (type_cell == 'hex') then
                loop3: do i = 1,size(grid%elem(ielem)%c2n,2)

                 i1 = grid%elem(ielem)%c2n(1,i)
                 i2 = grid%elem(ielem)%c2n(2,i)
                 i3 = grid%elem(ielem)%c2n(3,i)
                 i4 = grid%elem(ielem)%c2n(4,i)
                 i5 = grid%elem(ielem)%c2n(5,i)
                 i6 = grid%elem(ielem)%c2n(6,i)
                 i7 = grid%elem(ielem)%c2n(7,i)
                 i8 = grid%elem(ielem)%c2n(8,i)

                 if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                     (node1/=i4).and.(node1/=i5).and.(node1/=i6).and.          &
                     (node1/=i7).and.(node1/=i8)) cycle
                 if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                     (node2/=i4).and.(node2/=i5).and.(node2/=i6).and.          &
                     (node2/=i7).and.(node2/=i8)) cycle
                 if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                     (node3/=i4).and.(node3/=i5).and.(node3/=i6).and.          &
                     (node3/=i7).and.(node3/=i8)) cycle
                 if ((node4/=i1).and.(node4/=i2).and.(node4/=i3).and.          &
                     (node4/=i4).and.(node4/=i5).and.(node4/=i6).and.          &
                     (node4/=i7).and.(node4/=i8)) cycle
                 do j = 1,8
                    ifound = grid%elem(ielem)%c2n(j,i)
                    word   = tag0(ceiling(ifound*bsize_inv))
                    c1     = btest(word, mod(ifound,bsize))
                    if (c1) then
                       grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,1) = node1
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,2) = node2
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,3) = node3
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,4) = node4
                    !  write(4000+lmpi_id,'(i0,":",i0,"::",100(i0,1x))')       &
                    !    ibound,grid%bc(ibound)%nbfacet,node1,node2,node3,node4
                       cycle loop1q
                    end if
                 end do
                end do loop3

             else if (type_cell == 'prz') then
                loop3a: do i = 1,size(grid%elem(ielem)%c2n,2)
                 i1 = grid%elem(ielem)%c2n(1,i)
                 i2 = grid%elem(ielem)%c2n(2,i)
                 i3 = grid%elem(ielem)%c2n(3,i)
                 i4 = grid%elem(ielem)%c2n(4,i)
                 i5 = grid%elem(ielem)%c2n(5,i)
                 i6 = grid%elem(ielem)%c2n(6,i)

                 if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                     (node1/=i4).and.(node1/=i5).and.(node1/=i6)) cycle
                 if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                     (node2/=i4).and.(node2/=i5).and.(node2/=i6)) cycle
                 if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                     (node3/=i4).and.(node3/=i5).and.(node3/=i6)) cycle
                 if ((node4/=i1).and.(node4/=i2).and.(node4/=i3).and.          &
                     (node4/=i4).and.(node4/=i5).and.(node4/=i6)) cycle
                 do j = 1,6
                    ifound = grid%elem(ielem)%c2n(j,i)
                    word   = tag0(ceiling(ifound*bsize_inv))
                    c1     = btest(word, mod(ifound,bsize))
                    if (c1) then
                       grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,1) = node1
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,2) = node2
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,3) = node3
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,4) = node4
                    !  write(4000+lmpi_id,'(i0,":",i0,"::",100(i0,1x))')
                    !    ibound,grid%bc(ibound)%nbfacet,node1,node2,node3,node4
                       cycle loop1q
                    end if
                 end do
                end do loop3a

             else if (type_cell == 'pyr') then
                loop3b:  do i = 1,size(grid%elem(ielem)%c2n,2)
                 i1 = grid%elem(ielem)%c2n(1,i)
                 i2 = grid%elem(ielem)%c2n(2,i)
                 i3 = grid%elem(ielem)%c2n(3,i)
                 i4 = grid%elem(ielem)%c2n(4,i)
                 i5 = grid%elem(ielem)%c2n(5,i)

                 if ((node1/=i1).and.(node1/=i2).and.(node1/=i3).and.          &
                     (node1/=i4).and.(node1/=i5)) cycle
                 if ((node2/=i1).and.(node2/=i2).and.(node2/=i3).and.          &
                     (node2/=i4).and.(node2/=i5)) cycle
                 if ((node3/=i1).and.(node3/=i2).and.(node3/=i3).and.          &
                     (node3/=i4).and.(node3/=i5)) cycle

                 do j = 1,5
                    ifound = grid%elem(ielem)%c2n(j,i)
                    word   = tag0(ceiling(ifound*bsize_inv))
                    c1     = btest(word, mod(ifound,bsize))
                    if (c1) then
                       grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,1) = node1
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,2) = node2
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,3) = node3
                       grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq,4) = node4
                     ! write(4000+lmpi_id,'(i0,":",i0,"::",100(i0,1x))')
                     !   ibound, grid%bc(ibound)%nbfacet,node1,node2,node3,node4
                       cycle loop1q
                    end if
                 end do
                end do loop3b

             end if

           end do ! ielem

         end if ! not in face
       end do loop1q  ! iface (quad)
      end if    ! nfaceq > 0
    end do      ! ibound
    deallocate(tag0,tag012)

!----------------------------

    if (lmpi_id == 0.and.timing) call timer('... find bc faces')

!   now reallocate based on final face counts

    do ibound = 1,grid%nbound
      if (grid%bc(ibound)%nbfacet > 0) then
        call my_realloc_ptr(grid%bc(ibound)%f2ntb,grid%bc(ibound)%nbfacet,5)
      else
        call my_realloc_ptr(grid%bc(ibound)%f2ntb,1,5)
      end if
      if (grid%bc(ibound)%nbfaceq > 0) then
        call my_realloc_ptr(grid%bc(ibound)%f2nqb,grid%bc(ibound)%nbfaceq,6)
      else
        call my_realloc_ptr(grid%bc(ibound)%f2nqb,1,6)
      end if
    end do

!   do ibound = 1,grid%nbound
!     if (grid%bc(ibound)%nbfacet > 0) then
!        write(5000+lmpi_id,*) 'ib,nbt ',ibound,grid%bc(ibound)%nbfacet
!        do i = 1,grid%bc(ibound)%nbfacet
!           write(5000+lmpi_id,'(6(i0,1x))') grid%bc(ibound)%f2ntb(i,:)
!        end do
!     end if
!     if (grid%bc(ibound)%nbfaceq > 0) then
!        write(5000+lmpi_id,*) 'ib,nbq ',ibound,grid%bc(ibound)%nbfaceq
!        do i = 1,grid%bc(ibound)%nbfaceq
!           write(5000+lmpi_id,'(6(i0,1x))') grid%bc(ibound)%f2nqb(i,:)
!        end do
!     end if
!   end do

    if (lmpi_id == 0.and.timing) call timer('... END process_grid_bc_faces')

  end subroutine process_grid_bc_faces_mirror

!============================= PROCESS_GRID_BC_MIRROR_ALLOCATE ===============80
!
! Allocate bc for grids altered after reading (e.g., mirroring) but before
! called metis/ParMetis.
!
!=============================================================================80

  subroutine process_grid_bc_mirror_allocate( grid )

    use allocations, only : my_alloc_ptr, my_realloc_ptr
    use grid_types,  only : grid_type
    use bc_names,    only : usm3d_to_fun3d_bc
    use cfl_defs, only : hanim

    type(grid_type), intent(inout) :: grid

    integer :: itag, ntags, ibcc, inode, ibound, ielem, ichk, iface
    integer  :: usm3d_bc
    integer(system_i8) :: i8_totalcells

    integer, dimension(:), allocatable :: iflag

    continue

    if (lmpi_id == 0.and.timing) call timer('... START process_grid_sm')

    i8_totalcells = 0

    if (lmpi_id == 0) then
      write(*,*)
      write(*,*)        ' Processing grid input...'
      write(*,'(a,i0)') '   Number of nodes          : ', grid%nnodesg
    end if

    do ielem = 1,grid%nelem
      if (lmpi_id == 0)                                                        &
      write(*,'(a,a3,a,i0)') '   Number of ',grid%elem(ielem)%type_cell,       &
                             ' cells      : ', grid%elem(ielem)%ncellg
      i8_totalcells = i8_totalcells + grid%elem(ielem)%ncellg
    end do

    if (lmpi_id == 0) then
       write(*,'(a,i0)') '   Total number of cells    : ', i8_totalcells
       write(*,'(a,i0)') '   Number of tria bc faces  : ', ntface
       write(*,'(a,i0)') '   Number of quad bc faces  : ', nqface
       write(*,'(a,i0)') '   Total number of bc faces : ', ntface+nqface
    end if

    call lmpi_bcast(nbctags)
    allocate(iflag(nbctags))

    if (lmpi_id  == 0) then
       if (verbose2) then
         print *, 'tagmap : i, tag,  bc'
         do itag = 1,nbctags
           print '(3i10)', itag,tagmap(itag,1),tagmap(itag,2)
         end do
       end if
    end if

!   check if there are multiple bc tag specifcations

    do ichk = 1, nbctags
      do itag = 1, nbctags

        if   (   tagmap(itag,1)==tagmap(ichk,1) .and. ichk /= itag             &
           .and. tagmap(itag,2) /= tagmap(ichk,2)  )                           &
          print *,                                                             &
            '  WARNING: Multiple BC specification for surface tag, bc = ',     &
            tagmap(itag,1),tagmap(itag,2)

      end do
    end do

!   Figure out how many tags there are
!   count faces on each boundary instance with iflag

    iflag = 0
    do iface = 1, ntface + nqface
      itag = itagindex(facetag(iface))
      iflag(itag) = iflag(itag) + 1
    end do

!   count bc instances

    ntags = 0

    do itag = 1, nbctags
      if (iflag(itag) > 0) ntags = ntags + 1
    end do

    if (ntags /= nbctags) then
      if (lmpi_id == 0) &
        write (*,*) '  WARNING (ntags: ',ntags,'  /= nbctags: ',nbctags,' )'
      if (ntags > nbctags) then
        write (*,*) 'ERROR (ntags > nbctags)'
        write (*,*) 'ERROR stopping due to a memory check'
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end if
    end if

    if (lmpi_id == 0.and.timing) call timer('...count bc instances')

!   Allocate the bc instances

    grid%nbound = ntags

    allocate(grid%bc(grid%nbound))

!   allocate the boundary face to node pointer arrays
!   iflag holds the number of faces for each boundary
!   iflag will be set to a bc instance index

    ntags = 0

    alloc_bc_f2n : do itag = 1, nbctags

      if (iflag(itag)==0) cycle alloc_bc_f2n ! skip bc's with no faces

      ntags = ntags + 1

!     NOTE skip the translation of boundary numbers if a new number (e.g. >100)

      usm3d_bc = tagmap(itag,2)
      tagmap(itag,2) = usm3d_to_fun3d_bc(tagmap(itag,2),hanim)
      bad_bc_translation : if ( tagmap(itag,2) == -9999 ) then
        if (lmpi_master)  then
          write(*,*)"boundary face ",itag,tagmap(itag,1)," bc translation error"
          write(*,*)" originally ", usm3d_bc, " in mirror"
        end if
        call lmpi_conditional_stop(1,'mirrored usm3d bc translation')
      end if bad_bc_translation
      grid%bc(ntags)%ibc = tagmap(itag,2)

      ! NOTE(Pparty) Store true number of nbfaces for viscous and inviscid

      grid%bc(ntags)%nbfacetg = iflag(itag)
      grid%bc(ntags)%nbfaceqg = iflag(itag) ! TBD test for nbfaceq

!     f2ntb and f2nqb are overdimensioned here (each by the total number
!     of faces)...will  reallocate once the final counts for quad
!     and tria faces are known

      call my_alloc_ptr(grid%bc(ntags)%f2ntb,iflag(itag),5)
      call my_alloc_ptr(grid%bc(ntags)%f2nqb,iflag(itag),6)

      grid%bc(ntags)%nbfacet = 0
      grid%bc(ntags)%nbfaceq = 0

      iflag(itag) = ntags                  ! set iflag to index the bc instance

    end do alloc_bc_f2n

    ntags = 0

    symmetry_quarantine : do itag = 1, nbctags
      if (iflag(itag)==0) cycle symmetry_quarantine ! skip bc's with no faces
      ntags = ntags + 1
      if ( .not. grid%cc ) cycle symmetry_quarantine
      ibcc = grid%bc(ntags)%ibc
      if (ibcc == symmetry_x) grid%bc(ntags)%ibc = symmetry_x
      if (ibcc == symmetry_y) grid%bc(ntags)%ibc = symmetry_y
      if (ibcc == symmetry_z) grid%bc(ntags)%ibc = symmetry_z
    enddo symmetry_quarantine

    if (lmpi_id == 0.and.timing) call timer('... allocate bc arrays')

!   loop to fill f2ntb and f2nqb arrays

    loop1: do iface = 1, ntface + nqface

      ibound = iflag(itagindex(facetag(iface)))

      if (faceptr(iface,4)==0) then ! tri-faces
        grid%bc(ibound)%nbfacet = grid%bc(ibound)%nbfacet + 1
        do inode = 1, 3
          grid%bc(ibound)%f2ntb(grid%bc(ibound)%nbfacet, inode) =              &
            faceptr(iface,inode)
        end do
      else ! quad-faces
        grid%bc(ibound)%nbfaceq = grid%bc(ibound)%nbfaceq + 1
        do inode = 1, 4
          grid%bc(ibound)%f2nqb(grid%bc(ibound)%nbfaceq, inode) =              &
            faceptr(iface,inode)
        end do
      endif

    end do loop1

    if (lmpi_id == 0.and.timing)                                               &
       call timer('... reallocate based on final face counts')

!   now reallocate based on final face counts

    do ibound = 1,grid%nbound
      if (grid%bc(ibound)%nbfacet > 0) then
        call my_realloc_ptr(grid%bc(ibound)%f2ntb,grid%bc(ibound)%nbfacet,5)
      else
        call my_realloc_ptr(grid%bc(ibound)%f2ntb,1,5)
      end if
      if (grid%bc(ibound)%nbfaceq > 0) then
        call my_realloc_ptr(grid%bc(ibound)%f2nqb,grid%bc(ibound)%nbfaceq,6)
      else
        call my_realloc_ptr(grid%bc(ibound)%f2nqb,1,6)
      end if

    end do

    deallocate(iflag)

    if (lmpi_id == 0.and.timing)                                               &
       call timer('... END process_grid_bc_miror_allocate')

  end subroutine process_grid_bc_mirror_allocate


!============================= PROCESS_GRID_BC_MIRROR_FILL ===================80
!
! Process bc for grids altered after reading (e.g., mirroring) but before
! called metis/ParMetis.
!
!=============================================================================80

  subroutine process_grid_bc_mirror_fill( grid )

    use grid_types, only : grid_type

    type(grid_type), intent(inout) :: grid

    integer                                    :: ibound,j,k
    integer,         dimension(:), allocatable :: t_nbfacet, t_nbfaceq
    type(t_bc_type), dimension(:), allocatable :: t_bc

    continue

    if (lmpi_id == 0.and.timing)                                               &
       call timer('... START process_grid_bc_mirror_fill')

! Save off current nbfacet,f2ntb, nbfaceq,f2nqb

    allocate(t_nbfacet(grid%nbound)); t_nbfacet = grid%bc(:)%nbfacet
    allocate(t_nbfaceq(grid%nbound)); t_nbfaceq = grid%bc(:)%nbfaceq
    allocate(t_bc(grid%nbound))

    do ibound = 1,grid%nbound
       j = max(t_nbfacet(ibound),1)
       k = max(t_nbfaceq(ibound),1)
       allocate(t_bc(ibound)%f2ntb(j,3)); t_bc(ibound)%f2ntb = 0
       allocate(t_bc(ibound)%f2nqb(k,4)); t_bc(ibound)%f2nqb = 0
       if(t_nbfacet(ibound)>0)t_bc(ibound)%f2ntb = grid%bc(ibound)%f2ntb(:,1:3)
       if(t_nbfaceq(ibound)>0)t_bc(ibound)%f2nqb = grid%bc(ibound)%f2nqb(:,1:4)
     ! Zero out old
       grid%bc(ibound)%nbfacet = 0
       grid%bc(ibound)%nbfaceq = 0
       grid%bc(ibound)%f2ntb = 0
       grid%bc(ibound)%f2nqb = 0
    end do

   call process_grid_bc_faces_mirror(grid%nbound,t_nbfacet,t_nbfaceq,t_bc,grid)

    deallocate(faceptr)
    deallocate(facetag)
    deallocate(tagmap)

    if (lmpi_id == 0.and.timing)                                               &
       call timer('... END process_grid_bc_mirror_fill')

  end subroutine process_grid_bc_mirror_fill

!================================ READVGRID ==================================80
!
! Reads the c2n information from VGRID cogsg Format Meshes
!
!=============================================================================80

  subroutine puns3d_read_vgrid_c2n(flow_dir,grid)

    use element_defs,  only : type_tet, initialize_elem
    use grid_types,    only : grid_type
    use file_utils,    only : se_open_big_is_big
    use system_extensions, only : se_open_big

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(inout) :: grid

    integer  :: iostat, ntet, local_unit_grid, nsegments, i
    integer  :: error_code

    continue

    if (lmpi_id == 0) then
       local_unit_grid = unit_grid + 100
       if (.not.se_open_big_is_big()) then
          write(*,*) 'File I/O is not big endian. Unable to process VGrid mesh.'
          write(*,*) 'Please see your Fortran compiler endianess documentation.'
          call lmpi_conditional_stop(1)
       end if
       gridfilename = trim(flow_dir) // trim(grid%project) // '.cogsg'

       call se_open_big(local_unit_grid,file=gridfilename,form='unformatted',  &
            status='old',iostat=iostat,convert='big_endian')
       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(gridfilename),' stopping...'
         call lmpi_conditional_stop(iostat)
       endif
       rewind (unit=local_unit_grid)

 !     read mesh parameters

       call read_cogsg_dim(local_unit_grid,ntet,grid%nnodesg,nsegments,        &
         error_code )
       if ( error_code /= 0 ) then
         write(*,*)'error reading ',trim(gridfilename),' header, stopping...'
         call lmpi_conditional_stop(error_code)
       endif

       write(*,*)'    ... nsegments,ntet,nnodesg ',nsegments,ntet,grid%nnodesg

    end if
    call lmpi_conditional_stop(0)

    call lmpi_bcast(ntet)
    grid%ncellg = ntet
    call lmpi_bcast(grid%nnodesg)
    call distribute_fast_c2n(grid%nnodesg)

!   just one element type (tets)

    grid%nelem = 1

    allocate(grid%elem(grid%nelem))

!   now set up a few items for the element derived type

    call initialize_elem(grid%elem(1),type_tet)

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%elem(1)%ncell = 0
    grid%elem(1)%ncellg = ntet
    i = max(nint((ntet/lmpi_nproc) * 2.0), 10000)
    !write(*,*)"ALLOC c2n ",lmpi_id,i,ntet,ntet/lmpi_nproc

    allocate(grid%elem(1)%c2n(4,i)); grid%elem(1)%c2n  = 0
    allocate(grid%elem(1)%cl2g(i));  grid%elem(1)%cl2g = 0
   !allocate(grid%elem(1)%c2e(6,1)); grid%elem(1)%c2e  = 0

    allocate(grid%l2g(grid%nnodes0))
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)-1)+i
    end do
    allocate(grid%x(grid%nnodes0)); grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0)); grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0)); grid%z = 0.0_dp

    call puns3d_read_vgrid_c2n_SM(local_unit_grid,grid)

    if (lmpi_id == 0) close(local_unit_grid)

  end subroutine puns3d_read_vgrid_c2n


!========================== PUNS3D_READ_VGRID_VOL_C2N_SM =====================80
!
! Reads a single (or multisegmented) cogsg file, and extract the c2n data
! in a parallel environment.
!
! Note: grid%elem(1)%ncell reflects the running number versus the allocated
!       size during this routine.  Right before returning, the variable is
!       updated to the standard.
!
!=============================================================================80

  subroutine puns3d_read_vgrid_c2n_SM(unit_grid, grid)

    use grid_types,    only : grid_type

    integer,         intent(in)    :: unit_grid
    type(grid_type), intent(inout) :: grid

    integer :: i,j, ierr
    integer :: inew, mtet, mnodesg, nbou1, npoiv, nelev, nelec,nsegment,npoic
    integer :: bs, be, isegment, xs, xe
!beginNeverComplex
    real(dp):: ttime
!endNeverComplex

    integer,  dimension(:,:), allocatable :: tc2n
!beginNeverComplex
    real(dp), dimension(:),   allocatable :: x,y,z
!endNeverComplex

    continue

    if (lmpi_master) then

       rewind(unit_grid)
       read(unit_grid,end=300)inew,mtet,mnodesg,nbou1,npoiv,nelev,ttime

       if (verbose) then
          write(*,*)'inew,mtet,mnodesg,nbou1,npoiv,nelev,ttime = ',            &
                     inew,mtet,mnodesg,nbou1,npoiv,nelev,ttime
       end if

       nsegment = iabs(inew)
    end if
    call lmpi_bcast(nsegment)

    bs = 1
    if (lmpi_master) be = mtet
    call lmpi_bcast(be)

    ! For non-master, allocate dummy arguments
      if (.not.lmpi_master) then
         allocate(tc2n(4,1)); tc2n = 0
         allocate(x(1)); x = 0.0_dp
         allocate(y(1)); y = 0.0_dp
         allocate(z(1)); z = 0.0_dp
      end if

    if (lmpi_master) then
       allocate(tc2n(4,bs:be)); tc2n = 0
       rewind(unit_grid)
       read(unit_grid,end=300) inew, mtet, mnodesg, nbou1, npoiv, nelev, ttime,&
                              ((tc2n(j,i),i=bs,be),j=1,4)
      !do i = 1,mtet
      !   write(9000,'(1x,i0,":",1x,4(i0,1x))') i,tc2n(:,i)
      !end do
       call distribute_tet(1,nsegment,bs,be,grid,4,bs,be,tc2n)
     else
       call distribute_tet(1,nsegment,bs,be,grid,4,1,1,tc2n)
    end if
    if (lmpi_master) deallocate(tc2n)

    xs = 1
    if (lmpi_master) xe = mnodesg
    call lmpi_bcast(xe)
    !if (lmpi_master) write(*,*)'iseg,xs,xe ',lmpi_id,1,xs,xe

    if (lmpi_master) then
       allocate(x(xs:xe))
       allocate(y(xs:xe))
       allocate(z(xs:xe))
       read(unit_grid,end=300) (x(i),i=xs,xe),(y(i),i=xs,xe),(z(i),i=xs,xe)
      !do i = xs,xe
      !   write(8000,'(1x,i0,":",1x,3(F20.12,1x))') i,x(i),y(i),z(i)
      !end do
       call distribute_xyz(xs,xe,grid,xs,xe,x,y,z)
    else
       call distribute_xyz(xs,xe,grid,1,1,x,y,z)
    end if
    if (lmpi_master) deallocate(x,y,z)
    xs = xe+1

    if (nsegment > 1) then

       do isegment = 2,nsegment

          if (lmpi_master) read(unit_grid,end=400) nelec
          !if (lmpi_master) write(*,*)"nelec ",nelec
          call lmpi_bcast(nelec)

          if (nelec == 0) then
             if (lmpi_master) write(*,*)"VGRID Invalid nelec ",isegment,nelec
             call lmpi_die
          end if

          bs = be + 1
          be = be + nelec

          if (lmpi_master) then
             allocate(tc2n(4,bs:be))
             read(unit_grid,end=300) (tc2n(1,i),i=bs,be),(tc2n(2,i),i=bs,be),  &
                                     (tc2n(3,i),i=bs,be),(tc2n(4,i),i=bs,be)
           ! do i = bs,be
           !    write(9000,'(1x,i0,":",1x,4(i0,1x))') i,tc2n(:,i)
           ! end do
             call distribute_tet(isegment,nsegment,bs,be,grid,4,bs,be,tc2n)
          else
             call distribute_tet(isegment,nsegment,bs,be,grid,4,1,1,tc2n)
          end if
          if (lmpi_master) deallocate(tc2n)

          if (lmpi_master) read(unit_grid,end=300) npoic
          !if (lmpi_master) write(*,*)"npoic ",npoic
          call lmpi_bcast(npoic)
          xe = (xs + npoic)-1
          if (lmpi_master) then
             !write(*,*)'iseg,xs,xe ',lmpi_id,isegment,xs,xe
             allocate(x(xs:xe))
             allocate(y(xs:xe))
             allocate(z(xs:xe))
             read(unit_grid,end=300)(x(i),i=xs,xe),(y(i),i=xs,xe),(z(i),i=xs,xe)
           ! do i = xs,xe
           !    write(8000,'(1x,i0,":",1x,3(F20.12,1x))') i,x(i),y(i),z(i)
           ! end do
             call distribute_xyz(xs,xe,grid,xs,xe,x,y,z)
          else
             call distribute_xyz(xs,xe,grid,1,1,x,y,z)
          end if
          if (lmpi_master) deallocate(x,y,z)
          xs = xe+1

       end do
    end if
    if (size(grid%elem(1)%c2n,2) /= grid%elem(1)%ncell) then
       write(*,*)"SIZE c2n mismatch ",                                         &
         lmpi_id,grid%elem(1)%ncell,size(grid%elem(1)%c2n,2)
       write(*,*) 'Read error.'
       stop ! FIXME: should be lmpi_die or se_exit(1)?
    end if

    ! Deallocate dummy variables
    if (.not.lmpi_master) then
       deallocate(x,y,z)
       deallocate(tc2n)
    end if

    !   do i = 1,grid%nnodes0
    !      write(18000+lmpi_id,'(1x,i0,":",1x,3(F20.12,1x))')                  &
    !            grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
    !   end do
    !   do i = 1,grid%elem(1)%ncell
    !      write(19000+lmpi_id,'(1x,i0,":",1x,4(i0,1x))')                      &
    !            grid%elem(1)%cl2g(i),grid%elem(1)%c2n(:,i)
    !   end do

    grid%elem(1)%ncellg = be

return

300 continue
    write(*,*)'Unexpected END (300) reached in segment '
    call lmpi_die

400 continue
    ! read end=400 (nelec) should not occur. If it does, bcast matching 0.
    write(*,*)'Unexpected END (300) reached in segment '
    ierr = 0
    call lmpi_bcast(ierr) ! FIXME: why bother, given the next statement?
    call lmpi_die

  end subroutine puns3d_read_vgrid_c2n_SM


!========================== DISTRIBUTE_TET ===================================80
!
! Distribute tet by bcast and collect elements based on pp_nhead and pp_ntail.
!
!=============================================================================80

  subroutine distribute_tet(ipass,npass,bs,be,grid,d1,d2s,d2e,tc2n)

    use grid_types,  only : grid_type

    integer,                        intent(in)    :: ipass,npass
    integer,                        intent(in)    :: bs,be,d1,d2s,d2e
    type(grid_type),                intent(inout) :: grid
    integer, dimension(d1,d2s:d2e), intent(in)    :: tc2n

    integer :: new_size, actual_size, old_ncell,ict
    integer :: i,j,isize,nloop,iloop,icount,islice
    integer :: is,ie,ifound

    integer, dimension(:),   allocatable :: tag,  old_cl2g
    integer, dimension(:,:), allocatable :: pc2n, old_c2n

    continue

    !write(*,*)"ENTER dist_c2n ",lmpi_id,bs,be,d2s,d2e
    actual_size = size(grid%elem(1)%cl2g)

    icount = (be-bs)+1
    isize = (icount * d1)
    nloop = isize/bcast_buffer_size
    if (isize > nloop*bcast_buffer_size) nloop = nloop + 1

    islice = icount/nloop
    if ((islice*nloop) < icount) islice = islice + 1
    !write(*,*)'islice ',d1,islice
    allocate(pc2n(d1,islice)); pc2n = 0
    allocate(tag(islice))
    !if (lmpi_master)write(*,*)'bLOOP nloop,islice ',nloop,isize,islice

    is = bs
    do iloop = 1,nloop
       ie = (is + islice)-1
       if (ie > be) ie = be
       ict = (ie-is)+1
       if (ict > islice) write(*,*)'ICT > islice ',ict,islice,is,ie,(ie-is)+1

       !if (lmpi_master) write(*,'("bLOOP ",7(i0,1x))')
       !   iloop,nloop,is,ie,bs,be,ict
       if (lmpi_master) pc2n(1:d1,1:ict) = tc2n(1:d1,is:ie)
       call lmpi_bcast(pc2n)

       tag = 0
       ifound = 0
       do i = 1,ict
          !write(28000+lmpi_id,'(1x,i0,":",1x,4(i0,1x))') i,pc2n(:,i)
          do j = 1,d1
             if ((pc2n(j,i) >= pp_nhead(lmpi_id)).and.                         &
                 (pc2n(j,i) <= pp_ntail(lmpi_id))) then
                 ifound = ifound + 1
                 tag(ifound) = i
                 exit
             end if
          end do
       end do

       old_ncell = grid%elem(1)%ncell

       !write(*,*)"IFOUND ",lmpi_id,ifound,old_ncell,actual_size
       if (ifound > 0) then

          grid%elem(1)%ncell = old_ncell + ifound
          if (grid%elem(1)%ncell > actual_size) then

             if ((ipass == npass).and.(iloop == nloop)) then
                new_size = grid%elem(1)%ncell
             else
                new_size = grid%elem(1)%ncell*1.5
             end if

             allocate(old_c2n(4,actual_size)); old_c2n = grid%elem(1)%c2n
             deallocate(grid%elem(1)%c2n); nullify(grid%elem(1)%c2n)
             allocate(grid%elem(1)%c2n(4,new_size))

             !write(*,*)"REALLOC ",lmpi_id,new_size
             grid%elem(1)%c2n(:,1:actual_size) = old_c2n(:,1:actual_size)
             grid%elem(1)%c2n(:,actual_size+1:new_size) = 0
             deallocate(old_c2n)

             allocate(old_cl2g(actual_size)); old_cl2g = grid%elem(1)%cl2g
             deallocate(grid%elem(1)%cl2g);   nullify(grid%elem(1)%cl2g)
             allocate(grid%elem(1)%cl2g(new_size))
             grid%elem(1)%cl2g(1:actual_size) = old_cl2g(1:actual_size)
             grid%elem(1)%cl2g(actual_size+1:new_size) = 0
             deallocate(old_cl2g)

             actual_size = new_size
          end if

          !write(*,*)"ASSIGN ",lmpi_id,                                        &
          !  old_ncell+1,old_ncell+ifound,size(grid%elem(1)%c2n,2)
          do i = 1,ifound
             grid%elem(1)%c2n(:,old_ncell+i) = pc2n(:,tag(i))
             grid%elem(1)%cl2g(old_ncell+i) = (is-1)+tag(i)
          end do
       end if

       is = ie+1
    end do
    deallocate(pc2n)
    deallocate(tag)

    if ((ipass == npass)) then
       if (actual_size == 0) then
          write(*,*)"Error distributing c2n. Non-found for ",lmpi_id
          call lmpi_conditional_stop(1)
       else
          call lmpi_conditional_stop(0)
       end if

       if (actual_size >  grid%elem(1)%ncell) then
          !write(*,*)"FINAL REALLOC ",lmpi_id,grid%elem(1)%ncell,actual_size

          allocate(old_c2n(4,grid%elem(1)%ncell))
          old_c2n = grid%elem(1)%c2n(:,1:grid%elem(1)%ncell)
          deallocate(grid%elem(1)%c2n)
          nullify(grid%elem(1)%c2n)

          allocate(grid%elem(1)%c2n(4,grid%elem(1)%ncell))
          grid%elem(1)%c2n = old_c2n
          deallocate(old_c2n)

          allocate(old_cl2g(grid%elem(1)%ncell))
          old_cl2g = grid%elem(1)%cl2g(1:grid%elem(1)%ncell)
          deallocate(grid%elem(1)%cl2g)
          nullify(grid%elem(1)%cl2g)

          allocate(grid%elem(1)%cl2g(grid%elem(1)%ncell))
          grid%elem(1)%cl2g = old_cl2g
          deallocate(old_cl2g)
       end if
    end if

  end subroutine distribute_tet


!================================ READVGRID_SIO ==============================80
!
! Reads the c2n information from VGRID cogsg Format Meshes
!
!=============================================================================80

  subroutine puns3d_read_vgrid_c2n_sio(flow_dir,grid)

    use element_defs,  only : type_tet, initialize_elem
    use grid_types,    only : grid_type

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(inout) :: grid

    integer :: iostat, ntet, iunit, nsegments, i

    integer  :: nnodesg,nbou1,npoiv,nelev
    real(dp) :: ttime

    continue

    if (lmpi_id == 0) then
       iunit = unit_grid + 100
       gridfilename = trim(flow_dir) // trim(grid%project) // '.iostream'

       call se_open(iunit,file=gridfilename,form='unformatted',access="stream",&
           status='old',iostat=iostat)

       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(gridfilename),' stopping...'
         call lmpi_conditional_stop(iostat)
       endif
       rewind (unit=iunit)

 !     read mesh parameters

      !call read_cogsg_dim(iunit,ntet,grid%nnodesg,nsegments)
       read(iunit,end=300,iostat=iostat)                                       &
           nsegments,ntet,nnodesg,nbou1,npoiv,nelev,ttime
       if (nsegments > 1000000)                                                &
          write(*,*) nsegments,ntet,nnodesg,nbou1,npoiv,nelev,ttime
       if (nsegments < 0) nsegments = -nsegments

       write(*,*)'    ... nsegments,ntet,nnodesg ',nsegments,ntet,nnodesg

    end if
    call lmpi_conditional_stop(0)

    call lmpi_bcast(ntet)
    grid%ncellg = ntet

    call lmpi_bcast(nnodesg)
    grid%nnodesg = nnodesg

    call distribute_fast_c2n(grid%nnodesg)

!   just one element type (tets)

    grid%nelem = 1

    allocate(grid%elem(grid%nelem))

!   now set up a few items for the element derived type

    call initialize_elem(grid%elem(1),type_tet)

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%elem(1)%ncell = 0
    grid%elem(1)%ncellg = ntet
    i = max(nint((ntet/lmpi_nproc) * 2.0), 10000)
    !write(*,*)"ALLOC c2n ",lmpi_id,i,ntet,ntet/lmpi_nproc

    allocate(grid%elem(1)%c2n(4,i)); grid%elem(1)%c2n  = 0
    allocate(grid%elem(1)%cl2g(i));  grid%elem(1)%cl2g = 0
   !allocate(grid%elem(1)%c2e(6,1)); grid%elem(1)%c2e  = 0

    allocate(grid%l2g(grid%nnodes0))
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)-1)+i
    end do
    allocate(grid%x(grid%nnodes0)); grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0)); grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0)); grid%z = 0.0_dp

    call puns3d_read_vgrid_c2n_SM_sio(iunit,grid)

    if (lmpi_id == 0) close(iunit)

    return
  300 continue
    write(*,*)"Unexpected EOF ",trim(gridfilename)
    call lmpi_die(); stop

  end subroutine puns3d_read_vgrid_c2n_sio


!========================== PUNS3D_READ_VGRID_VOL_C2N_SM =====================80
!
! Reads a single (or multisegmented) cogsg file, and extract the c2n data
! in a parallel environment.
!
! Note: grid%elem(1)%ncell reflects the running number versus the allocated
!       size during this routine.  Right before returning, the variable is
!       updated to the standard.
!
!=============================================================================80

  subroutine puns3d_read_vgrid_c2n_SM_sio(unit_grid, grid)

    use grid_types,    only : grid_type

    integer,         intent(in)    :: unit_grid
    type(grid_type), intent(inout) :: grid

    integer :: i,j, ierr, nloop, iloop, cell_start, cell_end
    integer :: ie, nelem, ipe, isize, iostat

    integer(kind=system_i8) :: cell_bytes, n8loop

    integer,  dimension(:,:), allocatable :: tc2n
    real(dp), dimension(:),   allocatable :: x

    continue

!                                  ncellg *dim1*4bytes
    cell_bytes = (1_system_i8*grid%ncellg)*(4*4*1_system_i8)
!   if (lmpi_master)                                                           &
!      write(*,*)"Cell_bytes ",cell_bytes,grid%ncellg,grid%ncellg*16
    n8loop = cell_bytes/(bcast_buffer_size*1_system_i8)
    if (cell_bytes > n8loop*(bcast_buffer_size*1_system_i8)) n8loop = n8loop+1
    nloop = n8loop

    nelem = grid%ncellg/nloop
    if ((nelem*nloop) < grid%ncellg) nelem = nelem + 1

    allocate(tc2n(4,nelem)); tc2n = 0

    ie = nelem
    cell_start = 1
    do iloop = 1,nloop
       cell_end = (cell_start + nelem)-1
!      if (lmpi_master)                                                        &
!         write(*,'(a40,5(i0,1x))')"iloop,nloop,cell_start,cell_end,ie ",      &
!                                   iloop,nloop,cell_start,cell_end,ie
       if (iloop == nloop) then
          if (cell_end > grid%ncellg) cell_end = grid%ncellg
          ie = (cell_end-cell_start)+1
          if (ie /= nelem) then
             deallocate(tc2n)
             allocate(tc2n(4,ie))
          end if
       end if

       if (lmpi_master) read(unit_grid,end=300) ((tc2n(j,i),j=1,4),i=1,ie)
       call lmpi_bcast(tc2n)
       call distribute_tet_sio(4,ie,cell_start,cell_end,tc2n,grid)
       cell_start = cell_end+1
    end do ! cells
    deallocate(tc2n)

    ierr = 0
    if (lmpi_master) then
! X
       !if (lmpi_master) write(*,*)"Starting x"
       read(unit_grid,end=300)(grid%x(i),i=1,pp_nsize(lmpi_id))
       do ipe = 1,lmpi_nproc-1
          isize = pp_nsize(ipe)
          allocate(x(isize),stat=iostat)
          if (iostat /= 0) write(*,*)"Allocate X error ",iostat
          x = 0.0_dp
          read(unit_grid,end=300)(x(i),i=1,isize)
          call lmpi_send(x,isize,ipe,ipe*100,ierr)
          deallocate(x)
       end do
! Y
       !if (lmpi_master) write(*,*)"Starting y"
       read(unit_grid,end=300)(grid%y(i),i=1,pp_nsize(lmpi_id))
       do ipe = 1,lmpi_nproc-1
          isize = pp_nsize(ipe)
          allocate(x(isize),stat=iostat)
          if (iostat /= 0) write(*,*)"Allocate Y error ",iostat
          x = 0.0_dp
          read(unit_grid,end=300)(x(i),i=1,isize)
          call lmpi_send(x,isize,ipe,ipe*100,ierr)
          deallocate(x)
       end do
! Z
       !if (lmpi_master) write(*,*)"Starting z"
       read(unit_grid,end=300)(grid%z(i),i=1,pp_nsize(lmpi_id))
       do ipe = 1,lmpi_nproc-1
          isize = pp_nsize(ipe)
          allocate(x(isize))
          if (iostat /= 0) write(*,*)"Allocate Z error ",iostat
          x = 0.0_dp
          read(unit_grid,end=300)(x(i),i=1,isize)
          call lmpi_send(x,isize,ipe,ipe*100,ierr)
          deallocate(x)
       end do
    else
       isize = pp_nsize(lmpi_id)
       call lmpi_recv(grid%x,isize,0,lmpi_id*100,ierr)
       call lmpi_recv(grid%y,isize,0,lmpi_id*100,ierr)
       call lmpi_recv(grid%z,isize,0,lmpi_id*100,ierr)
    end if

    call lmpi_conditional_stop(ierr)

return

300 continue
    write(*,*)'Unexpected END (300) reached in segment '
    call lmpi_conditional_stop(ierr)

  end subroutine puns3d_read_vgrid_c2n_SM_sio


!========================== DISTRIBUTE_TET ===================================80
!
! Distribute tet by bcast and collect elements based on pp_nhead and pp_ntail.
!
!=============================================================================80

  subroutine distribute_tet_sio(d1,d2,d2s,d2e,tc2n,grid)

    use grid_types,  only : grid_type

    integer,                   intent(in)    :: d1,d2,d2s,d2e
    integer, dimension(d1,d2), intent(in)    :: tc2n
    type(grid_type),           intent(inout) :: grid

    integer :: new_size, actual_size, old_ncell
    integer :: i,j,ifound,ncellg

    integer, dimension(:),   allocatable :: tag,  old_cl2g
    integer, dimension(:,:), allocatable :: old_c2n

    continue

   !write(*,*)"ENTER dist_c2n ",lmpi_id,d1,d2,d2s,d2e
    actual_size = size(grid%elem(1)%cl2g)
    ncellg = grid%elem(1)%ncellg

    allocate(tag(d2)); tag = 0

    ifound = 0
    do i = 1,d2
       !write(28000+lmpi_id,'(1x,i0,":",1x,4(i0,1x))') i,pc2n(:,i)
       do j = 1,d1
          if ((tc2n(j,i) >= pp_nhead(lmpi_id)).and.                            &
              (tc2n(j,i) <= pp_ntail(lmpi_id))) then
             ifound = ifound + 1
             tag(ifound) = i
             exit
          end if
       end do
    end do

    old_ncell = grid%elem(1)%ncell

    if (ifound > 0) then

               !write(*,*)"FOUND ",lmpi_id,ifound,old_ncell,actual_size

       grid%elem(1)%ncell = old_ncell + ifound

       if (grid%elem(1)%ncell > actual_size) then

          if (d2e == ncellg) then
             new_size = grid%elem(1)%ncell
          else
             new_size = grid%elem(1)%ncell*1.5
            !write(1000+lmpi_id,*)"REALLOC ",lmpi_id,new_size,actual_size
          end if

          allocate(old_c2n(4,actual_size)); old_c2n = grid%elem(1)%c2n
          deallocate(grid%elem(1)%c2n); nullify(grid%elem(1)%c2n)
          allocate(grid%elem(1)%c2n(4,new_size))

          !write(*,*)"REALLOC ",lmpi_id,new_size
          grid%elem(1)%c2n(:,1:actual_size) = old_c2n(:,1:actual_size)
          grid%elem(1)%c2n(:,actual_size+1:new_size) = 0
          deallocate(old_c2n)

          allocate(old_cl2g(actual_size)); old_cl2g = grid%elem(1)%cl2g
          deallocate(grid%elem(1)%cl2g);   nullify(grid%elem(1)%cl2g)
          allocate(grid%elem(1)%cl2g(new_size))
          grid%elem(1)%cl2g(1:actual_size) = old_cl2g(1:actual_size)
          grid%elem(1)%cl2g(actual_size+1:new_size) = 0
          deallocate(old_cl2g)

          actual_size = new_size
       end if

            !write(*,*)"ASSIGN ",lmpi_id,                                      &
            !  old_ncell+1,old_ncell+ifound,size(grid%elem(1)%c2n,2)
       do i = 1,ifound
          grid%elem(1)%c2n(:,old_ncell+i) = tc2n(:,tag(i))
          grid%elem(1)%cl2g(old_ncell+i) = (d2s+tag(i))-1
       end do
    end if

    deallocate(tag)

    if (d2e == ncellg) then
       if (actual_size == 0) then
          write(*,*)"Error distributing c2n. Non-found for ",lmpi_id
          call lmpi_conditional_stop(1)
       else
          call lmpi_conditional_stop(0)
       end if

       if (actual_size >  grid%elem(1)%ncell) then
          !write(*,*)"FINAL REALLOC ",lmpi_id,grid%elem(1)%ncell,actual_size

          allocate(old_c2n(4,grid%elem(1)%ncell))
          old_c2n = grid%elem(1)%c2n(:,1:grid%elem(1)%ncell)
          deallocate(grid%elem(1)%c2n)
          nullify(grid%elem(1)%c2n)

          allocate(grid%elem(1)%c2n(4,grid%elem(1)%ncell))
          grid%elem(1)%c2n = old_c2n
          deallocate(old_c2n)

          allocate(old_cl2g(grid%elem(1)%ncell))
          old_cl2g = grid%elem(1)%cl2g(1:grid%elem(1)%ncell)
          deallocate(grid%elem(1)%cl2g)
          nullify(grid%elem(1)%cl2g)

          allocate(grid%elem(1)%cl2g(grid%elem(1)%ncell))
          grid%elem(1)%cl2g = old_cl2g
          deallocate(old_cl2g)
       end if
    end if

  end subroutine distribute_tet_sio


!========================== DISTRIBUTE_MIX ===================================80
!
! Distribute tet by bcast and collect elements based on pp_nhead and pp_ntail.
!
!=============================================================================80

  subroutine distribute_mix(ipass,npass,bs,be,grid,d1,d2s,d2e,tc2n,ielem,goff)

    use grid_types,   only : grid_type

    integer,                        intent(in)    :: ipass,npass,ielem
    integer,                        intent(in)    :: bs,be,d1,d2s,d2e,goff
    type(grid_type),                intent(inout) :: grid
    integer, dimension(d1,d2s:d2e), intent(in)    :: tc2n

    integer :: new_size, actual_size, old_ncell,ict
    integer :: i,j,isize,nloop,iloop,icount,islice
    integer :: is,ie,ifound

    integer, dimension(:),   allocatable :: tag,  old_cl2g
    integer, dimension(:,:), allocatable :: pc2n, old_c2n

    continue

    !write(*,*)"ENTER dist_mix ",lmpi_id,bs,be,d2s,d2e
    actual_size = size(grid%elem(ielem)%cl2g)

    icount = (be-bs)+1
    isize = (icount * d1)
    nloop = isize/bcast_buffer_size
    if (isize > nloop*bcast_buffer_size) nloop = nloop + 1

    islice = icount/nloop
    if ((islice*nloop) < icount) islice = islice + 1
    !write(*,*)'islice ',d1,islice
    allocate(pc2n(d1,islice)); pc2n = 0
    allocate(tag(islice))
    !if (lmpi_master)write(*,*)'bLOOP nloop,islice ',nloop,isize,islice

    is = bs
    do iloop = 1,nloop
       ie = (is + islice)-1
       if (ie > be) ie = be
       ict = (ie-is)+1
       if (ict > islice) write(*,*)'ICT > islice ',ict,islice,is,ie,(ie-is)+1

       if (verbose2) write(*,'("... bLOOP ",7(i0,1x))')                        &
          iloop,nloop,is,ie,bs,be,ict
       if (lmpi_master) pc2n(1:d1,1:ict) = tc2n(1:d1,is:ie)
       call lmpi_bcast(pc2n)

       tag = 0
       ifound = 0
       do i = 1,ict
          !write(28000+lmpi_id,'(1x,i0,":",1x,4(i0,1x))') i,pc2n(:,i)
          do j = 1,d1
             if ((pc2n(j,i) >= pp_nhead(lmpi_id)).and.                         &
                 (pc2n(j,i) <= pp_ntail(lmpi_id))) then
                 ifound = ifound + 1
                 tag(ifound) = i
                 exit
             end if
          end do
       end do

       old_ncell = grid%elem(ielem)%ncell

       !write(*,*)"IFOUND ",lmpi_id,ifound,old_ncell,actual_size
       if (ifound > 0) then

          grid%elem(ielem)%ncell = old_ncell + ifound
          if (grid%elem(ielem)%ncell > actual_size) then

             if ((ipass == npass).and.(iloop == nloop)) then
                new_size = grid%elem(ielem)%ncell
             else
                new_size = grid%elem(ielem)%ncell*1.5
             end if

             allocate(old_c2n(d1,actual_size)); old_c2n = grid%elem(ielem)%c2n
             deallocate(grid%elem(ielem)%c2n); nullify(grid%elem(ielem)%c2n)
             allocate(grid%elem(ielem)%c2n(d1,new_size))

             !write(*,*)"REALLOC ",lmpi_id,new_size
             grid%elem(ielem)%c2n(:,1:actual_size) = old_c2n(:,1:actual_size)
             grid%elem(ielem)%c2n(:,actual_size+1:new_size) = 0
             deallocate(old_c2n)

             allocate(old_cl2g(actual_size)); old_cl2g = grid%elem(ielem)%cl2g
             deallocate(grid%elem(ielem)%cl2g);   nullify(grid%elem(ielem)%cl2g)
             allocate(grid%elem(ielem)%cl2g(new_size))
             grid%elem(ielem)%cl2g(1:actual_size) = old_cl2g(1:actual_size)
             grid%elem(ielem)%cl2g(actual_size+1:new_size) = 0
             deallocate(old_cl2g)

             actual_size = new_size
          end if

          !write(*,*)"ASSIGN ",lmpi_id,                                        &
          !  old_ncell+1,old_ncell+ifound,size(grid%elem(ielem)%c2n,2)
          do i = 1,ifound
             grid%elem(ielem)%c2n(:,old_ncell+i) = pc2n(:,tag(i))
             grid%elem(ielem)%cl2g(old_ncell+i) = (is-1)+tag(i)+goff
          end do
       end if

       is = ie+1
    end do
    deallocate(pc2n)
    deallocate(tag)

    if (ipass == npass) then
       if (actual_size == 0) then
          write(*,*)"Error distributing c2n. Non-found for ",lmpi_id
          call lmpi_conditional_stop(1)
       else
          call lmpi_conditional_stop(0)
       end if

       if (actual_size >  grid%elem(ielem)%ncell) then
          !write(*,*)"FINAL REALL ",lmpi_id,grid%elem(ielem)%ncell,actual_size

          allocate(old_c2n(d1,grid%elem(ielem)%ncell))
          old_c2n = grid%elem(ielem)%c2n(:,1:grid%elem(ielem)%ncell)
          deallocate(grid%elem(ielem)%c2n)
          nullify(grid%elem(ielem)%c2n)

          allocate(grid%elem(ielem)%c2n(d1,grid%elem(ielem)%ncell))
          grid%elem(ielem)%c2n = old_c2n
          deallocate(old_c2n)

          allocate(old_cl2g(grid%elem(ielem)%ncell))
          old_cl2g = grid%elem(ielem)%cl2g(1:grid%elem(ielem)%ncell)
          deallocate(grid%elem(ielem)%cl2g)
          nullify(grid%elem(ielem)%cl2g)

          allocate(grid%elem(ielem)%cl2g(grid%elem(ielem)%ncell))
          grid%elem(ielem)%cl2g = old_cl2g
          deallocate(old_cl2g)
          !write(*,*)"FINAL s ",lmpi_id,ielem,grid%elem(ielem)%ncell
       end if
    end if

  end subroutine distribute_mix


!========================== DISTRIBUTE_XYZ ===================================80
!
! Distribute xyz by bcast and collect elements based on pp_nhead and pp_ntail.
!
!=============================================================================80

  subroutine distribute_xyz(xs,xe,grid,d1s,d1e,x,y,z)

    use grid_types,  only : grid_type

    integer,                        intent(in)    :: xs,xe,d1s,d1e
    type(grid_type),                intent(inout) :: grid
!beginNeverComplex
    real(dp),dimension(d1s:d1e),    intent(in)    :: x,y,z
!endNeverComplex

    integer :: is,ie,ios,ioe,jos,joe
    real(dp), dimension(:), allocatable :: tx
    logical :: fill

    continue

    is = pp_nhead(lmpi_id)
    ie = pp_ntail(lmpi_id)

    fill = ((xs <= ie).and.(xe >= is))
    if (fill) then
       jos = max(xs,is)
       joe = min(xe,ie)
       ios = (jos-is)+1
       ioe = (joe-is)+1
       !write(*,*)'FILL ',lmpi_id,xs,xe,jos,joe,ios,ioe
    else
       ioe = 0; ios = 0; jos = 0 ! suppress compiler warnings
    end if

    allocate(tx(xs:xe))

    if (lmpi_master) tx = x
    call lmpi_bcast(tx)
    if (fill) grid%x(ios:ioe) = tx(jos:joe)

    if (lmpi_master) tx = y
    call lmpi_bcast(tx)
    if (fill) grid%y(ios:ioe) = tx(jos:joe)

    if (lmpi_master) tx = z
    call lmpi_bcast(tx)
    if (fill) grid%z(ios:ioe) = tx(jos:joe)

    deallocate(tx)

!   do i = 1,size(grid%x)
!      write(50000+lmpi_id,*) grid%l2g(i),grid%x(i)
!   end do

  end subroutine distribute_xyz

!====================== PUNS3D_READ_VGRID_MULTISEG_COORD =====================80
!
! Reads the coordinate data from VGRID version 4 unformatted multi-segmented
!
! unformatted .gridu file.  Recursively reads ninc values into x,y,z crossing
!
! segment boundaries as needed.
!
!=============================================================================80

  recursive subroutine puns3d_read_vgrid_mseg_coord(x,y,z,ninc,segs_remain,    &
                                                    left_on_seg)
!beginNeverComplex
  real(dp), dimension(1), intent(inout) :: x,y,z
!endNeverComplex
  integer, intent(in)                   :: ninc
  integer, intent(inout)                :: segs_remain,left_on_seg
  integer                               :: have_from_seg, num_from_seg
  logical                               :: new_seg
  integer                               :: i
  real(dp)                              :: rdummy

  continue

  ! Something to read
  if (ninc <= 0) return             ! Stop recursion

  if (segs_remain > 0 .and. left_on_seg == 0) then
     read(unit_grid) left_on_seg    ! Read segment header
     have_from_seg = 0
     segs_remain = segs_remain - 1
     new_seg = .true.
  else if (left_on_seg == 0) then
     if (lmpi_master) write(*,*)"Final VGRID segment exhausted!"
     new_seg = .false.
     call lmpi_die
  else
     backspace(unit_grid)           ! z
     backspace(unit_grid)           ! y
     backspace(unit_grid)           ! x
     backspace(unit_grid)           ! nsegments
     read(unit_grid) have_from_seg  ! Read segment header
     have_from_seg = have_from_seg - left_on_seg
     new_seg = .false.
  endif

  if (left_on_seg >= ninc) then     ! How much can I read
     num_from_seg = ninc
  else
     num_from_seg = left_on_seg
  endif

  if (new_seg) then                 ! New Segment
     read(unit_grid) (x(i),i=1,num_from_seg)
     read(unit_grid) (y(i),i=1,num_from_seg)
     read(unit_grid) (z(i),i=1,num_from_seg)
  else                              ! Partial Segment
     read(unit_grid) (rdummy,i=1,have_from_seg),(x(i),i=1,num_from_seg)
     read(unit_grid) (rdummy,i=1,have_from_seg),(y(i),i=1,num_from_seg)
     read(unit_grid) (rdummy,i=1,have_from_seg),(z(i),i=1,num_from_seg)
     if ( .false. ) write(*,*) rdummy  ! hush compiler warning
  endif

  left_on_seg = left_on_seg - num_from_seg
  i = have_from_seg + num_from_seg + 1

  call puns3d_read_vgrid_mseg_coord(x(i),y(i),z(i),ninc-num_from_seg,          &
                                    segs_remain,left_on_seg)

  return

  end subroutine puns3d_read_vgrid_mseg_coord


!====================== PUNS3D_READ_VGRID_MULTISEG_CONEX =====================80
!
! Reads the connectivity data from VGRID version 4 unformatted multi-segmented
!
! unformatted .gridu file.  Recursively reads ninc values into val crossing
!
! segment boundaries as needed.
!
!=============================================================================80

  recursive subroutine puns3d_read_vgrid_mseg_conex(t,siz,ninc,segs_remain,    &
                                                    left_on_seg)

  integer, intent(in)                      :: siz,ninc
  integer, dimension(siz,1), intent(inout) :: t
  integer, intent(inout)                   :: segs_remain,left_on_seg
  integer                                  :: have_from_seg, num_from_seg
  logical                                  :: new_seg
  integer                                  :: i,j
  integer                                  :: idummy

  continue

  ! Something to read
  if (ninc <= 0) return             ! Stop recursion

  if (segs_remain > 0 .and. left_on_seg == 0) then
     read(unit_grid) left_on_seg    ! Read segment header
     have_from_seg = 0
     segs_remain = segs_remain - 1
     new_seg = .true.
  else if (left_on_seg == 0) then
     if (lmpi_master) write(*,*)"Final VGRID segment exhausted!",ninc
     new_seg = .false.
     call lmpi_die
  else
     do i=1,siz
       backspace(unit_grid)         ! t(i,j)
     enddo
     backspace(unit_grid)           ! nsegments
     read(unit_grid) have_from_seg  ! Read segment header
     have_from_seg = have_from_seg - left_on_seg
     new_seg = .false.
  endif

  if (left_on_seg >= ninc) then     ! How much can I read
     num_from_seg = ninc
  else
     num_from_seg = left_on_seg
  endif

  ! FUN3D ordering wrt VGRID ordering
  ! Tet 1,2,3,4
  ! Pyr 1,2,3,4,5
  ! Pri 1,4,6,2,3,5
  ! Hex 1,5,6,2,3,7,8,4

  if (new_seg) then                 ! New Segment
     if (siz < 6) then
       do i=1,siz
          read(unit_grid) (t(i,j),j=1,num_from_seg)
       enddo
     else if (siz == 6) then
       read(unit_grid) (t(1,j),j=1,num_from_seg)
       read(unit_grid) (t(4,j),j=1,num_from_seg)
       read(unit_grid) (t(6,j),j=1,num_from_seg)
       read(unit_grid) (t(2,j),j=1,num_from_seg)
       read(unit_grid) (t(3,j),j=1,num_from_seg)
       read(unit_grid) (t(5,j),j=1,num_from_seg)
     else if (siz == 8) then
       read(unit_grid) (t(1,j),j=1,num_from_seg)
       read(unit_grid) (t(5,j),j=1,num_from_seg)
       read(unit_grid) (t(6,j),j=1,num_from_seg)
       read(unit_grid) (t(2,j),j=1,num_from_seg)
       read(unit_grid) (t(3,j),j=1,num_from_seg)
       read(unit_grid) (t(7,j),j=1,num_from_seg)
       read(unit_grid) (t(8,j),j=1,num_from_seg)
       read(unit_grid) (t(4,j),j=1,num_from_seg)
     endif
  else                              ! Partial Segment
     if (siz < 6) then
       do i=1,siz
          read(unit_grid) (idummy,j=1,have_from_seg),(t(i,j),j=1,num_from_seg)
       enddo
     else if (siz == 6) then
       read(unit_grid) (idummy,j=1,have_from_seg),(t(1,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(4,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(6,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(2,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(3,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(5,j),j=1,num_from_seg)
     else if (siz == 8) then
       read(unit_grid) (idummy,j=1,have_from_seg),(t(1,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(5,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(6,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(2,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(3,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(7,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(8,j),j=1,num_from_seg)
       read(unit_grid) (idummy,j=1,have_from_seg),(t(4,j),j=1,num_from_seg)
     endif
     if ( .false. ) write(*,*) idummy  ! hush compiler warning
  endif

  left_on_seg = left_on_seg - num_from_seg
  i = ninc - num_from_seg
  j = have_from_seg + num_from_seg + 1

  call puns3d_read_vgrid_mseg_conex(t(:,j),siz,i,segs_remain,left_on_seg)

  return

  end subroutine puns3d_read_vgrid_mseg_conex



!=============================== READVGRID_TAGS ==============================80
!
! Reads bc from VGRID Format Meshes -- into public module variables:
!       faceptr, facetag, tagmap, nbctags
!
!=============================================================================80

  subroutine puns3d_read_vgrid_bc( flow_dir, grid )

    use grid_types,         only : grid_type

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(in)  :: grid
    integer :: viscous_flag

    integer  :: i,j, iostat, idummy

    character (len=80) :: bcfilename

    logical, parameter :: verbose = .false.
    integer, parameter :: unit_bc = 1000

    continue

     if (lmpi_id == 0) then

        bcfilename = trim(flow_dir) // trim(grid%project) // '.bc'

        if (verbose) write (*,*) 'Opening ',trim(bcfilename)
        call se_open(unit_bc,file=bcfilename,                                  &
                status='old',iostat=iostat)
        if ( iostat /= 0 ) then
          write(*,*)'error opening ',trim(bcfilename),                         &
            ' in puns3d_io_c2n.f90:puns3d_read_vgrid_bc stopping...'
          call lmpi_conditional_stop(1)
        endif
        rewind (unit_bc)

    !   no. of boundary faces and face types

        read (unit_bc,*) ntface, idummy, nbctags, viscous_flag
        if (verbose) write(*,*) ' ntface=',ntface,' nbctags=',nbctags
        if (.false.) write(*,*) idummy ! avoid compiler warnings
        if (.false.) write(*,*) viscous_flag ! avoid compiler warnings
        read (unit_bc,*) ! text

     end if
     call lmpi_conditional_stop(0)

     call lmpi_bcast(ntface)
     call lmpi_bcast(nbctags)

     if (allocated(faceptr)) deallocate(faceptr)
     if (allocated(facetag)) deallocate(facetag)
     allocate(faceptr(ntface,4)); faceptr = 0
     allocate(facetag(ntface));   facetag = 0

    ! Surface triangles, surface tags

      if (lmpi_id == 0) then
         do i=1,ntface
            read(unit_bc,*)idummy,facetag(i),(faceptr(i,j),j=1,3)
         end do
      end if
      call lmpi_bcast(facetag)
      call lmpi_bcast(faceptr)

      if (lmpi_id  == 0) close(unit_bc)

  end subroutine puns3d_read_vgrid_bc

!=============================== READFAST ====================================80
!
! Reads the c2n information from FAST Format Meshes
!
!=============================================================================80

  subroutine puns3d_read_fast_c2n(flow_dir, grid, format)

    use element_defs,  only : type_tet, initialize_elem
    use grid_types,    only : grid_type

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(inout) :: grid
    character(*),    intent(in)    :: format

    integer :: iostat, ntet, local_unit_grid

    continue

    if (lmpi_id == 0) then

       local_unit_grid = unit_grid + lmpi_id

       gridfilename = trim(flow_dir) // trim(grid%project) // '.fgrid'
       write(*,*)'   ... opening ',trim(gridfilename)

       if (trim(format) == 'ascii') then
         call se_open(local_unit_grid,file=gridfilename,                       &
               status='old',iostat=iostat)
       else if (trim(format) == 'unformatted') then
         call se_open(local_unit_grid,file=gridfilename,form='unformatted',    &
               status='old',iostat=iostat)
       else
         write(*,*)'Unsupported format ',trim(format)
         call lmpi_conditional_stop(1)
       end if
       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(gridfilename),                        &
                   ' stopping...; iostat = ', iostat
         call lmpi_conditional_stop(1)
       endif
       rewind (unit=local_unit_grid)

       if (trim(format) == 'ascii') then
          read (local_unit_grid,*) grid%nnodesg,ntface,ntet
       else
          read (local_unit_grid)   grid%nnodesg,ntface,ntet
       end if

       write(*,'("    ... nnodesg: ",i0," ntet: ",i0," ntface: ",i0,/)')       &
         grid%nnodesg,ntet,ntface

    end if
    call lmpi_conditional_stop(0)

    call lmpi_bcast(grid%nnodesg)
    call lmpi_bcast(ntface)
    call lmpi_bcast(ntet)
    grid%ncellg = ntet

!   just one element type (tets)

    grid%nelem = 1

!   now set up a few items for the element derived type

    allocate(grid%elem(grid%nelem))

    call initialize_elem(grid%elem(1),type_tet)
    grid%elem(1)%ncell = 0
    call puns3d_read_fast_core_c2n(local_unit_grid, grid, ntface, format)

    pp_csize = grid%elem(1)%ncell
    grid%elem(1)%ncell  = pp_csize(lmpi_id)
    grid%elem(1)%ncellg = grid%ncellg

    if (lmpi_id == 0) close(local_unit_grid)

  end subroutine puns3d_read_fast_c2n

!============================ PUNS3D_READ_FAST_CORE_C2N ======================80
!
! Does the xyz and c2n through a buffer.
!
!=============================================================================80

  subroutine puns3d_read_fast_core_c2n(unit_grid, grid, ntface, format)

    use grid_types, only : grid_type

    integer,         intent(in)    :: unit_grid
    type(grid_type), intent(inout) :: grid
    integer,         intent(in)    :: ntface
    character(*),    intent(in)    :: format

    integer :: i, j, idummy, bs, be, xs, xe, isegment
    integer :: nsegment, nnodesg, ntet, binc, xinc

    real(dp) :: rdummy

    integer,  dimension(:,:), allocatable :: tc2n
!beginNeverComplex
    real(dp), dimension(:),   allocatable :: x,y,z
!endNeverComplex

    integer, parameter :: nslice = buffer_limit/16 ! 4*4 Bytes

    continue

    nnodesg = grid%nnodesg
    ntet    = grid%ncellg

    call distribute_fast_c2n(grid%nnodesg)

    i = max(nint((ntet/lmpi_nproc) * 2.0), 10000)
    !write(*,*)"ALLOC c2n ",lmpi_id,i,ntet,ntet/lmpi_nproc

    allocate(grid%elem(1)%c2n(4,i)); grid%elem(1)%c2n  = 0
    allocate(grid%elem(1)%cl2g(i));  grid%elem(1)%cl2g = 0

    !write(*,*)"pp_ns ",lmpi_id,pp_nsize
    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%nnodes01   = grid%nnodes0
    allocate(grid%x(grid%nnodes0));   grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0));   grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0));   grid%z = 0.0_dp
    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

    nsegment = ntet/nslice
    if (ntet > nsegment*nslice) nsegment = nsegment + 1
    !write(*,*)'NS',lmpi_id,nsegment,ntet,nslice,ntet/nslice,nsegment*nslice

    binc = grid%ncellg /nsegment
    if (grid%ncellg > binc*nsegment) binc = binc + 1
    xinc = grid%nnodesg/nsegment
    if (grid%nnodesg > xinc*nsegment) xinc = xinc + 1
    !write(*,*)"binc,xinc ",lmpi_id,binc,xinc

   ! For non-master, allocate dummy arguments
     if (.not.lmpi_master) then
        allocate(tc2n(4,1)); tc2n = 0
        allocate(x(1)); x = 0.0_dp
        allocate(y(1)); y = 0.0_dp
        allocate(z(1)); z = 0.0_dp
     end if

     bs = 1
     xs = 1
     do isegment = 1,nsegment

        be = (bs + binc) - 1
        xe = (xs + xinc) - 1
        if (be > ntet)    be = ntet
        if (xe > nnodesg) xe = nnodesg
        !write(*,*)"isegment,bs,be = ",isegment,bs,be,xs,xe

        if (lmpi_master) then

         allocate(tc2n(4,bs:be)); tc2n = 0
         allocate(x(xs:xe)); x = 0.0_dp
         allocate(y(xs:xe)); y = 0.0_dp
         allocate(z(xs:xe)); z = 0.0_dp

        rewind(unit_grid)

         if (isegment /= 1) then
           if (trim(format) == 'ascii') then
               read (unit_grid,*) idummy, idummy, idummy
               read (unit_grid,*)                                              &
                    (rdummy,i=1,xs-1),(x(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),  &
                    (rdummy,i=1,xs-1),(y(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),  &
                    (rdummy,i=1,xs-1),(z(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),  &
                    ((idummy,   j=1,3),i=1,ntface),                            &
                    ( idummy,   i=1,ntface),                                   &
                    ((idummy,   j=1,4),i=1,bs-1), ((tc2n(j,i),j=1,4),i=bs,be)
           else
               read (unit_grid) idummy, idummy, idummy
               read (unit_grid)                                                &
                    (rdummy,i=1,xs-1),(x(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),  &
                    (rdummy,i=1,xs-1),(y(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),  &
                    (rdummy,i=1,xs-1),(z(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),  &
                    ((idummy,   j=1,3),i=1,ntface),                            &
                    ( idummy,   i=1,ntface),                                   &
                    ((idummy,   j=1,4),i=1,bs-1), ((tc2n(j,i),j=1,4),i=bs,be)
           end if
         else
           if (trim(format) == 'ascii') then
               read (unit_grid,*) idummy, idummy, idummy
               read (unit_grid,*)                                              &
                    (x(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),                    &
                    (y(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),                    &
                    (z(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),                    &
                    ((idummy,   j=1,3),i=1,ntface),                            &
                    ( idummy,   i=1,ntface),                                   &
                    ((tc2n(j,i),j=1,4),i=bs,be)
           else
               read (unit_grid) idummy, idummy, idummy
               read (unit_grid)                                                &
                    (x(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),                    &
                    (y(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),                    &
                    (z(i),i=xs,xe),(rdummy,i=xe+1,nnodesg),                    &
                    ((idummy,   j=1,3),i=1,ntface),                            &
                    ( idummy,   i=1,ntface),                                   &
                    ((tc2n(j,i),j=1,4),i=bs,be)
          end if
         end if
         call distribute_tet(isegment,nsegment,bs,be,grid,4,bs,be,tc2n)
         call distribute_xyz(xs,xe,grid,xs,xe,x,y,z)
        else
         call distribute_tet(isegment,nsegment,bs,be,grid,4,1,1,tc2n)
         call distribute_xyz(xs,xe,grid,1,1,x,y,z)
        end if
        if (.false.) write(*,*) idummy, rdummy ! compiler warnings

        if (lmpi_master) then
           deallocate(tc2n)
           deallocate(x,y,z)
        end if
        bs = be+1
        xs = xe+1

      end do ! isegment

    ! Deallocate dummy variables
    if (.not.lmpi_master) then
       deallocate(x,y,z)
       deallocate(tc2n)
    end if

   !do i = 1,grid%nnodes0
   !   write(18000+lmpi_id,'(1x,i0,":",1x,3(F20.12,1x))')                      &
   !         grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
   !end do
   !do i = 1,grid%elem(1)%ncell
   !   write(19000+lmpi_id,'(1x,i0,":",1x,4(i0,1x))')                          &
   !         grid%elem(1)%cl2g(i),grid%elem(1)%c2n(:,i)
   !end do

  end subroutine puns3d_read_fast_core_c2n

!================================ PUNS3D_READ_FUN2D_C2N ======================80
!
! Reads the xyz, c2n, facemap, facetag for FUN2D
!
!=============================================================================80

  subroutine puns3d_read_fun2d_c2n(flow_dir,grid)

    use bc_names,          only : symmetry_y
    use element_defs,      only : type_prz, initialize_elem
    use file_utils,        only : file_exists
    use grid_types,        only : grid_type
    use info_depr,         only : twod
    use system_extensions, only : se_open
    use twod_util,         only : nplanes, yspan

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(inout) :: grid

! local

    integer :: i, j, l, n, iostat, ncell2d, ierr
    integer :: xs, xe, ys, ye, is, ie, ios, ioe, jos, joe, nloop
    integer :: idummy, sym_tagmap1, sym_tagmap2
    integer :: nnodes2d, nedge2d
    integer :: nsedge2d, nvedge2d, nfedge2d
    integer :: segcount, facecount, iface
    integer :: icell, symface, nfaces
    integer :: iplane
    integer :: c2n(6)

    character(len=120) :: string

    integer,  dimension(:),   allocatable :: temp1
    integer,  dimension(:,:), allocatable :: eptr2d, temp2
    real(dp), dimension(:),   allocatable :: x2d, z2d

    type bc_seg_type
      integer                        :: segbc2d
      integer                        :: nedges2d
      integer                        :: seg_tagmap
      integer, dimension(:), pointer :: iedge2d
    end type bc_seg_type

    type(bc_seg_type), dimension(:), allocatable :: bc_seg

    integer, parameter :: bc_seg_map (0:8) =                                   &
      (/3000, 4000, 5000, -1, 4010, 4010, 5005, 7011, 7012 /)

      ! 0 = 3000  tangency
      ! 1 = 4000  viscous (strong)
      ! 2 = 5000  farfield riemann
      ! 3 =       unknown
      ! 4 = 4010  specified mass flux (viscous)
      ! 5 = 4010  specified mass flux (inviscid)
      ! 6 = 5005  farfield extrapolation
      ! 7 = 7011  subsonic inflow pt
      ! 8 = 7012  back pressure

  continue

    twod = .true.

    if (nplanes > 2) twod = .false.

    ierr = 0

    if (lmpi_master) then
       gridfilename = trim(flow_dir) // trim(grid%project) // '.faces'
       write (*,*) 'Reading FUN2D grid ', &
         trim(gridfilename)
       call se_open(unit_grid,file=gridfilename,                               &
               status='old',iostat=iostat)
       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(gridfilename),' stopping...'
         ierr = 1
       end if
    end if
    call lmpi_conditional_stop(ierr)

    if (lmpi_master) then

!      allow an extra edge types for additional bcs in aflr2 fun2d files
       read(unit_grid,*) idummy

       if (idummy < 0) then

!         new fun2d file with extra bcs

          read(unit_grid,*) nnodes2d, nedge2d, ncell2d

          segcount = -idummy
          allocate(bc_seg(segcount))
          do i=1,segcount
             read(unit_grid,*) n,bc_seg(i)%segbc2d,bc_seg(i)%nedges2d
          end do

   !     translate 2d bc types to fun3d types

          do i=1,segcount
             j = bc_seg(i)%segbc2d
             if ((j >= 0).and.(j <= 8).and.(j /= 3)) then
                bc_seg(i)%segbc2d = bc_seg_map(j)
             else
                write(*,*) 'Warning: Unknown FUN2D BC Type',j
             end if
          end do

       else ! idummy >= 0  old fun2d

          rewind (unit=unit_grid)
          segcount = 3
          allocate(bc_seg(segcount))

          read(unit_grid,*) nnodes2d,nedge2d,ncell2d,nsedge2d,nvedge2d,nfedge2d

          bc_seg(1)%segbc2d  = 3000
          bc_seg(1)%nedges2d = nsedge2d

          bc_seg(2)%segbc2d  = 4000
          bc_seg(2)%nedges2d = nvedge2d

          bc_seg(3)%segbc2d  = 5000
          bc_seg(3)%nedges2d = nfedge2d

       end if

    end if

    call lmpi_bcast(idummy)
    call lmpi_bcast(segcount)
    call lmpi_bcast(nnodes2d)
    call lmpi_bcast(nedge2d)
    call lmpi_bcast(ncell2d)
    if (idummy >= 0) then
       call lmpi_bcast(nsedge2d)
       call lmpi_bcast(nvedge2d)
       call lmpi_bcast(nfedge2d)
    end if

! Distribute nodes over processors

    grid%nnodesg = nplanes*nnodes2d

    call distribute_fast_c2n(grid%nnodesg)

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%nnodes01   = grid%nnodes0

    nfaces = 2*ncell2d

!   allocate space for some 2d arrays

    if (.not.lmpi_master) allocate(bc_seg(segcount))
    do i=1,segcount
      call lmpi_bcast(bc_seg(i)%segbc2d)
      call lmpi_bcast(bc_seg(i)%nedges2d)
      allocate(bc_seg(i)%iedge2d(max(1,bc_seg(i)%nedges2d)))
      bc_seg(i)%iedge2d = 0
    end do

!   read in the remainder of the 2d grid file, and count the bc tags we need
!   there are only 3 possible in the fun2d grid: inviscid, viscous, farfield

    if ((lmpi_master).and.(nedge2d*4*4 > buffer_limit))                        &
       write(*,*)"Array nedge2d is close to buffer_limit ",                    &
                 nedge2d*4*4,buffer_limit

    allocate(eptr2d(nedge2d,4)); eptr2d = 0
    if (lmpi_master) then
        read(unit_grid,*) ((eptr2d(n,l),l=1,4),n=1,nedge2d)
    end if
    call lmpi_bcast(eptr2d)

    nbctags = 0
    do i=1,segcount
       if (bc_seg(i)%nedges2d > 0) then
          nbctags = nbctags + 1
          if (lmpi_master) then
             read(unit_grid,*)
             read(unit_grid,*)(bc_seg(i)%iedge2d(n),n=1,bc_seg(i)%nedges2d)
          end if
          call lmpi_bcast(bc_seg(i)%iedge2d)
         !write(100+lmpi_id,*) bc_seg(i)%iedge2d
       end if
    end do

!   create a 3D (x,y,z) by copying the 2d set a unit depth

    allocate(grid%x(grid%nnodes0)); grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0)); grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0)); grid%z = 0.0_dp
    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

!   read in the 2D x,z pairs

    if (lmpi_master) read(unit_grid,*)

    is = pp_nhead(lmpi_id)
    ie = pp_ntail(lmpi_id)
    if (nnodes2d <= buffer_limit) then
       allocate(x2d(nnodes2d)); x2d = 0.0_dp
       allocate(z2d(nnodes2d)); z2d = 0.0_dp
       if (lmpi_master) then
          do i = 1,nnodes2d
             read(unit_grid,*) x2d(i), z2d(i)
          end do
       end if
       call lmpi_bcast(x2d)
       call lmpi_bcast(z2d)

       xs = 1
       do iplane = 1,nplanes
          xe = (xs + nnodes2d)-1
          if (xe > grid%nnodesg) xe = grid%nnodesg
          if ((xs <= ie).and.(xe >= is)) then
             jos = max(xs,is)
             joe = min(xe,ie)
             ios = (jos-is)+1
             ioe = (joe-is)+1
             j = nnodes2d * (iplane-1)
             grid%x(ios:ioe) = x2d(jos-j:joe-j)
             grid%z(ios:ioe) = z2d(jos-j:joe-j)
             grid%y(ios:ioe) = yspan*(nplanes-iplane)/(nplanes-1)
          end if
          xs = xs + nnodes2d
       end do

       deallocate(x2d,z2d)

    else
       nloop = nnodes2d/buffer_limit
       if (nnodes2d > nloop*buffer_limit) nloop = nloop + 1
      !if (lmpi_master) write(*,*)"SPLIT read ",nloop
       do xs = 1,nnodes2d,buffer_limit
          xe = (xs + buffer_limit)-1
          if (xe > nnodes2d) xe = nnodes2d
         !if (lmpi_master) write(*,*)'XS,XE ',xs,xe,nnodes2d
          allocate(x2d(xs:xe)); x2d = 0.0_dp
          allocate(z2d(xs:xe)); z2d = 0.0_dp
          if (lmpi_master) then
             do j = xs,xe
                read(unit_grid,*)x2d(j),z2d(j)
             end do
          end if
          call lmpi_bcast(x2d)
          call lmpi_bcast(z2d)

          ys = xs
          j  = 0
          do iplane = 1,nplanes
             ye = (ys + buffer_limit)-1
             if (ye > iplane*nnodes2d) ye = iplane*nnodes2d
             !write(*,'("xyij ",11(i0,1x))') lmpi_id,xs,xe,ys,ye,is,ie
             if ((ys <= ie).and.(ye >= is)) then
                jos = max(ys,is)
                joe = min(ye,ie)
                ios = (jos-is)+1
                ioe = (joe-is)+1
                !write(*,'("xyik ",14(i0,1x))')                                &
                ! lmpi_id,xs,xe,ys,ye,is,ie,jos,joe,ios,ioe,iplane,jos-j,joe-j
                grid%x(ios:ioe) = x2d(jos-j:joe-j)
                grid%z(ios:ioe) = z2d(jos-j:joe-j)
                grid%y(ios:ioe) = yspan*(nplanes-iplane)/(nplanes-1)
             end if
             j  = j  + nnodes2d
             ys = ys + nnodes2d
          end do
          deallocate(x2d,z2d)
       end do
    end if

!   do i = 1,pp_nsize(lmpi_id)
!      write(15000+lmpi_id,'(1x,i0,1x,3(F20.12,1x))')                          &
!        grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
!   end do

    if (allocated(faceptr)) deallocate(faceptr)
    if (allocated(facetag)) deallocate(facetag)
    allocate(faceptr(nfaces,4)); faceptr = 0
    allocate(facetag(nfaces));   facetag = 0

!   add 2 tags for the 2 symmetry planes to be imposed in the 2D direction

    nbctags = nbctags + 2

!   allocate the tagmap array and set its values

    if (allocated(tagmap))     deallocate(tagmap)
    if (allocated(tagmap_org)) deallocate(tagmap_org)
    if (allocated(family_map)) deallocate(family_map)
    if (allocated(family_org)) deallocate(family_org)

    allocate(tagmap(nbctags,2));     tagmap     = 0
    allocate(tagmap_org(nbctags,2)); tagmap_org = 0
    allocate(family_map(nbctags));   family_map = ''
    allocate(family_org(nbctags));   family_org = ''

!   set up the tagmap array

    nbctags = 0

    do i=1,segcount
      if (bc_seg(i)%nedges2d > 0) then
        nbctags = nbctags + 1
        tagmap(nbctags,1) = i
        tagmap(nbctags,2) = bc_seg(i)%segbc2d
        tagmap_org(nbctags,1) = tagmap(nbctags,1)
        tagmap_org(nbctags,2) = tagmap(nbctags,2)
        bc_seg(i)%seg_tagmap = tagmap(nbctags,1)
      end if
    end do

!   1st symmetry plane in 2d direction
    nbctags = nbctags + 1
    sym_tagmap1 = segcount + 1
    tagmap(nbctags,1) = sym_tagmap1
    tagmap(nbctags,2) = symmetry_y
    family_org(nbctags) = 'symmetry_y'
    family_map(nbctags) = 'symmetry_y'

!   2nd symmetry plane in 2d direction
    nbctags = nbctags + 1
    sym_tagmap2 = segcount + 2
    tagmap(nbctags,1) = sym_tagmap2
    tagmap(nbctags,2) = symmetry_y
    family_org(nbctags) = 'symmetry_y'
    family_map(nbctags) = 'symmetry_y'

!----------------------------
!   check to see whether there is an existing mapbc file; if so use the bc's
!   from that file; if it does not exist, write out the tagmap info that that
!   the user has the option of changing it if desired for the next run

    j = 0
    if (lmpi_master) then

       mapbcfilename = trim(flow_dir) // trim(grid%project) // '.mapbc'

       if ( file_exists(mapbcfilename) ) then

         if (verbose) write (*,*) 'Opening an existing ',mapbcfilename
         call se_open(unit_mapbc,file=mapbcfilename,                           &
               status='old',iostat=iostat)
         if ( iostat /= 0 ) then
           write(*,*)'error opening ',trim(mapbcfilename),' stopping...'
           call lmpi_conditional_stop(1)
         end if
! DANA changes FUN2D mapbc definition
         read(unit_mapbc,'(i10)') nbctags_org
         do i = 1,nbctags_org
           read (unit_mapbc,*) tagmap(i,1), tagmap(i,2)
           tagmap_org(i,1) = tagmap(i,1)
           tagmap_org(i,2) = tagmap(i,2)
           call backwards_compatible_bc(tagmap(i,2))
           backspace(unit_mapbc)
           read(unit_mapbc,'(a120)') string
           call extract_family(3,string,family_org(i))
           family_map(i) = family_org(i)
         end do
         nbctags_org = nbctags ! account for symmetry planes
         j = 1

       else

         call se_open(unit_mapbc,file=mapbcfilename,                           &
               status='new',iostat=iostat)
         if ( iostat /= 0 ) then
           write(*,*)'error opening ',trim(mapbcfilename),' stopping...'
           call lmpi_conditional_stop(1)
         end if
! DANA changes FUN2D mapbc definition
         write(unit_mapbc,*) nbctags
         do i = 1,nbctags
           write(unit_mapbc,'(2(i0,1x),a)')                                    &
             tagmap(i,1),tagmap(i,2),trim(family_map(i))
         end do

       end if
    end if
    call lmpi_conditional_stop(0)
    call lmpi_bcast(j)

    ! if bcast tagmap read above, then bcast

    if (j == 1) then
       call lmpi_bcast(tagmap)
       call lmpi_bcast(tagmap_org)
       do i = 1,nbctags
          call lmpi_bcast(family_map(i))
          call lmpi_bcast(family_org(i))
       end do
    end if

!   construct the face to node pointers on the symmetry plane boundaries

    call get_symfaceptr(ncell2d, nnodes2d, nedge2d, eptr2d, nfaces, faceptr,   &
                        symface, facetag, sym_tagmap1, sym_tagmap2, nplanes)

!   resize facetag and faceptr based on symface (number of symmetry faces set
!   in get_symfaceptr), plus, add inviscid, viscous, farfield, etc faces
!   note that the non-symmetry faces are equal to the number of equivalent
!   *edges* in the 2d grid

    ntface = 2*symface
    nqface = 0

    nfaces = ntface + nqface

    do i=1,segcount
      nqface = nqface + (nplanes-1)*bc_seg(i)%nedges2d
    end do

    nfaces = nfaces + nqface

    j = size(faceptr,1)
    allocate(temp1(j));   temp1 = facetag; deallocate(facetag)
    allocate(temp2(j,4)); temp2 = faceptr; deallocate(faceptr)
    allocate(facetag(nfaces));   facetag = 0
    allocate(faceptr(nfaces,4)); faceptr = 0
    do i = 1,min(nfaces,j)
       faceptr(i,:) = temp2(i,:)
       facetag(i)   = temp1(i)
    end do
    deallocate(temp1,temp2)

!   construct the face to node pointers on faces that align with the
!   2D direction; have already assigned faceptr data for the first 2*symface
!   faces corresponding to the symmetry plane, so the facecount starts there

    facecount = 2*symface

    do iplane=1,nplanes-1
      do i=1,segcount
        call get_faceptr(nnodes2d, nedge2d, eptr2d, nfaces, faceptr,           &
                           facetag, bc_seg(i)%nedges2d, bc_seg(i)%iedge2d,     &
                           facecount, bc_seg(i)%seg_tagmap, iplane)
      end do
    end do

!   deallocate bc_seg

    do i=1,segcount
      deallocate(bc_seg(i)%iedge2d)
      nullify(bc_seg(i)%iedge2d)
    end do
    deallocate(bc_seg)

!---------------
!   just one element type (prisms)

    grid%nelem = 1

    allocate(grid%elem(grid%nelem))

!   now set up a few items for the element derived type

    call initialize_elem(grid%elem(1), type_prz)

    if(verbose)write(*,*)' nfaces=',nfaces,' ntface=',ntface,' nqface=',nqface

!   finally, construct the cell to node pointer

    icell = 0
    do iplane=1,nplanes-1
      loop_iface: do iface = 1,symface
        c2n(1) = faceptr(iface,1) + nnodes2d*(iplane-1)
        c2n(2) = faceptr(iface,1) + nnodes2d*iplane
        c2n(3) = faceptr(iface,2) + nnodes2d*iplane
        c2n(4) = faceptr(iface,2) + nnodes2d*(iplane-1)
        c2n(5) = faceptr(iface,3) + nnodes2d*iplane
        c2n(6) = faceptr(iface,3) + nnodes2d*(iplane-1)
        do i = 1,6
           if ((c2n(i) >= is).and.(c2n(i) <= ie)) then
              icell = icell + 1
              cycle loop_iface
           end if
        end do
      end do loop_iface
    end do

    grid%elem(1)%ncell  = icell
    call lmpi_allgather(icell,pp_csize)

    allocate(grid%elem(1)%c2n(6,icell)); grid%elem(1)%c2n = 0
    allocate(grid%elem(1)%cl2g(icell));  grid%elem(1)%cl2g = 0

    icell = 0
    j = 0
    do iplane=1,nplanes-1
      loop_iface2: do iface = 1,symface
        c2n(1) = faceptr(iface,1) + nnodes2d*(iplane-1)
        c2n(2) = faceptr(iface,1) + nnodes2d*iplane
        c2n(3) = faceptr(iface,2) + nnodes2d*iplane
        c2n(4) = faceptr(iface,2) + nnodes2d*(iplane-1)
        c2n(5) = faceptr(iface,3) + nnodes2d*iplane
        c2n(6) = faceptr(iface,3) + nnodes2d*(iplane-1)
        j = j + 1
        do i = 1,6
           if ((c2n(i) >= is).and.(c2n(i) <= ie)) then
              icell = icell + 1
              grid%elem(1)%c2n(1:6,icell) = c2n(1:6)
              grid%elem(1)%cl2g(icell)    = j
              cycle loop_iface2
           end if
        end do
      end do loop_iface2
    end do

    grid%elem(1)%ncellg = j
    grid%ncellg         = j

  end subroutine puns3d_read_fun2d_c2n

!=============================== PUNS3D_SEND_MIRROR_XYZ ======================80
!
! Exchange XYZs after mirroring grid.
! XYZs are already in memory, and l2g already computed. Each processor bcasts
! the xyz's
!
!=============================================================================80

  subroutine puns3d_send_xyz_slice(nold_l2g,old_l2g,grid)

    use grid_types,         only : grid_type
    use allocations,        only : my_realloc_ptr

    integer,                      intent(in)    :: nold_l2g
    integer, dimension(nold_l2g), intent(in)    :: old_l2g
    type(grid_type),              intent(inout) :: grid

    integer  :: i,j, nhead,ntail,nsize, ipe,inode

    integer,  dimension(:), allocatable :: temp_l2g

    real(dp), dimension(:), allocatable :: savex,savey,savez
    real(dp), dimension(:), allocatable :: tempx,tempy,tempz

    continue

    ! Save xyz
    nsize = size(grid%x)
    allocate(savex(nsize)); savex = grid%x
    allocate(savey(nsize)); savey = grid%y
    allocate(savez(nsize)); savez = grid%z

    if (grid%nnodes01  /= size(grid%x)) then
       call my_realloc_ptr(grid%x, grid%nnodes01)
       call my_realloc_ptr(grid%y, grid%nnodes01)
       call my_realloc_ptr(grid%z, grid%nnodes01)
    end if

    nhead = pp_nhead(lmpi_id)
    ntail = pp_ntail(lmpi_id)
    do ipe = 0,lmpi_nproc-1

       if (lmpi_id == ipe) nsize = nold_l2g
       call lmpi_bcast(nsize,ipe)

       allocate(temp_l2g(nsize)); temp_l2g = 0
       if (lmpi_id == ipe) temp_l2g = old_l2g
       call lmpi_bcast(temp_l2g,ipe)

       allocate(tempx(nsize)); tempx = 0.0_dp
       allocate(tempy(nsize)); tempy = 0.0_dp
       allocate(tempz(nsize)); tempz = 0.0_dp

       if (lmpi_id == ipe) then
          tempx = savex
          tempy = savey
          tempz = savez
       end if

       call lmpi_bcast(tempx,ipe)
       call lmpi_bcast(tempy,ipe)
       call lmpi_bcast(tempz,ipe)

       do i = 1,nsize
          inode = temp_l2g(i)
          if ((inode >= nhead).and.(inode <= ntail)) then
             j = (inode-nhead)+1
             grid%x(j) = tempx(i)
             grid%y(j) = tempy(i)
             grid%z(j) = tempz(i)
          end if
       end do

       deallocate(temp_l2g)
       deallocate(tempx)
       deallocate(tempy)
       deallocate(tempz)

    end do ! ipe

    deallocate(savex,savey,savez)

 !do i = 1,grid%nnodes
 !  write(24000+lmpi_id,'(i0,3(F20.12,1x))')                                   &
 !        grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
 !end do
 !write(*,*) '24K'
 !call lmpi_die

  end subroutine puns3d_send_xyz_slice

!=============================== PUNS3D_SEND_MIRROR_C2N ======================80
!
! Exchange C2Ns after mirroring grid.
!
! C2ns are already in memory, and l2g already computed. Each processor bcasts
! the C2Ns.
!
! When the grid is mirrored, each PE has a set of original C2N and xyz and
! a set of mirrored C2N and xyz.  The original numbering is kept, and the
! mirrored numbering (both nodes and cells) starts with PE0.
!
!    Original set: pp_msize, pp_mhead, pp_mtail
!    Mirrored set: pp_nsize, pp_nhead, pp_ntail
!
! These are consistent with current l2g.
!
! Note: iloop = 1,2 reflects the two sets of data; this implicitedly preserves
!       the node and cell numbers.
!
!=============================================================================80

  subroutine puns3d_send_c2n_slice(grid)

    use grid_types,   only : grid_type
    use sort,         only : heap_sort

    type(grid_type), intent(inout) :: grid

    integer :: inode, ncellg, current_size
    integer :: i,j,k,m,ielem
    integer :: npc, s2, my_s2, max_s2, ipe, icell, ict
    integer :: n_head1,n_tail1,n_head2,n_tail2

    integer, dimension(:),   allocatable :: temp1, tempca, tempda
    integer, dimension(:),   allocatable :: tcl2g, t1_ind
    integer, dimension(:,:), allocatable :: temp2a, temp2b, temp2c, tc2n

    logical :: l1, l2

    logical, dimension(:), allocatable :: tagc

    logical :: verboset = .true.

   continue

   if (lmpi_id == 0) write(*,*) '    ... Mirroring send_c2n slice ...'
   !nnodes0 = grid%nnodes0

   if (verboset.and.lmpi_master) call se_wall_time('    ... Begin c2n slice ')

   n_head1 = grid%l2g(1)
   n_tail1 = grid%l2g(pp_msize(lmpi_id))
   n_head2 = grid%l2g(pp_msize(lmpi_id)+1)
   n_tail2 = grid%l2g(size(grid%l2g))

! Account for the case when some processors have NO mirrored nodes
! beyond their initial set. In this case, set the 2nd range to be
! equal to the 1st range.

   if (n_tail2 == n_tail1) n_head2 = n_tail2

   do ielem = 1,grid%nelem

    npc    = grid%elem(ielem)%node_per_cell
    my_s2  = grid%elem(ielem)%ncell
    ict    = 0
    if (my_s2 > 0) then
       allocate(tagc(my_s2)); tagc = .false.
       do i = 1,my_s2
          k = grid%elem(ielem)%c2n(1,i)
          l1 = ((k >= n_head1).and.(k <= n_tail1))
          l2 = ((k >= n_head2).and.(k <= n_tail2))
          if (l1.or.l2) then
             ict = ict + 1
             tagc(i) = .true.
          end if
       end do
    end if

    call lmpi_reduce(ict,j)
    if ((lmpi_master).and.(j /= grid%elem(ielem)%ncellg)) then
       write(*,*)"Exchange count ",j,grid%elem(ielem)%ncellg
    end if

    if (my_s2 > 0) then
       if (allocated(temp2a)) deallocate(temp2a)
       if (allocated(tempca)) deallocate(tempca)
       allocate(temp2a(npc,ict))
       allocate(tempca(ict))
       j = 0
       do i = 1,my_s2
          if (tagc(i)) then
             j = j + 1
             temp2a(:,j) = grid%elem(ielem)%c2n(:,i)
             tempca(j) = grid%elem(ielem)%cl2g(i)
          end if
       end do
       deallocate(tagc)
       my_s2 = ict
       deallocate(grid%elem(ielem)%c2n)
       nullify(grid%elem(ielem)%c2n)
       deallocate(grid%elem(ielem)%cl2g)
       nullify(grid%elem(ielem)%cl2g)
    end if
    call lmpi_reduce(my_s2,ncellg)
    call lmpi_bcast(ncellg)

!-------------

    current_size = nint((ncellg / lmpi_nproc) * 2.0)
    if (current_size < 10000) current_size = min(10000,ncellg)

    allocate(tc2n(npc,current_size));  tc2n = 0
    allocate(tcl2g(current_size));     tcl2g = 0

    call lmpi_max(my_s2,max_s2)
    call lmpi_bcast(max_s2)
    !if (lmpi_master) write(*,*)"max_s2 ",max_s2

    allocate(temp2b(npc,max_s2)); temp2b = 0
    allocate(tempda(max_s2));     tempda = 0
    k = 0
    do ipe = 0,lmpi_nproc-1

       if (verboset.and.lmpi_master) then
          if (mod(ipe,250)==0) write(*,*)'    ... loop2 ',ipe
       end if

       if (lmpi_id == ipe) s2 = my_s2
       call lmpi_bcast(s2,ipe)

       if (s2 > 0) then
          if (ipe == lmpi_id) then
             temp2b = 0
             temp2b(1:npc,1:s2) = temp2a(1:npc,1:s2)
             tempda = 0
             tempda(1:s2) = tempca(1:s2)
             deallocate(temp2a)
             deallocate(tempca)
          end if
          call lmpi_bcast(tempda,ipe)
          call lmpi_bcast(temp2b,ipe)

          do icell = 1,s2
             do i = 1,npc
                inode = temp2b(i,icell)
                if ((inode >= pp_nhead(lmpi_id)).and.                          &
                    (inode <= pp_ntail(lmpi_id))) then

                   k = k + 1

                   if (k > current_size) then
                      !write(*,*)'Reallocate ',lmpi_id,current_size
                      m = current_size

                    ! c2n
                      allocate(temp2c(npc,m)); temp2c = tc2n
                      deallocate(tc2n)
                      current_size = current_size + current_size/2
                      allocate(tc2n(npc,current_size)); tc2n = 0
                      tc2n(1:npc,1:m) = temp2c(1:npc,1:m)
                      deallocate(temp2c)

                    ! cl2g
                      allocate(temp1(m)); temp1 = tcl2g
                      deallocate(tcl2g)
                      allocate(tcl2g(current_size)); tcl2g = 0
                      tcl2g(1:m) = temp1(1:m)
                      deallocate(temp1)

                   end if

                   tc2n(1:npc,k) = temp2b(1:npc,icell)
                   tcl2g(k)      = tempda(icell)

                   exit
                end if
             end do ! npc
         end do     ! icell
       end if

    end do ! do ipe
    deallocate(temp2b)
    deallocate(tempda)

    grid%elem(ielem)%ncell  = k

    allocate(grid%elem(ielem)%cl2g(k))

    allocate(t1_ind(grid%elem(ielem)%ncell))
    call heap_sort(grid%elem(ielem)%ncell,tcl2g,t1_ind)
    grid%elem(ielem)%cl2g = tcl2g(t1_ind)
    deallocate(tcl2g)

    allocate(grid%elem(ielem)%c2n(npc,k))
    do i = 1,k
       grid%elem(ielem)%c2n(1:npc,i) = tc2n(1:npc,t1_ind(i))
    end do
    deallocate(tc2n)
    deallocate(t1_ind)

   !do i = 1,grid%elem(ielem)%ncell
   !   write(26000+lmpi_id,'(1x,i0,": ",8(i0,1x))')                            &
   !     grid%elem(ielem)%cl2g(i),grid%elem(ielem)%c2n(:,i)
   !end do

  end do ! do ielem

  if (verboset.and.lmpi_master) call se_wall_time('    ... End c2n slice ')

  end subroutine puns3d_send_c2n_slice

!=============================== puns3d_read_fast_bc =========================80
!
! Reads faceptr, facetag from FAST Format Meshes, into public module variables:
!       faceptr, facetag
!
!=============================================================================80

  subroutine puns3d_read_fast_bc(flow_dir,grid,format)

    use grid_types,         only : grid_type

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(in) :: grid
    character(*),    intent(in) :: format

    integer  :: i,j, iostat, nnodesg,ntface_local,ntet
    real(dp) :: rdummy3(3)

    continue

    if (lmpi_master) then
       gridfilename = trim(flow_dir) // trim(grid%project) // '.fgrid'

       if (verbose2) write (*,*) 'Opening ',trim(gridfilename)

       if (trim(format) == 'ascii') then
          call se_open(unit_grid+lmpi_id,file=gridfilename,                    &
               status='old',iostat=iostat)
       else if (trim(format) == 'unformatted') then
          call se_open(unit_grid+lmpi_id,file=gridfilename,form='unformatted', &
               status='old',iostat=iostat)
       else
         write(*,*)'Unsupported format ',trim(format)
         call lmpi_conditional_stop(1)
       end if
       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(gridfilename),' stopping...'
         call lmpi_conditional_stop(1)
       endif

! faceptr and facetag

       rewind (unit=unit_grid+lmpi_id)
       if (trim(format) == 'ascii') then
          read (unit_grid+lmpi_id,*) nnodesg,ntface_local,ntet
       else
          read (unit_grid+lmpi_id)   nnodesg,ntface_local,ntet
       end if

    end if
    call lmpi_conditional_stop(0)
    if (.false.) write(*,*) ntet ! avoid compiler warning

    call lmpi_bcast(ntface_local)

    if (allocated(faceptr)) deallocate(faceptr)
    if (allocated(facetag)) deallocate(facetag)
    allocate(faceptr(ntface_local,4)); faceptr = 0
    allocate(facetag(ntface_local));   facetag = 0

!   allocation of facptr is increase to 4 nodes for compatibility with
!   quad faces and initialize these to 0

    if (lmpi_master) then
       if (trim(format) == 'ascii') then
          read(unit_grid+lmpi_id,*)                                            &
           (rdummy3,i=1,nnodesg),                                              &
           ((faceptr(i,j),j=1,3),i=1,ntface_local),                            &
            (facetag(i),i=1,ntface_local)
       else
          read(unit_grid+lmpi_id)                                              &
           (rdummy3,i=1,nnodesg),                                              &
           ((faceptr(i,j),j=1,3),i=1,ntface_local),                            &
            (facetag(i),i=1,ntface_local)
       end if
       if (.false.) write(*,*) rdummy3 ! avoid compiler warnings

       close(unit_grid+lmpi_id)
    end if

    call lmpi_bcast(faceptr)
    call lmpi_bcast(facetag)

    if (verbose2) write(*,*) 'Read fast boundary conditions complete'

  end subroutine puns3d_read_fast_bc

!=============================== READFELISA ==================================80
!
! Reads the c2n information from FELISA Format Meshes
!
!=============================================================================80

  subroutine puns3d_read_felisa_c2n(flow_dir, grid)

    use element_defs, only : type_tet, initialize_elem
    use grid_types,   only : grid_type

    character(*),    intent(in)    :: flow_dir
    type(grid_type), intent(inout) :: grid

    integer :: iostat, idummy
    character(len=80)  :: text

    continue

    if (lmpi_master) then

  ! assigning file names.  check for which volume mesh file to use.

       gridfilename = trim(flow_dir) // trim(grid%project) // '.gri'
       call se_open(unit_grid,file=gridfilename,status='old',iostat=iostat)

       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(gridfilename),                        &
                   ' stopping...; iostat = ', iostat
         call lmpi_conditional_stop(1,'Bad file')
       endif
       rewind (unit=unit_grid)

  ! read mesh parameters
       read (unit_grid,'(a)') text
       read (unit_grid,*) grid%ncellg, grid%nnodesg, idummy
       if (.false.) write(*,*) idummy,text ! avoid compiler warnings
       read (unit_grid,'(a)') text
    end if
    call lmpi_conditional_stop(0)

    call lmpi_bcast(grid%nnodesg)
    call lmpi_bcast(grid%ncellg)

  ! just one element type (tets)

    grid%nelem = 1
    allocate(grid%elem(grid%nelem))
    call initialize_elem(grid%elem(1),type_tet)
    grid%elem(1)%ncell = 0
    grid%elem(1)%ncellg = grid%ncellg

    call puns3d_read_felisa_core_c2n(unit_grid,grid)

    pp_csize = grid%elem(1)%ncell
    grid%elem(1)%ncell  = pp_csize(lmpi_id)
    grid%elem(1)%ncellg = grid%ncellg

    if (lmpi_master) close(unit_grid)

  end subroutine puns3d_read_felisa_c2n

!=============================== READFELISA_BC ===============================80
!
! Reads the faceptr, facetag information from FELISA Format Meshes (.fro)
! The .bco is read in read_mapbc
!
!=============================================================================80

  subroutine puns3d_read_felisa_bc(flow_dir, grid)

    use grid_types,         only : grid_type

    character(*),    intent(in)    :: flow_dir
    type(grid_type), intent(inout) :: grid

    integer :: i, j, iostat, idummy, npboun

    character (len=80) :: bcfilename

    logical :: verbose = .false.

    continue

!   assigning file names.  check for which volume mesh file to use.

    if (lmpi_id == 0) then

       bcfilename = trim(flow_dir) // trim(grid%project) // '.fro'

       if (verbose) write (*,*) 'Opening ',trim(bcfilename)
       call se_open(unit_bc,file=bcfilename,                                   &
               status='old',iostat=iostat)
       if ( iostat /= 0 ) then
         write(*,*)'error opening ',trim(bcfilename),' stopping...'
         call lmpi_conditional_stop(1)
       endif
       rewind (unit_bc)

!   no. of boundary faces and face types

       read (unit_bc,*) ntface, npboun, idummy, idummy, idummy, nbctags
       if (verbose) write(*,*) ' ntface=',ntface,' nbctags=',nbctags
       if (.false.) write(*,*) idummy ! avoid compiler warnings

    end if
    call lmpi_conditional_stop(0)

    call lmpi_bcast(ntface)
    call lmpi_bcast(npboun)
    call lmpi_bcast(nbctags)

!   allocation of facptr is increase to 4 nodes for compatibility with
!   quad faces and initialize these to 0

    if (allocated(faceptr))    deallocate(faceptr)
    if (allocated(facetag))    deallocate(facetag)
    allocate(faceptr(ntface,4)); faceptr = 0
    allocate(facetag(ntface));   facetag = 0

!   Skip surface node coordinates

    if (lmpi_id == 0) then

       if (verbose) write(*,*) 'Skipping surface nodes'

       do i = 1,npboun
         read(unit_bc,*) idummy
       enddo

!   Surface triangles
!   Surface tag.  facetag(i) is equivalent to isurf(i)

       if (verbose) write(*,*) 'Reading boundary face connectivity surface tag'

       do i=1,ntface
         read(unit_bc,*)idummy,(faceptr(i,j),j=1,3),facetag(i)
       end do

       close(unit_bc)

       if (verbose) print *, 'read felisa .fro complete'

    end if

    call lmpi_bcast(faceptr)
    call lmpi_bcast(facetag)

  end subroutine puns3d_read_felisa_bc


!============================ PUNS3D_READ_FELISA_CORE_C2N ====================80
!
! Does the actual c2n reads
!
!=============================================================================80

  subroutine puns3d_read_felisa_core_c2n(unit_grid,grid)

    use grid_types,  only : grid_type

    integer,         intent(in)    :: unit_grid
    type(grid_type), intent(inout) :: grid

    integer :: i, ihead, itail, iest, ipe
    integer :: ct_c2n, sz_c2n, icell, is,ie,isize, ierr

    real(dp), dimension(:),   allocatable :: x,y,z
    integer,  dimension(:),   allocatable :: cl2g, temp1
    integer,  dimension(:,:), allocatable :: c2n,  temp2, tc2n

    continue

    call distribute_fast_c2n(grid%nnodesg)

! l2g

    grid%nnodes0  = pp_nsize(lmpi_id)
    grid%nnodes01 = grid%nnodes0
    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

! c2n

    ihead = pp_nhead(lmpi_id)
    itail = pp_ntail(lmpi_id)

    iest = max(nint((grid%elem(1)%ncellg/lmpi_nproc) * 2.0), 10000)
    allocate(cl2g  (iest)); cl2g = 0
    allocate(c2n (4,iest));  c2n = 0
    allocate(tc2n(4,iest)); tc2n = 0
    !write(*,*)"ih,it,iest ",lmpi_id,ihead,itail,iest

    ct_c2n = 0    ! count in c2n, cl2g
    sz_c2n = iest ! size  of c2n, cl2g
    do icell = 1,grid%elem(1)%ncellg,iest
       is = icell
       ie = icell+iest
       if (ie > grid%elem(1)%ncellg) ie = grid%elem(1)%ncellg
       isize = (ie-is)+1
       tc2n = 0
       if (lmpi_master) then
          do i = 1,isize
             read (unit_grid,*) tc2n(1:4,i)
             !write(300,'(1x,i0," : ",4(i0,1x))') (is+i)-1,tc2n(1:4,i)
          end do
       end if
       call lmpi_bcast(tc2n)
       do i = 1,isize
          if (((tc2n(1,i) >= ihead).and.(tc2n(1,i) <= itail)).or. &
              ((tc2n(2,i) >= ihead).and.(tc2n(2,i) <= itail)).or. &
              ((tc2n(3,i) >= ihead).and.(tc2n(3,i) <= itail)).or. &
              ((tc2n(4,i) >= ihead).and.(tc2n(4,i) <= itail))) then
             if (ct_c2n >= sz_c2n) then
                allocate(temp2(4,sz_c2n))
                temp2 = c2n
                deallocate(c2n)
                allocate(c2n(4,sz_c2n+iest)); c2n = 0
                c2n(1:4,1:ct_c2n) = temp2(1:4,1:ct_c2n)
                deallocate(temp2)

                allocate(temp1(sz_c2n))
                temp1 = cl2g
                deallocate(cl2g)
                allocate(cl2g(sz_c2n+iest)); cl2g = 0
                cl2g(1:ct_c2n) = temp1(1:ct_c2n)
                deallocate(temp1)

                sz_c2n = sz_c2n+iest
             end if
             ct_c2n = ct_c2n + 1
             c2n(1:4,ct_c2n) = tc2n(1:4,i)
             cl2g(ct_c2n)    = (is+i)-1
          end if
       end do
    end do
    if (ct_c2n > 0) then
       allocate(grid%elem(1)%c2n(4,ct_c2n))
       grid%elem(1)%c2n(1:4,1:ct_c2n) = c2n(1:4,1:ct_c2n)
       allocate(grid%elem(1)%cl2g(ct_c2n))
       grid%elem(1)%cl2g(1:ct_c2n) = cl2g(1:ct_c2n)
    end if
    deallocate(tc2n,c2n,cl2g)
    grid%elem(1)%ncell = ct_c2n

    !do i = 1,grid%elem(1)%ncell
    !   write(1300+lmpi_id,'(1x,i0," : ",4(i0,1x))')                           &
    !     grid%elem(1)%cl2g(i),grid%elem(1)%c2n(1:4,i)
    !end do

! xyz

    if (lmpi_master) read(unit_grid,*) ! coordinates

    allocate(grid%x(grid%nnodes0)); grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0)); grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0)); grid%z = 0.0_dp

    if (lmpi_master) then
       do i = 1,grid%nnodes0
         read (unit_grid,*) grid%x(i),grid%y(i),grid%z(i)
       end do
    end if

    if (lmpi_nproc > 1) then
       if (lmpi_master) then
          isize = pp_nsize(1)
          allocate(x(isize)); x = 0.0_dp
          allocate(y(isize)); y = 0.0_dp
          allocate(z(isize)); z = 0.0_dp

          do ipe = 1,lmpi_nproc-1
             if (pp_nsize(ipe) /= isize) then
                deallocate(x,y,z)
                isize = pp_nsize(ipe)
                allocate(x(isize)); allocate(y(isize)); allocate(z(isize))
             end if
             x = 0.0_dp; y = 0.0_dp; z = 0.0_dp

             do i = 1,pp_nsize(ipe)
                read(unit_grid,*) x(i),y(i),z(i)
             end do
             call lmpi_send(x,isize,ipe,ipe,ierr)
             call lmpi_send(y,isize,ipe,ipe,ierr)
             call lmpi_send(z,isize,ipe,ipe,ierr)
          end do
          deallocate(x,y,z)
       else
          call lmpi_recv(grid%x,grid%nnodes0,0,lmpi_id,ierr)
          call lmpi_recv(grid%y,grid%nnodes0,0,lmpi_id,ierr)
          call lmpi_recv(grid%z,grid%nnodes0,0,lmpi_id,ierr)
       end if
    end if
   !do i = 1,grid%nnodes0
   !   write(2200+lmpi_id,'(1x,i0," : ",3(E20.12,1x))') &
   !     grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
   !end do
   !write(*,*)"See 2200 nn0 ",grid%nnodes0
   !call lmpi_conditional_stop(1,'see 2200')

  end subroutine puns3d_read_felisa_core_c2n


!============================== distribute_fast_c2n ==========================80
!
! Compute the number of nodes per processor.
!
!=============================================================================80

  subroutine distribute_fast_c2n(nnodes)

    integer, intent(in) :: nnodes

    integer :: irem, istart, i

    continue

   ! Compute the node (c2n_info%n) _size, _head, and _tail for all processors.
   ! Each processor will hold this information to use in data exchange.

    irem = nnodes - ((nnodes / lmpi_nproc) * lmpi_nproc)
    istart = 1
    do i = 0, lmpi_nproc-1

        pp_nhead(i) = istart
        pp_nsize(i) = nnodes / lmpi_nproc
        if (irem  /=  0) then
          if (i <  irem) pp_nsize(i) = pp_nsize(i) + 1
        end if
        pp_ntail(i) = (pp_nhead(i) + pp_nsize(i)) - 1
        istart = pp_ntail(i) + 1

!       if (lmpi_id==0)                                                        &
!       write(*,*) 'i,nh,ns,nt = ',i,pp_nhead(i),pp_nsize(i),pp_ntail(i)
    end do

  end subroutine distribute_fast_c2n


!==================================== LUMP_BOUNDARIES ========================80
!
! Driver to lump boundaries
!
!=============================================================================80

  subroutine lump_boundaries(raw_grid_data, global_grid)

    use grid_types,    only : grid_type, raw_grid_data_type

    type(raw_grid_data_type), intent(in)    :: raw_grid_data
    type(grid_type),          intent(inout) :: global_grid

  continue

       select case (trim(raw_grid_data%patch_lumping))
         case ('none')
         case ('bc')
           if (lmpi_master) write(*,'(a)') ' Boundaries Grouped By BC Type'
           call puns3d_lump_bc()
         case ('family')
           if (lmpi_master) then
             write(*,'(a)') ' Boundaries Grouped By Family'
             write(*,'(a)') ' (requires family info in .mapbc file)'
           end if
           if (raw_grid_data%grid_format == 'vgrid') then
              call vgrid_lump_family()
           else
              call mapbc_lump_family(global_grid%project)
           end if
         case default
           if (lmpi_master) write (*,*) 'Error: Group Option Not Known'
           call lmpi_conditional_stop(1)
       end select

  end subroutine lump_boundaries


!============================ VGRID_LUMP_FAMILY ==============================80
!
! Lumps the seperate boundary segments of same bc family into single boundary
!
! Also used to simplify output and plotting operations
!
!=============================================================================80

  subroutine vgrid_lump_family()

    integer :: i, j, unique_max

    integer, dimension(:), allocatable :: unique_bc, unique_tag

    character(len=120) :: family
    character(len=120), dimension(:), allocatable :: unique_string

  continue

    unique_max = 1

    if (lmpi_master) then

       allocate(unique_bc(nbctags));    unique_bc  = 0
       allocate(unique_tag(nbctags));   unique_tag = 0
       allocate(unique_string(nbctags))

       unique_string(1) = trim(family_org(1))
       unique_bc(1)     = tagmap(1,2)
       unique_tag(1)    = 1

       outer: do i = 2,nbctags
         family = family_org(i)
         do j = 1,unique_max
            if ((trim(family)==trim(unique_string(j))) .and.                   &
                (tagmap(i,2)==unique_bc(j))          ) then
              unique_tag(i) = j
              cycle outer
            end if
         end do

         unique_max                = unique_max + 1
         unique_tag(i)             = unique_max
         unique_bc(unique_max)     = tagmap(i,2)
         unique_string(unique_max) = trim(family)

       end do outer
       nbctags = unique_max

    end if

    call lmpi_bcast(nbctags)
    if (nbctags /= nbctags_org) then
       deallocate(tagmap)
       deallocate(family_map)
       allocate(tagmap(nbctags,2));   tagmap     = 0
       allocate(family_map(nbctags)); family_map = ''
    end if

    if (lmpi_master) then

       write(*,*)
       write(*,'(a)')'  unique      bc  family'
       write(*,'(a)')'  ------      --  ------'
       do i = 1,unique_max
          write(*,'(2i8,2 a)')i,unique_bc(i), '  ', trim(unique_string(i))
       end do

!      now we can do some lumping

       do i = 1, ntface
         facetag(i) = unique_tag(facetag(i))
       enddo

       do i = 1, unique_max
         tagmap(i,1) = i
         tagmap(i,2) = unique_bc(i)
         call backwards_compatible_bc(tagmap(i,2))
         family_map(i) = unique_string(i)
       end do

       deallocate(unique_string)
       deallocate(unique_bc)
       deallocate(unique_tag)

    end if

    call lmpi_bcast(tagmap)
    call lmpi_bcast(facetag)
    do i = 1,nbctags
       call lmpi_bcast(family_map(i))
    end do

  end subroutine vgrid_lump_family

!============================ MAPBC_LUMP_FAMILY ==============================80
!
! Lumps the separate boundary segments of same bc family into single boundary
!
!=============================================================================80

  subroutine mapbc_lump_family(project)

    use system_extensions, only : se_open
    use string_utils,      only : strip

    character(80), intent(in) :: project

    integer :: i, j, unique_max, iostat, families
    integer :: facetag_old, facetag_new

    integer, dimension(:), allocatable :: unique_bc, unique_tag

    character(120) :: family
    character(120), dimension(:), allocatable :: unique_string
    character(120), dimension(:), allocatable :: family_string

    character(len=80) :: filename

    logical :: verbose = .false.
    logical :: found

    integer :: iu = 56

    continue

! allocate family_org and family_map, carry family info into solver.

    do i = 1,nbctags
       if (family_org(i) == '') then
          if (lmpi_master) write(*,'(/,a,i0)')' Info missing for boundary ',i+1
          call lmpi_die()
       end if
    end do

    if (lmpi_master) then

       allocate(unique_bc (nbctags)); unique_bc  = 0
       allocate(unique_tag(nbctags)); unique_tag = 0
       allocate (unique_string(nbctags))
       allocate (family_string(nbctags))

       unique_max = 0
       families   = 0
       do i = 1,nbctags

         family = family_org(i)
         family_string(i) = trim(family)

         if (verbose) write(*,'(a,i5,3a)')'family:',i,' >', trim(family) ,'<'

         found = .false.
         do j = 1, i-1
           if ( trim(family) == trim(family_string(j)) ) found = .true.
           if ( found ) exit
         enddo

         if ( .not. found ) then
           families = families + 1
           family_string(families) = trim(family)
         endif

         find_unique : do j = 1, nbctags
           ! Have I seen this family and bc before?
           existing_tag : if ( trim(family) == trim(unique_string(j))          &
                         .and. tagmap(i,2) ==  unique_bc(j)) then
             unique_tag(i) = j
             exit find_unique
           endif existing_tag

           ! Is this a new family and bc combo

           new_unique_tag : if (j > unique_max) then
             unique_max = unique_max + 1
             unique_string(j) = trim(family)
             unique_bc(j) = tagmap(i,2)
             unique_tag(i) = j
             exit find_unique
           endif new_unique_tag

         enddo find_unique
       end do

       if ( families /= unique_max ) then
         write(*,*) ' Number of families=',families

         write(*,*)
         write(*,'(a)')'  family      family name'
         write(*,'(a)')'  ------      -----------'

         do i = 1,families
           write(*,'(i8,6x,a)') i, trim(family_string(i))
         end do
       endif

       write(*,*) ' Number of families with unique_bc=',unique_max

       write(*,*)
       write(*,'(a)')'  unique      bc  family'
       write(*,'(a)')'  ------      --  ------'

       do i = 1,unique_max
         write(*,'(2i8,2a)') i, unique_bc(i), '  ', trim(unique_string(i))
       end do

       if (verbose) then
         write(*,*)
         write(*,'(a)')' old tag new tag'
         do i = 1,nbctags
           write(*,'(2i8)')i,unique_tag(i)
         end do
       endif

       do i=1,ntface+nqface
         facetag_old= facetag(i)
         facetag_new= unique_tag(facetag_old)
         facetag(i) = facetag_new
       end do

       do i=1,unique_max
         tagmap(i,2) = unique_bc(i)
         call backwards_compatible_bc(tagmap(i,2))
       end do

       filename = trim(project) // '.mapbc.override'

       call se_open(iu,file=filename,status='unknown',iostat=iostat)
       if ( iostat /= 0 ) then
         write(*,*)'Stopping...error opening ',trim(filename)
         call lmpi_conditional_stop(1)
       endif
       rewind(iu)

       write(iu,'(i8)') unique_max
       do i = 1,unique_max
         write(iu,'(2i8,2a)') i, unique_bc(i), '  ', trim(unique_string(i))
       end do

       close(iu)

       write(*,*)
       write(*,"(1x,a,i9,a,i9)")'Grouped ',nbctags,' boundaries into',unique_max
       write(*,"(1x,a,a)")      'Created BC file : ',trim(filename)
       write(*,*)

       do i = 1,nbctags
          family_map(i) = strip(family_string(i))
       end do

       deallocate(family_string)
       deallocate(unique_string)
       deallocate(unique_tag)
       deallocate(unique_bc)

       nbctags = unique_max

    end if

   call lmpi_conditional_stop(0)
   call lmpi_bcast(nbctags)
   call lmpi_bcast(tagmap)
   call lmpi_bcast(facetag)

   do i = 1,nbctags_org
      call lmpi_bcast(family_org(i))
   end do
   do i = 1,nbctags
      call lmpi_bcast(family_map(i))
   end do

  end subroutine mapbc_lump_family


!============================ PUNS3D_LUMP_BC =================================80
!
! Lumps the seperate boundary segments of same bc type into single boundary
! segments for compatabilty/testing with old school preface-makeface-puns3d
!
! Also used to simplify output and plotting operations
!
!=============================================================================80
!
  subroutine puns3d_lump_bc()

    integer :: iface, ibc, itag

    continue

    if (lmpi_master) write(*,*) 'Lumping boundary types together...'

!   set the bc tags to an tagmap index, which is their first tagmap instance
!     and set the first element in tagmap to 1, 2, 3, ....

!   this is done by getting their ibc value and setting their new tag
!     to the FIRST tagmap index of that ibc (boundary condition number).
!     therefore this routine depends on itag2ibc finding the FIRST tag

!   there is probably a better way to do this....

    do iface=1, ntface+nqface
      ibc = itag2ibc(facetag(iface))
      facetag(iface) = ibcindex(ibc)
    end do

    do itag = 1, nbctags
      tagmap(itag,1) = itag
    end do

  end subroutine puns3d_lump_bc

!=============================================================================80
! function itag2ibc
!=============================================================================80
  integer function itag2ibc(itag)

    integer, intent(in) :: itag

    continue

    itag2ibc = tagmap(itagindex(itag),2)

  end function itag2ibc

!=============================================================================80
! function itagindex
!=============================================================================80
  integer function itagindex(itag)

    integer, intent(in) :: itag

    integer             :: i

    continue

!   NOTE that puns3d_lump_bc is depending on the fact that this function
!     finds the FIRST itag (e.g. lowest itagindex) with a matching
!     boundary condition

    search_tag : do i = 1, nbctags

      if (tagmap(i,1) == itag) then
        itagindex  = i
        return
      end if

    end do search_tag

    itagindex = -99
    print *, 'ERROR: No index for surface tag = ', itag,nbctags
    print *, 'ERROR: bcmap index not found in itagindex, stopping...'
    stop ! FIXME: should be lmpi_die or se_exit(1)?

  end function itagindex

!=============================================================================80
! function ibcindex
!=============================================================================80

  integer function ibcindex(ibc)

    integer, intent(in) :: ibc

    integer             :: i

    integer, save       :: indexguess = 1

    continue

!   NOTE that puns3d_lump_bc is depending on the fact that this function
!     finds the FIRST itag (e.g. lowest itagindex) with a matching
!     boundary condition

    guess_the_tag : if (tagmap(indexguess,2) == ibc) then
      ibcindex = indexguess
      return
    end if guess_the_tag

    search_tag : do i = 1, nbctags

      if (tagmap(i,2) == ibc) then
        ibcindex  = i
        indexguess = i
        return
      end if

    end do search_tag

    ibcindex = -99
    print *, 'ERROR: No index for bc = ', ibc
    print *, 'ERROR: bcmap index not found in ibcindex, stopping...'
    stop ! FIXME: should be lmpi_die or se_exit(1)?


  end function ibcindex

!=============================== READ_AFLR3 ==================================80
!
! Reads the c2n information from AFLR3 Format Meshes, evenly distributing
! the cells among the PEs.
!
!=============================================================================80

  subroutine puns3d_read_aflr3_c2n(flow_dir,grid,format,twod_mode)

    use grid_types,        only : grid_type
    use element_defs,      only : type_tet, type_pyr, type_prz, type_hex,      &
                                  initialize_elem
    use system_extensions, only : se_open, se_open_big
    use info_depr,         only : twod
    use file_utils,        only : big_endian_io, se_open_big_is_big

    character(*), intent(in) :: flow_dir

    type(grid_type), intent(inout) :: grid
    character(*),    intent(in)    :: format
    logical,         intent(in)    :: twod_mode

    integer :: ntet, npyr, nprz, nhex, ntface, nqface

    integer :: i, j, idummy, my_nn, iostat
    integer :: nsegment, binc, xinc, isize, iest, goff
    integer :: bs, be, xs, xe, isegment

!beginNeverComplex
    real(dp) :: rdummy, rdummy3(3)
!endNeverComplex

    integer, dimension(:,:), allocatable :: tc2n

!beginNeverComplex
    real(dp), dimension(:), allocatable :: x,y,z
!endNeverComplex

    integer, parameter :: nslice = buffer_limit/16 ! 4*4 Bytes

  continue

    if ((trim(format) == 'stream').or.(trim(format) == 'stream64')) then
       call puns3d_read_aflr3_c2n_sio(flow_dir,grid,twod_mode,format)
       return
    endif

    if ( twod_mode ) twod = .true.

!   Only processor 0 reads the data (skip upto c2n)

    iostat = 0
    if (lmpi_master) then
       gridfilename = trim(flow_dir) // trim(grid%project)
       if ( trim(format) == 'ascii' ) then
          gridfilename = trim(gridfilename) // '.ugrid'
          write (*,*) ' Preparing to read ASCII AFLR3 grid ', &
                      trim(gridfilename)
          call se_open(unit_grid,file=gridfilename,status='old',iostat=iostat)
       else
         if ( big_endian_io() ) then
           gridfilename = trim(gridfilename) // '.r8.ugrid'
         else
           gridfilename = trim(gridfilename) // '.lr8.ugrid'
         end if
         write (*,*) ' Preparing to read unformatted AFLR3 grid ', &
                     trim(gridfilename)
         call se_open(unit_grid,file=gridfilename,form='unformatted',          &
                      status='old',iostat=iostat)
         if ( iostat /= 0 .and. &
           .not. big_endian_io() .and. se_open_big_is_big() ) then
           write(*,*)'error opening ',trim(gridfilename)
           gridfilename = trim(flow_dir) // trim(grid%project) // '.r8.ugrid'
           write (*,*) ' Preparing to read unformatted AFLR3 grid ', &
             trim(gridfilename)
           call se_open_big(unit_grid,file=gridfilename,form='unformatted',    &
             status='old',convert='big_endian',iostat=iostat)
         end if
       end if
       if ( iostat /= 0 )                                                      &
         write(*,*)'error opening ',trim(gridfilename),' stopping...'
    end if
    call lmpi_conditional_stop(iostat)

    if (lmpi_master) then

       if ( trim(format) == 'ascii' ) then
          read (unit_grid,*) grid%nnodesg,ntface,nqface,ntet,npyr,nprz,nhex
       else     ! Must be unformatted
          read (unit_grid)   grid%nnodesg,ntface,nqface,ntet,npyr,nprz,nhex
       end if

!      if (verbose) then
         write(*,'(''  nnodes              '',i0)')  grid%nnodesg
         write(*,'(''  ntface,nqface       '',2(i0,1x))') ntface,nqface
         write(*,'(''  ntet,npyr,nprz,nhex '',4(i0,1x))') ntet,npyr,nprz,nhex
         write(*,*)
!      end if

    end if

    call lmpi_bcast(grid%nnodesg)
    call lmpi_bcast(ntet)
    call lmpi_bcast(npyr)
    call lmpi_bcast(nprz)
    call lmpi_bcast(nhex)
    call lmpi_bcast(ntface)
    call lmpi_bcast(nqface)
    grid%ncellg = ntet+npyr+nprz+nhex

    call distribute_fast_c2n(grid%nnodesg)

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%nnodes01   = grid%nnodes0
    allocate(grid%x(grid%nnodes0));   grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0));   grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0));   grid%z = 0.0_dp
    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

!  Read xyz

   ! For non-master, allocate dummy arguments
     if (.not.lmpi_master) then
        allocate(x(1)); x = 0.0_dp
        allocate(y(1)); y = 0.0_dp
        allocate(z(1)); z = 0.0_dp
     end if

    nsegment = grid%nnodesg/nslice
    if (grid%nnodesg > nsegment*nslice) nsegment = nsegment + 1
    !if (lmpi_master)                                                          &
    !write(*,*)'NS',lmpi_id,nsegment,grid%nnodesg,nslice,                      &
    !               grid%nnodesg/nslice,nsegment*nslice
    xinc = grid%nnodesg/nsegment
    if (grid%nnodesg > xinc*nsegment) xinc = xinc+1

     xs = 1
     do isegment = 1,nsegment

        xe = (xs + xinc) - 1
        if (xe > grid%nnodesg) xe = grid%nnodesg
        !if (lmpi_master) write(*,*)"isegment,xs,xe = ",isegment,xs,xe

        if (lmpi_master) then
           allocate(x(xs:xe)); x = 0.0_dp
           allocate(y(xs:xe)); y = 0.0_dp
           allocate(z(xs:xe)); z = 0.0_dp

           if ( trim(format) == 'ascii' ) then
              if (isegment == 1) then
                 read(unit_grid,*)(x(i),y(i),z(i),i=xs,xe)
              else
                 rewind(unit_grid)
                 read(unit_grid,*)(idummy,i=1,7)
                 read(unit_grid,*)(rdummy,rdummy,rdummy,i=1,xs-1),             &
                                  (x(i),y(i),z(i),i=xs,xe)
              end if
           else     ! Must be unformatted
              rewind(unit_grid)
              read(unit_grid)(idummy,i=1,7)
              if (isegment == 1) then
                 read(unit_grid) (x(i),y(i),z(i),i=xs,xe)
              else
                 read(unit_grid) (rdummy,rdummy,rdummy,i=1,xs-1),              &
                                 (x(i),y(i),z(i),i=xs,xe)
              end if
           end if

           call distribute_xyz(xs,xe,grid,xs,xe,x,y,z)
           deallocate(x,y,z)
        else
           call distribute_xyz(xs,xe,grid,1,1,x,y,z)
        end if
        xs = xe+1

      end do
      if (.not.lmpi_master) deallocate(x,y,z)
      if (.false.) write(*,*) rdummy, idummy ! comp warns

! Skip faces

    if (lmpi_master.and.trim(format)=='ascii') then
       if (ntface > 0) read(unit_grid,*) ((idummy, i=1,3), j=1,ntface)
       if (nqface > 0) read(unit_grid,*) ((idummy, i=1,4), j=1,nqface)
       read (unit_grid,*) (idummy, i=1,ntface+nqface)
    end if

! Read c2n

    goff       = 0
    grid%nelem = 0
    if (ntet > 0) grid%nelem = grid%nelem + 1
    if (npyr > 0) grid%nelem = grid%nelem + 1
    if (nprz > 0) grid%nelem = grid%nelem + 1
    if (nhex > 0) grid%nelem = grid%nelem + 1

    allocate(grid%elem(grid%nelem))

    my_nn = 0
    if (ntet > 0) then

       my_nn = my_nn + 1

       ! Use elem for temporary storage
       isize = 4
       grid%elem(my_nn)%ncellg = ntet
       call initialize_elem(grid%elem(my_nn), type_tet)
       grid%elem(my_nn)%ncell  = 0

       iest = max(nint((ntet/lmpi_nproc) * 2.0), 10000)
       !write(*,*)"ALLOC c2n ",lmpi_id,iest,ntet,ntet/lmpi_nproc
       allocate(grid%elem(my_nn)%c2n(isize,iest)); grid%elem(my_nn)%c2n  = 0
       allocate(grid%elem(my_nn)%cl2g(iest));      grid%elem(my_nn)%cl2g = 0
      !allocate(grid%elem(my_nn)%c2e(6,1));        grid%elem(my_nn)%c2e  = 0

       if (.not.lmpi_master) then
          allocate(tc2n(isize,1)); tc2n = 0
       end if

       nsegment = ntet/nslice
       if (ntet > nsegment*nslice) nsegment = nsegment + 1

       binc = ntet/nsegment
       if (ntet > binc*nsegment) binc = binc + 1
       !write(*,*)'NS',lmpi_id,nsegment,ntet,nslice,ntet/nslice,               &
       !          nsegment*nslice,binc

       bs = 1
       do isegment = 1,nsegment
          be = (bs + binc) - 1
          if (be > ntet) be = ntet
          !write(*,*)"isegment,bs,be = ",isegment,bs,be
          if (lmpi_master) then
             allocate(tc2n(isize,bs:be)); tc2n = 0
             if (trim(format) == 'ascii') then
                if (isegment == 1) then
                   read(unit_grid,*) (tc2n(1,i),tc2n(2,i),                     &
                                      tc2n(3,i),tc2n(4,i),i=bs,be)
                else
                  rewind(unit_grid)
                  read(unit_grid,*)(idummy,i=1,7)
                  read(unit_grid,*)(rdummy3,i=1,grid%nnodesg)
                  if (ntface > 0) read(unit_grid,*)((idummy,j=1,3),i=1,ntface)
                  if (nqface > 0) read(unit_grid,*)((idummy,j=1,4),i=1,nqface)
                  read(unit_grid,*)  (idummy,i=1,ntface+nqface)
                  read(unit_grid,*) ((idummy,j=1,isize),i=1,bs-1),             &
                      (tc2n(1,i),tc2n(2,i),                                    &
                       tc2n(3,i),tc2n(4,i),i=bs,be)
                end if
             else     ! Must be unformatted
                backspace(unit_grid)
                if (isegment == 1) then
                  read(unit_grid)                                              &
                    (rdummy3,i=1,grid%nnodesg),                                &
                    ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface),  &
                    (idummy,i=1,ntface+nqface),                                &
                    (tc2n(1,i),tc2n(2,i),                                      &
                    tc2n(3,i),tc2n(4,i),i=bs,be)
                else
                  read(unit_grid)                                              &
                    (rdummy3,i=1,grid%nnodesg),                                &
                    ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface),  &
                    (idummy,i=1,ntface+nqface),                                &
                    ((idummy,j=1,isize),i=1,bs-1),                             &
                    (tc2n(1,i),tc2n(2,i),tc2n(3,i),tc2n(4,i),i=bs,be)
                end if
             end if
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,bs,be,tc2n,my_nn,goff)
             deallocate(tc2n)
         else
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,1,1,tc2n,my_nn,goff)
         end if ! master
         bs = be + 1
       end do
       if (.not.lmpi_master) deallocate(tc2n)
       goff = goff + ntet
    end if

    if (npyr > 0) then

       my_nn = my_nn + 1

       ! Use elem for temporary storage
       isize = 5
       grid%elem(my_nn)%ncellg = npyr
       call initialize_elem(grid%elem(my_nn), type_pyr)
       grid%elem(my_nn)%ncell  = 0

       iest = max(nint((npyr/lmpi_nproc) * 2.0), 10000)
       !write(*,*)"ALLOC c2n ",lmpi_id,iest,npyr,npyr/lmpi_nproc
       allocate(grid%elem(my_nn)%c2n(isize,iest)); grid%elem(my_nn)%c2n  = 0
       allocate(grid%elem(my_nn)%cl2g(iest));      grid%elem(my_nn)%cl2g = 0
      !allocate(grid%elem(my_nn)%c2e(8,1));        grid%elem(my_nn)%c2e  = 0

       if (.not.lmpi_master) then
          allocate(tc2n(isize,1)); tc2n = 0
       end if

       nsegment = npyr/nslice
       if (npyr > nsegment*nslice) nsegment = nsegment + 1

       binc = npyr/nsegment
       if (npyr > binc*nsegment) binc = binc + 1
       !write(*,*)'NS',lmpi_id,nsegment,npyr,nslice,npyr/nslice,               &
       !          nsegment*nslice,binc

       bs = 1
       do isegment = 1,nsegment
          be = (bs + binc) - 1
          if (be > npyr) be = npyr
          !write(*,*)"isegment,bs,be = ",isegment,bs,be
          if (lmpi_master) then
             allocate(tc2n(isize,bs:be)); tc2n = 0
             if (trim(format) == 'ascii') then
                if (isegment == 1) then
                   read(unit_grid,*)                                           &
                       (tc2n(4,i),tc2n(3,i),tc2n(5,i),                         &
                        tc2n(1,i),tc2n(2,i),i=bs,be)
                else     ! Must be unformatted
                  rewind(unit_grid)
                  read(unit_grid,*) (idummy,i=1,7)
                  read(unit_grid,*) (rdummy3,i=1,grid%nnodesg)
                  if (ntface > 0) read(unit_grid,*)((idummy,j=1,3),i=1,ntface)
                  if (nqface > 0) read(unit_grid,*)((idummy,j=1,4),i=1,nqface)
                  read(unit_grid,*)(idummy,i=1,ntface+nqface)
                  if (ntet > 0)   read(unit_grid,*)((idummy,j=1,4),i=1,ntet)
                  read(unit_grid,*) ((idummy,j=1,isize),i=1,bs-1),             &
                                    (tc2n(4,i),tc2n(3,i),                      &
                                     tc2n(5,i),tc2n(1,i),                      &
                                     tc2n(2,i),i=bs,be)
                end if
             else
                backspace(unit_grid)
                if (isegment == 1) then
                   read(unit_grid)                                             &
                     (rdummy3,i=1,grid%nnodesg),                               &
                     ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface), &
                     (idummy,i=1,ntface+nqface),                               &
                     (idummy,i=1,ntet*4),                                      &
                     (tc2n(4,i),tc2n(3,i), tc2n(5,i),tc2n(1,i),                &
                      tc2n(2,i),i=bs,be)
                else
                   read(unit_grid)                                             &
                     (rdummy3,i=1,grid%nnodesg),                               &
                     ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface), &
                     (idummy,i=1,ntface+nqface),                               &
                     (idummy,i=1,ntet*4),                                      &
                     ((idummy,j=1,isize),i=1,bs-1),                            &
                      (tc2n(4,i),tc2n(3,i), tc2n(5,i),tc2n(1,i),               &
                       tc2n(2,i),i=bs,be)
                end if
             end if
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,bs,be,tc2n,my_nn,goff)
             deallocate(tc2n)
         else
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,1,1,tc2n,my_nn,goff)
         end if ! master
         bs = be + 1
       end do
       if (.not.lmpi_master) deallocate(tc2n)
       goff = goff + npyr
    end if

    if (nprz > 0) then

       my_nn = my_nn + 1

       ! Use elem for temporary storage
       isize = 6
       grid%elem(my_nn)%ncellg = nprz
       call initialize_elem(grid%elem(my_nn), type_prz)
       grid%elem(my_nn)%ncell  = 0

       iest = max(nint((nprz/lmpi_nproc) * 2.0), 10000)
       !write(*,*)"ALLOC c2n ",lmpi_id,iest,nprz,nprz/lmpi_nproc
       allocate(grid%elem(my_nn)%c2n(isize,iest)); grid%elem(my_nn)%c2n  = 0
       allocate(grid%elem(my_nn)%cl2g(iest));      grid%elem(my_nn)%cl2g = 0
      !allocate(grid%elem(my_nn)%c2e(9,1));        grid%elem(my_nn)%c2e  = 0

       if (.not.lmpi_master) then
          allocate(tc2n(isize,1)); tc2n = 0
       end if

       nsegment = nprz/nslice
       if (nprz > nsegment*nslice) nsegment = nsegment + 1

       binc = nprz/nsegment
       if (nprz > binc*nsegment) binc = binc + 1

       bs = 1
       do isegment = 1,nsegment
          be = (bs + binc) - 1
          if (be > nprz) be = nprz
          if (lmpi_master) then
             !write(*,*)"isegment,bs,be = ",isegment,bs,be,nsegment
             allocate(tc2n(isize,bs:be)); tc2n = 0
             if (trim(format) == 'ascii') then
                if (isegment == 1) then
                   read(unit_grid,*) (tc2n(1,i),tc2n(4,i),                     &
                                      tc2n(6,i),tc2n(2,i),                     &
                                      tc2n(3,i),tc2n(5,i),i=bs,be)
                else     ! Must be unformatted
                  rewind(unit_grid)
                  read(unit_grid,*)(idummy,i=1,7)
                  read(unit_grid,*)(rdummy3,i=1,grid%nnodesg)
                  if (ntface > 0) read(unit_grid,*)((idummy,j=1,3),i=1,ntface)
                  if (nqface > 0) read(unit_grid,*)((idummy,j=1,4),i=1,nqface)
                  read(unit_grid,*)(idummy,i=1,ntface+nqface)
                  if (ntet > 0)   read(unit_grid,*)((idummy,j=1,4),i=1,ntet)
                  if (npyr > 0)   read(unit_grid,*)((idummy,j=1,5),i=1,npyr)
                  read(unit_grid,*) ((idummy,j=1,isize),i=1,bs-1),             &
                     (tc2n(1,i),tc2n(4,i),tc2n(6,i),                           &
                      tc2n(2,i),tc2n(3,i),tc2n(5,i),                           &
                      i=bs,be)
                end if
             else
                backspace(unit_grid)
                if (isegment == 1) then
                   read(unit_grid)                                             &
                     (rdummy3,i=1,grid%nnodesg),                               &
                     ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface), &
                     (idummy,i=1,ntface+nqface),                               &
                     (idummy,i=1,ntet*4),(idummy,i=1,npyr*5),                  &
                     (tc2n(1,i),tc2n(4,i), tc2n(6,i),tc2n(2,i),                &
                      tc2n(3,i),tc2n(5,i),i=bs,be)
                else
                   read(unit_grid)                                             &
                     (rdummy3,i=1,grid%nnodesg),                               &
                     ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface), &
                     (idummy,i=1,ntface+nqface),                               &
                     (idummy,i=1,ntet*4), (idummy,i=1,npyr*5),                 &
                     ((idummy,j=1,isize),i=1,bs-1),                            &
                      (tc2n(1,i),tc2n(4,i), tc2n(6,i),tc2n(2,i),               &
                       tc2n(3,i),tc2n(5,i),i=bs,be)
                end if
             end if
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,bs,be,tc2n,my_nn,goff)
             deallocate(tc2n)
         else
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,1,1,tc2n,my_nn,goff)
         end if ! master
         bs = be + 1
       end do
       if (.not.lmpi_master) deallocate(tc2n)
       goff = goff + nprz
    end if

    if (nhex > 0) then

       my_nn = my_nn + 1

       ! Use elem for temporary storage
       isize = 8
       grid%elem(my_nn)%ncellg = nhex
       call initialize_elem(grid%elem(my_nn), type_hex)
       grid%elem(my_nn)%ncell  = 0

       iest = max(nint((nhex/lmpi_nproc) * 2.0), 10000)
       !write(*,*)"ALLOC c2n ",lmpi_id,iest,nhex,nhex,lmpi_nproc

       allocate(grid%elem(my_nn)%c2n(isize,iest)); grid%elem(my_nn)%c2n  = 0
       allocate(grid%elem(my_nn)%cl2g(iest));      grid%elem(my_nn)%cl2g = 0
      !allocate(grid%elem(my_nn)%c2e(12,iest));    grid%elem(my_nn)%c2e  = 0

       if (.not.lmpi_master) then
          allocate(tc2n(isize,1)); tc2n = 0
       end if

       nsegment = nhex/nslice
       if (nhex > nsegment*nslice) nsegment = nsegment + 1

       binc = nhex/nsegment
       if (nhex > binc*nsegment) binc = binc + 1
       !write(*,*)'NS',lmpi_id,nsegment,nhex,nslice,nhex/nslice,               &
       !          nsegment*nslice,binc

       bs = 1
       do isegment = 1,nsegment
          be = (bs + binc) - 1
          if (be > nhex) be = nhex
          !write(*,*)"isegment,bs,be = ",isegment,bs,be
          if (lmpi_master) then
             allocate(tc2n(isize,bs:be)); tc2n = 0
             if (trim(format) == 'ascii') then
                if (isegment == 1) then
                   read(unit_grid,*) (tc2n(1,i),tc2n(5,i),                     &
                                      tc2n(6,i),tc2n(2,i),                     &
                                      tc2n(3,i),tc2n(7,i),                     &
                                      tc2n(8,i),tc2n(4,i),i=bs,be)
                else     ! Must be unformatted
                  rewind(unit_grid)
                  read(unit_grid,*)(idummy,i=1,7)
                  read(unit_grid,*)(rdummy3,i=1,grid%nnodesg)
                  if (ntface > 0) read(unit_grid,*)((idummy,j=1,3),i=1,ntface)
                  if (nqface > 0) read(unit_grid,*)((idummy,j=1,4),i=1,nqface)
                  read(unit_grid,*)(idummy,i=1,ntface+nqface)
                  if (ntet > 0)   read(unit_grid,*)((idummy,j=1,4),i=1,ntet)
                  if (npyr > 0)   read(unit_grid,*)((idummy,j=1,5),i=1,npyr)
                  if (nprz > 0)   read(unit_grid,*)((idummy,j=1,6),i=1,nprz)
                  read(unit_grid,*)((idummy,j=1,isize),i=1,bs-1),              &
                     (tc2n(1,i),tc2n(5,i),tc2n(6,i),                           &
                      tc2n(2,i),tc2n(3,i),tc2n(7,i),                           &
                      tc2n(8,i),tc2n(4,i),i=bs,be)
                end if
             else
                backspace(unit_grid)
                if (isegment == 1) then
                   read(unit_grid)                                             &
                     (rdummy3,i=1,grid%nnodesg),                               &
                     ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface), &
                     (idummy,i=1,ntface+nqface),                               &
                     (idummy,i=1,ntet*4), (idummy,i=1,npyr*5),                 &
                     (idummy,i=1,nprz*6),                                      &
                     (tc2n(1,i),tc2n(5,i), tc2n(6,i),tc2n(2,i),                &
                      tc2n(3,i),tc2n(7,i), tc2n(8,i),tc2n(4,i),i=bs,be)
                else
                   read(unit_grid)                                             &
                     (rdummy3,i=1,grid%nnodesg),                               &
                     ((idummy,j=1,3),i=1,ntface), ((idummy,j=1,4),i=1,nqface), &
                     (idummy,i=1,ntface+nqface),                               &
                     (idummy,i=1,ntet*4), (idummy,i=1,npyr*5),                 &
                     (idummy,i=1,nprz*6),                                      &
                     ((idummy,j=1,isize),i=1,bs-1),                            &
                     (tc2n(1,i),tc2n(5,i), tc2n(6,i),tc2n(2,i),                &
                      tc2n(3,i),tc2n(7,i), tc2n(8,i),tc2n(4,i),i=bs,be)
                end if
             end if
           ! do i = bs,be
           !    write(5000+lmpi_id,'(1x,i0,":",1x,8(i0,1x))')                  &
           !      ntet+npyr+nprz+i,tc2n(:,i)
           ! end do
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,bs,be,tc2n,my_nn,goff)
             deallocate(tc2n)
         else
             call distribute_mix(                                              &
                  isegment,nsegment,bs,be,grid,isize,1,1,tc2n,my_nn,goff)
         end if ! master
         bs = be + 1
       end do
       if (.not.lmpi_master) deallocate(tc2n)
    end if

    pp_csize(lmpi_id) = sum(grid%elem(1:grid%nelem)%ncell)

    goff = grid%elem(1)%ncellg
    do j = 2,grid%nelem
       do i = 1,grid%elem(j)%ncell
          grid%elem(j)%cl2g(i) = grid%elem(j)%cl2g(i) - goff
       end do
       goff = goff + grid%elem(j)%ncellg
    end do

    !do j = 1,grid%nelem
    !  write(*,*) 'NCELL ',lmpi_id,j,grid%elem(j)%ncell
    !  do i = 1,grid%elem(j)%ncell
    !     write(20000+j*1000+lmpi_id,'(1x,i0,":",1x,8(i0,1x))')                &
    !           grid%elem(j)%cl2g(i),grid%elem(j)%c2n(:,i)
    !  end do
    !end do

    if (.false.) rdummy3 = rdummy3

  end subroutine puns3d_read_aflr3_c2n

!============================== READ_AFLR3_SIO ===============================80
!
! Reads the c2n information from AFLR3 Format Meshes, evenly distributing
! the cells among the PEs.
!
!=============================================================================80

  subroutine puns3d_read_aflr3_c2n_sio(flow_dir,grid,twod_mode,format)

    use grid_types,        only : grid_type
    use element_defs,      only : type_tet, type_pyr, type_prz, type_hex,      &
                                  initialize_elem
    use system_extensions, only : se_open, se_open_big
    use info_depr,         only : twod
    use file_utils,        only : big_endian_io, se_open_big_is_big

    character(*),    intent(in)    :: flow_dir
    type(grid_type), intent(inout) :: grid
    logical,         intent(in)    :: twod_mode
    character(*),    intent(in)    :: format

    integer :: ntet, npyr, nprz, nhex, ntface, nqface, osize
    integer :: i, j, idummy, my_nn, iostat, ipe, ierr, isize, dim1
    integer(system_i8) :: ntet_i8, npyr_i8, nprz_i8, nhex_i8, ncellg_i8

!beginNeverComplex
    real(dp), dimension(:), allocatable :: xtemp,ytemp,ztemp,xyztemp
!endNeverComplex

    real(dp), dimension(:), allocatable :: xyz

    continue

    if (verbose2) then
       if (.not.lmpi_master) verbose2 = .false.
    end if

    if ( twod_mode ) twod = .true.
!   Only processor 0 reads the data (skip upto c2n)

    iostat = 0
    if (lmpi_master) then
      gridfilename = trim(flow_dir) // trim(grid%project)
      if ( big_endian_io() ) then
        gridfilename = trim(gridfilename) // '.b8.ugrid'
      else
        gridfilename = trim(gridfilename) // '.lb8.ugrid'
      end if
      write(*,*) 'Preparing to read binary AFLR3 grid: '//trim(gridfilename)
      call se_open(unit_grid,file=gridfilename,form='unformatted',             &
                   access='stream',status='old',iostat=iostat)
      if ( iostat /= 0 .and. &
        .not. big_endian_io() .and. se_open_big_is_big() ) then
        write(*,*)'error opening ',trim(gridfilename)
        gridfilename = trim(flow_dir) // trim(grid%project) // '.b8.ugrid'
        write (*,*) 'Preparing to read binary AFLR3 grid: ', &
          trim(gridfilename)
        call se_open_big(unit_grid,file=gridfilename,form='unformatted',    &
          access='stream',status='old',convert='big_endian',iostat=iostat)
      end if
      if ( iostat /= 0 ) write(*,*) 'FATAL: unable to read file.'
    end if
    call lmpi_conditional_stop(iostat)

    if (lmpi_master) then
       read (unit_grid) grid%nnodesg,ntface,nqface
       write(*,*)
       write(*,'(''  nnodes              '',i0)')  grid%nnodesg
       write(*,'(''  ntface,nqface       '',2(i0,1x))') ntface,nqface
       if (trim(format) == 'stream') then
          read (unit_grid) ntet,npyr,nprz,nhex
          ntet_i8 = ntet
          npyr_i8 = npyr
          nprz_i8 = nprz
          nhex_i8 = nhex
          write(*,'(''  ntet,npyr,nprz,nhex '',4(i0,1x))')                     &
            ntet_i8, npyr_i8, nprz_i8, nhex_i8
       else ! stream64 64-bit cell counts
          write(*,*) 'Reading cell counts using 64-bit integers.'
          read (unit_grid) ntet_i8,npyr_i8,nprz_i8,nhex_i8
          write(*,'(''  ntet_i8,npyr_i8,nprz_i8,nhex_i8 '',4(i0,1x))')         &
            ntet_i8, npyr_i8, nprz_i8, nhex_i8
       end if
       write(*,*)
    end if
    call lmpi_bcast(grid%nnodesg)
    call lmpi_bcast(ntet_i8)
    call lmpi_bcast(npyr_i8)
    call lmpi_bcast(nprz_i8)
    call lmpi_bcast(nhex_i8)
    call lmpi_bcast(ntface)
    call lmpi_bcast(nqface)
    grid%ncellg = ntet_i8+npyr_i8+nprz_i8+nhex_i8

    if ((ntet_i8 < 0).or.(npyr_i8 < 0).or.(nprz_i8 < 0).or.(nhex_i8 < 0))then
        if (lmpi_master) write(*,*) "Negative cell counts. tet,pyr,prz,hex",   &
           ntet_i8, npyr_i8, nprz_i8, nhex_i8
       call lmpi_conditional_stop(1,"Error reading cell counts.")
    end if

    ncellg_i8 = ntet_i8+npyr_i8+nprz_i8+nhex_i8
    if (verbose2) write(*,*)"ncellg_i8 ",ncellg_i8

    call distribute_fast_c2n(grid%nnodesg)

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%nnodes01 = grid%nnodes0

    allocate(grid%x(grid%nnodes0));   grid%x = 0.0_dp
    allocate(grid%y(grid%nnodes0));   grid%y = 0.0_dp
    allocate(grid%z(grid%nnodes0));   grid%z = 0.0_dp

    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

!  Read xyz

   if (lmpi_master) then
      if (verbose2) write(*,*)'... Reading xyz '

      isize = pp_nsize(0)
      allocate(xtemp(isize))
      allocate(ytemp(isize))
      allocate(ztemp(isize))
      read (unit_grid) (xtemp(i),ytemp(i),ztemp(i),i=1,isize)
      grid%x(1:isize) = xtemp(1:isize)
      grid%y(1:isize) = ytemp(1:isize)
      grid%z(1:isize) = ztemp(1:isize)
      deallocate(xtemp,ytemp,ztemp)

      osize = 0
      do ipe = 1,lmpi_nproc-1
         !if (verbose2) write(*,*)"ipe ",ipe,isize
         isize = pp_nsize(ipe)
         if (osize /= isize) then
            if (osize > 0) deallocate(xyz)
            osize = isize
            allocate(xyz(isize*3))
         end if
         xyz = 0.0_dp
         dim1 = size(xyz,1)
         allocate(xyztemp(dim1)); xyztemp = 0.0_dp
         read (unit_grid) xyztemp ! (x(i),y(i),z(i),i=1,isize)
         xyz = xyztemp
         deallocate(xyztemp)
         call lmpi_send(xyz,isize*3,ipe,ipe*100,ierr)
      end do
      !if (lmpi_master) call se_wall_time("Reading xyz--end")
   else
      isize = pp_nsize(lmpi_id)
      allocate(xyz(isize*3)); xyz = 0.0_dp
      call lmpi_recv(xyz,isize*3,0,lmpi_id*100,ierr)
      j = 1
      do i = 1,isize
         grid%x(i) = xyz(j)
         grid%y(i) = xyz(j+1)
         grid%z(i) = xyz(j+2)
         j = j + 3
      end do
   end if
   if (allocated(xyz)) deallocate(xyz)

! Skip faces

    if (lmpi_master) then
       if (verbose2) write(*,*)'... Reading faces'
       if (ntface > 0) read(unit_grid) ((idummy, i=1,3), j=1,ntface)
       if (nqface > 0) read(unit_grid) ((idummy, i=1,4), j=1,nqface)
       read (unit_grid) (idummy, i=1,ntface+nqface)
       if (.false.) idummy = idummy + 1
    end if

! Read c2n

    grid%nelem = 0
    if (ntet_i8 > 0) grid%nelem = grid%nelem + 1
    if (npyr_i8 > 0) grid%nelem = grid%nelem + 1
    if (nprz_i8 > 0) grid%nelem = grid%nelem + 1
    if (nhex_i8 > 0) grid%nelem = grid%nelem + 1

    allocate(grid%elem(grid%nelem))

    my_nn = 0
    if (ntet_i8 > 0) then
       if (verbose2) write(*,*)'... Reading c2n--tets'
       my_nn = my_nn + 1
       grid%elem(my_nn)%ncellg = ntet_i8
       call initialize_elem(grid%elem(my_nn), type_tet)
       call distribute_mix_sio(grid,my_nn)
    end if

    if (npyr_i8 > 0) then
       if (verbose2) write(*,*)'... Reading c2n--pyr'
       my_nn = my_nn + 1
       grid%elem(my_nn)%ncellg = npyr_i8
       call initialize_elem(grid%elem(my_nn), type_pyr)
       call distribute_mix_sio(grid,my_nn)
    end if

    if (nprz_i8 > 0) then
       if (verbose2) write(*,*)'... Reading c2n--prz'
       my_nn = my_nn + 1
       grid%elem(my_nn)%ncellg = nprz_i8
       call initialize_elem(grid%elem(my_nn), type_prz)
       call distribute_mix_sio(grid,my_nn)
    end if

    if (nhex_i8 > 0) then
       if (verbose2) write(*,*)'... Reading c2n--hex'
       my_nn = my_nn + 1
       grid%elem(my_nn)%ncellg = nhex_i8
       call initialize_elem(grid%elem(my_nn), type_hex)
       call distribute_mix_sio(grid,my_nn)
    end if

    pp_csize(lmpi_id) = sum(grid%elem(1:grid%nelem)%ncell)

    if (lmpi_master) close(unit_grid)

    if (verbose2) write(*,*)'... FINISH Reading'

  ! do ielem = 1,grid%nelem
  !   write(2000+lmpi_id,*)'NCELL ',lmpi_id,ielem,                             &
  !     grid%elem(ielem)%node_per_cell,grid%elem(ielem)%ncell
  !   write(2000+lmpi_id,*)'pp_ch,ct,cs',                                      &
  !     pp_chead(lmpi_id),pp_ctail(lmpi_id),pp_csize(lmpi_id)
  !   do i = 1,grid%elem(ielem)%ncell
  !      write(2000+lmpi_id,'(1x,i0,1x,i0,":",1x,8(i0,1x))')                   &
  !            grid%elem(ielem)%cl2g(i),k,grid%elem(ielem)%c2n(1:k,i)
  !   end do
  ! end do

  end subroutine puns3d_read_aflr3_c2n_sio

!========================== DISTRIBUTE_MIX_SIO ===============================80
!
! Distribute c2n (mix) from a stream io
!
!=============================================================================80

  subroutine distribute_mix_sio(grid,ielem)

    use grid_types, only : grid_type

    type(grid_type),    intent(inout) :: grid
    integer,            intent(in)    :: ielem

    integer :: i,ifound, isize, node, run_cell, est_size, save_c2n(8)
    integer :: inode, jcell, nhead, ntail, dim1, old_ncell, c_incr, ki, kloop

    integer(system_i8) :: ihead_i8, itail_i8, i8, ggoff_i8

    integer, dimension(:),   allocatable :: tag, old_cl2g
    integer, dimension(:,:), allocatable :: old_c2n, tc2n

    integer, parameter :: nslice = buffer_limit/16 ! 4*4 Bytes
   !integer, parameter :: nslice = 500000 ! Test buffering

    continue

    verbose2 = ((grid%elem(ielem)%ncellg > 300000000).and.lmpi_master)
    verbose2 = .false. ! Disable, but leave infrastructure in place
    ggoff_i8 = 0
    dim1     = grid%elem(ielem)%node_per_cell
    nhead    = pp_nhead(lmpi_id)
    ntail    = pp_ntail(lmpi_id)
    c_incr   = grid%elem(ielem)%ncellg/lmpi_nproc
    est_size = c_incr*2

    if (verbose2) write(*,'("ENTER dist_mix_sio ",5(i0,1x))')                 &
       ielem,dim1,grid%elem(ielem)%ncellg,nslice,c_incr

    allocate(grid%elem(ielem)%c2n(dim1,est_size)); grid%elem(ielem)%c2n  = 0
    allocate(grid%elem(ielem)%cl2g(est_size));     grid%elem(ielem)%cl2g = 0

    run_cell = 0
    grid%elem(ielem)%ncell = 0
    allocate(tag(nslice)); tag = 0

    ki = 0
    kloop = 0
    if (verbose2) then
       kloop = grid%elem(ielem)%ncellg/nslice
       if (kloop > 100) then
          kloop = kloop/50
       else
          kloop = 0
       end if
    end if

    do i8 = 1,grid%elem(ielem)%ncellg,nslice
       ki = ki + 1
       tag = 0
       ihead_i8 = i8
       itail_i8 = (ihead_i8+nslice)-1
       if (itail_i8 > grid%elem(ielem)%ncellg) itail_i8=grid%elem(ielem)%ncellg
       isize = (itail_i8 - ihead_i8)+1
       if (i8 == 1) then
          allocate(tc2n(dim1,isize)); tc2n = 0
       else
          if (isize /= nslice) then
             deallocate(tc2n)
             allocate(tc2n(dim1,isize)); tc2n = 0
          end if
       end if
       if (lmpi_master) then
          if (verbose2.and.(ki==kloop)) then
             write(*,*)"LOOP",ihead_i8,grid%elem(ielem)%ncellg
             ki = 0
          end if
          read(unit_grid) tc2n
       end if
       call lmpi_bcast(tc2n)

       ifound = 0
       do i = 1,isize
          !write(28000+lmpi_id,'(1x,i0,":",1x,4(i0,1x))') i,pc2n(:,i)
          do inode = 1,dim1
             node = tc2n(inode,i)
             if ((node >= nhead).and.(node <= ntail)) then
                ifound = ifound + 1
                tag(ifound) = i
                exit
             end if
          end do
       end do

       if (ifound > 0) then
         !write(*,*)"FOUND ",lmpi_id,ifound,grid%elem(ielem)%ncell,est_size

         if (dim1 == 4) then
            ! no-op
         elseif (dim1 == 5) then
            do i = 1,ifound
               jcell = tag(i)
               save_c2n(1:5) = tc2n(1:5,jcell)
               tc2n(1,jcell) = save_c2n(4)
               tc2n(2,jcell) = save_c2n(5)
               tc2n(3,jcell) = save_c2n(2)
               tc2n(4,jcell) = save_c2n(1)
               tc2n(5,jcell) = save_c2n(3)
            end do
         elseif (dim1 == 6) then
            do i = 1,ifound
               jcell = tag(i)
               save_c2n(1:6) = tc2n(1:6,jcell)
               tc2n(2,jcell) = save_c2n(4)
               tc2n(3,jcell) = save_c2n(5)
               tc2n(4,jcell) = save_c2n(2)
               tc2n(5,jcell) = save_c2n(6)
               tc2n(6,jcell) = save_c2n(3)
            end do
         else
            do i = 1,ifound
               jcell = tag(i)
               save_c2n(1:8) = tc2n(1:8,jcell)
               tc2n(2,jcell) = save_c2n(4)
               tc2n(3,jcell) = save_c2n(5)
               tc2n(4,jcell) = save_c2n(8)
               tc2n(5,jcell) = save_c2n(2)
               tc2n(6,jcell) = save_c2n(3)
               tc2n(7,jcell) = save_c2n(6)
               tc2n(8,jcell) = save_c2n(7)
            end do
         end if

          if ((grid%elem(ielem)%ncell+ifound) > est_size) then
            !write(*,*)"REalloc ",est_size,c_incr,grid%elem(ielem)%ncell+ifound
             est_size = max(est_size + c_incr,                                 &
                        grid%elem(ielem)%ncell+ifound+c_incr)

             if (grid%elem(ielem)%ncell > 0) then
               !write(*,*)"MOV1 ",est_size,c_incr,grid%elem(ielem)%ncell+ifound
                old_ncell = grid%elem(ielem)%ncell

                allocate(old_c2n(dim1,old_ncell))
                old_c2n(1:dim1,1:old_ncell) =                                  &
                  grid%elem(ielem)%c2n(1:dim1,1:old_ncell)
                deallocate(grid%elem(ielem)%c2n); nullify(grid%elem(ielem)%c2n)

                allocate(grid%elem(ielem)%c2n(dim1,est_size))
                grid%elem(ielem)%c2n = 0
                grid%elem(ielem)%c2n(1:dim1,1:old_ncell) =                     &
                  old_c2n(1:dim1,1:old_ncell)
                deallocate(old_c2n)

                allocate(old_cl2g(old_ncell))
                old_cl2g(1:old_ncell) = grid%elem(ielem)%cl2g(1:old_ncell)
                deallocate(grid%elem(ielem)%cl2g);nullify(grid%elem(ielem)%cl2g)
                allocate(grid%elem(ielem)%cl2g(est_size))
                grid%elem(ielem)%cl2g = 0
                grid%elem(ielem)%cl2g(1:old_ncell) = old_cl2g(1:old_ncell)
                deallocate(old_cl2g)
             else ! need more space, but no values to save
               !write(*,*)"MOV2 ",est_size,c_incr,grid%elem(ielem)%ncell+ifound
                deallocate(grid%elem(ielem)%c2n); nullify(grid%elem(ielem)%c2n)
                deallocate(grid%elem(ielem)%cl2g);nullify(grid%elem(ielem)%cl2g)
                allocate(grid%elem(ielem)%c2n(dim1,est_size))
                grid%elem(ielem)%c2n = 0
                allocate(grid%elem(ielem)%cl2g(est_size))
                grid%elem(ielem)%cl2g = 0
             end if
          end if
          ! Now assign found values
          grid%elem(ielem)%ncell = grid%elem(ielem)%ncell + ifound
          do i = 1,ifound
             run_cell = run_cell + 1
             jcell = tag(i)
             grid%elem(ielem)%cl2g(run_cell) = ggoff_i8 + jcell
             grid%elem(ielem)%c2n(1:dim1,run_cell) = tc2n(1:dim1,jcell)
          end do
       end if
       ggoff_i8 = ggoff_i8 + isize
    end do

    ! Compact data
    if (grid%elem(ielem)%ncell > 0) then
       if (verbose2) write(*,*)"COMPACT",lmpi_id,grid%elem(ielem)%ncell,est_size
       old_ncell = grid%elem(ielem)%ncell
       allocate(old_c2n(dim1,old_ncell))
       old_c2n(1:dim1,1:old_ncell) = grid%elem(ielem)%c2n(1:dim1,1:old_ncell)
       deallocate(grid%elem(ielem)%c2n); nullify(grid%elem(ielem)%c2n)
       allocate(grid%elem(ielem)%c2n(dim1,old_ncell))
       grid%elem(ielem)%c2n = 0
       grid%elem(ielem)%c2n(1:dim1,1:old_ncell) = old_c2n(1:dim1,1:old_ncell)
       deallocate(old_c2n)

       allocate(old_cl2g(old_ncell))
       old_cl2g(1:old_ncell) = grid%elem(ielem)%cl2g(1:old_ncell)
       deallocate(grid%elem(ielem)%cl2g); nullify(grid%elem(ielem)%cl2g)
       allocate(grid%elem(ielem)%cl2g(old_ncell)); grid%elem(ielem)%cl2g = 0
       grid%elem(ielem)%cl2g(1:old_ncell) = old_cl2g(1:old_ncell)
       deallocate(old_cl2g)
    else ! allocate a minimum of 1 (but ncell == 0)
       deallocate(grid%elem(ielem)%c2n); nullify(grid%elem(ielem)%c2n)
       allocate(grid%elem(ielem)%c2n(dim1,1)); grid%elem(ielem)%c2n = 0
       deallocate(grid%elem(ielem)%cl2g); nullify(grid%elem(ielem)%cl2g)
       allocate(grid%elem(ielem)%cl2g(1));     grid%elem(ielem)%cl2g = 0
    end if

    deallocate(tag)
    if (allocated(tc2n)) deallocate(tc2n)
    if (verbose2) write(*,*) "EXIT dist_mix_sio "

  end subroutine distribute_mix_sio

!=============================== PUNS3D_READ_AFLR3_BC ========================80
!
! Reads bc from AFLR3 Format Meshes -- into public module variables:
!       faceptr, facetag
!
!=============================================================================80

  subroutine puns3d_read_aflr3_bc( flow_dir, format, grid )

    use grid_types, only : grid_type
    use file_utils, only : big_endian_io, se_open_big_is_big
    use system_extensions, only : se_open, se_open_big

    character(*),    intent(in) :: flow_dir
    character(*),    intent(in) :: format
    type(grid_type), intent(in) :: grid

    integer            :: i,n, iostat, nnodesg, idum
    integer(system_i8) :: idum_i8
!beginNeverComplex
    real(dp) :: rdummy3(3)
!endNeverComplex

    continue

    master_open_and_read_header : if (lmpi_id == 0) then

       gridfilename = trim(flow_dir) // trim(grid%project)
       if ( trim(format) == 'ascii' ) then
          gridfilename = trim(gridfilename) // '.ugrid'
          call se_open(unit_grid,file=gridfilename,status='old',iostat=iostat)
       else if ( trim(format) == 'unformatted' ) then
         if ( big_endian_io() ) then
           gridfilename = trim(gridfilename) // '.r8.ugrid'
         else
           gridfilename = trim(gridfilename) // '.lr8.ugrid'
         end if
         if (verbose) write(*,*) ' Reading unformatted AFLR3 grid: ' //        &
                                 trim(gridfilename)
         call se_open(unit_grid,file=gridfilename,form='unformatted',          &
              status='old',iostat=iostat)
         if ( iostat /= 0 .and. &
           .not. big_endian_io() .and. se_open_big_is_big() ) then
           if (verbose) write(*,*)'error opening ',trim(gridfilename)
           gridfilename = trim(flow_dir) // trim(grid%project) // '.r8.ugrid'
           if (verbose)                                                &
             write (*,*) ' Preparing to read unformatted AFLR3 grid ', &
             trim(gridfilename)
           call se_open_big(unit_grid,file=gridfilename,form='unformatted',    &
             status='old',convert='big_endian',iostat=iostat)
         end if
       else if ((trim(format)=='stream').or.(trim(format)=='stream64')) then
          if ( big_endian_io() ) then
            gridfilename = trim(gridfilename) // '.b8.ugrid'
          else
            gridfilename = trim(gridfilename) // '.lb8.ugrid'
          end if
          if (verbose) write(*,*) '  Reading binary AFLR3 ugrid: ' //          &
                                  trim(gridfilename)
          call se_open(unit_grid,file=gridfilename,form='unformatted',         &
                       access='stream',status='old',iostat=iostat)
          if ( iostat /= 0 .and. &
            .not. big_endian_io() .and. se_open_big_is_big() ) then
            if (verbose) write(*,*)'error opening ',trim(gridfilename)
            gridfilename = trim(flow_dir) // trim(grid%project) // '.b8.ugrid'
            if (verbose) write (*,*) 'Preparing to read binary AFLR3 grid: ', &
              trim(gridfilename)
            call se_open_big(unit_grid,file=gridfilename,form='unformatted',  &
              access='stream',status='old',convert='big_endian',iostat=iostat)
          end if
       else
          write(*,*)'Unsupported file format ',trim(format),' stopping...'
          call lmpi_conditional_stop(1)
       end if

       if ( iostat /= 0 ) then
          write(*,*)'error opening ',trim(gridfilename),' stopping...'
          call lmpi_conditional_stop(1,'second ugrid read')
       end if

       rewind (unit=unit_grid)

       if ( trim(format) == 'ascii') then
         read(unit_grid,*) nnodesg,ntface,nqface,idum,idum,idum,idum
       elseif ((trim(format)=='stream').or.(trim(format)=='unformatted')) then
         read(unit_grid) nnodesg,ntface,nqface,idum,idum,idum,idum
       elseif ( trim(format) == 'stream64') then
         read(unit_grid) nnodesg,ntface,nqface,idum_i8,idum_i8,idum_i8,idum_i8
       endif
       if (.false.) idum = idum + idum_i8 ! suppress warnings

       !   read node coordinates

       if ( trim(format) == 'ascii' ) then ! (x(n),z(n),y(n),n=1,nnodesg)
          read(unit_grid,*) (rdummy3,n=1,nnodesg)
       end if
       if (.false.) write(*,*) rdummy3 ! avoid compiler warnings

    end if master_open_and_read_header
    call lmpi_conditional_stop(0,'second ugrid read')

    call lmpi_bcast(ntface)
    call lmpi_bcast(nqface)

!   allocate space for faceptr and facetag arrays

    if (allocated(faceptr)) deallocate(faceptr)
    if (allocated(facetag)) deallocate(facetag)
    allocate(faceptr(ntface+nqface,4)); faceptr = 0
    allocate(facetag(ntface+nqface));   facetag = 0

!   read faceptr (face to node) arrays

    if (lmpi_id == 0) then

       if ( trim(format) == 'ascii' ) then
          if(ntface>0) read(unit_grid,*)((faceptr(n,i), i=1,3), n=1,ntface)
          if(nqface>0) read(unit_grid,*)((faceptr(n+ntface,i),i=1,4),n=1,nqface)
          read(unit_grid,*) (facetag(n), n=1,ntface+nqface)
       else
          read(unit_grid)                                             &
            (rdummy3,i=1,grid%nnodesg),                               &
            ((faceptr(n,i), i=1,3), n=1,ntface),                      &
            ((faceptr(n+ntface,i), i=1,4), n=1,nqface),               &
            (facetag(n), n=1,ntface+nqface)
       endif

       close(unit_grid)

    end if ! lmpi_id == 0

    !write(*,*)'ntface,nqface = ',lmpi_id,ntface,nqface

    call lmpi_bcast(faceptr)
    call lmpi_bcast(facetag)

    if (verbose) print *, 'read aflr3 boundary conditions complete'

  end subroutine puns3d_read_aflr3_bc

!============================== puns3d_read_fieldview_c2n ====================80
!
! For pass == 1, read the Fieldview Format Meshes, evenly distribute the XYZs
! among the PEs; then bcast and collect the corresponding c2n information
! based on the nodes present.
!
! For pass = 2, read the bc.
!
!=============================================================================80

  subroutine puns3d_read_fieldview_c2n(pass,flow_dir,grid,raw_grid_data)

    use grid_types,        only : grid_type, raw_grid_data_type
    use system_extensions, only : se_open
    use info_depr,         only : twod

    integer,                  intent(in)    :: pass
    character(*),             intent(in)    :: flow_dir
    type(grid_type),          intent(inout) :: grid
    type(raw_grid_data_type), intent(in)    :: raw_grid_data

! local

    integer :: ierr, iostat, fv_precision
    logical :: istheremapbc

    continue

!   Check if mapbc file exists; if so, process it.
!   Else within the fieldview bc processing routine, open a
!   new file, write bc information, inform the user, and stop.

    ierr = 0
    if (raw_grid_data%fieldview_coordinate_precision == 'double') then
       fv_precision = 1
    else
       fv_precision = 0
    end if

    if (raw_grid_data%twod_mode) twod = .true.

    if (pass == 2) then
       if (lmpi_master) then
          mapbcfilename = trim(flow_dir) // trim(grid%project) // '.mapbc'
          inquire (file=mapbcfilename,exist=istheremapbc)
       end if
       call lmpi_bcast(istheremapbc)
    end if

    if ( skeleton > 0 ) write(*,*) ' ...ifieldview: format =',                 &
                                   raw_grid_data%data_format

    ascii_or_not : if (trim(raw_grid_data%data_format) == 'ascii') then

!     read a fieldview formatted grid

      if (lmpi_master) then
         gridfilename = trim(flow_dir) // trim(grid%project) // '.fvgrid_fmt'
         if (verbose) write (*,*) 'Opening ',gridfilename
         call se_open(unit_grid,file=gridfilename,                             &
               status='old',iostat=iostat)
         if ( iostat /= 0 ) then
           write(*,*)'error opening ',trim(gridfilename),' stopping...'
           ierr = 1
         end if
      end if
      call lmpi_conditional_stop(ierr)

      if (pass == 1) then
         call fieldv_fmt_par(unit_grid,grid)
      else
         call fieldv_fmt_bc_par(istheremapbc,unit_grid)
      end if

    else ascii_or_not

!     query to find out if file contains single or double-precision x,y,z data

      if ((pass==1).and.(lmpi_master)) then
         write(*,*)
        !write(*,'(2a)') ' Does the file contain single-precsion or double-',  &
        !                'precision coordinates?'
        !write(*,'(2a)') ' Hint: if the FieldView viewer can read it ',        &
        !                'then it is probably single precsion'
        !write(*,'(2a)') ' Enter 0 for single or 1 for double'
        !read(*,*) fv_precision
        !write(*,*)'fv_precision ',fv_precision
         if (fv_precision == 0) then
            write(*,'(a)') ' Using single-precision.'
         else
            write(*,'(a)') ' Using double-precision.'
         end if
      end if

!     read a fieldview unformatted grid

      if (lmpi_master) then
         gridfilename = trim(flow_dir)//trim(grid%project) // '.fvgrid_unf'
         if (verbose) write (*,*) 'Opening ',gridfilename
         call se_open(unit_grid,file=gridfilename,form='unformatted',          &
               status='old',iostat=iostat)
         if ( iostat /= 0 ) then
           write(*,*)'error opening ',trim(gridfilename),' stopping...'
           ierr = 1
         endif
      end if
      call lmpi_conditional_stop(ierr)

      if (pass == 1) then
         call fieldv_unf_par(unit_grid,grid,fv_precision)
      else
         call fieldv_unf_bc_par(istheremapbc,unit_grid,fv_precision)
      end if

    end if ascii_or_not

    if (lmpi_master) close(unit_grid)

  end subroutine puns3d_read_fieldview_c2n

!=================================== FIELDV_FMT_PAR ==========================80
!
! Reads a formatted, mixed-element, unstructured grid in FieldView Grid format
!
! NOTE: version 3_0 can in theory have comment lines embedded anywhere; we
! only check for comments immediately after line 2
!
!=============================================================================80

  subroutine fieldv_fmt_par(unit_grid,grid)

    use grid_types,   only : grid_type
    use element_defs, only : node_per_tet, node_per_hex,                       &
                             node_per_prz, node_per_pyr
    use string_utils, only : downcase

    integer,         intent(in)    :: unit_grid
    type(grid_type), intent(inout) :: grid

! local

    integer :: nn, nv, ngrids,nbnd_total, nvars, nbvars, idum
    integer :: i,n,nnodesg, nnodes, ipe, ierr, is, total_ncell, ncell
    integer :: fv_ver_major, fv_ver_minor, fv_file_flag

    integer, dimension(8) :: idumv
    integer, dimension(:),   allocatable :: nn_arr
    integer, dimension(:,:), allocatable :: icell

    real(dp) :: fdum
    real(dp), dimension(:), allocatable :: tempx, tempy, tempz

    integer               :: nface_grand_total

!   f2n_total corresponds to faceptr in puns3d_io but with
!   dimensions of total # of boundary faces  * 4

    character(len=80) :: txt
    character(len=1)  :: string_1
    character(len=5)  :: str_major, str_minor

    logical :: end_of_elements
    integer :: iostat

    integer, parameter :: max_slice = 5000000
    integer, dimension(4), parameter :: node_per_cell =                        &
             (/ node_per_tet, node_per_hex, node_per_prz, node_per_pyr /)

    continue

    ierr = 0
    if (lmpi_master) then

       rewind (unit_grid)

       read (unit_grid,*) txt, fv_ver_major, fv_ver_minor

       write(str_major,'(i0)') fv_ver_major
       write(str_minor,'(i0)') fv_ver_minor

       write (*,*)
       write (*,'(5a)')'Reading FIELDVIEW grid formatted (v',                  &
                 trim(adjustl(str_major)),'.', trim(adjustl(str_minor)),') '
       write (*,*)

       if (trim(adjustl(txt)) == 'FIELDVIEW') then
         fv_file_flag = 3
       else if (trim(adjustl(txt)) == 'FIELDVIEW_Grids') then
         fv_file_flag = 1
       else
         fv_file_flag = 2
       end if

!      skip any comment lines

       do
         read(unit_grid,*) string_1
         if (string_1 /= '!') exit
       end do
       backspace(unit_grid)

!      if older format or combined format, read flow constants

       if (fv_ver_major==2 .or. (fv_ver_major==3 .and. fv_file_flag==3)) then
         read(unit_grid,*) ! txt Constants
         read(unit_grid,*) (fdum,i=1,4) ! time,xmach,alpha,Re
         fdum = fdum   ! to avoid compiler warnings
       end if

!      number of grids

       if (fv_ver_major == 2 .and. fv_ver_minor == 4) then
         read(unit_grid,*)
         read(unit_grid,*) ngrids
       else
         read(unit_grid,*)  txt, ngrids
       end if

!      error (for fun3d) if more than one grid

       if (ngrids /= 1) then
         write(*,'(a,i0)') 'Error: ngrids = ', ngrids
         write(*,'(a)') 'FUN3D accepts only ngrids = 1'
         call lmpi_conditional_stop(1)
       end if

!      number of boundaries

       if (fv_ver_major == 2 .and. fv_ver_minor == 4) then
         read(unit_grid,*)
         read(unit_grid,*) nbnd_total
       else
         read(unit_grid,*) txt, txt, nbnd_total
       end if

!      read boundary table data

       do n=1,nbnd_total
         read(unit_grid,*)
       end do

!      number of volume (flow) variables in this file

       if (fv_ver_major == 2) then
         if (fv_ver_minor == 4) then
           read(unit_grid,*) txt
           read(unit_grid,*) nvars
         else
           read(unit_grid,*)  txt, txt, nvars ! Variable Names 4
         end if
!        read list of volume variables, but fun3d doesn't use them
         do nv=1,nvars
           read(unit_grid,*) ! txt
         end do
       end if

       if (fv_ver_major == 2 .and. fv_ver_minor == 5) then
!        number of boundary (flow) variables in this file
         read(unit_grid,*)  txt, txt, txt, nbvars ! Boundary Variable Names 4
!        read list of boundary variables, but fun3d doesn't use them
         do nv=1,nbvars
           read(unit_grid,*) ! txt
         end do
       end if

!      read node dimensions

       read(unit_grid,*) txt, nnodesg
       grid%nnodesg = nnodesg
    end if ! lmpi_master
    call lmpi_conditional_stop(0)

    call lmpi_bcast(grid%nnodesg)

    call distribute_fast_c2n(grid%nnodesg)

!   read node coordinates
!   if (lmpi_master.and.grid%nnodesg>max_slice) write(*,*)'    ... read xyz'

    nnodes = pp_nsize(lmpi_id)
    allocate(grid%x(nnodes)); grid%x = 0.0_dp
    allocate(grid%y(nnodes)); grid%y = 0.0_dp
    allocate(grid%z(nnodes)); grid%z = 0.0_dp

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%nnodes01   = grid%nnodes0

    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

    if (lmpi_master) then
       do i=1,nnodes
          read(unit_grid,*) grid%x(i),grid%y(i),grid%z(i)
       enddo
       if (lmpi_nproc > 1) then
          nnodes = pp_nsize(0)
          allocate(tempx(nnodes))
          allocate(tempy(nnodes))
          allocate(tempz(nnodes))
          do ipe = 1,lmpi_nproc-1
             tempx = 0.0_dp
             tempy = 0.0_dp
             tempz = 0.0_dp
             nnodes = pp_nsize(ipe)
             do i = 1,nnodes
                read(unit_grid,*) tempx(i),tempy(i),tempz(i)
             end do
             call lmpi_send(tempx,nnodes,ipe,ipe*100,ierr)
             call lmpi_send(tempy,nnodes,ipe,ipe*100,ierr)
             call lmpi_send(tempz,nnodes,ipe,ipe*100,ierr)
          end do
          deallocate(tempx,tempy,tempz)
       end if
    else
       call lmpi_recv(grid%x,nnodes,0,lmpi_id*100,ierr)
       call lmpi_recv(grid%y,nnodes,0,lmpi_id*100,ierr)
       call lmpi_recv(grid%z,nnodes,0,lmpi_id*100,ierr)
    end if
    call lmpi_conditional_stop(ierr)

    if (lmpi_master) then
       read(unit_grid,*) txt, txt, nface_grand_total
       do nv=1,nface_grand_total
         read(unit_grid,*) nn, idum, (idumv(n),n=1,idum)
       enddo
       if (.false.) write(*,*) nn,idum,idumv(1)  ! to avoid compiler warnings
!   read elements, number of tet, hex, prism, pyramid
       read(unit_grid,*) txt
    end if

!   read the elements
!   if(lmpi_master.and.grid%nnodesg>max_slice)write(*,*)'    ... read elements'

    allocate(nn_arr(max_slice));  nn_arr = 0
    allocate(icell(8,max_slice)); icell  = 0
    total_ncell = 0
    read_element : do
       ncell  = 0
       nn_arr = 0
       icell  = 0
       if (lmpi_master) then
         do is  = 1,max_slice
            ncell = ncell + 1

            read(unit_grid,*,iostat=iostat)                                    &
              nn_arr(is),idum,(icell(i,ncell),i=1,node_per_cell(nn_arr(is)))

            if (iostat < 0) then
              end_of_elements = .true.
              exit
            else if (iostat > 0) then
              write(*,*)'   ... testing ending text.'
              backspace(unit_grid)
              read(unit_grid,*,err=2003,end=2003) txt
              txt = downcase(txt)
              if (trim(txt) /= 'variables') then
                 write(*,*)'Invalid file format. After "Elements ' //          &
                           'section", either end of file, or "variables" ' //  &
                           'delimiters expected.'
                 write(*,*)'Text = "',trim(txt),'".'
                 call lmpi_conditional_stop(1)
              end if
              end_of_elements = .true.
              exit
            else
              end_of_elements = .false.
            end if
         end do
       end if

       call lmpi_bcast(end_of_elements)

       if (lmpi_master .and. end_of_elements) ncell = ncell-1

       call fv_process_elements(max_slice,ncell,nn_arr,icell,total_ncell)

       if (end_of_elements) exit ! Done
    end do read_element

    deallocate(nn_arr)
    deallocate(icell)
    call fv_finalize_elements(grid)
    grid%ncellg = total_ncell

    if (total_ncell == 0) then
       write(*,*)'  stopping...error reading fieldview grid...',               &
                 ' no element types detected'
       call lmpi_conditional_stop(1)
    end if
    call lmpi_conditional_stop(0)

    return

2003 continue ! error
    call lmpi_conditional_stop(1)

  end subroutine fieldv_fmt_par

!=================================== FIELDV_UNF_PAR ==========================80
!
! Reads an unformatted, mixed-element, unstructured grid in FieldView format
!
! NOTE: version 3_0 can in theory have comment lines embedded anywhere; we
! only check for comments immediately after line 2
!
!=============================================================================80

  subroutine fieldv_unf_par(unit_grid,grid,fv_precision)

    use grid_types,   only : grid_type
    use element_defs, only : node_per_tet, node_per_hex,                       &
                             node_per_prz

    integer,         intent(in)    :: unit_grid
    type(grid_type), intent(inout) :: grid
    integer,         intent(in)    :: fv_precision

! local

    integer :: nn, nv, ngrids,nbnd_total, nvars, nbvars, idum, jdum
    integer :: i,n, nnodes, ipe, ierr, is, total_ncell, ncell
    integer :: fv_ver_major, fv_ver_minor, fv_file_flag
    integer :: surface_results_flag, clockness_flag, fv_magic
    integer :: fv_nodes, fv_faces, fv_elements, bndry_type
    integer :: offset, offset1, ie, in
    integer :: ntet, nhex, nprz, npyr, max_slice
    integer :: iostat

    integer, dimension(:),   allocatable :: nn_arr, header
    integer, dimension(:,:), allocatable :: icell

    real(r4) :: r4_dum
    real(r8) :: r8_dum
    real(r4), dimension(:), allocatable :: x_r4, y_r4, z_r4

    real(dp), dimension(:), allocatable :: tempx, tempy, tempz

!   f2n_total corresponds to faceptr in puns3d_io but with
!   dimensions of total # of boundary faces  * 4

    character(len=80) :: bctype_dum
    character(len=5)  :: str_major, str_minor

    logical :: end_of_elements

    integer, parameter :: mmax_slice = 5000000 ! 5000

    continue

    ierr = 0
    if (lmpi_master) then

       rewind (unit_grid)

       read(unit_grid) fv_magic

       if (fv_magic /= 66051) then
         write(*,'(a)') 'Error reading fieldview magic number '
         call lmpi_conditional_stop(1)
       end if
    end if
    call lmpi_conditional_stop(0)

    if (lmpi_master) then

       read(unit_grid) ! txt FIELDVIEW

!   fieldview version

       read (unit_grid) fv_ver_major, fv_ver_minor

       write(str_major,'(i0)') fv_ver_major
       write(str_minor,'(i0)') fv_ver_minor

       write (*,*)
       write (*,'(6a)')'Reading FIELDVIEW grid unformatted (v',                &
                 trim(adjustl(str_major)),'.', trim(adjustl(str_minor)),') '
       write (*,*)

       fv_file_flag = 3 ! Assume FV_COMBINED_FILE

       if ((fv_ver_major == 2 .and. (fv_ver_minor >= 7)) .or.                  &
           fv_ver_major == 3) then
         backspace(unit_grid)
         read(unit_grid) fv_ver_major, fv_ver_minor, fv_file_flag, idum
         idum = idum ! avoid compiler warning
         if (fv_file_flag /= 1 .and. fv_file_flag /= 3) then
           write(*,'(a)') 'Error reading file: Not a "Grids" or "Combined" file'
           write(*,'(a)') 'FUN3D does not accept "Results" file'
           write(*,'(a,i0)') 'fv_file_flag = ', fv_file_flag
           call lmpi_conditional_stop(1)
         end if
       end if

       if (fv_file_flag >= 2) then
         read(unit_grid) ! time,xmach,alpha,Re
       end if

!      number of grids

       read(unit_grid) ngrids

!      error (for fun3d) if more than one grid

       if (ngrids /= 1) then
         write(*,'(a,i0)') 'Error: ngrids = ', ngrids
         write(*,'(a)') 'FUN3D accepts only ngrids = 1'
         call lmpi_conditional_stop(1)
       end if

    end if
    call lmpi_conditional_stop(0)

    if (lmpi_master) then

!      number of boundaries

       read(unit_grid) nbnd_total

!   allocate space for boundary types

       !call my_alloc_ptr (bctype_total, 80, nbnd_total)

       if (fv_ver_major == 2 .and. fv_ver_minor <= 4) then
         do n=1,nbnd_total
           read(unit_grid) bctype_dum
           if (.false.) then
              bctype_dum = bctype_dum
           end if
         end do
       else
         do n=1,nbnd_total
           read(unit_grid) surface_results_flag, clockness_flag, bctype_dum
                                                             ! bctype_total(n)
           surface_results_flag = surface_results_flag ! avoid compiler warnings
           if (.false.) then
              clockness_flag = clockness_flag
              bctype_dum     = bctype_dum
           end if
         end do
       end if

       nvars = 0
       if (fv_file_flag >= 2) then
   !     number of volume (flow) variables in this file
         read(unit_grid)  nvars
   !     read list of volume variables, but fun3d doesn't use them
         do nv=1,nvars
           read(unit_grid) bctype_dum ! txt
         end do
       end if

       nbvars = 0
       if ((fv_file_flag >= 2) .and.                                           &
           (fv_ver_major == 2 .and. fv_ver_minor > 4)) then
   !     number of boundary (flow) variables in this file
         read(unit_grid)  nbvars
   !     read list of boundary variables, but fun3d doesn't use them
         do nv=1,nbvars
           read(unit_grid) ! txt
         end do
       end if
   !   read node dimensions (fv_nodes is a fv flag of some sort...)

       read(unit_grid)  fv_nodes, grid%nnodesg

    end if ! lmpi_master

    call lmpi_bcast(fv_nodes)
    call lmpi_bcast(grid%nnodesg)

    call distribute_fast_c2n(grid%nnodesg)

!   read node coordinates

    nnodes = pp_nsize(lmpi_id)
    allocate(grid%x(nnodes)); grid%x = 0.0_dp
    allocate(grid%y(nnodes)); grid%y = 0.0_dp
    allocate(grid%z(nnodes)); grid%z = 0.0_dp

    grid%nnodes0 = pp_nsize(lmpi_id)
    grid%nnodes01   = grid%nnodes0

    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,grid%nnodes0
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

    if (lmpi_master) then

       offset = grid%nnodesg - nnodes
       if (fv_precision == 0) then ! single precision; read into temp r4 array
          allocate(x_r4(nnodes))
          allocate(y_r4(nnodes))
          allocate(z_r4(nnodes))

          read(unit_grid)                                                      &
           (x_r4(n),n=1,nnodes), (r4_dum,i=1,offset),                          &
           (y_r4(n),n=1,nnodes), (r4_dum,i=1,offset),                          &
           (z_r4(n),n=1,nnodes)

           do i=1,nnodes
             grid%x(i) = x_r4(i)
             grid%y(i) = y_r4(i)
             grid%z(i) = z_r4(i)
           enddo
           deallocate(x_r4)
           deallocate(y_r4)
           deallocate(z_r4)
       else
          read(unit_grid,err=103)                                              &
           (grid%x(n),n=1,nnodes), (r8_dum,i=1,offset),                        &
           (grid%y(n),n=1,nnodes), (r8_dum,i=1,offset),                        &
           (grid%z(n),n=1,nnodes)
       end if
       if (.false.) write(*,*) r4_dum,r8_dum

       if (lmpi_nproc > 1) then

          allocate(tempx(pp_nsize(0))) ! master IPE has largest slice
          allocate(tempy(pp_nsize(0)))
          allocate(tempz(pp_nsize(0)))
          do ipe = 1,lmpi_nproc-1
             backspace(unit_grid)
             nnodes = pp_nsize(ipe)
             offset = grid%nnodesg - nnodes
             offset1 = pp_ntail(ipe-1)
             tempx = 0.0_dp
             tempy = 0.0_dp
             tempz = 0.0_dp
             nnodes = pp_nsize(ipe)
             if (fv_precision == 0) then
                allocate(x_r4(nnodes))
                allocate(y_r4(nnodes))
                allocate(z_r4(nnodes))
                read(unit_grid)                                                &
                                      (r4_dum,i=1,offset1),                    &
                (x_r4(n),n=1,nnodes), (r4_dum,i=1,offset),                     &
                (y_r4(n),n=1,nnodes), (r4_dum,i=1,offset),                     &
                (z_r4(n),n=1,nnodes)
                do i=1,nnodes
                   tempx(i) = x_r4(i)
                   tempy(i) = y_r4(i)
                   tempz(i) = z_r4(i)
                enddo
                deallocate(x_r4)
                deallocate(y_r4)
                deallocate(z_r4)
             else
                read(unit_grid)                                                &
                                       (r8_dum,i=1,offset1),                   &
                (tempx(n),n=1,nnodes), (r8_dum,i=1,offset),                    &
                (tempy(n),n=1,nnodes), (r8_dum,i=1,offset),                    &
                (tempz(n),n=1,nnodes)
             end if
             call lmpi_send(tempx,nnodes,ipe,ipe*100,ierr)
             call lmpi_send(tempy,nnodes,ipe,ipe*100,ierr)
             call lmpi_send(tempz,nnodes,ipe,ipe*100,ierr)
          end do
          deallocate(tempx,tempy,tempz)
       end if
       ! if (fv_precision == 0) deallocate(x_r4,y_r4,z_r4)
    else
       call lmpi_recv(grid%x,nnodes,0,lmpi_id*100,ierr)
       call lmpi_recv(grid%y,nnodes,0,lmpi_id*100,ierr)
       call lmpi_recv(grid%z,nnodes,0,lmpi_id*100,ierr)
    end if
    call lmpi_conditional_stop(ierr)

! skip boundary face related arrays (read during pass 2)

    if (lmpi_master) then
       read_bnd_faces : do nn=1,nbnd_total

         read(unit_grid) fv_faces, bndry_type, idum ! nface_total(nn)
         if (.false.) bndry_type = bndry_type
         if (fv_faces /= 1002) then
           write(*,'(a)') 'error: fv_faces /= 1002'
           call lmpi_conditional_stop(1)
         end if

         read(unit_grid) (jdum,jdum,jdum,jdum,n=1,idum)
        !read(unit_grid) ( f2n_total(offset+n,1),f2n_total(offset+n,2),        &
        !                  f2n_total(offset+n,3),f2n_total(offset+n,4),        &
        !                  n=1,nface_total(nn) )
       end do read_bnd_faces
    end if
    call lmpi_conditional_stop(0)

    ! Read each sections of standard 3D elements (FV 13 Ref Manual p.369)
    total_ncell = 0
    element_section_loop: do
      if (lmpi_master) then

      !  NOTE: No support in party/fun3d for polyhedral faces or hanging nodes
      !  on the boundary  (which could appear here in FV v3_x files)

         !  read the number of elements of each type for the section

         read(unit_grid, iostat=iostat) fv_elements, ntet, nhex, nprz, npyr
         if (iostat < 0) then
           end_of_elements = .true.
         else
           if (fv_elements == 1004) then
             if( nvars > 0 ) read(unit_grid)
             cycle
           else if (fv_elements == 1006) then
             if( nbvars > 0 ) read(unit_grid)
             cycle
           else if (fv_elements /= 1003) then
             write(*,'(a)') 'error: fv_elements /= 1003'
             write(*,'(a)') 'this could mean this grid has arbitray ',         &
                            'polygonal faces, which are not allowed in FUN3D'
             call lmpi_conditional_stop(1)
           end if

           if (ntet+nhex+nprz+npyr == 0) then
              write(*,*)'  stopping...error reading fieldview grid...',        &
                        ' no element types detected'
              call lmpi_conditional_stop(1)
           end if
           end_of_elements = .false.
         endif
      end if

      call lmpi_conditional_stop(0)

      call lmpi_bcast(end_of_elements)
      if (end_of_elements) exit ! Done

      call lmpi_bcast(fv_elements)
      call lmpi_bcast(ntet)
      call lmpi_bcast(nhex)
      call lmpi_bcast(nprz)
      call lmpi_bcast(npyr)

!     read the elements

      max_slice = min(ntet+nhex+nprz+npyr,mmax_slice)
      allocate(icell(8,mmax_slice)); icell  = 0
      allocate(header(max_slice));  header = 0
      allocate(nn_arr(mmax_slice));  nn_arr = 0

!     if (lmpi_master) then
!       write(1000,*) 'Section ', ntet, nhex, nprz, npyr
!       write(77,*) 'Section ', ntet, nhex, nprz, npyr
!       write(1001,*) 'Section ', ntet, nhex, nprz, npyr
!     endif

      do ncell = 1,nhex+ntet+nprz+npyr,max_slice
         is = ncell
         ie = (is + max_slice) - 1
         if (ie > (ntet+nhex+nprz+npyr)) ie = nhex+ntet+nprz+npyr
         in = (ie - is) + 1

         if (lmpi_master) then
!          write(1000,*)"Nc,is,ie,in ",ncell,is,ie,in
           header = 0
           if (ncell == 1) then
            read(unit_grid)                                                    &
              (header(i),(icell(n,i), n=1,my_npc(header(i))),i=1,in)
           else
            nn_arr = 0
            icell  = 0
            backspace(unit_grid)
            read(unit_grid)                                                    &
              (jdum,     (idum,       n=1,my_npc(jdum)),     i=1,is-1),        &
              (header(i),(icell(n,i), n=1,my_npc(header(i))),i=1,in)
           end if
           do i=1,in ! convert nn_arr from npc to itype
!             write(77,'(i0,1x,i0)'), i+is-1, my_npc(header(i))
              nn_arr(i) = my_npc(header(i))
              if (nn_arr(i) == node_per_tet) then
                 nn_arr(i) = 1
              elseif (nn_arr(i) == node_per_hex) then
                 nn_arr(i) = 2
              elseif (nn_arr(i) == node_per_prz) then
                 nn_arr(i) = 3
              else
                 nn_arr(i) = 4
              end if
!             write(1001,'(i0,1x,i0," : ",8(i0,1x))'), i+is-1,                 &
!               my_npc(header(i)), icell(1:my_npc(header(i)),i)
           end do
         end if

         call fv_process_elements(max_slice,in,nn_arr,icell,total_ncell)
      end do
      deallocate(icell,header,nn_arr)
    end do element_section_loop

    call fv_finalize_elements(grid)

    !write(6000+lmpi_id,*)"grid%nelem ",grid%nelem
    !do i = 1,grid%nelem
    !    do j = 1,size(grid%elem(i)%c2n,2)
    !       write(6000+lmpi_id,'(5(i0,1x))')             &
    !         grid%elem(i)%cl2g(j),grid%elem(i)%c2n(:,j)
    !    end do
    !end do

    grid%ncellg = total_ncell

!   do n = 1,grid%nelem
!     if( grid%elem(n)%ncell > 0 ) then
!      write(*,*) lmpi_id,grid%elem(n)%ncell,grid%elem(n)%type_cell
!      write(*,*) (grid%elem(n)%c2n(i,1),i=1,grid%elem(n)%node_per_cell)
!      write(*,*) (grid%elem(n)%c2n(i,grid%elem(n)%ncell),                     &
!                          i=1,grid%elem(n)%node_per_cell)
!     endif
!   end do

    if (total_ncell == 0) then
       write(*,*)'  stopping...error reading fieldview grid...',               &
                 ' no element types detected'
       call lmpi_conditional_stop(1)
    end if
    call lmpi_conditional_stop(0)

    return

103 continue ! read error, probably unformatted precision

    write(*,*) 'Fieldview unformatted read error: ' //                         &
               'check raw_grid%fieldview_coordinate_precision'
    call lmpi_conditional_stop(1)

  end subroutine fieldv_unf_par

!=================================== MY_NPC ==================================80
!
! Return node_per_cell for a Fieldview unformatted header value
!
!=============================================================================80

  integer function my_npc(header)
    use element_defs, only : node_per_tet, node_per_hex,                       &
                             node_per_prz, node_per_pyr

    integer, intent(in) :: header

    integer, parameter :: max_num_elem_faces  = 6  ! parameter used by FV
    integer, parameter :: bits_per_wall       = 3  ! parameter used by FV
    integer, parameter :: elem_type_bit_shift = max_num_elem_faces*bits_per_wall
    integer            :: type_mask, j

    integer, dimension(4), parameter :: npc =                                  &
             (/ node_per_tet, node_per_pyr, node_per_prz, node_per_hex /)

    ! 1048576 == 4 == 8 npc
    !  786432 == 3 == 6 npc
    !  524288 == 2 == 5 npc
    !  262144 == 1 == 4 npc

  continue
    type_mask = ishft(7, elem_type_bit_shift)
    j         = ishft(iand(header,type_mask), -elem_type_bit_shift)
    my_npc   = npc(j)
  end function my_npc

!================================= FV_PROCESS_ELEMENTS =======================80
!
! Distribute FV cells and fill grid. Return grid and total_cells
!
!=============================================================================80

  subroutine fv_process_elements(max_slice,ncell,nn,icell,total_cells)

    use element_defs, only : node_per_tet, node_per_hex,                       &
                             node_per_prz, node_per_pyr

!   Note: inout on ncell, nn, icell will allow bcasting to non-master PEs.

    integer,                         intent(in)    :: max_slice
    integer,                         intent(inout) :: ncell
    integer, dimension(  max_slice), intent(inout) :: nn
    integer, dimension(8,max_slice), intent(inout) :: icell
    integer,                         intent(inout) :: total_cells

! local

    integer :: i,j,k, my_head, my_tail, act_size
    integer :: isize, itype, inode, mcell
    integer :: status

    integer, dimension(:),   allocatable :: temp_cl2g
    integer, dimension(:,:), allocatable :: temp_c2n

    integer, parameter :: start_size = 100000

    integer, dimension(4), parameter :: node_per_cell =                        &
             (/ node_per_tet, node_per_hex, node_per_prz, node_per_pyr /)

  continue

    if (total_cells == 0) then
       ncount = 0
       if (allocated(elem)) deallocate(elem)
       allocate(elem(4))
       do i = 1,4
          allocate(elem(i)%c2n(node_per_cell(i),start_size),stat=status)
          if (status /= 0) then
            write(*,*) 'Insufficient memory'
            call lmpi_die
          endif
          allocate(elem(i)%cl2g(start_size),stat=status)
          if (status /= 0) then
            write(*,*) 'Insufficient memory'
            call lmpi_die
          endif
          elem(i)%max_size = start_size

          elem(i)%act_size = 0
          elem(i)%c2n  = 0
          elem(i)%cl2g = 0
       end do
    end if

    call lmpi_bcast(ncell); mcell = iabs(ncell)
    call lmpi_bcast(nn)
    call lmpi_bcast(icell)

    my_head = pp_nhead(lmpi_id)
    my_tail = pp_ntail(lmpi_id)

    do_cell: do i = 1,mcell
       itype = nn(i)
       ncount(itype) = ncount(itype) + 1
       isize = node_per_cell(itype)
       do j = 1,isize
          inode = icell(j,i)
          if ((inode >= my_head).and.(inode <= my_tail)) then
             if (elem(itype)%act_size < elem(itype)%max_size) then

                elem(itype)%act_size = elem(itype)%act_size + 1
                k = elem(itype)%act_size
                elem(itype)%c2n(1:isize,k) = icell(1:isize,i)
                elem(itype)%cl2g(k)        = total_cells + i

             else

              ! c2n
                act_size = elem(itype)%act_size
                allocate(temp_c2n(isize,act_size),stat=status)
                if (status /= 0) then
                  write(*,*) 'Insufficient memory'
                  call lmpi_die
                endif
                temp_c2n = 0
                temp_c2n(1:isize,1:act_size) =                                 &
                      elem(itype)%c2n(1:isize,1:act_size)
                deallocate(elem(itype)%c2n); nullify(elem(itype)%c2n)

                elem(itype)%max_size = act_size + start_size
                allocate(elem(itype)%c2n(isize,elem(itype)%max_size),          &
                         stat=status)
                if (status /= 0) then
                  write(*,*) 'Insufficient memory'
                  call lmpi_die
                endif
                elem(itype)%c2n = 0
                elem(itype)%c2n(1:isize,1:act_size)=temp_c2n(1:isize,1:act_size)
                deallocate(temp_c2n)

              ! cl2g
                allocate(temp_cl2g(act_size),stat=status)
                if (status /= 0) then
                  write(*,*) 'Insufficient memory'
                  call lmpi_die
                endif
                temp_cl2g = 0
                temp_cl2g(1:act_size) = elem(itype)%cl2g(1:act_size)
                deallocate(elem(itype)%cl2g); nullify(elem(itype)%cl2g)

                allocate(elem(itype)%cl2g(elem(itype)%max_size),stat=status)
                if (status /= 0) then
                  write(*,*) 'Insufficient memory'
                  call lmpi_die
                endif
                elem(itype)%cl2g = 0
                elem(itype)%cl2g(1:act_size) = temp_cl2g(1:act_size)
                deallocate(temp_cl2g)

                elem(itype)%act_size = elem(itype)%act_size + 1
                elem(itype)%c2n(1:isize,elem(itype)%act_size)=icell(1:isize,i)
                elem(itype)%cl2g(elem(itype)%act_size) = total_cells + i
             end if
             cycle do_cell

          end if
       end do
    end do do_cell

    total_cells = total_cells + mcell
    call lmpi_bcast(total_cells)

    !write(*,*)"frmt,ncell,total_cells ",frmt,ncell,total_cells
  end subroutine fv_process_elements

!================================ FV_FINALIZE_ELEMENTS =======================80
!
! Set final sizes after FV cells have been distributed.
!
!=============================================================================80

  subroutine fv_finalize_elements(grid)

    use grid_types,   only : grid_type
    use element_defs, only : node_per_tet, node_per_hex,                       &
                             node_per_prz, node_per_pyr,                       &
                             type_tet, type_hex, type_prz, type_pyr,           &
                             initialize_elem

    type(grid_type),                 intent(inout) :: grid

! local

    integer :: i,j
    integer :: ne

  continue

! Move elements into grid.

    grid%nelem = 0
    do i = 1,4
      if (ncount(i) > 0) grid%nelem = grid%nelem + 1
    end do
    !write(*,*)"NELEM ",lmpi_id,grid%nelem

    allocate(grid%elem(grid%nelem))
    !write(*,*)"NC ",lmpi_id,grid%nelem,elem(:)%act_size

    ne = 0
    if (ncount(1) /= 0) then
      ne = ne + 1
      call initialize_elem(grid%elem(ne), type_tet)
      j = max(1,elem(1)%act_size)
      grid%elem(ne)%ncell  = elem(1)%act_size
      grid%elem(ne)%ncellg = ncount(1)
      allocate(grid%elem(ne)%c2n(node_per_tet,j))
      grid%elem(ne)%c2n(1:node_per_tet,1:j) = elem(1)%c2n(1:node_per_tet,1:j)
      allocate(grid%elem(ne)%cl2g(j))
      grid%elem(ne)%cl2g(1:j) = elem(1)%cl2g(1:j)
     !do i = 1,grid%elem(ne)%ncell
     !   write(34000+lmpi_id,20)                                               &
     !     grid%elem(ne)%cl2g(i),elem(ne)%c2n(1:node_per_tet,i)
     !end do
    end if
    deallocate(elem(1)%c2n)
    deallocate(elem(1)%cl2g)

    if (ncount(2) /= 0) then
      ne = ne + 1
      call initialize_elem(grid%elem(ne), type_hex)
      j = max(1,elem(2)%act_size)
      grid%elem(ne)%ncell  = elem(2)%act_size
      grid%elem(ne)%ncellg = ncount(2)
      allocate(grid%elem(ne)%c2n(node_per_hex,j))
      grid%elem(ne)%c2n(1:node_per_hex,1:j) = elem(2)%c2n(1:node_per_hex,1:j)
      allocate(grid%elem(ne)%cl2g(j))
      grid%elem(ne)%cl2g(1:j) = elem(2)%cl2g(1:j)
     !do i = 1,grid%elem(ne)%ncell
     !   write(34000+lmpi_id,20)                                               &
     !     grid%elem(ne)%cl2g(i),grid%elem(ne)%c2n(1:8,i)
     !end do
    end if
    deallocate(elem(2)%c2n)
    deallocate(elem(2)%cl2g)

    if (ncount(3) /= 0) then
      ne = ne + 1
      call initialize_elem(grid%elem(ne), type_prz)
      j = max(1,elem(3)%act_size)
      grid%elem(ne)%ncell  = elem(3)%act_size
      grid%elem(ne)%ncellg = ncount(3)
      allocate(grid%elem(ne)%c2n(node_per_prz,j))
      grid%elem(ne)%c2n(1:node_per_prz,1:j) = elem(3)%c2n(1:node_per_prz,1:j)
      allocate(grid%elem(ne)%cl2g(j))
      grid%elem(ne)%cl2g(1:j) = elem(3)%cl2g(1:j)
     !do i = 1,grid%elem(ne)%ncell
     !   write(34000+lmpi_id,20)                                               &
     !     grid%elem(ne)%cl2g(i),grid%elem(ne)%c2n(1:node_per_prz,i)
     !end do
    end if
    deallocate(elem(3)%c2n)
    deallocate(elem(3)%cl2g)

    if (ncount(4) /= 0) then
      ne = ne + 1
      call initialize_elem(grid%elem(ne), type_pyr)
      j = max(1,elem(4)%act_size)
      grid%elem(ne)%ncell  = elem(4)%act_size
      grid%elem(ne)%ncellg = ncount(4)
      allocate(grid%elem(ne)%c2n(node_per_pyr,j))
      grid%elem(ne)%c2n(1:node_per_pyr,1:j) = elem(4)%c2n(1:node_per_pyr,1:j)
      allocate(grid%elem(ne)%cl2g(j))
      grid%elem(ne)%cl2g(1:j) = elem(4)%cl2g(1:j)
     !do i = 1,grid%elem(ne)%ncell
     !   write(34000+lmpi_id,20)                                               &
     !     grid%elem(ne)%cl2g(i),grid%elem(ne)%c2n(1:node_per_pyr,i)
     !end do
    end if
    deallocate(elem(4)%c2n)
    deallocate(elem(4)%cl2g)

!20 format(i0,1x,8(i0,1x))

  end subroutine fv_finalize_elements

!=================================== FIELDV_FMT_BC_PAR =======================80
!
! Reads a formatted, mixed-element, unstructured grid in FieldView Grid format
!
! NOTE: version 3_0 can in theory have comment lines embedded anywhere; we
! only check for comments immediately after line 2
!
!        faceptr, facetag, tagmap, nbctags
!
!=============================================================================80

  subroutine fieldv_fmt_bc_par(istheremapbc,unit_grid)

    use info_depr,    only : cc_primal

    logical,         intent(in)    :: istheremapbc
    integer,         intent(in)    :: unit_grid

! local

    integer :: nn, nv, ngrids,nbnd_total, nvars, nbvars, idum
    integer :: i,n,nnodesg, iostat, ierr, bc_default
    integer :: fv_ver_major, fv_ver_minor, fv_file_flag
    integer :: nface_grand_total, nfcount_quad, nfcount_tri

    integer, dimension(8) :: idumv
    integer, dimension(:),   allocatable :: temp_facetag
    integer, dimension(:,:), allocatable :: temp_faceptr

    integer(system_i1), dimension(:), allocatable :: tag

    character(len=80), dimension(:), allocatable :: bctype_total

    real(dp) :: xdum, ydum, zdum, fdum

    character(len=80) :: txt
    character(len=1)  :: string_1

    continue

    ierr = 0
    if (lmpi_master) then

       rewind (unit_grid)

       read (unit_grid,*) txt, fv_ver_major, fv_ver_minor

       if (trim(adjustl(txt)) == 'FIELDVIEW') then
         fv_file_flag = 3
       else if (trim(adjustl(txt)) == 'FIELDVIEW_Grids') then
         fv_file_flag = 1
       else
         fv_file_flag = 2
       end if

!      skip any comment lines

       do
         read(unit_grid,*) string_1
         if (string_1 /= '!') exit
       end do
       backspace(unit_grid)

!      if older format or combined format, read flow constants

       if (fv_ver_major==2 .or. (fv_ver_major==3 .and. fv_file_flag==3)) then
         read(unit_grid,*) ! txt Constants
         read(unit_grid,*) (fdum,i=1,4) ! time,xmach,alpha,Re
         fdum = fdum   ! to avoid compiler warnings
       end if

!      number of grids

       if (fv_ver_major == 2 .and. fv_ver_minor == 4) then
         read(unit_grid,*)
         read(unit_grid,*) ngrids
       else
         read(unit_grid,*)  txt, ngrids
       end if
       if (.false.) ngrids = ngrids

!      number of boundaries

       if (fv_ver_major == 2 .and. fv_ver_minor == 4) then
         read(unit_grid,*)
         read(unit_grid,*) nbnd_total
       else
         read(unit_grid,*) txt, txt, nbnd_total
       end if

!      allocate space for boundary types

       allocate(bctype_total(nbnd_total)); bctype_total = ''

       if (fv_ver_major == 2 .and. fv_ver_minor <= 4) then
          do n=1,nbnd_total
            read(unit_grid,*)  bctype_total(n)
          end do
       else
          do n=1,nbnd_total
            read(unit_grid,*)  idum,idum,bctype_total(n)
          end do
       end if

       if (.not.istheremapbc.and.(lmpi_master)) then
          write (*,*)
          write (*,*) 'Creating  a new .mapbc file ',trim(mapbcfilename)
          write (*,*) 'Make sure to edit mapbcfile for compatibility'
          call se_open(unit_mapbc,file=mapbcfilename,                          &
                status='new',iostat=iostat)
          if ( iostat /= 0 ) then
            write(*,*)'error opening ',trim(mapbcfilename),' stopping...'
            ierr = 1
          end if

          if (ierr == 0) then
             write (unit_mapbc,'(i5)') nbnd_total
             bc_default = 1
             if ( cc_primal ) bc_default = 9999
             do n=1,nbnd_total
                write (unit_mapbc,'(2i5,2x,a)') n,bc_default,                  &
                                                trim(bctype_total(n))
             enddo
             write (*,*) 'Initial mapbcfile is written in this run'
             write (*,*) 'Make Sure to edit mapbcfile for compatibility'
             write (*,*) 'and repeat this run '
             ierr = 1
          end if
       end if
       deallocate(bctype_total)
    end if
    call lmpi_conditional_stop(ierr)

    if (lmpi_master) then

   !   number of volume (flow) variables in this file

       if (fv_ver_major == 2) then
         if (fv_ver_minor == 4) then
           read(unit_grid,*) txt
           read(unit_grid,*) nvars
         else
           read(unit_grid,*)  txt, txt, nvars ! Variable Names 4
         end if
   !     read list of volume variables, but fun3d doesn't use them
         do nv=1,nvars
           read(unit_grid,*) ! txt
         end do
       end if

       if (fv_ver_major == 2 .and. fv_ver_minor == 5) then
   !     number of boundary (flow) variables in this file
         read(unit_grid,*)  txt, txt, txt, nbvars ! Boundary Variable Names 4
   !     read list of boundary variables, but fun3d doesn't use them
         do nv=1,nbvars
           read(unit_grid,*) ! txt
         end do
       end if

   !   read node dimensions

       read(unit_grid,*)  txt,nnodesg

   !   read node coordinates

       do n=1,nnodesg
         read(unit_grid,*) xdum,ydum,zdum
       enddo
       if (.false.) write(*,*) xdum,ydum,zdum

    end if ! lmpi_master

    if (lmpi_master) read(unit_grid,*) txt,txt,nface_grand_total
!   if (lmpi_master) write(*,*)"nface_grand_total ",nface_grand_total

!   corresponds to write(2,'(''boundary faces'',i6)') nquad+ntri
!   warning - we need some way to distinguish diff types of boundaries

    if (lmpi_master) then
       nfcount_quad = 0
       nfcount_tri  = 0

       allocate(temp_faceptr(nface_grand_total,4)); temp_faceptr = 0
       allocate(temp_facetag(nface_grand_total));   temp_facetag = 0
       allocate(tag         (nface_grand_total));   tag          = 0

       ntface = 0
       nqface = 0
       do nv=1,nface_grand_total
         read(unit_grid,*) nn,idum,(idumv(n),n=1,idum)
         temp_facetag(nv) = nn
         tag(nv) = idum
         temp_faceptr(nv,1:idum) = idumv(1:idum)
         if(idum == 4) then
            nqface = nqface + 1
         else
            ntface = ntface + 1
         endif
       end do
    end if

    call lmpi_bcast(nqface)
    call lmpi_bcast(ntface)

    if (allocated(faceptr)) deallocate(faceptr)
    if (allocated(facetag)) deallocate(facetag)
    allocate(faceptr(nqface+ntface,4)); faceptr = 0
    allocate(facetag(nqface+ntface));   facetag = 0

    if (lmpi_master) then
       nfcount_quad = ntface ! offset
       nfcount_tri  = 0
       do nv = 1,ntface+nqface
          if (tag(nv) == 3) then
              nfcount_tri = nfcount_tri + 1
              faceptr(nfcount_tri,1:3) = temp_faceptr(nv,1:3)
              facetag(nfcount_tri)     = temp_facetag(nv)
          else
              nfcount_quad = nfcount_quad + 1
              faceptr(nfcount_quad,1:4) = temp_faceptr(nv,1:4)
              facetag(nfcount_quad)     = temp_facetag(nv)
          end if
       end do
!      write(*,*)"nfcount_tri, nfcount_quad ",nfcount_tri,nfcount_quad
       deallocate(temp_faceptr)
       deallocate(temp_facetag)
       deallocate(tag)
    end if
    call lmpi_bcast(faceptr)
    call lmpi_bcast(facetag)

  end subroutine fieldv_fmt_bc_par

!=================================== FIELDV_UNF_BC_PAR =======================80
!
! Reads an unformatted, mixed-element, unstructured grid in FieldView format
!
!        faceptr, facetag, tagmap, nbctags
!
!=============================================================================80

  subroutine fieldv_unf_bc_par(istheremapbc,unit_grid,fv_precision)

    use info_depr,    only : cc_primal
    use allocations,  only : my_alloc_ptr, my_realloc_ptr

    logical, intent(in) :: istheremapbc
    integer, intent(in) :: unit_grid
    integer, intent(in) :: fv_precision

! local

    integer :: n, nn, nv, ngrids, nvars, nbvars, nnodesg
    integer :: iostat, ierr, idum
    integer :: nbnd_total, bndry_type, bc_default
    integer :: fv_ver_major, fv_ver_minor, fv_file_flag
    integer :: fv_magic, fv_nodes, fv_faces
    integer :: nfcount_quad, nfcount_tri
    integer :: surface_results_flag, clockness_flag, offset

    integer, dimension(:),   allocatable :: nface_total
    integer, dimension(:),   pointer     :: temp_facetag
    integer, dimension(:,:), pointer     :: temp_faceptr

    character(len=5)                             :: str_major, str_minor
    character(len=80), dimension(:), allocatable :: bctype_total

    real(r4) :: r4_dum3(3)
    real(r8) :: r8_dum3(3)

    continue

    ierr = 0
    if (lmpi_master) then

       rewind (unit_grid)

       read(unit_grid) fv_magic

       if (fv_magic /= 66051) then
         write(*,'(a)') 'Error reading fieldview magic number '
         ierr = 1
       end if
    end if
    call lmpi_conditional_stop(ierr)

    if (lmpi_master) then

       read(unit_grid) ! txt FIELDVIEW

!   fieldview version

       read (unit_grid) fv_ver_major, fv_ver_minor

       write(str_major,'(i0)') fv_ver_major
       write(str_minor,'(i0)') fv_ver_minor

       fv_file_flag = 3 ! Assume FV_COMBINED_FILE

       if ((fv_ver_major == 2 .and. (fv_ver_minor >= 7)) .or.                  &
           fv_ver_major == 3) then
         backspace(unit_grid)
         read(unit_grid) fv_ver_major, fv_ver_minor, fv_file_flag, idum
         idum = idum ! avoid compiler warning
         if (fv_file_flag /= 1 .and. fv_file_flag /= 3) then
           write(*,'(a)') 'Error reading file: Not a "Grids" or "Combined" file'
           write(*,'(a)') 'FUN3D does not accept "Results" file'
           write(*,'(a,i0)') 'fv_file_flag = ', fv_file_flag
           ierr = 1
         end if
       end if

       if (fv_file_flag >= 2) then
         read(unit_grid) ! time,xmach,alpha,Re
       end if

!      number of grids

       read(unit_grid) ngrids

!      error (for fun3d) if more than one grid

       if (ngrids /= 1) then
         write(*,'(a,i0)') 'Error: ngrids = ', ngrids
         write(*,'(a)') 'FUN3D accepts only ngrids = 1'
         ierr = 1
       end if

    end if
    call lmpi_conditional_stop(ierr)

    if (lmpi_master) then

!      number of boundaries

       read(unit_grid) nbnd_total

!      allocate space for boundary types

       allocate(bctype_total(nbnd_total)); bctype_total = ''

       if (fv_ver_major == 2 .and. fv_ver_minor <= 4) then
          do n=1,nbnd_total
            read(unit_grid) bctype_total(n)
          end do
       else
         do n=1,nbnd_total
           read(unit_grid) surface_results_flag, clockness_flag, bctype_total(n)
           surface_results_flag = surface_results_flag ! avoid compiler warnings
           clockness_flag       = clockness_flag
         end do
       end if

       if (.not.istheremapbc.and.(lmpi_master)) then
          write (*,*)
          write (*,*) 'Creating  a new .mapbc file ',trim(mapbcfilename)
          write (*,*) 'Make sure to edit mapbcfile for compatibility'
          call se_open(unit_mapbc,file=mapbcfilename,                          &
                status='new',iostat=iostat)
          if ( iostat /= 0 ) then
            write(*,*)'error opening ',trim(mapbcfilename),' stopping...'
            ierr = 1
          end if

          if (ierr == 0) then
             write (unit_mapbc,'(i5)') nbnd_total
             bc_default = 1
             if ( cc_primal ) bc_default = 9999
             do n=1,nbnd_total
                write (unit_mapbc,'(2i5,2x,a)') n,bc_default,                  &
                                                trim(bctype_total(n))
             enddo
             write (*,*) 'Initial mapbcfile is written in this run'
             write (*,*) 'Make Sure to edit mapbcfile for compatibility'
             write (*,*) 'and repeat this run '
             ierr = 1
          end if
       end if
       deallocate(bctype_total)
    end if
    call lmpi_conditional_stop(ierr)
    call lmpi_bcast(nbnd_total)

    if (lmpi_master) then

!   number of volume (flow) variables in this file

       nvars = 0
       if (fv_file_flag >= 2) then
!         number of volume (flow) variables in this file
          read(unit_grid)  nvars
!         read list of volume variables, but fun3d doesn't use them
          do nv=1,nvars
             read(unit_grid) ! txt
          end do
       end if

       nbvars = 0
       if ((fv_file_flag >= 2) .and.                                           &
           (fv_ver_major == 2 .and. fv_ver_minor > 4)) then
!         number of boundary (flow) variables in this file
          read(unit_grid)  nbvars
!         read list of boundary variables, but fun3d doesn't use them
          do nv=1,nbvars
             read(unit_grid) ! txt
          end do
       end if

   !   read node dimensions

       read(unit_grid)  fv_nodes, nnodesg
       if (.false.) fv_nodes = fv_nodes

   !   skip node coordinates

       if (fv_precision == 0) then
         read(unit_grid) (r4_dum3,n=1,nnodesg) ! single precision
         if (.false.) r4_dum3 = r4_dum3
       else
         read(unit_grid) (r8_dum3,n=1,nnodesg) ! double precision
         if (.false.) r8_dum3 = r8_dum3
       end if

       allocate(nface_total(nbnd_total)); nface_total = 0

       ntface = 0
       nqface = 0
       offset = 0

       read_bnd_faces : do nn=1,nbnd_total

         read(unit_grid) fv_faces, bndry_type, nface_total(nn)

         if (fv_faces /= 1002) then
            write(*,'(a)') 'error: fv_faces /= 1002'
            ierr = 1
            exit read_bnd_faces
         end if

         if (nn /= 1) then
            call my_realloc_ptr(temp_faceptr,offset+nface_total(nn),4)
            call my_realloc_ptr(temp_facetag,offset+nface_total(nn) )
         else
            call my_alloc_ptr(temp_faceptr,offset+nface_total(nn),4)
            call my_alloc_ptr(temp_facetag,offset+nface_total(nn) )
         end if

         read(unit_grid) ( temp_faceptr(offset+n,1:4), n=1,nface_total(nn) )

         do n=1,nface_total(nn)
           temp_facetag(offset+n) = bndry_type
           if (temp_faceptr(offset+n,4)==0) then
              ntface = ntface + 1
           else
              nqface = nqface + 1
           end if
         end do

         offset = offset + nface_total(nn)

      end do read_bnd_faces
      deallocate(nface_total)

    end if
    call lmpi_conditional_stop(ierr)

    call lmpi_bcast(nqface)
    call lmpi_bcast(ntface)

    if (allocated(faceptr)) deallocate(faceptr)
    if (allocated(facetag)) deallocate(facetag)
    allocate(faceptr(nqface+ntface,4)); faceptr = 0
    allocate(facetag(nqface+ntface));   facetag = 0

    if (lmpi_master) then
       nfcount_quad = ntface ! offset
       nfcount_tri  = 0
       do nv = 1,ntface+nqface
          if (temp_faceptr(nv,4) == 0) then
              nfcount_tri = nfcount_tri + 1
              faceptr(nfcount_tri,1:3) = temp_faceptr(nv,1:3)
              facetag(nfcount_tri)     = temp_facetag(nv)
          else
              nfcount_quad = nfcount_quad + 1
              faceptr(nfcount_quad,1:4) = temp_faceptr(nv,1:4)
              facetag(nfcount_quad)     = temp_facetag(nv)
          end if
       end do
       deallocate(temp_faceptr)
       deallocate(temp_facetag)

    end if

    call lmpi_bcast(faceptr)
    call lmpi_bcast(facetag)

  end subroutine fieldv_unf_bc_par

!================================= MIRROR_GRID_PAR_DRIVER ====================80
!
! Driver for Mirroring grid (in Distributed Memory) about a symmetry plane
!
!=============================================================================80

  subroutine mirror_grid_par_driver(flow_dir,grid,raw_grid_data)

    use grid_types,    only : grid_type, raw_grid_data_type

    character(*), intent(in) :: flow_dir

    type(grid_type),          intent(inout) :: grid
    type(raw_grid_data_type), intent(in)    :: raw_grid_data

    integer :: i
    integer, dimension(:), allocatable :: old_l2g

  continue

! Read the tags

    select case(trim(raw_grid_data%grid_format))

      case ('aflr3')
        call puns3d_read_aflr3_bc(flow_dir,raw_grid_data%data_format,grid)

      case ('fieldview')
        call puns3d_read_fieldview_c2n(2,flow_dir,grid,raw_grid_data)

      case ('fast')
        call puns3d_read_fast_bc(flow_dir,grid,raw_grid_data%data_format)

      case ('vgrid')
        call puns3d_read_vgrid_bc(flow_dir,grid)

      case ('felisa')
        if (lmpi_master) write(*,*)"REPLACE puns3d_read_felisa_tags"
        call lmpi_conditional_stop(1)
        !call puns3d_read_felisa_tags(                                         &
        !       lnodes,grid%x,grid%y,grid%z,grid%project,                      &
        !       grid%nnodesg, grid%elem(1)%ncellg) ! FIXME: last arg undefined

      case default
        write(*,*)'mirroring:  grid_format not supported.'
        call lmpi_conditional_stop(1)

    end select

    call lump_boundaries(raw_grid_data,grid)

    ! Fill the bc arrays for all PEs

    call process_grid_bc_mirror_allocate(grid)

    ! Enforce points on symmetry plane

    call mirror_mirror_on_the_wall(                                            &
                        grid%nbound, grid%bc, grid%x, grid%y, grid%z,          &
                        pp_nhead(lmpi_id), pp_ntail(lmpi_id))

    ! Mirror the grid

       !write(*,*)"B4 mirror_grid_par g%nn,s(g%l2g) ",
       !   lmpi_id,grid%nnodes0,size(grid%l2g),grid%ncellg

    call mirror_grid_par(grid)
    grid%nnodes01  = grid%nnodes0

    ! Redistribute c2n for mirrored grid (must be called with old l2g
    call puns3d_send_c2n_slice(grid)

    ! Redistribute xyz for mirrored grid using nodes
    allocate(old_l2g(size(grid%l2g)))
    old_l2g = grid%l2g

    ! Reorder l2g to even distribute the nodes
    do i = 1,pp_nsize(lmpi_id)
       grid%l2g(i) = (pp_nhead(lmpi_id)+i)-1
    end do

    call puns3d_send_xyz_slice(size(old_l2g),old_l2g,grid)
    deallocate(old_l2g)

    ! write(*,*)'nn,nng,nc,ncg ',grid%nnodes,grid%nnodesg,                     &
    !   grid%elem(:)%ncell,grid%elem(:)%ncellg

  end subroutine mirror_grid_par_driver


!================================= MIRROR_GRID_PAR ===========================80
!
! Mirrors grid (in Distributed Memory) about a symmetry plane
!
! Note, if routine becomes cpu bound, then replace the first binary_search
! in omap with a O(1) bit array.
!
! Additionally, the values searched on omap (i.e., symmetry nodes not
! mirrored) can be compressed to only include the one point of sequential
! set of values.
!
!=============================================================================80

  subroutine mirror_grid_par(grid)

    use allocations, only : my_realloc_ptr
    use bc_types,    only : bcgrid_type
    use grid_types,  only : grid_type

    type(grid_type),  intent(inout) :: grid

    integer :: i,j,n,nd,imaxt,imaxq,ielem,n_mirror,nold,iostat,ierr
    integer :: n_head, n_tail, n_size
    integer :: nnodes0_org, nnodesg_org, nnodesg_new
    integer :: iface, face_2d
    integer :: ncellL, total_ncellg
    integer :: nbound_new, ib, ibb
    integer :: nsym, nsym_ct, my_symmetry_strong
    integer :: block_offset

    logical, dimension(:), allocatable :: symmetry_tag, skip_bound
    integer, dimension(:), allocatable :: bound_array
    integer, dimension(:), allocatable :: my_counts, temp

    type(bcgrid_type), dimension(:), pointer :: bc_temp

    character(len=1)   :: xyz
    character(len=256) :: project_mirror

  continue

    if (grid%cc) then
      write(*,*) 'cc not implemented in pp'
      call lmpi_die
    end if
    ierr = 0

    n_head = pp_nhead(lmpi_id)
    n_tail = pp_ntail(lmpi_id)
    n_size = pp_nsize(lmpi_id)
    ! write(*,'(a20,7(i0,1x))')'IS,IE = ',lmpi_id,n_head,n_tail,n_size

! save off original c2n_info n_ and c_ values into m_ and d_.

    allocate(pp_mhead(0:lmpi_nproc-1)); pp_mhead = pp_nhead
    allocate(pp_mtail(0:lmpi_nproc-1)); pp_mtail = pp_ntail
    allocate(pp_msize(0:lmpi_nproc-1)); pp_msize = pp_nsize

    allocate(pp_elem_ct(grid%nelem)); pp_elem_ct = 0
    do ielem = 1,grid%nelem
       pp_elem_ct(ielem) = grid%elem(ielem)%ncell
    end do

    nnodes0_org = grid%nnodes0
    !write(*,*)'NN_org,s(l2g) ',lmpi_id,nnodes0_org
    call lmpi_reduce(nnodes0_org,nnodesg_org)
    call lmpi_bcast(nnodesg_org)

    n_mirror = 0
    if (mirror_x) n_mirror = n_mirror + 1
    if (mirror_y) n_mirror = n_mirror + 1
    if (mirror_z) n_mirror = n_mirror + 1
    if (n_mirror > 1) then
       ierr = 1
       if (lmpi_master) write(*,*) 'Multi-plane mirroring not supported.'
    end if
    call lmpi_conditional_stop(ierr)

    if (mirror_x) then
      my_symmetry_strong = symmetry_x; xyz = 'x'
    else if (mirror_y) then
      my_symmetry_strong = symmetry_y; xyz = 'y'
    else ! if (mirror_z) then
      my_symmetry_strong = symmetry_z; xyz = 'z'
    end if

!   tag points on symmetry plane, and count them

    allocate(symmetry_tag(n_head:n_tail)); symmetry_tag = .false.

    do ib=1,grid%nbound
       if(grid%bc(ib)%ibc == my_symmetry_strong) then
         do iface=1,grid%bc(ib)%nbfacet
            do nd =1,3
              i = grid%bc(ib)%f2ntb(iface,nd)
              if ((i >= n_head).and.(i <= n_tail)) symmetry_tag(i) = .true.
            end do
         end do

         do iface=1,grid%bc(ib)%nbfaceq
            do nd =1,4
              i = grid%bc(ib)%f2nqb(iface,nd)
              if ((i >= n_head).and.(i <= n_tail)) symmetry_tag(i) = .true.
            end do
         end do
       end if
    end do

    nsym = 0
    do i = n_head,n_tail
       if (symmetry_tag(i)) nsym = nsym + 1
    end do
    !write(*,*)"NSYM ",lmpi_id,nsym,n_head,n_tail

    allocate(my_counts(lmpi_nproc))
    my_counts(lmpi_id+1) = nsym
    call lmpi_gather(nsym,my_counts)
    call lmpi_bcast(my_counts)
    nsym_ct = sum(my_counts)

    if (lmpi_master) then
       write(*,*) 'Mirroring: number of points on symmetry plane ',nsym_ct
       if (nsym == 0) then
          write(*,*) 'stopping in mirror_grid...nsym = 0'
          ierr = 1
       end if
    end if
    call lmpi_conditional_stop(ierr)

!-----------------
! block_offset = sum(previous whole blocks) - sum(wholes in previous blocks)

    block_offset = 0
    if (lmpi_id > 0) block_offset = sum(pp_nsize(0:lmpi_id-1)) -               &
                                    sum(my_counts(1:lmpi_id))

    sym_nnodesg_org = nnodesg_org
    allocate(temp(nsym)); temp = 0
    sym_num = nsym_ct
    allocate(sym_val(nsym_ct)); sym_val = 0
    n = 0
    do i = n_head,n_tail
       if (symmetry_tag(i)) then
          n = n + 1
          temp(n) = i
       end if
    end do
    call lmpi_gatherv(temp,my_counts(lmpi_id+1),sym_val,my_counts)
    call lmpi_bcast(sym_val)

    allocate(sym_off(nsym_ct)); sym_off = 0
    n = 0
    j = 0
    do i = n_head,n_tail
       if (symmetry_tag(i)) then
          n = n + 1
          temp(n) = nnodesg_org + block_offset + j
       else
         j = j + 1
       end if
    end do
    call lmpi_gatherv(temp,my_counts(lmpi_id+1),sym_off,my_counts)
    call lmpi_bcast(sym_off)
    deallocate(temp,my_counts)

!----------------------------

    nnodesg_new  = 2*nnodesg_org - nsym_ct
    grid%nnodesg = nnodesg_new
    grid%nnodes0 = (n_size+n_size) - nsym

    ! if(lmpi_master)write(*,*)'nnodesg_new =',lmpi_id,nsym_ct,nsym,nnodesg_new

    call my_realloc_ptr(grid%x, grid%nnodes0)
    call my_realloc_ptr(grid%y, grid%nnodes0)
    call my_realloc_ptr(grid%z, grid%nnodes0)

    allocate(grid%l2g(grid%nnodes0)); grid%l2g = 0
    do i = 1,nnodes0_org
       grid%l2g(i) = (n_head+i)-1
    end do

    n = 0
    j = n_head -1
    do i=1,nnodes0_org
       j = j + 1
       if (.not.symmetry_tag(j)) then
          n = n + 1
          grid%x(nnodes0_org+n) = grid%x(i)
          grid%y(nnodes0_org+n) = grid%y(i)
          grid%z(nnodes0_org+n) = grid%z(i)
          if (mirror_x) grid%x(nnodes0_org+n) = -grid%x(nnodes0_org+n)
          if (mirror_y) grid%y(nnodes0_org+n) = -grid%y(nnodes0_org+n)
          if (mirror_z) grid%z(nnodes0_org+n) = -grid%z(nnodes0_org+n)
          grid%l2g(nnodes0_org+n) = omap(j)
       end if
    end do

    !  do i = 1,grid%nnodes0
    !     write(20000+lmpi_id,'(i0,3(F20.12,1x))')                             &
    !       grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
    !   end do
    !write(*,*) 's20000'
    !call lmpi_die

!----------------------------

    total_ncellg = 0
    do ielem=1,grid%nelem
       total_ncellg  = total_ncellg + grid%elem(ielem)%ncellg
    end do

    do ielem=1,grid%nelem

      nold = grid%elem(ielem)%ncell

      ncellL                  = 2*size(grid%elem(ielem)%c2n,2)
      grid%elem(ielem)%ncell  = 2*grid%elem(ielem)%ncell
      grid%elem(ielem)%ncellg = 2*grid%elem(ielem)%ncellg

      call my_realloc_ptr(grid%elem(ielem)%c2n,                                &
                          grid%elem(ielem)%node_per_cell,                      &
                          max(ncellL,1))
      call my_realloc_ptr(grid%elem(ielem)%cl2g,max(ncellL,1))

!     swap nodes to make reflected cells right handed

      select case (grid%elem(ielem)%type_cell)

      case('tet')
        do i = 1,nold
           grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
           grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
           grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
           grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
        end do
      case('pyr')
        do i = 1,nold
           grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
           grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
           grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
           grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
           grid%elem(ielem)%c2n(5,nold+i) = omap(grid%elem(ielem)%c2n(5,i))
        end do
      case('prz')
        do i = 1,nold
           grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(6,i))
           grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(5,i))
           grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
           grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
           grid%elem(ielem)%c2n(5,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
           grid%elem(ielem)%c2n(6,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
        end do
      case('hex')
        if (twod) then
           face_2d = grid%elem(ielem)%face_2d
           if ((face_2d == 1) .or. (face_2d == 2)) then
              do i = 1,nold
                grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
                grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
                grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
                grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
                grid%elem(ielem)%c2n(5,nold+i) = omap(grid%elem(ielem)%c2n(5,i))
                grid%elem(ielem)%c2n(6,nold+i) = omap(grid%elem(ielem)%c2n(7,i))
                grid%elem(ielem)%c2n(7,nold+i) = omap(grid%elem(ielem)%c2n(6,i))
                grid%elem(ielem)%c2n(8,nold+i) = omap(grid%elem(ielem)%c2n(8,i))
              end do
            else if ((face_2d == 3) .or. (face_2d == 4)) then
              do i = 1,nold
                grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
                grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
                grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(5,i))
                grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(6,i))
                grid%elem(ielem)%c2n(5,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
                grid%elem(ielem)%c2n(6,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
                grid%elem(ielem)%c2n(7,nold+i) = omap(grid%elem(ielem)%c2n(7,i))
                grid%elem(ielem)%c2n(8,nold+i) = omap(grid%elem(ielem)%c2n(8,i))
              end do
            else if ((face_2d == 5) .or. (face_2d == 6)) then
              do i = 1,nold
                grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
                grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(5,i))
                grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
                grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(7,i))
                grid%elem(ielem)%c2n(5,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
                grid%elem(ielem)%c2n(6,nold+i) = omap(grid%elem(ielem)%c2n(6,i))
                grid%elem(ielem)%c2n(7,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
                grid%elem(ielem)%c2n(8,nold+i) = omap(grid%elem(ielem)%c2n(8,i))
              end do
            end if
        else
            do i = 1,nold
               grid%elem(ielem)%c2n(1,nold+i) = omap(grid%elem(ielem)%c2n(1,i))
               grid%elem(ielem)%c2n(2,nold+i) = omap(grid%elem(ielem)%c2n(3,i))
               grid%elem(ielem)%c2n(3,nold+i) = omap(grid%elem(ielem)%c2n(2,i))
               grid%elem(ielem)%c2n(4,nold+i) = omap(grid%elem(ielem)%c2n(4,i))
               grid%elem(ielem)%c2n(5,nold+i) = omap(grid%elem(ielem)%c2n(5,i))
               grid%elem(ielem)%c2n(6,nold+i) = omap(grid%elem(ielem)%c2n(7,i))
               grid%elem(ielem)%c2n(7,nold+i) = omap(grid%elem(ielem)%c2n(6,i))
               grid%elem(ielem)%c2n(8,nold+i) = omap(grid%elem(ielem)%c2n(8,i))
            end do
        end if
      end select
      do i = 1,nold
         grid%elem(ielem)%cl2g(nold+i) = grid%elem(ielem)%cl2g(i)+total_ncellg
      end do
    end do ! ielem
    call lmpi_conditional_stop(ierr)
    grid%ncellg = grid%ncellg+grid%ncellg

!------------------------------
! Leave xyz and c2n in memory. Use them later instead of reading them in.
! However, write the boundary face information to a file.

  if (lmpi_master) then

    ! count the non-symmetry boundaries
    nbound_new = 0

    do ib=1,grid%nbound
      if (grid%bc(ib)%ibc /= my_symmetry_strong) then
        nbound_new = nbound_new + 1
      end if
    end do

    nbound_new = 2*nbound_new

    write(*,*) 'number of boundaries before mirroring: ', grid%nbound
    write(*,*) 'number of boundaries  after mirroring: ', nbound_new

    allocate(bc_temp(grid%nbound))

    do ib=1,grid%nbound

      bc_temp(ib)%ibc           = grid%bc(ib)%ibc
      bc_temp(ib)%nbfacet       = grid%bc(ib)%nbfacet
      bc_temp(ib)%nbfaceq       = grid%bc(ib)%nbfaceq

      allocate(bc_temp(ib)%f2ntb(max(grid%bc(ib)%nbfacet,1),5))
      allocate(bc_temp(ib)%f2nqb(max(grid%bc(ib)%nbfaceq,1),6))
      bc_temp(ib)%f2ntb = 0
      bc_temp(ib)%f2nqb = 0
      bc_temp(ib)%f2ntb = grid%bc(ib)%f2ntb
      bc_temp(ib)%f2nqb = grid%bc(ib)%f2nqb
      deallocate(grid%bc(ib)%f2ntb)
      deallocate(grid%bc(ib)%f2nqb)

      allocate(bc_temp(ib)%face_bit(max(grid%bc(ib)%nbfacet,1)))
      allocate(bc_temp(ib)%face_bitq(max(grid%bc(ib)%nbfaceq,1)))
      bc_temp(ib)%face_bit  = 0
      bc_temp(ib)%face_bitq = 0

      if (associated(grid%bc(ib)%face_bit)) then
         bc_temp(ib)%face_bit  = grid%bc(ib)%face_bit
         deallocate(grid%bc(ib)%face_bit)
      end if
      if (associated(grid%bc(ib)%face_bitq)) then
         bc_temp(ib)%face_bitq = grid%bc(ib)%face_bitq
         deallocate(grid%bc(ib)%face_bitq)
      end if

    end do
    deallocate(grid%bc)

!   now that we have all the current pieces of bc copied, reallocate bc
!   to the new size

    allocate(grid%bc(nbound_new))
    allocate(bound_array(nbound_new)); bound_array = 0

    ibb = 0
    allocate(skip_bound(grid%nbound)); skip_bound = .false.

    do ib=1,grid%nbound

      if (bc_temp(ib)%ibc == my_symmetry_strong) then
          skip_bound(ib) = .true.
          cycle
      end if

!     original boundaries except symmetry

      ibb = ibb + 1

      bound_array(ibb) = ibb
      grid%bc(ibb)%ibc      = bc_temp(ib)%ibc
      grid%bc(ibb)%nbfacet  = bc_temp(ib)%nbfacet
      grid%bc(ibb)%nbfaceq  = bc_temp(ib)%nbfaceq

      imaxt = max(bc_temp(ib)%nbfacet,1)
      imaxq = max(bc_temp(ib)%nbfaceq,1)

      allocate(grid%bc(ibb)%f2ntb(imaxt,5));   grid%bc(ibb)%f2ntb     = 0
      allocate(grid%bc(ibb)%f2nqb(imaxq,6));   grid%bc(ibb)%f2nqb     = 0
      allocate(grid%bc(ibb)%face_bit(imaxt));  grid%bc(ibb)%face_bit  = 0
      allocate(grid%bc(ibb)%face_bitq(imaxq)); grid%bc(ibb)%face_bitq = 0

      if (grid%bc(ibb)%nbfacet > 0) then
        grid%bc(ibb)%f2ntb    = bc_temp(ib)%f2ntb
        grid%bc(ibb)%face_bit = bc_temp(ib)%face_bit
      end if

      if (grid%bc(ibb)%nbfaceq > 0) then
        grid%bc(ibb)%f2nqb     = bc_temp(ib)%f2nqb
        grid%bc(ibb)%face_bitq = bc_temp(ib)%face_bitq
      end if

!     new boundaries

      ibb = ibb + 1

      bound_array(ibb) = ibb
      grid%bc(ibb)%ibc      = bc_temp(ib)%ibc
      grid%bc(ibb)%nbfacet  = bc_temp(ib)%nbfacet
      grid%bc(ibb)%nbfaceq  = bc_temp(ib)%nbfaceq

      allocate(grid%bc(ibb)%f2ntb(imaxt,5));   grid%bc(ibb)%f2ntb     = 0
      allocate(grid%bc(ibb)%f2nqb(imaxq,6));   grid%bc(ibb)%f2nqb     = 0
      allocate(grid%bc(ibb)%face_bit(imaxt));  grid%bc(ibb)%face_bit  = 0
      allocate(grid%bc(ibb)%face_bitq(imaxq)); grid%bc(ibb)%face_bitq = 0
      if(grid%bc(ibb)%nbfacet>0)grid%bc(ibb)%face_bit  = bc_temp(ib)%face_bit
      if(grid%bc(ibb)%nbfaceq>0)grid%bc(ibb)%face_bitq = bc_temp(ib)%face_bitq

!     note: when setting f2ntb and f2nqb on the new boundaries,
!           reverse indicies to make normals point into the domain

      do iface=1,grid%bc(ibb)%nbfacet
        do nd = 1,3
          i = bc_temp(ib)%f2ntb(iface,nd)
          grid%bc(ibb)%f2ntb(iface,4-nd) = omap(i)
        end do
      end do

      do iface=1,grid%bc(ibb)%nbfaceq
        do nd = 1,4
          i = bc_temp(ib)%f2nqb(iface,nd)
          grid%bc(ibb)%f2nqb(iface,5-nd) = omap(i)
        end do
      end do

    end do

!   done; get rid of the bc_temp stuff

    do ib=1,grid%nbound
      deallocate(bc_temp(ib)%f2ntb)
      deallocate(bc_temp(ib)%f2nqb)
      deallocate(bc_temp(ib)%face_bit)
      deallocate(bc_temp(ib)%face_bitq)
    end do

    deallocate(bc_temp)

    grid%nbound = nbound_new

  end if ! master

  call lmpi_bcast(grid%nbound)

!---------------------------------------------------------------------
! If write_mirror, write out mirror'ed data in fgrid format.

  deallocate(tagmap)
  deallocate(family_map)
  if (lmpi_master) then
     j = 0
     do i = 1,nbctags_org
        if (.not.skip_bound(i)) j = j + 1
     end do
     j = j*2
  end if
  call lmpi_bcast(j)
  allocate(tagmap(j,2));   tagmap     = 0
  allocate(family_map(j)); family_map = repeat(' ',120)

  if (lmpi_master) then

    project_mirror = trim(grid%project) // "_mirror_" // trim(xyz) // ".mapbc"

    write(*,*)'Creating mirror mapbc file : ',trim(project_mirror)

    call se_open(99,file=project_mirror,status='unknown',iostat=iostat)
    if ( iostat /= 0 ) then
       write(*,*)'error opening ',trim(project_mirror),' stopping...'
       call lmpi_conditional_stop(1)
    endif

    nbctags = grid%nbound
    write(99,'(i0,1x)') grid%nbound
    ibb = 0
    do i = 1,nbctags_org
       if (.not.skip_bound(i)) then
          ibb = ibb + 1
          tagmap(ibb,1) = ibb
          tagmap(ibb,2) = tagmap_org(i,2)
          family_map(ibb) = family_org(i)
          write(99,'(2(i0,1x),a)') tagmap(ibb,1:2),trim(family_map(ibb))
          ibb = ibb + 1
          tagmap(ibb,1) = ibb
          tagmap(ibb,2) = tagmap_org(i,2)
          family_map(ibb) = family_map(ibb-1)
          write(99,'(2(i0,1x),a)') tagmap(ibb,1:2),trim(family_map(ibb))
       end if
    end do

   deallocate(skip_bound)

!----------------

   write(*,*)
   write(*,*)'Number of nodes          : ',nnodesg_new
   write(*,*)'Total number of cells    : ',grid%ncellg
   write(*,*)

  end if ! master
  call lmpi_conditional_stop(0)

  call lmpi_bcast(nbctags)
  call lmpi_bcast(tagmap)
  do i = 1,nbctags
     call lmpi_bcast(family_map(i))
  end do

! Clean-up

  sym_num = 0
  deallocate(sym_val, sym_off)

!-----------------------------------------------------
! Broadcast the boundary information

   if (.not.lmpi_master) then
      do ib=1,size(grid%bc)
         if(associated(grid%bc(ib)%f2ntb)) deallocate(grid%bc(ib)%f2ntb)
         if(associated(grid%bc(ib)%f2nqb)) deallocate(grid%bc(ib)%f2nqb)
         if(associated(grid%bc(ib)%face_bit)) deallocate(grid%bc(ib)%face_bit)
         if(associated(grid%bc(ib)%face_bitq))deallocate(grid%bc(ib)%face_bitq)
      end do
     deallocate(grid%bc)
   end if

   if (.not.lmpi_master) allocate(grid%bc(grid%nbound))

   allocate(temp(grid%nbound))
   if (lmpi_master) temp = grid%bc(:)%ibc
   call lmpi_bcast(temp)
   if (.not.lmpi_master) grid%bc(:)%ibc = temp

   if (lmpi_master) temp = grid%bc(:)%nbfacet
   call lmpi_bcast(temp)
   if (.not.lmpi_master) grid%bc(:)%nbfacet = temp

   if (lmpi_master) temp = grid%bc(:)%nbfaceq
   call lmpi_bcast(temp)
   if (.not.lmpi_master) grid%bc(:)%nbfaceq = temp
   deallocate(temp)

   do ib = 1,grid%nbound
      imaxt = max(grid%bc(ib)%nbfacet,1)
      imaxq = max(grid%bc(ib)%nbfaceq,1)
      if (.not.lmpi_master) then
         allocate(grid%bc(ib)%f2ntb(imaxt,5));   grid%bc(ib)%f2ntb     = 0
         allocate(grid%bc(ib)%f2nqb(imaxq,6));   grid%bc(ib)%f2nqb     = 0
         allocate(grid%bc(ib)%face_bit(imaxt));  grid%bc(ib)%face_bit  = 0
         allocate(grid%bc(ib)%face_bitq(imaxq)); grid%bc(ib)%face_bitq = 0
      end if
      call lmpi_bcast(grid%bc(ib)%f2ntb)
      call lmpi_bcast(grid%bc(ib)%f2nqb)
      call lmpi_bcast(grid%bc(ib)%face_bit)
      call lmpi_bcast(grid%bc(ib)%face_bitq)
   end do

!------------------------------------------------------
! Update c2n_info n_ and c_ (note original values saved in m_ and d_).
! c2n cell values just double; but node values vary.

  allocate(my_counts(lmpi_nproc))
  call lmpi_gather(grid%nnodes0,my_counts)
  call lmpi_bcast(my_counts)
  pp_nsize = my_counts
  deallocate(my_counts)

  !write(*,*)"ph,pt ",lmpi_id,pp_nhead,pp_ntail

  pp_nhead(0) = 1
  pp_ntail(0) = (pp_nhead(0) + pp_nsize(0))-1
  do i = 1,lmpi_nproc-1
     pp_nhead(i) = pp_ntail(i-1) + 1
     pp_ntail(i) = (pp_nhead(i) + pp_nsize(i))-1
  end do

  do i = 0,lmpi_nproc-1
     pp_chead(i) = (pp_chead(i)*2)-1
    pp_ctail(i) = pp_ctail(i)*2
    pp_csize(i) = pp_csize(i)*2
  end do

 end subroutine mirror_grid_par


!=============================== binary_search_lower =========================80
!
! Find the element or nearest lower element to the targetnode.
!
!=============================================================================80

  integer function binary_search_lower(nnodes,sortednodes,targetnode)

    integer,                    intent(in) :: nnodes
    integer, dimension(nnodes), intent(in) :: sortednodes
    integer,                    intent(in) :: targetnode

    integer :: k,lower,upper,mid

    continue

    binary_search_lower = 0
    if (nnodes <= 0) return
    if (targetnode < sortednodes(1)) return
    if (targetnode >= sortednodes(nnodes)) then
       binary_search_lower = nnodes
       return
    end if

    lower = 1
    upper = nnodes
    mid = (nnodes+1)/2

    do while ((lower < mid) .and. (mid < upper))
      if ( targetnode <= sortednodes(mid) ) then
        if ( targetnode == sortednodes(mid))then
          binary_search_lower = mid
          return
        end if
        upper = mid
      else
        lower = mid
      end if
      mid = (lower+upper)/2
    end do

    if (targetnode >= sortednodes(lower)) then
       k = lower
    else
       k = upper
    end if
    binary_search_lower = k
    if (targetnode < sortednodes(k))                                           &
       write(*,*)"Error binary_search_lower 1",targetnode,sortednodes(k)
    if (k > 1) then
       if (targetnode < sortednodes(k-1))                                      &
          write(*,*)"Error binary_search_lower 2",targetnode,sortednodes(k)
    end if

  end function binary_search_lower

!=============================== omap ========================================80
!
! Return the offset into the mirrored global numbering
!
!=============================================================================80

  integer function omap(target_value)

    use sort, only : binary_search

    integer, intent(in) :: target_value

    integer :: k

    continue

      k = binary_search(sym_num,sym_val,target_value)
      if (k > 0) then
         omap = target_value
         return
      end if

      k = binary_search_lower(sym_num,sym_val,target_value)
      if (k > 0) then
         omap = sym_off(k) + (target_value-sym_val(k))
      else
         omap = target_value + sym_nnodesg_org
      end if

  end function omap

!============================= PUNS3D_EXPAND_XYZ_LEVEL1 ======================80
!
! Expand grid: l2g and xyz to level1.
!
!=============================================================================80

  subroutine puns3d_expand_xyz_level1(grid)

    use grid_types,  only : grid_type
    use allocations, only : my_realloc_ptr
    use lmpi,        only : lmpi_conditional_stop

    type(grid_type), intent(inout) :: grid

    integer  :: bsize, tag_size, i, j, icell, ielem, npc, word, ierr
    integer  :: nnodes0, nnodes1, ict, inode, is, ie, ipe, last, iostat
    logical  :: b1
    real(dp) :: bsize_inv

    integer(system_i1), dimension(:), allocatable :: tag
    real(dp), dimension(:), allocatable :: tempx,tempy,tempz

    continue

    nnodes0 = size(grid%l2g)
    if ( nnodes0 == 0 ) then
      call lmpi_conditional_stop(1,&
        "zero nodes on part, use less cores, puns3d_expand_xyz_level1")
    else
      call lmpi_conditional_stop(0,&
        "zero nodes on part, use less cores, puns3d_expand_xyz_level1")
    end if
    is = grid%l2g(1)
    ie = grid%l2g(nnodes0)

    bsize     = bit_size(tag)
    bsize_inv = 1._dp/bit_size(tag)
    tag_size = ceiling(grid%nnodesg*bsize_inv)

    allocate(tag(tag_size),stat=iostat)
    if (iostat /= 0) write(*,*)"Bad alloc (tag)"
    do i = 1,tag_size
       tag(i) = 0
    end do

    do ielem = 1,grid%nelem
       npc = grid%elem(ielem)%node_per_cell
       do icell = 1,grid%elem(ielem)%ncell
          do i = 1,npc
             inode = grid%elem(ielem)%c2n(i,icell)
             if ((inode < is).or.(inode > ie)) then
                word = ceiling(inode*bsize_inv)
                tag(word) = ibset(tag(word), mod(inode,bsize))
             end if
          end do
       end do
    end do

    ! Count them
    ict   = 0
    inode = 1
    do word = 1,tag_size
       i  = tag(word)
       do j = 1,bsize
          if ((inode < is).or.(inode > ie)) then
             b1 = btest(i, mod(inode,bsize))
             if (b1) ict = ict + 1
          end if
          inode = inode + 1
       end do
    end do

    nnodes1 = nnodes0 + ict
    grid%nnodes01  = nnodes1
    call my_realloc_ptr(grid%l2g,nnodes1)
    call my_realloc_ptr(grid%x,nnodes1)
    call my_realloc_ptr(grid%y,nnodes1)
    call my_realloc_ptr(grid%z,nnodes1)

    ! Fill l2g (level1)
    ict   = 0
    inode = 1
    do word = 1,tag_size
       i = tag(word)
       do j = 1,bsize
          if ((inode < is).or.(inode > ie)) then
             b1 = btest(i, mod(inode,bsize))
             if (b1) then
                ict = ict + 1
                grid%l2g(nnodes0+ict) = inode
             end if
          end if
          inode = inode + 1
       end do
    end do
    deallocate(tag,stat=iostat)
    if (iostat/=0) write(*,*)'Bad dealloc (tag)'

    ierr = 0
    if (ict+nnodes0 /= nnodes1) then
       ierr = 1
       write(*,*)"Error in Expanding xyz ",lmpi_id,ict+nnodes0,nnodes1
    end if
    call lmpi_conditional_stop(ierr)

    ! Collect xyz (level1)
    last = nnodes0+1
    is   = 1
    do ipe = 0,lmpi_nproc-1
       if (lmpi_id == ipe) ie = grid%l2g(nnodes0)
       call lmpi_bcast(ie,ipe)
       allocate(tempx(is:ie)); tempx = 0.0_dp
       allocate(tempy(is:ie)); tempy = 0.0_dp
       allocate(tempz(is:ie)); tempz = 0.0_dp
       if (lmpi_id == ipe) then
          tempx(is:ie) = grid%x(1:nnodes0)
          tempy(is:ie) = grid%y(1:nnodes0)
          tempz(is:ie) = grid%z(1:nnodes0)
       end if

       call lmpi_bcast(tempx,ipe)
       call lmpi_bcast(tempy,ipe)
       call lmpi_bcast(tempz,ipe)

       if ((ict > 0).and.(last <= nnodes1)) then
          do i = is,ie
             if (i == grid%l2g(last)) then
                grid%x(last) = tempx(i)
                grid%y(last) = tempy(i)
                grid%z(last) = tempz(i)
                last = last + 1
                if (last > nnodes1) exit
             end if
          end do
       end if
       deallocate(tempx,tempy,tempz)

       is = ie + 1
    end do

!do i = 1,grid%nnodes
!   write(25000+lmpi_id,'(1x,i0," : ",3(F20.12,1x))')                          &
!     grid%l2g(i),grid%x(i),grid%y(i),grid%z(i)
!end do

  end subroutine puns3d_expand_xyz_level1

!=============================== READ_MAPBC ==================================80
!
! Reads the mapbc. Stores module variables: nbctags, tagmap, family
!
! FUN2D: processed elsewhere (coupled to grid)
!
! FIELDVIEW: if does exist, create elsewhere. (coupled to other info)
!
!=============================================================================80

  subroutine read_mapbc(raw_grid_data,grid,flow_dir)

    use grid_types,  only : grid_type, raw_grid_data_type
    use periodics,   only : periodic
    use multiblocks, only : multiblock
    use bc_names,    only : bc_is_periodic, block_interface, filter
    use info_depr,   only : ivisc
    use bc_names,    only : viscous_solid,farfield_riem

    type(raw_grid_data_type), intent(in) :: raw_grid_data
    type(grid_type),          intent(in) :: grid
    character(*),             intent(in) :: flow_dir

    integer  :: i, iostat, col
    logical  :: exist
    character (len=80)  :: mapbcfilename
    character (len=120) :: string

     integer, parameter :: unit_mapbc = 63

    continue

    if (trim(raw_grid_data%grid_format) == 'fun2d') return

    if (lmpi_master) then
       if (trim(raw_grid_data%grid_format) /= 'felisa') then
          mapbcfilename = trim(flow_dir) // trim(grid%project) // '.mapbc'
       else
          mapbcfilename = trim(flow_dir) // trim(grid%project) // '.bco'
       end if
       inquire (file=mapbcfilename,exist=exist)
    end if
    call lmpi_bcast(exist)

    if ((.not.exist).and.(trim(raw_grid_data%grid_format)=='fieldview')) return

    if (lmpi_master) then
       call se_open(unit_mapbc,file=mapbcfilename,                             &
                    form='formatted',status='old',iostat=iostat)
       if ( iostat /= 0 ) then
          write(*,*) lmpi_id,unit_mapbc
          write(*,*)'error opening ',trim(mapbcfilename),' stopping...'
          call lmpi_conditional_stop(1)
       endif
       rewind (unit_mapbc)

! determine nbctags, and position file for tagmap

       col = 3 ! Column in which family appears

       select case (trim(raw_grid_data%grid_format))
         case ('vgrid')
           col = 6
           rewind(unit_mapbc) ! Skip 1st 4 lines
           read(unit_mapbc,*); read(unit_mapbc,*)
           read(unit_mapbc,*); read(unit_mapbc,*)
           nbctags = 0
           do
             read(unit_mapbc,*,end=10,err=11)
             nbctags = nbctags + 1
           end do
10         continue
           if (nbctags == 0) then
              write(*,*)'stopping...error reading mapbc file'
              call lmpi_conditional_stop(1)
            end  if

           rewind(unit_mapbc) ! Skip 1st 4 lines
           read(unit_mapbc,*); read(unit_mapbc,*)
           read(unit_mapbc,*); read(unit_mapbc,*)

         case ('fast','aflr3','fieldview')
           read (unit_mapbc,*) nbctags ! No. of BC's

         case ('felisa')
           read (unit_mapbc,*) ! text
           read (unit_mapbc,*) nbctags
           read (unit_mapbc,*) ! text

         case default
           write(*,*)"Read_mapbc error. Invalid grid_format ",                 &
                     trim(raw_grid_data%grid_format)
           call lmpi_conditional_stop(1)
         end select

     end if ! lmpi_master
     call lmpi_conditional_stop(0)

     call lmpi_bcast(nbctags)
     nbctags_org = nbctags

     if (allocated(tagmap))     deallocate(tagmap)
     if (allocated(tagmap_org)) deallocate(tagmap_org)
     if (allocated(family_map)) deallocate(family_map)
     if (allocated(family_org)) deallocate(family_org)

     allocate(tagmap(nbctags,2));     tagmap     = 0
     allocate(tagmap_org(nbctags,2)); tagmap_org = 0
     allocate(family_map(nbctags));   family_map = ''
     allocate(family_org(nbctags));   family_org = ''

     if (lmpi_master) then
       do i = 1,nbctags
         read (unit_mapbc,*) tagmap(i,1), tagmap(i,2)
         tagmap_org(i,1) = tagmap(i,1)
         tagmap_org(i,2) = tagmap(i,2)
         if (trim(raw_grid_data%grid_format) == 'felisa') then
            tagmap(i,2) = felbc2funbc(tagmap(i,2))
         end if
         call backwards_compatible_bc(tagmap(i,2))
         backspace(unit_mapbc)
         read(unit_mapbc,'(a120)') string
         call extract_family(col,string,family_org(i))
         family_map(i) = family_org(i)
         if (ivisc == 0 .and. (tagmap(i,2) >= viscous_solid .and.              &
                               tagmap(i,2) < farfield_riem)) then
            write(*,*)"Read_mapbc error. Viscous BC given for Inviscid run!"
            call lmpi_conditional_stop(1)
         end if
        !write(*,*)"TAGS ",tagmap(i,:),trim(family_map(i))
       end do
      !write(*,*)"NBCTAGS.1 ",nbctags
    end if
    call lmpi_conditional_stop(0)
    call lmpi_bcast(tagmap)
    call lmpi_bcast(tagmap_org)
    do i = 1,nbctags
       call lmpi_bcast(family_map(i))
       call lmpi_bcast(family_org(i))
       if ( bc_is_periodic(tagmap(i,2)) ) periodic = .true.
       if ( tagmap(i,2) == block_interface                                     &
       .or. tagmap(i,2) == filter ) multiblock = .true.
    end do
    return

11  continue
    write(*,*)"Error reading VGRID mapbc."
    call lmpi_conditional_stop(1)

  end subroutine read_mapbc

!=============================== EXTRACT_FAMILY ==============================80
!
! Extrace family from string, where family is expected to begin at column
! "col" of a string with blanks delimited values.
!
!=============================================================================80

  subroutine extract_family(col,string,family)

     use string_utils, only : strip, squeeze

     integer,          intent(in)  :: col
     character(len=*), intent(in)  :: string
     character(len=*), intent(out) :: family

     integer :: cnt,beg,end,last,i,j,k,m,n
     character(len=256) :: tstring

     continue

       tstring = string
       tstring = squeeze(tstring)
       tstring = strip(tstring)
!      write(*,*)'S "',trim(string),'" ',col
!      write(*,*)'TS "',trim(tstring),'"'

! Test for old vgrid data, which does not always put a blank between
! columns 5 and 6.

       last = 0

       if (col == 6) then
          do i = len(tstring),1,-1
             if (tstring(i:i) /= ' ') then
                last = i
                exit
             end if
          end do
          cnt = 0
          do i = 1,last
             if (tstring(i:i) == ' ') cnt = cnt + 1
          end do
          if (cnt == 4) then ! probably old vgrid
             !write(*,*)"OLD tstring (",tstring(1:last),")"
             out1 : do i = last,1,-1 ! find last blank
                if (tstring(i:i) == ' ') then
                   m = i+1
                   !write(*,*)"m = ",m,last,"(",tstring(m:last),")"
                   do j = m,last
                      k = index('0123456789',tstring(j:j))
                      if (k == 0) then
                         !write(*,*)"J = ",j
                         do n = last,j,-1
                            tstring(n+1:n+1) = tstring(n:n)
                         end do
                         tstring(j:j) = ' '
                         exit out1
                      end if
                   end do
                end if
             end do out1
             !write(*,*)"NEW tstring (",trim(tstring),")"
          end if
       end if

       cnt = 0
       beg = 0
       end = 0
       last = 0
       do m = 1,len(tstring)
          if (tstring(m:m) == ' ') then
             cnt  = cnt + 1
             if (cnt == col-1) beg = m
             if (cnt == col) then
                end = m-1
                exit
             end if
          else
             last = m
          end if
       end do
       if ((beg > 0).and.(end == 0)) end = last

       family = ''
       if (end /= 0) then
          beg=beg+1
          family = tstring(beg:end)
!         write(*,*)'Family = "',trim(family),'" ',beg,end
       end if

   end subroutine extract_family

!========================= MIRROR_MIRROR_ON_THE_WALL =========================80
!
! Enforces points on a specified symmetry plane are on the symmetry plane
! (assumed to be located at x=0 for symmetry_x, etc.)
!
!=============================================================================80

  subroutine mirror_mirror_on_the_wall(nbound, bc, x, y, z, is, ie)

    use info_depr, only : mirror_x, mirror_y, mirror_z
    use bc_types,  only : bcgrid_type
    use bc_names,  only : symmetry_x, symmetry_y, symmetry_z
    use lmpi,      only : lmpi_master, lmpi_reduce, lmpi_max, lmpi_bcast,      &
                          lmpi_conditional_stop
    use kinddefs,  only : dp

    integer, intent(inout) :: nbound

    real(dp), dimension(:), pointer    :: x, y, z

    type(bcgrid_type), dimension(:), pointer :: bc

    integer, optional, intent(in) :: is,ie ! Index range for x,y,z

    integer :: inode, ib, is1, i_val
    logical :: iDM
    integer :: iface, nd, repairs, repairs_total

    real(dp)    :: tolerance, departure, r_val
    real(dp)    :: stop_tolerance, max_departure

    real(dp), parameter    :: my_0 = 0.0_dp

  continue

    is1 = -999
    iDM = present(is)
    if (iDM) is1 = is - 1

    tolerance = epsilon(1.0_dp)

    repairs_total = 0
    departure     = my_0
    max_departure = my_0
    repairs       = 0

    if (mirror_x) then
      do ib=1,nbound

        if(bc(ib)%ibc /= symmetry_x) cycle

        do iface=1,bc(ib)%nbfacet
          do nd =1,3
            inode = bc(ib)%f2ntb(iface,nd)
            if (iDM) then
               if ((inode < is).or.(inode > ie)) cycle
               inode = inode - is1
            end if
            if(abs(x(inode)) > tolerance) then
              departure = max( departure , abs( x(inode) ) )
              x(inode) = my_0
              repairs = repairs + 1
            endif
          end do
        end do

        do iface=1,bc(ib)%nbfaceq
          do nd =1,4
            inode = bc(ib)%f2nqb(iface,nd)
            if (iDM) then
               if ((inode < is).or.(inode > ie)) cycle
               inode = inode - is1
            end if
            if(abs(x(inode)) > tolerance) then
              departure = max( departure , abs( x(inode) ) )
              x(inode) = my_0
              repairs = repairs + 1
            endif
          end do
        end do

      end do
    end if

    i_val = repairs
    call lmpi_reduce(i_val,repairs)
    r_val = departure
    call lmpi_max(r_val,departure)

    if (lmpi_master) then

       if (repairs > 0) then
         write(*,*)
         write(*,*) ' Repairs : x-symmetry plane ', repairs
         write(*,*) ' ...maximum x-departure = ',departure
       else
         if (iDM) write(*,*)'No repairs X'
       endif

       repairs_total = repairs_total + repairs
       max_departure = max( max_departure , departure )

    end if

    departure     = my_0
    repairs       = 0

    if (mirror_y) then
      do ib=1,nbound

        if(bc(ib)%ibc /= symmetry_y) cycle

        do iface=1,bc(ib)%nbfacet
          do nd =1,3
            inode = bc(ib)%f2ntb(iface,nd)
            if (iDM) then
               if ((inode < is).or.(inode > ie)) cycle
               inode = inode - is1
            end if
            if(abs(y(inode)) > tolerance) then
              departure = max( departure , abs( y(inode) ) )
              y(inode) = my_0
              repairs = repairs + 1
            endif
          end do
        end do

        do iface=1,bc(ib)%nbfaceq
          do nd =1,4
            inode = bc(ib)%f2nqb(iface,nd)
            if (iDM) then
               if ((inode < is).or.(inode > ie)) cycle
               inode = inode - is1
            end if
            if(abs(y(inode)) > tolerance) then
              departure = max( departure , abs( y(inode) ) )
              y(inode) = my_0
              repairs = repairs + 1
            endif
          end do
        end do

      end do
    end if

    i_val = repairs
    call lmpi_reduce(i_val,repairs)
    r_val = departure
    call lmpi_max(r_val,departure)

    if (lmpi_master) then

       if (repairs > 0) then
         write(*,*)
         write(*,*) ' Repairs : y-symmetry plane ', repairs
         write(*,*) ' ...maximum y-departure = ',departure
       else
         if (iDM) write(*,*)'No repairs Y'
       endif

       repairs_total = repairs_total + repairs
       max_departure = max( max_departure , departure )

    end if

    departure     = my_0
    repairs       = 0

    if (mirror_z) then
      do ib=1,nbound

        if(bc(ib)%ibc /= symmetry_z) cycle

        do iface=1,bc(ib)%nbfacet
          do nd =1,3
            inode = bc(ib)%f2ntb(iface,nd)
            if (iDM) then
               if ((inode < is).or.(inode > ie)) cycle
               inode = inode - is1
            end if
            if(abs(z(inode)) > tolerance) then
              departure = max( departure , abs( z(inode) ) )
              z(inode) = my_0
              repairs = repairs + 1
            endif
          end do
        end do

        do iface=1,bc(ib)%nbfaceq
          do nd =1,4
            inode = bc(ib)%f2nqb(iface,nd)
            if (iDM) then
               if ((inode < is).or.(inode > ie)) cycle
               inode = inode - is1
            end if
            if(abs(z(inode)) > tolerance) then
              departure = max( departure , abs( z(inode) ) )
              z(inode) = my_0
              repairs = repairs + 1
            endif
          end do
        end do

      end do
    end if

    i_val = repairs
    call lmpi_reduce(i_val,repairs)
    r_val = departure
    call lmpi_max(r_val,departure)

    if (lmpi_master) then

       if (repairs > 0) then
         write(*,*)
         write(*,*) ' Repairs : z-symmetry plane ', repairs
         write(*,*) ' ...maximum z-departure = ', departure
       else
         if (iDM) write(*,*)'No repairs Z'
       end if

       repairs_total = repairs_total + repairs
       max_departure = max( max_departure , departure )

       if (repairs_total  == 0 .and. skeleton > 0) then
         write(*,*)
         write(*,*) ' You have the fairest symmetry in all the land!'
         write(*,*)
       elseif (repairs_total > 0) then
         write(*,*)
         write(*,*) ' !!!!!!!!!!!! WARNING !!!!!!!!!!!!'
         write(*,*) ' Grid points moved to the intended symmetry planes.'
         write(*,*) ' Points with x_symmetry are assumed to have x=0.'
         write(*,*) ' Points with y_symmetry are assumed to have y=0.'
         write(*,*) ' Points with z_symmetry are assumed to have z=0.'
         write(*,*) ' ...Points moved outside tolerance =',tolerance
         write(*,*) ' ...Total number of points moved =',repairs_total
         write(*,*) ' !!!!!!!!!!!! WARNING !!!!!!!!!!!!'
         write(*,*)
       endif

    end if

!   Stop if we have extreme variation from 0 on symmetry_plane

    stop_tolerance = 0.1_dp
    call lmpi_bcast(max_departure)

    if (max_departure > stop_tolerance) then
      if (lmpi_master) then
         write(*,*)
         write(*,*) ' Grid point departure > tolerance=',stop_tolerance
         write(*,*) ' ...Stopping in mirror_mirror_on_the_wall.'
         write(*,*)
      end if
      call lmpi_conditional_stop(1,'mirror_mirror_on_the_wall')
    endif
    call lmpi_conditional_stop(0,'mirror_mirror_on_the_wall')

  end subroutine mirror_mirror_on_the_wall

!========================== read_cogsg_dim ===================================80
!
! Read cogsg dimensions (ntet,nnodesg,nseg); either version 3 or 4.
!
!=============================================================================80

  subroutine read_cogsg_dim(unit_grid,ntet,nnodesg,nseg,error_code)

    integer, intent(in)  :: unit_grid
    integer, intent(out) :: ntet, nnodesg, nseg
    integer, intent(out) :: error_code

    integer :: nelec, npoic, inew

    continue

    error_code = 0

    nseg = 1

    read(unit_grid,end=200,err=300) inew,ntet,nnodesg
    read(unit_grid,end=200,err=301) ! ((coord(ip,id),ip=npois,npoie),id=1,ndimn)
    if (inew == -1) goto 500 ! -inew is # of segments.

    do

      read(unit_grid,end=500) nelec
      if (nelec == 0) goto 500
      ntet = ntet + nelec
      read(unit_grid,end=200) ! ((intmat(ie,in),ie=neles,nelee),in=1,nnode)

      read(unit_grid,end=200)npoic
      nnodesg = nnodesg + npoic
      read(unit_grid,end=200) ! ((coord(ip,id),ip=npois,npoie),id=1,ndimn)

      nseg = nseg + 1

    end do

 200  continue
      write(*,*)'read_cogsg_dim: unexpected end encountered.'
      error_code = 1
      return

 301  continue
      write(*,*)'read_cogsg_dim: read of coord record failed.'
 300  continue
      write(*,*)'read_cogsg_dim: read of header record failed.'
      write(*,*)'read_cogsg_dim: check the endian-ness of vgrid I/O.'
      error_code = 1
      return

 500  continue
      if (-inew /= nseg) write(*,*) 'Invalid # of segments in cogsg.'

  end subroutine read_cogsg_dim

!================================= GET_FACEPTR ===============================80
!
! Determines contributions to the faceptr array from faces aligned with the
! 2D direction
!
!=============================================================================80

  subroutine get_faceptr(nnodes2d, nedge2d, eptr2d, nface, faceptr,            &
                         facetag, nedges2d, iedge2d, facecount, seg_tagmap,    &
                         iplane)

    integer,                       intent(in)    :: nnodes2d
    integer,                       intent(in)    :: nedge2d, nface
    integer,                       intent(in)    :: nedges2d, iplane
    integer,                       intent(in)    :: seg_tagmap
    integer, dimension(nedge2d,4), intent(in)    :: eptr2d
    integer, dimension(nedges2d),  intent(in)    :: iedge2d
    integer,                       intent(inout) :: facecount
    integer, dimension(nface,4),   intent(inout) :: faceptr
    integer, dimension(nface),     intent(inout) :: facetag

    integer :: iedge, n
    integer :: node1, node2

    continue

    do n = 1,nedges2d

      iedge = iedge2d(n)
      node1 = eptr2d(iedge,1) + (iplane-1)*nnodes2d
      node2 = eptr2d(iedge,2) + (iplane-1)*nnodes2d

      facecount = facecount + 1

      faceptr(facecount,1) = node2
      faceptr(facecount,2) = node1
      faceptr(facecount,3) = faceptr(facecount,2) + nnodes2d
      faceptr(facecount,4) = faceptr(facecount,1) + nnodes2d
      facetag(facecount)   = seg_tagmap

    end do

  end subroutine get_faceptr

!=============================== GET_SYMFACEPTR ==============================80
!
! Determines symmetry plane contributions to faceptr array from fun2d grid data
!
!=============================================================================80

  subroutine get_symfaceptr(ncell2d, nnodes2d, nedge2d, eptr2d, ntface,        &
                            faceptr, symface, facetag, sym_tagmap1,            &
                            sym_tagmap2, nplanes)

    integer,                       intent(in)  :: ncell2d, nnodes2d
    integer,                       intent(in)  :: sym_tagmap1, sym_tagmap2
    integer,                       intent(in)  :: nedge2d, ntface, nplanes
    integer,                       intent(out) :: symface
    integer, dimension(nedge2d,4), intent(in)  :: eptr2d
    integer, dimension(ntface,4),  intent(out) :: faceptr
    integer, dimension(ntface),    intent(out) :: facetag

    integer :: iedge, iface, inode
    integer :: node1, node2, face1, face2
    integer :: nfaces2d

    integer, dimension(ntface) :: tag

    continue

    faceptr  = 0
    tag      = 0
    nfaces2d = 0

!   first get the facptr array for the one boundary that is defined by the
!   fun2d grid

    do iedge=1,nedge2d

      node1 = eptr2d(iedge,1)
      node2 = eptr2d(iedge,2)
      face1 = eptr2d(iedge,3)
      face2 = eptr2d(iedge,4)

      nfaces2d = max(nfaces2d, face1, face2)

      if (tag(face1) /= 0.and.faceptr(face1,3) == 0) then
        if (node1 == faceptr(face1,1).or.node1 == faceptr(face1,2)) then
          faceptr(face1,3) = node2
        else if (node2 == faceptr(face1,1).or.node2 == faceptr(face1,2)) then
          faceptr(face1,3) = node1
        end if
      end if
      if (tag(face2) /= 0.and.faceptr(face2,3) == 0) then
        if (node1 == faceptr(face2,1).or.node1 == faceptr(face2,2)) then
          faceptr(face2,3) = node2
        else if (node2 == faceptr(face2,1).or.node2 == faceptr(face2,2)) then
          faceptr(face2,3) = node1
        end if
      end if
      if (tag(face1) == 0) then
        faceptr(face1,1) = node1
        faceptr(face1,2) = node2
        tag(face1) = 1
      end if
      if (tag(face2) == 0.and.face2 <= ncell2d) then
        faceptr(face2,1) = node2
        faceptr(face2,2) = node1
        tag(face2) = 1
      end if

    end do

!   this faceptr array formed above has the fun2d ghost faces in them
!   these faces have node3 = 0 - toss these out, as we don't need them

    symface = 0
    do iface = 1,nfaces2d
      if (faceptr(iface,3) /= 0) then
        symface = symface + 1
        do inode = 1,3
          faceptr(symface,inode) = faceptr(iface,inode)
        end do
      end if
    end do

!   now get the facptr array for the opposite boundary that is obtained
!   by extrusion (note: the reordering of inode 1,2,3 --> 1,3,2 is done
!   to maintain righthandedness

    do iface = 1,symface
      do inode = 1,3
        faceptr(iface+symface,1) = faceptr(iface,1) + nnodes2d*(nplanes-1)
        faceptr(iface+symface,3) = faceptr(iface,2) + nnodes2d*(nplanes-1)
        faceptr(iface+symface,2) = faceptr(iface,3) + nnodes2d*(nplanes-1)
      end do
    end do

!   set the 4th node pointer to zero for these triangular faces

    do iface = 1,2*symface
      faceptr(iface,4) = 0
    end do

    do iface = 1,symface
      facetag(iface)         = sym_tagmap1
      facetag(iface+symface) = sym_tagmap2
    end do

  end subroutine get_symfaceptr

!=============================================================================80
! function felbc2funbc
!=============================================================================80

  integer function felbc2funbc(ibc)

    use bc_names, only : symmetry_y, farfield_riem, tangency
    use lmpi,     only : lmpi_die

    integer, intent(in) :: ibc

    continue

!   FELISA boundary condition markers
!   Inviscid surface - BC 1
!   symmetry plane   - BC 2
!   Extrapolation    - BC 3

    select case(ibc)
      case(1)
        felbc2funbc = tangency
      case(2)
        felbc2funbc = symmetry_y
      case(3)
!       NOTE:  use regular far-field, change in .bco to 5005/extrapolation
!         where needed.
        felbc2funbc = farfield_riem

      case(101:)
!       NOTE:  bc numbers greater than 100 "flow thru"
        felbc2funbc = ibc
      case default
        write(*,*) 'FATAL ERROR: felisa bc type unknown: ', ibc
        call lmpi_die
        felbc2funbc = -99
    end select

  end function felbc2funbc

!============================= DELETE_FACEPTR ================================80
!
! Deallocate local variables that span multiple modules.
!
!=============================================================================80

  subroutine delete_faceptr()

    continue

      if (allocated(faceptr)) deallocate(faceptr)
      if (allocated(facetag)) deallocate(facetag)
      if (allocated(tagmap))  deallocate(tagmap)
      if (allocated(tagmap_org)) deallocate(tagmap_org)

  end subroutine delete_faceptr

end module puns3d_io_c2n

DIST_SUBDIRS =

include Common.am

lib_LIBRARIES = libcore.a

libcore_a_SOURCES = $(libcore_SRCS) $(libcore_deps)
libcore_a_LIBADD =
libcore_a_LINK = $(F90LINK)

UNIT_TESTS = \
	allocations.fun \
	ddt.fun \
	file_utils.fun \
	linear_algebra.fun \
	string_utils.fun \
	sort.fun \
	system_extensions.fun \
	utilities.fun

EXTRA_DIST = \
	$(UNIT_TESTS) \
	fortran_template.rb \
	lmpi.template \
	lmpi_app.template \
	remove_stalemods.sh

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ > $@

libMODULES_INSTALL = $(INSTALL_DATA)
install-exec-local:
	@$(NORMAL_INSTALL)
	test -z "$(libdir)" || $(mkdir_p) "$(DESTDIR)$(libdir)"
	@list="*.$(FC_MODEXT)"; for p in $$list; do \
	  if test -f $$p; then \
	    f=$(am__strip_dir) \
	    echo " $(libMODULES_INSTALL) '$$p' '$(DESTDIR)$(libdir)/$$f'"; \
	    $(libMODULES_INSTALL) "$$p" "$(DESTDIR)$(libdir)/$$f"; \
	  else :; fi; \
	done
	@$(POST_INSTALL)
	@if test -e Complex/libcore.a; then \
	  (cd Complex; $(MAKE) install) \
	else :; fi;

uninstall-local:
	@$(NORMAL_UNINSTALL)
	@list="*.$(FC_MODEXT)"; for p in $$list; do \
	  p=$(am__strip_dir) \
	  echo " rm -f '$(DESTDIR)$(libdir)/$$p'"; \
	  rm -f "$(DESTDIR)$(libdir)/$$p"; \
	done
	@if test -e Complex/libcore.a; then \
	  (cd Complex; $(MAKE) uninstall) \
	else :; fi;

if BUILD_COMPLEX
DIST_SUBDIRS += Complex
endif

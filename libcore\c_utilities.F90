module c_utilities

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  implicit none

  private

  public :: cstring

  contains

!==================================== CSTRING ================================80
!
! Convert a standard Fortran character array into a NULL terminated string
! suitable for passing to a C function via Fortran 2003 Interoperability with C
!
!=============================================================================80

  function cstring(string) result(array)

#ifdef HAVE_FORTRAN_C_INTEROPERABILITY
    use iso_c_binding, only : c_char, c_null_char
#endif

    character(len=*),                                 intent(in)    :: string
#ifdef HAVE_FORTRAN_C_INTEROPERABILITY
    character(kind=c_char), dimension(len_trim(string)+1)           :: array
#else
    character, dimension(len_trim(string)+1)                        :: array
#endif

    integer                                                         :: i

    continue

    do i=1, len_trim(string)
      array(i)=string(i:i)
    end do
#ifdef HAVE_FORTRAN_C_INTEROPERABILITY
    array(len_trim(string)+1)=c_null_char
#else
    array(len_trim(string)+1)=char(0)
#endif

  end function cstring

end module c_utilities

module force_types

  use kinddefs,        only : dp

  implicit none

  private

  public :: force_type
  public :: nullify_force, deallocate_force, reallocate_totforce
  public :: zero_force

  type force_type
    logical                            :: add_to_total ! Add these contributions
                                                       ! to force totals
    real(dp)                           :: cl    ! Lift
    real(dp)                           :: cd    ! Drag
    real(dp)                           :: clp   ! Pressure lift
    real(dp)                           :: cdp   ! Pressure drag
    real(dp)                           :: clv   ! Viscous lift
    real(dp)                           :: cdv   ! Viscous drag
    real(dp)                           :: clf   ! Flux lift
    real(dp)                           :: cdf   ! Flux drag
    real(dp)                           :: cmx   ! x-moment
    real(dp)                           :: cmy   ! y-moment
    real(dp)                           :: cmz   ! z-moment
    real(dp)                           :: cmxp  ! Pressure x-moment
    real(dp)                           :: cmyp  ! Pressure y-moment
    real(dp)                           :: cmzp  ! Pressure z-moment
    real(dp)                           :: cmxv  ! Viscous x-moment
    real(dp)                           :: cmyv  ! Viscous y-moment
    real(dp)                           :: cmzv  ! Viscous z-moment
    real(dp)                           :: cmxf  ! Flux x-moment
    real(dp)                           :: cmyf  ! Flux y-moment
    real(dp)                           :: cmzf  ! Flux z-moment
    real(dp)                           :: cx    ! x-force
    real(dp)                           :: cy    ! y-force
    real(dp)                           :: cz    ! z-force
    real(dp)                           :: cxp   ! Pressure x-force
    real(dp)                           :: cyp   ! Pressure y-force
    real(dp)                           :: czp   ! Pressure z-force
    real(dp)                           :: cxv   ! Viscous x-force
    real(dp)                           :: cyv   ! Viscous y-force
    real(dp)                           :: czv   ! Viscous z-force
    real(dp)                           :: cxf   ! Flux x-force
    real(dp)                           :: cyf   ! Flux y-force
    real(dp)                           :: czf   ! Flux z-force
    real(dp)                           :: clcd  ! Lift-to-drag ratio
    real(dp)                           :: fom   ! Rotorcraft figure of merit**2
    real(dp)                           :: propeff! Rotorcraft prop efficiency
    real(dp)                           :: powerx ! Power-x
    real(dp)                           :: powery ! Power-y
    real(dp)                           :: powerz ! Power-z
    real(dp)                           :: rotor_thrust! Rotor thrust
    real(dp)                           :: heat  ! Heating
    real(dp)                           :: mass  ! mass flow
    real(dp)                           :: press ! static pressure
    real(dp)                           :: pt    ! total pressure
    real(dp)                           :: tt    ! total temperature
    real(dp)                           :: temp  ! temperature
    real(dp)                           :: rho   ! density
    real(dp)                           :: area  ! area
    real(dp), dimension(:), pointer    :: cp_t  ! Cp on surface trias
    real(dp), dimension(:), pointer    :: cq_t  ! Cq on surface trias
    real(dp), dimension(:), pointer    :: cfx_t ! x-comp of skin fric on trias
    real(dp), dimension(:), pointer    :: cfy_t ! y-comp of skin fric on trias
    real(dp), dimension(:), pointer    :: cfz_t ! z-comp of skin fric on trias
    real(dp), dimension(:), pointer    :: cp_q  ! Cp on surface quads
    real(dp), dimension(:), pointer    :: cq_q  ! Cq on surface quads
    real(dp), dimension(:), pointer    :: cfx_q ! x-comp of skin fric on quads
    real(dp), dimension(:), pointer    :: cfy_q ! y-comp of skin fric on quads
    real(dp), dimension(:), pointer    :: cfz_q ! z-comp of skin fric on quads
    integer,  dimension(:), pointer    :: list ! boundary force list

    real(dp) :: mflux_integral  ! integrated mass flux
    real(dp) :: eflux_integral  ! integrated energy flux
    real(dp) :: weflux_integral ! integrated energy flux (walls)

  end type force_type

contains

!================================= REALLOCATE_TOTFORCE =======================80
!
!  Reallocates the totforce instantiation of the force_type derived type.
!  totforce is an array sized to carry a running history of the integrated
!  forces and moments.
!
!  NOTE: the complete force_type has a number of arrays (pointers) that can
!  store boundary data for Cp, Cf, etc. As these surface arrays are not used in
!  the totforce history, they are *NOT* allocated or populated here,
!  merely nullified
!
!=============================================================================80

  subroutine reallocate_totforce(totforce, new_size)

    type(force_type), dimension(:), pointer :: totforce ! intent(inout)

    integer, intent(in) :: new_size

    integer  :: old_size, i

    type(force_type), dimension(:), allocatable :: tempforce

  continue

    old_size = size(totforce)

    if ( new_size > old_size) then

      allocate(tempforce(new_size))

!     nullify the surface arrays that are part of a general
!     force_type, but don't allocate or populate them

      do i = 1,new_size
      call nullify_force(tempforce(i))
      end do

      do i = 1,old_size

        tempforce(i)%cl      = totforce(i)%cl
        tempforce(i)%cd      = totforce(i)%cd
        tempforce(i)%clp     = totforce(i)%clp
        tempforce(i)%cdp     = totforce(i)%cdp
        tempforce(i)%clv     = totforce(i)%clv
        tempforce(i)%cdv     = totforce(i)%cdv
        tempforce(i)%clf     = totforce(i)%clf
        tempforce(i)%cdf     = totforce(i)%cdf
        tempforce(i)%cmx     = totforce(i)%cmx
        tempforce(i)%cmy     = totforce(i)%cmy
        tempforce(i)%cmz     = totforce(i)%cmz
        tempforce(i)%cmxp    = totforce(i)%cmxp
        tempforce(i)%cmyp    = totforce(i)%cmyp
        tempforce(i)%cmzp    = totforce(i)%cmzp
        tempforce(i)%cmxv    = totforce(i)%cmxv
        tempforce(i)%cmyv    = totforce(i)%cmyv
        tempforce(i)%cmzv    = totforce(i)%cmzv
        tempforce(i)%cmxf    = totforce(i)%cmxf
        tempforce(i)%cmyf    = totforce(i)%cmyf
        tempforce(i)%cmzf    = totforce(i)%cmzf
        tempforce(i)%cx      = totforce(i)%cx
        tempforce(i)%cy      = totforce(i)%cy
        tempforce(i)%cz      = totforce(i)%cz
        tempforce(i)%cxp     = totforce(i)%cxp
        tempforce(i)%cyp     = totforce(i)%cyp
        tempforce(i)%czp     = totforce(i)%czp
        tempforce(i)%cxv     = totforce(i)%cxv
        tempforce(i)%cyv     = totforce(i)%cyv
        tempforce(i)%czv     = totforce(i)%czv
        tempforce(i)%cxf     = totforce(i)%cxf
        tempforce(i)%cyf     = totforce(i)%cyf
        tempforce(i)%czf     = totforce(i)%czf
        tempforce(i)%clcd    = totforce(i)%clcd
        tempforce(i)%fom     = totforce(i)%fom
        tempforce(i)%propeff = totforce(i)%propeff
        tempforce(i)%powerx  = totforce(i)%powerx
        tempforce(i)%powery  = totforce(i)%powery
        tempforce(i)%powerz  = totforce(i)%powerz
        tempforce(i)%rotor_thrust = totforce(i)%rotor_thrust
        tempforce(i)%heat    = totforce(i)%heat
        tempforce(i)%mass    = totforce(i)%mass
        tempforce(i)%press   = totforce(i)%press
        tempforce(i)%pt      = totforce(i)%pt
        tempforce(i)%tt      = totforce(i)%tt
        tempforce(i)%temp    = totforce(i)%temp
        tempforce(i)%rho     = totforce(i)%rho
        tempforce(i)%area    = totforce(i)%area

!       deallocate the old surface array components before we can
!       deallocate totforce

        call deallocate_force(totforce(i))

      end do

      deallocate(totforce)

      allocate(totforce(new_size))

      do i = 1,new_size

!       nullify the surface arrays that are part of a general
!       force_type, but don't allocate or populate them

        call nullify_force(totforce(i))

        totforce(i)%cl      = tempforce(i)%cl
        totforce(i)%cd      = tempforce(i)%cd
        totforce(i)%clp     = tempforce(i)%clp
        totforce(i)%cdp     = tempforce(i)%cdp
        totforce(i)%clv     = tempforce(i)%clv
        totforce(i)%cdv     = tempforce(i)%cdv
        totforce(i)%clf     = tempforce(i)%clf
        totforce(i)%cdf     = tempforce(i)%cdf
        totforce(i)%cmx     = tempforce(i)%cmx
        totforce(i)%cmy     = tempforce(i)%cmy
        totforce(i)%cmz     = tempforce(i)%cmz
        totforce(i)%cmxp    = tempforce(i)%cmxp
        totforce(i)%cmyp    = tempforce(i)%cmyp
        totforce(i)%cmzp    = tempforce(i)%cmzp
        totforce(i)%cmxv    = tempforce(i)%cmxv
        totforce(i)%cmyv    = tempforce(i)%cmyv
        totforce(i)%cmzv    = tempforce(i)%cmzv
        totforce(i)%cmxf    = tempforce(i)%cmxf
        totforce(i)%cmyf    = tempforce(i)%cmyf
        totforce(i)%cmzf    = tempforce(i)%cmzf
        totforce(i)%cx      = tempforce(i)%cx
        totforce(i)%cy      = tempforce(i)%cy
        totforce(i)%cz      = tempforce(i)%cz
        totforce(i)%cxp     = tempforce(i)%cxp
        totforce(i)%cyp     = tempforce(i)%cyp
        totforce(i)%czp     = tempforce(i)%czp
        totforce(i)%cxv     = tempforce(i)%cxv
        totforce(i)%cyv     = tempforce(i)%cyv
        totforce(i)%czv     = tempforce(i)%czv
        totforce(i)%cxf     = tempforce(i)%cxf
        totforce(i)%cyf     = tempforce(i)%cyf
        totforce(i)%czf     = tempforce(i)%czf
        totforce(i)%clcd    = tempforce(i)%clcd
        totforce(i)%fom     = tempforce(i)%fom
        totforce(i)%propeff = tempforce(i)%propeff
        totforce(i)%powerx  = tempforce(i)%powerx
        totforce(i)%powery  = tempforce(i)%powery
        totforce(i)%powerz  = tempforce(i)%powerz
        totforce(i)%rotor_thrust = tempforce(i)%rotor_thrust
        totforce(i)%heat    = tempforce(i)%heat
        totforce(i)%mass    = tempforce(i)%mass
        totforce(i)%press   = tempforce(i)%press
        totforce(i)%pt      = tempforce(i)%pt
        totforce(i)%tt      = tempforce(i)%tt
        totforce(i)%temp    = tempforce(i)%temp
        totforce(i)%rho     = tempforce(i)%rho
        totforce(i)%area    = tempforce(i)%area

      end do

    end if

  end subroutine reallocate_totforce


!=============================== NULLIFY_FORCE ===============================80
!
! Nullifies array pointers in the force type
!
!=============================================================================80

  subroutine nullify_force(force)

    type(force_type), intent(inout) :: force

  continue

    nullify(force%cp_t)
    nullify(force%cq_t)
    nullify(force%cfx_t)
    nullify(force%cfy_t)
    nullify(force%cfz_t)
    nullify(force%cp_q)
    nullify(force%cq_q)
    nullify(force%cfx_q)
    nullify(force%cfy_q)
    nullify(force%cfz_q)
    nullify(force%list)

  end subroutine nullify_force

!=============================== DEALLOCATE_FORCE ============================80
!
! deallocate array pointers in the force type
!
!=============================================================================80

  subroutine deallocate_force(force)

    type(force_type), intent(inout) :: force

    continue

    if (associated(force%cp_t))  deallocate(force%cp_t)
    if (associated(force%cq_t))  deallocate(force%cq_t)
    if (associated(force%cfx_t)) deallocate(force%cfx_t)
    if (associated(force%cfy_t)) deallocate(force%cfy_t)
    if (associated(force%cfz_t)) deallocate(force%cfz_t)
    if (associated(force%cp_q))  deallocate(force%cp_q)
    if (associated(force%cq_q))  deallocate(force%cq_q)
    if (associated(force%cfx_q)) deallocate(force%cfx_q)
    if (associated(force%cfy_q)) deallocate(force%cfy_q)
    if (associated(force%cfz_q)) deallocate(force%cfz_q)
    if (associated(force%list))  deallocate(force%list)

  end subroutine deallocate_force

!================================ ZERO_FORCE =================================80
!
! Initialize force type to zero
!
!=============================================================================80

  subroutine zero_force(force)

    type(force_type),                      intent(inout) :: force

    continue

    force%cl   = 0.0_dp
    force%cd   = 0.0_dp
    force%clp  = 0.0_dp
    force%cdp  = 0.0_dp
    force%clv  = 0.0_dp
    force%cdv  = 0.0_dp
   !force%clf  = 0.0_dp
   !force%cdf  = 0.0_dp

    force%cmx  = 0.0_dp
    force%cmy  = 0.0_dp
    force%cmz  = 0.0_dp
    force%cmxp = 0.0_dp
    force%cmyp = 0.0_dp
    force%cmzp = 0.0_dp
    force%cmxv = 0.0_dp
    force%cmyv = 0.0_dp
    force%cmzv = 0.0_dp
   !force%cmxf = 0.0_dp
   !force%cmyf = 0.0_dp
   !force%cmzf = 0.0_dp

    force%cx   = 0.0_dp
    force%cy   = 0.0_dp
    force%cz   = 0.0_dp
    force%cxp  = 0.0_dp
    force%cyp  = 0.0_dp
    force%czp  = 0.0_dp
    force%cxv  = 0.0_dp
    force%cyv  = 0.0_dp
    force%czv  = 0.0_dp
   !force%cxf  = 0.0_dp
   !force%cyf  = 0.0_dp
   !force%czf  = 0.0_dp

    force%clcd   = 0.0_dp
    force%fom    = 0.0_dp
    force%propeff= 0.0_dp
    force%powerx = 0.0_dp
    force%powery = 0.0_dp
    force%powerz = 0.0_dp
    force%rotor_thrust = 0.0_dp
    force%heat   = 0.0_dp
    force%mass   = 0.0_dp
    force%press  = 0.0_dp
    force%pt     = 0.0_dp
    force%tt     = 0.0_dp
    force%temp   = 0.0_dp
    force%rho    = 0.0_dp
    force%area   = 0.0_dp

    force%mflux_integral = 0.0_dp
    force%eflux_integral = 0.0_dp
    force%weflux_integral = 0.0_dp

  end subroutine zero_force

end module force_types

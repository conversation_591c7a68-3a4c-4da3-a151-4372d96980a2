!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite turb_convection


  integer, parameter  ::        dp = selected_real_kind(15,307)
  integer, parameter  ::       odp = selected_real_kind(6,37)
  integer, parameter  :: system_i8 = selected_int_kind(10)
  integer, parameter  :: system_i1 = selected_int_kind(2)

  real(dp) :: xmr, tke, omega, nu, betastar, dist
  real(dp), parameter :: tol = 1.0e-8_dp

test test_turb_resid_conv


  use grid_types,    only : grid_type
! use soln_types,    only : soln_type
  use info_depr,     only : twod, grad_x_y_z_contents, use_edge_gradients
  use grids,         only : nullify_grid, allocate_grid, deallocate_grid
  use bc_types,      only : bcgrid_type, nullify_bc, allocate_dummy_bc      &
                          , deallocate_bc, bcsoln_type, allocate_bcsoln
  use element_types, only : elem_type
  use allocations,   only : my_alloc_ptr, my_realloc_ptr
  use element_defs,  only : nullify_elem, allocate_elem, initialize_elem    &
                          , deallocate_elem
  use element_defs,  only : max_node_per_cell, max_face_per_cell               &
                          , max_edge_per_cell 
  use element_defs,  only : local_f2n_pyr, local_f2e_pyr, local_e2n_pyr
  use element_defs,  only : local_f2n_hex, local_f2e_hex, local_e2n_hex
  use element_defs,  only : chk_norm_hex, chk_norm_pyr

  use fluid,         only : setup_fluid_gamma, setup_sutherland_constant
  use thermo,        only : q_type, primitive_q_type
  use bc_names,      only : twall
  use turbulence_info, only : turbulence_model_int, kw_lag
  use debug_defs,    only : gradient_construction_rhs
  use nml_boundary_conditions, only : wall_velocity
  use turb_parameters, only : t_diff1, t_diff2
  use grid_metrics,    only : dualmetric
  use turb_util,       only : turbmultieqn_grad, kloc, wloc
  use debug_defs,      only : unified_diffusion, diff_edge_avg_t
  use comprow_types,   only : crow_type, crow_flow
  use comprow,         only : set_up_comprow

  integer, parameter                 :: nedgeloc = 16
  integer, parameter                 :: n_grd    = 8
  integer, parameter                 :: nnodes0  = 9
  integer, parameter                 :: nnodes01 = 9
  integer, parameter                 :: n_tot    = 5
  integer, parameter                 :: n_turb   = 3
  integer, parameter                 :: nnz01    = 10

  type(grid_type)                    :: grid
  type(crow_type)                    :: crow
  type(crow_flow)                    :: crowf
! type(soln_type)                    :: soln
  real(dp),          dimension(n_tot ,nnodes01)  :: q_dof
  real(dp),          dimension(       nnodes01)  :: amut
  real(dp),          dimension(n_turb,nnodes01)  :: turb_res
  real(dp),          dimension(n_turb,nnodes01)  :: turb
  real(dp),          dimension(n_turb,n_turb,100)  :: a_diag !FIXME what is the correct dimension?
  real(odp),          dimension(n_turb,n_turb,100)  :: a_off !FIXME what is the correct dimension?
  real(dp),          dimension(n_grd ,nnodes01)  :: gradx
  real(dp),          dimension(n_grd ,nnodes01)  :: grady
  real(dp),          dimension(n_grd ,nnodes01)  :: gradz
  real(dp),          dimension(       nnodes01)  :: sst_f1
  real(dp),          dimension(10             )  :: a, b, c, d
  real(dp),          dimension(1              )  :: nu

  integer                            :: nnz0

! type(bcgrid_type), dimension(1)    :: bc
  type(bcsoln_type), dimension(1)    :: bcsoln
  integer                            :: ib
  integer                            :: dim1
  integer                            :: n_sta
  integer                            :: nodes_per_face
  integer                            :: face_index
  integer                            :: eqn_set

  integer                            :: ielem, icell
  integer                            :: cell
  integer                            :: node1, node2
  integer                            :: njac
  integer                            :: viscous_method
  integer                            :: i, ie, j
  integer                            :: i1, i2, edge
  integer                            :: r_start

  character(80)                      :: grid_motion
  real(dp), dimension(1,nnodes01)    :: res_gcl


  max_face_per_cell    = 6
  max_node_per_cell    = 8
  max_edge_per_cell    = 12
  eqn_set              = 0
  cell                 = 1
  njac                 = 5
  twod                 = .false.
  turbulence_model_int = kw_lag
  kloc                 = n_grd - n_turb + 1
  wloc                 = n_grd - n_turb + 2

  call setup_fluid_gamma
  call setup_sutherland_constant

  call nullify_grid ( grid )
  grid%nnodes0  = nnodes0
  grid%nnodes01 = nnodes01
  allocate ( grid%x( grid%nnodes01 ) )
  allocate ( grid%y( grid%nnodes01 ) )
  allocate ( grid%z( grid%nnodes01 ) )
  allocate ( grid%bc(1) )
  allocate ( grid%elem(2) )

  call allocate_bcsoln   ( 9, bcsoln(1) )

  call initialize_elem ( grid%elem(1), 'pyr' )
  call initialize_elem ( grid%elem(2), 'hex' )
  call allocate_elem ( grid%elem(2), .false. )

  grid%nelem                 = 2
  grid%nedge                 = nedgeloc
  grid%nedgeloc              = nedgeloc
  grid%nedgeloc_2d           = 0
  grid%elem(1)%edge_per_cell = 8
  grid%elem(2)%edge_per_cell = 12

  allocate ( grid%vol      (   grid%nnodes01 ) ) 
  allocate ( grid%dxdt     (   grid%nnodes01 ) ) 
  allocate ( grid%dydt     (   grid%nnodes01 ) ) 
  allocate ( grid%dzdt     (   grid%nnodes01 ) ) 
  allocate ( grid%facespeed(   grid%nedgeloc ) ) 
  allocate ( grid%eptr     ( 2,grid%nedgeloc ) ) 
  allocate ( grid%xn       (   grid%nedgeloc ) )
  allocate ( grid%yn       (   grid%nedgeloc ) )
  allocate ( grid%zn       (   grid%nedgeloc ) )
  allocate ( grid%ra       (   grid%nedgeloc ) )

  grid%x(1) = 0.0_dp; grid%y(1) = 0.0_dp; grid%z(1) = 0.0_dp
  grid%x(2) = 1.0_dp; grid%y(2) = 0.0_dp; grid%z(2) = 0.0_dp
  grid%x(3) = 1.0_dp; grid%y(3) = 1.0_dp; grid%z(3) = 0.0_dp
  grid%x(4) = 0.0_dp; grid%y(4) = 1.0_dp; grid%z(4) = 0.0_dp
  grid%x(5) = 1.0_dp; grid%y(5) = 0.0_dp; grid%z(5) = 5.0e-2_dp
  grid%x(6) = 1.0_dp; grid%y(6) = 1.0_dp; grid%z(6) = 5.0e-2_dp
  grid%x(7) = 0.0_dp; grid%y(7) = 0.0_dp; grid%z(7) = 5.0e-2_dp
  grid%x(8) = 0.0_dp; grid%y(8) = 1.0_dp; grid%z(8) = 5.0e-2_dp
  grid%x(9) =-0.5_dp; grid%y(9) = 0.5_dp; grid%z(9) = 2.5e-2_dp

  ib                  = 1
  grid%nbound         = 0 ! switch to 1 to test boundary gradients
  grid%bc(ib)%ibc     = 4110 ! viscous wall function boundary
  grid%bc(ib)%nbnode  = 5 ! number of boundary nodes
  grid%bc(ib)%nbfacet = 1
  grid%bc(ib)%nbfaceq = 1

  ! account for one triangle face and one quad face
  allocate ( grid%bc(ib)%ibnode( grid%bc(ib)%nbnode) )
  allocate ( grid%bc(ib)%f2ntb ( grid%bc(ib)%nbfacet, 5 ) )
  allocate ( grid%bc(ib)%f2nqb ( grid%bc(ib)%nbfaceq, 6 ) )
  allocate ( bcsoln(ib)%mu_t_wf ( grid%bc(ib)%nbnode ) )
  allocate ( bcsoln(ib)%omega_wf ( grid%bc(ib)%nbnode ) )
  allocate ( grid%elem(1)%c2n    (  5, 1 ) )
  allocate ( grid%elem(2)%c2n    (  8, 1 ) )
  allocate ( grid%elem(1)%c2e    (  8, 1 ) )
  allocate ( grid%elem(2)%c2e    ( 12, 1 ) )
  allocate ( grid%elem(1)%e2n_2d (  4, 2 ) )
  allocate ( grid%elem(2)%e2n_2d (  4, 2 ) )

  wall_velocity(1,:) = (/0.3_dp,0.2_dp,0.1_dp/)

  face_index     = 1
  nodes_per_face = 3
  grid%bc(ib)%f2ntb(face_index,1) = 1 ! 9 ! face 1, node 1
  grid%bc(ib)%f2ntb(face_index,2) = 5 ! 4 ! face 1, node 2
  grid%bc(ib)%f2ntb(face_index,3) = 4 ! 1 ! face 1, node 3
  grid%bc(ib)%f2ntb(face_index,4) = 1 ! cell number
  grid%bc(ib)%f2ntb(face_index,5) = 1 ! element type
  grid%bc(ib)%nbfacet             = 1 ! number of tri faces

  face_index     = 1
  nodes_per_face = 4
  grid%bc(ib)%f2nqb(face_index,1) = 1 ! 1 ! face 1, node 1
  grid%bc(ib)%f2nqb(face_index,2) = 4 ! 2 ! face 1, node 2
  grid%bc(ib)%f2nqb(face_index,3) = 3 ! 3 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,4) = 2 ! 4 ! face 1, node 3
  grid%bc(ib)%f2nqb(face_index,5) = 1 ! cell number
  grid%bc(ib)%f2nqb(face_index,6) = 2 ! element type
  grid%bc(ib)%nbfaceq             = 1 ! number of quad faces

  grid%bc(ib)%ibnode(1) = 1 ! boundary node
  grid%bc(ib)%ibnode(2) = 2 ! boundary node
  grid%bc(ib)%ibnode(3) = 3 ! boundary node
  grid%bc(ib)%ibnode(4) = 4 ! boundary node
  grid%bc(ib)%ibnode(5) = 9 ! boundary node

  grid%elem(1)%ncell       = 1
  cell                     = 1
  grid%elem(1)%c2n(1,cell) = 7 ! map of cell 1 nodes
  grid%elem(1)%c2n(2,cell) = 8
  grid%elem(1)%c2n(3,cell) = 4
  grid%elem(1)%c2n(4,cell) = 1
  grid%elem(1)%c2n(5,cell) = 9
  grid%elem(1)%node_per_cell = 5
  grid%elem(1)%c2e(1,cell) = 16 ! map of cell 1 edges
  grid%elem(1)%c2e(2,cell) = 15
  grid%elem(1)%c2e(3,cell) = 13
  grid%elem(1)%c2e(4,cell) = 12
  grid%elem(1)%c2e(5,cell) = 11
  grid%elem(1)%c2e(6,cell) = 14
  grid%elem(1)%c2e(7,cell) = 8
  grid%elem(1)%c2e(8,cell) = 9
  grid%elem(1)%local_f2n   = local_f2n_pyr
  grid%elem(1)%local_f2e   = local_f2e_pyr
  grid%elem(1)%local_e2n   = local_e2n_pyr
  grid%elem(1)%chk_norm    = chk_norm_pyr

  grid%elem(2)%ncell       = 1
  cell                     = 1
  grid%elem(2)%c2n(1,cell) = 1
  grid%elem(2)%c2n(2,cell) = 7
  grid%elem(2)%c2n(3,cell) = 2
  grid%elem(2)%c2n(4,cell) = 5
  grid%elem(2)%c2n(5,cell) = 4
  grid%elem(2)%c2n(6,cell) = 8
  grid%elem(2)%c2n(7,cell) = 3
  grid%elem(2)%c2n(8,cell) = 6
  grid%elem(2)%node_per_cell = 8
  grid%elem(2)%c2e(1 ,cell) = 2
  grid%elem(2)%c2e(2 ,cell) = 3
  grid%elem(2)%c2e(3 ,cell) = 1
  grid%elem(2)%c2e(4 ,cell) = 5
  grid%elem(2)%c2e(5 ,cell) = 4
  grid%elem(2)%c2e(6 ,cell) = 10
  grid%elem(2)%c2e(7 ,cell) = 7
  grid%elem(2)%c2e(8 ,cell) = 6
  grid%elem(2)%c2e(9 ,cell) = 9
  grid%elem(2)%c2e(10,cell) = 8
  grid%elem(2)%c2e(11,cell) = 11
  grid%elem(2)%c2e(12,cell) = 14
  grid%elem(2)%local_f2n   = local_f2n_hex
  grid%elem(2)%local_f2e   = local_f2e_hex
  grid%elem(2)%local_e2n   = local_e2n_hex
  grid%elem(2)%chk_norm    = chk_norm_hex

!----------------------------------------------------------------------
!------------------ allocate and compute eprt -------------------------
!----------------------------------------------------------------------

    allocate(grid%el2g(grid%nedge));   grid%el2g = 0
    allocate(grid%l2g(grid%nedge));    grid%l2g = 0

    do i = 1, grid%nnodes01
      grid%l2g(i) = i
    enddo

    do ielem = 1,grid%nelem
       i1 = grid%elem(ielem)%edge_per_cell
       i2 = grid%elem(ielem)%ncell
       allocate(grid%elem(ielem)%c2e(i1,i2)); grid%elem(ielem)%c2e = 0
    end do

    edge = 0
    do ielem  = 1,grid%nelem
       do icell = 1,grid%elem(ielem)%ncell
          loop_ie: do ie = 1,grid%elem(ielem)%edge_per_cell
             i = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,1),icell)
             j = grid%elem(ielem)%c2n(grid%elem(ielem)%local_e2n(ie,2),icell)
             node1 = min(i,j)
             node2 = max(i,j)
             do i = 1,edge
                if ((grid%eptr(1,i)==node1).and.(grid%eptr(2,i)==node2)) then 
                   grid%elem(ielem)%c2e(ie,icell) = i
                   cycle loop_ie
                end if
             end do
             edge = edge + 1
             grid%el2g(edge) = edge 
             grid%eptr(1,edge) = node1
             grid%eptr(2,edge) = node2
             grid%elem(ielem)%c2e(ie,icell) = edge 
         end do loop_ie
      end do
    end do
    grid%nedge    = edge 
    grid%nedgeloc = edge 
    grid%nedgeg   = edge 


!----------------------------------------------------------------------
!----------------------------------------------------------------------

  grid%xn(:) = 0._dp
  grid%yn(:) = 0._dp
  grid%zn(:) = 0._dp
  grid%ra(:) = 0._dp

  do ielem = 1, grid%nelem

    call dualmetric( grid%x, grid%y, grid%z, grid%dxdt, grid%dydt, grid%dzdt &
                   , grid%xn, grid%yn, grid%zn, grid%eptr                    &
                   , grid%facespeed, grid%nnodes01, grid%nedgeloc            &
                   , grid%elem(ielem)%ncell, grid%elem(ielem)%ncell          &
                   , grid%elem(ielem)%c2n, grid%elem(ielem)%c2e              &
                   , grid%elem(ielem)%node_per_cell                          &
                   , grid%elem(ielem)%edge_per_cell                          &
                   , grid%elem(ielem)%local_e2n, grid%vol, grid%ra           &
                   , grid_motion, res_gcl, ielem, grid%nelem )

  end do

  viscous_method = 0
  call set_up_comprow(viscous_method, grid, crow)

    dim1 = grid%nnodes01
    nnz0 = crow%ia(grid%nnodes0+1)-1
    
    call my_alloc_ptr(crowf%g2m, dim1)
    do i = 1, dim1 
       crowf%g2m(i) = i
    end do
    
    call my_alloc_ptr(crowf%nzg2m, nnz0)
    do i = 1, nnz0
       crowf%nzg2m(i) = i
    end do

!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!                       Manufactured solution
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
!----------------------------------------------------------------------
  a(1)=0; b(1)=0; c(1)=0; d(1)=1
  a(2)=1; b(2)=0; c(2)=0; d(2)=0
  a(3)=0; b(3)=1; c(3)=0; d(3)=0
  a(4)=0; b(4)=0; c(4)=1; d(4)=0
  a(5)=1; b(5)=1; c(5)=1; d(5)=1

  a(6)=1; b(6)=0; c(6)=0; d(6)=0
  a(7)=0; b(7)=1; c(7)=0; d(7)=0
  a(8)=0; b(8)=0; c(8)=1; d(8)=0

  a(9)=0; b(9)=0; c(9)=0; d(9)=1

  do i = 1, 9
    q_dof(1,i) = a(1)*grid%x(i) + b(1)*grid%y(i) + c(1)*grid%z(i) + d(1)
    q_dof(2,i) = a(2)*grid%x(i) + b(2)*grid%y(i) + c(2)*grid%z(i) + d(2)
    q_dof(3,i) = a(3)*grid%x(i) + b(3)*grid%y(i) + c(3)*grid%z(i) + d(3)
    q_dof(4,i) = a(4)*grid%x(i) + b(4)*grid%y(i) + c(4)*grid%z(i) + d(4)
    q_dof(5,i) = a(5)*grid%x(i) + b(5)*grid%y(i) + c(5)*grid%z(i) + d(5)
    turb(1,i)  = a(6)*grid%x(i) + b(6)*grid%y(i) + c(6)*grid%z(i) + d(6)
    turb(2,i)  = a(7)*grid%x(i) + b(7)*grid%y(i) + c(7)*grid%z(i) + d(7)
    turb(3,i)  = a(8)*grid%x(i) + b(8)*grid%y(i) + c(8)*grid%z(i) + d(8)
    amut(i)    = a(9)*grid%x(i) + b(9)*grid%y(i) + c(9)*grid%z(i) + d(9)
    sst_f1(i)  = 0.00_dp
  enddo

!----------------------------------------------------------------------
  gradx = 0.0_dp
  grady = 0.0_dp
  gradz = 0.0_dp

  call turbmultieqn_grad( grid%nnodes0, grid%nnodes01, grid%nedgeloc               &
                        , grid%nedgeloc_2d, grid%nnodes0_2d                        &
                        , grid%node_pairs_2d, grid%eptr, turb, grid%x, grid%y      &
                        , grid%z, grid%xn, grid%yn, grid%zn, grid%ra, grid%vol     &
                        , gradx, grady, gradz, grid%nbound, grid%bc, n_turb, n_grd &
                        , grid%nelem, grid%elem                                    &
                        , n_tot, q_dof, turbulence_model_int )

!=============================================================================80
!  3-equation edge_averaging
!=============================================================================80
! point = (/0.5_dp, 0.5_dp, 0.5_dp/)
  n_sta           = 1 ! first location
  q_type          = 1 ! primitive
  eqn_set         = 0 ! compressible

  t_diff1                   = 1
  t_diff2                   = 1
  turb_res                  = 0.0_dp
  r_start                   = 1

  call turb_resid_conv( turbulence_model_int,                                  &
                        grid%nnodes0, grid%nedgeloc, grid%eptr, turb,          &
                        q_dof, turb_res, grid%xn, grid%yn, grid%zn,            &
                        grid%ra, grid%facespeed,                               &
                        n_turb, r_start, grid%nedgeloc_2d,                     &
                        grid%x, grid%y, grid%z, gradx, grady, gradz )



  do i = 1, 9
  write(6,'(10(1x,f20.10))')  turb_res(1,i),turb_res(2,i), turb_res(3,i)
  enddo

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,1),'_dp,  turb_res(1,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,1),'_dp,  turb_res(2,1), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,1),'_dp,  turb_res(3,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,2),'_dp,  turb_res(1,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,2),'_dp,  turb_res(2,2), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,2),'_dp,  turb_res(3,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,3),'_dp,  turb_res(1,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,3),'_dp,  turb_res(2,3), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,3),'_dp,  turb_res(3,3), tol ) '
  write(6,'(a,f20.10,a)') '! node4'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,4),'_dp,  turb_res(1,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,4),'_dp,  turb_res(2,4), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,4),'_dp,  turb_res(3,4), tol ) '
  write(6,'(a,f20.10,a)') '! node5'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,5),'_dp,  turb_res(1,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,5),'_dp,  turb_res(2,5), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,5),'_dp,  turb_res(3,5), tol ) '
  write(6,'(a,f20.10,a)') '! node6'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,6),'_dp,  turb_res(1,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,6),'_dp,  turb_res(2,6), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,6),'_dp,  turb_res(3,6), tol ) '
  write(6,'(a,f20.10,a)') '! node7'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,7),'_dp,  turb_res(1,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,7),'_dp,  turb_res(2,7), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,7),'_dp,  turb_res(3,7), tol ) '
  write(6,'(a,f20.10,a)') '! node8'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,8),'_dp,  turb_res(1,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,8),'_dp,  turb_res(2,8), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,8),'_dp,  turb_res(3,8), tol ) '
  write(6,'(a,f20.10,a)') '! node9'
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(1,9),'_dp,  turb_res(1,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(2,9),'_dp,  turb_res(2,9), tol ) '
  write(6,'(a,f20.10,a)') 'assert_equal_within(',turb_res(3,9),'_dp,  turb_res(3,9), tol ) '


! node1
assert_equal_within(        0.0000000000_dp,  turb_res(1,1), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(2,1), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,1), tol ) 
! node2
assert_equal_within(        0.0000000000_dp,  turb_res(1,2), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(2,2), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,2), tol ) 
! node3
assert_equal_within(       -0.0125000000_dp,  turb_res(1,3), tol ) 
assert_equal_within(       -0.0125000000_dp,  turb_res(2,3), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(3,3), tol ) 
! node4
assert_equal_within(        0.0006250000_dp,  turb_res(1,4), tol ) 
assert_equal_within(       -0.0006250000_dp,  turb_res(2,4), tol ) 
assert_equal_within(       -0.0000312500_dp,  turb_res(3,4), tol ) 
! node5
assert_equal_within(       -0.0125000000_dp,  turb_res(1,5), tol ) 
assert_equal_within(        0.0000000000_dp,  turb_res(2,5), tol ) 
assert_equal_within(       -0.0006250000_dp,  turb_res(3,5), tol ) 
! node6
assert_equal_within(       -0.0250000000_dp,  turb_res(1,6), tol ) 
assert_equal_within(       -0.0250000000_dp,  turb_res(2,6), tol ) 
assert_equal_within(       -0.0012500000_dp,  turb_res(3,6), tol ) 
! node7
assert_equal_within(        0.0006250000_dp,  turb_res(1,7), tol ) 
assert_equal_within(       -0.0006250000_dp,  turb_res(2,7), tol ) 
assert_equal_within(       -0.0000312500_dp,  turb_res(3,7), tol ) 
! node8
assert_equal_within(        0.0012500000_dp,  turb_res(1,8), tol ) 
assert_equal_within(       -0.0166666667_dp,  turb_res(2,8), tol ) 
assert_equal_within(       -0.0008333333_dp,  turb_res(3,8), tol ) 
! node9
assert_equal_within(        0.0000000000_dp,  turb_res(1,9), tol ) 
assert_equal_within(       -0.0029166667_dp,  turb_res(2,9), tol ) 
assert_equal_within(       -0.0001458333_dp,  turb_res(3,9), tol )

  write(*,*)
  write(*,*) 'Finish part 1 test_turb_resid_conv'
  write(*,*)

  a_diag = 0.0
  a_off  = 0.0

  call turb_jacob_conv( grid%nnodes0,                                          &
                        grid%nedgeloc, grid%eptr, q_dof,                       &
                        grid%xn, grid%yn, grid%zn, grid%ra, a_diag,            &
                        a_off, crow%fhelp, grid%facespeed, n_turb,             &
                        crowf%g2m, grid%nedgeloc_2d )

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(1,1,1),'_dp,  a_diag(1,1,1), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(2,2,1),'_dp,  a_diag(2,2,1), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(3,3,1),'_dp,  a_diag(3,3,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(1,1,2),'_dp,  a_diag(1,1,2), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(2,2,2),'_dp,  a_diag(2,2,2), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(3,3,2),'_dp,  a_diag(3,3,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(1,1,3),'_dp,  a_diag(1,1,3), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(2,2,3),'_dp,  a_diag(2,2,3), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_diag(3,3,3),'_dp,  a_diag(3,3,3), tol ) '

  write(6,'(a,f20.10,a)') '! node1'
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(1,1,1),'_dp,  a_off(1,1,1), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(2,2,1),'_dp,  a_off(2,2,1), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(3,3,1),'_dp,  a_off(3,3,1), tol ) '
  write(6,'(a,f20.10,a)') '! node2'
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(1,1,2),'_dp,  a_off(1,1,2), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(2,2,2),'_dp,  a_off(2,2,2), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(3,3,2),'_dp,  a_off(3,3,2), tol ) '
  write(6,'(a,f20.10,a)') '! node3'
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(1,1,3),'_dp,  a_off(1,1,3), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(2,2,3),'_dp,  a_off(2,2,3), tol ) '
  write(6,'(a,es20.10,a)') 'assert_equal_within(',a_off(3,3,3),'_dp,  a_off(3,3,3), tol ) '

! node1
assert_equal_within(    9.0406412491E-12_dp,  a_diag(1,1,1), tol ) 
assert_equal_within(    9.0406412491E-12_dp,  a_diag(2,2,1), tol ) 
assert_equal_within(    9.0406412491E-12_dp,  a_diag(3,3,1), tol ) 
! node2
assert_equal_within(    6.5625000000E-12_dp,  a_diag(1,1,2), tol ) 
assert_equal_within(    6.5625000000E-12_dp,  a_diag(2,2,2), tol ) 
assert_equal_within(    6.5625000000E-12_dp,  a_diag(3,3,2), tol ) 
! node3
assert_equal_within(    6.2500000000E-12_dp,  a_diag(1,1,3), tol ) 
assert_equal_within(    6.2500000000E-12_dp,  a_diag(2,2,3), tol ) 
assert_equal_within(    6.2500000000E-12_dp,  a_diag(3,3,3), tol ) 

! node1
assert_equal_within(    0.0000000000E+00_dp,  a_off(1,1,1), tol ) 
assert_equal_within(    0.0000000000E+00_dp,  a_off(2,2,1), tol ) 
assert_equal_within(    0.0000000000E+00_dp,  a_off(3,3,1), tol ) 
! node2
assert_equal_within(   -3.1249999875E-13_dp,  a_off(1,1,2), tol ) 
assert_equal_within(   -3.1249999875E-13_dp,  a_off(2,2,2), tol ) 
assert_equal_within(   -3.1249999875E-13_dp,  a_off(3,3,2), tol ) 
! node3
assert_equal_within(   -3.8541666061E-13_dp,  a_off(1,1,3), tol ) 
assert_equal_within(   -3.8541666061E-13_dp,  a_off(2,2,3), tol ) 
assert_equal_within(   -3.8541666061E-13_dp,  a_off(3,3,3), tol ) 



!  call deallocate_elem ( grid%elem(1) )
! call deallocate_bc   ( bcsoln(1) )
!  call deallocate_bc   ( grid%bc(1) )

!  write(6,'(a,10(1x,f20.10))') 'fin test_turb_resid_diff'

end test

end test_suite

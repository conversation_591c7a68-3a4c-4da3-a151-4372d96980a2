module nml_exact

  use kinddefs,          only : dp
  use exact_defs,        only : ic_exact
  use twod_util,         only : q_2d
  use lmpi,              only : lmpi_master, lmpi_conditional_stop, lmpi_bcast
  use info_depr,         only : xmach, print_conditional
  use nml_global,        only : document_namelist
  use fluid,             only : gamma, setup_fluid_gamma
  use namelist_util,     only : nml_error
  use convection_defs,   only : incompressible_convection
  use exact_airfoil,     only : airfoil, te_angle, thickness, camber
  use exact_defs,        only : exact_solution, exact_freestream,            &
                                farfield_cl, farfield_point_vortex,          &
                                truncation_error, discretization_error,      &
                                null_mms_forcing_terms,                      &
                                lisbon_profile_at_x, lisbon_profile_at_z,    &
                                u_polyf, v_polyf, w_polyf,                   &
                                p_polyf, t_polyf, turb_polyf,                &
                                u_sinef, v_sinef, w_sinef,                   &
                                p_sinef, t_sinef, turb_sinef,                &
                                u_cosinef, v_cosinef, w_cosinef,             &
                                p_cosinef, t_cosinef, turb_cosinef,          &
                                u_expblf, v_expblf, w_expblf,                &
                                p_expblf, t_expblf, turb_expblf,             &
                                dirichlet_bc_override,                       &
                                lisbon_ms1, lisbon_ms2, lisbon_ms4,          &
                                lisbon_backstep,                             &
                                u_scale, v_scale, w_scale,                   &
                                p_scale, t_scale, turb_scale,                &
                                debug_forcing, stagnation_type,              &
                                exact_polyf, exact_sinef, exact_cosinef,     &
                                exact_expblf,                                &
                                exact_turb_reentrant,                        &
                                turb_reentrant, turb_reentrant_alpha,        &
                                turb_reentrant_exterior_angle,               &
                                turb_reentrant_right_corner,                 &
                                x_te, z_te,                                  &
                                exact_turb_exprsq,                           &
                                turb_exprsq, turb_exprsq_damping,            &
                                simple_sine, simple_sine_radial, simple_radial

  use exact_smith_hutton, only : smith_hutton
  use exact_cylinder,     only : cylinder, cylinder_radius
  use exact_sphere,       only : sphere, sphere_radius

  use complex_functions,  only : o

  implicit none

  private

  public :: read_nml_exact, echo_nml_exact

  logical :: echo = .false.
  logical :: show = .false.

  namelist / exact /                                                           &
                                                                   show, echo, &
    exact_solution, exact_freestream, exact_polyf, exact_sinef, exact_cosinef, &
    exact_expblf,                                                              &
    debug_forcing, smith_hutton,                                               &
    u_polyf, v_polyf, w_polyf, p_polyf, t_polyf, turb_polyf,                   &
    u_sinef, v_sinef, w_sinef, p_sinef, t_sinef, turb_sinef,                   &
    u_cosinef, v_cosinef, w_cosinef, p_cosinef, t_cosinef, turb_cosinef,       &
    u_expblf,  v_expblf,  w_expblf,  p_expblf,  t_expblf,  turb_expblf,        &
    simple_sine, simple_sine_radial, simple_radial,                            &
    u_scale, v_scale, w_scale, p_scale, t_scale, turb_scale,                   &
    stagnation_type, dirichlet_bc_override,                                    &
    lisbon_ms1, lisbon_ms2, lisbon_ms4, lisbon_backstep,                       &
    farfield_cl, farfield_point_vortex,                                        &
    null_mms_forcing_terms,                                                    &
    lisbon_profile_at_x, lisbon_profile_at_z,                                  &
    airfoil, te_angle, thickness, camber,                                      &
    cylinder, cylinder_radius,                                                 &
    sphere, sphere_radius,                                                     &
    truncation_error, discretization_error,                                    &
    exact_turb_reentrant, turb_reentrant,                                      &
    turb_reentrant_alpha, turb_reentrant_exterior_angle,                       &
    turb_reentrant_right_corner, x_te, z_te,                                   &
    exact_turb_exprsq, turb_exprsq, turb_exprsq_damping

contains

!=============================== READ_NML_EXACT ==============================80
!
! Read namelist for setting up exact solution.
!
!=============================================================================80

  subroutine read_nml_exact( eqn_set, nml_path )

    use file_utils,        only : available_unit
    use system_extensions, only : se_open
    use solution_types,    only : compressible

    integer,          intent(in) :: eqn_set
    character(len=*), intent(in) :: nml_path

    integer :: sunit, iostat1, iostat2

    continue

!   Initialize variable scalings

    u_scale    = 1._dp
    v_scale    = 1._dp
    w_scale    = 1._dp
    p_scale    = 1._dp
    t_scale    = 1._dp
    turb_scale = 1._dp
    if ( eqn_set == compressible ) then

!     Assume non-dimensionalization with density and speed of sound

      call setup_fluid_gamma()
      u_scale    = xmach
      v_scale    = xmach
      w_scale    = xmach
      p_scale    = 1._dp/gamma
    endif

    iostat1 = 0
    iostat2 = 0
    master_read_nml : if ( lmpi_master ) then

      sunit = available_unit()
      call se_open(sunit, file=nml_path, status='old', iostat=iostat1)
      if (iostat1 == 0) then
        show = print_conditional
        read(sunit,iostat=iostat2,nml=exact)
        if ( show ) write(*,nml=exact)
      else
        write(*,*) 'unable to open ', trim(nml_path)
      endif
      close(sunit)

    endif master_read_nml

    call nml_error( iostat1, iostat2, nml_path, 'exact' )

    if ( cylinder ) then
      exact_polyf       = .false.
      exact_sinef       = .false.
      exact_cosinef     = .false.
      exact_solution    = .true.
      exact_freestream  = .false.
      airfoil           = 0
    elseif ( sphere ) then
      exact_polyf       = .false.
      exact_sinef       = .false.
      exact_cosinef     = .false.
      exact_solution    = .true.
      exact_freestream  = .false.
      airfoil           = 0
    elseif ( smith_hutton > 0 .or. airfoil > 0 ) then
      exact_polyf       = .false.
      exact_sinef       = .false.
      exact_cosinef     = .false.
      exact_solution    = .true.
      exact_freestream  = .false.
    elseif ( lisbon_ms1 .or. lisbon_ms2 .or.      &
             lisbon_ms4 .or. lisbon_backstep ) then
      exact_polyf      = .false.
      exact_sinef      = .false.
      exact_cosinef    = .false.
      exact_solution   = .true.
      exact_freestream = .false.
    elseif ( exact_freestream .or. &
             exact_polyf      .or. &
             exact_sinef      .or. &
             exact_cosinef    .or. &
             exact_expblf        ) then
      exact_solution  = .true.
    endif

    if ( .not. exact_sinef ) then
      u_sinef    = 0._dp
      v_sinef    = 0._dp
      w_sinef    = 0._dp
      p_sinef    = 0._dp
      t_sinef    = 0._dp
      turb_sinef = 0._dp
    endif

    if ( .not. exact_cosinef ) then
      u_cosinef    = 0._dp
      v_cosinef    = 0._dp
      w_cosinef    = 0._dp
      p_cosinef    = 0._dp
      t_cosinef    = 0._dp
      turb_cosinef = 0._dp
    endif

    if ( .not. exact_polyf ) then
      u_polyf    = 0._dp
      v_polyf    = 0._dp
      w_polyf    = 0._dp
      p_polyf    = 0._dp
      t_polyf    = 0._dp
      turb_polyf = 0._dp
    endif

    call lmpi_bcast(exact_freestream)
    call lmpi_bcast(exact_polyf)
    call lmpi_bcast(exact_sinef)
    call lmpi_bcast(exact_cosinef)
    call lmpi_bcast(exact_expblf)
    call lmpi_bcast(exact_solution)

    call lmpi_bcast(truncation_error)
    call lmpi_bcast(discretization_error)

    call lmpi_bcast(debug_forcing)

    call lmpi_bcast(u_polyf)
    call lmpi_bcast(v_polyf)
    call lmpi_bcast(w_polyf)
    call lmpi_bcast(p_polyf)
    call lmpi_bcast(t_polyf)
    call lmpi_bcast(turb_polyf)

    call lmpi_bcast(u_sinef)
    call lmpi_bcast(v_sinef)
    call lmpi_bcast(w_sinef)
    call lmpi_bcast(p_sinef)
    call lmpi_bcast(t_sinef)
    call lmpi_bcast(turb_sinef)

    call lmpi_bcast(simple_sine)
    call lmpi_bcast(simple_sine_radial)
    call lmpi_bcast(simple_radial)

    call lmpi_bcast(exact_turb_exprsq)
    call lmpi_bcast(turb_exprsq)
    call lmpi_bcast(turb_exprsq_damping)

    call lmpi_bcast(exact_turb_reentrant)
    call lmpi_bcast(turb_reentrant)
    call lmpi_bcast(turb_reentrant_alpha)
    call lmpi_bcast(turb_reentrant_exterior_angle)
    call lmpi_bcast(turb_reentrant_right_corner)
    call lmpi_bcast(x_te)
    call lmpi_bcast(z_te)

    call lmpi_bcast(u_cosinef)
    call lmpi_bcast(v_cosinef)
    call lmpi_bcast(w_cosinef)
    call lmpi_bcast(p_cosinef)
    call lmpi_bcast(t_cosinef)
    call lmpi_bcast(turb_cosinef)

    call lmpi_bcast(u_expblf)
    call lmpi_bcast(v_expblf)
    call lmpi_bcast(w_expblf)
    call lmpi_bcast(p_expblf)
    call lmpi_bcast(t_expblf)
    call lmpi_bcast(turb_expblf)

    call lmpi_bcast(u_scale)
    call lmpi_bcast(v_scale)
    call lmpi_bcast(w_scale)
    call lmpi_bcast(p_scale)
    call lmpi_bcast(t_scale)
    call lmpi_bcast(turb_scale)

    call lmpi_bcast(dirichlet_bc_override)

    call lmpi_bcast(stagnation_type)

    call lmpi_bcast(lisbon_profile_at_x)
    call lmpi_bcast(lisbon_profile_at_z)
    call lmpi_bcast(lisbon_ms1)
    call lmpi_bcast(lisbon_ms2)
    call lmpi_bcast(lisbon_ms4)
    call lmpi_bcast(lisbon_backstep)
    call lmpi_bcast(null_mms_forcing_terms)

    call lmpi_bcast(airfoil)
    call lmpi_bcast(te_angle)
    call lmpi_bcast(thickness)
    call lmpi_bcast(camber)

    call lmpi_bcast(cylinder)
    call lmpi_bcast(cylinder_radius)

    call lmpi_bcast(sphere)
    call lmpi_bcast(sphere_radius)

    call lmpi_bcast(farfield_point_vortex)
    call lmpi_bcast(farfield_cl)

    if ( exact_solution ) ic_exact = .true.

    call check_inputs1(eqn_set)

  end subroutine read_nml_exact

!=============================== ECHO_NML_EXACT ==============================80
!
! Echo namelist for setting up exact solution.
!
!=============================================================================80

  subroutine echo_nml_exact(f)

  integer, intent(in)           :: f

  integer :: u, i

  logical :: sinef_chosen = .false.

  continue

    call check_inputs2()

    if ( q_2d ) call exact_coeff_2d()

    if ( .not. lmpi_master ) return

    if ( f /= 6 ) then
      u = f
      write(u,*)
      write(u,nml=exact)
      if ( .not.document_namelist ) return
    else
      u = 6
      if ( .not.echo ) return
    endif

    write(u,*)
    write(u,"(1x,a)") ' Summary of &exact namelist inputs:'

    if ( exact_freestream .or. document_namelist ) then
      write(u,*)
      write(u,*) ' Exact freestream solution specified via:'
      write(u,*) ' exact_freestream=',exact_freestream
    endif

    if ( smith_hutton > 0 .or. document_namelist ) then
      write(u,"(1x,a)") 'Smith-Hutton variable-coefficient convection-diffusion'
      write(u,"(1x,a)") 'within compressible S-A turbulent path specified via:'
      write(u,*) ' smith_hutton=',smith_hutton
      write(u,*) '             = 1, constant solution.'
      write(u,*) '             = 2, linear solution.'
      write(u,*) '             = 3, quadratic solution.'
      write(u,*) '             = 4, cubic solution.'
      write(u,*) '   otherwise > 0, reference solution.'
    endif

    if ( exact_cosinef .or. document_namelist ) then
      write(u,*)
      write(u,*) ' exact_cosinef=',exact_cosinef
      write(u,*) ' For exact_cosinef=T,'
      write(u,*) ' Simple cosine variation of solution specified via:'
      if ( incompressible_convection ) then
        write(u,*) ' => -cos[ pi*( cosinef(1)*x + cosinef(2)*y &
                   &+ cosinef(3)*z ) ] + 1'
      else
        write(u,*) ' => cos[ pi*( cosinef(1)*x + cosinef(2)*y &
                   &+ cosinef(3)*z ) ] - 1'
      endif
      write(u,'("    u_cosinef=",3f20.8)') (o(u_cosinef(i)),i=1,3)
      write(u,'("    v_cosinef=",3f20.8)') (o(v_cosinef(i)),i=1,3)
      write(u,'("    w_cosinef=",3f20.8)') (o(w_cosinef(i)),i=1,3)
      write(u,'("    p_cosinef=",3f20.8)') (o(p_cosinef(i)),i=1,3)
      write(u,'("    t_cosinef=",3f20.8)') (o(t_cosinef(i)),i=1,3)
      write(u,'(" turb_cosinef=",3f20.8)') (o(turb_cosinef(i)),i=1,3)
    endif

    if ( simple_sine  .or. document_namelist ) then
      sinef_chosen = .true.
      write(u,*)
      write(u,*) ' simple_sine=',simple_sine
      write(u,*) ' For simple_sine=T,'
      write(u,*) ' Simple sine variation of solution specified via:'
      !q = q + scale*sin( pi*( sx*x + sy*y + sz*z ) )
      write(u,*) ' => sin[ pi*( sinef(1)*x + sinef(2)*y + sinef(3)*z ) ]'
      write(u,'("    u_sinef=",3f20.8)') (o(u_sinef(i)),i=1,3)
      write(u,'("    v_sinef=",3f20.8)') (o(v_sinef(i)),i=1,3)
      write(u,'("    w_sinef=",3f20.8)') (o(w_sinef(i)),i=1,3)
      write(u,'("    p_sinef=",3f20.8)') (o(p_sinef(i)),i=1,3)
      write(u,'("    t_sinef=",3f20.8)') (o(t_sinef(i)),i=1,3)
      write(u,'(" turb_sinef=",3f20.8)') (o(turb_sinef(i)),i=1,3)
    endif

    if( simple_sine_radial .or. document_namelist ) then
      sinef_chosen = .true.
      write(u,*)
      write(u,*) ' simple_sine_radial=',simple_sine_radial
      write(u,*) ' For simple_sine_radial=T,'
      write(u,*) ' Simple 2D circumferential/radial variation of solution &
                   &specified via:'
      !r = sqrt( x**2 + z**2 )
      !t = x/z
      !q = q + scale*sin( pi*sx + pi*sy*t + pi*sz*r )
      write(u,*) ' => sin[ pi*( sinef(1) + sinef(2)*(x/z) + sinef(3)*radius ) ]'
      write(u,'("    u_sinef=",3f20.8)') (o(u_sinef(i)),i=1,3)
      write(u,'("    v_sinef=",3f20.8)') (o(v_sinef(i)),i=1,3)
      write(u,'("    w_sinef=",3f20.8)') (o(w_sinef(i)),i=1,3)
      write(u,'("    p_sinef=",3f20.8)') (o(p_sinef(i)),i=1,3)
      write(u,'("    t_sinef=",3f20.8)') (o(t_sinef(i)),i=1,3)
      write(u,'(" turb_sinef=",3f20.8)') (o(turb_sinef(i)),i=1,3)
    endif

    if( simple_radial .or. document_namelist ) then
      sinef_chosen = .true.
      write(u,*)
      write(u,*) ' simple_radial=',simple_radial
      write(u,*) ' For simple_radial=T,'
      write(u,*) ' Simple 2D radial variation of solution specified via:'
      !r = sqrt( x**2 + z**2 )
      !q = q + scale*sz*r
      write(u,*) ' => sinef(3)*radius'
      write(u,'("    u_sinef=",3f20.8)') (o(u_sinef(i)),i=1,3)
      write(u,'("    v_sinef=",3f20.8)') (o(v_sinef(i)),i=1,3)
      write(u,'("    w_sinef=",3f20.8)') (o(w_sinef(i)),i=1,3)
      write(u,'("    p_sinef=",3f20.8)') (o(p_sinef(i)),i=1,3)
      write(u,'("    t_sinef=",3f20.8)') (o(t_sinef(i)),i=1,3)
      write(u,'(" turb_sinef=",3f20.8)') (o(turb_sinef(i)),i=1,3)
    endif

    if ( (.not.sinef_chosen .and. exact_sinef) .or. document_namelist ) then
      write(u,*)
      write(u,*) ' exact_sinef=',exact_sinef
      write(u,*) ' For exact_sinef=T,'
      write(u,*) ' Sine variation of solution specified via:'
      !q = q + scale*( 0.5_dp*sx*( sin(pi*x)**2 ) &
      !              + 0.5_dp*sy*( sin(pi*y)**2 ) &
      !              + 0.5_dp*sz*( sin(pi*z)**2 ) )
      write(u,*) ' => 0.5*( sinef(1)*sin[ pi*x ]**2 &
                         &+ sinef(2)*sin[ pi*y ]**2 &
                         &+ sinef(3)*sin[ pi*z ]**2  )'
      write(u,'("    u_sinef=",3f20.8)') (o(u_sinef(i)),i=1,3)
      write(u,'("    v_sinef=",3f20.8)') (o(v_sinef(i)),i=1,3)
      write(u,'("    w_sinef=",3f20.8)') (o(w_sinef(i)),i=1,3)
      write(u,'("    p_sinef=",3f20.8)') (o(p_sinef(i)),i=1,3)
      write(u,'("    t_sinef=",3f20.8)') (o(t_sinef(i)),i=1,3)
      write(u,'(" turb_sinef=",3f20.8)') (o(turb_sinef(i)),i=1,3)
    endif

    if ( exact_polyf .or. document_namelist ) then
      write(u,*)
      write(u,*) ' exact_polyf=',exact_polyf
      write(u,*) ' For exact_polyf=T,'
      write(u,*) ' Polynomial variation of solution specified via:'
      write(u,*) ' ...coefficients of constant and linear terms&
                 & ( 1 : x : y : z )'
      write(u,'("    u_polyf(1:4)=",4f20.8)') (o(u_polyf(i)),i=1,4)
      write(u,'("    v_polyf(1:4)=",4f20.8)') (o(v_polyf(i)),i=1,4)
      write(u,'("    w_polyf(1:4)=",4f20.8)') (o(w_polyf(i)),i=1,4)
      write(u,'("    p_polyf(1:4)=",4f20.8)') (o(p_polyf(i)),i=1,4)
      write(u,'("    t_polyf(1:4)=",4f20.8)') (o(t_polyf(i)),i=1,4)
      write(u,'(" turb_polyf(1:4)=",4f20.8)') (o(turb_polyf(i)),i=1,4)
      write(u,*) ' ...coefficients of quadratic terms &
                 &( 1/2 x^2 : 1/2 y^2 : 1/2 z^2 )'
      write(u,'("    u_polyf(5:7)=",3f20.8)') (o(u_polyf(i)),i=5,7)
      write(u,'("    v_polyf(5:7)=",3f20.8)') (o(v_polyf(i)),i=5,7)
      write(u,'("    w_polyf(5:7)=",3f20.8)') (o(w_polyf(i)),i=5,7)
      write(u,'("    p_polyf(5:7)=",3f20.8)') (o(p_polyf(i)),i=5,7)
      write(u,'("    t_polyf(5:7)=",3f20.8)') (o(t_polyf(i)),i=5,7)
      write(u,'(" turb_polyf(5:7)=",3f20.8)') (o(turb_polyf(i)),i=5,7)
      write(u,*) ' ...coefficients of cubic terms &
                 &( 1/6 x^3 : 1/6 y^3 : 1/6 z^3 )'
      write(u,'("    u_polyf(8:10)=",3f20.8)') (o(u_polyf(i)),i=8,10)
      write(u,'("    v_polyf(8:10)=",3f20.8)') (o(v_polyf(i)),i=8,10)
      write(u,'("    w_polyf(8:10)=",3f20.8)') (o(w_polyf(i)),i=8,10)
      write(u,'("    p_polyf(8:10)=",3f20.8)') (o(p_polyf(i)),i=8,10)
      write(u,'("    t_polyf(8:10)=",3f20.8)') (o(t_polyf(i)),i=8,10)
      write(u,'(" turb_polyf(8:10)=",3f20.8)') (o(turb_polyf(i)),i=8,10)
      write(u,*) ' ...coefficients of quartic terms &
                 &( 1/12 x^4 : 1/12 y^4 : 1/12 z^4 )'
      write(u,'("    u_polyf(11:13)=",3f20.8)') (o(u_polyf(i)),i=11,13)
      write(u,'("    v_polyf(11:13)=",3f20.8)') (o(v_polyf(i)),i=11,13)
      write(u,'("    w_polyf(11:13)=",3f20.8)') (o(w_polyf(i)),i=11,13)
      write(u,'("    p_polyf(11:13)=",3f20.8)') (o(p_polyf(i)),i=11,13)
      write(u,'("    t_polyf(11:13)=",3f20.8)') (o(t_polyf(i)),i=11,13)
      write(u,'(" turb_polyf(11:13)=",3f20.8)') (o(turb_polyf(i)),i=11,13)
    endif

    if ( exact_expblf .or. document_namelist ) then
      write(u,*)
      write(u,*) ' exact_expblf=',exact_expblf
      write(u,*) ' For exact_expblf=T,'
      write(u,*) ' Boundary-layer like solution specified via:'
      write(u,*) ' ...exponential terms.'
      write(u,'("    u_expblf=",4f20.8)') o(u_expblf)
      write(u,'("    v_expblf=",4f20.8)') o(v_expblf)
      write(u,'("    w_expblf=",4f20.8)') o(w_expblf)
      write(u,'("    p_expblf=",4f20.8)') o(p_expblf)
      write(u,'("    t_expblf=",4f20.8)') o(t_expblf)
      write(u,'(" turb_expblf=",4f20.8)') o(turb_expblf)
    endif

    if ( exact_turb_exprsq .or. document_namelist ) then
      write(u,*)
      write(u,*) ' exact_turb_exprsq=',exact_turb_exprsq
      write(u,*) ' For exact_turb_exprsq=T,'
      write(u,*) ' Turbulent solution computed around 2D trailing edge via'
      write(u,*) ' exp( -damping * r^2 ) terms:'
      write(u,'("    turb_exprsq=",4f20.8)') o(turb_exprsq)
      write(u,'("    turb_exprsq_damping=",4f20.8)') o(turb_exprsq_damping)
      write(u,*) ' .......x_te=',o(x_te)
      write(u,*) ' .......z_te=',o(z_te)
    endif

    if ( exact_turb_reentrant .or. document_namelist ) then
      write(u,*)
      write(u,*) ' exact_turb_reentrant=',exact_turb_reentrant
      write(u,*) ' For exact_turb_reentrant=T,'
      write(u,*) ' Turbulent solution computed around 2D trailing edge via'
      write(u,*) ' reentrant corner solution with reentrant angle set'
      write(u,*) ' from exterior angle.'
      write(u,*) ' With turb_reentrant_alpha < 0:'
      write(u,*) '  q = r**alpha * sin( alpha*theta )'
      write(u,*) ' With turb_reentrant_alpha == alpha_in >= 0:'
      write(u,*) '  q = r**alpha_in * sin( alpha*theta )'
      write(u,*) ' In either case, no forcing function is set'
      write(u,'("    turb_reentrant=",4f20.8)') o(turb_reentrant)
      write(u,'("    turb_reentrant_exterior_angle=",4f20.8)') &
                   o(turb_reentrant_exterior_angle)
      write(u,'("    turb_reentrant_alpha=",4f20.8)') &
                   o(turb_reentrant_alpha)
      write(u,'("    turb_reentrant_right_corner=",L1)') &
                     turb_reentrant_right_corner
      write(u,*) ' .......x_te=',o(x_te)
      write(u,*) ' .......z_te=',o(z_te)
    endif

    write(u,*)
    write(u,*) ' Internal scaling of solution:'
    write(u,'("       u_scale=",f20.8)') o(u_scale)
    write(u,'("       v_scale=",f20.8)') o(v_scale)
    write(u,'("       w_scale=",f20.8)') o(w_scale)
    write(u,'("       p_scale=",f20.8)') o(p_scale)
    write(u,'("       t_scale=",f20.8)') o(t_scale)
    write(u,'("    turb_scale=",f20.8)') o(turb_scale)

    write(u,*)
    write(u,*) ' Stagnation_type=',stagnation_type

    write(u,*)
    write(u,*) ' Lisbon manufactured solutions:'
    write(u,*) '            lisbon_ms1=',lisbon_ms1
    write(u,*) '            lisbon_ms2=',lisbon_ms2
    write(u,*) '            lisbon_ms4=',lisbon_ms4
    write(u,'("    lisbon_profile_at_x=",f20.8)') &
              o(lisbon_profile_at_x)
    write(u,'("    lisbon_profile_at_z=",f20.8)') &
              o(lisbon_profile_at_z)
    write(u,'("            lisbon_backstep=",l1)') lisbon_backstep


    write(u,*)
    write(u,*) ' Airfoil solutions:'
    write(u,"(1x,a,i2)") '   airfoil=',airfoil
    write(u,"(1x,a,i2)") '          = 1, Joukowski.'
    write(u,"(1x,a,i2)") '          = 2, Karman-Trefftz.'
    write(u,"(1x,a,f20.10)") '   te_angle(deg)=',o(te_angle)
    write(u,"(1x,a,f20.10)") '       thickness=',o(thickness)
    write(u,"(1x,a,f20.10)") '          camber=',o(camber)

    write(u,*)
    write(u,*) ' Farfield point vortex approximation to farfield:'
    write(u,"(1x,a,i2)") '   farfield_point_vortex=',farfield_point_vortex
    write(u,"(1x,a)")    '     = 0, None.  Use exact solution if available.'
    write(u,"(1x,a)")    '     = 1, Use farfield_cl computed from forces.'
    write(u,"(1x,a)")    '     = 2, Use the farfield_cl from namelist (below).'
    write(u,"(1x,a,e20.10)") &
                         '   farfield_cl=',o(farfield_cl)

    write(u,*)
    write(u,*) ' Cylinder incompressible 2D solutions:'
    write(u,"(1x,a,L20)")    '         cylinder=',cylinder
    write(u,"(1x,a,f20.12)") '  cylinder_radius=',o(cylinder_radius)

    write(u,*)
    write(u,*) ' Sphere incompressible 3D solutions:'
    write(u,"(1x,a,L20)")    '         sphere=',sphere
    write(u,"(1x,a,f20.12)") '  sphere_radius=',o(sphere_radius)

    write(u,*)
    write(u,*) ' To override all node-centered bc data with exact soluion:'
    write(u,*) ' dirichlet_bc_override=',dirichlet_bc_override

    write(u,*)
    write(u,*) ' To enable specification of limited exact information:'
    write(u,*) ' (For use in specifying boundary conditions locally)'
    write(u,*) ' ...null_mms_forcing_terms=',null_mms_forcing_terms

  end subroutine echo_nml_exact

!=============================== EXACT_COEFF_2D ==============================80
!
! Null certain v and y-coefficients for 2D.
!
!=============================================================================80

  subroutine exact_coeff_2d()

    integer :: indx

  continue

    if ( q_2d ) then
      if ( lmpi_master ) write(*,*) ' ...Nulling v and y-coefficients (q_2d)'
      v_polyf   = 0._dp
      v_sinef   = 0._dp
      v_cosinef = 0._dp
      v_expblf  = 0._dp
      do indx = 1,13
        if(indx /= (indx/3)*3) cycle
        u_polyf(indx)    = 0._dp
        w_polyf(indx)    = 0._dp
        p_polyf(indx)    = 0._dp
        t_polyf(indx)    = 0._dp
        turb_polyf(indx) = 0._dp
      enddo
      u_sinef(2)    = 0._dp
      w_sinef(2)    = 0._dp
      p_sinef(2)    = 0._dp
      t_sinef(2)    = 0._dp
      turb_sinef(2) = 0._dp
      u_cosinef(2)    = 0._dp
      w_cosinef(2)    = 0._dp
      p_cosinef(2)    = 0._dp
      t_cosinef(2)    = 0._dp
      turb_cosinef(2) = 0._dp
    endif

  end subroutine exact_coeff_2d

!=============================== CHECK_INPUTS1 ===============================80
!
! Checks inputs (after local namelist read).
!
!=============================================================================80

  subroutine check_inputs1( eqn_set )

    use info_depr,      only : ivisc
    use solution_types, only : compressible, incompressible

    integer, intent(in) :: eqn_set

    integer :: ierr

  continue

    ierr = 0

    if ( lmpi_master .and. ( smith_hutton < 0 .or. smith_hutton > 5 ) ) then
      write(*,*)'Fatal error: Invalid input...smith_hutton=',smith_hutton
      ierr =1
    endif
    call lmpi_conditional_stop(ierr,'invalid Smith-Hutton input')

    if ( lmpi_master .and. smith_hutton > 0 ) then
      if ( eqn_set /= compressible ) then
        write(*,*)'Smith-Hutton only within compressible equation path..&
        &eqn_set=',eqn_set ; ierr = ierr + 1
      endif
      if ( abs( xmach - 1._dp ) > 1.0e-11_dp ) then
        write(*,*)'Smith=Hutton only for Mach=1..&
        &xmach=',xmach ; ierr = ierr + 1
      endif
      if ( ivisc /= 6 .and. ivisc /= 60 ) then
        write(*,*)'Smith-Hutton only within SA turbulence path..&
        &ivisc=',ivisc ; ierr = ierr + 1
      endif
    endif
    call lmpi_conditional_stop(ierr,'invalid Smith-Hutton inputs')

    if ( simple_sine .and. simple_sine_radial ) then
      write(*,*)'Fatal error: subroutine read_nml_exact...simple_sine and &
                &simple_sine_radial cannot both be .true.'
      call lmpi_conditional_stop(1)
    endif

    if ( simple_sine .and. simple_radial ) then
      write(*,*)'Fatal error: subroutine read_nml_exact...simple_sine and &
                &simple_radial cannot both be .true.'
      call lmpi_conditional_stop(1)
    endif

    if ( simple_radial .and. simple_sine_radial ) then
      write(*,*)'Fatal error: subroutine read_nml_exact...simple_radial and &
                &simple_sine_radial cannot both be .true.'
      call lmpi_conditional_stop(1)
    endif

    if ( lisbon_ms1 .and. (lisbon_ms2.or.lisbon_ms4) ) then
      write(*,*)'Fatal error: subroutine read_nml_exact...lisbon_ms1 and &
                &lisbon_ms2/4 cannot both be .true.'
      call lmpi_conditional_stop(1)
    endif

    if ( lisbon_ms2 .and. (lisbon_ms1.or.lisbon_ms4) ) then
      write(*,*)'Fatal error: subroutine read_nml_exact...lisbon_ms2 and &
                &lisbon_ms1/4 cannot both be .true.'
      call lmpi_conditional_stop(1)
    endif

    if ( lisbon_ms1 .or. lisbon_ms2 .or. lisbon_ms4 ) then
      if ( eqn_set /= incompressible .and. eqn_set /= compressible ) then
        write(*,*)'Fatal error: subroutine read_nml_exact.'
        write(*,*)'...lisbon_ms1/2/4 not setup for given equation set.'
        write(*,*)'...eqn_set=',eqn_set
        call lmpi_conditional_stop(1)
      endif
    endif

  end subroutine check_inputs1

!=============================== CHECK_INPUTS2 ===============================80
!
! Checks inputs (after all inputs completed).
!
!=============================================================================80

  subroutine check_inputs2()

    integer :: ierr

  continue

    ierr = 0
    if ( smith_hutton > 0 .and. .not.q_2d ) ierr = 1
    call lmpi_conditional_stop(ierr,'invalid Smith-Hutton inputs:check_inputs2')

  end subroutine check_inputs2

end module nml_exact


module visit

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#ifdef HAVE_VISIT
  use kinddefs,        only : dp
  use element_types,   only : elem_type
  use bc_types,        only : bcgrid_type
  use force_types,     only : force_type
#endif

  implicit none

#ifdef HAVE_VISIT
  include "visitfortransimV2interface.inc"
#endif

  private

  public :: initialize
  public :: step
  public :: shutdown

#ifdef HAVE_VISIT
  public :: VISIT_COMMAND_PROCESS, VISIT_COMMAND_SUCCESS, VISIT_COMMAND_FAILURE
  public :: run, one, autoupdate

  public :: x, y, z
  public :: nnodes0, nnodes01
  public :: ntface, nqface
  public :: ncell01
  public :: slen, idistfcn
  public :: nelem, elem
  public :: lconn, cellconnects, ghost

  public :: nbound, bc

  public :: ndim
  public :: q_dof, res
  public :: n_tot, dof0
  public :: eqn_set
  public :: rmshist, totforce

  public :: n_grd
  public :: gradx, grady, gradz

  public :: n_turb
  public :: turb, amut, turbres

  public :: adjoint_mode
  public :: sadj_rlam

  public :: copy_faces_and_cells
  public :: registerconstexpressions
  public :: registermeshvariables
  public :: registermeshexpressions
  public :: registercurves

  logical run, one, autoupdate

  integer, parameter          :: VISIT_COMMAND_PROCESS = 0
  integer, parameter          :: VISIT_COMMAND_SUCCESS = 1
  integer, parameter          :: VISIT_COMMAND_FAILURE = 2

! Pointers to x, y, z, q_dof, etc.
! Allows the callback routines to always access q_dof and x,y,z.
  real(dp), dimension(:),   pointer        :: x, y, z
  integer                                  :: nnodes0, nnodes01
  integer                                  :: ntface, nqface
  integer                                  :: ncell01
  integer                                  :: idistfcn
  real(dp), dimension(:),   pointer        :: slen

  integer                                  :: nelem
  type(elem_type),  dimension(:), pointer  :: elem
  integer,  dimension(:), allocatable      :: cellconnects
  integer,  dimension(:), allocatable      :: ghost
  integer                                  :: lconn

  integer                                  :: nbound
  type(bcgrid_type), dimension(:), pointer :: bc

  integer                                  :: ndim
  real(dp), dimension(:,:), pointer        :: q_dof, res
  integer                                  :: n_tot, dof0
  integer                                  :: eqn_set
  real(dp), dimension(:,:,:), pointer      :: rmshist
  type(force_type), dimension(:), pointer  :: totforce

  integer                                  :: n_grd
  real(dp), dimension(:,:), pointer        :: gradx, grady, gradz

  integer                                  :: n_turb
  real(dp), dimension(:,:), pointer        :: turb, turbres
  real(dp), dimension(:), pointer          :: amut

  logical                                  :: adjoint_mode
  real(dp), dimension(:,:,:), pointer      :: sadj_rlam
#endif

contains

!================================ INITIALIZE =================================80
!
! Initialize the co-processing environment
!
!=============================================================================80

  subroutine initialize(grid, soln, crow, raw_grid_data, flow_dir, sadj)
#ifdef HAVE_VISIT
    use io,                    only : project_rootname
    use info_depr,             only : title
    use lmpi,                  only : lmpi_master,lmpi_id,lmpi_nproc,          &
                                      lmpi_comm_world
    use string_utils,          only : sprintf
    use nml_overset_data,      only : dci_on_the_fly
    use suggar_info,           only : fun3d_comm
#endif
    use grid_types,            only : grid_type, raw_grid_data_type
    use solution_types,        only : soln_type
    use comprow_types,         only : crow_flow
    use solution_adj,          only : sadj_type

    type(grid_type),                          intent(in)    :: grid
    type(soln_type),                          intent(in)    :: soln
    type(crow_flow),                          intent(in)    :: crow
    type(raw_grid_data_type),                 intent(in)    :: raw_grid_data
    character(len=*),                         intent(in)    :: flow_dir
    type(sadj_type), optional,                intent(in)    :: sadj

#ifdef HAVE_VISIT
    integer                                                 :: err
    character(80)                                           :: trace
#endif

    continue

#ifdef HAVE_VISIT

#ifdef HAVE_MPI
    if (lmpi_master) write(*,*) 'Parallel VisIt Initialization on ',lmpi_nproc
    if (lmpi_nproc > 1) then
      err = visitsetparallel(1)
      if (err /= VISIT_OKAY) then
        write(*,*) '  VisItSetParallel Error! ',err
      endif
    endif
    err = visitsetparallelrank(lmpi_id)
    if (err /= VISIT_OKAY) then
      write(*,*) '  VisItSetParallelRank Error! ',lmpi_id,err
    endif

    if ( dci_on_the_fly ) then
      err = visitsetmpicommunicator(fun3d_comm)
    else
      err = visitsetmpicommunicator(lmpi_comm_world)
    endif
    if (err /= VISIT_OKAY) then
      write(*,*) '  VisItSetMPICommunicator Error! ',lmpi_id,err
    endif
#endif

    call getenv("TRACEVISIT", trace)
    if (len_trim(trace) > 0) then
      trace = trim(sprintf('VisIt-%i0', lmpi_id))//'.trace'
      err = visitopentracefile(trace,len_trim(trace))
      write(*,*) '  OpenTraceFile ',lmpi_id,err
    endif

!   err = visitsetdirectory()
!   err = visitsetoption()

    ! Initialize environment variables
    err = visitsetupenv()

    ! Write out .sim file that VisIt uses to connect (master only)
    if (lmpi_master) then
      err = visitinitializesim(trim(project_rootname),                         &
                               len_trim(project_rootname),                     &
                               trim(title), len_trim(title),                   &
                               trim(flow_dir), len_trim(flow_dir),             &
                               VISIT_F77NULLSTRING, VISIT_F77NULLSTRINGLEN,    &
                               VISIT_F77NULLSTRING, VISIT_F77NULLSTRINGLEN,    &
                               VISIT_F77NULLSTRING, VISIT_F77NULLSTRINGLEN)
!     if (err != VISIT_OKAY)
    endif

    if ( present(sadj) ) then
      call updatemesh(grid, soln, sadj)
    else
      call updatemesh(grid, soln)
    endif

    run        =  .true.
    one        =  .false.
    autoupdate =  .false.

#else

!   artificially use the argument data to avoid compiler warnings
    if (.false.) then
      if (grid%nnodes0 > 0 .or. soln%n_tot > 0 .or. crow%nnz0 > 0 .or.         &
          raw_grid_data%twod_mode .or. len_trim(flow_dir) > 0 .or.             &
          associated(sadj%rlam)) then
      end if
    end if

#endif

  end subroutine initialize

!=============================== PROCESS_STEP ================================80
!
! Process a timestep of the solution
!
!=============================================================================80

  logical function step()
#ifdef HAVE_VISIT
    use lmpi,                  only : lmpi_master, lmpi_bcast
!   use lmpi, only: lmpi_id

    integer visitstate, myresult, err
#endif

    continue

    step = .true.

#ifdef HAVE_VISIT
    if (autoupdate) then
      err = visittimestepchanged() ! Tell VisIt that the timestep changed
      err = visitupdateplots() ! Tell VisIt to update its plots
    endif

    if (.not. one) then
      run = .true.
    else
      run = .false.
    endif

    one = .false.

    eventloop : do

      ! Detect input from VisIt on processor 0 and then broadcast the
      ! results of that input to all processors.
!write(*,*) 'step ',run, one, autoupdate
      if (lmpi_master) then
        if (run .or. one) then
          visitstate = visitdetectinput(0, -1)
        else
          visitstate = visitdetectinput(1, -1)
        endif
      endif
      call lmpi_bcast(visitstate)

      if (visitstate == 0) then

!       write(*,*) 'There was no VisIt input, continue simulation...'
        step = .true.
        return

      elseif (visitstate == 1) then

        if (lmpi_master)                                                       &
          write(*,*) 'VisIt is trying to connect to this simulation...'
        run = .false.
        one = .false.
        myresult = visitattemptconnection()
        if (myresult == 1) then
          if (lmpi_master)                                                     &
            write(*,*) 'VisIt connected!'
        else
          write(*,*) 'VisIt did not connect!', myresult
        endif

      elseif (visitstate == 2) then

!       if (lmpi_master)                                                       &
!         write(*,*) 'VisIt wants to tell the engines something...'
        run = .false.
        one = .false.

        if (.not. processvisitcommand()) then
          write(*,*) 'Failed to process VisIt command, disconnecting...'
          myresult = visitdisconnect()
          run        = .true.
          step       = .true.
          autoupdate = .false.
          return
        endif

      elseif (visitstate == 3) then

!       if (lmpi_master)                                                       &
!         write(*,*) 'VisIt wants to tell the engine something via console...'

      else

        write(*,*) 'FATAL: Unrecoverable VisIt error!', visitstate
        step = .true.
        return

      endif

    enddo eventloop
#endif

  end function step

!================================= SHUTDOWN ==================================80
!
! Shutdown the co-processing environment
!
!=============================================================================80

  subroutine shutdown()
#ifdef HAVE_VISIT
    use lmpi,                  only : lmpi_master
#endif
    continue

#ifdef HAVE_VISIT
    if (lmpi_master)                                                           &
      write(*,*) 'Shutdown visit'
    if (allocated(ghost)) deallocate(ghost)
    if (allocated(cellconnects)) deallocate(cellconnects)
#endif

  end subroutine shutdown

#ifdef HAVE_VISIT
!================================ UPDATEMESH =================================80
!
! Update the mesh pointers to agree with updated simulation mesh
!
!=============================================================================80

  subroutine updatemesh(grid, soln, sadj)
    use grid_types,            only : grid_type
    use solution_types,        only : soln_type
    use solution_adj,          only : sadj_type

    type(grid_type),                          intent(in   ) :: grid
    type(soln_type),                          intent(in   ) :: soln
    type(sadj_type), optional,                intent(in   ) :: sadj

    continue

    ! Set pointers to the solver arrays
    x        => grid%x
    y        => grid%y
    z        => grid%z
    elem     => grid%elem

    slen     => grid%slen
    idistfcn =  grid%idistfcn

    nnodes0  =  grid%nnodes0
    nnodes01 =  grid%nnodes01
    nelem    =  grid%nelem

    ! Ghost node flags
    if (.not. allocated(ghost)) then
      allocate(ghost(nnodes01)); ghost = VISIT_GHOSTNODE_REAL
    endif
    ghost(nnodes0+1:nnodes01) = VISIT_GHOSTNODE_INTERIOR_BOUNDARY

    bc       => grid%bc
    nbound   =  grid%nbound;

    ndim     =  soln%ndim
    q_dof    => soln%q_dof
    res      => soln%res

    n_tot    =  soln%n_tot
    dof0     =  soln%dof0
    eqn_set  =  soln%eqn_set

    rmshist  => soln%rmshist
    totforce => soln%totforce

    n_grd    =  soln%n_grd
    gradx    => soln%gradx
    grady    => soln%grady
    gradz    => soln%gradz

    n_turb   =  soln%n_turb
    turb     => soln%turb
    turbres  => soln%turbres
    amut     => soln%amut

    adjoint_mode = .false.
    nullify(sadj_rlam)
    if ( present(sadj) ) then
      adjoint_mode = .true.
      sadj_rlam = sadj%rlam
    endif

  end subroutine updatemesh

!========================== PROCESSESVISITCOMMAND ============================80
!
! Prosses the commands sent by VisIt
!
!=============================================================================80

  logical function processvisitcommand()
    use lmpi,                  only : lmpi_master,lmpi_bcast
!   use lmpi, only: lmpi_id

    integer                     :: command, success
    logical                     :: more

    continue

!   write(*,*) 'ProcessVisItCommand'
    processvisitcommand = .true.

    if (lmpi_master) then
!   write(*,*) 'Master ProcessVisItCommand', lmpi_id
      success = visitprocessenginecommand()
!   write(*,*) 'Master ProcessVisItCommand', success

      if (success == VISIT_OKAY) then
        command = VISIT_COMMAND_SUCCESS
        processvisitcommand = .true.
      else
        command = VISIT_COMMAND_FAILURE
        processvisitcommand = .false.
      endif
!   write(*,*) 'Master ProcessVisItCommand', command
      call lmpi_bcast(command)

    else
      more = .true.
      do while(more)
        call lmpi_bcast(command)
!   write(*,*) 'Slave ProcessVisItCommand', lmpi_id, command
        if (command == VISIT_COMMAND_PROCESS) then
            success = visitprocessenginecommand()
        elseif (command == VISIT_COMMAND_SUCCESS) then
            processvisitcommand = .true.
            more = .false.
        else ! command == VISIT_COMMAND_FAILURE
            processvisitcommand = .false.
            more = .false.
        endif
      enddo
    endif

  end function processvisitcommand

!========================== COPY_FACES_AND_CELLS =============================80
!
! Copy the boundary and volume mesh connectivity into the form needed by VisIt
!
!=============================================================================80

  subroutine copy_faces_and_cells()
    use element_defs,          only : type_tet, type_pyr, type_prz, type_hex
!   use lmpi,                  only : lmpi_id

    implicit none

    integer                                            :: ib, ielem
    integer                                            :: ntri, nquad
    integer                                            :: ntet, npyr, nprz, nhex
    integer                                            :: itype
    integer                                            :: icell, iface, inode
    integer,  dimension(8)                             :: map

    continue

    ! Boundary faces first
    ntri = 0; nquad = 0;

    boundary_cnt_loop : do ib = 1,nbound
      ntri  = ntri  + bc(ib)%nbfacet
      nquad = nquad + bc(ib)%nbfaceq
    end do boundary_cnt_loop

    ! Then volume elements
    ntet = 0; npyr = 0; nprz = 0; nhex = 0

    do ielem = 1, nelem
      select case (elem(ielem)%type_cell)
        case (type_tet)
          ntet = ntet + elem(ielem)%ncell
        case (type_pyr)
          npyr = npyr + elem(ielem)%ncell
        case (type_prz)
          nprz = nprz + elem(ielem)%ncell
        case (type_hex)
          nhex = nhex + elem(ielem)%ncell
      end select
    enddo

    ! New Connectivity for VisIt (bias-0, outward normals, VTK node ordering)
    if (.not. allocated(cellconnects)) then
      allocate(cellconnects(4*ntri+5*nquad+5*ntet+6*npyr+7*nprz+9*nhex))
      cellconnects = 0
    endif

    !
    ! Assign connectivity
    !

    lconn   = 0
    ncell01 = 0

    boundary_loop : do ib = 1,nbound
      triangles : do iface = 1, bc(ib)%nbfacet
        ncell01 = ncell01 + 1
        lconn = lconn + 1
        cellconnects(lconn) = VISIT_CELL_TRI
        do inode = 1, 3
          lconn = lconn + 1
          cellconnects(lconn) = bc(ib)%ibnode(bc(ib)%f2ntb(iface,inode)) - 1
        enddo
!       cell = bc(ib)%f2ntb(iface,4)
!       material(cell) = ?
      enddo triangles

      quads : do iface = 1, bc(ib)%nbfaceq
        ncell01 = ncell01 + 1
        lconn = lconn + 1
        cellconnects(lconn) = VISIT_CELL_QUAD
        do inode = 1, 4
          lconn = lconn + 1
          cellconnects(lconn) = bc(ib)%ibnode(bc(ib)%f2nqb(iface,inode)) - 1
        enddo
!       cell = bc(ib)%f2nqb(iface,4)
!       material(cell) = ?
      enddo quads
    end do boundary_loop

    ! local elements
    do ielem = 1, nelem
      select case (elem(ielem)%type_cell)
        case (type_tet)
          itype = VISIT_CELL_TET
          map(1) = 1; map(2) = 3; map(3) = 2; map(4) = 4
        case (type_pyr)
          itype = VISIT_CELL_PYR
          map(1) = 1; map(2) = 4; map(3) = 3; map(4) = 2; map(5) = 5
        case (type_prz)
          itype = VISIT_CELL_WEDGE
          map(1) = 1; map(2) = 6; map(3) = 4; map(4) = 2; map(5) = 5
          map(6) = 3
        case (type_hex)
          itype = VISIT_CELL_HEX
          map(1) = 1; map(2) = 2; map(3) = 4; map(4) = 3; map(5) = 5
          map(6) = 6; map(7) = 8; map(8) = 7
      end select

      cell_loop : do icell = 1, elem(ielem)%ncell
        ncell01 = ncell01 + 1

        lconn   = lconn + 1
        cellconnects(lconn) = itype

        node_loop : do inode = 1, elem(ielem)%node_per_cell
          lconn = lconn + 1
          cellconnects(lconn) = elem(ielem)%c2n(map(inode),icell) - 1 ! bias 0
        enddo node_loop

!       write(*,*) 'copy_faces_and_cells'
!       write(*,*) lmpi_id,icell,lconn,                                        &
!           (cellconnects(inode),inode=lconn-elem(ielem)%node_per_cell,lconn)
      enddo cell_loop
    enddo

  end subroutine copy_faces_and_cells

!========================== REGISTERCONSTEXPRESSIONS =========================80
!
! Register pre-defined constant epressions
!
!=============================================================================80

subroutine registerconstexpressions(md)
  use kinddefs,              only : dp
  use solution_types,        only : compressible
  use info_depr,             only : xmach
  use fluid,                 only : gamma

  implicit none
  integer,             intent(in) :: md

  character(len=80)               :: p_inf, cp_factor, gamma_str
  integer                         :: emd, err

  continue

  if (eqn_set == compressible) then
    write(p_inf,"(e20.12)") 1._dp/gamma
    write(cp_factor,"(e20.12)") gamma*xmach**2
  else
    p_inf     = '1.0'
    cp_factor = '1.0'
  endif
  write(gamma_str,"(e20.12)") gamma

  ! Add expressions for common constants
  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    err = visitmdexprsetname(emd, 'gamma', 5)
    err = visitmdexprsetdefinition(emd, trim(gamma_str), len_trim(gamma_str))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    err = visitmdexprsetname(emd, 'p_inf', 5)
    err = visitmdexprsetdefinition(emd, trim(p_inf), len_trim(p_inf))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    err = visitmdexprsetname(emd, 'cp_factor', 9)
    err = visitmdexprsetdefinition(emd, trim(cp_factor), len_trim(cp_factor))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

end subroutine registerconstexpressions

!=========================== REGISTERMESHVARIABLES ===========================80
!
! Register variables for a mesh
!
!=============================================================================80

subroutine registermeshvariables(md, project)
  use solution_types,        only : compressible, generic_gas
  use generic_gas_map,       only : n_energy
  use string_utils,          only : int_to_s

  implicit none
  integer,             intent(in) :: md
  character(*),        intent(in) :: project

  integer                         :: plen
  integer                         :: qmd, err
  integer                         :: i
  character(80)                   :: vname

  continue

  plen = len_trim(project)

  ! Note: mesh_name // '/' // var will make pulldown for vars off of mesh_name

  if (eqn_set == compressible .or. eqn_set == generic_gas) then
    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/rho'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/u'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/v'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/w'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/p'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  do i = 1,5
    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/res' // int_to_s(i)
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif
  enddo

  if (adjoint_mode) then
    do i = 1,7
      if (visitmdvaralloc(qmd) == VISIT_OKAY) then
        vname = trim(adjustl(project)) // '/lambda' // int_to_s(i)
        err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
        err = visitmdvarsetmeshname(qmd, project, plen)
        err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
        err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
        err = visitmdsimaddvariable(md, qmd)
      endif
    enddo
  endif

  if (n_turb > 0) then
    do i = 1,n_turb
      if (visitmdvaralloc(qmd) == VISIT_OKAY) then
        vname = trim(adjustl(project)) // '/turb' // int_to_s(i)
        err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
        err = visitmdvarsetmeshname(qmd, project, plen)
        err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
        err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
        err = visitmdsimaddvariable(md, qmd)
      endif
      if (visitmdvaralloc(qmd) == VISIT_OKAY) then
        vname = trim(adjustl(project)) // '/turres' // int_to_s(i)
        err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
        err = visitmdvarsetmeshname(qmd, project, plen)
        err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
        err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
        err = visitmdsimaddvariable(md, qmd)
      endif
    enddo

    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/mu_t'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif
!uuprime
!vvprime
!wwprime
!uvprime
!uwprime
!vwprime
!yplus
!skinfr
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/gradx'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_ARRAY)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/grady'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_ARRAY)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/gradz'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_ARRAY)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/q_criterion'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_ARRAY)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (visitmdvaralloc(qmd) == VISIT_OKAY) then
    vname = trim(adjustl(project)) // '/hrles_blend'
    err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
    err = visitmdvarsetmeshname(qmd, project, plen)
    err = visitmdvarsettype(qmd, VISIT_VARTYPE_ARRAY)
    err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
    err = visitmdsimaddvariable(md, qmd)
  endif

  if (eqn_set == generic_gas) then
    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/tt'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif

    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/etot'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif

    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/htot'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif

    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/sonic'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif

    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/mol_wt'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif

    if (visitmdvaralloc(qmd) == VISIT_OKAY) then
      vname = trim(adjustl(project)) // '/mu'
      err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
      err = visitmdvarsetmeshname(qmd, project, plen)
      err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
      err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
      err = visitmdsimaddvariable(md, qmd)
    endif

    if (n_energy > 1) then
      if (visitmdvaralloc(qmd) == VISIT_OKAY) then
        vname = trim(adjustl(project)) // '/tv'
        err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
        err = visitmdvarsetmeshname(qmd, project, plen)
        err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
        err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
        err = visitmdsimaddvariable(md, qmd)
      endif

      if (visitmdvaralloc(qmd) == VISIT_OKAY) then
        vname = trim(adjustl(project)) // '/ev'
        err = visitmdvarsetname(qmd, trim(vname), len_trim(vname))
        err = visitmdvarsetmeshname(qmd, project, plen)
        err = visitmdvarsettype(qmd, VISIT_VARTYPE_SCALAR)
        err = visitmdvarsetcentering(qmd, VISIT_VARCENTERING_NODE)
        err = visitmdsimaddvariable(md, qmd)
      endif
    endif
  endif

end subroutine registermeshvariables

!========================== REGISTERMESHEXPRESSIONS ==========================80
!
! Register pre-defined epressions for a mesh
!
!=============================================================================80

subroutine registermeshexpressions(md, project)
  use solution_types,        only : compressible, incompressible, generic_gas

  implicit none
  integer,             intent(in) :: md
  character(*),        intent(in) :: project

  character(80)                   :: ename
  character(256)                  :: expr
  integer                         :: emd, err

  continue

  ! Add expressions for common variables
  ! Note: mesh_name // '/' // var will make pulldown for vars off of mesh_name
  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/Cp'
    expr  = '2.0*(<' // trim(adjustl(project)) // '/p>/p_inf-1.0)/cp_factor'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/Temperature'
    expr  = 'gamma*<' // trim(adjustl(project)) // '/p>/' //                   &
                  '<' // trim(adjustl(project)) // '/rho>'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if( eqn_set /= incompressible ) then
    if (visitmdexpralloc(emd) == VISIT_OKAY) then
      ename = trim(adjustl(project)) // '/Mach'
      expr  = 'sqrt((sqr(<' // trim(adjustl(project)) // '/u>) + '  //         &
                    'sqr(<' // trim(adjustl(project)) // '/v>) + '  //         &
                    'sqr(<' // trim(adjustl(project)) // '/w>)) / ' //         &
                    '<'     // trim(adjustl(project)) // '/Temperature>)'

      err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
      err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
      err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
      err = visitmdsimaddexpression(md, emd)
    endif
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/Entropy'
    expr  = '(<' // trim(adjustl(project)) // '/p>/p_inf) / ' //               &
            '<' // trim(adjustl(project)) // '/rho>^gamma - 1.0'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/vorticity'
    expr  = 'curl( {<' // trim(adjustl(project)) // '/u>, <'                   &
                       // trim(adjustl(project)) // '/v>, <'                   &
                       // trim(adjustl(project)) // '/w>} )'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_VECTOR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/vort_x'
    expr  = '<' // trim(adjustl(project)) // '/vorticity>[0]'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/vort_y'
    expr  = '<' // trim(adjustl(project)) // '/vorticity>[1]'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/vort_z'
    expr  = '<' // trim(adjustl(project)) // '/vorticity>[2]'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (visitmdexpralloc(emd) == VISIT_OKAY) then
    ename = trim(adjustl(project)) // '/div_vel'
    expr  = 'divergence( {<' // trim(adjustl(project)) // '/u>, <'             &
                             // trim(adjustl(project)) // '/v>, <'             &
                             // trim(adjustl(project)) // '/w>} )'

    err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
    err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
    err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
    err = visitmdsimaddexpression(md, emd)
  endif

  if (eqn_set == compressible) then
!   if (visitmdexpralloc(emd) == VISIT_OKAY) then
!     ename = trim(adjustl(project)) // '/Hi'
!     expr  = '2.0*(<' // trim(adjustl(project)) // '/p>/p_inf-1.0)/cp_factor'

!     err = visitmdexprsetname(emd, trim(ename), len_trim(ename))
!     err = visitmdexprsetdefinition(emd, trim(expr), len_trim(expr))
!     err = visitmdexprsettype(emd, VISIT_VARTYPE_SCALAR)
!     err = visitmdsimaddexpression(md, emd)
!   endif

!   Ptot
!   Ttot
!   hTot
!   mach
!   temperature
!   entropy
  endif

  if (eqn_set == incompressible) then
!   entropy
  endif

  if (eqn_set == generic_gas) then
!   Tv
!   mach
!   entropy
  endif

!  cfx
!  cfy
!  cfz
!  cq
!  heating
!  qrad
!  shear_x
!  shear_y
!  shear_z
!  slen
!  mu_t
!  turb1
!  turb2
!  turb_mach
!  iblank
!  imesh
!  vort_x
!  vort_y
!  vort_z
!  vort_mag
!  div_vel
!  q_criterion

!  yplus
!  skinfr
!  turbindex
!  uuprime,vvprime,wwprime,uvprime,uwprime,vwprime

end subroutine registermeshexpressions

!=============================== REGISTERCURVES ==============================80
!
! Register pre-defined constant epressions
!
!=============================================================================80

subroutine registercurves(md)

  use nml_nonlinear_solves, only : itime

  implicit none
  integer,             intent(in) :: md

  integer                         :: cmd, err
  integer                         :: i
  character(len=80)               :: cnum
  character(len=80)               :: cname
  character(len=80)               :: ylabel

  continue

  ! Add curves

  ! Mean Flow Residual
  do i = 1,5
    write(cnum,"(i0)") i
    cname = 'R' // trim(adjustl(cnum)) // ''

    if (visitmdcurvealloc(cmd) == VISIT_OKAY) then
      err = visitmdcurvesetname(cmd, cname, len_trim(cname))
      if (itime == 0) then
        err = visitmdcurvesetxlabel(cmd, "Iteration", 9)
      else
        err = visitmdcurvesetxlabel(cmd, "Timestep", 8)
      endif
      ylabel = 'Log ' // cname // ''
      err = visitmdcurvesetylabel(cmd, ylabel, len_trim(ylabel))
      err = visitmdsimaddcurve(md, cmd)
    endif
  enddo

  if (n_turb > 0) then
    do i = 1,n_turb
      write(cnum,"(i0)") i
      cname = 'TurbR' // trim(adjustl(cnum)) // ''

      if (visitmdcurvealloc(cmd) == VISIT_OKAY) then
        err = visitmdcurvesetname(cmd, cname, len_trim(cname))
        if (itime == 0) then
          err = visitmdcurvesetxlabel(cmd, "Iteration", 9)
        else
          err = visitmdcurvesetxlabel(cmd, "Timestep", 8)
        endif
        ylabel = 'Log ' // cname // ''
        err = visitmdcurvesetylabel(cmd, ylabel, len_trim(ylabel))
        err = visitmdsimaddcurve(md, cmd)
      endif
    enddo
  endif

  if (adjoint_mode) then
    do i = 1,7
      write(cnum,"(i0)") i
      cname = 'Lambda' // trim(adjustl(cnum)) // ''

      if (visitmdcurvealloc(cmd) == VISIT_OKAY) then
        err = visitmdcurvesetname(cmd, cname, len_trim(cname))
        if (itime == 0) then
          err = visitmdcurvesetxlabel(cmd, "Iteration", 9)
        else
          err = visitmdcurvesetxlabel(cmd, "Timestep", 8)
        endif
        ylabel = 'Log ' // cname // ''
        err = visitmdcurvesetylabel(cmd, ylabel, len_trim(ylabel))
        err = visitmdsimaddcurve(md, cmd)
      endif
    enddo
  endif

  if (visitmdcurvealloc(cmd) == VISIT_OKAY) then
    err = visitmdcurvesetname(cmd, "Cl", 2)
    if (itime == 0) then
      err = visitmdcurvesetxlabel(cmd, "Iteration", 9)
    else
      err = visitmdcurvesetxlabel(cmd, "Timestep", 8)
    endif
    err = visitmdcurvesetylabel(cmd, "Cl", 6)
    err = visitmdsimaddcurve(md, cmd)
  endif

  if (visitmdcurvealloc(cmd) == VISIT_OKAY) then
    err = visitmdcurvesetname(cmd, "Cd", 2)
    if (itime == 0) then
      err = visitmdcurvesetxlabel(cmd, "Iteration", 9)
    else
      err = visitmdcurvesetxlabel(cmd, "Timestep", 8)
    endif
    err = visitmdcurvesetylabel(cmd, "Cd", 6)
    err = visitmdsimaddcurve(md, cmd)
  endif

end subroutine registercurves

#endif

end module visit

#ifdef HAVE_VISIT

!=============================================================================80
!
! The following functions must be defined to satisfy the callbacks in the
! visitfortransimV2interface lib.
!
!=============================================================================80

!========================== VISITCOMMANDCALLBACK =============================80
!
! Required VisIt callback must satisfy the visitfortransimV2interface lib.
!
!=============================================================================80

subroutine visitcommandcallback(cmd, lcmd, args, largs)
  use visit,                 only : run, one, autoupdate
  use system_extensions,     only : se_open
  use file_utils,            only : available_unit
  use lmpi,                  only : lmpi_master
  use info_depr,             only : ntt

  implicit none
  include "visitfortransimV2interface.inc"
  character(80) cmd, args
  integer       lcmd, largs, err
  integer       iunit

  continue

  if (lmpi_master) then
    if (visitstrcmp(cmd, lcmd, "autoupdate", 10) == 0) then
      if (autoupdate) then
        write(*,*) 'visitcommandcallback: ',cmd(1:lcmd),': Off'
      else
        write(*,*) 'visitcommandcallback: ',cmd(1:lcmd),': On'
      endif
    else
      write(*,*) 'visitcommandcallback: ',cmd(1:lcmd)
    endif
  endif

  if (visitstrcmp(cmd, lcmd, "halt", 4) == 0) then
    run = .false.
  elseif (visitstrcmp(cmd, lcmd, "step", 4) == 0) then
    one = .true.
  elseif (visitstrcmp(cmd, lcmd, "run", 3) == 0) then
    run = .true.
  elseif (visitstrcmp(cmd, lcmd, "update", 6) == 0) then
    err = visittimestepchanged() ! Tell VisIt that the timestep changed
    err = visitupdateplots() ! Tell VisIt to update its plots
  elseif (visitstrcmp(cmd, lcmd, "autoupdate", 10) == 0) then
    autoupdate = .not. autoupdate
  elseif (visitstrcmp(cmd, lcmd, "stop", 4) == 0) then
    if (lmpi_master) then
      iunit  = available_unit()
      call se_open(iunit,file='stop.dat')
      write(iunit,*) ntt
      close(iunit)
    endif
    run = .true. ! so that it will continue to next iteration and stop
  endif

end subroutine visitcommandcallback

!========================== VISITBROADCASTINTFUNCTION ========================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitbroadcastintfunction(value,sender)
  use lmpi,                  only : lmpi_bcast

  implicit none
  integer             :: value, sender
  include "visitfortransimV2interface.inc"

  continue

! write(*,*) 'visitbroadcastintfunction: ', value
  call lmpi_bcast(value,sender)
  visitbroadcastintfunction = VISIT_OKAY
end function visitbroadcastintfunction

!======================== VISITBROADCASTSTRINGFUNCTION =======================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitbroadcaststringfunction(str,lstr,sender)
  use lmpi,                  only : lmpi_bcast, lmpi_id
#ifdef HAVE_FORTRAN_C_INTEROPERABILITY
  use iso_c_binding,         only : c_null_char
#endif

  implicit none
  character(len=lstr) :: str
  integer             :: lstr, sender
  include "visitfortransimV2interface.inc"

  continue

  call lmpi_bcast(str(1:lstr),sender)
! call lmpi_bcast(str,sender)

! if( sender == lmpi_id )                                                      &
!   write(*,*) 'visitbroadcaststringfunction: ', str(1:lstr-1)

#ifdef HAVE_FORTRAN_C_INTEROPERABILITY
! if(str(1:1) /= c_null_char)                                                  &
!   write(*,*) 'visitbroadcaststringfunction: ', str(1:lstr-1)
#else
! if(str(1:1) /= char(0))                                                      &
!   write(*,*) 'visitbroadcaststringfunction: ', str(1:lstr-1)
#endif
  visitbroadcaststringfunction = VISIT_OKAY
end function visitbroadcaststringfunction

!========================= VISITSLAVEPROCESSCALLBACK =========================80
!
! Required VisIt callback
!
!=============================================================================80

subroutine visitslaveprocesscallback()
  use lmpi,                  only : lmpi_bcast
  use visit,                 only : VISIT_COMMAND_PROCESS

  implicit none
  integer             :: command = VISIT_COMMAND_PROCESS

  continue

! write(*,*) 'visitslaveprocesscallback: '

  call lmpi_bcast(command)
end subroutine visitslaveprocesscallback

!=========================== VISITACTIVATETIMESTEP ===========================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitactivatetimestep()
  implicit none
  include "visitfortransimV2interface.inc"

  continue

! write(*,*) 'visitactivatetimestep: '

  visitactivatetimestep = VISIT_OKAY
end function visitactivatetimestep

!============================= VISITGETMETADATA ==============================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetmetadata()
  use kinddefs,              only : dp
  use io,                    only : project_rootname, prior_iters
  use lmpi,                  only : lmpi_nproc
  use info_depr,             only : simulation_time, ntt
  use nml_nonlinear_solves,  only : itime
  use visit,                 only : run, one, nbound, bc,                      &
                                    registerconstexpressions,                  &
                                    registermeshvariables,                     &
                                    registermeshexpressions,                   &
                                    registercurves

  implicit none
  include "visitfortransimV2interface.inc"
  character(80)                   :: project
  integer                         :: plen
  integer                         :: md, cmd, mesh, mat, err
  character(len=80)               :: p_inf, cp_factor
  real(dp)                        :: time_val
  character(len=80)               :: bndry
  character(len=256)              :: bc_family, bcname
  integer                         :: ib

  continue

! write(*,*) 'visitgetmetadata: '

  md = VISIT_INVALID_HANDLE

  if (visitmdsimalloc(md) == VISIT_OKAY) then

    if (itime == 0) then
      time_val = real(ntt+prior_iters, dp)
    else
      time_val = simulation_time
    end if
    err = visitmdsimsetcycletime(md, ntt, time_val)

    if (run .or. one) then
      err = visitmdsimsetmode(md, VISIT_SIMMODE_RUNNING)
    else
      err = visitmdsimsetmode(md, VISIT_SIMMODE_STOPPED)
    endif

    ! Add simulation commands
    err = visitmdcmdalloc(cmd)
    if (err == VISIT_OKAY) then
      err = visitmdcmdsetname(cmd, "halt", 4)
      err = visitmdsimaddgenericcommand(md, cmd)
    endif

    err = visitmdcmdalloc(cmd)
    if (err == VISIT_OKAY) then
      err = visitmdcmdsetname(cmd, "step", 4)
      err = visitmdsimaddgenericcommand(md, cmd)
    endif

    err = visitmdcmdalloc(cmd)
    if (err == VISIT_OKAY) then
      err = visitmdcmdsetname(cmd, "run", 3)
      err = visitmdsimaddgenericcommand(md, cmd)
    endif

    err = visitmdcmdalloc(cmd)
    if (err == VISIT_OKAY) then
      err = visitmdcmdsetname(cmd, "autoupdate", 10)
      err = visitmdsimaddgenericcommand(md, cmd)
    endif

    err = visitmdcmdalloc(cmd)
    if (err == VISIT_OKAY) then
      err = visitmdcmdsetname(cmd, "stop", 4)
      err = visitmdsimaddgenericcommand(md, cmd)
    endif

    err = visitmdcmdalloc(cmd)
    if (err == VISIT_OKAY) then
      err = visitmdcmdsetname(cmd, "update", 6)
      err = visitmdsimaddgenericcommand(md, cmd)
    endif

    project = trim(project_rootname)
    plen    = len_trim(project)

    ! Add mesh
    if (visitmdmeshalloc(mesh) == VISIT_OKAY) then
      err = visitmdmeshsetname(mesh, project, plen)
      err = visitmdmeshsetmeshtype(mesh, VISIT_MESHTYPE_UNSTRUCTURED)
      err = visitmdmeshsettopologicaldim(mesh, 3)
      err = visitmdmeshsetspatialdim(mesh, 3)
      err = visitmdmeshsetnumdomains(mesh, lmpi_nproc)

      err = visitmdsimaddmesh(md, mesh)
    endif

    ! Add expression constants
    call registerconstexpressions(md)

    ! Add solution variables
    call registermeshvariables(md, project)
    call registermeshexpressions(md, project)

    ! Add residual histories
    call registercurves(md)

    ! Add materials
    if (visitmdmatalloc(mat) == VISIT_OKAY) then
      err = visitmdmatsetname(mat, "Materials", 9);
      err = visitmdmatsetmeshname(mat, project, plen);

      boundary_loop : do ib = 1,nbound
        write(bndry,"(i0)") ib

        bc_family = trim(bc(ib)%bc_family)

        if ( bc_family == '' ) then
          bcname = 'Boundary_' // trim(adjustl(bndry)) // ''
        else
          bcname = 'Boundary_' // trim(adjustl(bndry)) // '_' //               &
                   trim(adjustl(bc_family)) // ''
        end if

        err = visitmdmataddmaterialname(mat, bcname, len_trim(bcname));
      end do boundary_loop

      err = visitmdmataddmaterialname(mat, "Volume", 6);

      err = visitmdsimaddmaterial(md, mat)
    endif

  endif

  visitgetmetadata = md
end function visitgetmetadata

!=============================== VISITGETMESH ================================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetmesh(domain, aname, lname)
  use io,                    only : project_rootname
  use string_utils,          only : char2int
  use visit,                 only : x, y, z, nnodes01, ncell01, lconn,         &
                                    cellconnects, ghost, copy_faces_and_cells

  implicit none
  character(80),          intent(in) :: aname
  integer,                intent(in) :: domain, lname

  include "visitfortransimV2interface.inc"
  integer                            :: mesh, vx, vy, vz, vc2n, vghost, err

  continue

! write(*,*) 'visitgetmesh: ', domain, ' ', aname(1:lname), ' ', lname, ' ',   &
!            trim(project_rootname)

  mesh = VISIT_INVALID_HANDLE

  if (visitstrcmp(aname, lname, trim(project_rootname),                        &
                  len_trim(project_rootname)) == 0) then

    if (visitucdmeshalloc(mesh) == VISIT_OKAY) then
      ! Nodes
      err = visitvardataalloc(vx)
      err = visitvardataalloc(vy)
      err = visitvardataalloc(vz)
      err = visitvardatasetd(vx, VISIT_OWNER_SIM, 1, nnodes01, x)
      err = visitvardatasetd(vy, VISIT_OWNER_SIM, 1, nnodes01, y)
      err = visitvardatasetd(vz, VISIT_OWNER_SIM, 1, nnodes01, z)

      err = visitucdmeshsetcoordsxyz(mesh, vx, vy, vz)

      ! VisIt Connectivity (bias-0, outward normals, VTK node ordering)
      call copy_faces_and_cells()

      err = visitvardataalloc(vc2n)
      err = visitvardataseti(vc2n, VISIT_OWNER_SIM, 1, lconn, cellconnects)

      err = visitucdmeshsetconnectivity(mesh, ncell01, vc2n)

      ! Ghost nodes
      err = visitvardataalloc(vghost)
      err = visitvardataseti(vghost, VISIT_OWNER_SIM, 1, nnodes01, ghost)
      err = visitucdmeshsetghostnodes(mesh, vghost)
    endif

  endif

  visitgetmesh = mesh
end function visitgetmesh

!============================= VISITGETVARIABLE ==============================80
!
! Required VisIt callback
!
!=============================================================================80
integer function visitgetvariable(domain, aname, lname)
  use kinddefs,              only : dp
  use io,                    only : project_rootname
  use string_utils,          only : char2int
  use solution_types,        only : compressible, incompressible, generic_gas
  use visit,                 only : nnodes01, n_tot, dof0, q_dof, eqn_set,     &
                                    n_grd, gradx, grady, gradz,                &
                                    res, turb, turbres, amut, idistfcn, slen,  &
                                    adjoint_mode, sadj_rlam
  use thermo,                only : q_type, conserved_q_type, primitive_q_type,&
                                    etop, ptoe
  use generic_gas_map,       only : n_momx, n_momy, n_momz, n_density, n_etot, &
                                    n_temperature_j, n_energy, n_energy_last,  &
                                    n_pressure_k, n_sonic_k,                   &
                                    n_molecular_weight, n_species, n_amu_k

  implicit none
  character(80),               intent(in) :: aname
  integer,                     intent(in) :: domain, lname
  include "visitfortransimV2interface.inc"
  integer                                 :: v, err, n_q_dof, i
  real(dp), dimension(:), pointer         :: q
  real(dp), dimension(:), allocatable     :: var
  real(dp)                                :: alen
  real(dp)                                :: q_criterion, hrles_blend
 INTEGER, DIMENSION (1)                  :: MAX_LOC, MIN_LOC
 integer                                 :: owner, ncomps, ntuples

  continue

! write(*,*) 'visitgetvariable: ', domain, ' ', aname(1:lname), ' ', lname

  v = VISIT_INVALID_HANDLE

  ! Convert to primitive variables
  n_q_dof = size(q_dof,2)
  if (q_type /= primitive_q_type)                                              &
    call etop(n_q_dof, q_dof, n_tot, eqn_set)

  if (visitstrcmp(aname(lname-2:lname), 3, "rho", 3) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      select case ( eqn_set )
      case ( compressible )
        var = q_dof(1,:)
      case ( incompressible )
        var = 1._dp
      case ( generic_gas )
        var = q_dof(n_density,:)
      end select
!     q => q_dof(1,:)
!     MIN_LOC = minloc(q)
!     MAX_LOC = maxloc(q)
!     write(*,*) 'Density Min/Max ',q(MIN_LOC),q(MAX_LOC)
!     err = visitvardatasetd(v, VISIT_OWNER_SIM, n_tot, n_q_dof, q_dof)
!     err = visitvardatasetd(v, VISIT_OWNER_SIM, 1, n_q_dof, q)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
!     var = 0._dp
!     err = visitvardatagetd(v, owner, ncomps, ntuples, var, n_q_dof)
!     MIN_LOC = minloc(var)
!     MAX_LOC = maxloc(var)
!     write(*,*) 'Rho Min/Max ',var(MIN_LOC),var(MAX_LOC)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname:lname), 1, "u", 1) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      select case ( eqn_set )
      case ( compressible )
        var = q_dof(2,:)
      case ( incompressible )
        var = q_dof(2,:)
      case ( generic_gas )
        var = q_dof(n_momx,:) / q_dof(n_density,:)
      end select
!     q => q_dof(2,:)
!     err = visitvardatasetd(v, VISIT_OWNER_SIM, 1, n_q_dof, q)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname:lname), 1, "v", 1) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      select case ( eqn_set )
      case ( compressible )
        var = q_dof(3,:)
      case ( incompressible )
        var = q_dof(3,:)
      case ( generic_gas )
        var = q_dof(n_momy,:) / q_dof(n_density,:)
      end select
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname:lname), 1, "w", 1) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      select case ( eqn_set )
      case ( compressible )
        var = q_dof(4,:)
      case ( incompressible )
        var = q_dof(4,:)
      case ( generic_gas )
        var = q_dof(n_momz,:) / q_dof(n_density,:)
      end select
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname:lname), 1, "p", 1) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      select case ( eqn_set )
      case ( compressible )
        var = q_dof(5,:)
      case ( incompressible )
        var = q_dof(1,:)
      case ( generic_gas )
        var = q_dof(n_pressure_k(1),:)
      end select
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-3:lname-1), 3, "res", 3) == 0) then
    i = char2int(aname(lname:lname))
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = res(i,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (adjoint_mode) then
    if (visitstrcmp(aname(lname-6:lname-1), 6, "lambda", 6) == 0) then
      i = char2int(aname(lname:lname))
      if (visitvardataalloc(v) == VISIT_OKAY) then
        allocate(var(n_q_dof))
        var = sadj_rlam(i,:,1)
        err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
        deallocate(var)
      endif
    endif
  endif

  if (visitstrcmp(aname(lname-4:lname-1), 4, "turb", 4) == 0) then
    i = char2int(aname(lname:lname))
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = turb(i,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-6:lname-1), 6, "turres", 6) == 0) then
    i = char2int(aname(lname:lname))
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = turbres(i,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-3:lname), 4, "mu_t", 4) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      err = visitvardatasetd(v, VISIT_OWNER_SIM, 1, n_q_dof, amut)
    endif
  endif

  if (visitstrcmp(aname(lname-4:lname), 5, "gradx", 5) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      err = visitvardatasetd(v, VISIT_OWNER_SIM, nnodes01, n_grd, gradx)
    endif
  endif

  if (visitstrcmp(aname(lname-4:lname), 5, "grady", 5) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      err = visitvardatasetd(v, VISIT_OWNER_SIM, nnodes01, n_grd, grady)
    endif
  endif

  if (visitstrcmp(aname(lname-4:lname), 5, "gradz", 5) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      err = visitvardatasetd(v, VISIT_OWNER_SIM, nnodes01, n_grd, gradz)
    endif
  endif

  if (visitstrcmp(aname(lname-10:lname), 11, "q_criterion", 11) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      do i = 1,n_q_dof
        var(i) = q_criterion(eqn_set, n_grd, gradx(:,i), grady(:,i),           &
                             gradz(:,i))
      enddo
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-10:lname), 11, "hrles_blend", 11) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      do i = 1,n_q_dof
        alen     = 0.0_dp
        if (idistfcn /= 0) then
          alen   = slen(i)
        end if
        var(i) = hrles_blend(eqn_set, n_tot, n_grd, alen, q_dof(:,i),          &
                             turb(:,i), gradx(:,i), grady(:,i), gradz(:,i))
      enddo
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-1:lname), 2, "tt", 2) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_temperature_j(1),:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-3:lname), 4, "etot", 4) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_etot,:) / q_dof(n_density,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-3:lname), 4, "htot", 4) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = (q_dof(n_etot,:) + q_dof(n_pressure_k(1),:)) / q_dof(n_density,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-4:lname), 5, "sonic", 5) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_sonic_k(1),:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-5:lname), 6, "mol_wt", 6) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_molecular_weight,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-1:lname), 2, "mu", 2) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_amu_k(1),:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-1:lname), 2, "tv", 2) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_temperature_j(2),:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (visitstrcmp(aname(lname-1:lname), 2, "ev", 2) == 0) then
    if (visitvardataalloc(v) == VISIT_OKAY) then
      allocate(var(n_q_dof))
      var = q_dof(n_energy_last,:)
      err = visitvardatasetd(v, VISIT_OWNER_COPY, 1, n_q_dof, var)
      deallocate(var)
    endif
  endif

  if (q_type /= conserved_q_type)                                              &
    call ptoe(n_q_dof, q_dof, n_tot, eqn_set)

  visitgetvariable = v
end function visitgetvariable

!=========================== VISITGETMIXEDVARIABLE ===========================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetmixedvariable(domain, aname, lname)
  implicit none
  integer,             intent(in) :: domain
  character(80),       intent(in) :: aname
  integer,             intent(in) :: lname
  include "visitfortransimV2interface.inc"

  integer                                 :: v

  continue

! write(*,*) 'visitgetmixedvariable: ', domain, ' ', aname(1:lname), ' ', lname

  v = VISIT_INVALID_HANDLE

  visitgetmixedvariable = v
end function visitgetmixedvariable

include 'q_criterion.f90'
include 'hrles_blend.f90'

!=============================== VISITGETCURVE ===============================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetcurve(aname, lname)
  use kinddefs,              only : dp
  use info_depr,             only : simulation_time, ntt
  use nml_nonlinear_solves,  only : itime
  use io,                    only : use_prior, prior_iters, prior_hist
  use visit,                 only : rmshist, totforce, ndim, n_turb
  use string_utils,          only : char2int

  implicit none
  character(80),       intent(in) :: aname
  integer,             intent(in) :: lname
  include "visitfortransimV2interface.inc"
  integer                         :: h, hx, hy, err
  integer                         :: iter, eqn
  real(dp), dimension(ntt)        :: x, y
! integer                         :: n_offset

  continue

! write(*,*) 'visitgetcurve: ', aname(1:lname), ' ', lname

! previous_walltime = 0._dp
! n_offset          = 0
! if ( use_prior == 1 .and. prior_iters > 0 ) then
!   if (prior_iters <= size(prior_hist,1) .and. &
!      njac_hist+6 <= size(prior_hist,2)) then
!     previous_walltime = prior_hist(prior_iters,njac_hist+6)
!     n_offset          = prior_iters
!   end if

  h = VISIT_INVALID_HANDLE

  if (visitstrcmp(aname, 1, "R", 1) == 0) then
    eqn = char2int(aname(2:lname))
    if (eqn > 0 .and. eqn <= ndim) then
      do iter = 1,ntt
        x(iter) = real(iter+prior_iters, dp)
        y(iter) = log10(real(rmshist(eqn,iter,1), dp)/                         &
                        real(rmshist(eqn,1,1), dp))
      enddo

      if (visitcurvedataalloc(h) == VISIT_OKAY) then
        err = visitvardataalloc(hx)
        err = visitvardataalloc(hy)
        err = visitvardatasetd(hx, VISIT_OWNER_COPY, 1, ntt, x)
        err = visitvardatasetd(hy, VISIT_OWNER_COPY, 1, ntt, y)
        err = visitcurvedatasetcoordsxy(h, hx, hy)
      endif
    endif
  endif

  if (visitstrcmp(aname, 5, "TurbR", 5) == 0) then
    eqn = char2int(aname(6:lname))
    if (eqn > 0 .and. eqn <= n_turb) then
      eqn = eqn + ndim
      do iter = 1,ntt
        x(iter) = real(iter+prior_iters, dp)
        y(iter) = log10(real(rmshist(eqn,iter,1), dp)/                         &
                        real(rmshist(eqn,1,1), dp))
      enddo

      if (visitcurvedataalloc(h) == VISIT_OKAY) then
        err = visitvardataalloc(hx)
        err = visitvardataalloc(hy)
        err = visitvardatasetd(hx, VISIT_OWNER_COPY, 1, ntt, x)
        err = visitvardatasetd(hy, VISIT_OWNER_COPY, 1, ntt, y)
        err = visitcurvedatasetcoordsxy(h, hx, hy)
      endif
    endif
  endif

  if (visitstrcmp(aname, 6, "Lambda", 6) == 0) then
    eqn = char2int(aname(7:lname))
    if (eqn > 0 .and. eqn <= ndim+2) then ! 5 + 2 fixed for adjoint???
      do iter = 1,ntt
        x(iter) = real(iter+prior_iters, dp)
        y(iter) = log10(real(rmshist(eqn,iter,1), dp)/                         &
                        real(rmshist(eqn,1,1), dp))
      enddo

      if (visitcurvedataalloc(h) == VISIT_OKAY) then
        err = visitvardataalloc(hx)
        err = visitvardataalloc(hy)
        err = visitvardatasetd(hx, VISIT_OWNER_COPY, 1, ntt, x)
        err = visitvardatasetd(hy, VISIT_OWNER_COPY, 1, ntt, y)
        err = visitcurvedatasetcoordsxy(h, hx, hy)
      endif
    endif
  endif

  if (visitstrcmp(aname, 2, "Cl", 2) == 0) then
    do iter = 1,ntt
      x(iter) = real(iter+prior_iters, dp)
      y(iter) = real(totforce(iter)%cl, dp)
    enddo

    if (visitcurvedataalloc(h) == VISIT_OKAY) then
      err = visitvardataalloc(hx)
      err = visitvardataalloc(hy)
      err = visitvardatasetd(hx, VISIT_OWNER_COPY, 1, ntt, x)
      err = visitvardatasetd(hy, VISIT_OWNER_COPY, 1, ntt, y)
      err = visitcurvedatasetcoordsxy(h, hx, hy)
    endif
  endif

  if (visitstrcmp(aname, 2, "Cd", 2) == 0) then
    do iter = 1,ntt
      x(iter) = real(iter+prior_iters, dp)
      y(iter) = real(totforce(iter)%cd, dp)
    enddo

    if (visitcurvedataalloc(h) == VISIT_OKAY) then
      err = visitvardataalloc(hx)
      err = visitvardataalloc(hy)
      err = visitvardatasetd(hx, VISIT_OWNER_COPY, 1, ntt, x)
      err = visitvardatasetd(hy, VISIT_OWNER_COPY, 1, ntt, y)
      err = visitcurvedatasetcoordsxy(h, hx, hy)
    endif
  endif

  visitgetcurve = h
end function visitgetcurve

!============================ VISITGETDOMAINLIST =============================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetdomainlist(aname, lname)
  use lmpi,                  only : lmpi_id, lmpi_nproc

  implicit none
  character(80),       intent(in) :: aname
  integer,             intent(in) :: lname
  include "visitfortransimV2interface.inc"
  integer                         :: h, dl, err

  continue

! write(*,*) 'visitgetdomainlist: ', aname(1:lname), ' ', lname

  h = VISIT_INVALID_HANDLE

  if (visitdomainlistalloc(h) == VISIT_OKAY) then
    if (visitvardataalloc(dl) == VISIT_OKAY) then
      err = visitvardataseti(dl, VISIT_OWNER_SIM, 1, 1, lmpi_id)
      err = visitdomainlistsetdomains(h, lmpi_nproc, dl)
    endif
  endif

  visitgetdomainlist = h
end function visitgetdomainlist

!=========================== VISITGETDOMAINBOUNDS ============================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetdomainbounds(aname, lname)
  implicit none
  character(80),       intent(in) :: aname
  integer,             intent(in) :: lname
  include "visitfortransimV2interface.inc"
  integer                         :: h

  continue

! write(*,*) 'visitgetdomainbounds: ', aname(1:lname)

  h = VISIT_INVALID_HANDLE

  visitgetdomainbounds = h
end function visitgetdomainbounds

!=========================== VISITGETDOMAINNESTING ===========================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetdomainnesting(aname, lname)
  implicit none
  character(80),       intent(in) :: aname
  integer,             intent(in) :: lname
  include "visitfortransimV2interface.inc"
  integer                         :: h

  continue

! write(*,*) 'visitgetdomainnesting: ', aname(1:lname)

  h = VISIT_INVALID_HANDLE

  visitgetdomainnesting = h
end function visitgetdomainnesting

!============================== VISITGETMATERIAL =============================80
!
! Required VisIt callback
!
!=============================================================================80

integer function visitgetmaterial(domain, aname, lname)
  use visit,                 only : ncell01, nelem, elem, nbound, bc
  use io,                    only : project_rootname
  use string_utils,          only : char2int

  implicit none
  character(80),       intent(in) :: aname
  integer,             intent(in) :: domain, lname
  include "visitfortransimV2interface.inc"
  integer                         :: h
  integer                         :: ib, iface, ielem, icell
  integer                         :: matno, cellid
  integer                         :: err
  character(len=80)               :: bndry
  character(len=256)              :: bc_family, bcname

  continue

! write(*,*) 'visitgetmaterial: ',domain, ' ', aname(1:lname)

  h = VISIT_INVALID_HANDLE

  if (visitstrcmp(aname, lname, "Materials", 9) == 0) then

    if (visitmatdataalloc(h) == VISIT_OKAY) then
      if (visitmatdataappendcells(h, ncell01) == VISIT_OKAY) then
        cellid = 0

        ! Add material for each boundary
        boundary_loop : do ib = 1,nbound
          write(bndry,"(i0)") ib

          bc_family = trim(bc(ib)%bc_family)

          if ( bc_family == '' ) then
            bcname = 'Boundary_' // trim(adjustl(bndry)) // ''
          else
            bcname = 'Boundary_' // trim(adjustl(bndry)) // '_' //             &
                     trim(adjustl(bc_family)) // ''
          end if

          err = visitmatdataaddmat(h, bcname, len_trim(bcname), matno)

          triangles : do iface = 1, bc(ib)%nbfacet
            err = visitmatdataaddcleancell(h, cellid, matno)
            cellid = cellid + 1
          enddo triangles

          quads : do iface = 1, bc(ib)%nbfaceq
            err = visitmatdataaddcleancell(h, cellid, matno)
            cellid = cellid + 1
          enddo quads
        end do boundary_loop

        if (visitmatdataaddmat(h, "Volume", 6, matno) == VISIT_OKAY) then
          do ielem = 1, nelem
            cell_loop : do icell = 1, elem(ielem)%ncell
              err = visitmatdataaddcleancell(h, cellid, matno)
              cellid = cellid + 1
            enddo cell_loop
          enddo
        end if
      end if
    end if

  end if

  visitgetmaterial = h
end function visitgetmaterial

#endif

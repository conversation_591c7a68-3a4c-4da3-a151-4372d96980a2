!================================= EDGE_AUGMENT_WEIGHT =======================80
!
! Egde-augmentation vector for constructing edge augmentation as follows:
! grad   = grad_avg + ( edge-grad - grad_avg dot edge   ) edge_augment_weight
! vector   vector       scalar      vector       vector   vector
!
! 'alpha_scheme' is a scheme derived in Nishikawa AIAA-2010-5093.
! It is different from the face-tangent in that the edge-augument weight is
! positive even for negative e_dot_n and that it has an adjustable damping
! coefficient, 'alpha'. Alpha = 4/3 gives a 4th-order accurate scheme for the
! diffusion equation on orthogonal grids. It is not 4th-order accurate for
! other equations, but expected to give a better damping effect.
!
! Default value of alpha is 4/3. It can be specified by the namelist variable,
! 'alpha' in the namelist 'multigrid'.
!
!=============================================================================80

  pure function edge_augment_weight( option, ex, ey, ez, nx, ny, nz )

    use kinddefs  ,     only : dp
    use debug_defs,     only : alpha

    integer,  intent(in) :: option
    real(dp), intent(in) :: ex, ey, ez, nx, ny, nz

    real(dp), dimension(3) :: edge_augment_weight

    real(dp) :: e_dot_n

    integer , parameter :: edge_normal = 0, face_tangent = 1, alpha_scheme = 2

  continue

    edge_augment_weight(:) = 0._dp

    if ( option == edge_normal ) then

      edge_augment_weight(1) = ex
      edge_augment_weight(2) = ey
      edge_augment_weight(3) = ez

    elseif ( option == face_tangent ) then

      e_dot_n = ex*nx + ey*ny + ez*nz

      edge_augment_weight(1) = nx
      edge_augment_weight(2) = ny
      edge_augment_weight(3) = nz

      if ( e_dot_n < 1.0e-10_dp ) then
        edge_augment_weight(:) = 0._dp
      else
        edge_augment_weight(:) = edge_augment_weight(:)/e_dot_n
      endif

    elseif ( option == alpha_scheme ) then

      e_dot_n = max( abs(ex*nx + ey*ny + ez*nz), 1.0e-10_dp)

      edge_augment_weight(1) = nx
      edge_augment_weight(2) = ny
      edge_augment_weight(3) = nz

      edge_augment_weight(:) = alpha * edge_augment_weight(:)/e_dot_n

    endif

  end function edge_augment_weight

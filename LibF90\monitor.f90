module monitor

  use kinddefs, only : dp, jp, odp, dqp, system_i1

  implicit none

  private

  public :: monitor_linear_residual, a_times_x, form_linear_residual
  public :: compute_direction

contains

!============================= MONITOR_RMS ===================================80
!
! Print norm of linear system residual.
!
!=============================================================================80

  subroutine monitor_rms(n_eqns, nb, rsum, region_name)

    use lmpi, only : lmpi_reduce, lmpi_bcast, lmpi_master

    integer, intent(in) :: n_eqns, nb

    real(dqp), dimension(nb), intent(in) :: rsum

    character(len=*), intent(in) :: region_name

    integer :: eq, total_n_eqns

    real(dqp), dimension(nb) :: reduced_rsum

  continue

    call lmpi_reduce(rsum,reduced_rsum)
    call lmpi_bcast(reduced_rsum)

    call lmpi_reduce(n_eqns,total_n_eqns)
    call lmpi_bcast(total_n_eqns)

    reduced_rsum(:) = sqrt(reduced_rsum(:)/real(total_n_eqns,dqp))

    if(.not.lmpi_master) return

    if(nb <= 5) then
      write(*,'(1x,a10,1x,5(e17.10,2x))')                                      &
            region_name,(reduced_rsum(eq),eq=1,nb)
    else
      write(*,'(1x,a10,1x,5(e17.10,2x),/,17x,5(e17.10,2x))')                   &
            region_name,(reduced_rsum(eq),eq=1,nb)
    endif

  end subroutine monitor_rms

!================================= COMPUTE_DIRECTION =========================80
!
! Compute start, end, stride for colored_sweeps/color_indices.
!
!=============================================================================80

  subroutine compute_direction(colored_sweeps, color_indices, order_backwards, &
                               start_array, end_array, stride_array)

    integer, intent(in) :: colored_sweeps

    integer, dimension(2,colored_sweeps), intent(in)  :: color_indices

    integer, dimension(colored_sweeps), intent(out) :: start_array
    integer, dimension(colored_sweeps), intent(out) :: end_array
    integer, dimension(colored_sweeps), intent(out) :: stride_array

    logical, intent(in) :: order_backwards

    integer :: colored_sweep

  continue

    do colored_sweep = 1, colored_sweeps

      direction : if( .not. order_backwards ) then

        start_array(colored_sweep)  = color_indices(1,colored_sweep)
        end_array(colored_sweep)    = color_indices(2,colored_sweep)
        stride_array(colored_sweep) = 1

      else direction

! Maintain null set in reverse

        null_set : if ( color_indices(1,colored_sweep) >                       &
                        color_indices(2,colored_sweep) ) then

          start_array(colored_sweep)  = color_indices(1,colored_sweep)
          end_array(colored_sweep)    = color_indices(2,colored_sweep)
          stride_array(colored_sweep) = 1

        else null_set

          start_array(colored_sweep)  = color_indices(2,colored_sweep)
          end_array(colored_sweep)    = color_indices(1,colored_sweep)
          stride_array(colored_sweep) = -1

        endif null_set

      endif direction

    enddo

  end subroutine compute_direction

!============================= MONITOR_LINEAR_RESIDUAL =======================80
!
! Get norm of linear system residual for on-processor equations (neq0)
! that includes off-processor equations (neqmax) with non-zero entries
! accounting through ja and a_off with nja entries
!
!=============================================================================80

  subroutine monitor_linear_residual(nb, nm, dq_dim, nr,                       &
                                     neq0, nia, nja, iam, jam,                 &
                                     a_diag_lu, pivot_lu, a_off, res,          &
                                     res_matvec, dq, a_diag, n_eqns,           &
                                     change_sign_res, neqmax,                  &
                                     diag_has_been_decomposed, diagonals,      &
                                     region_name, g2m, eqns)

    use info_depr, only : twod

    integer, intent(in) :: neq0, neqmax, nia, nja
    integer, intent(in) :: nb, nm, dq_dim, nr, n_eqns, diagonals

    integer, dimension(n_eqns), intent(in), optional :: eqns
    integer, dimension(nia),    intent(in)           :: iam
    integer, dimension(nja),    intent(in)           :: jam
    integer, dimension(:),      intent(in)           :: g2m

    logical, intent(in) :: change_sign_res

    real(dp), dimension(nr,neq0),    intent(in) :: res
    real(dp), dimension(nm,nm,neq0), intent(in) :: a_diag
    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    integer(system_i1), dimension(nm,neq0), intent(in)  :: pivot_lu

    real(odp), dimension(nm,nm,nja),    intent(in)  :: a_off
    real(dqp), dimension(dq_dim,neqmax),    intent(in)  :: dq
    real(jp), dimension(nm,neq0),       intent(out) :: res_matvec

    logical, dimension(neq0), intent(in) :: diag_has_been_decomposed

    character(len=*), intent(in) :: region_name

    integer :: i, n, nn, nn_eqns

    integer, dimension(neq0) :: node_list

    real(dqp), dimension(nb) :: rsum

  continue

    nn_eqns = n_eqns

! Get the linear residual

    if ( present(eqns) ) then    ! subdomain

      call form_linear_residual(neq0, nia, nja, iam, jam,                      &
                                a_diag_lu, pivot_lu, a_off, res,               &
                                res_matvec, dq, a_diag, nn_eqns, eqns,         &
                                dq_dim,nr,nm,change_sign_res, neqmax,          &
                                nb,diag_has_been_decomposed, diagonals,        &
                                off_diagonals=.true., rhs=.true., g2m=g2m)

    else                         ! every node on processor

      nn_eqns = neq0
      if ( twod ) nn_eqns = neq0/2
      do i = 1, nn_eqns
        node_list(i) = i
      end do

      call form_linear_residual(neq0, nia, nja, iam, jam,                      &
                                a_diag_lu, pivot_lu, a_off, res,               &
                                res_matvec, dq, a_diag, nn_eqns, node_list,    &
                                dq_dim,nr,nm,change_sign_res, neqmax,          &
                                nb,diag_has_been_decomposed, diagonals,        &
                                off_diagonals=.true., rhs=.true., g2m=g2m)

    endif

! Form the norm and print stuff

    rsum(:) = 0.0_dqp

!   Compute L2-norm of linearized equations

    do n = 1, nn_eqns
      if ( present(eqns) ) then
        i = eqns(n)
      else
        i = node_list(n)
      endif
      do nn = 1,nb
        rsum(nn) = rsum(nn) + res_matvec(nn,i)*res_matvec(nn,i)
      end do
    end do

    call monitor_rms(nn_eqns, nb, rsum, region_name)

  end subroutine monitor_linear_residual


!============================= FORM_LINEAR_RESIDUAL ==========================80
!
! Get linear system residual for on-processor equations (neq0)
!
!=============================================================================80
  subroutine form_linear_residual(neq0, nia, nja, iam, jam,                    &
                                   a_diag_lu, pivot_lu, a_off,res,             &
                                   res_matvec,dq,a_diag,n_eqns,eqns,           &
                                   dq_dim,nr,nm,change_sign_res,neqmax,        &
                                   nb,diag_has_been_decomposed, diagonals,     &
                                   off_diagonals, rhs, g2m)

    integer, intent(in) :: neq0, neqmax, nia, nja
    integer, intent(in) :: dq_dim, nr, nm, nb, n_eqns

    integer, dimension(n_eqns), intent(in) :: eqns
    integer, dimension(nia),    intent(in) :: iam
    integer, dimension(nja),    intent(in) :: jam
    integer, dimension(:),      intent(in) :: g2m

    logical, intent(in) :: change_sign_res

    real(dp), dimension(nr,neq0),    intent(in) :: res
    real(dp), dimension(nm,nm,neq0), intent(in) :: a_diag
    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    integer(system_i1), dimension(nm,neq0), intent(in)  :: pivot_lu

    real(odp), dimension(nm,nm,nja),    intent(in)  :: a_off
    real(dqp), dimension(dq_dim,neqmax),    intent(in)  :: dq
    real(jp), dimension(nm,neq0),       intent(out) :: res_matvec

    integer, intent(in) :: diagonals
    logical, intent(in) :: off_diagonals, rhs

    logical, dimension(neq0), intent(in) :: diag_has_been_decomposed

    integer :: i,j,n

    real(jp) :: change_sign

  continue

   change_sign = -1.0_jp
   if(change_sign_res) change_sign = +1.0_jp

! Form b-Ax
! First pick up the Ax piece

    call a_times_x(nb, dq_dim, nm, neqmax, neq0, nia, nja, iam, jam,           &
                   a_diag, a_diag_lu, pivot_lu, a_off, res_matvec, dq,         &
                   diag_has_been_decomposed, diagonals, off_diagonals, g2m,    &
                   n_eqns, eqns)

! Flip the sign on Ax

    res_matvec(1:nb,:) = -res_matvec(1:nb,:)

! Optionally add nonlinear residual

    if ( rhs ) then

      do n = 1, n_eqns
        i = eqns(n)
        do j = 1, nb
          res_matvec(j,i) = res_matvec(j,i) + change_sign * res(j,i)
        end do
      end do

    endif

  end subroutine form_linear_residual


!============================= A_TIMES_X =====================================80
!
! Compute A times x
!
!=============================================================================80
  subroutine a_times_x(nb, dq_dim, nm, neqmax, neq0, nia, nja, iam, jam,       &
                        a_diag, a_diag_lu, pivot_lu, a_off, res_matvec, dq,    &
                        diag_has_been_decomposed, diagonals, off_diagonals,    &
                        g2m, n_eqns, eqns)

    use info_depr, only : partial_pivoting, twod

    integer,           intent(in) :: neq0, neqmax, nia, nja, dq_dim
    integer,           intent(in) :: nm, nb
    integer, optional, intent(in) :: n_eqns

    integer, dimension(:),          optional, intent(in) :: eqns
    integer, dimension(nia+1),                intent(in) :: iam
    integer, dimension(nja),                  intent(in) :: jam
    integer, dimension(:),                    intent(in) :: g2m

    real(dp), dimension(nm,nm,neq0), intent(in) :: a_diag
    real(jp), dimension(nm,nm,neq0), intent(in) :: a_diag_lu

    integer(system_i1), dimension(nm, neq0), intent(in) :: pivot_lu

    real(odp), dimension(nm,nm,nja),    intent(in)  :: a_off
    real(jp), dimension(nm,neq0),       intent(out) :: res_matvec
    real(dqp), dimension(dq_dim,neqmax),    intent(in)  :: dq

    integer, intent(in) :: diagonals
    logical, intent(in) :: off_diagonals

    logical, dimension(neq0), intent(in) :: diag_has_been_decomposed

    integer :: i,j,n,nn,mm,jstart,jend,jcol, row, col

    real(jp), dimension(nb) :: t

  continue

! Figure out whether to do whole domain or just a subset
! n_eqns and eqns must both be present to do a subset

! Now do A*x over region of interest

    entire_grid : if ( .not. present(n_eqns) .or. .not. present(eqns) ) then

      do nn = 1, neq0
        if ( twod .and. ( nn > neq0/2) ) cycle !skip off-plane 2D
        do mm = 1, nb
          res_matvec(mm,nn) = 0.0_jp
        end do
      end do

      eqn_loop1 : do n = 1, neq0

        if ( twod .and. (n > neq0/2) ) cycle !skip off-plane 2D

        diag_contribs1 : if ( diagonals > 0 ) then

          reform_A1 : if ( diag_has_been_decomposed(n) ) then

! Forward U*delta_q

            if(partial_pivoting) then
              do row=1,nb
                t(row) = dq( pivot_lu(row,n) , n )
              enddo
            else
              do row=1,nb
                t(row) = dq(row,n)
              enddo
            endif

            do row=1,nb
              t(row) = t(row)/a_diag_lu(row,row,n)
              do col=row+1,nb
                t(row) = t(row) + a_diag_lu(row,col,n)*t(col)
              enddo
            enddo

! Backward L*( U*dq )

            do row=nb,2,-1
              do col=1,row-1
                t(row) = t(row) + a_diag_lu(row,col,n)*t(col)
              enddo
            enddo

            res_matvec(1:nb,n) = t(1:nb)

          else reform_A1

             row = g2m(n)
             do nn = 1, nb
              do mm = 1, nb
                res_matvec(nn,n) = res_matvec(nn,n) + a_diag(nn,mm,row)*dq(mm,n)
              end do
             end do

          endif reform_A1

        endif diag_contribs1

        offdiag_contribs1 : if ( off_diagonals ) then

! Off-diagonal contributions

          jstart = iam(n)
          jend   = iam(n+1)-1

          do j = jstart,jend
            jcol = jam(j)
            do nn = 1, nb
              do mm = 1, nb
                res_matvec(nn,n) = res_matvec(nn,n) + a_off(nn,mm,j)*dq(mm,jcol)
              end do
            end do
          end do

        endif offdiag_contribs1

      end do eqn_loop1

    else entire_grid

      do nn = 1, n_eqns
        i = eqns(nn)
        do mm = 1, nb
          res_matvec(mm,i) = 0.0_jp
        end do
      end do

      eqn_loop2 : do n = 1, n_eqns

        i = eqns(n)

        diag_contribs2 : if ( diagonals > 0 ) then

          reform_A2 : if ( diag_has_been_decomposed(i) ) then

! Forward U*delta_q

            if(partial_pivoting) then
              do row=1,nb
                t(row) = dq( pivot_lu(row,i) , i )
              enddo
            else
              do row=1,nb
                t(row) = dq(row,i)
              enddo
            endif

            do row=1,nb
              t(row) = dq(row,i)/a_diag_lu(row,row,i)
              do col=row+1,nb
                t(row) = t(row) + a_diag_lu(row,col,i)*dq(col,i)
              enddo
            enddo

! Backward L*( U*dq )

            do row=nb,2,-1
              do col=1,row-1
                t(row) = t(row) + a_diag_lu(row,col,i)*t(col)
              enddo
            enddo

            res_matvec(1:nb,i) = t(1:nb)

          else reform_A2

            row = g2m(i)
            do nn = 1, nb
              do mm = 1, nb
                res_matvec(nn,i) = res_matvec(nn,i) + a_diag(nn,mm,row)*dq(mm,i)
              end do
            end do

          endif reform_A2

        endif diag_contribs2

        offdiag_contribs2 : if ( off_diagonals ) then

! Off-diagonal contributions

          jstart = iam(i)
          jend   = iam(i+1)-1

          do j = jstart,jend
            jcol = jam(j)
            do nn = 1, nb
              do mm = 1, nb
                res_matvec(nn,i) = res_matvec(nn,i) + a_off(nn,mm,j)*dq(mm,jcol)
              end do
            end do
          end do

        endif offdiag_contribs2

      end do eqn_loop2

    endif entire_grid

  end subroutine a_times_x


end module monitor

module residual_bc_visc

  implicit none

  private

  public :: dvisrhs_bc_mix5
  public :: dvisrhs_bc_mix6

contains

!=============================== DVISRHS_BC_MIX6 =============================80
!
! This routine computes the viscous flux Jacobians on the boundaries
! for the general (mixed) element case (corresponding to subroutine
! bc_visc_flux_mix_cell) and adds them to the left-hand-side
!
! Note: qnode is assumed to contain conserved variables
!
!=============================================================================80
  subroutine dvisrhs_bc_mix6(nnodes0, nnodes01, nnz, nbound, bc, ia, ja, iau,  &
                             nelem, elem, x, y, z, qnode, amut, n_tot, adim,   &
                             nfunctions, coltag, rlam, n_turb, turb, symmetry, &
                             res, aa)

    use kinddefs,         only : dp
    use info_depr,        only : tref, re, xmach, twod, ivisc
    use fluid,            only : gamma, gm1, ggm1, sutherland_constant, prandtl
    use bc_names,         only : bc_ignore_2d,                                 &
                                 bc_has_visc_flux_closure, twall,              &
                                 bc_strong_viscous_adjoint,                    &
                                 symmetry_x, symmetry_y, symmetry_z
    use bc_types,         only : bcgrid_type
    use element_types,    only : elem_type
    use element_defs,     only : max_face_per_cell, max_node_per_cell
    use grid_metrics,     only : dual_area_quad
    use twod_util,        only : yplane_2d, y_coplanar_tol
    use utilities,        only : cell_jacobians, cell_gradients
    use turb_parameters,  only : turbulent_prandtl
    use turb_sa_const,    only : cv1
    use lmpi,             only : lmpi_die
    use generic_gas_map,  only : n_momx, n_momy, n_momz
    use flux_symmetry,    only : has_x_symmetry, has_y_symmetry, has_z_symmetry
    use adjoint_switches, only : use_bp_model
    use nml_noninertial_reference_frame, only : noninertial
    use grid_motion_helpers,             only : need_grid_velocity

    integer,                                        intent(in)    :: n_tot
    integer,                                        intent(in)    :: adim
    integer,                                        intent(in)    :: nnodes0
    integer,                                        intent(in)    :: nnodes01
    integer,                                        intent(in)    :: nelem
    integer,                                        intent(in)    :: nfunctions
    integer,                                        intent(in)    :: nnz
    integer,                                        intent(in)    :: nbound
    integer,                                        intent(in)    :: n_turb
    integer,  dimension(:),                         intent(in)    :: ia, ja, iau
    integer,  dimension(:),                         intent(in)    :: symmetry
    real(dp), dimension(nnodes01),                  intent(in)    :: amut
    real(dp), dimension(nnodes01),                  intent(in)    :: x,y,z
    real(dp), dimension(n_tot, nnodes01),           intent(in)    :: qnode
    real(dp), dimension(adim,nnodes01),             intent(in)    :: coltag
    real(dp), dimension(n_turb,nnodes01),           intent(in)    :: turb
    real(dp), dimension(adim,nnodes01, nfunctions), intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01, nfunctions),                            &
                                        optional,   intent(inout) :: res
    real(dp), dimension(adim,adim,nnz), optional,   intent(inout) :: aa
    type(elem_type),   dimension(nelem),            intent(in)    :: elem
    type(bcgrid_type), dimension(nbound),           intent(in)    :: bc

    integer, dimension(max_node_per_cell) :: c2n_cell
    integer, dimension(max_node_per_cell) :: node_map

    integer :: i, icell, ielem, nf, node, eqn
    integer :: in1, i_local, idiag, ioff
    integer :: bnn1, bnn2, bnn3, bnn4
    integer :: bnode1, bnode2, bnode3, bnode4
    integer :: bnode, bnn, nodec, ifcn, inode
    integer :: nodes_local, edges_local, ib
    integer :: k, column, face_2d

    real(dp) :: tx, ty, tz, rmucgp
    real(dp) :: c23, c43, cell_vol, fact
    real(dp) :: cgp, cstar, rmu, cgpt
    real(dp) :: umu, ux, uy, uz
    real(dp) :: vmu, vx, vy, vz
    real(dp) :: wmu, wx, wy, wz
    real(dp) :: txx, txy, txz, tyy, tyz, tzz
    real(dp) :: vf2, vf3, vf4
    real(dp) :: x1, x2, x3, xc
    real(dp) :: y1, y2, y3, yc
    real(dp) :: z1, z2, z3, zc
    real(dp) :: xmr, areax, areay, areaz
    real(dp) :: dtxxdr, dtxxdm, dtxxdn, dtxxdl, dtxxde, dtxxdt
    real(dp) :: dtxydr, dtxydm, dtxydn, dtxydl, dtxyde, dtxydt
    real(dp) :: dtxzdr, dtxzdm, dtxzdn, dtxzdl, dtxzde, dtxzdt
    real(dp) :: dtyydr, dtyydm, dtyydn, dtyydl, dtyyde, dtyydt
    real(dp) :: dtyzdr, dtyzdm, dtyzdn, dtyzdl, dtyzde, dtyzdt
    real(dp) :: dtzzdr, dtzzdm, dtzzdn, dtzzdl, dtzzde, dtzzdt
    real(dp) :: dtqxdr, dtqxdm, dtqxdn, dtqxdl, dtqxde, dtqxdt
    real(dp) :: dtqydr, dtqydm, dtqydn, dtqydl, dtqyde, dtqydt
    real(dp) :: dtqzdr, dtqzdm, dtqzdn, dtqzdl, dtqzde, dtqzdt
    real(dp) :: rax, ray, raz
    real(dp) :: usum, vsum, wsum
    real(dp) :: dpiecedr, dpiecedm, dpiecedn, dpiecedl, dpiecede, dpiecedt
    real(dp) :: dnudr,dnudm,dnudn,dnudl,dnude
    real(dp) :: chi,dchidr,dchidm,dchidn,dchidl,dchide,dchidt
    real(dp) :: psi,dpsidr,dpsidm,dpsidn,dpsidl,dpside,dpsidt
    real(dp) :: base,dbasedr,dbasedm,dbasedn,dbasedl,dbasede,dbasedt
    real(dp) :: fv1,fv1dr,fv1dm,fv1dn,fv1dl,fv1de,fv1dt
    real(dp) :: uwall, vwall, wwall
    real(dp) :: res2q1,res2q2,res2q3,res2q4,res2q5
    real(dp) :: res3q1,res3q2,res3q3,res3q4,res3q5
    real(dp) :: res4q1,res4q2,res4q3,res4q4,res4q5
    real(dp) :: res5q1,res5q2,res5q3,res5q4,res5q5

    real(dp), dimension(4)                 :: xnorm_q, ynorm_q, znorm_q

    real(dp), dimension(max_face_per_cell)    :: nx, ny, nz

    real(dp), dimension(max_node_per_cell)      :: rho_node, e_node
    real(dp), dimension(max_node_per_cell)      :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)      :: t_node, p_node, mu_node
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)      :: nu_node, turb_node
    real(dp), dimension(4,max_node_per_cell)    :: q_node
    real(dp), dimension(max_node_per_cell)    :: dmudr,dmudm,dmudn,dmudl,dmude
    real(dp), dimension(max_node_per_cell)    :: dudr, dudm, dudn, dudl, dude
    real(dp), dimension(max_node_per_cell)    :: dvdr, dvdm, dvdn, dvdl, dvde
    real(dp), dimension(max_node_per_cell)    :: dwdr, dwdm, dwdn, dwdl, dwde
    real(dp), dimension(max_node_per_cell)    :: dtdr, dtdm, dtdn, dtdl, dtde
    real(dp), dimension(max_node_per_cell)    :: dmutdr, dmutdm, dmutdn, dmutdl
    real(dp), dimension(max_node_per_cell)    :: dmutde, dmutdt

    real(dp), dimension(max_node_per_cell)    :: drmudr,dumudr,dvmudr,dwmudr
    real(dp), dimension(max_node_per_cell)    :: drmudm,dumudm,dvmudm,dwmudm
    real(dp), dimension(max_node_per_cell)    :: drmudn,dumudn,dvmudn,dwmudn
    real(dp), dimension(max_node_per_cell)    :: drmudl,dumudl,dvmudl,dwmudl
    real(dp), dimension(max_node_per_cell)    :: drmude,dumude,dvmude,dwmude
    real(dp), dimension(max_node_per_cell)    :: drmudt,dumudt,dvmudt,dwmudt

    real(dp), dimension(max_node_per_cell)      :: duxdr, duxdm
    real(dp), dimension(max_node_per_cell)      :: duydr, duydm
    real(dp), dimension(max_node_per_cell)      :: duzdr, duzdm
    real(dp), dimension(max_node_per_cell)      :: dvxdr, dvxdn
    real(dp), dimension(max_node_per_cell)      :: dvydr, dvydn
    real(dp), dimension(max_node_per_cell)      :: dvzdr, dvzdn
    real(dp), dimension(max_node_per_cell)      :: dwxdr, dwxdl
    real(dp), dimension(max_node_per_cell)      :: dwydr, dwydl
    real(dp), dimension(max_node_per_cell)      :: dwzdr, dwzdl
    real(dp), dimension(max_node_per_cell)      :: dtxdr, dtxdm, dtxdn
    real(dp), dimension(max_node_per_cell)      :: dtxdl, dtxde
    real(dp), dimension(max_node_per_cell)      :: dtydr, dtydm, dtydn
    real(dp), dimension(max_node_per_cell)      :: dtydl, dtyde
    real(dp), dimension(max_node_per_cell)      :: dtzdr, dtzdm, dtzdn
    real(dp), dimension(max_node_per_cell)      :: dtzdl, dtzde

    real(dp), dimension(max_node_per_cell)      :: dvf2dr, dvf2dm
    real(dp), dimension(max_node_per_cell)      :: dvf2dn, dvf2dl, dvf2de,dvf2dt
    real(dp), dimension(max_node_per_cell)      :: dvf3dr, dvf3dm
    real(dp), dimension(max_node_per_cell)      :: dvf3dn, dvf3dl, dvf3de,dvf3dt
    real(dp), dimension(max_node_per_cell)      :: dvf4dr, dvf4dm
    real(dp), dimension(max_node_per_cell)      :: dvf4dn, dvf4dl, dvf4de,dvf4dt
    real(dp), dimension(max_node_per_cell)      :: dvf5dr, dvf5dm
    real(dp), dimension(max_node_per_cell)      :: dvf5dn, dvf5dl, dvf5de,dvf5dt

    real(dp), dimension(max_node_per_cell)      :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell)      :: dgradz_celldq
    real(dp), dimension(4)                      :: gradx_cell, grady_cell
    real(dp), dimension(4)                      :: gradz_cell

    real(dp), dimension(5,6)    :: a

    real(dp),  parameter                   :: my_0   = 0.0_dp
    real(dp),  parameter                   :: my_1   = 1.0_dp
    real(dp),  parameter                   :: my_1p5 = 1.5_dp
    real(dp),  parameter                   :: my_2   = 2.0_dp
    real(dp),  parameter                   :: my_3   = 3.0_dp
    real(dp),  parameter                   :: my_4   = 4.0_dp
    real(dp),  parameter                   :: my_haf = 0.5_dp
    real(dp),  parameter                   :: my_half= 0.5_dp
    real(dp),  parameter                   :: my_6th = 1.0_dp/6.0_dp

    logical, dimension(nnodes0) :: node_hit_as_viscous

    logical :: form_matvec = .false.
    logical :: form_matrix = .false.

  continue

    if ( present(res) ) form_matvec = .true.
    if ( present(aa) )  form_matrix = .true.

    if ( (.not.form_matvec) .and. (.not.form_matrix)) then
      write(*,*) 'WARNING: dvisrhs_bc_mix6 called but did not request output...'
      return
    endif

    cstar = sutherland_constant / tref
    xmr   = xmach / re
    c43   = my_4 / my_3
    c23   = my_2 / my_3
    cgp   = my_1 / (gm1*prandtl)
    cgpt  = my_1 / (gm1*turbulent_prandtl)

! For bctype viscous_solid:
! note that the viscous no-slip strong boundary residuals will be set to zero
! after the all viscous fluxes have been closed off on the boundaries

    boundary_loop : do ib = 1, nbound

      if ( twod .and.  bc_ignore_2d(bc(ib)%ibc) ) cycle boundary_loop

    close_off_viscous: if ( bc_has_visc_flux_closure(bc(ib)%ibc) ) then

! First loop over all the tria boundary faces

      loop_tria_faces: do nf = 1, bc(ib)%nbfacet

!       global node numbers of the cell/face nodes on the boundary

        bnode1 = bc(ib)%ibnode(bc(ib)%f2ntb(nf,1))
        bnode2 = bc(ib)%ibnode(bc(ib)%f2ntb(nf,2))
        bnode3 = bc(ib)%ibnode(bc(ib)%f2ntb(nf,3))

        icell = bc(ib)%f2ntb(nf,4)         ! global cell number
        ielem = bc(ib)%f2ntb(nf,5)         ! cell type indicator

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       determine the local node number in the cell attached to the current
!       boundary face that corresponds to each of the local boundary nodes

        bnn1 = 0
        bnn2 = 0
        bnn3 = 0
        do node=1,elem(ielem)%node_per_cell

          if (bnode1 == c2n_cell(node)) bnn1 = node
          if (bnode2 == c2n_cell(node)) bnn2 = node
          if (bnode3 == c2n_cell(node)) bnn3 = node

        end do

! Compute cell averages, cell center, and set up some local solution arrays

        rmu = 0.0_dp
        umu = 0.0_dp
        vmu = 0.0_dp
        wmu = 0.0_dp
        rmucgp = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        t_node(:)   = 0.0_dp
        e_node(:)   = 0.0_dp
        p_node(:)   = 0.0_dp
        mu_node(:)  = 0.0_dp
        nu_node(:)  = 0.0_dp
        turb_node(:)= 0.0_dp
        q_node(:,:) = 0.0_dp

        duxdr(:) = 0._dp
        duxdm(:) = 0._dp
        duydr(:) = 0._dp
        duydm(:) = 0._dp
        duzdr(:) = 0._dp
        duzdm(:) = 0._dp

        dvxdr(:) = 0._dp
        dvxdn(:) = 0._dp
        dvydr(:) = 0._dp
        dvydn(:) = 0._dp
        dvzdr(:) = 0._dp
        dvzdn(:) = 0._dp

        dwxdr(:) = 0._dp
        dwxdl(:) = 0._dp
        dwydr(:) = 0._dp
        dwydl(:) = 0._dp
        dwzdr(:) = 0._dp
        dwzdl(:) = 0._dp

        dtxdr(:) = 0._dp
        dtxdm(:) = 0._dp
        dtxdn(:) = 0._dp
        dtxdl(:) = 0._dp
        dtxde(:) = 0._dp
        dtydr(:) = 0._dp
        dtydm(:) = 0._dp
        dtydn(:) = 0._dp
        dtydl(:) = 0._dp
        dtyde(:) = 0._dp
        dtzdr(:) = 0._dp
        dtzdm(:) = 0._dp
        dtzdn(:) = 0._dp
        dtzdl(:) = 0._dp
        dtzde(:) = 0._dp

! Zero out some derivatives of viscosity stuff; we may
! or may not fill these in with values later

        drmudr = 0.0_dp
        drmudm = 0.0_dp
        drmudn = 0.0_dp
        drmudl = 0.0_dp
        drmude = 0.0_dp
        drmudt = 0.0_dp

        dumudr = 0.0_dp
        dumudm = 0.0_dp
        dumudn = 0.0_dp
        dumudl = 0.0_dp
        dumude = 0.0_dp
        dumudt = 0.0_dp

        dvmudr = 0.0_dp
        dvmudm = 0.0_dp
        dvmudn = 0.0_dp
        dvmudl = 0.0_dp
        dvmude = 0.0_dp
        dvmudt = 0.0_dp

        dwmudr = 0.0_dp
        dwmudm = 0.0_dp
        dwmudn = 0.0_dp
        dwmudl = 0.0_dp
        dwmude = 0.0_dp
        dwmudt = 0.0_dp

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          rho_node(i) = qnode(1,node)

          u_node(i) = qnode(2,node)/qnode(1,node)
          v_node(i) = qnode(3,node)/qnode(1,node)
          w_node(i) = qnode(4,node)/qnode(1,node)

          e_node(i) = qnode(5,node)
          p_node(i) = gm1*(e_node(i) - my_haf*rho_node(i)*(u_node(i)*u_node(i) &
                    +                                      v_node(i)*v_node(i) &
                    +                                      w_node(i)*w_node(i)))
          t_node(i)  = gamma*p_node(i)/rho_node(i)
          if ( ivisc == 6 ) turb_node(i) = turb(1,node)

          mu_node(i) = (my_1 + cstar)/(t_node(i) + cstar)*t_node(i)**my_1p5
          nu_node(i) = mu_node(i)/rho_node(i)
          rmucgp     = rmucgp + mu_node(i)*cgp
          mu_node(i) = mu_node(i) + amut(node)
          rmucgp     = rmucgp + amut(node)*cgpt

          rmu = rmu + mu_node(i)
          umu = umu + u_node(i) * mu_node(i)
          vmu = vmu + v_node(i) * mu_node(i)
          wmu = wmu + w_node(i) * mu_node(i)

        end do node_loop1

! save off some sums we will need later for derivatives

      usum = umu
      vsum = vmu
      wsum = wmu

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu = rmu*fact
        umu = umu*fact/rmu
        vmu = vmu*fact/rmu
        wmu = wmu*fact/rmu
        rmucgp = rmucgp*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        xc  = 0.0_dp
        yc  = 0.0_dp
        zc  = 0.0_dp

        do i = 1, elem(ielem)%node_per_cell

!         global node number

          node = c2n_cell(i)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(elem(ielem)%node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

! Get derivatives of (primitive) variables wrt conserved variables
! for each node in the cell

!       nomenclature:
!         dudr = d(u)/d(rho)
!         dudm = d(u)/d(rho*u)
!         dvdr = d(v)/d(rho)
!         dvdn = d(v)/d(rho*v)
!         dwdr = d(w)/d(rho)
!         dwdl = d(w)/d(rho*w)
!         dadr = d(a)/d(rho)
!         dadm = d(a)/d(rho*u)
!         dadn = d(a)/d(rho*v)
!         dadl = d(a)/d(rho*w)
!         dade = d(a)/d(e)

        node_loop2 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          dudr(i) = - u_node(i) / rho_node(i)
          dudm(i) =        my_1 / rho_node(i)
          dudn(i) = 0.0_dp
          dudl(i) = 0.0_dp
          dude(i) = 0.0_dp

          dvdr(i) = - v_node(i) / rho_node(i)
          dvdm(i) = 0.0_dp
          dvdn(i) =        my_1 / rho_node(i)
          dvdl(i) = 0.0_dp
          dvde(i) = 0.0_dp

          dwdr(i) = - w_node(i) / rho_node(i)
          dwdm(i) = 0.0_dp
          dwdn(i) = 0.0_dp
          dwdl(i) =        my_1 / rho_node(i)
          dwde(i) = 0.0_dp

          dtdr(i) =   ggm1 * ( u_node(i)*u_node(i) +                           &
                               v_node(i)*v_node(i) +                           &
                               w_node(i)*w_node(i) -                           &
                               e_node(i)/rho_node(i) ) / rho_node(i)
          dtdm(i) = - ggm1 * u_node(i) / rho_node(i)
          dtdn(i) = - ggm1 * v_node(i) / rho_node(i)
          dtdl(i) = - ggm1 * w_node(i) / rho_node(i)
          dtde(i) =   ggm1 / rho_node(i)

! Get derivatives related to mu now too
! We'll need some derivatives of temperature wrt conserved variables first

          dmudr(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdr(i)
          dmudm(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdm(i)
          dmudn(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdn(i)
          dmudl(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdl(i)
          dmude(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtde(i)

          select case ( ivisc )
          case (2,8)
            dmutdr(i) = 0.0_dp
            dmutdm(i) = 0.0_dp
            dmutdn(i) = 0.0_dp
            dmutdl(i) = 0.0_dp
            dmutde(i) = 0.0_dp
            dmutdt(i) = 0.0_dp
          case (6)

            bp_model1 : if ( use_bp_model ) then

              dnudr = dmudr(i)/rho_node(i) - nu_node(i)/rho_node(i)
              dnudm = dmudm(i)/rho_node(i)
              dnudn = dmudn(i)/rho_node(i)
              dnudl = dmudl(i)/rho_node(i)
              dnude = dmude(i)/rho_node(i)

              chi = turb_node(i) / nu_node(i)
                dchidr = -turb_node(i)*dnudr/nu_node(i)/nu_node(i)
                dchidm = -turb_node(i)*dnudm/nu_node(i)/nu_node(i)
                dchidn = -turb_node(i)*dnudn/nu_node(i)/nu_node(i)
                dchidl = -turb_node(i)*dnudl/nu_node(i)/nu_node(i)
                dchide = -turb_node(i)*dnude/nu_node(i)/nu_node(i)
                dchidt = 1.0_dp / nu_node(i)

              psi = chi
                dpsidr = dchidr
                dpsidm = dchidm
                dpsidn = dchidn
                dpsidl = dchidl
                dpside = dchide
                dpsidt = dchidt

              if ( chi < 10._dp ) then
                base = 1.0_dp + exp(20.0_dp*chi)
                  dbasedr = exp(20.0_dp*chi)*20.0_dp*dchidr
                  dbasedm = exp(20.0_dp*chi)*20.0_dp*dchidm
                  dbasedn = exp(20.0_dp*chi)*20.0_dp*dchidn
                  dbasedl = exp(20.0_dp*chi)*20.0_dp*dchidl
                  dbasede = exp(20.0_dp*chi)*20.0_dp*dchide
                  dbasedt = exp(20.0_dp*chi)*20.0_dp*dchidt
                psi = 0.05_dp * log(base)
                  dpsidr = 0.05_dp/base*dbasedr
                  dpsidm = 0.05_dp/base*dbasedm
                  dpsidn = 0.05_dp/base*dbasedn
                  dpsidl = 0.05_dp/base*dbasedl
                  dpside = 0.05_dp/base*dbasede
                  dpsidt = 0.05_dp/base*dbasedt
              endif

              fv1 = psi**3 / (psi**3 + cv1**3)
                fv1dr = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidr               &
                         - psi**3*3.0_dp*psi**2*dpsidr ) / (psi**3 + cv1**3)**2
                fv1dm = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidm               &
                         - psi**3*3.0_dp*psi**2*dpsidm ) / (psi**3 + cv1**3)**2
                fv1dn = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidn               &
                         - psi**3*3.0_dp*psi**2*dpsidn ) / (psi**3 + cv1**3)**2
                fv1dl = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidl               &
                         - psi**3*3.0_dp*psi**2*dpsidl ) / (psi**3 + cv1**3)**2
                fv1de = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpside               &
                         - psi**3*3.0_dp*psi**2*dpside ) / (psi**3 + cv1**3)**2
                fv1dt = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidt               &
                         - psi**3*3.0_dp*psi**2*dpsidt ) / (psi**3 + cv1**3)**2

!             mut = max( 0._dp , rho_node(i)*turb_node(i)*fv1 )

              if ( rho_node(i)*turb_node(i)*fv1 > 0.0_dp ) then
                dmutdr(i) = turb_node(i)*(rho_node(i)*fv1dr + fv1)
                dmutdm(i) = turb_node(i)*rho_node(i)*fv1dm
                dmutdn(i) = turb_node(i)*rho_node(i)*fv1dn
                dmutdl(i) = turb_node(i)*rho_node(i)*fv1dl
                dmutde(i) = turb_node(i)*rho_node(i)*fv1de
                dmutdt(i) = rho_node(i)*(turb_node(i)*fv1dt + fv1)
              else
                dmutdr(i) = 0.0_dp
                dmutdm(i) = 0.0_dp
                dmutdn(i) = 0.0_dp
                dmutdl(i) = 0.0_dp
                dmutde(i) = 0.0_dp
                dmutdt(i) = 0.0_dp
              endif

            else bp_model1

              dnudr = dmudr(i)/rho_node(i) - nu_node(i)/rho_node(i)
              dnudm = dmudm(i)/rho_node(i)
              dnudn = dmudn(i)/rho_node(i)
              dnudl = dmudl(i)/rho_node(i)
              dnude = dmude(i)/rho_node(i)

              chi = turb_node(i) / nu_node(i)
              dchidr = -turb_node(i)*dnudr/nu_node(i)/nu_node(i)
              dchidm = -turb_node(i)*dnudm/nu_node(i)/nu_node(i)
              dchidn = -turb_node(i)*dnudn/nu_node(i)/nu_node(i)
              dchidl = -turb_node(i)*dnudl/nu_node(i)/nu_node(i)
              dchide = -turb_node(i)*dnude/nu_node(i)/nu_node(i)
              dchidt = 1.0_dp / nu_node(i)

              fv1 = chi**3/(chi**3 + cv1**3)

              fv1dr = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidr                  &
              - chi**3*3.0_dp*chi**2*dchidr)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dm = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidm                  &
              - chi**3*3.0_dp*chi**2*dchidm)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dn = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidn                  &
              - chi**3*3.0_dp*chi**2*dchidn)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dl = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidl                  &
              - chi**3*3.0_dp*chi**2*dchidl)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1de = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchide                  &
              - chi**3*3.0_dp*chi**2*dchide)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dt = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidt                  &
              - chi**3*3.0_dp*chi**2*dchidt)/(chi**3 + cv1**3)/(chi**3 + cv1**3)

!             mut = rho_node(i)*turb_node(i)*fv1

              dmutdr(i) = turb_node(i)*(rho_node(i)*fv1dr + fv1)
              dmutdm(i) = turb_node(i)*rho_node(i)*fv1dm
              dmutdn(i) = turb_node(i)*rho_node(i)*fv1dn
              dmutdl(i) = turb_node(i)*rho_node(i)*fv1dl
              dmutde(i) = turb_node(i)*rho_node(i)*fv1de
              dmutdt(i) = rho_node(i)*(turb_node(i)*fv1dt + fv1)

            endif bp_model1

          case default
            write(*,*) 'ivisc case not supported in dvisrhs_bc_mix6: ', ivisc
            call lmpi_die
            stop
          end select

          drmudr(i) = dmudr(i) + dmutdr(i)
          drmudm(i) = dmudm(i) + dmutdm(i)
          drmudn(i) = dmudn(i) + dmutdn(i)
          drmudl(i) = dmudl(i) + dmutdl(i)
          drmude(i) = dmude(i) + dmutde(i)
          drmudt(i) =            dmutdt(i)

        end do node_loop2

! Divide the viscosity sum derivatives by the averaging factor

        fact = 1._dp / real(nodes_local, dp)

        drmudr = drmudr*fact
        drmudm = drmudm*fact
        drmudn = drmudn*fact
        drmudl = drmudl*fact
        drmude = drmude*fact
        drmudt = drmudt*fact

! derivatives of umu, vmu, wmu

      node_loop_3 : do i_local = 1, nodes_local

        i = node_map(i_local)

        dumudr(i) = fact/rmu*(u_node(i)*drmudr(i)/fact + mu_node(i)*dudr(i))   &
                  - fact/rmu/rmu*usum*drmudr(i)
        dumudm(i) = fact/rmu*(u_node(i)*drmudm(i)/fact + mu_node(i)*dudm(i))   &
                  - fact/rmu/rmu*usum*drmudm(i)
        dumudn(i) = fact/rmu*(u_node(i)*drmudn(i)/fact + mu_node(i)*dudn(i))   &
                  - fact/rmu/rmu*usum*drmudn(i)
        dumudl(i) = fact/rmu*(u_node(i)*drmudl(i)/fact + mu_node(i)*dudl(i))   &
                  - fact/rmu/rmu*usum*drmudl(i)
        dumude(i) = fact/rmu*(u_node(i)*drmude(i)/fact + mu_node(i)*dude(i))   &
                  - fact/rmu/rmu*usum*drmude(i)
        dumudt(i) = fact/rmu*(u_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*usum*drmudt(i)

        dvmudr(i) = fact/rmu*(v_node(i)*drmudr(i)/fact + mu_node(i)*dvdr(i))   &
                  - fact/rmu/rmu*vsum*drmudr(i)
        dvmudm(i) = fact/rmu*(v_node(i)*drmudm(i)/fact + mu_node(i)*dvdm(i))   &
                  - fact/rmu/rmu*vsum*drmudm(i)
        dvmudn(i) = fact/rmu*(v_node(i)*drmudn(i)/fact + mu_node(i)*dvdn(i))   &
                  - fact/rmu/rmu*vsum*drmudn(i)
        dvmudl(i) = fact/rmu*(v_node(i)*drmudl(i)/fact + mu_node(i)*dvdl(i))   &
                  - fact/rmu/rmu*vsum*drmudl(i)
        dvmude(i) = fact/rmu*(v_node(i)*drmude(i)/fact + mu_node(i)*dvde(i))   &
                  - fact/rmu/rmu*vsum*drmude(i)
        dvmudt(i) = fact/rmu*(v_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*vsum*drmudt(i)

        dwmudr(i) = fact/rmu*(w_node(i)*drmudr(i)/fact + mu_node(i)*dwdr(i))   &
                  - fact/rmu/rmu*wsum*drmudr(i)
        dwmudm(i) = fact/rmu*(w_node(i)*drmudm(i)/fact + mu_node(i)*dwdm(i))   &
                  - fact/rmu/rmu*wsum*drmudm(i)
        dwmudn(i) = fact/rmu*(w_node(i)*drmudn(i)/fact + mu_node(i)*dwdn(i))   &
                  - fact/rmu/rmu*wsum*drmudn(i)
        dwmudl(i) = fact/rmu*(w_node(i)*drmudl(i)/fact + mu_node(i)*dwdl(i))   &
                  - fact/rmu/rmu*wsum*drmudl(i)
        dwmude(i) = fact/rmu*(w_node(i)*drmude(i)/fact + mu_node(i)*dwde(i))   &
                  - fact/rmu/rmu*wsum*drmude(i)
        dwmudt(i) = fact/rmu*(w_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*wsum*drmudt(i)

      end do node_loop_3

! Get the gradients in the primal cell via Green-Gauss

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)
        q_node(4,:) = t_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            4, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)
        tx = gradx_cell(4)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)
        ty = grady_cell(4)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)
        tz = gradz_cell(4)

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note: these are with respect to the primitive variables

        call cell_jacobians(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            elem(ielem)%local_f2n, elem(ielem)%e2n_2d,         &
                            dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                            cell_vol, nx, ny, nz)

! Convert to jacobians with respect to conservative variables

        if (twod) then

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdr(i) = dudr(i)*dgradx_celldq(i)
            duxdm(i) = dudm(i)*dgradx_celldq(i)
            dwxdr(i) = dwdr(i)*dgradx_celldq(i)
            dwxdl(i) = dwdl(i)*dgradx_celldq(i)
            dtxdr(i) = dtdr(i)*dgradx_celldq(i)
            dtxdm(i) = dtdm(i)*dgradx_celldq(i)
            dtxdl(i) = dtdl(i)*dgradx_celldq(i)
            dtxde(i) = dtde(i)*dgradx_celldq(i)

            duzdr(i) = dudr(i)*dgradz_celldq(i)
            duzdm(i) = dudm(i)*dgradz_celldq(i)
            dwzdr(i) = dwdr(i)*dgradz_celldq(i)
            dwzdl(i) = dwdl(i)*dgradz_celldq(i)
            dtzdr(i) = dtdr(i)*dgradz_celldq(i)
            dtzdm(i) = dtdm(i)*dgradz_celldq(i)
            dtzdl(i) = dtdl(i)*dgradz_celldq(i)
            dtzde(i) = dtde(i)*dgradz_celldq(i)

          end do

        else

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdr(i) = dudr(i)*dgradx_celldq(i)
            duxdm(i) = dudm(i)*dgradx_celldq(i)
            dvxdr(i) = dvdr(i)*dgradx_celldq(i)
            dvxdn(i) = dvdn(i)*dgradx_celldq(i)
            dwxdr(i) = dwdr(i)*dgradx_celldq(i)
            dwxdl(i) = dwdl(i)*dgradx_celldq(i)

            dtxdr(i) = dtdr(i)*dgradx_celldq(i)
            dtxdm(i) = dtdm(i)*dgradx_celldq(i)
            dtxdn(i) = dtdn(i)*dgradx_celldq(i)
            dtxdl(i) = dtdl(i)*dgradx_celldq(i)
            dtxde(i) = dtde(i)*dgradx_celldq(i)

            duydr(i) = dudr(i)*dgrady_celldq(i)
            duydm(i) = dudm(i)*dgrady_celldq(i)
            dvydr(i) = dvdr(i)*dgrady_celldq(i)
            dvydn(i) = dvdn(i)*dgrady_celldq(i)
            dwydr(i) = dwdr(i)*dgrady_celldq(i)
            dwydl(i) = dwdl(i)*dgrady_celldq(i)

            dtydr(i) = dtdr(i)*dgrady_celldq(i)
            dtydm(i) = dtdm(i)*dgrady_celldq(i)
            dtydn(i) = dtdn(i)*dgrady_celldq(i)
            dtydl(i) = dtdl(i)*dgrady_celldq(i)
            dtyde(i) = dtde(i)*dgrady_celldq(i)

            duzdr(i) = dudr(i)*dgradz_celldq(i)
            duzdm(i) = dudm(i)*dgradz_celldq(i)
            dvzdr(i) = dvdr(i)*dgradz_celldq(i)
            dvzdn(i) = dvdn(i)*dgradz_celldq(i)
            dwzdr(i) = dwdr(i)*dgradz_celldq(i)
            dwzdl(i) = dwdl(i)*dgradz_celldq(i)

            dtzdr(i) = dtdr(i)*dgradz_celldq(i)
            dtzdm(i) = dtdm(i)*dgradz_celldq(i)
            dtzdn(i) = dtdn(i)*dgradz_celldq(i)
            dtzdl(i) = dtdl(i)*dgradz_celldq(i)
            dtzde(i) = dtde(i)*dgradz_celldq(i)

          end do

        end if

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a triangle, this is just 1/3 the face normal, and is
! the same at each node

        x1 = x(bnode1)
        y1 = y(bnode1)
        z1 = z(bnode1)

        x2 = x(bnode2)
        y2 = y(bnode2)
        z2 = z(bnode2)

        x3 = x(bnode3)
        y3 = y(bnode3)
        z3 = z(bnode3)

!       - sign on normals because Green-Gauss needs outward pointing normals

        areax = -my_6th*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        areay = -my_6th*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        areaz = -my_6th*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

        txx = rmu*(c43*ux - c23*vy - c23*wz)
        txy = rmu*(uy + vx)
        txz = rmu*(uz + wx)

        tyy = rmu*(c43*vy - c23*ux - c23*wz)
        tyz = rmu*(vz + wy)

        tzz = rmu*(c43*wz - c23*ux - c23*vy)

!       [nondimensionalization factor xmr ] * [ viscosity ]
!       [ unit normal and area ] at dual face

        rax = xmr*areax
        ray = xmr*areay
        raz = xmr*areaz

        vf2 = rax*txx + ray*txy + raz*txz
        vf3 = rax*txy + ray*tyy + raz*tyz
        vf4 = rax*txz + ray*tyz + raz*tzz

!       form some more intermediate Jacobians at all nodes

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

! Stress pieces

          dtxxdr = rmu*(  c43*duxdr(i) - c23*dvydr(i) - c23*dwzdr(i))
          dtxxdm = rmu*(  c43*duxdm(i))
          dtxxdn = rmu*(- c23*dvydn(i))
          dtxxdl = rmu*(- c23*dwzdl(i))

          dtxydr = rmu*(duydr(i) + dvxdr(i))
          dtxydm = rmu*(duydm(i))
          dtxydn = rmu*(dvxdn(i))

          dtxzdr = rmu*(duzdr(i) + dwxdr(i))
          dtxzdm = rmu*(duzdm(i))
          dtxzdl = rmu*(dwxdl(i))

          dtyydr = rmu*(  c43*dvydr(i) - c23*duxdr(i) - c23*dwzdr(i))
          dtyydm = rmu*(- c23*duxdm(i))
          dtyydn = rmu*(  c43*dvydn(i))
          dtyydl = rmu*(- c23*dwzdl(i))

          dtyzdr = rmu*(dvzdr(i) + dwydr(i))
          dtyzdn = rmu*(dvzdn(i))
          dtyzdl = rmu*(dwydl(i))

          dtzzdr = rmu*(  c43*dwzdr(i) - c23*duxdr(i) - c23*dvydr(i))
          dtzzdm = rmu*(- c23*duxdm(i))
          dtzzdn = rmu*(- c23*dvydn(i))
          dtzzdl = rmu*(  c43*dwzdl(i))

! Add stress contributions

          dvf2dr(i) = rax*dtxxdr + ray*dtxydr + raz*dtxzdr
          dvf2dm(i) = rax*dtxxdm + ray*dtxydm + raz*dtxzdm
          dvf2dn(i) = rax*dtxxdn + ray*dtxydn
          dvf2dl(i) = rax*dtxxdl              + raz*dtxzdl
          dvf2de(i) = 0.0_dp
          dvf2dt(i) = 0.0_dp

          dvf3dr(i) = rax*dtxydr + ray*dtyydr + raz*dtyzdr
          dvf3dm(i) = rax*dtxydm + ray*dtyydm
          dvf3dn(i) = rax*dtxydn + ray*dtyydn + raz*dtyzdn
          dvf3dl(i) =              ray*dtyydl + raz*dtyzdl
          dvf3de(i) = 0.0_dp
          dvf3dt(i) = 0.0_dp

          dvf4dr(i) = rax*dtxzdr + ray*dtyzdr + raz*dtzzdr
          dvf4dm(i) = rax*dtxzdm              + raz*dtzzdm
          dvf4dn(i) =              ray*dtyzdn + raz*dtzzdn
          dvf4dl(i) = rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
          dvf4de(i) = 0.0_dp
          dvf4dt(i) = 0.0_dp

! Viscosity pieces

          dtxxdr = drmudr(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdm = drmudm(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdn = drmudn(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdl = drmudl(i)*(c43*ux - c23*vy - c23*wz)
          dtxxde = drmude(i)*(c43*ux - c23*vy - c23*wz)
          dtxxdt = drmudt(i)*(c43*ux - c23*vy - c23*wz)

          dtxydr = drmudr(i)*(uy + vx)
          dtxydm = drmudm(i)*(uy + vx)
          dtxydn = drmudn(i)*(uy + vx)
          dtxydl = drmudl(i)*(uy + vx)
          dtxyde = drmude(i)*(uy + vx)
          dtxydt = drmudt(i)*(uy + vx)

          dtxzdr = drmudr(i)*(uz + wx)
          dtxzdm = drmudm(i)*(uz + wx)
          dtxzdn = drmudn(i)*(uz + wx)
          dtxzdl = drmudl(i)*(uz + wx)
          dtxzde = drmude(i)*(uz + wx)
          dtxzdt = drmudt(i)*(uz + wx)

          dtyydr = drmudr(i)*(c43*vy - c23*ux - c23*wz)
          dtyydm = drmudm(i)*(c43*vy - c23*ux - c23*wz)
          dtyydn = drmudn(i)*(c43*vy - c23*ux - c23*wz)
          dtyydl = drmudl(i)*(c43*vy - c23*ux - c23*wz)
          dtyyde = drmude(i)*(c43*vy - c23*ux - c23*wz)
          dtyydt = drmudt(i)*(c43*vy - c23*ux - c23*wz)

          dtyzdr = drmudr(i)*(vz + wy)
          dtyzdm = drmudm(i)*(vz + wy)
          dtyzdn = drmudn(i)*(vz + wy)
          dtyzdl = drmudl(i)*(vz + wy)
          dtyzde = drmude(i)*(vz + wy)
          dtyzdt = drmudt(i)*(vz + wy)

          dtzzdr = drmudr(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdm = drmudm(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdn = drmudn(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdl = drmudl(i)*(c43*wz - c23*ux - c23*vy)
          dtzzde = drmude(i)*(c43*wz - c23*ux - c23*vy)
          dtzzdt = drmudt(i)*(c43*wz - c23*ux - c23*vy)

! Add viscosity contributions

          dvf2dr(i) = dvf2dr(i) + rax*dtxxdr + ray*dtxydr + raz*dtxzdr
          dvf2dm(i) = dvf2dm(i) + rax*dtxxdm + ray*dtxydm + raz*dtxzdm
          dvf2dn(i) = dvf2dn(i) + rax*dtxxdn + ray*dtxydn + raz*dtxzdn
          dvf2dl(i) = dvf2dl(i) + rax*dtxxdl + ray*dtxydl + raz*dtxzdl
          dvf2de(i) = dvf2de(i) + rax*dtxxde + ray*dtxyde + raz*dtxzde
          dvf2dt(i) = dvf2dt(i) + rax*dtxxdt + ray*dtxydt + raz*dtxzdt

          dvf3dr(i) = dvf3dr(i) + rax*dtxydr + ray*dtyydr + raz*dtyzdr
          dvf3dm(i) = dvf3dm(i) + rax*dtxydm + ray*dtyydm + raz*dtyzdm
          dvf3dn(i) = dvf3dn(i) + rax*dtxydn + ray*dtyydn + raz*dtyzdn
          dvf3dl(i) = dvf3dl(i) + rax*dtxydl + ray*dtyydl + raz*dtyzdl
          dvf3de(i) = dvf3de(i) + rax*dtxyde + ray*dtyyde + raz*dtyzde
          dvf3dt(i) = dvf3dt(i) + rax*dtxydt + ray*dtyydt + raz*dtyzdt

          dvf4dr(i) = dvf4dr(i) + rax*dtxzdr + ray*dtyzdr + raz*dtzzdr
          dvf4dm(i) = dvf4dm(i) + rax*dtxzdm + ray*dtyzdm + raz*dtzzdm
          dvf4dn(i) = dvf4dn(i) + rax*dtxzdn + ray*dtyzdn + raz*dtzzdn
          dvf4dl(i) = dvf4dl(i) + rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
          dvf4de(i) = dvf4de(i) + rax*dtxzde + ray*dtyzde + raz*dtzzde
          dvf4dt(i) = dvf4dt(i) + rax*dtxzdt + ray*dtyzdt + raz*dtzzdt

! Now do stress and viscosity pieces for vf5 all at once

!    vf5 = rax*tqx + ray*tqy + raz*tqz + (umu*vf2 + vmu*vf3 + wmu*vf4)

!         tqx = rmucgp*tx
            dtqxdr = rmucgp*dtxdr(i) + tx*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqxdm = rmucgp*dtxdm(i) + tx*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqxdn = rmucgp*dtxdn(i) + tx*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqxdl = rmucgp*dtxdl(i) + tx*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqxde = rmucgp*dtxde(i) + tx*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqxdt =                   tx*fact*(               cgpt*dmutdt(i))

!         tqy = rmucgp*ty
            dtqydr = rmucgp*dtydr(i) + ty*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqydm = rmucgp*dtydm(i) + ty*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqydn = rmucgp*dtydn(i) + ty*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqydl = rmucgp*dtydl(i) + ty*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqyde = rmucgp*dtyde(i) + ty*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqydt =                   ty*fact*(               cgpt*dmutdt(i))

!         tqz = rmucgp*tz
            dtqzdr = rmucgp*dtzdr(i) + tz*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqzdm = rmucgp*dtzdm(i) + tz*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqzdn = rmucgp*dtzdn(i) + tz*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqzdl = rmucgp*dtzdl(i) + tz*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqzde = rmucgp*dtzde(i) + tz*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqzdt =                   tz*fact*(               cgpt*dmutdt(i))

!         piece = umu*vf2 + vmu*vf3 + wmu*vf4
            dpiecedr = umu*dvf2dr(i) + vmu*dvf3dr(i) + wmu*dvf4dr(i) +         &
                       vf2*dumudr(i) + vf3*dvmudr(i) + vf4*dwmudr(i)
            dpiecedm = umu*dvf2dm(i) + vmu*dvf3dm(i) + wmu*dvf4dm(i) +         &
                       vf2*dumudm(i) + vf3*dvmudm(i) + vf4*dwmudm(i)
            dpiecedn = umu*dvf2dn(i) + vmu*dvf3dn(i) + wmu*dvf4dn(i) +         &
                       vf2*dumudn(i) + vf3*dvmudn(i) + vf4*dwmudn(i)
            dpiecedl = umu*dvf2dl(i) + vmu*dvf3dl(i) + wmu*dvf4dl(i) +         &
                       vf2*dumudl(i) + vf3*dvmudl(i) + vf4*dwmudl(i)
            dpiecede = umu*dvf2de(i) + vmu*dvf3de(i) + wmu*dvf4de(i) +         &
                       vf2*dumude(i) + vf3*dvmude(i) + vf4*dwmude(i)
            dpiecedt = umu*dvf2dt(i) + vmu*dvf3dt(i) + wmu*dvf4dt(i) +         &
                       vf2*dumudt(i) + vf3*dvmudt(i) + vf4*dwmudt(i)

          dvf5dr(i) = rax*dtqxdr + ray*dtqydr + raz*dtqzdr + dpiecedr
          dvf5dm(i) = rax*dtqxdm + ray*dtqydm + raz*dtqzdm + dpiecedm
          dvf5dn(i) = rax*dtqxdn + ray*dtqydn + raz*dtqzdn + dpiecedn
          dvf5dl(i) = rax*dtqxdl + ray*dtqydl + raz*dtqzdl + dpiecedl
          dvf5de(i) = rax*dtqxde + ray*dtqyde + raz*dtqzde + dpiecede
          dvf5dt(i) = rax*dtqxdt + ray*dtqydt + raz*dtqzdt + dpiecedt

        end do

! Finally for each node on the boundary tria, send the appropriate jacobian
! contributions to each

!       res(2,bnode) = res(2,bnode) - vf2
!       res(3,bnode) = res(3,bnode) - vf3
!       res(4,bnode) = res(4,bnode) - vf4
!       res(5,bnode) = res(5,bnode) - vf5

        node_loop3 : do in1 = 1,3

          if (in1 == 1) then
            bnode = bnode1
            bnn   = bnn1
          else if (in1 == 2) then
            bnode = bnode2
            bnn   = bnn2
          else
            bnode = bnode3
            bnn   = bnn3
          end if

          if (twod) then
            if (abs(y(bnode)-yplane_2d) > y_coplanar_tol) cycle node_loop3
          end if

! Diagonal contributions

          if ( bnode <= nnodes0 ) then

            a(2,1) =  - dvf2dr(bnn)
            a(2,2) =  - dvf2dm(bnn)
            a(2,3) =  - dvf2dn(bnn)
            a(2,4) =  - dvf2dl(bnn)
            a(2,5) =  - dvf2de(bnn)
            a(2,6) =  - dvf2dt(bnn)

            a(3,1) =  - dvf3dr(bnn)
            a(3,2) =  - dvf3dm(bnn)
            a(3,3) =  - dvf3dn(bnn)
            a(3,4) =  - dvf3dl(bnn)
            a(3,5) =  - dvf3de(bnn)
            a(3,6) =  - dvf3dt(bnn)

            a(4,1) =  - dvf4dr(bnn)
            a(4,2) =  - dvf4dm(bnn)
            a(4,3) =  - dvf4dn(bnn)
            a(4,4) =  - dvf4dl(bnn)
            a(4,5) =  - dvf4de(bnn)
            a(4,6) =  - dvf4dt(bnn)

            a(5,1) =  - dvf5dr(bnn)
            a(5,2) =  - dvf5dm(bnn)
            a(5,3) =  - dvf5dn(bnn)
            a(5,4) =  - dvf5dl(bnn)
            a(5,5) =  - dvf5de(bnn)
            a(5,6) =  - dvf5dt(bnn)

            if ( form_matrix ) then
              idiag = iau(bnode)

              aa(2,1,idiag) = aa(2,1,idiag) + a(2,1)
              aa(2,2,idiag) = aa(2,2,idiag) + a(2,2)
              aa(2,3,idiag) = aa(2,3,idiag) + a(2,3)
              aa(2,4,idiag) = aa(2,4,idiag) + a(2,4)
              aa(2,5,idiag) = aa(2,5,idiag) + a(2,5)
              if ( ivisc > 2 ) aa(2,6,idiag) = aa(2,6,idiag) + a(2,6)

              aa(3,1,idiag) = aa(3,1,idiag) + a(3,1)
              aa(3,2,idiag) = aa(3,2,idiag) + a(3,2)
              aa(3,3,idiag) = aa(3,3,idiag) + a(3,3)
              aa(3,4,idiag) = aa(3,4,idiag) + a(3,4)
              aa(3,5,idiag) = aa(3,5,idiag) + a(3,5)
              if ( ivisc > 2 ) aa(3,6,idiag) = aa(3,6,idiag) + a(3,6)

              aa(4,1,idiag) = aa(4,1,idiag) + a(4,1)
              aa(4,2,idiag) = aa(4,2,idiag) + a(4,2)
              aa(4,3,idiag) = aa(4,3,idiag) + a(4,3)
              aa(4,4,idiag) = aa(4,4,idiag) + a(4,4)
              aa(4,5,idiag) = aa(4,5,idiag) + a(4,5)
              if ( ivisc > 2 ) aa(4,6,idiag) = aa(4,6,idiag) + a(4,6)

              aa(5,1,idiag) = aa(5,1,idiag) + a(5,1)
              aa(5,2,idiag) = aa(5,2,idiag) + a(5,2)
              aa(5,3,idiag) = aa(5,3,idiag) + a(5,3)
              aa(5,4,idiag) = aa(5,4,idiag) + a(5,4)
              aa(5,5,idiag) = aa(5,5,idiag) + a(5,5)
              if ( ivisc > 2 ) aa(5,6,idiag) = aa(5,6,idiag) + a(5,6)
            endif

            if ( form_matvec ) then
              do ifcn = 1, nfunctions
                res(1,bnode,ifcn) = res(1,bnode,ifcn)                          &
                                  + a(2,1)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,1)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,1)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,1)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(2,bnode,ifcn) = res(2,bnode,ifcn)                          &
                                  + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,2)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(3,bnode,ifcn) = res(3,bnode,ifcn)                          &
                                  + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,3)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(4,bnode,ifcn) = res(4,bnode,ifcn)                          &
                                  + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,4)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(5,bnode,ifcn) = res(5,bnode,ifcn)                          &
                                  + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,5)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                if ( ivisc > 2 ) then
                  res(6,bnode,ifcn) = res(6,bnode,ifcn)                        &
                                    + a(2,6)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,6)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,6)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,6)*coltag(5,bnode)*rlam(5,bnode,ifcn)
                endif
              end do
            endif

          end if

! Off-diagonal contributions

          node_loop4 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n_cell(i)

            if (nodec == bnode) cycle node_loop4   ! already did diagonal

!           if (twod) then
!             if (abs(y(nodec)-yplane_2d) > y_coplanar_tol) cycle node_loop4
!           end if

            if ( nodec <= nnodes0 ) then

              a(2,1) =  - dvf2dr(i)
              a(2,2) =  - dvf2dm(i)
              a(2,3) =  - dvf2dn(i)
              a(2,4) =  - dvf2dl(i)
              a(2,5) =  - dvf2de(i)
              a(2,6) =  - dvf2dt(i)

              a(3,1) =  - dvf3dr(i)
              a(3,2) =  - dvf3dm(i)
              a(3,3) =  - dvf3dn(i)
              a(3,4) =  - dvf3dl(i)
              a(3,5) =  - dvf3de(i)
              a(3,6) =  - dvf3dt(i)

              a(4,1) =  - dvf4dr(i)
              a(4,2) =  - dvf4dm(i)
              a(4,3) =  - dvf4dn(i)
              a(4,4) =  - dvf4dl(i)
              a(4,5) =  - dvf4de(i)
              a(4,6) =  - dvf4dt(i)

              a(5,1) =  - dvf5dr(i)
              a(5,2) =  - dvf5dm(i)
              a(5,3) =  - dvf5dn(i)
              a(5,4) =  - dvf5dl(i)
              a(5,5) =  - dvf5de(i)
              a(5,6) =  - dvf5dt(i)

              if ( form_matrix ) then

! Determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == bnode) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                           bnode,' to the off diagonal of tria bnode ',nodec
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                aa(2,1,ioff) = aa(2,1,ioff) + a(2,1)
                aa(2,2,ioff) = aa(2,2,ioff) + a(2,2)
                aa(2,3,ioff) = aa(2,3,ioff) + a(2,3)
                aa(2,4,ioff) = aa(2,4,ioff) + a(2,4)
                aa(2,5,ioff) = aa(2,5,ioff) + a(2,5)
                if ( ivisc > 2 ) aa(2,6,ioff) = aa(2,6,ioff) + a(2,6)

                aa(3,1,ioff) = aa(3,1,ioff) + a(3,1)
                aa(3,2,ioff) = aa(3,2,ioff) + a(3,2)
                aa(3,3,ioff) = aa(3,3,ioff) + a(3,3)
                aa(3,4,ioff) = aa(3,4,ioff) + a(3,4)
                aa(3,5,ioff) = aa(3,5,ioff) + a(3,5)
                if ( ivisc > 2 ) aa(3,6,ioff) = aa(3,6,ioff) + a(3,6)

                aa(4,1,ioff) = aa(4,1,ioff) + a(4,1)
                aa(4,2,ioff) = aa(4,2,ioff) + a(4,2)
                aa(4,3,ioff) = aa(4,3,ioff) + a(4,3)
                aa(4,4,ioff) = aa(4,4,ioff) + a(4,4)
                aa(4,5,ioff) = aa(4,5,ioff) + a(4,5)
                if ( ivisc > 2 ) aa(4,6,ioff) = aa(4,6,ioff) + a(4,6)

                aa(5,1,ioff) = aa(5,1,ioff) + a(5,1)
                aa(5,2,ioff) = aa(5,2,ioff) + a(5,2)
                aa(5,3,ioff) = aa(5,3,ioff) + a(5,3)
                aa(5,4,ioff) = aa(5,4,ioff) + a(5,4)
                aa(5,5,ioff) = aa(5,5,ioff) + a(5,5)
                if ( ivisc > 2 ) aa(5,6,ioff) = aa(5,6,ioff) + a(5,6)

              endif

              if ( form_matvec ) then
                do ifcn = 1, nfunctions
                  res(1,nodec,ifcn) = res(1,nodec,ifcn)                        &
                                    + a(2,1)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,1)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,1)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,1)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(2,nodec,ifcn) = res(2,nodec,ifcn)                        &
                                    + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,2)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(3,nodec,ifcn) = res(3,nodec,ifcn)                        &
                                    + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,3)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(4,nodec,ifcn) = res(4,nodec,ifcn)                        &
                                    + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,4)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(5,nodec,ifcn) = res(5,nodec,ifcn)                        &
                                    + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,5)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  if ( ivisc > 2 ) then
                    res(6,nodec,ifcn) = res(6,nodec,ifcn)                      &
                                    + a(2,6)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,6)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,6)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,6)*coltag(5,bnode)*rlam(5,bnode,ifcn)
                  endif
                end do
              endif

            end if

          end do node_loop4

        end do node_loop3

      end do loop_tria_faces

! Now loop over all the quad boundary faces

      loop_quad_faces: do nf = 1, bc(ib)%nbfaceq

!       global node numbers of the cell/face nodes on the boundary

        bnode1 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,1))
        bnode2 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,2))
        bnode3 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,3))
        bnode4 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,4))

        icell = bc(ib)%f2nqb(nf,5)         ! global cell number
        ielem = bc(ib)%f2nqb(nf,6)         ! cell type indicator

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       determine the local node number in the cell attached to the current
!       boundary face that corresponds to each of the local boundary nodes

        bnn1 = 0
        bnn2 = 0
        bnn3 = 0
        bnn4 = 0
        do node=1,elem(ielem)%node_per_cell

          if (bnode1 == c2n_cell(node)) bnn1 = node
          if (bnode2 == c2n_cell(node)) bnn2 = node
          if (bnode3 == c2n_cell(node)) bnn3 = node
          if (bnode4 == c2n_cell(node)) bnn4 = node

        end do

! Compute cell averages, cell center, and set up some local solution arrays

        rmu = 0.0_dp
        umu = 0.0_dp
        vmu = 0.0_dp
        wmu = 0.0_dp
        rmucgp = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        t_node(:)   = 0.0_dp
        e_node(:)   = 0.0_dp
        p_node(:)   = 0.0_dp
        mu_node(:)  = 0.0_dp
        nu_node(:)  = 0.0_dp
        turb_node(:)= 0.0_dp
        q_node(:,:) = 0.0_dp

        duxdr(:) = 0._dp
        duxdm(:) = 0._dp
        duydr(:) = 0._dp
        duydm(:) = 0._dp
        duzdr(:) = 0._dp
        duzdm(:) = 0._dp

        dvxdr(:) = 0._dp
        dvxdn(:) = 0._dp
        dvydr(:) = 0._dp
        dvydn(:) = 0._dp
        dvzdr(:) = 0._dp
        dvzdn(:) = 0._dp

        dwxdr(:) = 0._dp
        dwxdl(:) = 0._dp
        dwydr(:) = 0._dp
        dwydl(:) = 0._dp
        dwzdr(:) = 0._dp
        dwzdl(:) = 0._dp

        dtxdr(:) = 0._dp
        dtxdm(:) = 0._dp
        dtxdn(:) = 0._dp
        dtxdl(:) = 0._dp
        dtxde(:) = 0._dp
        dtydr(:) = 0._dp
        dtydm(:) = 0._dp
        dtydn(:) = 0._dp
        dtydl(:) = 0._dp
        dtyde(:) = 0._dp
        dtzdr(:) = 0._dp
        dtzdm(:) = 0._dp
        dtzdn(:) = 0._dp
        dtzdl(:) = 0._dp
        dtzde(:) = 0._dp

! Zero out some derivatives of viscosity stuff; we may
! or may not fill these in with values later

        drmudr = 0.0_dp
        drmudm = 0.0_dp
        drmudn = 0.0_dp
        drmudl = 0.0_dp
        drmude = 0.0_dp
        drmudt = 0.0_dp

        dumudr = 0.0_dp
        dumudm = 0.0_dp
        dumudn = 0.0_dp
        dumudl = 0.0_dp
        dumude = 0.0_dp
        dumudt = 0.0_dp

        dvmudr = 0.0_dp
        dvmudm = 0.0_dp
        dvmudn = 0.0_dp
        dvmudl = 0.0_dp
        dvmude = 0.0_dp
        dvmudt = 0.0_dp

        dwmudr = 0.0_dp
        dwmudm = 0.0_dp
        dwmudn = 0.0_dp
        dwmudl = 0.0_dp
        dwmude = 0.0_dp
        dwmudt = 0.0_dp

        node_loop5 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          rho_node(i) = qnode(1,node)

          u_node(i) = qnode(2,node)/qnode(1,node)
          v_node(i) = qnode(3,node)/qnode(1,node)
          w_node(i) = qnode(4,node)/qnode(1,node)

          e_node(i) = qnode(5,node)
          p_node(i) = gm1*(e_node(i) - my_haf*rho_node(i)*(u_node(i)*u_node(i) &
                    +                                      v_node(i)*v_node(i) &
                    +                                      w_node(i)*w_node(i)))

          t_node(i)  = gamma*p_node(i)/rho_node(i)
          if ( ivisc == 6 ) turb_node(i) = turb(1,node)

          mu_node(i) = (my_1 + cstar)/(t_node(i) + cstar)*t_node(i)**my_1p5
          nu_node(i) = mu_node(i)/rho_node(i)
          rmucgp     = rmucgp + mu_node(i)*cgp
          mu_node(i) = mu_node(i) + amut(node)
          rmucgp     = rmucgp + amut(node)*cgpt

          rmu = rmu + mu_node(i)
          umu = umu + u_node(i) * mu_node(i)
          vmu = vmu + v_node(i) * mu_node(i)
          wmu = wmu + w_node(i) * mu_node(i)

        end do node_loop5

! save off some sums we will need later for derivatives

      usum = umu
      vsum = vmu
      wsum = wmu

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu = rmu*fact
        umu = umu*fact/rmu
        vmu = vmu*fact/rmu
        wmu = wmu*fact/rmu
        rmucgp = rmucgp*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        xc  = 0.0_dp
        yc  = 0.0_dp
        zc  = 0.0_dp

        do i = 1, elem(ielem)%node_per_cell

!         global node number

          node = c2n_cell(i)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(elem(ielem)%node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

! Get derivatives of (primitive) variables wrt conserved variables
! for each node in the cell

!       nomenclature:
!         dudr = d(u)/d(rho)
!         dudm = d(u)/d(rho*u)
!         dvdr = d(v)/d(rho)
!         dvdn = d(v)/d(rho*v)
!         dwdr = d(w)/d(rho)
!         dwdl = d(w)/d(rho*w)
!         dadr = d(a)/d(rho)
!         dadm = d(a)/d(rho*u)
!         dadn = d(a)/d(rho*v)
!         dadl = d(a)/d(rho*w)
!         dade = d(a)/d(e)

        node_loop6 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

          dudr(i) = - u_node(i) / rho_node(i)
          dudm(i) =        my_1 / rho_node(i)
          dudn(i) = 0.0_dp
          dudl(i) = 0.0_dp
          dude(i) = 0.0_dp

          dvdr(i) = - v_node(i) / rho_node(i)
          dvdm(i) = 0.0_dp
          dvdn(i) =        my_1 / rho_node(i)
          dvdl(i) = 0.0_dp
          dvde(i) = 0.0_dp

          dwdr(i) = - w_node(i) / rho_node(i)
          dwdm(i) = 0.0_dp
          dwdn(i) = 0.0_dp
          dwdl(i) =        my_1 / rho_node(i)
          dwde(i) = 0.0_dp

          dtdr(i) =   ggm1 * ( u_node(i)*u_node(i) +                           &
                               v_node(i)*v_node(i) +                           &
                               w_node(i)*w_node(i) -                           &
                               e_node(i)/rho_node(i) ) / rho_node(i)
          dtdm(i) = - ggm1 * u_node(i) / rho_node(i)
          dtdn(i) = - ggm1 * v_node(i) / rho_node(i)
          dtdl(i) = - ggm1 * w_node(i) / rho_node(i)
          dtde(i) =   ggm1 / rho_node(i)

! Get derivatives related to mu now too
! We'll need some derivatives of temperature wrt conserved variables first

          dmudr(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdr(i)
          dmudm(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdm(i)
          dmudn(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdn(i)
          dmudl(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtdl(i)
          dmude(i) = (my_1+cstar)/(t_node(i)+cstar)*sqrt(t_node(i)) *          &
                     (my_1p5-t_node(i)/(t_node(i)+cstar)) * dtde(i)

          select case ( ivisc )
          case (2,8)
            dmutdr(i) = 0.0_dp
            dmutdm(i) = 0.0_dp
            dmutdn(i) = 0.0_dp
            dmutdl(i) = 0.0_dp
            dmutde(i) = 0.0_dp
            dmutdt(i) = 0.0_dp
          case (6)

            bp_model2 : if ( use_bp_model ) then

              dnudr = dmudr(i)/rho_node(i) - nu_node(i)/rho_node(i)
              dnudm = dmudm(i)/rho_node(i)
              dnudn = dmudn(i)/rho_node(i)
              dnudl = dmudl(i)/rho_node(i)
              dnude = dmude(i)/rho_node(i)

              chi = turb_node(i) / nu_node(i)
                dchidr = -turb_node(i)*dnudr/nu_node(i)/nu_node(i)
                dchidm = -turb_node(i)*dnudm/nu_node(i)/nu_node(i)
                dchidn = -turb_node(i)*dnudn/nu_node(i)/nu_node(i)
                dchidl = -turb_node(i)*dnudl/nu_node(i)/nu_node(i)
                dchide = -turb_node(i)*dnude/nu_node(i)/nu_node(i)
                dchidt = 1.0_dp / nu_node(i)

              psi = chi
                dpsidr = dchidr
                dpsidm = dchidm
                dpsidn = dchidn
                dpsidl = dchidl
                dpside = dchide
                dpsidt = dchidt

              if ( chi < 10._dp ) then
                base = 1.0_dp + exp(20.0_dp*chi)
                  dbasedr = exp(20.0_dp*chi)*20.0_dp*dchidr
                  dbasedm = exp(20.0_dp*chi)*20.0_dp*dchidm
                  dbasedn = exp(20.0_dp*chi)*20.0_dp*dchidn
                  dbasedl = exp(20.0_dp*chi)*20.0_dp*dchidl
                  dbasede = exp(20.0_dp*chi)*20.0_dp*dchide
                  dbasedt = exp(20.0_dp*chi)*20.0_dp*dchidt
                psi = 0.05_dp * log(base)
                  dpsidr = 0.05_dp/base*dbasedr
                  dpsidm = 0.05_dp/base*dbasedm
                  dpsidn = 0.05_dp/base*dbasedn
                  dpsidl = 0.05_dp/base*dbasedl
                  dpside = 0.05_dp/base*dbasede
                  dpsidt = 0.05_dp/base*dbasedt
              endif

              fv1 = psi**3 / (psi**3 + cv1**3)
                fv1dr = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidr               &
                         - psi**3*3.0_dp*psi**2*dpsidr ) / (psi**3 + cv1**3)**2
                fv1dm = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidm               &
                         - psi**3*3.0_dp*psi**2*dpsidm ) / (psi**3 + cv1**3)**2
                fv1dn = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidn               &
                         - psi**3*3.0_dp*psi**2*dpsidn ) / (psi**3 + cv1**3)**2
                fv1dl = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidl               &
                         - psi**3*3.0_dp*psi**2*dpsidl ) / (psi**3 + cv1**3)**2
                fv1de = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpside               &
                         - psi**3*3.0_dp*psi**2*dpside ) / (psi**3 + cv1**3)**2
                fv1dt = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidt               &
                         - psi**3*3.0_dp*psi**2*dpsidt ) / (psi**3 + cv1**3)**2

!             mut = max( 0._dp , rho_node(i)*turb_node(i)*fv1 )

              if ( rho_node(i)*turb_node(i)*fv1 > 0.0_dp ) then
                dmutdr(i) = turb_node(i)*(rho_node(i)*fv1dr + fv1)
                dmutdm(i) = turb_node(i)*rho_node(i)*fv1dm
                dmutdn(i) = turb_node(i)*rho_node(i)*fv1dn
                dmutdl(i) = turb_node(i)*rho_node(i)*fv1dl
                dmutde(i) = turb_node(i)*rho_node(i)*fv1de
                dmutdt(i) = rho_node(i)*(turb_node(i)*fv1dt + fv1)
              else
                dmutdr(i) = 0.0_dp
                dmutdm(i) = 0.0_dp
                dmutdn(i) = 0.0_dp
                dmutdl(i) = 0.0_dp
                dmutde(i) = 0.0_dp
                dmutdt(i) = 0.0_dp
              endif

            else bp_model2

              dnudr = dmudr(i)/rho_node(i) - nu_node(i)/rho_node(i)
              dnudm = dmudm(i)/rho_node(i)
              dnudn = dmudn(i)/rho_node(i)
              dnudl = dmudl(i)/rho_node(i)
              dnude = dmude(i)/rho_node(i)

              chi = turb_node(i) / nu_node(i)
              dchidr = -turb_node(i)*dnudr/nu_node(i)/nu_node(i)
              dchidm = -turb_node(i)*dnudm/nu_node(i)/nu_node(i)
              dchidn = -turb_node(i)*dnudn/nu_node(i)/nu_node(i)
              dchidl = -turb_node(i)*dnudl/nu_node(i)/nu_node(i)
              dchide = -turb_node(i)*dnude/nu_node(i)/nu_node(i)
              dchidt = 1.0_dp / nu_node(i)

              fv1 = chi**3/(chi**3 + cv1**3)

              fv1dr = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidr                  &
              - chi**3*3.0_dp*chi**2*dchidr)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dm = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidm                  &
              - chi**3*3.0_dp*chi**2*dchidm)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dn = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidn                  &
              - chi**3*3.0_dp*chi**2*dchidn)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dl = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidl                  &
              - chi**3*3.0_dp*chi**2*dchidl)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1de = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchide                  &
              - chi**3*3.0_dp*chi**2*dchide)/(chi**3 + cv1**3)/(chi**3 + cv1**3)
              fv1dt = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidt                  &
              - chi**3*3.0_dp*chi**2*dchidt)/(chi**3 + cv1**3)/(chi**3 + cv1**3)

!             mut = rho_node(i)*turb_node(i)*fv1

              dmutdr(i) = turb_node(i)*(rho_node(i)*fv1dr + fv1)
              dmutdm(i) = turb_node(i)*rho_node(i)*fv1dm
              dmutdn(i) = turb_node(i)*rho_node(i)*fv1dn
              dmutdl(i) = turb_node(i)*rho_node(i)*fv1dl
              dmutde(i) = turb_node(i)*rho_node(i)*fv1de
              dmutdt(i) = rho_node(i)*(turb_node(i)*fv1dt + fv1)

            endif bp_model2

          case default
            write(*,*) 'ivisc case not supported in dvisrhs_bc_mix6: ', ivisc
            call lmpi_die
            stop
          end select

          drmudr(i) = dmudr(i) + dmutdr(i)
          drmudm(i) = dmudm(i) + dmutdm(i)
          drmudn(i) = dmudn(i) + dmutdn(i)
          drmudl(i) = dmudl(i) + dmutdl(i)
          drmude(i) = dmude(i) + dmutde(i)
          drmudt(i) =            dmutdt(i)

        end do node_loop6

! Divide the viscosity sum derivatives by the averaging factor

        fact = 1._dp / real(nodes_local, dp)

        drmudr = drmudr*fact
        drmudm = drmudm*fact
        drmudn = drmudn*fact
        drmudl = drmudl*fact
        drmude = drmude*fact
        drmudt = drmudt*fact

! derivatives of umu, vmu, wmu

      node_loop_4 : do i_local = 1, nodes_local

        i = node_map(i_local)

        dumudr(i) = fact/rmu*(u_node(i)*drmudr(i)/fact + mu_node(i)*dudr(i))   &
                  - fact/rmu/rmu*usum*drmudr(i)
        dumudm(i) = fact/rmu*(u_node(i)*drmudm(i)/fact + mu_node(i)*dudm(i))   &
                  - fact/rmu/rmu*usum*drmudm(i)
        dumudn(i) = fact/rmu*(u_node(i)*drmudn(i)/fact + mu_node(i)*dudn(i))   &
                  - fact/rmu/rmu*usum*drmudn(i)
        dumudl(i) = fact/rmu*(u_node(i)*drmudl(i)/fact + mu_node(i)*dudl(i))   &
                  - fact/rmu/rmu*usum*drmudl(i)
        dumude(i) = fact/rmu*(u_node(i)*drmude(i)/fact + mu_node(i)*dude(i))   &
                  - fact/rmu/rmu*usum*drmude(i)
        dumudt(i) = fact/rmu*(u_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*usum*drmudt(i)

        dvmudr(i) = fact/rmu*(v_node(i)*drmudr(i)/fact + mu_node(i)*dvdr(i))   &
                  - fact/rmu/rmu*vsum*drmudr(i)
        dvmudm(i) = fact/rmu*(v_node(i)*drmudm(i)/fact + mu_node(i)*dvdm(i))   &
                  - fact/rmu/rmu*vsum*drmudm(i)
        dvmudn(i) = fact/rmu*(v_node(i)*drmudn(i)/fact + mu_node(i)*dvdn(i))   &
                  - fact/rmu/rmu*vsum*drmudn(i)
        dvmudl(i) = fact/rmu*(v_node(i)*drmudl(i)/fact + mu_node(i)*dvdl(i))   &
                  - fact/rmu/rmu*vsum*drmudl(i)
        dvmude(i) = fact/rmu*(v_node(i)*drmude(i)/fact + mu_node(i)*dvde(i))   &
                  - fact/rmu/rmu*vsum*drmude(i)
        dvmudt(i) = fact/rmu*(v_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*vsum*drmudt(i)

        dwmudr(i) = fact/rmu*(w_node(i)*drmudr(i)/fact + mu_node(i)*dwdr(i))   &
                  - fact/rmu/rmu*wsum*drmudr(i)
        dwmudm(i) = fact/rmu*(w_node(i)*drmudm(i)/fact + mu_node(i)*dwdm(i))   &
                  - fact/rmu/rmu*wsum*drmudm(i)
        dwmudn(i) = fact/rmu*(w_node(i)*drmudn(i)/fact + mu_node(i)*dwdn(i))   &
                  - fact/rmu/rmu*wsum*drmudn(i)
        dwmudl(i) = fact/rmu*(w_node(i)*drmudl(i)/fact + mu_node(i)*dwdl(i))   &
                  - fact/rmu/rmu*wsum*drmudl(i)
        dwmude(i) = fact/rmu*(w_node(i)*drmude(i)/fact + mu_node(i)*dwde(i))   &
                  - fact/rmu/rmu*wsum*drmude(i)
        dwmudt(i) = fact/rmu*(w_node(i)*drmudt(i)/fact)                        &
                  - fact/rmu/rmu*wsum*drmudt(i)

      end do node_loop_4

! Get the gradients in the primal cell via Green-Gauss

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)
        q_node(4,:) = t_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            4, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)
        tx = gradx_cell(4)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)
        ty = grady_cell(4)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)
        tz = gradz_cell(4)

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note: these are with respect to the primitive variables

        call cell_jacobians(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            elem(ielem)%local_f2n, elem(ielem)%e2n_2d,         &
                            dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                            cell_vol, nx, ny, nz)

! Convert to jacobians with respect to conservative variables

        if (twod) then

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdr(i) = dudr(i)*dgradx_celldq(i)
            duxdm(i) = dudm(i)*dgradx_celldq(i)
            dwxdr(i) = dwdr(i)*dgradx_celldq(i)
            dwxdl(i) = dwdl(i)*dgradx_celldq(i)
            dtxdr(i) = dtdr(i)*dgradx_celldq(i)
            dtxdm(i) = dtdm(i)*dgradx_celldq(i)
            dtxdl(i) = dtdl(i)*dgradx_celldq(i)
            dtxde(i) = dtde(i)*dgradx_celldq(i)

            duzdr(i) = dudr(i)*dgradz_celldq(i)
            duzdm(i) = dudm(i)*dgradz_celldq(i)
            dwzdr(i) = dwdr(i)*dgradz_celldq(i)
            dwzdl(i) = dwdl(i)*dgradz_celldq(i)
            dtzdr(i) = dtdr(i)*dgradz_celldq(i)
            dtzdm(i) = dtdm(i)*dgradz_celldq(i)
            dtzdl(i) = dtdl(i)*dgradz_celldq(i)
            dtzde(i) = dtde(i)*dgradz_celldq(i)

          end do

        else

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdr(i) = dudr(i)*dgradx_celldq(i)
            duxdm(i) = dudm(i)*dgradx_celldq(i)
            dvxdr(i) = dvdr(i)*dgradx_celldq(i)
            dvxdn(i) = dvdn(i)*dgradx_celldq(i)
            dwxdr(i) = dwdr(i)*dgradx_celldq(i)
            dwxdl(i) = dwdl(i)*dgradx_celldq(i)

            dtxdr(i) = dtdr(i)*dgradx_celldq(i)
            dtxdm(i) = dtdm(i)*dgradx_celldq(i)
            dtxdn(i) = dtdn(i)*dgradx_celldq(i)
            dtxdl(i) = dtdl(i)*dgradx_celldq(i)
            dtxde(i) = dtde(i)*dgradx_celldq(i)

            duydr(i) = dudr(i)*dgrady_celldq(i)
            duydm(i) = dudm(i)*dgrady_celldq(i)
            dvydr(i) = dvdr(i)*dgrady_celldq(i)
            dvydn(i) = dvdn(i)*dgrady_celldq(i)
            dwydr(i) = dwdr(i)*dgrady_celldq(i)
            dwydl(i) = dwdl(i)*dgrady_celldq(i)

            dtydr(i) = dtdr(i)*dgrady_celldq(i)
            dtydm(i) = dtdm(i)*dgrady_celldq(i)
            dtydn(i) = dtdn(i)*dgrady_celldq(i)
            dtydl(i) = dtdl(i)*dgrady_celldq(i)
            dtyde(i) = dtde(i)*dgrady_celldq(i)

            duzdr(i) = dudr(i)*dgradz_celldq(i)
            duzdm(i) = dudm(i)*dgradz_celldq(i)
            dvzdr(i) = dvdr(i)*dgradz_celldq(i)
            dvzdn(i) = dvdn(i)*dgradz_celldq(i)
            dwzdr(i) = dwdr(i)*dgradz_celldq(i)
            dwzdl(i) = dwdl(i)*dgradz_celldq(i)

            dtzdr(i) = dtdr(i)*dgradz_celldq(i)
            dtzdm(i) = dtdm(i)*dgradz_celldq(i)
            dtzdn(i) = dtdn(i)*dgradz_celldq(i)
            dtzdl(i) = dtdl(i)*dgradz_celldq(i)
            dtzde(i) = dtde(i)*dgradz_celldq(i)

          end do

        end if

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a quad, the dual normal is in general different at
! at each node on the face

        call dual_area_quad(nnodes01,x,y,z,bnode1,bnode2,bnode3,bnode4,        &
                            noninertial,xnorm_q,ynorm_q,znorm_q)

! Finally for each node on the boundary quad, send the appropriate jacobian
! contributions to each

        node_loop7 : do in1 = 1,4

          if (in1 == 1) then
            bnode = bnode1
            bnn   = bnn1
          else if (in1 == 2) then
            bnode = bnode2
            bnn   = bnn2
          else if (in1 == 3) then
            bnode = bnode3
            bnn   = bnn3
          else
            bnode = bnode4
            bnn   = bnn4
          end if

          if (twod) then
            if (abs(y(bnode)-yplane_2d) > y_coplanar_tol) cycle node_loop7
          end if

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!           [nondimensionalization factor xmr ] * [ viscosity ]
!           [ unit normal and area ] at dual face

            areax = xnorm_q(in1)
            areay = ynorm_q(in1)
            areaz = znorm_q(in1)

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!       components of symmetric stress tensor

            txx = rmu*(c43*ux - c23*vy - c23*wz)
            txy = rmu*(uy + vx)
            txz = rmu*(uz + wx)

            tyy = rmu*(c43*vy - c23*ux - c23*wz)
            tyz = rmu*(vz + wy)

            tzz = rmu*(c43*wz - c23*ux - c23*vy)

            rax = xmr*areax
            ray = xmr*areay
            raz = xmr*areaz

            vf2 = rax*txx + ray*txy + raz*txz
            vf3 = rax*txy + ray*tyy + raz*tyz
            vf4 = rax*txz + ray*tyz + raz*tzz

!           form some intermediate Jacobians at all nodes

            do i_local = 1, nodes_local

!             local node number

              i = node_map(i_local)

! Stress pieces

              dtxxdr = rmu*(  c43*duxdr(i) - c23*dvydr(i) - c23*dwzdr(i))
              dtxxdm = rmu*(  c43*duxdm(i))
              dtxxdn = rmu*(- c23*dvydn(i))
              dtxxdl = rmu*(- c23*dwzdl(i))

              dtxydr = rmu*(duydr(i) + dvxdr(i))
              dtxydm = rmu*(duydm(i))
              dtxydn = rmu*(dvxdn(i))

              dtxzdr = rmu*(duzdr(i) + dwxdr(i))
              dtxzdm = rmu*(duzdm(i))
              dtxzdl = rmu*(dwxdl(i))

              dtyydr = rmu*(  c43*dvydr(i) - c23*duxdr(i) - c23*dwzdr(i))
              dtyydm = rmu*(- c23*duxdm(i))
              dtyydn = rmu*(  c43*dvydn(i))
              dtyydl = rmu*(- c23*dwzdl(i))

              dtyzdr = rmu*(dvzdr(i) + dwydr(i))
              dtyzdn = rmu*(dvzdn(i))
              dtyzdl = rmu*(dwydl(i))

              dtzzdr = rmu*(  c43*dwzdr(i) - c23*duxdr(i) - c23*dvydr(i))
              dtzzdm = rmu*(- c23*duxdm(i))
              dtzzdn = rmu*(- c23*dvydn(i))
              dtzzdl = rmu*(  c43*dwzdl(i))

! Add stress contributions

              dvf2dr(i) = rax*dtxxdr + ray*dtxydr + raz*dtxzdr
              dvf2dm(i) = rax*dtxxdm + ray*dtxydm + raz*dtxzdm
              dvf2dn(i) = rax*dtxxdn + ray*dtxydn
              dvf2dl(i) = rax*dtxxdl              + raz*dtxzdl
              dvf2de(i) = 0.0_dp
              dvf2dt(i) = 0.0_dp

              dvf3dr(i) = rax*dtxydr + ray*dtyydr + raz*dtyzdr
              dvf3dm(i) = rax*dtxydm + ray*dtyydm
              dvf3dn(i) = rax*dtxydn + ray*dtyydn + raz*dtyzdn
              dvf3dl(i) =              ray*dtyydl + raz*dtyzdl
              dvf3de(i) = 0.0_dp
              dvf3dt(i) = 0.0_dp

              dvf4dr(i) = rax*dtxzdr + ray*dtyzdr + raz*dtzzdr
              dvf4dm(i) = rax*dtxzdm              + raz*dtzzdm
              dvf4dn(i) =              ray*dtyzdn + raz*dtzzdn
              dvf4dl(i) = rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
              dvf4de(i) = 0.0_dp
              dvf4dt(i) = 0.0_dp

! Viscosity pieces

              dtxxdr = drmudr(i)*(c43*ux - c23*vy - c23*wz)
              dtxxdm = drmudm(i)*(c43*ux - c23*vy - c23*wz)
              dtxxdn = drmudn(i)*(c43*ux - c23*vy - c23*wz)
              dtxxdl = drmudl(i)*(c43*ux - c23*vy - c23*wz)
              dtxxde = drmude(i)*(c43*ux - c23*vy - c23*wz)
              dtxxdt = drmudt(i)*(c43*ux - c23*vy - c23*wz)

              dtxydr = drmudr(i)*(uy + vx)
              dtxydm = drmudm(i)*(uy + vx)
              dtxydn = drmudn(i)*(uy + vx)
              dtxydl = drmudl(i)*(uy + vx)
              dtxyde = drmude(i)*(uy + vx)
              dtxydt = drmudt(i)*(uy + vx)

              dtxzdr = drmudr(i)*(uz + wx)
              dtxzdm = drmudm(i)*(uz + wx)
              dtxzdn = drmudn(i)*(uz + wx)
              dtxzdl = drmudl(i)*(uz + wx)
              dtxzde = drmude(i)*(uz + wx)
              dtxzdt = drmudt(i)*(uz + wx)

              dtyydr = drmudr(i)*(c43*vy - c23*ux - c23*wz)
              dtyydm = drmudm(i)*(c43*vy - c23*ux - c23*wz)
              dtyydn = drmudn(i)*(c43*vy - c23*ux - c23*wz)
              dtyydl = drmudl(i)*(c43*vy - c23*ux - c23*wz)
              dtyyde = drmude(i)*(c43*vy - c23*ux - c23*wz)
              dtyydt = drmudt(i)*(c43*vy - c23*ux - c23*wz)

              dtyzdr = drmudr(i)*(vz + wy)
              dtyzdm = drmudm(i)*(vz + wy)
              dtyzdn = drmudn(i)*(vz + wy)
              dtyzdl = drmudl(i)*(vz + wy)
              dtyzde = drmude(i)*(vz + wy)
              dtyzdt = drmudt(i)*(vz + wy)

              dtzzdr = drmudr(i)*(c43*wz - c23*ux - c23*vy)
              dtzzdm = drmudm(i)*(c43*wz - c23*ux - c23*vy)
              dtzzdn = drmudn(i)*(c43*wz - c23*ux - c23*vy)
              dtzzdl = drmudl(i)*(c43*wz - c23*ux - c23*vy)
              dtzzde = drmude(i)*(c43*wz - c23*ux - c23*vy)
              dtzzdt = drmudt(i)*(c43*wz - c23*ux - c23*vy)

! Add viscosity contributions

              dvf2dr(i) = dvf2dr(i) + rax*dtxxdr + ray*dtxydr + raz*dtxzdr
              dvf2dm(i) = dvf2dm(i) + rax*dtxxdm + ray*dtxydm + raz*dtxzdm
              dvf2dn(i) = dvf2dn(i) + rax*dtxxdn + ray*dtxydn + raz*dtxzdn
              dvf2dl(i) = dvf2dl(i) + rax*dtxxdl + ray*dtxydl + raz*dtxzdl
              dvf2de(i) = dvf2de(i) + rax*dtxxde + ray*dtxyde + raz*dtxzde
              dvf2dt(i) = dvf2dt(i) + rax*dtxxdt + ray*dtxydt + raz*dtxzdt

              dvf3dr(i) = dvf3dr(i) + rax*dtxydr + ray*dtyydr + raz*dtyzdr
              dvf3dm(i) = dvf3dm(i) + rax*dtxydm + ray*dtyydm + raz*dtyzdm
              dvf3dn(i) = dvf3dn(i) + rax*dtxydn + ray*dtyydn + raz*dtyzdn
              dvf3dl(i) = dvf3dl(i) + rax*dtxydl + ray*dtyydl + raz*dtyzdl
              dvf3de(i) = dvf3de(i) + rax*dtxyde + ray*dtyyde + raz*dtyzde
              dvf3dt(i) = dvf3dt(i) + rax*dtxydt + ray*dtyydt + raz*dtyzdt

              dvf4dr(i) = dvf4dr(i) + rax*dtxzdr + ray*dtyzdr + raz*dtzzdr
              dvf4dm(i) = dvf4dm(i) + rax*dtxzdm + ray*dtyzdm + raz*dtzzdm
              dvf4dn(i) = dvf4dn(i) + rax*dtxzdn + ray*dtyzdn + raz*dtzzdn
              dvf4dl(i) = dvf4dl(i) + rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
              dvf4de(i) = dvf4de(i) + rax*dtxzde + ray*dtyzde + raz*dtzzde
              dvf4dt(i) = dvf4dt(i) + rax*dtxzdt + ray*dtyzdt + raz*dtzzdt

! Now do stress and viscosity pieces for vf5 all at once

!    vf5 = rax*tqx + ray*tqy + raz*tqz + (umu*vf2 + vmu*vf3 + wmu*vf4)

!         tqx = rmucgp*tx
            dtqxdr = rmucgp*dtxdr(i) + tx*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqxdm = rmucgp*dtxdm(i) + tx*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqxdn = rmucgp*dtxdn(i) + tx*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqxdl = rmucgp*dtxdl(i) + tx*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqxde = rmucgp*dtxde(i) + tx*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqxdt =                   tx*fact*(               cgpt*dmutdt(i))

!         tqy = rmucgp*ty
            dtqydr = rmucgp*dtydr(i) + ty*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqydm = rmucgp*dtydm(i) + ty*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqydn = rmucgp*dtydn(i) + ty*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqydl = rmucgp*dtydl(i) + ty*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqyde = rmucgp*dtyde(i) + ty*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqydt =                   ty*fact*(               cgpt*dmutdt(i))

!         tqz = rmucgp*tz
            dtqzdr = rmucgp*dtzdr(i) + tz*fact*(cgp*dmudr(i) + cgpt*dmutdr(i))
            dtqzdm = rmucgp*dtzdm(i) + tz*fact*(cgp*dmudm(i) + cgpt*dmutdm(i))
            dtqzdn = rmucgp*dtzdn(i) + tz*fact*(cgp*dmudn(i) + cgpt*dmutdn(i))
            dtqzdl = rmucgp*dtzdl(i) + tz*fact*(cgp*dmudl(i) + cgpt*dmutdl(i))
            dtqzde = rmucgp*dtzde(i) + tz*fact*(cgp*dmude(i) + cgpt*dmutde(i))
            dtqzdt =                   tz*fact*(               cgpt*dmutdt(i))

!             piece = umu*vf2 + vmu*vf3 + wmu*vf4
                dpiecedr = umu*dvf2dr(i) + vmu*dvf3dr(i) + wmu*dvf4dr(i) +     &
                           vf2*dumudr(i) + vf3*dvmudr(i) + vf4*dwmudr(i)
                dpiecedm = umu*dvf2dm(i) + vmu*dvf3dm(i) + wmu*dvf4dm(i) +     &
                           vf2*dumudm(i) + vf3*dvmudm(i) + vf4*dwmudm(i)
                dpiecedn = umu*dvf2dn(i) + vmu*dvf3dn(i) + wmu*dvf4dn(i) +     &
                           vf2*dumudn(i) + vf3*dvmudn(i) + vf4*dwmudn(i)
                dpiecedl = umu*dvf2dl(i) + vmu*dvf3dl(i) + wmu*dvf4dl(i) +     &
                           vf2*dumudl(i) + vf3*dvmudl(i) + vf4*dwmudl(i)
                dpiecede = umu*dvf2de(i) + vmu*dvf3de(i) + wmu*dvf4de(i) +     &
                           vf2*dumude(i) + vf3*dvmude(i) + vf4*dwmude(i)
                dpiecedt = umu*dvf2dt(i) + vmu*dvf3dt(i) + wmu*dvf4dt(i) +     &
                           vf2*dumudt(i) + vf3*dvmudt(i) + vf4*dwmudt(i)

              dvf5dr(i) = rax*dtqxdr + ray*dtqydr + raz*dtqzdr + dpiecedr
              dvf5dm(i) = rax*dtqxdm + ray*dtqydm + raz*dtqzdm + dpiecedm
              dvf5dn(i) = rax*dtqxdn + ray*dtqydn + raz*dtqzdn + dpiecedn
              dvf5dl(i) = rax*dtqxdl + ray*dtqydl + raz*dtqzdl + dpiecedl
              dvf5de(i) = rax*dtqxde + ray*dtqyde + raz*dtqzde + dpiecede
              dvf5dt(i) = rax*dtqxdt + ray*dtqydt + raz*dtqzdt + dpiecedt

            end do

!           res(2,bnode) = res(2,bnode) - vf2
!           res(3,bnode) = res(3,bnode) - vf3
!           res(4,bnode) = res(4,bnode) - vf4
!           res(5,bnode) = res(5,bnode) - vf5

! Diagonal contributions

          if ( bnode <= nnodes0 ) then

            a(2,1) =  - dvf2dr(bnn)
            a(2,2) =  - dvf2dm(bnn)
            a(2,3) =  - dvf2dn(bnn)
            a(2,4) =  - dvf2dl(bnn)
            a(2,5) =  - dvf2de(bnn)
            a(2,6) =  - dvf2dt(bnn)

            a(3,1) =  - dvf3dr(bnn)
            a(3,2) =  - dvf3dm(bnn)
            a(3,3) =  - dvf3dn(bnn)
            a(3,4) =  - dvf3dl(bnn)
            a(3,5) =  - dvf3de(bnn)
            a(3,6) =  - dvf3dt(bnn)

            a(4,1) =  - dvf4dr(bnn)
            a(4,2) =  - dvf4dm(bnn)
            a(4,3) =  - dvf4dn(bnn)
            a(4,4) =  - dvf4dl(bnn)
            a(4,5) =  - dvf4de(bnn)
            a(4,6) =  - dvf4dt(bnn)

            a(5,1) =  - dvf5dr(bnn)
            a(5,2) =  - dvf5dm(bnn)
            a(5,3) =  - dvf5dn(bnn)
            a(5,4) =  - dvf5dl(bnn)
            a(5,5) =  - dvf5de(bnn)
            a(5,6) =  - dvf5dt(bnn)

            if ( form_matrix ) then
              idiag = iau(bnode)

              aa(2,1,idiag) = aa(2,1,idiag) + a(2,1)
              aa(2,2,idiag) = aa(2,2,idiag) + a(2,2)
              aa(2,3,idiag) = aa(2,3,idiag) + a(2,3)
              aa(2,4,idiag) = aa(2,4,idiag) + a(2,4)
              aa(2,5,idiag) = aa(2,5,idiag) + a(2,5)
              if ( ivisc > 2 ) aa(2,6,idiag) = aa(2,6,idiag) + a(2,6)

              aa(3,1,idiag) = aa(3,1,idiag) + a(3,1)
              aa(3,2,idiag) = aa(3,2,idiag) + a(3,2)
              aa(3,3,idiag) = aa(3,3,idiag) + a(3,3)
              aa(3,4,idiag) = aa(3,4,idiag) + a(3,4)
              aa(3,5,idiag) = aa(3,5,idiag) + a(3,5)
              if ( ivisc > 2 ) aa(3,6,idiag) = aa(3,6,idiag) + a(3,6)

              aa(4,1,idiag) = aa(4,1,idiag) + a(4,1)
              aa(4,2,idiag) = aa(4,2,idiag) + a(4,2)
              aa(4,3,idiag) = aa(4,3,idiag) + a(4,3)
              aa(4,4,idiag) = aa(4,4,idiag) + a(4,4)
              aa(4,5,idiag) = aa(4,5,idiag) + a(4,5)
              if ( ivisc > 2 ) aa(4,6,idiag) = aa(4,6,idiag) + a(4,6)

              aa(5,1,idiag) = aa(5,1,idiag) + a(5,1)
              aa(5,2,idiag) = aa(5,2,idiag) + a(5,2)
              aa(5,3,idiag) = aa(5,3,idiag) + a(5,3)
              aa(5,4,idiag) = aa(5,4,idiag) + a(5,4)
              aa(5,5,idiag) = aa(5,5,idiag) + a(5,5)
              if ( ivisc > 2 ) aa(5,6,idiag) = aa(5,6,idiag) + a(5,6)
            endif

            if ( form_matvec ) then
              do ifcn = 1, nfunctions
                res(1,bnode,ifcn) = res(1,bnode,ifcn)                          &
                                  + a(2,1)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,1)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,1)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,1)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(2,bnode,ifcn) = res(2,bnode,ifcn)                          &
                                  + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,2)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(3,bnode,ifcn) = res(3,bnode,ifcn)                          &
                                  + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,3)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(4,bnode,ifcn) = res(4,bnode,ifcn)                          &
                                  + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,4)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                res(5,bnode,ifcn) = res(5,bnode,ifcn)                          &
                                  + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)  &
                                  + a(5,5)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                if ( ivisc > 2 ) then
                  res(6,bnode,ifcn) = res(6,bnode,ifcn)                        &
                                    + a(2,6)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,6)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,6)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,6)*coltag(5,bnode)*rlam(5,bnode,ifcn)
                endif
              end do
            endif

          end if

! Off-diagonal contributions

          node_loop8 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n_cell(i)

            if (nodec == bnode) cycle node_loop8   ! already did diagonal

!           if (twod) then
!             if (abs(y(nodec)-yplane_2d) > y_coplanar_tol) cycle node_loop8
!           end if

            if ( nodec <= nnodes0 ) then

              a(2,1) =  - dvf2dr(i)
              a(2,2) =  - dvf2dm(i)
              a(2,3) =  - dvf2dn(i)
              a(2,4) =  - dvf2dl(i)
              a(2,5) =  - dvf2de(i)
              a(2,6) =  - dvf2dt(i)

              a(3,1) =  - dvf3dr(i)
              a(3,2) =  - dvf3dm(i)
              a(3,3) =  - dvf3dn(i)
              a(3,4) =  - dvf3dl(i)
              a(3,5) =  - dvf3de(i)
              a(3,6) =  - dvf3dt(i)

              a(4,1) =  - dvf4dr(i)
              a(4,2) =  - dvf4dm(i)
              a(4,3) =  - dvf4dn(i)
              a(4,4) =  - dvf4dl(i)
              a(4,5) =  - dvf4de(i)
              a(4,6) =  - dvf4dt(i)

              a(5,1) =  - dvf5dr(i)
              a(5,2) =  - dvf5dm(i)
              a(5,3) =  - dvf5dn(i)
              a(5,4) =  - dvf5dl(i)
              a(5,5) =  - dvf5de(i)
              a(5,6) =  - dvf5dt(i)

              if ( form_matrix ) then

! Determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == bnode) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                           bnode,' to the off diagonal of quad bnode ',nodec
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                aa(2,1,ioff) = aa(2,1,ioff) + a(2,1)
                aa(2,2,ioff) = aa(2,2,ioff) + a(2,2)
                aa(2,3,ioff) = aa(2,3,ioff) + a(2,3)
                aa(2,4,ioff) = aa(2,4,ioff) + a(2,4)
                aa(2,5,ioff) = aa(2,5,ioff) + a(2,5)
                if ( ivisc > 2 ) aa(2,6,ioff) = aa(2,6,ioff) + a(2,6)

                aa(3,1,ioff) = aa(3,1,ioff) + a(3,1)
                aa(3,2,ioff) = aa(3,2,ioff) + a(3,2)
                aa(3,3,ioff) = aa(3,3,ioff) + a(3,3)
                aa(3,4,ioff) = aa(3,4,ioff) + a(3,4)
                aa(3,5,ioff) = aa(3,5,ioff) + a(3,5)
                if ( ivisc > 2 ) aa(3,6,ioff) = aa(3,6,ioff) + a(3,6)

                aa(4,1,ioff) = aa(4,1,ioff) + a(4,1)
                aa(4,2,ioff) = aa(4,2,ioff) + a(4,2)
                aa(4,3,ioff) = aa(4,3,ioff) + a(4,3)
                aa(4,4,ioff) = aa(4,4,ioff) + a(4,4)
                aa(4,5,ioff) = aa(4,5,ioff) + a(4,5)
                if ( ivisc > 2 ) aa(4,6,ioff) = aa(4,6,ioff) + a(4,6)

                aa(5,1,ioff) = aa(5,1,ioff) + a(5,1)
                aa(5,2,ioff) = aa(5,2,ioff) + a(5,2)
                aa(5,3,ioff) = aa(5,3,ioff) + a(5,3)
                aa(5,4,ioff) = aa(5,4,ioff) + a(5,4)
                aa(5,5,ioff) = aa(5,5,ioff) + a(5,5)
                if ( ivisc > 2 ) aa(5,6,ioff) = aa(5,6,ioff) + a(5,6)

              endif

              if ( form_matvec ) then
                do ifcn = 1, nfunctions
                  res(1,nodec,ifcn) = res(1,nodec,ifcn)                        &
                                    + a(2,1)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,1)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,1)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,1)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(2,nodec,ifcn) = res(2,nodec,ifcn)                        &
                                    + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,2)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(3,nodec,ifcn) = res(3,nodec,ifcn)                        &
                                    + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,3)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(4,nodec,ifcn) = res(4,nodec,ifcn)                        &
                                    + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,4)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  res(5,nodec,ifcn) = res(5,nodec,ifcn)                        &
                                    + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,5)*coltag(5,bnode)*rlam(5,bnode,ifcn)

                  if ( ivisc > 2 ) then
                    res(6,nodec,ifcn) = res(6,nodec,ifcn)                      &
                                    + a(2,6)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,6)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,6)*coltag(4,bnode)*rlam(4,bnode,ifcn)&
                                    + a(5,6)*coltag(5,bnode)*rlam(5,bnode,ifcn)
                  endif
                end do
              endif

            end if

          end do node_loop8

        end do node_loop7

      end do loop_quad_faces

    end if close_off_viscous

    end do boundary_loop

    add_diag_due_to_dirichlet : if ( form_matvec ) then

! Account for strong viscous BC but be careful to remember that symmetry takes
! precedence

      node_hit_as_viscous = .false.

      viscous_bcs : do ib = 1, nbound

        if ( .not. bc_strong_viscous_adjoint(bc(ib)%ibc) ) cycle viscous_bcs

        do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)

          local_node1 : if ( inode <= nnodes0 ) then

            already_hit : if ( .not. node_hit_as_viscous(inode) ) then

              if ( need_grid_velocity ) then
                uwall = bc(ib)%bdxdt(i)
                vwall = bc(ib)%bdydt(i)
                wwall = bc(ib)%bdzdt(i)
              else
                uwall = my_0
                vwall = my_0
                wwall = my_0
              endif

              if ( has_x_symmetry(symmetry(inode)) ) then
                res2q1 = my_0
                res2q2 = my_1
                res2q3 = my_0
                res2q4 = my_0
                res2q5 = my_0
              else
                res2q1 = -uwall
                res2q2 = my_1
                res2q3 = my_0
                res2q4 = my_0
                res2q5 = my_0
              endif

              if ( has_y_symmetry(symmetry(inode)) ) then
                res3q1 = my_0
                res3q2 = my_0
                res3q3 = my_1
                res3q4 = my_0
                res3q5 = my_0
              else
                res3q1 = -vwall
                res3q2 = my_0
                res3q3 = my_1
                res3q4 = my_0
                res3q5 = my_0
              endif

              if ( has_z_symmetry(symmetry(inode)) ) then
                res4q1 = my_0
                res4q2 = my_0
                res4q3 = my_0
                res4q4 = my_1
                res4q5 = my_0
              else
                res4q1 = -wwall
                res4q2 = my_0
                res4q3 = my_0
                res4q4 = my_1
                res4q5 = my_0
              endif

              res5q1 = -twall/ggm1-my_half*(uwall*uwall+vwall*vwall+wwall*wwall)
              res5q2 = my_0
              res5q3 = my_0
              res5q4 = my_0
              res5q5 = my_1

              res(1,inode,:) = res(1,inode,:)                                  &
                             + res2q1*rlam(2,inode,:)                          &
                             + res3q1*rlam(3,inode,:)                          &
                             + res4q1*rlam(4,inode,:)                          &
                             + res5q1*rlam(5,inode,:)
              res(2,inode,:) = res(2,inode,:)                                  &
                             + res2q2*rlam(2,inode,:)                          &
                             + res3q2*rlam(3,inode,:)                          &
                             + res4q2*rlam(4,inode,:)                          &
                             + res5q2*rlam(5,inode,:)
              res(3,inode,:) = res(3,inode,:)                                  &
                             + res2q3*rlam(2,inode,:)                          &
                             + res3q3*rlam(3,inode,:)                          &
                             + res4q3*rlam(4,inode,:)                          &
                             + res5q3*rlam(5,inode,:)
              res(4,inode,:) = res(4,inode,:)                                  &
                             + res2q4*rlam(2,inode,:)                          &
                             + res3q4*rlam(3,inode,:)                          &
                             + res4q4*rlam(4,inode,:)                          &
                             + res5q4*rlam(5,inode,:)
              res(5,inode,:) = res(5,inode,:)                                  &
                             + res2q5*rlam(2,inode,:)                          &
                             + res3q5*rlam(3,inode,:)                          &
                             + res4q5*rlam(4,inode,:)                          &
                             + res5q5*rlam(5,inode,:)
              if ( ivisc == 6 ) res(6,inode,:) = res(6,inode,:)+rlam(6,inode,:)

              node_hit_as_viscous(inode) = .true.

            endif already_hit

          endif local_node1

        end do

      end do viscous_bcs

      symm_bcs : do ib = 1, nbound

        if ( .not. (bc(ib)%ibc == symmetry_x .or. bc(ib)%ibc == symmetry_y     &
               .or. bc(ib)%ibc == symmetry_z) ) cycle symm_bcs

        eqn = 0
        select case(bc(ib)%ibc)
        case(symmetry_x)
          eqn = n_momx
        case(symmetry_y)
          eqn = n_momy
        case(symmetry_z)
          eqn = n_momz
        end select

        do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)

! Rely on the node_hit_as_viscous flag to tell us if we already treated this
! point in the viscous loop above

          if ( inode <= nnodes0 ) then
            if ( .not. node_hit_as_viscous(inode) ) then
              res(eqn,inode,:) = res(eqn,inode,:) + rlam(eqn,inode,:)
            endif
          endif

        end do

      end do symm_bcs

    endif add_diag_due_to_dirichlet

  end subroutine dvisrhs_bc_mix6


!=============================== DVISRHS_BC_MIX5 =============================80
!
! This routine computes the viscous flux Jacobians on the boundaries
! for the general (mixed) element case (corresponding to subroutine
! bc_visc_flux_mix_cell) and adds them to the left-hand-side
!
! Note: qnode is assumed to contain conserved variables
!
!=============================================================================80
  subroutine dvisrhs_bc_mix5(nnodes0, nnodes01, nnz, nbound, bc, ia, ja, iau,  &
                             nelem, elem, x, y, z, qnode, amut, n_tot, adim,   &
                             nfunctions, coltag, rlam, n_turb, turb, res, aa)

    use kinddefs,         only : dp
    use info_depr,        only : re, twod, ivisc
    use bc_names,         only : bc_ignore_2d,                                 &
                                 bc_has_visc_flux_closure,                     &
                                 bc_strong_viscous_adjoint,                    &
                                 symmetry_x, symmetry_y, symmetry_z
    use bc_types,         only : bcgrid_type
    use element_types,    only : elem_type
    use element_defs,     only : max_face_per_cell, max_node_per_cell
    use grid_metrics,     only : dual_area_quad
    use twod_util,        only : yplane_2d, y_coplanar_tol
    use utilities,        only : cell_jacobians, cell_gradients
    use turb_sa_const,    only : cv1
    use generic_gas_map,  only : n_momx, n_momy, n_momz
    use adjoint_switches, only : use_bp_model
    use nml_noninertial_reference_frame, only : noninertial

    integer,                                        intent(in)    :: n_tot
    integer,                                        intent(in)    :: adim
    integer,                                        intent(in)    :: nnodes0
    integer,                                        intent(in)    :: nnodes01
    integer,                                        intent(in)    :: nelem
    integer,                                        intent(in)    :: nfunctions
    integer,                                        intent(in)    :: nnz
    integer,                                        intent(in)    :: nbound
    integer,                                        intent(in)    :: n_turb
    integer,  dimension(:),                         intent(in)    :: ia, ja, iau
    real(dp), dimension(nnodes01),                  intent(in)    :: amut
    real(dp), dimension(nnodes01),                  intent(in)    :: x,y,z
    real(dp), dimension(n_tot, nnodes01),           intent(in)    :: qnode
    real(dp), dimension(adim,nnodes01),             intent(in)    :: coltag
    real(dp), dimension(n_turb,nnodes01),           intent(in)    :: turb
    real(dp), dimension(adim,nnodes01, nfunctions), intent(in)    :: rlam
    real(dp), dimension(adim,nnodes01, nfunctions),                            &
                                        optional,   intent(inout) :: res
    real(dp), dimension(adim,adim,nnz), optional,   intent(inout) :: aa
    type(elem_type),   dimension(nelem),            intent(in)    :: elem
    type(bcgrid_type), dimension(nbound),           intent(in)    :: bc

    integer, dimension(max_node_per_cell) :: c2n_cell
    integer, dimension(max_node_per_cell) :: node_map

    integer :: i, icell, ielem, nf, node, inode, eqn
    integer :: in1, i_local, idiag, ioff
    integer :: bnn1, bnn2, bnn3, bnn4
    integer :: bnode1, bnode2, bnode3, bnode4
    integer :: bnode, bnn, nodec, ifcn
    integer :: nodes_local, edges_local, ib
    integer :: k, column, face_2d

    real(dp) :: cell_vol, fact
    real(dp) :: rmu
    real(dp) :: ux, uy, uz
    real(dp) :: vx, vy, vz
    real(dp) :: wx, wy, wz
    real(dp) :: x1, x2, x3, xc
    real(dp) :: y1, y2, y3, yc
    real(dp) :: z1, z2, z3, zc
    real(dp) :: rei, areax, areay, areaz
    real(dp) :: dtxxdm, dtxxdt
    real(dp) :: dtxydm, dtxydn, dtxydt
    real(dp) :: dtxzdm, dtxzdl, dtxzdt
    real(dp) :: dtyydn, dtyydt
    real(dp) :: dtyzdn, dtyzdl, dtyzdt
    real(dp) :: dtzzdl, dtzzdt
    real(dp) :: rax, ray, raz
    real(dp) :: chi,dchidt,psi,dpsidt,base,dbasedt
    real(dp) :: fv1,fv1dt
    real(dp) :: dmutdt, mu_node

    real(dp), dimension(4)                 :: xnorm_q, ynorm_q, znorm_q
    real(dp), dimension(max_face_per_cell)    :: nx, ny, nz
    real(dp), dimension(max_node_per_cell)      :: u_node, v_node, w_node
    real(dp), dimension(max_node_per_cell)      :: x_node, y_node, z_node
    real(dp), dimension(max_node_per_cell)      :: turb_node
    real(dp), dimension(3,max_node_per_cell)    :: q_node
    real(dp), dimension(max_node_per_cell)    :: drmudt
    real(dp), dimension(max_node_per_cell)      :: duxdm
    real(dp), dimension(max_node_per_cell)      :: duydm
    real(dp), dimension(max_node_per_cell)      :: duzdm
    real(dp), dimension(max_node_per_cell)      :: dvxdn
    real(dp), dimension(max_node_per_cell)      :: dvydn
    real(dp), dimension(max_node_per_cell)      :: dvzdn
    real(dp), dimension(max_node_per_cell)      :: dwxdl
    real(dp), dimension(max_node_per_cell)      :: dwydl
    real(dp), dimension(max_node_per_cell)      :: dwzdl

    real(dp), dimension(max_node_per_cell)      :: dvf2dm
    real(dp), dimension(max_node_per_cell)      :: dvf2dn, dvf2dl,dvf2dt
    real(dp), dimension(max_node_per_cell)      :: dvf3dm
    real(dp), dimension(max_node_per_cell)      :: dvf3dn, dvf3dl,dvf3dt
    real(dp), dimension(max_node_per_cell)      :: dvf4dm
    real(dp), dimension(max_node_per_cell)      :: dvf4dn, dvf4dl,dvf4dt

    real(dp), dimension(max_node_per_cell)      :: dgradx_celldq, dgrady_celldq
    real(dp), dimension(max_node_per_cell)      :: dgradz_celldq
    real(dp), dimension(3)                      :: gradx_cell, grady_cell
    real(dp), dimension(3)                      :: gradz_cell

    real(dp), dimension(4,5)    :: a

    real(dp),  parameter                   :: my_2   = 2.0_dp
    real(dp),  parameter                   :: my_6th = 1.0_dp/6.0_dp

    logical :: form_matvec = .false.
    logical :: form_matrix = .false.

    logical, dimension(nnodes0) :: node_hit_as_viscous

  continue

    if ( present(res) ) form_matvec = .true.
    if ( present(aa) )  form_matrix = .true.

    if ( (.not.form_matvec) .and. (.not.form_matrix)) then
      write(*,*) 'WARNING: dvisrhs_bc_mix6 called but did not request output...'
      return
    endif

    rei = 1.0_dp/re

! For bctype viscous_solid:
! note that the viscous no-slip strong boundary residuals will be set to zero
! after the all viscous fluxes have been closed off on the boundaries

    boundary_loop : do ib = 1, nbound

      if ( twod .and.  bc_ignore_2d(bc(ib)%ibc) ) cycle boundary_loop

    close_off_viscous: if ( bc_has_visc_flux_closure(bc(ib)%ibc) ) then

! First loop over all the tria boundary faces

      loop_tria_faces: do nf = 1, bc(ib)%nbfacet

!       global node numbers of the cell/face nodes on the boundary

        bnode1 = bc(ib)%ibnode(bc(ib)%f2ntb(nf,1))
        bnode2 = bc(ib)%ibnode(bc(ib)%f2ntb(nf,2))
        bnode3 = bc(ib)%ibnode(bc(ib)%f2ntb(nf,3))

        icell = bc(ib)%f2ntb(nf,4)         ! global cell number
        ielem = bc(ib)%f2ntb(nf,5)         ! cell type indicator

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       determine the local node number in the cell attached to the current
!       boundary face that corresponds to each of the local boundary nodes

        bnn1 = 0
        bnn2 = 0
        bnn3 = 0
        do node=1,elem(ielem)%node_per_cell

          if (bnode1 == c2n_cell(node)) bnn1 = node
          if (bnode2 == c2n_cell(node)) bnn2 = node
          if (bnode3 == c2n_cell(node)) bnn3 = node

        end do

! Compute cell averages, cell center, and set up some local solution arrays

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        turb_node(:)= 0.0_dp
        q_node(:,:) = 0.0_dp

        duxdm(:) = 0._dp
        duydm(:) = 0._dp
        duzdm(:) = 0._dp

        dvxdn(:) = 0._dp
        dvydn(:) = 0._dp
        dvzdn(:) = 0._dp

        dwxdl(:) = 0._dp
        dwydl(:) = 0._dp
        dwzdl(:) = 0._dp

        drmudt = 0.0_dp

        node_loop1 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          u_node(i) = qnode(2,node)
          v_node(i) = qnode(3,node)
          w_node(i) = qnode(4,node)

          if ( ivisc > 2 ) turb_node(i) = turb(1,node)
          mu_node = 1.0_dp + amut(node)

          rmu = rmu + mu_node

        end do node_loop1

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu = rmu*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        xc  = 0.0_dp
        yc  = 0.0_dp
        zc  = 0.0_dp

        do i = 1, elem(ielem)%node_per_cell

!         global node number

          node = c2n_cell(i)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(elem(ielem)%node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

        if ( ivisc > 2 ) then
          node_loop2 : do i_local = 1, nodes_local
            i = node_map(i_local)
            chi = turb_node(i)
            dchidt = 1.0_dp
            if ( use_bp_model ) then
              psi = chi
                dpsidt = dchidt
              if ( psi < 10.0_dp ) then
                base = 1.0_dp + exp(20.0_dp*chi)
                  dbasedt = exp(20.0_dp*chi)*20.0_dp*dchidt
                psi = 0.05_dp*log(base)
                  dpsidt = 0.05_dp/base*dbasedt
              endif
              fv1 = psi**3 / (psi**3 + cv1**3)
                fv1dt = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidt               &
                         - psi**3*3.0_dp*psi**2*dpsidt ) / (psi**3 + cv1**3)**2
!             mut = max( 0._dp , turb_node(i)*fv1 )
              if ( turb_node(i)*fv1 > 0.0_dp ) then
                dmutdt = turb_node(i)*fv1dt + fv1
              else
                dmutdt = 0.0_dp
              endif
            else
              fv1 = chi**3/(chi**3 + cv1**3)
              fv1dt = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidt                  &
                  - chi**3*3.0_dp*chi**2*dchidt)/(chi**3+cv1**3)/(chi**3+cv1**3)
!             mut = turb_node(i)*fv1
              dmutdt = turb_node(i)*fv1dt + fv1
            endif
            drmudt(i) = dmutdt
          end do node_loop2
        endif

! Divide the viscosity sum derivatives by the averaging factor

        fact = 1._dp / real(nodes_local, dp)

        drmudt = drmudt*fact

! Get the gradients in the primal cell via Green-Gauss

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            3, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note: these are with respect to the primitive variables

        call cell_jacobians(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            elem(ielem)%local_f2n, elem(ielem)%e2n_2d,         &
                            dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                            cell_vol, nx, ny, nz)

! Convert to jacobians with respect to conservative variables

        if (twod) then

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdm(i) = dgradx_celldq(i)
            dwxdl(i) = dgradx_celldq(i)

            duzdm(i) = dgradz_celldq(i)
            dwzdl(i) = dgradz_celldq(i)

          end do

        else

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdm(i) = dgradx_celldq(i)
            dvxdn(i) = dgradx_celldq(i)
            dwxdl(i) = dgradx_celldq(i)

            duydm(i) = dgrady_celldq(i)
            dvydn(i) = dgrady_celldq(i)
            dwydl(i) = dgrady_celldq(i)

            duzdm(i) = dgradz_celldq(i)
            dvzdn(i) = dgradz_celldq(i)
            dwzdl(i) = dgradz_celldq(i)

          end do

        end if

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a triangle, this is just 1/3 the face normal, and is
! the same at each node

        x1 = x(bnode1)
        y1 = y(bnode1)
        z1 = z(bnode1)

        x2 = x(bnode2)
        y2 = y(bnode2)
        z2 = z(bnode2)

        x3 = x(bnode3)
        y3 = y(bnode3)
        z3 = z(bnode3)

!       - sign on normals because Green-Gauss needs outward pointing normals

        areax = -my_6th*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
        areay = -my_6th*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
        areaz = -my_6th*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )

!       [nondimensionalization factor rei ] * [ viscosity ]
!       [ unit normal and area ] at dual face

        rax = rei*areax
        ray = rei*areay
        raz = rei*areaz

!       form some more intermediate Jacobians at all nodes

        do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

! Stress pieces

          dtxxdm = rmu*my_2*duxdm(i)

          dtxydm = rmu*duydm(i)
          dtxydn = rmu*dvxdn(i)

          dtxzdm = rmu*duzdm(i)
          dtxzdl = rmu*dwxdl(i)

          dtyydn = rmu*my_2*dvydn(i)

          dtyzdn = rmu*dvzdn(i)
          dtyzdl = rmu*dwydl(i)

          dtzzdl = rmu*my_2*dwzdl(i)

! Add stress contributions

          dvf2dm(i) = rax*dtxxdm + ray*dtxydm + raz*dtxzdm
          dvf2dn(i) =              ray*dtxydn
          dvf2dl(i) =                         + raz*dtxzdl
          dvf2dt(i) = 0.0_dp

          dvf3dm(i) = rax*dtxydm
          dvf3dn(i) = rax*dtxydn + ray*dtyydn + raz*dtyzdn
          dvf3dl(i) =                           raz*dtyzdl
          dvf3dt(i) = 0.0_dp

          dvf4dm(i) = rax*dtxzdm
          dvf4dn(i) =              ray*dtyzdn
          dvf4dl(i) = rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
          dvf4dt(i) = 0.0_dp

! Viscosity pieces

          dtxxdt = drmudt(i)*my_2*ux

          dtxydt = drmudt(i)*(uy + vx)

          dtxzdt = drmudt(i)*(uz + wx)

          dtyydt = drmudt(i)*my_2*vy

          dtyzdt = drmudt(i)*(vz + wy)

          dtzzdt = drmudt(i)*my_2*wz

! Add viscosity contributions

          dvf2dt(i) = dvf2dt(i) + rax*dtxxdt + ray*dtxydt + raz*dtxzdt
          dvf3dt(i) = dvf3dt(i) + rax*dtxydt + ray*dtyydt + raz*dtyzdt
          dvf4dt(i) = dvf4dt(i) + rax*dtxzdt + ray*dtyzdt + raz*dtzzdt

        end do

! Finally for each node on the boundary tria, send the appropriate jacobian
! contributions to each

!       res(2,bnode) = res(2,bnode) - vf2
!       res(3,bnode) = res(3,bnode) - vf3
!       res(4,bnode) = res(4,bnode) - vf4
!       res(5,bnode) = res(5,bnode) - vf5

        node_loop3 : do in1 = 1,3

          if (in1 == 1) then
            bnode = bnode1
            bnn   = bnn1
          else if (in1 == 2) then
            bnode = bnode2
            bnn   = bnn2
          else
            bnode = bnode3
            bnn   = bnn3
          end if

          if (twod) then
            if (abs(y(bnode)-yplane_2d) > y_coplanar_tol) cycle node_loop3
          end if

! Diagonal contributions

          if ( bnode <= nnodes0 ) then

            a(2,2) =  - dvf2dm(bnn)
            a(2,3) =  - dvf2dn(bnn)
            a(2,4) =  - dvf2dl(bnn)
            a(2,5) =  - dvf2dt(bnn)

            a(3,2) =  - dvf3dm(bnn)
            a(3,3) =  - dvf3dn(bnn)
            a(3,4) =  - dvf3dl(bnn)
            a(3,5) =  - dvf3dt(bnn)

            a(4,2) =  - dvf4dm(bnn)
            a(4,3) =  - dvf4dn(bnn)
            a(4,4) =  - dvf4dl(bnn)
            a(4,5) =  - dvf4dt(bnn)

            if ( form_matrix ) then
              idiag = iau(bnode)

              aa(2,2,idiag) = aa(2,2,idiag) + a(2,2)
              aa(2,3,idiag) = aa(2,3,idiag) + a(2,3)
              aa(2,4,idiag) = aa(2,4,idiag) + a(2,4)
              if ( ivisc > 2 ) aa(2,5,idiag) = aa(2,5,idiag) + a(2,5)

              aa(3,2,idiag) = aa(3,2,idiag) + a(3,2)
              aa(3,3,idiag) = aa(3,3,idiag) + a(3,3)
              aa(3,4,idiag) = aa(3,4,idiag) + a(3,4)
              if ( ivisc > 2 ) aa(3,5,idiag) = aa(3,5,idiag) + a(3,5)

              aa(4,2,idiag) = aa(4,2,idiag) + a(4,2)
              aa(4,3,idiag) = aa(4,3,idiag) + a(4,3)
              aa(4,4,idiag) = aa(4,4,idiag) + a(4,4)
              if ( ivisc > 2 ) aa(4,5,idiag) = aa(4,5,idiag) + a(4,5)

            endif

            if ( form_matvec ) then
              do ifcn = 1, nfunctions
                res(2,bnode,ifcn) = res(2,bnode,ifcn)                          &
                                  + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                res(3,bnode,ifcn) = res(3,bnode,ifcn)                          &
                                  + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                res(4,bnode,ifcn) = res(4,bnode,ifcn)                          &
                                  + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                if ( ivisc > 2 ) then
                  res(5,bnode,ifcn) = res(5,bnode,ifcn)                        &
                                    + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)
                endif
              end do
            endif

          end if

! Off-diagonal contributions

          node_loop4 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n_cell(i)

            if (nodec == bnode) cycle node_loop4   ! already did diagonal

!           if (twod) then
!             if (abs(y(nodec)-yplane_2d) > y_coplanar_tol) cycle node_loop4
!           end if

            if ( nodec <= nnodes0 ) then

              a(2,2) =  - dvf2dm(i)
              a(2,3) =  - dvf2dn(i)
              a(2,4) =  - dvf2dl(i)
              a(2,5) =  - dvf2dt(i)

              a(3,2) =  - dvf3dm(i)
              a(3,3) =  - dvf3dn(i)
              a(3,4) =  - dvf3dl(i)
              a(3,5) =  - dvf3dt(i)

              a(4,2) =  - dvf4dm(i)
              a(4,3) =  - dvf4dn(i)
              a(4,4) =  - dvf4dl(i)
              a(4,5) =  - dvf4dt(i)

              if ( form_matrix ) then

! Determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == bnode) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                           bnode,' to the off diagonal of tria bnode ',nodec
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                aa(2,2,ioff) = aa(2,2,ioff) + a(2,2)
                aa(2,3,ioff) = aa(2,3,ioff) + a(2,3)
                aa(2,4,ioff) = aa(2,4,ioff) + a(2,4)
                if ( ivisc > 2 ) aa(2,5,ioff) = aa(2,5,ioff) + a(2,5)

                aa(3,2,ioff) = aa(3,2,ioff) + a(3,2)
                aa(3,3,ioff) = aa(3,3,ioff) + a(3,3)
                aa(3,4,ioff) = aa(3,4,ioff) + a(3,4)
                if ( ivisc > 2 ) aa(3,5,ioff) = aa(3,5,ioff) + a(3,5)

                aa(4,2,ioff) = aa(4,2,ioff) + a(4,2)
                aa(4,3,ioff) = aa(4,3,ioff) + a(4,3)
                aa(4,4,ioff) = aa(4,4,ioff) + a(4,4)
                if ( ivisc > 2 ) aa(4,5,ioff) = aa(4,5,ioff) + a(4,5)

              endif

              if ( form_matvec ) then
                do ifcn = 1, nfunctions
                  res(2,nodec,ifcn) = res(2,nodec,ifcn)                        &
                                    + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                  res(3,nodec,ifcn) = res(3,nodec,ifcn)                        &
                                    + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                  res(4,nodec,ifcn) = res(4,nodec,ifcn)                        &
                                    + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                  if ( ivisc > 2 ) then
                    res(5,nodec,ifcn) = res(5,nodec,ifcn)                      &
                                    + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)
                  endif
                end do
              endif

            end if

          end do node_loop4

        end do node_loop3

      end do loop_tria_faces

! Now loop over all the quad boundary faces

      loop_quad_faces: do nf = 1, bc(ib)%nbfaceq

!       global node numbers of the cell/face nodes on the boundary

        bnode1 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,1))
        bnode2 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,2))
        bnode3 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,3))
        bnode4 = bc(ib)%ibnode(bc(ib)%f2nqb(nf,4))

        icell = bc(ib)%f2nqb(nf,5)         ! global cell number
        ielem = bc(ib)%f2nqb(nf,6)         ! cell type indicator

!       copy c2n array from the derived type so we  minimize
!       references to derived types inside loops as much as possible

        do node = 1, elem(ielem)%node_per_cell
          c2n_cell(node) = elem(ielem)%c2n(node,icell)
        end do

! Set some loop indicies and local mapping arrays depending on whether we are
! doing a 2D case or a 3D case

        node_map(:) = 0

        if (twod) then

          face_2d = elem(ielem)%face_2d

          nodes_local = 3
          if (elem(ielem)%local_f2n(face_2d,1) /=                              &
              elem(ielem)%local_f2n(face_2d,4)) nodes_local = 4

          do i=1,nodes_local
            node_map(i) = elem(ielem)%local_f2n(face_2d,i)
          end do

          edges_local = 3
          if (elem(ielem)%local_f2e(face_2d,1) /=                              &
              elem(ielem)%local_f2e(face_2d,4)) edges_local = 4

        else

          nodes_local = elem(ielem)%node_per_cell

          do i=1,nodes_local
            node_map(i) = i
          end do

          edges_local = elem(ielem)%edge_per_cell

        end if

!       determine the local node number in the cell attached to the current
!       boundary face that corresponds to each of the local boundary nodes

        bnn1 = 0
        bnn2 = 0
        bnn3 = 0
        bnn4 = 0
        do node=1,elem(ielem)%node_per_cell

          if (bnode1 == c2n_cell(node)) bnn1 = node
          if (bnode2 == c2n_cell(node)) bnn2 = node
          if (bnode3 == c2n_cell(node)) bnn3 = node
          if (bnode4 == c2n_cell(node)) bnn4 = node

        end do

! Compute cell averages, cell center, and set up some local solution arrays

        rmu = 0.0_dp

        x_node(:)   = 0.0_dp
        y_node(:)   = 0.0_dp
        z_node(:)   = 0.0_dp
        u_node(:)   = 0.0_dp
        v_node(:)   = 0.0_dp
        w_node(:)   = 0.0_dp
        turb_node(:)= 0.0_dp
        q_node(:,:) = 0.0_dp

        duxdm(:) = 0._dp
        duydm(:) = 0._dp
        duzdm(:) = 0._dp

        dvxdn(:) = 0._dp
        dvydn(:) = 0._dp
        dvzdn(:) = 0._dp

        dwxdl(:) = 0._dp
        dwydl(:) = 0._dp
        dwzdl(:) = 0._dp

        drmudt = 0.0_dp

        node_loop5 : do i_local = 1, nodes_local

!         local node number

          i = node_map(i_local)

!         global node number

          node = c2n_cell(i)

          x_node(i) = x(node)
          y_node(i) = y(node)
          z_node(i) = z(node)

          u_node(i) = qnode(2,node)
          v_node(i) = qnode(3,node)
          w_node(i) = qnode(4,node)

          if ( ivisc > 2 ) turb_node(i) = turb(1,node)
          mu_node = 1.0_dp + amut(node)

          rmu = rmu + mu_node

        end do node_loop5

!       now compute cell averages by dividing by the number of nodes
!       that contributed

        fact = 1._dp / real(nodes_local, dp)

        rmu = rmu*fact

!       compute the cell center (must loop over node_per_cell even in 2D)

        xc  = 0.0_dp
        yc  = 0.0_dp
        zc  = 0.0_dp

        do i = 1, elem(ielem)%node_per_cell

!         global node number

          node = c2n_cell(i)

          xc  =  xc + x(node)
          yc  =  yc + y(node)
          zc  =  zc + z(node)

        end do

        fact = 1._dp / real(elem(ielem)%node_per_cell, dp)

        xc  =  xc*fact
        yc  =  yc*fact
        zc  =  zc*fact

        if ( ivisc > 2 ) then
          node_loop6 : do i_local = 1, nodes_local
            i = node_map(i_local)
            chi = turb_node(i)
            dchidt = 1.0_dp
            if ( use_bp_model ) then
              psi = chi
                dpsidt = dchidt
              if ( psi < 10.0_dp ) then
                base = 1.0_dp + exp(20.0_dp*chi)
                  dbasedt = exp(20.0_dp*chi)*20.0_dp*dchidt
                psi = 0.05_dp*log(base)
                  dpsidt = 0.05_dp/base*dbasedt
              endif
              fv1 = psi**3 / (psi**3 + cv1**3)
                fv1dt = ( (psi**3 + cv1**3)*3.0_dp*psi**2*dpsidt               &
                         - psi**3*3.0_dp*psi**2*dpsidt ) / (psi**3 + cv1**3)**2
!             mut = max( 0._dp , turb_node(i)*fv1 )
              if ( turb_node(i)*fv1 > 0.0_dp ) then
                dmutdt = turb_node(i)*fv1dt + fv1
              else
                dmutdt = 0.0_dp
              endif
            else
              fv1 = chi**3/(chi**3 + cv1**3)
              fv1dt = ((chi**3 + cv1**3)*3.0_dp*chi**2*dchidt                  &
                  - chi**3*3.0_dp*chi**2*dchidt)/(chi**3+cv1**3)/(chi**3+cv1**3)
!             mut = rho_node(i)*turb_node(i)*fv1
              dmutdt = turb_node(i)*fv1dt + fv1
            endif
            drmudt(i) = dmutdt
          end do node_loop6
        endif

! Divide the viscosity sum derivatives by the averaging factor

        fact = 1._dp / real(nodes_local, dp)

        drmudt = drmudt*fact

! Get the gradients in the primal cell via Green-Gauss

        q_node(1,:) = u_node(:)
        q_node(2,:) = v_node(:)
        q_node(3,:) = w_node(:)

        call cell_gradients(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            3, q_node, elem(ielem)%local_f2n,                  &
                            elem(ielem)%e2n_2d, gradx_cell, grady_cell,        &
                            gradz_cell, cell_vol, nx, ny, nz)

        ux = gradx_cell(1)
        vx = gradx_cell(2)
        wx = gradx_cell(3)

        uy = grady_cell(1)
        vy = grady_cell(2)
        wy = grady_cell(3)

        uz = gradz_cell(1)
        vz = gradz_cell(2)
        wz = gradz_cell(3)

! Get the jacobians of the gradients in the primal cell via Green-Gauss
! Note: these are with respect to the primitive variables

        call cell_jacobians(edges_local, max_node_per_cell,                    &
                            elem(ielem)%face_per_cell, x_node, y_node, z_node, &
                            elem(ielem)%local_f2n, elem(ielem)%e2n_2d,         &
                            dgradx_celldq, dgrady_celldq, dgradz_celldq,       &
                            cell_vol, nx, ny, nz)

! Convert to jacobians with respect to conservative variables

        if (twod) then

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdm(i) = dgradx_celldq(i)
            dwxdl(i) = dgradx_celldq(i)

            duzdm(i) = dgradz_celldq(i)
            dwzdl(i) = dgradz_celldq(i)

          end do

        else

          do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

            duxdm(i) = dgradx_celldq(i)
            dvxdn(i) = dgradx_celldq(i)
            dwxdl(i) = dgradx_celldq(i)

            duydm(i) = dgrady_celldq(i)
            dvydn(i) = dgrady_celldq(i)
            dwydl(i) = dgrady_celldq(i)

            duzdm(i) = dgradz_celldq(i)
            dvzdn(i) = dgradz_celldq(i)
            dwzdl(i) = dgradz_celldq(i)

          end do

        end if

! Now we get this boundary faces' contribution to the dual normal at each
! node; note that for a quad, the dual normal is in general different at
! at each node on the face

        call dual_area_quad(nnodes01,x,y,z,bnode1,bnode2,bnode3,bnode4,        &
                            noninertial,xnorm_q,ynorm_q,znorm_q)

! Finally for each node on the boundary quad, send the appropriate jacobian
! contributions to each

        node_loop7 : do in1 = 1,4

          if (in1 == 1) then
            bnode = bnode1
            bnn   = bnn1
          else if (in1 == 2) then
            bnode = bnode2
            bnn   = bnn2
          else if (in1 == 3) then
            bnode = bnode3
            bnn   = bnn3
          else
            bnode = bnode4
            bnn   = bnn4
          end if

          if (twod) then
            if (abs(y(bnode)-yplane_2d) > y_coplanar_tol) cycle node_loop7
          end if

! Viscous contributions at dual face [ full Navier-Stokes terms ]

!           [nondimensionalization factor rei ] * [ viscosity ]
!           [ unit normal and area ] at dual face

            areax = xnorm_q(in1)
            areay = ynorm_q(in1)
            areaz = znorm_q(in1)

            rax = rei*areax
            ray = rei*areay
            raz = rei*areaz

!           form some intermediate Jacobians at all nodes

            do i_local = 1, nodes_local

!             local node number

              i = node_map(i_local)

! Stress pieces

              dtxxdm = rmu*my_2*duxdm(i)

              dtxydm = rmu*duydm(i)
              dtxydn = rmu*dvxdn(i)

              dtxzdm = rmu*duzdm(i)
              dtxzdl = rmu*dwxdl(i)

              dtyydn = rmu*my_2*dvydn(i)

              dtyzdn = rmu*dvzdn(i)
              dtyzdl = rmu*dwydl(i)

              dtzzdl = rmu*my_2*dwzdl(i)

! Add stress contributions

              dvf2dm(i) = rax*dtxxdm + ray*dtxydm + raz*dtxzdm
              dvf2dn(i) =              ray*dtxydn
              dvf2dl(i) =                         + raz*dtxzdl
              dvf2dt(i) = 0.0_dp

              dvf3dm(i) = rax*dtxydm
              dvf3dn(i) = rax*dtxydn + ray*dtyydn + raz*dtyzdn
              dvf3dl(i) =                           raz*dtyzdl
              dvf3dt(i) = 0.0_dp

              dvf4dm(i) = rax*dtxzdm
              dvf4dn(i) =              ray*dtyzdn
              dvf4dl(i) = rax*dtxzdl + ray*dtyzdl + raz*dtzzdl
              dvf4dt(i) = 0.0_dp

! Viscosity pieces

              dtxxdt = drmudt(i)*my_2*ux

              dtxydt = drmudt(i)*(uy + vx)

              dtxzdt = drmudt(i)*(uz + wx)

              dtyydt = drmudt(i)*my_2*vy

              dtyzdt = drmudt(i)*(vz + wy)

              dtzzdt = drmudt(i)*my_2*wz

! Add viscosity contributions

              dvf2dt(i) = dvf2dt(i) + rax*dtxxdt + ray*dtxydt + raz*dtxzdt
              dvf3dt(i) = dvf3dt(i) + rax*dtxydt + ray*dtyydt + raz*dtyzdt
              dvf4dt(i) = dvf4dt(i) + rax*dtxzdt + ray*dtyzdt + raz*dtzzdt

            end do

!           res(2,bnode) = res(2,bnode) - vf2
!           res(3,bnode) = res(3,bnode) - vf3
!           res(4,bnode) = res(4,bnode) - vf4

! Diagonal contributions

          if ( bnode <= nnodes0 ) then

            a(2,2) =  - dvf2dm(bnn)
            a(2,3) =  - dvf2dn(bnn)
            a(2,4) =  - dvf2dl(bnn)
            a(2,5) =  - dvf2dt(bnn)

            a(3,2) =  - dvf3dm(bnn)
            a(3,3) =  - dvf3dn(bnn)
            a(3,4) =  - dvf3dl(bnn)
            a(3,5) =  - dvf3dt(bnn)

            a(4,2) =  - dvf4dm(bnn)
            a(4,3) =  - dvf4dn(bnn)
            a(4,4) =  - dvf4dl(bnn)
            a(4,5) =  - dvf4dt(bnn)

            if ( form_matrix ) then
              idiag = iau(bnode)

              aa(2,2,idiag) = aa(2,2,idiag) + a(2,2)
              aa(2,3,idiag) = aa(2,3,idiag) + a(2,3)
              aa(2,4,idiag) = aa(2,4,idiag) + a(2,4)
              if ( ivisc > 2 ) aa(2,5,idiag) = aa(2,5,idiag) + a(2,5)

              aa(3,2,idiag) = aa(3,2,idiag) + a(3,2)
              aa(3,3,idiag) = aa(3,3,idiag) + a(3,3)
              aa(3,4,idiag) = aa(3,4,idiag) + a(3,4)
              if ( ivisc > 2 ) aa(3,5,idiag) = aa(3,5,idiag) + a(3,5)

              aa(4,2,idiag) = aa(4,2,idiag) + a(4,2)
              aa(4,3,idiag) = aa(4,3,idiag) + a(4,3)
              aa(4,4,idiag) = aa(4,4,idiag) + a(4,4)
              if ( ivisc > 2 ) aa(4,5,idiag) = aa(4,5,idiag) + a(4,5)

            endif

            if ( form_matvec ) then
              do ifcn = 1, nfunctions
                res(2,bnode,ifcn) = res(2,bnode,ifcn)                          &
                                  + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                res(3,bnode,ifcn) = res(3,bnode,ifcn)                          &
                                  + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                res(4,bnode,ifcn) = res(4,bnode,ifcn)                          &
                                  + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)  &
                                  + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)  &
                                  + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                if ( ivisc > 2 ) then
                  res(5,bnode,ifcn) = res(5,bnode,ifcn)                        &
                                    + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)
                endif
              end do
            endif

          end if

! Off-diagonal contributions

          node_loop8 : do i_local = 1, nodes_local

!           local node number

            i = node_map(i_local)

!           global node number

            nodec = c2n_cell(i)

            if (nodec == bnode) cycle node_loop8   ! already did diagonal

!           if (twod) then
!             if (abs(y(nodec)-yplane_2d) > y_coplanar_tol) cycle node_loop8
!           end if

            if ( nodec <= nnodes0 ) then

              a(2,2) =  - dvf2dm(i)
              a(2,3) =  - dvf2dn(i)
              a(2,4) =  - dvf2dl(i)
              a(2,5) =  - dvf2dt(i)

              a(3,2) =  - dvf3dm(i)
              a(3,3) =  - dvf3dn(i)
              a(3,4) =  - dvf3dl(i)
              a(3,5) =  - dvf3dt(i)

              a(4,2) =  - dvf4dm(i)
              a(4,3) =  - dvf4dn(i)
              a(4,4) =  - dvf4dl(i)
              a(4,5) =  - dvf4dt(i)

              if ( form_matrix ) then

! Determine location of nonzero contribution in comp row storage

                ioff = 0

                do k = ia(nodec), ia(nodec+1) - 1
                  column = ja(k)
                  if (column == bnode) ioff = k
                end do

                if (ioff == 0) then
                  write(6,*)'error: no place to put contribution from node ',  &
                           bnode,' to the off diagonal of quad bnode ',nodec
                  stop ! FIXME: should be lmpi_die or se_exit(1)?
                end if

                aa(2,2,ioff) = aa(2,2,ioff) + a(2,2)
                aa(2,3,ioff) = aa(2,3,ioff) + a(2,3)
                aa(2,4,ioff) = aa(2,4,ioff) + a(2,4)
                if ( ivisc > 2 ) aa(2,5,ioff) = aa(2,5,ioff) + a(2,5)

                aa(3,2,ioff) = aa(3,2,ioff) + a(3,2)
                aa(3,3,ioff) = aa(3,3,ioff) + a(3,3)
                aa(3,4,ioff) = aa(3,4,ioff) + a(3,4)
                if ( ivisc > 2 ) aa(3,5,ioff) = aa(3,5,ioff) + a(3,5)

                aa(4,2,ioff) = aa(4,2,ioff) + a(4,2)
                aa(4,3,ioff) = aa(4,3,ioff) + a(4,3)
                aa(4,4,ioff) = aa(4,4,ioff) + a(4,4)
                if ( ivisc > 2 ) aa(4,5,ioff) = aa(4,5,ioff) + a(4,5)

              endif

              if ( form_matvec ) then
                do ifcn = 1, nfunctions
                  res(2,nodec,ifcn) = res(2,nodec,ifcn)                        &
                                    + a(2,2)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,2)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,2)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                  res(3,nodec,ifcn) = res(3,nodec,ifcn)                        &
                                    + a(2,3)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,3)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,3)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                  res(4,nodec,ifcn) = res(4,nodec,ifcn)                        &
                                    + a(2,4)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,4)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,4)*coltag(4,bnode)*rlam(4,bnode,ifcn)

                  if ( ivisc > 2 ) then
                    res(5,nodec,ifcn) = res(5,nodec,ifcn)                      &
                                    + a(2,5)*coltag(2,bnode)*rlam(2,bnode,ifcn)&
                                    + a(3,5)*coltag(3,bnode)*rlam(3,bnode,ifcn)&
                                    + a(4,5)*coltag(4,bnode)*rlam(4,bnode,ifcn)
                  endif
                end do
              endif

            end if

          end do node_loop8

        end do node_loop7

      end do loop_quad_faces

    end if close_off_viscous

    end do boundary_loop

    add_diag_due_to_dirichlet : if ( form_matvec ) then

      node_hit_as_viscous = .false.

      viscous_bcs : do ib = 1, nbound

        if ( .not. bc_strong_viscous_adjoint(bc(ib)%ibc) ) cycle viscous_bcs

        do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)

          local_node1 : if ( inode <= nnodes0 ) then

            if ( .not. node_hit_as_viscous(inode) ) then

              res(2,inode,:) = res(2,inode,:) + rlam(2,inode,:)
              res(3,inode,:) = res(3,inode,:) + rlam(3,inode,:)
              res(4,inode,:) = res(4,inode,:) + rlam(4,inode,:)
              if (ivisc == 6) res(5,inode,:) = res(5,inode,:)+rlam(5,inode,:)

              node_hit_as_viscous(inode) = .true.

            endif ! node_hit_as_viscous

          endif local_node1

        end do

      end do viscous_bcs

      symm_bcs : do ib = 1, nbound

        if ( .not. (bc(ib)%ibc == symmetry_x .or. bc(ib)%ibc == symmetry_y     &
               .or. bc(ib)%ibc == symmetry_z) ) cycle symm_bcs

        eqn = 0
        select case(bc(ib)%ibc)
        case(symmetry_x)
          eqn = n_momx
        case(symmetry_y)
          eqn = n_momy
        case(symmetry_z)
          eqn = n_momz
        end select

        do i = 1, bc(ib)%nbnode

          inode = bc(ib)%ibnode(i)

! Rely on the node_hit_as_viscous flag to tell us if we already treated this
! point in the viscous loop above

          if ( inode <= nnodes0 ) then
            if ( .not. node_hit_as_viscous(inode) ) then
              res(eqn,inode,:) = res(eqn,inode,:) + rlam(eqn,inode,:)
            endif
          endif

        end do

      end do symm_bcs

    endif add_diag_due_to_dirichlet

  end subroutine dvisrhs_bc_mix5

end module residual_bc_visc

!================================= WEIGHTS ===================================80
!
! Weights in least square
!
!=============================================================================80

  pure function weights( dx, dy, dz, rlsq )

    real(dp), intent(in) :: dx, dy, dz

    real(dp), dimension(3,3), intent(in) :: rlsq

    real(dp), dimension(3) :: weights

    real(dp) :: coef1, coef2

  continue

    coef1 =  dy - rlsq(2,1)*dx
    coef2 =  dz - rlsq(3,1)*dx - rlsq(3,2)*coef1

    weights(1) =  rlsq(1,1)*dx    - rlsq(1,3)*coef1 + rlsq(1,2)*coef2
    weights(2) =  rlsq(2,2)*coef1 - rlsq(2,3)*coef2
    weights(3) =  rlsq(3,3)*coef2

  end function weights

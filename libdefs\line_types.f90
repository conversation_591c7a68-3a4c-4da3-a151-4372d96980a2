module line_types

  implicit none

  private

  public :: line_type

  type line_type

    character(22) :: type

    integer :: n_lines ! Number of lines.

    ! Total number of dofs (nodes/cells) in lines.
    integer :: total_dofs

    ! Mapping from line to dof (node/cell) index.
    integer, dimension(:), pointer :: line_to_dof_index

    ! Entry in line_dofs array which is the first dof in each line.
    ! (compressed row storage - dimensioned n_lines + 1)
    integer, dimension(:), pointer :: first_entry

    integer, dimension(:,:), allocatable :: gptr

  end type line_type

end module line_types

module multiblocks

  use nml_multiblock, only : multiblock_tol

  implicit none

  private

  public :: multiblock, find_multiblock_pairs
  public :: n_multiblock_pairs, multiblock_pairs

  integer :: n_multiblock_pairs = 0 ! Number of pairs of interface patches

  integer, dimension(:,:), pointer :: multiblock_pairs ! pairs of patches

  logical :: multiblock = .false. ! Multiblock or not

contains

!=============================== FIND_MULTIBLOCK_PAIRS =======================80
!
!   Establishes matching pairs of points on block interfaces
!
!=============================================================================80
  subroutine find_multiblock_pairs(nbound,bc,x,y,z,nnodes0)

    use bc_types,    only : bcgrid_type
    use allocations, only : my_realloc_ptr
    use lmpi,        only : lmpi_die
    use kinddefs,    only : dp

    integer, intent(in) :: nbound, nnodes0

    real(dp), dimension(:), intent(in) :: x, y, z

    type(bcgrid_type), dimension(nbound), intent(inout) :: bc

    integer :: i, j, k, ib, jb, inode, jnode

    real(dp) :: xj, yj, zj, xk, yk, zk, dist

    logical :: found

  continue

    pair_loop : do i = 1, n_multiblock_pairs

      ib = multiblock_pairs(1,i)
      jb = multiblock_pairs(2,i)

      bc(ib)%interface_boundary = jb
      bc(jb)%interface_boundary = ib

      call my_realloc_ptr(bc(ib)%interface_data, bc(ib)%nbnode)
      call my_realloc_ptr(bc(jb)%interface_data, bc(jb)%nbnode)
      bc(ib)%interface_data(:) = 0
      bc(jb)%interface_data(:) = 0

      j_loop : do j = 1, bc(ib)%nbnode
        inode = bc(ib)%ibnode(j)
        if ( inode > nnodes0 ) cycle j_loop
        xj = x(inode)
        yj = y(inode)
        zj = z(inode)
        found = .false.
        k_loop : do k = 1, bc(jb)%nbnode
          jnode = bc(jb)%ibnode(k)
          xk = x(jnode)
          yk = y(jnode)
          zk = z(jnode)
          dist = sqrt ( (xj-xk)**2 + (yj-yk)**2 + (zj-zk)**2 )
          if ( dist > multiblock_tol ) cycle k_loop
          found = .true.
          bc(ib)%interface_data(j) = k
          bc(jb)%interface_data(k) = j
          exit k_loop
        end do k_loop
        if ( .not. found ) then
          write(*,*) 'Problem matching node on interface.',ib,jb
          call lmpi_die
          stop
        endif
      end do j_loop

      do j = 1, bc(ib)%nbnode
        if ( bc(ib)%ibnode(j) > nnodes0 ) cycle
        if ( bc(ib)%interface_data(j) == 0 ) then
          write(*,*) 'Empty entry in interface_data.', ib, j
          call lmpi_die
          stop
        endif
        if ( bc(ib)%interface_data(j) > nnodes0 ) then
          write(*,*) 'Off-processor entry in interface_data.', ib, j
          call lmpi_die
          stop
        endif
      end do

      do j = 1, bc(jb)%nbnode
        if ( bc(jb)%ibnode(j) > nnodes0 ) cycle
        if ( bc(jb)%interface_data(j) == 0 ) then
          write(*,*) 'Empty entry in interface_data.', jb, j
          call lmpi_die
          stop
        endif
        if ( bc(jb)%interface_data(j) > nnodes0 ) then
          write(*,*) 'Off-processor entry in interface_data.', jb, j
          call lmpi_die
          stop
        endif
      end do

    end do pair_loop

  end subroutine find_multiblock_pairs

end module multiblocks

module solution_getg

  use kinddefs,        only : dp

  implicit none

  private

  public :: set_up_getg, getg_type, edge_stencil_type

  type getg_type
    integer :: ntp ! Number of time-planes to store in drdxl

    real(dp), pointer, dimension(:,:,:)      :: dvdx  ! dvol/dgrid (moving grid)
    real(dp), pointer, dimension(:,:,:)      :: dfdx  ! dcost/dgrid
    real(dp), pointer, dimension(:,:,:,:)    :: drdxl ! dR/dgrid*lambdaf
    real(dp), pointer, dimension(:,:,:)      :: sourceterm_sum ! Contributions
                                                               ! to stored xyz
                                                               ! linearization
                                                               ! arising from
                                                               ! SA source term
                                                               ! (for grid
                                                               ! adjoint)
    real(dp), pointer, dimension(:,:,:)      :: overset_terms ! Overset contribs
  end type getg_type

  type edge_stencil_type
    integer :: n

    integer, dimension(:), pointer :: nodes

    real(dp), dimension(:),   pointer :: dxndx,dxndy,dxndz
    real(dp), dimension(:),   pointer :: dyndx,dyndy,dyndz
    real(dp), dimension(:),   pointer :: dzndx,dzndy,dzndz
    real(dp), dimension(:,:), pointer :: dwhatdx,dwhatdy,dwhatdz
    real(dp), dimension(:,:), pointer :: dwdx,dwdy,dwdz
  end type edge_stencil_type

contains


!================================ SET_UP_GETG=================================80
!
!   Allocate variables specific to getgrad
!
!=============================================================================80

  subroutine set_up_getg(grid,crow,getg,design)

    use info_depr,            only : ivisc
    use nml_nonlinear_solves, only : itime
    use nml_global,           only : moving_grid
    use lmpi,                 only : lmpi_die
    use grid_types,           only : grid_type
    use comprow_types,        only : crow_type
    use allocations,          only : my_alloc_ptr
    use design_types,         only : design_type
    use nml_overset_data,     only : overset_flag
    use adjoint_switches,     only : get_dldx, show_surface_sensitivity

    type(grid_type),   intent(in)    :: grid
    type(crow_type),   intent(in)    :: crow
    type(getg_type),   intent(inout) :: getg
    type(design_type), intent(in)    :: design

  continue

    getg%ntp = 1

    if ( moving_grid ) then
      how_many_timeplanes : select case(itime)
      case(1)
        getg%ntp = 2
      case(2)
        getg%ntp = 3
      case(3)
        getg%ntp = 4
      case default
        write(*,*) 'Invalid value of itime in set_up_getg',itime
        call lmpi_die
      end select how_many_timeplanes
    endif

    shape_alloc : if (design%shape_active .or. design%rigid_active .or.        &
                      design%kinematic%active .or. design%trimming%active .or. &
                      get_dldx .or. show_surface_sensitivity) then

      call my_alloc_ptr(getg%dvdx, 3, crow%nnz01, design%nfunctions)
      call my_alloc_ptr(getg%dfdx, 3, crow%nnz01, design%nfunctions)
      call my_alloc_ptr(getg%drdxl,3, grid%nnodes01, design%nfunctions,        &
                        getg%ntp)

      if ( ivisc >= 6 ) then
        call my_alloc_ptr(getg%sourceterm_sum, 3, grid%nnodes01,               &
                          design%nfunctions)
      else
        call my_alloc_ptr(getg%sourceterm_sum, 1, 1, 1)
      endif

      if ( overset_flag ) then
        call my_alloc_ptr(getg%overset_terms,3,grid%nnodes01,design%nfunctions)
      else
        call my_alloc_ptr(getg%overset_terms,1,1,1)
      endif

    else
      call my_alloc_ptr(getg%dvdx,      1, 1, 1)
      call my_alloc_ptr(getg%dfdx,      1, 1, 1)
      call my_alloc_ptr(getg%drdxl,     1, 1, 1, 1)
      call my_alloc_ptr(getg%sourceterm_sum, 1, 1, 1)
      call my_alloc_ptr(getg%overset_terms, 1, 1, 1)
    endif shape_alloc

  end subroutine set_up_getg

end module solution_getg

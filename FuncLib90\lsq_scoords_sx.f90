!================================= LSQ_SCOORDS_SX ============================80
!
! Coordinates in least square - symmetry_x
!
!=============================================================================80

  pure function lsq_scoords_sx( lsq_mrefs, tf, lc_max, dx, xc, yc, zc, slenc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type),     intent(in) :: lsq_mrefs
    real(dp), dimension(4), intent(in) :: lc_max
    real(dp),               intent(in) :: tf, dx, xc, yc, zc, slenc

    real(dp), dimension(4) :: lsq_scoords_sx, lc

    real(dp) :: xs, ys, zs, slens

  continue

    xs = xc + dx
    ys = yc
    zs = zc
    slens = slenc

    lc = lsq_coords( lsq_mrefs, tf, xs, ys, zs, slens )

    lsq_scoords_sx = lsq_scoords( lc, lc_max )

  end function lsq_scoords_sx

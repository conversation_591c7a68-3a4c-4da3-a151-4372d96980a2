!   vim: set filetype=fortran:
! emacs: -*- f90 -*-

test_suite load_balance

test global2local_order
 type(migrator_type) :: migrator

 migrator%new_nnodes01 = 2
 allocate(migrator%new_l2g(migrator%new_nnodes01))
 allocate(migrator%sorted_global(migrator%new_nnodes01))
 allocate(migrator%sorted_local(migrator%new_nnodes01))
 allocate(migrator%source_part(migrator%new_nnodes01))

 migrator%new_l2g(1) = 10
 migrator%new_l2g(2) = 20
 migrator%sorted_global(1) = 10
 migrator%sorted_global(2) = 20
 migrator%sorted_local(1) = 1
 migrator%sorted_local(2) = 2
 migrator%source_part(1) = 100
 migrator%source_part(2) = 200

 assert_equal(1,migrated_local(migrator,10,100))
 assert_equal(2,migrated_local(migrator,20,200))

 assert_equal(3,migrated_local(migrator,25,250))
 assert_equal(4,migrated_local(migrator, 5, 50))
 assert_equal(5,migrated_local(migrator,15,150))

 assert_equal(1,migrated_local(migrator,10,100))
 assert_equal(2,migrated_local(migrator,20,200))
 assert_equal(3,migrated_local(migrator,25,250))
 assert_equal(4,migrated_local(migrator, 5, 50))
 assert_equal(5,migrated_local(migrator,15,150))

 assert_equal(5, migrator%new_nnodes01)

 assert_equal(10, migrator%new_l2g(1))
 assert_equal(20, migrator%new_l2g(2))
 assert_equal(25, migrator%new_l2g(3))
 assert_equal( 5, migrator%new_l2g(4))
 assert_equal(15, migrator%new_l2g(5))

 assert_equal( 5, migrator%sorted_global(1))
 assert_equal(10, migrator%sorted_global(2))
 assert_equal(15, migrator%sorted_global(3))
 assert_equal(20, migrator%sorted_global(4))
 assert_equal(25, migrator%sorted_global(5))

 assert_equal( 4, migrator%sorted_local(1))
 assert_equal( 1, migrator%sorted_local(2))
 assert_equal( 5, migrator%sorted_local(3))
 assert_equal( 2, migrator%sorted_local(4))
 assert_equal( 3, migrator%sorted_local(5))

 assert_equal(100, migrator%source_part(1))
 assert_equal(200, migrator%source_part(2))
 assert_equal(250, migrator%source_part(3))
 assert_equal( 50, migrator%source_part(4))
 assert_equal(150, migrator%source_part(5))

 deallocate(migrator%new_l2g)
 deallocate(migrator%sorted_global)
 deallocate(migrator%sorted_local)
end test

test global2local_reverse
 type(migrator_type) :: migrator

 migrator%new_nnodes01 = 2
 allocate(migrator%new_l2g(migrator%new_nnodes01))
 allocate(migrator%sorted_global(migrator%new_nnodes01))
 allocate(migrator%sorted_local(migrator%new_nnodes01))
 allocate(migrator%source_part(migrator%new_nnodes01))

 migrator%new_l2g(1) = 20
 migrator%new_l2g(2) = 10
 migrator%sorted_global(1) = 10
 migrator%sorted_global(2) = 20
 migrator%sorted_local(1) = 2
 migrator%sorted_local(2) = 1
 migrator%source_part(1) = 200
 migrator%source_part(2) = 100

 assert_equal(2,migrated_local(migrator,10,100))
 assert_equal(1,migrated_local(migrator,20,200))

 assert_equal(3,migrated_local(migrator,25,250))

 assert_equal(2,migrated_local(migrator,10,100))
 assert_equal(1,migrated_local(migrator,20,200))
 assert_equal(3,migrated_local(migrator,25,250))

 assert_equal(20, migrator%new_l2g(1))
 assert_equal(10, migrator%new_l2g(2))
 assert_equal(25, migrator%new_l2g(3))

 assert_equal(10, migrator%sorted_global(1))
 assert_equal(20, migrator%sorted_global(2))
 assert_equal(25, migrator%sorted_global(3))

 assert_equal( 2, migrator%sorted_local(1))
 assert_equal( 1, migrator%sorted_local(2))
 assert_equal( 3, migrator%sorted_local(3))

 assert_equal(200, migrator%source_part(1))
 assert_equal(100, migrator%source_part(2))
 assert_equal(250, migrator%source_part(3))

 deallocate(migrator%new_l2g)
 deallocate(migrator%sorted_global)
 deallocate(migrator%sorted_local)
end test

end test_suite

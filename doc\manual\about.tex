\section*{About this Document\label{c:about}}

This manual is intended to guide an application engineer through configuration,
compiling, installing, and executing the \FunThreeD simulation package.
The focus is on the most commonly exercised capabilities.
Therefore, some of the immature or rarely exercised capabilities
are intentionally omitted in the interest of clarity.
An accompanying document that provides example cases
is under development.

Release of the generic gas capability is restricted because of
International Traffic in Arms Regulations (ITAR),
so \FunThreeD usually distributed with the generic gas capability
disabled. See \sectionref{s:obtaining} for details.
This manual describes \FunThreeD with and without
the generic gas capability, denoted \cmd{eqn_type='generic'}.
Features that are specific to an \cmd{eqn_type} are explicitly
indicated.

This document is updated and released with
each subsequent version of \FunThreeD. 
In fact, a significant portion is automatically 
extracted from the \FunThreeD source code.
If you have difficulties, find any errors,
or have any suggestions for improvement please
contact the authors at\\
\\
\funsupport
\\
\\
We would like to hear from you.

#include <stdio.h>
#include <stdlib.h>
#ifndef __APPLE__
#include <malloc.h>
#endif

extern double fitsegmentLS (
         long            *rorder,
         long            *rncontrol,
         long             param,
         long             npts,
         double          *xcoord,
         double          *ycoord,
         long            *nknot,
         double         **knots,
         double         **xcontrol,
         double         **ycontrol,
         double          *segtval);

int main(int argc, char *argv[])
{
  int    i;
  int    el,seg;
  FILE   *fp;
  int    nelement;
  int    nsegment;
  long   npts;
  double *xcoord;
  double *ycoord;
  long   nknot;
  double *knots;
  double *xcontrol;
  double *ycontrol;
  double *segtval;
  double fit;
  long   rorder=4;
  long   rncontrol=20;
  long   param=1;

  if((fp=fopen("sine.geom","r")) == NULL ) {
    fprintf(stderr,"File not found\n");
    exit(1);
  }

  fscanf(fp,"%d",&nelement);
  fprintf(stderr,"%d Element(s)\n",nelement);

  for( el=0; el<nelement; el++ ) {
    fscanf(fp,"%d",&nsegment);
    fprintf(stderr,"Element %d has %d Segment(s)\n",el,nsegment);

    for( seg=0; seg<nsegment; seg++ ) {
      fscanf(fp,"%d",&npts);
      fprintf(stderr,"Element %d, Segment %d has %d Points\n",el,seg,npts);

      if( (xcoord=(double *)malloc(npts*sizeof(double))) == NULL) {
        fprintf(stderr,"Memory error\n");
        exit(1);
      }

      if( (ycoord=(double *)malloc(npts*sizeof(double))) == NULL) {
        fprintf(stderr,"Memory error\n");
        exit(1);
      }

      if( (segtval=(double *)malloc(npts*sizeof(double))) == NULL) {
        fprintf(stderr,"Memory error\n");
        exit(1);
      }

      for( i=0; i<npts; i++) {
        fscanf(fp,"%lg %lg",&xcoord[i],&ycoord[i]);
fprintf(stdout,"X[%d] = %g, Y[%d] = %g\n",i,xcoord[i],i,ycoord[i]);
      }

      fit = fitsegmentLS(&rorder, &rncontrol, param, npts, xcoord, ycoord, &nknot, &knots, &xcontrol, &ycontrol, segtval);

      fprintf(stderr,"Element %d, Segment %d fit with %g error\n",el,seg,fit);

      free(segtval);
      free(ycoord);
      free(xcoord);
    }
  }
}

module scratch_q

  use kinddefs,         only : dp
  use solution_types,   only : soln_type
  use lmpi,             only : lmpi_master, lmpi_conditional_stop

  implicit none

  private

  public :: set_temp_q, recover_q
  public :: set_tempx_q, max_error_temps
  public :: scratch_q_flag
  public :: realify_q, realify_scratch_q

  type q_type

    real(dp), dimension(:,:), allocatable :: q
    real(dp), dimension(:,:), allocatable :: t

  end type q_type

  type(q_type), dimension(:,:), allocatable :: temp

  logical, save :: first_time_through = .true.

  type(q_type), dimension(:), allocatable :: tempx

  logical, save :: first_time_throughx = .true.

contains

!=============================== SET_TEMP_Q ==================================80
!
!  Set temp q array.
!
!=============================================================================80
  subroutine set_temp_q( k, cc, flag, fl, soln, site )

    use info_depr,      only : ngrid
    use fun3d_maximums, only : ngrid_max

    logical,          intent(in) :: cc
    integer,          intent(in) :: k, flag, fl
    type(soln_type),  intent(in) :: soln
    character(len=*), intent(in) :: site

    integer :: size1, size2, size3, size5, size6

    logical, dimension(ngrid_max,2), save :: first_time = .true.

    logical :: debug = .false.

  continue

    if ( first_time_through ) then
      allocate(temp(ngrid,2))
      first_time_through = .false.
    endif

    size1 = soln%n_q - soln%n_turb
    size2 = size( soln%q_dof, 2 )
    size3 =            soln%n_turb
    size5 = size1+1
    size6 = size1+size3 !=soln%n_q

    if ( first_time(fl,k) ) then
      allocate( temp(fl,k)%q( size1, size2 ) )
      allocate( temp(fl,k)%t( size3, size2 ) )
      first_time(fl,k) = .false.
    endif

    if ( flag == 1 ) then

      temp(fl,k)%q(1:size1,:) = soln%q_dof(1:size1,:)

      if ( debug .and. lmpi_master ) &
      write(*,"(1x,2a)") 'Conservative->Primitive : ',site

    elseif ( flag == 2 ) then

      if ( cc ) then
        temp(fl,k)%t(1:size3,:) = soln%q_dof(size5:size6,:)
      else
        temp(fl,k)%t(1:size3,:) = soln%turb(1:size3,:)
      endif

      if ( debug .and. lmpi_master ) &
      write(*,"(1x,2a)") 'T:Stored : ',site

    elseif ( flag == 3 ) then

      temp(fl,k)%q(1:size1,:) = soln%q_dof(1:size1,:)
      if ( cc ) then
        temp(fl,k)%t(1:size3,:) = soln%q_dof(size5:size6,:)
      else
        temp(fl,k)%t(1:size3,:) = soln%turb(1:size3,:)
      endif

      if ( debug .and. lmpi_master ) &
      write(*,"(1x,2a)") 'Q+T:Stored : ',site


    else

      if ( lmpi_master ) write(*,*) ' flag=',flag,' site=',trim(site),' k=',k
      call lmpi_conditional_stop(1,' Flag error:set_temp_q')

    endif

  end subroutine set_temp_q

!=============================== RECOVER_Q ===================================80
!
!  Recover q.
!
!=============================================================================80
  subroutine recover_q( k, cc, flag, fl, soln, site )

    logical,         intent(in)    :: cc
    integer,         intent(in)    :: k, flag, fl
    type(soln_type), intent(inout) :: soln

    character(len=*), intent(in), optional :: site

    integer :: size1, size3, size5, size6

  continue

    size1 = soln%n_q - soln%n_turb
    size3 =            soln%n_turb
    size5 = size1+1
    size6 = size1+size3 !=soln%n_q

    if ( flag == 1 ) then

      soln%q_dof(1:size1,:) = temp(fl,k)%q(1:size1,:)

    elseif ( flag == 2 ) then

      if ( cc ) then
        soln%q_dof(size5:size6,:) = temp(fl,k)%t(1:size3,:)
      else
        soln%turb(     1:size3,:) = temp(fl,k)%t(1:size3,:)
      endif

    elseif ( flag == 3 ) then

      soln%q_dof(1:size1,:) = temp(fl,k)%q(1:size1,:)

      if ( cc ) then
        soln%q_dof(size5:size6,:) = temp(fl,k)%t(1:size3,:)
      else
        soln%turb(     1:size3,:) = temp(fl,k)%t(1:size3,:)
      endif

    else

      if ( lmpi_master ) write(*,*) ' flag=',flag,' k=',k
      if ( present(site) ) then
        if ( lmpi_master ) write(*,*) ' site=',trim(site)
      endif
      call lmpi_conditional_stop(1,'flag error:recover_q')

    endif

  end subroutine recover_q

!=============================== SET_TEMPX_Q =================================80
!
!  Set tempx q array.
!
!=============================================================================80
  subroutine set_tempx_q( fl, soln, site )

    use info_depr,      only : ngrid
    use fun3d_maximums, only : ngrid_max

    integer,          intent(in) :: fl
    type(soln_type),  intent(in) :: soln
    character(len=*), intent(in) :: site

    integer :: size1, size2

    logical, save :: first_time(ngrid_max) = .true.

  continue

    if ( first_time_throughx ) then
      allocate(tempx(ngrid))
      first_time_throughx = .false.
    endif

    size1 = soln%n_q - soln%n_turb
    size2 = size( soln%q_dof, 2 )

    if ( first_time(fl) ) then
      allocate( tempx(fl)%q( size1, size2 ) )
      first_time(fl) = .false.
    endif

    tempx(fl)%q(1:size1,:) = soln%q_dof(1:size1,:)

    if ( lmpi_master ) write(*,"(1x,2a)") 'Setting tempx%q : ',site

  end subroutine set_tempx_q

!=============================== MAX_ERROR_TEMPS =============================80
!
!  Recover q.
!
!=============================================================================80
  subroutine max_error_temps( k, fl )

    use lmpi, only : lmpi_max_and_maxid, lmpi_bcast

    integer, intent(in) :: k, fl

    integer :: size1, size2, i, j, maxid, ierr

    integer,  dimension(5) :: jmax
    real(dp), dimension(5) :: error, max_error, q1, q2

  continue

    size1 = size( temp(fl,k)%q, 1 )
    size2 = size( temp(fl,k)%q, 2 )

    ierr = size(  tempx(fl)%q, 1 ) - size1
    call lmpi_conditional_stop(ierr,' Error in size1:max_error_temps')
    ierr = size(  tempx(fl)%q, 2 ) - size2
    call lmpi_conditional_stop(ierr,' Error in size2:max_error_temps')


    jmax(:)      = 0
    max_error(:) = -huge(1._dp)
    do i=1,size1
      do j=1,size2
        error(i) = temp(fl,k)%q(i,j) - tempx(fl)%q(i,j)
        if ( abs( error(i) ) < max_error(i) ) cycle
        max_error(i) = abs( error(i) )
        jmax(i)      = j
      enddo
    enddo

    do i=1,size1
      call lmpi_max_and_maxid( real(max_error(i),dp), maxid )
      call lmpi_bcast( max_error(i), maxid )
      q1(i) = tempx(fl)%q(i,jmax(i))
      call lmpi_bcast( q1(i), maxid )
      q2(i) =  temp(fl,k)%q(i,jmax(i))
      call lmpi_bcast( q2(i), maxid )
    enddo
    if ( lmpi_master ) then
      write(*,"(1x,a,5e22.14)") ' max_error=',max_error
      write(*,"(1x,a,5e22.14)") '        q1=',q1
      write(*,"(1x,a,5e22.14)") '        q2=',q2
      write(*,"(1x,a,5e22.14)") '     q2-q1=',q2-q1
    endif

  end subroutine max_error_temps

!=============================== SCRATCH_Q_FLAG ==============================80
!
!  Set scratch_q_flag
!
!=============================================================================80
  pure function scratch_q_flag( qset, tightly_couple )

    logical, intent(in) :: tightly_couple
    integer, intent(in) :: qset
    integer             :: scratch_q_flag

  continue

    scratch_q_flag = -10

    if ( qset == 1 .and. .not.tightly_couple ) then

      scratch_q_flag = 1

    elseif ( qset == 2 ) then

      scratch_q_flag = 2

    elseif ( qset == 1 .and. tightly_couple ) then

      scratch_q_flag = 3

    endif

  end function scratch_q_flag

!=============================== REALIFY_Q ===================================80
!
!  Recover real part of q for complex path.
!
!=============================================================================80
  subroutine realify_q( cc, flag, soln )

    logical,         intent(in)    :: cc
    integer,         intent(in)    :: flag
    type(soln_type), intent(inout) :: soln

  continue

    if ( flag == 1 ) then

      soln%q_dof(:,:) = real( soln%q_dof(:,:), dp )

    elseif ( flag == 2 ) then

      if ( cc ) then
        soln%q_dof(:,:) = real( soln%q_dof(:,:), dp )
      else
        soln%turb(:,:)  = real(  soln%turb(:,:), dp )
      endif

    elseif ( flag == 3 ) then

      soln%q_dof(:,:) =  real( soln%q_dof(:,:), dp )

      if ( .not.cc ) then
        soln%turb(:,:) = real(  soln%turb(:,:), dp )
      endif

    else

      if ( lmpi_master ) write(*,*) ' flag=',flag
      call lmpi_conditional_stop(1,'flag error:realify_q')

    endif

  end subroutine realify_q

!=============================== REALIFY_SCRATCH_Q ===========================80
!
!  Recover real part of q for complex path.
!
!=============================================================================80
  subroutine realify_scratch_q( k, cc, flag, fl )

    logical,         intent(in) :: cc
    integer,         intent(in) :: k, flag, fl

  continue

    if ( flag == 1 ) then

      temp(fl,k)%q(:,:) = real( temp(fl,k)%q(:,:), dp )

    elseif ( flag == 2 ) then

      if ( cc ) then
        temp(fl,k)%q(:,:) = real( temp(fl,k)%q(:,:), dp )
      else
        temp(fl,k)%t(:,:)  = real(  temp(fl,k)%t(:,:), dp )
      endif

    elseif ( flag == 3 ) then

      temp(fl,k)%q(:,:) =  real( temp(fl,k)%q(:,:), dp )

      if ( .not.cc ) then
        temp(fl,k)%t(:,:) = real(  temp(fl,k)%t(:,:), dp )
      endif

    else

      if ( lmpi_master ) write(*,*) ' flag=',flag
      call lmpi_conditional_stop(1,'flag error:realify_scaratch_q')

    endif

  end subroutine realify_scratch_q

end module scratch_q

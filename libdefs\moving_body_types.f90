! Definitions pertaining to moving bodies in dynamic simulations

module moving_body_types

  use kinddefs,    only : dp
  use force_types, only : force_type

  implicit none

  private

  public :: rotation_vector_type, translation_vector_type
  public :: moving_body_type, moving_body, observer
  public :: time_varying_surface_type, time_varying_surface
  public :: specified_rigid_motion_type, specified_rigid_motion
  public :: trim_type

  type rotation_vector_type
    real(dp) :: theta      ! rotation angle from t=0 position (rad)
    real(dp) :: dthetadt   ! rate-of-change of rotation angle
    real(dp) :: tx         ! x-component of unit rotation vector
    real(dp) :: ty         ! y-component of unit rotation vector
    real(dp) :: tz         ! z-component of unit rotation vector
    real(dp) :: xorigin    ! current x-origin of unit rotation vector
    real(dp) :: yorigin    ! current y-origin of unit rotation vector
    real(dp) :: zorigin    ! current z-origin of unit rotation vector
    real(dp) :: start_time ! time at which rotation starts
    real(dp) :: duration   ! how long rotation lasts
  end type rotation_vector_type

  type translation_vector_type
    real(dp) :: ds         ! displacement from t=0 position
    real(dp) :: dsdt       ! rate of change in displacement
    real(dp) :: sx         ! x-component of unit displacement vector
    real(dp) :: sy         ! y-component of unit displacement vector
    real(dp) :: sz         ! z-component of unit displacement vector
    real(dp) :: start_time ! time at which translation starts
    real(dp) :: duration   ! how long translation lasts
  end type translation_vector_type

  type moving_body_type
    character(len=80)               :: body_name        ! identifying name
    character(len=80)               :: parent_name      ! parent name
    integer                         :: n_ancestors      ! no. ancestors
    integer,  dimension(:), pointer :: ancestors        ! ancestor list
    integer                         :: n_descendants    ! no. offspring
    integer,  dimension(:), pointer :: descendants      ! offspring list
    integer,  dimension(:), pointer :: generation       ! family tree level
    character(len=80)               :: mesh_movement    ! rigid or deform
    character(len=80)               :: motion_driver    ! what's in control
    real(dp)                        :: xcg, ycg, zcg    ! CG coords
    real(dp)                        :: mass             ! body mass
    real(dp)                        :: i_xx             ! Ixx mom. of inertia
    real(dp)                        :: i_yy             ! Iyy mom. of inertia
    real(dp)                        :: i_zz             ! Izz mom. of inertia
    real(dp)                        :: i_xy             ! Ixy mom. of inertia
    real(dp)                        :: i_xz             ! Ixz mom. of inertia
    real(dp)                        :: i_yz             ! Iyz mom. of inertia
    real(dp)                        :: xmc, ymc, zmc    ! moment center
    real(dp)                        :: sref, cref, bref ! ref. geom data
    integer                         :: move_mc          ! fixed/moving
    integer                         :: translate        ! forced trans flag
    real(dp)                        :: transrate        ! forced trans rate
    real(dp)                        :: rfreq_trans      ! forced trans freq
    type(translation_vector_type)   :: translation_vector ! see above
    integer                         :: rotate           ! forced rotat flag
    real(dp)                        :: rotrate          ! forced rotat rate
    real(dp)                        :: rfreq_rotate     ! forced rotat freq
    real(dp)                        :: rotation_phase   ! phase shift trig fcn
    real(dp)                        :: rotation_tphase  ! phase shift T angle
    real(dp)                        :: translation_phase  ! phase shift trig fcn
    real(dp)                        :: translation_tphase ! phase shift T angle
    type(rotation_vector_type)      :: rotation_vector  ! see above
    integer                         :: n_defining_bndry ! no. body bndrys
    integer,  dimension(:), pointer :: defining_bndry   ! body bndry list
    real(dp), dimension(4,4)        :: transform_matrix ! coord. transform
    real(dp), dimension(4,4)        :: transform_matrixatn  ! at t=n
    real(dp), dimension(4,4)        :: transform_matrixatn1 ! at t=n-1
    real(dp), dimension(4,4)        :: transform_matrixatn2 ! at t=n-2
    real(dp), dimension(4,4)        :: transform_matrixatn3 ! at t=n-3
    real(dp), dimension(4,4)        :: transform_matrixatn4 ! at t=n-4
    real(dp), dimension(4,4)        :: inv_transform       ! inverse transform
    real(dp), dimension(4,4)        :: slice_transform     ! for slicing
    real(dp), dimension(4,4)        :: inv_slice_transform ! slicing inverse
    real(dp), dimension(4,4)        :: slice_transform_0     ! for slicing
    real(dp), dimension(4,4)        :: inv_slice_transform_0 ! slicing inverse
    real(dp), dimension(4,4)        :: static_transform    ! t0 grid positioning
    real(dp), dimension(4,4)        :: inv_static_transform ! static inverse
    real(dp), dimension(3)          :: body_lin_vel     ! linear vel. vector
    real(dp), dimension(3)          :: body_ang_vel     ! angular vel. vector
    real(dp), dimension(3)          :: euler_angles     ! euler angles
    type(force_type), dimension(:), pointer :: bcforce  ! boundary force/mom.
    type(force_type)                        :: totforce ! total force/mom.
    integer                                 :: nmodes   ! number of modes
    real(dp), dimension(:,:,:),     pointer :: ae_hist  ! modal history
    logical                         :: fake_body        ! duplicate for
                                                        ! specialized parent-
                                                        ! child motion
    character(len=80)               :: trim_control     ! type of trim control
                                                        ! applied to this body
    real(dp)                        :: baseline_psi     ! starting azimuth for
                                                        ! this body (degrees)
    integer                         :: steps_per_period ! Number of timesteps
                                                        ! per period for this
                                                        ! body
    integer                         :: n_trim_targets   ! number of trim targets
    type(trim_type),  dimension(:), pointer :: trim_target ! holds variables to
                                                           ! trim to, and how
    real(dp), dimension(:,:), pointer :: trim_jacobian     ! array of trim
                                                           ! sensitivities
    real(dp), dimension(:,:), pointer :: inv_trim_jacobian ! the inverse
  end type moving_body_type

  type time_varying_surface_type
    character(len=80)                 :: body_name     ! identifying name
    integer                           :: n_time_slices ! no. pts in time
    real(dp), dimension(:),   pointer :: time_slice    ! list of time values
    real(dp)                          :: repeat_time   ! time to repeat
    integer                           :: itotal        ! no. pts on surface
    real(dp), dimension(:,:), pointer :: xsurf         ! space-time x-coords
    real(dp), dimension(:,:), pointer :: ysurf         ! space-time y-coords
    real(dp), dimension(:,:), pointer :: zsurf         ! space-time z-coords
    integer,  dimension(:),   pointer :: inodemt
  end type time_varying_surface_type

  type specified_rigid_motion_type
    character(len=80)                   :: body_name     ! identifying name
    integer                             :: n_time_slices ! no. pts in time
    real(dp), dimension(:),     pointer :: time_slice    ! list of time values
    real(dp), dimension(:),     pointer :: xcg           ! x-coord of CG
    real(dp), dimension(:),     pointer :: ycg           ! y-coord of CG
    real(dp), dimension(:),     pointer :: zcg           ! z-coord of CG
    real(dp)                            :: repeat_time   ! time to repeat
    real(dp), dimension(:,:,:), pointer :: transforms    ! 4 x 4 x n_time_slices
    real(dp), dimension(:,:,:), pointer :: inverse_transforms
  end type specified_rigid_motion_type

  type trim_type
    character(len=80)  :: trim_variable        ! trim this variable...
    real(dp)           :: trim_value           ! to match this value...
    integer            :: body_used_for_trim   ! by moving this body...
    character(len=80)  :: motion_used_for_trim ! in this manner...
    integer            :: mode_used_for_trim   ! by using this mode... (AE only)
    real(dp)           :: per_step_limit       ! max movement per trim attempt
    integer            :: update_freq          ! how frequently to update
  end type trim_type

  type(moving_body_type),            dimension(:), allocatable :: moving_body
  type(moving_body_type)                                       :: observer
  type(time_varying_surface_type),   dimension(:), allocatable ::              &
                                                         time_varying_surface
  type(specified_rigid_motion_type), dimension(:), allocatable ::              &
                                                         specified_rigid_motion

end module moving_body_types

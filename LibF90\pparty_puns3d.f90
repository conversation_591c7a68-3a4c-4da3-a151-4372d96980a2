! Routines to create unstructured mesh data for FUN3D in parallel

module pparty_puns3d

  use timings,        only : timing, timer
  use kinddefs,       only : dp, system_i8
  use lmpi,           only : lmpi_nproc, lmpi_id, lmpi_bcast, lmpi_reduce,     &
                             lmpi_gather, lmpi_gatherv, lmpi_master,           &
                             lmpi_conditional_stop, lmpi_min, lmpi_max
  use complex_functions, only : o
  implicit none

  private

  public :: puns3d_bc, pparty_puns3d_bc_sm, euler_number, raw_grid_checker
  public :: pparty_puns3d_bc_cc_sm

  public :: verbose
  logical :: verbose = .false.

contains

!==================================== PUNS3D_BC ==============================80
!
! Driver routine to create unstructured mesh data for FUN3D/HEFSS
!
!=============================================================================80

  subroutine puns3d_bc(flow_dir, raw_grid_data, global_grid)

    use puns3d_io_c2n, only : puns3d_read_fast_bc,                             &
                              puns3d_read_vgrid_bc,                            &
                              puns3d_read_felisa_bc,                           &
                              puns3d_read_aflr3_bc,                            &
                              puns3d_read_fieldview_c2n

    use info_depr,     only : mirror_x, mirror_y, mirror_z
    use grid_types,    only : grid_type, raw_grid_data_type
    use puns3d_io_c2n, only : lump_boundaries

    character(*), intent(in) :: flow_dir

    type(raw_grid_data_type), intent(in)    :: raw_grid_data
    type(grid_type),          intent(inout) :: global_grid

  continue

    select case (trim(raw_grid_data%grid_format))

      case ('aflr3')
       call puns3d_read_aflr3_bc(flow_dir,raw_grid_data%data_format,global_grid)

      case ('fieldview')
        call puns3d_read_fieldview_c2n(2,flow_dir,global_grid,raw_grid_data)

      case ('fast')
        call puns3d_read_fast_bc(flow_dir,global_grid,raw_grid_data%data_format)

      case ('vgrid')
        call puns3d_read_vgrid_bc(flow_dir,global_grid)

       case ('felisa')
         call puns3d_read_felisa_bc(flow_dir,global_grid)

       case ('fun2d')
         ! already done in puns3d_read_fun2d_c2n

       case default
         write(*,*)'pparty_puns3d.f90:puns3d; grid_format NOT supported.'
         call lmpi_conditional_stop(1)
    end select

    if (lmpi_master.and.timing) call timer('... puns3d_read_fast_tags')

    if (.not.((mirror_x).or.(mirror_y).or.(mirror_z)))                        &
       call lump_boundaries(raw_grid_data,global_grid)

    if (lmpi_master.and.timing) call timer('... puns3d_process_grid')

  end subroutine puns3d_bc

!================================ pparty_puns3d_bc_sm ========================80
!
! Checks cell for negative volume and swaps if needed (c2n may change).
! Checks boundary face handedness and swaps if needed.
! In "finder", constructs boundary nodes; and sets f2ntb,f2nqb to index
! into ibnode.
!
! Upto this point, node numbers have been kept global to avoid the overhead
! of mapping.  However, the gridmetrics and mechanics throughout this routine
! made it more efficient to use local numbering.  So, eptr, c2n, faces, etc.
! are mapped to l2g.  Then mapped back at the end.
!
!=============================================================================80

  subroutine pparty_puns3d_bc_sm(grid)

    use grid_types,     only : grid_type
    use info_depr,      only : make_tets
    use metis_defs,     only : partition_lines
    use sort,           only : heap_sort, binary_search
    use puns3d_checker, only : chkgrd, chkbnd
    use allocations,    only : my_alloc_ptr

    type(grid_type), intent(inout) :: grid

    integer :: ibound, ielem
    integer :: i, j, ierr
    integer :: icount1, nbnode

    integer, dimension(:), allocatable :: t1,t1_ind,mark
    integer, dimension(:), allocatable :: counts, itemp

    real(dp), dimension(:), allocatable :: tempx

    integer                                :: nloc             ! Dimension of
                                                               ! locs array
    integer,     dimension(:),   pointer   :: locs             ! Node ptr to
                                                               ! adjacent cells
    integer,     dimension(:),   pointer   :: locvc            ! List of cells
                                                               ! about each node
    integer,     dimension(:),   pointer   :: locvc_type       ! Type of cells
                                                               ! about each node

  continue

! Allocate block interface data for safety

    do ibound = 1, grid%nbound
      call my_alloc_ptr(grid%bc(ibound)%interface_data,1)
    end do

! Convert to local numbering system: l2g, x,y,z, faces, c2n, eptr

    ierr = 0
    allocate(t1_ind(grid%nnodes01))
    call heap_sort(grid%nnodes01,grid%l2g,t1_ind)
    allocate(t1(grid%nnodes01))
    t1 = grid%l2g(t1_ind)

    allocate(tempx(grid%nnodes01))
    tempx = grid%x; grid%x = tempx(t1_ind)
    tempx = grid%y; grid%y = tempx(t1_ind)
    tempx = grid%z; grid%z = tempx(t1_ind)
    deallocate(tempx)

    do ibound = 1,grid%nbound
      do i = 1,grid%bc(ibound)%nbfacet
        grid%bc(ibound)%f2ntb(i,1) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2ntb(i,1))
        grid%bc(ibound)%f2ntb(i,2) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2ntb(i,2))
        grid%bc(ibound)%f2ntb(i,3) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2ntb(i,3))
      end do
      do i = 1,grid%bc(ibound)%nbfaceq
        grid%bc(ibound)%f2nqb(i,1) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,1))
        grid%bc(ibound)%f2nqb(i,2) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,2))
        grid%bc(ibound)%f2nqb(i,3) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,3))
        grid%bc(ibound)%f2nqb(i,4) =                                           &
                      binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,4))
      end do
    end do

    do ielem = 1, grid%nelem
      if (grid%elem(ielem)%ncell > 0) then
        do i = 1,grid%elem(ielem)%ncell
          do j = 1, grid%elem(ielem)%node_per_cell
            grid%elem(ielem)%c2n(j,i) =                                        &
                       binary_search(grid%nnodes01,t1,grid%elem(ielem)%c2n(j,i))
          end do
        end do
      end if
    end do

    do i = 1,grid%nedge
      j = grid%eptr(1,i); grid%eptr(1,i) = binary_search(grid%nnodes01,t1,j)
      j = grid%eptr(2,i); grid%eptr(2,i) = binary_search(grid%nnodes01,t1,j)
    end do

!-----------------------------------------------------
! check tet handedness

    if (make_tets.or.partition_lines) then
       do ielem = 1,grid%nelem
          if (grid%elem(ielem)%type_cell == 'tet')                             &
             call chkgrd(size(grid%elem(ielem)%c2n,2),                         &
                         grid%elem(ielem)%ncell, grid%nnodes01,                &
                         grid%elem(ielem)%c2n, grid%x,grid%y,grid%z)
       end do
    end if

!-----------------------------------------------------

!   find the elements that surround each node

    nloc = 0
    do ielem = 1,grid%nelem
      j = grid%elem(ielem)%ncell
      if (j > 0) nloc = nloc + j*grid%elem(ielem)%node_per_cell
    end do

    allocate(locvc(nloc));           locvc      = 0
    allocate(locvc_type(nloc));      locvc_type = 0
    allocate(locs(grid%nnodes01+1)); locs       = 0

    do ielem = 1,grid%nelem
      call countsurel(grid%elem(ielem)%ncell,                                  &
                      grid%elem(ielem)%node_per_cell, grid%elem(ielem)%c2n,    &
                      grid%nnodes01, locs, ielem, grid%nelem)
    end do

    do ielem = 1,grid%nelem
      call surel(grid%elem(ielem)%ncell,                                       &
                 grid%elem(ielem)%node_per_cell, grid%elem(ielem)%c2n,         &
                 grid%nnodes01, nloc, locs, locvc, locvc_type,                 &
                 ielem, grid%nelem,                                            &
                 verbose )
    end do

!   make sure boundary faces are right-handed; also find interior cell
!   corresponding to each boundary face

    do ibound=1,grid%nbound
      if (grid%bc(ibound)%nbfacet > 0) then
        call chkbnd(ibound, grid%nnodes01, grid%nbound, nloc, grid%x, grid%y,  &
                    grid%z, locs, locvc, locvc_type, grid%nelem, grid%elem,    &
                    grid%bc(ibound)%nbfacet, grid%bc(ibound)%f2ntb(:,:),       &
                    'tria', 3, grid%bc(ibound)%ibc, verbose, ierr)
        if (ierr == 1) exit
      endif

      if (grid%bc(ibound)%nbfaceq > 0) then
        call chkbnd(ibound, grid%nnodes01, grid%nbound, nloc, grid%x, grid%y,  &
                    grid%z, locs, locvc, locvc_type, grid%nelem, grid%elem,    &
                    grid%bc(ibound)%nbfaceq, grid%bc(ibound)%f2nqb(:,:),       &
                    'quad', 4, grid%bc(ibound)%ibc, verbose, ierr)
        if (ierr == 1) exit
      endif

    end do
    call lmpi_conditional_stop(ierr)

    deallocate(locvc);      nullify (locvc)
    deallocate(locvc_type); nullify(locvc_type)
    deallocate(locs);       nullify(locs)

! Localizes f2ntb to index into ibnode.
! Sets ibnode to face node, then sets f2ntb to the new local boundary node.

    allocate(mark(grid%nnodes01)); mark = 0
    call finder( verbose, grid%nbound, grid%nnodes01, mark, grid%bc )
    deallocate(mark)

! Convert back from local numbering system: x,y,z,c2n,eptr
! Note, in "finder" ibnode have been replaced by f2ntb; and f2ntb
!       have been localized to ibnode (i.e., no conversion).
!   So, ibnode are converted to  grid%l2g(t1_ind(j))

    allocate(tempx(grid%nnodes01))
    tempx = grid%x; grid%x(t1_ind) = tempx
    tempx = grid%y; grid%y(t1_ind) = tempx
    tempx = grid%z; grid%z(t1_ind) = tempx
    deallocate(tempx)

    if (grid%nbound > 0) then
       do ibound = 1,grid%nbound
          nbnode = grid%bc(ibound)%nbnode
          if (nbnode > 0) then
             do i = 1,nbnode
                j = grid%bc(ibound)%ibnode(i)
                grid%bc(ibound)%ibnode(i) =  grid%l2g(t1_ind(j))
             end do
          end if
       end do
    end if

! Convert c2n back.

    do ielem = 1, grid%nelem
       do i = 1,grid%elem(ielem)%ncell
          do j = 1, grid%elem(ielem)%node_per_cell
             grid%elem(ielem)%c2n(j,i) =                                       &
               grid%l2g(t1_ind(grid%elem(ielem)%c2n(j,i)))
          end do
       end do
    end do

! Convert eptr back.

    do i = 1,grid%nedge
       grid%eptr(1,i) = grid%l2g(t1_ind(grid%eptr(1,i)))
       grid%eptr(2,i) = grid%l2g(t1_ind(grid%eptr(2,i)))
    end do

    deallocate(t1,t1_ind)

! Compute grid%bc(:)%nbnodeg

    allocate(counts(lmpi_nproc))
    do ibound = 1,grid%nbound
      counts = 0
      call lmpi_gather(grid%bc(ibound)%nbnode,counts)
      call lmpi_bcast(counts)
      icount1 = sum(counts)

      allocate(itemp(icount1))
      call lmpi_gatherv(grid%bc(ibound)%ibnode,counts(lmpi_id+1),itemp,counts)

      if (lmpi_master) then
        call heap_sort(icount1,itemp)
        j = 1
        do i = 1,icount1-1
          if (itemp(i) /= itemp(i+1)) then
            j = j + 1
          end if
        end do
      end if
      call lmpi_bcast(j)
      grid%bc(ibound)%nbnodeg = j
      deallocate(itemp)
    end do
    deallocate(counts)

  end subroutine pparty_puns3d_bc_sm

!================================ pparty_puns3d_bc_cc_sm =====================80
!
! Checks cell for negative volume and swaps if needed (c2n may change).
! Checks boundary face handedness and swaps if needed.
! In "finder", constructs boundary nodes; and sets f2ntb,f2nqb to index
! into ibnode.
!
! Upto this point, node numbers have been kept global to avoid the overhead
! of mapping.  However, the gridmetrics and mechanics throughout this routine
! made it more efficient to use local numbering.  So, eptr, c2n, faces, etc.
! are mapped to l2g.  Then mapped back at the end.
!
!=============================================================================80

  subroutine pparty_puns3d_bc_cc_sm(grid)

    use grid_types,     only : grid_type
    use info_depr,      only : make_tets
    use metis_defs,     only : partition_lines
    use sort,           only : heap_sort, binary_search
!   use puns3ds,        only : finder, surel, countsurel
    use puns3d_checker, only : chkgrd, chkbnd
    use allocations,    only : my_alloc_ptr

    type(grid_type), intent(inout) :: grid

    integer :: ibound, ielem
    integer :: i, j, k, ierr
    integer :: icount1, nbnode

    integer, dimension(:), allocatable :: t1,t1_ind,mark
    integer, dimension(:), allocatable :: counts, itemp

    integer                                :: nloc             ! Dimension of
                                                               ! locs array
    integer,     dimension(:),   pointer   :: locs             ! Node ptr to
                                                               ! adjacent cells
    integer,     dimension(:),   pointer   :: locvc            ! List of cells
                                                               ! about each node
    integer,     dimension(:),   pointer   :: locvc_type       ! Type of cells
                                                               ! about each node

    real(dp), dimension(:), allocatable :: tempx

  continue

! Allocate block interface data for safety

    do ibound = 1, grid%nbound
      call my_alloc_ptr(grid%bc(ibound)%interface_data,1)
    end do

    !if (lmpi_master) write(*,*)"Can this be combined with pparty_puns3d_bc_sm"

! Convert to local numbering system: l2g, x,y,z, faces, c2n, eptr

    ierr = 0
    allocate(t1_ind(grid%nnodes01))
    call heap_sort(grid%nnodes01,grid%l2g,t1_ind)
    allocate(t1(grid%nnodes01))
    t1 = grid%l2g(t1_ind)

    allocate(tempx(grid%nnodes01))
    tempx = grid%x; grid%x = tempx(t1_ind)
    tempx = grid%y; grid%y = tempx(t1_ind)
    tempx = grid%z; grid%z = tempx(t1_ind)
    deallocate(tempx)

    do ibound = 1,grid%nbound
      do i = 1,grid%bc(ibound)%nbfacet
        grid%bc(ibound)%f2ntb(i,1) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2ntb(i,1))
        grid%bc(ibound)%f2ntb(i,2) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2ntb(i,2))
        grid%bc(ibound)%f2ntb(i,3) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2ntb(i,3))
      end do
      do i = 1,grid%bc(ibound)%nbfaceq
        grid%bc(ibound)%f2nqb(i,1) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,1))
        grid%bc(ibound)%f2nqb(i,2) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,2))
        grid%bc(ibound)%f2nqb(i,3) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,3))
        grid%bc(ibound)%f2nqb(i,4) =                                           &
             binary_search(grid%nnodes01,t1,grid%bc(ibound)%f2nqb(i,4))
      end do
    end do

    do ielem = 1, grid%nelem
      if (grid%elem(ielem)%ncell > 0) then
        do i = 1,grid%elem(ielem)%ncell
          do j = 1, grid%elem(ielem)%node_per_cell
            grid%elem(ielem)%c2n(j,i) =                                        &
                 binary_search(grid%nnodes01,t1,grid%elem(ielem)%c2n(j,i))
          end do
        end do
      end if
    end do

    do i = 1,grid%nedge
      j = grid%eptr(1,i); grid%eptr(1,i) = binary_search(grid%nnodes01,t1,j)
      j = grid%eptr(2,i); grid%eptr(2,i) = binary_search(grid%nnodes01,t1,j)
    end do

!-----------------------------------------------------
! check tet handedness

    if (make_tets.or.partition_lines) then
       do ielem = 1,grid%nelem
          if (grid%elem(ielem)%type_cell == 'tet')                             &
             call chkgrd(size(grid%elem(ielem)%c2n,2),                         &
                         grid%elem(ielem)%ncell, grid%nnodes01,                &
                         grid%elem(ielem)%c2n, grid%x,grid%y,grid%z)
       end do
    end if

!-----------------------------------------------------
!   find the elements that surround each node

    k = 0
    do ielem = 1,grid%nelem
      j = grid%elem(ielem)%ncell
      if (j > 0) k = k + j*grid%elem(ielem)%node_per_cell
    end do
!   nloc = grid%nnodes01
    nloc = k

    allocate(locvc(nloc));      locvc      = 0
    allocate(locvc_type(nloc)); locvc_type = 0
    allocate(locs(k));          locs       = 0

    do ielem = 1,grid%nelem
      call countsurel(grid%elem(ielem)%ncell,                                  &
                      grid%elem(ielem)%node_per_cell, grid%elem(ielem)%c2n,    &
                      size(locs)-1, locs, ielem, grid%nelem)
 !                    grid%nnodes01, locs, ielem, grid%nelem)
    end do

    do ielem = 1,grid%nelem
      call surel(grid%elem(ielem)%ncell,                                       &
                 grid%elem(ielem)%node_per_cell, grid%elem(ielem)%c2n,         &
                 size(locs)-1, nloc, locs, locvc, locvc_type,                  &
          !      grid%nnodes01, nloc, locs, locvc, locvc_type,                 &
                 ielem, grid%nelem,                                            &
                 verbose )
    end do

!   make sure boundary faces are right-handed; also find interior cell
!   corresponding to each boundary face

    do ibound=1,grid%nbound
      if (grid%bc(ibound)%nbfacet > 0) then
        call chkbnd(ibound, grid%nnodes01, grid%nbound, nloc, grid%x, grid%y,  &
                    grid%z, locs, locvc, locvc_type, grid%nelem, grid%elem,    &
                    grid%bc(ibound)%nbfacet, grid%bc(ibound)%f2ntb, 'tria', 3, &
                    grid%bc(ibound)%ibc, verbose, ierr)
        if (ierr == 1) exit
      endif

      if (grid%bc(ibound)%nbfaceq > 0) then
        call chkbnd(ibound, grid%nnodes01, grid%nbound, nloc, grid%x, grid%y,  &
                    grid%z, locs, locvc, locvc_type, grid%nelem, grid%elem,    &
                    grid%bc(ibound)%nbfaceq, grid%bc(ibound)%f2nqb, 'quad', 4, &
                    grid%bc(ibound)%ibc, verbose, ierr)
        if (ierr == 1) exit
      endif

    end do
    call lmpi_conditional_stop(ierr)

    deallocate(locvc);      nullify (locvc)
    deallocate(locvc_type); nullify(locvc_type)
    deallocate(locs);       nullify(locs)

! Localizes f2ntb to index into ibnode.
! Sets ibnode to face node, then sets f2ntb to the new local boundary node.

    !write(*,*)"B4 finder ",lmpi_id
    allocate(mark(grid%nnodes01)); mark = 0
    call finder( verbose, grid%nbound, grid%nnodes01, mark, grid%bc )
    deallocate(mark)
    !write(*,*)"AF finder ",lmpi_id

!   allocate space for dual volumes, unit normals, etc

    do ibound = 1, grid%nbound
      if (associated(grid%bc(ibound)%bxn)) then
         deallocate(grid%bc(ibound)%bxn)
         nullify(grid%bc(ibound)%bxn)
      end if
      if (associated(grid%bc(ibound)%byn)) then
         deallocate(grid%bc(ibound)%byn)
         nullify(grid%bc(ibound)%byn)
      end if
      if (associated(grid%bc(ibound)%bzn)) then
         deallocate(grid%bc(ibound)%bzn)
         nullify(grid%bc(ibound)%bzn)
      end if
      allocate(grid%bc(ibound)%bxn(1)); grid%bc(ibound)%bxn = 0.0_dp
      allocate(grid%bc(ibound)%byn(1)); grid%bc(ibound)%byn = 0.0_dp
      allocate(grid%bc(ibound)%bzn(1)); grid%bc(ibound)%bzn = 0.0_dp
    end do

! Allocate deprecated arrays (dummy arrays allocated to 1).

    if (associated(grid%slen)) then
       deallocate(grid%slen); nullify(grid%slen)
    end if
    if (associated(grid%iflagslen)) then
       deallocate(grid%iflagslen); nullify(grid%iflagslen)
    end if
    allocate(grid%slen(1));      grid%slen      = 0.0_dp
    allocate(grid%iflagslen(1)); grid%iflagslen = 0

! Convert back from local numbering system: x,y,z,c2n,eptr
! Note, in "finder" ibnode have been replaced by f2ntb; and f2ntb
!       have been localized to ibnode (i.e., no conversion).
!   So, ibnode are converted to  grid%l2g(t1_ind(j))

    allocate(tempx(grid%nnodes01))
    tempx = grid%x; grid%x(t1_ind) = tempx
    tempx = grid%y; grid%y(t1_ind) = tempx
    tempx = grid%z; grid%z(t1_ind) = tempx
    deallocate(tempx)

    if (grid%nbound > 0) then
       do ibound = 1,grid%nbound
          nbnode = grid%bc(ibound)%nbnode
          if (nbnode > 0) then
             do i = 1,nbnode
                j = grid%bc(ibound)%ibnode(i)
                grid%bc(ibound)%ibnode(i) =  grid%l2g(t1_ind(j))
             end do
          end if
       end do
    end if

! Convert c2n back.

    do ielem = 1, grid%nelem
       do i = 1,grid%elem(ielem)%ncell
          do j = 1, grid%elem(ielem)%node_per_cell
             grid%elem(ielem)%c2n(j,i) =                                       &
               grid%l2g(t1_ind(grid%elem(ielem)%c2n(j,i)))
          end do
       end do
    end do

! Convert eptr back.

    do i = 1,grid%nedge
       grid%eptr(1,i) = grid%l2g(t1_ind(grid%eptr(1,i)))
       grid%eptr(2,i) = grid%l2g(t1_ind(grid%eptr(2,i)))
    end do

    deallocate(t1,t1_ind)

! Compute grid%bc(:)%nbnodeg

    allocate(counts(lmpi_nproc))
    do ibound = 1,grid%nbound
      counts = 0
      call lmpi_gather(grid%bc(ibound)%nbnode,counts)
      call lmpi_bcast(counts)
      icount1 = sum(counts)

      allocate(itemp(icount1))
      call lmpi_gatherv(grid%bc(ibound)%ibnode,counts(lmpi_id+1),itemp,counts)

      if (lmpi_master) then
        call heap_sort(icount1,itemp)
        j = 1
        do i = 1,icount1-1
          if (itemp(i) /= itemp(i+1)) then
            j = j + 1
          end if
        end do
      end if
      call lmpi_bcast(j)
      grid%bc(ibound)%nbnodeg = j
      deallocate(itemp)
    end do
    deallocate(counts)
    grid%nbfaceg = 0 ! DANA TBD

  end subroutine pparty_puns3d_bc_cc_sm

!============================= RAW_GRID_CHECKER ==============================80
!
! Checks cell for negative volume and swaps if needed (c2n may change).
!
!=============================================================================80
  subroutine raw_grid_checker(grid)

    use grid_types,         only : grid_type
    use sort,               only : heap_sort, binary_search
    use info_depr,          only : twod
    use puns3d_checker,     only : chkelem

    type(grid_type), intent(inout) :: grid

    integer :: ielem, nnegcell, i, j

    integer, dimension(:), allocatable :: t1, t1_ind

    real(dp) :: max_cellvol, min_cellvol, maxangle, max_cellangle
    real(dp) :: volmin, volmax
! beginNeverComplex
    real(dp) :: real_local, real_global
! endNeverComplex

    real(dp), dimension(:), allocatable :: tempx

    integer :: negative_volume_cells_global, negative_volume_cells

  continue

! Convert to local numbering system: x,y,z

    allocate(t1_ind(grid%nnodes01))
    call heap_sort(grid%nnodes01,grid%l2g,t1_ind)
    allocate(t1(grid%nnodes01))
    t1 = grid%l2g(t1_ind)

    allocate(tempx(grid%nnodes01))
    tempx = grid%x; grid%x = tempx(t1_ind)
    tempx = grid%y; grid%y = tempx(t1_ind)
    tempx = grid%z; grid%z = tempx(t1_ind)
    deallocate(tempx)

! Convert to local numbering system: c2n

    do ielem = 1, grid%nelem
      do i = 1, grid%elem(ielem)%ncell
        do j = 1, grid%elem(ielem)%node_per_cell
          grid%elem(ielem)%c2n(j,i) =                                          &
                       binary_search(grid%nnodes01,t1,grid%elem(ielem)%c2n(j,i))
        end do
      end do
    end do

    deallocate(t1)

    negative_volume_cells        = 0
    negative_volume_cells_global = 0

    volmin   =  huge(1._dp)
    volmax   = -huge(1._dp)
    maxangle =  0._dp

    element_loop : do ielem = 1,grid%nelem

      call gridmetric(ielem, grid, min_cellvol, max_cellvol, max_cellangle,    &
                      nnegcell)

      negative_volume_cells = negative_volume_cells + nnegcell

! check for negative volumes - if left-handed can be fixed

      if (nnegcell > 0) then
         negative_volume_cells = negative_volume_cells - nnegcell
         if ( lmpi_master ) then
           write(*,'(a,a,a)') '    will swap connectivity to fix ',            &
                              'left-handed ', grid%elem(ielem)%type_cell
         endif
         call chkelem(ielem, grid)
         call gridmetric(ielem, grid, min_cellvol, max_cellvol,                &
                         max_cellangle, nnegcell)
         negative_volume_cells = negative_volume_cells + nnegcell
         if ( lmpi_master ) write(*,*)
      end if

      volmin        = min(volmin, min_cellvol)
      volmax        = max(volmax, max_cellvol)
      maxangle      = max(maxangle, max_cellangle)

      call element_degeneracy(grid,ielem)

      if ( twod ) call check_cell_2d(grid,ielem)

    end do element_loop

    call lmpi_reduce(negative_volume_cells, negative_volume_cells_global)
    call lmpi_bcast(negative_volume_cells_global)

    if (negative_volume_cells_global > 0 .and. lmpi_master) then
      write(*,*)
      write(*,'(a,i7)') ' Warning! negative volumes detected:',                &
                         negative_volume_cells_global
      write(*,*)
    end if

    real_local = real(volmin,dp)
    call lmpi_min(real_local,real_global); call lmpi_bcast(real_global)
    volmin = real_global

    real_local = real(volmax,dp)
    call lmpi_max(real_local,real_global); call lmpi_bcast(real_global)
    volmax = real_global

    real_local = real(maxangle,dp)
    call lmpi_max(real_local,real_global); call lmpi_bcast(real_global)
    maxangle = real_global

    if (lmpi_master) then
      write(*,'("cell statistics:  ",a,",",2(e16.8,","),f15.9,/)') &
        'all', o(volmin), o(volmax), o(maxangle)
    end if

! Convert back from local numbering system: x,y,z

    allocate(tempx(grid%nnodes01))
    tempx = grid%x; grid%x(t1_ind) = tempx
    tempx = grid%y; grid%y(t1_ind) = tempx
    tempx = grid%z; grid%z(t1_ind) = tempx
    deallocate(tempx)

! Convert back from local numbering system: c2n

    do ielem = 1, grid%nelem
       do i = 1,grid%elem(ielem)%ncell
          do j = 1, grid%elem(ielem)%node_per_cell
             grid%elem(ielem)%c2n(j,i) =                                       &
               grid%l2g(t1_ind(grid%elem(ielem)%c2n(j,i)))
          end do
       end do
    end do

    deallocate(t1_ind)

  end subroutine raw_grid_checker


!================================ CHECK_CELL_2D ==============================80
!
! Checks cells to make sure that for 2D computations, all the nodes on the
! primary cell face (face_2d) lie on the primary 2D plane (yplane_2d)
!
!=============================================================================80
  subroutine check_cell_2d(grid,ielem)

    use twod_util,  only : yplane_2d, y_coplanar_tol
    use grid_types, only : grid_type
    use kinddefs,   only : dp
    use lmpi,       only : lmpi_max_and_maxid

    integer, intent(in) :: ielem

    type(grid_type), intent(inout) :: grid

    integer :: cell, not_on_yplane_2d, iface, total_faces, tempint
    integer :: nn1, nn2, nn3, nn4, n1, n2 ,n3, n4, nface

    integer, save :: candidate_face = 0

    real(dp) :: y1_chk, y2_chk, y3_chk, y4_chk

    logical :: solve_plane_found

    logical, parameter :: debug = .false.

    integer :: ncell = 0, proc, closest_candidate_face

    real(dp) :: off_plane_error, closest_off_plane_error

  continue

    solve_plane_found = .false.

    closest_off_plane_error = huge(1._dp)

    nface = 0
    if (trim(grid%elem(ielem)%type_cell) == 'hex') then
      nface = 6
    else if (trim(grid%elem(ielem)%type_cell) == 'prz') then
      nface = 2
    else
      write(*,*) 'error in check_cell_2d...cell type not hex or prz'
      call lmpi_conditional_stop(1,'cell type:check_cell_2d')
      stop
    end if

    if ( debug ) then
      cell = grid%elem(ielem)%ncell
      call lmpi_reduce(cell,ncell)
      call lmpi_bcast(ncell)
    endif

    face_loop : do iface = 1, nface

      off_plane_error = -huge(1._dp)

! yplane_2d and face_2d together identify the primary 2D plane/face

      not_on_yplane_2d = 0
      total_faces      = 0

      cell_loop : do cell = 1, grid%elem(ielem)%ncell

        nn1 = grid%elem(ielem)%local_f2n(iface,1)
        nn2 = grid%elem(ielem)%local_f2n(iface,2)
        nn3 = grid%elem(ielem)%local_f2n(iface,3)
        nn4 = grid%elem(ielem)%local_f2n(iface,4)

        n1 = grid%elem(ielem)%c2n(nn1,cell)
        n2 = grid%elem(ielem)%c2n(nn2,cell)
        n3 = grid%elem(ielem)%c2n(nn3,cell)

        total_faces = total_faces + 1

        y1_chk = abs(grid%y(n1) - yplane_2d)
        y2_chk = abs(grid%y(n2) - yplane_2d)
        y3_chk = abs(grid%y(n3) - yplane_2d)

        off_plane_error = max( off_plane_error, &
                               y1_chk, y2_chk, y3_chk )

        if (nn4 == nn1) then
          if (y1_chk > y_coplanar_tol .or. y2_chk > y_coplanar_tol  .or.       &
              y3_chk > y_coplanar_tol                             ) then
            not_on_yplane_2d = not_on_yplane_2d + 1
          end if
        else
          n4              = grid%elem(ielem)%c2n(nn4,cell)
          y4_chk          = abs(grid%y(n4) - yplane_2d)
          off_plane_error = max( off_plane_error, y4_chk )
          if (y1_chk > y_coplanar_tol .or. y2_chk > y_coplanar_tol  .or.       &
              y3_chk > y_coplanar_tol .or. y4_chk > y_coplanar_tol) then
            not_on_yplane_2d = not_on_yplane_2d + 1
          end if
        end if

      end do cell_loop

      call lmpi_reduce(not_on_yplane_2d,tempint)
      not_on_yplane_2d = tempint
      call lmpi_bcast(not_on_yplane_2d)

      call lmpi_max_and_maxid( real(off_plane_error,dp), proc )
      call lmpi_bcast(off_plane_error, proc )

      if ( off_plane_error < closest_off_plane_error ) then
        closest_candidate_face  = iface
        closest_off_plane_error = off_plane_error
      endif

      if((not_on_yplane_2d == 0) .and. .not.solve_plane_found) then
        candidate_face = iface
        solve_plane_found = .true.
      elseif(not_on_yplane_2d == 0) then
        if ( lmpi_master ) then
          write(*,*)
          write(*,*)' Fatal error in check_cell_2d...stopping.'
          write(*,*)' Considering the element type: ',grid%elem(ielem)%type_cell
          write(*,*)' And the topological face : ',iface
          write(*,*)' This topological face is on the 2d plane.'
          write(*,'(1x,a,e11.4,a)')&
          ' (within a tolerance of ',o(y_coplanar_tol),')'
          write(*,*)' But another face on the 2d plane was already found!'
          write(*,*)' candidate_face=',candidate_face
          write(*,*)' Faces considered on this topological face =',total_faces
        endif
        call lmpi_conditional_stop(1,'extra faces on 2D plane:check_cell_2d')
        stop
      end if

      if ( debug ) then
        write(*,*) ' face=',iface,                  &
        ' faces not_on_yplane_2d=',not_on_yplane_2d,&
        ' of ncells=',ncell,                        &
        ' solve_plane_found=',solve_plane_found
        write(*,*) ' off_plane_error=',off_plane_error
      endif

    end do face_loop

! Check that a valid solve plane has been found

    if (.not.solve_plane_found) then
      if ( lmpi_master ) then
        write(*,*)
        write(*,*) ' Warning in check_cell_2d...'
        write(*,'(a,i8,a,i8,3a,f7.3,a)') ' WARNING: ',not_on_yplane_2d,' of ', &
                                  total_faces,' ',grid%elem(ielem)%type_cell,  &
                                    ' faces are not on the y = ',o(yplane_2d), &
                                       ' plane'
        write(*,'(a,e11.4,a)') ' (within a tolerance of ',o(y_coplanar_tol),')'
        write(*,'(3a)')' YOU WILL NOT BE ABLE TO RUN THIS MESH IN THE 2D MODE',&
                       ' EITHER FOR VISCOUS CASES',                            &
                       ' OR MIXED ELEMENT DUAL FACE INTEGRATIONS'
        write(*,"(1x,a,i0,a,e12.5)")                      &
        ' Closest candidate face=',closest_candidate_face,&
        ' off plane by ',closest_off_plane_error
      endif
      candidate_face = closest_candidate_face
      if ( lmpi_master )write(*,*)' Reverting to face_2d value =',candidate_face
    end if

    grid%elem(ielem)%face_2d = candidate_face

    if ( lmpi_master ) then
      write(*,"(1x,3a,i0,a,e12.5)")               &
      ' Element_type=',grid%elem(ielem)%type_cell,&
      '  face_2d=',grid%elem(ielem)%face_2d,      &
      ' off_plane_error=',closest_off_plane_error
    endif

  end subroutine check_cell_2d


!================================ CELL_TO_PART_TAG2 ==========================80
!
! Tag the bit representing the cell number if the cell (c2p) is off_processor.
!
! For level01 cells, uniquely assign to one processor globally.
!
!   1. determine partition for each nnode
!      Since only looking at level01 cells, only need to determine level1 nodes.
!
!   2. cell_to_part assigns cell to processor with lowest id.
!      May be better load balancing if it where processor with most nodes.
!
!=============================================================================80

  subroutine cell_to_part_tag2(grid, n_c2p, c2p)

    use grid_types, only : grid_type

    type(grid_type),                   intent(in)  :: grid
    integer,                           intent(in)  :: n_c2p
    integer,         dimension(n_c2p), intent(out) :: c2p

    integer  :: i,j,k,last,node1,n0,ipe,ioff,icell,nnodes1,ict,ielem
    integer(system_i8) :: ict_i8, j_i8, local_grid_ncellg_i8

    integer, dimension(:), allocatable :: nodelist0, tag1, l2g_level1

  continue

    ioff = grid%nnodes0
    nnodes1 = grid%nnodes01-grid%nnodes0
    allocate(tag1(nnodes1)); tag1 = -1

! Determine which processor owns (level0) the local level1 nodes.

    do ipe = 0,lmpi_nproc-1

       if (lmpi_id == ipe) n0 = grid%nnodes0
       call lmpi_bcast(n0,ipe)
       allocate(nodelist0(n0))
       if (lmpi_id == ipe) nodelist0 = grid%l2g(1:n0)
       call lmpi_bcast(nodelist0,ipe)

       if (lmpi_id /= ipe) then
          last = 1
          out1: do i = 1,n0
             node1 = nodelist0(i)
 1           continue
             if (node1 < grid%l2g(ioff+last)) cycle
             if (node1 > grid%l2g(ioff+last)) then
                last = last + 1
                if (last > grid%nnodes01-grid%nnodes0) exit out1
                go to 1 ! increment last, but do not increment i.
             end if
             tag1(last) = ipe
             last = last + 1
             if (last > grid%nnodes01-grid%nnodes0) exit out1
          end do out1
       end if

       deallocate(nodelist0)
    end do

    j = 0
    do i = 1,nnodes1
       if (tag1(i) == -1) j = j + 1
    end do
    call lmpi_max(j,k)
    call lmpi_bcast(k)
    if (lmpi_master) then
       if (k /= 0) write(*,*)"Error in Cell_to_part. Contact support."
    end if
    call lmpi_conditional_stop(k)

!----------------------

! Fill tag2 with unique processor

    allocate(l2g_level1(nnodes1))
    l2g_level1 = grid%l2g(grid%nnodes0+1:grid%nnodes01)

    ict = 0
    c2p = lmpi_nproc

    do ielem = 1,grid%nelem
       do icell  = 1,grid%elem(ielem)%ncell
          ict = ict + 1
          j = grid%elem(ielem)%c2n(1,icell)
          if (j <= grid%nnodes0) then
             c2p(ict) = lmpi_id
         else
             j = j - grid%nnodes0
             c2p(ict) = tag1(j)
         end if
         !write(400+lmpi_id,*) grid%elem(ielem)%cl2g(icell),c2p(ict)
       end do ! ncell
    end do ! ielem
    deallocate(l2g_level1,tag1)

    ict_i8 = 0
    do i = 1,n_c2p
       if (c2p(i) == lmpi_id) ict_i8 = ict_i8 + 1
    end do
    call lmpi_reduce(ict_i8,j_i8)
    call lmpi_bcast(j_i8)
    if (lmpi_master) then
       local_grid_ncellg_i8 = grid%elem(1)%ncellg
       do ielem = 2,grid%nelem
          local_grid_ncellg_i8 = local_grid_ncellg_i8 + grid%elem(ielem)%ncellg
       end do
    end if

  end subroutine cell_to_part_tag2

!================================ ELEMENT_DEGENERACY =========================80
!
! Check for degenerate elements
!
!=============================================================================80
  subroutine element_degeneracy(grid,ielem)

    use system_extensions, only : se_open
    use kinddefs,          only : dp
    use string_utils,      only : int_to_s
    use fun3d_constants,   only : my_3rd, my_4th
    use grid_types,        only : grid_type

    integer, intent(in) :: ielem

    type(grid_type), intent(in) :: grid

    integer :: iface
    integer :: n1,n2,n3,n4
    integer :: tempint
    integer :: icell, corner
    integer :: m1,m2,m3
    integer :: collapsed_tria
    integer :: collapsed_quad
    integer :: degenerate_quad

    integer, dimension(grid%elem(ielem)%face_per_cell) :: face_type

    real(dp) :: xavg,yavg,zavg
    real(dp) :: x1, x2, x3, x4
    real(dp) :: y1, y2, y3, y4
    real(dp) :: z1, z2, z3, z4
    real(dp) :: angle, conv, dot
    real(dp) :: term, angle_tol

    real(dp), dimension(2) :: dx, dy, dz

    character(len=80) :: filename

    integer, parameter :: iu = 56

    logical :: found_quad_problem

  continue

!   initialize tolerance

    conv      = acos(-1._dp)/180._dp
    angle_tol = 2.0_dp

!   determine which faces are tria, and mark them; all other
!   faces assumed to be quad

!   default to tria faces

    face_type = 0

    do iface = 1, grid%elem(ielem)%face_per_cell
      if (grid%elem(ielem)%local_f2n(iface,4) /=                               &
          grid%elem(ielem)%local_f2n(iface,1)) then
!       nodes 1 and 4 are different...quad face
        face_type(iface) = 1
      end if
    end do

!   loop over cells

    collapsed_tria  = 0
    collapsed_quad  = 0
    degenerate_quad = 0

    cell_loop : do icell = 1, grid%elem(ielem)%ncell

!     loop over tria faces

      tria_faces_loop : do iface = 1, grid%elem(ielem)%face_per_cell

        if (face_type(iface) == 0) then

          n1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,1),icell)
          n2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,2),icell)
          n3 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,3),icell)

          x1 = grid%x(n1)
          x2 = grid%x(n2)
          x3 = grid%x(n3)

          y1 = grid%y(n1)
          y2 = grid%y(n2)
          y3 = grid%y(n3)

          z1 = grid%z(n1)
          z2 = grid%z(n2)
          z3 = grid%z(n3)

          xavg = (x1 + x2 + x3)*my_3rd
          yavg = (y1 + y2 + y3)*my_3rd
          zavg = (z1 + z2 + z3)*my_3rd

          if(abs(x1-x2)+abs(y1-y2)+abs(z1-z2) < 1.0e-14_dp) then
            collapsed_tria = collapsed_tria + 1
          elseif(abs(x1-x3)+abs(y1-y3)+abs(z1-z3) < 1.0e-14_dp) then
            collapsed_tria = collapsed_tria + 1
          elseif(abs(x3-x2)+abs(y3-y2)+abs(z3-z2) < 1.0e-14_dp) then
            collapsed_tria = collapsed_tria + 1
          endif

        end if

      end do tria_faces_loop

!     loop over quad faces

      found_quad_problem = .false.

      quad_faces_loop : do iface = 1, grid%elem(ielem)%face_per_cell

        quad_type : if (face_type(iface) /= 0) then

          n1 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,1),icell)
          n2 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,2),icell)
          n3 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,3),icell)
          n4 = grid%elem(ielem)%c2n(grid%elem(ielem)%local_f2n(iface,4),icell)

          x1 = grid%x(n1)
          x2 = grid%x(n2)
          x3 = grid%x(n3)
          x4 = grid%x(n4)

          y1 = grid%y(n1)
          y2 = grid%y(n2)
          y3 = grid%y(n3)
          y4 = grid%y(n4)

          z1 = grid%z(n1)
          z2 = grid%z(n2)
          z3 = grid%z(n3)
          z4 = grid%z(n4)

          xavg = (x1 + x2 + x3 + x4)*my_4th
          yavg = (y1 + y2 + y3 + y4)*my_4th
          zavg = (z1 + z2 + z3 + z4)*my_4th

          if(abs(x1-x2)+abs(y1-y2)+abs(z1-z2) < 1.0e-14_dp) then
            collapsed_quad = collapsed_quad + 1
            found_quad_problem = .true.
          elseif(abs(x1-x3)+abs(y1-y3)+abs(z1-z3) < 1.0e-14_dp) then
            collapsed_quad = collapsed_quad + 1
            found_quad_problem = .true.
          elseif(abs(x1-x4)+abs(y1-y4)+abs(z1-z4) < 1.0e-14_dp) then
            collapsed_quad = collapsed_quad + 1
            found_quad_problem = .true.
          elseif(abs(x2-x3)+abs(y2-y3)+abs(z2-z3) < 1.0e-14_dp) then
            collapsed_quad = collapsed_quad + 1
            found_quad_problem = .true.
          elseif(abs(x2-x4)+abs(y2-y4)+abs(z2-z4) < 1.0e-14_dp) then
            collapsed_quad = collapsed_quad + 1
            found_quad_problem = .true.
          elseif(abs(x3-x4)+abs(y3-y4)+abs(z3-z4) < 1.0e-14_dp) then
            collapsed_quad = collapsed_quad + 1
            found_quad_problem = .true.
          endif

          corner_loop : do corner = 1,4
            m1 = n1
            m2 = n2
            m3 = n3
            if(corner == 2) then
              m1 = n2
              m2 = n3
              m3 = n4
            elseif(corner == 3) then
              m1 = n3
              m2 = n4
              m3 = n1
            elseif(corner == 4) then
              m1 = n4
              m2 = n1
              m3 = n2
            endif

            dx(1) = grid%x(m2)-grid%x(m1)
            dy(1) = grid%y(m2)-grid%y(m1)
            dz(1) = grid%z(m2)-grid%z(m1)
            dx(2) = grid%x(m3)-grid%x(m2)
            dy(2) = grid%y(m3)-grid%y(m2)
            dz(2) = grid%z(m3)-grid%z(m2)
            term  = sqrt( dx(1)**2 + dy(1)**2 + dz(1)**2 )
            dx(1) = dx(1)/term
            dy(1) = dy(1)/term
            dz(1) = dz(1)/term
            term  = sqrt( dx(2)**2 + dy(2)**2 + dz(2)**2 )
            dx(2) = dx(2)/term
            dy(2) = dy(2)/term
            dz(2) = dz(2)/term

            dot   = dx(1)*dx(2) + dy(1)*dy(2) + dz(1)*dz(2)
            dot   = max( -1.0_dp , dot )
            dot   = min( +1.0_dp , dot )
            angle = acos(dot)/conv

            if(abs(angle) < angle_tol) then
              degenerate_quad = degenerate_quad + 1
              found_quad_problem = .true.

              if (degenerate_quad == 1) then
                 ! Open file to store degenerate quad info
                 filename = trim(grid%project) // '_degenerate_quads.dat.'     &
                                               // int_to_s(lmpi_id+1)
                 call se_open(iu,file = filename)
                 write(iu,*)                                                   &
                 'Tolerance for angle change between adjacent edges=',angle_tol
              end if

              write(iu,*)
              write(iu,*) ' cell, face, corner=',icell,iface,corner
              write(iu,*) ' angle=',angle,'   m1,m2,m3=',m1,m2,m3
              write(iu,*) ' xavg,yavg,zavg=',xavg,yavg,zavg
              write(iu,*) ' dr(1)=',dx(1),dy(1),dz(1)
              write(iu,*) ' dr(2)=',dx(2),dy(2),dz(2)

              write(iu,*) ' x,y,z (m1)=',grid%x(m1),grid%y(m1),grid%z(m1)
              write(iu,*) ' x,y,z (m2)=',grid%x(m2),grid%y(m2),grid%z(m2)
              write(iu,*) ' x,y,z (m3)=',grid%x(m3),grid%y(m3),grid%z(m3)
              write(iu,*)
            endif

          enddo corner_loop

        end if quad_type

      end do quad_faces_loop

! try to visualize any cells that exhibited quad face problems

      if (found_quad_problem) call plot_bad_quad_cells(grid,ielem,icell,.false.)

    end do cell_loop

! close any open files

    call plot_bad_quad_cells(grid,ielem,icell,.true.)

    if ( degenerate_quad > 0 ) close(iu)

    call lmpi_reduce(collapsed_tria,tempint)
    collapsed_tria = tempint
    call lmpi_bcast(collapsed_tria)

    if ( collapsed_tria > 0 ) then
      if ( lmpi_master ) then
        write(*,*) ' ...Collapsed_triangular faces=',collapsed_tria
      endif
      call lmpi_conditional_stop(1)
    endif

    call lmpi_reduce(collapsed_quad,tempint)
    collapsed_quad = tempint
    call lmpi_bcast(collapsed_quad)

    if ( collapsed_quad > 0 ) then
      if ( lmpi_master ) then
        write(*,*) ' ...Collapsed_quadrilateral faces=', collapsed_quad
      endif
      call lmpi_conditional_stop(1)
    endif

    call lmpi_reduce(degenerate_quad,tempint)
    degenerate_quad = tempint
    call lmpi_bcast(degenerate_quad)

    if ( degenerate_quad > 0 .and. lmpi_master ) then
      write(*,*)' Possibly degenerate quadrilateral faces=',degenerate_quad
      write(*,*)' ...Exceeded tolerance of degree change less than ',angle_tol
      write(*,*)' ...between adjacent quadrilateral edges.'
    endif

  end subroutine element_degeneracy


!================================ PLOT_BAD_QUAD_CELLS ========================80
!
! Dump out poor quad cells in current family
!
!=============================================================================80
  subroutine plot_bad_quad_cells(grid,ielem,icell,close_file)

    use grid_types,        only : grid_type
    use lmpi,              only : lmpi_id
    use system_extensions, only : se_open
    use string_utils,      only : int_to_s

    integer, intent(in) :: ielem, icell

    logical, intent(in) :: close_file

    type(grid_type), intent(in) :: grid

    integer :: i

    integer, save :: iu = 83

    logical, save :: first_time_in = .true.

    character(len=80) :: filename, format2

  continue

    if ( close_file ) then
      if ( first_time_in ) return
      close(iu)
      return
    endif

    if ( first_time_in ) then
      select case(trim(grid%elem(ielem)%type_cell))
      case('prz')
        filename = trim(grid%project) // '_bad_prz_cells.dat.'                 &
                   // int_to_s(lmpi_id+1)
      case('pyr')
        filename = trim(grid%project) // '_bad_pyr_cells.dat.'                 &
                   // int_to_s(lmpi_id+1)
      case('hex')
        filename = trim(grid%project) // '_bad_hex_cells.dat.'                 &
                   // int_to_s(lmpi_id+1)
      case default
        write(*,*) 'unknown type_cell in plot_bad_quad_cells'
      end select
      call se_open(iu,file = filename)
      rewind(iu)

      write(iu,'(a)') 'title="tecplot geometry file"'
      write(iu,'(2a)') 'variables="x", "y", "z"'
    endif

    select case(trim(grid%elem(ielem)%type_cell))
    case('prz')
      write(iu,*)'ZONE T="PRZ" N=6, E=1, F=FEPOINT, ET=BRICK'
    case('pyr')
      write(iu,*)'ZONE T="PYR" N=5, E=1, F=FEPOINT, ET=BRICK'
    case('hex')
      write(iu,*)'ZONE T="HEX" N=8, E=1, F=FEPOINT, ET=BRICK'
    end select

! now write out the current cell that has been detected as being suspect

    format2 = "(10e23.15)"

    do i = 1, grid%elem(ielem)%node_per_cell
      write(iu,format2) grid%x(grid%elem(ielem)%c2n(i,icell)),                 &
                        grid%y(grid%elem(ielem)%c2n(i,icell)),                 &
                        grid%z(grid%elem(ielem)%c2n(i,icell))
    end do

    select case(trim(grid%elem(ielem)%type_cell))
    case('prz')
      write(iu,'(8(1x,i0))') 5,2,1,6,3,3,4,4
    case('pyr')
      write(iu,'(8(1x,i0))') 1,2,3,4,5,5,5,5
    case('hex')
      write(iu,'(8(1x,i0))') 1,2,4,3,5,6,8,7
    end select

    first_time_in = .false.

  end subroutine plot_bad_quad_cells


!================================ EULER_NUMBER ===============================80
!
! Computes the Euler number of the interior and boundary surfaces
!
!=============================================================================80

  subroutine euler_number( grid, euler_number_bad )

    use grid_types, only : grid_type
    use kinddefs,   only : system_i1

    type(grid_type), intent(in ) :: grid
    logical,         intent(out) :: euler_number_bad

    integer :: ntfaces, nqfaces, nbnodes, ncells, ncellfaces
    integer :: ibound, ielem, euler_int, euler_bnd, node, bnode, temp, joff
    integer :: totalnodes, icell, totaledges, i, n1, n2, localedges, n_c2p

    integer, dimension(:), allocatable :: c2p

    integer(system_i1), dimension(grid%nnodes0) :: mark

  continue

! Compute c2p (cell to process to avoid double bookkeeping)

    n_c2p = 0
    do ielem = 1, grid%nelem
       n_c2p = n_c2p + grid%elem(ielem)%ncell
    end do
    allocate(c2p(n_c2p)); c2p = 0
    call cell_to_part_tag2(grid,n_c2p,c2p)

! count total number of boundary faces and nodes

    ntfaces = 0
    nqfaces = 0
    nbnodes = 0
    mark    = 0

    do ibound = 1, grid%nbound
      ntfaces = ntfaces + sum(grid%bc(ibound)%face_bit(:))
      nqfaces = nqfaces + sum(grid%bc(ibound)%face_bitq(:))
      do bnode = 1, grid%bc(ibound)%nbnode
        node = grid%bc(ibound)%ibnode(bnode)
        if (node <= grid%nnodes0) then
          if (mark(node) == 0) then
            nbnodes = nbnodes + 1
            mark(node) = 1
          endif
        endif
      end do
    end do

    call lmpi_reduce(ntfaces,temp)
    ntfaces = temp
    call lmpi_bcast(ntfaces)

    call lmpi_reduce(nqfaces,temp)
    nqfaces = temp
    call lmpi_bcast(nqfaces)

    call lmpi_reduce(nbnodes,temp)
    nbnodes = temp
    call lmpi_bcast(nbnodes)

! count total number of interior elements and faces

    ncells     = 0
    ncellfaces = 0

    joff = 0
    elem_loop : do ielem = 1, grid%nelem
      cell_loop : do icell = 1, grid%elem(ielem)%ncell
         if (c2p(joff+icell) == lmpi_id) then! cell owned by ipe
            ncells = ncells + 1
            ncellfaces = ncellfaces + grid%elem(ielem)%face_per_cell
         end if
      end do cell_loop
      joff = joff + grid%elem(ielem)%ncell
    end do elem_loop

    call lmpi_reduce(ncells,temp)
    ncells = temp
    call lmpi_bcast(ncells)

    call lmpi_reduce(ncellfaces,temp)
    ncellfaces = temp
    call lmpi_bcast(ncellfaces)

    call lmpi_reduce(grid%nnodes0,temp)
    totalnodes = temp
    call lmpi_bcast(totalnodes)

! To determine number of edges in global mesh, count the ones that
! span the partition boundaries first, then divide that by two since
! they will be double-counted.  Then add the local edge count to this.

    totaledges = 0
    edge_loop1 : do i = 1, grid%nedgeloc
      n1 = grid%eptr(1,i)
      n2 = grid%eptr(2,i)
      if ( (n1<=grid%nnodes0 .and. n2>grid%nnodes0) .or.                       &
           (n2<=grid%nnodes0 .and. n1>grid%nnodes0) ) then
        totaledges = totaledges + 1
      endif
    end do edge_loop1

    call lmpi_reduce(totaledges,temp)
    totaledges = temp/2
    call lmpi_bcast(totaledges)

    localedges = 0
    edge_loop2 : do i = 1, grid%nedgeloc
      n1 = grid%eptr(1,i)
      n2 = grid%eptr(2,i)
      if ( n1<=grid%nnodes0 .and. n2<=grid%nnodes0 ) then
        localedges = localedges + 1
      endif
    end do edge_loop2

    call lmpi_reduce(localedges,temp)
    totaledges = totaledges + temp
    call lmpi_bcast(totaledges)

!   the basic definition of the Euler number of a surface is
!   #faces - #edges + #vertices.  the formulas for euler_bnd and
!   euler_int were lifted from NSU3D.

!   #edges_b = ( ntfaces*3 + nqfaces*4 ) / 2 because of shared faces
!   #faces_b =   ntfaces   + nqfaces

!   #faces_b - #edges_b  = - ntfaces/2 - nqfaces

!   boundary euler : #vertices_b + #faces_b - #edges_b

    euler_bnd= nbnodes - ntfaces/2 - nqfaces

!   At this point, ncellfaces is not the number of faces in the mesh
!   because it is the sum over the cells of faces per cell.
!   Because each face is either shared by another cell in the interior or
!   by a boundary face, the number of unique faces in the grid is:

    ncellfaces = (ncellfaces+ntfaces+nqfaces)/2

!   interior euler : 2( #vertices - #cells + #faces - #edges )

    euler_int= totalnodes - ncells + ncellfaces - totaledges

    euler_int = euler_int*2

    if ( lmpi_master ) then
      write(*,'(3(a,i0))') '     ... Euler numbers Grid:', grid%igrid,         &
                           ' Boundary:', euler_bnd, ' Interior:', euler_int
    endif

!   if the two Euler numbers aren't the same, there is some problem

    euler_number_bad = .false.
    if ( euler_int /= euler_bnd ) then
      euler_number_bad = .true.
      if ( lmpi_master ) then
        write(*,'(a,i0,a,i0,a)') 'ERROR: Volume-mesh Euler number (',          &
          euler_int, ') differs from Boundary-mesh Euler number (',            &
          euler_bnd, ').'
        write(*,'(7x,a)') 'http://wikipedia.org/wiki/Euler_number_(topology)'
        write(*,'(a,3(1x,i0))')' nbnodes,ntfaces,nqfaces =',                   &
                                 nbnodes,ntfaces,nqfaces
        write(*,'(a,4(1x,i0))')' totalnodes,ncells,ncellfaces,totaledges =',   &
                                 totalnodes,ncells,ncellfaces,totaledges
        write(*,*) 'To locate the problem, you can try the following:'
        write(*,*) 'o Set &code_run_control steps=50 /'
        write(*,*) 'o Set &code_run_control stopping_tolerance = 1.0e-20 /'
        write(*,*) 'o Set &raw_grid ignore_euler_number=T /'
        write(*,*) 'o Run code with the command line option --test_freestream'
        write(*,*) &
    'o Look for "Iter density_RMS density_max X-location Y-location Z-location"'
        write(*,*) 'A valid mesh should have residuals that stay 1.0e-12 or ', &
                   'lower'
        write(*,*) '(perhaps 1.0e-11 for meshes with large physical dimensions)'
        write(*,*) 'If so, try running case with ignore_euler_number=T.'
        write(*,*) 'If not, look in the vicinity of the x,y,z location given.'
      endif
    end if

    if ( euler_int /= euler_bnd .and. lmpi_master ) then
    write(*,*)
    write(*,*)' A valid grid is composed of elemental volumes:'
    write(*,*)' (1) tetrahedra (2) hexahedra (3) prisms (4) pyramids'
    write(*,*)' face-connected either to each other or to boundary faces:'
    write(*,*)' (1) triangles (2) quadrilaterals.'
    write(*,*)' Each boundary edge connects to precisely 2 boundary faces.'
    write(*,*)' Two neighboring boundary faces share exactly one boundary edge.'
    write(*,*)' For each boundary face A connecting to a boundary node, every'
    write(*,*)' other boundary face connecting to the same boundary node can be'
    write(*,*)' found by a path through a connected-edge|connected-face'
    write(*,*)' travers starting from boundary face A.'
    write(*,*)
    write(*,*)' The above restrictions are meant to exclude certain topologies'
    write(*,*)' such as two spherical boundaries coming together at a point'
    write(*,*)' or two rectangular boundaries connecting along an edge.'
    write(*,*)
    write(*,*)' The above restrictions are not checked explicitly currently'
    write(*,*)' within FUN3D but will cause the Euler Number check described'
    write(*,*)' below to fail.'
    write(*,*)
    write(*,*)' The Euler Number computed from boundary data (EN_b) is:'
    write(*,*)
    write(*,*)'    N_b - E_b + F_b = EN_b'
    write(*,*)
    write(*,*)' where'
    write(*,*)
    write(*,*)' N_b = boundary nodes    counted'
    write(*,*)' E_b = boundary edges    inferred from N_tri/N_quad'
    write(*,*)' F_b = boundary faces    inferred from N_tri/N_quad'
    write(*,*)
    write(*,*)' The Euler number is a characteristic number for the topology'
    write(*,*)' of the boundary or boundaries.'
    write(*,*)
    write(*,*)' The Euler Number computed from volume data (EN_v) is:'
    write(*,*)
    write(*,*)'    2 ( N - E + F -C ) = EN_v'
    write(*,*)
    write(*,*)' where'
    write(*,*)
    write(*,*)' N = volume nodes     counted'
    write(*,*)' E = volume edges     counted'
    write(*,*)' F = volume faces     inferred from C and F_b'
    write(*,*)' C = volume cells     counted'
    write(*,*)
    write(*,*)' The formula that is checked within FUN3D is"'
    write(*,*)
    write(*,*)'    EN_v - EN_b = 0'
    write(*,*)
    write(*,*)' Barth derived this formula in VKI Lecture Notes for tetrahedra'
    write(*,*)' and noted the formula does not hold in certain cases,'
    write(*,*)' such as two simplices that share only one common edge'
    write(*,*)' or two simplices that share only one common node.'
    write(*,*)' It can be proved by induction that the formula holds for every'
    write(*,*)' valid grid as defined above.'
    write(*,*)
    write(*,*)' Try this checklist to diagnose the problem:'
    write(*,*)' 1. Check the EN_b with your expectations for this case:'
    write(*,*)'      = 2, spherical topology, simple 3D wing with symmetry, ...'
    write(*,*)'      = 0, torus, donut, ...'
    write(*,*)'      =-2, double torus, ...'
    write(*,*)'      =-4, triple torus, pretzel, ...'
    write(*,*)'      = 4, sphere within a sphere, ..'
    write(*,*)'      = 6, 2 spheres within a sphere, ..'
    write(*,*)' 2. Check the number of boundary nodes N_b and faces F_b'
    write(*,*)'    reported with expected values.'
    write(*,*)' 3. Check the number of nodes N and cells C reported'
    write(*,*)'    with expected values.'
    write(*,*)' 4. If steps 1-3 check, EN_b /= EN_v points to inconsistencies'
    write(*,*)'    in edge counts, i.e., delta(E) = 2(EN_b-EN_v). EN_b > EN_v'
    write(*,*)'    implies you have more edges than expected. When this occurs,'
    write(*,*)'    the reported face counts will differ from an actual count.'
    write(*,*)'    An error of this type would arise when there are adjacent'
    write(*,*)'    faces which are inconsistent, i.e.,'
    write(*,*)
    write(*,*)'        1-----2    1-----2'
    write(*,*)'        |    /|    |\    |'
    write(*,*)'        |   / |    | \   |    where 1-2-3-4 denotes 3D nodes'
    write(*,*)'        |  /  |    |  \  |    and left face abuts the right face'
    write(*,*)'        | /   |    |   \ |'
    write(*,*)'        4 ----3    4 ----3'
    write(*,*)
    write(*,*)' The localization technique described above with freestream'
    write(*,*)' residual evaluations can be used but even if the freestram'
    write(*,*)' residuals remain zero, the best practice is to not proceed'
    write(*,*)' without repairing the grid to ensure EN_b = EN_v.'
    write(*,*)
    write(*,*)' Aside: Barth notes that the above formulas are specific forms'
    write(*,*)' of the general Dehn-Sommerville formula, reported'
    write(*,*)' in Wikipedia to hold for simplicial polytopes and simple'
    write(*,*)' polytopes.  The pyramid is not a simple polytope.'
    endif

    deallocate(c2p)

  end subroutine euler_number


!=============================== GRIDMETRIC ==================================80
!
!  Computes the volume and face normals of grid cells.
!
!  Pparty:
!    - for metrics only do each unique cell over whole grid once
!      (i.e., no double counting).  Thus c2p and mpi.
!    - validity do all cells. However, if just informational, then only do
!      can do unique cells.  If corrects, then must do all cells; or do
!      unique cells then exchange.
!
!  Based on the intent(out), only unique cells need to be done.
!
!=============================================================================80
  subroutine gridmetric(ielem, grid, volmin, volmax, maxangle, nnegvol)

    use grid_types,      only : grid_type
    use element_defs,    only : element_metric

    integer, intent(in)  :: ielem
    integer, intent(out) :: nnegvol

    real(dp), intent(out) :: volmin
    real(dp), intent(out) :: volmax
    real(dp), intent(out) :: maxangle

    type(grid_type), intent(in) :: grid

    integer :: i,j,n
    integer :: intvalue

    real(dp) :: volume
    real(dp) :: angle,conv,dot,pi
! beginNeverComplex
    real(dp) :: real_local, real_global
! endNeverComplex

    real(dp), dimension(grid%elem(ielem)%face_per_cell) :: xn,yn,zn

    character(len=3)  :: element_type

  continue

    element_type  = grid%elem(ielem)%type_cell

    nnegvol      =  0
    pi           =  acos(-1._dp)
    conv         =  pi/180._dp
    maxangle     = -360._dp
    volmin       = huge(1._dp)
    volmax       = -volmin

    cell_loop : do n = 1, grid%elem(ielem)%ncell

      call element_metric(n, ielem, grid, volume, xn, yn, zn)

      if (volume > volmax) volmax = volume
      if (volume < volmin) volmin = volume

      if (volume <= 0._dp) nnegvol = nnegvol + 1

!     check min, max cell-face angles
!     note: only need to check upper part of matrix chk_norm since
!     dot products commute (A*B = B*A) and the diagonal indicates
!     the dot product of a face with itself

      do i=1, grid%elem(ielem)%face_per_cell
        do j=i+1, grid%elem(ielem)%face_per_cell
          if (grid%elem(ielem)%chk_norm(i,j) > 0) then
            dot = xn(i)*xn(j) + yn(i)*yn(j) + zn(i)*zn(j)
            angle = 180._dp - acos(dot)/conv
            if (angle > maxangle) maxangle = angle
          end if
        end do
      end do

    end do cell_loop

    real_local = real(volmin,dp)
    call lmpi_min(real_local,real_global); call lmpi_bcast(real_global)
    volmin = real_global

    real_local = real(volmax,dp)
    call lmpi_max(real_local,real_global); call lmpi_bcast(real_global)
    volmax = real_global

    real_local = real(maxangle,dp)
    call lmpi_max(real_local,real_global); call lmpi_bcast(real_global)
    maxangle = real_global

    call lmpi_reduce(nnegvol,intvalue); call lmpi_bcast(intvalue)
    nnegvol = intvalue

! print grid quality checks

    if ( lmpi_master ) then
      if (1 == ielem) write(*,'(a)')&
"cell statistics: type,      min volume,      max volume, max face angle"
      write(*,'("cell statistics:  ",a,",",2(e16.8,","),f15.9)') &
        element_type, o(volmin), o(volmax), o(maxangle)

      if (nnegvol > 0) then
        write(*,'(a,i0,a,a3,a)')'  warning: ',nnegvol,' ',element_type,     &
              ' cells have negative volumes in pparty_puns3d:gridmetric'
      end if
    end if

  end subroutine gridmetric


!==================================== FINDER =================================80
!
! Create the ibnode array - ibnode maps the local (to a boundary instance) node
! number into the volume grid node number.
!
! Note: also determines the number of nodes in a boundary instance.
!
!=============================================================================80

  subroutine finder( verbose, nbound, nnodesg, mark, bc, istart, iend )

    use bc_types,        only : bcgrid_type
    use allocations,     only : my_alloc_ptr

    integer,                         intent(in)  :: nbound, nnodesg
    integer, dimension(nnodesg),     intent(inout) :: mark
    logical,                         intent(in)  :: verbose
    type(bcgrid_type), dimension(:), pointer     :: bc
    integer,                         intent(in), optional :: istart, iend

    integer :: ibound, iface, inode

    continue

    if (verbose) write(*,*) 'calculating nbnode in finder'

    boundary_instance : do ibound = 1, nbound

!   calculate nbnode (total number of nodes on each bc type)
!   use mark as a temp array

!   use mark to flag all volume nodes found on this surface

      mark = 0

!     tria faces

      do iface = 1, bc(ibound)%nbfacet
        mark(bc(ibound)%f2ntb(iface,1)) = 1
        mark(bc(ibound)%f2ntb(iface,2)) = 1
        mark(bc(ibound)%f2ntb(iface,3)) = 1
      end do

!     quad faces

      do iface = 1, bc(ibound)%nbfaceq
        mark(bc(ibound)%f2nqb(iface,1)) = 1
        mark(bc(ibound)%f2nqb(iface,2)) = 1
        mark(bc(ibound)%f2nqb(iface,3)) = 1
        mark(bc(ibound)%f2nqb(iface,4)) = 1
      end do

      if (present(istart)) then
         !write(*,*)'finder: (**) nnodesg,sum(mark) = ',nnodesg,sum(mark)
         do inode = 1,nnodesg
            if ((mark(inode)==1)                                         &
                .and. ((inode<istart).or.(inode>iend))) mark(inode) = 0
         end do
      end if

!     calculate the number of surface nodes flagged and allocate bc%ibnode

      bc(ibound)%nbnode = sum(mark)

      call my_alloc_ptr( bc(ibound)%ibnode, bc(ibound)%nbnode )

!     fill the bc(ibound)%ibnode array

      bc(ibound)%nbnode = 0

      do inode = 1, nnodesg

        if (mark(inode)==1) then

            bc(ibound)%nbnode = bc(ibound)%nbnode + 1
            bc(ibound)%ibnode(bc(ibound)%nbnode) = inode

!           set mark to a vol node to surface node pointer

            mark(inode) = bc(ibound)%nbnode

        end if

      end do

      if (.not.present(istart)) then

!     use mark as a vol node to surface node pointer to set f2ntb and
!     f2nqb to index the bc(ibound)%ibnode array

!     tria faces

      do iface = 1, bc(ibound)%nbfacet
        bc(ibound)%f2ntb(iface,1) = mark(bc(ibound)%f2ntb(iface,1))
        bc(ibound)%f2ntb(iface,2) = mark(bc(ibound)%f2ntb(iface,2))
        bc(ibound)%f2ntb(iface,3) = mark(bc(ibound)%f2ntb(iface,3))
      end do

!     quad faces

      do iface = 1, bc(ibound)%nbfaceq
        bc(ibound)%f2nqb(iface,1) = mark(bc(ibound)%f2nqb(iface,1))
        bc(ibound)%f2nqb(iface,2) = mark(bc(ibound)%f2nqb(iface,2))
        bc(ibound)%f2nqb(iface,3) = mark(bc(ibound)%f2nqb(iface,3))
        bc(ibound)%f2nqb(iface,4) = mark(bc(ibound)%f2nqb(iface,4))
      end do

      end if ! (.not.present(istart))

      if (verbose) write(*,*)                                                  &
                            ' boundary ', ibound, ' has ',  bc(ibound)%nbnode, &
                            ' nodes ibc = ',bc(ibound)%ibc

    end do boundary_instance

!   set mark back to zero for safety elsewhere in code

    mark = 0

  end subroutine finder

!============================== COUNTSUREL ===================================80
!
! Counts number of elements surrounding each node/point
!
!=============================================================================80

  subroutine countsurel( ncell, node_per_cell, c2n, nnodesg, locs, icall,      &
                         nelem)

    integer,                                 intent(in)    :: ncell
    integer,                                 intent(in)    :: node_per_cell
    integer,                                 intent(in)    :: icall
    integer,                                 intent(in)    :: nelem
    integer,                                 intent(in)    :: nnodesg
    integer, dimension(nnodesg+1),           intent(inout) :: locs
    integer, dimension(node_per_cell,ncell), intent(in)    :: c2n

    integer :: i, j, ip

    continue

!   locs(nnodesg+1)    - pointer from nodes to adjacent cells in locvc

!   this sub counts the number of elements surrounding each node

!   zero out integer vector loc for nnodesg + 1 vector elements
!   (first call only)

    if (icall == 1) then
      locs = 0
    end if

!   find number of times each gridpoint appears in an element
!   (same as number of elements surrounding a grid point and
!   store ahead)

    do i=1,ncell
       do j=1,node_per_cell
          ip = c2n(j,i) + 1
          locs(ip) = locs(ip) + 1
       end do
    end do

!   sum elements in loc vector so that all elements surrounding
!   points can be stored in a 1-d vector
!   (must be done only after all element types have been processed)

    if (icall == nelem) then

       do i=2,nnodesg+1
          locs(i) = locs(i) + locs(i-1)
       end do

     end if

  end subroutine countsurel


!================================ SUREL ======================================80
!
! Finds elements surrounding each node/point
!
!=============================================================================80

  subroutine surel( ncell, node_per_cell, c2n, nnodesg, nloc, locs, locvc,     &
                    locvc_type, ielem, nelem, verbose )

    integer,                                     intent(in)    :: ncell
    integer,                                     intent(in)    :: node_per_cell
    integer,                                     intent(in)    :: ielem
    integer,                                     intent(in)    :: nelem
    integer,                                     intent(in)    :: nnodesg, nloc
    integer,          dimension(nnodesg+1),      intent(inout) :: locs
    integer,          dimension(nloc),           intent(inout) :: locvc
    integer,          dimension(nloc),           intent(inout) :: locvc_type
    integer,     dimension(node_per_cell,ncell), intent(in)    :: c2n
    logical,                                     intent(in)    :: verbose

    integer :: i, j, ip, is

    continue

!   locs(nnodesg+1)   - pointer from nodes to adjacent cells in locvc
!   locvc(nloc)      - list of cells about each node
!   locvc_type(nloc) -  list of type of cells about each node

!   this sub finds all elements (and their types) surrounding each node

!   ensure that there is plenty of room in vector locvc to store
!   all elements surrounding each point

    if (locs(nnodesg+1)>size(locvc,1) ) then

      write (*,*) 'error in surel nloc'
      write (*,*) 'locvc not allocated large enough, stopping...'
      write (*,*) ' size(locvc,1) ', size(locvc,1),                            &
                    ' locs(nnodesg+1) ', locs(nnodesg+1)
      stop ! FIXME: should be lmpi_die or se_exit(1)?

    end if

!   store cells in locvc, store their type in locvc_type

    do i=1,ncell
       do j=1,node_per_cell
          ip = c2n(j,i)
          is = locs(ip) + 1
          locs(ip) = is
          locvc(is) = i
          locvc_type(is) = ielem
       end do
    end do

!   reorder loc (must do only after all cell types have been processed)

    if (ielem == nelem) then

      do i=nnodesg+1,2,-1
         locs(i) = locs(i-1)
      end do

      locs(1) = 0

      if (verbose) write (*,*)'surel locs(nnodesg+1) = ',locs(nnodesg+1)

    end if

  end subroutine surel

end module pparty_puns3d

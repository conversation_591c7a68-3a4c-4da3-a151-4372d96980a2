! Routines used for load balancing an adapted grid

module load_balance

  implicit none

  private

  public :: migrator_type, migrator_from_grid, migrate_elem, migrate_l2g
  public :: migrated_local, migrate_stencil, migrate_node
  public :: migrate_verify
  public :: update_ghosts

  interface migrate_node
    module procedure migrate_node_integer
    module procedure migrate_node_real_dp
    module procedure migrate_node_real_dp_2
    module procedure migrate_node_complex_dp
    module procedure migrate_node_complex_dp_2
  end interface

  type migrator_type
    integer :: old_nnodes0, old_nnodes01
    integer :: new_nnodes0, new_nnodes01
    integer, dimension(:), allocatable :: old_part
    integer, dimension(:), allocatable :: new_part
    integer, dimension(:), allocatable :: old_l2g

    integer, dimension(:), allocatable :: new_l2g
    integer, dimension(:), allocatable :: sorted_global
    integer, dimension(:), allocatable :: sorted_local
    integer, dimension(:), allocatable :: source_part

    integer :: nstatic
    integer, dimension(:), allocatable :: new_static
    integer, dimension(:), allocatable :: old_static
    integer, dimension(:), allocatable :: message_size_send, message_size_recv
    integer :: message_total_send, message_total_recv
    integer, dimension(:), allocatable :: local_send, local_recv
  end type migrator_type

contains

!============================== migrator_from_grid ===========================80
!=============================================================================80

  subroutine migrator_from_grid( migrator, nnodes0, nnodes01, part, l2g, grid )
    use lmpi, only : lmpi_nproc, lmpi_id, lmpi_allgather
    use parmetis_interface,  only : adaptiveRepart, partKway
    use grid_types, only : grid_type
    type(migrator_type),          intent(out)   :: migrator
    integer,                      intent(in)    :: nnodes0, nnodes01
    integer, dimension(nnodes01), intent(in)    :: part, l2g
    type(grid_type),              intent(in)    :: grid

    integer :: node, processor, my_offset
    integer :: node1, node2
    integer :: elem, cell, edge
    integer :: first_node, last_node

    integer, dimension(:), allocatable :: proc_nnodes0
    integer, dimension(:), allocatable :: node_offset
    integer, dimension(:), allocatable :: implied_global

    integer, dimension(:), allocatable :: degree
    integer, dimension(:), allocatable :: adjacency
    integer, dimension(:), allocatable :: row_start
    integer, dimension(:), allocatable :: edge_weight

    logical :: adaptive_repart

    logical :: new_connection
    integer :: conn

    continue

    migrator%old_nnodes0  = nnodes0
    migrator%old_nnodes01 = nnodes01

    allocate(migrator%old_part( migrator%old_nnodes01 ))
    copy_old_part : do node = 1, migrator%old_nnodes01
      migrator%old_part(node) = part(node)
    end do copy_old_part

    allocate(migrator%old_l2g( migrator%old_nnodes01 ))
    copy_old_l2g : do node = 1, migrator%old_nnodes01
      migrator%old_l2g(node) = l2g(node)
    end do copy_old_l2g

!metis
    allocate(proc_nnodes0( lmpi_nproc ))
    call lmpi_allgather(nnodes0, proc_nnodes0)
    allocate(node_offset( lmpi_nproc+1 ))
    node_offset(1) = 1
    convert_count_to_offest : do processor = 1, lmpi_nproc
      node_offset(processor+1) = node_offset(processor) + &
                                 proc_nnodes0(processor)
    end do convert_count_to_offest
    deallocate(proc_nnodes0)

    allocate(implied_global( nnodes01 ))
    implied_global = -1

    my_offset = node_offset(lmpi_id+1) - 1
    implied_global_with_offest : do node = 1, nnodes0
      implied_global(node) = my_offset + node
    end do implied_global_with_offest

    call update_ghosts( nnodes0, nnodes01, &
      l2g, part, implied_global )

    allocate(degree( grid%nnodes0 ))
    degree = 0
    count_elem : do elem = 1, grid%nelem
      count_cell : do cell = 1, grid%elem(elem)%ncell
        count_edge : do edge = 1, grid%elem(elem)%edge_per_cell
          node1 = grid%elem(elem)%c2n(grid%elem(elem)%local_e2n(edge,1),cell)
          node2 = grid%elem(elem)%c2n(grid%elem(elem)%local_e2n(edge,2),cell)
          count_node1_local : if ( node1 <= nnodes0 ) then
            degree(node1) = degree(node1)+1
          end if count_node1_local
          count_node2_local : if ( node2 <= nnodes0 ) then
            degree(node2) = degree(node2)+1
          end if count_node2_local
        end do count_edge
      end do count_cell
    end do count_elem

    allocate(row_start(nnodes0+1))
    row_start(1) = 1
    do node = 1, nnodes0
      row_start(node+1) = row_start(node) + degree(node)
    end do
    allocate(adjacency(row_start(nnodes0+1)))

    degree = 0
    unique_elem : do elem = 1, grid%nelem
      unique_cell : do cell = 1, grid%elem(elem)%ncell
        unique_edge : do edge = 1, grid%elem(elem)%edge_per_cell
          node1 = grid%elem(elem)%c2n(grid%elem(elem)%local_e2n(edge,1),cell)
          node2 = grid%elem(elem)%c2n(grid%elem(elem)%local_e2n(edge,2),cell)
          unique_node1_local : if ( node1 <= nnodes0 ) then
            new_connection = .true.
            unique_search_node1 : do conn = 1, degree(node1)
              if ( adjacency(row_start(node1)+conn-1) == &
                   implied_global(node2) ) new_connection = .false.
            end do unique_search_node1
            unique_add_node1 : if ( new_connection ) then
              adjacency(row_start(node1)+degree(node1)) = implied_global(node2)
              degree(node1) = degree(node1)+1
            end if unique_add_node1
          end if unique_node1_local
          unique_node2_local : if ( node2 <= nnodes0 ) then
            new_connection = .true.
            unique_search_node2 : do conn = 1, degree(node2)
              if ( adjacency(row_start(node2)+conn-1) == &
                   implied_global(node1) ) new_connection = .false.
            end do unique_search_node2
            unique_add_node2 : if ( new_connection ) then
              adjacency(row_start(node2)+degree(node2)) = implied_global(node1)
              degree(node2) = degree(node2)+1
            end if unique_add_node2
          end if unique_node2_local
        end do unique_edge
      end do unique_cell
    end do unique_elem

    row_start(1) = 1
    do node = 1, nnodes0
      row_start(node+1) = row_start(node) + degree(node)
    end do
    deallocate(adjacency)
    allocate(adjacency(row_start(nnodes0+1)-1))

    degree = 0
    fill_elem : do elem = 1, grid%nelem
      fill_cell : do cell = 1, grid%elem(elem)%ncell
        fill_edge : do edge = 1, grid%elem(elem)%edge_per_cell
          node1 = grid%elem(elem)%c2n(grid%elem(elem)%local_e2n(edge,1),cell)
          node2 = grid%elem(elem)%c2n(grid%elem(elem)%local_e2n(edge,2),cell)
          fill_node1_local : if ( node1 <= nnodes0 ) then
            new_connection = .true.
            fill_search_node1 : do conn = 1, degree(node1)
              if ( adjacency(row_start(node1)+conn-1) == &
                   implied_global(node2) ) new_connection = .false.
            end do fill_search_node1
            fill_add_node1 : if ( new_connection ) then
              adjacency(row_start(node1)+degree(node1)) = implied_global(node2)
              degree(node1) = degree(node1)+1
            end if fill_add_node1
          end if fill_node1_local
          fill_node2_local : if ( node2 <= nnodes0 ) then
            new_connection = .true.
            fill_search_node2 : do conn = 1, degree(node2)
              if ( adjacency(row_start(node2)+conn-1) == &
                   implied_global(node1) ) new_connection = .false.
            end do fill_search_node2
            fill_add_node2 : if ( new_connection ) then
              adjacency(row_start(node2)+degree(node2)) = implied_global(node1)
              degree(node2) = degree(node2)+1
            end if fill_add_node2
          end if fill_node2_local
        end do fill_edge
      end do fill_cell
    end do fill_elem

    deallocate( degree )
    deallocate( implied_global )


    allocate( edge_weight( row_start(nnodes0+1)-1 ) )

    first_node = my_offset + 1
    last_node = my_offset + nnodes0
    edge_weight = 1
    do edge = 1, row_start(nnodes0+1)-1
      if ( adjacency(edge) < first_node .or. &
           adjacency(edge) > last_node ) then
        edge_weight( edge ) = 1000
      end if
    end do

    allocate(migrator%new_part( migrator%old_nnodes01 ))
    migrator%new_part(1:migrator%old_nnodes0) = lmpi_id + 1

    adaptive_repart = .false.

    if (adaptive_repart) then
      call adaptiveRepart( lmpi_nproc, node_offset,                         &
                           migrator%old_nnodes0, row_start, adjacency,      &
                           migrator%new_part )
    else
      call partKway( lmpi_nproc, node_offset,                               &
                     migrator%old_nnodes0, row_start, adjacency,            &
                     migrator%new_part, edge_weight=edge_weight )
    end if

    deallocate( edge_weight )
    deallocate( node_offset )
    deallocate(adjacency)

! metis does not update ghosts
    call update_ghosts( migrator%old_nnodes0, migrator%old_nnodes01, &
      migrator%old_l2g, migrator%old_part, migrator%new_part )


  end subroutine migrator_from_grid

!============================== migrate_elem =================================80
!=============================================================================80

  subroutine migrate_elem( migrator, node_per_cell, ncell, c2n )
    use lmpi, only : lmpi_nproc, lmpi_id, lmpi_alltoall, lmpi_alltoallv,&
      lmpi_master, lmpi_alltoallv2
    use allocations, only : my_realloc_ptr
    type(migrator_type), intent(inout) :: migrator
    integer, intent(in) :: node_per_cell
    integer, intent(inout) :: ncell
    integer, dimension(:,:), pointer :: c2n ! intent(inout) when F2008 arrives


    integer, dimension(:), allocatable :: message_size_1, message_size_2

    integer :: my_part, cell, owner_part, cell_node, destination
    integer :: previous_node, previous_part

    integer, dimension(:), allocatable :: next_position

    integer :: message_total_1, message_total_2

    integer, dimension(:,:), allocatable :: message_body_1, message_body_2
    integer, dimension(:,:), allocatable :: message_source_1, message_source_2

    integer, dimension(:,:), allocatable :: test_message_body_2
    integer :: test_i

    integer :: processor, new_cell

    logical :: keep_cell

    continue

    my_part = lmpi_id + 1

    allocate(message_size_1(lmpi_nproc))
    allocate(message_size_2(lmpi_nproc))
    message_size_1 = 0
    message_size_2 = 0

    count_cell : do cell = 1, ncell
      owner_part = migrator%old_part(c2n(1,cell))
      count_own_this_cell : if ( my_part == owner_part ) then
        count_cell_node : do cell_node = 1, node_per_cell
          destination = migrator%new_part(c2n(cell_node,cell))
          count_has_destination : if (destination /= my_part) then
            count_prev_node : do previous_node = 1, cell_node - 1
              previous_part = migrator%new_part(c2n(previous_node,cell))
              if ( destination == previous_part ) cycle count_cell_node
            end do count_prev_node
            message_size_1(destination)=message_size_1(destination)+1
          end if count_has_destination
        end do count_cell_node
      end if count_own_this_cell
    end do count_cell

    call lmpi_alltoall( message_size_1, message_size_2 )

    message_total_1 = sum( message_size_1 )
    message_total_2 = sum( message_size_2 )

    allocate( message_body_1(node_per_cell, message_total_1) )
    allocate( message_body_2(node_per_cell, message_total_2) )
    allocate( message_source_1(node_per_cell, message_total_1) )
    allocate( message_source_2(node_per_cell, message_total_2) )

    allocate(next_position(lmpi_nproc+1))

    next_position = 0
    next_position(1) = 1
    calc_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + message_size_1(processor)
    end do calc_next_position

    fill_cell : do cell = 1, ncell
      owner_part = migrator%old_part(c2n(1,cell))
      fill_own_this_cell : if ( my_part == owner_part ) then
        fill_cell_node : do cell_node = 1, node_per_cell
          destination = migrator%new_part(c2n(cell_node,cell))
          fill_has_destination : if (destination /= my_part) then
            fill_prev_node : do previous_node = 1, cell_node - 1
              previous_part = migrator%new_part(c2n(previous_node,cell))
              if ( destination == previous_part ) cycle fill_cell_node
            end do fill_prev_node
            message_body_1(:,next_position(destination)) = &
              migrator%old_l2g(c2n(:,cell))
            message_source_1(:,next_position(destination)) = &
              migrator%old_part(c2n(:,cell))
            next_position(destination) = next_position(destination) +1
          end if fill_has_destination
        end do fill_cell_node
      end if fill_own_this_cell
    end do fill_cell

    deallocate(next_position)

    call lmpi_alltoallv( message_body_1, message_size_1, &
                         message_body_2, message_size_2 )

    test_v2 : if ( .false. ) then
      if (lmpi_master) print *, "test lmpi_alltoallv2"
      allocate( test_message_body_2(node_per_cell, message_total_2) )

      call lmpi_alltoallv2( message_body_1, message_size_1, &
                       test_message_body_2, message_size_2 )
      do test_i = 1, message_total_2
        do cell_node = 1, node_per_cell
          if (   message_body_2(cell_node,test_i) /= &
            test_message_body_2(cell_node,test_i) ) then
            print *, lmpi_id,cell_node, test_i,&
              message_body_2(cell_node,test_i),&
              test_message_body_2(cell_node,test_i)
          end if
        end do
      end do
      deallocate(test_message_body_2)

    end if test_v2

    call lmpi_alltoallv( message_source_1, message_size_1, &
                         message_source_2, message_size_2 )

    deallocate(message_size_1)
    deallocate(message_size_2)
    deallocate(message_body_1)
    deallocate(message_source_1)

    new_cell = 0
    compress_static_cells : do cell = 1, ncell
      owner_part = migrator%old_part(c2n(1,cell))
      compress_own_this_cell : if ( my_part == owner_part ) then
        keep_cell = .false.
        compress_cell_nodes : do cell_node = 1, node_per_cell
          destination = migrator%new_part(c2n(cell_node,cell))
          keep_cell = ( keep_cell .or. (my_part==destination) )
        end do compress_cell_nodes
        compress_keep_cell : if (keep_cell) then
          new_cell = new_cell + 1
          map_cell_nodes : do cell_node = 1, node_per_cell
            c2n(cell_node,new_cell) = migrated_local( migrator,       &
                             migrator%old_l2g( c2n(cell_node,cell) ), &
                             migrator%old_part( c2n(cell_node,cell) ) )
          end do map_cell_nodes
        end if compress_keep_cell
      end if compress_own_this_cell
    end do compress_static_cells

    resize : if ( ncell /= new_cell + message_total_2 ) then
      call my_realloc_ptr( c2n, node_per_cell, new_cell+message_total_2 )
    end if resize

    merge_migrated_cells : do cell = 1, message_total_2
      new_cell = new_cell + 1
      map_migrated_cell_nodes : do cell_node = 1, node_per_cell
        c2n(cell_node,new_cell) = migrated_local( migrator,                &
                                          message_body_2(cell_node,cell),  &
                                          message_source_2(cell_node,cell) )
      end do map_migrated_cell_nodes
    end do merge_migrated_cells
    deallocate(message_body_2)
    deallocate(message_source_2)

    ncell = new_cell

  end subroutine migrate_elem

!============================== migrated_local ===============================80
!=============================================================================80

  function migrated_local( migrator, global, source )
    use sort, only : lookup

    integer                            :: migrated_local
    type(migrator_type), intent(inout) :: migrator
    integer,             intent(in)    :: global, source

    integer :: local, oldsize, newsize

    integer, dimension(:), allocatable :: temp

    integer :: insert_point, indx

    continue

    local = lookup(global, migrator%new_nnodes01, &
      migrator%sorted_global, migrator%sorted_local )

    node_exists : if ( local > 0 ) then
      migrated_local  = local
      return
    end if node_exists

    local = migrator%new_nnodes01 + 1

    oldsize = size(migrator%new_l2g,1)
    resize_bigger : if ( local > oldsize ) then
      newsize = oldsize + 5000

      allocate(temp(oldsize))

      temp = migrator%new_l2g
      deallocate(migrator%new_l2g)
      allocate(migrator%new_l2g(newsize))
      migrator%new_l2g(1:oldsize) = temp

      temp = migrator%sorted_global
      deallocate(migrator%sorted_global)
      allocate(migrator%sorted_global(newsize))
      migrator%sorted_global(1:oldsize) = temp

      temp = migrator%sorted_local
      deallocate(migrator%sorted_local)
      allocate(migrator%sorted_local(newsize))
      migrator%sorted_local(1:oldsize) = temp

      temp = migrator%source_part
      deallocate(migrator%source_part)
      allocate(migrator%source_part(newsize))
      migrator%source_part(1:oldsize) = temp

      deallocate(temp)
    end if resize_bigger

    insert_point = migrator%new_nnodes01 + 1
    find_insert_point : do indx = 1, migrator%new_nnodes01
      if (migrator%sorted_global(indx) > global) then
        insert_point = indx
        exit find_insert_point
      end if
    end do find_insert_point

    slide_global_up : do indx = migrator%new_nnodes01, insert_point, -1
      migrator%sorted_global(indx+1) = migrator%sorted_global(indx)
    end do slide_global_up
    slide_local_up : do indx = migrator%new_nnodes01, insert_point, -1
      migrator%sorted_local(indx+1) = migrator%sorted_local(indx)
    end do slide_local_up

    migrator%new_nnodes01 = migrator%new_nnodes01 + 1
    migrator%new_l2g(local) = global
    migrator%sorted_global(insert_point) = global
    migrator%sorted_local(insert_point) = local
    migrator%source_part(local) = source

    migrated_local  = local

  end function migrated_local
!============================== migrate_l2g ==================================80
!=============================================================================80

  subroutine migrate_l2g( migrator )
    use lmpi, only : lmpi_nproc, lmpi_id, lmpi_alltoall, lmpi_alltoallv
    use sort, only : set_up_lookup
    type(migrator_type), intent(inout) :: migrator

    integer, dimension(:), allocatable :: message_size_1, message_size_2

    integer :: my_part, node, destination

    integer, dimension(:), allocatable :: next_position

    integer :: message_total_1, message_total_2

    integer, dimension(:), allocatable :: message_body_1, message_body_2

    integer :: processor, nnodes0, recv_node

    continue

    my_part = lmpi_id + 1

    allocate(message_size_1(lmpi_nproc))
    allocate(message_size_2(lmpi_nproc))
    message_size_1 = 0
    message_size_2 = 0

    count_l2g : do node = 1, migrator%old_nnodes0
      destination = migrator%new_part(node)
      count_has_destination : if (destination /= my_part) then
        message_size_1(destination)=message_size_1(destination)+1
      end if count_has_destination
    end do count_l2g

    call lmpi_alltoall( message_size_1, message_size_2 )

    message_total_1 = sum( message_size_1 )
    message_total_2 = sum( message_size_2 )

    allocate( message_body_1(message_total_1) )
    allocate( message_body_2(message_total_2) )

    allocate(next_position(lmpi_nproc+1))

    next_position = 0
    next_position(1) = 1
    calc_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                      + message_size_1(processor)
    end do calc_next_position

    fill_l2g : do node = 1, migrator%old_nnodes0
      destination = migrator%new_part(node)
      fill_has_destination : if (destination /= my_part) then
        message_body_1(next_position(destination)) = &
          migrator%old_l2g(node)
        next_position(destination) = next_position(destination) +1
      end if fill_has_destination
    end do fill_l2g

    deallocate(next_position)

    call lmpi_alltoallv( message_body_1, message_size_1, &
                         message_body_2, message_size_2 )

    deallocate(message_size_1)
    deallocate(message_body_1)

    nnodes0 = 0
    count_static_nodes : do node = 1, migrator%old_nnodes0
      destination = migrator%new_part(node)
      count_static : if (destination == my_part) then
        nnodes0 = nnodes0 + 1
      end if count_static
    end do count_static_nodes

    allocate( migrator%new_l2g( nnodes0 + message_total_2 ) )
    allocate( migrator%sorted_global( nnodes0 + message_total_2 ) )
    allocate( migrator%sorted_local( nnodes0 + message_total_2 ) )
    allocate( migrator%source_part( nnodes0 + message_total_2 ) )

    migrator%new_nnodes0 = 0

    compress_static_nodes : do node = 1, migrator%old_nnodes0
      destination = migrator%new_part(node)
      compress_static : if (destination == my_part) then
        migrator%new_nnodes0 = migrator%new_nnodes0 + 1
        migrator%new_l2g( migrator%new_nnodes0 ) = &
          migrator%old_l2g(node)
        migrator%source_part( migrator%new_nnodes0 ) = my_part
      end if compress_static
    end do compress_static_nodes

    node = 0
    each_source_proc : do processor = 1, lmpi_nproc
      each_node_from_proc : do recv_node = 1, message_size_2(processor)
        node = node + 1
        migrator%new_nnodes0 = migrator%new_nnodes0 + 1
        migrator%new_l2g( migrator%new_nnodes0 ) = &
          message_body_2(node)
        migrator%source_part( migrator%new_nnodes0 ) = processor
      end do each_node_from_proc
    end do each_source_proc

    deallocate(message_size_2)
    deallocate(message_body_2)

    migrator%new_nnodes01 = migrator%new_nnodes0

    call set_up_lookup( migrator%new_nnodes0, migrator%new_l2g, &
      migrator%sorted_global, migrator%sorted_local )

  end subroutine migrate_l2g

!============================== migrate_stencil ==============================80
!=============================================================================80

  subroutine migrate_stencil( migrator )
    use sort, only : set_up_lookup, lookup
    use lmpi, only : lmpi_nproc, lmpi_id, lmpi_alltoall, lmpi_alltoallv
    type(migrator_type), intent(inout) :: migrator

    integer :: my_part, owner_part
    integer :: node, processor

    integer, dimension(:), allocatable :: old_sorted_global
    integer, dimension(:), allocatable :: old_sorted_local
    integer, dimension(:), allocatable :: next_position
    integer, dimension(:), allocatable :: global_send, global_recv

    continue

    my_part = lmpi_id + 1

    allocate(migrator%message_size_send(lmpi_nproc))
    allocate(migrator%message_size_recv(lmpi_nproc))
    migrator%message_size_send = 0
    migrator%message_size_recv = 0

    migrator%nstatic = 0
    count_static_nodes : do node = 1, migrator%new_nnodes01
      owner_part = migrator%source_part(node)
      count_as_static : if ( my_part == owner_part ) then
        migrator%nstatic = migrator%nstatic + 1
      end if count_as_static
    end do count_static_nodes

    allocate( migrator%old_static(migrator%nstatic) )
    allocate( migrator%new_static(migrator%nstatic) )

    allocate( old_sorted_global( migrator%old_nnodes0 ) )
    allocate( old_sorted_local( migrator%old_nnodes0 ) )
    call set_up_lookup( migrator%old_nnodes0, migrator%old_l2g, &
      old_sorted_global, old_sorted_local )

    migrator%nstatic = 0
    fill_static_nodes : do node = 1, migrator%new_nnodes01
      owner_part = migrator%source_part(node)
      fill_as_static : if ( my_part == owner_part ) then
        migrator%nstatic = migrator%nstatic + 1
        migrator%new_static(migrator%nstatic) = node
        migrator%old_static(migrator%nstatic) = lookup( migrator%new_l2g(node),&
          migrator%old_nnodes0, old_sorted_global, old_sorted_local )
      end if fill_as_static
    end do fill_static_nodes

    count_recv : do node = 1, migrator%new_nnodes01
      owner_part = migrator%source_part(node)
      count_needed : if ( my_part /= owner_part ) then
        migrator%message_size_recv(owner_part) = &
          migrator%message_size_recv(owner_part)+1
      end if count_needed
    end do count_recv

    call lmpi_alltoall( migrator%message_size_recv, migrator%message_size_send )

    migrator%message_total_recv = sum( migrator%message_size_recv )
    migrator%message_total_send = sum( migrator%message_size_send )

    allocate( migrator%local_recv(migrator%message_total_recv) )
    allocate( migrator%local_send(migrator%message_total_send) )

    allocate( global_recv(migrator%message_total_recv) )
    allocate( global_send(migrator%message_total_send) )

    allocate(next_position(lmpi_nproc+1))

    next_position = 0
    next_position(1) = 1
    calc_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + migrator%message_size_recv(processor)
    end do calc_next_position

    fill_recv : do node = 1, migrator%new_nnodes01
      owner_part = migrator%source_part(node)
      fill_needed : if ( my_part /= owner_part ) then
        migrator%local_recv(next_position(owner_part)) = node
        global_recv(next_position(owner_part)) = migrator%new_l2g(node)
        next_position(owner_part) = next_position(owner_part) + 1
      end if fill_needed
    end do fill_recv

    deallocate(next_position)

    call lmpi_alltoallv( global_recv, migrator%message_size_recv, &
                         global_send, migrator%message_size_send )

    map_send : do node = 1, migrator%message_total_send
      migrator%local_send(node) = lookup( global_send(node),&
          migrator%old_nnodes0, old_sorted_global, old_sorted_local )
    end do map_send

    deallocate( global_recv )
    deallocate( global_send )

    deallocate( old_sorted_global )
    deallocate( old_sorted_local )

  end subroutine migrate_stencil

!============================== migrate_node_* ===============================80
!=============================================================================80

  subroutine migrate_node_integer( migrator, data )

    use allocations, only : my_realloc_ptr

    use lmpi, only : lmpi_alltoallv

    type(migrator_type), intent(in) :: migrator
    integer, dimension(:), pointer :: data ! intent(inout) when F2008 arrives
    integer, dimension(:), allocatable :: static_data
    integer :: node
    integer, dimension(:), allocatable :: message_body_send, message_body_recv
    continue

    allocate( message_body_send(migrator%message_total_send) )
    allocate( message_body_recv(migrator%message_total_recv) )

    pack_send_data : do node = 1, migrator%message_total_send
      message_body_send( node ) = data( migrator%local_send( node ) )
    end do pack_send_data

    call lmpi_alltoallv( message_body_send, migrator%message_size_send, &
                         message_body_recv, migrator%message_size_recv )

    allocate( static_data( migrator%nstatic ) )
    pack_static_data : do node = 1, migrator%nstatic
      static_data( node ) = data( migrator%old_static(node) )
    end do pack_static_data

    call my_realloc_ptr( data, migrator%new_nnodes01 )

    copy_static_data : do node = 1, migrator%nstatic
      data(  migrator%new_static(node) ) = static_data( node )
    end do copy_static_data
    deallocate( static_data )

    unpack_recv_data : do node = 1, migrator%message_total_recv
      data( migrator%local_recv( node ) ) = message_body_recv( node )
    end do unpack_recv_data

    deallocate( message_body_send )
    deallocate( message_body_recv )

  end subroutine migrate_node_integer

  subroutine migrate_node_real_dp( migrator, data )

    use kinddefs,        only : dp

    use allocations, only : my_realloc_ptr

    use lmpi, only : lmpi_alltoallv

    type(migrator_type), intent(in) :: migrator
    real(dp), dimension(:), pointer :: data ! intent(inout) with F2008
    real(dp), dimension(:), allocatable :: static_data
    integer :: node
    real(dp), dimension(:), allocatable :: message_body_send
    real(dp), dimension(:), allocatable :: message_body_recv
    continue

    allocate( message_body_send(migrator%message_total_send) )
    allocate( message_body_recv(migrator%message_total_recv) )

    pack_send_data : do node = 1, migrator%message_total_send
      message_body_send( node ) = data( migrator%local_send( node ) )
    end do pack_send_data

    call lmpi_alltoallv( message_body_send, migrator%message_size_send, &
                         message_body_recv, migrator%message_size_recv )

    allocate( static_data( migrator%nstatic ) )
    pack_static_data : do node = 1, migrator%nstatic
      static_data( node ) = data( migrator%old_static(node) )
    end do pack_static_data

    call my_realloc_ptr( data, migrator%new_nnodes01 )

    copy_static_data : do node = 1, migrator%nstatic
      data(  migrator%new_static(node) ) = static_data( node )
    end do copy_static_data
    deallocate( static_data )

    unpack_recv_data : do node = 1, migrator%message_total_recv
      data( migrator%local_recv( node ) ) = message_body_recv( node )
    end do unpack_recv_data

    deallocate( message_body_send )
    deallocate( message_body_recv )

  end subroutine migrate_node_real_dp

  subroutine migrate_node_real_dp_2( migrator, data )

    use kinddefs,        only : dp

    use allocations, only : my_realloc_ptr

    use lmpi, only : lmpi_alltoallv

    type(migrator_type), intent(in) :: migrator
    real(dp), dimension(:,:), pointer :: data ! intent(inout) with F2008
    real(dp), dimension(:,:), allocatable :: static_data
    integer :: dmn, node, inner
    real(dp), dimension(:,:), allocatable :: message_body_send
    real(dp), dimension(:,:), allocatable :: message_body_recv
    continue

    dmn = size( data, 1 )

    allocate( message_body_send(dmn,migrator%message_total_send) )
    allocate( message_body_recv(dmn,migrator%message_total_recv) )

    pack_send_data : do node = 1, migrator%message_total_send
      do inner = 1, dmn
        message_body_send( inner, node ) = &
          data( inner, migrator%local_send( node ) )
      end do
    end do pack_send_data

    call lmpi_alltoallv( message_body_send, migrator%message_size_send, &
                         message_body_recv, migrator%message_size_recv )

    allocate( static_data( dmn, migrator%nstatic ) )
    pack_static_data : do node = 1, migrator%nstatic
      do inner = 1, dmn
        static_data( inner, node ) = data( inner, migrator%old_static(node) )
      end do
    end do pack_static_data

    call my_realloc_ptr( data, dmn, migrator%new_nnodes01 )

    copy_static_data : do node = 1, migrator%nstatic
      do inner = 1, dmn
        data( inner, migrator%new_static(node) ) = static_data( inner, node )
      end do
    end do copy_static_data
    deallocate( static_data )

    unpack_recv_data : do node = 1, migrator%message_total_recv
      do inner = 1, dmn
        data( inner, migrator%local_recv( node ) ) = &
          message_body_recv( inner, node )
      end do
    end do unpack_recv_data

    deallocate( message_body_send )
    deallocate( message_body_recv )

  end subroutine migrate_node_real_dp_2

  subroutine migrate_node_complex_dp( migrator, data )

    use kinddefs,        only : dp

    use allocations, only : my_realloc_ptr

    use lmpi, only : lmpi_alltoallv

    type(migrator_type), intent(in) :: migrator
    complex(dp), dimension(:), pointer :: data ! intent(inout) with F2008
    complex(dp), dimension(:), allocatable :: static_data
    integer :: node
    complex(dp), dimension(:), allocatable :: message_body_send
    complex(dp), dimension(:), allocatable :: message_body_recv
    continue

    allocate( message_body_send(migrator%message_total_send) )
    allocate( message_body_recv(migrator%message_total_recv) )

    pack_send_data : do node = 1, migrator%message_total_send
      message_body_send( node ) = data( migrator%local_send( node ) )
    end do pack_send_data

    call lmpi_alltoallv( message_body_send, migrator%message_size_send, &
                         message_body_recv, migrator%message_size_recv )

    allocate( static_data( migrator%nstatic ) )
    pack_static_data : do node = 1, migrator%nstatic
      static_data( node ) = data( migrator%old_static(node) )
    end do pack_static_data

    call my_realloc_ptr( data, migrator%new_nnodes01 )

    copy_static_data : do node = 1, migrator%nstatic
      data(  migrator%new_static(node) ) = static_data( node )
    end do copy_static_data
    deallocate( static_data )

    unpack_recv_data : do node = 1, migrator%message_total_recv
      data( migrator%local_recv( node ) ) = message_body_recv( node )
    end do unpack_recv_data

    deallocate( message_body_send )
    deallocate( message_body_recv )

  end subroutine migrate_node_complex_dp

  subroutine migrate_node_complex_dp_2( migrator, data )

    use kinddefs,        only : dp

    use allocations, only : my_realloc_ptr

    use lmpi, only : lmpi_alltoallv

    type(migrator_type), intent(in) :: migrator
    complex(dp), dimension(:,:), pointer :: data ! intent(inout) with F2008
    complex(dp), dimension(:,:), allocatable :: static_data
    integer :: dmn, node, inner
    complex(dp), dimension(:,:), allocatable :: message_body_send
    complex(dp), dimension(:,:), allocatable :: message_body_recv
    continue

    dmn = size( data, 1 )

    allocate( message_body_send(dmn,migrator%message_total_send) )
    allocate( message_body_recv(dmn,migrator%message_total_recv) )

    pack_send_data : do node = 1, migrator%message_total_send
      do inner = 1, dmn
        message_body_send( inner, node ) = &
          data( inner, migrator%local_send( node ) )
      end do
    end do pack_send_data

    call lmpi_alltoallv( message_body_send, migrator%message_size_send, &
                         message_body_recv, migrator%message_size_recv )

    allocate( static_data( dmn, migrator%nstatic ) )
    pack_static_data : do node = 1, migrator%nstatic
      do inner = 1, dmn
        static_data( inner, node ) = data( inner, migrator%old_static(node) )
      end do
    end do pack_static_data

    call my_realloc_ptr( data, dmn, migrator%new_nnodes01 )

    copy_static_data : do node = 1, migrator%nstatic
      do inner = 1, dmn
        data( inner, migrator%new_static(node) ) = static_data( inner, node )
      end do
    end do copy_static_data
    deallocate( static_data )

    unpack_recv_data : do node = 1, migrator%message_total_recv
      do inner = 1, dmn
        data( inner, migrator%local_recv( node ) ) = &
          message_body_recv( inner, node )
      end do
    end do unpack_recv_data

    deallocate( message_body_send )
    deallocate( message_body_recv )

  end subroutine migrate_node_complex_dp_2

!============================== update_ghosts ================================80
!=============================================================================80
  subroutine update_ghosts( nnodes0, nnodes01, l2g, part, data )
    use sort, only : set_up_lookup, lookup
    use lmpi, only : lmpi_nproc, lmpi_alltoall, lmpi_alltoallv, &
                     lmpi_conditional_stop
    integer, intent(in) :: nnodes0, nnodes01
    integer, dimension(nnodes01), intent(inout) :: data
    integer, dimension(nnodes01), intent(in) :: l2g, part

    integer :: node, processor, local

    integer, dimension(:), allocatable :: message_size_1, message_size_2
    integer :: message_total_1, message_total_2
    integer, dimension(:), allocatable :: message_body_1, message_body_2

    integer, dimension(:), allocatable :: next_position
    integer, dimension(:), allocatable :: sorted_global, sorted_local

    continue

    allocate(message_size_1(lmpi_nproc))
    allocate(message_size_2(lmpi_nproc))
    message_size_1 = 0
    message_size_2 = 0

    count_ghost : do node = nnodes0+1, nnodes01
      message_size_1(part(node))=message_size_1(part(node))+1
    end do count_ghost

    call lmpi_alltoall( message_size_1, message_size_2 )

    message_total_1 = sum( message_size_1 )
    message_total_2 = sum( message_size_2 )

    allocate(message_body_1(message_total_1))
    allocate(message_body_2(message_total_2))
    allocate(next_position(lmpi_nproc+1))

    next_position = 0
    next_position(1) = 1
    pack_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + message_size_1(processor)
    end do pack_next_position

    pack_ghost : do node = nnodes0+1, nnodes01
      processor = part(node)
      message_body_1(next_position(processor)) = l2g(node)
      next_position(processor) = next_position(processor) + 1
    end do pack_ghost

    call lmpi_alltoallv( message_body_1, message_size_1, &
                         message_body_2, message_size_2 )

    allocate( sorted_global( nnodes0 ) )
    allocate( sorted_local( nnodes0 ) )
    call set_up_lookup( nnodes0, l2g, sorted_global, sorted_local )

    do node = 1, message_total_2
      local = lookup(message_body_2(node),nnodes0, sorted_global, sorted_local )
      if (0 == local) then
        call lmpi_conditional_stop(1,'update_ghosts, missing node')
      end if
      message_body_2(node) = data( local )
    end do
    call lmpi_conditional_stop(0,'update_ghosts, missing node')

    deallocate(sorted_global)
    deallocate(sorted_local)

    call lmpi_alltoallv( message_body_2, message_size_2, &
                         message_body_1, message_size_1 )

    next_position = 0
    next_position(1) = 1
    unpack_next_position : do processor = 1, lmpi_nproc
      next_position(processor+1) = next_position(processor) &
                                 + message_size_1(processor)
    end do unpack_next_position

    unpack_ghost : do node = nnodes0+1, nnodes01
      processor = part(node)
      data(node) = message_body_1(next_position(processor))
      next_position(processor) = next_position(processor) + 1
    end do unpack_ghost

    deallocate(next_position)
    deallocate(message_size_1)
    deallocate(message_size_2)

  end subroutine update_ghosts

!============================== migrate_verify ===============================80
!=============================================================================80

  subroutine migrate_verify( migrator, l2g )

    use lmpi, only : lmpi_id, lmpi_nproc, lmpi_conditional_stop

    type(migrator_type), intent(in) :: migrator
    integer, dimension(:), pointer :: l2g ! intent(in) when F2008 arrives
    integer :: node

    integer :: problem

    continue


    problem = 0
    do node = 1, migrator%new_nnodes01
      if ( l2g(node) /= migrator%new_l2g(node) ) then
        write(*,'("proc ",i6," node ",i6," of ",2i6," l2g ",2i8)')&
          lmpi_id,                                                &
          node, migrator%new_nnodes0, migrator%new_nnodes01,      &
          l2g(node), migrator%new_l2g(node)
        problem = 1
      end if
    end do

    if ( problem /= 0 .and. &
      lmpi_nproc < 100 .and. migrator%new_nnodes01 < 10000 ) then
      do node = 1,  migrator%new_nnodes01
        write(300+lmpi_id,*) migrator%new_l2g(node), l2g(node), node, 'l2g'
      end do
      do node = 1,  migrator%nstatic
        write(300+lmpi_id,*) migrator%new_static(node), &
          migrator%old_static(node), node, 'static'
      end do
      do node = 1, migrator%message_total_recv
        write(300+lmpi_id,*) &
          migrator%local_recv(node), node, 'local_recv'
      end do

    end if

    call lmpi_conditional_stop(problem,'migrate_verify: l2g problem')

  end subroutine migrate_verify

end module load_balance


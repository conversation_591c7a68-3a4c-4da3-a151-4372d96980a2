module lsq_util

  use kinddefs,        only : dp
  use twod_util,       only : q_2d
  use lsq_constants,   only : mlsq, wflsq1, wflsq2, tf, cg_tol
  implicit none

  private

  public :: lsq_grad_st
  public :: lsq_lu_finalize
  public :: lsq_coords_info, lsq_scoords_info
  public :: clsq_cc_info

contains

!================================= LSQ_GRAD_ST ===============================80
!
! Scale, transform, and scale lsq gradients.
!
!=============================================================================80

  subroutine lsq_grad_st( lsq_mrefs, f1, f2, f3, gradx, grady, gradz, lc_max )

    use lsq_types,           only : lsq_ref_type

    type(lsq_ref_type),     intent(in)  :: lsq_mrefs
    real(dp), dimension(:), intent(in)  :: f1, f2, f3
    real(dp), dimension(4), intent(in)  :: lc_max
    real(dp), dimension(:), intent(out) :: gradx, grady, gradz

    real(dp), dimension(size(f1,1)) :: t1, t2, t3

  continue

    !...account for scaling.
    t1(:) = f1(:)/lc_max(1)
    t2(:) = f2(:)/lc_max(2)
    t3(:) = f3(:)/lc_max(3)

    !...Cartesian gradients.
    gradx(:) = ( lsq_mrefs%tr(1,1)*t1(:) &
               + lsq_mrefs%tr(2,1)*t2(:) &
               + lsq_mrefs%tr(3,1)*t3(:) )*lsq_mrefs%scaleir
    grady(:) = ( lsq_mrefs%tr(1,2)*t1(:) &
               + lsq_mrefs%tr(2,2)*t2(:) &
               + lsq_mrefs%tr(3,2)*t3(:) )*lsq_mrefs%scaleir
    gradz(:) = ( lsq_mrefs%tr(1,3)*t1(:) &
               + lsq_mrefs%tr(2,3)*t2(:) &
               + lsq_mrefs%tr(3,3)*t3(:) )*lsq_mrefs%scaleir

  end subroutine lsq_grad_st

!================================== LSQ_LU_FINALIZE ==========================80
!
! Final LU computations...check for singular determinant.
!
!=============================================================================80
  subroutine lsq_lu_finalize( det_tolerance, lsq, detf )

    use lmpi, only : lmpi_id

    real(dp),                 intent(in)    :: det_tolerance
    real(dp), dimension(3,3), intent(inout) :: lsq
    logical,                  intent(out)   :: detf

    real(dp) :: ri11, ri22, ri33, r11, r22, r33, r12, r13, r23
    real(dp) :: sfac, sfac_avg

    integer, save :: detfs = 0

  continue

    detf = .false.

    r11 = lsq(1,1)
    r12 = lsq(1,2)
    r13 = lsq(1,3)
    r22 = lsq(2,2)
    r23 = lsq(2,3)
    r33 = lsq(3,3)

    sfac = max( abs(r11), abs(r12), abs(r13) )
    sfac = max(    sfac, abs(r22), abs(r23) )
    sfac = max(              sfac, abs(r33) )

    sfac_avg = ( r11 + r12 + r13        &
               + r12 + r22 + r23        &
               + r13 + r23 + r33 )/9.0_dp

    r11 = r11/sfac
    r12 = r12/sfac
    r13 = r13/sfac
    r22 = r22/sfac
    r23 = r23/sfac
    r33 = r33/sfac

    if ( r11 < det_tolerance ) detf = .true.

    ri11  = 1._dp/r11

    ri22 = ( r22 - ri11*r12*r12 )

    if ( ri22 < det_tolerance ) detf = .true.

    ri22 = 1._dp/ri22

    r23 = ( r23 - ri11*r12*r13 )

    ri33 = ( r33 - ri11*r13*r13 &
                 - ri22*r23*r23 )

    if ( ri33 < det_tolerance ) detf = .true.

    ri33 = 1._dp/ri33

    if ( detf ) detfs = detfs + 1

    if ( detfs == 1 ) then
      write(*,"(1x,a,i5,a,e20.10,a,e20.10)")       &
      'lmpi_id=',lmpi_id,                          &
      ' sfac(max)=',sfac, ' sfac(avg)=',sfac_avg
      write(*,"(1x,a,i5,a/(1x,3e20.10))")                           &
        'lmpi_id=',lmpi_id,' R entries scaled by sfac(max) below:', &
        r11,r12,r13,r12,r22,r23,r13,r23,r33
    endif

    ri11  = 1._dp/lsq(1,1)

    ri22 = 1._dp/( lsq(2,2) - ri11*lsq(1,2)*lsq(1,2) )

    lsq(2,3) = ( lsq(2,3) - ri11*lsq(1,2)*lsq(1,3) )

    lsq(3,3) = 1._dp/( lsq(3,3) - ri11*lsq(1,3)*lsq(1,3) &
                                      - ri22*lsq(2,3)*lsq(2,3) )

    lsq(1,1)  = ri11
    lsq(2,2)  = ri22

    lsq(2,1) = lsq(1,2)*ri11
    lsq(3,1) = lsq(1,3)*ri11
    lsq(3,2) = lsq(2,3)*ri22

    !...fill in 3x3 matrix

    lsq(1,2)  = ( lsq(2,1)*lsq(3,2) - lsq(3,1) )*lsq(3,3)

    lsq(1,3)  = lsq(2,1)*lsq(2,2)
    lsq(2,3)  = lsq(3,2)*lsq(3,3)

  end subroutine lsq_lu_finalize

!================================= LSQ_COORDS_INFO ===========================80
!
! Local lsq coordinate information.
!
!=============================================================================80

  subroutine lsq_coords_info( lsq_mrefs, n, lc, lc_max )

    use lsq_types,           only : lsq_ref_type

    type(lsq_ref_type),       intent(in) :: lsq_mrefs
    integer,                  intent(in)  :: n
    real(dp), dimension(4,n), intent(in)  :: lc
    real(dp), dimension(4),   intent(in)  :: lc_max

    integer :: i

    real(dp) :: wsq

  continue

    write(*,*)
    write(*,*) 'Reference values for lsq:'
    write(*,"(1x,a,7f20.10)") '      (x/y/z)r=',lsq_mrefs%xr,&
                                                lsq_mrefs%yr,&
                                                lsq_mrefs%zr
    write(*,"(1x,a,7f20.10)") '         slenr=',lsq_mrefs%slenr
    write(*,"(1x,a,7f20.10)") ' slen(x/y/z)0r=',lsq_mrefs%slenxnr,&
                                                lsq_mrefs%slenynr,&
                                                lsq_mrefs%slenznr

    write(*,*)
    write(*,*) 'Transformation matrix (Cartesian from local):'
    write(*,"(1x,a,7f20.10)") ' tx,lx,mx=',lsq_mrefs%tr(:,1)
    write(*,"(1x,a,7f20.10)") ' ty,ly,my=',lsq_mrefs%tr(:,2)
    write(*,"(1x,a,7f20.10)") ' tz,lz,mz=',lsq_mrefs%tr(:,3)

    write(*,*)
    write(*,"(1x,a,i10)") 'Points in stencil (excluding symmetry)=',n
    write(*,"(1x,a,4f20.10)") 'Local coordinates:'
    write(*,"(1x,9x,a,4(17x,a),6x,a)")         &
    'i','xie','eta','zie','wsq','inv_distancesq'
    do i=1,n
      wsq = wflsq1*1._dp + wflsq2*lc(4,i)/lc_max(4)
      write(*,"(1x,i10,3f20.10,2e20.10)") i, lc(1:3,i),wsq,lc(4,i)
    enddo

    write(*,*)
    write(*,"(1x,a,e20.10)")  '....(inv_d^2)max=',lc_max(4)
    write(*,"(1x,a,3e20.10)") '(xie,eta,zie)max=',lc_max(1:3)

  end subroutine lsq_coords_info

!================================= LSQ_SCOORDS_INFO ==========================80
!
! Local lsq scaled coordinate information.
!
!=============================================================================80

  subroutine lsq_scoords_info( n, lc, lc_max )

    integer,                  intent(in)  :: n
    real(dp), dimension(4,n), intent(in)  :: lc
    real(dp), dimension(4),   intent(in)  :: lc_max

    integer :: i

    real(dp), dimension(4) :: sc

  continue

    write(*,*)
    write(*,"(1x,a,i10)") 'Points in stencil (excluding symmetry)=',n
    write(*,"(1x,a,4f20.10)") 'Local scaled coordinates:'
    write(*,"(1x,9x,a,3(17x,a),14x,a)") 'i','xie','eta','zie','weight'
    do i=1,n
      sc(:) = lsq_scoords( lc(:,i), lc_max )
      write(*,"(1x,i10,3f20.10,e20.10)") i, sc(:)
    enddo

  end subroutine lsq_scoords_info

!================================== CLSQ_CC_INFO  ============================80
!
! Cell-based info for cell-centered.
!
!=============================================================================80
  subroutine clsq_cc_info( lsq_mrefs, cell, clsq_ni, clsq_nb, xc, yc, zc,      &
                           ia, ja, ia_ns,                                      &
                           clsq_ia, clsq_ja,                                   &
                           cgamma, slen, slenxn, slenyn, slenzn, bcc, cl2g )

    use lsq_types,           only : lsq_ref_type
    use bc_types,            only : bcc_type
    use fun3d_constants,     only : conv

    type(lsq_ref_type),     intent(in) :: lsq_mrefs
    integer,                intent(in) :: cell, clsq_ni, clsq_nb
    integer, dimension(:),  intent(in) :: ia, ja, ia_ns
    integer, dimension(:),  intent(in) :: clsq_ia, clsq_ja, cl2g
    real(dp), dimension(:), intent(in) :: xc, yc, zc
    real(dp), dimension(:), intent(in) :: cgamma, slen
    real(dp), dimension(:), intent(in) :: slenxn, slenyn, slenzn
    type(bcc_type),         intent(in) :: bcc

    integer :: ii, jj, cella, fb, mapr

    real(dp) :: dx, dy, dz, r, t

    mapr = lsq_mrefs%mapr
    if ( mapr == 0 ) then
      write(*,*) 'Cell-based least square (CLSQ) path...no mapping:'
    elseif ( lsq_mrefs%mapr == 1 ) then
      write(*,*) 'Cell-based least square (CLSQ) path...Cartesian mapping:'
    elseif ( mapr == 2 ) then
      write(*,*) 'Cell-based approximate mapped least square (CAMLSQ) path:'
    elseif ( mapr == 3 ) then
      write(*,*) 'Cell-based exact mapped least square (CEMLSQ) path:'
    endif
    write(*,"(1x,a,i10)")    'With mapping flag..........mlsq=',mlsq
    write(*,"(1x,a,i10)")    '                 ..........mapr=',mapr
    write(*,"(1x,a,2f10.5)") 'With weightings...wflsq1,wflsq2=',&
                                                wflsq1,wflsq2
    write(*,"(1x,a,f10.5)")  'With 2D accounting...........tf=',tf
    write(*,"(1x,a,2f10.5)") 'Cgamma tolerance.........cg_tol=',cg_tol
    write(*,*)
    write(*,*) 'Cell-center at dof(grid-ordering)=',cell
    write(*,*) '            at dof(       global)=',cl2g(cell)
    write(*,"(1x,a,4f20.12)")   '          (x/y/z)c=',&
       xc(cell),yc(cell),zc(cell)
    if ( q_2d ) then
      r = sqrt( xc(cell)**2 + zc(cell)**2 )
      t = atan2( real(zc(cell),dp),real(xc(cell),dp) )
      write(*,"(1x,a,4f20.10)") ' radius,theta(deg)=',&
      r,t*conv
    endif
    write(*,"(1x,a,4f20.12)")   '              slen=',&
      slen(cell)
    write(*,"(1x,a,4f20.12)")   '            cgamma=',&
      cgamma(cell)
    write(*,"(1x,a,4f20.12)")   '      slen(x/y/z)0=',&
      slenxn(cell),slenyn(cell),slenzn(cell)
    write(*,*)
    write(*,"(1x,a,i10)") 'Cartesian coordinates of points in stencil:'
    write(*,*) ' ........Face-points=',ia_ns(cell)-ia(cell)
    write(*,*) ' ....Interior points=',clsq_ni
    write(*,*) ' ....Boundary points=',clsq_nb
    write(*,*) ' .......Total points=',clsq_ni+clsq_nb
    write(*,"(1x,8x,a,6x,a,3(18x,a),14x,a)") 'ii','cell','xc','yc','zc','global'
    jj = 0
    do ii=ia(cell),ia_ns(cell)-1
      jj = jj + 1
      cella = ja(ii)
      write(*,"(1x,2i10,3f20.12,i20)") &
      jj,cella,xc(cella),yc(cella),zc(cella),cl2g(cella)
    enddo
    do ii=clsq_ia(cell),clsq_ia(cell+1)-1
      jj = jj + 1
      cella = clsq_ja(ii)
      write(*,"(1x,2i10,3f20.12,i20)") &
      jj,cella,xc(cella),yc(cella),zc(cella),cl2g(cella)
    enddo
    if ( clsq_nb > 0 ) then
      write(*,"(1x,8x,a,6x,a,3(15x,a))") 'ii','fb','xface','yface','zface'
    endif
    do ii=bcc%clsq_ib(cell),bcc%clsq_ib(cell+1)-1
      jj = jj + 1
      fb = bcc%clsq_jb(ii)
      write(*,"(1x,i210,5f20.12)")                &
        jj,fb,bcc%xface(fb),bcc%yface(fb),bcc%zface(fb)
    enddo
    write(*,*)
    write(*,"(1x,a,i10)") 'Relative coordinates of points in stencil:'
    write(*,"(1x,8x,a,3(16x,a),10x,a,16x,a)")    &
     'ii','x-xc','y-yc','z-zc','slen-slenc','slen'
    jj = 0
    do ii=ia(cell),ia_ns(cell)-1
      jj = jj + 1
      cella = ja(ii)
      dx = xc(cella) - xc(cell)
      dy = yc(cella) - yc(cell)
      dz = zc(cella) - zc(cell)
      write(*,"(1x,i10,5f20.12)")                       &
       jj, dx, dy, dz, slen(cella)-slen(cell),slen(cella)
    enddo
    do ii=clsq_ia(cell),clsq_ia(cell+1)-1
      jj = jj + 1
      cella = clsq_ja(ii)
      dx = xc(cella) - xc(cell)
      dy = yc(cella) - yc(cell)
      dz = zc(cella) - zc(cell)
      write(*,"(1x,i10,5f20.12)")                       &
       jj, dx, dy, dz, slen(cella)-slen(cell),slen(cella)
    enddo
    do ii=bcc%clsq_ib(cell),bcc%clsq_ib(cell+1)-1
      jj = jj + 1
      fb = bcc%clsq_jb(ii)
      dx = bcc%xface(fb) - xc(cell)
      dy = bcc%yface(fb) - yc(cell)
      dz = bcc%zface(fb) - zc(cell)
      write(*,"(1x,i10,5f20.12)")                                 &
       -jj, dx, dy, dz, bcc%slenface(fb)-slen(cell),bcc%slenface(fb)
    enddo

  end subroutine clsq_cc_info

  include 'lsq_scoords.f90'

end module lsq_util

!================================= LSQ_SCOORDS_SZ ============================80
!
! Coordinates in least square - symmetry_z
!
!=============================================================================80

  pure function lsq_scoords_sz( lsq_mrefs, tf, lc_max, dz, xc, yc, zc, slenc )

    use lsq_types,             only : lsq_ref_type

    type(lsq_ref_type),     intent(in) :: lsq_mrefs
    real(dp), dimension(4), intent(in) :: lc_max
    real(dp),               intent(in) :: tf, dz, xc, yc, zc, slenc

    real(dp), dimension(4) :: lsq_scoords_sz, lc

    real(dp) :: xs, ys, zs, slens

  continue

    xs = xc
    ys = yc
    zs = zc + dz
    slens = slenc

    lc = lsq_coords( lsq_mrefs, tf, xs, ys, zs, slens )

    lsq_scoords_sz = lsq_scoords( lc, lc_max )

  end function lsq_scoords_sz

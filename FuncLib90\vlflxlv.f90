


!=================================== VLFLXLV =================================80
!
! vanLeer flux limiter applied to a vector
!
!=============================================================================80

  pure function vlflxlv(grad_a, grad_b, ndim)

    use kinddefs,        only : dp
    use fun3d_constants, only : flim_llim

    integer,                   intent(in) :: ndim
    real(dp), dimension(ndim), intent(in) :: grad_a, grad_b
    real(dp), dimension(ndim)             :: vlflxlv

  continue

    vlflxlv = (grad_a*abs(grad_b)+grad_b*abs(grad_a)) /                        &
              (abs(grad_b)+abs(grad_a)+flim_llim)

  end function vlflxlv

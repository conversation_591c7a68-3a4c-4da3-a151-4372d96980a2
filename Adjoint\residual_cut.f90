
! to enable tetrahedral cut cell adjoint

module residual_cut

  implicit none

  private

  public :: cut_cell_atlam
  public :: cut_cell_dfdq
  public :: ldfss_atlam

  logical, parameter :: check_for_nans = .false.

contains

!================================= cut_cell_atlam ============================80
!
! compute all interior adjoint residual pieces (A transpose lambda)
!   for cut cell grid
! soln%q_dof expected in primitive
!
!=============================================================================80

  subroutine cut_cell_atlam( grid, soln, sadj, design, coltag, rlam )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
    use cut_types,            only : cut
    use info_depr,            only : ntt, ivisc, kappa_umuscl
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations, iflim
    use lmpi,                 only : lmpi_die
    use cut_cell,             only : report_nan

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer :: edge, uncut_edge, triangle
    integer :: node1, node2
    real(dp), dimension(3)    :: face_center
    real(dp), dimension(3)    :: extrapolation1
    real(dp), dimension(3)    :: extrapolation2
    real(dp), dimension(3)    :: normal
    real(dp)    :: area
    integer :: bc, surf, ibc

    real(dp), dimension(5,design%nfunctions,grid%nnodes0)    :: dr_dgx
    real(dp), dimension(5,design%nfunctions,grid%nnodes0)    :: dr_dgy
    real(dp), dimension(5,design%nfunctions,grid%nnodes0)    :: dr_dgz

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: half    = 0.5_dp
    real(dp), parameter    :: third   = 1.0_dp/3.0_dp
    logical,  parameter    :: fast_res = .true.

    integer :: my_ntt

    continue

    if ( kappa_umuscl > 0.1_dp ) then
      write(*,*) 'subroutine cut_cell_atlam() not coded for kappa_umuscl > 0...'
      call lmpi_die
      stop
    endif

    soln%viscous_method = 1

    dr_dgx = zero
    dr_dgy = zero
    dr_dgz = zero

    if (check_for_nans) call report_nan( soln%q_dof, 'start qnde')
    if (check_for_nans) call report_nan( soln%gradx, 'start grdx')
    if (check_for_nans) call report_nan( soln%grady, 'start grdy')
    if (check_for_nans) call report_nan( soln%gradz, 'start grdz')

    if (check_for_nans) call report_nan( sadj%res, 'start ares')

    my_ntt = ntt
    if (itime /= 0) my_ntt = pseudo_sub

    standard_scheme_on_uncut_edges : do uncut_edge = 1, cut%uncut_edges
      edge = cut%uncut_edge_index(uncut_edge)
      node1 = grid%eptr(1,edge)
      node2 = grid%eptr(2,edge)

      if ( node1 > grid%nnodes0 .and. node2 > grid%nnodes0 ) &
        cycle standard_scheme_on_uncut_edges

      normal(1)  = grid%xn(edge)
      normal(2)  = grid%yn(edge)
      normal(3)  = grid%zn(edge)
      area       = grid%ra(edge)

      face_center(1) = half*(grid%x(node1) + grid%x(node2))
      face_center(2) = half*(grid%y(node1) + grid%y(node2))
      face_center(3) = half*(grid%z(node1) + grid%z(node2))
      extrapolation1(1) = face_center(1) - grid%x(node1)
      extrapolation1(2) = face_center(2) - grid%y(node1)
      extrapolation1(3) = face_center(3) - grid%z(node1)
      extrapolation2(1) = face_center(1) - grid%x(node2)
      extrapolation2(2) = face_center(2) - grid%y(node2)
      extrapolation2(3) = face_center(3) - grid%z(node2)
      if ( (my_ntt <= first_order_iterations) .or. (iflim /= 3) ) then
        call integrate_dual_atlam(                       &
          grid, soln, sadj, design, coltag, rlam,        &
          node1, node2,                                  &
          extrapolation1, extrapolation2,                &
          normal, area )
      else
        if ( fast_res ) then
          call integrate_dual_atlam_iflim_fst(             &
            grid, soln, sadj, design, coltag, rlam,        &
            node1, node2,                                  &
            extrapolation1, extrapolation2,                &
            normal, area,                                  &
            dr_dgx, dr_dgy, dr_dgz )
        else
          call integrate_dual_atlam_iflim(                 &
            grid, soln, sadj, design, coltag, rlam,        &
            node1, node2,                                  &
            extrapolation1, extrapolation2,                &
            normal, area )
        end if
      end if
      if (ivisc > 0) then
        call integrate_visc_atlam(                       &
          grid, soln, sadj, design, coltag, rlam,        &
          node1, node2,                                  &
          normal, area )
      end if
    end do standard_scheme_on_uncut_edges

    if (check_for_nans) call report_nan( sadj%res, 'int   ares')

    exact_dual_int_on_cut_edges : do edge = 1, cut%cut_edges
      sub_triangle : do triangle=1, cut%cut_edge(edge)%number_of_triangles
        node1 = cut%cut_edge(edge)%left_node
        node2 = cut%cut_edge(edge)%right_node
        face_center = third * ( cut%cut_edge(edge)%triangle_node1(:,triangle) &
                              + cut%cut_edge(edge)%triangle_node2(:,triangle) &
                              + cut%cut_edge(edge)%triangle_node3(:,triangle) )
        extrapolation1(1) = face_center(1) - grid%x(node1)
        extrapolation1(2) = face_center(2) - grid%y(node1)
        extrapolation1(3) = face_center(3) - grid%z(node1)
        extrapolation2(1) = face_center(1) - grid%x(node2)
        extrapolation2(2) = face_center(2) - grid%y(node2)
        extrapolation2(3) = face_center(3) - grid%z(node2)
        if ( (my_ntt <= first_order_iterations) .or. (iflim /= 3) ) then
          call integrate_dual_atlam(                       &
            grid, soln, sadj, design, coltag, rlam,        &
            node1, node2,                                  &
            extrapolation1, extrapolation2,                &
            cut%cut_edge(edge)%triangle_normal(:,triangle),&
            cut%cut_edge(edge)%triangle_area(triangle) )
        else
          if ( fast_res ) then
            call integrate_dual_atlam_iflim_fst(             &
              grid, soln, sadj, design, coltag, rlam,        &
              node1, node2,                                  &
              extrapolation1, extrapolation2,                &
              cut%cut_edge(edge)%triangle_normal(:,triangle),&
              cut%cut_edge(edge)%triangle_area(triangle),    &
            dr_dgx, dr_dgy, dr_dgz )
          else
            call integrate_dual_atlam_iflim(                 &
              grid, soln, sadj, design, coltag, rlam,        &
              node1, node2,                                  &
              extrapolation1, extrapolation2,                &
              cut%cut_edge(edge)%triangle_normal(:,triangle),&
              cut%cut_edge(edge)%triangle_area(triangle) )
          end if
        end if
        if (ivisc > 0) then
          call integrate_visc_atlam(                       &
            grid, soln, sadj, design, coltag, rlam,        &
            node1, node2,                                  &
            cut%cut_edge(edge)%triangle_normal(:,triangle),&
            cut%cut_edge(edge)%triangle_area(triangle) )
        end if
      end do sub_triangle
    end do exact_dual_int_on_cut_edges

    exact_dual_int_on_cut_bcs : do bc = 1, cut%cut_bcs
      bc_triangle : do triangle=1, cut%cut_bc(bc)%number_of_triangles
        ibc = cut%cut_bc(bc)%ibc
        if ( -1 == ibc ) ibc = cut%cut_bc(bc)%triangle_ibc(triangle)
        if ( fast_res ) then
          call integrate_dual_atlam_bcf(                   &
            grid, soln, sadj, design, coltag, rlam,        &
            ibc,                                           &
            cut%cut_bc(bc)%node,                           &
            cut%cut_bc(bc)%triangle_node1(:,triangle),     &
            cut%cut_bc(bc)%triangle_node2(:,triangle),     &
            cut%cut_bc(bc)%triangle_node3(:,triangle),     &
            cut%cut_bc(bc)%triangle_normal(:,triangle),    &
            cut%cut_bc(bc)%triangle_area(triangle),        &
            dr_dgx, dr_dgy, dr_dgz )
        else
          call integrate_dual_atlam_bc(                    &
            grid, soln, sadj, design, coltag, rlam,        &
            ibc,                                           &
            cut%cut_bc(bc)%node,                           &
            cut%cut_bc(bc)%triangle_node1(:,triangle),     &
            cut%cut_bc(bc)%triangle_node2(:,triangle),     &
            cut%cut_bc(bc)%triangle_node3(:,triangle),     &
            cut%cut_bc(bc)%triangle_normal(:,triangle),    &
            cut%cut_bc(bc)%triangle_area(triangle) )
        end if
        if (ivisc > 0 )                                  &
        call integrate_visc_atlam_bc(                    &
          grid, soln, sadj, design, coltag, rlam,        &
          ibc,                                           &
          cut%cut_bc(bc)%node,                           &
          cut%cut_bc(bc)%triangle_node1(:,triangle),     &
          cut%cut_bc(bc)%triangle_node2(:,triangle),     &
          cut%cut_bc(bc)%triangle_node3(:,triangle),     &
          cut%cut_bc(bc)%triangle_normal(:,triangle),    &
          cut%cut_bc(bc)%triangle_area(triangle) )
      end do bc_triangle
    end do exact_dual_int_on_cut_bcs

    exact_dual_int_on_cut_surfs : do surf = 1, cut%cut_surfs
      surf_triangle : do triangle=1, cut%cut_surf(surf)%number_of_triangles
        ibc = cut%cut_surf(surf)%ibc
        if ( -1 == ibc ) ibc = cut%cut_surf(surf)%triangle_ibc(triangle)
        if ( fast_res ) then
          call integrate_dual_atlam_bcf(                   &
            grid, soln, sadj, design, coltag, rlam,        &
            ibc,                                           &
            cut%cut_surf(surf)%node,                       &
            cut%cut_surf(surf)%triangle_node1(:,triangle), &
            cut%cut_surf(surf)%triangle_node2(:,triangle), &
            cut%cut_surf(surf)%triangle_node3(:,triangle), &
            cut%cut_surf(surf)%triangle_normal(:,triangle),&
            cut%cut_surf(surf)%triangle_area(triangle),    &
            dr_dgx, dr_dgy, dr_dgz  )
        else
          call integrate_dual_atlam_bc(                    &
            grid, soln, sadj, design, coltag, rlam,        &
            ibc,                                           &
            cut%cut_surf(surf)%node,                       &
            cut%cut_surf(surf)%triangle_node1(:,triangle), &
            cut%cut_surf(surf)%triangle_node2(:,triangle), &
            cut%cut_surf(surf)%triangle_node3(:,triangle), &
            cut%cut_surf(surf)%triangle_normal(:,triangle),&
            cut%cut_surf(surf)%triangle_area(triangle) )
        end if
        if (ivisc > 0 )                                  &
        call integrate_visc_atlam_bc(                    &
          grid, soln, sadj, design, coltag, rlam,        &
          ibc,                                           &
          cut%cut_surf(surf)%node,                       &
          cut%cut_surf(surf)%triangle_node1(:,triangle), &
          cut%cut_surf(surf)%triangle_node2(:,triangle), &
          cut%cut_surf(surf)%triangle_node3(:,triangle), &
          cut%cut_surf(surf)%triangle_normal(:,triangle),&
          cut%cut_surf(surf)%triangle_area(triangle) )
      end do surf_triangle
    end do exact_dual_int_on_cut_surfs

    if ( fast_res ) then
      do node1 = 1, grid%nnodes0
        call gradient_deriv_cut( grid, soln, design,                      &
          node1, dr_dgx(:,:,node1), dr_dgy(:,:,node1), dr_dgz(:,:,node1), &
          sadj%res )
      end do
    end if

    if (check_for_nans) call report_nan( sadj%res, 'cut   ares')

  end subroutine cut_cell_atlam

!================================= cut_cell_atlam ============================80
!
! compute all interior adjoint residual pieces (A transpose lambda)
!   for cut cell grid
! soln%q_dof expected in primitive
!
!=============================================================================80

  subroutine ldfss_atlam( grid, soln, sadj, design, crow, coltag, rlam )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
    use comprow_types,        only : crow_type
    use info_depr,            only : ntt, kappa_umuscl
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use grid_motion_helpers,  only : need_grid_velocity
    use inviscid_flux,        only : first_order_iterations
    use cut_cell,             only : report_nan
    use lmpi,                 only : lmpi_die

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    type(crow_type),   intent(in)    :: crow
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer :: edge
    integer :: node1, node2
    real(dp), dimension(3)    :: face_center
    real(dp), dimension(3)    :: extrapolation1
    real(dp), dimension(3)    :: extrapolation2
    real(dp), dimension(3)    :: normal
    real(dp)    :: area, face_speed
    logical :: second

    real(dp), dimension(5,design%nfunctions,grid%nnodes0)    :: dr_dgx
    real(dp), dimension(5,design%nfunctions,grid%nnodes0)    :: dr_dgy
    real(dp), dimension(5,design%nfunctions,grid%nnodes0)    :: dr_dgz

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: half    = 0.5_dp

    integer :: my_ntt

    continue

    if ( kappa_umuscl > 0.1_dp ) then
      write(*,*) 'subroutine ldfss_atlam() not coded for kappa_umuscl > 0...'
      call lmpi_die
      stop
    endif

    dr_dgx = zero
    dr_dgy = zero
    dr_dgz = zero

    if (check_for_nans) call report_nan( soln%q_dof, 'start qnde')
    if (check_for_nans) call report_nan( soln%gradx, 'start grdx')
    if (check_for_nans) call report_nan( soln%grady, 'start grdy')
    if (check_for_nans) call report_nan( soln%gradz, 'start grdz')

    if (check_for_nans) call report_nan( sadj%res, 'start ares')

    my_ntt = ntt
    if (itime /= 0) my_ntt = pseudo_sub
    second = (my_ntt > first_order_iterations)

    standard_scheme_on_uncut_edges : do edge = 1, grid%nedgeloc
      node1 = grid%eptr(1,edge)
      node2 = grid%eptr(2,edge)

      if ( node1 > grid%nnodes0 .and. node2 > grid%nnodes0 ) &
        cycle standard_scheme_on_uncut_edges

      normal(1)  = grid%xn(edge)
      normal(2)  = grid%yn(edge)
      normal(3)  = grid%zn(edge)
      area       = grid%ra(edge)

      face_speed = zero
      if ( need_grid_velocity ) face_speed = grid%facespeed(edge)

      face_center(1) = half*(grid%x(node1) + grid%x(node2))
      face_center(2) = half*(grid%y(node1) + grid%y(node2))
      face_center(3) = half*(grid%z(node1) + grid%z(node2))
      extrapolation1(1) = face_center(1) - grid%x(node1)
      extrapolation1(2) = face_center(2) - grid%y(node1)
      extrapolation1(3) = face_center(3) - grid%z(node1)
      extrapolation2(1) = face_center(1) - grid%x(node2)
      extrapolation2(2) = face_center(2) - grid%y(node2)
      extrapolation2(3) = face_center(3) - grid%z(node2)
      call integrate_ldfss_fst(                        &
        grid, soln, sadj, design, coltag, rlam,        &
        node1, node2,                                  &
        extrapolation1, extrapolation2,                &
        normal, area, face_speed, second,              &
        dr_dgx, dr_dgy, dr_dgz )
    end do standard_scheme_on_uncut_edges

    if (check_for_nans) call report_nan( sadj%res, 'int   ares')

    do node1 = 1, grid%nnodes0
      call gradient_deriv( grid, soln, design, crow,                    &
        node1, dr_dgx(:,:,node1), dr_dgy(:,:,node1), dr_dgz(:,:,node1), &
        sadj%res )
    end do

    if (check_for_nans) call report_nan( sadj%res, 'grd   ares')

  end subroutine ldfss_atlam

!================================= integrate_dual_atlam ======================80
!
! integrate atlam for a triangle between two duals
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dual_atlam(                &
    grid, soln, sadj, design, coltag, rlam,       &
    node1,node2,                                  &
    extrapolation1, extrapolation2,               &
    normal, area)

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : flux_construction
    use inviscid_flux,        only : first_order_iterations

    use thermo,               only : dprimitive_dconserved

    use flux_functions,       only : roe_primitive_jacobian, &
                                     vanleer_primitive_jacobian

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: node1, node2

    real(dp), dimension(3),     intent(in) :: extrapolation1
    real(dp), dimension(3),     intent(in) :: extrapolation2
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    integer :: my_ntt
    real(dp)    :: second, second1

    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradx
    real(dp), dimension(5,design%nfunctions)    :: dres_dgrady
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradz

    integer :: i,j,k

    real(dp), dimension(5)    :: state1
    real(dp), dimension(5)    :: state2
    real(dp), dimension(5,5)    :: df_dstate1,df_dstate2

    real(dp), dimension(5,5)    :: dQdq

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: one    = 1.0_dp

    continue

    my_ntt = ntt
    if (itime /= 0) my_ntt = pseudo_sub

    second = one
    if (my_ntt <= first_order_iterations) second = zero

    state1 = soln%q_dof(:,node1)                                             &
      + second * soln%phi(:,node1) * soln%gradx(:,node1) * extrapolation1(1) &
      + second * soln%phi(:,node1) * soln%grady(:,node1) * extrapolation1(2) &
      + second * soln%phi(:,node1) * soln%gradz(:,node1) * extrapolation1(3)

    second1 = second
    if ( state1(1) <= zero .or. state1(5) <= zero ) then
      state1 = soln%q_dof(:,node1)
      second1 = zero
    end if

    state2 = soln%q_dof(:,node2)                                             &
      + second * soln%phi(:,node2) * soln%gradx(:,node2) * extrapolation2(1) &
      + second * soln%phi(:,node2) * soln%grady(:,node2) * extrapolation2(2) &
      + second * soln%phi(:,node2) * soln%gradz(:,node2) * extrapolation2(3)

    if ( state2(1) <= zero .or. state2(5) <= zero ) then
      state2 = soln%q_dof(:,node2)
    end if

    select case (flux_construction)
    case ('vanleer')
      call vanleer_primitive_jacobian(state1,state2,normal,one,zero,&
        df_dstate1,df_dstate2)
    case ('roe')
      call roe_primitive_jacobian(state1,state2,normal,one,zero,&
        df_dstate1,df_dstate2)
    case default
      df_dstate1 = zero
      df_dstate2 = zero
    end select

! call the stuff that depends on the state1
    local_node1 : if ( node1 <= grid%nnodes0  ) then
      dres_dstate = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dstate1(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dstate1(i,j) )
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node1,k) = sadj%res(i,node1,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      do k = 1, design%nfunctions
        dres_dgradx(:,k) = second1 * soln%phi(:,node1) * extrapolation1(1) * &
          dres_dstate(:,k)
        dres_dgrady(:,k) = second1 * soln%phi(:,node1) * extrapolation1(2) * &
          dres_dstate(:,k)
        dres_dgradz(:,k) = second1 * soln%phi(:,node1) * extrapolation1(3) * &
          dres_dstate(:,k)
      end do
      call gradient_deriv_cut( grid, soln, design,    &
        node1, dres_dgradx, dres_dgrady, dres_dgradz, &
        sadj%res )
    end if local_node1

! call the stuff that depends on the state2
    local_node2 : if ( node2 <= grid%nnodes0  ) then
      dres_dstate = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dstate2(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dstate2(i,j) )
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node2,k) = sadj%res(i,node2,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      do k = 1, design%nfunctions
        dres_dgradx(:,k) = second1 * soln%phi(:,node1) * extrapolation2(1) * &
          dres_dstate(:,k)
        dres_dgrady(:,k) = second1 * soln%phi(:,node1) * extrapolation2(2) * &
          dres_dstate(:,k)
        dres_dgradz(:,k) = second1 * soln%phi(:,node1) * extrapolation2(3) * &
          dres_dstate(:,k)
      end do
      call gradient_deriv_cut( grid, soln, design,    &
        node2, dres_dgradx, dres_dgrady, dres_dgradz, &
        sadj%res )
    end if local_node2

  end subroutine integrate_dual_atlam

!================================= integrate_dual_atlam_iflim ================80
!
! integrate atlam for a triangle between two duals
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dual_atlam_iflim_fst(      &
    grid, soln, sadj, design, coltag, rlam,       &
    node1,node2,                                  &
    extrapolation1, extrapolation2,               &
    normal, area,                                 &
    dr_dgx, dr_dgy, dr_dgz )

    use kinddefs,         only : dp
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,        only : sadj_type
    use design_types,        only : design_type

    use inviscid_flux,          only : flux_construction
    use thermo,           only : dprimitive_dconserved

    use flux_functions,   only : roe_primitive_jacobian, &
                                 vanleer_primitive_jacobian

    use limiter_functions,only : pswitch_grad

    use cut_types,        only : cut_lap_limit, cut_lap_coef

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: node1, node2

    real(dp), dimension(3),     intent(in) :: extrapolation1
    real(dp), dimension(3),     intent(in) :: extrapolation2
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                                                  intent(inout) :: dr_dgx
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                                                  intent(inout) :: dr_dgy
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                                                  intent(inout) :: dr_dgz

    logical :: realizable_limit1, realizable_limit2
    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dlim

    real(dp), dimension(design%nfunctions)    :: dres_dphi

    integer :: i,j,k

    real(dp), dimension(5)    :: state1
    real(dp), dimension(5)    :: state2
    real(dp), dimension(5,5)    :: df_dstate1,df_dstate2

    real(dp), dimension(5,5)    :: dQdq

    real(dp)    :: phi
    real(dp)    :: dphi_dp1, dphi_dp2
    real(dp)    :: dphi_dgradpx1, dphi_dgradpy1, dphi_dgradpz1
    real(dp)    :: dphi_dgradpx2, dphi_dgradpy2, dphi_dgradpz2

    real(dp)    :: lap
    real(dp)    :: length
    real(dp)    :: dlap_dp
    real(dp), dimension(5)    :: conserved1
    real(dp), dimension(5)    :: conserved2

    real(dp), parameter    :: zero = 0.0_dp
    real(dp), parameter    :: one  = 1.0_dp
    real(dp), parameter    :: two  = 2.0_dp

    continue

    call pswitch_grad(     &
      extrapolation1(1),   &
      extrapolation1(2),   &
      extrapolation1(3),   &
      extrapolation2(1),   &
      extrapolation2(2),   &
      extrapolation2(3),   &
      soln%q_dof(5,node1), &
      soln%q_dof(5,node2), &
      soln%gradx(5,node1), &
      soln%grady(5,node1), &
      soln%gradz(5,node1), &
      soln%gradx(5,node2), &
      soln%grady(5,node2), &
      soln%gradz(5,node2), &
      phi,                 &
      dphi_dp1,            &
      dphi_dp2,            &
      dphi_dgradpx1,       &
      dphi_dgradpy1,       &
      dphi_dgradpz1,       &
      dphi_dgradpx2,       &
      dphi_dgradpy2,       &
      dphi_dgradpz2)

    state1 = soln%q_dof(:,node1)                                          &
      + phi * soln%phi(:,node1) * soln%gradx(:,node1) * extrapolation1(1) &
      + phi * soln%phi(:,node1) * soln%grady(:,node1) * extrapolation1(2) &
      + phi * soln%phi(:,node1) * soln%gradz(:,node1) * extrapolation1(3)

    realizable_limit1 = .false.
    if ( state1(1) <= zero .or. state1(5) <= zero ) then
      state1 = soln%q_dof(:,node1)
      realizable_limit1 = .true.
    end if

    state2 = soln%q_dof(:,node2)                                          &
      + phi * soln%phi(:,node2) * soln%gradx(:,node2) * extrapolation2(1) &
      + phi * soln%phi(:,node2) * soln%grady(:,node2) * extrapolation2(2) &
      + phi * soln%phi(:,node2) * soln%gradz(:,node2) * extrapolation2(3)

    realizable_limit2 = .false.
    if ( state2(1) <= zero .or. state2(5) <= zero ) then
      state2 = soln%q_dof(:,node2)
      realizable_limit2 = .true.
    end if

    select case (flux_construction)
    case ('vanleer')
      call vanleer_primitive_jacobian(state1,state2,normal,one,zero,&
        df_dstate1,df_dstate2)
    case ('roe')
      call roe_primitive_jacobian(state1,state2,normal,one,zero,&
        df_dstate1,df_dstate2)
    case default
      df_dstate1 = zero
      df_dstate2 = zero
    end select

! call the stuff that depends on the state1
    dres_dstate = zero
    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
            + coltag(i,node1) * rlam(i,node1,k) * df_dstate1(i,j)  &
            - coltag(i,node2) * rlam(i,node2,k) * df_dstate1(i,j) )
        end do
      end do
    end do

    local_node1_cc : if ( node1 <= grid%nnodes0  ) then
      call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node1,k) = sadj%res(i,node1,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do
    end if local_node1_cc

    no_feas_limit1 : if ( .not. realizable_limit1 ) then
      local_node1_grad : if ( node1 <= grid%nnodes0  ) then
        do j = 1, 5
          do k = 1, design%nfunctions
            dr_dgx(j,k,node1) = dr_dgx(j,k,node1) &
              + phi * soln%phi(j,node1) * extrapolation1(1) * dres_dstate(j,k)
            dr_dgy(j,k,node1) = dr_dgy(j,k,node1) &
              + phi * soln%phi(j,node1) * extrapolation1(2) * dres_dstate(j,k)
            dr_dgz(j,k,node1) = dr_dgz(j,k,node1) &
              + phi * soln%phi(j,node1) * extrapolation1(3) * dres_dstate(j,k)
          end do
        end do
      end if local_node1_grad

      dres_dphi = zero
      do i = 1, 5
        dres_dphi(:) = dres_dphi(:) + soln%phi(i,node1) * (            &
          extrapolation1(1) * soln%gradx(i,node1) +                    &
          extrapolation1(2) * soln%grady(i,node1) +                    &
          extrapolation1(3) * soln%gradz(i,node1) )                    &
          * dres_dstate(i,:)
      end do

      local_node1_lim_cc1 : if ( node1 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp1
        call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node1_lim_cc1
      local_node1_lim_cc2 : if ( node2 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp1
        call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node1_lim_cc2

      local_node1_grad1 : if ( node1 <= grid%nnodes0  ) then
        do k = 1, design%nfunctions
          dr_dgx(5,k,node1) = dr_dgx(5,k,node1) &
            + dres_dphi(k)*dphi_dgradpx1
          dr_dgy(5,k,node1) = dr_dgy(5,k,node1) &
            + dres_dphi(k)*dphi_dgradpy1
          dr_dgz(5,k,node1) = dr_dgz(5,k,node1) &
            + dres_dphi(k)*dphi_dgradpz1
        end do
      end if local_node1_grad1
      local_node1_grad2 : if ( node2 <= grid%nnodes0  ) then
        do k = 1, design%nfunctions
          dr_dgx(5,k,node2) = dr_dgx(5,k,node2) &
            + dres_dphi(k)*dphi_dgradpx2
          dr_dgy(5,k,node2) = dr_dgy(5,k,node2) &
            + dres_dphi(k)*dphi_dgradpy2
          dr_dgz(5,k,node2) = dr_dgz(5,k,node2) &
            + dres_dphi(k)*dphi_dgradpz2
        end do
      end if local_node1_grad2
    end if no_feas_limit1

! call the stuff that depends on the state2
    dres_dstate = zero
    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
            + coltag(i,node1) * rlam(i,node1,k) * df_dstate2(i,j)  &
            - coltag(i,node2) * rlam(i,node2,k) * df_dstate2(i,j) )
        end do
      end do
    end do

    local_node2_cc : if ( node2 <= grid%nnodes0  ) then
      call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node2,k) = sadj%res(i,node2,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do
    end if local_node2_cc

    no_feas_limit2 : if ( .not. realizable_limit2 ) then
      local_node2_grad : if ( node2 <= grid%nnodes0  ) then
        do j = 1, 5
          do k = 1, design%nfunctions
            dr_dgx(j,k,node2) = dr_dgx(j,k,node2) &
              + phi * soln%phi(j,node1) * extrapolation2(1) * dres_dstate(j,k)
            dr_dgy(j,k,node2) = dr_dgy(j,k,node2) &
              + phi * soln%phi(j,node1) * extrapolation2(2) * dres_dstate(j,k)
            dr_dgz(j,k,node2) = dr_dgz(j,k,node2) &
              + phi * soln%phi(j,node1) * extrapolation2(3) * dres_dstate(j,k)
          end do
        end do
      end if local_node2_grad

      dres_dphi = zero
      do i = 1, 5
        dres_dphi(:) = dres_dphi(:) + soln%phi(i,node1) * (            &
          extrapolation1(1) * soln%gradx(i,node1) +                    &
          extrapolation1(2) * soln%grady(i,node1) +                    &
          extrapolation1(3) * soln%gradz(i,node1) )                    &
          * dres_dstate(i,:)
      end do

      local_node2_lim_cc1 : if ( node1 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp1
        call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node2_lim_cc1
      local_node2_lim_cc2 : if ( node2 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp2
        call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node2_lim_cc2

      local_node2_grad1 : if ( node1 <= grid%nnodes0  ) then
        do k = 1, design%nfunctions
          dr_dgx(5,k,node1) = dr_dgx(5,k,node1) &
            + dres_dphi(k)*dphi_dgradpx1
          dr_dgy(5,k,node1) = dr_dgy(5,k,node1) &
            + dres_dphi(k)*dphi_dgradpy1
          dr_dgz(5,k,node1) = dr_dgz(5,k,node1) &
            + dres_dphi(k)*dphi_dgradpz1
        end do
      end if local_node2_grad1
      local_node2_grad2 : if ( node2 <= grid%nnodes0  ) then
        do k = 1, design%nfunctions
          dr_dgx(5,k,node2) = dr_dgx(5,k,node2) &
            + dres_dphi(k)*dphi_dgradpx2
          dr_dgy(5,k,node2) = dr_dgy(5,k,node2) &
            + dres_dphi(k)*dphi_dgradpy2
          dr_dgz(5,k,node2) = dr_dgz(5,k,node2) &
            + dres_dphi(k)*dphi_dgradpz2
        end do
      end if local_node2_grad2
    end if no_feas_limit2

! add in the cut_lap terms

    if (node1 <= grid%nnodes0) then
      if ( soln%q_dof(5,node1) < cut_lap_limit ) then
        length = sqrt( (grid%x(node2)-grid%x(node1)) ** 2 + &
                       (grid%y(node2)-grid%y(node1)) ** 2 + &
                       (grid%x(node2)-grid%z(node1)) ** 2 )
        lap = cut_lap_coef*(cut_lap_limit-soln%q_dof(5,node1))**2
        do k = 1, design%nfunctions
          do i = 1, 5
            sadj%res(i,node1,k) = sadj%res(i,node1,k) + area * lap/length *   &
              coltag(i,node1) * rlam(i,node1,k)
            sadj%res(i,node2,k) = sadj%res(i,node2,k) - area * lap/length *   &
              coltag(i,node1) * rlam(i,node1,k)
          end do
        end do
        dlap_dp = -cut_lap_coef*two*(cut_lap_limit-soln%q_dof(5,node1))
        conserved1 = in_conserved_variables(soln%q_dof(:,node1))
        conserved2 = in_conserved_variables(soln%q_dof(:,node2))
        call dprimitive_dconserved(soln%q_dof(:,node1),dQdq)
        do k = 1, design%nfunctions
          do j = 1, 5
            do i = 1, 5
              sadj%res(j,node1,k) = sadj%res(j,node1,k) -                    &
                area * dlap_dp/length *                                      &
                (conserved2(i)-conserved1(i)) * dQdq(j,5) *                  &
                coltag(i,node1) * rlam(i,node1,k)
            end do
          end do
        end do
      end if
    end if

    if (node2 <= grid%nnodes0) then
      if ( soln%q_dof(5,node2) < cut_lap_limit ) then
        length = sqrt( (grid%x(node1)-grid%x(node2)) ** 2 + &
                       (grid%y(node1)-grid%y(node2)) ** 2 + &
                       (grid%x(node1)-grid%z(node2)) ** 2 )
        lap = cut_lap_coef*(cut_lap_limit-soln%q_dof(5,node2))**2
        do k = 1, design%nfunctions
          do i = 1, 5
            sadj%res(i,node2,k) = sadj%res(i,node2,k) + area * lap/length *   &
              coltag(i,node2) * rlam(i,node2,k)
            sadj%res(i,node1,k) = sadj%res(i,node1,k) - area * lap/length *   &
              coltag(i,node2) * rlam(i,node2,k)
          end do
        end do
        dlap_dp = -cut_lap_coef*two*(cut_lap_limit-soln%q_dof(5,node2))
        conserved1 = in_conserved_variables(soln%q_dof(:,node1))
        conserved2 = in_conserved_variables(soln%q_dof(:,node2))
        call dprimitive_dconserved(soln%q_dof(:,node2),dQdq)
        do k = 1, design%nfunctions
          do j = 1, 5
            do i = 1, 5
              sadj%res(j,node2,k) = sadj%res(j,node2,k) -                    &
                area * dlap_dp/length *                                      &
                (conserved1(i)-conserved2(i)) * dQdq(j,5) *                  &
                coltag(i,node2) * rlam(i,node2,k)
            end do
          end do
        end do
      end if
    end if

  end subroutine integrate_dual_atlam_iflim_fst

!================================= integrate_dual_atlam_iflim ================80
!
! integrate atlam for a triangle between two duals
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dual_atlam_iflim(          &
    grid, soln, sadj, design, coltag, rlam,       &
    node1,node2,                                  &
    extrapolation1, extrapolation2,               &
    normal, area)

    use kinddefs,         only : dp
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,        only : sadj_type
    use design_types,        only : design_type

    use inviscid_flux,     only : flux_construction
    use thermo,           only : dprimitive_dconserved

    use flux_functions,   only : roe_primitive_jacobian, &
                                 vanleer_primitive_jacobian

    use limiter_functions,only : pswitch_grad

    use cut_types,        only : cut_lap_limit, cut_lap_coef

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: node1, node2

    real(dp), dimension(3),     intent(in) :: extrapolation1
    real(dp), dimension(3),     intent(in) :: extrapolation2
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    logical :: realizable_limit1, realizable_limit2
    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradx
    real(dp), dimension(5,design%nfunctions)    :: dres_dgrady
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradz
    real(dp), dimension(5,design%nfunctions)    :: dres_dlim

    real(dp), dimension(design%nfunctions)    :: dres_dphi

    integer :: i,j,k

    real(dp), dimension(5)    :: state1
    real(dp), dimension(5)    :: state2
    real(dp), dimension(5,5)    :: df_dstate1,df_dstate2

    real(dp), dimension(5,5)    :: dQdq

    real(dp)    :: phi
    real(dp)    :: dphi_dp1, dphi_dp2
    real(dp)    :: dphi_dgradpx1, dphi_dgradpy1, dphi_dgradpz1
    real(dp)    :: dphi_dgradpx2, dphi_dgradpy2, dphi_dgradpz2

    real(dp)    :: lap
    real(dp)    :: length
    real(dp)    :: dlap_dp
    real(dp), dimension(5)    :: conserved1
    real(dp), dimension(5)    :: conserved2

    real(dp), parameter    :: zero = 0.0_dp
    real(dp), parameter    :: one  = 1.0_dp
    real(dp), parameter    :: two  = 2.0_dp

    continue

    call pswitch_grad(     &
      extrapolation1(1),   &
      extrapolation1(2),   &
      extrapolation1(3),   &
      extrapolation2(1),   &
      extrapolation2(2),   &
      extrapolation2(3),   &
      soln%q_dof(5,node1), &
      soln%q_dof(5,node2), &
      soln%gradx(5,node1), &
      soln%grady(5,node1), &
      soln%gradz(5,node1), &
      soln%gradx(5,node2), &
      soln%grady(5,node2), &
      soln%gradz(5,node2), &
      phi,                 &
      dphi_dp1,            &
      dphi_dp2,            &
      dphi_dgradpx1,       &
      dphi_dgradpy1,       &
      dphi_dgradpz1,       &
      dphi_dgradpx2,       &
      dphi_dgradpy2,       &
      dphi_dgradpz2)

    state1 = soln%q_dof(:,node1)                                          &
      + phi * soln%phi(:,node1) * soln%gradx(:,node1) * extrapolation1(1) &
      + phi * soln%phi(:,node1) * soln%grady(:,node1) * extrapolation1(2) &
      + phi * soln%phi(:,node1) * soln%gradz(:,node1) * extrapolation1(3)

    realizable_limit1 = .false.
    if ( state1(1) <= zero .or. state1(5) <= zero ) then
      state1 = soln%q_dof(:,node1)
      realizable_limit1 = .true.
    end if

    state2 = soln%q_dof(:,node2)                                          &
      + phi * soln%phi(:,node2) * soln%gradx(:,node2) * extrapolation2(1) &
      + phi * soln%phi(:,node2) * soln%grady(:,node2) * extrapolation2(2) &
      + phi * soln%phi(:,node2) * soln%gradz(:,node2) * extrapolation2(3)

    realizable_limit2 = .false.
    if ( state2(1) <= zero .or. state2(5) <= zero ) then
      state2 = soln%q_dof(:,node2)
      realizable_limit2 = .true.
    end if

    select case (flux_construction)
    case ('vanleer')
      call vanleer_primitive_jacobian(state1,state2,normal,one,zero,&
        df_dstate1,df_dstate2)
    case ('roe')
      call roe_primitive_jacobian(state1,state2,normal,one,zero,&
        df_dstate1,df_dstate2)
    case default
      df_dstate1 = zero
      df_dstate2 = zero
    end select

! call the stuff that depends on the state1
    dres_dstate = zero
    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
            + coltag(i,node1) * rlam(i,node1,k) * df_dstate1(i,j)  &
            - coltag(i,node2) * rlam(i,node2,k) * df_dstate1(i,j) )
        end do
      end do
    end do

    local_node1_cc : if ( node1 <= grid%nnodes0  ) then
      call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node1,k) = sadj%res(i,node1,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do
    end if local_node1_cc

    no_feas_limit1 : if ( .not. realizable_limit1 ) then
      local_node1_grad : if ( node1 <= grid%nnodes0  ) then
        do k = 1, design%nfunctions
          dres_dgradx(:,k) = phi * soln%phi(:,node1) * extrapolation1(1) * &
            dres_dstate(:,k)
          dres_dgrady(:,k) = phi * soln%phi(:,node1) * extrapolation1(2) * &
            dres_dstate(:,k)
          dres_dgradz(:,k) = phi * soln%phi(:,node1) * extrapolation1(3) * &
            dres_dstate(:,k)
        end do
        call gradient_deriv_cut( grid, soln, design,    &
          node1, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )
      end if local_node1_grad

      dres_dphi = zero
      do i = 1, 5
        dres_dphi(:) = dres_dphi(:) + soln%phi(i,node1) * (            &
          extrapolation1(1) * soln%gradx(i,node1) +                    &
          extrapolation1(2) * soln%grady(i,node1) +                    &
          extrapolation1(3) * soln%gradz(i,node1) )                    &
          * dres_dstate(i,:)
      end do

      local_node1_lim_cc1 : if ( node1 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp1
        call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node1_lim_cc1
      local_node1_lim_cc2 : if ( node2 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp1
        call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node1_lim_cc2

      local_node1_grad1 : if ( node1 <= grid%nnodes0  ) then
        dres_dgradx = zero
        dres_dgradx(5,:) = dres_dphi(:)*dphi_dgradpx1
        dres_dgrady = zero
        dres_dgrady(5,:) = dres_dphi(:)*dphi_dgradpy1
        dres_dgradz = zero
        dres_dgradz(5,:) = dres_dphi(:)*dphi_dgradpz1
        call gradient_deriv_cut( grid, soln, design,    &
          node1, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )
      end if local_node1_grad1
      local_node1_grad2 : if ( node2 <= grid%nnodes0  ) then
        dres_dgradx = zero
        dres_dgradx(5,:) = dres_dphi(:)*dphi_dgradpx2
        dres_dgrady = zero
        dres_dgrady(5,:) = dres_dphi(:)*dphi_dgradpy2
        dres_dgradz = zero
        dres_dgradz(5,:) = dres_dphi(:)*dphi_dgradpz2
        call gradient_deriv_cut( grid, soln, design,    &
          node2, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )
      end if local_node1_grad2
    end if no_feas_limit1

! call the stuff that depends on the state2
    dres_dstate = zero
    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
            + coltag(i,node1) * rlam(i,node1,k) * df_dstate2(i,j)  &
            - coltag(i,node2) * rlam(i,node2,k) * df_dstate2(i,j) )
        end do
      end do
    end do

    local_node2_cc : if ( node2 <= grid%nnodes0  ) then
      call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node2,k) = sadj%res(i,node2,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do
    end if local_node2_cc

    no_feas_limit2 : if ( .not. realizable_limit2 ) then
      local_node2_grad : if ( node2 <= grid%nnodes0  ) then
        do k = 1, design%nfunctions
          dres_dgradx(:,k) = phi * soln%phi(:,node2) * extrapolation2(1) * &
            dres_dstate(:,k)
          dres_dgrady(:,k) = phi * soln%phi(:,node2) * extrapolation2(2) * &
            dres_dstate(:,k)
          dres_dgradz(:,k) = phi * soln%phi(:,node2) * extrapolation2(3) * &
            dres_dstate(:,k)
        end do
        call gradient_deriv_cut( grid, soln, design,    &
          node2, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )
      end if local_node2_grad

      dres_dphi = zero
      do i = 1, 5
        dres_dphi(:) = dres_dphi(:) + soln%phi(i,node1) * (            &
          extrapolation1(1) * soln%gradx(i,node1) +                    &
          extrapolation1(2) * soln%grady(i,node1) +                    &
          extrapolation1(3) * soln%gradz(i,node1) )                    &
          * dres_dstate(i,:)
      end do

      local_node2_lim_cc1 : if ( node1 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp1
        call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node1,k) = sadj%res(i,node1,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node2_lim_cc1
      local_node2_lim_cc2 : if ( node2 <= grid%nnodes0  ) then
        dres_dlim = zero
        dres_dlim(5,:) = dres_dphi(:)*dphi_dp2
        call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
        do i = 1, 5
          do j = 1, 5
            do k = 1, design%nfunctions
              sadj%res(i,node2,k) = sadj%res(i,node2,k) &
                + dQdq(j,i)*dres_dlim(j,k)
            end do
          end do
        end do
      end if local_node2_lim_cc2

      local_node2_grad1 : if ( node1 <= grid%nnodes0  ) then
        dres_dgradx = zero
        dres_dgradx(5,:) = dres_dphi(:)*dphi_dgradpx1
        dres_dgrady = zero
        dres_dgrady(5,:) = dres_dphi(:)*dphi_dgradpy1
        dres_dgradz = zero
        dres_dgradz(5,:) = dres_dphi(:)*dphi_dgradpz1
        call gradient_deriv_cut( grid, soln, design,    &
          node1, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )
      end if local_node2_grad1
      local_node2_grad2 : if ( node2 <= grid%nnodes0  ) then
        dres_dgradx = zero
        dres_dgradx(5,:) = dres_dphi(:)*dphi_dgradpx2
        dres_dgrady = zero
        dres_dgrady(5,:) = dres_dphi(:)*dphi_dgradpy2
        dres_dgradz = zero
        dres_dgradz(5,:) = dres_dphi(:)*dphi_dgradpz2
        call gradient_deriv_cut( grid, soln, design,    &
          node2, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )
      end if local_node2_grad2
    end if no_feas_limit2

! add in the cut_lap terms

    if (node1 <= grid%nnodes0) then
      if ( soln%q_dof(5,node1) < cut_lap_limit ) then
        length = sqrt( (grid%x(node2)-grid%x(node1)) ** 2 + &
                       (grid%y(node2)-grid%y(node1)) ** 2 + &
                       (grid%x(node2)-grid%z(node1)) ** 2 )
        lap = cut_lap_coef*(cut_lap_limit-soln%q_dof(5,node1))**2
        do k = 1, design%nfunctions
          do i = 1, 5
            sadj%res(i,node1,k) = sadj%res(i,node1,k) + area * lap/length *   &
              coltag(i,node1) * rlam(i,node1,k)
            sadj%res(i,node2,k) = sadj%res(i,node2,k) - area * lap/length *   &
              coltag(i,node1) * rlam(i,node1,k)
          end do
        end do
        dlap_dp = -cut_lap_coef*two*(cut_lap_limit-soln%q_dof(5,node1))
        conserved1 = in_conserved_variables(soln%q_dof(:,node1))
        conserved2 = in_conserved_variables(soln%q_dof(:,node2))
        call dprimitive_dconserved(soln%q_dof(:,node1),dQdq)
        do k = 1, design%nfunctions
          do j = 1, 5
            do i = 1, 5
              sadj%res(j,node1,k) = sadj%res(j,node1,k) -                    &
                area * dlap_dp/length *                                      &
                (conserved2(i)-conserved1(i)) * dQdq(j,5) *                  &
                coltag(i,node1) * rlam(i,node1,k)
            end do
          end do
        end do
      end if
    end if

    if (node2 <= grid%nnodes0) then
      if ( soln%q_dof(5,node2) < cut_lap_limit ) then
        length = sqrt( (grid%x(node1)-grid%x(node2)) ** 2 + &
                       (grid%y(node1)-grid%y(node2)) ** 2 + &
                       (grid%x(node1)-grid%z(node2)) ** 2 )
        lap = cut_lap_coef*(cut_lap_limit-soln%q_dof(5,node2))**2
        do k = 1, design%nfunctions
          do i = 1, 5
            sadj%res(i,node2,k) = sadj%res(i,node2,k) + area * lap/length *   &
              coltag(i,node2) * rlam(i,node2,k)
            sadj%res(i,node1,k) = sadj%res(i,node1,k) - area * lap/length *   &
              coltag(i,node2) * rlam(i,node2,k)
          end do
        end do
        dlap_dp = -cut_lap_coef*two*(cut_lap_limit-soln%q_dof(5,node2))
        conserved1 = in_conserved_variables(soln%q_dof(:,node1))
        conserved2 = in_conserved_variables(soln%q_dof(:,node2))
        call dprimitive_dconserved(soln%q_dof(:,node2),dQdq)
        do k = 1, design%nfunctions
          do j = 1, 5
            do i = 1, 5
              sadj%res(j,node2,k) = sadj%res(j,node2,k) -                    &
                area * dlap_dp/length *                                      &
                (conserved1(i)-conserved2(i)) * dQdq(j,5) *                  &
                coltag(i,node2) * rlam(i,node2,k)
            end do
          end do
        end do
      end if
    end if

  end subroutine integrate_dual_atlam_iflim

!================================= integrate_visc_atlam ======================80
!
! integrate laminar part of atlam for a triangle between two duals
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_visc_atlam(                &
    grid, soln, sadj, design, coltag, rlam,       &
    node1,node2,                                  &
    normal, area)

    use kinddefs,         only : dp
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,        only : sadj_type
    use design_types,        only : design_type

    use thermo,           only : dprimitive_dconserved

    use flux_functions,   only : visc_primitive_jacobian, &
                                 full_visc_primitive_jacobian

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: node1, node2

    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    real(dp)    :: dx, dy, dz

    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradx
    real(dp), dimension(5,design%nfunctions)    :: dres_dgrady
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradz

    integer :: i,j,k

    real(dp), dimension(5,5)    :: df_dstate1,df_dstate2
    real(dp), dimension(5,5)    :: df_dgradx1,df_dgradx2
    real(dp), dimension(5,5)    :: df_dgrady1,df_dgrady2
    real(dp), dimension(5,5)    :: df_dgradz1,df_dgradz2

    real(dp), dimension(5,5)    :: dQdq

    real(dp), parameter    :: zero    = 0.0_dp

    continue

    dx = grid%x(node2) - grid%x(node1)
    dy = grid%y(node2) - grid%y(node1)
    dz = grid%z(node2) - grid%z(node1)

    if ( soln%viscous_method == 2 ) then
      call visc_primitive_jacobian(soln%q_dof(:,node1),soln%q_dof(:,node2), &
                                   soln%amut(node1),soln%amut(node2),       &
                                   dx,dy,dz,normal,df_dstate1,df_dstate2)
      df_dgradx1 = zero
      df_dgrady1 = zero
      df_dgradz1 = zero
      df_dgradx2 = zero
      df_dgrady2 = zero
      df_dgradz2 = zero
    else
      call full_visc_primitive_jacobian( &
        soln%q_dof(:,node1),             &
        soln%gradx(:,node1),             &
        soln%grady(:,node1),             &
        soln%gradz(:,node1),             &
        soln%q_dof(:,node2),             &
        soln%gradx(:,node2),             &
        soln%grady(:,node2),             &
        soln%gradz(:,node2),             &
        soln%amut(node1),                &
        soln%amut(node2),                &
        dx,dy,dz,normal,                 &
        df_dstate1,df_dstate2,           &
        df_dgradx1,df_dgradx2,           &
        df_dgrady1,df_dgrady2,           &
        df_dgradz1,df_dgradz2 )
    end if

! call the stuff that depends on the state1
    local_node1 : if ( node1 <= grid%nnodes0  ) then
      dres_dstate = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dstate1(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dstate1(i,j) )
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node1,k) = sadj%res(i,node1,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      dres_dgradx = zero
      dres_dgrady = zero
      dres_dgradz = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dgradx(j,k) = dres_dgradx(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dgradx1(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dgradx1(i,j) )
            dres_dgrady(j,k) = dres_dgrady(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dgrady1(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dgrady1(i,j) )
            dres_dgradz(j,k) = dres_dgradz(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dgradz1(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dgradz1(i,j) )
          end do
        end do
      end do
      call gradient_deriv_cut( grid, soln, design,    &
        node1, dres_dgradx, dres_dgrady, dres_dgradz, &
        sadj%res )
    end if local_node1

! call the stuff that depends on the state2
    local_node2 : if ( node2 <= grid%nnodes0  ) then
      dres_dstate = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(j,k) = dres_dstate(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dstate2(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dstate2(i,j) )
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node2,k) = sadj%res(i,node2,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      dres_dgradx = zero
      dres_dgrady = zero
      dres_dgradz = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dgradx(j,k) = dres_dgradx(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dgradx2(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dgradx2(i,j) )
            dres_dgrady(j,k) = dres_dgrady(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dgrady2(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dgrady2(i,j) )
            dres_dgradz(j,k) = dres_dgradz(j,k) + area*(             &
              + coltag(i,node1) * rlam(i,node1,k) * df_dgradz2(i,j)  &
              - coltag(i,node2) * rlam(i,node2,k) * df_dgradz2(i,j) )
          end do
        end do
      end do
      call gradient_deriv_cut( grid, soln, design,    &
        node2, dres_dgradx, dres_dgrady, dres_dgradz, &
        sadj%res )
    end if local_node2

  end subroutine integrate_visc_atlam

!================================= integrate_dual_atlam_bcf ==================80
!
! integrate atlam for a boundary triangle
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dual_atlam_bcf(            &
    grid, soln, sadj, design, coltag, rlam,       &
    ibc,node,                                     &
    triangle_node1,triangle_node2,triangle_node3, &
    normal,area,                                  &
    dr_dgx, dr_dgy, dr_dgz )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations, iflim

    use flux_bc,              only : farfield_riem_prim_jacob,   &
                                     elem_based_bc_prim_jacob,   &
                                     farfield_extrap_prim_jacob, &
                                     farfield_pback_prim_jacob,  &
                                     tangent_prim_jacob,         &
                                     blown_te_prim_jacob
    use limiter_functions,    only : extrap_grad

    use thermo,               only : dprimitive_dconserved

    use cut_types,            only : cut_bc_extrapolation_order

    use quadrature_rules,     only : triangle01_nq,    &
                                     triangle01_bary1, &
                                     triangle01_bary2, &
                                     triangle01_bary3, &
                                     triangle01_weight

    use bc_names,             only : symmetry_x, symmetry_y, symmetry_z

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: ibc, node

    real(dp), dimension(3),     intent(in) :: triangle_node1
    real(dp), dimension(3),     intent(in) :: triangle_node2
    real(dp), dimension(3),     intent(in) :: triangle_node3
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                                                intent(inout) :: dr_dgx
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                                                intent(inout) :: dr_dgy
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                                                intent(inout) :: dr_dgz

    integer :: my_ntt
    real(dp)    :: second
    real(dp)    :: phi
    real(dp)    :: dphi_dpressure
    real(dp)    :: dphi_dgradx, dphi_dgrady, dphi_dgradz

    integer :: ixyz
    real(dp), dimension(3)    :: integration_point

    logical :: realizable_limit
    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dlim

    real(dp), dimension(5,5)    :: dQdq

    real(dp), dimension(design%nfunctions)    :: dres_dphi

    integer :: i,j,k

    real(dp)    :: dx, dy, dz
    real(dp), dimension(5)    :: state
    real(dp), dimension(5,5)    :: dflux_dstate

    real(dp), parameter    :: zero    = 0.0_dp

    integer :: iq

    continue

    if ( node > grid%nnodes0  ) return

    my_ntt = ntt
    if (itime /= 0) my_ntt = pseudo_sub

    second = cut_bc_extrapolation_order
    if (my_ntt <= first_order_iterations) second = zero

    sum_quadrature_points : do iq = 1, triangle01_nq

      do ixyz = 1, 3
        integration_point(ixyz) = triangle01_bary1(iq)*triangle_node1(ixyz) &
                                + triangle01_bary2(iq)*triangle_node2(ixyz) &
                                + triangle01_bary3(iq)*triangle_node3(ixyz)
      end do

      dx = integration_point(1) - grid%x(node)
      dy = integration_point(2) - grid%y(node)
      dz = integration_point(3) - grid%z(node)

      phi = second
      if (iflim == 3) then
        call extrap_grad( dx, dy, dz, soln%q_dof(5,node),             &
          soln%gradx(5,node), soln%grady(5,node), soln%gradz(5,node), &
          phi, dphi_dpressure,                                        &
          dphi_dgradx, dphi_dgrady, dphi_dgradz)
        phi = phi * second
        dphi_dpressure = dphi_dpressure * second
        dphi_dgradx = dphi_dgradx * second
        dphi_dgrady = dphi_dgrady * second
        dphi_dgradz = dphi_dgradz * second
      end if

      state = soln%q_dof(:,node)                                &
            + second * soln%phi(:,node) * soln%gradx(:,node)*dx &
            + second * soln%phi(:,node) * soln%grady(:,node)*dy &
            + second * soln%phi(:,node) * soln%gradz(:,node)*dz

      realizable_limit = .false.
      if ( state(1) <= zero .or. state(5) <= zero ) then
        state = soln%q_dof(:,node)
        realizable_limit = .true.
      end if

      select case(ibc)
      case (3000,4000,4040)
        call tangent_prim_jacob(state, normal, dflux_dstate)
      case (symmetry_x,symmetry_y,symmetry_z)
        state = soln%q_dof(:,node)
        realizable_limit = .true.
        call tangent_prim_jacob(state, normal, dflux_dstate)
      case (3055)
        call blown_te_prim_jacob(state, normal, dflux_dstate)
      case (5000)
        call farfield_riem_prim_jacob(state, normal, dflux_dstate)
      case (5050,7011,5051)
        call elem_based_bc_prim_jacob(ibc, state, normal, dflux_dstate)
      case (5005)
        call farfield_extrap_prim_jacob(state, normal, dflux_dstate)
      case (5010,5011)
        call farfield_pback_prim_jacob(state, normal, dflux_dstate)
      case (5015)
        dflux_dstate = zero
      case default
        write(*,*) 'residual_cut:integrate_dual_atlam_bcf, unknown ibc =',ibc
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end select

      dres_dstate = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(j,k) = dres_dstate(j,k) + area * triangle01_weight(iq)*&
              coltag(i,node) * rlam(i,node,k) * dflux_dstate(i,j)
          end do
        end do
      end do

! cell centered part
      call dprimitive_dconserved( soln%q_dof(:,node), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node,k) = sadj%res(i,node,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      second_order: if ( .not. realizable_limit ) then

        ! gradient part
        do j = 1, 5
          do k = 1, design%nfunctions
            dr_dgx(j,k,node) = dr_dgx(j,k,node) &
              + phi * soln%phi(j,node) * dx * dres_dstate(j,k)
            dr_dgy(j,k,node) = dr_dgy(j,k,node) &
              + phi * soln%phi(j,node) * dy * dres_dstate(j,k)
            dr_dgz(j,k,node) = dr_dgz(j,k,node) &
              + phi * soln%phi(j,node) * dz * dres_dstate(j,k)
          end do
        end do

        limiter_derivatives : if (iflim == 3) then
          dres_dphi = zero
          do i = 1, 5
            dres_dphi(:) = dres_dphi(:) + soln%phi(i,node) * ( &
              dx * soln%gradx(i,node) +                        &
              dy * soln%grady(i,node) +                        &
              dz * soln%gradz(i,node) )                        &
              * dres_dstate(i,:)
          end do

          dres_dlim = zero
          dres_dlim(5,:) = dres_dphi(:)*dphi_dpressure
          call dprimitive_dconserved( soln%q_dof(:,node), dQdq )
          do i = 1, 5
            do j = 1, 5
              do k = 1, design%nfunctions
                sadj%res(i,node,k) = sadj%res(i,node,k) &
                  + dQdq(j,i)*dres_dlim(j,k)
              end do
            end do
          end do

          do k = 1, design%nfunctions
            dr_dgx(5,k,node) = dr_dgx(5,k,node) &
              + dres_dphi(k)*dphi_dgradx
            dr_dgy(5,k,node) = dr_dgy(5,k,node) &
              + dres_dphi(k)*dphi_dgrady
            dr_dgz(5,k,node) = dr_dgz(5,k,node) &
              + dres_dphi(k)*dphi_dgradz
          end do

        end if limiter_derivatives
      end if second_order

    end do sum_quadrature_points

  end subroutine integrate_dual_atlam_bcf

!================================= integrate_dual_atlam_bc ==================80
!
! integrate atlam for a boundary triangle
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dual_atlam_bc(             &
    grid, soln, sadj, design, coltag, rlam,       &
    ibc,node,                                     &
    triangle_node1,triangle_node2,triangle_node3,normal,area )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type

    use info_depr,            only : ntt
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations, iflim

    use flux_bc,              only : farfield_riem_prim_jacob,   &
                                     elem_based_bc_prim_jacob,   &
                                     farfield_extrap_prim_jacob, &
                                     farfield_pback_prim_jacob,  &
                                     tangent_prim_jacob,         &
                                     blown_te_prim_jacob
    use limiter_functions,    only : extrap_grad

    use thermo,               only : dprimitive_dconserved

    use cut_types,            only : cut_bc_extrapolation_order

    use quadrature_rules,     only : triangle01_nq,    &
                                     triangle01_bary1, &
                                     triangle01_bary2, &
                                     triangle01_bary3, &
                                    triangle01_weight

    use bc_names,             only : symmetry_x, symmetry_y, symmetry_z

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: ibc, node

    real(dp), dimension(3),     intent(in) :: triangle_node1
    real(dp), dimension(3),     intent(in) :: triangle_node2
    real(dp), dimension(3),     intent(in) :: triangle_node3
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    integer :: my_ntt
    real(dp)    :: second
    real(dp)    :: phi
    real(dp)    :: dphi_dpressure
    real(dp)    :: dphi_dgradx, dphi_dgrady, dphi_dgradz

    integer :: ixyz
    real(dp), dimension(3)    :: integration_point

    logical :: realizable_limit
    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradx
    real(dp), dimension(5,design%nfunctions)    :: dres_dgrady
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradz
    real(dp), dimension(5,design%nfunctions)    :: dres_dlim

    real(dp), dimension(5,5)    :: dQdq

    real(dp), dimension(design%nfunctions)    :: dres_dphi

    integer :: i,j,k

    real(dp)    :: dx, dy, dz
    real(dp), dimension(5)    :: state
    real(dp), dimension(5,5)    :: dflux_dstate

    real(dp), parameter    :: zero    = 0.0_dp

    integer :: iq

    continue

    if ( node > grid%nnodes0  ) return

    my_ntt = ntt
    if (itime /= 0) my_ntt = pseudo_sub

    second = cut_bc_extrapolation_order
    if (my_ntt <= first_order_iterations) second = zero

    sum_quadrature_points : do iq = 1, triangle01_nq

      do ixyz = 1, 3
        integration_point(ixyz) = triangle01_bary1(iq)*triangle_node1(ixyz) &
                                + triangle01_bary2(iq)*triangle_node2(ixyz) &
                                + triangle01_bary3(iq)*triangle_node3(ixyz)
      end do

      dx = integration_point(1) - grid%x(node)
      dy = integration_point(2) - grid%y(node)
      dz = integration_point(3) - grid%z(node)

      phi = second
      if (iflim == 3) then
        call extrap_grad( dx, dy, dz, soln%q_dof(5,node),             &
          soln%gradx(5,node), soln%grady(5,node), soln%gradz(5,node), &
          phi, dphi_dpressure,                                        &
          dphi_dgradx, dphi_dgrady, dphi_dgradz)
        phi = phi * second
        dphi_dpressure = dphi_dpressure * second
        dphi_dgradx = dphi_dgradx * second
        dphi_dgrady = dphi_dgrady * second
        dphi_dgradz = dphi_dgradz * second
      end if

      state = soln%q_dof(:,node)                                &
            + second * soln%phi(:,node) * soln%gradx(:,node)*dx &
            + second * soln%phi(:,node) * soln%grady(:,node)*dy &
            + second * soln%phi(:,node) * soln%gradz(:,node)*dz

      realizable_limit = .false.
      if ( state(1) <= zero .or. state(5) <= zero ) then
        state = soln%q_dof(:,node)
        realizable_limit = .true.
      end if

      select case(ibc)
      case (3000,4000,4040)
        call tangent_prim_jacob(state, normal, dflux_dstate)
      case (symmetry_x, symmetry_y, symmetry_z)
        state = soln%q_dof(:,node)
        realizable_limit = .true.
        call tangent_prim_jacob(state, normal, dflux_dstate)
      case (3055)
        call blown_te_prim_jacob(state, normal, dflux_dstate)
      case (5000)
        call farfield_riem_prim_jacob(state, normal, dflux_dstate)
      case (5050,7011,5051)
        call elem_based_bc_prim_jacob(ibc, state, normal, dflux_dstate)
      case (5005)
        call farfield_extrap_prim_jacob(state, normal, dflux_dstate)
      case (5010,5011)
        call farfield_pback_prim_jacob(state, normal, dflux_dstate)
      case (5015)
        dflux_dstate = zero
      case default
        write(*,*) 'residual_cut:integrate_dual_atlam_bc, unknown ibc =',ibc
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end select

      dres_dstate = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(j,k) = dres_dstate(j,k) + area * triangle01_weight(iq)*&
              coltag(i,node) * rlam(i,node,k) * dflux_dstate(i,j)
          end do
        end do
      end do

! cell centered part
      call dprimitive_dconserved( soln%q_dof(:,node), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node,k) = sadj%res(i,node,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      second_order: if ( .not. realizable_limit ) then

        ! gradient part
        do k = 1, design%nfunctions
          dres_dgradx(:,k) = phi * soln%phi(:,node) * dx * dres_dstate(:,k)
          dres_dgrady(:,k) = phi * soln%phi(:,node) * dy * dres_dstate(:,k)
          dres_dgradz(:,k) = phi * soln%phi(:,node) * dz * dres_dstate(:,k)
        end do
        call gradient_deriv_cut( grid, soln, design,   &
          node, dres_dgradx, dres_dgrady, dres_dgradz, &
          sadj%res )

        limiter_derivatives : if (iflim == 3) then
          dres_dphi = zero
          do i = 1, 5
            dres_dphi(:) = dres_dphi(:) + soln%phi(i,node) * ( &
              dx * soln%gradx(i,node) +                        &
              dy * soln%grady(i,node) +                        &
              dz * soln%gradz(i,node) )                        &
              * dres_dstate(i,:)
          end do

          dres_dlim = zero
          dres_dlim(5,:) = dres_dphi(:)*dphi_dpressure
          call dprimitive_dconserved( soln%q_dof(:,node), dQdq )
          do i = 1, 5
            do j = 1, 5
              do k = 1, design%nfunctions
                sadj%res(i,node,k) = sadj%res(i,node,k) &
                  + dQdq(j,i)*dres_dlim(j,k)
              end do
            end do
          end do

          dres_dgradx = zero
          dres_dgradx(5,:) = dres_dphi(:)*dphi_dgradx
          dres_dgrady = zero
          dres_dgrady(5,:) = dres_dphi(:)*dphi_dgrady
          dres_dgradz = zero
          dres_dgradz(5,:) = dres_dphi(:)*dphi_dgradz
          call gradient_deriv_cut( grid, soln, design,   &
            node, dres_dgradx, dres_dgrady, dres_dgradz, &
            sadj%res )
        end if limiter_derivatives
      end if second_order

    end do sum_quadrature_points

  end subroutine integrate_dual_atlam_bc

!================================= integrate_visc_atlam_bc ==================80
!
! integrate atlam for a boundary triangle (laminar part)
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_visc_atlam_bc(             &
    grid, soln, sadj, design, coltag, rlam,       &
    ibc,node,                                     &
    triangle_node1,triangle_node2,triangle_node3,normal,area )

    use kinddefs,         only : dp
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use solution_adj,     only : sadj_type
    use design_types,     only : design_type

    use fluid,            only : gamma
    use bc_names,         only : twall

    use ivals,            only : u0, v0, w0
    use flux_functions,   only : visc_primitive_jacobian, &
                                 full_visc_primitive_jacobian

    use thermo,           only : dprimitive_dconserved

    use quadrature_rules, only : triangle01_nq,    &
                                 triangle01_bary1, &
                                 triangle01_bary2, &
                                 triangle01_bary3, &
                                 triangle01_weight

    use bc_names,         only : symmetry_x, symmetry_y, symmetry_z

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(sadj_type),   intent(inout) :: sadj
    type(design_type), intent(in)    :: design
    real(dp), dimension(soln%adim,grid%nnodes01),    intent(in)    :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(in) :: rlam

    integer,  intent(in) :: ibc, node

    real(dp), dimension(3),     intent(in) :: triangle_node1
    real(dp), dimension(3),     intent(in) :: triangle_node2
    real(dp), dimension(3),     intent(in) :: triangle_node3
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    integer :: ixyz
    real(dp), dimension(3)    :: integration_point

    real(dp), dimension(5)    :: boundary_state

    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradx
    real(dp), dimension(5,design%nfunctions)    :: dres_dgrady
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradz

    real(dp), dimension(5,5)    :: dQdq

    integer :: i,j,k

    real(dp)    :: dot
    real(dp)    :: dx, dy, dz
    real(dp), dimension(5,5)    :: df_dstate1
    real(dp), dimension(5,5)    :: df_dgradx1
    real(dp), dimension(5,5)    :: df_dgrady1
    real(dp), dimension(5,5)    :: df_dgradz1
    real(dp), dimension(5,5)    :: df_dstate2
    real(dp), dimension(5,5)    :: df_dgradx2
    real(dp), dimension(5,5)    :: df_dgrady2
    real(dp), dimension(5,5)    :: df_dgradz2

    integer :: iq

    real(dp), parameter    :: zero = 0.0_dp
    real(dp), parameter    :: two  = 2.0_dp

    continue

    if ( node > grid%nnodes0  ) return

    sum_quadrature_points : do iq = 1, triangle01_nq

      do ixyz = 1, 3
        integration_point(ixyz) = triangle01_bary1(iq)*triangle_node1(ixyz) &
                                + triangle01_bary2(iq)*triangle_node2(ixyz) &
                                + triangle01_bary3(iq)*triangle_node3(ixyz)
      end do

      dx = integration_point(1) - grid%x(node)
      dy = integration_point(2) - grid%y(node)
      dz = integration_point(3) - grid%z(node)
      dot = dx*normal(1) + &
            dy*normal(2) + &
            dz*normal(3)
      dot = abs(dot)
      dx = dot*normal(1)
      dy = dot*normal(2)
      dz = dot*normal(3)

      select case(ibc)
      case (4000) ! on no-slip boundaries (not ghost node)
        boundary_state(1) = gamma/twall*soln%q_dof(5,node)
        boundary_state(2) = zero
        boundary_state(3) = zero
        boundary_state(4) = zero
        boundary_state(5) = soln%q_dof(5,node)
      case (4040) ! on no-slip boundaries (not ghost node)
        boundary_state(1) = gamma/twall*soln%q_dof(5,node)
        boundary_state(2) = u0
        boundary_state(3) = v0
        boundary_state(4) = w0
        boundary_state(5) = soln%q_dof(5,node)
      case (3000)
        return ! do not close off visc fluxes on inviscid surfaces
      case (5000,5010,5011,5015,5050,7011,5051)
        return ! do not close off visc fluxes on farfield surfaces
      case (5005)
        dx = two * dx
        dy = two * dy
        dz = two * dz
        boundary_state = soln%q_dof(:,node)
      case (symmetry_x, symmetry_y, symmetry_z)
        return ! do not close off visc fluxes on symmetry planes
      case default
        write(*,*) 'integrate_visc_atlam_bc, unknown ibc =',ibc
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end select

      if (  soln%viscous_method == 2 ) then
        call visc_primitive_jacobian(soln%q_dof(:,node),boundary_state, &
                                     soln%amut(node),soln%amut(node),   &
                                     dx,dy,dz,normal,df_dstate1,df_dstate2)
        df_dgradx1 = zero
        df_dgrady1 = zero
        df_dgradz1 = zero
        df_dgradx2 = zero
        df_dgrady2 = zero
        df_dgradz2 = zero
      else
        call full_visc_primitive_jacobian( &
          soln%q_dof(:,node),              &
          soln%gradx(:,node),              &
          soln%grady(:,node),              &
          soln%gradz(:,node),              &
          boundary_state,                  &
          soln%gradx(:,node),              &
          soln%grady(:,node),              &
          soln%gradz(:,node),              &
          soln%amut(node),                 &
          soln%amut(node),                 &
          dx,dy,dz,normal,                 &
          df_dstate1,df_dstate2,           &
          df_dgradx1,df_dgradx2,           &
          df_dgrady1,df_dgrady2,           &
          df_dgradz1,df_dgradz2 )
      end if

      dres_dstate = zero
      do i = 1, 5
        do k = 1, design%nfunctions
          do j = 1, 5
            dres_dstate(j,k) = dres_dstate(j,k) + area * triangle01_weight(iq)*&
              coltag(i,node) * rlam(i,node,k) * df_dstate1(i,j)
          end do
        end do
      end do

      select case(ibc)
      case (4000,4040) ! on no-slip boundaries (not ghost node)
        do i = 1, 5
          do k = 1, design%nfunctions
            dres_dstate(5,k) = dres_dstate(5,k) + area * triangle01_weight(iq)*&
              coltag(i,node) * rlam(i,node,k) * gamma/twall*df_dstate2(i,1)
            dres_dstate(5,k) = dres_dstate(5,k) + area * triangle01_weight(iq)*&
              coltag(i,node) * rlam(i,node,k) * df_dstate2(i,5)
          end do
        end do
      case (5005)
        do i = 1, 5
          do k = 1, design%nfunctions
            do j = 1, 5
              dres_dstate(j,k) = dres_dstate(j,k) + area*triangle01_weight(iq)*&
                coltag(i,node) * rlam(i,node,k) * df_dstate2(i,j)
            end do
          end do
        end do
      case default
        write(*,*) 'integrate_visc_atlam_bc, unknown ibc =',ibc
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end select

! cell centered part
      call dprimitive_dconserved( soln%q_dof(:,node), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node,k) = sadj%res(i,node,k) &
              + dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      dres_dgradx = zero
      dres_dgrady = zero
      dres_dgradz = zero
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dres_dgradx(j,k) = dres_dgradx(j,k) + area*(             &
              + coltag(i,node) * rlam(i,node,k) * df_dgradx1(i,j) )
            dres_dgrady(j,k) = dres_dgrady(j,k) + area*(             &
              + coltag(i,node) * rlam(i,node,k) * df_dgrady1(i,j) )
            dres_dgradz(j,k) = dres_dgradz(j,k) + area*(             &
              + coltag(i,node) * rlam(i,node,k) * df_dgradz1(i,j) )
            dres_dgradx(j,k) = dres_dgradx(j,k) + area*(             &
              + coltag(i,node) * rlam(i,node,k) * df_dgradx2(i,j) )
            dres_dgrady(j,k) = dres_dgrady(j,k) + area*(             &
              + coltag(i,node) * rlam(i,node,k) * df_dgrady2(i,j) )
            dres_dgradz(j,k) = dres_dgradz(j,k) + area*(             &
              + coltag(i,node) * rlam(i,node,k) * df_dgradz2(i,j) )
          end do
        end do
      end do
      call gradient_deriv_cut( grid, soln, design,   &
        node, dres_dgradx, dres_dgrady, dres_dgradz, &
        sadj%res )

    end do sum_quadrature_points

  end subroutine integrate_visc_atlam_bc

!================================= cut_cell_dfdq =============================80
!
! compute cut boundary adjoint residual pieces (A transpose lambda)
!   for cut cell grid
! soln%q_dof expected in conserved
!
!=============================================================================80
  subroutine cut_cell_dfdq( grid, soln, design, force, dfdq )

    use kinddefs,            only : dp
    use grid_types,          only : grid_type
    use solution_types,      only : soln_type
    use design_types,        only : design_type
    use force_types,         only : force_type
    use bc_names,            only : bc_used_for_force_calculation, &
                                    bc_has_skin_friction,          &
                                    bc_is_flow_through

    use cut_types,           only : cut

    use lmpi_app,            only : lmpi_sumnode
    use thermo,              only : etop, ptoe

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design
    type(force_type),  intent(in)    :: force
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(inout) :: dfdq

! create a local version of dfdq, so that I can use lmpi_sumnode safely
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions)    ::dfdq_cut

    integer :: bc, surf, triangle, ibc
    integer :: i,j,k

    real(dp), parameter    :: zero = 0.0_dp

    continue

    call etop(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)

    dfdq_cut = zero

    exact_dual_int_on_cut_bcs : do bc = 1, cut%cut_bcs
      bc_triangle : do triangle=1, cut%cut_bc(bc)%number_of_triangles
        ibc = cut%cut_bc(bc)%ibc
        if ( -1 == ibc ) ibc = cut%cut_bc(bc)%triangle_ibc(triangle)
        if ( bc_is_flow_through(ibc) ) cycle
        bc_applies_force : if ( bc_used_for_force_calculation(ibc) ) then
          call integrate_dfdq(                                                 &
            grid, soln, design, force, dfdq_cut,                               &
            ibc,                                                               &
            cut%cut_bc(bc)%node,                                               &
            cut%cut_bc(bc)%triangle_node1(:,triangle),                         &
            cut%cut_bc(bc)%triangle_node2(:,triangle),                         &
            cut%cut_bc(bc)%triangle_node3(:,triangle),                         &
            cut%cut_bc(bc)%triangle_normal(:,triangle),                        &
            cut%cut_bc(bc)%triangle_area(triangle) )
          if ( bc_has_skin_friction(ibc) )                                     &
            call integrate_dfdq_visc(                                          &
              grid, soln, design, force, dfdq_cut,                             &
              ibc,                                                             &
              cut%cut_bc(bc)%node,                                             &
              cut%cut_bc(bc)%triangle_node1(:,triangle),                       &
              cut%cut_bc(bc)%triangle_node2(:,triangle),                       &
              cut%cut_bc(bc)%triangle_node3(:,triangle),                       &
              cut%cut_bc(bc)%triangle_normal(:,triangle),                      &
              cut%cut_bc(bc)%triangle_area(triangle) )
        end if bc_applies_force
      end do bc_triangle
    end do exact_dual_int_on_cut_bcs

    exact_dual_int_on_cut_surfs : do surf = 1, cut%cut_surfs
      surf_triangle : do triangle=1, cut%cut_surf(surf)%number_of_triangles
        ibc = cut%cut_surf(surf)%ibc
        if ( -1 == ibc ) ibc = cut%cut_surf(surf)%triangle_ibc(triangle)
        if ( bc_is_flow_through(ibc) ) cycle
        surf_applies_force : if ( bc_used_for_force_calculation(ibc) ) then
          call integrate_dfdq(                                                 &
            grid, soln, design, force, dfdq_cut,                               &
            ibc,                                                               &
            cut%cut_surf(surf)%node,                                           &
            cut%cut_surf(surf)%triangle_node1(:,triangle),                     &
            cut%cut_surf(surf)%triangle_node2(:,triangle),                     &
            cut%cut_surf(surf)%triangle_node3(:,triangle),                     &
            cut%cut_surf(surf)%triangle_normal(:,triangle),                    &
            cut%cut_surf(surf)%triangle_area(triangle) )
          if ( bc_has_skin_friction(ibc) )                                     &
            call integrate_dfdq_visc(                                          &
              grid, soln, design, force, dfdq_cut,                             &
              ibc,                                                             &
              cut%cut_surf(surf)%node,                                         &
              cut%cut_surf(surf)%triangle_node1(:,triangle),                   &
              cut%cut_surf(surf)%triangle_node2(:,triangle),                   &
              cut%cut_surf(surf)%triangle_node3(:,triangle),                   &
              cut%cut_surf(surf)%triangle_normal(:,triangle),                  &
              cut%cut_surf(surf)%triangle_area(triangle) )
        end if surf_applies_force
      end do surf_triangle
    end do exact_dual_int_on_cut_surfs

    do k = 1, design%nfunctions
      call lmpi_sumnode(dfdq_cut(:,:,k),1)
      do j = 1, grid%nnodes0
        do i = 1, soln%adim
          dfdq(i,j,k) = dfdq(i,j,k) + dfdq_cut(i,j,k)
        end do
      end do
    end do

    call ptoe(size(soln%q_dof,2),soln%q_dof,soln%n_tot,soln%eqn_set)

  end subroutine cut_cell_dfdq

!================================= integrate_dfdq ============================80
!
! integrate dfdq for a boundary triangle
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dfdq(               &
    grid, soln, design, force, dfdq,       &
    ibc,node,                              &
    triangle_node1,triangle_node2,triangle_node3,normal,area )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use design_types,         only : design_type

    use info_depr,            only : ntt, alpha, yaw, xmach
    use nml_nonlinear_solves, only : itime
    use timeacc,              only : pseudo_sub
    use inviscid_flux,        only : first_order_iterations

    use fluid,                only : gamma
    use ivals,                only : p0

    use refgeom,              only : sref

    use force_types,          only : force_type
    use forces,               only : cd_id, cdp_id, cl_id,                     &
                                     clp_id, cmx_id, cmxp_id, cmy_id, cmyp_id, &
                                     cmz_id, cmzp_id, clcd_id

    use quadrature_rules,     only : triangle01_nq,    &
                                     triangle01_bary1, &
                                     triangle01_bary2, &
                                     triangle01_bary3, &
                                     triangle01_weight

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design
    type(force_type),  intent(in)    :: force
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(inout) :: dfdq

    integer,  intent(in) :: ibc, node

    real(dp), dimension(3),     intent(in) :: triangle_node1
    real(dp), dimension(3),     intent(in) :: triangle_node2
    real(dp), dimension(3),     intent(in) :: triangle_node3
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    integer :: my_ntt
    real(dp)    :: second, realizable_second

    integer :: ixyz
    real(dp), dimension(3)    :: integration_point

    real(dp), dimension(5)    :: state
    real(dp), dimension(5,design%nfunctions)    :: df_dstate

    integer :: func, comp

    real(dp)    :: dcp5
    real(dp)    :: dx, dy, dz

    real(dp)    :: pi, conv, csa, sna, csy, sny
    real(dp)    :: factorl, factord
    real(dp)    :: cl,clp,cd,cdp
    real(dp)    :: weight, target, power

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: my_1    = 1.0_dp
    real(dp), parameter    :: my_2    = 2.0_dp
    real(dp), parameter    :: my_4    = 4.0_dp
    real(dp), parameter    :: my_180  = 180.0_dp

    integer :: iq

    continue

    if ( node > grid%nnodes0  ) return

    if ( .not. ( (ibc == 3000) .or. (ibc == 3055) .or. &
                 (ibc == 4000) .or. (ibc == 4040) ) ) then
      write(*,*) 'ibc =',ibc,'not implemented in residual_cut:integrate_dfdq'
      stop ! FIXME: should be lmpi_die or se_exit(1)?
    end if

    my_ntt = ntt
    if (itime /= 0) my_ntt = pseudo_sub

    second = my_1
    if (my_ntt <= first_order_iterations) second = zero

    pi = my_4*atan(my_1)
    conv = my_180/pi
    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)
    sny=sin(yaw/conv)

    sum_quadrature_points : do iq = 1, triangle01_nq

      do ixyz = 1, 3
        integration_point(ixyz) = triangle01_bary1(iq)*triangle_node1(ixyz) &
                                + triangle01_bary2(iq)*triangle_node2(ixyz) &
                                + triangle01_bary3(iq)*triangle_node3(ixyz)
      end do

      dx = integration_point(1) - grid%x(node)
      dy = integration_point(2) - grid%y(node)
      dz = integration_point(3) - grid%z(node)

      state = soln%q_dof(:,node)                                &
            + second * soln%phi(:,node) * soln%gradx(:,node)*dx &
            + second * soln%phi(:,node) * soln%grady(:,node)*dy &
            + second * soln%phi(:,node) * soln%gradz(:,node)*dz

      realizable_second = second
      if ( state(1) <= zero .or. state(5) <= zero ) then
        state = soln%q_dof(:,node)
        realizable_second = zero
      end if

      dcp5 = my_2/(p0*gamma*xmach*xmach)

      df_dstate = zero

      func_loop : do func = 1, design%nfunctions

        component_loop : do comp = 1, design%function_data(func)%ncomponents

          if ( design%function_data(func)%component_data(comp)%boundary_id &
               == 0 ) then
            cd   = force%cd
            cdp  = force%cdp
            cl   = force%cl
            clp  = force%clp
          else
            cycle component_loop
          endif

          weight  = design%function_data(func)%component_data(comp)%weight
          target  = design%function_data(func)%component_data(comp)%target
          power   = design%function_data(func)%component_data(comp)%power

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

          factord  = zero
          factorl  = zero

          select case (design%function_data(func)%component_data(comp)%name)
          case ( cd_id )
            factord = weight*power*(cd - target)**(power-1.0_dp)/sref
          case ( cdp_id )
            factord = weight*power*(cdp - target)**(power-1.0_dp)/sref
          case ( cl_id )
            factorl = weight*power*(cl - target)**(power-1.0_dp)/sref
          case ( clp_id )
            factorl = weight*power*(clp - target)**(power-1.0_dp)/sref
          case ( cmx_id, cmxp_id, cmy_id, cmyp_id, cmz_id, cmzp_id, clcd_id )
            write(*,*) "residual_cut:integrate_dfdq implement", &
              design%function_data(func)%component_data(comp)%name
          case default
            cycle component_loop
          end select

          df_dstate(5,func) = df_dstate(5,func)                         &
            + csa*csy*normal(1)*area*triangle01_weight(iq)*dcp5*factord &
            - sny*    normal(2)*area*triangle01_weight(iq)*dcp5*factord &
            + sna*csy*normal(3)*area*triangle01_weight(iq)*dcp5*factord

          df_dstate(5,func) = df_dstate(5,func)                         &
            - sna*normal(1)*area*triangle01_weight(iq)*dcp5*factorl     &
            + csa*normal(3)*area*triangle01_weight(iq)*dcp5*factorl

        end do component_loop
      end do func_loop

      call extrapolated_state_deriv( grid, soln, design, &
        node, dx, dy, dz, realizable_second, df_dstate, dfdq )

    end do sum_quadrature_points

  end subroutine integrate_dfdq

!================================= integrate_dfdq_visc =======================80
!
! integrate dfdq for a boundary triangle (skin friction piece)
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_dfdq_visc(          &
    grid, soln, design, force, dfdq,       &
    ibc,node,                              &
    triangle_node1,triangle_node2,triangle_node3,normal,area )

    use kinddefs,         only : dp
    use grid_types,       only : grid_type
    use solution_types,   only : soln_type
    use design_types,        only : design_type

    use info_depr,        only : alpha, yaw, xmach

    use fluid,            only : gamma
    use bc_names,         only : twall

    use refgeom,          only : sref

    use flux_functions,   only : visc_primitive_jacobian, &
                                 full_visc_primitive_jacobian

    use thermo,           only : dprimitive_dconserved

    use force_types,      only : force_type
    use ivals,            only : u0, v0, w0
    use forces,           only : cd_id, cdv_id, cl_id, clv_id, &
      cmx_id, cmxv_id, cmy_id, cmyv_id, cmz_id, cmzv_id, clcd_id

    use quadrature_rules, only : triangle01_nq,    &
                                 triangle01_bary1, &
                                 triangle01_bary2, &
                                 triangle01_bary3, &
                                 triangle01_weight

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design
    type(force_type),  intent(in)    :: force
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(inout) :: dfdq

    integer,  intent(in) :: ibc, node

    real(dp), dimension(3),     intent(in) :: triangle_node1
    real(dp), dimension(3),     intent(in) :: triangle_node2
    real(dp), dimension(3),     intent(in) :: triangle_node3
    real(dp), dimension(3),     intent(in) :: normal
    real(dp),                   intent(in) :: area

    integer :: ixyz
    real(dp), dimension(3)    :: integration_point

    real(dp), dimension(5)    :: boundary_state

    real(dp), dimension(5,design%nfunctions)    :: df_dstate
    real(dp), dimension(5,design%nfunctions)    :: df_dgradx
    real(dp), dimension(5,design%nfunctions)    :: df_dgrady
    real(dp), dimension(5,design%nfunctions)    :: df_dgradz

    real(dp), dimension(5,5)    :: dQdq

    real(dp), dimension(5,5)    :: df_dstate1
    real(dp), dimension(5,5)    :: df_dgradx1
    real(dp), dimension(5,5)    :: df_dgrady1
    real(dp), dimension(5,5)    :: df_dgradz1
    real(dp), dimension(5,5)    :: df_dstate2
    real(dp), dimension(5,5)    :: df_dgradx2
    real(dp), dimension(5,5)    :: df_dgrady2
    real(dp), dimension(5,5)    :: df_dgradz2

    integer :: func, comp

    real(dp)    :: dx, dy, dz, dot

    real(dp)    :: pi, conv, csa, sna, csy, sny
    real(dp)    :: factorl, factord
    real(dp)    :: cl,clv,cd,cdv
    real(dp)    :: weight, target, power

    real(dp)    :: tm ! two_over_mach_squared

    real(dp), parameter    :: zero    = 0.0_dp
    real(dp), parameter    :: my_1    = 1.0_dp
    real(dp), parameter    :: my_2    = 2.0_dp
    real(dp), parameter    :: my_4    = 4.0_dp
    real(dp), parameter    :: my_180  = 180.0_dp

    integer :: iq
    integer :: i, j, k

    continue

    if ( node > grid%nnodes0  ) return

    if ( .not. ( (ibc == 4000) .or. (ibc == 4040) ) ) then
      write(*,*) 'ibc =',ibc,'not implemented residual_cut:integrate_dfdq_visc'
      stop ! FIXME: should be lmpi_die or se_exit(1)?
    end if

    pi = my_4*atan(my_1)
    conv = my_180/pi
    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)
    sny=sin(yaw/conv)

    tm = my_2 / xmach / xmach

    sum_quadrature_points : do iq = 1, triangle01_nq

      do ixyz = 1, 3
        integration_point(ixyz) = triangle01_bary1(iq)*triangle_node1(ixyz) &
                                + triangle01_bary2(iq)*triangle_node2(ixyz) &
                                + triangle01_bary3(iq)*triangle_node3(ixyz)
      end do

      dx = integration_point(1) - grid%x(node)
      dy = integration_point(2) - grid%y(node)
      dz = integration_point(3) - grid%z(node)
      dot = dx*normal(1) + &
            dy*normal(2) + &
            dz*normal(3)
      dot = abs(dot)
      dx = dot*normal(1)
      dy = dot*normal(2)
      dz = dot*normal(3)

      select case(ibc)
      case (4000) ! on no-slip boundaries (not ghost node)
        boundary_state(1) = gamma/twall*soln%q_dof(5,node)
        boundary_state(2) = zero
        boundary_state(3) = zero
        boundary_state(4) = zero
        boundary_state(5) = soln%q_dof(5,node)
      case (4040) ! on no-slip boundaries (not ghost node)
        boundary_state(1) = gamma/twall*soln%q_dof(5,node)
        boundary_state(2) = u0
        boundary_state(3) = v0
        boundary_state(4) = w0
        boundary_state(5) = soln%q_dof(5,node)
      case default
        write(*,*) 'integrate_dfdq_visc, unknown ibc =',ibc
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end select

      if ( soln%viscous_method == 2 ) then
        call visc_primitive_jacobian(soln%q_dof(:,node),boundary_state, &
                                     soln%amut(node),soln%amut(node),   &
                                     dx,dy,dz,normal,df_dstate1,df_dstate2)
        df_dgradx1 = zero
        df_dgrady1 = zero
        df_dgradz1 = zero
        df_dgradx2 = zero
        df_dgrady2 = zero
        df_dgradz2 = zero
      else
        call full_visc_primitive_jacobian( &
          soln%q_dof(:,node),              &
          soln%gradx(:,node),              &
          soln%grady(:,node),              &
          soln%gradz(:,node),              &
          boundary_state,                  &
          soln%gradx(:,node),              &
          soln%grady(:,node),              &
          soln%gradz(:,node),              &
          soln%amut(node),                 &
          soln%amut(node),                 &
          dx,dy,dz,normal,                 &
          df_dstate1,df_dstate2,           &
          df_dgradx1,df_dgradx2,           &
          df_dgrady1,df_dgrady2,           &
          df_dgradz1,df_dgradz2 )
      end if

      df_dstate = zero
      df_dgradx = zero
      df_dgrady = zero
      df_dgradz = zero

      func_loop : do func = 1, design%nfunctions

        component_loop : do comp = 1, design%function_data(func)%ncomponents

          if ( design%function_data(func)%component_data(comp)%boundary_id &
               == 0 ) then
            cd   = force%cd
            cdv  = force%cdv
            cl   = force%cl
            clv  = force%clv
          else
            cycle component_loop
          endif

          weight  = design%function_data(func)%component_data(comp)%weight
          target  = design%function_data(func)%component_data(comp)%target
          power   = design%function_data(func)%component_data(comp)%power

! Set up the constant factors
! If the component name isn't applicable, cycle the component loop

          factord  = zero
          factorl  = zero

          select case (design%function_data(func)%component_data(comp)%name)
          case ( cd_id )
            factord = weight*power*(cd - target)**(power-1.0_dp)/sref
          case ( cdv_id )
            factord = weight*power*(cdv - target)**(power-1.0_dp)/sref
          case ( cl_id )
            factorl = weight*power*(cl - target)**(power-1.0_dp)/sref
          case ( clv_id )
            factorl = weight*power*(clv - target)**(power-1.0_dp)/sref
          case ( cmx_id, cmxv_id, cmy_id, cmyv_id, cmz_id, cmzv_id, clcd_id )
            write(*,*) "residual_cut:integrate_dfdq_visc implement", &
              design%function_data(func)%component_data(comp)%name
          case default
            cycle component_loop
          end select

          df_dstate(:,func) = df_dstate(:,func)                             &
            + csa*csy*tm*df_dstate1(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dstate1(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dstate1(4,:)*area*triangle01_weight(iq)*factord
          df_dstate(5,func) = df_dstate(5,func) +   gamma/twall*(           &
            + csa*csy*tm*df_dstate2(2,1)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dstate2(3,1)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dstate2(4,1)*area*triangle01_weight(iq)*factord )
          df_dstate(5,func) = df_dstate(5,func) +               (           &
            + csa*csy*tm*df_dstate2(2,5)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dstate2(3,5)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dstate2(4,5)*area*triangle01_weight(iq)*factord )

          df_dgradx(:,func) = df_dgradx(:,func)                             &
            + csa*csy*tm*df_dgradx1(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dgradx1(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dgradx1(4,:)*area*triangle01_weight(iq)*factord &
            + csa*csy*tm*df_dgradx2(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dgradx2(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dgradx2(4,:)*area*triangle01_weight(iq)*factord

          df_dgrady(:,func) = df_dgrady(:,func)                             &
            + csa*csy*tm*df_dgrady1(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dgrady1(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dgrady1(4,:)*area*triangle01_weight(iq)*factord &
            + csa*csy*tm*df_dgrady2(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dgrady2(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dgrady2(4,:)*area*triangle01_weight(iq)*factord

          df_dgradz(:,func) = df_dgradz(:,func)                             &
            + csa*csy*tm*df_dgradz1(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dgradz1(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dgradz1(4,:)*area*triangle01_weight(iq)*factord &
            + csa*csy*tm*df_dgradz2(2,:)*area*triangle01_weight(iq)*factord &
            - sny*    tm*df_dgradz2(3,:)*area*triangle01_weight(iq)*factord &
            + sna*csy*tm*df_dgradz2(4,:)*area*triangle01_weight(iq)*factord

          df_dstate(:,func) = df_dstate(:,func)                             &
            - sna*    tm*df_dstate1(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dstate1(4,:)*area*triangle01_weight(iq)*factorl
          df_dstate(5,func) = df_dstate(5,func) +   gamma/twall*(           &
            - sna*    tm*df_dstate2(2,1)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dstate2(4,1)*area*triangle01_weight(iq)*factorl )
          df_dstate(5,func) = df_dstate(5,func) +   gamma/twall*(           &
            - sna*    tm*df_dstate2(2,5)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dstate2(4,5)*area*triangle01_weight(iq)*factorl )

          df_dgradx(:,func) = df_dgradx(:,func)                             &
            - sna*    tm*df_dgradx1(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dgradx1(4,:)*area*triangle01_weight(iq)*factorl &
            - sna*    tm*df_dgradx2(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dgradx2(4,:)*area*triangle01_weight(iq)*factorl

          df_dgrady(:,func) = df_dgrady(:,func)                             &
            - sna*    tm*df_dgrady1(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dgrady1(4,:)*area*triangle01_weight(iq)*factorl &
            - sna*    tm*df_dgrady2(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dgrady2(4,:)*area*triangle01_weight(iq)*factorl

          df_dgradz(:,func) = df_dgradz(:,func)                             &
            - sna*    tm*df_dgradz1(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dgradz1(4,:)*area*triangle01_weight(iq)*factorl &
            - sna*    tm*df_dgradz2(2,:)*area*triangle01_weight(iq)*factorl &
            + csa*    tm*df_dgradz2(4,:)*area*triangle01_weight(iq)*factorl

        end do component_loop
      end do func_loop

      call dprimitive_dconserved( soln%q_dof(:,node), dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            dfdq(i,node,k) = dfdq(i,node,k) + dQdq(j,i)*df_dstate(j,k)
          end do
        end do
      end do

      call gradient_deriv_cut( grid, soln, design, &
        node, df_dgradx, df_dgrady, df_dgradz,     &
        dfdq )

    end do sum_quadrature_points

  end subroutine integrate_dfdq_visc

!================================= integrate_ldfss_fst =======================80
!
! integrate atlam for a triangle between two duals
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine integrate_ldfss_fst(                 &
    grid, soln, sadj, design, coltag, rlam,       &
    node1,node2,                                  &
    extrapolation1, extrapolation2,               &
    normal, area, face_speed, second,             &
    dr_dgx, dr_dgy, dr_dgz )

    use kinddefs,        only : dp
    use grid_types,      only : grid_type
    use solution_types,  only : soln_type, generic_gas
    use solution_adj,    only : sadj_type
    use design_types,    only : design_type
    use fluid,           only : sutherland_constant, gamma
    use inviscid_flux,   only : flux_construction
    use info_depr,       only : ivisc, tref, xmach, re
    use thermo,          only : dprimitive_dconserved
    use flux_functions,  only : ldfss_primitive_jac_w_ddt
    use generic_gas_map, only : n_amu_k

    type(grid_type),        intent(in   ) :: grid
    type(soln_type),        intent(inout) :: soln
    type(sadj_type),        intent(inout) :: sadj
    type(design_type),      intent(in   ) :: design
    real(dp), dimension(soln%adim,grid%nnodes01),                              &
                            intent(in   ) :: coltag
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),            &
                            intent(in   ) :: rlam
    integer,                intent(in   ) :: node1, node2
    real(dp), dimension(3), intent(in   ) :: extrapolation1
    real(dp), dimension(3), intent(in   ) :: extrapolation2
    real(dp), dimension(3), intent(in   ) :: normal
    real(dp),               intent(in   ) :: area, face_speed
    logical,                intent(in   ) :: second
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                            intent(inout) :: dr_dgx
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                            intent(inout) :: dr_dgy
    real(dp), dimension(5,design%nfunctions,grid%nnodes0), &
                            intent(inout) :: dr_dgz

    logical :: realizable_limit1, realizable_limit2

    real(dp), dimension(5,design%nfunctions)    :: dres_dstate
    real(dp), dimension(5,design%nfunctions)    :: dres_dnode
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradx
    real(dp), dimension(5,design%nfunctions)    :: dres_dgrady
    real(dp), dimension(5,design%nfunctions)    :: dres_dgradz

    integer :: i,j,k

    real(dp), dimension(5)    :: state1
    real(dp), dimension(5)    :: state2

    real(dp), dimension(5)    :: dflux_dgradx1,dflux_dgradx2
    real(dp), dimension(5)    :: dflux_dgrady1,dflux_dgrady2
    real(dp), dimension(5)    :: dflux_dgradz1,dflux_dgradz2
    real(dp), dimension(5)    :: dflux_drho1,dflux_drho2
    real(dp), dimension(5)    :: dflux_dpress1,dflux_dpress2

    real(dp), dimension(5,5)    :: dflux_dstate1,dflux_dstate2

    real(dp), dimension(5,5)    :: dQdq

    real(dp) :: cstar, mu1, mu2, mu, xmr

    real(dp), parameter    :: zero = 0.0_dp
    real(dp), parameter    :: my_half = 0.5_dp

    continue

    cstar = zero
    if (ivisc >= 2) cstar = sutherland_constant/tref

    state1 = soln%q_dof(:,node1)                                    &
      + soln%phi(:,node1) * soln%gradx(:,node1) * extrapolation1(1) &
      + soln%phi(:,node1) * soln%grady(:,node1) * extrapolation1(2) &
      + soln%phi(:,node1) * soln%gradz(:,node1) * extrapolation1(3)

    realizable_limit1 = .false.
    if ( state1(1) <= zero .or. state1(5) <= zero ) then
      state1 = soln%q_dof(:,node1)
      realizable_limit1 = .true.
    end if

    state2 = soln%q_dof(:,node2)                                    &
      + soln%phi(:,node2) * soln%gradx(:,node2) * extrapolation2(1) &
      + soln%phi(:,node2) * soln%grady(:,node2) * extrapolation2(2) &
      + soln%phi(:,node2) * soln%gradz(:,node2) * extrapolation2(3)

    realizable_limit2 = .false.
    if ( state2(1) <= zero .or. state2(5) <= zero ) then
      state2 = soln%q_dof(:,node2)
      realizable_limit2 = .true.
    end if

    select case (flux_construction)
    case ('ldfss','dldfss')
      if ( soln%eqn_set == generic_gas ) then
        if(ivisc >= 2)then
          mu1 = soln%q_dof(n_amu_k(1),node1)
          mu2 = soln%q_dof(n_amu_k(1),node2)
        else
          mu1 = zero
          mu2 = zero
        end if
      else
        if(ivisc >= 2)then
          xmr = xmach/re
          mu1 = viscosity_law(cstar,gamma*soln%q_dof(5,node1)/                 &
                soln%q_dof(1,node1))*xmr
          mu2 = viscosity_law(cstar,gamma*soln%q_dof(5,node2)/                 &
                soln%q_dof(1,node2))*xmr
        else
          mu1 = zero
          mu2 = zero
        end if
      end if
      mu = my_half*(mu1 + mu2)
      call ldfss_primitive_jac_w_ddt(                                          &
      extrapolation1(1), extrapolation1(2), extrapolation1(3),                 &
      extrapolation2(1), extrapolation2(2), extrapolation2(3),                 &
      normal(1), normal(2), normal(3), area, grid%vol(node1), grid%vol(node2), &
      soln%gradx(5,node1),soln%grady(5,node1),soln%gradz(5,node1),             &
      soln%phi(5,node1),                                                       &
      soln%gradx(5,node2),soln%grady(5,node2),soln%gradz(5,node2),             &
      soln%phi(5,node2),                                                       &
      face_speed,mu,                                                           &
      state1, state2, second, .true.,                                          &
      .true., .true., .true.,                                                  &
      dflux_dgradx1, dflux_dgrady1, dflux_dgradz1, dflux_drho1, dflux_dpress1, &
      dflux_dgradx2, dflux_dgrady2, dflux_dgradz2, dflux_drho2, dflux_dpress2, &
      dflux_dstate1, dflux_dstate2 )
    case default
      dflux_dstate1 = zero
      dflux_dstate2 = zero
    end select

! call the stuff that depends on the state1
    dres_dstate = zero
    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          dres_dstate(j,k) = dres_dstate(j,k) + (                     &
            + coltag(i,node1) * rlam(i,node1,k) * dflux_dstate1(i,j)  &
            - coltag(i,node2) * rlam(i,node2,k) * dflux_dstate1(i,j) )
        end do
      end do
    end do
    dres_dnode = zero
    j = 1
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dnode(j,k) = dres_dnode(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_drho1(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_drho1(i) )
      end do
    end do
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dnode(j,k) = dres_dnode(j,k) + (                     &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dpress1(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dpress1(i) )
      end do
    end do
    dres_dgradx = 0
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dgradx(j,k) = dres_dgradx(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dgradx1(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dgradx1(i) )
      end do
    end do
    dres_dgrady = 0
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dgrady(j,k) = dres_dgrady(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dgrady1(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dgrady1(i) )
      end do
    end do
    dres_dgradz = 0
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dgradz(j,k) = dres_dgradz(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dgradz1(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dgradz1(i) )
      end do
    end do

    local_node1_cc : if ( node1 <= grid%nnodes0  ) then
      call dprimitive_dconserved( soln%q_dof(:,node1), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node1,k) = sadj%res(i,node1,k) &
              + dQdq(j,i)*dres_dstate(j,k) + dQdq(j,i)*dres_dnode(j,k)
          end do
        end do
      end do
    end if local_node1_cc

    no_feas_limit1 : if ( .not. realizable_limit1 .and. second ) then
      local_node1_grad : if ( node1 <= grid%nnodes0  ) then
        do j = 1, 5
          do k = 1, design%nfunctions
            dr_dgx(j,k,node1) = dr_dgx(j,k,node1)                        &
              + soln%phi(j,node1) * extrapolation1(1) * dres_dstate(j,k)
            dr_dgy(j,k,node1) = dr_dgy(j,k,node1)                        &
              + soln%phi(j,node1) * extrapolation1(2) * dres_dstate(j,k)
            dr_dgz(j,k,node1) = dr_dgz(j,k,node1)                        &
              + soln%phi(j,node1) * extrapolation1(3) * dres_dstate(j,k)
          end do
        end do
      end if local_node1_grad
    end if no_feas_limit1

    local_node1_grad_press : if ( node1 <= grid%nnodes0  ) then
      do j = 1, 5
        do k = 1, design%nfunctions
          dr_dgx(j,k,node1) = dr_dgx(j,k,node1)                        &
            + dres_dgradx(j,k)
          dr_dgy(j,k,node1) = dr_dgy(j,k,node1)                        &
            + dres_dgrady(j,k)
          dr_dgz(j,k,node1) = dr_dgz(j,k,node1)                        &
            + dres_dgradz(j,k)
        end do
      end do
    end if local_node1_grad_press

! call the stuff that depends on the state2
    dres_dstate = zero
    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          dres_dstate(j,k) = dres_dstate(j,k) + (                     &
            + coltag(i,node1) * rlam(i,node1,k) * dflux_dstate2(i,j)  &
            - coltag(i,node2) * rlam(i,node2,k) * dflux_dstate2(i,j) )
        end do
      end do
    end do
    dres_dnode = zero
    j = 1
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dnode(j,k) = dres_dnode(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_drho2(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_drho2(i) )
      end do
    end do
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dnode(j,k) = dres_dnode(j,k) + (                     &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dpress2(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dpress2(i) )
      end do
    end do
    dres_dgradx = 0
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dgradx(j,k) = dres_dgradx(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dgradx2(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dgradx2(i) )
      end do
    end do
    dres_dgrady = 0
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dgrady(j,k) = dres_dgrady(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dgrady2(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dgrady2(i) )
      end do
    end do
    dres_dgradz = 0
    j = 5
    do i = 1, 5
      do k = 1, design%nfunctions
        dres_dgradz(j,k) = dres_dgradz(j,k) + (                   &
          + coltag(i,node1) * rlam(i,node1,k) * dflux_dgradz2(i)  &
          - coltag(i,node2) * rlam(i,node2,k) * dflux_dgradz2(i) )
      end do
    end do

    local_node2_cc : if ( node2 <= grid%nnodes0  ) then
      call dprimitive_dconserved( soln%q_dof(:,node2), dQdq )
      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            sadj%res(i,node2,k) = sadj%res(i,node2,k) &
              + dQdq(j,i)*dres_dstate(j,k) + dQdq(j,i)*dres_dnode(j,k)
          end do
        end do
      end do
    end if local_node2_cc

    no_feas_limit2 : if ( .not. realizable_limit2  .and. second ) then
      local_node2_grad : if ( node2 <= grid%nnodes0  ) then
        do j = 1, 5
          do k = 1, design%nfunctions
            dr_dgx(j,k,node2) = dr_dgx(j,k,node2)                        &
              + soln%phi(j,node2) * extrapolation2(1) * dres_dstate(j,k)
            dr_dgy(j,k,node2) = dr_dgy(j,k,node2)                        &
              + soln%phi(j,node2) * extrapolation2(2) * dres_dstate(j,k)
            dr_dgz(j,k,node2) = dr_dgz(j,k,node2)                        &
              + soln%phi(j,node2) * extrapolation2(3) * dres_dstate(j,k)
          end do
        end do
      end if local_node2_grad
    end if no_feas_limit2

    local_node2_grad_press : if ( node2 <= grid%nnodes0  ) then
      do j = 1, 5
        do k = 1, design%nfunctions
          dr_dgx(j,k,node2) = dr_dgx(j,k,node2)                        &
            + dres_dgradx(j,k)
          dr_dgy(j,k,node2) = dr_dgy(j,k,node2)                        &
            + dres_dgrady(j,k)
          dr_dgz(j,k,node2) = dr_dgz(j,k,node2)                        &
            + dres_dgradz(j,k)
        end do
      end do
    end if local_node2_grad_press

  end subroutine integrate_ldfss_fst

!================================= extrapolated_state_deriv ==================80
!
! compute second order adjoint residual pieces for reconstructed state
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine extrapolated_state_deriv( grid, soln, design, &
    extrapolation_node, dx, dy, dz, second, dres_dstate, res )

    use kinddefs,           only : dp
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use design_types,        only : design_type

    use info_depr,        only : wls_inv_terms
    use thermo,           only : dprimitive_dconserved

    use cut_types, only : cut, required_lstgs_degree

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design
    integer, intent(in) :: extrapolation_node
    real(dp),    intent(in) :: dx,dy,dz
    real(dp),    intent(in)  :: second
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dstate
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(inout) :: res

    real(dp), dimension(5,5)    :: other_dQdq, extrapolation_dQdq

    integer :: degree
    integer :: i,j,k
    integer :: comp_row_index, other_node
    real(dp)    :: dx1,dy1,dz1
    real(dp)    :: w11, w22, w33
    real(dp)    :: r12r11, r13r11, r23r22, rmess
    real(dp)    :: coef1, coef2
    real(dp)    :: termx, termy, termz, weightx, weighty, weightz
    real(dp), dimension(5)    :: weight

    real(dp), parameter    :: half    = 0.5_dp
    real(dp), parameter    :: my_1    = 1.0_dp

    continue

    if ( extrapolation_node > grid%nnodes0 ) return

! node contribution

    call dprimitive_dconserved( soln%q_dof(:,extrapolation_node), &
                                extrapolation_dQdq )

    do i = 1, 5
      do j = 1, 5
        do k = 1, design%nfunctions
          res(i,extrapolation_node,k) = res(i,extrapolation_node,k) + &
            extrapolation_dQdq(j,i)*dres_dstate(j,k)
        end do
      end do
    end do

    degree = cut%firstn2n(extrapolation_node+1) - &
             cut%firstn2n(extrapolation_node)
    if ( degree < required_lstgs_degree ) return
    if ( second < half ) return

    grad_loop : do comp_row_index = cut%firstn2n(extrapolation_node),          &
                                    cut%firstn2n(extrapolation_node+1) - 1

      other_node = cut%n2n(comp_row_index)

      if( other_node == extrapolation_node ) cycle grad_loop ! skip diagonal

      dx1 = grid%x(other_node) - grid%x(extrapolation_node)
      dy1 = grid%y(other_node) - grid%y(extrapolation_node)
      dz1 = grid%z(other_node) - grid%z(extrapolation_node)

      if ( wls_inv_terms ) then
        dx1 = sqrt(cut%vol(other_node) / cut%vol(extrapolation_node)) * dx1
        dy1 = sqrt(cut%vol(other_node) / cut%vol(extrapolation_node)) * dy1
        dz1 = sqrt(cut%vol(other_node) / cut%vol(extrapolation_node)) * dz1
      end if

      w11 = my_1/(grid%r11(extrapolation_node)*grid%r11(extrapolation_node))
      w22 = my_1/(grid%r22(extrapolation_node)*grid%r22(extrapolation_node))
      w33 = my_1/(grid%r33(extrapolation_node)*grid%r33(extrapolation_node))
      r12r11 = grid%r12(extrapolation_node)/grid%r11(extrapolation_node)
      r13r11 = grid%r13(extrapolation_node)/grid%r11(extrapolation_node)
      r23r22 = grid%r23(extrapolation_node)/grid%r22(extrapolation_node)
      rmess  = (grid%r12(extrapolation_node)*grid%r23(extrapolation_node) &
             - grid%r13(extrapolation_node)*grid%r22(extrapolation_node)) &
             / (grid%r11(extrapolation_node)*grid%r22(extrapolation_node) &
             * grid%r33(extrapolation_node)*grid%r33(extrapolation_node))
      coef1  = dy1 - dx1*r12r11
      coef2  = dz1 - dx1*r13r11 - r23r22*coef1
      termx = dx1*w11 - w22*r12r11*coef1 + rmess*coef2
      termy = w22*coef1 - r23r22*w33*coef2
      termz = w33*coef2
      if ( wls_inv_terms ) then
        weightx = sqrt(cut%vol(other_node)/cut%vol(extrapolation_node)) * termx
        weighty = sqrt(cut%vol(other_node)/cut%vol(extrapolation_node)) * termy
        weightz = sqrt(cut%vol(other_node)/cut%vol(extrapolation_node)) * termz
      else
        weightx = termx
        weighty = termy
        weightz = termz
      end if

      weight(1) = second * soln%phi(1,extrapolation_node) * &
                  (weightx*dx+weighty*dy+weightz*dz)
      weight(2) = second * soln%phi(2,extrapolation_node) * &
                  (weightx*dx+weighty*dy+weightz*dz)
      weight(3) = second * soln%phi(3,extrapolation_node) * &
                  (weightx*dx+weighty*dy+weightz*dz)
      weight(4) = second * soln%phi(4,extrapolation_node) * &
                  (weightx*dx+weighty*dy+weightz*dz)
      weight(5) = second * soln%phi(5,extrapolation_node) * &
                  (weightx*dx+weighty*dy+weightz*dz)

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            res(i,extrapolation_node,k) = res(i,extrapolation_node,k)  &
              - weight(j)*extrapolation_dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,other_node), &
                                  other_dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            res(i,other_node,k) = res(i,other_node,k)  &
              + weight(j)*other_dQdq(j,i)*dres_dstate(j,k)
          end do
        end do
      end do

    end do grad_loop

  end subroutine extrapolated_state_deriv

!================================= gradient_deriv_cut ========================80
!
! compute second order adjoint residual pieces for gradx, grady, gradz
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine gradient_deriv_cut( grid, soln, design, &
    extrapolation_node, dres_dgradx, dres_dgrady, dres_dgradz, res )

    use kinddefs,           only : dp
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use design_types,        only : design_type

    use info_depr,        only : wls_inv_terms
    use thermo,           only : dprimitive_dconserved

    use cut_types, only : cut, required_lstgs_degree

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design
    integer, intent(in) :: extrapolation_node
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dgradx
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dgrady
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dgradz
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(inout) :: res

    real(dp), dimension(5,5)    :: other_dQdq, extrapolation_dQdq

    integer :: degree
    integer :: i,j,k
    integer :: comp_row_index, other_node
    real(dp)    :: dx1,dy1,dz1
    real(dp)    :: w11, w22, w33
    real(dp)    :: r12r11, r13r11, r23r22, rmess
    real(dp)    :: coef1, coef2
    real(dp)    :: termx, termy, termz, weightx, weighty, weightz

    real(dp), parameter    :: my_1    = 1.0_dp

    continue

    if ( extrapolation_node > grid%nnodes0 ) return

! node contribution

    call dprimitive_dconserved( soln%q_dof(:,extrapolation_node), &
                                extrapolation_dQdq )

    degree = cut%firstn2n(extrapolation_node+1) - &
             cut%firstn2n(extrapolation_node)
    if ( degree < required_lstgs_degree ) return

    grad_loop : do comp_row_index = cut%firstn2n(extrapolation_node),          &
                                    cut%firstn2n(extrapolation_node+1) - 1

      other_node = cut%n2n(comp_row_index)

      if( other_node == extrapolation_node ) cycle grad_loop ! skip diagonal

      dx1 = grid%x(other_node) - grid%x(extrapolation_node)
      dy1 = grid%y(other_node) - grid%y(extrapolation_node)
      dz1 = grid%z(other_node) - grid%z(extrapolation_node)

      if ( wls_inv_terms ) then
        dx1 = sqrt(cut%vol(other_node) / cut%vol(extrapolation_node)) * dx1
        dy1 = sqrt(cut%vol(other_node) / cut%vol(extrapolation_node)) * dy1
        dz1 = sqrt(cut%vol(other_node) / cut%vol(extrapolation_node)) * dz1
      end if

      w11 = my_1/(grid%r11(extrapolation_node)*grid%r11(extrapolation_node))
      w22 = my_1/(grid%r22(extrapolation_node)*grid%r22(extrapolation_node))
      w33 = my_1/(grid%r33(extrapolation_node)*grid%r33(extrapolation_node))
      r12r11 = grid%r12(extrapolation_node)/grid%r11(extrapolation_node)
      r13r11 = grid%r13(extrapolation_node)/grid%r11(extrapolation_node)
      r23r22 = grid%r23(extrapolation_node)/grid%r22(extrapolation_node)
      rmess  = (grid%r12(extrapolation_node)*grid%r23(extrapolation_node) &
             - grid%r13(extrapolation_node)*grid%r22(extrapolation_node)) &
             / (grid%r11(extrapolation_node)*grid%r22(extrapolation_node) &
             * grid%r33(extrapolation_node)*grid%r33(extrapolation_node))
      coef1  = dy1 - dx1*r12r11
      coef2  = dz1 - dx1*r13r11 - r23r22*coef1
      termx = dx1*w11 - w22*r12r11*coef1 + rmess*coef2
      termy = w22*coef1 - r23r22*w33*coef2
      termz = w33*coef2
      if ( wls_inv_terms ) then
        weightx = sqrt(cut%vol(other_node)/cut%vol(extrapolation_node)) * termx
        weighty = sqrt(cut%vol(other_node)/cut%vol(extrapolation_node)) * termy
        weightz = sqrt(cut%vol(other_node)/cut%vol(extrapolation_node)) * termz
      else
        weightx = termx
        weighty = termy
        weightz = termz
      end if

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            res(i,extrapolation_node,k) = res(i,extrapolation_node,k)  &
              - weightx*extrapolation_dQdq(j,i)*dres_dgradx(j,k)       &
              - weighty*extrapolation_dQdq(j,i)*dres_dgrady(j,k)       &
              - weightz*extrapolation_dQdq(j,i)*dres_dgradz(j,k)
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,other_node), &
                                  other_dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            res(i,other_node,k) = res(i,other_node,k)    &
              + weightx*other_dQdq(j,i)*dres_dgradx(j,k) &
              + weighty*other_dQdq(j,i)*dres_dgrady(j,k) &
              + weightz*other_dQdq(j,i)*dres_dgradz(j,k)
          end do
        end do
      end do

    end do grad_loop

  end subroutine gradient_deriv_cut

!================================= gradient_deriv_cut ========================80
!
! compute second order adjoint residual pieces for gradx, grady, gradz
! soln%q_dof expected in primitive
!
!=============================================================================80
  subroutine gradient_deriv( grid, soln, design, crow, &
    extrapolation_node, dres_dgradx, dres_dgrady, dres_dgradz, res )

    use kinddefs,           only : dp
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use design_types,       only : design_type
    use comprow_types,      only : crow_type

    use thermo,           only : dprimitive_dconserved

    type(grid_type),   intent(in)    :: grid
    type(soln_type),   intent(inout) :: soln
    type(design_type), intent(in)    :: design
    type(crow_type),   intent(in)    :: crow
    integer, intent(in) :: extrapolation_node
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dgradx
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dgrady
    real(dp), dimension(5,design%nfunctions),    intent(in) :: dres_dgradz
    real(dp), dimension(soln%adim,grid%nnodes01,design%nfunctions),    &
      intent(inout) :: res

    real(dp), dimension(5,5)    :: other_dQdq, extrapolation_dQdq

    integer :: i,j,k
    integer :: comp_row_index, other_node
    real(dp)    :: dx1,dy1,dz1
    real(dp)    :: w11, w22, w33
    real(dp)    :: r12r11, r13r11, r23r22, rmess
    real(dp)    :: coef1, coef2
    real(dp)    :: weightx, weighty, weightz

    real(dp), parameter    :: my_1    = 1.0_dp

    continue

    if ( extrapolation_node > grid%nnodes0 ) return

! node contribution

    call dprimitive_dconserved( soln%q_dof(:,extrapolation_node), &
                                extrapolation_dQdq )

    grad_loop : do comp_row_index = crow%ia(extrapolation_node),          &
                                    crow%ia(extrapolation_node+1) - 1

      other_node = crow%ja(comp_row_index)

      if( other_node == extrapolation_node ) cycle grad_loop ! skip diagonal

      dx1 = grid%x(other_node) - grid%x(extrapolation_node)
      dy1 = grid%y(other_node) - grid%y(extrapolation_node)
      dz1 = grid%z(other_node) - grid%z(extrapolation_node)

      w11 = my_1/(grid%r11(extrapolation_node)*grid%r11(extrapolation_node))
      w22 = my_1/(grid%r22(extrapolation_node)*grid%r22(extrapolation_node))
      w33 = my_1/(grid%r33(extrapolation_node)*grid%r33(extrapolation_node))
      r12r11 = grid%r12(extrapolation_node)/grid%r11(extrapolation_node)
      r13r11 = grid%r13(extrapolation_node)/grid%r11(extrapolation_node)
      r23r22 = grid%r23(extrapolation_node)/grid%r22(extrapolation_node)
      rmess  = (grid%r12(extrapolation_node)*grid%r23(extrapolation_node) &
             - grid%r13(extrapolation_node)*grid%r22(extrapolation_node)) &
             / (grid%r11(extrapolation_node)*grid%r22(extrapolation_node) &
             * grid%r33(extrapolation_node)*grid%r33(extrapolation_node))
      coef1  = dy1 - dx1*r12r11
      coef2  = dz1 - dx1*r13r11 - r23r22*coef1
      weightx = dx1*w11 - w22*r12r11*coef1 + rmess*coef2
      weighty = w22*coef1 - r23r22*w33*coef2
      weightz = w33*coef2

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            res(i,extrapolation_node,k) = res(i,extrapolation_node,k)  &
              - weightx*extrapolation_dQdq(j,i)*dres_dgradx(j,k)       &
              - weighty*extrapolation_dQdq(j,i)*dres_dgrady(j,k)       &
              - weightz*extrapolation_dQdq(j,i)*dres_dgradz(j,k)
          end do
        end do
      end do

      call dprimitive_dconserved( soln%q_dof(:,other_node), &
                                  other_dQdq )

      do i = 1, 5
        do j = 1, 5
          do k = 1, design%nfunctions
            res(i,other_node,k) = res(i,other_node,k)    &
              + weightx*other_dQdq(j,i)*dres_dgradx(j,k) &
              + weighty*other_dQdq(j,i)*dres_dgrady(j,k) &
              + weightz*other_dQdq(j,i)*dres_dgradz(j,k)
          end do
        end do
      end do

    end do grad_loop

  end subroutine gradient_deriv

  include 'in_conserved_variables.f90'
  include 'viscosity_law.f90'

end module residual_cut

module composite_jacobian_util

  use kinddefs,          only : dp, odp
  use exact_defs,        only : ic_exact
  use info_depr,         only : complex_mode
  use solution_types,    only : soln_type
  use comprow_types,     only : crow_flow
  use grid_types,        only : grid_type, mass_type
  use lmpi,              only : lmpi_master, lmpi_conditional_stop,            &
                                lmpi_id, lmpi_nproc, lmpi_reduce, lmpi_bcast,  &
                                lmpi_max_and_maxid

  implicit none

  private

  public :: set_likely_flags
  public :: reorder_lhs_m2g
  public :: header_composite_jacobian, header_set, gfile1_header
  public :: check_crow_entries
  public :: get_cmp_residual
  public :: write_gold_dof_info
  public :: frechet_setup
  public :: check_frechet
  public :: corrupt_jacobian, complex_epsilon, real_epsilon

  real(dp) :: delta_q

! beginNeverComplex

  real(dp) :: warn_diag,     warn_off
  real(dp) :: warn_diag_rel, warn_off_rel

  real(dp), parameter :: complex_epsilon = 1.e-14_dp ! complex step

  ! finite diff. step cannot be too small
  real(dp), parameter :: real_epsilon    = 1.e-8_dp

  ! warning message issued if |error| > my_warn_factor * step size
  real(dp), parameter :: my_warn_factor  = 10.0_dp

  ! warning message issued if |relative-error| > rel_factor * step size
  real(dp), parameter :: rel_factor  = 100.0_dp

! endNeverComplex

  real(dp), parameter :: zero = 0.0_dp

! beginNeverComplex
  real(dp) :: comparator, err_max, rel_max, err_at_rel_max, max_abs_err
! endNeverComplex

  logical :: likely_adiag_errors = .false.
  logical :: likely_aoff_errors  = .false.
  logical :: likely_errors       = .false.

  integer :: diag_likely_n, diag_likely_proc, diag_likely_dof
  integer :: aoff_likely_n, aoff_likely_proc, aoff_likely_dof, aoff_likely_off

  integer :: diag_likely_i, diag_likely_j
  integer :: aoff_likely_i, aoff_likely_j

  real(dp) :: diag_likely_max, aoff_likely_max
  real(dp) :: diag_likely_abs, aoff_likely_abs

  contains

!==================================== SET_LIKELY_FLAGS =======================80
!
! Sets likely error flags
!
!=============================================================================80

  subroutine set_likely_flags( )

    continue

    likely_adiag_errors = .false.
    likely_aoff_errors  = .false.

    diag_likely_n    = 0
    diag_likely_proc = 0
    diag_likely_dof  = 0
    diag_likely_i    = 0
    diag_likely_j    = 0

    aoff_likely_n    = 0
    aoff_likely_proc = 0
    aoff_likely_dof  = 0 ; aoff_likely_off = 0
    aoff_likely_i    = 0
    aoff_likely_j    = 0

    diag_likely_max  = -huge(1._dp)
    aoff_likely_max  = -huge(1._dp)
    diag_likely_abs  = -huge(1._dp)
    aoff_likely_abs  = -huge(1._dp)

  end subroutine set_likely_flags

!=================================  CHECK_LIKELY =============================80
!
! Check likely errors.
!
!=============================================================================80

  subroutine check_likely( dof, off, njac, analytic, frechet, error,           &
                           likely_n, likely_proc, likely_dof, likely_off,      &
                           likely_i, likely_j,                                 &
                           likely_max, likely_abs, tolerance )

    integer,  intent(in)    :: dof, off, njac
    integer,  intent(inout) :: likely_n, likely_proc, likely_dof, likely_off,  &
                               likely_i, likely_j
    real(dp), intent(inout) :: likely_max, likely_abs
! beginNeverComplex
    real(dp), intent(in)    :: tolerance
! endNeverComplex

    real(dp), dimension(njac,njac), intent(in) :: analytic, frechet, error

    integer  :: i, j

    logical :: hit

    real(dp) :: rel_max

    logical :: first_time

  continue

    first_time = .true.

    max_abs_err = -huge(1._dp)
    do i=1,njac
      do j=1,njac
        err_max     = abs( real(error(i,j),dp) )
        max_abs_err = max( max_abs_err , err_max )
      enddo
    end do

    do i=1,njac
      do j=1,njac

        hit = .false.

        rel_max = -huge(1._dp)

        err_max = abs( real(error(i,j),dp) )

        if ( err_max <= 0.90_dp*max_abs_err ) cycle

        if ( err_max < rel_factor*tolerance ) cycle

        comparator = abs ( real(analytic(i,j),dp) )

        comparator = max( comparator, abs ( real(frechet(i,j),dp) ) )

        if ( comparator > rel_factor*tolerance ) then
          if ( err_max  > rel_factor*tolerance*comparator ) then
            rel_max = max ( rel_max, err_max/comparator )
            hit = .true.
          endif
        endif

        if ( .not. hit ) cycle

        if ( first_time ) likely_n    = likely_n + 1

        first_time = .false.

        if ( rel_max  > likely_max ) then
          likely_max  = rel_max
          likely_abs  = err_max
          likely_proc = lmpi_id
          likely_dof  = dof
          likely_off  = off
          likely_i    = i
          likely_j    = j
        endif

      enddo
    end do

  end subroutine check_likely

!==================================== SET_TOL ================================80
!
! Sets tolerances and solution perturbation increment
!
!=============================================================================80

  subroutine set_tol( )

    real(dp) :: my_warn_diag, my_warn_off

    continue

    if (complex_mode) then
      delta_q = cmplx(0.0_dp,real(complex_epsilon,dp),dp)
      my_warn_diag  = complex_epsilon*my_warn_factor
      my_warn_off   = my_warn_diag
      if (odp /= dp) my_warn_off   = real_epsilon*my_warn_factor
    else
      delta_q = real_epsilon
      my_warn_diag  = real_epsilon*my_warn_factor
      my_warn_off   = my_warn_diag
    end if

    warn_diag = real( my_warn_diag, dp )
    warn_off  = real( my_warn_off,  dp )

    warn_diag_rel = warn_diag*rel_factor
    warn_off_rel  = warn_off *rel_factor

  end subroutine set_tol

!==================================== INFO_DIAG ==============================80
!
! Writes out the detailed comparison between the analytic and finite difference
! (or complex) diagonal jacobians at a point
!
!=============================================================================80

  subroutine info_diag(f, string, n, dof0, njac, d_lhs, d_rhs,                 &
                       error_d, max_error_d, an_ref_diag, fd_ref_diag )

    use info_depr,       only : cc_primal

    character(len=80), intent(in) :: string

    integer, intent(in)    :: f, n, dof0, njac

    real(dp), dimension(njac,njac,dof0), intent(in) :: d_lhs
    real(dp), dimension(njac,njac,dof0), intent(in) :: d_rhs
    real(dp), dimension(njac,njac),      intent(in) :: error_d

    real(dp), intent(in) :: max_error_d
    real(dp), intent(in) :: an_ref_diag
    real(dp), intent(in) :: fd_ref_diag

    integer  :: i, j, i_rel, j_rel

    real(dp) :: tolerance

    logical  :: print_derivative

    continue

    print_derivative = .true.

    tolerance = 100._dp*real_epsilon
    if ( complex_mode ) tolerance = 100._dp*complex_epsilon

    write(f,*)
    if ( cc_primal ) then
      write(f,'(2x,3a,i6,a)')                                                  &
         "*** ",trim(string)," Jacobians For Cell ",n," ***"
    else
      write(f,'(2x,3a,i6,a)')                                                  &
         "*** ",trim(string)," Jacobians For Node ",n," ***"
    endif
    write(f,'(2x,2(a,i6))') '    lmpi_id=',lmpi_id,' dof0=',dof0
    write(f,*)

    write(f,'(2x,a)')"Analytic"
    call write_values( f, d_lhs(:,:,n), 0 )

    if (complex_mode) then
      write(f,'(2x,a)')"Frechet Derivative Using Complex Variable Delta"
    else
      write(f,'(2x,a)')"Frechet Derivative Using Real Variable Delta"
    end if
    call write_values( f, d_rhs(:,:,n), 0 )

    write(f,'(2x,a)')"Error"
    call write_values( f, error_d, 0 )

    write(f,*)
    !...check for the occurrence of likely errors.
    likely_errors = .false.
    do i=1,njac
      do j=1,njac
        err_max = abs( real(error_d(i,j),dp) )
        if ( err_max <= 0.9999_dp*abs(max_error_d) ) cycle
        if ( abs(max_error_d) < rel_factor*warn_diag ) cycle
        comparator = abs ( real(d_rhs(i,j,n),dp) )  !FD/CD
        if ( (comparator > rel_factor*warn_diag ) .and.          &
             (err_max    > rel_factor*warn_diag*comparator) ) then
          likely_errors = .true.
          rel_max = real(error_d(i,j))/comparator
          err_at_rel_max = err_max
          i_rel          = i
          j_rel          = j
        endif
      enddo
    end do
    write(f,*)
    if ( likely_errors ) then
      write(f,*)
      write(f,'(2x,a,e13.5,a)')                                         &
                              "  --> Max Rel. Error   = ",              &
                                real(rel_max),                          &
                                "    WARNING: LIKELY ERROR IN A_DIAG !!!"
      write(f,'(2x,a,e13.5,a)') &
                              "  -->  At Abs. Error   = ",real(err_at_rel_max)
      write(f,'(2x,a,2i4)')   &
                              "  -->  At (i,j)        = ",i_rel,j_rel
      write(f,*)
      write(f,'(2x,a,e13.5)') "  --> Max Abs. Error   = ",real(max_error_d,dp)
      likely_adiag_errors = .true. !turn on global indicator
    elseif ( abs(max_error_d) > warn_diag ) then
      write(f,*)
      write(f,'(2x,a,e13.5,a)')                                          &
                              "  --> Max Error (diag) = ",               &
                              real(max_error_d),                         &
                              "    WARNING: POSSIBLE ERROR IN A_DIAG !!!"
    else !Supress information if below tolerance
      if ( abs( real( max_error_d,dp) ) > &
           abs( real(   tolerance,dp) )   ) then
        write(f,'(2x,a,e13.5)') "  --> Max Error (diag) = ",real(max_error_d)
      else
        write(f,'(2x,a,e13.5)') "  --> Max Error (diag) <     tolerance"
        print_derivative = .false.
      endif
    end if
    if ( print_derivative ) then
      write(f,'(2x,a,e13.5)') "              Analytic = ",real(an_ref_diag)
      write(f,'(2x,a,e13.5)') "               Frechet = ",real(fd_ref_diag)
    endif

  end subroutine info_diag


!===================================== INFO_OFF ==============================80
!
! Writes out the detailed comparison between the analytic and finite difference
! (or complex) off-diagonal jacobians at a point
!
!=============================================================================80

  subroutine info_off( f, string, n, dof0, nnz0, njac, a_lhs, a_rhs,           &
                       error_a_rhs, max_error_a_rhs, an_ref_off, fd_ref_off,   &
                       max_off_diag, je_list, dof_list, off_index,             &
                       off_index_simple )

    use info_depr,       only : cc_primal

    character(len=80), intent(in) :: string

    integer, intent(in)    :: f, n, dof0, nnz0, njac, max_off_diag
    integer, intent(in)    :: off_index, off_index_simple

    integer, dimension(max_off_diag), intent(in) :: je_list, dof_list

    real(dp), dimension(njac,njac,nnz0),         intent(in) :: a_lhs
    real(dp), dimension(njac,njac,nnz0),         intent(in) :: a_rhs
    real(dp), dimension(njac,njac,max_off_diag), intent(in) :: error_a_rhs

    real(dp), dimension(max_off_diag), intent(in) :: max_error_a_rhs
    real(dp), dimension(max_off_diag), intent(in) :: an_ref_off
    real(dp), dimension(max_off_diag), intent(in) :: fd_ref_off

    integer  :: i, j, k, ioff, i_rel, j_rel

    real(dp) :: tolerance, limit

    logical  :: print_derivative

    continue

    tolerance = 100._dp*real_epsilon
    if ( complex_mode ) tolerance = 100._dp*complex_epsilon
    limit     = epsilon(1._odp)
    tolerance = max( tolerance, limit )

    write(f,*)
    do k = 1, off_index
      ioff = je_list(k)
      write(f,*)
      if ( dof_list(k) <= dof0 ) then
      if ( cc_primal ) then
        write(f,'(2x,3a,i6,a,i6,a)')                                           &
           "*** ",trim(string)," Jacobians For Cell ",n,                       &
           "   Due to on-processor cell ",dof_list(k)," ***"
      else
        write(f,'(2x,3a,i6,a,i6,a)')                                           &
           "*** ",trim(string)," Jacobians For Node ",n,                       &
           "   Due to on-processor node ",dof_list(k)," ***"
      endif
      else
      if ( cc_primal ) then
        write(f,'(2x,3a,i6,a,i6,a)')                                           &
           "*** ",trim(string)," Jacobians For Cell ",n,                       &
           "   Due to off-processor cell ",dof_list(k)," ***"
      else
        write(f,'(2x,3a,i6,a,i6,a)')                                           &
           "*** ",trim(string)," Jacobians For Node ",n,                       &
           "   Due to off-processor node ",dof_list(k)," ***"
      endif

      endif
      if ( k > off_index_simple ) then
        write(f,*)
        if ( cc_primal ) then
          write(f,'(2x,a)')"Non-Simply Connected Cell"
        else
          write(f,'(2x,a)')"Non-Simply Connected Node"
        endif
      else
        if ( cc_primal ) then
          write(f,'(2x,a)')"Simply (Face) Connected Cell"
        else
          write(f,'(2x,a)')"Simply (Edge) Connected Node"
        endif
      end if
      if ( cc_primal ) write(f,*) ' k,off_index,off_index_simple=',&
                                    k,off_index,off_index_simple
      write(f,*)

      write(f,'(2x,a)')"Analytic"
      call write_values( f, a_lhs(:,:,ioff), 1 )

      if (complex_mode) then
        write(f,'(2x,a)')"Frechet Derivative Using Complex Variable Delta"
      else
        write(f,'(2x,a)')"Frechet Derivative Using Real Variable Delta"
      end if
      call write_values( f, a_rhs(:,:,ioff), 1 )

      write(f,'(2x,a)')"Error"
      call write_values( f, error_a_rhs(:,:,k), 1 )

      write(f,*)
      likely_errors = .false.
      !...check for the occurrence of likely errors.
      do i=1,njac
        do j=1,njac
          err_max = abs( real(error_a_rhs(i,j,k),dp) )
          if ( err_max <= 0.9999_dp*abs(max_error_a_rhs(k)) ) cycle
          if (abs(max_error_a_rhs(k)) < rel_factor*warn_off) cycle
          comparator = abs ( real(a_rhs(i,j,ioff)) )  !FD/CD
          if ( (comparator > rel_factor*warn_off ) .and.          &
               (err_max    > rel_factor*warn_off*comparator) ) then
            likely_errors = .true.
            rel_max = real(error_a_rhs(i,j,k))/comparator
            err_at_rel_max = err_max
            i_rel          = i
            j_rel          = j
          endif
        enddo
      end do

      print_derivative = .true.
      if ( likely_errors ) then
        write(f,*)
        write(f,'(2x,a,e13.5,a)')"  --> Max Rel. Error   = ",           &
                                 real(rel_max),                         &
                                 "    WARNING: LIKELY ERROR IN A_OFF !!!"
        write(f,'(2x,a,e13.5,a)')"  -->  At Abs. Error   = ",&
                                 real(err_at_rel_max)
        write(f,'(2x,a,2i4)')    "  -->  At (i,j)        = ",i_rel,j_rel
        write(f,*)
        write(f,'(2x,a,e13.5)')  "  --> Max Abs. Error   = ",&
                                 real(max_error_a_rhs(k),dp)
        likely_aoff_errors = .true.  !turn on global indicator
      elseif (abs(max_error_a_rhs(k)) > warn_off) then
        write(f,*)
        write(f,'(2x,a,e13.5,a)')                                         &
                                 "  --> Max Error        = ",             &
                                 real(max_error_a_rhs(k)),                &
                                 "    WARNING: POSSIBLE ERROR IN A_OFF !!!"
      else  !Supress information if below tolerance
        if ( abs( real( max_error_a_rhs(k),dp) ) > &
             abs( real(          tolerance,dp) )   ) then
          write(f,'(2x,a,e13.5)') "  --> Max Error  (off) = ",&
                                  real(max_error_a_rhs(k))
        else
          write(f,'(2x,a,e13.5)') "  --> Max Error  (off) <     tolerance"
          print_derivative = .false.
        endif
      end if
      if ( print_derivative ) then
        write(f,'(2x,a,e13.5)')  "              Analytic = ",real(an_ref_off(k))
        write(f,'(2x,a,e13.5)')  "               Frechet = ",real(fd_ref_off(k))
      endif
    end do

  end subroutine info_off

!================================ GFILE1_HEADER ==============================80
!
! Header.
!
!=============================================================================80

  subroutine gfile1_header( iu, ip, set, sets, frac, fraction_dof,             &
                            total, reset_q )

    use nml_global,        only : irest
    use debug_defs,        only : composite_jacobian_seed,                     &
                                  composite_jacobian_fraction,                 &
                                  ntt_for_jacobian_check

    integer,  intent(in) :: iu, ip, set, sets, frac, total
    logical,  intent(in) :: reset_q
! beginNeverComplex
    real(dp), intent(in) :: fraction_dof
! endNeverComplex

    continue

    if ( .not. lmpi_master ) return

    if ( ip == 0 ) then
    write(iu,*)
    write(iu,*)
    write(iu,'(2x,a)')"************** JACOBIAN CHECK **************************"
    if ( fraction_dof < 1._dp ) then
    write(iu,'(2x,3a)')" Jacobian Assessments At Random Locations"
    else
    write(iu,'(2x,3a)')" Jacobian Assessments At All Locations"
    endif
    write(iu,'(2x,2(a,i2))')"       set=",set," of",sets
    write(iu,'(2x,3a)')" First Diagonal Terms Followed by Off-Diagonal Terms"
    if ( .not.reset_q ) then
    write(iu,'(2x,2(a,i10))')" Solution : Not Reset by Manufactured Solution"
    write(iu,'(2x,2(a,i10))')"          : ntt_for_jacobian_check=",&
                                          ntt_for_jacobian_check
    write(iu,'(2x,2(a,i10))')"          :                  irest=",&
                                                           irest
    endif
    write(iu,'(2x,a)')"********************************************************"

    write(iu,*)
    write(iu,*) 'Number of assessments can be controlled via namelist:'
    write(iu,*) '&debug'
    write(iu,*) ' echo                        = .true.'
    write(iu,*) ' composite_jacobian_seed     = <random number seed>'
    write(iu,*) ' composite_jacobian_fraction = <fraction of global dof>'
    write(iu,*) '/'
    write(iu,*)
    write(iu,*) 'Currently::'
    write(iu,*) ' ......composite_jacobian_seed =',composite_jacobian_seed
    write(iu,*) ' ..composite_jacobian_fraction =',&
               real(composite_jacobian_fraction,dp)
    write(iu,*)
    endif

    if ( fraction_dof < 1._dp ) then
      write(iu,'(2x,a,i10,a,f20.10,a,i10)')                &
      ' lmpi_id=',ip,' DoF-Comparisons/DoF0=',fraction_dof,&
      ' DoF-Comparisons=',frac
      if ( ip == lmpi_nproc - 1 )                   &
      write(iu,'(2x,9x,10x,22x,14x,a,i10)')         &
      ' Total DoF-Comparisons=',total
    endif

  end subroutine gfile1_header

!================================ INFO_SUMMARY_DIAG ==========================80
!
! Writes out a summary  comparison between the analytic and finite difference
! (or complex) diagonal jacobians
!
!=============================================================================80

  subroutine info_summary_diag( iu, string, max_error_d, an_ref_diag,          &
                                fd_ref_diag )

    character(len=80),     intent(in) :: string

    integer,               intent(in) :: iu

    real(dp),              intent(in) :: max_error_d
    real(dp),              intent(in) :: an_ref_diag
    real(dp),              intent(in) :: fd_ref_diag

    real(dp) :: tolerance

    logical  :: print_derivative

    continue

    print_derivative = .true.

    tolerance = 100._dp*real_epsilon
    if ( complex_mode ) tolerance = 100._dp*complex_epsilon

    write(iu,*)
    write(iu,*)
    write(iu,'(2x,a)')"************** SUMMARY *********************************"
    write(iu,'(2x,3a)')" Max Errors For ",trim(string)," Jacobians"
    write(iu,'(2x,a)')"********************************************************"


    write(iu,*)
    if ( likely_adiag_errors ) then
      write(iu,'(2x,a,e13.5,a)')                                       &
                               "      Max Error (diag) = ",            &
                                real(max_error_d),                     &
                               "   WARNING: LIKELY ERROR IN A_DIAG !!!"
    elseif (abs(max_error_d) > warn_diag) then
      write(iu,'(2x,a,e13.5,a)')                                        &
                               "      Max Error (diag) = ",             &
                               real(max_error_d),                       &
                               "   WARNING: POSSIBLE ERROR IN A_DIAG !!!"
    else !Supress information if below tolerance
      if ( abs( real( max_error_d,dp) ) > &
           abs( real(   tolerance,dp) )   ) then
        write(iu,'(2x,a,e13.5)') "      Max Error (diag) = ",real(max_error_d)
      else
        write(iu,'(2x,a,e13.5)') "      Max Error (diag) <     tolerance"
        print_derivative = .false.
      endif
    end if

    if ( print_derivative ) then
      write(iu,'(2x,a,e13.5)') "              Analytic = ",real(an_ref_diag)
      write(iu,'(2x,a,e13.5)') "               Frechet = ",real(fd_ref_diag)
    endif

  end subroutine info_summary_diag


!================================ INFO_SUMMARY_OFF ===========================80
!
! Writes out a summary  comparison between the analytic and finite difference
! (or complex) off-diagonal jacobians
!
!=============================================================================80

  subroutine info_summary_off(iu,max_error_a_rhs,an_ref_off,fd_ref_off )

    integer,               intent(in) :: iu

    real(dp),              intent(in) :: max_error_a_rhs
    real(dp),              intent(in) :: an_ref_off
    real(dp),              intent(in) :: fd_ref_off

    real(dp) :: tolerance, limit

    logical  :: print_derivative

    continue

    print_derivative = .true.

    tolerance = 100._dp*real_epsilon
    if ( complex_mode ) tolerance = 100._dp*complex_epsilon
    limit     = epsilon(1._odp)
    tolerance = max( tolerance, limit )

    write(iu,*)
    if ( likely_aoff_errors ) then
      write(iu,'(2x,a,e13.5,a)')                                     &
                               "      Max Error  (off) = ",          &
                               real(max_error_a_rhs),                &
                               "   WARNING: LIKELY ERROR IN A_OFF !!!"
    elseif (abs(max_error_a_rhs) > warn_off) then
      write(iu,'(2x,a,e13.5,a)')                                       &
                               "      Max Error  (off) = ",            &
                               real(max_error_a_rhs),                  &
                               "   WARNING: POSSIBLE ERROR IN A_OFF !!!"
    else !Supress information if below tolerance
      if ( abs( real(max_error_a_rhs,dp) ) > &
           abs( real(      tolerance,dp) )   ) then
        write(iu,'(2x,a,e13.5)') "      Max Error  (off) = ",&
        real(max_error_a_rhs)
      else
        write(iu,'(2x,a,e13.5)') "      Max Error  (off) <     tolerance"
        print_derivative = .false.
      endif
    end if

    if ( print_derivative ) then
      write(iu,'(2x,a,e13.5)') "              Analytic = ",real(an_ref_off)
      write(iu,'(2x,a,e13.5)') "               Frechet = ",real(fd_ref_off)
    endif

  end subroutine info_summary_off

!================================ MXNORM =====================================80
!
! Matrix of error norms.
!
!=============================================================================80

  subroutine mxnorm( f, njac, error, entries, max_loc,                         &
                     likely_n, likely_proc, likely_dof, likely_off,            &
                     likely_i, likely_j, likely_max, likely_absolute )

    integer, intent(in) :: f, njac, entries

    real(dp), dimension(njac,njac,3), intent(in) :: error
    integer, dimension(:),            intent(in) :: max_loc

    integer,  intent(in) :: likely_n, likely_proc, likely_dof, likely_off,     &
                            likely_i, likely_j
    real(dp), intent(in) :: likely_max, likely_absolute

    integer  :: site

    continue

    site = 1
    if ( max_loc(3) == 0 ) site = 0

    write(f,*)
    if ( site == 0 ) then
      write(f,'(2x,a,i20)')'   Diagonal Jacobian Error at Maximum Error&
                           &...block-matrix comparisons=',entries
    else
      write(f,'(2x,a,i20)')'   Off-Diagonal Jacobian Error at Maximum Error&
                           &...block-matrix comparisons=',entries
    endif
    write(f,'(2x,a,i20)')   '   .........on processor=',max_loc(1)
    write(f,'(2x,2(a,i20))')'   .........at local dof=',max_loc(2),&
                                       '   global dof=',max_loc(4)
    if ( site == 1 )                                               &
    write(f,'(2x,2(a,i20))')'   .......from local dof=',max_loc(3),&
                                       '   global dof=',max_loc(5)

    write(f,*)
    write(f,'(2x,a)')"Analytic"
    call write_values( f, error(:,:,1), site )

    write(f,*)
    if (complex_mode) then
      write(f,'(2x,a)')"Frechet Derivative Using Complex Variable Delta"
    else
      write(f,'(2x,a)')"Frechet Derivative Using Real Variable Delta"
    end if
    call write_values( f, error(:,:,2), site )

    write(f,*)
    write(f,'(2x,a)')"Error"
    call write_values( f, error(:,:,3), site )

    write(f,*)
    write(f,'(2x,a,i20)')   '            LIKELY ERRORS=',likely_n
    if ( likely_n == 0 ) return
    write(f,'(2x,a,E20.4)') '           Relative Error=',&
                            real(likely_max,dp)
    write(f,'(2x,a,G20.8)') '           Relative Error=',&
                            real(likely_max,dp)
    write(f,'(2x,a,E20.4)') '        At Absolute Error=',&
                            real(likely_absolute,dp)
    write(f,'(2x,a,i20)')   '               at lmpi_id=',likely_proc
    write(f,'(2x,a,i20)')   '                   at dof=',likely_dof
    write(f,'(2x,a,i20)')   '                 from dof=',likely_off
    write(f,'(2x,a,i20)')   '               at block-i=',likely_i
    write(f,'(2x,a,i20)')   '               at block-j=',likely_j

  end subroutine mxnorm

!================================ MNORM ======================================80
!
! Matrix of error norms.
!
!=============================================================================80

  subroutine mnorm( f, error, entries, type )

    integer, intent(in) :: f, entries, type

    real(dp), dimension(:,:), intent(in) :: error

    character(len=20) :: ch

    continue

    ch = 'diagonal'
    if ( type == 1 ) ch = 'off-diagonal'

    write(f,*)
    write(f,'(2x,a,i20,2a)')'   L1 Norm of Jacobian Error...&
                         &block-matrix comparisons=',entries, ' entry=',trim(ch)
    write(f,*)
    call write_values( f, error, type )

  end subroutine mnorm


!================================ GET_ERROR_D ================================80
!
! Computes difference between finite difference/complex diagonal jacobians
! and the "analytic" diagonal jacobians at a point; also gets the max error
! over all components (e.g. 5x5) at the point, and keeps track of max error
! over all points
!
!=============================================================================80

  subroutine get_error_d( dof, ip, njac, dof0,                                 &
                          d_lhs, d_rhs, error_d, max_error_d_dof,              &
                          diag_sum, diag_entries, diag_max,  diag_max_loc,     &
                          fd_ref_diag_dof,                                     &
                          an_ref_diag_dof, max_error_d_global,                 &
                          an_ref_diag_global, fd_ref_diag_global, n )

    integer, intent(in)    :: dof, ip, n, dof0, njac
    integer, intent(inout) :: diag_entries

    real(dp), dimension(njac,njac,dof0), intent(in)    :: d_lhs
    real(dp), dimension(njac,njac,dof0), intent(in)    :: d_rhs
    real(dp), dimension(njac,njac),      intent(inout) :: error_d

    real(dp), intent(out) :: max_error_d_dof
    real(dp), intent(out) :: an_ref_diag_dof
    real(dp), intent(out) :: fd_ref_diag_dof

    real(dp), intent(inout) :: max_error_d_global
    real(dp), intent(inout) :: an_ref_diag_global
    real(dp), intent(inout) :: fd_ref_diag_global

    integer,  dimension(:),           intent(inout) :: diag_max_loc
    real(dp), dimension(njac,njac,3), intent(inout) :: diag_max
    real(dp), dimension(njac,njac),   intent(inout) :: diag_sum

    integer   :: i, j

    logical :: first

    continue

    diag_entries = diag_entries + 1
    do i = 1, njac
      do j = 1, njac
        error_d(i,j)  = (d_lhs(i,j,n) - d_rhs(i,j,n))
        diag_sum(i,j) = diag_sum(i,j) + abs( real(error_d(i,j), dp) )
      end do
    end do

    ! Find maximum over i,j

    max_error_d_dof = zero
    fd_ref_diag_dof = zero
    an_ref_diag_dof = zero
    first           = .true.

    do i = 1, njac
      do j = 1, njac
        if ( first .or. &
             abs(error_d(i,j)) > abs(max_error_d_dof) ) then
          max_error_d_dof  = error_d(i,j)
          fd_ref_diag_dof  = d_rhs(i,j,n)
          an_ref_diag_dof  = d_lhs(i,j,n)
          first = .false.
        end if
      end do
    end do

    call check_likely( n, n, njac,                                      &
                       d_lhs(1,1,n), d_rhs(1,1,n), error_d(1,1),        &
                       diag_likely_n, diag_likely_proc,                 &
                       diag_likely_dof, i,                              &
                       diag_likely_i, diag_likely_j,                    &
                       diag_likely_max, diag_likely_abs, warn_diag )

    ! Find global maximum

    first = .false.
    if ( diag_max_loc(1) < 0 ) first = .true.
    if ( first .or. &
         abs(max_error_d_dof) >  abs(max_error_d_global) ) then
      max_error_d_global      = max_error_d_dof
      fd_ref_diag_global      = fd_ref_diag_dof
      an_ref_diag_global      = an_ref_diag_dof
      diag_max(1:njac,1:njac,1) =   d_lhs(1:njac,1:njac,n)
      diag_max(1:njac,1:njac,2) =   d_rhs(1:njac,1:njac,n)
      diag_max(1:njac,1:njac,3) = error_d(1:njac,1:njac)
      diag_max_loc(1)         = ip
      diag_max_loc(2)         = dof
      diag_max_loc(3)         = 0
      first                   = .true.
    end if

  end subroutine get_error_d


!================================= GET_ERROR_A ===============================80
!
! Computes difference between finite difference/complex off-diagonal jacobians
! and the "analytic" off-diagonal jacobians at a point; also gets the max error
! over all components (e.g. 5x5) at the point, and keeps track of max error
! over all points
!
!=============================================================================80

  subroutine get_error_a( dof, ip, njac, nnz0, max_off_diag, je_list, dof_list,&
                          a_lhs, a_rhs, error_a, max_error_a_dof,              &
                          off_sum, off_entries, off_max, off_max_loc,          &
                          fd_ref_off_dof,                                      &
                          an_ref_off_dof, max_error_a_global,                  &
                          an_ref_off_global, fd_ref_off_global, off_indx)

    integer, intent(in)    :: dof, ip, nnz0, njac, max_off_diag, off_indx
    integer, intent(inout) :: off_entries

    integer, dimension(max_off_diag), intent(in) :: je_list, dof_list

    real(dp), dimension(njac,njac,nnz0),         intent(in)    :: a_lhs
    real(dp), dimension(njac,njac,nnz0),         intent(in)    :: a_rhs
    real(dp), dimension(njac,njac,max_off_diag), intent(inout) :: error_a

    real(dp), dimension(max_off_diag), intent(inout) :: max_error_a_dof
    real(dp), dimension(max_off_diag), intent(inout) :: an_ref_off_dof
    real(dp), dimension(max_off_diag), intent(inout) :: fd_ref_off_dof

    real(dp), intent(inout) :: max_error_a_global
    real(dp), intent(inout) :: an_ref_off_global
    real(dp), intent(inout) :: fd_ref_off_global

    integer,  dimension(:),           intent(inout) :: off_max_loc
    real(dp), dimension(njac,njac,3), intent(inout) :: off_max
    real(dp), dimension(njac,njac),   intent(inout) :: off_sum

    integer  :: i, j, k, ioff
    logical  :: first

  continue

    off_entries  = off_entries + off_indx
    do i = 1, njac
      do j = 1, njac
        do k = 1, off_indx
          ioff = je_list(k)
          error_a(i,j,k) = (a_lhs(i,j,ioff) - a_rhs(i,j,ioff))
          off_sum(i,j)   = off_sum(i,j) + abs( real(error_a(i,j,k),dp) )
         end do
      end do
    end do


   ! Find maximum over i,j at each k

    max_error_a_dof  = zero
    fd_ref_off_dof   = zero
    an_ref_off_dof   = zero
    do k = 1, off_indx
      first = .true.
      ioff = je_list(k)
      do i = 1, njac
        do j = 1, njac
          if ( first .or. &
               abs(error_a(i,j,k)) > abs(max_error_a_dof(k)) ) then
            max_error_a_dof(k) = error_a(i,j,k)
            fd_ref_off_dof(k)  = a_rhs(i,j,ioff)
            an_ref_off_dof(k)  = a_lhs(i,j,ioff)
            first              = .false.
          end if
        end do
      end do

      call check_likely( dof, dof_list(k), njac,                          &
                         a_lhs(1,1,ioff), a_rhs(1,1,ioff), error_a(1,1,k),&
                         aoff_likely_n, aoff_likely_proc,                 &
                         aoff_likely_dof, aoff_likely_off,                &
                         aoff_likely_i, aoff_likely_j,                    &
                         aoff_likely_max, aoff_likely_abs, warn_off )

    end do

    ! Find global maximum

    first = .false.
    if ( off_max_loc(1) < 0 ) first = .true.
    do k = 1, off_indx
      ioff = je_list(k)
      if ( first .or. &
           abs(max_error_a_dof(k)) > abs(max_error_a_global) ) then
        max_error_a_global     = max_error_a_dof(k)
        fd_ref_off_global      = fd_ref_off_dof(k)
        an_ref_off_global      = an_ref_off_dof(k)
        off_max(1:njac,1:njac,1) =   a_lhs(1:njac,1:njac,ioff)
        off_max(1:njac,1:njac,2) =   a_rhs(1:njac,1:njac,ioff)
        off_max(1:njac,1:njac,3) = error_a(1:njac,1:njac,k)
        off_max_loc(1)         = ip
        off_max_loc(2)         = dof
        off_max_loc(3)         = dof_list(k)
        first = .false.
      end if
    end do

  end subroutine get_error_a

!=================================== INITCV ==================================80
!
! Initialized q to the nominal conserved variable state
!
!=============================================================================80

  subroutine initcv(eqn_set, q_dof, dof0, n_tot, n_q, n_turb, pressure_jac,    &
                    enthalpy)

    use fluid,              only : gamma, gm1
    use info_depr,          only : lowmach_prec, xmach, cc_primal
    use ivals,              only : rho0, p0
    use twod_util,          only : q_2d
    use solution_types,     only : compressible, generic_gas
    use generic_gas_map,    only : n_species, n_momz, n_momx, n_momy, n_etot,  &
                                   n_energy_j, n_energy, n_temperature_j
    use thermo_gen,         only : thermo_interface

    integer, intent(in) :: eqn_set, dof0, n_tot, n_q, n_turb

    real(dp), dimension(n_tot,dof0),              intent(inout) :: q_dof
    real(dp), dimension(n_species,n_energy,dof0), intent(inout) :: enthalpy

    real(dp) :: rho, ru, rv, rw, p, pterm, term, pi, conv, q2
    real(dp) :: alpha_test, yaw_test

    real(dp), dimension(5) :: variation

    real(dp), dimension(:), allocatable :: my_pturb

    integer :: i, j

    !For generic gas path
    real(dp) :: epsgen
    real(dp), dimension(:,:,:), optional, intent(out) :: pressure_jac
    !end generic gas path variables

    real(dp), parameter :: my_1    =   1.0_dp
    real(dp), parameter :: my_half =   0.5_dp
    real(dp), parameter :: my_p3   =   0.03_dp
    real(dp), parameter :: my_p4   =   0.04_dp
    real(dp), parameter :: my_p5   =   0.5_dp
    real(dp), parameter :: eps     =   0.01_dp

    continue

    if(lowmach_prec) then

!     Set q_dof for conserved variables (compressible)
!     Note: bias velocities away from zero to avoid differentiation problem
!           of absolute value.

      alpha_test = 30.0_dp
      yaw_test   = 15.0_dp

      variation(1) = 0.50_dp
      variation(2) = 0.25_dp
      variation(3) = 0.10_dp
      variation(4) = 0.15_dp
      variation(5) = 0.30_dp

      pi    = acos(-my_1)
      conv  = 180.0_dp/pi

      rho  =   rho0
      ru   =   rho0*xmach*cos(alpha_test/conv)*cos(yaw_test/conv)
      rv   = - rho0*xmach*cos(alpha_test/conv)*cos(yaw_test/conv)
      rw   =   rho0*xmach*cos(alpha_test/conv)*cos(yaw_test/conv)
      p    =   p0

      do i = 1,dof0

        term = real(i,dp)/real(dof0,dp)

        q_dof(1,i) = rho*( my_1 + variation(1)*term )
        q_dof(2,i) =  ru*( my_1 + variation(2)*term )
        q_dof(3,i) =  rv*( my_1 + variation(3)*term )
        q_dof(4,i) =  rw*( my_1 + variation(4)*term )
        pterm      =   p*( my_1 + variation(5)*term )

        if ( q_2d ) q_dof(3,i) = 0._dp

        q_dof(5,i) = pterm/gm1 + my_half*                                     &
                    (q_dof(2,i)**2 + q_dof(3,i)**2 + q_dof(4,i)**2)/q_dof(1,i)
      end do

    elseif ( eqn_set == compressible ) then

!     Set q_dof for conserved variables (compressible)

      do i = 1,dof0

        q_dof(1,i) = my_1

        q_dof(2,i) = my_p3 *q_dof(1,i) + real(i,dp)*eps

        q_dof(3,i) = my_p4 *q_dof(1,i) + real(i,dp)*eps

        if ( q_2d ) q_dof(3,i) = 0._dp

        q_dof(4,i) = my_p5 *q_dof(1,i) + real(i,dp)*eps

        q_dof(5,i) = (my_1/gamma)/gm1 + my_half*                               &
                     ( q_dof(2,i)*q_dof(2,i)                                   &
                     + q_dof(3,i)*q_dof(3,i)                                   &
                     + q_dof(4,i)*q_dof(4,i) )/q_dof(1,i)

      end do

    elseif ( eqn_set == generic_gas ) then

!     Set q_dof for conserved variables (generic_gas)

      epsgen = 1._dp

      do i = 1,dof0

        rho = sum(q_dof(1:n_species,i))

        q_dof(n_momx,i) = my_p3*rho + real(i,dp)*eps

        q_dof(n_momy,i) = my_p4*rho + real(i,dp)*eps

        if ( q_2d ) q_dof(n_momz,i) = 0._dp

        q_dof(n_momz,i) = my_p5*rho + real(i,dp)*eps

        q_dof(n_momx:n_momz,i) = q_dof(n_momx:n_momz,i)*epsgen

        q2 = (q_dof(n_momx,i)*q_dof(n_momx,i)                                  &
           + q_dof(n_momy,i)*q_dof(n_momy,i)                                   &
           + q_dof(n_momz,i)*q_dof(n_momz,i))/rho**2

        q_dof(n_temperature_j(1),i) = 300._dp  !Set constant temperature

        call thermo_interface(q_dof(:,i),n_tot,pressure_jac(:,:,i),            &
                              enthalpy(:,:,i),0)

        !total energy
        q_dof(n_etot,i) = rho*(q_dof(n_energy_j(1),i) + my_half*q2)

      end do

    else

!     Set q_dof for conserved variables (incompressible)

      do i = 1,dof0

        q_dof(1,i) = my_1

        q_dof(2,i) = my_p3 + real(i,dp)*eps

        q_dof(3,i) = my_p4 + real(i,dp)*eps

        if ( q_2d ) q_dof(3,i) = 0._dp

        q_dof(4,i) = my_p5 + real(i,dp)*eps

      end do

    end if

    if ( (.not.cc_primal) .or. (n_turb == 0) ) return

    allocate(my_pturb(n_turb))

    do j = 1,n_turb
      my_pturb(j) = 0.02_dp * real(j, dp)
    end do

    do i = 1,dof0

      do j = 1,n_turb
        q_dof(n_q-n_turb+j,i) = my_pturb(j) + real(i,dp)*eps
      end do

!!!!!!!      amut(i) = my_pamut + real(i,dp)*teps
    end do

    deallocate(my_pturb)

  end subroutine initcv

!==================================== INIT_TURB ==============================80
!
! Initialize turbulence quantities to the nominal state
!
!=============================================================================80

  subroutine init_turb(turb, amut, nnodes01, n_turb)

    integer,                                 intent(in)    :: nnodes01, n_turb

    real(dp), dimension(n_turb,nnodes01),    intent(inout) :: turb
    real(dp), dimension(nnodes01),           intent(inout) :: amut

    integer                 :: i, j

    real(dp), dimension(:), allocatable    :: my_pturb

    real(dp), parameter     :: my_pamut = 657.8_dp
    real(dp), parameter     :: eps      = .01_dp

    continue

    allocate(my_pturb(n_turb))

    do j = 1,n_turb
      my_pturb(j) = 0.02_dp * real(j, dp)
    end do

    do i = 1,nnodes01

      do j = 1,n_turb
        turb(j,i) = my_pturb(j) + real(i,dp)*eps
      end do

      amut(i) = my_pamut + real(i,dp)*eps
    end do

    deallocate(my_pturb)

  end subroutine init_turb

!============================== REORDER_LHS_M2G ==============================80
!
! Copy the diagonal and off diagonal jacobian arrays
! converting from matrix ordering to grid ordering.
!
!=============================================================================80

  subroutine reorder_lhs_m2g( soln, crow, set, njac, ierr )

    use stability_defs,       only : d_lhs, a_lhs, adj_rhs

    integer,             intent(in)  :: set, njac
    integer,             intent(out) :: ierr
    type(soln_type),     intent(in)  :: soln
    type(crow_flow),     intent(in)  :: crow

    integer :: i, j, k, kk, entry, dof, km, dofm

! beginNeverComplex
    real(dp) :: aj_sums
! endNeverComplex

  continue

    ierr = 0

    if ( set == 2 ) then

      do dof = 1,soln%dof0
        dofm = crow%g2m(dof)
        do i = 1, njac
          do j = 1, njac
            d_lhs(i,j,dof) = soln%a_turb_diag(i,j,dofm)
          end do
        end do
        entry = 0
        do k = adj_rhs%ia(dof), adj_rhs%ia(dof+1)-1
          entry = entry + 1
          if ( entry <= crow%ia(dof+1)-crow%ia(dof) ) then
            kk = crow%ia(dof) + entry - 1
            km = crow%nzg2m(k)
            do i = 1, njac
              do j = 1, njac
                a_lhs(i,j,k) = soln%a_turb_off(i,j,km)
              end do
            end do
          else
            do i = 1, njac
              do j = 1, njac
                a_lhs(i,j,k) = 0._dp
              end do
            end do
          endif
        end do
      end do

    else

      do dof = 1, soln%dof0
        dofm = crow%g2m(dof)
        do i = 1, njac
          do j = 1, njac
            d_lhs(i,j,dof) = soln%a_diag(i,j,dofm)
          end do
        end do
        entry = 0
        do k = adj_rhs%ia(dof), adj_rhs%ia(dof+1)-1
          entry = entry + 1
          if ( entry <= crow%ia(dof+1)-crow%ia(dof) ) then
            kk = crow%ia(dof) + entry - 1
            km = crow%nzg2m(kk)
            do i = 1, njac
              do j = 1, njac
                a_lhs(i,j,k) = soln%a_off(i,j,km)
              end do
            end do
          else
            do i = 1, njac
              do j = 1, njac
                a_lhs(i,j,k) = 0._dp
              end do
            end do
          endif
        end do
      end do

    end if

    ! Accumulate analytic Jacobian sums.

    aj_sums = sum( abs( real( d_lhs(:,:,:), dp ) ) ) &
            + sum( abs( real( a_lhs(:,:,:), dp ) ) )

    if ( aj_sums < 1.0e-11_dp ) then
      ierr = 1
      write(*,*) ' Reordering and setting Jacobians...lmpi_id=',lmpi_id
      write(*,*) ' ...set=',set,' njac=',njac,' aj_sums=',aj_sums
    endif

  end subroutine reorder_lhs_m2g

!========================= HEADER_COMPOSITE_JACOBIAN =========================80
!
! Blurt.
!
!=============================================================================80

  subroutine header_composite_jacobian

  use debug_defs,           only : composite_jacobian_lhs,                     &
                                   composite_jacobian_rhs, allow_dt
  use info_depr,            only : ivisc
  use inviscid_flux,        only : flux_construction, flux_construction_lhs,   &
                                   first_order_iterations, iflim
  use debug_jacobian,       only : edge_terms_only

  continue

    if ( .not.lmpi_master ) return

    write(*,*)
    write(*,*) '*************** NOTES ON COMPOSITE JACOBIAN CHECKING **********'
    write(*,'(1x,a,L1)') ".....composite_jacobian_lhs=",composite_jacobian_lhs
    write(*,'(1x,a,L1)') ".....composite_jacobian_rhs=",composite_jacobian_rhs
    write(*,'(1x,a,L1)') "...................allow_dt=",allow_dt
    write(*,*)
    write(*,'(1x,a,i10)') "Inviscid flux parameters are below:"
    write(*,'(1x,a,i10)') ".....first_order_iterations=",first_order_iterations
    write(*,'(1x,a,i10)') ".....iflim=",iflim
    write(*,'(1x,2a)')    ".....flux_construction    =",&
                           trim(flux_construction)
    write(*,'(1x,2a)')    ".....flux_construction_lhs=",&
                           trim(flux_construction_lhs)

    write(*,*)
    write(*,'(1x,a,i10)') ".............ivisc=",ivisc
    if( ivisc > 0 )                                           &
    write(*,'(1x,a,L10)') "...edge_terms_only=",edge_terms_only
    write(*,*)
    if (complex_mode) then
      write(*,'(1x,a,e13.5)') "Complex Step Size = ",complex_epsilon
    else
      write(*,'(1x,a,e13.5)') "Finite Difference Step Size = ",real_epsilon
    end if
    write(*,*)
    write(*,*)
    write(*,'(1x,a)') "Potential problem inconsistencies are flagged as:"
    write(*,*)
    write(*,'(1x,a)') "  WARNING: NO RESIDUAL JACOBIAN CONTRIBUTIONS!!!"
    write(*,'(1x,a)') "              -or-"
    write(*,'(1x,a)') "  WARNING: NO ANALYTIC JACOBIAN CONTRIBUTIONS!!!"
    write(*,*)
    write(*,*)
    write(*,'(1x,a)') "Potential Jacobian errors are flagged with warnings:"
    write(*,*)
    write(*,'(1x,a)') "  WARNING: POSSIBLE ERROR IN A_OFF !!!"
    write(*,'(1x,a)') "              -or-"
    write(*,'(1x,a)') "  WARNING: POSSIBLE ERROR IN A_DIAG !!!"
    write(*,*)
    write(*,'(1x,a)') "  WARNING: LIKELY ERROR IN A_OFF !!!"
    write(*,'(1x,a)') "              -or-"
    write(*,'(1x,a)') "  WARNING: LIKELY ERROR IN A_DIAG !!!"
    write(*,*)
    write(*,'(1x,a)') "Currently:"
    write(*,*)
    write(*,'(1x,2a,e13.5,a)') "POSSIBLE ERRORS issued when |error|",&
                               " > ",warn_diag,' : a_diag'
    write(*,'(1x,2a,e13.5,a)') "POSSIBLE ERRORS issued when |error|",&
                               " > ",warn_off, ' : a_off'
    write(*,*)
    write(*,'(1x,2a,e13.5,a)') "LIKELY ERRORS issued when |relative-error|",&
                               " > ",warn_diag_rel,' : a_diag'
    write(*,'(1x,2a,e13.5,a)') "LIKELY ERRORS issued when |relative-error|",&
                               " > ",warn_off_rel, ' : a_off'
    write(*,*)
    write(*,*)
    write(*,*) '*************** NOTES ON COMPOSITE JACOBIAN CHECKING **********'
    write(*,*)

  end subroutine header_composite_jacobian

!==================================== HEADER_SET =============================80
!
! Blurt.
!
!=============================================================================80

  subroutine header_set( eqn_set, jacobian_type )

    use solution_types, only : compressible, incompressible, generic_gas

    integer,      intent(in) :: eqn_set
    character(*), intent(in) :: jacobian_type

  continue

    !if ( eqn_set /= compressible .and. eqn_set /= incompressible ) then
    !  call lmpi_conditional_stop(1,'header_set: only in/compress pg')
    !endif

    if ( .not.lmpi_master ) return

    write(*,*)
    write(*,*) '*************************************************************'
    write(*,*) '*************************************************************'
    write(*,*)
    if ( eqn_set == compressible ) then
      write(*,*) '    Special code to check Compressible ',                  &
                      trim(jacobian_type),' jacobians'
    else if ( eqn_set == incompressible ) then
      write(*,*) '    Special code to check Incompressible ',                &
                      trim(jacobian_type),' jacobians'
    else if ( eqn_set == generic_gas ) then
      write(*,*) '    Special code to check Generic Gas ',                   &
                      trim(jacobian_type),' jacobians'
    end if
    write(*,*) '    for all points (interior + boundary)'

    write(*,*)
    write(*,*) '*************************************************************'
    write(*,*) '*************************************************************'
    write(*,*)

  end subroutine header_set

!============================== CHECK_CROW_ENTRIES ===========================80
!
! Check crow entries.
!
!=============================================================================80

  subroutine check_crow_entries(grid, soln, crow)

    use stability_defs, only : adj_rhs

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(inout) :: crow

    integer :: k, n

  continue

    loop_dof : do n = 1, soln%dof0
      write(30,*)
      write(30,"(1x,a,i10)") ' Degree of freedom(  grid order)=',n
      write(30,"(1x,a,i10)") ' Degree of freedom(matrix order)=',crow%g2m(n)
      write(30,"(1x,a,f20.10)") ' ...xq=',grid%xq(n)
      write(30,"(1x,a,f20.10)") ' ...yq=',grid%yq(n)
      write(30,"(1x,a,f20.10)") ' ...zq=',grid%zq(n)
      write(30,"(1x,a,i10)")    ' ............off_diagonals=',&
                               crow%ia(n+1)-crow%ia(n)
      write(30,"(1x,a,i10)")    ' .......simple connections=',&
                               crow%ia_ns(n)-crow%ia(n)
      write(30,"(1x,a,i10)")    ' ...non-simple connections=',&
                               crow%ia(n+1)-crow%ia_ns(n)
      write(30,"(1x,a,i10)")    ' ..........rhs connections=',&
                               adj_rhs%ia(n+1)-adj_rhs%ia(n)
      write(30,*) ' .................Simply-connected adjacencies:'
      write(30,"(1x,10i5)") (crow%ja(k),k=crow%ia(n),crow%ia_ns(n)-1)
      if ( crow%ia_ns(n) /= crow%ia(n+1) ) then
      write(30,*) ' .................Non-simply-connected adjacencies:'
      write(30,"(1x,10i5)") (crow%ja(k),k=crow%ia_ns(n),crow%ia(n+1)-1)
      endif
      if ( adj_rhs%ia(n+1) - adj_rhs%ia(n) > crow%ia(n+1) - crow%ia(n) ) then
      write(30,*) ' ..................................RHS adjacencies:'
      write(30,"(1x,10i5)") (adj_rhs%ja(k),k=adj_rhs%ia(n),adj_rhs%ia(n+1)-1)
      endif
    end do loop_dof

  end subroutine check_crow_entries

!==================== INITIALIZE_Q_FRECHET ===================================80
!
! Initialized conserved variable to the nominal state.
!
!=============================================================================80

  subroutine initialize_q_frechet( grid, soln )

    use exact, only : exact_q

    type(grid_type), intent(inout) :: grid
    type(soln_type), intent(inout) :: soln

    integer :: dof, n_q, n_turb, n_mf

    real(dp), dimension(soln%n_q) :: q_exact

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = soln%n_q - n_turb

    do dof=1,soln%dof0

      call exact_q( soln%eqn_set,  soln%n_q,                &
                   grid%xq(dof), grid%yq(dof), grid%zq(dof), q_exact)

      if ( grid%cc ) then
        soln%q_dof(1:n_q,   dof) = q_exact(     1:n_q)
      else
        soln%q_dof(1:n_mf,  dof) = q_exact(     1:n_mf)
        soln%turb( 1:n_turb,dof) = q_exact(n_mf+1:n_q)
      endif

    enddo

  end subroutine initialize_q_frechet

!==================================== GET_CMP_RESIDUAL =======================80
!
! Computes residuals of perturbed q_dof.
!
!=============================================================================80

  subroutine get_cmp_residual( grid, soln, mass, crow, ip, set,                &
                               n, njac, r1, pdof, pq)

    use info_depr,         only : tightly_couple
    use node_avg_cc,       only : qt_avg_to_nodes
    use bc_cache_cc,       only : qt_lsq
    use cc_defs,           only : clsq_bc
    use fun3d_res_flow,    only : res_flow
    use flux_turb,         only : turbulent_residual
    use flux_turb_cc,      only : turbulent_residual_cc
    use lmpi_app,          only : lmpi_xfer
    use solution_types,    only : generic_gas
    use inviscid_flux,     only : dc_part
    use debug_defs,        only : debug_dc

    integer, intent(in) :: ip, set, n, njac, pdof, pq

    real(dp), dimension(njac),    intent(out) :: r1

    type(grid_type),               intent(inout) :: grid
    type(soln_type),               intent(inout) :: soln
    type(mass_type), dimension(:), intent(in)    :: mass
    type(crow_flow),               intent(in)    :: crow

    integer :: pqt, n_q, n_turb, n_mf

    integer, dimension(0:lmpi_nproc-1) :: dof_site

    real(dp), parameter :: zero = 0.0_dp

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = n_q - n_turb

    !...normally these are done on the meanflow side.
    if ( grid%cc ) then
      if ( soln%viscous_method == 4 ) then
        call qt_avg_to_nodes(grid, soln)
      endif
      if ( clsq_bc .or. soln%viscous_method == 3 ) then
        call qt_lsq(soln%eqn_set, soln%n_q, soln%q_dof, grid%bcc)
      endif
    endif

    ! Set up the sites corresponding to on-processor dof.

    if ( grid%cc ) then
      call q_site( soln%dof0, grid%cl2g, pdof, ip, dof_site )
    else
      call q_site( soln%dof0, grid%l2g, pdof, ip, dof_site )
    endif

    if_generic: if (soln%eqn_set == generic_gas) then
      if ( set == 1 .and. tightly_couple .and. .not.grid%cc ) then
        if ( pq <= n_mf ) then
          call perturb_gen( soln%q_dof, pq, soln%n_tot, dof_site,              &
                            soln%pressure_jac, soln%enthalpy_ij )
          call res_flow(grid, soln, crow, mass)
          call turbulent_residual(grid, soln, crow)
          call lmpi_xfer( soln%res )
          if ( ip == lmpi_id ) then
            r1(:) = soln%res(:,n)
          endif
          call restore_gen( soln%q_dof, soln%pressure_jac, soln%enthalpy_ij,   &
                            dof_site )
        else
          pqt = pq - n_mf
          call perturb_gen( soln%turb, pqt, soln%n_tot, dof_site,              &
                            soln%pressure_jac, soln%enthalpy_ij )
          call res_flow(grid, soln, crow, mass)
          call turbulent_residual(grid, soln, crow)
          call lmpi_xfer( soln%res )
          if ( ip == lmpi_id ) then
            r1(:) = soln%res(:,n)
          endif
          call restore_gen( soln%turb, soln%pressure_jac, soln%enthalpy_ij,    &
                            dof_site )
        endif
      elseif ( set == 1 .and. .not.grid%cc ) then
        call perturb_gen( soln%q_dof, pq, soln%n_tot, dof_site,                &
                          soln%pressure_jac, soln%enthalpy_ij )
        if (debug_dc) then
          dc_part = .false.
          call res_flow(grid, soln, crow, mass)
          soln%res = zero
          dc_part = .true.
          call res_flow(grid, soln, crow, mass)
          dc_part = .false.
        else
          call res_flow(grid, soln, crow, mass)
        end if
        call lmpi_xfer( soln%res )
        if ( ip == lmpi_id ) then
          r1(:) = soln%res(:,n)
        endif
        call restore_gen( soln%q_dof, soln%pressure_jac, soln%enthalpy_ij,     &
                          dof_site )
      elseif ( set == 1 ) then
        call perturb_gen( soln%q_dof, pq, soln%n_tot, dof_site,                &
                          soln%pressure_jac, soln%enthalpy_ij )
        call lmpi_xfer( soln%res )
        if ( ip == lmpi_id ) then
          r1(:) = soln%res(:,n)
        endif
        call restore_gen( soln%q_dof, soln%pressure_jac, soln%enthalpy_ij,     &
                          dof_site )
      elseif( grid%cc ) then
        pqt = pq + n_mf
        call perturb_gen( soln%q_dof, pqt, soln%n_tot, dof_site,               &
                          soln%pressure_jac, soln%enthalpy_ij )
        call turbulent_residual_cc(grid, soln, crow)
        call lmpi_xfer( soln%turbres )
        if ( ip == lmpi_id ) then
          r1(:) = soln%turbres(:,n)
        endif
        call restore_gen( soln%q_dof, soln%pressure_jac, soln%enthalpy_ij,     &
                          dof_site )
      else
        pqt = pq + n_mf
        call perturb_gen( soln%turb, pq, soln%n_tot, dof_site,                 &
                          soln%pressure_jac, soln%enthalpy_ij )
        call turbulent_residual(grid, soln, crow)
        call lmpi_xfer( soln%turbres )
        if ( ip == lmpi_id ) then
          r1(:) = soln%turbres(:,n)
        endif
        call restore_gen( soln%turb, soln%pressure_jac, soln%enthalpy_ij,      &
                          dof_site )
      endif

    else if_generic

      if ( set == 1 .and. tightly_couple .and. .not.grid%cc ) then
        if ( pq <= n_mf ) then
          call perturb( soln%q_dof, pq, dof_site )
          call res_flow(grid, soln, crow, mass)
          call turbulent_residual(grid, soln, crow)
          call lmpi_xfer( soln%res )
          if ( ip == lmpi_id ) then
            r1(:) = soln%res(:,n)
          endif
          call restore( soln%q_dof, pq, pq, dof_site )
        else
          pqt = pq - n_mf
          call perturb( soln%turb, pqt, dof_site )
          call res_flow(grid, soln, crow, mass)
          call turbulent_residual(grid, soln, crow)
          call lmpi_xfer( soln%res )
          if ( ip == lmpi_id ) then
            r1(:) = soln%res(:,n)
          endif
          call restore( soln%turb, pq, pqt, dof_site )
        endif
      elseif ( set == 1 .and. .not.grid%cc ) then
        call perturb( soln%q_dof, pq, dof_site )
        call res_flow(grid, soln, crow, mass)
        call lmpi_xfer( soln%res )
        if ( ip == lmpi_id ) then
          r1(:) = soln%res(:,n)
        endif
        call restore( soln%q_dof, pq, pq, dof_site )
      elseif ( set == 1 ) then
        call perturb( soln%q_dof, pq, dof_site )
        call res_flow(grid, soln, crow, mass)
        call lmpi_xfer( soln%res )
        if ( ip == lmpi_id ) then
          r1(:) = soln%res(:,n)
        endif
        call restore( soln%q_dof, pq, pq, dof_site )
      elseif( grid%cc ) then
        pqt = pq + n_mf
        call perturb( soln%q_dof, pqt, dof_site )
        call turbulent_residual_cc(grid, soln, crow)
        call lmpi_xfer( soln%turbres )
        if ( ip == lmpi_id ) then
          r1(:) = soln%turbres(:,n)
        endif
        call restore( soln%q_dof, pqt, pqt, dof_site )
      else
        pqt = pq + n_mf
        call perturb( soln%turb, pq, dof_site )
        call turbulent_residual(grid, soln, crow)
        call lmpi_xfer( soln%turbres )
        if ( ip == lmpi_id ) then
          r1(:) = soln%turbres(:,n)
        endif
        call restore( soln%turb, pqt, pq, dof_site )
      endif
    end if if_generic

    if ( 0 == 1 ) &
    call complete_restore( grid%cc, n_q, n_turb, soln%q_dof, soln%turb )

  end subroutine get_cmp_residual

!==================================== Q_SITE =================================80
!
! Find sites corresponding to perturbed Q.
!
!=============================================================================80

  subroutine q_site( dof0, l2g, pdof, ip, dof_site )

    integer, intent(in) :: pdof, ip, dof0

    integer, dimension(:),              intent(in)    :: l2g
    integer, dimension(0:lmpi_nproc-1), intent(inout) :: dof_site

    integer :: dof, ierr, i, found, pdofg

    logical :: on_proc

  continue

    ierr        = 0
    dof_site(:) = 0
    pdofg       = 0
    found       = 0

    on_proc = .true.
    if ( ip == lmpi_id ) then
      if ( pdof > dof0 ) on_proc = .false.
      pdofg        = l2g( pdof )
      dof_site(ip) = pdof
    endif
    call lmpi_bcast( on_proc, ip )

    call lmpi_bcast( pdofg, ip )

    if ( on_proc ) then

      if ( lmpi_id == ip ) then
        found        = 1
      endif

    else

      found = 0
      do dof=1,dof0
        if ( pdofg /= l2g(dof) ) cycle
        found             = 1
        dof_site(lmpi_id) = dof
        exit
      enddo

    endif

    i = found ; call lmpi_reduce(i, found ) ; call lmpi_bcast(found)

    if ( found == 0 ) ierr = 1
    call lmpi_conditional_stop(ierr,'off-processor dofg not found:q_site')

    if ( found /= 1 ) ierr = found
    call lmpi_conditional_stop(ierr,'Too many dofg:q_site')

    ! Find all sites not found.

    if ( dof_site(lmpi_id) == 0 ) then
      do dof=dof0+1,size(l2g,1)
        if ( pdofg /= l2g(dof) ) cycle
        dof_site(lmpi_id) = dof
        exit
      enddo
    endif

  end subroutine q_site

!==================================== PERTURB ================================80
!
! Perturb Q.
!
!=============================================================================80

  subroutine perturb( q, m, dof_site )

    integer,  intent(in) :: m

    real(dp), dimension(:,:),            intent(inout) :: q
    integer,  dimension(0:lmpi_nproc-1), intent(in)    :: dof_site

    integer :: dof

  continue

    dof = dof_site(lmpi_id)

    if ( dof == 0 ) return

    q(m,dof) =   q( m,dof) + delta_q

  end subroutine perturb

!================================== PERTURB_GEN ==============================80
!
! Perturb Q (generic gas path)
!
!=============================================================================80

  subroutine perturb_gen( q, m, n_tot, dof_site, pjac, enthalpy )

    use generic_gas_map,      only : n_species, n_pressure_k,                  &
                                     n_momx, n_momy, n_momz,                   &
                                     n_etot, n_density
    use inviscid_flux,        only : mean_decouple
    use thermo_gen,           only : thermo_interface

    integer,  intent(in) :: m, n_tot

    real(dp), dimension(:,:),            intent(inout) :: q
    real(dp), dimension(:,:,:),          intent(inout) :: pjac
    real(dp), dimension(:,:,:),          intent(inout) :: enthalpy
    integer,  dimension(0:lmpi_nproc-1), intent(in)    :: dof_site

    real(dp), dimension(n_species)   :: frac_i
    real(dp), dimension(n_species+1) :: beta

    real(dp) :: dq_rho,dq,rhoinv,alpha
    integer  :: dof, ns

  continue

    dof = dof_site(lmpi_id)

    if ( dof == 0 ) return

    beta(:) = pjac(:,1,dof)

    rhoinv   = 1.0_dp/sum(q(1:n_species,dof))

    if (mean_decouple .and. m <= n_species) then
      do ns = 1,n_species
        frac_i(ns) = q(ns,dof)*rhoinv
        dq         = delta_q*frac_i(ns)
        q(ns,dof)  = q(ns,dof) + dq
      end do
    else
      q(m,dof) =   q( m,dof) + delta_q
    end if

    alpha = (q(n_momx,dof)*q(n_momx,dof)                                       &
           + q(n_momy,dof)*q(n_momy,dof)                                       &
           + q(n_momz,dof)*q(n_momz,dof))*rhoinv*rhoinv
    beta(1:n_species) = beta(1:n_species)                              &
                          + beta(n_species+1)*0.5_dp*alpha

    if (m <= n_species) then
      if(mean_decouple) then
        dq_rho = delta_q*real(sum(frac_i(1:n_species)*beta(1:n_species)))
      else
        dq_rho = delta_q*beta(m)
      end if
      q(n_density,dof) = sum(q(1:n_species,dof))
    else if (m > n_species .and. m < n_etot) then
      dq_rho = -delta_q*beta(n_species+1)*q(m,dof)*rhoinv
    else if (m == n_etot) then
      dq_rho = delta_q*beta(n_species+1)
    else
      write(*,*) "Error in perturb_gen!  Stopping..."
      call lmpi_conditional_stop(1)
    end if

    q(n_pressure_k(1),dof) = q(n_pressure_k(1),dof) + dq_rho

    call thermo_interface(q(:,dof),n_tot,pjac(:,:,dof),enthalpy(:,:,dof),2)

  end subroutine perturb_gen

!================================== RESTORE_GEN ==============================80
!
! Restore Q (generic gas path)
!
!=============================================================================80

  subroutine restore_gen( q, pjac, H, dof_site )

    use stability_defs,    only : q00, pjac00, H00

    real(dp), dimension(:,:),            intent(inout) :: q
    real(dp), dimension(:,:,:),          intent(inout) :: pjac
    real(dp), dimension(:,:,:),          intent(inout) :: H
    integer,  dimension(0:lmpi_nproc-1), intent(in)    :: dof_site

    integer :: dof

  continue

    dof = dof_site(lmpi_id)

    if ( dof == 0 ) return

    q(:,dof)      = q00(:,dof)
    pjac(:,:,dof) = pjac00(:,:,dof)
    H(:,:,dof)    = H00(:,:,dof)

  end subroutine restore_gen

!==================================== RESTORE ================================80
!
! Restore Q.
!
!=============================================================================80

  subroutine restore( q, pq, m, dof_site )

    use stability_defs,    only : q00

    integer,  intent(in) :: pq, m

    real(dp), dimension(:,:),            intent(inout) :: q
    integer,  dimension(0:lmpi_nproc-1), intent(in)    :: dof_site

    integer :: dof

  continue

    dof = dof_site(lmpi_id)

    if ( dof == 0 ) return

    q(m,dof) = q00(pq,dof)

  end subroutine restore

!==================================== COMPLETE_RESTORE =======================80
!
! Restore all of Q.
!
!=============================================================================80

  subroutine complete_restore( cc, n_q, n_turb, q_dof, turb )

    use stability_defs,    only : q00

    logical, intent(in) :: cc

    integer, intent(in) :: n_q, n_turb

    real(dp), dimension(:,:), intent(inout) :: q_dof, turb

    integer :: n_mf, t

  continue

    n_mf = n_q - n_turb

    if ( cc ) then
      q_dof(  1:n_q, : ) = q00(    1:n_q, : )
    else
      q_dof( 1:n_mf, : ) = q00(   1:n_mf, : )
      do t=1,n_turb
        turb(     t, : ) = q00( n_mf + t, : )
      enddo
    endif

  end subroutine complete_restore

!================================= SET_GOLD_DOF ==============================80
!
! Set dof for golden file and write to file.
!
!=============================================================================80

  subroutine write_gold_dof_info( grid, soln, crow, iu, gold_dof,              &
                                  set, njac, base_res, base_turbres )

    use info_depr,         only : tightly_couple, twod
    use turb_sa_2012,      only : debug_vt, constant_rnu_source
    use bc_names,          only : bc_name_index, element_based_bc,             &
                                  bc_has_visc_flux_closure, bc_ignore_2d,      &
                                  bc_null
    use inviscid_flux,     only : flux_construction, flux_construction_lhs,    &
                                  mean_decouple
    use twod_util,         only : q_2d
    use stability_defs,    only : r00
    use generic_gas_map,   only : n_species, n_momx
    use nml_boundary_conditions,  only : wall_temp_flag, wall_temperature

    integer, intent(in) :: iu, gold_dof, set, njac

    type(grid_type), intent(in) :: grid
    type(soln_type), intent(in) :: soln
    type(crow_flow), intent(in) :: crow

    real(dp), dimension(:,:), intent(in) :: base_res, base_turbres

    real(dp), dimension(soln%n_q) :: q_gold

    integer :: j, n_q, n_turb, n_mf, ib, eq, ibc, node, cell1, n

    character(len=80) :: bc_name

  continue

    n_q    = soln%n_q
    n_turb = soln%n_turb
    n_mf   = n_q - n_turb

    write(iu,*)
    write(iu,*) 'Detail Jacobian information can be selected via namelist:'
    write(iu,*) '&debug'
    write(iu,*) ' debug_q      = .true.'
    write(iu,*) ' debug_q_loc  = <dof_to_select>'
    write(iu,*) ' debug_q_proc = <lmpi_id_to_select>'
    write(iu,*) '/'
    write(iu,*)
    write(iu,"(1x,2(a,i0))") ' Initial residual at dof = ',gold_dof,&
                             ' of ',soln%dofg
    write(iu,"(1x,2(a,i0))") '                 lmpi_id = ',lmpi_id
    if ( grid%cc ) then
      write(iu,"(1x,a,i0)")  '         with global dof = ',&
      grid%cl2g(gold_dof)
    else
      write(iu,"(1x,a,i0)")  '         with global dof = ',&
      grid%l2g(gold_dof)
    endif
    write(iu,"(1x,a,i0)")    '         with matrix dof = ',&
      crow%g2m(gold_dof)
    write(iu,*)
    write(iu,"(1x,a,i5)")      ' .....igrid=',grid%igrid
    write(iu,"(1x,a,i5)")      ' ....origin=',grid%origin
    write(iu,*)
    write(iu,"(1x,a,i5)")      ' .......n_q=',n_q
    write(iu,"(1x,a,i5)")      ' ....n_turb=',n_turb
    write(iu,"(1x,a,i5)")      ' ......n_mf=',n_mf
    write(iu,"(1x,a,L5)")      ' ......q_2d=',q_2d
    write(iu,*)
    write(iu,"(1x,a,3f20.10)") ' ........xq=',real(grid%xq(      gold_dof),dp)
    write(iu,"(1x,a,3f20.10)") ' ........yq=',real(grid%yq(      gold_dof),dp)
    write(iu,"(1x,a,3f20.10)") ' ........zq=',real(grid%zq(      gold_dof),dp)
    write(iu,"(1x,a,3f20.10)") ' ......slen=',real(grid%slen(    gold_dof),dp)
    if ( .not.grid%cc ) &
    write(iu,"(1x,a,i20)")     ' ..symmetry=',grid%symmetry(gold_dof)
    write(iu,*)
    write(iu,*) ' Boundary conditions invoked:'
    write(iu,*)
    write(iu,"(11x,3x,a,3x,a,2x,a,2x,a,2x,a)") 'ib','ibc',&
    'bc_name'
    if ( .not.grid%cc ) then
      do ib = 1, grid%nbound

        ibc = grid%bc(ib)%ibc
        if ( twod .and. bc_ignore_2d(ibc) ) cycle
        do j = 1, grid%bc(ib)%nbnode
          node = grid%bc(ib)%ibnode(j)
          if ( node /= gold_dof ) cycle

          call bc_name_index(ibc,bc_name,.true.)
          write(iu,"(11x,i5,i6,2x,a)") ib, ibc, trim(bc_name)

        enddo
      enddo

      write(iu,*)
      write(iu,"(11x,3x,a,3x,a,2x,a,2x,a)") 'ib','ibc',&
      'element_based',                                 &
      'visc_flux_closure'
      !12345678901234567890
      do ib = 1, grid%nbound

        ibc = grid%bc(ib)%ibc
        if ( twod .and. bc_ignore_2d(ibc) ) cycle

        do j = 1, grid%bc(ib)%nbnode
          node = grid%bc(ib)%ibnode(j)
          if ( node /= gold_dof ) cycle

          write(iu,"(11x,i5,i6,L15,L19)") ib, ibc, &
          element_based_bc(ibc),                   &
          bc_has_visc_flux_closure(ibc)

        enddo
      enddo

      write(iu,*)
      write(iu,"(11x,3x,a,3x,a,2x,a,2x,a)") 'ib','ibc',&
      'wall_temp_flag',                                &
      'wall_temperature'
      !12345678901234567890
      do ib = 1, grid%nbound

        ibc = grid%bc(ib)%ibc
        if ( twod .and. bc_ignore_2d(ibc) ) cycle

        do j = 1, grid%bc(ib)%nbnode
          node = grid%bc(ib)%ibnode(j)
          if ( node /= gold_dof ) cycle

          if ( wall_temp_flag(ib) ) then
            write(iu,"(11x,i5,i6,L16,e18.5,a)") ib, ibc,     &
            wall_temp_flag(ib),real(wall_temperature(ib),dp),&
            ' (adiabatic wall temperature)'
          else
            write(iu,"(11x,i5,i6,L16,e18.5)") ib, ibc,     &
            wall_temp_flag(ib),real(wall_temperature(ib),dp)
          endif

        enddo
      enddo

      write(iu,*)
      write(iu,"(1x,a,9i2)") '            debug_vt=',&
                                          debug_vt
      write(iu,"(1x,a,L1)")  ' constant_rnu_source=',&
                               constant_rnu_source

    else

      do n = 1, grid%bcc%n_faces0

        ibc   = grid%bcc%ibc(n) ; if ( ibc == bc_null ) cycle
        cell1 = grid%bcc%cell(n)

        if ( cell1 /= gold_dof ) cycle

        ib    = grid%bcc%ibf(n)

        call bc_name_index(ibc,bc_name,.true.)
        write(iu,"(11x,i5,i6,2x,a)") ib, ibc, trim(bc_name)

     enddo

    endif

    write(iu,*)
    write(iu,'(1x,2a)')    "     flux_construction=",&
                            trim(flux_construction)
    write(iu,'(1x,2a)')    " flux_construction_lhs=",&
                          trim(flux_construction_lhs)

    write(iu,*)
    write(iu,"(1x,8x,a,15x,a,12x,a)") 'eq','q_dof','residual'

    if(mean_decouple) then
      q_gold(1)     = sum(soln%q_dof(1:n_species,gold_dof))
      q_gold(2:n_q) = soln%q_dof(n_momx:n_q,gold_dof)
    else
      q_gold(1:n_q) = soln%q_dof(1:n_q,gold_dof)
    end if

    if ( tightly_couple ) then
      eq = 0
      do j=1,soln%n_q-soln%n_turb
        eq = eq+1
        write(iu,"(1x,i10,3e20.8)") eq,real(q_gold(j),dp),              &
                                       real(  base_res(eq,gold_dof),dp)
      enddo
      do j=1,soln%n_turb
        eq = eq + 1
        if ( grid%cc ) then
        write(iu,"(1x,i10,3e20.8)") eq,real(soln%q_dof(eq,gold_dof),dp),&
                                       real(  base_res(eq,gold_dof),dp)
        else
        write(iu,"(1x,i10,3e20.8)") eq,real(soln%turb( j,gold_dof),dp),&
                                       real( base_res(eq,gold_dof),dp)
        endif
      enddo
    else
      eq = 0
      do j=1,soln%n_q-soln%n_turb
        eq = eq+1
        write(iu,"(1x,i10,3e20.8)") eq,real(q_gold(j),dp),             &
                                       real(  base_res(eq,gold_dof),dp)
      enddo
      do j=1,soln%n_turb
        eq = eq + 1
        if ( grid%cc ) then
        write(iu,"(1x,i10,3e20.8)") eq,real(soln%q_dof(eq,gold_dof),dp),&
                                     real( base_turbres(j,gold_dof),dp)
        else
        write(iu,"(1x,i10,3e20.8)") eq,real(soln%turb( j,gold_dof),dp),&
                                    real( base_turbres(j,gold_dof),dp)
        endif
      enddo
    endif
    write(iu,*)

    write(iu,"(1x,6x,a,i5,5x,a,i5)") 'set=',set,'njac=',njac
    write(iu,*)
    write(iu,"(1x,8x,a,17x,a,12x)") 'eq','r00'
    do j=1,njac
      write(iu,"(1x,i10,3e20.8)") j,real(r00(j,gold_dof),dp)
    enddo
    write(iu,*)

  end subroutine write_gold_dof_info

!============================== FRECHET_SETUP ================================80
!
! Initialization for Frechet derivatives.
!
!=============================================================================80

  subroutine frechet_setup( grid, soln, crow, mass, reset_q )

    use nml_global,          only : irest
    use info_depr,           only : ntt, twod, tightly_couple
    use debug_jacobian,      only : edge_terms_only
    use fill_jacobians,      only : fill_jacobian
    use fill_jacobians_gen,  only : fill_jacobian_gen
    use fill_jacobians_cc,   only : fill_jacobian_cc
    use flux_turb,           only : resid_jac_turb
    use flux_turb_cc,        only : resid_jac_turb_cc
    use fun3d_res_flow,      only : res_flow
    use flux_turb,           only : turbulent_residual
    use flux_turb_cc,        only : turbulent_residual_cc
    use force_types,         only : zero_force
    use debug_defs,          only : ntt_for_jacobian_check,                    &
                                  composite_jacobian_rhs,                      &
                                  boundary_closure_viscous,                    &
                                  boundary_closure_inviscid,                   &
                                  boundary_closure_strong, allow_dt, debug_dc
    use inviscid_flux,     only : first_order_iterations, iflim,               &
                                  flux_construction, flux_construction_lhs,    &
                                  gen_use_perf_jac, mean_decouple, dc_part
    use turb_sa_2012,      only : debug_vt, constant_rnu_source
    use lmpi_app,          only : lmpi_xfer
    use stability_defs,    only : q00, pjac00, H00
    use generic_gas_map,   only : n_pjac, n_mom, n_species, n_energy
    use solution_types,    only : generic_gas

    type(grid_type),               intent(inout) :: grid
    type(soln_type),               intent(inout) :: soln
    type(crow_flow),               intent(inout) :: crow
    type(mass_type), dimension(:), intent(in)    :: mass
    logical,                       intent(out)   :: reset_q

    integer :: dof, t, n_mf

  continue

    ! Control what we are checking.

    boundary_closure_viscous  = .true.
    boundary_closure_inviscid = .true.
    boundary_closure_strong   = .true.

    !...source terms for S-A model:
    debug_vt(:) = 0
    constant_rnu_source = .false.

    !...parameters here associated with inviscid jacobians.
    iflim = 0
    if ( flux_construction == 'roe' ) then
      flux_construction_lhs = flux_construction
    endif

    allow_dt = .false.  !turn off time term

    first_order_iterations = 1000
    if ( composite_jacobian_rhs ) first_order_iterations = 0

    !   Full viscous or edge_terms_only Jacobians.

    edge_terms_only = .true.
    if ( .not.grid%cc ) then
      edge_terms_only = .false.
    elseif ( soln%viscous_method == soln%viscous_method_lhs ) then
      edge_terms_only = .false.
    endif

    !   set up some tolerances, etc.

    call set_tol()

    call zero_force(soln%totforce(ntt))

    !   initialize rhs, lhs, qnode, and gradx/y/z to zero

    soln%res         = zero
    soln%a_diag      = zero
    soln%a_off       = zero
    soln%a_turb_diag = zero
    soln%a_turb_off  = zero
    soln%amut        = zero
    soln%gradx       = zero
    soln%grady       = zero
    soln%gradz       = zero

    ! Initialize conserved q variables to a nominal state.

    if ( irest == 0 .and. ntt_for_jacobian_check < 1 ) then
      reset_q = .true.
      if ( ic_exact ) then
        call initialize_q_frechet( grid, soln )
      else
        !...even the off-solve-plane data for twod.
        call initcv(soln%eqn_set, soln%q_dof, soln%neq0, &
                    soln%n_tot, soln%n_q, soln%n_turb,   &
                    soln%pressure_jac, soln%enthalpy_ij )
        if ( (soln%n_turb > 0) .and. (.not.grid%cc) )              &
        call init_turb(soln%turb, soln%amut, soln%neq0, soln%n_turb)
      endif

      ! Set the off-solve-plane twod data for safety.

      if ( twod ) then
        do dof=1,soln%dof0
          soln%q_dof(:,dof+soln%dof0) = soln%q_dof(:,dof)
          if ( soln%n_turb == 0 ) cycle
          soln%turb(:,dof+soln%dof0) = soln%turb(:,dof)
        enddo
      endif
    else
      reset_q = .false.
    endif

    ! Residuals of mean flow.

    if (debug_dc) then
      dc_part = .false.
      call res_flow(grid, soln, crow, mass)
      dc_part = .true.
      call res_flow(grid, soln, crow, mass)
      dc_part = .false.
    else
      call res_flow(grid, soln, crow, mass)
    end if

    ! Residuals of turbulence.

    if ( soln%n_turb > 0 .and. tightly_couple ) then
      if( grid%cc ) then
        call turbulent_residual_cc(grid, soln, crow)
      else
        call turbulent_residual(grid, soln, crow)
      endif
    endif

    ! Jacobians of mean flow (and tightly_coupled turbulence).

    if ( grid%cc ) then
      call fill_jacobian_cc(grid, soln, crow)
    else
      if(mean_decouple .and. soln%eqn_set == 2) then
        if (debug_dc) dc_part = .true.
        call fill_jacobian(grid, soln, crow)
        if (debug_dc) dc_part = .false.
      else if( .not. gen_use_perf_jac .and. soln%eqn_set == 2) then
        call fill_jacobian_gen(grid, soln, crow)
      else if( gen_use_perf_jac .and. soln%eqn_set == 2) then
        call fill_jacobian(grid, soln, crow)
      else
        call fill_jacobian(grid, soln, crow)
      end if
    endif

    if ( .not.tightly_couple .and. soln%n_turb > 0 ) then

      ! Residuals and jacobians for turbulence.

      if ( grid%cc ) then
        call resid_jac_turb_cc(grid, soln, crow)
      else
        call resid_jac_turb(grid, soln, crow)
      endif

    endif

    call lmpi_xfer( soln%res )
    if ( soln%n_turb > 0 .and. .not. tightly_couple ) &
    call lmpi_xfer( soln%turbres )

    if ( soln%eqn_set == generic_gas ) then
      allocate( q00( soln%n_tot, size(soln%q_dof,2) ) ) !first Q
      allocate( pjac00( n_pjac, n_mom, size(soln%pressure_jac,3)) )
      allocate( H00( n_species, n_energy, size(soln%enthalpy_ij,3)) )
    else
      allocate( q00( soln%n_q, size(soln%q_dof,2) ) ) !first Q
    end if

    if ( grid%cc ) then
      q00( 1:soln%n_q, : ) = soln%q_dof( 1:soln%n_q, : )
    else
      n_mf = size(soln%q_dof,1)
      q00( 1:n_mf, : ) = soln%q_dof( 1:n_mf, : )
      if ( soln%eqn_set == generic_gas ) then
        pjac00 = soln%pressure_jac
        H00    = soln%enthalpy_ij
      end if
      do t=1,soln%n_turb
        q00( n_mf + t, : ) = soln%turb( t, : )
      enddo
    endif

  end subroutine frechet_setup

!============================== CHECK_FRECHET ================================80
!
! Check Frechet derivatives.
!
!=============================================================================80

  subroutine check_frechet( grid, soln, crow, ip, max_adj_lhs, njac,          &
                            gfile1, gfile2, gold_dof, jacobian_type )

    use stability_defs,     only : d_lhs, a_lhs, d_rhs, a_rhs, skip_jac

    type(grid_type),               intent(in)    :: grid
    type(soln_type),               intent(inout) :: soln
    type(crow_flow),               intent(inout) :: crow

    integer, intent(in)  :: ip, max_adj_lhs, njac, gfile1, gfile2, gold_dof

    character(len=*), intent(in) :: jacobian_type

    integer :: i, dof, off_index, off_index_simple, k, maxid

    real(dp), dimension(njac,njac)   :: diag_sum, off_sum
    real(dp), dimension(njac,njac,3) :: diag_max, off_max

    character(len=80) :: string

    integer, dimension(:), allocatable :: je_list, dof_list

    real(dp), dimension(:,:),  allocatable :: error_d
    real(dp), dimension(:,:,:),allocatable :: error_a

    real(dp), dimension(:), allocatable :: max_error_a
    real(dp), dimension(:), allocatable :: fd_ref_off
    real(dp), dimension(:), allocatable :: an_ref_off

    logical :: summary_only = .false.

    real(dp) :: max_error_d
    real(dp) :: max_error_d_intjac
    real(dp) :: max_error_a_intjac
    real(dp) :: fd_ref_diag
    real(dp) :: an_ref_diag
    real(dp) :: fd_ref_diag_intjac
    real(dp) :: an_ref_diag_intjac
    real(dp) :: fd_ref_off_intjac
    real(dp) :: an_ref_off_intjac, temp

    integer,                    save :: diag_entries, off_entries
    real(dp), dimension(7),     save :: g
    real(dp), dimension(7,7),   save :: dm, om, xm
    real(dp), dimension(7,7,3), save :: dmx, omx

    integer,  dimension(5) :: off_max_loc, diag_max_loc

  continue

    diag_sum = zero ; off_sum = zero
    diag_max = zero ; off_max = zero

    if ( ip == 0 ) then
      dm = zero ; om = zero ; dmx = zero ; omx = zero ; g = zero
      diag_entries = 0 ; off_entries = 0
      off_max_loc(1) = -1 ; diag_max_loc(1) = -1
    endif

    if ( njac > size(dm,1) ) &
    call lmpi_conditional_stop(1,'size issue:check_frechet')

    if ( soln%dofg > 125 ) summary_only = .true.

    ! initialize error maxes and reference values

    max_error_a_intjac = -huge(1._dp)
    max_error_d_intjac = -huge(1._dp)

    fd_ref_diag_intjac = zero
    fd_ref_off_intjac  = zero

    an_ref_diag_intjac = zero
    an_ref_off_intjac  = zero

    if ( ip == lmpi_id ) then

      ! allocate temporary arrays.
      allocate(max_error_a(max_adj_lhs))
      allocate( fd_ref_off(max_adj_lhs))
      allocate( an_ref_off(max_adj_lhs))
      allocate(    je_list(max_adj_lhs))
      allocate(   dof_list(max_adj_lhs))
      allocate(    error_d(njac,njac))             !...local error diag
      allocate(    error_a(njac,njac,max_adj_lhs)) !...local error a_off

      ! initialize error maxes and reference values

      max_error_a = zero
      max_error_d = zero

      fd_ref_diag = zero
      fd_ref_off  = zero

      an_ref_diag = zero
      an_ref_off  = zero

      do dof = 1, soln%dof0

        if ( skip_jac(dof) ) cycle

        !Set local copies of block matrices for checking.

        off_index = 0
        off_index_simple = crow%ia_ns(dof) - crow%ia(dof)
        do k = crow%ia(dof), crow%ia(dof+1)-1

          off_index             = off_index + 1
          je_list( off_index)   = k
          dof_list(off_index)   = crow%ja(k)

        end do

        ! differences between jacobians.
        ! store max error and corrresponding jacobians

        call get_error_d( dof, ip, njac, soln%dof0,                     &
                        d_lhs, d_rhs, error_d, max_error_d,             &
                        diag_sum, diag_entries, diag_max, diag_max_loc, &
                        fd_ref_diag, an_ref_diag,                       &
                        max_error_d_intjac,an_ref_diag_intjac,          &
                        fd_ref_diag_intjac, dof)

        call get_error_a( dof, ip, njac, crow%nnz0, max_adj_lhs,         &
                        je_list, dof_list,                               &
                        a_lhs, a_rhs, error_a, max_error_a,              &
                        off_sum, off_entries, off_max, off_max_loc,      &
                        fd_ref_off, an_ref_off,                          &
                        max_error_a_intjac, an_ref_off_intjac,           &
                        fd_ref_off_intjac, off_index)

        if (.not. summary_only) then

          ! detailed comparisons for diagonal jacobians at this dof

          string = 'diagonal '//trim(jacobian_type)
          call info_diag(6, string, dof, soln%dof0, njac, d_lhs, d_rhs, &
                       error_d,max_error_d,an_ref_diag,fd_ref_diag )

          ! detailed comparisons for off-diagonal jacobians at this dof

          string = 'off-diagonal '//trim(jacobian_type)
          call info_off(6,string, dof, soln%dof0, crow%nnz0, njac,      &
                      a_lhs, a_rhs,                                     &
                      error_a, max_error_a, an_ref_off, fd_ref_off,     &
                      max_adj_lhs, je_list, dof_list, off_index,        &
                      off_index_simple )

        endif

        if ( dof == gold_dof ) then

          string = 'diagonal '//trim(jacobian_type)
          call info_diag( gfile2,                                       &
                          string, dof, soln%dof0, njac, d_lhs, d_rhs,   &
                          error_d,max_error_d,an_ref_diag,fd_ref_diag )
          string = 'off-diagonal '//trim(jacobian_type)
          call info_off( gfile2, string, dof, soln%dof0, crow%nnz0,     &
                         njac, a_lhs, a_rhs,                            &
                         error_a, max_error_a, an_ref_off, fd_ref_off,  &
                         max_adj_lhs, je_list, dof_list, off_index,     &
                         off_index_simple )
        end if

        if ( summary_only .and. dof == gold_dof ) then

          string = 'diagonal '//trim(jacobian_type)
          call info_diag(         6,                                    &
                          string, dof, soln%dof0, njac, d_lhs, d_rhs,   &
                          error_d,max_error_d,an_ref_diag,fd_ref_diag )
          string = 'off-diagonal '//trim(jacobian_type)
          call info_off( 6, string, dof, soln%dof0, crow%nnz0,          &
                         njac, a_lhs, a_rhs,                            &
                         error_a, max_error_a, an_ref_off, fd_ref_off,  &
                         max_adj_lhs, je_list, dof_list, off_index,     &
                         off_index_simple )
        end if

      end do

      g(1) = max_error_d_intjac
      g(2) = an_ref_diag_intjac
      g(3) = fd_ref_diag_intjac

      g(4) = max_error_a_intjac
      g(5) =  an_ref_off_intjac
      g(6) =  fd_ref_off_intjac

      dm(1:njac,1:njac) = diag_sum(1:njac,1:njac)
      om(1:njac,1:njac) =  off_sum(1:njac,1:njac)

      dmx(1:njac,1:njac,1:3) = diag_max(1:njac,1:njac,1:3)
      omx(1:njac,1:njac,1:3) =  off_max(1:njac,1:njac,1:3)

      ! deallocate work arrays,
      deallocate(max_error_a)
      deallocate( fd_ref_off)
      deallocate( an_ref_off)
      deallocate(    je_list) ; deallocate(dof_list)
      deallocate(    error_d) ; deallocate( error_a)

    endif

    if ( ip == lmpi_nproc-1 ) then
      if ( grid%cc ) then
        diag_max_loc(4) = grid%cl2g(diag_max_loc(2))
        if ( diag_max_loc(3) > 0 )                &
        diag_max_loc(5) = grid%cl2g(diag_max_loc(3))
         off_max_loc(4) = grid%cl2g( off_max_loc(2))
        if ( off_max_loc(3) > 0 )                &
         off_max_loc(5) = grid%cl2g( off_max_loc(3))
      else
        diag_max_loc(4) = grid%l2g(diag_max_loc(2))
        if ( diag_max_loc(3) > 0 )                &
        diag_max_loc(5) = grid%l2g(diag_max_loc(3))
         off_max_loc(4) = grid%l2g( off_max_loc(2))
        if ( off_max_loc(3) > 0 )                &
         off_max_loc(5) = grid%l2g( off_max_loc(3))
      endif
      temp = abs(g(1))
      call lmpi_max_and_maxid( real(temp,dp), maxid )
      call lmpi_bcast(       g(1:3), maxid )
      call lmpi_bcast(          dmx, maxid )
      call lmpi_bcast( diag_max_loc, maxid)

      temp = abs(g(4))
      call lmpi_max_and_maxid( real(temp,dp), maxid )
      call lmpi_bcast(      g(4:6), maxid )
      call lmpi_bcast(         omx, maxid )
      call lmpi_bcast( off_max_loc, maxid)

      i = diag_entries ; call lmpi_reduce(i,diag_entries)
      i =  off_entries ; call lmpi_reduce(i, off_entries )

      xm(:,:) = dm(:,:) ; call lmpi_reduce( xm, dm )
      xm(:,:) = om(:,:) ; call lmpi_reduce( xm, om )

      i = diag_likely_n ; call lmpi_reduce(i,diag_likely_n)
      temp = diag_likely_max
      call lmpi_max_and_maxid( real(temp,dp), maxid )
      call lmpi_bcast( diag_likely_max,  maxid )
      call lmpi_bcast( diag_likely_abs,  maxid )
      call lmpi_bcast( diag_likely_proc, maxid )
      call lmpi_bcast( diag_likely_dof,  maxid )
      call lmpi_bcast( diag_likely_i,    maxid )
      call lmpi_bcast( diag_likely_j,    maxid )

      i = aoff_likely_n ; call lmpi_reduce(i,aoff_likely_n)
      temp = aoff_likely_max
      call lmpi_max_and_maxid( real(temp,dp), maxid )
      call lmpi_bcast( aoff_likely_max,  maxid )
      call lmpi_bcast( aoff_likely_abs,  maxid )
      call lmpi_bcast( aoff_likely_proc, maxid )
      call lmpi_bcast( aoff_likely_dof,  maxid )
      call lmpi_bcast( aoff_likely_off,  maxid )
      call lmpi_bcast( aoff_likely_i,    maxid )
      call lmpi_bcast( aoff_likely_j,    maxid )
    endif

    if ( ip == lmpi_nproc-1 .and. lmpi_master ) then

      diag_sum(1:njac,1:njac) = dm( 1:njac,1:njac)
       off_sum(1:njac,1:njac) = om( 1:njac,1:njac)
      diag_max(1:njac,1:njac,1:3) = dmx(1:njac,1:njac,1:3)
       off_max(1:njac,1:njac,1:3) = omx(1:njac,1:njac,1:3)

      ! summary of max diagonal jacobian error over all dofs

      string = trim(jacobian_type)
      call info_summary_diag( gfile1, string, g(1), g(2), g(3) )

      call mxnorm( gfile1, njac, diag_max, diag_entries, diag_max_loc,    &
               diag_likely_n, diag_likely_proc,                           &
               diag_likely_dof, diag_likely_dof,                          &
               diag_likely_i, diag_likely_j, diag_likely_max,             &
               diag_likely_abs )

      diag_sum(:,:) = diag_sum(:,:)/real(diag_entries,dp)

      call mnorm( gfile1, diag_sum, diag_entries, 0 )

      ! summary of max off-diagonal jacobian error over all dofs

      call info_summary_off(  gfile1, g(4), g(5), g(6) )

      call mxnorm( gfile1, njac, off_max, off_entries, off_max_loc,    &
               aoff_likely_n, aoff_likely_proc,                        &
               aoff_likely_dof, aoff_likely_off,                       &
               aoff_likely_i, aoff_likely_j, aoff_likely_max,          &
               aoff_likely_abs )

      off_sum(:,:) = off_sum(:,:)/real(off_entries,dp)

      call mnorm( gfile1, off_sum, off_entries, 1 )

      write(*,*) ! write blank line for aesthetics

    endif

  end subroutine check_frechet

!============================== CORRUPT_JACOBIAN =============================80
!
! Purposely corrupt Jacobian.
!
!=============================================================================80

  subroutine corrupt_jacobian(grid, soln, crow, gfile1 )

    integer,         intent(in)    :: gfile1
    type(grid_type), intent(in)    :: grid
    type(soln_type), intent(inout) :: soln
    type(crow_flow), intent(in)    :: crow

    integer :: ioff, k, dof, dofm, dofg, dofg_off, k_p, offg_p

  continue

  ! Diagonal.

  do dof=1,grid%dof0
    if ( grid%cc ) then
      dofg = grid%cl2g(dof)
    else
      dofg = grid%l2g(dof)
    endif
    if ( dofg /= 3 ) cycle

    soln%a_diag(1,1,crow%g2m(dof)) = -10000._dp ; exit

  enddo

  if ( lmpi_master ) write(gfile1,*) &
  ' Corrupting Diagonal(1,1) at global dof=',3

  ! Off-diagonal.

  offg_p = 0
  k_p    = 0
  do dof=1,grid%dof0

    if ( grid%cc ) then
      dofg = grid%cl2g(dof)
    else
      dofg = grid%l2g(dof)
    endif
    if ( dofg /= 5 ) cycle

    dofm = crow%g2m(dof)

    do k=crow%iam(dofm),crow%iam(dofm+1)-1
      ioff = crow%m2g( crow%jam(k) )
      if ( grid%cc ) then
        dofg_off = grid%cl2g(ioff)
      else
        dofg_off = grid%l2g(ioff)
      endif
      if ( k == crow%iam(dofm) ) then
        k_p = k ; offg_p = dofg_off
      endif
      if ( lmpi_master ) &
      write(gfile1,*) '                           ...dof_off=',dofg_off
      if ( dofg_off /= 4 ) cycle
      k_p = k ; offg_p = dofg_off
      exit
    enddo

    soln%a_off(2,1,k_p) = +10000._dp ; exit

  enddo

  k = offg_p ; call lmpi_reduce(k, offg_p)

  if ( lmpi_master ) then
    write(gfile1,*) ' Corrupting Off-Diagonal(2,1) at global dof=',5
    write(gfile1,*) '                            from global dof=',offg_p
  endif

end subroutine corrupt_jacobian

!================================ WRITE_VALUES ===============================80
!
! Write values in a jacobian row only if above tolerance.
!
!=============================================================================80

  subroutine write_values( f, error, type )

    integer, intent(in) :: f, type

    real(dp), dimension(:,:), intent(in) :: error

    integer  :: i, j, njac

    logical :: check_row_below_tolerance

    real(dp) :: tolerance, dq_tolerance

    character(len=16), dimension( size(error,1) ) :: error_text

    continue

    njac = size( error, 1 )

    tolerance = 100._dp*real_epsilon
    if ( complex_mode ) tolerance = 100._dp*complex_epsilon

    dq_tolerance = epsilon(1._odp)
    if ( type == 1 ) then
      tolerance = max( tolerance, dq_tolerance )
    endif

    do i=1,njac

      check_row_below_tolerance = .true.
      do j=1,njac
        if ( abs( real(error(i,j),dp) ) > &
             abs( real( tolerance,dp) )   ) then
          check_row_below_tolerance = .false.
          write(error_text(j),"(3x,e13.5)") real(error(i,j),dp)
        else
          write(error_text(j),"(5x,a)") '< tolerance'
        endif
      enddo

      if ( check_row_below_tolerance ) then
        write(f,'(3x,a,e13.5)') '=> all values in row < ',real(tolerance,dp)
      else
        write(f,'(10(a16))') (error_text(j),j=1,njac)
      endif
    end do

  end subroutine write_values

end module composite_jacobian_util

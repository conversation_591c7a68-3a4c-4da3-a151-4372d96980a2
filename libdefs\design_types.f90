module design_types

  use kinddefs, only : dp

  implicit none

  private

  public :: design_type, body_type, function_type, component_type, sleep_delay
  public :: command_type, opt_data_type, model_variable_type, auxiliary_type
  public :: max_string_length, cpstar_id, rigid_type, design_run
  public :: setup_design, design_dirs, cpstar_data, cpstar_slices, max_functions
  public :: ks_rho, ks_function

  integer, parameter :: max_string_length = 5000
  integer, parameter :: max_functions = 50 ! Max number of cost fcns/constraints

  integer :: sleep_delay   = 30     ! Wait between codes for
                                    ! file system to catch up
  integer :: design_dirs   = 1      ! # of directories/files for design
  integer :: cpstar_slices = 0      ! How many cpstar slices

  real(dp) :: ks_rho       = 100._dp ! Constant for KS function

  logical :: cpstar_data  = .false. ! inverse design target
  logical :: setup_design = .false. ! Set up directories/files for design
  logical :: design_run   = .false. ! In the middle of a design?
  logical :: ks_function  = .false. ! Apply KS function to optimization

  character(len=*), parameter :: cpstar_id = 'cpstar'

  type auxiliary_type
    logical :: active
    type(body_type), dimension(:), pointer :: body_data
  end type auxiliary_type

  type design_type
    integer :: nbodies
    integer :: nfunctions

    real(dp) :: mach_value
    real(dp) :: mach_lower
    real(dp) :: mach_upper
    real(dp) :: alpha_value
    real(dp) :: alpha_lower
    real(dp) :: alpha_upper
    real(dp) :: yaw_value
    real(dp) :: yaw_lower
    real(dp) :: yaw_upper
    real(dp) :: xrate_value
    real(dp) :: xrate_lower
    real(dp) :: xrate_upper
    real(dp) :: yrate_value
    real(dp) :: yrate_lower
    real(dp) :: yrate_upper
    real(dp) :: zrate_value
    real(dp) :: zrate_lower
    real(dp) :: zrate_upper

    real(dp), dimension(:), pointer :: mach_derivative
    real(dp), dimension(:), pointer :: alpha_derivative
    real(dp), dimension(:), pointer :: yaw_derivative
    real(dp), dimension(:), pointer :: xrate_derivative
    real(dp), dimension(:), pointer :: yrate_derivative
    real(dp), dimension(:), pointer :: zrate_derivative

    logical :: mach_active
    logical :: alpha_active
    logical :: yaw_active
    logical :: xrate_active
    logical :: yrate_active
    logical :: zrate_active
    logical :: shape_active
    logical :: rigid_active
    logical :: auxiliary_data_present
    logical :: kinematic_data_present
    logical :: trimming_data_present

    type(auxiliary_type) :: auxiliary
    type(auxiliary_type) :: kinematic
    type(auxiliary_type) :: trimming

    type(body_type),     dimension(:), pointer :: body_data
    type(rigid_type),    dimension(:), pointer :: rigid_data
    type(function_type), dimension(:), pointer :: function_data
  end type design_type

  type body_type
    integer :: ndv
    integer :: parameterization !Massoud=1 bandaid=2 jets=3 sculptor=4 userdef=5

    character(len=80) :: body_text_label
    character(len=80) :: body_name

    real(dp), dimension(:),   pointer    :: value
    real(dp), dimension(:),   pointer    :: lower_bound
    real(dp), dimension(:),   pointer    :: upper_bound
    real(dp), dimension(:,:), pointer    :: shape_derivative

    integer, dimension(:), pointer :: active
  end type body_type


  type rigid_type
    integer :: ndv

    character(len=80) :: body_text_label
    character(len=80) :: rigid_name

    real(dp), dimension(:),   pointer    :: value
    real(dp), dimension(:),   pointer    :: lower_bound
    real(dp), dimension(:),   pointer    :: upper_bound
    real(dp), dimension(:,:), pointer    :: shape_derivative

    logical, dimension(:), pointer :: active

    character(len=7), dimension(:), pointer :: name
  end type rigid_type


  type function_type
    integer :: ncomponents
    integer :: fcn_type

    integer, dimension(:), pointer :: timesteps

    real(dp) :: lower_bound
    real(dp) :: upper_bound
    real(dp) :: value
    real(dp) :: weight
    real(dp) :: target
    real(dp) :: power

    logical :: averaging

    type(component_type), dimension(:), pointer :: component_data
  end type function_type


  type component_type
    real(dp)          :: value
    real(dp)          :: weight
    real(dp)          :: target
    real(dp)          :: power
    integer           :: boundary_id
    character(len=10) :: name
  end type component_type


  type command_type
    integer :: ncommands

    character(len=80) :: code
    character(len=80), dimension(:), pointer :: command
  end type command_type


  type opt_data_type
    integer :: ndv
    integer :: nobjectives
    integer :: nconstraints
    integer :: nscale
    integer :: nuncoupled

    logical :: allocated

    real(dp), dimension(:),   pointer    :: design_variables
    real(dp), dimension(:),   pointer    :: lower_bounds
    real(dp), dimension(:),   pointer    :: upper_bounds
    real(dp), dimension(:),   pointer    :: objectives
    real(dp), dimension(:),   pointer    :: weights
    real(dp), dimension(:),   pointer    :: targets
    real(dp), dimension(:),   pointer    :: powers
    real(dp), dimension(:),   pointer    :: objective_lower_bound
    real(dp), dimension(:),   pointer    :: objective_upper_bound
    real(dp), dimension(:),   pointer    :: constraints
    real(dp), dimension(:),   pointer    :: constraint_lower_bound
    real(dp), dimension(:),   pointer    :: constraint_upper_bound
    real(dp), dimension(:),   pointer    :: scale
    real(dp), dimension(:,:), pointer    :: gradients
    real(dp), dimension(:,:), pointer    :: constraint_gradients

    logical,  dimension(:),   pointer    :: coupled
  end type opt_data_type


  type model_variable_type
    integer :: restart_flow  ! Restart the flow solver for the current model
    integer :: restart_dual  ! Restart the dual solver for the current model

    real(dp) :: weight       ! Weight for multipoint

    character(len=max_string_length) :: model_directory ! Dir where model
                                                        ! resides
    character(len=max_string_length) :: desc_directory  ! Dir where model desc
                                                        ! resides

    type(opt_data_type) :: opt_data                     ! Optimization data
                                                        ! for model
  end type model_variable_type

end module design_types

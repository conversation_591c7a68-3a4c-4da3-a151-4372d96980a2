!================================= DES_VELTERM ===============================80
!
! Velocity term needed for turbulence.
!
!=============================================================================80

  pure function des_velterm( ux, uy, uz, vx, vy, vz, wx, wy, wz )

    real(dp), intent(in) :: ux, uy, uz, vx, vy, vz, wx, wy, wz

    real(dp) :: des_velterm

  continue

    des_velterm = ux*ux + uy*uy + uz*uz + vx*vx + vy*vy + vz*vz +              &
                  wx*wx + wy*wy + wz*wz

  end function des_velterm

!================================= SUGGAR_MODULE =============================80
!
! DESCRIPTION:
! This module provides routines for either computing overset connectivity from
! within the flow solver, or reading it from precomputed dci files.
!
! SUGGAR was created by <PERSON> and can be obtained by
! requesting it from <NAME_EMAIL>
!
!=============================================================================80

module suggar

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use dirtlib,             only : dci_file
  use nml_overset_data,    only : dci_dir
  use nml_grid_motion,     only : input_xml_file

#ifdef HAVE_SUGGAR
  use kinddefs,     only : dp, system_i8
  use overset_defs, only : formatted_dci_file
  use suggar_info,  only : suggar_nproc, is_suggar, world_nproc, suggar_id_l2g
#endif
  use suggar_info,  only : is_fun3d, fun3d_id_l2g, world_comm

  implicit none

  private

  public :: overset_connectivity, skip_xml_element, find_name, xml_commands
  public :: init_libsuggar

! Data to allow tweaking of SUGGAR hole cutting

  type xml_commands
    integer                                   :: n_commands
    character(len=256), dimension(:), pointer :: parent
    character(len=256), dimension(:), pointer :: commands
  end type xml_commands

#ifdef HAVE_SUGGAR
  integer                                     :: n_xml
  character(len=80)                           :: xml_file = 'Input.xml_dynamic'
  character(len=1024), dimension(:), allocatable :: xml_strings
#endif

  contains

!================================ INIT_LIBSUGGAR =============================80
!
! Sets up libsuggar infrastructure
!
!=============================================================================80

  subroutine init_libsuggar()

    use lmpi,              only : lmpi_id, lmpi_die
    use suggar_info,       only : my_dc_group, is_fun3d, suggar_nproc
    use system_extensions, only : se_flush
    use nml_overset_data,  only : dci_on_the_fly
#ifdef HAVE_SUGGAR
    use suggar_info,       only : n_dc_groups

    integer :: ierr
    logical, parameter :: enable_trace = .false. ! log suggar/dirtlib calls
#endif

  continue

    if (dci_on_the_fly) then

!     create the desired number of dc groups

      if (is_fun3d) then
        my_dc_group = 0
      else
        my_dc_group = lmpi_id + 1
        if (my_dc_group > suggar_nproc) then
          write(*,*)'cannot have my_dc_group > suggar_nproc'
          write(*,*)'my_dc_group  = ', my_dc_group
          write(*,*)'suggar_nproc = ', suggar_nproc
          call se_flush()
          call lmpi_die
        end if
!       for now, only 1 dc group allowed; eventually will remove
        if (my_dc_group /= 1) then
          write(*,*)'error: my_dc_group = ',my_dc_group
          write(*,*)'only one dc_group allowed at this time'
          write(*,*)'likely caused by setting suggar_nproc > 1'
          call se_flush()
          call lmpi_die
        end if
      end if
#ifdef HAVE_SUGGAR
      call dcxf_create_dcx_groups(n_dc_groups, my_dc_group, ierr)
#endif

!     optionally allow tracing for debugging purposes

#ifdef HAVE_SUGGAR
      if (enable_trace) then
        call drtf_enable_trace_drt_calls()
        call dcf_enable_trace_dc_calls()
      end if
#endif

!     register the fun3d and suggar processors with dcf

#ifdef HAVE_SUGGAR
      if (is_suggar) then
        call drtf_rank_dci_only(ierr)
      else
        call drtf_rank_flow_only(ierr)
      end if
#endif

!     dcxf_set_dci_mrank_commwrld sets master suggar rank in comm_world

#ifdef HAVE_SUGGAR
      call dcxf_set_dci_mrank_commwrld(suggar_id_l2g(0),ierr)
#endif

    end if

  end subroutine init_libsuggar


!=========================== OVERSET_CONNECTIVITY ============================80
!
! Compute new connectivity (if needed) by either calling the appropriate
! libsuggar routines or calling an external suggar executable; or simply
! read in the connectivity if it is already available
!
!=============================================================================80

  subroutine overset_connectivity(grid)

    use grid_types,          only : grid_type
    use moving_body_types,   only : moving_body
    use nml_grid_motion,     only : n_moving_bodies
    use lmpi,                only : lmpi_master, lmpi_bcast, lmpi_die,         &
                                    have_multiple_comms
    use overset_defs,        only : dci
    use nml_overset_data,    only : dci_on_the_fly, reuse_existing_dci, dci_io
    use dirtlib,             only : update_iblank
#ifdef HAVE_SUGGAR
    use nml_overset_data,    only : skip_dci_output
    use suggar_info,         only : active_dc_group
    use timings,             only : time_moving_grid, timer
    use string_utils,        only : sub_string
    use file_utils,          only : available_unit
#endif
    use info_depr,           only : ntt
    use nml_global,          only : grid_motion_only, body_motion_only,   &
                                    grid_motion_and_dci_only
    use io,                  only : prior_iters
    use file_utils,          only : file_exists
    use system_extensions,   only : se_flush
    use dcif,                only : load_dcif_data

    type(grid_type),                                    intent(inout) :: grid

    character(len=80)  :: string

#ifdef HAVE_SUGGAR
    type(xml_commands), save :: input_xml_commands
    integer            :: body, num_hc_failure, num_donor_quality, iunit, nreal
    integer            :: ierr, body_arg, m
    integer(system_i8) :: mem_usage
    integer, parameter :: octree_update = 2 ! new octree freq. (deforming only)
    logical            :: suggar_needs_new_mesh, rebuild_octree
    logical            :: mesh_points_updated
    logical,      save :: new_xml_file       = .true.
    logical,      save :: octree_used        = .false.
    logical            :: test_send_new_mesh = .false. ! to test with rigid grid
    real(dp)           :: time0, time1, suggar_time
    real(dp)           :: user_time0, user_time1, suggar_user_time
    real(dp)           :: system_time0, system_time1, suggar_system_time
    real(dp)           :: wall_time0, wall_time1, suggar_wall_time
#endif
    integer            :: i

    logical            :: need_new_dci, already_have_dci

    logical, dimension(n_moving_bodies) :: fake_body

  continue

!   see if we can just skip all this

    if (body_motion_only) return
    if (grid_motion_only) then
      if (.not. grid_motion_and_dci_only) return
    end if

    if (is_fun3d) then
      call set_dci_counter(need_new_dci)
    end if

    if (have_multiple_comms) then
      call lmpi_bcast(need_new_dci,   fun3d_id_l2g(0), world_comm)
      call lmpi_bcast(dci,            fun3d_id_l2g(0), world_comm)
      call lmpi_bcast(grid%project,   fun3d_id_l2g(0), world_comm)
      call lmpi_bcast(ntt,            fun3d_id_l2g(0), world_comm)
      call lmpi_bcast(prior_iters,    fun3d_id_l2g(0), world_comm)
      call lmpi_bcast(input_xml_file, fun3d_id_l2g(0), world_comm)
    end if

!   set the dci and xml file names

    if (dci == 0) then
      dci_file = trim(dci_dir) // trim(grid%project) // '.dci'
    else
      write(string,*) dci
      dci_file = trim(dci_dir) // trim(grid%project) //                        &
                 trim(adjustl(string)) // '.dci'
    end if

! Identify any "fake" (duplicate) bodies used for specialized parent-child
! motion.  This assumes that all fake bodies are input prior to the real
! (youngest of the same name) body

    if (is_fun3d) then
      do i = 1, n_moving_bodies
        fake_body(i) = moving_body(i)%fake_body
      end do
    endif

#ifdef HAVE_SUGGAR
    if (dci_on_the_fly) then
      call lmpi_bcast(fake_body, fun3d_id_l2g(0), world_comm)
    endif
#else
    if ( .false. ) write(*,*) fake_body
#endif

    new_dci_needed : if (need_new_dci) then

      already_have_dci = .false.
      if (reuse_existing_dci) then
        if (file_exists(dci_file)) already_have_dci = .true.
      end if

      get_dci_data : if (dci_on_the_fly .and. (.not. already_have_dci)) then

        if (is_fun3d .and. lmpi_master) then
          write(*,'(/,2a)') ' Computing DCI data: ', trim(dci_file)
        end if

#ifdef HAVE_SUGGAR

!       select active dc group (currently only one allowed)

        active_dc_group = 1
        call dcxf_select_dcx_group(active_dc_group, ierr)
        if (ierr /= 0) then
          if (is_fun3d .and. lmpi_master) then
            write(*,*) 'error flag returned from dcxf_select_dcx_group'
            call se_flush()
          end if
          call lmpi_die
        end if

!       get the xml commands for suggar

        if (is_fun3d .and. lmpi_master) then

          ierr = 0

          if (new_xml_file) then

!           first step, grab all the xml commands from the xml file used to
!           set up the static composite grid (except those xml commands
!           relating to output), and write them to a new file

            call read_xml_strings(input_xml_commands%n_commands,               &
                                  input_xml_commands%parent,                   &
                                  input_xml_commands%commands,                 &
                                  trim(input_xml_file))

            iunit = available_unit()
            open(unit=iunit, file=xml_file, status="unknown")
            rewind(iunit)
            do m = 1, input_xml_commands%n_commands
              write(iunit,'(a)') trim(input_xml_commands%commands(m))
            end do
            close(iunit)

!           check for <use_octree_hole_cut/>

            do m = 1, input_xml_commands%n_commands
              if (trim(adjustl(input_xml_commands%commands(m))) ==             &
                                 '<use_octree_hole_cut/>') then
                octree_used = .true.
                exit
              end if
            end do

          end if

!         set up the xml commands related to motion

          call xml_strings_for_motion(input_xml_commands, fake_body, ierr)

        end if

        call lmpi_bcast(ierr, fun3d_id_l2g(0), world_comm)
        if (ierr /= 0) then
          call lmpi_die
        end if

        call lmpi_bcast(n_xml, fun3d_id_l2g(0), world_comm)
        call lmpi_bcast(octree_used, fun3d_id_l2g(0), world_comm)

        if (.not. allocated(xml_strings)) then
          allocate(xml_strings(n_xml))
        end if

        do m=1,n_xml
          call lmpi_bcast(xml_strings(m), fun3d_id_l2g(0), world_comm)
        end do

!       pass the xml commands to suggar

        suggar_only_1 : if (is_suggar) then

          call dcf_get_cpu_time(time0, user_time0, system_time0, ierr)
          call dcf_get_wall_time(wall_time0, ierr)

          if (new_xml_file) then
            call dcf_reopen_stdout("suggar_output" // char(0))
            call dcf_reopen_stderr("suggar_error" // char(0))
            call dcf_init_no_args(trim(xml_file) // char(0))
          end if

          call dcf_begin_motion_input()

          do m = 1,n_xml
            call dcf_add_motion_input(trim(xml_strings(m)) // char(0))
          end do

          call dcf_end_motion_input()
          call dcf_parse_motion()

        end if suggar_only_1

!       send new mesh-point coordinates to suggar if needed

        mesh_points_updated = .false.

        nreal = 0
        do body = 1, n_moving_bodies

          if ( .not.fake_body(body) ) nreal = nreal + 1

          if ( sub_string(moving_body(body)%mesh_movement,'deform') ) then
            suggar_needs_new_mesh = .true.
          else if (test_send_new_mesh) then
            suggar_needs_new_mesh = .true.
          else
            suggar_needs_new_mesh = .false.
          end if

! if the current body is deforming and therefore needs to hand suggar updated
! mesh coordinates, then look at the preceding body in the list.  If it is a
! fake body, assume that it has the actual composite rigid motion that should
! instead be used as the inverse transform to apply to coordinates being handed
! to suggar. otherwise use the traditional approach of simply using the
! transform for the current body.

          if (suggar_needs_new_mesh) then
            body_arg = body
            if ( body > 1 ) then
              if ( fake_body(body-1) ) body_arg = body-1
            endif
            call send_new_mesh_to_suggar(body_arg, grid, nreal)
            mesh_points_updated = .true.
          end if

        end do

!         if mesh points have been updated, see if we want to rebuild the octree
!         (not rebuilding saves time, at the expense of hole-cutting accuracy)
!         always rebuild for the first new set of mesh points

        rebuild_octree = .false.
        if (octree_used) then
          if (mesh_points_updated) then
            if (dci/octree_update*octree_update == dci .or. ntt == 1) then
              rebuild_octree = .true.
              if (is_fun3d .and. lmpi_master) then
                write(*,'(a)') ' (rebuilding octree)'
              end if
            end if
          end if
        end if

        suggar_only_2 : if (is_suggar) then

!         have suggar update the mesh if needed; update of octree
!         may be performed less frequently to save cpu time

          if (mesh_points_updated) then
            call dcxf_recalc_grid_data(ierr)
            if (rebuild_octree) then
              call dcf_reinit_one_time()
            end if
          end if

!         compute the new connectivity info

          if (skip_dci_output) then
            call dcf_compute_dci(0)
          else
            call dcf_compute_dci(1)
          end if
          call dcf_gather_dci()

!         get timing and memory usage info

          call dcf_get_cpu_time(time1, user_time1, system_time1, ierr)
          call dcf_get_wall_time(wall_time1, ierr)

          suggar_time        = time1 - time0
          suggar_user_time   = user_time1 - user_time0
          suggar_system_time = system_time1 - system_time0
          suggar_wall_time   = wall_time1 - wall_time0

          call dcf_get_memory_usage(mem_usage, ierr)

!         get orphan info

          call dcxf_get_orphan_count(num_hc_failure,num_donor_quality,ierr)

          new_xml_file = .false.

        end if suggar_only_2

!       note: these bcasts force synchronization between the suggar and fun3d
!       processes; otherwise we would need lmpi_synchronize(world_comm) here

        call lmpi_bcast(new_xml_file,      suggar_id_l2g(0), world_comm)
        call lmpi_bcast(suggar_wall_time,  suggar_id_l2g(0), world_comm)
        call lmpi_bcast(mem_usage,         suggar_id_l2g(0), world_comm)
        call lmpi_bcast(num_hc_failure,    suggar_id_l2g(0), world_comm)
        call lmpi_bcast(num_donor_quality, suggar_id_l2g(0), world_comm)

!       dump out orphan info (and optionally timing and memory usage)

        if (is_fun3d .and. lmpi_master) then

          write(*,*)
          write(*,'(a)') ' Orphan Info:'
          write(*,'(a,i0,a)') '   Found      ', num_hc_failure,                &
                              ' orphans because of hole cut failures'
          write(*,'(a,i0,a)') '   Sort added ', num_donor_quality,             &
                              ' orphans because of poor quality donors'

          if (time_moving_grid) then
            write(*,*)
            write(*,'(a)') ' SUGGAR/SUGGAR++ Resource Requirements:'
            write(*,'(a,f0.6,a)')                                              &
                 '   Wall Clock Time ', suggar_wall_time, ' seconds'
            write(*,'(a,i0,a)')                                                &
                 '   Memory Usage    ', mem_usage/1024, ' Mbytes'
            write(*,*)
          end if

        end if

!       load in the new dci data (all fun3d ranks and only the suggar master)

        if (is_fun3d .or. (is_suggar .and. lmpi_master)) then
          call drtf_get_dci_header()
          call drtf_get_dci()
        end if

        if (is_suggar) then
          call dcf_release_dci()
        end if
#endif

! reinitialize DiRTlib and update iblank arrays

        if (is_fun3d) call update_iblank(grid%nnodes01, grid%iblank)

      else get_dci_data

!       just read in existing dci data

        dcif_or_dirtlib : if ( dci_io ) then

          call load_dcif_data(grid)

        else dcif_or_dirtlib

          if (is_fun3d .and. lmpi_master) then
            write(*,'(2a)') &
              ' suggar:overset_connectivity Reading DCI data: ', trim(dci_file)
          end if

          if (is_fun3d) then
            if ( .not. file_exists(trim(dci_file)) ) then
              if (lmpi_master) write(*,'(/,2a)') ' Stopping: could not find ', &
                                                   trim(dci_file)
              call se_flush
              call lmpi_die
            end if
#ifdef HAVE_SUGGAR
            call drtf_load_dci_file_header(trim(dci_file) // char(0))
            call drtf_load_flex_grid_drt_file(trim(dci_file) // char(0))
#endif
          end if

! reinitialize DiRTlib and update iblank arrays

          if (is_fun3d) call update_iblank(grid%nnodes01, grid%iblank)

        endif dcif_or_dirtlib

      end if get_dci_data

    else new_dci_needed

      if (is_fun3d .and. lmpi_master) then
        write(*,'(/,2a)') ' Reusing DCI data ', trim(dci_file)
      end if

    end if new_dci_needed

  end subroutine overset_connectivity


!================================= SET_DCI_COUNTER ===========================80
!
! Sets the dci file counter for dynamic overset grids
!
!=============================================================================80

  subroutine set_dci_counter(need_new_dci_file)

    use lmpi,              only : lmpi_die, lmpi_master, lmpi_conditional_stop
    use info_depr,         only : ntt
    use timeacc_coeffs,    only : t_stage
    use system_extensions, only : se_flush
    use io,                only : prior_iters
    use overset_defs,      only : dci, dci_period_old
    use nml_overset_data,  only : dci_period, dci_freq, reset_dci_period

    logical, intent(out) :: need_new_dci_file

    integer              :: nperiods, ierr

    logical,        save :: init = .true.

  continue

    need_new_dci_file = .false.

    initial_pass : if (init) then

!     reset dci_period if requested and allowed

      ierr = 0

      if (reset_dci_period .and. dci > 0) then

        if (dci_period /= dci_period_old) then
          if (dci == dci_period_old) then
            if (lmpi_master) then
              write(*,'(a,i0,a)') ' Resetting dci counter from ', dci,         &
                                   ' to 0 as requested via --reset_dci_period'
              write(*,'(2a)') '   make sure the time step and any precomputed',&
                              ' dci files are altered accordingly'
            end if
            dci = 0
          else
!           cannot reset at this point
            if (lmpi_master) then
              write(*,'(3a)') ' Stopping: you requested reset_dci_period, but',&
                              ' this is only allowed at the end of the (old)', &
                              ' dci_period'
              write(*,'(a)')  ' (i.e. only if last dci counter = old dci_period'
              write(*,'(a,i0)') '   last dci counter = ', dci
              write(*,'(a,i0)') '   old dci_period   = ', dci_period_old
              call se_flush()
              ierr = 1
            end if
          end if
        else  ! When dci_period == dci_period_old (ADAPTATION ONLY!!!!)
          if (lmpi_master) then
            write(*,'(3a)') ' WARNING: dci_period == dci_period_old',          &
                            ' SHOULD ONLY BE USED WHEN RESTARTING',            &
                            ' AN ADAPTATION CASE TO RECOMPUTE NEW DCIS'
            write(*,'(a,i0,a)') ' Resetting dci counter from ', dci,           &
                                 ' to 0 as requested via --reset_dci_period'
            write(*,'(2a)') '   make sure the time step and any precomputed',  &
                            ' dci files are altered accordingly'
          end if
          dci = 0
        end if

        reset_dci_period = .false.

      else if (.not. reset_dci_period) then

!       make sure dci_period has not changed from restart value without
!       also using --reset_dci_period

        if ((dci_period_old /= 0) .and. (dci_period /= dci_period_old)) then

          if (lmpi_master) then
            write(*,'(2a)') ' Stopping: you have changed dci_period',          &
                            ' without also using --reset_dci_period'
            write(*,'(a,i0)') '    old dci_period = ', dci_period_old
            write(*,'(a,i0)') '    new dci_period = ', dci_period
            call se_flush()
            ierr = 1
          end if
        end if

      end if

      call lmpi_conditional_stop(ierr)

      init = .false.

    end if initial_pass

!   temporary hack to reuse dci data at each stage of a multistage scheme
!   ideally, the look-ahead stages should use the dci data corresponding
!   to the next time step

    if (t_stage > 1) then
      if (ntt == 1 .and. lmpi_master) then
        write(*,*)
        write(*,'(2a)') ' WARNING: using same dci file at each stage of',      &
                        ' the multistage time advancement scheme'
        write(*,'(a)')  ' (this warning issued for first time step only)'
        write(*,*)
      end if
      return
    end if

!   account for any periodicty of the dci files

    nperiods = 0

    if (dci_period > 0) then

!     determine where the current time step lies in terms of periods of
!     the dci file sequence

      do

        dci = (ntt+prior_iters-1)/dci_freq*dci_freq + 1 - nperiods*dci_period

        if ( (dci >= 0) .and. (dci <= dci_period) ) exit

        nperiods = nperiods + 1

!       make sure we don't go on ad infinitum...

        if (nperiods > 1e6 .or. dci < 0) then
          if (lmpi_master) then
            write(*,*)'stopping in set_dci_counter...'
            write(*,*)'  could not bracket time step within a period of',      &
                      ' the suggar dci file sequence'
          end if
          call lmpi_die
        end if

      end do

    else

      dci = (ntt+prior_iters-1)/dci_freq*dci_freq + 1

    end if

    if (dci == ntt+prior_iters-dci_period*nperiods) need_new_dci_file = .true.

  end subroutine set_dci_counter

#ifdef HAVE_SUGGAR

!============================== READ_XML_STRINGS =============================80
!
! Reads SUGGAR xml input for determining domain connectivity information
!
!=============================================================================80

  subroutine read_xml_strings(n_commands, parent, commands, file)

    use allocations,        only : my_alloc_ptr, my_realloc_ptr
    use file_utils,         only : available_unit

    integer,                      intent(inout) :: n_commands
    character(len=*),             intent(in)    :: file

    character(len=256),  dimension(:),  pointer :: commands, parent

    character(len=256) :: string
    character(len=5)   :: string_5

    integer            :: ios, iunit, n_cmd

    logical            :: end_of_file

  continue

    iunit = available_unit()

    if (.not. associated(commands)) call my_alloc_ptr(commands, 256, 1)
    if (.not. associated(parent))   call my_alloc_ptr(parent,   256, 1)

    have_xml_file : if (file /= '') then

      open(unit=iunit, file=file, status="old", iostat=ios)
      if (ios /= 0) then
        write(*,*) 'Failed to open ',trim(file)
        stop ! FIXME: should be lmpi_die or se_exit(1)?
      end if

!     count number of commands

      n_cmd = 0

      rewind(iunit)

      command_glo_1 : do

!       read(iunit,'(a)',iostat=ios) string
!       if (ios == -1) exit command_glo_1        ! -1 => end of file
!       string_5 = trim(adjustl(string))
        global_1 : do
          read(iunit,'(a)',iostat=ios) string
          if (ios == -1) exit command_glo_1
          string_5 = trim(adjustl(string))
          if (string_5 == '</glo') then
            n_cmd = n_cmd + 1
            exit command_glo_1
          end if
!         skip <output> commands; will form new ones (3 lines worth) later
          if (string_5 == '<outp') then
            n_cmd = n_cmd + 3  ! to add <debug_information> element
            call skip_xml_element('output', string, iunit, end_of_file)
            if (end_of_file) exit command_glo_1
            cycle global_1
          end if
!         skip <dynamic> or </dynamic> lines (but not <dynamic/>)
          if (trim(adjustl(string)) == '<dynamic>')  cycle global_1
          if (trim(adjustl(string)) == '</dynamic>') cycle global_1
!         skip blank lines
          if (trim(adjustl(string)) == '') cycle global_1
          n_cmd = n_cmd + 1
!         add counter for <output_grid value="no"/> line to be added later
          if (n_cmd == 1) then
            n_cmd = n_cmd + 1
          end if
        end do global_1

      end do command_glo_1

!     reallocate arrays to hold more commands if needed

      if ( n_commands+n_cmd > size(commands) ) then
        call my_realloc_ptr(commands, 256, n_commands+n_cmd)
        call my_realloc_ptr(parent,   256, n_commands+n_cmd)
      end if

!     now go back and get the actual commands

      n_cmd = n_commands

      rewind(iunit)

      command_glo_2 : do

!       read(iunit,'(a)',iostat=ios) string
!       if (ios == -1) exit command_glo_2
!       string_5 = trim(adjustl(string))
        global_2 : do
          read(iunit,'(a)',iostat=ios) string
          if (ios == -1) exit command_glo_2
          string_5 = trim(adjustl(string))
          if (string_5 == '</glo') then
            n_cmd = n_cmd + 1
            commands(n_cmd) = trim(string)
            exit command_glo_2
          end if
!         skip exisiting <output> commands; add new ones for <debug_information>
          if (string_5 == '<outp') then
            n_cmd = n_cmd + 1
            commands(n_cmd) =trim('  <output>')
            n_cmd = n_cmd + 1
            commands(n_cmd) =trim('  <debug_information max_percent_out="50"/>')
            n_cmd = n_cmd + 1
            commands(n_cmd) =trim('  </output>')
            call skip_xml_element('output', string, iunit, end_of_file)
            if (end_of_file) exit command_glo_2
            cycle global_2
          end if
!         skip <dynamic> or </dynamic> lines (but not <dynamic/>)
          if (trim(adjustl(string)) == '<dynamic>')  cycle global_2
          if (trim(adjustl(string)) == '</dynamic>') cycle global_2
!         skip blank lines
          if (trim(adjustl(string)) == '') cycle global_2
          n_cmd = n_cmd + 1
          commands(n_cmd) = trim(string)
!         add command to skip output of composite grid
          if (n_cmd == 1) then
            n_cmd = n_cmd + 1
            commands(n_cmd) = '  <output_grid value="no"/>'
          end if

        end do global_2

      end do command_glo_2

      n_commands = n_cmd

      close(iunit)

    end if have_xml_file

  end subroutine read_xml_strings


!=============================== XML_STRINGS_FOR_MOTION ======================80
!
! Forms the motion data xml strings for SUGGAR; these will appear in
! SUGGAR_motion.log file output by libsuggar
!
!=============================================================================80

  subroutine xml_strings_for_motion(input_xml_commands, fake_body, ierr)

    use moving_body_types,  only : moving_body
    use nml_grid_motion,    only : n_moving_bodies
    use lmpi,               only : lmpi_master, lmpi_die
    use system_extensions,  only : se_flush
    use string_utils,       only : sub_string

    type(xml_commands),                      intent(in)  :: input_xml_commands

    integer,                                 intent(out) :: ierr

    logical, dimension(n_moving_bodies), intent(in) :: fake_body

    character(len=5)       :: string5

    integer                :: m, body, body1, max_xml_strings, body_arg

    logical, dimension(n_moving_bodies) :: name_match_found

    logical                             :: found
    logical,                       save :: init = .true.

  continue

    ierr = 0

    master_gets_commands : if (lmpi_master) then

      if (init) then

!       sizing estimate for xml_strings:
!       1) minimum requirement (not allowing for spaces) for each complete
!          dynamic body is 15 lines; double that to allow spaces
!       2) need <global>, </global>, <output>, </output> lines, plus
!          a few lines for output description, plus some lines for
!          blanks - say 30 lines in all
!       3) the static bodies are the real wild card; lets allow 300 for that

           max_xml_strings = 30*n_moving_bodies + 30 + 300

           allocate(xml_strings(max_xml_strings))

        init = .false.

      end if

      n_xml = 0
      xml_strings(:) = ''

!     form xml commands for motion

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)') '<global>'

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)')  ' '

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)') '  <output>'

      if (formatted_dci_file) then
        n_xml = n_xml + 1
        write(xml_strings(n_xml),'(4a)')                                       &
            '    <domain_connectivity style="ascii_gen_drt_pairs" ',           &
            'filename="', trim(dci_file), '"/>'
      else
        n_xml = n_xml + 1
        write(xml_strings(n_xml),'(4a)')                                       &
            '    <domain_connectivity style="unformatted_gen_drt_pairs" ',     &
            'byte_order="native" filename="', trim(dci_file), '"/>'
      end if

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)') '  </output>'

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)')  ' '

      name_match_found(:) = .false.

      input_string_loop : do m=1,input_xml_commands%n_commands

        string5 = adjustl(input_xml_commands%commands(m))

        if (string5 == '<body') then

          n_xml = n_xml + 1
          write(xml_strings(n_xml),'(a)') trim(input_xml_commands%commands(m))

!         if this body matches a fun3d moving body, output the motion transform

          body_loop : do body = 1,n_moving_bodies

            call find_name(input_xml_commands%commands(m),                     &
                           moving_body(body)%body_name, found)

            if (found .and. .not.fake_body(body)) then

              n_xml = n_xml + 1
              write(xml_strings(n_xml),'(a)')  '      <dynamic>'

              n_xml = n_xml + 1
              write(xml_strings(n_xml),'(a)')  '      <transform>'

!             provide suggar with transforms for grid in xml format

! if the current body is found to be a match, then look at the preceding body
! in the list.  If it is a fake body, assume that it has the actual composite
! rigid motion that should instead be used as the inverse transform to apply to
! coordinates being handed to suggar. otherwise use the traditional approach of
! simply using the transform for the current body.

              body_arg = body

              if ( body > 1 ) then
                if ( fake_body(body-1) ) then
                  if (sub_string(moving_body(body)%mesh_movement,'deform')) then
                    body_arg = body-1
                  endif
                endif
              endif

              call get_xml_transforms( moving_body(body_arg)%transform_matrix, &
                                       moving_body(body_arg)%inv_transform )

              n_xml = n_xml + 1
              write(xml_strings(n_xml),'(a)')  '      </transform>'

              n_xml = n_xml + 1
              write(xml_strings(n_xml),'(a)')  '      </dynamic>'

              name_match_found(body) = .true.

              exit body_loop

            end if

          end do body_loop

        end if

        if (string5 == '</bod') then
          n_xml = n_xml + 1
          write(xml_strings(n_xml),'(a)') trim(input_xml_commands%commands(m))
        end if

      end do input_string_loop

!     check for moving bodies that may have the same name as one for which
!     name_match_found was found above

      do body = 1,n_moving_bodies
        if (.not. name_match_found(body)) then  ! search for a duplicate name
          do body1 = 1,n_moving_bodies
            if ((moving_body(body)%body_name == moving_body(body1)%body_name)  &
                .and. name_match_found(body1)) name_match_found(body) = .true.
          end do
        end if
      end do

      do body = 1,n_moving_bodies
        if (.not. name_match_found(body)) then
          write(*,'(3a)')                                                      &
            ' Error: could not find a matching name in the xml file for the',  &
            ' body named: ', trim(moving_body(body)%body_name)
          write(*,'(2a,i0)')                                                   &
            ' which was specified in the moving_body.input file for body',     &
            ' number ', body
          call se_flush()
          ierr = 1
        end if
      end do

      if (ierr /= 0) return

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)')  ' '

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)') '</global>'

      n_xml = n_xml + 1
      write(xml_strings(n_xml),'(a)')  ' '

!     make sure we have not exceeded the allocated size of xml_strings

      if (n_xml > size(xml_strings)) then
        write(*,'(a,i5)') ' Stopping: number of xml strings ', n_xml
        write(*,'(a,i5)') ' Size of xml_strings array       ', size(xml_strings)
        call lmpi_die
      end if

    end if master_gets_commands

  end subroutine xml_strings_for_motion

#endif


!=============================== SKIP_XML_ELEMENT ============================80
!
! Skips one or more lines in the xml file containing the xml <tag>  i.e skips
!
! <tag .... />
!
! -or-
!
! <tag>
!   .
!   .
!   .
! </tag>
!
! Note that the <tag> may be recursive; the best example is the <body> tag,
! where there may be subsequent <body> tags (children) within the parent body
!
!=============================================================================80

  recursive subroutine skip_xml_element(tag, string, iunit, end_of_file)

    character(len=*), intent(in)  :: tag, string

    integer,          intent(in)  :: iunit

    logical,          intent(out) :: end_of_file

    character(len=5)              :: string_5, elememt_beg_tag, elememt_end_tag
    character(len=256)            :: new_string

    integer                       :: ios

    end_of_file = .false.

    elememt_beg_tag = '<' // adjustl(tag)
    elememt_end_tag = '</' // adjustl(tag)

    skip_one_or_more : if (index(string,'/>') /= 0) then

!     element to skip is contained in single line, which has already been read

      return

    else skip_one_or_more

!     element to skip spans multiple lines

      skip_multiple_lines : do

        read(iunit,'(a)',iostat=ios) new_string

        if (ios == -1) then
          end_of_file = .true.
          return
        end if

        string_5 = trim(adjustl(new_string))

!       check for children with the same tag

        if (string_5 == elememt_beg_tag) then
          call skip_xml_element(tag, new_string, iunit, end_of_file)
        end if

        if (string_5 == elememt_end_tag) exit skip_multiple_lines

      end do skip_multiple_lines

    end if skip_one_or_more

  end subroutine skip_xml_element


!==================================== FIND_NAME ==============================80
!
! Looks in the character string line for the character string "name" (ie. name
! must be surrounded by double quotes in string), and returns found .true. or
! found .false. accordingly
!
!=============================================================================80

  subroutine find_name(line, name, found)

    character(len=*),   intent(in) :: line, name

    logical,            intent(out) :: found

    integer                         :: istart, iend, i

  continue

    found = .false.

    istart = 0
    iend   = 0

    do

      if (istart >= len(line)) exit

      line_loop_1 : do i = istart+1,len(line)
        istart = i
        if ( line(i:i) == '"' ) exit line_loop_1
      end do line_loop_1

      if ((istart >= 1) .and. istart < len(line)) then

        line_loop_2 : do i = istart+1,len(line)
          iend = i
          if ( line(i:i) == '"' ) exit line_loop_2
        end do line_loop_2

        if (iend > istart .and. iend <= len(line)) then
          if (line(istart+1:iend-1) == trim(name)) then
            found = .true.
          end if
        end if

      end if

      if (found) exit

    end do

  end subroutine find_name


#ifdef HAVE_SUGGAR

!================================= WRITE_XML_STRINGS =========================80
!
! writes the xml strings to a file for SUGGAR
!
!=============================================================================80

  subroutine write_xml_strings()

    use lmpi,       only : lmpi_master
    use file_utils, only : available_unit

    integer           :: m, iunit

  continue

    if (lmpi_master) then

      iunit = available_unit()
      open(unit=iunit, file=xml_file)

      rewind(iunit)

      do m = 1, n_xml
        write(iunit,'(a)') trim(xml_strings(m))
      end do

      close(iunit)

    end if

  end subroutine write_xml_strings


!=============================== GET_XML_TRANSLATIONS ========================80
!
! Gets the xml syntax for a pure translation
!
!=============================================================================80

  subroutine get_xml_translations(a_moving_body)

    use moving_body_types, only : moving_body_type

    type(moving_body_type),            intent(in)    :: a_moving_body

    real(dp)          :: ds, dx, dy, dz

    character(len=80) :: format1

  continue

    format1 = '(a,7(es12.4,a))'

    ds = a_moving_body%translation_vector%ds

    dx = ds*a_moving_body%translation_vector%sx
    dy = ds*a_moving_body%translation_vector%sy
    dz = ds*a_moving_body%translation_vector%sz

    n_xml = n_xml + 1
    write(xml_strings(n_xml),format1)                                          &
          '        <translate axis="x" value="', dx, '"/>'
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format1)                                          &
          '        <translate axis="y" value="', dy, '"/>'
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format1)                                          &
          '        <translate axis="z" value="', dz, '"/>'

  end subroutine get_xml_translations


!================================ GET_XML_ROTATIONS ==========================80
!
! Gets the xml syntax for a pure rotation
!
!=============================================================================80

  subroutine get_xml_rotations(a_moving_body)

    use moving_body_types, only : moving_body_type

    type(moving_body_type),            intent(in)    :: a_moving_body

    real(dp)          :: x0, y0, z0, axis_x, axis_y, axis_z, theta

    character(len=80) :: format1

  continue

    format1 = '(a,7(es12.4,a))'

    x0 = a_moving_body%rotation_vector%xorigin
    y0 = a_moving_body%rotation_vector%yorigin
    z0 = a_moving_body%rotation_vector%zorigin

    axis_x = a_moving_body%rotation_vector%tx
    axis_y = a_moving_body%rotation_vector%ty
    axis_z = a_moving_body%rotation_vector%tz

    theta = a_moving_body%rotation_vector%theta

    n_xml = n_xml + 1
    write(xml_strings(n_xml),format1)                                          &
          '        <rotate_about_v axis_vector="',                             &
          axis_x, ',', axis_y, ',', axis_z, '" value="', theta,                &
          '" originx="', x0, '" originy="', y0, '" originz ="', z0, '"/>'

  end subroutine get_xml_rotations


!================================ GET_XML_TRANSFORMS =========================80
!
! Gets the xml syntax for the 4x4 transform matrix and its inverse
!
!=============================================================================80

  subroutine get_xml_transforms(transform_matrix, inv_transform)

    real(dp), dimension(4,4), intent(in) :: transform_matrix, inv_transform

    real(dp)          :: a11, a12, a13, a14
    real(dp)          :: a21, a22, a23, a24
    real(dp)          :: a31, a32, a33, a34
    real(dp)          :: a41, a42, a43, a44

    real(dp)          :: i11, i12, i13, i14
    real(dp)          :: i21, i22, i23, i24
    real(dp)          :: i31, i32, i33, i34
    real(dp)          :: i41, i42, i43, i44

    character(len=80) :: format1, format2

  continue

    format1 = '(a)'
    format2 = '(a,4(es19.12,a))'

    a11 = transform_matrix(1,1)
    a12 = transform_matrix(1,2)
    a13 = transform_matrix(1,3)
    a14 = transform_matrix(1,4)
    a21 = transform_matrix(2,1)
    a22 = transform_matrix(2,2)
    a23 = transform_matrix(2,3)
    a24 = transform_matrix(2,4)
    a31 = transform_matrix(3,1)
    a32 = transform_matrix(3,2)
    a33 = transform_matrix(3,3)
    a34 = transform_matrix(3,4)
    a41 = transform_matrix(4,1)
    a42 = transform_matrix(4,2)
    a43 = transform_matrix(4,3)
    a44 = transform_matrix(4,4)

    i11 = inv_transform(1,1)
    i12 = inv_transform(1,2)
    i13 = inv_transform(1,3)
    i14 = inv_transform(1,4)
    i21 = inv_transform(2,1)
    i22 = inv_transform(2,2)
    i23 = inv_transform(2,3)
    i24 = inv_transform(2,4)
    i31 = inv_transform(3,1)
    i32 = inv_transform(3,2)
    i33 = inv_transform(3,3)
    i34 = inv_transform(3,4)
    i41 = inv_transform(4,1)
    i42 = inv_transform(4,2)
    i43 = inv_transform(4,3)
    i44 = inv_transform(4,4)

    n_xml = n_xml + 1
    write(xml_strings(n_xml),format1)                                          &
          "        <matrix  type='local_to_global'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        a11='",a11, "' a12='",a12, "' a13='",a13, "' a14='",a14,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        a21='",a21, "' a22='",a22, "' a23='",a23, "' a24='",a24,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        a31='",a31, "' a32='",a32, "' a33='",a33, "' a34='",a34,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        a41='",a41, "' a42='",a42, "' a43='",a43, "' a44='",a44,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        i11='",i11, "' i12='",i12, "' i13='",i13, "' i14='",i14,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        i21='",i21, "' i22='",i22, "' i23='",i23, "' i24='",i24,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        i31='",i31, "' i32='",i32, "' i33='",i33, "' i34='",i34,"'"
    n_xml = n_xml + 1
    write(xml_strings(n_xml),format2)                                          &
          "        i41='",i41, "' i42='",i42, "' i43='",i43, "' i44='",i44,"'/>"

  end subroutine get_xml_transforms


!=========================== SEND_NEW_MESH_TO_SUGGAR =========================80
!
! Extracts new mesh points for grids associated with moving bodies for SUGGAR;
! the points are then passed to the SUGGAR process
!
! The new mesh points are only really needed for deforming meshes, but that
! distinction is assumed to be made outside this routine, in order to allow
! testing of rigid mesh cases. The output new mesh points are the current
! deformed mesh points in moving grids, but (rigidly) transformed back to the
! t=0 positions (equivalent to the t=0 meshes being deformed)
!
! NOTE: assumes that the number and order of the grid points in each overset
! grid is the same as it was when SUGGAR read in the original grid
! components to create the original composite overset grid file
!
!=============================================================================80

  subroutine send_new_mesh_to_suggar(body, grid, key)

    use grid_types,          only : grid_type
    use moving_body_types,   only : moving_body
    use lmpi,                only : lmpi_master, lmpi_bcast, lmpi_send,        &
                                    lmpi_recv, lmpi_synchronize, lmpi_id
    use info_depr,           only : twod
    use string_utils,        only : sub_string

    integer,                                            intent(in) :: body, key
    type(grid_type),                                    intent(in) :: grid

    integer                                        :: i, ii, ierr, jtag, proc
    integer, dimension(1)                          :: component_nnodes
    integer, dimension(:),       allocatable       :: map

    real(dp)                                       :: xold, yold, zold
    real(dp), dimension(:),      allocatable       :: tgridx, tgridy, tgridz
    real(dp), dimension(4,4)                       :: inv_transform

    logical,                                  save :: init = .true.
    logical                                        :: dynamic_transform

  continue

    solver_only_2 : if (is_fun3d) then

!     set component grid associated with current body; use only level 0 nodes

      ii = 0
      do i = 1,grid%nnodes0
        if (grid%imesh(i) == key) then
          ii = ii + 1
        end if
      end do

      component_nnodes(1) = ii

      if (component_nnodes(1) > 0) then
        allocate(map(component_nnodes(1)))
        allocate(tgridx(component_nnodes(1)))
        allocate(tgridy(component_nnodes(1)))
        allocate(tgridz(component_nnodes(1)))
      end if

!     determine if we need to pass transformed coordinates
!     inv_transform takes coords from current position to t=0 position
!     in the composite mesh

      inv_transform = moving_body(body)%inv_transform

      dynamic_transform = .false.

      if (.not. sub_string(moving_body(body)%mesh_movement,'static')) then
        dynamic_transform = .true.
      end if

!     map array holds l2g (local-to-global) data for points in the
!     current component mesh

      ii = 0

      do i=1,grid%nnodes0

        if (grid%imesh(i) == key) then

          ii = ii + 1

          map(ii) = grid%l2g(i)

          tgridx(ii) = grid%x(i)
          tgridy(ii) = grid%y(i)
          tgridz(ii) = grid%z(i)

!         transform current mesh coordinates to t=0 system if needed

          transform_coords : if (dynamic_transform) then

!           current coordinates

            xold = tgridx(ii)
            yold = tgridy(ii)
            zold = tgridz(ii)

!           coordinates in initial system

            tgridx(ii) = inv_transform(1,1)*xold                               &
                       + inv_transform(1,2)*yold                               &
                       + inv_transform(1,3)*zold                               &
                       + inv_transform(1,4)

            if (.not.(twod))                                                   &
            tgridy(ii) = inv_transform(2,1)*xold                               &
                       + inv_transform(2,2)*yold                               &
                       + inv_transform(2,3)*zold                               &
                       + inv_transform(2,4)

            tgridz(ii) = inv_transform(3,1)*xold                               &
                       + inv_transform(3,2)*yold                               &
                       + inv_transform(3,3)*zold                               &
                       + inv_transform(3,4)

          end if transform_coords

        end if

      end do

    end if solver_only_2

    call lmpi_synchronize(world_comm)

    solver_only_3 : if (is_fun3d) then

      call lmpi_send(component_nnodes, 1, suggar_id_l2g(0), 1000, ierr,        &
                     world_comm)

      if (component_nnodes(1) > 0) then

        call lmpi_send(map,    component_nnodes(1), suggar_id_l2g(0), 1002,    &
                       ierr, world_comm)
        call lmpi_send(tgridx, component_nnodes(1), suggar_id_l2g(0), 1003,    &
                       ierr, world_comm)
        call lmpi_send(tgridy, component_nnodes(1), suggar_id_l2g(0), 1004,    &
                       ierr, world_comm)
        call lmpi_send(tgridz, component_nnodes(1), suggar_id_l2g(0), 1005,    &
                       ierr, world_comm)
      end if

    else solver_only_3

!     only the suggar master is to receive the new grid data

      suggar_master_only:  if (lmpi_master) then

        do proc = 0, world_nproc-suggar_nproc-1

          call lmpi_recv(component_nnodes, 1, proc, 1000,                      &
                         ierr, jtag, world_comm)

          if (component_nnodes(1) > 0) then

            allocate(map(component_nnodes(1)))
            allocate(tgridx(component_nnodes(1)))
            allocate(tgridy(component_nnodes(1)))
            allocate(tgridz(component_nnodes(1)))

            call lmpi_recv(map, component_nnodes(1), proc, 1002,               &
                           ierr, jtag, world_comm)
            call lmpi_recv(tgridx, component_nnodes(1), proc, 1003,            &
                           ierr, jtag, world_comm)
            call lmpi_recv(tgridy, component_nnodes(1), proc, 1004,            &
                           ierr, jtag, world_comm)
            call lmpi_recv(tgridz, component_nnodes(1), proc, 1005,            &
                           ierr, jtag, world_comm)

!           zero as leading argument to indicate map array corresponds to
!           composite mesh rather than individual component meshes

            call dcxf_put_rank_gridpoints_dbl(0, tgridx, tgridy, tgridz,       &
                                              component_nnodes(1), map,        &
                                              ierr)

            deallocate(map)
            deallocate(tgridx)
            deallocate(tgridy)
            deallocate(tgridz)

          end if

        end do

      end if suggar_master_only

    end if solver_only_3

    if (is_fun3d) then
      if (allocated(map))    deallocate(map)
      if (allocated(tgridx)) deallocate(tgridx)
      if (allocated(tgridy)) deallocate(tgridy)
      if (allocated(tgridz)) deallocate(tgridz)
    end if

    init = .false.

  end subroutine send_new_mesh_to_suggar

#endif

end module suggar

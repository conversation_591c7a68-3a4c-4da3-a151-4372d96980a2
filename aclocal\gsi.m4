# -*- Autoconf -*-
#
# Assigned Shell Variables:
#   $enable_gsi      Build with GSI wrapper
#
# Assigned AC_DEFINES:
#   HAVE_GSI
#
# Assigned AM_CONDITIONALS:
#   BUILD_GSI_SUPPORT
#
AC_DEFUN([AX_GSI],[

AC_ARG_ENABLE(gsi,
        [[  --enable-gsi            build with GSI wrapper [no]]],
        [enable_gsi=$enableval],    [enable_gsi="no"])

if test "$enable_gsi" != 'no'
then
  AC_DEFINE([HAVE_GSI],[1],[GSI is available])
  AM_CONDITIONAL(BUILD_GSI_SUPPORT,true)
else
  AM_CONDITIONAL(BUILD_GSI_SUPPORT,false)
fi

])


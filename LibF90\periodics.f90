module periodics

  use bc_types, only : periodic_type
  use nml_periodicity, only : periodic_dir, periodic_tol

  implicit none

  private

  public :: load_periodic_data, nperiodic, periodic
  public :: periodic1a, periodic1b, periodic_data
  public :: periodic2a, periodic2b, periodic3a, periodic3b, periodic_nature

  integer :: nperiodic  = 0      ! Number of periodic sets
  integer :: periodic1a = 0      ! Periodic BC #1a
  integer :: periodic1b = 0      ! Periodic BC #1b
  integer :: periodic2a = 0      ! Periodic BC #2a
  integer :: periodic2b = 0      ! Periodic BC #2b
  integer :: periodic3a = 0      ! Periodic BC #3a
  integer :: periodic3b = 0      ! Periodic BC #3b


  integer, dimension(:), pointer :: periodic_nature ! nature of periodic
                                                    ! point: primary or
                                                    ! secondary

  logical :: periodic = .false. ! Periodicity flag

  type(periodic_type), dimension(:), allocatable :: periodic_data

contains

!=============================== LOAD_PERIODIC_DATA ==========================80
!
!   Loads data related to periodic BC's
!
!=============================================================================80
  subroutine load_periodic_data(nnodes0,nbound,bc,x,y,z,nnodes01)

    use bc_types,        only : bcgrid_type
    use allocations,     only : my_alloc_ptr, my_realloc_ptr
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_conditional_stop

    integer, intent(in) :: nnodes0, nbound, nnodes01

    real(dp), dimension(nnodes01), intent(in) :: x, y, z

    type(bcgrid_type), dimension(nbound), intent(in) :: bc

    integer :: npair, npair1, npair2, npair3, ipass, indexa, previous_npair
    integer :: i, j, inode, basenode, indexb, nfound, ii, pdir
    integer :: type1, type2, type3
    integer :: single_plane_periodic_nodes
    integer :: corner1, corner2, corner3, corner4, k, ib
    integer :: corner5, corner6, corner7, corner8
    integer :: primary_node, secondary_node
    integer :: secondary_node1, secondary_node2, secondary_node3
    integer :: index1, index2, index3, index4, index5, index6

    integer, dimension(8) :: corner, sorted_corner
    integer, dimension(nnodes0) :: node_tag
    integer, dimension(:,:), pointer :: pair

    real(dp) :: test_value, xb, yb, zb
    real(dp) :: xi, yi, zi, dist

    real(dp), dimension(8) :: test_coord

    logical :: found_em, xdir_is_periodic, ydir_is_periodic
    logical :: zdir_is_periodic, found_it

  continue

    indexa = 0
    index1 = 0; index2 = 0; index3 = 0; index4 = 0; index5 = 0; index6 = 0
    indexb = 0
    dist = 0.0_dp

    npair  = 0 ! total number of pairs across all periodic boundary pairs
    npair1 = 0 ! number of pairs across periodicity1 boundary pairs
    npair2 = 0 ! number of pairs across periodicity2 boundary pairs
    npair3 = 0 ! number of pairs across periodicity3 boundary pairs

    call my_alloc_ptr(pair,2,1)

! Determine all of the raw pairs

    pass_loop : do ipass = 1, 3   ! 3 possible periodic pairs of boundaries

      pdir = periodic_dir(ipass)

      select case(ipass)
      case(1)
        if ( periodic1a == 0 ) exit pass_loop
        indexa = periodic1a
        indexb = periodic1b
      case(2)
        if ( periodic2a == 0 ) exit pass_loop
        indexa = periodic2a
        indexb = periodic2b
      case(3)
        if ( periodic3a == 0 ) exit pass_loop
        indexa = periodic3a
        indexb = periodic3b
      end select

! Tag the nodes on BC a

      previous_npair = npair

      node_tag = 0

      do i = 1, bc(indexa)%nbnode
        inode = bc(indexa)%ibnode(i)
        if ( inode > nnodes0 ) cycle
        node_tag(inode) = 1
        npair = npair + 1
      end do

      single_plane_periodic_nodes = sum(node_tag)

      call my_realloc_ptr(pair, 2, npair)

! Now go store the pairs

      npair = previous_npair

      nfound = 0
      node_loop : do i = 1, bc(indexa)%nbnode

        basenode = bc(indexa)%ibnode(i)

        if ( basenode > nnodes0 ) cycle node_loop

        xb = x(basenode)
        yb = y(basenode)
        zb = z(basenode)

        other_plane : do ii = 1, bc(indexb)%nbnode

          inode = bc(indexb)%ibnode(ii)

          if ( inode > nnodes0 ) cycle other_plane
          if ( node_tag(inode) > 0 ) cycle other_plane

          xi = x(inode)
          yi = y(inode)
          zi = z(inode)

          select case(pdir)
          case(1)
            dist = sqrt((yi-yb)**2 + (zi-zb)**2)
          case(2)
            dist = sqrt((xi-xb)**2 + (zi-zb)**2)
          case(3)
            dist = sqrt((xi-xb)**2 + (yi-yb)**2)
          end select

          if ( dist > periodic_tol ) cycle other_plane

          nfound = nfound + 1

          npair = npair + 1
          pair(1,npair) = basenode
          pair(2,npair) = inode

          node_tag(inode) = 2

          exit other_plane

        end do other_plane

      end do node_loop

      if ( nfound /= single_plane_periodic_nodes ) then
        write(*,*)'Error: could not locate all periodic pairs during pass',ipass
        write(*,*)'nfound, single_plane_periodic_nodes = ',  nfound,           &
                  single_plane_periodic_nodes
        write(*,*)'Either the mesh topology is invalid for periodic simulations'
        write(*,*)'or you need to increase the search tolerance using the'
        write(*,*)'periodic_tol input.  The current value of this parameter'
        write(*,*)'is ', periodic_tol
        call lmpi_conditional_stop(1,'load_periodic_data: bad pairs')
      endif
      call lmpi_conditional_stop(0,'load_periodic_data: bad pairs')

      select case(ipass)
      case(1)
        npair1 = npair
      case(2)
        npair2 = npair - npair1
      case(3)
        npair3 = npair - npair1 - npair2
      end select

    end do pass_loop

! Now that we know the raw pairs, if there is more than one periodic pair
! of boundaries, then we need to intelligently post-process this information

    how_many_pairs : if ( npair2 > 0 .and. npair3 > 0 ) then ! then 3 peri dirs

! Classify the nature of each periodic boundary point
! node_tag = 1: node: 1 simple periodic pair
! node_tag = 2: edge: 2 periodic pairs
! node_tag = 3: corner: 4 periodic pair

      node_tag(:) = 0

      do ib = 1, nbound
        if ( ib == periodic1a .or. ib == periodic1b .or.                       &
             ib == periodic2a .or. ib == periodic2b .or.                       &
             ib == periodic3a .or. ib == periodic3b ) then
          do j = 1, bc(ib)%nbnode
            inode = bc(ib)%ibnode(j)
            node_tag(inode) = node_tag(inode) + 1
          end do
        endif
      end do

! Figure out how many periodic sets we will have so we can allocate
! the first level of the periodic derived type.  Also find the eight
! corners while we're at it

      type1 = 0
      type2 = 0
      type3 = 0

      corner1 = 0; corner2 = 0; corner3 = 0; corner4 = 0
      corner5 = 0; corner6 = 0; corner7 = 0; corner8 = 0

      do i = 1, nnodes0
        if ( node_tag(i) == 1 ) type1 = type1 + 1
        if ( node_tag(i) == 2 ) type2 = type2 + 1
        if ( node_tag(i) == 3 ) type3 = type3 + 1
        if ( node_tag(i) == 3 ) then
          if ( corner1 == 0 ) then
            corner1 = i
          else if ( corner2 == 0 ) then
            corner2 = i
          else if ( corner3 == 0 ) then
            corner3 = i
          else if ( corner4 == 0 ) then
            corner4 = i
          else if ( corner5 == 0 ) then
            corner5 = i
          else if ( corner6 == 0 ) then
            corner6 = i
          else if ( corner7 == 0 ) then
            corner7 = i
          else if ( corner8 == 0 ) then
            corner8 = i
          else
            write(*,*) 'More than 8 corners found.'
            call lmpi_conditional_stop(1,'load_periodic_data: bad corners')
          endif
        endif
      end do

      if ( corner8 == 0 ) then
        write(*,*) 'Trouble finding all 8 corners.'
        call lmpi_conditional_stop(1,'load_periodic_data: bad corners')
      endif

      call lmpi_conditional_stop(0,'load_periodic_data: bad corners')

      write(*,*) 'Found this many simple periodic nodes: ', type1
      write(*,*) 'Found this many edge   periodic nodes: ', type2
      write(*,*) 'Found this many corner periodic nodes: ', type3

! Divide to get the number of periodic sets of each type

      type1 = type1 / 2
      type2 = type2 / 4
      type3 = type3 / 8

      nperiodic = type1 + type2 + type3

      allocate(periodic_data(nperiodic))
      periodic_data(:)%n = 0

! First process the corner points
! There are 8 corners - one will be the primary, the other 7 become secondary

      nperiodic = 1
      periodic_data(nperiodic)%n = 8
      call my_alloc_ptr(periodic_data(nperiodic)%list,                         &
                                                     periodic_data(nperiodic)%n)

      periodic_data(nperiodic)%list(1) = corner1
      periodic_data(nperiodic)%list(2) = corner2
      periodic_data(nperiodic)%list(3) = corner3
      periodic_data(nperiodic)%list(4) = corner4
      periodic_data(nperiodic)%list(5) = corner5
      periodic_data(nperiodic)%list(6) = corner6
      periodic_data(nperiodic)%list(7) = corner7
      periodic_data(nperiodic)%list(8) = corner8

! Now find the edge seams:
! These will have one primary node and 3 secondary nodes

      pass_loop2 : do ipass = 1, 3

        select case(ipass)
        case(1)
          index1 = 1
          index2 = npair1
          index3 = npair1+1
          index4 = npair1+npair2
          index5 = npair1+npair2+1
          index6 = npair1+npair2+npair3
        case(2)
          index1 = npair1 + 1
          index2 = npair1 + npair2
          index3 = 1
          index4 = npair1
          index5 = npair1+npair2+1
          index6 = npair1+npair2+npair3
        case(3)
          index1 = npair1 + npair2 + 1
          index2 = npair1 + npair2 + npair3
          index3 = 1
          index4 = npair1
          index5 = npair1 + 1
          index6 = npair1 + npair2
        end select

        do j = index1, index2

          primary_node    = pair(1,j)
          secondary_node1 = pair(2,j)
          secondary_node2 = 0
          secondary_node3 = 0

          if ( node_tag(primary_node) == 2 ) then

            found_em = .false.

            search1 : do k = index3, index4
              if ( pair(1,k) == primary_node ) then
                secondary_node2 = pair(2,k)
                found_em = .true.
                exit search1
              else if ( pair(2,k) == primary_node ) then
                secondary_node2 = pair(1,k)
                found_em = .true.
                exit search1
              endif
            end do search1

            search2 : do k = index3, index4
              if ( pair(1,k) == secondary_node1 ) then
                secondary_node3 = pair(2,k)
                found_em = .true.
                exit search2
              else if ( pair(2,k) == secondary_node1 ) then
                secondary_node3 = pair(1,k)
                found_em = .true.
                exit search2
              endif
            end do search2

            if ( .not. found_em ) then

              search3 : do k = index5, index6
                if ( pair(1,k) == primary_node ) then
                  secondary_node2 = pair(2,k)
                  found_em = .true.
                  exit search3
                else if ( pair(2,k) == primary_node ) then
                  secondary_node2 = pair(1,k)
                  found_em = .true.
                  exit search3
                endif
              end do search3

              search4 : do k = index5, index6
                if ( pair(1,k) == secondary_node1 ) then
                  secondary_node3 = pair(2,k)
                  found_em = .true.
                  exit search4
                else if ( pair(2,k) == secondary_node1 ) then
                  secondary_node3 = pair(1,k)
                  found_em = .true.
                  exit search4
                endif
              end do search4

            endif

            if ( .not. found_em ) then
              write(*,*) 'Trouble finding 4 edges', j
              call lmpi_conditional_stop(1,'load_periodic_data: bad edges')
            endif

! untag them so we don't do them again

            node_tag(primary_node)    = 0
            node_tag(secondary_node1) = 0
            node_tag(secondary_node2) = 0
            node_tag(secondary_node3) = 0

            nperiodic = nperiodic + 1
            periodic_data(nperiodic)%n = 4

            call my_alloc_ptr(periodic_data(nperiodic)%list,                   &
                                                     periodic_data(nperiodic)%n)

            periodic_data(nperiodic)%list(1) = primary_node
            periodic_data(nperiodic)%list(2) = secondary_node1
            periodic_data(nperiodic)%list(3) = secondary_node2
            periodic_data(nperiodic)%list(4) = secondary_node3

          endif
        end do

      end do pass_loop2
      call lmpi_conditional_stop(0,'load_periodic_data: bad edges')

! The remaining periodic nodes with tag=1 will just be pairs of 2 points

      do j = 1, npair
        primary_node = pair(1,j)
        if ( node_tag(primary_node) == 1 ) then
          secondary_node = pair(2,j)
          nperiodic = nperiodic + 1
          periodic_data(nperiodic)%n = 2
          call my_alloc_ptr(periodic_data(nperiodic)%list,                     &
                                                     periodic_data(nperiodic)%n)
          periodic_data(nperiodic)%list(1) = primary_node
          periodic_data(nperiodic)%list(2) = secondary_node
        endif
      end do

    else if ( npair2 > 0 ) then ! two periodic directions present

! Classify the nature of each periodic boundary point
! node_tag = 1: node: 1 simple periodic pair
! node_tag = 2: corner: 2 periodic pairs

      node_tag(:) = 0

      do ib = 1, nbound
        if ( ib == periodic1a .or. ib == periodic1b .or.                       &
             ib == periodic2a .or. ib == periodic2b .or.                       &
             ib == periodic3a .or. ib == periodic3b ) then
          do j = 1, bc(ib)%nbnode
            inode = bc(ib)%ibnode(j)
            node_tag(inode) = node_tag(inode) + 1
          end do
        endif
      end do

! Figure out how many periodic sets we will have so we can allocate
! the first level of the periodic derived type.  Also find the eight
! corners while we're at it

      type1 = 0
      type2 = 0

      corner(1:8) = 0

      do i = 1, nnodes0

        if ( node_tag(i) == 1 ) type1 = type1 + 1
        if ( node_tag(i) == 2 ) type2 = type2 + 1

        if ( node_tag(i) == 2 ) then

          found_it = .false.
          search5 : do j = 1, 8
            if ( corner(j) == 0 ) then
              corner(j) = i
              found_it = .true.
              exit search5
            endif
          end do search5

          if ( .not. found_it ) then
            write(*,*) 'More than 8 corners found.'
            call lmpi_conditional_stop(1,'load_periodic_data: bad corners2')
          endif

        endif

      end do

      if ( corner(8) == 0 ) then
        write(*,*) 'Trouble finding all 8 corners.'
        call lmpi_conditional_stop(1,'load_periodic_data: bad corners2')
      endif
      call lmpi_conditional_stop(0,'load_periodic_data: bad corners2')

! Sort the raw corners into the two periodic groups of four corners each

      xdir_is_periodic = .false.
      ydir_is_periodic = .false.
      zdir_is_periodic = .false.

      select case(periodic_dir(1))
      case(1)
        xdir_is_periodic = .true.
      case(2)
        ydir_is_periodic = .true.
      case(3)
        zdir_is_periodic = .true.
      end select

      select case(periodic_dir(2))
      case(1)
        xdir_is_periodic = .true.
      case(2)
        ydir_is_periodic = .true.
      case(3)
        zdir_is_periodic = .true.
      end select

      if ( .not. xdir_is_periodic ) then
        write(*,*) 'x-direction determined to not be periodic.'
        do j = 1, 8
          test_coord(j) = x(corner(j))
        end do
      endif

      if ( .not. ydir_is_periodic ) then
        write(*,*) 'y-direction determined to not be periodic.'
        do j = 1, 8
          test_coord(j) = y(corner(j))
        end do
      endif

      if ( .not. zdir_is_periodic ) then
        write(*,*) 'z-direction determined to not be periodic.'
        do j = 1, 8
          test_coord(j) = z(corner(j))
        end do
      endif

      test_value = test_coord(1)   ! arbitrary choice for comparisons

      sorted_corner(1:8) = 0

      sorting : do j = 1, 8

        which_group : if ( abs(test_coord(j)-test_value) <= periodic_tol ) then

          found_it = .false.
          search6 : do k = 1, 4
            if ( sorted_corner(k) == 0 ) then
              sorted_corner(k) = corner(j)
              found_it = .true.
              exit search6
            endif
          end do search6

          if ( .not. found_it ) then
            write(*,*) 'More than 4 corners found, section 1'
            call lmpi_conditional_stop(1,'load_periodic_data: bad corner sec 1')
          endif

        else which_group

          found_it = .false.
          search7 : do k = 5, 8
            if ( sorted_corner(k) == 0 ) then
              sorted_corner(k) = corner(j)
              found_it = .true.
              exit search7
            endif
          end do search7

          if ( .not. found_it ) then
            write(*,*) 'More than 4 corners found, section 2'
            call lmpi_conditional_stop(1,'load_periodic_data: bad corner sec 2')
          endif

        endif which_group

      end do sorting

      if ( sorted_corner(4) == 0 .or. sorted_corner(8) == 0 ) then
        write(*,*) 'Trouble finding all 8 sorted_corners.'
        call lmpi_conditional_stop(1,'load_periodic_data: bad corner sec')
      endif
      call lmpi_conditional_stop(0,'load_periodic_data: bad corner sec')

      write(*,*) 'Found this many simple periodic nodes: ', type1
      write(*,*) 'Found this many corner periodic nodes: ', type2

! Divide to get the number of periodic sets of each type

      type1 = type1 / 2
      type2 = type2 / 4

      nperiodic = type1 + type2

      allocate(periodic_data(nperiodic))
      periodic_data(:)%n = 0

! First process the corner points
! There are 8 corners forming two periodic sets

      nperiodic = 1
      periodic_data(nperiodic)%n = 4
      call my_alloc_ptr(periodic_data(nperiodic)%list,                         &
                                                     periodic_data(nperiodic)%n)

      periodic_data(nperiodic)%list(1) = sorted_corner(1)
      periodic_data(nperiodic)%list(2) = sorted_corner(2)
      periodic_data(nperiodic)%list(3) = sorted_corner(3)
      periodic_data(nperiodic)%list(4) = sorted_corner(4)

      nperiodic = 2
      periodic_data(nperiodic)%n = 4
      call my_alloc_ptr(periodic_data(nperiodic)%list,                         &
                                                     periodic_data(nperiodic)%n)

      periodic_data(nperiodic)%list(1) = sorted_corner(5)
      periodic_data(nperiodic)%list(2) = sorted_corner(6)
      periodic_data(nperiodic)%list(3) = sorted_corner(7)
      periodic_data(nperiodic)%list(4) = sorted_corner(8)

! The remaining periodic nodes with tag=1 will just be pairs of 2 points

      do j = 1, npair
        primary_node = pair(1,j)
        if ( node_tag(primary_node) == 1 ) then
          secondary_node = pair(2,j)
          nperiodic = nperiodic + 1
          periodic_data(nperiodic)%n = 2
          call my_alloc_ptr(periodic_data(nperiodic)%list,                     &
                                                     periodic_data(nperiodic)%n)
          periodic_data(nperiodic)%list(1) = primary_node
          periodic_data(nperiodic)%list(2) = secondary_node
        endif
      end do


    else how_many_pairs

! There is just one pair of periodic boundaries

      nperiodic = npair
      allocate(periodic_data(nperiodic))

      nperiodic = 0

      do j = 1, npair
        primary_node   = pair(1,j)
        secondary_node = pair(2,j)
        nperiodic = nperiodic + 1
        periodic_data(nperiodic)%n = 2
        call my_alloc_ptr(periodic_data(nperiodic)%list,                       &
                                                     periodic_data(nperiodic)%n)
        periodic_data(nperiodic)%list(1) = primary_node
        periodic_data(nperiodic)%list(2) = secondary_node
      end do

    endif how_many_pairs

  end subroutine load_periodic_data

end module periodics

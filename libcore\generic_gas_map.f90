module generic_gas_map

! Parameters required for eqn_set=2

  use kinddefs,        only : dp

  implicit none

  private

  public :: n_species, n_energy, n_mom, n_pjac, n_bc, n_density
  public :: n_momx, n_momy, n_momz, n_etot, n_energy_last, n_molecular_weight
  public :: n_tot_g, ndim_g, ngrad_g, n_turb_g

  public :: generic_gas_restart_version, generic_gas_n_rad, generic_gas_n_abl
  public :: chem_on, therm_on, electrons, perfect
  public :: gas_radiation, rad_use_impl_lines, cpiv_min_factor

  public :: rf_vis, rf_inv, eigzero, eigzero_imp, v_ref, rho_ref, l_ref
  public :: rf_chem, temperature_ref
  public :: sutherland1, sutherland2, prandtl
  public :: multi_component_diff


  public :: n_pressure_k, n_sonic_k, n_temperature_j, n_energy_j, n_entropy_j
  public :: n_cv_j, n_eta_j, n_amu_k, n_dif_i
  public :: n_enthalpy_ij, n_pressure_jac, n_delqr
  public :: n_photo_1, n_photo_2, photo_num
  public :: n_erad_1, n_erad_2, n_arad_1, n_arad_2, aji_num
  public :: n_level_1
  public :: vorticity_based
  public :: algebraic_turb_on, baldwin_lomax_on, cebeci_smith_on
  public :: algebraic_turb_insert, algebraic_turb_delete

  public :: chem_flag, therm_flag, turb_int_inf,                               &
            turb_vis_ratio_inf, prandtl_turb, schmidt_turb, prod_to_des_limit, &
            turb_cond_model, turb_comp_model,                                  &
            alpha_realizability, jb_rough, law_of_the_wall, vuong_coakley,     &
            tke_amb, omega_amb, sst_sust

  integer :: n_species = 1        ! number of species
  integer :: n_energy  = 1        ! number of energy equations
  integer :: n_mom  = 1           ! number of momentum equations
  integer :: n_pjac = 2           ! number of pressure jacobian elements
  integer :: n_bc                 ! number of instances of inflow
                                  ! boundaries
  integer :: n_density = 1        ! density index in qnode
  integer :: n_momx = 2           ! 1st x-momentum index in qnode
  integer :: n_momy = 3           ! 1st y-momentum index in qnode
  integer :: n_momz = 4           ! 1st z-momentum index in qnode
  integer :: n_etot = 5           ! total energy density index in qnode
  integer :: n_energy_last = 5    ! last energy density index in qnode
  integer :: n_molecular_weight   ! molecular_weight index in qnode
  integer :: n_delqr              ! radiation source term
  integer     :: n_photo_1            ! photo-dis-ionization source start
  integer     :: n_photo_2            ! photo-dis-ionization source end
  integer     :: photo_num            ! photo-dis-ionization source length
  integer :: n_erad_1, n_erad_2, n_arad_1, n_arad_2, aji_num
  integer :: n_tot_g              ! total variables in generic path
  integer :: ndim_g = 5           ! ndim in generic path
  integer :: ngrad_g              ! number gradients in generic path
  integer :: n_turb_g = 0         ! number of turbulence eqs in generic path
  integer :: n_level_1 = 0        ! starting location of time level n-1

! Added restart variables for generic  ! From namelist
  integer :: generic_gas_restart_version = 0  ! restart version (0 none)
  integer :: generic_gas_n_rad = 0    ! radiation model (0 none, 1 coupled)
  integer :: generic_gas_n_abl = 0    ! ablation  model (0 node, 1 coupled)

  integer, dimension(:),   pointer :: n_pressure_k    ! pressure index in qnode
  integer, dimension(:),   pointer :: n_sonic_k       ! sonic vel index in qnode
  integer, dimension(:),   pointer :: n_temperature_j ! temp. index in qnode
  integer, dimension(:),   pointer :: n_energy_j      ! energy index in qnode
  integer, dimension(:),   pointer :: n_entropy_j     ! entropy index in qnode
  integer, dimension(:),   pointer :: n_cv_j          ! cv index in qnode
  integer, dimension(:),   pointer :: n_eta_j         ! cond. index in qnode
  integer, dimension(:),   pointer :: n_amu_k         ! visc. index in qnode
  integer, dimension(:),   pointer :: n_dif_i         ! diff. index in qnode
  integer, dimension(:),   pointer :: n_enthalpy_ij   ! enthalpy_ij index
  integer, dimension(:),   pointer :: n_pressure_jac  ! press_jac index

  logical :: chem_on              ! true if species source terms active
  logical :: therm_on             ! true if thermal source terms active
  logical :: rad_use_impl_lines   ! Lines-of-sight in implicit-lines file
  logical :: electrons            ! true if electrons present
  logical :: perfect = .false.    ! this is the default
  logical :: vorticity_based = .false. ! To use vorticity for the prod. term
  logical :: multi_component_diff ! true by stefan maxwell eqns sub-iter
  logical :: algebraic_turb_on = .false. ! To use algebraic turbulence model
  logical :: algebraic_turb_insert = .false. ! To insert algebraic turb
                                             ! model in restart
  logical :: algebraic_turb_delete = .false. ! To delete algebraic turb
                                             ! model from restart
  logical :: baldwin_lomax_on  = .false. ! To use Baldwin-Lomax alg turb model
  logical :: cebeci_smith_on   = .false. ! To use Cebeci-Smith  alg turb model

  real(dp)    :: rf_inv = 1._dp    ! inviscid relaxation factor
  real(dp)    :: rf_vis = 1._dp    ! viscous  relaxation factor
  real(dp)    :: rf_chem = 1._dp   ! chemical source reduction factor
  real(dp)    :: eigzero              ! eigenvalue limiter factor for flux
  real(dp)    :: eigzero_imp          ! eigenvalue limiter factor for Jacobian
  real(dp)    :: v_ref, rho_ref, l_ref, temperature_ref
  real(dp)    :: sutherland1, sutherland2, prandtl
  real(dp)    :: cpiv_min_factor ! factor applied to cpi, keep cpiv above floor

  integer :: chem_flag  ! 1=yes, 0=no
  integer :: therm_flag ! 1=yes, 0=no

  real(dp)    :: alpha_realizability = 0._dp
  logical     :: jb_rough = .false.
  logical     :: law_of_the_wall = .false.
  logical     :: vuong_coakley = .false. !Eddy viscosity
  logical     :: sst_sust = .false.   ! SST with controlled decay term
  integer     :: turb_cond_model      ! 0:Turb. Prandtl number
                                      ! 1:Algebraic/Reynolds stress model
  integer     :: turb_comp_model      ! 0:no compressibility correction
                                      ! 1:Wilcox compressibility correction

  real(dp)    :: prandtl_turb         ! Turbulent Prandtl number
  real(dp)    :: schmidt_turb         ! Turbulent Schmidt number

  real(dp)    :: turb_int_inf         ! free-stream turbulence intensity
  real(dp)    :: turb_vis_ratio_inf   ! free-stream turbulence viscosity ratio
  real(dp)    :: prod_to_des_limit = 20._dp ! production to destruction limit
  real(dp)    :: tke_amb, omega_amb

  character(10) :: gas_radiation      ! Control radiation coupling

end module generic_gas_map

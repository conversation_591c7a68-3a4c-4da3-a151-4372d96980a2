module flow

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

!------------------------------------------------------------------------------
! NASA/LaRC, Computational Aerosciences Branch, D302
!------------------------------------------------------------------------------
!
! MODULE: flow
!
!> <AUTHOR> FUN3D Development Team
!
! DESCRIPTION:
!> This module exposes the main entry points into the flow solver.
!
! REVISION HISTORY:
!------------------------------------------------------------------------------

  use complex_functions,     only : o
  use big_angle_set,         only : big_angler
  use solver_data,           only : grid, hiro_grid, mass, soln, crow,         &
                                    relaxation
  use line_adaptation,       only : set_adapt_lines
  use line_algebraic_turb,   only : set_algebraic_turb_lines
  use radiation,             only : read_radiation_lines_of_sight
  use string_utils,          only : int_to_s
  use wu_defs,               only : times
  use defect_correction,     only : defect_correction_local
  use solve_box,             only : grid_box_scaling, set_solve_box,           &
                                    reference_q_read, reference_q_write
  use reference_q,           only : read_reference_q, write_reference_q
  use cc_defs,               only : ccrecon
  use exact_defs,            only : ic_exact, truncation_error,                &
                                    output_symmetry_residuals
  use exact,                 only : set_forcing
  use nml_exact,             only : echo_nml_exact
  use check_discretization,  only : resid_check, lisbon_check, hm_check
  use check_solves,          only : check_ia_ja, check_g2m, check_xfer
  use eqn_groups,            only : process_eqn_groups
  use grid_types,            only : raw_grid_data_type
  use grids,                 only : nullify_grid, set_grid_origin_cc
  use parallel_embed,        only : embed_the_grid
  use kinddefs,              only : dp
  use nml_code_run_control,  only : iterwrt, alpha_sweep,                      &
                                    cycle_increment, cycle_write_convergence,  &
                                    restart_read_write,                        &
                                    absolute_stopping_tolerance,               &
                                    relative_stopping_tolerance, no_restart
  use cfl_defs,              only : face_realizability, unrealizable, hanim
  use nml_generic_gas_map,   only : shuffle_enabled
  use info_depr,             only : ngrid, cc_primal, ntt, embed_grid,         &
                                    write_mesh, write_mesh_project,            &
                                    complex_mode, twod, testing, skeleton,     &
                                    alpha, time_timestep_loop, simulation_time,&
                                    print_conditional, fmg_level, adapt, ncyc, &
                                    ivisc, pp_cmd_stats,                       &
                                    track_group_forces, local_moment_centers
  use nml_fwh_acoustic_data, only : fwh_data_freq
  use nml_nonlinear_solves,  only : itime, dt
  use nml_noninertial_reference_frame, only : noninertial
  use adjoint_switches,      only : timedep_adj_frozen
  use debug_defs,            only : bp_solution, grid_measures, hiro_agglom,   &
                                    test_freestream, extract_special_line,     &
                                    write_resmax_files
  use nml_debug,             only : sync_dof_global, print_memusage
  use lsq_defs,              only : nc_mapped_lsq
  use bc_util,               only : setup_test_freestream, write_aflr3_mapbc
  use cut_types,             only : cut_cell_activated, cut_cell_visualize
  use cut_utils,             only : cut_cell_preprocess
  use cut_visualizations,    only : cut_cell_visualization
  use design_types,          only : design_type, cpstar_data, design_run
  use design_io_helpers,     only : set_up_design ! complex join point
  use designs,               only : write_design, evaluate_function_values,    &
                                    free_design, slice_for_cpstar
  use profiles,              only : find_profiles
  use inviscid_flux,         only : iflim, mass_flux, mass_flux_bc
  use lmpi,                  only : lmpi_nproc, lmpi_id, lmpi_master,          &
                                    lmpi_start, lmpi_die, lmpi_synchronize,    &
                                    lmpi_conditional_stop, lmpi_bcast,         &
                                    lmpi_finalize, have_multiple_comms
  use lmpi, only : lmpi_io => lmpi_mpiio_type
  use lmpi_app,              only : lmpi_set_grid_level, lmpi_set_xfer01,      &
                                    lmpi_set_max_grid_level, lmpi_app_time,    &
                                    lmpi_set_defaultghostlevel,                &
                                    lmpi_write_stats
  use pparty_preprocessor,   only : pparty_preprocess, pparty_setup_stuff
  use pparty_metis,          only : verify_in_sequence
  use test_reconstruction,   only : reconstruction_dilemma
  use lsq_nc,                only : nlsq_max_edges, nlsq_lu
  use cgamma_data,           only : cgamman
  use cgamma_util,           only : deallocate_partial_cgamma_data
  use solution,              only : nullify_soln, set_up_solution, rrest,      &
                                    rqtimeavg, set_global_scalars,             &
                                    soln_dump, set_up_neq, set_viscous_method, &
                                    setup_timedep_qfiles, write_timedep_qfiles,&
                                    close_timedep_qfiles, read_restart_map_data
  use solution_globals,      only : set_up_global_bndry_data, slice_bndry_data,&
                                    deallocate_global_bndry_data
! use solidity,              only : test_solidity
  use solution_writes,       only : print_status, output_global_surface_data,  &
                                    animation_requested, remove_slice_data,    &
                                    output_tavg_surface_data, solution_output, &
                                    fwh_global_surface_data, write_vol_tec,    &
                                    end_native_volume_data_write
  use gcr_util,              only : convergence_content, convergence_history,  &
                                    start_convergence_history
  use multigrid_tec,         only : status_turb, forces_vs_h,                  &
                                    detail_status_turb,                        &
                                    max_residual_locations, change_alpha,      &
                                    line_plot_driver, special_line_tecplot
  use flow_initialization,   only : init_flowfield, data_transfer,             &
                                    special_bc_reset, init_freestream
  use thermodynamics,        only : physics_modules_dummy
  use io,                    only : readme ! complex join point
  use io,                    only : prior_iters,                               &
                                    check_project_names,                       &
                                    relax_schedule_gridmove,                   &
                                    write_timestep_timing
  use grid_motion,           only : update_moving_mesh, static_grid_transforms,&
                                    reposition_grid, set_slice_transforms,     &
                                    initialize_moving_grid,                    &
                                    update_mesh_metrics, mesh_has_been_updated,&
                                    update_surface_mesh
  use aeroelastic,           only : ae_pred, ae_corr, setup_modal_aeroelastic, &
                                    structural_model, initialize_ae_hist,      &
                                    write_aero_loads_to_file
  use sixdof,                only : cleanup_sixdof
  use nml_grid_motion,       only : six_dof, grid_motion_attribute,            &
                                    n_moving_bodies
  use grid_motion_helpers,   only : set_up_moving_body,                        &
                                    initialize_a_moving_body,                  &
                                    initialize_all_moving_bodies,              &
                                    set_specified_rigid_motion_data,           &
                                    check_if_grid_velocity_needed,             &
                                    static_mesh_deform_setup,                  &
                                    static_mesh_deform
  use nml_grid_transform,    only : static_grid_transform
  use nml_mdo_surface_data,  only : massoud_output_freq, aero_loads_output_freq
  use grid_symmetry_cc,      only : set_up_symmetry_cc
  use grid_metrics,          only : compute_dual_metrics
  use check_symmetry,        only : y_res_check, z_res_check, x_res_check
  use node_avg_cc,           only : set_up_node_averaging, set_crow_avg_to_nodes
  use averagings,            only : set_averaging
  use inviscid_flux,         only : invoke_mapped_lines, tecplot_mapped_lines
  use structured_lines,      only : set_mapped_lines
  use gradient_driver,       only : sumgs_variable
  use lsq_cc,                only : set_up_lsq_cc
  use deconstruction,        only : initialize_deconstruction
  use refine_adaptation_input,only: adapt_library, ladapt_freq, ladapt_max,    &
                                    sfadapt_grdspd, adapt_metric_from_file
  use bcs,                   only : bc_init_nc, enable_bc_9997                 &
                                  , bc_setup_wall_function_nodes
  use bc_gen,                only : bc_init_gen, bc_init_plenum,               &
                                    bc_init_fixed_in
  use bc_cache_cc,           only : set_bcc, allocate_qt
  use timings,               only : timing, timer, checkpoint, wall_cr_inv,    &
                                    time_moving_grid, count_max, nrollovers
  use multigrid_util,        only : reset_fmg_track, mg_initialization,        &
                                    zero_fas_accounting, fmg_track_total,      &
                                    set_rwu_time
  use restrict_prolong,      only : restrict_prolong_driver, heap_sort_driver, &
                                    restrict_prolong_test, sr_matrix_order_setup
  use multigrid_verify,      only : nonlinear_multigrid_convergence
  use multigrid_util_nc,     only : fmg_prolongation_nc
  use multigrid_util_cc,     only : fmg_prolongation_cc
  use dual_agglom_driver,    only : dual_agglomeration
  use cell_agglom_driver,    only : cell_agglomeration
  use allocations,           only : my_alloc_ptr
  use timeacc,               only : stage_value_predictor, temporal_backplanes,&
                                    timestep_scalars, set_simulation_time,     &
                                    new_timestep, update_temporal_backplanes,  &
                                    comp_qtimeavg, init_grid_backplanes
  use nml_time_avg_params,   only : itime_avg, use_prior_time_avg
  use bc_names,              only : bc_explicitly_initialized,                 &
                                    bc_initialized_solid, dirichlet_discrete,  &
                                    viscous_wall_function, viscous_wf_trs,     &
                                    farfield_shkfit
  use comprow,               only : set_up_comprow_flow, fill_fia_fja
  use debug_defs,            only : composite_jacobian_lhs,                    &
                                    composite_jacobian_rhs,                    &
                                    check_defect_correction,                   &
                                    write_q_boundary_discrete,                 &
                                    ntt_for_jacobian_check,                    &
                                    multigrid_timing_file
  use composite_jacobian,    only : check_cmp_jacobians
  use survey_matrix,         only : survey_jacobians
  use relax_steady,          only : steady_relax=>relax
  use relax_unsteady,        only : unsteady_relax=>relax
  use multiscale,            only : allocate_multigrid, correction_scheme,     &
                                    fas_scheme, ir_passes_line, ir_passes,     &
                                    dof_coarsest_grid_target, ngrid_actual,    &
                                    reset_multigrid_targets,                   &
                                    prolong_order,                             &
                                    meanflow_fas, turbulence_fas,              &
                                    separate_fas_turbulence, fas_pass
  use multigrid_ideal,       only : check_ideal_residuals, deallocate_smoothing
  use multigrid_cs,          only : reset_initial_qset
  use multigrid_fas,         only : fas
  use force_driver,          only : compute_forces, set_node_nearest
  use forces_hinge,          only : group_forces_hinge
  use forces,                only : group_forces
  use input_sanity,          only : check_input_sanity, check_compatibility
  use code_status,           only : code_id, flow_code_id
  use twod_util,             only : setup_2d, check_on_plane, copy_twod
  use adjust_alphas,         only : adjust_alpha, cl_const
  use system_extensions,     only : se_sleep, se_flush, se_shell, se_wall_time
  use file_utils,            only : rm
  use rotors,                only : write_source_grid, init_rotor_source,      &
                                    advance_rotor_blade, source2node_assoc,    &
                                    irotwrt, rotor_flag
  use rotor_motion,          only : extract_comprehensive_loads,               &
                                    write_comprehensive_loads,                 &
                                    output_comprehensive_loads,                &
                                    rotor_performance, set_up_rotor_motion
  use nml_rotor_data,        only : comprehensive_rotor_coupling,              &
                                    overset_rotor
  use fsi_coupling_tight,    only : advance_fsi_tight, finalize_fsi_tight
  use hiarms_act_disk,       only : hiarms_init, hiarms_source2node,           &
                                    hiarms_write_source, hiarms_save_flow,     &
                                    hiarms_get_loads, hiarms_finalize,         &
                                    hiarms_flag, nrotor_input
  use noninertials,          only : setup_nonin, setup_nonin_observer
  use moves,                 only : snap_grid
  use timeacc_coeffs,        only : set_time_coeffn,set_time_coeff2, dt_vec,   &
                                    stage, t_stage, back_planes
  use nml_overset_data,      only : overset_flag, dci_io
  use overset_defs,          only : interpolate_fringes
  use dirtlib,               only : init_overset, get_donor_details,           &
                                    overset_update, trim_overset_stencils,     &
                                    init_dirtlib_communication,                &
                                    sync_global_variables
  use utilities,             only : check_for_stop
  use moving_body_types,     only : moving_body, observer,                     &
                                    specified_rigid_motion
  use grid_metrics,          only : get_grid_measures
  use debug_verify,          only : history_verify, start_verify
  use triage,                only : triage_residuals, truncation_errors
  use nml_global,            only : document_namelist, ngrid_request,          &
                                    write_block_data, grids_to_read,           &
                                    restart_level, boundary_animation_freq,    &
                                    volume_animation_freq, irest, moving_grid, &
                                    grid_motion_only, nsequence, slice_freq,   &
                                    boundary_animation_freq_tavg
  use multigrid_defs,        only : fmg_cycles_request,                        &
                                    fmg_prolong, fmg_levels_request,           &
                                    ideal_relaxation, ideal_coarse_grid
  use lsq_augment_cloud,     only : modify_lsq_stencil
  use rtiming,               only : rtime, rtime_set, rtime_mark
  use flux_constants,        only : set_flux_constants
  use lsq_constants,         only : set_lsq_constants
  use hiro_mechanics,        only : hiro_agglomeration, count_blocks
  use suggar_info,           only : fun3d_id_l2g, world_comm, is_fun3d
  use suggar,                only : overset_connectivity, init_libsuggar
  use split_comm,            only : split_communicator
  use skewness,              only : set_cell_skewness, set_cell_skewness_nc
  use cgamma_cc,             only : set_cgamma_cc
  use cgamma_nc,             only : set_cgamma_nc
  use distance_function,     only : compute_distance_function
  use grid_metrics,          only : lref_for_flux_limiters,                    &
                                    lref_for_flux_limiters_cc
  use periodics,             only : load_periodic_data, periodic
  use multiblocks,           only : multiblock, find_multiblock_pairs
  use generic_gas_map,       only : n_turb_g, algebraic_turb_on,               &
                                    rad_use_impl_lines
  use boundary_writes_cc,    only : output_boundary_cf_cc
  use sampling_main,         only : survey
  use check_allowable_exact, only : check_allowable_bc
  use tecplot_io_helpers,    only : set_strand_usage, bypass_solver
  use nml_relax_schedule,    only : relax_schedule_linear
  use cell_reynolds,         only : set_cell_re
  use global_image,          only : global_image_ugrid
  use bc_strong_nc,          only : write_q_boundary_nc, read_q_boundary_nc,   &
                                    bc_strong_q_nc
  use turb_bc_nc,            only : bc_strong_turb_q_nc
  use pundit,                only : pundit_initialize, pundit_register_data,   &
                                    pundit_perform_connectivity, pundit_flag,  &
                                    pundit_write_output, pundit_solution_update
  use massoud,               only : write_mdo_surface_data, set_up_massoud,    &
                                    write_massoud_file
  use nml_project,           only : project_names
  use nml_vortex_generator,  only : nvg
  use vortex_generators,     only : setup_vgs
  use ssdc_interface,        only : ssdc_init, ssdc_timestep,  mask_ssdc,      &
                                    ssdc_overset_update, all_ssdc
  use sfe_interface,         only : sfe_init, sfe_timestep

  use nml_governing_equations, only : ssdc_flag, max_component_grids,          &
                                      discretization, sfe_flag
  use nml_boundary_conditions, only : plenum_id, fixed_in_id
  use sampling_output,         only : end_fwh_write
  use dcif,                    only : init_dcif, overset_update_nodirt

  implicit none

  private

  public :: initialize_project, initialize_data, post, iterate
  public :: initialize_data1, initialize_data2
  public :: step_pre, step_solver, step_post, step_initialized, update_mesh
  public :: iter, fl, fas_cycles, istop, dump_solution
  public :: start_of_timestep_loop, end_of_timestep_loop

  type(design_type)                                   :: design
  type(raw_grid_data_type),save                       :: raw_grid_data

  integer :: eqn_set                   ! local eqn_set
  integer :: i, ib, eq, iter           ! Loop indices
  integer :: istop, ierr = 0           ! Wanna stop? Need to stop?
  integer :: fl, fas_cycles
  integer :: skeleton_hold, hold
  integer :: dof_check, output_target
  integer :: starting_time             ! Time started (wallclock)
  integer :: delta_clicks              ! Time started (wallclock)
  integer :: alpha_flag                ! Signal alpha change

  integer, dimension(:), pointer :: eqn_mask ! mask off certain eqns

  real(dp) :: simulation_time_atn    ! Simulation time at
                                     ! beginning of timestep
  real(dp) :: start_of_timestep_loop ! Time timestep loop started
  real(dp) :: end_of_timestep_loop   ! Time timestep loop finished
  real(dp) :: time_increment         ! Time taken by current timestep
  real(dp) :: rmschk = 0.0_dp
  real(dp), dimension(2) :: relax_field

  logical :: use_inviscid_bndry, skip_min_wall_spacing

  character(len=80) :: flow_dir
  character(len=80) :: nml_path
  character(len=80) :: command

  logical, parameter :: mpi_debug_sleep   = .false.
  logical, parameter :: timing_main = .false.
  logical, parameter :: time_comm   = .false. ! time communication

  logical :: first_time_through = .true.
  logical :: step_initialized = .false.

  logical :: convert_v4_to_stream

  logical :: dbtime = .false.

  logical :: tolerance_reached

  contains


!=============================== INITIALIZE_PROJECT ==========================80
!
! Primary FUN3D initialization/setup operations
!
! DESCRIPTION:
!> Initializes the solver up to and including the processsing of namelist
!> files and command line arguments.  It also initiates the MPI environment
!> for parallel execution.
!> @brief
!> Initalize the solver.
!
!> @param[in] input_nml  An optional name for the input namelist
!>                       (default: "fun3d.nml")
!=============================================================================80

  subroutine initialize_project(input_nml)

    use solution_types,      only : generic_gas, elasticity
    use main_nml,            only : read_fun3d_nml, main_nml_read
    use command_line_parser, only : usage, read_command_line

    character(200), optional, intent(in) :: input_nml

  continue

    relax_field = 0._dp

    if ( timing_main ) call rtime('reset')
    call times('FUN3D-StartUp')

    code_id = flow_code_id

!...Start MPI processes and allow a small nap after startup if desired.

    call lmpi_start
    call usage()

    if (mpi_debug_sleep) then
      write(*,*) 'proc', lmpi_id, 'Sleeping...'
      call se_sleep(60)
      write(*,*) 'proc', lmpi_id, 'Awake...'
    endif

    call checkpoint(starting_time)

!...Read primary input parameters (but not grid or restart data) in this order:
!   (1) namelist (fun3d.nml). (2) command_line.

!...Read and echo input file.

    flow_dir = ''
    if ( .not.present(input_nml) ) then
      nml_path = 'fun3d.nml'
    else
      nml_path = trim(input_nml)
    endif

    call read_fun3d_nml( eqn_set, raw_grid_data, nml_path )

!...Make sure we have physics_modules for generic gas eqn_set

    if ( generic_gas == eqn_set .and. physics_modules_dummy() ) then
      if (lmpi_master) write(*,*) 'eqn_set = ', eqn_set
      write(*,*)     " **"
      write(*,*)     " **"
      write(*,*)     " ** This version of FUN3D does not have"
      write(*,*)     " ** the generic gas physics modules. Remove"
      write(*,*)     " **   eqn_type = 'generic'"
      write(*,'(3a)')" ** from ",trim(nml_path),"."
      write(*,*)     " **"
      write(*,*)     " **"
      call lmpi_conditional_stop(1, &
        'eqn_set incompatible with dummy physics modules')
    end if

    ngrid = ngrid_request

!...Read namelists.

    call main_nml_read(eqn_set,flow_dir,nml_path)

!...Read any command line options.

    call read_command_line()

!...Read/set linear and possibly nonlinear relaxation schedules.

    if ( eqn_set == elasticity ) then
      call relax_schedule_gridmove(relaxation,flow_dir)
    else
      allocate(relaxation%schedule(6))
      call relax_schedule_linear( relaxation )
    endif

    if (print_memusage) dbtime = .true.

  end subroutine initialize_project

!================================ INITIALIZE_DATA ============================80
!
! FUN3D initialization/setup operations
!
! DESCRIPTION:
!> Initializes solver data required prior to populating the grid
!> and soln types.
!> @brief
!> Initalize basic solver data.
!
!> @param[in] brun  Flag to signal success of data initialization.  If .true.
!>                  then processing can continue safely.  If .false. then
!>                  the solver should be stopped.
!
!=============================================================================80

  subroutine initialize_data(brun)

    logical, intent(out) :: brun

  continue

    call initialize_data1()
    call initialize_data2(brun)

  end subroutine initialize_data


!================================ INITIALIZE_DATA1 ===========================80
!
! Basic FUN3D initialization/setup operations; the scope of "basic" is intended
! encompass all the things needed before we can fill the grid and soln types
!
! Given the big ball of mud, this distinction is murky at best
!
!=============================================================================80

  subroutine initialize_data1()

    use main_nml,             only : write_namelist_docs
    use io,                   only : project_rootname
    use nml_project,          only : set_project_names
    use nml_code_run_control, only : irs_flag

  continue

!...Set grid names on levels (maybe changed from nml via command line or Python)

    call set_project_names(project_rootname)

!...Echo namelists.

    call write_namelist_docs(6)

    call split_communicator()

    skeleton_hold = skeleton

    bypass_solver = .false.

!...If we're planning to convert v4 restart files to stream IO, reset lmpi_io
!...to 0 until after we've done the restart

    convert_v4_to_stream   = .false.
    if (lmpi_io == 3) then
      if (lmpi_master) then
        write(*,'(2a)')' WARNING: flow solve will be bypassed; will convert',  &
                       ' v4 restart files to stream IO'
        write(*,*)
      end if
      convert_v4_to_stream = .true.
      bypass_solver        = .true.
      ncyc    = 1 ! for safety
      lmpi_io = 0
    end if

    if (ncyc == 0) then
      if (animation_requested(1) .or. irs_flag) then
        if (lmpi_master) then
          write(*,'(2a)')' WARNING: flow solve will be bypassed; only flow',   &
                         ' visualization output will be generated'
          write(*,*)
        end if
        bypass_solver = .true.
        ncyc          = 1      ! for safety
        if (irest == 0) then   ! must have something to visualize !
          if (lmpi_master) then
            write(*,'(2a)')' Stopping...need to read a restart file in order', &
                           ' dump out flow visualization data if steps = 0 !'
            call se_flush()
          end if
           call lmpi_die
        end if
      else
        if (lmpi_master) then
          write(*,'(2a)')' Stopping...request to perform zero time steps only',&
                         ' allowed if some flow viz output is also requested'
          write(*,'(a)') ' via, e.g  --animation_freq'
          call se_flush()
        end if
         call lmpi_die
      end if
    end if

    if ( adapt .and. .not. ( irest == 1 .or. ladapt_freq /= 0 .or. &
                             adapt_metric_from_file /= '' ) ) then
      if (lmpi_master) &
        write(*,*)' Stopping... set namelist restart_read = "on" with --adapt'
      call lmpi_conditional_stop(1,'set restart_read = "on" with --adapt')
    end if

    fun3d_only_0 : if (is_fun3d) then

!.....Ensure grid part files exist if we are reading them.

      if ( raw_grid_data%read_part_files ) then
        istop = 0 ! Assume all names/files are valid
        if (lmpi_master) then
           istop = check_project_names(1,project_names)
           if (istop > 0) then
             write(*,*)'Not all grids to read are present. The first missing is'
             write(*,*)trim(project_names(istop))
           end if
        end if
        call lmpi_conditional_stop(istop)

        if ( lmpi_master .and. hanim ) then
          write(*,*) ' complex_mode=',complex_mode,' processors=',lmpi_nproc
        endif

      end if

!.....Preliminary multigrid setup ; allocate primary derived types.

      ngrid = ngrid_request

      if (skeleton > 0 .and. ngrid > 1 ) then
        write(*,*) 'Initiating FMG/FAS/CS MULTIGRID.'
        write(*,*) ' .................grids_to_read =',grids_to_read
        write(*,*) ' .........................ngrid =',ngrid
        write(*,*) ' ................agglomerations =',ngrid-grids_to_read
      endif

!.....Set a few things if this is a case that requires mesh deformation
!     outside of the usual "moving_grid" sceneario: static aeroelastics
!     steady ablation, etc.

      call static_mesh_deform_setup()

!.....Sanity check on grid-motion items before allocations

      if ((.not. static_mesh_deform) .and. (.not. moving_grid)) then
        n_moving_bodies = 0
        grid_motion_attribute = 'static'
      end if

    end if fun3d_only_0

    call lmpi_set_max_grid_level(ngrid)

    call lmpi_bcast(n_moving_bodies, fun3d_id_l2g(0), world_comm)

    allocate(grid(ngrid))                         ! grid data
    allocate(soln(ngrid))                         ! solution data
    allocate(crow(ngrid))                         ! compressed row storage data
    allocate(mass(1,ngrid))                       ! dummy for res_flow and fas
    call allocate_multigrid(ngrid)                ! multigrid array allocations
    if ( nc_mapped_lsq ) allocate(cgamman(ngrid))

    allocate(moving_body(max(1,n_moving_bodies))) ! moving body/grid data
    call initialize_all_moving_bodies(moving_body)
    call initialize_a_moving_body(observer)
    allocate(specified_rigid_motion(n_moving_bodies))
    call set_specified_rigid_motion_data()

    fun3d_only_1 : if (is_fun3d) then

!.....Set some global scalar data and initialize a few items.

      do i=1,ngrid
        call nullify_grid(grid(i))
        call nullify_soln(soln(i))
        grid(i)%project = trim(project_names(i))
        grid(i)%igrid   = i
        if ( lmpi_master .and. (.not.no_restart) .and. (lmpi_io /= 0) ) then
          command = 'touch ' // trim(grid(i)%project) // '.flow'
          call se_shell(command)
        endif
      end do

      call set_grid_origin_cc(cc_primal, grids_to_read, ngrid, grid)

      call set_viscous_method(grid,soln)

      do i = 1, ngrid
        call set_global_scalars(soln(i), eqn_set)
      end do

      call init_freestream( soln(1)%eqn_set, soln(1)%n_tot )

    end if fun3d_only_1

  end subroutine initialize_data1


!================================ INITIALIZE_DATA2 ===========================80
!
! Secondary FUN3D initialization/setup operations; after these we can start
! the flow solution
!
! Given the big ball of mud, this distinction is murky at best
!
!=============================================================================80

  subroutine initialize_data2(brun)

    use solution_types,      only : generic_gas, compressible, incompressible
    use visit,               only : cop_initialize=>initialize
    use solution,            only : wrest
    use nml_volume_output,   only : read_nml_volume_output
    use nml_boundary_output, only : read_nml_boundary_output
    use nml_sampling_output, only : read_nml_sampling_output
    use main_nml,            only : write_namelist_docs
    use lmpi_app,            only : lmpi_xfer
    use openacc,             only : use_openacc, openacc_initialize
    use string_utils,        only : sub_string
    use inviscid_flux,       only : mean_decouple
    use grid_motion_helpers, only : need_grid_velocity
    use flow_initialization, only : initialize_backplanes

    logical, intent(out) :: brun

    logical :: complex_testing

    integer :: maxbcnodes

  continue

!...Perform some basic "sanity checks" on the input data and options.

    call check_input_sanity(flow_dir,eqn_set)

!   for the split-communicator scenario, ensure we have current
!   info on all processors for dirtlib/suggar

    if ( have_multiple_comms ) then
      call lmpi_bcast(overset_flag,  fun3d_id_l2g(0), world_comm)
      call lmpi_bcast(overset_rotor, fun3d_id_l2g(0), world_comm)
    end if

    call init_libsuggar()

    call init_dirtlib_communication()

    fun3d_only_1 : if (is_fun3d) then

!.....If generic_gas & restart, read tdata and restart file and compare species.
!     Shuffle: Extract restart generic gas info, validate, create map.
!     See read_restart_map_data for retained values. Do this at an early point
!     to compare tdata species with restart file. Detect errors ASAP

      if ((eqn_set == generic_gas).and.((irest == 1).or.(irest == -1))) then
         if (lmpi_master) write(*,*)" ...Checking # of species for generic_gas."
         call read_restart_map_data('',grid(1),soln(1))
      end if

!....Set some timestep data.

      call timestep_scalars()
      dt_vec(:) = dt

!.....Major setup operations: read grid partitions, initialize solution,
!     read restart files, memory allocation, special options setup, etc

!.....Open and close debug_verify file.

      call start_verify()

      if ( design_run ) call set_up_design(soln(1)%eqn_set, itime, design)

      if ( ngrid == 1 .and. hanim ) then
        call start_convergence_history( grid(1)%project, 1, ierr )
        call lmpi_conditional_stop(ierr,'start_convergence_history:initialize')
      endif

    end if fun3d_only_1

!...If running a split communicator, share the value of ngrid
!   so other ranks can execute following loop in sync with FUN3D ranks

    if ( have_multiple_comms ) then
      call lmpi_bcast(ngrid, fun3d_id_l2g(0), world_comm)
    endif

!...Set flag for grid-speed terms

    call check_if_grid_velocity_needed()

    read_and_init_levels : do i = 1, ngrid

      fun3d_only_1b : if (is_fun3d) then

        call lmpi_set_grid_level(i)

        if ( i <= grids_to_read ) then

           call times('GridRead',i)
           if (.not.raw_grid_data%read_part_files) then

             grid(i)%cc = cc_primal
             call pparty_preprocess('',raw_grid_data,grid(i))
             grid(i)%cc = cc_primal
             if ( embed_grid ) call embed_the_grid( grid(i), .true. )
             if (.not.associated(grid(i)%fptr).or.(grid(i)%cc)) then
                call pparty_setup_stuff(grid(i))
                ! CC DANA TBD
                grid(i)%nface_augmentors = -1000
                deallocate(grid(i)%flsq_ja)
                allocate(grid(i)%flsq_ja(1)); grid(i)%flsq_ja = 0
                if ( complex_mode .and. i == 1 ) then
!                 propagate potential complex alpha perturbation to freestream
                  call init_freestream( soln(i)%eqn_set, soln(i)%n_tot )
                end if
             end if

           else

             call readme(soln(i)%eqn_set, grid(i), '')

           end if

           if ( lmpi_master ) then
             write(*,*)
             write(*,*) 'Grid read complete'
           end if
           call times('GridRead',i)

!          You can call global_image_ugrid( grid(1), 'checkme.b8.ugrid' )
!          for checking

           grid(i)%cc = cc_primal

           if (pp_cmd_stats >= 2) call lmpi_write_stats(grid(i)%project,       &
                                       grid(i)%nnodes0, grid(i)%nnodes01,      &
                                       grid(i)%nedgeloc, grid(i)%nedge)

           if (grid(i)%cc.and..not.raw_grid_data%read_part_files) &
              call lmpi_set_defaultghostlevel(4)

           if ( i == 1 ) call sync_dof_global( grid(1) )

           if ( cpstar_data ) call slice_for_cpstar(grid(i))

           if (write_block_data) call count_blocks( grid(i) )

           call setup_2d(grid(i))

!..........Perform any up-front grid manipulations

           if ( ( design_run .and. design%nbodies > 0 ) .or. snap_grid ) then
             call reposition_grid(grid, flow_dir, nml_path, .false.)
           endif

           if (static_grid_transform) then
             call static_grid_transforms(grid(i), moving_body)
           endif

           if ( ngrid > 1 ) call heap_sort_driver(grid(i))

           if ( i > 1 ) then
             call restrict_prolong_driver( i, ngrid, grid )
           endif

        else

!.........Agglomerate (if we have not reached the target).

          dof_check = grid(i-1)%nnodesg
          if ( cc_primal ) dof_check = grid(i-1)%ncellg
          if ( dof_check <= dof_coarsest_grid_target ) then
            call reset_multigrid_targets( i-1 )
            exit read_and_init_levels
          endif

          if ( cc_primal ) then
            call cell_agglomeration( i, ngrid, grid, soln, crow)
          else
            call dual_agglomeration( i, ngrid, grid, soln, crow)
            if (write_block_data) call count_blocks( grid(i) )
          endif

          call setup_2d(grid(i))

          if ( prolong_order(i) > 0 ) then
            call heap_sort_driver(grid(i))
            call restrict_prolong_driver( i, ngrid, grid )
          endif

        endif

!.......Set up and (possibly) correct metrics for symmetry planes

        if ( (.not. cc_primal) .and. (grid(i)%origin /= 3) ) then
          call compute_dual_metrics(grid(i), moving_grid)
          call init_grid_backplanes(grid(i))
        end if

!.......Stop agglomeration if requested.

        if ( hiro_agglom ) call hiro_agglomeration(grid(1), hiro_grid)

!.......Perform cut cell operations if requested

        if (cut_cell_activated) call cut_cell_preprocess(grid(i))

!.......Size soln equations

        call set_up_neq( grid(i), soln(i) )

!.......Dump grid measures if requested

        if ( grid_measures ) then
          call get_grid_measures(grid(i))
          call lmpi_synchronize()
          call lmpi_finalize
          stop
        endif

!.......Scale grid, possibly.

        call grid_box_scaling( grid(i) )

!.......Perform some more sanity checks

        call check_compatibility(soln(i)%eqn_set, i, grid(i), soln(i) )

        if ( twod ) then
          call check_on_plane(grid(i))
          call echo_nml_exact(6)
        endif

        call special_bc_reset( grid(i), soln(i) )

!.......Set skewness for cell-centered.

        if ( grid(i)%cc .and. .not.complex_mode ) then
          call set_cell_skewness( grid(i) )
        endif

        if ( grid(i)%cc .and. ccrecon <= 0 ) call modify_lsq_stencil(grid(i))

!.......Set up compressed row storage arrays (non-agglomerated meshes).

        if ( grid(i)%origin < 3 ) then
          call set_up_comprow_flow(soln(i)%viscous_method, grid(i), crow(i))
        endif

        if ( testing .and. nsequence > 1 .and. .not.grid(i)%cc ) then
          if ( i > 1 .and. i <= nsequence ) then
            call verify_in_sequence( grid(i-1)%project,   &
            grid(i-1)%nnodes0, grid(i-1)%l2g,             &
            grid(i-1)%x,       grid(i-1)%y,   grid(i-1)%z,&
            grid(  i)%nnodes0, grid(  i)%l2g,             &
            grid(i  )%x,       grid(i  )%y,   grid(i  )%z )
          endif
        endif

!.......Check for problems with compressed row storage.

        if ( testing ) call check_ia_ja( grid(i), soln(i), crow(i) )

!.......Set some information about unknowns for timing.

        if ( timing_main ) then
          if ( grid(i)%cc ) then
            call rtime_set( grid(i)%nnodes0, soln(i)%neq0, grid(i)%nface,      &
                            size( crow(i)%ja , 1 ) )
          elseif ( .not.twod ) then
            call rtime_set( grid(i)%nnodes0, soln(i)%neq0, grid(i)%nedgeloc,   &
                            size( crow(i)%ja , 1 ) )
          else
            call rtime_set( grid(i)%nnodes0_2d, soln(i)%neq0,                  &
                            grid(i)%nedgeloc_2d,                               &
                            size( crow(i)%ja , 1 ) )
          endif
        endif

!.......Set cache-friendly bc information and constants for included functions.

        if ( grid(i)%cc ) then
          call set_bcc ( grid(i) )
          call fill_fia_fja( grid(i)%ncell0, crow(i)%ia, crow(i)%ia_ns, &
                             crow(i)%fia, crow(i)%fja,                  &
                             grid(i)%nface, grid(i)%fptr, grid(i)%bcc )
          call allocate_qt( soln(i)%n_q, grid(i)%bcc )
          call set_up_symmetry_cc( grid(i) )
        endif

!.......Get reference length scale when certain flux limiters are being used.

        if ((i == 1) .and. (iflim >= 15) .and. (iflim <= 17)) then
          if (.not. grid(i)%cc) then
            call lref_for_flux_limiters(grid(i))
          else
            call lref_for_flux_limiters_cc(grid(i))
          end if
        end if

!.......Compute the distance function for turbulent cases, or where the mesh
!       is deforming based on elasticity relations or cell-centered.

        if ( ivisc >= 2 .or. n_turb_g > 0 .or.                                 &
             sub_string(grid(i)%grid_motion,'deform') .or. bp_solution )then

          grid(i)%idistfcn = 1
          if (grid(i)%origin < 3) then           ! skip for agglomerated meshes

            use_inviscid_bndry    = .false.
            skip_min_wall_spacing = .false.

            if (ivisc == 0 .and. sub_string(grid(i)%grid_motion,'deform')) then
              use_inviscid_bndry    = .true.
              skip_min_wall_spacing = .true.
            elseif ( bp_solution ) then
              use_inviscid_bndry    = .true.
              skip_min_wall_spacing = .false.
            end if

            if (grid(i)%cc) then
              skip_min_wall_spacing = .true.
            end if

            complex_testing = .false.
            if ( complex_mode .and. testing ) complex_testing = .true.
            call compute_distance_function(grid(i), complex_testing,           &
                              skip_min_wall_spacing_arg=skip_min_wall_spacing, &
                              use_inviscid_boundaries_arg = use_inviscid_bndry)
            if ( .not.grid(i)%cc ) then
              call set_node_nearest( grid(i), crow(i) )
            endif

          end if

        end if

!.......Check skewness for node-centered.

        if ( .not.grid(i)%cc .and. bp_solution .and. .not.complex_mode ) then
          call set_cell_skewness_nc( grid(i) )
        endif

!.......For freestream-preservation tests, set non-symmetry bcs to farfield.

        if (test_freestream) then
          call setup_test_freestream( grid(i)%bc, grid(i)%nbound )
        end if

!.......Set up solution arrays.

        if ( i > 1 ) then
           call set_up_solution(i, grid(i), crow(i), soln(i), soln(1))
        else
           call set_up_solution(1, grid(1), crow(1), soln(1))
        end if

!.......Set up decoupled scheme (for generic gas path)
        if (mean_decouple) then
          call my_alloc_ptr(mass_flux,grid(1)%nedgeloc)
          maxbcnodes = maxval(grid(1)%bc(:)%nbnode)
          call my_alloc_ptr(mass_flux_bc,grid(1)%nbound,maxbcnodes)
        end if

!.......Enable profiles to be taken from analytic soln

        if (i == 1) call enable_bc_9997(grid(i))

!.......Initialize solution throughout the field.

        if(skeleton > 0) then
          write(*,*) 'Initializing solution throughout the field.'
        end if

        call init_flowfield(grid(i), soln(i), nml_path)

!.......Initialize solution on the boundaries (possibly)

        if(skeleton > 0) write(*,*) 'Initialize boundaries.'

        boundary_init : if ( .not.grid(i)%cc .and. &
                              soln(i)%eqn_set == generic_gas ) then

          do ib = 1,grid(i)%nbound
            if( bc_initialized_solid(grid(i)%bc(ib)%ibc) ) then
              call bc_init_gen(grid(i), soln(i), grid(i)%bc(ib), ib)
            endif
            if(plenum_id(ib) > 0)then
              call bc_init_plenum(soln(i)%bcsoln(ib)%jet_inflow,               &
                                  soln(i)%bcsoln(ib)%jet_pressure_jac,         &
                                  soln(i)%bcsoln(ib)%jet_enthalpy_ij, ib)
            else if (fixed_in_id(ib) > 0) then
              call bc_init_fixed_in(soln(i)%bcsoln(ib)%fix_inflow,             &
                                    soln(i)%bcsoln(ib)%fix_pressure_jac,       &
                                    soln(i)%bcsoln(ib)%fix_enthalpy_ij, ib)
            end if
          end do

        else if( ic_exact ) then

          if(skeleton > 0) write(*,*) 'Skipping boundary initialization steps.'

        else if ( .not.grid(i)%cc .and. soln(i)%eqn_set /= generic_gas ) then

          do ib = 1,grid(i)%nbound

          if(skeleton > 0) write(*,'(a,2i6,1x,l2)')                            &
                'Explicit initialization, ib, ibc: ', ib, grid(i)%bc(ib)%ibc   &
                , bc_explicitly_initialized(grid(i)%bc(ib)%ibc, itime)
            if ( bc_explicitly_initialized(grid(i)%bc(ib)%ibc, itime) ) then
              call bc_init_nc(grid(i), soln(i), ib, grid(i)%bc(ib))
            end if

          end do

        end if boundary_init

!.......Ensure any changes in bc solution data are transfered to ghost nodes

        call lmpi_xfer(soln(i)%q_dof)

!.......Now that Q is set, initialize all of the backplane data

        if ( itime /= 0 ) then
          call initialize_backplanes(soln(i)%n_tot, soln(i)%n_turb,            &
                                     size(soln(i)%q_dof,2), soln(i)%q_dof,     &
                                     soln(i)%qatn, soln(i)%qatn1,              &
                                     soln(i)%qatn2, soln(i)%qatn3,             &
                                     soln(i)%qatn4, soln(i)%turb,              &
                                     soln(i)%turbatn, soln(i)%turbatn1,        &
                                     soln(i)%turbatn2, soln(i)%turbatn3,       &
                                     soln(i)%turbatn4, grid(i)%x, grid(i)%z,   &
                                     soln(i)%eqn_set)
        endif

        if(skeleton > 0) write(*,*) 'Set flux constants.'

        call set_flux_constants(soln(i)%eqn_set)

        if ( .not.complex_mode .and. ngrid > 1 ) then
          call set_cell_re( i, soln(i)%eqn_set, soln(i)%dof0,  soln(i)%q_dof,  &
                               soln(i)%amut,    grid(i)%volq,  grid(i)%slen,   &
                               crow(i)%ia,      crow(i)%ia_ns, crow(i)%ja,     &
                               soln(i)%cell_re )
        endif

        call check_allowable_bc( grid(i), soln(i) )

!.......Check for shock fitting bc usage when the shock fitting adaptation
!       library has not been envoked

        if ( .not.grid(i)%cc ) then
          do ib = 1,grid(i)%nbound
            if ( adapt_library /= 'sfline' .and.                              &
                 grid(i)%bc(ib)%ibc == farfield_shkfit ) then
              if ( lmpi_master ) then
                write(*,*) ' '
                write(*,*) ' A shock fitting bc was specified but the'
                write(*,*) ' mesh adaptation option was not set to sfline.'
                write(*,*) ' The adaptation option was = ', adapt_library
                write(*,*) ' Check your namelist input file!'
                call lmpi_die
              end if
            end if
          end do
        end if

!.......Initialize wall function array

        soln(i)%wall_function_node = .false.

        do ib = 1,grid(i)%nbound
          if ( grid(i)%bc(ib)%ibc == viscous_wall_function                     &
          .or. grid(i)%bc(ib)%ibc == viscous_wf_trs                            &
             ) then
          call bc_setup_wall_function_nodes ( soln(i), grid(i)%bc(ib) )
          endif
        end do

!.......Load in discrete boundary data if needed.

        if ( .not.grid(i)%cc ) then
          load_discrete : do ib = 1,grid(i)%nbound
            if ( grid(i)%bc(ib)%ibc /= dirichlet_discrete ) cycle
            call lmpi_conditional_stop(i-1,'igrid/=1:flow.F90')
            call read_q_boundary_nc(grid(i), soln(i))
            if(skeleton > 0) write(*,'(a,2i6,1x,l2)') 'bc_strong_q_nc, ib=', ib
            call bc_strong_q_nc(grid(i), soln(i))
            if ( soln(i)%n_turb > 0 )                &
            call bc_strong_turb_q_nc(grid(i), soln(i))
            exit load_discrete
          end do load_discrete
        endif

!.......Load in periodic node pairs if needed.

        if ( periodic ) then
          call load_periodic_data(grid(i)%nnodes0,grid(i)%nbound,grid(i)%bc,   &
                                  grid(i)%x,grid(i)%y,grid(i)%z,               &
                                  grid(i)%nnodes01)
        endif

!.......Establish multiblock interfaces if needed.

        if ( multiblock ) then
          call find_multiblock_pairs(grid(i)%nbound,grid(i)%bc,grid(i)%x,      &
                                     grid(i)%y,grid(i)%z,grid(i)%nnodes0)
        endif

!.......Set up moving_body data for the flow solver

        if(skeleton > 0) write(*,*) 'Set up moving body.'
        if (i == 1) then
          call set_up_moving_body(grid(i), soln(i))
        end if

      if ( hanim .and. face_realizability ) then
        if ( i == 1 ) allocate(unrealizable(ngrid_actual))
        if (  twod ) then
          if(skeleton > 0) write(*,*) 'Set up unrealizable_edge_counter....',&
                                                grid(i)%nedgeloc_2d
          allocate(unrealizable(i)%edge_counter(grid(i)%nedgeloc_2d))
        else
          if(skeleton > 0) write(*,*) 'Set up unrealizable_edge_counter....',&
                                                grid(i)%nedgeloc
          allocate(unrealizable(i)%edge_counter(grid(i)%nedgeloc))
        end if
        unrealizable(i)%edge_counter(:) = 0
      endif

      end if fun3d_only_1b

!...Initialize the FUN3D - DiRTlib interface

      if ( have_multiple_comms ) then
        call sync_global_variables(flow_dir,nml_path,grid(i),soln(i))
      endif

      if (overset_flag .and. (.not. bypass_solver)) then
        if ( dci_io ) then
          call init_dcif(grid(i), flow_dir)
        else
          call init_overset(grid(i), soln(i), crow(i), .false., raw_grid_data, &
                            flow_dir, ssdc_flag)
        endif
      end if

!...Initialize Pundit interface

      if ( pundit_flag ) then
        call pundit_initialize()
        call pundit_register_data(grid(i),soln(i))
        call pundit_perform_connectivity()
        call pundit_write_output()
      endif

      fun3d_only_1c : if (is_fun3d) then

!.......Set up equation groups for sets of linear equations.

        mask_ssdc_grids : if ( ssdc_flag ) then
          call my_alloc_ptr(eqn_mask, soln(i)%neq0)
          call mask_ssdc(soln(i)%neq0, soln(i)%neq01, eqn_mask, grid(i)%imesh)
          call process_eqn_groups(grid(i), crow(i), soln(i), relaxation, i,    &
                                  flow_dir, nml_path, eqn_mask=eqn_mask)
          deallocate(eqn_mask)
        else mask_ssdc_grids
          call process_eqn_groups(grid(i), crow(i), soln(i), relaxation, i,    &
                                  flow_dir, nml_path)
        endif mask_ssdc_grids

#ifdef HAVE_CUDA
        if (lmpi_master) write(*,*)'    ... Setting up data for GPGPUs.'
        call transfer_iam_jam_eqns(crow(i)%iam, crow(i)%jam,                   &
               size(crow(i)%iam),size(crow(i)%jam),                            &
               soln(i)%eqn_groups(2)%point_dofs,                               &
               size(soln(i)%eqn_groups(2)%point_dofs))
#endif


!.......Check for a problem with g2m.

        if ( testing ) call check_g2m( grid(i), soln(i), crow(i) )

        if ( bp_solution ) then
          call set_averaging( grid(i), crow(i) )
        endif

        if ( adapt_library == 'line' .or.                                      &
             adapt_library == 'sfline' ) then
          call set_adapt_lines( grid(i), crow(i) )
        endif

        if ( algebraic_turb_on )then
          call set_algebraic_turb_lines( grid(i), crow(i) )
        end if

        if ( rad_use_impl_lines )then
          call read_radiation_lines_of_sight( grid(i), crow(i) )
        end if

        if ( grid(i)%cc ) call check_xfer( grid(i), soln(i), crow(i) )

        if ( i > 1 .and. ngrid > 1 ) then
          call sr_matrix_order_setup( i, ngrid, grid, crow )
          call restrict_prolong_test( i, ngrid, grid, crow )
        endif

!.......Get least squares coefficients.

        if ( grid(i)%cc ) then
          call set_lsq_constants( grid(i), soln(i), crow(i) )
          call set_cgamma_cc( grid(i), crow(i) )
          call set_up_lsq_cc( soln(i)%viscous_method, grid(i), crow(i) )

          !...set up node averaging if element structure exists.
          if ( grid(i)%origin < 3 ) then
            call set_up_node_averaging( i, grid(i) )
            if ( soln(i)%viscous_method_lhs == 4 )then
              call set_crow_avg_to_nodes( grid(i) )
            endif
          endif
        else
          if ( nc_mapped_lsq ) then
            call set_lsq_constants( grid(i), soln(i), crow(i) )
            call set_cgamma_nc( grid(i) )
            call nlsq_max_edges(i, grid(i)%nnodes0, grid(i)%symmetry,  &
                                   grid(i)%x, grid(i)%y, grid(i)%z )
            call nlsq_lu(i, grid(i)%nnodes0, grid(i)%symmetry,                 &
                         grid(i)%x, grid(i)%y, grid(i)%z,                      &
                         grid(i)%cgamma, grid(i)%slen,                         &
                         grid(i)%slenxn, grid(i)%slenyn, grid(i)%slenzn,       &
                         grid(i)%nlsq, grid(i)%nbound, grid(i)%bc )
            call deallocate_partial_cgamma_data
          endif
          call sumgs_variable( grid(i), crow(i) )
        endif

!.......Initialize deconstruction array.

        if ( grids_to_read == ngrid ) then
          call initialize_deconstruction( grid(i), pass = 2 )
        else
          call initialize_deconstruction( grid(i), pass = 1 )
        endif

!.......Unit test some stuff related to reconstruction.

        if (testing) then
          call reconstruction_dilemma( grid(i) , soln(i), nml_path )
        end if

!.......Set up massoud arrays for mixed elements - dummies for the moment

        if(skeleton > 0) write(*,*) 'Set up massoud.'
        mass(1,i)%itotal = 0
        call set_up_massoud(mass(1,i))

!.......Set up for modal-based structural model

        if (i == 1) call setup_modal_aeroelastic(grid(i))

!.......Set up for any user-defined vortex generators

        if ( nvg > 0 ) call setup_vgs(grid(i))

!.......Initialize noninertial rotation speeds

        if(skeleton > 0) write(*,*) 'Set up non intertial.'
        if (noninertial) then
          call setup_nonin(grid(i))
          call setup_nonin_observer()
        end if

!.......Initialize the rotor sources

        if(skeleton > 0) write(*,*) 'Set up rotor source.'
        if (rotor_flag) then
          call init_rotor_source(soln(1)%eqn_set, flow_dir)
          call source2node_assoc(grid(1)%nnodes0,                              &
                                 grid(1)%nnodes01,                             &
                                 grid(1)%x, grid(1)%y, grid(1)%z,              &
                                 grid(1)%bc, grid(1)%nbound, grid(1)%l2g)
          call write_source_grid(1, 0)
        end if

!.......Initialize the HI-ARMS rotor sources

        if (hiarms_flag) then
          call hiarms_init(nrotor_input)
          call hiarms_source2node(grid(1)%nnodes0, grid(1)%nnodes01, grid(1)%x,&
                                  grid(1)%y, grid(1)%z, grid(1)%bc,            &
                                  grid(1)%nbound, grid(1)%l2g)
        end if

!.......Read the restart file

        get_restart : if ( ( irest == 1 .and. ngrid == 1 )         .or. &
                           ( irest == 1 .and. i == restart_level ) ) then
          select case (soln(i)%eqn_set)
            case (compressible,incompressible,generic_gas)
              call rrest('', grid(i), soln(i))
            case default
              if(lmpi_master) write(*,*) 'No restart capability...stopping.'
              if(lmpi_master) write(*,*) '...eqn_set=',soln(i)%eqn_set
              call lmpi_conditional_stop(1,'No restart:initialize')
          end select

        end if get_restart

        if ( hanim .and. soln(i)%n_turb > 0 ) then
          call detail_status_turb( grid(i), soln(i) )
         endif

!.......If we're converting v4 restarts to stream, reset lmpi_io now that
!       we're done reading the restart

        if (convert_v4_to_stream) lmpi_io = 2

!.......Initialize reference solution.

        if ( reference_q_read ) call read_reference_q( grid(i), soln(i) )

!.......Read the q-data from time-averaged restart file

        if ( itime_avg == 1 .and. i == 1 .and. use_prior_time_avg ==1 ) then
          if(lmpi_master) write(*,*)'itime_avg before call rqtimeavg', itime_avg
          select case (soln(i)%eqn_set)
            case (compressible,incompressible)
              if(lmpi_master) write(*,*) 'calling rqtimeavg', itime_avg
              call rqtimeavg('', grid(i), soln(i))
            case default
              if(lmpi_master) write(*,*) 'No time-avg capability...stopping.'
              if(lmpi_master) write(*,*) '...eqn_set=',soln(i)%eqn_set
              call lmpi_die
          end select
        end if

!.......Transfer data between processors to ensure everthing starts in synch

        call data_transfer(grid(i), soln(i))

!.......Set on-processor forcing terms and solve_box (two equation sets)

        if ( (soln(i)%eqn_set == compressible) .or. &
             (soln(i)%eqn_set == incompressible) ) then
          call set_forcing( grid(i), soln(i))
          call set_solve_box( grid(i), soln(i), crow(i), i )
        endif

        if ( ngrid > 1 ) then
          call lmpi_set_xfer01( i, size(soln(i)%q_dof,2) )
        endif

!.......Write namelist docs after namelists, command_lines, and 1 grid read.

        if ( i == 1 .and. document_namelist ) &
        call write_namelist_docs(0)

      end if fun3d_only_1c

    end do read_and_init_levels

    fun3d_only_1d : if (is_fun3d) then

      do i=ngrid_actual,1,-1

        call lmpi_set_grid_level(i)

!.......Reset deconstruction array (possibly).

        if ( grids_to_read /= ngrid ) then
          call initialize_deconstruction( grid(i), pass = 2 )
        endif

!.......Write file h_measures...coarsest to finest.

        if ( testing .or. print_conditional .or. ic_exact ) then
          call hm_check(grid(i),soln(i))
        end if

!.......Check and set big_angle logical for node-centered.

        if ( .not.cc_primal ) then
          call big_angler( grid )
        endif

      enddo

!.....Set mapped line information (coarsest to finest), if needed.

      if ( invoke_mapped_lines .or. tecplot_mapped_lines ) then

        do i=ngrid_actual,1,-1
          call set_mapped_lines( grid(i), crow(i) )
        enddo

      endif

!.....If we are going to run a time-dependent adjoint after this,
!.....initialize the files that will store Q at each time level

      if ( timedep_adj_frozen ) call setup_timedep_qfiles(grid(1),soln(1))

!.....Set up infrastructure related to SSDC

      if ( ssdc_flag ) then
        call ssdc_init(nml_path,grid(1),soln(1))
        if ( overset_flag .or. pundit_flag ) then
          call trim_overset_stencils(grid(1)%imesh,max_component_grids,        &
                                     discretization)
        endif
      endif

      if ( sfe_flag ) then
        call sfe_init(nml_path,grid(1),soln(1),crow(1))
      end if


    end if fun3d_only_1d

! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+
! Full Multigrid (FMG) cycle - - -
!
! Note: ngrid_actual is number of available grids...grid(1) is finest
!
! Note: fmg_level is the grid level in the FMG process
!       ...fmg_level==1 is the level of the finest grid
!       ...fmg_level==fmg_levels_request is the level of the coarsest grid
!
! =+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+=+

    call mg_initialization( )

    if(skeleton > 0 .and. is_fun3d) then
      write(*,*) 'Start of FMG cycle..........ngrid_actual =',ngrid_actual
      write(*,*) '                  ....fmg_levels_request =',fmg_levels_request
    endif

!...Initialize Coprocessor interface

    if(is_fun3d) then

      call read_nml_volume_output(soln(1)%n_turb, eqn_set, grid(1)%idistfcn,   &
                                  .false., soln(1)%adim, nml_path)
      call read_nml_boundary_output(soln(1)%n_turb, eqn_set, grid(1)%idistfcn, &
                                  .false., soln(1)%adim, nml_path)
      call read_nml_sampling_output(soln(1)%n_turb, eqn_set, grid(1)%idistfcn, &
                                  .false., soln(1)%adim, nml_path)

      if(skeleton > 0) then
        write(*,*) 'Set up co-processing.'
      endif
      call cop_initialize( grid(1), soln(1), crow(1), raw_grid_data, flow_dir )

    endif

!...Initialize Moving Grid

    if (is_fun3d) then
      call initialize_moving_grid(grid,crow,soln,moving_body,observer,         &
                                  flow_dir,nml_path)
    end if

!...Initialize history data for modal aeroelastic simulations

    if (is_fun3d) then
      if (trim(adjustl(structural_model)) == 'internal') then
        call initialize_ae_hist()
      end if
    end if


!...Check for a quick update of the restart file with immediate exit

    if (shuffle_enabled.and.(restart_read_write == 1)) then
     write(*,*)"restart_read_write found. Writing restart file, then exiting."
     call wrest(grid(1), soln(1), '')
     brun = .false.
!    call lmpi_conditional_stop(1,"Stopping due to Restart_read_write.")
     return
    end if

!...For full multigrid (with mesh sequencing), get solutions on coarser levels,
!   interpolating up to next finer level; when complete, we have an improved
!   initial solution on the finest level

    call mesh_sequencing(brun)

    if ( use_openacc .and. is_fun3d ) then
      call openacc_initialize(grid,soln,crow,need_grid_velocity)
    end if

    call times('FUN3D-StartUp')

  end subroutine initialize_data2


!===================================== POST ==================================80
!
! Post processing and shutdown operations
!
!=============================================================================80

  subroutine post()

    use file_utils,           only : big_endian_io
    use visit,                only : cop_shutdown=>shutdown
    use nml_sonic_boom,       only : nsignals
    use crinkle_cut,          only : get_pressure_signals
    use nml_grid_transform,   only : static_grid_transform
    use openacc,              only : use_openacc, openacc_shutdown
    use nml_code_run_control, only : irs_flag
    use lmpi,                 only : lmpi_die
    use adjoint_switches,     only : write_final_field
    use flow_initialization,  only : write_qnode

    logical :: use_t0_coords_arg

    character(256) :: grid_filename

  continue

    if ( use_openacc .and. is_fun3d ) call openacc_shutdown(grid,soln,crow)

!   output results

    fl = 1
    call fmg_post

    fun3d_only : if (is_fun3d) then

!.....Call IRS post-processing

#if defined(HAVE_IRS)
      if ( irs_flag ) call perform_irs_solve()
#else
      if ( irs_flag ) then
        write(*,*) 'IRS post-processing requested; exec not linked to library.'
        call lmpi_die()
        stop
      endif
#endif

!.....Write out final solution if testing for chaos

      if ( write_final_field ) then
        call write_qnode(grid(1)%nnodes0, soln(1)%q_dof, 'final')
      endif

!.....Shut down Co-processor

      call cop_shutdown()

!.....Shut down Dymore if needed

      if (comprehensive_rotor_coupling == 'fsi_tight') call finalize_fsi_tight

!.....Write Tecplot for cc data

      call output_boundary_cf_cc(grid(1), soln(1))

!.....Write profile information

      call find_profiles(grid(1), soln(1), './')

!.....Evaluate design functions and store current xyz's

      if ( design_run ) then
        call evaluate_function_values(grid(1),soln(1),design)
        if( lmpi_master ) call write_design(design)
        call free_design(design)
      else
        if ( nsignals > 0 ) call get_pressure_signals(grid(1),soln(1))
      endif

      if ( write_mesh ) then
        call write_aflr3_mapbc( grid(1),trim(write_mesh_project)//'.mapbc' )
        if ( big_endian_io() ) then
          grid_filename = trim(write_mesh_project)//'.b8.ugrid'
        else
          grid_filename = trim(write_mesh_project)//'.lb8.ugrid'
        end if
        use_t0_coords_arg = (design_run .and.                                  &
               (trim(grid(1)%grid_motion) /= 'static'.or.static_grid_transform))
        call global_image_ugrid( grid(1), grid_filename, use_t0_coords_arg )
      endif

!.....Close out the files that store the time-dependent flowfield data
!     for time-accurate adjoints

      if ( timedep_adj_frozen ) call close_timedep_qfiles()

!.....Output HI-ARMS rotor solution file
      if (hiarms_flag .and. lmpi_master) then
        call hiarms_write_source(ntt)
        call hiarms_finalize()
      end if

!.....Shutdown 6DOF library and free up associated memory

      if (six_dof) then
        call cleanup_sixdof()
      end if

!.....Execution complete

      if ( timing_main ) call rtime('Execution complete')

      if ( write_q_boundary_discrete ) &
      call write_q_boundary_nc(grid(1), soln(1))

      call end_native_volume_data_write()
      call end_fwh_write()

      call lmpi_synchronize()
      if (lmpi_master) write(*,*) 'Done.'
      if (dbtime.and.(lmpi_id < 5)) call se_wall_time("FUN3D done. ")

      if ( multigrid_timing_file ) call fmg_track_total(0)

    end if fun3d_only

    call lmpi_finalize()

  end subroutine post


!==================================== ITERATE ================================80
!
! Each pass advances the solution through one step of a time-advancement scheme
! and performs numerous secondary operations
!
!=============================================================================80

  subroutine iterate(bcontinue)

    use visit,       only : cop_step=>step

    logical, intent(out) :: bcontinue

  continue

!...Make the default to continue with time stepping after the current one is
!   completed. bcontinue will be set to .false. if this turns out to be the
!   last step or the code needs to bail out for some reason.

    bcontinue = .true.

    if ( .not. cop_step() ) return

!...Move grid to new position if requested, and update the mesh metrics

    call update_mesh()

!...Advance the solution though a single time step

    call step_solver( bcontinue )

!...Operations to perform prior to moving to next time step

    call step_post( bcontinue )

    call lmpi_bcast( bcontinue, fun3d_id_l2g(0), world_comm )

  end subroutine iterate


!===================================== STEP_PRE ==============================80
!
! Preliminary operations before advancing through a time step: increment
! counters, increment time, set coeficients for time accuracy, etc.
!
!=============================================================================80

  subroutine step_pre()

    use lmpi,        only : lmpi_master
    use grid_motion, only : compute_forces_moving_body

  continue

    if (.not. is_fun3d) return

    if ( lmpi_master .and. timing ) call timer('Reset at start of timestep')

    simulation_time_atn = simulation_time  ! time at end of previous time step

    iter       = iter + 1
    ntt        = iter

!...Make sure we have enough space to store the solution history, in case
!   more steps are run than what was specified in the namelist input

    call history_reallocs()

    if ( ntt == 1 ) then
      soln(fl)%time1 = starting_time
    else
      soln(fl)%time1 = soln(fl)%time2
    endif

!...Compute dt/ shuffle backplane dt's  Note: all current time-accurate
!   schemes use a constant dt, and so all backplane dt's are identical
!   and thus new_timestep currently has no net effect

    call new_timestep()

!...Advance previous simulation time by dt for time accurate cases
!   (or possibly by a fraction of dt for multistage schemes)

    call set_simulation_time(simulation_time_atn)

    soln(fl)%simtime(ntt) = simulation_time

!...Compute coefficients needed for specified temporal accuracy

    t_stage = 1
    call set_time_coeffn()
    call set_time_coeff2()

!...Compute forces on moving bodies

    call compute_forces_moving_body(grid(fl), soln(fl), moving_body)

!...Compute initial boundary forces to fill arrays.

    if (ntt == 1) then
      call compute_forces( grid(fl), soln(fl), ntt )
    end if

!...Aeroelastic predictor step (modal-based)

    if (trim(structural_model) == 'internal') then
      call ae_pred()
    endif

    step_initialized = .true.

  end subroutine step_pre


!=================================== UPDATE_MESH =============================80
!
! Move mesh (surface and volume) to a new position and compute the spatial
! and temporal metrics. Save/shuffle grid backplanes and update any transform
! matrices that drive mesh movement
!
!=============================================================================80

  subroutine update_mesh()

    use grid_motion, only : update_transform_matrix

  continue

    if (.not. is_fun3d) return

    if (.not. step_initialized) call step_pre()

    if ( itime /= 0 ) then
      call update_temporal_backplanes(soln(fl),grid(fl), grid_only=.true.)
    endif

    call adapt_lines()  ! feature-based adaption along grid lines

    call update_transform_matrix(moving_body, observer)

    call update_surface_mesh(grid, soln, moving_body, fl)

    call update_moving_mesh(grid, soln, moving_body, nml_path, fl)

    call update_mesh_metrics(grid, crow, fl)

    if (noninertial .and. mesh_has_been_updated) then
      call setup_nonin(grid(fl))
      call setup_nonin_observer()
    end if

  end subroutine update_mesh

!=================================== STEP_SOLVER =============================80
!
! Advances the solution through one time step, possible using multiple stages
! within the time step, and/or subiterations within the time step
!
!=============================================================================80

  subroutine step_solver(bcontinue)

    use lmpi,        only : lmpi_master
    use cfl_control, only : set_cfl_m_t_levels

    logical, intent(inout) :: bcontinue

    integer :: gcr_exit = 0

  continue

    fun3d_only : if (is_fun3d) then

      if ( timing_main ) call rtime('start:time_stage_loop')

      if (.not. step_initialized) call step_pre()

!.....Save the solution at current step and reshuffle backplane data

      if ( itime /= 0 ) then
        call update_temporal_backplanes(soln(fl),grid(fl), soln_only=.true.)
      endif

!.....Get cfl's for "best practice" cfl ramping...currently steady-state only

      if ( itime == 0 ) then
        call set_cfl_m_t_levels( ntt, fl, ngrid_actual, fmg_levels_request,    &
                                 restart_level, fas_cycles )
      endif

!.....The following set of actions are application/test specific - unlike those
!     above that are more-or-less generic, only differing for physical-time
!     steps or pseudo-time steps (i.e. time-accurate vs.steady state)

!.....Developer's tool for checking Jacobians and convergence.

      call developer_tools(bcontinue)
      if ( .not. bcontinue ) return

!.....Compute the HI-ARMS rotor sources

      if (hiarms_flag) then
        call hiarms_save_flow(soln(fl)%n_tot,    grid(fl)%nnodes0,             &
                              grid(fl)%nnodes01, soln(fl)%q_dof)
        call hiarms_get_loads(1)
      end if

    end if fun3d_only

    time_stage_loop: do t_stage = 1,stage   ! inner stages within a timestep

      fun3d_only_1 : if (is_fun3d) then

        multi_stage_only : if (t_stage > 1) then

!.........Advance previous simulation time by  a fraction of dt if multistage

          call set_simulation_time(simulation_time_atn)

!.........Compute coefficients needed for specified temporal accuracy

          call set_time_coeffn()
          call set_time_coeff2()

!..........Save the grid / solution at current step and reshuffle backplane data

          if ( itime /= 0 ) then
            call update_temporal_backplanes(soln(fl),grid(fl))
          endif

!.........Move grid to new position if requested, and update the metrics

          call update_mesh()

        end if multi_stage_only

      end if fun3d_only_1

!.....Get new overset connectivity information if needed

      call update_overset_connectivity()

!.....If only doing mesh motion, output requested data, check for a stop
!     request, and skip to next timestep

      if (grid_motion_only) then
        call grid_motion_output(bcontinue)
        cycle time_stage_loop
      endif

      fun3d_only_2 : if (is_fun3d) then

!.......Save freestream time-dependent solution info for adjoint

        if ( timedep_adj_frozen .and. ntt == 1 ) then
          call write_timedep_qfiles(grid(1),soln(1),.true.)
        endif

!.......Update the fringe values for overset problems

        call update_overset_fringe_values()

!.......Move rotor blade sources for time-dependent cases

        if ( rotor_flag .and. (itime /= 0) ) then
          call advance_rotor_blade()
          call source2node_assoc(grid(fl)%nnodes0, grid(fl)%nnodes01,          &
                                 grid(fl)%x, grid(fl)%y, grid(fl)%z,           &
                                 grid(fl)%bc, grid(fl)%nbound,                 &
                                 grid(fl)%l2g, (ntt==ncyc))
        end if

!.......Reinitialize boundary conditions if necessary
        call reinitialize_bcs()

!.......Stage predictor for multi-stage time-accurate schemes

        if(itime >= 4 ) then
          if( back_planes == 3 ) call stage_value_predictor(soln(fl))
        endif

        if (time_moving_grid .and. lmpi_master) call timer('reset')

!.......Ensure twod solution is consistent.

        if ( twod ) then
          call copy_twod( grid(fl)%nnodes0_2d, grid(fl)%node_pairs_2d,         &
                          soln(fl)%q_dof )
        end if

        if ( first_time_through ) then

!.........Enable triage in testing environment.

          if ( testing ) then
            call triage_residuals( grid, soln )
            call lmpi_set_grid_level(fl)
          endif

!.........Write truncation errors.

          if ( ic_exact .or. truncation_error ) then
            call truncation_errors( grid, soln )
          end if

          first_time_through = .false.

        endif

!.......Set some multigrid diaganostics on the first step

        if ( iter == 1 ) then
          if ( ideal_coarse_grid .or. ideal_relaxation ) then
            call check_ideal_residuals( grid, soln, crow, mass, fmg_level )
            call zero_fas_accounting()
          end if
          call times('FMG-StartUp',fl)
          if ( multigrid_timing_file ) call fmg_track_total(fl)
          call times('FMG-FAS',fl)
        endif

!.......If coupled with SSDC, advance those points by dt and reinterpolate
!       at fringes

        if ( ssdc_flag ) call ssdc_timestep(soln(1),grid(1),ntt+prior_iters)
        if ( all_ssdc ) cycle time_stage_loop

!.......If using stabilized finite elements, advance the solution using
!       the time stepping in the SFE library

        if ( sfe_flag )then
          call sfe_timestep(soln(1),grid(1),ntt+prior_iters)
          cycle time_stage_loop
        end if

        with_or_without_fas : if ( (ngrid > 1) .and. fas_scheme ) then

!.........Full Approximation Scheme (FAS) multigrid cycling

          fas_pass = 1 !legacy approach - loose or tight coupling in relaxation

          if ( itime /= 0 ) then

            call fas( fmg_level, nml_path, unsteady_relax )

          elseif ( separate_fas_turbulence ) then

            hold           = turbulence_fas
            turbulence_fas = 0              ; fas_pass = 1
            call fas( fmg_level, nml_path, steady_relax )
            turbulence_fas = hold

            hold           = meanflow_fas
            meanflow_fas   = 0              ; fas_pass = 2
            call fas( fmg_level, nml_path, steady_relax )
            meanflow_fas   = hold

          else

            call fas( fmg_level, nml_path, steady_relax )

          endif

        else with_or_without_fas

!.........Solve the equations and update the solution variables.

          if ( timing_main ) call rtime('start:relax')

          if ( itime /= 0 ) then
            call unsteady_relax( 1, 1, 1, relax_field, nml_path, gcr_exit )
          else
            call steady_relax( 1, 1, 1, relax_field, nml_path, gcr_exit )
          endif

          if ( timing_main ) call rtime('..end:relax')

          if (time_moving_grid .and. lmpi_master) then
            write(*,*)
            call timer('To advance flow equations one time step')
          end if

        endif with_or_without_fas

        if ( timing_main ) call rtime('..end:time_stage_loop')
        if ( timing_main ) call rtime_mark()

      end if fun3d_only_2

    end do time_stage_loop  ! end of stages within a timestep

    fun3d_only_3 : if (is_fun3d) then

!.....Determine the number of back_planes available for the next time step

      call temporal_backplanes(itime, back_planes, stage)

    end if fun3d_only_3

!...reset initialization flag for next time step

    step_initialized = .false.

  end subroutine step_solver


!===================================== STEP_POST =============================80
!
! Perform miscellaneous operations associated with current time step that can
! be done only after advancing the mesh and solution through the time step
!
!=============================================================================80

  subroutine step_post( bcontinue )

    use kinddefs,                 only : system_r8
    use nml_code_run_control,     only : duration_limit_in_minutes
    use refine_adaptation_input,  only : adapt_intersect_metric_in_time
    use lmpi,                     only : lmpi_master, lmpi_duration
    use adaptation_parameter,     only : time_accurate_metric_intersect
    use aeroelastic,              only : structural_model,                     &
                                         external_structural_model

    logical, intent(inout) :: bcontinue

    real(system_r8)        :: duration_in_sec

  continue

!...Check for early termination of the execution

    fun3d_only_0 : if ( is_fun3d ) then

!     user request

      prevent_file_acess : if ( .not. time_timestep_loop ) then
        if ( lmpi_master .and. timing ) call timer( 'Before check for stop' )
        call check_for_stop(istop)
        if ( lmpi_master .and. timing ) call timer( 'After check for stop' )
      end if prevent_file_acess

      if ( (iter >= istop) .and. istop /= 0 ) then
        if (lmpi_master) then
           write(*,*)
           write(*,'(a)')' Stopping: requested via stop.dat'
           write(*,*)
        end if
        bcontinue = .false.
      end if

!     solution converged

      if ( solution_converged() ) then  ! (min. 4 iterations)
        if (lmpi_master .and. .not.hanim ) then
          write(*,*)
          write(*,'(a)')' Stopping: convergence criterion reached'
          write(*,*)
        end if
        bcontinue = .false.
      end if

!     time's up

      if (lmpi_master .and. ( duration_limit_in_minutes > 0.0_dp) ) then
        call lmpi_duration(duration_in_sec)
        if ( duration_in_sec/60.0_dp >= duration_limit_in_minutes) then
          write(*,*)
          write(*,'(a,e14.6,a,e14.6)') ' Stopping: Run time',                  &
            duration_in_sec/60.0_dp,' exceeds duration_limit_in_minutes of',   &
            duration_limit_in_minutes
          write(*,*)
          bcontinue = .false.
        end if
      end if

    end if fun3d_only_0

    call lmpi_bcast( istop, fun3d_id_l2g(0), world_comm )
    call lmpi_bcast( bcontinue, fun3d_id_l2g(0), world_comm )

    if (istop > fas_cycles) fas_cycles = istop

    fun3d_only_1 : if ( is_fun3d ) then

!.....Skip to next time step if only moving the grid; possibly write restart

      if ( grid_motion_only ) then
        if ( dump_solution(bcontinue) ) call soln_dump( fl, grid(fl), soln(fl) )
        return
      end if

!.....Compute the force and moment coefficients

      if ( lmpi_master .and. timing ) call timer('Before compute forces')

      call compute_forces( grid(fl), soln(fl), ntt )
      if ( lmpi_master .and. track_group_forces .and. .not.bypass_solver ) then
        if ( local_moment_centers ) then
          call   group_forces_hinge( grid(fl), soln(fl) )
        else
          call   group_forces( grid(fl), soln(fl) )
        endif
      endif

      if ( lmpi_master .and. timing ) call timer('After compute forces')

!.....Update time-averaged quantities

      if ( fl == 1 .and. itime_avg > 0 ) then
        call comp_qtimeavg( soln(fl)%n_tot,soln(fl)%n_grd,grid(fl)%nnodes0,    &
                            grid(fl)%nnodes01,soln(fl)%q_dof,                  &
                            soln(fl)%q_time_avg, soln(fl)%qq_time_avg,         &
                            soln(fl)%eqn_set, soln(fl)%amut,                   &
                            soln(fl)%gradx, soln(fl)%grady, soln(fl)%gradz )
      end if

!.....Aeroelastic corrector step (modal-based)

      if ( trim(structural_model) == 'internal' ) then
        call ae_corr( grid(fl), soln(fl), ntt )
      end if

!.....Adjust angle of attack to match specified Cl if requested.

      call alter_alpha()

!.....Keep track of time used

      call checkpoint( soln(fl)%time2 )

      delta_clicks = soln(fl)%time2 - soln(fl)%time1
      if ( delta_clicks < 0 ) then
        nrollovers   = nrollovers + 1
        delta_clicks = count_max + delta_clicks
      endif
      time_increment = wall_cr_inv*real(delta_clicks,dp)

      if ( ntt == 1 ) then
        soln(fl)%walltime(ntt) = time_increment
      else
        soln(fl)%walltime(ntt) = soln(fl)%walltime(ntt-1) + time_increment
      endif

!.....Print lift, drag, residual norm, max resid and location to screen

      call output_status()

!.....Periodically write multigrid convergence file if we are not at end

      call write_mg_convergence()

!.....Time-accurate metric intersection

      if ( adapt_intersect_metric_in_time )                                    &
        call time_accurate_metric_intersect( grid(1),                          &
          soln(1)%eqn_set, soln(1)%n_tot, soln(1)%q_dof )

!.....Option-dependent output

      call optional_output(bcontinue)

!.....Exchange data with external FEM/CSD model

      if ( trim(structural_model) == 'external' .or.                           &
           trim(structural_model) == 'external_static' ) then
        call external_structural_model()
      end if

!.....Advance Dymore if doing tight coupling

      if ( comprehensive_rotor_coupling == 'fsi_tight' ) then
        if ( ntt > 1 ) call advance_fsi_tight( ntt )
      end if

      if ( time_moving_grid .and. lmpi_master ) then
        write(*,*)
        call timer( 'To complete output options requiring global data' )
      end if

!.....Reset printing flag back to zero

      skeleton = 0
      call se_flush()

      if ( lmpi_master .and. timing ) call timer( 'Reset at end of timestep' )
      if ( timing_main ) call rtime( '..end:time_step_loop' )

      if ( ngrid > 1 .and. ( tolerance_reached .or. ntt == fas_cycles ) ) then
        call forces_vs_h( fl, ntt, grid, soln )
      endif

!.....Write restart file and adapted mesh - periodically, or at end of run

      if ( dump_solution(bcontinue) ) then
        call soln_dump( fl, grid(fl), soln(fl) )
        call write_adapted_mesh()
      end if

    end if fun3d_only_1

  end subroutine step_post


!=================================== FLOW_ADAPT ==============================80
!
! Adapation operations
!
!=============================================================================80

  subroutine flow_adapt()

    use refine_adaptation_driver, only : feature_based_adaptation,             &
                                         write_adapt_stats
    use solution,                 only : wrest
    use grid_helper,              only : grid_maximum_imesh, grid_imesh_order
    use global_image,             only : global_image_component_ugrid
    use overset_defs,             only : imesh_order
    use file_utils,               only : big_endian_io

    integer :: maximum_imesh, component

    character(256) :: filename, grid_filename

    continue

! remember the imesh_order to keep adaptation straight
!FIXME talk to bob always execute the next block right after init_overset
!FIXME and always execute block on line 767 of dirtlib to get imesh
!FIXME running 1 of 9  proc????
!FIXME fold into init_overset
    if ( overset_flag ) then
      call grid_maximum_imesh(grid(1), maximum_imesh)
      allocate(imesh_order(maximum_imesh+1))
      call grid_imesh_order(grid(1), maximum_imesh)
    end if

    call feature_based_adaptation( grid(1), soln(1) )

    if (lmpi_master) write(*,*) "wrest dump solution restart requires ntt = 0"

    ntt = 0
    call wrest(grid(1), soln(1), '')

    if ( testing ) then
       call write_adapt_stats( grid(1)%nnodesg, grid(1)%elem(1)%ncellg )
    end if

    if (lmpi_master) write(*,*) "writing adapted aflr3 ugrid, ",       &
      trim(grid(1)%project)
    set_orig_xyz : if ( trim(grid(1)%grid_motion) /= 'static' .and. &
      abs(itime) > 0 ) then
      if (lmpi_master) &
        write(*,*) " setting xyz to xyz at 0 ",trim(grid(1)%grid_motion)
      grid(1)%x = grid(1)%xat0
      grid(1)%y = grid(1)%yat0
      grid(1)%z = grid(1)%zat0
    end if set_orig_xyz
    call write_aflr3_mapbc(  grid(1), trim(grid(1)%project)//'.mapbc' )
    if ( big_endian_io() ) then
      grid_filename = trim(grid(1)%project)//'.b8.ugrid'
    else
      grid_filename = trim(grid(1)%project)//'.lb8.ugrid'
    endif
    call global_image_ugrid(grid(1), grid_filename, cl2g_available_arg=.false.)

    if (overset_flag) then
      call grid_maximum_imesh(grid(1), maximum_imesh)
      do component = 0, maximum_imesh
        filename = "Component_Grid_"//trim(int_to_s(component))
        if (lmpi_master) write(*,*) "writing ", trim(filename)
        call global_image_component_ugrid( grid(1), filename, component )
      end do
    end if

    call end_native_volume_data_write()
    call end_fwh_write()

    if (lmpi_master) write(*,*) "Done."
    call lmpi_finalize()
    stop

  end subroutine flow_adapt


!===================================== FMG_PRE ===============================80
!
! Secondary FUN3D initialization/setup operations
!
!=============================================================================80

  subroutine fmg_pre()

    use sampling_headers,     only : number_of_geometries, sampling_frequency
    use nml_solidity,         only : need_solidity
    use nml_code_run_control, only : irs_flag
    use lmpi,                 only : lmpi_die

    integer :: igeom, sampling_sum

  continue

    fun3d_only : if (is_fun3d) then

      feature_adapt_and_quit : if ( adapt )then
        call flow_adapt()
      end if feature_adapt_and_quit

      skeleton  = skeleton_hold
      fmg_level = fl

      if ( skeleton > 0 ) write(*,*) 'FMG...begin startup for level=',fmg_level

      call times('FMG-StartUp',fl)

      if ( fl < fmg_levels_request .and. fmg_prolong > 0 ) then
        if ( skeleton > 0 ) write(*,*) 'FMG...prolongation from level=',fl+1
        if ( grid(fl)%cc ) then
          call fmg_prolongation_cc(grid, soln, fl)
        else
          call fmg_prolongation_nc(grid, soln, fl)
        endif
      elseif( fl < fmg_levels_request ) then
        if ( skeleton > 0 ) write(*,*) 'FMG...skipping prolongation.'
      endif

!.....Set up of boundary / volume data output for animation, aeroelastics, etc

      if (write_massoud_file                 .or.                              &
          write_aero_loads_to_file           .or.                              &
          need_solidity                      .or.                              &
          boundary_animation_freq(fl) /= 0   .or.                              &
          boundary_animation_freq_tavg /= 0  .or.                              &
          slice_freq /= 0                    .or.                              &
          fwh_data_freq /= 0) then

        call set_up_global_bndry_data(grid(fl), soln(fl)%global_bndry_data,    &
                                      soln(fl)%n_tot, soln(fl)%n_turb,         &
                                      soln(fl)%n_grd, soln(fl)%njac,           &
                                      soln(fl)%adim)

        if ( boundary_animation_freq(fl) /= 0 ) then
          call output_global_surface_data(grid(fl), soln(fl), 0 )
        end if

        if ( fwh_data_freq /= 0 ) then
          call fwh_global_surface_data(grid(fl), soln(fl), 0 )
        end if

        if ( boundary_animation_freq_tavg /= 0 ) then
          call output_tavg_surface_data(grid(fl), soln(fl), 0)
        end if

        if ( slice_freq /= 0 ) then
           call slice_bndry_data( grid(1), soln(1), 0, nml_path, init=.true. )
           if (prior_iters == 0 .or. slice_freq < 0) remove_slice_data=.true.
        end if

        if ( write_massoud_file .or. write_aero_loads_to_file ) then
            call write_mdo_surface_data( grid(fl), soln(fl), 0)
        end if

      end if

      if (volume_animation_freq(fl) /= 0 .and. grid(fl)%origin == 1 ) then
        call write_vol_tec( grid(fl), soln(fl), 0 )
      end if

      sampling_sum = 0
      sampling_sum = sum(abs(sampling_frequency(1:number_of_geometries)))
      if ( sampling_sum /= 0  .and. fl == 1 ) then
        call survey( grid(fl), soln(fl), 0, nml_path )
      end if

      call set_strand_usage(bypass_solver, report_usage=.false.)

!.....Output Tecplot files of the initial solution on selected surfaces or
!     throughout entire volume; alternatively, dump animation output and stop

      if (prior_iters == 0 ) then
        call solution_output( grid(fl), soln(fl), 0, nml_path )
      else if (bypass_solver) then
        if (boundary_animation_freq(fl) /= 0)   boundary_animation_freq(fl) =1
        if (fwh_data_freq /= 0)                 fwh_data_freq               =1
        if (boundary_animation_freq_tavg /= 0)  boundary_animation_freq_tavg=1
        if (slice_freq /= 0)                    slice_freq                  =1
        if (volume_animation_freq(fl) /= 0)     volume_animation_freq(fl)   =1
        if (aero_loads_output_freq /= 0)        aero_loads_output_freq      =1
        if (massoud_output_freq /= 0)           massoud_output_freq         =1
        ntt         = prior_iters
        prior_iters = 0
        if (overset_rotor) then
          call set_up_rotor_motion(soln(fl)%eqn_set, simulation_time, flow_dir)
        end if
        if (moving_grid) then
          call set_slice_transforms( moving_body )
        end if
        call compute_forces( grid(fl), soln(fl), 1 )
        call solution_output( grid(fl), soln(fl), ntt, nml_path )
#if defined(HAVE_IRS)
        if ( irs_flag ) call perform_irs_solve()
#else
        if ( irs_flag ) then
          write(*,*)'IRS post-processing requested; exec not linked to library.'
          call lmpi_die()
          stop
        endif
#endif
        if (convert_v4_to_stream) then
          call soln_dump(fl, grid(fl), soln(fl))
        end if
        call end_native_volume_data_write()
        call end_fwh_write()
        call lmpi_synchronize()
        if (lmpi_master) write(*,*) 'Done.'
        call lmpi_finalize()
        stop
      end if

      output_target = ncyc + prior_iters
      if ( ngrid > 1 ) output_target = fmg_cycles_request(fl)
      if (boundary_animation_freq(fl) < 0)                                     &
          boundary_animation_freq(fl)       = output_target
      if (fwh_data_freq < 0)                                                   &
          fwh_data_freq                     = output_target
      if (boundary_animation_freq_tavg < 0)                                    &
          boundary_animation_freq_tavg      = output_target
      if (slice_freq < 0)                                                      &
          slice_freq                        = output_target
      if (volume_animation_freq(fl) < 0)                                       &
          volume_animation_freq(fl)         = output_target
      do igeom = 1,number_of_geometries
        if (sampling_frequency(igeom) < 0)                                     &
            sampling_frequency(igeom)       = output_target
      end do
      if (aero_loads_output_freq < 0)                                          &
          aero_loads_output_freq            = output_target
      if (massoud_output_freq < 0)                                             &
          massoud_output_freq               = output_target

!.....Set simulation time (nondimensional time) at beginning of the run

      call set_simulation_time()

      if ( dbtime.and.(lmpi_id < 5)) call se_wall_time("Start: time_step_loop")
      if ( time_comm ) call lmpi_app_time('start_sync')

      if ( timing_main ) call rtime_mark('reset')
      if ( timing_main ) call rtime('start:time_step_loop')

      if ( ngrid > 1 .and. fl == fmg_levels_request .and. hanim ) then
        call start_convergence_history( grid(fl)%project, grid(fl)%igrid, &
                                        ierr )
        call lmpi_conditional_stop(ierr,'start_convergence_history:fmg_pre')
      endif

      if ( skeleton > 0 ) write(*,*) 'FMG.....end startup for level=',fmg_level

      iter = 0

    end if fun3d_only

  end subroutine fmg_pre


!===================================== FMG_POST ==============================80
!
! Additional post processing operations
!
!=============================================================================80

  subroutine fmg_post()

    use file_utils,              only : big_endian_io
    use sampling_headers,        only : number_of_geometries, sampling_frequency
    use adaptation_parameter,    only : time_accurate_metric_export
    use refine_adaptation_input, only : adapt_intersect_metric_in_time

    integer        :: igeom

    character(256) :: filename

  continue

    if ( hanim .or. extract_special_line .or. tecplot_mapped_lines ) then
      call lmpi_synchronize()
      if ( lmpi_master )                               &
      call convergence_history( grid(fl)%project, ierr )
      call lmpi_conditional_stop( ierr,'convergence_history2:flow.F90' )
      if ( lmpi_master .and. write_resmax_files )           &
      call max_residual_locations( grid(fl), soln(fl), ierr )
      call lmpi_conditional_stop(ierr,'max_residual_locations2:flow.F90')
      if ( extract_special_line ) then
        call special_line_tecplot( grid(fl), soln(fl) )
      endif
      if ( tecplot_mapped_lines ) then
         call line_plot_driver( grid(fl), soln(fl), alpha_flag, alpha )
      endif

      call lmpi_synchronize()
    endif

    fun3d_only_1 : if (is_fun3d) then

      if ( time_timestep_loop ) then
        if (dbtime.and.(lmpi_id < 5)) call se_wall_time("Stop: time_step_loop")
        call write_timestep_timing(start_of_timestep_loop,end_of_timestep_loop)
      endif

!.....Developer's tool for creating files with Jacobians.

      if ( lmpi_nproc == 1 ) then
        call survey_jacobians( grid, soln, crow, fl )
      endif

      if ( ir_passes > 0 .or. ir_passes_line > 0 ) call deallocate_smoothing()

      if ( time_comm) call lmpi_app_time('stop')

!.....Remove stop file so it doesn't kill the next run unintentionally

      if ( lmpi_master ) call rm('stop.dat')

!.....Make sure any "end-of-run" animation/plot files are output if the
!     convergence tolerance was met before iter = ncyc or the user has
!     requested a (graceful) stop

      if ( tolerance_reached .or. istop > 0 ) then
        output_target = ntt + prior_iters
        if ( ngrid > 1 ) output_target = ntt
        if (boundary_animation_freq(fl) > 0)                                   &
            boundary_animation_freq(fl)           = output_target
        if (fwh_data_freq > 0)                                                 &
           fwh_data_freq                          = output_target
        if (boundary_animation_freq_tavg > 0)                                  &
            boundary_animation_freq_tavg          = output_target
        if (slice_freq > 0)                                                    &
            slice_freq                            = output_target
        if (volume_animation_freq(fl) > 0)                                     &
            volume_animation_freq(fl)             = output_target
        do igeom = 1,number_of_geometries
          if (sampling_frequency(igeom) > 0)                                   &
              sampling_frequency(igeom)           = output_target
        end do
        if (aero_loads_output_freq > 0)                                        &
            aero_loads_output_freq                = output_target
        if (massoud_output_freq > 0)                                           &
            massoud_output_freq                  = output_target
        call solution_output(grid(fl), soln(fl), ntt, nml_path )
      end if
!   call test_solidity(grid(fl),soln(fl))

!.....Deallocate any memory reserved for global-data collection

      if (associated(soln(fl)%global_bndry_data)) then
        do ib=1,size(soln(fl)%global_bndry_data)
          call deallocate_global_bndry_data(soln(fl)%global_bndry_data(ib))
        end do
        deallocate(soln(fl)%global_bndry_data)
      end if

    end if fun3d_only_1

!...Need to communicate a command to stop for the suggar proc

    call lmpi_bcast(istop, fun3d_id_l2g(0), world_comm)

!...Do an immediate dead stop for istop < 0 (no restart files written)

    if (istop < 0) then
!     remove file so next run isn't automatically stopped
      if ( lmpi_master ) call rm('stop.dat')
      call lmpi_finalize
      stop
    end if

    fun3d_only_2 : if (is_fun3d) then

!.....Output reference solution.

      if ( reference_q_write ) call write_reference_q( grid(fl), soln(fl) )

!.....Check residuals at x/y/z=0 (for acid check of symmetry implementation)

      if(output_symmetry_residuals) then
        call x_res_check(grid(fl), soln(fl))
        call y_res_check(grid(fl), soln(fl))
        call z_res_check(grid(fl), soln(fl))
      endif

!.....Check the residual and discretization errors in detail

      if ( testing .or. print_conditional .or. ic_exact ) then
        call resid_check(grid(fl), soln(fl))
        call lisbon_check(grid(fl), soln(fl))
      end if

      if ( cut_cell_activated .or. cut_cell_visualize )                        &
        call cut_cell_visualization(grid(fl),soln(fl))

      if (adapt) then
        if (lmpi_master) write(*,*) 'Writing new grid file for modified mesh'
        call write_aflr3_mapbc( grid(fl), trim(grid(fl)%project)//'.mapbc')
        if ( big_endian_io() ) then
          filename = trim(grid(fl)%project)//'.b8.ugrid'
        else
          filename = trim(grid(fl)%project)//'.lb8.ugrid'
        end if
        call global_image_ugrid(grid(fl), filename, cl2g_available_arg=.false.)
      end if

      if ( adapt_intersect_metric_in_time )                                    &
        call time_accurate_metric_export( grid(1) )

!.....Write history to debug_verify file - accumulated on master processor.

      if (lmpi_master) call history_verify(soln(fl))

    end if fun3d_only_2

    if ( correction_scheme ) call reset_initial_qset
    call times('FMG-FAS',fl)
    if ( multigrid_timing_file ) call reset_fmg_track(fl)

  end subroutine fmg_post


!=================================== MESH_SEQUENCING =========================80
!
! Full multigrid startup by mesh sequencing: obtain solutions starting from
! coarsest level up to, but not including, solving on the finest level; this
! gives an improved starting solution on the finest level (fl=1)
!
!=============================================================================80

  subroutine mesh_sequencing(brun)

    use cfl_control,    only : reset_cfl_parameters

    logical, intent(out) :: brun
    integer              :: it, level

  continue

    brun = .true.

    fmg_levels : do level = fmg_levels_request, 2, -1

      fl = level

      call lmpi_bcast(fl, fun3d_id_l2g(0), world_comm)

      fas_cycles = ncyc
      if ( ngrid > 1 ) then
        fas_cycles = fmg_cycles_request(fl)
        ncyc       = fas_cycles

      endif

      call fmg_pre()

      it = 0

      fas_iterations: do

        it = it + 1

        if (it > fas_cycles) exit fas_iterations

        call iterate(brun)

        if ( .not. brun ) then ! early exit
           if ( istop == -999 ) exit fmg_levels
           fmg_cycles_request(fl) = iter
           brun = .true.
           exit fas_iterations
        endif

      end do fas_iterations

      call reset_cfl_parameters( ntt, fl, fl-1 )

      call fmg_post()

    end do fmg_levels

!   allow for potential exit before finishing all the coarser levels

    if (.not. brun) return

!   finished with coarser level solutions; get ready for finest grid level

    fl = 1
    fas_cycles = ncyc
    if ( ngrid > 1 ) then
      fas_cycles = fmg_cycles_request(fl)
      ncyc       = fas_cycles

    endif

    call fmg_pre()

  end subroutine mesh_sequencing


!================================= SOLUTION_CONVERGED ========================80
!
! Logical function to assess whether flow equations (mean and turb) have
! sufficiently converged. Note that we insist on 4 iterations being done before
! checking for convergence
!
!=============================================================================80

  function solution_converged()

    use solution_types,           only : generic_gas
    use cfl_control,              only : reset_cfl_new_alpha
    use nml_global,               only : body_motion_only
    use gcr_defs,                 only : rmsmax, rmsloc

    logical  :: solution_converged
    logical  :: atolerance_reached, rtolerance_reached

    integer  :: eq_lower, eq_upper

    real(dp) :: cr

  continue

    solution_converged = .false.

    atolerance_reached = .false.

    if (grid_motion_only .or. body_motion_only) return

!     Check the absolute stopping tolerance.

    if ( hanim ) then

      rmschk = maxval(rmsloc(1:soln(fl)%n_q))

    else

      rmschk = maxval(soln(fl)%rmshist(1:soln(fl)%n_q,ntt,1))

    end if

    if( rmschk <= absolute_stopping_tolerance .and. iter >= 4 )  &
    atolerance_reached = .true.

    tolerance_reached = atolerance_reached

    !   Check the relative stopping tolerance.

    if ( hanim ) then

      eq_lower = 1
      eq_upper = soln(fl)%n_q
      if ( ( soln(fl)%eqn_set == generic_gas           )  .or. &
           ( meanflow_fas > 0 .and. turbulence_fas > 0 ) ) then
      elseif ( meanflow_fas > 0 ) then
        eq_upper = soln(fl)%n_q-soln(fl)%n_turb
      elseif ( turbulence_fas > 0 ) then
        eq_lower = soln(fl)%n_q-soln(fl)%n_turb+1
      endif

      ! If rms < 0.1*absolute tolerance, skip relative tolerance check, i.e.,
      ! assume relative tolerance satisfied.

      rtolerance_reached = .true.
      do eq=eq_lower,eq_upper
        if ( rmsloc(eq) < 0.1_dp*absolute_stopping_tolerance ) cycle
        if ( rmsloc(eq) <                                   &
             rmsmax(eq) * relative_stopping_tolerance ) cycle
        rtolerance_reached = .false.
        exit
      enddo

      if ( iter >= 4 .and. rtolerance_reached ) tolerance_reached = .true.

    endif

    if ( hanim .and. lmpi_master .and. tolerance_reached ) then
      write(*,*)
      write(*,"(1x,a,e12.2,a,i6,a,i2)") &
      'Convergence reached for maximum rms=',o(rmschk),' at ntt=',ntt,' fl=',fl
      if ( atolerance_reached ) then
        write(*,"(1x,a,e12.2)")                     &
        'Reached absolute_stopping_tolerance(ast)=',&
               o(absolute_stopping_tolerance)
      else
        write(*,"(1x,a,e12.2)")                           &
        'Did not reach absolute_stopping_tolerance(ast)=',&
                     o(absolute_stopping_tolerance)
      endif
      if ( rtolerance_reached ) then
        write(*,"(1x,a,e12.2)")                     &
        'Reached relative_stopping_tolerance(rst)=',&
               o(relative_stopping_tolerance)
      else
        write(*,"(1x,a,e12.2)")                           &
        'Did not reach relative_stopping_tolerance(rst)=',&
                     o(relative_stopping_tolerance)
      endif
      write(*,"(11x,a2,9x,a,6x,a,3x,a,5x,a)") &
      'eq','rms','rmsmax','reduction','rmshist'
      do eq=eq_lower,eq_upper
        cr = -1._dp
        if ( rmsloc(eq)  >= 0.1_dp*absolute_stopping_tolerance ) then
          cr  = rmsloc(eq) / rmsmax(eq)
        endif
        if ( cr > 0._dp ) then
          write(*,"(11x,i2,4(e12.2))")             &
          eq, o(rmsloc(eq)), o(rmsmax(eq)), o(cr), &
          o(soln(fl)%rmshist(eq,ntt,1))
        else
          write(*,"(11x,i2,2(e12.2),2x,a,e12.2)")         &
          eq, o(rmsloc(eq)), o(rmsmax(eq)), 'rms<ast/10', &
          o(soln(fl)%rmshist(eq,ntt,1))
        endif
      enddo
      write(*,*)
    endif

!   Reset tolerance_reached (possibly).

    if ( fl == 1 .and. tolerance_reached ) then
      if ( alpha_sweep .and. cycle_increment < 0 ) then
        call change_alpha( grid(fl)%project, soln(fl), alpha_flag )
        call reset_cfl_new_alpha( ntt, fl )
        if ( alpha_flag == 0 ) tolerance_reached = .false.
      endif
    endif

    if ( tolerance_reached ) solution_converged = .true.

  end function solution_converged


!==================================== ADAPT_LINES ============================80
!
! Driver routine for line adaption
!
!=============================================================================80

  subroutine adapt_lines()

    use refine_adaptation_driver, only : feature_based_adaptation
    use line_adaptation,          only : line_adapt
    use refine_adaptation_driver, only : soln_realloc_after_adapt

    integer :: ladapt = 0

  continue

    if ( ladapt_freq <=0 ) return

    if (adapt_library == 'sfline' .and. sfadapt_grdspd) then
      grid(fl)%dxdt = 0.0_dp
      grid(fl)%dydt = 0.0_dp
      grid(fl)%dzdt = 0.0_dp
    end if

    if (ntt/ladapt_freq*ladapt_freq == ntt .and. ladapt < ladapt_max) then
      ladapt = ladapt + 1
      if ( adapt_library == 'line' .or.                                        &
           adapt_library == 'sfline' ) then
        call line_adapt(soln(fl), grid(fl))
      else
        call feature_based_adaptation(grid(fl), soln(fl))
        call setup_2d(grid(fl))
        call compute_dual_metrics(grid(fl), moving_grid)
        call set_up_comprow_flow(soln(fl)%viscous_method, grid(fl),crow(fl))
        call soln_realloc_after_adapt(grid(fl), soln(fl), crow(fl))
        call process_eqn_groups(grid(fl), crow(fl), soln(fl), relaxation,      &
                                fl, flow_dir, nml_path)
        call sumgs_variable( grid(fl), crow(fl) )
      end if

    end if

  end subroutine adapt_lines


!======================== UPDATE_OVERSET_CONNECTIVIY =========================80
!
! Get new overset connectivity information if needed
!
!=============================================================================80

  subroutine update_overset_connectivity()

  continue

    if ( moving_grid ) then

      if ( pundit_flag ) then

        call pundit_register_data(grid(1),soln(1))
        call pundit_perform_connectivity()

      else if ( overset_flag ) then

        call overset_connectivity(grid(1))
        if ( (complex_mode.or.timedep_adj_frozen.or.interpolate_fringes.or.  &
              ssdc_flag) .and. is_fun3d ) then
          call get_donor_details(grid(1)%nnodes0, grid(1)%nnodes01,          &
                                 grid(1)%iblank, grid(1)%x, grid(1)%y,       &
                                 grid(1)%z)
        endif

      endif

      if ((ssdc_flag .and. (pundit_flag.or.overset_flag)) .and. is_fun3d) then
        call ssdc_overset_update(grid(1))
        call trim_overset_stencils(grid(1)%imesh,max_component_grids,        &
                                   discretization)
      endif

    endif

  end subroutine update_overset_connectivity


!======================== UPDATE_OVERSET_FRINGE_VALUES =======================80
!
! Update the fringe values for overset problems
!
!=============================================================================80

  subroutine update_overset_fringe_values()

  continue

    if ( pundit_flag ) then

      if ( ivisc > 2 ) then
        call pundit_solution_update(grid(fl),soln(fl),crow(fl),3)
      else
        call pundit_solution_update(grid(fl),soln(fl),crow(fl),1)
      endif

    else if ( overset_flag ) then

      if ( dci_io ) then
        call overset_update_nodirt(grid(fl)%nnodes0, grid(fl)%nnodes01,        &
                                   soln(fl)%q_dof, soln(fl)%turb,              &
                                   soln(fl)%n_turb, soln(fl)%n_tot,            &
                                   grid(fl)%iblank, crow(fl)%ia,               &
                                   crow(fl)%ja, 'all')
      else
        call overset_update(grid(fl)%nnodes0, grid(fl)%nnodes01,               &
                            soln(fl)%q_dof, soln(fl)%turb, grid(fl)%x,         &
                            grid(fl)%y, grid(fl)%z, soln(fl)%n_turb,           &
                            soln(fl)%n_tot, grid(fl)%iblank, crow(fl)%ia,      &
                            crow(fl)%ja, 'all', ssdc_flag)
      endif

    end if

  end subroutine update_overset_fringe_values


!============================= GRID_MOTION_OUTPUT ============================80
!
! When only doing mesh motion (not a flow solve), output requested data, and
! check to see if user has requested a stop
!
!=============================================================================80

  subroutine grid_motion_output(bcontinue)

    logical, intent(inout) :: bcontinue

  continue

    if (is_fun3d .and. t_stage == 1) then
      if (time_moving_grid .and. lmpi_master) call timer('reset')
      call solution_output(grid(fl),soln(fl),ntt,nml_path)
      if (time_moving_grid .and. lmpi_master) then
        write(*,*)
        call timer('To complete output options requiring global data')
      end if
    end if

    call check_for_stop(istop)
    call lmpi_bcast(istop, fun3d_id_l2g(0), world_comm)
    if (istop > fas_cycles) fas_cycles = istop
    if ((iter >= istop) .and. istop /= 0) then
      bcontinue = .false.
    end if

  end subroutine grid_motion_output


!================================ REINITIALIZE_BCS ===========================80
!
! Reinitialize boundary conditions if necessary
!
!=============================================================================80

  subroutine reinitialize_bcs()

    use solution_types, only : generic_gas, incompressible
    use lmpi_app,       only : lmpi_xfer

    logical :: reinitialize_bc

  continue

    reinitialize_bc = .false.

!   for time-accurate cases, reinitialize unsteady boundary conditions
!   and save variables at current timestep

    if ( itime /= 0 ) reinitialize_bc = .true.

    if ( hanim .or. bp_solution .or. ic_exact .or. &
         grid(fl)%cc .or. ngrid > 1 ) then

      reinitialize_bc = .false.

    elseif ( iter==1 .and. irest==1 .and. soln(fl)%eqn_set/=generic_gas ) then

!     if the first time step, reinitialize boundary conditions
!     for a steady-state restart case so that there will be no
!     inconsistency if boundary values have changed i.e. mass flux

      reinitialize_bc = .true.

    elseif ( noninertial .and. soln(fl)%eqn_set /= incompressible ) then

!     if noninertial reference frame, reinitialize boundary conditions
!     since density changes (compressible flows)

      reinitialize_bc = .true.

    endif

    if ( reinitialize_bc ) then

      if (soln(fl)%eqn_set /= generic_gas ) then

        do ib = 1,grid(fl)%nbound
          if ( bc_explicitly_initialized(grid(fl)%bc(ib)%ibc, itime) ) then
            call bc_init_nc(grid(fl), soln(fl), ib, grid(fl)%bc(ib))
          end if
        end do

      else

        do ib = 1,grid(fl)%nbound
          if ( bc_initialized_solid(grid(fl)%bc(ib)%ibc) ) then
            call bc_init_gen(grid(fl), soln(fl), grid(fl)%bc(ib), ib)
          end if
        end do

      end if

!     make sure all ghost-cell changes due to bc updates are transfered

      call lmpi_xfer(soln(fl)%q_dof)

    end if

  end subroutine reinitialize_bcs


!=================================== ALTER_ALPHA =============================80
!
!  Adjust angle of attack to match specified Cl if requested.
!
!=============================================================================80

  subroutine alter_alpha()

    use cfl_control, only : reset_cfl_new_alpha

  continue

    if ( fl == 1 .and. alpha_sweep .and. cycle_increment > 0 ) then
      if ( ntt == (ntt/cycle_increment)*cycle_increment ) then
        call change_alpha( grid(fl)%project, soln(fl), alpha_flag )
        call reset_cfl_new_alpha( ntt, fl )
      endif
    elseif (cl_const) then
      call adjust_alpha( soln(fl) )
    else
      soln(fl)%alpha(ntt) = alpha
    endif

  end subroutine alter_alpha


!================================ WRITE_MG_CONVERGENCE =======================80
!
!  Periodically write a multigrid convergence file if we are not at end of the
!  run to mitigate the effects of ungraceful exits upon convergence.
!
!=============================================================================80

  subroutine write_mg_convergence()

  continue

    if ( hanim .and. ntt /= fmg_cycles_request(fl)) then
      if ( (ntt/cycle_write_convergence)*cycle_write_convergence == ntt ) then
        if ( lmpi_master )                               &
        call convergence_history( grid(fl)%project, ierr )
        call lmpi_conditional_stop(ierr,'convergence_history1:flow.F90')
        if ( lmpi_master .and. write_resmax_files )           &
        call max_residual_locations( grid(fl), soln(fl), ierr )
        call lmpi_conditional_stop(ierr,'max_residual_locations1:flow.F90')
      endif
    endif

  end subroutine write_mg_convergence

!================================== DUMP_SOLUTION ============================80
!
!  Logical funtion to see if it is time to write a restart file
!
!=============================================================================80

  function dump_solution(bcontinue, ntt_dump)

    logical,           intent(in) :: bcontinue
    integer, optional, intent(in) :: ntt_dump

    logical :: dump_solution

  continue

    dump_solution = .false.

    if ( ntt/iterwrt*iterwrt == ntt )     dump_solution = .true.
    if ( ntt ==  fmg_cycles_request(fl) ) dump_solution = .true.
    if ( .not. bcontinue )                dump_solution = .true.

!   possibly override the scheduled frequency

    if ( present(ntt_dump) ) then
      if (ntt == ntt_dump) dump_solution = .true.
    endif

!   if user has entered negative value in the stop.dat file, don't write restart

    if (istop < 0) dump_solution = .false.

  end function dump_solution


!================================ WRITE_ADAPTED_MESH =========================80
!
!  Periodically write the adapted mesh
!
!=============================================================================80

  subroutine write_adapted_mesh()

    use file_utils,  only : big_endian_io

    character(256) :: grid_filename

  continue

    if (adapt) then
      if(lmpi_master)write(*,*)'Writing new grid file for modified mesh'
      call write_aflr3_mapbc( grid(fl),trim(grid(fl)%project)//'.mapbc')
      if ( big_endian_io() ) then
        grid_filename = trim(grid(fl)%project)//'.b8.ugrid'
      else
        grid_filename = trim(grid(fl)%project)//'.lb8.ugrid'
      end if
      call global_image_ugrid(grid(fl),grid_filename,cl2g_available_arg=.false.)
    end if

  end subroutine write_adapted_mesh


!================================== DEVELOPER_TOOLs ==========================80
!
!  Developer's tool for checking Jacobians and convergence
!
!=============================================================================80

  subroutine developer_tools(bcontinue)

    logical, intent(inout) :: bcontinue

  continue

    if ( check_defect_correction ) then
      call defect_correction_local(grid(fl), soln(fl), &
                                   crow(fl), mass(:,fl))
      rmschk        = 0._dp
      istop         = -999
      bcontinue     = .false.
    elseif ( ntt == ntt_for_jacobian_check ) then
      call check_cmp_jacobians(grid(fl), soln(fl), crow(fl), mass(:,fl))
      rmschk        = 0._dp
      istop         = -999
      bcontinue     = .false.
    elseif ( composite_jacobian_lhs .or. composite_jacobian_rhs ) then
      call check_cmp_jacobians(grid(fl), soln(fl), crow(fl), mass(:,fl))
      rmschk        = 0._dp
      istop         = -999
      bcontinue     = .false.
    endif

  end subroutine developer_tools


!================================== OPTIONAL_OUTPUT ==========================80
!
!  Option dependent-output: flow viz, aerodynamic loads, rotor-specific
!
!=============================================================================80

  subroutine optional_output(bcontinue)

    logical, intent(in) :: bcontinue

  continue

!.....Cut-cell visualization

      if ( dump_solution(bcontinue) ) then
        if ( cut_cell_activated .or. cut_cell_visualize )                      &
          call cut_cell_visualization(grid(fl),soln(fl))
      end if

!.....Output rotor solution file

      if (rotor_flag) then
        if (ntt/irotwrt*irotwrt == ntt) then
          call write_source_grid(1,ntt)
        else
          call write_source_grid(0,ntt)
        end if
      end if

!.....Output the current solution on selected boundaries, extracted surfaces
!     or throughout entire volume

      if (time_moving_grid .and. lmpi_master) call timer('reset')

      call solution_output(grid(fl), soln(fl), ntt, nml_path )

!.....Output unsteady aero loads for comprehensive rotorcraft code

      if (output_comprehensive_loads) then
        call extract_comprehensive_loads( grid(fl), soln(fl) )
        call write_comprehensive_loads()
      end if

!.....Output rotor performance metrics (moving, overset rotor motion)

      if (overset_rotor) call rotor_performance()

  end subroutine optional_output


!==================================== OUTPUT_STATUS ==========================80
!
!  Print lift, drag, residual norm, max resid and location, etc to screen
!
!=============================================================================80

  subroutine output_status()

  continue

    if ( lmpi_master .and. timing ) call timer('Before print_status')

    if ( hanim .and. soln(fl)%n_turb > 0 ) then
      call status_turb( grid(fl)%cc, soln(fl) )
    endif

    if ( .not. all_ssdc ) call set_rwu_time( fl )

    call print_status( grid(fl)%igrid, soln(fl) )

    if ( hanim ) then
      ierr = 0
      if ( lmpi_master ) &
      call convergence_content( fl, soln(fl), ntt, ierr )
      call lmpi_conditional_stop(ierr,'error set_content:flow/main/whatever')
    endif

    if ( hanim .and. soln(fl)%n_turb > 0 .and.                                 &
       ( ntt/iterwrt*iterwrt == ntt .or. ntt == ncyc ) ) then
      call detail_status_turb( grid(fl), soln(fl) )
    endif

    if ( ngrid > 1 ) then
      call nonlinear_multigrid_convergence(soln(fl),fl,iter)
    endif

    if ( lmpi_master .and. timing ) call timer('After print_status')

  end subroutine output_status


!================================== HISTORY_REALLOCS =========================80
!
!  Reallocate arrays that were sized for the number of iterations/time steps
!  at the start of the run, but the flow solver may be called upon to to more
!  iterations/time steps than originally expected (e.g. if used in a framework
!  like HELIOS)
!
!=============================================================================80

  subroutine history_reallocs()

    use allocations, only : my_realloc_ptr
    use force_types, only : reallocate_totforce

    integer  :: factor, size1, size2, size3, new_size

  continue

!   try increasing by a factor of 2 if reallocation is needed

    factor = 2

    size1 = size(soln(fl)%totforce)
    if ( ntt > size1) then
      new_size = factor*size1
      call reallocate_totforce(soln(fl)%totforce, new_size)
    end if

    size1 = size(soln(fl)%rmshist,1)
    size2 = size(soln(fl)%rmshist,2)
    size3 = size(soln(fl)%rmshist,3)
    if ( ntt+1 > size2 ) then        ! definitely need the +1
      new_size = factor*size2
      call my_realloc_ptr(soln(fl)%rmshist,  size1, new_size, size3)
      call my_realloc_ptr(soln(fl)%rmaxhist, size1, new_size, size3)
      call my_realloc_ptr(soln(fl)%xlochist, size1, new_size, size3)
      call my_realloc_ptr(soln(fl)%ylochist, size1, new_size, size3)
      call my_realloc_ptr(soln(fl)%zlochist, size1, new_size, size3)
    end if

    size1 = size(soln(fl)%simtime)
    if ( ntt > size1 ) then
      new_size = factor*size1
      call my_realloc_ptr(soln(fl)%simtime,  new_size)
      call my_realloc_ptr(soln(fl)%walltime, new_size)
      call my_realloc_ptr(soln(fl)%alpha,    new_size)
    end if

    size1 = size(grid(fl)%thetax)
    if ( ntt > size1 ) then
      new_size = factor*size1
      call my_realloc_ptr(grid(fl)%thetax, new_size)
      call my_realloc_ptr(grid(fl)%thetay, new_size)
      call my_realloc_ptr(grid(fl)%thetaz, new_size)
      call my_realloc_ptr(grid(fl)%xorig,  new_size)
      call my_realloc_ptr(grid(fl)%yorig,  new_size)
      call my_realloc_ptr(grid(fl)%zorig,  new_size)
    end if

  end subroutine history_reallocs

end module flow

\section{Grid Motion}\label{s:grid_motion_overview}

\FunThreeD has an extensive capability for grid motion. Grid motion may be rigid, where points in
the grid move in unison, or grid motion may result from elastic deformation of the grid 
to follow the motion of one or more boundaries in the domain. To accommodate both types of grid
motion, a distinction is made between `body motion' and `grid motion'. The user begins by
defining one or more bodies (a \emph{body} being a collection of one or more boundary patches in the grid),
and specifies how that body is to move. 
This section describes how to specify the motion of each body
as rigid motion, elastic motion, or
a combination of rigid and elastic motions.
The motion and deformation of volume grid surrounding the bodies
is controlled through the concept of mesh movement 
(see \sectionref{s:mesh_movement_types}).

Motion requires the introduction of reference frames. \FunThreeD solves the flow equations in an
inertial reference frame (one exception to this, \sectionref{s:nml_noninertial_reference_frame},
can not be used with grid motion).
Each moving body has its own reference frame; the body frame is taken to coincide
with the inertial reference frame at $t=0$. In addition to the inertial and body
reference frames that are automatically bookkept when grid motion is activated, the user may define an
`observer frame' for visualization output. Typically, the `observer frame' is either the inertial frame
(default) or one of the moving body frames.
There is the provision for the observer
frame to move independently, if required. 

\FunThreeD allows for hierarchical (parent/child) body motions in which the motion of one body follows 
the motion of another body. The top level of these  hierarchical motions is the inertial frame, and the
default parent frame of any moving body is taken as the inertial frame (i.e., by default the motion of a body
is assume to be relative to the inertial frame).

For all but a few specialized capabilities discussed below (see \sectionref{s:static_grid_manipulations}),
grid motion requires that \FunThreeD
be executed in `time-accurate' mode (see \sectionref{s:nml_nonlinear_solver_parameters}). To
activate grid motion, 
the input variable \cmd{moving_grid = .true.} in the \cmd{&global} namelist is required.
Finally, an additional input file, \file{moving_body.input}, is needed to establish the
details of the motion (see \sectionref{s:movingbody}).

Any grid motion that the user specifies \emph{leaves the original grid file unaltered}. Grid motion always
starts from the input grid set in the \cmd{&raw_grid} namelist (see \sectionref{s:nml_raw_grid}). The
\FunThreeD restart file contains enough information so that motion can be continued seamlessly through
restarts.  If the user would like to obtain a new grid file with the grid in its position at the end
of the execution, use  the command-line option \cmd{--write_mesh [new_project]}, where \cmd{[new_project]} is
the project name of the new grid file. Note that regardless of the grid format of the original mesh, 
the new mesh format will be in AFLR stream format (\cmd{b8.ugrid}).

The distance function needed for any turbulence model is 
computed once by default, based on the \emph{input} grid. If the entire grid is moved rigidly, the distance function
remains unchanged with motion. However, for deforming grids and (to a lesser extent) rigid overset
grids, the distance from a point to the nearest surface may change to some extent as the grid is moved
from the initial state. For deforming meshes, the grid points nearest to the surface behave as a nearly rigid
grid, so the effect on the distance function is minimal. Currently, it is up to the user to decide if
the extra cost of recomputing the distance function is justified. As defined in 
\sectionref{s:nml_global}, setting \cmd{recompute_turb_dist = .true.} will force the distance function to
be recomputed every \cmd{turb_dist_update_freq} time steps when grid motion is accomplished by deformation,
or if overset grids are used. The distance function is never recomputed for rigid meshes that do not
require overset connectivity.
These same distance function considerations also apply to the specialized capabilities discussed in 
\sectionref{s:static_grid_manipulations}.

\subsection{Overview of \protect\file{moving_body.input}}

Moving-grid cases require the \file{moving_body.input} input file.
Complete details are provided in \sectionref{s:movingbody} and
an overview is proved here.
Within this file, basic information required for all moving-grid cases is accomplished through the 
\cmd{&body_definitions} namelist (see \sectionref{s:nml_body_definitions}). Depending on the the type
of `motion driver' selected (i.e. how the body is to be moved), additional namelist input
is usually required. 
The \cmd{&forced_motion} namelist
(See \sectionref{s:nml_forced_motion}) allows the user to specify 
constant translation
and/or rotational motion of a body.
The \var{&motion_from_file} namelist (see \sectionref{s:nml_motion_from_file})
specifies body motion by providing a time sequence of $4\times4$ transform matrices, and is the most
general means of specifying rigid-body motion.
The \cmd{&observer_motion} namelist (see \sectionref{s:nml_observer_motion}) allows the user to specify motion of an `observer' reference frame.
Visualization (see \sectionref{s:flowvis}) is in the `observer' frame.


\subsection{Choosing the Type of Mesh Movement}\label{s:mesh_movement_types}

The user must choose a means to move the mesh that will accommodate the prescribed
or calculated body motion. 
As described in \sectionref{s:nml_body_definitions}, the choices are \cmd{mesh_movement = `rigid'},
 \cmd{mesh_movement = `deform'}, or a combination of the two (e.g. \cmd{mesh_movement = `rigid+deform'}.
Whenever possible, the most robust and fastest executing option is `rigid',
since rigid motion is quickly and efficiently applied
via a $4\times4$ transform matrix (see \sectionref{s:transform_mats}) and is guaranteed to maintain positive cell volumes
as the mesh is moved. Mesh deformation, on the other hand, increases the computational time as it requires
the solution of an additional system of partial differential equations (linear elasticity). Furthermore, 
there is no guarantee of positive cell volumes
after deformation. 
\FunThreeD will automatically try to fix negative volumes during deformation,
but this is rarely successful.
Execution terminates when negative cell volumes are encountered and cannot be remedied.

Elastic body deformations of the preclude purely rigid grid motion 
(i.e., the body itself undergoing deformation, 
       as occurs for aeroelastic bodies).
However, some cases can benefit from
the combination of rigid and deforming mesh motion.
This can allows large-scale motion to be described as rigid motion and
reduces the magnitude of the elastic deformation to smaller-scale motions.
Reducing the magnitude of body elastic motions can reduce the cost of
computing the elastic deformation of the adjacent volume grid.
Smaller elastic deformations also increase the robustness by reducing
the likelihood of generating negative volumes.
An example of combined rigid and elastic deformation is a rotor blade of
a rotorcraft simulation, 
where rigid motion is used to rotate the blades around the shaft axis and 
elastic deformation is used to deform the the blade under aerodynamic loading. 

\subsection{$4\times4$ Transform Matrices}\label{s:transform_mats}

All rigid motions (body and/or grid) within \FunThreeD are accomplished via application of $4\times4$
matrices to describe affine transformations.\cite{geom-tool-comp-graph}
These transforms map a body/grid from the position at $t=0$ to the current
position at $t=T$. The inverse of these transforms are used to map a body/grid
from its current position back to its position at $t=0$.  Although the user usually does not
need to know the details of these transform matrices to use the grid motion capability in
\FunThreeD, they are described below for reference.

The $4\times4$ transform matrices contain both translation
and orthonormal rotation components. Given a point at an initial position $(x, y, z)^T$, 
application of the transform
matrix moves the point to its new position $(x^\prime, y^\prime, z^\prime)^T$

\begin{equation*}
\left[ \begin{array}{c} x^\prime \\ y^\prime \\ z^\prime \\ 1  \end{array} \right] =
  \left[ \begin{array}{cccc}
     R_{11} & R_{12} & R_{13} & T_x \\ R_{21} & R_{22} & R_{23} & T_y \\
     R_{31} & R_{32} & R_{33} & T_z \\    0   &    0   &   0    &  1  \\
  \end{array} \right]
  \left[ \begin{array}{c} x \\ y \\ z \\ 1  \end{array} \right] \, \,
\end{equation*}

\noindent
where the $3\times3$ submatrix entries in the upper left define an orthonormal rotation about the origin, $(0, 0, 0)^T$,
and the last column defines a translation. The last row is always set as $(0, 0, 0, 1)^T$.
Application of the inverse of this transform matrix moves the point back to the initial position.

Two often-encountered transforms are a pure translation from the origin to a point $(x_0, y_0, z_0)^T$
and a pure rotation $\theta$ in the direction $\mathbf{\hat{n}}$ (unit vector)
about the origin

\begin{equation*}
[\mathbf{T_0}] =
  \left[ \begin{array}{cccc}
     1 & 0 & 0 & x_0 \\ 0 & 1 & 0 & y_0 \\
     0 & 0 & 1 & z_0 \\ 0 & 0 & 0 &   1 \\
  \end{array} \right]
\end{equation*}

\begin{equation*}
[\mathbf{R_0}] =
  \left[ \begin{array}{cccc}
     (1-c\theta){n_x}^2    + c\theta      &
     (1-c\theta){n_x}{n_y} - {n_z}s\theta &
     (1-c\theta){n_x}{n_z} + {n_y}s\theta & 0 \\
     (1-c\theta){n_x}{n_y} + {n_z}s\theta &
     (1-c\theta){n_y}^2    + c\theta      &
     (1-c\theta){n_y}{n_z} - {n_x}s\theta & 0 \\
     (1-c\theta){n_x}{n_z} - {n_y}s\theta &
     (1-c\theta){n_y}{n_z} + {n_x}s\theta &
     (1-c\theta){n_z}^2    + c\theta      & 0 \\
     0 & 0 & 0 & 1  \\
  \end{array} \right]
\end{equation*}


\noindent
where $s\theta = \sin(\theta)$ and $c\theta = \cos(\theta)$.
The right-hand rule in the direction of $\mathbf{\hat{n}}$ defines the sense of $\theta$.
Other forms of the $3\times3$ rotation submatrix may arise depending on how the motion is specified
(e.g., chained rotations such as Euler angles).
Note that $[\mathbf{T_0}]^{-1}$ is simply $[\mathbf{T_0}]$ with $(x_0, y_0, z_0)^T$ replaced by $(-x_0, -y_0, -z_0)^T$,
and maps $(x_0, y_0, z_0)^T$ to the origin.

An extremely useful feature of the transform matrix approach is that multiple transformations telescope via
matrix multiplication. Thus, rotation about a point $(x_0, y_0, z_0)^T$, by an angle $\theta$, in the direction
$\mathbf{\hat{n}}$ is effected by first translating to the origin, performing the rotation, and then translating back to
$(x_0, y_0, z_0)^T$, which is accomplished via three matrix multiplications to give the complete transform matrix
$[\mathbf{T}]$

\begin{equation*}
[\mathbf{T}] = [\mathbf{T_0}][\mathbf{R_0}][\mathbf{T_0}]^{-1}
\end{equation*}

The telescoping property of the transform matrices is also useful for tracking motions of one body
relative to another.  For example, a wing may be translating up and down, while at the same time,
a flap may be pitching about a hinge line fixed on the wing. It is natural to describe the pitching
motion of the flap in its own coordinate system, which at t=0 is the same as the wing coordinate
system. If the transform matrix describing the plunging of
the wing relative to its initial position in an inertial reference frame is given by
$[\mathbf{T}]_\mathbf{wing}$, and the transform matrix of the pitching motion of the flap relative to a hinge
line defined in the flap coordinate system is given by $[\mathbf{T}]_\mathbf{flap}$, then the position of
the flap, relative to the inertial frame, may by computed using the composite transform

\begin{equation*}
[\mathbf{T}] = [\mathbf{T}]_\mathbf{wing}[\mathbf{T}]_\mathbf{flap}
\end{equation*}

\noindent
The order of matrix multiplication is important: post multiplication of the parent transform takes coordinates
from the child system into the parent system, which is then moved relative to the inertial frame according
to the parent transform. The example above is for a simple, one-generation, parent-child composite motion,
but the concept may be extended to any number of generations.

\subsection{Verifying Grid Motion Inputs}

When setting up a moving-grid case, it is usually a good practice to verify that the desired grid motion will
be performed before an attempt is made with a large and potentially expensive time-dependent flow solution.
There are two options that may
be used to check that the motion setup is correct; both must be used in conjunction with boundary
output to be useful (see \sectionref{s:flowvis}). Both options bypass the flow-solution but not the grid-motion
mechanics.  Because these options decouple the flow solution and the grid motion, they are generally restricted
to cases in which the motion is prescribed by the user. If the motion results from aerodynamic loading, such six degrees of freedom motion or as a result of aeroelastic deformation, there is no complete check but to run the coupled
simulation. However, it may be possible to prescribe a motion that is similar to the expected
motion so that body definitions and time step sizes may be verified prior to the coupled solution.

The first option is enabled by setting
\cmd{body_motion_only = .true.} in the \cmd{&global} namelist. As the name of the option implies, only the
user-defined bodies are moved, not the entire mesh. 
This is the quickest check, especially for deforming
meshes, since the elasticity equations are not solved. 
Volume and surface elements adjacent to the moving bodies will 
likely become inverted during this test.
So, expect to see inverted elements in visualization of output grids on
symmetry boundaries, farfield boundaries, sampling planes, etc.

The second option is more discriminating for deforming grids,
and is enabled by setting \cmd{grid_motion_only = .true.} in the \cmd{&global} namelist.
This option runs through the entire grid-motion mechanics, including solution of the linear elasticity equations
for deforming grids. This will significantly increase the computational time for this check compared to 
\cmd{body_motion_only = .true.}.
For deforming meshes, this option will indicate whether or not the mesh will survive the
prescribed motion without negative volumes. 
All visualization options can be used with this option to observe the surface and volume grid deformation. 

Because  \cmd{body_motion_only = .true.} option is fairly quick even for deforming meshes,
the recommended approach for deforming meshes
is to do start with \cmd{body_motion_only = .true.} to verify that the body moves as desired.  After
the basic motion is verified, the \cmd{grid_motion_only = .true.} can be exercised,
to ensure the deformation will be
successful as the body executes its motion.
Both body and grid motion are fast for rigid meshes.

For periodic motions it is strongly recommended to verify that the bodies return to their initial position after the
expected number of time steps. Failure to return to the initial position at the expected time likely indicates an error
in the input time step or the nondimensional frequency.
Insufficient numerical precision of the
input values of these quantities can prevent a return to the initial position after one period.

\subsection{Static Grid Manipulations}\label{s:static_grid_manipulations}

Several options available to move or deform
the grid during pre-processing are not considered `moving grid' options.
Therefore, these static grid manipulations do not require the code
to be run in a time-accurate manner, the \file{moving_body.input}
file, or \cmd{moving_grid = .true.} in the \cmd{&global} namelist. 
These static manipulations result in a one time movement of
the mesh at the start of the code execution. As with moving grids, the original
grid file specified in the \cmd{&raw_grid} namelist (see \sectionref{s:nml_raw_grid}) remains unaltered. 
The \cmd{body_motion_only} or \cmd{grid_motion_only} options
do not apply to these static grid manipulations.
As for time-accurate moving grids,
the rigid-grid option (\cmd{&grid_transform}) is preferred
over the deforming grid option (\cmd{&body_transform}) whenever possible
because it is less expensive and more robust.

\subsubsection{Grid Transform}

This static grid manipulation is governed by the \cmd{&grid_transform} namelist in the \file{fun3d.nml} file
(see \sectionref{s:nml_grid_transform}).
It allows the \emph{entire} grid to be rotated, translated, or scaled
with a few simple inputs.
A more general manipulation of the grid may be obtained through the input of a $4\times4$ transform matrix.

\subsubsection{Body Transform}

This static grid manipulation is governed by the \cmd{&body_transform} namelist in the \file{fun3d.nml} file 
(see \sectionref{s:nml_body_transform}).
It allows user-defined bodies to be rotated or translated (but not scaled).
An example would be to make a small change to the angle of a flap,
while leaving the orientation of the wing unchanged.
The namelist allows for one or more bodies to be defined as collections of
one or more boundaries in the grid,
where each body may be manipulated independently of the others.
As with the \cmd{&grid_transform} option, a $4\times4$ transform
matrix may be input if the simple rotation and translation
options provided by the namelist input are insufficient to describe
the transform.
When this option is used,
the specified bodies will be moved according to the input,
and the mesh will be deformed to
accommodate the new body positions.

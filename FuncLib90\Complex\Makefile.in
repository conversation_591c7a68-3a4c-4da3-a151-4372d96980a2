# Makefile.in generated by automake 1.11.1 from Makefile.am.
# @configure_input@

# Copyright (C) 1994, 1995, 1996, 1997, 1998, 1999, 2000, 2001, 2002,
# 2003, 2004, 2005, 2006, 2007, 2008, 2009  Free Software Foundation,
# Inc.
# This Makefile.in is free software; the Free Software Foundation
# gives unlimited permission to copy and/or distribute it,
# with or without modifications, as long as this notice is preserved.

# This program is distributed in the hope that it will be useful,
# but WITHOUT ANY WARRANTY, to the extent permitted by law; without
# even the implied warranty of MERCHANTABILITY or FITNESS FOR A
# PARTICULAR PURPOSE.

@SET_MAKE@
VPATH = @srcdir@
pkgdatadir = $(datadir)/@PACKAGE@
pkgincludedir = $(includedir)/@PACKAGE@
pkglibdir = $(libdir)/@PACKAGE@
pkglibexecdir = $(libexecdir)/@PACKAGE@
am__cd = CDPATH="$${ZSH_VERSION+.}$(PATH_SEPARATOR)" && cd
install_sh_DATA = $(install_sh) -c -m 644
install_sh_PROGRAM = $(install_sh) -c
install_sh_SCRIPT = $(install_sh) -c
INSTALL_HEADER = $(INSTALL_DATA)
transform = $(program_transform_name)
NORMAL_INSTALL = :
PRE_INSTALL = :
POST_INSTALL = :
NORMAL_UNINSTALL = :
PRE_UNINSTALL = :
POST_UNINSTALL = :
build_triplet = @build@
host_triplet = @host@
target_triplet = @target@
DIST_COMMON = $(srcdir)/../Common.am $(srcdir)/Makefile.am \
	$(srcdir)/Makefile.in
subdir = FuncLib90/Complex
ACLOCAL_M4 = $(top_srcdir)/aclocal.m4
am__aclocal_m4_deps =  \
	$(top_srcdir)/aclocal/ax_f90_module_extension.m4 \
	$(top_srcdir)/aclocal/ax_f90_module_flag.m4 \
	$(top_srcdir)/aclocal/capri.m4 $(top_srcdir)/aclocal/cgns.m4 \
	$(top_srcdir)/aclocal/cuda.m4 \
	$(top_srcdir)/aclocal/dynamic_loading.m4 \
	$(top_srcdir)/aclocal/f90_tuner.m4 \
	$(top_srcdir)/aclocal/f90_unix.m4 \
	$(top_srcdir)/aclocal/fccht.m4 \
	$(top_srcdir)/aclocal/fortran_2003_environment.m4 \
	$(top_srcdir)/aclocal/fortran_asynchronous_io.m4 \
	$(top_srcdir)/aclocal/fortran_c_interoperability.m4 \
	$(top_srcdir)/aclocal/fortran_etime.m4 \
	$(top_srcdir)/aclocal/fortran_open_big_endian.m4 \
	$(top_srcdir)/aclocal/fortran_open_stream.m4 \
	$(top_srcdir)/aclocal/fortran_posix_interface.m4 \
	$(top_srcdir)/aclocal/fun3d.m4 $(top_srcdir)/aclocal/gsi.m4 \
	$(top_srcdir)/aclocal/meshsim.m4 $(top_srcdir)/aclocal/mpi.m4 \
	$(top_srcdir)/aclocal/parmetis.m4 \
	$(top_srcdir)/aclocal/resource_limit.m4 \
	$(top_srcdir)/aclocal/zoltan.m4 $(top_srcdir)/configure.ac
am__configure_deps = $(am__aclocal_m4_deps) $(CONFIGURE_DEPENDENCIES) \
	$(ACLOCAL_M4)
mkinstalldirs = $(install_sh) -d
CONFIG_HEADER = $(top_builddir)/config.h
CONFIG_CLEAN_FILES =
CONFIG_CLEAN_VPATH_FILES =
SOURCES =
DIST_SOURCES =
DISTFILES = $(DIST_COMMON) $(DIST_SOURCES) $(TEXINFOS) $(EXTRA_DIST)
ACLOCAL = @ACLOCAL@
ACLOCAL_AMFLAGS = @ACLOCAL_AMFLAGS@
AMTAR = @AMTAR@
AUTOCONF = @AUTOCONF@
AUTOHEADER = @AUTOHEADER@
AUTOMAKE = @AUTOMAKE@
AWK = @AWK@
BIBTEX = @BIBTEX@
CAPRIheader = @CAPRIheader@
CAPRIlibrary = @CAPRIlibrary@
CC = @CC@
CCDEPMODE = @CCDEPMODE@
CFLAGS = @CFLAGS@
CGNSinclude = @CGNSinclude@
CGNSlibrary = @CGNSlibrary@
CPP = @CPP@
CPPFLAGS = @CPPFLAGS@
CUDACC = @CUDACC@
CUDAFLAGS = @CUDAFLAGS@
CUDA_LIB_PATH = @CUDA_LIB_PATH@
CXX = @CXX@
CXXDEPMODE = @CXXDEPMODE@
CXXFLAGS = @CXXFLAGS@
CYGPATH_W = @CYGPATH_W@
DEFS = @DEFS@
DEPDIR = @DEPDIR@
ECHO_C = @ECHO_C@
ECHO_N = @ECHO_N@
ECHO_T = @ECHO_T@
EGREP = @EGREP@
EXEEXT = @EXEEXT@
F90_EXT_LIB = @F90_EXT_LIB@
FC = @FC@
FCFLAGS = @FCFLAGS@
FCLIBS = @FCLIBS@
FC_MODEXT = @FC_MODEXT@
FC_MODINC = @FC_MODINC@
GREP = @GREP@
HAVE_F2PY = @HAVE_F2PY@
HAVE_RUBY = @HAVE_RUBY@
INSTALL = @INSTALL@
INSTALL_DATA = @INSTALL_DATA@
INSTALL_PROGRAM = @INSTALL_PROGRAM@
INSTALL_SCRIPT = @INSTALL_SCRIPT@
INSTALL_STRIP_PROGRAM = @INSTALL_STRIP_PROGRAM@
KNIFE_SUBDIR = @KNIFE_SUBDIR@
LDFLAGS = @LDFLAGS@
LIBOBJS = @LIBOBJS@
LIBS = @LIBS@
LTLIBOBJS = @LTLIBOBJS@
MAKEINFO = @MAKEINFO@
MKDIR_P = @MKDIR_P@
MOD_DEP_COMPILER = @MOD_DEP_COMPILER@
MPIF90 = @MPIF90@
MPIINC = @MPIINC@
MPIRUN = @MPIRUN@
MPI_EXT = @MPI_EXT@
MPI_Prefix = @MPI_Prefix@
OBJEXT = @OBJEXT@
PACKAGE = @PACKAGE@
PACKAGE_BUGREPORT = @PACKAGE_BUGREPORT@
PACKAGE_NAME = @PACKAGE_NAME@
PACKAGE_STRING = @PACKAGE_STRING@
PACKAGE_TARNAME = @PACKAGE_TARNAME@
PACKAGE_VERSION = @PACKAGE_VERSION@
PATH_SEPARATOR = @PATH_SEPARATOR@
PDFLATEX = @PDFLATEX@
PERL5 = @PERL5@
PHYSICS_TYPE = @PHYSICS_TYPE@
PYTHON = @PYTHON@
PYTHON_EXEC_PREFIX = @PYTHON_EXEC_PREFIX@
PYTHON_PLATFORM = @PYTHON_PLATFORM@
PYTHON_PREFIX = @PYTHON_PREFIX@
PYTHON_SUBDIR = @PYTHON_SUBDIR@
PYTHON_VERSION = @PYTHON_VERSION@
RANLIB = @RANLIB@
REFINE_SUBDIR = @REFINE_SUBDIR@
SBOOMlibrary = @SBOOMlibrary@
SDKheader = @SDKheader@
SDKlibrary = @SDKlibrary@
SET_MAKE = @SET_MAKE@
SHELL = @SHELL@
SIXDOFLIBS = @SIXDOFLIBS@
STRIP = @STRIP@
TECIOLIBS = @TECIOLIBS@
VERSION = @VERSION@
VisItinclude = @VisItinclude@
VisItlibrary = @VisItlibrary@
XMKMF = @XMKMF@
Xheader = @Xheader@
Xlibrary = @Xlibrary@
abs_builddir = @abs_builddir@
abs_srcdir = @abs_srcdir@
abs_top_builddir = @abs_top_builddir@
abs_top_srcdir = @abs_top_srcdir@
ac_ct_CC = @ac_ct_CC@
ac_ct_CXX = @ac_ct_CXX@
ac_ct_FC = @ac_ct_FC@
ac_empty = @ac_empty@
am__include = @am__include@
am__leading_dot = @am__leading_dot@
am__quote = @am__quote@
am__tar = @am__tar@
am__untar = @am__untar@
bindir = @bindir@
build = @build@
build_alias = @build_alias@
build_cpu = @build_cpu@
build_os = @build_os@
build_vendor = @build_vendor@
builddir = @builddir@
datadir = @datadir@
datarootdir = @datarootdir@
dirtlibrary = @dirtlibrary@
docdir = @docdir@
dotlibrary = @dotlibrary@
dvidir = @dvidir@
dymorelibrary = @dymorelibrary@
exec_prefix = @exec_prefix@
fcompiler = @fcompiler@
have_bibtex = @have_bibtex@
have_latex = @have_latex@
host = @host@
host_alias = @host_alias@
host_cpu = @host_cpu@
host_os = @host_os@
host_vendor = @host_vendor@
htmldir = @htmldir@
includedir = @includedir@
infodir = @infodir@
install_sh = @install_sh@
irslibrary = @irslibrary@
knife_deps = @knife_deps@
knife_ldadd = @knife_ldadd@
ksoptlibrary = @ksoptlibrary@
libcore_path = @libcore_path@
libdir = @libdir@
libexecdir = @libexecdir@
libturb_path = @libturb_path@
localedir = @localedir@
localstatedir = @localstatedir@
mandir = @mandir@
meshsim_ldadd = @meshsim_ldadd@
mkdir_p = @mkdir_p@
mpi_ldadd = @mpi_ldadd@
npsollibrary = @npsollibrary@
oldincludedir = @oldincludedir@
parmetis_include = @parmetis_include@
parmetis_ldadd = @parmetis_ldadd@
pdfdir = @pdfdir@
pkgpyexecdir = @pkgpyexecdir@
pkgpythondir = @pkgpythondir@
portlibrary = @portlibrary@
prefix = @prefix@
program_transform_name = @program_transform_name@
psdir = @psdir@
punditinclude = @punditinclude@
punditlibrary = @punditlibrary@
pyexecdir = @pyexecdir@
pythondir = @pythondir@
refine_deps = @refine_deps@
refine_ldadd = @refine_ldadd@
sbindir = @sbindir@
sdxlibrary = @sdxlibrary@
sfelibrary = @sfelibrary@
sharedstatedir = @sharedstatedir@
snoptlibrary = @snoptlibrary@
sparskitlibrary = @sparskitlibrary@
srcdir = @srcdir@
ssdclibrary = @ssdclibrary@
subdirs = @subdirs@
suggarlibrary = @suggarlibrary@
sysconfdir = @sysconfdir@
target = @target@
target_alias = @target_alias@
target_cpu = @target_cpu@
target_os = @target_os@
target_vendor = @target_vendor@
top_build_prefix = @top_build_prefix@
top_builddir = @top_builddir@
top_srcdir = @top_srcdir@
zoltan_include = @zoltan_include@
zoltan_ldadd = @zoltan_ldadd@
FuncLib90_SRCS = \
	adaptive_entropy_fix.f90 \
	aharten.f90 \
	bary_tet.f90 \
	characteristic_difference.f90 \
	coords_cylindrical_polar.f90 \
	cross_product.f90 \
	cross_product_x.f90 \
	crow_entry.f90 \
	daharten.f90 \
	des_velterm.f90 \
	det_3x3.f90 \
	det_4x4.f90 \
	df_conv.f90 \
	dfc_tangency.f90 \
	dfc_tangency_i.f90 \
	dfdubar.f90 \
	dfl_tangency.f90 \
	dflroe.f90 \
	dflroe_aj.f90 \
	dflroe_i.f90 \
	dfroe.f90 \
	dfroe_aj.f90 \
	dfroe_i.f90 \
	dfduc3_i.f90 \
	df_unsplit.f90 \
	df_unsplit_i.f90 \
	dfv_eb.f90 \
	dfv_eb_i.f90 \
	dfv_flsq.f90 \
	dfv_flsq_i.f90 \
	dfv_mom_dgrad.f90 \
	dfv_node_avg.f90 \
	dfv_node_avg_i.f90 \
	dfvf.f90 \
	dfvf_i.f90 \
	dfvs_scalar.f90 \
	dgrad_node_avg.f90 \
	dgradn_flsq.f90 \
	dkw_source.f90 \
	dmut_sa.f90 \
	dq.f90 \
	dqc_via_dnu.f90 \
	dqc_via_duvw.f90 \
	dqccm.f90 \
	dqt_flsq.f90 \
	dqtf_node_avg.f90 \
	dqumuscl.f90 \
	dsa1_psi.f90 \
	dsa1_turb_abs.f90 \
	dsa3_psi.f90 \
	dsa_source.f90 \
	dsa_source_s.f90 \
	dviscosity_law.f90 \
	ebv_tet_flux.f90 \
	ebv_tet_jac.f90 \
	edge_augment_weight.f90 \
	edge_ngrad.f90 \
	element_center.f90 \
	element_edge_da.f90 \
	element_big_angle.f90 \
	element_grad.f90 \
	element_dgrad.f90 \
	flsq_map_ref.f90 \
	flsq_wsq.f90 \
	flux_aufs.f90 \
	flux_conv.f90 \
	flux_ecroe.f90 \
	flux_hllcs.f90 \
	flux_hllcs_ddt.f90 \
	flux_ldfss.f90 \
	flux_ldfss_ddt.f90 \
	flux_proe.f90 \
	flux_roe.f90 \
	flux_roe_i.f90 \
	flux_roe_tangency.f90 \
	flux_roe_dc.f90 \
	flux_rrhll.f90 \
	flux_stvd.f90 \
	flux_turb_advection.f90 \
	flux_vanleer.f90 \
	flux_unsplit.f90 \
	flux_unsplit_i.f90 \
	flux_upwind_hvisc17.f90 \
	flux_visc.f90 \
	flux_visc_i.f90 \
	fv_flsq.f90 \
	fv_flsq_i.f90 \
	fv_node_avg.f90 \
	fv_node_avg_i.f90 \
	fvs_scalar.f90 \
	get_p.f90 \
	get_q.f90 \
	get_q_ddt.f90 \
	grad_face_lsq.f90 \
	grad_flsq.f90 \
	grad_in_tet.f90 \
	grad_node_avg.f90 \
	hrles_blend.f90 \
	in_conserved_variables.f90 \
	in_primitive_variables.f90 \
	iswch_coef.f90 \
	iswch_coef_ddt.f90 \
	keep_turb_data.f90 \
	kw_blend.f90 \
	kw_source.f90 \
	log_mean.f90 \
	lsq_coords.f90 \
	lsq_grad_stn.f90 \
	lsq_gradc.f90 \
	lsq_lc_max.f90 \
	lsq_map_ref.f90 \
	lsq_scoords.f90 \
	lsq_scoords_sx.f90 \
	lsq_scoords_sy.f90 \
	lsq_scoords_sz.f90 \
	lstgs_func.f90 \
	mapping_coords.f90 \
	mapping_system.f90 \
	minmods.f90 \
	minmodv.f90 \
	nodes_to_node_avg.f90 \
	omega_factor.f90 \
	pressure_limiter.f90 \
	pswitch.f90 \
	q_criterion.f90 \
	q_face_lsq.f90 \
	qf.f90 \
	qfcc.f90 \
	qp_from_qt.f90 \
	qr_tangency.f90 \
	qr_tangency_i.f90 \
	qt_from_qc.f90 \
	qt_from_qp.f90 \
	qtf_node_avg.f90 \
	qtgrad_node_avg.f90 \
	r_vec.f90 \
	reconstruct_weight.f90 \
	roe_avg.f90 \
	roe_efix.f90 \
	roe_efix_u.f90 \
	sa0_turb_abs.f90 \
	sa_source.f90 \
	setup_t.f90 \
	setup_t_inverse.f90 \
	shk_fit_shkspd.f90 \
	skin_fric.f90 \
	skip_plane.f90 \
	smthlms.f90 \
	smthlmv.f90 \
	tang_vecs.f90 \
	tau_v.f90 \
	tau_v_i.f90 \
	tinv_3d.f90 \
	tinverse.f90 \
	trv_from_t.f90 \
	turb_fluctuations.f90 \
	turb_index.f90 \
	turb_limit.f90 \
	vaflxls.f90 \
	vaflxlv.f90 \
	viscosity_law_ddt.f90 \
	viscosity_law_ddt5.f90 \
	vkflxls.f90 \
	vlflxls.f90 \
	vlflxlv.f90 \
	vnlrFJ.f90 \
	vnlrFJc.f90 \
	vol_tet.f90 \
	vswch_coef.f90 \
	vswch_coef_ddt.f90 \
	vswch_coef_orig.f90 \
	vswch_coef_orig_ddt.f90 \
	w_update.f90 \
	weights.f90 \
	y_plus.f90

BUILT_SOURCES = $(FuncLib90_SRCS)
CLEANFILES = $(BUILT_SOURCES)
all: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) all-am

.SUFFIXES:
$(srcdir)/Makefile.in:  $(srcdir)/Makefile.am $(srcdir)/../Common.am $(am__configure_deps)
	@for dep in $?; do \
	  case '$(am__configure_deps)' in \
	    *$$dep*) \
	      ( cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh ) \
	        && { if test -f $@; then exit 0; else break; fi; }; \
	      exit 1;; \
	  esac; \
	done; \
	echo ' cd $(top_srcdir) && $(AUTOMAKE) --gnu FuncLib90/Complex/Makefile'; \
	$(am__cd) $(top_srcdir) && \
	  $(AUTOMAKE) --gnu FuncLib90/Complex/Makefile
.PRECIOUS: Makefile
Makefile: $(srcdir)/Makefile.in $(top_builddir)/config.status
	@case '$?' in \
	  *config.status*) \
	    cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh;; \
	  *) \
	    echo ' cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe)'; \
	    cd $(top_builddir) && $(SHELL) ./config.status $(subdir)/$@ $(am__depfiles_maybe);; \
	esac;

$(top_builddir)/config.status: $(top_srcdir)/configure $(CONFIG_STATUS_DEPENDENCIES)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh

$(top_srcdir)/configure:  $(am__configure_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(ACLOCAL_M4):  $(am__aclocal_m4_deps)
	cd $(top_builddir) && $(MAKE) $(AM_MAKEFLAGS) am--refresh
$(am__aclocal_m4_deps):
tags: TAGS
TAGS:

ctags: CTAGS
CTAGS:


distdir: $(DISTFILES)
	@srcdirstrip=`echo "$(srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	topsrcdirstrip=`echo "$(top_srcdir)" | sed 's/[].[^$$\\*]/\\\\&/g'`; \
	list='$(DISTFILES)'; \
	  dist_files=`for file in $$list; do echo $$file; done | \
	  sed -e "s|^$$srcdirstrip/||;t" \
	      -e "s|^$$topsrcdirstrip/|$(top_builddir)/|;t"`; \
	case $$dist_files in \
	  */*) $(MKDIR_P) `echo "$$dist_files" | \
			   sed '/\//!d;s|^|$(distdir)/|;s,/[^/]*$$,,' | \
			   sort -u` ;; \
	esac; \
	for file in $$dist_files; do \
	  if test -f $$file || test -d $$file; then d=.; else d=$(srcdir); fi; \
	  if test -d $$d/$$file; then \
	    dir=`echo "/$$file" | sed -e 's,/[^/]*$$,,'`; \
	    if test -d "$(distdir)/$$file"; then \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    if test -d $(srcdir)/$$file && test $$d != $(srcdir); then \
	      cp -fpR $(srcdir)/$$file "$(distdir)$$dir" || exit 1; \
	      find "$(distdir)/$$file" -type d ! -perm -700 -exec chmod u+rwx {} \;; \
	    fi; \
	    cp -fpR $$d/$$file "$(distdir)$$dir" || exit 1; \
	  else \
	    test -f "$(distdir)/$$file" \
	    || cp -p $$d/$$file "$(distdir)/$$file" \
	    || exit 1; \
	  fi; \
	done
check-am: all-am
check: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) check-am
all-am: Makefile
installdirs:
install: $(BUILT_SOURCES)
	$(MAKE) $(AM_MAKEFLAGS) install-am
install-exec: install-exec-am
install-data: install-data-am
uninstall: uninstall-am

install-am: all-am
	@$(MAKE) $(AM_MAKEFLAGS) install-exec-am install-data-am

installcheck: installcheck-am
install-strip:
	$(MAKE) $(AM_MAKEFLAGS) INSTALL_PROGRAM="$(INSTALL_STRIP_PROGRAM)" \
	  install_sh_PROGRAM="$(INSTALL_STRIP_PROGRAM)" INSTALL_STRIP_FLAG=-s \
	  `test -z '$(STRIP)' || \
	    echo "INSTALL_PROGRAM_ENV=STRIPPROG='$(STRIP)'"` install
mostlyclean-generic:

clean-generic:
	-test -z "$(CLEANFILES)" || rm -f $(CLEANFILES)

distclean-generic:
	-test -z "$(CONFIG_CLEAN_FILES)" || rm -f $(CONFIG_CLEAN_FILES)
	-test . = "$(srcdir)" || test -z "$(CONFIG_CLEAN_VPATH_FILES)" || rm -f $(CONFIG_CLEAN_VPATH_FILES)

maintainer-clean-generic:
	@echo "This command is intended for maintainers to use"
	@echo "it deletes files that may require special tools to rebuild."
	-test -z "$(BUILT_SOURCES)" || rm -f $(BUILT_SOURCES)
clean: clean-am

clean-am: clean-generic mostlyclean-am

distclean: distclean-am
	-rm -f Makefile
distclean-am: clean-am distclean-generic

dvi: dvi-am

dvi-am:

html: html-am

html-am:

info: info-am

info-am:

install-data-am:

install-dvi: install-dvi-am

install-dvi-am:

install-exec-am:

install-html: install-html-am

install-html-am:

install-info: install-info-am

install-info-am:

install-man:

install-pdf: install-pdf-am

install-pdf-am:

install-ps: install-ps-am

install-ps-am:

installcheck-am:

maintainer-clean: maintainer-clean-am
	-rm -f Makefile
maintainer-clean-am: distclean-am maintainer-clean-generic

mostlyclean: mostlyclean-am

mostlyclean-am: mostlyclean-generic

pdf: pdf-am

pdf-am:

ps: ps-am

ps-am:

uninstall-am:

.MAKE: all check install install-am install-strip

.PHONY: all all-am check check-am clean clean-generic distclean \
	distclean-generic distdir dvi dvi-am html html-am info info-am \
	install install-am install-data install-data-am install-dvi \
	install-dvi-am install-exec install-exec-am install-html \
	install-html-am install-info install-info-am install-man \
	install-pdf install-pdf-am install-ps install-ps-am \
	install-strip installcheck installcheck-am installdirs \
	maintainer-clean maintainer-clean-generic mostlyclean \
	mostlyclean-generic pdf pdf-am ps ps-am uninstall uninstall-am


%.f90: $(top_srcdir)/FuncLib90/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/FuncLib90/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

# Tell versions [3.59,3.63) of GNU make to not export all variables.
# Otherwise a system limit (for SysV at least) may be exceeded.
.NOEXPORT:

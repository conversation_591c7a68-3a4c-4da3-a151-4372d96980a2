include ../Common.am

LIBCORE_DIR=@libcore_path@/Complex

noinst_LIBRARIES = libdefs.a

libdefs_a_LIBADD =
libdefs_a_SOURCES =
libdefs_a_LINK = $(F90LINK)

nodist_libdefs_a_SOURCES = $(libdefs_SRCS)

BUILT_SOURCES += $(nodist_libdefs_a_SOURCES)

CLEANFILES += $(BUILT_SOURCES)

%.f90: $(top_srcdir)/libdefs/%.f90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.F90: $(top_srcdir)/libdefs/%.F90
	$(top_srcdir)/Complex/complexifile.rb --out $(builddir) $<

%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @builddir@ \
	-I $(LIBCORE_DIR) > $@

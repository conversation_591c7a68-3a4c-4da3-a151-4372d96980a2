module split_element

  use lmpi, only : lmpi_id, lmpi_bcast, lmpi_reduce, lmpi_max, lmpi_nproc,     &
                   lmpi_conditional_stop, lmpi_master, lmpi_allgatherv,        &
                   lmpi_allgather
  implicit none

  private

  public :: split_elements, gather_split_bc

  public :: prisms_to_tets, pyramids_to_tets, hexes_to_tets

contains


!============================= SPLIT_ELEMENTS ================================80
!
! Chop up the hexes, prisms, and pyramids of a mixed-element grid into tets
!
!=============================================================================80
  subroutine split_elements(global_grid)

    use element_defs, only : type_tet, initialize_elem, allocate_elem
    use grid_types,   only : grid_type
    use local_grid,   only : pp_nhead, pp_ntail, pp_csize

    type(grid_type),   intent(inout) :: global_grid

    integer :: ielem, i, ib, ntface, nqface, totalcells, ioflag, ioff
    integer :: inode, nhead, ntail
    integer :: ielem_pyr,  ielem_prz,  ielem_hex,  ielem_tet
    integer :: ntetg_before, ntetg_after, ntet_before, ntet_after, ntet_uniq

    integer, dimension(:), allocatable :: temp

    continue

    if (lmpi_id == 0)                                                          &
    write(*,*)'Converting mixed-element mesh into pure tetrahedral mesh...'

    ntail = 0

    nhead = pp_nhead(lmpi_id)
    ntail = pp_ntail(lmpi_id)

!   determine the element ids for each element type (they can be in any order)

    ielem_pyr = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'pyr') then
        ielem_pyr = i
        exit
      endif
    end do

    ielem_prz = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'prz') then
        ielem_prz = i
        exit
      endif
    end do

    ielem_hex = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'hex') then
        ielem_hex = i
        exit
      endif
    end do

!   if there are no non-tet elements, we have nothing to do - exit this routine
!   In parallel, some PEs may have some (or no) tets.

    i = ielem_hex + ielem_prz + ielem_pyr
    ib = i
    call lmpi_reduce(ib,i)
    call lmpi_bcast(i)

!   if there are no non-tet elements, we have nothing to do - exit this routine

    if (i == 0) then
       write(*,*)'There are no mixed elements to split!'
       return
    end if

!   there are splitable elements - do we also have some tets to start with?

    ntet_before  = 0; ntet_after   = 0
    ntetg_before = 0; ntetg_after  = 0
    ielem_tet    = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'tet') then
        ielem_tet = i
        ntetg_before = global_grid%elem(i)%ncellg
        ntet_before  = global_grid%elem(i)%ncell
        exit
      endif
    end do

    i = ielem_tet
    call lmpi_reduce(ielem_tet,i)
    call lmpi_bcast(i)
    ib = ntetg_before
    call lmpi_max(ib,ntetg_before)
    call lmpi_bcast(ntetg_before)
    ! write(*,*)' tet_index, ntetg_before ',lmpi_id,i,ntetg_before

!   in case we don't have any tets to start with, allocate some stuff for tets,
!   as we will add to the tets element type as we split up other element types

    if (i == 0) then ! got_tets = .false.

      global_grid%nelem = global_grid%nelem + 1
      ielem_tet = global_grid%nelem
      call elem_type_my_realloc(global_grid%elem,global_grid%nelem,            &
                                ielem_tet)
      call initialize_elem(global_grid%elem(global_grid%nelem),type_tet)

      ! Set to 1 for allocation purposes
      global_grid%elem(global_grid%nelem)%ncell  = 1
      global_grid%elem(global_grid%nelem)%ncellg = 1
      call allocate_elem(global_grid%elem(global_grid%nelem), .false.)

      ! Now set the actual cells in element
      global_grid%elem(global_grid%nelem)%ncell  = 0
      global_grid%elem(global_grid%nelem)%ncellg = 0
    end if

!   split any pyramids into tets

    if ( ielem_pyr /= 0 ) then

      if (lmpi_master) write(*,*)'Splitting pyramids into tets...'

      call pyramids_to_tets(global_grid%elem(ielem_pyr)%ncell,                 &
                            global_grid%elem(ielem_pyr)%c2n,                   &
                            global_grid%elem(ielem_tet)%ncell,                 &
                            global_grid%elem(ielem_tet)%c2n,                   &
                            global_grid%elem(ielem_tet)%cl2g,                  &
                            global_grid%elem(ielem_tet)%ncellg)

!     now eliminate old pyramid elements from the elem type (the arrays that
!     comprise the pyramid element type were deallocated in pyramids_to_tets)

      global_grid%nelem = global_grid%nelem - 1
      call elem_type_my_realloc(global_grid%elem,global_grid%nelem,            &
                                ielem_pyr)

    end if

!   again determine the element ids for each element type remaining (note that
!   they can change each time we remove an element type)

    ielem_prz = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'prz') then
        ielem_prz = i
        exit
      endif
    end do

    ielem_hex = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'hex') then
        ielem_hex = i
        exit
      endif
    end do

    ielem_tet = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'tet') then
        ielem_tet = i
        exit
      endif
    end do

!   split any prisms into tets

    if ( ielem_prz /= 0 ) then

      if (lmpi_master) write(*,*)'Splitting prisms into tets...'

      call prisms_to_tets(global_grid%elem(ielem_prz)%ncell,                   &
                          global_grid%elem(ielem_prz)%c2n,                     &
                          global_grid%elem(ielem_tet)%ncell,                   &
                          global_grid%elem(ielem_tet)%c2n,                     &
                          global_grid%elem(ielem_tet)%cl2g,                    &
                          global_grid%elem(ielem_tet)%ncellg)

!     now eliminate old prism elements from the elem type (the arrays that
!     comprise the prism element type were deallocated in prisms_to_tets)

      global_grid%nelem = global_grid%nelem - 1
      call elem_type_my_realloc(global_grid%elem,global_grid%nelem,            &
                                ielem_prz)

    end if

!   again determine the element ids for each element type remaining (note that
!   they can change each time we remove an element type)

    ielem_hex = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'hex') then
        ielem_hex = i
        exit
      endif
    end do

    ielem_tet = 0
    do i = 1, global_grid%nelem
      if(global_grid%elem(i)%type_cell == 'tet') then
        ielem_tet = i
        exit
      endif
    end do

!   split any hexes into tets

    if ( ielem_hex /= 0 ) then

      if (lmpi_master) write(*,*)'Splitting hexes into tets...'

      call hexes_to_tets(global_grid%elem(ielem_hex)%ncell,                    &
                         global_grid%elem(ielem_hex)%c2n,                      &
                         global_grid%elem(ielem_tet)%ncell,                    &
                         global_grid%elem(ielem_tet)%c2n,                      &
                         global_grid%elem(ielem_tet)%cl2g,                     &
                         global_grid%elem(ielem_tet)%ncellg)

!     now eliminate old hex elements from the elem type (the arrays that
!     comprise the hex element type were deallocated in hexes_to_tets)

      global_grid%nelem = global_grid%nelem - 1
      call elem_type_my_realloc(global_grid%elem,global_grid%nelem,            &
                                ielem_hex)

    end if

!   Update pp cell info (pp_csize) and cl2g

    pp_csize(lmpi_id) = global_grid%elem(1)%ncell
    call lmpi_allgather(global_grid%elem(1)%ncell,pp_csize)

    ntet_after  = global_grid%elem(1)%ncell

    ntet_uniq = 0
    do i = ntet_before+1,ntet_after
      inode = minval(global_grid%elem(1)%c2n(1:4,i))
      if ((inode >= nhead).and.(inode <= ntail)) ntet_uniq = ntet_uniq + 1
    end do

    allocate(temp(0:lmpi_nproc-1)); temp = 0
    call lmpi_allgather(ntet_uniq,temp)
    ntetg_after = ntetg_before + sum(temp)
    global_grid%elem(1)%ncellg = ntetg_after
    global_grid%ncellg         = ntetg_after

    ioff = 0
    if (lmpi_id > 0) ioff = sum(temp(0:lmpi_id-1))
    ioff = ioff + ntetg_before

    deallocate(temp)

    ! write(*,*)'UNIQ ',lmpi_id,ntet_uniq,ntetg_after
    ! write(*,*)"TET b/a ",lmpi_id,ntetg_before,ntetg_after, &
    !            ntet_before,ntet_after

!   Only update cl2g unique to current processor (i.e., node1 in cell)

    ib = 0
    do i = ntet_before+1,global_grid%elem(1)%ncell
      inode = minval(global_grid%elem(1)%c2n(1:4,i))
      if ((inode >= nhead).and.(inode <= ntail)) then
        ib = ib + 1
        global_grid%elem(1)%cl2g(i) = ioff + ib
      end if
    end do

    ! do i = 1,global_grid%elem(1)%ncell
    !    if (global_grid%elem(1)%cl2g(i) > 0) then
    !       write(900+lmpi_id,*) global_grid%elem(1)%cl2g(i),  &
    !                            global_grid%elem(1)%c2n(:,i)
    !    end if
    ! end do

!------------------------------------------------------------------------------
!
!   at this point we have broken all element into tets, all we are left to do
!   is to split any quad faces into triangles

    ioflag = 0

    do ib = 1,global_grid%nbound

      if (global_grid%bc(ib)%nbfaceq > 0) then

        if ((ioflag == 0).and.(lmpi_master)) then
          write(*,*)'Splitting quad boundary faces into triangles...'
          ioflag = 1
        end if

        call quads_to_trias(global_grid%bc(ib)%nbfacet,                        &
                            global_grid%bc(ib)%f2ntb,                          &
                            global_grid%bc(ib)%nbfaceq,                        &
                            global_grid%bc(ib)%f2nqb)

      end if

    end do

!   print out info for pure tet grid

    if (lmpi_master) then

       write(*,'(a,i0)') ' Number of nodes          : ', global_grid%nnodesg

       totalcells = 0
       do ielem = 1,global_grid%nelem
          write(*,'(a,a3,a,i10)')                                              &
            ' Number Of ',global_grid%elem(ielem)%type_cell,                   &
            ' cells      : ', global_grid%elem(ielem)%ncellg
          totalcells = totalcells + global_grid%elem(ielem)%ncellg
       end do
       global_grid%ncellg = totalcells

   end if
   call lmpi_bcast(global_grid%ncellg)

   ntface = 0
   nqface = 0
   do ib=1,global_grid%nbound
     ntface = ntface + global_grid%bc(ib)%nbfacet
     nqface = nqface + global_grid%bc(ib)%nbfaceq
   end do

   if (lmpi_master) then

       write(*,'(a,i10)') ' Total Number of cells    : ', global_grid%ncellg
       write(*,'(a,i10)') ' Number of tria bc faces  : ', ntface
       write(*,'(a,i10)') ' Number of quad bc faces  : ', nqface
       write(*,'(a,i10)') ' Total number of bc faces : ', ntface+nqface

    end if

  end subroutine split_elements


!============================= PYRAMIDS_TO_TETS ==============================80
!
! Chop pyramids into tets
!
!   Reference: "How to Subdivide Pyramids, Prisms and Hexahedra into Tetrahedra"
!              J. Dompierre et. al.
!              Centre de recherche en calcul applique (CERCA)
!              Rapport CERCA R99-79
!
!=============================================================================80
  subroutine pyramids_to_tets(npyr,c2n_pyr,ntet,c2n_tet,                       &
                              cl2g_tet, ntetg)

    use allocations, only : my_alloc_ptr, my_realloc_ptr
    use kinddefs,    only : system_i8

    integer,                    intent(in)    :: npyr
    integer,                    intent(inout) :: ntet
    integer(system_i8),         intent(inout) :: ntetg
    integer, dimension(5,npyr), intent(in)    :: c2n_pyr
    integer, dimension(:,:),    pointer       :: c2n_tet
    integer, dimension(:),      pointer       :: cl2g_tet
    integer, dimension(:,:),    pointer       :: c2n_tet_new

    integer :: max_new_tets, ncell_new, indx, start_indx, ierr
    integer :: node1, node2, node3, node4, node5, i, j
    integer(system_i8) :: ncell_new_i8, i8

    continue

    ierr   = 0

    max_new_tets = 2 * npyr   ! split each pyramid into 2 tets

    call my_alloc_ptr(c2n_tet_new,4,max_new_tets)

!   initialize new tet counter

    ncell_new    = 0

    cut_pyramids : do j = 1, npyr

      node1 = c2n_pyr(1,j)
      node2 = c2n_pyr(2,j)
      node3 = c2n_pyr(3,j)
      node4 = c2n_pyr(4,j)
      node5 = c2n_pyr(5,j)

!     check pyramid base node pairs to determine how to split the pyramid

      if ( min(node1,node3) < min(node2,node4)) then

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node2
        c2n_tet_new(3,ncell_new) = node3
        c2n_tet_new(4,ncell_new) = node5

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node3
        c2n_tet_new(3,ncell_new) = node4
        c2n_tet_new(4,ncell_new) = node5

      else if ( min(node2,node4) < min(node1,node3) ) then

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node2
        c2n_tet_new(2,ncell_new) = node3
        c2n_tet_new(3,ncell_new) = node4
        c2n_tet_new(4,ncell_new) = node5

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node2
        c2n_tet_new(2,ncell_new) = node4
        c2n_tet_new(3,ncell_new) = node1
        c2n_tet_new(4,ncell_new) = node5

      else

        write(*,*)'error in pyramid_to_tets...stopping'
        ierr = 1
        exit

      end if

    end do cut_pyramids

    call lmpi_conditional_stop(ierr)

!   increase size of c2n for the tetrahedra and add the new ones
!   also bump up the size of cl2g

    if (ncell_new > 0) then

       start_indx = ntet ! will be 0 if no tets
       ntet = ntet + ncell_new

       call my_realloc_ptr(c2n_tet,4,ntet)
       call my_realloc_ptr(cl2g_tet,ntet)

       do i = 1, ncell_new
         indx = start_indx + i
         c2n_tet(1,indx) = c2n_tet_new(1,i)
         c2n_tet(2,indx) = c2n_tet_new(2,i)
         c2n_tet(3,indx) = c2n_tet_new(3,i)
         c2n_tet(4,indx) = c2n_tet_new(4,i)
       end do

    end if

    ncell_new_i8 = ncell_new
    call lmpi_reduce(ncell_new_i8,i8)
    call lmpi_bcast(i8)
    ntetg = ntetg + i8

!   we can deallocate the c2n_tet_new now

    deallocate(c2n_tet_new)

  end subroutine pyramids_to_tets


!=============================== PRISMS_TO_TETS ==============================80
!
! Chop prisms into tets
!
!   Reference: "How to Subdivide Pyramids, Prisms and Hexahedra into Tetrahedra"
!              J. Dompierre et. al.
!              Centre de recherche en calcul applique (CERCA)
!              Rapport CERCA R99-79
!
!=============================================================================80
  subroutine prisms_to_tets(nprz,c2n_prz,ntet,c2n_tet,                         &
                            cl2g_tet, ntetg)

    use allocations, only : my_alloc_ptr, my_realloc_ptr
    use kinddefs,    only : system_i8

    integer,                    intent(in)    :: nprz
    integer,                    intent(inout) :: ntet
    integer(system_i8),         intent(inout) :: ntetg
    integer, dimension(6,nprz), intent(in)    :: c2n_prz
    integer, dimension(:,:),    pointer       :: c2n_tet
    integer, dimension(:),      pointer       :: cl2g_tet
    integer, dimension(:,:),    pointer       :: c2n_tet_new

    integer               :: max_new_tets, ncell_new, indx, start_indx, ierr
    integer               :: node1, node2, node3, node4, node5, node6
    integer               :: i, j, minnode, node
    integer, dimension(6) :: rotation
    integer, dimension(6) :: rotation_fun3d_numbering

    integer(system_i8) :: ncell_new_i8, i8

    continue

    ierr = 0                  ! set to no errors

    max_new_tets = 3 * nprz   ! split each prism into 3 tets

    call my_alloc_ptr(c2n_tet_new,4,max_new_tets)

!   initialize new tet counter

    ncell_new    = 0

! first renumber the local nodes in the c2n_prz array to conform to
! the ordering required for the algorithm given by the reference:
! nodes 1,2,3 at one triangular end of the prism, nodes 4,5,6 at the other

    cut_prisms : do j = 1, nprz

      node1 = c2n_prz(1,j)
      node2 = c2n_prz(4,j)
      node3 = c2n_prz(6,j)
      node4 = c2n_prz(2,j)
      node5 = c2n_prz(3,j)
      node6 = c2n_prz(5,j)

! determine which of the 6 possible rotations of the prism is required to get
! the smallest global node number at the lower left corner, as per
! figure 6 of the reference (see also table 2 of the reference)
! the reference

      minnode = min( node1, node2, node3, node4, node5, node6 )

      if (node1 == minnode) then

        rotation(1) = 1
        rotation(2) = 2
        rotation(3) = 3
        rotation(4) = 4
        rotation(5) = 5
        rotation(6) = 6

      else if (node2 == minnode) then

        rotation(1) = 2
        rotation(2) = 3
        rotation(3) = 1
        rotation(4) = 5
        rotation(5) = 6
        rotation(6) = 4

      else if (node3 == minnode) then

        rotation(1) = 3
        rotation(2) = 1
        rotation(3) = 2
        rotation(4) = 6
        rotation(5) = 4
        rotation(6) = 5

      else if (node4 == minnode) then

        rotation(1) = 4
        rotation(2) = 6
        rotation(3) = 5
        rotation(4) = 1
        rotation(5) = 3
        rotation(6) = 2

      else if (node5 == minnode) then

        rotation(1) = 5
        rotation(2) = 4
        rotation(3) = 6
        rotation(4) = 2
        rotation(5) = 1
        rotation(6) = 3

      else if (node6 == minnode) then

        rotation(1) = 6
        rotation(2) = 5
        rotation(3) = 4
        rotation(4) = 3
        rotation(5) = 2
        rotation(6) = 1

      else

        rotation(:) = 0   ! to supress compiler warning
        write(*,*)'error in prisms_to_tets...stopping'
        ierr = 1
        exit

     end if

! check rotated prism quad face node pairs to determine how to split the prism

      rotation_fun3d_numbering = rotation
      do node = 1, 6
        if ( 2 == rotation(node) ) rotation_fun3d_numbering(node) = 4
        if ( 3 == rotation(node) ) rotation_fun3d_numbering(node) = 6
        if ( 4 == rotation(node) ) rotation_fun3d_numbering(node) = 2
        if ( 5 == rotation(node) ) rotation_fun3d_numbering(node) = 3
        if ( 6 == rotation(node) ) rotation_fun3d_numbering(node) = 5
      end do

      node1 = c2n_prz(rotation_fun3d_numbering(1),j)
      node2 = c2n_prz(rotation_fun3d_numbering(2),j)
      node3 = c2n_prz(rotation_fun3d_numbering(3),j)
      node4 = c2n_prz(rotation_fun3d_numbering(4),j)
      node5 = c2n_prz(rotation_fun3d_numbering(5),j)
      node6 = c2n_prz(rotation_fun3d_numbering(6),j)

      if ( min(node2,node6) < min(node3,node5)) then

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node2
        c2n_tet_new(3,ncell_new) = node3
        c2n_tet_new(4,ncell_new) = node6

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node2
        c2n_tet_new(3,ncell_new) = node6
        c2n_tet_new(4,ncell_new) = node5

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node5
        c2n_tet_new(3,ncell_new) = node6
        c2n_tet_new(4,ncell_new) = node4

      else if ( min(node3,node5) < min(node2,node6) ) then

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node2
        c2n_tet_new(3,ncell_new) = node3
        c2n_tet_new(4,ncell_new) = node5

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node5
        c2n_tet_new(3,ncell_new) = node3
        c2n_tet_new(4,ncell_new) = node6

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node5
        c2n_tet_new(3,ncell_new) = node6
        c2n_tet_new(4,ncell_new) = node4

      else

        write(*,*)'error in prism_to_tets (2)...stopping'
        ierr = 1
        exit

      end if

    end do cut_prisms

    call lmpi_conditional_stop(ierr)

    start_indx = ntet
    ntet = ntet + ncell_new

!   increase size of c2n for the tetrahedra and add the new ones
!   also bump up the size of cl2g

    call my_realloc_ptr(c2n_tet,4,ntet)
    call my_realloc_ptr(cl2g_tet,ntet)

    do i = 1, ncell_new
      indx = start_indx + i
      c2n_tet(1,indx) = c2n_tet_new(1,i)
      c2n_tet(2,indx) = c2n_tet_new(2,i)
      c2n_tet(3,indx) = c2n_tet_new(3,i)
      c2n_tet(4,indx) = c2n_tet_new(4,i)
    end do

!   we can deallocate the c2n_tet_new now

    deallocate(c2n_tet_new)

    ncell_new_i8 = ncell_new
    call lmpi_reduce(ncell_new_i8,i8)
    call lmpi_bcast(i8)
    ntetg = ntetg + i8

  end subroutine prisms_to_tets


!================================ HEXES_TO_TETS ==============================80
!
! Chop hexes into tets
!
!   Reference: "How to Subdivide Pyramids, Prisms and Hexahedra into Tetrahedra"
!              J. Dompierre et. al.
!              Centre de recherche en calcul applique (CERCA)
!              Rapport CERCA R99-79
!
!=============================================================================80
  subroutine hexes_to_tets(nhex,c2n_hex,ntet,c2n_tet,                          &
                           cl2g_tet, ntetg)

    use allocations, only : my_alloc_ptr, my_realloc_ptr
    use kinddefs,    only : system_i8

    integer,                    intent(in)    :: nhex
    integer,                    intent(inout) :: ntet
    integer(system_i8),         intent(inout) :: ntetg
    integer, dimension(8,nhex), intent(in)    :: c2n_hex
    integer, dimension(:,:),    pointer       :: c2n_tet
    integer, dimension(:),      pointer       :: cl2g_tet
    integer, dimension(:,:),    pointer       :: c2n_tet_new

    integer               :: max_new_tets, ncell_new, indx, start_indx
    integer               :: node1, node2, node3, node4
    integer               :: node5, node6, node7, node8
    integer               :: i, j, minnode, sumbit, ierr
    integer               :: set_choose, temp, rotation2, node
    integer, dimension(8) :: rotation
    integer, dimension(8) :: rotation_fun3d_numbering
    integer, dimension(3) :: bit

    integer(system_i8) :: ncell_new_i8, i8

    continue

    ierr = 0

    max_new_tets = 6 * nhex   ! split each hex into 5 or 6 tets

    call my_alloc_ptr(c2n_tet_new,4,max_new_tets)

!   initialize new tet counter

    ncell_new    = 0

! first renumber the local nodes in the c2n_hex array to conform to the ordering
! required for the algorithm given by the reference: nodes 1,2,3,4 in cyclic
! order on "bottom" face and 5,6,7,8 in cyclic order on "top" face

    cut_hexes : do j = 1, nhex

      node1 = c2n_hex(1,j)
      node2 = c2n_hex(2,j)
      node3 = c2n_hex(4,j)
      node4 = c2n_hex(3,j)
      node5 = c2n_hex(5,j)
      node6 = c2n_hex(6,j)
      node7 = c2n_hex(8,j)
      node8 = c2n_hex(7,j)

!     determine which of the 8 possible rotations of the hex is required to get
!     the smallest global node number at the lower front left corner, as per
!     figure 9 of the reference (see also table 4 of the reference)

      minnode = min( node1, node2, node3, node4, node5, node6, node7, node8 )

      if (node1 == minnode) then

        rotation(1) = 1
        rotation(2) = 2
        rotation(3) = 3
        rotation(4) = 4
        rotation(5) = 5
        rotation(6) = 6
        rotation(7) = 7
        rotation(8) = 8

      else if (node2 == minnode) then

        rotation(1) = 2
        rotation(2) = 1
        rotation(3) = 5
        rotation(4) = 6
        rotation(5) = 3
        rotation(6) = 4
        rotation(7) = 8
        rotation(8) = 7

      else if (node3 == minnode) then

        rotation(1) = 3
        rotation(2) = 2
        rotation(3) = 6
        rotation(4) = 7
        rotation(5) = 4
        rotation(6) = 1
        rotation(7) = 5
        rotation(8) = 8

      else if (node4 == minnode) then

        rotation(1) = 4
        rotation(2) = 1
        rotation(3) = 2
        rotation(4) = 3
        rotation(5) = 8
        rotation(6) = 5
        rotation(7) = 6
        rotation(8) = 7

      else if (node5 == minnode) then

        rotation(1) = 5
        rotation(2) = 1
        rotation(3) = 4
        rotation(4) = 8
        rotation(5) = 6
        rotation(6) = 2
        rotation(7) = 3
        rotation(8) = 7

      else if (node6 == minnode) then

        rotation(1) = 6
        rotation(2) = 2
        rotation(3) = 1
        rotation(4) = 5
        rotation(5) = 7
        rotation(6) = 3
        rotation(7) = 4
        rotation(8) = 8

      else if (node7 == minnode) then

        rotation(1) = 7
        rotation(2) = 3
        rotation(3) = 2
        rotation(4) = 6
        rotation(5) = 8
        rotation(6) = 4
        rotation(7) = 1
        rotation(8) = 5

      else if (node8 == minnode) then

        rotation(1) = 8
        rotation(2) = 4
        rotation(3) = 3
        rotation(4) = 7
        rotation(5) = 5
        rotation(6) = 1
        rotation(7) = 2
        rotation(8) = 6

      else

        rotation(:) = 0   ! to supress compiler warning
        write(*,*)'error in hexes_to_tets...stopping'
        ierr = 1
        exit

      end if

!     rotated hex nodes (after 1st rotation)

      rotation_fun3d_numbering = rotation
      do node = 1, 8
        if ( 3 == rotation(node) ) rotation_fun3d_numbering(node) = 4
        if ( 4 == rotation(node) ) rotation_fun3d_numbering(node) = 3
        if ( 7 == rotation(node) ) rotation_fun3d_numbering(node) = 8
        if ( 8 == rotation(node) ) rotation_fun3d_numbering(node) = 7
      end do

      node1 = c2n_hex(rotation_fun3d_numbering(1),j)
      node2 = c2n_hex(rotation_fun3d_numbering(2),j)
      node3 = c2n_hex(rotation_fun3d_numbering(3),j)
      node4 = c2n_hex(rotation_fun3d_numbering(4),j)
      node5 = c2n_hex(rotation_fun3d_numbering(5),j)
      node6 = c2n_hex(rotation_fun3d_numbering(6),j)
      node7 = c2n_hex(rotation_fun3d_numbering(7),j)
      node8 = c2n_hex(rotation_fun3d_numbering(8),j)

! first compute the "bit" for the 3 special faces. this indicates how the next
! rotation is to be performed and the subsequent division into tets

!     initialize all bits to zero

      bit = 0

!     quad face with rotated nodes 2-3-7-6

      if ( min(node2,node7) < min(node3,node6)) bit(1) = 1

!     quad face with rotated nodes 3-4-8-7

      if ( min(node4,node7) < min(node3,node8)) bit(2) = 1

!     quad face with rotated nodes 5-6-7-8

      if ( min(node5,node7) < min(node6,node8)) bit(3) = 1

!     sum of the face bits indicate how many diagonals go through rotated
!     vertex 7

      sumbit = sum(bit)

!     now check the bit flags to see how the second rotation, about the 1-7
!     axis should be done (see table 5 of the reference)

      if ( bit(1) == 0 .and. bit(2) == 0 .and. bit(3) == 0 ) then

!       no rotation needed

        rotation2 = 0

      else if ( bit(1) == 0 .and. bit(2) == 0 .and. bit(3) == 1 ) then

!       120 deg rotation needed

        rotation2 = 120

      else if ( bit(1) == 0 .and. bit(2) == 1 .and. bit(3) == 0 ) then

!       240 deg rotation needed

        rotation2 = 240

      else if ( bit(1) == 0 .and. bit(2) == 1 .and. bit(3) == 1 ) then

!       no rotation needed

        rotation2 = 0

      else if ( bit(1) == 1 .and. bit(2) == 0 .and. bit(3) == 0 ) then

!       no rotation needed

        rotation2 = 0

      else if ( bit(1) == 1 .and. bit(2) == 0 .and. bit(3) == 1 ) then

!       240 deg rotation needed

        rotation2 = 240

      else if ( bit(1) == 1 .and. bit(2) == 1 .and. bit(3) == 0 ) then

!       120 deg rotation needed

        rotation2 = 120

      else if ( bit(1) == 1 .and. bit(2) == 1 .and. bit(3) == 1 ) then

!       no rotation needed

        rotation2 = 0

      else

        rotation2 = 0   ! to supress compiler warning
        write(*,*)'stopping in hexes_to_tets...something wrong with bit array'
        ierr = 1
        exit

      end if

      select case(rotation2)

        case(0)

!         no rotation

        case(120)

!         affect 120 deg rotation with index shifts

          temp        = rotation(2)
          rotation(2) = rotation(5)
          rotation(5) = rotation(4)
          rotation(4) = temp

          temp        = rotation(6)
          rotation(6) = rotation(8)
          rotation(8) = rotation(3)
          rotation(3) = temp

        case(240)

!         affect 240 deg rotation with index shifts

          temp        = rotation(2)
          rotation(2) = rotation(4)
          rotation(4) = rotation(5)
          rotation(5) = temp

          temp        = rotation(6)
          rotation(6) = rotation(3)
          rotation(3) = rotation(8)
          rotation(8) = temp

        case default

          write(*,*)'error in hexes_to_tets...invalid rotation2 angle'
          ierr = 1
          exit

      end select

!     rotated hex nodes (after 2nd rotation)

      rotation_fun3d_numbering = rotation
      do node = 1, 8
        if ( 3 == rotation(node) ) rotation_fun3d_numbering(node) = 4
        if ( 4 == rotation(node) ) rotation_fun3d_numbering(node) = 3
        if ( 7 == rotation(node) ) rotation_fun3d_numbering(node) = 8
        if ( 8 == rotation(node) ) rotation_fun3d_numbering(node) = 7
      end do

      node1 = c2n_hex(rotation_fun3d_numbering(1),j)
      node2 = c2n_hex(rotation_fun3d_numbering(2),j)
      node3 = c2n_hex(rotation_fun3d_numbering(3),j)
      node4 = c2n_hex(rotation_fun3d_numbering(4),j)
      node5 = c2n_hex(rotation_fun3d_numbering(5),j)
      node6 = c2n_hex(rotation_fun3d_numbering(6),j)
      node7 = c2n_hex(rotation_fun3d_numbering(7),j)
      node8 = c2n_hex(rotation_fun3d_numbering(8),j)

!     check sumbit to to determine how to split the hex

      if ( sumbit == 0 ) then

!       one unique set of 5 splittings

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node2
        c2n_tet_new(3,ncell_new) = node3
        c2n_tet_new(4,ncell_new) = node6

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node3
        c2n_tet_new(3,ncell_new) = node8
        c2n_tet_new(4,ncell_new) = node6

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node3
        c2n_tet_new(3,ncell_new) = node4
        c2n_tet_new(4,ncell_new) = node8

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node1
        c2n_tet_new(2,ncell_new) = node6
        c2n_tet_new(3,ncell_new) = node8
        c2n_tet_new(4,ncell_new) = node5

        ncell_new = ncell_new + 1
        c2n_tet_new(1,ncell_new) = node3
        c2n_tet_new(2,ncell_new) = node8
        c2n_tet_new(3,ncell_new) = node6
        c2n_tet_new(4,ncell_new) = node7

      else if ( sumbit == 1 ) then

!       2 sets of 6 splittings

        set_choose = 1  ! both sets are valid, choose 1st one

        select case (set_choose)

          case(1)

!           set 1

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node6
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node5

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node2
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node4

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node2
            c2n_tet_new(4,ncell_new) = node3

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node2
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node3

          case(2)

!           set 2

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node6
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node5

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node4

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node3

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node2
            c2n_tet_new(2,ncell_new) = node1
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node3

          case default

            write(*,*)'error in subroutine hexes_to_tets...must select set'
            ierr = 1
            exit

        end select

      else if ( sumbit == 2 ) then

!       2 sets of 6 splittings

        set_choose = 1  ! both sets are valid, choose 1st one

        select case (set_choose)

          case(1)

!           set 1

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node5
            c2n_tet_new(3,ncell_new) = node6
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node4
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node5
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node4
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node3

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node6
            c2n_tet_new(4,ncell_new) = node3

          case(2)

!           set 2

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node3
            c2n_tet_new(3,ncell_new) = node4
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node5
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node8

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node4
            c2n_tet_new(4,ncell_new) = node8

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node5
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node3
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node6

          case default

            write(*,*)'error in subroutine hexes_to_tets...must select set'
            ierr = 1
            exit

        end select

      else if ( sumbit == 3 ) then

!       3 sets of 6 splittings

        set_choose = 1  ! all three sets are valid, choose 1st one

        select case (set_choose)

          case(1)

!           set 1

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node3
            c2n_tet_new(3,ncell_new) = node4
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node4
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node5
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node6
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node5

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node2
            c2n_tet_new(2,ncell_new) = node6
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node1

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node2
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node1

          case(2)

!           set 2

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node5

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node6
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node5

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node2
            c2n_tet_new(4,ncell_new) = node3

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node8
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node4

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node4

          case(3)

!           set 3

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node6

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node2
            c2n_tet_new(3,ncell_new) = node3
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node3
            c2n_tet_new(3,ncell_new) = node4
            c2n_tet_new(4,ncell_new) = node7

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node6
            c2n_tet_new(3,ncell_new) = node7
            c2n_tet_new(4,ncell_new) = node5

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node4
            c2n_tet_new(4,ncell_new) = node8

            ncell_new = ncell_new + 1
            c2n_tet_new(1,ncell_new) = node1
            c2n_tet_new(2,ncell_new) = node7
            c2n_tet_new(3,ncell_new) = node8
            c2n_tet_new(4,ncell_new) = node5

          case default

            write(*,*)'error in subroutine hexes_to_tets...must select set'
            ierr = 1
            exit

        end select

      else

        write(*,*)'error in hex_to_tets...stopping'
        ierr = 1
        exit

      end if

    end do cut_hexes

    call lmpi_conditional_stop(ierr)

    start_indx = ntet
    ntet = ntet + ncell_new

!   increase size of c2n for the tetrahedra and add the new ones
!   also bump up the size of cl2g

    call my_realloc_ptr(c2n_tet,4,ntet)
    call my_realloc_ptr(cl2g_tet,ntet)

    do i = 1, ncell_new
      indx = start_indx + i
      c2n_tet(1,indx) = c2n_tet_new(1,i)
      c2n_tet(2,indx) = c2n_tet_new(2,i)
      c2n_tet(3,indx) = c2n_tet_new(3,i)
      c2n_tet(4,indx) = c2n_tet_new(4,i)
    end do

    ncell_new_i8 = ncell_new
    call lmpi_reduce(ncell_new_i8,i8)
    call lmpi_bcast(i8)
    ntetg = ntetg + i8

!   we can deallocate the c2n_tet_new now

    deallocate(c2n_tet_new)

  end subroutine hexes_to_tets


!=============================== QUADS_TO_TRIAS ==============================80
!
! Split quads on boundary into triangles
!
!   Reference: "How to Subdivide Pyramids, Prisms and Hexahedra into Tetrahedra"
!              J. Dompierre et. al.
!              Centre de recherche en calcul applique (CERCA)
!              Rapport CERCA R99-79
!
!=============================================================================80
  subroutine quads_to_trias(nbfacet,f2ntb,nbfaceq,f2nqb)

    use allocations,     only : my_alloc_ptr, my_realloc_ptr

    integer,                 intent(inout) :: nbfacet, nbfaceq
    integer, dimension(:,:), pointer       :: f2ntb, f2nqb

    integer                          :: if, indx, max_new_trias, nface_new
    integer                          :: node1, node2, node3, node4
    integer, dimension(:,:), pointer :: f2ntb_split

  continue

!   split each quad face into 2 triangles

    max_new_trias = 2*nbfaceq

    call my_alloc_ptr(f2ntb_split,max_new_trias,5)

!   initialize new face counter

    nface_new    = 0

    cut_quads : do if=1,nbfaceq

      node1 = f2nqb(if,1)
      node2 = f2nqb(if,2)
      node3 = f2nqb(if,3)
      node4 = f2nqb(if,4)

! note: at this point the f2n arrays contain global node numbers

! check quad node pairs to determine how to split the quad
! (this must be done using the global numbers to insure
! the split is consistent with element splitting)

      if ( min(node1,node3) < min(node2,node4) ) then

        nface_new = nface_new + 1
        f2ntb_split(nface_new,1) = node1
        f2ntb_split(nface_new,2) = node2
        f2ntb_split(nface_new,3) = node3

        nface_new = nface_new + 1
        f2ntb_split(nface_new,1) = node1
        f2ntb_split(nface_new,2) = node3
        f2ntb_split(nface_new,3) = node4

      else if( min(node2,node4) < min(node1,node3) ) then

        nface_new = nface_new + 1
        f2ntb_split(nface_new,1) = node2
        f2ntb_split(nface_new,2) = node3
        f2ntb_split(nface_new,3) = node4

        nface_new = nface_new + 1
        f2ntb_split(nface_new,1) = node2
        f2ntb_split(nface_new,2) = node4
        f2ntb_split(nface_new,3) = node1

      else

        write(*,*)'error in quad_to_tria....stopping'
        stop

      end if

    end do cut_quads

!   reallocate the tria face-to-node pointer array and update

    call my_realloc_ptr(f2ntb, nbfacet + nface_new, 5)

      do if = 1, nface_new
        indx = nbfacet + if
        f2ntb(indx,1) = f2ntb_split(if,1)
        f2ntb(indx,2) = f2ntb_split(if,2)
        f2ntb(indx,3) = f2ntb_split(if,3)
!       f2ntb(indx,4), f2ntb(indx,5) get filled in later
      end do

      nbfacet = nbfacet + nface_new

! reallocate f2nqb array to size 1 by 6 as a placeholder, and zero out nbfaceq

      call my_realloc_ptr(f2nqb, 1, 6)

      nbfaceq = 0

!     we can deallocate the f2ntb_split now

      deallocate(f2ntb_split)

  end subroutine quads_to_trias


!=========================== ELEM_TYPE_MY_REALLOC ============================80
!
! Reallocate array space within an element type
!
!=============================================================================80

  subroutine elem_type_my_realloc(x,dim1,skip_id)

    use element_types, only : elem_type
    use element_defs,  only : initialize_elem, allocate_elem, deallocate_elem

    integer,           intent(in) :: dim1
    integer, optional, intent(in) :: skip_id

    type(elem_type), dimension(:), pointer :: x
    type(elem_type), dimension(:), pointer :: y

    integer :: old_dim1, ierr, i
    integer :: loop_end, icount, elem_remove_id

    continue

!   create another array y, with the same number of elements as in array x.
!   for this, determine the number of elements in array x along each
!   dimension.
!   get the status of this operation in a variable ierr. if successful, this
!   variable ierr should have a value of 0.

    old_dim1 = size(x,1)

    allocate(y(old_dim1),stat=ierr)
    if (ierr /= 0) then
      write(*,*) "Reallocation failed"
      stop ! FIXME: should be lmpi_die or se_exit(1)?
    end if

! Perform y = x operation

    do i = 1, old_dim1
      call initialize_elem(y(i),x(i)%type_cell)
      y(i)%ncell     = x(i)%ncell
      y(i)%ncellg    = x(i)%ncellg
      call allocate_elem(y(i), .false.)
      if (x(i)%ncell > 0) then
         y(i)%cl2g = x(i)%cl2g
         y(i)%c2n  = x(i)%c2n
      end if
    end do

!   having allocated the required memory for y, now release the memory used
!   by the array x.

    do i = 1, old_dim1
      call deallocate_elem(x(i))
    end do

    deallocate(x,stat=ierr)
    if(ierr /= 0)then
      write(6,*)'Reallocation failed'
      stop 2
    end if

!   having safely transferred all the values from the existing array x to y,
!   and having cleaned the array x completely, now allocate dimensions to
!   x to dim1

    allocate(x(dim1),stat=ierr)
    if (ierr /= 0)then
      write(6,*)'Reallocation failed'
      stop 2
    end if

    loop_end = min(dim1,old_dim1)
    if (present(skip_id)) then
      elem_remove_id = skip_id
      loop_end = max(dim1,old_dim1)
    else
      elem_remove_id = 0
    endif

    icount = 0
    do i = 1, loop_end
      if (i /= elem_remove_id) then
        icount = icount + 1
        call initialize_elem(x(icount),y(i)%type_cell)
        x(icount)%ncell     = y(i)%ncell
        x(icount)%ncellg    = y(i)%ncellg
        call allocate_elem(x(icount), .false.)
        if (x(icount)%ncell > 0) then
            x(icount)%cl2g = y(i)%cl2g
            x(icount)%c2n  = y(i)%c2n
         end if
      endif
    end do

    do i = 1, old_dim1
      call deallocate_elem(y(i))
    end do

    deallocate(y,stat=ierr)
    if(ierr /= 0)then
      write(6,*)'Reallocation failed'
      stop 2
    end if

  end subroutine elem_type_my_realloc


!============================= GATHER_SPLIT_BC ===============================80
!
! Gather, the bc's from split_element (tet) using the new l2g.
!
!=============================================================================80
  subroutine gather_split_bc(grid)

    use kinddefs,   only : dp, system_i1
    use grid_types, only : grid_type
    use local_grid, only : pp_nhead, pp_ntail

    type(grid_type),   intent(inout) :: grid

    integer :: ib,i,inode,nhead,ntail,ntface,ntface_all

    integer :: iface, ifound, icell
    integer :: i1,i2,i3,i4, n1,n2,n3, word,word1,word2,word3
    integer :: bsize, tag_size

    logical  :: b1,b2,b3,c1,c2,c3

    real(dp) :: bsize_inv

    integer(system_i1), dimension(:), allocatable :: tag0, tag01

    integer, dimension(:),   allocatable :: temp, counts
    integer, dimension(:,:), allocatable :: tempin,tempout

    integer, parameter :: npt = 3 ! node per tet face

    continue

    if (lmpi_id == 0) write(*,*)'Gathering split bc ',grid%nbound

    nhead = pp_nhead(lmpi_id)
    ntail = pp_ntail(lmpi_id)

    bsize     = bit_size(tag0)
    bsize_inv = 1._dp/bit_size(tag0)
    tag_size = ceiling(grid%nnodesg*bsize_inv)

    allocate(tag0 (tag_size)); tag0  = 0
    allocate(tag01(tag_size)); tag01 = 0

    do i = 1,grid%nnodes01
       word1 = ceiling(grid%l2g(i)*bsize_inv)
       if (i <= grid%nnodes0)                                                  &
          tag0 (word1) = ibset(tag0(word1),  mod(grid%l2g(i),bsize))
       tag01(word1) = ibset(tag01(word1), mod(grid%l2g(i),bsize))
    end do

!   do ib = 1,grid%nbound
!      write(16000+lmpi_id,*) 'ib ',ib,grid%bc(ib)%nbfacet
!      do iface = 1,grid%bc(ib)%nbfacet
!         write(16000+lmpi_id,*) grid%bc(ib)%f2ntb(iface,:)
!      end do
!   end do

    allocate(counts(lmpi_nproc)); counts = 0

    split_bound_quad_to_tri : do ib = 1,grid%nbound
       ntface = 0
    !  write(17000+lmpi_id,*) 'ib nbfacet ',ib,grid%bc(ib)%nbfacet
       do iface = 1,grid%bc(ib)%nbfacet
          inode = grid%bc(ib)%f2ntb(iface,1)
          if ((inode >= nhead).and.(inode <= ntail)) then
             ntface = ntface + 1
    !        write(17000+lmpi_id,*) grid%bc(ib)%f2ntb(iface,1:3)
          end if
       end do

       call lmpi_reduce(ntface,ntface_all)
       call lmpi_bcast(ntface_all)
       ! if (lmpi_master) write(*,*)' ib,ntface_all ',ib,ntface_all

       if (ntface_all > 0) then
          allocate(tempin(npt,ntface)); tempin = 0
          ntface = 0
          do iface = 1,grid%bc(ib)%nbfacet
             inode = grid%bc(ib)%f2ntb(iface,1)
             if ((inode >= nhead).and.(inode <= ntail)) then
                ntface = ntface + 1
                ! NOTE: the swapping of the index
                tempin(1,ntface) = grid%bc(ib)%f2ntb(iface,1)
                tempin(2,ntface) = grid%bc(ib)%f2ntb(iface,2)
                tempin(3,ntface) = grid%bc(ib)%f2ntb(iface,3)
             end if
          end do
          deallocate(grid%bc(ib)%f2ntb)

          allocate(tempout(npt,ntface_all)); tempout = 0

          call lmpi_allgather(ntface,counts)

          call lmpi_allgatherv(tempin,counts,tempout)
          deallocate(tempin)

           allocate(temp(ntface_all)); temp = 0
           ntface = 0
           loop1t: do iface = 1,ntface_all
              n1 = tempout(1,iface)
              word1 = ceiling(n1*bsize_inv)
              b1 = btest(int(tag01(word1)), mod(n1,bsize))
              if (.not.b1) cycle
              c1 = btest(int(tag0(word1)), mod(n1,bsize))

              n2 = tempout(2,iface)
              word2 = ceiling(n2*bsize_inv)
              b2 = btest(int(tag01(word2)), mod(n2,bsize))
              if (.not.b2) cycle
              c2 = btest(int(tag0(word2)), mod(n2,bsize))

              n3 = tempout(3,iface)
              word3 = ceiling(n3*bsize_inv)
              b3 = btest(int(tag01(word3)), mod(n3,bsize))
              if (.not.b3) cycle
              c3 = btest(int(tag0(word3)), mod(n3,bsize))

              if (c1.or.c2.or.c3) then
                 ntface = ntface + 1
                 temp(ntface) = iface
              else ! (not.(c1.or.c2.or.c3))
                 loop2: do icell = 1,size(grid%elem(1)%c2n,2)

                  i1 = grid%elem(1)%c2n(1,icell)
                  i2 = grid%elem(1)%c2n(2,icell)
                  i3 = grid%elem(1)%c2n(3,icell)
                  i4 = grid%elem(1)%c2n(4,icell)

                  if ((n1/=i1).and.(n1/=i2).and.(n1/=i3).and.(n1/=i4)) cycle
                  if ((n2/=i1).and.(n2/=i2).and.(n2/=i3).and.(n2/=i4)) cycle
                  if ((n3/=i1).and.(n3/=i2).and.(n3/=i3).and.(n3/=i4)) cycle

                  ! Find other node by subtraction

                  ifound = (i1+i2+i3+i4) - (n1+n2+n3)
                  word = ceiling(ifound*bsize_inv)
                  c1 = btest(int(tag0(word)), mod(ifound,bsize))
                  if (c1) then
                     ntface = ntface + 1
                     temp(ntface) = iface
                     cycle loop1t
                  end if
                 end do loop2

              end if
           end do loop1t

           grid%bc(ib)%nbfacet = ntface
           grid%bc(ib)%nbfaceq = 0

           allocate(grid%bc(ib)%f2ntb(max(1,ntface),npt+2))
           grid%bc(ib)%f2ntb = 0
           do i = 1,ntface
              grid%bc(ib)%f2ntb(i,1:npt) = tempout(1:npt,temp(i))
           end do
           deallocate(temp)
           deallocate(tempout)
        end if
        grid%bc(ib)%nbfacetg = grid%bc(ib)%nbfacetg + 2* grid%bc(ib)%nbfaceqg
        grid%bc(ib)%nbfaceqg = 0
    end do split_bound_quad_to_tri
    deallocate(counts)
    deallocate(tag0,tag01)

!   do ibound = 1,grid%nbound
!     if (grid%bc(ibound)%nbfacet > 0) then
!        write(6000+lmpi_id,*) 'ib,nbt ',ibound,grid%bc(ibound)%nbfacet
!        do i = 1,grid%bc(ibound)%nbfacet
!           write(6000+lmpi_id,'(6(i0,1x))') grid%bc(ibound)%f2ntb(i,:)
!        end do
!     end if
!     if (grid%bc(ibound)%nbfaceq > 0) then
!        write(6000+lmpi_id,*) 'ib,nbq ',ibound,grid%bc(ibound)%nbfaceq
!        do i = 1,grid%bc(ibound)%nbfaceq
!           write(6000+lmpi_id,'(6(i0,1x))') grid%bc(ibound)%f2nqb(i,:)
!        end do
!     end if
!   end do

  end subroutine gather_split_bc

end module split_element

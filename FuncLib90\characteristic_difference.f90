!======================= CHARACTERISTIC_DIFFERENCE ===========================80
!
! This routine computes the characteristic difference RBdq where
! dq is the primitive variable difference (input)
! B is the transfrom from primitive to characteristic variables
! R is the eigen vector transform from conservative to characteristic difference
! The elements of R and B are input
! A scale factor is allowed which can help in stability in highly expanded wakes
!
!=============================================================================80

  pure function characteristic_difference(dq, q, rho, c, xn, yn, zn, lm,       &
                                          n_energy, n_species, n_etot, n_momx, &
                                          n_momy, n_momz, n_turb_g, n_turb_ke, &
                                          n_dis_nutl, n_energy_last, betat,    &
                                          ndim, sfac)
    use kinddefs,        only : dp
    use fun3d_constants, only : my_half

    integer,  intent(in) :: n_energy, n_species, n_turb_g, n_momx, n_momy,     &
                            n_momz, n_etot, n_energy_last, n_turb_ke,          &
                            n_dis_nutl, ndim
    real(dp), intent(in) :: rho, c, xn, yn, zn, betat, sfac
    real(dp), dimension(3,2),  intent(in ) :: lm
    real(dp), dimension(ndim), intent(in ) :: dq, q
    real(dp)                               :: drho, c2, dvec1, dvec2, dvec_turb
    real(dp), dimension(ndim)              :: characteristic_difference

    drho   = sum(dq(1:n_species))
    c2     = c**2
    dvec1  = -dq(n_etot)
    dvec2  = -rho*c*(xn*dq(n_momx)+yn*dq(n_momy)+zn*dq(n_momz))
    characteristic_difference(1:n_species)                                     &
      = c2*dq(1:n_species) + q(1:n_species)*dvec1/rho
    characteristic_difference(n_momx)                                          &
      = rho*(lm(1,1)*dq(n_momx) + lm(2,1)*dq(n_momy) + lm(3,1)*dq(n_momz))*c
    characteristic_difference(n_momy)                                          &
      = rho*(lm(1,2)*dq(n_momx) + lm(2,2)*dq(n_momy) + lm(3,2)*dq(n_momz))*c
    characteristic_difference(n_momz) = (-dvec1-dvec2)
    characteristic_difference(n_etot) = (-dvec1+dvec2)
    if(n_energy>1)then
      characteristic_difference(n_energy_last)                                 &
        = q(n_energy_last)*(c2*drho + dvec1) + rho*c2*dq(n_energy_last)
    end if
    if(n_turb_g > 1)then
      dvec_turb = -betat*(q(n_turb_ke)*drho + rho*dq(n_turb_ke))
      characteristic_difference(1:n_species) =                                 &
        characteristic_difference(1:n_species) + q(1:n_species)*dvec_turb/rho
      characteristic_difference(n_momz) = characteristic_difference(n_momz)    &
                                        - dvec_turb*my_half
      characteristic_difference(n_etot) = characteristic_difference(n_etot)    &
                                        - dvec_turb*my_half
      if(n_energy>1)then
        characteristic_difference(n_energy_last) =                             &
          characteristic_difference(n_energy_last) + q(n_energy_last)*dvec_turb
      end if
      characteristic_difference(n_turb_ke)                                     &
        = q(n_turb_ke)*(c2*drho + dvec1 + dvec_turb) + rho*c2*dq(n_turb_ke)
      characteristic_difference(n_dis_nutl)                                    &
        = q(n_dis_nutl)*(c2*drho + dvec1 + dvec_turb) + rho*c2*dq(n_dis_nutl)
    end if
    if(n_turb_g == 1)then
      characteristic_difference(n_dis_nutl)                                    &
        = q(n_dis_nutl)*(c2*drho + dvec1) + rho*c2*dq(n_dis_nutl)
    end if
    characteristic_difference = sfac*characteristic_difference
  end function characteristic_difference

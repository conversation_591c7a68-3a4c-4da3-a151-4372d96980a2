/****************************************************************************** *
 *      Developed By:  <PERSON>
 *                     NASA Langley Research Center
 *                     Phone:(757)864-5318
 *                     Email:<EMAIL>
 *
 *      Modifications: 
 *
 *
 *      Developed For: NASA Langley Research Center
 *
 *      Copyright:     Unauthorized use, dissemination or export of this
 *                     software is a violation of U.S. Government computer law.
 *
 ******************************************************************************/
/* Compile with `gcc -g -O2 -o rutro rutro.c -lm` */
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

typedef double Point[3];
typedef int    Elem[4];
typedef int    Tri[3];
typedef double Matrix[16];

typedef char myBool;
#define TRUE 1
#define FALSE 0

typedef struct _myMesh {
  int   nNode,nElem,nDV;
  Point *node;
  Elem  *elem;
  int   *id;
  Point *sd;
} MyMesh;


#define LINESIZE 100000
#define PARTY

#if ( ( (defined(__BYTE_ORDER) && (__BYTE_ORDER==__LITTLE_ENDIAN)) || \
        (defined(_BYTE_ORDER) && (_BYTE_ORDER==_LITTLE_ENDIAN)) ||    \
        (defined(BYTE_ORDER) && (BYTE_ORDER==LITTLE_ENDIAN)) ||       \
        (defined(WINNT)) ) &&                                         \
      !defined(NO_BYTE_SWAP) )
#define GEO_ENDIANSWAP(size_,x)            \
{                                          \
  register int  ii_,sz_=size_-1;           \
  register char *c_=(char *)(x);           \
  if( size_ > 1 )                          \
    for( ii_=0; ii_<size_/2; ++ii_ ) {     \
      *(c_+ii_)       ^= *(c_+(sz_-ii_));  \
      *(c_+(sz_-ii_)) ^= *(c_+ii_);        \
      *(c_+ii_)       ^= *(c_+(sz_-ii_));  \
    }                                      \
}
#else
#define GEO_ENDIANSWAP(size_,x) {}
#endif


// ----------------------------------------------------------------------------
static size_t private_fread( void *ptr, size_t size, size_t nmemb, FILE *stream)
{
  int    i;
  size_t ret;
  char   *tmp=(char *)ptr;

  ret = fread(ptr,size,nmemb,stream);

  for( i=0; i<nmemb; i++ ) {
    GEO_ENDIANSWAP(size,tmp);
    tmp += size;
  }

  return( ret );
}


// ----------------------------------------------------------------------------
static size_t private_fwrite( void *ptr, size_t size, size_t nmemb, FILE *stream)
{
  int    i;
  size_t ret;
  char   *tmp;

  for( i=0,tmp=(char *)ptr; i<nmemb; i++ ) {
    GEO_ENDIANSWAP(size,tmp);
    tmp += size;
  }

  ret = fwrite(ptr,size,nmemb,stream);

  for( i=0,tmp=(char *)ptr; i<nmemb; i++ ) {
    GEO_ENDIANSWAP(size,tmp);
    tmp += size;
  }

  return( ret );
}


// ----------------------------------------------------------------------------
static void private_DeleteMesh(MyMesh* mesh)
{
  if( mesh ) {
    if( mesh->sd   ) free(mesh->sd);
    if( mesh->id   ) free(mesh->id);
    if( mesh->elem ) free(mesh->elem);
    if( mesh->node ) free(mesh->node);
    free(mesh);
  }
}


// ----------------------------------------------------------------------------
static MyMesh* private_NewMesh(int nNode, int nElem, int nDV)
{
  MyMesh *mesh=NULL;

  if( (mesh=(MyMesh*)calloc(1,sizeof(MyMesh))) == NULL ) {
    fprintf(stderr,"Could not allocate Mesh\n");
    goto MyMesh_New_Error;
  }

  if( (mesh->node=(Point*)malloc(nNode*sizeof(Point))) == NULL ) {
    fprintf(stderr,"Could not allocate %d Mesh nodes\n",nNode);
    goto MyMesh_New_Error;
  }

  if( (mesh->elem=(Elem*)malloc(nElem*sizeof(Elem))) == NULL ) {
    fprintf(stderr,"Could not allocate %d Mesh elements\n",nElem);
    goto MyMesh_New_Error;
  }

  if( (mesh->id=(int*)malloc(nNode*sizeof(int))) == NULL ) {
    fprintf(stderr,"Could not allocate %d Mesh global Ids\n",nNode);
    goto MyMesh_New_Error;
  }

  if( nDV > 0 ) {
    if( (mesh->sd=(Point*)malloc(nNode*nDV*sizeof(Point))) == NULL ) {
      fprintf(stderr,"Could not allocate %d Sensitivity Derivatives\n",nNode*nDV);
      goto MyMesh_New_Error;
    }
  }

  mesh->nNode = nNode;
  mesh->nElem = nElem;
  mesh->nDV   = nDV;

  return mesh;

MyMesh_New_Error:
  if( mesh ) private_DeleteMesh(mesh);
  return NULL;
}


// ----------------------------------------------------------------------------
static MyMesh* private_ReadTecplot(char *filename)
{
  int      i,j;
  char     line[LINESIZE];
  int      ndv;
  int      n,ne,np;
  Point*   sd;
  FILE*    fp=NULL;
  MyMesh*  mesh=NULL;

  /* Open File */
  fprintf(stdout,"Reading a Tecplot file \"%s\"\n", filename);
  if( (fp=fopen(filename, "r")) == NULL ) {
    fprintf(stderr,"Could not open Tecplot file name \"%s\".\n", filename);
    goto ReadTecplot_Error;
  }

  /* Skip TITLE */
  fgets(line, LINESIZE, fp);
  fgets(line, LINESIZE, fp);

  for( ndv=0,i=0; i<strlen(line); i++ )
    if( line[i] == '"' ) ndv++;

  ndv = (int)(((double)ndv/2. - 4.) / 3.);
  fprintf(stdout,"%d Design Variables Found\n", ndv);

  fgets(line, LINESIZE, fp);

  for( n=0, i=0; i<strlen(line); i++ ) {
    if( line[i] == '=' ) {
        n++;
        if(n == 2) sscanf(&line[i+1], "%d", &np);
        if(n == 3) sscanf(&line[i+1], "%d", &ne);
    }
  }
  if( n < 3 ) {
    fprintf(stderr,"Could not read the Tecplot file \"%s\".\n", filename);
    goto ReadTecplot_Error;
  }
  fprintf(stdout,"%d Points, %d Elements\n", np, ne);

  /* Allocate Mesh */
  if( (mesh=private_NewMesh(np,ne,ndv)) == NULL ) {
    fprintf(stderr,"Could not allocate mesh storage.\n");
    goto ReadTecplot_Error;
  }

  /* Read Mesh Data */
  for( n=0,sd=mesh->sd; n<np; n++ ) {
#ifdef PARTY
    if( fscanf(fp, "%lg%lg%lg%d",
        &mesh->node[n][0], &mesh->node[n][1], &mesh->node[n][2],
        &mesh->id[n]) != 4 ) {
      fprintf(stderr,"Error reading Point %d data.\n",n+1);
      goto ReadTecplot_Error;
    }
#else
    if( fscanf(fp, "%lg%lg%lg",
        &mesh->node[n][0], &mesh->node[n][1], &mesh->node[n][2]) != 3 ) {
      fprintf(stderr,"Error reading Point %d data.\n",n+1);
      goto ReadTecplot_Error;
    }
    mesh->id[n] = n+1;
#endif

    for( i=0; i<ndv; i++ ) {
      if(fscanf(fp, "%lg%lg%lg", &(*sd)[0], &(*sd)[1], &(*sd)[2]) != 3) {
        fprintf(stderr,"Error reading Point %d, Design Variable %d.\n",n+1,i+1);
        goto ReadTecplot_Error;
      }
      sd++;
    }
  }

  /* Read Connectivity */
  for( n=0; n<ne; n++ ) {
#ifdef PARTY
    if (fscanf(fp, "%d%d%d%d", &mesh->elem[n][0], &mesh->elem[n][1],
                               &mesh->elem[n][2], &mesh->elem[n][3]) != 4 ) {
        fprintf(stderr,"Error reading Element %d data.\n",n+1);
        goto ReadTecplot_Error;
    }
#else
    if(fscanf(fp, "%d%d%d", &mesh->elem[n][0], &mesh->elem[n][1],
                            &mesh->elem[n][2]) != 3 ) {
      fprintf(stderr,"Error reading Element %d data.\n",n+1);
      goto ReadTecplot_Error;
    }
    mesh->elem[n][3] = mesh->elem[n][2];
#endif

    mesh->elem[n][0]--;
    mesh->elem[n][1]--;
    mesh->elem[n][2]--;
    mesh->elem[n][3]--;
  }
  fclose(fp);
  return mesh;

ReadTecplot_Error:
  if( mesh ) private_DeleteMesh(mesh);
  if( fp ) fclose(fp);
  return NULL;
}


// ----------------------------------------------------------------------------
static myBool private_WriteTecplot(char *filename, MyMesh *mesh)
{
  int      n;
  int      d;
  Point*   sd;
  FILE*    fp=NULL;

  /* Open File */
  fprintf(stdout,"Writing a Tecplot file \"%s\"\n", filename);
  if( (fp=fopen(filename, "w")) == NULL ) {
    fprintf(stderr,"Could not open Tecplot file name \"%s\".\n", filename);
    goto WriteTecplot_Error;
  }

  /* Write TITLE */
  fprintf(fp, "TITLE = \"rutro\"\nVARIABLES = \"X\" \"Y\" \"Z\" \"ID\" ");
  for( n=0; n<mesh->nDV; n++ ) 
    fprintf(fp, " \"XD%ld\" \"YD%ld\" \"ZD%ld\" ", 
	    ((long)n+1), ((long)n+1), ((long)n+1));
  fprintf(fp, "\n");
  fprintf(fp, "ZONE T=ALL, I= %ld, J= %ld F=FEPOINT\n",
	  ((long)mesh->nNode),((long)mesh->nElem));

  /* Write Mesh Data */
  for( n=0,sd=mesh->sd; n<mesh->nNode; n++ ) {
#ifdef PARTY
    fprintf(fp, "%21.15g %21.15g %21.15g %d",
            mesh->node[n][0], mesh->node[n][1], mesh->node[n][2],
            mesh->id[n]);
#else
    fprintf(fp, "%21.15g %21.15g %21.15g",
            mesh->node[n][0], mesh->node[n][1], mesh->node[n][2]);
#endif

    for( d=0; d<mesh->nDV; d++) {
      fprintf(fp, " %21.15g %21.15g %21.15g", (*sd)[0], (*sd)[1], (*sd)[2]);
      sd++;
    }
    fprintf(fp, "\n");
  }

  /* Write Connectivity */
  for( n=0; n<mesh->nElem; n++ ) {
    fprintf(fp, "%ld %ld %ld %ld\n", 
	    ((long)mesh->elem[n][0]+1), ((long)mesh->elem[n][1]+1),
	    ((long)mesh->elem[n][2]+1), ((long)mesh->elem[n][3]+1));
  }
  fclose(fp);
  return TRUE;

WriteTecplot_Error:
  if( fp ) fclose(fp);
  return FALSE;
}


// ----------------------------------------------------------------------------
static MyMesh* private_ReadBSD(char *filename)
{
  int      i,j;
  char     line[LINESIZE];
  int      n,ne,nd,np;
  Point*   sd;
  FILE*    fp=NULL;
  MyMesh*  mesh=NULL;

  /* Open File */
  fprintf(stdout,"Reading a BSD file \"%s\"\n", filename);
  if( (fp=fopen(filename, "rb")) == NULL ) {
    fprintf(stderr,"Could not open BSD file name \"%s\".\n", filename);
    goto ReadBSD_Error;
  }

  j  = private_fread(&np,sizeof(long),1,fp);
  j += private_fread(&ne,sizeof(long),1,fp);
  j += private_fread(&nd,sizeof(long),1,fp);
  if(j != 3) {
    fprintf(stderr,"Could not read the design BSD file %s\n", filename);
    goto ReadBSD_Error;
  }
  fprintf(stdout,"%d Points, %d DVs, %d Elements\n", np, nd, ne);

  /* Allocate Mesh */
  if( (mesh=private_NewMesh(np,ne,nd)) == NULL ) {
    fprintf(stderr,"Could not allocate mesh storage.\n");
    goto ReadBSD_Error;
  }

  /* Read Mesh Data */
  for( n=0; n<np; n++ ) {
#ifdef PARTY
    j  = private_fread(&(mesh->id[n]),sizeof(int),1,fp);
    j += private_fread(mesh->node[n],sizeof(Point),1,fp);
    if(j != 2) {
      fprintf(stderr,"Error reading Point %d data.\n",n+1);
      goto ReadBSD_Error;
    }
#else
    j  = private_fread(mesh->node[n],sizeof(Point),1,fp);
    if(j != 1) {
      fprintf(stderr,"Error reading Point %d data.\n",n+1);
      goto ReadBSD_Error;
    }
    mesh->id[n] = n+1;
#endif
  }

  for( n=0,sd=mesh->sd; n<np; n++ ) {
    j  = private_fread(&(mesh->id[n]),sizeof(int),1,fp);
    if(j != 1) {
      fprintf(stderr,"Error reading Point %d, Design Variable ID.\n",n+1);
      goto ReadBSD_Error;
    }
    j = private_fread(sd,sizeof(Point),nd,fp);
    if(j != nd) {
      fprintf(stderr,"Error reading Point %d, Design Variables.\n",n+1);
      goto ReadBSD_Error;
    }
    sd += nd;
  }

  /* Read Connectivity */
  for( n=0; n<ne; n++ ) {
#ifdef PARTY
    j = private_fread(mesh->elem[n],sizeof(Elem),1,fp);
    if(j != 1) {
        fprintf(stderr,"Error reading Element %d data.\n",n+1);
        goto ReadBSD_Error;
    }
#else
    j = private_fread(mesh->elem[n],sizeof(Tri),1,fp);
    if(j != 1) {
      fprintf(stderr,"Error reading Element %d data.\n",n+1);
      goto ReadBSD_Error;
    }
    mesh->elem[n][3] = mesh->elem[n][2];
#endif

    mesh->elem[n][0]--;
    mesh->elem[n][1]--;
    mesh->elem[n][2]--;
    mesh->elem[n][3]--;
  }
  fclose(fp);
  return mesh;

ReadBSD_Error:
  if( mesh ) private_DeleteMesh(mesh);
  if( fp ) fclose(fp);
  return NULL;
}


// ----------------------------------------------------------------------------
static myBool private_WriteBSD(char *filename, MyMesh *mesh)
{
  int      j;
  int      n;
  Point*   sd;
  long     el[4];
  long     lp,le,ld;
  FILE*    fp=NULL;

  /* Open File */
  fprintf(stdout,"Writing a BSD file \"%s\"\n", filename);
  if( (fp=fopen(filename, "wb")) == NULL ) {
    fprintf(stderr,"Could not open BSD file name \"%s\".\n", filename);
    goto WriteBSD_Error;
  }

  lp = (long)mesh->nNode;
  le = (long)mesh->nElem;
  ld = (long)mesh->nDV;

  j  = private_fwrite(&(lp),sizeof(long),1,fp);
  j += private_fwrite(&(le),sizeof(long),1,fp);
  j += private_fwrite(&(ld),sizeof(long),1,fp);
  if(j != 3) {
    fprintf(stderr,"Could not write the design BSD file %s\n", filename);
    goto WriteBSD_Error;
  }

  /* Write Mesh Data */
  for( n=0; n<mesh->nNode; n++ ) {
#ifdef PARTY
    j  = private_fwrite(&(mesh->id[n]),sizeof(int),1,fp);
    j += private_fwrite(mesh->node[n],sizeof(Point),1,fp);
    if(j != 2) {
      fprintf(stderr,"Error writing Point %d data.\n",n+1);
      goto WriteBSD_Error;
    }
#else
    j  = private_fwrite(mesh->node[n],sizeof(Point),1,fp);
    if(j != 1) {
      fprintf(stderr,"Error writing Point %d data.\n",n+1);
      goto WriteBSD_Error;
    }
#endif
  }

  for( n=0,sd=mesh->sd; n<mesh->nNode; n++ ) {
    j = private_fwrite(&(mesh->id[n]),sizeof(int),1,fp);
    if(j != 1) {
      fprintf(stderr,"Error writing Point %d, Design Variable ID\n",n+1);
      goto WriteBSD_Error;
    }
    j += private_fwrite(sd,sizeof(Point),mesh->nDV,fp);
    if(j != mesh->nDV) {
      fprintf(stderr,"Error writing Point %d, Design Variables\n",n+1);
      goto WriteBSD_Error;
    }
    sd += mesh->nDV;
  }

  /* Write Connectivity */
  for( n=0; n<mesh->nElem; n++ ) {
    el[0] = (long)mesh->elem[n][0] + 1;
    el[1] = (long)mesh->elem[n][1] + 1;
    el[2] = (long)mesh->elem[n][2] + 1;
    el[3] = (long)mesh->elem[n][3] + 1;
    j = private_fwrite(el,sizeof(long),4,fp);
    if(j != 4) {
        fprintf(stderr,"Error writing Element %d data.\n",n+1);
        goto WriteBSD_Error;
    }
  }
  fclose(fp);
  return TRUE;

WriteBSD_Error:
  if( fp ) fclose(fp);
  return FALSE;
}


// ----------------------------------------------------------------------------
static void private_MultiplyMatrix(Matrix amat, Matrix bmat, Matrix mat)
{
#define A(row,col)  amat[(row<<2)+col]
#define B(row,col)  bmat[(row<<2)+col]
#define M(row,col)   mat[(row<<2)+col]

  int i;

/* Note: mat[i][j] = amat[i][k] bmat[k][j] */

  for( i=0; i<4; i++ ) {
     double ai0=A(i,0), ai1=A(i,1), ai2=A(i,2), ai3=A(i,3);

     M(i,0) = ai0*B(0,0) + ai1*B(1,0) + ai2*B(2,0) + ai3*B(3,0);
     M(i,1) = ai0*B(0,1) + ai1*B(1,1) + ai2*B(2,1) + ai3*B(3,1);
     M(i,2) = ai0*B(0,2) + ai1*B(1,2) + ai2*B(2,2) + ai3*B(3,2);
     M(i,3) = ai0*B(0,3) + ai1*B(1,3) + ai2*B(2,3) + ai3*B(3,3);
  }

#undef M
#undef B
#undef A
}


// ----------------------------------------------------------------------------
static void private_Translate(Matrix mat, double dx, double dy, double dz)
{
  Matrix res;
  Matrix work={1.0,0.0,0.0,dx,
               0.0,1.0,0.0,dy,
               0.0,0.0,1.0,dz,
               0.0,0.0,0.0,1.0};

  private_MultiplyMatrix(mat,work,res);

  memcpy((void *)mat,(const void *)res,sizeof(Matrix));
}


// ----------------------------------------------------------------------------
static myBool private_Rotate(Matrix mat, double ax, double ay, double az, double angle)
{
  double u[3];
  double c,s;
  double xx,yy,zz,xy,yz,zx,xs,ys,zs;
  double ic;
  Matrix res;
  Matrix work;
  double arad=angle*acos(0.0)/90.0;    /* Radians */

/* Create dependent Matricies from OpenGL "Red Book" pg. 478 */

  double mag;
  mag = sqrt(ax*ax + ay*ay + az*az);

  if( mag == 0.0 ) {
    fprintf(stderr,"Invalid rotation axis.\n");
    return FALSE;
  }
  mag = 1.0/mag;

  u[0] = ax*mag;
  u[1] = ay*mag;
  u[2] = az*mag;

  s = sin( arad );
  c = cos( arad );

  xx = u[0]*u[0];
  yy = u[1]*u[1];
  zz = u[2]*u[2];
  xy = u[0]*u[1];
  yz = u[1]*u[2];
  zx = u[2]*u[0];
  xs = u[0]*s;
  ys = u[1]*s;
  zs = u[2]*s;
  ic = 1.0 - c;

  work[ 0] = (ic*xx) + c;
  work[ 1] = (ic*xy) - zs;
  work[ 2] = (ic*zx) + ys;
  work[ 3] = 0.0;

  work[ 4] = (ic*xy) + zs;
  work[ 5] = (ic*yy) + c;
  work[ 6] = (ic*yz) - xs;
  work[ 7] = 0.0;

  work[ 8] = (ic*zx) - ys;
  work[ 9] = (ic*yz) + xs;
  work[10] = (ic*zz) + c;
  work[11] = 0.0;

  work[12] = 0.0;
  work[13] = 0.0;
  work[14] = 0.0;
  work[15] = 1.0;

  private_MultiplyMatrix(mat,work,res);

  memcpy((void *)mat,(const void *)res,sizeof(Matrix));

  return TRUE;
}


// ----------------------------------------------------------------------------
static void private_Scale(Matrix mat, double sx, double sy, double sz)
{
  Matrix res;
  Matrix work={ sx,0.0,0.0,0.0,
               0.0, sy,0.0,0.0,
               0.0,0.0, sz,0.0,
               0.0,0.0,0.0,1.0};

  private_MultiplyMatrix(mat,work,res);

  memcpy((void *)mat,(const void *)res,sizeof(Matrix));
}


// ----------------------------------------------------------------------------
static void private_Print(FILE* fp, Matrix mat)
{
  fprintf(fp, "Matrix:\n");
  fprintf(fp, "|%21.15g %21.15g %21.15g %21.15g|\n", mat[ 0], mat[ 1], mat[ 2], mat[ 3]);
  fprintf(fp, "|%21.15g %21.15g %21.15g %21.15g|\n", mat[ 4], mat[ 5], mat[ 6], mat[ 7]);
  fprintf(fp, "|%21.15g %21.15g %21.15g %21.15g|\n", mat[ 8], mat[ 9], mat[10], mat[11]);
  fprintf(fp, "|%21.15g %21.15g %21.15g %21.15g|\n", mat[12], mat[13], mat[14], mat[15]);
}


// ----------------------------------------------------------------------------
static myBool private_LoadTransforms(char *filename, Matrix mat)
{
  char     buf[256];
  char     tok[256];
  int      cnt;
  double   dx,dy,dz;
  double   angle;
  FILE*    fp=NULL;

  /* Open File */
  fprintf(stdout,"Reading Transformations from \"%s\"\n", filename);
  if( (fp=fopen(filename, "r")) == NULL ) {
    fprintf(stderr,"Could not open Transformations file \"%s\".\n", filename);
    goto LoadTransforms_Error;
  }

  /* Initialize Matrix to Identity */
  mat[1] = mat[2] = mat[ 3] = mat[ 4] = mat[ 6] = mat[ 7] = 0.0;
  mat[8] = mat[9] = mat[11] = mat[12] = mat[13] = mat[14] = 0.0;
  mat[0] = mat[5] = mat[10] = mat[15] = 1.0;

  while( fgets(buf,256,fp) != NULL ) {
    if( buf[0] == '#' || strlen(buf) < 2 ) continue;

    cnt = sscanf(buf,"%s", tok);
    if (cnt != 1) {
      fprintf(stderr,"Bad Transformation file. %d\n",((int)strlen(buf)));
      goto LoadTransforms_Error;
    } else if (strcmp(tok,"TRANSLATE") == 0) {
      cnt = sscanf(buf,"%*s %lg %lg %lg",&dx,&dy,&dz);
      if (cnt != 3) {
        fprintf(stderr,"Bad TRANSLATE command.\n");
        goto LoadTransforms_Error;
      }
      private_Translate(mat,dx,dy,dz);
    } else if (strcmp(tok,"ROTATE") == 0) {
      cnt = sscanf(buf,"%*s %lg %lg %lg %lg",&dx,&dy,&dz,&angle);
      if (cnt != 4) {
        fprintf(stderr,"Bad ROTATE command.\n");
        goto LoadTransforms_Error;
      }
      private_Rotate(mat,dx,dy,dz,angle);
    } else if (strcmp(tok,"SCALE") == 0) {
      cnt = sscanf(buf,"%*s %lg %lg %lg",&dx,&dy,&dz);
      if (cnt != 3) {
        fprintf(stderr,"Bad SCALE command.\n");
        goto LoadTransforms_Error;
      }
      private_Scale(mat,dx,dy,dz);
    } else {
      fprintf(stderr,"Bad transformation command.\n");
      goto LoadTransforms_Error;
    }
    /* private_Print(stderr,mat); */
  }
  return TRUE;

LoadTransforms_Error:
  if( fp ) fclose(fp);
  return FALSE;
}


// ----------------------------------------------------------------------------
static myBool private_Inverse(Matrix mat, Matrix inv)
{
#define ABS(a)          ((a) >  0  ? (a) : -(a))
#define SWAP_ROWS(a, b) { double *_tmp = a; (a)=(b); (b)=_tmp; }
#define M(row,col)      mat[(row<<2)+col]
#define O(row,col)      inv[(row<<2)+col]

  double wtmp[4][8];
  double m0, m1, m2, m3, s;
  double *r0, *r1, *r2, *r3;

  r0 = wtmp[0], r1 = wtmp[1], r2 = wtmp[2], r3 = wtmp[3];

  r0[0] = M(0,0), r0[1] = M(0,1), r0[2] = M(0,2), r0[3] = M(0,3),
  r0[4] = 1.0, r0[5] = r0[6] = r0[7] = 0.0,

  r1[0] = M(1,0), r1[1] = M(1,1), r1[2] = M(1,2), r1[3] = M(1,3),
  r1[5] = 1.0, r1[4] = r1[6] = r1[7] = 0.0,

  r2[0] = M(2,0), r2[1] = M(2,1), r2[2] = M(2,2), r2[3] = M(2,3),
  r2[6] = 1.0, r2[4] = r2[5] = r2[7] = 0.0,

  r3[0] = M(3,0), r3[1] = M(3,1), r3[2] = M(3,2), r3[3] = M(3,3),
  r3[7] = 1.0, r3[4] = r3[5] = r3[6] = 0.0;

/* Choose pivot - or die */

  if( ABS(r3[0])>ABS(r2[0]) ) SWAP_ROWS(r3, r2);
  if( ABS(r2[0])>ABS(r1[0]) ) SWAP_ROWS(r2, r1);
  if( ABS(r1[0])>ABS(r0[0]) ) SWAP_ROWS(r1, r0);
  if( 0.0 == r0[0] ) return FALSE;              /* Singular */

/* Eliminate first variable     */

  m1 = r1[0]/r0[0]; m2 = r2[0]/r0[0]; m3 = r3[0]/r0[0];
  s = r0[1]; r1[1] -= m1*s; r2[1] -= m2*s; r3[1] -= m3*s;
  s = r0[2]; r1[2] -= m1*s; r2[2] -= m2*s; r3[2] -= m3*s;
  s = r0[3]; r1[3] -= m1*s; r2[3] -= m2*s; r3[3] -= m3*s;
  s = r0[4];
  if( s != 0.0 ) { r1[4] -= m1*s; r2[4] -= m2*s; r3[4] -= m3*s; }
  s = r0[5];
  if( s != 0.0 ) { r1[5] -= m1*s; r2[5] -= m2*s; r3[5] -= m3*s; }
  s = r0[6];
  if( s != 0.0 ) { r1[6] -= m1*s; r2[6] -= m2*s; r3[6] -= m3*s; }
  s = r0[7];
  if( s != 0.0 ) { r1[7] -= m1*s; r2[7] -= m2*s; r3[7] -= m3*s; }

/* Choose pivot - or die */

  if( ABS(r3[1]) > ABS(r2[1]) ) SWAP_ROWS(r3, r2);
  if( ABS(r2[1]) > ABS(r1[1]) ) SWAP_ROWS(r2, r1);
  if( 0.0 == r1[1] )  return FALSE;             /* Singular */

/* Eliminate second variable */

  m2 = r2[1]/r1[1]; m3 = r3[1]/r1[1];
  r2[2] -= m2*r1[2]; r3[2] -= m3*r1[2];
  r2[3] -= m2*r1[3]; r3[3] -= m3*r1[3];
  s = r1[4]; if( 0.0 != s ) { r2[4] -= m2*s; r3[4] -= m3*s; }
  s = r1[5]; if( 0.0 != s ) { r2[5] -= m2*s; r3[5] -= m3*s; }
  s = r1[6]; if( 0.0 != s ) { r2[6] -= m2*s; r3[6] -= m3*s; }
  s = r1[7]; if( 0.0 != s ) { r2[7] -= m2*s; r3[7] -= m3*s; }

/* Choose pivot - or die */

  if( ABS(r3[2]) > ABS(r2[2]) ) SWAP_ROWS(r3, r2);
  if( 0.0 == r2[2] )  return FALSE;             /* Singular */

/* Eliminate third variable */

  m3 = r3[2]/r2[2];
  r3[3] -= m3*r2[3], r3[4] -= m3*r2[4],
  r3[5] -= m3*r2[5], r3[6] -= m3*r2[6],
  r3[7] -= m3*r2[7];

/* Last check */

  if( 0.0 == r3[3] ) return FALSE;              /* Singular */

  s = 1.0/r3[3];                                /* Now back substitute row 3 */
  r3[4] *= s; r3[5] *= s; r3[6] *= s; r3[7] *= s;

  m2 = r2[3];                                   /* Now back substitute row 2 */
  s  = 1.0/r2[2];
  r2[4] = s*(r2[4] - r3[4]*m2), r2[5] = s*(r2[5] - r3[5]*m2),
  r2[6] = s*(r2[6] - r3[6]*m2), r2[7] = s*(r2[7] - r3[7]*m2);
  m1 = r1[3];
  r1[4] -= r3[4]*m1, r1[5] -= r3[5]*m1,
  r1[6] -= r3[6]*m1, r1[7] -= r3[7]*m1;
  m0 = r0[3];
  r0[4] -= r3[4]*m0, r0[5] -= r3[5]*m0,
  r0[6] -= r3[6]*m0, r0[7] -= r3[7]*m0;

  m1 = r1[2];                                   /* Now back substitute row 1 */
  s  = 1.0/r1[1];
  r1[4] = s*(r1[4] - r2[4]*m1), r1[5] = s*(r1[5] - r2[5]*m1),
  r1[6] = s*(r1[6] - r2[6]*m1), r1[7] = s*(r1[7] - r2[7]*m1);
  m0 = r0[2];
  r0[4] -= r2[4]*m0, r0[5] -= r2[5]*m0,
  r0[6] -= r2[6]*m0, r0[7] -= r2[7]*m0;

  m0 = r0[1];                                   /* Now back substitute row 0 */
  s  = 1.0/r0[0];
  r0[4] = s*(r0[4] - r1[4]*m0), r0[5] = s*(r0[5] - r1[5]*m0),
  r0[6] = s*(r0[6] - r1[6]*m0), r0[7] = s*(r0[7] - r1[7]*m0);

  O(0,0) = r0[4], O(0,1) = r0[5], O(0,2) = r0[6], O(0,3) = r0[7],
  O(1,0) = r1[4], O(1,1) = r1[5], O(1,2) = r1[6], O(1,3) = r1[7],
  O(2,0) = r2[4], O(2,1) = r2[5], O(2,2) = r2[6], O(2,3) = r2[7],
  O(3,0) = r3[4], O(3,1) = r3[5], O(3,2) = r3[6], O(3,3) = r3[7];

#undef O
#undef M
#undef SWAP_ROWS
#undef ABS

  return TRUE;
}


// ----------------------------------------------------------------------------
static void private_Apply(MyMesh *mesh, Matrix mat)
{
  int    n;
  int    d;
  Point* sd;
  Point  pt;

  for( n=0,sd=mesh->sd; n<mesh->nNode; n++ ) {

    pt[0] = mesh->node[n][0];
    pt[1] = mesh->node[n][1];
    pt[2] = mesh->node[n][2];

    mesh->node[n][0] = mat[0]*pt[0] + mat[1]*pt[1] + mat[ 2]*pt[2] + mat[ 3];
    mesh->node[n][1] = mat[4]*pt[0] + mat[5]*pt[1] + mat[ 6]*pt[2] + mat[ 7];
    mesh->node[n][2] = mat[8]*pt[0] + mat[9]*pt[1] + mat[10]*pt[2] + mat[11];

    for( d=0; d<mesh->nDV; d++) {
      pt[0] = (*sd)[0];
      pt[1] = (*sd)[1];
      pt[2] = (*sd)[2];

      (*sd)[0] = mat[0]*pt[0] + mat[1]*pt[1] + mat[ 2]*pt[2];
      (*sd)[1] = mat[4]*pt[0] + mat[5]*pt[1] + mat[ 6]*pt[2];
      (*sd)[2] = mat[8]*pt[0] + mat[9]*pt[1] + mat[10]*pt[2];

      sd++;
    }
  }
}


// ----------------------------------------------------------------------------
static myBool private_ApplyInverse(MyMesh *mesh, Matrix mat)
{
  Matrix inv;

  if( !private_Inverse(mat, inv) ) {
    fprintf(stderr,"Could not build inverse transformation.\n");
    return FALSE;
  }

  private_Apply(mesh, inv);

  return TRUE;
}


// ----------------------------------------------------------------------------
int main(int argc, char *argv[])
{
  int     i;
  MyMesh* mesh;
  Matrix  mat;
  char*   filename=NULL;
  char*   tfile=NULL;
  char*   ofile=NULL;
  myBool  inverse=FALSE;
  myBool  binary=FALSE;

  /* Process arguments */
  for( i=1; i<argc; i++ ) {
    if( strcmp(argv[i],"-i") == 0 ) {
      fprintf(stderr,"Inverse transformation requested\n");
      inverse = TRUE;
    } else if( strcmp(argv[i],"-b") == 0 ) {
      fprintf(stderr,"Binary file I/O requested\n");
      binary = TRUE;
    } else if( strcmp(argv[i],"-o") == 0 ) {
      ofile = argv[++i];
    } else if( strcmp(argv[i],"-t") == 0 ) {
      tfile = argv[++i];
    } else {
      filename = argv[i];
    }
  }
  if( !ofile )
    ofile = filename;

  /* Check arguments */
  if( argc < 2 || tfile == NULL || filename == NULL ) {
    fprintf(stderr,"\nPurpose: %s transforms mesh and sensitivity derivatives for FUN3D\n\n",argv[0]);
    fprintf(stderr,"Usage: %s inputfile -t transforms [-o filename] [-i] [-b]\n\n",argv[0]);
    fprintf(stderr,"       where -t transforms  Specify the transforms file\n");
    fprintf(stderr,"             -o filename  Specify the output file name\n");
    fprintf(stderr,"             -i Apply the inverse transformation\n\n");
    fprintf(stderr,"             -b Binary file I/O [default: ASCII Tecplot]\n\n");
    fprintf(stderr,"\nThe transforms file contains any combination of:\n");
    fprintf(stderr,"\tTRANSLATE dx dy dz\n");
    fprintf(stderr,"\tROTATE ax ay az angle\n");
    fprintf(stderr,"\tSCALE sx sy sz\n");
    fprintf(stderr,"\t# in 1st column => comment\n");
    exit(1);
  }

  fprintf(stderr,"Apply \"%s\" to file \"%s\"\n",tfile,filename);

  /* Load the mesh with optional SDs */
  if( binary ) {
    if( (mesh=private_ReadBSD(filename)) == NULL ) {
      fprintf(stderr,"Failed to read Mesh in \"%s\"\n",filename);
      exit(1);
    }
  } else {
    if( (mesh=private_ReadTecplot(filename)) == NULL ) {
      fprintf(stderr,"Failed to read Mesh in \"%s\"\n",filename);
      exit(1);
    }
  }

  if( !private_LoadTransforms(tfile, mat) ) {
    fprintf(stderr,"Failed to load transformations\n");
    exit(1);
  }

  if( inverse ) {
    if( !private_ApplyInverse(mesh, mat) ) {
      fprintf(stderr,"Failed to apply inverse transform to Mesh\n");
      exit(1);
    }
  } else {
    private_Apply(mesh, mat);
  }

  /* Write the mesh with optional SDs */
  if( binary ) {
    if( !private_WriteBSD(ofile, mesh) ) {
      fprintf(stderr,"Failed to write Mesh in \"%s\"\n",ofile);
      exit(1);
    }
  } else {
    if( !private_WriteTecplot(ofile, mesh) ) {
      fprintf(stderr,"Failed to write Mesh in \"%s\"\n",ofile);
      exit(1);
    }
  }

  fprintf(stderr,"Successful Completion...\n");
  exit(0);
}

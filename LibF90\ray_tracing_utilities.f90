module ray_tracing_utilities

use kinddefs,        only : dp

implicit none

private

public :: point
public :: ray_type
public :: generate_ray
public :: my_alloc_points
public :: sanitize_ray_for_hara

! The point type contains all the information describing a point on a ray.
type point

    real(dp), dimension(3) :: point_xyz
    real(dp)               :: point_dist
    integer                :: p2c_local
    integer                :: p2e_local

end type


! The full ray type contains both the ray geometry and flow field data.
type ray_type

    ! flow_data(flow property, point index in ray)
    real(dp), dimension(:,:), pointer   :: flow_data
    type(point), dimension(:), pointer  :: rg

end type

contains

!============================== GENERATE_RAY =================================80
!
! Given a grid, solution, starting point, and a direction, generate a ray.
! Build its geometry and interpolate flow field data onto it. (Don't use this
! for sampling. Use build_ray_geom for that.)
!
!=============================================================================80
  function generate_ray( grid, soln, p01, n, traceback) result(newray)

    use grid_types,         only : grid_type
    use solution_types,     only : soln_type

    type(grid_type),           intent(in) :: grid
    type(soln_type),           intent(in) :: soln
    real(dp),dimension(:),     intent(in) :: p01, n
    logical, optional,         intent(in) :: traceback

    type(ray_type)                        :: newray
    type(point), dimension(:), pointer    :: points

  continue

    points => build_ray_geom(grid, p01, n, traceback)

    newray%rg => sort_ray_points(points)

    call prune_duplicates(newray%rg)

    newray%flow_data => retrieve_flow_data(grid, soln, newray%rg)

    deallocate(points)

  end function generate_ray



!============================== BUILD_RAY_GEOM ===============================80
!
! Return the full ray geometry, starting at point p01, and propagating in
! direction n. The ray contains a set of points, one point for each cell it
! passes through. The point within each cell that is taken as ray data is
! the midpoint of the ray segment that lies within the cell. Also, cell id
! data is associated with each point, for later use in interpolating flow field
! data onto the ray.
!
!=============================================================================80
  function build_ray_geom( grid, p01, n, traceback ) result(rg)

    use grid_helper,        only : grid_cell_unique
    use grid_types,         only : grid_type
    use element_defs,       only : max_node_per_cell
    use sampling_funclib,   only : get_span, is_inside_cylinder,               &
                                   is_inside_triangle2,                        &
                                   is_inside_element
    use geometry_utils,     only : line_plane_point2

    !=========================================================================80
    ! VARIABLE NOTES:
    ! p01 is the first point on line, n is the oriented vector direction of line
    ! nhat is the normalized input n vector, p02 is a second point along line
    ! traceback is an optional flag: if true, trace backwards from p01 (along
    ! the negative n-hat direction) in addition to forwards.
    !=========================================================================80

    type(grid_type),        intent(in)    :: grid
    real(dp), dimension(:), intent(in)    :: p01, n
    logical, optional,      intent(in)    :: traceback

    type(point), dimension(:), pointer    :: rg

    real(dp), dimension(3)                :: nhat, p02, p, p1, p2, p3
    real(dp), dimension(3)                :: pf1, pf2

    real(dp), dimension(2,3)              :: span

    real(dp)                              :: radius

    integer                               :: cell
    integer                               :: element_set
    integer                               :: node
    integer                               :: node_per_cell
    integer                               :: local_node
    integer                               :: local_count
    integer                               :: n_pts_est

    logical                               :: loop, cell_hit, flag, back

    integer :: nn1, nn2, nn3, nn4
    integer :: face_per_cell
    integer :: face_index
    real(dp), dimension(3,max_node_per_cell) :: pn

    real(dp), parameter                   :: half = 0.5_dp
    real(dp), parameter                   :: eps = 10.0_dp**(-15)

  continue

    back = .false.
    if (present(traceback)) back = traceback

    n_pts_est = 100

    ! my_alloc_points and my_realloc_points are contained within this module,
    ! and are designed for use exclusively with the point-type.

    nullify( rg )
    call my_alloc_points( rg, n_pts_est )

    rg(:)%p2c_local = 0
    rg(:)%p2e_local = 0

    nhat = n / sqrt(dot_product(n,n))
    p02 = p01 + nhat
    local_count = 0


    !=========================================================================80
    ! Loop over all element types. For each element type, loop over all elements
    ! of that type. For each element, first check if the element can possibly
    ! have a point along the line. If it can, loop over all faces and check each
    ! one for intersection. Store those intersection points to pf1 and pf2, and
    ! then store the midpoint of pf1 and pf2 to the ray.
    !=========================================================================80
      element_type_count:  do element_set = 1, grid%nelem

        face_per_cell =  grid%elem(element_set)%face_per_cell
        node_per_cell =  grid%elem(element_set)%node_per_cell

        element_loop : do cell = 1, grid%elem(element_set)%ncell

          span = get_span ( grid, element_set, cell, node_per_cell )
          ! span(1,:) - minimum coordinates of element
          ! span(2,:) - maximum coordinates of element
          radius = 1.0_dp*sqrt( (span(1,1)-span(2,1))**2                       &
                              + (span(1,2)-span(2,2))**2                       &
                              + (span(1,3)-span(2,3))**2)
          p(:) = half*(span(1,:)+span(2,:))


          is_unique: if ( is_inside_cylinder(p,p01,p02,radius) .and.           &
              grid_cell_unique( grid,grid%elem(element_set)%c2n(:,cell) )      &
               ) then

            cell_hit = .false.
            pf1      = 0.0_dp
            pf2      = 0.0_dp

            ! Load element geometry in pn(:,:) (get points of nodes in order).
            do node = 1, node_per_cell

              local_node  = grid%elem(element_set)%c2n(node,cell)
              pn(:,node)  = (/grid%x(local_node), grid%y(local_node),          &
                              grid%z(local_node)/)
            enddo

            ! Does this cell contain the point of origin of the line, p01?
            flag = is_inside_element(node_per_cell, (/p01(1), pn(1,:)/),       &
                               (/p01(2), pn(2,:)/), (/p01(3), pn(3,:)/))

            ! Loop over faces and check for intersection.
            threed_faces: do face_index = 1, face_per_cell

              nn1 = grid%elem(element_set)%local_f2n(face_index,1)
              nn2 = grid%elem(element_set)%local_f2n(face_index,2)
              nn3 = grid%elem(element_set)%local_f2n(face_index,3)
              nn4 = grid%elem(element_set)%local_f2n(face_index,4)

              ! p1, p2, and p3 hold the face nodes you are examining.
              p1 = pn(:,nn1)
              p2 = pn(:,nn2)
              p3 = pn(:,nn3)

              loop = .true.

              check_for_intersect: do
                ! Where does the line intersect the face plane?
                p = line_plane_point2( p1, p2, p3, p01, nhat )

                !==================== ON_HIT: IF... ==========================80
                ! If p is inside the face, and either: this point is forward
                ! on the ray, the user opted to trace backwards, or this is the
                ! the cell containing p01 (there will be an intersection point
                ! behind p01 where dot_product(p-p01,nhat)<0, and we don't want
                ! to throw out the first cell), then save the intersection data
                ! so this cell can be added to the ray.
                !=============================================================80
                on_hit: if ( is_inside_triangle2(p,p1,p2,p3) .and.             &
                     (dot_product(p-p01,nhat)>0.0_dp .or. back .or. flag) ) then

                  ! If hit already, pf1 has been set, so set pf2 now.
                  if ( cell_hit ) then
                      ! The pf1/=p catch handles edge and corner intersections.
                      if (abs(dot_product(p-pf1,nhat)) >= eps) pf2 = p
                  else
                      pf1 = p
                      cell_hit = .true.
                  endif
                endif on_hit

                if (( loop .eqv. .false. ).OR.(nn4 == nn1))  exit

                ! If nn4/=nn1, face is a quad. Check second triangle.
                p2 = pn(:,nn3)
                p3 = pn(:,nn4)
                loop = .false.
              end do check_for_intersect

            end do threed_faces

            add_point_to_ray: if ( cell_hit ) then

              ! Resize rg if it's too small.
              local_count = local_count + 1
              if ( local_count > size(rg) ) then
                call my_realloc_points( rg , int(size(rg) * 1.25_dp) )
              endif

              ! Store the midpoint of pf1 and pf2 to the line.
              ! Check that pf2 has been set; if not, only use pf1.
              if (pf2(1)/=0.0_dp .or. pf2(2)/=0.0_dp .or. pf2(3)/=0.0_dp) then
                p = half * (pf1 + pf2)
              else
                p = pf1
              endif
              rg(local_count)%point_xyz(:)  = p
              rg(local_count)%point_dist    = dot_product(p-p01,nhat)
              rg(local_count)%p2c_local     = cell
              rg(local_count)%p2e_local     = element_set

            endif add_point_to_ray

          endif is_unique

        end do element_loop

      end do element_type_count

      ! Make sure that you send rg back with only as many points as it actually
      ! intersected!
      if (local_count < size(rg)) call my_realloc_points(rg,local_count)

  end function build_ray_geom


!============================== RETRIEVE_FLOW_DATA============================80
!
! Interpolate the contents of q_dof onto the ray via the sampling function
! element_stats. There is room for future modification, here... We're carrying
! the whole solution variable in case anyone ever needs anything else in it.
!
!=============================================================================80
  function retrieve_flow_data( grid, soln, rg ) result( fd )

    use allocations,        only : my_alloc_ptr
    use element_defs,       only : max_node_per_cell
    use grid_types,         only : grid_type
    use solution_types,     only : soln_type
    use geometry_utils,     only : element_stats

    !=========================================================================80
    ! VARIABLE NOTES:
    ! fd will contain the flow_data table. Row-->flow data, Col-->Cell ID.
    ! qp is a temporary variable that holds the flow data at all the nodes in
    ! a given element, and it gets sent to element_stats which returns the
    ! interpolated average which gets stored as flow data for the ray.
    !=========================================================================80

    type(grid_type),                    intent(in)  :: grid
    type(soln_type),                    intent(in)  :: soln
    type(point), dimension(:), pointer              :: rg

    real(dp), dimension(:,:), pointer               :: fd, qp
    real(dp), dimension(3,0:max_node_per_cell)      :: pn

    integer :: i, q_size, node, local_node, cell, node_per_cell, elem

  continue

    q_size = size(soln%q_dof(:,1))

    nullify(fd)
    if ( associated(fd) ) deallocate(fd)
    call my_alloc_ptr(fd,q_size,size(rg))

    nullify(qp)
    if ( associated(qp) ) deallocate(qp)
    call my_alloc_ptr(qp,q_size,max_node_per_cell)

    ! Loop over every element in rg and assign flow data to the corresponding
    ! location in fd.
    do i=1,size(rg)

      ! Load data from point in rg. (Position, cell, nodes in that element.)
      pn(:,0)       = rg(i)%point_xyz
      cell          = rg(i)%p2c_local
      elem          = rg(i)%p2e_local
      node_per_cell = grid%elem(elem)%node_per_cell

      ! Load element geometry in pn(:,:). Get points of nodes in order.
      do node = 1, node_per_cell

        local_node  = grid%elem(elem)%c2n(node,cell)
        pn(:,node)  = (/grid%x(local_node), grid%y(local_node),                &
                        grid%z(local_node)/)
        qp(:,node)  = soln%q_dof(:,local_node)
      enddo

      ! Interpolate with element_stats and store the average to the ray.
      fd(:,i) = element_stats(q_size, node_per_cell, pn(1,:), pn(2,:),         &
                              pn(3,:), qp)

    end do

    deallocate(qp)

  end function retrieve_flow_data


!============================== SORT_RAY_POINTS ==============================80
!
! Use a merge sort to get the array of ray points ordered by distance from p01.
!
!=============================================================================80
  recursive function sort_ray_points(points) result( points_out )

    type(point), dimension(:), pointer :: points

    type(point), dimension(:), pointer :: points_a, points_b, points_out

    integer :: i

  continue

    i = size(points)

    if (i<=1) then
      call my_alloc_points(points_out, i )
      points_out = points
      return
    endif

    points_a => points(1:i/2)
    points_b => points((i/2+1):i)

    points_a => sort_ray_points(points_a)
    points_b => sort_ray_points(points_b)

    points_out => merge_for_sort(points_a,points_b)

    deallocate(points_a)
    deallocate(points_b)

  end function sort_ray_points


!============================== MERGE_FOR_SORT================================80
!
! For use by the sort_ray_points merge sort routine.
!
!=============================================================================80
  function merge_for_sort(a,b) result(c)

    type(point), dimension(:), pointer              :: a, b, c

    integer :: i, j, k

  continue

    call my_alloc_points( c, size(a)+size(b) )

    i = 1
    j = 1
    k = 1

    ! Typical merge sort merging routine. [a] is the array on the left,
    ! [b] is the array on the right. Compare the i'th element on the left
    ! to the j'th element on the right. If the element on the right is
    ! closer to point p0, then put that one into the result array [c] and
    ! increment j. The rest should be straightforward.
    do while (i<=size(a) .and. j<=size(b))

      if ( a(i)%point_dist >= b(j)%point_dist ) then
        c(k) = b(j)
        j = j + 1
      else
        c(k) = a(i)
        i = i + 1
      end if
      k = k + 1

    end do

    ! Throw the rest of [a] or [b] into [c], depending on which one still
    ! contains elements which have not yet been added to [c].
    if (i<=size(a)) c(k:size(c)) = a(i:size(a))
    if (j<=size(b)) c(k:size(c)) = b(j:size(b))

  end function


!============================== PRUNE_DUPLICATES =============================80
!
! Check the ray for duplicate points (which are possible if nodes fall along the
! line of sight), and remove them.
!
! Note: the points given to this subroutine as an argument MUST BE SORTED BY
! DISTANCE, or this routine will not work!
!=============================================================================80
  subroutine prune_duplicates(points)

    type(point), dimension(:), pointer  :: points, temp

    integer                             :: i, j, status, cnt

  continue

    if (size(points) == 0) return

    cnt = 0
    do i = 2, size(points)
      if(points(i)%point_dist == points(i-1)%point_dist) cnt = cnt + 1
    end do

    call my_alloc_points(temp,size(points)-cnt)

    temp(1) = points(1)
    j = 2

    do i = 2, size(points)
      ! If point(i) is a duplicate, skip it.
      if(points(i)%point_dist == points(i-1)%point_dist) cycle
      temp(j) = points(i)
      j = j + 1
    end do

    deallocate(points,stat = status)
    if ( status /= 0) stop "Deallocating failed in prune_duplicates."

    points => temp

  end subroutine prune_duplicates


!============================== MY_ALLOC_POINTS ==============================80
!
! Allocate an array of point-type data.
!
!=============================================================================80
  subroutine my_alloc_points(points,dmn)

    type(point), dimension(:), pointer  :: points
    integer, intent(in)                 :: dmn

    integer                             :: status

  continue

    allocate(points(dmn),stat=status)
    if ( status /= 0) stop "Insufficient memory. (Error in my_alloc_points)"

    points(:)%point_xyz(1) = 0.0_dp
    points(:)%point_xyz(2) = 0.0_dp
    points(:)%point_xyz(3) = 0.0_dp
    points(:)%point_dist   = 0.0_dp
    points(:)%p2c_local    = 0
    points(:)%p2e_local    = 0

  end subroutine my_alloc_points


!============================== MY_REALLOC_POINTS ============================80
!
! If you have an array of point-type data and it's already been allocated but
! you want to change its size, you have come to the right place.
!
!=============================================================================80
  subroutine my_realloc_points(points,dmn)

    type(point), dimension(:), pointer  :: points, temp
    integer, intent(in)                 :: dmn

    integer                             :: status, old_dim, smaller

  continue

    old_dim = size(points)
    smaller = min0(old_dim,dmn)

    call my_alloc_points(temp,dmn)

    temp(1:smaller) = points(1:smaller)

    deallocate(points,stat = status)
    if ( status /= 0) stop "Deallocating failed in my_realloc_points."

    points => temp

  end subroutine my_realloc_points



!============================== SANITIZE_RAY_FOR_HARA ========================80
!
! HARA does not like rays where points have wall distances less than zero.
! When the radiation module initializes rays starting at the wall and traces
! them forward, the first point along all of these rays should always be located
! on the wall and have a point_dist of zero. But round-off error in ray tracing
! sometimes causes a negative wall distance to occur, and this routine corrects
! for that.
!
! NOTE: This routine removes all points behind p0.
!
! NOTE: Only use this routine for rays that should not have points located
! behind the point at which the ray was launched (p0). If you know a ray was
! traced with back-tracing enabled (points were deliberately traced behind p0),
! this is not the routine to send it to. This routine checks that the points
! behind p0 are only located a very small distance behind it (small compared
! to the order of magnitude of the spacing between "good" points, where good
! points are all points with point_dist greater than zero), and if there are
! points far behind p0, this routine throws an error message.
!
! The order of magnitude (delta) of point spacing for good points is,
! arbitrarily, an average of the spacing between all points with point_dist
! greater than 0.
!
! The "small distance" behind p0 that we check is arbitrarily taken to be
! delta, but this can be modified as necessary.
!
!=============================================================================80
  subroutine sanitize_ray_for_hara(line)

    use allocations,       only : my_alloc_ptr
    use system_extensions, only : se_flush

    type(ray_type), intent(inout) :: line

    integer   :: i, j, k
    real(dp)  :: delta

    real(dp), dimension(:,:), pointer   :: fd_temp
    type(point), dimension(:), pointer  :: rg_temp

    real(dp), parameter :: zero = 0.0_dp

  continue

    i = 1
    j = size(line%rg)

    do while (line%rg(i)%point_dist < zero)
      i = i + 1
    end do

    ! If i=1, then there are no points behind p0, and the ray is already fine.
    if (i==1) return

    delta = (line%rg(j)%point_dist - line%rg(i)%point_dist)/(j-i)

    if (abs(line%rg(1)%point_dist) > delta) then
      write(*,*) 'Warning: there are points on this ray behind p0 farther ',   &
                 'than delta away.'
      write(*,*) 'Suggested solutions: '
      write(*,*) '+ Check whether this ray was traced with back-tracing ',     &
                 'enabled.'
      write(*,*) '+ Check that this ray was launched from a wall surface node.'
      write(*,*) 'Delta for this ray was:   ', delta
      write(*,*) 'ray%rg(1)%point_dist was: ', line%rg(1)%point_dist
      write(*,*) 'Note: computations will continue anyway, and all points ',   &
                 'behind p0 for this ray will be removed.'
      call se_flush
    end if

    ! Now we set k to contain the new size of the ray.
    k = j - i + 1

    nullify(rg_temp)
    nullify(fd_temp)
    call my_alloc_points(rg_temp,k)
    call my_alloc_ptr(fd_temp,size(line%flow_data(:,1)),k)

    rg_temp(1:k)   = line%rg(i:j)
    fd_temp(:,1:k) = line%flow_data(:,i:j)

    deallocate(line%rg)
    deallocate(line%flow_data)

    line%rg        => rg_temp
    line%flow_data => fd_temp

  end subroutine sanitize_ray_for_hara

end module ray_tracing_utilities

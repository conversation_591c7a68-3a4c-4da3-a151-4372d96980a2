!================================= SA0_TURB_ABS ==============================80
!
! The variation of |turb| ( or turb * f_negative ) for S-A.
!
!=============================================================================80

  pure function sa0_turb_abs( turb, rnu )

    real(dp), intent(in) :: turb, rnu

    real(dp) :: sa0_turb_abs, chi

    real(dp), parameter :: cn1 = 16._dp

  continue

      sa0_turb_abs = turb

      if ( turb < 0._dp ) then

        chi = turb / rnu

        sa0_turb_abs = turb*( cn1 + chi**3 )/ ( cn1 - chi**3 )

      endif

  end function sa0_turb_abs

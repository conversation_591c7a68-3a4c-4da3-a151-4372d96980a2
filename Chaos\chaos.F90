program chaos

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

  use chaos_datas,        only : ntimesteps, free_data, timestep, time_data,   &
                                 time_data_back, nparts, overlap, adjoint_only,&
                                 gridfile
  use chaos_readers,      only : read_res_vol, read_dfdq, read_drdqt,          &
                                 read_info, read_drdd
  use chaos_brute_force,  only : linear_solve
  use chaos_construction, only : get_csr, get_overlapped_csr
  use lmpi,               only : lmpi_start, lmpi_id, lmpi_nproc, lmpi_die,    &
                                 lmpi_finalize, lmpi_master
  use string_utils,       only : char2int

  implicit none

  character(len=100) :: argument1, argument2

continue

! Start up MPI and determine how many time planes we are expecting based
! on the number of ranks

  call lmpi_start

  timestep = lmpi_id + 1

  ntimesteps = lmpi_nproc

#ifndef HAVE_FORTRAN_2003_ENVIRONMENT
  write(*,*) 'Compiler does not support command line parsing.'
  call lmpi_die
  stop
#else
  if ( command_argument_count() == 0 ) then
    write(*,*) 'Number of spatial partitions and project name must be provided'
    write(*,*) 'on command line.'
    call lmpi_die
    stop
  endif
  call get_command_argument(1,argument1)
  call get_command_argument(2,argument2)
#endif

  nparts   = char2int(trim(argument1))
  gridfile = '../Flow/' // trim(argument2) // '.b8.ugrid'

! Read basic data about the problem

  call read_info()

! Read all data for my own timeplane

  call read_res_vol(timestep, time_data)
  call read_dfdq(timestep, time_data)
  call read_drdqt(timestep, time_data)
  call read_drdd(timestep, time_data)

! Back plane: Need df/dQ and dR/dQtranspose

  if ( timestep > 1 ) then
    call read_dfdq(timestep-1, time_data_back)
    call read_drdqt(timestep-1, time_data_back)
  endif

! Set up CSR arrays

  if ( overlap ) then
    call get_overlapped_csr()
  else
    call get_csr()
  endif

  call linear_solve(1) ! adjoint mode

  if ( .not. adjoint_only ) call linear_solve(2) ! tangent mode

  call free_data(time_data)
  call free_data(time_data_back)

  if ( lmpi_master ) write(*,*) 'Done.'

  call lmpi_finalize()

end program chaos

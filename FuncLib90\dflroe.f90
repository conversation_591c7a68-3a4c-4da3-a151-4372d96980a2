!============================ DFLROE =========================================80
!
! Roe flux jacobians for the left state
!
! Note: ql, qr are conservative variables
!
! All derivatives worked out in detail
!
!=============================================================================80

  pure function dflroe(xnorm, ynorm, znorm, area, face_speed, ql, qr)

    use kinddefs,  only : dp
    use inviscid_flux,     only : lhs_a_eigenvalue_coef, lhs_u_eigenvalue_coef
    use fluid,     only : gm1

    real(dp), intent(in) :: xnorm, ynorm, znorm, area, face_speed

    real(dp), dimension(5), intent(in) :: ql, qr

    real(dp), dimension(5,5)           :: dflroe

    real(dp) :: fluxp1rl,fluxp1ul,fluxp1vl,fluxp1wl
    real(dp) :: fluxp2rl,fluxp2ul,fluxp2vl,fluxp2wl,fluxp2pl
    real(dp) :: fluxp3rl,fluxp3ul,fluxp3vl,fluxp3wl,fluxp3pl
    real(dp) :: fluxp4rl,fluxp4ul,fluxp4vl,fluxp4wl,fluxp4pl
    real(dp) :: fluxp5rl,fluxp5ul,fluxp5vl,fluxp5wl,fluxp5pl

    real(dp) :: t1rl,t1ul,t1vl,t1wl,t1pl
    real(dp) :: t2rl,t2ul,t2vl,t2wl,t2pl
    real(dp) :: t3rl,t3ul,t3vl,t3wl,t3pl
    real(dp) :: t4rl,t4ul,t4vl,t4wl,t4pl
    real(dp) :: t5rl,t5ul,t5vl,t5wl,t5pl

    real(dp) :: r54
    real(dp) :: r54rl,r54ul,r54vl,r54wl

    real(dp) :: r44
    real(dp) :: r44rl,r44wl

    real(dp) :: r34
    real(dp) :: r34rl,r34vl

    real(dp) :: r24
    real(dp) :: r24rl,r24ul

    real(dp) :: r53
    real(dp) :: r53rl,r53ul,r53vl,r53wl

    real(dp) :: r43
    real(dp) :: r43rl,r43ul,r43vl,r43wl

    real(dp) :: r33
    real(dp) :: r33rl,r33ul,r33vl,r33wl

    real(dp) :: r23
    real(dp) :: r23rl,r23ul,r23vl,r23wl

    real(dp) :: r52
    real(dp) :: r52rl,r52ul,r52vl,r52wl,r52pl

    real(dp) :: r42
    real(dp) :: r42rl,r42ul,r42vl,r42wl,r42pl

    real(dp) :: r32
    real(dp) :: r32rl,r32ul,r32vl,r32wl,r32pl

    real(dp) :: r22
    real(dp) :: r22rl,r22ul,r22vl,r22wl,r22pl

    real(dp) :: r51
    real(dp) :: r51rl,r51ul,r51vl,r51wl,r51pl

    real(dp) :: r41
    real(dp) :: r41rl,r41ul,r41vl,r41wl,r41pl

    real(dp) :: r31
    real(dp) :: r31rl,r31ul,r31vl,r31wl,r31pl

    real(dp) :: r21
    real(dp) :: r21rl,r21ul,r21vl,r21wl,r21pl

    real(dp) :: dv1
    real(dp) :: dv1rl,dv1ul,dv1vl,dv1wl,dv1pl

    real(dp) :: dv2
    real(dp) :: dv2rl,dv2ul,dv2vl,dv2wl,dv2pl

    real(dp) :: dv3
    real(dp) :: dv3rl

    real(dp) :: dv4
    real(dp) :: dv4rl,dv4ul,dv4vl,dv4wl,dv4pl

    real(dp) :: c2
    real(dp) :: c2rl,c2ul,c2vl,c2wl,c2pl

    real(dp) :: dubar
    real(dp) :: dubarrl,dubarul,dubarvl,dubarwl

    real(dp) :: du
    real(dp) :: durl,duul

    real(dp) :: dv
    real(dp) :: dvrl,dvvl

    real(dp) :: dw
    real(dp) :: dwrl,dwwl

    real(dp) :: dpress
    real(dp) :: dpressrl,dpressul,dpressvl,dpresswl,dpresspl

    real(dp) :: drho
    real(dp) :: drhorl

    real(dp) :: eig1, abseig1
    real(dp) :: eig1rl,eig1ul,eig1vl,eig1wl,eig1pl

    real(dp) :: eig2, abseig2
    real(dp) :: eig2rl,eig2ul,eig2vl,eig2wl,eig2pl

    real(dp) :: eig3, abseig3
    real(dp) :: eig3rl,eig3ul,eig3vl,eig3wl,eig3pl

    real(dp) :: maxeig
    real(dp) :: maxeigrl,maxeigul,maxeigvl,maxeigwl,maxeigpl

    real(dp) :: ubar
    real(dp) :: ubarrl,ubarul,ubarvl,ubarwl

    real(dp) :: c
    real(dp) :: crl,cul,cvl,cwl,cpl

    real(dp) :: q2
    real(dp) :: q2rl,q2ul,q2vl,q2wl

    real(dp) :: h
    real(dp) :: hrl,hul,hvl,hwl,hpl

    real(dp) :: u
    real(dp) :: url,uul

    real(dp) :: v
    real(dp) :: vrl,vvl

    real(dp) :: w
    real(dp) :: wrl,wwl

    real(dp) :: wat
    real(dp) :: watrl

    real(dp) :: rho
    real(dp) :: rhorl

    real(dp) :: ubarr, hr, enrgyr, pressr, q2r, ur, vr, wr, rhor

    real(dp) :: ubarl
    real(dp) :: ubarlrl,ubarlul,ubarlvl,ubarlwl

    real(dp) :: hl
    real(dp) :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp) :: enrgyl
    real(dp) :: enrgylpl

    real(dp) :: pressl
    real(dp) :: presslrl,presslul,presslvl,presslwl,presslpl

    real(dp) :: q2l
    real(dp) :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp) :: ul
    real(dp) :: ulrl,ulul

    real(dp) :: vl
    real(dp) :: vlrl,vlvl

    real(dp) :: wl
    real(dp) :: wlrl,wlwl

    real(dp) :: rhol
    real(dp) :: rholrl

    real(dp) :: eigeps1, eigeps2, eigeps3
    real(dp) :: d1, d2, d3

    real(dp) :: fa, faeps, absfa, dm, dterm

  continue

! Primitive variables on "left" side of face

      rhol = ql(1)
        rholrl = 1.0_dp
      ul = ql(2) / rhol
        ulrl = -ul/rhol
        ulul = 1.0_dp / rhol
      vl = ql(3) / rhol
        vlrl = -vl/rhol
        vlvl = 1.0_dp / rhol
      wl = ql(4) / rhol
        wlrl = -wl/rhol
        wlwl = 1.0_dp / rhol

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = ql(5)
        enrgylpl = 1.0_dp

      pressl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
        presslrl = -0.5_dp*gm1*(rhol*q2lrl + q2l*rholrl)
        presslul = -0.5_dp*gm1*rhol*q2lul
        presslvl = -0.5_dp*gm1*rhol*q2lvl
        presslwl = -0.5_dp*gm1*rhol*q2lwl
        presslpl = gm1

      Hl = (enrgyl + pressl)/rhol
        Hlrl = (rhol*(presslrl) - (enrgyl+pressl)*rholrl) / rhol / rhol
        Hlul = (rhol*(presslul)) / rhol / rhol
        Hlvl = (rhol*(presslvl)) / rhol / rhol
        Hlwl = (rhol*(presslwl)) / rhol / rhol
        Hlpl = (rhol*(enrgylpl+presslpl)) / rhol / rhol

      ubarl = xnorm*ul + ynorm*vl + znorm*wl - face_speed
        ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
        ubarlul = xnorm*ulul
        ubarlvl = ynorm*vlvl
        ubarlwl = znorm*wlwl

! Primitive variables on "right" side of face

      rhor = qr(1)

      ur = qr(2) / rhor

      vr = qr(3) / rhor

      wr = qr(4) / rhor

      q2r = ur*ur + vr*vr + wr*wr

      enrgyr = qr(5)

      pressr = gm1*(enrgyr - 0.5_dp*rhor*q2r)

      Hr = (enrgyr + pressr)/rhor

      ubarr  = xnorm*ur + ynorm*vr + znorm*wr - face_speed

! Compute Roe averages

      rho = sqrt(rhol*rhor)

      rhorl = 0.5_dp / rho * (rhor*rholrl)

      wat = rho/(rho + rhor)
        watrl = ((rho + rhor)*rhorl - rho*(rhorl)) / (rho+rhor) / (rho+rhor)

      u = ul*wat + ur*(1.0_dp - wat)
        url = ul*watrl + wat*ulrl + ur*(-watrl)
        uul = wat*ulul

      v = vl*wat + vr*(1.0_dp - wat)
        vrl = vl*watrl + wat*vlrl + vr*(-watrl)
        vvl = wat*vlvl

      w = wl*wat + wr*(1.0_dp - wat)
        wrl = wl*watrl + wat*wlrl + wr*(-watrl)
        wwl = wat*wlwl

      H = Hl*wat + Hr*(1.0_dp - wat)
        Hrl = Hl*watrl + wat*Hlrl + Hr*(-watrl)
        Hul = wat*Hlul
        Hvl = wat*Hlvl
        Hwl = wat*Hlwl
        Hpl = wat*Hlpl

      q2 = u*u + v*v + w*w
        q2rl = 2.0_dp*u*url + 2.0_dp*v*vrl + 2.0_dp*w*wrl
        q2ul = 2.0_dp*u*uul
        q2vl = 2.0_dp*v*vvl
        q2wl = 2.0_dp*w*wwl

      c = sqrt(gm1*(H - 0.5_dp*q2))
        crl = 0.5_dp / c * gm1*(Hrl - 0.5_dp*q2rl)
        cul = 0.5_dp / c * gm1*(Hul - 0.5_dp*q2ul)
        cvl = 0.5_dp / c * gm1*(Hvl - 0.5_dp*q2vl)
        cwl = 0.5_dp / c * gm1*(Hwl - 0.5_dp*q2wl)
        cpl = 0.5_dp / c * gm1*(Hpl)

      ubar = xnorm*u + ynorm*v + znorm*w - face_speed
        ubarrl = xnorm*url + ynorm*vrl + znorm*wrl
        ubarul = xnorm*uul
        ubarvl = ynorm*vvl
        ubarwl = znorm*wwl

! Eigenvalue limiting.  In terms of dimensional equations:
! -limit eigenvalues as fraction of local maximum

      fa    = ubar
      faeps = 0.05_dp*c
      absfa = abs( fa )
      if ( absfa < faeps ) absfa = 0.5_dp*(fa**2/faeps + faeps)

      maxeig = absfa + c

      if(abs(fa) < faeps ) then
        dm = fa/faeps
      elseif(fa > my_0) then
        dm = my_1
      else
        dm =-my_1
      endif

      dterm = my_1 + 0.5_dp*(my_1 - dm**2)*0.05_dp

        maxeigrl = dm*(ubarrl) + crl*dterm
        maxeigul = dm*(ubarul) + cul*dterm
        maxeigvl = dm*(ubarvl) + cvl*dterm
        maxeigwl = dm*(ubarwl) + cwl*dterm
        maxeigpl = cpl*dterm

! Now compute eigenvalues, eigenvectors, and strengths

      eig1 = ubar + c
      eig2 = ubar - c
      eig3 = ubar

! acoustic eigenvalue limiters

      eigeps1 = lhs_a_eigenvalue_coef*maxeig
      eigeps2 = lhs_a_eigenvalue_coef*maxeig

! convective eigenvalue limiter

      eigeps3 = lhs_u_eigenvalue_coef*maxeig

      abseig1 = abs( eig1 )
      abseig2 = abs( eig2 )
      abseig3 = abs( eig3 )

      if(abseig1 < eigeps1) abseig1 = 0.5_dp*(eig1**2/eigeps1 + eigeps1)
      if(abseig2 < eigeps2) abseig2 = 0.5_dp*(eig2**2/eigeps2 + eigeps2)
      if(abseig3 < eigeps3) abseig3 = 0.5_dp*(eig3**2/eigeps3 + eigeps3)

      if(abs(eig1) < eigeps1 ) then
        d1 = eig1/eigeps1
      elseif(eig1 > my_0) then
        d1 = my_1
      else
        d1 =-my_1
      endif

      dterm = 0.5_dp*(my_1 - d1**2)*lhs_a_eigenvalue_coef

        eig1rl = d1*(ubarrl + crl) + dterm*maxeigrl
        eig1ul = d1*(ubarul + cul) + dterm*maxeigul
        eig1vl = d1*(ubarvl + cvl) + dterm*maxeigvl
        eig1wl = d1*(ubarwl + cwl) + dterm*maxeigwl
        eig1pl = d1*(cpl) + dterm*maxeigpl

      if(abs(eig2) < eigeps2 ) then
        d2 = eig2/eigeps2
      elseif(eig2 > my_0) then
        d2 = my_1
      else
        d2 =-my_1
      endif

      dterm = 0.5_dp*(my_1 - d2**2)*lhs_a_eigenvalue_coef

        eig2rl = d2*(ubarrl - crl) + dterm*maxeigrl
        eig2ul = d2*(ubarul - cul) + dterm*maxeigul
        eig2vl = d2*(ubarvl - cvl) + dterm*maxeigvl
        eig2wl = d2*(ubarwl - cwl) + dterm*maxeigwl
        eig2pl = d2*(- cpl) + dterm*maxeigpl

      if(abs(eig3) < eigeps3 ) then
        d3 = eig3/eigeps3
      elseif(eig3 > my_0) then
        d3 = my_1
      else
        d3 =-my_1
      endif

      dterm = 0.5_dp*(my_1 - d3**2)*lhs_u_eigenvalue_coef

        eig3rl = d3*ubarrl + dterm*maxeigrl
        eig3ul = d3*ubarul + dterm*maxeigul
        eig3vl = d3*ubarvl + dterm*maxeigvl
        eig3wl = d3*ubarwl + dterm*maxeigwl
        eig3pl = dterm*maxeigpl

      drho = rhor - rhol
        drhorl = - rholrl

      dpress = pressr - pressl
        dpressrl = - presslrl
        dpressul = - presslul
        dpressvl = - presslvl
        dpresswl = - presslwl
        dpresspl = - presslpl

      du = ur - ul
        durl = - ulrl
        duul = - ulul

      dv = vr - vl
        dvrl = - vlrl
        dvvl = - vlvl

      dw = wr - wl
        dwrl = - wlrl
        dwwl = - wlwl

      dubar = ubarr - ubarl
        dubarrl = - ubarlrl
        dubarul = - ubarlul
        dubarvl = - ubarlvl
        dubarwl = - ubarlwl

      c2 = c*c
        c2rl = 2.0_dp * c * crl
        c2ul = 2.0_dp * c * cul
        c2vl = 2.0_dp * c * cvl
        c2wl = 2.0_dp * c * cwl
        c2pl = 2.0_dp * c * cpl

! jumps have units of density

      dv1 = 0.5_dp*(dpress + rho*c*dubar)/c2
        dv1rl = 0.5_dp*(c2*(dpressrl + rho*(c*dubarrl + dubar*crl)      &
                + c*dubar*rhorl) - (dpress + rho*c*dubar)*c2rl) / c2 / c2
        dv1ul = 0.5_dp*(c2*(dpressul + rho*(c*dubarul + dubar*cul))     &
                - (dpress + rho*c*dubar)*c2ul) / c2 / c2
        dv1vl = 0.5_dp*(c2*(dpressvl + rho*(c*dubarvl + dubar*cvl))     &
                - (dpress + rho*c*dubar)*c2vl) / c2 / c2
        dv1wl = 0.5_dp*(c2*(dpresswl + rho*(c*dubarwl + dubar*cwl))     &
                - (dpress + rho*c*dubar)*c2wl) / c2 / c2
        dv1pl = 0.5_dp*(c2*(dpresspl + rho*(dubar*cpl))                 &
                - (dpress + rho*c*dubar)*c2pl) / c2 / c2

      dv2 = 0.5_dp*(dpress - rho*c*dubar)/c2
        dv2rl = 0.5_dp*(c2*(dpressrl - rho*(c*dubarrl + dubar*crl)      &
                - c*dubar*rhorl) - (dpress - rho*c*dubar)*c2rl)         &
                / c2 / c2
        dv2ul = 0.5_dp*(c2*(dpressul - rho*(c*dubarul + dubar*cul))     &
                - (dpress - rho*c*dubar)*c2ul) / c2 / c2
        dv2vl = 0.5_dp*(c2*(dpressvl - rho*(c*dubarvl + dubar*cvl))     &
                - (dpress - rho*c*dubar)*c2vl) / c2 / c2
        dv2wl = 0.5_dp*(c2*(dpresswl - rho*(c*dubarwl + dubar*cwl))     &
                - (dpress - rho*c*dubar)*c2wl) / c2 / c2
        dv2pl = 0.5_dp*(c2*(dpresspl - rho*(dubar*cpl))                 &
                - (dpress - rho*c*dubar)*c2pl) / c2 / c2

      dv3 = rho
        dv3rl = rhorl

      dv4 = (c*c*drho - dpress)/c2
        dv4rl = (c2*((c*(c*drhorl+drho*crl)+c*drho*crl) - dpressrl)        &
                - (c*c*drho - dpress)*c2rl) / c2 / c2
        dv4ul = (c2*((c*(drho*cul)+c*drho*cul) - dpressul)                 &
                - (c*c*drho - dpress)*c2ul) / c2 / c2
        dv4vl = (c2*((c*(drho*cvl)+c*drho*cvl) - dpressvl)                 &
                - (c*c*drho - dpress)*c2vl) / c2 / c2
        dv4wl = (c2*((c*(drho*cwl)+c*drho*cwl) - dpresswl)                 &
                - (c*c*drho - dpress)*c2wl) / c2 / c2
        dv4pl = (c2*((c*(drho*cpl)+c*drho*cpl) - dpresspl)                 &
                - (c*c*drho - dpress)*c2pl) / c2 / c2

      r21 = u + c*xnorm
        r21rl = url + xnorm*crl
        r21ul = uul + xnorm*cul
        r21vl = xnorm*cvl
        r21wl = xnorm*cwl
        r21pl = xnorm*cpl

      r31 = v + c*ynorm
        r31rl = vrl + ynorm*crl
        r31ul = ynorm*cul
        r31vl = vvl + ynorm*cvl
        r31wl = ynorm*cwl
        r31pl = ynorm*cpl

      r41 = w + c*znorm
        r41rl = wrl + znorm*crl
        r41ul = znorm*cul
        r41vl = znorm*cvl
        r41wl = wwl + znorm*cwl
        r41pl = znorm*cpl

      r51 = H + c*(ubar+face_speed)
        r51rl = Hrl + c*ubarrl + (ubar+face_speed)*crl
        r51ul = Hul + c*ubarul + (ubar+face_speed)*cul
        r51vl = Hvl + c*ubarvl + (ubar+face_speed)*cvl
        r51wl = Hwl + c*ubarwl + (ubar+face_speed)*cwl
        r51pl = Hpl + (ubar+face_speed)*cpl

      r22 = u - c*xnorm
        r22rl = url - xnorm*crl
        r22ul = uul - xnorm*cul
        r22vl = - xnorm*cvl
        r22wl = - xnorm*cwl
        r22pl = - xnorm*cpl

      r32 = v - c*ynorm
        r32rl = vrl - ynorm*crl
        r32ul = - ynorm*cul
        r32vl = vvl - ynorm*cvl
        r32wl = - ynorm*cwl
        r32pl = - ynorm*cpl

      r42 = w - c*znorm
        r42rl = wrl - znorm*crl
        r42ul = - znorm*cul
        r42vl = - znorm*cvl
        r42wl = wwl - znorm*cwl
        r42pl = - znorm*cpl

      r52 = H - c*(ubar+face_speed)
        r52rl = Hrl - c*ubarrl - (ubar+face_speed)*crl
        r52ul = Hul - c*ubarul - (ubar+face_speed)*cul
        r52vl = Hvl - c*ubarvl - (ubar+face_speed)*cvl
        r52wl = Hwl - c*ubarwl - (ubar+face_speed)*cwl
        r52pl = Hpl - (ubar+face_speed)*cpl

      r23 = du - dubar*xnorm
        r23rl = durl - xnorm*dubarrl
        r23ul = duul - xnorm*dubarul
        r23vl = - xnorm*dubarvl
        r23wl = - xnorm*dubarwl

      r33 = dv - dubar*ynorm
        r33rl = dvrl - ynorm*dubarrl
        r33ul = - ynorm*dubarul
        r33vl = dvvl - ynorm*dubarvl
        r33wl = - ynorm*dubarwl

      r43 = dw - dubar*znorm
        r43rl = dwrl - znorm*dubarrl
        r43ul = - znorm*dubarul
        r43vl = - znorm*dubarvl
        r43wl = dwwl - znorm*dubarwl

      r53 = u*du + v*dv + w*dw - (ubar+face_speed)*dubar
        r53rl = u*durl+du*url + v*dvrl+dv*vrl + w*dwrl+dw*wrl              &
                - (ubar+face_speed)*dubarrl - dubar*ubarrl
        r53ul = u*duul+du*uul - (ubar+face_speed)*dubarul - dubar*ubarul
        r53vl = v*dvvl+dv*vvl - (ubar+face_speed)*dubarvl - dubar*ubarvl
        r53wl = w*dwwl+dw*wwl - (ubar+face_speed)*dubarwl - dubar*ubarwl

      r24 = u
        r24rl = url
        r24ul = uul

      r34 = v
        r34rl = vrl
        r34vl = vvl

      r44 = w
        r44rl = wrl
        r44wl = wwl

      r54 = 0.5_dp*q2
        r54rl = 0.5_dp*q2rl
        r54ul = 0.5_dp*q2ul
        r54vl = 0.5_dp*q2vl
        r54wl = 0.5_dp*q2wl

!           t1 = abseig1*dv1     + abseig2*dv2                                 &
!                                + abseig3*dv4

        t1rl = abseig1*dv1rl+dv1*eig1rl + abseig2*dv2rl+dv2*eig2rl         &
             + abseig3*dv4rl+dv4*eig3rl
        t1ul = abseig1*dv1ul+dv1*eig1ul + abseig2*dv2ul+dv2*eig2ul         &
             + abseig3*dv4ul+dv4*eig3ul
        t1vl = abseig1*dv1vl+dv1*eig1vl + abseig2*dv2vl+dv2*eig2vl         &
             + abseig3*dv4vl+dv4*eig3vl
        t1wl = abseig1*dv1wl+dv1*eig1wl + abseig2*dv2wl+dv2*eig2wl         &
             + abseig3*dv4wl+dv4*eig3wl
        t1pl = abseig1*dv1pl+dv1*eig1pl + abseig2*dv2pl+dv2*eig2pl         &
             + abseig3*dv4pl+dv4*eig3pl

!           t2 = abseig1*r21*dv1 + abseig2*r22*dv2                             &
!              + abseig3*r23*dv3 + abseig3*r24*dv4

        t2rl = abseig1*(r21*dv1rl+dv1*r21rl)+r21*dv1*eig1rl                &
             + abseig2*(r22*dv2rl+dv2*r22rl)+r22*dv2*eig2rl                &
             + abseig3*(r23*dv3rl+dv3*r23rl)+r23*dv3*eig3rl                &
             + abseig3*(r24*dv4rl+dv4*r24rl)+r24*dv4*eig3rl

        t2ul = abseig1*(r21*dv1ul+dv1*r21ul)+r21*dv1*eig1ul                &
             + abseig2*(r22*dv2ul+dv2*r22ul)+r22*dv2*eig2ul                &
             + abseig3*(dv3*r23ul)+r23*dv3*eig3ul                          &
             + abseig3*(r24*dv4ul+dv4*r24ul)+r24*dv4*eig3ul

        t2vl = abseig1*(r21*dv1vl+dv1*r21vl)+r21*dv1*eig1vl                &
             + abseig2*(r22*dv2vl+dv2*r22vl)+r22*dv2*eig2vl                &
             + abseig3*(dv3*r23vl)+r23*dv3*eig3vl                          &
             + abseig3*(r24*dv4vl)+r24*dv4*eig3vl

        t2wl = abseig1*(r21*dv1wl+dv1*r21wl)+r21*dv1*eig1wl                &
             + abseig2*(r22*dv2wl+dv2*r22wl)+r22*dv2*eig2wl                &
             + abseig3*(dv3*r23wl)+r23*dv3*eig3wl                          &
             + abseig3*(r24*dv4wl)+r24*dv4*eig3wl

        t2pl = abseig1*(r21*dv1pl+dv1*r21pl)+r21*dv1*eig1pl                &
             + abseig2*(r22*dv2pl+dv2*r22pl)+r22*dv2*eig2pl                &
             +r23*dv3*eig3pl + abseig3*(r24*dv4pl)+r24*dv4*eig3pl

!           t3 = abseig1*r31*dv1 + abseig2*r32*dv2                             &
!              + abseig3*r33*dv3 + abseig3*r34*dv4

        t3rl = abseig1*(r31*dv1rl+dv1*r31rl)+r31*dv1*eig1rl                &
             + abseig2*(r32*dv2rl+dv2*r32rl)+r32*dv2*eig2rl                &
             + abseig3*(r33*dv3rl+dv3*r33rl)+r33*dv3*eig3rl                &
             + abseig3*(r34*dv4rl+dv4*r34rl)+r34*dv4*eig3rl

        t3ul = abseig1*(r31*dv1ul+dv1*r31ul)+r31*dv1*eig1ul                &
             + abseig2*(r32*dv2ul+dv2*r32ul)+r32*dv2*eig2ul                &
             + abseig3*(dv3*r33ul)+r33*dv3*eig3ul                          &
             + abseig3*(r34*dv4ul)+r34*dv4*eig3ul

        t3vl = abseig1*(r31*dv1vl+dv1*r31vl)+r31*dv1*eig1vl                &
             + abseig2*(r32*dv2vl+dv2*r32vl)+r32*dv2*eig2vl                &
             + abseig3*(dv3*r33vl)+r33*dv3*eig3vl                          &
             + abseig3*(r34*dv4vl+dv4*r34vl)+r34*dv4*eig3vl

        t3wl = abseig1*(r31*dv1wl+dv1*r31wl)+r31*dv1*eig1wl                &
             + abseig2*(r32*dv2wl+dv2*r32wl)+r32*dv2*eig2wl                &
             + abseig3*(dv3*r33wl)+r33*dv3*eig3wl                          &
             + abseig3*(r34*dv4wl)+r34*dv4*eig3wl

        t3pl = abseig1*(r31*dv1pl+dv1*r31pl)+r31*dv1*eig1pl                &
             + abseig2*(r32*dv2pl+dv2*r32pl)+r32*dv2*eig2pl                &
             +r33*dv3*eig3pl + abseig3*(r34*dv4pl)+r34*dv4*eig3pl

!           t4 = abseig1*r41*dv1 + abseig2*r42*dv2                             &
!              + abseig3*r43*dv3 + abseig3*r44*dv4

        t4rl = abseig1*(r41*dv1rl+dv1*r41rl)+r41*dv1*eig1rl                &
             + abseig2*(r42*dv2rl+dv2*r42rl)+r42*dv2*eig2rl                &
             + abseig3*(r43*dv3rl+dv3*r43rl)+r43*dv3*eig3rl                &
             + abseig3*(r44*dv4rl+dv4*r44rl)+r44*dv4*eig3rl

        t4ul = abseig1*(r41*dv1ul+dv1*r41ul)+r41*dv1*eig1ul                &
             + abseig2*(r42*dv2ul+dv2*r42ul)+r42*dv2*eig2ul                &
             + abseig3*(dv3*r43ul)+r43*dv3*eig3ul                          &
             + abseig3*(r44*dv4ul)+r44*dv4*eig3ul

        t4vl = abseig1*(r41*dv1vl+dv1*r41vl)+r41*dv1*eig1vl                &
             + abseig2*(r42*dv2vl+dv2*r42vl)+r42*dv2*eig2vl                &
             + abseig3*(dv3*r43vl)+r43*dv3*eig3vl                          &
             + abseig3*(r44*dv4vl)+r44*dv4*eig3vl

        t4wl = abseig1*(r41*dv1wl+dv1*r41wl)+r41*dv1*eig1wl                &
             + abseig2*(r42*dv2wl+dv2*r42wl)+r42*dv2*eig2wl                &
             + abseig3*(dv3*r43wl)+r43*dv3*eig3wl                          &
             + abseig3*(r44*dv4wl+dv4*r44wl)+r44*dv4*eig3wl

        t4pl = abseig1*(r41*dv1pl+dv1*r41pl)+r41*dv1*eig1pl                &
             + abseig2*(r42*dv2pl+dv2*r42pl)+r42*dv2*eig2pl                &
             +r43*dv3*eig3pl + abseig3*(r44*dv4pl)+r44*dv4*eig3pl

        t5rl = abseig1*(r51*dv1rl+dv1*r51rl)+r51*dv1*eig1rl                &
             + abseig2*(r52*dv2rl+dv2*r52rl)+r52*dv2*eig2rl                &
             + abseig3*(r53*dv3rl+dv3*r53rl)+r53*dv3*eig3rl                &
             + abseig3*(r54*dv4rl+dv4*r54rl)+r54*dv4*eig3rl

        t5ul = abseig1*(r51*dv1ul+dv1*r51ul)+r51*dv1*eig1ul                &
             + abseig2*(r52*dv2ul+dv2*r52ul)+r52*dv2*eig2ul                &
             + abseig3*(dv3*r53ul)+r53*dv3*eig3ul                          &
             + abseig3*(r54*dv4ul+dv4*r54ul)+r54*dv4*eig3ul

        t5vl = abseig1*(r51*dv1vl+dv1*r51vl)+r51*dv1*eig1vl                &
             + abseig2*(r52*dv2vl+dv2*r52vl)+r52*dv2*eig2vl                &
             + abseig3*(dv3*r53vl)+r53*dv3*eig3vl                          &
             + abseig3*(r54*dv4vl+dv4*r54vl)+r54*dv4*eig3vl

        t5wl = abseig1*(r51*dv1wl+dv1*r51wl)+r51*dv1*eig1wl                &
             + abseig2*(r52*dv2wl+dv2*r52wl)+r52*dv2*eig2wl                &
             + abseig3*(dv3*r53wl)+r53*dv3*eig3wl                          &
             + abseig3*(r54*dv4wl+dv4*r54wl)+r54*dv4*eig3wl

        t5pl = abseig1*(r51*dv1pl+dv1*r51pl)+r51*dv1*eig1pl                &
             + abseig2*(r52*dv2pl+dv2*r52pl)+r52*dv2*eig2pl                &
             +r53*dv3*eig3pl + abseig3*(r54*dv4pl)+r54*dv4*eig3pl

! Compute flux using variables from left side of face

!           fluxp1 = area*rhol*ubarl

        fluxp1rl = area*(rhol*ubarlrl + ubarl*rholrl)
        fluxp1ul = area*(rhol*ubarlul)
        fluxp1vl = area*(rhol*ubarlvl)
        fluxp1wl = area*(rhol*ubarlwl)

!           fluxp2 = area*(rhol*ul*ubarl + xnorm*pressl)

        fluxp2rl = area*(rhol*(ul*ubarlrl+ubarl*ulrl) +                    &
                   ul*ubarl*rholrl + xnorm*presslrl)
        fluxp2ul = area*(rhol*(ul*ubarlul+ubarl*ulul) + xnorm*presslul)
        fluxp2vl = area*(rhol*(ul*ubarlvl) + xnorm*presslvl)
        fluxp2wl = area*(rhol*(ul*ubarlwl) + xnorm*presslwl)
        fluxp2pl = area*(xnorm*presslpl)

!           fluxp3 = area*(rhol*vl*ubarl + ynorm*pressl)

        fluxp3rl = area*(rhol*(vl*ubarlrl+ubarl*vlrl) +                    &
                   vl*ubarl*rholrl + ynorm*presslrl)
        fluxp3ul = area*(rhol*(vl*ubarlul) + ynorm*presslul)
        fluxp3vl = area*(rhol*(vl*ubarlvl+ubarl*vlvl) + ynorm*presslvl)
        fluxp3wl = area*(rhol*(vl*ubarlwl) + ynorm*presslwl)
        fluxp3pl = area*(ynorm*presslpl)

!           fluxp4 = area*(rhol*wl*ubarl + znorm*pressl)

        fluxp4rl = area*(rhol*(wl*ubarlrl+ubarl*wlrl) +                    &
                   wl*ubarl*rholrl + znorm*presslrl)
        fluxp4ul = area*(rhol*(wl*ubarlul) + znorm*presslul)
        fluxp4vl = area*(rhol*(wl*ubarlvl) + znorm*presslvl)
        fluxp4wl = area*(rhol*(wl*ubarlwl+ubarl*wlwl) + znorm*presslwl)
        fluxp4pl = area*(znorm*presslpl)

!           fluxp5 = area*(enrgyl + pressl)*ubarl + area*face_speed*pressl

        fluxp5rl = area*((enrgyl + pressl)*ubarlrl +                       &
                   ubarl*(presslrl)) + area*face_speed*presslrl
        fluxp5ul = area*((enrgyl + pressl)*ubarlul +                       &
                   ubarl*(presslul)) + area*face_speed*presslul
        fluxp5vl = area*((enrgyl + pressl)*ubarlvl +                       &
                   ubarl*(presslvl)) + area*face_speed*presslvl
        fluxp5wl = area*((enrgyl + pressl)*ubarlwl +                       &
                   ubarl*(presslwl)) + area*face_speed*presslwl
        fluxp5pl = area*(ubarl*(enrgylpl+presslpl))+area*face_speed*presslpl

!         flux1 = 0.5_dp*(fluxp1 + fluxm1 - area*t1)
!         flux2 = 0.5_dp*(fluxp2 + fluxm2 - area*t2)
!         flux3 = 0.5_dp*(fluxp3 + fluxm3 - area*t3)
!         flux4 = 0.5_dp*(fluxp4 + fluxm4 - area*t4)
!         flux5 = 0.5_dp*(fluxp5 + fluxm5 - area*t5)

      dflroe(1,1) = 0.5_dp*(fluxp1rl - area*t1rl)
      dflroe(1,2) = 0.5_dp*(fluxp1ul - area*t1ul)
      dflroe(1,3) = 0.5_dp*(fluxp1vl - area*t1vl)
      dflroe(1,4) = 0.5_dp*(fluxp1wl - area*t1wl)
      dflroe(1,5) = 0.5_dp*(- area*t1pl)

      dflroe(2,1) = 0.5_dp*(fluxp2rl - area*t2rl)
      dflroe(2,2) = 0.5_dp*(fluxp2ul - area*t2ul)
      dflroe(2,3) = 0.5_dp*(fluxp2vl - area*t2vl)
      dflroe(2,4) = 0.5_dp*(fluxp2wl - area*t2wl)
      dflroe(2,5) = 0.5_dp*(fluxp2pl - area*t2pl)

      dflroe(3,1) = 0.5_dp*(fluxp3rl - area*t3rl)
      dflroe(3,2) = 0.5_dp*(fluxp3ul - area*t3ul)
      dflroe(3,3) = 0.5_dp*(fluxp3vl - area*t3vl)
      dflroe(3,4) = 0.5_dp*(fluxp3wl - area*t3wl)
      dflroe(3,5) = 0.5_dp*(fluxp3pl - area*t3pl)

      dflroe(4,1) = 0.5_dp*(fluxp4rl - area*t4rl)
      dflroe(4,2) = 0.5_dp*(fluxp4ul - area*t4ul)
      dflroe(4,3) = 0.5_dp*(fluxp4vl - area*t4vl)
      dflroe(4,4) = 0.5_dp*(fluxp4wl - area*t4wl)
      dflroe(4,5) = 0.5_dp*(fluxp4pl - area*t4pl)

      dflroe(5,1) = 0.5_dp*(fluxp5rl - area*t5rl)
      dflroe(5,2) = 0.5_dp*(fluxp5ul - area*t5ul)
      dflroe(5,3) = 0.5_dp*(fluxp5vl - area*t5vl)
      dflroe(5,4) = 0.5_dp*(fluxp5wl - area*t5wl)
      dflroe(5,5) = 0.5_dp*(fluxp5pl - area*t5pl)

  end function dflroe

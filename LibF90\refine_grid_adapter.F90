module refine_grid_adapter

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

#ifdef HAVE_REFINE
  use kinddefs, only : dp
#endif

  implicit none

  private

  public :: refine_init_grid
  public :: refine_init_soln

  public :: refine_subdivide
  public :: refine_two_adapt

  public :: refine_grid_soln

  public :: refineInit
  public :: refineLoadCapriGeom
  public :: refineSaveCapriGeom
  public :: refineProjectAllFaces
  public :: refineTestCADParameters
  public :: refineTecplotSurface
  public :: refineParallelAdapt
  public :: refineParallelPreProject
  public :: refineParallelSwapInColor
  public :: refineParallelSmoothInColor
  public :: refineParallelRelaxNeg
  public :: refineParallelRelaxSurf
  public :: refineParallelProjectGridMove
  public :: refineParallelProjectOnly
  public :: refineReportAR
  public :: refineGetGrid
  public :: refineFree
  public :: refineFreezeBL
  public :: refineFreezeMixed
  public :: refineFreezeFile
  public :: refineFreezeRadius
  public :: refineLoadBalance

#ifdef HAVE_REFINE
  integer :: module_header_nbound
  integer, dimension(:), allocatable ::  module_header_ibc
  character(len=120), dimension(:), allocatable :: module_header_bc_family

  integer ::  module_header_nelem
  character(len=3), dimension(:), allocatable ::  module_header_type_cell

  real(dp) :: timeZero = 0.0_dp
#endif

contains

#ifdef HAVE_REFINE
  subroutine refineResetTimer()
    use system_extensions, only : se_wall_clock
    continue
    timeZero = se_wall_clock()
  end subroutine refineResetTimer

  subroutine refineReportTimer( string )
    use system_extensions, only : se_wall_clock, se_flush
    use lmpi,              only : lmpi_master
    character(len=*), intent(in) :: string
    real(dp) :: timeNow
    real :: delta
    continue
    timeNow = se_wall_clock()
    delta = timeNow-timeZero
    if (lmpi_master) then
      write(*,'(a,f15.8,a)') string, delta, ' sec. (wallclock)'
      call se_flush()
    end if
  end subroutine refineReportTimer
#endif

!=========================== refineFreezeBL ==================================80
!=============================================================================80

  subroutine refineFreezeBL( grid, bl_thick )

    use grid_types,          only : grid_type
    use kinddefs,            only : dp
#ifdef HAVE_REFINE
    use bc_names,            only : bc_used_for_distance_function
    use load_balance,        only : update_ghosts
    use lmpi,                only : lmpi_reduce, lmpi_master
    use nml_overset_data,    only : overset_flag
#endif

    type(grid_type), intent(inout) :: grid
    real(dp),        intent(in)    :: bl_thick

#ifdef HAVE_REFINE
    integer :: ibound, node, boundarynode
    integer, dimension(grid%nnodes01) :: state
    integer :: my_frozen_count, total_frozen_count
    interface
      subroutine gridfreezenode( node )
        integer, intent(in) :: node
      end subroutine gridfreezenode
    end interface

    continue

    state = 0
    do node = 1, grid%nnodes0
      if (real(grid%slen(node),dp) <= real(bl_thick,dp)) state(node) = 1
      if (overset_flag) then
        ! Unfreeze all holes and fringes - workaround
        if (grid%iblank(node) <= 0) state(node) = 0
      end if
    end do
    do ibound = 1, grid%nbound
      viscous_bc : if (bc_used_for_distance_function(grid%bc(ibound)%ibc)) then
        do boundarynode = 1, grid%bc(ibound)%nbnode
          node = grid%bc(ibound)%ibnode(boundarynode)
          if (node <= grid%nnodes0) state(node) = 1
        end do
      end if viscous_bc
    end do
    call update_ghosts( grid%nnodes0, grid%nnodes01, grid%l2g, grid%part, &
      state )
    do node = 1, grid%nnodes01
      if (state(node) == 1) call gridfreezenode(node)
    end do

    my_frozen_count = 0
    do node = 1, grid%nnodes0
      if (state(node) == 1) my_frozen_count = my_frozen_count + 1
    end do
    call lmpi_reduce(my_frozen_count,total_frozen_count)
    if (lmpi_master) then
      write(*,*) 'refineFreezeBL: ', total_frozen_count,                       &
        ' of ', grid%nnodesg, ' nodes frozen ',                                &
        real(total_frozen_count,dp)/real(grid%nnodesg,dp)*100.0_dp,' %'
    end if
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      bl_thick, grid%nnodes0
#endif
  end subroutine refineFreezeBL

!=========================== refineFreezeMixed ===============================80
!=============================================================================80

  subroutine refineFreezeMixed( grid )

    use grid_types,          only : grid_type
#ifdef HAVE_REFINE
    use kinddefs,            only : dp
    use load_balance,        only : update_ghosts
    use lmpi,                only : lmpi_reduce, lmpi_master
#endif

    type(grid_type), intent(inout) :: grid

#ifdef HAVE_REFINE
    integer :: node
    integer :: ielem, cell, cell_node
    integer, dimension(grid%nnodes01) :: state
    integer :: edge
    integer, dimension(grid%nnodes01) :: state_expanded
    integer :: my_frozen_count, total_frozen_count
    interface
      subroutine gridfreezenode( node )
        integer, intent(in) :: node
      end subroutine gridfreezenode
    end interface

    continue

    state = 0
    do ielem = 1, grid%nelem
      if ( 4 /= grid%elem(ielem)%node_per_cell ) then
        do cell = 1, grid%elem(ielem)%ncell
          do cell_node = 1, grid%elem(ielem)%node_per_cell
            node = grid%elem(ielem)%c2n(cell_node,cell)
            if (node <= grid%nnodes0) state(node) = 1
          end do
        end do
      end if
    end do
    call update_ghosts( grid%nnodes0, grid%nnodes01, grid%l2g, grid%part, &
      state )
    expand_mixed_freeze : if ( grid%nelem > 1 ) then
      state_expanded = 0
      do edge = 1, grid%nedgeloc
        if ( state(grid%eptr(1,edge)) == 1 .or. &
             state(grid%eptr(2,edge)) == 1 ) then
          if ( grid%eptr(1,edge) <= grid%nnodes0 ) &
            state_expanded(grid%eptr(1,edge)) = 1
          if ( grid%eptr(2,edge) <= grid%nnodes0 ) &
            state_expanded(grid%eptr(2,edge)) = 1
        end if
      end do
      call update_ghosts( grid%nnodes0, grid%nnodes01, grid%l2g, grid%part, &
        state_expanded )
      state = state_expanded
    end if expand_mixed_freeze
    do node = 1, grid%nnodes01
      if (state(node) == 1) call gridfreezenode(node)
    end do

    my_frozen_count = 0
    do node = 1, grid%nnodes0
      if (state(node) == 1) my_frozen_count = my_frozen_count + 1
    end do
    call lmpi_reduce(my_frozen_count,total_frozen_count)
    if (lmpi_master) then
      write(*,*) 'refineFreezeMixed: ', total_frozen_count,                    &
        ' of ', grid%nnodesg, ' nodes frozen ',                                &
        real(total_frozen_count,dp)/real(grid%nnodesg,dp)*100.0_dp,' %'
    end if
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE', grid%nnodes0
#endif
  end subroutine refineFreezeMixed

!=========================== refineFreezeFile ================================80
!=============================================================================80

  subroutine refineFreezeFile( grid, filename )

    use grid_types,          only : grid_type
#ifdef HAVE_REFINE
    use load_balance,        only : update_ghosts
    use lmpi,                only : lmpi_master
    use system_extensions,   only : se_open
#endif

    type(grid_type), intent(inout) :: grid
    character(len=*), intent(in) :: filename

#ifdef HAVE_REFINE
    integer                           :: ibound, node, boundarynode
    integer                           :: f, status
    integer, dimension(grid%nnodes01) :: state

    interface
      subroutine gridfreezenode( node )
        integer, intent(in) :: node
      end subroutine gridfreezenode
    end interface

    continue

    f = 49

    state = 0

    call se_open(unit=f,file=filename,status='old', iostat=status)
    if (status /= 0 ) then
      if (lmpi_master) write(*,*) &
        'freeze surface file not opened, returning ',trim(filename)
      return
    end if
    read_line_in_file : do
      read(f,*,err=234,end=234) ibound
      if ( (ibound < 1) .or. (ibound > grid%nbound) ) exit read_line_in_file
      if(lmpi_master) write(*,*)' freezing boundary face ', ibound
      do boundarynode = 1, grid%bc(ibound)%nbnode
        node = grid%bc(ibound)%ibnode(boundarynode)
        if (node <= grid%nnodes0) state(node) = 1
      end do
    enddo read_line_in_file
234 continue
    close(f)
    call update_ghosts( grid%nnodes0, grid%nnodes01, grid%l2g, grid%part, &
      state )
    do node = 1, grid%nnodes01
      if (state(node) == 1) call gridfreezenode(node)
    end do
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      grid%nnodes0, trim(filename)
#endif
  end subroutine refineFreezeFile

!=========================== refineFreezeRadius ==============================80
!=============================================================================80

  subroutine refineFreezeRadius( grid, user_specified_radius )

    use grid_types,          only : grid_type
    use kinddefs,            only : dp
#ifdef HAVE_REFINE
    use load_balance,        only : update_ghosts
    use lmpi,                only : lmpi_reduce, lmpi_master
#endif

    type(grid_type), intent(inout) :: grid
    real(dp),        intent(in)    :: user_specified_radius

#ifdef HAVE_REFINE
    integer :: node
    real(dp) :: radius_squared, radius
    integer, dimension(grid%nnodes01) :: state
    integer :: my_frozen_count, total_frozen_count
    interface
      subroutine gridfreezenode( node )
        integer, intent(in) :: node
      end subroutine gridfreezenode
    end interface

    continue

    state = 0 ! initialize node state to not frozen
    loop_over_all_nodes : do node = 1, grid%nnodes0
      radius_squared = grid%y(node)**2 + grid%z(node)**2
      ! prevent divide-by-zero on the x-axis
      radius = 0.0_dp
      if ( radius_squared > 0.0_dp ) radius = sqrt(radius_squared)
      ! freeze if radius is bigger then user_specified_H
      ! user_specified_H = user specified H/L times reference length
      if ( radius > user_specified_radius ) state(node) = 1
    end do loop_over_all_nodes

    ! do a little mpi magic for parallel execution
    call update_ghosts( grid%nnodes0, grid%nnodes01, grid%l2g, grid%part, &
      state )

    ! tell refine to freeze nodes with a state of 1
    do node = 1, grid%nnodes01
      if (state(node) == 1) call gridfreezenode(node)
    end do

    ! report nodes frozen to user
    my_frozen_count = 0
    do node = 1, grid%nnodes0
      if (state(node) == 1) my_frozen_count = my_frozen_count + 1
    end do
    call lmpi_reduce(my_frozen_count,total_frozen_count)
    if (lmpi_master) then
      write(*,*) 'refineFreezeRadius: ', total_frozen_count,                   &
        ' of ', grid%nnodesg, ' nodes frozen ',                                &
        real(total_frozen_count,dp)/real(grid%nnodesg,dp)*100.0_dp,' %'
    end if
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      user_specified_radius, grid%nnodes0
#endif
  end subroutine refineFreezeRadius

!=========================== refine_init_grid ================================80
!=============================================================================80

  subroutine refine_init_grid( grid, anisotropic_metric )

    use kinddefs,            only : dp
    use grid_types,          only : grid_type
    use lmpi,                only : lmpi_conditional_stop
#ifdef HAVE_REFINE
    use lmpi,                only : lmpi_id, lmpi_master
    use grid_helper,         only : create_test_g2l
    use grid_helper,         only : create_part, grid_canonic_cell
    use element_defs,       only : fun3d_to_ugrid_pyramid, &
      fun3d_to_ugrid_prism, fun3d_to_ugrid_hex
    use refine_interface,    only : refine_fortran_init, &
                                    refine_fortran_import_metric
#endif

    type(grid_type),           intent(inout) :: grid
    real(dp), dimension(6,grid%nnodes01), intent(in) :: anisotropic_metric

#ifdef HAVE_REFINE
    integer :: ibound, icell, face
    integer, dimension(:,:), allocatable :: canonic_c2n
    integer, dimension(:,:), allocatable :: f2n

    integer :: ielem

    interface
      function ref_fortran_import_cell( node_per_cell, ncell, c2n )
        integer :: ref_fortran_import_cell
        integer, intent(in) :: node_per_cell
        integer, intent(in) :: ncell
        integer, dimension(node_per_cell,ncell), intent(in) :: c2n
      end function ref_fortran_import_cell
      function ref_fortran_import_face( boundary_index, node_per, nface, f2n )
        integer :: ref_fortran_import_face
        integer, intent(in) :: boundary_index
        integer, intent(in) :: node_per
        integer, intent(in) :: nface
        integer, dimension(node_per,nface), intent(in) :: f2n
      end function ref_fortran_import_face
    end interface
    integer, dimension(8) :: nodes
    integer :: status
    continue

    call create_test_g2l(grid)
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    call lmpi_conditional_stop(                                                &
      refine_fortran_init( grid%nnodes01, grid%nnodesg, grid%l2g, grid%part,   &
      lmpi_id, grid%x, grid%y, grid%z ), 'ref_node_init')

    call lmpi_conditional_stop(                                                &
      refine_fortran_import_metric( grid%nnodes01, anisotropic_metric),'metric')

    module_header_nelem = grid%nelem
    allocate(module_header_type_cell(module_header_nelem))
    ref_element_group :do ielem = 1, grid%nelem
      module_header_type_cell(ielem) = grid%elem(ielem)%type_cell
      allocate( canonic_c2n( grid%elem(ielem)%node_per_cell,                   &
                             grid%elem(ielem)%ncell ) )
      orient_ref_cell : do icell = 1, grid%elem(ielem)%ncell
        call grid_canonic_cell(grid, grid%elem(ielem)%c2n(:,icell), &
                                     canonic_c2n(:,icell) )
        nodes(1:grid%elem(ielem)%node_per_cell) = canonic_c2n(:,icell)
        if ( grid%elem(ielem)%node_per_cell == 5 )                             &
          call fun3d_to_ugrid_pyramid(nodes(1:grid%elem(ielem)%node_per_cell), &
          canonic_c2n(:,icell))
        if ( grid%elem(ielem)%node_per_cell == 6 )                             &
          call fun3d_to_ugrid_prism(nodes(1:grid%elem(ielem)%node_per_cell),   &
          canonic_c2n(:,icell))
        if ( grid%elem(ielem)%node_per_cell == 8 )                             &
          call fun3d_to_ugrid_hex(nodes(1:grid%elem(ielem)%node_per_cell),     &
          canonic_c2n(:,icell))
      end do orient_ref_cell
      call lmpi_conditional_stop(                                              &
        ref_fortran_import_cell(grid%elem(ielem)%node_per_cell,                &
                        grid%elem(ielem)%ncell, canonic_c2n), 'ref_import_cell')
      deallocate( canonic_c2n )
      if (lmpi_master)                                          &
        write(*,'(" element group ",i0," of ",a," has ",i0)')   &
        ielem, grid%elem(ielem)%type_cell, grid%elem(ielem)%ncellg
    end do ref_element_group

! to remeber fun3d specific boundary info in module header
    module_header_nbound = grid%nbound
    allocate(module_header_ibc(module_header_nbound))
    allocate(module_header_bc_family(module_header_nbound))
    do ibound = 1, grid%nbound
      module_header_ibc(ibound) = grid%bc(ibound)%ibc
      module_header_bc_family(ibound) = grid%bc(ibound)%bc_family
    end do

    if (lmpi_master) write(*,'(1x,i0," boundary groups")') grid%nbound

    ref_boundary_group : do ibound = 1, grid%nbound
      has_tris : if ( grid%bc(ibound)%nbfacet > 0 ) then
        allocate( f2n(3,grid%bc(ibound)%nbfacet) )
        do face = 1, grid%bc(ibound)%nbfacet
          f2n(1,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2ntb(face,1))
          f2n(2,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2ntb(face,2))
          f2n(3,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2ntb(face,3))
        end do
        status=ref_fortran_import_face(ibound, 3, grid%bc(ibound)%nbfacet, f2n)
        if ( status /= 0 ) &
          call lmpi_conditional_stop(status, 'ref_fortran_import_face')
        deallocate( f2n )
      end if has_tris
      has_quads : if ( grid%bc(ibound)%nbfaceq > 0 ) then
        allocate( f2n(4,grid%bc(ibound)%nbfaceq) )
        do face = 1, grid%bc(ibound)%nbfaceq
          f2n(1,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,1))
          f2n(2,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,2))
          f2n(3,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,3))
          f2n(4,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,4))
        end do
        status=ref_fortran_import_face(ibound, 4, grid%bc(ibound)%nbfaceq, f2n )
        if ( status /= 0 ) &
          call lmpi_conditional_stop(status, 'ref_fortran_import_face')
        deallocate( f2n )
      end if has_quads
    end do ref_boundary_group
    call lmpi_conditional_stop(0, 'ref_fortran_import_face')

#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      grid%nnodes0, anisotropic_metric(1,1)
    call lmpi_conditional_stop(1, 'need HAVE_REFINE')
#endif
  end subroutine refine_init_grid

!=========================== refine_init_soln ================================80
!=============================================================================80

  subroutine refine_init_soln( grid, soln, sadj, design )

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type

    use lmpi,                 only : lmpi_conditional_stop
#ifdef HAVE_REFINE
    use lmpi,                 only : lmpi_master
    use nml_nonlinear_solves, only : itime
    use refine_interface,     only : refine_fortran_import_aux
    use solution_types,       only : generic_gas
#endif

    type(grid_type),           intent(inout) :: grid
    type(soln_type), optional, intent(inout) :: soln
    type(sadj_type), optional, intent(inout) :: sadj
    type(design_type), optional, intent(inout) :: design

#ifdef HAVE_REFINE
    integer :: naux, offset, func, status

    interface
      function ref_fortran_naux( naux )
        integer :: ref_fortran_naux
        integer, intent(in) :: naux
      end function ref_fortran_naux
    end interface

    continue

    naux = 0

    naux_soln : if ( present(soln) ) then
      naux_equ : if ( soln%eqn_set == generic_gas ) then
        naux = soln%n_tot
      else
        naux = soln%ndim
        naux = naux + soln%n_turb
        if (abs(itime) > 0) then
          naux = naux + soln%ndim + soln%n_turb     ! atn
          naux = naux + soln%ndim + soln%n_turb     ! atn1
          if (abs(itime) >=3) then
            naux = naux + soln%ndim + soln%n_turb   ! atn2
          end if
        end if
      end if naux_equ
      if (present(sadj) .and. present(design)) &
        naux = naux + soln%adim * design%nfunctions
    end if naux_soln

    if (lmpi_master) write(*,*) "import total solution vector size ",naux

    call lmpi_conditional_stop(ref_fortran_naux( naux ),'naux first call')

    offset = 0
    have_soln : if ( present(soln) ) then
      soln_eqn_set : if ( soln%eqn_set == generic_gas ) then
        status = refine_fortran_import_aux( soln%n_tot, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%n_tot
      else
        status = refine_fortran_import_aux( soln%ndim, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%ndim
        if (soln%n_turb>0) then
          status = refine_fortran_import_aux( soln%n_turb, grid%nnodes01, &
            offset, soln%turb )
          offset = offset + soln%n_turb
        end if
        if (abs(itime) > 0) then
          status = refine_fortran_import_aux( soln%ndim, grid%nnodes01, offset,&
            soln%qatn )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            status = refine_fortran_import_aux( soln%n_turb, grid%nnodes01, &
              offset, soln%turbatn )
            offset = offset + soln%n_turb
          end if
          status = refine_fortran_import_aux( soln%ndim, grid%nnodes01, offset,&
            soln%qatn1 )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            status = refine_fortran_import_aux( soln%n_turb, grid%nnodes01, &
              offset, soln%turbatn1 )
            offset = offset + soln%n_turb
          end if
          if (abs(itime) >=3) then
            status = refine_fortran_import_aux( soln%ndim, grid%nnodes01, &
              offset, soln%qatn2 )
            offset = offset + soln%ndim
            if (soln%n_turb>0) then
              status = refine_fortran_import_aux( soln%n_turb, grid%nnodes01, &
                offset, soln%turbatn2)
              offset = offset + soln%n_turb
            end if
          end if
        end if
      end if soln_eqn_set
      have_sadj : if (present(sadj) .and. present(design)) then
        do func = 1, design%nfunctions
          status = refine_fortran_import_aux( soln%adim, grid%nnodes01, offset,&
            sadj%rlam(:,:,func) )
          offset = offset + soln%adim
        end do
      end if have_sadj
    end if have_soln
    if ( naux /= offset ) then
      call lmpi_conditional_stop(1,'naux /= offset')
    end if
    call lmpi_conditional_stop(status,'status')
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE', grid%nnodes0
    call lmpi_conditional_stop(1, 'need HAVE_REFINE')
    if (present(soln)) write(*,*) soln%eqn_set
    if (present(sadj)) write(*,*) sadj%rlam(1,1,1)
    if (present(design)) write(*,*) design%nfunctions
#endif
  end subroutine refine_init_soln


!=========================== refine_grid_soln ================================80
!=============================================================================80

  subroutine refine_grid_soln( grid, soln, sadj, design )

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use design_types,         only : design_type
#ifdef HAVE_REFINE
    use solution_types,       only : generic_gas
    use kinddefs,             only : system_i8
    use refine_interface,     only : refine_fortran_node,                      &
                                     refine_fortran_aux
    use info_depr,            only : ebv_tets
    use nml_nonlinear_solves, only : itime
    use grid_motion_helpers,  only : need_grid_velocity
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_reset_lmpi_xfer,                     &
                                     grid_contiguous_imesh,                    &
                                     grid_contiguous_twod,                     &
                                     grid_cell_unique,                         &
                                     n2c_type, n2c_from_grid, deallocate_n2c
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use lmpi,                 only : lmpi_id, lmpi_master,                     &
                                     lmpi_reduce, lmpi_bcast,                  &
                                     lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer
    use parallel_embed,       only : embed_renumber_faces,                     &
                                     embed_find_boundary_cells,                &
                                     embed_add_adjoint_faces,                  &
                                     embed_test_boundary_cells,                &
                                     embed_make_edgeloc, embed_make_edge,      &
                                     embed_make_edgel2g, embed_share_edgel2g
    use grid_metrics,         only : compute_dual_metrics
    use nml_global,           only : moving_grid
    use bc_names,             only : need_distance_function
    use distance_function,    only : compute_distance_function
    use system_extensions,    only : se_flush
    use nml_overset_data,     only : overset_flag
    use element_defs,         only : ugrid_to_fun3d_pyramid,                   &
                                     ugrid_to_fun3d_prism, ugrid_to_fun3d_hex, &
                                     nullify_elem, initialize_elem
    use bc_types,             only : nullify_bc
    use refine_adaptation_input, only : adapt_twod
#else
    use lmpi,                 only : lmpi_master
#endif

    type(grid_type), intent(inout)           :: grid
    type(soln_type), intent(inout), optional :: soln
    type(sadj_type), intent(inout), optional :: sadj
    type(design_type), optional, intent(inout) :: design

#ifdef HAVE_REFINE
    integer :: i, ielem
    integer :: cell
    integer, dimension(:,:), allocatable :: f2n
    integer :: face, nunique
    integer(system_i8) :: nunique_i8, ncellg_i8
    type(n2c_type) :: n2c
    integer :: offset, status, func
    integer, dimension(8) :: nodes

    interface
      function ref_fortran_size_node( nnodes0, nnodes01, nnodesg )
        integer :: ref_fortran_size_node
        integer, intent(out) :: nnodes0, nnodes01, nnodesg
      end function ref_fortran_size_node
      function ref_fortran_size_cell( node_per_cell, ncell )
        integer :: ref_fortran_size_cell
        integer,               intent(in)  :: node_per_cell
        integer,               intent(out) :: ncell
      end function ref_fortran_size_cell
      function ref_fortran_cell( node_per_cell, ncell, c2n )
        integer :: ref_fortran_cell
        integer, intent(in)  :: node_per_cell, ncell
        integer, dimension(node_per_cell,ncell), intent(out) :: c2n
      end function ref_fortran_cell
      function ref_fortran_size_face( ibound, node_per_face, nface )
        integer :: ref_fortran_size_face
        integer,               intent(in)  :: ibound, node_per_face
        integer,               intent(out) :: nface
      end function ref_fortran_size_face
      function ref_fortran_face( ibound, node_per_face, nface, f2n )
        integer :: ref_fortran_face
        integer, intent(in)  :: ibound, node_per_face, nface
        integer, dimension(node_per_face,nface), intent(out) :: f2n
      end function ref_fortran_face
      function ref_fortran_free(  )
        integer :: ref_fortran_free
      end function ref_fortran_free
    end interface

    continue

    grid%partid = lmpi_id+1
    grid%project = "refine"

    if (lmpi_master) write(*,*) 'refineGetGrid motion=', trim(grid%grid_motion)

    call lmpi_conditional_stop(                                            &
      ref_fortran_size_node( grid%nnodes0, grid%nnodes01, grid%nnodesg ),  &
      'refine_grid_soln first call to ref_fortran_size_node')
    if (lmpi_master)                                        &
      write(*,'(" The adapted grid has",i0," nodesg")')     &
      grid%nnodesg
    grid%firstemptynode  = 0
    grid%firstemptynode0 = 0
    if (lmpi_master) write(*,*)"reload nodes..."
    call my_alloc_ptr(grid%l2g,      grid%nnodes01)
    call my_alloc_ptr(grid%x,        grid%nnodes01)
    call my_alloc_ptr(grid%y,        grid%nnodes01)
    call my_alloc_ptr(grid%z,        grid%nnodes01)

    call lmpi_conditional_stop(                                                &
      refine_fortran_node( grid%nnodes01, grid%l2g, grid%x, grid%y, grid%z ),  &
      'refine_fortran_node')

    if (lmpi_master) write(*,*)"filling level01 local part vectors..."
    call create_test_g2l(grid)
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    if (overset_flag) then
      call lmpi_conditional_stop(1, 'implement refine overset')
    end if

    have_soln : if (present(soln)) then
      if (lmpi_master) write(*,*)"reload solution..."
      offset = 0
! FIXME? sonl%neq* could be set by solution.set_up_neq
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
      call lmpi_reduce(soln%neq0, soln%dofg)
      call lmpi_bcast(soln%dofg)
      soln_eqn_set : if ( soln%eqn_set == generic_gas ) then
        call my_alloc_ptr( soln%q_dof, soln%n_tot, soln%neq01 )
        status = refine_fortran_aux( soln%n_tot, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%n_tot
      else
        call my_alloc_ptr( soln%q_dof, soln%ndim, grid%nnodes01 )
        status = refine_fortran_aux( soln%ndim, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%ndim
        if (soln%n_turb>0) then
          call my_alloc_ptr( soln%turb, soln%n_turb, grid%nnodes01 )
          status = refine_fortran_aux( soln%n_turb, grid%nnodes01, offset, &
            soln%turb )
          offset = offset + soln%n_turb
        else
          call my_alloc_ptr( soln%turb, 1, 1 )
        end if
        if(abs(itime) > 0) then
          call my_alloc_ptr( soln%qatn, soln%ndim, grid%nnodes01 )
          status = refine_fortran_aux( soln%ndim, grid%nnodes01, offset, &
            soln%qatn )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call my_alloc_ptr( soln%turbatn, soln%n_turb, grid%nnodes01 )
            status = refine_fortran_aux( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn )
            offset = offset + soln%n_turb
          else
            call my_alloc_ptr( soln%turbatn, 1, 1 )
          end if
          call my_alloc_ptr( soln%qatn1, soln%ndim, grid%nnodes01 )
          status = refine_fortran_aux( soln%ndim, grid%nnodes01, offset, &
            soln%qatn1 )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call my_alloc_ptr( soln%turbatn1, soln%n_turb, grid%nnodes01 )
            status = refine_fortran_aux( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn1 )
            offset = offset + soln%n_turb
          else
            call my_alloc_ptr( soln%turbatn1, 1, 1 )
          end if
          if(abs(itime) >= 3) then
            call my_alloc_ptr( soln%qatn2, soln%ndim, grid%nnodes01 )
            status = refine_fortran_aux( soln%ndim, grid%nnodes01, offset, &
              soln%qatn2 )
            offset = offset + soln%ndim
            if (soln%n_turb>0) then
              call my_alloc_ptr( soln%turbatn2, soln%n_turb, grid%nnodes01 )
              status = refine_fortran_aux( soln%n_turb, grid%nnodes01, offset,&
                soln%turbatn2 )
              offset = offset + soln%n_turb
            else
              call my_alloc_ptr( soln%turbatn2, 1, 1 )
            end if
          end if
        end if
      end if soln_eqn_set
      if (present(sadj) .and. present(design)) then
        call my_alloc_ptr( sadj%rlam,soln%adim,grid%nnodes01,design%nfunctions )
        do func = 1, design%nfunctions
          status = refine_fortran_aux( soln%adim, grid%nnodes01, offset, &
            sadj%rlam(:,:,func) )
          offset = offset + soln%adim
        end do
      end if
    end if have_soln

    call lmpi_conditional_stop(status, 'refine_fortran_aux status')

    if (lmpi_master) write(*,*) "total solution vector size ",offset

    !get cells FIXME MAP WORKING
    ! ncell, ncellg

    grid%nelem = module_header_nelem
    allocate(grid%elem(grid%nelem))
    init_each_elem_group : do ielem = 1, grid%nelem
      call nullify_elem( grid%elem(ielem) )
      grid%elem(ielem)%type_cell = module_header_type_cell(ielem)
      call initialize_elem( grid%elem(ielem) )
    end do init_each_elem_group
    deallocate(module_header_type_cell)

    get_each_elem_group : do ielem = 1, grid%nelem
      status = ref_fortran_size_cell( grid%elem(ielem)%node_per_cell, &
                                      grid%elem(ielem)%ncell )
      call my_alloc_ptr(grid%elem(ielem)%c2n,           &
                        grid%elem(ielem)%node_per_cell, &
                        grid%elem(ielem)%ncell )
      status = ref_fortran_cell( grid%elem(ielem)%node_per_cell, &
                                 grid%elem(ielem)%ncell,         &
                                 grid%elem(ielem)%c2n)

      orient_fun3d_cell : do cell = 1, grid%elem(ielem)%ncell
        nodes(1:grid%elem(ielem)%node_per_cell) = &
          grid%elem(ielem)%c2n(1:grid%elem(ielem)%node_per_cell,cell)
        if ( grid%elem(ielem)%node_per_cell == 5 )                             &
          call ugrid_to_fun3d_pyramid(nodes(1:grid%elem(ielem)%node_per_cell), &
          grid%elem(ielem)%c2n(1:grid%elem(ielem)%node_per_cell,cell))
        if ( grid%elem(ielem)%node_per_cell == 6 )                             &
          call ugrid_to_fun3d_prism(nodes(1:grid%elem(ielem)%node_per_cell),   &
          grid%elem(ielem)%c2n(1:grid%elem(ielem)%node_per_cell,cell))
        if ( grid%elem(ielem)%node_per_cell == 8 )                             &
          call ugrid_to_fun3d_hex(nodes(1:grid%elem(ielem)%node_per_cell),     &
          grid%elem(ielem)%c2n(1:grid%elem(ielem)%node_per_cell,cell))
      end do orient_fun3d_cell

      nunique_i8 = 0
      do cell = 1, grid%elem(ielem)%ncell
        if ( grid_cell_unique(grid, grid%elem(ielem)%c2n(:,cell) ) ) &
          nunique_i8 = nunique_i8 + 1
      end do
      call lmpi_reduce(nunique_i8, ncellg_i8)
      grid%elem(ielem)%ncellg = ncellg_i8
      call lmpi_bcast(grid%elem(ielem)%ncellg)

      if (lmpi_master)                                          &
        write(*,'(" element group ",i0," of ",a," has ",i0)')   &
        ielem, grid%elem(ielem)%type_cell, grid%elem(ielem)%ncellg
    end do get_each_elem_group

    if (lmpi_master) write(*,*)"get boundary faces, ", module_header_nbound
    grid%nbound = module_header_nbound
    allocate(grid%bc(grid%nbound))
    do i = 1, grid%nbound
      call nullify_bc(grid%bc(i))
    end do
    get_ibc_from_module_header : do i = 1, grid%nbound
      grid%bc(i)%ibc = module_header_ibc(i)
      grid%bc(i)%bc_family = module_header_bc_family(i)
    end do get_ibc_from_module_header
    deallocate(module_header_bc_family)
    deallocate(module_header_ibc)

    fill_bound : do i = 1, grid%nbound
!triangles
      status = ref_fortran_size_face( i, 3, grid%bc(i)%nbfacet )
      call my_alloc_ptr(grid%bc(i)%f2ntb,   max(1,grid%bc(i)%nbfacet),5)
      call my_alloc_ptr(grid%bc(i)%face_bit,max(1,grid%bc(i)%nbfacet))
      bc_has_tris : if ( grid%bc(i)%nbfacet > 0 ) then
        allocate(f2n(3, grid%bc(i)%nbfacet))
        status = ref_fortran_face( i, 3, grid%bc(i)%nbfacet, f2n )
        transpose_tri_index : do face = 1, grid%bc(i)%nbfacet
          grid%bc(i)%f2ntb(face,1) = f2n(1,face)
          grid%bc(i)%f2ntb(face,2) = f2n(2,face)
          grid%bc(i)%f2ntb(face,3) = f2n(3,face)
        end do transpose_tri_index
        deallocate( f2n )
      end if bc_has_tris
      nunique = 0
      do face = 1, grid%bc(i)%nbfacet
        if (grid%bc(i)%f2ntb(face,1) <= grid%nnodes0) then
          nunique = nunique + 1
          grid%bc(i)%face_bit(face) = 1
        else
          grid%bc(i)%face_bit(face) = 0
        end if
      end do
      call lmpi_reduce(nunique,grid%bc(i)%nbfacetg)
      call lmpi_bcast(grid%bc(i)%nbfacetg)
!quads
      status = ref_fortran_size_face( i, 4, grid%bc(i)%nbfaceq )
      call my_alloc_ptr(grid%bc(i)%f2nqb,    max(1,grid%bc(i)%nbfaceq),6)
      call my_alloc_ptr(grid%bc(i)%face_bitq,max(1,grid%bc(i)%nbfaceq))
      bc_has_quads : if ( grid%bc(i)%nbfaceq > 0 ) then
        allocate(f2n(4, grid%bc(i)%nbfaceq))
        status = ref_fortran_face( i, 4, grid%bc(i)%nbfaceq, f2n )
        transpose_quad_index : do face = 1, grid%bc(i)%nbfaceq
          grid%bc(i)%f2nqb(face,1) = f2n(1,face)
          grid%bc(i)%f2nqb(face,2) = f2n(2,face)
          grid%bc(i)%f2nqb(face,3) = f2n(3,face)
          grid%bc(i)%f2nqb(face,4) = f2n(4,face)
        end do transpose_quad_index
        deallocate( f2n )
      end if bc_has_quads
      nunique = 0
      do face = 1, grid%bc(i)%nbfaceq
        if (grid%bc(i)%f2nqb(face,1) <= grid%nnodes0) then
          nunique = nunique + 1
          grid%bc(i)%face_bitq(face) = 1
        else
          grid%bc(i)%face_bitq(face) = 0
        end if
      end do
      call lmpi_reduce(nunique,grid%bc(i)%nbfaceqg)
      call lmpi_bcast(grid%bc(i)%nbfaceqg)
    end do fill_bound

    if (lmpi_master) write(*,*)"set up node lmpi_xfer send and rec..."
    call grid_reset_lmpi_xfer (grid)

    if (lmpi_master) write(*,*)"construct node 2 cell (n2c) structure..."
    call n2c_from_grid(grid, n2c)

    if (lmpi_master) write(*,*)"find boundary cells..."
    call embed_find_boundary_cells (grid, n2c)
    if (lmpi_master) write(*,*)"find phantom adjoint boundary faces..."
    call embed_add_adjoint_faces(grid, n2c)
    if (lmpi_master) write(*,*)"renumber boundary nodes..."
    call embed_renumber_faces(grid)
    if (lmpi_master) write(*,*)"test boundary cells..."
    call embed_test_boundary_cells(grid)

    if (lmpi_master) write(*,*)"creating loc edges..."
    call embed_make_edgeloc (grid, n2c)
    if (lmpi_master) write(*,*)"creating remaining edges..."
    call embed_make_edge (grid, n2c)

    if (lmpi_master) write(*,*)"freeing node 2 cell (n2c) array..."
    call deallocate_n2c(n2c)

    if (lmpi_master) &
      write(*,*)"creating unique edge local to global numbering..."
    call my_alloc_ptr(grid%el2g,grid%nedge)
    call embed_make_edgel2g(2, grid%nedge, grid%eptr, &
      grid%nnodes0, grid%nnodes01, grid%l2g,          &
      grid%nedgeg, grid%el2g)

    if (lmpi_master) write(*,*)"share ghost edge local to global numbering..."
    call embed_share_edgel2g(2, grid%nedge, grid%eptr, grid%el2g, &
      grid, .true.)

    if (lmpi_master) write(*,*)"deallocate part and g2l helper arrays..."
    deallocate(grid%part)         ; grid%part         => null()
    deallocate(grid%sortedglobal) ; grid%sortedglobal => null()
    deallocate(grid%sortedlocal)  ; grid%sortedlocal  => null()

    if (overset_flag) call grid_contiguous_imesh(grid)

    if (adapt_twod) call grid_contiguous_twod(grid)

    if (lmpi_master) write(*,*)"populate ghost xyzs..."
    call lmpi_xfer(grid%x)
    call lmpi_xfer(grid%y)
    call lmpi_xfer(grid%z)

    resize_soln : if ( present(soln) ) then
      if (lmpi_master) write(*,*)"populate ghost solution..."
      resize_soln_equ_set : if ( soln%eqn_set== generic_gas ) then
        call my_realloc_ptr( soln%q_dof, soln%n_tot, grid%nnodes01 )
        call lmpi_xfer( soln%q_dof )
      else
        call my_realloc_ptr( soln%q_dof, soln%ndim, grid%nnodes01 )
        call lmpi_xfer( soln%q_dof )
        if (soln%n_turb>0) then
          call my_realloc_ptr( soln%turb, soln%n_turb, grid%nnodes01 )
          call lmpi_xfer( soln%turb )
        end if
        if (abs(itime) > 0) then
          call my_realloc_ptr( soln%qatn, soln%ndim, grid%nnodes01 )
          call lmpi_xfer( soln%qatn)
          if (soln%n_turb>0) then
            call my_realloc_ptr( soln%turbatn, soln%n_turb, grid%nnodes01 )
            call lmpi_xfer( soln%turbatn )
          end if
          call my_realloc_ptr( soln%qatn1, soln%ndim, grid%nnodes01 )
          call lmpi_xfer( soln%qatn1)
          if (soln%n_turb>0) then
            call my_realloc_ptr( soln%turbatn1, soln%n_turb, grid%nnodes01 )
            call lmpi_xfer( soln%turbatn1 )
          end if
          if (abs(itime) >= 3) then
            call my_realloc_ptr( soln%qatn2, soln%ndim, grid%nnodes01 )
            call lmpi_xfer( soln%qatn2)
            if (soln%n_turb>0) then
              call my_realloc_ptr( soln%turbatn2, soln%n_turb, grid%nnodes01 )
              call lmpi_xfer( soln%turbatn2 )
            end if
          end if
        end if
      end if resize_soln_equ_set
      resize_sadj : if (present(sadj) .and. present(design)) then
        call my_realloc_ptr( sadj%rlam, &
          soln%adim, grid%nnodes01, design%nfunctions )
        do func = 1, design%nfunctions
          call lmpi_xfer( sadj%rlam(:,:,func) )
        end do
      end if resize_sadj

      if (lmpi_master) write(*,*)"size soln neq scalars..."
      ! FIXME? sonl%neq* could be set by solution.set_up_neq
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
      call lmpi_reduce(soln%neq0, soln%dofg)
      call lmpi_bcast(soln%dofg)

    end if resize_soln

    if (lmpi_master) write(*,*)"allocate grid reals..."
    call my_alloc_ptr(grid%vol, grid%nnodes01)

    if (need_grid_velocity) then
      call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
      call my_alloc_ptr(grid%dydt,      grid%nnodes01)
      call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
      call my_alloc_ptr(grid%facespeed, grid%nedge)
    else
      call my_alloc_ptr(grid%dxdt,      1)
      call my_alloc_ptr(grid%dydt,      1)
      call my_alloc_ptr(grid%dzdt,      1)
      call my_alloc_ptr(grid%facespeed, 1)
    end if
    call my_alloc_ptr(grid%res_gcl,   1, 1)

    call my_alloc_ptr(grid%xn, grid%nedge)
    call my_alloc_ptr(grid%yn, grid%nedge)
    call my_alloc_ptr(grid%zn, grid%nedge)
    call my_alloc_ptr(grid%ra, grid%nedge)

    call my_alloc_ptr(grid%r11,       grid%nnodes01)
    call my_alloc_ptr(grid%r12,       grid%nnodes01)
    call my_alloc_ptr(grid%r13,       grid%nnodes01)
    call my_alloc_ptr(grid%r22,       grid%nnodes01)
    call my_alloc_ptr(grid%r23,       grid%nnodes01)
    call my_alloc_ptr(grid%r33,       grid%nnodes01)
    call my_alloc_ptr(grid%symmetry,  grid%nnodes01)

    if ( ebv_tets ) then
      call my_alloc_ptr(grid%weight,10,grid%nedge)
    else
      call my_alloc_ptr(grid%weight,1,1)
    endif

    do i = 1, grid%nbound
      call my_alloc_ptr(grid%bc(i)%bxn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%byn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%bzn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%slen_wall, grid%bc(i)%nbnode)
      if (need_grid_velocity) then
        call my_alloc_ptr( grid%bc(i)%bdxdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdydt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, max0(grid%bc(i)%nbnode,1) )
      else
        call my_alloc_ptr( grid%bc(i)%bdxdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdydt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, 1 )
      end if

    end do

! call PHYSICS_DEPS/grid_motion.f90:move_grid to get node/boundary speeds

    back_plane_volume : if ( trim(grid%grid_motion) /= 'static' .and. &
                             abs(itime) > 0 ) then
        call my_alloc_ptr( grid%xat0, grid%nnodes01 )
        call my_alloc_ptr( grid%yat0, grid%nnodes01 )
        call my_alloc_ptr( grid%zat0, grid%nnodes01 )
        grid%xat0 = grid%x
        grid%yat0 = grid%y
        grid%zat0 = grid%z

        call my_alloc_ptr( grid%xatn, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn3, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn3, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn3, grid%nnodes01 )

        call my_alloc_ptr(grid%volatn, grid%nnodes01)
        call my_alloc_ptr(grid%volatn1, grid%nnodes01)
        call my_alloc_ptr(grid%volatn2, grid%nnodes01)
        call my_alloc_ptr(grid%volatn3, grid%nnodes01)

    end if back_plane_volume

    if (lmpi_master) write(*,*)"make inviscid metrics..."
    call compute_dual_metrics(grid, moving_grid)

    call my_alloc_ptr(grid%slen,grid%nnodes01)
    call my_alloc_ptr(grid%iflagslen,grid%nnodes01)
    call my_alloc_ptr(grid%des_slen,grid%nnodes01)

    compute_dist_fcn : if (need_distance_function(grid%bc)) then
      if (lmpi_master) write(*,*)"computing distance function..."
      grid%idistfcn = 1
      call compute_distance_function(grid,.true.)
      call lmpi_xfer(grid%slen)
      call lmpi_xfer(grid%iflagslen)
    else
      if (lmpi_master) write(*,*)"skip distance function..."
      grid%idistfcn = 0
    end if compute_dist_fcn

    if (lmpi_master) then
      write(*,*)"distance function complete."
      call se_flush
    endif

    if (lmpi_master) then
      write(*,*)"refineGetGrid complete."
      call se_flush
    end if

    status = ref_fortran_free( )

#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) grid%nnodes0
    if (present(design) .and. .false.) write(*,*) design%nfunctions
    if (present(soln) .and. .false.) write(*,*) soln%eqn_set
    if (present(sadj) .and. .false.) write(*,*) sadj%rlam(1,1,1)
#endif

  end subroutine refine_grid_soln
!=========================== refine_subdivide ================================80
!=============================================================================80

  subroutine refine_subdivide( grid, ratio )

    use kinddefs,            only : dp
    use grid_types,          only : grid_type
    use lmpi,                only : lmpi_conditional_stop
#ifdef HAVE_REFINE
    use refine_interface,    only : refine_fortran_import_ratio
#endif

    type(grid_type),                    intent(inout) :: grid
    real(dp), dimension(grid%nnodes01), intent(in)    :: ratio

#ifdef HAVE_REFINE
    continue

    call lmpi_conditional_stop(                                                &
      refine_fortran_import_ratio( grid%nnodes01, ratio),'ratio')

#else
    if ( .false. ) print *, grid%nnodes0, ratio(1)
    call lmpi_conditional_stop(1, 'need HAVE_REFINE')
#endif
  end subroutine refine_subdivide

!=========================== refine_two_adapt ================================80
!=============================================================================80

  subroutine refine_two_adapt( )

    use lmpi,                only : lmpi_conditional_stop
#ifdef HAVE_REFINE
    interface
      function ref_fortran_adapt(  )
        integer :: ref_fortran_adapt
      end function ref_fortran_adapt
    end interface
    continue

    call lmpi_conditional_stop(  ref_fortran_adapt( ),'adapt')

#else
    call lmpi_conditional_stop(1, 'need HAVE_REFINE')
#endif
  end subroutine refine_two_adapt

!=========================== refineInit ======================================80
!=============================================================================80

  subroutine refineInit( grid, anisotropic_metric, soln, sadj )

    use kinddefs,             only : dp
    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
    use lmpi,                 only : lmpi_conditional_stop
#ifdef HAVE_REFINE
    use design_types,         only : design_run
    use nml_nonlinear_solves, only : itime
    use nml_grid_transform,   only : static_grid_transform
    use lmpi,                 only : lmpi_id, lmpi_master
    use grid_helper,          only : create_test_g2l
    use grid_helper,          only : create_part, grid_canonic_cell
    use refine_interface,     only : refine_gridcreate        &
                                   , refine_gridsetmap        &
                                   , refine_gridsetauxvector  &
                                   , refine_gridsetauxmatrix  &
                                   , refine_gridsetauxmatrix3
    use nml_overset_data,     only : overset_flag
    use solution_types,       only : generic_gas
#endif

    type(grid_type),           intent(inout) :: grid
    real(dp), dimension(6,grid%nnodes01), intent(in) :: anisotropic_metric
    type(soln_type), optional, intent(inout) :: soln
    type(sadj_type), optional, intent(inout) :: sadj

#ifdef HAVE_REFINE
    integer :: api_version, expected_version
    integer :: ibound, icell, face
    integer, dimension(:,:), allocatable :: canonic_c2n
    integer, dimension(:,:), allocatable :: f2n
    integer :: naux, offset

    integer :: ielem

    interface
      subroutine gridapiversion( api_version )
        integer, intent(out) :: api_version
      end subroutine gridapiversion
      subroutine gridsetcostconstraint( constraint )
        integer, intent(in) :: constraint
      end subroutine gridsetcostconstraint
      subroutine gridsetnodelocal2global( partid, nnodeg, nnode, nnode0,       &
                                          local2global )
        integer, intent(in) :: partid, nnodeg, nnode, nnode0
        integer, dimension(nnode), intent(in) :: local2global
      end subroutine gridsetnodelocal2global
      subroutine gridsetnodepart( nnode, part )
        integer, intent(in) :: nnode
        integer, dimension(nnode), intent(in) :: part
      end subroutine gridsetnodepart
      subroutine gridinsertcells( node_per_cell, ncell, c2n )
        integer, intent(in) :: node_per_cell, ncell
        integer, dimension(node_per_cell, ncell), intent(in) :: c2n
      end subroutine gridinsertcells
      subroutine gridinsertbc( ibound, node_per_face, nface, f2n )
        integer, intent(in) :: ibound, node_per_face, nface
        integer, dimension(node_per_face, nface), intent(in) :: f2n
      end subroutine gridinsertbc
      subroutine gridsetimesh( nnode, imesh )
        integer, intent(in) :: nnode
        integer, dimension(nnode), intent(in) :: imesh
      end subroutine gridsetimesh
      subroutine gridsetnaux( naux )
        integer, intent(in) :: naux
      end subroutine gridsetnaux
    end interface

    continue

    call create_test_g2l(grid)
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    call gridapiversion( api_version )

    expected_version = 100800000
    wrong_version : if ( expected_version /= api_version ) then
      if (lmpi_master)                                                         &
        write(*,'(" refine: wrong api version (",i0,") expected ",i0)')        &
        api_version, expected_version
      call lmpi_conditional_stop(1,"refine api version")
    end if wrong_version
    call lmpi_conditional_stop(0,"refine api version")

    if (trim(grid%grid_motion) /= 'static' .or. static_grid_transform) then
      call refine_gridcreate( lmpi_id, grid%nnodes01,                          &
                              grid%xat0, grid%yat0, grid%zat0 )
    else
      call refine_gridcreate( lmpi_id, grid%nnodes01, grid%x, grid%y, grid%z )
    end if
    call gridsetnodelocal2global( lmpi_id, grid%nnodesg,                     &
                                 grid%nnodes01, grid%nnodes0, grid%l2g )
    call gridsetnodepart( grid%nnodes01, grid%part )

! 1 - volume, 8 - projectable single face per tet
    if ( design_run ) call gridsetcostconstraint( 1+8 )

    call refine_gridsetmap( grid%nnodes01, anisotropic_metric )

    module_header_nelem = grid%nelem
    allocate(module_header_type_cell(module_header_nelem))
    each_element_group :do ielem = 1, grid%nelem
      module_header_type_cell(ielem) = grid%elem(ielem)%type_cell
      allocate( canonic_c2n( grid%elem(ielem)%node_per_cell,                   &
                             grid%elem(ielem)%ncell ) )
      orient_each_cell : do icell = 1, grid%elem(ielem)%ncell
        call grid_canonic_cell(grid, grid%elem(ielem)%c2n(:,icell), &
                                     canonic_c2n(:,icell) )
      end do orient_each_cell
      call gridinsertcells( grid%elem(ielem)%node_per_cell,                    &
                            grid%elem(ielem)%ncell, canonic_c2n )
      deallocate( canonic_c2n )
      if (lmpi_master)                                          &
        write(*,'(" element group ",i0," of ",a," has ",i0)')   &
        ielem, grid%elem(ielem)%type_cell, grid%elem(ielem)%ncellg
    end do each_element_group

! to remeber fun3d specific boundary info in module header
    module_header_nbound = grid%nbound
    allocate(module_header_ibc(module_header_nbound))
    allocate(module_header_bc_family(module_header_nbound))
    do ibound = 1, grid%nbound
      module_header_ibc(ibound) = grid%bc(ibound)%ibc
      module_header_bc_family(ibound) = grid%bc(ibound)%bc_family
    end do

    do ibound = 1, grid%nbound
      bound_has_tris : if ( grid%bc(ibound)%nbfacet > 0 ) then
        allocate( f2n(3,grid%bc(ibound)%nbfacet) )
        do face = 1, grid%bc(ibound)%nbfacet
          f2n(1,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2ntb(face,1))
          f2n(2,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2ntb(face,2))
          f2n(3,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2ntb(face,3))
        end do
        call gridinsertbc( ibound, 3, grid%bc(ibound)%nbfacet, f2n )
        deallocate( f2n )
      end if bound_has_tris
      bound_has_quads : if ( grid%bc(ibound)%nbfaceq > 0 ) then
        allocate( f2n(4,grid%bc(ibound)%nbfaceq) )
        do face = 1, grid%bc(ibound)%nbfaceq
          f2n(1,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,1))
          f2n(2,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,2))
          f2n(3,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,3))
          f2n(4,face) = grid%bc(ibound)%ibnode(grid%bc(ibound)%f2nqb(face,4))
        end do
        call gridinsertbc( ibound, 4, grid%bc(ibound)%nbfaceq, f2n )
        deallocate( f2n )
      end if bound_has_quads
    end do

    imesh_for_overset : if (overset_flag) then
      call gridsetimesh( grid%nnodes01, grid%imesh )
    end if imesh_for_overset

    naux = 0

    naux_soln : if ( present(soln) ) then
      naux_equ : if ( soln%eqn_set == generic_gas ) then
        naux = soln%n_tot
      else
        naux = soln%ndim
        naux = naux + soln%n_turb
        if (abs(itime) > 0) then
          naux = naux + soln%ndim + soln%n_turb     ! atn
          naux = naux + soln%ndim + soln%n_turb     ! atn1
          if (abs(itime) >=3) then
            naux = naux + soln%ndim + soln%n_turb   ! atn2
          end if
        end if
      end if naux_equ
      if (present(sadj) ) naux = naux + soln%adim
    end if naux_soln

    call gridsetnaux( naux )

    offset = 0
    have_soln : if ( present(soln) ) then
      soln_eqn_set : if ( soln%eqn_set == generic_gas ) then
        call refine_gridsetauxmatrix( soln%n_tot, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%n_tot
      else
        call refine_gridsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%ndim
        if (soln%n_turb>0) then
          call refine_gridsetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
            soln%turb )
          offset = offset + soln%n_turb
        end if
        if (abs(itime) > 0) then
          call refine_gridsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call refine_gridsetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn )
            offset = offset + soln%n_turb
          end if
          call refine_gridsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn1 )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call refine_gridsetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn1 )
            offset = offset + soln%n_turb
          end if
          if (abs(itime) >=3) then
            call refine_gridsetauxmatrix( soln%ndim, grid%nnodes01, offset, &
              soln%qatn2 )
            offset = offset + soln%ndim
            if (soln%n_turb>0) then
              call refine_gridsetauxmatrix( soln%n_turb, grid%nnodes01, &
                offset, soln%turbatn2)
              offset = offset + soln%n_turb
            end if
          end if
        end if
      end if soln_eqn_set
      have_sadj : if (present(sadj)) then
        call refine_gridsetauxmatrix3( soln%adim, grid%nnodes01, offset, &
          sadj%rlam )
        offset = offset + soln%adim
      end if have_sadj
    end if have_soln
    if ( naux /= offset ) then
      call lmpi_conditional_stop(1,' naux /= offset')
    end if
    call lmpi_conditional_stop(0,' naux /= offset')
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      grid%nnodes0, anisotropic_metric(1,1)
    call lmpi_conditional_stop(1, 'need HAVE_REFINE')
    if (present(soln)) write(*,*) soln%eqn_set
    if (present(sadj)) write(*,*) sadj%rlam(1,1,1)
#endif
  end subroutine refineInit

!=========================== refineLoadCapriGeom =============================80
!=============================================================================80

  function refineLoadCapriGeom( url, modeler, capri )
    logical :: refineLoadCapriGeom
    character(len=*), intent(in) :: url
    character(len=*), intent(in) :: modeler
    character(len=*), intent(in) :: capri
#ifdef HAVE_REFINE
    integer :: nullIndex
    character(len=256) :: tempString
    character(len=81) :: modelerString
    character(len=256) :: urlString
    integer :: status
    interface
      subroutine gridParallelLoadCapri(url,modeler,capriProject,status)
        character(*), intent(in) :: url,modeler,capriProject
        integer, intent(out) :: status
      end subroutine gridParallelLoadCapri
    end interface

    continue

    urlString = url
    nullIndex = min(len_trim(urlString)+1,len(urlString))
    urlString(nullIndex:nullIndex) = char(0) ! null terminate for safey in C
    modelerString = modeler
    nullIndex = min(len_trim(modelerString)+1,len(modelerString))
    modelerString(nullIndex:nullIndex) = char(0) ! null terminate for safey in C
    tempString = capri
    nullIndex = min(len_trim(tempString)+1,len(tempString))
    tempString(nullIndex:nullIndex) = char(0) ! null terminate for safey in C
    call gridParallelLoadCapri(urlString,modelerString,tempString,status)
    refineLoadCapriGeom = (status==1)
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      trim(url), trim(modeler), trim(capri)
    refineLoadCapriGeom = .false.
#endif
  end function refineLoadCapriGeom

!=========================== refineSaveCapriGeom =============================80
!=============================================================================80

  subroutine refineSaveCapriGeom( capri )

#ifdef HAVE_REFINE
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_master, lmpi_reduce, lmpi_bcast,          &
                                lmpi_conditional_stop
    use sort,            only : heap_sort
#endif

    character(len=*), intent(in) :: capri

#ifdef HAVE_REFINE
    integer :: nGeomNode, nGeomEdge, nGeomFace
    integer :: localBoundNode, nBoundNode
    integer :: node, markednode, maxedgenode
    integer :: istop
    integer, dimension(:), allocatable :: oldglobal

    integer :: geomedge
    integer :: ncurve
    integer,     dimension(:),   pointer :: curve
    real(dp), dimension(:,:), pointer    :: curve_xyz
    real(dp), dimension(:),   pointer    :: curve_t

    integer :: geomface
    integer :: nface, face, i, lastnode, nfaceg
    integer,     dimension(:,:), pointer :: f2n
    real(dp), dimension(:,:), pointer    :: f2n_xyz, f2n_uv
    integer,     dimension(:),   allocatable :: facenodes, sortnodes
    integer :: faceedgecount, nnode
    integer :: min_face_interior_node, max_face_interior_node
    integer,     dimension(:),   allocatable :: facel2g, faceg2l
    real(dp), dimension(:,:), allocatable    :: xyz, uv

    integer :: nullIndex
    character(len=256) :: tempString
    interface
      subroutine gridlocalboundnode( localBoundNode )
        integer, intent(out) :: localBoundNode
      end subroutine gridlocalboundnode
      subroutine gridgeomsize( nGeomNode, nGeomEdge, nGeomFace )
        integer, intent(out) :: nGeomNode, nGeomEdge, nGeomFace
      end subroutine gridgeomsize
      subroutine gridupdateedgegrid( edgeId, nCurveNode, xyz, t)
        integer, intent(in) :: edgeId, nCurveNode
        real(selected_real_kind(15,307)),dimension(nCurveNode),intent(in) :: xyz
        real(selected_real_kind(15,307)),dimension(nCurveNode),intent(in) :: t
      end subroutine gridupdateedgegrid
      subroutine gridrenumberglobalnodes( nnode, new2old )
        integer, intent(in) :: nnode
        integer, dimension(nnode), intent(inout) :: new2old
      end subroutine gridrenumberglobalnodes
      subroutine gridfaceedgecount( faceId, faceEdgeCount )
        integer, intent(in)  :: faceId
        integer, intent(out) :: faceEdgeCount
      end subroutine gridfaceedgecount
      subroutine gridfaceedgel2g( faceId, faceEdgeCount, local2global )
        integer, intent(in)  :: faceId, faceEdgeCount
        integer, dimension(faceEdgeCount), intent(out) :: local2global
      end subroutine gridfaceedgel2g
      subroutine gridupdategeometryface( faceId, nnode, xyz, uv, nface, f2n)
        integer, intent(in) :: faceId, nnode, nface
        real(selected_real_kind(15,307)),dimension(3,nnode),intent(in) :: xyz
        real(selected_real_kind(15,307)),dimension(2,nnode),intent(in) :: uv
        integer, dimension(3,nface), intent(in) :: f2n
      end subroutine gridupdategeometryface
      subroutine gridCreateShellFromFaces()
      end subroutine gridCreateShellFromFaces
      subroutine gridParallelSaveCapri(capriProject)
        character(*), intent(in) :: capriProject
      end subroutine gridParallelSaveCapri
    end interface

    continue

    call eliminate_unused_globals( )

    call gridlocalboundnode( localBoundNode )
    call lmpi_reduce( localBoundNode, nBoundNode)
    call lmpi_bcast( nBoundNode )
! the oldglobal array sets the new ordering for the gridex restart convention
    allocate(oldglobal(nBoundNode)); oldglobal = -1
    if (lmpi_master)                                                         &
      write(*,'("  There are",i0," nodes on the boundary.")') nBoundNode
    call gridgeomsize( nGeomNode, nGeomEdge, nGeomFace )
    if (lmpi_master)                                                           &
      write(*,'("  There are ",i0," ",i0," geometry nodes and edges.")')       &
      nGeomNode, nGeomEdge
! mark geometry nodes
    do node = 1, nGeomNode
      oldglobal(node) = node
    end do
    markednode = nGeomNode
! mark geometry edge interior nodes
    do geomedge = 1, nGeomEdge
      nullify( curve, curve_xyz, curve_t )
      call get_edge_curve(geomedge, ncurve, curve, curve_xyz, curve_t)
      edge_global_renumbering : do node = 2, ncurve-1
        markednode = markednode + 1
        if (markednode <= nBoundNode) then
          oldglobal(markednode) = curve(node)
        else
          write(*,*) 'error1 nBoundNode markednode ', markednode, nBoundNode
        end if
      end do edge_global_renumbering
      if (lmpi_master)                                                       &
        call gridupdateedgegrid( geomedge, ncurve, curve_xyz, curve_t)
      deallocate( curve, curve_xyz, curve_t )
    end do
    maxedgenode = markednode
    if (lmpi_master)                                                           &
      write(*,'("  Renumber",i0," global nodes for geometry edges...")')       &
      markednode
    call gridRenumberGlobalNodes( markednode, oldglobal )
    do node = 1, markednode
      oldglobal(node) = node
    end do
    if (lmpi_master) write(*,'("  The largest edge node",i0)') maxedgenode
! start on faces
    if (lmpi_master)                                                         &
      write(*,'("  There are",i0," geometry faces.")') nGeomFace
! continue to fill oldglobal in so that the desired global index is set for
!   interior geometry face nodes
    istop = 0
    nfaceg = 0
    do geomface = 1, nGeomFace
      nullify( f2n, f2n_xyz, f2n_uv )
! returns the current global indices of the face triangles
!   (the interior face nodes are not in gridex convention order yet)
      call gather_geometry_faces(geomface, nface, f2n, f2n_xyz, f2n_uv )
      if (lmpi_master)                                                       &
        write(*,'("   Face",i0," has",i0," triangles.")') geomface, nface
      allocate(facenodes(3*nface),sortnodes(3*nface))
      do face = 1, nface
        do node = 1, 3
          facenodes(node+3*(face-1)) = f2n(node,face)
        end do
      end do
      call heap_sort(nface*3,facenodes, sortnodes)
      lastnode = -1
      do i = 1, 3*nface
        node = facenodes(sortnodes(i))
        if (node /= lastnode) then
          lastnode = node
          if (node>maxedgenode) then
            markednode = markednode + 1
            if (markednode <= nBoundNode) then
              oldglobal(markednode) = node
            else
              write(*,*) 'error2 nBoundNode markednode ', markednode, nBoundNode
              istop = 1
            end if
          end if
        end if
      end do
      deallocate( facenodes, sortnodes )
      deallocate( f2n, f2n_xyz, f2n_uv )
      nfaceg = nfaceg + nface
    end do
    call lmpi_conditional_stop(istop)
    istop = 0
    if (lmpi_master) then
      write(*,'("  There are ",i0," total boundary faces...")')                &
      nfaceg
      write(*,'("  Renumber ",i0," global nodes for geometry faces...")')      &
      markednode
      if (markednode /= nBoundNode) then
        write(*,*) 'error3 nBoundNode /= markednode ', markednode, nBoundNode
        istop = 1
      end if
    endif
    call lmpi_conditional_stop(istop)
! all the boundary nodes are finally given the correct global index for
!   the gridex convention in this call to gridRenumberGlobalNodes
    call gridRenumberGlobalNodes( markednode, oldglobal )
    deallocate(oldglobal)
    if (lmpi_master) allocate(faceg2l(nBoundNode))
    face_update : do geomface = 1, nGeomFace
      ! master needs a correct faceg2l to prepare for gridupdategeometryface
      mark_faceg2l_edge_nodes : if (lmpi_master) then
        faceg2l = 0
        call gridfaceedgecount( geomface, faceEdgeCount )
        allocate(facel2g(faceEdgeCount)); facel2g = -1
        call gridfaceedgel2g(geomface, faceEdgeCount, facel2g  )
        do node = 1, faceEdgeCount
          faceg2l(facel2g(node)) = node
        end do
        deallocate(facel2g)
        nnode = faceEdgeCount
      end if mark_faceg2l_edge_nodes
      nullify( f2n, f2n_xyz, f2n_uv )
      call gather_geometry_faces(geomface, nface, f2n, f2n_xyz, f2n_uv )
      master_update_face_nodes : if (lmpi_master) then
        min_face_interior_node = nBoundNode+1
        max_face_interior_node = -1
        find_min_max_face_interior_ind : do face = 1, nface
          do node = 1, 3
            node_is_interior : if (faceg2l(f2n(node,face)) <= 0) then
              min_face_interior_node=min(min_face_interior_node,f2n(node,face))
              max_face_interior_node=max(max_face_interior_node,f2n(node,face))
            end if node_is_interior
          end do
        end do find_min_max_face_interior_ind
        mark_faceg2l_face_interior: do node = min_face_interior_node, &
                                              max_face_interior_node
          nnode = nnode + 1
          faceg2l(node) = nnode
        end do mark_faceg2l_face_interior
        write(*,'("   Face ",i0," has ",i0," nodes.")') geomface, nnode
        allocate(xyz(3,nnode)) ; xyz = -999.0_dp
        allocate(uv(2,nnode)) ; uv = -999.0_dp
        master_populate_face_nodes: do face = 1, nface
          f2n(1,face) = faceg2l(f2n(1,face))
          f2n(2,face) = faceg2l(f2n(2,face))
          f2n(3,face) = faceg2l(f2n(3,face))
          xyz(:,f2n(1,face)) = f2n_xyz(1:3,face)
          xyz(:,f2n(2,face)) = f2n_xyz(4:6,face)
          xyz(:,f2n(3,face)) = f2n_xyz(7:9,face)
          uv(:,f2n(1,face))  = f2n_uv(1:2,face)
          uv(:,f2n(2,face))  = f2n_uv(3:4,face)
          uv(:,f2n(3,face))  = f2n_uv(5:6,face)
        end do master_populate_face_nodes
        call gridupdategeometryface( geomface, nnode, xyz, uv, nface, f2n )
        deallocate(xyz,uv)
      end if master_update_face_nodes
      deallocate( f2n, f2n_xyz, f2n_uv )
    end do face_update
    if (lmpi_master) then
      deallocate(faceg2l)
      write(*,'(" Replace Volume UGrid with TShell...")')
      call gridCreateShellFromFaces()
      write(*,'(" Save new CAPRI geometry info ",a,"...")') trim( capri )
      tempString = capri
      nullIndex = min(len_trim(tempString)+1,len(tempString))
      tempString(nullIndex:nullIndex) = char(0) ! null terminate for safey in C
      call gridParallelSaveCapri(tempString)
    end if
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',  &
      trim(capri)
#endif
  end subroutine refineSaveCapriGeom

#ifdef HAVE_REFINE
!=========================== get_edge_curve ==================================80
!=============================================================================80
  subroutine get_edge_curve(edgeId, ncurve, curve, curve_xyz, curve_t)

    use kinddefs,        only : dp
    use lmpi,            only : lmpi_master, lmpi_bcast
    use allocations,     only : my_alloc_ptr

    integer,                     intent(in)  :: edgeId
    integer,                     intent(out) :: ncurve
    integer,     dimension(:),   pointer     :: curve
    real(dp), dimension(:,:), pointer        :: curve_xyz
    real(dp), dimension(:),   pointer        :: curve_t

    integer                              :: nedge
    integer,     dimension(2)            :: endPoints
    integer,     dimension(:,:), pointer :: edgenodes
    real(dp), dimension(:,:), pointer    :: edgeparam
    integer                              :: activenode, lastedge, segment, try

    interface
      subroutine gridgeomedgeendpoints( edgeId, endPoints )
        integer, intent(in) :: edgeId
        integer, dimension(2), intent(out) :: endPoints
      end subroutine gridgeomedgeendpoints
    end interface

    continue

    ncurve = 0

    call gridgeomedgeendpoints( edgeId, endPoints )

    nullify( edgenodes, edgeparam )
    call gather_geometry_edge_segments(edgeId, nedge, edgenodes, edgeparam)
    ncurve = nedge + 1
    call my_alloc_ptr(curve,ncurve) ; curve = 0
    call my_alloc_ptr(curve_t,ncurve) ; curve_t = -999.0_dp
    call my_alloc_ptr(curve_xyz,3,ncurve) ; curve_xyz = -999.0_dp
    master_forms_curve_from_seg : if (lmpi_master) then
      curve(1) = endPoints(1)
      activenode = curve(1)
      lastedge = -1
      curve_segment : do segment = 1, nedge
        find_next_segment : do try = 1, nedge
          if (try==lastedge) cycle find_next_segment
          found_next_edge1 : if ( activenode==edgenodes(1,try) ) then
            curve(segment+1) = edgenodes(2,try)
            curve_xyz(1,segment)   = edgeparam(1,try)
            curve_xyz(2,segment)   = edgeparam(2,try)
            curve_xyz(3,segment)   = edgeparam(3,try)
            curve_t(    segment)   = edgeparam(4,try)
            curve_xyz(1,segment+1) = edgeparam(5,try)
            curve_xyz(2,segment+1) = edgeparam(6,try)
            curve_xyz(3,segment+1) = edgeparam(7,try)
            curve_t(    segment+1) = edgeparam(8,try)
            activenode = curve(segment+1)
            lastedge = try
            exit find_next_segment
          end if found_next_edge1
          found_next_edge2 : if ( activenode==edgenodes(2,try) ) then
            curve(segment+1) = edgenodes(1,try)
            curve_xyz(1,segment+1) = edgeparam(1,try)
            curve_xyz(2,segment+1) = edgeparam(2,try)
            curve_xyz(3,segment+1) = edgeparam(3,try)
            curve_t(    segment+1) = edgeparam(4,try)
            curve_xyz(1,segment)   = edgeparam(5,try)
            curve_xyz(2,segment)   = edgeparam(6,try)
            curve_xyz(3,segment)   = edgeparam(7,try)
            curve_t(    segment)   = edgeparam(8,try)
            activenode = curve(segment+1)
            lastedge = try
            exit find_next_segment
          end if found_next_edge2
        end do find_next_segment
      end do curve_segment
    end if master_forms_curve_from_seg
    call lmpi_bcast(curve)
    call lmpi_bcast(curve_xyz)
    call lmpi_bcast(curve_t)
    deallocate( edgenodes )
    deallocate( edgeparam )

  end subroutine get_edge_curve
#endif

#ifdef HAVE_REFINE
!=========================== gather_geometry_edge_segments ===================80
!=============================================================================80
  subroutine gather_geometry_edge_segments(geomedge,nedge,edgenodes,edgeparam)

    use kinddefs,        only : dp
    use lmpi,            only : lmpi_id, lmpi_nproc,                           &
                                lmpi_reduce, lmpi_bcast,                       &
                                lmpi_allgather, lmpi_allgatherv
    use allocations,     only : my_alloc_ptr

    integer,                              intent(in)  :: geomedge
    integer,                              intent(out) :: nedge
    integer,     dimension(:,:), pointer              :: edgenodes
    real(dp), dimension(:,:), pointer                 :: edgeparam

    integer                                  :: unique
    integer                                  :: maxedge, edge
    integer                                  :: edgeId
    integer,     dimension(2)                :: globalnodes, nodeparts
    real(dp), dimension(2)                   :: t
    real(dp), dimension(3,2)                 :: xyz
    integer,     dimension(:,:), allocatable :: localnodes
    real(dp), dimension(:,:), allocatable    :: localparam
    integer,     dimension(:),   allocatable :: nedgeproc

    interface
      subroutine gridmaxedge( maxedge )
        integer, intent(out) :: maxedge
      end subroutine gridmaxedge
      subroutine gridedge( edge, edgeId, globalnodes, nodeparts, t, xyz )
        integer,               intent(in)  :: edge
        integer,               intent(out) :: edgeId
        integer, dimension(2), intent(out) :: globalnodes, nodeparts
        real(selected_real_kind(15,307)), dimension(2),   intent(out) :: t
        real(selected_real_kind(15,307)), dimension(3,2), intent(out) :: xyz
      end subroutine gridedge
    end interface

    continue

    call gridmaxedge(maxedge)
    unique = 0
    do edge = 1, maxedge
      call gridedge( edge, edgeId, globalnodes, nodeparts, t, xyz )
      count_this_edge_id : if (geomedge == edgeId) then
        if (globalnodes(1) < globalnodes(2) ) then
          if (lmpi_id == nodeparts(1)) unique = unique + 1
        else
          if (lmpi_id == nodeparts(2)) unique = unique + 1
        end if
      end if count_this_edge_id
    end do
    call lmpi_reduce(unique, nedge)
    call lmpi_bcast(nedge)
    allocate(localnodes(2,max(1,unique))) ; localnodes = 0
    allocate(localparam(8,max(1,unique))) ; localparam = -999.0_dp
    allocate(nedgeproc(lmpi_nproc)) ; nedgeproc = 0;
    call my_alloc_ptr(edgenodes,2,max(1,nedge))
    call my_alloc_ptr(edgeparam,8,max(1,nedge))
    unique = 0
    do edge = 1, maxedge
      call gridedge( edge, edgeId, globalnodes, nodeparts, t, xyz )
      add_unique_edge_id : if (geomedge == edgeId) then
        if (globalnodes(1) < globalnodes(2) ) then
          if (lmpi_id == nodeparts(1)) then
            unique = unique + 1
            localnodes(1,unique) = globalnodes(1)
            localnodes(2,unique) = globalnodes(2)
            localparam(1,unique) = xyz(1,1)
            localparam(2,unique) = xyz(2,1)
            localparam(3,unique) = xyz(3,1)
            localparam(4,unique) = t(1)
            localparam(5,unique) = xyz(1,2)
            localparam(6,unique) = xyz(2,2)
            localparam(7,unique) = xyz(3,2)
            localparam(8,unique) = t(2)
          end if
        else
          if (lmpi_id == nodeparts(2)) then
            unique = unique + 1
            localnodes(1,unique) = globalnodes(1)
            localnodes(2,unique) = globalnodes(2)
            localparam(1,unique) = xyz(1,1)
            localparam(2,unique) = xyz(2,1)
            localparam(3,unique) = xyz(3,1)
            localparam(4,unique) = t(1)
            localparam(5,unique) = xyz(1,2)
            localparam(6,unique) = xyz(2,2)
            localparam(7,unique) = xyz(3,2)
            localparam(8,unique) = t(2)
          end if
        end if
      end if add_unique_edge_id
    end do
    call lmpi_allgather(unique,nedgeproc)
    call lmpi_allgatherv(localnodes, nedgeproc, edgenodes)
    call lmpi_allgatherv(localparam, nedgeproc, edgeparam)
    deallocate(localnodes, localparam, nedgeproc)

  end subroutine gather_geometry_edge_segments
#endif

#ifdef HAVE_REFINE
!=========================== gather_geometry_faces ===========================80
!=============================================================================80
  subroutine gather_geometry_faces(faceId, nface, f2n, f2n_xyz, f2n_uv )

    use kinddefs,        only : dp
    use lmpi,            only : lmpi_id, lmpi_nproc,                           &
                                lmpi_reduce, lmpi_bcast,                       &
                                lmpi_allgather, lmpi_allgatherv
    use allocations,     only : my_alloc_ptr

    integer,                              intent(in)  :: faceId
    integer,                              intent(out) :: nface
    integer,     dimension(:,:), pointer              :: f2n
    real(dp), dimension(:,:), pointer                 :: f2n_xyz, f2n_uv

    integer                              :: maxface
    integer                              :: face, unique, nfacelocal
    integer                              :: id
    integer,     dimension(3)            :: globalnodes, nodeparts
    real(dp), dimension(2,3)             :: uv
    real(dp), dimension(3,3)             :: xyz
    integer,     dimension(:,:), pointer :: localf2n
    real(dp), dimension(:,:), pointer    :: localf2n_xyz, localf2n_uv
    integer,     dimension(:),   pointer :: nfaceproc

    interface
      subroutine gridmaxface( maxface )
        integer, intent(out) :: maxface
      end subroutine gridmaxface
      subroutine gridface( face, faceId, globalnodes, nodeparts, uv, xyz )
        integer,               intent(in)  :: face
        integer,               intent(out) :: faceId
        integer, dimension(3), intent(out) :: globalnodes, nodeparts
        real(selected_real_kind(15,307)), dimension(2,3), intent(out) :: uv
        real(selected_real_kind(15,307)), dimension(3,3), intent(out) :: xyz
      end subroutine gridface
    end interface
    continue
    nface = 0
    nullify(f2n)
    call gridmaxface(maxface)
    unique = 0
    do face = 1, maxface
      call gridface( face, id, globalnodes, nodeparts, uv, xyz )
      if (id == faceId .and. lmpi_id == nodeparts(1)) unique = unique + 1
    end do
    nfacelocal = unique
    call lmpi_reduce(nfacelocal, nface)
    call lmpi_bcast(nface)
    call my_alloc_ptr(localf2n,3,nfacelocal)
    call my_alloc_ptr(localf2n_xyz,9,nfacelocal)
    call my_alloc_ptr(localf2n_uv,6,nfacelocal)
    call my_alloc_ptr(f2n,3,nface)
    call my_alloc_ptr(f2n_xyz,9,nface)
    call my_alloc_ptr(f2n_uv,6,nface)
    unique = 0
    do face = 1, maxface
      call gridface( face, id, globalnodes, nodeparts, uv, xyz )
      if (id == faceId .and. lmpi_id == nodeparts(1)) then
        unique = unique + 1
        localf2n(:,unique) = globalnodes
        localf2n_xyz(1:3,unique) = xyz(:,1)
        localf2n_xyz(4:6,unique) = xyz(:,2)
        localf2n_xyz(7:9,unique) = xyz(:,3)
        localf2n_uv(1:2,unique) = uv(:,1)
        localf2n_uv(3:4,unique) = uv(:,2)
        localf2n_uv(5:6,unique) = uv(:,3)
      end if
    end do
    if (lmpi_nproc == 1) then
      f2n = localf2n
      f2n_xyz = localf2n_xyz
      f2n_uv = localf2n_uv
    else
      call my_alloc_ptr(nfaceproc,lmpi_nproc)
      call lmpi_allgather(nfacelocal,nfaceproc)
      call lmpi_allgatherv(localf2n, nfaceproc, f2n)
      call lmpi_allgatherv(localf2n_xyz, nfaceproc, f2n_xyz)
      call lmpi_allgatherv(localf2n_uv, nfaceproc, f2n_uv)
      deallocate(nfaceproc)
    end if
    deallocate(localf2n, localf2n_xyz, localf2n_uv)

  end subroutine gather_geometry_faces
#endif

!=========================== refineProjectAllFaces ===========================80
!=============================================================================80

  subroutine refineProjectAllFaces(  )
#ifdef HAVE_REFINE
    interface
      subroutine gridprojectallfaces()
      end subroutine gridprojectallfaces
    end interface

    continue

    call gridprojectallfaces()
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refineProjectAllFaces

!=========================== refineTestCADParameters =========================80
!=============================================================================80

  subroutine refineTestCADParameters(  )
#ifdef HAVE_REFINE
    interface
      subroutine gridtestcadparameters()
      end subroutine gridtestcadparameters
    end interface

    continue

    call gridtestcadparameters()
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refineTestCADParameters

!=========================== refineTecplotSurface ============================80
!=============================================================================80

  subroutine refineTecplotSurface( )
#ifdef HAVE_REFINE
    interface
      subroutine gridwritetecplotsurfacezone()
      end subroutine gridwritetecplotsurfacezone
    end interface

    continue

    call gridwritetecplotsurfacezone()
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refineTecplotSurface

!=========================== refineParallelAdapt =============================80
!=============================================================================80

  subroutine refineParallelAdapt( timer, minLength_arg, maxLength_arg )

    use kinddefs,        only : dp
    use lmpi,        only : lmpi_master
#ifdef HAVE_REFINE
    use lmpi,        only : lmpi_nproc, lmpi_id,                               &
                            lmpi_bcast, lmpi_reduce, lmpi_synchronize
    use refine_adaptation_input, only : adapt_load_balance
#endif

    logical,               intent(in) :: timer
    real(dp), optional,    intent(in) :: minLength_arg, maxLength_arg

#ifdef HAVE_REFINE
    real(dp)    :: minLength, maxLength
    integer     :: processor
    integer     :: sizeint, sizereal
    integer,     dimension(:), allocatable :: queueint
    real(dp), dimension(:), allocatable    :: queuereal
    integer     :: oldnnodeg
    integer     :: nnodeg, localUnused, totalUnused

    interface
      subroutine gridparalleladapt( processor, minLength, maxLength )
        integer, intent(in) :: processor
        real(selected_real_kind(15,307)), intent(in) :: minLength, maxLength
      end subroutine gridparalleladapt
      subroutine queueDumpSize( sizeint, sizereal )
        integer, intent(out) :: sizeint, sizereal
      end subroutine queueDumpSize
      subroutine queueDump( sizeint, sizereal, queueint, queuereal )
        integer, intent(in) :: sizeint, sizereal
        integer,     dimension(sizeint), intent(out) :: queueint
        real(selected_real_kind(15,307)), dimension(sizereal), intent(out) ::  &
              queuereal
      end subroutine queueDump
      subroutine gridApplyQueue( sizeint, sizereal, queueint, queuereal )
        integer, intent(in) :: sizeint, sizereal
        integer,     dimension(sizeint), intent(in) :: queueint
        real(selected_real_kind(15,307)), dimension(sizereal), intent(in) ::   &
              queuereal
      end subroutine gridApplyQueue
      subroutine gridGlobalNNode( nnodeg )
        integer, intent(out) :: nnodeg
      end subroutine gridGlobalNNode
      subroutine gridNUnusedNodeGlobal( nunused )
        integer, intent(out) :: nunused
      end subroutine gridNUnusedNodeGlobal
    end interface

    continue

    minLength = 0.3_dp
    maxLength = 1.8_dp
    if ( present(minLength_arg) ) minLength = minLength_arg
    if ( present(maxLength_arg) ) maxLength = maxLength_arg
    if (timer) call refineResetTimer()
    call gridGlobalNNode( oldnnodeg )
    call gridparalleladapt( -1, minLength, maxLength )
    call sync_global_numbering( lmpi_nproc, oldnnodeg )
    call refineUpdateGhostXYZ( lmpi_nproc )
    if (timer) call refineReportTimer(" adapt  interior")
    if (timer) call refineResetTimer()

    if ( adapt_load_balance ) then
      call gridGlobalNNode( nnodeg )
      call gridNUnusedNodeGlobal( localUnused )
      call lmpi_reduce( localUnused, totalUnused )
      if (lmpi_master)                                                         &
        write(*,'(a,i0)') " adapt global node count ",nnodeg-totalUnused
      return
    end if

    do processor = 0, lmpi_nproc-1
      call gridGlobalNNode( oldnnodeg )
      if (lmpi_id == processor) then
        call gridparalleladapt( processor, minLength, maxLength )
        call queueDumpSize(sizeint, sizereal)
      end if
! set nnodesg (and offset when interior edge is split for projection)
      call sync_global_numbering( lmpi_nproc, oldnnodeg )
      call lmpi_bcast(sizeint,processor)
      call lmpi_bcast(sizereal,processor)
      allocate(queueint(sizeint))
      allocate(queuereal(sizereal))
      if ( lmpi_id == processor ) then
        call queueDump(sizeint, sizereal, queueint, queuereal)
      end if
      call lmpi_bcast(queueint,processor)
      call lmpi_bcast(queuereal,processor)
      if ( lmpi_id /= processor ) then
        call gridApplyQueue(sizeint, sizereal, queueint, queuereal)
      end if
      deallocate(queueint)
      deallocate(queuereal)
    end do
    call lmpi_synchronize()
    if (timer) call refineReportTimer(" adapt  ghost   ")
    if (timer) call refineResetTimer()

    call gridGlobalNNode( nnodeg )
    call gridNUnusedNodeGlobal( localUnused )
    call lmpi_reduce( localUnused, totalUnused )
    if (lmpi_master)                                                         &
      write(*,'(a,i0)') " adapt global node count ",nnodeg-totalUnused
    call refineUpdateGhostXYZ( lmpi_nproc )
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',                       &
      timer
    if (present(minLength_arg) .and. .false.) write(*,*) minLength_arg
    if (present(maxLength_arg) .and. .false.) write(*,*) maxLength_arg
#endif
    end subroutine refineParallelAdapt

!=========================== refineParallelPreProject ========================80
!=============================================================================80

  subroutine refineParallelPreProject( timer )

#ifdef HAVE_REFINE
    use kinddefs,        only : dp
    use lmpi,        only : lmpi_nproc, lmpi_id,                               &
                            lmpi_bcast, lmpi_synchronize
#else
    use lmpi,        only : lmpi_master
#endif

    logical,               intent(in) :: timer

#ifdef HAVE_REFINE
    integer     :: processor
    integer     :: sizeint, sizereal
    integer,     dimension(:), allocatable :: queueint
    real(dp), dimension(:), allocatable    :: queuereal
    integer     :: oldnnodeg

    interface
      subroutine gridparallelpreproject( processor )
        integer, intent(in) :: processor
      end subroutine gridparallelpreproject
      subroutine queueDumpSize( sizeint, sizereal )
        integer, intent(out) :: sizeint, sizereal
      end subroutine queueDumpSize
      subroutine queueDump( sizeint, sizereal, queueint, queuereal )
        integer, intent(in) :: sizeint, sizereal
        integer,     dimension(sizeint), intent(out) :: queueint
        real(selected_real_kind(15,307)), dimension(sizereal), intent(out) ::  &
              queuereal
      end subroutine queueDump
      subroutine gridApplyQueue( sizeint, sizereal, queueint, queuereal )
        integer, intent(in) :: sizeint, sizereal
        integer,     dimension(sizeint), intent(in) :: queueint
        real(selected_real_kind(15,307)), dimension(sizereal), intent(in) ::   &
              queuereal
      end subroutine gridApplyQueue
      subroutine gridGlobalNNode( nnodeg )
        integer, intent(out) :: nnodeg
      end subroutine gridGlobalNNode
    end interface

    continue

    if (timer) call refineResetTimer()
    call gridGlobalNNode( oldnnodeg )
    call gridparallelpreproject( -1 )
    call sync_global_numbering( lmpi_nproc, oldnnodeg )
    call refineUpdateGhostXYZ( lmpi_nproc )
    if (timer) call refineReportTimer(" prepj  interior")
    if (timer) call refineResetTimer()
    do processor = 0, lmpi_nproc-1
      call gridGlobalNNode( oldnnodeg )
      if (lmpi_id == processor) then
        call gridparallelpreproject( processor )
        call queueDumpSize(sizeint, sizereal)
      end if
! set nnodesg (and offset when interior edge is split for projection)
      call sync_global_numbering( lmpi_nproc, oldnnodeg )
      call lmpi_bcast(sizeint,processor)
      call lmpi_bcast(sizereal,processor)
      allocate(queueint(sizeint))
      allocate(queuereal(sizereal))
      if ( lmpi_id == processor ) then
        call queueDump(sizeint, sizereal, queueint, queuereal)
      end if
      call lmpi_bcast(queueint,processor)
      call lmpi_bcast(queuereal,processor)
      if ( lmpi_id /= processor ) then
        call gridApplyQueue(sizeint, sizereal, queueint, queuereal)
      end if
      deallocate(queueint)
      deallocate(queuereal)
    end do
    call lmpi_synchronize()
    if (timer) call refineReportTimer(" prepj  ghost   ")
    if (timer) call refineResetTimer()

    call refineUpdateGhostXYZ( lmpi_nproc )
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',                       &
      timer
#endif
    end subroutine refineParallelPreProject

!=========================== refineParallelSwapInColor =======================80
!=============================================================================80

  subroutine refineParallelSwapInColor( nproc, timer, ARlimit )

    use kinddefs,        only : dp
#ifdef HAVE_REFINE
    use lmpi,        only : lmpi_id,                                          &
                            lmpi_bcast, lmpi_synchronize,                     &
                            lmpi_allgather
    use refine_adaptation_input, only : adapt_load_balance
#else
    use lmpi,        only : lmpi_master
#endif

    integer,               intent(in) :: nproc
    logical,               intent(in) :: timer
    real(dp), optional,    intent(in) :: ARlimit

#ifdef HAVE_REFINE
    integer     :: color, canidate, neighbor, processor
    integer, dimension(nproc)       :: ghostcount
    integer, dimension(nproc,nproc) :: messagesize
    logical, dimension(nproc)       :: color_member, completed
    real(dp)    :: limit
    integer     :: sizeint, sizereal
    integer,     dimension(:), allocatable :: queueint
    real(dp), dimension(:), allocatable    :: queuereal
    integer     :: oldnnodeg

    interface
      subroutine gridparallelswap( processor, ARlimit )
        integer, intent(in) :: processor
        real(selected_real_kind(15,307)), intent(in) :: ARlimit
      end subroutine gridparallelswap
      subroutine gridghostcount( nproc, nghost )
        integer,                   intent(in)  :: nproc
        integer, dimension(nproc), intent(out) :: nghost
      end subroutine gridghostcount
      subroutine queueDumpSize( sizeint, sizereal )
        integer, intent(out) :: sizeint, sizereal
      end subroutine queueDumpSize
      subroutine queueDump( sizeint, sizereal, queueint, queuereal )
        integer, intent(in) :: sizeint, sizereal
        integer,     dimension(sizeint), intent(out) :: queueint
        real(selected_real_kind(15,307)), dimension(sizereal), intent(out) ::  &
              queuereal
      end subroutine queueDump
      subroutine gridApplyQueue( sizeint, sizereal, queueint, queuereal )
        integer, intent(in) :: sizeint, sizereal
        integer,     dimension(sizeint), intent(in) :: queueint
        real(selected_real_kind(15,307)), dimension(sizereal), intent(in) ::   &
              queuereal
      end subroutine gridApplyQueue
      subroutine gridGlobalNNode( nnodeg )
        integer, intent(out) :: nnodeg
      end subroutine gridGlobalNNode
    end interface

    continue

    limit = 0.6_dp
    if (present(ARlimit)) limit = ARlimit
    if (timer) call refineResetTimer()
    call gridGlobalNNode( oldnnodeg )
    if ( adapt_load_balance ) then
      call gridparallelswap( -2, limit )
    else
      call gridparallelswap( -1, limit )
    end if
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" swap   interior in color")
    if (timer) call refineResetTimer()
    call sync_global_numbering( nproc, oldnnodeg )

    if ( adapt_load_balance ) then
      return
    end if

    completed = .false.
    color_loop : do color = 1, nproc

      call gridghostcount( nproc, ghostcount )
      call lmpi_allgather( ghostcount, messagesize )

      color_member = .false.
      potential_color_member : do canidate = 1, nproc
        needs_to_be_done : if ( .not. completed(canidate) ) then
          independence_from_members : do neighbor = 1, nproc
            if ( color_member(neighbor) .and.         &
                 messagesize(canidate,neighbor) > 0 ) &
                 cycle potential_color_member
          end do independence_from_members
          color_member(canidate) = .true.
        end if needs_to_be_done
      end do potential_color_member

      !if (lmpi_master) write(*,'(a,i4,40l2)') "  color", color, color_member

      call gridGlobalNNode( oldnnodeg )
      border_swap : if (color_member(lmpi_id+1)) then
        call gridparallelswap( lmpi_id, limit )
      end if border_swap
      call sync_global_numbering( nproc, oldnnodeg )

      queue_loop : do processor = 0, nproc-1
        active_queue : if (color_member(processor+1)) then
          if (lmpi_id == processor) then
            call queueDumpSize(sizeint, sizereal)
          end if
          call lmpi_bcast(sizeint,processor)
          call lmpi_bcast(sizereal,processor)
          allocate(queueint(sizeint))
          allocate(queuereal(sizereal))
          if ( lmpi_id == processor ) then
            call queueDump(sizeint, sizereal, queueint, queuereal)
          end if
          call lmpi_bcast(queueint,processor)
          call lmpi_bcast(queuereal,processor)
          if ( lmpi_id /= processor ) then
            call gridApplyQueue(sizeint, sizereal, queueint, queuereal)
          end if
          deallocate(queueint)
          deallocate(queuereal)
        end if active_queue
      end do queue_loop

      call refineUpdateGhostXYZ( nproc ) ! for freeze state

      completed = (completed .or. color_member)

    end do color_loop
    call lmpi_synchronize()
    if (timer) call refineReportTimer(" swap   ghosts   in color")
    if (timer) call refineResetTimer()
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE',                       &
      nproc, timer
    if (present(ARlimit) .and. .false. ) write(*,*) ARlimit
#endif
  end subroutine refineParallelSwapInColor

!=========================== refineParallelSmoothInColor =====================80
!=============================================================================80

  subroutine refineParallelSmoothInColor( nproc, timer, modifySurface )

#ifdef HAVE_REFINE
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_id, lmpi_allgather
    use refine_adaptation_input, only : adapt_load_balance
#else
    use lmpi,            only : lmpi_master
#endif

    integer, intent(in) :: nproc
    logical, intent(in) :: timer
    integer, intent(in) :: modifySurface

#ifdef HAVE_REFINE
    integer     :: color, canidate, neighbor
    integer, dimension(nproc)       :: ghostcount
    integer, dimension(nproc,nproc) :: messagesize
    logical, dimension(nproc)       :: color_member, completed
    real(dp)    :: optimizationLimit, laplacianLimit

    interface
      subroutine gridparallelsmooth( processor,                                &
                                     optimizationLimit, laplacianLimit,        &
                                     modifySurface )
        integer, intent(in) :: processor, modifySurface
        real(selected_real_kind(15,307)), intent(in) :: optimizationLimit
        real(selected_real_kind(15,307)), intent(in) :: laplacianLimit
      end subroutine gridparallelsmooth
    end interface

    continue

    optimizationLimit = 0.50_dp
    laplacianLimit    = 0.75_dp
    if (timer) call refineResetTimer()
! smooth partition interior
    call gridparallelsmooth( -1,                                               &
                             optimizationLimit, laplacianLimit,                &
                             modifySurface )
    call refineUpdateGhostXYZ( nproc )
    if (timer) call refineReportTimer(" smooth interior in color")

    if ( adapt_load_balance ) then
      return
    end if

    completed = .false.
    color_loop : do color = 1, nproc
      call gridghostcount( nproc, ghostcount )
      call lmpi_allgather( ghostcount, messagesize )

      color_member = .false.
      potential_color_member : do canidate = 1, nproc
        needs_to_be_done : if ( .not. completed(canidate) ) then
          independence_from_members : do neighbor = 1, nproc
            if ( color_member(neighbor) .and.         &
                 messagesize(canidate,neighbor) > 0 ) &
                 cycle potential_color_member
          end do independence_from_members
          color_member(canidate) = .true.
        end if needs_to_be_done
      end do potential_color_member

      !if (lmpi_master) write(*,'(a,i4,40l2)') "  color", color, color_member

      border_smooth : if (color_member(lmpi_id+1)) then
        call gridparallelsmooth( lmpi_id,                                      &
                                 optimizationLimit, laplacianLimit,            &
                                 modifySurface )
      end if border_smooth
      call refineUpdateGhostXYZ( nproc )

      completed = (completed .or. color_member)

    end do color_loop
    if (timer)call refineReportTimer(" smooth ghosts   in color")
    if (timer)call refineResetTimer()
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) nproc, timer, modifySurface
#endif
  end subroutine refineParallelSmoothInColor

!=========================== refineParallelRelaxNeg ==========================80
!=============================================================================80

  subroutine refineParallelRelaxNeg( timer, modifySurface )

#ifdef HAVE_REFINE
    use lmpi,        only : lmpi_id, lmpi_nproc
#else
    use lmpi,        only : lmpi_master
#endif

    logical, intent(in) :: timer
    integer, intent(in) :: modifySurface

#ifdef HAVE_REFINE
    integer :: processor

    interface
      subroutine gridparallelrelaxneg( processor, modifySurface )
        integer, intent(in) :: processor, modifySurface
      end subroutine gridparallelrelaxneg
    end interface

    continue

    if (timer) call refineResetTimer()
    ! processor = 1 allows only partion interior modifications.
    ! the interior of the pasrtions does not affect other partionions.
    ! Below loop will initially will modify partition interior, and then
    ! will modify its boundaries.  Note: loop executing on each processor.
    ! Will first modify interior and then wait until it is its turn
    ! ...refineUpdateGhostXYZ() will block
    do processor = -1, lmpi_nproc-1
      if ( ( lmpi_id == processor ) .or. ( -1 == processor ) )               &
        call gridparallelrelaxneg( processor, modifySurface )
      call refineUpdateGhostXYZ( lmpi_nproc )
      if (timer.and.(processor==-1)) call refineReportTimer(" rlxNeg interior")
    end do
    if (timer ) call refineReportTimer(" rlxNeg ghosts  ")
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) timer, modifySurface
#endif
  end subroutine refineParallelRelaxNeg

!=========================== refineParallelRelaxSurf =========================80
!=============================================================================80

  subroutine refineParallelRelaxSurf( timer )

#ifdef HAVE_REFINE
    use lmpi,        only : lmpi_id, lmpi_nproc
#else
    use lmpi,        only : lmpi_master
#endif

    logical, intent(in) :: timer

#ifdef HAVE_REFINE
    integer :: processor

    interface
      subroutine gridparallelrelaxsurf( processor )
        integer, intent(in) :: processor
      end subroutine gridparallelrelaxsurf
    end interface

    continue

    if (timer) call refineResetTimer()
    ! processor = 1 allows only partion interior modifications.
    ! the interior of the pasrtions does not affect other partionions.
    ! Below loop will initially will modify partition interior, and then
    ! will modify its boundaries.  Note: loop executing on each processor.
    ! Will first modify interior and then wait until it is its turn
    ! ...refineUpdateGhostXYZ() will block
    do processor = -1, lmpi_nproc-1
      if ( ( lmpi_id == processor ) .or. ( -1 == processor ) )               &
        call gridparallelrelaxsurf( processor )
      call refineUpdateGhostXYZ( lmpi_nproc )
      if (timer.and.(processor==-1)) call refineReportTimer(" rlxSrf interior")
    end do
    if (timer ) call refineReportTimer(" rlxSrf ghosts  ")
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) timer
#endif
  end subroutine refineParallelRelaxSurf

!=========================== refineParallelProjectGridMove ===================80
!=============================================================================80

  function refineParallelProjectGridMove( nproc, relaxationScheme,           &
                                          totalSteps, subIterations,         &
                                          timer)
    use kinddefs,        only : dp
    use lmpi,            only : lmpi_master
#ifdef HAVE_REFINE
    use lmpi,            only : lmpi_nproc, lmpi_id, lmpi_bcast,  &
                                lmpi_allgather, lmpi_reduce, lmpi_synchronize
    use lmpi_app,        only : lmpi_sendreceive
#endif

    integer, intent(in) :: nproc, relaxationScheme, totalSteps, subIterations
    logical, intent(in) :: timer
    real(dp) :: refineParallelProjectGridMove

#ifdef HAVE_REFINE
    integer :: processor
    integer, dimension(nproc)       :: ghostcount
    integer, dimension(nproc,nproc) :: messagesize
    integer :: clientid, serverid

    integer, dimension(nproc+1) :: clientindex
    integer, dimension(:),   allocatable :: clientlocal, clientglobal
    integer :: clientsize

    integer, dimension(nproc+1) :: serverindex
    integer, dimension(:),   allocatable :: serverglobal, serverlocal
    integer :: serversize

    integer :: ndim

    integer    :: step
    real(dp)   :: position
    integer    :: iteration
    real(dp)   :: residual, totalResidual
    integer    :: nnodeg
    real(dp), parameter    :: little_number = 1.0e-30
    real(dp)   :: firstResidual, lastResidual
    real(dp)   :: convergence
    logical    :: divergence

    interface
      subroutine gridghostcount( nproc, nghost )
        integer,                   intent(in)  :: nproc
        integer, dimension(nproc), intent(out) :: nghost
      end subroutine gridghostcount
      subroutine gridloadghostnodes( nproc, clientindex,                       &
                                     clientsize, localnode, globalnode )
        integer,                        intent(in)  :: nproc, clientsize
        integer, dimension(nproc+1),    intent(in)  :: clientindex
        integer, dimension(clientsize), intent(out) :: localnode, globalnode
      end subroutine gridloadghostnodes
      subroutine gridloadlocalnodes( nnode, globalnode, localnode )
        integer,                        intent(in)  :: nnode
        integer, dimension(nnode),      intent(in)  :: globalnode
        integer, dimension(nnode),      intent(out) :: localnode
      end subroutine gridloadlocalnodes
      subroutine gridmovesetprojectiondisp( )
      end subroutine gridmovesetprojectiondisp
      subroutine gridmovedataleadingdim( ndim )
        integer, intent(out) :: ndim
      end subroutine gridmovedataleadingdim
      subroutine gridmoveinitializempitest( )
      end subroutine gridmoveinitializempitest
      subroutine gridmovecompletempitest( )
      end subroutine gridmovecompletempitest
      subroutine gridmoverelaxstartup( relaxationScheme )
        integer, intent(in) :: relaxationScheme
      end subroutine gridmoverelaxstartup
      subroutine gridmoverelaxstartstep(position)
        real(selected_real_kind(15,307)), intent(in) :: position
      end subroutine gridmoverelaxstartstep
      subroutine gridmoverelaxsubiter(residual)
        real(selected_real_kind(15,307)), intent(out) :: residual
      end subroutine gridmoverelaxsubiter
      subroutine gridmoverelaxshutdown( )
      end subroutine gridmoverelaxshutdown
      subroutine gridmoveapplydisplacements( )
      end subroutine gridmoveapplydisplacements
      subroutine gridmovefree( )
      end subroutine gridmovefree
      subroutine gridGlobalNNode( nnodeg )
        integer, intent(out) :: nnodeg
      end subroutine gridGlobalNNode
    end interface
#endif
    continue
    refineParallelProjectGridMove = 1.0_dp
#ifdef HAVE_REFINE
    if (timer) call lmpi_synchronize()
    if (timer) call refineResetTimer()
    call gridghostcount( nproc, ghostcount )
    call lmpi_allgather( ghostcount, messagesize )
    clientid = lmpi_id + 1
    clientsize = sum(messagesize(:,clientid))
    clientindex(1) = 1
    do processor = 1, lmpi_nproc
      clientindex(processor+1) = clientindex(processor)                        &
                               + messagesize(processor,clientid)
    end do
    allocate(clientlocal(clientsize))
    allocate(clientglobal(clientsize))

    serverid = lmpi_id + 1
    serversize = sum(messagesize(serverid,:))
    serverindex(1) = 1
    do processor = 1, lmpi_nproc
      serverindex(processor+1) = serverindex(processor)                        &
                               + messagesize(serverid,processor)
    end do
    allocate(serverlocal(serversize))
    allocate(serverglobal(serversize))

    call gridloadghostnodes( nproc, clientindex,                               &
                             clientsize, clientlocal, clientglobal )
    call lmpi_sendreceive(clientglobal, clientindex, serverglobal, serverindex)
    call gridloadlocalnodes( serversize, serverglobal, serverlocal )
    deallocate(clientglobal)
    deallocate(serverglobal)
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" gridmv mpiSetUp")
    if (timer) call refineResetTimer()
    call gridmovesetprojectiondisp()
    call refineUpdateGhostXYZ( nproc ) ! to communicate cad parameters
    call gridmovedataleadingdim(ndim)
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" gridmv projDisp")
    if (timer) call refineResetTimer()
    call gridmoveinitializempitest()
    call refineUpdateGridMoveGhost( nproc, ndim,                               &
      serverlocal,  serverindex, clientlocal, clientindex)
    call gridmovecompletempitest()
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" gridmv mpiTest ")
    if (timer) call refineResetTimer()
    call gridmoverelaxstartup(relaxationScheme)
    divergence = .false.
    firstResidual = 1.0_dp
    projection_steps : do step = 1, totalSteps
      position = real(step,dp) / real(totalSteps,dp)
      call gridmoverelaxstartstep(position)
      call refineUpdateGridMoveGhost( nproc, ndim,                             &
        serverlocal,  serverindex, clientlocal, clientindex)
      do iteration = 1, subIterations
        call gridmoverelaxsubiter(residual)
        call refineUpdateGridMoveGhost( nproc, ndim,                           &
          serverlocal,  serverindex, clientlocal, clientindex)
        report : if ( 1==iteration .or. 0==mod(iteration,subIterations/5) ) then
          call lmpi_reduce(residual,totalResidual)
          if ( lmpi_master ) then
            call gridGlobalNNode( nnodeg )
            totalResidual = sqrt(totalResidual/real(nnodeg,dp))
            write(*,'(i4,i6," gridmove residual",e24.15)')                     &
              step, iteration, totalResidual
          end if
          call lmpi_bcast(totalResidual)
          lastResidual = totalResidual
          if ( 1==iteration ) firstResidual = totalResidual
          if ( firstResidual > little_number ) then
            convergence = lastResidual/firstResidual
          else
            convergence = lastResidual
          end if
          divergence = ( convergence > 10000.0_dp )
          if (divergence) exit projection_steps
        end if report
      end do
    end do projection_steps
    call gridmoverelaxshutdown()
    if ( .not. divergence ) then
      call gridmoveapplydisplacements()
    else
      if ( lmpi_master ) then
        write(*,'(" a gridmove divergence of",e10.1," detected.")') convergence
        write(*,*) "projection gridmove displacements are not applied."
      end if
    end if
    call gridmovefree( )
    deallocate(clientlocal)
    deallocate(serverlocal)
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" gridmv relax   ")
    if (timer) call refineResetTimer()
    call refineUpdateGhostXYZ( nproc )
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" gridmv update  ")
    if (timer) call refineResetTimer()
    refineParallelProjectGridMove = convergence
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) &
      nproc, relaxationScheme, totalSteps, subIterations, timer
#endif
  end function refineParallelProjectGridMove

!=========================== refineParallelProjectOnly =======================80
!=============================================================================80

  subroutine refineParallelProjectOnly( timer )

#ifdef HAVE_REFINE
    use lmpi,            only : lmpi_nproc, lmpi_synchronize
#else
    use lmpi,            only : lmpi_master
#endif

    logical, intent(in) :: timer

#ifdef HAVE_REFINE
    interface
      subroutine gridmovesetprojectiondisp( )
      end subroutine gridmovesetprojectiondisp
      subroutine gridmoveapplydisplacements( )
      end subroutine gridmoveapplydisplacements
      subroutine gridmovefree( )
      end subroutine gridmovefree
    end interface

    continue

    if (timer) call lmpi_synchronize()
    if (timer) call refineResetTimer()
    call gridmovesetprojectiondisp()
    call gridmoveapplydisplacements()
    call gridmovefree( )
    ! to communicate cad parameters and displacements
    call refineUpdateGhostXYZ( lmpi_nproc )
    if (timer) call lmpi_synchronize()
    if (timer) call refineReportTimer(" gridpj update  ")
    if (timer) call refineResetTimer()
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) timer
#endif
  end subroutine refineParallelProjectOnly

#ifdef HAVE_REFINE
!=========================== refineUpdateGridMoveGhost =======================80
!=============================================================================80

  subroutine refineUpdateGridMoveGhost( nproc, ndim,                           &
    servernode,  serverindex, clientnode, clientindex)

    use kinddefs,        only : dp
    use lmpi_app,        only : lmpi_sendreceive

    integer,                                    intent(in) :: nproc, ndim
    integer, dimension(nproc+1),                intent(in) :: serverindex
    integer, dimension(serverindex(nproc+1)-1), intent(in) :: servernode
    integer, dimension(nproc+1),                intent(in) :: clientindex
    integer, dimension(clientindex(nproc+1)-1), intent(in) :: clientnode

    integer :: serversize
    integer :: clientsize
    real(dp), dimension(ndim,serverindex(nproc+1)-1)    :: serverdata
    real(dp), dimension(ndim,clientindex(nproc+1)-1)    :: clientdata

    interface
      subroutine gridmoveloadlocalnodedata( ndim, nnode, nodes, data )
        integer,                        intent(in)  :: ndim, nnode
        integer, dimension(nnode),      intent(in)  :: nodes
        real(selected_real_kind(15,307)),dimension(ndim,nnode),intent(out)::data
      end subroutine gridmoveloadlocalnodedata
      subroutine gridmovesetlocalnodedata( ndim, nnode, nodes, data )
        integer,                        intent(in)  :: ndim, nnode
        integer, dimension(nnode),      intent(in)  :: nodes
        real(selected_real_kind(15,307)),dimension(ndim,nnode),intent(out)::data
      end subroutine gridmovesetlocalnodedata
    end interface

    continue

    serversize = serverindex(nproc+1)-1
    clientsize = clientindex(nproc+1)-1
    call gridmoveloadlocalnodedata( ndim, serversize, servernode, serverdata )
    call lmpi_sendreceive(serverdata, serverindex, clientdata, clientindex)
    call gridmovesetlocalnodedata( ndim, clientsize, clientnode, clientdata )

  end subroutine refineUpdateGridMoveGhost
#endif

#ifdef HAVE_REFINE
!=========================== refineUpdateGhostXYZ ============================80
!=============================================================================80

  subroutine refineUpdateGhostXYZ( nproc )

    use kinddefs,        only : dp
    use lmpi,            only : lmpi_nproc, lmpi_id, lmpi_allgather
    use lmpi_app,        only : lmpi_sendreceive

    integer, intent(in) :: nproc

    integer :: processor
    integer, dimension(nproc)       :: ghostcount
    integer, dimension(nproc,nproc) :: messagesize
    integer :: clientid, serverid

    integer, dimension(nproc+1) :: clientindex
    integer, dimension(:),   allocatable :: localnode, globalnode
    integer :: clientsize

    integer, dimension(nproc+1) :: serverindex
    integer, dimension(:),   allocatable :: servernode
    integer :: serversize

    integer :: ndim
    real(dp), dimension(:,:), allocatable    :: serverdata, clientdata
    interface
      subroutine gridghostcount( nproc, nghost )
        integer,                   intent(in)  :: nproc
        integer, dimension(nproc), intent(out) :: nghost
      end subroutine gridghostcount
      subroutine gridloadghostnodes( nproc, clientindex,                       &
                                     clientsize, localnode, globalnode )
        integer,                        intent(in)  :: nproc, clientsize
        integer, dimension(nproc+1),    intent(in)  :: clientindex
        integer, dimension(clientsize), intent(out) :: localnode, globalnode
      end subroutine gridloadghostnodes
      subroutine gridloadglobalnodedata( ndim, nnode, nodes, data )
        integer,                        intent(in)  :: ndim, nnode
        integer, dimension(nnode),      intent(in)  :: nodes
        real(selected_real_kind(15,307)),dimension(ndim,nnode),intent(out)::data
      end subroutine gridloadglobalnodedata
      subroutine gridsetlocalnodedata( ndim, nnode, nodes, data )
        integer,                        intent(in)  :: ndim, nnode
        integer, dimension(nnode),      intent(in)  :: nodes
        real(selected_real_kind(15,307)),dimension(ndim,nnode),intent(out)::data
      end subroutine gridsetlocalnodedata
    end interface

    continue

    call gridghostcount( nproc, ghostcount )
    call lmpi_allgather( ghostcount, messagesize )
    clientid = lmpi_id + 1
    clientsize = sum(messagesize(:,clientid))
    clientindex(1) = 1
    do processor = 1, lmpi_nproc
      clientindex(processor+1) = clientindex(processor)                        &
                               + messagesize(processor,clientid)
    end do
    allocate(localnode(clientsize))
    allocate(globalnode(clientsize))

    serverid = lmpi_id + 1
    serversize = sum(messagesize(serverid,:))
    serverindex(1) = 1
    do processor = 1, lmpi_nproc
      serverindex(processor+1) = serverindex(processor)                        &
                               + messagesize(serverid,processor)
    end do
    allocate(servernode(serversize))

    call gridloadghostnodes( nproc, clientindex,                               &
                             clientsize, localnode, globalnode )
    call lmpi_sendreceive(globalnode, clientindex, servernode, serverindex)
    ndim = 4
    allocate(serverdata(ndim,serversize))
    allocate(clientdata(ndim,clientsize))
    call gridloadglobalnodedata( ndim, serversize, servernode, serverdata )
    call lmpi_sendreceive(serverdata, serverindex, clientdata, clientindex)
    call gridsetlocalnodedata( ndim, clientsize, localnode, clientdata )
    deallocate(localnode, globalnode)
    deallocate(servernode)
    deallocate(serverdata, clientdata)

  end subroutine refineUpdateGhostXYZ
#endif

#ifdef HAVE_REFINE
!=========================== sync_global_numbering ===========================80
!=============================================================================80

  subroutine sync_global_numbering( nproc, oldnnodeg )


    use lmpi,            only : lmpi_nproc, lmpi_id, lmpi_allgather

    integer, intent(in) :: nproc, oldnnodeg

    integer :: i
    integer :: newnnodeg
    integer :: newnodes
    integer, dimension(nproc) :: nodecount, nodeoffset

    interface
      subroutine gridGlobalNNode( nnodeg )
        integer, intent(out) :: nnodeg
      end subroutine gridGlobalNNode
      subroutine gridGlobalShift( oldnnodeg, newnnodeg, nodeoffset )
        integer, intent(in) :: oldnnodeg, newnnodeg, nodeoffset
      end subroutine gridGlobalShift
    end interface

    continue

    call gridGlobalNNode( newnnodeg )
    newnodes = newnnodeg - oldnnodeg
    call lmpi_allgather(newnodes,nodecount)
    nodeoffset(1) = 0
    convert_count_to_offest : do i = 2, lmpi_nproc
      nodeoffset(i) = nodeoffset(i-1) + nodecount(i-1)
    end do convert_count_to_offest
    newnnodeg = oldnnodeg + sum(nodecount)
    call gridGlobalShift( oldnnodeg, newnnodeg, nodeoffset(lmpi_id+1) )

  end subroutine sync_global_numbering
#endif

#ifdef HAVE_REFINE
!=============================== allgatherv_unique ===========================80
!
! Given a local array of various sizes on each process, return an array on
! all processors with the sorted intersection.  Could one day be moved to
! lmpi if needed elsewhere.
!
! The returned array is allocated within the routine and returned.
!
!=============================================================================80

  subroutine allgatherv_unique(size_arrayin, arrayin, arrayout)

    use lmpi,     only : lmpi_nproc, lmpi_allgather, lmpi_allgatherv
    use kinddefs, only : system_i1

    integer,               intent(in) :: size_arrayin
    integer, dimension(:), intent(in) :: arrayin
    integer, dimension(:), pointer    :: arrayout

    integer :: i, icount, all_size, max_nodes

    integer,    dimension(:), allocatable :: sizes
    integer(system_i1), dimension(:), allocatable :: tag

    continue

    allocate(sizes(lmpi_nproc)); sizes = 0
    call lmpi_allgather(size_arrayin,sizes)
    !write(*,*)id,': sizes = ',sizes
    !write(*,*)id,': arrayin = ',arrayin

    all_size = sum(sizes)
    allocate(arrayout(all_size)); arrayout = 0

    call lmpi_allgatherv(arrayin, sizes, arrayout)
    !write(*,*)id,': arrayout = ',arrayout

    deallocate(sizes)

    max_nodes = maxval(arrayout)
    !write(*,*)id,': max_nodes = ',max_nodes

    allocate(tag(max_nodes)); tag = 0
    do i = 1,size(arrayout)
       tag(arrayout(i)) = 1
    end do
    deallocate(arrayout)

    icount = 0
    do i = 1,max_nodes
       if (tag(i)==1) icount = icount + 1
    end do
    !write(*,*)id,': icount = ',icount

    allocate(arrayout(icount)); arrayout = 0

    icount = 0
    do i = 1,max_nodes
       if (tag(i)==1) then
          icount = icount + 1
          arrayout(icount) = i
       end if
    end do

    deallocate(tag)

  end subroutine allgatherv_unique
#endif

#ifdef HAVE_REFINE
!=========================== eliminate_unused_globals ========================80
!=============================================================================80

  subroutine eliminate_unused_globals()

    use lmpi,        only : lmpi_synchronize

    integer :: nunused
    integer, dimension(:), allocatable :: unused
    integer, dimension(:), pointer     :: totalunused

    interface
      subroutine gridNUnusedNodeGlobal( nunused )
        integer, intent(out) :: nunused
      end subroutine gridNUnusedNodeGlobal
      subroutine gridGetUnusedNodeGlobal( nunused, unused )
        integer,                     intent(in)  :: nunused
        integer, dimension(nunused), intent(out) :: unused
      end subroutine gridGetUnusedNodeGlobal
      subroutine gridCopyUnusedNodeGlobal( nunused, unused )
        integer,                     intent(in)  :: nunused
        integer, dimension(nunused), intent(in)  :: unused
      end subroutine gridCopyUnusedNodeGlobal
      subroutine gridEliminateUnusedNodeGlobal( )
      end subroutine gridEliminateUnusedNodeGlobal
    end interface

    continue

    call refineResetTimer()
    call gridNUnusedNodeGlobal( nunused )
    allocate( unused(nunused) )
    call gridGetUnusedNodeGlobal(nunused, unused)
    call allgatherv_unique(nunused, unused, totalunused)
    deallocate( unused )
    call gridCopyUnusedNodeGlobal(size(totalunused), totalunused)
    deallocate( totalunused )

    call gridEliminateUnusedNodeGlobal(  )

    call lmpi_synchronize()
    call refineReportTimer(" eliminate unused")
    call refineResetTimer()

  end subroutine eliminate_unused_globals
#endif

!=========================== refineReportAR ==================================80
!=============================================================================80

  function refineReportAR( )

    use kinddefs,          only : dp
    use lmpi,              only : lmpi_master
#ifdef HAVE_REFINE
    use system_extensions, only : se_flush
    use lmpi,              only : lmpi_min, lmpi_bcast
#endif

    real(dp) :: refineReportAR

#ifdef HAVE_REFINE
    real(selected_real_kind(15,307)) :: aspectratio, minAR

    interface
      subroutine gridminar( aspectratio )
        real(selected_real_kind(15,307)), intent(out) :: aspectratio
      end subroutine gridminar
    end interface
#endif
    continue
    refineReportAR = -1.0_dp
#ifdef HAVE_REFINE
    call gridminar( aspectratio )
    call lmpi_min( aspectratio, minAR )
    call lmpi_bcast( minAR )
    refineReportAR = minAR
    if (lmpi_master) then
      write(*,'(" min aspect ratio",f11.8)') minAR
      call se_flush()
    end if
#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end function refineReportAR

!=========================== refineGetGrid ===================================80
!=============================================================================80

  subroutine refineGetGrid( grid, soln, sadj )

    use grid_types,           only : grid_type
    use solution_types,       only : soln_type
    use solution_adj,         only : sadj_type
#ifdef HAVE_REFINE
    use solution_types,       only : generic_gas
    use kinddefs,             only : system_i8
    use refine_interface,     only : refine_gridgetnodes                       &
                                   , refine_gridgetauxmatrix                   &
                                   , refine_gridgetauxmatrix3
    use info_depr,            only : ebv_tets
    use nml_nonlinear_solves, only : itime
    use grid_motion_helpers, only : need_grid_velocity
    use grid_helper,          only : create_test_g2l, create_part,             &
                                     grid_reset_lmpi_xfer,                     &
                                     grid_contiguous_imesh,                    &
                                     grid_cell_unique,                         &
                                     n2c_type, n2c_from_grid, deallocate_n2c
    use allocations,          only : my_alloc_ptr, my_realloc_ptr
    use lmpi,                 only : lmpi_id, lmpi_master,                     &
                                     lmpi_reduce, lmpi_bcast,                  &
                                     lmpi_conditional_stop
    use lmpi_app,             only : lmpi_xfer
    use parallel_embed,       only : embed_renumber_faces,                     &
                                     embed_find_boundary_cells,                &
                                     embed_add_adjoint_faces,                  &
                                     embed_test_boundary_cells,                &
                                     embed_make_edgeloc, embed_make_edge,      &
                                     embed_make_edgel2g, embed_share_edgel2g
    use grid_metrics,         only : compute_dual_metrics
    use nml_global,           only : moving_grid
    use bc_names,             only : need_distance_function
    use distance_function,    only : compute_distance_function
    use system_extensions,    only : se_flush
    use nml_overset_data,     only : overset_flag
    use element_defs,         only : nullify_elem, initialize_elem
    use bc_types,             only : nullify_bc
#else
    use lmpi,                 only : lmpi_master
#endif

    type(grid_type), intent(inout)           :: grid
    type(soln_type), intent(inout), optional :: soln
    type(sadj_type), intent(inout), optional :: sadj

#ifdef HAVE_REFINE
    integer :: i, ielem
    integer :: cell
    integer, dimension(:,:), allocatable :: f2n
    integer :: face, nunique
    integer(system_i8) :: nunique_i8, ncellg_i8
    type(n2c_type) :: n2c
    integer :: offset

    interface
      subroutine gridSortFUN3D( nnodes0, nnodes01, nnodesg )
        integer, intent(out) :: nnodes0, nnodes01, nnodesg
      end subroutine gridSortFUN3D
      subroutine gridGetNCell( node_per_cell, ncell )
        integer,               intent(in)  :: node_per_cell
        integer,               intent(out) :: ncell
      end subroutine gridGetNCell
      subroutine gridGetCell( node_per_cell, ncell, c2n )
        integer, intent(in)  :: node_per_cell, ncell
        integer, dimension(node_per_cell,ncell), intent(out) :: c2n
      end subroutine gridGetCell
      subroutine gridGetImesh( nnode, imesh )
        integer,                   intent(in)  :: nnode
        integer, dimension(nnode), intent(out) :: imesh
      end subroutine gridGetImesh
      subroutine gridGetBcSize( ibound, node_per_face, nface )
        integer, intent(in)  :: ibound, node_per_face
        integer, intent(out) :: nface
      end subroutine gridGetBcSize
      subroutine gridGetBC( ibound, node_per_face, nface, f2n )
        integer, intent(in)  :: ibound, node_per_face, nface
        integer, dimension(node_per_face,nface), intent(out) :: f2n
      end subroutine gridGetBC
    end interface

    continue

    call eliminate_unused_globals( )

    grid%partid = lmpi_id+1
    grid%project = "refine"

    if (lmpi_master) write(*,*) 'refineGetGrid motion=', trim(grid%grid_motion)

    call gridSortFUN3D( grid%nnodes0, grid%nnodes01, grid%nnodesg )
    if (lmpi_master)                                        &
      write(*,'(" The adapted grid has ",i0," nodesg")')    &
      grid%nnodesg
    grid%firstemptynode  = 0
    grid%firstemptynode0 = 0
    if (lmpi_master) write(*,*)"reload nodes..."
    call my_alloc_ptr(grid%l2g,      grid%nnodes01)
    call my_alloc_ptr(grid%x,        grid%nnodes01)
    call my_alloc_ptr(grid%y,        grid%nnodes01)
    call my_alloc_ptr(grid%z,        grid%nnodes01)
    call refine_gridgetnodes( grid%nnodes01, grid%l2g, grid%x, grid%y, grid%z )

    if (lmpi_master) write(*,*)"filling level01 local part vectors..."
    call create_test_g2l(grid)
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    if (overset_flag) then
      if (lmpi_master) write(*,*)"reload imesh..."
      call my_alloc_ptr(grid%imesh,grid%nnodes01)
      call gridGetImesh(grid%nnodes01, grid%imesh )
      ! note that iblank is wrong now, must call suggar+dirtlib to fix
      call my_alloc_ptr(grid%iblank,grid%nnodes01)
    end if

    have_soln : if (present(soln)) then
      if (lmpi_master) write(*,*)"reload solution..."
      offset = 0
! FIXME? sonl%neq* could be set by solution.set_up_neq
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
      call lmpi_reduce(soln%neq0, soln%dofg)
      call lmpi_bcast(soln%dofg)
      soln_eqn_set : if ( soln%eqn_set == generic_gas ) then
        call my_alloc_ptr( soln%q_dof, soln%n_tot, soln%neq01 )
        call refine_gridgetauxmatrix( soln%n_tot, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%n_tot
      else
        call my_alloc_ptr( soln%q_dof, soln%ndim, grid%nnodes01 )
        call refine_gridgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
          soln%q_dof )
        offset = offset + soln%ndim
        if (soln%n_turb>0) then
          call my_alloc_ptr( soln%turb, soln%n_turb, grid%nnodes01 )
          call refine_gridgetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
            soln%turb )
          offset = offset + soln%n_turb
        else
          call my_alloc_ptr( soln%turb, 1, 1 )
        end if
        if(abs(itime) > 0) then
          call my_alloc_ptr( soln%qatn, soln%ndim, grid%nnodes01 )
          call refine_gridgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call my_alloc_ptr( soln%turbatn, soln%n_turb, grid%nnodes01 )
            call refine_gridgetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn )
            offset = offset + soln%n_turb
          else
            call my_alloc_ptr( soln%turbatn, 1, 1 )
          end if
          call my_alloc_ptr( soln%qatn1, soln%ndim, grid%nnodes01 )
          call refine_gridgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
            soln%qatn1 )
          offset = offset + soln%ndim
          if (soln%n_turb>0) then
            call my_alloc_ptr( soln%turbatn1, soln%n_turb, grid%nnodes01 )
            call refine_gridgetauxmatrix( soln%n_turb, grid%nnodes01, offset, &
              soln%turbatn1 )
            offset = offset + soln%n_turb
          else
            call my_alloc_ptr( soln%turbatn1, 1, 1 )
          end if
          if(abs(itime) >= 3) then
            call my_alloc_ptr( soln%qatn2, soln%ndim, grid%nnodes01 )
            call refine_gridgetauxmatrix( soln%ndim, grid%nnodes01, offset, &
              soln%qatn2 )
            offset = offset + soln%ndim
            if (soln%n_turb>0) then
              call my_alloc_ptr( soln%turbatn2, soln%n_turb, grid%nnodes01 )
              call refine_gridgetauxmatrix( soln%n_turb, grid%nnodes01, offset,&
                soln%turbatn2 )
              offset = offset + soln%n_turb
            else
              call my_alloc_ptr( soln%turbatn2, 1, 1 )
            end if
          end if
        end if
      end if soln_eqn_set
      if (present(sadj)) then
        call my_alloc_ptr( sadj%rlam, soln%adim, grid%nnodes01, 1 )
        call refine_gridgetauxmatrix3( soln%adim, grid%nnodes01, offset, &
          sadj%rlam )
      end if
    end if have_soln

    grid%nelem = module_header_nelem
    allocate(grid%elem(grid%nelem))
    init_each_elem_group : do ielem = 1, grid%nelem
      call nullify_elem( grid%elem(ielem) )
      grid%elem(ielem)%type_cell = module_header_type_cell(ielem)
      call initialize_elem( grid%elem(ielem) )
    end do init_each_elem_group
    deallocate(module_header_type_cell)

    get_each_elem_group : do ielem = 1, grid%nelem
      call gridGetNCell( grid%elem(ielem)%node_per_cell, &
                         grid%elem(ielem)%ncell )
      call my_alloc_ptr(grid%elem(ielem)%c2n,           &
                        grid%elem(ielem)%node_per_cell, &
                        grid%elem(ielem)%ncell )
      call gridGetCell( grid%elem(ielem)%node_per_cell, &
                        grid%elem(ielem)%ncell,         &
                        grid%elem(ielem)%c2n)

      nunique_i8 = 0
      do cell = 1, grid%elem(ielem)%ncell
        if ( grid_cell_unique(grid, grid%elem(ielem)%c2n(:,cell) ) ) &
          nunique_i8 = nunique_i8 + 1
      end do
      call lmpi_reduce(nunique_i8, ncellg_i8)
      grid%elem(ielem)%ncellg = ncellg_i8
      call lmpi_bcast(grid%elem(ielem)%ncellg)

      if (lmpi_master)                                          &
        write(*,'(" element group ",i0," of ",a," has ",i0)')   &
        ielem, grid%elem(ielem)%type_cell, grid%elem(ielem)%ncellg
    end do get_each_elem_group

    if (lmpi_master) write(*,*)"get boundary faces, ", module_header_nbound
    grid%nbound = module_header_nbound
    allocate( grid%bc(grid%nbound) )
    get_ibc_from_module_header : do i = 1, grid%nbound
      call nullify_bc( grid%bc(i) )
      grid%bc(i)%ibc = module_header_ibc(i)
      grid%bc(i)%bc_family = module_header_bc_family(i)
    end do get_ibc_from_module_header
    deallocate(module_header_bc_family)
    deallocate(module_header_ibc)

    fill_bound : do i = 1, grid%nbound
!triangles
      call gridGetBcSize( i, 3, grid%bc(i)%nbfacet )
      call my_alloc_ptr(grid%bc(i)%f2ntb,   max(1,grid%bc(i)%nbfacet),5)
      call my_alloc_ptr(grid%bc(i)%face_bit,max(1,grid%bc(i)%nbfacet))
      bc_has_tris : if ( grid%bc(i)%nbfacet > 0 ) then
        allocate(f2n(3, grid%bc(i)%nbfacet))
        call gridGetBc( i, 3, grid%bc(i)%nbfacet, f2n )
        transpose_tri_index : do face = 1, grid%bc(i)%nbfacet
          grid%bc(i)%f2ntb(face,1) = f2n(1,face)
          grid%bc(i)%f2ntb(face,2) = f2n(2,face)
          grid%bc(i)%f2ntb(face,3) = f2n(3,face)
        end do transpose_tri_index
        deallocate( f2n )
      end if bc_has_tris
      nunique = 0
      do face = 1, grid%bc(i)%nbfacet
        if (grid%bc(i)%f2ntb(face,1) <= grid%nnodes0) then
          nunique = nunique + 1
          grid%bc(i)%face_bit(face) = 1
        else
          grid%bc(i)%face_bit(face) = 0
        end if
      end do
      call lmpi_reduce(nunique,grid%bc(i)%nbfacetg)
      call lmpi_bcast(grid%bc(i)%nbfacetg)
!quads
      call gridGetBcSize( i, 4, grid%bc(i)%nbfaceq )
      call my_alloc_ptr(grid%bc(i)%f2nqb,    max(1,grid%bc(i)%nbfaceq),6)
      call my_alloc_ptr(grid%bc(i)%face_bitq,max(1,grid%bc(i)%nbfaceq))
      bc_has_quads : if ( grid%bc(i)%nbfaceq > 0 ) then
        allocate(f2n(4, grid%bc(i)%nbfaceq))
        call gridGetBc( i, 4, grid%bc(i)%nbfaceq, f2n )
        transpose_quad_index : do face = 1, grid%bc(i)%nbfaceq
          grid%bc(i)%f2nqb(face,1) = f2n(1,face)
          grid%bc(i)%f2nqb(face,2) = f2n(2,face)
          grid%bc(i)%f2nqb(face,3) = f2n(3,face)
          grid%bc(i)%f2nqb(face,4) = f2n(4,face)
        end do transpose_quad_index
        deallocate( f2n )
      end if bc_has_quads
      nunique = 0
      do face = 1, grid%bc(i)%nbfaceq
        if (grid%bc(i)%f2nqb(face,1) <= grid%nnodes0) then
          nunique = nunique + 1
          grid%bc(i)%face_bitq(face) = 1
        else
          grid%bc(i)%face_bitq(face) = 0
        end if
      end do
      call lmpi_reduce(nunique,grid%bc(i)%nbfaceqg)
      call lmpi_bcast(grid%bc(i)%nbfaceqg)
    end do fill_bound

    deallocate(grid%sortedglobal) ; grid%sortedglobal => null()
    deallocate(grid%sortedlocal)  ; grid%sortedlocal  => null()
    call create_test_g2l(grid)

    if (lmpi_master) write(*,*)"set up node lmpi_xfer send and rec..."
    call grid_reset_lmpi_xfer (grid)

    if (lmpi_master) write(*,*)"construct node 2 cell (n2c) structure..."
    call n2c_from_grid(grid, n2c)

    if (lmpi_master) write(*,*)"find boundary cells..."
    call embed_find_boundary_cells (grid, n2c)
    if (lmpi_master) write(*,*)"find phantom adjoint boundary faces..."
    call embed_add_adjoint_faces(grid, n2c)
    if (lmpi_master) write(*,*)"renumber boundary nodes..."
    call embed_renumber_faces(grid)
    if (lmpi_master) write(*,*)"test boundary cells..."
    call embed_test_boundary_cells(grid)

    if (lmpi_master) write(*,*)"creating loc edges..."
    call embed_make_edgeloc (grid, n2c)
    if (lmpi_master) write(*,*)"creating remaining edges..."
    call embed_make_edge (grid, n2c)

    if (lmpi_master) write(*,*)"freeing node 2 cell (n2c) array..."
    call deallocate_n2c(n2c)

    if (lmpi_master) &
      write(*,*)"creating unique edge local to global numbering..."
    call my_alloc_ptr(grid%el2g,grid%nedge)
    call embed_make_edgel2g(2, grid%nedge, grid%eptr, &
      grid%nnodes0, grid%nnodes01, grid%l2g,          &
      grid%nedgeg, grid%el2g)

    if (lmpi_master) write(*,*)"share ghost edge local to global numbering..."
    call embed_share_edgel2g(2, grid%nedgeloc, grid%eptr, grid%el2g, &
      grid, .true.)

    if (lmpi_master) write(*,*)"deallocate part and g2l helper arrays..."
    deallocate(grid%part)         ; grid%part         => null()
    deallocate(grid%sortedglobal) ; grid%sortedglobal => null()
    deallocate(grid%sortedlocal)  ; grid%sortedlocal  => null()

    if (overset_flag) call grid_contiguous_imesh(grid)

    if (lmpi_master) write(*,*)"populate ghost xyzs..."
    call lmpi_xfer(grid%x)
    call lmpi_xfer(grid%y)
    call lmpi_xfer(grid%z)

    resize_soln : if ( present(soln) ) then
      if (lmpi_master) write(*,*)"populate ghost solution..."
      resize_soln_equ_set : if ( soln%eqn_set== generic_gas ) then
        call my_realloc_ptr( soln%q_dof, soln%n_tot, grid%nnodes01 )
        call lmpi_xfer( soln%q_dof )
      else
        call my_realloc_ptr( soln%q_dof, soln%ndim, grid%nnodes01 )
        call lmpi_xfer( soln%q_dof )
        if (soln%n_turb>0) then
          call my_realloc_ptr( soln%turb, soln%n_turb, grid%nnodes01 )
          call lmpi_xfer( soln%turb )
        end if
        if (abs(itime) > 0) then
          call my_realloc_ptr( soln%qatn, soln%ndim, grid%nnodes01 )
          call lmpi_xfer( soln%qatn)
          if (soln%n_turb>0) then
            call my_realloc_ptr( soln%turbatn, soln%n_turb, grid%nnodes01 )
            call lmpi_xfer( soln%turbatn )
          end if
          call my_realloc_ptr( soln%qatn1, soln%ndim, grid%nnodes01 )
          call lmpi_xfer( soln%qatn1)
          if (soln%n_turb>0) then
            call my_realloc_ptr( soln%turbatn1, soln%n_turb, grid%nnodes01 )
            call lmpi_xfer( soln%turbatn1 )
          end if
          if (abs(itime) >= 3) then
            call my_realloc_ptr( soln%qatn2, soln%ndim, grid%nnodes01 )
            call lmpi_xfer( soln%qatn2)
            if (soln%n_turb>0) then
              call my_realloc_ptr( soln%turbatn2, soln%n_turb, grid%nnodes01 )
              call lmpi_xfer( soln%turbatn2 )
            end if
          end if
        end if
      end if resize_soln_equ_set
      resize_sadj : if (present(sadj)) then
        call my_realloc_ptr( sadj%rlam, soln%adim, grid%nnodes01, 1 )
        call lmpi_xfer( sadj%rlam(:,:,1) )
      end if resize_sadj

      if (lmpi_master) write(*,*)"size soln neq scalars..."
      ! FIXME? sonl%neq* could be set by solution.set_up_neq
      soln%neq0 =  grid%nnodes0
      soln%neq01=  grid%nnodes01
      call lmpi_reduce(soln%neq0, soln%dofg)
      call lmpi_bcast(soln%dofg)

    end if resize_soln

    if (lmpi_master) write(*,*)"allocate grid reals..."
    call my_alloc_ptr(grid%vol, grid%nnodes01)

    if (need_grid_velocity) then
      call my_alloc_ptr(grid%dxdt,      grid%nnodes01)
      call my_alloc_ptr(grid%dydt,      grid%nnodes01)
      call my_alloc_ptr(grid%dzdt,      grid%nnodes01)
      call my_alloc_ptr(grid%facespeed, grid%nedge)
    else
      call my_alloc_ptr(grid%dxdt,      1)
      call my_alloc_ptr(grid%dydt,      1)
      call my_alloc_ptr(grid%dzdt,      1)
      call my_alloc_ptr(grid%facespeed, 1)
    end if
    call my_alloc_ptr(grid%res_gcl,   1, 1)

    call my_alloc_ptr(grid%xn, grid%nedge)
    call my_alloc_ptr(grid%yn, grid%nedge)
    call my_alloc_ptr(grid%zn, grid%nedge)
    call my_alloc_ptr(grid%ra, grid%nedge)

    if ( ebv_tets ) then
      call my_alloc_ptr(grid%weight,10,grid%nedge)
    else
      call my_alloc_ptr(grid%weight,1,1)
    endif

    call my_alloc_ptr(grid%r11,       grid%nnodes01)
    call my_alloc_ptr(grid%r12,       grid%nnodes01)
    call my_alloc_ptr(grid%r13,       grid%nnodes01)
    call my_alloc_ptr(grid%r22,       grid%nnodes01)
    call my_alloc_ptr(grid%r23,       grid%nnodes01)
    call my_alloc_ptr(grid%r33,       grid%nnodes01)
    call my_alloc_ptr(grid%symmetry,  grid%nnodes01)

    do i = 1, grid%nbound
      call my_alloc_ptr(grid%bc(i)%bxn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%byn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%bzn, grid%bc(i)%nbnode)
      call my_alloc_ptr(grid%bc(i)%slen_wall, grid%bc(i)%nbnode)
      if (need_grid_velocity) then
        call my_alloc_ptr( grid%bc(i)%bdxdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdydt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      max0(grid%bc(i)%nbnode,1) )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, max0(grid%bc(i)%nbnode,1) )
      else
        call my_alloc_ptr( grid%bc(i)%bdxdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdydt,      1 )
        call my_alloc_ptr( grid%bc(i)%bdzdt,      1 )
        call my_alloc_ptr( grid%bc(i)%bfacespeed, 1 )
      end if

    end do

! call PHYSICS_DEPS/grid_motion.f90:move_grid to get node/boundary speeds

    back_plane_volume : if ( trim(grid%grid_motion) /= 'static' .and. &
                             abs(itime) > 0 ) then
        call my_alloc_ptr( grid%xat0, grid%nnodes01 )
        call my_alloc_ptr( grid%yat0, grid%nnodes01 )
        call my_alloc_ptr( grid%zat0, grid%nnodes01 )
        grid%xat0 = grid%x
        grid%yat0 = grid%y
        grid%zat0 = grid%z

        call my_alloc_ptr( grid%xatn, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn1, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn2, grid%nnodes01 )
        call my_alloc_ptr( grid%xatn3, grid%nnodes01 )
        call my_alloc_ptr( grid%yatn3, grid%nnodes01 )
        call my_alloc_ptr( grid%zatn3, grid%nnodes01 )

        call my_alloc_ptr(grid%volatn, grid%nnodes01)
        call my_alloc_ptr(grid%volatn1, grid%nnodes01)
        call my_alloc_ptr(grid%volatn2, grid%nnodes01)
        call my_alloc_ptr(grid%volatn3, grid%nnodes01)

    end if back_plane_volume

    if (lmpi_master) write(*,*)"make inviscid metrics..."
    call compute_dual_metrics(grid, moving_grid)

    call my_alloc_ptr(grid%slen,grid%nnodes01)
    call my_alloc_ptr(grid%iflagslen,grid%nnodes01)
    call my_alloc_ptr(grid%des_slen,grid%nnodes01)

    compute_dist_fcn : if (need_distance_function(grid%bc)) then
      if (lmpi_master) write(*,*)"computing distance function..."
      grid%idistfcn = 1
      call compute_distance_function(grid,.true.)
      call lmpi_xfer(grid%slen)
      call lmpi_xfer(grid%iflagslen)
    else
      if (lmpi_master) write(*,*)"skip distance function..."
      grid%idistfcn = 0
    end if compute_dist_fcn

    if (lmpi_master) then
      write(*,*)"distance function complete."
      call se_flush
    endif

    if (lmpi_master) then
      write(*,*)"refineGetGrid complete."
      call se_flush
    end if

#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) grid%nnodes0
    if (present(soln) .and. .false.) write(*,*) soln%eqn_set
    if (present(sadj) .and. .false.) write(*,*) sadj%rlam(1,1,1)
#endif

  end subroutine refineGetGrid

!=========================== refineFree ======================================80
!=============================================================================80

  subroutine refineFree( )
#ifdef HAVE_REFINE
    interface
      subroutine gridfree()
      end subroutine gridfree
    end interface

    continue

    call gridfree()
#else
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
#endif
  end subroutine refineFree

!=========================== refineFree ======================================80
!=============================================================================80

  subroutine refineLoadBalance( grid, timer )
    use grid_types,          only : grid_type
    use lmpi,                only : lmpi_master
#ifdef HAVE_REFINE
    use kinddefs,            only : system_i8
    use lmpi,                only : lmpi_reduce, lmpi_bcast, &
                                    lmpi_nproc, lmpi_id
    use load_balance, only : migrator_type, migrator_from_grid, &
      migrate_l2g, migrate_elem, migrate_stencil, migrate_node, migrate_verify
    use allocations,         only : my_alloc_ptr
    use refine_interface,    only : refine_gridgetnodes
    use grid_helper,         only : create_test_g2l
    use grid_helper,         only : create_part, grid_cell_unique
    use nml_overset_data,    only : overset_flag
    use refine_interface,    only : refine_gridgetauxmatrix  &
                                  , refine_gridsetmap        &
                                  , refine_gridsetauxmatrix  &
                                  , refine_gridcreate
    use element_defs,        only : nullify_elem, &
      initialize_elem, deallocate_elem
    interface
      subroutine gridSortFUN3D( nnodes0, nnodes01, nnodesg )
        integer, intent(out) :: nnodes0, nnodes01, nnodesg
      end subroutine gridSortFUN3D
      subroutine gridGetNCell( node_per_cell, ncell )
        integer,               intent(in)  :: node_per_cell
        integer,               intent(out) :: ncell
      end subroutine gridGetNCell
      subroutine gridGetCell( node_per_cell, ncell, c2n )
        integer, intent(in)  :: node_per_cell, ncell
        integer, dimension(node_per_cell,ncell), intent(out) :: c2n
      end subroutine gridGetCell
      subroutine gridGetBcSize( ibound, node_per_face, nface )
        integer, intent(in)  :: ibound, node_per_face
        integer, intent(out) :: nface
      end subroutine gridGetBcSize
      subroutine gridGetBC( ibound, node_per_face, nface, f2n )
        integer, intent(in)  :: ibound, node_per_face, nface
        integer, dimension(node_per_face,nface), intent(out) :: f2n
      end subroutine gridGetBC
      subroutine gridGetMap( nnodes01, anisotropic_metric )
        integer, intent(in) :: nnodes01
        real(selected_real_kind(15,307)),dimension(6,nnodes01),intent(out):: &
          anisotropic_metric
      end subroutine gridGetMap
      subroutine gridGetFreezeState( nnode, freeze_state )
        integer, intent(in)  :: nnode
        integer, dimension(nnode), intent(out) :: freeze_state
      end subroutine gridGetFreezeState
      subroutine gridGetImesh( nnode, imesh )
        integer,                   intent(in)  :: nnode
        integer, dimension(nnode), intent(out) :: imesh
      end subroutine gridGetImesh
      subroutine gridGetNAux( naux )
        integer, intent(out)  :: naux
      end subroutine gridGetNAux
      subroutine gridsetnodelocal2global( partid, nnodeg, nnode, nnode0,       &
                                          local2global )
        integer, intent(in) :: partid, nnodeg, nnode, nnode0
        integer, dimension(nnode), intent(in) :: local2global
      end subroutine gridsetnodelocal2global
      subroutine gridsetnodepart( nnode, part )
        integer, intent(in) :: nnode
        integer, dimension(nnode), intent(in) :: part
      end subroutine gridsetnodepart
      subroutine gridfreezenode( node )
        integer, intent(in) :: node
      end subroutine gridfreezenode
      subroutine gridinsertcells( node_per_cell, ncell, c2n )
        integer, intent(in) :: node_per_cell, ncell
        integer, dimension(node_per_cell, ncell), intent(in) :: c2n
      end subroutine gridinsertcells
      subroutine gridinsertbc( ibound, node_per_face, nface, f2n )
        integer, intent(in) :: ibound, node_per_face, nface
        integer, dimension(node_per_face, nface), intent(in) :: f2n
      end subroutine gridinsertbc
      subroutine gridsetimesh( nnode, imesh )
        integer, intent(in) :: nnode
        integer, dimension(nnode), intent(in) :: imesh
      end subroutine gridsetimesh
      subroutine gridsetnaux( naux )
        integer, intent(in) :: naux
      end subroutine gridsetnaux
    end interface
#endif

    type(grid_type), intent(inout)           :: grid
    logical,               intent(in) :: timer

#ifdef HAVE_REFINE
    type(migrator_type) :: migrator

    integer :: elem, cell, node
    integer(system_i8) :: nunique_i8, ncellg_i8

    integer :: bound
    type migrating_bc_type
      integer :: nface
      integer, dimension(:,:), pointer :: f2n
    end type migrating_bc_type
    type(migrating_bc_type), dimension(:), allocatable :: mtri
    type(migrating_bc_type), dimension(:), allocatable :: mquad

    real(dp), dimension(:,:), pointer :: anisotropic_metric
    integer, dimension(:), pointer :: freeze_state

    integer :: naux
    real(dp), dimension(:,:), pointer :: aux

    logical :: verbose

    continue

    if (timer) call refineResetTimer()

    verbose = .false.
    verbose = ( verbose .and. lmpi_master ) ! only master can be verbose

    if ( 1 == lmpi_nproc ) then
      if ( verbose ) write(*,*) 'rlb skip for 1 proc'
      return
    end if

    if ( verbose ) write(*,*) 'rlb prepare refine'

    call eliminate_unused_globals( )
    call gridSortFUN3D( grid%nnodes0, grid%nnodes01, grid%nnodesg )

    if ( verbose ) write(*,*) 'rlb get nodes'

    call my_alloc_ptr(grid%l2g,      grid%nnodes01)
    call my_alloc_ptr(grid%x,        grid%nnodes01)
    call my_alloc_ptr(grid%y,        grid%nnodes01)
    call my_alloc_ptr(grid%z,        grid%nnodes01)
    call refine_gridgetnodes( grid%nnodes01, grid%l2g, grid%x, grid%y, grid%z )

    if ( verbose ) write(*,*) 'rlb make part'

    call create_test_g2l(grid)
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    grid%nelem = module_header_nelem
    allocate(grid%elem(grid%nelem))
    init_each_elem_group : do elem = 1, grid%nelem
      call nullify_elem( grid%elem(elem) )
      grid%elem(elem)%type_cell = module_header_type_cell(elem)
      call initialize_elem( grid%elem(elem) )
    end do init_each_elem_group
    deallocate(module_header_type_cell)

    get_each_elem_group : do elem = 1, grid%nelem
      call gridGetNCell( grid%elem(elem)%node_per_cell, &
                         grid%elem(elem)%ncell )
      call my_alloc_ptr(grid%elem(elem)%c2n,           &
                        grid%elem(elem)%node_per_cell, &
                        grid%elem(elem)%ncell )
      call gridGetCell( grid%elem(elem)%node_per_cell, &
                        grid%elem(elem)%ncell,         &
                        grid%elem(elem)%c2n)

      nunique_i8 = 0
      do cell = 1, grid%elem(elem)%ncell
        if ( grid_cell_unique(grid, grid%elem(elem)%c2n(:,cell) ) ) &
          nunique_i8 = nunique_i8 + 1
      end do
      call lmpi_reduce(nunique_i8, ncellg_i8)
      grid%elem(elem)%ncellg = ncellg_i8
      call lmpi_bcast(grid%elem(elem)%ncellg)

      if (verbose)                                                  &
        write(*,'(" rlb element group ",i0," of ",a," has ",i0)')   &
        elem, grid%elem(elem)%type_cell, grid%elem(elem)%ncellg
    end do get_each_elem_group

    if ( verbose ) write(*,*) 'rlb migrator'
    call migrator_from_grid( migrator, &
      grid%nnodes0, grid%nnodes01, grid%part, grid%l2g, grid )

    if ( verbose ) write(*,*) 'rlb migrate l2g'
    call migrate_l2g( migrator )

    migrate_each_elem_group : do elem = 1, grid%nelem
      if ( verbose ) write(*,*) 'rlb migrate elem',elem
      call migrate_elem( migrator, grid%elem(elem)%node_per_cell, &
        grid%elem(elem)%ncell, grid%elem(elem)%c2n )
    end do migrate_each_elem_group

    if ( verbose ) write(*,*) 'rlb migrate bcs'

    allocate(mtri(grid%nbound))
    allocate(mquad(grid%nbound))
    migrate_bound : do bound = 1, grid%nbound
      call gridGetBcSize( bound, 3, mtri(bound)%nface )
      allocate(mtri(bound)%f2n(3, mtri(bound)%nface))
      call gridGetBc( bound, 3, mtri(bound)%nface, mtri(bound)%f2n )
      call migrate_elem( migrator, 3, &
        mtri(bound)%nface, mtri(bound)%f2n )
      call gridGetBcSize( bound, 4, mquad(bound)%nface )
      allocate(mquad(bound)%f2n(4, mquad(bound)%nface))
      call gridGetBc( bound, 4, mquad(bound)%nface, mquad(bound)%f2n )
      call migrate_elem( migrator, 4, &
        mquad(bound)%nface, mquad(bound)%f2n )
    end do migrate_bound

    if ( verbose ) write(*,*) 'rlb migrate node stencil'
    call migrate_stencil( migrator )

    if ( verbose ) write(*,*) 'rlb migrate l2g'
    call migrate_node( migrator, grid%l2g )

    if ( verbose ) write(*,*) 'rlb migrate verify l2g'
    call migrate_verify( migrator, grid%l2g )

    if ( verbose ) write(*,*) 'rlb migrate xyz'
    call migrate_node( migrator, grid%x )
    call migrate_node( migrator, grid%y )
    call migrate_node( migrator, grid%z )

    if ( verbose ) write(*,*) 'rlb migrate map (anisotropic_metric)'
    allocate( anisotropic_metric(6,grid%nnodes01) )
    call gridGetMap(grid%nnodes01, anisotropic_metric)
    call migrate_node( migrator, anisotropic_metric )

    if ( verbose ) write(*,*) 'rlb migrate freeze'
    call my_alloc_ptr( freeze_state, grid%nnodes01 )
    call gridGetFreezeState(grid%nnodes01, freeze_state)
    call migrate_node( migrator, freeze_state )

    if (overset_flag) then
      if ( verbose ) write(*,*) 'rlb migrate imesh'
      call my_alloc_ptr(grid%imesh,grid%nnodes01)
      call gridGetImesh(grid%nnodes01, grid%imesh )
      call migrate_node( migrator, grid%imesh )
    else
        if ( verbose ) write(*,*) 'rlb skip imesh'
    end if

    call gridGetNAUX( naux )
    if ( verbose ) write(*,*) 'rlb naux',naux
    if ( naux > 0 ) then
      call my_alloc_ptr(aux,naux,grid%nnodes01)
      call refine_gridgetauxmatrix( naux, grid%nnodes01, 0, aux )
      call migrate_node( migrator, aux )
    end if

    if ( verbose ) write(*,*) 'rlb free refine'
    call refineFree( )

    grid%nnodes0 = migrator%new_nnodes0
    grid%nnodes01 = migrator%new_nnodes01
    if ( associated(grid%part) ) deallocate(grid%part)
    allocate(grid%part(grid%nnodes01))
    call create_part(grid%nnodes0, grid%nnodes01, grid%l2g, grid%part)

    call refine_gridcreate( lmpi_id, grid%nnodes01, grid%x, grid%y, grid%z )
    call gridsetnodelocal2global( lmpi_id, grid%nnodesg,                     &
                                 grid%nnodes01, grid%nnodes0, grid%l2g )
    call gridsetnodepart( grid%nnodes01, grid%part )

    refreeze_nodes : do node = 1, grid%nnodes01
      if ( 1 == freeze_state(node) ) then
        call gridfreezenode( node )
      end if
    end do refreeze_nodes

    call refine_gridsetmap( grid%nnodes01, anisotropic_metric )

    module_header_nelem = grid%nelem
    allocate(module_header_type_cell(module_header_nelem))
    each_element_group :do elem = 1, grid%nelem
      module_header_type_cell(elem) = grid%elem(elem)%type_cell
      call gridinsertcells( grid%elem(elem)%node_per_cell,                    &
                            grid%elem(elem)%ncell, grid%elem(elem)%c2n )
      if (lmpi_master)                                          &
        write(*,'(" element group ",i0," of ",a," has ",i0)')   &
        elem, grid%elem(elem)%type_cell, grid%elem(elem)%ncellg
      call deallocate_elem(  grid%elem(elem) )
    end do each_element_group
    deallocate( grid%elem ) ; grid%elem => null()

    do bound = 1, grid%nbound
      bound_has_tris : if ( mtri(bound)%nface > 0 ) then
        call gridinsertbc( bound, 3, &
          mtri(bound)%nface, mtri(bound)%f2n )
      end if bound_has_tris
      bound_has_quads : if ( mquad(bound)%nface > 0 ) then
        call gridinsertbc( bound, 4, &
          mquad(bound)%nface, mquad(bound)%f2n )
      end if bound_has_quads
    end do

    imesh_for_overset : if (overset_flag) then
      call gridsetimesh( grid%nnodes01, grid%imesh )
    end if imesh_for_overset

    call gridsetnaux( naux )

    if ( naux > 0 ) then
      call refine_gridsetauxmatrix( naux, grid%nnodes01, 0, aux )
    end if

    deallocate( grid%x, grid%y, grid%z )
    nullify(    grid%x, grid%y, grid%z )
    deallocate( grid%l2g, grid%part )
    nullify(    grid%l2g, grid%part )
    deallocate( freeze_state )
    nullify(    freeze_state )
    deallocate( anisotropic_metric )
    nullify(    anisotropic_metric )

    dealloc_bound : do bound = 1, grid%nbound
      deallocate( mtri(bound)%f2n )
      nullify(    mtri(bound)%f2n )
      deallocate( mquad(bound)%f2n )
      nullify(    mquad(bound)%f2n )
    end do dealloc_bound

    dealloc_imesh_if_overset : if (overset_flag) then
      deallocate( grid%imesh )
      nullify(    grid%imesh )
    end if dealloc_imesh_if_overset

    if ( naux > 0 ) then
      deallocate( aux )
      nullify(    aux )
    end if

    call refineProjectAllFaces(  )

    if (timer) call refineReportTimer(" refine load balance     ")
    if (timer) call refineResetTimer()

#else
    if (lmpi_master)                                                           &
    write(*,*) 'Error! not compiled with -DHAVE_REFINE'
    if (.false.) write(*,*) grid%nnodes0
    if (.false.) write(*,*) timer
#endif

  end subroutine refineLoadBalance

end module refine_grid_adapter

module chaos_datas

  use kinddefs, only : dp

  implicit none

  private

  integer, public :: ntimesteps, nnodes, timestep, nparts

  integer, public :: m0          ! Starting timestep of obj definition
  integer, public :: m1          ! Ending timestep of obj definition

  integer, dimension(:), pointer, public :: ia  => null()
  integer, dimension(:), pointer, public :: ja  => null()
  integer, dimension(:), pointer, public :: iau => null()

  real(dp), public :: omega      ! Weight in FUN3D obj function
  real(dp), public :: power      ! Power in FUN3D obj function
  real(dp), public :: dt         ! Time step
  real(dp), public :: obj_target ! Target in obj definition
  real(dp), public :: a2inv = 1.0_dp  ! 1 over alpha**2

  real(dp), dimension(:), allocatable, public :: obj ! contribs to f from each
                                                     ! time level

  character(len=100), public :: gridfile

  logical, parameter, public :: overlap      = .false.
  logical, parameter, public :: adjoint_only = .true.

  public :: free_data
  public :: jacobian_type
  public :: time_data_type

  type jacobian_type
    integer :: n
    integer, dimension(:), pointer :: list => null()
    real(dp), dimension(:,:,:), pointer :: blocks => null()
  end type jacobian_type

  type time_data_type
    real(dp), dimension(:),   allocatable :: vol
    real(dp), dimension(:,:), allocatable :: res
    real(dp), dimension(:,:), allocatable :: dfdq
    real(dp), dimension(:,:), allocatable :: drdd
    type(jacobian_type), dimension(:), allocatable :: drdq
    type(jacobian_type), dimension(:), allocatable :: drdqt
  end type time_data_type

  type(time_data_type), save, public :: time_data, time_data_back

contains

!==================================== FREE_DATA ==============================80
!
!   Frees up the big data type
!
!=============================================================================80
  subroutine free_data(time_data)

    type(time_data_type), intent(inout) :: time_data

    integer :: j

  continue

    if ( allocated(time_data%vol) ) deallocate(time_data%vol)
    if ( allocated(time_data%res) ) deallocate(time_data%res)
    if ( allocated(time_data%dfdq) ) deallocate(time_data%dfdq)

    if ( allocated(time_data%drdq) ) then
      do j = 1, nnodes
        if(associated(time_data%drdq(j)%list)) then
          deallocate(time_data%drdq(j)%list)
        endif
        if(associated(time_data%drdq(j)%blocks)) then
          deallocate(time_data%drdq(j)%blocks)
        endif
      end do
      deallocate(time_data%drdq)
    endif

    if ( allocated(time_data%drdqt) ) then
      do j = 1, nnodes
        if(associated(time_data%drdqt(j)%list)) then
          deallocate(time_data%drdqt(j)%list)
        endif
        if(associated(time_data%drdqt(j)%blocks)) then
          deallocate(time_data%drdqt(j)%blocks)
        endif
      end do
      deallocate(time_data%drdqt)
    endif

  end subroutine free_data

end module chaos_datas

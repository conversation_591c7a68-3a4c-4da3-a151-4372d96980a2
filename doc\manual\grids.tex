
\section{Grids}\label{s:grids}

This chapter explains how to supply the proper file formats to \FunThreeD,
but does not cover how to create a mesh. 
See \sectionref{s:grid-gen} for grid generation guidance.
\FunThreeD supports a direct reader for many grid formats.
The format of the grid is specified in the \texttt{\&raw\_grid} namelist,
\sectionref{s:nml_raw_grid}. 
In addition to the directly read formats,
translators are provided to convert additional grid
formats into a format that can be read directly, 
see \sectionref{s:grid_translator}.
\FunThreeD has the ability to apply rigid body rotations while reading the
grid (see \sectionref{s:nml_grid_transform} 
      and \sectionref{s:nml_body_transform})
and mirror a grid about a symmetry plane (see \sectionref{s:grid-mirror}).

\subsection{File Endianness}\label{s:endianness}

The ordering of bytes within a data item is known as ``endianness.''
If the endianness of a file is different than the native endianness
of the computer then a conversion must be performed.
The endianness of each
grid file format is described in \sectionref{s:grid_types}.
If your compiler supports it, \<PERSON><PERSON><PERSON><PERSON><PERSON> will attempt to
open binary files with a \cmd{open(convert=...)} keyword extension. 
Consult the documentation of the Fortran compiler you are using
to determine if other methods are available.
For example, with the \Intel Fortran compiler, the endianness of file
input and output can be controlled by setting 
the \cmd{F_UFMTENDIAN} environment variable to \cmd{big} or
\cmd{little}.

\subsection{Supported Grid Formats}
\label{gridformats}\label{s:grid_types}

\FunThreeD natively supports the grid formats summarized in
\tab{t:grid_formats}.
\begin{table}[hbpt]
  \centering\tabularfont
  \caption{File extensions.}
  \label{t:grid_formats}
  \begin{threeparttable}
  \begin{tabular}{lll}
    Format & Grid files & BC File \\
    \midrule
    AFLR3 & \var{.ugrid} & \var{.mapbc} \\
    FAST  & \var{.fgrid} & \var{.mapbc} \\
    FieldView & \var{.fvgrid_fmt} & \var{.mapbc} \\
              & \var{.fvgrid_unf} & \var{.mapbc} \\
    FUN2D & \var{.faces} & \var{.mapbc} \\
    VGRID & \var{.cogsg}, \var{.bc} & \var{.mapbc}\tnote{*} \\
    FELISA & \var{.gri}, \var{.fro} & \var{.bco} \\
    \bottomrule
  \end{tabular}
  \begin{tablenotes}
    \item [*] Same suffix, but GridTool format.
  \end{tablenotes}
  \end{threeparttable}
\end{table}

The standard \FunThreeD \file{.mapbc} file format
contains the boundary condition information for the grid.
The first line is an integer corresponding to
the number of boundary groups contained in the grid file.
Each subsequent line in this file contains two integers, 
the boundary face number and the \FunThreeD boundary condition integer;
these numbers may optionally be followed by a character string that
specifies a ``family'' name for the boundary.
The family name is required
if the \cmd{patch_lumping} option (\sectionref{s:nml_raw_grid})
is invoked to combine patches
into fewer patch families.
Below is a sample
\file{.mapbc} file illustrative for all grid formats except
GridTool/\VGRID, FELISA, and FUN2D, which are described later.

\begin{Verbatim}
13
1      6662  box_ymin
2      5025  box_zmax
3      5050  box_xmin
4      5025  box_ymax
5      5025  box_zmin
6      5025  box_xmax
7      3000  wing_upper
8      3000  wing_lower
9      3000  wing_upper
10     3000  wing_upper
11     3000  wing_lower
12     3000  wing_lower
13     3000  wing_tip
\end{Verbatim}

\subsubsection{AFLR3 Grids}

AFLR3, SolidMesh, Pointwise, and GridEx can all produce this format
and \FunThreeD ships with translators that convert Plot3D and CGNS
grids to AFLR3 format.
The format is documented online at
\url{http://simcenter.msstate.edu/docs/solidmesh/ugridformat.html}

AFLR3 grid file format types are indicated by file suffixes.
The formatted (plain text) style has a \var{.ugrid} suffix
while other types vary according to 
endianness (see \sectionref{s:endianness}) and binary type
as shown in \tab{t:aflr3_suffixes}.
\begin{table}[hbtp]
  \centering\tabularfont
  \caption{AFLR3 non-ASCII grid suffixes.}
  \label{t:aflr3_suffixes}
  \begin{tabular}{lll}
    Type                &  Little endian   & Big endian \\
    \midrule
    Fortran Stream, C Binary & \var{.lb8.ugrid} & \var{.b8.ugrid} \\
    Fortran Unformatted      & \var{.lr8.ugrid} & \var{.r8.ugrid} \\
  \end{tabular}
\end{table}
The boundary conditions are specified via 
the standard \FunThreeD \file{.mapbc} format.

\subsubsection{FAST Grids}

The \file{.fgrid} file contains the complete grid stored in ASCII FAST format.
The format is documented online at
\url{http://simcenter.msstate.edu/docs/solidmesh/FASTformat.html}
The boundary conditions are specified via 
the standard \FunThreeD \file{.mapbc} format.

\subsubsection{\protect\VGRID Grids}

The \file{.cogsg} file contains the grid nodes and tetrahedra 
stored in unformatted \VGRID format.
The \VGRID cogsg files always have big endian byte order
regardless of the computer used in grid generation.
See \sectionref{s:endianness} for instructions on specifying
file endianness.

The \file{.bc} file contains the boundary information for the grid,
as well as a flag for each boundary face.
For viscous grids with a symmetry plane, 
\VGRID is known to produce boundary triangles in the \file{.bc} file
that are incompatible with the volume tetrahedra.
\FunThreeD provides a \texttt{repair\_vgrid\_mesh} utility 
to swap the edges of these inconsistent boundary triangles.
If \FunThreeD reports that there are boundary triangles without 
a matching volume tetrahedra, use this utility.

\VGRID has a different \file{.mapbc} boundary condition format.
For each boundary flag used in the \file{.bc} file,
the \file{.mapbc} file contains the boundary type information.
The \VGRID boundary conditions are described at the website:
\url{http://tetruss.larc.nasa.gov/usm3d/bc.html}.
The \FunThreeD boundary condition integers can also be used
in place of the \VGRID boundary condition integers.
Internally, \FunThreeD converts the \VGRID boundary condition integers
to the \FunThreeD boundary condition integers as indicated in
\tab{t:usm2fun}.

\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Boundary type mapping between \VGRID and \FunThreeD.}
  \begin{tabular}{rr}
   VGRID & FUN3D \\
   \midrule
   $-$1 &   $-$1 \\
    0 & 5000 \\
    1 & 6662 \\
    2 & 5005 \\
    3 & 5000 \\
    4 & 4000 \\
    5 & 3000 \\
   44 & 4000 \\
   55 & 3000 \\
   \end{tabular}\label{t:usm2fun}
\end{table}

\subsubsection{FieldView Grids}

The \file{.fvgrid_fmt} file contains 
the complete grid stored in ASCII FieldView FV-UNS format,
and the \file{.fvgrid_unf} file contains 
the complete grid stored in unformatted FieldView FV-UNS format.
Supported FV-UNS file versions are 2.4, 2.5, and 3.0.
With FV-UNS version 3.0, 
the support is only for the grid file in split grid and results format;
the combined grid/results format is not supported.
\FunThreeD does not support the arbitrary polyhedron elements of the FV-UNS 3.0 
standard.
For ASCII FV-UNS 3.0, the standard allows comment lines (line starting with !) 
anywhere in the file.
\FunThreeD only allows comments immediately after line 1.
Only one grid section is allowed.
The precision of the unformatted grid format should 
be specified by the \cmd{fieldview_coordinate_precision} 
variable in the \cmd{&raw_grid} namelist, 
see \sectionref{s:nml_raw_grid}.
The boundary conditions are specified via 
the standard \FunThreeD \file{.mapbc} format.

\subsubsection{FELISA Grids}

The \file{.gri} file contains 
the grid stored in formatted FELISA format.\cite{felisa-user-manual}
The \file{.fro} file contains 
the surface mesh nodes and connectivities and associated 
boundary face tags for each surface triangle.
This file can contain additional surface normal or tangent information
(as output from \href{http://geolab.larc.nasa.gov/GridEx/}{GridEx} or SURFACE 
mesh generation tools), but the additional data is not read by 
\FunThreeD.
The \file{.bco} file contains a flag for each boundary face.  
If original FELISA boundary condition flags (1, 2, or 3) are used, 
they are translated to the 
corresponding \FunThreeD 4-digit boundary condition flag 
according to \tab{t:felisa-bc}.  
Alternatively, \FunThreeD 4-digit boundary condition flags
can be assigned directly in this file.
\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Boundary type mapping between FELISA and \FunThreeD.}
  \begin{tabular}{rr}
   FELISA & FUN3D \\
   \midrule
    1 & 3000 \\
    2 & 6662 \\
    3 & 5000 \\
  \end{tabular}\label{t:felisa-bc}
\end{table}

\subsubsection{\FunTwoD Grids}

The \file{.faces} file contains 
the complete grid stored in formatted \FunTwoD format 
(triangles).
Internally, 
\FunThreeD will extrude the triangles into prisms in the $y$-direction
and
the 2D mode of \FunThreeD is automatically enabled.
Output from the flow solver will include this one-cell wide 
extruded mesh.

Boundary conditions are contained in the \FunTwoD grid file as integers 0--8.
The mappings to \FunThreeD boundary conditions are given
in \tab{t:fun2d_to_fun3d_bcs}.
\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Boundary type mapping between \FunTwoD and \FunThreeD.}
  \begin{tabular}{rr}
   FUN2D & FUN3D \\
   \midrule
    0 & 3000 \\
    1 & 4000 \\
    2 & 5000 \\
    3 & $-$1 \\
    4 & 4010 \\
    5 & 4010 \\
    6 & 5005 \\
    7 & 7011 \\
    8 & 7012 \\
  \end{tabular}
  \label{t:fun2d_to_fun3d_bcs}
\end{table}
If \FunThreeD does not detect a \file{.mapbc}, 
it will write a \file{.mapbc} file
that contains the default \tab{t:fun2d_to_fun3d_bcs} mapping.
If you wish to change the boundary conditions from the defaults
based on the \file{.faces} file,
simply edit them in this \file{.mapbc} file and rerun \FunThreeD.
The boundary conditions in the \file{.mapbc} file
have precedence over the \file{.faces}  boundary conditions.
If you wish to revert to the boundary conditions in the \file{.faces} file
after modifying the \file{.mapbc},
you can remove the \file{.mapbc} and rerun \FunThreeD.

\subsection{Translation of Additional Grid Formats}
\label{s:grid_translator}

While \FunThreeD supports the direct read of multiple formats,
utilities are provided to translate additional grid formats
into a format that \FunThreeD can read.

\subsubsection{PLOT3D Grids}

The utility \file{plot3d_to_aflr3} converts a PLOT3D structured grid
to an AFLR3-format hexahedral unstructured grid.
The original structured grid must be 3D multiblock 
\url{http://www.grc.nasa.gov/WWW/wind/valid/plot3d.html}
(no iblanking)
with the file extension \file{.p3d} for formatted ASCII or 
the the file extension \file{.ufmt} for Fortran unformatted.
Only one-to-one connectivity is allowed with this option
(no patching or overset).
The grid should contain no singular (degenerate) lines or points.
A neutral map file with extension \file{.nmf} is also required.
This file gives boundary conditions and connectivity information.
The \file{.nmf} file is described at
\url{http://geolab.larc.nasa.gov/Volume/Doc/nmf.htm}.

Note that the \cmd{Type} name in the \file{.nmf} file must correspond 
with one of \FunThreeD's BC types, plus it allows the Type \file{one-to-one}.
If the \cmd{Type} is not recognized, you will get errors like:
\begin{Verbatim}
  This may be an invalid BC index.
\end{Verbatim}
An example \file{.nmf} file is shown here for \
a simple single-zone airfoil C-grid
($5\times257\times129$) with six exterior boundary conditions and 
one \file{one-to-one}
patch in the wake where the C-grid attaches to itself:
\begin{Verbatim}[fontsize=\scriptsize]
# ===== Neutral Map File generated by the V2k software of NASA Langley's GEOLAB =====
# ===================================================================================
# Block#   IDIM   JDIM   KDIM
# -----------------------------------------------------------------------------------
      1

      <USER>      <GROUP>    257    129

# ===================================================================================
# Type            B1  F1  S1   E1  S2   E2   B2  F2   S1   E1   S2  E2  Swap
# -----------------------------------------------------------------------------------
'tangency'         1   3   1  257   1  129
'tangency'         1   4   1  257   1  129
'farfield_extr'    1   5   1  129   1    5
'farfield_extr'    1   6   1  129   1    5
'one-to-one'       1   1   1    5   1   41    1   1    1    5  257 217 false
'viscous_solid'    1   1   1    5  41  217
'farfield_riem'    1   2   1    5   1  257
\end{Verbatim}

\subsubsection{CGNS Grids}

\FunThreeD is distributed with a utility \file{cgns_to_aflr3}
that converts CGNS files \url{http://cgns.sourceforge.net/} 
to AFLR3 grids. 
This utility will only be built if \FunThreeD is configured with a 
CGNS library, see \sectionref{s:install-cgns}.
Only the \cmd{Unstructured} type of CGNS files are supported.
The following CGNS mixed element types are supported:
\cmd{PENTA_6} (prisms), \cmd{HEX_8} (hexes), \cmd{TETRA_4} (tets),
and \cmd{PYRA_5} (pyramids).

The CGNS file must include \cmd{Elements_t} nodes for all boundary faces (type
\cmd{QUAD_4} or \cmd{TRI_3}) to refer to the corresponding boundary elements.
Otherwise, the utility cannot recognize what boundaries are present
because it currently identifies boundaries via these 2D element types.
The \file{cgns_to_aflr3} utility requires that the BC elements be listed either
as a range or a sequential list.

It is also helpful to have separate element nodes
for each boundary element of a given BC type.
This way, it is easier to interpret the boundaries,
i.e., body versus symmetry versus farfield.
Visualization tools, such as \Tecplot,
can easily distinguish the various boundary condition groups
as long as each group has its own node in the CGNS tree.
Under \cmd{BC_t}, \file{cgns_to_aflr3} reads these BC names,
but ignores additional boundary data (e.g., \cmd{BCDataSet}, \cmd{BCData}).

\begin{table}[hbtp]
  \centering\tabularfont
  \caption{Boundary type mapping between CGNS and \FunThreeD.}
  \begin{tabular}{ll}
   CGNS & FUN3D \\
   \midrule
    BCSymmetryPlane & 6661, 6662, or 6663 via prompt\\
    BCFarfield      & 5000 \\
    BCWallViscous   & 4000 \\
    BCWall          & 4000 \\
    BCWallInviscid  & 3000 \\
    BCOutflow       & 5026 \\
    BCTunnelOutflow & 5026 \\
    BCInflow        & 5000 \\
    BCTunnelInflow  & 5000 \\
  \end{tabular}
  \label{t:cgns_to_fun3d_bcs}
\end{table}

If the CGNS file is missing BCs (no \cmd{BC_t} node),
\file{cgns_to_aflr3} still tries to construct the BCs based
on the boundary face \cmd{Elements_t} information.
If these boundary element nodes have a name
listed in \tab{t:cgns_to_fun3d_bcs},
a \file{.mapbc} file will be written that contains the \FunThreeD
boundary condition numbers.
If the name is not recognized,
you will see the message:
\begin{Verbatim}
    WARNING: BC type ... in CGNS file not recognized.
\end{Verbatim}
in which case you will need to fix it by by editing the .mapbc file
manually.
Always check the \file{.mapbc} file after the utility has run,
to make sure that the boundary conditions
have all been interpreted and set correctly.
If a translation problem is observed, 
you should edit the \file{.mapbc} file before running \FunThreeD.

\subsection{Implicit Lines}
\label{s:grid_lines}

The standard implicit solution relaxation scheme in \FunThreeD is
a point-implicit, 
which inverts the linearization of the residual at each node
to compute an update to the solution.
A line-implicit relaxation scheme can be used
that inverts the linearization of a line of nodes
simultaneously.
Typically, lines are constructed for the subset of nodes in the
boundary layer to address the stiffness inherent in the Navier-Stokes
equations on anisotropic grids.
Therefore, the use of line-implicit relaxation may improve the convergence 
of viscous flow simulations.
These lines are used in conjunction with 
the standard point-implicit relaxation scheme.
Detailed descriptions of both line and point relaxation
schemes are provided by Nielsen et al.\cite{nielsen-exact-dual}
Currently, every viscous boundary node must have
one and only one associated implicit line.
Lines of nodes are specified in
a formatted file with the suffix \file{.lines_fmt} that contains
the definitions of lines emanating from viscous boundary nodes
as a list of node numbers.
The format of the \file{.lines_fmt} file is
\begin{Verbatim}
[total number of lines] [total number of points in lines]
[min points in a line] [max points in a line]
 [points in line 1]
  [first node of line 1]
  ...
  [last node of line 1]
 [points in line 2]
  ...
\end{Verbatim}

\subsection{Grid Mirroring}\label{s:grid-mirror}

A half computational domain with a symmetry plane can be mirrored 
to form a complete domain.
This is a common use case for reflecting half of an aircraft configuration
to form a full-span configuration to include the effects of side slip.
Use the \cmd{--mirror_x}, \cmd{--mirror_y}, or \cmd{--mirror_z} 
command line options (\sectionref{s:clo}) to
reflect the domain and remove the boundary patch located at
$x=0$, $y=0$, or $z=0$, respectively.
When one or more of these mirror command line options are provided,
\FunThreeD will start execution on a grid that is mirrored internally
during grid processing.
The symmetry plane boundary face patch will be removed and each
non-symmetry face patch will duplicated about the symmetry plane.
These operations on boundary face patches will alter the numbering
of patches.
Therefore,
the boundary face indexes used to specify boundary conditions
and co-visualization will be modified to track these changes.
The user is required to update the indexes in these namelists appropriately.
The file \file{[project_rootname]_mirror*.mapbc} will be created 
in the current directory to aid this process.

The internally mirrored grid will be discarded when execution is complete.
So, provide the same mirror command line option to any restarted executions.
Alternatively,
the \cmd{--write_mesh [new_project]} command line option is available
to export the mirrored grid.
Utilizing this exported grid would remove the need for
the mirror command line option on subsequent runs.


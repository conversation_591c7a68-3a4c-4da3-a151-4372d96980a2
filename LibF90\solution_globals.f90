module solution_globals

  use kinddefs,       only : dp
  use solution_types, only : soln_type, global_bndry_type, global_slice_type

  implicit none

  private

  public :: set_up_global_bndry_data
  public :: deallocate_global_bndry_data
  public :: global_slice_data, slice_bndry_data
  public :: nslice_groups, slice_group_beg, slice_group_end
  public :: mpi_memory_reserve

  !beginNoComplexInterface
  public  :: get_global_bndry_data
  private :: compute_bndryl2g
  !endNoComplexInterface

  integer :: nslice_groups ! number of groups of slices

  integer, dimension(:), allocatable :: slice_group_beg ! start of slice group
  integer, dimension(:), allocatable :: slice_group_end ! end of slice group

  type(global_slice_type), dimension(:), allocatable :: global_slice_data

!!!!!! may cause a double free with some fortran compilers !!!!!!!
  logical, parameter :: enforce_mpi_memory_reserve = .false. ! hold back for MPI

  integer            :: mpi_memory_reserve         = 0       ! reserve (Mb)
  logical, parameter :: available_memory_verbose   = .false. ! show steps

  logical :: need_t0_coords = .false. ! save global image of initial surfaces(s)
                                      ! for slicing or other output that is
                                      ! mapped onto the initial surface shape

  interface gather_global_bndry_data
    module procedure gather_global_bndry_data_real1
    module procedure gather_global_bndry_data_real2
    module procedure gather_global_bndry_data_int1
  end interface

contains

!========================== SET_UP_GLOBAL_BNDRY_DATA =========================80
!
! Sets up some preliminary info for the global_bndry_data type
!
!=============================================================================80

  subroutine set_up_global_bndry_data( grid, global_bndry_data, n_tot, n_turb, &
                                       n_grd, njac, adim )

    use grid_types,              only : grid_type
    use nml_boundary_output,     only : need_normals
    use nml_fwh_acoustic_data,   only : fwh_data_freq

    type(grid_type),                       intent(in) :: grid
    type(global_bndry_type), dimension(:), pointer    :: global_bndry_data
    integer,                               intent(in) :: n_tot, n_turb, n_grd
    integer,                               intent(in) :: njac, adim

    integer :: ib

    logical, save :: init = .true.

  continue

    if ( grid%origin > 1 .and. grid%cc ) return !skip agglomerated cc grids

    if (.not. init) return

    if (fwh_data_freq /= 0) need_normals = .true.

    do ib = 1,grid%nbound

!     default reference frame for this data is the inertial frame

      global_bndry_data(ib)%ref_frame = 0

!     default to gathering *no* global images of the boundaries; as various
!     options are requested (animation, slicing, aero_loads, etc) we will
!     add to the list of boundaries we need to gather

      global_bndry_data(ib)%used = .false.

!     get connectivity data for the global image of the boundaries; since this
!     is done only once we'll just go ahead and do it for all boundaries

      call get_global_bndry_connectivity(grid%bc(ib),grid,                     &
                                                     global_bndry_data(ib))

!     allocate the global data arrays to unit size for safety; will reallocate
!     the boundaries that actually get output before filling with data

      allocate(global_bndry_data(ib)%xglobal_bndry(1))
      allocate(global_bndry_data(ib)%yglobal_bndry(1))
      allocate(global_bndry_data(ib)%zglobal_bndry(1))
      allocate(global_bndry_data(ib)%xat0global_bndry(1))
      allocate(global_bndry_data(ib)%yat0global_bndry(1))
      allocate(global_bndry_data(ib)%zat0global_bndry(1))
      allocate(global_bndry_data(ib)%qglobal_bndry(n_tot, 1))
      allocate(global_bndry_data(ib)%cpglobal_bndry(1))
      allocate(global_bndry_data(ib)%cqglobal_bndry(1))
      allocate(global_bndry_data(ib)%cfxglobal_bndry(1))
      allocate(global_bndry_data(ib)%cfyglobal_bndry(1))
      allocate(global_bndry_data(ib)%cfzglobal_bndry(1))
      allocate(global_bndry_data(ib)%slenglobal_bndry(1))
      allocate(global_bndry_data(ib)%amutglobal_bndry(1))
      allocate(global_bndry_data(ib)%turbglobal_bndry(n_turb, 1))
      allocate(global_bndry_data(ib)%l2gglobal_bndry(1))
      allocate(global_bndry_data(ib)%iblankglobal_bndry(1))
      allocate(global_bndry_data(ib)%imeshglobal_bndry(1))
      allocate(global_bndry_data(ib)%gradxglobal_bndry(n_grd, 1))
      allocate(global_bndry_data(ib)%gradyglobal_bndry(n_grd, 1))
      allocate(global_bndry_data(ib)%gradzglobal_bndry(n_grd, 1))
      allocate(global_bndry_data(ib)%tavg_qglobal_bndry(2*n_tot, 1))
      allocate(global_bndry_data(ib)%uavgglobal_bndry(1))
      allocate(global_bndry_data(ib)%vavgglobal_bndry(1))
      allocate(global_bndry_data(ib)%wavgglobal_bndry(1))
      allocate(global_bndry_data(ib)%volglobal_bndry(1))
      allocate(global_bndry_data(ib)%resglobal_bndry(njac, 1))
      allocate(global_bndry_data(ib)%turresglobal_bndry(n_turb, 1))
      allocate(global_bndry_data(ib)%res_gclglobal_bndry(1, 1))
      allocate(global_bndry_data(ib)%rlamglobal_bndry(adim, 1))
      allocate(global_bndry_data(ib)%proc_idglobal_bndry(1))
      allocate(global_bndry_data(ib)%amutoffbodyglobal_bndry(1))
      allocate(global_bndry_data(ib)%bxnglobal_bndry(1))
      allocate(global_bndry_data(ib)%bynglobal_bndry(1))
      allocate(global_bndry_data(ib)%bznglobal_bndry(1))
! 6 viscous flux boundary variables:
! shear_x, shear_y, shear_z, heating_convective, heating_radiative, mdot
      allocate(global_bndry_data(ib)%viscous_flux_bndry(6, 1))
      allocate(global_bndry_data(ib)%utau_wfglobal_bndry(1))
      allocate(global_bndry_data(ib)%phi_wfglobal_bndry(1))
      allocate(global_bndry_data(ib)%mu_t_wfglobal_bndry(1))
      allocate(global_bndry_data(ib)%k_wf_bcglobal_bndry(1))
      allocate(global_bndry_data(ib)%omega_wf_bcglobal_bndry(1))

    end do

    init = .false.

  end subroutine set_up_global_bndry_data

!============================ GET_GLOBAL_BNDRY_DATA ==========================80
!
!  Constructs a global (unpartitioned) representation of the current solution
!  on boundaries that have been selected for output
!
!=============================================================================80

   subroutine get_global_bndry_data( nbound, grid, soln, global_bndry_data,    &
                                     ref_frame, sadj, adjoint_mode_arg )

     use kinddefs,             only : dp
     use grid_types,           only : grid_type
     use lmpi,                 only : lmpi_master, lmpi_die, lmpi_bcast, lmpi_id
     use lmpi_app,             only : lmpi_xfer
     use solution_types,       only : generic_gas, compressible
     use solution_adj,         only : sadj_type
     use info_depr,            only : cc_primal, xmach
     use nml_global,           only : moving_grid, boundary_animation_freq_tavg
     use nml_boundary_output,  only : n_output_variables,                      &
                                      output_variables,                        &
                                      need_slen, need_gradients,               &
                                      need_amut, need_avg_velocity,            &
                                      need_tavg, need_viscous_flux,            &
                                      need_amutoffbody, need_normals
     use bc_names,             only : bc_used_for_distance_function
     use nml_overset_data,     only : overset_flag
     use pundit,               only : pundit_flag
     use turb_util,            only : set_slen_wall_nonzero, set_slen_wall_zero
     use generic_gas_map,      only : rho_ref, v_ref
     use flow_initialization,  only : uzm, rhozm
     use nml_slice_data,       only : slice_initial_coords
     use nml_mdo_surface_data, only : aero_loads_use_initial_coords,           &
                                      massoud_use_initial_coords

     integer,                                 intent(in)    :: nbound
     integer,                                 intent(in)    :: ref_frame
     type(grid_type),                         intent(inout) :: grid
     type(soln_type),                         intent(inout) :: soln
     type(global_bndry_type),                                                  &
                           dimension(nbound), intent(inout) :: global_bndry_data
     type(sadj_type),               optional, intent(in)    :: sadj
     logical,                       optional, intent(in)    :: adjoint_mode_arg

     logical :: adjoint_mode
     logical :: solid_wall, need_avg_vel
     logical :: need_avg_slen
     logical :: verbose = .false.

     logical :: use_bcsoln_stresses = .false. ! use shear data from bcsoln
                                              ! instead of shear data from
                                              ! force integration routine

     integer :: nface, nfacelocal, nfacenodes, nvar, ib, beg_trb, end_trb, i
     integer :: nbfacet, nbfaceq

     integer, dimension(nbound) :: alloc_status

     real(dp), dimension(:),   allocatable :: slen_face
     real(dp), dimension(:),   allocatable :: slenface_t, slenface_q
     real(dp), dimension(:),   allocatable :: cp_face
     real(dp), dimension(:),   allocatable :: cq_face
     real(dp), dimension(:),   allocatable :: cfx_face
     real(dp), dimension(:),   allocatable :: cfy_face
     real(dp), dimension(:),   allocatable :: cfz_face
     real(dp), dimension(:),   allocatable :: uavgt, uavgq
     real(dp), dimension(:),   allocatable :: vavgt, vavgq
     real(dp), dimension(:),   allocatable :: wavgt, wavgq
     real(dp), dimension(:),   allocatable :: amutoffbodyt, amutoffbodyq
     real(dp), dimension(:),   allocatable :: uavg_face
     real(dp), dimension(:),   allocatable :: vavg_face
     real(dp), dimension(:),   allocatable :: wavg_face
     real(dp), dimension(:),   allocatable :: amutoffbody_face
     real(dp), dimension(:),   allocatable :: temp_face_data
     real(dp), dimension(:),   allocatable :: temp_vol
     real(dp), dimension(:,:), allocatable :: temp_bndry, global_bndry
     real(dp), dimension(:),   allocatable :: transfer_array

     type pointer_array
       real(dp), dimension(:), pointer :: p
     end type pointer_array
     type( pointer_array ), dimension( 6 ) :: temp_ptr

   continue

     if ( present(adjoint_mode_arg) ) then
       adjoint_mode = adjoint_mode_arg
     else
       adjoint_mode = .false.
     endif

     if ( adjoint_mode .and. (.not.present(sadj)) ) then
       write(*,*) 'adjoint mode requested in get_global_bndry_data, but'
       write(*,*) 'sadj not provided...stopping.'
       call lmpi_die
     endif

     if (slice_initial_coords)          need_t0_coords = .true.
     if (aero_loads_use_initial_coords) need_t0_coords = .true.
     if (massoud_use_initial_coords)    need_t0_coords = .true.
     if (moving_grid)                   need_t0_coords = .true.

!    insure the global_bndry arrays are allocated large enough - reallocate
!    if not (arrays are initially allocated to unit size); also check that
!    sufficient memory can be allocated for any temporary arrays needed to
!    gather the global data; remove any boundary from the output list if
!    there was not sufficient space to allocate the required arrays; the
!    global_bndry arrays need only be checked at the start; the temp arrays
!    need checking each time through

     call check_global_array_sizes(grid%nbound, soln%n_tot, soln%n_grd,      &
                                   soln%n_turb, soln%njac, soln%adim,        &
                                   global_bndry_data, alloc_status)

     check_global_array_memory : do ib=1,grid%nbound
       if (global_bndry_data(ib)%used) then
         if (alloc_status(ib) /= 0) then
           global_bndry_data(ib)%used      = .false.
           soln%global_bndry_data(ib)%used = .false.
           if (lmpi_master) then
             write(*,'(2a)')                                                 &
           ' WARNING: allocate failed for global_bndry_data array',          &
             ' in check_global_array_sizes'
             write(*,'(a,i0)')                                               &
             ' ...skipping requested boundary data output for boundary ', ib
           end if
         end if
       end if
     end do check_global_array_memory

     call check_temp_array_sizes(grid%nbound, grid%bc, global_bndry_data,      &
                                 alloc_status)

     check_temp_array_memory : do ib=1,grid%nbound
       if (global_bndry_data(ib)%used) then
         if (alloc_status(ib) /= 0) then
           global_bndry_data(ib)%used      = .false.
           soln%global_bndry_data(ib)%used = .false.
           if (lmpi_master) then
             write(*,'(2a)')                                                 &
             ' WARNING: allocate failed for temp arrray(s)',                 &
             ' in check_temp_array_sizes'
             write(*,'(a,i0)')                                               &
             ' ...skipping requested boundary data output for boundary ', ib
           end if
         end if
       end if
     end do check_temp_array_memory

     if (verbose .and. lmpi_master )                                           &
       write(*,*) " populate global x,y,z and solution on boundaries..."

!    start gathering (local) data from each processor into the global arrays
!    note: the gather steps below use mpi_gatherv (rather than mpi_allgatherv)
!    so that the gathered (global) data is available only on the master proc -
!    non-master procs will have the initilized (zero) values

!    1) gather any required node-based data into global node-based arrays

     boundary_loop_1 : do ib=1,grid%nbound

       if (.not.(global_bndry_data(ib)%used)) cycle boundary_loop_1

       nfacelocal = global_bndry_data(ib)%nfacelocal
       nface      = global_bndry_data(ib)%nface
       nfacenodes = global_bndry_data(ib)%nfacenodes

       solid_wall = .false.
       if ( bc_used_for_distance_function(grid%bc(ib)%ibc) ) solid_wall = .true.

!      initialize

       global_bndry_data(ib)%qglobal_bndry(:,:)         = 0._dp
       global_bndry_data(ib)%xglobal_bndry(:)           = 0._dp
       global_bndry_data(ib)%yglobal_bndry(:)           = 0._dp
       global_bndry_data(ib)%zglobal_bndry(:)           = 0._dp
       global_bndry_data(ib)%xat0global_bndry(:)        = 0._dp
       global_bndry_data(ib)%yat0global_bndry(:)        = 0._dp
       global_bndry_data(ib)%zat0global_bndry(:)        = 0._dp
       global_bndry_data(ib)%slenglobal_bndry(:)        = 0._dp
       global_bndry_data(ib)%rlamglobal_bndry(:,:)      = 0._dp
       global_bndry_data(ib)%amutglobal_bndry(:)        = 0._dp
       global_bndry_data(ib)%turbglobal_bndry(:,:)      = 0._dp
       global_bndry_data(ib)%l2gglobal_bndry(:)         = 0._dp
       global_bndry_data(ib)%iblankglobal_bndry(:)      = 0._dp
       global_bndry_data(ib)%imeshglobal_bndry(:)       = 0._dp
       global_bndry_data(ib)%gradxglobal_bndry(:,:)     = 0._dp
       global_bndry_data(ib)%gradyglobal_bndry(:,:)     = 0._dp
       global_bndry_data(ib)%gradzglobal_bndry(:,:)     = 0._dp
       global_bndry_data(ib)%volglobal_bndry(:)         = 0._dp
       global_bndry_data(ib)%resglobal_bndry(:,:)       = 0._dp
       global_bndry_data(ib)%turresglobal_bndry(:,:)    = 0._dp
       global_bndry_data(ib)%res_gclglobal_bndry(:,:)   = 0._dp
       global_bndry_data(ib)%tavg_qglobal_bndry(:,:)    = 0._dp
       global_bndry_data(ib)%proc_idglobal_bndry(:)     = 0._dp
       global_bndry_data(ib)%viscous_flux_bndry(:,:)    = 0._dp
       global_bndry_data(ib)%amutoffbodyglobal_bndry(:) = 0._dp
       global_bndry_data(ib)%utau_wfglobal_bndry(:)     = 0._dp
       global_bndry_data(ib)%phi_wfglobal_bndry(:)      = 0._dp
       global_bndry_data(ib)%mu_t_wfglobal_bndry(:)     = 0._dp
       global_bndry_data(ib)%k_wf_bcglobal_bndry(:)     = 0._dp
       global_bndry_data(ib)%omega_wf_bcglobal_bndry(:) = 0._dp
       global_bndry_data(ib)%bxnglobal_bndry(:)         = 0._dp
       global_bndry_data(ib)%bynglobal_bndry(:)         = 0._dp
       global_bndry_data(ib)%bznglobal_bndry(:)         = 0._dp

!      store the reference frame in which the global data currently resides

       global_bndry_data(ib)%ref_frame = ref_frame

!      allocate some work arrays

       allocate(global_bndry(4,nface))
       allocate(temp_bndry(4,max(nfacelocal,1)))

       if (cc_primal) then
         call gather_global_bndry_data(                                        &
                            size(soln%qtavg,2), nfacenodes,                    &
                            nface,  nfacelocal,                                &
                            global_bndry_data(ib)%qglobal_bndry,               &
                            soln%qtavg,                                        &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_q)
       else
         call gather_global_bndry_data(                                        &
                            size(soln%q_dof,2), nfacenodes,                    &
                            nface,  nfacelocal,                                &
                            global_bndry_data(ib)%qglobal_bndry,               &
                            soln%q_dof,                                        &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_tot)
       end if
       call gather_global_bndry_data(                                          &
                          size(grid%x), nfacenodes, nface, nfacelocal,         &
                          global_bndry_data(ib)%xglobal_bndry,                 &
                          grid%x,                                              &
                          global_bndry_data(ib)%bndryl2g,                      &
                          global_bndry_data(ib)%localf2n,                      &
                          global_bndry_data(ib)%f2n,                           &
                          global_bndry_data(ib)%nfaceproc,                     &
                          temp_bndry, global_bndry)

       call gather_global_bndry_data(                                          &
                          size(grid%y), nfacenodes, nface, nfacelocal,         &
                          global_bndry_data(ib)%yglobal_bndry,                 &
                          grid%y,                                              &
                          global_bndry_data(ib)%bndryl2g,                      &
                          global_bndry_data(ib)%localf2n,                      &
                          global_bndry_data(ib)%f2n,                           &
                          global_bndry_data(ib)%nfaceproc,                     &
                          temp_bndry, global_bndry)

       call gather_global_bndry_data(                                          &
                          size(grid%z), nfacenodes, nface, nfacelocal,         &
                          global_bndry_data(ib)%zglobal_bndry,                 &
                          grid%z,                                              &
                          global_bndry_data(ib)%bndryl2g,                      &
                          global_bndry_data(ib)%localf2n,                      &
                          global_bndry_data(ib)%f2n,                           &
                          global_bndry_data(ib)%nfaceproc,                     &
                          temp_bndry, global_bndry)

       call gather_global_bndry_data(                                          &
                          size(grid%l2g), nfacenodes, nface, nfacelocal,       &
                          global_bndry_data(ib)%l2gglobal_bndry,               &
                          grid%l2g,                                            &
                          global_bndry_data(ib)%bndryl2g,                      &
                          global_bndry_data(ib)%localf2n,                      &
                          global_bndry_data(ib)%f2n,                           &
                          global_bndry_data(ib)%nfaceproc,                     &
                          temp_bndry, global_bndry)

       variable_loop : do nvar = 1,n_output_variables

         select case (trim(adjustl(output_variables(nvar))))

           case('uuprime')
             if (soln%n_turb==7) then
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 1
               end_trb = soln%n_q - soln%n_turb + 1
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 1
               end_trb = 1
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if
             else
             continue
             end if

           case('vvprime')
             if (soln%n_turb==7) then
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 2
               end_trb = soln%n_q - soln%n_turb + 2
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 2
               end_trb = 2
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if
             else
             continue
             end if

           case('wwprime')
             if (soln%n_turb==7) then
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 3
               end_trb = soln%n_q - soln%n_turb + 3
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 3
               end_trb = 3
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if
             else
             continue
             end if

           case('uvprime')
             if (soln%n_turb==7) then
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 4
               end_trb = soln%n_q - soln%n_turb + 4
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 4
               end_trb = 4
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if
             else
             continue
             end if

           case('uwprime')
             if (soln%n_turb==7) then
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 5
               end_trb = soln%n_q - soln%n_turb + 5
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 5
               end_trb = 5
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if
             else
             continue
             end if

           case('vwprime')
             if (soln%n_turb==7) then
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 6
               end_trb = soln%n_q - soln%n_turb + 6
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 6
               end_trb = 6
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if
             else
             continue
             end if

           case('turb1')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 1
               end_trb = soln%n_q - soln%n_turb + 1
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 1
               end_trb = 1
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('turb2')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 2
               end_trb = soln%n_q - soln%n_turb + 2
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 2
               end_trb = 2
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('turb3')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 3
               end_trb = soln%n_q - soln%n_turb + 3
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 3
               end_trb = 3
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('turb4')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 4
               end_trb = soln%n_q - soln%n_turb + 4
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 4
               end_trb = 4
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('turb5')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 5
               end_trb = soln%n_q - soln%n_turb + 5
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 5
               end_trb = 5
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('turb6')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 6
               end_trb = soln%n_q - soln%n_turb + 6
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 6
               end_trb = 6
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('turb7')
             if (cc_primal) then
               beg_trb = soln%n_q - soln%n_turb + 7
               end_trb = soln%n_q - soln%n_turb + 7
               call gather_global_bndry_data(                                  &
                              size(soln%qtavg,2), nfacenodes,                  &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%qtavg,                                      &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_q, beg_trb, end_trb)
             else
               beg_trb = 7
               end_trb = 7
               call gather_global_bndry_data(                                  &
                              size(soln%turb,2), nfacenodes,                   &
                              nface,  nfacelocal,                              &
                              global_bndry_data(ib)%turbglobal_bndry,          &
                              soln%turb,                                       &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              soln%n_turb, beg_trb, end_trb)
             end if

           case('iblank')
             if (cc_primal .and. .not. (overset_flag.or.pundit_flag)) then
               global_bndry_data(ib)%iblankglobal_bndry = 1._dp
             else
               call gather_global_bndry_data(                                  &
                              size(grid%iblank), nfacenodes, nface, nfacelocal,&
                              global_bndry_data(ib)%iblankglobal_bndry,        &
                              grid%iblank,                                     &
                              global_bndry_data(ib)%bndryl2g,                  &
                              global_bndry_data(ib)%localf2n,                  &
                              global_bndry_data(ib)%f2n,                       &
                              global_bndry_data(ib)%nfaceproc,                 &
                              temp_bndry, global_bndry)
             end if

           case('imesh')
             call gather_global_bndry_data(                                    &
                            size(grid%imesh), nfacenodes, nface, nfacelocal,   &
                            global_bndry_data(ib)%imeshglobal_bndry,           &
                            grid%imesh,                                        &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry)

           case('volume')
             call gather_global_bndry_data(                                    &
                            size(grid%vol), nfacenodes, nface, nfacelocal,     &
                            global_bndry_data(ib)%volglobal_bndry,             &
                            grid%vol,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry)

           case('res1')
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes, nface, nfacelocal,   &
                            global_bndry_data(ib)%resglobal_bndry,             &
                            soln%res,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%njac, 1, 1)

           case('res2')
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes, nface, nfacelocal,   &
                            global_bndry_data(ib)%resglobal_bndry,             &
                            soln%res,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%njac, 2, 2)

           case('res3')
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes, nface, nfacelocal,   &
                            global_bndry_data(ib)%resglobal_bndry,             &
                            soln%res,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%njac, 3, 3)

           case('res4')
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes, nface, nfacelocal,   &
                            global_bndry_data(ib)%resglobal_bndry,             &
                            soln%res,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%njac, 4, 4)

           case('res5')
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes, nface, nfacelocal,   &
                            global_bndry_data(ib)%resglobal_bndry,             &
                            soln%res,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%njac, 5, 5)

           case('lambda1')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 1, 1)

           case('lambda2')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 2, 2)

           case('lambda3')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 3, 3)

           case('lambda4')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 4, 4)

           case('lambda5')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 5, 5)

           case('lambda6')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 6, 6)

           case('lambda7')
             call gather_global_bndry_data(                                    &
                            size(sadj%rlam,2), nfacenodes, nface, nfacelocal,  &
                            global_bndry_data(ib)%rlamglobal_bndry,            &
                            sadj%rlam(:,:,1),                                  &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%adim, 7, 7)

           case('turres1')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 1, 1)

           case('turres2')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 2, 2)

           case('turres3')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 3, 3)

           case('turres4')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 4, 4)

           case('turres5')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 5, 5)
           case('turres6')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 6, 6)
           case('turres7')
             call gather_global_bndry_data(                                    &
                            size(soln%turbres,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%turresglobal_bndry,          &
                            soln%turbres,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            soln%n_turb, 7, 7)

           case('res_gcl')
             call gather_global_bndry_data(                                    &
                            size(grid%res_gcl,2), nfacenodes,                  &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%res_gclglobal_bndry,         &
                            grid%res_gcl,                                      &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            1, 1, 1)

           case('processor_id')
             allocate(temp_vol(size(grid%x)))
             temp_vol(:) = lmpi_id
             call gather_global_bndry_data(                                    &
                            size(grid%x), nfacenodes, nface, nfacelocal,       &
                            global_bndry_data(ib)%proc_idglobal_bndry,         &
                            temp_vol,                                          &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry)
             deallocate(temp_vol)

           case('utau_wf')

!            Coming from bcsoln, the array length is keyed in to which
!            partition things are sitting on, so we need to gather all the
!            partition information together in to one local array to then
!            transfer to the global database

             allocate(transfer_array(size(soln%res,2)))
             transfer_array(:) = 0._dp
             do i = 1, grid%bc(ib)%nbnode
               transfer_array(grid%bc(ib)%ibnode(i)) = soln%bcsoln(ib)%u_tau(i)
             end do
             call lmpi_xfer(transfer_array)
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes,                      &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%utau_wfglobal_bndry,         &
                            transfer_array,                                    &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry )
             deallocate(transfer_array)

           case('phi_wf')
             allocate(transfer_array(size(soln%res,2)))
             transfer_array(:) = 0._dp
             do i = 1, grid%bc(ib)%nbnode
               transfer_array(grid%bc(ib)%ibnode(i)) = soln%bcsoln(ib)%phi_wf(i)
             end do
             call lmpi_xfer(transfer_array)
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes,                      &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%phi_wfglobal_bndry,          &
                            transfer_array,                                    &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry )
             deallocate(transfer_array)

           case('mu_t_wf')
             allocate(transfer_array(size(soln%res,2)))
             transfer_array(:) = 0._dp
             do i = 1, grid%bc(ib)%nbnode
               transfer_array(grid%bc(ib)%ibnode(i)) &
                      = soln%bcsoln(ib)%mu_t_wf(i)
             end do
             call lmpi_xfer(transfer_array)
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes,                      &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%mu_t_wfglobal_bndry,         &
                            transfer_array,                                    &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry )
             deallocate(transfer_array)

           case('k_wallfunction_bc')
             allocate(transfer_array(size(soln%res,2)))
             transfer_array(:) = 0._dp
             do i = 1, grid%bc(ib)%nbnode
               transfer_array(grid%bc(ib)%ibnode(i)) &
                      = soln%bcsoln(ib)%k_wf(i)
             end do
             call lmpi_xfer(transfer_array)
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes,                      &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%k_wf_bcglobal_bndry,         &
                            transfer_array,                                    &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry )
             deallocate(transfer_array)

           case('omega_wallfunction_bc')
             allocate(transfer_array(size(soln%res,2)))
             transfer_array(:) = 0._dp
             do i = 1, grid%bc(ib)%nbnode
               transfer_array(grid%bc(ib)%ibnode(i)) &
                      = soln%bcsoln(ib)%omega_wf(i)
             end do
             call lmpi_xfer(transfer_array)
             call gather_global_bndry_data(                                    &
                            size(soln%res,2), nfacenodes,                      &
                            nface, nfacelocal,                                 &
                            global_bndry_data(ib)%omega_wf_bcglobal_bndry,     &
                            transfer_array,                                    &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry )
             deallocate(transfer_array)

           case default

         end select

       end do variable_loop

!      Output shear and heating from node-centered bcsoln without double
!      filtering as occurs in cq, cf_x, cf_y, and cf_z

       viscous_boundary_fluxes : if (need_viscous_flux)then

         temp_ptr(1)%p => soln%bcsoln(ib)%taux_flux_k(1,:)
         temp_ptr(2)%p => soln%bcsoln(ib)%tauy_flux_k(1,:)
         temp_ptr(3)%p => soln%bcsoln(ib)%tauz_flux_k(1,:)
         temp_ptr(4)%p => soln%bcsoln(ib)%energy_flux_j(1,:)
         temp_ptr(5)%p => soln%bcsoln(ib)%radiation_flux(:)
         temp_ptr(6)%p => soln%bcsoln(ib)%mdot(:)

         allocate(transfer_array(size(soln%res,2)))
         do nvar = 1, 6
           transfer_array(:) = 0._dp
           do i = 1,grid%bc(ib)%nbnode
             transfer_array(grid%bc(ib)%ibnode(i)) = temp_ptr(nvar)%p(i)
           end do
           call lmpi_xfer(transfer_array)
!          call gather_global_bndry_data( size(soln%res,2), nfacenodes, nface, &
!               nfacelocal, global_bndry_data(ib)%viscous_flux_bndry(nvar,:),  &
!               transfer_array, global_bndry_data(ib)%bndryl2g,                &
!               global_bndry_data(ib)%localf2n, global_bndry_data(ib)%f2n,     &
!               global_bndry_data(ib)%nfaceproc, temp_bndry, global_bndry,     &
!               grid%bc(ib)%nbnode, grid%bc(ib)%ibnode)
           call gather_global_bndry_data(                                      &
                          size(soln%res,2), nfacenodes, nface, nfacelocal,     &
                          global_bndry_data(ib)%viscous_flux_bndry(nvar,:),    &
                          transfer_array,                                      &
                          global_bndry_data(ib)%bndryl2g,                      &
                          global_bndry_data(ib)%localf2n,                      &
                          global_bndry_data(ib)%f2n,                           &
                          global_bndry_data(ib)%nfaceproc,                     &
                          temp_bndry, global_bndry)

         end do
         deallocate(transfer_array)

         global_bndry_data(ib)%viscous_flux_bndry(1:5,:) =                     &
           - global_bndry_data(ib)%viscous_flux_bndry(1:5,:)

         if ( soln%eqn_set == generic_gas ) then ! make heating and shear MKS
           global_bndry_data(ib)%viscous_flux_bndry(1:5,:) =                   &
             rho_ref*v_ref**2*global_bndry_data(ib)%viscous_flux_bndry(1:5,:)
           global_bndry_data(ib)%viscous_flux_bndry(4:5,:) =                   &
             1.e-4_dp*v_ref*global_bndry_data(ib)%viscous_flux_bndry(4:5,:)
         else if (soln%eqn_set == compressible ) then
           global_bndry_data(ib)%viscous_flux_bndry(:,:) =                     &
             rhozm*(uzm/xmach)**2*global_bndry_data(ib)%viscous_flux_bndry(:,:)
           global_bndry_data(ib)%viscous_flux_bndry(4:5,:) =                   &
            1.e-4_dp*(uzm/xmach)*global_bndry_data(ib)%viscous_flux_bndry(4:5,:)
         end if

       end if viscous_boundary_fluxes

       if (need_gradients) then
         call gather_global_bndry_data(                                        &
                        size(soln%gradx,2), nfacenodes, nface,  nfacelocal,    &
                        global_bndry_data(ib)%gradxglobal_bndry,               &
                        soln%gradx,                                            &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        soln%n_grd)
         call gather_global_bndry_data(                                        &
                        size(soln%grady,2), nfacenodes, nface,  nfacelocal,    &
                        global_bndry_data(ib)%gradyglobal_bndry,               &
                        soln%grady,                                            &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        soln%n_grd)
         call gather_global_bndry_data(                                        &
                        size(soln%gradz,2), nfacenodes, nface,  nfacelocal,    &
                        global_bndry_data(ib)%gradzglobal_bndry,               &
                        soln%gradz,                                            &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        soln%n_grd)
       end if

       surface_normals: if (need_normals) then

!        note: normal componentrs NOT individually selectable; if
!        need_normals = .T., all three components are made available

!        hack: gather_global_bndry_data loads data from volume-data arrays into
!        global surface arrays; since the normal data already is in (local)
!        surface arryays, use Peter's hack of creatting a temp volume array
!        and loading local surface data into that, then calling
!        gather_global_bndry_data as usual (note: same hack is used elsewhere
!        to handle bcsoln data)

         allocate(transfer_array(size(grid%x)))
         transfer_array(:) = 0.0_dp

         do i = 1,grid%bc(ib)%nbnode
           transfer_array(grid%bc(ib)%ibnode(i)) = grid%bc(ib)%bxn(i)
         end do
         call gather_global_bndry_data(                                        &
                        size(grid%x), nfacenodes, nface, nfacelocal,           &
                        global_bndry_data(ib)%bxnglobal_bndry,                 &
                        transfer_array,                                        &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        temp_bndry, global_bndry)

         do i = 1,grid%bc(ib)%nbnode
           transfer_array(grid%bc(ib)%ibnode(i)) = grid%bc(ib)%byn(i)
         end do
         call gather_global_bndry_data(                                        &
                        size(grid%x), nfacenodes, nface, nfacelocal,           &
                        global_bndry_data(ib)%bynglobal_bndry,                 &
                        transfer_array,                                        &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        temp_bndry, global_bndry)

         do i = 1,grid%bc(ib)%nbnode
           transfer_array(grid%bc(ib)%ibnode(i)) = grid%bc(ib)%bzn(i)
         end do
         call gather_global_bndry_data(                                        &
                        size(grid%x), nfacenodes, nface, nfacelocal,           &
                        global_bndry_data(ib)%bznglobal_bndry,                 &
                        transfer_array,                                        &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        temp_bndry, global_bndry)

         deallocate(transfer_array)

!        Reverse sign so that normals point out of the body; the code stores
!        boundary normals so that they point out of the fluid domain

         global_bndry_data(ib)%bxnglobal_bndry(:) =                            &
                                     - global_bndry_data(ib)%bxnglobal_bndry
         global_bndry_data(ib)%bynglobal_bndry(:) =                            &
                                     - global_bndry_data(ib)%bynglobal_bndry
         global_bndry_data(ib)%bznglobal_bndry(:) =                            &
                                     - global_bndry_data(ib)%bznglobal_bndry

       end if surface_normals

       if (need_slen .and. .not.cc_primal) then   ! cc - in face avg section

         call set_slen_wall_nonzero( grid%nbound, grid%bc, grid%slen )
         call gather_global_bndry_data(                                        &
                        size(grid%slen), nfacenodes, nface, nfacelocal,        &
                        global_bndry_data(ib)%slenglobal_bndry,                &
                        grid%slen,                                             &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        temp_bndry, global_bndry)
         call set_slen_wall_zero( grid%nbound, grid%bc, grid%slen )

       end if

       if (need_amut) then
         call gather_global_bndry_data(                                        &
                        size(soln%amut), nfacenodes, nface, nfacelocal,        &
                        global_bndry_data(ib)%amutglobal_bndry,                &
                        soln%amut,                                             &
                        global_bndry_data(ib)%bndryl2g,                        &
                        global_bndry_data(ib)%localf2n,                        &
                        global_bndry_data(ib)%f2n,                             &
                        global_bndry_data(ib)%nfaceproc,                       &
                        temp_bndry, global_bndry)
       end if

       if (need_avg_velocity .and. .not.(solid_wall)) then

!        for non-solid walls, simply  set average velocities to boundary
!        velocities -  solid walls are treated later

         global_bndry_data(ib)%uavgglobal_bndry(:) =                           &
                                      global_bndry_data(ib)%qglobal_bndry(2,:)
         global_bndry_data(ib)%vavgglobal_bndry(:) =                           &
                                      global_bndry_data(ib)%qglobal_bndry(3,:)
         global_bndry_data(ib)%wavgglobal_bndry(:) =                           &
                                      global_bndry_data(ib)%qglobal_bndry(4,:)

       end if

       if (need_tavg .or. boundary_animation_freq_tavg /= 0) then
         call gather_global_bndry_data(                                        &
                       size(soln%q_time_avg,2), nfacenodes, nface,  nfacelocal,&
                       global_bndry_data(ib)%tavg_qglobal_bndry,               &
                       soln%q_time_avg,                                        &
                       global_bndry_data(ib)%bndryl2g,                         &
                       global_bndry_data(ib)%localf2n,                         &
                       global_bndry_data(ib)%f2n,                              &
                       global_bndry_data(ib)%nfaceproc,                        &
                       2*soln%n_tot)
       end if

       if (need_t0_coords) then
         call gather_global_bndry_data(                                        &
                            size(grid%xat0), nfacenodes, nface, nfacelocal,    &
                            global_bndry_data(ib)%xat0global_bndry,            &
                            grid%xat0,                                         &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry)

         call gather_global_bndry_data(                                        &
                            size(grid%yat0), nfacenodes, nface, nfacelocal,    &
                            global_bndry_data(ib)%yat0global_bndry,            &
                            grid%yat0,                                         &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry)

         call gather_global_bndry_data(                                        &
                            size(grid%zat0), nfacenodes, nface, nfacelocal,    &
                            global_bndry_data(ib)%zat0global_bndry,            &
                            grid%zat0,                                         &
                            global_bndry_data(ib)%bndryl2g,                    &
                            global_bndry_data(ib)%localf2n,                    &
                            global_bndry_data(ib)%f2n,                         &
                            global_bndry_data(ib)%nfaceproc,                   &
                            temp_bndry, global_bndry)
       end if

!      deallocate temporary arrays

       deallocate(global_bndry)
       deallocate(temp_bndry)

!      finally, bcast the global boundary data that may be needed elsewhere
!      (for example, boundary-data slicing or aero_loads output); all other
!      global boundary data is needed only on the master, for subsequent output

       call lmpi_bcast(global_bndry_data(ib)%cpglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%cqglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%xglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%yglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%zglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%xat0global_bndry)
       call lmpi_bcast(global_bndry_data(ib)%yat0global_bndry)
       call lmpi_bcast(global_bndry_data(ib)%zat0global_bndry)
       call lmpi_bcast(global_bndry_data(ib)%qglobal_bndry)

     end do boundary_loop_1

!    2) gather any required face-based data into global node-based arrays
!       start by gathering local face-based data into global face-based arrays,
!       then average to nodes

     boundary_loop_2 : do ib=1,grid%nbound

       if (.not.(global_bndry_data(ib)%used)) cycle boundary_loop_2

       nfacelocal = global_bndry_data(ib)%nfacelocal
       nface      = global_bndry_data(ib)%nface
       nfacenodes = global_bndry_data(ib)%nfacenodes

       nbfacet = grid%bc(ib)%nbfacet
       nbfaceq = grid%bc(ib)%nbfaceq

       solid_wall = .false.
       if ( bc_used_for_distance_function(grid%bc(ib)%ibc) ) solid_wall = .true.

!      initialize

       global_bndry_data(ib)%cpglobal_bndry(:)   = 0._dp
       global_bndry_data(ib)%cqglobal_bndry(:)   = 0._dp
       global_bndry_data(ib)%cfxglobal_bndry(:)  = 0._dp
       global_bndry_data(ib)%cfyglobal_bndry(:)  = 0._dp
       global_bndry_data(ib)%cfzglobal_bndry(:)  = 0._dp
       if (solid_wall) then
         global_bndry_data(ib)%uavgglobal_bndry(:) = 0._dp
         global_bndry_data(ib)%vavgglobal_bndry(:) = 0._dp
         global_bndry_data(ib)%wavgglobal_bndry(:) = 0._dp
       end if

!      store the reference frame in which the global data was obtained

       global_bndry_data(ib)%ref_frame = ref_frame

!      2a) gather local face-based data into global face-based arrays

!      allocate some work arrays; availability of memory for these arrays has
!      already been checked in check_allocations

       allocate(cp_face(nface))
       allocate(cq_face(nface))
       allocate(cfx_face(nface))
       allocate(cfy_face(nface))
       allocate(cfz_face(nface))

       allocate(temp_face_data(nfacelocal))

       call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq,   &
                                    grid%bc(ib)%face_bit,                      &
                                    grid%bc(ib)%face_bitq,                     &
                                    soln%bcforce(ib)%cp_t,                     &
                                    soln%bcforce(ib)%cp_q,                     &
                                    global_bndry_data(ib)%nfaceproc,           &
                                    temp_face_data, cp_face)

       call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq,   &
                                    grid%bc(ib)%face_bit,                      &
                                    grid%bc(ib)%face_bitq,                     &
                                    soln%bcforce(ib)%cq_t,                     &
                                    soln%bcforce(ib)%cq_q,                     &
                                    global_bndry_data(ib)%nfaceproc,           &
                                    temp_face_data, cq_face)

       call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq,   &
                                    grid%bc(ib)%face_bit,                      &
                                    grid%bc(ib)%face_bitq,                     &
                                    soln%bcforce(ib)%cfx_t,                    &
                                    soln%bcforce(ib)%cfx_q,                    &
                                    global_bndry_data(ib)%nfaceproc,           &
                                    temp_face_data, cfx_face)

       call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq,   &
                                    grid%bc(ib)%face_bit,                      &
                                    grid%bc(ib)%face_bitq,                     &
                                    soln%bcforce(ib)%cfy_t,                    &
                                    soln%bcforce(ib)%cfy_q,                    &
                                    global_bndry_data(ib)%nfaceproc,           &
                                    temp_face_data, cfy_face)

       call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq,   &
                                    grid%bc(ib)%face_bit,                      &
                                    grid%bc(ib)%face_bitq,                     &
                                    soln%bcforce(ib)%cfz_t,                    &
                                    soln%bcforce(ib)%cfz_q,                    &
                                    global_bndry_data(ib)%nfaceproc,           &
                                    temp_face_data, cfz_face)

       if (need_slen .and. cc_primal) then

!        round-about way to get nodal values of slen from cc path, so we
!        can shoe-horn slen output into node-path mechanics

         allocate(slen_face(nface))
         allocate(slenface_t(nbfacet))
         allocate(slenface_q(nbfaceq))

         global_bndry_data(ib)%slenglobal_bndry(:) = 0._dp

         call fill_slen_face_arrays(grid, slenface_t, slenface_q, nbfacet,     &
                                    nbfaceq, ib)

         call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq, &
                                      grid%bc(ib)%face_bit,                    &
                                      grid%bc(ib)%face_bitq,                   &
                                      slenface_t,                              &
                                      slenface_q,                              &
                                      global_bndry_data(ib)%nfaceproc,         &
                                      temp_face_data, slen_face)

       else
         allocate(slen_face(1))
         allocate(slenface_t(1))
         allocate(slenface_q(1))
       end if

!      get average off-surface velocity components on solid surfaces for
!      computing surface streamlines

       if (need_avg_velocity .and. solid_wall) then
         allocate(uavg_face(nface))
         allocate(vavg_face(nface))
         allocate(wavg_face(nface))
         allocate(uavgt(max(1,nbfacet)))
         allocate(vavgt(max(1,nbfacet)))
         allocate(wavgt(max(1,nbfacet)))
         allocate(uavgq(max(1,nbfaceq)))
         allocate(vavgq(max(1,nbfaceq)))
         allocate(wavgq(max(1,nbfaceq)))
         need_avg_vel = .true.
       else
         allocate(uavg_face(1))
         allocate(vavg_face(1))
         allocate(wavg_face(1))
         allocate(uavgt(1))
         allocate(vavgt(1))
         allocate(wavgt(1))
         allocate(uavgq(1))
         allocate(vavgq(1))
         allocate(wavgq(1))
         need_avg_vel = .false.
       end if
       if (need_amutoffbody) then
         allocate(amutoffbody_face(nface))
         allocate(amutoffbodyt(max(1,nbfacet)))
         allocate(amutoffbodyq(max(1,nbfaceq)))
       else
         allocate(amutoffbody_face(1))
         allocate(amutoffbodyt(1))
         allocate(amutoffbodyq(1))
       end if

       if (need_avg_velocity .and. solid_wall) then

         if (cc_primal) then
           call compute_face_avg_velocity(soln%n_q, size(soln%qtavg,2),        &
                             nbfacet, nbfaceq, grid%bc(ib)%f2ntb,              &
                             grid%bc(ib)%f2nqb, soln%qtavg,                    &
                             grid%nelem, grid%elem, uavgt, vavgt, wavgt,       &
                             uavgq, vavgq, wavgq)
         else
           call compute_face_avg_velocity(soln%n_tot, size(soln%q_dof,2),      &
                             nbfacet, nbfaceq, grid%bc(ib)%f2ntb,              &
                             grid%bc(ib)%f2nqb, soln%q_dof,                    &
                             grid%nelem, grid%elem, uavgt, vavgt, wavgt,       &
                             uavgq, vavgq, wavgq)
         end if

         call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq, &
                                      grid%bc(ib)%face_bit,                    &
                                      grid%bc(ib)%face_bitq,                   &
                                      uavgt,                                   &
                                      uavgq,                                   &
                                      global_bndry_data(ib)%nfaceproc,         &
                                      temp_face_data, uavg_face)

         call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq, &
                                      grid%bc(ib)%face_bit,                    &
                                      grid%bc(ib)%face_bitq,                   &
                                      vavgt,                                   &
                                      vavgq,                                   &
                                      global_bndry_data(ib)%nfaceproc,         &
                                      temp_face_data, vavg_face)

         call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq, &
                                      grid%bc(ib)%face_bit,                    &
                                      grid%bc(ib)%face_bitq,                   &
                                      wavgt,                                   &
                                      wavgq,                                   &
                                      global_bndry_data(ib)%nfaceproc,         &
                                      temp_face_data, wavg_face)

       end if

       if (need_amutoffbody) then

         call compute_offbody_amut(size(soln%amut), nbfacet, nbfaceq,          &
                                   grid%bc(ib)%f2ntb, grid%bc(ib)%f2nqb,       &
                                   soln%amut, grid%nelem, grid%elem,           &
                                   amutoffbodyt, amutoffbodyq,                 &
                                   grid%bc(ib)%ibnode, grid%bc(ib)%nbnode,     &
                                   solid_wall)

         call gather_global_face_data(ib, nface, nfacelocal, nbfacet, nbfaceq, &
                                      grid%bc(ib)%face_bit,                    &
                                      grid%bc(ib)%face_bitq,                   &
                                      amutoffbodyt,                            &
                                      amutoffbodyq,                            &
                                      global_bndry_data(ib)%nfaceproc,         &
                                      temp_face_data, amutoffbody_face)

       end if

!      get nodal values of face-based data by averaging over all faces that
!      contain the node; note this must be done only on the master processor
!      since only the master has non-zero data in the global_bndry arrays

       if (lmpi_master) then

         if (need_slen .and. cc_primal) then
           need_avg_slen = .true.
         else
           need_avg_slen = .false.
         end if

         call average_face_based_data(nface, nfacenodes,                       &
                               global_bndry_data(ib)%f2n,                      &
                               global_bndry_data(ib)%xglobal_bndry,            &
                               global_bndry_data(ib)%yglobal_bndry,            &
                               global_bndry_data(ib)%zglobal_bndry,            &
                               global_bndry_data(ib)%cpglobal_bndry,           &
                               global_bndry_data(ib)%cqglobal_bndry,           &
                               global_bndry_data(ib)%cfxglobal_bndry,          &
                               global_bndry_data(ib)%cfyglobal_bndry,          &
                               global_bndry_data(ib)%cfzglobal_bndry,          &
                               global_bndry_data(ib)%uavgglobal_bndry,         &
                               global_bndry_data(ib)%vavgglobal_bndry,         &
                               global_bndry_data(ib)%wavgglobal_bndry,         &
                               global_bndry_data(ib)%slenglobal_bndry,         &
                               global_bndry_data(ib)%amutoffbodyglobal_bndry,  &
                               cp_face, cq_face,                               &
                               cfx_face, cfy_face, cfz_face,                   &
                               uavg_face, vavg_face, wavg_face,                &
                               amutoffbody_face,                               &
                               slen_face, need_avg_vel, need_avg_slen,         &
                               need_amutoffbody )

       end if

!      optionally use the shear data from bcsoln instead of the data collected
!      in the force module. The bcsoln data is not filtered by the
!      average_face_based_data process above

       if (use_bcsoln_stresses) then
         global_bndry_data(ib)%cfxglobal_bndry(:) =                            &
             2.0_dp*global_bndry_data(ib)%viscous_flux_bndry(1,:)/rhozm/uzm**2
         global_bndry_data(ib)%cfyglobal_bndry(:) =                            &
             2.0_dp*global_bndry_data(ib)%viscous_flux_bndry(2,:)/rhozm/uzm**2
         global_bndry_data(ib)%cfzglobal_bndry(:) =                            &
             2.0_dp*global_bndry_data(ib)%viscous_flux_bndry(3,:)/rhozm/uzm**2
       end if

!      deallocate temporary arrays

       deallocate(cp_face)
       deallocate(cq_face)
       deallocate(cfx_face)
       deallocate(cfy_face)
       deallocate(cfz_face)
       deallocate(temp_face_data)
       deallocate(uavg_face)
       deallocate(vavg_face)
       deallocate(wavg_face)
       deallocate(amutoffbody_face)
       deallocate(amutoffbodyt)
       deallocate(amutoffbodyq)
       deallocate(uavgt)
       deallocate(vavgt)
       deallocate(wavgt)
       deallocate(uavgq)
       deallocate(vavgq)
       deallocate(wavgq)

       deallocate(slen_face)
       deallocate(slenface_t)
       deallocate(slenface_q)

!      finally, bcast the global boundary data that may be needed elsewhere
!      (for example, boundary-data slicing or aero_loads output); all other
!      global boundary data is needed only on the master, for subsequent output

       call lmpi_bcast(global_bndry_data(ib)%cfxglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%cfyglobal_bndry)
       call lmpi_bcast(global_bndry_data(ib)%cfzglobal_bndry)

     end do boundary_loop_2

   end subroutine get_global_bndry_data


!========================= CHECK_GLOBAL_ARRAY_SIZES ==========================80
!
!  Checks sizes of global boundary arrays to insure they are large enough to
!  hold the requested output data - they are initially allocated to unit size
!
!  Note: the allocated arrays must be assigned a value to insure the allocation
!  really "counts" (i.e. memory may not be assigned until it is actually used)
!
!=============================================================================80

  subroutine check_global_array_sizes( nbound, n_tot, n_grd, n_turb, njac,     &
                                       adim, global_bndry_data, alloc_status )

    use nml_boundary_output,  only : n_output_variables, need_slen,            &
                                     output_variables, need_gradients,         &
                                     need_amut, need_avg_velocity,             &
                                     need_resid, need_turbresid,               &
                                     need_tavg, need_lambda,                   &
                                     need_viscous_flux, need_amutoffbody,      &
                                     need_normals
    use nml_global,           only : boundary_animation_freq_tavg
    use lmpi,                 only : lmpi_reduce, lmpi_bcast, lmpi_id

    integer,                    intent(in)    :: nbound, n_tot
    integer,                    intent(in)    :: n_grd, n_turb
    integer,                    intent(in)    :: njac, adim
    integer, dimension(nbound), intent(out)   :: alloc_status
    type(global_bndry_type),                                                   &
             dimension(nbound), intent(inout) :: global_bndry_data

    integer :: ib, nvar, status, mem_avail, iunit

    integer, dimension(nbound) :: my_alloc_status

  continue

    boundary_loop : do ib = 1,nbound

      my_alloc_status(ib) = 0

      if (.not.(global_bndry_data(ib)%used)) cycle boundary_loop

      if (enforce_mpi_memory_reserve) then
        iunit = lmpi_id+100
        mem_avail = available_memory(status, available_memory_verbose, iunit)
        if (mem_avail < mpi_memory_reserve) then
          my_alloc_status(ib) = 1
          cycle boundary_loop
        end if
      end if

      if (size(global_bndry_data(ib)%xglobal_bndry,1) /=                       &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%xglobal_bndry)
        allocate(global_bndry_data(ib)%xglobal_bndry(                          &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%xglobal_bndry(1))     ! for safety
        end if
        global_bndry_data(ib)%xglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%yglobal_bndry,1) /=                       &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%yglobal_bndry)
        allocate(global_bndry_data(ib)%yglobal_bndry(                          &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%yglobal_bndry(1))
        end if
        global_bndry_data(ib)%yglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%zglobal_bndry,1) /=                       &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%zglobal_bndry)
        allocate(global_bndry_data(ib)%zglobal_bndry(                          &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%zglobal_bndry(1))
        end if
        global_bndry_data(ib)%zglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%qglobal_bndry,2) /=                       &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%qglobal_bndry)
        allocate(global_bndry_data(ib)%qglobal_bndry(                          &
                            n_tot,                                             &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%qglobal_bndry(n_tot,1))
        end if
        global_bndry_data(ib)%qglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%cpglobal_bndry,1) /=                      &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%cpglobal_bndry)
        allocate(global_bndry_data(ib)%cpglobal_bndry(                         &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%cpglobal_bndry(1))
        end if
        global_bndry_data(ib)%cpglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%cqglobal_bndry,1) /=                      &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%cqglobal_bndry)
        allocate(global_bndry_data(ib)%cqglobal_bndry(                         &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%cqglobal_bndry(1))
        end if
        global_bndry_data(ib)%cqglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%cfxglobal_bndry,1) /=                     &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%cfxglobal_bndry)
        allocate(global_bndry_data(ib)%cfxglobal_bndry(                        &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%cfxglobal_bndry(1))
        end if
        global_bndry_data(ib)%cfxglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%cfyglobal_bndry,1) /=                     &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%cfyglobal_bndry)
        allocate(global_bndry_data(ib)%cfyglobal_bndry(                        &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%cfyglobal_bndry(1))
        end if
        global_bndry_data(ib)%cfyglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%cfzglobal_bndry,1) /=                     &
               global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%cfzglobal_bndry)
        allocate(global_bndry_data(ib)%cfzglobal_bndry(                        &
                            global_bndry_data(ib)%nfacenodes),                 &
                            stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%cfzglobal_bndry(1))
        end if
        global_bndry_data(ib)%cfzglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      if (size(global_bndry_data(ib)%l2gglobal_bndry,1) /=                     &
        global_bndry_data(ib)%nfacenodes) then
        deallocate(global_bndry_data(ib)%l2gglobal_bndry)
        allocate(global_bndry_data(ib)%l2gglobal_bndry(                        &
          global_bndry_data(ib)%nfacenodes),                                   &
          stat=status)
        if (status /= 0) then
          my_alloc_status(ib) = 1
          allocate(global_bndry_data(ib)%l2gglobal_bndry(1))
        end if
        global_bndry_data(ib)%l2gglobal_bndry = 0._dp
        if (status /= 0) cycle boundary_loop
      end if

      variable_loop : do nvar = 1,n_output_variables

        select case(trim(adjustl(output_variables(nvar))))

          case('uuprime','vvprime','wwprime','uvprime','uwprime','vwprime')
            if (n_turb==7) then
            if (size(global_bndry_data(ib)%turbglobal_bndry,2) /=              &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%turbglobal_bndry)
              allocate(global_bndry_data(ib)%turbglobal_bndry(                 &
                                  n_turb,                                      &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%turbglobal_bndry(n_turb,1))
              end if
              global_bndry_data(ib)%turbglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if
            else
            continue
            end if

          case('turb1','turb2','turb3','turb4','turb5','turb6','turb7')
            if (size(global_bndry_data(ib)%turbglobal_bndry,2) /=              &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%turbglobal_bndry)
              allocate(global_bndry_data(ib)%turbglobal_bndry(                 &
                                  n_turb,                                      &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%turbglobal_bndry(n_turb,1))
              end if
              global_bndry_data(ib)%turbglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('iblank')
            if (size(global_bndry_data(ib)%iblankglobal_bndry,1) /=            &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%iblankglobal_bndry)
              allocate(global_bndry_data(ib)%iblankglobal_bndry(               &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%iblankglobal_bndry(1))
              end if
              global_bndry_data(ib)%iblankglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('imesh')
            if (size(global_bndry_data(ib)%imeshglobal_bndry,1) /=             &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%imeshglobal_bndry)
              allocate(global_bndry_data(ib)%imeshglobal_bndry(                &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%imeshglobal_bndry(1))
              end if
              global_bndry_data(ib)%imeshglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('processor_id')
            if (size(global_bndry_data(ib)%proc_idglobal_bndry,1) /=           &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%proc_idglobal_bndry)
              allocate(global_bndry_data(ib)%proc_idglobal_bndry(              &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%proc_idglobal_bndry(1))
              end if
              global_bndry_data(ib)%proc_idglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('volume')
            if (size(global_bndry_data(ib)%volglobal_bndry,1) /=               &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%volglobal_bndry)
              allocate(global_bndry_data(ib)%volglobal_bndry(                  &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%volglobal_bndry(1))
              end if
              global_bndry_data(ib)%volglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('res_gcl')
            if (size(global_bndry_data(ib)%res_gclglobal_bndry,2) /=           &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%res_gclglobal_bndry)
              allocate(global_bndry_data(ib)%res_gclglobal_bndry(              &
                                  1, global_bndry_data(ib)%nfacenodes),        &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%res_gclglobal_bndry(1,1))
              end if
              global_bndry_data(ib)%res_gclglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('utau_wf')
            if (size(global_bndry_data(ib)%utau_wfglobal_bndry,1) /=           &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%utau_wfglobal_bndry)
              allocate(global_bndry_data(ib)%utau_wfglobal_bndry(              &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%utau_wfglobal_bndry(1))
              end if
              global_bndry_data(ib)%utau_wfglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('phi_wf')
            if (size(global_bndry_data(ib)%phi_wfglobal_bndry,1) /=            &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%phi_wfglobal_bndry)
              allocate(global_bndry_data(ib)%phi_wfglobal_bndry(               &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%phi_wfglobal_bndry(1))
              end if
              global_bndry_data(ib)%phi_wfglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('mu_t_wf')
            if (size(global_bndry_data(ib)%mu_t_wfglobal_bndry,1) /=           &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%mu_t_wfglobal_bndry)
              allocate(global_bndry_data(ib)%mu_t_wfglobal_bndry(              &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%mu_t_wfglobal_bndry(1))
              end if
              global_bndry_data(ib)%mu_t_wfglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('k_wallfunction_bc')
            if (size(global_bndry_data(ib)%k_wf_bcglobal_bndry,1) /=           &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%k_wf_bcglobal_bndry)
              allocate(global_bndry_data(ib)%k_wf_bcglobal_bndry(              &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%k_wf_bcglobal_bndry(1))
              end if
              global_bndry_data(ib)%k_wf_bcglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case('omega_wallfunction_bc')
            if (size(global_bndry_data(ib)%omega_wf_bcglobal_bndry,1) /=       &
                     global_bndry_data(ib)%nfacenodes) then
              deallocate(global_bndry_data(ib)%omega_wf_bcglobal_bndry)
              allocate(global_bndry_data(ib)%omega_wf_bcglobal_bndry(          &
                                  global_bndry_data(ib)%nfacenodes),           &
                                  stat=status)
              if (status /= 0) then
                my_alloc_status(ib) = 1
                allocate(global_bndry_data(ib)%omega_wf_bcglobal_bndry(1))
              end if
              global_bndry_data(ib)%omega_wf_bcglobal_bndry = 0._dp
              if (status /= 0) cycle boundary_loop
            end if

          case default

        end select

      end do variable_loop

      if (need_viscous_flux) then
        if (size(global_bndry_data(ib)%viscous_flux_bndry,2) /=                &
            global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%viscous_flux_bndry)
          allocate(global_bndry_data(ib)%viscous_flux_bndry(                   &
                              6, global_bndry_data(ib)%nfacenodes),            &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%viscous_flux_bndry(6,1))
          end if
          global_bndry_data(ib)%viscous_flux_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_gradients) then
        if (size(global_bndry_data(ib)%gradxglobal_bndry,2) /=                 &
            global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%gradxglobal_bndry)
          allocate(global_bndry_data(ib)%gradxglobal_bndry(                    &
                              n_grd, global_bndry_data(ib)%nfacenodes),        &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%gradxglobal_bndry(n_grd,1))
          end if
          global_bndry_data(ib)%gradxglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
        if (size(global_bndry_data(ib)%gradyglobal_bndry,2) /=                 &
            global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%gradyglobal_bndry)
          allocate(global_bndry_data(ib)%gradyglobal_bndry(                    &
                              n_grd, global_bndry_data(ib)%nfacenodes),        &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%gradyglobal_bndry(n_grd,1))
          end if
          global_bndry_data(ib)%gradyglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
        if (size(global_bndry_data(ib)%gradzglobal_bndry,2) /=                 &
            global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%gradzglobal_bndry)
          allocate(global_bndry_data(ib)%gradzglobal_bndry(                    &
                              n_grd, global_bndry_data(ib)%nfacenodes),        &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%gradzglobal_bndry(n_grd,1))
          end if
          global_bndry_data(ib)%gradzglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_slen) then
        if (size(global_bndry_data(ib)%slenglobal_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%slenglobal_bndry)
          allocate(global_bndry_data(ib)%slenglobal_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%slenglobal_bndry(1))
          end if
          global_bndry_data(ib)%slenglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_lambda) then
        if (size(global_bndry_data(ib)%rlamglobal_bndry,2) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%rlamglobal_bndry)
          allocate(global_bndry_data(ib)%rlamglobal_bndry(adim,                &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%rlamglobal_bndry(adim,1))
          end if
          global_bndry_data(ib)%rlamglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_amut) then
        if (size(global_bndry_data(ib)%amutglobal_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%amutglobal_bndry)
          allocate(global_bndry_data(ib)%amutglobal_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%amutglobal_bndry(1))
          end if
          global_bndry_data(ib)%amutglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_tavg .or. boundary_animation_freq_tavg /= 0) then
        if (size(global_bndry_data(ib)%tavg_qglobal_bndry,2) /=                &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%tavg_qglobal_bndry)
          allocate(global_bndry_data(ib)%tavg_qglobal_bndry(                   &
                              2*n_tot, global_bndry_data(ib)%nfacenodes),      &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%tavg_qglobal_bndry(2*n_tot,1))
          end if
          global_bndry_data(ib)%tavg_qglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_avg_velocity) then
        if (size(global_bndry_data(ib)%uavgglobal_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%uavgglobal_bndry)
          allocate(global_bndry_data(ib)%uavgglobal_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%uavgglobal_bndry(1))
          end if
          global_bndry_data(ib)%uavgglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
        if (size(global_bndry_data(ib)%vavgglobal_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%vavgglobal_bndry)
          allocate(global_bndry_data(ib)%vavgglobal_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%vavgglobal_bndry(1))
          end if
          global_bndry_data(ib)%vavgglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
        if (size(global_bndry_data(ib)%wavgglobal_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%wavgglobal_bndry)
          allocate(global_bndry_data(ib)%wavgglobal_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%wavgglobal_bndry(1))
          end if
          global_bndry_data(ib)%wavgglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      surface_normals: if (need_normals) then

!       note: surface normal components are not individually selectable;
!       all three components are made available if need_normals = .T.

        if (size(global_bndry_data(ib)%bxnglobal_bndry,1) /=             &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%bxnglobal_bndry)
          allocate(global_bndry_data(ib)%bxnglobal_bndry(                &
                              global_bndry_data(ib)%nfacenodes),         &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%bxnglobal_bndry(1))
          end if
          global_bndry_data(ib)%bxnglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if

        if (size(global_bndry_data(ib)%bynglobal_bndry,1) /=             &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%bynglobal_bndry)
          allocate(global_bndry_data(ib)%bynglobal_bndry(                &
                              global_bndry_data(ib)%nfacenodes),         &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%bynglobal_bndry(1))
          end if
          global_bndry_data(ib)%bynglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if

        if (size(global_bndry_data(ib)%bznglobal_bndry,1) /=             &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%bznglobal_bndry)
          allocate(global_bndry_data(ib)%bznglobal_bndry(                &
                              global_bndry_data(ib)%nfacenodes),         &
                             stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%bznglobal_bndry(1))
          end if
          global_bndry_data(ib)%bznglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if

      end if surface_normals

      if (need_amutoffbody) then
        if (size(global_bndry_data(ib)%amutoffbodyglobal_bndry,1) /=           &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%amutoffbodyglobal_bndry)
          allocate(global_bndry_data(ib)%amutoffbodyglobal_bndry(              &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%amutoffbodyglobal_bndry(1))
          end if
          global_bndry_data(ib)%amutoffbodyglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_resid) then
        if (size(global_bndry_data(ib)%resglobal_bndry,2) /=                   &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%resglobal_bndry)
          allocate(global_bndry_data(ib)%resglobal_bndry(                      &
                              njac, global_bndry_data(ib)%nfacenodes),         &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%resglobal_bndry(njac,1))
          end if
          global_bndry_data(ib)%resglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_turbresid) then
        if (size(global_bndry_data(ib)%turresglobal_bndry,2) /=                &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%turresglobal_bndry)
          allocate(global_bndry_data(ib)%turresglobal_bndry(                   &
                              n_turb, global_bndry_data(ib)%nfacenodes),       &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%turresglobal_bndry(n_turb,1))
          end if
          global_bndry_data(ib)%turresglobal_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (need_t0_coords) then
        if (size(global_bndry_data(ib)%xat0global_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%xat0global_bndry)
          allocate(global_bndry_data(ib)%xat0global_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%xat0global_bndry(1))     ! for safety
          end if
          global_bndry_data(ib)%xat0global_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
        if (size(global_bndry_data(ib)%yat0global_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%yat0global_bndry)
          allocate(global_bndry_data(ib)%yat0global_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%yat0global_bndry(1))     ! for safety
          end if
          global_bndry_data(ib)%yat0global_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
        if (size(global_bndry_data(ib)%zat0global_bndry,1) /=                  &
                 global_bndry_data(ib)%nfacenodes) then
          deallocate(global_bndry_data(ib)%zat0global_bndry)
          allocate(global_bndry_data(ib)%zat0global_bndry(                     &
                              global_bndry_data(ib)%nfacenodes),               &
                              stat=status)
          if (status /= 0) then
            my_alloc_status(ib) = 1
            allocate(global_bndry_data(ib)%zat0global_bndry(1))     ! for safety
          end if
          global_bndry_data(ib)%zat0global_bndry = 0._dp
          if (status /= 0) cycle boundary_loop
        end if
      end if

      if (enforce_mpi_memory_reserve) then
        iunit = lmpi_id+100
        mem_avail = available_memory(status, available_memory_verbose, iunit)
        if (mem_avail < mpi_memory_reserve) then
          my_alloc_status(ib) = 1
        end if
      end if

    end do boundary_loop

    call lmpi_reduce(my_alloc_status, alloc_status)
    call lmpi_bcast(alloc_status)

  end subroutine check_global_array_sizes

!=========================== CHECK_TEMP_ARRAY_SIZES ==========================80
!
!  Checks whether memory can be allocated for the temporary arrays needed to
!  gather the requested global boundary data
!
!  Note: the allocated arrays must be assigned a value to insure the allocation
!  really "counts" (i.e. memory may not be assigned until it is actually used)
!
!=============================================================================80

  subroutine check_temp_array_sizes(nbound, bc, global_bndry_data, alloc_status)

    use lmpi,                only : lmpi_reduce, lmpi_bcast, lmpi_nproc, lmpi_id
    use bc_types,            only : bcgrid_type
    use bc_names,            only : bc_used_for_distance_function
    use nml_boundary_output, only : need_avg_velocity

    integer,                              intent(in)    :: nbound
    integer,           dimension(nbound), intent(out)   :: alloc_status
    type(bcgrid_type), dimension(nbound), intent(in)    :: bc
    type(global_bndry_type),                                                   &
                       dimension(nbound), intent(inout) :: global_bndry_data

    integer :: ib, status
    integer :: status1,  status2,  status3, status4,  status5,  status6
    integer :: status7,  status8,  status9, status10, status11, status12
    integer :: status13, status14, status15
    integer :: nface, nfacelocal, nbfacet, nbfaceq, mem_avail, iunit

    integer, dimension(nbound) :: my_alloc_status

    real(dp), dimension(:,:), allocatable :: temp_bndry, global_bndry
    real(dp), dimension(:),   allocatable :: cp_face, cq_face
    real(dp), dimension(:),   allocatable :: cfx_face, cfy_face, cfz_face
    real(dp), dimension(:),   allocatable :: temp_face_data
    real(dp), dimension(:),   allocatable :: uavg_face, vavg_face, wavg_face
    real(dp), dimension(:),   allocatable :: uavgt, vavgt, wavgt
    real(dp), dimension(:),   allocatable :: uavgq, vavgq, wavgq

    logical :: solid_wall

  continue

    boundary_loop : do ib = 1,nbound

      my_alloc_status(ib) = 0

      if (.not.(global_bndry_data(ib)%used)) cycle boundary_loop

      if (enforce_mpi_memory_reserve) then
        iunit = lmpi_id+100
        mem_avail = available_memory(status, available_memory_verbose, iunit)
        if (mem_avail < mpi_memory_reserve) then
          my_alloc_status(ib) = 1
          cycle boundary_loop
        end if
      end if

      nface      = global_bndry_data(ib)%nface
      nfacelocal = global_bndry_data(ib)%nfacelocal
      nbfacet    = bc(ib)%nbfacet
      nbfaceq    = bc(ib)%nbfaceq

      solid_wall = .false.
      if ( bc_used_for_distance_function(bc(ib)%ibc) ) solid_wall = .true.

!     1) temp arrays for gathering node-based data

      if (lmpi_nproc == 1) then
        allocate(global_bndry(4,1), stat=status1)
        allocate(temp_bndry(4,1),   stat=status2)
      else
        allocate(global_bndry(4,nface),           stat=status1)
        if (status1 /= 0) allocate(global_bndry(4,1))
        allocate(temp_bndry(4,max(nfacelocal,1)), stat=status2)
        if (status2 /= 0) allocate(temp_bndry(4,1))
      end if

      global_bndry = 0._dp
      temp_bndry   = 0._dp

      if (status1 == 0) then
        deallocate(global_bndry)
      else
        my_alloc_status(ib) = 1
      end if
      if (status2 == 0) then
        deallocate(temp_bndry)
      else
        my_alloc_status(ib) = 1
      end if

!     2) temp arrays for gathering face-based data

      allocate(cp_face(nface),  stat=status1)
      if (status1 /= 0)  allocate(cp_face(1))
      cp_face = 0._dp
      allocate(cq_face(nface),  stat=status2)
      if (status2 /= 0)  allocate(cq_face(1))
      cq_face = 0._dp
      allocate(cfx_face(nface), stat=status3)
      if (status3 /= 0)  allocate(cfx_face(1))
      cfx_face = 0._dp
      allocate(cfy_face(nface), stat=status4)
      if (status4 /= 0)  allocate(cfy_face(1))
      cfy_face = 0._dp
      allocate(cfz_face(nface), stat=status5)
      if (status5 /= 0)  allocate(cfz_face(1))
      cfz_face = 0._dp

      if (lmpi_nproc == 1) then
        allocate(temp_face_data(1), stat=status6)
      else
        allocate(temp_face_data(nfacelocal), stat=status6)
      if (status6 /= 0)  allocate(temp_face_data(1))
      end if

      temp_face_data = 0._dp

      if (need_avg_velocity .and. solid_wall) then
        allocate(uavg_face(nface),      stat=status7)
        if (status7 /= 0)  allocate(uavg_face(1))
        uavg_face = 0.
        allocate(vavg_face(nface),      stat=status8)
        if (status8 /= 0)  allocate(vavg_face(1))
        vavg_face = 0.
        allocate(wavg_face(nface),      stat=status9)
        if (status9 /= 0)  allocate(wavg_face(1))
        wavg_face = 0.
        allocate(uavgt(max(1,nbfacet)), stat=status10)
        if (status10 /= 0)  allocate(uavgt(1))
        uavgt = 0.
        allocate(vavgt(max(1,nbfacet)), stat=status11)
        if (status11 /= 0)  allocate(vavgt(1))
        vavgt = 0.
        allocate(wavgt(max(1,nbfacet)), stat=status12)
        if (status12 /= 0)  allocate(wavgt(1))
        wavgt = 0.
        allocate(uavgq(max(1,nbfaceq)), stat=status13)
        if (status13 /= 0)  allocate(uavgq(1))
        uavgq = 0.
        allocate(vavgq(max(1,nbfaceq)), stat=status14)
        if (status14 /= 0)  allocate(vavgq(1))
        vavgq = 0.
        allocate(wavgq(max(1,nbfaceq)), stat=status15)
        if (status15 /= 0)  allocate(wavgq(1))
        wavgq = 0.
      else
        allocate(uavg_face(1), stat=status7)
        uavg_face = 0.
        allocate(vavg_face(1), stat=status8)
        vavg_face = 0.
        allocate(wavg_face(1), stat=status9)
        wavg_face = 0.
        allocate(uavgt(1),     stat=status10)
        uavgt = 0.
        allocate(vavgt(1),     stat=status11)
        vavgt = 0.
        allocate(wavgt(1),     stat=status12)
        wavgt = 0.
        allocate(uavgq(1),     stat=status13)
        uavgq = 0.
        allocate(vavgq(1),     stat=status14)
        vavgq = 0.
        allocate(wavgq(1),     stat=status15)
        wavgq = 0.
      end if

      if (status1 == 0) then
        deallocate(cp_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status2 == 0) then
        deallocate(cq_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status3 == 0) then
        deallocate(cfx_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status4 == 0) then
        deallocate(cfy_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status5 == 0) then
        deallocate(cfz_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status6 == 0) then
        deallocate(temp_face_data)
      else
        my_alloc_status(ib) = 1
      end if
      if (status7 == 0) then
        deallocate(uavg_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status8 == 0) then
        deallocate(vavg_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status9 == 0) then
        deallocate(wavg_face)
      else
        my_alloc_status(ib) = 1
      end if
      if (status10 == 0) then
        deallocate(uavgt)
      else
        my_alloc_status(ib) = 1
      end if
      if (status11 == 0) then
        deallocate(vavgt)
      else
        my_alloc_status(ib) = 1
      end if
      if (status12 == 0) then
        deallocate(wavgt)
      else
        my_alloc_status(ib) = 1
      end if
      if (status13 == 0) then
        deallocate(uavgq)
      else
        my_alloc_status(ib) = 1
      end if
      if (status14 == 0) then
        deallocate(vavgq)
      else
        my_alloc_status(ib) = 1
      end if
      if (status15 == 0) then
        deallocate(wavgq)
      else
        my_alloc_status(ib) = 1
      end if

      if (enforce_mpi_memory_reserve) then
        iunit = lmpi_id+100
        mem_avail = available_memory(status, available_memory_verbose, iunit)
        if (mem_avail < mpi_memory_reserve) then
          my_alloc_status(ib) = 1
        end if
      end if

    end do boundary_loop

    call lmpi_reduce(my_alloc_status, alloc_status)
    call lmpi_bcast(alloc_status)

  end subroutine check_temp_array_sizes

!============================ GET_GLOBAL_BNDRY_CONNECTIVITY ==================80
!
!  Gets connectivity data to construct a global (unpartitioned) representation
!  of the current solution on the boundary
!
!=============================================================================80

   subroutine get_global_bndry_connectivity(bc, grid, global_bndry_data,       &
                                            save_memory)

     use grid_types,        only : grid_type
     use bc_types,          only : bcgrid_type
     use sort,              only : binary_search
     use lmpi,              only : lmpi_master, lmpi_nproc,                    &
                                   lmpi_reduce, lmpi_bcast,                    &
                                   lmpi_allgather, lmpi_allgatherv
     use system_extensions, only : se_open

     type(grid_type),             intent(in)  :: grid
     type(bcgrid_type),           intent(in)  :: bc
     type(global_bndry_type),     intent(out) :: global_bndry_data
     logical,           optional, intent(in)  :: save_memory

     logical :: mark_and_sweep
     logical :: verbose = .false.
     logical :: debug   = .false.

     integer :: nface, nfacelocal, nfacenodes, iunit, face, node

     integer, dimension(:), allocatable :: global2local

     continue

!    get a local and global count of the number of boundary faces

     nfacelocal = local_unique_bndry_faces(bc)

     call lmpi_reduce(nfacelocal,nface)
     call lmpi_bcast(nface)

     if (verbose .and. lmpi_master )                                           &
       write(*,*) " collecting global boundary connectivity for ",nface,       &
                  " faces..."

!    the local face to node pointer f2n has a LEADING dimension of 4
!    for face iface in order to handle both tria and quad faces:
!      f2n(1,iface) ...node 1
!      f2n(2,iface) ...node 2
!      f2n(3,iface) ...node 3
!      f2n(4,iface) ...node 4 if quad; 0 if tri
!      note that f2n here is node,face whereas ftntb and f2nqb are face,node !!

     allocate(global_bndry_data%f2n(4,nface))

!    fill the local f2n for the requested output bc type; note we need to get
!    the global node numbering before we can gather them from the processors

     if (lmpi_nproc == 1) then

       call fill_local_bndry_f2n(bc,nface,global_bndry_data%f2n)

       allocate(global_bndry_data%localf2n(4,1))
       allocate(global_bndry_data%nfaceproc(1))

     else

       allocate(global_bndry_data%localf2n(4,max(nfacelocal,1)))

       call fill_local_bndry_f2n(bc,nfacelocal,global_bndry_data%localf2n)

       convert_f2n_to_global_index : do face = 1, nfacelocal
         do node = 1, 4
           if (global_bndry_data%localf2n(node,face) > 0 )                     &
             global_bndry_data%localf2n(node,face) =                           &
                                grid%l2g(global_bndry_data%localf2n(node,face))
         end do
       end do convert_f2n_to_global_index

       allocate(global_bndry_data%nfaceproc(lmpi_nproc))

       call lmpi_allgather(nfacelocal,global_bndry_data%nfaceproc)
       call lmpi_allgatherv(global_bndry_data%localf2n,                        &
                            global_bndry_data%nfaceproc, global_bndry_data%f2n)

     end if

     dump_global_face : if (debug) then
       iunit = 92
       call se_open (iunit,file='f2nglobal.debug',            &
                     status='unknown')
       do face = 1, nface
         write(iunit,'(5i12)')face,global_bndry_data%f2n(1,face),              &
                                   global_bndry_data%f2n(2,face),              &
                                   global_bndry_data%f2n(3,face),              &
                                   global_bndry_data%f2n(4,face)
       end do
       close(iunit)
     end if dump_global_face

!    get the global node numbers for the nodes in the boundary faces

     if (verbose .and. lmpi_master ) write(*,*) " compute_bndryl2g..."

     call compute_bndryl2g(nface,global_bndry_data%f2n,nfacenodes,             &
                                                    global_bndry_data%bndryl2g)

     if (verbose .and. lmpi_master )                                         &
       write(*,*) " there are ",nfacenodes," output boundary nodes..."

     if (verbose .and. lmpi_master )                                         &
       write(*,*) " apply local numbering to faces..."

     mark_and_sweep = .true.
     if ( present(save_memory) ) mark_and_sweep = ( .not. save_memory )

     save_time_or_memory : if (mark_and_sweep) then

       allocate(global2local(grid%nnodesg))

       do node = 1, nfacenodes
         global2local(global_bndry_data%bndryl2g(node)) = node
       end do
       lookup_f2n_local_index : do face = 1, nface
         do node = 1, 4
           if (global_bndry_data%f2n(node,face) > 0) then
             global_bndry_data%f2n(node,face) =                                &
                             global2local(global_bndry_data%f2n(node,face))
           end if
         end do
       end do lookup_f2n_local_index

       deallocate(global2local)

     else

! ToDo: this next loop could be parallelized (distributed across processors)

       search_f2n_local_index : do face = 1, nface
         do node = 1, 4
           global_bndry_data%f2n(node,face) = binary_search(nfacenodes,        &
                   global_bndry_data%bndryl2g, global_bndry_data%f2n(node,face))
         end do
       end do search_f2n_local_index

     end if save_time_or_memory

     dump_local_face : if (debug) then
       iunit = 92
       call se_open (iunit,file='localf2n.debug',             &
                     status='unknown')
       do face = 1, nface
         write(iunit,'(5i12)')face,global_bndry_data%f2n(1,face),              &
                                   global_bndry_data%f2n(2,face),              &
                                   global_bndry_data%f2n(3,face),              &
                                   global_bndry_data%f2n(4,face)
       end do
       close(iunit)
     end if dump_local_face

!    rerun fill_local_bndry_f2n to get local numbering

     if (lmpi_nproc > 1) then

       call fill_local_bndry_f2n(bc,nfacelocal,global_bndry_data%localf2n)

     end if

!    set the scalar integers in the global_bndry_data type

     global_bndry_data%nface      = nface
     global_bndry_data%nfacenodes = nfacenodes
     global_bndry_data%nfacelocal = nfacelocal

   end subroutine get_global_bndry_connectivity

!============================ FILL_LOCAL_BNDRY_F2N ===========================80
!
!  Fills the face to node (f2n) array for faces on the partition
!
!=============================================================================80

   subroutine fill_local_bndry_f2n( bc, faces, f2n )

     use bc_types, only : bcgrid_type

     type(bcgrid_type),           intent(in)  :: bc
     integer,                     intent(in)  :: faces
     integer, dimension(4,faces), intent(out) :: f2n

     integer :: face, totalfaces

     continue

     totalfaces = 0

     tri_faces : do face = 1, bc%nbfacet
       if (bc%face_bit(face) == 1) then
         totalfaces = totalfaces + 1
         f2n(1,totalfaces) = bc%ibnode(bc%f2ntb(face,1))
         f2n(2,totalfaces) = bc%ibnode(bc%f2ntb(face,2))
         f2n(3,totalfaces) = bc%ibnode(bc%f2ntb(face,3))
         f2n(4,totalfaces) = 0
       end if
     end do tri_faces

     quad_faces : do face = 1, bc%nbfaceq
       if (bc%face_bitq(face) == 1) then
         totalfaces = totalfaces + 1
         f2n(1,totalfaces) = bc%ibnode(bc%f2nqb(face,1))
         f2n(2,totalfaces) = bc%ibnode(bc%f2nqb(face,2))
         f2n(3,totalfaces) = bc%ibnode(bc%f2nqb(face,3))
         f2n(4,totalfaces) = bc%ibnode(bc%f2nqb(face,4))
       end if
     end do quad_faces

     if (faces /= totalfaces)                                                  &
       write(*,*) "fill_local_bndry_f2n, face miscount"

   end subroutine fill_local_bndry_f2n

!============================== COMPUTE_BNDRYL2G =============================80
!
!  Determine the global node number of each boundary node on the global surface
!
!=============================================================================80

   subroutine compute_bndryl2g( nface, f2n, nfacenodes, bndryl2g )

     use sort,        only : heap_sort
     use allocations, only : my_alloc_ptr

     integer,                     intent(in)  :: nface
     integer, dimension(4,nface), intent(in)  :: f2n
     integer,                     intent(out) :: nfacenodes
     integer, dimension(:),       pointer     :: bndryl2g

     integer :: face, node, currentnode, ntotal, nunique

     integer, dimension (:), pointer :: totalnodes, totalnodesort

     continue

     ntotal = 0
     count_total_nodes : do face = 1, nface
       do node = 1, 4
         if (f2n(node,face) > 0 ) ntotal = ntotal + 1
       end do
     end do count_total_nodes

     call my_alloc_ptr(totalnodes, ntotal)

     ntotal=0
     fill_total_nodes : do face = 1, nface
       do node = 1, 4
         if (f2n(node,face) > 0 ) then
           ntotal = ntotal + 1
           totalnodes(ntotal) = f2n(node,face)
         end if
       end do
     end do fill_total_nodes

     call my_alloc_ptr(totalnodesort, ntotal)
     call heap_sort(ntotal, totalnodes, totalnodesort)

     nunique = 0
     currentnode = 0
     count_unique_nodes : do node = 1, ntotal
       if (currentnode /= totalnodes(totalnodesort(node)) ) then
         nunique = nunique + 1
         currentnode = totalnodes(totalnodesort(node))
       end if
     end do count_unique_nodes

     allocate(bndryl2g(nunique))
     bndryl2g = 0

     nunique = 0
     currentnode = 0
     fill_unique_nodes : do node = 1, ntotal
       if (currentnode /= totalnodes(totalnodesort(node)) ) then
         nunique = nunique + 1
         currentnode = totalnodes(totalnodesort(node))
         bndryl2g(nunique) = currentnode
       end if
     end do fill_unique_nodes

     nfacenodes = nunique

     deallocate(totalnodes)
     deallocate(totalnodesort)

   end subroutine compute_bndryl2g

!========================= LOCAL_UNIQUE_BNDRY_FACES ==========================80
!
!  Counts the number of unique boundary faces on the partition
!
!=============================================================================80

   integer function local_unique_bndry_faces(bc)

     use bc_types, only : bcgrid_type

     type(bcgrid_type), intent(in) :: bc

     integer :: face, totalfaces

     continue

     totalfaces = 0

     tri_faces : do face = 1, bc%nbfacet
       if (bc%face_bit(face) == 1) totalfaces = totalfaces + 1
     end do tri_faces

     quad_faces : do face = 1, bc%nbfaceq
       if (bc%face_bitq(face) == 1) totalfaces = totalfaces + 1
     end do quad_faces

     local_unique_bndry_faces = totalfaces

   end function local_unique_bndry_faces

!======================== DEALLOCATE_GLOBAL_BNDRY_DATA =======================80
!
! Deallocates memory for the global_bndry_data
!
!=============================================================================80

  subroutine deallocate_global_bndry_data( global_bndry_data )

    type(global_bndry_type), intent(inout) :: global_bndry_data

    integer :: status

  continue

    status = 0

    if (associated(global_bndry_data%f2n))                                   &
        deallocate(global_bndry_data%f2n,stat=status)
    if (associated(global_bndry_data%localf2n))                              &
        deallocate(global_bndry_data%localf2n,stat=status)
    if (associated(global_bndry_data%nfaceproc))                             &
        deallocate(global_bndry_data%nfaceproc,stat=status)
    if (associated(global_bndry_data%bndryl2g))                              &
        deallocate(global_bndry_data%bndryl2g,stat=status)
    if (associated(global_bndry_data%xglobal_bndry))                         &
        deallocate(global_bndry_data%xglobal_bndry,stat=status)
    if (associated(global_bndry_data%yglobal_bndry))                         &
        deallocate(global_bndry_data%yglobal_bndry,stat=status)
    if (associated(global_bndry_data%zglobal_bndry))                         &
        deallocate(global_bndry_data%zglobal_bndry,stat=status)
    if (associated(global_bndry_data%xat0global_bndry))                      &
        deallocate(global_bndry_data%xat0global_bndry,stat=status)
    if (associated(global_bndry_data%yat0global_bndry))                      &
        deallocate(global_bndry_data%yat0global_bndry,stat=status)
    if (associated(global_bndry_data%zat0global_bndry))                      &
        deallocate(global_bndry_data%zat0global_bndry,stat=status)
    if (associated(global_bndry_data%qglobal_bndry))                         &
        deallocate(global_bndry_data%qglobal_bndry,stat=status)
    if (associated(global_bndry_data%cpglobal_bndry))                        &
        deallocate(global_bndry_data%cpglobal_bndry,stat=status)
    if (associated(global_bndry_data%cqglobal_bndry))                        &
        deallocate(global_bndry_data%cqglobal_bndry,stat=status)
    if (associated(global_bndry_data%cfxglobal_bndry))                       &
        deallocate(global_bndry_data%cfxglobal_bndry,stat=status)
    if (associated(global_bndry_data%cfyglobal_bndry))                       &
        deallocate(global_bndry_data%cfyglobal_bndry,stat=status)
    if (associated(global_bndry_data%cfzglobal_bndry))                       &
        deallocate(global_bndry_data%cfzglobal_bndry,stat=status)
    if (associated(global_bndry_data%tavg_qglobal_bndry))                    &
        deallocate(global_bndry_data%tavg_qglobal_bndry,stat=status)
    if (associated(global_bndry_data%slenglobal_bndry))                      &
        deallocate(global_bndry_data%slenglobal_bndry,stat=status)
    if (associated(global_bndry_data%amutglobal_bndry))                      &
        deallocate(global_bndry_data%amutglobal_bndry,stat=status)
    if (associated(global_bndry_data%turbglobal_bndry))                      &
        deallocate(global_bndry_data%turbglobal_bndry,stat=status)
    if (associated(global_bndry_data%l2gglobal_bndry))                    &
        deallocate(global_bndry_data%l2gglobal_bndry,stat=status)
    if (associated(global_bndry_data%iblankglobal_bndry))                    &
        deallocate(global_bndry_data%iblankglobal_bndry,stat=status)
    if (associated(global_bndry_data%imeshglobal_bndry))                     &
        deallocate(global_bndry_data%imeshglobal_bndry,stat=status)
    if (associated(global_bndry_data%gradxglobal_bndry))                     &
        deallocate(global_bndry_data%gradxglobal_bndry,stat=status)
    if (associated(global_bndry_data%gradyglobal_bndry))                     &
        deallocate(global_bndry_data%gradyglobal_bndry,stat=status)
    if (associated(global_bndry_data%gradzglobal_bndry))                     &
        deallocate(global_bndry_data%gradzglobal_bndry,stat=status)
    if (associated(global_bndry_data%uavgglobal_bndry))                      &
        deallocate(global_bndry_data%uavgglobal_bndry,stat=status)
    if (associated(global_bndry_data%vavgglobal_bndry))                      &
        deallocate(global_bndry_data%vavgglobal_bndry,stat=status)
    if (associated(global_bndry_data%wavgglobal_bndry))                      &
        deallocate(global_bndry_data%wavgglobal_bndry,stat=status)
    if (associated(global_bndry_data%volglobal_bndry))                       &
        deallocate(global_bndry_data%volglobal_bndry,stat=status)
    if (associated(global_bndry_data%resglobal_bndry))                       &
        deallocate(global_bndry_data%resglobal_bndry,stat=status)
    if (associated(global_bndry_data%turresglobal_bndry))                    &
        deallocate(global_bndry_data%turresglobal_bndry,stat=status)
    if (associated(global_bndry_data%res_gclglobal_bndry))                   &
        deallocate(global_bndry_data%res_gclglobal_bndry,stat=status)
    if (associated(global_bndry_data%rlamglobal_bndry))                      &
        deallocate(global_bndry_data%rlamglobal_bndry,stat=status)
    if (associated(global_bndry_data%proc_idglobal_bndry))                   &
        deallocate(global_bndry_data%proc_idglobal_bndry,stat=status)
    if (associated(global_bndry_data%amutoffbodyglobal_bndry))               &
        deallocate(global_bndry_data%amutoffbodyglobal_bndry,stat=status)

    if (associated(global_bndry_data%bxnglobal_bndry))                       &
        deallocate(global_bndry_data%bxnglobal_bndry,stat=status)
    if (associated(global_bndry_data%bynglobal_bndry))                       &
        deallocate(global_bndry_data%bynglobal_bndry,stat=status)
    if (associated(global_bndry_data%bznglobal_bndry))                       &
        deallocate(global_bndry_data%bznglobal_bndry,stat=status)

    if (associated(global_bndry_data%viscous_flux_bndry))                    &
        deallocate(global_bndry_data%viscous_flux_bndry,stat=status)
    if (associated(global_bndry_data%utau_wfglobal_bndry))                   &
        deallocate(global_bndry_data%utau_wfglobal_bndry,stat=status)
    if (associated(global_bndry_data%phi_wfglobal_bndry))                    &
        deallocate(global_bndry_data%phi_wfglobal_bndry,stat=status)
    if (associated(global_bndry_data%mu_t_wfglobal_bndry))                   &
        deallocate(global_bndry_data%mu_t_wfglobal_bndry,stat=status)
    if (associated(global_bndry_data%k_wf_bcglobal_bndry))                   &
        deallocate(global_bndry_data%k_wf_bcglobal_bndry,stat=status)
    if (associated(global_bndry_data%omega_wf_bcglobal_bndry))               &
        deallocate(global_bndry_data%omega_wf_bcglobal_bndry,stat=status)

    if (status /= 0) write(*,*) ' error: deallocate_global_bndry_data'

  end subroutine deallocate_global_bndry_data

!=============================== SLICE_BNDRY_DATA ============================80
!
!  Slices Cp and Cf data on global boundaries at user-specified stations
!  The stations may be aligned in either x, y, or z directions.
!
!  If the grid is either static, non-moving boundary, or, if moving:
!    1) slicing is done in the body reference frame (rigid or deforming)
!    2) if the boundary is deforming, then slice_initial_coords option = .T.
!
!  The implication of these assumptions is that the computation of the
!  interpolation coefficients need only be done once; otherwise they are
!  recomputed each time this routine is called
!
!  The result is Cp and Cf data on a sorted collection of
!  segments that lie in the cutting direction.
!
!  There is a mind-numbing sequence of transformations that take place here;
!  to help follow the sequence, when a transform is initally performed,
!  it is tagged as
!
!  !   ===> this is transform N <===   (N=1,2,...) (a recent change has reduced
!                                      the effective number of transforms to 2)
!
!  Later, when that transform is undone, it is tagged as
!
!  !   ===> this negates transform N <===
!
!  Upon exit, soln%global_bndry_data should be as it was on entering
!
!=============================================================================80

  subroutine slice_bndry_data( grid, soln, time_step, nml_path, init,          &
                               geometry_only )

    use solution_types,      only : soln_type, compressible
    use grid_types,          only : grid_type
    use allocations,         only : my_alloc_ptr
    use lmpi,                only : lmpi_master, lmpi_die
    use lmpi,                only : lmpi_bcast
    use system_extensions,   only : se_flush, se_open
    use file_utils,          only : available_unit
    use info_depr,           only : xmach
    use nml_rotor_data,      only : overset_rotor
    use fluid,               only : gamma
    use moving_body_types,   only : moving_body, observer
    use rotors,              only : rotor, get_rotor_id_from_ref_frame
    use file_utils,          only : file_exists
    use grid_motion_helpers, only : transform_coord, transform_vector,         &
                                    transform_normal
    use nml_slice_data,      only : read_nml_slice_data, slice_location,       &
                                    slice_x, slice_y, slice_z, nslices,        &
                                    xx_box_max, yy_box_max, zz_box_max,        &
                                    xx_box_min, yy_box_min, zz_box_min,        &
                                    slice_xmc, slice_ymc, slice_zmc,           &
                                    n_bndrys_to_slice, bndrys_to_slice,        &
                                    slice_frame, le_def, te_def, chord_dir,    &
                                    slice_group, slice_increment,              &
                                    use_local_chord, replicate_all_bodies,     &
                                    tecplot_slice_output, corner_angle,        &
                                    output_sectional_forces, maxslices,        &
                                    slice_initial_coords, maxbnds,             &
                                    custom_transform, output_in_slice_coords
    use boundary_slicing,    only : init_slicing_data,                         &
                                    gbl_bndry_to_inertial_frame,               &
                                    gbl_bndry_from_inertial_frame,             &
                                    reorient_slice_by_direction,               &
                                    transform_inertial_to_body,                &
                                    transform_body_to_inertial,                &
                                    calculate_slice_size,                      &
                                    compute_interpolation_coeff,               &
                                    reallocate_slice,                          &
                                    deallocate_global_slice_data,              &
                                    sortseg4, get_le_te, get_c4, slice_type

    integer,           intent(in)    :: time_step
    type(soln_type),   intent(inout) :: soln
    type(grid_type),   intent(in)    :: grid
    character(*),      intent(in)    :: nml_path
    logical, optional, intent(in)    :: init, geometry_only

    integer, parameter :: maxloops = 200

    character(80)                                 :: info_file

    logical,  dimension(maxslices),          save :: bound_box
    logical,                                 save :: initialized = .false.
    logical,                                 save :: compute_interp_coeff=.true.
    logical,                                 save :: write_info = .true.
    logical, dimension(:), allocatable,      save :: bndry_not_sliced

    integer                                       :: bnd, ib, i, ii, section
    integer                                       :: ix, ixm1, nx, nseg
    integer                                       :: group
    integer                                       :: rotor_id, blade_id
    integer                                       :: slice_frame_id
    integer, dimension(:), allocatable,      save :: bndry_frame_id
    integer                                       :: kbox, iu
    integer                                       :: node1, node2, node3, node4
    integer                                       :: loop, nstart
    integer                                       :: pindex

    real(dp), dimension(:,:,:), allocatable, save :: box_transform
    real(dp), dimension(:,:,:), allocatable, save :: inv_box_transform
    real(dp), dimension(:,:,:), allocatable, save :: inv_custom_transform

    real(dp), dimension(4,4)                      :: transform
    real(dp)                                      :: x_box_min,x_box_max
    real(dp)                                      :: y_box_min,y_box_max
    real(dp)                                      :: z_box_min,z_box_max
    real(dp)                                      :: conv
    real(dp)                                      :: x1, y1, z1, x2, y2, z2
    real(dp)                                      :: x3, y3, z3, x4, y4, z4
    real(dp)                                      :: xs1, xs2
    real(dp)                                      :: ys1, ys2
    real(dp)                                      :: cp1, cp2, cps1, cps2
    real(dp)                                      :: cfx1, cfx2, cfxs1, cfxs2
    real(dp)                                      :: cfy1, cfy2, cfys1, cfys2
    real(dp)                                      :: cfz1, cfz2, cfzs1, cfzs2
    real(dp)                                      :: slice_loc
    real(dp)                                      :: pinf, cpfactor
    real(dp),      dimension(:),       pointer :: x_bndry => null()
    real(dp),      dimension(:),       pointer :: y_bndry => null()
    real(dp),      dimension(:),       pointer :: z_bndry => null()

    type(slice_type), dimension(:), allocatable, save :: slice

    logical :: already_sorted, skip_force_output

    continue

    if (present(init) .and. initialized) return

    skip_force_output = .false.
    if (present(geometry_only)) skip_force_output = .true.

    conv = 180_dp/acos(-1.0_dp)

    if ( soln%eqn_set == compressible ) then
      pinf     = 1.0_dp/gamma
      cpfactor = 2.0_dp/(gamma*xmach*xmach)
      pindex   = 5                 ! location of pressure in the qnode array
    else
      pinf     = 1.0_dp
      cpfactor = 2.0_dp
      pindex   = 1                 ! location of pressure in the qnode array
    end if

    initial_pass : if (.not. initialized) then

!     check for no-longer-supported input file; now via fun3d.nml

      if ( file_exists('slice_global_bndry.input') ) then
        if (lmpi_master) write(*,*)                                            &
                            "File slice_global_bndry.input found--stopping"
        if (lmpi_master) write(*,*)                                            &
                            "Please put &slice_data namelist in fun3d.nml"
        call lmpi_die
      end if

      call read_nml_slice_data( nml_path )

      call init_slicing_data(grid, soln, maxslices, maxbnds, nslices,          &
                           slice_location, slice_increment,                    &
                           slice_x, slice_y, slice_z,                          &
                           xx_box_max, yy_box_max, zz_box_max,                 &
                           xx_box_min, yy_box_min, zz_box_min,                 &
                           chord_dir, le_def, te_def, corner_angle,            &
                           n_bndrys_to_slice, bndrys_to_slice,                 &
                           replicate_all_bodies,                               &
                           slice_frame, custom_transform,                      &
                           slice_xmc, slice_ymc, slice_zmc,                    &
                           slice, global_slice_data, slice_group,              &
                           nslice_groups, slice_group_beg, slice_group_end,    &
                           box_transform, inv_box_transform,                   &
                           inv_custom_transform,                               &
                           bound_box, bndry_frame_id, bndry_not_sliced)

      call lmpi_bcast(use_local_chord)
      call lmpi_bcast(tecplot_slice_output)
      call lmpi_bcast(output_sectional_forces)
      call lmpi_bcast(slice_initial_coords)

      initialized = .true.

      return

    end if initial_pass

!   begin slicing process

    if (lmpi_master) then
      write(*,*)
      write(*,'(a,i5)')' Slicing global boundary data for time step ', time_step
    end if

!   make sure the required global_bndry_data is in the inertial reference
!   frame at the start
!   ===> this is transform 1 <===
    call gbl_bndry_to_inertial_frame(grid, soln, bndry_not_sliced,             &
                                     bndry_frame_id)

!   first time through, output some data to be used for sections/slices

    iu = available_unit()
    info_file = 'slice.info'

    info_0 : if (lmpi_master .and. write_info) then

      call se_open(unit=iu, file=info_file)
      rewind(iu)
      write(iu,*)
      write(iu,*) ' Number of slices       ', nslices
      write(iu,*) ' Number of slice groups ',nslice_groups
      write(iu,'(a)') ' Slice_Group   Begin_Slice   End_Slice'
      do group = 1,nslice_groups
        write(iu,'(i12,i14,i12)') group, slice_group_beg(group),               &
                                  slice_group_end(group)
      end do
      do section = 1, nslices
        write(iu,*)
        write(iu,*) ' Parameters for slice number ', section
          write(iu,*) '   slice group ',slice_group(section)
        if (slice_x(section)) then
          write(iu,*) '   slice location, x = ', slice_location(section)
        else if (slice_y(section)) then
          write(iu,*) '   slice location, y = ', slice_location(section)
        else if (slice_z(section)) then
          write(iu,*) '   slice location, z = ', slice_location(section)
        else
        end if
        write(iu,*) '   use bounding box    ', bound_box(section)
        if (bound_box(section)) then
          write(iu,*) '   box xmin, xmax      ', xx_box_min(section),          &
                                               xx_box_max(section)
          write(iu,*) '   box ymin, ymax      ', yy_box_min(section),          &
                                               yy_box_max(section)
          write(iu,*) '   box zmin, zmax      ', zz_box_min(section),          &
                                               zz_box_max(section)
        end if
        if (slice_xmc(section) /= huge(1._dp)) then
          write(iu,*) '   sectional xmc       ', slice_xmc(section)
        else
          write(iu,*) '   sectional xmc       ', 'x at quarter chord of slice'
        end if
        if (slice_ymc(section) /= huge(1._dp)) then
          write(iu,*) '   sectional ymc       ', slice_ymc(section)
        else
          write(iu,*) '   sectional ymc       ', 'y at quarter chord of slice'
        end if
        if (slice_zmc(section) /= huge(1._dp)) then
          write(iu,*) '   sectional zmc       ', slice_zmc(section)
        else
          write(iu,*) '   sectional zmc       ', 'z at quarter chord of slice'
        end if
        write(iu,*) '   boundaries to search=',n_bndrys_to_slice(section)
        do bnd = 1,n_bndrys_to_slice(section)
          write(iu,"(1x,3(a,i0))") '     ',bndrys_to_slice(section,bnd),&
                      ' bnd=',bnd,' of ',n_bndrys_to_slice(section)
        end do
        write(iu,*) '   reference frame'
        write(iu,*) '     ',"'",trim(slice_frame(section)),"'"
        write(iu,*) '   reference frame id'
        write(iu,*) '     ',global_slice_data(section)%ref_frame
        if (global_slice_data(section)%le_def == 1) then
          write(iu,*) '   leading edge: fwd-most point'
        else if (global_slice_data(section)%le_def > 1) then
          write(iu,*) '   leading edge: max. distance to TE over fwd-most ',   &
                           global_slice_data(section)%le_def, ' points'
        else
          write(iu,*) '   leading edge: least-squares parabolic fit of ',      &
                          abs(global_slice_data(section)%le_def), ' points'
        end if
        if (global_slice_data(section)%te_def == 1) then
          write(iu,*) '   trailing edge: aft-most point'
        else if (global_slice_data(section)%te_def > 1) then
          write(iu,*) '   trailing edge: corner detection over aft-most  ',    &
                           global_slice_data(section)%te_def, ' segments'
        else
          write(iu,*) '   trailing edge: least-squares parabolic fit of ',     &
                          abs(global_slice_data(section)%te_def), ' points'
        end if

      end do

    end if info_0

!   to suppress compiler warnings

    xs1   = 0._dp
    ys1   = 0._dp
    cps1  = 0._dp
    cfxs1 = 0._dp
    cfys1 = 0._dp
    cfzs1 = 0._dp
    xs2   = 0._dp
    ys2   = 0._dp
    cps2  = 0._dp
    cfxs2 = 0._dp
    cfys2 = 0._dp
    cfzs2 = 0._dp

! SECTION_LOOP
!   below, section_loop is divided into three principle sections. the first
!   section (boundary_loop_0) goes through and figures out the required
!   array sizes, but doesn't actually the interpolation coefficients.
!   After the required array sizes are determined, the second section
!   (boundary_loop_1) repeats the search process and computes the
!   interpolation coefficients, but does not actually do the interpolations.
!   In the third sction (segment_loop), the actual interpolations are performed

!   loop over slice locations

    section_loop : do section = 1, nslices

      slice_loc = slice_location(section)

      call reorient_slice_by_direction(box_transform(1:4,1:4,section),         &
                                       xx_box_max(section),                    &
                                       yy_box_max(section),                    &
                                       zz_box_max(section),                    &
                                       xx_box_min(section),                    &
                                       yy_box_min(section),                    &
                                       zz_box_min(section),                    &
                                       x_box_max, y_box_max, z_box_max,        &
                                       x_box_min, y_box_min, z_box_min,        &
                                       slice_xmc(section),                     &
                                       slice_ymc(section),                     &
                                       slice_zmc(section))

!     for moving bodies, slicing is typically done in the reference frame
!     fixed with the moving body; slice_transform takes the body from the
!     inertial frame to this reference frame; conversely inv_slice_transform
!     goes from inertial to body reference frame; for computational efficiency,
!     we combine this (potential) slice_transform with one than locally
!     reorients the data depending on the slice direction (follows box5/6
!     code convention where all slicing is done on z=constant planes)
!     ===> this is transform 2 <===
      call transform_inertial_to_body(box_transform(1:4,1:4,section),          &
                                      custom_transform(section,1:4,1:4),       &
                                      global_slice_data(section)%ref_frame,    &
                                      n_bndrys_to_slice, bndrys_to_slice,      &
                                      slice_initial_coords, soln, section)

!     get the interpolation cefficients if we don't already have them

      compute_coefficients : if (compute_interp_coeff) then

!       if allocated from a previous call to this routine, deallocate
!       some arrays before allocating them for the current geometry

        call deallocate_global_slice_data( global_slice_data(section) )

!       first section: count the number of intersections (ix) and
!       segments (nseg) so we can size some arrays
        call calculate_slice_size(soln, n_bndrys_to_slice(section),            &
                                  bndrys_to_slice(section,:), slice_loc,       &
                                  slice_initial_coords, bound_box(section),    &
                                  x_box_max, y_box_max, z_box_max,             &
                                  x_box_min, y_box_min, z_box_min,             &
                                  ix, kbox, nseg)

!       check that some segments are found for this slice

        if (nseg == 0) then
          if (lmpi_master) then
            write(*,*)'Stopping: problem in slice_bndry_data'
            write(*,*)'  no segments found for slice number',section
            if (slice_x(section)) then
              write(*,*)'  slice requested at x =',slice_loc
            else if (slice_y(section)) then
              write(*,*)'  slice requested at y =',slice_loc
            else if (slice_z(section)) then
              write(*,*)'  slice requested at z =',slice_loc
            end if
            if (.not. slice_initial_coords) then
              write(*,*)'  if this is an aeroelastic/rotorcraft case, you may',&
                        ' want to try setting slice_initial_coords = .true.',  &
                        ' in the &slice_data namelist'
            end if
            call se_flush
          end if
          call lmpi_die
        end if

!       now that we know the final ix and nseg counts, we can allocate
!       what we will need (min size of 1 for safety)

        nx   = max(1, ix)
        nseg = max(1,nseg)

        call reallocate_slice(slice(section), maxloops, nx, nseg)

!       second section: repeat the search and determine the interpolation coeffs
!       but don't actually do the interpolations
        call compute_interpolation_coeff(soln, n_bndrys_to_slice(section),     &
                                  bndrys_to_slice(section,:), slice_loc,       &
                                  slice_initial_coords, bound_box(section),    &
                                  x_box_max, y_box_max, z_box_max,             &
                                  x_box_min, y_box_min, z_box_min,             &
                                  slice(section), ix, kbox, nseg)

!       if box option was used, see if we found anything in the specified box

        if (kbox == 0 .and. bound_box(section)) then
          write(*,*) 'No nodes found in box for eta = ', section
          cycle section_loop
        end if

!       allocate the arrays used to hold the final results

        call my_alloc_ptr(global_slice_data(section)%xs1, nseg)
        call my_alloc_ptr(global_slice_data(section)%ys1, nseg)
        call my_alloc_ptr(global_slice_data(section)%zs1, nseg)
        call my_alloc_ptr(global_slice_data(section)%xs2, nseg)
        call my_alloc_ptr(global_slice_data(section)%ys2, nseg)
        call my_alloc_ptr(global_slice_data(section)%zs2, nseg)
        call my_alloc_ptr(global_slice_data(section)%cps1, nseg)
        call my_alloc_ptr(global_slice_data(section)%cps2, nseg)
        call my_alloc_ptr(global_slice_data(section)%cfxs1, nseg)
        call my_alloc_ptr(global_slice_data(section)%cfxs2, nseg)
        call my_alloc_ptr(global_slice_data(section)%cfys1, nseg)
        call my_alloc_ptr(global_slice_data(section)%cfys2, nseg)
        call my_alloc_ptr(global_slice_data(section)%cfzs1, nseg)
        call my_alloc_ptr(global_slice_data(section)%cfzs2, nseg)
        call my_alloc_ptr(global_slice_data(section)%xnorm, nseg)
        call my_alloc_ptr(global_slice_data(section)%ynorm, nseg)
        call my_alloc_ptr(global_slice_data(section)%znorm, nseg)

      end if compute_coefficients

!     we have the interpolation coefficients, do the interpolations

      segment_loop : do nseg = 1,slice(section)%nseg

        ix   = slice(section)%ix(nseg)
        ixm1 = slice(section)%ixm1(nseg)

        do ii = ixm1,ix

          ib    = slice(section)%ibnd(ii)
          node1 = slice(section)%node_11(ii)
          node2 = slice(section)%node_22(ii)

          x_bndry => null()
          y_bndry => null()
          z_bndry => null()

!---rtb
!         if (slice_initial_coords) then
!           x_bndry => soln%global_bndry_data(ib)%xat0global_bndry
!           y_bndry => soln%global_bndry_data(ib)%yat0global_bndry
!           z_bndry => soln%global_bndry_data(ib)%zat0global_bndry
!         else
!           x_bndry => soln%global_bndry_data(ib)%xglobal_bndry
!           y_bndry => soln%global_bndry_data(ib)%yglobal_bndry
!           z_bndry => soln%global_bndry_data(ib)%zglobal_bndry
!         end if

            x_bndry => soln%global_bndry_data(ib)%xglobal_bndry
            y_bndry => soln%global_bndry_data(ib)%yglobal_bndry
            z_bndry => soln%global_bndry_data(ib)%zglobal_bndry
!---rtb

          x1 = x_bndry(node1)
          x2 = x_bndry(node2)

          y1 = y_bndry(node1)
          y2 = y_bndry(node2)

          z1 = z_bndry(node1)
          z2 = z_bndry(node2)

          cp1 = cpfactor *                                                     &
            (soln%global_bndry_data(ib)%qglobal_bndry(pindex,node1)/pinf-1._dp)
          cp2 = cpfactor *                                                     &
            (soln%global_bndry_data(ib)%qglobal_bndry(pindex,node2)/pinf-1._dp)

          cfx1 = soln%global_bndry_data(ib)%cfxglobal_bndry(node1)
          cfx2 = soln%global_bndry_data(ib)%cfxglobal_bndry(node2)

          cfy1 = soln%global_bndry_data(ib)%cfyglobal_bndry(node1)
          cfy2 = soln%global_bndry_data(ib)%cfyglobal_bndry(node2)

          cfz1 = soln%global_bndry_data(ib)%cfzglobal_bndry(node1)
          cfz2 = soln%global_bndry_data(ib)%cfzglobal_bndry(node2)

          if (ii == ixm1) then
            xs1   = x1 + slice(section)%factor(ii)*(x2-x1)
            ys1   = y1 + slice(section)%factor(ii)*(y2-y1)
            cps1  = cp1  + slice(section)%factor(ii)*(cp2-cp1)
            cfxs1 = cfx1 + slice(section)%factor(ii)*(cfx2-cfx1)
            cfys1 = cfy1 + slice(section)%factor(ii)*(cfy2-cfy1)
            cfzs1 = cfz1 + slice(section)%factor(ii)*(cfz2-cfz1)
          else if (ii == ix) then
            xs2   = x1 + slice(section)%factor(ii)*(x2-x1)
            ys2   = y1 + slice(section)%factor(ii)*(y2-y1)
            cps2  = cp1  + slice(section)%factor(ii)*(cp2-cp1)
            cfxs2 = cfx1 + slice(section)%factor(ii)*(cfx2-cfx1)
            cfys2 = cfy1 + slice(section)%factor(ii)*(cfy2-cfy1)
            cfzs2 = cfz1 + slice(section)%factor(ii)*(cfz2-cfz1)
          else
            if (lmpi_master) then
              write(*,*) 'error in slice_bndry_data'
              call se_flush
              call lmpi_die
            end if
          end if

        end do

!       face normal (minus to account for outward pointing normal)

        ib    = slice(section)%ibnd(ix)

        x_bndry => null()
        y_bndry => null()
        z_bndry => null()

!---rtb
!       if (slice_initial_coords) then
!         x_bndry => soln%global_bndry_data(ib)%xat0global_bndry
!         y_bndry => soln%global_bndry_data(ib)%yat0global_bndry
!         z_bndry => soln%global_bndry_data(ib)%zat0global_bndry
!       else
!         x_bndry => soln%global_bndry_data(ib)%xglobal_bndry
!         y_bndry => soln%global_bndry_data(ib)%yglobal_bndry
!         z_bndry => soln%global_bndry_data(ib)%zglobal_bndry
!       end if

          x_bndry => soln%global_bndry_data(ib)%xglobal_bndry
          y_bndry => soln%global_bndry_data(ib)%yglobal_bndry
          z_bndry => soln%global_bndry_data(ib)%zglobal_bndry
!---rtb

        node1 = slice(section)%node_1(ix)
        node2 = slice(section)%node_2(ix)
        node3 = slice(section)%node_3(ix)
        node4 = slice(section)%node_4(ix)

        x1 = x_bndry(node1)
        x2 = x_bndry(node2)
        x3 = x_bndry(node3)
        x4 = x_bndry(node4)

        y1 = y_bndry(node1)
        y2 = y_bndry(node2)
        y3 = y_bndry(node3)
        y4 = y_bndry(node4)

        z1 = z_bndry(node1)
        z2 = z_bndry(node2)
        z3 = z_bndry(node3)
        z4 = z_bndry(node4)

        if (slice(section)%face_type(nseg) == 'tria') then
          global_slice_data(section)%xnorm(nseg) =                             &
                -0.5_dp*( (y2-y1)*(z3-z1) - (z2-z1)*(y3-y1) )
          global_slice_data(section)%ynorm(nseg) =                             &
                -0.5_dp*( (z2-z1)*(x3-x1) - (x2-x1)*(z3-z1) )
          global_slice_data(section)%znorm(nseg) =                             &
                -0.5_dp*( (x2-x1)*(y3-y1) - (y2-y1)*(x3-x1) )
        else
          global_slice_data(section)%xnorm(nseg) =                             &
                -0.5_dp*( (y3 - y1)*(z4 - z2) - (z3 - z1)*(y4 - y2) )
          global_slice_data(section)%ynorm(nseg) =                             &
                -0.5_dp*( (z3 - z1)*(x4 - x2) - (x3 - x1)*(z4 - z2) )
          global_slice_data(section)%znorm(nseg) =                             &
                -0.5_dp*( (x3 - x1)*(y4 - y2) - (y3 - y1)*(x4 - x2) )
        end if

        global_slice_data(section)%xs1(nseg)   = xs1
        global_slice_data(section)%ys1(nseg)   = ys1
        global_slice_data(section)%xs2(nseg)   = xs2
        global_slice_data(section)%ys2(nseg)   = ys2
        global_slice_data(section)%zs1(nseg)   = slice_loc
        global_slice_data(section)%zs2(nseg)   = slice_loc
        global_slice_data(section)%cps1(nseg)  = cps1
        global_slice_data(section)%cps2(nseg)  = cps2
        global_slice_data(section)%cfxs1(nseg) = cfxs1
        global_slice_data(section)%cfxs2(nseg) = cfxs2
        global_slice_data(section)%cfys1(nseg) = cfys1
        global_slice_data(section)%cfys2(nseg) = cfys2
        global_slice_data(section)%cfzs1(nseg) = cfzs1
        global_slice_data(section)%cfzs2(nseg) = cfzs2

      end do segment_loop

!     sort the segments we have found

      nseg = slice(section)%nseg

      if (compute_interp_coeff) then
        already_sorted = .false.
      else
        already_sorted = .true.
      end if

      call sortseg4(nseg,                                                     &
                    slice(section)%nloops,                                    &
                    maxloops,                                                 &
                    slice(section)%npts,                                      &
                    global_slice_data(section)%xs1,                           &
                    global_slice_data(section)%xs2,                           &
                    global_slice_data(section)%ys1,                           &
                    global_slice_data(section)%ys2,                           &
                    global_slice_data(section)%zs1,                           &
                    global_slice_data(section)%zs2,                           &
                    global_slice_data(section)%cps1,                          &
                    global_slice_data(section)%cps2,                          &
                    global_slice_data(section)%cfxs1,                         &
                    global_slice_data(section)%cfxs2,                         &
                    global_slice_data(section)%cfys1,                         &
                    global_slice_data(section)%cfys2,                         &
                    global_slice_data(section)%cfzs1,                         &
                    global_slice_data(section)%cfzs2,                         &
                    global_slice_data(section)%xnorm,                         &
                    global_slice_data(section)%ynorm,                         &
                    global_slice_data(section)%znorm,                         &
                    slice(section)%sptr,                                      &
                    slice(section)%old_to_new,                                &
                    slice(section)%swap,                                      &
                    already_sorted)

!     set some remaining data for the global_data_slice derived type

!     note: these are the input moment center values and may get reset later
!           they MUST be set/reset to slice_*mc values here
      global_slice_data(section)%xmc = slice_xmc(section)
      global_slice_data(section)%ymc = slice_ymc(section)
      global_slice_data(section)%zmc = slice_zmc(section)

      if (compute_interp_coeff) then

        if (slice_x(section)) then
          global_slice_data(section)%slice_dir = 1
        else if (slice_y(section)) then
          global_slice_data(section)%slice_dir = 2
        else
          global_slice_data(section)%slice_dir = 3
        end if
        global_slice_data(section)%slice_val = slice_loc
        global_slice_data(section)%nseg      = nseg
        global_slice_data(section)%nloops    = slice(section)%nloops

        call my_alloc_ptr(global_slice_data(section)%ibeg,                     &
                          slice(section)%nloops)
        call my_alloc_ptr(global_slice_data(section)%iend,                     &
                          slice(section)%nloops)

        nstart = 1
        do loop = 1,slice(section)%nloops
          global_slice_data(section)%ibeg(loop) = nstart
          global_slice_data(section)%iend(loop) = nstart                       &
                                                + slice(section)%npts(loop) - 1
          nstart = nstart+slice(section)%npts(loop)
        end do

      end if

!     determine le and te location of the section

      if (.not. slice_initial_coords) then
!       do not reuse the previously identified segments that contain
!       the LE and TE points
        slice(section)%le_seg    = -1
        slice(section)%te_seg(:) = -1
      end if

      call get_le_te(global_slice_data(section), slice(section)%le_seg,        &
                     slice(section)%te_seg)

!     determine the moment center (quarter chord) of the section

      call get_c4(global_slice_data(section))

!     store sliced x/y/z data in original orientation (or not! - leave it
!     in the slicing orientation, and just undo the artifice of the "box"
!     transform)

      if (output_in_slice_coords(section)) then
        transform(1:4,1:4) = inv_box_transform(1:4,1:4,section)
      else
        transform(1:4,1:4) = matmul(                                           &
                               inv_custom_transform(section,1:4,1:4),          &
                               inv_box_transform(1:4,1:4,section))
      end if

      if (slice_xmc(section) /= huge(1.0_dp)  .and.                            &
          slice_ymc(section) /= huge(1.0_dp)  .and.                            &
          slice_zmc(section) /= huge(1.0_dp)) then

        call transform_coord(slice_xmc(section),                               &
                             slice_ymc(section),                               &
                             slice_zmc(section),                               &
                             transform)

      end if

      call transform_coord(global_slice_data(section)%xmc,                     &
                           global_slice_data(section)%ymc,                     &
                           global_slice_data(section)%zmc,                     &
                           transform)

      call transform_coord(global_slice_data(section)%xle,                     &
                           global_slice_data(section)%yle,                     &
                           global_slice_data(section)%zle,                     &
                           transform)

      call transform_coord(global_slice_data(section)%xte,                     &
                           global_slice_data(section)%yte,                     &
                           global_slice_data(section)%zte,                     &
                           transform)

      call transform_coord(global_slice_data(section)%xs1,                     &
                           global_slice_data(section)%ys1,                     &
                           global_slice_data(section)%zs1,                     &
                           transform)

      call transform_coord(global_slice_data(section)%xs2,                     &
                           global_slice_data(section)%ys2,                     &
                           global_slice_data(section)%zs2,                     &
                           transform)

      call transform_vector(global_slice_data(section)%cfxs1,                  &
                            global_slice_data(section)%cfys1,                  &
                            global_slice_data(section)%cfzs1,                  &
                            transform)

      call transform_vector(global_slice_data(section)%cfxs2,                  &
                            global_slice_data(section)%cfys2,                  &
                            global_slice_data(section)%cfzs2,                  &
                            transform)

      call transform_normal(global_slice_data(section)%xnorm,                  &
                            global_slice_data(section)%ynorm,                  &
                            global_slice_data(section)%znorm,                  &
                            transform)

!     output some data for each slice the first time through

      info_1 : if (lmpi_master .and. write_info) then

        write(iu,*)
        if (slice_x(section)) then
          write(iu,'(a,i0,a,e10.3)') '  Info for slice ',section,' at x = ',   &
                                    slice_loc
        else if (slice_y(section)) then
          write(iu,'(a,i0,a,e10.3)') '  Info for slice ',section,' at y = ',   &
                                    slice_loc
        else if (slice_z(section)) then
          write(iu,'(a,i0,a,e10.3)') '  Info for slice ',section,' at z = ',   &
                                    slice_loc
        end if
        write(iu,'(a,i0)')'    Number of sections/loops ', slice(section)%nloops
        do i = 1, slice(section)%nloops
          write(iu,'(2(a,i0))')'    Number of points, loop ',i,': ',           &
                               slice(section)%npts(i)
        end do

      end if info_1

!     set some auxilary data that we may want to use when outputting the
!     sliced data (e.g. rotation angle)

      slice_frame_id = global_slice_data(section)%ref_frame

      global_slice_data(section)%aux_data(:) = 0.0_dp

      select case(slice_frame_id)
        case(-1)
          global_slice_data(section)%aux_data(1) =                             &
                                         observer%rotation_vector%theta*conv
          global_slice_data(section)%aux_data(2) =                             &
                                         observer%translation_vector%ds
        case(1:)
          global_slice_data(section)%aux_data(1) =                             &
                         moving_body(slice_frame_id)%rotation_vector%theta*conv
          global_slice_data(section)%aux_data(2) =                             &
                         moving_body(slice_frame_id)%translation_vector%ds
        case default
          global_slice_data(section)%aux_data(1) = 0.0_dp
          global_slice_data(section)%aux_data(2) = 0.0_dp
      end select

      if (overset_rotor) then
        call get_rotor_id_from_ref_frame(global_slice_data(section)%ref_frame, &
                                         rotor_id, blade_id)
        global_slice_data(section)%aux_data(1) = rotor(rotor_id)%psi1*conv
      end if

!     undo the transform used to convert soln%global_bndry_data
!     (potentially) from the noninertial frame to the t=0 frame and
!     simultaneously to the box5/6 orientation (where slicing is always
!     done on z=constant planes)
!     ===> this negates transform 2 <===
      call transform_body_to_inertial(inv_box_transform(1:4,1:4,section),      &
                                      inv_custom_transform(section,1:4,1:4),   &
                                      global_slice_data(section)%ref_frame,    &
                                      n_bndrys_to_slice, bndrys_to_slice,      &
                                      slice_initial_coords, soln, section)

    end do section_loop

!   we may or may not be able to skip computation of interpolation coefficients
!   on subsequent entries to the routine

    if (trim(grid%grid_motion) /= 'static') then
      if (slice_initial_coords) then
        compute_interp_coeff = .false.
      end if
    else
      compute_interp_coeff = .false.
    end if

    if (lmpi_master) close(iu)

    write_info = .false.

!   compute section unit vectors in the chord, normal and span directions

    call get_unit_vectors()

!   compute and output sectional force/moment coefficients

    if (.not. skip_force_output) then
      call sectional_force(grid%project, output_sectional_forces, time_step)
    end if

!   make sure any transformed global_bndry_data is returned to its incoming
!   reference frame
!   ===> this negates transform 1 <===
    call gbl_bndry_from_inertial_frame(grid, soln, bndry_not_sliced,           &
                                       bndry_frame_id)

  end subroutine slice_bndry_data

!============================== SECTIONAL_FORCE ==============================80
!
! Computes sectional forces and moments acting on solid surfaces; requires
! sliced global boundary data; for sections with multiple loops (e.g. multi-
! element airfoils, forces are totaled over all the loops
!
! Unless otherwise set by the user, the sectional moment coefficient is
! taken about the sectional quarter chord
!
!=============================================================================80

  subroutine sectional_force( project, output_sectional_forces, time_step )

    use info_depr,         only : alpha, yaw, simulation_time
    use nml_global,        only : moving_grid
    use nml_rotor_data,    only : overset_rotor
    use refgeom,           only : cref
    use lmpi,              only : lmpi_master
    use rotors,            only : rotor, nrotor, get_rotor_id_from_ref_frame
    use io,                only : prior_iters
    use moving_body_types, only : moving_body
    use system_extensions, only : se_open
    use file_utils,        only : rm, available_unit
    use nml_slice_data,    only : nslices, use_local_chord

    integer,       intent(in) :: time_step
    character(80), intent(in) :: project
    logical,       intent(in) :: output_sectional_forces

    character(80) :: format0, format1, format2, format3
    character(80) :: format4, format5
    character(80) :: filename

    integer :: n, ibeg, iend, loop, section, iu, rn
    integer :: rotor_id, blade_id, slice_frame_id

    real(dp) :: x1, x2, y1, y2, z1, z2
    real(dp) :: seglen, xnorm, ynorm, znorm, area
    real(dp) :: cpavg, cfxavg, cfyavg, cfzavg
    real(dp) :: cl, cd, cx, cy, cz, cmx, cmy, cmz
    real(dp) :: clp, cdp, cxp, cyp, czp, cmxp, cmyp, cmzp
    real(dp) :: clv, cdv, cxv, cyv, czv, cmxv, cmyv, cmzv
    real(dp) :: pi, conv, csa, sna, csy, sny, dcx, dcy, dcz
    real(dp) :: xmc, ymc, zmc, xmid, ymid, zmid, xmc_if, ymc_if, zmc_if
    real(dp) :: xle, yle, zle, xte, yte, zte
    real(dp) :: local_chord, chord, chord_x, chord_y, chord_z, pitch
    real(dp) :: norm_x, norm_y, norm_z, span_x, span_y, span_z
    real(dp) :: ca, cn, cs, cma, cmn, cms

    real(dp), dimension(4,4) :: transform

    logical, save :: init = .true.

    character(1) :: title_star(67)

    data title_star/67*'*'/

  continue

!   angle of attack and yaw factors

    pi = acos(-1.0_dp)
    conv = 180_dp/pi

    csa=cos(alpha/conv)
    sna=sin(alpha/conv)
    csy=cos(yaw/conv)
    sny=sin(yaw/conv)

    master_open : if (lmpi_master .and. output_sectional_forces) then

      iu = available_unit()
      filename = trim(project) // '.sectional_forces'

      if (init) then
        if (prior_iters == 0) call rm(filename)
        init = .false.
      end if

      call se_open(iu,file=filename,position='append')

    end if master_open

    section_loop : do section = 1,nslices

      write_header : if (section == 1 .and. lmpi_master                        &
                                      .and. output_sectional_forces) then
        write(*,*)
        write(*,'(2a,i5)') " Writing sectional force/moment data for time",    &
                           " step", time_step
        write(iu,'(67a1)') title_star
        write(iu,'(23x,a)')      ' SECTIONAL FORCE DATA'
        write(iu,'(17x,a,i5)')   ' Time Step        ', time_step+prior_iters
        write(iu,'(17x,a,e14.7)')' Simulation Time  ', simulation_time
        if (overset_rotor) then
          do rn = 1,nrotor
            write(iu,'(17x,a,f7.1,a,i2,a)') ' Psi              ',              &
                  real(rotor(rn)%psi1*conv,dp), ' deg (rotor', rn, ')'
          end do
        end if
        write(iu,'(67a1)') title_star
        write(iu,*)
      end if write_header

!     initialize forces and moments

      cl  = 0._dp
      cd  = 0._dp
      cx  = 0._dp
      cy  = 0._dp
      cz  = 0._dp
      cmx = 0._dp
      cmy = 0._dp
      cmz = 0._dp

      clp  = 0._dp
      cdp  = 0._dp
      cxp  = 0._dp
      cyp  = 0._dp
      czp  = 0._dp
      cmxp = 0._dp
      cmyp = 0._dp
      cmzp = 0._dp

      clv  = 0._dp
      cdv  = 0._dp
      cxv  = 0._dp
      cyv  = 0._dp
      czv  = 0._dp
      cmxv = 0._dp
      cmyv = 0._dp
      cmzv = 0._dp

      ca  = 0._dp
      cn  = 0._dp
      cs  = 0._dp
      cma = 0._dp
      cmn = 0._dp
      cms = 0._dp

!     leading and trailing edges of the slice

      xle = global_slice_data(section)%xle
      yle = global_slice_data(section)%yle
      zle = global_slice_data(section)%zle
      xte = global_slice_data(section)%xte
      yte = global_slice_data(section)%yte
      zte = global_slice_data(section)%zte

!     local chord length

      local_chord = sqrt((xte-xle)**2 + (yte-yle)**2 + (zte-zle)**2)

!     moment center of the slice (in slice reference frame)

      xmc = global_slice_data(section)%xmc
      ymc = global_slice_data(section)%ymc
      zmc = global_slice_data(section)%zmc

!     transform moment center into inertial frame (if no-moving, transform
!     contains identity matrix)

      transform(:,:) = 0.0_dp
      transform(1,1) = 1.0_dp
      transform(2,2) = 1.0_dp
      transform(3,3) = 1.0_dp
      transform(4,4) = 1.0_dp

      if (moving_grid) then
        slice_frame_id = global_slice_data(section)%ref_frame
        if (slice_frame_id > 0) then
          transform(:,:) =  moving_body(slice_frame_id)%slice_transform(:,:)
        end if
      end if

      xmc_if = transform(1,1)*xmc + transform(1,2)*ymc + transform(1,3)*zmc    &
             + transform(1,4)
      ymc_if = transform(2,1)*xmc + transform(2,2)*ymc + transform(2,3)*zmc    &
             + transform(2,4)
      zmc_if = transform(3,1)*xmc + transform(3,2)*ymc + transform(3,3)*zmc    &
             + transform(3,4)

      global_slice_data(section)%xmc_if = xmc_if
      global_slice_data(section)%ymc_if = ymc_if
      global_slice_data(section)%zmc_if = zmc_if

!     section unit vectors in chord, span and normal directions

      chord_x = global_slice_data(section)%chord_vec(1)
      chord_y = global_slice_data(section)%chord_vec(2)
      chord_z = global_slice_data(section)%chord_vec(3)

      span_x = global_slice_data(section)%span_vec(1)
      span_y = global_slice_data(section)%span_vec(2)
      span_z = global_slice_data(section)%span_vec(3)

      norm_x = global_slice_data(section)%norm_vec(1)
      norm_y = global_slice_data(section)%norm_vec(2)
      norm_z = global_slice_data(section)%norm_vec(3)

!     geometric pitch

      select case(global_slice_data(section)%slice_dir)

        case(1)
          if (abs(chord_y) > epsilon(1.0_dp)) then
            pitch = atan(chord_z/chord_y)*conv
          else
            pitch = 90.0_dp
          end if

        case(2)
          if (abs(chord_x) > epsilon(1.0_dp)) then
            pitch = atan(-chord_z/chord_x)*conv
          else
            pitch = 90.0_dp
          end if

        case(3)
          if (abs(chord_x) > epsilon(1.0_dp)) then
            pitch = atan(-chord_y/chord_x)*conv
          else
            pitch = 90.0_dp
          end if

        case default

      end select

      global_slice_data(section)%pitch = pitch

      body_loop : do loop = 1,global_slice_data(section)%nloops

        ibeg = global_slice_data(section)%ibeg(loop)
        iend = global_slice_data(section)%iend(loop)

!       loop over the segments

        segment_loop : do n=ibeg,iend

          x1 = global_slice_data(section)%xs1(n)
          x2 = global_slice_data(section)%xs2(n)
          y1 = global_slice_data(section)%ys1(n)
          y2 = global_slice_data(section)%ys2(n)
          z1 = global_slice_data(section)%zs1(n)
          z2 = global_slice_data(section)%zs2(n)

!         segment length

          seglen = sqrt((x2-x1)**2 + (y2-y1)**2 + (z2-z1)**2)

!         normals from sliced faces

          xnorm = global_slice_data(section)%xnorm(n)
          ynorm = global_slice_data(section)%ynorm(n)
          znorm = global_slice_data(section)%znorm(n)

!         unit normals

          area  = sqrt (xnorm*xnorm + ynorm*ynorm + znorm*znorm)
          xnorm = xnorm/area
          ynorm = ynorm/area
          znorm = znorm/area

!         average values for the segment

          cpavg  = 0.5_dp*(global_slice_data(section)%cps1(n) +                &
                              global_slice_data(section)%cps2(n))
          cfxavg = 0.5_dp*(global_slice_data(section)%cfxs1(n) +               &
                              global_slice_data(section)%cfxs2(n))
          cfyavg = 0.5_dp*(global_slice_data(section)%cfys1(n) +               &
                              global_slice_data(section)%cfys2(n))
          cfzavg = 0.5_dp*(global_slice_data(section)%cfzs1(n) +               &
                              global_slice_data(section)%cfzs2(n))

          xmid   = 0.5_dp*(x1 + x2)
          ymid   = 0.5_dp*(y1 + y2)
          zmid   = 0.5_dp*(z1 + z2)

!         add to totals; note: multiplication by segment length instead
!         of area gives a per-unit-width (sectional) value

          dcx = cpavg*xnorm*seglen  ! pressure contributions
          dcy = cpavg*ynorm*seglen
          dcz = cpavg*znorm*seglen

          clp = clp - dcx*sna     + dcz*csa
          cdp = cdp + dcx*csa*csy - dcy*sny + dcz*sna*csy

          cxp = cxp + dcx
          cyp = cyp + dcy
          czp = czp + dcz

          cmxp = cmxp + dcz*(ymid-ymc) - dcy*(zmid-zmc)
          cmyp = cmyp - dcz*(xmid-xmc) + dcx*(zmid-zmc)
          cmzp = cmzp + dcy*(xmid-xmc) - dcx*(ymid-ymc)

          dcx = cfxavg*seglen       ! viscous contributions
          dcy = cfyavg*seglen
          dcz = cfzavg*seglen

          clv = clv - dcx*sna     + dcz*csa
          cdv = cdv + dcx*csa*csy - dcy*sny + dcz*sna*csy

          cxv = cxv + dcx
          cyv = cyv + dcy
          czv = czv + dcz

          cmxv = cmxv + dcz*(ymid-ymc) - dcy*(zmid-zmc)
          cmyv = cmyv - dcz*(xmid-xmc) + dcx*(zmid-zmc)
          cmzv = cmzv + dcy*(xmid-xmc) - dcx*(ymid-ymc)

          cl = clp + clv
          cd = cdp + cdv

          cx = cxp + cxv
          cy = cyp + cyv
          cz = czp + czv

          cmx = cmxp + cmxv
          cmy = cmyp + cmyv
          cmz = cmzp + cmzv

        end do segment_loop

      end do body_loop

!     get (total) force/moment components in airfoil coordinate system

      ca   =  cx*chord_x  + cy*chord_y  + cz*chord_z
      cn   =  cx*norm_x   + cy*norm_y   + cz*norm_z
      cs   =  cx*span_x   + cy*span_y   + cz*span_z
      cma  =  cmx*chord_x + cmy*chord_y + cmz*chord_z
      cmn  =  cmx*norm_x  + cmy*norm_y  + cmz*norm_z
      cms  =  cmx*span_x  + cmy*span_y  + cmz*span_z

!     non-dimensionalize everything

!     note: we use chord rather than sref for sectional nondimensionalization;
!     the default is to use the local chord but can use cref instead;
!     also, use the chord to nondimensionalize all moments

      if (use_local_chord) then
        chord = local_chord
      else
        chord = cref
      end if

      cl = cl / chord
      cd = cd / chord

      cmx = cmx / (chord*chord)
      cmy = cmy / (chord*chord)
      cmz = cmz / (chord*chord)

      cx = cx / chord
      cy = cy / chord
      cz = cz / chord

      clp = clp / chord
      cdp = cdp / chord
      clv = clv / chord
      cdv = cdv / chord

      cmxp = cmxp / (chord*chord)
      cmyp = cmyp / (chord*chord)
      cmzp = cmzp / (chord*chord)
      cmxv = cmxv / (chord*chord)
      cmyv = cmyv / (chord*chord)
      cmzv = cmzv / (chord*chord)

      cxp = cxp / chord
      cyp = cyp / chord
      czp = czp / chord
      cxv = cxv / chord
      cyv = cyv / chord
      czv = czv / chord

      ca = ca / chord
      cn = cn / chord
      cs = cs / chord

      cma = cma / (chord*chord)
      cms = cms / (chord*chord)
      cmn = cmn / (chord*chord)

!     store off sectional data in the derived type

      global_slice_data(section)%cls  = cl
      global_slice_data(section)%cds  = cd
      global_slice_data(section)%cxs  = cx
      global_slice_data(section)%cys  = cy
      global_slice_data(section)%czs  = cz
      global_slice_data(section)%cmxs = cmx
      global_slice_data(section)%cmys = cmy
      global_slice_data(section)%cmzs = cmz

      global_slice_data(section)%clps  = clp
      global_slice_data(section)%cdps  = cdp
      global_slice_data(section)%cxps  = cxp
      global_slice_data(section)%cyps  = cyp
      global_slice_data(section)%czps  = czp
      global_slice_data(section)%cmxps = cmxp
      global_slice_data(section)%cmyps = cmyp
      global_slice_data(section)%cmzps = cmzp

      global_slice_data(section)%clvs  = clv
      global_slice_data(section)%cdvs  = cdv
      global_slice_data(section)%cxvs  = cxv
      global_slice_data(section)%cyvs  = cyv
      global_slice_data(section)%czvs  = czv
      global_slice_data(section)%cmxvs = cmxv
      global_slice_data(section)%cmyvs = cmyv
      global_slice_data(section)%cmzvs = cmzv

      global_slice_data(section)%ca  = ca
      global_slice_data(section)%cn  = cn
      global_slice_data(section)%cs  = cs
      global_slice_data(section)%cma = cma
      global_slice_data(section)%cmn = cmn
      global_slice_data(section)%cms = cms

!     get blade and rotor id (must be called from all processors)

      if (overset_rotor) then
       call get_rotor_id_from_ref_frame(global_slice_data(section)%ref_frame,  &
                                        rotor_id, blade_id)
      end if

!     output force and moment data

      master_write : if (lmpi_master .and. output_sectional_forces) then

        format0 = "(1x,'Cl  = ',e14.7,3x,'Cd  = ',e14.7)"
        format1 = "(1x,'Cmx = ',e14.7,3x,'Cmy = ',e14.7,3x,'Cmz = ',e14.7)"
        format2 = "(1x,'Cx  = ',e14.7,3x,'Cy  = ',e14.7,3x,'Cz  = ',e14.7)"
        format3 = "(1x,'Section Moment Center (x,y,z): ',3(e10.4,2x))"
        format4 = "(1x,'Cma = ',e14.7,3x,'Cmn = ',e14.7,3x,'Cms = ',e14.7)"
        format5 = "(1x,'Ca  = ',e14.7,3x,'Cn  = ',e14.7,3x,'Cs  = ',e14.7)"

        if (global_slice_data(section)%slice_dir == 1) then
          write(iu,*)       'FORCE SUMMARY FOR SLICE ',section,' AT X = ',     &
                             real(global_slice_data(section)%slice_val,dp)
        else if (global_slice_data(section)%slice_dir == 2) then
          write(iu,*)       'FORCE SUMMARY FOR SLICE ',section,' AT Y = ',     &
                             real(global_slice_data(section)%slice_val,dp)
        else
          write(iu,*)       'FORCE SUMMARY FOR SLICE ',section,' AT Z = ',     &
                             real(global_slice_data(section)%slice_val,dp)
        end if

        write(iu,*)
        write(iu,format3) real(xmc,dp),real(ymc,dp),real(zmc,dp)

        write(iu,*)
        if (overset_rotor) then
          write(iu,'(1x,a,i2,a,i2,a)')                                         &
                             'Extracted Geometrical Parameters, Rotor ',       &
                              rotor_id, ' Blade ', blade_id, ':'
        else
          write(iu,'(1x,a)') 'Extracted Geometrical Parameters:'
        end if
        write(iu,'(3x,a,3f14.5,a,f14.5)') 'chord  unit vector:      ',         &
                 real(chord_x,dp), real(chord_y,dp), real(chord_z,dp),         &
                 '     chord length: ', real(chord,dp)
        write(iu,'(3x,a,3f14.5)') 'normal unit vector:      ',                 &
                 real(norm_x,dp), real(norm_y,dp), real(norm_z,dp)
        write(iu,'(3x,a,3f14.5)') 'span   unit vector:      ',                 &
                 real(span_x,dp), real(span_y,dp), real(span_z,dp)
        write(iu,'(3x,a,3f14.5)') 'xle, yle, zle:           ',                 &
                 real(xle,dp), real(yle,dp), real(zle,dp)
        write(iu,'(3x,a,3f14.5)') 'xte, yte, zte:           ',                 &
                 real(xte,dp), real(yte,dp), real(zte,dp)
        write(iu,'(3x,a,3f14.5)') 'xqc, yqc, zqc:           ',                 &
                 real(xmc,dp), real(ymc,dp), real(zmc,dp)
        write(iu,'(3x,a,3f14.5)') 'xqc, yqc, zqc (inertial):',                 &
                 real(xmc_if,dp), real(ymc_if,dp), real(zmc_if,dp)
        write(iu,'(3x,a,f14.5,a)')'geometric pitch (deg):   ',                 &
                 real(pitch,dp)
        write(iu,*)
        write(iu,*)       '---------------------------------------------------'
        write(iu,*)       'Pressure forces'
        write(iu,format0) real(global_slice_data(section)%clps,dp),            &
                          real(global_slice_data(section)%cdps,dp)
        write(iu,format1) real(global_slice_data(section)%cmxps,dp),           &
                          real(global_slice_data(section)%cmyps,dp),           &
                          real(global_slice_data(section)%cmzps,dp)
        write(iu,format2) real(global_slice_data(section)%cxps,dp),            &
                          real(global_slice_data(section)%cyps,dp),            &
                          real(global_slice_data(section)%czps,dp)
        write(iu,*)       'Viscous forces'
        write(iu,format0) real(global_slice_data(section)%clvs,dp),            &
                          real(global_slice_data(section)%cdvs,dp)
        write(iu,format1) real(global_slice_data(section)%cmxvs,dp),           &
                          real(global_slice_data(section)%cmyvs,dp),           &
                          real(global_slice_data(section)%cmzvs,dp)
        write(iu,format2) real(global_slice_data(section)%cxvs,dp),            &
                          real(global_slice_data(section)%cyvs,dp),            &
                          real(global_slice_data(section)%czvs,dp)
        write(iu,*)       'Total forces'
        write(iu,format0) real(global_slice_data(section)%cls,dp),             &
                          real(global_slice_data(section)%cds,dp)
        write(iu,format1) real(global_slice_data(section)%cmxs,dp),            &
                          real(global_slice_data(section)%cmys,dp),            &
                          real(global_slice_data(section)%cmzs,dp)
        write(iu,format2) real(global_slice_data(section)%cxs,dp),             &
                          real(global_slice_data(section)%cys,dp),             &
                          real(global_slice_data(section)%czs,dp)
        write(iu,*)       'Total forces, section-aligned coordinates: axial, ',&
                          'normal, span'
        write(iu,format4) real(global_slice_data(section)%cma,dp),             &
                          real(global_slice_data(section)%cmn,dp),             &
                          real(global_slice_data(section)%cms,dp)
        write(iu,format5) real(global_slice_data(section)%ca,dp),              &
                          real(global_slice_data(section)%cn,dp),              &
                          real(global_slice_data(section)%cs,dp)
        write(iu,*)
        write(iu,*)
        write(iu,*)

      end if master_write

    end do section_loop

    if (lmpi_master .and. output_sectional_forces) close(iu)

  end subroutine sectional_force

!=============================== GET_UNIT_VECTORS ============================80
!
! Determines section unit vectors chord, normal and span directions
!
!=============================================================================80

  subroutine get_unit_vectors()

    real(dp)    :: xle, yle, zle, xte, yte, zte
    real(dp)    :: xmc_p, ymc_p, zmc_p, xmc_m, ymc_m, zmc_m
    real(dp)    :: chord_x, chord_y, chord_z, chord
    real(dp)    :: span_x, span_y, span_z, span
    real(dp)    :: norm_x, norm_y, norm_z, norm

    integer     :: group, section

  continue

    section_group_loop : do group = 1,nslice_groups

      section_loop : do section = slice_group_beg(group),slice_group_end(group)

!       leading and trailing edges of the slice

        xle = global_slice_data(section)%xle
        yle = global_slice_data(section)%yle
        zle = global_slice_data(section)%zle
        xte = global_slice_data(section)%xte
        yte = global_slice_data(section)%yte
        zte = global_slice_data(section)%zte

!       unit vector in chord direction

        chord   = sqrt((xte-xle)**2 + (yte-yle)**2 + (zte-zle)**2)
        chord_x = (xte-xle)/chord
        chord_y = (yte-yle)/chord
        chord_z = (zte-zle)/chord

!       unit normal in span direction (along line of moment centers)

        if (slice_group_end(group)-slice_group_beg(group) > 1) then

          if (section == slice_group_beg(group)) then

            xmc_m = global_slice_data(section)%xmc
            ymc_m = global_slice_data(section)%ymc
            zmc_m = global_slice_data(section)%zmc

            xmc_p = global_slice_data(section+1)%xmc
            ymc_p = global_slice_data(section+1)%ymc
            zmc_p = global_slice_data(section+1)%zmc

          else if (section == slice_group_end(group)) then

            xmc_m = global_slice_data(section-1)%xmc
            ymc_m = global_slice_data(section-1)%ymc
            zmc_m = global_slice_data(section-1)%zmc

            xmc_p = global_slice_data(section)%xmc
            ymc_p = global_slice_data(section)%ymc
            zmc_p = global_slice_data(section)%zmc

          else

            xmc_m = global_slice_data(section-1)%xmc
            ymc_m = global_slice_data(section-1)%ymc
            zmc_m = global_slice_data(section-1)%zmc

            xmc_p = global_slice_data(section+1)%xmc
            ymc_p = global_slice_data(section+1)%ymc
            zmc_p = global_slice_data(section+1)%zmc

          end if

          span   = sqrt((xmc_p-xmc_m)**2 + (ymc_p-ymc_m)**2 + (zmc_p-zmc_m)**2)
          span_x = (xmc_p-xmc_m)/span
          span_y = (ymc_p-ymc_m)/span
          span_z = (zmc_p-zmc_m)/span

        else

!         all we can do with one slice station is to take the span direction
!         in a fixed coordinate direction depending on the slice orientation

          select case(global_slice_data(section)%slice_dir)

            case(1)              ! note: assumes section is such that yle > yte
              span_x = 1.0_dp
              span_y = 0.0_dp
              span_z = 0.0_dp

            case(2)              ! note: assumes section is such that xle < xte
              span_x = 0.0_dp
              span_y = 1.0_dp
              span_z = 0.0_dp

            case(3)              ! note: assumes section is such that xle < xte
              span_x = 0.0_dp
              span_y = 0.0_dp
              span_z = 1.0_dp

            case default
              span_x = 1.0_dp    ! just something to silence compiler warnings
              span_y = 0.0_dp
              span_z = 0.0_dp

          end select

        end if

!       normal direction is cross product of chord vector with span vector

        norm_x = chord_y*span_z - chord_z*span_y
        norm_y = chord_z*span_x - chord_x*span_z
        norm_z = chord_x*span_y - chord_y*span_x

!       unit vector in normal direction

        norm   = sqrt(norm_x*norm_x + norm_y*norm_y + norm_z*norm_z)
        norm_x = norm_x/norm
        norm_y = norm_y/norm
        norm_z = norm_z/norm

!       store the data in the derived type

        global_slice_data(section)%chord_vec(1) = chord_x
        global_slice_data(section)%chord_vec(2) = chord_y
        global_slice_data(section)%chord_vec(3) = chord_z

        global_slice_data(section)%span_vec(1) = span_x
        global_slice_data(section)%span_vec(2) = span_y
        global_slice_data(section)%span_vec(3) = span_z

        global_slice_data(section)%norm_vec(1) = norm_x
        global_slice_data(section)%norm_vec(2) = norm_y
        global_slice_data(section)%norm_vec(3) = norm_z

      end do section_loop

    end do section_group_loop

  end subroutine get_unit_vectors

!======================== COMPUTE_FACE_AVG_VELOCITY ==========================80
!
! Computes averages of off-surface velocity components, for computing
! streamlines near solid surfaces
!
!=============================================================================80

  subroutine compute_face_avg_velocity( n_tot, nnodes, nbfacet, nbfaceq,       &
                                        f2ntb, f2nqb, qnode, nelem, elem,      &
                                        uavgt, vavgt, wavgt,                   &
                                        uavgq, vavgq, wavgq )

    use element_types, only : elem_type

    integer,                            intent(in)  :: n_tot, nnodes
    integer,                            intent(in)  :: nbfacet, nbfaceq, nelem
    integer,  dimension(nbfacet,5),     intent(in)  :: f2ntb
    integer,  dimension(nbfaceq,6),     intent(in)  :: f2nqb
    real(dp), dimension(n_tot, nnodes), intent(in)  :: qnode
    type(elem_type), dimension(nelem),  intent(in)  :: elem
    real(dp), dimension(nbfacet),       intent(out) :: uavgt, vavgt, wavgt
    real(dp), dimension(nbfaceq),       intent(out) :: uavgq, vavgq, wavgq

    integer :: face, i, icell, ielem, node, nodepercell

    real(dp) :: fact

  continue

    tria_faces : do face = 1,nbfacet

      uavgt(face) = 0._dp
      vavgt(face) = 0._dp
      wavgt(face) = 0._dp

      icell = f2ntb(face,4)   ! cell number associated with iface
      ielem = f2ntb(face,5)   ! cell type associated with iface

      nodepercell = elem(ielem)%node_per_cell

      fact  = 1._dp / real(nodepercell,dp)

      do i = 1,nodepercell
        node = elem(ielem)%c2n(i,icell)
        uavgt(face) = uavgt(face) + qnode(2,node)
        vavgt(face) = vavgt(face) + qnode(3,node)
        wavgt(face) = wavgt(face) + qnode(4,node)
      end do

      uavgt(face) = uavgt(face) * fact
      vavgt(face) = vavgt(face) * fact
      wavgt(face) = wavgt(face) * fact

    end do tria_faces

    quad_faces : do face = 1,nbfaceq

      uavgq(face) = 0._dp
      vavgq(face) = 0._dp
      wavgq(face) = 0._dp

      icell = f2nqb(face,5)   ! cell number associated with iface
      ielem = f2nqb(face,6)   ! cell type associated with iface

      nodepercell = elem(ielem)%node_per_cell

      fact  = 1._dp / real(nodepercell,dp)

      do i = 1,nodepercell
        node = elem(ielem)%c2n(i,icell)
        uavgq(face) = uavgq(face) + qnode(2,node)
        vavgq(face) = vavgq(face) + qnode(3,node)
        wavgq(face) = wavgq(face) + qnode(4,node)
      end do

      uavgq(face) = uavgq(face) * fact
      vavgq(face) = vavgq(face) * fact
      wavgq(face) = wavgq(face) * fact

    end do quad_faces

  end subroutine compute_face_avg_velocity

!======================== COMPUTE_OFFBODY_AMUT ===============================80
!
! Computes averages of off-surface amut, for computing
! turbulence index
!
!=============================================================================80

  subroutine compute_offbody_amut( nnodes, nbfacet, nbfaceq, f2ntb,            &
                                   f2nqb, amut, nelem, elem, amutoffbodyt,     &
                                   amutoffbodyq, ibnode, nbnode, solid_wall )

    use element_types, only : elem_type

    integer,                           intent(in)  :: nnodes, nbnode
    integer,                           intent(in)  :: nbfacet, nbfaceq, nelem
    integer,  dimension(nbfacet,5),    intent(in)  :: f2ntb
    integer,  dimension(nbfaceq,6),    intent(in)  :: f2nqb
    integer, dimension(nbnode),        intent(in)  :: ibnode
    real(dp), dimension(nnodes),       intent(in)  :: amut
    type(elem_type), dimension(nelem), intent(in)  :: elem
    logical,                           intent(in)  :: solid_wall
    real(dp), dimension(nbfacet),      intent(out) :: amutoffbodyt
    real(dp), dimension(nbfaceq),      intent(out) :: amutoffbodyq

    integer :: face, icell, ielem, node, nodepercell
    integer :: icellnode, ifacenode, nodeperface, icount

    integer, dimension(8) :: node_flag

    real(dp) :: fact

  continue

    if (solid_wall) then

      nodeperface = 3
      tria_faces : do face = 1,nbfacet

        amutoffbodyt(face) = 0._dp

        icell = f2ntb(face,4)   ! cell number associated with face
        ielem = f2ntb(face,5)   ! cell type associated with face

        nodepercell = elem(ielem)%node_per_cell

!       set up node_flag array for icell to distinguish off-surface nodes
!       from on-surface nodes

        node_flag(:) = 1

        do icellnode = 1, nodepercell

          do ifacenode = 1, nodeperface

            node = ibnode(f2ntb(face,ifacenode))  ! global node number

!           if a cell node lies on the surface, set its flag to zero

            if (elem(ielem)%c2n(icellnode,icell) == node) then
               node_flag(icellnode) = 0
            end if

          end do

        end do

        icount = 0
        do icellnode = 1,nodepercell
          if ( node_flag(icellnode) == 1) then
            node = elem(ielem)%c2n(icellnode,icell)
            amutoffbodyt(face) = amutoffbodyt(face) + amut(node)
            icount = icount + 1
          end if
        end do

        fact  = 1._dp / real(icount,dp)
        amutoffbodyt(face) = amutoffbodyt(face) * fact

      end do tria_faces

      nodeperface = 4
      quad_faces : do face = 1,nbfaceq

        amutoffbodyq(face) = 0._dp

        icell = f2nqb(face,5)   ! cell number associated with face
        ielem = f2nqb(face,6)   ! cell type associated with face

        nodepercell = elem(ielem)%node_per_cell

!       set up node_flag array for icell to distinguish off-surface nodes
!       from on-surface nodes

        node_flag(:) = 1

        do icellnode = 1, nodepercell

          do ifacenode = 1, nodeperface

            node = ibnode(f2nqb(face,ifacenode))  ! global node number

!           if a cell node lies on the surface, set its flag to zero

            if (elem(ielem)%c2n(icellnode,icell) == node) then
               node_flag(icellnode) = 0
            end if

          end do

        end do

        icount = 0
        do icellnode = 1,nodepercell
          if ( node_flag(icellnode) == 1) then
            node = elem(ielem)%c2n(icellnode,icell)
            amutoffbodyq(face) = amutoffbodyq(face) + amut(node)
            icount = icount + 1
          end if
        end do

        fact  = 1._dp / real(icount,dp)
        amutoffbodyq(face) = amutoffbodyq(face) * fact

      end do quad_faces

    else

!     set to a large negative value to serve as an indicator

      amutoffbodyt(:) = -100.0_dp
      amutoffbodyq(:) = -100.0_dp

    end if

  end subroutine compute_offbody_amut

!====================== GATHER_GLOBAL_BNDRY_DATA_REAL1 =======================80
!
! Gathers real rank 1 array of boundary data from across all processors
! NOTE: result is gathered onto the master node only
!
!=============================================================================80

  subroutine gather_global_bndry_data_real1( nnodes, nfacenodes, nface,        &
                                             nfacelocal, data_global,          &
                                             data_local, bndryl2g, localf2n,   &
                                             f2n, nfaceproc, temp_bndry,       &
                                             global_bndry )

    use lmpi, only : lmpi_nproc, lmpi_gatherv, lmpi_id

    integer,                           intent(in)  :: nnodes, nfacenodes
    integer,                           intent(in)  :: nface, nfacelocal
    integer,  dimension(nfacenodes),   intent(in)  :: bndryl2g
    integer,  dimension(4,nfacelocal), intent(in)  :: localf2n
    integer,  dimension(4,nface),      intent(in)  :: f2n
    integer,  dimension(lmpi_nproc),   intent(in)  :: nfaceproc
    real(dp), dimension(nnodes),       intent(in)  :: data_local
    real(dp), dimension(nfacenodes),   intent(out) :: data_global
    real(dp), dimension(4,nfacelocal), intent(out) :: temp_bndry
    real(dp), dimension(4,nface),      intent(out) :: global_bndry

    integer :: node, face

  continue

    temp_bndry   = 0._dp
    global_bndry = 0._dp

    if (lmpi_nproc == 1) then

      do node = 1, nfacenodes
        data_global(node) = data_local(bndryl2g(node))
      end do

    else

      do face = 1, nfacelocal
        do node = 1, 4
          if (localf2n(node,face) > 0)                                         &
            temp_bndry(node,face) = data_local(localf2n(node,face))
        end do
      end do

      call lmpi_gatherv(temp_bndry, nfaceproc(lmpi_id+1), &
        global_bndry, nfaceproc)

      do face = 1, nface
        do node = 1, 4
          if (f2n(node,face) > 0) then
             data_global(f2n(node,face)) = global_bndry(node,face)
          end if
        end do
      end do

    end if

  end subroutine gather_global_bndry_data_real1

!====================== GATHER_GLOBAL_BNDRY_DATA_REAL2 =======================80
!
! Gathers real rank 2 array of boundary data from across all processors
! NOTE: result is gathered onto the master node only
!
!=============================================================================80

  subroutine gather_global_bndry_data_real2( nnodes, nfacenodes, nface,        &
                                             nfacelocal, data_global,          &
                                             data_local, bndryl2g, localf2n,   &
                                             f2n, nfaceproc, nvar, var_beg,    &
                                             var_end )

    use lmpi, only : lmpi_nproc, lmpi_gatherv

    integer,                              intent(in)  :: nnodes, nfacenodes
    integer,                              intent(in)  :: nface, nfacelocal
    integer,                              intent(in)  :: nvar
    integer,  dimension(nfacenodes),      intent(in)  :: bndryl2g
    integer,  dimension(4,nfacelocal),    intent(in)  :: localf2n
    integer,  dimension(4,nface),         intent(in)  :: f2n
    integer,  dimension(lmpi_nproc),      intent(in)  :: nfaceproc
    real(dp), dimension(nvar,nnodes),     intent(in)  :: data_local
    real(dp), dimension(nvar,nfacenodes), intent(out) :: data_global
    integer,                    optional, intent(in)  :: var_beg, var_end

    integer :: node, face, var, beg_var, end_var

    real(dp), dimension(:,:,:), allocatable :: tbndry, gbndry

  continue

    beg_var = 1
    end_var = nvar

    if (present(var_beg)) beg_var = var_beg
    if (present(var_end)) end_var = var_end

    if (lmpi_nproc == 1) then


      do node = 1, nfacenodes

        do var = beg_var,end_var
          data_global(var,node) = data_local(var,bndryl2g(node))
        end do

      end do

    else

      allocate(gbndry(beg_var:end_var,4,nface))
      allocate(tbndry(beg_var:end_var,4,max(nfacelocal,1)))

      do face = 1, nfacelocal
        do node = 1, 4
         if (localf2n(node,face) > 0) then
          do var = beg_var,end_var
            tbndry(var,node,face) = data_local(var,localf2n(node,face))
          end do
         endif
        end do
      end do

      call lmpi_gatherv(tbndry, nfaceproc, gbndry)

      do face = 1, nface
        do node = 1, 4
         if (f2n(node,face) > 0) then
          do var = beg_var,end_var
             data_global(var,f2n(node,face)) = gbndry(var,node,face)
          end do
         end if
        end do
      end do

      deallocate(gbndry,tbndry)

    end if

  end subroutine gather_global_bndry_data_real2

!======================= GATHER_GLOBAL_BNDRY_DATA_INT1 =======================80
!
! Gathers integer rank 1 array of boundary data from across all processors
! NOTE: result is gathered onto the master node only
! NOTE: the gathered result is placed in a real array
!
!=============================================================================80

  subroutine gather_global_bndry_data_int1( nnodes, nfacenodes, nface,         &
                                            nfacelocal, data_global,           &
                                            data_local, bndryl2g, localf2n,    &
                                            f2n, nfaceproc, temp_bndry,        &
                                            global_bndry )

    use lmpi, only : lmpi_nproc, lmpi_gatherv, lmpi_id

    integer,                           intent(in)  :: nnodes, nfacenodes
    integer,                           intent(in)  :: nface, nfacelocal
    integer,  dimension(nfacenodes),   intent(in)  :: bndryl2g
    integer,  dimension(4,nfacelocal), intent(in)  :: localf2n
    integer,  dimension(4,nface),      intent(in)  :: f2n
    integer,  dimension(lmpi_nproc),   intent(in)  :: nfaceproc
    integer,  dimension(nnodes),       intent(in)  :: data_local
    real(dp), dimension(nfacenodes),   intent(out) :: data_global
    real(dp), dimension(4,nfacelocal), intent(out) :: temp_bndry
    real(dp), dimension(4,nface),      intent(out) :: global_bndry

    integer :: node, face

  continue

    temp_bndry   = 0._dp
    global_bndry = 0._dp

    if (lmpi_nproc == 1) then

      do node = 1, nfacenodes
        data_global(node) = data_local(bndryl2g(node))
      end do

    else

      do face = 1, nfacelocal
        do node = 1, 4
          if (localf2n(node,face) > 0)                                         &
            temp_bndry(node,face) = real(data_local(localf2n(node,face)), dp)
        end do
      end do

      call lmpi_gatherv(temp_bndry, nfaceproc(lmpi_id+1), &
        global_bndry, nfaceproc)

      do face = 1, nface
        do node = 1, 4
          if (f2n(node,face) > 0) then
             data_global(f2n(node,face)) = global_bndry(node,face)
          end if
        end do
      end do

    end if

  end subroutine gather_global_bndry_data_int1

!========================== GATHER_GLOBAL_FACE_DATA ==========================80
!
! Gathers real rank 1 array of boundary face data from across all processors
! NOTE: result is gathered onto the master node only
!
!=============================================================================80

  subroutine gather_global_face_data( ib, nface, nfacelocal, nbfacet, nbfaceq, &
                                      face_bit, face_bitq,                     &
                                      data_local_t, data_local_q, nfaceproc,   &
                                      temp_face_data, global_face_data )

    use lmpi, only : lmpi_nproc, lmpi_gatherv, lmpi_id

    integer,                         intent(in)  :: ib, nface, nfacelocal
    integer,                         intent(in)  :: nbfacet, nbfaceq
    integer,  dimension(nbfacet),    intent(in)  :: face_bit
    integer,  dimension(nbfaceq),    intent(in)  :: face_bitq
    integer,  dimension(lmpi_nproc), intent(in)  :: nfaceproc
    real(dp), dimension(nbfacet),    intent(in)  :: data_local_t
    real(dp), dimension(nbfaceq),    intent(in)  :: data_local_q
    real(dp), dimension(nfacelocal), intent(out) :: temp_face_data
    real(dp), dimension(nface),      intent(out) :: global_face_data

    integer :: totalfaces, face

  continue

    temp_face_data   = 0._dp
    global_face_data = 0._dp

    if (lmpi_nproc == 1) then

       totalfaces = 0

       tri_faces_1 : do face = 1, nbfacet
         if (face_bit(face) == 1) then
           totalfaces = totalfaces + 1
           global_face_data(totalfaces)  = data_local_t(face)
         end if
       end do tri_faces_1

       quad_faces_1 : do face = 1, nbfaceq
         if (face_bitq(face) == 1) then
           totalfaces = totalfaces + 1
           global_face_data(totalfaces)  = data_local_q(face)
         end if
       end do quad_faces_1

       if (totalfaces /= nface)                                                &
         write(*,*) "gather_global_face_data, face miscount, boundary ", ib

    else

       totalfaces = 0

       tri_faces : do face = 1, nbfacet
         if (face_bit(face) == 1) then
           totalfaces = totalfaces + 1
           temp_face_data(totalfaces)  = data_local_t(face)
         end if
       end do tri_faces

       quad_faces : do face = 1, nbfaceq
         if (face_bitq(face) == 1) then
           totalfaces = totalfaces + 1
           temp_face_data(totalfaces)  = data_local_q(face)
         end if
       end do quad_faces

       if (totalfaces /= nfacelocal)                                           &
         write(*,*) "gather_global_face_data, face miscount, boundary ", ib

       call lmpi_gatherv(temp_face_data, nfaceproc(lmpi_id+1), &
                       global_face_data, nfaceproc)

    end if

  end subroutine gather_global_face_data

!========================= AVERAGE_FACE_BASED_DATA ===========================80
!
! Averages face-based data in global arrays to nodes
!
!=============================================================================80

  subroutine average_face_based_data( nface, nfacenodes, f2n, xglobal_bndry,   &
                                      yglobal_bndry, zglobal_bndry,            &
                                      cpglobal_bndry, cqglobal_bndry,          &
                                      cfxglobal_bndry, cfyglobal_bndry,        &
                                      cfzglobal_bndry, uavgglobal_bndry,       &
                                      vavgglobal_bndry, wavgglobal_bndry,      &
                                      slenavgglobal_bndry,                     &
                                      amutoffbodyglobal_bndry,                 &
                                      cp_face, cq_face,                        &
                                      cfx_face, cfy_face, cfz_face,            &
                                      uavg_face, vavg_face, wavg_face,         &
                                      amutoffbody_face,                        &
                                      slen_face, need_avg_vel, need_avg_slen,  &
                                      need_amutoffbody )

    use grid_metrics,                    only : dual_area_quad, dual_area_tria
    use nml_noninertial_reference_frame, only : noninertial

    integer,                         intent(in)    :: nface, nfacenodes
    integer,  dimension(4,nface),    intent(in)    :: f2n
    real(dp), dimension(nfacenodes), intent(in)    :: xglobal_bndry
    real(dp), dimension(nfacenodes), intent(in)    :: yglobal_bndry
    real(dp), dimension(nfacenodes), intent(in)    :: zglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: cpglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: cqglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: cfxglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: cfyglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: cfzglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: uavgglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: vavgglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: wavgglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: amutoffbodyglobal_bndry
    real(dp), dimension(nfacenodes), intent(inout) :: slenavgglobal_bndry
    real(dp), dimension(nface),      intent(in)    :: cp_face
    real(dp), dimension(nface),      intent(in)    :: cq_face
    real(dp), dimension(nface),      intent(in)    :: cfx_face
    real(dp), dimension(nface),      intent(in)    :: cfy_face
    real(dp), dimension(nface),      intent(in)    :: cfz_face
    real(dp), dimension(nface),      intent(in)    :: uavg_face
    real(dp), dimension(nface),      intent(in)    :: vavg_face
    real(dp), dimension(nface),      intent(in)    :: wavg_face
    real(dp), dimension(nface),      intent(in)    :: amutoffbody_face
    real(dp), dimension(nface),      intent(in)    :: slen_face
    logical,                         intent(in)    :: need_avg_vel
    logical,                         intent(in)    :: need_avg_slen
    logical,                         intent(in)    :: need_amutoffbody

    integer :: face, f2n_node, node, node1, node2, node3, node4

    real(dp)                            :: area, xnorm, ynorm, znorm
    real(dp), dimension(4)              :: xnorm_q, ynorm_q, znorm_q
    real(dp), dimension(3)              :: xnorm_t, ynorm_t, znorm_t
    real(dp), dimension(:), allocatable :: areasum

  continue

    allocate(areasum(nfacenodes))
    areasum(:) = 0._dp

    do face = 1,nface

      node1 = f2n(1,face)
      node2 = f2n(2,face)
      node3 = f2n(3,face)
      node4 = f2n(4,face)

!     contributions to dual face areas

      if (node4 == 0) then
        call dual_area_tria(nfacenodes, xglobal_bndry, yglobal_bndry,          &
                            zglobal_bndry, node1, node2, node3, noninertial,   &
                            xnorm_t, ynorm_t, znorm_t)
      else
        call dual_area_quad(nfacenodes, xglobal_bndry, yglobal_bndry,          &
                            zglobal_bndry, node1, node2, node3, node4,         &
                            noninertial, xnorm_q, ynorm_q, znorm_q)
      end if

      do node = 1,4

        f2n_node = f2n(node,face)

        if (f2n_node > 0) then

          if (node4 == 0) then
            xnorm = xnorm_t(node)
            ynorm = ynorm_t(node)
            znorm = znorm_t(node)
          else
            xnorm = xnorm_q(node)
            ynorm = ynorm_q(node)
            znorm = znorm_q(node)
          end if

          area = sqrt(xnorm**2+ynorm**2+znorm**2)

          cpglobal_bndry(f2n_node)    = cpglobal_bndry(f2n_node)               &
                                      + cp_face(face)*area
          cqglobal_bndry(f2n_node)    = cqglobal_bndry(f2n_node)               &
                                      + cq_face(face)*area
          cfxglobal_bndry(f2n_node)   = cfxglobal_bndry(f2n_node)              &
                                      + cfx_face(face)*area
          cfyglobal_bndry(f2n_node)   = cfyglobal_bndry(f2n_node)              &
                                      + cfy_face(face)*area
          cfzglobal_bndry(f2n_node)   = cfzglobal_bndry(f2n_node)              &
                                      + cfz_face(face)*area

          areasum(f2n_node) = areasum(f2n_node) + area

        end if

      end do

    end do

    cpglobal_bndry(:)  = cpglobal_bndry(:)/areasum(:)
    cqglobal_bndry(:)  = cqglobal_bndry(:)/areasum(:)
    cfxglobal_bndry(:) = cfxglobal_bndry(:)/areasum(:)
    cfyglobal_bndry(:) = cfyglobal_bndry(:)/areasum(:)
    cfzglobal_bndry(:) = cfzglobal_bndry(:)/areasum(:)

!   do the same for average off-surface velocities, if needed

    compute_avg_vel : if (need_avg_vel) then

      areasum(:) = 0._dp

      do face = 1,nface

        node1 = f2n(1,face)
        node2 = f2n(2,face)
        node3 = f2n(3,face)
        node4 = f2n(4,face)

!       contributions to dual face areas

        if (node4 == 0) then
          call dual_area_tria(nfacenodes, xglobal_bndry, yglobal_bndry,        &
                              zglobal_bndry, node1, node2, node3, noninertial, &
                              xnorm_t, ynorm_t, znorm_t)
        else
          call dual_area_quad(nfacenodes, xglobal_bndry, yglobal_bndry,        &
                              zglobal_bndry, node1, node2, node3, node4,       &
                              noninertial, xnorm_q, ynorm_q, znorm_q)
        end if

        do node = 1,4

          f2n_node = f2n(node,face)

          if (f2n_node > 0) then

            if (node4 == 0) then
              xnorm = xnorm_t(node)
              ynorm = ynorm_t(node)
              znorm = znorm_t(node)
            else
              xnorm = xnorm_q(node)
              ynorm = ynorm_q(node)
              znorm = znorm_q(node)
            end if

            area = sqrt(xnorm**2+ynorm**2+znorm**2)

            uavgglobal_bndry(f2n_node) = uavgglobal_bndry(f2n_node)            &
                                       + uavg_face(face)*area
            vavgglobal_bndry(f2n_node) = vavgglobal_bndry(f2n_node)            &
                                       + vavg_face(face)*area
            wavgglobal_bndry(f2n_node) = wavgglobal_bndry(f2n_node)            &
                                       + wavg_face(face)*area

            areasum(f2n_node) = areasum(f2n_node) + area

          end if

        end do

      end do

      uavgglobal_bndry(:) = uavgglobal_bndry(:)/areasum(:)
      vavgglobal_bndry(:) = vavgglobal_bndry(:)/areasum(:)
      wavgglobal_bndry(:) = wavgglobal_bndry(:)/areasum(:)

    end if compute_avg_vel

!   do the same for off-body amut, if needed

    compute_amutoffbody : if (need_amutoffbody) then

      areasum(:) = 0._dp

      do face = 1,nface

        node1 = f2n(1,face)
        node2 = f2n(2,face)
        node3 = f2n(3,face)
        node4 = f2n(4,face)

!       contributions to dual face areas

        if (node4 == 0) then
          call dual_area_tria(nfacenodes, xglobal_bndry, yglobal_bndry,        &
                              zglobal_bndry, node1, node2, node3, noninertial, &
                              xnorm_t, ynorm_t, znorm_t)
        else
          call dual_area_quad(nfacenodes, xglobal_bndry, yglobal_bndry,        &
                              zglobal_bndry, node1, node2, node3, node4,       &
                              noninertial, xnorm_q, ynorm_q, znorm_q)
        end if

        do node = 1,4

          f2n_node = f2n(node,face)

          if (f2n_node > 0) then

            if (node4 == 0) then
              xnorm = xnorm_t(node)
              ynorm = ynorm_t(node)
              znorm = znorm_t(node)
            else
              xnorm = xnorm_q(node)
              ynorm = ynorm_q(node)
              znorm = znorm_q(node)
            end if

            area = sqrt(xnorm**2+ynorm**2+znorm**2)

            amutoffbodyglobal_bndry(f2n_node) =                                &
                                              amutoffbodyglobal_bndry(f2n_node)&
                                              + amutoffbody_face(face)*area

            areasum(f2n_node) = areasum(f2n_node) + area

          end if

        end do

      end do

      amutoffbodyglobal_bndry(:) = amutoffbodyglobal_bndry(:)/areasum(:)

    end if compute_amutoffbody

!   do the same for average slen distance, if needed

    compute_avg_slen : if (need_avg_slen) then

      areasum(:) = 0._dp

      do face = 1,nface

        node1 = f2n(1,face)
        node2 = f2n(2,face)
        node3 = f2n(3,face)
        node4 = f2n(4,face)

!       contributions to dual face areas

        if (node4 == 0) then
          call dual_area_tria(nfacenodes, xglobal_bndry, yglobal_bndry,        &
                              zglobal_bndry, node1, node2, node3, noninertial, &
                              xnorm_t, ynorm_t, znorm_t)
        else
          call dual_area_quad(nfacenodes, xglobal_bndry, yglobal_bndry,        &
                              zglobal_bndry, node1, node2, node3, node4,       &
                              noninertial, xnorm_q, ynorm_q, znorm_q)
        end if

        do node = 1,4

          f2n_node = f2n(node,face)

          if (f2n_node > 0) then

            if (node4 == 0) then
              xnorm = xnorm_t(node)
              ynorm = ynorm_t(node)
              znorm = znorm_t(node)
            else
              xnorm = xnorm_q(node)
              ynorm = ynorm_q(node)
              znorm = znorm_q(node)
            end if

            area = sqrt(xnorm**2+ynorm**2+znorm**2)

            slenavgglobal_bndry(f2n_node) = slenavgglobal_bndry(f2n_node)      &
                                           + slen_face(face)*area

            areasum(f2n_node) = areasum(f2n_node) + area

          end if

        end do

      end do

      slenavgglobal_bndry(:) = slenavgglobal_bndry(:)/areasum(:)

    end if compute_avg_slen

    deallocate(areasum)

  end subroutine average_face_based_data

!============================== AVAILABLE_MEMORY =============================80
!
! Fills slen in node-path face arrays from slen cell-path face array
!
!=============================================================================80

  subroutine fill_slen_face_arrays( grid, slenface_t, slenface_q, nbfacet,     &
                                    nbfaceq, ib_current )

    use grid_types, only : grid_type

    type(grid_type),              intent(in)  :: grid
    integer,                      intent(in)  :: nbfacet, nbfaceq, ib_current
    real(dp), dimension(nbfacet), intent(out) :: slenface_t
    real(dp), dimension(nbfaceq), intent(out) :: slenface_q

    integer :: ib, indx, aindx, n, cell1

  continue

    do n = 1, grid%bcc%n_faces0

      ib  = grid%bcc%ibf(n)

      if (ib /= ib_current) cycle  ! face is not on the boundary of interest

      indx = grid%bcc%f2n_index(n)

      if (indx == 0) cycle

      cell1  = grid%bcc%cell(n)
      aindx = abs(indx)

      if (indx > 0) then
        slenface_t(aindx) = grid%slen(cell1)
      else
        slenface_q(aindx) = grid%slen(cell1)
      end if

    end do

  end subroutine fill_slen_face_arrays

!============================== AVAILABLE_MEMORY =============================80
!
! Determines the amount of memory (in Mb) currently available for use,
! using the method of bisection; convergence tolerance is set by
! the parameter tol; normal routine completion results in return_status = 0,
! and except for the exceptions noted below, a non-zero return status
! indicates an error
!
! Notes:
! 1) an upper-bound estimate of avaialble memory set by the intrinsic "huge";
! typically this is about 2Gb, so for processors with more than 2Gb currently
! available the estimate of this routine will be low; if more than 2GB is
! currently available the return_status will be -1, and the value of
! available_memory should be interpreted as "at least"
! 2) similarly, if memory cleanup occurs while the bisection method is in
! progress, the previously unallocatable upper bound may become allocatable;
! rather than attempt to chase a moving target, the routine returns at that
! point with a return status of 4, and the value of available_memory should
! be interpreted as "at least"
!
!=============================================================================80

  integer function available_memory( return_status, verbose, iunit )

    use kinddefs, only : dp

    integer,           intent(out) :: return_status
    logical, optional, intent(in)  :: verbose
    integer, optional, intent(in)  :: iunit

    real(dp), dimension(:), allocatable :: x

    integer :: size1, size2, size_avg, status1, status2, status_avg
    integer :: i, iter, iu, temp, tol

    integer, parameter :: one_kb   = 1024
    integer, parameter :: one_mb   = 1048576
    integer, parameter :: iter_max = 150

    logical :: verbosity

  continue

    tol = one_mb   ! Mb tolerance

    verbosity = .false.
    if (present(verbose)) verbosity = verbose

    iu = 6
    if (present(iunit)) iu = iunit

!   trial sizes, in bytes

    size1 = one_kb
    size2 = (huge(size1) - 2*size1)

!   trial sizes, in dp words

    size1 = size1 / dp
    size2 = size2 / dp

    allocate(x(size1), stat=status1)
    if (allocated(x)) deallocate(x)
    allocate(x(size2), stat=status2)
    if (allocated(x)) deallocate(x)

    if (verbosity) then
      write(iu,'(a)') 'starting point allocations and allocate status'
      write(iu,'(a,i0,1x,i0)') 'allocate x: size1, status1 ', size1, status1
      write(iu,'(a,i0,1x,i0)') 'allocate x: size2, status2 ', size2, status2
      write(iu,'(a,i0,a)')     'convergence tolerance      ', tol/one_mb, ' Mb'
      write(iu,*)
    end if

!   need status1 = 0, but despite small size1, allocation failed; bail out

    if (status1 /= 0) then
      available_memory = 0
      return_status = -2
      return
    end if

!   need status2 =/ 0 to use bisection, but we were able to allocate the
!   initial starting size2 successfully

    if (status2 == 0) then
      available_memory = size2 / one_mb * dp
      return_status = -1
      return
    end if

    iter = 0

    do i = 1,iter_max

      if (dp*abs(size2 - size1) <= tol) exit

      allocate(x(size1), stat=status1)
      if (allocated(x)) deallocate(x)
      allocate(x(size2), stat=status2)
      if (allocated(x)) deallocate(x)

      if (verbosity) then
        write(iu,'(a,i0)') 'iteration ',iter
        write(iu,'(a,i0,1x,i0)') 'allocate x: size1, status1 ', size1, status1
        write(iu,'(a,i0,1x,i0)') 'allocate x: size2, status2 ', size2, status2
        write(iu,*)
      end if

      if (status1 == 0 .and. status2 /= 0) then

!       normal situation; required for bisection method

        size_avg = (size1 + size2) / 2

        allocate(x(size_avg), stat=status_avg)
        if (allocated(x)) deallocate(x)

        if (status_avg /= 0 .and. status2 /= 0) size2 = size_avg
        if (status_avg == 0 .and. status1 == 0) size1 = size_avg

        if (size2 < size1) then   ! always take the smallest as size1
          temp = size1
          size1 = size1
          size2 = temp
        end if

      else if (status1 == 0 .and. status2 == 0) then

!       memory trash cleanup must have occurred; moving target since
!       suddenly the larger memory request is met

        size1 = size2
        exit

      else if (status1 /= 0) then

        size1 = 0
        exit

      end if

      iter = iter + 1

    end do

    available_memory = size1 * dp / one_mb

    return_status = 0

!   check for some failure indicators

    if (available_memory < 0) return_status = 1
    if (iter >= iter_max)     return_status = 2
    if (iter == 0)            return_status = 3
    if (status1 == 0 .and.                                                     &
        status2 == 0)         return_status = 4
    if (status1 /= 0)         return_status = 5

    if (verbosity) then
      write(iu,'(a,i0,a)') 'available memory ', available_memory, ' Mb'
      if (return_status /= 0) then
        write(iu,'(a,i0)') 'return_status = ', return_status
      end if
      write(iu,*)
      write(iu,*)
    end if

  end function available_memory

end module solution_globals

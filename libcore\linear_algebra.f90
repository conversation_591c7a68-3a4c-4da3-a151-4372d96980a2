
! A grouping of functions for linear algebra

module linear_algebra

  use kinddefs, only : dp, r8, r4

  implicit none

  private

  public :: identity_matrix
  public :: eigenvalueandvect3sym

  public :: display3
  public :: tridiag, poseigtridiag
  public :: singleLU
  public :: singlePivotLU
  public :: backsolveLU
  public :: hh_inverse
  interface hh_inverse
    module procedure hh_inverse_full
    module procedure hh_inverse_half
  end interface
  public :: gaussian_elimination

contains

  pure function identity_matrix(n)

    integer,      intent(in) :: n
    real(dp), dimension(n,n) :: identity_matrix

    integer :: i

    continue

    identity_matrix = 0.0_dp
    do i = 1, n
      identity_matrix(i,i) = 1.0_dp
    end do

  end function identity_matrix

  subroutine display3(a,string)

    use kinddefs, only: dp

    real(dp), dimension(3,3),           intent(in) :: a
    character(*),             optional, intent(in) :: string

    character(*), parameter :: form1 = '(3f25.15)'
    character(*), parameter :: form2 = '(3f25.15," ",a)'

    continue

    if (present(string)) then
      write(*,form2)a(1,:),string
      write(*,form2)a(2,:),string
      write(*,form2)a(3,:),string
    else
      write(*,form1)a(1,:)
      write(*,form1)a(2,:)
      write(*,form1)a(3,:)
    end if
    write(*,*)

  end subroutine display3

  subroutine tridiag(a,d,e,q)

    use kinddefs, only: dp

    real(dp), dimension(3,3), intent(in)  :: a
    real(dp), dimension(3),   intent(out) :: d, e
    real(dp), dimension(3,3), intent(out) :: q

    real(dp) :: l, u, v, s

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

    continue

    d(1) = a(1,1)
    e(3) = my_0
    q = my_0
    q(1,1) = my_1
    if ( abs(a(1,3)) > tiny(0.0_dp) ) then
      l = sqrt( a(1,2)*a(1,2) + a(1,3)*a(1,3) )
      u = a(1,2) / l
      v = a(1,3) / l
      s = 2.0_dp * u * a(2,3) + v * ( a(3,3) - a(2,2) )
      d(2) = a(2,2) + v*s
      d(3) = a(3,3) - v*s
      e(1) = l
      e(2) = a(2,3) - u*s
      q(2,2) = u
      q(2,3) = v
      q(3,2) = v
      q(3,3) = -u
    else
      d(2) = a(2,2)
      d(3) = a(3,3)
      e(1) = a(1,2)
      e(2) = a(2,3)
      q(2,2) = my_1
      q(3,3) = my_1
    end if

  end subroutine tridiag

  subroutine eigenvalueandvect3sym(m,e1,e2,e3,ev1,ev2,ev3,ierr)

    use kinddefs, only: dp

    real(dp), dimension(3,3), intent(in)  :: m
    real(dp),                 intent(out) :: e1,e2,e3
    real(dp), dimension(3),   intent(out) :: ev1,ev2,ev3
    integer,                  intent(out) :: ierr

    real(dp), dimension(3)   :: d, e
    real(dp), dimension(3,3) :: z

    continue

    call tridiag(m,d,e,z)
    call poseigtridiag(d,e,z,ierr)
    e1=d(1)
    e2=d(2)
    e3=d(3)
    ev1(:)=z(:,1)
    ev2(:)=z(:,2)
    ev3(:)=z(:,3)

  end subroutine eigenvalueandvect3sym

!http://www.netlib.org/eispack/
! modifed from orig tql2
!
!     this subroutine is a translation of the algol procedure tql2,
!     num. math. 11, 293-306(1968) by bowdler, martin, reinsch, and
!     wilkinson.
!     handbook for auto. comp., vol.ii-linear algebra, 227-240(1971).
!
!     this subroutine finds the eigenvalues and eigenvectors
!     of a symmetric tridiagonal matrix by the ql method.
!     the eigenvectors of a full symmetric matrix can also
!     be found if  tred2  has been used to reduce this
!     full matrix to tridiagonal form.
!
!     on input
!
!        d contains the diagonal elements of the input matrix.
!
!        e contains the subdiagonal elements of the input matrix
!          in its first n-1 positions.  e(n) is arbitrary.
!
!        z contains the transformation matrix produced in the
!          reduction by  tred2, if performed.  if the eigenvectors
!          of the tridiagonal matrix are desired, z must contain
!          the identity matrix.
!
!      on output
!
!        d contains the eigenvalues in ascending order.  if an
!          error exit is made, the eigenvalues are correct but
!          unordered for indices 1,2,...,ierr-1.
!
!        e has been destroyed.
!
!        z contains orthonormal eigenvectors of the symmetric
!          tridiagonal (or full) matrix.  if an error exit is made,
!          z contains the eigenvectors associated with the stored
!          eigenvalues.
!
!        ierr is set to
!          zero       for normal return,
!          j          if the j-th eigenvalue has not been
!                     determined after 30 iterations.
!

!     questions and comments should be directed to burton s. garbow,
!     mathematics and computer science div, argonne national laboratory
!
!     this version dated august 1983.
!
!     ------------------------------------------------------------------
!
  subroutine poseigtridiag(d,e,z,ierr)

    use kinddefs, only: dp

    real(dp), dimension(3),   intent(inout) :: d, e
    real(dp), dimension(3,3), intent(inout) :: z
    integer,                  intent(out)   :: ierr

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp
    real(dp), parameter :: my_2 = 2.0_dp

    real(dp) :: c, c2, c3, dl1, el1, f, g, h, p, r, s, s2
    real(dp) :: zero_tolerance

    integer  :: i, j, k, l, m, ii, l1, l2, mml

    real(dp), dimension(3) :: d_backup, e_backup

    continue

    ierr = 0

    d_backup = d
    e_backup = e

    f = my_0
    zero_tolerance = my_0
    e(3) = my_0

    row_loop : do l = 1, 3
      j = 0
      h = abs(d(l)) + abs(e(l))
      if (zero_tolerance < h) zero_tolerance = h
! look for small sub-diagonal element
      test_for_zero_e : do m = l, 3
        if (abs(e(m)) <= spacing(real(zero_tolerance,dp))) exit test_for_zero_e
! e(3) is always zero, so there is no exit through the bottom of the loop
      end do test_for_zero_e
      l_not_equal_m : if (m /= l) then
        iterate : do
          j = j + 1
! set error -- no convergence to an eigenvalue after 30 iterations
          too_many_iterations : if (j > 30 ) then
            write(*,*) 'd', d
            write(*,*) 'e', e
            write(*,*) 'zero_tolerance', zero_tolerance
            write(*,*) 'spacing', spacing(real(zero_tolerance,dp))
            write(*,*) 'd_backup', d_backup
            write(*,*) 'e_backup', e_backup
            ierr = l
            write(*,*) "linear_algebra: poseigtridiag: too many iter, row ",ierr
            return
          end if too_many_iterations
! form shift
          l1 = l + 1
          l2 = l1 + 1
          g = d(l)
          p = (d(l1) - g) / (my_2 * e(l))
          r = sqrt(p*p+my_1)
          d(l) = e(l) / (p + sign(r,p))
          d(l1) = e(l) * (p + sign(r,p))
          dl1 = d(l1)
          h = g - d(l)
          if (l2 <= 3) then
            do i = l2, 3
              d(i) = d(i) - h
            end do
          end if
          f = f + h
! ql transformation
          p = d(m)
          c = my_1
          c2 = c
          el1 = e(l1)
          s = my_0
          mml = m - l
!      for i=m-1 step -1 until l do --
          c3 = c2
          s2 = s
          do ii = 1, mml
            c3 = c2
            c2 = c
            s2 = s
            i = m - ii
            g = c * e(i)
            h = c * p
            r = sqrt(p*p+e(i)*e(i))
            e(i+1) = s * r
            s = e(i) / r
            c = p / r
            p = c * d(i) - s * g
            d(i+1) = h + s * (c * g + s * d(i))
!      form vector
            do k = 1, 3
              h = z(k,i+1)
              z(k,i+1) = s * z(k,i) + c * h
              z(k,i) = c * z(k,i) - s * h
            end do
          end do
          p = -s * s2 * c3 * el1 * e(l) / dl1
          e(l) = s * p
          d(l) = c * p
          if (abs(e(l)) < spacing(real(zero_tolerance,dp))) exit iterate
        end do iterate
      end if l_not_equal_m
      d(l) = d(l) + f
    end do row_loop

    do i = 1, 3
      d(i) = abs(d(i))
    end do

    order_eigen_system : do ii = 2, 3
      i = ii - 1
      k = i
      p = d(i)
      find_largest_eigvalue : do j = ii, 3
        if (d(j) <= p) cycle
        k = j
        p = d(j)
      end do find_largest_eigvalue
      swap_eigs :if (k /= i) then
        d(k) = d(i)
        d(i) = p
        do j = 1, 3
          p = z(j,i)
          z(j,i) = z(j,k)
          z(j,k) = -p
        end do
      end if swap_eigs
    end do order_eigen_system

  end subroutine poseigtridiag

  subroutine singleLU(a)

    use kinddefs, only: dp

    real(dp), dimension(:,:), intent(inout) :: a

    integer :: nrow, ncol
    integer :: j, k, k1

    continue

    nrow = size(a,1)
    ncol = size(a,2)

    rectangular_matrix : if (nrow /= ncol) then
      write(*,*) "Error: singleLU: need square matrix"
      return
    end if rectangular_matrix

    do k = 2,ncol
      k1 = k-1
      do j = k,nrow
        a(j,k1    ) = a(j,k1    ) / a(k1,k1)
        a(j,k:ncol) = a(j,k:ncol) - a(j ,k1) * a(k1,k:ncol)
      end do
    end do

  end subroutine singleLU

  subroutine singlePivotLU(a,p)

    use kinddefs, only: dp

    real(dp), dimension(:,:), intent(inout) :: a
    real(dp), dimension(:,:), intent(out)   :: p

    integer :: a_rows, a_columns
    integer :: p_rows, p_columns
    integer :: n
    integer :: j, k, k1
    integer :: ibest, ilook

    real(dp) :: temp

    real(dp), parameter :: my_0 = 0.0_dp
    real(dp), parameter :: my_1 = 1.0_dp

    continue

    a_rows = size(a,1)
    a_columns = size(a,2)

    rectangular_matrix : if (a_rows /= a_columns) then
      write(*,*) "Error: singlePivotLU: need square matrix",a_rows,a_columns
      return
    end if rectangular_matrix

    p_rows = size(p,1)
    p_columns = size(p,2)

    matching_p_matrix : if ( (p_rows /= a_rows)      .or.                      &
                             (p_columns /= a_columns) ) then
      write(*,*) "Error: singlePivotLU: need p matrix matching a dimensions"
      return
    end if matching_p_matrix

    n = a_columns

    p = my_0
    do j = 1, n
      p(j,j) = my_1
    end do

    pivot_row : do k = 1, n-1
      ibest = k
      find_bigest_lower_entry : do ilook = k+1, n
        if ( abs(a(ilook,k)) > abs(a(ibest,k)) ) then
          ibest = ilook
        end if
      end do find_bigest_lower_entry
      interchange_rows : if ( ibest /= k ) then

        permutation_matrix : do j = 1, n
          temp = p(ibest,j)
          p(ibest,j) = p(k,j)
          p(k,j) = temp
        end do permutation_matrix

        a_matrix : do j = 1, n
          temp = a(ibest,j)
          a(ibest,j) = a(k,j)
          a(k,j) = temp
        end do a_matrix

      end if interchange_rows

      k1 = k+1
      finally_do_the_lu : do j = k1,n
        a(j,k) = a(j,k) / a(k,k)
        a(j,k1:n) = a(j,k1:n) - a(j,k) * a(k,k1:n)
      end do finally_do_the_lu
    end do pivot_row

  end subroutine singlePivotLU

  subroutine backsolveLU(lu,x,b)

    use kinddefs, only: dp

    real(dp), dimension(:,:), intent(in)  :: lu
    real(dp), dimension(:),   intent(out) :: x
    real(dp), dimension(:),   intent(in)  :: b

    integer :: nrow, ncol
    integer :: j, j1, k

    continue

    nrow = size(lu,1)
    ncol = size(lu,2)

    rectangular_matrix : if (nrow /= ncol) then
      write(*,*) "Error: backsolveLU: need sqaure matrix"
      return
    end if rectangular_matrix

    x = b

    do j = 2,nrow
      j1 = j-1
      x(j:nrow) = x(j:nrow) - lu(j:nrow,j1)*x(j1)
    end do

    do j = nrow,1,-1
      if (j<nrow) then
        j1 = j+1
        do k = j1,nrow
          x(j) = x(j) - lu(j,k)*x(k)
        end do
      end if
      x(j) = x(j) / lu(j,j)
    end do

  end subroutine backsolveLU

!=============================================================================80
!
! Householder routine for finding inverse of a matrix - full precision
!
!=============================================================================80
  subroutine hh_inverse_full(a,ai,n)

    integer,                  intent(in)    :: n
    real(r8), dimension(:,:), intent(inout) :: a
    real(r8), dimension(:,:), intent(out)   :: ai

    real(r8), dimension(n)   :: d
    real(r8), dimension(n,n) :: wrk

    real(r8), dimension(4) :: tmp

    integer :: nm1, i, j, k, kp1, jm1, p

    continue

    nm1=n-1

    do i = 1,n
      do j = 1,n
        wrk(i,j) = a(i,j)
      enddo
    enddo

!   First row of the inverse matrix, finds HH factorization of matrix ``a''

    ai(:,1) = 0.0_r8
    ai(1,1) = 1.0_r8

    do k=1,nm1
      tmp(1)=0.0_r8
      do i=k,n
        tmp(1)=tmp(1)+a(i,k)*a(i,k)
      enddo
!     store new a(k,k) into d(k) for back substitution
      tmp(1)=sqrt(tmp(1))
      tmp(4)=sign(tmp(1),a(k,k))
      a(k,k)=     tmp(4)+a(k,k)
      d(k)  =    -tmp(4)
      tmp(4)=a(k,k)*tmp(1)
      tmp(2)=abs(tmp(4))
!     modify k+1, k+2, ..,n col of A  using u (i.e.,the kth col of a)
      kp1=k+1
      do j=kp1,n
        tmp(3)=0.0_r8
        do i=k,n
          tmp(3)=tmp(3)+a(i,k)*a(i,j)
        enddo
        tmp(3)=tmp(3)/tmp(2)
        do i=k,n
          a(i,j)=a(i,j)-a(i,k)*tmp(3)
        enddo
      enddo
!     do same for rhs
      tmp(3)=0.0_r8
      do i=k,n
          tmp(3)=tmp(3)+a(i,k)*ai(i,1)
      enddo
      tmp(3)=tmp(3)/tmp(2)
      do i=k,n
        ai(i,1)=ai(i,1)-a(i,k)*tmp(3)
      enddo
    enddo
    d(n)=a(n,n)

!   do back substitution

    p1_back_substitution : do i=1,n
      j=n-i+1
      ai(j,1)=ai(j,1)/d(j)
      jm1=j-1
      if (jm1 ==  0) cycle p1_back_substitution
      do k=1,jm1
        ai(k,1)=ai(k,1)-a(k,j)*ai(j,1)
      enddo
    enddo p1_back_substitution

    do p = 2,n
      ai(:,p) = 0.0_r8
      ai(p,p) = 1.0_r8

      do k=1,nm1
        tmp(2)=-a(k,k)*d(k)
        tmp(3)=0.0_r8
        do i=k,n
          tmp(3)=tmp(3)+a(i,k)*ai(i,p)
        enddo
        tmp(3)=tmp(3)/tmp(2)
        do i=k,n
          ai(i,p)=ai(i,p)-a(i,k)*tmp(3)
        enddo
      enddo

!     do back substitution

      p2n_back_substitution : do i=1,n
        j=n-i+1
        ai(j,p)=ai(j,p)/d(j)
        jm1=j-1
        if (jm1 == 0) cycle p2n_back_substitution
        do k=1,jm1
          ai(k,p)=ai(k,p)-a(k,j)*ai(j,p)
        enddo
      enddo p2n_back_substitution
    enddo

    do i = 1,n
      do j = 1,n
        a(i,j) = wrk(i,j)
      enddo
    enddo

  end subroutine hh_inverse_full


!=============================================================================80
!
! Householder routine for finding inverse of a matrix - half precision
!
!=============================================================================80
  subroutine hh_inverse_half(a,ai,n)

    integer,                  intent(in)    :: n
    real(r4), dimension(:,:), intent(inout) :: a
    real(r4), dimension(:,:), intent(out)   :: ai

    real(r4), dimension(n)   :: d
    real(r4), dimension(n,n) :: wrk

    real(r4), dimension(4) :: tmp

    integer :: nm1, i, j, k, kp1, jm1, p

    continue

    nm1=n-1

    do i = 1,n
      do j = 1,n
        wrk(i,j) = a(i,j)
      enddo
    enddo

!   First row of the inverse matrix, finds HH factorization of matrix ``a''

    ai(:,1) = 0.0_r4
    ai(1,1) = 1.0_r4

    do k=1,nm1
      tmp(1)=0.0_r4
      do i=k,n
        tmp(1)=tmp(1)+a(i,k)*a(i,k)
      enddo
!     store new a(k,k) into d(k) for back substitution
      tmp(1)=sqrt(tmp(1))
      tmp(4)=sign(tmp(1),a(k,k))
      a(k,k)=     tmp(4)+a(k,k)
      d(k)  =    -tmp(4)
      tmp(4)=a(k,k)*tmp(1)
      tmp(2)=abs(tmp(4))
!     modify k+1, k+2, ..,n col of A  using u (i.e.,the kth col of a)
      kp1=k+1
      do j=kp1,n
        tmp(3)=0.0_r4
        do i=k,n
          tmp(3)=tmp(3)+a(i,k)*a(i,j)
        enddo
        tmp(3)=tmp(3)/tmp(2)
        do i=k,n
          a(i,j)=a(i,j)-a(i,k)*tmp(3)
        enddo
      enddo
!     do same for rhs
      tmp(3)=0.0_r4
      do i=k,n
          tmp(3)=tmp(3)+a(i,k)*ai(i,1)
      enddo
      tmp(3)=tmp(3)/tmp(2)
      do i=k,n
        ai(i,1)=ai(i,1)-a(i,k)*tmp(3)
      enddo
    enddo
    d(n)=a(n,n)

!   do back substitution

    p1_back_substitution : do i=1,n
      j=n-i+1
      ai(j,1)=ai(j,1)/d(j)
      jm1=j-1
      if (jm1 ==  0) cycle p1_back_substitution
      do k=1,jm1
        ai(k,1)=ai(k,1)-a(k,j)*ai(j,1)
      enddo
    enddo p1_back_substitution

    do p = 2,n
      ai(:,p) = 0.0_r4
      ai(p,p) = 1.0_r4

      do k=1,nm1
        tmp(2)=-a(k,k)*d(k)
        tmp(3)=0.0_r4
        do i=k,n
          tmp(3)=tmp(3)+a(i,k)*ai(i,p)
        enddo
        tmp(3)=tmp(3)/tmp(2)
        do i=k,n
          ai(i,p)=ai(i,p)-a(i,k)*tmp(3)
        enddo
      enddo

!     do back substitution

      p2n_back_substitution : do i=1,n
        j=n-i+1
        ai(j,p)=ai(j,p)/d(j)
        jm1=j-1
        if (jm1 == 0) cycle p2n_back_substitution
        do k=1,jm1
          ai(k,p)=ai(k,p)-a(k,j)*ai(j,p)
        enddo
      enddo p2n_back_substitution
    enddo

    do i = 1,n
      do j = 1,n
        a(i,j) = wrk(i,j)
      enddo
    enddo

  end subroutine hh_inverse_half

  subroutine gaussian_elimination(a_in,x,b_in,bad_col)

    use kinddefs, only: dp

    real(dp), dimension(:,:), intent(in)  :: a_in
    real(dp), dimension(:),   intent(out) :: x
    real(dp), dimension(:),   intent(in)  :: b_in
    integer,                  intent(out) :: bad_col

    integer :: i, j, k, n, biggest_i

    real(dp), dimension(size(a_in,1),size(a_in,2)) :: a
    real(dp), dimension(size(b_in,1)) :: b
    real(dp) :: temp

    real(dp), parameter :: zero = 0.0_dp
    real(dp), parameter :: one = 1.0_dp

    continue

    x = zero
    bad_col = 0

    n = size(a,1)

    a = a_in
    b = b_in

    do k = 1,n-1
      biggest_i = k
      do i = k+1, n
        if ( abs(a(i,k)) > abs(a(biggest_i,k)) ) then
          biggest_i = i
        end if
      end do
      need_to_exchange_rows : if ( k /= biggest_i )  then
        exchange_col : do j = k, n ! 1:k-1 shoud be zero
          temp = a(k,j)
          a(k,j) = a(biggest_i,j)
          a(biggest_i,j) = temp
        end do exchange_col
        temp = b(k)
        b(k) = b(biggest_i)
        b(biggest_i) = temp
      end if need_to_exchange_rows
      normalize : do j = k+1,n
        if ( abs( a(k,k) ) < spacing( real(a(k,j),dp) ) ) then
          bad_col = k
          return
        end if
        a(k,j) = a(k,j) / a(k,k)
      end do normalize
      if ( abs( a(k,k) ) < spacing( real(b(k),dp) ) ) then
        bad_col = k
        return
      end if
      b(k) = b(k) / a(k,k)
      a(k,k) = one
      do i = k+1, n
        do j = k+1, n
          a(i,j) = a(i,j) - a(k,j) * a(i,k)
        end do
        b(i) = b(i) - b(k) * a(i,k)
        a(i,k) = zero
      end do
    end do

    if ( abs( a(n,n) ) < spacing( real(b(n),dp) ) ) then
      bad_col = n
      return
    end if
    b(n) = b(n) / a(n,n)
    a(n,n) = one

    do j = n, 1, -1
      do i = j-1, 1, -1
        b(i) = b(i) - b(j)*a(i,j)
        a(i,j) = zero
      end do
    end do

    x = b

  end subroutine gaussian_elimination

end module linear_algebra

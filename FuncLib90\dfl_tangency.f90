!============================ DFP_TANGENCY ===================================80
!
! Roe flux jacobians (+) for inviscid
!
! Simpler expression for flux given:
!         (1) "right" state is relected state
!         (2) no entropy fix and face_speed = 0.
!
! Note: ql, qr are conservative variables
!
!=============================================================================80

  pure function dfl_tangency(xnorm, ynorm, znorm, area, ql, gamma, gm1)

    real(dp), intent(in) :: xnorm, ynorm, znorm, area

    real(dp), dimension(5), intent(in) :: ql

    real(dp), intent(in) :: gamma, gm1

    real(dp), dimension(5,5)           :: dfl_tangency

    real(dp) :: termrl, termul, termvl, termwl, termpl

    real(dp) :: c2l
    real(dp) :: c2lrl,c2lul,c2lvl,c2lwl,c2lpl

    real(dp) :: c
    real(dp) :: crl,cul,cvl,cwl,cpl

    real(dp) :: ubarl
    real(dp) :: ubarlrl,ubarlul,ubarlvl,ubarlwl

!   real(dp) :: hl
!   real(dp) :: hlrl,hlul,hlvl,hlwl,hlpl

    real(dp) :: enrgyl
!   real(dp) :: enrgylpl

    real(dp) :: pressl
    real(dp) :: presslrl,presslul,presslvl,presslwl,presslpl

    real(dp) :: q2l
    real(dp) :: q2lrl,q2lul,q2lvl,q2lwl

    real(dp) :: ul
    real(dp) :: ulrl,ulul

    real(dp) :: vl
    real(dp) :: vlrl,vlvl

    real(dp) :: wl
    real(dp) :: wlrl,wlwl

    real(dp) :: rhol
    real(dp) :: rholrl

    real(dp), dimension(5) :: dq

  continue

      rhol = ql(1)
        rholrl = 1.0_dp
      ul = ql(2) / rhol
        ulrl = -ul/rhol
        ulul = 1.0_dp / rhol
      vl = ql(3) / rhol
        vlrl = -vl/rhol
        vlvl = 1.0_dp / rhol
      wl = ql(4) / rhol
        wlrl = -wl/rhol
        wlwl = 1.0_dp / rhol

      q2l = ul*ul + vl*vl + wl*wl
        q2lrl = 2.0_dp*ul*ulrl + 2.0_dp*vl*vlrl + 2.0_dp*wl*wlrl
        q2lul = 2.0_dp*ul*ulul
        q2lvl = 2.0_dp*vl*vlvl
        q2lwl = 2.0_dp*wl*wlwl

      enrgyl = ql(5)
!       enrgylpl = 1.0_dp

      pressl = gm1*(enrgyl - 0.5_dp*rhol*q2l)
        presslrl = -0.5_dp*gm1*(rhol*q2lrl + q2l*rholrl)
        presslul = -0.5_dp*gm1*rhol*q2lul
        presslvl = -0.5_dp*gm1*rhol*q2lvl
        presslwl = -0.5_dp*gm1*rhol*q2lwl
        presslpl = gm1

!     Hl = (enrgyl + pressl)/rhol
!       Hlrl = (rhol*(presslrl) - (enrgyl+pressl)*rholrl) / rhol / rhol
!       Hlul = (rhol*(presslul)) / rhol / rhol
!       Hlvl = (rhol*(presslvl)) / rhol / rhol
!       Hlwl = (rhol*(presslwl)) / rhol / rhol
!       Hlpl = (rhol*(enrgylpl+presslpl)) / rhol / rhol

      ubarl = xnorm*ul + ynorm*vl + znorm*wl
        ubarlrl = xnorm*ulrl + ynorm*vlrl + znorm*wlrl
        ubarlul = xnorm*ulul
        ubarlvl = ynorm*vlvl
        ubarlwl = znorm*wlwl

      c2l = gamma*pressl/rhol
        c2lrl = gamma*( presslrl/rhol - pressl*rholrl/rhol**2 )
        c2lul = gamma*( presslul/rhol                         )
        c2lvl = gamma*( presslvl/rhol                         )
        c2lwl = gamma*( presslwl/rhol                         )
        c2lpl = gamma*( presslpl/rhol                         )

      c   = sqrt( c2l + gm1*0.5_dp*ubarl**2 )
        crl = 0.5_dp*( c2lrl + gm1*ubarl*ubarlrl )/c
        cul = 0.5_dp*( c2lul + gm1*ubarl*ubarlul )/c
        cvl = 0.5_dp*( c2lvl + gm1*ubarl*ubarlvl )/c
        cwl = 0.5_dp*( c2lwl + gm1*ubarl*ubarlwl )/c
        cpl = 0.5_dp*( c2lpl                     )/c

! term = pressl + rhol*( ubarl**2 + c*ubarl )

        termrl = presslrl + rholrl*( ubarl**2 + c*ubarl )
        termul = presslul
        termvl = presslvl
        termwl = presslwl
        termpl = presslpl

        termrl = termrl + rhol*( 2.0_dp*ubarl*ubarlrl + crl*ubarl + c*ubarlrl )
        termul = termul + rhol*( 2.0_dp*ubarl*ubarlul + cul*ubarl + c*ubarlul )
        termvl = termvl + rhol*( 2.0_dp*ubarl*ubarlvl + cvl*ubarl + c*ubarlvl )
        termwl = termwl + rhol*( 2.0_dp*ubarl*ubarlwl + cwl*ubarl + c*ubarlwl )
        termpl = termpl + rhol*(                      + cpl*ubarl            )

! res2 = area*xnorm*term
! res3 = area*ynorm*term
! res4 = area*znorm*term

        dq(1) = area*termrl
        dq(2) = area*termul
        dq(3) = area*termvl
        dq(4) = area*termwl
        dq(5) = area*termpl

      dfl_tangency(1,1:5) = my_0
      dfl_tangency(2,1:5) = xnorm*dq(1:5)
      dfl_tangency(3,1:5) = ynorm*dq(1:5)
      dfl_tangency(4,1:5) = znorm*dq(1:5)
      dfl_tangency(5,1:5) = my_0

  end function dfl_tangency

module bc_types

  use kinddefs, only : dp, system_i1

  implicit none

  private

  !beginMakeComplexInterface
  public :: nullify_bc
  public :: allocate_dummy_bc
  public :: allocate_bc
  public :: deallocate_bc
  !endMakeComplexInterface
  public :: allocate_bcsoln
  public :: deallocate_bcsoln

  public :: bcgrid_type
  public :: bcsoln_type
  public :: local_2_global_type
  public :: bcc_type
  public :: periodic_type

  type local_2_global_type
    integer                        :: nbnode
    integer, dimension(:), pointer :: bcmap ! map bc nodes from
                                            ! local grid to global grid
  end type local_2_global_type

  integer, public, dimension(2,3), parameter :: tri_side_node = &
    reshape( (/ 2, 3, 3, 1, 1, 2 /), (/2,3/) )

  integer, public, dimension(2,4), parameter :: quad_side_node = &
    reshape( (/ 1, 2, 2, 3, 3, 4, 4, 1 /), (/2,4/) )

  type bcgrid_type

    integer :: ibc             ! Boundary type identifier
    integer :: nbnode          ! Number of boundary nodes
    integer :: nbfacet         ! Number of boundary triangles
    integer :: nbfaceq         ! Number of boundary quadrilaterals
    integer :: nbnodeg         ! Number of boundary nodes (global)
    integer :: nbfacetg        ! Number of boundary triangles (global)
    integer :: nbfaceqg        ! Number of boundary quads (global)

    character(120) :: bc_family     = '' ! Family name
    character(120) :: bc_family_org = '' ! Family name

    ! List of boundary nodes on current boundary
    integer,  dimension(:),   pointer :: ibnode => null()
    ! Slen at the wall
    real(dp),  dimension(:), pointer :: slen_wall => null()
    ! Node corresponding to nearest node off boundary
    integer, dimension(:), pointer :: node_nearest
    ! Triangular face-to-node indices into ibnode array
    ! entry 4 is c2n index and entry 5 is elem index
    integer,  dimension(:,:), pointer :: f2ntb => null()
    ! Quadrilateral face-to-node indices into ibnode array
    ! entry 5 is c2n index and entry 6 is elem index
    integer,  dimension(:,:), pointer :: f2nqb => null()
    ! If triangular face is associated with partition
    integer,  dimension(:),   pointer :: face_bit => null()
    ! If quadrilateral face is associated with partition
    integer,  dimension(:),   pointer :: face_bitq => null()

    real(dp), dimension(:), pointer :: bxn => null() !x-norm at bound nodes
    real(dp), dimension(:), pointer :: byn => null() !y-norm at bound nodes
    real(dp), dimension(:), pointer :: bzn => null() !z-norm at bound nodes

    real(dp), dimension(:), pointer :: bdxdt  => null() ! u of bound nodes
    real(dp), dimension(:), pointer :: bdydt  => null() ! v of bound nodes
    real(dp), dimension(:), pointer :: bdzdt  => null() ! w of bound nodes

    real(dp), dimension(:), pointer :: bfacespeed => null() ! face speed

! extra physics associated with a boundary instance: mostly for unsteady
    integer  :: time_samples = 0
    real(dp) :: frequency    =  0.0_dp

! cell-centered pointers
    integer, dimension(:), pointer :: qcell_ptr_t => null()
    integer, dimension(:), pointer :: qcell_ptr_q => null()

! Triangular face user-defined bc data
    real(dp),  dimension(:,:,:), pointer :: dbfacet    => null()
    integer,   dimension(:,:  ), pointer :: l2g_bfacet => null()

! Multiblock interface data
    integer :: interface_boundary = 0
    integer, dimension(:), pointer :: interface_data => null()

  end type bcgrid_type

  type bcsoln_type
    integer                           :: nbnode = 0
!   species mass diffusion flux
    real(dp), dimension(:,:), pointer :: mass_flux_i      => null()
!   species mass diffusion flux from CHarring Ablator Responce (CHAR) code
    real(dp), dimension(:,:), pointer :: mass_flux_CHAR   => null()
!   surface recession due to ablation
    real(dp), dimension(:),   pointer :: recession        => null()
!   energy diffusion flux
    real(dp), dimension(:,:), pointer :: energy_flux_j    => null()
!   x-momentum diffusion flux
    real(dp), dimension(:,:), pointer :: taux_flux_k      => null()
!   y-momentum diffusion flux
    real(dp), dimension(:,:), pointer :: tauy_flux_k      => null()
!   z-momentum diffusion flux
    real(dp), dimension(:,:), pointer :: tauz_flux_k      => null()
    real(dp), dimension(:),   pointer :: radiation_flux   => null()
    real(dp), dimension(:),   pointer :: temperature      => null()
    real(dp), dimension(:),   pointer :: mdot             => null()
    real(dp), dimension(:),   pointer :: char_frac        => null()

    real(dp), dimension(:),   pointer :: gradn_sqrtk   => null()
    real(dp), dimension(:),   pointer :: k_wf          => null()
    real(dp), dimension(:),   pointer :: omega_wf      => null()
    real(dp), dimension(:),   pointer :: mu_t_wf       => null()
    real(dp), dimension(:),   pointer :: u_tau         => null()
    real(dp), dimension(:),   pointer :: v_corr        => null()
    real(dp), dimension(:),   pointer :: rho           => null()
    real(dp), dimension(:),   pointer :: nu            => null()
    real(dp), dimension(:),   pointer :: uplus_wf      => null()
    real(dp), dimension(:),   pointer :: yplus_wf      => null()
    real(dp), dimension(:),   pointer :: phi_wf        => null()
    ! block interface
    integer,  dimension(:),   pointer :: face1_ibnode  => null()
    integer,  dimension(:),   pointer :: face2_ibnode  => null()
    real(dp), dimension(:,:), pointer :: face1_data    => null()
    real(dp), dimension(:,:), pointer :: face2_data    => null()

    real(dp), dimension(:),   pointer :: cxn           => null()
!   total x component of surface area
    real(dp), dimension(:),   pointer :: cyn           => null()
!   total y component of surface area
    real(dp), dimension(:),   pointer :: czn           => null()
!   total z component of surface area
    logical :: find_shared_surface_nodes = .true.

    real(dp), dimension(:),   pointer :: jet_inflow    => null()
    real(dp), dimension(:,:), pointer :: jet_pressure_jac  => null()
    real(dp), dimension(:,:), pointer :: jet_enthalpy_ij   => null()

    real(dp), dimension(:),   pointer :: fix_inflow    => null()
    real(dp), dimension(:,:), pointer :: fix_pressure_jac  => null()
    real(dp), dimension(:,:), pointer :: fix_enthalpy_ij   => null()

  end type bcsoln_type

  type bcc_type

!   Cache friendly information for cell-centered bc.

    integer :: n_faces0, n_faces01, n_qt

    integer, dimension(:), pointer :: cell      => null()
    integer, dimension(:), pointer :: ibc       => null()
    integer, dimension(:), pointer :: f2n_index => null()

    integer, dimension(:,:), pointer :: nodes   => null()

    real(dp), dimension(:), pointer :: xn => null()
    real(dp), dimension(:), pointer :: yn => null()
    real(dp), dimension(:), pointer :: zn => null()

    real(dp), dimension(:), pointer :: area => null()

    real(dp), dimension(:), pointer :: xface    => null()
    real(dp), dimension(:), pointer :: yface    => null()
    real(dp), dimension(:), pointer :: zface    => null()
    real(dp), dimension(:), pointer :: slenface => null()

    real(dp), dimension(:), pointer :: cdt_bc_inv => null()
    real(dp), dimension(:), pointer :: cdt_bc_vis => null()

    integer(system_i1), dimension(:), pointer :: ibf  => null()

    real(dp), dimension(:,:), pointer :: bcf => null()

    !...crs for center-lsq (boundary).
    integer, dimension(:), pointer :: clsq_ib => null()
    integer, dimension(:), pointer :: clsq_jb => null()

    !...crs for face-lsq (boundary).
    integer, dimension(:), pointer :: flsq_ib => null()
    integer, dimension(:), pointer :: flsq_jb => null()

    !...T variable.
    real(dp), dimension(:,:), pointer :: qt => null()

  end type bcc_type

  type periodic_type
    integer :: n
    integer, dimension(:), pointer :: list   ! Grid points in the periodic group
    integer, dimension(:), pointer :: mlist  ! Corresponding matrix-numbered pts
  end type periodic_type

contains

!============================= NULLIFY_BC ====================================80
!
!   Deallocates memory for the boundaries
!
!=============================================================================80

  subroutine nullify_bc( bc )

    type(bcgrid_type), intent(inout) :: bc

    continue

    nullify( bc%ibnode )
    nullify( bc%slen_wall )
    nullify( bc%node_nearest )
    nullify( bc%f2ntb )
    nullify( bc%f2nqb )
    nullify( bc%face_bit )
    nullify( bc%face_bitq )
    nullify( bc%bxn )
    nullify( bc%byn )
    nullify( bc%bzn )
    nullify( bc%bdxdt )
    nullify( bc%bdydt )
    nullify( bc%bdzdt )
    nullify( bc%bfacespeed )
    nullify( bc%qcell_ptr_t )
    nullify( bc%qcell_ptr_q )
    nullify( bc%dbfacet )
    nullify( bc%l2g_bfacet )
    nullify( bc%interface_data )

  end subroutine nullify_bc

!============================= ALLOCATE_DUMMY_BC =============================80
!
! Allocates minimal memory for the bc% derived type arrays.
!
!=============================================================================80

  subroutine allocate_dummy_bc( bc )

    use allocations, only : my_alloc_ptr

    type(bcgrid_type), intent(inout) :: bc

  continue

    call my_alloc_ptr( bc%ibnode,    1 )
    call my_alloc_ptr( bc%slen_wall, 1 )
    call my_alloc_ptr( bc%node_nearest, 1 )
    call my_alloc_ptr( bc%f2ntb,     1, 5 )
    call my_alloc_ptr( bc%f2nqb,     1, 6 )

    call my_alloc_ptr( bc%face_bit,  1 )
    call my_alloc_ptr( bc%face_bitq, 1 )

    call my_alloc_ptr( bc%bxn, 1 )
    call my_alloc_ptr( bc%byn, 1 )
    call my_alloc_ptr( bc%bzn, 1 )

    call my_alloc_ptr( bc%bdxdt, 1 )
    call my_alloc_ptr( bc%bdydt, 1 )
    call my_alloc_ptr( bc%bdzdt, 1 )

    call my_alloc_ptr( bc%bfacespeed, 1 )

    call my_alloc_ptr( bc%qcell_ptr_t, 1 )
    call my_alloc_ptr( bc%qcell_ptr_q, 1 )

    call my_alloc_ptr( bc%dbfacet,    1, 3, 5 )
    call my_alloc_ptr( bc%l2g_bfacet, 1, 1 )
    call my_alloc_ptr( bc%interface_data, 1 )

  end subroutine allocate_dummy_bc

!============================= DEALLOCATE_BC =================================80
!
!   Deallocates memory for the boundaries
!
!=============================================================================80

  subroutine deallocate_bc( bc )

    type(bcgrid_type), intent(inout) :: bc

    continue

    if (associated(bc%ibnode))          deallocate(bc%ibnode)
    if (associated(bc%slen_wall))       deallocate(bc%slen_wall)
    if (associated(bc%node_nearest))    deallocate(bc%node_nearest)
    if (associated(bc%f2ntb))           deallocate(bc%f2ntb)
    if (associated(bc%f2nqb))           deallocate(bc%f2nqb)
    if (associated(bc%face_bit))        deallocate(bc%face_bit)
    if (associated(bc%face_bitq))       deallocate(bc%face_bitq)
    if (associated(bc%bxn))             deallocate(bc%bxn)
    if (associated(bc%byn))             deallocate(bc%byn)
    if (associated(bc%bzn))             deallocate(bc%bzn)
    if (associated(bc%bdxdt))           deallocate(bc%bdxdt)
    if (associated(bc%bdydt))           deallocate(bc%bdydt)
    if (associated(bc%bdzdt))           deallocate(bc%bdzdt)
    if (associated(bc%bfacespeed))      deallocate(bc%bfacespeed)
    if (associated(bc%qcell_ptr_t))     deallocate(bc%qcell_ptr_t)
    if (associated(bc%qcell_ptr_q))     deallocate(bc%qcell_ptr_q)
    if (associated(bc%dbfacet))         deallocate(bc%dbfacet)
    if (associated(bc%l2g_bfacet))      deallocate(bc%l2g_bfacet)
    if (associated(bc%interface_data))  deallocate(bc%interface_data)

  end subroutine deallocate_bc

!============================= ALLOCATE_BC ===================================80
!
! Allocates memory for the boundaries
! SP Note: Don't rely on this routine for much of anything...
!
! Note: using specific-named intrinsic max0 to facilitate conversion
!       to complex variable version of code
!
!=============================================================================80

  subroutine allocate_bc( bc, cc_primal, need_grid_velocity )

    use allocations, only : my_alloc_ptr

    type(bcgrid_type), intent(inout) :: bc
    logical,           intent(in   ) :: cc_primal, need_grid_velocity

  continue

    call my_alloc_ptr( bc%ibnode,     max0(bc%nbnode,1) )
    call my_alloc_ptr( bc%f2ntb,      max0(bc%nbfacet,1), 5 )
    call my_alloc_ptr( bc%f2nqb,      max0(bc%nbfaceq,1), 6 )
    call my_alloc_ptr( bc%dbfacet,    max0(bc%nbfacet,1), 3, 5 )
    call my_alloc_ptr( bc%l2g_bfacet, max0(bc%nbfacet,1), 1 )
    call my_alloc_ptr( bc%bxn,        max0(bc%nbnode,1) )
    call my_alloc_ptr( bc%byn,        max0(bc%nbnode,1) )
    call my_alloc_ptr( bc%bzn,        max0(bc%nbnode,1) )

    if (need_grid_velocity) then
      call my_alloc_ptr( bc%bdxdt,      max0(bc%nbnode,1) )
      call my_alloc_ptr( bc%bdydt,      max0(bc%nbnode,1) )
      call my_alloc_ptr( bc%bdzdt,      max0(bc%nbnode,1) )
      call my_alloc_ptr( bc%bfacespeed, max0(bc%nbnode,1) )
    else
      call my_alloc_ptr( bc%bdxdt,      1 )
      call my_alloc_ptr( bc%bdydt,      1 )
      call my_alloc_ptr( bc%bdzdt,      1 )
      call my_alloc_ptr( bc%bfacespeed, 1 )
    end if

    call my_alloc_ptr( bc%face_bit,  max0(bc%nbfacet,1) )
    call my_alloc_ptr( bc%face_bitq, max0(bc%nbfaceq,1) )

    if ( cc_primal ) then
      call my_alloc_ptr(bc%qcell_ptr_t, max0(bc%nbfacet,1))
      call my_alloc_ptr(bc%qcell_ptr_q, max0(bc%nbfaceq,1))
    else
      call my_alloc_ptr(bc%qcell_ptr_t, 1)
      call my_alloc_ptr(bc%qcell_ptr_q, 1)
    endif

    if ( cc_primal ) then
      call my_alloc_ptr(bc%slen_wall,    1)
      call my_alloc_ptr(bc%node_nearest, 1)
    else
      call my_alloc_ptr(bc%slen_wall,    max0(bc%nbnode,1) )
      call my_alloc_ptr(bc%node_nearest, max0(bc%nbnode,1) )
    endif

    call my_alloc_ptr(bc%interface_data, 1)

  end subroutine allocate_bc

!=========================== ALLOCATE_BCSOLN =================================80
!
! Allocates memory for the boundary diffusion flux
!
! Note: using specific-named intrinsic max0 to facilitate conversion
!       to complex variable version of code
!
!=============================================================================80

  subroutine allocate_bcsoln( nbnode, bcsoln )

    use allocations,     only : my_alloc_ptr
    use generic_gas_map, only : n_species, n_energy, n_mom, n_tot_g, n_pjac

    integer,           intent(in   ) :: nbnode
    type(bcsoln_type), intent(inout) :: bcsoln

    continue

    call my_alloc_ptr( bcsoln%mass_flux_i,   n_species, max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%mass_flux_CHAR,n_species, max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%recession,                max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%energy_flux_j, n_energy,  max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%taux_flux_k,   n_mom,     max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%tauy_flux_k,   n_mom,     max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%tauz_flux_k,   n_mom,     max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%radiation_flux,           max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%temperature,              max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%mdot,                     max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%char_frac,                max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%gradn_sqrtk,              max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%k_wf,                     max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%omega_wf,                 max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%mu_t_wf,                  max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%u_tau,                    max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%v_corr,                   max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%rho,                      max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%nu,                       max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%uplus_wf,                 max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%yplus_wf,                 max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%phi_wf,                   max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%face1_ibnode,             max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%face2_ibnode,             max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%face1_data,     5,        max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%face2_data,     5,        max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%cxn,                      max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%cyn,                      max0(nbnode,1) )
    call my_alloc_ptr( bcsoln%czn,                      max0(nbnode,1) )

    call my_alloc_ptr( bcsoln%jet_inflow,    n_tot_g)
    call my_alloc_ptr( bcsoln%jet_pressure_jac, n_pjac, n_mom)
    call my_alloc_ptr( bcsoln%jet_enthalpy_ij , n_species, n_energy)

    call my_alloc_ptr( bcsoln%fix_inflow,    n_tot_g)
    call my_alloc_ptr( bcsoln%fix_pressure_jac, n_pjac, n_mom)
    call my_alloc_ptr( bcsoln%fix_enthalpy_ij , n_species, n_energy)

    bcsoln%mass_flux_i    = 0._dp
    bcsoln%mass_flux_CHAR = 0._dp
    bcsoln%recession      = 0._dp
    bcsoln%energy_flux_j  = 0._dp
    bcsoln%taux_flux_k    = 0._dp
    bcsoln%tauy_flux_k    = 0._dp
    bcsoln%tauz_flux_k    = 0._dp
    bcsoln%radiation_flux = 0._dp
    bcsoln%temperature    = -9999._dp
    bcsoln%mdot           = 0._dp
    bcsoln%char_frac      = 0._dp
    bcsoln%nbnode         = nbnode
    bcsoln%gradn_sqrtk    = 0._dp
    bcsoln%k_wf           = 0._dp
    bcsoln%omega_wf       = 0._dp
    bcsoln%mu_t_wf        = 0._dp
    bcsoln%u_tau          = 0._dp
    bcsoln%v_corr         = 0._dp
    bcsoln%rho            = 0._dp
    bcsoln%nu             = 0._dp
    bcsoln%uplus_wf       = 0._dp
    bcsoln%yplus_wf       = 0._dp
    bcsoln%phi_wf         = 0._dp
    bcsoln%face1_ibnode   = 0._dp
    bcsoln%face2_ibnode   = 0._dp
    bcsoln%face1_data     = 0._dp
    bcsoln%face2_data     = 0._dp
    bcsoln%cxn            = 0._dp
    bcsoln%cyn            = 0._dp
    bcsoln%czn            = 0._dp

    bcsoln%jet_inflow     = 0._dp

    bcsoln%fix_inflow     = 0._dp

  end subroutine allocate_bcsoln

!========================= DEALLOCATE_BCSOLN =================================80
!
! frees memory for the boundary diffusion flux
!
!=============================================================================80

  subroutine deallocate_bcsoln( bcsoln )

    type(bcsoln_type), intent(inout) :: bcsoln

    continue

    if ( associated(bcsoln%mass_flux_i) )    deallocate(bcsoln%mass_flux_i)
    bcsoln%mass_flux_i => null()
    if ( associated(bcsoln%mass_flux_CHAR) ) deallocate(bcsoln%mass_flux_CHAR)
    bcsoln%mass_flux_CHAR => null()
    if ( associated(bcsoln%recession) )      deallocate(bcsoln%recession)
    bcsoln%recession => null()
    if ( associated(bcsoln%energy_flux_j) )  deallocate(bcsoln%energy_flux_j)
    bcsoln%energy_flux_j => null()
    if ( associated(bcsoln%taux_flux_k) )    deallocate(bcsoln%taux_flux_k)
    bcsoln%taux_flux_k => null()
    if ( associated(bcsoln%tauy_flux_k) )    deallocate(bcsoln%tauy_flux_k)
    bcsoln%tauy_flux_k => null()
    if ( associated(bcsoln%tauz_flux_k) )    deallocate(bcsoln%tauz_flux_k)
    bcsoln%tauz_flux_k => null()
    if ( associated(bcsoln%radiation_flux) ) deallocate(bcsoln%radiation_flux)
    bcsoln%radiation_flux => null()
    if ( associated(bcsoln%temperature) )    deallocate(bcsoln%temperature)
    bcsoln%temperature => null()
    if ( associated(bcsoln%mdot) )           deallocate(bcsoln%mdot)
    bcsoln%mdot => null()
    if ( associated(bcsoln%char_frac) )      deallocate(bcsoln%char_frac)
    bcsoln%char_frac => null()
    if ( associated(bcsoln%gradn_sqrtk) )    deallocate(bcsoln%gradn_sqrtk)
    bcsoln%gradn_sqrtk => null()
    if ( associated(bcsoln%k_wf) )           deallocate(bcsoln%k_wf)
    bcsoln%k_wf => null()
    if ( associated(bcsoln%omega_wf) )       deallocate(bcsoln%omega_wf)
    bcsoln%omega_wf => null()
    if ( associated(bcsoln%mu_t_wf) )        deallocate(bcsoln%mu_t_wf)
    bcsoln%mu_t_wf => null()
    if ( associated(bcsoln%u_tau) )          deallocate(bcsoln%u_tau)
    bcsoln%u_tau => null()
    if ( associated(bcsoln%v_corr) )         deallocate(bcsoln%v_corr)
    bcsoln%v_corr => null()
    if ( associated(bcsoln%rho) )            deallocate(bcsoln%rho)
    bcsoln%rho => null()
    if ( associated(bcsoln%nu) )             deallocate(bcsoln%nu)
    bcsoln%nu => null()
    if ( associated(bcsoln%uplus_wf) )       deallocate(bcsoln%uplus_wf)
    bcsoln%uplus_wf => null()
    if ( associated(bcsoln%yplus_wf) )       deallocate(bcsoln%yplus_wf)
    bcsoln%yplus_wf => null()
    if ( associated(bcsoln%phi_wf) )         deallocate(bcsoln%phi_wf)
    bcsoln%phi_wf => null()

    if ( associated(bcsoln%face1_ibnode) )   deallocate(bcsoln%face1_ibnode)
    bcsoln%face1_ibnode => null()
    if ( associated(bcsoln%face2_ibnode) )   deallocate(bcsoln%face2_ibnode)
    bcsoln%face2_ibnode => null()
    if ( associated(bcsoln%face1_data) )     deallocate(bcsoln%face1_data)
    bcsoln%face1_data => null()
    if ( associated(bcsoln%face2_data) )     deallocate(bcsoln%face2_data)
    bcsoln%face2_data => null()

    if ( associated(bcsoln%cxn) )            deallocate(bcsoln%cxn)
    bcsoln%cxn => null()
    if ( associated(bcsoln%cyn) )            deallocate(bcsoln%cyn)
    bcsoln%cyn => null()
    if ( associated(bcsoln%czn) )            deallocate(bcsoln%czn)
    bcsoln%czn => null()

    if ( associated(bcsoln%jet_inflow) )     deallocate( bcsoln%jet_inflow)
    bcsoln%jet_inflow => null()
    if ( associated(bcsoln%jet_pressure_jac))deallocate(bcsoln%jet_pressure_jac)
    bcsoln%jet_pressure_jac => null()
    if ( associated(bcsoln%jet_enthalpy_ij)) deallocate(bcsoln%jet_enthalpy_ij)
    bcsoln%jet_enthalpy_ij => null()

    if ( associated(bcsoln%fix_inflow) )     deallocate( bcsoln%fix_inflow)
    bcsoln%fix_inflow => null()
    if ( associated(bcsoln%fix_pressure_jac))deallocate(bcsoln%fix_pressure_jac)
    bcsoln%fix_pressure_jac => null()
    if ( associated(bcsoln%fix_enthalpy_ij)) deallocate(bcsoln%fix_enthalpy_ij)
    bcsoln%fix_enthalpy_ij => null()

  end subroutine deallocate_bcsoln

end module bc_types

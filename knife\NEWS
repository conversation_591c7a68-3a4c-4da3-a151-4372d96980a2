
Friday, April 13, 2012: At version 1.2.7

- use autoconf standard FC_FUNC_ macros to name mangle fortran interface
- use config.h to define FC_FUNC_ (for xlf and xlc)

Thursday, November 17, 2011: At version 1.2.6

- package cleanup and include bootstrap

Monday, November 7, 2011: At version 1.2.5

- to allow bootstrap outside of git repo

Tuesday, October 31, 2011: At version 1.2.4

- compile on Mac OSX when __APPLE__ defined.

Tuesday, October 31, 2011: At version 1.2.3

- updated domain01.* to fun3d fortran std
- only build required execuatables for subpackage

Tuesday, October 31, 2011: At version 1.2.2

knife is now distributed as a subpackage of fun3d

- moved example to fun3d tutorials
- cleaned up cruft before dumping into fun3d repo

Wednesday, August 24, 2011: At version 1.2.1, fun3d-10.7-38759 or later

- improved debugging output due to non-manifold geometry inputs
- repaired intel 10 reported warnings/errors
- created a knife-viz tool to export to tecplot
- updated example/diamond-airfoil scripts

Tuesday, June 22, 2010: At version 1.2.0, fun3d-10.7-38759 or later
Now will ignore zero area cut surface triangles during normal and area
computations. Added a more general .tri surface file reader that
auto-detects ASCII and either little-endian or big-endian Fortran
unformatted. 

Thursday, April 22, 2010: At version 1.1.0, fun3d-10.7-38759 or later
Use all surfaces faces when faces keyword is missing from [project].knife
Added knife_cut_surface_dim_ and knife_cut_surface_ to API to request cut
surface geometry as it was used.  added subtriangle parent information
for cut surfaces (knife_surface_sens_), cut edges
(knife_between_poly_sens_), and cut boundaries (knife_boundary_sens_)
to build sensitivity

Thursday, January 3, 2008: At version 1.0.0, FUN3D r25733 or later 
passes triangle_tag and precomupted normal and area. Added 
example/diamond-airfoil VGrid faux geom example.

Tuesday, April 10, 2007: Accounted for fun3d-23948 c2n index flip.
At version 0.8.1

Wednesday, February 7, 2007: Multiple cut regions complete. 
Tag before attempting speedup. At version 0.8

Tuesday, December 26, 2006: Tag before adding multiple cut regions.
At version 0.7.1

Tuesday, December 12, 2006: Added code to release resources. 
FUN3D version 22403.
At version 0.7.0

Thursday, November 2, 2006: Initial parallel working with FUN3D version 21360.
At version 0.6.3

Wednesday, October 25, 2006: Refactored to create only the required duals.
All duals are created for the file fun input method. At version 0.6.2

Friday, October 20, 2006: 75k ONERA M6 Running, at version 0.6.1

Tuesday, October 17, 2006: Replicated Ruby functionality, at version 0.6.0

Friday, October 6, 2006: Started Package at version 0.1.0

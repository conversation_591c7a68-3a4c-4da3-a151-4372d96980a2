! F2KCLI : Fortran 200x Command Line Interface
! copyright Interactive Software Services Ltd. 2001
! For conditions of use see manual.txt
!
! Platform    : Unix/Linux
! Compiler    : Any Fortran 9x compiler supporting IARGC/GETARG
!               which counts the first true command line argument
!               after the program name as argument number one.
!               (Excludes compilers which require a special USE
!               statement to make IARGC/GETARG available).
! To compile  : f90 -c f2kcli.f90
!               (exact compiler name will vary)
! Implementer : <PERSON> Wakefield, I.S.S. Ltd.
! Date        : February 2001

module f2kcli

#ifdef HAVE_CONFIG_H
#include "config.h"
#endif

! make this module blank if the compiler provides
#ifndef HAVE_FORTRAN_2003_ENVIRONMENT

  implicit none

  private

  public :: get_command_argument, command_argument_count

contains

  integer function command_argument_count()

! Description. Returns the number of command arguments.
!
! Class. Inquiry function
!
! Arguments. None.
!
! Result Characteristics. Scalar default integer.
!
! Result Value. The result value is equal to the number of command
!   arguments available. If there are no command arguments available
!   or if the processor does not support command arguments, then
!   the result value is 0. If the processor has a concept of a command
!   name, the command name does not count as one of the command
!   arguments.

#if defined(HAVE_PXF)
#else
    integer, external :: iargc
#endif

    continue

#ifdef HAVE_PXF
    command_argument_count = ipxfargc()
#else
    command_argument_count = iargc()
#endif

  end function command_argument_count

  subroutine get_command_argument(number,value,length,status)

! Description. Returns a command argument.
!
! Class. Subroutine.
! NUMBER shall be scalar and of type default integer. It is an
!   INTENT(IN) argument. It specifies the number of the command
!   argument that the other arguments give information about. Useful
!   values of NUMBER are those between 0 and the argument count
!   returned by the COMMAND_ARGUMENT_COUNT intrinsic.
!   Other values are allowed, but will result in error status return
!   (see below).  Command argument 0 is defined to be the command
!   name by which the program was invoked if the processor has such
!   a concept. It is allowed to call the GET_COMMAND_ARGUMENT
!   procedure for command argument number 0, even if the processor
!   does not define command names or other command arguments.
!   The remaining command arguments are numbered consecutively from
!   1 to the argument count in an order determined by the processor.
! VALUE (optional) shall be scalar and of type default character.
!   It is an INTENT(OUT) argument. It is assigned the value of the
!   command argument specified by NUMBER. If the command argument value
!   cannot be determined, VALUE is assigned all blanks.
! LENGTH (optional) shall be scalar and of type default integer.
!   It is an INTENT(OUT) argument. It is assigned the significant length
!   of the command argument specified by NUMBER. The significant
!   length may include trailing blanks if the processor allows command
!   arguments with significant trailing blanks. This length does not
!   consider any possible truncation or padding in assigning the
!   command argument value to the VALUE argument; in fact the
!   VALUE argument need not even be present. If the command
!   argument length cannot be determined, a length of 0 is assigned.
! STATUS (optional) shall be scalar and of type default integer.
!   It is an INTENT(OUT) argument. It is assigned the value 0 if
!   the argument retrieval is sucessful. It is assigned a
!   processor-dependent non-zero value if the argument retrieval fails.
!
! NOTE
!   One possible reason for failure is that NUMBER is negative or
!   greater than COMMAND_ARGUMENT_COUNT().

    integer,                intent(in)  :: number
    character(*), optional, intent(out) :: value
    integer,      optional, intent(out) :: length
    integer,      optional, intent(out) :: status

#if defined(HAVE_PXF)
#else
    integer, external :: iargc
    external getarg
#endif

!  A temporary variable for the rare case case where LENGTH is
!  specified but VALUE is not. An arbitrary maximum argument length
!  of 1000 characters should cover virtually all situations.

    character(1000) :: tmpval

    continue

! Possible error codes:
! 1 = Argument number is less than minimum
! 2 = Argument number exceeds maximum

    if (number < 0) then
      if (present(value )) value  = ' '
      if (present(length)) length = 0
      if (present(status)) status = 1
      return
#ifdef HAVE_PXF
    else if (number > ipxfargc()) then
#else
    else if (number > iargc()) then
#endif
      if (present(value )) value  = ' '
      if (present(length)) length = 0
      if (present(status)) status = 2
      return
    end if

! Get the argument if VALUE is present

#ifdef HAVE_PXF
    if (present(value)) call pxfgetarg(number,value)
#else
    if (present(value)) call getarg(number,value)
#endif

! The LENGTH option is fairly pointless under Unix.
! Trailing spaces can only be specified using quotes.
! Since the command line has already been processed by the
! shell before the application sees it, we have no way of
! knowing the true length of any quoted arguments. LEN_TRIM
! is used to ensure at least some sort of meaningful result.

    if (present(length)) then
      if (present(value)) then
        length = len_trim(value)
      else
#ifdef HAVE_PXF
        call pxfgetarg(number,tmpval)
#else
        call getarg(number,tmpval)
#endif
        length = len_trim(tmpval)
      end if
    end if

! Since GETARG does not return a result code, assume success

    if (present(status)) status = 0

  end subroutine get_command_argument

#endif
! end HAVE_FORTRAN_2003_ENVIRONMENT

end module f2kcli

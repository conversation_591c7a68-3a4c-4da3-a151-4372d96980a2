!================================= DFV_EB_I ==================================80
!
! Edge-based jacobian terms for cell-centered given l,r state.
!
!=============================================================================80
  pure function dfv_eb_i( xnorm, ynorm, znorm, area, amutl, amutr, dx, dy, dz )

    real(dp), intent(in) :: xnorm, ynorm, znorm, area
    real(dp), intent(in) :: amutl, amutr, dx, dy, dz

    real(dp), dimension(4,4,2) :: dfv_eb_i

    real(dp)                 :: amutf, ds2i
    real(dp), dimension(3)   :: dgrad
    real(dp), dimension(3,2) :: dugrad, dvgrad, dwgrad

  continue

    amutf = 0.5_dp*( amutl + amutr )

    ds2i = 1.0_dp/( dx*dx + dy*dy + dz*dz )

    dgrad(1) = dx*ds2i
    dgrad(2) = dy*ds2i
    dgrad(3) = dz*ds2i

    dugrad(:,2) = dgrad(:)
    dvgrad(:,2) = dgrad(:)
    dwgrad(:,2) = dgrad(:)

    dugrad(:,1) = -dugrad(:,2)
    dvgrad(:,1) = -dvgrad(:,2)
    dwgrad(:,1) = -dwgrad(:,2)

    dfv_eb_i(:,:,1:2) = dfvf_i( xnorm, ynorm, znorm, area, amutf, &
                                dugrad, dvgrad, dwgrad, 2 )

  end function dfv_eb_i

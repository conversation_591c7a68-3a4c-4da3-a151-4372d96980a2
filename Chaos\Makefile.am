DIST_SUBDIRS =

include Common.am

LIBCORE_DIR=@libcore_path@

bin_PROGRAMS = chaos

chaos_SOURCES = $(chaos_SRCS)

if BUILD_SPARSKIT_SUPPORT
chaos_LDADD = -L@sparskitlibrary@ -lskit
else
chaos_LDADD =
endif

chaos_LDADD                     += -L$(LIBCORE_DIR) -lcore @F90_EXT_LIB@

#Build Fortran dependencies
%.d : %.f90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) > $@

%.d : %.F90
	@top_srcdir@/Perl/Depend.pl $< @srcdir@ \
	-I $(LIBCORE_DIR) > $@
